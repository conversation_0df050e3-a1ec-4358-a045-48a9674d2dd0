import os
import sys
import logging
import pytz
from datetime import datetime, timedelta

!pip install PyMySQL==1.1.1

# Configuration parameters - passed from config.yml via papermill
# These parameters are set in config.yml and injected by the DAG framework
ts = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

# Core processing parameters
ENTITY_NAME = "storeops"  # Set to specific entity name or None to process all entities
LIST_ENTITIES_ONLY = False  # Set to True to only list available entities
LOG_LEVEL = "INFO"  # Logging level
DEBUG_MODE = False  # Set to True to enable debug mode (skip actual operations)

# Environment configuration - configurable by end user
ENVIRONMENT = "preprod"  # Environment setting (preprod, prod, etc.)

# StarRocks configuration - passed from config.yml
STARROCKS_SECRET_PATH = (
    f"data/services/planner-platform/{ENVIRONMENT}/databases/starrocks/capacity_planner"
)

# Data loader configuration - fetch from vault
DATA_LOADER_VAULT_PATH = f"data/services/planner-platform/{ENVIRONMENT}/backend/data_loader"

if "JUPYTERHUB_USER" in os.environ:
    DEBUG_MODE: bool = True
    cwd = "/home/<USER>/airflow-de-dags/dags/de/planner/workflow/data_loader"
    os.chdir(cwd)
else:
    DEBUG_MODE: bool = False
    cwd = "/usr/local/airflow/dags/repo/dags/de/planner/workflow/data_loader"
    os.chdir(cwd)

import pencilbox as pb
from config_loader import load_entity_config, get_available_entities
from utils.forecast_utils import process_forecast

# Setup logging
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL.upper()),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    stream=sys.stdout,
    force=True,
)
logger = logging.getLogger(__name__)

# Fetch data loader configuration from vault
logger.info(f"Fetching data loader configuration from vault path: {DATA_LOADER_VAULT_PATH}")
try:
    data_loader_config = pb.get_secret(DATA_LOADER_VAULT_PATH)
    S3_BUCKET = data_loader_config.get("s3_bucket", "blinkit-data-staging")
    S3_REGION = data_loader_config.get("s3_region", "ap-southeast-1")
    IAM_ROLE_ARN = data_loader_config.get(
        "iam_role_arn", "arn:aws:iam::183295456051:role/preprod-blinkit-planner-starrocks-role"
    )
    logger.info(f"Successfully fetched data loader configuration from vault")
    logger.info(f"S3 bucket: {S3_BUCKET}")
    logger.info(f"S3 region: {S3_REGION}")
    logger.info(f"IAM role ARN: {IAM_ROLE_ARN}")
except Exception as e:
    logger.warning(f"Failed to fetch data loader configuration from vault: {str(e)}")
    logger.warning("Using fallback configuration values")
    # Fallback values - always use preprod
    S3_BUCKET = "blinkit-data-staging"
    S3_REGION = "ap-southeast-1"
    IAM_ROLE_ARN = "arn:aws:iam::183295456051:role/preprod-blinkit-planner-starrocks-role"

# Calculate model trigger time
if not ts:
    ts = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

# Parse the timestamp and add 5 hours 30 minutes (IST offset)
from datetime import timedelta

base_time = datetime.strptime(ts, "%Y-%m-%d %H:%M:%S")
adjusted_time = base_time + timedelta(hours=5, minutes=30)
MODEL_TRIGGER_TIME = adjusted_time.strftime("%Y-%m-%d %H:%M:%S")

logger.info(f"Original timestamp: {ts}")
logger.info(f"Model trigger time (UTC + 05:30): {MODEL_TRIGGER_TIME}")

# Create StarRocks configuration object
STARROCKS_CONFIG = {
    "secret_path": STARROCKS_SECRET_PATH,
    "s3_bucket": S3_BUCKET,
    "s3_region": S3_REGION,
    "iam_role_arn": IAM_ROLE_ARN,
}
logger.info(f"StarRocks configuration initialized")

def process_entity(entity_name, model_trigger_time, debug_mode=False, starrocks_config=None):
    """Process all forecasts for a specific entity."""
    logger.info("=" * 80)
    logger.info(f"STARTING {entity_name.upper()} UPDATE PROCESS")
    logger.info("=" * 80)

    try:
        logger.info(f"Using model trigger time: {model_trigger_time}")
        logger.info(f"Debug mode: {debug_mode}")

        entity_config = load_entity_config(entity_name)
        model_configs = entity_config.get("models", {})

        if not model_configs:
            logger.warning(f"No model configurations found for entity '{entity_name}'")
            return {"entity": entity_name, "processed": 0, "total": 0, "success": False}

        processed_count = 0
        total_forecasts = 0

        for model_id, model_config in model_configs.items():
            forecast_types = model_config.get("forecast_types", [])
            total_forecasts += len(forecast_types)

            for forecast_type in forecast_types:
                try:
                    logger.info(f"Processing {forecast_type} forecasts for model {model_id}")
                    result = process_forecast(
                        forecast_type,
                        entity_config,
                        table_suffix=model_id,
                        model_trigger_time=MODEL_TRIGGER_TIME,
                        debug_mode=DEBUG_MODE,
                        starrocks_config=STARROCKS_CONFIG,
                    )
                    if result:
                        processed_count += 1
                except Exception as e:
                    logger.error(
                        f"Failed to process {forecast_type} forecast for model {model_id}: {str(e)}"
                    )

        logger.info(
            f"Completed {processed_count}/{total_forecasts} forecast types for {entity_name}"
        )
        logger.info("=" * 80)
        logger.info(f"{entity_name.upper()} UPDATE PROCESS COMPLETED")
        logger.info("=" * 80)

        return {
            "entity": entity_name,
            "processed": processed_count,
            "total": total_forecasts,
            "success": processed_count > 0,
        }

    except Exception as e:
        logger.error(f"Fatal error processing entity '{entity_name}': {str(e)}")
        return {
            "entity": entity_name,
            "processed": 0,
            "total": 0,
            "success": False,
            "error": str(e),
        }

def process_all_entities(model_trigger_time, debug_mode=False, starrocks_config=None):
    """Process all entities defined in the YAML configuration."""
    logger.info("=" * 80)
    logger.info("STARTING MULTI-ENTITY UPDATE PROCESS")
    logger.info("=" * 80)

    try:
        entities = get_available_entities()
        logger.info(f"Found {len(entities)} entities to process: {entities}")

        results = []

        for entity_name in entities:
            try:
                result = process_entity(
                    entity_name, MODEL_TRIGGER_TIME, DEBUG_MODE, STARROCKS_CONFIG
                )
                results.append(result)
            except Exception as e:
                logger.error(f"Failed to process entity '{entity_name}': {str(e)}")
                results.append(
                    {
                        "entity": entity_name,
                        "processed": 0,
                        "total": 0,
                        "success": False,
                        "error": str(e),
                    }
                )

        logger.info("=" * 80)
        logger.info("MULTI-ENTITY UPDATE PROCESS SUMMARY")
        logger.info("=" * 80)

        total_processed = 0
        total_forecasts = 0
        successful_entities = 0

        for result in results:
            entity_name = result["entity"]
            processed = result["processed"]
            total = result["total"]
            success = result["success"]

            status = "✅ SUCCESS" if success else "❌ FAILED"
            logger.info(f"{entity_name}: {processed}/{total} forecasts processed - {status}")

            total_processed += processed
            total_forecasts += total
            if success:
                successful_entities += 1

        logger.info("=" * 80)
        logger.info(f"OVERALL SUMMARY: {successful_entities}/{len(entities)} entities successful")
        logger.info(f"TOTAL FORECASTS: {total_processed}/{total_forecasts} processed")
        logger.info("=" * 80)

        return results

    except Exception as e:
        logger.error(f"Fatal error in multi-entity processing: {str(e)}")
        return []

# Main execution logic
try:
    # Handle list entities request
    if LIST_ENTITIES_ONLY:
        entities = get_available_entities()
        print("Available entities:")
        for entity in entities:
            print(f"  - {entity}")
    else:
        # Process specific entity or all entities
        if ENTITY_NAME:
            # Process single entity
            available_entities = get_available_entities()
            if ENTITY_NAME not in available_entities:
                logger.error(f"Entity '{ENTITY_NAME}' not found in configuration")
                logger.error(f"Available entities: {available_entities}")
                raise ValueError(f"Entity '{ENTITY_NAME}' not found")

            result = process_entity(ENTITY_NAME, MODEL_TRIGGER_TIME, DEBUG_MODE, STARROCKS_CONFIG)
            if not result["success"]:
                raise RuntimeError(f"Failed to process entity '{ENTITY_NAME}'")
        else:
            # Process all entities
            results = process_all_entities(MODEL_TRIGGER_TIME, DEBUG_MODE, STARROCKS_CONFIG)

            # Check if any entity failed
            failed_entities = [r for r in results if not r["success"]]
            if failed_entities:
                failed_names = [r["entity"] for r in failed_entities]
                logger.error(f"Some entities failed to process: {failed_names}")
                raise RuntimeError(f"Some entities failed: {failed_names}")

except Exception as e:
    logger.error(f"Fatal error: {str(e)}", exc_info=True)
    raise

