# Global settings
global:
  catalog: "blinkit_iceberg_staging"
  time_column: "updated_ts"
  model_id_column: "model_id"
  kafka:
    connection_id: "[Kafka] prod-data-events"
    topic_template: "blinkit.capacity-planner.{entity}-events"

# Standard forecast types used across all entities
forecast_types:
  weekly:
    primary_key: ["entity_id", "week_start_date"]
  daily:
    primary_key: ["entity_id", "week_start_date", "date"]
  hourly:
    primary_key: ["entity_id", "week_start_date", "date", "hour"]
  custom:
    primary_key: ["entity_id", "week_start_date", "date", "start_ts", "end_ts", "group_id"]
  od_slots:
    primary_key: ["entity_id", "week_start_date", "date", "start_ts", "end_ts", "action", "slot_id"]

# Entity-specific configurations
entities:
  storeops:
    entity: "storeops"
    category: "instore"
    catalog: "blinkit_iceberg_staging"
    schema: "storeops_etls"
    kafka:
      connection_id: "[Kafka] prod-data-events"
      topic: "blinkit.capacity-planner.storeops-events"
      forecast_types: ["custom", "od_slots"]  # Forecast types that should send data to Kafka
    models:
      m1:
        forecast_types: ["weekly", "daily", "hourly", "custom", "od_slots"]
      m2:
        forecast_types: ["weekly", "daily"]
