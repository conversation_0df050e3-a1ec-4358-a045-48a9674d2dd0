#!/usr/bin/env python
# coding: utf-8

import pandas as pd
import pencilbox as pb
import uuid
import json
import logging
from datetime import datetime
import pytz

logger = logging.getLogger(__name__)

def format_timestamp(ts):
    """Format timestamp to ISO format with Z suffix"""
    if isinstance(ts, datetime):
        return ts.strftime("%Y-%m-%dT%H:%M:%SZ")
    ts_str = str(ts)
    return ts_str if ts_str.endswith('Z') else f"{ts_str}Z"

def send_to_kafka(df, event_name, entity_config, debug_mode=False):
    """
    Send data to Kafka topic using pb.to_kafka

    Creates forecast objects from DataFrame columns, automatically including all columns
    except 'week_start_date' and 'date'. Applies special formatting for specific column types.

    Args:
        df (DataFrame): DataFrame containing the data to send
        event_name (str): Name of the event
        entity_config (dict): Entity configuration containing kafka settings
        debug_mode (bool): If True, skip actual Kafka sending and only log debug info

    Column Processing:
        - Excluded: 'week_start_date', 'date' (not included in forecast objects)
        - Timestamps: 'start_ts', 'end_ts' formatted with format_timestamp()
        - Boolean: 'week_off' converted to boolean (string '1' -> True, others -> False)
        - String IDs: 'group_id', 'entity_id', 'model_id' converted to strings
        - Other columns: Included as-is with original values
    """
    if df.empty:
        logger.info(f"No data to send to Kafka")
        return

    # Extract values from entity config
    entity = entity_config["entity"]
    kafka_connection_id = entity_config["kafka"]["connection_id"]
    kafka_topic = entity_config["kafka"]["topic"]

    logger.info(f"Preparing to send {len(df)} rows to Kafka topic: {kafka_topic}")

    # Create event timestamp
    event_epoch = datetime.now(pytz.UTC).strftime("%Y-%m-%dT%H:%M:%SZ")

    # Group records by entity_id using defaultdict for cleaner code
    from collections import defaultdict
    store_groups = defaultdict(list)
    for _, row in df.iterrows():
        store_groups[str(row['entity_id'])].append(row.to_dict())

    # Create DataFrame for Kafka with key and value columns
    kafka_data = []

    for entity_id, entity_records in store_groups.items():
        # Format forecasts
        forecasts = []
        for record in entity_records:
            forecast = {}

            # Include all columns except week_start_date and date
            excluded_columns = {'week_start_date', 'date'}

            for column, value in record.items():
                if column not in excluded_columns:
                    # Apply special formatting for timestamp columns
                    if column in ['start_ts', 'end_ts']:
                        forecast[column] = format_timestamp(value)
                    # Convert week_off to boolean
                    elif column == 'week_off':
                        forecast[column] = value == '1' if isinstance(value, str) else bool(value)
                    # Convert ID columns to strings
                    elif column in ['group_id', 'entity_id', 'model_id']:
                        forecast[column] = str(value)
                    # Keep other columns as-is
                    else:
                        forecast[column] = value

            forecasts.append(forecast)

        # Create key and value
        key = {
            "entity_id": entity_id,
            "event_name": event_name,
            "entity": entity
        }

        value = {
            "event_name": event_name,
            "payload": {"forecasts": forecasts},
            "uuid": str(uuid.uuid4()),
            "event_ts": event_epoch,
            "source": "capacity_planner_service"
        }

        kafka_data.append({"key": key, "value": value})

    # Convert to DataFrame
    kafka_df = pd.DataFrame(kafka_data)

    if not kafka_df.empty:
        if debug_mode:
            sample_key = json.dumps(kafka_df.iloc[0]['key'], default=str)
            sample_value = json.dumps(kafka_df.iloc[0]['value'], default=str)
            logger.info(f"[DEBUG] Would send {len(kafka_df)} messages to Kafka")
            logger.info(f"[DEBUG] Sample key: {sample_key}")
            logger.info(f"[DEBUG] Sample value: {sample_value}")
        else:
            logger.info(f"Sending {len(kafka_df)} messages to Kafka")

            # Use pb.to_kafka
            pb.to_kafka(
                conn_id=kafka_connection_id,
                topic=kafka_topic,
                df=kafka_df,
                key_columns=["key"],
                value_columns=["value"]
            )

            # # Create a copy for CSV with JSON serialized columns (used to run from local)
            # file_name = kafka_df.iloc[0]['key']['event_name']
            # csv_df = kafka_df.copy()
            # csv_df['key'] = csv_df['key'].apply(lambda x: json.dumps(x, default=str))
            # csv_df['value'] = csv_df['value'].apply(lambda x: json.dumps(x, default=str))
            # csv_df.to_csv(f"{file_name}.csv", index=False)

            logger.info(f"Successfully sent {len(kafka_df)} messages to Kafka")
