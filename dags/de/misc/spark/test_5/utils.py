from pyspark.sql import SparkSession

def generate_random_spark_df(spark: SparkSession):
    """
    Generate a random Spark DataFrame with 100 rows and 5 columns.
    """
    data = [
        (random.randint(1, 100), random.randint(1, 100), random.randint(1, 100), random.randint(1, 100), random.randint(1, 100))
        for _ in range(100)
    ] 
    df = spark.createDataFrame(data, ["col1", "col2", "col3", "col4", "col5"])
    return df