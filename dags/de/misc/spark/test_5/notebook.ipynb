{"cells": [{"cell_type": "code", "execution_count": null, "id": "a4f16f4c", "metadata": {}, "outputs": [], "source": ["from pyspark.sql import SparkSession\n", "\n", "spark = SparkSession.builder.appName(\"de_misc_spark_test_5_v1\").getOrCreate()"]}, {"cell_type": "code", "execution_count": null, "id": "5e56af78", "metadata": {}, "outputs": [], "source": ["spark"]}, {"cell_type": "code", "execution_count": null, "id": "a274e345-a535-4768-931e-cd952562a7ab", "metadata": {}, "outputs": [], "source": ["# hudi_df = spark.sql(\"select * from lake_grofers_db.gr_user limit 10\")"]}, {"cell_type": "code", "execution_count": null, "id": "93c6458e-8e63-4dfd-bbc2-5ce7f7fb0f31", "metadata": {}, "outputs": [], "source": ["# hudi_df.show(10, False)"]}, {"cell_type": "code", "execution_count": null, "id": "ca9db18b-5030-4219-bbc0-d1caf685bbf7", "metadata": {}, "outputs": [], "source": ["iceberg_df = spark.sql(\n", "    \"select * from blinkit_iceberg.de_etls.blinkit_view_definition_history limit 10\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "311f656c-35f7-4c2e-9528-dfb7ad6d09be", "metadata": {}, "outputs": [], "source": ["iceberg_df.show(10, False)"]}, {"cell_type": "code", "execution_count": null, "id": "b399e1b1-2f0c-4e63-818d-c9520f6e84d1", "metadata": {}, "outputs": [], "source": ["from utils import generate_random_spark_df"]}, {"cell_type": "code", "execution_count": null, "id": "f9e6844b", "metadata": {}, "outputs": [], "source": ["df = generate_random_spark_df(spark)"]}, {"cell_type": "code", "execution_count": null, "id": "dffccfaa", "metadata": {}, "outputs": [], "source": ["df.show(truncate=False)"]}, {"cell_type": "code", "execution_count": null, "id": "6c170587-dfd0-400b-af81-6e5305977ab3", "metadata": {}, "outputs": [], "source": ["spark.stop()"]}, {"cell_type": "code", "execution_count": null, "id": "2ef73412-1fc1-4098-ac7f-99ed15d02d4b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "17a8dc96-e76a-4954-8202-c20695fb558a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "dc8ea701-88f4-43f3-af82-e5817a4e6ca5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 5}