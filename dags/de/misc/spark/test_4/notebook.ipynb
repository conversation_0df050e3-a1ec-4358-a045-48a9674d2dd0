{"cells": [{"cell_type": "code", "execution_count": null, "id": "adaa9a15-7efa-4376-ba37-a68b4225f973", "metadata": {}, "outputs": [], "source": ["from pyspark.sql import SparkSession"]}, {"cell_type": "code", "execution_count": null, "id": "cb6486db-09d3-40cb-87eb-2c8b4f8d8917", "metadata": {}, "outputs": [], "source": ["spark = SparkSession.builder.appName(\"Sample Test PySpark Application\").getOrCreate()"]}, {"cell_type": "code", "execution_count": null, "id": "2d3ec29b-a112-40f2-9ae7-8c3240bba582", "metadata": {}, "outputs": [], "source": ["spark.stop()"]}, {"cell_type": "code", "execution_count": null, "id": "3f7be88e-b8ef-4fc3-b182-7c82fdfe91eb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "python", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "pygments_lexer": "python3"}}, "nbformat": 4, "nbformat_minor": 5}