import sys, os
import logging
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash_operator import <PERSON>sh<PERSON>perator
from airflow.operators.python_operator import PythonOperator
from kubernetes.client import models as k8s
import pencilbox as pb

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import k8s_executor_config, k8s_executor_config_dbt


def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")

    owner = {"email": "<EMAIL>", "slack_id": "U079CFQGS5T"}
    slack_id = owner["slack_id"]
    _owner = {
        "s": "s" if len(slack_id.split(" ")) > 1 else "",
        "slack_id": "<!subteam^{}>".format(slack_id),
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner{s}*: {slack_id}
        *Log Url*: {log_url}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        s=_owner["s"],
        slack_id=_owner["slack_id"],
        log_url=log_url,
    )

    slack_alert_configs = [
        {"channel": "bl-data-slack-testing"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(
                channel=channel, text=slack_failure_msg, user="data-plumber"
            )
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^U079CFQGS5T> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(
                channel="bl-data-slack-testing", text=message, user="data-plumber"
            )


args = {
    "slack_id": "U079CFQGS5T",
    "owner": "<EMAIL>",
    "on_failure_callback": slack_failure_alert,
    "start_date": datetime.strptime("2024-03-28T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2025-07-10T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "S03ST1ENERJ",
    "retries": 1,
    "retry_delay": timedelta(seconds=90),
    "retry_exponential_backoff": True,
}

dag = DAG(
    dag_id="de_dwh_etl_trino_dbt_bl_query_log_daily_v1",
    default_args=args,
    schedule_interval=None,
    catchup=False,
    max_active_runs=1,
)

dbt_base = "cd /dbt-models/trino_dwh && dbt "
model_vars = '\'{{"run_date": "{}", "dag_id": "{}", "dag_run_id": "{}"}}\''.format("{{ data_interval_end }}",
                                                                                   '{{ dag.dag_id }}', '{{ run_id }}')


dbt_run_hp_test_model = BashOperator(
    task_id="dbt_run_hp_test_model",
    dag=dag,
    bash_command="cd /dbt-models/dwh_hpl && dbt "
                 + "run -m dwh_hpl_test_dbt_model "
                 + " --vars "
                 + model_vars
                 + " --target prod_p0",
    executor_config=k8s_executor_config_dbt, 
)

[ dbt_run_hp_test_model]
# dbt_run_bl_query_log_daily

## --------------------------------------------- Maintenance Flow ----------------------------------------------------



if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=1)
    done = set([])


    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)


    for k, _ in dag.task_dict.items():
        run(k)
