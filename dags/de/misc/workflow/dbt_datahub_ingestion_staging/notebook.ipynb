{"cells": [{"cell_type": "code", "execution_count": null, "id": "9089e9dd-e8bd-45cc-b564-55c52e0b66f9", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb \n", "import time"]}, {"cell_type": "code", "execution_count": null, "id": "16764e10-7e48-41b9-9240-66ed4bb5605a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6c77d7cc-cc18-4959-a938-6978cf7b141c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "bf15970c", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "id": "5e7d4d54-c046-483d-a6e9-93c5abcf1df9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7f6e3abe-6913-46f7-b408-64dd3d57e38e", "metadata": {}, "outputs": [], "source": ["time.sleep(60)  "]}, {"cell_type": "code", "execution_count": null, "id": "65e310f7-2053-4952-9343-36dbe7735943", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f97b9c2e-aaa1-446b-9bc8-5ac6dc73d67b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "da1c7561-4a48-452f-8e99-04b3d3b9cfe9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3df62cc5-5293-4da7-9864-32b6b3ad45a1", "metadata": {}, "outputs": [], "source": ["# # ingesting data using recipe file\n", "# try:\n", "#     logger.info(\"Starting datahub ingestion process .... \")\n", "#     result = subprocess.run(\n", "#         [\"datahub\", \"ingest\", \"-c\", \"target/config.yml\"],\n", "#         check=True,\n", "#         capture_output=True,\n", "#         text=True,\n", "#     )\n", "#     logger.info(\"Ingestion completed successfully : \", result.stdout)\n", "# except subprocess.CalledProcessError as e:\n", "#     logger.error(\"An error occurred while ingesting to datahub: \", e.stderr)"]}, {"cell_type": "code", "execution_count": null, "id": "2ca93345", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}