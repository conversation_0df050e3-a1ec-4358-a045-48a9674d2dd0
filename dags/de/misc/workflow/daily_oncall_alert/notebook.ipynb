{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import requests\n", "import pandas as pd\n", "import pencilbox as pb\n", "from slack_sdk import WebClient"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["genie_key = pb.get_secret(\"dse/services/opsgenie_oncall\")[\"genie_key\"]\n", "headers = {\"Authorization\": f\"<PERSON>ie<PERSON>ey {genie_key}\"}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def slack_user_id(email):\n", "    slack_token = pb.get_secret(\"dse/services/slack/tokens\")[\"codsworth\"]\n", "    client = WebClient(token=slack_token)\n", "    response = client.users_lookupByEmail(email=email)\n", "    return response.data[\"user\"][\"id\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_active_schedules():\n", "    schedule_api_url = \"https://api.eu.opsgenie.com/v2/schedules/\"\n", "    response = requests.get(url=schedule_api_url, headers=headers).json()\n", "    enabled_schedules = {}\n", "\n", "    for schedule in response[\"data\"]:\n", "        if schedule[\"enabled\"]:\n", "            enabled_schedules[schedule[\"id\"]] = schedule[\"ownerTeam\"][\"name\"]\n", "\n", "    return enabled_schedules"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_schedules = get_active_schedules()\n", "params = {\"flat\": True}\n", "msg_data = {\n", "    team: {\"Primary\": [], \"Secondary\": []} for team in active_schedules.values()\n", "}\n", "\n", "for sch_id, team in active_schedules.items():\n", "    url = f\"https://api.eu.opsgenie.com/v2/schedules/{sch_id}/on-calls\"\n", "    response = requests.get(url=url, params=params, headers=headers).json()\n", "\n", "    emp_emails = response[\"data\"][\"onCallRecipients\"]\n", "    participants = [f\"<@{slack_user_id(email)}>\" for email in emp_emails]\n", "\n", "    sch_name = response[\"data\"][\"_parent\"][\"name\"]\n", "    if \"gobd\" not in sch_name.lower():  # remove gobd specific schedules\n", "        if \"secondary\" in sch_name.lower():\n", "            msg_data[team][\"Secondary\"].extend(participants)\n", "        else:\n", "            msg_data[team][\"Primary\"].extend(participants)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["message = [\"On-call Schedule for Today:\"]\n", "\n", "for team, oncalls in msg_data.items():\n", "    if oncalls[\"Primary\"]:\n", "        line_0 = f\"\\n>`{team}`\"\n", "        line_1 = f\">*Primary* : {' | '.join(oncalls['Primary'])}\"\n", "        tmp = [line_0, line_1]\n", "\n", "        if oncalls[\"Secondary\"]:\n", "            line_2 = f\">*Secondary* : {' | '.join(oncalls['Secondary'])}\"\n", "            tmp.append(line_2)\n", "\n", "        tmp = \"\\n\".join(tmp)\n", "        message.append(tmp)\n", "\n", "message = \"\\n\".join(message)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\"tech-oncall\", message)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}}, "nbformat": 4, "nbformat_minor": 4}