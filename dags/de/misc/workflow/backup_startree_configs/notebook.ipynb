{"cells": [{"cell_type": "code", "execution_count": null, "id": "58e05286-c401-4f3c-ab2f-319b093fd8c1", "metadata": {}, "outputs": [], "source": ["import json\n", "import boto3\n", "import requests\n", "import pencilbox as pb\n", "from datetime import datetime\n", "\n", "current_time = datetime.now()\n", "\n", "headers = {\n", "    \"accept\": \"application/json\",\n", "}\n", "BUCKET = \"grofers-prod-dse-sgp\"\n", "KEY = \"startree-pinot\"\n", "DATE = current_time.strftime(\"%d-%m-%Y\")\n", "CONTROLLER_URL = pb.get_secret(\"dse/pinot/startree\")[\"controller_url\"]"]}, {"cell_type": "code", "execution_count": null, "id": "96ac0097", "metadata": {}, "outputs": [], "source": ["def dump_json_to_s3(property_):\n", "    property_names = (\n", "        requests.get(f\"{CONTROLLER_URL}/{property_}\", headers=headers).json()\n", "        if property_ == \"schemas\"\n", "        else requests.get(f\"{CONTROLLER_URL}/{property_}\", headers=headers).json()[\n", "            property_\n", "        ]\n", "    )\n", "    s3 = boto3.client(\"s3\")\n", "    for property_name in property_names:\n", "        json_object = requests.get(\n", "            f\"{CONTROLLER_URL}/{property_}/{property_name}\", headers=headers\n", "        ).json()\n", "        extra_key = f\"{KEY}/{DATE}/{property_}/{property_name}.json\"\n", "        print(f\"Writing... {extra_key}\")\n", "        s3.put_object(Body=json.dumps(json_object), Bucket=BUCKET, Key=extra_key)"]}, {"cell_type": "markdown", "id": "83c26cd6-2fb5-48a0-8ee3-340d57911df5", "metadata": {}, "source": ["### Write Schemas to S3"]}, {"cell_type": "code", "execution_count": null, "id": "10f34842-2273-490d-9aa9-af215fe3401d", "metadata": {}, "outputs": [], "source": ["dump_json_to_s3(\"schemas\")"]}, {"cell_type": "markdown", "id": "2fffa04e-1d58-4e2b-b30d-a8427501cd04", "metadata": {}, "source": ["### Write tables to s3"]}, {"cell_type": "code", "execution_count": null, "id": "0f03ff38-4783-42ad-82a7-c82ce4752865", "metadata": {}, "outputs": [], "source": ["dump_json_to_s3(\"tables\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}