{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import Dict, Optional, List\n", "from collections import defaultdict\n", "from dataclasses import dataclass\n", "import requests\n", "import re\n", "from datetime import timedelta, datetime\n", "from dataclasses import dataclass\n", "import os\n", "import yaml\n", "import pytz\n", "import pencilbox as pb\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DAGGERS_FOLDER = \"/usr/local/airflow/dags/repo/daggers\"\n", "current_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)\n", "EXPIRED_DAG_BUFFER_DAYS = 7\n", "delete_dag_date = current_date - timedelta(days=EXPIRED_DAG_BUFFER_DAYS)\n", "\n", "LOAD_TYPE_RESOURCES_MAP = {\n", "    \"tiny\": {\n", "        \"cpu\": {\"request\": 0.5, \"limit\": 1},\n", "        \"memory\": {\"request\": \"1Gi\", \"limit\": \"3Gi\"},\n", "        \"node_selector\": \"general\",\n", "        \"cost\": 1,\n", "    },\n", "    \"low\": {\n", "        \"cpu\": {\"request\": 1, \"limit\": 2},\n", "        \"memory\": {\"request\": \"4Gi\", \"limit\": \"7Gi\"},\n", "        \"node_selector\": \"general\",\n", "        \"cost\": 2,\n", "    },\n", "    \"medium\": {\n", "        \"cpu\": {\"request\": 1, \"limit\": 2},\n", "        \"memory\": {\"request\": \"8Gi\", \"limit\": \"15Gi\"},\n", "        \"node_selector\": \"general\",\n", "        \"cost\": 3,\n", "    },\n", "    \"high-mem\": {\n", "        \"cpu\": {\"request\": 2, \"limit\": 4},\n", "        \"memory\": {\"request\": \"16Gi\", \"limit\": \"30Gi\"},\n", "        \"node_selector\": \"high-mem\",\n", "        \"cost\": 4,\n", "    },\n", "    \"high-cpu\": {\n", "        \"cpu\": {\"request\": 4, \"limit\": 8},\n", "        \"memory\": {\"request\": \"16Gi\", \"limit\": \"30Gi\"},\n", "        \"node_selector\": \"high-cpu\",\n", "        \"cost\": 5,\n", "    },\n", "    \"very-high-mem\": {\n", "        \"cpu\": {\"request\": 2, \"limit\": 4},\n", "        \"memory\": {\"request\": \"32Gi\", \"limit\": \"60Gi\"},\n", "        \"node_selector\": \"high-mem\",\n", "        \"cost\": 6,\n", "    },\n", "    \"very-high-cpu\": {\n", "        \"cpu\": {\"request\": 8, \"limit\": 16},\n", "        \"memory\": {\"request\": \"32Gi\", \"limit\": \"60Gi\"},\n", "        \"node_selector\": \"high-cpu\",\n", "        \"cost\": 7,\n", "    },\n", "    \"ultra-high-mem\": {\n", "        \"cpu\": {\"request\": 2, \"limit\": 4},\n", "        \"memory\": {\"request\": \"64Gi\", \"limit\": \"120Gi\"},\n", "        \"node_selector\": \"ultra-high-mem\",\n", "        \"cost\": 8,\n", "    },\n", "    \"ultra-high-cpu\": {\n", "        \"cpu\": {\"request\": 16, \"limit\": 32},\n", "        \"memory\": {\"request\": \"64Gi\", \"limit\": \"120Gi\"},\n", "        \"node_selector\": \"ultra-high-cpu\",\n", "        \"cost\": 9,\n", "    },\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["@dataclass\n", "class DagConfig:\n", "    dag_id: str\n", "    owner_slack_id: str\n", "\n", "\n", "@dataclass\n", "class ResourceRequest:\n", "    cpu: str\n", "    memory: str\n", "\n", "\n", "@dataclass\n", "class ResourceRecommendation:\n", "    cluster_id: str\n", "    namespace: str\n", "    controller_name: str\n", "    container_name: str\n", "    current_request: ResourceRequest\n", "    recommended_request: ResourceRequest\n", "    monthly_savings: float\n", "    efficiency: Dict[str, float]\n", "    avg_usage: ResourceRequest\n", "    max_usage: ResourceRequest\n", "\n", "\n", "@dataclass\n", "class ResourceMapping:\n", "    current_type: str\n", "    recommended_type: str\n", "    current_resources: Dict\n", "    recommended_resources: Dict\n", "    monthly_savings: float\n", "    pod_name: str"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fetch_allocation_data(\n", "    pod_name: str,\n", "    base_url: str,\n", "    namespace: str = \"airflow\",\n", "    container: str = \"base\",\n", "    window: str = \"48h\",\n", ") -> Dict:\n", "\n", "    endpoint = f\"{base_url}/model/allocation\"\n", "    params = {\n", "        \"filter\": f'cluster:\"prod-sgp\"+namespace:\"{namespace}\"+pod:\"{pod_name}\"+container:\"{container}\"',\n", "        \"window\": window,\n", "        \"aggregate\": \"\",\n", "        \"idle\": \"false\",\n", "        \"accumulate\": \"true\",\n", "        \"limit\": \"1\",\n", "    }\n", "\n", "    try:\n", "        response = requests.get(endpoint, params=params)\n", "        response.raise_for_status()\n", "        data = response.json()\n", "\n", "        if data[\"code\"] == 200:\n", "            return data[\"data\"][0]\n", "        else:\n", "            print(f\"Error fetching allocation for pod {pod_name}: {data}\")\n", "            return {}\n", "\n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"Error fetching allocation data for pod {pod_name}: {e}\")\n", "        return {}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def parse_resource_value(value: str) -> float:\n", "    if isinstance(value, (int, float)):\n", "        return float(value)\n", "\n", "    if value.endswith(\"m\"):\n", "        return float(value[:-1]) / 1000\n", "\n", "    memory_multipliers = {\"Ki\": 1 / (1024 * 1024), \"Mi\": 1 / 1024, \"Gi\": 1, \"Ti\": 1024}\n", "\n", "    match = re.match(r\"(\\d+)([KMGT]i)\", value)\n", "    if match:\n", "        num, unit = match.groups()\n", "        return float(num) * memory_multipliers[unit]\n", "\n", "    return float(value)\n", "\n", "\n", "def find_resource_type(cpu: str, memory: str, resource_map: Dict) -> Optional[str]:\n", "    \"\"\"Find matching resource type from the resource map\"\"\"\n", "    cpu_value = parse_resource_value(cpu)\n", "    memory_value = parse_resource_value(memory)\n", "\n", "    for resource_type, specs in resource_map.items():\n", "        cpu_request = specs[\"cpu\"][\"request\"]\n", "        memory_request = parse_resource_value(specs[\"memory\"][\"request\"])\n", "        cpu_match = 0.9 <= cpu_value / cpu_request <= 1.1\n", "        memory_match = 0.9 <= memory_value / memory_request <= 1.1\n", "\n", "        if cpu_match and memory_match:\n", "            return resource_type\n", "\n", "    return None\n", "\n", "\n", "def get_recommended_resource_type(cpu: str, memory: str, resource_map: Dict) -> str:\n", "    \"\"\"Find the most appropriate resource type for given requirements\"\"\"\n", "    cpu_value = parse_resource_value(cpu)\n", "    memory_value = parse_resource_value(memory)\n", "\n", "    for resource_type, specs in resource_map.items():\n", "        cpu_request = specs[\"cpu\"][\"request\"]\n", "        memory_request = parse_resource_value(specs[\"memory\"][\"request\"])\n", "\n", "        if cpu_value > cpu_request or memory_value > memory_request:\n", "            continue\n", "        return resource_type\n", "\n", "    return \"custom\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_resource_recommendations(\n", "    recommendations: List[ResourceRecommendation], resource_map: Dict\n", ") -> Dict[str, List[ResourceMapping]]:\n", "\n", "    resource_mappings = {\"optimizable\": [], \"custom\": [], \"optimal\": []}\n", "\n", "    for rec in recommendations:\n", "        current_type = (\n", "            find_resource_type(rec.current_request.cpu, rec.current_request.memory, resource_map)\n", "            or \"custom\"\n", "        )\n", "\n", "        recommended_type = get_recommended_resource_type(\n", "            rec.recommended_request.cpu, rec.recommended_request.memory, resource_map\n", "        )\n", "\n", "        mapping = ResourceMapping(\n", "            current_type=current_type,\n", "            recommended_type=recommended_type,\n", "            current_resources={\n", "                \"cpu\": rec.current_request.cpu,\n", "                \"memory\": rec.current_request.memory,\n", "            },\n", "            recommended_resources={\n", "                \"cpu\": rec.recommended_request.cpu,\n", "                \"memory\": rec.recommended_request.memory,\n", "            },\n", "            monthly_savings=rec.monthly_savings,\n", "            pod_name=rec.controller_name,\n", "        )\n", "\n", "        if current_type == recommended_type:\n", "            resource_mappings[\"optimal\"].append(mapping)\n", "        elif recommended_type == \"custom\":\n", "            resource_mappings[\"custom\"].append(mapping)\n", "        else:\n", "            resource_mappings[\"optimizable\"].append(mapping)\n", "\n", "    return resource_mappings"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def find_config_files(directory):\n", "    config_files = []\n", "    for item in os.listdir(directory):\n", "        item_path = os.path.join(directory, item)\n", "        if os.path.isdir(item_path):\n", "            config_files.extend(find_config_files(item_path))\n", "        elif item == \"config.yml\":\n", "            config_files.append(item_path)\n", "\n", "    return config_files\n", "\n", "\n", "def is_paused_or_expired(file_path):\n", "    with open(file_path, \"r\") as file:\n", "        config = yaml.safe_load(file)\n", "\n", "    end_date = config.get(\"schedule\", {}).get(\"end_date\", None)\n", "    expired = False\n", "    if end_date:\n", "        expired = datetime.strptime(end_date, \"%Y-%m-%dT%H:%M:%S\") < delete_dag_date\n", "\n", "    if config.get(\"paused\", False) == True or expired == True:\n", "        return True\n", "    return False\n", "\n", "\n", "def fetch_all_dags(config_files):\n", "    all_dags: List[DagConfig] = []\n", "    for file_path in config_files:\n", "        if not is_paused_or_expired(file_path):\n", "            try:\n", "                with open(file_path, \"r\") as file:\n", "                    config = yaml.safe_load(file)\n", "\n", "                dag_config = DagConfig(\n", "                    dag_id=config.get(\"namespace\", \"unknown\")\n", "                    + \"_\"\n", "                    + config.get(\"project_name\", \"unknown\")\n", "                    + \"_\"\n", "                    + config.get(\"dag_type\", \"unknown\")\n", "                    + \"_\"\n", "                    + config.get(\"dag_name\", \"unknown\")\n", "                    + \"_v\"\n", "                    + str(config.get(\"version\", 1)),\n", "                    owner_slack_id=config.get(\"owner\", {}).get(\"slack_id\", \"unknown\"),\n", "                )\n", "                all_dags.append(dag_config)\n", "            except Exception as e:\n", "                print(f\"Error in getting dag configs for {file_path}\", e)\n", "    return all_dags"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_optimization_report(aggregated_costs, dag_configs):\n", "    report_data = []\n", "\n", "    if aggregated_costs:\n", "        for item in aggregated_costs:\n", "            dag_id = item.get(\"dag_id\", \"N/A\")\n", "            task_id = item.get(\"task_id\", \"N/A\")\n", "            current_type = item.get(\"current_type\", \"N/A\")\n", "            recommended_type = item.get(\"recommended_type\", \"N/A\")\n", "            monthly_savings = item.get(\"monthly_savings\", 0)\n", "            recommended_cpu_cores = item.get(\"recommended_cpu_cores\", \"N/A\")\n", "            recommended_ram_bytes = item.get(\"recommended_ram_bytes\", \"N/A\")\n", "            slack_tag = None\n", "            for config in dag_configs:\n", "                if dag_id[:53] == config.dag_id[:53]:\n", "                    slack_tag = config.owner_slack_id\n", "                    break\n", "\n", "            report_data.append(\n", "                {\n", "                    \"dag_id\": dag_id,\n", "                    \"task_id\": task_id,\n", "                    \"current_load_type\": current_type,\n", "                    \"recommended_load_type\": recommended_type,\n", "                    \"recommended_cpu\": recommended_cpu_cores,\n", "                    \"recommended_memory\": recommended_ram_bytes,\n", "                    \"monthly_savings\": round(monthly_savings, 2),\n", "                    \"slack_id\": slack_tag,\n", "                    \"date\": datetime.now(),\n", "                }\n", "            )\n", "\n", "    if not report_data:\n", "        print(\"No data available.\")\n", "        return\n", "\n", "    df = pd.DataFrame(report_data)\n", "    return df\n", "\n", "\n", "def send_slack_alert_top_savings(df):\n", "    df_sorted = df.sort_values(by=\"monthly_savings\", ascending=False).head(10)\n", "\n", "    message = \"⚙️ Please optimize the following DAGs by today EOD:\\n\"\n", "    for _, row in df_sorted.iterrows():\n", "        dag_id = row[\"dag_id\"]\n", "        task_id = row[\"task_id\"]\n", "        current_type = row[\"current_load_type\"]\n", "        recommended_type = row[\"recommended_load_type\"]\n", "        recommended_cpu_cores = row[\"recommended_cpu\"]\n", "        recommended_ram_bytes = row[\"recommended_memory\"]\n", "        monthly_savings = row[\"monthly_savings\"]\n", "        slack_tag = row[\"slack_id\"]\n", "        message += f\"\\nDAG: {dag_id}\\n\"\n", "\n", "        if slack_tag:\n", "            if slack_tag.startswith(\"S\"):\n", "                message += f\"  *Owner*: <!subteam^{slack_tag}>\\n\"\n", "            else:\n", "                message += f\"  *Owner*: <@{slack_tag}>\\n\"\n", "        message += f\"  Task: {task_id}\\n\"\n", "        message += f\"    Current Load Type: `{current_type}`\\n\"\n", "        message += f\"    Recommended Load Type: `{recommended_type}`\\n\"\n", "        message += f\"    Recommended CPU Cores: `{recommended_cpu_cores}`\\n\"\n", "        message += f\"    Recommended Memory: `{recommended_ram_bytes}`\\n\"\n", "        message += f\"    Monthly Savings: `${monthly_savings:.2f}`\\n\"\n", "    pb.send_slack_message(\"bl-data-slack-testing\", message)\n", "    print(message)\n", "\n", "\n", "def push_report_to_trino(df):\n", "    con = pb.get_connection(\"[Warehouse] Trino\")\n", "    schema_name = \"de_etls\"\n", "    table_name = \"dags_optimization_report\"\n", "    column_dtypes = [\n", "        {\"name\": \"dag_id\", \"type\": \"VARCHAR\", \"description\": \"Unique DAG Identifier\"},\n", "        {\"name\": \"task_id\", \"type\": \"VARCHAR\", \"description\": \"Unique Task Identifier\"},\n", "        {\n", "            \"name\": \"current_load_type\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"Current resource type of the task\",\n", "        },\n", "        {\n", "            \"name\": \"recommended_load_type\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"Recommended resource type for the task\",\n", "        },\n", "        {\n", "            \"name\": \"recommended_cpu\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"Recommended number of CPU cores\",\n", "        },\n", "        {\n", "            \"name\": \"recommended_memory\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"Recommended RAM in bytes\",\n", "        },\n", "        {\n", "            \"name\": \"monthly_savings\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Estimated monthly savings in USD\",\n", "        },\n", "        {\"name\": \"slack_id\", \"type\": \"VARCHAR\", \"description\": \"Slack tag for the owner\"},\n", "        {\"name\": \"date\", \"type\": \"DATE\", \"description\": \"Date when the report was generated\"},\n", "    ]\n", "\n", "    create_kwargs = {\n", "        \"schema_name\": schema_name,\n", "        \"table_name\": table_name,\n", "        \"column_dtypes\": column_dtypes,\n", "        \"partition_key\": [\"date\"],\n", "        \"load_type\": \"append\",\n", "        \"table_description\": \"Dag optimization report\",\n", "    }\n", "    pb.to_trino(df, **create_kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fetch_kubecost_recommendations(\n", "    base_url: str,\n", "    namespace: str = \"airflow\",\n", "    container: str = \"base\",\n", "    window: str = \"10h\",\n", "    target_cpu_util: float = 0.80,\n", "    target_ram_util: float = 0.80,\n", ") -> List[ResourceRecommendation]:\n", "\n", "    endpoint = f\"{base_url}/model/savings/requestSizingV2\"\n", "    params = {\n", "        \"filter\": f'namespace:\"{namespace}\" container:\"{container}\"',\n", "        \"targetCPUUtilization\": target_cpu_util,\n", "        \"targetRAMUtilization\": target_ram_util,\n", "        \"window\": window,\n", "        \"limit\": 10,\n", "    }\n", "\n", "    try:\n", "        response = requests.get(endpoint, params=params)\n", "        response.raise_for_status()\n", "        data = response.json()\n", "        recommendations = []\n", "        for rec in data[\"Recommendations\"]:\n", "            recommendation = ResourceRecommendation(\n", "                cluster_id=rec[\"clusterID\"],\n", "                namespace=rec[\"namespace\"],\n", "                controller_name=rec[\"controllerName\"],\n", "                container_name=rec[\"containerName\"],\n", "                current_request=ResourceRequest(\n", "                    cpu=rec[\"latestKnownRequest\"][\"cpu\"], memory=rec[\"latestKnownRequest\"][\"memory\"]\n", "                ),\n", "                recommended_request=ResourceRequest(\n", "                    cpu=rec[\"recommendedRequest\"][\"cpu\"], memory=rec[\"recommendedRequest\"][\"memory\"]\n", "                ),\n", "                monthly_savings=rec[\"monthlySavings\"][\"total\"],\n", "                efficiency=rec[\"currentEfficiency\"],\n", "                avg_usage=ResourceRequest(\n", "                    cpu=rec[\"averageUsage\"][\"cpu\"], memory=rec[\"averageUsage\"][\"memory\"]\n", "                ),\n", "                max_usage=ResourceRequest(\n", "                    cpu=rec[\"maxUsage\"][\"cpu\"], memory=rec[\"maxUsage\"][\"memory\"]\n", "                ),\n", "            )\n", "            recommendations.append(recommendation)\n", "        return recommendations\n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"Error fetching recommendations: {e}\")\n", "        return []\n", "\n", "\n", "def aggregate_cost_by_labels(\n", "    recommendations: List[ResourceMapping], base_url: str\n", ") -> List[Dict[str, str]]:\n", "    result = []\n", "\n", "    for resourceRecom in recommendations:\n", "        allocation_data = fetch_allocation_data(resourceRecom.pod_name, base_url)\n", "        if allocation_data:\n", "            for key, value in allocation_data.items():\n", "                if isinstance(value, dict) and \"properties\" in value:\n", "                    labels = value[\"properties\"].get(\"labels\", {})\n", "                    dag_id = labels.get(\"dag_id\", \"N/A\")\n", "                    task_id = labels.get(\"task_id\", \"N/A\")\n", "                    recommendation_data = {\n", "                        \"dag_id\": dag_id,\n", "                        \"task_id\": task_id,\n", "                        \"pod_name\": resourceRecom.pod_name,\n", "                        \"current_type\": resourceRecom.current_type,\n", "                        \"recommended_type\": resourceRecom.recommended_type,\n", "                        \"monthly_savings\": resourceRecom.monthly_savings,\n", "                        \"cpu_cores\": resourceRecom.current_resources.get(\"cpu\", \"N/A\"),\n", "                        \"ram_bytes\": resourceRecom.current_resources.get(\"memory\", \"N/A\"),\n", "                        \"recommended_cpu_cores\": resourceRecom.recommended_resources.get(\n", "                            \"cpu\", \"N/A\"\n", "                        ),\n", "                        \"recommended_ram_bytes\": resourceRecom.recommended_resources.get(\n", "                            \"memory\", \"N/A\"\n", "                        ),\n", "                    }\n", "                    result.append(recommendation_data)\n", "\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def main():\n", "    base_url = \"https://kubecost-internal-infra.prod-sgp-k8s.grofer.io\"\n", "    recommendations = fetch_kubecost_recommendations(base_url)\n", "    mappings = analyze_resource_recommendations(recommendations, LOAD_TYPE_RESOURCES_MAP)\n", "    aggregated_costs = aggregate_cost_by_labels(mappings[\"optimizable\"], base_url)\n", "    config_files = find_config_files(DAGGERS_FOLDER)\n", "    dag_configs = fetch_all_dags(config_files)\n", "    optimization_report_df = generate_optimization_report(aggregated_costs, dag_configs)\n", "    push_report_to_trino(optimization_report_df)\n", "    send_slack_alert_top_savings(optimization_report_df)\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}