{"cells": [{"cell_type": "code", "execution_count": null, "id": "spark_session_cell", "metadata": {"tags": ["spark_init"]}, "outputs": [], "source": ["from pyspark.sql import SparkSession\n", "spark = SparkSession.builder.appName('de_misc_workflow_capacity_planner_data_prep_v1').getOrCreate()"]}, {"cell_type": "code", "execution_count": null, "id": "8fbbfde4-50a3-4019-a96a-c18a23802aaf", "metadata": {}, "outputs": [], "source": ["spark"]}, {"cell_type": "code", "execution_count": null, "id": "75299463-28ba-4f92-8737-f9a60b0b4800", "metadata": {}, "outputs": [], "source": ["from pyspark.sql import SparkSession\n", "from pyspark.sql.functions import (\n", "    expr,\n", "    rand,\n", "    round,\n", "    lit,\n", "    current_date,\n", "    date_sub,\n", "    date_add,\n", "    hour,\n", "    col,\n", "    monotonically_increasing_id,\n", ")\n", "from pyspark.sql.types import StructType, StructField, StringType, IntegerType, DoubleType, DateType"]}, {"cell_type": "code", "execution_count": null, "id": "90c18d5b-c436-4f99-8e49-c29241bcd6e0", "metadata": {}, "outputs": [], "source": ["# Constants\n", "TOTAL_ROWS = 100_000_000  # 100 million rows\n", "NUM_PARTITIONS = 200\n", "MIN_ORDERS_PER_HOUR = 5\n", "MAX_ORDERS_PER_HOUR = 200\n", "MIN_ITEMS_PER_ORDER = 1\n", "MAX_ITEMS_PER_ORDER = 15"]}, {"cell_type": "code", "execution_count": null, "id": "1d3a57af-69f2-478f-b91a-a36d2a8d53aa", "metadata": {}, "outputs": [], "source": ["# Output paths configuration\n", "write_dict = {\n", "    \"order_metrics\": {\n", "        # \"pinot\": \"s3://grofers-test-dse-singapore/pinot-offline-tables/capacity_planner/benchmarking/pinot/fact_order_metrics_benchmarking_v4\",\n", "        \"starrocks\": \"s3://blinkit-preprod-pinot-fs/capacity-planner/benchmarking/fact_order_metrics_benchmarking\",\n", "    },\n", "    \"store_metrics\": {\n", "        # \"pinot\": \"s3://grofers-test-dse-singapore/pinot-offline-tables/capacity_planner/benchmarking/pinot/fact_store_metrics_benchmarking_v4\",\n", "        \"starrocks\": \"s3://blinkit-preprod-pinot-fs/capacity-planner/benchmarking/fact_store_metrics_benchmarking\",\n", "    },\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "103bd420-5fd2-4f1a-b00e-7fce91a2de3a", "metadata": {}, "outputs": [], "source": ["def generate_order_metrics_data():\n", "    \"\"\"Generates synthetic order metrics data with realistic distributions.\"\"\"\n", "    print(\"🚀 Generating order metrics data...\")\n", "\n", "    df = spark.range(0, TOTAL_ROWS, 1, NUM_PARTITIONS)\n", "\n", "    end_date = current_date()\n", "    start_date = date_sub(end_date, 6 * 365)\n", "\n", "    df = (\n", "        df.withCol<PERSON>n(\n", "            \"dt\", expr(\"date_add(date_sub(current_date(), 2190), cast(rand() * 2190 as int))\")\n", "        )\n", "        .withColumn(\"hour\", expr(\"cast(rand() * 24 as int)\"))\n", "        .withColumn(\"outlet_id\", expr(\"cast(rand() * 2000 + 1 as int)\"))\n", "    )\n", "\n", "    df = df.withColumn(\n", "        \"orders\",\n", "        expr(\n", "            f\"\"\"\n", "            CASE \n", "                WHEN hour BETWEEN 8 AND 11 OR hour BETWEEN 17 AND 20 \n", "                THEN cast(rand() * ({MAX_ORDERS_PER_HOUR} - {MIN_ORDERS_PER_HOUR} * 2) + {MIN_ORDERS_PER_HOUR} * 2 as int)\n", "                ELSE cast(rand() * ({MAX_ORDERS_PER_HOUR}/2 - {MIN_ORDERS_PER_HOUR}) + {MIN_ORDERS_PER_HOUR} as int)\n", "            END\n", "        \"\"\"\n", "        ),\n", "    )\n", "\n", "    df = df.withColumn(\n", "        \"items_per_order\",\n", "        round(\n", "            expr(\n", "                \"\"\"\n", "                CASE \n", "                    WHEN rand() < 0.7 THEN rand() * 6 + 2  \n", "                    WHEN rand() < 0.9 THEN rand() * 4 + 8  \n", "                    <PERSON>LS<PERSON> rand() * 3 + 12                   \n", "                END\n", "            \"\"\"\n", "            ),\n", "            2,\n", "        ),\n", "    )\n", "\n", "    df = df.withColumn(\n", "        \"total_items_quantity_ordered\", expr(\"cast(orders * items_per_order as int)\")\n", "    )\n", "\n", "    df = df.drop(\"id\").dropDuplicates([\"dt\", \"hour\", \"outlet_id\"])\n", "    df = df.withColumn(\"dt\", col(\"dt\").cast(\"string\"))\n", "    df = df.repartition(\"dt\")\n", "\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "686de640-1c21-4578-9002-42c8cf8304e3", "metadata": {}, "outputs": [], "source": ["def generate_store_metrics_data():\n", "    \"\"\"Generates synthetic store metrics data with realistic distributions.\"\"\"\n", "    print(\"🚀 Generating store metrics data...\")\n", "\n", "    df = spark.range(0, TOTAL_ROWS, 1, NUM_PARTITIONS)\n", "\n", "    end_date = current_date()\n", "    start_date = date_sub(end_date, 6 * 365)\n", "\n", "    # Base columns\n", "    df = (\n", "        df.withCol<PERSON>n(\n", "            \"dt\", expr(\"date_add(date_sub(current_date(), 2190), cast(rand() * 2190 as int))\")\n", "        )\n", "        .withColumn(\"hour\", expr(\"cast(rand() * 24 as int)\"))\n", "        .withColumn(\"outlet_id\", expr(\"cast(rand() * 2000 + 1 as int)\"))\n", "    )\n", "\n", "    # Generate primary metrics with realistic distributions\n", "    df = df.withColumn(\"total_items_quantity_ordered\", expr(\"cast(rand() * 500 + 50 as int)\"))\n", "    df = df.withColumn(\"picking_time_sec\", expr(\"cast(rand() * 3600 + 300 as int)\"))\n", "    df = df.withColumn(\"picker_busy_time_mins\", expr(\"cast(rand() * 55 + 5 as int)\"))\n", "    df = df.withColumn(\"picker_active_time_mins\", expr(\"cast(rand() * 60 + 15 as int)\"))\n", "    df = df.withColumn(\"putawayed_qty\", expr(\"cast(rand() * 400 + 50 as int)\"))\n", "    df = df.withColumn(\"putter_active_time_mins\", expr(\"cast(rand() * 55 + 5 as int)\"))\n", "\n", "    # Employee quantity metrics\n", "    df = df.withColumn(\"qty_ordered_new_employee_od\", expr(\"cast(rand() * 200 + 20 as int)\"))\n", "    df = df.withColumn(\"qty_ordered_old_employee_od\", expr(\"cast(rand() * 300 + 30 as int)\"))\n", "    df = df.withColumn(\"qty_ordered_new_employee\", expr(\"cast(rand() * 250 + 25 as int)\"))\n", "    df = df.withColumn(\"qty_ordered_old_employee\", expr(\"cast(rand() * 350 + 35 as int)\"))\n", "\n", "    # Calculated metrics\n", "    df = df.withColumn(\n", "        \"picking_time_per_item\",\n", "        round(col(\"picking_time_sec\").cast(\"double\") / col(\"total_items_quantity_ordered\"), 2),\n", "    )\n", "\n", "    df = df.withColumn(\n", "        \"picking_utilization\",\n", "        round(\n", "            col(\"picker_busy_time_mins\").cast(\"double\") / col(\"picker_active_time_mins\") * 100, 2\n", "        ),\n", "    )\n", "\n", "    df = df.withColumn(\n", "        \"items_put_away_per_hour\",\n", "        round(\n", "            col(\"putawayed_qty\").cast(\"double\")\n", "            / (col(\"putter_active_time_mins\").cast(\"double\") / 60),\n", "            2,\n", "        ),\n", "    )\n", "\n", "    df = df.withColumn(\n", "        \"od_contribution_pct\",\n", "        round(\n", "            (col(\"qty_ordered_new_employee_od\") + col(\"qty_ordered_old_employee_od\")).cast(\"double\")\n", "            / (col(\"qty_ordered_new_employee\") + col(\"qty_ordered_old_employee\"))\n", "            * 100,\n", "            2,\n", "        ),\n", "    )\n", "\n", "    df = df.withColumn(\"login_hours\", round(expr(\"rand() * 8 + 4\"), 2))\n", "\n", "    # Final cleanup\n", "    df = df.drop(\"id\").dropDuplicates([\"dt\", \"hour\", \"outlet_id\"])\n", "    df = df.withColumn(\"dt\", col(\"dt\").cast(\"string\"))\n", "    df = df.repartition(\"dt\")\n", "\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "743c99ac-1feb-42d4-8d9f-ffc8d1f40450", "metadata": {}, "outputs": [], "source": ["def write_to_location(df, table_name, location_name, output_path):\n", "    \"\"\"Writes DataFrame to specified location with proper partitioning.\"\"\"\n", "    print(f\"📤 Writing {table_name} data to {location_name} at {output_path}\")\n", "\n", "    (\n", "        df.write.partitionBy(\"dt\")\n", "        .option(\"maxRecordsPerFile\", 1000000)\n", "        .option(\"compression\", \"snappy\")\n", "        .mode(\"overwrite\")\n", "        .parquet(output_path)\n", "    )\n", "\n", "    print(f\"✅ Successfully wrote {table_name} data to: {output_path}\")"]}, {"cell_type": "code", "execution_count": null, "id": "f4a27eea-c53c-4c34-8a07-6ff71f8ca5ac", "metadata": {}, "outputs": [], "source": ["def main():\n", "    print(\"🎯 Starting data generation for fact tables...\")\n", "\n", "    # Generate order metrics data\n", "    # order_metrics_df = generate_order_metrics_data()\n", "    # order_metrics_df.cache()\n", "\n", "    # Generate store metrics data\n", "    store_metrics_df = generate_store_metrics_data()\n", "    store_metrics_df.cache()\n", "\n", "    # Print statistics for order metrics\n", "    # order_count = order_metrics_df.count()\n", "    # order_partitions = order_metrics_df.select(\"dt\").distinct().count()\n", "    # print(f\"\\n📊 Order Metrics Statistics:\")\n", "    # print(f\"Generated {order_count:,} rows across {order_partitions} date partitions\")\n", "    # order_metrics_df.show(3)\n", "\n", "    # Print statistics for store metrics\n", "    store_count = store_metrics_df.count()\n", "    store_partitions = store_metrics_df.select(\"dt\").distinct().count()\n", "    print(f\"\\n📊 store Metrics Statistics:\")\n", "    print(f\"Generated {store_count:,} rows across {store_partitions} date partitions\")\n", "    store_metrics_df.show(3)\n", "\n", "    # Write order metrics to all locations\n", "    # for location_name, output_path in write_dict[\"order_metrics\"].items():\n", "    #     write_to_location(order_metrics_df, \"order_metrics\", location_name, output_path)\n", "\n", "    # Write store metrics to all locations\n", "    for location_name, output_path in write_dict[\"store_metrics\"].items():\n", "        write_to_location(store_metrics_df, \"store_metrics\", location_name, output_path)\n", "\n", "    # Cleanup\n", "    # order_metrics_df.unpersist()\n", "    store_metrics_df.unpersist()\n", "    spark.stop()\n", "\n", "    print(\"🎉 All fact tables generated and written successfully!\")"]}, {"cell_type": "code", "execution_count": null, "id": "1fc9fa49-a078-415d-b2c4-9ada20fd9d7a", "metadata": {}, "outputs": [], "source": ["main()"]}, {"cell_type": "code", "execution_count": null, "id": "19302853-be07-4f46-9b60-ec89a231643c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "python", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "pygments_lexer": "python3"}}, "nbformat": 4, "nbformat_minor": 5}