# -*- coding: utf-8 -*-

import os
import logging
from contextlib import closing
from datetime import datetime, timedelta

import airflow
from airflow import DAG
from airflow.hooks.base_hook import BaseHook
from airflow.models import Variable
from kubernetes.client import models as k8s
from metrics_plugin import SparkKubernetesOperator, SparkKubernetesSensor

import pencilbox as pb


cwd = os.path.dirname(os.path.realpath(__file__))
kubernetes_cluster_id = "blinkit-analytics-eks"


def slack_failure_alert(context):
    task_instance = context.get("task_instance")
    dag_id = task_instance.dag_id
    run_id = context.get("run_id")
    task_id = task_instance.task_id
    log_url = task_instance.log_url.replace("http://", "https://")
    notebook_url = (
        f"https://dse-commuter.grofer.io/view/{dag_id}/{run_id}/{task_id}.ipynb"
        if task_instance.operator == "PapermillOperator"
        else None
    )

    owner = {"email": "<EMAIL>", "slack_id": "U04UN5M83RQ"}
    slack_id = owner["slack_id"]
    _owner = {
        "s": "s" if len(slack_id.split(" ")) > 1 else "",
        "slack_id": "<@{}>".format(slack_id),
    }

    slack_failure_msg = f"""
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner{_owner["s"]}*: {_owner["slack_id"]}
        *Log Url*: {log_url}
        {f"*Notebook Url*: {notebook_url}" if notebook_url is not None else ""}
    """

    slack_alert_configs = [
        {"channel": "bl-data-airflow-alerts"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(channel=channel, text=slack_failure_msg)
        except Exception as e:
            logging.warning(e)
            message = (
                f"{_owner['slack_id']} `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(channel="bl-data-airflow-alerts", text=message)


def failure_alerts(context):
    slack_failure_alert(context)


args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": datetime.strptime("2025-04-08T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2025-07-08T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "U04UN5M83RQ",
    "execution_timeout": timedelta(minutes=840),
    "pool": "default_spark_pool",
}

dag = DAG(
    dag_id="de_misc_workflow_capacity_planner_data_prep_v1",
    default_args=args,
    schedule_interval=None,
    tags=["de", "misc", "workflow"],
)

env = {}
default_env = {
    "AIRFLOW_DAG_ID": "de_misc_workflow_capacity_planner_data_prep_v1",
    "AIRFLOW_DAG_OWNER": "<EMAIL>",
    "AIRFLOW_DAG_RUN_ID": "{{ run_id }}",
    "PYTHONPATH": "/usr/local/airflow/dags/repo/dag_utils",
}
env.update(default_env)

spark_args = {
    "etl_name": "de_misc_workflow_capacity_planner_data_prep_v1",
    "driver_file": "s3://blinkit-eks-spark/{environment}/driver_files/run_notebook.py",
    "spark_conf": {
        "spark.sql.parquet.compression.codec": "snappy",
        "spark.sql.parquet.writeLegacyFormat": "false",
        "spark.sql.shuffle.partitions": "200",
        "spark.sql.sources.partitionOverwriteMode": "dynamic",
    },
    "job_args": [
        "s3://grofers-prod-dse-sgp/repo/{airflow}/dags/de/misc/workflow/capacity_planner_data_prep/notebook.ipynb",
        "s3://grofers-prod-dse-sgp/airflow/dag_runs/de_misc_workflow_capacity_planner_data_prep_v1/{run_id}/notebook.ipynb",
        "--parameters",
        {
            "cwd": "/usr/local/airflow/dags/repo/dags/de/misc/workflow/capacity_planner_data_prep",
        },
    ],
    "load_type": "low",
    "node_type": "spot",
    "tenant": "blinkit",
    "env": Variable.get("env", "stage"),
}

notebook_submit_task = SparkKubernetesOperator(
    dag=dag,
    task_id="submit_notebook",
    log_events_on_failure=True,
    kubernetes_conn_id=kubernetes_cluster_id,
    delete_on_termination=True,
    base_container_name="spark-kubernetes-driver",
    get_logs=True,
    step_command=spark_args,
    queue="celery",
)

notebook_poll_task = SparkKubernetesSensor(
    dag=dag,
    task_id="poll_notebook",
    application_name="{{ task_instance.xcom_pull(task_ids='submit_notebook', key='spark_application_name') }}",
    namespace="{{ task_instance.xcom_pull(task_ids='submit_notebook', key='spark_application_namespace') }}",
    kubernetes_conn_id=kubernetes_cluster_id,
    poke_interval=2,
    attach_log=True,
    application_file="{{ task_instance.xcom_pull(task_ids='submit_notebook', key='spark_application_template_body') }}",
    queue="celery",
)

notebook_submit_task >> notebook_poll_task

if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
