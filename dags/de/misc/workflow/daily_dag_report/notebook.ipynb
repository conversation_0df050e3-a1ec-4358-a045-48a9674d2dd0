{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Daily Airflow Failure Report"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# IMPORTS\n", "import os\n", "import re\n", "import json\n", "import requests\n", "import pandas as pd\n", "import pencilbox as pb\n", "from datetime import date, datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# START AND END DATE\n", "today = str((datetime.now()).strftime(\"%Y/%m/%d\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# To avoid pandas from truncating the html output\n", "pd.set_option(\"display.max_colwidth\", -1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Function for Github Workflow"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["workflow_url = \"https://api.github.com/repos/grofers/airflow-dags/dispatches\"\n", "token = pb.get_secret(\"dse/ds_model_service\")[\"workflow_token\"]\n", "\n", "\"\"\"\n", "In this there is a limit to payload, the following is from Github Documentation.\n", "=>The maximum number of top-level properties in client_payload is 10.\n", "=>The payload can contain a maximum of 65,535 characters.\n", "\"\"\"\n", "\n", "\n", "def call_github_workflow(config_to_dag_link: dict):\n", "    headers = {\n", "        \"Accept\": \"application/vnd.github.everest-preview+json\",\n", "        \"Authorization\": f\"token {token}\",\n", "    }\n", "\n", "    payload = {\n", "        \"event_type\": \"trigger-pausing\",\n", "        \"client_payload\": {\n", "            \"config_to_dag_link\": config_to_dag_link,\n", "        },\n", "    }\n", "    payload_json = json.dumps(payload)\n", "    response = requests.post(workflow_url, headers=headers, data=payload_json)\n", "    response.raise_for_status()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c = pb.get_connection(\"airflow\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_run = pd.read_sql(\n", "    \"\"\"\n", "select * from dag_run\n", "where end_date >= NOW() - INTERVAL '24 HOURS'\n", "\"\"\",\n", "    c,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_run[\"namespace\"] = data_run[\"dag_id\"].apply(lambda x: x.split(\"_\")[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_daily_run = data_run.groupby([\"namespace\", \"state\"]).size().unstack(fill_value=0)\n", "data_daily_run[\"dag_runs\"] = data_run[\"namespace\"].value_counts()\n", "data_daily_run[\"dags\"] = data_run.drop_duplicates(\"dag_id\")[\"namespace\"].value_counts()\n", "data_daily_run = data_daily_run.reset_index().sort_values(\"failed\", ascending=False)\n", "data_daily_run.set_index(\"namespace\", inplace=True)\n", "data_daily_run = data_daily_run[[\"failed\", \"success\", \"dags\", \"dag_runs\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_run[(data_run[\"state\"] == \"failed\")].head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_daily_run = data_daily_run[[\"dags\", \"dag_runs\", \"success\", \"failed\"]]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Consecutive DAG failures"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["threshold = 90\n", "dag_info = pd.read_sql(\n", "    \"\"\"\n", "select dag_id, owner, dag_path\n", "from\n", "(\n", "    select dag.dag_id, dag.owners as owner, sd.fileloc as dag_path,\n", "    count(dag_run.execution_date) as run_count,\n", "    (count(case when dag_run.state = 'failed' then 1 end)::float/count(1)*100) as failure_percentage\n", "    from dag\n", "    inner join dag_run on dag.dag_id = dag_run.dag_id\n", "    inner join serialized_dag sd on dag.dag_id = sd.dag_id\n", "    where dag.is_paused = 'False'\n", "    and dag.is_active = 'True'\n", "    and dag_run.execution_date >= date(now() - interval '3 days')\n", "    group by dag.dag_id, dag.owners, sd.fileloc\n", "    having (count(case when dag_run.state = 'failed' then 1 end)::float/ count(1) * 100) > 90.0\n", "    and count(dag_run.execution_date) > 5\n", ") subquery1\n", "\n", "union\n", "\n", "select dag_id, owner, dag_path\n", "from\n", "(\n", "    select dag_id, owner, dag_path,\n", "    sum(case when rnk = 1 and state = 'failed' then 1 else 0 end) as last_failure_flag,\n", "    sum(case when state = 'failed' then 1 else 0 end) as total_failures\n", "    from\n", "    (\n", "        select dag.dag_id, dag.owners as owner, sd.fileloc as dag_path, dag_run.state,\n", "        row_number() over(partition by dag.dag_id order by execution_date desc) as rnk\n", "        from dag\n", "        inner join dag_run on dag.dag_id = dag_run.dag_id\n", "        inner join serialized_dag sd on dag.dag_id = sd.dag_id\n", "        where dag.is_paused = 'False'\n", "        and dag.is_active = 'True'\n", "        and dag_run.execution_date >= date(now() - interval '30 days')\n", "    ) as subquery2\n", "    where rnk <= 5\n", "    group by dag_id, owner, dag_path\n", ") as subquery3\n", "where last_failure_flag = 1\n", "and total_failures = 5\n", "\"\"\",\n", "    c,\n", ")\n", "\n", "dag_info[\"dag_path\"] = dag_info[\"dag_path\"].astype(str)\n", "dag_info[\"dag_path\"] = (\n", "    dag_info[\"dag_path\"]\n", "    .str.split(\"/\")\n", "    .apply(lambda x: x[-5:-1])\n", "    .apply(lambda x: \"/\".join(x))\n", ")\n", "dag_info[\"namespace\"] = dag_info[\"dag_id\"].apply(lambda x: x.split(\"_\")[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["failing_dags = dag_info[[\"namespace\", \"owner\", \"dag_id\", \"dag_path\"]]\n", "failing_dags.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dags_to_pause = failing_dags[[\"namespace\", \"owner\", \"dag_id\", \"dag_path\"]]\n", "dags_to_pause[\"dag_id\"] = dags_to_pause[\"dag_id\"].apply(\n", "    lambda dag_id: f\"<a href='https://airflow-common.grofer.io/calendar?dag_id={dag_id}'>{dag_id}</a>\"\n", ")\n", "dags_to_pause.index += 1\n", "dags_to_pause.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cont_fail_message = (\n", "    f\"No dags failed more than {threshold}% times consecutively in past 3 days.\"\n", ")\n", "\n", "if dags_to_pause.shape[0] >= 1:\n", "    dag_pause_table = dags_to_pause.to_html(\n", "        classes=\"table table-striped table-bordered table-hover table-condensed\",\n", "        escape=False,\n", "        render_links=True,\n", "    )\n", "    cont_fail_message = f\"\"\"\n", "    <p> The following dags failed more than {threshold}% times consecutively in past 3 days: </p>\n", "    {dag_pause_table}\n", "\n", "    <h3> Please check these dag failures, we will pause them the next time they fail. </h3>\n", "    \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["user_dag_failure_info_mail = pd.read_sql(\n", "    \"\"\"\n", "    SELECT dag.owners as owner, count(case when dag_run.state = 'failed' then 1 end) as failure_count\n", "    FROM dag\n", "    INNER JOIN dag_run ON dag.dag_id = dag_run.dag_id\n", "    WHERE dag.is_paused = 'False'\n", "    AND dag.is_active = 'True'\n", "    AND dag_run.execution_date >= date(NOW() - INTERVAL '3 days')\n", "    GROUP BY dag.owners\n", "    ORDER BY failure_count DESC LIMIT 10;\n", "    \"\"\",\n", "    c,\n", ")\n", "user_dag_failure_info_mail.set_index(\"owner\", inplace=True)\n", "\n", "# Create a list of unique owner values\n", "owners = user_dag_failure_info_mail.index.tolist()\n", "\n", "# Create a comma-separated string of owners for the SQL query\n", "owners_string = \", \".join([f\"'{owner}'\" for owner in owners])\n", "\n", "# SQL query to join with serialized_dags to get Slack IDs for specific users with most failures\n", "slack_query = f\"\"\"\n", "SELECT\n", "    s.data->'dag'->'default_args'->'__var'->'owner' AS owner,\n", "    s.data->'dag'->'default_args'->'__var'->'slack_id' AS slack_id\n", "FROM\n", "    serialized_dag AS s\n", "WHERE\n", "    s.data->'dag'->'default_args'->'__var'->>'owner' IN ({owners_string})\n", "\"\"\"\n", "\n", "# Read the Slack ID information into a DataFrame\n", "slack_info = pd.read_sql(slack_query, c)\n", "distinct_slack_info = slack_info.drop_duplicates(subset=[\"owner\"])\n", "distinct_slack_info.set_index(\"owner\", inplace=True)\n", "distinct_slack_info.loc[:, (\"slack_id\")] = distinct_slack_info.loc[\n", "    :, (\"slack_id\")\n", "].apply(\n", "    lambda x: x\n", "    if (not x)\n", "    else (\n", "        f\"<@{x}>\"\n", "        if x.startswith(\"U\")\n", "        else (f\"<!subteam^{x}>\" if x.startswith(\"S\") else None)\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["user_failure_info_table = user_dag_failure_info_mail.to_html(\n", "    classes=\"table table-striped table-bordered table-hover table-condensed\",\n", "    escape=False,\n", "    render_links=True,\n", ")\n", "users_failure_message = f\"\"\"\n", "<p> The following users have the most failures in past 3 days: </p>\n", "{user_failure_info_table}\n", "\n", "<h3> Please check your DAG's. </h3>\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dftable = data_daily_run.to_html(\n", "    classes=\"table table-striped table-bordered table-hover table-condensed\"\n", ")\n", "\n", "html = \"\"\"\n", "<head>\n", "  <meta charset=\"utf-8\">\n", "  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n", "  <link rel=\"stylesheet\" href=\"https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css\">\n", "  <script src=\"https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js\"></script>\n", "  <script src=\"https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js\"></script>\n", "  <style>\n", "table {\n", "  border-collapse: collapse;\n", "  width: 100%;\n", "}\n", "\n", "th {\n", "  text-align: center;\n", "  padding: 8px;\n", "}\n", "\n", "td {\n", "  text-align: left;\n", "  padding: 8px;\n", "}\n", "\n", "tr:nth-child(even){background-color: #FFD5D5}\n", "\n", "th {\n", "  background-color: #ff8400;\n", "  color: white;\n", "}\n", "</style>\n", "\n", "</head>\"\"\" + \"\"\" <h2> Report on your DAGs </h2>\n", "<p> Report on your\n", "<img src=\"https://emoji.slack-edge.com/TCQ18L22Z/airflow_spin_new/39ee36ca0ff810b5.gif\" alt=\"Flowy airflow\" width=\"42\" height=\"42\" align=\"middle\">\n", "DAGs, the good, the bad and the ugly.</p>\n", "<h2> Summary </h2>\n", "{table}\n", "\n", "<h3> We had {failed_dags} failed dag runs and {success_dags} successful dag runs. </h3>\n", "\n", "<br/>\n", "<h2> Bad DAGs </h2>\n", "{cont_fail_message}\n", "<h2> Users with most failures in last 3 days </h2>\n", "{users_failure_message}\n", "\n", "\"\"\".format(\n", "    table=dftable,\n", "    success_dags=len(data_run[(data_run[\"state\"] == \"success\")]),\n", "    failed_dags=len(data_run[(data_run[\"state\"] == \"failed\")]),\n", "    cont_fail_message=cont_fail_message,\n", "    users_failure_message=users_failure_message,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["emails = [email for email in dags_to_pause.owner.drop_duplicates() if \"@\" in email]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.send_email(\n", "    from_email=\"<EMAIL>\",\n", "    to_email=[\"<EMAIL>\"],\n", "    cc=emails,\n", "    subject=\"Airflow DAGs Report {date}\".format(date=today),\n", "    html_content=html,\n", "    mime_subtype=\"mixed\",\n", "    mime_charset=\"utf-8\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install tabulate\n", "from tabulate import tabulate"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["failed_dags = data_run[(data_run[\"state\"] == \"failed\")][[\"dag_id\", \"run_id\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["failed_dags.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import date\n", "\n", "ascii_table = (\n", "    f\":microphone: Airflow DAGs Report for {date.today()} :\\n\"\n", "    + \"```\"\n", "    + tabulate(\n", "        data_daily_run,\n", "        tablefmt=\"pipe\",\n", "        headers=\"keys\",\n", "    )\n", "    + \"```\"\n", ")\n", "tag_users = \" \".join(list(distinct_slack_info[\"slack_id\"]))\n", "message = f\"\"\"*:loudspeaker: Users with most Airflow DAG failures in the last 3 days. Please check your Airflow DAGs*\n", "{tag_users}\n", "\"\"\"\n", "ascii_table_user_failure = (\n", "    f\"\"\n", "    + message\n", "    + \"```\"\n", "    + tabulate(\n", "        user_dag_failure_info_mail,\n", "        tablefmt=\"pipe\",\n", "        headers=\"keys\",\n", "    )\n", "    + \"```\"\n", ")\n", "print(ascii_table_user_failure)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pb.send_slack_message(channel=\"bl-data-airflow-alerts\", text=ascii_table)\n", "# pb.send_slack_message(channel=\"bl-data-airflow-alerts\", text=ascii_table_user_failure)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Append Airflow Daily Execution Log To Trino"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# today = date.today()\n", "# date_filter = today - <PERSON><PERSON><PERSON>(days=7)\n", "# date_filter_1 = today - <PERSON><PERSON><PERSON>(days=2)\n", "# # date_filter = str(date_filter)\n", "# # date_filter"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# airflow_daily_log = pd.read_sql(\n", "#     f\"\"\"\n", "#     SELECT\n", "#         dag_run.execution_date execution_dt,\n", "#         dag_run.run_id,\n", "#         dag_run.state,\n", "#         dag.dag_id,\n", "#         dag.owners as owner,\n", "#         dag.is_paused,\n", "#         (dag_run.start_date AT TIME ZONE 'Asia/Kolkata') execution_start_ts_ist,\n", "#         (dag_run.end_date AT TIME ZONE 'Asia/Kolkata') execution_complete_ts_ist,\n", "#         to_timestamp(cast(split_part((s.data->'dag'->'default_args'->'__var'->'end_date'->>'__var'),'.',1) as int))::date AS dag_end_date\n", "#     FROM\n", "#         dag\n", "#         INNER JOIN dag_run ON dag.dag_id = dag_run.dag_id\n", "#         INNER JOIN serialized_dag s on s.dag_id = dag.dag_id\n", "#     where\n", "#         dag_run.execution_date >= date'{date_filter_1}'\n", "# \"\"\",\n", "#     c,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# daily_execution_log = {\n", "#     \"schema_name\": \"de_etls\",\n", "#     \"table_name\": \"daily_common_airflow_execution_log\",\n", "#     \"column_dtypes\": [\n", "#         {\"name\": \"execution_dt\", \"type\": \"date\", \"description\": \"execution_date\"},\n", "#         {\"name\": \"run_id\", \"type\": \"varchar\", \"description\": \"execution id\"},\n", "#         {\"name\": \"state\", \"type\": \"varchar\", \"description\": \"dag execution status\"},\n", "#         {\"name\": \"dag_id\", \"type\": \"varchar\", \"description\": \"dag execution path\"},\n", "#         {\"name\": \"owner\", \"type\": \"varchar\", \"description\": \"dag owner id\"},\n", "#         {\"name\": \"is_paused\", \"type\": \"boolean\", \"description\": \"dag status?\"},\n", "#         {\n", "#             \"name\": \"execution_start_ts_ist\",\n", "#             \"type\": \"timestamp(6)\",\n", "#             \"description\": \"dag execution started ts\",\n", "#         },\n", "#         {\n", "#             \"name\": \"execution_complete_ts_ist\",\n", "#             \"type\": \"timestamp(6)\",\n", "#             \"description\": \"dag execution completed ts\",\n", "#         },\n", "#         {\"name\": \"dag_end_date\", \"type\": \"date\", \"description\": \"dag end date\"},\n", "#     ],\n", "#     \"primary_key\": [\"run_id\"],\n", "#     \"partition_key\": [\"execution_dt\"],\n", "#     \"incremental_key\": \"execution_dt\",\n", "#     \"load_type\": \"partition_overwrite\",  # append, rebuild, truncate or upsert\n", "#     \"table_description\": \"Table stored daily log of airflow dag execution\",\n", "# }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# airflow_dag_report = pd.read_sql(\n", "#     f\"\"\"\n", "#     SELECT\n", "#         dag.dag_id,\n", "#         dag.owners as owner,\n", "#         max(dag_run.start_date AT TIME ZONE 'Asia/Kolkata') last_start_ts_ist,\n", "#         max(dag_run.end_date AT TIME ZONE 'Asia/Kolkata') last_complete_ts_ist\n", "#     FROM\n", "#         dag\n", "#         INNER JOIN dag_run ON dag.dag_id = dag_run.dag_id\n", "#         INNER JOIN serialized_dag s on s.dag_id = dag.dag_id\n", "#     where\n", "#         dag_run.state = 'success'\n", "#         and dag.is_paused = false\n", "#         and dag_run.execution_date >= date'{date_filter}'\n", "#     group by 1,2\n", "# \"\"\",\n", "#     c,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dag_daily_log = {\n", "#     \"schema_name\": \"de_etls\",\n", "#     \"table_name\": \"agg_daily_common_airflow_success_report\",\n", "#     \"column_dtypes\": [\n", "#         {\"name\": \"dag_id\", \"type\": \"varchar\", \"description\": \"dag execution path\"},\n", "#         {\"name\": \"owner\", \"type\": \"varchar\", \"description\": \"dag owner id\"},\n", "#         {\n", "#             \"name\": \"last_start_ts_ist\",\n", "#             \"type\": \"timestamp(6)\",\n", "#             \"description\": \"dag execution started ts\",\n", "#         },\n", "#         {\n", "#             \"name\": \"last_complete_ts_ist\",\n", "#             \"type\": \"timestamp(6)\",\n", "#             \"description\": \"dag execution completed ts\",\n", "#         },\n", "#     ],\n", "#     \"primary_key\": [\"dag_id\"],\n", "#     \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "#     \"table_description\": \"Table stored daily log of airflow dag execution\",\n", "# }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pb.to_trino(data_obj=airflow_daily_log, **daily_execution_log)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pb.to_trino(data_obj=airflow_dag_report, **dag_daily_log)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Calling the Github workflow to pause the dags"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dags_to_pause[\"dag_path\"] = dags_to_pause[\"dag_path\"].apply(\n", "    lambda x: f\"daggers/{x}/config.yml\"\n", ")\n", "dags_to_pause[\"dag_id\"] = dags_to_pause[\"dag_id\"].apply(\n", "    lambda x: re.search(r'href=[\\'\"]?([^\\'\" >]+)', x).group(1)\n", ")\n", "config_to_dag_link = dict(zip(dags_to_pause[\"dag_path\"], dags_to_pause[\"dag_id\"]))\n", "call_github_workflow(config_to_dag_link)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}