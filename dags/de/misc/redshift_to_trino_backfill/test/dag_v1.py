# -*- coding: utf-8 -*-

import sys, os
import json
import logging
from contextlib import closing
from datetime import datetime, timedelta
import pandas as pd
import re
import time
import string
import random

import requests

import airflow
from airflow import DAG
from airflow.hooks.base_hook import BaseHook
from airflow.operators.python_operator import PythonOperator
from kubernetes.client import models as k8s
from metrics_plugin import CreateHiveToIcebergOperator

import pencilbox as pb


cwd = os.path.dirname(os.path.realpath(__file__))


k8s_executor_config = {
    "pod_override": k8s.V1Pod(
        spec=k8s.V1PodSpec(
            node_selector={"nodetype": "airflow-dags-spot"},
            tolerations=[
                k8s.V1Toleration(
                    effect="NoSchedule",
                    key="service",
                    operator="Equal",
                    value="airflow-dags",
                ),
            ],
            containers=[
                k8s.V1Container(
                    image="442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable",
                    name="base",
                    volume_mounts=[
                        k8s.V1VolumeMount(
                            mount_path="/usr/local/airflow/plugins",
                            name="airflow-dags",
                            sub_path="airflow/plugins",
                        ),
                    ],
                    resources=k8s.V1ResourceRequirements(
                        limits={"cpu": 1, "memory": "4G"},
                        requests={"cpu": 0.5, "memory": "500M"},
                    ),
                )
            ],
            service_account_name="blinkit-prep-airflow-prep-prep-eks-role",
        )
    )
}


def slack_failure_alert(context):
    task_instance = context.get("task_instance")
    dag_id = task_instance.dag_id
    run_id = context.get("run_id")
    task_id = task_instance.task_id
    log_url = task_instance.log_url.replace("http://", "https://")

    owner = {"email": "<EMAIL>", "slack_id": "U04UN5M83RQ"}
    slack_id = owner["slack_id"]
    _owner = {
        "s": "s" if len(slack_id.split(" ")) > 1 else "",
        "slack_id": "<@{}>".format(slack_id),
    }

    slack_failure_msg = f"""
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner{_owner["s"]}*: {_owner["slack_id"]}
        *Log Url*: {log_url}
    """

    slack_alert_configs = [
        {"channel": "bl-data-slack-testing"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(channel=channel, text=slack_failure_msg)
        except Exception as e:
            logging.warning(e)
            message = (
                f"{_owner['slack_id']} `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(channel="bl-data-slack-testing", text=message)


def failure_alerts(context):
    slack_failure_alert(context)


args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": datetime.strptime("2023-04-24T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2024-12-30T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "U04UN5M83RQ",
}

dag = DAG(
    dag_id="de_misc_redshift_to_trino_backfill_test_v1",
    default_args=args,
    schedule_interval="@once",
    tags=["redshift_to_trino_backfill"],
)

env = {}
default_env = {
    "AIRFLOW_DAG_ID": "de_misc_redshift_to_trino_backfill_test_v1",
    "AIRFLOW_DAG_OWNER": "<EMAIL>",
}
env.update(default_env)


def get_redshift_col_names(red_table_view_name, redshift_connection):

    get_red_col_details = f"""
    select * from pg_get_cols('playground.{red_table_view_name}') 
    cols(view_schema name, view_name name, col_name name, col_type varchar, col_num int);
    """
    redshift_view_col_names = pd.read_sql(get_red_col_details, redshift_connection)
    print("redshift_view_col_names: ", redshift_view_col_names)
    return redshift_view_col_names


def create_redshift_table_view(ti, **kwargs):
    red_table = kwargs["red_table"]
    red_schema = kwargs["red_schema"]
    select_query = kwargs["select_query"]
    redshift_connection = pb.get_connection("redpen")
    table_view_name = (
        red_table
        + "_view_"
        + "".join(random.choices(string.ascii_lowercase + string.digits, k=5))
    )

    # create view query
    sql = f"""
    CREATE VIEW playground.{table_view_name} AS {select_query}
    """

    redshift_connection.execute(sql)
    print("created view: playground." + table_view_name)

    redshift_col_names = get_redshift_col_names(table_view_name, redshift_connection)

    col_names_source = ",".join([x for x in redshift_col_names["col_name"].tolist()])

    ti.xcom_push(key="col_names_source", value=col_names_source)
    ti.xcom_push(key="table_view_name", value=table_view_name)
    ti.xcom_push(key="redshift_col_names", value=redshift_col_names)


def unload_to_s3(ti, **kwargs):

    redshift_schema = kwargs["redshift_schema"]
    redshift_table_view = ti.xcom_pull(
        key="table_view_name", task_ids="create_redshift_view"
    )
    s3_path = f"s3://grofers-prod-dse-sgp/redshift-migration/{redshift_schema}/{redshift_table_view}/"
    redshift_unload_copy_role_arn = pb.get_secret(
        "dse/redshift/grofers/redshift-unload-copy-role"
    )["arn"]
    unload_options = ["PARQUET"]
    overwrite_option = "CLEANPATH"

    redshift_connection = pb.get_connection("redpen")

    select_query = f""" SELECT * FROM playground.{redshift_table_view} """

    # upload call
    execution_start_time = time.time()

    unload_sql = """
        UNLOAD ('{select_query}')
        to '{s3_path}'
        iam_role '{iam_role_arn}'
        region as '{region}'
        PARALLEL {parallel}
        {unload_options}
        {overwrite_option}
    """.format(
        select_query=select_query,
        region="ap-southeast-1",
        s3_path=s3_path,
        iam_role_arn=redshift_unload_copy_role_arn,
        parallel="ON",
        unload_options=" ".join(unload_options),
        overwrite_option=overwrite_option,
    )

    red_connection = redshift_connection.connect()
    red_connection = redshift_connection.execution_options(autocommit=True)

    print(f"Unloading playground.{redshift_table_view} from Redshift to {s3_path}")

    red_connection.execute(unload_sql)

    print(
        f"Step 1 - unload to s3 - Execution Time: --- {(time.time() - execution_start_time)} seconds ---"
    )


def redshift_to_hive_datatype(redshift_datatype):
    # Define the Redshift to Hive data type mapping
    redshift_to_hive_mapping = {
        "character varying": "VARCHAR",
        "bigint": "BIGINT",
        "integer": "INT",
        "boolean": "BOOLEAN",
        "timestamp without time zone": "TIMESTAMP",
        "timestamp with time zone": "TIMESTAMP",
        "double precision": "DOUBLE",
        "numeric": "DECIMAL",
        "decimal": "DECIMAL",
        "smallint": "SMALLINT",
        "real": "FLOAT",
        "date": "DATE",
        "time": "VARCHAR",
        "bytea": "BINARY",
        "text": "VARCHAR",
        "json": "VARCHAR",
        "uuid": "VARCHAR",
        "interval": "VARCHAR",
        "char": "VARCHAR",
        "smallserial": "SMALLINT",
        "serial": "INT",
        "bigserial": "BIGINT",
        "bit": "VARCHAR",
        "bit varying": "VARCHAR",
    }

    # handling precision values
    base_type, *precision = redshift_datatype.lower().split("(")
    hive_datatype = redshift_to_hive_mapping.get(base_type)

    if hive_datatype is None:
        raise ValueError(f"Unknown Redshift data type: {redshift_datatype}")

    return (
        f"{hive_datatype}({precision[0]}"
        if hive_datatype and "VARCHAR" not in hive_datatype and precision
        else hive_datatype
    )


def convert_redshift_to_hive_dtypes(redshift_col_names_df):

    hive_col_names_df = redshift_col_names_df
    try:
        hive_col_names_df["col_type"] = redshift_col_names_df["col_type"].apply(
            redshift_to_hive_datatype
        )
    except ValueError as e:
        print(e)

    hive_dtype = hive_col_names_df["col_name"] + " " + hive_col_names_df["col_type"]
    hive_dtypes = ",".join([x for x in hive_dtype])

    return hive_col_names_df, hive_dtypes


def create_hive_table(ti, **kwargs):
    trino_connection = pb.get_connection("[Warehouse] Trino")
    trino_schema = "interim"
    trino_table = kwargs["trino_table"]
    redshift_schema = kwargs["redshift_schema"]
    redshift_table_view = ti.xcom_pull(
        key="table_view_name", task_ids="create_redshift_view"
    )
    redshift_table_view_col_names = ti.xcom_pull(
        key="redshift_col_names", task_ids="create_redshift_view"
    )
    s3_path = f"s3://grofers-prod-dse-sgp/redshift-migration/{redshift_schema}/{redshift_table_view}/"
    hive_table = f"{trino_schema}.{trino_table}_hive_" + "".join(
        random.choices(string.ascii_lowercase + string.digits, k=5)
    )

    hive_column_names_df, hive_dtypes = convert_redshift_to_hive_dtypes(
        redshift_table_view_col_names
    )

    create_temp_hive_table = f"""
        CREATE TABLE IF NOT EXISTS {hive_table} (
            {hive_dtypes}
        )
        WITH (
            format = 'PARQUET', 
            external_location = '{s3_path}'
        )
    """

    execution_start_time = time.time()

    print(
        f"Creating {trino_schema} hive table on Trino blinkit.{hive_table} from path {s3_path}"
    )
    trino_connection.execute(create_temp_hive_table)
    print(
        f"Step 2 - create  hive table in trino - Execution Time: --- {(time.time() - execution_start_time)} seconds ---"
    )

    ti.xcom_push(key="hive_table", value=hive_table)
    hive_column_names_dict = hive_column_names_df.to_dict()
    ti.xcom_push(key="hive_column_names_dict", value=json.dumps(hive_column_names_dict))


task_create_redshift_view = PythonOperator(
    task_id="create_redshift_view",
    python_callable=create_redshift_table_view,
    op_kwargs={
        "red_schema": "playground",
        "red_table": "redshift_all_data_types",
        "select_query": "SELECT id, timestamp_, timestamptz, decimal_, varchar_, date_ FROM playground.redshift_all_data_types LIMIT 100",
    },
    dag=dag,
    retries=2,
    retry_delay=timedelta(seconds=20),
    retry_exponential_backoff=True,
    executor_config=k8s_executor_config,
)

task_redshift_to_s3_dump = PythonOperator(
    task_id="redshift_to_s3_dump",
    python_callable=unload_to_s3,
    op_kwargs={
        "redshift_schema": "playground",
    },
    dag=dag,
    retries=2,
    retry_delay=timedelta(seconds=20),
    retry_exponential_backoff=True,
    executor_config=k8s_executor_config,
)

task_create_hive_table = PythonOperator(
    task_id="create_hive_table",
    python_callable=create_hive_table,
    op_kwargs={
        "redshift_schema": "playground",
        "trino_table": "timezone_backfill_test",
    },
    dag=dag,
    retries=2,
    retry_delay=timedelta(seconds=20),
    retry_exponential_backoff=True,
    executor_config=k8s_executor_config,
)

task_create_iceberg_table = CreateHiveToIcebergOperator(
    task_id="create_iceberg_table",
    trino_catalog="blinkit_iceberg",
    trino_schema="playground",
    trino_table="timezone_backfill_test",
    primary_key=["id"],
    partition_key=["date_"],
    hive_table="{{ task_instance.xcom_pull(task_ids='create_hive_table', key='hive_table') }}",
    hive_column_names_dict="{{ task_instance.xcom_pull(task_ids='create_hive_table', key='hive_column_names_dict') }}",
    col_names_source="{{ task_instance.xcom_pull(task_ids='create_redshift_view', key='col_names_source') }}",
    dag=dag,
    retries=2,
    retry_delay=timedelta(seconds=20),
    retry_exponential_backoff=True,
    executor_config=k8s_executor_config,
)

(
    task_create_redshift_view
    >> task_redshift_to_s3_dump
    >> task_create_hive_table
    >> task_create_iceberg_table
)

if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
