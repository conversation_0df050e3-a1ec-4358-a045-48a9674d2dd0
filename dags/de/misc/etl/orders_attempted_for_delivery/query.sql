-- Project name: misc | DAG name: orders_attempted_for_delivery | Version: 2 | Owner: sang<PERSON><PERSON><EMAIL> | Schedule interval: 0 0 * * * 
WITH oa_base AS
(
SELECT DISTINCT
    order_id,
    action_type,
    id,
    convert_timezone('ASIA/KOLKATA',actual_event_time)::DATE AS delivery_attempted_date,
    convert_timezone('ASIA/KOLKATA',actual_event_time) AS actual_event_timestamp,

    CASE WHEN action_type = 'RESCHEDULED' THEN convert_timezone('ASIA/KOLKATA',(TIMESTAMP 'EPOCH' + cast(trim(split_part(split_part(meta,'"',8),'-',1)) as int) * INTERVAL '1 SECOND'))
         ELSE NULL
    END AS slot_start,

    CASE WHEN action_type = 'RESCHEDULED' THEN convert_timezone('ASIA/KOLKATA',(TIMESTAMP 'EPOCH' + cast(trim(split_part(split_part(meta,'"',8),'-',2)) as int) * INTERVAL '1 SECOND'))
         ELSE NULL
    END AS slot_end,

    CASE WHEN action_type = 'RESCHEDULED' AND convert_timezone('ASIA/KOLKATA',actual_event_time) < slot_start THEN 'true'
         ELSE NULL
    END AS without_attempt_reschedule_flag

FROM lake_last_mile.order_action
WHERE action_type IN ('DELIVERY_FAILED_INITIATED','DELIVERY_REATTEMPT','RESCHEDULED','DELIVERED','CANCELLED')
     AND convert_timezone('ASIA/KOLKATA',actual_event_time)::DATE  BETWEEN '{{ macros.ds_add(ds, -2) }}' AND '{{ macros.ds_add(ds, -1) }}' 
ORDER BY 1,3
),

oa_attempts AS
(
SELECT
    order_id,
    action_type,
    id,
    actual_event_timestamp,
    delivery_attempted_date,
    delivery_attempted_slot
FROM
(
SELECT
    order_id,
    action_type,
    LEAD(action_type,1) OVER (PARTITION BY order_id ORDER BY order_id, id) AS next_action_type,

    CASE WHEN action_type = 'DELIVERY_REATTEMPT' AND next_action_type = 'DELIVERY_FAILED_INITIATED' THEN 'true'
         WHEN action_type = 'DELIVERY_FAILED_INITIATED' AND next_action_type = 'DELIVERED' THEN 'true'
         WHEN action_type = 'DELIVERY_FAILED_INITIATED' AND next_action_type = 'CANCELLED' THEN 'true'
         ELSE NULL
    END AS attempt_number_filter_flag,

    id,
    delivery_attempted_date,
    actual_event_timestamp,
    ((to_char(slot_start, 'HH24:MI:SS'))||' - '||(to_char(slot_end, 'HH24:MI:SS'))) AS slot,
    LEAD(slot,1) OVER (PARTITION BY order_id ORDER BY order_id, id) AS delivery_attempted_slot


FROM oa_base
WHERE without_attempt_reschedule_flag IS NULL
)
WHERE action_type <> 'RESCHEDULED'
    AND attempt_number_filter_flag IS NULL
ORDER BY 1,3
),

final_base as
(
SELECT
    city,
    merchant_id,
    merchant_name,
    station,
    order_id,
    action_type,
    id,
    actual_event_timestamp,
    delivery_attempted_date,
    slot,

    FIRST_VALUE(slot IGNORE NULLS) OVER (PARTITION BY order_id ORDER BY order_id, id  ROWS BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING) AS final_delivery_attempted_slot

FROM
(
SELECT DISTINCT
    c.name AS city,
    CAST(o.checkout_merchant_id AS TEXT) AS merchant_id,
    o.checkout_merchant_name AS merchant_name,
    s.name AS station,
    o.external_order_id AS order_id,
    oa.action_type,
    oa.id,

    LAST_VALUE(oa.id) OVER (PARTITION BY o.external_order_id ORDER BY o.external_order_id, oa.id  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) AS last_id,

    oa.actual_event_timestamp,

    CASE WHEN action_type = 'CANCELLED' AND actual_event_timestamp < convert_timezone('ASIA/KOLKATA',o.delivery_slot_start_time) THEN 'true'
         ELSE NULL
    END AS without_attempt_cancelled_flag,

    delivery_attempted_date,
    delivery_attempted_slot,

    CASE WHEN len((((o.delivery_slot_start_time + interval '5.5 hours') - trunc(o.delivery_slot_start_time + interval '5.5 hours')))/3600000000) = 1

        THEN
    ('0'||(((o.delivery_slot_start_time + interval '5.5 hours') - trunc(o.delivery_slot_start_time + interval '5.5 hours'))/3600000000)||':00:00'||' - '||('0'||((o.delivery_slot_end_time + interval '5.5 hours') - trunc(o.delivery_slot_end_time + interval '5.5 hours'))/3600000000)||':00:00')

        ELSE
    ((((o.delivery_slot_start_time + interval '5.5 hours') - trunc(o.delivery_slot_start_time + interval '5.5 hours'))/3600000000)||':00:00'||' - '||(((o.delivery_slot_end_time + interval '5.5 hours') - trunc(o.delivery_slot_end_time + interval '5.5 hours'))/3600000000)||':00:00')
    END AS delivery_slot,

    CASE WHEN oa.id = last_id THEN COALESCE(delivery_attempted_slot,delivery_slot)
         ELSE delivery_attempted_slot
    END AS slot

FROM oa_attempts AS oa
INNER JOIN lake_last_mile.order AS o
	ON oa.order_id = o.id
LEFT JOIN lake_last_mile.station AS s
    ON o.station_id = s.id
LEFT JOIN lake_last_mile.city AS c
    ON s.city_id = c.id
ORDER BY 5,7
)
WHERE without_attempt_cancelled_flag IS NULL
ORDER BY 5,7
)

SELECT
    city,
    merchant_id,
    merchant_name,
    station,
    delivery_attempted_date,
    final_delivery_attempted_slot AS delivery_attempted_slot,

    count(order_id) AS total_attempt_count

FROM final_base
GROUP BY 1,2,3,4,5,6
ORDER BY 1,2,4,5,6