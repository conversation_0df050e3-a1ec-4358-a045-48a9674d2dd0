{"cells": [{"cell_type": "code", "execution_count": null, "id": "9de6a81a-0c79-436e-bc95-6df73e15df44", "metadata": {}, "outputs": [], "source": ["import time\n", "time.sleep(1*60)\n", "print(\"Hello World\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}