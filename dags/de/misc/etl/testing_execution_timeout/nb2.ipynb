{"cells": [{"cell_type": "code", "execution_count": null, "id": "b3a63c83-4169-4175-b60a-70d518ea8358", "metadata": {}, "outputs": [], "source": ["import time\n", "time.sleep(3*60)\n", "print(\"Hello World\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}