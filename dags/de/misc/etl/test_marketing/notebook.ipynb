{"cells": [{"cell_type": "code", "execution_count": null, "id": "e25d303c-9e40-473d-862a-21c24e24dc7b", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime, timedelta\n", "import time"]}, {"cell_type": "code", "execution_count": null, "id": "14fced2a-103c-4e26-9ee1-fa649a1680d1", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["ds = (datetime.today() - timed<PERSON>ta(days=1)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "6b00152f-cd4c-44d1-a473-ae404d868f7a", "metadata": {}, "outputs": [], "source": ["print(ds)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}