{"cells": [{"cell_type": "code", "execution_count": null, "id": "53788e17-0330-4269-8f2d-a6c4bf9a1008", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7e147fa9-a7fd-4c66-8435-f4282b62365e", "metadata": {}, "outputs": [], "source": ["!pip install scikit-learn\n", "!pip install numpy==1.26.4\n", "import pencilbox as pb\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.cluster import KMeans\n", "from sklearn.preprocessing import MinMaxScaler\n", "from sklearn.preprocessing import StandardScaler\n", "import numpy as np\n", "import requests\n", "import json\n", "from requests.auth import HTTPBasicAuth"]}, {"cell_type": "code", "execution_count": null, "id": "da478475-50a9-4962-a382-8ee7947e519f", "metadata": {}, "outputs": [], "source": ["cont = pb.get_connection(\"[Warehouse] Trino\")\n", "# conn = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "b54e6105-1e07-4d06-bff0-e859e9c4d527", "metadata": {}, "outputs": [], "source": ["stores = \"\"\"select distinct current_timestamp as timestamptz, frontend_merchant_id as merchant_id,\n", "pos_outlet_id as outlet_id\n", "from dwh.dim_merchant_outlet_facility_mapping\n", "WHERE is_frontend_merchant_active = true AND is_backend_merchant_active = true AND is_pos_outlet_active = 1\n", "        AND is_mapping_enabled = true AND is_express_store = true AND is_current_mapping_active = true\n", "        AND is_current = true AND pos_outlet_name <> 'SS Gurgaon Test Store'\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "a5a84942-38a3-4cc5-912f-5c128ac75af2", "metadata": {}, "outputs": [], "source": ["SS_outlet = pd.read_sql(sql=stores, con=cont)"]}, {"cell_type": "code", "execution_count": null, "id": "dd5d3fbe-f868-45bb-b107-8c6cb9aba8c1", "metadata": {}, "outputs": [], "source": ["SS_outlet"]}, {"cell_type": "code", "execution_count": null, "id": "ebc4ddc5-4d88-4575-8796-18fd0b1e504c", "metadata": {}, "outputs": [], "source": ["SS_details = \"\"\"\n", "\n", "with l0_keywords as(\n", "    select l0_category_id, keyword\n", "    from dwh.dim_keywords_l0_mapping\n", "    where is_current = true\n", "    -- and l0_category_id_id = 1487\n", "    group by 1,2\n", "    ),\n", "    \n", "    \n", "l0_carts as (\n", "select \n", "    frontend_merchant_id,\n", "    l0_category_id,\n", "    count(distinct cart_id) carts\n", "from dwh.fact_sales_order_item_details fsoid\n", "join dwh.dim_product dp on dp.product_id = fsoid.product_id and is_current\n", "where\n", "    order_create_dt_ist between current_date - interval '14' day and current_date - interval '1' day\n", "    and order_current_status = 'DELIVERED'\n", "group by 1,2\n", " ),\n", " \n", "platform_carts as (\n", "select \n", "    frontend_merchant_id,\n", "    count(distinct cart_id) carts\n", "from dwh.fact_sales_order_item_details\n", "where\n", "    order_create_dt_ist between current_date - interval '14' day and current_date - interval '1' day\n", "    and order_current_status = 'DELIVERED'\n", "group by 1\n", " )  \n", "\n", "select \n", "-- at_date_ist, \n", "a.merchant_id,\n", "-- merchant_name,\n", "a.city_name,\n", "coalesce(c.l0_category_id,0) l0_category_id,\n", "(lc.carts*100.00)/pc.carts as cart_pen,\n", "case when sum(total_searches_count) > 0 then (sum(total_atc)*100.0/sum(total_searches_count)) end search_atc_conversion\n", "from dwh.agg_daily_search_keyword_conversion_metrics a \n", "join (select merchant_id,merchant_name,city_name from dwh.dim_merchant where is_current = true group by 1,2,3) b on a.merchant_id = b.merchant_id\n", "join l0_keywords c on a.keyword = c.keyword\n", "join l0_carts lc on lc.frontend_merchant_id = a.merchant_id and c.l0_category_id = lc.l0_category_id\n", "join platform_carts pc on pc.frontend_merchant_id = a.merchant_id\n", "where \n", "    a.at_date_ist between current_date - interval '14' day and current_date - interval '1' day\n", "group by 1,2,3,4\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "e704b57d-c7df-4737-a1c2-7f56a3404d02", "metadata": {}, "outputs": [], "source": ["SS_df_og = pd.read_sql(sql=SS_details, con=cont)"]}, {"cell_type": "code", "execution_count": null, "id": "dd123b98-ef2e-4cad-b180-b82c60aa020b", "metadata": {}, "outputs": [], "source": ["SS_df_og = SS_df_og[SS_df_og[\"l0_category_id\"] == 1487]"]}, {"cell_type": "code", "execution_count": null, "id": "99b24d82-5920-4cb8-9684-42e81d02faf3", "metadata": {}, "outputs": [], "source": ["SS_df_og = SS_df_og.merge(SS_outlet, on=[\"merchant_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "65b3fa18-44b3-4a06-bf20-b2c05fcc8d9c", "metadata": {}, "outputs": [], "source": ["da = SS_df_og.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "ebc49c4f-2b86-4d42-b2e3-7d2a70775d0f", "metadata": {}, "outputs": [], "source": ["da.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d51f4370-e779-4646-98bc-f8705206734e", "metadata": {}, "outputs": [], "source": ["condition1 = da.groupby(\"l0_category_id\")[\"merchant_id\"].transform(\"nunique\") < 5\n", "condition2 = da[\"search_atc_conversion\"].isnull()\n", "sd1 = da[condition2].copy()\n", "filtered_df_temp = da[~condition2].copy()\n", "condition3 = (\n", "    filtered_df_temp.groupby([\"city_name\", \"l0_category_id\"])[\"merchant_id\"].transform(\"nunique\")\n", "    < 3\n", ")\n", "sd2 = filtered_df_temp[condition1 | condition3].copy()\n", "fd = filtered_df_temp[~(condition1 | condition3)].copy()\n", "sd = pd.concat([sd1, sd2], ignore_index=True)\n", "fd[\"search_atc_conversion\"] = fd[\"search_atc_conversion\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "12ff02fe-29ff-4acc-b0cd-19b2ba224d4e", "metadata": {}, "outputs": [], "source": ["l0_cat = fd.l0_category_id.unique()\n", "l0_cat = pd.DataFrame(l0_cat)"]}, {"cell_type": "code", "execution_count": null, "id": "e12a4f85-939b-47cf-95b8-322225f580e4", "metadata": {}, "outputs": [], "source": ["fd.head()\n", "metrics = [\"search_atc_conversion\", \"cart_pen\"]"]}, {"cell_type": "code", "execution_count": null, "id": "82c43b39-b00a-4f87-96a7-fe9937d8e18f", "metadata": {}, "outputs": [], "source": ["def kmeans(kmeans_df, l0_category_id, no_of_clusters, iters):\n", "\n", "    df_l0 = kmeans_df[(kmeans_df[\"l0_category_id\"] == l0_category_id)]\n", "    df = df_l0[metrics + [\"merchant_id\"]]\n", "    x = df.drop(\"merchant_id\", axis=1)\n", "    x = pd.DataFrame(x)\n", "    x.dropna(inplace=True)\n", "    # x.mean()\n", "\n", "    kmeans = KMeans(no_of_clusters, max_iter=iters)\n", "\n", "    identified_clusters = kmeans.fit_predict(x)\n", "    data_with_clusters = x.copy()\n", "    data_with_clusters[\"cluster\"] = identified_clusters\n", "\n", "    ##mapping cluster back to merchants\n", "    data_with_clusters[\"merchant_id\"] = df[\"merchant_id\"]\n", "\n", "    # data_with_clusters\n", "    # = data_with_clusters[[\"cluster\", \"merchant_id\"]]\n", "    # data_with_clusters = df.merge(data_with_clusters, on=[\"merchant_id\"])\n", "\n", "    ### mean metric value of clusters\n", "    SS_cluster_mean = data_with_clusters.groupby(\"cluster\", as_index=False).mean()\n", "\n", "    ### filling mean cluster value against every metric\n", "    SS_cluster_mean = SS_cluster_mean.loc[:, [\"cluster\", \"search_atc_conversion\"]]\n", "    SS_cluster_mean.rename(\n", "        columns={\"search_atc_conversion\": \"search_atc_conversion_mean\"}, inplace=True\n", "    )\n", "    SS_cluster_mean[\"search_atc_conversion_mean\"] = SS_cluster_mean[\n", "        \"search_atc_conversion_mean\"\n", "    ].round(2)\n", "\n", "    data_with_clusters = data_with_clusters.merge(SS_cluster_mean, on=[\"cluster\"])\n", "    SS_cluster_mean = SS_cluster_mean[\"search_atc_conversion_mean\"].round(2)\n", "    SS_cluster_mean = np.sort(SS_cluster_mean)\n", "\n", "    ### renaming clusters for actual flow : PAN India\n", "    if no_of_clusters == 5:\n", "        for i in data_with_clusters.index:\n", "            if (data_with_clusters[\"search_atc_conversion_mean\"][i] == SS_cluster_mean[0]) | (\n", "                data_with_clusters[\"search_atc_conversion_mean\"][i] == SS_cluster_mean[1]\n", "            ):\n", "                data_with_clusters[\"cluster\"][i] = \"C\"\n", "            elif (data_with_clusters[\"search_atc_conversion_mean\"][i] == SS_cluster_mean[3]) | (\n", "                data_with_clusters[\"search_atc_conversion_mean\"][i] == SS_cluster_mean[4]\n", "            ):\n", "                data_with_clusters[\"cluster\"][i] = \"A\"\n", "            else:\n", "                data_with_clusters[\"cluster\"][i] = \"B\"\n", "\n", "    ### renaming clusters for actual flow : City level\n", "    if no_of_clusters == 3:\n", "        for i in data_with_clusters.index:\n", "            if data_with_clusters[\"search_atc_conversion_mean\"][i] == SS_cluster_mean[0]:\n", "                data_with_clusters[\"cluster\"][i] = \"C\"\n", "            elif (\n", "                data_with_clusters[\"search_atc_conversion_mean\"][i]\n", "                == SS_cluster_mean[len(SS_cluster_mean) - 1]\n", "            ):\n", "                data_with_clusters[\"cluster\"][i] = \"A\"\n", "            else:\n", "                data_with_clusters[\"cluster\"][i] = \"B\"\n", "\n", "    data_with_clusters.drop(metrics + [\"search_atc_conversion_mean\"], axis=1, inplace=True)\n", "\n", "    kmeans_df_clus = df_l0.merge(data_with_clusters, on=[\"merchant_id\"])\n", "\n", "    return kmeans_df_clus"]}, {"cell_type": "code", "execution_count": null, "id": "9f830084-fb47-4c03-8b1e-e9321da1e9ad", "metadata": {}, "outputs": [], "source": ["kmeans_df_clus_PAN_final = pd.DataFrame()\n", "\n", "for l0_category_id in l0_cat.loc[:, 0]:\n", "    print(l0_category_id)\n", "    kmeans_df_clus_PAN = kmeans(fd, l0_category_id, 5, 1000)\n", "    kmeans_df_clus_PAN_final = kmeans_df_clus_PAN_final.append(\n", "        kmeans_df_clus_PAN, ignore_index=True\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "96a144a3-584f-4620-bb2b-1bdac26babc9", "metadata": {}, "outputs": [], "source": ["cities = fd.city_name.unique()\n", "cities = pd.DataFrame(cities)\n", "kmeans_df_clus_cities_final = pd.DataFrame()\n", "kmeans_df_clus_city_l0_final = pd.DataFrame()\n", "for city in pd.unique(cities[0]):\n", "    df_city = fd[fd[\"city_name\"] == city]\n", "    l0_cat_city = df_city.l0_category_id.unique()\n", "    l0_cat_city = pd.DataFrame(l0_cat_city)\n", "    print(city)\n", "    for l0_category_id in pd.unique(l0_cat_city[0]):\n", "        print(l0_category_id)\n", "        kmeans_df_clus_city_l0 = kmeans(df_city, l0_category_id, 3, 1000)\n", "        kmeans_df_clus_city_l0_final = kmeans_df_clus_city_l0_final.append(\n", "            kmeans_df_clus_city_l0, ignore_index=True\n", "        )\n", "    # kmeans_df_clus_cities_final = kmeans_df_clus_cities_final.append(kmeans_df_clus_city_l0_final, ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "c37100cd-ef13-4f4f-810a-1f7a055a3525", "metadata": {}, "outputs": [], "source": ["sd[\"cluster\"] = \"C\""]}, {"cell_type": "code", "execution_count": null, "id": "816a0e9e-8171-43b2-bd0a-f3beb987c3ba", "metadata": {}, "outputs": [], "source": ["sd[\"cluster_level\"] = \"city\"\n", "kmeans_df_clus_city_l0_final[\"cluster_level\"] = \"city\"\n", "kmeans_df_clus_PAN_final[\"cluster_level\"] = \"pan_india\""]}, {"cell_type": "code", "execution_count": null, "id": "22a00aa1-7bf4-40f2-8218-75d000d561fb", "metadata": {}, "outputs": [], "source": ["final_df = pd.concat(\n", "    [kmeans_df_clus_city_l0_final, kmeans_df_clus_PAN_final, sd], ignore_index=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b01315df-0015-46b9-a697-83c3ab981666", "metadata": {}, "outputs": [], "source": ["final_df"]}, {"cell_type": "code", "execution_count": null, "id": "a553aa2f-61c8-492f-b61f-db2e80959a75", "metadata": {}, "outputs": [], "source": ["final_df.columns\n", "push_df = final_df[(final_df[\"cluster_level\"] == \"city\") & (final_df[\"l0_category_id\"] == 1487)][\n", "    [\n", "        \"merchant_id\",\n", "        \"city_name\",\n", "        \"cart_pen\",\n", "        \"search_atc_conversion\",\n", "        \"timestamptz\",\n", "        \"outlet_id\",\n", "        \"cluster\",\n", "    ]\n", "].copy()\n", "push_df[\"quantile_search_atc\"] = push_df[\"search_atc_conversion\"]\n", "push_df[\"quantile_cart_pen\"] = push_df[\"cart_pen\"]"]}, {"cell_type": "code", "execution_count": null, "id": "d976c408-9bff-4f47-9d73-7b38c0a7a39d", "metadata": {}, "outputs": [], "source": ["push_df.rename(\n", "    columns={\n", "        \"merchant_id\": \"superstore\",\n", "        \"cluster\": \"Flag\",\n", "        \"cart_pen\": \"cartpenetration\",\n", "        \"search_atc_conversion\": \"fnv_search_to_atc\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "060ebda3-5219-4733-9eb2-5b04e79f0e48", "metadata": {}, "outputs": [], "source": ["push_df = push_df.loc[\n", "    :,\n", "    [\n", "        \"timestamptz\",\n", "        \"superstore\",\n", "        \"outlet_id\",\n", "        \"city_name\",\n", "        \"cartpenetration\",\n", "        \"fnv_search_to_atc\",\n", "        \"quantile_cart_pen\",\n", "        \"quantile_search_atc\",\n", "        \"Flag\",\n", "    ],\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "7f91b148-f646-4dcc-8d4d-cbfd80e29d10", "metadata": {}, "outputs": [], "source": ["push_df.reset_index(drop=True, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "e2473ac9-5d68-44db-bcdc-a559f18f7120", "metadata": {}, "outputs": [], "source": ["table = push_df.copy()\n", "pd.unique(table[\"city_name\"])"]}, {"cell_type": "code", "execution_count": null, "id": "43166b34-cc52-4575-89ab-bbd3c65c7ad2", "metadata": {}, "outputs": [], "source": ["push_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "cdaeb48f-2bf5-4037-a365-b28b574ea131", "metadata": {}, "outputs": [], "source": ["table.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "cfaa9fbf-77c3-44cf-ad4d-3b0990426bcd", "metadata": {}, "outputs": [], "source": ["table[\"timestamptz\"] = pd.to_datetime(table[\"timestamptz\"], format=\"%Y-%m-%d %H:%M:%S.%f %Z\")"]}, {"cell_type": "code", "execution_count": null, "id": "4de28a4e-b0da-4de9-b193-a05b7b1b1d84", "metadata": {}, "outputs": [], "source": ["table = table.astype(\n", "    {\n", "        \"cartpenetration\": \"float\",\n", "        \"fnv_search_to_atc\": \"float\",\n", "        \"quantile_cart_pen\": \"float\",\n", "        \"quantile_search_atc\": \"float\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "478a3403-1a68-4ed7-9733-68a72b8e97b8", "metadata": {}, "outputs": [], "source": ["table.shape"]}, {"cell_type": "code", "execution_count": null, "id": "18e57bab-db6a-4718-9aa8-780a3b689cda", "metadata": {}, "outputs": [], "source": ["table.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "2a66b19d-3846-44d8-8b64-5f4b21428deb", "metadata": {}, "outputs": [], "source": ["selected_df = table[[\"superstore\", \"Flag\"]].copy()\n", "selected_df.rename(columns={\"superstore\": \"superstore_id\", \"Flag\": \"cluster_class\"}, inplace=True)\n", "cluster_updates = selected_df.to_dict(orient=\"records\")\n", "print(f\"{len(cluster_updates)} superstores to be updated.\")"]}, {"cell_type": "code", "execution_count": null, "id": "942089a9-27da-4463-85af-148ee4e2b564", "metadata": {}, "outputs": [], "source": ["vault_data = json.loads(\n", "    pb.get_secret(\"dse/credentials/airflow_miscellaneous\")[\"pricing_api_automation_bot_user\"]\n", ")\n", "\n", "user_prod = vault_data[\"username\"]\n", "pwd_prod = vault_data[\"password\"]\n", "auth_credentials = HTTPBasicAuth(user_prod, pwd_prod)\n", "\n", "headers = {\n", "    \"content-type\": \"application/json\",\n", "}\n", "\n", "BASE_URL = \"https://product-pricing-pricing-pre-prod.prod-sgp-k8s.grofer.io/\"\n", "url = f\"{BASE_URL}api/prices/v1/superstores/update_superstore_city_cluster/\"\n", "\n", "payload = {\"cluster_updates\": cluster_updates, \"sync_rules_flag\": False}\n", "print(payload)"]}, {"cell_type": "code", "execution_count": null, "id": "e667a4fe-d02a-4ead-87b0-082381639289", "metadata": {}, "outputs": [], "source": ["response = requests.post(url, headers=headers, auth=auth_credentials, json=payload)\n", "print(f\"Status code: {response.status_code} | Response: {response.json()}\")"]}, {"cell_type": "code", "execution_count": null, "id": "773d182e-1f5d-40f5-9524-c5941b6333d0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}