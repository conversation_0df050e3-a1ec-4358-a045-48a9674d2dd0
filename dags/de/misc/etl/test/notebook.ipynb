{"cells": [{"cell_type": "code", "execution_count": null, "id": "3a93c94e-7c32-4a82-b0d0-7e0b796cf7c3", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "import json\n", "import requests\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "!pip install pandasql\n", "import pandasql as ps\n", "import pencilbox as pb\n", "from requests.auth import HTTPBasicAuth\n", "\n", "pd.options.mode.chained_assignment = None"]}, {"cell_type": "code", "execution_count": null, "id": "9d008843-57cb-4f0d-9e3d-f51af3f39ab1", "metadata": {}, "outputs": [], "source": ["trino_conn = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "7395bdf9-6fc5-4779-8977-08f999eebf14", "metadata": {}, "outputs": [], "source": ["bf_rules_missed_deactivation_query = f\"\"\"\n", "select * from pricing_v3.attribute_management_attributemasterrule \n", "where status in ('ACTIVE_CALLBACK_SUCCESS', 'ACTIVE')\n", "and end_ts < CAST((current_timestamp AT TIME ZONE 'UTC') AS TIMESTAMP)\n", "and id not in (11048195,11048177,11048189,11048174,11048191,11048183,11048184,11048182,11048185,11048192,11048178,11048172,11048176,11048188,11048179,11048187,11048186,11048173,11048175,11048181,11048180,11048193,11048194,11048190)\n", "and attribute_type = 'ItemBrandFund'\n", "and insert_ds_ist > '2022-01-01'\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "a18e8ba7-e9d5-4b17-9ba8-0bace3332a8c", "metadata": {}, "outputs": [], "source": ["bf_rules_missed_deactivation = pd.read_sql(bf_rules_missed_deactivation_query, trino_conn)"]}, {"cell_type": "code", "execution_count": null, "id": "6a979dbe-14da-4c65-bf8a-88adee26e3bd", "metadata": {}, "outputs": [], "source": ["bf_rules_missed_deactivation.shape[0]"]}, {"cell_type": "code", "execution_count": null, "id": "e4065ca1-99d0-4a44-89fe-598c7a866c66", "metadata": {}, "outputs": [], "source": ["bf_rules_missed_deactivation.to_csv(\"bf_rules_missed_deactivation.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "61119f17-5fc0-4f6c-96e1-d0795c232a97", "metadata": {}, "outputs": [], "source": ["if not bf_rules_missed_deactivation.empty:\n", "    text_req = f\"\"\"<!subteam^S03SZGE4Y6T> [TESTING]\"\"\"\n", "    pb.send_slack_message(\n", "        channel=\"bl-pricing-v3-alerts\",\n", "        text=text_req,\n", "        files=[\"bf_rules_missed_deactivation.csv\"],\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "18378a3d-2d0f-4e7b-92db-e1873cb8c691", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e1cc0fc9-b3e1-4b6c-92dd-c2e090854916", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}