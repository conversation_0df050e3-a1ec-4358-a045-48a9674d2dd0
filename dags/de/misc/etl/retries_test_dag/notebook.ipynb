{"cells": [{"cell_type": "code", "execution_count": null, "id": "6241e046-f786-422b-8368-00e5f963888a", "metadata": {}, "outputs": [], "source": ["print(\"hellooooooooooooooo\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}