{"cells": [{"cell_type": "code", "execution_count": null, "id": "27bd1344-668e-4d5f-b538-dd25688ca546", "metadata": {}, "outputs": [], "source": ["import platform\n", "\n", "print(platform.uname()[1])"]}, {"cell_type": "code", "execution_count": null, "id": "9bc77f69-1836-4b84-b2bb-1188deebed5a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}