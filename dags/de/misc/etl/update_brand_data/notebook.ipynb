{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "import string\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table_name = \"brand_data\"\n", "table_schema = \"consumer\"\n", "s3_access_iam_role = \"arn:aws:iam::442534439095:role/redshift-data-migration-role\"\n", "s3_dump_path = f\"s3://grofers-prod-dse-sgp/dwh/flattable/brand_data/{''.join(random.choices(string.ascii_lowercase + string.digits, k = 6))}/\"\n", "column_dtypes = [\n", "    {\n", "        \"name\": \"customer_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"Grofers user_id of customer\",\n", "    },\n", "    {\n", "        \"name\": \"type_id\",\n", "        \"type\": \"Product Type id\",\n", "        \"description\": \"Test\",\n", "    },\n", "    {\n", "        \"name\": \"brand\",\n", "        \"type\": \"varchar(256)\",\n", "        \"description\": \"Product Brand\",\n", "    },\n", "    {\n", "        \"name\": \"orders_with_ptype\",\n", "        \"type\": \"int\",\n", "        \"description\": \"Number of orders with specific type id\",\n", "    },\n", "    {\n", "        \"name\": \"total_orders\",\n", "        \"type\": \"int\",\n", "        \"description\": \"Total Orders\",\n", "    },\n", "    {\n", "        \"name\": \"orders_with_ptype_brand\",\n", "        \"type\": \"int\",\n", "        \"description\": \"Order of that ptype and brand\",\n", "    },\n", "    {\n", "        \"name\": \"last_brand\",\n", "        \"type\": \"varchar(256)\",\n", "        \"description\": \"Last brand purchased\",\n", "    },\n", "    {\n", "        \"name\": \"second_last_brand\",\n", "        \"type\": \"varchar(256)\",\n", "        \"description\": \"Second last brand purchased\",\n", "    },\n", "]\n", "table_description = (\n", "    \"This table contains customer level affinity for particular brands in a ptype\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rebuild_sql = \"\"\"\n", "\n", "with b as (\n", "    with a as(\n", "        select \n", "            distinct customeR_id,\n", "            cart_id,\n", "            dense_rank() over (partition by  customer_id order by cart_checkout_ts desc) as rnk\n", "\n", "        from \n", "            order_item_data o\n", "\n", "        where \n", "            date(install_ts+interval '5.5 hours')>=current_date-interval '10 months'\n", "            and lower(city) NOT IN ('narela','not in service area','haridwar')\n", "            AND (o.merchant_business_type IS NULL\n", "                OR o.merchant_business_type <> 'b2b')\n", "            AND lower(city) IS NOT NULL\n", "            and is_delivered=true\n", "            and category_id!=9\n", "            )\n", "        \n", "        select \n", "        * \n", "\n", "        from \n", "            a \n", "\n", "        where \n", "            rnk<=10\n", "        ),\n", "\n", "    c as (\n", "        select \n", "            distinct b.customer_id,\n", "            count(distinct o.cart_id) as total_orders\n", "        \n", "        from \n", "            order_item_data o\n", "        inner join \n", "            b \n", "                on b.cart_id=o.cart_id\n", "        \n", "        where \n", "            category_id!=9\n", "            and lower(city) NOT IN ('narela','not in service area','haridwar')\n", "            AND (o.merchant_business_type IS NULL\n", "                OR o.merchant_business_type <> 'b2b')\n", "            AND lower(city) IS NOT NULL\n", "            and is_delivered=true\n", "\n", "        group by \n", "            1\n", "        ),\n", "    \n", "    d as (\n", "        select \n", "            distinct b.customer_id,\n", "            type_id,\n", "            count(distinct o.cart_id)  orders_with_ptype\n", "        \n", "        from \n", "            order_item_data o\n", "        inner join \n", "            b \n", "                on b.cart_id=o.cart_id\n", "        \n", "        where \n", "            category_id!=9\n", "            and lower(city) NOT IN ('narela','not in service area','haridwar')\n", "            AND (o.merchant_business_type IS NULL\n", "                OR o.merchant_business_type <> 'b2b')\n", "            AND lower(city) IS NOT NULL\n", "            and is_delivered=true\n", "\n", "        group by \n", "            1,2\n", "        ),\n", "\n", "    e as (\n", "        select \n", "            distinct b.customer_id,\n", "            type_id,\n", "            case \n", "                when \n", "                    brand='' \n", "                    or brand is null\n", "                then 'Best Value' \n", "                else brand \n", "            end as brand,\n", "            count(distinct o.cart_id)  orders_with_ptype_brand\n", "                    \n", "            from \n", "                order_item_data o\n", "            inner join \n", "                b \n", "                    on b.cart_id=o.cart_id\n", "                    \n", "            where \n", "                category_id!=9\n", "                and lower(city) NOT IN ('narela','not in service area','haridwar')\n", "                AND (o.merchant_business_type IS NULL\n", "                    OR o.merchant_business_type <> 'b2b')\n", "                AND lower(city) IS NOT NULL\n", "                and is_delivered=true\n", "\n", "                    group by 1,2,3\n", "                    ),\n", "\n", "    f as (\n", "        with a1 as (\n", "            select \n", "                distinct b.customer_id,\n", "                type_id,\n", "                case \n", "                    when \n", "                        brand='' \n", "                        or brand is null \n", "                    then 'Best Value' \n", "                    else brand \n", "                end as brand,\n", "                rnk,\n", "                dense_rank() over (partition by b.customer_id,type_id order by rnk) as rnk_rank\n", "                    \n", "            from \n", "                order_item_data o\n", "            inner join \n", "                b \n", "                    on b.cart_id=o.cart_id\n", "            \n", "            where \n", "                category_id!=9\n", "                and lower(city) NOT IN ('narela','not in service area','haridwar')\n", "                AND (o.merchant_business_type IS NULL\n", "                    OR o.merchant_business_type <> 'b2b')\n", "                AND lower(city) IS NOT NULL\n", "                and is_delivered=true\n", "                ),\n", "\n", "    a2 as (\n", "        select \n", "            distinct customer_id,\n", "            type_id,\n", "            min(rnk) as max_rnk\n", "        \n", "        from a1\n", "        \n", "        group by \n", "            1,2\n", "            ),\n", "                    \n", "    a3 as (\n", "        select \n", "            distinct a1.customeR_id,\n", "            a1.type_id,\n", "            brand\n", "        from \n", "            a1\n", "        inner join \n", "            a2 \n", "                on a2.customer_id=a1.customer_id\n", "                and a1.type_id=a2.type_id\n", "        \n", "        where \n", "            max_rnk=rnk\n", "        ),\n", "\n", "    a4 as (\n", "        select \n", "            distinct a1.customeR_id,\n", "            a1.type_id,\n", "            brand\n", "        \n", "        from \n", "            a1\n", "                    \n", "        where \n", "            rnk_rank=2\n", "        )\n", "                    \n", "    select \n", "        a3.customeR_id,\n", "        a3.type_id,\n", "        a3.brand as last_brand,\n", "        a4.brand as second_last_brand\n", "    \n", "    from \n", "        a3\n", "    left join \n", "        a4 \n", "            on a4.customer_id=a3.customeR_id\n", "                and a3.type_id=a4.type_id\n", "        )\n", "select \n", "    distinct c.customer_id::int,\n", "    d.type_id::int,\n", "    e.brand,\n", "    orders_with_ptype::int,\n", "    total_orders::int,\n", "    orders_with_ptype_brand::int, \n", "    last_brand,\n", "    second_last_brand\n", "\n", "from c\n", "inner join \n", "    d \n", "        on d.customer_id=c.customer_id\n", "inner join \n", "    e \n", "        on e.customer_id=d.customer_id\n", "            and e.type_id=d.type_id\n", "inner join \n", "    f \n", "        on f.customer_id=d.customer_id\n", "            and f.type_id=d.type_id\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["unload_sql = f\"\"\"\n", "UNLOAD ($$ {rebuild_sql} $$)\n", "to '{s3_dump_path}'\n", "iam_role '{s3_access_iam_role}'\n", "FORMAT PARQUET\n", "ALLOWOVERWRITE\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_connection = pb.get_connection(\"redpen\")\n", "redshift_connection.execute(unload_sql)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": table_schema,\n", "    \"table_name\": table_name,\n", "    \"primary_key\": [],\n", "    \"column_dtypes\": column_dtypes,\n", "    \"sortkey\": [],\n", "    \"incremental_key\": [],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": table_description,\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(s3_dump_path, copy_params=[\"FORMAT AS PARQUET\"], **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}