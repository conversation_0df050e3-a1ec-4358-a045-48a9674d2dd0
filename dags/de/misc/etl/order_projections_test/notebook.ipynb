{"cells": [{"cell_type": "code", "execution_count": null, "id": "d397b0a8-aaed-4a43-86c6-27d88c4b5d55", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "from datetime import datetime, timedelta\n", "import os"]}, {"cell_type": "code", "execution_count": null, "id": "8b3d32e4-4331-4896-a45f-76547182e05d", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "4fe85713-35d5-408f-9efd-b4840132c6f8", "metadata": {}, "outputs": [], "source": ["# ov_end_date = (datetime.today() + timedelta(hours=5.5) - timedelta(days=3)).date().strftime(\"%Y-%m-%d\")\n", "# train_end_date = (pd.to_datetime((datetime.today() + timedelta(hours=5.5)).date().strftime(\"%Y-%m\") + \"-01\") - timedelta(days=1)).date().strftime(\"%Y-%m-%d\")\n", "\n", "\n", "train_end_date = \"2024-05-15\"\n", "start_date = (\n", "    (pd.to_datetime(train_end_date) - timedelta(days=730)).date().strftime(\"%Y-%m-%d\")\n", ")\n", "ov_end_date = (\n", "    (pd.to_datetime(train_end_date) + timedelta(days=90)).date().strftime(\"%Y-%m-%d\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "685837bb-7ec5-4a78-936b-7f00e8f284af", "metadata": {}, "outputs": [], "source": ["start_date, ov_end_date, train_end_date"]}, {"cell_type": "code", "execution_count": null, "id": "12d09535-ecf7-4f84-96b6-213eb5397170", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c03b0adc-a924-453b-af10-cbf492d4c0f0", "metadata": {}, "outputs": [], "source": ["## remove below dates\n", "\n", "## incorrect data in source for below dates\n", "list1 = [\n", "    \"2024-01-21\",\n", "    \"2024-02-13\",\n", "    \"2024-02-14\",\n", "    \"2024-02-15\",\n", "    \"2024-02-16\",\n", "    \"2024-02-17\",\n", "    \"2024-02-18\",\n", "    \"2024-02-19\",\n", "    \"2024-02-20\",\n", "    \"2024-02-21\",\n", "]\n", "\n", "list2 = []\n", "for x in list(pd.date_range(start=\"2023-04-01\", end=\"2023-06-30\")):\n", "    list2 = list2 + [x.strftime(\"%Y-%m-%d\")]\n", "\n", "remove_dates = list1 + list2"]}, {"cell_type": "code", "execution_count": null, "id": "ca57133f-151f-4ada-88cf-afe870c28c0e", "metadata": {}, "outputs": [], "source": ["disruption_input = pb.from_sheets(\n", "    \"1lepKjqGE5dKrp5D_0orbl4BFVv1RkaA2SZKDouDSy3k\", \"Disruption Input\"\n", ")\n", "# disruption_input = pd.read_csv('Disruption Input.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "3f59eca3-5d58-489f-bfe6-4440ced09fe2", "metadata": {}, "outputs": [], "source": ["list3 = []\n", "\n", "for i in range(0, len(disruption_input)):\n", "    dt = disruption_input.loc[i, \"date_\"]\n", "\n", "    for x in list(\n", "        pd.date_range(\n", "            start=pd.to_datetime(dt), end=pd.to_datetime(dt) + timed<PERSON>ta(days=1)\n", "        )\n", "    ):\n", "        list3 = list3 + [x.strftime(\"%Y-%m-%d\")]"]}, {"cell_type": "code", "execution_count": null, "id": "cd51ec51-51a9-4dfb-952d-e80fdf3cef9d", "metadata": {}, "outputs": [], "source": ["remove_dates = remove_dates + list3"]}, {"cell_type": "code", "execution_count": null, "id": "fa2cc476-cf1c-4bf5-a473-81f84efedb0e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ca04c05e-be81-4a1e-8bce-373990bdf768", "metadata": {}, "outputs": [], "source": ["new_polygon = pb.from_sheets(\n", "    \"1ICaG1vKwnUANBOU9gbZ6wWy938p8eJxOhD721aqZPR8\", \"IB/OB Plan\"\n", ")[\n", "    [\n", "        \"Outlet Status\",\n", "        \"Outlet Id\",\n", "        \"Facility Id\",\n", "        \"Outlet Name\",\n", "        \"City\",\n", "        \"Store Status\",\n", "        \"Store Type\",\n", "        \"OB Final Date\",\n", "        \"Store Type for Marketing/Assortment/Other Initiatives\",\n", "    ]\n", "]\n", "# new_polygon = pd.read_csv('IB_OB Plan.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "a18c080a-97b9-4195-b158-1798cd448941", "metadata": {}, "outputs": [], "source": ["# new_polygon[\"Outlet Id\"] = new_polygon[\"Outlet Id\"].str.strip()"]}, {"cell_type": "code", "execution_count": null, "id": "bce1aa87-55cb-495b-a319-f60a36fb5f6b", "metadata": {}, "outputs": [], "source": ["new_polygon = new_polygon[\n", "    (new_polygon[\"Outlet Id\"] != \"\")\n", "    & (~new_polygon[\"Outlet Id\"].isna())\n", "    & (~new_polygon[\"OB Final Date\"].isna())\n", "    & (new_polygon[\"OB Final Date\"] != \"#VALUE!\")\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "4f6e7a58-e352-4d2e-9949-7557a141a023", "metadata": {}, "outputs": [], "source": ["new_polygon[\"Outlet Id\"] = new_polygon[\"Outlet Id\"].astype(int)\n", "new_polygon[\"OB Final Date\"] = pd.to_datetime(\n", "    new_polygon[\"OB Final Date\"], errors=\"coerce\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "476e29a2-c60b-4a36-b722-a53aa4249cbb", "metadata": {}, "outputs": [], "source": ["new_polygon[\"Store Type for Marketing/Assortment/Other Initiatives\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "5043d517-1c97-493b-9903-903ccfa4d8f1", "metadata": {}, "outputs": [], "source": ["selected_new_polygon = new_polygon[\n", "    (pd.to_datetime(new_polygon[\"OB Final Date\"]) > pd.to_datetime(train_end_date))\n", "    & (pd.to_datetime(new_polygon[\"OB Final Date\"]) <= pd.to_datetime(ov_end_date))\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "5b12c309-16a8-4a8e-90ba-723ce2b7f875", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "18c0afb0-83d7-488d-b7cd-d76a80b72d57", "metadata": {}, "outputs": [], "source": ["active_store_sql = \"\"\"SELECT cast(sn.node_id as int) as merchant_id\n", "--       sn.name,\n", "--      CAST(json_extract(sn.meta, '$.city_name') AS VARCHAR) AS city\n", "FROM serviceability.ser_store_polygons s\n", "JOIN serviceability.ser_node sn ON s.merchant_id = sn.node_id\n", "WHERE s.is_active\n", "    AND sn.is_active\n", "    AND s.type = 'STORE_POLYGON'\n", "    and s.merchant_id not in (\n", "    select node_id from serviceability.ser_disruption_schedule\n", "        where reason_code = 'DISRUPTION_STORE_LAUNCH'\n", "            and active\n", "            and insert_ds_ist >= cast((current_date-interval '2' month) as varchar)\n", "    )\n", "    and CAST(json_extract(sn.meta, '$.city_name') AS VARCHAR) not in ('Not in service area')\n", "GROUP BY 1\"\"\"\n", "\n", "active_store = pd.read_sql_query(active_store_sql, con)"]}, {"cell_type": "code", "execution_count": null, "id": "4f765349-aec5-4e42-9ea9-99221caa7760", "metadata": {}, "outputs": [], "source": ["active_store_list = tuple(active_store[\"merchant_id\"].unique())"]}, {"cell_type": "code", "execution_count": null, "id": "b90f7c99-3a1b-42e3-9208-4b84385ef46d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "791ed0b9-2295-400a-8334-6663e485b15f", "metadata": {}, "outputs": [], "source": ["outlet_merchant_sql = \"\"\"select dm.city_name, pos_outlet_id as outlet_id, pos_outlet_name as outlet_name, \n", "frontend_merchant_id as merchant_id, frontend_merchant_name as merchant_name\n", "from dwh.dim_merchant_outlet_facility_mapping a\n", "join dwh.dim_merchant dm on a.frontend_merchant_id = dm.merchant_id and dm.is_current = true\n", "where a.is_current = true\n", "and a.is_mapping_enabled = true\n", "and a.is_current_mapping_active = true\n", "group by 1,2,3,4,5\n", "\"\"\"\n", "outlet_merchant_mapping = pd.read_sql_query(outlet_merchant_sql, con)"]}, {"cell_type": "code", "execution_count": null, "id": "d38f44c4-2d67-42ea-892d-79c447b583d7", "metadata": {}, "outputs": [], "source": ["selected_new_polygon = selected_new_polygon.merge(\n", "    outlet_merchant_mapping, how=\"left\", left_on=[\"Outlet Id\"], right_on=[\"outlet_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fecc8c60-fb6d-4e05-8d08-c3edba50ed1b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "dd305844-69c2-48f4-afd9-b2ad463eb71a", "metadata": {}, "outputs": [], "source": ["polygon_sql = f\"\"\"\n", "select city_name, merchant_name, a.* from\n", "(select merchant_id, ST_GeometryFromText(polygon) as pol, update_ts\n", "from (select cast(merchant_id as int) as merchant_id, polygon, update_ts, rank() over (partition by merchant_id order by update_ts desc) as rnk\n", "from serviceability.ser_polygon_refresh_logs\n", "where type='STORE_POLYGON'\n", "and update_ts < cast('{train_end_date}' as date) + interval '1' day\n", "and polygon is not null\n", ") where rnk =1) a\n", "join dwh.dim_merchant dm on a.merchant_id = dm.merchant_id and dm.is_current = true\n", "\"\"\"\n", "\n", "latest_polygon = pd.read_sql_query(polygon_sql, con)"]}, {"cell_type": "code", "execution_count": null, "id": "12695207-57dc-4066-986e-eff6bfc6d6b6", "metadata": {}, "outputs": [], "source": ["latest_polygon[latest_polygon.merchant_id == 33413]"]}, {"cell_type": "code", "execution_count": null, "id": "c5d9b1a5-fcc0-4440-8758-030adef95750", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d4d7758c-3ea7-4d72-9c7d-4a1e2d5f689a", "metadata": {}, "outputs": [], "source": ["buffer_tagging_sql = \"\"\"select a.* \n", "    from\n", "    (select a.*\n", "    from storeops_etls.merchant_buffer_stores_mapping a\n", "    join dwh.dim_merchant dm on a.parent_merchant_id = dm.merchant_id and dm.is_current\n", "    where a.merchant_id != parent_merchant_id\n", "    and a.city_name = dm.city_name) a\n", "    left join (select frontend_merchant_id as merchant_id, store_live_date\n", "    from dwh.dim_merchant_outlet_facility_mapping a\n", "    left join (select outlet_id, live_date as store_live_date\n", "        from dwh.dim_outlet\n", "        where is_current = true\n", "        and is_outlet_active = 1\n", "        group by 1,2) b on a.pos_outlet_id = b.outlet_id\n", "    where is_mapping_enabled and is_current\n", "    group by 1,2) b on a.merchant_id = b.merchant_id\n", "\n", "    -- where a.polygon_created_at < date_add('day', 1, b.store_live_date)\n", "    where a.area_covered > 0\n", "\"\"\"\n", "buffer_tagging = pd.read_sql_query(buffer_tagging_sql, con)"]}, {"cell_type": "code", "execution_count": null, "id": "f8f84ed9-b7aa-4736-94eb-de3620b8ebc9", "metadata": {}, "outputs": [], "source": ["# cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "4d0a7ac7-ef1f-46ac-8abc-ad730a800eee", "metadata": {}, "outputs": [], "source": ["orders = pd.read_csv(cwd + \"/orders_train_end_20240515.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "c4134bd1-5c4c-4c4b-808a-efd8f424de7d", "metadata": {}, "outputs": [], "source": ["daus = pd.read_csv(cwd + \"/daus_train_end_20240515.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "bf6ecfe9-3958-4c34-838d-d6597172a05e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b2263de4-f47d-4e3e-ac4e-efea9fb6b86a", "metadata": {}, "outputs": [], "source": ["order_date_start = orders[\"date_\"].max()\n", "dau_date_start = daus[\"date_\"].max()\n", "order_date_start, dau_date_start"]}, {"cell_type": "code", "execution_count": null, "id": "38bc1a45-9da8-4248-a4b7-fe87b5a29c5a", "metadata": {}, "outputs": [], "source": ["check_missing_polygon = active_store.merge(\n", "    latest_polygon, how=\"left\", on=[\"merchant_id\"], indicator=True\n", ")\n", "missing_old_polygon_stores = tuple(\n", "    list(\n", "        check_missing_polygon[check_missing_polygon[\"_merge\"] == \"left_only\"]\n", "        .merge(\n", "            orders[[\"merchant_id\"]].drop_duplicates(), on=[\"merchant_id\"], how=\"left\"\n", "        )[\"merchant_id\"]\n", "        .unique()\n", "    )\n", ")\n", "missing_old_polygon_stores"]}, {"cell_type": "code", "execution_count": null, "id": "62aaea2c-4c89-4015-95b0-f022931e7f89", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "111270fc-071d-49e2-b2ff-3accce5713ba", "metadata": {}, "outputs": [], "source": ["orders_sql = f\"\"\"\n", "    select city_name, merchant_id, merchant_name, cast(dt as date) as date_, sum(orders) as orders, sum(gmv) as gmv, sum(item_qty) as item_qty\n", "    from\n", "        (select a.*, city_name, merchant_id, merchant_name,\n", "        ST_Distance(geom, ST_POINT(merchant_store_longitude,merchant_store_latitude))*1000 as distance,\n", "        rank() over (partition by dt, lat, long order by ST_Distance(geom, ST_POINT(merchant_store_longitude,merchant_store_latitude))*1000 asc) as rnk\n", "        from\n", "        (select dt, lat, long,\n", "            ST_POINT(cast(long as double), cast(lat as double)) as geom,\n", "            sum(order_count) as orders,\n", "            sum(gmv) as gmv,\n", "            sum(total_product_quantity) as item_qty\n", "            from consumer_etls.hex9_user_order_gmv_ipc\n", "            where dt >= cast('{order_date_start}' as date) + interval '1' day\n", "        group by 1,lat,long) a\n", "\n", "        join (select city_name, merchant_name, merchant_store_latitude, merchant_store_longitude, a.* from\n", "                (\n", "                (select merchant_id, ST_GeometryFromText(polygon) as pol\n", "                from (select cast(merchant_id as int) as merchant_id, polygon, rank() over (partition by merchant_id order by update_ts desc) as rnk\n", "                from serviceability.ser_polygon_refresh_logs\n", "                where type='STORE_POLYGON' and\n", "                update_ts < cast('{train_end_date}' as date) + interval '1' day\n", "                and polygon is not null\n", "                and cast(merchant_id as int) not in {missing_old_polygon_stores}\n", "                ) where rnk =1)\n", "                \n", "                UNION ALL\n", "                \n", "                (select cast(merchant_id as int) as merchant_id, ST_GeometryFromText(polygon) as pol\n", "                from ds_etls.ds_live_store_polygons\n", "                where cast(merchant_id as int) in {missing_old_polygon_stores}\n", "                and city_name is not null\n", "                and type = 'STORE_POLYGON')\n", "            ) a\n", "            join dwh.dim_merchant dm on a.merchant_id = dm.merchant_id and dm.is_current = true\n", "            ) b\n", "        on ST_WITHIN(a.geom, b.pol)\n", "        )\n", "    -- where rnk = 1\n", "    group by 1,2,3,4\n", "\"\"\"\n", "\n", "orders_new = pd.read_sql_query(orders_sql, con)"]}, {"cell_type": "code", "execution_count": null, "id": "9d48bdd0-9742-4f16-86a4-b9ecbe4e0311", "metadata": {}, "outputs": [], "source": ["orders_new[\"date_\"].min(), orders_new[\"date_\"].max()"]}, {"cell_type": "code", "execution_count": null, "id": "35fbbd7a-01d4-4ee2-802a-a23174208e9b", "metadata": {}, "outputs": [], "source": ["orders_new.groupby([\"date_\"])[\"orders\"].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "deac8a34-2d60-46ea-b052-96f8117c3535", "metadata": {}, "outputs": [], "source": ["daus_new_sql = f\"\"\"\n", "    select city_name, merchant_id, merchant_name, cast(at_date_ist as date) as date_, sum(dau) as dau\n", "    from\n", "        (select a.*, city_name, merchant_id, merchant_name,\n", "        ST_Distance(geom, ST_POINT(merchant_store_longitude,merchant_store_latitude))*1000 as distance,\n", "        rank() over (partition by at_date_ist, lat, long order by ST_Distance(geom, ST_POINT(merchant_store_longitude,merchant_store_latitude))*1000 asc) as rnk\n", "        from\n", "        (select at_date_ist, lat1 as lat, long1 as long,\n", "            ST_POINT(cast(long1 as double), cast(lat1 as double)) as geom,\n", "            count(distinct device_id) as dau\n", "            from consumer_etls.daily_userid_nrl_dau_hex9\n", "            where at_date_ist >= cast('{dau_date_start}' as date) + interval '1' day\n", "        group by 1,lat1,long1) a\n", "\n", "        join (select city_name, merchant_name, merchant_store_latitude, merchant_store_longitude, a.* from\n", "                (\n", "                (select merchant_id, ST_GeometryFromText(polygon) as pol\n", "                from (select cast(merchant_id as int) as merchant_id, polygon, rank() over (partition by merchant_id order by update_ts desc) as rnk\n", "                from serviceability.ser_polygon_refresh_logs\n", "                where type='STORE_POLYGON' and\n", "                update_ts < cast('{train_end_date}' as date) + interval '1' day\n", "                and polygon is not null\n", "                and cast(merchant_id as int) not in {missing_old_polygon_stores}\n", "                ) where rnk =1)\n", "                \n", "                UNION ALL\n", "                \n", "                (select cast(merchant_id as int) as merchant_id, ST_GeometryFromText(polygon) as pol\n", "                from ds_etls.ds_live_store_polygons\n", "                where cast(merchant_id as int) in {missing_old_polygon_stores}\n", "                and city_name is not null\n", "                and type = 'STORE_POLYGON')\n", "            ) a\n", "            join dwh.dim_merchant dm on a.merchant_id = dm.merchant_id and dm.is_current = true\n", "            ) b\n", "        on ST_WITHIN(a.geom, b.pol)\n", "        )\n", "    -- where rnk = 1\n", "    group by 1,2,3,4\n", "\"\"\"\n", "\n", "daus_new = pd.read_sql_query(daus_new_sql, con)"]}, {"cell_type": "code", "execution_count": null, "id": "797374f1-e079-423a-aa00-1157b1adef38", "metadata": {}, "outputs": [], "source": ["daus_new[\"date_\"].min(), daus_new[\"date_\"].max()"]}, {"cell_type": "code", "execution_count": null, "id": "4c3f13a4-5807-4530-b3d2-343121f63b09", "metadata": {}, "outputs": [], "source": ["daus_new.groupby([\"date_\"])[\"dau\"].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "5cf305da-6f38-4041-9057-8ad41a0319d0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "875f7ad5-8612-49f0-b2a5-97d0ab34b1f9", "metadata": {}, "outputs": [], "source": ["if orders_new.shape[0] > 0:\n", "    orders = orders[orders.date_ >= start_date].append(orders_new, ignore_index=True)\n", "else:\n", "    orders = orders[orders.date_ >= start_date]"]}, {"cell_type": "code", "execution_count": null, "id": "95a3739c-7c9c-4efb-ba2d-670767511359", "metadata": {}, "outputs": [], "source": ["if daus_new.shape[0] > 0:\n", "    daus = daus[daus.date_ >= start_date].append(daus_new, ignore_index=True)\n", "else:\n", "    daus = daus[daus.date_ >= start_date]"]}, {"cell_type": "code", "execution_count": null, "id": "fcce72b6-d1cc-4e0c-b750-d8d4329f1c86", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "86efd121-7683-4d0f-a8c2-b273d9111f63", "metadata": {}, "outputs": [], "source": ["orders = orders.merge(\n", "    daus[[\"merchant_id\", \"date_\", \"dau\"]].drop_duplicates(),\n", "    how=\"left\",\n", "    on=[\"merchant_id\", \"date_\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "06a65105-d662-4f84-b66b-448af7648997", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1be4fb35-2f4c-46ae-919d-8053ee72aba9", "metadata": {}, "outputs": [], "source": ["orders[\"aov\"] = np.round(orders[\"gmv\"] * 1.00 / orders[\"orders\"], 0)\n", "orders[\"ipc\"] = np.round(orders[\"item_qty\"] * 1.00 / orders[\"orders\"], 2)"]}, {"cell_type": "code", "execution_count": null, "id": "84830816-9956-4a15-af08-f55838b63b16", "metadata": {}, "outputs": [], "source": ["orders.drop(columns=[\"gmv\", \"item_qty\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "c489d14f-2a9b-4f8c-a5d0-19fce239b08a", "metadata": {}, "outputs": [], "source": ["orders[\"date_\"].min(), orders[\"date_\"].max()"]}, {"cell_type": "code", "execution_count": null, "id": "f7f15fec-6a66-4b5f-a6b9-6d29690ba69b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3b433326-4700-4b87-9228-84e2911ddcea", "metadata": {}, "outputs": [], "source": ["polygon_change_input = pb.from_sheets(\n", "    \"1lepKjqGE5dKrp5D_0orbl4BFVv1RkaA2SZKDouDSy3k\", \"Buffer store input\"\n", ")\n", "# polygon_change_input = pd.read_csv('Buffer store input.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "4cf736c1-55e6-4e08-80fd-903ec6172659", "metadata": {}, "outputs": [], "source": ["polygon_change_input[\"merchant_id\"] = polygon_change_input[\"merchant_id\"].astype(int)\n", "polygon_change_input[\"is_uploaded_in_system\"] = polygon_change_input[\n", "    \"is_uploaded_in_system\"\n", "].astype(int)\n", "polygon_change_input[\"date_live_from\"] = pd.to_datetime(\n", "    polygon_change_input[\"date_live_from\"]\n", ").dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "e237376f-aa9a-481f-a74e-3c1114969a73", "metadata": {}, "outputs": [], "source": ["polygon_change_input.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "5e9ff533-0975-4d95-a429-9882204c87ee", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"upcoming_polygons\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"city_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"city name\",\n", "        },\n", "        {\n", "            \"name\": \"merchant_id\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"merchant id\",\n", "        },\n", "        {\n", "            \"name\": \"merchant_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"merchant name\",\n", "        },\n", "        {\n", "            \"name\": \"polygon\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"polygon\",\n", "        },\n", "        {\n", "            \"name\": \"date_live_from\",\n", "            \"type\": \"date\",\n", "            \"description\": \"date polygon live from\",\n", "        },\n", "        {\n", "            \"name\": \"store_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"type of store\",\n", "        },\n", "        {\n", "            \"name\": \"is_uploaded_in_system\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"flag to check if underlined polygon updated in the system table\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"merchant_id\", \"date_live_from\"],\n", "    \"partition_key\": [\"merchant_id\"],\n", "    \"incremental_key\": \"merchant_id\",\n", "    \"load_type\": \"partition_overwrite\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"This table stores upcoming polygon changes\",\n", "}\n", "\n", "pb.to_trino(polygon_change_input, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "72207f8b-0b9a-4d78-b92c-c9a713875a21", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d5028b8d-1159-429e-96aa-1fd676922873", "metadata": {}, "outputs": [], "source": ["orders_upcoming_polygon_sql = f\"\"\"\n", "    select city_name, merchant_id, merchant_name, \n", "    cast(dt as date) as date_, \n", "    sum(orders) as orders, sum(gmv) as gmv, sum(item_qty) as item_qty\n", "    from\n", "        (select a.*, city_name, merchant_id, merchant_name\n", "        from\n", "        (select dt, lat, long,\n", "            ST_POINT(cast(long as double), cast(lat as double)) as geom,\n", "            sum(order_count) as orders,\n", "            sum(gmv) as gmv,\n", "            sum(total_product_quantity) as item_qty\n", "            from consumer_etls.hex9_user_order_gmv_ipc\n", "            where dt >= cast('{start_date}' as date)\n", "        group by 1,lat,long) a\n", "\n", "        join (select *, ST_GeometryFromText(polygon) as pol \n", "            from interim.upcoming_polygons\n", "            where merchant_id is not null) b on ST_WITHIN(a.geom, b.pol)\n", "        )\n", "    group by 1,2,3,4\n", "\"\"\"\n", "orders_upcoming_polygon = pd.read_sql_query(orders_upcoming_polygon_sql, con)"]}, {"cell_type": "code", "execution_count": null, "id": "44a2c4cb-05f2-47fe-82ec-079a8ab0f56a", "metadata": {}, "outputs": [], "source": ["# orders_upcoming_polygon.to_csv('orders_upcoming_polygon.csv', index=False)\n", "\n", "# orders_upcoming_polygon = pd.read_csv('orders_upcoming_polygon.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "20f77dfa-816c-4647-be79-d98cce69643e", "metadata": {}, "outputs": [], "source": ["daus_upcoming_polygon_sql = f\"\"\"\n", "    select city_name, merchant_id, merchant_name, cast(at_date_ist as date) as date_, sum(dau) as dau\n", "    from\n", "        (select a.*, city_name, merchant_id, merchant_name\n", "        from\n", "        (select at_date_ist, lat1 as lat, long1 as long,\n", "            ST_POINT(cast(long1 as double), cast(lat1 as double)) as geom,\n", "            count(distinct device_id) as dau\n", "            from consumer_etls.daily_userid_nrl_dau_hex9\n", "            where at_date_ist >= cast('{start_date}' as date)\n", "        group by 1,lat1,long1) a\n", "\n", "        join (select *, ST_GeometryFromText(polygon) as pol \n", "            from interim.upcoming_polygons\n", "            where merchant_id is not null) b \n", "        on ST_WITHIN(a.geom, b.pol)\n", "        )\n", "    group by 1,2,3,4\n", "\"\"\"\n", "\n", "daus_upcoming_polygon = pd.read_sql_query(daus_upcoming_polygon_sql, con)"]}, {"cell_type": "code", "execution_count": null, "id": "d917f14e-b051-48e5-bf4c-e2f03a5878a1", "metadata": {}, "outputs": [], "source": ["# daus_upcoming_polygon = daus_upcoming_polygon2.append(daus_upcoming_polygon1).sort_values(by=[\"city_name\", \"merchant_id\", \"date_\"]).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "b93d3fac-9d20-4a1f-9548-f33ae49d319f", "metadata": {}, "outputs": [], "source": ["# daus_upcoming_polygon.to_csv('daus_upcoming_polygon.csv', index=False)\n", "\n", "# daus_upcoming_polygon = pd.read_csv('daus_upcoming_polygon.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "51b7100c-be8e-422a-9347-bde550fbf67d", "metadata": {}, "outputs": [], "source": ["orders_upcoming_polygon = orders_upcoming_polygon.merge(\n", "    daus_upcoming_polygon[[\"merchant_id\", \"date_\", \"dau\"]].drop_duplicates(),\n", "    how=\"left\",\n", "    on=[\"merchant_id\", \"date_\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ff88b993-ce8d-45d8-963b-26f1ee1c312b", "metadata": {}, "outputs": [], "source": ["orders_upcoming_polygon[\"aov\"] = np.round(\n", "    orders_upcoming_polygon[\"gmv\"] * 1.00 / orders_upcoming_polygon[\"orders\"], 0\n", ")\n", "orders_upcoming_polygon[\"ipc\"] = np.round(\n", "    orders_upcoming_polygon[\"item_qty\"] * 1.00 / orders_upcoming_polygon[\"orders\"], 2\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "dab602dc-c74f-4729-8505-3cab54788aba", "metadata": {}, "outputs": [], "source": ["orders_upcoming_polygon.drop(columns=[\"gmv\", \"item_qty\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "923fa423-0ba9-46ea-92cf-038f182b8d55", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "be2d29ed-66ff-4a63-96c6-831185f368a2", "metadata": {}, "outputs": [], "source": ["orders[\"is_upcoming_polygon\"] = 0\n", "orders_upcoming_polygon[\"is_upcoming_polygon\"] = 1"]}, {"cell_type": "code", "execution_count": null, "id": "50a9de39-f796-4f57-8a85-7cda35a8a820", "metadata": {}, "outputs": [], "source": ["orders.shape"]}, {"cell_type": "code", "execution_count": null, "id": "70fe5ad6-006a-400e-b7ed-57b68324b43b", "metadata": {}, "outputs": [], "source": ["orders = orders.append(orders_upcoming_polygon)"]}, {"cell_type": "code", "execution_count": null, "id": "8b300e50-4ed7-442c-a9f2-430eed222ec0", "metadata": {}, "outputs": [], "source": ["orders.shape"]}, {"cell_type": "code", "execution_count": null, "id": "80136e53-ad25-4d85-a970-5677b251b90b", "metadata": {}, "outputs": [], "source": ["orders[\"date_\"].min(), orders[\"date_\"].max()"]}, {"cell_type": "code", "execution_count": null, "id": "2070016a-bfc6-4d55-a5e9-9e168e9e8036", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d374b35e-46eb-4da7-b31a-f1fb87ff6771", "metadata": {}, "outputs": [], "source": ["dt_actual_orders_sql = f\"\"\"\n", "select date(oid.cart_checkout_ts_ist) as date_,\n", "count(distinct cart_id) as orders, sum(total_selling_price) as gmv,\n", "sum(total_selling_price)*1.00/count(distinct cart_id) as aov, sum(total_product_quantity)*1.00/count(distinct cart_id) as ipc\n", "from dwh.fact_sales_order_details oid\n", "join dwh.dim_merchant dm on oid.frontend_merchant_id = dm.merchant_id and dm.is_current\n", "left join dwh.dim_merchant_outlet_facility_mapping m on oid.frontend_merchant_id = m.frontend_merchant_id and m.is_current and m.is_mapping_enabled and m.is_current_mapping_active\n", "where oid.order_create_dt_ist  > cast('{train_end_date}' as date)\n", "AND oid.order_create_dt_ist < current_date\n", "AND oid.is_internal_order = FALSE\n", "group by 1\n", "\"\"\"\n", "dt_actual_orders = pd.read_sql_query(dt_actual_orders_sql, con)"]}, {"cell_type": "code", "execution_count": null, "id": "ce0efaf0-afd2-4d4d-9174-a415e59d1129", "metadata": {}, "outputs": [], "source": ["first_order = (\n", "    orders[orders.orders > 20].groupby([\"merchant_id\"])[\"date_\"].min().reset_index()\n", ")\n", "first_order.rename(columns={\"date_\": \"first_order_date\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "61443a06-6eee-44a0-8df9-350afb9009da", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fb81383a-d14b-4eb3-8877-f151cd405629", "metadata": {}, "outputs": [], "source": ["# conv_sql = f\"\"\"SELECT\n", "#     snapshot_date_ist as date_,\n", "#     a.merchant_id,\n", "#     sum(overall_conversion) as conv,\n", "#     SUM(daily_active_users) as daus\n", "# FROM dwh.agg_daily_consumer_conversion_details a\n", "# join dwh.dim_merchant dm on a.merchant_id = cast(dm.merchant_id as varchar) and dm.is_current\n", "# where snapshot_date_ist >= cast('{start_date}' as date)\n", "# and snapshot_date_ist < cast('{ov_end_date}' as date)\n", "# and snapshot_hour_ist = 24\n", "# and a.merchant_id != 'Overall'\n", "# GROUP BY 1,2\"\"\"\n", "\n", "# conv = pd.read_sql_query(conv_sql, con)"]}, {"cell_type": "code", "execution_count": null, "id": "55f27a0f-7458-4dc1-b419-a87d2d8985ad", "metadata": {}, "outputs": [], "source": ["# conv.merchant_id = conv.merchant_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "f940b15e-3f44-467d-90f9-1cdc05674a7b", "metadata": {}, "outputs": [], "source": ["# orders = orders.merge(conv[[\"date_\", \"merchant_id\", \"daus\", \"conv\"]].drop_duplicates(), on=[\"date_\", \"merchant_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "14634a28-3f0f-448c-9bea-a124b6b2b741", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "46561296-4c8c-47d1-b76e-2acf9cca0e9b", "metadata": {}, "outputs": [], "source": ["## incorrect data in source fro below dates\n", "\n", "orders = orders[~orders[\"date_\"].isin(remove_dates)]"]}, {"cell_type": "code", "execution_count": null, "id": "96265348-1249-4fe1-a96e-d9175c5b126f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7db82f0f-e537-41c7-991b-b8ccf12e0e1c", "metadata": {}, "outputs": [], "source": ["orders[\"conversion\"] = np.round(orders[\"orders\"] * 100.0 / orders[\"dau\"], 1)"]}, {"cell_type": "code", "execution_count": null, "id": "3ec3fd7a-d048-4bc9-88c9-bb08840f0a14", "metadata": {}, "outputs": [], "source": ["orders[orders[\"conversion\"] > 100].shape"]}, {"cell_type": "code", "execution_count": null, "id": "69b1f86b-e9e5-495d-84aa-8f09faf5d9db", "metadata": {}, "outputs": [], "source": ["orders = (\n", "    orders[\n", "        (orders[\"conversion\"] < 100)\n", "        | (orders.merchant_id.isin(list(orders_upcoming_polygon.merchant_id.unique())))\n", "    ]\n", "    .reset_index()\n", "    .drop(columns=[\"index\"])\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b10ede4f-d29f-404d-9d48-f6c055b8d0da", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0ddc3af8-47e4-4125-9675-583689346fd6", "metadata": {}, "outputs": [], "source": ["orders[\"merchant_id\"] = orders[\"merchant_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "0009838c-309c-411a-9cfc-a5b55ed11f74", "metadata": {}, "outputs": [], "source": ["# orders = orders.merge(active_store, on=[\"merchant_id\"], how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "id": "0e38e884-85b5-4dde-bc36-be73e6694061", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7f3611cb-8ddd-48f1-b033-6aea5560bd67", "metadata": {}, "outputs": [], "source": ["store_live_sql = \"\"\"select frontend_merchant_id as merchant_id, pos_outlet_id as outlet_id, store_live_date, pos_outlet_name as outlet_name\n", "from dwh.dim_merchant_outlet_facility_mapping a\n", "left join (select outlet_id, live_date as store_live_date\n", "    from dwh.dim_outlet\n", "    where is_current = true\n", "    and is_outlet_active = 1\n", "    group by 1,2) b on a.pos_outlet_id = b.outlet_id\n", "where is_mapping_enabled and is_current\n", "group by 1,2,3,4\"\"\"\n", "\n", "store_live = pd.read_sql_query(store_live_sql, con)"]}, {"cell_type": "code", "execution_count": null, "id": "b5ac058a-e7b4-4d2f-b08f-6ee12a8a7e3b", "metadata": {}, "outputs": [], "source": ["orders = orders.merge(store_live, how=\"left\", on=[\"merchant_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "a076588b-253d-4a08-b5ae-3548055385ad", "metadata": {}, "outputs": [], "source": ["orders = orders.merge(first_order, how=\"left\", on=[\"merchant_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "b0a82a25-bfc6-4fd7-a181-0a8a32719663", "metadata": {}, "outputs": [], "source": ["orders[\"store_live_date\"] = np.where(\n", "    (orders[\"store_live_date\"] > orders[\"first_order_date\"])\n", "    | (orders[\"store_live_date\"].isna())\n", "    | (orders[\"store_live_date\"] == \"1900-01-01\"),\n", "    orders[\"first_order_date\"],\n", "    orders[\"store_live_date\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2b2058c1-215c-4e50-b7a6-01be348fbc9f", "metadata": {}, "outputs": [], "source": ["orders[\"store_live_date\"].fillna(\"1900-01-01\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "4ac98149-529d-4bcd-8a8b-23cede7c9ec1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f4c6429a-9316-4829-8e7f-3ede949aefda", "metadata": {"tags": []}, "outputs": [], "source": ["hfs_sql = f\"\"\"select date_, 1 as hfs_tag\n", "from ba_etls.master_date_calendar\n", "where date_ >= cast('{start_date}' as varchar)\n", "and date_ < cast((cast('{ov_end_date}' as date) + interval '1' day) as varchar)\n", "and hfs_tag != 'Non HFS' and hfs_tag != ''\n", "group by 1,2\n", "order by 1\"\"\"\n", "hfs_days = pd.read_sql_query(hfs_sql, con)"]}, {"cell_type": "code", "execution_count": null, "id": "259ffdc8-498a-4191-8901-1530412dfe38", "metadata": {}, "outputs": [], "source": ["# hfs_days[\"date_\"] = pd.to_datetime(hfs_days[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "0a2761c2-1e1a-49ff-8342-0881ddc10efd", "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame([])\n", "\n", "import calendar\n", "\n", "\n", "def last_day_of_month(year, month):\n", "    return calendar.monthrange(year, month)[1]\n", "\n", "\n", "year = 2022\n", "for month in range(1, 13):\n", "    df = df.append(\n", "        pd.DataFrame(\n", "            data=[\n", "                [\n", "                    pd.to_datetime(\n", "                        str(year)\n", "                        + \"-\"\n", "                        + str(month)\n", "                        + \"-\"\n", "                        + str(last_day_of_month(2022, month))\n", "                    ),\n", "                    1,\n", "                ]\n", "            ],\n", "            columns=[\"date_\", \"hfs_tag\"],\n", "        )\n", "    )\n", "\n", "    for day in range(1, 8):\n", "        df = df.append(\n", "            pd.DataFrame(\n", "                data=[\n", "                    [pd.to_datetime(str(year) + \"-\" + str(month) + \"-\" + str(day)), 1]\n", "                ],\n", "                columns=[\"date_\", \"hfs_tag\"],\n", "            )\n", "        )"]}, {"cell_type": "code", "execution_count": null, "id": "370aad39-0e92-4b1a-9741-31c05640b99f", "metadata": {}, "outputs": [], "source": ["hfs_days = hfs_days.append(df).drop_duplicates()\n", "hfs_days[\"date_\"] = pd.to_datetime(hfs_days[\"date_\"])\n", "hfs_days = hfs_days.sort_values(by=[\"date_\"]).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "2e9ee67f-ce4f-4540-93f9-208fb152ade9", "metadata": {}, "outputs": [], "source": ["for i in range(0, len(hfs_days)):\n", "    if i == 0:\n", "        hfs_days.loc[i, \"hfs_days_passed\"] = 1\n", "    elif (hfs_days.loc[i - 1, \"hfs_days_passed\"] >= 7) & (\n", "        (hfs_days.loc[i, \"date_\"] - hfs_days.loc[i - 1, \"date_\"]).days > 20\n", "    ):\n", "        hfs_days.loc[i, \"hfs_days_passed\"] = 1\n", "    else:\n", "        hfs_days.loc[i, \"hfs_days_passed\"] = hfs_days.loc[i - 1, \"hfs_days_passed\"] + 1"]}, {"cell_type": "code", "execution_count": null, "id": "3d0ac268-8d2f-4f66-b135-5a97c24badef", "metadata": {}, "outputs": [], "source": ["hfs_days[\"date_\"] = hfs_days[\"date_\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "1289b324-7404-478d-837d-742044f2f8b3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "98be493c-b1f2-43c1-a38f-2e61e1771a86", "metadata": {}, "outputs": [], "source": ["orders = orders.merge(hfs_days[[\"date_\", \"hfs_tag\"]], how=\"left\", on=[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "864c7738-6ed8-4294-a1ad-e954196a27fd", "metadata": {}, "outputs": [], "source": ["orders[\"hfs_tag\"].fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "65663a1a-787b-447d-9a9d-24bddd7d8ee9", "metadata": {}, "outputs": [], "source": ["orders[\"wom\"] = pd.to_datetime(orders[\"date_\"]).dt.day // 7 + 1"]}, {"cell_type": "code", "execution_count": null, "id": "72260677-d810-4ea0-80fb-869cce03da86", "metadata": {}, "outputs": [], "source": ["orders.hfs_tag.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "25ac44e3-dbf3-4b5f-9d4b-b9c08e17a974", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6ae5dea8-6d71-4dde-aec1-1f3d71874c1e", "metadata": {}, "outputs": [], "source": ["festival_sql = f\"\"\"select event_date as date_, event_name, calendar_holiday_type, (case when disruption = 'Major' then 1 else 0 end) as is_major_disruption\n", "from dwh.bl_calendar_events_holidays\n", "where event_date >= cast('{start_date}' as date)\n", "and event_date < cast('{ov_end_date}' as date) + interval '1' day\n", "group by 1,2,3,4\n", "order by 1\"\"\"\n", "\n", "all_festivals = pd.read_sql_query(festival_sql, con)"]}, {"cell_type": "code", "execution_count": null, "id": "f5639c31-e829-465d-9e29-3eea917d7a29", "metadata": {}, "outputs": [], "source": ["def func_join_str(df):\n", "    return \", \".join(df[\"event_name\"])\n", "\n", "\n", "def func_join_str1(df):\n", "    return \", \".join(df[\"calendar_holiday_type\"])"]}, {"cell_type": "code", "execution_count": null, "id": "dba2299f-0deb-4eef-ba26-086f183fd740", "metadata": {}, "outputs": [], "source": ["festivals = (\n", "    all_festivals.groupby([\"date_\"])\n", "    .apply(func_join_str)\n", "    .reset_index()\n", "    .merge(\n", "        all_festivals.groupby([\"date_\"]).apply(func_join_str1).reset_index(),\n", "        how=\"left\",\n", "        on=[\"date_\"],\n", "    )\n", "    .merge(all_festivals.groupby([\"date_\"])[\"is_major_disruption\"].max().reset_index())\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b1fdf51a-415a-4968-9886-9bee4c1cffbe", "metadata": {}, "outputs": [], "source": ["festivals.rename(\n", "    columns={\"0_x\": \"event_name\", \"0_y\": \"calendar_holiday_type\"}, inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2028f61c-4776-4c04-8ff5-32c04cd94676", "metadata": {}, "outputs": [], "source": ["## pb.to_sheets(festivals, \"1lepKjqGE5dKrp5D_0orbl4BFVv1RkaA2SZKDouDSy3k\", \"festive_dates\")"]}, {"cell_type": "code", "execution_count": null, "id": "5aae594e-ebe5-4c9e-ae29-85af214b3b49", "metadata": {}, "outputs": [], "source": ["orders = orders.merge(festivals, on=[\"date_\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "638b667b-b0fa-456f-9353-1fe917124d15", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ed061865-ef7c-4674-a008-edaebd3fe6e0", "metadata": {}, "outputs": [], "source": ["orders1 = orders.sort_values(\n", "    by=[\"city_name\", \"outlet_id\", \"is_upcoming_polygon\", \"date_\"],\n", "    ascending=[True, True, True, True],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "75dd37dd-331d-49fe-a43a-58391ecc79bd", "metadata": {}, "outputs": [], "source": ["orders1[\"day\"] = pd.to_datetime(orders1[\"date_\"]).dt.strftime(\"%A\")"]}, {"cell_type": "code", "execution_count": null, "id": "29830d90-05bc-45bf-a2a7-f20ee8c63b04", "metadata": {}, "outputs": [], "source": ["orders1[\"dod_change\"] = orders1.groupby([\"merchant_id\"])[\"orders\"].pct_change(1) + 1\n", "orders1[\"wow_change\"] = orders1.groupby([\"merchant_id\"])[\"orders\"].pct_change(7) + 1\n", "orders1[\"wo4w_change\"] = orders1.groupby([\"merchant_id\"])[\"orders\"].pct_change(28) + 1\n", "\n", "\n", "orders1[\"dod_conv_change\"] = orders1.groupby([\"merchant_id\"])[\"conversion\"].diff(1)\n", "orders1[\"wow_conv_change\"] = orders1.groupby([\"merchant_id\"])[\"conversion\"].diff(7)\n", "orders1[\"wo4w_conv_change\"] = orders1.groupby([\"merchant_id\"])[\"conversion\"].diff(28)"]}, {"cell_type": "code", "execution_count": null, "id": "a1e9787c-b7a7-495c-a366-9d0a3fd622ec", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "af3b0b87-49f7-4fad-94cf-d7439bdd1ff0", "metadata": {}, "outputs": [], "source": ["orders2 = (\n", "    orders1[\n", "        orders1[\"date_\"]\n", "        >= pd.to_datetime(orders1[\"store_live_date\"]) + timed<PERSON>ta(days=60)\n", "    ]\n", "    .reset_index()\n", "    .drop(columns=[\"index\"])\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0af17192-6c91-46b5-81ee-e318db19cf25", "metadata": {}, "outputs": [], "source": ["orders2[\"event_name\"].fillna(\"-\", inplace=True)\n", "orders2[\"is_major_disruption\"].fillna(\"-\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "17eef3dc-c418-4303-bae8-2b14e17eb989", "metadata": {}, "outputs": [], "source": ["day_outlet_level_orders = (\n", "    orders2[(orders2[\"date_\"] >= start_date) & (orders2[\"date_\"] <= ov_end_date)]\n", "    .groupby([\"outlet_id\", \"day\"])\n", "    .agg({\"wow_change\": \"mean\", \"wo4w_change\": \"mean\"})\n", "    .reset_index()\n", ")\n", "day_outlet_level_orders.rename(\n", "    columns={\"wow_change\": \"day_outlet_avg_wow\", \"wo4w_change\": \"day_outlet_avg_wo4w\"},\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9e364d28-0a3c-45fa-bf84-902ba24503af", "metadata": {}, "outputs": [], "source": ["orders2 = orders2.merge(day_outlet_level_orders, how=\"left\", on=[\"outlet_id\", \"day\"])"]}, {"cell_type": "code", "execution_count": null, "id": "3bf3174c-ead7-447f-958c-06101ea3f4d7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ad879746-6d6e-44e7-9ae3-8cb76a283921", "metadata": {}, "outputs": [], "source": ["orders2[\"rank\"] = orders2.groupby([\"outlet_id\", \"is_upcoming_polygon\"])[\"date_\"].rank(\n", "    method=\"min\", ascending=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "70ff701c-eee2-4df0-846a-771ecdca339a", "metadata": {}, "outputs": [], "source": ["orders3 = orders2.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "c1933b63-d00e-4ab8-b30c-e72c4a2ec7eb", "metadata": {}, "outputs": [], "source": ["orders3 = orders3[orders3.orders > 100]"]}, {"cell_type": "code", "execution_count": null, "id": "93a16273-040c-410f-b5e2-79a135870209", "metadata": {"tags": []}, "outputs": [], "source": ["orders3[\"latest_order_above_500\"] = 0\n", "orders3[\"latest_aov_order_above_500\"] = 0\n", "orders3[\"latest_qty_order_above_500\"] = 0\n", "\n", "orders3[\"latest_non_disruption_day_orders\"] = 0\n", "orders3[\"latest_non_disruption_day_aov\"] = 0\n", "orders3[\"latest_non_disruption_day_qty\"] = 0\n", "\n", "orders3_1 = orders3[orders3[\"day\"] == \"Monday\"].reset_index()\n", "orders3_1[\"rank\"] = orders3_1.groupby([\"outlet_id\", \"is_upcoming_polygon\"])[\n", "    \"date_\"\n", "].rank(method=\"min\", ascending=True)\n", "for i in range(0, len(orders3_1)):\n", "    if orders3_1.loc[i, \"rank\"] == 1:\n", "        orders3_1.loc[i, \"latest_order_above_500\"] = orders3_1.loc[i, \"orders\"]\n", "        orders3_1.loc[i, \"latest_aov_order_above_500\"] = orders3_1.loc[i, \"aov\"]\n", "        orders3_1.loc[i, \"latest_qty_order_above_500\"] = orders3_1.loc[i, \"ipc\"]\n", "        orders3_1.loc[i, \"latest_non_disruption_day_orders\"] = orders3_1.loc[\n", "            i, \"orders\"\n", "        ]\n", "        orders3_1.loc[i, \"latest_non_disruption_day_aov\"] = orders3_1.loc[i, \"aov\"]\n", "        orders3_1.loc[i, \"latest_non_disruption_day_qty\"] = orders3_1.loc[i, \"ipc\"]\n", "\n", "    else:\n", "        orders3_1.loc[i, \"latest_order_above_500\"] = np.where(\n", "            orders3_1.loc[i, \"orders\"] < 500,\n", "            orders3_1.loc[i - 1, \"latest_order_above_500\"],\n", "            orders3_1.loc[i, \"orders\"],\n", "        )\n", "        orders3_1.loc[i, \"latest_aov_order_above_500\"] = np.where(\n", "            orders3_1.loc[i, \"orders\"] < 500,\n", "            orders3_1.loc[i - 1, \"latest_aov_order_above_500\"],\n", "            orders3_1.loc[i, \"aov\"],\n", "        )\n", "        orders3_1.loc[i, \"latest_qty_order_above_500\"] = np.where(\n", "            orders3_1.loc[i, \"orders\"] < 500,\n", "            orders3_1.loc[i - 1, \"latest_qty_order_above_500\"],\n", "            orders3_1.loc[i, \"ipc\"],\n", "        )\n", "\n", "        orders3_1.loc[i, \"latest_non_disruption_day_orders\"] = np.where(\n", "            (\n", "                (orders3_1.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_1.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_1.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_1.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_1.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_1.loc[i, \"dod_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_1.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_1.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_1.loc[i, \"wo4w_conv_change\"] < -2)\n", "            ),\n", "            orders3_1.loc[i - 1, \"latest_non_disruption_day_orders\"],\n", "            orders3_1.loc[i, \"orders\"],\n", "        )\n", "        orders3_1.loc[i, \"latest_non_disruption_day_aov\"] = np.where(\n", "            (\n", "                (orders3_1.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_1.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_1.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_1.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_1.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_1.loc[i, \"dod_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_1.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_1.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_1.loc[i, \"wo4w_conv_change\"] < -2)\n", "            ),\n", "            orders3_1.loc[i - 1, \"latest_non_disruption_day_aov\"],\n", "            orders3_1.loc[i, \"aov\"],\n", "        )\n", "        orders3_1.loc[i, \"latest_non_disruption_day_qty\"] = np.where(\n", "            (\n", "                (orders3_1.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_1.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_1.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_1.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_1.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_1.loc[i, \"dod_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_1.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_1.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_1.loc[i, \"wo4w_conv_change\"] < -2)\n", "            ),\n", "            orders3_1.loc[i - 1, \"latest_non_disruption_day_qty\"],\n", "            orders3_1.loc[i, \"ipc\"],\n", "        )\n", "\n", "\n", "orders3_2 = orders3[orders3[\"day\"] == \"Tuesday\"].reset_index()\n", "orders3_2[\"rank\"] = orders3_2.groupby([\"outlet_id\", \"is_upcoming_polygon\"])[\n", "    \"date_\"\n", "].rank(method=\"min\", ascending=True)\n", "for i in range(0, len(orders3_2)):\n", "    if orders3_2.loc[i, \"rank\"] == 1:\n", "        orders3_2.loc[i, \"latest_order_above_500\"] = orders3_2.loc[i, \"orders\"]\n", "        orders3_2.loc[i, \"latest_aov_order_above_500\"] = orders3_2.loc[i, \"aov\"]\n", "        orders3_2.loc[i, \"latest_qty_order_above_500\"] = orders3_2.loc[i, \"ipc\"]\n", "        orders3_2.loc[i, \"latest_non_disruption_day_orders\"] = orders3_2.loc[\n", "            i, \"orders\"\n", "        ]\n", "        orders3_2.loc[i, \"latest_non_disruption_day_aov\"] = orders3_2.loc[i, \"aov\"]\n", "        orders3_2.loc[i, \"latest_non_disruption_day_qty\"] = orders3_2.loc[i, \"ipc\"]\n", "\n", "    else:\n", "        orders3_2.loc[i, \"latest_order_above_500\"] = np.where(\n", "            orders3_2.loc[i, \"orders\"] < 500,\n", "            orders3_2.loc[i - 1, \"latest_order_above_500\"],\n", "            orders3_2.loc[i, \"orders\"],\n", "        )\n", "        orders3_2.loc[i, \"latest_aov_order_above_500\"] = np.where(\n", "            orders3_2.loc[i, \"orders\"] < 500,\n", "            orders3_2.loc[i - 1, \"latest_aov_order_above_500\"],\n", "            orders3_2.loc[i, \"aov\"],\n", "        )\n", "        orders3_2.loc[i, \"latest_qty_order_above_500\"] = np.where(\n", "            orders3_2.loc[i, \"orders\"] < 500,\n", "            orders3_2.loc[i - 1, \"latest_qty_order_above_500\"],\n", "            orders3_2.loc[i, \"ipc\"],\n", "        )\n", "\n", "        orders3_2.loc[i, \"latest_non_disruption_day_orders\"] = np.where(\n", "            (\n", "                (orders3_2.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_2.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_2.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_2.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_2.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_2.loc[i, \"dod_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_2.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_2.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_2.loc[i, \"wo4w_conv_change\"] < -2)\n", "            ),\n", "            orders3_2.loc[i - 1, \"latest_non_disruption_day_orders\"],\n", "            orders3_2.loc[i, \"orders\"],\n", "        )\n", "        orders3_2.loc[i, \"latest_non_disruption_day_aov\"] = np.where(\n", "            (\n", "                (orders3_2.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_2.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_2.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_2.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_2.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_2.loc[i, \"dod_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_2.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_2.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_2.loc[i, \"wo4w_conv_change\"] < -2)\n", "            ),\n", "            orders3_2.loc[i - 1, \"latest_non_disruption_day_aov\"],\n", "            orders3_2.loc[i, \"aov\"],\n", "        )\n", "        orders3_2.loc[i, \"latest_non_disruption_day_qty\"] = np.where(\n", "            (\n", "                (orders3_2.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_2.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_2.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_2.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_2.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_2.loc[i, \"dod_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_2.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_2.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_2.loc[i, \"wo4w_conv_change\"] < -2)\n", "            ),\n", "            orders3_2.loc[i - 1, \"latest_non_disruption_day_qty\"],\n", "            orders3_2.loc[i, \"ipc\"],\n", "        )\n", "\n", "\n", "orders3_3 = orders3[orders3[\"day\"] == \"Wednesday\"].reset_index()\n", "orders3_3[\"rank\"] = orders3_3.groupby([\"outlet_id\", \"is_upcoming_polygon\"])[\n", "    \"date_\"\n", "].rank(method=\"min\", ascending=True)\n", "for i in range(0, len(orders3_3)):\n", "    if orders3_3.loc[i, \"rank\"] == 1:\n", "        orders3_3.loc[i, \"latest_order_above_500\"] = orders3_3.loc[i, \"orders\"]\n", "        orders3_3.loc[i, \"latest_aov_order_above_500\"] = orders3_3.loc[i, \"aov\"]\n", "        orders3_3.loc[i, \"latest_qty_order_above_500\"] = orders3_3.loc[i, \"ipc\"]\n", "        orders3_3.loc[i, \"latest_non_disruption_day_orders\"] = orders3_3.loc[\n", "            i, \"orders\"\n", "        ]\n", "        orders3_3.loc[i, \"latest_non_disruption_day_aov\"] = orders3_3.loc[i, \"aov\"]\n", "        orders3_3.loc[i, \"latest_non_disruption_day_qty\"] = orders3_3.loc[i, \"ipc\"]\n", "\n", "    else:\n", "        orders3_3.loc[i, \"latest_order_above_500\"] = np.where(\n", "            orders3_3.loc[i, \"orders\"] < 500,\n", "            orders3_3.loc[i - 1, \"latest_order_above_500\"],\n", "            orders3_3.loc[i, \"orders\"],\n", "        )\n", "        orders3_3.loc[i, \"latest_aov_order_above_500\"] = np.where(\n", "            orders3_3.loc[i, \"orders\"] < 500,\n", "            orders3_3.loc[i - 1, \"latest_aov_order_above_500\"],\n", "            orders3_3.loc[i, \"aov\"],\n", "        )\n", "        orders3_3.loc[i, \"latest_qty_order_above_500\"] = np.where(\n", "            orders3_3.loc[i, \"orders\"] < 500,\n", "            orders3_3.loc[i - 1, \"latest_qty_order_above_500\"],\n", "            orders3_3.loc[i, \"ipc\"],\n", "        )\n", "\n", "        orders3_3.loc[i, \"latest_non_disruption_day_orders\"] = np.where(\n", "            (\n", "                (orders3_3.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_3.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_3.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_3.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_3.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_3.loc[i, \"dod_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_3.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_3.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_3.loc[i, \"wo4w_conv_change\"] < -2)\n", "            ),\n", "            orders3_3.loc[i - 1, \"latest_non_disruption_day_orders\"],\n", "            orders3_3.loc[i, \"orders\"],\n", "        )\n", "        orders3_3.loc[i, \"latest_non_disruption_day_aov\"] = np.where(\n", "            (\n", "                (orders3_3.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_3.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_3.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_3.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_3.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_3.loc[i, \"dod_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_3.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_3.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_3.loc[i, \"wo4w_conv_change\"] < -2)\n", "            ),\n", "            orders3_3.loc[i - 1, \"latest_non_disruption_day_aov\"],\n", "            orders3_3.loc[i, \"aov\"],\n", "        )\n", "        orders3_3.loc[i, \"latest_non_disruption_day_qty\"] = np.where(\n", "            (\n", "                (orders3_3.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_3.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_3.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_3.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_3.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_3.loc[i, \"dod_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_3.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_3.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_3.loc[i, \"wo4w_conv_change\"] < -2)\n", "            ),\n", "            orders3_3.loc[i - 1, \"latest_non_disruption_day_qty\"],\n", "            orders3_3.loc[i, \"ipc\"],\n", "        )\n", "\n", "\n", "orders3_4 = orders3[orders3[\"day\"] == \"Thursday\"].reset_index()\n", "orders3_4[\"rank\"] = orders3_4.groupby([\"outlet_id\", \"is_upcoming_polygon\"])[\n", "    \"date_\"\n", "].rank(method=\"min\", ascending=True)\n", "for i in range(0, len(orders3_4)):\n", "    if orders3_4.loc[i, \"rank\"] == 1:\n", "        orders3_4.loc[i, \"latest_order_above_500\"] = orders3_4.loc[i, \"orders\"]\n", "        orders3_4.loc[i, \"latest_aov_order_above_500\"] = orders3_4.loc[i, \"aov\"]\n", "        orders3_4.loc[i, \"latest_qty_order_above_500\"] = orders3_4.loc[i, \"ipc\"]\n", "        orders3_4.loc[i, \"latest_non_disruption_day_orders\"] = orders3_4.loc[\n", "            i, \"orders\"\n", "        ]\n", "        orders3_4.loc[i, \"latest_non_disruption_day_aov\"] = orders3_4.loc[i, \"aov\"]\n", "        orders3_4.loc[i, \"latest_non_disruption_day_qty\"] = orders3_4.loc[i, \"ipc\"]\n", "\n", "    else:\n", "        orders3_4.loc[i, \"latest_order_above_500\"] = np.where(\n", "            orders3_4.loc[i, \"orders\"] < 500,\n", "            orders3_4.loc[i - 1, \"latest_order_above_500\"],\n", "            orders3_4.loc[i, \"orders\"],\n", "        )\n", "        orders3_4.loc[i, \"latest_aov_order_above_500\"] = np.where(\n", "            orders3_4.loc[i, \"orders\"] < 500,\n", "            orders3_4.loc[i - 1, \"latest_aov_order_above_500\"],\n", "            orders3_4.loc[i, \"aov\"],\n", "        )\n", "        orders3_4.loc[i, \"latest_qty_order_above_500\"] = np.where(\n", "            orders3_4.loc[i, \"orders\"] < 500,\n", "            orders3_4.loc[i - 1, \"latest_qty_order_above_500\"],\n", "            orders3_4.loc[i, \"ipc\"],\n", "        )\n", "\n", "        orders3_4.loc[i, \"latest_non_disruption_day_orders\"] = np.where(\n", "            (\n", "                (orders3_4.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_4.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_4.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_4.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_4.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_4.loc[i, \"dod_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_4.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_4.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_4.loc[i, \"wo4w_conv_change\"] < -2)\n", "            ),\n", "            orders3_4.loc[i - 1, \"latest_non_disruption_day_orders\"],\n", "            orders3_4.loc[i, \"orders\"],\n", "        )\n", "        orders3_4.loc[i, \"latest_non_disruption_day_aov\"] = np.where(\n", "            (\n", "                (orders3_4.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_4.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_4.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_4.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_4.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_4.loc[i, \"dod_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_4.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_4.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_4.loc[i, \"wo4w_conv_change\"] < -2)\n", "            ),\n", "            orders3_4.loc[i - 1, \"latest_non_disruption_day_aov\"],\n", "            orders3_4.loc[i, \"aov\"],\n", "        )\n", "        orders3_4.loc[i, \"latest_non_disruption_day_qty\"] = np.where(\n", "            (\n", "                (orders3_4.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_4.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_4.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_4.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_4.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_4.loc[i, \"dod_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_4.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_4.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_4.loc[i, \"wo4w_conv_change\"] < -2)\n", "            ),\n", "            orders3_4.loc[i - 1, \"latest_non_disruption_day_qty\"],\n", "            orders3_4.loc[i, \"ipc\"],\n", "        )\n", "\n", "\n", "orders3_5 = orders3[orders3[\"day\"] == \"Friday\"].reset_index()\n", "orders3_5[\"rank\"] = orders3_5.groupby([\"outlet_id\", \"is_upcoming_polygon\"])[\n", "    \"date_\"\n", "].rank(method=\"min\", ascending=True)\n", "for i in range(0, len(orders3_5)):\n", "    if orders3_5.loc[i, \"rank\"] == 1:\n", "        orders3_5.loc[i, \"latest_order_above_500\"] = orders3_5.loc[i, \"orders\"]\n", "        orders3_5.loc[i, \"latest_aov_order_above_500\"] = orders3_5.loc[i, \"aov\"]\n", "        orders3_5.loc[i, \"latest_qty_order_above_500\"] = orders3_5.loc[i, \"ipc\"]\n", "        orders3_5.loc[i, \"latest_non_disruption_day_orders\"] = orders3_5.loc[\n", "            i, \"orders\"\n", "        ]\n", "        orders3_5.loc[i, \"latest_non_disruption_day_aov\"] = orders3_5.loc[i, \"aov\"]\n", "        orders3_5.loc[i, \"latest_non_disruption_day_qty\"] = orders3_5.loc[i, \"ipc\"]\n", "\n", "    else:\n", "        orders3_5.loc[i, \"latest_order_above_500\"] = np.where(\n", "            orders3_5.loc[i, \"orders\"] < 500,\n", "            orders3_5.loc[i - 1, \"latest_order_above_500\"],\n", "            orders3_5.loc[i, \"orders\"],\n", "        )\n", "        orders3_5.loc[i, \"latest_aov_order_above_500\"] = np.where(\n", "            orders3_5.loc[i, \"orders\"] < 500,\n", "            orders3_5.loc[i - 1, \"latest_aov_order_above_500\"],\n", "            orders3_5.loc[i, \"aov\"],\n", "        )\n", "        orders3_5.loc[i, \"latest_qty_order_above_500\"] = np.where(\n", "            orders3_5.loc[i, \"orders\"] < 500,\n", "            orders3_5.loc[i - 1, \"latest_qty_order_above_500\"],\n", "            orders3_5.loc[i, \"ipc\"],\n", "        )\n", "\n", "        orders3_5.loc[i, \"latest_non_disruption_day_orders\"] = np.where(\n", "            (\n", "                (orders3_5.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_5.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_5.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_5.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_5.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_5.loc[i, \"dod_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_5.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_5.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_5.loc[i, \"wo4w_conv_change\"] < -2)\n", "            ),\n", "            orders3_5.loc[i - 1, \"latest_non_disruption_day_orders\"],\n", "            orders3_5.loc[i, \"orders\"],\n", "        )\n", "        orders3_5.loc[i, \"latest_non_disruption_day_aov\"] = np.where(\n", "            (\n", "                (orders3_5.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_5.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_5.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_5.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_5.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_5.loc[i, \"dod_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_5.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_5.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_5.loc[i, \"wo4w_conv_change\"] < -2)\n", "            ),\n", "            orders3_5.loc[i - 1, \"latest_non_disruption_day_aov\"],\n", "            orders3_5.loc[i, \"aov\"],\n", "        )\n", "        orders3_5.loc[i, \"latest_non_disruption_day_qty\"] = np.where(\n", "            (\n", "                (orders3_5.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_5.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_5.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_5.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_5.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_5.loc[i, \"dod_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_5.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_5.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_5.loc[i, \"wo4w_conv_change\"] < -2)\n", "            ),\n", "            orders3_5.loc[i - 1, \"latest_non_disruption_day_qty\"],\n", "            orders3_5.loc[i, \"ipc\"],\n", "        )\n", "\n", "\n", "orders3_6 = orders3[orders3[\"day\"] == \"Saturday\"].reset_index()\n", "orders3_6[\"rank\"] = orders3_6.groupby([\"outlet_id\", \"is_upcoming_polygon\"])[\n", "    \"date_\"\n", "].rank(method=\"min\", ascending=True)\n", "for i in range(0, len(orders3_6)):\n", "    if orders3_6.loc[i, \"rank\"] == 1:\n", "        orders3_6.loc[i, \"latest_order_above_500\"] = orders3_6.loc[i, \"orders\"]\n", "        orders3_6.loc[i, \"latest_aov_order_above_500\"] = orders3_6.loc[i, \"aov\"]\n", "        orders3_6.loc[i, \"latest_qty_order_above_500\"] = orders3_6.loc[i, \"ipc\"]\n", "        orders3_6.loc[i, \"latest_non_disruption_day_orders\"] = orders3_6.loc[\n", "            i, \"orders\"\n", "        ]\n", "        orders3_6.loc[i, \"latest_non_disruption_day_aov\"] = orders3_6.loc[i, \"aov\"]\n", "        orders3_6.loc[i, \"latest_non_disruption_day_qty\"] = orders3_6.loc[i, \"ipc\"]\n", "    else:\n", "        orders3_6.loc[i, \"latest_order_above_500\"] = np.where(\n", "            orders3_6.loc[i, \"orders\"] < 500,\n", "            orders3_6.loc[i - 1, \"latest_order_above_500\"],\n", "            orders3_6.loc[i, \"orders\"],\n", "        )\n", "        orders3_6.loc[i, \"latest_aov_order_above_500\"] = np.where(\n", "            orders3_6.loc[i, \"orders\"] < 500,\n", "            orders3_6.loc[i - 1, \"latest_aov_order_above_500\"],\n", "            orders3_6.loc[i, \"aov\"],\n", "        )\n", "        orders3_6.loc[i, \"latest_qty_order_above_500\"] = np.where(\n", "            orders3_6.loc[i, \"orders\"] < 500,\n", "            orders3_6.loc[i - 1, \"latest_qty_order_above_500\"],\n", "            orders3_6.loc[i, \"ipc\"],\n", "        )\n", "\n", "        orders3_6.loc[i, \"latest_non_disruption_day_orders\"] = np.where(\n", "            (\n", "                (orders3_6.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_6.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_6.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_6.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_6.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_6.loc[i, \"dod_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_6.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_6.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_6.loc[i, \"wo4w_conv_change\"] < -2)\n", "            ),\n", "            orders3_6.loc[i - 1, \"latest_non_disruption_day_orders\"],\n", "            orders3_6.loc[i, \"orders\"],\n", "        )\n", "        orders3_6.loc[i, \"latest_non_disruption_day_aov\"] = np.where(\n", "            (\n", "                (orders3_6.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_6.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_6.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_6.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_6.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_6.loc[i, \"dod_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_6.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_6.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_6.loc[i, \"wo4w_conv_change\"] < -2)\n", "            ),\n", "            orders3_6.loc[i - 1, \"latest_non_disruption_day_aov\"],\n", "            orders3_6.loc[i, \"aov\"],\n", "        )\n", "        orders3_6.loc[i, \"latest_non_disruption_day_qty\"] = np.where(\n", "            (\n", "                (orders3_6.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_6.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_6.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_6.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_6.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_6.loc[i, \"dod_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_6.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_6.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_6.loc[i, \"wo4w_conv_change\"] < -2)\n", "            ),\n", "            orders3_6.loc[i - 1, \"latest_non_disruption_day_qty\"],\n", "            orders3_6.loc[i, \"ipc\"],\n", "        )\n", "\n", "\n", "orders3_7 = orders3[orders3[\"day\"] == \"Sunday\"].reset_index()\n", "orders3_7[\"rank\"] = orders3_7.groupby([\"outlet_id\", \"is_upcoming_polygon\"])[\n", "    \"date_\"\n", "].rank(method=\"min\", ascending=True)\n", "for i in range(0, len(orders3_7)):\n", "    if orders3_7.loc[i, \"rank\"] == 1:\n", "        orders3_7.loc[i, \"latest_order_above_500\"] = orders3_7.loc[i, \"orders\"]\n", "        orders3_7.loc[i, \"latest_aov_order_above_500\"] = orders3_7.loc[i, \"aov\"]\n", "        orders3_7.loc[i, \"latest_qty_order_above_500\"] = orders3_7.loc[i, \"ipc\"]\n", "        orders3_7.loc[i, \"latest_non_disruption_day_orders\"] = orders3_7.loc[\n", "            i, \"orders\"\n", "        ]\n", "        orders3_7.loc[i, \"latest_non_disruption_day_aov\"] = orders3_7.loc[i, \"aov\"]\n", "        orders3_7.loc[i, \"latest_non_disruption_day_qty\"] = orders3_7.loc[i, \"ipc\"]\n", "\n", "    else:\n", "        orders3_7.loc[i, \"latest_order_above_500\"] = np.where(\n", "            orders3_7.loc[i, \"orders\"] < 500,\n", "            orders3_7.loc[i - 1, \"latest_order_above_500\"],\n", "            orders3_7.loc[i, \"orders\"],\n", "        )\n", "        orders3_7.loc[i, \"latest_aov_order_above_500\"] = np.where(\n", "            orders3_7.loc[i, \"orders\"] < 500,\n", "            orders3_7.loc[i - 1, \"latest_aov_order_above_500\"],\n", "            orders3_7.loc[i, \"aov\"],\n", "        )\n", "        orders3_7.loc[i, \"latest_qty_order_above_500\"] = np.where(\n", "            orders3_7.loc[i, \"orders\"] < 500,\n", "            orders3_7.loc[i - 1, \"latest_qty_order_above_500\"],\n", "            orders3_7.loc[i, \"ipc\"],\n", "        )\n", "\n", "        orders3_7.loc[i, \"latest_non_disruption_day_orders\"] = np.where(\n", "            (\n", "                (orders3_7.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_7.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_7.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_7.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_7.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_7.loc[i, \"dod_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_7.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_7.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_7.loc[i, \"wo4w_conv_change\"] < -2)\n", "            ),\n", "            orders3_7.loc[i - 1, \"latest_non_disruption_day_orders\"],\n", "            orders3_7.loc[i, \"orders\"],\n", "        )\n", "        orders3_7.loc[i, \"latest_non_disruption_day_aov\"] = np.where(\n", "            (\n", "                (orders3_7.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_7.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_7.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_7.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_7.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_7.loc[i, \"dod_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_7.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_7.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_7.loc[i, \"wo4w_conv_change\"] < -2)\n", "            ),\n", "            orders3_7.loc[i - 1, \"latest_non_disruption_day_aov\"],\n", "            orders3_7.loc[i, \"aov\"],\n", "        )\n", "        orders3_7.loc[i, \"latest_non_disruption_day_qty\"] = np.where(\n", "            (\n", "                (orders3_7.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_7.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_7.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_7.loc[i, \"wo4w_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_7.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_7.loc[i, \"dod_conv_change\"] < -2)\n", "            )\n", "            | (\n", "                (orders3_7.loc[i, \"dod_conv_change\"] < -2)\n", "                & (orders3_7.loc[i, \"wow_conv_change\"] < -2)\n", "                & (orders3_7.loc[i, \"wo4w_conv_change\"] < -2)\n", "            ),\n", "            orders3_7.loc[i - 1, \"latest_non_disruption_day_qty\"],\n", "            orders3_7.loc[i, \"ipc\"],\n", "        )"]}, {"cell_type": "code", "execution_count": null, "id": "2edb21cb-480c-4eba-83f2-cfa21ed3ad4e", "metadata": {}, "outputs": [], "source": ["orders3 = (\n", "    orders3_1.append(orders3_2)\n", "    .append(orders3_3)\n", "    .append(orders3_4)\n", "    .append(orders3_5)\n", "    .append(orders3_6)\n", "    .append(orders3_7)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "22333eb1-b952-4c1d-908f-cf924e3b9142", "metadata": {}, "outputs": [], "source": ["low_order_dates = (\n", "    orders3[orders3.orders < 500]\n", "    .groupby([\"outlet_id\"])[\"date_\"]\n", "    .nunique()\n", "    .reset_index()\n", ")\n", "low_order_dates.rename(columns={\"date_\": \"low_order_dates\"}, inplace=True)\n", "tot_dates = orders3.groupby([\"outlet_id\"])[\"date_\"].nunique().reset_index()\n", "tot_dates.rename(columns={\"date_\": \"all_dates\"}, inplace=True)\n", "\n", "low_order_dates = low_order_dates.merge(tot_dates, how=\"right\", on=[\"outlet_id\"])\n", "low_order_dates[\"low_order_dates\"].fillna(0, inplace=True)\n", "\n", "low_order_dates[\"perc_dates\"] = (\n", "    low_order_dates[\"low_order_dates\"] * 100.0 / low_order_dates[\"all_dates\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "964a0c93-1b1e-41eb-85c3-7e04c8eb44b3", "metadata": {}, "outputs": [], "source": ["low_order_dates[\n", "    (low_order_dates[\"perc_dates\"] >= 90) & (low_order_dates[\"all_dates\"] > 60)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "6e01c429-d526-4ec0-b9b5-0646249d9dc8", "metadata": {}, "outputs": [], "source": ["small_store_list = list(\n", "    low_order_dates[\n", "        (low_order_dates[\"perc_dates\"] >= 90) & (low_order_dates[\"all_dates\"] > 60)\n", "    ][\"outlet_id\"].unique()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f9e5e2ac-fcfa-4293-b5a5-f6e504337aee", "metadata": {}, "outputs": [], "source": ["orders3[\"orders\"] = np.where(\n", "    orders3.outlet_id.isin(small_store_list),\n", "    orders3[\"latest_non_disruption_day_orders\"],\n", "    np.where(\n", "        orders3[\"orders\"] < 500,\n", "        orders3[\"latest_order_above_500\"],\n", "        orders3[\"latest_non_disruption_day_orders\"],\n", "    ),\n", ")\n", "orders3[\"aov\"] = np.where(\n", "    (orders3[\"orders\"] < 500) & (~orders3.outlet_id.isin(small_store_list)),\n", "    orders3[\"latest_aov_order_above_500\"],\n", "    orders3[\"latest_non_disruption_day_aov\"],\n", ")\n", "orders3[\"ipc\"] = np.where(\n", "    (orders3[\"orders\"] < 500) & (~orders3.outlet_id.isin(small_store_list)),\n", "    orders3[\"latest_qty_order_above_500\"],\n", "    orders3[\"latest_non_disruption_day_qty\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6194b69f-57ea-440f-a9cf-62013212711b", "metadata": {}, "outputs": [], "source": ["orders3.shape, orders3[(orders3.orders > 500)].shape"]}, {"cell_type": "code", "execution_count": null, "id": "6846d981-4188-4391-addd-84d2e2e0e7c2", "metadata": {}, "outputs": [], "source": ["orders3 = orders3[(orders3.orders > 500) | (orders3.outlet_id.isin(small_store_list))]"]}, {"cell_type": "code", "execution_count": null, "id": "9794474f-7280-489f-88df-a315f7ed2d83", "metadata": {}, "outputs": [], "source": ["orders3.shape"]}, {"cell_type": "code", "execution_count": null, "id": "15e6407f-6335-47e1-bb8b-c10244f09f6c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c27746e4-87ee-4ac1-81a6-6f65f4656a85", "metadata": {}, "outputs": [], "source": ["orders3.drop(columns=[\"index\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "b83de0ee-213e-4234-9868-ee7399f4f81b", "metadata": {}, "outputs": [], "source": ["orders3 = orders3.sort_values(\n", "    by=[\"city_name\", \"outlet_id\", \"is_upcoming_polygon\", \"date_\"]\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "ccdda84c-d256-4d9d-a1a2-d8304331144a", "metadata": {}, "outputs": [], "source": ["orders3.drop(columns=[\"index\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "775b56d4-3786-4487-96e7-26c4596ead63", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d325adf7-847a-47c0-925b-3377e5f68158", "metadata": {}, "outputs": [], "source": ["orders3 = orders3[orders3[\"outlet_name\"].str.find(\"Test\") == -1]"]}, {"cell_type": "code", "execution_count": null, "id": "0d077664-2436-4ef1-9973-0efcac718e46", "metadata": {}, "outputs": [], "source": ["orders3[\"aov\"] = orders3[\"aov\"].astype(float)\n", "orders3[\"ipc\"] = orders3[\"ipc\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "fb9c601d-3bc1-4063-af7f-d78f8afd47df", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9e334bbd-c865-4b9a-b766-68bddcf7d90f", "metadata": {}, "outputs": [], "source": ["orders3[\"dod_change\"] = orders3.groupby([\"city_name\", \"merchant_id\"])[\n", "    \"orders\"\n", "].pct_change(1)\n", "orders3[\"wow_change\"] = orders3.groupby([\"city_name\", \"merchant_id\"])[\n", "    \"orders\"\n", "].pct_change(7)\n", "\n", "orders3[\"aov_dod_change\"] = orders3.groupby([\"city_name\", \"merchant_id\"])[\n", "    \"aov\"\n", "].pct_change(1)\n", "orders3[\"aov_wow_change\"] = orders3.groupby([\"city_name\", \"merchant_id\"])[\n", "    \"aov\"\n", "].pct_change(7)\n", "\n", "orders3[\"ipc_dod_change\"] = orders3.groupby([\"city_name\", \"merchant_id\"])[\n", "    \"ipc\"\n", "].pct_change(1)\n", "orders3[\"ipc_wow_change\"] = orders3.groupby([\"city_name\", \"merchant_id\"])[\n", "    \"ipc\"\n", "].pct_change(7)"]}, {"cell_type": "code", "execution_count": null, "id": "6c259c2f-dfb5-47fb-9cfc-b496a488ec09", "metadata": {}, "outputs": [], "source": ["orders3[\"dod_change\"].fillna(0, inplace=True)\n", "orders3[\"wow_change\"].fillna(0, inplace=True)\n", "orders3[\"aov_dod_change\"].fillna(0, inplace=True)\n", "orders3[\"aov_wow_change\"].fillna(0, inplace=True)\n", "orders3[\"ipc_dod_change\"].fillna(0, inplace=True)\n", "orders3[\"ipc_wow_change\"].fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "6fc62665-ea60-4b55-a1fa-efe10e6db2cb", "metadata": {}, "outputs": [], "source": ["orders3[\"dt\"] = pd.to_datetime(orders3[\"date_\"]).dt.strftime(\"%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "8727a4a3-3f06-4a62-868c-0e26b237acc8", "metadata": {}, "outputs": [], "source": ["orders3[\"date_\"] = orders3[\"date_\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "79177308-4a2e-45b4-8353-ba460e63db1a", "metadata": {}, "outputs": [], "source": ["orders3 = orders3.merge(\n", "    hfs_days[hfs_days[\"hfs_days_passed\"] == 1][[\"date_\", \"hfs_days_passed\"]],\n", "    how=\"left\",\n", "    on=[\"date_\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1b29ad7e-de57-4a2d-8685-ecefdddc2480", "metadata": {}, "outputs": [], "source": ["orders3.rename(columns={\"hfs_days_passed\": \"hfs_day1\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "9d9eefa0-ebc8-4e9f-8f97-4352be1ef640", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d5047ab3-ddb9-45c1-b23c-4fbeafbfed22", "metadata": {}, "outputs": [], "source": ["hfs_date_df = (\n", "    orders3[orders3[\"date_\"] <= train_end_date]\n", "    .groupby(\n", "        [\"city_name\", \"outlet_id\", \"outlet_name\", \"dt\", \"day\", \"hfs_tag\", \"hfs_day1\"]\n", "    )\n", "    .agg({\"dod_change\": \"mean\", \"aov_dod_change\": \"mean\", \"ipc_dod_change\": \"mean\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b72006d8-09d5-4507-8f8c-ad303c93a625", "metadata": {}, "outputs": [], "source": ["hfs_date_df.groupby([\"dt\", \"day\", \"hfs_tag\"]).agg({\"dod_change\": \"mean\"}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "4b5476d1-bc58-4ab6-80ee-e1b221e3c7d5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "934b9eaf-96d1-435c-bfa7-61dba3002650", "metadata": {}, "outputs": [], "source": ["dod_avg_1 = (\n", "    orders3[(orders3[\"date_\"] <= train_end_date)]\n", "    .groupby(\n", "        [\n", "            \"city_name\",\n", "            \"outlet_id\",\n", "            \"outlet_name\",\n", "            \"dt\",\n", "            \"day\",\n", "            \"hfs_tag\",\n", "            \"hfs_day1\",\n", "            \"is_upcoming_polygon\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"dod_change\": \"mean\",\n", "            \"aov_dod_change\": \"mean\",\n", "            \"ipc_dod_change\": \"mean\",\n", "            \"date_\": \"count\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3814c948-432a-4db9-8aed-38978d6f5bed", "metadata": {}, "outputs": [], "source": ["dod_avg_2 = (\n", "    orders3[(orders3[\"date_\"] <= train_end_date)]\n", "    .groupby(\n", "        [\n", "            \"city_name\",\n", "            \"outlet_id\",\n", "            \"outlet_name\",\n", "            \"dt\",\n", "            \"day\",\n", "            \"hfs_tag\",\n", "            \"is_upcoming_polygon\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"dod_change\": \"mean\",\n", "            \"aov_dod_change\": \"mean\",\n", "            \"ipc_dod_change\": \"mean\",\n", "            \"date_\": \"count\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4c595b31-6ccc-44af-a474-379a80384f03", "metadata": {}, "outputs": [], "source": ["dod_avg_3 = (\n", "    orders3[(orders3[\"date_\"] <= train_end_date)]\n", "    .groupby(\n", "        [\n", "            \"city_name\",\n", "            \"outlet_id\",\n", "            \"outlet_name\",\n", "            \"day\",\n", "            \"hfs_tag\",\n", "            \"is_upcoming_polygon\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"dod_change\": \"mean\",\n", "            \"aov_dod_change\": \"mean\",\n", "            \"ipc_dod_change\": \"mean\",\n", "            \"date_\": \"count\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f4199776-8859-43d6-82c9-aaaf89883984", "metadata": {}, "outputs": [], "source": ["dod_avg_4 = (\n", "    orders3[(orders3[\"date_\"] <= train_end_date)]\n", "    .groupby([\"city_name\", \"outlet_id\", \"outlet_name\", \"day\", \"is_upcoming_polygon\"])\n", "    .agg(\n", "        {\n", "            \"dod_change\": \"mean\",\n", "            \"aov_dod_change\": \"mean\",\n", "            \"ipc_dod_change\": \"mean\",\n", "            \"date_\": \"count\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "bf6461e6-c9f8-4498-94a1-c94c8c012552", "metadata": {}, "outputs": [], "source": ["dod_avg_5 = (\n", "    orders3[(orders3[\"date_\"] <= train_end_date)]\n", "    .groupby([\"city_name\", \"day\", \"is_upcoming_polygon\"])\n", "    .agg(\n", "        {\n", "            \"dod_change\": \"mean\",\n", "            \"aov_dod_change\": \"mean\",\n", "            \"ipc_dod_change\": \"mean\",\n", "            \"date_\": \"count\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "081f68dd-bc10-4032-8c42-4a2adadb70a0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9f980076-f059-442c-befd-34cd6db2ff7f", "metadata": {}, "outputs": [], "source": ["dod_avg_1[\"dod_change\"] = dod_avg_1[\"dod_change\"] + 1\n", "dod_avg_1[\"aov_dod_change\"] = dod_avg_1[\"aov_dod_change\"].fillna(0) + 1\n", "dod_avg_1[\"ipc_dod_change\"] = dod_avg_1[\"ipc_dod_change\"].fillna(0) + 1\n", "\n", "dod_avg_2[\"dod_change\"] = dod_avg_2[\"dod_change\"] + 1\n", "dod_avg_2[\"aov_dod_change\"] = dod_avg_2[\"aov_dod_change\"].fillna(0) + 1\n", "dod_avg_2[\"ipc_dod_change\"] = dod_avg_2[\"ipc_dod_change\"].fillna(0) + 1\n", "\n", "\n", "dod_avg_3[\"dod_change\"] = dod_avg_3[\"dod_change\"] + 1\n", "dod_avg_3[\"aov_dod_change\"] = dod_avg_3[\"aov_dod_change\"].fillna(0) + 1\n", "dod_avg_3[\"ipc_dod_change\"] = dod_avg_3[\"ipc_dod_change\"].fillna(0) + 1\n", "\n", "\n", "dod_avg_4[\"dod_change\"] = dod_avg_4[\"dod_change\"] + 1\n", "dod_avg_4[\"aov_dod_change\"] = dod_avg_4[\"aov_dod_change\"].fillna(0) + 1\n", "dod_avg_4[\"ipc_dod_change\"] = dod_avg_4[\"ipc_dod_change\"].fillna(0) + 1\n", "\n", "\n", "dod_avg_5[\"dod_change\"] = dod_avg_5[\"dod_change\"] + 1\n", "dod_avg_5[\"aov_dod_change\"] = dod_avg_5[\"aov_dod_change\"].fillna(0) + 1\n", "dod_avg_5[\"ipc_dod_change\"] = dod_avg_5[\"ipc_dod_change\"].fillna(0) + 1"]}, {"cell_type": "code", "execution_count": null, "id": "8fe7938a-d0e9-4059-a423-2f54026916c8", "metadata": {}, "outputs": [], "source": ["dod_avg_2.groupby([\"day\", \"hfs_tag\"])[\"dod_change\"].mean()"]}, {"cell_type": "code", "execution_count": null, "id": "5f5edfc5-e30b-4918-9d50-d801dd886798", "metadata": {}, "outputs": [], "source": ["orders3[(orders3.day == \"Monday\") & (orders3.hfs_tag == 0)].groupby([\"date_\"])[\n", "    \"dod_change\"\n", "].mean().reset_index().sort_values(by=[\"dod_change\"]).head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "a0d06d8a-39bd-4520-a125-478f5bdeba9f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a71941b8-43ee-4acf-abad-86f64ef6bca9", "metadata": {}, "outputs": [], "source": ["dod_avg_2[(dod_avg_2.outlet_id == 4547) & (dod_avg_2.day == \"Monday\")].head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "92975314-8ee1-4a55-a0fe-60eb55274ac1", "metadata": {}, "outputs": [], "source": ["orders3[(orders3.outlet_id == 4547) & (orders3.day == \"Saturday\")][\n", "    [\n", "        \"city_name\",\n", "        \"merchant_id\",\n", "        \"merchant_name\",\n", "        \"date_\",\n", "        \"orders\",\n", "        \"store_live_date\",\n", "        \"hfs_tag\",\n", "        \"event_name\",\n", "        \"is_major_disruption\",\n", "        \"day\",\n", "        \"dod_change\",\n", "        \"wow_change\",\n", "        \"wo4w_change\",\n", "        \"dod_conv_change\",\n", "        \"wow_conv_change\",\n", "        \"wo4w_conv_change\",\n", "        \"aov_dod_change\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "679eb9ee-aa8b-4676-950e-7b8e60b268c1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4b921ad2-8663-4d9d-b81e-bb316cfd2dcf", "metadata": {}, "outputs": [], "source": ["orders3[\n", "    (orders3[\"outlet_id\"] == 3814)\n", "    # & (orders3.day==\"Tuesday\")\n", "    & (orders3[\"date_\"] >= \"2023-02-27\")\n", "][\n", "    [\n", "        \"city_name\",\n", "        \"merchant_id\",\n", "        \"date_\",\n", "        \"day\",\n", "        \"hfs_tag\",\n", "        \"orders\",\n", "        \"store_live_date\",\n", "        # 'aov',\n", "        \"event_name\",\n", "        \"aov\",\n", "        # 'aov_dod_change',\n", "        \"dod_change\",\n", "        \"wow_change\",\n", "        \"wo4w_change\",\n", "        \"dod_conv_change\",\n", "        \"wow_conv_change\",\n", "        \"wo4w_conv_change\",\n", "        # 'rank',\n", "        \"latest_order_above_500\",\n", "        \"latest_non_disruption_day_orders\",\n", "    ]\n", "].head(\n", "    10\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "88391b8e-794e-4efe-b979-700858817d55", "metadata": {}, "outputs": [], "source": ["# orders3[(orders3[\"date_\"] <= \"2023-12-15\") & (orders3[\"outlet_id\"] == 3671) & (orders3[\"date_\"] >= \"2023-12-07\")][['city_name', 'merchant_id', 'date_', 'day', 'hfs_tag','orders', 'conversion',\n", "#        'store_live_date',\n", "#         # 'aov',\n", "#        'event_name', 'dod_change', 'wow_change', 'wo4w_change',\n", "#         'dod_conv_change', 'wow_conv_change', 'wo4w_conv_change',\n", "#        'rank', 'latest_order_above_500',\n", "#        'latest_non_disruption_day_orders',\n", "# ]]"]}, {"cell_type": "code", "execution_count": null, "id": "82bf4d6a-4553-4a98-a9f3-ec84ceb02415", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(dod_avg_2[dod_avg_2.date_>1], \"1lepKjqGE5dKrp5D_0orbl4BFVv1RkaA2SZKDouDSy3k\", \"orders_raw\")"]}, {"cell_type": "code", "execution_count": null, "id": "5f2a9699-d2f4-4e60-8b70-f3fe97114ea7", "metadata": {}, "outputs": [], "source": ["# orders1[orders1.outlet_id == 4319].drop_duplicates().tail(60)"]}, {"cell_type": "code", "execution_count": null, "id": "25ff17ee-7ca0-4d81-b7c7-fe5d5f140c8b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cab55632-dea7-4db9-b9ff-cf3b7daa42f5", "metadata": {}, "outputs": [], "source": ["train_end_date_day = (pd.to_datetime(train_end_date)).strftime(\"%A\")\n", "train_end_date_day"]}, {"cell_type": "code", "execution_count": null, "id": "fa4372bd-56ab-4de8-aa15-2e866fb389e4", "metadata": {}, "outputs": [], "source": ["orders3 = orders3.merge(\n", "    polygon_change_input[[\"merchant_id\", \"date_live_from\"]],\n", "    how=\"left\",\n", "    on=[\"merchant_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "aca99092-056c-4c71-8ca3-bf52256cd13c", "metadata": {}, "outputs": [], "source": ["recent_orders = orders3[\n", "    (\n", "        (orders3.is_upcoming_polygon == 0)\n", "        & (\n", "            orders3.date_\n", "            >= (pd.to_datetime(train_end_date) - timed<PERSON>ta(days=40)).strftime(\n", "                \"%Y-%m-%d\"\n", "            )\n", "        )\n", "        & (orders3.date_ <= train_end_date)\n", "    )\n", "    | (\n", "        (orders3.is_upcoming_polygon == 1)\n", "        & (orders3.date_ <= pd.to_datetime(orders3.date_live_from))\n", "    )\n", "].reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "07670f82-d811-440e-8b18-445d737c77e8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8889c3ff-a2b5-43d1-9a16-8f0f37b39a83", "metadata": {}, "outputs": [], "source": ["recent_orders[\"rank_p0\"] = (\n", "    recent_orders[recent_orders.day == train_end_date_day]\n", "    .groupby([\"outlet_id\", \"is_upcoming_polygon\"])[\"date_\"]\n", "    .rank(method=\"min\", ascending=False)\n", ")\n", "recent_orders[\"rank_p1\"] = recent_orders.groupby([\"outlet_id\", \"is_upcoming_polygon\"])[\n", "    \"date_\"\n", "].rank(method=\"min\", ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "622fc14a-9804-476b-90d9-3e20e0e38c08", "metadata": {}, "outputs": [], "source": ["recent_orders[\"check_priority\"] = np.where(~recent_orders[\"rank_p0\"].isna(), 0, 1)"]}, {"cell_type": "code", "execution_count": null, "id": "85c46b51-16ae-4535-9807-bbc1357ebd46", "metadata": {}, "outputs": [], "source": ["recent_orders.merchant_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "b113ddd4-271b-483b-9c94-79b10d3d0d63", "metadata": {}, "outputs": [], "source": ["recent_orders1 = recent_orders[recent_orders[\"rank_p0\"] == 1].append(\n", "    recent_orders[(recent_orders[\"rank_p0\"].isna()) & (recent_orders[\"rank_p1\"] == 1)]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "74d3dd65-4b61-41a1-97c6-c4a6a2be467a", "metadata": {}, "outputs": [], "source": ["recent_orders1.drop(columns=[\"index\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "ee7ac0c2-0636-47b2-8e14-ce049a240f10", "metadata": {}, "outputs": [], "source": ["# recent_orders = recent_orders[(recent_orders.date_ >= (pd.to_datetime(train_end_date) - timedelta(days=30)).strftime(\"%Y-%m-%d\"))\n", "#       & (recent_orders.date_ <= train_end_date)].reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "38133031-3626-4d43-82b2-ab21b8414149", "metadata": {}, "outputs": [], "source": ["# recent_orders[\"latest_date_rank\"] = recent_orders[recent_orders.event_name=='-'].groupby([\"outlet_id\"])[\"date_\"].rank(method='min', ascending=False)\n", "\n", "recent_orders1[\"latest_date_rank\"] = recent_orders1.groupby([\"outlet_id\"])[\n", "    \"check_priority\"\n", "].rank(method=\"min\")"]}, {"cell_type": "code", "execution_count": null, "id": "a343830f-b247-4621-ba5a-972339a6cca7", "metadata": {}, "outputs": [], "source": ["latest_non_festive_day_orders = recent_orders1[\n", "    recent_orders1[\"latest_date_rank\"] == 1\n", "].reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "27221b6a-d154-4358-bce3-a7bbd040d519", "metadata": {}, "outputs": [], "source": ["latest_non_festive_day_orders.groupby([\"date_\"])[\"outlet_id\"].count()"]}, {"cell_type": "code", "execution_count": null, "id": "b80236ba-1816-4c59-bc40-11ddf75e0f65", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "45f9c955-44e8-431a-80cc-12d3dda4fe42", "metadata": {}, "outputs": [], "source": ["# latest_non_festive_day_orders[\"diff\"] = (pd.to_datetime(latest_non_festive_day_orders[\"date_\"]) - pd.to_datetime(train_end_date)).dt.days//7"]}, {"cell_type": "code", "execution_count": null, "id": "3449c805-2dde-4044-b759-ec6f433da518", "metadata": {}, "outputs": [], "source": ["# latest_non_festive_day_orders"]}, {"cell_type": "code", "execution_count": null, "id": "10328c7f-b122-4fe9-bd1a-ed978053902e", "metadata": {}, "outputs": [], "source": ["# inc_factor = latest_non_festive_day_orders[latest_non_festive_day_orders[\"day\"]==train_end_date_day][\"wow_change\"].mean() + 1\n", "# inc_factor"]}, {"cell_type": "code", "execution_count": null, "id": "6bf71cd2-ba83-494d-a65b-a2b43a0a76af", "metadata": {}, "outputs": [], "source": ["latest_non_festive_day_orders[\"inc_orders\"] = np.round(\n", "    latest_non_festive_day_orders[\"orders\"], 0\n", ").astype(int)\n", "# *inc_factor*diff"]}, {"cell_type": "code", "execution_count": null, "id": "cda15f19-9fba-4337-97d6-50552b4a2351", "metadata": {}, "outputs": [], "source": ["latest_non_festive_day_orders[\"orders\"].sum(), latest_non_festive_day_orders[\n", "    \"inc_orders\"\n", "].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "5f0f73a4-df57-43fa-aa7e-1de86974358c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2f4c3e79-d32e-4929-b2b3-0ae563b73766", "metadata": {}, "outputs": [], "source": ["date_range = pd.date_range(\n", "    start=pd.to_datetime(train_end_date) + timedelta(days=1),\n", "    end=pd.to_datetime(ov_end_date),\n", "    freq=\"D\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5a6539fb-41f6-4a05-8e34-56e322633e3e", "metadata": {}, "outputs": [], "source": ["base_df = pd.DataFrame(date_range, columns=[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "58c82df0-de23-43b3-b2e7-fcd39af106a5", "metadata": {}, "outputs": [], "source": ["base_df[\"day\"] = base_df[\"date_\"].dt.strftime(\"%A\")"]}, {"cell_type": "code", "execution_count": null, "id": "d3cc4d1b-7f64-4021-b218-66ea50ced8b5", "metadata": {}, "outputs": [], "source": ["base_df[\"wom\"] = pd.to_datetime(base_df[\"date_\"]).dt.day // 7 + 1"]}, {"cell_type": "code", "execution_count": null, "id": "89ab72f4-bccc-4da5-87fe-88035b705c89", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "72d5f12c-98ae-412d-af3d-70f30aa51c27", "metadata": {}, "outputs": [], "source": ["hfs_input = pb.from_sheets(\"1lepKjqGE5dKrp5D_0orbl4BFVv1RkaA2SZKDouDSy3k\", \"HFS Input\")\n", "\n", "# hfs_input = pd.read_csv('HFS Input.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "e279cf40-3d85-4a0d-9d43-c1f2e6006657", "metadata": {}, "outputs": [], "source": ["hfs_input"]}, {"cell_type": "code", "execution_count": null, "id": "5d1d4e84-7a4b-4a82-8ece-fe111f1f50da", "metadata": {}, "outputs": [], "source": ["hfs_df = pd.DataFrame([])"]}, {"cell_type": "code", "execution_count": null, "id": "d5122a7f-4a78-494a-b27d-2a095a5a8ab5", "metadata": {}, "outputs": [], "source": ["for i in range(0, len(hfs_input)):\n", "\n", "    dr = pd.date_range(\n", "        start=hfs_input.loc[i, \"start_date\"], end=hfs_input.loc[i, \"end_date\"], freq=\"D\"\n", "    )\n", "    # print(dr)\n", "\n", "    hfs_df = hfs_df.append(pd.DataFrame(dr, columns=[\"date_\"]))"]}, {"cell_type": "code", "execution_count": null, "id": "7e042796-5173-4ea7-a7e3-c1c1bf77c7f6", "metadata": {}, "outputs": [], "source": ["hfs_df[\"hfs_tag\"] = 1"]}, {"cell_type": "code", "execution_count": null, "id": "8bd0c930-5937-4947-a0be-195520641b4d", "metadata": {}, "outputs": [], "source": ["hfs_df2 = hfs_input[[\"start_date\"]]\n", "hfs_df2[\"start_date\"] = pd.to_datetime(hfs_df2[\"start_date\"])\n", "hfs_df2.rename(columns={\"start_date\": \"date_\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "acddbd0d-b2c7-47ec-8510-941f72ba3def", "metadata": {}, "outputs": [], "source": ["hfs_df2[\"hfs_day1\"] = 1"]}, {"cell_type": "code", "execution_count": null, "id": "49b25630-c482-4080-81f2-83d98d58a238", "metadata": {}, "outputs": [], "source": ["hfs_df = hfs_df.merge(hfs_df2, how=\"left\", on=[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "961261bf-69d7-45a9-bdfd-223fe8660ec1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e868232c-8f09-4417-b640-e9beee0a9823", "metadata": {}, "outputs": [], "source": ["base_df = base_df.merge(hfs_df, on=[\"date_\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "25a04889-7b85-4406-98c1-85865333e17f", "metadata": {}, "outputs": [], "source": ["base_df[\"hfs_tag\"].fillna(0, inplace=True)\n", "base_df[\"hfs_day1\"].fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "459ed351-5d3d-4668-84b8-199895b42ab5", "metadata": {}, "outputs": [], "source": ["base_df"]}, {"cell_type": "code", "execution_count": null, "id": "9110aad6-e241-4d7c-8313-9fbdfb77aaa6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2240269b-4114-448d-b49b-a6e5c2a958e6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d93d0381-4c01-4527-84d5-dc66e8f45eaf", "metadata": {}, "outputs": [], "source": ["base_df1 = pd.merge(\n", "    orders3.merge(\n", "        active_store.append(\n", "            polygon_change_input[polygon_change_input.store_type != \"new\"][\n", "                [\"merchant_id\"]\n", "            ]\n", "        ).drop_duplicates(),\n", "        on=[\"merchant_id\"],\n", "        how=\"inner\",\n", "    )[\n", "        [\n", "            \"city_name\",\n", "            \"outlet_id\",\n", "            \"outlet_name\",\n", "            \"store_live_date\",\n", "            \"is_upcoming_polygon\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(),\n", "    base_df,\n", "    how=\"cross\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fec50d88-38e8-487d-aca5-fc758f345ff9", "metadata": {}, "outputs": [], "source": ["base_df1.drop(columns=[\"index\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "091b0116-50b0-4fe9-bea0-dd7a9e4d6e3c", "metadata": {}, "outputs": [], "source": ["base_df1[\"store_type\"] = np.where(\n", "    (\n", "        (\n", "            pd.to_datetime(base_df1[\"date_\"])\n", "            - pd.to_datetime(base_df1[\"store_live_date\"])\n", "        ).dt.days\n", "        < 60\n", "    )\n", "    | (base_df1[\"store_live_date\"] == \"1900-01-01\"),\n", "    \"new\",\n", "    \"old\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8032b5c4-9e46-49d8-826b-d7c037fcb52f", "metadata": {}, "outputs": [], "source": ["base_df1[\"dt\"] = base_df1[\"date_\"].dt.strftime(\"%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "e7ee7d42-d7d1-4974-a5f4-db43f68f352d", "metadata": {}, "outputs": [], "source": ["existing_store_df = base_df1[base_df1[\"store_type\"] == \"old\"]"]}, {"cell_type": "code", "execution_count": null, "id": "38af330d-68c9-4c7b-80a8-2d38fc407e1c", "metadata": {}, "outputs": [], "source": ["new_store_df = base_df1[base_df1[\"store_type\"] == \"new\"]"]}, {"cell_type": "code", "execution_count": null, "id": "73f223f8-6660-4ef3-bd9a-1dfb6ef82d4c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e643f299-eb2e-4221-bfb7-62effd54769e", "metadata": {}, "outputs": [], "source": ["existing_store_df1 = existing_store_df.merge(\n", "    dod_avg_1[dod_avg_1[\"date_\"] > 1][\n", "        [\n", "            \"city_name\",\n", "            \"outlet_id\",\n", "            \"outlet_name\",\n", "            \"dt\",\n", "            \"day\",\n", "            \"hfs_tag\",\n", "            \"hfs_day1\",\n", "            \"is_upcoming_polygon\",\n", "            \"dod_change\",\n", "            \"aov_dod_change\",\n", "            \"ipc_dod_change\",\n", "        ]\n", "    ].drop_duplicates(),\n", "    how=\"left\",\n", "    on=[\n", "        \"city_name\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"dt\",\n", "        \"day\",\n", "        \"hfs_tag\",\n", "        \"hfs_day1\",\n", "        \"is_upcoming_polygon\",\n", "    ],\n", ")\n", "existing_store_df1.rename(\n", "    columns={\n", "        \"dod_change\": \"dod_change_1\",\n", "        \"aov_dod_change\": \"aov_dod_change_1\",\n", "        \"ipc_dod_change\": \"ipc_dod_change_1\",\n", "    },\n", "    inplace=True,\n", ")\n", "\n", "# existing_store_df1 = existing_store_df.merge(dod_avg_3.groupby([\"city_name\", \"day\", \"hfs_tag\"]).agg({\"dod_change\": \"mean\", \"aov_change\": \"mean\"}).reset_index(), how=\"left\", on=[\"city_name\", \"day\", \"hfs_tag\"])"]}, {"cell_type": "code", "execution_count": null, "id": "a0e64c76-36fa-4c9a-baca-b8a8de31e9e4", "metadata": {}, "outputs": [], "source": ["existing_store_df1 = existing_store_df1.merge(\n", "    dod_avg_2[dod_avg_2[\"date_\"] > 1][\n", "        [\n", "            \"city_name\",\n", "            \"outlet_id\",\n", "            \"outlet_name\",\n", "            \"dt\",\n", "            \"day\",\n", "            \"hfs_tag\",\n", "            \"is_upcoming_polygon\",\n", "            \"dod_change\",\n", "            \"aov_dod_change\",\n", "            \"ipc_dod_change\",\n", "        ]\n", "    ].drop_duplicates(),\n", "    how=\"left\",\n", "    on=[\n", "        \"city_name\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"dt\",\n", "        \"day\",\n", "        \"hfs_tag\",\n", "        \"is_upcoming_polygon\",\n", "    ],\n", ")\n", "existing_store_df1.rename(\n", "    columns={\n", "        \"dod_change\": \"dod_change_2\",\n", "        \"aov_dod_change\": \"aov_dod_change_2\",\n", "        \"ipc_dod_change\": \"ipc_dod_change_2\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d3230b8a-b34f-42df-9534-7916f0fd6817", "metadata": {}, "outputs": [], "source": ["existing_store_df1 = existing_store_df1.merge(\n", "    dod_avg_3[dod_avg_3[\"date_\"] > 1][\n", "        [\n", "            \"city_name\",\n", "            \"outlet_id\",\n", "            \"outlet_name\",\n", "            \"day\",\n", "            \"hfs_tag\",\n", "            \"is_upcoming_polygon\",\n", "            \"dod_change\",\n", "            \"aov_dod_change\",\n", "            \"ipc_dod_change\",\n", "        ]\n", "    ].drop_duplicates(),\n", "    how=\"left\",\n", "    on=[\n", "        \"city_name\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"day\",\n", "        \"hfs_tag\",\n", "        \"is_upcoming_polygon\",\n", "    ],\n", ")\n", "existing_store_df1.rename(\n", "    columns={\n", "        \"dod_change\": \"dod_change_3\",\n", "        \"aov_dod_change\": \"aov_dod_change_3\",\n", "        \"ipc_dod_change\": \"ipc_dod_change_3\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "78cbb1df-74a9-4a3c-89cc-d202cdf136b0", "metadata": {}, "outputs": [], "source": ["existing_store_df1 = existing_store_df1.merge(\n", "    dod_avg_4[dod_avg_4[\"date_\"] > 1][\n", "        [\n", "            \"city_name\",\n", "            \"outlet_id\",\n", "            \"outlet_name\",\n", "            \"day\",\n", "            \"is_upcoming_polygon\",\n", "            \"dod_change\",\n", "            \"aov_dod_change\",\n", "            \"ipc_dod_change\",\n", "        ]\n", "    ].drop_duplicates(),\n", "    how=\"left\",\n", "    on=[\"city_name\", \"outlet_id\", \"outlet_name\", \"day\", \"is_upcoming_polygon\"],\n", ")\n", "existing_store_df1.rename(\n", "    columns={\n", "        \"dod_change\": \"dod_change_4\",\n", "        \"aov_dod_change\": \"aov_dod_change_4\",\n", "        \"ipc_dod_change\": \"ipc_dod_change_4\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "52d61e4c-8edf-4e48-ab2e-ec74f6b070d4", "metadata": {}, "outputs": [], "source": ["existing_store_df1 = existing_store_df1.merge(\n", "    dod_avg_5[dod_avg_5[\"date_\"] > 1][\n", "        [\n", "            \"day\",\n", "            \"city_name\",\n", "            \"is_upcoming_polygon\",\n", "            \"dod_change\",\n", "            \"aov_dod_change\",\n", "            \"ipc_dod_change\",\n", "        ]\n", "    ].drop_duplicates(),\n", "    how=\"left\",\n", "    on=[\"day\", \"city_name\", \"is_upcoming_polygon\"],\n", ")\n", "existing_store_df1.rename(\n", "    columns={\n", "        \"dod_change\": \"dod_change_5\",\n", "        \"aov_dod_change\": \"aov_dod_change_5\",\n", "        \"ipc_dod_change\": \"ipc_dod_change_5\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c5b44c2a-af2f-4cf1-baa0-c2627735d7d2", "metadata": {}, "outputs": [], "source": ["existing_store_df1[\"dod_change\"] = np.where(\n", "    (existing_store_df1[\"dod_change_1\"].isna())\n", "    & (existing_store_df1[\"dod_change_2\"].isna())\n", "    & (existing_store_df1[\"dod_change_3\"].isna()),\n", "    existing_store_df1[\"dod_change_4\"],\n", "    np.where(\n", "        (existing_store_df1[\"dod_change_1\"].isna())\n", "        & (existing_store_df1[\"dod_change_2\"].isna()),\n", "        existing_store_df1[\"dod_change_3\"],\n", "        np.where(\n", "            existing_store_df1[\"dod_change_1\"].isna(),\n", "            existing_store_df1[\"dod_change_2\"],\n", "            existing_store_df1[\"dod_change_1\"],\n", "        ),\n", "    ),\n", ")\n", "\n", "existing_store_df1[\"aov_dod_change\"] = np.where(\n", "    (existing_store_df1[\"aov_dod_change_1\"].isna())\n", "    & (existing_store_df1[\"aov_dod_change_2\"].isna())\n", "    & (existing_store_df1[\"aov_dod_change_3\"].isna()),\n", "    existing_store_df1[\"aov_dod_change_4\"],\n", "    np.where(\n", "        (existing_store_df1[\"aov_dod_change_1\"].isna())\n", "        & (existing_store_df1[\"aov_dod_change_2\"].isna()),\n", "        existing_store_df1[\"aov_dod_change_3\"],\n", "        np.where(\n", "            existing_store_df1[\"aov_dod_change_1\"].isna(),\n", "            existing_store_df1[\"aov_dod_change_2\"],\n", "            existing_store_df1[\"aov_dod_change_1\"],\n", "        ),\n", "    ),\n", ")\n", "\n", "existing_store_df1[\"ipc_dod_change\"] = np.where(\n", "    (existing_store_df1[\"ipc_dod_change_1\"].isna())\n", "    & (existing_store_df1[\"ipc_dod_change_2\"].isna())\n", "    & (existing_store_df1[\"ipc_dod_change_3\"].isna()),\n", "    existing_store_df1[\"ipc_dod_change_4\"],\n", "    np.where(\n", "        (existing_store_df1[\"ipc_dod_change_1\"].isna())\n", "        & (existing_store_df1[\"ipc_dod_change_2\"].isna()),\n", "        existing_store_df1[\"ipc_dod_change_3\"],\n", "        np.where(\n", "            existing_store_df1[\"ipc_dod_change_1\"].isna(),\n", "            existing_store_df1[\"ipc_dod_change_2\"],\n", "            existing_store_df1[\"ipc_dod_change_1\"],\n", "        ),\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "63dbbef5-eec5-4acc-a199-b037871e3978", "metadata": {}, "outputs": [], "source": ["existing_store_df1[\"dod_change\"] = np.where(\n", "    (existing_store_df1[\"dod_change\"].isna())\n", "    | (\n", "        abs(existing_store_df1[\"dod_change_5\"] - existing_store_df1[\"dod_change\"])\n", "        >= 0.1\n", "    ),\n", "    existing_store_df1[\"dod_change_5\"],\n", "    existing_store_df1[\"dod_change\"],\n", ")\n", "existing_store_df1[\"aov_dod_change\"] = np.where(\n", "    existing_store_df1[\"aov_dod_change\"] > 1.1,\n", "    1.1,\n", "    existing_store_df1[\"aov_dod_change\"],\n", ")\n", "existing_store_df1[\"aov_dod_change\"].fillna(\n", "    existing_store_df1[\"aov_dod_change_5\"], inplace=True\n", ")\n", "existing_store_df1[\"ipc_dod_change\"].fillna(\n", "    existing_store_df1[\"ipc_dod_change_5\"], inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "476c6e3f-5115-43b1-81ba-47b04f71b712", "metadata": {}, "outputs": [], "source": ["existing_store_df1[\"dod_change\"].isna().sum(), existing_store_df1[\n", "    \"aov_dod_change\"\n", "].isna().sum(), existing_store_df1[\"ipc_dod_change\"].isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "7f027d72-9519-47b1-9219-a0a6d41af9ee", "metadata": {}, "outputs": [], "source": ["existing_store_df1.drop(\n", "    columns=[\n", "        \"dod_change_1\",\n", "        \"dod_change_2\",\n", "        \"dod_change_3\",\n", "        \"dod_change_4\",\n", "        \"dod_change_5\",\n", "        \"aov_dod_change_1\",\n", "        \"aov_dod_change_2\",\n", "        \"aov_dod_change_3\",\n", "        \"aov_dod_change_4\",\n", "        \"aov_dod_change_5\",\n", "        \"ipc_dod_change_1\",\n", "        \"ipc_dod_change_2\",\n", "        \"ipc_dod_change_3\",\n", "        \"ipc_dod_change_4\",\n", "        \"ipc_dod_change_5\",\n", "    ],\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "53d25b02-56eb-4a52-8a6b-bfaa6621a0d9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "760f11e1-76a5-4d50-91c2-0067d623cc40", "metadata": {}, "outputs": [], "source": ["new_store_list = existing_store_df1[\n", "    (existing_store_df1[\"dod_change\"].isna())\n", "    & (existing_store_df1[\"date_\"] > train_end_date)\n", "][[\"outlet_id\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "26257cb6-7f25-430a-98d4-e0c78bf3f052", "metadata": {}, "outputs": [], "source": ["existing_store_df1 = existing_store_df1[\n", "    ~existing_store_df1[\"outlet_id\"].isin(list(new_store_list[\"outlet_id\"].unique()))\n", "].reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "a4b65c28-0053-40ba-a3b6-bade76e286fb", "metadata": {}, "outputs": [], "source": ["new_store_df = new_store_df.append(\n", "    base_df1.merge(new_store_list, how=\"inner\", on=[\"outlet_id\"])\n", ").drop_duplicates()\n", "new_store_df[\"store_type\"] = \"new\""]}, {"cell_type": "code", "execution_count": null, "id": "2e694b46-ad82-4dc8-9536-c6643c38da5d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "491372c6-f555-446e-83ab-11119e06ad7d", "metadata": {}, "outputs": [], "source": ["existing_store_df1.drop(columns=[\"store_live_date\", \"index\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "2371f158-ba43-4a6f-a6aa-0795a19f9f2f", "metadata": {}, "outputs": [], "source": ["new_store_df.drop(columns=[\"store_live_date\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "49b5920b-bcd4-42bc-baaa-1b21f2e85989", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "48ef1834-137f-4e15-bb42-a4e9951a30c0", "metadata": {}, "outputs": [], "source": ["latest_non_festive_day_orders[\"store_type\"] = np.where(\n", "    (\n", "        (\n", "            pd.to_datetime(latest_non_festive_day_orders[\"date_\"])\n", "            - pd.to_datetime(latest_non_festive_day_orders[\"store_live_date\"])\n", "        ).dt.days\n", "        < 60\n", "    )\n", "    | (latest_non_festive_day_orders[\"store_live_date\"] == \"1900-01-01\"),\n", "    \"new\",\n", "    \"old\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "52a74aa7-6a3a-4661-9830-5c9869212d08", "metadata": {}, "outputs": [], "source": ["latest_non_festive_day_orders1 = latest_non_festive_day_orders[\n", "    [\n", "        \"city_name\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"date_\",\n", "        \"day\",\n", "        \"hfs_tag\",\n", "        \"is_upcoming_polygon\",\n", "        \"store_type\",\n", "        \"inc_orders\",\n", "        \"ipc\",\n", "        \"aov\",\n", "    ]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "41c8aeb5-ace5-4d0f-aa9d-7e20a183d693", "metadata": {}, "outputs": [], "source": ["latest_non_festive_day_orders1[\"dod_change\"] = np.NaN\n", "# latest_non_festive_day_orders1[\"aov_change\"] = np.NaN"]}, {"cell_type": "code", "execution_count": null, "id": "9a8f38cd-f9b7-4620-8500-366fb22ea069", "metadata": {}, "outputs": [], "source": ["latest_non_festive_day_orders1[latest_non_festive_day_orders1.outlet_id == 4547]"]}, {"cell_type": "code", "execution_count": null, "id": "edf3a1f8-c6ad-4c22-b66a-257d5a2022b4", "metadata": {}, "outputs": [], "source": ["existing_store_df1 = existing_store_df1.merge(\n", "    latest_non_festive_day_orders1.rename(columns={\"date_\": \"latest_non_festive_date\"})[\n", "        [\"outlet_id\", \"is_upcoming_polygon\", \"latest_non_festive_date\"]\n", "    ].drop_duplicates(),\n", "    how=\"inner\",\n", "    on=[\"outlet_id\", \"is_upcoming_polygon\"],\n", ")\n", "\n", "rem_df = existing_store_df1[\n", "    [\n", "        \"city_name\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"date_\",\n", "        \"day\",\n", "        \"wom\",\n", "        \"hfs_tag\",\n", "        \"store_type\",\n", "    ]\n", "].merge(\n", "    latest_non_festive_day_orders1[[\"outlet_id\"]].drop_duplicates(),\n", "    how=\"outer\",\n", "    on=[\"outlet_id\"],\n", "    indicator=True,\n", ")\n", "new_store_df2 = rem_df[rem_df[\"_merge\"] == \"left_only\"].reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "a2d061aa-9602-41f6-a7f7-ecb508abec0b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "acbe153a-9e77-4b52-a562-af0d2723bf7d", "metadata": {}, "outputs": [], "source": ["new_store_df2.drop(columns=[\"index\", \"_merge\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "b002dd6b-0070-4da0-ac28-c1da1be1c15b", "metadata": {}, "outputs": [], "source": ["existing_store_df2 = latest_non_festive_day_orders1.rename(\n", "    columns={\"inc_orders\": \"orders\"}\n", ").append(existing_store_df1)"]}, {"cell_type": "code", "execution_count": null, "id": "9e01cf51-ffd7-4cd4-84b2-801ae07e95ae", "metadata": {}, "outputs": [], "source": ["existing_store_df2[\"date_\"] = pd.to_datetime(existing_store_df2[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "3873eec5-1437-44ff-b0bd-df72bc34b2de", "metadata": {}, "outputs": [], "source": ["existing_store_df2 = existing_store_df2.sort_values(\n", "    by=[\"city_name\", \"outlet_id\", \"is_upcoming_polygon\", \"date_\"]\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "ead972f1-c6ff-4f96-b0cd-fdbcd58b17da", "metadata": {}, "outputs": [], "source": ["existing_store_df2[\"orders\"].fillna(0, inplace=True)\n", "# existing_store_df2[\"aov\"].fillna(0, inplace=True)\n", "# existing_store_df2[\"ipc\"].fillna(0, inplace=True)\n", "existing_store_df2[\"dod_change\"].fillna(1, inplace=True)\n", "existing_store_df2[\"aov_dod_change\"].fillna(1, inplace=True)\n", "existing_store_df2[\"ipc_dod_change\"].fillna(1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "65d15b08-58b4-4f47-9fcf-179a3a63ef3d", "metadata": {}, "outputs": [], "source": ["existing_store_df2[\"latest_non_festive_date\"] = np.where(\n", "    existing_store_df2[\"latest_non_festive_date\"].isna(),\n", "    existing_store_df2[\"date_\"].astype(str),\n", "    existing_store_df2[\"latest_non_festive_date\"].astype(str),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9c99af10-1763-498f-b1e2-f881132e0881", "metadata": {}, "outputs": [], "source": ["existing_store_df2[\n", "    (existing_store_df2.outlet_id == 4547)\n", "    & (existing_store_df2.date_ >= existing_store_df2[\"latest_non_festive_date\"])\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "33a21ade-164a-46c0-833d-2af6e9fda683", "metadata": {}, "outputs": [], "source": ["for i in range(0, len(existing_store_df2)):\n", "\n", "    if (existing_store_df2.loc[i, \"orders\"] == 0) & (\n", "        pd.to_datetime(existing_store_df2.loc[i, \"date_\"])\n", "        >= pd.to_datetime(existing_store_df2.loc[i, \"latest_non_festive_date\"])\n", "    ):\n", "        existing_store_df2.loc[i, \"orders\"] = (\n", "            existing_store_df2.loc[i - 1, \"orders\"]\n", "            * existing_store_df2.loc[i, \"dod_change\"]\n", "        )\n", "        existing_store_df2.loc[i, \"aov\"] = (\n", "            existing_store_df2.loc[i - 1, \"aov\"]\n", "            * existing_store_df2.loc[i, \"aov_dod_change\"]\n", "        )\n", "        # existing_store_df2.loc[i, \"aov\"] = existing_store_df2.loc[i-1, \"aov\"]*np.where((existing_store_df2.loc[i, \"aov_dod_change\"]<0.95) | (existing_store_df2.loc[i, \"aov_dod_change\"]>1.05),1,existing_store_df2.loc[i, \"aov_dod_change\"])\n", "        existing_store_df2.loc[i, \"ipc\"] = (\n", "            existing_store_df2.loc[i - 1, \"ipc\"]\n", "            * existing_store_df2.loc[i, \"ipc_dod_change\"]\n", "        )\n", "        # existing_store_df2.loc[i, \"ipc\"] = existing_store_df2.loc[i-1, \"ipc\"]*np.where((existing_store_df2.loc[i, \"ipc_dod_change\"]<0.95) | (existing_store_df2.loc[i, \"ipc_dod_change\"]>1.05),1,existing_store_df2.loc[i, \"ipc_dod_change\"])\n", "\n", "    # if existing_store_df2.loc[i, \"aov\"] == 0:\n", "    #     existing_store_df2.loc[i, \"aov\"] = existing_store_df2.loc[i-1, \"aov\"]*existing_store_df2.loc[i, \"aov_change\"]"]}, {"cell_type": "code", "execution_count": null, "id": "c4a1386a-0e7b-4fd5-8970-37edb7bcdeeb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "71c8e137-d208-475a-bb7c-b09720a49c5d", "metadata": {}, "outputs": [], "source": ["# existing_store_df2[(existing_store_df2.outlet_id==4547) & (existing_store_df2.orders>0) & (existing_store_df2.is_upcoming_polygon==1)].head(60)"]}, {"cell_type": "code", "execution_count": null, "id": "0ea3df6f-424a-408f-989d-0bf3d1794938", "metadata": {}, "outputs": [], "source": ["orders[\"date_\"] = pd.to_datetime(orders[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "c5b550ce-9c85-4363-98fc-db494d3696d0", "metadata": {}, "outputs": [], "source": ["existing_store_df2[\"orders\"] = existing_store_df2[\"orders\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "42e4d333-d248-4009-817d-7631b8bf17b2", "metadata": {}, "outputs": [], "source": ["existing_store_df2.drop(columns=[\"index\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "1e2dfddc-ae15-4c87-984f-887e27ee1da9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d0bae3a5-dbf9-4f95-859f-97dd16d00c15", "metadata": {}, "outputs": [], "source": ["new_store_df = new_store_df.append(new_store_df2)"]}, {"cell_type": "code", "execution_count": null, "id": "f3bc41c9-ec7a-489f-9e47-1969f7589cc8", "metadata": {}, "outputs": [], "source": ["new_store_df[[\"dod_change\", \"aov_dod_change\", \"ipc_dod_change\", \"orders\", \"aov\"]] = 0"]}, {"cell_type": "code", "execution_count": null, "id": "f02a00e8-605a-4b04-af13-f4ca5cb7d3cf", "metadata": {}, "outputs": [], "source": ["new_store_df1 = new_store_df[\n", "    [\n", "        \"city_name\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"date_\",\n", "        \"day\",\n", "        \"hfs_tag\",\n", "        \"store_type\",\n", "        \"orders\",\n", "        \"aov\",\n", "        \"dod_change\",\n", "        \"aov_dod_change\",\n", "        \"ipc_dod_change\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "677258f3-da23-4ae1-b9ed-fd25f66c45f4", "metadata": {}, "outputs": [], "source": ["new_store_df1[\"outlet_id\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "c0fae4b9-748b-46e6-a767-e6d80013bd70", "metadata": {}, "outputs": [], "source": ["new_store_df1 = new_store_df1[new_store_df1[\"date_\"] > pd.to_datetime(train_end_date)]"]}, {"cell_type": "code", "execution_count": null, "id": "05027e9b-68fb-4f56-a373-6a8451ccdaf4", "metadata": {}, "outputs": [], "source": ["existing_store_df2 = existing_store_df2[\n", "    (existing_store_df2[\"date_\"] > pd.to_datetime(train_end_date))\n", "    & (existing_store_df2[\"orders\"] > 0)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "b1cdabae-f053-44b6-a493-efe76244caee", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "96499206-5e89-49b4-8a54-c990b7851ad6", "metadata": {}, "outputs": [], "source": ["# existing_store_df2 = existing_store_df2[~existing_store_df2[\"outlet_id\"].isin([3480, 3513, 4392, 3753, 4414, 4422, 3754, 3312, 3508, 3984, 3895, 4099])]"]}, {"cell_type": "code", "execution_count": null, "id": "0316d1b2-c80a-44b9-a46a-57edfb80a908", "metadata": {}, "outputs": [], "source": ["existing_store_df2 = existing_store_df2[~existing_store_df2[\"date_\"].isin(list1)]"]}, {"cell_type": "code", "execution_count": null, "id": "09569bc7-1904-4c8e-bba8-097f3b7275e7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "305a536f-519e-4cda-9df3-81abd012b198", "metadata": {}, "outputs": [], "source": ["polygon_change_input1 = polygon_change_input[[\"merchant_id\", \"date_live_from\"]].merge(\n", "    outlet_merchant_mapping, how=\"left\", on=[\"merchant_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "78f0cdcf-3149-4042-a051-7c6c1dad518c", "metadata": {}, "outputs": [], "source": ["polygon_change_input1"]}, {"cell_type": "code", "execution_count": null, "id": "3c0cfb67-c78c-4644-9111-11376bcd8b38", "metadata": {}, "outputs": [], "source": ["existing_store_df2 = existing_store_df2.merge(\n", "    polygon_change_input1[[\"outlet_id\", \"date_live_from\"]], how=\"left\", on=[\"outlet_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "505f0843-febe-4c2b-be3e-8cdb05782344", "metadata": {}, "outputs": [], "source": ["existing_store_df2.shape"]}, {"cell_type": "code", "execution_count": null, "id": "86cb2093-7c26-4b8b-9a8b-aba40d421d23", "metadata": {}, "outputs": [], "source": ["existing_store_df2 = existing_store_df2[\n", "    (\n", "        (existing_store_df2.is_upcoming_polygon == 0)\n", "        & (\n", "            (existing_store_df2.date_ < existing_store_df2.date_live_from)\n", "            | (existing_store_df2.date_live_from.isna())\n", "        )\n", "    )\n", "    | (\n", "        (existing_store_df2.is_upcoming_polygon == 1)\n", "        & (\n", "            (existing_store_df2.date_ >= existing_store_df2.date_live_from)\n", "            | (existing_store_df2.date_live_from.isna())\n", "        )\n", "    )\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "7347be21-057b-4528-9022-d35697cc563b", "metadata": {}, "outputs": [], "source": ["existing_store_df2.shape"]}, {"cell_type": "code", "execution_count": null, "id": "fa0a535b-0e8e-446d-a627-054adab02d86", "metadata": {}, "outputs": [], "source": ["# existing_store_df2[existing_store_df2.outlet_id==4547].head(40)"]}, {"cell_type": "code", "execution_count": null, "id": "35d4b800-e143-4bf8-9dcb-e0c6b2dcc641", "metadata": {}, "outputs": [], "source": ["buffer_store_growth = pb.from_sheets(\n", "    \"1lepKjqGE5dKrp5D_0orbl4BFVv1RkaA2SZKDouDSy3k\", \"buffer_store_growth\"\n", ")\n", "\n", "# buffer_store_growth = pd.read_csv('buffer_store_growth.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "649d6d83-72dc-42e0-9fea-7c0b969a2679", "metadata": {}, "outputs": [], "source": ["buffer_store_growth1 = buffer_store_growth[~buffer_store_growth[\"city_name1\"].isna()][\n", "    [\"city_name1\", \"wk_diff1\", \"conv_delta1\"]\n", "]\n", "buffer_store_growth1.rename(\n", "    columns={\n", "        \"city_name1\": \"city_name\",\n", "        \"wk_diff1\": \"wk_diff\",\n", "        \"conv_delta1\": \"conv_delta\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "980f4ddf-ca1a-4f38-8832-bb5a0c763566", "metadata": {}, "outputs": [], "source": ["ov_buffer_store_growth1 = (\n", "    buffer_store_growth1.groupby([\"wk_diff\"])[\"conv_delta\"].mean().reset_index()\n", ")\n", "ov_buffer_store_growth1.rename(columns={\"conv_delta\": \"ov_conv_delta\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "21ac6f16-859d-443d-93dd-aca980c83589", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cf1e684c-a7b6-43e0-a5b2-70a0b6d29074", "metadata": {}, "outputs": [], "source": ["existing_store_df2[\"wk_diff\"] = (\n", "    pd.to_datetime(existing_store_df2[\"date_\"])\n", "    - pd.to_datetime(existing_store_df2[\"date_live_from\"])\n", ").dt.days // 7"]}, {"cell_type": "code", "execution_count": null, "id": "71c41423-bc18-4b58-8e3f-a6917cc764fc", "metadata": {}, "outputs": [], "source": ["existing_store_df3 = existing_store_df2.merge(\n", "    buffer_store_growth1, how=\"left\", on=[\"city_name\", \"wk_diff\"]\n", ")\n", "existing_store_df3 = existing_store_df3.merge(\n", "    ov_buffer_store_growth1, how=\"left\", on=[\"wk_diff\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4730d740-1a52-4bc8-ac97-f4680265ab59", "metadata": {}, "outputs": [], "source": ["existing_store_df3[\"conv_delta\"] = np.where(\n", "    existing_store_df3[\"conv_delta\"].isna(),\n", "    existing_store_df3[\"ov_conv_delta\"],\n", "    existing_store_df3[\"conv_delta\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "89b75edb-e8c7-4833-9037-c9e6d6d8bafc", "metadata": {}, "outputs": [], "source": ["existing_store_df3[\"conv_delta\"].fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "42edf308-7596-4bbb-ad42-59f94cfaf67b", "metadata": {}, "outputs": [], "source": ["existing_store_df3[\"conv_delta\"] = np.where(\n", "    existing_store_df3[\"conv_delta\"] < 0, 0, existing_store_df3[\"conv_delta\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "83f91954-dfb2-4f79-96b7-6dc7c16793bc", "metadata": {}, "outputs": [], "source": ["existing_store_df3[\"orders\"].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "2b935724-3967-4499-a15c-0bbbf4625028", "metadata": {}, "outputs": [], "source": ["existing_store_df3[\"orders\"] = np.round(\n", "    existing_store_df3[\"orders\"] * (1 + existing_store_df3[\"conv_delta\"]), 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f0d88cd9-8640-44a1-ba96-480147877e6a", "metadata": {}, "outputs": [], "source": ["existing_store_df3[\"orders\"].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "f60a3500-9d7f-45c1-9c31-51028a5f47d1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "c6dbf35a-0fd0-490a-9008-6fa95d346724", "metadata": {}, "source": ["### New/Buffer Stores Tagging"]}, {"cell_type": "code", "execution_count": null, "id": "64b7110a-f2a7-4d5d-b632-2c87d759afd9", "metadata": {}, "outputs": [], "source": ["orders_cy = pd.read_csv(cwd + \"/orders_train_end_20240515.csv\")\n", "if orders_new.shape[0] > 0:\n", "    orders_cy = orders_cy.append(orders_new)\n", "orders_cy.date_ = pd.to_datetime(orders_cy.date_)\n", "orders_cy[\"aov\"] = np.round(orders_cy[\"gmv\"] * 1.00 / orders_cy[\"orders\"], 0)\n", "orders_cy[\"ipc\"] = np.round(orders_cy[\"item_qty\"] * 1.00 / orders_cy[\"orders\"], 2)\n", "\n", "orders_cy = orders_cy.merge(\n", "    outlet_merchant_mapping[[\"outlet_id\", \"merchant_id\"]].drop_duplicates(),\n", "    on=[\"merchant_id\"],\n", "    how=\"left\",\n", ")\n", "# orders_cy = orders_cy.merge(active_store, on=[\"merchant_id\"], how=\"inner\")\n", "\n", "orders_cy.drop(columns=[\"gmv\", \"item_qty\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "caa127cb-6490-44ee-abdf-04df9a7085a3", "metadata": {}, "outputs": [], "source": ["orders_cy.groupby([\"date_\"])[\"orders\"].sum().tail(15)"]}, {"cell_type": "code", "execution_count": null, "id": "7c6f8a19-1d7f-40e3-9b84-cd8f77d91aea", "metadata": {}, "outputs": [], "source": ["orders_cy1 = orders_cy[\n", "    (orders_cy.date_ >= pd.to_datetime(train_end_date))\n", "    & (orders_cy.date_ <= pd.to_datetime(ov_end_date))\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "57594ca7-d4d2-4ba3-9dd9-aa6eca29e187", "metadata": {}, "outputs": [], "source": ["orders_cy1[\"rank\"] = orders_cy1.groupby([\"merchant_id\"])[\"date_\"].rank(\n", "    method=\"min\", ascending=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "67ddc957-f480-4db6-9f8e-d1944a9cbb88", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7c653c50-623e-471e-a4eb-846455a9d7b3", "metadata": {}, "outputs": [], "source": ["check_new_stores = (\n", "    orders_cy1[orders_cy1[\"rank\"] == 1][\n", "        [\"city_name\", \"outlet_id\", \"orders\", \"ipc\", \"aov\"]\n", "    ]\n", "    .drop_duplicates()\n", "    .merge(\n", "        existing_store_df3[[\"outlet_id\"]].drop_duplicates(),\n", "        on=[\"outlet_id\"],\n", "        how=\"outer\",\n", "        indicator=True,\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ee03c7db-1501-49b9-8831-f18309c54394", "metadata": {}, "outputs": [], "source": ["check_new_stores.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c4e59f13-30c6-48d2-ac5d-235ba0aa3266", "metadata": {}, "outputs": [], "source": ["check_new_stores = check_new_stores[check_new_stores[\"_merge\"] == \"left_only\"][\n", "    [\"city_name\", \"outlet_id\", \"orders\", \"ipc\", \"aov\"]\n", "].reset_index()\n", "check_new_stores.drop(columns=[\"index\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "8849bbd0-4afd-4894-8c5f-38b2616d301f", "metadata": {}, "outputs": [], "source": ["check_new_stores = check_new_stores.merge(store_live, how=\"left\", on=[\"outlet_id\"])\n", "check_new_stores = check_new_stores.merge(\n", "    new_polygon[[\"Outlet Id\", \"OB Final Date\"]],\n", "    how=\"left\",\n", "    left_on=[\"outlet_id\"],\n", "    right_on=[\"Outlet Id\"],\n", ")\n", "check_new_stores[\"store_live_date\"] = np.where(\n", "    (check_new_stores[\"store_live_date\"].isna())\n", "    | (check_new_stores[\"store_live_date\"] == \"1900-01-01\"),\n", "    pd.to_datetime(check_new_stores[\"OB Final Date\"]).dt.strftime(\"%Y-%m-%d\"),\n", "    check_new_stores[\"store_live_date\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "14392b9c-e32f-44bf-8116-1363b23bd8b9", "metadata": {}, "outputs": [], "source": ["check_new_stores[\"store_live_date\"] = pd.to_datetime(\n", "    check_new_stores[\"store_live_date\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d0fab521-958a-40fe-9cb7-b1f98ac15dd7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "87a8f3ec-2975-4287-888e-07610be067b2", "metadata": {}, "outputs": [], "source": ["buffer_stores_area = buffer_tagging[\n", "    [\"merchant_id\", \"parent_merchant_id\", \"area_covered\"]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "93a70c4b-980d-42dd-a18e-44e78e3a87ce", "metadata": {}, "outputs": [], "source": ["buffer_stores_area = buffer_stores_area.merge(\n", "    outlet_merchant_mapping[[\"outlet_id\", \"merchant_id\"]],\n", "    how=\"left\",\n", "    on=[\"merchant_id\"],\n", ")\n", "buffer_stores_area = buffer_stores_area.merge(\n", "    active_store, how=\"inner\", on=[\"merchant_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5048a8ef-29ba-4365-9eba-327c2a63fe2c", "metadata": {}, "outputs": [], "source": ["buffer_stores_area[\"rank\"] = buffer_stores_area.groupby([\"merchant_id\"])[\n", "    \"area_covered\"\n", "].rank(method=\"max\", ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "ac394e99-5621-4078-b5d8-d6ddaa9fdec6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4d87184d-cddb-4f6f-a669-3e1979c5d842", "metadata": {}, "outputs": [], "source": ["check_new_stores = check_new_stores.merge(\n", "    buffer_stores_area[\n", "        (buffer_stores_area[\"area_covered\"] > 20) & (buffer_stores_area[\"rank\"] == 1)\n", "    ][[\"merchant_id\", \"parent_merchant_id\"]].drop_duplicates(),\n", "    how=\"outer\",\n", "    on=[\"merchant_id\"],\n", "    indicator=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c039dc21-d7c6-42e3-88e6-86cbfcdc6264", "metadata": {}, "outputs": [], "source": ["check_new_stores = check_new_stores[\n", "    check_new_stores[\"outlet_name\"].str.find(\"Test \") == -1\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "41699ee8-6b33-4390-8d53-6d2af1cd646e", "metadata": {}, "outputs": [], "source": ["recently_opened_stores = check_new_stores[\n", "    (check_new_stores[\"_merge\"] == \"left_only\")\n", "    & (\n", "        check_new_stores[\"store_live_date\"]\n", "        > pd.to_datetime(train_end_date) - <PERSON><PERSON><PERSON>(days=60)\n", "    )\n", "    & (check_new_stores[\"store_live_date\"] <= pd.to_datetime(datetime.today()))\n", "].drop_duplicates()\n", "recently_opened_stores[\"type\"] = \"recent_new\""]}, {"cell_type": "code", "execution_count": null, "id": "a8db0e1c-06e3-43ef-9f2b-3af5b783a1b0", "metadata": {}, "outputs": [], "source": ["nsa_stores = check_new_stores[\n", "    (check_new_stores[\"_merge\"] == \"left_only\")\n", "    & (check_new_stores[\"store_live_date\"] >= pd.to_datetime(datetime.today()))\n", "].drop_duplicates()\n", "nsa_stores[\"type\"] = \"nsa\""]}, {"cell_type": "code", "execution_count": null, "id": "040176e9-4f09-4572-9a3f-a3ce684003c2", "metadata": {}, "outputs": [], "source": ["buffer_stores = check_new_stores[\n", "    (check_new_stores[\"_merge\"] == \"both\")\n", "].drop_duplicates()\n", "buffer_stores[\"type\"] = \"recent_buffer\""]}, {"cell_type": "code", "execution_count": null, "id": "165bc0b0-0c6f-4b98-a22d-63a649a2c2d8", "metadata": {"tags": []}, "outputs": [], "source": ["recently_opened_stores.shape[0], nsa_stores.shape[0], buffer_stores.shape[0]"]}, {"cell_type": "code", "execution_count": null, "id": "80d9b982-0896-4bde-8f35-fc1caf88451c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "a9f3650f-4cf4-4497-927c-ab3f5dd9f4d4", "metadata": {}, "source": ["### Recently Opened New & Buffer Stores"]}, {"cell_type": "code", "execution_count": null, "id": "17573afd-2a03-453f-a14d-5969fc63b572", "metadata": {"tags": []}, "outputs": [], "source": ["new_store_trend = pd.read_csv(cwd + \"/new_store_city_trend.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "e782be8e-58d1-490b-abee-f45f18f5fbcb", "metadata": {}, "outputs": [], "source": ["new_store_trend.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "45896feb-e43f-48fd-be86-d45dc2a39d37", "metadata": {}, "outputs": [], "source": ["first_live_date = (\n", "    recently_opened_stores[\"store_live_date\"]\n", "    .append(buffer_stores[\"store_live_date\"])\n", "    .min()\n", ")\n", "if str(first_live_date) == \"NaT\":\n", "    first_live_date = ov_end_date\n", "first_live_date"]}, {"cell_type": "code", "execution_count": null, "id": "53d48bd3-782e-423b-a34f-5725e347b540", "metadata": {}, "outputs": [], "source": ["date_range1 = pd.date_range(\n", "    start=pd.to_datetime(first_live_date), end=pd.to_datetime(ov_end_date), freq=\"D\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e49bcba9-e0d4-4a5d-8bb8-d1355394c802", "metadata": {}, "outputs": [], "source": ["base_df_new_stores = pd.DataFrame(date_range1, columns=[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "44dc66e0-5dd9-4023-83cf-fbfacf9f8c83", "metadata": {}, "outputs": [], "source": ["base_df_new_stores[\"day\"] = base_df_new_stores[\"date_\"].dt.strftime(\"%A\")"]}, {"cell_type": "code", "execution_count": null, "id": "6edb45fd-f86c-416f-8d51-8374262932d8", "metadata": {}, "outputs": [], "source": ["base_df_new_stores = base_df_new_stores.merge(hfs_df, on=[\"date_\"], how=\"left\")\n", "base_df_new_stores[\"hfs_tag\"].fillna(0, inplace=True)\n", "base_df_new_stores[\"hfs_day1\"].fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "d689121f-10ac-4cf3-aeed-669f50dc4311", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df = pd.merge(\n", "    recently_opened_stores[\n", "        [\n", "            \"city_name\",\n", "            \"outlet_id\",\n", "            \"outlet_name\",\n", "            \"store_live_date\",\n", "            \"type\",\n", "            \"parent_merchant_id\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .append(\n", "        buffer_stores[\n", "            [\n", "                \"city_name\",\n", "                \"outlet_id\",\n", "                \"outlet_name\",\n", "                \"store_live_date\",\n", "                \"type\",\n", "                \"parent_merchant_id\",\n", "            ]\n", "        ].drop_duplicates()\n", "    )\n", "    .drop_duplicates()\n", "    .reset_index(),\n", "    base_df_new_stores,\n", "    how=\"cross\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "41d51240-ae92-4d09-b9eb-9bac2d671e69", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0fc68281-f10b-4290-a7e5-c85d2681929d", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df = recently_opened_stores_base_df[\n", "    recently_opened_stores_base_df[\"date_\"]\n", "    >= recently_opened_stores_base_df[\"store_live_date\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "d5ab2bab-f238-4ccd-af6b-21644196238b", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df = recently_opened_stores_base_df.merge(\n", "    orders_cy[\n", "        [\"city_name\", \"date_\", \"orders\", \"aov\", \"ipc\", \"outlet_id\"]\n", "    ].drop_duplicates(),\n", "    how=\"left\",\n", "    left_on=[\"date_\", \"outlet_id\", \"city_name\"],\n", "    right_on=[\"date_\", \"outlet_id\", \"city_name\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ac8098f4-88dd-4c1e-bb1a-fcc0a00ee303", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8984fc1d-4dcf-487e-9819-1f11c1f6819a", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df.rename(columns={\"date__x\": \"date_\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "3b621df0-7ae0-447f-98f4-d03bd5452481", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1 = recently_opened_stores_base_df.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "3b9d1b4a-0af0-4f22-9c44-8cbd8c54495d", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df[\"rank\"] = (\n", "    recently_opened_stores_base_df[recently_opened_stores_base_df.orders > 100]\n", "    .groupby([\"outlet_id\"])[\"date_\"]\n", "    .rank(method=\"min\", ascending=True)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d3a0bcfd-84ac-4019-b0e0-ccba74fd88bb", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1 = recently_opened_stores_base_df1.merge(\n", "    recently_opened_stores_base_df[[\"city_name\", \"outlet_id\", \"date_\", \"rank\"]],\n", "    how=\"left\",\n", "    on=[\"city_name\", \"outlet_id\", \"date_\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "87207a15-dfff-4ae1-bf04-b173795d56e2", "metadata": {}, "outputs": [], "source": ["store_start_date = (\n", "    recently_opened_stores_base_df1[recently_opened_stores_base_df1[\"rank\"] == 1]\n", "    .groupby([\"outlet_id\"])[\"date_\"]\n", "    .min()\n", "    .reset_index()\n", ")\n", "store_start_date.rename(columns={\"date_\": \"store_start_date\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "ed292684-0756-48d6-b2b0-725ecab6fe7b", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1 = recently_opened_stores_base_df1.merge(\n", "    store_start_date, how=\"left\", on=[\"outlet_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "997c2c3a-f52e-4cc1-93cc-a6c8d695d386", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "29ba20c5-26a6-43d5-9c47-fc9699589996", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1 = recently_opened_stores_base_df1[\n", "    recently_opened_stores_base_df1[\"date_\"]\n", "    >= recently_opened_stores_base_df1[\"store_start_date\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "a8124fb8-7853-4994-8e2d-071e704be914", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1[\"nth_week\"] = (\n", "    pd.to_datetime(recently_opened_stores_base_df1[\"date_\"])\n", "    - pd.to_datetime(recently_opened_stores_base_df1[\"store_live_date\"])\n", ").dt.days // 7"]}, {"cell_type": "code", "execution_count": null, "id": "0b10915c-a80c-42ac-8238-b6cba43918fe", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1[\"dt\"] = pd.to_datetime(\n", "    recently_opened_stores_base_df1[\"date_\"]\n", ").dt.strftime(\"%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "3a056535-ea31-4eaf-bfff-44194ca3fb43", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1[\"orders\"] = np.where(\n", "    recently_opened_stores_base_df1[\"rank\"] == 1,\n", "    recently_opened_stores_base_df1[\"orders\"],\n", "    0,\n", ")\n", "recently_opened_stores_base_df1[\"aov\"] = np.where(\n", "    recently_opened_stores_base_df1[\"rank\"] == 1,\n", "    recently_opened_stores_base_df1[\"aov\"],\n", "    0,\n", ")\n", "recently_opened_stores_base_df1[\"ipc\"] = np.where(\n", "    recently_opened_stores_base_df1[\"rank\"] == 1,\n", "    recently_opened_stores_base_df1[\"ipc\"],\n", "    0,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f0062926-3e57-4ae3-bd29-eed79f65b5d9", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1 = recently_opened_stores_base_df1.merge(\n", "    outlet_merchant_mapping[[\"merchant_id\", \"outlet_id\"]].rename(\n", "        columns={\"merchant_id\": \"parent_merchant_id\", \"outlet_id\": \"parent_outlet_id\"}\n", "    ),\n", "    how=\"left\",\n", "    on=[\"parent_merchant_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4da1ad2d-b338-4659-9f8d-26048f57a40a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "06b95564-e393-49d5-99f7-3bb85890654b", "metadata": {}, "outputs": [], "source": ["## new stores trend for new stores\n", "recently_opened_stores_base_df1_new = recently_opened_stores_base_df1[\n", "    (recently_opened_stores_base_df1.type == \"recent_new\")\n", "].merge(\n", "    new_store_trend.groupby([\"city_name\", \"day\", \"nth_week\"])[\"dod_change\"]\n", "    .mean()\n", "    .reset_index(),\n", "    on=[\"city_name\", \"day\", \"nth_week\"],\n", "    how=\"left\",\n", ")\n", "recently_opened_stores_base_df1_new = recently_opened_stores_base_df1_new.merge(\n", "    new_store_trend.groupby([\"day\", \"nth_week\"])[\"dod_change\"].mean().reset_index(),\n", "    on=[\"day\", \"nth_week\"],\n", "    how=\"left\",\n", ")\n", "recently_opened_stores_base_df1_new[\"dod_change\"] = np.where(\n", "    recently_opened_stores_base_df1_new[\"dod_change_x\"].isna(),\n", "    recently_opened_stores_base_df1_new[\"dod_change_y\"],\n", "    recently_opened_stores_base_df1_new[\"dod_change_x\"],\n", ")\n", "recently_opened_stores_base_df1_new.drop(\n", "    columns=[\"dod_change_x\", \"dod_change_y\"], inplace=True\n", ")\n", "recently_opened_stores_base_df1_new[\"dod_change\"] = (\n", "    recently_opened_stores_base_df1_new[\"dod_change\"] + 1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b6bd2611-e26d-41d5-b6e7-f0c5f823a0dc", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1_new = recently_opened_stores_base_df1_new.merge(\n", "    dod_avg_5[(dod_avg_5[\"date_\"] > 1) & (dod_avg_5[\"is_upcoming_polygon\"] == 0)][\n", "        [\"day\", \"city_name\", \"dod_change\", \"aov_dod_change\", \"ipc_dod_change\"]\n", "    ].drop_duplicates(),\n", "    on=[\"day\", \"city_name\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fe5495eb-b5a0-49a5-a5e5-8d58f321e679", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1_new[\"dod_change\"] = np.where(\n", "    recently_opened_stores_base_df1_new[\"dod_change_x\"].isna(),\n", "    recently_opened_stores_base_df1_new[\"dod_change_y\"],\n", "    recently_opened_stores_base_df1_new[\"dod_change_x\"],\n", ")\n", "recently_opened_stores_base_df1_new.drop(\n", "    columns=[\"dod_change_x\", \"dod_change_y\"], inplace=True\n", ")\n", "# recently_opened_stores_base_df1_new[\"aov_dod_change\"] = 1\n", "# recently_opened_stores_base_df1_new[\"ipc_dod_change\"] = 1"]}, {"cell_type": "code", "execution_count": null, "id": "8911dde6-71ae-4ab5-9795-0bd8ffc4255a", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1_new[\"dod_change\"].fillna(1, inplace=True)\n", "recently_opened_stores_base_df1_new[\"aov_dod_change\"].fillna(1, inplace=True)\n", "recently_opened_stores_base_df1_new[\"ipc_dod_change\"].fillna(1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "85e789af-883a-456b-ab7e-b5d12b4c0c80", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d16b99bc-a592-434b-861b-ae898c2bed38", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1_buffer = recently_opened_stores_base_df1[\n", "    (recently_opened_stores_base_df1.type == \"recent_buffer\")\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "d1cf444c-fae9-4c26-b261-c6b584fdcefd", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1_buffer = recently_opened_stores_base_df1_buffer.merge(\n", "    dod_avg_1[dod_avg_1[\"date_\"] > 1][\n", "        [\n", "            \"city_name\",\n", "            \"outlet_id\",\n", "            \"dt\",\n", "            \"day\",\n", "            \"hfs_tag\",\n", "            \"hfs_day1\",\n", "            \"dod_change\",\n", "            \"aov_dod_change\",\n", "            \"ipc_dod_change\",\n", "        ]\n", "    ]\n", "    .rename(columns={\"outlet_id\": \"parent_outlet_id\"})\n", "    .drop_duplicates(),\n", "    how=\"left\",\n", "    on=[\"city_name\", \"parent_outlet_id\", \"dt\", \"day\", \"hfs_tag\", \"hfs_day1\"],\n", ")\n", "recently_opened_stores_base_df1_buffer.rename(\n", "    columns={\n", "        \"dod_change\": \"dod_change_1\",\n", "        \"aov_dod_change\": \"aov_dod_change_1\",\n", "        \"ipc_dod_change\": \"ipc_dod_change_1\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9c3cabe7-7fba-4aac-abb5-1499b235a8bb", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1_buffer = recently_opened_stores_base_df1_buffer.merge(\n", "    dod_avg_2[dod_avg_2[\"date_\"] > 1][\n", "        [\n", "            \"city_name\",\n", "            \"outlet_id\",\n", "            \"dt\",\n", "            \"day\",\n", "            \"hfs_tag\",\n", "            \"dod_change\",\n", "            \"aov_dod_change\",\n", "            \"ipc_dod_change\",\n", "        ]\n", "    ]\n", "    .rename(columns={\"outlet_id\": \"parent_outlet_id\"})\n", "    .drop_duplicates(),\n", "    how=\"left\",\n", "    on=[\"city_name\", \"parent_outlet_id\", \"dt\", \"day\", \"hfs_tag\"],\n", ")\n", "recently_opened_stores_base_df1_buffer.rename(\n", "    columns={\n", "        \"dod_change\": \"dod_change_2\",\n", "        \"aov_dod_change\": \"aov_dod_change_2\",\n", "        \"ipc_dod_change\": \"ipc_dod_change_2\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "be668394-4155-4ac3-acd0-9de845112c0a", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1_buffer = recently_opened_stores_base_df1_buffer.merge(\n", "    dod_avg_3[dod_avg_3[\"date_\"] > 1][\n", "        [\n", "            \"city_name\",\n", "            \"outlet_id\",\n", "            \"day\",\n", "            \"hfs_tag\",\n", "            \"dod_change\",\n", "            \"aov_dod_change\",\n", "            \"ipc_dod_change\",\n", "        ]\n", "    ]\n", "    .rename(columns={\"outlet_id\": \"parent_outlet_id\"})\n", "    .drop_duplicates(),\n", "    how=\"left\",\n", "    on=[\"city_name\", \"parent_outlet_id\", \"day\", \"hfs_tag\"],\n", ")\n", "recently_opened_stores_base_df1_buffer.rename(\n", "    columns={\n", "        \"dod_change\": \"dod_change_3\",\n", "        \"aov_dod_change\": \"aov_dod_change_3\",\n", "        \"ipc_dod_change\": \"ipc_dod_change_3\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "66ec74af-7d3f-49c7-a0d8-344017397d6c", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1_buffer = recently_opened_stores_base_df1_buffer.merge(\n", "    dod_avg_4[dod_avg_4[\"date_\"] > 1][\n", "        [\n", "            \"city_name\",\n", "            \"outlet_id\",\n", "            \"day\",\n", "            \"dod_change\",\n", "            \"aov_dod_change\",\n", "            \"ipc_dod_change\",\n", "        ]\n", "    ]\n", "    .rename(columns={\"outlet_id\": \"parent_outlet_id\"})\n", "    .drop_duplicates(),\n", "    how=\"left\",\n", "    on=[\"city_name\", \"parent_outlet_id\", \"day\"],\n", ")\n", "recently_opened_stores_base_df1_buffer.rename(\n", "    columns={\n", "        \"dod_change\": \"dod_change_4\",\n", "        \"aov_dod_change\": \"aov_dod_change_4\",\n", "        \"ipc_dod_change\": \"ipc_dod_change_4\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "769680af-384f-4cea-ad38-eff610ecdf4a", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1_buffer = recently_opened_stores_base_df1_buffer.merge(\n", "    dod_avg_5[dod_avg_5[\"date_\"] > 1][\n", "        [\"day\", \"city_name\", \"dod_change\", \"aov_dod_change\", \"ipc_dod_change\"]\n", "    ].drop_duplicates(),\n", "    how=\"left\",\n", "    on=[\"day\", \"city_name\"],\n", ")\n", "recently_opened_stores_base_df1_buffer.rename(\n", "    columns={\n", "        \"dod_change\": \"dod_change_5\",\n", "        \"aov_dod_change\": \"aov_dod_change_5\",\n", "        \"ipc_dod_change\": \"ipc_dod_change_5\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9fc153f9-acc5-43c1-9282-a4d1f3f04c7b", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1_buffer[\"dod_change\"] = np.where(\n", "    (recently_opened_stores_base_df1_buffer[\"dod_change_1\"].isna())\n", "    & (recently_opened_stores_base_df1_buffer[\"dod_change_2\"].isna())\n", "    & (recently_opened_stores_base_df1_buffer[\"dod_change_3\"].isna()),\n", "    recently_opened_stores_base_df1_buffer[\"dod_change_4\"],\n", "    np.where(\n", "        (recently_opened_stores_base_df1_buffer[\"dod_change_1\"].isna())\n", "        & (recently_opened_stores_base_df1_buffer[\"dod_change_2\"].isna()),\n", "        recently_opened_stores_base_df1_buffer[\"dod_change_3\"],\n", "        np.where(\n", "            recently_opened_stores_base_df1_buffer[\"dod_change_1\"].isna(),\n", "            recently_opened_stores_base_df1_buffer[\"dod_change_2\"],\n", "            recently_opened_stores_base_df1_buffer[\"dod_change_1\"],\n", "        ),\n", "    ),\n", ")\n", "\n", "recently_opened_stores_base_df1_buffer[\"aov_dod_change\"] = np.where(\n", "    (recently_opened_stores_base_df1_buffer[\"aov_dod_change_1\"].isna())\n", "    & (recently_opened_stores_base_df1_buffer[\"aov_dod_change_2\"].isna())\n", "    & (recently_opened_stores_base_df1_buffer[\"aov_dod_change_3\"].isna()),\n", "    recently_opened_stores_base_df1_buffer[\"aov_dod_change_4\"],\n", "    np.where(\n", "        (recently_opened_stores_base_df1_buffer[\"aov_dod_change_1\"].isna())\n", "        & (recently_opened_stores_base_df1_buffer[\"aov_dod_change_2\"].isna()),\n", "        recently_opened_stores_base_df1_buffer[\"aov_dod_change_3\"],\n", "        np.where(\n", "            recently_opened_stores_base_df1_buffer[\"aov_dod_change_1\"].isna(),\n", "            recently_opened_stores_base_df1_buffer[\"aov_dod_change_2\"],\n", "            recently_opened_stores_base_df1_buffer[\"aov_dod_change_1\"],\n", "        ),\n", "    ),\n", ")\n", "\n", "recently_opened_stores_base_df1_buffer[\"ipc_dod_change\"] = np.where(\n", "    (recently_opened_stores_base_df1_buffer[\"ipc_dod_change_1\"].isna())\n", "    & (recently_opened_stores_base_df1_buffer[\"ipc_dod_change_2\"].isna())\n", "    & (recently_opened_stores_base_df1_buffer[\"ipc_dod_change_3\"].isna()),\n", "    recently_opened_stores_base_df1_buffer[\"ipc_dod_change_4\"],\n", "    np.where(\n", "        (recently_opened_stores_base_df1_buffer[\"ipc_dod_change_1\"].isna())\n", "        & (recently_opened_stores_base_df1_buffer[\"ipc_dod_change_2\"].isna()),\n", "        recently_opened_stores_base_df1_buffer[\"ipc_dod_change_3\"],\n", "        np.where(\n", "            recently_opened_stores_base_df1_buffer[\"ipc_dod_change_1\"].isna(),\n", "            recently_opened_stores_base_df1_buffer[\"ipc_dod_change_2\"],\n", "            recently_opened_stores_base_df1_buffer[\"ipc_dod_change_1\"],\n", "        ),\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1df533cb-44ff-42f3-8b61-c85057e37812", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1_buffer[\"dod_change\"].fillna(\n", "    recently_opened_stores_base_df1_buffer[\"dod_change_5\"], inplace=True\n", ")\n", "recently_opened_stores_base_df1_buffer[\"aov_dod_change\"].fillna(\n", "    recently_opened_stores_base_df1_buffer[\"aov_dod_change_5\"], inplace=True\n", ")\n", "recently_opened_stores_base_df1_buffer[\"ipc_dod_change\"].fillna(\n", "    recently_opened_stores_base_df1_buffer[\"ipc_dod_change_5\"], inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d43bc531-0926-4f89-b41a-b5db925027ac", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1_buffer.drop(\n", "    columns=[\n", "        \"parent_outlet_id\",\n", "        \"dod_change_1\",\n", "        \"dod_change_2\",\n", "        \"dod_change_3\",\n", "        \"dod_change_4\",\n", "        \"dod_change_5\",\n", "        \"aov_dod_change_1\",\n", "        \"aov_dod_change_2\",\n", "        \"aov_dod_change_3\",\n", "        \"aov_dod_change_4\",\n", "        \"aov_dod_change_5\",\n", "        \"ipc_dod_change_1\",\n", "        \"ipc_dod_change_2\",\n", "        \"ipc_dod_change_3\",\n", "        \"ipc_dod_change_4\",\n", "        \"ipc_dod_change_5\",\n", "    ],\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "75053ede-8aef-41da-a60b-67122318636f", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1 = (\n", "    recently_opened_stores_base_df1_new.append(recently_opened_stores_base_df1_buffer)\n", "    .sort_values(by=[\"city_name\", \"outlet_id\", \"date_\"])\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3d7d1739-910b-4032-bd2a-ca8b83b85e47", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1[\"ipc\"] = recently_opened_stores_base_df1[\"ipc\"].astype(\n", "    float\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7ca1a649-7ff3-41ec-a48b-641138928200", "metadata": {}, "outputs": [], "source": ["for i in range(0, len(recently_opened_stores_base_df1)):\n", "\n", "    if recently_opened_stores_base_df1.loc[i, \"orders\"] == 0:\n", "        recently_opened_stores_base_df1.loc[i, \"orders\"] = np.round(\n", "            recently_opened_stores_base_df1.loc[i - 1, \"orders\"]\n", "            * recently_opened_stores_base_df1.loc[i, \"dod_change\"],\n", "            0,\n", "        ).astype(int)\n", "        # recently_opened_stores_base_df1.loc[i, \"aov\"] = np.round(recently_opened_stores_base_df1.loc[i-1, \"aov\"]*np.where((recently_opened_stores_base_df1.loc[i, \"aov_dod_change\"]<0.95) | (recently_opened_stores_base_df1.loc[i, \"aov_dod_change\"] > 1.05),1,recently_opened_stores_base_df1.loc[i, \"aov_dod_change\"]),0).astype(int)\n", "        recently_opened_stores_base_df1.loc[i, \"aov\"] = np.round(\n", "            recently_opened_stores_base_df1.loc[i - 1, \"aov\"]\n", "            * recently_opened_stores_base_df1.loc[i, \"aov_dod_change\"],\n", "            0,\n", "        ).astype(int)\n", "\n", "        # recently_opened_stores_base_df1.loc[i, \"ipc\"] = np.round(recently_opened_stores_base_df1.loc[i-1, \"ipc\"]*np.where((recently_opened_stores_base_df1.loc[i, \"ipc_dod_change\"]<0.95) | (recently_opened_stores_base_df1.loc[i, \"ipc_dod_change\"] > 1.05),1,recently_opened_stores_base_df1.loc[i, \"ipc_dod_change\"]),0).astype(int)\n", "        recently_opened_stores_base_df1.loc[i, \"ipc\"] = np.round(\n", "            recently_opened_stores_base_df1.loc[i - 1, \"ipc\"]\n", "            * recently_opened_stores_base_df1.loc[i, \"ipc_dod_change\"],\n", "            0,\n", "        ).astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "fc5aa9b1-7a09-4c0c-874e-36039d727c0d", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1[\"store_type\"] = \"new\""]}, {"cell_type": "code", "execution_count": null, "id": "21b24e37-960c-40e7-a170-7e7e7754bb16", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1 = recently_opened_stores_base_df1[\n", "    ~recently_opened_stores_base_df1[\"date_\"].isin(list1)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "6719508a-dd20-4e04-903a-d9c9795b0408", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1.drop(columns=[\"index\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "477a35b6-94d2-4fed-ab1a-af9bd029c29a", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df1[\n", "    (recently_opened_stores_base_df1.outlet_id == 4470)\n", "    & (recently_opened_stores_base_df1.date_ == \"2024-05-10\")\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "8e95d4bb-a832-4707-a60b-d18fa2dbc116", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df2 = recently_opened_stores_base_df1[\n", "    recently_opened_stores_base_df1[\"date_\"]\n", "    >= pd.to_datetime(train_end_date) + timedelta(days=1)\n", "].reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "1e960776-90de-4b98-9ccd-df8c726b2885", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df2.shape"]}, {"cell_type": "code", "execution_count": null, "id": "db8c41dd-2c03-4f7f-ac14-e87cdf43656d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "bee0e37b-956d-4813-ac57-3ff8809d4ea3", "metadata": {}, "source": ["### NSA Polygons"]}, {"cell_type": "code", "execution_count": null, "id": "b327df7c-e83d-4417-bd2d-e3bee3a35288", "metadata": {"tags": []}, "outputs": [], "source": ["z_dau_nsa_polygon_sql = f\"\"\"\n", "    SELECT merchant_id, count(distinct userid) as z_dau\n", "    from\n", "    \n", "    (SELECT\n", "        DATE(format_time) AS date_,    \n", "        g.<PERSON><PERSON>,\n", "        cast(latitude as double) as lat,\n", "        cast(longitude as double) as long\n", "    FROM\n", "       zomato.jumbo_derived.users_home_location_base\n", "            AS g\n", "    WHERE\n", "         dt >= date_format(current_date - interval '1' day, '%%Y%%m%%d')\n", "         AND dt < date_format(current_date, '%%Y%%m%%d')\n", "         AND latitude != '' AND longitude != ''\n", "    GROUP BY\n", "        1,2,3,4) z\n", "    LEFT JOIN\n", "    (\n", "        (select a.merchant_id, ST_Difference(a.pol, b.pol) as nsa_pol\n", "        from(\n", "            (select *, ST_GeometryFromText(polygon) as pol \n", "            from interim.upcoming_polygons\n", "            where merchant_id is not null\n", "            and store_type != 'new') a\n", "\n", "            left join (select cast(merchant_id as int) as merchant_id, ST_GeometryFromText(polygon) as pol\n", "                    from ds_etls.ds_live_store_polygons\n", "                    where city_name is not null\n", "                    and type = 'STORE_POLYGON'\n", "                ) b \n", "            on a.merchant_id = b.merchant_id\n", "            )\n", "        where ST_Difference(a.pol, b.pol) is not null)\n", "\n", "        union all\n", "\n", "        (select merchant_id, ST_GeometryFromText(polygon) as nsa_pol \n", "        from interim.upcoming_polygons\n", "        where merchant_id is not null\n", "        and store_type = 'new')\n", "    ) p\n", "    ON ST_WITHIN(ST_POINT(long,lat), nsa_pol)\n", "\n", "    WHERE merchant_id is not null\n", "    GROUP BY 1\n", "\"\"\"\n", "\n", "z_dau_nsa_polygon = pd.read_sql_query(z_dau_nsa_polygon_sql, con)"]}, {"cell_type": "code", "execution_count": null, "id": "69785437-3e8e-4242-a552-b059ba51a797", "metadata": {}, "outputs": [], "source": ["z_dau_nsa_polygon"]}, {"cell_type": "code", "execution_count": null, "id": "2776bea7-616a-4af8-946b-6ffc12834ae1", "metadata": {}, "outputs": [], "source": ["z_dau_conv_sql = \"\"\"WITH store_live as (\n", "    Select b.city_name,cast(a.frontend_merchant_id as varchar) as frontend_merchant_id, \n", "    min(order_create_dt_ist) as start_date\n", "    from dwh.fact_sales_order_details a\n", "    join dwh.dim_merchant b on a.frontend_merchant_id = b.merchant_id and order_create_dt_ist between valid_from_ts_ist and valid_to_ts_ist\n", "    where order_type in ('RetailForwardOrder', 'DropShippingForwardOrder')\n", "    and order_create_dt_ist is not null\n", "    group by 1,2\n", "),\n", "\n", "zomato_users AS (\n", "    SELECT\n", "        DATE(format_time) AS date_,    \n", "        g.<PERSON><PERSON>,\n", "        cast(latitude as double) as lat,\n", "        cast(longitude as double) as long\n", "    FROM\n", "       zomato.jumbo_derived.users_home_location_base\n", "            AS g\n", "    WHERE\n", "         dt >= date_format(current_date - interval '30' day, '%Y%m%d')\n", "         AND latitude != '' AND longitude != ''\n", "    GROUP BY\n", "        1,2,3,4\n", "),\n", "\n", "ds_polygon as (\n", "select city_name, merchant_id, start_date, update_ts, pol, date, rank() over (partition by merchant_id, date order by update_ts desc) as rnk\n", "    from(\n", "        (select date\n", "        from dwh.dim_date\n", "        where date >= current_date-interval '6' month\n", "        and date < current_date\n", "        group by 1) a\n", "        left join\n", "        (Select city_name, merchant_id, start_date, update_ts, ST_GeometryFromText(polygon) as pol\n", "        from serviceability.ser_polygon_refresh_logs a\n", "        JOIN store_live b on a.merchant_id = b.frontend_merchant_id and start_date >= current_date-interval '6' month) b\n", "        ON a.date >= b.update_ts\n", "    )\n", "    where update_ts >= start_date\n", "    and date_diff('day', start_date, date) between 0 and 7\n", ")\n", "\n", "select city_name, date_diff('day', start_date, date_) as nth_day,\n", "count(distinct userid) as z_dau,\n", "count(distinct dim_customer_key)*100.0/count(distinct userid) as z_dau_conv\n", "from\n", "    (select \n", "    dp.city_name, \n", "    g.date_, g.userid,\n", "    start_date, od.dim_customer_key\n", "    FROM zomato_users AS g\n", "    INNER JOIN\n", "            zomato.jumbo_derived.z_b_user_id_mapping\n", "                AS p\n", "                ON z_user_id = g.userid AND p.dt = DATE_FORMAT(CURRENT_DATE - INTERVAL '1' DAY, '%Y%m%d')\n", "    INNER JOIN\n", "            ds_polygon dp on ST_WITHIN(ST_POINT(long,lat), pol) and g.date_ = dp.date\n", "    LEFT JOIN\n", "          dwh.fact_sales_order_details od on p.b_user_id = cast(od.dim_customer_key as varchar)\n", "                and od.order_create_dt_ist = g.date_\n", "     WHERE g.date_ >= start_date - interval '7' day \n", "    group by 1,2,3,4,5)\n", "group by 1,2\n", "\"\"\"\n", "\n", "z_dau_conv = pd.read_sql_query(z_dau_conv_sql, con)\n", "\n", "# z_dau_conv = pd.read_csv('Z_DAU_conversion_2024_06_04.csv')\n", "z_dau_conv.rename(columns={\"conv\": \"z_dau_conv\"}, inplace=True)\n", "\n", "z_dau_conv1 = (\n", "    z_dau_conv[z_dau_conv.nth_day == 1]\n", "    .groupby([\"city_name\"])[\"z_dau_conv\"]\n", "    .mean()\n", "    .reset_index()\n", ")\n", "z_dau_conv1[\"z_dau_conv\"] = np.ceil(z_dau_conv1[\"z_dau_conv\"]) / 100\n", "\n", "z_dau_conv1"]}, {"cell_type": "code", "execution_count": null, "id": "5afbc6ed-bf8a-4722-84c2-425fc411a3be", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "017d57f0-b0ed-40ac-a639-525d8bde5a8d", "metadata": {}, "outputs": [], "source": ["b_nsa_pings_polygon_sql = f\"\"\"\n", "SELECT merchant_id, ceil(avg(b_nsa_pings)) as b_nsa_pings\n", "from(\n", "    SELECT merchant_id, at_date_ist, count(distinct device_uuid) as b_nsa_pings\n", "    from\n", "    (select at_date_ist, device_uuid, latitude as lat, longitude as long\n", "    from ds_etls.nsa_pings_raw\n", "    where at_date_ist >= cast((current_date - interval '7' day) as varchar)\n", "    and at_date_ist < cast(current_date as varchar)\n", "    group by 1,2,3,4) z\n", "    LEFT JOIN\n", "    (\n", "        (select a.merchant_id, ST_Difference(a.pol, b.pol) as nsa_pol\n", "        from(\n", "            (select *, ST_GeometryFromText(polygon) as pol \n", "            from interim.upcoming_polygons\n", "            where merchant_id is not null\n", "            and store_type != 'new') a\n", "\n", "            left join (select cast(merchant_id as int) as merchant_id, ST_GeometryFromText(polygon) as pol\n", "                    from ds_etls.ds_live_store_polygons\n", "                    where city_name is not null\n", "                    and type = 'STORE_POLYGON'\n", "                ) b \n", "            on a.merchant_id = b.merchant_id\n", "            )\n", "        where ST_Difference(a.pol, b.pol) is not null)\n", "\n", "        union all\n", "\n", "        (select merchant_id, ST_GeometryFromText(polygon) as nsa_pol \n", "        from interim.upcoming_polygons\n", "        where merchant_id is not null\n", "        and store_type = 'new')\n", "    ) p\n", "    ON ST_WITHIN(ST_POINT(long,lat), nsa_pol)\n", "\n", "    WHERE merchant_id is not null\n", "    GROUP BY 1,2\n", ")\n", "GROUP BY 1\n", "\"\"\"\n", "\n", "b_nsa_pings_polygon = pd.read_sql_query(b_nsa_pings_polygon_sql, con)"]}, {"cell_type": "code", "execution_count": null, "id": "c89ce821-32af-4d2b-9626-2645630ee7c1", "metadata": {}, "outputs": [], "source": ["b_nsa_pings_polygon"]}, {"cell_type": "code", "execution_count": null, "id": "fdba77c5-7534-4b0f-8a9c-80dcf9d8ee6c", "metadata": {}, "outputs": [], "source": ["b_dau_nsa_polygon_sql = f\"\"\"\n", "    SELECT merchant_id, count(distinct device_id) as b_dau\n", "    from\n", "    (select at_date_ist, device_id, lat1 as lat, long1 as long\n", "    from consumer_etls.daily_userid_nrl_dau_hex9\n", "    where at_date_ist = current_date - interval '1' day\n", "    group by 1,2,3,4) z\n", "    LEFT JOIN\n", "    (\n", "        (select a.merchant_id, ST_Difference(a.pol, b.pol) as nsa_pol\n", "        from(\n", "            (select *, ST_GeometryFromText(polygon) as pol \n", "            from interim.upcoming_polygons\n", "            where merchant_id is not null\n", "            and store_type != 'new') a\n", "\n", "            left join (select cast(merchant_id as int) as merchant_id, ST_GeometryFromText(polygon) as pol\n", "                    from ds_etls.ds_live_store_polygons\n", "                    where city_name is not null\n", "                    and type = 'STORE_POLYGON'\n", "                ) b \n", "            on a.merchant_id = b.merchant_id\n", "            )\n", "        where ST_Difference(a.pol, b.pol) is not null)\n", "\n", "        union all\n", "\n", "        (select merchant_id, ST_GeometryFromText(polygon) as nsa_pol \n", "        from interim.upcoming_polygons\n", "        where merchant_id is not null\n", "        and store_type = 'new')\n", "    ) p\n", "    ON ST_WITHIN(ST_POINT(long,lat), nsa_pol)\n", "\n", "    WHERE merchant_id is not null\n", "    GROUP BY 1\n", "\"\"\"\n", "\n", "b_dau_nsa_polygon = pd.read_sql_query(b_dau_nsa_polygon_sql, con)"]}, {"cell_type": "code", "execution_count": null, "id": "3cc879be-544d-448d-ba77-4ad135cdac80", "metadata": {}, "outputs": [], "source": ["b_dau_nsa_polygon"]}, {"cell_type": "code", "execution_count": null, "id": "b5bbd480-bf0a-435b-ad6c-0ba5c5cfaf17", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cd50d570-a8ea-4dac-ab71-a39088a52c85", "metadata": {}, "outputs": [], "source": ["nsa_base_df = pd.merge(\n", "    pd.merge(z_dau_nsa_polygon, b_nsa_pings_polygon, how=\"outer\", on=[\"merchant_id\"]),\n", "    b_dau_nsa_polygon,\n", "    how=\"outer\",\n", "    on=[\"merchant_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4fa33854-903d-4693-bc2f-0176570c5119", "metadata": {}, "outputs": [], "source": ["nsa_base_df = nsa_base_df.merge(outlet_merchant_mapping, how=\"left\", on=[\"merchant_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "8ea7b5dc-5582-416d-80ce-fc8ea2131405", "metadata": {}, "outputs": [], "source": ["nsa_base_df.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "c30f0781-269b-4140-8e50-3e0b3230c3b8", "metadata": {}, "outputs": [], "source": ["nsa_base_df = nsa_base_df.merge(\n", "    polygon_change_input[[\"merchant_id\", \"date_live_from\"]],\n", "    how=\"left\",\n", "    on=[\"merchant_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8833c52d-0424-45c0-835c-99320baf7862", "metadata": {}, "outputs": [], "source": ["nsa_base_df"]}, {"cell_type": "code", "execution_count": null, "id": "387e15eb-1250-410e-9072-d979ffc041f2", "metadata": {}, "outputs": [], "source": ["nsa_base_df1 = nsa_base_df.merge(\n", "    z_dau_conv1[[\"city_name\", \"z_dau_conv\"]].drop_duplicates(),\n", "    how=\"left\",\n", "    on=[\"city_name\"],\n", ")\n", "nsa_base_df1[\"z_dau_conv\"].fillna(\n", "    np.round(z_dau_conv1[\"z_dau_conv\"].mean(), 2), inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a0721e00-cfa3-4865-89c2-09829ba7553d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "56ae8d66-b513-422c-9212-6c1f2f48b12a", "metadata": {}, "outputs": [], "source": ["nsa_base_df1[\"b_orders\"] = np.maximum(\n", "    np.ceil(nsa_base_df1[\"z_dau\"] * nsa_base_df1[\"z_dau_conv\"]),\n", "    np.ceil((nsa_base_df1[\"b_nsa_pings\"] + nsa_base_df1[\"b_dau\"]) * 0.2),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "15a7040d-9a80-4fef-81ee-7f23e4375eb8", "metadata": {}, "outputs": [], "source": ["nsa_base_df1"]}, {"cell_type": "code", "execution_count": null, "id": "ef76ed47-294d-4a36-8bb1-63800515af2b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8ec31c53-59e4-429d-955a-b888f74aa7d7", "metadata": {}, "outputs": [], "source": ["first_live_date = nsa_base_df1[\"date_live_from\"].min()\n", "if str(first_live_date) == \"NaT\":\n", "    first_live_date = ov_end_date\n", "first_live_date"]}, {"cell_type": "code", "execution_count": null, "id": "d82d854e-a08d-4af7-b88d-ac7546acef8b", "metadata": {}, "outputs": [], "source": ["date_range2 = pd.date_range(\n", "    start=pd.to_datetime(first_live_date), end=pd.to_datetime(ov_end_date), freq=\"D\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1f4d2266-5491-4a0d-ad39-8412874329db", "metadata": {}, "outputs": [], "source": ["base_df_nsa_stores = pd.DataFrame(date_range1, columns=[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "9930b300-cf9a-46db-9926-75b1616441f2", "metadata": {}, "outputs": [], "source": ["base_df_nsa_stores[\"day\"] = base_df_nsa_stores[\"date_\"].dt.strftime(\"%A\")"]}, {"cell_type": "code", "execution_count": null, "id": "32a79fb5-0016-47fa-8527-48403a55cc92", "metadata": {}, "outputs": [], "source": ["base_df_nsa_stores = base_df_nsa_stores.merge(hfs_df, on=[\"date_\"], how=\"left\")\n", "base_df_nsa_stores[\"hfs_tag\"].fillna(0, inplace=True)\n", "base_df_nsa_stores[\"hfs_day1\"].fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "790314a4-4e84-4d5d-a271-29746e479919", "metadata": {}, "outputs": [], "source": ["nsa_stores_base_df = pd.merge(\n", "    nsa_base_df1[\n", "        [\"city_name\", \"outlet_id\", \"outlet_name\", \"date_live_from\", \"b_orders\"]\n", "    ].drop_duplicates(),\n", "    base_df_nsa_stores,\n", "    how=\"cross\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fa0f6eaa-7715-4c0d-87ba-585345390288", "metadata": {}, "outputs": [], "source": ["nsa_stores_base_df = nsa_stores_base_df[\n", "    nsa_stores_base_df[\"date_\"] >= nsa_stores_base_df[\"date_live_from\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "5175c648-72ea-4cdc-9adb-0df1fa873b05", "metadata": {}, "outputs": [], "source": ["nsa_stores_base_df[\"b_orders\"] = np.where(\n", "    nsa_stores_base_df[\"date_\"] == nsa_stores_base_df[\"date_live_from\"],\n", "    nsa_stores_base_df[\"b_orders\"],\n", "    0,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d748472a-a805-4b8a-9b0b-0a2993051028", "metadata": {}, "outputs": [], "source": ["nsa_stores_base_df[\"nth_week\"] = (\n", "    pd.to_datetime(nsa_stores_base_df[\"date_\"])\n", "    - pd.to_datetime(nsa_stores_base_df[\"date_live_from\"])\n", ").dt.days // 7"]}, {"cell_type": "code", "execution_count": null, "id": "7d9b386a-fa87-4d19-ada2-5c35e5acb037", "metadata": {}, "outputs": [], "source": ["nsa_stores_base_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "32e3bba4-bbc7-480a-86a0-5d866378d9f1", "metadata": {}, "outputs": [], "source": ["nsa_store_city_trend = pd.read_csv(cwd + \"/nsa_store_city_trend.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "340427ca-9f62-467f-95a1-0b1cb7057503", "metadata": {}, "outputs": [], "source": ["nsa_stores_base_df1 = nsa_stores_base_df.merge(\n", "    nsa_store_city_trend[[\"city_name\", \"nth_week\", \"day\", \"dod_change\"]],\n", "    how=\"left\",\n", "    on=[\"city_name\", \"nth_week\", \"day\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "35ddafd9-66d1-454d-8b16-ecd341bcf88f", "metadata": {}, "outputs": [], "source": ["for i in range(0, len(nsa_stores_base_df1)):\n", "\n", "    if nsa_stores_base_df1.loc[i, \"b_orders\"] == 0:\n", "        if nsa_stores_base_df1.loc[i, \"nth_week\"] == 0:\n", "            nsa_stores_base_df1.loc[i, \"b_orders\"] = np.ceil(\n", "                nsa_stores_base_df1.loc[i - 1, \"b_orders\"] * (1.01)\n", "            ).astype(int)\n", "        else:\n", "            nsa_stores_base_df1.loc[i, \"b_orders\"] = np.ceil(\n", "                nsa_stores_base_df1.loc[i - 1, \"b_orders\"]\n", "                * (1 + nsa_stores_base_df1.loc[i, \"dod_change\"])\n", "            ).astype(int)\n", "\n", "        # recently_opened_stores_base_df1.loc[i, \"aov\"] = np.round(recently_opened_stores_base_df1.loc[i-1, \"aov\"]*recently_opened_stores_base_df1.loc[i, \"aov_dod_change\"],0).astype(int)\n", "        # recently_opened_stores_base_df1.loc[i, \"ipc\"] = np.round(recently_opened_stores_base_df1.loc[i-1, \"ipc\"]*recently_opened_stores_base_df1.loc[i, \"ipc_dod_change\"],0).astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "bdbc136b-adc5-42ab-8ff7-6fe1e4e3daaf", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "43de81c9-eb40-4798-862a-4e464671d2ee", "metadata": {}, "outputs": [], "source": ["combined_df = existing_store_df3[\n", "    [\n", "        \"city_name\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"date_\",\n", "        \"day\",\n", "        \"hfs_tag\",\n", "        \"store_type\",\n", "        \"orders\",\n", "        \"dod_change\",\n", "        \"ipc\",\n", "        \"aov\",\n", "    ]\n", "].append(\n", "    recently_opened_stores_base_df2[\n", "        [\n", "            \"city_name\",\n", "            \"outlet_id\",\n", "            \"outlet_name\",\n", "            \"date_\",\n", "            \"day\",\n", "            \"hfs_tag\",\n", "            \"store_type\",\n", "            \"orders\",\n", "            \"dod_change\",\n", "            \"ipc\",\n", "            \"aov\",\n", "        ]\n", "    ]\n", ")\n", "# .append(\n", "# upcoming_buffer_stores_base_df3[[\"city_name\",\"outlet_id\",\"outlet_name\",\"date_\",\"day\",\"hfs_tag\",\"store_type\",\"orders\",\"dod_change\",\"ipc\",\"aov\"]]\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "54e6865f-8a04-43ce-85a2-70a2e04c8ea2", "metadata": {}, "outputs": [], "source": ["combined_df[\"gmv\"] = np.round(combined_df[\"orders\"] * combined_df[\"aov\"], 0)"]}, {"cell_type": "code", "execution_count": null, "id": "eddd2e55-993f-4706-a718-10c01e60e18a", "metadata": {}, "outputs": [], "source": ["combined_df[\"outlet_id\"] = combined_df[\"outlet_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "207e0bd2-b0de-456a-9b43-f41041b0d124", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "50a8e3d2-9353-4f1e-a5fd-344fff5f063b", "metadata": {}, "outputs": [], "source": ["pure_nsa_stores = list(\n", "    set(nsa_stores_base_df1[\"outlet_id\"].unique())\n", "    - set(combined_df[\"outlet_id\"].unique())\n", ")\n", "pure_nsa_stores"]}, {"cell_type": "code", "execution_count": null, "id": "c809cc68-4119-4134-af4f-e632b2feacb6", "metadata": {}, "outputs": [], "source": ["combined_df = combined_df.merge(\n", "    nsa_stores_base_df1[[\"outlet_id\", \"date_\", \"b_orders\"]],\n", "    how=\"left\",\n", "    on=[\"outlet_id\", \"date_\"],\n", ")\n", "combined_df[\"b_orders\"].fillna(0, inplace=True)\n", "\n", "combined_df[\"orders\"] = combined_df[\"orders\"] + combined_df[\"b_orders\"]"]}, {"cell_type": "code", "execution_count": null, "id": "ad5a3baa-06d6-4748-8a37-57573a3e215f", "metadata": {}, "outputs": [], "source": ["combined_df.drop(columns=[\"b_orders\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "3339b35d-bfd8-4f76-ba5e-1e7a0529f69f", "metadata": {}, "outputs": [], "source": ["combined_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "6500e3fa-799d-4923-89cd-dfb48f928406", "metadata": {}, "outputs": [], "source": ["nsa_stores_base_df1[\"store_type\"] = \"nsa\"\n", "nsa_stores_base_df1[\"orders\"] = nsa_stores_base_df1[\"b_orders\"]\n", "nsa_stores_base_df1[\"ipc\"] = 0\n", "nsa_stores_base_df1[\"aov\"] = 0\n", "nsa_stores_base_df1[\"gmv\"] = 0"]}, {"cell_type": "code", "execution_count": null, "id": "2683122d-fab6-4cef-a9a4-b15748f9781a", "metadata": {}, "outputs": [], "source": ["combined_df = combined_df.append(\n", "    nsa_stores_base_df1[nsa_stores_base_df1[\"outlet_id\"].isin(pure_nsa_stores)][\n", "        [\n", "            \"city_name\",\n", "            \"outlet_id\",\n", "            \"outlet_name\",\n", "            \"date_\",\n", "            \"day\",\n", "            \"hfs_tag\",\n", "            \"store_type\",\n", "            \"orders\",\n", "            \"dod_change\",\n", "            \"ipc\",\n", "            \"aov\",\n", "            \"gmv\",\n", "        ]\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d927ca18-18f0-4801-a761-35be9d298770", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "24e28455-61a5-449b-ab20-f2504716e134", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b213d4a3-0ed3-40c0-a4ed-a67f7701f977", "metadata": {}, "outputs": [], "source": ["all_stores = combined_df[[\"city_name\", \"outlet_id\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "67976a7b-ac07-4945-a488-7b53b2e443e4", "metadata": {}, "outputs": [], "source": ["all_stores1 = all_stores.copy()\n", "all_stores1[\"city_name\"] = \"All\"\n", "\n", "all_stores = all_stores.append(all_stores1)"]}, {"cell_type": "code", "execution_count": null, "id": "51f83c28-46a2-4e8d-bf89-84252738efcb", "metadata": {}, "outputs": [], "source": ["all_stores[\"outlet_check\"] = \"All\""]}, {"cell_type": "code", "execution_count": null, "id": "96b1a7dd-f36c-4df7-a4fd-a1343f41675d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ecf89263-f0f3-469e-8ad7-c8d79fe3b808", "metadata": {}, "outputs": [], "source": ["bump_input = pb.from_sheets(\n", "    \"1lepKjqGE5dKrp5D_0orbl4BFVv1RkaA2SZKDouDSy3k\", \"Manual Bump Input\"\n", ")\n", "\n", "# bump_input = pd.read_csv('Manual Bump Input.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "8756ac75-59e7-4fc8-9588-eda51b63b50c", "metadata": {}, "outputs": [], "source": ["bump_input[\"outlet_check\"] = bump_input[\"outlet_id\"]"]}, {"cell_type": "code", "execution_count": null, "id": "086be6b2-eb7f-4331-8e3c-57184dbe73f7", "metadata": {}, "outputs": [], "source": ["bump_input1 = bump_input.merge(all_stores, how=\"left\", on=[\"city_name\", \"outlet_check\"])"]}, {"cell_type": "code", "execution_count": null, "id": "9062e52e-a8c9-447b-bbd8-68969bd2fe48", "metadata": {}, "outputs": [], "source": ["bump_input1[\"outlet_id\"] = np.where(\n", "    bump_input1[\"outlet_id_y\"].isna(),\n", "    bump_input1[\"outlet_id_x\"],\n", "    bump_input1[\"outlet_id_y\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "acf0b2ee-ce8b-480d-bb97-c36eeaab9487", "metadata": {}, "outputs": [], "source": ["bump_input1.drop(columns=[\"outlet_id_x\", \"outlet_id_y\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "716b6faa-5c40-4035-a81e-582f7118b846", "metadata": {}, "outputs": [], "source": ["bump_input1[\"date_\"] = pd.to_datetime(bump_input1[\"date_\"])\n", "bump_input1[\"outlet_id\"] = bump_input1[\"outlet_id\"].astype(int)\n", "bump_input1[\"bump_percentage\"] = bump_input1[\"bump_percentage\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "66e29c03-3fc0-4d03-beaa-1cf56c4bfa10", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2414be6b-c10c-4ee6-b458-a884ffd604bf", "metadata": {}, "outputs": [], "source": ["combined_df = combined_df.merge(\n", "    bump_input1[[\"outlet_id\", \"date_\", \"bump_percentage\"]],\n", "    how=\"left\",\n", "    on=[\"date_\", \"outlet_id\"],\n", ")\n", "combined_df[\"bump_percentage\"].fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "4540b92e-815e-4038-b6ee-9482546feb9f", "metadata": {}, "outputs": [], "source": ["combined_df[\"bumped_orders\"] = np.round(\n", "    combined_df[\"orders\"] * (1 + combined_df[\"bump_percentage\"]), 0\n", ")\n", "combined_df[\"bumped_gmv\"] = np.round(\n", "    combined_df[\"gmv\"] * (1 + combined_df[\"bump_percentage\"]), 0\n", ")\n", "# combined_df[\"bumped_aov\"] = np.round(combined_df[\"aov\"]*(1+combined_df[\"bump_percentage\"]),0)"]}, {"cell_type": "code", "execution_count": null, "id": "494f155b-61b0-4070-8e7e-45c300fb7a20", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "654e1b29-0a55-405e-a954-00d525ce47f0", "metadata": {}, "outputs": [], "source": ["combined_df[\"bumped_orders\"] = np.round(combined_df[\"bumped_orders\"], 0)\n", "combined_df[\"bumped_gmv\"] = np.round(combined_df[\"bumped_gmv\"], 0)\n", "combined_df[\"aov\"] = np.round(combined_df[\"aov\"], 0)\n", "combined_df[\"ipc\"] = np.round(combined_df[\"ipc\"], 2)"]}, {"cell_type": "code", "execution_count": null, "id": "d5435bfd-00d9-4adf-9539-8ef10b844978", "metadata": {}, "outputs": [], "source": ["ov_combined_df = combined_df.copy()\n", "ov_combined_df[\"city_name\"] = \"Total Forecasted\"\n", "ov_combined_df[\"outlet_id\"] = -1\n", "ov_combined_df[\"outlet_name\"] = \"Overall\"\n", "ov_combined_df = (\n", "    ov_combined_df.groupby([\"city_name\", \"outlet_id\", \"outlet_name\", \"date_\"])\n", "    .agg({\"bumped_orders\": \"sum\", \"bumped_gmv\": \"sum\", \"aov\": \"mean\", \"ipc\": \"mean\"})\n", "    .reset_index()\n", ")\n", "\n", "ov_combined_df[\"bumped_orders\"] = np.round(ov_combined_df[\"bumped_orders\"], 0)\n", "ov_combined_df[\"bumped_gmv\"] = np.round(ov_combined_df[\"bumped_gmv\"], 0)\n", "ov_combined_df[\"aov\"] = np.round(\n", "    ov_combined_df[\"bumped_gmv\"] / ov_combined_df[\"bumped_orders\"], 0\n", ")\n", "ov_combined_df[\"ipc\"] = np.round(ov_combined_df[\"ipc\"], 2)"]}, {"cell_type": "code", "execution_count": null, "id": "39f3b2df-1a36-404b-9783-8302a256c2f5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3b39c262-211d-44b0-8bae-ed13e6950409", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    combined_df, \"1lepKjqGE5dKrp5D_0orbl4BFVv1RkaA2SZKDouDSy3k\", \"qtr_final_raw_amj\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5e66c0d5-3bcb-4a4c-9211-ae5fecd65ec2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1cf4ab6e-b326-4674-96ef-e81c13ed8f53", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a5a834dc-88c8-44e1-8ac1-3c91320d0461", "metadata": {}, "outputs": [], "source": ["ov_actual_df = dt_actual_orders[dt_actual_orders.date_ > train_end_date].copy()\n", "ov_actual_df[\"city_name\"] = \"Total Actual\"\n", "ov_actual_df[\"outlet_id\"] = -1\n", "ov_actual_df[\"outlet_name\"] = \"Overall\"\n", "ov_actual_df[\"gmv\"] = np.round(ov_actual_df[\"orders\"] * ov_actual_df[\"aov\"], 0)\n", "ov_actual_df = (\n", "    ov_actual_df.groupby([\"city_name\", \"outlet_id\", \"outlet_name\", \"date_\"])\n", "    .agg({\"orders\": \"sum\", \"gmv\": \"sum\", \"aov\": \"mean\", \"ipc\": \"mean\"})\n", "    .reset_index()\n", ")\n", "\n", "ov_actual_df[\"bumped_orders\"] = np.round(ov_actual_df[\"orders\"], 0)\n", "ov_actual_df[\"bumped_gmv\"] = np.round(ov_actual_df[\"gmv\"], 0)\n", "ov_actual_df[\"aov\"] = np.round(ov_actual_df[\"aov\"], 0)\n", "ov_actual_df[\"ipc\"] = np.round(ov_actual_df[\"ipc\"], 2)"]}, {"cell_type": "code", "execution_count": null, "id": "3873510b-098b-4491-95e5-e4d7d92947b9", "metadata": {}, "outputs": [], "source": ["df = (\n", "    combined_df.groupby([\"city_name\", \"outlet_id\", \"outlet_name\"])[\"date_\"]\n", "    .count()\n", "    .reset_index()\n", ")\n", "df[df[\"date_\"] > 90]"]}, {"cell_type": "code", "execution_count": null, "id": "5f8e62dd-b061-4f5e-9cb8-eb87dca2b585", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1763f3a2-dbac-4985-9342-fd2cb9d90b9c", "metadata": {}, "outputs": [], "source": ["pivot_df = combined_df.pivot(\n", "    index=[\"city_name\", \"outlet_id\", \"outlet_name\"],\n", "    columns=[\"date_\"],\n", "    values=[\"bumped_orders\"],\n", ").reset_index()\n", "ov_pivot_df = ov_combined_df.pivot(\n", "    index=[\"city_name\", \"outlet_id\", \"outlet_name\"],\n", "    columns=[\"date_\"],\n", "    values=[\"bumped_orders\"],\n", ").reset_index()\n", "ov_actual_pivot_df = ov_actual_df.pivot(\n", "    index=[\"city_name\", \"outlet_id\", \"outlet_name\"],\n", "    columns=[\"date_\"],\n", "    values=[\"bumped_orders\"],\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "caccc717-7e86-47d2-97b7-03569fa736d4", "metadata": {}, "outputs": [], "source": ["pivot_df.columns = [\"city_name\", \"outlet_id\", \"outlet_name\"] + list(\n", "    combined_df[\"date_\"].astype(str).unique()\n", ")\n", "ov_pivot_df.columns = [\"city_name\", \"outlet_id\", \"outlet_name\"] + list(\n", "    combined_df[\"date_\"].astype(str).unique()\n", ")\n", "ov_actual_pivot_df.columns = [\"city_name\", \"outlet_id\", \"outlet_name\"] + list(\n", "    ov_actual_df[\"date_\"].astype(str).unique()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4b34c7b2-bedc-4a5f-afee-c99a1e6768cd", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    ov_pivot_df.append(ov_actual_pivot_df).append(pivot_df),\n", "    \"1ZTOxa1JzzgmHL7_XfFfSj3UGnusmzhE-lvHo9FDLuNc\",\n", "    \"Order Projection - polygon input\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "dc2be9b7-9340-4ef4-9c5d-5211e4f677c7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fea98129-b2e1-4be1-9b7f-200e44f2b319", "metadata": {}, "outputs": [], "source": ["gmv_pivot_df = combined_df.pivot(\n", "    index=[\"city_name\", \"outlet_id\", \"outlet_name\"],\n", "    columns=[\"date_\"],\n", "    values=[\"bumped_gmv\"],\n", ").reset_index()\n", "ov_gmv_pivot_df = ov_combined_df.pivot(\n", "    index=[\"city_name\", \"outlet_id\", \"outlet_name\"],\n", "    columns=[\"date_\"],\n", "    values=[\"bumped_gmv\"],\n", ").reset_index()\n", "ov_actual_gmv_pivot_df = ov_actual_df.pivot(\n", "    index=[\"city_name\", \"outlet_id\", \"outlet_name\"],\n", "    columns=[\"date_\"],\n", "    values=[\"bumped_gmv\"],\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "ffc6a77c-b05d-4aba-a9dd-6e151c45652e", "metadata": {}, "outputs": [], "source": ["gmv_pivot_df.columns = [\"city_name\", \"outlet_id\", \"outlet_name\"] + list(\n", "    combined_df[\"date_\"].astype(str).unique()\n", ")\n", "ov_gmv_pivot_df.columns = [\"city_name\", \"outlet_id\", \"outlet_name\"] + list(\n", "    combined_df[\"date_\"].astype(str).unique()\n", ")\n", "ov_actual_gmv_pivot_df.columns = [\"city_name\", \"outlet_id\", \"outlet_name\"] + list(\n", "    ov_actual_df[\"date_\"].astype(str).unique()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "077f0aad-1290-43b5-827f-c41135fe16d5", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(ov_gmv_pivot_df.append(ov_actual_gmv_pivot_df).append(gmv_pivot_df), \"1ZTOxa1JzzgmHL7_XfFfSj3UGnusmzhE-lvHo9FDLuNc\", \"GMV Projection\")"]}, {"cell_type": "code", "execution_count": null, "id": "a75f984a-682a-47f0-b1e1-12e176ac270b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "69ebeafc-05b3-49f1-bab9-77b8263b2c25", "metadata": {}, "outputs": [], "source": ["aov_pivot_df = combined_df.pivot(\n", "    index=[\"city_name\", \"outlet_id\", \"outlet_name\"], columns=[\"date_\"], values=[\"aov\"]\n", ").reset_index()\n", "ov_aov_pivot_df = ov_combined_df.pivot(\n", "    index=[\"city_name\", \"outlet_id\", \"outlet_name\"], columns=[\"date_\"], values=[\"aov\"]\n", ").reset_index()\n", "ov_actual_aov_pivot_df = ov_actual_df.pivot(\n", "    index=[\"city_name\", \"outlet_id\", \"outlet_name\"], columns=[\"date_\"], values=[\"aov\"]\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "9db4f701-3050-414b-8e9c-5b6630ebf51b", "metadata": {}, "outputs": [], "source": ["aov_pivot_df.columns = [\"city_name\", \"outlet_id\", \"outlet_name\"] + list(\n", "    combined_df[\"date_\"].astype(str).unique()\n", ")\n", "ov_aov_pivot_df.columns = [\"city_name\", \"outlet_id\", \"outlet_name\"] + list(\n", "    combined_df[\"date_\"].astype(str).unique()\n", ")\n", "ov_actual_aov_pivot_df.columns = [\"city_name\", \"outlet_id\", \"outlet_name\"] + list(\n", "    ov_actual_df[\"date_\"].astype(str).unique()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3da4fd00-d4ff-4484-a9b6-2e7fc771bc86", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(ov_aov_pivot_df.append(ov_actual_aov_pivot_df).append(aov_pivot_df), \"1ZTOxa1JzzgmHL7_XfFfSj3UGnusmzhE-lvHo9FDLuNc\", \"AOV Projection\")"]}, {"cell_type": "code", "execution_count": null, "id": "d621a82c-f6f3-4aad-92a8-f1e63b92efa8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0f5c1fbe-c161-4ab1-8f6f-1e0ca08ee0c6", "metadata": {}, "outputs": [], "source": ["ipc_pivot_df = combined_df.pivot(\n", "    index=[\"city_name\", \"outlet_id\", \"outlet_name\"], columns=[\"date_\"], values=[\"ipc\"]\n", ").reset_index()\n", "ov_ipc_pivot_df = ov_combined_df.pivot(\n", "    index=[\"city_name\", \"outlet_id\", \"outlet_name\"], columns=[\"date_\"], values=[\"ipc\"]\n", ").reset_index()\n", "ov_actual_ipc_pivot_df = ov_actual_df.pivot(\n", "    index=[\"city_name\", \"outlet_id\", \"outlet_name\"], columns=[\"date_\"], values=[\"ipc\"]\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "48810a43-e2f6-4b7c-8cf7-9c2ed706e024", "metadata": {}, "outputs": [], "source": ["ipc_pivot_df.columns = [\"city_name\", \"outlet_id\", \"outlet_name\"] + list(\n", "    combined_df[\"date_\"].astype(str).unique()\n", ")\n", "ov_ipc_pivot_df.columns = [\"city_name\", \"outlet_id\", \"outlet_name\"] + list(\n", "    combined_df[\"date_\"].astype(str).unique()\n", ")\n", "ov_actual_ipc_pivot_df.columns = [\"city_name\", \"outlet_id\", \"outlet_name\"] + list(\n", "    ov_actual_df[\"date_\"].astype(str).unique()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "cc831f43-e9dd-4b0c-b726-e3d19d678b68", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(ov_ipc_pivot_df.append(ov_actual_ipc_pivot_df).append(ipc_pivot_df), \"1ZTOxa1JzzgmHL7_XfFfSj3UGnusmzhE-lvHo9FDLuNc\", \"IPC Projection\")"]}, {"cell_type": "code", "execution_count": null, "id": "38707079-1409-405c-bb0c-e0a44033adb4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3ec72452-0ebe-4b41-8377-75d7714a6dfa", "metadata": {"tags": []}, "outputs": [], "source": ["current_logic = pd.read_sql_query(\n", "    f\"\"\"select * from(\n", "        select outletid, date, carts, aov, ipc, rank() over (partition by outletid, date order by updated_on desc) as rnk\n", "        from logistics_data_etls.cart_projections\n", "        where date >= cast('{train_end_date}' as date) + interval '1' day\n", "        and date < cast('{ov_end_date}' as date) + interval '1' day\n", "        )\n", "        where rnk=1\"\"\",\n", "    con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7f2f21e5-d749-49ea-b628-5a6b1dd1db2a", "metadata": {}, "outputs": [], "source": ["current_logic.shape"]}, {"cell_type": "code", "execution_count": null, "id": "d04fda74-5983-45bf-ac05-56ca731348fb", "metadata": {}, "outputs": [], "source": ["current_logic_old_stores = current_logic[\n", "    current_logic[\"outletid\"].isin(list(existing_store_df3[\"outlet_id\"].unique()))\n", "]\n", "current_logic_old_stores[\"store_type\"] = \"old\""]}, {"cell_type": "code", "execution_count": null, "id": "d6dc7390-9649-4532-b8f6-98ce1e754bbf", "metadata": {}, "outputs": [], "source": ["recently_opened_stores_base_df[\"outlet_id\"] = recently_opened_stores_base_df[\n", "    \"outlet_id\"\n", "].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "a6b65615-daf2-41dd-813c-6902c6ba83a6", "metadata": {}, "outputs": [], "source": ["current_logic_recently_opened_stores = current_logic[\n", "    current_logic[\"outletid\"].isin(\n", "        list(combined_df[combined_df[\"store_type\"] == \"new\"][\"outlet_id\"].unique())\n", "    )\n", "]\n", "current_logic_recently_opened_stores[\"store_type\"] = \"new\""]}, {"cell_type": "code", "execution_count": null, "id": "db76298a-a21f-4c20-8547-fe1c85811ea8", "metadata": {}, "outputs": [], "source": ["combined_df[combined_df[\"store_type\"] == \"new\"][\n", "    \"outlet_id\"\n", "].nunique(), current_logic_recently_opened_stores[\"outletid\"].nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "b9414e01-dc54-43b5-bc19-6e71d4839bb9", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    current_logic_old_stores.append(current_logic_recently_opened_stores),\n", "    \"1lepKjqGE5dKrp5D_0orbl4BFVv1RkaA2SZKDouDSy3k\",\n", "    \"current_proj_amj\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "bd650cb5-4711-468a-b060-3662588033e2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "23ce07f3-e739-4f83-92d5-e42056eae063", "metadata": {}, "outputs": [], "source": ["actual_orders_sql = f\"\"\"\n", "\n", "select dm.city_name, date(oid.cart_checkout_ts_ist) as date_, oid.frontend_merchant_id as merchant_id,\n", "m.pos_outlet_id as outlet_id, count(distinct cart_id) as orders, sum(total_selling_price) as gmv,\n", "sum(total_selling_price)*1.00/count(distinct cart_id) as aov, sum(total_product_quantity)*1.00/count(distinct cart_id) as ipc\n", "from dwh.fact_sales_order_details oid\n", "join dwh.dim_merchant dm on oid.frontend_merchant_id = dm.merchant_id and dm.is_current\n", "left join dwh.dim_merchant_outlet_facility_mapping m on oid.frontend_merchant_id = m.frontend_merchant_id and m.is_current and m.is_mapping_enabled and m.is_current_mapping_active\n", "where oid.order_create_dt_ist  > cast('{train_end_date}' as date)\n", "AND oid.order_create_dt_ist < current_date\n", "AND oid.is_internal_order = FALSE\n", "group by 1,2,3,4\n", "\"\"\"\n", "actual_orders = pd.read_sql_query(actual_orders_sql, con)"]}, {"cell_type": "code", "execution_count": null, "id": "45ba77b9-869d-4e44-ac1c-29ed53623a16", "metadata": {}, "outputs": [], "source": ["actual_orders[\"ipc\"] = actual_orders[\"ipc\"].astype(float)\n", "actual_orders[\"date_\"] = pd.to_datetime(actual_orders[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "79eadfb6-0951-407d-931a-b701bd3d394c", "metadata": {}, "outputs": [], "source": ["orders_jfm_old_stores = actual_orders[\n", "    actual_orders[\"outlet_id\"].isin(list(existing_store_df3[\"outlet_id\"].unique()))\n", "]\n", "orders_jfm_old_stores[\"store_type\"] = \"old\""]}, {"cell_type": "code", "execution_count": null, "id": "15564c88-1f1a-4537-958a-073609acd459", "metadata": {}, "outputs": [], "source": ["orders_jfm_recently_opened_stores = actual_orders[\n", "    actual_orders[\"outlet_id\"].isin(\n", "        list(combined_df[combined_df[\"store_type\"] == \"new\"][\"outlet_id\"].unique())\n", "    )\n", "]\n", "orders_jfm_recently_opened_stores[\"store_type\"] = \"new\""]}, {"cell_type": "code", "execution_count": null, "id": "f90bcff3-afcc-452e-a407-d9c909b18736", "metadata": {}, "outputs": [], "source": ["missing_stores = combined_df[\n", "    (combined_df.date_ == \"2024-05-01\")\n", "    & (\n", "        ~combined_df[\"outlet_id\"].isin(\n", "            list(\n", "                actual_orders[actual_orders.date_ == \"2024-05-01\"][\"outlet_id\"].unique()\n", "            )\n", "        )\n", "    )\n", "][\"outlet_id\"].unique()\n", "missing_stores"]}, {"cell_type": "code", "execution_count": null, "id": "ce855d68-03c5-4e6c-a233-16759812048b", "metadata": {}, "outputs": [], "source": ["store_live[store_live.outlet_id.isin([4479, 3579])]"]}, {"cell_type": "code", "execution_count": null, "id": "7045bd86-5f29-47ab-9d6a-6d76bd2d6d32", "metadata": {}, "outputs": [], "source": ["actual_orders[actual_orders.outlet_id == 4479]"]}, {"cell_type": "code", "execution_count": null, "id": "1779ce5c-c8eb-4829-be4e-61cf63e7c299", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    orders_jfm_old_stores.groupby([\"date_\", \"store_type\"])\n", "    .agg({\"orders\": \"sum\", \"gmv\": \"sum\", \"ipc\": \"mean\"})\n", "    .reset_index()\n", "    .append(\n", "        orders_jfm_recently_opened_stores.groupby([\"date_\", \"store_type\"])\n", "        .agg({\"orders\": \"sum\", \"gmv\": \"sum\", \"ipc\": \"sum\"})\n", "        .reset_index()\n", "    ),\n", "    \"1lepKjqGE5dKrp5D_0orbl4BFVv1RkaA2SZKDouDSy3k\",\n", "    \"orders_amj\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "35d3a3f0-d03a-427a-9cbd-76bf50f07160", "metadata": {}, "outputs": [], "source": ["orders_jfm_old_stores[\"merchant_id\"].nunique(), orders_jfm_recently_opened_stores[\n", "    \"merchant_id\"\n", "].nunique(), orders_jfm_old_stores[\"merchant_id\"].append(\n", "    orders_jfm_recently_opened_stores[\"merchant_id\"]\n", ").nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "a299ceed-235f-408a-9142-85fa4fb54243", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "07994296-a1d4-4d2a-9d40-c1878bd16881", "metadata": {}, "outputs": [], "source": ["all_stores_projected = combined_df.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "da30bbe8-aafc-43ea-bbad-2adf900aeb5f", "metadata": {}, "outputs": [], "source": ["all_stores_actual = orders_jfm_old_stores.append(orders_jfm_recently_opened_stores)"]}, {"cell_type": "code", "execution_count": null, "id": "5e3e7c1e-94ad-4af7-8d19-4c1b48d71452", "metadata": {}, "outputs": [], "source": ["all_stores_projected.rename(\n", "    columns={\"bumped_orders\": \"forecasted_orders\"}, inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "67dc74a1-1bcc-46dd-935b-8a5c006e7d30", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0c60ebef-5816-4c35-b32b-d93713d4536b", "metadata": {}, "outputs": [], "source": ["comp = all_stores_projected[\n", "    (all_stores_projected[\"date_\"] < datetime.today().strftime(\"%Y-%m-%d\"))\n", "    & (~all_stores_projected[\"date_\"].isin([\"2024-04-09\", \"2024-04-17\"]))\n", "][\n", "    [\n", "        \"city_name\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"date_\",\n", "        \"store_type\",\n", "        \"forecasted_orders\",\n", "    ]\n", "].merge(\n", "    all_stores_actual[[\"city_name\", \"outlet_id\", \"date_\", \"orders\"]],\n", "    on=[\"city_name\", \"outlet_id\", \"date_\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "266dfe3b-251f-497f-985d-bd4fa1efef75", "metadata": {}, "outputs": [], "source": ["comp1 = all_stores_projected[(all_stores_projected[\"date_\"] == \"2024-05-01\")][\n", "    [\n", "        \"city_name\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"date_\",\n", "        \"store_type\",\n", "        \"forecasted_orders\",\n", "    ]\n", "].merge(\n", "    all_stores_actual[(all_stores_actual[\"date_\"] == \"2024-05-01\")][\n", "        [\"city_name\", \"outlet_id\", \"date_\", \"orders\"]\n", "    ],\n", "    on=[\"city_name\", \"outlet_id\", \"date_\"],\n", "    how=\"right\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a864ddbb-84db-447a-89e9-67b62759241d", "metadata": {}, "outputs": [], "source": ["comp[\"orders\"].fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "bed81cdb-f1b0-451b-840b-a1b05115c4e7", "metadata": {}, "outputs": [], "source": ["comp[\"delta\"] = np.where(\n", "    comp[\"orders\"] > 0, (comp[\"forecasted_orders\"] / comp[\"orders\"] - 1) * 100, 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "32496733-a8ba-43c5-b644-59deafbb2b8d", "metadata": {}, "outputs": [], "source": ["comp[\"week\"] = comp[\"date_\"].dt.strftime(\"%W\")"]}, {"cell_type": "code", "execution_count": null, "id": "22c40aae-a34c-4b3d-bafc-51fe065fee08", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(comp, \"1ZTOxa1JzzgmHL7_XfFfSj3UGnusmzhE-lvHo9FDLuNc\", \"comparison_raw\")"]}, {"cell_type": "code", "execution_count": null, "id": "35a3ce55-bcd1-46a4-8e4a-e29720770185", "metadata": {}, "outputs": [], "source": ["comp[\"month\"] = comp[\"date_\"].dt.strftime(\"%m\")"]}, {"cell_type": "code", "execution_count": null, "id": "ea2ffe09-8d6e-4e40-9ad6-c622cf82dd12", "metadata": {}, "outputs": [], "source": ["# comp[comp.outlet_id==4096]"]}, {"cell_type": "code", "execution_count": null, "id": "58089ca8-70f9-4c3c-9d8b-9c0abae5a344", "metadata": {}, "outputs": [], "source": ["avg_comp = (\n", "    comp.groupby(\n", "        [\n", "            \"city_name\",\n", "            \"outlet_id\",\n", "            \"store_type\",\n", "            \"week\",\n", "        ]\n", "    )\n", "    .agg({\"orders\": \"sum\", \"forecasted_orders\": \"sum\", \"delta\": \"mean\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ccd017c3-4678-4f8a-bc2a-3de5a08a78f6", "metadata": {}, "outputs": [], "source": ["avg_comp[\"delta_bucket\"] = np.where(\n", "    (avg_comp[\"delta\"] >= -5) & (avg_comp[\"delta\"] <= 5),\n", "    \"+-5%\",\n", "    np.where(\n", "        (avg_comp[\"delta\"] >= -10) & (avg_comp[\"delta\"] <= 10),\n", "        \"+-10%\",\n", "        np.where(\n", "            (avg_comp[\"delta\"] >= -20) & (avg_comp[\"delta\"] <= 20), \"+-20%\", \">20%\"\n", "        ),\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "562648b7-7c5b-4ed2-bfa0-e568480bf8cd", "metadata": {}, "outputs": [], "source": ["delta_agg = (\n", "    avg_comp.groupby(\n", "        [\n", "            \"store_type\",\n", "            \"delta_bucket\",\n", "            \"week\",\n", "        ]\n", "    )\n", "    .agg({\"outlet_id\": \"nunique\", \"orders\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f767831d-2f24-4553-a56f-b2a4028cff00", "metadata": {}, "outputs": [], "source": ["weekly_df = (\n", "    avg_comp.groupby([\"store_type\", \"week\"]).agg({\"orders\": \"sum\"}).reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6bc690bf-c278-4811-aea8-ce5012cfd5ba", "metadata": {}, "outputs": [], "source": ["weekly_df.rename(columns={\"orders\": \"weekly_orders\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "b90754df-53b9-4360-9c59-25e75c8ee790", "metadata": {}, "outputs": [], "source": ["delta_agg = delta_agg.merge(weekly_df, on=[\"week\", \"store_type\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "64601471-9fed-40ba-b228-b091403eff22", "metadata": {}, "outputs": [], "source": ["delta_agg[\"orders%\"] = np.round(\n", "    delta_agg[\"orders\"] * 100 / delta_agg[\"weekly_orders\"], 2\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "86d70ba6-6d9b-4ad6-8bb3-bb20a7b80f2f", "metadata": {}, "outputs": [], "source": ["delta_agg[\"orders%\"] = delta_agg[\"orders%\"].astype(str) + \"%\""]}, {"cell_type": "code", "execution_count": null, "id": "3bc24f1f-00dc-436a-8d1d-85369e800c46", "metadata": {}, "outputs": [], "source": ["delta_agg.rename(columns={\"outlet_id\": \"outlet_cnt\"}, inplace=True)\n", "delta_agg.drop(columns=[\"orders\", \"weekly_orders\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "1bf45a1d-e422-40c8-a774-329d8bb8b6cd", "metadata": {}, "outputs": [], "source": ["delta_agg[delta_agg.week == \"18\"]"]}, {"cell_type": "code", "execution_count": null, "id": "82df8673-d054-4af8-8504-a7db41dff6a7", "metadata": {}, "outputs": [], "source": ["delta_agg[delta_agg.week == \"19\"]"]}, {"cell_type": "code", "execution_count": null, "id": "8450e8e7-801a-4027-b84d-6e21132654ca", "metadata": {}, "outputs": [], "source": ["comp[(comp.store_type == \"new\") & (comp.month == \"05\")].sort_values(\n", "    by=[\"delta\"], ascending=False\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "46c264b4-04a4-45b9-b948-21ca68633343", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "70235b27-2556-4f07-be50-11ac91b8b96c", "metadata": {"tags": []}, "outputs": [], "source": ["existing_store_df3[existing_store_df3.outlet_id == 4502].tail(10)"]}, {"cell_type": "code", "execution_count": null, "id": "ef7c6b82-3f9f-41eb-a100-bd44b34dba0f", "metadata": {}, "outputs": [], "source": ["combined_df[combined_df.outlet_id == 4502].head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "c554adcc-c8f9-4ae5-bf1c-8d41e6662502", "metadata": {}, "outputs": [], "source": ["orders_cy[orders_cy.outlet_id == 3943].sort_values(by=[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "b8223f30-a09e-4948-951a-b629aa492b99", "metadata": {}, "outputs": [], "source": ["store_live[store_live.outlet_id == 4489]"]}, {"cell_type": "code", "execution_count": null, "id": "05c4746c-abf7-41ad-8557-4ae6b40b5d19", "metadata": {}, "outputs": [], "source": ["active_store[active_store.merchant_id == 34826]"]}, {"cell_type": "code", "execution_count": null, "id": "869e1783-8506-4dfe-a576-30b0b1bd7d4f", "metadata": {}, "outputs": [], "source": ["first_order[first_order.merchant_id == 34034]"]}, {"cell_type": "code", "execution_count": null, "id": "31464085-f376-4b09-a608-6ea4764681e4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "83bf26be-4a81-45ad-8703-42fe36b0dba1", "metadata": {}, "outputs": [], "source": ["store_live[store_live.merchant_id == 30559]"]}, {"cell_type": "code", "execution_count": null, "id": "b81feb99-793f-4187-982a-43987bf8a17c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}