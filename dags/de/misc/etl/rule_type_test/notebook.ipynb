{"cells": [{"cell_type": "code", "execution_count": null, "id": "3e9b28a2-35ac-4a9f-b706-1099cd292246", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3840e1ee-dd5b-417e-8151-92a7c97df890", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import time\n", "from datetime import datetime, timedelta, date\n", "import json\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "!pip install pandasql\n", "import pandasql as ps\n", "\n", "pd.options.mode.chained_assignment = None"]}, {"cell_type": "code", "execution_count": null, "id": "7e2f898a-5652-4081-9ff4-9877bbdc8832", "metadata": {}, "outputs": [], "source": ["trino_conn = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "5991355e-a991-4a8d-b434-39788a395874", "metadata": {}, "outputs": [], "source": ["rules_query = \"\"\"\n", "\n", "with\n", "    present as (\n", "        select\n", "            rule_type,\n", "            sum(count_of_rules_starting) as count_of_rules_starting,\n", "            sum(count_of_rules_ending) as count_of_rules_ending\n", "        from\n", "            (\n", "                select\n", "                    date (start_ts) as start_date,\n", "                    date (end_ts) as end_date,\n", "                    rule_type,\n", "                    count(\n", "                        distinct case\n", "                            when status = 'INSERTED' then id\n", "                        end\n", "                    ) as count_of_rules_starting,\n", "                    count(\n", "                        distinct case\n", "                            when status = 'ACTIVE_CALLBACK_SUCCESS' then id\n", "                        end\n", "                    ) as count_of_rules_ending\n", "                from\n", "                    pricing_v3.rule_management_masterrule\n", "                where\n", "                    insert_ds_ist > cast(current_date - interval '60' day as varchar)\n", "                    and lake_active_record\n", "                    and (\n", "                        (\n", "                            start_ts BETWEEN cast(current_date as timestamp) - interval '330' minute AND cast(current_date as timestamp) + interval '1' day - interval '320' minute\n", "                        )\n", "                        OR (\n", "                            end_ts BETWEEN cast(current_date as timestamp) - interval '330' minute AND cast(current_date as timestamp) + interval '1' day - interval '320' minute\n", "                        )\n", "                    )\n", "                group by\n", "                    1,\n", "                    2,\n", "                    3\n", "                having\n", "                    count(\n", "                        distinct case\n", "                            when status = 'INSERTED' then id\n", "                        end\n", "                    ) > 0\n", "                    or count(\n", "                        distinct case\n", "                            when status = 'ACTIVE_CALLBACK_SUCCESS' then id\n", "                        end\n", "                    ) > 0\n", "            )\n", "        where\n", "            (\n", "                start_date = DATE(current_timestamp AT TIME ZONE 'UTC')\n", "                and count_of_rules_starting > 0\n", "            )\n", "            or (\n", "                end_date = DATE(current_timestamp AT TIME ZONE 'UTC')\n", "                and count_of_rules_ending > 0\n", "            )\n", "        group by\n", "            1\n", "    ),\n", "    rule_type as (\n", "        select distinct\n", "            rule_type\n", "        from\n", "            pricing_v3.rule_management_masterrule\n", "        where\n", "            insert_ds_ist > cast(current_date - interval '60' day as varchar)\n", "            and lake_active_record\n", "    ),\n", "    olds as (\n", "        select\n", "            rule_type,\n", "            sum(count_of_rules_starting) as count_of_rules_starting,\n", "            sum(count_of_rules_ending) as count_of_rules_ending\n", "        from\n", "            (\n", "                select\n", "                    date (start_ts) as start_date,\n", "                    date (end_ts) as end_date,\n", "                    rule_type,\n", "                    count(\n", "                        distinct case\n", "                            when status = 'INSERTED' then id\n", "                        end\n", "                    ) as count_of_rules_starting,\n", "                    count(\n", "                        distinct case\n", "                            when status = 'ACTIVE_CALLBACK_SUCCESS' then id\n", "                        end\n", "                    ) as count_of_rules_ending\n", "                from\n", "                    pricing_v3.rule_management_masterrule\n", "                where\n", "                    insert_ds_ist > cast(current_date - interval '60' day as varchar)\n", "                    and lake_active_record\n", "                    and start_ts < cast(current_date as timestamp) - interval '330' minute\n", "                    and end_ts > cast(current_date as timestamp) - interval '330' minute\n", "                group by\n", "                    1,\n", "                    2,\n", "                    3\n", "                having\n", "                    count(\n", "                        distinct case\n", "                            when status = 'INSERTED' then id\n", "                        end\n", "                    ) > 0\n", "                    or count(\n", "                        distinct case\n", "                            when status = 'ACTIVE_CALLBACK_SUCCESS' then id\n", "                        end\n", "                    ) > 0\n", "            )\n", "        where\n", "            (\n", "                start_date < DATE(current_timestamp AT TIME ZONE 'UTC')\n", "                and count_of_rules_starting > 0\n", "            )\n", "            or (\n", "                end_date < DATE(current_timestamp AT TIME ZONE 'UTC')\n", "                and count_of_rules_ending > 0\n", "            )\n", "        group by\n", "            1\n", "    )\n", "select\n", "    rt.rule_type,\n", "    coalesce(p.count_of_rules_starting, 0) as present_starting_count,\n", "    coalesce(p.count_of_rules_ending, 0) as present_ending_count,\n", "    coalesce(o.count_of_rules_starting, 0) as old_starting_count,\n", "    coalesce(o.count_of_rules_ending, 0) as old_ending_count\n", "from\n", "    rule_type rt\n", "    left join present p on p.rule_type = rt.rule_type\n", "    left join olds o on o.rule_type = rt.rule_type\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "e988b920-27a4-4035-a892-14c44f1ed96e", "metadata": {}, "outputs": [], "source": ["rules_cnt = pd.read_sql_query(rules_query, con=trino_conn)"]}, {"cell_type": "code", "execution_count": null, "id": "9ab055bc-bb14-4f9a-9109-4dfe9c019b48", "metadata": {}, "outputs": [], "source": ["rules_cnt"]}, {"cell_type": "code", "execution_count": null, "id": "d01f6498-11c9-47f9-a36b-f37110bdfca1", "metadata": {}, "outputs": [], "source": ["bf_query = \"\"\"\n", "\n", "\n", "with\n", "    olds as (\n", "        SELECT\n", "            'Brand Fund' as rule_type,\n", "            sum(count_of_rules_starting) AS count_of_rules_starting,\n", "            sum(count_of_rules_ending) AS count_of_rules_ending\n", "        FROM\n", "            (\n", "                SELECT\n", "                    date (start_ts) AS start_date,\n", "                    date (end_ts) AS end_date,\n", "                    count(\n", "                        DISTINCT CASE\n", "                            WHEN status = 'INSERTED' THEN id\n", "                        END\n", "                    ) AS count_of_rules_starting,\n", "                    count(\n", "                        DISTINCT CASE\n", "                            WHEN status = 'ACTIVE_CALLBACK_SUCCESS' THEN id\n", "                        END\n", "                    ) AS count_of_rules_ending\n", "                FROM\n", "                    pricing_v3.attribute_management_attributemasterrule\n", "                WHERE\n", "                    start_ts < cast(current_date as timestamp) - interval '330' minute\n", "                    and end_ts > cast(current_date as timestamp) - interval '330' minute\n", "                    and attribute_type = 'ItemBrandFund'\n", "                    and insert_ds_ist > cast(current_date - interval '60' day as varchar)\n", "                GROUP BY\n", "                    1,\n", "                    2\n", "            )\n", "        WHERE\n", "            (\n", "                start_date < DATE(current_timestamp AT TIME ZONE 'UTC')\n", "                AND count_of_rules_starting > 0\n", "                and end_date >= DATE(current_timestamp AT TIME ZONE 'UTC')\n", "            )\n", "        GROUP BY\n", "            1\n", "    ),\n", "    present as (\n", "        SELECT\n", "            'Brand Fund' as rule_type,\n", "            sum(count_of_rules_starting) AS count_of_rules_starting,\n", "            sum(count_of_rules_ending) AS count_of_rules_ending\n", "        FROM\n", "            (\n", "                SELECT\n", "                    date (start_ts) AS start_date,\n", "                    date (end_ts) AS end_date,\n", "                    count(\n", "                        DISTINCT CASE\n", "                            WHEN status = 'INSERTED' THEN id\n", "                        END\n", "                    ) AS count_of_rules_starting,\n", "                    count(\n", "                        DISTINCT CASE\n", "                            WHEN status = 'ACTIVE_CALLBACK_SUCCESS' THEN id\n", "                        END\n", "                    ) AS count_of_rules_ending\n", "                FROM\n", "                    pricing_v3.attribute_management_attributemasterrule\n", "                WHERE\n", "                    (\n", "                        (\n", "                            start_ts BETWEEN cast(current_date as timestamp) - interval '330' minute AND cast(current_date as timestamp)  + interval '1' day - interval '320' minute\n", "                        )\n", "                        OR (\n", "                            end_ts BETWEEN cast(current_date as timestamp) - interval '330' minute AND cast(current_date as timestamp)  + interval '1' day - interval '320' minute\n", "                        )\n", "                    )\n", "                    and attribute_type = 'ItemBrandFund'\n", "                    and insert_ds_ist > cast(current_date - interval '60' day as varchar)\n", "                GROUP BY\n", "                    1,\n", "                    2\n", "            )\n", "        WHERE\n", "            (\n", "                start_date = DATE(current_timestamp AT TIME ZONE 'UTC')\n", "                AND count_of_rules_starting > 0\n", "            )\n", "            OR (\n", "                end_date = DATE(current_timestamp AT TIME ZONE 'UTC')\n", "                AND count_of_rules_ending > 0\n", "            )\n", "        GROUP BY\n", "            1\n", "    )\n", "select\n", "    'Brand Fund' rule_type,\n", "    coalesce(p.count_of_rules_starting, 0) as present_starting_count,\n", "    coalesce(p.count_of_rules_ending, 0) as present_ending_count,\n", "    coalesce(o.count_of_rules_starting, 0) as old_starting_count,\n", "    coalesce(o.count_of_rules_ending, 0) as old_ending_count\n", "from\n", "    present p\n", "    left join olds o on o.rule_type = p.rule_type\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "29b06410-ec77-4580-81bf-0978d4462cc1", "metadata": {}, "outputs": [], "source": ["bf_cnt = pd.read_sql_query(bf_query, con=trino_conn)"]}, {"cell_type": "code", "execution_count": null, "id": "f4fb6b3a-37c1-4972-a895-f8a0ace01073", "metadata": {}, "outputs": [], "source": ["bf_cnt"]}, {"cell_type": "code", "execution_count": null, "id": "e1f8b11e-8a77-4fd8-ad93-062c1a96d61f", "metadata": {}, "outputs": [], "source": ["datavaladf = rules_cnt.append(bf_cnt)"]}, {"cell_type": "code", "execution_count": null, "id": "6fa401e7-3573-4af4-976e-5be118ee02d3", "metadata": {}, "outputs": [], "source": ["# datavaladf = pb.from_sheets(\"1u8OmBKjwXwgOdfuu5ZI0ON3LiJ-F1EBhA4hDZoev-6Y\", \"Sheet1\")"]}, {"cell_type": "code", "execution_count": null, "id": "0a4dc745-2dd3-48bf-bf2d-0ffb0aa54b06", "metadata": {}, "outputs": [], "source": ["alert_channel = \"bl-pricing-observability\""]}, {"cell_type": "code", "execution_count": null, "id": "e65ae4a9-051a-4e04-ac25-964d487a34f5", "metadata": {}, "outputs": [], "source": ["date_selected = pd.to_datetime(\n", "    datetime.today() + timedelta(hours=5.5) - timedelta(days=1)\n", ").strftime(\"%Y-%m-%d\")\n", "date_selected"]}, {"cell_type": "code", "execution_count": null, "id": "78d35012-bce8-4713-a557-b871c7a49712", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=6.5,\n", "    row_height=0.825,\n", "    font_size=20,\n", "    header_color=\"#404040\",  # \"#204B57\",\n", "    row_colors=[\n", "        \"#f1f1f2\",  # 1\n", "        \"#f1f1f2\",  # 2\n", "        \"#f1f1f2\",  # 3\n", "        \"#f1f1f2\",  # 4\n", "        \"#f1f1f2\",  # 5\n", "        \"#f1f1f2\",  # 6\n", "        \"#f1f1f2\",  # 7\n", "        \"#f1f1f2\",  # 8\n", "        \"#f1f1f2\",  # 9\n", "        \"#f1f1f2\",  # 10\n", "        \"#f1f1f2\",  # 11\n", "        \"#f1f1f2\",  # 12\n", "        \"#f1f1f2\",  # 13\n", "        \"#f1f1f2\",  # 14\n", "        \"#f1f1f2\",  # 15\n", "        \"#f1f1f2\",  # 16\n", "        \"#f1f1f2\",  # 17\n", "        \"#f1f1f2\",  # 18\n", "        \"#f1f1f2\",  # 19\n", "        \"#f1f1f2\",  # 20\n", "    ],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    #     cellLoc='center',\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, cellLoc=\"center\", **kwargs\n", "    )\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\", fontsize=24)\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "ae9f2b71-ad88-4e58-be14-c7c16d2b32b8", "metadata": {}, "outputs": [], "source": ["fig, ax = render_mpl_table(datavaladf, header_columns=0)\n", "fig.savefig(\"alert_avail.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "7412f87b-96b0-432a-8731-b1f7162e7459", "metadata": {}, "outputs": [], "source": ["print(alert_channel)"]}, {"cell_type": "code", "execution_count": null, "id": "dac9c555-22e2-4126-8d49-ef546a9e42d5", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "\n", "if datavaladf.shape[0] > 0:\n", "    fig, ax = render_mpl_table(datavaladf, header_columns=0)\n", "    fig.savefig(\"alert_avail.png\")\n", "\n", "    # filepath = './mytable.png'\n", "    file_check = \"./alert_avail.png\"\n", "    filepath = file_check\n", "    channel = alert_channel\n", "    text = \"[Ignore | From test DAG] Count of rules starting & ending today\"\n", "else:\n", "    print(\"<PERSON><PERSON> failed\")\n", "pb.send_slack_message(channel=channel, text=text, files=[filepath])"]}, {"cell_type": "code", "execution_count": null, "id": "b0d719be-8608-4b39-ba7d-bbd09a2ad356", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}