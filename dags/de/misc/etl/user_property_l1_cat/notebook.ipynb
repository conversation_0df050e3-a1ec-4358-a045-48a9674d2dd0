{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table_name = \"user_property_l1_cat\"\n", "table_schema = \"consumer\"\n", "column_dtypes = [\n", "    {\n", "        \"name\": \"customer_id\",\n", "        \"type\": \"bigint\",\n", "        \"description\": \"Grofers user_id of the customer\",\n", "    },\n", "    {\n", "        \"name\": \"l_one_cat\",\n", "        \"type\": \"VARCHAR(5000)\",\n", "        \"description\": \"L1 category\",\n", "    },\n", "    {\n", "        \"name\": \"l_zero_cat\",\n", "        \"type\": \"VARCHAR(5000)\",\n", "        \"description\": \"L0 category\",\n", "    },\n", "]\n", "table_description = \"This table contains category wise categorisation of customer_ids\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["l0_sql = \"\"\"\n", "WITH oid_base AS \n", "(\n", "    WITH oid_col_sel AS \n", "    (\n", "        SELECT\n", "            customer_id,\n", "            l_cat,\n", "            cart_checkout_ts,\n", "            city,\n", "            direction,\n", "            source,\n", "            merchant_business_type \n", "        FROM\n", "            order_item_data \n", "    )\n", "    SELECT\n", "        * \n", "    FROM\n", "        order_item_data \n", "    WHERE\n", "        DATE(cart_checkout_ts + INTERVAL '5.5 hours') >= '2019-01-01' \n", "        AND city NOT IN \n", "        (\n", "            'Haridwar',\n", "            '<PERSON><PERSON><PERSON>',\n", "            'Not in service area'\n", "        )\n", "        AND city NOT ilike '%%B2B%%' \n", "        AND source IN \n", "        (\n", "            'consumer_android',\n", "            'consumer_ios',\n", "            'consumer_web'\n", "        )\n", "        -- and (source <> 'others' or source is null)\n", "        AND \n", "        (\n", "            direction IS NULL \n", "            OR direction IN \n", "            (\n", "                'RetailForwardOrder',\n", "                'RetailSuborder'\n", "            )\n", "        )\n", "        AND \n", "        (\n", "            merchant_business_type <> 'b2b' \n", "            OR merchant_business_type IS NULL\n", "        )\n", "        AND is_delivered IS TRUE \n", ")\n", "SELECT DISTINCT\n", "    customer_id,\n", "    CASE\n", "        WHEN\n", "            l_cat ilike ('%%,%%') \n", "        THEN\n", "            regexp_replace(l_cat, ',', '&') \n", "        ELSE\n", "            l_cat \n", "    END\n", "    AS l_zero_cat \n", "FROM\n", "    oid_base \n", "WHERE\n", "    CURRENT_DATE - DATE(cart_checkout_ts + INTERVAL '5.5 hours') <= 180 \n", "GROUP BY\n", "    1, 2\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["l1_sql = \"\"\"\n", "WITH oid_base AS \n", "(\n", "    WITH oid_col_sel AS \n", "    (\n", "        SELECT\n", "            customer_id,\n", "            l1_cat,\n", "            cart_checkout_ts,\n", "            city,\n", "            direction,\n", "            source,\n", "            merchant_business_type \n", "        FROM\n", "            order_item_data \n", "    )\n", "    SELECT\n", "        * \n", "    FROM\n", "        order_item_data \n", "    WHERE\n", "        DATE(cart_checkout_ts + INTERVAL '5.5 hours') >= '2019-01-01' \n", "        AND city NOT IN \n", "        (\n", "            'Haridwar',\n", "            '<PERSON><PERSON><PERSON>',\n", "            'Not in service area'\n", "        )\n", "        AND city NOT ilike '%%B2B%%' \n", "        AND source IN \n", "        (\n", "            'consumer_android',\n", "            'consumer_ios',\n", "            'consumer_web'\n", "        )\n", "        -- and (source <> 'others' or source is null)\n", "        AND \n", "        (\n", "            direction IS NULL \n", "            OR direction IN \n", "            (\n", "                'RetailForwardOrder',\n", "                'RetailSuborder'\n", "            )\n", "        )\n", "        AND \n", "        (\n", "            merchant_business_type <> 'b2b' \n", "            OR merchant_business_type IS NULL\n", "        )\n", "        AND is_delivered IS TRUE \n", ")\n", "SELECT DISTINCT\n", "    customer_id,\n", "    CASE\n", "        WHEN\n", "            l1_cat ilike ('%%,%%') \n", "        THEN\n", "            regexp_replace(l1_cat, ',', '&') \n", "        ELSE\n", "            l1_cat \n", "    END\n", "    AS l_one_cat \n", "FROM\n", "    oid_base \n", "WHERE\n", "    CURRENT_DATE - DATE(cart_checkout_ts + INTERVAL '5.5 hours') <= 180 \n", "GROUP BY\n", "    1, 2\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_l0 = pd.read_sql(sql=l0_sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_l1 = pd.read_sql(sql=l1_sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_1 = df_l0.groupby(\"customer_id\")[\"l_zero_cat\"].apply(\n", "    lambda tags: \" : \".join(filter(None, tags))\n", ")\n", "df_2 = df_l1.groupby(\"customer_id\")[\"l_one_cat\"].apply(\n", "    lambda tags: \" : \".join(filter(None, tags))\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_1_test = df_1.to_frame().reset_index()\n", "df_2_test = df_2.to_frame().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_final = df_2_test.merge(df_1_test, on=\"customer_id\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": table_schema,  # Redshift schema name\n", "    \"table_name\": table_name,  # Redshift table name\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [],\n", "    \"sortkey\": [],\n", "    \"incremental_key\": [],\n", "    \"load_type\": \"truncate\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": table_description,  # Description of the table being sent to redshift\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(df_final, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}