{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table_name = \"user_lat_long_data\"\n", "schema_name = \"consumer\"\n", "column_dtypes = [\n", "    {\n", "        \"name\": \"customer_id\",\n", "        \"type\": \"BIGINT\",\n", "        \"description\": \"Grofers user_id\",\n", "    },\n", "    {\n", "        \"name\": \"snapshot_date\",\n", "        \"type\": \"DATE\",\n", "        \"description\": \"ETL snapshot date\",\n", "    },\n", "    {\n", "        \"name\": \"first_order_date\",\n", "        \"type\": \"DATE\",\n", "        \"description\": \"First order date of customer\",\n", "    },\n", "    {\n", "        \"name\": \"first_order_lat\",\n", "        \"type\": \"VARCHAR(255)\",\n", "        \"description\": \"Latitude of first order location\",\n", "    },\n", "    {\n", "        \"name\": \"first_order_lon\",\n", "        \"type\": \"VARCHAR(255)\",\n", "        \"description\": \"Longitude of first order location\",\n", "    },\n", "    {\n", "        \"name\": \"first_order_city\",\n", "        \"type\": \"VARCHAR(255)\",\n", "        \"description\": \"First order city of customer\",\n", "    },\n", "    {\n", "        \"name\": \"last_order_date\",\n", "        \"type\": \"DATE\",\n", "        \"description\": \"Last order date of customer\",\n", "    },\n", "    {\n", "        \"name\": \"last_order_lat\",\n", "        \"type\": \"VARCHAR(255)\",\n", "        \"description\": \"Latitude of last order location\",\n", "    },\n", "    {\n", "        \"name\": \"last_order_lon\",\n", "        \"type\": \"VARCHAR(255)\",\n", "        \"description\": \"Longitude of last order location\",\n", "    },\n", "    {\n", "        \"name\": \"last_order_city\",\n", "        \"type\": \"VARCHAR(255)\",\n", "        \"description\": \"Last order city of customer\",\n", "    },\n", "]\n", "table_description = \"This table contains geographical location of first and last order of all the customers\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "select\n", "   customer_id,\n", "   date(getdate()) as snapshot_date,\n", "   max(\n", "   case\n", "      when\n", "         first_order = 1 \n", "      then\n", "         COALESCE(cart_checkout_ts, install_ts) \n", "   end\n", ") as First_Order_Date, max(\n", "   case\n", "      when\n", "         first_order = 1 \n", "      then\n", "         user_location_lat \n", "   end\n", ") as First_Order_Lat, max(\n", "   case\n", "      when\n", "         first_order = 1 \n", "      then\n", "         user_location_lon \n", "   end\n", ") as First_Order_Lon, max(\n", "   case\n", "      when\n", "         first_order = 1 \n", "      then\n", "         city \n", "   end\n", ") as First_Order_City, max(\n", "   case\n", "      when\n", "         last_order = 1 \n", "      then\n", "         COALESCE(cart_checkout_ts, install_ts) \n", "   end\n", ") as last_Order_Date, max(\n", "   case\n", "      when\n", "         last_order = 1 \n", "      then\n", "         user_location_lat \n", "   end\n", ") as Last_Order_Lat, max(\n", "   case\n", "      when\n", "         last_order = 1 \n", "      then\n", "         user_location_lon \n", "   end\n", ") as Last_Order_Lon, max(\n", "   case\n", "      when\n", "         last_order = 1 \n", "      then\n", "         city \n", "   end\n", ") as Last_Order_city \n", "from\n", "   (\n", "      select\n", "         customer_id,\n", "         date(cart_checkout_ts + interval '5.5 hours') as cart_checkout_ts,\n", "         date(install_ts + interval '5.5 hours') as install_ts,\n", "         user_location_lat,\n", "         user_location_lon,\n", "         order_id,\n", "         city,\n", "         ROW_NUMBER() OVER ( PARTITION BY customer_id \n", "      ORDER BY\n", "         order_id ASC) as First_order,\n", "         ROW_NUMBER() OVER ( PARTITION BY customer_id \n", "      ORDER BY\n", "         order_id DESC) as Last_order \n", "      from\n", "         order_data \n", "      where\n", "         customer_id IN\n", "         (\n", "            select distinct\n", "               customer_id \n", "            from\n", "               order_data \n", "            where\n", "               date(install_ts + interval '5.5 hours') > (\n", "               select\n", "                  max(snapshot_date) - 1 \n", "               from\n", "                  consumer.user_lat_long_data)\n", "         )\n", "   )\n", "   d \n", "where\n", "   (\n", "      first_order = 1 \n", "      or last_order = 1\n", "   )\n", "group by\n", "   1,\n", "   2\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_connection = pb.get_connection(\"redpen\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.read_sql(sql=sql, con=redshift_connection)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": schema_name,  # Redshift schema name\n", "    \"table_name\": table_name,  # Redshift table name\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": \"customer_id\",  # list\n", "    \"sortkey\": [],  # list\n", "    \"incremental_key\": \"snapshot_date\",  # string\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": table_description,  # Description of the table being sent to redshift\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}