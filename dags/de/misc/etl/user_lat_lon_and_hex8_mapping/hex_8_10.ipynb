{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install h3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from h3 import h3\n", "import pandas as pd\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table_name = \"gr_users_hex8_mapping\"\n", "schema_name = \"consumer\"\n", "column_dtypes = [\n", "    {\n", "        \"name\": \"customer_id\",\n", "        \"type\": \"BIGINT\",\n", "        \"description\": \"Grofers user_id\",\n", "    },\n", "    {\n", "        \"name\": \"first_order_date\",\n", "        \"type\": \"DATE\",\n", "        \"description\": \"First order_date of the customer\",\n", "    },\n", "    {\n", "        \"name\": \"first_order_hex8\",\n", "        \"type\": \"VARCHAR(100)\",\n", "        \"description\": \"H3 grid index of customer's first order location at resolution 8\",\n", "    },\n", "    {\n", "        \"name\": \"last_order_date\",\n", "        \"type\": \"DATE\",\n", "        \"description\": \"Last order_date of the customer\",\n", "    },\n", "    {\n", "        \"name\": \"last_order_hex8\",\n", "        \"type\": \"VARCHAR(100)\",\n", "        \"description\": \"H3 grid index of customer's last order location at resolution 8\",\n", "    },\n", "    {\n", "        \"name\": \"first_order_hex10\",\n", "        \"type\": \"VARCHAR(20)\",\n", "        \"description\": \"H3 grid index of customer's first order location at resolution 10\",\n", "    },\n", "    {\n", "        \"name\": \"last_order_hex10\",\n", "        \"type\": \"VARCHAR(20)\",\n", "        \"description\": \"H3 grid index of customer's last order location at resolution 10\",\n", "    },\n", "]\n", "table_description = \"This table contains H3 grid location of customer's first and last order at resolution 8 and 10\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "select\n", "   * \n", "from\n", "   consumer.user_lat_long_data \n", "where\n", "   snapshot_date = date(getdate()) \n", "   and first_order_lat <> '' \n", "   and last_order_lat <> ''\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def execute_sql(con, sql):\n", "    for df in pd.read_sql_query(\n", "        sql=sql, con=con, chunksize=1200000, coerce_float=False\n", "    ):\n", "        df[\"first_order_lat\"] = df[\"first_order_lat\"].astype(float)\n", "        df[\"first_order_lon\"] = df[\"first_order_lon\"].astype(float)\n", "        df[\"first_order_hex8\"] = df.apply(\n", "            lambda row: h3.geo_to_h3(row[\"first_order_lat\"], row[\"first_order_lon\"], 8),\n", "            axis=1,\n", "        )\n", "        df[\"first_order_hex10\"] = df.apply(\n", "            lambda row: h3.geo_to_h3(\n", "                row[\"first_order_lat\"], row[\"first_order_lon\"], 10\n", "            ),\n", "            axis=1,\n", "        )\n", "        df[\"last_order_lat\"] = df[\"last_order_lat\"].astype(float)\n", "        df[\"last_order_lon\"] = df[\"last_order_lon\"].astype(float)\n", "        df[\"last_order_hex8\"] = df.apply(\n", "            lambda row: h3.geo_to_h3(row[\"last_order_lat\"], row[\"last_order_lon\"], 8),\n", "            axis=1,\n", "        )\n", "        df[\"last_order_hex10\"] = df.apply(\n", "            lambda row: h3.geo_to_h3(row[\"last_order_lat\"], row[\"last_order_lon\"], 10),\n", "            axis=1,\n", "        )\n", "        selected_columns_from_df = df[\n", "            [\n", "                \"customer_id\",\n", "                \"first_order_date\",\n", "                \"first_order_hex8\",\n", "                \"last_order_date\",\n", "                \"last_order_hex8\",\n", "                \"last_order_hex10\",\n", "                \"first_order_hex10\",\n", "            ]\n", "        ]\n", "        return selected_columns_from_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_connection = pb.get_connection(\"redpen\")\n", "df = execute_sql(con=redshift_connection, sql=sql)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": schema_name,  # Redshift schema name\n", "    \"table_name\": table_name,  # Redshift table name\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": \"customer_id\",  # list\n", "    \"sortkey\": [],  # list\n", "    \"incremental_key\": \"customer_id\",  # string\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": table_description,  # Description of the table being sent to redshift\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}