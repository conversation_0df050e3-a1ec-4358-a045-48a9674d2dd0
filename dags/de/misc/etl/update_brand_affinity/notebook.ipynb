{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "import string\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table_name = \"brand_affinity\"\n", "table_schema = \"consumer\"\n", "s3_access_iam_role = \"arn:aws:iam::442534439095:role/redshift-data-migration-role\"\n", "s3_dump_path = f\"s3://grofers-prod-dse-sgp/dwh/flattable/brand_affinity/{''.join(random.choices(string.ascii_lowercase + string.digits, k = 6))}/\"\n", "column_dtypes = [\n", "    {\n", "        \"name\": \"customer_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"Grofers user_id of customer\",\n", "    },\n", "    {\n", "        \"name\": \"type_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"Product Type id\",\n", "    },\n", "    {\n", "        \"name\": \"brand\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Product Brand\",\n", "    },\n", "    {\n", "        \"name\": \"tagging\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Classification of user\",\n", "    },\n", "]\n", "table_description = (\n", "    \"This table contains classification of user as per brand and product type id\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rebuild_sql = \"\"\"\n", "SELECT \n", "  customer_id::int,\n", "  type_id::int,\n", "  brand,\n", "  CASE\n", "      WHEN tagging ='Recently acquired' \n", "      THEN 'one time user'\n", "      ELSE tagging\n", "  END AS tagging\n", "\n", "FROM \n", "  (WITH b AS (\n", "      WITH a AS (\n", "        SELECT \n", "          DISTINCT customer_id,\n", "          type_id,\n", "          brand,\n", "          CASE \n", "            WHEN orders_with_ptype=1\n", "              THEN 'Recently acquired' \n", "            WHEN orders_with_ptype=2 \n", "              THEN \n", "                CASE \n", "                  WHEN (orders_with_ptype_brand::float/orders_with_ptype)=1 \n", "                  THEN 'ongoing loyal' \n", "                  when brand=last_brand \n", "                  then 'in_trial' \n", "                  ELSE 'indifferent' \n", "                END\n", "            WHEN orders_with_ptype>=3\n", "              THEN CASE \n", "                      WHEN orders_with_ptype_brand=1 AND brand=last_brand\n", "                        THEN 'in_trial' \n", "                      WHEN (orders_with_ptype_brand=1 AND brand<>last_brand) \n", "                        THEN 'indifferent' \n", "            ELSE \n", "              CASE \n", "                WHEN (orders_with_ptype_brand::float/orders_with_ptype)>=0.75 \n", "                THEN \n", "                  CASE \n", "                    WHEN \n", "                      ((brand=second_last_brand)\n", "                      OR (brand=last_brand)) \n", "                    THEN 'ongoing loyal' \n", "                    ELSE 'recent lapser' \n", "                  END\n", "                WHEN \n", "                  (orders_with_ptype_brand::float/orders_with_ptype)<0.75\n", "                  AND (orders_with_ptype_brand::float/orders_with_ptype)>=0.25\n", "                THEN \n", "                  CASE \n", "                    WHEN \n", "                      brand=second_last_brand\n", "                      AND brand=last_brand \n", "                    THEN 'recent loyal' \n", "                    ELSE 'switcher' \n", "                  END\n", "                WHEN ((orders_with_ptype_brand::float/orders_with_ptype)<0.25) \n", "                  THEN 'switcher' \n", "                  ELSE 'no tag' \n", "                END\n", "              END\n", "            END AS tagging\n", "        \n", "        FROM \n", "          brand_data\n", "        )\n", "                   \n", "      SELECT \n", "        *,\n", "        CASE \n", "          WHEN tagging='ongoing loyal' \n", "            THEN 1 \n", "          WHEN tagging='recent loyal' \n", "            THEN 2 \n", "          WHEN tagging='Recently acquired' \n", "            THEN 3 \n", "          WHEN tagging='switcher' \n", "            THEN 4 \n", "          WHEN tagging='recent lapser' \n", "            THEN 5 \n", "          WHEN tagging='in_trial' \n", "            THEN 6 \n", "          WHEN tagging='indifferent' \n", "            THEN 7 \n", "        END AS priority\n", "      \n", "      FROM \n", "        a\n", "        )\n", "\n", "      SELECT \n", "        *,\n", "        dense_rank() over (partition BY customer_id,type_id,brand ORDER BY priority ASC) AS rnk\n", "\n", "  FROM \n", "    b\n", "  )\n", "\n", "WHERE rnk=1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["unload_sql = f\"\"\"\n", "UNLOAD ($$ {rebuild_sql} $$)\n", "to '{s3_dump_path}'\n", "iam_role '{s3_access_iam_role}'\n", "FORMAT PARQUET\n", "ALLOWOVERWRITE\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_connection = pb.get_connection(\"redpen\")\n", "redshift_connection.execute(unload_sql)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": table_schema,\n", "    \"table_name\": table_name,\n", "    \"primary_key\": [],\n", "    \"column_dtypes\": column_dtypes,\n", "    \"sortkey\": [],\n", "    \"incremental_key\": [],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": table_description,\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(s3_dump_path, copy_params=[\"FORMAT AS PARQUET\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}