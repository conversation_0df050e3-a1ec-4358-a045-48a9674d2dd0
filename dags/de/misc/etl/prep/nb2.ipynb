{"cells": [{"cell_type": "code", "execution_count": null, "id": "704fd6be-c648-4aa8-8f09-d59fd02645fc", "metadata": {}, "outputs": [], "source": ["import time\n", "time.sleep(3*60)\n", "print(\"Hello World\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}