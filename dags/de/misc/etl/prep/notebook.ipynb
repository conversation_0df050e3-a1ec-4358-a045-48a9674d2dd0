{"cells": [{"cell_type": "code", "execution_count": null, "id": "a6f2efab-12a1-4132-aea3-1f96f9c8ba51", "metadata": {}, "outputs": [], "source": ["import time\n", "time.sleep(1*60)\n", "print(\"Hello World\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}