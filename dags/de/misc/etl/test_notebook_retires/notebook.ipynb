{"cells": [{"cell_type": "code", "execution_count": null, "id": "5275ca18-b208-4aab-9254-4e1e6217eba5", "metadata": {}, "outputs": [], "source": ["print(\"helloWorld\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}