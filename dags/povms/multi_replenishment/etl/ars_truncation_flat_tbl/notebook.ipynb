{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import datetime\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "from datetime import timedelta\n", "import time\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "CON_PRESTO = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    start = time.time()\n", "    df = pd.read_sql_query(sql, con)\n", "    end = time.time()\n", "    if (end - start) > 60:\n", "        print(\"Time: \", (end - start) / 60, \"min\")\n", "    else:\n", "        print(\"Time: \", end - start, \"s\")\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date = (datetime.datetime.now() - timed<PERSON><PERSON>(days=7)).strftime(\"%Y%m%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["today = (datetime.datetime.now() - timed<PERSON>ta(days=1)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_type_df = pb.from_sheets(\n", "    sheetid=\"1NlMI5N6wW1i-gD2UbjTiqW_fnuTM8MpaX9FSxKnHPfI\", sheetname=\"ars_run_type\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_type_list = list(run_type_df[\"run_type\"])\n", "run_type_list = tuple(run_type_list)\n", "manual_flag = run_type_df[\"manual flag\"][0]\n", "\n", "if manual_flag == \"TRUE\":\n", "    mnf = tuple([\"true\", \"\"])\n", "else:\n", "    mnf = tuple([\"\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_type_list\n", "manual_flag\n", "mnf"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_created_at_query = f\"\"\"\n", "select *\n", ", row_number() over(partition by backend_facility_id,frontend_facility_id order by run_id asc) indent\n", "from \n", "\n", "(select distinct\n", "        be.facility_id backend_facility_id,\n", "        fe.facility_id frontend_facility_id,\n", "        ars_run_id run_id,\n", "        ars_run_flag,\n", "        manual_run\n", "        \n", "        from metrics.esto_details a\n", "        left join lake_retail.console_outlet be on a.sender_outlet_id=be.id\n", "        left join lake_retail.console_outlet fe on a.receiving_outlet_id=fe.id       \n", "        where sto_created_at between current_date-1 and current_date + 1\n", "        and ars_run_flag in {run_type_list} and manual_run in {mnf}\n", "        )\n", "\"\"\"\n", "\n", "sto_dt_df = read_sql_query(sto_created_at_query, CON_REDSHIFT)\n", "# sto_dt_df = pd.read_sql_query(\n", "#     sto_created_at_query,\n", "#     redshift,\n", "#     params={\"ars_run_type_list\": tuple(run_type_list), \"manual_run_flag\": mnf},\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_dt_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_id_list1 = list(sto_dt_df[\"run_id\"].unique())\n", "run_id_list1 = tuple(run_id_list1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(run_id_list1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_id_query = f\"\"\"\n", "                select distinct run_id, started_at_ist as started_at_ist\n", "                    from (\n", "                            select run_id, completed_at as completed_at_utc,\n", "                                    started_at as started_at_utc,\n", "                                    cast(from_unixtime(completed_at/pow(10,6), 'Asia/Kolkata') as timestamp) completed_at_ist\n", "                                    , cast(from_unixtime(started_at/pow(10,6), 'Asia/Kolkata') as timestamp) started_at_ist\n", "                            from lake_ars.job_run\n", "                            where run_id in {run_id_list1}\n", "                            and JSON_EXTRACT_SCALAR(simulation_params, '$.run') in ('ars_lite')\n", "                        ) r\n", "                    \"\"\"\n", "\n", "run_id_df = read_sql_query(run_id_query, CON_PRESTO)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_id_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_id_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["transfer_optimisation_query = f\"\"\"\n", "select frontend_outlet_id, backend_outlet_id,\n", "        sto_quantity v1_quantity, sto_quantity_post_truncation_in_case v2_quantity, sto_quantity_post_truncation as v2_quantity_without_case,\n", "        run_id, item_id, quantity_drops\n", "        from lake_ars.transfers_optimization_results_v2 x\n", "        where run_id in {run_id_list1}\n", "        and insert_ds_ist >= cast(current_date - interval '2' day as varchar)\n", "        \n", "\"\"\"\n", "transfer_opt_df = read_sql_query(transfer_optimisation_query, CON_PRESTO)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["transfer_opt_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["faci_out_query = \"\"\"\n", "select distinct id, facility_id from lake_retail.console_outlet\n", "\"\"\"\n", "facility_outlet_mapping = read_sql_query(faci_out_query, CON_PRESTO)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# facility_outlet_mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_name_query = \"\"\"\n", "select u.id frontend_facility_id, name facility_name from lake_crates.facility u\n", "                        join (select id, max(updated_at) updated_at from lake_crates.facility group by 1) p on u.id=p.id and u.updated_at=p.updated_at\"\"\"\n", "\n", "facility_name_df = read_sql_query(facility_name_query, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# facility_name_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dark_store_list_query = \"\"\"\n", "select distinct facility_id from lake_retail.console_outlet where business_type_id=7\"\"\"\n", "\n", "dark_store_list_df = read_sql_query(dark_store_list_query, CON_PRESTO)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["zone_mapping_query = \"\"\"\n", "    \n", "    select facility_id , max(zone) zone2\n", "    from metrics.outlet_zone_mapping\n", "    group by 1\n", "    \n", "    \"\"\"\n", "\n", "zone_df = read_sql_query(zone_mapping_query, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# zone_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["zone_df[[\"facility_id\"]] = zone_df[[\"facility_id\"]].astype(float)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# zone_df = zone_df.astype({\"facility_id\": float}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_fe = pd.merge(\n", "    transfer_opt_df,\n", "    facility_outlet_mapping.rename(\n", "        columns={\"id\": \"frontend_outlet_id\", \"facility_id\": \"frontend_facility_id\"}\n", "    ),\n", "    on=[\"frontend_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "base_be = pd.merge(\n", "    base_fe,\n", "    facility_outlet_mapping.rename(\n", "        columns={\"id\": \"backend_outlet_id\", \"facility_id\": \"backend_facility_id\"}\n", "    ),\n", "    on=[\"backend_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "base_be = base_be[\n", "    base_be[\"frontend_facility_id\"].isin(dark_store_list_df[\"facility_id\"])\n", "]\n", "\n", "base_zone = pd.merge(\n", "    base_be,\n", "    zone_df.rename(columns={\"facility_id\": \"frontend_facility_id\"}),\n", "    on=\"frontend_facility_id\",\n", "    how=\"left\",\n", ")\n", "base_fe_name = pd.merge(\n", "    base_zone, facility_name_df, on=\"frontend_facility_id\", how=\"left\"\n", ")\n", "\n", "base_be_name = pd.merge(\n", "    base_fe_name,\n", "    facility_name_df.rename(\n", "        columns={\n", "            \"frontend_facility_id\": \"backend_facility_id\",\n", "            \"facility_name\": \"backend_facility_name\",\n", "        }\n", "    ),\n", "    on=\"backend_facility_id\",\n", "    how=\"left\",\n", ")\n", "\n", "base_final_frame2 = pd.merge(\n", "    base_be_name,\n", "    sto_dt_df,\n", "    on=[\"frontend_facility_id\", \"backend_facility_id\", \"run_id\"],\n", ")\n", "df2 = pd.merge(base_final_frame2, run_id_df, on=\"run_id\")\n", "\n", "df = df2[\n", "    [\n", "        \"zone2\",\n", "        \"backend_facility_id\",\n", "        \"frontend_facility_id\",\n", "        \"facility_name\",\n", "        \"backend_facility_name\",\n", "        \"frontend_outlet_id\",\n", "        \"backend_outlet_id\",\n", "        \"run_id\",\n", "        \"item_id\",\n", "        \"v1_quantity\",\n", "        \"v2_quantity\",\n", "        \"quantity_drops\",\n", "        \"indent\",\n", "        \"started_at_ist\",\n", "        \"v2_quantity_without_case\",\n", "    ]\n", "].rename(\n", "    columns={\n", "        \"started_at_ist\": \"ars_started_at\",\n", "        \"facility_name\": \"frontend_facility_name\",\n", "    }\n", ")\n", "\n", "\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["json_cols = [\"quantity_drops\"]\n", "import json\n", "\n", "\n", "def clean_json(x):\n", "    \"Create apply function for decoding JSON\"\n", "    return json.loads(x)\n", "\n", "\n", "# Apply the function column wise to each column of interest\n", "for x in json_cols:\n", "    df[x] = df[x].apply(clean_json)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"inward_drop\"] = df[\"quantity_drops\"].apply(lambda x: x[\"inward_drop\"])\n", "df[\"storage_drop\"] = df[\"quantity_drops\"].apply(lambda x: x[\"storage_drop\"])\n", "df[\"truck_load_drop\"] = df[\"quantity_drops\"].apply(lambda x: x[\"truck_load_drop\"])\n", "df[\"picking_capacity_sku_drop\"] = df[\"quantity_drops\"].apply(\n", "    lambda x: x[\"picking_capacity_sku_drop\"]\n", ")\n", "df[\"picking_capacity_quantity_drop\"] = df[\"quantity_drops\"].apply(\n", "    lambda x: x[\"picking_capacity_quantity_drop\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.rename(columns={\"zone2\": \"zone\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"created_at\"] = today"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"run_id\"].nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = df[\n", "    [\n", "        \"zone\",\n", "        \"backend_facility_id\",\n", "        \"frontend_facility_id\",\n", "        \"frontend_facility_name\",\n", "        \"backend_facility_name\",\n", "        \"frontend_outlet_id\",\n", "        \"backend_outlet_id\",\n", "        \"run_id\",\n", "        \"item_id\",\n", "        \"v1_quantity\",\n", "        \"v2_quantity\",\n", "        \"quantity_drops\",\n", "        \"indent\",\n", "        \"ars_started_at\",\n", "        \"inward_drop\",\n", "        \"storage_drop\",\n", "        \"truck_load_drop\",\n", "        \"picking_capacity_sku_drop\",\n", "        \"picking_capacity_quantity_drop\",\n", "        \"created_at\",\n", "        \"v2_quantity_without_case\",\n", "    ]\n", "]\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df[['indent']].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"zone\", \"type\": \"varchar\", \"description\": \"zone\"},\n", "    {\n", "        \"name\": \"backend_facility_id\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend_facility_id\",\n", "    },\n", "    {\n", "        \"name\": \"frontend_facility_id\",\n", "        \"type\": \"float\",\n", "        \"description\": \"frontend_facility_id\",\n", "    },\n", "    {\n", "        \"name\": \"frontend_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"frontend_facility_name\",\n", "    },\n", "    {\n", "        \"name\": \"backend_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"backend_facility_name\",\n", "    },\n", "    {\"name\": \"frontend_outlet_id\", \"type\": \"int\", \"description\": \"frontend_outlet_id\"},\n", "    {\"name\": \"backend_outlet_id\", \"type\": \"int\", \"description\": \"backend_outlet_id\"},\n", "    {\"name\": \"run_id\", \"type\": \"varchar\", \"description\": \"run_id\"},\n", "    {\"name\": \"item_id\", \"type\": \"varchar\", \"description\": \"item_id\"},\n", "    {\"name\": \"v1_quantity\", \"type\": \"float\", \"description\": \"v1_quantity\"},\n", "    {\"name\": \"v2_quantity\", \"type\": \"float\", \"description\": \"v2_quantity_with_case\"},\n", "    {\"name\": \"quantity_drops\", \"type\": \"varchar\", \"description\": \"quantity_drops\"},\n", "    {\"name\": \"indent\", \"type\": \"varchar\", \"description\": \"indent\"},\n", "    {\"name\": \"ars_started_at\", \"type\": \"timestamp\", \"description\": \"indent\"},\n", "    {\"name\": \"inward_drop\", \"type\": \"float\", \"description\": \"inward_drop\"},\n", "    {\"name\": \"storage_drop\", \"type\": \"float\", \"description\": \"storage_drop\"},\n", "    {\"name\": \"truck_load_drop\", \"type\": \"float\", \"description\": \"truck_load_drop\"},\n", "    {\n", "        \"name\": \"picking_capacity_sku_drop\",\n", "        \"type\": \"float\",\n", "        \"description\": \"picking_capacity_sku_drop\",\n", "    },\n", "    {\n", "        \"name\": \"picking_capacity_quantity_drop\",\n", "        \"type\": \"float\",\n", "        \"description\": \"picking_capacity_quantity_drop\",\n", "    },\n", "    {\"name\": \"created_at\", \"type\": \"varchar\", \"description\": \"created_at\"},\n", "    {\n", "        \"name\": \"v2_quantity_without_case\",\n", "        \"type\": \"float\",\n", "        \"description\": \"v2_quantity_without_case\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"ars_truncation_rca_l7_raw_data_v2\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"run_id\", \"frontend_facility_id\", \"item_id\"],\n", "    \"sortkey\": [\"run_id\"],\n", "    \"incremental_key\": \"item_id\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"Database for RCA report of ARS validation\",  # Description of the table being sent to redshift\n", "}\n", "\n", "pb.to_redshift(df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q = f\"\"\"\n", "    select * from metrics.ars_truncation_rca_l7_raw_data_v2\n", "    where\n", "    -- run_id in ('20220413001004')\n", "    created_at = \n", "            --'2022-04-13'\n", "     (select max(created_at) from metrics.ars_truncation_rca_l7_raw_data_v2)\n", "     \"\"\"\n", "qq = read_sql_query(q, CON_REDSHIFT)\n", "qq"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# qq.to_csv('qq.csv', index=False)\n", "# 364536 - 308460"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}