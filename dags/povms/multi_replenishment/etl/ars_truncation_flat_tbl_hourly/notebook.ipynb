{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import datetime\n", "import sys\n", "from datetime import timedelta\n", "import time\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "retail = pb.get_connection(\"retail\")\n", "pos_con = pb.get_connection(\"[Replica] POS\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date = (datetime.datetime.now() - timed<PERSON><PERSON>(days=7)).strftime(\"%Y%m%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "import pytz\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")\n", "\n", "current_time = datetime.now(IST) - <PERSON>el<PERSON>(hours=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["today = current_time.strftime(\"%Y-%m-%d 00:00:00\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["today"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["datetime.now()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_type_df = pb.from_sheets(\n", "    sheetid=\"1NlMI5N6wW1i-gD2UbjTiqW_fnuTM8MpaX9FSxKnHPfI\", sheetname=\"ars_run_type\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_type_list = list(run_type_df[\"run_type\"])\n", "manual_flag = run_type_df[\"manual flag\"][0]\n", "\n", "if manual_flag == \"TRUE\":\n", "    mnf = tuple([\"true\", \"\"])\n", "else:\n", "    mnf = tuple([\"\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_created_at_query = \"\"\"\n", "select *\n", ", row_number() over(partition by backend_facility_id,frontend_facility_id order by run_id asc) indent\n", "from \n", "\n", "(select distinct\n", "        be.facility_id backend_facility_id,\n", "        fe.facility_id frontend_facility_id,\n", "        ars_run_id run_id,\n", "        ars_run_flag,\n", "        manual_run,\n", "        sto_created_at\n", "        \n", "        from metrics.esto_details a\n", "        left join lake_retail.console_outlet be on a.sender_outlet_id=be.id\n", "        left join lake_retail.console_outlet fe on a.receiving_outlet_id=fe.id       \n", "        where sto_created_at >= %(input_date)s\n", "        and ars_run_flag in %(ars_run_type_list)s and manual_run in %(manual_run_flag)s\n", "        )\n", "\"\"\"\n", "\n", "sto_dt_df = pd.read_sql_query(\n", "    sto_created_at_query,\n", "    redshift,\n", "    params={\n", "        \"ars_run_type_list\": tuple(run_type_list),\n", "        \"manual_run_flag\": mnf,\n", "        \"input_date\": today,\n", "    },\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if sto_dt_df.shape[0] > 0:\n", "    pass\n", "else:\n", "    sys.exit()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_id_list1 = list(sto_dt_df[\"run_id\"].unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(run_id_list1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def split(a, n):\n", "    k, m = divmod(len(a), n)\n", "    return (a[i * k + min(i, m) : (i + 1) * k + min(i + 1, m)] for i in range(n))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_time"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hour = current_time.hour"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hour"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_n = current_time.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_id_query = \"\"\"\n", "                select\n", "                distinct\n", "                run_id\n", "                , started_at_ist\n", "                , completed_at_ist\n", "                from\n", "                (select run_id\n", "                , completed_at completed_at_utc\n", "                , started_at started_at_utc\n", "                , convert_tz(completed_at,'+00:00','+05:30') completed_at_ist\n", "                , convert_tz(started_at,'+00:00','+05:30') started_at_ist\n", "                    from ars.job_run \n", "                    where  RUN_ID IN %(run_id_list)s\n", "                    and JSON_EXTRACT(simulation_params, '$.run') in (\"ars_lite\")\n", "                ) r\n", "                    \"\"\"\n", "\n", "run_id_df2 = pd.read_sql_query(\n", "    run_id_query, pos_con, params={\"run_id_list\": run_id_list1}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_id_df2[\"completed_at_date\"] = run_id_df2[\"completed_at_ist\"].dt.strftime(\"%Y-%m-%d\")\n", "run_id_df2[\"completed_at_hour\"] = run_id_df2[\"completed_at_ist\"].dt.strftime(\"%H\")\n", "run_id_df2[\"completed_at_hour\"] = run_id_df2[\"completed_at_hour\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_id_df = run_id_df2[\n", "    (run_id_df2[\"completed_at_date\"].isin([date_n]))\n", "    & (run_id_df2[\"completed_at_hour\"].isin([hour]))\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if run_id_df.shape[0] > 0:\n", "    pass\n", "else:\n", "    sys.exit()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["list2 = tuple(run_id_df[\"run_id\"].unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start = time.time()\n", "transfer_optimisation_query = \"\"\"\n", "select frontend_outlet_id,\n", "        backend_outlet_id,\n", "        sto_quantity v1_quantity,\n", "        sto_quantity_post_truncation_in_case v2_quantity,\n", "        run_id,\n", "        item_id,\n", "        quantity_drops\n", "        from ars.transfers_optimization_results_v2 x\n", "        where run_id in %(run_id_list)s           \n", "\"\"\"\n", "transfer_opt_df = pd.read_sql_query(\n", "    transfer_optimisation_query, pos_con, params={\"run_id_list\": list2}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_id_df[\"run_id\"].nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["faci_out_query = \"\"\"\n", "select distinct id, facility_id from retail.console_outlet\n", "\"\"\"\n", "facility_outlet_mapping = pd.read_sql_query(faci_out_query, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_name_query = \"\"\"\n", "select u.id frontend_facility_id, name facility_name from crates.facility u\n", "                        join (select id, max(updated_at) updated_at from crates.facility group by 1) p on u.id=p.id and u.updated_at=p.updated_at\"\"\"\n", "\n", "facility_name_df = pd.read_sql_query(facility_name_query, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dark_store_list_query = \"\"\"\n", "select distinct facility_id from retail.console_outlet where business_type_id=7\"\"\"\n", "\n", "dark_store_list_df = pd.read_sql_query(dark_store_list_query, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["zone_mapping_query = \"\"\"select facility_id\n", ", max(zone) zone2\n", "from metrics.outlet_zone_mapping\n", "group by 1\n", "\"\"\"\n", "\n", "zone_df = pd.read_sql_query(zone_mapping_query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["zone_df = zone_df.astype({\"facility_id\": float}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_fe = pd.merge(\n", "    transfer_opt_df,\n", "    facility_outlet_mapping.rename(\n", "        columns={\"id\": \"frontend_outlet_id\", \"facility_id\": \"frontend_facility_id\"}\n", "    ),\n", "    on=[\"frontend_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "\n", "base_be = pd.merge(\n", "    base_fe,\n", "    facility_outlet_mapping.rename(\n", "        columns={\"id\": \"backend_outlet_id\", \"facility_id\": \"backend_facility_id\"}\n", "    ),\n", "    on=[\"backend_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "\n", "base_be = base_be[\n", "    base_be[\"frontend_facility_id\"].isin(dark_store_list_df[\"facility_id\"])\n", "]\n", "\n", "\n", "base_zone = pd.merge(\n", "    base_be,\n", "    zone_df.rename(columns={\"facility_id\": \"frontend_facility_id\"}),\n", "    on=\"frontend_facility_id\",\n", "    how=\"left\",\n", ")\n", "\n", "base_fe_name = pd.merge(\n", "    base_zone, facility_name_df, on=\"frontend_facility_id\", how=\"left\"\n", ")\n", "\n", "base_be_name = pd.merge(\n", "    base_fe_name,\n", "    facility_name_df.rename(\n", "        columns={\n", "            \"frontend_facility_id\": \"backend_facility_id\",\n", "            \"facility_name\": \"backend_facility_name\",\n", "        }\n", "    ),\n", "    on=\"backend_facility_id\",\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_final_frame2 = pd.merge(\n", "    base_be_name,\n", "    sto_dt_df,\n", "    on=[\"frontend_facility_id\", \"backend_facility_id\", \"run_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df2 = pd.merge(base_final_frame2, run_id_df, on=\"run_id\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = df2[\n", "    [\n", "        \"zone2\",\n", "        \"backend_facility_id\",\n", "        \"frontend_facility_id\",\n", "        \"facility_name\",\n", "        \"backend_facility_name\",\n", "        \"frontend_outlet_id\",\n", "        \"backend_outlet_id\",\n", "        \"run_id\",\n", "        \"item_id\",\n", "        \"v1_quantity\",\n", "        \"v2_quantity\",\n", "        \"quantity_drops\",\n", "        \"indent\",\n", "        \"started_at_ist\",\n", "    ]\n", "].rename(\n", "    columns={\n", "        \"started_at_ist\": \"ars_started_at\",\n", "        \"facility_name\": \"frontend_facility_name\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["json_cols = [\"quantity_drops\"]\n", "import json\n", "\n", "\n", "def clean_json(x):\n", "    \"Create apply function for decoding JSON\"\n", "    return json.loads(x)\n", "\n", "\n", "# Apply the function column wise to each column of interest\n", "for x in json_cols:\n", "    df[x] = df[x].apply(clean_json)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"inward_drop\"] = df[\"quantity_drops\"].apply(lambda x: x[\"inward_drop\"])\n", "df[\"storage_drop\"] = df[\"quantity_drops\"].apply(lambda x: x[\"storage_drop\"])\n", "df[\"truck_load_drop\"] = df[\"quantity_drops\"].apply(lambda x: x[\"truck_load_drop\"])\n", "df[\"picking_capacity_sku_drop\"] = df[\"quantity_drops\"].apply(\n", "    lambda x: x[\"picking_capacity_sku_drop\"]\n", ")\n", "df[\"picking_capacity_quantity_drop\"] = df[\"quantity_drops\"].apply(\n", "    lambda x: x[\"picking_capacity_quantity_drop\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.rename(columns={\"zone2\": \"zone\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"created_at\"] = date_n\n", "df[\"hour\"] = hour"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"run_id\"].nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"zone\", \"type\": \"varchar\", \"description\": \"zone\"},\n", "    {\n", "        \"name\": \"backend_facility_id\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend_facility_id\",\n", "    },\n", "    {\n", "        \"name\": \"frontend_facility_id\",\n", "        \"type\": \"float\",\n", "        \"description\": \"frontend_facility_id\",\n", "    },\n", "    {\n", "        \"name\": \"frontend_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"frontend_facility_name\",\n", "    },\n", "    {\n", "        \"name\": \"backend_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"backend_facility_name\",\n", "    },\n", "    {\"name\": \"frontend_outlet_id\", \"type\": \"int\", \"description\": \"frontend_outlet_id\"},\n", "    {\"name\": \"backend_outlet_id\", \"type\": \"int\", \"description\": \"backend_outlet_id\"},\n", "    {\"name\": \"run_id\", \"type\": \"varchar\", \"description\": \"run_id\"},\n", "    {\"name\": \"item_id\", \"type\": \"varchar\", \"description\": \"item_id\"},\n", "    {\"name\": \"v1_quantity\", \"type\": \"float\", \"description\": \"v1_quantity\"},\n", "    {\"name\": \"v2_quantity\", \"type\": \"float\", \"description\": \"v2_quantity\"},\n", "    {\"name\": \"quantity_drops\", \"type\": \"varchar\", \"description\": \"quantity_drops\"},\n", "    {\"name\": \"indent\", \"type\": \"varchar\", \"description\": \"indent\"},\n", "    {\"name\": \"ars_started_at\", \"type\": \"timestamp\", \"description\": \"indent\"},\n", "    {\"name\": \"inward_drop\", \"type\": \"float\", \"description\": \"inward_drop\"},\n", "    {\"name\": \"storage_drop\", \"type\": \"float\", \"description\": \"storage_drop\"},\n", "    {\"name\": \"truck_load_drop\", \"type\": \"float\", \"description\": \"truck_load_drop\"},\n", "    {\n", "        \"name\": \"picking_capacity_sku_drop\",\n", "        \"type\": \"float\",\n", "        \"description\": \"picking_capacity_sku_drop\",\n", "    },\n", "    {\n", "        \"name\": \"picking_capacity_quantity_drop\",\n", "        \"type\": \"float\",\n", "        \"description\": \"picking_capacity_quantity_drop\",\n", "    },\n", "    {\"name\": \"created_at\", \"type\": \"varchar\", \"description\": \"created_at\"},\n", "    {\"name\": \"hour\", \"type\": \"int\", \"description\": \"hour\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"ars_truncation_rca_l7_raw_data_hourly_refresh_v3\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"run_id\", \"frontend_facility_id\", \"item_id\"],\n", "    \"sortkey\": [\"run_id\", \"created_at\", \"hour\"],\n", "    \"incremental_key\": [\"item_id\"],\n", "    \"load_type\": \"append\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"Database for RCA report of ARS validation\",  # Description of the table being sent to redshift\n", "}\n", "\n", "pb.to_redshift(df, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}