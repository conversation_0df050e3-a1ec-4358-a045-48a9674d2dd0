{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import math\n", "import time\n", "import random\n", "import logging\n", "import warnings\n", "from collections import defaultdict\n", "from datetime import datetime, date, timedelta\n", "import numpy as np\n", "import pandas as pd\n", "import requests\n", "import pencilbox as pb\n", "import sqlalchemy as sqla\n", "from sklearn.externals import joblib\n", "from sklearn.metrics import mean_squared_error\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.model_selection import train_test_split\n", "\n", "pd.set_option(\"display.max_columns\", 50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims = pb.get_connection(\"ims\")\n", "retail = pb.get_connection(\"retail\")\n", "redshift = pb.get_connection(\"redshift\")\n", "capacity_system = pb.get_connection(\"supply_orchestrator\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["today = (datetime.today() + <PERSON><PERSON>ta(hours=5, minutes=30)).date()\n", "\n", "sdate = today + <PERSON><PERSON><PERSON>(1)\n", "edate = today + <PERSON><PERSON><PERSON>(9)\n", "\n", "days = pd.date_range(sdate, edate - timedelta(days=1), freq=\"d\")\n", "date = days.to_frame(index=False)\n", "date.columns = [\"delivery_date\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["input = pb.from_sheets(\"1sazGyWriKPuJdi1tqkeVvw1MMjJ1fk5MExgwNpEwwOA\", \"Input\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# capping = list(filter(None, input.capping.values))\n", "# capping = float(capping[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_id_list = set(filter(None, input.item_id.values))\n", "outlet_id_list = set(filter(None, input.outlet_id.values))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_id_list = \", \".join(item_id_list)\n", "outlet_id_list = \", \".join(outlet_id_list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items = list(filter(None, input.item_id.values))\n", "outlets = list(filter(None, input.outlet_id.values))\n", "capping = list(filter(None, input.capping1.values))\n", "saleable_guidelines = list(filter(None, input.saleable_guidelines.values))\n", "qpc_multiplier = list(filter(None, input.qpc_multiplier.values))\n", "\n", "itm_outlets = pd.DataFrame(\n", "    list(zip(items, outlets, capping, saleable_guidelines, qpc_multiplier)),\n", "    columns=[\n", "        \"item_id\",\n", "        \"outlet_id\",\n", "        \"capping\",\n", "        \"saleable_guidelines\",\n", "        \"qpc_multiplier\",\n", "    ],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["itm_outlets.item_id = itm_outlets.item_id.astype(int)\n", "itm_outlets.outlet_id = itm_outlets.outlet_id.astype(int)\n", "itm_outlets.capping = itm_outlets.capping.astype(float)\n", "itm_outlets.saleable_guidelines = itm_outlets.saleable_guidelines.astype(int)\n", "itm_outlets.qpc_multiplier = itm_outlets.qpc_multiplier.astype(float)\n", "\n", "# len(itm_outlets)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# itm_details_query = \"\"\"\n", "#     SELECT item_id, name as item_name, inner_case_size, shelf_life, outward_guidelines, 1 as tmp\n", "#     FROM rpc.product_product pp2\n", "#     WHERE pp2.id in (SELECT max(id) as id FROM rpc.product_product pp\n", "#         WHERE pp.active=1 and pp.approved=1 GROUP BY item_id)\n", "#     AND item_id in ({item_id})\n", "# \"\"\".format(\n", "#     item_id=item_id_list\n", "# )\n", "\n", "# outlet_details_query = \"\"\"\n", "#     select id as outlet_id, name as outlet_name, facility_id, 1 as tmp\n", "#     from retail.console_outlet\n", "#     where id in ({outlet_id})\n", "# \"\"\".format(\n", "#     outlet_id=outlet_id_list\n", "# )\n", "\n", "# itm_details = pd.read_sql_query(itm_details_query, retail)\n", "# outlet_details = pd.read_sql_query(outlet_details_query, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# itm_outlet_details = pd.merge(itm_details, outlet_details, on=\"tmp\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# itm_outlet_details_query = \"\"\"select c.item_id, c.outlet_id, c.outlet_name, c.facility_id, c.item_name, c.inner_case_size, c.shelf_life, c.outward_guidelines, avg(lp.landing_price) as landing_price from\n", "# (select a.*, b.outlet_id, outlet_name, facility_id\n", "# from\n", "# (\n", "# (SELECT item_id, variant_id, name as item_name, inner_case_size, shelf_life, outward_guidelines\n", "#     FROM rpc.product_product pp2\n", "#     WHERE pp2.id in (SELECT max(id) as id FROM rpc.product_product pp\n", "#         WHERE pp.active=1 and pp.approved=1 GROUP BY item_id)\n", "#     AND item_id in ({item_id})) a\n", "\n", "# CROSS JOIN\n", "\n", "# (select id as outlet_id, name as outlet_name, facility_id\n", "# from retail.console_outlet\n", "# where id in ({outlet_id})) b\n", "\n", "# )) c\n", "# LEFT JOIN ims.ims_inventory_landing_price lp on c.variant_id = lp.variant_id and c.outlet_id = lp.outlet_id\n", "\n", "# group by 1,2,3,4,5,6,7,8\n", "\n", "# \"\"\".format(item_id = item_id_list, outlet_id = outlet_id_list)\n", "\n", "# itm_outlet_details = pd.read_sql_query(itm_outlet_details_query, retail)\n", "\n", "\n", "itm_outlet_details_query = \"\"\"\n", "\n", "with item_product_mapping as (with PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (CASE\n", "               WHEN C1.NAME = C.name THEN C2.name\n", "               ELSE C1.name\n", "           END) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name AS product_type\n", "   from lake_cms.gr_product P\n", "   INNER join lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER join lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER join lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER join lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   INNER join lake_cms.gr_product_type pt ON p.type_id=pt.id )\n", "\n", "  SELECT item_id,\n", "          product_id,\n", "          (cat.product || ' ' || cat.unit) AS name,\n", "          cat.L0,\n", "          cat.l1,\n", "          cat.l2,\n", "          cat.brand,\n", "          cat.manf,\n", "          cat.product_type\n", "  from  lake_rpc.item_product_mapping  rpc\n", "  INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "  AND rpc.offer_id IS NULL\n", "  AND rpc.item_id IS NOT NULL\n", "  AND rpc.product_id IS NOT NULL)\n", "\n", "select l0, l1, l2, c.item_id, c.outlet_id, c.outlet_name, c.facility_id, max(c.item_name) as item_name, max(c.inner_case_size) as inner_case_size, max(c.shelf_life) as shelf_life, avg(c.variant_mrp) as mrp, max(c.variant_uom_text) as item_uom, avg(lp.landing_price) as landing_price, max(c.outward_guidelines) as outward_guidelines, 1 as tmp from\n", "(select a.*, b.outlet_id, outlet_name, facility_id\n", "from \n", "(\n", "(SELECT item_id, variant_id, name as item_name, inner_case_size,shelf_life, variant_mrp, variant_uom_text, outward_guidelines\n", "FROM lake_rpc.product_product pp2\n", "WHERE pp2.id in (SELECT max(id) as id FROM lake_rpc.product_product pp\n", "WHERE pp.active=1 and pp.approved=1 GROUP BY item_id)\n", "AND item_id in ({item_id})) a\n", "\n", "CROSS JOIN\n", "\n", "(select id as outlet_id, name as outlet_name, facility_id\n", "from lake_retail.console_outlet\n", "where id in ({outlet_id})) b\n", "\n", ")) c\n", "\n", "LEFT JOIN (select a.item_id, a.outlet_id, max(landing_price) as landing_price\n", "from consumer.weighted_landing_price a\n", "inner join (select item_id, outlet_id, max(dt_ist) as dt_ist from consumer.weighted_landing_price group by 1,2) b\n", "on a.item_id = b.item_id and a.outlet_id = b.outlet_id and a.dt_ist = b.dt_ist\n", "group by 1,2) lp on c.item_id = lp.item_id and c.outlet_id = lp.outlet_id\n", "\n", "LEFT JOIN item_product_mapping ipm on c.item_id = ipm.item_id\n", "\n", "group by 1,2,3,4,5,6,7\n", "\n", "\"\"\".format(\n", "    item_id=item_id_list, outlet_id=outlet_id_list\n", ")\n", "\n", "itm_outlet_details = pd.read_sql(itm_outlet_details_query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["itm_outlet_details = itm_outlet_details.merge(\n", "    itm_outlets, on=[\"item_id\", \"outlet_id\"], how=\"inner\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["itm_outlet_details.landing_price = np.where(\n", "    itm_outlet_details.landing_price.isna(),\n", "    itm_outlet_details.mrp * 0.8,\n", "    itm_outlet_details.landing_price,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["itm_outlet_details.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "select item_id, facility_id, app_live\n", "from consumer.rpc_daily_availability\n", "where order_date = (select max(order_date) from consumer.rpc_daily_availability)\n", "and item_id in ({item_id})\n", "\"\"\".format(\n", "    item_id=item_id_list\n", ")\n", "\n", "app_live = pd.read_sql(sql, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["itm_outlet_details = itm_outlet_details.merge(\n", "    app_live, on=[\"item_id\", \"facility_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "SELECT \n", "     iii.outlet_id,\n", "     iii.item_id,\n", "     (case when iii.upper_limit > 0 then 1 else 0 end) as is_on_metering,\n", "     iii.quantity as onshelf_inventory,\n", "     (iii.quantity - COALESCE(sum(case when iibi.blocked_type in (1,2,4) then iibi.quantity else 0 end),0)) as net_avail_qty\n", "FROM\n", "ims.ims_item_inventory iii\n", "     LEFT JOIN\n", "ims.ims_item_blocked_inventory iibi ON iii.item_id = iibi.item_id AND iii.outlet_id = iibi.outlet_id\n", "     INNER JOIN\n", "retail.warehouse_outlet_mapping wom on iii.outlet_id = wom.cloud_store_id\n", "     INNER JOIN\n", "retail.console_outlet co ON co.id=iii.outlet_id\n", "     INNER JOIN\n", "retail.console_location cl ON cl.id = co.tax_location_id\n", "     INNER JOIN\n", "(SELECT item_id, name FROM rpc.product_product pp2 \n", "WHERE pp2.id in (\n", "SELECT max(id) as id FROM rpc.product_product pp \n", "WHERE pp.active=1 and pp.approved=1 GROUP BY item_id)\n", ") product ON product.item_id = iii.item_id\n", "WHERE iii.outlet_id in ({outlet_id})\n", "AND iii.item_id in ({item_id})\n", "AND iii.active = 1 \n", "GROUP BY 1,2,3,4;\n", "\n", "\"\"\".format(\n", "    item_id=item_id_list, outlet_id=outlet_id_list\n", ")\n", "\n", "metering = pd.read_sql_query(sql, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["itm_outlet_details = itm_outlet_details.merge(\n", "    metering, on=[\"item_id\", \"outlet_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["itm_outlet_details.app_live = np.where(\n", "    itm_outlet_details.app_live.isna(), 0, itm_outlet_details.app_live\n", ")\n", "itm_outlet_details.is_on_metering = np.where(\n", "    itm_outlet_details.is_on_metering.isna(), 0, itm_outlet_details.is_on_metering\n", ")\n", "\n", "itm_outlet_details.is_on_metering = itm_outlet_details.is_on_metering.astype(int)\n", "itm_outlet_details.app_live = itm_outlet_details.app_live.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["buckets = (itm_outlet_details[\"saleable_guidelines\"].unique() + 1).tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["buckets.sort()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["buckets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date[\"tmp\"] = 1\n", "\n", "itm_outlets_date = pd.merge(itm_outlet_details, date, on=\"tmp\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class LiquidationAgingInventory(object):\n", "    def __init__(self, day, buckets):\n", "        try:\n", "            self.date = day\n", "            self.buckets = buckets\n", "            self.csv_rows = []\n", "            self.fetch_outlets_and_compute()\n", "\n", "        except Exception as e:\n", "            print(e)\n", "\n", "    def fetch_outlets_and_compute(self):\n", "        outlets = self.get_outlets()\n", "        for row in outlets.itertuples():\n", "            outlet_id = int(row.outlet_id)\n", "            self.compute_aging(outlet_id)\n", "\n", "    @staticmethod\n", "    def daterange(start_date, end_date):\n", "        for n in range(int((end_date - start_date).days)):\n", "            yield start_date + timedelta(n)\n", "\n", "    def get_outlets(self):\n", "        query = \"\"\"select distinct outlet_id from retail.console_outlet_cms_store cocs \n", "        inner join retail.console_outlet co on co.id=cocs.outlet_id \n", "        where cocs.active=1 and co.active=1 and co.device_id <> 47 and co.id in ({outlet_id})\n", "        union select id from retail.console_outlet where id in (895,580,467);\"\"\".format(\n", "            outlet_id=outlet_id_list\n", "        )\n", "        rows = pd.read_sql_query(sql=query, con=retail)\n", "\n", "        return rows\n", "\n", "    def get_item_level_bucket_isd_quantities(\n", "        self, variant_datas, bucket_days, item_blocked_quantity\n", "    ):\n", "\n", "        bucket_isd_quantities = [0 for item in bucket_days]\n", "        for variant_data in variant_datas:\n", "            variant_bucket_isd_quantities = variant_data[\"bucket_isd_quantities\"]\n", "            for i in range(0, len(bucket_days)):\n", "                bucket_isd_quantities[i] += variant_bucket_isd_quantities[i]\n", "        #         if item_blocked_quantity > 0:\n", "        #             i = len(bucket_isd_quantities) - 1\n", "        #             while i > -1 and item_blocked_quantity > 0:\n", "        #                 bucket_inventory = bucket_isd_quantities[i]\n", "        #                 inventory = min(bucket_inventory, item_blocked_quantity)\n", "        #                 item_blocked_quantity -= inventory\n", "        #                 bucket_isd_quantities[i] -= inventory\n", "        #                 i -= 1\n", "        return bucket_isd_quantities\n", "\n", "    def get_item_variant_mrp(self, item_variants_data):\n", "\n", "        max_variant_quantity = 0\n", "        max_quantity_item_variant_mrp = float(\"-inf\")\n", "\n", "        item_variant_mrp_mapping = {}\n", "        for item_variant in item_variants_data:\n", "            if sum(item_variant.get(\"bucket_isd_quantities\")) > max_variant_quantity:\n", "                max_quantity_item_variant_mrp = item_variant.get(\"variant_mrp\")\n", "                max_variant_quantity = sum(item_variant.get(\"bucket_isd_quantities\"))\n", "        return max_quantity_item_variant_mrp\n", "\n", "    def generate_item_level_aggregate(\n", "        self, outlet_id, bucket_days, blocked_item_inventory_map\n", "    ):\n", "\n", "        for item_id in self.outlet_item_inventory_map:\n", "            item_variants_data = self.outlet_item_inventory_map[item_id]\n", "            item_variant_mrp = self.get_item_variant_mrp(item_variants_data)\n", "            item_bucket_isd_quantities = self.get_item_level_bucket_isd_quantities(\n", "                item_variants_data,\n", "                bucket_days,\n", "                blocked_item_inventory_map.get(item_id, 0),\n", "            )\n", "            csv_row = [str(item_id), str(outlet_id), str(item_variant_mrp), self.date]\n", "            csv_row.extend(item_bucket_isd_quantities)\n", "            self.csv_rows.append(csv_row)\n", "        df = pd.DataFrame.from_records(self.csv_rows)\n", "        return df\n", "\n", "    def get_outlet_item_blocked_inventories_map(self, outlet_id, item_ids):\n", "\n", "        item_ids = \",\".join(item_ids)\n", "        query = \"\"\"select sum(quantity) quantity, item_id from ims.ims_item_blocked_inventory where outlet_id = {outlet_id} and item_id in ({item_ids}) and quantity>0 and blocked_type!=3 group by item_id;\"\"\"\n", "        query = query.format(outlet_id=outlet_id, item_ids=item_ids)\n", "        datas = pd.read_sql_query(sql=query, con=retail)\n", "        blocked_item_inventory_map = {}\n", "        for item_data in datas.itertuples():\n", "            blocked_item_inventory_map[item_data.item_id] = int(item_data.quantity)\n", "        return blocked_item_inventory_map\n", "\n", "    def add_variant_in_item_inventory_map(self, item_id, variant_data):\n", "        if item_id not in self.outlet_item_inventory_map:\n", "            self.outlet_item_inventory_map[item_id] = []\n", "        self.outlet_item_inventory_map[item_id].append(variant_data)\n", "\n", "    def get_isd_quantity_for_variant_with_buckets(\n", "        self,\n", "        variant_id,\n", "        variant_quantity,\n", "        bucket_days,\n", "        pending_putaway_inventory,\n", "        bucket_days_variant_quantity_map,\n", "    ):\n", "\n", "        try:\n", "            bucket_isd_quantities = [0 for item in bucket_days]\n", "            remaining_variant_quantity = variant_quantity\n", "\n", "            index = 0\n", "            while index < len(bucket_days):\n", "                if variant_id in bucket_days_variant_quantity_map[index]:\n", "                    bucket_quantity = bucket_days_variant_quantity_map[index][\n", "                        variant_id\n", "                    ]\n", "                else:\n", "                    bucket_quantity = 0\n", "                if remaining_variant_quantity > bucket_quantity:\n", "                    bucket_isd_quantities[index] = bucket_quantity\n", "                else:\n", "                    bucket_isd_quantities[index] = remaining_variant_quantity\n", "                remaining_variant_quantity -= bucket_quantity\n", "                if remaining_variant_quantity <= 0:\n", "                    break\n", "\n", "                index += 1\n", "\n", "            if remaining_variant_quantity < 0:\n", "                remaining_variant_quantity = 0\n", "\n", "            bucket_isd_quantities.append(remaining_variant_quantity)\n", "            if pending_putaway_inventory > 0:\n", "                i = 0\n", "                while i < len(bucket_isd_quantities) and pending_putaway_inventory > 0:\n", "                    bucket_inventory = bucket_isd_quantities[i]\n", "                    inventory = min(bucket_inventory, pending_putaway_inventory)\n", "                    pending_putaway_inventory -= inventory\n", "                    bucket_isd_quantities[i] -= inventory\n", "                    i += 1\n", "            return bucket_isd_quantities\n", "        except Exception as e:\n", "            raise e\n", "\n", "    def get_in_clause_string_query(self, str_list):\n", "        query_value = \"\"\n", "        if str_list:\n", "            query_value = \"'\" + \"','\".join(str_list) + \"'\"\n", "        return query_value\n", "\n", "    def fetch_isd_quantity_for_variants(\n", "        self, outlet_id, variant_ids, start_date_time, end_date_time\n", "    ):\n", "        query = \"\"\"select COALESCE(sum(delta),0) as quantity, variant_id from ims.ims_inventory_stock_details isd inner join ims.ims_inward_invoice iii on isd.grn_id = iii.grn_id \n", "        where isd.outlet_id = {outlet_id} and isd.variant_id in ({variant_ids}) and iii.source_type in (1,2) and isd.created_at between \"{start_date_time}\" and \"{end_date_time}\" group by variant_id;\"\"\"\n", "        query = query.format(\n", "            outlet_id=outlet_id,\n", "            variant_ids=self.get_in_clause_string_query(variant_ids),\n", "            start_date_time=start_date_time,\n", "            end_date_time=end_date_time,\n", "        )\n", "        rows = pd.read_sql_query(sql=query, con=retail)\n", "        return rows\n", "\n", "    def get_isd_bucket_detail_for_variant_quantity_map(\n", "        self, outlet_id, variant_ids, start_date, end_date\n", "    ):\n", "        isd_bucket_variants = self.fetch_isd_quantity_for_variants(\n", "            outlet_id, variant_ids, start_date, end_date\n", "        )\n", "        variant_quantity_map = {}\n", "        for isd_bucket_variant in isd_bucket_variants.itertuples():\n", "            variant_quantity_map[isd_bucket_variant.variant_id] = int(\n", "                isd_bucket_variant.quantity\n", "            )\n", "        return variant_quantity_map\n", "\n", "    def get_isd_bucket_details_for_variants(self, outlet_id, variant_ids, bucket_days):\n", "        index = 0\n", "        bucket_days_variant_quantity_map = []\n", "\n", "        # As the inventory calculation have to made on date basis we changed the time stamp to \"YYYY-MM-DD 18:29:59\" .Previously it was on timestamp basis.\n", "\n", "        ref_date_time = datetime.strptime(\n", "            self.date.strftime(\"%Y-%m-%d 18:29:59\"),\n", "            \"%Y-%m-%d %H:%M:%S\",\n", "        )\n", "\n", "        end_date = ref_date_time\n", "        while index < len(bucket_days):\n", "            start_date = ref_date_time + timedelta(days=-bucket_days[index])\n", "            bucket_days_variant_quantity_map.append(\n", "                self.get_isd_bucket_detail_for_variant_quantity_map(\n", "                    outlet_id, variant_ids, start_date, end_date\n", "                )\n", "            )\n", "            end_date = start_date\n", "            index += 1\n", "        return bucket_days_variant_quantity_map\n", "\n", "    def fetch_outlet_pending_putaway_variants(self, outlet_id):\n", "\n", "        query = \"\"\"select variant_id, outlet_id, quantity from ims.ims_good_inventory where outlet_id = {outlet_id} and inventory_update_type_id = 28 and quantity > 0;\"\"\"\n", "        query = query.format(outlet_id=outlet_id)\n", "        active_variants = pd.read_sql_query(sql=query, con=retail)\n", "        pending_putaway_inventory_map = {}\n", "        for active_variant in active_variants.itertuples():\n", "            pending_putaway_inventory_map[active_variant.variant_id] = int(\n", "                active_variant.quantity\n", "            )\n", "        return pending_putaway_inventory_map\n", "\n", "    def fetch_outlet_variants(self, outlet_id):\n", "        query = \"\"\"select pp.item_id,pp.variant_mrp, ii.variant_id, ii.outlet_id, ii.quantity from \n", "        (select variant_id, outlet_id, sum(quantity) quantity from \n", "        (select variant_id, outlet_id, quantity from ims.ims_inventory where outlet_id = {outlet_id} and quantity > 0 and active = 1 \n", "        union all select variant_id, outlet_id, quantity from ims.ims_good_inventory where outlet_id = {outlet_id} and quantity > 0 and active = 1) inventory group by variant_id, outlet_id) \n", "        ii inner join rpc.product_product pp on pp.variant_id = ii.variant_id\n", "        where item_id in ({item_id});\"\"\"\n", "        query = query.format(outlet_id=outlet_id, item_id=item_id_list)\n", "        rows = pd.read_sql_query(sql=query, con=retail)\n", "        return rows\n", "\n", "    def compute_aging(self, outlet_id):\n", "        try:\n", "            self.outlet_item_inventory_map = {}\n", "            item_ids = set()\n", "            active_variants = self.fetch_outlet_variants(outlet_id)\n", "            if len(active_variants) == 0:\n", "                return\n", "            pending_putaway_inventory_map = self.fetch_outlet_pending_putaway_variants(\n", "                outlet_id\n", "            )\n", "            #             self.curr_date_time = day\n", "            bucket_days = self.buckets\n", "            variant_ids = [row.variant_id for row in active_variants.itertuples()]\n", "            bucket_days_variant_quantity_map = self.get_isd_bucket_details_for_variants(\n", "                outlet_id, variant_ids, bucket_days\n", "            )\n", "            for active_variant in active_variants.itertuples():\n", "                variant_id = active_variant.variant_id\n", "                variant_mrp = active_variant.variant_mrp\n", "                variant_quantity = int(active_variant.quantity)\n", "                pending_putaway_inventory = pending_putaway_inventory_map.get(\n", "                    variant_id, 0\n", "                )\n", "\n", "                (\n", "                    bucket_isd_quantities\n", "                ) = self.get_isd_quantity_for_variant_with_buckets(\n", "                    variant_id,\n", "                    variant_quantity,\n", "                    bucket_days,\n", "                    pending_putaway_inventory,\n", "                    bucket_days_variant_quantity_map,\n", "                )\n", "                variant_data = {\n", "                    \"variant_id\": variant_id,\n", "                    \"variant_mrp\": variant_mrp,\n", "                    \"bucket_isd_quantities\": bucket_isd_quantities,\n", "                }\n", "                item_ids.add(str(active_variant.item_id))\n", "                self.add_variant_in_item_inventory_map(\n", "                    active_variant.item_id, variant_data\n", "                )\n", "            blocked_item_inventory_map = self.get_outlet_item_blocked_inventories_map(\n", "                outlet_id, item_ids\n", "            )\n", "            a = self.generate_item_level_aggregate(\n", "                outlet_id, bucket_days, blocked_item_inventory_map\n", "            )\n", "            return a\n", "        except Exception as e:\n", "            print(e)\n", "\n", "\n", "# LiquidationAgingInventory()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x1 = LiquidationAgingInventory(date.delivery_date[0], buckets)\n", "x2 = LiquidationAgingInventory(date.delivery_date[1], buckets)\n", "x3 = LiquidationAgingInventory(date.delivery_date[2], buckets)\n", "x4 = LiquidationAgingInventory(date.delivery_date[3], buckets)\n", "x5 = LiquidationAgingInventory(date.delivery_date[4], buckets)\n", "x6 = LiquidationAgingInventory(date.delivery_date[5], buckets)\n", "x7 = LiquidationAgingInventory(date.delivery_date[6], buckets)\n", "x8 = LiquidationAgingInventory(date.delivery_date[7], buckets)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data_1 = pd.DataFrame(x1.csv_rows)\n", "inv_data_2 = pd.DataFrame(x2.csv_rows)\n", "inv_data_3 = pd.DataFrame(x3.csv_rows)\n", "inv_data_4 = pd.DataFrame(x4.csv_rows)\n", "inv_data_5 = pd.DataFrame(x5.csv_rows)\n", "inv_data_6 = pd.DataFrame(x6.csv_rows)\n", "inv_data_7 = pd.DataFrame(x7.csv_rows)\n", "inv_data_8 = pd.DataFrame(x8.csv_rows)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data_1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_outward_days = itm_outlet_details[\n", "    [\"item_id\", \"saleable_guidelines\"]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def format_df(data):\n", "    data.columns = [\"item_id\", \"outlet_id\", \"mrp\", \"timestamp\"] + buckets\n", "    data[\"delivery_date\"] = data[\"timestamp\"]\n", "    data[\"current_inventory\"] = 0\n", "    data.item_id = data.item_id.astype(int)\n", "\n", "    data = data.merge(item_outward_days, on=[\"item_id\"], how=\"left\")\n", "\n", "    for i in range(0, len(data)):\n", "        for col in buckets:\n", "            if col <= data.loc[i, \"saleable_guidelines\"] + 1:\n", "                data.loc[i, \"current_inventory\"] = (\n", "                    data.loc[i, \"current_inventory\"] + data.loc[i, col]\n", "                )\n", "\n", "    data.drop(\n", "        [\n", "            \"mrp\",\n", "            \"timestamp\",\n", "            \"saleable_guidelines\",\n", "        ]\n", "        + buckets,\n", "        inplace=True,\n", "        axis=1,\n", "    )\n", "\n", "    return data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data_1 = format_df(inv_data_1)\n", "inv_data_2 = format_df(inv_data_2)\n", "inv_data_3 = format_df(inv_data_3)\n", "inv_data_4 = format_df(inv_data_4)\n", "inv_data_5 = format_df(inv_data_5)\n", "inv_data_6 = format_df(inv_data_6)\n", "inv_data_7 = format_df(inv_data_7)\n", "inv_data_8 = format_df(inv_data_8)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data = inv_data_1.append(\n", "    [inv_data_2, inv_data_3, inv_data_4, inv_data_5, inv_data_6, inv_data_7, inv_data_8]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data = (\n", "    inv_data.groupby([\"item_id\", \"outlet_id\", \"delivery_date\"])[\"current_inventory\"]\n", "    .sum()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data.outlet_id = inv_data.outlet_id.astype(int)\n", "inv_data.item_id = inv_data.item_id.astype(int)\n", "\n", "inv_data = itm_outlets_date.merge(\n", "    inv_data, on=[\"item_id\", \"outlet_id\", \"delivery_date\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data.current_inventory = np.where(\n", "    inv_data.current_inventory.isna(), 0, inv_data.current_inventory\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data[\"union\"] = inv_data[\"item_id\"].astype(str) + inv_data[\"outlet_id\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data[\"union\"] = inv_data[\"union\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tat_days = input[[\"union\", \"tat_days\"]]\n", "tat_days.union = tat_days.union.astype(int)\n", "tat_days.tat_days = tat_days.tat_days.astype(int)\n", "tat_days.tat_days = tat_days.tat_days + 1\n", "\n", "tat_days = tat_days.set_index(\"union\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d = tat_days.to_dict()\n", "d1 = d[\"tat_days\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print(d1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data = (\n", "    inv_data.groupby(\"union\")\n", "    .apply(lambda x: (x.nlargest(d1[x.name], \"union\")))\n", "    .reset_index(drop=True)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_query = \"\"\"\n", "select item_id, outlet_id, delivery_date, sum(case when status_id <> 5 then quantity else 0 end) as qty_ordered, count(distinct order_id) as carts,\n", "sum(case when status_id <> 5 then slotB_quantity else 0 end) as slotB_qty_blocked\n", "from(\n", "    select o.outlet as outlet_id, date(convert_tz(o.scheduled_at,\"+00:00\",\"+05:30\")) as delivery_date, \n", "    (case when extract(hour from convert_tz(o.scheduled_at,'+00:00','+05:30')) > 16 then oi.quantity end) as slotB_quantity,\n", "    o.order_id, oi.item_id, oi.quantity, o.status_id, os.name as status\n", "    from ims.ims_order_details o\n", "    left join ims.ims_order_items oi on o.id = oi.order_details_id\n", "    left join ims.ims_order_status os on o.status_id = os.id\n", "    where date(convert_tz(o.scheduled_at,\"+00:00\",\"+05:30\")) between current_date + interval 1 day and current_date + interval 4 day\n", "    and oi.item_id in ({item_id})\n", ") a\n", "group by 1,2,3\n", "\"\"\".format(\n", "    item_id=item_id_list\n", ")\n", "\n", "orders_placed = pd.read_sql_query(orders_query, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["billed_query = \"\"\"\n", "select item_id, outlet_id, delivery_date, sum(picked_quantity) as billed_qty from(\n", "    SELECT p.outlet_id,\n", "       date(convert_tz(p.max_scheduled_time,\"+00:00\",\"+05:30\")) AS delivery_date,\n", "       p.pick_list_id,\n", "       PLO.order_id,\n", "       PLI.item_id,\n", "       sum(PLI.picked_quantity) AS picked_quantity\n", "       \n", "    FROM warehouse_location.warehouse_pick_list p\n", "    INNER JOIN warehouse_location.warehouse_pick_list_item PLI ON PLI.pick_list_id=p.id AND PLI.state IN (2,4)\n", "    LEFT JOIN warehouse_location.warehouse_pick_list_order_mapping PLO ON PLO.pick_list_id=p.id \n", "    LEFT JOIN warehouse_location.warehouse_wave_pick_list_mapping m ON p.id = m.pick_list_id\n", "    LEFT JOIN warehouse_location.warehouse_wave w ON w.id = m.wave_id\n", "    \n", "    WHERE p.id>44404456 and\n", "    date(convert_tz(p.max_scheduled_time,\"+00:00\",\"+05:30\")) = date_add(date(convert_tz(current_date(),\"+00:00\",\"+05:30\")),interval 1 DAY)\n", "    and p.outlet_id in ({outlet_id})\n", "    and PLI.item_id in ({item_id})\n", "    AND p.state IN(10)\n", "    \n", "    GROUP BY 1,2,3,4,5) a\n", "group by 1,2,3\n", "\"\"\".format(\n", "    item_id=item_id_list, outlet_id=outlet_id_list\n", ")\n", "\n", "billed_list = pd.read_sql_query(billed_query, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["billed_list.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["billed_list.delivery_date = pd.to_datetime(billed_list.delivery_date)\n", "orders_placed.delivery_date = pd.to_datetime(orders_placed.delivery_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_placed = orders_placed.merge(\n", "    billed_list, on=[\"item_id\", \"outlet_id\", \"delivery_date\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_placed.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data.delivery_date = pd.to_datetime(inv_data.delivery_date)\n", "orders_placed.delivery_date = pd.to_datetime(orders_placed.delivery_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_orders = inv_data.merge(\n", "    orders_placed, on=[\"item_id\", \"outlet_id\", \"delivery_date\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_orders.head(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_orders = inv_orders.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_orders.head(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f_cpd_query = \"\"\"\n", "select item_id, outlet_id, avg(consumption) as avg_f_cpd from( \n", "    select item_id, o.cloud_store_id as outlet_id, consumption, date\n", "    from lake_snorlax.date_wise_consumption a\n", "    left join lake_retail.warehouse_outlet_mapping o on a.outlet_id = o.warehouse_id\n", "    where date(date) between current_date - interval '6 days' and current_date\n", "    and item_id in ({item_id})\n", ")\n", "where outlet_id in ({outlet_id})\n", "group by 1,2    \n", "\"\"\".format(\n", "    item_id=item_id_list, outlet_id=outlet_id_list\n", ")\n", "\n", "f_cpd = pd.read_sql_query(sql=f_cpd_query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_orders = inv_orders.merge(f_cpd, on=[\"item_id\", \"outlet_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_orders.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Item qpc"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# query = \"\"\"with checkout_details as\n", "# (\n", "# select\n", "# oo.order_id,\n", "# os.grofer_order_id,\n", "# oo.order_date\n", "# from\n", "# (\n", "# select\n", "# order_id,\n", "# grofer_order_id\n", "# from\n", "# (\n", "# select\n", "# order_id,\n", "# id as grofer_order_id,\n", "# dense_rank() over(partition by a.order_id order by a.update_ts::timestamp desc) as rnk\n", "# from\n", "#   (\n", "#   select distinct order_id,\n", "#   id,\n", "#   to_timestamp(os.update_ts, 'YYYY-MM-DD HH24:MI') as update_ts\n", "#   from lake_oms_bifrost.oms_suborder as os\n", "#   where os.type='RetailSuborder' and\n", "#   date(os.install_ts+ interval '330 minute') between current_date-interval '7 days' and  current_date-interval '1 days'\n", "#   and current_status not in ('REPROCURED','CANCELLED')\n", "#      ) as a\n", "#      )as b\n", "#     --  where rnk=1\n", "#      group by 1,2\n", "#      )os\n", "#      inner join\n", "#      (\n", "#      select id as order_id,\n", "#      (oo.install_ts+ interval '330 minute') as order_date\n", "#      from lake_oms_bifrost.oms_order as oo\n", "#      where\n", "#       date(oo.install_ts+ interval '330 minute') between current_date-interval '7 days' and  current_date-interval '1 days'\n", "#       and oo.type='RetailForwardOrder'\n", "#       and current_status not in ('REPROCURED','CANCELLED')\n", "#       ) oo\n", "#       on os.order_id=oo.order_id\n", "#       )\n", "\n", "#      ,\n", "#  order_details AS\n", "#  (SELECT DISTINCT c.order_id ,\n", "#                   c.grofer_order_id,\n", "#                   date(c.order_date) AS order_date,\n", "#                   cast(outlet AS int) AS outlet ,\n", "#                   facility_id,\n", "#                   item_id ::int,\n", "#                   product_id ,\n", "#                   quantity,\n", "#                   offer_id ,\n", "#                   mrp,\n", "#                   price\n", "#   FROM lake_ims.ims_order_details AS a\n", "#   inner join checkout_details as c on a.order_id=c.grofer_order_id\n", "#   LEFT JOIN lake_ims.ims_order_actuals AS b ON a.id=b.order_details_id\n", "#  inner JOIN lake_retail.console_outlet AS pco ON a.outlet=pco.id\n", "# inner join lake_retail.console_company_type rcc on pco.company_type_id=rcc.id\n", "# where upper(rcc.entity_type) like '%%B2B_INDEPENDENT%%'\n", "# and pco.active=1\n", "# and pco.device_id!=47\n", "# -- and item_id in (10015336)\n", "# and item_id is not null\n", "#         ),\n", "\n", "\n", "# last_7days_qpc as (select *, overall_qpc*1.000/live_days as avg_overall_qpc from(\n", "# select a.item_id, a.outlet, avg(a.qty) as avg_cpd, avg(a.carts) as avg_carts, avg(qpc) as avg_qpc, count(distinct a.order_date) as live_days, sum(a.qty::float/b.total_carts::float) as overall_qpc from(\n", "#     (select order_date, outlet, item_id, ROUND(sum(quantity),4) as qty, ROUND(count(distinct order_id),4) as carts, sum(quantity)/count(distinct order_id) as qpc from\n", "#     order_details\n", "#     where item_id in ({item_id})\n", "#     group by 1,2,3\n", "#     ) a\n", "\n", "#     left join\n", "\n", "#     (select order_date, outlet, count(distinct order_id)::float as total_carts from\n", "#     order_details\n", "#     group by 1,2) b\n", "\n", "#     on a.order_date = b.order_date and a.outlet = b.outlet\n", "# )\n", "# group by 1,2)),\n", "\n", "# last_3days_qpc as (select *, overall_qpc*1.000/live_days as avg_overall_qpc from(\n", "# select a.item_id, a.outlet, avg(a.qty) as avg_cpd, avg(a.carts) as avg_carts, avg(qpc) as avg_qpc, count(distinct a.order_date) as live_days, sum(a.qty/b.total_carts) as overall_qpc from(\n", "#     (select order_date, outlet, item_id, ROUND(sum(quantity),4) as qty, ROUND(count(distinct order_id),4) as carts, sum(quantity)/count(distinct order_id) as qpc from\n", "#     order_details\n", "#     where item_id in ({item_id})\n", "#     and order_date between current_date- interval '3 days' and current_date-interval '1 days'\n", "#     group by 1,2,3\n", "#     ) a\n", "\n", "#     left join\n", "\n", "#     (select order_date, outlet, count(distinct order_id)::float as total_carts from\n", "#     order_details\n", "#     where order_date between current_date- interval '3 days' and current_date-interval '1 days'\n", "#     group by 1,2) b\n", "\n", "#     on a.order_date = b.order_date and a.outlet = b.outlet\n", "# )\n", "# group by 1,2))\n", "\n", "# select distinct a.item_id, a.outlet as outlet_id, (case when a.avg_overall_qpc is not null then a.avg_overall_qpc else b.avg_overall_qpc end) as avg_overall_qpc\n", "# from last_3days_qpc a\n", "# left join last_7days_qpc b\n", "# on a.item_id = b.item_id and a.outlet = b.outlet\n", "\n", "\n", "# \"\"\".format(\n", "#     item_id=item_id_list\n", "# )\n", "\n", "# hist_cpd_new = pd.read_sql_query(sql=query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"WITH it_pd AS\n", "(SELECT DISTINCT item_id,\n", "               product_id,\n", "               multiplier,\n", "               updated_on,\n", "               max(updated_on) OVER (PARTITION BY product_id) AS last_updated\n", "FROM it_pd_log),\n", " item_level_info AS\n", "(SELECT item_id,\n", "      variant_mrp,\n", "      row_number() OVER (PARTITION BY item_id\n", "                         ORDER BY updated_at DESC) AS row_rank\n", "FROM lake_rpc.product_product),\n", "\n", " mapping AS\n", "(SELECT DISTINCT item_id,\n", "               product_id,\n", "               coalesce(multiplier,1) AS multiplier\n", "FROM it_pd\n", "WHERE last_updated = updated_on ),\n", "\n", " checkout_details AS\n", "(SELECT DISTINCT (convert_timezone('Asia/Kolkata',a.install_ts)) AS order_date,\n", "               o.outlet as outlet_id,\n", "               o.facility_id,\n", "               a.id AS ancestor,\n", "               a.current_status,\n", "               oi.product_id,\n", "               c.item_id,\n", "               oi.selling_price,\n", "               d.variant_mrp,\n", "               oi.quantity*multiplier AS quantity,\n", "               multiplier,\n", "               sum(d.variant_mrp) OVER (PARTITION BY a.id,\n", "                                                     oi.product_id) AS total_mrp\n", "FROM lake_oms_bifrost.oms_order a\n", "INNER JOIN lake_oms_bifrost.oms_merchant b ON a.merchant_id = b.id\n", "LEFT JOIN\n", " (SELECT *\n", "  FROM lake_oms_bifrost.oms_order_item\n", "  WHERE freebie_id IS NULL\n", "    AND date(install_ts + interval '5.5 Hours') >= dateadd(day, -16, current_date)) oi ON a.id = oi.order_id\n", "LEFT JOIN mapping c ON oi.product_id = c.product_id\n", "LEFT JOIN\n", " (SELECT *\n", "  FROM item_level_info\n", "  WHERE row_rank = 1) d ON c.item_id = d.item_id\n", "INNER JOIN\n", " (SELECT DISTINCT ancestor,\n", "                    outlet,\n", "                  facility_id,\n", "                  f.name AS facility_name\n", "  FROM lake_ims.ims_order_details o\n", "  INNER JOIN lake_retail.console_outlet ro ON ro.id=o.outlet\n", "  INNER JOIN lake_crates.facility f ON f.id=ro.facility_id\n", "  WHERE date(o.created_at) BETWEEN dateadd(day, -16, current_date) AND dateadd(day, -1, current_date)) o ON o.ancestor=a.id\n", "WHERE date(convert_timezone('Asia/Kolkata',a.install_ts)) >= dateadd(day, -16, current_date)\n", " AND a.direction ='FORWARD'\n", " AND b.city_name NOT IN ('Not in service area',\n", "                         'Hapur',\n", "                         'Test city')\n", " AND b.city_name NOT ILIKE '%%b2b%%'\n", " AND a.current_status <> 'CANCELLED'\n", " AND a.type IN ('RetailForwardOrder',\n", "                'InternalForwardOrder')\n", "GROUP BY 1,\n", "        2,\n", "        3,\n", "        4,\n", "        5,\n", "        6,\n", "        7,\n", "        8,\n", "        9,\n", "        10,\n", "        11),\n", "\n", "order_details AS\n", "(SELECT DISTINCT ancestor,\n", "  date(order_date) AS order_date,\n", "  outlet_id::int,\n", "  facility_id,\n", "  item_id ::int,\n", "  product_id ,\n", "  quantity,\n", "  total_mrp,\n", "  selling_price,\n", "  extract(hour from order_date) as bracket\n", "FROM checkout_details),\n", "\n", "\n", "app_live as (\n", "select * from(select item_id, o.id as outlet_id, date(order_date) as order_date,\n", "    extract(hour from order_date) as bracket,\n", "    max(app_live) as app_live\n", "from consumer.rpc_daily_availability a\n", "left join lake_retail.console_outlet o on a.facility_id = o.facility_id\n", "where date(order_date) between current_date-interval '14 days' and current_date-interval '1 days'\n", "group by 1,2,3,4)\n", "where app_live = 1),\n", "\n", "hourly_live_data as (select distinct a.item_id, a.outlet_id, a.order_date, a.bracket, ancestor, quantity as qty\n", "    -- ROUND(sum(quantity),4) as qty, \n", "    -- ROUND(count(distinct order_id),4) as carts\n", "from app_live a\n", "left join order_details b on a.item_id = b.item_id and a.outlet_id = b.outlet_id and a.order_date = b.order_date and a.bracket = b.bracket\n", "--group by 1,2,3,4\n", "),\n", "\n", "last_3_days as (\n", "\n", "    select b.item_id, a.outlet_id, a.order_date, a.bracket, qty, total_carts, (qty*1.0000/total_carts) as qpc from(\n", "        \n", "        (select outlet_id, order_date, bracket, ROUND(count(distinct ancestor),4) as total_carts\n", "        from order_details\n", "        where order_date between current_date-interval '3 days' and current_date-interval '1 days'\n", "        group by 1,2,3) a\n", "        \n", "        left join\n", "        \n", "        (select distinct item_id, outlet_id\n", "        from hourly_live_data) b\n", "        \n", "        on a.outlet_id = b.outlet_id\n", "        \n", "        left join \n", "            \n", "        (select item_id, order_date, outlet_id, bracket, ROUND(sum(case when qty is null then 0 else qty end),4) as qty -- count(distinct date_) as days_live_in_7, sum(qty) as qty_in_7, sum(carts) as carts_in_7\n", "        from hourly_live_data\n", "        where order_date between current_date-interval '3 days' and current_date-interval '1 days'\n", "        group by 1,2,3,4) c\n", "        \n", "        on b.item_id = c.item_id and a.outlet_id = c.outlet_id and a.order_date = c.order_date and a.bracket = c.bracket)\n", "    \n", "    where b.item_id in ({item_id})\n", "    and a.outlet_id in ({outlet_id})\n", "),\n", "\n", "last_7_days as (\n", "\n", "    select a.item_id, a.outlet_id, a.bracket, (case when sale_days_in_7>=3 then qty_in_7 else null end) as qty_in_7, total_carts_in_7 from(\n", "    \n", "        (select item_id, outlet_id, bracket, ROUND(sum(case when qty is null then 0 else qty end),4) as qty_in_7, count(distinct order_date) as sale_days_in_7 -- count(distinct date_) as days_live_in_7, sum(qty) as qty_in_7, sum(carts) as carts_in_7\n", "        from hourly_live_data\n", "        where order_date between current_date-interval '7 days' and current_date-interval '1 days'\n", "        group by 1,2,3) a\n", "    \n", "        left join \n", "    \n", "        (select outlet_id, bracket, ROUND(count(distinct ancestor),4) as total_carts_in_7\n", "        from hourly_live_data\n", "        where order_date between current_date-interval '7 days' and current_date-interval '1 days'\n", "        group by 1,2) b\n", "    \n", "    on a.outlet_id = b.outlet_id and a.bracket = b.bracket)\n", "    where \n", "    a.item_id in ({item_id})\n", "    and\n", "    a.outlet_id in ({outlet_id})\n", "),\n", "\n", "last_14_days as (\n", "\n", "    select a.item_id, a.outlet_id, a.bracket, (case when sale_days_in_14>=3 then qty_in_14 else null end) as qty_in_14, total_carts_in_14 from(\n", "    \n", "        (select item_id, outlet_id, bracket, ROUND(sum(case when qty is null then 0 else qty end),4) as qty_in_14, count(distinct order_date) as sale_days_in_14 -- count(distinct date_) as days_live_in_7, sum(qty) as qty_in_7, sum(carts) as carts_in_7\n", "        from hourly_live_data\n", "        where order_date between current_date-interval '14 days' and current_date-interval '1 days'\n", "        group by 1,2,3) a\n", "    \n", "        left join \n", "    \n", "        (select outlet_id, bracket, ROUND(count(distinct ancestor),4) as total_carts_in_14\n", "        from hourly_live_data\n", "        where order_date between current_date-interval '14 days' and current_date-interval '1 days'\n", "        group by 1,2) b\n", "    \n", "    on a.outlet_id = b.outlet_id and a.bracket = b.bracket)\n", "\n", "    where \n", "    a.item_id in ({item_id})\n", "    and\n", "    a.outlet_id in ({outlet_id})\n", ")\n", "\n", "select item_id, outlet_id, avg(qpc) as avg_overall_qpc from(\n", "    select item_id, outlet_id, order_date, sum(qpc*total_carts) as qty, sum(total_carts) as total_carts, sum(qpc*total_carts)/sum(total_carts) as qpc from(\n", "        select item_id, outlet_id, order_date, bracket, qty, total_carts, (case when qpc is null then avg(qpc) over (partition by item_id, outlet_id, order_date) else qpc end) as qpc from(\n", "\n", "                select a.item_id, a.outlet_id, a.order_date, a.bracket, qty, total_carts,\n", "                (case when (qpc is null and qty_in_7 is not null) then (qty_in_7*1.0000/total_carts_in_7)\n", "                    when (qpc is null and qty_in_7 is null) then (qty_in_14*1.0000/total_carts_in_14)\n", "                    else qpc end) as qpc\n", "                from last_3_days a\n", "                left join last_7_days b on a.item_id = b.item_id and a.outlet_id = b.outlet_id and a.bracket = b.bracket\n", "                left join last_14_days c on a.item_id = c.item_id and a.outlet_id = c.outlet_id and a.bracket = c.bracket   \n", "            )\n", "        )\n", "    group by 1,2,3\n", "    )\n", "group by 1,2\n", "\"\"\".format(\n", "    item_id=item_id_list, outlet_id=outlet_id_list\n", ")\n", "\n", "\n", "hist_cpd = pd.read_sql(query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hist_cpd.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Outlet facility mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# query = \"\"\"select id as outlet_id,facility_id from retail.console_outlet\"\"\"\n", "# retail = pb.get_connection(\"retail\")\n", "# facility = pd.read_sql_query(query, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# hist_cpd_facility=hist_cpd.merge(facility,on=[\"outlet_id\"],how=\"left\")\n", "# hist_cpd_facility.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_orders = inv_orders.merge(hist_cpd, on=[\"item_id\", \"outlet_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_orders.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Reading slot wise capacity details from capacity system and manipulation\n", "query = \"\"\"select distinct backend_merchant_id,\n", "backend_merchant_name,\n", "date(delivery_date) as delivery_date,\n", "delivery_slot_type,\n", "sum(given_capacity) as planned_capacity,\n", "sum(capacity_utilised) as actual_capacity\n", "from\n", "(SELECT \n", "    warehouse_external_id AS backend_merchant_id,\n", "    warehouse_name AS backend_merchant_name,\n", "    DATE(slot_start AT TIME ZONE 'ASIA/KOLKATA') AS delivery_date,\n", "    TO_CHAR(slot_start AT TIME ZONE 'ASIA/KOLKATA','HH24:MI:SS') || ' - ' || \n", "    TO_CHAR(slot_end AT TIME ZONE 'ASIA/KOLKATA','HH24:MI:SS') AS delivery_slot,\n", "    slot_type,\n", "    CASE WHEN EXTRACT(HOUR FROM (slot_start AT TIME ZONE 'ASIA/KOLKATA'))< 16 THEN 'SLOT A' ELSE  'SLOT B' END AS delivery_slot_type,\n", "    warehouse_asked AS asked_capcity,\n", "    warehouse_planned AS given_capacity,\n", "    warehouse_actual AS capacity_utilised,\n", "    warehouse_available AS capacity_under_utilised,\n", "    min(update_ts) AS update_ts\n", "FROM sco_path_capacity\n", "WHERE date(slot_start) >= date(now() - interval '3 days')\n", "and warehouse_planned < 9999\n", "GROUP BY 1,2,3,4,5,6,7,8,9,10\n", "ORDER BY 2,3,4,5) a\n", "where\n", "delivery_date between date(current_date - interval '3 days') and date(current_date + interval '8 days')\n", "and given_capacity > 0\n", "group by 1,2,3,4\n", "order by 2,3;\n", "\"\"\"\n", "\n", "capacity_details = pd.read_sql_query(query, capacity_system)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select distinct cms.outlet_id,o.name as outlet,cms.cms_store, o.facility_id\n", "from\n", "lake_retail.console_outlet_cms_store cms\n", "join lake_retail.console_outlet o on cms.outlet_id= o.id\n", "where\n", "cms.active = 1 and cms.cms_update_active = 1;\n", "\"\"\"\n", "\n", "cms_outlet_details = pd.read_sql(query, redshift)\n", "cms_outlet_details.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["warehouse_capacities = (\n", "    capacity_details.merge(\n", "        cms_outlet_details,\n", "        how=\"inner\",\n", "        left_on=[\"backend_merchant_id\"],\n", "        right_on=[\"cms_store\"],\n", "    )[\n", "        [\n", "            \"outlet_id\",\n", "            \"outlet\",\n", "            \"delivery_date\",\n", "            \"delivery_slot_type\",\n", "            \"planned_capacity\",\n", "            \"backend_merchant_name\",\n", "        ]\n", "    ]\n", "    .rename(columns={\"planned_capacity\": \"Capacity\"})\n", "    .drop_duplicates()\n", ")\n", "\n", "warehouse_capacities = (\n", "    warehouse_capacities.groupby([\"delivery_date\", \"outlet_id\", \"outlet\"])\n", "    .agg({\"Capacity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "warehouse_capacities[\"delivery_date\"] = pd.to_datetime(\n", "    warehouse_capacities[\"delivery_date\"]\n", ")\n", "warehouse_capacities = warehouse_capacities[\n", "    warehouse_capacities[\"outlet_id\"].isin(list(inv_orders[\"outlet_id\"].unique()))\n", "]\n", "\n", "# warehouse_capacities = warehouse_capacities.merge(\n", "#     facility, on=[\"outlet_id\"], how=\"left\"\n", "# )\n", "# warehouse_capacities = warehouse_capacities.drop([\"outlet_id\", \"outlet\"], axis=1)\n", "# warehouse_capacities = (\n", "#     warehouse_capacities.groupby([\"delivery_date\", \"facility_id\"])[[\"Capacity\"]]\n", "#     .max()\n", "#     .reset_index()\n", "# )\n", "warehouse_capacities.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["warehouse_capacities.delivery_date = pd.to_datetime(warehouse_capacities.delivery_date)\n", "inv_orders.delivery_date = pd.to_datetime(inv_orders.delivery_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["past_capacities = warehouse_capacities[warehouse_capacities.delivery_date < today]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_capacity = inv_orders.merge(\n", "    warehouse_capacities, how=\"left\", on=[\"outlet_id\", \"delivery_date\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_capacity.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["avg_orders_query = \"\"\"\n", "\n", "with past_week_orders as (\n", "select o.outlet as outlet_id, item_id, date(o.created_at) as scheduled_at, sum(quantity) as actual_orders\n", "\n", "    FROM lake_ims.ims_order_details o\n", "    INNER JOIN lake_ims.ims_order_items i ON o.id = i.order_details_id \n", "    INNER JOIN lake_retail.console_outlet rco ON o.outlet = rco.id\n", "    left join lake_retail.console_location  rcl ON rco.tax_location_id=rcl.id\n", "    left join lake_crates.facility fac on fac.id=rco.facility_id\n", "    where o.id>'53716760'  and o.status_id not in (5) and   i.id>'*********' \n", "    and i.item_id in ({item_id})\n", "    and date(o.created_at + interval '5.5 hours') between current_date- interval '7 days' and current_date-interval '1 days'\n", "    and\n", "     o.ancestor in\n", "    (select ancestor from\n", "    (\n", "    select  outlet,ancestor,count(id) cnt,min(date(created_at + interval '5.5 hours')) frst_created\n", "    from lake_ims.ims_order_details where  ancestor in\n", "    (\n", "    select distinct ancestor\n", "    from lake_ims.ims_order_details o\n", "    where date(o.created_at + interval '5.5 hours') between current_date-7 and current_date-1\n", "    and o.id>53716760  \n", "    and outlet in ({outlet_id})\n", "    ) group by 1,2 \n", "    )\n", "    temp1)\n", "    \n", "group by 1,2,3\n", "\n", "),\n", "\n", "last_3days_avg as (select item_id, outlet_id, avg(actual_orders) as avg_qty_picked \n", "from past_week_orders\n", "where scheduled_at between current_date-interval '3 days' and current_date - interval '1 days'\n", "group by 1,2),\n", "\n", "last_7days_avg as (select item_id, outlet_id, avg(actual_orders) as avg_qty_picked \n", "from past_week_orders\n", "group by 1,2)\n", "\n", "select distinct a.item_id, a.outlet_id, (case when a.avg_qty_picked is null then b.avg_qty_picked else a.avg_qty_picked end) as avg_qty_picked\n", "from last_3days_avg a\n", "left join last_7days_avg b\n", "on a.item_id = b.item_id and a.outlet_id and b.outlet_id\n", "\n", "\"\"\".format(\n", "    item_id=item_id_list, outlet_id=outlet_id_list\n", ")\n", "\n", "avg_qty_picked = pd.read_sql(avg_orders_query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_capacity = order_capacity.merge(\n", "    avg_qty_picked, how=\"left\", on=[\"item_id\", \"outlet_id\"]\n", ")\n", "order_capacity.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "select item_id, outlet_id, delivery_date, sum(qty_reqd) as qty_picked from(\n", "    SELECT p.outlet_id,\n", "           date(convert_tz(p.max_scheduled_time,\"+00:00\",\"+05:30\")) AS delivery_date,\n", "           p.pick_list_id,\n", "           PLI.item_id,\n", "           sum(PLI.required_quantity) AS qty_reqd\n", "    FROM warehouse_location.warehouse_wave w\n", "    INNER JOIN warehouse_location.warehouse_wave_pick_list_mapping m ON w.id = m.wave_id\n", "    INNER JOIN\n", "      (SELECT id,\n", "              pick_list_id,\n", "              max_scheduled_time,\n", "              outlet_id,\n", "              state\n", "       FROM warehouse_location.warehouse_pick_list\n", "       WHERE id>44404456 )p ON p.id = m.pick_list_id\n", "    INNER JOIN warehouse_location.warehouse_pick_list_item PLI ON PLI.pick_list_id=p.id AND PLI.state IN (2,4)\n", "    WHERE date(convert_tz(p.max_scheduled_time,\"+00:00\",\"+05:30\")) = date_add(date(convert_tz(current_date(),\"+00:00\",\"+05:30\")), interval 1 DAY)\n", "      AND p.state IN(9,\n", "                     10,\n", "                     11,\n", "                     12,\n", "                     13,\n", "                     14,\n", "                     8,\n", "                     3,\n", "                     4)\n", "      AND p.outlet_id IN ({outlet_id})\n", "      AND PLI.item_id IN ({item_id})\n", "    GROUP BY 1,\n", "             2,\n", "             3,\n", "             4 ) a\n", "group by 1,2,3\n", "\"\"\".format(\n", "    item_id=item_id_list, outlet_id=outlet_id_list\n", ")\n", "\n", "pick_list = pd.read_sql_query(sql, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pick_list.delivery_date = pd.to_datetime(pick_list.delivery_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_cap_picked = order_capacity.merge(\n", "    pick_list, how=\"left\", on=[\"item_id\", \"outlet_id\", \"delivery_date\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_cap_picked.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_po_query = \"\"\"\n", "select distinct\n", "o.facility_id,\n", "date(issue_date) as issue_date,\n", "date(expiry_date) as expiry_date,\n", "(case when date_add(date(ps.schedule_date_time), interval 1 day) is null then date_add(date(expiry_date), interval 1 day) else\n", "date_add(date(ps.schedule_date_time), interval 1 day) end) AS delivery_date,\n", "poi.item_id,\n", "poi.units_ordered as expected_po_qty\n", "from po.purchase_order p\n", "left join\n", "po.po_schedule ps on p.id=ps.po_id_id\n", "inner join\n", "po.purchase_order_items poi on p.id=poi.po_id\n", "inner join\n", "retail.console_outlet o on o.id=p.outlet_id\n", "inner join\n", "po.purchase_order_status posa on posa.po_id = p.id\n", "inner join\n", "po.purchase_order_state posta on posta.id = posa.po_state_id\n", "where \n", "posta.name in ('Created','Scheduled','Rescheduled')\n", "and date(convert_tz(p.issue_date,'+00:00','+05:30'))\n", "and poi.item_id in ({item_id})\n", "\"\"\".format(\n", "    item_id=item_id_list\n", ")\n", "\n", "open_po = pd.read_sql_query(sql=open_po_query, con=retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_cap_picked.delivery_date = pd.to_datetime(order_cap_picked.delivery_date)\n", "open_po.delivery_date = pd.to_datetime(open_po.delivery_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_cap_picked_po = order_cap_picked.merge(\n", "    open_po, on=[\"item_id\", \"facility_id\", \"delivery_date\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_cap_picked_po.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_recvd_query = \"\"\"select o.outlet as outlet_id, date(o.scheduled_at) as delivery_date, count(distinct o.order_id) as orders_recvd\n", "from ims.ims_order_details o\n", "where date(scheduled_at) between current_date - interval 3 day and current_date + interval 4 day\n", "and outlet in ({outlet_id})\n", "and o.status_id <> 5\n", "group by 1,2\n", "\"\"\".format(\n", "    outlet_id=outlet_id_list\n", ")\n", "\n", "total_orders = pd.read_sql_query(sql=orders_recvd_query, con=retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_cap_picked_po.delivery_date = pd.to_datetime(order_cap_picked_po.delivery_date)\n", "total_orders.delivery_date = pd.to_datetime(total_orders.delivery_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["past_orders = total_orders[total_orders.delivery_date < today]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin = order_cap_picked_po.merge(\n", "    total_orders, on=[\"outlet_id\", \"delivery_date\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Calculate fulfillment percentage"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fulfillment_perc = past_orders.merge(\n", "    past_capacities, on=[\"outlet_id\", \"delivery_date\"], how=\"left\"\n", ")\n", "fulfillment_perc.rename(columns={\"Capacity\": \"Total_Capacity\"}, inplace=True)\n", "fulfillment_perc.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# fulfillment_perc[fulfillment_perc[\"outlet_id\"]==316]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# fulfillment_perc = fulfillment_perc[fulfillment_perc.Total_Capacity > 0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fulfillment_perc[\"fulfillment_percentage\"] = round(\n", "    fulfillment_perc.orders_recvd * 100 / fulfillment_perc.Total_Capacity, 2\n", ")\n", "fulfillment_perc.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fulfillment_perc.fulfillment_percentage = np.where(\n", "    fulfillment_perc.fulfillment_percentage > 100,\n", "    100,\n", "    fulfillment_perc.fulfillment_percentage,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["avg_fulfillment_perc = (\n", "    fulfillment_perc.groupby([\"outlet_id\"])[\"fulfillment_percentage\", \"orders_recvd\"]\n", "    .mean()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["avg_fulfillment_perc.rename({\"orders_recvd\": \"avg_orders_recvd\"}, inplace=True, axis=1)\n", "avg_fulfillment_perc.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin = fin.merge(avg_fulfillment_perc, on=[\"outlet_id\"], how=\"left\")\n", "fin.rename(columns={\"Capacity\": \"Total_Capacity\"}, inplace=True)\n", "fin.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Using method 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cols = [\n", "#     \"item_id\",\n", "#     \"item_name\",\n", "#     \"outlet_id\",\n", "#     \"outlet_name\",\n", "#     \"current_inventory\",\n", "#     \"delivery_date\",\n", "#     \"Total_Capacity\",\n", "#     \"orders_recvd\",\n", "#     \"carts\",\n", "#     \"avg_carts\",\n", "#     \"qty_ordered\",\n", "#     \"avg_qpc\",\n", "#     \"qty_picked\",\n", "#     \"expected_po_qty\",\n", "# ]\n", "# fin_m_1 = fin[cols].copy()\n", "\n", "# fin_m_1[\"calculated_carts\"] = (\n", "#     fin_m_1[\"carts\"] * fin[\"Total_Capacity\"] / fin_m_1[\"orders_recvd\"]\n", "# )\n", "# fin_m_1[\"calculated_carts\"] = np.where(\n", "#     fin_m_1[\"calculated_carts\"].isna(),\n", "#     fin_m_1[\"avg_carts\"],\n", "#     fin_m_1[\"calculated_carts\"],\n", "# )\n", "\n", "# fin_m_1[\"calculated_qty\"] = fin_m_1[\"calculated_carts\"] * fin_m_1[\"avg_qpc\"]\n", "# fin_m_1[\"qty_picked\"] = np.where(fin_m_1[\"qty_picked\"].isna(), 0, fin_m_1[\"qty_picked\"])\n", "# fin_m_1[\"expected_po_qty\"] = np.where(\n", "#     fin_m_1[\"expected_po_qty\"].isna(), 0, fin_m_1[\"expected_po_qty\"]\n", "# )\n", "# fin_m_1[\"calculated_qty\"] = np.ceil(fin_m_1[\"calculated_qty\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Using method 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cols_2 = [\n", "    \"item_id\",\n", "    \"item_name\",\n", "    \"l0\",\n", "    \"l1\",\n", "    \"l2\",\n", "    \"item_uom\",\n", "    \"outlet_id\",\n", "    \"outlet_name\",\n", "    \"current_inventory\",\n", "    \"onshelf_inventory\",\n", "    \"delivery_date\",\n", "    \"Total_Capacity\",\n", "    \"avg_orders_recvd\",\n", "    \"fulfillment_percentage\",\n", "    \"orders_recvd\",\n", "    \"carts\",\n", "    \"qty_ordered\",\n", "    \"slotB_qty_blocked\",\n", "    \"avg_overall_qpc\",\n", "    \"qpc_multiplier\",\n", "    \"qty_picked\",\n", "    \"billed_qty\",\n", "    \"expected_po_qty\",\n", "    \"inner_case_size\",\n", "    \"avg_qty_picked\",\n", "    \"app_live\",\n", "    \"is_on_metering\",\n", "    \"net_avail_qty\",\n", "    \"landing_price\",\n", "    \"outward_guidelines\",\n", "    \"capping\",\n", "    \"saleable_guidelines\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_m_2 = fin[cols_2].copy()\n", "\n", "j = 0\n", "while j < len(fin_m_2) - 1:\n", "    outlet = fin_m_2.loc[j, \"outlet_id\"]\n", "    i = j + 1\n", "\n", "    if i == len(fin_m_2) - 1:\n", "        break\n", "\n", "    while fin_m_2.loc[i, \"outlet_id\"] == outlet:\n", "        if i == len(fin_m_2) - 1:\n", "            break\n", "        if pd.isnull(fin_m_2.loc[i, \"Total_Capacity\"]):\n", "            fin_m_2.loc[i, \"Total_Capacity\"] = fin_m_2.loc[i - 1, \"Total_Capacity\"]\n", "        i = i + 1\n", "\n", "    j = i\n", "\n", "fin_m_2.orders_recvd.fillna(0, inplace=True)\n", "fin_m_2[\"fulfillment_percentage\"] = np.where(\n", "    fin_m_2[\"fulfillment_percentage\"].isna(), 100, fin_m_2[\"fulfillment_percentage\"]\n", ")\n", "fin_m_2[\"exp_capacity_fulfillment\"] = np.where(\n", "    fin_m_2[\"Total_Capacity\"].isna(),\n", "    round(fin_m_2[\"avg_orders_recvd\"], 0),\n", "    round(fin_m_2[\"Total_Capacity\"] * fin_m_2[\"fulfillment_percentage\"] / 100, 0),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_m_2[\"exp_capacity_fulfillment\"] = fin_m_2[\"exp_capacity_fulfillment\"].fillna(0)\n", "fin_m_2[\"slotB_qty_blocked\"] = fin_m_2[\"slotB_qty_blocked\"].fillna(0)\n", "\n", "fin_m_2[\"carts\"] = np.where(fin_m_2[\"carts\"].isna(), 0, fin_m_2[\"carts\"])\n", "fin_m_2[\"qty_ordered\"] = np.where(\n", "    fin_m_2[\"qty_ordered\"].isna(), 0, fin_m_2[\"qty_ordered\"]\n", ")\n", "fin_m_2[\"billed_qty\"] = np.where(fin_m_2[\"billed_qty\"].isna(), 0, fin_m_2[\"billed_qty\"])\n", "fin_m_2[\"avg_overall_qpc\"] = np.where(\n", "    fin_m_2[\"avg_overall_qpc\"].isna(), 0, fin_m_2[\"avg_overall_qpc\"]\n", ")\n", "fin_m_2[\"bumped_up_qpc\"] = fin_m_2[\"avg_overall_qpc\"] * fin_m_2[\"qpc_multiplier\"]\n", "\n", "fin_m_2[\"avg_qty_picked\"] = np.where(\n", "    fin_m_2[\"avg_qty_picked\"].isna(), 0, fin_m_2[\"avg_qty_picked\"]\n", ")\n", "\n", "fin_m_2[\"remaining_carts\"] = (\n", "    fin_m_2[\"exp_capacity_fulfillment\"] - fin_m_2[\"orders_recvd\"]\n", ")\n", "fin_m_2[\"remaining_carts\"] = np.where(\n", "    fin_m_2[\"remaining_carts\"] < 0, 0, fin_m_2[\"remaining_carts\"]\n", ")\n", "\n", "fin_m_2[\"extrapolated_qty\"] = np.ceil(\n", "    fin_m_2[\"remaining_carts\"] * fin_m_2[\"bumped_up_qpc\"]\n", ")\n", "fin_m_2[\"calculated_qty\"] = fin_m_2[\"qty_ordered\"] + np.ceil(\n", "    fin_m_2[\"remaining_carts\"] * fin_m_2[\"bumped_up_qpc\"]\n", ")\n", "fin_m_2[\"forecasted_qty\"] = np.ceil(\n", "    fin_m_2[\"exp_capacity_fulfillment\"] * fin_m_2[\"bumped_up_qpc\"]\n", ")\n", "\n", "\n", "fin_m_2[\"qty_picked\"] = np.where(fin_m_2[\"qty_picked\"].isna(), 0, fin_m_2[\"qty_picked\"])\n", "fin_m_2[\"billed_qty\"] = np.where(fin_m_2[\"billed_qty\"].isna(), 0, fin_m_2[\"billed_qty\"])\n", "fin_m_2[\"expected_po_qty\"] = np.where(\n", "    fin_m_2[\"expected_po_qty\"].isna(), 0, fin_m_2[\"expected_po_qty\"]\n", ")\n", "fin_m_2[\"calculated_qty\"] = np.ceil(fin_m_2[\"calculated_qty\"])\n", "\n", "fin_m_2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# fin1.drop(['calculated_case'], inplace = True, axis = 1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin1 = fin_m_2.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin1 = fin1.sort_values(by=[\"item_id\", \"outlet_id\", \"delivery_date\"]).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin1.drop([\"index\"], inplace=True, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin1.onshelf_inventory = fin1.onshelf_inventory.fillna(0)\n", "fin1[\"next_day_inv\"] = 0\n", "\n", "fin1.loc[0, \"actual_inv\"] = (\n", "    fin1.loc[0, \"onshelf_inventory\"]\n", "    + fin1.loc[0, \"expected_po_qty\"]\n", "    - fin1.loc[0, \"qty_picked\"]\n", "    + fin1.loc[0, \"billed_qty\"]\n", ")\n", "\n", "if fin1.loc[0, \"actual_inv\"] < 0:\n", "    fin1.loc[0, \"actual_inv\"] = 0\n", "\n", "fin1.loc[0, \"qty_required\"] = (\n", "    fin1.loc[0, \"calculated_qty\"]\n", "    - fin1.loc[0, \"qty_picked\"]\n", "    - fin1.loc[0, \"actual_inv\"]\n", ")\n", "if fin1.loc[0, \"qty_required\"] < 0:\n", "    fin1.loc[0, \"qty_required\"] = 0\n", "\n", "fin1.loc[0, \"next_day_inv\"] = (\n", "    fin1.loc[0, \"onshelf_inventory\"]\n", "    + fin1.loc[0, \"expected_po_qty\"]\n", "    - fin1.loc[0, \"qty_picked\"]\n", "    + fin1.loc[0, \"billed_qty\"]\n", ") - (fin1.loc[0, \"calculated_qty\"] - fin1.loc[0, \"qty_picked\"])\n", "if fin1.loc[0, \"next_day_inv\"] < 0:\n", "    fin1.loc[0, \"next_day_inv\"] = 0\n", "\n", "if (\n", "    fin1.loc[0, \"qty_required\"] > fin1.loc[0, \"capping\"] * fin1.loc[0, \"avg_qty_picked\"]\n", "    and fin1.loc[0, \"avg_qty_picked\"] != 0\n", "):\n", "    fin1.loc[0, \"qty_capped\"] = fin1.loc[0, \"capping\"] * fin1.loc[0, \"avg_qty_picked\"]\n", "else:\n", "    fin1.loc[0, \"qty_capped\"] = fin1.loc[0, \"qty_required\"]\n", "\n", "for i in range(1, len(fin1)):\n", "\n", "    if fin1.loc[i, \"delivery_date\"] == date.iloc[0][\"delivery_date\"]:\n", "\n", "        fin1.loc[i, \"actual_inv\"] = (\n", "            fin1.loc[i, \"onshelf_inventory\"]\n", "            + fin1.loc[i, \"expected_po_qty\"]\n", "            - fin1.loc[i, \"qty_picked\"]\n", "            + fin1.loc[i, \"billed_qty\"]\n", "        )\n", "        if fin1.loc[i, \"actual_inv\"] < 0:\n", "            fin1.loc[i, \"actual_inv\"] = 0\n", "\n", "        fin1.loc[i, \"qty_required\"] = (\n", "            fin1.loc[i, \"calculated_qty\"]\n", "            - fin1.loc[i, \"qty_picked\"]\n", "            - fin1.loc[i, \"actual_inv\"]\n", "        )\n", "        if fin1.loc[i, \"qty_required\"] < 0:\n", "            fin1.loc[i, \"qty_required\"] = 0\n", "\n", "        fin1.loc[i, \"next_day_inv\"] = (\n", "            fin1.loc[i, \"onshelf_inventory\"]\n", "            + fin1.loc[i, \"expected_po_qty\"]\n", "            - fin1.loc[i, \"qty_picked\"]\n", "            + fin1.loc[i, \"billed_qty\"]\n", "        ) - (fin1.loc[i, \"calculated_qty\"] - fin1.loc[i, \"qty_picked\"])\n", "\n", "        if fin1.loc[i, \"next_day_inv\"] < 0:\n", "            fin1.loc[i, \"next_day_inv\"] = 0\n", "\n", "        if (\n", "            fin1.loc[i, \"qty_required\"]\n", "            > fin1.loc[i, \"capping\"] * fin1.loc[i, \"avg_qty_picked\"]\n", "            and fin1.loc[i, \"avg_qty_picked\"] != 0\n", "        ):\n", "            fin1.loc[i, \"qty_capped\"] = (\n", "                fin1.loc[i, \"capping\"] * fin1.loc[i, \"avg_qty_picked\"]\n", "            )\n", "        else:\n", "            fin1.loc[i, \"qty_capped\"] = fin1.loc[i, \"qty_required\"]\n", "\n", "    else:\n", "        if fin1.loc[i - 1, \"next_day_inv\"] > fin1.loc[i, \"onshelf_inventory\"]:\n", "            if (\n", "                fin1.loc[i, \"delivery_date\"] == date.iloc[1][\"delivery_date\"]\n", "                and fin1.loc[i - 1, \"expected_po_qty\"] == 0\n", "            ):\n", "                fin1.loc[i - 1, \"next_day_inv\"] = fin1.loc[i, \"onshelf_inventory\"]\n", "\n", "            elif fin1.loc[i, \"delivery_date\"] == date.iloc[2][\"delivery_date\"] and (\n", "                fin1.loc[i - 1, \"expected_po_qty\"] == 0\n", "                and fin1.loc[i - 2, \"expected_po_qty\"] == 0\n", "            ):\n", "                fin1.loc[i - 1, \"next_day_inv\"] = fin1.loc[i, \"onshelf_inventory\"]\n", "\n", "        fin1.loc[i, \"actual_inv\"] = (\n", "            fin1.loc[i - 1, \"next_day_inv\"]\n", "            + fin1.loc[i, \"expected_po_qty\"]\n", "            - fin1.loc[i, \"qty_picked\"]\n", "            + fin1.loc[i, \"billed_qty\"]\n", "        )\n", "\n", "        if fin1.loc[i, \"actual_inv\"] < 0:\n", "            fin1.loc[i, \"actual_inv\"] = 0\n", "\n", "        fin1.loc[i, \"qty_required\"] = (\n", "            fin1.loc[i, \"calculated_qty\"]\n", "            - fin1.loc[i, \"qty_picked\"]\n", "            - fin1.loc[i, \"actual_inv\"]\n", "        )\n", "        if fin1.loc[i, \"qty_required\"] < 0:\n", "            fin1.loc[i, \"qty_required\"] = 0\n", "\n", "        fin1.loc[i, \"next_day_inv\"] = (\n", "            fin1.loc[i - 1, \"next_day_inv\"]\n", "            + fin1.loc[i, \"expected_po_qty\"]\n", "            - fin1.loc[i, \"qty_picked\"]\n", "            + fin1.loc[i, \"billed_qty\"]\n", "        ) - (fin1.loc[i, \"calculated_qty\"] - fin1.loc[i, \"qty_picked\"])\n", "        if fin1.loc[i, \"next_day_inv\"] < 0:\n", "            fin1.loc[i, \"next_day_inv\"] = 0\n", "\n", "        if (\n", "            fin1.loc[i, \"delivery_date\"]\n", "            == date.iloc[0][\"delivery_date\"]\n", "            + timedelta(days=int(fin1.loc[i, \"saleable_guidelines\"]) - 1)\n", "        ) and (fin1.loc[i, \"next_day_inv\"] > fin1.loc[i, \"expected_po_qty\"]):\n", "            fin1.loc[i, \"next_day_inv\"] = fin1.loc[i, \"expected_po_qty\"]\n", "\n", "        if (\n", "            fin1.loc[i, \"qty_required\"]\n", "            > fin1.loc[i, \"capping\"] * fin1.loc[i, \"avg_qty_picked\"]\n", "            and fin1.loc[i, \"avg_qty_picked\"] != 0\n", "        ):\n", "            fin1.loc[i, \"qty_capped\"] = (\n", "                fin1.loc[i, \"capping\"] * fin1.loc[i, \"avg_qty_picked\"]\n", "            )\n", "        else:\n", "            fin1.loc[i, \"qty_capped\"] = fin1.loc[i, \"qty_required\"]\n", "\n", "fin1[\"qty_capped\"] = np.ceil(fin1[\"qty_capped\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin1[\"updated_at\"] = datetime.strftime(\n", "    datetime.today() + timed<PERSON>ta(hours=5, minutes=30), \"%Y-%m-%d %H:%M:%S\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_cols = [\n", "    \"updated_at\",\n", "    \"item_id\",\n", "    \"item_name\",\n", "    \"l0\",\n", "    \"l1\",\n", "    \"l2\",\n", "    \"item_uom\",\n", "    \"outlet_id\",\n", "    \"outlet_name\",\n", "    \"net_avail_qty\",\n", "    \"current_inventory\",\n", "    \"onshelf_inventory\",\n", "    \"delivery_date\",\n", "    \"Total_Capacity\",\n", "    \"fulfillment_percentage\",\n", "    \"exp_capacity_fulfillment\",\n", "    \"orders_recvd\",\n", "    \"remaining_carts\",\n", "    \"avg_overall_qpc\",\n", "    \"qpc_multiplier\",\n", "    \"bumped_up_qpc\",\n", "    \"forecasted_qty\",\n", "    \"extrapolated_qty\",\n", "    \"carts\",\n", "    \"qty_ordered\",\n", "    \"slotB_qty_blocked\",\n", "    \"calculated_qty\",\n", "    \"qty_picked\",\n", "    \"billed_qty\",\n", "    \"expected_po_qty\",\n", "    \"actual_inv\",\n", "    \"next_day_inv\",\n", "    \"inner_case_size\",\n", "    \"avg_qty_picked\",\n", "    \"qty_required\",\n", "    \"qty_capped\",\n", "    \"app_live\",\n", "    \"is_on_metering\",\n", "    \"landing_price\",\n", "    \"outward_guidelines\",\n", "    \"capping\",\n", "    \"saleable_guidelines\",\n", "]\n", "fin1 = fin1[fin_cols]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inner_case = input[[\"item_id\", \"outlet_id\", \"inner_case_size\"]]\n", "inner_case.columns = [\"item_id\", \"outlet_id\", \"inner_case_size_from_sheet\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inner_case.outlet_id = inner_case.outlet_id.astype(int)\n", "inner_case.item_id = inner_case.item_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin1 = fin1.merge(inner_case, on=[\"item_id\", \"outlet_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# fin1[\"cap\"] = capping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin1 = fin1.rename(columns={\"Total_Capacity\": \"total_capacity\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin1.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table_fin = fin1[\n", "    [\n", "        \"updated_at\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"current_inventory\",\n", "        \"delivery_date\",\n", "        \"total_capacity\",\n", "        \"orders_recvd\",\n", "        \"remaining_carts\",\n", "        \"avg_overall_qpc\",\n", "        \"extrapolated_qty\",\n", "        \"carts\",\n", "        \"qty_ordered\",\n", "        \"calculated_qty\",\n", "        \"qty_picked\",\n", "        \"billed_qty\",\n", "        \"expected_po_qty\",\n", "        \"actual_inv\",\n", "        \"next_day_inv\",\n", "        \"inner_case_size\",\n", "        \"avg_qty_picked\",\n", "        \"qty_required\",\n", "        \"qty_capped\",\n", "        \"app_live\",\n", "        \"is_on_metering\",\n", "        \"inner_case_size_from_sheet\",\n", "        \"capping\",\n", "        \"landing_price\",\n", "        \"outward_guidelines\",\n", "        \"net_avail_qty\",\n", "        \"item_uom\",\n", "        \"qpc_multiplier\",\n", "        \"bumped_up_qpc\",\n", "        \"forecasted_qty\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"fulfillment_percentage\",\n", "        \"exp_capacity_fulfillment\",\n", "        \"saleable_guidelines\",\n", "        \"slotB_qty_blocked\",\n", "        \"onshelf_inventory\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table_fin = table_fin.rename(columns={\"capping\": \"cap\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table_fin.current_inventory = table_fin.current_inventory.astype(int)\n", "table_fin.onshelf_inventory = table_fin.onshelf_inventory.astype(int)\n", "\n", "table_fin.extrapolated_qty.fillna(0, inplace=True)\n", "table_fin.extrapolated_qty = table_fin.extrapolated_qty.astype(int)\n", "table_fin.carts = table_fin.carts.astype(int)\n", "table_fin.qty_ordered = table_fin.qty_ordered.astype(int)\n", "table_fin.slotB_qty_blocked = table_fin.slotB_qty_blocked.astype(int)\n", "\n", "table_fin.forecasted_qty.fillna(0, inplace=True)\n", "table_fin.forecasted_qty = table_fin.forecasted_qty.astype(int)\n", "\n", "table_fin.calculated_qty.fillna(0, inplace=True)\n", "table_fin.calculated_qty = table_fin.calculated_qty.astype(int)\n", "table_fin.qty_picked = table_fin.qty_picked.astype(int)\n", "table_fin.billed_qty = table_fin.billed_qty.astype(int)\n", "table_fin.expected_po_qty = table_fin.expected_po_qty.astype(int)\n", "\n", "table_fin.actual_inv.fillna(0, inplace=True)\n", "table_fin.actual_inv = table_fin.actual_inv.astype(int)\n", "table_fin.next_day_inv.fillna(0, inplace=True)\n", "table_fin.next_day_inv = table_fin.next_day_inv.astype(int)\n", "\n", "table_fin.avg_overall_qpc = table_fin.avg_overall_qpc.astype(str)\n", "table_fin.avg_overall_qpc = table_fin.avg_overall_qpc.str.slice(0, 7)\n", "\n", "table_fin.qpc_multiplier = table_fin.qpc_multiplier.astype(float)\n", "\n", "table_fin.bumped_up_qpc = table_fin.bumped_up_qpc.astype(str)\n", "table_fin.bumped_up_qpc = table_fin.bumped_up_qpc.str.slice(0, 7)\n", "\n", "table_fin.total_capacity = table_fin.total_capacity.fillna(0).astype(int)\n", "table_fin.fulfillment_percentage = table_fin.fulfillment_percentage.astype(float)\n", "table_fin.exp_capacity_fulfillment = table_fin.exp_capacity_fulfillment.astype(int)\n", "table_fin.orders_recvd = table_fin.orders_recvd.astype(int)\n", "table_fin.remaining_carts = table_fin.remaining_carts.astype(int)\n", "\n", "\n", "table_fin.landing_price = table_fin.landing_price.astype(float)\n", "table_fin.outward_guidelines = np.nan\n", "table_fin.saleable_guidelines = table_fin.saleable_guidelines.astype(int)\n", "\n", "table_fin.net_avail_qty = np.where(\n", "    (table_fin.net_avail_qty.isna()) | (table_fin.net_avail_qty <= 0),\n", "    0,\n", "    table_fin.net_avail_qty,\n", ")\n", "table_fin.net_avail_qty = table_fin.net_avail_qty.astype(float)\n", "\n", "table_fin.item_uom = table_fin.item_uom.astype(str)\n", "\n", "table_fin.l0 = np.where(table_fin.l0.isna(), \"Mapping not present\", table_fin.l0)\n", "table_fin.l0 = table_fin.l0.astype(str)\n", "table_fin.l1 = table_fin.l1.astype(str)\n", "table_fin.l2 = table_fin.l2.astype(str)\n", "\n", "\n", "table_fin.outlet_name = table_fin.outlet_name.str.slice(0, 40)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table_fin.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table_fin.rename(columns={\"slotB_qty_blocked\": \"slotb_qty_blocked\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"updated_at\", \"type\": \"timestamp\", \"description\": \"time of run\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item id\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar(60)\", \"description\": \"item name\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"outlet id\"},\n", "    {\"name\": \"outlet_name\", \"type\": \"varchar(40)\", \"description\": \"outlet name\"},\n", "    {\n", "        \"name\": \"current_inventory\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"usable inventory at delivery_date(ageing less than saleable guidelines)\",\n", "    },\n", "    {\n", "        \"name\": \"delivery_date\",\n", "        \"type\": \"timestamp\",\n", "        \"description\": \"customer delivery date\",\n", "    },\n", "    {\n", "        \"name\": \"total_capacity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"capacity of facility on delivery date\",\n", "    },\n", "    {\n", "        \"name\": \"orders_recvd\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"number of orders received on delivery date at a facility\",\n", "    },\n", "    {\n", "        \"name\": \"remaining_carts\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"how much more carts are expected to be received on delivery date at a facility\",\n", "    },\n", "    {\n", "        \"name\": \"avg_overall_qpc\",\n", "        \"type\": \"varchar(7)\",\n", "        \"description\": \"ratio of item qty to facility carts. calculated on live hour in last 3 days.\",\n", "    },\n", "    {\n", "        \"name\": \"extrapolated_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"how much more qty is expected to be received on delivery date for an item\",\n", "    },\n", "    {\n", "        \"name\": \"carts\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"number of carts received with the item\",\n", "    },\n", "    {\n", "        \"name\": \"qty_ordered\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"qty of the item received\",\n", "    },\n", "    {\n", "        \"name\": \"calculated_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"total qty of item expected to be received\",\n", "    },\n", "    {\n", "        \"name\": \"qty_picked\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"qty of item picked out of qty ordered\",\n", "    },\n", "    {\n", "        \"name\": \"billed_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"qty of item billed out of qty ordered\",\n", "    },\n", "    {\"name\": \"expected_po_qty\", \"type\": \"integer\", \"description\": \"expected po qty\"},\n", "    {\n", "        \"name\": \"actual_inv\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"actual inventory we have(incl po, excl picked) to fulfill calculated qty\",\n", "    },\n", "    {\n", "        \"name\": \"next_day_inv\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"next day opening inventory(excl dump)\",\n", "    },\n", "    {\"name\": \"inner_case_size\", \"type\": \"integer\", \"description\": \"inner case size\"},\n", "    {\n", "        \"name\": \"avg_qty_picked\",\n", "        \"type\": \"float\",\n", "        \"description\": \"average qty picked in last 3 days\",\n", "    },\n", "    {\n", "        \"name\": \"qty_required\",\n", "        \"type\": \"float\",\n", "        \"description\": \"how much more inventory needed to fulfill calculated qty\",\n", "    },\n", "    {\n", "        \"name\": \"qty_capped\",\n", "        \"type\": \"float\",\n", "        \"description\": \"qty after applying capping factor\",\n", "    },\n", "    {\"name\": \"app_live\", \"type\": \"integer\", \"description\": \"app status\"},\n", "    {\n", "        \"name\": \"is_on_metering\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"whether item is on metering\",\n", "    },\n", "    {\n", "        \"name\": \"inner_case_size_from_sheet\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"input inner case size\",\n", "    },\n", "    {\"name\": \"cap\", \"type\": \"float\", \"description\": \"capping factor\"},\n", "    {\"name\": \"landing_price\", \"type\": \"float\", \"description\": \"landing price\"},\n", "    {\n", "        \"name\": \"outward_guidelines\",\n", "        \"type\": \"int\",\n", "        \"description\": \"outward guidelines of item\",\n", "    },\n", "    {\n", "        \"name\": \"net_avail_qty\",\n", "        \"type\": \"float\",\n", "        \"description\": \"unblocked inventory at time of run\",\n", "    },\n", "    {\"name\": \"item_uom\", \"type\": \"varchar(30)\", \"description\": \"unit of measurement\"},\n", "    {\n", "        \"name\": \"qpc_multiplier\",\n", "        \"type\": \"float\",\n", "        \"description\": \"factor to increase/decrease average overall qpc\",\n", "    },\n", "    {\n", "        \"name\": \"bumped_up_qpc\",\n", "        \"type\": \"varchar(7)\",\n", "        \"description\": \"average overall qpc multiplied with qpc multiplier\",\n", "    },\n", "    {\n", "        \"name\": \"forecasted_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"bumped up qpc multiplied with expected capacity fulfillment\",\n", "    },\n", "    {\"name\": \"l0\", \"type\": \"varchar(30)\", \"description\": \"level 0 category of item\"},\n", "    {\"name\": \"l1\", \"type\": \"varchar(30)\", \"description\": \"level 1 category of item\"},\n", "    {\"name\": \"l2\", \"type\": \"varchar(30)\", \"description\": \"level 2 category of item\"},\n", "    {\n", "        \"name\": \"fulfillment_percentage\",\n", "        \"type\": \"float\",\n", "        \"description\": \"average percetage of capacity fulfilled in last 3 days\",\n", "    },\n", "    {\n", "        \"name\": \"exp_capacity_fulfillment\",\n", "        \"type\": \"int\",\n", "        \"description\": \"total capacity multiplied with fulfillment percentage\",\n", "    },\n", "    {\n", "        \"name\": \"saleable_guidelines\",\n", "        \"type\": \"int\",\n", "        \"description\": \"saleable guidelines of item\",\n", "    },\n", "    {\n", "        \"name\": \"slotb_qty_blocked\",\n", "        \"type\": \"int\",\n", "        \"description\": \"orders blocked for slot B on delivery date\",\n", "    },\n", "    {\n", "        \"name\": \"onshelf_inventory\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"onshelf inventory at delivery_date\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary = (\n", "    table_fin[table_fin.delivery_date != date.iloc[0][\"delivery_date\"]]\n", "    .groupby(\n", "        [\n", "            \"item_id\",\n", "            \"item_name\",\n", "            \"l0\",\n", "            \"outlet_id\",\n", "            \"outlet_name\",\n", "            \"item_uom\",\n", "            \"landing_price\",\n", "            \"net_avail_qty\",\n", "        ]\n", "    )\n", "    .agg({\"qty_capped\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin1 = fin1.astype(\n", "    {\n", "        \"item_id\": \"int\",\n", "        \"outlet_id\": \"int\",\n", "        \"forecasted_qty\": \"int\",\n", "        \"actual_inv\": \"int\",\n", "        \"slotB_qty_blocked\": \"int\",\n", "        \"current_inventory\": \"int\",\n", "        \"onshelf_inventory\": \"int\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(fin1, \"1sazGyWriKPuJdi1tqkeVvw1MMjJ1fk5MExgwNpEwwOA\", \"Detailed\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(summary, \"1sazGyWriKPuJdi1tqkeVvw1MMjJ1fk5MExgwNpEwwOA\", \"Summary\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"bread_indenting_details\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"item_id\", \"outlet_id\", \"updated_at\"],\n", "    \"sortkey\": [\"updated_at\", \"item_id\", \"outlet_id\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"Stores detailed log for perishables and FNV indent\",\n", "}\n", "\n", "pb.to_redshift(table_fin, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# fin1.to_csv('perishables_demand.csv', index = False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# summary.to_csv('perishables_demand_summary.csv')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}