{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import pencilbox as pb\n", "\n", "\n", "!pip install xlrd\n", "import xlrd\n", "\n", "!pip install openpyxl\n", "import openpyxl"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# hero = pd.read_excel(cwd + \"/Hero & bucket X.xlsx\", sheet_name=\"Hero Article\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# hero = hero[[\"Item No\", \"Facility\", \"Union\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# hero.columns = [\"item_id\", \"facility\", \"union\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql = \"\"\" \n", "with PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name as product_type\n", "   FROM lake_cms.GR_PRODUCT P\n", "   INNER JOIN lake_cms.GR_PRODUCT_CATEGORY_MAPPING PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.GR_CATEGORY C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.GR_CATEGORY C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.GR_CATEGORY C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   inner join lake_cms.gr_product_type pt on p.type_id=pt.id),\n", "   \n", "   \n", "   \n", "item_product_name as (SELECT item_id, product_id,(cat.product || ' ' || cat.unit) AS name,cat.product_type\n", "    FROM lake_rpc.item_product_mapping rpc\n", "    INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "    AND rpc.offer_id IS NULL\n", "    AND rpc.item_id IS NOT NULL\n", "    AND rpc.product_id IS NOT NULL),\n", "\n", "item_ptype as (SELECT distinct item_id, cat.product_type, cat.brand\n", "    FROM lake_rpc.item_product_mapping rpc\n", "    INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "    AND rpc.offer_id IS NULL\n", "    AND rpc.item_id IS NOT NULL\n", "    AND rpc.product_id IS NOT NULL\n", "),\n", "\n", "\n", "bucket_x_mapping as (\n", "    select\n", "    distinct\n", "    item_id,\n", "    o.facility_id,\n", "    tag_value\n", "    from lake_rpc.item_outlet_tag_mapping a\n", "    left join lake_retail.console_outlet o on a.outlet_id = o.id\n", "    where\n", "    tag_value in ('Y', 'N')\n", "    and a.active = true\n", "),\n", "\n", "fin_bucket_x_mapping as (\n", "\n", "select * from\n", "bucket_x_mapping\n", "\n", "union\n", "\n", "select item_id, 116, tag_value\n", "from bucket_x_mapping\n", "where facility_id in (32)\n", "\n", "-- union\n", "\n", "-- select item_id, 56, max(tag_value) as tag_value\n", "-- from bucket_x_mapping\n", "-- where facility_id in (48,45,51,56,68,22)\n", "-- group by 1,2\n", "\n", "-- union\n", "\n", "-- select item_id, 13, tag_value\n", "-- from bucket_x_mapping\n", "-- where facility_id in (12)\n", "\n", "),\n", "\n", "bucket_a_b_mapping as (\n", "    select\n", "    distinct\n", "    item_id,\n", "    o.facility_id,\n", "    tag_value\n", "    from lake_rpc.item_outlet_tag_mapping a\n", "    left join lake_retail.console_outlet o on a.outlet_id = o.id\n", "    where tag_value in ('A', 'B')\n", "    and a.active = true\n", "),\n", "\n", "fin_bucket_a_b_mapping as (\n", "\n", "select * from\n", "bucket_a_b_mapping\n", "\n", "union\n", "\n", "select item_id, 116, tag_value\n", "from bucket_a_b_mapping\n", "where facility_id in (32)\n", "\n", "-- union\n", "\n", "-- select item_id, 56, min(tag_value) as tag_value\n", "-- from bucket_a_b_mapping\n", "-- where facility_id in (48,45,51,56,68,22)\n", "-- group by 1,2\n", "\n", "-- union\n", "\n", "-- select item_id, 13, tag_value\n", "-- from bucket_a_b_mapping\n", "-- where facility_id in (12)\n", "\n", "),\n", "\n", "item_state as (\n", "select item_id, facility_id, (case when master_assortment_substate_id = 1 then 'active' else 'non active' end) as itm_state \n", "from\n", "    (select distinct item_id, o.facility_id, \n", "    rank() over (partition by item_id, o.facility_id order by pma.updated_at desc) as rnk,\n", "    master_assortment_substate_id\n", "    from\n", "    lake_rpc.product_master_assortment pma \n", "    left join lake_retail.console_outlet o\n", "    on pma.city_id = o.tax_location_id)\n", "where rnk = 1\n", "),\n", "\n", "itm_fac_asp as (\n", "select item_id, facility_id, sum(sp*Sales_Quantity)/sum(Sales_Quantity) as asp \n", "from(\n", "    select pp.item_id as item_id,\n", "     p.outlet_id,\n", "     outlet.facility_id,\n", "     ipd.selling_price as sp,\n", "     sum(ipd.quantity) as Sales_Quantity\n", "    from\n", "    lake_pos.view_pos_invoice p\n", "    JOIN lake_retail.console_outlet outlet ON outlet.id = p.outlet_id and outlet.type_id = 2\n", "    JOIN lake_pos.pos_invoice_product_details ipd ON p.id = ipd.invoice_id\n", "    JOIN lake_rpc.product_product pp ON pp.variant_id = ipd.variant_id\n", "    WHERE CONVERT_TIMEZONE('ASIA/KOLKATA',p.pos_timestamp)::DATE BETWEEN current_date-30 AND current_date-1\n", "    AND p.invoice_type_id = '1'\n", "    GROUP BY 1,2,3,4)\n", "GROUP By 1,2),\n", "\n", "stocked_out_pid as (SELECT traits__merchant_id as traits__merchant_id, traits__merchant_name as traits__merchant_name, custom__cart_id , substring(custom__pid_list,2,LEN(custom__pid_list)-2) as custom__pid_list \n", "    FROM spectrum.localytics_event_data3\n", "    WHERE at_date_ist = CURRENT_DATE - interval '1 days' \n", "    AND name = 'items_stocked_out_visit'\n", "    AND custom__cart_id > 0),\n", "    \n", "checkout_completed as (\n", "    SELECT CAST(properties__cart_id AS VARCHAR(10))\n", "    FROM spectrum.mobile_event_data\n", "    WHERE at_date_ist between CURRENT_DATE - interval '1 days' AND CURRENT_DATE\n", "    AND name = 'Checkout Step Completed'\n", "    AND properties__cart_id IS NOT NULL\n", "    GROUP BY 1),\n", "\n", "numbers as (select\n", "    n::int\n", "    from\n", "        (select \n", "          row_number() over (order by true) as n\n", "       from stocked_out_pid)\n", "        cross join\n", "      (select \n", "          max(regexp_count(custom__pid_list, '[,]')) as max_num \n", "       from stocked_out_pid)\n", "        where\n", "      n <= max_num + 1),\n", "  \n", "parsed_list as (select\n", "  traits__merchant_id, \n", "  traits__merchant_name,\n", "  custom__cart_id,\n", "  trim(split_part(custom__pid_list,',',n)) as pid \n", "    from\n", "      stocked_out_pid\n", "      cross join\n", "      numbers\n", "      where\n", "      split_part(custom__pid_list,',',n) is not null\n", "      and split_part(custom__pid_list,',',n) != ''),\n", "            \n", "open_po as(\n", "    SELECT (case\n", "                when o.facility_id = 1 then 'Ahmedabad'\n", "                when o.facility_id = 3 then 'Bengaluru'\n", "                when o.facility_id = 5 then 'Delhi'\n", "                when o.facility_id = 12 then 'Gurgaon'\n", "                when o.facility_id = 13 then 'G 2'\n", "                when o.facility_id = 14 then 'Hyderabad'\n", "                when o.facility_id = 15 then 'Jaipur'\n", "                when o.facility_id = 18 then 'Mumbai'\n", "                when o.facility_id = 21 then 'Noida'\n", "                when o.facility_id = 22 then 'Pune'\n", "                when o.facility_id = 24 then 'Kolkata'\n", "                when o.facility_id = 26 then 'G 3'\n", "                when o.facility_id = 29 then 'G 4'\n", "                when o.facility_id = 49 then 'G 5'\n", "                when o.facility_id = 30 then 'Lucknow'\n", "                when o.facility_id = 31 then 'N 2'\n", "                when o.facility_id = 32 then 'B 2'\n", "                when o.facility_id = 33 then 'Faridabad'\n", "                when o.facility_id = 34 then 'K 2'\n", "                when o.facility_id = 39 then 'Sonepath'\n", "                when o.facility_id = 40 then '<PERSON><PERSON><PERSON><PERSON>'\n", "                -- when o.facility_id = 41 then 'Bilaspur'\n", "                when o.facility_id = 42 then 'C 2'\n", "                when o.facility_id = 43 then 'H 2'\n", "                when o.facility_id = 45 then 'M 4'\n", "                when o.facility_id = 47 then '<PERSON><PERSON>'\n", "                when o.facility_id = 48 then 'M 2'\n", "                when o.facility_id = 49 then 'G 5'\n", "                when o.facility_id = 51 then 'M 5'\n", "                when o.facility_id = 56 then 'M 6'\n", "                when o.facility_id = 57 then 'H 2 Bulk'\n", "                when o.facility_id = 63 then 'Guwahati'\n", "                when o.facility_id = 68 then 'Indore'\n", "                when o.facility_id = 69 then 'Chandigarh'\n", "                when o.facility_id = 64 then 'Faridabad OS'\n", "                when o.facility_id = 92 then 'Dasna_2'\n", "                when o.facility_id = 116 then 'B2 Bulk'\n", "                when o.facility_id = 120 then 'Orange Something 2'\n", "                when o.facility_id = 118 then 'Kundli_P2'\n", "                when o.facility_id = 125 then 'DarkS1 - Gurgaon'\n", "                when o.facility_id = 132 then 'DarkS2 - Gurgaon'\n", "                when o.facility_id = 133 then 'DarkS1 - Ghaziabad'\n", "                when o.facility_id = 135 then 'Gurgaon DarkS3'\n", "                when o.facility_id = 139 then 'Bamnoli'\n", "                when o.facility_id = 140 then 'Mundka'\n", "                when o.facility_id = 149 then 'Mumbai EC'\n", "                else null end) as Facility,\n", "             o.facility_id,\n", "             p.po_number,\n", "             p.vendor_name,\n", "             CONVERT_TIMEZONE('ASIA/KOLKATA',issue_date)::DATE AS issue_date,\n", "             date(expiry_date) AS expiry_date,\n", "             date(ps.schedule_date_time) AS Schedule_Date_Time,\n", "             poi.item_id,\n", "             poi.units_ordered AS Qty,\n", "             poi.name,\n", "             poi.manufacturer_name,\n", "             poi.mrp,\n", "             poi.landing_rate* poi.units_ordered AS total_value,\n", "             (CASE WHEN itm_fac_asp.asp is not null then itm_fac_asp.asp \n", "                    WHEN item_asp.asp2 is not null then item_asp.asp2\n", "                    ELSE poi.mrp*0.7 END) AS asp,\n", "             CASE\n", "                 WHEN posta.id = 5 THEN 'Cancelled From Draft'\n", "                 ELSE posta.name\n", "             END AS state\n", "      FROM lake_po.purchase_order p\n", "      LEFT JOIN lake_po.po_schedule ps ON p.id=ps.po_id_id\n", "      INNER JOIN lake_po.purchase_order_items poi ON p.id=poi.po_id\n", "      INNER JOIN lake_retail.console_outlet o ON o.id=p.outlet_id\n", "      INNER JOIN lake_po.purchase_order_status posa ON posa.po_id = p.id\n", "      INNER JOIN lake_po.purchase_order_state posta ON posta.id = posa.po_state_id\n", "      LEFT JOIN itm_fac_asp ON itm_fac_asp.item_id = poi.item_id and itm_fac_asp.facility_id = o.facility_id\n", "      LEFT JOIN (select item_id, avg(asp) as asp2 from itm_fac_asp\n", "            GROUP BY 1) item_asp ON item_asp.item_id = poi.item_id\n", "      WHERE\n", "    posta.name IN ('Created',\n", "                'Scheduled',\n", "                'Unscheduled',\n", "                'Rescheduled')),\n", "                \n", "fin_open_po as (select open_po.po_number,\n", "      open_po.Item_Id,\n", "      b.product_type,\n", "      b.brand,\n", "      (case when c.tag_value = 'A' then 'Bucket A' when c.tag_value = 'B' then 'Bucket B' else 'Bucket C' end) as bucket,\n", "      (case when d.tag_value = 'Y' then 'Bucket X' else '-' end) as bucket_x,\n", "      item_state.itm_state,\n", "      open_po.facility_id,\n", "      open_po.Facility,\n", "      open_po.vendor_name,\n", "      open_po.manufacturer_name,\n", "      open_po.issue_date,\n", "      open_po.expiry_date,\n", "      open_po.Qty,\n", "      open_po.total_value,\n", "      open_po.state,\n", "      open_po.asp\n", "from open_po\n", "left join item_ptype b on open_po.item_id = b.item_id\n", "left join fin_bucket_a_b_mapping c on open_po.item_id = c.item_id and open_po.facility_id = c.facility_id\n", "left join fin_bucket_x_mapping d on open_po.item_id = d.item_id and open_po.facility_id = d.facility_id\n", "left join item_state on open_po.item_id = item_state.item_id and open_po.facility_id = item_state.facility_id),\n", "\n", "next_4_days_cpd as\n", "(SELECT item_id, facility_id,\n", "  max(cpd_today) AS cpd_today,\n", "  max(cpd_1) AS cpd_1,\n", "  max(cpd_2) AS cpd_2,\n", "  max(cpd_3) AS cpd_3,\n", "  max(cpd_4) AS cpd_4\n", "    FROM(      \n", "        SELECT s.item_id, o.facility_id, s.date, \n", "          (CASE\n", "              WHEN date = CURRENT_DATE THEN sum(s.consumption)\n", "            END) AS cpd_today, \n", "            (CASE\n", "              WHEN date = CURRENT_DATE+1 THEN sum(s.consumption)\n", "          END) AS cpd_1, \n", "          (CASE\n", "              WHEN date = CURRENT_DATE+2 THEN sum(s.consumption)\n", "          END) AS cpd_2, \n", "          (CASE\n", "              WHEN date = CURRENT_DATE+3 THEN sum(s.consumption)\n", "          END) AS cpd_3, \n", "          (CASE\n", "              WHEN date = CURRENT_DATE+4 THEN sum(s.consumption)\n", "          END) AS cpd_4\n", "          FROM lake_snorlax.date_wise_consumption s\n", "          INNER JOIN lake_retail.console_outlet o ON s.outlet_id = o.id\n", "          WHERE date(s.date) BETWEEN CURRENT_DATE AND CURRENT_DATE + 4\n", "             AND o.facility_id IS NOT NULL\n", "          GROUP BY 1,2,3)\n", "     GROUP BY 1,2),\n", "      \n", "fin_next_4_days_cpd as      \n", "    (SELECT item_id,\n", "          (CASE\n", "          WHEN facility_id = 1 THEN 'Ahmedabad'\n", "          WHEN facility_id = 32 THEN 'B 2'\n", "          WHEN facility_id = 3 THEN 'Bengaluru'\n", "        --   WHEN facility_id = 41 THEN 'Bilaspur'\n", "          WHEN facility_id = 42 THEN 'C 2'\n", "          WHEN facility_id = 47 THEN 'Dasna'\n", "          WHEN facility_id = 5 THEN 'Delhi'\n", "          WHEN facility_id = 33 THEN 'Faridabad'\n", "        --   WHEN facility_id = 13 THEN 'G 2'\n", "          WHEN facility_id = 26 THEN 'G 3'\n", "          WHEN facility_id = 29 THEN 'G 4'\n", "          WHEN facility_id = 49 THEN 'G 5'\n", "          WHEN facility_id = 12 THEN 'Gurgaon'\n", "          WHEN facility_id = 43 THEN 'H 2'\n", "          WHEN facility_id = 14 THEN 'Hyderabad'\n", "          WHEN facility_id = 15 THEN 'Jaipur'\n", "          WHEN facility_id = 34 THEN 'K 2'\n", "          WHEN facility_id = 40 THEN 'Kapashera'\n", "          WHEN facility_id = 24 THEN 'Kolkata'\n", "          WHEN facility_id = 30 THEN 'Lucknow'\n", "          WHEN facility_id = 48 THEN 'M 2'\n", "          WHEN facility_id = 45 THEN 'M 4'\n", "          WHEN facility_id = 51 THEN 'M 5'\n", "          WHEN facility_id = 18 THEN 'Mumbai'\n", "          WHEN facility_id = 31 THEN 'N 2'\n", "          WHEN facility_id = 21 THEN 'Noida'\n", "          WHEN facility_id = 22 THEN 'Pune'\n", "          WHEN facility_id = 39 THEN 'Sonepath'\n", "          WHEN facility_id = 49 THEN 'G 5'\n", "          WHEN facility_id = 68 THEN 'Indore'\n", "          WHEN facility_id = 69 THEN 'Chandigarh'\n", "          WHEN facility_id = 63 THEN 'Guwahati'\n", "          WHEN facility_id = 64 THEN 'Faridabad OS'\n", "          WHEN facility_id = 57 THEN 'H 2 Bulk'\n", "          WHEN facility_id = 4 THEN 'Chennai'\n", "          when facility_id = 92 then 'Dasna_2'\n", "        when facility_id = 120 then 'Orange Something 2'\n", "        when facility_id = 118 then 'Kundli_P2'\n", "        when facility_id = 125 then 'DarkS1 - Gurgaon'\n", "        when facility_id = 132 then 'DarkS2 - Gurgaon'\n", "        when facility_id = 133 then 'DarkS1 - Ghaziabad'\n", "        when facility_id = 135 then 'Gurgaon DarkS3'\n", "        when facility_id = 139 then 'Bamnoli'\n", "        when facility_id = 140 then 'Mundka'\n", "        when facility_id = 149 then 'Mumbai EC'\n", "            \n", "          ELSE NULL\n", "          END) AS facility, cpd_today, cpd_1, cpd_2, cpd_3, cpd_4 \n", "        from\n", "        next_4_days_cpd\n", "    \n", "    UNION\n", "    \n", "    SELECT item_id, 'G 2', sum(cpd_today) as cpd_today, sum(cpd_1) as cpd_1, sum(cpd_2) as cpd_2, sum(cpd_3) as cpd_3, sum(cpd_4) as cpd_4 \n", "    from next_4_days_cpd\n", "    where facility_id in (47,33,26,29,12,69,15,40,30,31,39,92)\n", "    GROUP BY item_id\n", "    \n", "    UNION\n", "    \n", "    SELECT item_id, 'M 6', sum(cpd_today) as cpd_today, sum(cpd_1) as cpd_1, sum(cpd_2) as cpd_2, sum(cpd_3) as cpd_3, sum(cpd_4) as cpd_4 \n", "    from next_4_days_cpd\n", "    where facility_id in (48,45,51,68,22)\n", "    GROUP BY item_id\n", "    \n", "    UNION\n", "    \n", "    SELECT item_id, 'B2 Bulk', sum(cpd_today) as cpd_today, sum(cpd_1) as cpd_1, sum(cpd_2) as cpd_2, sum(cpd_3) as cpd_3, sum(cpd_4) as cpd_4 \n", "     FROM next_4_days_cpd\n", "     where facility_id in (32,3,42,43)\n", "    GROUP BY item_id),\n", "\n", "avg_30_days_cpd as \n", "(select item_id, facility_id, facility, avg(cpd) as avg_cpd from(\n", "    select a.item_id, o.facility_id, \n", "    (CASE\n", "          WHEN o.facility_id = 1 THEN 'Ahmedabad'\n", "          WHEN o.facility_id = 32 THEN 'B 2'\n", "          WHEN o.facility_id = 3 THEN 'Bengaluru'\n", "          WHEN o.facility_id = 42 THEN 'C 2'\n", "          WHEN o.facility_id = 47 THEN 'Dasna'\n", "          WHEN o.facility_id = 5 THEN 'Delhi'\n", "          WHEN o.facility_id = 33 THEN 'Faridabad'\n", "        --   WHEN o.facility_id = 13 THEN 'G 2'\n", "          WHEN o.facility_id = 26 THEN 'G 3'\n", "          WHEN o.facility_id = 29 THEN 'G 4'\n", "          WHEN o.facility_id = 49 THEN 'G 5'\n", "          WHEN o.facility_id = 12 THEN 'Gurgaon'\n", "          WHEN o.facility_id = 43 THEN 'H 2'\n", "          WHEN o.facility_id = 14 THEN 'Hyderabad'\n", "          WHEN o.facility_id = 15 THEN 'Jaipur'\n", "          WHEN o.facility_id = 34 THEN 'K 2'\n", "          WHEN o.facility_id = 40 THEN 'Kapashera'\n", "          WHEN o.facility_id = 24 THEN 'Kolkata'\n", "          WHEN o.facility_id = 30 THEN 'Lucknow'\n", "          WHEN o.facility_id = 48 THEN 'M 2'\n", "          WHEN o.facility_id = 45 THEN 'M 4'\n", "          WHEN o.facility_id = 51 THEN 'M 5'\n", "          WHEN o.facility_id = 18 THEN 'Mumbai'\n", "          WHEN o.facility_id = 31 THEN 'N 2'\n", "          WHEN o.facility_id = 21 THEN 'Noida'\n", "          WHEN o.facility_id = 22 THEN 'Pune'\n", "          WHEN o.facility_id = 39 THEN 'Sonepath'\n", "          WHEN o.facility_id = 48 THEN 'M 2'\n", "          WHEN o.facility_id = 49 THEN 'G 5'\n", "          WHEN o.facility_id = 68 THEN 'Indore'\n", "          WHEN o.facility_id = 69 THEN 'Chandigarh'\n", "          WHEN o.facility_id = 63 THEN 'Guwahati'\n", "          WHEN o.facility_id = 64 THEN 'Faridabad OS'\n", "          WHEN o.facility_id = 57 THEN 'H 2 Bulk'\n", "          WHEN o.facility_id = 4 THEN 'Chennai'\n", "          WHEN o.facility_id = 118 THEN 'Kundli_P2'\n", "          WHEN o.facility_id = 92 THEN 'Dasna_2'\n", "          when o.facility_id = 125 then 'DarkS1 - Gurgaon'\n", "            when o.facility_id = 132 then 'DarkS2 - Gurgaon'\n", "            when o.facility_id = 133 then 'DarkS1 - Ghaziabad'\n", "            when o.facility_id = 135 then 'Gurgaon DarkS3'\n", "            when o.facility_id = 139 then 'Bamnoli'\n", "            when o.facility_id = 140 then 'Mundka'\n", "            when o.facility_id = 149 then 'Mumbai EC'\n", "          ELSE NULL\n", "        END) AS facility, sum(a.consumption) as cpd, date(date) as date\n", "    from lake_snorlax.date_wise_consumption a\n", "    left join\n", "    lake_retail.console_outlet o on a.outlet_id = o.id\n", "    where date between current_date and current_date+30\n", "    and facility is not null\n", "    GROUP BY 1,2,3,5\n", "    )\n", "GROUP BY 1,2,3),\n", "\n", "fin_avg_30_days_cpd as (\n", "    select item_id, facility, avg_cpd \n", "    from avg_30_days_cpd\n", "    \n", "    union\n", "    \n", "    select item_id, 'G 2', sum(avg_cpd) as avg_cpd\n", "    from avg_30_days_cpd\n", "    where facility_id in (47,33,26,29,12,69,15,40,30,31,39,92)\n", "    group by 1\n", "    \n", "    union\n", "    \n", "    select item_id, 'M 6', sum(avg_cpd) as avg_cpd\n", "    from avg_30_days_cpd\n", "    where facility_id in (48,45,51,68,22)\n", "    group by 1\n", "    \n", "    union\n", "    \n", "    select item_id, 'B2 Bulk', sum(avg_cpd) as avg_cpd\n", "    from avg_30_days_cpd\n", "    where facility_id in (32,3,42,43)\n", "    group by 1\n", "    \n", "    ),\n", "\n", "ptype_next_4_days_cpd as (select\n", "    b.product_type, \n", "    a.facility, \n", "    sum(a.cpd_today) as cpd_today,\n", "    sum(a.cpd_1) as cpd_1,\n", "    sum(a.cpd_2) as cpd_2,\n", "    sum(a.cpd_3) as cpd_3,\n", "    sum(a.cpd_4) as cpd_4\n", "from fin_next_4_days_cpd a\n", "left join \n", "item_ptype b on a.item_id = b.item_id\n", "where product_type is not null\n", "group by 1,2\n", "),\n", "\n", "ptype_avg_cpd as (select \n", "    b.product_type, \n", "    a.facility, \n", "    sum(a.avg_cpd) as avg_cpd\n", "from fin_avg_30_days_cpd a\n", "left join \n", "item_ptype b on a.item_id = b.item_id\n", "where product_type is not null\n", "group by 1,2),\n", "\n", "current_inventory as (select a.item_id, a.outlet_id, a.actual_quantity, (case when b.blocked_quantity is null then 0 else b.blocked_quantity end) as blocked_quantity from(\n", "\n", "    (select item_id, outlet_id, quantity as actual_quantity from lake_ims.ims_item_inventory) a\n", "    \n", "    left join\n", "    \n", "    (select l.* from(\n", "        (select item_id, outlet_id, quantity as blocked_quantity, updated_at from lake_ims.ims_item_blocked_inventory) l\n", "        inner join\n", "        (select item_id, outlet_id, max(updated_at) as updated_at from lake_ims.ims_item_blocked_inventory\n", "        where date(updated_at) = current_date\n", "        group by 1,2) m\n", "        on l.item_id = m.item_id and l.outlet_id = m.outlet_id and l.updated_at = m.updated_at)) b\n", "\n", "    on a.item_id = b.item_id and a.outlet_id = b.outlet_id)\n", "),\n", "\n", "selected_outlet as (select id, name, facility_id from lake_retail.console_outlet where facility_id is not null\n", "                and (name like '%%SSC_%%' or name like '%%MODI_%%')\n", "                and name not like 'Not In Service Area %%' ),        \n", "\n", "itm_fac_inv as (\n", "    select item_id, facility_id, sum(Net_Quantity) as inventory from(    \n", "        select inv.item_id, inv.outlet_id, o.facility_id, o.name as o_name, \n", "                (inv.actual_quantity - inv.blocked_quantity) as Net_Quantity \n", "                from current_inventory inv\n", "                inner join\n", "                selected_outlet o\n", "                ON inv.outlet_id = o.id)\n", "    group by 1,2),\n", "\n", "item_inv as (select item_id,(CASE\n", "              WHEN facility_id = 1 THEN 'Ahmedabad'\n", "              WHEN facility_id = 32 THEN 'B 2'\n", "              WHEN facility_id = 3 THEN 'Bengaluru'\n", "              WHEN facility_id = 42 THEN 'C 2'\n", "              WHEN facility_id = 47 THEN 'Dasna'\n", "              WHEN facility_id = 5 THEN 'Delhi'\n", "              WHEN facility_id = 33 THEN 'Faridabad'\n", "            --   WHEN facility_id = 13 THEN 'G 2'\n", "              WHEN facility_id = 26 THEN 'G 3'\n", "              WHEN facility_id = 29 THEN 'G 4'\n", "              WHEN facility_id = 49 THEN 'G 5'\n", "              WHEN facility_id = 12 THEN 'Gurgaon'\n", "              WHEN facility_id = 43 THEN 'H 2'\n", "              WHEN facility_id = 14 THEN 'Hyderabad'\n", "              WHEN facility_id = 15 THEN 'Jaipur'\n", "              WHEN facility_id = 34 THEN 'K 2'\n", "              WHEN facility_id = 40 THEN 'Kapashera'\n", "              WHEN facility_id = 24 THEN 'Kolkata'\n", "              WHEN facility_id = 30 THEN 'Lucknow'\n", "              WHEN facility_id = 48 THEN 'M 2'\n", "              WHEN facility_id = 45 THEN 'M 4'\n", "              WHEN facility_id = 51 THEN 'M 5'\n", "              WHEN facility_id = 18 THEN 'Mumbai'\n", "              WHEN facility_id = 31 THEN 'N 2'\n", "              WHEN facility_id = 21 THEN 'Noida'\n", "              WHEN facility_id = 22 THEN 'Pune'\n", "              WHEN facility_id = 39 THEN 'Sonepath'\n", "              WHEN facility_id = 49 THEN 'G 5'\n", "              WHEN facility_id = 68 THEN 'Indore'\n", "              WHEN facility_id = 69 THEN 'Chandigarh'\n", "              WHEN facility_id = 63 THEN 'Guwahati'\n", "              WHEN facility_id = 64 THEN 'Faridabad OS'\n", "              WHEN facility_id = 57 THEN 'H 2 Bulk'\n", "              WHEN facility_id = 4 THEN 'Chennai'\n", "              when facility_id = 92 then 'Dasna_2'\n", "            when facility_id = 120 then 'Orange Something 2'\n", "            when facility_id = 118 then 'Kundli_P2'\n", "            when facility_id = 125 then 'DarkS1 - Gurgaon'\n", "            when facility_id = 132 then 'DarkS2 - Gurgaon'\n", "            when facility_id = 133 then 'DarkS1 - Ghaziabad'\n", "            when facility_id = 135 then 'Gurgaon DarkS3'\n", "            when facility_id = 139 then 'Bamnoli'\n", "            when facility_id = 140 then 'Mundka'\n", "            when facility_id = 149 then 'Mumbai EC'\n", "              ELSE NULL\n", "              END) AS facility, inventory\n", "              \n", "            from itm_fac_inv\n", "                \n", "            union\n", "            \n", "            select item_id, 'G 2', sum(inventory) as inventory \n", "            from itm_fac_inv\n", "            where facility_id in (47,33,26,29,49,12,69,15,40,30,31,39,92,118,13)\n", "            group by 1\n", "            \n", "            union\n", "            \n", "            select item_id, 'M 6', sum(inventory) as inventory \n", "            from itm_fac_inv\n", "            where facility_id in (48,45,51,56,68,22)\n", "            group by 1\n", "            \n", "            union\n", "            \n", "            select item_id, 'B2 Bulk', sum(inventory) as inventory \n", "            from itm_fac_inv\n", "            where facility_id in (32,3,42,43,116)\n", "            group by 1\n", "    ),\n", "\n", "ptype_inv as (select b.product_type, a.facility, sum(a.inventory) as inventory\n", "from item_inv a\n", "left join item_ptype b\n", "on a.item_id = b.item_id\n", "where a.facility is not null\n", "group by 1,2),\n", "\n", "ptype_doi as (select product_type, facility,\n", "            (case when (inventory-cpd_today) <= 0 then 0 else (inventory-cpd_today)/(avg_cpd+0.00000001) end) as doi_1,\n", "            (case when (inventory-cpd_today-cpd_1) <= 0 then 0 else (inventory-cpd_today-cpd_1)/(avg_cpd+0.00000001) end) as doi_2,\n", "            (case when (inventory-cpd_today-cpd_1-cpd_2) <= 0 then 0 else (inventory-cpd_today-cpd_1-cpd_2)/(avg_cpd+0.00000001) end) as doi_3,\n", "            (case when (inventory-cpd_today-cpd_1-cpd_2-cpd_3) <= 0 then 0 else (inventory-cpd_today-cpd_1-cpd_2-cpd_3)/(avg_cpd+0.00000001) end) as doi_4\n", "            from(\n", "                select a.*, (case when b.avg_cpd is null then 0 else b.avg_cpd end) as avg_cpd,\n", "                (case when c.cpd_today is null then 0 else c.cpd_today end) as cpd_today, \n", "                (case when c.cpd_1 is null then 0 else c.cpd_1 end) as cpd_1, \n", "                (case when c.cpd_2 is null then 0 else c.cpd_2 end) as cpd_2, \n", "                (case when c.cpd_3 is null then 0 else c.cpd_3 end) as cpd_3, \n", "                (case when c.cpd_4 is null then 0 else c.cpd_4 end) as cpd_4\n", "                from ptype_inv a\n", "                left join ptype_avg_cpd b on a.product_type = b.product_type and a.facility = b.facility\n", "                left join ptype_next_4_days_cpd c on a.product_type = c.product_type and a.facility = c.facility\n", "                where a.product_type is not null)),\n", "\n", "tbl_join as (\n", "SELECT Facility, vendor_name, manufacturer_name, po_number, item_id, product_type, brand, bucket, bucket_x, itm_state, issue_date, expiry_date, state, Qty, total_value, inventory, cpd_today, doi_1, doi_2, doi_3, doi_4,\n", "  (CASE\n", "        WHEN ideal_inv_1 = 0 AND inv_lack_1 > 0 THEN inv_lack_1*ASP\n", "        WHEN ideal_inv_1 = 0 AND inv_lack_1 = 0 THEN cpd_1*ASP\n", "        ELSE 0\n", "    END) AS GMV_Loss_1,\n", "  (CASE\n", "        WHEN ideal_inv_1 = 0  AND inv_lack_1 > 0 THEN ceil(inv_lack_1)\n", "        WHEN ideal_inv_1 = 0  AND inv_lack_1 = 0 THEN ceil(cpd_1)\n", "        ELSE 0\n", "    END) AS Qty_Loss_1,\n", "  (CASE\n", "        WHEN ideal_inv_1 = 0 THEN 1\n", "        ELSE 0\n", "    END) AS Stock_out_1,\n", "  \n", "  (CASE\n", "        WHEN ideal_inv_2 = 0 AND inv_lack_2 > 0 THEN inv_lack_2*ASP\n", "        WHEN ideal_inv_2 = 0 AND inv_lack_2 = 0 THEN cpd_2*ASP\n", "        ELSE 0\n", "    END) AS GMV_Loss_2,\n", "  (CASE\n", "        WHEN ideal_inv_2 = 0  AND inv_lack_2 > 0 THEN ceil(inv_lack_2)\n", "        WHEN ideal_inv_2 = 0  AND inv_lack_2 = 0 THEN ceil(cpd_2)\n", "        ELSE 0\n", "    END) AS Qty_Loss_2,\n", "  (CASE\n", "        WHEN ideal_inv_2 = 0 THEN 1\n", "        ELSE 0\n", "    END) AS Stock_out_2,\n", "  \n", "  (CASE\n", "        WHEN ideal_inv_3 = 0 AND inv_lack_3 > 0 THEN inv_lack_3*ASP\n", "        WHEN ideal_inv_3 = 0 AND inv_lack_3 = 0 THEN cpd_3*ASP\n", "        ELSE 0\n", "    END) AS GMV_Loss_3,\n", "  (CASE\n", "        WHEN ideal_inv_3 = 0  AND inv_lack_3 > 0 THEN ceil(inv_lack_3)\n", "        WHEN ideal_inv_3 = 0  AND inv_lack_3 = 0 THEN ceil(cpd_3)\n", "        ELSE 0\n", "    END) AS Qty_Loss_3,\n", "  (CASE\n", "        WHEN ideal_inv_3 = 0 THEN 1\n", "        ELSE 0\n", "    END) AS Stock_out_3,\n", "    \n", "  (CASE\n", "        WHEN ideal_inv_4 = 0 AND inv_lack_4 > 0 THEN inv_lack_4*ASP\n", "        WHEN ideal_inv_4 = 0 AND inv_lack_4 = 0 THEN cpd_4*ASP\n", "        ELSE 0\n", "    END) AS GMV_Loss_4,\n", "  (CASE\n", "        WHEN ideal_inv_4 = 0  AND inv_lack_4 > 0 THEN ceil(inv_lack_4)\n", "        WHEN ideal_inv_4 = 0  AND inv_lack_4 = 0 THEN ceil(cpd_4)\n", "        ELSE 0\n", "    END) AS Qty_Loss_4,\n", "  (CASE\n", "        WHEN ideal_inv_4 = 0 THEN 1\n", "        ELSE 0\n", "    END) AS Stock_out_4\n", "from\n", "    (select inv_.*, \n", "    (case when doi.doi_1 is null then 0 else doi.doi_1 end) as doi_1,\n", "    (case when doi.doi_2 is null then 0 else doi.doi_2 end) as doi_2,\n", "    (case when doi.doi_3 is null then 0 else doi.doi_3 end) as doi_3,\n", "    (case when doi.doi_4 is null then 0 else doi.doi_4 end) as doi_4,\n", "        (case when cpd.cpd_today is null then 0 else cpd.cpd_today end) as cpd_today,\n", "              (CASE \n", "                  WHEN (inv_.inventory-cpd.cpd_today) > cpd.cpd_1 THEN (inv_.inventory-cpd.cpd_today)\n", "                  ELSE 0 \n", "                END) AS ideal_inv_1, -- Ideal inventory for tomorrow\n", "            (CASE \n", "                  WHEN (inv_.inventory-cpd.cpd_today) > cpd.cpd_1 THEN 0\n", "                  WHEN (inv_.inventory-cpd.cpd_today)*-1 > 0 THEN 0\n", "                  ELSE (inv_.inventory-cpd.cpd_today-cpd.cpd_1)*-1\n", "                END) AS inv_lack_1,    \n", "             (case when cpd.cpd_1 is null then 0 else cpd.cpd_1 end) as cpd_1, -- Tomorrow's cpd\n", "             (CASE\n", "                  WHEN (inv_.inventory-cpd.cpd_today-cpd.cpd_1) > cpd.cpd_2 THEN (inv_.inventory-cpd.cpd_today-cpd.cpd_1)\n", "                  ELSE 0\n", "              END) AS ideal_inv_2,\n", "             (CASE\n", "                  WHEN (inv_.inventory-cpd.cpd_today-cpd.cpd_1) > cpd.cpd_2 THEN 0\n", "                  WHEN(inv_.inventory-cpd.cpd_today-cpd.cpd_1)*-1 > 0 THEN 0\n", "                  ELSE (inv_.inventory-cpd.cpd_today-cpd.cpd_1-cpd.cpd_2)*-1\n", "              END) AS inv_lack_2,\n", "             (case when cpd.cpd_2 is null then 0 else cpd.cpd_2 end) as cpd_2,\n", "             (CASE\n", "                  WHEN (inv_.inventory-cpd.cpd_today-cpd.cpd_1-cpd.cpd_2) > cpd.cpd_3 THEN (inv_.inventory-cpd.cpd_today-cpd.cpd_1-cpd.cpd_2)\n", "                  ELSE 0\n", "              END) AS ideal_inv_3,\n", "              (CASE\n", "                  WHEN (inv_.inventory-cpd.cpd_today-cpd.cpd_1-cpd.cpd_2) > cpd.cpd_3 THEN 0\n", "                  WHEN (inv_.inventory-cpd.cpd_today-cpd.cpd_1-cpd.cpd_2)*-1 > 0 THEN 0\n", "                  ELSE (inv_.inventory-cpd.cpd_today-cpd.cpd_1-cpd.cpd_2-cpd.cpd_3)*-1\n", "              END) AS inv_lack_3,\n", "             (case when cpd.cpd_3 is null then 0 else cpd.cpd_3 end) as cpd_3,\n", "             (CASE\n", "                  WHEN (inv_.inventory-cpd.cpd_today-cpd.cpd_1-cpd.cpd_2-cpd.cpd_3) > cpd.cpd_4 THEN (inv_.inventory-cpd.cpd_today-cpd.cpd_1-cpd.cpd_2-cpd.cpd_3)\n", "                  ELSE 0\n", "              END) AS ideal_inv_4,\n", "              (CASE\n", "                  WHEN (inv_.inventory-cpd.cpd_today-cpd.cpd_1-cpd.cpd_2-cpd.cpd_3) > cpd.cpd_4 THEN 0\n", "                  WHEN (inv_.inventory-cpd.cpd_today-cpd.cpd_1-cpd.cpd_2-cpd.cpd_3)*-1 > 0 THEN 0\n", "                  ELSE (inv_.inventory-cpd.cpd_today-cpd.cpd_1-cpd.cpd_2-cpd.cpd_3-cpd.cpd_4)*-1\n", "              END) AS inv_lack_4,\n", "             (case when cpd.cpd_4 is null then 0 else cpd.cpd_4 end) as cpd_4\n", "                \n", "    from \n", "    \n", "    (select po.*, (case when inv1.inventory is null then 0 else inv1.inventory end) as inventory \n", "    from fin_open_po po \n", "    left join item_inv inv1\n", "    on inv1.item_id = po.item_id and inv1.facility = po.facility) inv_\n", "    \n", "    left join \n", "    ptype_doi doi \n", "    on inv_.product_type = doi.product_type and inv_.facility = doi.facility \n", "    \n", "    left join\n", "    fin_next_4_days_cpd cpd \n", "    on cpd.item_id = inv_.item_id and cpd.facility = inv_.facility\n", "    )\n", "\n", "where facility is not null)\n", "\n", "select * from tbl_join\n", "\n", "\n", "\"\"\"\n", "\n", "open_po = pd.read_sql(sql, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# open_po_hero = open_po.merge(hero, on=[\"item_id\", \"facility\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# open_po_hero[\"is_hero\"] = np.where(open_po_hero.union.isna(), 0, 1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_po_hero = open_po.copy()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Calculate Index"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# open_po_hero[\"index_1\"] = np.where(\n", "#     open_po_hero.bucket_x == \"Bucket X\",\n", "#     1,\n", "#     np.where(\n", "#         (open_po_hero.bucket == \"Bucket A\") & (open_po_hero.is_hero == 1),\n", "#         0.9,\n", "#         np.where(\n", "#             open_po_hero.bucket == \"Bucket A\",\n", "#             0.8,\n", "#             np.where(\n", "#                 (open_po_hero.bucket == \"Bucket B\") & (open_po_hero.is_hero == 1),\n", "#                 0.7,\n", "#                 0.5,\n", "#             ),\n", "#         ),\n", "#     ),\n", "# )\n", "\n", "# open_po_hero[\"index_2\"] = np.where(\n", "#     open_po_hero.bucket_x == \"Bucket X\",\n", "#     1,\n", "#     np.where(\n", "#         (open_po_hero.bucket == \"Bucket A\") & (open_po_hero.is_hero == 1),\n", "#         0.9,\n", "#         np.where(\n", "#             open_po_hero.bucket == \"Bucket A\",\n", "#             0.8,\n", "#             np.where(\n", "#                 (open_po_hero.bucket == \"Bucket B\") & (open_po_hero.is_hero == 1),\n", "#                 0.7,\n", "#                 0.5,\n", "#             ),\n", "#         ),\n", "#     ),\n", "# )\n", "\n", "# open_po_hero[\"index_3\"] = np.where(\n", "#     open_po_hero.bucket_x == \"Bucket X\",\n", "#     1,\n", "#     np.where(\n", "#         (open_po_hero.bucket == \"Bucket A\") & (open_po_hero.is_hero == 1),\n", "#         0.9,\n", "#         np.where(\n", "#             open_po_hero.bucket == \"Bucket A\",\n", "#             0.8,\n", "#             np.where(\n", "#                 (open_po_hero.bucket == \"Bucket B\") & (open_po_hero.is_hero == 1),\n", "#                 0.7,\n", "#                 0.5,\n", "#             ),\n", "#         ),\n", "#     ),\n", "# )\n", "\n", "# open_po_hero[\"index_4\"] = np.where(\n", "#     open_po_hero.bucket_x == \"Bucket X\",\n", "#     1,\n", "#     np.where(\n", "#         (open_po_hero.bucket == \"Bucket A\") & (open_po_hero.is_hero == 1),\n", "#         0.9,\n", "#         np.where(\n", "#             open_po_hero.bucket == \"Bucket A\",\n", "#             0.8,\n", "#             np.where(\n", "#                 (open_po_hero.bucket == \"Bucket B\") & (open_po_hero.is_hero == 1),\n", "#                 0.7,\n", "#                 0.5,\n", "#             ),\n", "#         ),\n", "#     ),\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_po_hero[\"index_1\"] = np.where(\n", "    open_po_hero.bucket_x == \"Bucket X\",\n", "    1,\n", "    np.where(\n", "        open_po_hero.bucket == \"Bucket A\",\n", "        0.8,\n", "        np.where(\n", "            open_po_hero.bucket == \"Bucket B\",\n", "            0.6,\n", "            0.5,\n", "        ),\n", "    ),\n", ")\n", "\n", "open_po_hero[\"index_2\"] = np.where(\n", "    open_po_hero.bucket_x == \"Bucket X\",\n", "    1,\n", "    np.where(\n", "        open_po_hero.bucket == \"Bucket A\",\n", "        0.8,\n", "        np.where(\n", "            open_po_hero.bucket == \"Bucket B\",\n", "            0.6,\n", "            0.5,\n", "        ),\n", "    ),\n", ")\n", "\n", "open_po_hero[\"index_3\"] = np.where(\n", "    open_po_hero.bucket_x == \"Bucket X\",\n", "    1,\n", "    np.where(\n", "        open_po_hero.bucket == \"Bucket A\",\n", "        0.8,\n", "        np.where(\n", "            open_po_hero.bucket == \"Bucket B\",\n", "            0.6,\n", "            0.5,\n", "        ),\n", "    ),\n", ")\n", "\n", "open_po_hero[\"index_4\"] = np.where(\n", "    open_po_hero.bucket_x == \"Bucket X\",\n", "    1,\n", "    np.where(\n", "        open_po_hero.bucket == \"Bucket A\",\n", "        0.8,\n", "        np.where(\n", "            open_po_hero.bucket == \"Bucket B\",\n", "            0.6,\n", "            0.5,\n", "        ),\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# open_po_hero.to_csv('item_level_PO_priority_with_hero.csv', index = False)\n", "\n", "# open_po.head()\n", "\n", "# open_po_hero[open_po_hero.po_number == \"1772308\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Calculate rank"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_po_cal = open_po_hero.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_po_cal.gmv_loss_1 = open_po_cal.gmv_loss_1 * open_po_cal.index_1\n", "open_po_cal.qty_loss_1 = open_po_cal.qty_loss_1 * open_po_cal.index_1\n", "open_po_cal.stock_out_1 = open_po_cal.stock_out_1 * open_po_cal.index_1\n", "\n", "open_po_cal.gmv_loss_2 = open_po_cal.gmv_loss_2 * open_po_cal.index_2\n", "open_po_cal.qty_loss_2 = open_po_cal.qty_loss_2 * open_po_cal.index_2\n", "open_po_cal.stock_out_2 = open_po_cal.stock_out_2 * open_po_cal.index_2\n", "\n", "open_po_cal.gmv_loss_3 = open_po_cal.gmv_loss_3 * open_po_cal.index_3\n", "open_po_cal.qty_loss_3 = open_po_cal.qty_loss_3 * open_po_cal.index_3\n", "open_po_cal.stock_out_3 = open_po_cal.stock_out_3 * open_po_cal.index_3\n", "\n", "open_po_cal.gmv_loss_4 = open_po_cal.gmv_loss_4 * open_po_cal.index_4\n", "open_po_cal.qty_loss_4 = open_po_cal.qty_loss_4 * open_po_cal.index_4\n", "open_po_cal.stock_out_4 = open_po_cal.stock_out_4 * open_po_cal.index_4"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_po_cal.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary = (\n", "    open_po_cal.groupby(\n", "        [\"facility\", \"vendor_name\", \"po_number\", \"issue_date\", \"expiry_date\", \"state\"]\n", "    )\n", "    .agg(\n", "        {\n", "            \"manufacturer_name\": \"max\",\n", "            \"qty\": \"sum\",\n", "            \"total_value\": \"sum\",\n", "            \"gmv_loss_1\": \"sum\",\n", "            \"gmv_loss_2\": \"sum\",\n", "            \"gmv_loss_3\": \"sum\",\n", "            \"gmv_loss_4\": \"sum\",\n", "            \"qty_loss_1\": \"sum\",\n", "            \"qty_loss_2\": \"sum\",\n", "            \"qty_loss_3\": \"sum\",\n", "            \"qty_loss_4\": \"sum\",\n", "            \"stock_out_1\": \"sum\",\n", "            \"stock_out_2\": \"sum\",\n", "            \"stock_out_3\": \"sum\",\n", "            \"stock_out_4\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary[\"geom_mean_1\"] = (\n", "    summary[\"gmv_loss_1\"] * summary[\"qty_loss_1\"] * summary[\"stock_out_1\"]\n", ") ** (1 / 3)\n", "summary[\"geom_mean_2\"] = (\n", "    summary[\"gmv_loss_2\"] * summary[\"qty_loss_2\"] * summary[\"stock_out_2\"]\n", ") ** (1 / 3)\n", "summary[\"geom_mean_3\"] = (\n", "    summary[\"gmv_loss_3\"] * summary[\"qty_loss_3\"] * summary[\"stock_out_3\"]\n", ") ** (1 / 3)\n", "summary[\"geom_mean_4\"] = (\n", "    summary[\"gmv_loss_4\"] * summary[\"qty_loss_4\"] * summary[\"stock_out_4\"]\n", ") ** (1 / 3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin = summary[\n", "    [\n", "        \"facility\",\n", "        \"vendor_name\",\n", "        \"manufacturer_name\",\n", "        \"po_number\",\n", "        \"issue_date\",\n", "        \"expiry_date\",\n", "        \"qty\",\n", "        \"total_value\",\n", "        \"geom_mean_1\",\n", "        \"geom_mean_2\",\n", "        \"geom_mean_3\",\n", "        \"geom_mean_4\",\n", "        \"state\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin[\"po_vendor_rank\"] = (\n", "    fin.sort_values(\n", "        [\"facility\", \"geom_mean_1\", \"expiry_date\"], ascending=[True, False, True]\n", "    )\n", "    .groupby([\"facility\", \"vendor_name\", \"manufacturer_name\"])\n", "    .cumcount()\n", "    + 1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ranked = (\n", "    fin[fin[\"po_vendor_rank\"] == 1]\n", "    .reset_index()\n", "    .drop(axis=1, columns=[\"po_vendor_rank\", \"index\"])\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ranked[\"rnk\"] = (\n", "    ranked.sort_values(\"geom_mean_1\", ascending=False).groupby(\"facility\").cumcount()\n", "    + 1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_ranked = ranked.sort_values([\"facility\", \"rnk\"])\n", "fin_ranked = fin_ranked.reset_index()\n", "fin_ranked = fin_ranked.drop(\"index\", axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_ranked = fin_ranked[\n", "    [\n", "        \"facility\",\n", "        \"vendor_name\",\n", "        \"manufacturer_name\",\n", "        \"po_number\",\n", "        \"issue_date\",\n", "        \"expiry_date\",\n", "        \"qty\",\n", "        \"total_value\",\n", "        \"geom_mean_1\",\n", "        \"geom_mean_2\",\n", "        \"geom_mean_3\",\n", "        \"geom_mean_4\",\n", "        \"rnk\",\n", "        \"state\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_ranked.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, date, timedelta\n", "\n", "today = (datetime.today() + <PERSON><PERSON>ta(hours=5, minutes=30)).date()\n", "\n", "fin_ranked.to_csv(\"PO_priority \" + today.strftime(\"%Y-%m-%d\") + \".csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sending mailers\n", "import jinja2\n", "from datetime import date\n", "from jinja2 import Template\n", "\n", "from_email = \"<EMAIL>\"\n", "subject = \"PO priority for %s\" % date.today()\n", "\n", "to_email = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "\n", "# loader = jinja2.FileSystemLoader(\n", "#     searchpath=\"/usr/local/airflow/dags/repo/dags/warehouse/inventory/report/stock_take/\")\n", "\n", "# tmpl_environ = jinja2.Environment(loader=loader)\n", "\n", "# template = tmpl_environ.get_template(\"email_template.html\")\n", "\n", "# df12 = df4\n", "# message_text = template.render(\n", "#     products=df12.to_dict(orient=\"records\"), today=today, outlet=outlet_id\n", "# )\n", "\n", "\n", "pb.send_email(\n", "    from_email,\n", "    to_email,\n", "    subject,\n", "    html_content=\"<head></head><p>Hi,</p> <p>PFA PO priority report for today.</p> <p><PERSON><PERSON>,<br /><PERSON><PERSON>ran</p>\",\n", "    files=[\"PO_priority \" + today.strftime(\"%Y-%m-%d\") + \".csv\"],\n", ")\n", "\n", "print(\"mail sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}