{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ignoring warning\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Importing Libraries\n", "import sqlalchemy as sqla\n", "import pandas as pd\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "import numpy as np\n", "import pencilbox as pb\n", "\n", "# import pandas_redshift as pr\n", "# from gr_mailer import send_email"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"redshift\")\n", "redshift.schema = \"consumer\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cms query which checkes available item and map item_id with p_type and brand_id.\n", "# mapp each item to outlet by using gr_merchant table\n", "product_query = \"\"\"\n", "\n", "\n", "\n", "\n", "with item_detail as\n", "(\n", "SELECT distinct\n", "pp.item_id,\n", "ipm.product_id,\n", "p.type as product_type,\n", "p.type_id as p_type,\n", "CAT.NAME AS l0,\n", "CAT.ID AS l0_ID,\n", "pb.name as brand,\n", "pb.id as brand_id\n", "--P.ENABLED_FLAG\n", "FROM\n", "GR_PRODUCT P\n", "INNER JOIN GR_PRODUCT_CATEGORY_MAPPING PCM ON P.ID = PCM.PRODUCT_ID\n", "INNER JOIN GR_CATEGORY CAT2 ON PCM.CATEGORY_ID = CAT2.ID AND PCM.IS_PRIMARY=TRUE\n", "INNER JOIN GR_CATEGORY CAT1 ON CAT2.PARENT_CATEGORY_ID = CAT1.ID\n", "INNER JOIN GR_CATEGORY CAT ON  CAT1.PARENT_CATEGORY_ID = CAT.ID\n", "inner join consumer.rpc_item_product_mapping ipm on ipm.product_id=p.id and ipm.item_id is not null\n", "left join pos_product_product pp on pp.item_id=ipm.item_id\n", "left join rpc_product_brand pb on pb.id=pp.brand_id\n", "where pp.active =1 and pp.approved=1 \n", "and p.name not ilike '%%patanjali%%'\n", "and cat2.name not ilike '%%baby food%%'\n", "and cat2.name not ilike '%%12 months+%%'\n", "and cat2.name not ilike '%%6-12 months%%'\n", "and cat2.name not ilike '%%0-6 months%%'\n", "and cat2.name not ilike '%%Other Baby Food%%'\n", "and cat2.name not ilike '%%12-18 months%%'\n", "and cat2.name not ilike '%%18-24 months%%'\n", "and cat2.name not ilike '%%24 months+%%'\n", "and p.type not ilike '%%Junior Cereal%%'\n", "and p.type not ilike '%%Baby Cereal%%'\n", "and p.type not ilike '%%Infant Formula%%'\n", "and p.type not ilike '%%Follow up Formula%%'\n", "and p.type not ilike '%%Baby Milk Powder%%'\n", "and cat.id not in (9,1487)\n", "and pp.item_id not in (10045065,10045062,10045603,10045063)\n", "),\n", "\n", "final_item as\n", "(\n", "select a.*\n", "from item_detail as a\n", "inner join (select item_id,min(brand_id) as brand_id from item_detail group by 1) as b\n", "on a.item_id=b.item_id\n", "and a.brand_id=b.brand_id\n", "),\n", "\n", "item_merchant as\n", "(\n", "select distinct a.*,\n", "mpm.merchant_id\n", "from final_item as a\n", "inner join gr_merchant_product_mapping as mpm on a.product_id=mpm.product_id\n", "where mpm.inventory_limit>0 \n", "and mpm.enabled_flag='true'\n", "and IS_AVAILABLE = 'true'),\n", "\n", "merchant_mapping as\n", "(\n", "select \n", "    frontend.id frontend_id, \n", "    frontend.name frontend_name,\n", "    backend.id backend_id,\n", "    backend.name backend_name\n", "from gr_merchant frontend\n", "join gr_virtual_to_real_merchant_mapping vmm\n", "    on frontend.id=vmm.virtual_merchant_id \n", "join gr_merchant backend\n", "    on backend.id=vmm.real_merchant_id\n", "where \n", "    frontend.enabled_flag ='true'\n", "    and vmm.enabled_flag='true'\n", "   \n", "and  backend.name not ilike '%%Smart Bachat Club%%'\n", "and backend.name not ilike '%%Donation%%'\n", "and backend.name not ilike '%%Collection%%'\n", "and backend.name not ilike '%%Collect Return%%'\n", "and backend.name not ilike '%%Dummy%%'\n", "and backend.name not ilike '%%test%%'\n", ")\n", ",\n", "\n", "final_data as\n", "(\n", "select distinct  a.*\n", "from item_merchant a \n", "inner join merchant_mapping m\n", "on m.backend_id=a.merchant_id\n", "),\n", "\n", "item_outlet as\n", "(\n", "select distinct\n", "current_date as assort_date,\n", "mpm.p_type,\n", "mpm.product_type,\n", "mpm.BRAND_ID,\n", "mpm.BRAND,\n", "pos.id as outlet_id,\n", "pos.name as outlet_name,\n", "pcl.name as city\n", "from final_data mpm\n", "inner join Rt_console_outlet_cms_store rt on mpm.merchant_id=rt.cms_store\n", "inner join Pos_console_outlet pos on rt.outlet_id=pos.id\n", "inner join pos_console_location pcl on pos.tax_location_id=pcl.id\n", "where rt.Active='true'\n", "and pos.Active='true'\n", "and pos.name like '%%SSC%%'\n", ")\n", "\n", "select * from item_outlet\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["product_detail = pd.read_sql_query(sql=product_query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["product_detail.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dropping duplicates\n", "product_detail = product_detail.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# connecting to postgres\n", "pricing_engine_uri = \"******************************************************************************************************************************/pricing\"\n", "pricing_engine = sqla.create_engine(pricing_engine_uri)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# taking latest 2 days of data\n", "Pricing_base_query = \"\"\"\n", "with masterrule_base as\n", "(\n", "select \n", "  cast(config::JSON->>'p_type' as integer) as p_type,\n", "  cast(config::JSON->>'brand_id' as integer) as brand_id,\n", "  cast(config::JSON->>'l2_category' as integer) as l2_category,\n", "  cast(config::JSON->>'manufacturer_id' as integer)as manufacturer_id,\n", "  update_ts,\n", "  case when config::JSON ->>'all_outlets' = 'true'\n", "    then (SELECT cast('{' || string_agg( '\"'||retail_outlet_id || '\" : ' || active , ',') || '}' as json) as outlets\n", "    from \n", "    ( \n", "    select distinct \n", "        retail_outlet_id,\n", "        active\n", "    from Pricing_domain_outlet\n", "    WHERE active = 'true'\n", "    ) as outlet_base\n", "    )else cast(config::JSON ->>'outlets' as json)\n", "  end as outlet_json,\n", "    start_ts as pricing_start_ts,\n", "    end_ts as pricing_end_ts\n", "    from rule_management_masterrule as rmm\n", "    where date(update_ts) between (now()- interval '2 day')and now()\n", "    and rmm.rule_type='RetainedMarginTemplate'\n", "    and rmm.Status='ACTIVE_CALLBACK_SUCCESS'\n", "    ),\n", "    \n", "    \n", "    price_with_base as\n", "    (\n", "    select distinct\n", "    outlet_id,\n", "    p_type,\n", "    brand_id,\n", "    update_ts,\n", "    pricing_start_ts,\n", "    pricing_end_ts\n", "    from\n", "    (\n", "    select distinct \n", "    cast(json_object_keys(outlet_json) as integer) as outlet_id,\n", "    cast(json_extract_path(outlet_json,json_object_keys(outlet_json)) as text) as outlet_flag,\n", "\n", "    p_type,\n", "    brand_id,\n", "    update_ts,\n", "    pricing_start_ts,\n", "    pricing_end_ts\n", "     \n", "    \n", "FROM masterrule_base\n", ")as a\n", "where outlet_flag='true'\n", ")\n", "\n", "\n", "select * from  price_with_base\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# reading query\n", "pricing_base = pd.read_sql_query(sql=Pricing_base_query, con=pricing_engine)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_base.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# loading csv file which has past data\n", "historian_data_query = \"\"\"\n", "select * from metrics.masterrule_max\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["historian_data = pd.read_sql_query(sql=historian_data_query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["historian_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["historian_data = historian_data.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_base.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["historian_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_frame_final = pd.concat([historian_data, pricing_base])\n", "pricing_concat = pricing_frame_final.drop_duplicates()\n", "pricing_concat = pricing_concat.reset_index()\n", "pricing_concat.drop([\"index\"], inplace=True, axis=1)\n", "pricing_concat[\"update_ts\"] = pd.to_datetime(pricing_concat[\"update_ts\"], utc=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["max_update = (\n", "    pricing_concat.groupby([\"outlet_id\", \"p_type\", \"brand_id\"])[\"update_ts\"]\n", "    .max()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["max_update.head()\n", "max_update[\n", "    [\"p_type\", \"brand_id\", \"outlet_id\"]\n", "].drop_duplicates().shape, max_update.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["price_joining = max_update.merge(\n", "    pricing_concat, on=[\"brand_id\", \"outlet_id\", \"p_type\", \"update_ts\"], how=\"inner\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["price_joining.pricing_start_ts = pd.to_datetime(\n", "    price_joining.pricing_start_ts, utc=True\n", ").dt.date\n", "price_joining.pricing_end_ts = pd.to_datetime(\n", "    price_joining.pricing_end_ts, utc=True\n", ").dt.date\n", "price_joining.update_ts = pd.to_datetime(price_joining.update_ts, utc=True).dt.date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"outlet_id\", \"type\": \"int\"},\n", "    {\"name\": \"p_type\", \"type\": \"int\"},\n", "    {\"name\": \"brand_id\", \"type\": \"int\"},\n", "    {\"name\": \"update_ts\", \"type\": \"date\"},\n", "    {\"name\": \"pricing_end_ts\", \"type\": \"date\"},\n", "    {\"name\": \"pricing_start_ts\", \"type\": \"date\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"masterrule_max\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"outlet_id\", \"p_type\", \"brand_id\"],\n", "    \"sortkey\": \"date_of_snapshot\",\n", "    \"incremental_key\": \"date_of_snapshot\",\n", "    \"load_type\": \"truncate\",  # append, rebuild, truncate or upsert\n", "}\n", "\n", "pb.to_redshift(price_joining, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_rm = product_detail.merge(\n", "    price_joining, on=[\"brand_id\", \"outlet_id\", \"p_type\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_rm.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_rm.describe()\n", "pricing_rm.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_rm.assort_date = pd.to_datetime(pricing_rm.assort_date, utc=True).dt.date\n", "pricing_rm.pricing_start_ts = pd.to_datetime(\n", "    pricing_rm.pricing_start_ts, utc=True\n", ").dt.date\n", "pricing_rm.pricing_end_ts = pd.to_datetime(pricing_rm.pricing_end_ts, utc=True).dt.date\n", "pricing_rm.update_ts = pd.to_datetime(pricing_rm.update_ts, utc=True).dt.date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_rm[\"final_flag\"] = (pricing_rm.assort_date <= pricing_rm.pricing_end_ts) & (\n", "    pricing_rm.assort_date >= pricing_rm.pricing_start_ts\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_ids = pd.DataFrame(\n", "    [\n", "        116,\n", "        240,\n", "        99,\n", "        235,\n", "        292,\n", "        309,\n", "        113,\n", "        135,\n", "        299,\n", "        337,\n", "        339,\n", "        103,\n", "        242,\n", "        260,\n", "        263,\n", "        85,\n", "        246,\n", "        346,\n", "        352,\n", "        100,\n", "        238,\n", "        279,\n", "        280,\n", "        314,\n", "        316,\n", "        340,\n", "        354,\n", "        334,\n", "        343,\n", "        287,\n", "        289,\n", "        369,\n", "        370,\n", "        367,\n", "        396,\n", "        478,\n", "        475,\n", "        520,\n", "        522,\n", "        482,\n", "        481,\n", "        493,\n", "        514,\n", "        491,\n", "        512,\n", "        505,\n", "        503,\n", "    ],\n", "    columns=[\"outlet_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# a=np.isin(pricing_rm,outlet_ids,invert=True)\n", "outlet_ids.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_outlet = pricing_rm.merge(outlet_ids, on=[\"outlet_id\"], how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["p_type_not_detailed = pricing_outlet[pricing_outlet.final_flag == False]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["p_type_not_detailed.drop(columns=[\"final_flag\", \"update_ts\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_rm_final = p_type_not_detailed[\n", "    [\n", "        \"assort_date\",\n", "        \"product_type\",\n", "        \"p_type\",\n", "        \"brand\",\n", "        \"brand_id\",\n", "        \"city\",\n", "        \"outlet_name\",\n", "        \"outlet_id\",\n", "        \"pricing_start_ts\",\n", "        \"pricing_end_ts\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_rm_final.drop(\n", "    columns=[\"pricing_start_ts\", \"pricing_end_ts\", \"assort_date\"], inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_final = pricing_rm_final.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_final.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["todays_date = datetime.now().strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_final.to_csv(\n", "    \"/tmp/RM_input_not_received-{}.csv\".format(todays_date),\n", "    encoding=\"utf8\",\n", "    index=False,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sender = \"<EMAIL>\"\n", "to = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "# to=[\"<EMAIL>\"]\n", "# to=[\"<EMAIL>\"]\n", "subject = \"[Pricing] RM input not received - Report\"\n", "message_text = \"\"\"\n", "\n", "Hi,\n", "\n", "\n", "The below reports shows ptype and brand for each city outlet where no rm input was uploaded in system for today.\n", "\n", "\n", "\"\"\"\n", "\n", "print(\"Sending message\")\n", "\n", "pb.send_email(\n", "    sender,\n", "    to,\n", "    subject,\n", "    message_text,\n", "    files=[\"/tmp/RM_input_not_received-{}.csv\".format(todays_date)],\n", "    dryrun=False,\n", "    cc=None,\n", "    mime_subtype=\"mixed\",\n", "    mime_charset=\"utf-8\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}}, "nbformat": 4, "nbformat_minor": 2}