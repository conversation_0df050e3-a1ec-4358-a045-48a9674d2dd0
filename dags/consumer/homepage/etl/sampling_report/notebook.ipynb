{"cells": [{"cell_type": "code", "execution_count": null, "id": "9eb4c66b-95af-46c8-af36-a5fc579e2eb6", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "from datetime import date, datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "83427818-61ad-4585-80ce-68f7143e65e8", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1hVVz9TZsB8w2f-ESLkiqHBSmD7K4GsGL85a8UXnQy6I\"\n", "sheet_name = \"Sheet1\"\n", "df1 = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "b9000ecf-d3c7-4ac0-96e7-b4b5c6f0f826", "metadata": {}, "outputs": [], "source": ["df1[\"product_id\"] = df1[\"product_id\"].replace(to_replace=\"\", value=0)\n", "df1[\"rate\"] = df1[\"rate\"].replace(to_replace=\"#N/A\", value=0)"]}, {"cell_type": "code", "execution_count": null, "id": "bd84b98c-8b3e-494a-b666-b361baceb4c8", "metadata": {}, "outputs": [], "source": ["df1[\"product_id\"] = df1[\"product_id\"].astype(\"int\")\n", "df1[\"rate\"] = df1[\"rate\"].astype(\"float\")\n", "# df['start_date']=df['start_date'].astype('datetime64[ns]')"]}, {"cell_type": "code", "execution_count": null, "id": "5616bd94-70be-4f48-93fb-171085d7c980", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"consumer\",\n", "    \"table_name\": \"sampling_price\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"product_id\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"product_id of sampled product\",\n", "        },\n", "        {\"name\": \"rate\", \"type\": \"float\", \"description\": \"Price per sample\"},\n", "        # {\"name\": \"start_date\", \"type\": \"Date\", \"description\": \"start date of freebie distribution\"}\n", "    ],\n", "    \"primary_key\": [\"product_id\"],\n", "    # \"sortkey\": [\"date\"],\n", "    # \"incremental_key\": \"date\",\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Table stores data for associated pid of freebie pids\",\n", "}\n", "\n", "pb.to_redshift(df1, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "e86ae191-48b7-4d20-92ee-beab5c5dc224", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "04e24f91-6c1b-4a47-b392-739c9019a070", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cf438f3e-8882-4250-ac4b-95257cac6316", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f1ee33e3-08ed-4fa9-bae9-50fde5bdeb76", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2e99c6cc-cf61-4c57-8220-76f5eb4187bb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f87ecdc7-9ae8-4c34-b120-68350b874a1a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e8f6d4c7-9a86-4f7d-b868-cb9cb7ac91e6", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1hVVz9TZsB8w2f-ESLkiqHBSmD7K4GsGL85a8UXnQy6I\"\n", "sheet_name = \"Sheet3\"\n", "df = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "d5e109d9-54bd-4590-84cb-5c0482d63f45", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b2f8b02f-4ab1-4275-91b3-9d579933a8bb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "68b0e0f9-b63e-4ae4-b855-4f0a714d11f7", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib"]}, {"cell_type": "code", "execution_count": null, "id": "536f6f6a-0f42-47be-8d1f-68bf626dbfce", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=7.0,\n", "    row_height=1.325,\n", "    font_size=35,\n", "    header_color=\"#E96125\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array(\n", "            [col_width, row_height]\n", "        )\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        bbox=bbox,\n", "        cellLoc=\"center\",\n", "        colLabels=data.columns,\n", "        **kwargs\n", "    )\n", "    # mpl_table.auto_set_column_width(col=list(range(len(df_v1.columns))))\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        #         elif k[0] == data.shape[0]:\n", "        #             cell.set_text_props(weight=\"bold\", ma=\"center\")\n", "        # cell.set_facecolor('#f1f1f2')\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "46c90bc7-52e2-4a96-baa6-99896df5269c", "metadata": {}, "outputs": [], "source": ["bmhpc_b_image = \"/tmp/Sampling_summary.png\""]}, {"cell_type": "code", "execution_count": null, "id": "39fd04ea-ba6a-4d92-a4c1-dd26e9929654", "metadata": {}, "outputs": [], "source": ["# fig, ax = render_mpl_table(final_summary, header_columns=0)\n", "\n", "fig1, ax1 = render_mpl_table(df)\n", "# fig2, ax2 = render_mpl_table(df_mu)\n", "# fig3, ax3 = render_mpl_table(df_hy)\n", "\n", "fig1.savefig(bmhpc_b_image)\n", "# fig2.savefig(bmhpc_m_image)\n", "# fig3.savefig(bmhpc_h_image)"]}, {"cell_type": "code", "execution_count": null, "id": "0c43edf6-f677-4802-81d6-a01af832ff2f", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"bl-sampling-execution\",\n", "    text=\"Sampling Report\",\n", "    files=[\"/tmp/Sampling_summary.png\"],\n", ")\n", "# bl-sampling-execution"]}, {"cell_type": "code", "execution_count": null, "id": "7a9358d2-b3f2-47e1-a9e1-004b526580ef", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9ebc894b-ad80-409d-92b7-35e7078996f9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}