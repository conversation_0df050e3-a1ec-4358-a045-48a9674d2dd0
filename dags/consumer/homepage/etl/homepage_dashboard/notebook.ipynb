{"cells": [{"cell_type": "code", "execution_count": null, "id": "26d7d567-ea8e-463d-a094-7bb1928cbcee", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime, date, timedelta\n", "from time import time\n", "\n", "# !pip install pyarrow"]}, {"cell_type": "code", "execution_count": null, "id": "e292a6ea-6f01-4e80-9fe6-80cd96b1e12f", "metadata": {}, "outputs": [], "source": ["t0 = time()\n", "redshift_connection = pb.get_connection(\"[Warehouse] Redshift\")\n", "date_filter = date.today() - <PERSON><PERSON><PERSON>(days=1)\n", "date_filter"]}, {"cell_type": "code", "execution_count": null, "id": "3e9c9e50-52d0-4f1f-8b8c-92ab78218576", "metadata": {}, "outputs": [], "source": ["base1 = f\"\"\" \n", "\n", "drop table if exists impressions;\n", "\n", "\n", "\n", "create temp table impressions as \n", "(\n", "select at_date_ist,traits__city_name  as city,case when platform like 'android' then 'android' else 'ios' end as platform, device_uuid,properties__widget_title widget_title, \n", "--properties__widget_name widget_name,\n", "properties__child_widget_title child_widget_title\n", "from spectrum.mobile_impression_data\n", "where name in ('Image Shown','Promotional Carousel Shown','Promotional Carousel Banner Shown',\n", "'Product Shown','Static Banner Shown','Product Shelf Widget Shown','Banner Widget Shown')\n", "and at_date_ist > current_date-10\n", "and properties__page_name in ('Homepage', 'feed')\n", "group by 1,2,3,4,5,6\n", ");\n", "\n", "\"\"\"\n", "t2 = time()\n", "redshift_connection.execute(base1)\n", "t3 = time()\n", "query_elapsed1 = t3 - t2\n", "print(\"Elapsed time to run the query is %f seconds.\" % query_elapsed1)"]}, {"cell_type": "code", "execution_count": null, "id": "d0d66f21-4e9c-4dc8-9397-7b8839b16068", "metadata": {}, "outputs": [], "source": ["base2 = f\"\"\" \n", "\n", "drop table if exists click;\n", "\n", "\n", "\n", "create temp table click as \n", "(\n", "select e.at_date_ist,e.traits__city_name  as city,\n", "case when e.platform like 'android' then 'android' else 'ios' end as platform\n", ", e.device_uuid,e.properties__widget_title widget_title, \n", "--e.properties__widget_name widget_name,\n", "e.properties__child_widget_title child_widget_title\n", "from spectrum.mobile_event_data as e\n", "--from impressions as i\n", "--join spectrum.mobile_event_data as e on i.at_date_ist=e.at_date_ist and lower(i.device_uuid)=lower(e.device_uuid) and \n", " --e.properties__widget_title=i.widget_title\n", " --and e.properties__child_widget_title=i.child_widget_title\n", "where e.name in  ('Promotional Carousel Banner Clicked','Static Banner Clicked','Image Clicked','Product Clicked','Banner Widget Clicked')\n", "and e.at_date_ist > current_date-10\n", "and e.properties__page_name in ('Homepage', 'feed')\n", "group by 1,2,3,4,5,6\n", ");\n", "\n", "\"\"\"\n", "t2 = time()\n", "redshift_connection.execute(base2)\n", "t3 = time()\n", "query_elapsed1 = t3 - t2\n", "print(\"Elapsed time to run the query is %f seconds.\" % query_elapsed1)"]}, {"cell_type": "code", "execution_count": null, "id": "*************-457d-8886-0d11e997850a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8e27175b-7e6b-481a-8d15-5b7d116504de", "metadata": {}, "outputs": [], "source": ["base3 = f\"\"\" \n", "\n", "drop table if exists homepage_atc;\n", "\n", "\n", "create temp table homepage_atc as \n", "(\n", "select at_date_ist,traits__city_name  as city,case when platform like 'android' then 'android' else 'ios' end as platform,device_uuid,properties__widget_title widget_title, \n", "--properties__widget_name widget_name,\n", "properties__child_widget_title child_widget_title,properties__mrp mrp\n", "from spectrum.mobile_event_data \n", "where name like 'Product Added' and properties__page_name in ('Homepage', 'feed')\n", "and at_date_ist > current_date-10\n", "--group by 1,2,3,4,5,6,7\n", ");\n", "\n", "\n", "\n", "\n", "\"\"\"\n", "t2 = time()\n", "redshift_connection.execute(base3)\n", "t3 = time()\n", "query_elapsed1 = t3 - t2\n", "print(\"Elapsed time to run the query is %f seconds.\" % query_elapsed1)"]}, {"cell_type": "code", "execution_count": null, "id": "bbd99eb8-be8d-40ae-a277-71ce7f9c1566", "metadata": {}, "outputs": [], "source": ["# base4 = f\"\"\"\n", "\n", "# drop table if exists product_page_atc;\n", "\n", "# create temp table product_page_atc as\n", "# (\n", "\n", "# with base as\n", "# (\n", "# select at_date_ist, device_uuid, properties__page_visit_id,properties__entry_source_title as widget_title,\n", "# --properties__entry_source_name as widget_name,\n", "# properties__entry_source_child_title as child_widget_title\n", "# FROM spectrum.mobile_impression_data\n", "# WHERE\n", "#     name = 'Product Page Visit'\n", "#     and at_date_ist > current_date-7\n", "#     and lower(properties__last_page_name) in  ('homepage', 'feed')\n", "# )\n", "\n", "\n", "# select e.at_date_ist,traits__city_name  as city,case when platform like 'android' then 'android' else 'ios' end as platform,widget_title,e.device_uuid,--widget_name,\n", "# child_widget_title,e.properties__mrp mrp\n", "# from spectrum.mobile_event_data as e\n", "# join base as b on b.device_uuid=e.device_uuid and b.at_date_ist=e.at_date_ist and b.properties__page_visit_id=e.properties__page_visit_id\n", "# where name = 'Product Added'\n", "# group by 1,2,3,4,5,6,7\n", "# );\n", "\n", "\n", "# \"\"\"\n", "# t2 = time()\n", "# redshift_connection.execute(base4)\n", "# t3 = time()\n", "# query_elapsed1 = t3 - t2\n", "# print(\"Elapsed time to run the query is %f seconds.\" % query_elapsed1)"]}, {"cell_type": "code", "execution_count": null, "id": "8312da61-13d4-43d8-9150-a56bf2cbbc5f", "metadata": {}, "outputs": [], "source": ["base5 = f\"\"\" \n", "\n", "drop table if exists product_list_atc;\n", "drop table if exists page_visits;\n", "create temp table page_visits as\n", "(\n", "\n", "select at_date_ist,device_uuid,traits__city_name as city,\n", "properties__entry_source_title widget_title,properties__entry_source_child_title child_widget_title,\n", "properties__page_visit_id,platform\n", "from spectrum.mobile_impression_data\n", "WHERE\n", "    name in ( 'Product List Visit','Landing Pages Visit')\n", "    and at_date_ist > current_date-10\n", "    and properties__last_page_name in  ( 'feed','Homepage')\n", "group by 1,2,3,4,5,6,7\n", ");\n", "create temp table product_list_atc as (\n", "\n", "\n", "\n", "\n", "\n", "with direct_atc as\n", "(\n", "select i.at_date_ist,i.device_uuid,widget_title,child_widget_title,i.platform,properties__mrp as mrp,city\n", "from spectrum.mobile_event_data as i\n", "join page_visits as a on a.at_date_ist=i.at_date_ist and a.device_uuid=i.device_uuid and a.properties__page_visit_id=i.properties__page_visit_id\n", "where name like 'Product Added' \n", "and i.at_date_ist > current_date-10\n", "\n", ")\n", ",\n", "\n", "\n", "\n", " base1 as \n", "(\n", "select m.at_date_ist, m.device_uuid, m.properties__page_visit_id, widget_title,\n", " child_widget_title\n", "FROM spectrum.mobile_impression_data as m\n", "join page_visits as a on a.at_date_ist=m.at_date_ist and a.device_uuid=m.device_uuid and a.properties__page_visit_id=m.properties__last_page_visit_id\n", "WHERE\n", "    name = 'Product List Visit'\n", "    and m.at_date_ist > current_date-10\n", "    \n", "),\n", "\n", "product_list_atc1 as (\n", "select e.at_date_ist,e.device_uuid,widget_title,\n", "child_widget_title,e.platform,properties__mrp as mrp,traits__city_name as city\n", "from spectrum.mobile_event_data as e\n", "join base1 as b on b.device_uuid=e.device_uuid and b.at_date_ist=e.at_date_ist and b.properties__page_visit_id=e.properties__page_visit_id\n", "where name = 'Product Added'\n", "--group by 1,2,3,4,5,6,7,8\n", ")\n", "\n", "\n", "select at_date_ist,city, device_uuid,widget_title,\n", "child_widget_title,case when platform like 'android' then 'android' else 'ios' end as platform,mrp\n", "from product_list_atc1\n", "union all\n", "select at_date_ist,city,  device_uuid,widget_title,\n", "child_widget_title,case when platform like 'android' then 'android' else 'ios' end as platform,mrp\n", "from direct_atc\n", "\n", ");\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\"\"\"\n", "t2 = time()\n", "redshift_connection.execute(base5)\n", "t3 = time()\n", "query_elapsed1 = t3 - t2\n", "print(\"Elapsed time to run the query is %f seconds.\" % query_elapsed1)"]}, {"cell_type": "code", "execution_count": null, "id": "b3a748af-fb85-40bb-ad9c-15f5848d0931", "metadata": {}, "outputs": [], "source": ["# base6 = f\"\"\"\n", "\n", "# drop table if exists landing_page_atc;\n", "\n", "# create temp table landing_page_atc as\n", "# (\n", "# with base as\n", "# (\n", "# select at_date_ist, device_uuid, properties__page_visit_id,properties__entry_source_title as widget_title,\n", "# --properties__entry_source_name as widget_name,\n", "# properties__entry_source_child_title as child_widget_title\n", "# FROM spectrum.mobile_impression_data\n", "# WHERE\n", "#     name = '<PERSON> Pages Visit'\n", "#     and at_date_ist> current_date-7\n", "#     and lower(properties__last_page_name) in  ('homepage', 'feed')\n", "# )\n", "\n", "# select e.at_date_ist,traits__city_name  as city,case when platform like 'android' then 'android' else 'ios' end as platform,widget_title,e.device_uuid,--widget_name,\n", "# child_widget_title,e.properties__mrp mrp\n", "# from spectrum.mobile_event_data as e\n", "# join base as b on b.device_uuid=e.device_uuid and b.at_date_ist=e.at_date_ist and b.properties__page_visit_id=e.properties__page_visit_id\n", "# where name = 'Product Added'\n", "# --group by 1,2,3,4,5,6,7\n", "\n", "# );\n", "\n", "\n", "# \"\"\"\n", "# t2 = time()\n", "# redshift_connection.execute(base6)\n", "# t3 = time()\n", "# query_elapsed1 = t3 - t2\n", "# print(\"Elapsed time to run the query is %f seconds.\" % query_elapsed1)"]}, {"cell_type": "code", "execution_count": null, "id": "1cb81638-06a6-4a53-b4bd-4326e1345511", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "92603022-fbd8-401c-a3b1-8d1a4efc485a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "bc6787cd-2fc0-4520-8aa9-9435e5b6b1a3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3dc5a605-df9a-44a9-a75b-3da0fea13656", "metadata": {}, "outputs": [], "source": ["l0_query = \"\"\"\n", "with impressions_fin as\n", "(\n", "select at_date_ist,city,platform, count(distinct device_uuid) as devices, count( device_uuid) as impr\n", "from impressions\n", "group by 1,2,3\n", "\n", "),\n", "\n", "clicks_fin as (\n", "select at_date_ist,city,platform, count(distinct device_uuid) as devices_clicked, count( device_uuid) as clicks\n", "from click\n", "group by 1,2,3\n", "\n", ")\n", ",\n", "homepage_atc_fin as\n", "(\n", "select at_date_ist,city,platform, count(distinct device_uuid) as devices_atced, count( device_uuid) as homepage_atc,\n", "sum(mrp) as homepage_gmv\n", "from homepage_atc\n", "group by 1,2,3\n", "\n", ")\n", ",\n", "\n", "\n", "product_list_atc_fin as \n", "(\n", "select at_date_ist,city,platform, count(distinct device_uuid) as plp_devices_atced, count( device_uuid) as plp_atcs,sum(mrp) as plp_gmv\n", "from product_list_atc\n", "group by 1,2,3\n", ")\n", "--,\n", "\n", "--landing_page_atc_fin as \n", "--(\n", "--select at_date_ist,city,platform, count(distinct device_uuid) as lp_devices_atced, count( device_uuid) as lp_atcs, sum(mrp) as lp_gmv\n", "--from landing_page_atc\n", "--group by 1,2,3\n", "--)\n", "\n", "\n", "select i.at_date_ist,i.city,i.platform,devices,impr,devices_clicked,clicks,devices_atced,homepage_atc,plp_devices_atced,plp_atcs\n", ",homepage_gmv,plp_gmv\n", "from impressions_fin as i\n", "left join clicks_fin as c on c.at_date_ist=i.at_date_ist and c.city=i.city and c.platform=i.platform\n", "left join homepage_atc_fin as ha on ha.at_date_ist=i.at_date_ist and ha.city=i.city and ha.platform=i.platform\n", "--left join product_page_atc_fin as pp on pp.at_date_ist=i.at_date_ist and pp.city=i.city and pp.platform=i.platform\n", "left join product_list_atc_fin as pl on pl.at_date_ist=i.at_date_ist and pl.city=i.city and pl.platform=i.platform\n", "--left join landing_page_atc_fin as lp on lp.at_date_ist=i.at_date_ist and lp.city=i.city and lp.platform=i.platform\n", " \n", "\n", "\n", "\"\"\"\n", "t2 = time()\n", "l0_output_indirect = pd.read_sql_query(l0_query, redshift_connection)\n", "t3 = time()\n", "query_elapsed1 = t3 - t2\n", "print(\"Elapsed time to run the query is %f seconds.\" % query_elapsed1)"]}, {"cell_type": "code", "execution_count": null, "id": "967cb69b-9ec0-47fb-9c55-ee81c126c095", "metadata": {}, "outputs": [], "source": ["l0_output_indirect.shape"]}, {"cell_type": "code", "execution_count": null, "id": "96f50b80-98f9-46b7-b526-f35e8676b3d2", "metadata": {}, "outputs": [], "source": ["# l0_output_indirect.columns"]}, {"cell_type": "code", "execution_count": null, "id": "54f8b990-f829-47db-b6f9-eb5637c6ba38", "metadata": {}, "outputs": [], "source": ["# l0_output_indirect.to_csv('file2.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "00a4334c-125c-439d-9cf5-6b9afcc57665", "metadata": {}, "outputs": [], "source": ["# l0_output_indirect['at_date_ist'].value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "ff2f08c0-d144-4dfb-8bae-3467625cf6c7", "metadata": {}, "outputs": [], "source": ["# l1_query = \"\"\"\n", "# with impressions_fin as\n", "# (\n", "# select at_date_ist,city,platform,widget_name,\n", "# count(distinct device_uuid) as devices, count( device_uuid) as impr\n", "# from impressions\n", "# group by 1,2,3,4\n", "\n", "# ),\n", "\n", "# clicks_fin as (\n", "# select at_date_ist,city,platform,widget_name, count(distinct device_uuid) as devices_clicked, count( device_uuid) as clicks\n", "# from click\n", "# group by 1,2,3,4\n", "\n", "# )\n", "# ,\n", "# homepage_atc_fin as\n", "# (\n", "# select at_date_ist,city,platform,widget_name, count(distinct device_uuid) as devices_atced, count( device_uuid) as homepage_atc, sum(mrp) as homepage_gmv\n", "# from homepage_atc\n", "# group by 1,2,3,4\n", "\n", "# ),\n", "\n", "# product_page_atc_fin as\n", "# (\n", "# select at_date_ist,city,platform,widget_name, count(distinct device_uuid) as pdp_devices_atced, count( device_uuid) as pdp_atcs, sum(mrp) as pdp_gmv\n", "# from product_page_atc\n", "# group by 1,2,3,4\n", "# ),\n", "\n", "# product_list_atc_fin as\n", "# (\n", "# select at_date_ist,city,platform,widget_name, count(distinct device_uuid) as plp_devices_atced, count( device_uuid) as plp_atcs, sum(mrp) as plp_gmv\n", "# from product_list_atc\n", "# group by 1,2,3,4\n", "# ),\n", "\n", "# landing_page_atc_fin as\n", "# (\n", "# select at_date_ist,city,platform,widget_name, count(distinct device_uuid) as lp_devices_atced, count( device_uuid) as lp_atcs, sum(mrp) as lp_gmv\n", "# from landing_page_atc\n", "# group by 1,2,3,4\n", "# )\n", "\n", "# select i.at_date_ist,i.city,i.platform,i.widget_name,devices,impr,devices_clicked,clicks,devices_atced,homepage_atc,pdp_devices_atced,pdp_atcs,plp_devices_atced,plp_atcs,\n", "# lp_devices_atced,lp_atcs,\n", "# homepage_gmv,pdp_gmv,plp_gmv,lp_gmv\n", "# from impressions_fin as i\n", "# left join clicks_fin as c on c.at_date_ist=i.at_date_ist and lower(c.widget_name)=lower(i.widget_name) and c.city=i.city and c.platform=i.platform\n", "# left join homepage_atc_fin as ha on ha.at_date_ist=i.at_date_ist and lower(ha.widget_name)=lower(i.widget_name) and ha.city=i.city and ha.platform=i.platform\n", "# left join product_page_atc_fin as pp on pp.at_date_ist=i.at_date_ist and lower(pp.widget_name)=lower(i.widget_name) and pp.city=i.city and pp.platform=i.platform\n", "# left join product_list_atc_fin as pl on pl.at_date_ist=i.at_date_ist and lower(pl.widget_name)=lower(i.widget_name) and pl.city=i.city and pl.platform=i.platform\n", "# left join landing_page_atc_fin as lp on lp.at_date_ist=i.at_date_ist and lower(lp.widget_name)=lower(i.widget_name) and lp.city=i.city and lp.platform=i.platform\n", "\n", "\n", "# \"\"\"\n", "\n", "# l1_output_indirect = pd.read_sql_query(l1_query, redshift_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "fccb6a93-db14-4678-aadf-122d5d40e46b", "metadata": {}, "outputs": [], "source": ["l2_query = \"\"\"\n", "with impressions_fin as\n", "(\n", "select at_date_ist,city,platform,--widget_name,\n", "widget_title,\n", "count(distinct device_uuid) as devices, count( device_uuid) as impr\n", "from impressions\n", "group by 1,2,3,4\n", "\n", "),\n", "\n", "clicks_fin as (\n", "select at_date_ist,city,platform,--widget_name,\n", "widget_title, count(distinct device_uuid) as devices_clicked, count( device_uuid) as clicks\n", "from click\n", "group by 1,2,3,4\n", "\n", ")\n", ",\n", "homepage_atc_fin as\n", "(\n", "select at_date_ist,city,platform,--widget_name,\n", "widget_title, count(distinct device_uuid) as devices_atced, count( device_uuid) as homepage_atc,sum(mrp) as homepage_gmv\n", "from homepage_atc\n", "group by 1,2,3,4\n", "\n", "),\n", "\n", "--product_page_atc_fin as \n", "--(\n", "--select at_date_ist,city,platform,--widget_name,\n", "--widget_title, count(distinct device_uuid) as pdp_devices_atced, count( device_uuid) as pdp_atcs,sum(mrp) as pdp_gmv\n", "--from product_page_atc\n", "--group by 1,2,3,4\n", "--),\n", "\n", "product_list_atc_fin as \n", "(\n", "select at_date_ist,city,platform,--widget_name,\n", "widget_title, count(distinct device_uuid) as plp_devices_atced, count( device_uuid) as plp_atcs,sum(mrp) as plp_gmv\n", "from product_list_atc\n", "group by 1,2,3,4\n", ")\n", "--,\n", "\n", "--landing_page_atc_fin as \n", "--(\n", "--select at_date_ist,city,platform,--widget_name,\n", "--widget_title, count(distinct device_uuid) as lp_devices_atced, count( device_uuid) as lp_atcs,sum(mrp) as lp_gmv\n", "--from landing_page_atc\n", "--group by 1,2,3,4\n", "--)\n", "\n", "select i.at_date_ist,i.city,i.platform,--i.widget_name,\n", "lower(i.widget_title) as widget_title,devices,impr,devices_clicked,clicks,devices_atced,homepage_atc\n", ",plp_devices_atced,plp_atcs,\n", "homepage_gmv,plp_gmv\n", "from impressions_fin as i\n", "left join clicks_fin as c on c.at_date_ist=i.at_date_ist   and lower(c.widget_title)=lower(i.widget_title) --and lower(c.widget_name)=lower(i.widget_name)\n", "and c.city=i.city and c.platform=i.platform\n", "left join homepage_atc_fin as ha on ha.at_date_ist=i.at_date_ist   and lower(ha.widget_title)=lower(i.widget_title)\n", "and ha.city=i.city and ha.platform=i.platform\n", "--left join product_page_atc_fin as pp on pp.at_date_ist=i.at_date_ist and lower(pp.widget_title)=lower(i.widget_title)\n", "--and pp.city=i.city and pp.platform=i.platform\n", "left join product_list_atc_fin as pl on pl.at_date_ist=i.at_date_ist  and lower(pl.widget_title)=lower(i.widget_title)\n", "and pl.city=i.city and pl.platform=i.platform\n", "--left join landing_page_atc_fin as lp on lp.at_date_ist=i.at_date_ist and lower(lp.widget_title)=lower(i.widget_title)\n", "--and lp.city=i.city and lp.platform=i.platform\n", "where devices>50\n", "order by devices desc\n", "\n", "\"\"\"\n", "t2 = time()\n", "l2_output_indirect = pd.read_sql_query(l2_query, redshift_connection)\n", "t3 = time()\n", "query_elapsed1 = t3 - t2\n", "print(\"Elapsed time to run the query is %f seconds.\" % query_elapsed1)"]}, {"cell_type": "code", "execution_count": null, "id": "36b281e3-7690-40b9-b5f3-2b45976e421d", "metadata": {}, "outputs": [], "source": ["l2_output_indirect.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c7492994-a376-4fb0-9a21-bae7b000e123", "metadata": {}, "outputs": [], "source": ["l2_output_indirect.shape"]}, {"cell_type": "code", "execution_count": null, "id": "d1616780-2bf1-4635-af3f-7a78dadb5023", "metadata": {}, "outputs": [], "source": ["# l2_output_indirect['widget_title'].value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "e41894e0-bb6d-4f53-ab34-d962c4705053", "metadata": {}, "outputs": [], "source": ["# l2_output_indirect.to_csv('file2.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "80f6b441-9a90-43a8-9ac8-5156589f53ce", "metadata": {}, "outputs": [], "source": ["# cd"]}, {"cell_type": "code", "execution_count": null, "id": "49b9f0b5-78f2-480e-8fe9-b4488fb887ef", "metadata": {}, "outputs": [], "source": ["l3_query = \"\"\"\n", "with impressions_fin as\n", "(\n", "select at_date_ist,city,platform,--widget_name,\n", "widget_title,child_widget_title,\n", "count(distinct device_uuid) as devices, count( device_uuid) as impr\n", "from impressions\n", "group by 1,2,3,4,5\n", "\n", "),\n", "\n", "clicks_fin as (\n", "select at_date_ist,city,platform,--widget_name,\n", "widget_title,child_widget_title, count(distinct device_uuid) as devices_clicked, count( device_uuid) as clicks\n", "from click\n", "group by 1,2,3,4,5\n", "\n", ")\n", ",\n", "homepage_atc_fin as\n", "(\n", "select at_date_ist,city,platform,--widget_name,\n", "widget_title,child_widget_title, count(distinct device_uuid) as devices_atced, count( device_uuid) as homepage_atc,sum(mrp) as homepage_gmv\n", "from homepage_atc\n", "group by 1,2,3,4,5\n", "\n", "),\n", "\n", "--product_page_atc_fin as \n", "--(\n", "--select at_date_ist,city,platform,--widget_name,\n", "--widget_title,child_widget_title, count(distinct device_uuid) as pdp_devices_atced, count( device_uuid) as pdp_atcs,sum(mrp) as pdp_gmv\n", "--from product_page_atc\n", "--group by 1,2,3,4,5\n", "--),\n", "\n", "product_list_atc_fin as \n", "(\n", "select at_date_ist,city,platform,--widget_name,\n", "widget_title,child_widget_title, count(distinct device_uuid) as plp_devices_atced, count( device_uuid) as plp_atcs,sum(mrp) as plp_gmv\n", "from product_list_atc\n", "group by 1,2,3,4,5\n", ")\n", "--,\n", "--\n", "--landing_page_atc_fin as \n", "--(\n", "--select at_date_ist,city,platform,--widget_name,\n", "--widget_title,child_widget_title, count(distinct device_uuid) as lp_devices_atced, count( device_uuid) as lp_atcs,sum(mrp) as lp_gmv\n", "--from landing_page_atc\n", "--group by 1,2,3,4,5\n", "--)\n", "\n", "select i.at_date_ist,i.city,i.platform,--i.widget_name,\n", "lower(i.widget_title) as widget_title,lower(i.child_widget_title) as child_widget_title,devices,impr,devices_clicked,clicks,devices_atced,homepage_atc\n", ",plp_devices_atced,plp_atcs,\n", "\n", "homepage_gmv,plp_gmv\n", "from impressions_fin as i\n", "left join clicks_fin as c on c.at_date_ist=i.at_date_ist   and lower(c.widget_title)=lower(i.widget_title) --and lower(c.widget_name)=lower(i.widget_name)\n", "and lower(c.child_widget_title)=lower(i.child_widget_title) and c.city=i.city and c.platform=i.platform\n", "left join homepage_atc_fin as ha on ha.at_date_ist=i.at_date_ist  and lower(ha.widget_title)=lower(i.widget_title)\n", "and lower(ha.child_widget_title)=lower(i.child_widget_title) and ha.city=i.city and ha.platform=i.platform\n", "--left join product_page_atc_fin as pp on pp.at_date_ist=i.at_date_ist  and lower(pp.widget_title)=lower(i.widget_title)\n", "--and lower(pp.child_widget_title)=lower(i.child_widget_title) and pp.city=i.city and pp.platform=i.platform\n", "left join product_list_atc_fin as pl on pl.at_date_ist=i.at_date_ist  and lower(pl.widget_title)=lower(i.widget_title)\n", "and lower(pl.child_widget_title)=lower(i.child_widget_title) and pl.city=i.city and pl.platform=i.platform\n", "--left join landing_page_atc_fin as lp on lp.at_date_ist=i.at_date_ist  and lower(lp.widget_title)=lower(i.widget_title)\n", "--and lower(lp.child_widget_title)=lower(i.child_widget_title) and lp.city=i.city and lp.platform=i.platform\n", "where devices>10 and clicks>0\n", "order by devices desc\n", "\n", "\"\"\"\n", "t2 = time()\n", "l3_output_indirect = pd.read_sql_query(l3_query, redshift_connection)\n", "t3 = time()\n", "query_elapsed1 = t3 - t2\n", "print(\"Elapsed time to run the query is %f seconds.\" % query_elapsed1)"]}, {"cell_type": "code", "execution_count": null, "id": "f7febb27-71bf-4081-8315-bb90187b66a2", "metadata": {}, "outputs": [], "source": ["# l3_output_indirect.shape"]}, {"cell_type": "code", "execution_count": null, "id": "1eca9c40-3679-41b3-afe5-34ef1bc29a2f", "metadata": {}, "outputs": [], "source": ["# l3_output_indirect.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d3468ca4-3d58-44b2-bf63-d8a4ee4ef534", "metadata": {}, "outputs": [], "source": ["!pip install pandasql\n", "import pandasql as ps"]}, {"cell_type": "code", "execution_count": null, "id": "5e9d68c2-8e00-4838-9031-01860861a41e", "metadata": {}, "outputs": [], "source": ["l3_output_indirect.columns"]}, {"cell_type": "code", "execution_count": null, "id": "a576cc82-63db-4efd-9ea3-447661c626c7", "metadata": {}, "outputs": [], "source": ["df_merge = ps.sqldf(\n", "    \"\"\"\n", "    select \n", "        at_date_ist,city,platform,'Homepage' as level,'-' as  widget_name,'-' as widget_title,\n", "        '-' as child_widget_title,devices,impr as impressions,\n", "        devices_clicked,clicks,devices_atced,homepage_atc,plp_devices_atced,plp_atcs,\n", "        homepage_gmv,plp_gmv\n", "    from\n", "        l0_output_indirect i0\n", "        \n", "    --union all\n", "    \n", "        --select \n", "        --at_date_ist,city,platform,'widget_name' as level,  widget_name,'-' as widget_title,\n", "        --'-' as child_widget_title,devices,impr as impressions,\n", "        --devices_clicked,clicks,devices_atced,homepage_atc,pdp_devices_atced,\n", "        --pdp_atcs,plp_devices_atced,plp_atcs,\n", "        --homepage_gmv,pdp_gmv,plp_gmv\n", "    --from\n", "        --l1_output_indirect i1\n", "        \n", "    union all\n", "    \n", "        select \n", "        at_date_ist,city,platform,'widget_title' as level, '-' as  widget_name, widget_title,\n", "        '-' as child_widget_title,devices,impr as impressions,\n", "        devices_clicked,clicks,devices_atced,homepage_atc\n", "        ,plp_devices_atced,plp_atcs,\n", "        homepage_gmv,plp_gmv\n", "    from\n", "        l2_output_indirect i2\n", "        \n", "    union all\n", "    \n", "        select \n", "        at_date_ist,city,platform,'child_widget_title' as level, '-' as  widget_name, widget_title,\n", "         child_widget_title,devices,impr as impressions,\n", "        devices_clicked,clicks,devices_atced,homepage_atc,\n", "        plp_devices_atced,plp_atcs,\n", "        homepage_gmv,plp_gmv\n", "    from\n", "        l3_output_indirect i3\n", "        \n", " \n", "    \n", "    \n", "   \n", "\n", "\n", "\"\"\",\n", "    locals(),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e91dcc04-fd47-4b64-85de-b045e1b6b731", "metadata": {}, "outputs": [], "source": ["df_merge.shape"]}, {"cell_type": "code", "execution_count": null, "id": "1e701174-6fee-4b2a-9bc7-014c430d187b", "metadata": {}, "outputs": [], "source": ["df_merge.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f4bd6b4f-8445-4062-9521-cfba8c2c8cdf", "metadata": {}, "outputs": [], "source": ["# df_merge.to_csv('file4.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "4cdb897a-d697-4319-95c6-d0f3ffaf6a3f", "metadata": {}, "outputs": [], "source": ["# df_merge.columns"]}, {"cell_type": "code", "execution_count": null, "id": "712bf3d6-848e-4298-a46d-c769ed06a268", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "dd5da221-3af4-418f-b06c-759253f4baef", "metadata": {}, "outputs": [], "source": ["t6 = time()\n", "kwargs = {\n", "    \"schema_name\": \"consumer\",\n", "    \"table_name\": \"homepage_performance\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"at_date_ist\", \"type\": \"date\", \"description\": \"report date\"},\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"city\"},\n", "        {\"name\": \"platform\", \"type\": \"varchar\", \"description\": \"platform\"},\n", "        {\"name\": \"level\", \"type\": \"varchar\", \"description\": \"Reporting level\"},\n", "        {\"name\": \"widget_name\", \"type\": \"varchar\", \"description\": \"widget name\"},\n", "        {\"name\": \"widget_title\", \"type\": \"varchar\", \"description\": \"widget title\"},\n", "        {\n", "            \"name\": \"child_widget_title\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"child widget title\",\n", "        },\n", "        {\n", "            \"name\": \"devices\",\n", "            \"type\": \"float\",\n", "            \"description\": \"unique devices on homepage\",\n", "        },\n", "        {\"name\": \"impressions\", \"type\": \"float\", \"description\": \"homepage impressions\"},\n", "        {\n", "            \"name\": \"devices_clicked\",\n", "            \"type\": \"float\",\n", "            \"description\": \"devices who clicked on homepage\",\n", "        },\n", "        {\"name\": \"clicks\", \"type\": \"float\", \"description\": \"total clicks on homepage\"},\n", "        {\n", "            \"name\": \"devices_atced\",\n", "            \"type\": \"float\",\n", "            \"description\": \"Number of devices who added product from homepage\",\n", "        },\n", "        {\n", "            \"name\": \"homepage_atc\",\n", "            \"type\": \"float\",\n", "            \"description\": \"Total atc happening on homepage\",\n", "        },\n", "        # {\"name\": \"pdp_devices_atced\", \"type\": \"float\", \"description\": \"Number of users who added any product from PDP page associated with homepage\"},\n", "        # {\"name\": \"pdp_atcs\", \"type\": \"float\", \"description\": \"Total atc happening on  PDP page associated with homepage\"},\n", "        {\n", "            \"name\": \"plp_devices_atced\",\n", "            \"type\": \"float\",\n", "            \"description\": \"Number of users who added any product from PlP page associated with homepage\",\n", "        },\n", "        {\n", "            \"name\": \"plp_atcs\",\n", "            \"type\": \"float\",\n", "            \"description\": \"Total atc happening on  PlP page associated with homepage\",\n", "        },\n", "        # {\"name\": \"lp_devices_atced\", \"type\": \"float\", \"description\": \"Number of users who added any product from landing page associated with homepage\"},\n", "        # {\"name\": \"lp_atcs\", \"type\": \"float\", \"description\": \"Total atc happening on  landing page associated with homepage\"},\n", "        {\n", "            \"name\": \"homepage_gmv\",\n", "            \"type\": \"float\",\n", "            \"description\": \"GMV coming from homepage atc\",\n", "        },\n", "        # {\"name\": \"pdp_gmv\", \"type\": \"float\", \"description\": \"GMV coming from atc on PDP page associated with homepage\"},\n", "        {\n", "            \"name\": \"plp_gmv\",\n", "            \"type\": \"float\",\n", "            \"description\": \"GMV coming from atc on PlP page associated with homepage\",\n", "        },\n", "        # {\"name\": \"lp_gmv\", \"type\": \"float\", \"description\": \"GMV coming from atc on landing page associated with homepage\"},\n", "    ],\n", "    \"primary_key\": [\"at_date_ist\", \"level\"],\n", "    \"sortkey\": [\"at_date_ist\", \"level\"],\n", "    \"incremental_key\": \"at_date_ist\",\n", "    \"load_type\": \"truncate\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This table stores the daily assets performance tracking with direct atcs and indirect atcs\",\n", "}\n", "\n", "pb.to_redshift(df_merge, **kwargs)\n", "t7 = time()\n", "elapsed = t7 - t6\n", "print(\"Table Update Time is %f seconds.\" % elapsed)\n", "\n", "elapsed = t7 - t0\n", "print(\"Total Table Update Time is %f seconds.\" % elapsed)"]}, {"cell_type": "code", "execution_count": null, "id": "f36ae383-4516-4d40-b0f3-29bc91c69718", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}