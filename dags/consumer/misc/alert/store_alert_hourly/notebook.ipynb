{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install pandasql"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "from datetime import date, timedelta, datetime\n", "import pytz"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Redshift\")\n", "current_date = datetime.now(pytz.timezone(\"Asia/Kolkata\")).strftime(\"%Y-%m-%d\")\n", "current_timestamp = datetime.now(pytz.timezone(\"Asia/Kolkata\")).strftime(\n", "    \"%Y-%m-%d %H:%M:%S\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["my_query = \"\"\"\n", "with base as (\n", "select \n", "extract(hour from cart_checkout_ts_ist) as hour , merchant_name as store ,\n", "count(distinct case when date(cart_checkout_ts_ist) = current_date then order_id end) as t ,\n", "count(distinct case when date(cart_checkout_ts_ist) = current_date-1 then order_id end) as \"t-1\" ,\n", "count(distinct case when date(cart_checkout_ts_ist) = current_date-7 then order_id end) as \"t-7\" ,\n", "count(distinct case when date(cart_checkout_ts_ist) = current_date-14 then order_id end) as \"t-14\" ,\n", "count(distinct case when date(cart_checkout_ts_ist) = current_date-28 then order_id end) as \"t-28\" \n", "from dwh.fact_sales_order_details d \n", "join dwh.dim_merchant m on d.frontend_merchant_id = m.merchant_id\n", "where date(cart_checkout_ts_ist) in (current_date ,current_date-7 , current_date-28 ,current_date-1 ,current_date-14)\n", "and extract(hour from cart_checkout_ts_ist) = (select max(extract(hour from cart_checkout_ts_ist)) \n", "from dwh.fact_sales_order_details where cart_checkout_ts_ist >= current_date)         \n", "and m.is_current\n", "and order_current_status <> 'CANCELLED'\n", "group by 1,2\n", ") \n", "\n", "select b.*   --, c.t_cumulative , c.\"t-1_cumulative\" ,c.\"t-7_cumulative\" ,c.\"t-14_cumulative\" ,c.\"t-28_cumulative\"\n", "from base b --join cumulative c on b.store = c.store\n", "\n", "\n", "\"\"\".format()\n", "\n", "df = pd.read_sql_query(my_query, con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df2 = pd.DataFrame(columns=df.columns)\n", "for i in range(df.shape[0]):\n", "    a = df.loc[i, \"t\"]\n", "    b = df.loc[i, \"t-7\"]\n", "    if b > 0 and 100.0 * (a - b) / b <= -25:\n", "        df2 = df2.append(df.loc[i, :], ignore_index=True)\n", "\n", "mes = df2.to_csv(index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(channel=\"store-alert\", text=mes)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}