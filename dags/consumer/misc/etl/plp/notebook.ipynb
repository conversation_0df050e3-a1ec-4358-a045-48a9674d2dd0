{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def plp_analysis(query, query1, query2, query3, sd, ed):\n", "\n", "    import pandas as pd\n", "    import pencilbox as pb\n", "    import datetime\n", "    import numpy as np\n", "\n", "    # Taking out Impression Data\n", "    def base_file(query, start_date, end_date):\n", "\n", "        con = pb.get_connection(\"redshift\")\n", "        df1 = pd.read_sql(sql=query, con=con)\n", "\n", "        # Calculating Total Impression of L2\n", "        df1[\"cityid\"] = df1[\"city\"] + df1[\"l2_cat\"]\n", "        z = df1.groupby(\"cityid\")[\"imp\"].sum()\n", "        df1 = pd.merge(df1, z, how=\"left\", on=\"cityid\")\n", "        df1 = df1.rename(columns={\"imp_x\": \"imp\", \"imp_y\": \"L2_TotImpr\"})\n", "        df1 = df1[df1.imp > 0]\n", "\n", "        # Calculating Assetwise Impression, imp share, RGM/Impression, ATC/Impression\n", "        df1[\"id2\"] = df1[\"cityid\"] + df1[\"page_name\"]\n", "        s = df1.groupby(\"id2\")[\"imp\"].sum()\n", "        df1 = pd.merge(df1, s, how=\"left\", on=\"id2\")\n", "        df1 = df1.rename(columns={\"imp_x\": \"imp\", \"imp_y\": \"Asset_TotImpr\"})\n", "        df1[\"impwithinasset\"] = df1[\"imp\"] / df1[\"Asset_TotImpr\"]\n", "        df1[\"rgmperimpr\"] = df1[\"rm\"] / df1[\"imp\"]\n", "        df1[\"rgmperimpr\"] = df1[\"rgmperimpr\"].fillna(0)\n", "        df1[\"atcperimpr\"] = df1[\"total_atc\"] / df1[\"imp\"]\n", "        df1[\"atcperimpr\"] = df1[\"atcperimpr\"].fillna(0)\n", "\n", "        # Taking Top 90% Impression PIDs\n", "        df1 = df1[df1.total_atc > 0]\n", "        df1 = df1.sort_values([\"id2\", \"impwithinasset\"], ascending=False)\n", "        df1[\"cum\"] = df1.groupby(\"id2\")[\"impwithinasset\"].cumsum()\n", "        df1 = df1[df1.cum <= 0.9]\n", "\n", "        # Adding RM%\n", "        df1[\"rm%\"] = np.where(df1[\"gmv\"] == 0, 0, (df1[\"rm\"] / df1[\"gmv\"]))\n", "\n", "        # Storing the L2s name\n", "        # L2=df1.l2_cat.unique()\n", "\n", "        return df1\n", "\n", "    # taking out the PLP scores\n", "    def plp_scores(query1, query2, query3, sd, ed):\n", "\n", "        # getting data from sql\n", "        con1 = pb.get_connection(\"redshift\")\n", "        con2 = pb.get_connection(\"mother_db\")\n", "        mapping1 = pd.read_sql(\n", "            sql=query1, con=con1\n", "        )  # city merchant mapping from order data table\n", "        mapping2 = pd.read_sql(sql=query2, con=con1)  # warehouse mapping query\n", "        scores = pd.read_sql(sql=query3, con=con2)  # current plp scores\n", "\n", "        # merging two data sets for mapping and removing duplicates\n", "        mapping = (\n", "            pd.concat([mapping1, mapping1]).drop_duplicates().reset_index(drop=True)\n", "        )\n", "\n", "        # getting the city level data\n", "        scores = pd.merge(\n", "            scores,\n", "            mapping[[\"merchant_id\", \"frontend_city\"]],\n", "            how=\"left\",\n", "            on=\"merchant_id\",\n", "        )\n", "        scores = scores.dropna()\n", "        scores = scores.iloc[:, [0, 1, 5, 6, 7, 8]]\n", "        scores = scores.drop_duplicates().reset_index()\n", "\n", "        # getting the input rank\n", "        scores[\"input_rank\"] = scores.groupby([\"frontend_city\", \"l2_cat\"])[\n", "            \"sorting_score\"\n", "        ].rank(\"dense\", ascending=False)\n", "        scores = scores.sort_values(by=[\"frontend_city\", \"l2_cat\"])\n", "\n", "        return scores\n", "\n", "    # Combining data\n", "    scores = plp_scores(query1, query2, query3, sd, ed)\n", "    df1 = base_file(query, sd, ed)\n", "    scores[\"product_id\"] = scores[\"product_id\"].apply(str)\n", "    df1 = pd.merge(\n", "        df1,\n", "        scores[[\"sorting_score\", \"input_rank\", \"frontend_city\", \"product_id\"]],\n", "        how=\"left\",\n", "        left_on=[\"city\", \"pid\"],\n", "        right_on=[\"frontend_city\", \"product_id\"],\n", "    )\n", "    df1[\"index\"] = df1.index\n", "    df1 = df1[\n", "        [\n", "            \"index\",\n", "            \"city\",\n", "            \"pid\",\n", "            \"name\",\n", "            \"l_cat\",\n", "            \"l1_cat\",\n", "            \"l2_cat\",\n", "            \"imp\",\n", "            \"total_atc\",\n", "            \"gmv\",\n", "            \"rm\",\n", "            \"Asset_TotImpr\",\n", "            \"impwithinasset\",\n", "            \"rgmperimpr\",\n", "            \"atcperimpr\",\n", "            \"rm%\",\n", "            \"sorting_score\",\n", "            \"input_rank\",\n", "        ]\n", "    ]\n", "    df1[\"input_rank\"] = df1[\"input_rank\"].fillna(0)\n", "    df1[\"sorting_score\"] = df1[\"sorting_score\"].fillna(0)\n", "    df1[\"rm%\"] = df1[\"rm%\"].fillna(0)\n", "    df1[\"rm\"] = df1[\"rm\"].fillna(0)\n", "    df1[\"updated_on\"] = pd.to_datetime(\"today\")\n", "    df1[\"updated_on\"] = df1[\"updated_on\"].apply(str)\n", "\n", "    return df1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "\n", "today = datetime.datetime.today()\n", "sd = today.strftime(\"%Y-%m-01\")\n", "ed = today.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"with PLP_Imp as\n", "(\n", "   select\n", "        traits__city_name city,\n", "       properties__page_name,\n", "       -- properties__child_widget_id,\n", "       -- properties__widget_name, \n", "       properties__widget_id PID,\n", "       count(distinct session_uuid) Imp\n", "   from spectrum.mobile_impression_data mid\n", "   WHERE at_date_ist BETWEEN '{start_date}' AND '{end_date}'    \n", "   and name = 'Product Shown'\n", "   and properties__page_name in ('Product List')\n", "   and properties__child_widget_id is null\n", "   and traits__city_name is not null\n", "   and properties__display_tab_name not in ('Trending','Grofers Brands')\n", "   group by 1,2,3\n", "),\n", "\n", "\n", "-- RM calc\n", "oid as (\n", "    select date(install_ts + interval '5.5 hours') dt, \n", "        oi.city,\n", "        product_id PID,\n", "         (case when a.offer_type ilike '%%flat%%'\n", "          then COALESCE((a.bf_margin * (oi.price_mrp - a.sp)),0.0)\n", "          else COALESCE((a.bf_margin * oi.price_mrp), 0.0)\n", "        end) as bf,\n", "        COALESCE(sum(landing_price*quantity)/nullif(sum(quantity),0),0) as lp,\n", "        COALESCE(sum(price*quantity)/nullif(sum(quantity),0),0) as sp\n", "        from order_item_data oi\n", "        left join all_margins a \n", "        on lower(oi.city) = lower(case when a.city ilike 'bangalore' then 'bengaluru' else a.city end)\n", "        and oi.product_id = a.pid\n", "        and date(convert_timezone('Asia/Kolkata',oi.install_ts)) between date(a.start_date) and date(a.end_date)\n", "        where \n", "        date(install_ts + interval '5.5 hours') between '{start_date}' AND '{end_date}' \n", "        group by 1,2,3,4\n", "    ),\n", "\n", "oid1 as \n", "    (select distinct city, PID, avg(sp-lp+bf) as rm from oid group by 1,2),\n", "---\n", "\n", "ATC_All as\n", "(\n", "  select\n", "    a.traits__city_name city,\n", "    case when a.properties__page_name = 'Product List' then 'Product List' \n", "        when a.properties__page_name = 'Search List' then 'Search List'\n", "        else 'Others' end as properties__page_name,\n", "    a.properties__product_id PID,\n", "    a.session_uuid,\n", "    a.properties__price price,\n", "    count(*) as ATC,\n", "    b.rm\n", "  from spectrum.mobile_event_data a \n", "  left join oid1 b \n", "  on a.traits__city_name = b.city and a.properties__product_id = b.PID\n", "  WHERE a.at_date_ist BETWEEN '{start_date}' AND '{end_date}'    \n", "  and a.name = 'Product Added'\n", "  and a.properties__page_name= 'Product List'\n", "  and a.properties__display_tab_name not in ('Trending','<PERSON>ro<PERSON> Brands')\n", "  and traits__city_name is not null\n", "  group by 1,2,3,4,5,7\n", "),\n", "\n", "ATC_All2 as\n", "(\n", "select\n", "    city,\n", "    properties__page_name,\n", "    PID,\n", "    sum(ATC) as total_ATC,\n", "    sum(ATC*price) as Gmv,\n", "    sum(ATC*rm) as RM\n", "from ATC_All\n", "group by 1,2,3\n", "),\n", "\n", "Join1 as\n", "(\n", "    select \n", "        a.city, \n", "        a.properties__page_name Page_Name, \n", "        a.<PERSON>, \n", "        a.<PERSON>, \n", "        b.total_ATC,\n", "        b.Gmv,\n", "        b.R<PERSON>\n", "    from PLP_Imp a left join ATC_All2 b\n", "    on a.properties__page_name = b.properties__page_name\n", "    and a.PID = b.PID\n", "    and a.city = b.city\n", "),\n", "\n", "cat as (\n", "    SELECT distinct \n", "        P.ID AS PID, \n", "        CAT.NAME AS l_cat, \n", "        CAT1.NAME AS l1_cat, \n", "        CAT2.NAME AS l2_cat,\n", "        <PERSON>.<PERSON>, \n", "        P.UNIT, \n", "        <PERSON><PERSON>,\n", "        gp.\"type\" as ptype,\n", "        gp.type_id as type_id\n", "    FROM \n", "    GR_PRODUCT P\n", "    INNER JOIN GR_PRODUCT_CATEGORY_MAPPING PCM ON P.ID = PCM.PRODUCT_ID\n", "    INNER JOIN GR_CATEGORY CAT2 ON PCM.CATEGORY_ID = CAT2.ID AND PCM.IS_PRIMARY=TRUE\n", "    INNER JOIN GR_CATEGORY CAT1 ON CAT2.PARENT_CATEGORY_ID = CAT1.ID\n", "    INNER JOIN GR_CATEGORY CAT ON  CAT1.PARENT_CATEGORY_ID = CAT.ID\n", "    Left JOIN gr_product gp on gp.id=p.id\n", "    ),\n", "\n", "Join2 as \n", "(\n", "    select\n", "        a.city,\n", "        a.<PERSON>_<PERSON>, \n", "        a.<PERSON>,\n", "        b.<PERSON>,\n", "        b.l_cat, \n", "        b.l1_cat, \n", "        b.l2_cat,\n", "        a.<PERSON>, \n", "        a.total_ATC,\n", "        a.Gmv,\n", "        a.RM\n", "    from Join1 a left join Cat b\n", "    on a.PID = b.PID\n", ")\n", "\n", "select * from Join2\"\"\".format(\n", "    start_date=sd, end_date=ed\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query1 = \"\"\"select distinct merchant_id, merchant_name, frontend_city from consumer.order_data\n", "where  date(install_ts + interval '5.5 hours') between '{start_date}' AND '{end_date}'\n", "and merchant_id is not null\"\"\".format(\n", "    start_date=sd, end_date=ed\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query2 = \"\"\"with MERCHANT AS\n", "  (SELECT distinct \n", "          M.id,\n", "          m.name,\n", "          m.enabled_flag,\n", "          L.NAME AS city\n", "   FROM consumer.GR_MERCHANT M\n", "   INNER JOIN consumer.GR_MERCHANT_ADDITIONAL_INFO MAI ON MAI.MERCHANT_ID = M.ID\n", "   AND (MAI.MERCHANT_STORE_TYPE = 'grofers' )\n", "   INNER JOIN consumer.GR_LOCALITY L2 ON M.LOCALITY_ID = L2.ID\n", "   AND (M.NAME NOT ILIKE '%%GROCERY%%MART%%'\n", "        AND M.NAME NOT ILIKE '%%FRESHBURY%%' AND M.NAME NOT ILIKE '%%test%%' AND M.NAME NOT ILIKE '%%donation%%')\n", "   INNER JOIN consumer.GR_LOCALITY L1 ON L2.PARENT_LOCALITY_ID = L1.ID\n", "   INNER JOIN consumer.GR_LOCALITY L ON L1.PARENT_LOCALITY_ID = L.ID\n", "   AND L.ID <> 2051\n", "   )\n", "select distinct\n", "vmp.virtual_merchant_id as merchant_id,\n", "m1.name as merchant_name,\n", "m1.city as frontend_city\n", "\n", "\n", "\n", "\n", "from\n", "gr_virtual_to_real_merchant_mapping vmp\n", "inner join merchant m1 on m1.id=vmp.virtual_merchant_id\n", "and m1.enabled_flag=true\n", "where vmp.enabled_flag=true\n", "order by m1.city\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query3 = \"\"\"with t1 as (\n", "SELECT p.name product_name,\n", "       product_id,\n", "       merchant_id,\n", "       m.name merchant_name,\n", "       mpm.inventory_limit,\n", "       sorting_score,\n", "       mpm.sorting_score_expiry_ts as expiry\n", "FROM gr_merchant_product_mapping mpm\n", "JOIN gr_product p ON p.id=mpm.product_id\n", "JOIN gr_merchant m ON m.id=mpm.merchant_id\n", "where mpm.sorting_score_expiry_ts >= '{Date}'\n", "\n", "), \n", "\n", "\n", "cat as (\n", "    SELECT distinct \n", "        P.ID AS PID, \n", "        CAT.NAME AS l_cat, \n", "        CAT1.NAME AS l1_cat, \n", "        CAT2.NAME AS l2_cat,\n", "        <PERSON>.<PERSON>, \n", "        P.UNIT, \n", "        <PERSON><PERSON>,\n", "        gp.type as ptype,\n", "        gp.type_id as type_id\n", "    FROM \n", "    GR_PRODUCT P\n", "    INNER JOIN GR_PRODUCT_CATEGORY_MAPPING PCM ON P.ID = PCM.PRODUCT_ID\n", "    INNER JOIN GR_CATEGORY CAT2 ON PCM.CATEGORY_ID = CAT2.ID AND PCM.IS_PRIMARY=TRUE\n", "    INNER JOIN GR_CATEGORY CAT1 ON CAT2.PARENT_CATEGORY_ID = CAT1.ID\n", "    INNER JOIN GR_CATEGORY CAT ON  CAT1.PARENT_CATEGORY_ID = CAT.ID\n", "    Left JOIN gr_product gp on gp.id=p.id\n", "    ),\n", "\n", "join1 as (\n", "    SELECT distinct\n", "            t1.product_name,\n", "            t1.product_id,\n", "            t1.merchant_id,\n", "            t1.merchant_name,\n", "            t1.inventory_limit,\n", "            t1.sorting_score,\n", "            t1.expiry, \n", "            cat.l2_cat \n", "        from t1 left join cat on t1.product_id = cat.PID\n", ")\n", "\n", "select * from join1 \n", "ORDER BY sorting_score DESC\"\"\".format(\n", "    Date=ed\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = plp_analysis(query, query1, query2, query3, sd, ed)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"index\", \"type\": \"int8\"},\n", "    {\"name\": \"city\", \"type\": \"varchar(max)\"},\n", "    {\"name\": \"pid\", \"type\": \"varchar(max)\"},\n", "    {\"name\": \"name\", \"type\": \"varchar(max)\"},\n", "    {\"name\": \"l_cat\", \"type\": \"varchar(max)\"},\n", "    {\"name\": \"l1_cat\", \"type\": \"varchar(max)\"},\n", "    {\"name\": \"l2_cat\", \"type\": \"varchar(max)\"},\n", "    {\"name\": \"imp\", \"type\": \"int8\"},\n", "    {\"name\": \"total_atc\", \"type\": \"decimal(19,6)\"},\n", "    {\"name\": \"gmv\", \"type\": \"decimal(19,6)\"},\n", "    {\"name\": \"rm\", \"type\": \"decimal(19,6)\"},\n", "    {\"name\": \"Asset_TotImpr\", \"type\": \"decimal(19,6)\"},\n", "    {\"name\": \"impwithinasset\", \"type\": \"decimal(19,6)\"},\n", "    {\"name\": \"rgmperimpr\", \"type\": \"decimal(19,6)\"},\n", "    {\"name\": \"atcperimpr\", \"type\": \"decimal(19,6)\"},\n", "    {\"name\": \"rm%\", \"type\": \"decimal(19,6)\"},\n", "    {\"name\": \"sorting_score\", \"type\": \"decimal(19,6)\"},\n", "    {\"name\": \"input_rank\", \"type\": \"decimal(19,6)\"},\n", "    {\"name\": \"updated_on\", \"type\": \"varchar(max)\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "\n", "kwargs = {\n", "    \"schema_name\": \"consumer\",  # Redshift schema name\n", "    \"table_name\": \"plp_performance\",  # Redshift table name\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"index\"],  # list\n", "    \"sortkey\": [\"index\"],  # list\n", "    # \"incremental_key\": , # string\n", "    \"load_type\": \"truncate\",  # append, rebuild, truncate or upsert\n", "}\n", "# pb.to_redshift(df, **kwargs)\n", "# or\n", "pb.to_redshift(final, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["runtime = datetime.datetime.today()\n", "print(runtime)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}