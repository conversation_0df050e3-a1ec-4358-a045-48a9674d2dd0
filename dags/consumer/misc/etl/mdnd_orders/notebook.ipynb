{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Importing Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import datetime as dt\n", "from datetime import datetime"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Setting Database Connection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_connection = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Initializing Variables"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_schema_name = \"consumer\"\n", "redshift_table_name = \"mdnd_orders\"\n", "send_exception_message_to_this_slack_user_id = \"U03S2CRJG2Z\"\n", "sheet_id = \"1TrmtVVrYRsPPHzVHOXnrn2S8BO0BB-HXGoLTWnzqbhA\"\n", "yday_date = dt.date.today() - dt.<PERSON><PERSON><PERSON>(days=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Querying max date already present in the table (just a validation check)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["max_date_query = \"\"\"\n", "    select\n", "        max(complaint_date_ist) as max_date_in_table\n", "    from\n", "        consumer.mdnd_orders\n", "\"\"\"\n", "\n", "max_date_in_table = pd.read_sql_query(max_date_query, redshift_connection)\n", "\n", "start_date = max_date_in_table.iloc[0, 0] + dt.<PERSON><PERSON><PERSON>(days=1)\n", "print(start_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date = start_date\n", "\n", "if start_date > yday_date:\n", "    info_message = (\n", "        \"Data for yday (\"\n", "        + str(yday_date)\n", "        + \") already present in `\"\n", "        + redshift_schema_name\n", "        + \".\"\n", "        + redshift_table_name\n", "        + \"` Please check.\"\n", "    )\n", "    pb.send_slack_message(\n", "        channel=send_exception_message_to_this_slack_user_id, text=info_message\n", "    )\n", "\n", "while date <= yday_date:\n", "    if date.day == 1 or date.day == 21 or date.day == 31:\n", "        tab_name = date.strftime(\"%-dst %b'%y\")\n", "    elif date.day == 2 or date.day == 22:\n", "        tab_name = date.strftime(\"%-dnd %b'%y\")\n", "    elif date.day == 3 or date.day == 23:\n", "        tab_name = date.strftime(\"%-drd %b'%y\")\n", "    elif date.day >= 4:\n", "        tab_name = date.strftime(\"%-dth %b'%y\")\n", "\n", "    try:\n", "        daily_mdnd_orders = pb.from_sheets(sheet_id, tab_name)\n", "        daily_mdnd_orders[\"complaint_date_ist\"] = date\n", "        daily_mdnd_orders = daily_mdnd_orders.rename(\n", "            columns={\n", "                list(daily_mdnd_orders)[0]: \"order_id\",\n", "                list(daily_mdnd_orders)[1]: \"channel\",\n", "            }\n", "        )\n", "\n", "        daily_mdnd_orders = daily_mdnd_orders[\n", "            [\"complaint_date_ist\", \"order_id\", \"channel\"]\n", "        ]\n", "        daily_mdnd_orders[\"order_id\"] = pd.to_numeric(daily_mdnd_orders[\"order_id\"])\n", "        daily_mdnd_orders[\"complaint_date_ist\"] = pd.to_datetime(\n", "            daily_mdnd_orders[\"complaint_date_ist\"]\n", "        )\n", "        daily_mdnd_orders[\"channel\"] = daily_mdnd_orders[\"channel\"].str.lower()\n", "\n", "        # Append data in the table\n", "        kwargs = {\n", "            \"schema_name\": redshift_schema_name,  # Redshift schema name\n", "            \"table_name\": redshift_table_name,  # Redshift table name\n", "            \"column_dtypes\": [\n", "                {\n", "                    \"name\": \"complaint_date_ist\",\n", "                    \"type\": \"date\",\n", "                    \"description\": \"Date (IST) on which the MDND complaint was registered\",\n", "                },\n", "                {\n", "                    \"name\": \"order_id\",\n", "                    \"type\": \"bigint\",\n", "                    \"description\": \"Order ID for which the MDND complaint was registered\",\n", "                },\n", "                {\n", "                    \"name\": \"channel\",\n", "                    \"type\": \"varchar\",\n", "                    \"description\": \"Channel/Source through which the MDND complaint was registered\",\n", "                },\n", "            ],\n", "            \"primary_key\": [\"complaint_date_ist\", \"order_id\", \"channel\"],  # list\n", "            \"sortkey\": [\"complaint_date_ist\"],  # list\n", "            \"incremental_key\": \"\",  # string\n", "            \"load_type\": \"append\",  # append, rebuild, truncate or upsert\n", "            \"table_description\": \"This table tracks the orders with MDND complaints. Since this is manually filled data, it may have duplicate rows.\",\n", "        }\n", "\n", "        pb.to_redshift(daily_mdnd_orders, **kwargs)\n", "\n", "    except Exception as e:\n", "        exception_text = (\n", "            \"Exception occurred while extracting data for \"\n", "            + str(date)\n", "            + \": `\"\n", "            + str(e)\n", "            + \"` Please check.\"\n", "        )\n", "        pb.send_slack_message(\n", "            channel=send_exception_message_to_this_slack_user_id, text=exception_text\n", "        )\n", "\n", "    date = date + dt.<PERSON><PERSON><PERSON>(days=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}