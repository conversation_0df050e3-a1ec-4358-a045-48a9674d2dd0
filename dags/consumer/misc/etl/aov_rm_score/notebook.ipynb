{"cells": [{"cell_type": "code", "execution_count": null, "id": "018420ae-2a34-4090-ac2f-12f78cdc9aeb", "metadata": {}, "outputs": [], "source": ["!pip install pandasql\n", "\n", "\n", "import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "import pytz\n", "import warnings"]}, {"cell_type": "code", "execution_count": null, "id": "4a03e979-f434-4299-a6bf-992a2868f0a4", "metadata": {}, "outputs": [], "source": ["current_date = datetime.now(pytz.timezone(\"Asia/Kolkata\")).strftime(\"%Y-%m-%d\")\n", "con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "my_query = \"\"\"\n", "with new_base as (\n", "    with base as (\n", "    select user_id, 1.0*sum(total_selling_price)/count(distinct cart_id) as aov ,\n", "    sum(total_selling_price) as wallet_share ,\n", "    100.0*sum(total_retained_margin)/sum(total_selling_price) as rm ,count(distinct cart_id) as transactions,\n", "    1.0*sum(total_selling_price)/sum(items) as asp from (\n", "    select dim_master_customer_key as user_id , cart_id , total_selling_price  ,\n", "    total_retained_margin  ,total_procured_quantity as items\n", "    from dwh.fact_sales_order_details\n", "    where cart_checkout_ts_ist >= current_date-90\n", "    and order_current_status <> 'CANCELLED'\n", "    and total_selling_price > 0\n", "    and total_procured_quantity > 0\n", "    group by 1,2,3,4,5\n", "    )\n", "    group by 1\n", "    ) ,\n", "    \n", "    wallet_share as (\n", "    select user_id , avg(wallet_share) as wallet_share from (\n", "    select dim_master_customer_key as user_id , \n", "    extract('month' from cart_checkout_ts_ist) as month ,sum(total_selling_price) as wallet_share\n", "    from dwh.fact_sales_order_details\n", "    where cart_checkout_ts_ist >= date(date_trunc('month' , current_date))-90\n", "    and order_current_status <> 'CANCELLED'\n", "    and total_selling_price > 0\n", "    and total_procured_quantity > 0\n", "    group by 1,2\n", "    )\n", "    group by 1\n", "    ) ,\n", "    \n", "    \n", "    final_base as (\n", "    select b.user_id ,\n", "    (case when aov between 0 and 200 then 1\n", "    when aov between 200 and 400 then 2 \n", "    when aov between 400 and 600 then 3\n", "    when aov between 600 and 800 then 4\n", "    else 5 end) as aov ,\n", "    (case when rm between 0 and 5 then 1 \n", "    when rm between 5 and 10 then 2 \n", "    when rm between 10 and 15 then 3 \n", "    when rm between 15 and 20 then 4\n", "    else 5 end) as rm ,transactions , w.wallet_share\n", "    from base b join wallet_share w on b.user_id = w.user_id\n", "    ) ,\n", "    \n", "    \n", "    score_pre_boost as (\n", "    select distinct user_id , aov , rm , wallet_share ,\n", "    transactions ,\n", "    (case when transactions <= 3 then 0 \n", "    when rm = 1 or (rm = 2 and aov < 3) or (rm = 3 and aov = 1) then 1\n", "    when (rm = 2 and aov > 2) or (rm = 3 and aov between 2 and 3) or (rm = 4 and aov = 1) then 2\n", "    when (rm = 3 and aov = 4) or (rm = 4 and aov between 2 and 3) or (rm = 5 and aov = 1) then 3\n", "    when (rm = 3 and aov = 5) or (rm = 4 and aov = 4) or (rm = 5 and aov between 2 and 3) then 4\n", "    else 5 end) as score ,\n", "    current_date as last_updated_at \n", "    from final_base \n", "    )\n", "    \n", "    select user_id ,aov ,rm,wallet_share ,transactions ,\n", "    (case when score > 0 and wallet_share >= (select PERCENTILE_CONT ( 0.9 ) WITHIN GROUP (order by wallet_share) from score_pre_boost ) then score+1 else score end) as score ,\n", "    last_updated_at\n", "    from score_pre_boost\n", "    \n", "    \n", ") ,\n", "\n", "old_base as (\n", "select distinct user_id ,aov ,rm ,transactions , score,last_updated_at from consumer.user_aov_rm_score\n", ") ,\n", "\n", "update_scr as (\n", "select o.user_id ,(case when n.user_id is null then o.aov else n.aov end) as aov, \n", "(case when n.user_id is null then o.rm else n.rm end) as rm,\n", "(case when n.user_id is null then o.transactions else n.transactions end) as transactions,\n", "(case when n.user_id is null then o.score else n.score end) as score ,\n", "(case when n.user_id is null then o.last_updated_at else n.last_updated_at end) as last_updated_at \n", "from old_base o left join new_base n on o.user_id = n.user_id\n", ") ,\n", "\n", "new_user_scr as (\n", "select * from (\n", "select n.user_id , n.aov , n.rm ,n.transactions , n.score ,n.last_updated_at , o.user_id as indicator\n", "from new_base n left join old_base o on n.user_id = o.user_id\n", ")\n", "where indicator is null\n", ")\n", "\n", "select * from update_scr \n", "union all\n", "select user_id , aov , rm ,transactions, score,last_updated_at\n", "from new_user_scr\n", "\"\"\".format()\n", "\n", "df = pd.read_sql_query(my_query, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "ad837674-5905-44ad-a2ca-2311a61913b7", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"consumer\",\n", "    \"table_name\": \"user_aov_rm_score\",\n", "    \"primary_key\": [],\n", "    \"column_dtypes\": [\n", "        {\"name\": \"user_id\", \"type\": \"BIGINT\", \"description\": \"user_id\"},\n", "        {\"name\": \"aov\", \"type\": \"INTEGER\", \"description\": \"aov_score\"},\n", "        {\"name\": \"rm\", \"type\": \"INTEGER\", \"description\": \"rm_score\"},\n", "        {\"name\": \"transactions\", \"type\": \"INTEGER\", \"description\": \"transactions\"},\n", "        {\"name\": \"score\", \"type\": \"INTEGER\", \"description\": \"overall_score\"},\n", "        {\"name\": \"last_updated_at\", \"type\": \"DATE\", \"description\": \"last_update_date\"},\n", "    ],\n", "    \"sortkey\": [],\n", "    \"incremental_key\": \"\",\n", "    \"load_type\": \"truncate\",  # append, , truncate or upsert, rebuild\n", "    \"table_description\": \"This table contains the user type details\",\n", "}\n", "\n", "\n", "####   run this query to write data on table\n", "pb.to_redshift(df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "9a23f1f6-173f-4fc2-8ddb-f67b24ef5acf", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"segment_computed_traits\",\n", "    \"table_name\": \"user_aov_rm_score\",\n", "    \"primary_key\": [],\n", "    \"column_dtypes\": [\n", "        {\"name\": \"user_id\", \"type\": \"BIGINT\", \"description\": \"user_id\"},\n", "        {\"name\": \"aov\", \"type\": \"INTEGER\", \"description\": \"aov_score\"},\n", "        {\"name\": \"rm\", \"type\": \"INTEGER\", \"description\": \"rm_score\"},\n", "        {\"name\": \"transactions\", \"type\": \"INTEGER\", \"description\": \"transactions\"},\n", "        {\"name\": \"score\", \"type\": \"INTEGER\", \"description\": \"overall_score\"},\n", "        {\"name\": \"last_updated_at\", \"type\": \"DATE\", \"description\": \"last_update_date\"},\n", "    ],\n", "    \"sortkey\": [],\n", "    \"incremental_key\": \"\",\n", "    \"load_type\": \"truncate\",  # append, , truncate or upsert, rebuild\n", "    \"table_description\": \"This table contains the user type details\",\n", "}\n", "\n", "\n", "####   run this query to write data on table\n", "pb.to_redshift(df, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}