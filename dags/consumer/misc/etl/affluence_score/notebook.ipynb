{"cells": [{"cell_type": "code", "execution_count": null, "id": "3be760e1-cb3c-4e5c-a7fc-b0826d835414", "metadata": {}, "outputs": [], "source": ["!pip install pandasql\n", "\n", "import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "import pytz\n", "\n", "\n", "current_date = datetime.now(pytz.timezone(\"Asia/Kolkata\")).strftime(\"%Y-%m-%d\")\n", "con = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "2f37298e-4c53-4ae1-8a4d-c6f3e70686ee", "metadata": {}, "outputs": [], "source": ["my_query = \"\"\"\n", "WITH\n", "l1_category AS (\n", "    SELECT\n", "        l1_category_id,\n", "        MAX(l1_category) AS l1_category\n", "    FROM\n", "        dwh.dim_product\n", "    WHERE\n", "        is_current\n", "        AND is_product_enabled\n", "    GROUP BY\n", "        1\n", "),\n", "\n", "\n", "sold_items AS (\n", "    SELECT\n", "        l1_category_id,\n", "        item_id,\n", "        AVG(sp_) AS sp,\n", "        AVG(weight_in_gm_) AS weight_in_gm,\n", "        sp::FLOAT/weight_in_gm AS price_per_gram,\n", "        AVG(qty_sold_) AS qty_sold\n", "    FROM\n", "        (SELECT\n", "            o.cart_checkout_ts_ist::DATE AS date_,\n", "            ic.l1_id AS l1_category_id,\n", "            o.item_id,\n", "            AVG(o.unit_forward_selling_price) AS sp_,\n", "            AVG(pp.weight_in_gm) AS weight_in_gm_,\n", "            SUM(o.total_net_quantity) AS qty_sold_\n", "        FROM\n", "            dwh.fact_sales_invoice_item_details\n", "                AS o\n", "        INNER JOIN\n", "            lake_rpc.item_category_details\n", "                AS ic\n", "                ON ic.item_id = o.item_id\n", "        INNER JOIN\n", "            lake_rpc.item_product_mapping\n", "                AS ipm\n", "                ON ipm.item_id = o.item_id\n", "                AND ipm.offer_id IS NULL\n", "        INNER JOIN\n", "            lake_rpc.product_product\n", "                AS pp\n", "                ON pp.item_id = ipm.item_id\n", "        INNER JOIN\n", "            (SELECT\n", "                item_id,\n", "                max(id) AS max_id\n", "            FROM\n", "                lake_rpc.product_product\n", "            WHERE\n", "                active = 1\n", "            GROUP BY\n", "                1\n", "            )\n", "                AS mp\n", "                ON mp.item_id = pp.item_id\n", "                AND mp.max_id = pp.id\n", "        WHERE\n", "            cart_checkout_ts_ist BETWEEN current_date-90 and current_date-1\n", "            AND is_cancelled_suborder = FALSE\n", "        GROUP BY\n", "            1, 2, 3\n", "        )\n", "    GROUP BY\n", "        1, 2\n", "),\n", "\n", "category_wise_percentiles AS (\n", "    SELECT\n", "        l1_category_id_ AS l1_category_id,\n", "        price_per_gram_,\n", "        sales,\n", "        running_sum_as_per_ascending_sales,\n", "        \n", "        PERCENTILE_CONT(0.1) WITHIN GROUP ( \n", "            ORDER BY\n", "                running_sum_as_per_ascending_sales\n", "        ) OVER (\n", "            PARTITION BY\n", "                l1_category_id\n", "        ) AS percentile_10,\n", "        \n", "        PERCENTILE_CONT(0.2) WITHIN GROUP ( \n", "            ORDER BY\n", "                running_sum_as_per_ascending_sales\n", "        ) OVER (\n", "            PARTITION BY\n", "                l1_category_id\n", "        ) AS percentile_20,\n", "        \n", "        PERCENTILE_CONT(0.3) WITHIN GROUP ( \n", "            ORDER BY\n", "                running_sum_as_per_ascending_sales\n", "        ) OVER (\n", "            PARTITION BY\n", "                l1_category_id\n", "        ) AS percentile_30,\n", "        \n", "        PERCENTILE_CONT(0.4) WITHIN GROUP ( \n", "            ORDER BY\n", "                running_sum_as_per_ascending_sales\n", "        ) OVER (\n", "            PARTITION BY\n", "                l1_category_id\n", "        ) AS percentile_40,\n", "        \n", "        PERCENTILE_CONT(0.5) WITHIN GROUP ( \n", "            ORDER BY\n", "                running_sum_as_per_ascending_sales\n", "        ) OVER (\n", "            PARTITION BY\n", "                l1_category_id\n", "        ) AS percentile_50,\n", "        \n", "        PERCENTILE_CONT(0.6) WITHIN GROUP ( \n", "            ORDER BY\n", "                running_sum_as_per_ascending_sales\n", "        ) OVER (\n", "            PARTITION BY\n", "                l1_category_id\n", "        ) AS percentile_60,\n", "        \n", "        PERCENTILE_CONT(0.7) WITHIN GROUP ( \n", "            ORDER BY\n", "                running_sum_as_per_ascending_sales\n", "        ) OVER (\n", "            PARTITION BY\n", "                l1_category_id\n", "        ) AS percentile_70,\n", "        \n", "        PERCENTILE_CONT(0.8) WITHIN GROUP ( \n", "            ORDER BY\n", "                running_sum_as_per_ascending_sales\n", "        ) OVER (\n", "            PARTITION BY\n", "                l1_category_id\n", "        ) AS percentile_80,\n", "        \n", "        PERCENTILE_CONT(0.9) WITHIN GROUP ( \n", "            ORDER BY\n", "                running_sum_as_per_ascending_sales\n", "        ) OVER (\n", "            PARTITION BY\n", "                l1_category_id\n", "        ) AS percentile_90,\n", "        \n", "        PERCENTILE_CONT(1.0) WITHIN GROUP ( \n", "            ORDER BY\n", "                running_sum_as_per_ascending_sales\n", "        ) OVER (\n", "            PARTITION BY\n", "                l1_category_id\n", "        ) AS percentile_100\n", "        \n", "    FROM\n", "        (SELECT\n", "            s.l1_category_id AS l1_category_id_,\n", "            ROUND(s.price_per_gram, 2) AS price_per_gram_,\n", "            SUM(s.qty_sold) AS sales,\n", "            \n", "            SUM(sales) OVER (\n", "                PARTITION BY\n", "                    l1_category_id_\n", "                ORDER BY\n", "                    price_per_gram_\n", "                ROWS UNBOUNDED PRECEDING\n", "            )\n", "            AS running_sum_as_per_ascending_sales\n", "            \n", "        FROM\n", "            sold_items\n", "                AS s\n", "        GROUP BY\n", "            1, 2\n", "        )\n", "),\n", "\n", "category_wise_70th_percentile AS (\n", "    SELECT\n", "        l1_category_id,\n", "        price_per_gram_ AS price_per_gram_70th_percentile\n", "    FROM\n", "        (SELECT\n", "            l1_category_id,\n", "            price_per_gram_,\n", "            ROW_NUMBER() OVER(PARTITION BY l1_category_id ORDER BY running_sum_as_per_ascending_sales DESC) AS rank_\n", "        FROM\n", "            category_wise_percentiles\n", "        WHERE\n", "            running_sum_as_per_ascending_sales < percentile_70\n", "        )\n", "    WHERE\n", "        rank_ = 1\n", "),\n", "\n", "premium_item_classification AS (\n", "    SELECT\n", "        s.item_id,\n", "        \n", "        CASE\n", "            WHEN s.price_per_gram >= p.price_per_gram_70th_percentile\n", "            THEN 'Premium'\n", "            ELSE 'Non-Premium'\n", "        END AS item_type\n", "\n", "    FROM\n", "        sold_items\n", "            AS s\n", "    INNER JOIN\n", "        category_wise_70th_percentile\n", "            AS p\n", "            ON p.l1_category_id = s.l1_category_id\n", "    GROUP BY\n", "        1, 2\n", "),\n", "\n", "old_users AS (\n", "    SELECT\n", "        dim_master_customer_key AS customer_id,\n", "        MAX(delivered_cart_rank) AS max_cart_rank\n", "    FROM\n", "        dwh.fact_sales_order_details\n", "    WHERE\n", "        cart_checkout_ts_ist \n", "            BETWEEN current_date-90 and current_date-1\n", "    GROUP BY\n", "        1\n", "    HAVING\n", "        max_cart_rank > 3\n", "),\n", "\n", "customer_wise_premium_sales AS (\n", "    SELECT\n", "        c.master_customer_id AS customer_id,\n", "        COUNT(DISTINCT ic.l1_id) AS l1_categories_bought,\n", "        \n", "        CASE\n", "            WHEN COUNT(DISTINCT o.cart_id) > 0\n", "            THEN COUNT(DISTINCT CASE WHEN i.item_type = 'Premium' THEN o.cart_id END)::FLOAT/COUNT(DISTINCT o.cart_id) \n", "        END AS premium_carts_percent,\n", "        \n", "        CASE\n", "            WHEN SUM(o.total_net_selling_price) > 0\n", "            THEN SUM(CASE WHEN i.item_type = 'Premium' THEN o.total_net_selling_price END)::FLOAT/SUM(o.total_net_selling_price)\n", "        END AS premium_items_spend\n", "    FROM\n", "        dwh.fact_sales_invoice_item_details\n", "            AS o\n", "    LEFT JOIN\n", "        lake_rpc.item_category_details\n", "            AS ic\n", "            ON ic.item_id = o.item_id\n", "    LEFT JOIN\n", "        premium_item_classification\n", "            AS i\n", "            ON i.item_id = o.item_id\n", "    join dwh.dim_customer c on o.dim_customer_key = c.customer_id\n", "    WHERE\n", "        cart_checkout_ts_ist \n", "            BETWEEN current_date-90 and current_date-1\n", "        AND is_cancelled_suborder = FALSE\n", "    GROUP BY\n", "        1\n", "),\n", "\n", "cb_deciles AS (\n", "    SELECT\n", "        PERCENTILE_CONT(0.1) WITHIN GROUP ( \n", "            ORDER BY\n", "                l1_categories_bought\n", "        ) AS cb_percentile_10,\n", "        \n", "        PERCENTILE_CONT(0.2) WITHIN GROUP ( \n", "            ORDER BY\n", "                l1_categories_bought\n", "        ) AS cb_percentile_20,\n", "        \n", "        PERCENTILE_CONT(0.3) WITHIN GROUP ( \n", "            ORDER BY\n", "                l1_categories_bought\n", "        ) AS cb_percentile_30,\n", "        \n", "        PERCENTILE_CONT(0.4) WITHIN GROUP ( \n", "            ORDER BY\n", "                l1_categories_bought\n", "        ) AS cb_percentile_40,\n", "        \n", "        PERCENTILE_CONT(0.5) WITHIN GROUP ( \n", "            ORDER BY\n", "                l1_categories_bought\n", "        ) AS cb_percentile_50,\n", "        \n", "        PERCENTILE_CONT(0.6) WITHIN GROUP ( \n", "            ORDER BY\n", "                l1_categories_bought\n", "        ) AS cb_percentile_60,\n", "        \n", "        PERCENTILE_CONT(0.7) WITHIN GROUP ( \n", "            ORDER BY\n", "                l1_categories_bought\n", "        ) AS cb_percentile_70,\n", "        \n", "        PERCENTILE_CONT(0.8) WITHIN GROUP ( \n", "            ORDER BY\n", "                l1_categories_bought\n", "        ) AS cb_percentile_80,\n", "        \n", "        PERCENTILE_CONT(0.9) WITHIN GROUP ( \n", "            ORDER BY\n", "                l1_categories_bought\n", "        ) AS cb_percentile_90,\n", "        \n", "        PERCENTILE_CONT(1.0) WITHIN GROUP ( \n", "            ORDER BY\n", "                l1_categories_bought\n", "        ) AS cb_percentile_100\n", "\n", "    FROM\n", "        customer_wise_premium_sales\n", "),\n", "\n", "pcp_deciles AS (\n", "    SELECT\n", "        PERCENTILE_CONT(0.1) WITHIN GROUP ( \n", "            ORDER BY\n", "                premium_carts_percent\n", "        ) AS pcp_percentile_10,\n", "        \n", "        PERCENTILE_CONT(0.2) WITHIN GROUP ( \n", "            ORDER BY\n", "                premium_carts_percent\n", "        ) AS pcp_percentile_20,\n", "        \n", "        PERCENTILE_CONT(0.3) WITHIN GROUP ( \n", "            ORDER BY\n", "                premium_carts_percent\n", "        ) AS pcp_percentile_30,\n", "        \n", "        PERCENTILE_CONT(0.4) WITHIN GROUP ( \n", "            ORDER BY\n", "                premium_carts_percent\n", "        ) AS pcp_percentile_40,\n", "        \n", "        PERCENTILE_CONT(0.5) WITHIN GROUP ( \n", "            ORDER BY\n", "                premium_carts_percent\n", "        ) AS pcp_percentile_50,\n", "        \n", "        PERCENTILE_CONT(0.6) WITHIN GROUP ( \n", "            ORDER BY\n", "                premium_carts_percent\n", "        ) AS pcp_percentile_60,\n", "        \n", "        PERCENTILE_CONT(0.7) WITHIN GROUP ( \n", "            ORDER BY\n", "                premium_carts_percent\n", "        ) AS pcp_percentile_70,\n", "        \n", "        PERCENTILE_CONT(0.8) WITHIN GROUP ( \n", "            ORDER BY\n", "                premium_carts_percent\n", "        ) AS pcp_percentile_80,\n", "        \n", "        PERCENTILE_CONT(0.9) WITHIN GROUP ( \n", "            ORDER BY\n", "                premium_carts_percent\n", "        ) AS pcp_percentile_90,\n", "        \n", "        PERCENTILE_CONT(1.0) WITHIN GROUP ( \n", "            ORDER BY\n", "                premium_carts_percent\n", "        ) AS pcp_percentile_100\n", "\n", "    FROM\n", "        customer_wise_premium_sales\n", "),\n", "\n", "pis_deciles AS (\n", "    SELECT\n", "        PERCENTILE_CONT(0.1) WITHIN GROUP ( \n", "            ORDER BY\n", "                premium_items_spend\n", "        ) AS pis_percentile_10,\n", "        \n", "        PERCENTILE_CONT(0.2) WITHIN GROUP ( \n", "            ORDER BY\n", "                premium_items_spend\n", "        ) AS pis_percentile_20,\n", "        \n", "        PERCENTILE_CONT(0.3) WITHIN GROUP ( \n", "            ORDER BY\n", "                premium_items_spend\n", "        ) AS pis_percentile_30,\n", "        \n", "        PERCENTILE_CONT(0.4) WITHIN GROUP ( \n", "            ORDER BY\n", "                premium_items_spend\n", "        ) AS pis_percentile_40,\n", "        \n", "        PERCENTILE_CONT(0.5) WITHIN GROUP ( \n", "            ORDER BY\n", "                premium_items_spend\n", "        ) AS pis_percentile_50,\n", "        \n", "        PERCENTILE_CONT(0.6) WITHIN GROUP ( \n", "            ORDER BY\n", "                premium_items_spend\n", "        ) AS pis_percentile_60,\n", "        \n", "        PERCENTILE_CONT(0.7) WITHIN GROUP ( \n", "            ORDER BY\n", "                premium_items_spend\n", "        ) AS pis_percentile_70,\n", "        \n", "        PERCENTILE_CONT(0.8) WITHIN GROUP ( \n", "            ORDER BY\n", "                premium_items_spend\n", "        ) AS pis_percentile_80,\n", "        \n", "        PERCENTILE_CONT(0.9) WITHIN GROUP ( \n", "            ORDER BY\n", "                premium_items_spend\n", "        ) AS pis_percentile_90,\n", "        \n", "        PERCENTILE_CONT(1.0) WITHIN GROUP ( \n", "            ORDER BY\n", "                premium_items_spend\n", "        ) AS pis_percentile_100\n", "\n", "    FROM\n", "        customer_wise_premium_sales\n", "),\n", "\n", "customer_wise_metrics AS (\n", "    SELECT\n", "        s.customer_id,\n", "        s.l1_categories_bought,\n", "       \n", "        \n", "        s.premium_carts_percent,\n", "        \n", "        \n", "        s.premium_items_spend,\n", "        (s.premium_carts_percent + s.premium_items_spend) as score, \n", "        case when l1_categories_bought>28 then 1 \n", "            when l1_categories_bought>18 then 0.75\n", "            when l1_categories_bought>13 then 0.5\n", "            else 0.25 end as weights \n", "       \n", "\n", "    FROM\n", "        customer_wise_premium_sales AS s) ,\n", "\n", "final as (\n", "SELECT\n", "    round(score*weights,1) as affluence_score ,\n", "    customer_id\n", "FROM\n", "    customer_wise_metrics s\n", "    ) ,\n", "    \n", "aov_rm_score as (\n", "select user_id , score\n", "from segment_computed_traits.user_aov_rm_score\n", "group by 1,2\n", ") ,\n", "\n", "temp as (\n", "select current_date as last_updated_at , f.customer_id , f.affluence_score , a.score as aov_rm_score\n", "from final f join aov_rm_score a on f.customer_id = a.user_id\n", "group by 1,2,3,4\n", ")\n", "\n", "select last_updated_at ,\n", "customer_id , affluence_score , aov_rm_score ,\n", "(case when aff_score is null then 0 \n", "when aff_score = 1 then 1 \n", "when aff_score in (2,3) then 2\n", "when aff_score in (4,5,6) then aff_score-1\n", "when aff_score in (7,8,9) then 6 end)  as final_score from (\n", "select last_updated_at , customer_id ,affluence_score ,\n", "aov_rm_score ,  case when affluence_score between 0.1 and 0.4 then (aov_rm_score+1)\n", "when affluence_score between 0.5 and 0.7 then (aov_rm_score+2)\n", "when affluence_score >=0.8 then (aov_rm_score+3) end aff_score\n", "from temp\n", "group by 1,2,3,4,5\n", ")\n", "\"\"\".format()"]}, {"cell_type": "code", "execution_count": null, "id": "ac90b402-7762-477f-9e1d-0dd66c98ced4", "metadata": {}, "outputs": [], "source": ["df = pd.read_sql_query(my_query, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "ad815a9a-edbc-47ae-a1da-bfa6b3b4631f", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"consumer\",\n", "    \"table_name\": \"affluence_score_logs\",\n", "    \"primary_key\": [],\n", "    \"column_dtypes\": [\n", "        {\"name\": \"last_updated_at\", \"type\": \"DATE\", \"description\": \"last_update_date\"},\n", "        {\"name\": \"customer_id\", \"type\": \"BIGINT\", \"description\": \"user_id\"},\n", "        {\"name\": \"affluence_score\", \"type\": \"FLOAT\", \"description\": \"affluence_score\"},\n", "        {\"name\": \"aov_rm_score\", \"type\": \"INTEGER\", \"description\": \"aov_rm_score\"},\n", "        {\"name\": \"final_score\", \"type\": \"INTEGER\", \"description\": \"final_score\"},\n", "    ],\n", "    \"sortkey\": [],\n", "    \"incremental_key\": \"\",\n", "    \"load_type\": \"append\",  # append, , truncate or upsert, rebuild\n", "    \"table_description\": \"This table contains the user type details\",\n", "}\n", "\n", "\n", "####   run this query to write data on table\n", "pb.to_redshift(df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "e7de8a13-1d42-45ca-8b29-feb13fd534d3", "metadata": {}, "outputs": [], "source": ["q2 = \"\"\"\n", "select * from (\n", "select last_updated_at ,customer_id , affluence_score ,aov_rm_score ,final_score ,\n", "rank() over (partition by customer_id order by last_updated_at desc) as rn\n", "from consumer.affluence_score_logs\n", ")\n", "where rn = 1\n", "\"\"\".format()"]}, {"cell_type": "code", "execution_count": null, "id": "c1a884cd-c432-4e10-90a7-b9b5e3411dee", "metadata": {}, "outputs": [], "source": ["df2 = pd.read_sql_query(q2, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "a34a10db-f38e-470e-9401-bfa4412e2732", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"consumer\",\n", "    \"table_name\": \"affluence_score\",\n", "    \"primary_key\": [],\n", "    \"column_dtypes\": [\n", "        {\"name\": \"last_updated_at\", \"type\": \"DATE\", \"description\": \"last_update_date\"},\n", "        {\"name\": \"customer_id\", \"type\": \"BIGINT\", \"description\": \"user_id\"},\n", "        {\"name\": \"affluence_score\", \"type\": \"FLOAT\", \"description\": \"affluence_score\"},\n", "        {\"name\": \"aov_rm_score\", \"type\": \"INTEGER\", \"description\": \"aov_rm_score\"},\n", "        {\"name\": \"final_score\", \"type\": \"INTEGER\", \"description\": \"final_score\"},\n", "    ],\n", "    \"sortkey\": [],\n", "    \"incremental_key\": \"\",\n", "    \"load_type\": \"truncate\",  # append, , truncate or upsert, rebuild\n", "    \"table_description\": \"This table contains the user type details\",\n", "}\n", "\n", "\n", "####   run this query to write data on table\n", "pb.to_redshift(df, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}