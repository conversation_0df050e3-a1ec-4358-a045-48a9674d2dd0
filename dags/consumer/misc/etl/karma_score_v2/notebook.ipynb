{"cells": [{"cell_type": "code", "execution_count": null, "id": "1c61a987-d56d-48bd-a8cc-b28f2b37b221", "metadata": {"tags": []}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import pencilbox as pb\n", "import logging\n", "from datetime import datetime\n", "from pytz import timezone"]}, {"cell_type": "code", "execution_count": null, "id": "0b8ed94c-aa90-4277-96ff-25a13e3f7a31", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "93bc23e4-6bcc-42ac-a2e0-591ef07558e7", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "with cust_level as (\n", "select\n", "oid.dim_customer_key,\n", "cart_checkout_ts_ist as order_ts,\n", "ci.install_ts as complaint_ts,\n", "oid.cart_id,\n", "ci.order_item_id,\n", "oid.product_id,\n", "unit_selling_price,\n", "unit_retained_margin,\n", "total_selling_price,\n", "oid.product_quantity,\n", "ci.quantity as complaint_quantity,\n", "ci.id,\n", "ci.reason,\n", "l0_category,\n", "rf.ticket_id as refund_ticket\n", "\n", "from dwh.fact_sales_order_item_details oid\n", "left join lake_crm.crm_issue_item ci on ci.order_item_id = oid.order_item_id\n", "left join lake_payments_db.gr_payment_refund rf on rf.ticket_id = ci.issue_id and rf.status = 'SUCCESS' \n", "left join lake_crm.view_crm_issue ISS ON ISS.id=cI.issue_id\n", "left join dwh.dim_product dp on oid.dim_product_key = dp.product_key --and dp.is_current = true\n", "left join dwh.dim_merchant dm on dm.merchant_id = oid.frontend_merchant_id and dm.is_current = true\n", "\n", "where cart_checkout_ts_ist between current_date-90 and current_date-1\n", "and order_current_status = 'DELIVERED'\n", "and order_type not ilike '%%internal%%'\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15\n", ")\n", ",\n", "\n", "abuse_scores as (\n", "select\n", "dim_customer_key ,\n", "count(distinct cart_id) as delivered_orders,\n", "sum(total_selling_price) as gmv,\n", "count(distinct case when id is not null then cart_id end) as complaint_orders,\n", "count(distinct case when refund_ticket is not null then cart_id end) as refund_orders,\n", "count(distinct id) as complaints,\n", "sum(unit_selling_price*complaint_quantity) as complaint_amount,\n", "coalesce(sum(case when refund_ticket is not null then unit_selling_price*complaint_quantity end),0) as refund_amount ,\n", "(case when delivered_orders = 0 then 0 else 100.0*complaint_orders/delivered_orders end) as complaint_orders_perc ,\n", "(case when delivered_orders = 0 then 0 else 100.0*refund_orders/delivered_orders end) as refund_orders_perc,\n", "(case when gmv = 0 then 0 else 100.0*refund_amount/gmv end) as refund_amount_perc\n", "from cust_level cl\n", "\n", "group by 1\n", ") ,\n", "\n", "\n", "base as (\n", "SELECT dim_customer_key,\n", "       count(distinct cart_id) AS carts,\n", "       sum(total_selling_price) AS gmv,\n", "       min(cart_checkout_ts_ist) AS first_date,\n", "       max(cart_checkout_ts_ist) as last_date ,\n", "       sum(total_retained_margin) as rm,\n", "       DATEDIFF(DAY, first_date, CURRENT_DATE) AS active_days,\n", "       DATEDIFF(DAY , last_date ,CURRENT_DATE) as days_since_last_order,\n", "       sum(total_discount_amount) as discount,\n", "       sum(total_cashback_amount) as cashback,\n", "       sum(total_delivery_cost) as delivery_fee,\n", "       sum(tip_amount) as tip_amount,\n", "       sum(slot_charges) as slot_charge,\n", "       max(delivered_cart_rank) as cart_rank,\n", "       avg(order_rating) as order_rating\n", "       \n", "FROM dwh.fact_sales_order_details od\n", "WHERE order_current_status = 'DELIVERED'\n", "  AND cart_checkout_ts_ist::date BETWEEN CURRENT_DATE - 90 AND CURRENT_DATE-1\n", "  AND od.order_type NOT ILIKE '%%internal%%'\n", "  AND od.order_type NOT ILIKE '%%dropshipping%%'\n", "  AND od.city_name NOT ILIKE '%%not in service area%%'\n", "  AND od.city_name NOT ILIKE '%%narela%%'\n", "  AND od.city_name NOT ILIKE '%%haridwar%%'\n", "  AND od.total_selling_price > 0\n", "GROUP BY 1\n", ")\n", "\n", "\n", "select b.*,\n", "1.0*DATEDIFF(DAY, b.first_date, b.last_date)/(b.carts-1) as recency ,\n", "a.refund_orders ,\n", "a.complaint_orders ,\n", "a.complaints ,\n", "a.refund_amount,\n", "a.complaint_orders_perc ,\n", "a.refund_orders_perc ,\n", "a.refund_amount_perc \n", "from base b\n", "join abuse_scores a on b.dim_customer_key = a.dim_customer_key\n", "where b.carts > 2\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "091b3db8-5a65-4dca-9ea6-85459849238e", "metadata": {"tags": []}, "outputs": [], "source": ["df = pd.read_sql(query, con)  # get_df_from_query(query5)\n", "df[\"frequency\"] = df[\"carts\"]\n", "df[\"monetary\"] = df[\"gmv\"] - df[\"refund_amount\"]\n", "# df[['recency' ,'monetary' ,'frequency' ,'refund_amount_perc' ,'complaint_orders_perc']].describe()"]}, {"cell_type": "code", "execution_count": null, "id": "267bcdf0-635e-4b03-82a4-c7549bfc8dbc", "metadata": {}, "outputs": [], "source": ["refund_amount_perc_mean = df[\"refund_amount_perc\"].mean()\n", "complaint_orders_perc_mean = df[\"complaint_orders_perc\"].mean()\n", "refund_amount_perc_95 = df[\"refund_amount_perc\"].quantile(0.95)\n", "complaint_orders_perc_95 = df[\"complaint_orders_perc\"].quantile(0.95)"]}, {"cell_type": "code", "execution_count": null, "id": "ee141b83-bdda-4799-97ae-f1312168337e", "metadata": {}, "outputs": [], "source": ["# scaler = MinMaxScaler()\n", "# recency quartile segmentation\n", "# df_dash = pd.DataFrame(scaler.fit_transform(df[['recency' ,'frequency' ,'monetary' ,'refund_amount_perc' ,'complaint_orders_perc']]))\n", "# df_dash.columns = ['recency' ,'frequency' ,'monetary' ,'refund_amount_perc' ,'complaint_orders_perc']\n", "r_labels = range(4, 0, -1)\n", "recency = df[\"recency\"].rank(method=\"first\")\n", "r_quartiles, bins = pd.qcut(recency, 4, labels=r_labels, retbins=True)\n", "df = df.assign(R=r_quartiles.values)\n", "\n", "\n", "# frequency quartile segmentation\n", "\n", "f_labels = range(1, 5)\n", "frequency = df[\"frequency\"].rank(method=\"first\")  # rank to deal with duplicate values\n", "f_quartiles, bins = pd.qcut(frequency, 4, labels=f_labels, retbins=True)\n", "df = df.assign(F=f_quartiles.values)\n", "\n", "\n", "# monetary value quartile segmentation\n", "\n", "m_labels = range(1, 5)\n", "monetary = df[\"monetary\"]\n", "m_quartiles, bins = pd.qcut(monetary, 4, labels=m_labels, retbins=True)\n", "df = df.assign(M=m_quartiles.values)\n", "\n", "\n", "# # refund_amount_perc quartile segmentation\n", "\n", "a_labels = range(3, 0, -1)\n", "df_dash = df[df[\"refund_amount_perc\"] > 0][\n", "    [\"dim_customer_key\", \"refund_amount_perc\"]\n", "].copy()\n", "refund_amount_perc = df_dash[\"refund_amount_perc\"].rank(method=\"first\")\n", "a_quartiles, bins = pd.qcut(refund_amount_perc, 3, labels=a_labels, retbins=True)\n", "df_dash = df_dash.assign(A=a_quartiles.values)\n", "df_dash = df_dash[[\"dim_customer_key\", \"A\"]]\n", "df = df.merge(df_dash, how=\"left\", on=\"dim_customer_key\")\n", "\n", "# df = df.assign(A=a_quartiles.values)\n", "\n", "# complaint_rate_perc quartile segmentation\n", "\n", "c_labels = range(3, 0, -1)\n", "df_dash = df[df[\"complaint_orders_perc\"] > 0][\n", "    [\"dim_customer_key\", \"complaint_orders_perc\"]\n", "].copy()\n", "complaint_orders_perc = df_dash[\"complaint_orders_perc\"].rank(method=\"first\")\n", "c_quartiles, bins = pd.qcut(complaint_orders_perc, 3, labels=c_labels, retbins=True)\n", "df_dash = df_dash.assign(C=c_quartiles.values)\n", "df_dash = df_dash[[\"dim_customer_key\", \"C\"]]\n", "df = df.merge(df_dash, how=\"left\", on=\"dim_customer_key\")\n", "\n", "df[\"A\"] = df[\"A\"].cat.add_categories(4)\n", "df[\"A\"].fillna(4, inplace=True)\n", "\n", "df[\"C\"] = df[\"C\"].cat.add_categories(4)\n", "df[\"C\"].fillna(4, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "b833a852-f65f-4808-9959-bab862a651d1", "metadata": {}, "outputs": [], "source": ["# . Assigning Segment and Score\n", "\n", "\n", "def join_rfm(x):\n", "    return str(x[\"R\"]) + str(x[\"F\"]) + str(x[\"M\"]) + str(x[\"A\"]) + str(x[\"C\"])\n", "\n", "\n", "df[\"segment\"] = df.apply(join_rfm, axis=1)\n", "df[\"score\"] = df[[\"R\", \"F\", \"M\", \"A\", \"C\"]].sum(axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "155588ac-169f-4783-94a7-ee543884e63f", "metadata": {}, "outputs": [], "source": ["df[\"score\"] = df.apply(\n", "    lambda row: 4\n", "    if (\n", "        row.complaint_orders_perc >= complaint_orders_perc_95\n", "        and row.refund_amount_perc >= refund_amount_perc_95\n", "    )\n", "    else row.score,\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "623f4d63-1cf7-4a4a-9305-45a75fecfee8", "metadata": {}, "outputs": [], "source": ["df[\"customer_id\"] = df[\"dim_customer_key\"]\n", "df[\"karma_score\"] = df[\"score\"]\n", "# df[\"updated_at\"] = pd.Timestamp(\"today\").strftime(\"%Y-%m-%d\")\n", "df[\"updated_at\"] = pd.Timestamp(\"today\").strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "55124387-d5ed-47a3-a54b-92ed2e3049b4", "metadata": {}, "outputs": [], "source": ["df_insert = df[\n", "    [\n", "        \"customer_id\",\n", "        \"karma_score\",\n", "        \"segment\",\n", "        \"recency\",\n", "        \"frequency\",\n", "        \"monetary\",\n", "        \"carts\",\n", "        \"gmv\",\n", "        \"first_date\",\n", "        \"active_days\",\n", "        #   \"non_fnv_rm_perc\",\n", "        \"updated_at\",\n", "        \"refund_amount_perc\",\n", "        \"complaint_orders_perc\",\n", "        #    \"fmcg_pen\",\n", "        #    \"gm_pen\",\n", "        #    \"staples_pen\",\n", "        #    \"fnv_pen\" ,\n", "        #     \"rm\" , \"fnv_rm\" , \"non_fnv_rm\" ,\n", "        \"refund_orders\",\n", "        \"complaint_orders\",\n", "        \"complaints\",\n", "        \"refund_amount\",\n", "        \"refund_orders_perc\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "2ee5d7d2-4a78-4462-a313-c89fed31172f", "metadata": {}, "outputs": [], "source": ["df_insert[\"karma_label\"] = df_insert.karma_score.apply(\n", "    lambda x: \"Iron\"\n", "    if x == 4\n", "    else \"Bronze\"\n", "    if x < 13\n", "    else \"Silver\"\n", "    if x < 17\n", "    else \"Gold\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1efc5077-ec22-4d33-83cd-52a0b1797605", "metadata": {}, "outputs": [], "source": ["# INSERT INTO TABLE\n", "\n", "column_dtypes = [\n", "    {\"name\": \"customer_id\", \"type\": \"bigint\", \"description\": \"Customer ID\"},\n", "    {\"name\": \"karma_score\", \"type\": \"float\", \"description\": \"Final Score\"},\n", "    {\"name\": \"segment\", \"type\": \"varchar\", \"description\": \"segment\"},\n", "    {\"name\": \"recency\", \"type\": \"float\", \"description\": \"recency\"},\n", "    {\"name\": \"frequency\", \"type\": \"float\", \"description\": \"frequency\"},\n", "    {\"name\": \"monetary\", \"type\": \"float\", \"description\": \"monetary\"},\n", "    {\n", "        \"name\": \"carts\",\n", "        \"type\": \"float\",\n", "        \"description\": \"No of carts in previous 3 months\",\n", "    },\n", "    {\n", "        \"name\": \"gmv\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Total SP in previous 3 months minus the refund\",\n", "    },\n", "    {\n", "        \"name\": \"first_date\",\n", "        \"type\": \"timestamp\",\n", "        \"description\": \"First transaction date on previous 3 months\",\n", "    },\n", "    {\n", "        \"name\": \"active_days\",\n", "        \"type\": \"int\",\n", "        \"description\": \"Days between First transaction date and current date\",\n", "    },\n", "    #  {\"name\": \"abuse_score\", \"type\": \"float\", \"description\": \"Abuse score of the user\"},\n", "    #   {\n", "    #      \"name\": \"non_fnv_rm_perc\",\n", "    #     \"type\": \"float\",\n", "    #    \"description\": \"RM percentage barring FnV items\",\n", "    # },\n", "    {\"name\": \"updated_at\", \"type\": \"date\", \"description\": \"Updated at\"},\n", "    {\n", "        \"name\": \"refund_amount_perc\",\n", "        \"type\": \"float\",\n", "        \"description\": \"refund_amount_perc\",\n", "    },\n", "    {\n", "        \"name\": \"complaint_orders_perc\",\n", "        \"type\": \"float\",\n", "        \"description\": \"complaint_orders_perc\",\n", "    },\n", "    #   {\"name\": \"fmcg_pen\", \"type\": \"float\", \"description\": \"fmcg_pen\"},\n", "    #  {\"name\": \"gm_pen\", \"type\": \"float\", \"description\": \"gm_pen\"},\n", "    #    {\"name\": \"staples_pen\", \"type\": \"float\", \"description\": \"staples_pen\"},\n", "    #    {\"name\": \"fnv_pen\", \"type\": \"float\", \"description\": \"fnv_pen\"} ,\n", "    #   {\"name\": \"rm\", \"type\": \"float\", \"description\": \"rm\"} ,\n", "    #   {\"name\": \"fnv_rm\", \"type\": \"float\", \"description\": \"fnv_rm\"} ,\n", "    #   {\"name\": \"non_fnv_rm\", \"type\": \"float\", \"description\": \"non_fnv_rm\"} ,\n", "    {\"name\": \"refund_orders\", \"type\": \"float\", \"description\": \"refund_orders\"},\n", "    {\"name\": \"complaint_orders\", \"type\": \"float\", \"description\": \"compaint_orders\"},\n", "    {\"name\": \"complaints\", \"type\": \"float\", \"description\": \"complaints\"},\n", "    {\"name\": \"refund_amount\", \"type\": \"float\", \"description\": \"refund_amount\"},\n", "    {\n", "        \"name\": \"refund_orders_perc\",\n", "        \"type\": \"float\",\n", "        \"description\": \"refund_orders_perc\",\n", "    },\n", "    {\"name\": \"karma_label\", \"type\": \"varchar\", \"description\": \"karma_label\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "27d7f180-0825-4a47-a90f-bdbc65f5ad0a", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"consumer\",\n", "    \"table_name\": \"karma_score_v2\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"customer_id\"],\n", "    \"sortkey\": [\"updated_at\"],\n", "    \"incremental_key\": \"customer_id\",\n", "    \"load_type\": \"append\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This table stores final scores and karma label for customers for slot prioritisation; based on Recency , Frequency , Monetary , Complaint rate and refund amount rate. Only customers who transacted in previous 3 months are considered.\",\n", "}\n", "pb.to_redshift(df_insert, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "689860cb-8514-4cf9-8186-248f2e2a8658", "metadata": {}, "outputs": [], "source": ["prod = \"\"\"\n", "\n", "with base as (\n", "select *,\n", "rank() over(partition by customer_id order by updated_at desc) as rnk\n", "\n", "from consumer.karma_score_v2\n", ")\n", "\n", "select customer_id,\n", "karma_score,\n", "karma_label,\n", "updated_at\n", "\n", "from base\n", "where rnk = 1\n", "group by 1,2,3,4\n", "\"\"\"\n", "\n", "df_prod = pd.read_sql(prod, con)"]}, {"cell_type": "code", "execution_count": null, "id": "abeab01f-216a-4e61-afff-e7dd4f601262", "metadata": {}, "outputs": [], "source": ["# INSERT INTO SEGMENT TABLE\n", "\n", "column_dtypes = [\n", "    {\"name\": \"customer_id\", \"type\": \"bigint\", \"description\": \"Customer ID \"},\n", "    {\"name\": \"karma_score\", \"type\": \"float\", \"description\": \"Karma score.range: 4-20\"},\n", "    {\n", "        \"name\": \"karma_label\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Category label : Iron : 4 , Bronze: 5-12 , Silver: 13-16 , Gold: 17-20\",\n", "    },\n", "    {\"name\": \"updated_at\", \"type\": \"date\", \"description\": \"Updated at\"},\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"segment_computed_traits\",\n", "    \"table_name\": \"karma_score_v2\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"customer_id\"],\n", "    \"sortkey\": [\"updated_at\"],\n", "    \"incremental_key\": \"customer_id\",\n", "    \"load_type\": \"truncate\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This table stores final scores and karma label for customers for slot prioritisation; based on Recency , Frequency , Monetary , Complaint rate and refund amount rate. Only customers who transacted in previous 3 months are considered.\",\n", "}\n", "pb.to_redshift(df_prod, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}