{"cells": [{"cell_type": "code", "execution_count": null, "id": "460027be-021b-4e09-ac9c-db712a72378f", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import pencilbox as pb\n", "import logging\n", "from datetime import datetime\n", "from pytz import timezone"]}, {"cell_type": "code", "execution_count": null, "id": "c19da03b-fdde-4336-b5ec-6da215e69e21", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "665b57ca-6fc7-4c11-90c5-c3ce655036ac", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "with cust_level as (\n", "select\n", "oid.dim_master_customer_key as dim_customer_key,\n", "cart_checkout_ts_ist as order_ts,\n", "ci.install_ts as complaint_ts,\n", "oid.cart_id,\n", "ci.order_item_id,\n", "oid.product_id,\n", "unit_selling_price,\n", "unit_retained_margin,\n", "total_selling_price,\n", "oid.product_quantity,\n", "oid.procured_quantity ,\n", "ci.quantity as complaint_quantity,\n", "(case when procured_quantity <> product_quantity and lower(ci.reason) in ('item_missing' ,'missing_item' ,'not_available' , 'missing_freebie')  then null else ci.id end) as id,\n", "lower(ci.reason) as reason,\n", "l0_category_id,\n", "(case when procured_quantity <> product_quantity and lower(rf.refund_reason) in ('mis_item') then null else rf.ticket_id end) as refund_ticket,\n", "ci.given_resolution ,\n", "lower(rf.refund_reason) as refund_reason \n", "\n", "from dwh.fact_sales_order_item_details oid\n", "left join lake_crm.crm_issue_item ci on ci.order_item_id = oid.order_item_id\n", "left join lake_payments_db.gr_payment_refund rf on rf.ticket_id = ci.issue_id and rf.status = 'SUCCESS' \n", "left join lake_crm.view_crm_issue ISS ON ISS.id=cI.issue_id\n", "left join dwh.dim_product dp on oid.dim_product_key = dp.product_key --and dp.is_current = true\n", "left join dwh.dim_merchant dm on dm.merchant_id = oid.frontend_merchant_id and dm.is_current = true\n", "\n", "where cart_checkout_ts_ist between current_date-90 and current_date-1\n", "and order_current_status = 'DELIVERED'\n", "and order_type not ilike '%%internal%%'\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18\n", ") ,\n", "\n", "\n", "abuse_scores as (\n", "select\n", "dim_customer_key,\n", "count(distinct cart_id) as delivered_orders,\n", "sum(total_selling_price) as gmv,\n", "\n", "count(distinct case when id is not null \n", "    and reason not in ('price_difference_customer' ,'price_diff' ,'missing_freebie') \n", "    then cart_id end) as complaint_orders,\n", "    \n", "count(distinct case when refund_ticket is not null \n", "    and lower(given_resolution) <> 'replacement' \n", "    and refund_reason not in    ('ord_cancel','prc_diff' ,'slot_charge_delay' ,\n", "                                'non_acceptable_payment' ,'order_not_delivered' ,\n", "                                 'door_step_complaints')\n", "    then cart_id end) as refund_orders,\n", "    \n", "count(case when id is not null \n", "    and reason not in ('price_difference_customer' ,'price_diff' ,'missing_freebie') \n", "    then id end) as complaints,\n", "    \n", "sum(case when id is not null \n", "    and reason not in ('price_difference_customer' ,'price_diff' ,'missing_freebie') \n", "    then unit_selling_price*complaint_quantity end) as complaint_amount,\n", "\n", "coalesce(sum(case when refund_ticket is not null \n", "    and lower(given_resolution) <> 'replacement' \n", "    and refund_reason not in    ('ord_cancel','prc_diff' ,'slot_charge_delay' ,\n", "                                'non_acceptable_payment' ,'order_not_delivered' ,\n", "                                'door_step_complaints')\n", "then unit_selling_price*complaint_quantity end),0) as refund_amount ,\n", "(case when delivered_orders = 0 then 0 else 100.0*complaint_orders/delivered_orders end) as complaint_orders_perc ,\n", "(case when delivered_orders = 0 then 0 else 100.0*refund_orders/delivered_orders end) as refund_orders_perc,\n", "(case when gmv = 0 then 0 else 100.0*refund_amount/gmv end) as refund_amount_perc\n", "from cust_level cl\n", "\n", "group by 1\n", ") ,\n", "\n", "\n", "base as (\n", "SELECT dim_master_customer_key as dim_customer_key,\n", "      count(distinct cart_id) AS carts,\n", "      sum(total_selling_price) AS gmv,\n", "      min(cart_checkout_ts_ist) AS first_date,\n", "      max(cart_checkout_ts_ist) as last_date ,\n", "      sum(total_retained_margin) as rm,\n", "      DATEDIFF(DAY, first_date, CURRENT_DATE) AS active_days,\n", "      DATEDIFF(DAY , last_date ,CURRENT_DATE) as days_since_last_order,\n", "      sum(total_discount_amount) as discount,\n", "      sum(total_cashback_amount) as cashback,\n", "      sum(total_delivery_cost) as delivery_fee,\n", "      sum(tip_amount) as tip_amount,\n", "      sum(slot_charges) as slot_charge,\n", "      max(delivered_cart_rank) as cart_rank,\n", "      avg(order_rating) as order_rating\n", "       \n", "FROM dwh.fact_sales_order_details od\n", "WHERE order_current_status = 'DELIVERED'\n", "  AND cart_checkout_ts_ist::date BETWEEN CURRENT_DATE - 90 AND CURRENT_DATE-1\n", "  AND od.order_type NOT ILIKE '%%internal%%'\n", "  AND od.order_type NOT ILIKE '%%dropshipping%%'\n", "  AND od.city_name NOT ILIKE '%%not in service area%%'\n", "  AND od.city_name NOT ILIKE '%%narela%%'\n", "  AND od.city_name NOT ILIKE '%%haridwar%%'\n", "  AND od.total_selling_price > 0\n", "GROUP BY 1\n", ") \n", "\n", "\n", "select b.*,\n", "1.0*DATEDIFF(DAY, b.first_date, b.last_date)/(b.carts-1) as recency ,\n", "a.refund_orders ,\n", "a.complaint_orders ,\n", "a.complaints ,\n", "a.refund_amount,\n", "a.complaint_orders_perc ,\n", "a.refund_orders_perc ,\n", "a.refund_amount_perc \n", "from base b\n", "join abuse_scores a on b.dim_customer_key = a.dim_customer_key\n", "where b.carts > 2\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "7e10154e-852a-4938-ae7a-04bf3df2be7e", "metadata": {}, "outputs": [], "source": ["df = pd.read_sql(query, con)  # get_df_from_query(query5)\n", "df[\"frequency\"] = df[\"carts\"]\n", "df[\"monetary\"] = df[\"gmv\"] - df[\"refund_amount\"]"]}, {"cell_type": "code", "execution_count": null, "id": "e99e8fe0-8d63-4dc2-a6fd-886cab21f275", "metadata": {}, "outputs": [], "source": ["refund_amount_perc_mean = df[\"refund_amount_perc\"].mean()\n", "complaint_orders_perc_mean = df[\"complaint_orders_perc\"].mean()\n", "refund_amount_perc_95 = df[\"refund_amount_perc\"].quantile(0.95)\n", "complaint_orders_perc_95 = df[\"complaint_orders_perc\"].quantile(0.95)"]}, {"cell_type": "code", "execution_count": null, "id": "ee657bae-abde-4c4e-a0d2-24995602595f", "metadata": {}, "outputs": [], "source": ["r_labels = range(4, 0, -1)\n", "recency = df[\"recency\"].rank(method=\"first\")\n", "r_quartiles, bins = pd.qcut(recency, 4, labels=r_labels, retbins=True)\n", "df = df.assign(R=r_quartiles.values)\n", "\n", "\n", "# frequency quartile segmentation\n", "\n", "f_labels = range(1, 5)\n", "frequency = df[\"frequency\"].rank(method=\"first\")  # rank to deal with duplicate values\n", "f_quartiles, bins = pd.qcut(frequency, 4, labels=f_labels, retbins=True)\n", "df = df.assign(F=f_quartiles.values)\n", "\n", "\n", "# monetary value quartile segmentation\n", "\n", "m_labels = range(1, 5)\n", "monetary = df[\"monetary\"]\n", "m_quartiles, bins = pd.qcut(monetary, 4, labels=m_labels, retbins=True)\n", "df = df.assign(M=m_quartiles.values)\n", "\n", "\n", "# # refund_amount_perc quartile segmentation\n", "\n", "a_labels = range(3, 0, -1)\n", "df_dash = df[df[\"refund_amount_perc\"] > 0][\n", "    [\"dim_customer_key\", \"refund_amount_perc\"]\n", "].copy()\n", "refund_amount_perc = df_dash[\"refund_amount_perc\"].rank(method=\"first\")\n", "a_quartiles, bins = pd.qcut(refund_amount_perc, 3, labels=a_labels, retbins=True)\n", "df_dash = df_dash.assign(A=a_quartiles.values)\n", "df_dash = df_dash[[\"dim_customer_key\", \"A\"]]\n", "df = df.merge(df_dash, how=\"left\", on=\"dim_customer_key\")\n", "\n", "# df = df.assign(A=a_quartiles.values)\n", "\n", "# complaint_rate_perc quartile segmentation\n", "\n", "c_labels = range(3, 0, -1)\n", "df_dash = df[df[\"complaint_orders_perc\"] > 0][\n", "    [\"dim_customer_key\", \"complaint_orders_perc\"]\n", "].copy()\n", "complaint_orders_perc = df_dash[\"complaint_orders_perc\"].rank(method=\"first\")\n", "c_quartiles, bins = pd.qcut(complaint_orders_perc, 3, labels=c_labels, retbins=True)\n", "df_dash = df_dash.assign(C=c_quartiles.values)\n", "df_dash = df_dash[[\"dim_customer_key\", \"C\"]]\n", "df = df.merge(df_dash, how=\"left\", on=\"dim_customer_key\")\n", "\n", "df[\"A\"] = df[\"A\"].cat.add_categories(4)\n", "df[\"A\"].fillna(4, inplace=True)\n", "\n", "df[\"C\"] = df[\"C\"].cat.add_categories(4)\n", "df[\"C\"].fillna(4, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "50a4cec1-65f9-4b95-b808-e31dd54f14af", "metadata": {}, "outputs": [], "source": ["# . Assigning Segment and Score\n", "def join_rfm(x):\n", "    return str(x[\"R\"]) + str(x[\"F\"]) + str(x[\"M\"]) + str(x[\"A\"]) + str(x[\"C\"])\n", "\n", "\n", "df[\"segment\"] = df.apply(join_rfm, axis=1)\n", "df[\"score\"] = df[[\"R\", \"F\", \"M\", \"A\", \"C\"]].sum(axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "3108b495-2c11-45b5-889e-fec183a94529", "metadata": {}, "outputs": [], "source": ["df[\"score\"] = df.apply(\n", "    lambda row: 4\n", "    if (\n", "        row.complaint_orders > 2\n", "        and row.complaint_orders_perc >= complaint_orders_perc_95\n", "        and row.refund_amount_perc >= refund_amount_perc_95\n", "    )\n", "    else row.score,\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "29566f48-1506-476e-a653-8d3700824516", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fcf4b6be-cc90-4f85-9946-6ca78459423d", "metadata": {}, "outputs": [], "source": ["df[\"customer_id\"] = df[\"dim_customer_key\"]\n", "df[\"karma_score\"] = df[\"score\"]\n", "df[\"updated_at\"] = pd.Timestamp(\"today\").strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "3c7e788e-42e4-4a6b-b900-d1f85ffcf9c5", "metadata": {}, "outputs": [], "source": ["df_insert = df[\n", "    [\n", "        \"customer_id\",\n", "        \"karma_score\",\n", "        \"segment\",\n", "        \"recency\",\n", "        \"frequency\",\n", "        \"monetary\",\n", "        \"carts\",\n", "        \"gmv\",\n", "        \"first_date\",\n", "        \"active_days\",\n", "        \"updated_at\",\n", "        \"refund_amount_perc\",\n", "        \"complaint_orders_perc\",\n", "        \"refund_orders\",\n", "        \"complaint_orders\",\n", "        \"complaints\",\n", "        \"refund_amount\",\n", "        \"refund_orders_perc\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "09ce1c8b-5a6c-4408-93c2-d48fd2dfdb12", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "466f08f5-5e46-4b44-9ea7-9acbc708f8f1", "metadata": {}, "outputs": [], "source": ["df_insert[\"karma_label\"] = df_insert.karma_score.apply(\n", "    lambda x: \"Iron\"\n", "    if x == 4\n", "    else \"Bronze\"\n", "    if x < 13\n", "    else \"Silver\"\n", "    if x < 17\n", "    else \"Gold\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ef9d435f-2d3a-4e2d-92b7-dceffe37d9f7", "metadata": {}, "outputs": [], "source": ["# INSERT INTO TABLE\n", "\n", "column_dtypes = [\n", "    {\"name\": \"customer_id\", \"type\": \"bigint\", \"description\": \"Customer ID\"},\n", "    {\"name\": \"karma_score\", \"type\": \"float\", \"description\": \"Final Score\"},\n", "    {\"name\": \"segment\", \"type\": \"varchar\", \"description\": \"segment\"},\n", "    {\"name\": \"recency\", \"type\": \"float\", \"description\": \"recency\"},\n", "    {\"name\": \"frequency\", \"type\": \"float\", \"description\": \"frequency\"},\n", "    {\"name\": \"monetary\", \"type\": \"float\", \"description\": \"monetary\"},\n", "    {\n", "        \"name\": \"carts\",\n", "        \"type\": \"float\",\n", "        \"description\": \"No of carts in previous 3 months\",\n", "    },\n", "    {\n", "        \"name\": \"gmv\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Total SP in previous 3 months minus the refund\",\n", "    },\n", "    {\n", "        \"name\": \"first_date\",\n", "        \"type\": \"timestamp\",\n", "        \"description\": \"First transaction date on previous 3 months\",\n", "    },\n", "    {\n", "        \"name\": \"active_days\",\n", "        \"type\": \"int\",\n", "        \"description\": \"Days between First transaction date and current date\",\n", "    },\n", "    {\"name\": \"updated_at\", \"type\": \"date\", \"description\": \"Updated at\"},\n", "    {\n", "        \"name\": \"refund_amount_perc\",\n", "        \"type\": \"float\",\n", "        \"description\": \"refund_amount_perc\",\n", "    },\n", "    {\n", "        \"name\": \"complaint_orders_perc\",\n", "        \"type\": \"float\",\n", "        \"description\": \"complaint_orders_perc\",\n", "    },\n", "    {\"name\": \"refund_orders\", \"type\": \"float\", \"description\": \"refund_orders\"},\n", "    {\"name\": \"complaint_orders\", \"type\": \"float\", \"description\": \"compaint_orders\"},\n", "    {\"name\": \"complaints\", \"type\": \"float\", \"description\": \"complaints\"},\n", "    {\"name\": \"refund_amount\", \"type\": \"float\", \"description\": \"refund_amount\"},\n", "    {\n", "        \"name\": \"refund_orders_perc\",\n", "        \"type\": \"float\",\n", "        \"description\": \"refund_orders_perc\",\n", "    },\n", "    {\"name\": \"karma_label\", \"type\": \"varchar\", \"description\": \"karma_label\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "3464fc6c-df8f-4b96-9909-aacdbf2c6916", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"consumer\",\n", "    \"table_name\": \"karma_score_v3\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"customer_id\"],\n", "    \"sortkey\": [\"updated_at\"],\n", "    \"incremental_key\": \"customer_id\",\n", "    \"load_type\": \"append\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This table stores final scores and karma label for customers for slot prioritisation; based on Recency , Frequency , Monetary , Complaint rate and refund amount rate. Only customers who transacted in previous 3 months are considered.\",\n", "}\n", "pb.to_redshift(df_insert, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "b748b382-7b81-45da-ac00-13576435e3a8", "metadata": {}, "outputs": [], "source": ["q = \"\"\"\n", "\n", "with base as (\n", "select master_customer_id , customer_id ,\n", "(case when master_customer_id = customer_id then 1 else 0 end) as chec\n", "from dwh.dim_customer\n", "group by 1,2,3\n", ") \n", "\n", "\n", "select b.customer_id , s.karma_score ,s.segment ,s.recency , s.frequency , s.monetary,\n", "s.carts , s.gmv , s.first_date , s.active_days , s.updated_at , s.refund_amount_perc , s.complaint_orders_perc ,\n", "s.refund_orders , s.complaint_orders , s.complaints , s.refund_amount , s.refund_orders_perc, s.karma_label\n", "from consumer.karma_score_v3 s join base b on s.customer_id = b.master_customer_id and chec = 0 and s.updated_at = current_date\n", "\"\"\"\n", "\n", "df_app = pd.read_sql_query(q, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "fab4cbee-30b2-4ca8-a87a-d37b12caa0c9", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"consumer\",\n", "    \"table_name\": \"karma_score_v3\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"customer_id\"],\n", "    \"sortkey\": [\"updated_at\"],\n", "    \"incremental_key\": \"customer_id\",\n", "    \"load_type\": \"append\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This table stores final scores and karma label for customers for slot prioritisation; based on Recency , Frequency , Monetary , Complaint rate and refund amount rate. Only customers who transacted in previous 3 months are considered.\",\n", "}\n", "pb.to_redshift(df_app, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "ce828646-51e0-4505-9772-3b98da7636cd", "metadata": {}, "outputs": [], "source": ["prod = \"\"\"\n", "\n", "with base as (\n", "select *,\n", "rank() over(partition by customer_id order by updated_at desc) as rnk\n", "\n", "from consumer.karma_score_v3\n", ")\n", "\n", "select customer_id,\n", "karma_score,\n", "karma_label,\n", "updated_at\n", "\n", "from base\n", "where rnk = 1\n", "group by 1,2,3,4\n", "\"\"\"\n", "\n", "df_prod = pd.read_sql(prod, con)"]}, {"cell_type": "code", "execution_count": null, "id": "2cfedfbb-028b-46be-948b-d8e1234cfdfa", "metadata": {}, "outputs": [], "source": ["# INSERT INTO SEGMENT TABLE\n", "\n", "column_dtypes = [\n", "    {\"name\": \"customer_id\", \"type\": \"bigint\", \"description\": \"Customer ID \"},\n", "    {\"name\": \"karma_score\", \"type\": \"float\", \"description\": \"Karma score.range: 4-20\"},\n", "    {\n", "        \"name\": \"karma_label\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Category label : Iron : 4 , Bronze: 5-12 , Silver: 13-16 , Gold: 17-20\",\n", "    },\n", "    {\"name\": \"updated_at\", \"type\": \"date\", \"description\": \"Updated at\"},\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"segment_computed_traits\",\n", "    \"table_name\": \"karma_score_v3\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"customer_id\"],\n", "    \"sortkey\": [\"updated_at\"],\n", "    \"incremental_key\": \"customer_id\",\n", "    \"load_type\": \"truncate\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This table stores final scores and karma label for customers for slot prioritisation; based on Recency , Frequency , Monetary , Complaint rate and refund amount rate. Only customers who transacted in previous 3 months are considered.\",\n", "}\n", "pb.to_redshift(df_prod, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}