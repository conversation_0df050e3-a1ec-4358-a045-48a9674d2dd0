{"cells": [{"cell_type": "code", "execution_count": null, "id": "6e1d902b-cfbd-4bf0-8860-873870e093d8", "metadata": {}, "outputs": [], "source": ["!pip install pandasql\n", "import time\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb\n", "from gspread.exceptions import APIError\n", "from datetime import datetime\n", "from datetime import timedelta\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "20baebc9-b68c-4b47-80ef-ac09cee058cc", "metadata": {}, "outputs": [], "source": ["def from_sheets(sheet_id, sheet_name, service_account=\"service_account\", max_tries=3):\n", "    _retry_for = [429, 500, 503]\n", "    for attempt in range(max_tries):\n", "        print(f\"Trying to read attempt: {attempt}...\")\n", "        try:\n", "            return pb.from_sheets(sheet_id, sheet_name, service_account)\n", "            print(\"Read from the sheet\")\n", "            break\n", "        except APIError as e:\n", "            exception_code = e.response.json()[\"error\"][\"code\"]\n", "            print(e)\n", "            if exception_code in _retry_for:\n", "                time.sleep(60 * attempt)\n", "\n", "\n", "def to_sheets(df, sheet_id, sheet_name, service_account=\"service_account\", max_tries=3):\n", "    _retry_for = [429, 500, 503]\n", "    for attempt in range(max_tries):\n", "        print(f\"Trying to write attempt: {attempt}...\")\n", "        try:\n", "            return pb.to_sheets(df, sheet_id, sheet_name, service_account)\n", "            print(\"Pushed Data to the sheet\")\n", "            break\n", "        except APIError as e:\n", "            exception_code = e.response.json()[\"error\"][\"code\"]\n", "            print(e)\n", "            if exception_code in _retry_for:\n", "                time.sleep(60 * attempt)"]}, {"cell_type": "code", "execution_count": null, "id": "eb97ed97-0bb3-4689-9fa7-7387e70fc328", "metadata": {}, "outputs": [], "source": ["presto = pb.get_connection(\"[Warehouse] Presto\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "909734c2-cbd5-44ca-ae06-d706802c412a", "metadata": {}, "outputs": [], "source": ["Sheet_id = \"1NMayfbmxR-ehA1IytHja2hMwJ9tPKjYa_8-T5knVSzI\""]}, {"cell_type": "code", "execution_count": null, "id": "a3212dd4-8880-4ff9-b02b-6bb6a18ee9eb", "metadata": {}, "outputs": [], "source": ["coins = from_sheets(Sheet_id, \"Coins\")\n", "coins.item_id.astype(int).unique()"]}, {"cell_type": "code", "execution_count": null, "id": "e4e13a31-6740-420d-a16e-8a0661dd5d5a", "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "st = time.time()\n", "inbound = pd.read_sql_query(\n", "    f\"\"\"SELECT f.id as facility_id,\n", "                                       f.name as facility_name,\n", "                                       i.vendor_id,\n", "                                       coalesce(m.name,v.vendor_name) as vendor_name,\n", "                                       i.vendor_invoice_id as invoice_id,\n", "                                       (case when i.source_type = 1 then 'PO'\n", "                                            when i.source_type = 2 then 'STO'\n", "                                            when i.source_type = 3 then 'CRWI'\n", "                                        else null end) as grn_type,\n", "                                       i.po_id, \n", "                                       pp.item_id,\n", "                                       id.name as item_name,\n", "                                       sum(po.units_ordered) as po_qty ,\n", "                                       max(ii.created_at + interval '330' minute) as grn_at,\n", "                                       sum(po.units_ordered*po.landing_rate) as po_value,\n", "                                       sum(ii.\"delta\") as grn_qty,\n", "                                       sum(ii.landing_price * ii.\"delta\") AS grn_value,\n", "                                       dr.name as disc_reason,\n", "                                       max(d.created_at + interval '330' minute) as disc_at,\n", "                                       coalesce(sum(di.quantity),0) as disc_qty,\n", "                                       coalesce(sum(di.landing_price*di.\"quantity\"),0) as disc_value\n", "    FROM (select x.* from lake_view_ims.ims_inward_invoice x\n", "          join lake_view_retail.console_outlet c on c.id = x.outlet_id\n", "          where insert_ds_ist > cast(current_date - interval '30' DAY as varchar) \n", "          and c.business_type_id in (1,12)) i\n", "    JOIN (select * from lake_view_ims.ims_inventory_stock_details\n", "          where insert_ds_ist > cast(current_date - interval '30' DAY as varchar) ) ii ON ii.grn_id = i.grn_id\n", "    JOIN lake_view_retail.console_outlet c on c.id = i.outlet_id and c.business_type_id in (1,12)\n", "    JOIN lake_view_crates.facility f on f.id = c.facility_id\n", "    JOIN lake_view_rpc.product_product pp on pp.variant_id = ii.variant_id and pp.active = 1\n", "    and pp.item_id in {*coins.item_id.astype(int).unique(),}\n", "    JOIN lake_view_rpc.item_details id on id.item_id = pp.item_id\n", "    LEFT JOIN lake_view_retail.console_merchant m on m.id = i.vendor_id\n", "    LEFT JOIN lake_view_vms.vms_vendor v on v.id = i.vendor_id\n", "    LEFT JOIN lake_view_po.purchase_order p on p.po_number = i.po_id\n", "    LEFT JOIN lake_view_po.purchase_order_items po on po.po_id = p.id\n", "    LEFT JOIN lake_view_pos.discrepancy_note d ON d.vendor_invoice_id = i.vendor_invoice_id\n", "    LEFT JOIN lake_view_pos.discrepancy_note_product_detail di ON d.id = di.dn_id_id and di.upc_id = pp.upc\n", "    left join lake_view_pos.discrepancy_reason dr on dr.id = di.reason_code\n", "    GROUP BY 1,2,3,4,5,6,7,8,9,15\"\"\",\n", "    presto,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3afed0e9-c6e1-4305-aeaf-bad87818f557", "metadata": {}, "outputs": [], "source": ["inb = inbound[\n", "    [\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"vendor_id\",\n", "        \"vendor_name\",\n", "        \"grn_type\",\n", "        \"invoice_id\",\n", "        \"po_id\",\n", "        \"po_qty\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"grn_qty\",\n", "        \"grn_value\",\n", "        \"grn_at\",\n", "        \"disc_reason\",\n", "        \"disc_at\",\n", "        \"disc_qty\",\n", "        \"disc_value\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "f0ea8e85-079f-482f-9c06-5daae32bb22e", "metadata": {}, "outputs": [], "source": ["inbound[\"grn_date\"] = pd.to_datetime(inbound[\"grn_at\"]).dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "e58da0dd-1901-4d7e-9f29-a2db3f8d2c46", "metadata": {}, "outputs": [], "source": ["from pandasql import sqldf\n", "\n", "pysqldf = lambda q: sqldf(q, globals())\n", "q = \"\"\"\n", "select facility_id , facility_name , item_id , item_name , \n", "(case when item_id in (10116155,10116154,10116161,10116156) then 'New' else 'Old' end) as coin_type ,\n", "sum(po_qty) as po_qty , sum(grn_qty) as grn_qty , sum(disc_qty) as disc_qty\n", "from inbound\n", "group by 1,2,3,4,5\n", "union all\n", "select facility_id , facility_name ,'#overall' as item_id , '#overall' as item_name , \n", "(case when item_id in (10116155,10116154,10116161,10116156) then 'New' else 'Old' end) as coin_type ,\n", "sum(po_qty) as po_qty , sum(grn_qty) as grn_qty , sum(disc_qty) as disc_qty\n", "from inbound\n", "group by 1,2,3,4,5\n", "union all\n", "select facility_id , facility_name ,'#overall' as item_id , '#overall' as item_name , \n", "'#overall' as coin_type ,\n", "sum(po_qty) as po_qty , sum(grn_qty) as grn_qty , sum(disc_qty) as disc_qty\n", "from inbound\n", "group by 1,2,3,4,5\n", "\"\"\".format()\n", "\n", "q2 = \"\"\"\n", "select facility_id , facility_name , item_id , item_name , \n", "(case when item_id in (10116155,10116154,10116161,10116156) then 'New' else 'Old' end) as coin_type ,\n", "disc_reason , sum(disc_qty)\n", "from inbound\n", "group by 1,2,3,4,5,6\n", "union all\n", "select facility_id , facility_name ,'#overall' as item_id , '#overall' as item_name , \n", "(case when item_id in (10116155,10116154,10116161,10116156) then 'New' else 'Old' end) as coin_type ,\n", "disc_reason , sum(disc_qty)\n", "from inbound\n", "group by 1,2,3,4,5,6\n", "union all\n", "select facility_id , facility_name ,'#overall' as item_id , '#overall' as item_name , \n", "'#overall' as coin_type ,disc_reason , sum(disc_qty)\n", "from inbound\n", "group by 1,2,3,4,5,6\n", "\"\"\".format()\n", "\n", "inb_summ = pysqldf(q)\n", "disc_summ = pysqldf(q2)\n", "\n", "# inb_summ = (\n", "#     inbound.groupby(\n", "#         [\"facility_id\", \"facility_name\", \"item_id\", \"item_name\"]\n", "#     )\n", "#     .agg({\"po_qty\":sum , \"grn_qty\": sum,  \"disc_qty\": sum})\n", "#     .reset_index()\n", "# )\n", "\n", "# disc_summ = (\n", "#     inbound.groupby(\n", "#         [\"facility_id\", \"facility_name\", \"item_id\", \"item_name\" ,\"disc_reason\"]\n", "#     )\n", "#     .agg({\"disc_qty\": sum})\n", "#     .reset_index()\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "e2d6782c-ff49-4649-ae99-738d23d45090", "metadata": {}, "outputs": [], "source": ["to_sheets(inb_summ, \"13A-MB1WzX-Pr0qfkTdFDJOeFEKZFw8D6DG15t8dhPmE\", \"IB(WH) Summary\")\n", "to_sheets(disc_summ, \"13A-MB1WzX-Pr0qfkTdFDJOeFEKZFw8D6DG15t8dhPmE\", \"DISC(WH) Summary\")"]}, {"cell_type": "code", "execution_count": null, "id": "e92cc399-a160-4be0-96d5-061e8bd95161", "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "st = time.time()\n", "outbound = pd.read_sql_query(\n", "    f\"\"\"\n", "                select c.facility_id as sender_facility_id,\n", "                       f.name as sender_facility_name,\n", "                       c1.facility_id as ds_facility_id,\n", "                       f1.name as ds_facility_name,\n", "                       e.sto_id,\n", "                       e.item_id,\n", "                       e.item_name,\n", "                       max(sto_created_at) as sto_created_at,\n", "                       avg(reserved_quantity) as sto_qty,\n", "                       max(picking_completed_at_ist) as picked_at,\n", "                       sum(picked_quantity) as picked_qty,\n", "                       max(sto_invoice_created_at) as billed_at,\n", "                       sum(coalesce(billed_quantity,0)) as billed_qty,\n", "                       max(grn_started_at) as grn_at,\n", "                       sum(coalesce(inwarded_quantity,0)) as grn_qty,\n", "                       max(discrepancy_created_at) as disc_at,\n", "                       sum(coalesce(disc_qty,0)) as disc_qty,\n", "                       sum(coalesce(b2b_return_qty,0)) as b2b_qty\n", "                from metrics.esto_details e\n", "                join lake_retail.console_outlet c on c.id = e.sender_outlet_id and c.business_type_id in (1,12)\n", "                join lake_crates.facility f on f.id = c.facility_id\n", "                join lake_retail.console_outlet c1 on c1.id = e.receiving_outlet_id and c1.business_type_id in (7)\n", "                join lake_crates.facility f1 on f1.id = c1.facility_id\n", "                where item_id in {*coins.item_id.astype(int).unique(),}\n", "                and sto_created_at::date >= current_date-30 \n", "                group by 1,2,3,4,5,6,7\n", "            \"\"\",\n", "    redshift,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c4a21aaf-172e-401c-b8a5-af78b145b50c", "metadata": {}, "outputs": [], "source": ["outbound[\"diff_qty\"] = (\n", "    outbound[\"billed_qty\"] - outbound[\"grn_qty\"] - outbound[\"b2b_qty\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b2e54014-5b5c-4734-8199-9b0a4ac6a1ff", "metadata": {}, "outputs": [], "source": ["outbound[\"sto_date\"] = pd.to_datetime(outbound[\"sto_created_at\"]).dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "eeee1f65-8be5-4270-b7cc-35b8c06a5c7d", "metadata": {}, "outputs": [], "source": ["q3 = \"\"\"\n", "\n", "select sender_facility_id , sender_facility_name , item_id , item_name , \n", "(case when item_id in (10116155,10116154,10116161,10116156) then 'New' else 'Old' end) as coin_type ,\n", "sum(sto_qty) as sto_qty , sum(billed_qty) as billed_qty ,sum(grn_qty) as grn_qty , sum(disc_qty) as disc_qty ,\n", "sum(b2b_qty) as b2b_qty , sum(diff_qty) as diff_qty \n", "from outbound\n", "group by 1,2,3,4,5\n", "union all\n", "select sender_facility_id , sender_facility_name ,'#overall' as item_id , '#overall' as item_name , \n", "(case when item_id in (10116155,10116154,10116161,10116156) then 'New' else 'Old' end) as coin_type ,\n", "sum(sto_qty) as sto_qty , sum(billed_qty) as billed_qty ,sum(grn_qty) as grn_qty, sum(disc_qty) as disc_qty ,\n", "sum(b2b_qty) as b2b_qty , sum(diff_qty) as diff_qty \n", "from outbound\n", "group by 1,2,3,4,5\n", "union all\n", "select sender_facility_id , sender_facility_name ,'#overall' as item_id , '#overall' as item_name , \n", "'#overall' as coin_type ,\n", "sum(sto_qty) as sto_qty , sum(billed_qty) as billed_qty ,sum(grn_qty) as grn_qty, sum(disc_qty) as disc_qty ,\n", "sum(b2b_qty) as b2b_qty , sum(diff_qty) as diff_qty \n", "from outbound\n", "group by 1,2,3,4,5\n", "\"\"\".format()\n", "\n", "out_summ = pysqldf(q3)"]}, {"cell_type": "code", "execution_count": null, "id": "5b62420a-1a63-4710-96fb-0e377d0b4f72", "metadata": {}, "outputs": [], "source": ["to_sheets(out_summ, \"13A-MB1WzX-Pr0qfkTdFDJOeFEKZFw8D6DG15t8dhPmE\", \"OB(WH) Summary\")"]}, {"cell_type": "code", "execution_count": null, "id": "5f174676-d191-4aaf-9763-f7cb147a76f6", "metadata": {}, "outputs": [], "source": ["q4 = \"\"\"\n", "-- picking threshold : 1000\n", "-- putaway threshold : 4000\n", " WITH picking AS\n", "  (SELECT f.id AS facility_id,\n", "          f.name AS facility_name,\n", "          \"date\",\n", "          count(DISTINCT CASE\n", "                             WHEN total_picked_qty < 1000 THEN picker_id\n", "                             ELSE NULL\n", "                         END)*100.0/count(DISTINCT picker_id) AS picker_percentage,\n", "          count(DISTINCT picker_id) AS total_pickers,\n", "          sum(line_items) AS line_items,\n", "          sum(pna_qty) AS pna_qty,\n", "          sum(total_picked_qty)/count(DISTINCT picker_id) AS picker_ipp\n", "   FROM\n", "     (SELECT picker_id,\n", "             outlet_id,\n", "             \"date\",\n", "             sum(line_items) AS line_items,\n", "             sum(pna_qty) AS pna_qty,\n", "             sum(tot_quantity) AS total_picked_qty\n", "      FROM (\n", "              (SELECT assigned_to_name AS picker_id,\n", "                      wpl.outlet_id,\n", "                      cast(s.created_at AS date) AS \"date\",\n", "                      count(wpli.item_id) AS line_items,\n", "                      'bulk_pick_list' AS \"type\",\n", "                      sum(wpli.picked_quantity) AS tot_quantity,\n", "                      sum(CASE\n", "                              WHEN wpli.state IN (3,8,10) THEN (wpli.required_quantity - wpli.picked_quantity)\n", "                              ELSE 0\n", "                          END) pna_qty\n", "               FROM lake_view_warehouse_location.warehouse_pick_list wpl\n", "               JOIN lake_view_warehouse_location.warehouse_pick_list_item wpli ON wpl.id = wpli.pick_list_id\n", "               AND wpli.item_name NOT LIKE '%Blink%'\n", "               JOIN lake_view_warehouse_location.warehouse_wave w ON w.id = wpl.wave_id\n", "               JOIN lake_view_po.sto s ON s.id = w.entity_id\n", "               JOIN lake_view_vms.vms_vendor_city_mapping v ON v.vendor_id = s.source_entity_vendor_id\n", "               AND v.active = 1\n", "               JOIN lake_view_vms.vms_vendor_city_tax_info vt ON vt.vendor_city_id = v.id\n", "               AND vt.active = 1\n", "               AND vt.legal_name NOT LIKE '%BLINK%'\n", "               JOIN lake_view_retail.console_outlet co ON co.id = wpl.outlet_id\n", "               AND co.business_type_id IN (1,\n", "                                           12)\n", "               AND co.active =1\n", "               AND co.device_id != 47\n", "               WHERE wpl.state IN (4,\n", "                                   10)\n", "                 AND wpl.insert_ds_ist >= cast(CURRENT_DATE - interval '31' DAY AS varchar)\n", "                 AND wpli.insert_ds_ist >= cast(CURRENT_DATE - interval '31' DAY AS varchar)\n", "                 AND s.created_at >= cast(CURRENT_DATE - interval '30' DAY AS TIMESTAMP) \n", "               GROUP BY 1,\n", "                        2,\n", "                        3,\n", "                        5)\n", "            UNION ALL\n", "              (SELECT ipl.assigned_to_name AS picker_id,\n", "                      ipl.outlet_id,\n", "                      cast(s.created_at AS date) AS \"date\",\n", "                      count(DISTINCT ipl.item_id) AS line_items,\n", "                      'item_pick_list' AS \"type\",\n", "                      sum(ipli.picked_quantity) AS tot_quantity,\n", "                      sum(CASE\n", "                              WHEN ipli.state IN (3,4,5) THEN (ipli.required_quantity - ipli.picked_quantity)\n", "                              ELSE 0\n", "                          END) pna_qty\n", "               FROM lake_view_warehouse_location.warehouse_item_pick_list ipl\n", "               JOIN lake_view_warehouse_location.warehouse_item_pick_list_line_item ipli ON ipl.id = ipli.item_pick_list_id\n", "               AND ipl.item_name NOT LIKE '%Blink%'\n", "               JOIN lake_view_po.sto s ON s.id = cast(ipli.entity_id AS int)\n", "               JOIN lake_view_vms.vms_vendor_city_mapping v ON v.vendor_id = s.source_entity_vendor_id\n", "               AND v.active = 1\n", "               JOIN lake_view_vms.vms_vendor_city_tax_info vt ON vt.vendor_city_id = v.id\n", "               AND vt.active = 1\n", "               AND vt.legal_name NOT LIKE '%BLINK%'\n", "               JOIN lake_view_retail.console_outlet co ON co.id = ipl.outlet_id\n", "               AND co.business_type_id IN (1,\n", "                                           12)\n", "               AND co.active =1\n", "               AND co.device_id != 47\n", "               WHERE ipl.state = 4\n", "                 AND ipli.insert_ds_ist >= cast(CURRENT_DATE - interval '31' DAY AS varchar)\n", "                 AND s.created_at >= cast(CURRENT_DATE - interval '30' DAY AS TIMESTAMP) \n", "               GROUP BY 1,\n", "                        2,\n", "                        3,\n", "                        5))\n", "      GROUP BY 1,\n", "               2,\n", "               3) main\n", "   JOIN lake_view_retail.console_outlet co ON co.id = main.outlet_id\n", "   AND co.business_type_id IN (1,\n", "                               12)\n", "   AND co.facility_id <> 1383\n", "   JOIN lake_view_crates.facility f ON f.id = co.facility_id\n", "   GROUP BY 1,\n", "            2,\n", "            3\n", "   ORDER BY 1,\n", "            3),\n", "      location_adh AS\n", "  (SELECT f.id AS facility_id,\n", "           f.name AS facility_name,\n", "           cast(pl.created_at AS date) AS \"date\",\n", "           100-(sum((CASE\n", "                         WHEN (plir.location_suggested != plir.location_picked\n", "                               AND plir.location_suggested IS NOT NULL) THEN 1\n", "                         ELSE 0\n", "                     END))*100.0/count(pli.item_id)) percent_location_adh\n", "   FROM\n", "     (SELECT *\n", "      FROM lake_view_warehouse_location.warehouse_pick_list\n", "      WHERE insert_ds_ist >= cast(CURRENT_DATE - interval '31' DAY AS varchar)\n", "        AND \"type\" IN (2,\n", "                       3,\n", "                       4,\n", "                       5,\n", "                       6,\n", "                       7)) pl\n", "   LEFT JOIN lake_warehouse_location.pick_list_log pll ON pll.pick_list_id = pl.id\n", "   AND pll.picklist_state = 4\n", "   AND pll.insert_ds_ist >= cast(CURRENT_DATE - interval '31' DAY AS varchar)\n", "   JOIN lake_view_retail.console_outlet co ON co.id = pl.outlet_id\n", "   AND co.business_type_id IN (1,\n", "                               12)\n", "   LEFT JOIN lake_view_retail.warehouse_facility f ON f.id = co.facility_id\n", "   LEFT JOIN lake_view_warehouse_location.warehouse_pick_list_item PLI ON pl.id = pli.pick_list_id\n", "   AND pli.insert_ds_ist >= cast(CURRENT_DATE - interval '31' DAY AS varchar)\n", "   LEFT JOIN\n", "     (SELECT *\n", "      FROM lake_view_warehouse_location.warehouse_pick_list_item_location_error_log\n", "      WHERE error_type = 1\n", "        AND insert_ds_ist >= cast(CURRENT_DATE - interval '31' DAY AS varchar) ) plir ON plir.item_id = pli.item_id\n", "   AND plir.pick_list_id = pli.pick_list_id\n", "   LEFT JOIN lake_view_warehouse_location.warehouse_storage_location sl ON sl.id = plir.location_picked\n", "   WHERE coalesce(sl.zone_identifier, 'NULL') NOT LIKE '%%pack%%'\n", "   GROUP BY 1,\n", "            2,\n", "            3),\n", "putaway AS\n", "  (SELECT facility_id,\n", "          facility_name,\n", "          putaway_date AS \"date\",\n", "          (CASE\n", "               WHEN count(DISTINCT putter_id) = 0 THEN NULL\n", "               ELSE count(DISTINCT CASE\n", "                                       WHEN putaway_qty < 4000 THEN putter_id\n", "                                       ELSE NULL\n", "                                   END)*100.0/count(DISTINCT putter_id)\n", "           END) AS putter_perc,\n", "          count(DISTINCT putter_id) AS total_putters,\n", "          sum(putaway_qty)/count(DISTINCT putter_id) AS putter_ipp\n", "   FROM\n", "     (SELECT c.facility_id,\n", "             f.name AS facility_name,\n", "             cast(pi.updated_at AS date) AS putaway_date,\n", "             assigned_to_name AS putter_id,\n", "             sum(pil.quantity) AS putaway_qty\n", "      FROM lake_view_warehouse_location.warehouse_put_list p\n", "      JOIN lake_view_warehouse_location.warehouse_put_list_item pi ON pi.putlist_id = p.id\n", "      AND pi.item_name NOT LIKE '%Blink%'\n", "      --   JOIN lake_view_warehouse_location.warehouse_wave w ON w.id = p.wave_id\n", "      --   AND w.wave_type = 1\n", "      JOIN lake_view_warehouse_location.warehouse_put_list_item_location pil ON pil.putlist_item_id = pi.id\n", "      JOIN lake_view_warehouse_location.warehouse_storage_location ws ON ws.id = pil.location_id\n", "      AND ws.zone_identifier NOT LIKE '%PACK%'\n", "      JOIN lake_view_retail.console_outlet c ON c.id = p.outlet_id\n", "      AND c.active = 1\n", "      AND c.business_type_id IN (1,\n", "                                 12)\n", "      JOIN lake_view_crates.facility f ON f.id = c.facility_id\n", "      WHERE p.insert_ds_ist >= cast(CURRENT_DATE - interval '31' DAY AS varchar)\n", "        AND pi.updated_at >= cast(CURRENT_DATE - interval '30' DAY AS TIMESTAMP) \n", "        AND p.creation_type = 1\n", "      GROUP BY 1,\n", "               2,\n", "               3,\n", "               4)\n", "   GROUP BY 1,\n", "            2,\n", "            3\n", "   ORDER BY 3,\n", "            1),\n", "      fillrate AS\n", "  (SELECT c.facility_id,\n", "          f.name AS facility_name,\n", "          i.\"date\",\n", "          sum(i.reserved_qty) AS created_qty,\n", "          sum(CASE\n", "                  WHEN s.created_by NOT IN (14,3511) THEN reserved_qty\n", "                  ELSE NULL\n", "              END) AS manual_qty,\n", "          sum(i.billed_qty) AS billed_qty,\n", "          sum(i.inward_qty) AS inward_qty,\n", "          sum(i.billed_qty)*100.0/sum(i.reserved_qty) AS bill_fillrate,\n", "          sum(i.inward_qty)*100.0/sum(i.reserved_qty) AS grn_fillrate,\n", "          sum(sales) AS sale_value,\n", "          sum(d.box_count) - sum(d.dispatched_box_count) AS pending_boxes,\n", "          sum(d.dispatched_box_count)*100.0/sum(d.box_count) AS dispatch_scan_adherence,\n", "          count(DISTINCT CASE\n", "                             WHEN ((dispatch_at <= (s.dispatch_time + INTERVAL '1' HOUR))) THEN s.id\n", "                             ELSE NULL\n", "                         END)*100.0/count(DISTINCT s.id) AS on_time_dispatch,\n", "          sum(short) AS short_quantity,\n", "          sum(short_val) AS short_value,\n", "          sum(disc_others_qty) AS other_quantity,\n", "          sum(disc_others_value) AS other_value,\n", "          sum(b2b_return_qty) AS b2b_return_qty,\n", "          sum(b2b_bad_return) AS b2b_bad_return,\n", "          sum(b2b_return_value) AS b2b_return_value,\n", "          sum(b2b_bad_return_value) AS b2b_bad_return_value\n", "   FROM\n", "     (SELECT j.sto_id,\n", "             j.outlet_id,\n", "             cast(j.created_at AS date) AS \"date\",\n", "             sum(billed_quantity) AS billed_qty,\n", "             sum(reserved_quantity) AS reserved_qty,\n", "             sum(inward_quantity) AS inward_qty\n", "      FROM lake_view_ims.ims_sto_details j\n", "      JOIN lake_view_ims.ims_sto_item ii ON ii.sto_id = j.sto_id\n", "      WHERE j.created_at >= cast(CURRENT_DATE - interval '30' DAY AS TIMESTAMP) \n", "      GROUP BY 1,\n", "               2,\n", "               3) i\n", "   JOIN lake_view_po.sto s ON s.id = i.sto_id\n", "   JOIN lake_view_retail.console_outlet c ON c.id = i.outlet_id\n", "   AND c.active = 1\n", "   AND c.device_id <> 47\n", "   AND c.business_type_id IN (1,\n", "                              12)\n", "   LEFT JOIN\n", "     (SELECT grofers_order_id,\n", "             sum(actual_sales) AS sales\n", "      FROM lake_view_pos.pos_invoice\n", "      WHERE insert_ds_ist >= cast(CURRENT_DATE - interval '31' DAY AS varchar)\n", "        AND invoice_type_id IN (5,\n", "                                14,\n", "                                16)-- AND pos_timestamp >= cast(CURRENT_DATE - interval '31' DAY AS TIMESTAMP)\n", "\n", "      GROUP BY 1) p ON cast(p.grofers_order_id AS int) = cast(i.sto_id AS int)\n", "   JOIN lake_view_crates.facility f ON f.id = c.facility_id\n", "   AND f.name NOT LIKE '%PC%'\n", "   AND f.name NOT LIKE '%Perishable%'\n", "   LEFT JOIN\n", "     (SELECT sto_id,\n", "             source_outlet_id,\n", "             sum(dispatched_box_count) AS dispatched_box_count,\n", "             max(dl.created_at) AS dispatch_at,\n", "             sum(box_count) AS box_count\n", "      FROM lake_view_warehouse_location.dispatch_indent d\n", "      LEFT JOIN lake_view_warehouse_location.dispatch_indent_box dd ON dd.dispatch_indent_id = d.id\n", "      LEFT JOIN lake_view_warehouse_location.dispatch_indent_box_log dl ON dl.dispatch_indent_box_id = dd.box_id\n", "      AND dl.dispatch_indent_box_state = 2\n", "      WHERE d.created_at >= cast(CURRENT_DATE - interval '30' DAY AS TIMESTAMP) \n", "      GROUP BY 1,\n", "               2) d ON d.sto_id = i.sto_id\n", "   AND d.source_outlet_id = i.outlet_id\n", "   LEFT JOIN\n", "     (SELECT sto_id,\n", "             sender_outlet_id,\n", "             sum(short_qty) AS short,\n", "             sum(short_value) AS short_val,\n", "             sum(dn_others_qty) AS disc_others_qty,\n", "             sum(dn_others_value) AS disc_others_value\n", "      FROM\n", "        (SELECT DISTINCT pi.grofers_order_id AS sto_id,\n", "                         pi.outlet_id AS sender_outlet_id,\n", "                         pi.invoice_id,\n", "                         pp.item_id\n", "         FROM lake_view_pos.pos_invoice pi\n", "         JOIN lake_view_pos.pos_invoice_product_details ipd ON pi.id = ipd.invoice_id\n", "         JOIN lake_view_rpc.product_product pp ON pp.variant_id = ipd.variant_id\n", "         JOIN lake_view_po.sto s ON cast(s.id AS varchar) = pi.grofers_order_id\n", "         WHERE pi.insert_ds_ist >= cast(CURRENT_DATE - interval '31' DAY AS varchar)\n", "           AND ipd.insert_ds_ist >= cast(CURRENT_DATE - interval '31' DAY AS varchar)\n", "           AND s.created_at >= cast(CURRENT_DATE - interval '30' DAY AS TIMESTAMP) \n", "           AND invoice_type_id IN (5,\n", "                                   14,\n", "                                   16)) e\n", "      JOIN\n", "        (SELECT vendor_invoice_id,\n", "                p.item_id,\n", "                sum(CASE\n", "                        WHEN di.reason_code IN (12,13) THEN quantity\n", "                        ELSE 0\n", "                    END) AS short_qty,\n", "                sum(CASE\n", "                        WHEN di.reason_code IN (12,13) THEN di.landing_price*di.quantity\n", "                        ELSE 0\n", "                    END) AS short_value,\n", "                sum(CASE\n", "                        WHEN di.reason_code NOT IN (12,13) THEN quantity\n", "                        ELSE 0\n", "                    END) AS dn_others_qty,\n", "                sum(CASE\n", "                        WHEN di.reason_code NOT IN (12,13) THEN di.landing_price*di.quantity\n", "                        ELSE 0\n", "                    END) AS dn_others_value\n", "         FROM lake_view_pos.discrepancy_note d\n", "         JOIN lake_view_pos.discrepancy_note_product_detail di ON di.dn_id_id = d.id\n", "         JOIN\n", "           (SELECT DISTINCT upc,\n", "                            item_id\n", "            FROM lake_view_rpc.product_product\n", "            WHERE active = 1) p ON p.upc = di.upc_id\n", "         GROUP BY 1,\n", "                  2) dis ON dis.vendor_invoice_id = e.invoice_id\n", "      AND dis.item_id = e.item_id\n", "      GROUP BY 1,\n", "               2) sh ON cast(sh.sto_id AS int) = cast(i.sto_id AS int)\n", "   AND cast(sh.sender_outlet_id AS int) = cast(i.outlet_id AS int)\n", "   LEFT JOIN\n", "     ( SELECT cast(pi2.grofers_order_id AS int) AS sto_id,\n", "              sum(pipd.quantity) AS b2b_return_qty,\n", "              sum(pipd.quantity * pipd.selling_price) b2b_return_value,\n", "              sum(pipd.quantity) - sum(b2b_customer_return) AS b2b_bad_return,\n", "              sum(pipd.quantity * pipd.selling_price) - sum(b2b_customer_return_value) AS b2b_bad_return_value\n", "      FROM lake_view_pos.pos_invoice pi\n", "      INNER JOIN lake_view_pos.pos_invoice pi2 ON pi2.invoice_id=pi.original_invoice_id\n", "      INNER JOIN lake_view_pos.pos_invoice_product_details pipd ON pi.id=pipd.invoice_id\n", "      INNER JOIN lake_view_rpc.product_product pp ON pp.variant_id = pipd.variant_id\n", "      LEFT JOIN\n", "        ( SELECT invoice_id AS ret_invoice_id,\n", "                 item_id,\n", "                 iil.variant_id,\n", "                 sum(CASE\n", "                         WHEN inventory_update_type_id IN (23,35) THEN iil.\"delta\"\n", "                     END) AS b2b_customer_return,\n", "                 sum(CASE\n", "                         WHEN inventory_update_type_id IN (23,35) THEN iil.\"delta\"*iil.transaction_lp\n", "                     END) AS b2b_customer_return_value\n", "         FROM lake_view_ims.ims_inventory_log iil\n", "         JOIN lake_rpc.product_product pp ON iil.variant_id = pp.variant_id\n", "         WHERE iil.insert_ds_ist >= cast(CURRENT_DATE - interval '31' DAY AS varchar)\n", "           AND iil.inventory_update_type_id IN (23,\n", "                                                24,\n", "                                                25,\n", "                                                35,\n", "                                                36,\n", "                                                37,\n", "                                                66,\n", "                                                68,\n", "                                                119,\n", "                                                120)\n", "         GROUP BY 1,\n", "                  2,\n", "                  3) rt ON rt.ret_invoice_id = pi.invoice_id\n", "      AND rt.variant_id = pipd.variant_id\n", "      WHERE pi.invoice_type_id IN (8,\n", "                                   15)\n", "        AND pi2.invoice_type_id IN (5,\n", "                                    14,\n", "                                    16)\n", "        AND pi.insert_ds_ist BETWEEN cast(CURRENT_DATE - interval '31' DAY AS varchar) AND cast(CURRENT_DATE AS varchar)\n", "        AND pi2.insert_ds_ist BETWEEN cast(CURRENT_DATE - interval '31' DAY AS varchar) AND cast(CURRENT_DATE AS varchar)\n", "        AND pipd.insert_ds_ist BETWEEN cast(CURRENT_DATE - interval '31' DAY AS varchar) AND cast(CURRENT_DATE AS varchar)\n", "      GROUP BY 1 ) bb ON bb.sto_id = i.sto_id\n", "   JOIN lake_view_vms.vms_vendor_city_mapping v ON v.vendor_id = s.source_entity_vendor_id\n", "   AND v.active = 1\n", "   JOIN lake_view_vms.vms_vendor_city_tax_info vt ON vt.vendor_city_id = v.id\n", "   AND vt.active = 1\n", "   WHERE vt.legal_name NOT LIKE '%BLINK%'\n", "   GROUP BY 1,\n", "            2,\n", "            3),\n", "            \n", "            \n", "            \n", "            \n", "      dam_pil AS\n", "  (SELECT o.facility_id,\n", "          cast(i.pos_timestamp AS date) AS \"DATE\",\n", "          sum(CASE\n", "                  WHEN i.inventory_update_type_id IN (11, 87)\n", "                       AND j.reason_id <> 14 THEN i.\"delta\"\n", "                  ELSE 0\n", "              END) damaged_quantity,\n", "          sum(CASE\n", "                  WHEN i.inventory_update_type_id IN (11, 87)\n", "                       AND j.reason_id <> 14 THEN i.\"delta\" * i.transaction_lp\n", "                  ELSE 0\n", "              END) damaged_value,\n", "          sum(CASE\n", "                  WHEN i.inventory_update_type_id IN (12,89) THEN i.\"delta\"\n", "                  ELSE 0\n", "              END) expired_qty,\n", "          sum(CASE\n", "                  WHEN i.inventory_update_type_id IN (12,89) THEN i.\"delta\" * i.transaction_lp\n", "                  ELSE 0\n", "              END) expired_value,\n", "          sum(CASE\n", "                  WHEN i.inventory_update_type_id IN (64,88) THEN i.\"delta\"\n", "                  ELSE 0\n", "              END) nte_qty,\n", "          sum(CASE\n", "                  WHEN i.inventory_update_type_id IN (64,88) THEN i.\"delta\" * i.transaction_lp\n", "                  ELSE 0\n", "              END) nte_value,\n", "          sum(CASE\n", "                  WHEN i.inventory_update_type_id IN (14, 26, 44, 45, 46, 47, 48, 74, 118, 121, 122) THEN i.\"delta\"\n", "                  ELSE 0\n", "              END) AS stock_update_positive_qty,\n", "          sum(CASE\n", "                  WHEN i.inventory_update_type_id IN (14, 26, 44, 45, 46, 47, 48, 74, 118, 121, 122) THEN i.\"delta\"*i.transaction_lp\n", "                  ELSE 0\n", "              END) AS stock_update_positive_value,\n", "          sum(CASE\n", "                  WHEN i.inventory_update_type_id IN (15, 27, 39, 40, 41, 42, 43, 73, 117, 129, 130, 131, 132) THEN i.\"delta\"\n", "                  ELSE 0\n", "              END) AS stock_update_negative_qty,\n", "          sum(CASE\n", "                  WHEN i.inventory_update_type_id IN (15, 27, 39, 40, 41, 42, 43, 73, 117, 129, 130, 131, 132) THEN i.\"delta\"*i.transaction_lp\n", "                  ELSE 0\n", "              END) AS stock_update_negative_value\n", "   FROM lake_view_ims.ims_inventory_log i\n", "   LEFT JOIN lake_view_ims.ims_bad_inventory_update_log j ON j.inventory_update_id = i.inventory_update_id\n", "   JOIN lake_view_retail.console_outlet o ON o.id = i.outlet_id\n", "   AND o.active = 1\n", "   AND o.device_id != 47\n", "   AND o.company_type_id != 4\n", "   AND o.business_type_id IN (1,\n", "                              12)\n", "   AND i.inventory_update_type_id IN (11,\n", "                                      87,\n", "                                      88,\n", "                                      89,\n", "                                      12,\n", "                                      64,\n", "                                      14,\n", "                                      26,\n", "                                      44,\n", "                                      45,\n", "                                      46,\n", "                                      47,\n", "                                      48,\n", "                                      74,\n", "                                      118,\n", "                                      121,\n", "                                      15,\n", "                                      27,\n", "                                      39,\n", "                                      40,\n", "                                      41,\n", "                                      42,\n", "                                      43,\n", "                                      73,\n", "                                      117,\n", "                                      129,\n", "                                      130,\n", "                                      131,\n", "                                      132)\n", "   JOIN lake_view_vms.vms_vendor_city_mapping cm ON cm.vendor_id = i.entity_vendor_id\n", "   AND cm.active = 1\n", "   JOIN lake_view_vms.vms_vendor_city_tax_info cti ON cti.vendor_city_id = cm.id\n", "   AND cti.active = 1\n", "   WHERE i.insert_ds_ist >= cast(CURRENT_DATE - interval '31' DAY AS varchar)\n", "     AND i.\"delta\" > 0\n", "     AND i.pos_timestamp >= cast(CURRENT_DATE - interval '30' DAY AS TIMESTAMP) \n", "     AND o.name NOT LIKE 'I%'\n", "     AND cti.legal_name NOT LIKE '%BLINK%'\n", "   GROUP BY 1,\n", "            2),\n", "      rsto AS\n", "  (SELECT r.facility_id,\n", "          cast(i.created_at AS DATE) AS \"DATE\",\n", "          sum(expected_quantity) rsto_expected_quantity,\n", "          sum(billed_quantity) rsto_billed_quantity,\n", "          sum(inward_quantity) rsto_inward_quantity\n", "   FROM lake_view_ims.ims_sto_details i\n", "   JOIN lake_view_ims.ims_sto_item si ON si.sto_id = i.sto_id\n", "   JOIN lake_view_po.sto s ON s.id = i.sto_id\n", "   JOIN lake_view_retail.console_outlet o ON o.id = i.outlet_id\n", "   AND o.business_type_id = 7\n", "   JOIN lake_view_retail.console_outlet r ON r.id = i.merchant_outlet_id\n", "   AND r.business_type_id IN (1,\n", "                              12)\n", "   JOIN lake_view_vms.vms_vendor_city_mapping cm ON cm.vendor_id = s.source_entity_vendor_id\n", "   AND cm.active = 1\n", "   JOIN lake_view_vms.vms_vendor_city_tax_info cti ON cti.vendor_city_id = cm.id\n", "   AND cti.active = 1\n", "   WHERE i.created_at >= cast(CURRENT_DATE - interval '30' DAY AS TIMESTAMP) \n", "     AND cti.legal_name NOT LIKE '%BLINK%'\n", "   GROUP BY 1,\n", "            2)\n", "            \n", "            \n", "SELECT f.facility_id,\n", "       f.facility_name,\n", "       f.\"date\",\n", "       pi.picker_percentage,\n", "       pi.total_pickers,\n", "       picker_ipp,\n", "       pu.putter_perc,\n", "       pu.total_putters,\n", "       putter_ipp,\n", "       created_qty,\n", "       billed_qty,\n", "       inward_qty,\n", "       bill_fillrate,\n", "       grn_fillrate,\n", "       pending_boxes,\n", "       dispatch_scan_adherence,\n", "       short_quantity,\n", "       short_value,\n", "       other_quantity,\n", "       other_value,\n", "       b2b_return_qty,\n", "       b2b_bad_return,\n", "       coalesce(rsto_expected_quantity, 0) rsto_expected_quantity,\n", "       coalesce(rsto_billed_quantity, 0) rsto_billed_quantity,\n", "       coalesce(rsto_inward_quantity, 0) rsto_inward_quantity,\n", "       coalesce(dam.damaged_quantity, 0) damaged_quantity,\n", "       coalesce(dam.expired_qty,0) AS expired_qty,\n", "       coalesce(dam.nte_qty,0) AS nte_qty,\n", "       coalesce(dam.stock_update_positive_qty,0) AS stock_update_positive_qty,\n", "       coalesce(dam.stock_update_negative_qty,0) AS stock_update_negative_qty,\n", "       coalesce(dam.stock_update_negative_qty,0) - coalesce(dam.stock_update_positive_qty,0) AS net_varaince_qty,\n", "       line_items,\n", "       coalesce(manual_qty,0) AS manual_qty,\n", "       coalesce(pna_qty,0) AS pna_qty,\n", "       coalesce(b2b_return_value,0) AS b2b_return_value,\n", "       coalesce(b2b_bad_return_value,0) AS b2b_bad_return_value\n", "FROM fillrate f\n", "LEFT JOIN picking pi ON pi.facility_id = f.facility_id\n", "AND pi.\"date\" = f.\"date\"\n", "LEFT JOIN putaway pu ON pu.facility_id = f.facility_id\n", "AND pu.\"date\" = f.\"date\"\n", "LEFT JOIN dam_pil dam ON dam.facility_id = f.facility_id\n", "AND dam.\"DATE\" = f.\"DATE\"\n", "LEFT JOIN rsto ON rsto.facility_id = f.facility_id\n", "AND rsto.\"DATE\" = f.\"DATE\"\n", "LEFT JOIN location_adh ON location_adh.facility_id = f.facility_id\n", "AND location_adh.\"date\" = f.\"DATE\"\n", "ORDER BY 1,\n", "\"\"\".format()"]}, {"cell_type": "code", "execution_count": null, "id": "003e03ca-dee1-4122-bdda-ab13cf4f0bfb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}