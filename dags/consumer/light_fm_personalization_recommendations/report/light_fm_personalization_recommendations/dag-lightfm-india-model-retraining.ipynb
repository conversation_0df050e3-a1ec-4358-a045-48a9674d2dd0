{"cells": [{"cell_type": "markdown", "id": "901ec157-c98b-4aee-b98c-aabe6c5e65b5", "metadata": {}, "source": ["## Package Installs"]}, {"cell_type": "code", "execution_count": null, "id": "ace380d3-1325-481d-b8f1-d6568f5dcf2b", "metadata": {}, "outputs": [], "source": ["!pip install lightfm"]}, {"cell_type": "markdown", "id": "2a9951eb-2df2-4fa9-bd1e-0f96bbb3c9fd", "metadata": {}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": null, "id": "6061c894-acb9-480c-b576-5380f50a152e", "metadata": {}, "outputs": [], "source": ["import time\n", "import json\n", "import joblib\n", "import pickle\n", "import numpy as np\n", "import pandas as pd\n", "from tqdm import tqdm\n", "import pencilbox as pb\n", "from copy import deepcopy\n", "from collections import Counter\n", "from datetime import datetime, timedelta\n", "from sklearn.preprocessing import MinMaxScaler"]}, {"cell_type": "code", "execution_count": null, "id": "758fb73f-3c5e-4ca3-b0d9-e88074fa5165", "metadata": {}, "outputs": [], "source": ["## wait for 5 seconds to make sure pip install has completed\n", "time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "f5ae8e9e-611e-4af8-85ed-96452c3d6792", "metadata": {}, "outputs": [], "source": ["from lightfm import LightFM\n", "from sklearn.base import clone\n", "from lightfm.data import Dataset\n", "from lightfm.evaluation import auc_score, recall_at_k\n", "from lightfm.cross_validation import random_train_test_split"]}, {"cell_type": "code", "execution_count": null, "id": "5ce277ff-397b-40c0-8a95-607a20774027", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "markdown", "id": "a7d1daf0-d5ea-4e64-8ad8-62e650bf7082", "metadata": {}, "source": ["## Get date till which order data has been used to train the model"]}, {"cell_type": "code", "execution_count": null, "id": "db484571-7ff9-4e84-b651-1700b2681682", "metadata": {}, "outputs": [], "source": ["filename = \"date_model_india_trained.gz\"\n", "s3_bucket = \"prod-dse-projects\"\n", "s3_key = f\"recommendations/a2k/lightfm/india/{filename}\"\n", "pb.from_s3(s3_bucket, s3_key, filename)"]}, {"cell_type": "code", "execution_count": null, "id": "5cb1617b-ce53-4ae8-a766-6e2cdb1e793a", "metadata": {}, "outputs": [], "source": ["model_save_date = joblib.load(filename)\n", "model_save_date"]}, {"cell_type": "markdown", "id": "29c99aed-1979-4442-acd8-3e7abf0b23b7", "metadata": {}, "source": ["## Get today's date"]}, {"cell_type": "code", "execution_count": null, "id": "f194afb3-fd2a-4bd7-8727-241d7dde508f", "metadata": {}, "outputs": [], "source": ["# for dag\n", "today_date = (datetime.now().date() + timedelta(days=1)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "markdown", "id": "1ce4c0ba-6476-416b-a52a-62d3c25d09db", "metadata": {}, "source": ["## Define start and end dates"]}, {"cell_type": "code", "execution_count": null, "id": "1358610a-7ab1-4b13-9604-18a272688346", "metadata": {}, "outputs": [], "source": ["START_DATE = model_save_date\n", "END_DATE = today_date"]}, {"cell_type": "code", "execution_count": null, "id": "0e5fcc60-51f7-4cd9-b1d8-1a77a03c0e4f", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"personalization-notifications\",\n", "    text=f\"Training model on data from {START_DATE} to {END_DATE}\",\n", ")"]}, {"cell_type": "markdown", "id": "1e08e5e8-ca2f-4cb0-ae80-2654b8150b04", "metadata": {}, "source": ["## Rebuilding Inference Table in Redshift"]}, {"cell_type": "code", "execution_count": null, "id": "206766a3-583a-4d88-8999-8ac0d163d584", "metadata": {}, "outputs": [], "source": ["## Rebuilding the table for new inference\n", "kwargs_default = {\n", "    \"schema_name\": \"consumer\",\n", "    \"table_name\": \"product_shelf_ranking\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"customer_key\", \"type\": \"bigint\", \"description\": \"user id\"},\n", "        {\"name\": \"ctx_value\", \"type\": \"super\", \"description\": \"feature store context\"},\n", "        {\"name\": \"end_date\", \"type\": \"varchar(max)\", \"description\": \"end date\"},\n", "        {\"name\": \"city\", \"type\": \"varchar(max)\", \"description\": \"city\"},\n", "    ],\n", "    \"primary_key\": [\"customer_key\"],\n", "    \"sortkey\": [],\n", "    \"load_type\": \"rebuild\",\n", "    \"table_description\": \"Feature store context value\",\n", "    \"force_upsert_without_increment_check\": True,\n", "}\n", "\n", "df_temp = pd.DataFrame(columns=[\"customer_key\", \"ctx_value\", \"end_date\"])\n", "df_temp.loc[0] = [0, {\"morning\": {\"cid\": 0, \"pid\": [0]}}, END_DATE]\n", "df_temp[\"ctx_value\"] = df_temp[\"ctx_value\"].map(lambda x: json.dumps(x))\n", "df_temp[\"city\"] = \"city\"\n", "\n", "pb.to_redshift(df_temp, **kwargs_default)"]}, {"cell_type": "code", "execution_count": null, "id": "4da2774e-098e-4225-b5b4-a9f5fad70d3e", "metadata": {}, "outputs": [], "source": ["q = \"\"\"\n", "ALTER TABLE consumer.product_shelf_ranking ALTER DISTSTYLE EVEN;\n", "\"\"\"\n", "redshift_connection = pb.get_connection(\"redpen\")\n", "redshift_connection.execute(\"commit;\")\n", "redshift_connection.execute(q)"]}, {"cell_type": "code", "execution_count": null, "id": "be90613f-52cb-4d21-8d64-77a5edd9dd51", "metadata": {}, "outputs": [], "source": ["text = f\"Redshift table rebuilt!\"\n", "pb.send_slack_message(channel=\"bl-personalization-notifications\", text=text)"]}, {"cell_type": "markdown", "id": "c37a7ecf-e2f6-4db5-bd79-a0ff52dd6eda", "metadata": {}, "source": ["## Pulling Order data - (S3 Saved Date) to (Today) - [less than Today] - Assuming the dag is triggered around 1 AM"]}, {"cell_type": "code", "execution_count": null, "id": "be39f0b1-f05a-4c64-a82d-9bce875931ee", "metadata": {}, "outputs": [], "source": ["q = f\"\"\"\n", "SELECT\n", "\n", "DIM_CUSTOMER_KEY as CUSTOMER_KEY,\n", "fsod.PRODUCT_ID,\n", "FRONTEND_MERCHANT_ID,\n", "PRODUCT_QUANTITY,\n", "CART_ID,\n", "fsod.CITY_NAME,\n", "\n", "PRODUCT_NAME,\n", "PRODUCT_TYPE,\n", "BRAND_NAME,\n", "L1_CATEGORY,\n", "L0_CATEGORY,\n", "IS_COMBO,\n", "TOTAL_MRP,\n", "CART_CHECKOUT_TS_IST,\n", "\n", "DENSE_RANK() OVER(PARTITION BY DIM_CUSTOMER_KEY ORDER BY CART_CHECKOUT_TS_IST) as CART_RANK\n", "\n", "FROM\n", "    dwh.FACT_SALES_ORDER_ITEM_DETAILS fsod\n", "    JOIN dwh.DIM_PRODUCT dp\n", "    ON fsod.PRODUCT_ID = dp.PRODUCT_ID\n", "    JOIN DWH.DIM_MERCHANT m\n", "    ON m.MERCHANT_ID = fsod.FRONTEND_MERCHANT_ID\n", "\n", "WHERE\n", "CART_CHECKOUT_TS_IST >= '{START_DATE}'\n", "and CART_CHECKOUT_TS_IST < '{END_DATE}'\n", "and dp.IS_CURRENT\n", "and m.IS_CURRENT\n", "and (dp.PRODUCT_TYPE_ID IS NULL\n", "        OR \n", "    dp.PRODUCT_TYPE_ID NOT IN (\n", "            11780,\n", "            -- share deal\n", "            459,\n", "            -- <PERSON><PERSON>\n", "            11778,\n", "            -- Free\n", "            11791,\n", "            -- Grofers Go Gift Items\n", "            11927,\n", "            -- Special Deal\n", "            11929,\n", "            -- VIP Deals\n", "            11930,\n", "            -- <PERSON><PERSON>s\n", "            11932,\n", "            -- Whatsapp Deals\n", "            11936\n", "            -- List Deals\n", "    )\n", ")\n", "\n", "and dp.L0_CATEGORY NOT IN ('Specials', 'Best Value')\n", "and fsod.ORDER_TYPE NOT IN ('InternalForwardOrder', 'InternalReverseOrder', 'DropShippingInternalReverseOrder')\n", "\n", "-- Remove B2B Orders\n", "and m.CITY_NAME not LIKE '%%B2B%%'\n", "and PRODUCT_QUANTITY>0\n", "and UNIT_SELLING_PRICE >1\n", "\n", "ORDER BY CUSTOMER_KEY, CART_RANK asc;\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "5d47d84b-ae59-477e-be74-0624bef7ca1a", "metadata": {}, "outputs": [], "source": ["df_orders = pd.read_sql_query(sql=q, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "3fff93a9-041d-4335-b0c7-14f8f44d0f8e", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"bl-personalization-notifications\",\n", "    text=f\"Data contains: {df_orders.shape[0]} interactions | {df_orders['customer_key'].nunique()} users |{df_orders['city_name'].nunique()} cities | {df_orders['frontend_merchant_id'].nunique()} stores | {df_orders['product_id'].nunique()} products\",\n", ")"]}, {"cell_type": "markdown", "id": "714075e3-6924-4572-9559-443419344621", "metadata": {"tags": []}, "source": ["## Creating User Properties"]}, {"cell_type": "code", "execution_count": null, "id": "94f37c35-9e5b-4fc3-996b-368b6d54fbe5", "metadata": {}, "outputs": [], "source": ["## user_properties\n", "df_users = (\n", "    df_orders.sort_values(by=[\"customer_key\", \"cart_rank\"], ascending=False)\n", "    .dropna(subset=[\"city_name\"])\n", "    .drop_duplicates(subset=[\"customer_key\"])\n", "    .reset_index(drop=True)[\n", "        [\"customer_key\", \"cart_rank\", \"city_name\", \"frontend_merchant_id\"]\n", "    ]\n", "    .copy()\n", ")\n", "df_users.shape"]}, {"cell_type": "markdown", "id": "e869d489-3f52-47a5-922e-7c3b718c96e0", "metadata": {}, "source": ["## Creating Item Properties"]}, {"cell_type": "code", "execution_count": null, "id": "dacbda95-9ced-4b99-aa40-1c52da7f3862", "metadata": {}, "outputs": [], "source": ["q = f\"\"\"\n", "    SELECT\n", "\n", "    fsod.PRODUCT_ID,\n", "    PRODUCT_NAME,\n", "    COALESCE(PRODUCT_TYPE_ID,0) as PRODUCT_TYPE_ID,\n", "    COALESCE(BRAND_ID,0) as BRAND_ID,\n", "    L1_CATEGORY_ID,\n", "    L0_CATEGORY_ID\n", "\n", "    FROM\n", "        dwh.FACT_SALES_ORDER_ITEM_DETAILS fsod\n", "        JOIN dwh.DIM_PRODUCT dp\n", "        ON fsod.PRODUCT_ID = dp.PRODUCT_ID\n", "        JOIN DWH.DIM_MERCHANT m\n", "        ON m.MERCHANT_ID = fsod.FRONTEND_MERCHANT_ID\n", "\n", "    WHERE\n", "    CART_CHECKOUT_TS_IST >= '2022-01-01'\n", "    and CART_CHECKOUT_TS_IST < '{END_DATE}'\n", "    and dp.IS_CURRENT\n", "    and m.IS_CURRENT\n", "    and (PRODUCT_TYPE_ID = 0\n", "            OR \n", "         PRODUCT_TYPE_ID NOT IN (\n", "                11780,\n", "                -- share deal\n", "                459,\n", "                -- <PERSON><PERSON>\n", "                11778,\n", "                -- Free\n", "                11791,\n", "                -- Grofers Go Gift Items\n", "                11927,\n", "                -- Special Deal\n", "                11929,\n", "                -- VIP Deals\n", "                11930,\n", "                -- <PERSON><PERSON>s\n", "                11932,\n", "                -- Whatsapp Deals\n", "                11936\n", "                -- List Deals\n", "        )\n", "    )\n", "\n", "    and dp.L0_CATEGORY NOT IN ('Specials', 'Best Value')\n", "    and fsod.ORDER_TYPE NOT IN ('InternalForwardOrder', 'InternalReverseOrder', 'DropShippingInternalReverseOrder')\n", "\n", "    -- Remove B2B Orders\n", "    and m.CITY_NAME not LIKE '%%B2B%%'\n", "    and PRODUCT_QUANTITY>0\n", "    and UNIT_SELLING_PRICE >1\n", "\n", "    GROUP BY 1,2,3,4,5,6;\n", "    \"\"\"\n", "\n", "df_products = pd.read_sql_query(sql=q, con=con)\n", "df_products.shape"]}, {"cell_type": "markdown", "id": "bb6fe7e6-a0b2-494b-b884-18cc2089e129", "metadata": {"tags": []}, "source": ["## Defining all users and items for retraining the model"]}, {"cell_type": "code", "execution_count": null, "id": "3929d0c0-a540-4b52-8bb5-2c228f270657", "metadata": {}, "outputs": [], "source": ["all_items = df_products[\"product_id\"].unique().tolist()\n", "print(len(all_items))\n", "\n", "all_users = df_orders[\"customer_key\"].unique().tolist()\n", "print(len(all_users))"]}, {"cell_type": "markdown", "id": "46935f13-4d97-47df-9e1e-871152380658", "metadata": {"tags": []}, "source": ["### Creating User Feature List"]}, {"cell_type": "code", "execution_count": null, "id": "73cbb736-db7f-4a72-97ac-a54bf5c0a5b5", "metadata": {}, "outputs": [], "source": ["user_features_list = []"]}, {"cell_type": "code", "execution_count": null, "id": "0febb953-1321-4323-ad74-8e34b3a31f59", "metadata": {}, "outputs": [], "source": ["cols = [\"city_name\", \"frontend_merchant_id\"]\n", "\n", "for col in cols:\n", "\n", "    features = df_users[col].dropna().unique().tolist()\n", "    features = [col + \":\" + str(i) for i in features]\n", "    user_features_list.extend(features)\n", "\n", "len(user_features_list)"]}, {"cell_type": "code", "execution_count": null, "id": "27f5691a-10be-4035-96da-e10a327a05a8", "metadata": {}, "outputs": [], "source": ["df_users[\"features\"] = [[]] * len(df_users)\n", "\n", "cols = [\"city_name\", \"frontend_merchant_id\"]\n", "\n", "for col in cols:\n", "    df_users[\"features\"] = df_users[\"features\"] + df_users[col].apply(\n", "        lambda x: [col + \":\" + str(i) for i in x]\n", "        if isinstance(x, list)\n", "        else [col + \":\" + str(x)]\n", "        if x == x\n", "        else []\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "3165045a-8eaa-4a54-af0f-f9d397c3cabe", "metadata": {}, "outputs": [], "source": ["user_feature_tuple = list(\n", "    zip(df_users[\"customer_key\"], df_users[\"features\"].apply(lambda x: list(set(x))))\n", ")"]}, {"cell_type": "markdown", "id": "77f56b71-2fba-402a-9a6f-664f0011857e", "metadata": {"tags": []}, "source": ["### Creating Product Feature List"]}, {"cell_type": "code", "execution_count": null, "id": "a1af5786-59d1-4986-9393-25e64c66f884", "metadata": {}, "outputs": [], "source": ["product_features_list = []"]}, {"cell_type": "code", "execution_count": null, "id": "e3c05525-d221-45be-b3e3-8b05efba250b", "metadata": {}, "outputs": [], "source": ["cols = [\"product_type_id\", \"brand_id\", \"l1_category_id\", \"l0_category_id\"]\n", "\n", "for col in cols:\n", "\n", "    features = df_products[col].dropna().unique().tolist()\n", "    features = [col + \":\" + str(i) for i in features]\n", "    product_features_list.extend(features)\n", "\n", "len(product_features_list)"]}, {"cell_type": "code", "execution_count": null, "id": "d912e9d7-3a65-453c-84e4-fda99f75dc04", "metadata": {}, "outputs": [], "source": ["df_products[\"features\"] = [[]] * len(df_products)\n", "\n", "cols = [\"product_type_id\", \"brand_id\", \"l1_category_id\", \"l0_category_id\"]\n", "\n", "for col in cols:\n", "    df_products[\"features\"] = df_products[\"features\"] + df_products[col].apply(\n", "        lambda x: [col + \":\" + str(i) for i in x]\n", "        if isinstance(x, list)\n", "        else [col + \":\" + str(x)]\n", "        if x == x\n", "        else []\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "a7485e1c-643b-4d82-be4c-392f15a29c57", "metadata": {}, "outputs": [], "source": ["product_feature_tuple = list(\n", "    zip(\n", "        df_products[\"product_id\"], df_products[\"features\"].apply(lambda x: list(set(x)))\n", "    )\n", ")"]}, {"cell_type": "markdown", "id": "2ffefdb5-9b27-4037-8e63-17f18bba8f7f", "metadata": {}, "source": ["## Load saved model, dataset and features"]}, {"cell_type": "code", "execution_count": null, "id": "2565ae39-c2fc-4d2a-8d19-0a2aa06fc30c", "metadata": {}, "outputs": [], "source": ["filename = \"dataset_india.gz\"\n", "s3_bucket = \"prod-dse-projects\"\n", "s3_key = f\"recommendations/a2k/lightfm/india/{filename}\"\n", "\n", "pb.from_s3(s3_bucket, s3_key, filename)\n", "\n", "dataset = joblib.load(filename)"]}, {"cell_type": "code", "execution_count": null, "id": "f283d106-a363-4c4b-a62e-601df0ccf382", "metadata": {}, "outputs": [], "source": ["class LightFMResizable(LightFM):\n", "    \"\"\"A LightFM that resizes the model to accomodate new users,\n", "    items, and features\"\"\"\n", "\n", "    def fit_partial(\n", "        self,\n", "        interactions,\n", "        user_features=None,\n", "        item_features=None,\n", "        sample_weight=None,\n", "        epochs=1,\n", "        num_threads=1,\n", "        verbose=False,\n", "    ):\n", "        try:\n", "            self._check_initialized()\n", "            self._resize(interactions, user_features, item_features)\n", "        except ValueError:\n", "            # This is the first call so just fit without resizing\n", "            pass\n", "\n", "        super().fit_partial(\n", "            interactions,\n", "            user_features,\n", "            item_features,\n", "            sample_weight,\n", "            epochs,\n", "            num_threads,\n", "            verbose,\n", "        )\n", "\n", "        return self\n", "\n", "    def _resize(self, interactions, user_features=None, item_features=None):\n", "        \"\"\"Resizes the model to accommodate new users/items/features\"\"\"\n", "\n", "        no_components = self.no_components\n", "        no_user_features, no_item_features = interactions.shape  # default\n", "\n", "        if hasattr(user_features, \"shape\"):\n", "            no_user_features = user_features.shape[-1]\n", "        if hasattr(item_features, \"shape\"):\n", "            no_item_features = item_features.shape[-1]\n", "\n", "        if (\n", "            no_user_features == self.user_embeddings.shape[0]\n", "            and no_item_features == self.item_embeddings.shape[0]\n", "        ):\n", "            return self\n", "\n", "        new_model = clone(self)\n", "        new_model._initialize(no_components, no_item_features, no_user_features)\n", "\n", "        # update all attributes from self._check_initialized\n", "        for attr in (\n", "            \"item_embeddings\",\n", "            \"item_embedding_gradients\",\n", "            \"item_embedding_momentum\",\n", "            \"item_biases\",\n", "            \"item_bias_gradients\",\n", "            \"item_bias_momentum\",\n", "            \"user_embeddings\",\n", "            \"user_embedding_gradients\",\n", "            \"user_embedding_momentum\",\n", "            \"user_biases\",\n", "            \"user_bias_gradients\",\n", "            \"user_bias_momentum\",\n", "        ):\n", "            # extend attribute matrices with new rows/cols from\n", "            # freshly initialized model with right shape\n", "            old_array = getattr(self, attr)\n", "            old_slice = [slice(None, i) for i in old_array.shape]\n", "            new_array = getattr(new_model, attr)\n", "            new_array[tuple(old_slice)] = old_array\n", "            setattr(self, attr, new_array)\n", "\n", "        return self"]}, {"cell_type": "code", "execution_count": null, "id": "1aa641c0-7cf9-4ad2-9f45-1c5ef48ff3c1", "metadata": {}, "outputs": [], "source": ["filename = \"lightfm_model_india.pickle\"\n", "s3_bucket = \"prod-dse-projects\"\n", "s3_key = f\"recommendations/a2k/lightfm/india/{filename}\"\n", "pb.from_s3(s3_bucket, s3_key, filename)\n", "\n", "with open(filename, \"rb\") as handle:\n", "    model = pickle.load(handle)\n", "\n", "model"]}, {"cell_type": "markdown", "id": "cb9d7b4c-68da-4e6b-89bb-0ed2ddc0bd88", "metadata": {}, "source": ["## Partial fit dataset on new data"]}, {"cell_type": "code", "execution_count": null, "id": "f4ae696b-2812-473b-8e92-797698cb42b5", "metadata": {}, "outputs": [], "source": ["(\n", "    user_id_map_old,\n", "    user_feature_map_old,\n", "    item_id_map_old,\n", "    item_feature_map_old,\n", ") = dataset.mapping()"]}, {"cell_type": "code", "execution_count": null, "id": "8362df7c-2ea4-48a6-9d9f-78e49c58d57b", "metadata": {}, "outputs": [], "source": ["dataset.fit_partial(\n", "    users=all_users,\n", "    items=all_items,\n", "    item_features=product_features_list,\n", "    user_features=user_features_list,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "55ee0987-f5f0-4b21-9eb2-b419594fb1b2", "metadata": {}, "outputs": [], "source": ["item_features = dataset.build_item_features(product_feature_tuple, normalize=False)\n", "user_features = dataset.build_user_features(user_feature_tuple, normalize=False)"]}, {"cell_type": "code", "execution_count": null, "id": "4605102f-68de-4dc0-ad7a-285ecea4789a", "metadata": {}, "outputs": [], "source": ["user_id_map, user_feature_map, item_id_map, item_feature_map = dataset.mapping()\n", "\n", "reverse_item_id_map = {value: key for key, value in item_id_map.items()}\n", "reverse_user_id_map = {value: key for key, value in user_id_map.items()}"]}, {"cell_type": "code", "execution_count": null, "id": "8a9c34cb-87ec-4fe8-bc6a-2ba697de44a8", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"bl-personalization-notifications\",\n", "    text=f\"Dataset updated: | old: {len(user_id_map_old)} users - {len(user_feature_map_old) - len(user_id_map_old)} user features - {len(item_id_map_old)} items - {len(item_feature_map_old) - len(item_id_map_old)} item features | new: {len(user_feature_map)} users - {len(user_feature_map) - len(user_id_map)} user features - {len(item_id_map)} items - {len(item_feature_map) - len(item_id_map)} item features\",\n", ")"]}, {"cell_type": "markdown", "id": "c9fa797d-9a35-4837-bbe8-f1295bbd133d", "metadata": {}, "source": ["## Re-training"]}, {"cell_type": "code", "execution_count": null, "id": "be148165-fb7f-4d95-86b1-2ddc91293b5c", "metadata": {}, "outputs": [], "source": ["## interactions\n", "## weight = count of orders with a pid\n", "df_interaction = (\n", "    df_orders.groupby([\"customer_key\", \"product_id\"])[\"product_quantity\"]\n", "    .count()\n", "    .reset_index(name=\"weight\")\n", ")\n", "## how many customers buy a particular pid and what's the ratio of that number with a user's buy count of the pid\n", "## provides a signal if the product is very specific to the user and hence should be given more weightage or it's a product most people buy and hence less important\n", "df_interaction[\"count\"] = df_interaction.groupby(\"product_id\")[\n", "    \"customer_key\"\n", "].transform(\"count\")\n", "df_interaction[\"weight_count_ratio\"] = (\n", "    df_interaction[\"weight\"] / df_interaction[\"count\"]\n", ")\n", "\n", "## how many items the user has bought overall and the ratio of that to a particular pid purchase count\n", "df_interaction[\"user_count\"] = df_interaction.groupby(\"customer_key\")[\n", "    \"weight\"\n", "].transform(\"sum\")\n", "df_interaction[\"weight_user_count_ratio\"] = (\n", "    df_interaction[\"weight\"] / df_interaction[\"user_count\"]\n", ")\n", "\n", "## Todo: think about better combining methods\n", "df_interaction[\"combined_weight\"] = (\n", "    df_interaction[\"weight_count_ratio\"] * df_interaction[\"weight_user_count_ratio\"]\n", ")\n", "\n", "## scaling weight to be in 1-10 range\n", "scaler = MinMaxScaler(feature_range=(1, 10))\n", "df_interaction[\"scaled_weight\"] = scaler.fit_transform(\n", "    np.log(df_interaction[\"combined_weight\"]).values.reshape(-1, 1)\n", ")\n", "## keep interactions which are in all_items\n", "df_interaction = df_interaction[\n", "    df_interaction[\"product_id\"].isin(all_items)\n", "].reset_index(drop=True)\n", "df_interaction.shape\n", "\n", "(interactions, weights) = dataset.build_interactions(\n", "    [\n", "        (x[0], x[1], x[2])\n", "        for x in df_interaction[[\"customer_key\", \"product_id\", \"scaled_weight\"]].values\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2a53a58d-cc15-4447-89ce-b4e6c5cdda4d", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"bl-personalization-notifications\", text=f\"TRAINING STARTED...\"\n", ")\n", "\n", "model.fit_partial(\n", "    interactions=interactions,\n", "    sample_weight=weights,\n", "    item_features=item_features,\n", "    user_features=user_features,\n", "    num_threads=16,\n", "    epochs=10,\n", "    verbose=True,\n", ")\n", "\n", "pb.send_slack_message(\n", "    channel=\"bl-personalization-notifications\", text=f\"TRAINING COMPLETED...\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a6e1cef7-5195-4751-90b1-1a1217d9e1fd", "metadata": {}, "outputs": [], "source": ["## Create a backup for model and dataset\n", "for filename in [\"dataset_india.gz\", \"lightfm_model_india.pickle\"]:\n", "\n", "    s3_bucket = \"prod-dse-projects\"\n", "    s3_key = f\"recommendations/a2k/lightfm/india/backup/{filename}\"\n", "    pb.to_s3(filename, s3_bucket, s3_key)"]}, {"cell_type": "code", "execution_count": null, "id": "f8250946-e2c4-4c93-9bb7-6e045a52a177", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"bl-personalization-notifications\",\n", "    text=f\"Backup of model and dataset saved to S3!\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "47ecd3ba-db0f-4295-9eb9-d3c55327962d", "metadata": {}, "outputs": [], "source": ["## saving model\n", "with open(f\"lightfm_model_india.pickle\", \"wb\") as handle:\n", "    pickle.dump(model, handle, protocol=pickle.HIGHEST_PROTOCOL)"]}, {"cell_type": "code", "execution_count": null, "id": "42a07975-1d7f-4a5a-b259-6bc80d462884", "metadata": {}, "outputs": [], "source": ["## saving dataset\n", "joblib.dump(dataset, f\"dataset_india.gz\")"]}, {"cell_type": "code", "execution_count": null, "id": "641a029d-2732-46ad-84bd-0843f0f0c42b", "metadata": {}, "outputs": [], "source": ["## Saving new model and dataset to S3\n", "filename = \"dataset_india.gz\"\n", "s3_bucket = \"prod-dse-projects\"\n", "s3_key = f\"recommendations/a2k/lightfm/india/{filename}\"\n", "pb.to_s3(filename, s3_bucket, s3_key)"]}, {"cell_type": "code", "execution_count": null, "id": "5c0b62b5-0d98-4444-882f-8bd50a85207f", "metadata": {}, "outputs": [], "source": ["filename = \"lightfm_model_india.pickle\"\n", "s3_bucket = \"prod-dse-projects\"\n", "s3_key = f\"recommendations/a2k/lightfm/india/{filename}\"\n", "pb.to_s3(filename, s3_bucket, s3_key)"]}, {"cell_type": "code", "execution_count": null, "id": "a8d1f89f-ae33-46c5-8368-************", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"bl-personalization-notifications\",\n", "    text=f\"{START_DATE} to {END_DATE} data trained and model saved to S3!\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1dfa9a48-5e50-4058-93fe-8d909ecc1c68", "metadata": {}, "outputs": [], "source": ["## Saving date of last order, till when the model has been trained on\n", "save_date = END_DATE\n", "filename = \"date_model_india_trained.gz\"\n", "\n", "joblib.dump(save_date, filename)\n", "\n", "## Pushing to S3\n", "s3_bucket = \"prod-dse-projects\"\n", "s3_key = f\"recommendations/a2k/lightfm/india/{filename}\"\n", "pb.to_s3(filename, s3_bucket, s3_key)"]}, {"cell_type": "code", "execution_count": null, "id": "454e6e81-3b35-4958-81ce-506982e8a804", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"bl-personalization-notifications\",\n", "    text=f\"Model last trained till: {save_date}\",\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}