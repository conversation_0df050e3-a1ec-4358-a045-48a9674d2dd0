{"cells": [{"cell_type": "markdown", "id": "2a9951eb-2df2-4fa9-bd1e-0f96bbb3c9fd", "metadata": {}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": null, "id": "13d197d3-0cfa-4a76-af4d-12e517d55b3e", "metadata": {}, "outputs": [], "source": ["!pip install lightfm"]}, {"cell_type": "code", "execution_count": null, "id": "6061c894-acb9-480c-b576-5380f50a152e", "metadata": {}, "outputs": [], "source": ["import os\n", "import gc\n", "import ast\n", "import json\n", "import time\n", "import joblib\n", "import pickle\n", "import requests\n", "import numpy as np\n", "import pandas as pd\n", "import pencilbox as pb\n", "from copy import deepcopy\n", "from tqdm.notebook import tqdm\n", "from joblib import Parallel, delayed\n", "from datetime import datetime, timedelta\n", "from sklearn.preprocessing import MinMaxScaler"]}, {"cell_type": "code", "execution_count": null, "id": "5ce277ff-397b-40c0-8a95-607a20774027", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "30c25c46-2234-4a02-a260-39143859e560", "metadata": {}, "outputs": [], "source": ["overall_start_time = time.time()"]}, {"cell_type": "markdown", "id": "ea2e248b-a75f-4ec9-8551-d751ea2152d2", "metadata": {}, "source": ["## Get date till which order data has been used to inference the model"]}, {"cell_type": "code", "execution_count": null, "id": "58a39583-d867-4dec-a4f0-0a97019b39ee", "metadata": {}, "outputs": [], "source": ["filename = \"date_model_india_inferenced.gz\"\n", "s3_bucket = \"prod-dse-projects\"\n", "s3_key = f\"recommendations/a2k/lightfm/india/{filename}\"\n", "pb.from_s3(s3_bucket, s3_key, filename)\n", "\n", "model_inferenced_date = joblib.load(filename)\n", "model_inferenced_date"]}, {"cell_type": "markdown", "id": "3585068e-f72b-4878-8e9d-cb94ce21c885", "metadata": {}, "source": ["## Get today's date"]}, {"cell_type": "code", "execution_count": null, "id": "80aababc-c580-4ff1-8922-d3557b8bdd4e", "metadata": {}, "outputs": [], "source": ["# For Dag\n", "today_date = (datetime.now().date() + timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "\n", "today_date"]}, {"cell_type": "markdown", "id": "f6d80b2b-9112-4b45-b4c9-87db6f4a59f6", "metadata": {}, "source": ["## Define start and end dates"]}, {"cell_type": "code", "execution_count": null, "id": "9b2910ab-44be-4bf0-816a-37e13dcd9a11", "metadata": {}, "outputs": [], "source": ["START_DATE = model_inferenced_date\n", "END_DATE = today_date"]}, {"cell_type": "code", "execution_count": null, "id": "ac1e8ca9-daf2-4ca0-ab8b-eca4dd477637", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"bl-personalization-notifications\",\n", "    text=f\"Inferencing model on interactions from {START_DATE} to {END_DATE}\",\n", ")"]}, {"cell_type": "markdown", "id": "31abf858-7879-4de6-8091-a0eaecbe1536", "metadata": {}, "source": ["### All products"]}, {"cell_type": "code", "execution_count": null, "id": "d3156ccf-a3a0-41ae-9684-f808c472d9ad", "metadata": {}, "outputs": [], "source": ["q = \"\"\"\n", "SELECT\n", "\n", "dp.PRODUCT_ID,\n", "\n", "PRODUCT_NAME,\n", "PRODUCT_TYPE,\n", "BRAND_NAME,\n", "COALESCE(PRODUCT_TYPE_ID,0) as PRODUCT_TYPE_ID,\n", "COALESCE(BRAND_ID,0) as BRAND_ID,\n", "L1_CATEGORY_ID,\n", "L0_CATEGORY_ID,\n", "\n", "gp.variant,\n", "\n", "L1_CATEGORY\n", "\n", "FROM\n", "    dwh.DIM_PRODUCT dp\n", "    LEFT JOIN lake_cms.gr_product gp\n", "    ON dp.product_id = gp.id\n", "\n", "WHERE\n", "dp.IS_CURRENT\n", "and dp.IS_PRODUCT_ENABLED\n", "\n", "and (PRODUCT_TYPE_ID = 0\n", "        OR \n", "     PRODUCT_TYPE_ID NOT IN (\n", "            11780,\n", "            -- share deal\n", "            459,\n", "            -- <PERSON><PERSON>\n", "            11778,\n", "            -- Free\n", "            11791,\n", "            -- Grofers Go Gift Items\n", "            11927,\n", "            -- Special Deal\n", "            11929,\n", "            -- VIP Deals\n", "            11930,\n", "            -- <PERSON><PERSON>s\n", "            11932,\n", "            -- Whatsapp Deals\n", "            11936\n", "            -- List Deals\n", "    )\n", ")\n", "\n", "and dp.L0_CATEGORY NOT IN ('Specials', 'Best Value')\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "8a21e574-2020-4054-bc27-ee2363152ddf", "metadata": {}, "outputs": [], "source": ["df_products = pd.read_sql_query(sql=q, con=con)\n", "df_products.shape"]}, {"cell_type": "markdown", "id": "f04b3cb1-9734-4da7-bf90-601bee7c562c", "metadata": {}, "source": ["### df_products edits"]}, {"cell_type": "code", "execution_count": null, "id": "63fc7b8f-d17a-40fc-90eb-f71c92c32a86", "metadata": {}, "outputs": [], "source": ["df_products.loc[\n", "    df_products[\"product_type\"] == \"Manual Brush\", \"product_type\"\n", "] = \"Toothbrush\"\n", "df_products.loc[df_products[\"product_type\"] == \"Chips\", \"product_type\"] = \"Potato Chips\""]}, {"cell_type": "code", "execution_count": null, "id": "5e0c40dd-d14c-4607-bb1b-4458d04f6d01", "metadata": {}, "outputs": [], "source": ["df_products.loc[\n", "    (df_products[\"product_name\"].apply(lambda x: x.lower()).str.contains(\"pack of\"))\n", "    | df_products[\"product_name\"].apply(lambda x: x.lower()).str.contains(\" buy \"),\n", "    \"multi_item_flag\",\n", "] = 1\n", "\n", "df_products[\"multi_item_flag\"].fillna(0, inplace=True)"]}, {"cell_type": "markdown", "id": "ec7decb4-9b62-4b3b-a2e4-6938ac9b6f14", "metadata": {}, "source": ["## L1 Flags"]}, {"cell_type": "code", "execution_count": null, "id": "4b67f44d-09ef-43c7-9214-a5e67bb0016c", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1XK4kZ5QzctepbcMwN-E10Yk9yZ22E3A6oM-NIVOGUYs\"\n", "sheet_name = \"l1_flags\"\n", "df_l1_flags = pb.from_sheets(sheet_id, sheet_name, service_account=\"service_account\")\n", "df_l1_flags.shape"]}, {"cell_type": "code", "execution_count": null, "id": "b54a93f6-7c98-4488-a125-43b9f1cff2b8", "metadata": {}, "outputs": [], "source": ["df_l1_flags[\"l1_category_id\"] = df_l1_flags[\"l1_category_id\"].astype(\"int64\")\n", "\n", "df_l1_flags[\"relevant_only_if_not_bought\"] = df_l1_flags[\n", "    \"relevant_only_if_not_bought\"\n", "].astype(\"int64\")\n", "df_l1_flags[\"relevant_only_if_bought_before\"] = df_l1_flags[\n", "    \"relevant_only_if_bought_before\"\n", "].astype(\"int64\")\n", "\n", "df_l1_flags[\"needs_discovery\"] = df_l1_flags[\"needs_discovery\"].astype(\"int64\")\n", "\n", "df_l1_flags[\"heterogenous_categories\"] = df_l1_flags[\"heterogenous_categories\"].astype(\n", "    \"int64\"\n", ")\n", "df_l1_flags[\"temp_categories_exclude\"] = df_l1_flags[\"temp_categories_exclude\"].astype(\n", "    \"int64\"\n", ")\n", "\n", "df_l1_flags[\"tod_morning_score\"] = df_l1_flags[\"tod_morning_score\"].astype(\"float\")\n", "df_l1_flags[\"tod_evening_score\"] = df_l1_flags[\"tod_evening_score\"].astype(\"float\")\n", "df_l1_flags[\"brand_diversity\"] = df_l1_flags[\"brand_diversity\"].astype(\"int64\")"]}, {"cell_type": "markdown", "id": "65cf4dff-ee3a-4aa9-939a-9655138504fb", "metadata": {}, "source": ["### TOD"]}, {"cell_type": "code", "execution_count": null, "id": "d7560f9b-ec92-4837-a015-487f3f6f55d2", "metadata": {}, "outputs": [], "source": ["tod_dict = {\n", "    \"morning\": dict(df_l1_flags[[\"l1_category_id\", \"tod_morning_score\"]].values),\n", "    \"evening\": dict(df_l1_flags[[\"l1_category_id\", \"tod_evening_score\"]].values),\n", "}"]}, {"cell_type": "markdown", "id": "ada98a9c-5e53-40f1-a701-13c9197193d3", "metadata": {}, "source": ["### Brand Diversity Flag"]}, {"cell_type": "code", "execution_count": null, "id": "6011c926-315a-447a-a74d-c442c5de1b37", "metadata": {}, "outputs": [], "source": ["df_products = df_products.merge(\n", "    df_l1_flags[[\"l1_category_id\", \"brand_diversity\"]], how=\"left\"\n", ")\n", "df_products[\"brand_diversity\"].fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "f78a54b6-ee2c-4f25-b9d3-bfb52ab1bad4", "metadata": {}, "outputs": [], "source": ["df_products.shape"]}, {"cell_type": "markdown", "id": "4ce8f998-8ecc-4849-833c-ad723231c39c", "metadata": {}, "source": ["## Similar L1 Info"]}, {"cell_type": "code", "execution_count": null, "id": "528e2830-f3bd-4e0f-9a63-a69f1a3a7f19", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1XK4kZ5QzctepbcMwN-E10Yk9yZ22E3A6oM-NIVOGUYs\"\n", "sheet_name = \"similar_l1\"\n", "df_similar_l1 = pb.from_sheets(sheet_id, sheet_name, service_account=\"service_account\")\n", "df_similar_l1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "e7781b60-f370-45a9-8b76-4a4d1bd18a74", "metadata": {}, "outputs": [], "source": ["df_similar_l1[\"l1_category_id\"] = df_similar_l1[\"l1_category_id\"].astype(\"int64\")"]}, {"cell_type": "markdown", "id": "c38ed92d-88d8-4cc0-928e-e693cab8cc13", "metadata": {"tags": []}, "source": ["## LightFM dataset"]}, {"cell_type": "code", "execution_count": null, "id": "4fe73b7a-e317-40fd-be7e-33c761d3608a", "metadata": {}, "outputs": [], "source": ["from lightfm import LightFM\n", "from sklearn.base import clone\n", "from lightfm.data import Dataset"]}, {"cell_type": "markdown", "id": "e621b99d-80ed-4d94-a3a1-c7033248cfb1", "metadata": {}, "source": ["## Load Saved Model and Dataset"]}, {"cell_type": "code", "execution_count": null, "id": "d35e8a8b-4cff-4c4d-9269-7ccd94cec974", "metadata": {}, "outputs": [], "source": ["filename = \"dataset_india.gz\"\n", "s3_bucket = \"prod-dse-projects\"\n", "s3_key = f\"recommendations/a2k/lightfm/india/{filename}\"\n", "\n", "pb.from_s3(s3_bucket, s3_key, filename)\n", "\n", "dataset = joblib.load(filename)"]}, {"cell_type": "code", "execution_count": null, "id": "d7ebfaad-ff9e-4c90-ab9c-5ed6d1273a2e", "metadata": {}, "outputs": [], "source": ["user_id_map, user_feature_map, item_id_map, item_feature_map = dataset.mapping()\n", "\n", "reverse_item_id_map = {value: key for key, value in item_id_map.items()}\n", "reverse_user_id_map = {value: key for key, value in user_id_map.items()}"]}, {"cell_type": "code", "execution_count": null, "id": "8aadd42c-41cc-4d4c-935f-179944d57681", "metadata": {}, "outputs": [], "source": ["n_items = dataset.item_features_shape()[0]\n", "n_items"]}, {"cell_type": "code", "execution_count": null, "id": "fdb57503-ec40-4768-bc21-cfb15f9a978f", "metadata": {}, "outputs": [], "source": ["n_users = dataset.user_features_shape()[0]\n", "n_users"]}, {"cell_type": "markdown", "id": "ef53309e-51e0-4c72-99af-56d0afd3de68", "metadata": {}, "source": ["## LightFM Model"]}, {"cell_type": "code", "execution_count": null, "id": "bbcb89ac-9520-4b81-bcbb-a91739e3b508", "metadata": {}, "outputs": [], "source": ["class LightFMResizable(LightFM):\n", "    \"\"\"A LightFM that resizes the model to accomodate new users,\n", "    items, and features\"\"\"\n", "\n", "    def fit_partial(\n", "        self,\n", "        interactions,\n", "        user_features=None,\n", "        item_features=None,\n", "        sample_weight=None,\n", "        epochs=1,\n", "        num_threads=1,\n", "        verbose=False,\n", "    ):\n", "        try:\n", "            self._check_initialized()\n", "            self._resize(interactions, user_features, item_features)\n", "        except ValueError:\n", "            # This is the first call so just fit without resizing\n", "            pass\n", "\n", "        super().fit_partial(\n", "            interactions,\n", "            user_features,\n", "            item_features,\n", "            sample_weight,\n", "            epochs,\n", "            num_threads,\n", "            verbose,\n", "        )\n", "\n", "        return self\n", "\n", "    def _resize(self, interactions, user_features=None, item_features=None):\n", "        \"\"\"Resizes the model to accommodate new users/items/features\"\"\"\n", "\n", "        no_components = self.no_components\n", "        no_user_features, no_item_features = interactions.shape  # default\n", "\n", "        if hasattr(user_features, \"shape\"):\n", "            no_user_features = user_features.shape[-1]\n", "        if hasattr(item_features, \"shape\"):\n", "            no_item_features = item_features.shape[-1]\n", "\n", "        if (\n", "            no_user_features == self.user_embeddings.shape[0]\n", "            and no_item_features == self.item_embeddings.shape[0]\n", "        ):\n", "            return self\n", "\n", "        new_model = clone(self)\n", "        new_model._initialize(no_components, no_item_features, no_user_features)\n", "\n", "        # update all attributes from self._check_initialized\n", "        for attr in (\n", "            \"item_embeddings\",\n", "            \"item_embedding_gradients\",\n", "            \"item_embedding_momentum\",\n", "            \"item_biases\",\n", "            \"item_bias_gradients\",\n", "            \"item_bias_momentum\",\n", "            \"user_embeddings\",\n", "            \"user_embedding_gradients\",\n", "            \"user_embedding_momentum\",\n", "            \"user_biases\",\n", "            \"user_bias_gradients\",\n", "            \"user_bias_momentum\",\n", "        ):\n", "            # extend attribute matrices with new rows/cols from\n", "            # freshly initialized model with right shape\n", "            old_array = getattr(self, attr)\n", "            old_slice = [slice(None, i) for i in old_array.shape]\n", "            new_array = getattr(new_model, attr)\n", "            new_array[tuple(old_slice)] = old_array\n", "            setattr(self, attr, new_array)\n", "\n", "        return self"]}, {"cell_type": "code", "execution_count": null, "id": "d47f2882-1321-4062-889b-041f76114e9e", "metadata": {"tags": []}, "outputs": [], "source": ["filename = \"lightfm_model_india.pickle\"\n", "s3_bucket = \"prod-dse-projects\"\n", "s3_key = f\"recommendations/a2k/lightfm/india/{filename}\"\n", "pb.from_s3(s3_bucket, s3_key, filename)\n", "\n", "with open(filename, \"rb\") as handle:\n", "    model = pickle.load(handle)\n", "\n", "model"]}, {"cell_type": "markdown", "id": "f76e02c3-99a4-4f44-bb5e-38dbbc105cb2", "metadata": {}, "source": ["## Exclusions"]}, {"cell_type": "code", "execution_count": null, "id": "40b167f1-fad1-46b5-96d6-3b6f6cee0b8c", "metadata": {}, "outputs": [], "source": ["EXCLUDED_PTYPES = (\n", "    11780,  # share deal\n", "    459,  # <PERSON><PERSON>\n", "    11778,  # Free\n", "    11791,  # Grofers Go Gift Items\n", "    11927,  # Special Deal\n", "    11929,  # VIP Deals\n", "    11930,  # Click Deals\n", "    11932,  # Whatsapp Deals\n", "    11936,  # List Deals\n", "    10319,  # C<PERSON>ret<PERSON>\n", "    1704,  # <PERSON><PERSON>,\n", "    8868,  # Packaged Water\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ae440d9c-0de3-483b-bd9f-ee19f1413e97", "metadata": {}, "outputs": [], "source": ["pids = np.array([*item_id_map])"]}, {"cell_type": "code", "execution_count": null, "id": "36cab58d-bee7-489b-af6f-7299d51fe4c8", "metadata": {}, "outputs": [], "source": ["df_products = df_products[\n", "    ~df_products[\"product_type_id\"].isin(EXCLUDED_PTYPES)\n", "].reset_index(drop=True)\n", "df_products.shape"]}, {"cell_type": "code", "execution_count": null, "id": "165c7b72-5d7a-4055-b6f5-e328cf3686d9", "metadata": {}, "outputs": [], "source": ["item_features_list = [str(i) for i in item_feature_map.keys()]\n", "\n", "df_products = df_products[\n", "    (df_products[\"product_id\"].isin(item_id_map.keys()))\n", "    & (\n", "        df_products[\"product_type_id\"]\n", "        .apply(lambda x: \"product_type_id:\" + str(x))\n", "        .isin(item_features_list)\n", "    )\n", "    & (\n", "        df_products[\"brand_id\"]\n", "        .apply(lambda x: \"brand_id:\" + str(x))\n", "        .isin(item_features_list)\n", "    )\n", "    & (\n", "        df_products[\"l1_category_id\"]\n", "        .apply(lambda x: \"l1_category_id:\" + str(x))\n", "        .isin(item_features_list)\n", "    )\n", "    & (\n", "        df_products[\"l0_category_id\"]\n", "        .apply(lambda x: \"l0_category_id:\" + str(x))\n", "        .isin(item_features_list)\n", "    )\n", "].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "fc346d8e-0049-4238-9ab9-7d39153b5d16", "metadata": {}, "outputs": [], "source": ["df_products.shape"]}, {"cell_type": "code", "execution_count": null, "id": "d57afdf9-ae04-4ac2-af6d-8a3199ca7cff", "metadata": {}, "outputs": [], "source": ["df_products_backup = df_products.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "e43f2f6d-17d2-4f01-8f27-5ac5fabcacc5", "metadata": {}, "outputs": [], "source": ["df_products_backup.shape"]}, {"cell_type": "markdown", "id": "cf0fa413-5012-49b8-b669-76c7d59ea2bf", "metadata": {}, "source": ["## RECOMMENDATION ALGORITHM"]}, {"cell_type": "code", "execution_count": null, "id": "437f8c63-57a4-4f19-b391-598f7b9db186", "metadata": {}, "outputs": [], "source": ["random_state = datetime.now().date().day\n", "\n", "\n", "def generateReco(\n", "    user_id, tod=\"morning\", n_weight=2, n_thres=6, N=10, NL1=24, rd_ratio=2\n", "):\n", "    \"\"\"\n", "    user_id       : user id\n", "    tod           : time of day | {'morning','afternoon','evening'}\n", "    n_weight      : number of products used to get average score for l1 | default: {10}\n", "    n_thres       : weight of expected_price of an L1, while ranking l1 | default: {6}\n", "    N             : number of pids to keep per L1 | default: {4}\n", "    NL1           : number of L1s to keep | default: {24}\n", "    rd_ratio      : number of relevant L1s for each discovery L1 | default: {2}\n", "    \"\"\"\n", "\n", "    ## Step 0: Get random state\n", "    if tod == \"morning\":\n", "        rs = user_id * random_state * 1\n", "    else:\n", "        rs = user_id * random_state * 2\n", "\n", "    ## Step 1: Get relevance scores from the model\n", "    try:\n", "        scores = model.predict(\n", "            user_id_map[user_id],\n", "            np.arange(n_items),\n", "            item_features=item_features,\n", "            user_features=user_features,\n", "        )\n", "    except:\n", "        return pd.DataFrame()\n", "\n", "    df = pd.DataFrame(scores, columns=[\"score\"])\n", "    df[\"product_id\"] = pids\n", "\n", "    ## Step 3: Get Previosuly Bought information\n", "    try:\n", "        bought_pids = [\n", "            int(p)\n", "            for p in ast.literal_eval(\n", "                df_users[df_users[\"dim_customer_key\"] == user_id][\"products\"].iloc[0]\n", "            )\n", "        ]\n", "    except:\n", "        bought_pids = []\n", "\n", "    df_bought = pd.DataFrame(bought_pids, columns=[\"product_id\"])\n", "    df_bought = df_bought.merge(df_products, how=\"inner\", on=\"product_id\")\n", "\n", "    ## Step 4: Add store and user id information\n", "    df[\"customer_key\"] = user_id\n", "\n", "    ## Step 6: Get product information\n", "    df = df.merge(df_products, how=\"inner\", on=\"product_id\")\n", "\n", "    if len(df) == 0:\n", "        return pd.DataFrame()\n", "\n", "    ## Step 2: scale relevance scores between 0 and 1\n", "    scaler = MinMaxScaler(feature_range=(0, 1))\n", "    df[\"score\"] = scaler.fit_transform(df[\"score\"].values.reshape(-1, 1))\n", "\n", "    ## Step 7: creating flags for bought brand / bought pid / bought L1 / bought ptype\n", "    df.loc[df[\"brand_name\"].isin(df_bought[\"brand_name\"]), \"bought_brand\"] = 1\n", "    df.loc[df[\"brand_name\"] == \"\", \"bought_brand\"] = 0\n", "    df.loc[df[\"l1_category_id\"].isin(df_bought[\"l1_category_id\"]), \"bought_L1\"] = 1\n", "    df.loc[df[\"product_id\"].isin(df_bought[\"product_id\"]), \"bought_pid\"] = 1\n", "    df.loc[df[\"product_type\"].isin(df_bought[\"product_type\"]), \"bought_ptype\"] = 1\n", "    df.fillna(0, inplace=True)\n", "\n", "    df[\"l1pid_score\"] = df[\"score\"]\n", "\n", "    # Step 9: Deduplicate products and keep most relevant one\n", "    df = (\n", "        df.sort_values(\n", "            by=[\"l1_category_id\", \"bought_pid\", \"score\"], ascending=[True, False, False]\n", "        )\n", "        .drop_duplicates(\n", "            subset=[\"l1_category_id\", \"brand_name\", \"product_type\", \"variant\"]\n", "        )\n", "        .reset_index(drop=True)\n", "    )\n", "\n", "    ## Step 10: get priority score of user\n", "    user_priority_score = user_priority_dict.get(user_id, 0)\n", "\n", "    ## Step 11: pid_sorting_score based on user_priority_score\n", "    if user_priority_score in [7, 8]:\n", "        df[\"pid_sorting_score\"] = df[\"price_per_unit\"] * df[\"l1pid_score\"]\n", "\n", "    elif user_priority_score in [6]:\n", "        df[\"pid_sorting_score\"] = df[\"price_per_unit\"] * df[\"l1pid_score\"].map(\n", "            lambda x: x ** 2\n", "        )\n", "\n", "    else:\n", "        df[\"pid_sorting_score\"] = df[\"l1pid_score\"]\n", "\n", "    if len(df) == 0:\n", "        return pd.DataFrame()\n", "\n", "    ## Step 13: l1 ranking\n", "    ## Step 13a: Calculating L1 score through aggregation\n", "    df_l1 = (\n", "        df.groupby([\"l1_category_id\", \"bought_L1\"])[\"score\"]\n", "        .apply(lambda x: np.mean(sorted(x, reverse=True)[:n_weight]))\n", "        .reset_index(name=\"l1_score\")\n", "    )\n", "\n", "    ## Step 13b: Calculating l1_tod_score\n", "    df_l1[\"tod_score\"] = df_l1[\"l1_category_id\"].map(tod_dict[tod]).fillna(0)\n", "    df_l1[\"l1_tod_score\"] = df_l1[\"l1_score\"] * df_l1[\"tod_score\"]\n", "\n", "    df_l1 = df_l1.merge(df_l1_flags, how=\"left\").fillna(0)\n", "\n", "    ## Step 13c: Filter L1s, using L1 flags\n", "    df_l1 = df_l1[\n", "        ~(\n", "            ((df_l1[\"relevant_only_if_not_bought\"] == 1) & (df_l1[\"bought_L1\"] == 1))\n", "            | (\n", "                (df_l1[\"relevant_only_if_bought_before\"] == 1)\n", "                & (df_l1[\"bought_L1\"] == 0)\n", "            )\n", "            | ((df_l1[\"temp_categories_exclude\"] == 1))\n", "        )\n", "    ].copy()\n", "\n", "    ## Step 13d: Removing similar L1s\n", "    df_l1 = df_l1.merge(\n", "        df_similar_l1[[\"l1_category_id\", \"l1_category_group\"]],\n", "        how=\"left\",\n", "        on=\"l1_category_id\",\n", "    )\n", "    df_l1[\"l1_category_group\"] = df_l1.apply(\n", "        lambda row: row[\"l1_category_group\"]\n", "        if row[\"l1_category_group\"] == row[\"l1_category_group\"]\n", "        else row[\"l1_category_id\"],\n", "        axis=1,\n", "    )\n", "\n", "    df_l1 = (\n", "        df_l1.sort_values(by=\"l1_tod_score\", ascending=False)\n", "        .drop_duplicates(subset=[\"l1_category_group\"])\n", "        .reset_index(drop=True)\n", "    )\n", "\n", "    ## Step 13e: Selecting relevant L1s\n", "    n_rel = int(rd_ratio * 24 / (rd_ratio + 1))\n", "    df_l1_relevance = (\n", "        df_l1.head(n_rel)\n", "        .reset_index(drop=True)\n", "        .sort_values(by=\"l1_tod_score\", ascending=False)\n", "    )\n", "\n", "    if len(df_l1_relevance) != n_rel:\n", "        return pd.DataFrame()\n", "\n", "    ## Step 13f: Sampling discovery L1s\n", "    df_l1_discovery = df_l1[\n", "        ~(df_l1[\"l1_category_id\"].isin(df_l1_relevance[\"l1_category_id\"]))\n", "        & ~(df_l1[\"l1_category_group\"].isin(df_l1_relevance[\"l1_category_group\"]))\n", "        & (df_l1[\"needs_discovery\"] == 1)\n", "    ].copy()\n", "\n", "    ## check if sum of l1_tod_score should not be 0\n", "    if df_l1_discovery[\"l1_tod_score\"].sum() != 0:\n", "        df_l1_discovery[\"l1_tod_score\"] = df_l1_discovery[\"l1_tod_score\"].transform(\n", "            lambda x: x / x.sum()\n", "        )\n", "    else:\n", "        df_l1_discovery[\"l1_tod_score\"] = df_l1_discovery[\"l1_score\"].transform(\n", "            lambda x: x / x.sum()\n", "        )\n", "\n", "    if len(df_l1_discovery) < 8:\n", "        return pd.DataFrame()\n", "\n", "    ## Step 13g: In case l1_tod_score becomes 0 for a lot of L1s, which can't create 8 discovery L1s through sampling, then use l1 score instead\n", "    try:\n", "        np.random.seed(rs)\n", "        sampled_L1s = np.random.choice(\n", "            df_l1_discovery[\"l1_category_id\"].tolist(),\n", "            size=24 - n_rel,\n", "            replace=False,\n", "            p=df_l1_discovery[\"l1_tod_score\"].tolist(),\n", "        )\n", "    except:\n", "        np.random.seed(rs)\n", "        df_l1_discovery[\"l1_tod_score\"] = df_l1_discovery[\"l1_score\"].transform(\n", "            lambda x: x / x.sum()\n", "        )\n", "        try:\n", "            sampled_L1s = np.random.choice(\n", "                df_l1_discovery[\"l1_category_id\"].tolist(),\n", "                size=24 - n_rel,\n", "                replace=False,\n", "                p=df_l1_discovery[\"l1_tod_score\"].tolist(),\n", "            )\n", "        except:\n", "            df_l1_discovery = df_l1[\n", "                ~(df_l1[\"l1_category_id\"].isin(df_l1_relevance[\"l1_category_id\"]))\n", "                & ~(\n", "                    df_l1[\"l1_category_group\"].isin(\n", "                        df_l1_relevance[\"l1_category_group\"]\n", "                    )\n", "                )\n", "            ].copy()\n", "\n", "            df_l1_discovery[\"l1_tod_score\"] = df_l1_discovery[\"l1_score\"].transform(\n", "                lambda x: x / x.sum()\n", "            )\n", "\n", "            np.random.seed(rs)\n", "            sampled_L1s = np.random.choice(\n", "                df_l1_discovery[\"l1_category_id\"].tolist(),\n", "                size=24 - n_rel,\n", "                replace=False,\n", "                p=df_l1_discovery[\"l1_tod_score\"].tolist(),\n", "            )\n", "\n", "    df_l1_discovery = (\n", "        df_l1_discovery[df_l1_discovery[\"l1_category_id\"].isin(sampled_L1s)]\n", "        .sort_values(by=\"l1_tod_score\", ascending=False)\n", "        .reset_index(drop=True)\n", "    )\n", "\n", "    rank_discovery = [(i + 1) * (rd_ratio + 1) for i in range(len(df_l1_discovery))]\n", "    rank_relevance = [(i + 1) for i in range(24) if i + 1 not in rank_discovery]\n", "\n", "    df_l1_relevance[\"l1_rank\"] = rank_relevance\n", "    df_l1_discovery[\"l1_rank\"] = rank_discovery\n", "\n", "    df_l1 = pd.concat([df_l1_relevance, df_l1_discovery])\n", "    df_l1 = df_l1.sort_values(by=\"l1_rank\").reset_index(drop=True)\n", "\n", "    df = df.merge(\n", "        df_l1[[\"l1_category_id\", \"l1_rank\"]], how=\"inner\", on=\"l1_category_id\"\n", "    )\n", "\n", "    ## Step 14: Sampling Top 4 brands/ptypes (under the name of L2)\n", "    df[\"l2\"] = df.apply(\n", "        lambda row: row[\"product_type\"]\n", "        if row[\"brand_diversity\"] == 0\n", "        else row[\"brand_name\"],\n", "        axis=1,\n", "    )\n", "    df_l2 = (\n", "        df.groupby([\"l1_category_id\", \"l2\"])[\"score\"]\n", "        .apply(lambda x: np.mean(sorted(x, reverse=True)[:n_weight]))\n", "        .reset_index(name=\"l2_score\")\n", "    )\n", "    df_l2[\"l2_score\"] = df_l2.groupby(\"l1_category_id\")[\"l2_score\"].transform(\n", "        lambda x: x / x.sum()\n", "    )\n", "\n", "    np.random.seed(rs)\n", "    df_l2 = (\n", "        df_l2.groupby(\"l1_category_id\")\n", "        .apply(\n", "            lambda df: np.random.choice(\n", "                df[\"l2\"].tolist(), size=4, replace=False, p=df[\"l2_score\"].tolist()\n", "            )\n", "            if df.shape[0] > 4\n", "            else df[\"l2\"].tolist()\n", "        )\n", "        .reset_index(name=\"l2\")\n", "        .explode(\"l2\")\n", "    )\n", "\n", "    df_l2[\"top4\"] = 1\n", "    df = df.merge(df_l2, how=\"left\", on=[\"l1_category_id\", \"l2\"])\n", "    df[\"top4\"].fillna(0, inplace=True)\n", "\n", "    ## if brand diversity is 1, then we can't use bought brand flag, otherwise, brand diversity won't show up\n", "    df[\"bought_brand\"] = df[\"bought_brand\"] * df[\"brand_diversity\"].map(\n", "        lambda x: 0 if x == 1 else 1\n", "    )\n", "\n", "    ## Step 15: pid ranking - here sorting by score\n", "    df = df.sort_values(\n", "        by=[\"l1_category_id\", \"bought_pid\", \"bought_brand\", \"bought_ptype\", \"score\"],\n", "        ascending=False,\n", "    )\n", "\n", "    ## Step 15a: keeping only one pid that has been bought before, per L1 category (bought before at brand and ptype level, excluding variant)\n", "    df_bought_before = df[\n", "        (df[\"bought_brand\"] == 1) & (df[\"bought_ptype\"] == 1)\n", "    ].drop_duplicates(subset=[\"l1_category_id\"])\n", "    df_bought_before[\"bought_pid\"] = 1\n", "\n", "    df = pd.concat([df_bought_before, df[df[\"bought_pid\"] == 0]])\n", "\n", "    df = df.drop_duplicates(subset=[\"product_id\"])\n", "\n", "    ## Step 15b: reranking - here sorting by pid_sorting_score, which may contain price weightage\n", "    df = df.sort_values(\n", "        by=[\"l1_category_id\", \"bought_pid\", \"pid_sorting_score\"], ascending=False\n", "    )\n", "\n", "    ## Step 15c: ptype / brand ranking of pids\n", "    df[\"ptype_pid_rank\"] = df.groupby([\"l1_category_id\", \"product_type\"]).cumcount() + 1\n", "    df[\"brand_pid_rank\"] = df.groupby([\"l1_category_id\", \"brand_name\"]).cumcount() + 1\n", "    df[\"pid_rank\"] = df.apply(\n", "        lambda row: row[\"ptype_pid_rank\"]\n", "        if row[\"brand_diversity\"] == 0\n", "        else row[\"brand_pid_rank\"],\n", "        axis=1,\n", "    )\n", "\n", "    ## Step 15d: Final Ranking overall\n", "    sort_cols = [\"l1_rank\", \"bought_pid\", \"top4\", \"pid_rank\"]\n", "    sort_rule = [True, False, False, True]\n", "\n", "    df = df.sort_values(by=sort_cols, ascending=sort_rule)\n", "    df[\"pid_rank\"] = df.groupby(\"l1_category_id\").cumcount() + 1\n", "\n", "    ## Step 16: filtering down to 'N' pids per L1 and 'NL1' L1s and selecting final columns\n", "    df_reco = df[(df[\"pid_rank\"] <= N) & (df[\"l1_rank\"] <= NL1)].reset_index(drop=True)\n", "    df_reco = df_reco[\n", "        [\n", "            \"customer_key\",\n", "            \"l1_rank\",\n", "            \"l1_category_id\",\n", "            \"l1_category\",\n", "            \"pid_rank\",\n", "            \"product_id\",\n", "            \"product_name\",\n", "            \"brand_name\",\n", "            \"product_type\",\n", "            \"price\",\n", "            \"score\",\n", "        ]\n", "    ].copy()\n", "\n", "    df_reco[\"tod\"] = tod\n", "\n", "    return df_reco"]}, {"cell_type": "markdown", "id": "4ffe9b2b-a191-4658-a5c2-1a1291893991", "metadata": {}, "source": ["## Inference"]}, {"cell_type": "markdown", "id": "03572ce4-ec7d-4eba-b462-6717631b35dd", "metadata": {}, "source": ["### Getting all users who had an interaction in the defined time window"]}, {"cell_type": "code", "execution_count": null, "id": "915015bd-117e-4beb-b6b1-e9df1c950a1d", "metadata": {"tags": []}, "outputs": [], "source": ["q = f\"\"\"\n", "WITH\n", "\n", "    INTERACTIONS AS (\n", "\n", "        SELECT\n", "            DIM_CUSTOMER_KEY\n", "\n", "        FROM\n", "            dwh.FACT_SALES_ORDER_ITEM_DETAILS fsod\n", "            JOIN dwh.DIM_PRODUCT dp\n", "            ON fsod.PRODUCT_ID = dp.PRODUCT_ID\n", "            JOIN DWH.DIM_MERCHANT m\n", "            ON m.MERCHANT_ID = fsod.FRONTEND_MERCHANT_ID\n", "\n", "        WHERE\n", "        CART_CHECKOUT_TS_IST >= '{START_DATE}'\n", "        and CART_CHECKOUT_TS_IST < '{END_DATE}'\n", "        and dp.IS_CURRENT\n", "        and m.IS_CURRENT\n", "        and (dp.PRODUCT_TYPE_ID IS NULL\n", "                OR \n", "            dp.PRODUCT_TYPE_ID NOT IN (\n", "                    11780,\n", "                    -- share deal\n", "                    459,\n", "                    -- <PERSON><PERSON>\n", "                    11778,\n", "                    -- Free\n", "                    11791,\n", "                    -- Grofers Go Gift Items\n", "                    11927,\n", "                    -- Special Deal\n", "                    11929,\n", "                    -- VIP Deals\n", "                    11930,\n", "                    -- <PERSON><PERSON>s\n", "                    11932,\n", "                    -- Whatsapp Deals\n", "                    11936\n", "                    -- List Deals\n", "            )\n", "        )\n", "\n", "        and dp.L0_CATEGORY NOT IN ('Specials', 'Best Value')\n", "        and fsod.ORDER_TYPE NOT IN ('InternalForwardOrder', 'InternalReverseOrder', 'DropShippingInternalReverseOrder')\n", "\n", "        -- Remove B2B Orders\n", "        and m.CITY_NAME not LIKE '%%B2B%%'\n", "        and PRODUCT_QUANTITY>0\n", "        and UNIT_SELLING_PRICE >1\n", "\n", "        GROUP BY 1\n", ")\n", "\n", "SELECT \n", "    pua.*\n", "\n", "FROM\n", "    INTERACTIONS as i\n", "    INNER JOIN \n", "    consumer.personalisation_user_attributes as pua\n", "    \n", "    ON i.DIM_CUSTOMER_KEY = pua.DIM_CUSTOMER_KEY\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "e0eb7f4c-9466-43a1-81c8-36b14e71d39e", "metadata": {}, "outputs": [], "source": ["df_users_interaction = pd.read_sql(q, con)\n", "df_users_interaction.shape"]}, {"cell_type": "code", "execution_count": null, "id": "3adf6ec7-9342-4f85-93bc-ee65d53d14fd", "metadata": {}, "outputs": [], "source": ["cities = [\n", "    i for i in df_users_interaction[\"max_order_city_name\"].unique().tolist() if i != \"\"\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "446f0d6f-4c07-4517-9bfe-6e8757d00991", "metadata": {"tags": []}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"bl-personalization-notifications\",\n", "    text=f\"Interactions from {START_DATE} to {END_DATE} contain: {df_users_interaction['dim_customer_key'].nunique()} users | {len(cities)} cities | {df_users_interaction['max_order_merchant_id'].nunique()} stores\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9e5c7204-f1a4-48a6-b6f9-d48fb36f2b16", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"consumer\",\n", "    \"table_name\": \"product_shelf_ranking\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"customer_key\", \"type\": \"bigint\", \"description\": \"user id\"},\n", "        {\"name\": \"ctx_value\", \"type\": \"super\", \"description\": \"feature store context\"},\n", "        {\"name\": \"end_date\", \"type\": \"varchar(max)\", \"description\": \"end date\"},\n", "        {\"name\": \"city\", \"type\": \"varchar(max)\", \"description\": \"city\"},\n", "    ],\n", "    \"primary_key\": [\"customer_key\"],\n", "    \"sortkey\": [],\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"Feature store context value\",\n", "    \"force_upsert_without_increment_check\": True,\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "2d980ec9-d433-4672-96e8-1e5bf801492d", "metadata": {"tags": []}, "outputs": [], "source": ["i_city = 1\n", "completed_cities = []\n", "\n", "for city in cities:\n", "\n", "    time_start = time.time()\n", "\n", "    text = f\"Getting data for {city} | {i_city}/{len(cities)}\"\n", "    pb.send_slack_message(channel=\"bl-personalization-notifications\", text=text)\n", "\n", "    i_city += 1\n", "\n", "    ## Get user Info\n", "    df_users = df_users_interaction[\n", "        df_users_interaction[\"max_order_city_name\"] == city\n", "    ].reset_index(drop=True)\n", "    df_users.rename(\n", "        columns={\n", "            \"max_order_city_name\": \"city_name\",\n", "            \"max_order_merchant_id\": \"frontend_merchant_id\",\n", "        },\n", "        inplace=True,\n", "    )\n", "\n", "    all_user_count = df_users.shape[0]\n", "\n", "    df_users = df_users[\n", "        df_users[\"dim_customer_key\"].isin(user_id_map.keys())\n", "    ].reset_index(drop=True)\n", "\n", "    df_users.loc[df_users[\"priority_score\"] == \"\", \"priority_score\"] = \"0\"\n", "    df_users[\"priority_score\"] = df_users[\"priority_score\"].astype(\"float\").map(int)\n", "\n", "    df_users[\"frontend_merchant_id\"] = (\n", "        df_users[\"frontend_merchant_id\"].astype(\"float\").map(int)\n", "    )\n", "\n", "    df_users = df_users[\n", "        df_users[\"frontend_merchant_id\"]\n", "        .apply(lambda x: \"frontend_merchant_id:\" + str(x))\n", "        .isin([str(i) for i in user_feature_map.keys()])\n", "    ].reset_index(drop=True)\n", "\n", "    ## user store dict\n", "    user_store_dict = dict(\n", "        df_users[[\"dim_customer_key\", \"frontend_merchant_id\"]].drop_duplicates().values\n", "    )\n", "\n", "    ## user priority dict\n", "    user_priority_dict = dict(\n", "        df_users[[\"dim_customer_key\", \"priority_score\"]].drop_duplicates().values\n", "    )\n", "\n", "    ## User Feature Tuple\n", "    cols = [\"city_name\", \"frontend_merchant_id\"]\n", "\n", "    df_users[\"features\"] = [[]] * len(df_users)\n", "\n", "    for col in cols:\n", "        df_users[\"features\"] = df_users[\"features\"] + df_users[col].apply(\n", "            lambda x: [col + \":\" + str(i) for i in x]\n", "            if isinstance(x, list)\n", "            else [col + \":\" + str(x)]\n", "            if x == x\n", "            else []\n", "        )\n", "\n", "    user_feature_tuple = list(\n", "        zip(\n", "            df_users[\"dim_customer_key\"],\n", "            df_users[\"features\"].apply(lambda x: list(set(x))),\n", "        )\n", "    )\n", "\n", "    user_features = dataset.build_user_features(user_feature_tuple, normalize=False)\n", "\n", "    ## Price per unit at city level\n", "    q = f\"\"\"\n", "        select pid as product_id, avg(price) as price,avg(price_per_unit) as price_per_unit\n", "        from consumer.pid_unit_price\n", "        where city='{city}'\n", "        group by 1\n", "        \"\"\"\n", "\n", "    df_price = pd.read_sql_query(sql=q, con=con)\n", "\n", "    ## merging price with df_products\n", "    df_products = df_products_backup.copy()\n", "\n", "    df_products = df_products.merge(df_price, how=\"inner\", on=\"product_id\")\n", "\n", "    ## Replacing price per unit = 0, with actual price\n", "    df_products.loc[\n", "        df_products[\"price_per_unit\"] == 0, \"price_per_unit\"\n", "    ] = df_products.loc[df_products[\"price_per_unit\"] == 0, \"price\"]\n", "\n", "    ## item feature tuple\n", "    cols = [\"product_type_id\", \"brand_id\", \"l1_category_id\", \"l0_category_id\"]\n", "\n", "    df_products[\"features\"] = [[]] * len(df_products)\n", "\n", "    cols = [\"product_type_id\", \"brand_id\", \"l1_category_id\", \"l0_category_id\"]\n", "\n", "    for col in cols:\n", "        df_products[\"features\"] = df_products[\"features\"] + df_products[col].apply(\n", "            lambda x: [col + \":\" + str(i) for i in x]\n", "            if isinstance(x, list)\n", "            else [col + \":\" + str(x)]\n", "            if x == x\n", "            else []\n", "        )\n", "\n", "    product_feature_tuple = list(\n", "        zip(\n", "            df_products[\"product_id\"],\n", "            df_products[\"features\"].apply(lambda x: list(set(x))),\n", "        )\n", "    )\n", "\n", "    item_features = dataset.build_item_features(product_feature_tuple, normalize=False)\n", "\n", "    df_products[\"n_pid_l1\"] = df_products.groupby(\"l1_category_id\")[\n", "        \"product_id\"\n", "    ].transform(lambda x: x.nunique())\n", "    df_products = df_products[df_products[\"n_pid_l1\"] >= 6].reset_index(drop=True)\n", "\n", "    df_products = df_products.drop(columns=[\"features\", \"n_pid_l1\"])\n", "\n", "    ## OUT OF STOCK DATA at city level\n", "    store_ids = \",\".join(\n", "        [str(i) for i in df_users[\"frontend_merchant_id\"].unique().tolist()]\n", "    )\n", "\n", "    q = f\"\"\"\n", "    select\n", "        vm.id frontend_id,\n", "        p.id product_id\n", "    from\n", "        lake_cms.gr_merchant_product_mapping fmpm\n", "        join lake_cms.gr_product p on (\n", "            p.id = fmpm.product_id\n", "            and p.enabled_flag = True\n", "        )\n", "        join lake_cms.view_gr_merchant vm on (\n", "            vm.id = fmpm.merchant_id\n", "            and vm.enabled_flag = True\n", "            and vm.virtual_type = 'superstore_merchant'\n", "        )\n", "        join lake_pricing_v3.pricing_domain_product pdp on (\n", "            pdp.retail_outlet_id = vm.id\n", "            AND p.id = pdp.cms_product_id\n", "        )\n", "        join lake_cms.gr_virtual_to_real_merchant_mapping vrmm on vrmm.virtual_merchant_id = fmpm.merchant_id\n", "        left join lake_cms.view_gr_merchant rm on rm.id = vrmm.real_merchant_id\n", "        left join lake_retail.console_outlet_cms_store ocs on ocs.cms_store = rm.id\n", "            and ocs.active = 1\n", "            and ocs.cms_update_active = 1\n", "        inner join lake_logistics.logistics_node ln on ln.external_id=vm.id\n", "            and ln.active\n", "\n", "        left join lake_retail.console_outlet o on o.id = ocs.outlet_id\n", "    where\n", "        (\n", "            fmpm.enabled_flag = True\n", "            and fmpm.inventory_limit > 0\n", "            and pdp.price IS not null\n", "            and frontend_id in ({store_ids})\n", "        )\n", "    group by\n", "        1,\n", "        2\n", "    \"\"\"\n", "\n", "    df_stock = pd.read_sql_query(sql=q, con=con)\n", "\n", "    ## Inference\n", "    user_ids = df_users[\"dim_customer_key\"].unique().tolist()\n", "    total_users = len(user_ids)\n", "\n", "    store_ids = df_users[\"frontend_merchant_id\"].unique().tolist()\n", "\n", "    text = f\"Data ready for {city} with {total_users}/{all_user_count} users, {df_stock['product_id'].nunique()} products and {len(store_ids)} stores!\"\n", "    pb.send_slack_message(channel=\"bl-personalization-notifications\", text=text)\n", "\n", "    user_count = 0\n", "\n", "    df_products_city = df_products.copy()\n", "\n", "    i_store = 1\n", "\n", "    reco_city = pd.DataFrame()\n", "\n", "    for store_id in store_ids:\n", "\n", "        users = df_users[df_users[\"frontend_merchant_id\"] == store_id][\n", "            \"dim_customer_key\"\n", "        ].tolist()\n", "\n", "        df_products = df_products_city.copy()\n", "        df_products = df_products.merge(\n", "            df_stock[df_stock[\"frontend_id\"] == store_id][[\"product_id\"]],\n", "            how=\"inner\",\n", "            on=\"product_id\",\n", "        )\n", "\n", "        df_products[\"n_pid_l1\"] = df_products.groupby(\"l1_category_id\")[\n", "            \"product_id\"\n", "        ].transform(lambda x: x.nunique())\n", "        df_products = df_products[df_products[\"n_pid_l1\"] >= 4].reset_index(drop=True)\n", "\n", "        start = time.time()\n", "\n", "        text = f\"Recommendations starting for {city} | store: {store_id} - {i_store}/{len(store_ids)}\"\n", "        pb.send_slack_message(channel=\"bl-personalization-notifications\", text=text)\n", "\n", "        df_reco = pd.concat(\n", "            Parallel(n_jobs=16, backend=\"multiprocessing\")(\n", "                delayed(generateReco)(\n", "                    user_id=user_id,\n", "                    tod=tod,\n", "                    n_weight=2,\n", "                    n_thres=6,\n", "                    N=10,\n", "                    NL1=24,\n", "                    rd_ratio=2,\n", "                )\n", "                for user_id in users\n", "                for tod in [\"morning\", \"evening\"]\n", "            )\n", "        )\n", "\n", "        end = time.time()\n", "\n", "        time_taken = round(round(end - start) / 60, 1)\n", "\n", "        text = f\"Recommendations ready for {city} | store: {store_id} - {i_store}/{len(store_ids)} | {len(users)} users | Took {time_taken} minutes\"\n", "        pb.send_slack_message(channel=\"bl-personalization-notifications\", text=text)\n", "\n", "        if df_reco.shape[0] != 0:\n", "            df_reco.rename(\n", "                columns={\"tod\": \"tid\", \"l1_category_id\": \"cid\", \"product_id\": \"pid\"},\n", "                inplace=True,\n", "            )\n", "\n", "            df_reco = df_reco.sort_values(\n", "                by=[\"customer_key\", \"tid\", \"l1_rank\", \"pid_rank\"]\n", "            ).reset_index(drop=True)\n", "\n", "            reco = (\n", "                df_reco[[\"customer_key\", \"tid\", \"cid\", \"l1_rank\", \"pid\"]]\n", "                .groupby([\"customer_key\", \"tid\", \"cid\", \"l1_rank\"])[\"pid\"]\n", "                .apply(list)\n", "                .reset_index(name=\"pid\")\n", "            )\n", "            reco[\"cid_pid\"] = json.loads(reco[[\"cid\", \"pid\"]].to_json(orient=\"records\"))\n", "            reco = reco.sort_values(by=[\"customer_key\", \"tid\", \"l1_rank\"]).reset_index(\n", "                drop=True\n", "            )\n", "            reco = (\n", "                reco.groupby([\"customer_key\", \"tid\"])[\"cid_pid\"]\n", "                .apply(list)\n", "                .reset_index()\n", "            )\n", "            reco = (\n", "                reco.groupby(\"customer_key\")\n", "                .apply(lambda df: dict(df[[\"tid\", \"cid_pid\"]].values))\n", "                .reset_index(name=\"ctx_value\")\n", "            )\n", "\n", "            reco[\"ctx_value\"] = reco[\"ctx_value\"].map(lambda x: json.dumps(x))\n", "            reco[\"end_date\"] = END_DATE\n", "            reco[\"city\"] = city\n", "\n", "            reco_city = pd.concat([reco_city, reco], ignore_index=True)\n", "\n", "            user_count += reco.shape[0]\n", "\n", "            del reco\n", "            del df_reco\n", "\n", "            gc.collect()\n", "\n", "        else:\n", "            user_count += 0\n", "\n", "        i_store += 1\n", "\n", "    text = f\"Pushing recommendations to Redshift table for {city} | {reco_city.shape[0]} users\"\n", "    pb.send_slack_message(channel=\"bl-personalization-notifications\", text=text)\n", "\n", "    pb.to_redshift(reco_city, **kwargs)\n", "\n", "    del reco_city\n", "    gc.collect()\n", "\n", "    time_end = time.time()\n", "\n", "    time_taken = round(round(time_end - time_start) / 60)\n", "\n", "    text = f\"Recommendations for {city} generated and added to Redshift table | Took {time_taken} minutes | Recos generated for {user_count}/{total_users} users\"\n", "    pb.send_slack_message(channel=\"bl-personalization-notifications\", text=text)\n", "\n", "    completed_cities.append(city)"]}, {"cell_type": "code", "execution_count": null, "id": "d8c1f639-0005-4622-8c1e-509e6e058335", "metadata": {}, "outputs": [], "source": ["overall_end_time = time.time()"]}, {"cell_type": "code", "execution_count": null, "id": "90f8d17b-b95d-4630-964a-650800cd56af", "metadata": {}, "outputs": [], "source": ["if len(completed_cities) != len(cities):\n", "    pb.send_slack_message(\n", "        channel=\"bl-personalization-notifications\",\n", "        text=f\"Came out of loop at {city} - {store_id}\",\n", "    )\n", "    pb.send_slack_message(\n", "        channel=\"bl-personalization-notifications\",\n", "        text=f\"Completed cities: {completed_cities}\",\n", "    )\n", "\n", "else:\n", "    time_taken = round(round(overall_start_time - overall_end_time) / 60)\n", "    pb.send_slack_message(\n", "        channel=\"bl-personalization-notifications\",\n", "        text=f\"Completed inference in {time_taken} minutes\",\n", "    )\n", "\n", "    pb.send_slack_message(\n", "        channel=\"bl-personalization-notifications\",\n", "        text=f\"Model last inferenced till: {END_DATE}\",\n", "    )\n", "\n", "    ## Saving date of last order, till when the model has been inferenced on\n", "    save_date = END_DATE\n", "    filename = \"date_model_india_inferenced.gz\"\n", "\n", "    joblib.dump(save_date, filename)\n", "\n", "    ## Pushing to S3\n", "    s3_bucket = \"prod-dse-projects\"\n", "    s3_key = f\"recommendations/a2k/lightfm/india/{filename}\"\n", "    pb.to_s3(filename, s3_bucket, s3_key)"]}, {"cell_type": "code", "execution_count": null, "id": "f2272b85-f9b2-4c12-a07c-6e4d154a016c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}