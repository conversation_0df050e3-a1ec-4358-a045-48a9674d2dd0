{"cells": [{"cell_type": "code", "execution_count": null, "id": "df5e699f-493f-46c4-9566-229b335dc1c9", "metadata": {}, "outputs": [], "source": ["### adding unique transacting users as a new column"]}, {"cell_type": "code", "execution_count": null, "id": "69479925-c303-4808-b611-9afd73309872", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime, timedelta\n", "import pytz"]}, {"cell_type": "code", "execution_count": null, "id": "420e4526-f82c-4021-804a-33d7996eb79a", "metadata": {}, "outputs": [], "source": ["con_rs = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "99b4b31e-a036-4134-84ee-c4e5a0f4d6d3", "metadata": {"tags": []}, "outputs": [], "source": ["datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "current_time = str(datetime.now(pytz.timezone(\"Asia/Kolkata\")))\n", "\n", "time_sql = \"\"\"\n", "\n", "WITH cte AS\n", "  (SELECT max(at_ist) dt1\n", "   FROM spectrum.mobile_event_data\n", "   WHERE at_date_ist = CURRENT_DATE),\n", "     x AS\n", "  (SELECT max(cart_checkout_ts_ist) dt2\n", "   FROM dwh.fact_sales_order_details\n", "   WHERE cart_checkout_ts_ist::date = CURRENT_DATE),\n", "     f AS\n", "  (SELECT CASE\n", "              WHEN min(dt2)<min(dt1) THEN dt2\n", "              ELSE dt1\n", "          END min_dt\n", "   FROM x,\n", "        cte\n", "   GROUP BY dt2,\n", "            dt1)\n", "SELECT CASE\n", "           WHEN extract(minutes\n", "                        FROM min_dt)>55 THEN extract(hour\n", "                                                     FROM min_dt)\n", "           ELSE extract(hour\n", "                        FROM min_dt) - 1\n", "       END hr\n", "FROM f\n", "\"\"\"\n", "\n", "time_df = pd.read_sql(sql=time_sql, con=con_rs)\n", "\n", "curr_hour = time_df.values[0][0]  # int(current_time[11:13])\n", "print(curr_hour)  # ,\"time\",time.['hr'])"]}, {"cell_type": "code", "execution_count": null, "id": "167d04cf-9bcd-4bfb-a446-8e4a63385e2a", "metadata": {}, "outputs": [], "source": ["time_df.values[0][0]"]}, {"cell_type": "code", "execution_count": null, "id": "007bab8b-b6c6-47f7-b40e-f7b315192963", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f67085b2-1fb4-427b-b074-738ecef2250d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d72ecb5d-7b60-447b-bf9a-70c599aa60b3", "metadata": {"tags": []}, "outputs": [], "source": ["sql = f\"\"\"\n", "\n", "drop table if exists mobile_events ;\n", "CREATE TEMPORARY TABLE mobile_events AS (\n", "    SELECT\n", "        at_date_ist AS mobile_event_date_ist,\n", "        EXTRACT(HOUR FROM at_ist) AS mobile_event_hour_ist,\n", "        EXTRACT(MINUTE FROM at_ist) AS mobile_event_minute_ist,\n", "        \n", "        LOWER(device_uuid) AS device_uuid,\n", "        merchant.merchant_id,\n", "        merchant.city_name AS city,\n", "        name\n", "\n", "    FROM\n", "        spectrum.mobile_event_data\n", "            AS events\n", "    LEFT JOIN\n", "        dwh.dim_merchant\n", "            AS merchant\n", "            ON merchant.merchant_id =  events.traits__merchant_id\n", "                AND is_current = True \n", "\n", "    WHERE EXTRACT(HOUR FROM at_ist) <= {curr_hour}\n", "    and \n", "        mobile_event_date_ist IN (\n", "             CURRENT_DATE,\n", "            (CURRENT_DATE - INTERVAL '1 DAY')::DATE,\n", "            (CURRENT_DATE - INTERVAL '7 DAYS')::DATE,\n", "            (CURRENT_DATE - INTERVAL '14 DAYS')::DATE,\n", "            (CURRENT_DATE - INTERVAL '21 DAYS')::DATE,            \n", "            (CURRENT_DATE - INTERVAL '28 DAYS')::DATE\n", "        )\n", "        AND name IN (\n", "            'Product Added', 'Cart Viewed', 'Checkout Step Viewed',          \n", "            'App Launch', 'Search Results Viewed', 'Homepage Visit' \n", "        )\n", "\n", "    GROUP BY\n", "        1, 2, 3, 4, 5, 6, 7\n", ");\n", "\n", "WITH\n", "\n", "--\n", "-- Extracts data from fact table for the calculation of\n", "-- GMV, new transacting users\n", "--\n", "fact_data AS (\n", "    SELECT\n", "        LOWER(fact.device_id) AS device_id,\n", "        fact.total_selling_price,\n", "        fact.cart_checkout_ts_ist::DATE AS cart_checkout_date_ist,\n", "        \n", "        DATE_PART('minute', fact.cart_checkout_ts_ist) AS cart_checkout_minute_ist,\n", "        DATE_PART('hour', fact.cart_checkout_ts_ist) AS cart_checkout_hour_ist,\n", "        \n", "        channel,\n", "        merchant.city_name AS city,\n", "        frontend_merchant_id AS merchant_id\n", "        \n", "    FROM \n", "        dwh.fact_sales_order_details\n", "            AS fact\n", "    LEFT JOIN\n", "        dwh.dim_merchant\n", "            AS merchant\n", "            ON merchant.merchant_key = fact.dim_frontend_merchant_key\n", "\n", "    WHERE \n", "        fact.cart_checkout_ts_ist >= CURRENT_DATE - INTERVAL '30 DAYS'\n", "        AND is_internal_order IS FALSE\n", "        \n", "    GROUP BY\n", "        1, 2, 3, 4, 5, 6, 7, 8\n", "),\n", "\n", "transactions AS (\n", "    SELECT \n", "        fact_data.device_id,\n", "        fact_data.total_selling_price,\n", "        fact_data.cart_checkout_date_ist,\n", "        fact_data.cart_checkout_hour_ist,\n", "        fact_data.cart_checkout_minute_ist,\n", "        fact_data.channel,\n", "        fact_data.city,\n", "        fact_data.merchant_id AS merchant_id\n", "        \n", "    FROM \n", "        fact_data\n", "            \n", "    WHERE\n", "        cart_checkout_date_ist IN (\n", "             CURRENT_DATE,\n", "            (CURRENT_DATE - INTERVAL '1 DAY')::DATE,\n", "            (CURRENT_DATE - INTERVAL '7 DAYS')::DATE,\n", "            (CURRENT_DATE - INTERVAL '14 DAYS')::DATE,\n", "            (CURRENT_DATE - INTERVAL '21 DAYS')::DATE,            \n", "            (CURRENT_DATE - INTERVAL '28 DAYS')::DATE\n", "        )\n", "        \n", "    GROUP BY \n", "        1, 2, 3, 4, 5, 6, 7, 8\n", "),\n", "\n", "--\n", "-- Calculates max hour from extracted transaction data\n", "-- for which we have data till 55th min of hour\n", "--\n", "transactions_max_hour AS (\n", "    SELECT\n", "        MAX(cart_checkout_hour_ist) AS max_hour\n", "\n", "    FROM\n", "        transactions\n", "\n", "    WHERE\n", "        cart_checkout_date_ist = CURRENT_DATE\n", "        AND cart_checkout_minute_ist >= 55\n", "),\n", "\n", "--\n", "-- Calculates max hour from extracted events data\n", "-- for which we have data till 55th min of hour\n", "--\n", "mobile_event_max_hour AS (\n", "    SELECT\n", "        MAX(mobile_event_hour_ist) AS max_hour\n", "\n", "    FROM\n", "        mobile_events\n", "\n", "    WHERE\n", "        mobile_event_date_ist = CURRENT_DATE\n", "        and mobile_event_minute_ist >= 55\n", "),\n", "\n", "--\n", "-- Calculates minimum of the hours extracted from \n", "-- transactions_max_hour and mobile_event_max_hour\n", "--\n", "latest_hour_transactions_mobile_events AS (\n", "    SELECT \n", "        LEAST(\n", "            transactions_max_hour.max_hour,\n", "            mobile_event_max_hour.max_hour\n", "        ) AS max_hour\n", "            \n", "    FROM\n", "        transactions_max_hour\n", "    CROSS JOIN\n", "        mobile_event_max_hour\n", "),\n", "\n", "\n", "base1 as (\n", "SELECT\n", "        mobile_event_date_ist AS snapshot_date_ist,\n", "        cast(mobile_events.city as varchar) AS city,\n", "        cast(mobile_events.merchant_id as varchar) merchant_id,\n", "        'Today So Far' hour_,\n", "        MAX(mobile_event_hour_ist) AS snapshot_hour_ist,\n", "        COUNT(DISTINCT mobile_events.device_uuid) AS daus,\n", "        (\n", "            COUNT(DISTINCT transactions.device_id) * 100.0 \n", "            / \n", "            NULLIF(COUNT(DISTINCT mobile_events.device_uuid),0) \n", "        ) AS conversion,\n", "        COUNT(distinct case when name = 'Product Added' then mobile_events.device_uuid end) AS atc\n", "    from\n", "        mobile_events\n", "    LEFT JOIN\n", "        transactions\n", "            ON LOWER(mobile_events.device_uuid) = LOWER(transactions.device_id)\n", "                AND mobile_events.city = transactions.city\n", "                AND mobile_events.mobile_event_date_ist = transactions.cart_checkout_date_ist\n", "                AND mobile_events.merchant_id = transactions.merchant_id\n", "                AND transactions.cart_checkout_hour_ist >= mobile_events.mobile_event_hour_ist\n", "                -- Filter out data for previous days where hour is greater than max hour data for today.\n", "                AND transactions.cart_checkout_hour_ist <= (\n", "                    SELECT max_hour FROM latest_hour_transactions_mobile_events\n", "                )\n", "                -- fact table has both mobile and web users whereas mobile events has only mobile users\n", "                -- this removes web users from fact to keep data comparable\n", "                -- which is required to calculate conversion\n", "                AND transactions.channel <> 'consumer_web'\n", "    WHERE\n", "        mobile_events.city IS NOT NULL\n", "        -- Filter out data for previous days where hour is greater than max hour data for today.\n", "        AND mobile_events.mobile_event_hour_ist <= (\n", "            SELECT max_hour FROM latest_hour_transactions_mobile_events\n", "        )\n", "    GROUP BY\n", "        1, 2, 3\n", "),\n", "\n", "base2 as (\n", "SELECT\n", "        mobile_event_date_ist AS snapshot_date_ist,\n", "        mobile_events.city AS city,\n", "        cast(mobile_events.merchant_id as varchar) merchant_id,\n", "        cast(mobile_event_hour_ist as varchar) hour_,\n", "        MAX(mobile_event_hour_ist) AS snapshot_hour_ist,\n", "        COUNT(DISTINCT mobile_events.device_uuid) AS daus,\n", "        (\n", "            COUNT(DISTINCT transactions.device_id) * 100.0 \n", "            / \n", "            NULLIF(COUNT(DISTINCT mobile_events.device_uuid),0) \n", "        ) AS conversion,\n", "        COUNT(distinct case when name = 'Product Added' then mobile_events.device_uuid end) AS atc\n", "    from\n", "        mobile_events\n", "    LEFT JOIN\n", "        transactions\n", "            ON LOWER(mobile_events.device_uuid) = LOWER(transactions.device_id)\n", "                AND mobile_events.city = transactions.city\n", "                AND mobile_events.mobile_event_date_ist = transactions.cart_checkout_date_ist\n", "                AND mobile_events.merchant_id = transactions.merchant_id\n", "                AND transactions.cart_checkout_hour_ist >= mobile_events.mobile_event_hour_ist\n", "                -- Filter out data for previous days where hour is greater than max hour data for today.\n", "                AND transactions.cart_checkout_hour_ist <= (\n", "                    SELECT max_hour FROM latest_hour_transactions_mobile_events\n", "                )\n", "                -- fact table has both mobile and web users whereas mobile events has only mobile users\n", "                -- this removes web users from fact to keep data comparable\n", "                -- which is required to calculate conversion\n", "                AND transactions.channel <> 'consumer_web'\n", "    WHERE\n", "        mobile_events.city IS NOT NULL\n", "        -- Filter out data for previous days where hour is greater than max hour data for today.\n", "        AND mobile_events.mobile_event_hour_ist <= (\n", "            SELECT max_hour FROM latest_hour_transactions_mobile_events\n", "        )\n", "    GROUP BY\n", "        1, 2, 3, 4\n", "),\n", "\n", "base3 as (\n", "SELECT\n", "        mobile_event_date_ist AS snapshot_date_ist,\n", "        'Pan India' AS city,\n", "        '-1' merchant_id,\n", "        'Today So Far'  hour_,\n", "        MAX(mobile_event_hour_ist) AS snapshot_hour_ist,\n", "        COUNT(DISTINCT mobile_events.device_uuid) AS daus,\n", "        (\n", "            COUNT(DISTINCT transactions.device_id) * 100.0 \n", "            / \n", "            NULLIF(COUNT(DISTINCT mobile_events.device_uuid),0) \n", "        ) AS conversion,\n", "        COUNT(distinct case when name = 'Product Added' then mobile_events.device_uuid end) AS atc\n", "    from\n", "        mobile_events\n", "    LEFT JOIN\n", "        transactions\n", "            ON LOWER(mobile_events.device_uuid) = LOWER(transactions.device_id)\n", "                AND mobile_events.city = transactions.city\n", "                AND mobile_events.mobile_event_date_ist = transactions.cart_checkout_date_ist\n", "                AND mobile_events.merchant_id = transactions.merchant_id\n", "                AND transactions.cart_checkout_hour_ist >= mobile_events.mobile_event_hour_ist\n", "                -- Filter out data for previous days where hour is greater than max hour data for today.\n", "                AND transactions.cart_checkout_hour_ist <= (\n", "                    SELECT max_hour FROM latest_hour_transactions_mobile_events\n", "                )\n", "                -- fact table has both mobile and web users whereas mobile events has only mobile users\n", "                -- this removes web users from fact to keep data comparable\n", "                -- which is required to calculate conversion\n", "                AND transactions.channel <> 'consumer_web'\n", "    WHERE\n", "        mobile_events.city IS NOT NULL\n", "        -- Filter out data for previous days where hour is greater than max hour data for today.\n", "        AND mobile_events.mobile_event_hour_ist <= (\n", "            SELECT max_hour FROM latest_hour_transactions_mobile_events\n", "        )\n", "    GROUP BY\n", "        1, 2, 3, 4\n", "),\n", "base4 as (\n", "SELECT\n", "        mobile_event_date_ist AS snapshot_date_ist,\n", "        'Pan India' AS city,\n", "        '-1' merchant_id,\n", "        cast(mobile_event_hour_ist as varchar) hour_,\n", "        MAX(mobile_event_hour_ist) AS snapshot_hour_ist,\n", "        COUNT(DISTINCT mobile_events.device_uuid) AS daus,\n", "        (\n", "            COUNT(DISTINCT transactions.device_id) * 100.0 \n", "            / \n", "            NULLIF(COUNT(DISTINCT mobile_events.device_uuid),0) \n", "        ) AS conversion,\n", "        COUNT(distinct case when name = 'Product Added' then mobile_events.device_uuid end) AS atc\n", "    from\n", "        mobile_events\n", "    LEFT JOIN\n", "        transactions\n", "            ON LOWER(mobile_events.device_uuid) = LOWER(transactions.device_id)\n", "                AND mobile_events.city = transactions.city\n", "                AND mobile_events.mobile_event_date_ist = transactions.cart_checkout_date_ist\n", "                AND mobile_events.merchant_id = transactions.merchant_id\n", "                AND transactions.cart_checkout_hour_ist >= mobile_events.mobile_event_hour_ist\n", "                -- Filter out data for previous days where hour is greater than max hour data for today.\n", "                AND transactions.cart_checkout_hour_ist <= (\n", "                    SELECT max_hour FROM latest_hour_transactions_mobile_events\n", "                )\n", "                -- fact table has both mobile and web users whereas mobile events has only mobile users\n", "                -- this removes web users from fact to keep data comparable\n", "                -- which is required to calculate conversion\n", "                AND transactions.channel <> 'consumer_web'\n", "    WHERE\n", "        mobile_events.city IS NOT NULL\n", "        -- Filter out data for previous days where hour is greater than max hour data for today.\n", "        AND mobile_events.mobile_event_hour_ist <= (\n", "            SELECT max_hour FROM latest_hour_transactions_mobile_events\n", "        )\n", "    GROUP BY\n", "        1, 2, 3,4\n", "),\n", "base5 as (\n", "SELECT\n", "        mobile_event_date_ist AS snapshot_date_ist,\n", "        mobile_events.city AS city,\n", "        '-1' merchant_id,\n", "        'Today So Far' hour_,\n", "        MAX(mobile_event_hour_ist) AS snapshot_hour_ist,\n", "        COUNT(DISTINCT mobile_events.device_uuid) AS daus,\n", "        (\n", "            COUNT(DISTINCT transactions.device_id) * 100.0 \n", "            / \n", "            NULLIF(COUNT(DISTINCT mobile_events.device_uuid),0) \n", "        ) AS conversion,\n", "        COUNT(distinct case when name = 'Product Added' then mobile_events.device_uuid end) AS atc\n", "    from\n", "        mobile_events\n", "    LEFT JOIN\n", "        transactions\n", "            ON LOWER(mobile_events.device_uuid) = LOWER(transactions.device_id)\n", "                AND mobile_events.city = transactions.city\n", "                AND mobile_events.mobile_event_date_ist = transactions.cart_checkout_date_ist\n", "                AND mobile_events.merchant_id = transactions.merchant_id\n", "                AND transactions.cart_checkout_hour_ist >= mobile_events.mobile_event_hour_ist\n", "                -- Filter out data for previous days where hour is greater than max hour data for today.\n", "                AND transactions.cart_checkout_hour_ist <= (\n", "                    SELECT max_hour FROM latest_hour_transactions_mobile_events\n", "                )\n", "                -- fact table has both mobile and web users whereas mobile events has only mobile users\n", "                -- this removes web users from fact to keep data comparable\n", "                -- which is required to calculate conversion\n", "                AND transactions.channel <> 'consumer_web'\n", "    WHERE\n", "        mobile_events.city IS NOT NULL\n", "        -- Filter out data for previous days where hour is greater than max hour data for today.\n", "        AND mobile_events.mobile_event_hour_ist <= (\n", "            SELECT max_hour FROM latest_hour_transactions_mobile_events\n", "        )\n", "    GROUP BY\n", "        1, 2, 3,4\n", "),\n", "base6 as (\n", "SELECT\n", "        mobile_event_date_ist AS snapshot_date_ist,\n", "        mobile_events.city AS city,\n", "        '-1' merchant_id,\n", "        cast(mobile_event_hour_ist as varchar) hour_,\n", "        MAX(mobile_event_hour_ist) AS snapshot_hour_ist,\n", "        COUNT(DISTINCT mobile_events.device_uuid) AS daus,\n", "        (COUNT(DISTINCT transactions.device_id) * 100.0 / \n", "            NULLIF(COUNT(DISTINCT mobile_events.device_uuid),0) \n", "        ) AS conversion,\n", "        COUNT(distinct case when name = 'Product Added' then mobile_events.device_uuid end) AS atc\n", "    from\n", "        mobile_events\n", "    LEFT JOIN\n", "        transactions\n", "            ON LOWER(mobile_events.device_uuid) = LOWER(transactions.device_id)\n", "                AND mobile_events.city = transactions.city\n", "                AND mobile_events.mobile_event_date_ist = transactions.cart_checkout_date_ist\n", "                AND mobile_events.merchant_id = transactions.merchant_id\n", "                AND transactions.cart_checkout_hour_ist >= mobile_events.mobile_event_hour_ist\n", "                -- Filter out data for previous days where hour is greater than max hour data for today.\n", "                AND transactions.cart_checkout_hour_ist <= (\n", "                    SELECT max_hour FROM latest_hour_transactions_mobile_events\n", "                )\n", "                -- fact table has both mobile and web users whereas mobile events has only mobile users\n", "                -- this removes web users from fact to keep data comparable\n", "                -- which is required to calculate conversion\n", "                AND transactions.channel <> 'consumer_web'\n", "    WHERE\n", "        mobile_events.city IS NOT NULL\n", "        -- Filter out data for previous days where hour is greater than max hour data for today.\n", "        AND mobile_events.mobile_event_hour_ist <= (\n", "            SELECT max_hour FROM latest_hour_transactions_mobile_events\n", "        )\n", "    GROUP BY\n", "        1, 2, 3,4\n", "),\n", "\n", "sum_agg as (\n", "Select * from base1\n", "union \n", "Select * from base2\n", "union \n", "Select * from base3\n", "union \n", "Select * from base4\n", "union \n", "Select * from base5\n", "union \n", "Select * from base6\n", "),\n", "\n", "final_agg as (\n", "select\n", "a.snapshot_date_ist date_,\n", "a.city city_name,\n", "a.merchant_id,\n", "a.hour_,\n", "coalesce(a.daus,0) dau_today,\n", "coalesce(b.daus,0) d7_dau,\n", "coalesce(c.daus,0) d14_dau,\n", "coalesce(d.daus,0) d21_dau,\n", "coalesce(e.daus,0) d28_dau,\n", "\n", "coalesce(a.conversion,0) transacting_users_today,\n", "coalesce(b.conversion,0) d7_transacting_users,\n", "coalesce(c.conversion,0) d14_transacting_users,\n", "coalesce(d.conversion,0) d21_transacting_users,\n", "coalesce(e.conversion,0) d28_transacting_users,\n", "\n", "coalesce(a.atc,0)*100.00/ NULLIF(a.daus,0) conversion_today,\n", "coalesce(b.atc,0)*100.00/ NULLIF(b.daus,0) d7_conversion,\n", "coalesce(c.atc,0)*100.00 /NULLIF(c.daus,0) d14_conversion,\n", "coalesce(d.atc,0)*100.00 /NULLIF(d.daus,0) d21_conversion,\n", "coalesce(e.atc,0)*100.00 / NULLIF(e.daus,0) d28_conversion\n", "\n", "from\n", "sum_agg a\n", "left join\n", "sum_agg b on a.hour_ = b.hour_ and a.city = b.city and a.merchant_id = b.merchant_id and a.snapshot_date_ist - b.snapshot_date_ist = 7\n", "left join\n", "sum_agg c on a.hour_ = c.hour_ and a.city = c.city and a.merchant_id = c.merchant_id and a.snapshot_date_ist - c.snapshot_date_ist = 14\n", "left join\n", "sum_agg d on a.hour_ = d.hour_ and a.city = d.city and a.merchant_id = d.merchant_id and a.snapshot_date_ist - d.snapshot_date_ist = 21\n", "left join\n", "sum_agg e on a.hour_ = e.hour_ and a.city = e.city and a.merchant_id = e.merchant_id and a.snapshot_date_ist - e.snapshot_date_ist = 28\n", "where a.snapshot_date_ist = current_date\n", ")\n", "\n", "select * from final_agg\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "be219011-03c9-4ea2-8b20-f1ce85774d40", "metadata": {}, "outputs": [], "source": ["df_dau = pd.read_sql(sql=sql, con=con_rs)\n", "df_dau.head()"]}, {"cell_type": "code", "execution_count": null, "id": "32754270-00fb-49b9-b063-90b08f44e3d0", "metadata": {"tags": []}, "outputs": [], "source": ["sql = \"\"\"\n", "with orders as (\n", "select\n", "datE(cart_checkout_ts_ist) date_,\n", "dm.city_name,\n", "cast(dm.merchant_id as varchar) merchant_id,\n", "cast(extract(hour from order_create_ts_ist) as varchar) hour_,\n", "count(distinct cart_id) orders\n", "from \n", "dwh.fact_sales_order_details a\n", "JOIN dwh.dim_merchant AS dm ON a.dim_frontend_merchant_key = dm.merchant_key\n", "where extract(hour from cart_checkout_ts_ist) <= {}\n", "and datE(order_create_ts_ist) in (current_date-28, current_date-21, current_date-14, current_date-7, current_date)\n", "and order_type in ('RetailForwardOrder', 'DropShippingForwardOrder')\n", "group by 1,2,3,4\n", "),\n", "\n", "-- all details\n", "agg as (\n", "select \n", "date_,\n", "nvl(city_name,'NA') city_name,\n", "merchant_id,\n", "hour_,\n", "sum(orders) orders from orders group by 1,2,3,4),\n", "\n", "-- city and store and full day\n", "agg2 as (\n", "select \n", "date_,\n", "nvl(city_name,'NA') city_name,\n", "merchant_id,\n", "'Today So Far' as hour_,\n", "sum(orders) orders from orders group by 1,2,3),\n", "\n", "-- city and over-1 and hour\n", "agg3 as (\n", "select \n", "date_,\n", "nvl(city_name,'NA') city_name,\n", "'-1' as merchant_id,\n", "hour_,\n", "sum(orders) orders from orders group by 1,2,4),\n", "\n", "-- city and over-1 and full day\n", "agg4 as (\n", "select \n", "date_,\n", "nvl(city_name,'NA') city_name,\n", "'-1' as merchant_id,\n", "'Today So Far' as hour_,\n", "sum(orders) orders from orders group by 1,2),\n", "\n", "-- overall pan india and over-1 and hour\n", "agg5 as (\n", "select \n", "date_,\n", "'Pan India' as city_name,\n", "'-1' as merchant_id,\n", "hour_,\n", "sum(orders) orders from orders group by 1,4),\n", "\n", "-- overall pan india and over-1 and full day\n", "agg6 as (\n", "select \n", "date_,\n", "'Pan India' as city_name,\n", "'-1' as merchant_id,\n", "'Today So Far' as hour_,\n", "sum(orders) orders from orders group by 1),\n", "\n", "\n", "\n", "sum_agg as (\n", "select* from agg\n", "union all\n", "select* from agg2\n", "union all\n", "select* from agg3\n", "union all\n", "select* from agg4\n", "union all\n", "select* from agg5\n", "union all\n", "select* from agg6\n", "),\n", "\n", "final_agg as (\n", "select\n", "a.date_,\n", "a.city_name,\n", "a.merchant_id,\n", "a.hour_,\n", "coalesce(a.orders,0) orders_today,\n", "coalesce(b.orders,0) d7_orders,\n", "coalesce(c.orders,0) d14_orders,\n", "coalesce(d.orders,0) d21_orders,\n", "coalesce(e.orders,0) d28_orders\n", "from\n", "sum_agg a\n", "left join\n", "sum_agg b on a.hour_ = b.hour_ and a.city_name = b.city_name and a.merchant_id = b.merchant_id and a.date_ - b.date_ = 7\n", "left join\n", "sum_agg c on a.hour_ = c.hour_ and a.city_name = c.city_name and a.merchant_id = c.merchant_id and a.date_ - c.date_ = 14\n", "left join\n", "sum_agg d on a.hour_ = d.hour_ and a.city_name = d.city_name and a.merchant_id = d.merchant_id and a.date_ - d.date_ = 21\n", "left join\n", "sum_agg e on a.hour_ = e.hour_ and a.city_name = e.city_name and a.merchant_id = e.merchant_id and a.date_ - e.date_ = 28\n", "where a.date_ = current_date\n", ")\n", "\n", "select *from final_agg\n", "\"\"\".format(\n", "    curr_hour\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e42e8282-acb5-4b2c-9db0-359594a34efb", "metadata": {}, "outputs": [], "source": ["df_orders = pd.read_sql(sql=sql, con=con_rs)\n", "df_orders.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3a183acb-6635-4fc0-94db-efec3ec8d44a", "metadata": {}, "outputs": [], "source": ["df_orders[(df_orders[\"merchant_id\"] == \"-1\") & (df_orders[\"hour_\"] == \"12\")]"]}, {"cell_type": "code", "execution_count": null, "id": "20090f68-f92d-4266-85fa-8d54942c0292", "metadata": {}, "outputs": [], "source": ["df_orders[df_orders.city_name == \"Pan India\"]"]}, {"cell_type": "code", "execution_count": null, "id": "0f3eff4c-76c2-4d7b-96d1-4721e16fc328", "metadata": {"jupyter": {"source_hidden": true}, "tags": []}, "outputs": [], "source": ["sql = \"\"\"\n", "\n", "-- all details\n", "\n", "create temporary table surge_cart_base as (\n", "select\n", "at_date_ist date_,\n", "city_name,\n", "cast(traits__merchant_id as varchar) merchant_id,\n", "cast(extract(hour from at_ist) as varchar) hour_,\n", "traits__cart_id,\n", "coalesce(s.slot_amount,0) surge_flag\n", "from\n", "spectrum.mobile_event_data a\n", "left join a.properties__shipments s on true\n", "join dwh.dim_merchant b on a.traits__merchant_id = b.merchant_id\n", "where extract(hour from at_ist) <= {}\n", "and at_date_ist in (current_date-28, current_date-21, current_date-14, current_date-7, current_date)\n", "and name = 'Cart Viewed' and traits__cart_id is not null\n", "group by 1,2,3,4,5,6\n", ");\n", "\n", "-- all details\n", "\n", "with surge_carts as (\n", "select\n", "date_,\n", "city_name,\n", "merchant_id,\n", "hour_,\n", "count(distinct case when surge_flag > 0 then traits__cart_id end)*100.00/greatest(1,count(distinct traits__cart_id)) surge_cart_pct\n", "from\n", "surge_cart_base\n", "group by 1,2,3,4\n", "),\n", "\n", "-- city and store and full day\n", "\n", "surge_carts2 as (\n", "select\n", "date_,\n", "city_name,\n", "merchant_id,\n", "'Today So Far' as hour_,\n", "count(distinct case when surge_flag > 0 then traits__cart_id end)*100.00/greatest(1,count(distinct traits__cart_id)) surge_cart_pct\n", "from\n", "surge_cart_base\n", "group by 1,2,3\n", "),\n", "\n", "-- city and over-1 and hour\n", "\n", "surge_carts3 as (\n", "select\n", "date_,\n", "city_name,\n", "'-1' as merchant_id,\n", "hour_,\n", "count(distinct case when surge_flag > 0 then traits__cart_id end)*100.00/greatest(1,count(distinct traits__cart_id)) surge_cart_pct\n", "from\n", "surge_cart_base\n", "group by 1,2,4\n", "),\n", "\n", "-- city and over-1 and full day\n", "\n", "surge_carts4 as (\n", "select\n", "date_,\n", "city_name,\n", "'-1' as merchant_id,\n", "'Today So Far' as hour_,\n", "count(distinct case when surge_flag > 0 then traits__cart_id end)*100.00/greatest(1,count(distinct traits__cart_id)) surge_cart_pct\n", "from\n", "surge_cart_base\n", "group by 1,2\n", "),\n", "\n", "-- overall pan india and over-1 and hour\n", "\n", "surge_carts5 as (\n", "select\n", "date_,\n", "'Pan India' as city_name,\n", "'-1' as merchant_id,\n", "hour_,\n", "count(distinct case when surge_flag > 0 then traits__cart_id end)*100.00/greatest(1,count(distinct traits__cart_id)) surge_cart_pct\n", "from\n", "surge_cart_base\n", "group by 1,4\n", "),\n", "\n", "-- overall pan india and over-1 and full day\n", "\n", "surge_carts6 as (\n", "select\n", "date_,\n", "'Pan India' as city_name,\n", "'-1' as merchant_id,\n", "'Today So Far' hour_,\n", "count(distinct case when surge_flag > 0 then traits__cart_id end)*100.00/greatest(1,count(distinct traits__cart_id)) surge_cart_pct\n", "from\n", "surge_cart_base\n", "group by 1\n", "),\n", "\n", "sum_agg as (\n", "select* from surge_carts\n", "union all\n", "select* from surge_carts2\n", "union all\n", "select* from surge_carts3\n", "union all\n", "select* from surge_carts4\n", "union all\n", "select* from surge_carts5\n", "union all\n", "select* from surge_carts6\n", "),\n", "\n", "\n", "final_agg as (\n", "select\n", "a.date_,\n", "a.city_name,\n", "a.merchant_id,\n", "a.hour_,\n", "coalesce(a.surge_cart_pct,0) surge_cart_pct_today,\n", "coalesce(b.surge_cart_pct,0) d7_surge_cart_pct,\n", "coalesce(c.surge_cart_pct,0) d14_surge_cart_pct,\n", "coalesce(d.surge_cart_pct,0) d21_surge_cart_pct,\n", "coalesce(e.surge_cart_pct,0) d28_surge_cart_pct\n", "from\n", "sum_agg a\n", "left join\n", "sum_agg b on a.hour_ = b.hour_ and a.city_name = b.city_name and a.merchant_id = b.merchant_id and a.date_ - b.date_ = 7\n", "left join\n", "sum_agg c on a.hour_ = c.hour_ and a.city_name = c.city_name and a.merchant_id = c.merchant_id and a.date_ - c.date_ = 14\n", "left join\n", "sum_agg d on a.hour_ = d.hour_ and a.city_name = d.city_name and a.merchant_id = d.merchant_id and a.date_ - d.date_ = 21\n", "left join\n", "sum_agg e on a.hour_ = e.hour_ and a.city_name = e.city_name and a.merchant_id = e.merchant_id and a.date_ - e.date_ = 28\n", "where a.date_ = current_date\n", ")\n", "\n", "select *from final_agg\n", "\n", "\n", "\n", "\"\"\".format(\n", "    curr_hour\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "09f57e7d-1919-478e-ab15-e1ccde8354ef", "metadata": {}, "outputs": [], "source": ["df_surge = pd.read_sql(sql=sql, con=con_rs)\n", "df_surge.head()"]}, {"cell_type": "code", "execution_count": null, "id": "15318070-eeb3-4eaf-9bbd-b4b25ea143af", "metadata": {"jupyter": {"source_hidden": true}, "tags": []}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2e735dc8-5151-4115-b054-79a2474e3eea", "metadata": {}, "outputs": [], "source": ["# df_conv = pd.read_sql(sql=sql, con=con_rs)\n", "# df_conv.head()"]}, {"cell_type": "code", "execution_count": null, "id": "327236da-b8e2-454f-9738-05d5f6b80677", "metadata": {"jupyter": {"source_hidden": true}, "tags": []}, "outputs": [], "source": ["sql = \"\"\"\n", "\n", "with orders_with_time as (\n", "select\n", "datE(order_create_ts_ist) date_,\n", "city_name,\n", "cast(frontend_merchant_id as varchar) merchant_id,\n", "cast(extract(hour from order_create_ts_ist) as varchar) hour_,\n", "order_id,\n", "case when datediff(seconds,order_create_ts_ist,order_deliver_ts_ist) > 900 then 1 else 0 end over_15_mins_flag\n", "from \n", "dwh.fact_sales_order_details \n", "where extract(hour from order_create_ts_ist) <= {}\n", "and datE(order_create_ts_ist) in (current_date-28, current_date-21, current_date-14, current_date-7, current_date)\n", "and order_current_status <> 'CANCELLED'\n", "group by 1,2,3,4,5,6\n", "),\n", "\n", "-- all details\n", "\n", "quick_del as (\n", "select\n", "date_,\n", "city_name,\n", "merchant_id,\n", "hour_,\n", "count(distinct case when over_15_mins_flag > 0 then order_id end)*100.00/greatest(1,count(distinct order_id)) orders_over_15mins_pct\n", "from\n", "orders_with_time \n", "group by 1,2,3,4\n", "),\n", "\n", "-- city and store and full day\n", "\n", "quick_del2 as (\n", "select\n", "date_,\n", "city_name,\n", "merchant_id,\n", "'Today So Far' as hour_,\n", "count(distinct case when over_15_mins_flag > 0 then order_id end)*100.00/greatest(1,count(distinct order_id)) orders_over_15mins_pct\n", "from\n", "orders_with_time \n", "group by 1,2,3\n", "),\n", "\n", "-- city and over-1 and hour\n", "\n", "quick_del3 as (\n", "select\n", "date_,\n", "city_name,\n", "'-1' as merchant_id,\n", "hour_,\n", "count(distinct case when over_15_mins_flag > 0 then order_id end)*100.00/greatest(1,count(distinct order_id)) orders_over_15mins_pct\n", "from\n", "orders_with_time \n", "group by 1,2,4\n", "),\n", "\n", "-- city and over-1 and full day\n", "\n", "quick_del4 as (\n", "select\n", "date_,\n", "city_name,\n", "'-1' as merchant_id,\n", "'Today So Far' as hour_,\n", "count(distinct case when over_15_mins_flag > 0 then order_id end)*100.00/greatest(1,count(distinct order_id)) orders_over_15mins_pct\n", "from\n", "orders_with_time \n", "group by 1,2\n", "),\n", "\n", "-- overall pan india and over-1 and hour\n", "\n", "quick_del5 as (\n", "select\n", "date_,\n", "'Pan India' as city_name,\n", "'-1' as merchant_id,\n", "hour_,\n", "count(distinct case when over_15_mins_flag > 0 then order_id end)*100.00/greatest(1,count(distinct order_id)) orders_over_15mins_pct\n", "from\n", "orders_with_time \n", "group by 1,4\n", "),\n", "\n", "-- overall pan india and over-1 and full day\n", "\n", "quick_del6 as (\n", "select\n", "date_,\n", "'Pan India' as city_name,\n", "'-1' as merchant_id,\n", "'Today So Far' as hour_,\n", "count(distinct case when over_15_mins_flag > 0 then order_id end)*100.00/greatest(1,count(distinct order_id)) orders_over_15mins_pct\n", "from\n", "orders_with_time \n", "group by 1,4\n", "),\n", "\n", "sum_agg as (\n", "select* from quick_del\n", "union all\n", "select* from quick_del2\n", "union all\n", "select* from quick_del3\n", "union all\n", "select* from quick_del4\n", "union all\n", "select* from quick_del5\n", "union all\n", "select* from quick_del6\n", "),\n", "\n", "\n", "final_agg as (\n", "select\n", "a.date_,\n", "a.city_name,\n", "a.merchant_id,\n", "a.hour_,\n", "coalesce(a.orders_over_15mins_pct,0) orders_over_15mins_pct_today,\n", "coalesce(b.orders_over_15mins_pct,0) d7_orders_over_15mins_pct,\n", "coalesce(c.orders_over_15mins_pct,0) d14_orders_over_15mins_pct,\n", "coalesce(d.orders_over_15mins_pct,0) d21_orders_over_15mins_pct,\n", "coalesce(e.orders_over_15mins_pct,0) d28_orders_over_15mins_pct\n", "from\n", "sum_agg a\n", "left join\n", "sum_agg b on a.hour_ = b.hour_ and a.city_name = b.city_name and a.merchant_id = b.merchant_id and a.date_ - b.date_ = 7\n", "left join\n", "sum_agg c on a.hour_ = c.hour_ and a.city_name = c.city_name and a.merchant_id = c.merchant_id and a.date_ - c.date_ = 14\n", "left join\n", "sum_agg d on a.hour_ = d.hour_ and a.city_name = d.city_name and a.merchant_id = d.merchant_id and a.date_ - d.date_ = 21\n", "left join\n", "sum_agg e on a.hour_ = e.hour_ and a.city_name = e.city_name and a.merchant_id = e.merchant_id and a.date_ - e.date_ = 28\n", "where a.date_ = current_date\n", ")\n", "\n", "select *from final_agg\n", "\n", "\"\"\".format(\n", "    curr_hour\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "aa3bfdc9-f3e9-4dfc-9835-afb9bc3f578d", "metadata": {}, "outputs": [], "source": ["df_orders_15 = pd.read_sql(sql=sql, con=con_rs)\n", "df_orders_15.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bca57b8c-6b5d-4473-b2ef-faaf4d3c468d", "metadata": {"tags": []}, "outputs": [], "source": ["sql = f\"\"\"\n", "\n", "drop table if exists pt_widget;\n", "\n", "CREATE TEMPORARY TABLE pt_widget AS (\n", "    select \n", "        at_date_ist,\n", "        at_ist,\n", "        traits__city_name as city,\n", "        traits__merchant_id as merchant_id,\n", "        \n", "        CASE \n", "            WHEN properties__widget_title ilike '%%earliest%%' \n", "            THEN TRIM ('earliest ' FROM properties__widget_title) \n", "            ELSE properties__widget_title \n", "        END as properties__widget_title,\n", "        \n", "        user_id,\n", "        name,\n", "        upper(device_uuid) as device_uuid\n", "        \n", "    from \n", "        spectrum.mobile_impression_data\n", "    \n", "    where \n", "    extract(hour from at_ist) <= {curr_hour} and \n", "        at_ist < CURRENT_TIMESTAMP + INTERVAL '5.5 HOURS'\n", "        and at_date_ist  in (current_date-28, current_date-21, current_date-14, current_date-7, current_date)\n", "        and name = 'Earliest Promise Time Widget V2 Shown'\n", "        \n", "    GROUP BY\n", "        1, 2, 3, 4, 5, 6, 7, 8\n", ");\n", "\n", "WITH\n", "\n", "tsl AS (\n", "    SELECT \n", "        frontend_merchant_id,\n", "        (timestamp + interval '5.5 hours')::date as date_,\n", "        count(DISTINCT cart_id) as carts\n", "        \n", "    FROM \n", "        consumer.ticktock_serviceability_logs\n", "        \n", "    WHERE \n", "        timestamp >= concat(To_CHAR(current_timestamp - interval '1000 hour','YYYY-MM-DD'), ' 18:30:00')\n", "    \n", "    group by \n", "        1, 2\n", "        \n", "    having carts > 10\n", "),\n", "\n", "pt_cte as (\n", "    select \n", "        at_date_ist AS date_,\n", "        extract(hour from at_ist) as hour_,\n", "        dm.city_name,\n", "        dm.merchant_id,\n", "        dm.merchant_name,\n", "        device_uuid,\n", "        \n", "        CASE \n", "            WHEN \n", "                properties__widget_title ilike '%%delivery by%%' \n", "                AND properties__widget_title ilike '%%a.m%%' \n", "                AND CHAR_LENGTH(properties__widget_title) = 27 \n", "            THEN \n", "                DATEDIFF(minutes,at_ist,DATEADD(hour,(SUBSTRING(properties__widget_title,19,1)::int \n", "                - extract(hour from at_ist))::int,DATEADD(minutes,(SUBSTRING(properties__widget_title,21,2)::int \n", "                - extract(minutes from at_ist)::int)::int,at_ist)))\n", "            WHEN\n", "                properties__widget_title ilike '%%delivery by%%' \n", "                and properties__widget_title ilike '%%a.m%%' \n", "                and CHAR_LENGTH(properties__widget_title) = 28 \n", "            THEN DATEDIFF(minutes,at_ist,DATEADD(hour,(SUBSTRING(properties__widget_title,19,2)::int - extract(hour from at_ist))::int,DATEADD(minutes,(SUBSTRING(properties__widget_title,22,2)::int - extract(minutes from at_ist)::int)::int,at_ist)))\n", "            WHEN \n", "                properties__widget_title ilike '%%delivery by%%' \n", "                and properties__widget_title ilike '%%a.m%%'\n", "                and CHAR_LENGTH(properties__widget_title) = 25 \n", "            then DATEDIFF(minutes,at_ist,DATEADD(hour,(SUBSTRING(properties__widget_title,19,2)::int - extract(hour from at_ist))::int,DATEADD(minutes,(0 - extract(minutes from at_ist)::int)::int,at_ist)))\n", "            when \n", "                properties__widget_title ilike '%%delivery by%%' \n", "                and properties__widget_title ilike '%%a.m%%' \n", "                and CHAR_LENGTH(properties__widget_title) = 24 \n", "            then DATEDIFF(minutes,at_ist,DATEADD(hour,(SUBSTRING(properties__widget_title,19,1)::int - extract(hour from at_ist))::int,DATEADD(minutes,(0 - extract(minutes from at_ist)::int)::int,at_ist)))\n", "\n", "            when \n", "                properties__widget_title ilike '%%delivery by today 12%%'\n", "                and properties__widget_title ilike '%%p.m%%' \n", "                and CHAR_LENGTH(properties__widget_title) = 27 \n", "            then DATEDIFF(minutes,at_ist,DATEADD(hour,((SUBSTRING(properties__widget_title,19,1)::int) - extract(hour from at_ist))::int,DATEADD(minutes,(SUBSTRING(properties__widget_title,21,2)::int - extract(minutes from at_ist)::int)::int,at_ist)))\n", "            when \n", "                properties__widget_title ilike '%%delivery by today 12%%' \n", "                and properties__widget_title ilike '%%p.m%%' \n", "                and CHAR_LENGTH(properties__widget_title) = 28 \n", "            then DATEDIFF(minutes,at_ist,DATEADD(hour,((SUBSTRING(properties__widget_title,19,2)::int) - extract(hour from at_ist))::int,DATEADD(minutes,(SUBSTRING(properties__widget_title,22,2)::int - extract(minutes from at_ist)::int)::int,at_ist)))\n", "            when \n", "                properties__widget_title ilike '%%delivery by today 12%%'\n", "                and properties__widget_title ilike '%%p.m%%' \n", "                and CHAR_LENGTH(properties__widget_title) = 25 \n", "            then DATEDIFF(minutes,at_ist,DATEADD(hour,((SUBSTRING(properties__widget_title,19,2)::int) - extract(hour from at_ist))::int,DATEADD(minutes,(0 - extract(minutes from at_ist)::int)::int,at_ist)))\n", "            when \n", "                properties__widget_title ilike '%%delivery by today 12%%' \n", "                and properties__widget_title ilike '%%p.m%%' \n", "                and CHAR_LENGTH(properties__widget_title) = 24 \n", "            then DATEDIFF(minutes,at_ist,DATEADD(hour,((SUBSTRING(properties__widget_title,19,1)::int) - extract(hour from at_ist))::int,DATEADD(minutes,(0 - extract(minutes from at_ist)::int)::int,at_ist)))\n", "            when \n", "                properties__widget_title ilike '%%delivery by%%' \n", "                and properties__widget_title ilike '%%p.m%%' \n", "                and CHAR_LENGTH(properties__widget_title) = 27 \n", "            then DATEDIFF(minutes,at_ist,DATEADD(hour,((SUBSTRING(properties__widget_title,19,1)::int + 12) - extract(hour from at_ist))::int,DATEADD(minutes,(SUBSTRING(properties__widget_title,21,2)::int - extract(minutes from at_ist)::int)::int,at_ist)))\n", "            when \n", "                properties__widget_title ilike '%%delivery by%%' \n", "                and properties__widget_title ilike '%%p.m%%' \n", "                and CHAR_LENGTH(properties__widget_title) = 28 \n", "            then DATEDIFF(minutes,at_ist,DATEADD(hour,((SUBSTRING(properties__widget_title,19,2)::int + 12) - extract(hour from at_ist))::int,DATEADD(minutes,(SUBSTRING(properties__widget_title,22,2)::int - extract(minutes from at_ist)::int)::int,at_ist)))\n", "            when \n", "                properties__widget_title ilike '%%delivery by%%' \n", "                and properties__widget_title ilike '%%p.m%%' \n", "                and CHAR_LENGTH(properties__widget_title) = 25 \n", "            then DATEDIFF(minutes,at_ist,DATEADD(hour,((SUBSTRING(properties__widget_title,19,2)::int + 12) - extract(hour from at_ist))::int,DATEADD(minutes,(0 - extract(minutes from at_ist)::int)::int,at_ist)))\n", "            when \n", "                properties__widget_title ilike '%%delivery by%%' \n", "                and properties__widget_title ilike '%%p.m%%' \n", "                and CHAR_LENGTH(properties__widget_title) = 24 \n", "            then DATEDIFF(minutes,at_ist,DATEADD(hour,((SUBSTRING(properties__widget_title,19,1)::int + 12) - extract(hour from at_ist))::int,DATEADD(minutes,(0 - extract(minutes from at_ist)::int)::int,at_ist)))\n", "            when \n", "                properties__widget_title ilike '%%closed%%' \n", "                or properties__widget_title ilike '%%temporarily%%' \n", "            then -1\n", "            when \n", "                properties__widget_title ilike '%% minutes%%'\n", "            then SUBSTRING(properties__widget_title,13,2)::int else null \n", "        end as promise_time\n", "        \n", "    from \n", "        pt_widget \n", "            AS p \n", "    join \n", "        dwh.dim_merchant\n", "            AS dm\n", "            ON p.merchant_id = dm.merchant_id\n", "                AND dm.is_current IS true\n", "    join\n", "        tsl \n", "            AS t \n", "            on p.merchant_id = t.frontend_merchant_id \n", "                and p.at_date_ist = t.date_\n", "    \n", "    where \n", "        promise_time > 15\n", "),\n", "\n", "-- all details \n", "\n", "pt_base as (\n", "    select\n", "        date_,\n", "        city_name,\n", "        cast(merchant_id as varchar) merchant_id,\n", "        cast(hour_ as varchar) hour_,\n", "        count(distinct device_uuid) users_wpmt_15\n", "        \n", "    from \n", "        pt_cte\n", "    \n", "    GROUP BY \n", "        1, 2, 3, 4\n", "),\n", "\n", "-- city and store and full day\n", "\n", "pt_base2 as (\n", "select\n", "date_,\n", "city_name,\n", "cast(merchant_id as varchar) merchant_id,\n", "'Today So Far' hour_,\n", "count(distinct device_uuid) users_wpmt_15\n", "from pt_cte group by 1,2,3\n", "),\n", "\n", "-- city and over-1 and hour\n", "\n", "pt_base3 as (\n", "select\n", "date_,\n", "city_name,\n", "'-1' as merchant_id,\n", "cast(hour_ as varchar) hour_,\n", "count(distinct device_uuid) users_wpmt_15\n", "from pt_cte group by 1,2,4\n", "),\n", "\n", "-- city and over-1 and full day\n", "\n", "pt_base4 as (\n", "select\n", "date_,\n", "city_name,\n", "'-1' as merchant_id,\n", "'Today So Far' as hour_,\n", "count(distinct device_uuid) users_wpmt_15\n", "from pt_cte group by 1,2\n", "),\n", "\n", "-- overall pan india and over-1 and hour\n", "\n", "pt_base5 as (\n", "select\n", "date_,\n", "'Pan India' as city_name,\n", "'-1' as merchant_id,\n", "cast(hour_ as varchar) hour_,\n", "count(distinct device_uuid) users_wpmt_15\n", "from pt_cte group by 1,4\n", "),\n", "\n", "-- overall pan india and over-1 and full day\n", "\n", "pt_base6 as (\n", "select\n", "date_,\n", "'Pan India' as city_name,\n", "'-1' as merchant_id,\n", "'Today So Far' as hour_,\n", "count(distinct device_uuid) users_wpmt_15\n", "from pt_cte group by 1\n", "),\n", "\n", "sum_agg as (\n", "select* from pt_base\n", "union all\n", "select* from pt_base2\n", "union all\n", "select* from pt_base3\n", "union all\n", "select* from pt_base4\n", "union all\n", "select* from pt_base5\n", "union all\n", "select* from pt_base6\n", "),\n", "\n", "\n", "final_agg as (\n", "select\n", "a.date_,\n", "a.city_name,\n", "a.merchant_id,\n", "a.hour_,\n", "coalesce(a.users_wpmt_15,0)*100 customers_w15min_pt_today,\n", "coalesce(b.users_wpmt_15,0)*100 d7_customers_w15min_pt_,\n", "coalesce(c.users_wpmt_15,0)*100 d14_customers_w15min_pt_,\n", "coalesce(d.users_wpmt_15,0)*100 d21_customers_w15min_pt_,\n", "coalesce(e.users_wpmt_15,0)*100 d28_customers_w15min_pt_\n", "from\n", "sum_agg a\n", "left join\n", "sum_agg b on a.hour_ = b.hour_ and a.city_name = b.city_name and a.merchant_id = b.merchant_id and a.date_ - b.date_ = 7\n", "left join\n", "sum_agg c on a.hour_ = c.hour_ and a.city_name = c.city_name and a.merchant_id = c.merchant_id and a.date_ - c.date_ = 14\n", "left join\n", "sum_agg d on a.hour_ = d.hour_ and a.city_name = d.city_name and a.merchant_id = d.merchant_id and a.date_ - d.date_ = 21\n", "left join\n", "sum_agg e on a.hour_ = e.hour_ and a.city_name = e.city_name and a.merchant_id = e.merchant_id and a.date_ - e.date_ = 28\n", "where a.date_ = current_date\n", ")\n", "\n", "select *from final_agg\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "75e9e5d7-b614-4cc2-b746-5f585efa1c3c", "metadata": {}, "outputs": [], "source": ["df_promise_time = pd.read_sql(sql=sql, con=con_rs)\n", "df_promise_time.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e7558761-c3b6-4f65-96b9-e224ce3db423", "metadata": {"jupyter": {"source_hidden": true}, "tags": []}, "outputs": [], "source": ["sql = f\"\"\"\n", "-- all details \n", "with \n", "base_store as (\n", "select * \n", "from metrics.store_hourly_weighted_availability as a\n", "where date(a.date_) in (current_date,current_date -7, current_date -14,current_date-21,current_date-28)\n", "and hour <= {curr_hour}\n", "union\n", "select * \n", "from metrics.store_hourly_weighted_availability_logs as a\n", "where date(a.date_) in (current_date,current_date -7, current_date -14,current_date-21,current_date-28)\n", "and hour <= {curr_hour}\n", "),\n", "base_city as (\n", "select * \n", "from metrics.city_hourly_weighted_availability as a\n", "where date(a.date_) in (current_date,current_date -7, current_date -14,current_date-21,current_date-28)\n", "and hour <= {curr_hour}\n", "union\n", "select * \n", "from metrics.city_hourly_weighted_availability_logs as a\n", "where date(a.date_) in (current_date,current_date -7, current_date -14,current_date-21,current_date-28)\n", "and hour <= {curr_hour}\n", "),\n", "\n", "city_zone as (\n", "    SELECT z.city_name city_name,\n", "    a.city \n", "    FROM metrics.store_hourly_weighted_availability AS a\n", "    LEFT JOIN\n", "      (SELECT facility_id,\n", "              frontend_merchant_id AS merchant_id\n", "       FROM dwh.dim_merchant_outlet_facility_mapping\n", "       WHERE valid_to_utc >= CURRENT_DATE\n", "         AND is_current = TRUE\n", "         AND is_mapping_enabled = TRUE\n", "       GROUP BY 1,\n", "                2) AS b ON a.facility_id =b.facility_id\n", "    join dwh.dim_merchant z on z.merchant_id = b.merchant_id and z.is_current = true\n", "    group by 1,2\n", "),\n", "\n", "\n", "\n", "\n", "perishable_av as (\n", "\n", "SELECT z.city_name city_name,\n", "cast(b.merchant_id as varchar(max)) merchant_id,\n", "date_,\n", "cast(hour as varchar(max)) hour_,\n", "avg(weighted_availability) perish_avail_pct\n", "FROM base_store AS a\n", "LEFT JOIN\n", "  (SELECT facility_id,\n", "          frontend_merchant_id AS merchant_id\n", "   FROM dwh.dim_merchant_outlet_facility_mapping\n", "   WHERE valid_to_utc >= CURRENT_DATE\n", "     AND is_current = TRUE\n", "     AND is_mapping_enabled = TRUE\n", "   GROUP BY 1,\n", "            2) AS b ON a.facility_id =b.facility_id\n", "join dwh.dim_merchant z on z.merchant_id = b.merchant_id and z.is_current = true\n", "WHERE date(a.date_) IN (CURRENT_DATE,CURRENT_DATE -7,CURRENT_DATE -14,CURRENT_DATE-21,CURRENT_DATE-28)\n", "and assortment_type = 'Perishable'\n", "group by 1,2,3,4\n", "),\n", "\n", "-- city and store and full day\n", "\n", "perishable_av2 as (\n", "SELECT z.city_name city_name,\n", "cast(b.merchant_id as varchar(max)) merchant_id,\n", "date_,\n", "'Today So Far' hour_,\n", "avg(weighted_availability) perish_avail_pct\n", "FROM base_store AS a\n", "LEFT JOIN\n", "  (SELECT facility_id,\n", "          frontend_merchant_id AS merchant_id\n", "   FROM dwh.dim_merchant_outlet_facility_mapping\n", "   WHERE valid_to_utc >= CURRENT_DATE\n", "     AND is_current = TRUE\n", "     AND is_mapping_enabled = TRUE\n", "   GROUP BY 1,\n", "            2) AS b ON a.facility_id =b.facility_id\n", "join dwh.dim_merchant z on z.merchant_id = b.merchant_id and z.is_current = true\n", "WHERE date(a.date_) IN (CURRENT_DATE,CURRENT_DATE -7,CURRENT_DATE -14,CURRENT_DATE-21,CURRENT_DATE-28)\n", "and assortment_type = 'Perishable'\n", "group by 1,2,3,4\n", "),\n", "\n", "-- city and over-1 and hour\n", "\n", "perishable_av3 as (\n", "Select b.city_name city_name,\n", "'-1' merchant_id,\n", "date_,\n", "cast(hour as varchar(max)) hour_,\n", "avg(weighted_availability) perish_avail_pct\n", "from base_city a\n", "join city_zone b on a.city = b.city\n", "WHERE date(a.date_) IN (CURRENT_DATE,CURRENT_DATE -7,CURRENT_DATE -14,CURRENT_DATE-21,CURRENT_DATE-28)\n", "and assortment_type = 'Perishable'\n", "group by 1,2,3,4\n", "),\n", "\n", "-- city and over-1 and full day\n", "\n", "perishable_av4 as (\n", "Select b.city_name city_name,\n", "'-1' merchant_id,\n", "date_,\n", "'Today So Far' hour_,\n", "avg(weighted_availability) perish_avail_pct\n", "from base_city a\n", "join city_zone b on a.city = b.city\n", "WHERE date(a.date_) IN (CURRENT_DATE,CURRENT_DATE -7,CURRENT_DATE -14,CURRENT_DATE-21,CURRENT_DATE-28)\n", "and assortment_type = 'Perishable'\n", "group by 1,2,3,4\n", "),\n", "\n", "-- overall pan india and over-1 and hour\n", "\n", "perishable_av5 as (\n", "Select 'Pan India' city_name,\n", "'-1' merchant_id,\n", "date_,\n", "cast(hour as varchar(max)) hour_,\n", "avg(weighted_availability) perish_avail_pct\n", "from base_city a\n", "WHERE date(a.date_) IN (CURRENT_DATE,CURRENT_DATE -7,CURRENT_DATE -14,CURRENT_DATE-21,CURRENT_DATE-28)\n", "and assortment_type = 'Perishable'\n", "group by 1,2,3,4\n", "),\n", "\n", "-- overall pan india and over-1 and full day\n", "\n", "perishable_av6 as (\n", "Select 'Pan India' city_name,\n", "'-1' merchant_id,\n", "date_,\n", "'Today So Far' hour_,\n", "avg(weighted_availability) perish_avail_pct\n", "from base_city a\n", "WHERE date(a.date_) IN (CURRENT_DATE,CURRENT_DATE -7,CURRENT_DATE -14,CURRENT_DATE-21,CURRENT_DATE-28)\n", "and assortment_type = 'Perishable'\n", "group by 1,2,3,4\n", "),\n", "\n", "sum_agg as (\n", "select* from perishable_av\n", "union all\n", "select* from perishable_av2\n", "union all\n", "select* from perishable_av3\n", "union all\n", "select* from perishable_av4\n", "union all\n", "select* from perishable_av5\n", "union all\n", "select* from perishable_av6\n", ")\n", ",\n", "\n", "\n", "final_agg as (\n", "select\n", "a.date_,\n", "a.city_name,\n", "a.merchant_id,\n", "a.hour_,\n", "coalesce(a.perish_avail_pct,0)*100 perish_avail_today,\n", "\n", "coalesce(b.perish_avail_pct,0)*100 d7_perish_avail,\n", "coalesce(c.perish_avail_pct,0)*100 d14_perish_avail,\n", "coalesce(d.perish_avail_pct,0)*100 d21_perish_avail,\n", "coalesce(e.perish_avail_pct,0)*100 d28_perish_avail\n", "from\n", "sum_agg a\n", "left join\n", "sum_agg b on a.hour_ = b.hour_ and a.city_name = b.city_name and a.merchant_id = b.merchant_id and date(a.date_) - date(b.date_) = 7\n", "left join\n", "sum_agg c on a.hour_ = c.hour_ and a.city_name = c.city_name and a.merchant_id = c.merchant_id and date(a.date_) - date(c.date_) = 14\n", "left join\n", "sum_agg d on a.hour_ = d.hour_ and a.city_name = d.city_name and a.merchant_id = d.merchant_id and date(a.date_) - date(d.date_) = 21\n", "left join\n", "sum_agg e on a.hour_ = e.hour_ and a.city_name = e.city_name and a.merchant_id = e.merchant_id and date(a.date_) - date(e.date_) = 28\n", "where a.date_ = current_date\n", ")\n", "\n", "select * from final_agg\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "f18e977a-53a8-469b-a080-b43bf0daf7c9", "metadata": {}, "outputs": [], "source": ["df_perish_av = pd.read_sql(sql=sql, con=con_rs)\n", "df_perish_av.head()"]}, {"cell_type": "code", "execution_count": null, "id": "67d2fbfc-cae7-4635-9a57-3fe41737decc", "metadata": {}, "outputs": [], "source": ["df_perish_av[(df_perish_av[\"merchant_id\"] == \"-1\") & (df_perish_av[\"hour_\"] == \"12\")]"]}, {"cell_type": "code", "execution_count": null, "id": "6f5b0666-eaae-426d-b6ba-926e8b74114e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e1ac5cd9-ad51-41ba-b936-ae8d653930e2", "metadata": {"tags": []}, "outputs": [], "source": ["sql = f\"\"\"\n", "-- all details \n", "with \n", "base_store as (\n", "select * \n", "from metrics.store_hourly_weighted_availability as a\n", "where date(a.date_) in (current_date,current_date -7, current_date -14,current_date-21,current_date-28)\n", "and hour <= {curr_hour}\n", "union\n", "select * \n", "from metrics.store_hourly_weighted_availability_logs as a\n", "where date(a.date_) in (current_date,current_date -7, current_date -14,current_date-21,current_date-28)\n", "and hour <= {curr_hour}\n", "),\n", "base_city as (\n", "select * \n", "from metrics.city_hourly_weighted_availability as a\n", "where date(a.date_) in (current_date,current_date -7, current_date -14,current_date-21,current_date-28)\n", "and hour <= {curr_hour}\n", "union\n", "select * \n", "from metrics.city_hourly_weighted_availability_logs as a\n", "where date(a.date_) in (current_date,current_date -7, current_date -14,current_date-21,current_date-28)\n", "and hour <= {curr_hour}\n", "),\n", "\n", "city_zone as (\n", "    SELECT z.city_name city_name,\n", "    a.city \n", "    FROM metrics.store_hourly_weighted_availability AS a\n", "    LEFT JOIN\n", "      (SELECT facility_id,\n", "              frontend_merchant_id AS merchant_id\n", "       FROM dwh.dim_merchant_outlet_facility_mapping\n", "       WHERE valid_to_utc >= CURRENT_DATE\n", "         AND is_current = TRUE\n", "         AND is_mapping_enabled = TRUE\n", "       GROUP BY 1,\n", "                2) AS b ON a.facility_id =b.facility_id\n", "    join dwh.dim_merchant z on z.merchant_id = b.merchant_id and z.is_current = true\n", "    group by 1,2\n", "),\n", "\n", "\n", "perishable_av as (\n", "\n", "SELECT z.city_name city_name,\n", "cast(b.merchant_id as varchar(max)) merchant_id,\n", "date_,\n", "cast(hour as varchar(max)) hour_,\n", "avg(weighted_availability) perish_avail_pct\n", "FROM base_store AS a\n", "LEFT JOIN\n", "  (SELECT facility_id,\n", "          frontend_merchant_id AS merchant_id\n", "   FROM dwh.dim_merchant_outlet_facility_mapping\n", "   WHERE valid_to_utc >= CURRENT_DATE\n", "     AND is_current = TRUE\n", "     AND is_mapping_enabled = TRUE\n", "   GROUP BY 1,\n", "            2) AS b ON a.facility_id =b.facility_id\n", "join dwh.dim_merchant z on z.merchant_id = b.merchant_id and z.is_current = true\n", "WHERE date(a.date_) IN (CURRENT_DATE,CURRENT_DATE -7,CURRENT_DATE -14,CURRENT_DATE-21,CURRENT_DATE-28)\n", "and assortment_type = 'Packaged Goods'\n", "group by 1,2,3,4\n", "),\n", "\n", "-- city and store and full day\n", "\n", "perishable_av2 as (\n", "SELECT z.city_name city_name,\n", "cast(b.merchant_id as varchar(max)) merchant_id,\n", "date_,\n", "'Today So Far' hour_,\n", "avg(weighted_availability) perish_avail_pct\n", "FROM base_store AS a\n", "LEFT JOIN\n", "  (SELECT facility_id,\n", "          frontend_merchant_id AS merchant_id\n", "   FROM dwh.dim_merchant_outlet_facility_mapping\n", "   WHERE valid_to_utc >= CURRENT_DATE\n", "     AND is_current = TRUE\n", "     AND is_mapping_enabled = TRUE\n", "   GROUP BY 1,\n", "            2) AS b ON a.facility_id =b.facility_id\n", "join dwh.dim_merchant z on z.merchant_id = b.merchant_id and z.is_current = true\n", "WHERE date(a.date_) IN (CURRENT_DATE,CURRENT_DATE -7,CURRENT_DATE -14,CURRENT_DATE-21,CURRENT_DATE-28)\n", "and assortment_type = 'Packaged Goods'\n", "group by 1,2,3,4\n", "),\n", "\n", "-- city and over-1 and hour\n", "\n", "perishable_av3 as (\n", "Select B.city_name city_name,\n", "'-1' merchant_id,\n", "date_,\n", "cast(hour as varchar(max)) hour_,\n", "avg(weighted_availability) perish_avail_pct\n", "from base_city a\n", "join city_zone b on a.city = b.city\n", "WHERE date(a.date_) IN (CURRENT_DATE,CURRENT_DATE -7,CURRENT_DATE -14,CURRENT_DATE-21,CURRENT_DATE-28)\n", "and assortment_type = 'Packaged Goods'\n", "group by 1,2,3,4\n", "),\n", "\n", "-- city and over-1 and full day\n", "\n", "perishable_av4 as (\n", "Select b.city_name city_name,\n", "'-1' merchant_id,\n", "date_,\n", "'Today So Far' hour_,\n", "avg(weighted_availability) perish_avail_pct\n", "from base_city a\n", "join city_zone b on a.city = b.city\n", "WHERE date(a.date_) IN (CURRENT_DATE,CURRENT_DATE -7,CURRENT_DATE -14,CURRENT_DATE-21,CURRENT_DATE-28)\n", "and assortment_type = 'Packaged Goods'\n", "group by 1,2,3,4\n", "),\n", "\n", "-- overall pan india and over-1 and hour\n", "\n", "perishable_av5 as (\n", "Select 'Pan India' city_name,\n", "'-1' merchant_id,\n", "date_,\n", "cast(hour as varchar(max)) hour_,\n", "avg(weighted_availability) perish_avail_pct\n", "from base_city a\n", "WHERE date(a.date_) IN (CURRENT_DATE,CURRENT_DATE -7,CURRENT_DATE -14,CURRENT_DATE-21,CURRENT_DATE-28)\n", "and assortment_type = 'Packaged Goods'\n", "group by 1,2,3,4\n", "),\n", "\n", "-- overall pan india and over-1 and full day\n", "\n", "perishable_av6 as (\n", "Select 'Pan India' city_name,\n", "'-1' merchant_id,\n", "date_,\n", "'Today So Far' hour_,\n", "avg(weighted_availability) perish_avail_pct\n", "from base_city a\n", "WHERE date(a.date_) IN (CURRENT_DATE,CURRENT_DATE -7,CURRENT_DATE -14,CURRENT_DATE-21,CURRENT_DATE-28)\n", "and assortment_type = 'Packaged Goods'\n", "group by 1,2,3,4\n", "),\n", "\n", "sum_agg as (\n", "select* from perishable_av\n", "union all\n", "select* from perishable_av2\n", "union all\n", "select* from perishable_av3\n", "union all\n", "select* from perishable_av4\n", "union all\n", "select* from perishable_av5\n", "union all\n", "select* from perishable_av6\n", ")\n", ",\n", "\n", "\n", "final_agg as (\n", "select\n", "a.date_,\n", "a.city_name,\n", "a.merchant_id,\n", "a.hour_,\n", "coalesce(a.perish_avail_pct,0)*100 packaged_avail_today,\n", "coalesce(b.perish_avail_pct,0)*100 d7_packaged_avail,\n", "coalesce(c.perish_avail_pct,0)*100 d14_packaged_avail,\n", "coalesce(d.perish_avail_pct,0)*100 d21_packaged_avail,\n", "coalesce(e.perish_avail_pct,0)*100 d28_packaged_avail\n", "from\n", "sum_agg a\n", "left join\n", "sum_agg b on a.hour_ = b.hour_ and a.city_name = b.city_name and a.merchant_id = b.merchant_id and date(a.date_) - date(b.date_) = 7\n", "left join\n", "sum_agg c on a.hour_ = c.hour_ and a.city_name = c.city_name and a.merchant_id = c.merchant_id and date(a.date_) - date(c.date_) = 14\n", "left join\n", "sum_agg d on a.hour_ = d.hour_ and a.city_name = d.city_name and a.merchant_id = d.merchant_id and date(a.date_) - date(d.date_) = 21\n", "left join\n", "sum_agg e on a.hour_ = e.hour_ and a.city_name = e.city_name and a.merchant_id = e.merchant_id and date(a.date_) - date(e.date_) = 28\n", "where a.date_ = current_date\n", ")\n", "\n", "select * from final_agg\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "1547c1e9-be38-4c66-b9e3-d0c5bf259519", "metadata": {}, "outputs": [], "source": ["df_packaged_av = pd.read_sql(sql=sql, con=con_rs)\n", "df_packaged_av.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c946c58b-ccd3-4a11-a1f6-a35af4d36095", "metadata": {"tags": []}, "outputs": [], "source": ["sql = f\"\"\"\n", "-- all details \n", "with \n", "base_store as (\n", "select * \n", "from metrics.store_hourly_weighted_availability as a\n", "where date(a.date_) in (current_date,current_date -7, current_date -14,current_date-21,current_date-28)\n", "and hour <= {curr_hour}\n", "union\n", "select * \n", "from metrics.store_hourly_weighted_availability_logs as a\n", "where date(a.date_) in (current_date,current_date -7, current_date -14,current_date-21,current_date-28)\n", "and hour <= {curr_hour}\n", "),\n", "base_city as (\n", "select * \n", "from metrics.city_hourly_weighted_availability as a\n", "where date(a.date_) in (current_date,current_date -7, current_date -14,current_date-21,current_date-28)\n", "and hour <= {curr_hour}\n", "union\n", "select * \n", "from metrics.city_hourly_weighted_availability_logs as a\n", "where date(a.date_) in (current_date,current_date -7, current_date -14,current_date-21,current_date-28)\n", "and hour <= {curr_hour}\n", "),\n", "\n", "city_zone as (\n", "    SELECT z.city_name city_name,\n", "    a.city \n", "    FROM metrics.store_hourly_weighted_availability AS a\n", "    LEFT JOIN\n", "      (SELECT facility_id,\n", "              frontend_merchant_id AS merchant_id\n", "       FROM dwh.dim_merchant_outlet_facility_mapping\n", "       WHERE valid_to_utc >= CURRENT_DATE\n", "         AND is_current = TRUE\n", "         AND is_mapping_enabled = TRUE\n", "       GROUP BY 1,\n", "                2) AS b ON a.facility_id =b.facility_id\n", "    join dwh.dim_merchant z on z.merchant_id = b.merchant_id and z.is_current = true\n", "    group by 1,2\n", "),\n", "\n", "\n", "\n", "\n", "perishable_av as (\n", "\n", "SELECT z.city_name city_name,\n", "cast(b.merchant_id as varchar(max)) merchant_id,\n", "date_,\n", "cast(hour as varchar(max)) hour_,\n", "avg(weighted_availability) perish_avail_pct\n", "FROM base_store AS a\n", "LEFT JOIN\n", "  (SELECT facility_id,\n", "          frontend_merchant_id AS merchant_id\n", "   FROM dwh.dim_merchant_outlet_facility_mapping\n", "   WHERE valid_to_utc >= CURRENT_DATE\n", "     AND is_current = TRUE\n", "     AND is_mapping_enabled = TRUE\n", "   GROUP BY 1,\n", "            2) AS b ON a.facility_id =b.facility_id\n", "join dwh.dim_merchant z on z.merchant_id = b.merchant_id and z.is_current = true\n", "WHERE date(a.date_) IN (CURRENT_DATE,CURRENT_DATE -7,CURRENT_DATE -14,CURRENT_DATE-21,CURRENT_DATE-28)\n", "and assortment_type = 'FnV'\n", "group by 1,2,3,4\n", "),\n", "\n", "-- city and store and full day\n", "\n", "perishable_av2 as (\n", "SELECT z.city_name city_name,\n", "cast(b.merchant_id as varchar(max)) merchant_id,\n", "date_,\n", "'Today So Far' hour_,\n", "avg(weighted_availability) perish_avail_pct\n", "FROM base_store AS a\n", "LEFT JOIN\n", "  (SELECT facility_id,\n", "          frontend_merchant_id AS merchant_id\n", "   FROM dwh.dim_merchant_outlet_facility_mapping\n", "   WHERE valid_to_utc >= CURRENT_DATE\n", "     AND is_current = TRUE\n", "     AND is_mapping_enabled = TRUE\n", "   GROUP BY 1,\n", "            2) AS b ON a.facility_id =b.facility_id\n", "join dwh.dim_merchant z on z.merchant_id = b.merchant_id and z.is_current = true\n", "WHERE date(a.date_) IN (CURRENT_DATE,CURRENT_DATE -7,CURRENT_DATE -14,CURRENT_DATE-21,CURRENT_DATE-28)\n", "and assortment_type = 'FnV'\n", "group by 1,2,3,4\n", "),\n", "\n", "-- city and over-1 and hour\n", "\n", "perishable_av3 as (\n", "Select b.city_name city_name,\n", "'-1' merchant_id,\n", "date_,\n", "cast(hour as varchar(max)) hour_,\n", "avg(weighted_availability) perish_avail_pct\n", "from base_city a\n", "join city_zone b on a.city = b.city\n", "WHERE date(a.date_) IN (CURRENT_DATE,CURRENT_DATE -7,CURRENT_DATE -14,CURRENT_DATE-21,CURRENT_DATE-28)\n", "and assortment_type = 'FnV'\n", "group by 1,2,3,4\n", "),\n", "\n", "-- city and over-1 and full day\n", "\n", "perishable_av4 as (\n", "Select b.city_name city_name,\n", "'-1' merchant_id,\n", "date_,\n", "'Today So Far' hour_,\n", "avg(weighted_availability) perish_avail_pct\n", "from base_city a\n", "join city_zone b on a.city = b.city\n", "WHERE date(a.date_) IN (CURRENT_DATE,CURRENT_DATE -7,CURRENT_DATE -14,CURRENT_DATE-21,CURRENT_DATE-28)\n", "and assortment_type = 'FnV'\n", "group by 1,2,3,4\n", "),\n", "\n", "-- overall pan india and over-1 and hour\n", "\n", "perishable_av5 as (\n", "Select 'Pan India' city_name,\n", "'-1' merchant_id,\n", "date_,\n", "cast(hour as varchar(max)) hour_,\n", "avg(weighted_availability) perish_avail_pct\n", "from base_city a\n", "WHERE date(a.date_) IN (CURRENT_DATE,CURRENT_DATE -7,CURRENT_DATE -14,CURRENT_DATE-21,CURRENT_DATE-28)\n", "and assortment_type = 'FnV'\n", "group by 1,2,3,4\n", "),\n", "\n", "-- overall pan india and over-1 and full day\n", "\n", "perishable_av6 as (\n", "Select 'Pan India' city_name,\n", "'-1' merchant_id,\n", "date_,\n", "'Today So Far' hour_,\n", "avg(weighted_availability) perish_avail_pct\n", "from base_city a\n", "WHERE date(a.date_) IN (CURRENT_DATE,CURRENT_DATE -7,CURRENT_DATE -14,CURRENT_DATE-21,CURRENT_DATE-28)\n", "and assortment_type = 'FnV'\n", "group by 1,2,3,4\n", "),\n", "\n", "sum_agg as (\n", "select* from perishable_av\n", "union all\n", "select* from perishable_av2\n", "union all\n", "select* from perishable_av3\n", "union all\n", "select* from perishable_av4\n", "union all\n", "select* from perishable_av5\n", "union all\n", "select* from perishable_av6\n", ")\n", ",\n", "\n", "\n", "final_agg as (\n", "select\n", "a.date_,\n", "a.city_name,\n", "a.merchant_id,\n", "a.hour_,\n", "coalesce(a.perish_avail_pct,0)*100 fnv_avail_today,\n", "coalesce(b.perish_avail_pct,0)*100 d7_fnv_avail,\n", "coalesce(c.perish_avail_pct,0)*100 d14_fnv_avail,\n", "coalesce(d.perish_avail_pct,0)*100 d21_fnv_avail,\n", "coalesce(e.perish_avail_pct,0)*100 d28_fnv_avail\n", "from\n", "sum_agg a\n", "left join\n", "sum_agg b on a.hour_ = b.hour_ and a.city_name = b.city_name and a.merchant_id = b.merchant_id and date(a.date_) - date(b.date_) = 7\n", "left join\n", "sum_agg c on a.hour_ = c.hour_ and a.city_name = c.city_name and a.merchant_id = c.merchant_id and date(a.date_) - date(c.date_) = 14\n", "left join\n", "sum_agg d on a.hour_ = d.hour_ and a.city_name = d.city_name and a.merchant_id = d.merchant_id and date(a.date_) - date(d.date_) = 21\n", "left join\n", "sum_agg e on a.hour_ = e.hour_ and a.city_name = e.city_name and a.merchant_id = e.merchant_id and date(a.date_) - date(e.date_) = 28\n", "where a.date_ = current_date\n", ")\n", "\n", "select *from final_agg\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "40b3968d-0d7f-4831-8b90-7779edf02d8f", "metadata": {}, "outputs": [], "source": ["df_fnv_av = pd.read_sql(sql=sql, con=con_rs)\n", "df_fnv_av.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6a6f8ae4-23e1-4b54-94d9-beebdb3ebe9b", "metadata": {"tags": []}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a1ecca84-82b2-4f2c-9810-5cbd858aa52e", "metadata": {"tags": []}, "outputs": [], "source": ["# df_tran_users = pd.read_sql(sql=sql, con=con_rs)\n", "# df_tran_users.head()main_df = df_dau.merge(df_orders, how=\"left\", on=[\"hour_\", \"city_name\", \"merchant_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "346d288b-34a7-468d-93b3-09dfae2cac72", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b28364a6-f1cb-4529-9e78-553290ff3d97", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "43d1110d-5b38-4447-8b11-65ad2a4bdae2", "metadata": {"tags": []}, "outputs": [], "source": ["main_df = df_dau.merge(df_orders, how=\"left\", on=[\"hour_\", \"city_name\", \"merchant_id\"])\n", "main_df.columns.values[0] = \"date_\"\n", "main_df = main_df.merge(df_surge, how=\"left\", on=[\"hour_\", \"city_name\", \"merchant_id\"])\n", "main_df.columns.values[0] = \"date_\"\n", "# main_df = main_df.merge(df_conv, how=\"left\", on=[\"hour_\", \"city_name\", \"merchant_id\"])\n", "# main_df.columns.values[0] = \"date_\"\n", "main_df = main_df.merge(\n", "    df_orders_15, how=\"left\", on=[\"hour_\", \"city_name\", \"merchant_id\"]\n", ")\n", "main_df.columns.values[0] = \"date_\"\n", "main_df = main_df.merge(\n", "    df_promise_time, how=\"left\", on=[\"hour_\", \"city_name\", \"merchant_id\"]\n", ")\n", "main_df.columns.values[0] = \"date_\"\n", "main_df = main_df.merge(\n", "    df_perish_av, how=\"left\", on=[\"hour_\", \"city_name\", \"merchant_id\"]\n", ")\n", "main_df.columns.values[0] = \"date_\"\n", "main_df = main_df.merge(\n", "    df_packaged_av, how=\"left\", on=[\"hour_\", \"city_name\", \"merchant_id\"]\n", ")\n", "main_df.columns.values[0] = \"date_\"\n", "main_df = main_df.merge(df_fnv_av, how=\"left\", on=[\"hour_\", \"city_name\", \"merchant_id\"])\n", "main_df.columns.values[0] = \"date_\"\n", "# main_df = main_df.merge(\n", "#    df_tran_users, how=\"left\", on=[\"hour_\", \"city_name\", \"merchant_id\"])\n", "# main_df.columns.values[0] = \"date_\"\n", "main_df = main_df.fillna(0)\n", "main_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "68ccd752-b87d-495c-9b96-41dcd5dc03fb", "metadata": {"tags": []}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e3e25af7-0ec8-41b6-887b-9e31504e7a31", "metadata": {}, "outputs": [], "source": ["list(main_df)"]}, {"cell_type": "code", "execution_count": null, "id": "ce3aef59-2662-40be-b518-21dc3401835a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "804e1c28-c14c-4bb4-89d7-a67ebfdd8225", "metadata": {"tags": []}, "outputs": [], "source": ["main_df = main_df[\n", "    [\n", "        \"date_\",\n", "        \"hour_\",\n", "        \"city_name\",\n", "        \"merchant_id\",\n", "        \"dau_today\",\n", "        \"d7_dau\",\n", "        \"d14_dau\",\n", "        \"d21_dau\",\n", "        \"d28_dau\",\n", "        \"orders_today\",\n", "        \"d7_orders\",\n", "        \"d14_orders\",\n", "        \"d21_orders\",\n", "        \"d28_orders\",\n", "        \"surge_cart_pct_today\",\n", "        \"d7_surge_cart_pct\",\n", "        \"d14_surge_cart_pct\",\n", "        \"d21_surge_cart_pct\",\n", "        \"d28_surge_cart_pct\",\n", "        \"conversion_today\",\n", "        \"d7_conversion\",\n", "        \"d14_conversion\",\n", "        \"d21_conversion\",\n", "        \"d28_conversion\",\n", "        \"orders_over_15mins_pct_today\",\n", "        \"d7_orders_over_15mins_pct\",\n", "        \"d14_orders_over_15mins_pct\",\n", "        \"d21_orders_over_15mins_pct\",\n", "        \"d28_orders_over_15mins_pct\",\n", "        \"customers_w15min_pt_today\",\n", "        \"d7_customers_w15min_pt_\",\n", "        \"d14_customers_w15min_pt_\",\n", "        \"d21_customers_w15min_pt_\",\n", "        \"d28_customers_w15min_pt_\",\n", "        \"perish_avail_today\",\n", "        \"d7_perish_avail\",\n", "        \"d14_perish_avail\",\n", "        \"d21_perish_avail\",\n", "        \"d28_perish_avail\",\n", "        \"packaged_avail_today\",\n", "        \"d7_packaged_avail\",\n", "        \"d14_packaged_avail\",\n", "        \"d21_packaged_avail\",\n", "        \"d28_packaged_avail\",\n", "        \"fnv_avail_today\",\n", "        \"d7_fnv_avail\",\n", "        \"d14_fnv_avail\",\n", "        \"d21_fnv_avail\",\n", "        \"d28_fnv_avail\",\n", "        \"transacting_users_today\",\n", "        \"d7_transacting_users\",\n", "        \"d14_transacting_users\",\n", "        \"d21_transacting_users\",\n", "        \"d28_transacting_users\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "185a720d-5978-47d3-941a-56f310d455a5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ee7df180-df15-4140-984d-e6ec75278843", "metadata": {}, "outputs": [], "source": ["# main_df[main_df.city_name == \"Pan India\"].head()"]}, {"cell_type": "code", "execution_count": null, "id": "6fced9e6-f46d-41a5-ac83-c75a5d209aa5", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1P8ekJYArVEyxQwJLzjA4cS_5gD2str1Wl_2eC9bjlZ0\"\n", "sheet_name = \"View\"\n", "# pb.to_sheets(main_df, sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "be52bf1c-5072-41fb-ae6f-a5a27a43edbc", "metadata": {}, "outputs": [], "source": ["### deleting existing data for current day and replacing it with main_df"]}, {"cell_type": "code", "execution_count": null, "id": "ddf04142-54cf-4a1d-bd50-d285725930a2", "metadata": {}, "outputs": [], "source": ["redshift_connection = pb.get_connection(\"redpen\")\n", "redshift_connection.execute(\"delete from metrics.growth_rca where date_ = current_date\")"]}, {"cell_type": "code", "execution_count": null, "id": "ab41e890-eed8-4bf8-8361-47b53e7d8d4a", "metadata": {}, "outputs": [], "source": ["main_df[\"dau_today\"] = main_df[\"dau_today\"].astype(float)\n", "main_df[\"d7_dau\"] = main_df[\"d7_dau\"].astype(float)\n", "main_df[\"d14_dau\"] = main_df[\"d14_dau\"].astype(float)\n", "main_df[\"d21_dau\"] = main_df[\"d21_dau\"].astype(float)\n", "main_df[\"d28_dau\"] = main_df[\"d28_dau\"].astype(float)\n", "# main_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "f468b619-9afb-434a-a110-b2eb724402cf", "metadata": {"tags": []}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"growth_rca\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date_\", \"type\": \"date\", \"description\": \"Date\"},\n", "        {\"name\": \"hour_\", \"type\": \"varchar\", \"description\": \"Hour\"},\n", "        {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"City Name\"},\n", "        {\"name\": \"merchant_id\", \"type\": \"varchar\", \"description\": \"Merchant Name\"},\n", "        {\"name\": \"dau_today\", \"type\": \"float\", \"description\": \"DAU D-1\"},\n", "        {\"name\": \"d7_dau\", \"type\": \"float\", \"description\": \"DAU D-7\"},\n", "        {\"name\": \"d14_dau\", \"type\": \"float\", \"description\": \"Date\"},\n", "        {\"name\": \"d21_dau\", \"type\": \"float\", \"description\": \"Date\"},\n", "        {\"name\": \"d28_dau\", \"type\": \"float\", \"description\": \"Date\"},\n", "        {\"name\": \"orders_today\", \"type\": \"float\", \"description\": \"Date\"},\n", "        {\"name\": \"d7_orders\", \"type\": \"float\", \"description\": \"Date\"},\n", "        {\"name\": \"d14_orders\", \"type\": \"float\", \"description\": \"Date\"},\n", "        {\"name\": \"d21_orders\", \"type\": \"float\", \"description\": \"Date\"},\n", "        {\"name\": \"d28_orders\", \"type\": \"float\", \"description\": \"Date\"},\n", "        {\n", "            \"name\": \"surge_cart_pct_today\",\n", "            \"type\": \"float\",\n", "            \"description\": \"surge_cart_pct_today\",\n", "        },\n", "        {\n", "            \"name\": \"d7_surge_cart_pct\",\n", "            \"type\": \"float\",\n", "            \"description\": \"d7_surge_cart_pct\",\n", "        },\n", "        {\n", "            \"name\": \"d14_surge_cart_pct\",\n", "            \"type\": \"float\",\n", "            \"description\": \"d7_surge_cart_pct\",\n", "        },\n", "        {\n", "            \"name\": \"d21_surge_cart_pct\",\n", "            \"type\": \"float\",\n", "            \"description\": \"d7_surge_cart_pct\",\n", "        },\n", "        {\n", "            \"name\": \"d28_surge_cart_pct\",\n", "            \"type\": \"float\",\n", "            \"description\": \"d7_surge_cart_pct\",\n", "        },\n", "        {\n", "            \"name\": \"conversion_today\",\n", "            \"type\": \"float\",\n", "            \"description\": \"surge_cart_pct_today\",\n", "        },\n", "        {\"name\": \"d7_conversion\", \"type\": \"float\", \"description\": \"d7_surge_cart_pct\"},\n", "        {\"name\": \"d14_conversion\", \"type\": \"float\", \"description\": \"d7_surge_cart_pct\"},\n", "        {\"name\": \"d21_conversion\", \"type\": \"float\", \"description\": \"d7_surge_cart_pct\"},\n", "        {\"name\": \"d28_conversion\", \"type\": \"float\", \"description\": \"d7_surge_cart_pct\"},\n", "        {\n", "            \"name\": \"orders_over_15mins_pct_today\",\n", "            \"type\": \"float\",\n", "            \"description\": \"surge_cart_pct_today\",\n", "        },\n", "        {\n", "            \"name\": \"d7_orders_over_15mins_pct\",\n", "            \"type\": \"float\",\n", "            \"description\": \"d7_surge_cart_pct\",\n", "        },\n", "        {\n", "            \"name\": \"d14_orders_over_15mins_pct\",\n", "            \"type\": \"float\",\n", "            \"description\": \"d7_surge_cart_pct\",\n", "        },\n", "        {\n", "            \"name\": \"d21_orders_over_15mins_pct\",\n", "            \"type\": \"float\",\n", "            \"description\": \"d7_surge_cart_pct\",\n", "        },\n", "        {\n", "            \"name\": \"d28_orders_over_15mins_pct\",\n", "            \"type\": \"float\",\n", "            \"description\": \"d7_surge_cart_pct\",\n", "        },\n", "        {\n", "            \"name\": \"customers_w15min_pt_today\",\n", "            \"type\": \"float\",\n", "            \"description\": \"surge_cart_pct_today\",\n", "        },\n", "        {\n", "            \"name\": \"d7_customers_w15min_pt_\",\n", "            \"type\": \"float\",\n", "            \"description\": \"d7_surge_cart_pct\",\n", "        },\n", "        {\n", "            \"name\": \"d14_customers_w15min_pt_\",\n", "            \"type\": \"float\",\n", "            \"description\": \"d7_surge_cart_pct\",\n", "        },\n", "        {\n", "            \"name\": \"d21_customers_w15min_pt_\",\n", "            \"type\": \"float\",\n", "            \"description\": \"d7_surge_cart_pct\",\n", "        },\n", "        {\n", "            \"name\": \"d28_customers_w15min_pt_\",\n", "            \"type\": \"float\",\n", "            \"description\": \"d7_surge_cart_pct\",\n", "        },\n", "        {\n", "            \"name\": \"perish_avail_today\",\n", "            \"type\": \"float\",\n", "            \"description\": \"surge_cart_pct_today\",\n", "        },\n", "        {\n", "            \"name\": \"d7_perish_avail\",\n", "            \"type\": \"float\",\n", "            \"description\": \"d7_surge_cart_pct\",\n", "        },\n", "        {\n", "            \"name\": \"d14_perish_avail\",\n", "            \"type\": \"float\",\n", "            \"description\": \"d7_surge_cart_pct\",\n", "        },\n", "        {\n", "            \"name\": \"d21_perish_avail\",\n", "            \"type\": \"float\",\n", "            \"description\": \"d7_surge_cart_pct\",\n", "        },\n", "        {\n", "            \"name\": \"d28_perish_avail\",\n", "            \"type\": \"float\",\n", "            \"description\": \"d7_surge_cart_pct\",\n", "        },\n", "        {\n", "            \"name\": \"packaged_avail_today\",\n", "            \"type\": \"float\",\n", "            \"description\": \"surge_cart_pct_today\",\n", "        },\n", "        {\n", "            \"name\": \"d7_packaged_avail\",\n", "            \"type\": \"float\",\n", "            \"description\": \"d7_surge_cart_pct\",\n", "        },\n", "        {\n", "            \"name\": \"d14_packaged_avail\",\n", "            \"type\": \"float\",\n", "            \"description\": \"d7_surge_cart_pct\",\n", "        },\n", "        {\n", "            \"name\": \"d21_packaged_avail\",\n", "            \"type\": \"float\",\n", "            \"description\": \"d7_surge_cart_pct\",\n", "        },\n", "        {\n", "            \"name\": \"d28_packaged_avail\",\n", "            \"type\": \"float\",\n", "            \"description\": \"d7_surge_cart_pct\",\n", "        },\n", "        {\n", "            \"name\": \"fnv_avail_today\",\n", "            \"type\": \"float\",\n", "            \"description\": \"surge_cart_pct_today\",\n", "        },\n", "        {\"name\": \"d7_fnv_avail\", \"type\": \"float\", \"description\": \"d7_surge_cart_pct\"},\n", "        {\"name\": \"d14_fnv_avail\", \"type\": \"float\", \"description\": \"d7_surge_cart_pct\"},\n", "        {\"name\": \"d21_fnv_avail\", \"type\": \"float\", \"description\": \"d7_surge_cart_pct\"},\n", "        {\"name\": \"d28_fnv_avail\", \"type\": \"float\", \"description\": \"d7_surge_cart_pct\"},\n", "        {\n", "            \"name\": \"transacting_users_today\",\n", "            \"type\": \"float\",\n", "            \"description\": \"d7_surge_cart_pct\",\n", "        },\n", "        {\n", "            \"name\": \"d7_transacting_users\",\n", "            \"type\": \"float\",\n", "            \"description\": \"d7_surge_cart_pct\",\n", "        },\n", "        {\n", "            \"name\": \"d14_transacting_users\",\n", "            \"type\": \"float\",\n", "            \"description\": \"d7_surge_cart_pct\",\n", "        },\n", "        {\n", "            \"name\": \"d21_transacting_users\",\n", "            \"type\": \"float\",\n", "            \"description\": \"d7_surge_cart_pct\",\n", "        },\n", "        {\n", "            \"name\": \"d28_transacting_users\",\n", "            \"type\": \"float\",\n", "            \"description\": \"d7_surge_cart_pct\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"date_\"],\n", "    \"sortkey\": [\"date_\"],\n", "    \"load_type\": \"append\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Growth RCA metrics\",\n", "}\n", "pb.to_redshift(main_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "35752920-ce00-418a-bef3-daa4b6a0a256", "metadata": {}, "outputs": [], "source": ["main_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "cb067efc-2a0b-40d9-9f71-6cfd40beecf6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}