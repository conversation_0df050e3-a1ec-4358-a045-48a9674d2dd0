{"cells": [{"cell_type": "code", "execution_count": null, "id": "c6c13a72-984e-47bf-a687-362b6e9fa73e", "metadata": {}, "outputs": [], "source": ["!pip install pandasql\n", "!pip install h3"]}, {"cell_type": "code", "execution_count": null, "id": "3c01f105-6066-4e8d-a82d-8a1d78fa3594", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "import pytz\n", "import h3"]}, {"cell_type": "code", "execution_count": null, "id": "7997ea6d-5b7e-4365-942e-765c5a6cf3fe", "metadata": {}, "outputs": [], "source": ["################# getting connection #######################\n", "con = pb.get_connection(\"[Warehouse] Redshift\")\n", "current_date = datetime.now(pytz.timezone(\"Asia/Kolkata\")).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "e42509cc-18ad-489d-8420-ab60498951e4", "metadata": {}, "outputs": [], "source": ["############ collecting lat,lng from spectrum table for past 30 days ##################\n", "my_query = \"\"\"\n", "drop table if exists all_eve;\n", "create temporary table all_eve as (\n", "select distinct traits__latitude as lat , traits__longitude as lng\n", "from spectrum.mobile_impression_data\n", "where at_date_ist between current_date-30 and current_date-1\n", "and name in ('App Launch' , 'Earliest Promise Time Widget V2 Shown')\n", "and traits__latitude is not null\n", "and traits__longitude is not null\n", "group by 1,2\n", ") ;\n", "\n", "select * from all_eve\n", "\"\"\".format()\n", "\n", "\n", "df = pd.read_sql_query(my_query, con)"]}, {"cell_type": "code", "execution_count": null, "id": "429ead11-bf1f-45ce-a5d5-b4570cc4139d", "metadata": {}, "outputs": [], "source": ["########## Assigning hexids to lat,lng ###################\n", "df[\"hexids\"] = df.apply(\n", "    lambda row: h3.geo_to_h3(lat=row.lat, lng=row.lng, resolution=9), axis=1\n", ")\n", "\n", "\n", "##########  taking data of distance matrix index from csv file  ###############\n", "my_query2 = \"\"\"\n", "select * from consumer.hex_index_data\n", "\"\"\".format()\n", "\n", "df2 = pd.read_sql_query(my_query2, con)\n", "\n", "# df2 = pd.read_csv('hex_index_data.csv')\n", "# df2 = df2[['index' ,'hex_id']].copy()\n", "# df2 = df2.rename(columns={\n", "#     \"index\": \"matrix_index\",\n", "#     \"hex_id\": \"hexids\"\n", "# })\n", "\n", "\n", "########   Assigning matrix index to each hexagon ############\n", "final_df = df.merge(df2, how=\"left\", on=\"hexids\")"]}, {"cell_type": "code", "execution_count": null, "id": "e890fa2d-fd76-4879-9f53-3fcc425c88d9", "metadata": {}, "outputs": [], "source": ["#######  putting data on table ############\n", "kwargs = {\n", "    \"schema_name\": \"consumer\",\n", "    \"table_name\": \"lat_lng_to_hexagon\",\n", "    \"primary_key\": [],\n", "    \"column_dtypes\": [\n", "        {\"name\": \"lat\", \"type\": \"FLOAT\", \"description\": \"lat\"},\n", "        {\"name\": \"lng\", \"type\": \"FLOAT\", \"description\": \"long\"},\n", "        {\"name\": \"hexids\", \"type\": \"VARCHAR\", \"description\": \"hexids\"},\n", "        {\"name\": \"matrix_index\", \"type\": \"FLOAT\", \"description\": \"matrix_index\"},\n", "    ],\n", "    \"sortkey\": [],\n", "    \"incremental_key\": \"\",\n", "    \"load_type\": \"truncate\",  # append, , truncate or upsert, rebuild\n", "    \"table_description\": \"This table contains the lat long mapped to hex9s\",\n", "}\n", "\n", "pb.to_redshift(final_df, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}