{"cells": [{"cell_type": "code", "execution_count": null, "id": "81cbd3c4-c90b-4271-860e-3e308f774472", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "id": "23885442-cb77-4e05-b0e4-e8c3927799ae", "metadata": {}, "outputs": [], "source": ["trino_conn = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "a2d335a5-135e-4a58-b134-ce8041a4c422", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"consumer_etls\",\n", "    \"table_name\": \"l2_wa_part3\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date_\", \"type\": \"date\", \"description\": \"NA\"},\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"NA\"},\n", "        {\"name\": \"facility_name\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"l0\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"l1\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"l2\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"fe_weighted_availability\", \"type\": \"double\", \"description\": \"NA\"},\n", "    ],\n", "    \"primary_key\": [\"item_id\", \"facility_id\", \"date_\", \"l2\"],\n", "    \"load_type\": \"truncate\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"notebook 1 of l2 x store wa breakdown\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "64145f65-187e-4f0b-a65b-3ef5e8a71d70", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "with availability_calculations as (\n", "select date_, \n", "       hour_, \n", "       fb.facility_id,\n", "       fb.facility_name,\n", "       fb.item_id, \n", "       fb.city,\n", "       icd.l0,\n", "       icd.l1,\n", "       icd.l2,\n", "       available_flag * (item_weight / nullif(total_item_weights,0)) * (hour_weight / nullif(total_hour_weights,0)) as facility_available_score,\n", "       1 * (item_weight / nullif(total_item_weights,0)) * (hour_weight / nullif(total_hour_weights,0)) as facility_all_available_score,\n", "       available_flag * (item_weight / nullif(total_item_weights,0)) * (hour_weight / nullif(total_hour_weights,0)) * (store_weight / nullif(total_store_weights,0)) as city_available_score,\n", "       1 * (item_weight / nullif(total_item_weights,0)) * (hour_weight / nullif(total_hour_weights,0)) * (store_weight / nullif(total_store_weights,0)) as city_all_available_score\n", "from blinkit_iceberg_staging.consumer_etls.l2_wa_part2 fb\n", "    join rpc.item_category_details icd on icd.item_id = fb.item_id and icd.lake_active_record=true\n", ")\n", "\n", "select date_,\n", "       case when city = 'Gurgaon' then 'HR-NCR'\n", "            when city = 'Ghaziabad' then 'UP-NCR' else city end as city,\n", "       facility_id facility_id,\n", "       facility_name,\n", "       l0,\n", "       l1,\n", "       l2,\n", "       100.0000 * sum(facility_available_score) / nullif(sum(facility_all_available_score),0) as fe_weighted_availability\n", "from availability_calculations\n", "group by 1,2,3,4,5,6,7\n", "\n", "union all\n", "\n", "select date_,\n", "       'Pan India' as city,\n", "       -1 as facility_id,\n", "       'All' as facility_name,\n", "       l0,\n", "       l1,\n", "       l2,\n", "       100.0000 * sum(city_available_score * pw) / nullif(sum(city_all_available_score * pw),0) as fe_weighted_availability\n", "from availability_calculations\n", "group by 1,2,3,4,5,6,7\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "64f76f09-e7b0-479d-8b6d-236e860226ed", "metadata": {}, "outputs": [], "source": ["pb.to_trino(sql, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}