{"cells": [{"cell_type": "code", "execution_count": null, "id": "81cbd3c4-c90b-4271-860e-3e308f774472", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "id": "23885442-cb77-4e05-b0e4-e8c3927799ae", "metadata": {}, "outputs": [], "source": ["trino_conn = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "a2d335a5-135e-4a58-b134-ce8041a4c422", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"consumer_etls\",\n", "    \"table_name\": \"l2_store_hourly_search\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"at_date_ist\", \"type\": \"date\", \"description\": \"NA\"},\n", "        {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"l1_category\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"l2_category\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"NA\"},\n", "        {\"name\": \"facility_name\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"searches\", \"type\": \"integer\", \"description\": \"NA\"},\n", "        {\"name\": \"search_atcs\", \"type\": \"integer\", \"description\": \"NA\"},\n", "    ],\n", "    \"primary_key\": [\"at_date_ist\", \"facility_id\", \"l2_category\"],\n", "    \"load_type\": \"truncate\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"search l2 x store\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "64145f65-187e-4f0b-a65b-3ef5e8a71d70", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "with base as (\n", "select\n", "    at_date_ist,\n", "    dp.l0_category,\n", "    dp.l1_category,\n", "    l2.l2_category,\n", "    mofm.facility_id,\n", "    mofm.facility_name,\n", "    city_name,\n", "    sum(search_dau) searches,\n", "    sum(dau_atc) search_atcs\n", "from dwh.agg_hourly_search_keyword_conversion_metrics kcm\n", "    join dwh.dim_keywords_l2_mapping l2 on kcm.keyword = l2.keyword and l2.is_current=true\n", "    left join (select distinct l2_category, l1_category, l0_category from dwh.dim_product where is_current=true) dp on dp.l2_category = l2.l2_category\n", "    join dwh.dim_merchant_outlet_facility_mapping mofm on mofm.frontend_merchant_id = kcm.merchant_id\n", "        and is_pos_outlet_active=1 \n", "        and is_backend_merchant_active=true \n", "        and is_frontend_merchant_active=true \n", "        and mofm.is_current \n", "        and is_mapping_enabled=true\n", "where at_hour_ist <= extract(hour from current_timestamp)\n", "  and at_date_ist in (current_date, current_date - interval '7' day, current_date - interval '14' day, current_date - interval '21' day, current_date - interval '28' day)\n", "  and merchant_name not in ('Overall')\n", "  and city_name not in ('Overall')\n", "group by 1,2,3,4,5,6,7\n", ")\n", "\n", "select\n", "    *\n", "from base\n", "union all\n", "select\n", "    at_date_ist,\n", "    l0_category,\n", "    l1_category,\n", "    l2_category,\n", "    -1 as facility_id,\n", "    'All' as facility_name,\n", "    'Pan India' as city_name,\n", "    sum(searches) searches,\n", "    sum(search_atcs) search_atcs\n", "from base\n", "group by 1,2,3,4,5,6,7\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "23e1d271-6e08-422a-88b3-fdc107724581", "metadata": {}, "outputs": [], "source": ["pb.to_trino(sql, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}