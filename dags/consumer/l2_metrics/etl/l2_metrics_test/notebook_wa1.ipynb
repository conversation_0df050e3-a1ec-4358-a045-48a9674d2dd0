{"cells": [{"cell_type": "code", "execution_count": null, "id": "81cbd3c4-c90b-4271-860e-3e308f774472", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "id": "23885442-cb77-4e05-b0e4-e8c3927799ae", "metadata": {}, "outputs": [], "source": ["trino_conn = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "a2d335a5-135e-4a58-b134-ce8041a4c422", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"consumer_etls\",\n", "    \"table_name\": \"l2_wa_part1\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"NA\"},\n", "        {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"NA\"},\n", "        {\"name\": \"facility_name\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"date_\", \"type\": \"date\", \"description\": \"NA\"},\n", "        {\"name\": \"hour_\", \"type\": \"integer\", \"description\": \"NA\"},\n", "        {\"name\": \"available_flag\", \"type\": \"integer\", \"description\": \"NA\"},\n", "    ],\n", "    \"primary_key\": [\"item_id\", \"facility_id\", \"date_\", \"hour_\"],\n", "    \"load_type\": \"truncate\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"notebook 1 of l2 x store wa breakdown\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "64145f65-187e-4f0b-a65b-3ef5e8a71d70", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "with fo as (\n", "select \n", "    x.facility_id,\n", "    m.facility_name facility_name,\n", "    cl.name city,\n", "    x.outlet_id\n", "from po.physical_facility_outlet_mapping x\n", "    inner join dwh.dim_merchant_outlet_facility_mapping m on m.pos_outlet_id = x.outlet_id \n", "            and is_pos_outlet_active=1 \n", "            and is_backend_merchant_active=true \n", "            and is_frontend_merchant_active=true \n", "            and is_current and \n", "            is_mapping_enabled=true\n", "    join retail.console_outlet co on co.facility_id = x.facility_id and business_type_id = 7 and co.lake_active_record=true\n", "    join retail.console_location cl on cl.id = co.tax_location_id and cl.lake_active_record=true\n", "where x.lake_active_record = true\n", "),\n", "\n", "hourly_inventory_snapshots as (\n", "select \n", "    *, \n", "    extract(hour from updated_at_ist) as hour_\n", "from supply_etls.hourly_inventory_snapshots \n", "where (date_ist = current_date or date_ist = current_date - interval '7' day or\n", "       date_ist = current_date - interval '14' day or date_ist = current_date - interval '21' day or date_ist = current_date - interval '28' day)\n", "),\n", "\n", "availability as (\n", "select fo.city, \n", "       item_id, \n", "       rpc.facility_id,\n", "       fo.facility_name,\n", "       date(updated_at_ist) as date_, \n", "       hour_,\n", "       case when current_inventory > 0 then 1 else 0 end as available_flag\n", "from hourly_inventory_snapshots rpc\n", "    join fo on rpc.facility_id = fo.facility_id\n", "where hour_ <= extract(hour from current_timestamp)\n", ")\n", "select * from availability\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "8c7203a2-9c64-49e7-8830-48eaf6a07348", "metadata": {}, "outputs": [], "source": ["pb.to_trino(sql, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}