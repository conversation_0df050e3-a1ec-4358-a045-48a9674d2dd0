{"cells": [{"cell_type": "code", "execution_count": null, "id": "81cbd3c4-c90b-4271-860e-3e308f774472", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "id": "23885442-cb77-4e05-b0e4-e8c3927799ae", "metadata": {}, "outputs": [], "source": ["trino_conn = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "a2d335a5-135e-4a58-b134-ce8041a4c422", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"consumer_etls\",\n", "    \"table_name\": \"l2_wa_part2\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date_\", \"type\": \"date\", \"description\": \"NA\"},\n", "        {\"name\": \"hour_\", \"type\": \"integer\", \"description\": \"NA\"},\n", "        {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"NA\"},\n", "        {\"name\": \"facility_name\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"NA\"},\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"available_flag\", \"type\": \"integer\", \"description\": \"NA\"},\n", "        {\"name\": \"item_weight\", \"type\": \"double\", \"description\": \"NA\"},\n", "        {\"name\": \"total_item_weights\", \"type\": \"double\", \"description\": \"NA\"},\n", "        {\"name\": \"store_weight\", \"type\": \"double\", \"description\": \"NA\"},\n", "        {\"name\": \"total_store_weights\", \"type\": \"double\", \"description\": \"NA\"},\n", "        {\"name\": \"hour_weight\", \"type\": \"double\", \"description\": \"NA\"},\n", "        {\"name\": \"total_hour_weights\", \"type\": \"double\", \"description\": \"NA\"},\n", "        {\"name\": \"pw\", \"type\": \"double\", \"description\": \"NA\"},\n", "    ],\n", "    \"primary_key\": [\"item_id\", \"facility_id\", \"date_\", \"hour_\"],\n", "    \"load_type\": \"truncate\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"notebook 2 of l2 x store wa breakdown\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "64145f65-187e-4f0b-a65b-3ef5e8a71d70", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "with city_item_score as (\n", "select city,\n", "       assortment_type,\n", "       cast(item_id as integer) item_id,\n", "       1.0000000*cast(cart_penetration as double) as cart_penetration,\n", "       1.0000000*cast(weights as double) as item_weight\n", "from supply_etls.city_item_weights\n", "where updated_at in (select max(updated_at) from supply_etls.city_item_weights)\n", "group by 1,2,3,4,5\n", "),\n", "\n", "city_store_score as (\n", "select city,\n", "       facility_id, \n", "       1.0000000*cast(store_weight as double) as store_weight\n", "from supply_etls.city_store_weights\n", "where updated_at in (select max(updated_at) from supply_etls.city_store_weights)\n", "group by 1,2,3\n", "),\n", "\n", "city_hour_score as (\n", "select distinct city, \n", "       order_hour as hour_, \n", "       1.0000000*cast(weights as double) as hour_weight\n", "from supply_etls.city_hour_weights\n", "where updated_at in (select max(updated_at) from supply_etls.city_hour_weights)\n", "group by 1,2,3\n", "),\n", "\n", "availability as (\n", "select * from blinkit_iceberg_staging.consumer_etls.l2_wa_part1\n", "),\n", "\n", "score_merge_base as (\n", "select hb.*, \n", "       cis_0.item_weight, \n", "       css_0.store_weight, \n", "       chs.hour_weight\n", "from availability hb\n", "    left join city_item_score cis_0 on hb.city = cis_0.city and hb.item_id = cis_0.item_id\n", "    left join city_store_score css_0 on hb.facility_id = css_0.facility_id\n", "    left join city_hour_score chs on chs.city = hb.city and chs.hour_ = hb.hour_\n", "),\n", "\n", "aggregate_item_score_merge as (\n", "select date_, \n", "       hour_, \n", "       facility_id, \n", "       facility_name,\n", "       sum(item_weight) as total_item_weights\n", "from score_merge_base\n", "group by 1,2,3,4\n", "),\n", "\n", "aggregate_store_score_merge as (\n", "select date_, \n", "       hour_, \n", "       city, \n", "       sum(store_weight) as total_store_weights\n", "from score_merge_base\n", "group by 1,2,3\n", "),\n", "\n", "aggregate_hour_score_merge as (\n", "select date_, \n", "       city, \n", "       facility_id,\n", "       facility_name,\n", "       sum(hour_weight) as total_hour_weights\n", "from score_merge_base\n", "group by 1,2,3,4\n", "),\n", "\n", "pw as (\n", "select\n", "    city,\n", "    cast(sum(weight) as double) pw\n", "from supply_etls.city_weights\n", "where updated_at in (select max(updated_at) from supply_etls.city_weights)\n", "group by 1\n", "),\n", "\n", "final_base as (\n", "select smb.date_, \n", "       smb.hour_, \n", "       smb.facility_id,\n", "       smb.facility_name,\n", "       item_id,\n", "       ahsm.city,\n", "       available_flag,\n", "       item_weight, \n", "       total_item_weights, \n", "       store_weight, \n", "       total_store_weights, \n", "       hour_weight, \n", "       total_hour_weights,\n", "       pw\n", "from score_merge_base smb\n", "    left join aggregate_item_score_merge aism on smb.date_ = aism.date_ and smb.hour_ = aism.hour_ and smb.facility_id = aism.facility_id\n", "    left join aggregate_store_score_merge assm on smb.date_ = assm.date_ and smb.hour_ = assm.hour_ and smb.city = assm.city\n", "    left join aggregate_hour_score_merge ahsm on smb.date_ = ahsm.date_ and smb.city = ahsm.city and smb.facility_id = ahsm.facility_id\n", "    left join pw on pw.city = ahsm.city\n", ")\n", "select * from final_base\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "437325fd-2709-43fe-8f2e-4fbf655c877e", "metadata": {}, "outputs": [], "source": ["pb.to_trino(sql, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}