{"cells": [{"cell_type": "code", "execution_count": null, "id": "d392feae-7dce-42fb-887d-58963989f95c", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "id": "bb3d8656-64e4-4c55-a4c0-0df126387a19", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "8b5318e9-bfcd-4233-b2ab-b02f44a2ae79", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1M1SFaitQNBPLrxndZ6c2ohYdSvzaiJWvPvPOPXcheEM\"\n", "sheet_name = \"control_dates_for_backfill\"\n", "df_input = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "58695b43-99ff-4a23-adba-5c6392347c26", "metadata": {}, "outputs": [], "source": ["start_date = df_input.start_date[0]\n", "end_date = df_input.end_date[0]\n", "start_dt = df_input.start_dt[0]\n", "end_dt = df_input.end_dt[0]"]}, {"cell_type": "code", "execution_count": null, "id": "459ba9c7-6230-42db-9b77-9b8107e8b13e", "metadata": {}, "outputs": [], "source": ["print(start_date)\n", "print(end_date)\n", "print(start_dt)\n", "print(end_dt)"]}, {"cell_type": "code", "execution_count": null, "id": "36efb23d-c465-4826-a0fa-afb025e485a8", "metadata": {}, "outputs": [], "source": ["# start_date = \"2025-06-02\"\n", "# end_date = \"2025-06-08\"\n", "# start_dt = \"20250602\"\n", "# end_dt = \"20250609\""]}, {"cell_type": "markdown", "id": "b5df0d7c-40ea-432a-a2da-a7d32ba31209", "metadata": {}, "source": ["Discount"]}, {"cell_type": "code", "execution_count": null, "id": "df32b386-b965-4432-aa29-83470443a9df", "metadata": {"tags": []}, "outputs": [], "source": ["query1 = f\"\"\"\n", "\n", "select\n", "cast(date(date_trunc('week',od.order_deliver_ts_ist)) as var<PERSON>r) as week,\n", "cast(date(date_trunc('month',od.order_deliver_ts_ist)) as varchar) as month,\n", "oid.order_id,    \n", "oid.outlet_id, \n", "coalesce(p.l1_category,'null_category') as l1_category,\n", "case when oid.product_id in (526255,560227,560226,560225,560224) then 'Photo_Printing'\n", "    when oid.product_id in (541364,541365,541366) then 'passport_photo'\n", "    when lower(product_name) like '%%%%black%%%%' then 'Black_N_White'\n", "    when lower(product_name) like '%%%%photo%%%%' then 'Photo'\n", "    when oid.product_id in (573298,573299,573300,573301,573302,573303,573304,573305,577405,578226,594380,594381,594382,594384,594379) then 'Color'\n", "    else 'Color' end as product,     \n", "     \n", "sum(oid.total_discount_amount) as total_discount_amount\n", "\n", "from dwh.fact_sales_order_item_details oid\n", "left join dwh.fact_sales_order_details od on oid.order_id=od.order_id\n", "left join dwh.dim_product p on p.product_id=oid.product_id and p.is_current=true\n", "\n", "left join ba_etls.pnl_all_category_tag_ ct0 on ct0.l0_category_id=cast(p.l0_category_id as varchar) and ct0.l1_category_id='-1' and ct0.l2_category_id='-1'\n", "left join ba_etls.pnl_all_category_tag_ ct1 on ct1.l1_category_id=cast(p.l1_category_id as varchar) and ct1.l2_category_id='-1'\n", "left join ba_etls.pnl_all_category_tag_ ct2 on ct2.l2_category_id=cast(p.l2_category_id as varchar)\n", "\n", "where oid.order_create_dt_ist >= date'{start_date}' - interval '1' day\n", "    and od.order_create_dt_ist >=  date'{start_date}' - interval '1' day\n", "    and od.order_deliver_ts_ist >=  date'{start_date}'\n", "    and od.order_deliver_ts_ist < date'{end_date}' + interval '1' day\n", "    and oid.is_internal_order = false\n", "    and oid.procured_quantity > 0\n", "    and oid.unit_selling_price > 0\n", "    and oid.order_current_status = 'DELIVERED'\n", "    and coalesce(p.brand_name,'blank') != 'Apple'\n", "    and p.l1_category_id = 2039\n", "\n", "group by 1,2,3,4,5,6\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "759b3699-47bc-4bd6-a154-a30bacf61e75", "metadata": {}, "outputs": [], "source": ["df1 = pd.read_sql(query1, con)"]}, {"cell_type": "markdown", "id": "06cb0ffc-b5d3-4df7-95ff-78aeadcfab65", "metadata": {}, "source": ["GMV & Margin"]}, {"cell_type": "code", "execution_count": null, "id": "f2bb41eb-ba62-4aea-ba31-a842bd64a0e2", "metadata": {"tags": []}, "outputs": [], "source": ["query3 = f\"\"\"\n", "-- select week, sum(checkout_cost_qty) as checkout_cost_qty, sum(delivered_cost_qty) as delivered_cost_qty, sum(delivered_gmv) as delivered_gmv, sum(checkout_gmv) as checkout_gmv, sum(delivered_orders) as delivered_orders, sum(checkout_orders) as checkout_orders,sum(delivered_carts) as delivered_carts,sum(checkout_carts) as checkout_carts,sum(delivered_qty) as delivered_qty,sum(checkout_qty) as checkout_qty \n", "-- from\n", "-- (\n", "select\n", "cast(date(date_trunc('week',fs.cart_checkout_ts_ist)) as varchar) as week,\n", "cast(date(date_trunc('month',fs.cart_checkout_ts_ist)) as varchar) as month,\n", "oi.order_id,\n", "oi.outlet_id, \n", "coalesce(c.l1_category,'null_category') as l1_category,\n", "\n", "    case when oi.product_id in (526255,560227,560226,560225,560224) then 'Photo_Printing'\n", "        when oi.product_id in (541364,541365,541366) then 'passport_photo'\n", "        when lower(product_name) like '%%%%black%%%%' then 'Black_N_White'\n", "        when lower(product_name) like '%%%%photo%%%%' then 'Photo'\n", "        when oi.product_id in (573298,573299,573300,573301,573302,573303,573304,573305,577405,578226,594380,594381,594382,594384,594379) then 'Color'\n", "        else 'Color' end as product, \n", "    \n", "    sum(case when oi.product_id in (594381,594384,541366) then 2\n", "        when oi.product_id in (573298,573299,573300,573303,573304,594380,594382) then 3\n", "        when oi.product_id in (573302,577405,578226) then 4\n", "        when oi.product_id in (573301) then 5\n", "        when oi.product_id in (594379) then 15\n", "        when oi.product_id in (573305) then 26\n", "        else oi.product_quantity end) as checkout_cost_qty,\n", "    \n", "    sum(case when oi.product_id in (594381,594384,541366) then 2\n", "    when oi.product_id in (573298,573299,573300,573303,573304,594380,594382) then 3\n", "    when oi.product_id in (573302,577405,578226) then 4\n", "    when oi.product_id in (573301) then 5\n", "    when oi.product_id in (594379) then 15\n", "    when oi.product_id in (573305) then 26\n", "    else oi.procured_quantity end) as delivered_cost_qty,\n", "\n", "sum(case when oi.order_current_status = 'DELIVERED' then oi.unit_selling_price*oi.procured_quantity else 0 end) as delivered_gmv,\n", "sum(oi.unit_selling_price*oi.product_quantity) as checkout_gmv,\n", "\n", "count(distinct case when oi.order_current_status = 'DELIVERED' then oi.order_id end) as delivered_orders,\n", "count(distinct oi.order_id) as checkout_orders,\n", "\n", "count(distinct case when oi.order_current_status = 'DELIVERED' then oi.cart_id end) as delivered_carts,\n", "count(distinct oi.cart_id) as checkout_carts,\n", "\n", "sum(case when oi.order_current_status = 'DELIVERED' then oi.procured_quantity else 0 end) as delivered_qty,\n", "sum(oi.product_quantity) as checkout_qty\n", "\n", "\n", "from dwh.fact_sales_order_item_details oi\n", "inner join dwh.fact_sales_order_details fs on fs.order_id = oi.order_id\n", "left join dwh.dim_product c on c.product_id = oi.product_id and c.is_current=true\n", "\n", "\n", "where oi.order_create_dt_ist >= date'{start_date}' - interval '1' day\n", "    and fs.order_create_dt_ist >=  date'{start_date}' - interval '1' day\n", "    and fs.cart_checkout_ts_ist >=  date'{start_date}'\n", "    and fs.cart_checkout_ts_ist < date'{end_date}' + interval '1' day\n", "    and oi.is_internal_order = false\n", "    --and oi.procured_quantity > 0\n", "    and oi.unit_selling_price > 0\n", "    --and oi.order_current_status = 'DELIVERED'\n", "    and coalesce(c.brand_name,'blank') != 'Apple' \n", "    and c.l1_category_id = 2039\n", "group by 1,2,3,4,5,6\n", "-- )\n", "-- group by 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "2a8e06ed-686a-40c8-869f-113c1ef70a44", "metadata": {}, "outputs": [], "source": ["df3 = pd.read_sql(query3, con)"]}, {"cell_type": "markdown", "id": "b19d1df3-3d35-472f-bf48-3124bbf604ea", "metadata": {}, "source": ["Refunds"]}, {"cell_type": "code", "execution_count": null, "id": "73c7c901-c2cf-4131-b9d0-613ea7949ba8", "metadata": {"tags": []}, "outputs": [], "source": ["query6 = f\"\"\"\n", "\n", "with refunds_int as (\n", "        select \n", "            date(refund_date) as date_,\n", "            r.outlet_id,\n", "            cart_id,\n", "            order_id,\n", "            r.product_id,\n", "            p.l1_category,\n", "            case when r.product_id in (526255,560227,560226,560225,560224) then 'Photo_Printing'\n", "                 when r.product_id in (541364,541365,541366) then 'passport_photo'\n", "                 when lower(product_name) like '%%black%%' then 'Black_N_White'\n", "                 when lower(product_name) like '%%photo%%' then 'Photo'\n", "                 when r.product_id in (573298,573299,573300,573301,573302,573303,573304,573305,577405,578226,594380,594381,594382,594384,594379) then 'Color'\n", "                 else 'Color' end as product,\n", "            sum(cast(coalesce(case when rm_loss = 0 then net_refund end,0) as double)) pure_refund_loss,\n", "            sum(cast(coalesce(case when refund_type = 'non_dsr_refund' then rm_loss end,0) as double)) non_dsr_rm_loss,\n", "            sum(cast(coalesce(case when refund_type = 'dsr_refund' then rm_loss end,0) as double)) dsr_rm_loss\n", "\n", "        from  dwh.flat_order_item_refunds r\n", "        left join dwh.dim_product as p on r.product_id = p.product_id and p.is_current\n", "        where refund_date >=  date'{start_date}'\n", "        and refund_date <  date'{end_date}' + interval '1' day\n", "        and p.l1_category_id = 2039\n", "        group by 1,2,3,4,5,6,7\n", ")\n", "\n", "\n", "\n", ", charges_refund as (\n", "    select\n", "    date(refund_date) as date_,\n", "    o.outlet_id,\n", "    o.cart_id,\n", "    o.order_id,\n", "    sum(case when o.product_id = 0 then net_refund else 0 end) as charges_based_rf\n", "    from dwh.flat_order_item_refunds as o\n", "    join refunds_int as r on r.cart_id = o.cart_id\n", "    where refund_date >=  date'{start_date}'\n", "    and refund_date <  date'{end_date}' + interval '1' day\n", "    group by 1,2,3,4\n", ")\n", "\n", "\n", ", allocated_refund as (\n", "    select \n", "    date_,\n", "    outlet_id,\n", "    cart_id,\n", "    order_id,\n", "    o.product_id,\n", "    p.l1_category,\n", "    o.product,\n", "    sum(pure_refund_loss + non_dsr_rm_loss + dsr_rm_loss) as rf\n", "    from refunds_int as o\n", "    left join dwh.dim_product as p on p.product_id = o.product_id and p.is_current\n", "    where o.product_id <> 0\n", "    group by 1,2,3,4,5,6,7\n", ")\n", "\n", "\n", ", final_allocation as (\n", "    select\n", "    date(date_trunc('week',a.date_)) as week,\n", "    date(date_trunc('month',a.date_)) as month,\n", "    a.outlet_id,\n", "    a.cart_id,\n", "    a.order_id,\n", "    a.product_id,\n", "    a.product,\n", "    a.l1_category,\n", "    coalesce(rf,0) as rf,\n", "    coalesce(rf/sum(rf) over(partition by a.cart_id),0) as perc,\n", "    case when coalesce(rf,0) = 0 then 0 else coalesce(rf/sum(rf) over(partition by a.cart_id),0)*coalesce(charges_based_rf,0) end as allocated_amt,\n", "    case when coalesce(rf,0) = 0 then 0 else coalesce(rf/sum(rf) over(partition by a.cart_id),0)*coalesce(charges_based_rf,0) end + rf as final_rf\n", "    from allocated_refund as a\n", "    join charges_refund as c on a.cart_id = c.cart_id\n", ")\n", "\n", "select\n", "    week,\n", "    month, \n", "    outlet_id, \n", "    order_id,\n", "    product, \n", "    l1_category,\n", "    sum(final_rf) as final_refund\n", "from final_allocation \n", "group by 1,2,3,4,5,6\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "e2f33522-8dbe-4687-bb1b-b649a94594be", "metadata": {}, "outputs": [], "source": ["df6 = pd.read_sql(query6, con)\n", "df6[\"final_refund\"] = df6[\"final_refund\"].astype(float)"]}, {"cell_type": "markdown", "id": "9574e667-7f66-4fd3-8ef1-c3f066ed542b", "metadata": {}, "source": ["Returns"]}, {"cell_type": "code", "execution_count": null, "id": "e111b454-2aac-4e8e-9c82-b36c875ecb6c", "metadata": {"tags": []}, "outputs": [], "source": ["query7 = f\"\"\"\n", "\n", "-- select week, sum(return_loss) as return_loss from (\n", "select\n", "cast(date(date_trunc('week',day)) as var<PERSON><PERSON>) as week,\n", "cast(date(date_trunc('month',day)) as var<PERSON>r) as month,\n", "order_id,\n", "outlet_id,\n", "p.l1_category,\n", "\n", "    case when r.product_id in (526255,560227,560226,560225,560224) then 'Photo_Printing'\n", "        when r.product_id in (541364,541365,541366) then 'passport_photo'\n", "        when lower(r.product_name) like '%%%%black%%%%' then 'Black_N_White'\n", "        when lower(r.product_name) like '%%%%photo%%%%' then 'Photo'\n", "        when r.product_id in (573298,573299,573300,573301,573302,573303,573304,573305,577405,578226,594380,594381,594382,594384,594379) then 'Color'\n", "        else 'Color' end as product, \n", "\n", "     \n", "sum(cast(coalesce(return_loss,0) as double)) as return_loss\n", "\n", "from dwh.flat_order_item_returns r\n", "left join dwh.dim_product p on r.product_id=p.product_id and p.is_current\n", "left join ba_etls.pnl_all_category_tag_ ct0 on ct0.l0_category_id=cast(p.l0_category_id as varchar) and ct0.l1_category_id='-1' and ct0.l2_category_id='-1'\n", "left join ba_etls.pnl_all_category_tag_ ct1 on ct1.l1_category_id=cast(p.l1_category_id as varchar) and ct1.l2_category_id='-1'\n", "left join ba_etls.pnl_all_category_tag_ ct2 on ct2.l2_category_id=cast(p.l2_category_id as varchar)\n", "\n", "where day >= date'{start_date}'\n", "and day < date'{end_date}' + interval '1' day\n", "and p.l1_category_id=2039\n", "group by 1,2,3,4,5,6\n", "-- ) group by 1\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "e945b30b-b31b-47e7-9f99-8f0e61604a1d", "metadata": {}, "outputs": [], "source": ["df7 = pd.read_sql(query7, con)"]}, {"cell_type": "code", "execution_count": null, "id": "31979dd7-fc22-42a7-bc78-5c31374bfd7b", "metadata": {}, "outputs": [], "source": ["df7 = pd.merge(\n", "    df6, df7, on=[\"week\", \"month\", \"order_id\", \"outlet_id\", \"l1_category\", \"product\"], how=\"outer\"\n", ").<PERSON>na(0)"]}, {"cell_type": "code", "execution_count": null, "id": "c45c1d94-ddb0-4b5a-962d-2fbfd8d6cbac", "metadata": {}, "outputs": [], "source": ["df7[\"final_return_loss\"] = df7[\"return_loss\"].astype(float)\n", "# df7[\"final_refund\"] = df7[\"pure_refund_loss\"].astype(float) + df7[\"non_dsr_rm_loss\"].astype(float)"]}, {"cell_type": "markdown", "id": "6092927f-6d2e-4b85-a074-f269aabffe6a", "metadata": {}, "source": ["DC and Surge"]}, {"cell_type": "code", "execution_count": null, "id": "35efe74f-3381-4109-adb2-2559ddef7cbd", "metadata": {}, "outputs": [], "source": ["query8 = f\"\"\"\n", "\n", "\n", "with order_charges as (\n", "    select\n", "    cart_id,\n", "    order_id,\n", "    cast(total_selling_price as double)/cast(sum(total_selling_price) over (partition by cart_id) as double) as order_ratio\n", "\n", "    from dwh.fact_sales_order_details oi\n", "    where oi.order_create_dt_ist >= date'{start_date}' - interval '1' day\n", "),\n", "\n", "base as (\n", "    select * from\n", "    (\n", "        select\n", "        cart_id,\n", "        order_id,\n", "        c.product_id,\n", "        c.product_name,\n", "        c.l1_category,\n", "        cast(case when c.l1_category_id = 2039 then oi.unit_selling_price*oi.procured_quantity else 0 end as double)/cast(sum(oi.unit_selling_price*oi.procured_quantity) over (partition by order_id) as double) as paas_ratio\n", "        \n", "        from dwh.fact_sales_order_item_details oi\n", "        left join dwh.dim_product c on c.product_id = oi.product_id and c.is_current=true\n", "        \n", "        where oi.order_create_dt_ist >= date'{start_date}' - interval '1' day\n", "    )\n", "    \n", "    where paas_ratio>0\n", ")\n", "\n", "-- select week, \n", "-- sum(convenience_charge) as convenience_charge,\n", "-- sum(eco_friendly_packaging_charges) as eco_friendly_packaging_charges,\n", "-- sum(night_charge) as night_charge,\n", "-- sum(delivery_cost) as delivery_cost,\n", "-- sum(slot_charges) as slot_charges,\n", "-- sum(gift_packing_charges) as gift_packing_charges,\n", "-- sum(small_cart_charges) as small_cart_charges,\n", "-- sum(festival_charges) as festival_charges,\n", "-- sum(diff_additional_charges) as diff_additional_charges,\n", "-- sum(additional_charges_amount) as additional_charges_amount\n", "\n", "-- from\n", "-- (\n", "select\n", "cast(date(date_trunc('week', order_deliver_ts_ist)) as var<PERSON>r) as week,\n", "cast(date(date_trunc('month', order_deliver_ts_ist)) as varchar) as month,\n", "    case when b.product_id in (526255,560227,560226,560225,560224) then 'Photo_Printing'\n", "        when b.product_id in (541364,541365,541366) then 'passport_photo'\n", "        when lower(b.product_name) like '%%%%black%%%%' then 'Black_N_White'\n", "        when lower(b.product_name) like '%%%%photo%%%%' then 'Photo'\n", "        when b.product_id in (573298,573299,573300,573301,573302,573303,573304,573305,577405,578226,594380,594381,594382,594384,594379) then 'Color'\n", "        else 'Color' end as product, \n", "l1_category,\n", "a.order_id,\n", "outlet_id,\n", "sum(a.convenience_charge*b.paas_ratio*o.order_ratio) AS convenience_charge,\n", "sum(eco_friendly_packaging_cost*b.paas_ratio*o.order_ratio) AS eco_friendly_packaging_charges,\n", "sum(e.night_charges*b.paas_ratio*o.order_ratio) AS night_charge,\n", "sum(total_delivery_cost*b.paas_ratio*o.order_ratio) AS delivery_cost,\n", "sum(slot_charges*b.paas_ratio*o.order_ratio) AS slot_charges,\n", "sum(gift_packing_charge*b.paas_ratio*o.order_ratio) AS gift_packing_charges,\n", "sum(small_cart_charge*b.paas_ratio*o.order_ratio) AS small_cart_charges,\n", "sum(festival_charge*b.paas_ratio*o.order_ratio) AS festival_charges,\n", "sum(diff_additional_charges*b.paas_ratio*o.order_ratio) as diff_additional_charges,\n", "sum(additional_charges_amount*b.paas_ratio*o.order_ratio) as additional_charges_amount\n", "\n", "from\n", "dwh.fact_sales_order_details a\n", "left join dwh.fact_sales_order_details_ext e on a.order_id = e.order_id and e.order_create_dt_ist >= date'{start_date}' - interval '1' day\n", "join base b on b.cart_id=a.cart_id\n", "join order_charges as o on o.cart_id = b.cart_id\n", "\n", "where \n", "a.order_create_dt_ist >= date'{start_date}' - interval '1' day\n", "and order_deliver_ts_ist >= date'{start_date}' \n", "and order_deliver_ts_ist <  date'{end_date}' + interval '1' day\n", "and order_current_status = 'DELIVERED'\n", "and lower(order_type) not like '%%%%internal%%%%'\n", "-- and a.order_id = 1050805477\n", "group by 1,2,3,4,5,6\n", "-- )\n", "-- group by 1\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "4c845bc6-9f07-4678-98de-61dfc1dd30e1", "metadata": {}, "outputs": [], "source": ["df8 = pd.read_sql(query8, con)"]}, {"cell_type": "code", "execution_count": null, "id": "78985ec2-8af4-47c0-a2e8-60b377c26580", "metadata": {}, "outputs": [], "source": ["df7.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e15c1255-5065-4c21-b843-48935cc73fec", "metadata": {}, "outputs": [], "source": ["dfb = (\n", "    df1.merge(\n", "        df3, how=\"outer\", on=[\"week\", \"month\", \"order_id\", \"outlet_id\", \"l1_category\", \"product\"]\n", "    )\n", "    .merge(\n", "        df7, how=\"outer\", on=[\"week\", \"month\", \"order_id\", \"outlet_id\", \"l1_category\", \"product\"]\n", "    )\n", "    .fillna(0)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ce9f25d3-bb25-42d4-9019-0cb8b7bec60c", "metadata": {}, "outputs": [], "source": ["query9 = \"\"\"\n", "\n", "with iroo as (\n", "select\n", "cart_id,\n", "outlet_id,\n", "order_id\n", "from dwh.fact_sales_order_details \n", "where order_create_dt_ist >= date'{start_date}'-interval '2' day\n", "and order_create_dt_ist < date'{end_date}'+interval '2' day\n", "group by 1,2,3\n", "\n", ")\n", ",\n", "base as (\n", "select\n", "account_date,\n", "tp.delivery_driver_id,\n", "-- tp.trip_id,\n", "coalesce(od.outlet_id,od2.outlet_id,iroo.outlet_id) as outlet_id,\n", "coalesce(od.order_id,od2.order_id,iroo.order_id) as order_id,\n", "coalesce(od.cart_id,od2.cart_id,iroo.cart_id) as cart_id,\n", "sum(orders) as orders,\n", "sum((coalesce(tp.base_pay,0)+coalesce(tp.drop_distance_pay,0)+coalesce(tp.pickup_distance_pay,0)+coalesce(tp.surge_pay,0)+coalesce(tp.trip_ming,0)+coalesce(tp.wait_pay,0)+coalesce(tp.ldrp,0)+coalesce(incentive_pay,0))/coalesce(od.cnt,1.00)) as order_cost\n", "from zomato.logs_dashboard_etls.trip_pay tp\n", "left join (\n", "    select \n", "    *,\n", "    count(*) over (partition by trip_id) as cnt \n", "    from dwh.fact_supply_chain_order_details \n", "    where  order_checkout_dt_ist>=--date'2024-02-29'\n", "                     date'{start_date}'-interval '1' day\n", "                    --  and trip_id='**********'\n", "          ) od on od.trip_id=cast(tp.trip_id as varchar)\n", "left join dwh.fact_supply_chain_order_details od2 \n", "on cast(od2.order_id as varchar)=case when tab_id1 like 'XL%%' then coalesce(split_part(tab_id1, '-', 2),'-1') else tab_id1 end\n", "and od2.order_checkout_dt_ist>=--date'2024-02-29'\n", "                date'{start_date}'-interval '2' day\n", "left join iroo on tp.tab_id1=cast(iroo.order_id as varchar)\n", "where\n", "tp.dt >= '{start_dt}' \n", "and tp.dt < DATE_FORMAT((cast(date_format(date_parse('{end_dt}', '%%Y%%m%%d'), '%%Y-%%m-%%d') as date) + interval '1' day), '%%Y%%m%%d')\n", "group by 1,2,3,4,5\n", ")\n", "\n", ",\n", "non_null_cart_base as (\n", "select\n", "account_date,\n", "delivery_driver_id,\n", "outlet_id,\n", "order_id,\n", "cart_id,\n", "sum(orders) as orders,\n", "sum(order_cost) as order_cost\n", "from base\n", "where cart_id is not null\n", "-- and delivery_driver_id=2324323\n", "group by 1,2,3,4,5\n", ")\n", "\n", ",\n", "null_cart_base as (\n", "select\n", "account_date,\n", "delivery_driver_id,\n", "sum(orders) as driver_orders,\n", "sum(case when cart_id is null then order_cost end) as non_cart_order_cost\n", "from base\n", "group by 1,2\n", ")\n", "\n", ",\n", "base_prep as (\n", "select\n", "b.account_date,\n", "b.delivery_driver_id,\n", "outlet_id,\n", "order_id,\n", "cart_id,\n", "order_cost,\n", "driver_orders,\n", "non_cart_order_cost,\n", "case when c.driver_orders=0 then 0 \n", "     when c.driver_orders is null then 0\n", "     else cast(coalesce(c.non_cart_order_cost,0) as double)/c.driver_orders end as non_cart_order_cost_per_order\n", "     \n", "from non_null_cart_base b\n", "left join null_cart_base c on b.account_date=c.account_date and b.delivery_driver_id=c.delivery_driver_id\n", ")\n", ",\n", "base1 as (\n", "select\n", "account_date,\n", "delivery_driver_id,\n", "outlet_id,\n", "order_id,\n", "cart_id,\n", "coalesce(order_cost,0)+coalesce(non_cart_order_cost_per_order,0) as cart_cost\n", "from base_prep\n", ")\n", "\n", ",\n", "jumbo_ledger as (\n", "select \n", "account_date,\n", "delivery_driver_id,\n", "sum(coalesce(bonus_pay_amount,0)) as bonus_pay_amount,\n", "sum(coalesce(calls_per_order_incentive_amount,0)) as calls_per_order_incentive_amount,\n", "sum(coalesce(order_rating_incentive_amount,0)) as order_rating_incentive_amount,\n", "sum(coalesce(reservationpay_amount,0)) as reservationpay_amount,\n", "sum(coalesce(hourly_pay,0)) as hourly_pay,\n", "sum(coalesce(offer_incentive,0)) as offer_incentive,\n", "sum(coalesce(weekly_ming_amount,0)) as weekly_ming_amount,\n", "sum(coalesce(weekly_milestone_amount,0)) as weekly_milestone_amount,\n", "sum(coalesce(ming_amount,0)) as ming_amount,\n", "sum(coalesce(daily_milestone_amount,0)) as daily_milestone_amount,\n", "sum(acc_orders) as total_orders\n", "from logistics_data_etls.blinkit_rider_daily_accounting_summary_v1\n", "where account_date >=date'{start_date}'\n", "and account_date < date'{end_date}' + interval '1' day\n", "-- and delivery_driver_id in (1629702,1987535,3430465)  --= 3232975 -- \n", "group by 1,2\n", ")\n", "\n", ",\n", "offers as (\n", "with offer_base as (\n", "select distinct week(date_parse(p.dt,'%%Y%%m%%d')) as week, \n", "driver_id, info,\n", "try_cast(format_datetime(from_unixtime(timestamp/1000),'yyyy-MM-dd HH:mm:ss') as timestamp) as calc_at,\n", "from_unixtime(timestamp),\n", "dt,\n", "try(try(info[1])[1]) as RC_id,\n", "try(try(info[1])[2]) as Offer_name,\n", "total_achieved as amount,\n", "rank() over(partition by driver_id,try(try(info[1])[2]) order by \n", "cast(format_datetime(from_unixtime(timestamp/1000),'yyyy-MM-dd HH:mm:ss') as timestamp) desc ) rnk\n", "from zomato.jumbo2.driver_offer_payouts p\n", "where p.dt between date_format(date_trunc('week',current_date),'%%Y%%m%%d') and  date_format(current_date,'%%Y%%m%%d')\n", "and driver_id in (\n", "        select distinct delivery_driver_id \n", "        from zomato.carthero_prod.delivery_driver_service_mappings\n", "        where driver_service_id=13\n", "        and system_enabled=1 and driver_enabled=1\n", "        )\n", ")\n", ",\n", "rc as (\n", "select _id,\n", "date(from_unixtime(try(validity_period[1]))) as validity_end,\n", "date(from_unixtime(try(validity_period[2]))) as validity_start\n", "from zomato.mongo_rule_engine_prod.rule_configs\n", "where dt > '20221231'\n", ")\n", ",\n", "final_offers as (\n", "select \n", "week, \n", "driver_id, \n", "dt,\n", "calc_at,\n", "case when extract(dow from calc_at)=1 and extract(hour from calc_at)<6 then 0 else 1 end as flag,\n", "offer_name,\n", "split_part(offer_name,' ',1) as offer_day,\n", "amount as offer_payout\n", "from\n", "(\n", "select * from\n", "(\n", "select  *, rank() over(partition by driver_id order by \n", "dt desc ) rnk2 from offer_base\n", "where rnk=1\n", ")\n", "where rnk2=1\n", ")\n", "where driver_id in (\n", "            select distinct delivery_driver_id from zomato.carthero_prod.delivery_driver_service_mappings\n", "            where driver_service_id=13\n", "            and system_enabled=1 and driver_enabled=1\n", "            )\n", "and split_part(offer_name,' ',1)!=date_format(current_date,'%%W')\n", ")\n", "\n", "select\n", "week, \n", "driver_id, \n", "dt,\n", "offer_day,\n", "max(offer_payout) as current_offer_payout\n", "from\n", "final_offers\n", "where flag=1\n", "group by 1,2,3,4\n", ")\n", ",\n", "accounting as (\n", "select \n", "jl.account_date, \n", "jl.delivery_driver_id, \n", "total_orders,\n", "coalesce(bonus_pay_amount,0) as bonus_pay_amount,\n", "coalesce(calls_per_order_incentive_amount,0) as calls_per_order_incentive_amount,\n", "coalesce(order_rating_incentive_amount,0) as order_rating_incentive_amount,\n", "coalesce(reservationpay_amount,0) as reservationpay_amount,\n", "coalesce(hourly_pay,0) as hourly_pay,\n", "coalesce(offer_incentive,0) as offer_incentive,\n", "coalesce(weekly_ming_amount,0) as weekly_ming_amount,\n", "coalesce(weekly_milestone_amount,0) as weekly_milestone_amount,\n", "coalesce(ming_amount,0) as ming_amount,\n", "coalesce(daily_milestone_amount,0) as daily_milestone_amount,\n", "coalesce(bonus_pay_amount,0)\n", "+coalesce(calls_per_order_incentive_amount,0)\n", "+coalesce(order_rating_incentive_amount,0)\n", "+coalesce(reservationpay_amount,0)\n", "+coalesce(hourly_pay,0)\n", "+coalesce(offer_incentive,0)\n", "+coalesce(o.current_offer_payout,0)\n", "+coalesce(daily_milestone_amount,0)\n", "+coalesce(ming_amount,0)\n", "+coalesce(weekly_milestone_amount,0)\n", "+coalesce(weekly_ming_amount,0) as non_order_cost\n", "from jumbo_ledger as jl\n", "left join offers o on jl.delivery_driver_id=o.driver_id and date_format(jl.account_date,'%%W')=offer_day \n", "                   and extract(week from jl.account_date)=extract(week from current_date)\n", ")\n", ",\n", "cost_base as (\n", "select\n", "b.account_date,\n", "b.outlet_id,\n", "b.cart_id,\n", "b.order_id,\n", "b.delivery_driver_id,\n", "b.cart_cost,\n", "case when a.total_orders=0 then 0 \n", "     when a.total_orders is null then 0\n", "     else cast(coalesce(a.non_order_cost,0) as double)/a.total_orders end as non_order_cost_per_order\n", "from base1 as b \n", "left join accounting as a on a.account_date=b.account_date and a.delivery_driver_id=b.delivery_driver_id\n", ")\n", "\n", ",\n", "final as (\n", "select\n", "account_date,\n", "outlet_id,\n", "cart_id,\n", "order_id,\n", "delivery_driver_id,\n", "sum(coalesce(non_order_cost_per_order,0)+coalesce(cart_cost,0)) as lm,\n", "count(*)over() as c\n", "from cost_base\n", "group by 1,2,3,4,5\n", ")\n", "\n", ",\n", "insurance as (\n", "select policy_date,\n", "store_id,\n", "count(a.delivery_driver_id) as insurance_riders\n", "from zomato.carthero_prod.driver_daily_insurances a\n", "left join zomato.jumbo_derived.delivery_drivers_dt b on b.driver_id = a.delivery_driver_id and policy_date = date(date_parse(b.dt,'%%Y%%m%%d'))\n", "--left join blinkit.logistics_data_etls.b_driver_daily_accounting_v1 f on policy_date = f.account_date and  concat('FE',cast(f.delivery_driver_id+10000 as varchar)) = a.delivery_driver_id\n", "where a.dt<>'0' and b.dt<>'0' and active = 1 and driver_category = 'blinkit'\n", "-- and policy_date between (current_date - interval '8' day) and (current_date - interval '2' day)\n", "and policy_date >=date'{start_date}'\n", "and policy_date < date'{end_date}' + interval '1' day\n", "group by 1,2\n", ")\n", ",\n", "insurance_base as (\n", "select\n", "-- 1 as flag,\n", "sum(insurance_riders)*4.54 as insurance_cost\n", "from insurance\n", "-- group by 1\n", ")\n", ",\n", "final_base_with_insurance as (\n", "select\n", "account_date,\n", "outlet_id,\n", "cart_id,\n", "order_id,\n", "delivery_driver_id,\n", "lm,\n", "-- insurance_cost,\n", "cast(insurance_cost as double)/c as insurance_cost_per_order\n", "-- c\n", "from final f\n", "left join insurance_base i on 1=1-- f.flag=i.flag\n", "-- limit 1000\n", "-- -- group by 1,2,3,4,5\n", ")\n", ",\n", "jkl as (\n", "select\n", "account_date,\n", "outlet_id,\n", "cart_id,\n", "order_id,\n", "sum(coalesce(lm,0)+coalesce(insurance_cost_per_order,0)) as last_mile_cost\n", "from final_base_with_insurance\n", "group by 1,2,3,4\n", "),\n", "\n", "paas as (\n", "select order_id,product,sum(ic) as ic from \n", "(select\n", "oi.cart_id,\n", "oi.order_id,\n", "\n", "    case when oi.product_id in (526255,560227,560226,560225,560224) then 'Photo_Printing'\n", "        when oi.product_id in (541364,541365,541366) then 'passport_photo'\n", "        when lower(product_name) like '%%%%black%%%%' then 'Black_N_White'\n", "        when lower(product_name) like '%%%%photo%%%%' then 'Photo'\n", "        when oi.product_id in (573298,573299,573300,573301,573302,573303,573304,573305,577405,578226,594380,594381,594382,594384,594379) then 'Color'\n", "        else 'Color' end as product, \n", "\n", "oi.product_id,\n", "dp.l1_category_id,\n", "case when (dp.l0_category_id=343 and dp.l1_category_id<>2039) then 0 else 1.00/cast(count(m.item_id)over(partition by oi.order_id) as double) end as ic\n", "\n", "from dwh.fact_sales_order_item_details oi\n", "left join dwh.dim_product dp on dp.product_id=oi.product_id and dp.is_current\n", "left join dwh.dim_item_product_offer_mapping m on m.product_id=oi.product_id and m.is_current\n", "\n", "where \n", "oi.order_create_dt_ist between date'{start_date}' - interval '2' day and date'{end_date}' + interval '2' day\n", "and order_current_status = 'DELIVERED'\n", ")\n", "where l1_category_id=2039\n", "group by 1,2\n", ")\n", "\n", "\n", "-- select week, sum(last_mile_cost) as lm from (\n", "select \n", "date_trunc('week',account_date) as week,\n", "date_trunc('month',account_date) as month,\n", "outlet_id,\n", "paas.order_id,\n", "product,\n", "'Print as a service' as l1_category,\n", "sum(last_mile_cost*paas.ic) as  last_mile_cost\n", "from jkl \n", "join paas on jkl.order_id=paas.order_id\n", "group by 1,2,3,4,5\n", "-- )\n", "-- group by 1\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "56d9886c-e5dd-479b-9770-dc25e7d95cba", "metadata": {}, "outputs": [], "source": ["df9 = pd.read_sql(\n", "    query9.format(start_date=start_date, end_date=end_date, start_dt=start_dt, end_dt=end_dt), con\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9af79553-f81a-4846-82fa-fea1a9531699", "metadata": {}, "outputs": [], "source": ["df9.shape"]}, {"cell_type": "code", "execution_count": null, "id": "addba958-8f42-4b41-b0ae-7126a509e41c", "metadata": {}, "outputs": [], "source": ["df9.head()"]}, {"cell_type": "code", "execution_count": null, "id": "1b360baa-9fe1-416b-9184-27022d65f963", "metadata": {}, "outputs": [], "source": ["dfg = pd.merge(\n", "    dfb, df9, how=\"outer\", on=[\"week\", \"month\", \"order_id\", \"outlet_id\", \"l1_category\", \"product\"]\n", ").<PERSON>na(0)"]}, {"cell_type": "code", "execution_count": null, "id": "6aef592c-316d-49ab-9c9a-3a315b8ea3a3", "metadata": {}, "outputs": [], "source": ["dfs = pd.merge(\n", "    dfg, df8, how=\"outer\", on=[\"week\", \"month\", \"order_id\", \"outlet_id\", \"l1_category\", \"product\"]\n", ").<PERSON>na(0)"]}, {"cell_type": "code", "execution_count": null, "id": "e4465717-de71-432b-a27c-70c4eb03a98f", "metadata": {}, "outputs": [], "source": ["# query10 = \"\"\"\n", "\n", "# select\n", "\n", "# 0 as ob_fees_allocated,\n", "# 0 as rider_support_tech_cost_allocated\n", "# from dwh.dim_product\n", "\n", "# \"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "cc479f13-fa05-4b48-a77f-94c667388cb0", "metadata": {}, "outputs": [], "source": ["# df10 = pd.read_sql(\n", "#     query10.format(start_date=start_date, end_date=end_date, start_dt=start_dt, end_dt=end_dt), con\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "7ddb9e3b-674a-4e56-9cab-54c91fddaa94", "metadata": {}, "outputs": [], "source": ["# dfm = pd.merge(\n", "#     dfs, df10, how=\"outer\", on=[\"week\", \"month\", \"outlet_id\", \"l1_category\", \"product\"]\n", "# ).<PERSON><PERSON>(0)"]}, {"cell_type": "code", "execution_count": null, "id": "b0a907b3-c55c-4545-a83b-87d194af50a9", "metadata": {}, "outputs": [], "source": ["query11 = \"\"\"\n", "\n", "\n", "with cost as (\n", "    select\n", "    date(date_trunc('week',snapshot_date_ist)) AS week,\n", "    date(date_trunc('month',snapshot_date_ist)) AS month,\n", "    outlet_id,\n", "    SUM(fleet_cost) AS fleet_cost,\n", "    SUM(fleet_operations_team_cost) AS fleet_operations_team_cost,\n", "    SUM(fleet_operations_leads_cost) AS fleet_operations_leads_cost,\n", "    SUM(outsource_manpower_cost) AS outsource_manpower_cost,\n", "    SUM(inhouse_manpower_cost) AS inhouse_manpower_cost,\n", "    SUM(platform_cost) AS platform_cost,\n", "    (coalesce(sum(fleet_cost),0)\n", "     + coalesce(sum(fleet_operations_team_cost),0)) as blinkit_fleet_cost,\n", "    (coalesce(SUM(outsource_manpower_cost),0)\n", "     + coalesce(SUM(inhouse_manpower_cost),0)\n", "     + coalesce(SUM(platform_cost),0)) as customer_support_cost,\n", "     \n", "    (coalesce(SUM(fnv_putaway_cost),0)\n", "     + coalesce(SUM(picking_cost),0)\n", "     + coalesce(SUM(generic_putaway_cost),0)\n", "     + coalesce(SUM(team_lead_cost),0)\n", "     + coalesce(SUM(commission_cost),0)\n", "     + coalesce(SUM(management_overhead_cost),0)) as store_variable_cost,\n", "     \n", "    SUM(total_pg_charges) as total_pg_charges\n", "    \n", "    \n", "\n", "    FROM blinkit.fin_etls.fin_cost_metrics_daily\n", "    where snapshot_date_ist >= date'{start_date}'\n", "    and snapshot_date_ist < date'{end_date}' + interval '1' day\n", "    group by 1,2,3\n", "),\n", "\n", "paas_metrics as (\n", "     select date(date_trunc('week', order_deliver_ts_ist)) as week,\n", "     date(date_trunc('month', order_deliver_ts_ist)) as month,\n", "     oid.outlet_id,\n", "     oid.order_id,\n", "     case when c.l1_category_id = 2039 and oi.product_id in (526255,560227,560226,560225,560224) then 'Photo_Printing'\n", "          when c.l1_category_id = 2039 and oi.product_id in (541364,541365,541366) then 'passport_photo'\n", "          when c.l1_category_id = 2039 and lower(c.product_name) like '%%black%%' then 'Black_N_White'\n", "          when c.l1_category_id = 2039 and lower(c.product_name) like '%%photo%%' then 'Photo'\n", "     when (c.l1_category_id = 2039 \n", "           and (oi.product_id not in (526255,560227,560226,560225,560224)\n", "                or (lower(c.product_name) not like '%%black%%')\n", "                or (lower(c.product_name) not like '%%photo%%')\n", "                or oi.product_id in (573298,573299,573300,573301,573302,573303,573304,573305,577405,578226,594380,594381,594382,594384,594379))) then 'Color'\n", "     else 'Others' end as product,\n", "    cast(sum(case when c.l1_category_id = 2039 then oi.unit_selling_price*oi.procured_quantity else 0 end) as double) as paas_gmv_bifurcated,\n", "    cast(count(distinct case when c.l1_category_id = 2039 then oid.order_id else 0 end) as double) as paas_orders_bifurcated,\n", "    sum(case when oi.product_id in (594381,594384,541366) then (2*oi.product_quantity)\n", "             when oi.product_id in (573298,573299,573300,573303,573304,594380,594382) then (3*oi.product_quantity)\n", "             when oi.product_id in (573302,577405,578226) then (4*oi.product_quantity)\n", "             when oi.product_id in (573301) then (5*oi.product_quantity)\n", "             when oi.product_id in (594379) then (15*oi.product_quantity)\n", "             when oi.product_id in (573305) then (26*oi.product_quantity)\n", "             else oi.product_quantity end) as paas_items_bifurcated\n", "             \n", "    from dwh.fact_sales_order_item_details oi\n", "    inner join dwh.fact_sales_order_details as oid on oi.order_id = oid.order_id\n", "    left join dwh.dim_product as c on c.product_id = oi.product_id and is_current\n", "    where oi.order_create_dt_ist >= date'{start_date}' - interval '1' day\n", "    and oid.order_create_dt_ist >= date'{start_date}' - interval '1' day\n", "    and oid.order_deliver_ts_ist >= date'{start_date}'\n", "    and oid.order_deliver_ts_ist <  date'{end_date}' + interval '1' day\n", "    and oi.order_current_status = 'DELIVERED'\n", "    and lower(oi.order_type) not like '%%internal%%'\n", "    group by 1,2,3,4,5\n", ")\n", "\n", ", pan_india_paas_overall_metrics as (\n", "\n", "    select date(date_trunc('week', order_deliver_ts_ist)) as week,\n", "    date(date_trunc('month', order_deliver_ts_ist)) as month,\n", "    oid.outlet_id,\n", "    cast(sum(oi.unit_selling_price*oi.procured_quantity) as double) as overall_gmv,\n", "    cast(sum(case when c.l1_category_id = 2039 then oi.unit_selling_price*oi.procured_quantity else 0 end) as double) as paas_gmv,\n", "    cast(sum(procured_quantity) as double) as overall_items,\n", "    sum(case when c.l1_category_id = 2039 then \n", "             (case when oi.product_id in (594381,594384,541366) then (2*oi.product_quantity)\n", "                   when oi.product_id in (573298,573299,573300,573303,573304,594380,594382) then (3*oi.product_quantity)\n", "                   when oi.product_id in (573302,577405,578226) then (4*oi.product_quantity)\n", "                   when oi.product_id in (573301) then (5*oi.product_quantity)\n", "                   when oi.product_id in (594379) then (15*oi.product_quantity)\n", "                   when oi.product_id in (573305) then (26*oi.product_quantity)\n", "                   else oi.product_quantity end)\n", "             else 0 end) as paas_items,\n", "    cast(count(distinct oid.order_id) as double) as overall_orders,\n", "    cast(count(distinct case when c.l1_category_id = 2039 then oid.order_id else 0 end) as double) as paas_orders\n", "    \n", "    from dwh.fact_sales_order_item_details oi\n", "    inner join dwh.fact_sales_order_details as oid on oi.order_id = oid.order_id\n", "    left join dwh.dim_product as c on c.product_id = oi.product_id and is_current\n", "    where oi.order_create_dt_ist >= date'{start_date}' - interval '1' day\n", "    and oid.order_create_dt_ist >= date'{start_date}' - interval '1' day\n", "    and oid.order_deliver_ts_ist >= date'{start_date}'\n", "    and oid.order_deliver_ts_ist <  date'{end_date}' + interval '1' day\n", "    and oi.order_current_status = 'DELIVERED'\n", "    and lower(oi.order_type) not like '%%internal%%'\n", "    group by 1,2,3\n", "\n", ")\n", "\n", ", cost_per_rs_gmv as (\n", "    select c.week,\n", "    c.month,\n", "    c.outlet_id,\n", "    c.total_pg_charges,\n", "    ind.overall_gmv,\n", "    ind.overall_items,\n", "    ind.overall_orders,\n", "    (cast(total_pg_charges as double)/cast(overall_gmv as double)) as cost_per_rs_pg_charges\n", "    from cost as c\n", "    inner join pan_india_paas_overall_metrics as ind on  ind.outlet_id = c.outlet_id\n", "                                                     and ind.week = c.week\n", "                                                     and ind.month = c.month\n", ")\n", "\n", ", cost_in_paas_ratio_pg as (\n", "    select c.*,\n", "    p.product,\n", "    p.order_id,\n", "    p.paas_gmv_bifurcated,\n", "    (cost_per_rs_pg_charges*paas_gmv_bifurcated) as pg_allocated\n", "    \n", "    from cost_per_rs_gmv as c\n", "    inner join paas_metrics as p on c.outlet_id = p.outlet_id\n", "                                 and c.week = p.week\n", "                                 and c.month = p.month\n", ")\n", "\n", ", cost_per_item as (\n", "    select c.week,\n", "    c.month,\n", "    c.outlet_id,\n", "    c.blinkit_fleet_cost,\n", "    c.store_variable_cost,\n", "    ind.overall_items,\n", "    (cast(blinkit_fleet_cost as double)/cast(overall_items as double)) as cost_per_item_fleet,\n", "    (cast(store_variable_cost as double)/cast(overall_items as double)) as cost_per_item_storeops\n", "    from cost as c\n", "    inner join pan_india_paas_overall_metrics as ind on  ind.outlet_id = c.outlet_id\n", "                                                     and ind.week = c.week\n", "                                                     and ind.month = c.month\n", ")\n", "\n", ", cost_in_paas_ratio_fleet_storeops_variable as (\n", "    select c.*,\n", "    p.product,\n", "    p.order_id,\n", "    p.paas_items_bifurcated,\n", "    (cost_per_item_fleet*(paas_items_bifurcated/500)) as fleet_allocated,\n", "    (cost_per_item_storeops*(paas_orders_bifurcated)) as storeops_variable_allocated\n", "    \n", "    from cost_per_item as c\n", "    inner join paas_metrics as p on c.outlet_id = p.outlet_id\n", "                                 and c.week = p.week\n", "                                 and c.month = p.month\n", "\n", ")\n", "\n", ", base as (\n", "select * from \n", "(\n", "select \n", "week,\n", "month,\n", "outlet_id,\n", "order_id,\n", "product,\n", "fleet_allocated,\n", "storeops_variable_allocated,\n", "0 as pg_allocated\n", "\n", "from cost_in_paas_ratio_fleet_storeops_variable\n", "\n", "UNION ALL\n", "\n", "select \n", "week,\n", "month,\n", "outlet_id,\n", "order_id,\n", "product,\n", "0 as fleet_allocated,\n", "0 as storeops_variable_allocated,\n", "pg_allocated\n", "\n", "from cost_in_paas_ratio_pg\n", ")\n", "\n", "where \n", "--outlet_id = 3678\n", "-- and\n", "product != 'Others')\n", "\n", "\n", "-- select week, sum(fleet_paas) as fleet_paas, sum(storeops_paas) as storeops_paas, sum(pg_charges_paas) as pg_charges_paas from \n", "-- (\n", "select \n", "week,\n", "month,\n", "outlet_id,\n", "order_id,\n", "product,\n", "'Print as a service' as l1_category,\n", "sum(fleet_allocated) as fleet_paas,\n", "sum(storeops_variable_allocated) as storeops_paas,\n", "sum(pg_allocated) as pg_charges_paas\n", "\n", "from base\n", "group by 1,2,3,4,5   \n", "-- )\n", "-- group by 1\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "ce9672bf-95f9-48a3-b50c-daaf106cd181", "metadata": {}, "outputs": [], "source": ["df11 = pd.read_sql(query11.format(start_date=start_date, end_date=end_date), con)"]}, {"cell_type": "code", "execution_count": null, "id": "92800217-8f7f-41b0-bdb6-5b7deda7e213", "metadata": {}, "outputs": [], "source": ["query12 = \"\"\"\n", "\n", "with refunds_int as (\n", "        select \n", "            date(refund_date) as date_,\n", "            r.outlet_id,\n", "            cart_id,\n", "            order_id,\n", "            r.product_id,\n", "            p.l1_category,\n", "            case when r.product_id in (526255,560227,560226,560225,560224) then 'Photo_Printing'\n", "                 when r.product_id in (541364,541365,541366) then 'passport_photo'\n", "                 when lower(product_name) like '%%black%%' then 'Black_N_White'\n", "                 when lower(product_name) like '%%photo%%' then 'Photo'\n", "                 when r.product_id in (573298,573299,573300,573301,573302,573303,573304,573305,577405,578226,594380,594381,594382,594384,594379) then 'Color'\n", "                 else 'Color' end as product,\n", "            sum(cast(coalesce(case when rm_loss = 0 then net_refund end,0) as double)) pure_refund_loss,\n", "            sum(cast(coalesce(case when refund_type = 'non_dsr_refund' then rm_loss end,0) as double)) non_dsr_rm_loss,\n", "            sum(cast(coalesce(case when refund_type = 'dsr_refund' then rm_loss end,0) as double)) dsr_rm_loss\n", "\n", "        from  dwh.flat_order_item_refunds r\n", "        left join dwh.dim_product as p on r.product_id = p.product_id and p.is_current\n", "        where refund_date >=  date'{start_date}'\n", "        and refund_date <  date'{end_date}' + interval '1' day\n", "        and p.l1_category_id = 2039\n", "        group by 1,2,3,4,5,6,7\n", ")\n", "\n", "\n", "\n", ", charges_refund as (\n", "    select\n", "    date(refund_date) as date_,\n", "    o.outlet_id,\n", "    o.cart_id,\n", "    o.order_id,\n", "    sum(case when o.product_id = 0 then net_refund else 0 end) as charges_based_rf\n", "    from dwh.flat_order_item_refunds as o\n", "    join refunds_int as r on r.cart_id = o.cart_id\n", "    where refund_date >=  date'{start_date}'\n", "    and refund_date <  date'{end_date}' + interval '1' day\n", "    group by 1,2,3,4\n", ")\n", "\n", "\n", ", allocated_refund as (\n", "    select \n", "    date_,\n", "    outlet_id,\n", "    cart_id,\n", "    order_id,\n", "    o.product_id,\n", "    p.l1_category,\n", "    o.product,\n", "    sum(pure_refund_loss + non_dsr_rm_loss + dsr_rm_loss) as rf\n", "    from refunds_int as o\n", "    left join dwh.dim_product as p on p.product_id = o.product_id and p.is_current\n", "    where o.product_id <> 0\n", "    group by 1,2,3,4,5,6,7\n", ")\n", "\n", "\n", ", final_allocation as (\n", "    select\n", "    date(date_trunc('week',a.date_)) as week,\n", "    date(date_trunc('month',a.date_)) as month,\n", "    a.outlet_id,\n", "    a.cart_id,\n", "    a.order_id,\n", "    a.product_id,\n", "    a.product,\n", "    a.l1_category,\n", "    coalesce(rf,0) as rf,\n", "    coalesce(rf/sum(rf) over(partition by a.cart_id),0) as perc,\n", "    case when coalesce(rf,0) = 0 then 0 else coalesce(rf/sum(rf) over(partition by a.cart_id),0)*coalesce(charges_based_rf,0) end as allocated_amt,\n", "    case when coalesce(rf,0) = 0 then 0 else coalesce(rf/sum(rf) over(partition by a.cart_id),0)*coalesce(charges_based_rf,0) end + rf as final_rf\n", "    from allocated_refund as a\n", "    join charges_refund as c on a.cart_id = c.cart_id\n", ")\n", "\n", "\n", ", paas as (\n", "select\n", "    week,\n", "    month, \n", "    outlet_id, \n", "    order_id,\n", "    product, \n", "    l1_category,\n", "    sum(final_rf) as final_refund\n", "from final_allocation \n", "group by 1,2,3,4,5,6\n", ")\n", "\n", ", customer_support_cost as (\n", "    select \n", "    date(date_trunc('week',snapshot_date_ist)) AS week,\n", "    date(date_trunc('month',snapshot_date_ist)) AS month,\n", "    outlet_id,\n", "    sum(coalesce(outsource_manpower_cost,0) + coalesce(inhouse_manpower_cost,0) + coalesce(platform_cost,0)) as customer_support_cost\n", "    \n", "    FROM blinkit.fin_etls.fin_cost_metrics_daily\n", "    where snapshot_date_ist >= date'{start_date}'\n", "    and snapshot_date_ist < date'{end_date}' + interval '1' day\n", "    group by 1,2,3\n", "\n", ")\n", "\n", ", refund_overall as (\n", "        select\n", "        date(date_trunc('week',refund_date)) AS week,\n", "        date(date_trunc('month',refund_date)) AS month,\n", "        r.outlet_id,\n", "        c.customer_support_cost,\n", "        sum(cast(coalesce(case when rm_loss = 0 then net_refund end,0) as double)) pure_refund_loss,\n", "        sum(cast(coalesce(case when refund_type = 'non_dsr_refund' then rm_loss end,0) as double)) non_dsr_rm_loss,\n", "        sum(cast(coalesce(case when refund_type = 'dsr_refund' then rm_loss end,0) as double)) dsr_rm_loss,\n", "        c.customer_support_cost/(sum(cast(coalesce(case when rm_loss = 0 then net_refund end,0) as double))\n", "                                 + sum(cast(coalesce(case when refund_type = 'non_dsr_refund' then rm_loss end,0) as double))\n", "                                 + sum(cast(coalesce(case when refund_type = 'dsr_refund' then rm_loss end,0) as double))) as per_rs_refund_cost\n", "\n", "        from  dwh.flat_order_item_refunds r\n", "        left join customer_support_cost as c on r.outlet_id = c.outlet_id\n", "                                             and date(date_trunc('week',refund_date)) = c.week\n", "                                             and date(date_trunc('month',refund_date)) = c.month\n", "        where refund_date >=  date'{start_date}'\n", "        and refund_date <  date'{end_date}' + interval '1' day\n", "\n", "        group by 1,2,3,4\n", ")\n", "\n", "\n", "    select a.week as week,\n", "    a.month,\n", "    a.outlet_id,\n", "    a.order_id,\n", "    product,\n", "    l1_category,\n", "    (coalesce(a.final_refund,0)*coalesce(per_rs_refund_cost,0)) as customer_support_paas\n", "    \n", "    from paas as a\n", "    left join refund_overall as b on date(a.week) = b.week\n", "                                  and date(a.month) = b.month\n", "                                  and a.outlet_id = b.outlet_id\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "57f20fb0-b6e6-4458-8f13-19b9ebb7d767", "metadata": {}, "outputs": [], "source": ["df12 = pd.read_sql(query12.format(start_date=start_date, end_date=end_date), con)"]}, {"cell_type": "code", "execution_count": null, "id": "f7dd7134-256b-473e-b4b8-4db937b37af1", "metadata": {}, "outputs": [], "source": ["df12.shape"]}, {"cell_type": "code", "execution_count": null, "id": "b9f1db17-8ae9-4c98-9359-481da998a374", "metadata": {}, "outputs": [], "source": ["df12.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c896a76b-e6e4-4302-997b-9f69619b0bb6", "metadata": {}, "outputs": [], "source": ["dfp = pd.merge(\n", "    dfs, df11, how=\"outer\", on=[\"week\", \"month\", \"order_id\", \"outlet_id\", \"l1_category\", \"product\"]\n", ").<PERSON>na(0)"]}, {"cell_type": "code", "execution_count": null, "id": "4b92988c-93f1-43d5-b0e3-9bce97754049", "metadata": {}, "outputs": [], "source": ["dff = pd.merge(\n", "    dfp, df12, how=\"outer\", on=[\"week\", \"month\", \"order_id\", \"outlet_id\", \"l1_category\", \"product\"]\n", ").<PERSON>na(0)"]}, {"cell_type": "code", "execution_count": null, "id": "d536901b-219f-4a9d-9119-cfe2991266a7", "metadata": {}, "outputs": [], "source": ["dff.columns"]}, {"cell_type": "code", "execution_count": null, "id": "3f8badce-8bbe-4a71-bb7b-5e27cfb1c6ad", "metadata": {}, "outputs": [], "source": ["dff.shape"]}, {"cell_type": "code", "execution_count": null, "id": "2b7db271-c20a-4391-bb1d-99062f30879b", "metadata": {}, "outputs": [], "source": ["dff.head()"]}, {"cell_type": "code", "execution_count": null, "id": "488c70cc-b0ea-441d-8ef3-49005dda70e2", "metadata": {}, "outputs": [], "source": ["dff[\"ob_fees_allocated\"] = 0\n", "dff[\"rider_support_tech_cost_allocated\"] = 0"]}, {"cell_type": "code", "execution_count": null, "id": "5916f0cd-d9f2-4f26-b9b3-679923e7680b", "metadata": {}, "outputs": [], "source": ["# dff.fillna(0)\n", "dff[\"week\"] = pd.to_datetime(dff[\"week\"])\n", "dff[\"month\"] = pd.to_datetime(dff[\"month\"])\n", "dff[\"order_id\"] = dff[\"order_id\"].astype(int)\n", "dff[\"outlet_id\"] = dff[\"outlet_id\"].astype(int)\n", "dff[\"l1_category\"] = dff[\"l1_category\"].astype(str)\n", "dff[\"product\"] = dff[\"product\"].astype(str)\n", "dff[\"total_discount_amount\"] = dff[\"total_discount_amount\"].astype(float)\n", "dff[\"checkout_cost_qty\"] = dff[\"checkout_cost_qty\"].astype(int)\n", "dff[\"delivered_cost_qty\"] = dff[\"delivered_cost_qty\"].astype(int)\n", "dff[\"delivered_gmv\"] = dff[\"delivered_gmv\"].astype(float)\n", "dff[\"checkout_gmv\"] = dff[\"checkout_gmv\"].astype(float)\n", "dff[\"delivered_orders\"] = dff[\"delivered_orders\"].astype(int)\n", "dff[\"checkout_orders\"] = dff[\"checkout_orders\"].astype(int)\n", "dff[\"delivered_carts\"] = dff[\"delivered_carts\"].astype(int)\n", "dff[\"checkout_carts\"] = dff[\"checkout_carts\"].astype(int)\n", "dff[\"delivered_qty\"] = dff[\"delivered_qty\"].astype(int)\n", "dff[\"checkout_qty\"] = dff[\"checkout_qty\"].astype(int)\n", "dff[\"final_refund\"] = dff[\"final_refund\"].astype(float)\n", "# dff['non_dsr_rm_loss'] = dff['non_dsr_rm_loss'].astype(float)\n", "dff[\"return_loss\"] = dff[\"return_loss\"].astype(float)\n", "dff[\"final_return_loss\"] = dff[\"final_return_loss\"].astype(float)\n", "# dff['final_refund'] = dff['final_refund'].astype(float)\n", "dff[\"convenience_charge\"] = dff[\"convenience_charge\"].astype(float)\n", "dff[\"eco_friendly_packaging_charges\"] = dff[\"eco_friendly_packaging_charges\"].astype(float)\n", "dff[\"night_charge\"] = dff[\"night_charge\"].astype(float)\n", "dff[\"delivery_cost\"] = dff[\"delivery_cost\"].astype(float)\n", "dff[\"slot_charges\"] = dff[\"slot_charges\"].astype(float)\n", "dff[\"gift_packing_charges\"] = dff[\"gift_packing_charges\"].astype(float)\n", "dff[\"small_cart_charges\"] = dff[\"small_cart_charges\"].astype(float)\n", "dff[\"festival_charges\"] = dff[\"festival_charges\"].astype(float)\n", "dff[\"diff_additional_charges\"] = dff[\"diff_additional_charges\"].astype(float)\n", "dff[\"additional_charges_amount\"] = dff[\"additional_charges_amount\"].astype(float)\n", "dff[\"last_mile_cost\"] = dff[\"last_mile_cost\"].astype(float)\n", "dff[\"ob_fees_allocated\"] = dff[\"ob_fees_allocated\"].astype(float)\n", "dff[\"rider_support_tech_cost_allocated\"] = dff[\"rider_support_tech_cost_allocated\"].astype(float)\n", "dff[\"fleet_paas\"] = dff[\"fleet_paas\"].astype(float)\n", "dff[\"storeops_paas\"] = dff[\"storeops_paas\"].astype(float)\n", "dff[\"pg_charges_paas\"] = dff[\"pg_charges_paas\"].astype(float)\n", "dff[\"customer_support_paas\"] = dff[\"customer_support_paas\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "9d287621-7537-4b2c-846f-0af1286850f5", "metadata": {}, "outputs": [], "source": ["dff.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "53797591-b82f-4132-9b62-59c07ee6cb5e", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"fin_etls\",\n", "    \"table_name\": \"paas_pnl_order_level\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"week\", \"type\": \"DATE\", \"description\": \"date of creation of order\"},\n", "        {\"name\": \"month\", \"type\": \"DATE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"order_id\", \"type\": \"INTEGER\", \"description\": \"outlet id\"},\n", "        {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"outlet id\"},\n", "        {\"name\": \"l1_category\", \"type\": \"VARCHAR\", \"description\": \"outlet id\"},\n", "        {\"name\": \"product\", \"type\": \"VARCHAR\", \"description\": \"date of creation of order\"},\n", "        {\"name\": \"total_discount_amount\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"checkout_cost_qty\", \"type\": \"INTEGER\", \"description\": \"outlet id\"},\n", "        {\"name\": \"delivered_cost_qty\", \"type\": \"INTEGER\", \"description\": \"outlet id\"},\n", "        {\"name\": \"delivered_gmv\", \"type\": \"DOUBLE\", \"description\": \"date of creation of order\"},\n", "        {\"name\": \"checkout_gmv\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"delivered_orders\", \"type\": \"INTEGER\", \"description\": \"outlet id\"},\n", "        {\"name\": \"checkout_orders\", \"type\": \"INTEGER\", \"description\": \"outlet id\"},\n", "        {\"name\": \"delivered_carts\", \"type\": \"INTEGER\", \"description\": \"outlet id\"},\n", "        {\"name\": \"checkout_carts\", \"type\": \"INTEGER\", \"description\": \"outlet id\"},\n", "        {\"name\": \"delivered_qty\", \"type\": \"INTEGER\", \"description\": \"date of creation of order\"},\n", "        {\"name\": \"checkout_qty\", \"type\": \"INTEGER\", \"description\": \"outlet id\"},\n", "        {\"name\": \"return_loss\", \"type\": \"DOUBLE\", \"description\": \"date of creation of order\"},\n", "        {\"name\": \"final_return_loss\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"final_refund\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"last_mile_cost\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"convenience_charge\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"eco_friendly_packaging_charges\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"night_charge\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"delivery_cost\", \"type\": \"DOUBLE\", \"description\": \"date of creation of order\"},\n", "        {\"name\": \"slot_charges\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"gift_packing_charges\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"small_cart_charges\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"festival_charges\", \"type\": \"DOUBLE\", \"description\": \"date of creation of order\"},\n", "        {\"name\": \"diff_additional_charges\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"additional_charges_amount\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"fleet_paas\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"storeops_paas\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"pg_charges_paas\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"customer_support_paas\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"ob_fees_allocated\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"rider_support_tech_cost_allocated\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "    ],\n", "    \"primary_key\": [\"week\", \"outlet_id\", \"product\", \"order_id\"],\n", "    \"partition_key\": [\"week\", \"month\"],\n", "    \"incremental_key\": [],\n", "    \"load_type\": \"partition_overwrite\",\n", "    \"force_upsert_without_increment_check\": True,\n", "    \"table_description\": \"this table is made temporary data push\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "66b5be17-f2e1-4849-8965-cd9ff8cf419f", "metadata": {}, "outputs": [], "source": ["pb.to_trino(dff, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}