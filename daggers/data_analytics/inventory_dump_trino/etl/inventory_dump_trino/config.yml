alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 3
dag_name: inventory_dump_trino
dag_type: etl
escalation_priority: medium
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_analytics
notebooks:
- executor_config:
    load_type: medium
    node_type: spot
  name: notebook_1
  parameters: null
  retries: 1
  tag: first
- executor_config:
    load_type: medium
    node_type: spot
  name: notebook_2
  parameters: null
  retries: 1
  tag: first
- executor_config:
    load_type: medium
    node_type: spot
  name: notebook_3
  parameters: null
  retries: 1
  tag: first
- executor_config:
    load_type: medium
    node_type: spot
  name: notebook_4
  parameters: null
  tag: first
- executor_config:
    load_type: medium
    node_type: spot
  name: notebook_5
  parameters: null
  retries: 1
  tag: first
- executor_config:
    load_type: medium
    node_type: spot
  name: notebook_6
  parameters: null
  retries: 1
  tag: second
owner:
  email: <EMAIL>
  slack_id: U03S75C25B7
path: data_analytics/inventory_dump_trino/etl/inventory_dump_trino
paused: false
pool: data_analytics_pool
project_name: inventory_dump_trino
schedule:
  end_date: '2025-08-07T00:00:00'
  interval: 37 0 * * *
  start_date: '2025-06-24T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 47
