alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: daily_9
dag_type: workflow
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_analytics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03S75C25B7
path: data_analytics/automation_mh/workflow/daily_9
paused: false
pool: data_analytics_pool
project_name: automation_mh
schedule:
  end_date: '2025-08-24T00:00:00'
  interval: 27 3 * * *
  start_date: '2025-04-04T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
