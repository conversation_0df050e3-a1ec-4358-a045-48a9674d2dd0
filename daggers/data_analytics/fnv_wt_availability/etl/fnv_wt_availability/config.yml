alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: fnv_wt_availability
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: low
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_analytics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03S75C25B7
path: data_analytics/fnv_wt_availability/etl/fnv_wt_availability
paused: false
pool: data_analytics_pool
project_name: fnv_wt_availability
schedule:
  end_date: '2025-08-07T00:00:00'
  interval: 15 2 * * 2
  start_date: '2024-10-15T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
