{"cells": [{"cell_type": "code", "execution_count": null, "id": "ec0a4063-2093-4b2f-b298-925f3a09ef5f", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "from datetime import date\n", "from datetime import timedelta\n", "from datetime import datetime\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\").connect()"]}, {"cell_type": "code", "execution_count": null, "id": "791bb868-f487-4529-9712-34b5b3439317", "metadata": {}, "outputs": [], "source": ["start_date = \"2025-05-01\"\n", "end_date = \"2025-05-31\""]}, {"cell_type": "code", "execution_count": null, "id": "452ecb60-6084-4f70-8422-33b320e30d27", "metadata": {}, "outputs": [], "source": ["data = (\n", "    \"\"\"\n", "select date(date_trunc('month',bfs.date)) as month,\n", "manufacturer_id,\n", "manufacturer,\n", "sum(total_brand_fund) as total_brand_fund_bfs\n", "from pricing_v3.attribute_management_brandsfundsummary bfs \n", "where \n", "    bfs.partition_field >= ('{{start_date}}')\n", "    and bfs.date>= cast('{{start_date}}' as date)\n", "    and bfs.date <= cast('{{end_date}}' as date) \n", "    and bfs.lake_active_record = True\n", "    and is_active\n", "    AND bfs.id NOT IN\n", "        (SELECT id\n", "         FROM pricing_v3.attribute_management_brandsfundsummary\n", "         WHERE partition_field >= ('{{start_date}}') and\n", "         is_active=FALSE)\n", "group by 1,2,3\n", "\"\"\".replace(\n", "        \"%\", \"%%\"\n", "    )\n", "    .replace(\"{{\", \"{\")\n", "    .replace(\"}}\", \"}\")\n", ")\n", "query = data.format(\n", "    start_date=start_date,  # Replace with actual start date\n", "    end_date=end_date,  # Replace with actual end date\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a26dc5d1-f3eb-452a-8860-aade01a813a5", "metadata": {}, "outputs": [], "source": ["# data = \"\"\"\n", "\n", "# with item_tax as (select\n", "# item_id,\n", "# max(case when coalesce(igst,0) = 0 then (COALESCE(cgst,0) + COALESCE(cess,0) + COALESCE(sgst,0)) else (coalesce(igst,0) + COALESCE(cess,0)) end) as product_tax,\n", "# max(coalesce(additional_cess_value,0)) as add_cess_value\n", "# from\n", "#     (select\n", "#     *,row_number() over(partition by item_id order by updated_at desc) as rnk\n", "\n", "#     from rpc.product_tax)\n", "\n", "#     where rnk=1\n", "#     group by 1\n", "#     )\n", "\n", "\n", "# , base as (select\n", "# month,\n", "# manufacturer_id,\n", "# manufacturer_name as manufacturer,\n", "# sum(total_brand_fund) as total_brand_fund_bfs,\n", "# cast(sum(new_total_brand_fund_post_tax) as double) as brand_fund_post_tax\n", "# from\n", "# (select\n", "#     date(date_trunc('month',bfs.date)) as month,\n", "#     bfs.manufacturer_id,\n", "#     city,\n", "#     lower(manufacturer) as manufacturer_name,\n", "#     offer_type,\n", "#     sum(qty_sold) as qty_sold,\n", "#     sum(total_brand_fund) as total_brand_fund,\n", "#     sum(case when total_brand_fund = 0 then 0\n", "#          else (((total_brand_fund - coalesce(it.add_cess_value,0)*qty_sold)/(1+coalesce(it.product_tax,0)/100))) end) as new_total_brand_fund_post_tax\n", "\n", "# from pricing_v3.attribute_management_brandsfundsummary bfs\n", "# left join item_tax it on it.item_id=bfs.item_id\n", "# where\n", "#     bfs.partition_field >= ('{{start_date}}')\n", "#     and bfs.date>= cast('{{start_date}}' as date)\n", "#     and bfs.date <= cast('{{end_date}}' as date)\n", "#     and bfs.lake_active_record = True\n", "#     and is_active\n", "\n", "#     AND id NOT IN\n", "#         (SELECT id\n", "#          FROM pricing_v3.attribute_management_brandsfundsummary\n", "#          WHERE partition_field >= ('{{start_date}}') and\n", "#          is_active=FALSE)\n", "\n", "# group by 1,2,3,4,5) a\n", "# group by 1,2,3\n", "# )\n", "\n", "# select * from base\n", "\n", "# \"\"\".replace(\"%\", \"%%\").replace(\"{{\", \"{\").replace(\"}}\", \"}\")\n", "# query = data.format(\n", "#     start_date=start_date,  # Replace with actual start date\n", "#     end_date=end_date,    # Replace with actual end date\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "8972319b-4338-4a8d-b790-e09ee17a41b4", "metadata": {}, "outputs": [], "source": ["df = pd.read_sql(query, con)"]}, {"cell_type": "code", "execution_count": null, "id": "ebad3601-bc48-4ad1-97a0-39de29e43c16", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2d5b1097-de99-4f34-951f-50522e68798a", "metadata": {}, "outputs": [], "source": ["df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "832ae8d5-a743-419b-a1dd-4046cd30cadf", "metadata": {}, "outputs": [], "source": ["df = df.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "1f5563b8-7136-4c6d-a924-ffa7861cae92", "metadata": {}, "outputs": [], "source": ["df = df[df[\"total_brand_fund_bfs\"] != 0]"]}, {"cell_type": "code", "execution_count": null, "id": "67512dac-6287-46dd-96d5-e5c1623b0c1d", "metadata": {}, "outputs": [], "source": ["df.head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "115a8276-4d2a-4009-89fa-569007f630f8", "metadata": {}, "outputs": [], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "1811a52a-1220-430f-8a22-5f9cd4e2775b", "metadata": {}, "outputs": [], "source": ["df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "07c6459e-3a64-46ea-9832-ac1504c3e0c9", "metadata": {}, "outputs": [], "source": ["df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "2bbdfd8d-6e6b-4b4a-9ccf-8286caf17929", "metadata": {}, "outputs": [], "source": ["df[\"month\"] = pd.to_datetime(df[\"month\"])\n", "df[\"manufacturer_id\"] = df[\"manufacturer_id\"].astype(int)\n", "df[\"manufacturer\"] = df[\"manufacturer\"].astype(str)\n", "df[\"total_brand_fund_bfs\"] = df[\"total_brand_fund_bfs\"].astype(float)\n", "# df[\"brand_fund_post_tax\"] = df[\"brand_fund_post_tax\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "762d4f10-4f54-4463-891d-2a1b227f47b5", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f191edc2-8109-4d50-a397-14004f97652a", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"fin_etls\",\n", "    \"table_name\": \"bfs_data_agggg_april_new\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"month\", \"type\": \"DATE\", \"description\": \"date of creation of order\"},\n", "        {\"name\": \"manufacturer_id\", \"type\": \"INTEGER\", \"description\": \"outlet id\"},\n", "        {\"name\": \"manufacturer\", \"type\": \"VARCHAR\", \"description\": \"date of creation of order\"},\n", "        {\"name\": \"total_brand_fund_bfs\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        # {\"name\": \"brand_fund_post_tax\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "    ],\n", "    \"primary_key\": [\"month\", \"manufacturer_id\"],\n", "    \"partition_key\": [\"month\"],\n", "    \"incremental_key\": [],\n", "    \"load_type\": \"partition_overwrite\",\n", "    \"force_upsert_without_increment_check\": True,\n", "    \"table_description\": \"this table is made temporary data push\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "0d1e08a5-c548-4c6e-ad7d-b4aaa548be13", "metadata": {}, "outputs": [], "source": ["pb.to_trino(df, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}