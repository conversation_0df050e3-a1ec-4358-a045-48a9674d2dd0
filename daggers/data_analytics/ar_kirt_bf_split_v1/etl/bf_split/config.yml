alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 3
dag_name: bf_split
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_analytics
notebooks:
- alias: bfs_base_1
  executor_config:
    load_type: tiny
    node_type: spot
  name: bfs_base_1
  parameters: null
  tag: group_01
- alias: BF_code_pre_allocation_2
  executor_config:
    load_type: tiny
    node_type: spot
  name: BF_code_pre_allocation_2
  parameters: null
  tag: group_02
- alias: this_is_for_hp_grn_3
  executor_config:
    load_type: tiny
    node_type: spot
  name: this_is_for_hp_grn_3
  parameters: null
  tag: group_03
- alias: hp_pre_latest_4
  executor_config:
    load_type: tiny
    node_type: spot
  name: hp_pre_latest_4
  parameters: null
  tag: group_04
owner:
  email: <EMAIL>
  slack_id: U08JNRAUJAY
path: data_analytics/ar_kirt_bf_split_v1/etl/bf_split
paused: false
pool: data_analytics_pool
project_name: ar_kirt_bf_split_v1
schedule:
  end_date: '2025-08-31T00:00:00'
  interval: 5 2 15 * *
  start_date: '2025-06-02T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 1
