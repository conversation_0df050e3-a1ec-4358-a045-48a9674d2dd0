{"cells": [{"cell_type": "code", "execution_count": null, "id": "ec0a4063-2093-4b2f-b298-925f3a09ef5f", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "from datetime import date\n", "from datetime import timedelta\n", "from datetime import datetime\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\").connect()"]}, {"cell_type": "code", "execution_count": null, "id": "33e54e9c-516c-4136-af39-0ace5a9a2e84", "metadata": {}, "outputs": [], "source": ["# Fetch dynamic manufacturer IDs\n", "query_manufacturer_ids = \"\"\"\n", "SELECT DISTINCT manufacturer_id\n", "FROM fin_etls.bfs_data_agggg_april_new\n", "WHERE month = date'2025-05-01'\n", "order by manufacturer_id\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "25408989-6763-47d2-891d-2dd19b419b1f", "metadata": {}, "outputs": [], "source": ["df = pd.read_sql(query_manufacturer_ids, con)"]}, {"cell_type": "code", "execution_count": null, "id": "35735c1c-1c8c-426f-835d-60037e361ef6", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6a598e76-9ffe-47ca-b744-ccf5ad502f3e", "metadata": {}, "outputs": [], "source": ["df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "5efa77b8-0704-442a-af83-1340de4af8fd", "metadata": {}, "outputs": [], "source": ["manufacturer_id_placeholder = \", \".join(map(str, df[\"manufacturer_id\"].tolist()))"]}, {"cell_type": "code", "execution_count": null, "id": "c5c3289a-e460-4f52-9a76-cdde50cd0558", "metadata": {}, "outputs": [], "source": ["start_date = \"2025-05-01\"\n", "end_date = \"2025-05-31\""]}, {"cell_type": "code", "execution_count": null, "id": "4983160f-1cb4-4c8f-a188-ca3e7510c69a", "metadata": {}, "outputs": [], "source": ["print(start_date)\n", "print(end_date)"]}, {"cell_type": "code", "execution_count": null, "id": "a4d9250c-1b5a-4ac2-a510-08616fe89342", "metadata": {}, "outputs": [], "source": ["data = (\n", "    \"\"\"\n", "\n", "with bfs as (\n", "    select * \n", "    from (\n", "        select b.date,\n", "        b.city,\n", "        b.product_id,\n", "        b.item_id,\n", "        b.manufacturer_id,\n", "        max(lower(b.manufacturer)) as manufacturer_name--,\n", "        -- row_number() over(partition by date, city, product_id, item_id, manufacturer_id, manufacturer order by qty_sold desc) as rnk\n", "        from pricing_v3.attribute_management_brandsfundsummary as b\n", "        where b.partition_field >= ('{{start_date}}')\n", "              and b.date >= date('{{start_date}}')\n", "              and b.date <= date('{{end_date}}')\n", "              and b.lake_active_record = True\n", "              and is_active\n", "              AND b.id NOT IN (SELECT id\n", "                                 FROM pricing_v3.attribute_management_brandsfundsummary\n", "                                 WHERE partition_field >= ('{{start_date}}')\n", "                                 and is_active=FALSE)\n", "        group by 1,2,3,4,5--,6--,7\n", "        )\n", "        -- where rnk > 1\n", "    --   limit 100\n", "),\n", "\n", "item_tax as (\n", "    select\n", "    item_id,\n", "    max(case when coalesce(igst,0) = 0 then (COALESCE(cgst,0) + COALESCE(cess,0) + COALESCE(sgst,0)) else (coalesce(igst,0) + COALESCE(cess,0)) end) as product_tax,\n", "    max(coalesce(additional_cess_value,0)) as add_cess_value\n", "    from\n", "        (\n", "         select *, row_number() over(partition by item_id order by updated_at desc) as rnk\n", "         from rpc.product_tax\n", "        )\n", "        where rnk=1\n", "        group by 1\n", "),\n", "\n", "item_map as (\n", " SELECT \n", "        pp.item_id,\n", "        pp.name as item_name,\n", "        pp.brand_id,\n", "        pm.id as manufacturer_id,\n", "        pm.name as manufacturer,\n", "        b.name as brand,\n", "        pp.shelf_life\n", "    FROM rpc.product_product pp \n", "        INNER JOIN (SELECT item_id, max(id) as max_id\n", "                    FROM rpc.product_product \n", "                    WHERE approved=1 and active = 1 and lake_active_record = true\n", "                    GROUP BY 1) a ON a.item_id = pp.item_id AND a.max_id = pp.id\n", "        INNER JOIN rpc.product_brand b on b.id = pp.brand_id and b.active = 1 AND b.lake_active_record = true\n", "        INNER JOIN rpc.product_manufacturer pm on pm.id = b.manufacturer_id and pm.active = 1 AND pm.lake_active_record = true\n", "\n", "    GROUP BY 1,2,3,4,5,6,7\n", "),\n", "\n", "map as (\n", "    select \n", "    product_id, \n", "    item_id, \n", "    avg_selling_price_ratio, \n", "    avg_mrp_ratio\n", "    from\n", "        (select\n", "        om.product_id,id.item_id,id.name,p.product_name,\n", "        avg(om.avg_selling_price_ratio) avg_selling_price_ratio,\n", "        avg(om.avg_mrp_ratio) as avg_mrp_ratio,\n", "        avg(multiplier) multiplier\n", "        from dwh.dim_item_product_offer_mapping om\n", "        join rpc.item_details id on om.item_id=id.item_id\n", "        join dwh.dim_product p on p.product_id=om.product_Id and p.is_current\n", "        where om.is_current\n", "        group by 1,2,3,4\n", "        )\n", "    -- where product_id in (548090,137060)\n", "\n", "),\n", "\n", "fc_ds_outbound_quantity_flow AS (\n", "    SELECT\n", "        DATE(pi.pos_timestamp + INTERVAL '330' MINUTE) AS date_ist,\n", "        co.facility_id,\n", "        pp.item_id,\n", "        fc.name AS fc_name,\n", "        fc.identifier AS fc_identifier,\n", "        con.id AS pos_outlet_id,\n", "    \n", "        COALESCE(\n", "            SUM(CASE WHEN invoice_type_id IN (1, 9) THEN pd.quantity END)\n", "        ,0) AS b2c_outbound,\n", "        \n", "        COALESCE(\n", "            SUM(CASE WHEN invoice_type_id IN (5, 14, 16) AND con.business_type_id in (7, 8)  THEN pd.quantity END)\n", "        ,0) AS b2b_ds_outbound\n", "    \n", "    FROM\n", "        blinkit.pos.pos_invoice\n", "            AS pi\n", "    JOIN\n", "        blinkit.retail.console_outlet\n", "            AS co\n", "            ON co.id = pi.outlet_id\n", "    JOIN  \n", "        blinkit.retail.warehouse_facility\n", "            AS fc\n", "            ON fc.id = co.facility_id\n", "    JOIN\n", "        blinkit.pos.pos_invoice_product_details\n", "            AS pd\n", "            ON pi.id = pd.invoice_id\n", "    JOIN \n", "        rpc.product_product \n", "            AS pp\n", "            ON pp.variant_id=pd.variant_id\n", "    LEFT JOIN\n", "        blinkit.ims.ims_sto_details\n", "            AS sto\n", "            ON pi.grofers_order_id = CAST(sto.sto_id AS VARCHAR)\n", "    LEFT JOIN\n", "        blinkit.retail.console_outlet\n", "            AS con\n", "            ON con.id = sto.merchant_outlet_id\n", "\n", "    WHERE\n", "        con.name NOT LIKE '%infra%'\n", "        AND pi.pos_timestamp >= date(date_trunc('month', date_add('month', -1, date('{{start_date}}'))))\n", "        AND pi.pos_timestamp  < date(date_trunc('month',date'{{end_date}}'))\n", "        AND pi.insert_ds_ist >= cast(date(date_trunc('month', date_add('month', -1, date('{{start_date}}')))) as varchar)\n", "        AND pd.insert_ds_ist >= cast(date(date_trunc('month', date_add('month', -1, date('{{start_date}}')))) as varchar)\n", "    \n", "    GROUP BY\n", "        1, 2, 3, 4, 5, 6\n", "),\n", "\n", "base_raw AS (\n", "   SELECT\n", "        date_ist,\n", "        facility_id,\n", "        item_id,\n", "        fc_name,\n", "        fc_identifier,\n", "        pos_outlet_id,\n", "        (b2c_outbound + b2b_ds_outbound) AS total_outbound,\n", "        SUM((b2c_outbound + b2b_ds_outbound)) \n", "            OVER(\n", "                PARTITION BY \n", "                    facility_id, \n", "                    date_ist\n", "            ) AS daily_total_fc_outbounds_unit\n", "\n", "    FROM\n", "        fc_ds_outbound_quantity_flow\n", "\n", "    WHERE\n", "        (b2c_outbound + b2b_ds_outbound) <> 0\n", "),\n", "\n", "base_prep as (\n", "select *, row_number() over(partition by month, item_id, pos_outlet_id order by outbound desc) as rnk from (\n", "    SELECT\n", "    date(date_trunc('month',date_ist)) as month,\n", "    item_id,\n", "    facility_id,\n", "    pos_outlet_id,\n", "    sum(total_outbound) as outbound\n", "\n", "FROM\n", "    base_raw\n", "    group by 1,2,3,4\n", "    )\n", ")\n", "\n", ",fin as (\n", "    select \n", "    date(fs.order_deliver_ts_ist) as date_, \n", "    oi.city_name,\n", "    oi.outlet_id,\n", "    o.facility_id,\n", "    oi.product_id,\n", "    p.item_id,\n", "    b.manufacturer_id,\n", "    b.manufacturer_name,\n", "    -- p.manufacturer,\n", "    -- mm.manufacturer_name,\n", "    -- mm.manufacturer_id,\n", "    m.avg_selling_price_ratio, \n", "    m.avg_mrp_ratio,\n", "    sum(oi.procured_quantity*oi.unit_brand_fund*m.avg_selling_price_ratio) as brand_fund,\n", "    sum(oi.total_mrp) as total_mrp,\n", "    sum(oi.total_selling_price) as total_selling_price,\n", "    sum(oi.procured_quantity) as qty_sold,   \n", "    sum(oi.total_mrp*avg_mrp_ratio) as mrp_gmv\n", "    from dwh.fact_sales_order_item_details as oi\n", "    inner join dwh.fact_sales_order_details as fs on oi.order_id = fs.order_id\n", "    left join dwh.dim_product as p on p.product_id = oi.product_id and p.is_current\n", "    left join map as m on m.product_id = oi.product_id\n", "    left join item_map as p on p.item_id = m.item_id\n", "    left join bfs as b on b.date = date(fs.order_deliver_ts_ist)\n", "                       and (case when b.city = 'Gurgaon' then 'HR-NCR'\n", "                                 when b.city = 'Ghaziabad' then 'UP-NCR'\n", "                                 when b.city = 'Bahadurgarh' then 'HR-NCR'\n", "                                 else b.city end) = oi.city_name\n", "                       and b.product_id = oi.product_id\n", "                       and b.item_id = p.item_id\n", "    left join base_prep as o on oi.outlet_id = o.pos_outlet_id\n", "                             and date_trunc('month', date_add('month', -1, date(fs.order_deliver_ts_ist))) = o.month\n", "                             and p.item_id = o.item_id\n", "                             and o.rnk = 1\n", "                                                                        \n", "    where \n", "    oi.order_create_dt_ist >= date'{{start_date}}' - interval '1' day\n", "    and fs.order_create_dt_ist >= date'{{start_date}}' - interval '1' day\n", "    and fs.order_deliver_ts_ist >= date'{{start_date}}'\n", "    and fs.order_deliver_ts_ist < date'{{end_date}}' + interval '1' day\n", "    and oi.order_create_dt_ist < date'{{end_date}}' + interval '1' day\n", "    and fs.order_create_dt_ist < date'{{end_date}}' + interval '1' day\n", "    and oi.order_current_status = 'DELIVERED'\n", "    and oi.is_internal_order = FALSE\n", "    and b.manufacturer_id in ({{manufacturer_id_placeholder}})\n", "    and oi.procured_quantity > 0\n", "    and oi.total_selling_price > 0\n", "    group by 1,2,3,4,5,6,7,8,9,10\n", "    having sum(oi.procured_quantity*oi.unit_brand_fund*m.avg_selling_price_ratio) > 0\n", "    )\n", "\n", "-- select * from fin\n", "\n", ", common_fid as (\n", "    select month,\n", "    pos_outlet_id,\n", "    facility_id,\n", "    row_number() over(partition by month, pos_outlet_id order by outbound desc) as rnk\n", "    from base_prep\n", "    where pos_outlet_id in (select distinct outlet_id from fin where facility_id is null)\n", "    and rnk = 1\n", ")\n", "\n", ", po_data as (\n", "    select \n", "    facility_id,\n", "    item_id,\n", "    entity_name,\n", "    Vendor_Id,\n", "    Vendor_Name,\n", "    grn_city\n", "    from (\n", "    select\n", "    date(po.issue_date + interval '330' minute) as po_date,\n", "    po.outlet_id,\n", "    poi.item_id as item_id,\n", "    co.name as Outlet_Name,\n", "    co.facility_id as facility_id,\n", "    co.location as grn_city,\n", "    lower(cti.legal_name) as entity_name,\n", "    po.vendor_id as Vendor_Id,\n", "    po.vendor_name as Vendor_Name,\n", "    row_number() over(partition by co.facility_id ,item_id order by date(po.issue_date + interval '330' minute) desc) as rk\n", "    from po.purchase_order po\n", "    left join vms.vms_vendor_city_mapping vcm on vcm.vendor_id = po.destination_entity_vendor_id AND vcm.active = 1\n", "    left join vms.vms_vendor_city_tax_info cti on cti.vendor_city_id = vcm.id AND cti.active = 1\n", "    inner join po.purchase_order_items poi on po.id = poi.po_id\n", "    inner join retail.console_outlet co on co.id = po.outlet_id\n", "    inner join retail.console_location cl on cl.id = co.tax_location_id\n", "    where lower(cti.legal_name) <> 'na'\n", "    and po.po_type_id = 2\n", "    and date(po.issue_date + interval '330' minute) >= date'2024-01-01'\n", "    )\n", "    where rk = 1\n", "    group by 1,2,3,4,5,6\n", ")\n", "\n", "\n", ", \n", "po_data_outlet as (\n", "    select \n", "    outlet_id,\n", "    item_id,\n", "    entity_name,\n", "    Vendor_Id,\n", "    Vendor_Name,\n", "    grn_city\n", "    from (\n", "    select\n", "    date(po.issue_date + interval '330' minute) as po_date,\n", "    po.outlet_id,\n", "    poi.item_id as item_id,\n", "    co.name as Outlet_Name,\n", "    co.facility_id as facility_id,\n", "    co.location as grn_city,\n", "    lower(cti.legal_name) as entity_name,\n", "    po.vendor_id as Vendor_Id,\n", "    po.vendor_name as Vendor_Name,\n", "    row_number() over(partition by outlet_id ,item_id order by date(po.issue_date + interval '330' minute) desc) as rk\n", "    from po.purchase_order po\n", "    left join vms.vms_vendor_city_mapping vcm on vcm.vendor_id = po.destination_entity_vendor_id AND vcm.active = 1\n", "    left join vms.vms_vendor_city_tax_info cti on cti.vendor_city_id = vcm.id AND cti.active = 1\n", "    inner join po.purchase_order_items poi on po.id = poi.po_id\n", "    inner join retail.console_outlet co on co.id = po.outlet_id\n", "    inner join retail.console_location cl on cl.id = co.tax_location_id\n", "    where lower(cti.legal_name) <> 'na'\n", "    and po.po_type_id = 2\n", "    and date(po.issue_date + interval '330' minute) >= date'2024-01-01'\n", "    )\n", "    where rk = 1\n", "    group by 1,2,3,4,5,6\n", ")\n", "\n", ", raw_final as (\n", "    select\n", "    date_,\n", "    city_name,\n", "    outlet_id,\n", "    product_id,\n", "    avg_selling_price_ratio, \n", "    avg_mrp_ratio,\n", "    a.item_id,\n", "    manufacturer_name, \n", "    manufacturer_id,\n", "    brand_fund,\n", "    qty_sold,\n", "    mrp_gmv,\n", "    total_mrp,\n", "    case when a.facility_id is not null then a.facility_id else b.facility_id end as facility_id,\n", "    p.entity_name,\n", "    Vendor_Name as vendor_name,\n", "    Vendor_Id as vendor_id,\n", "    grn_city\n", "    \n", "    \n", "    from fin as a\n", "    left join common_fid as b on b.month = date_trunc('month', date_add('month', -1, date(date_)))\n", "                              and b.pos_outlet_id = a.outlet_id\n", "                              and b.rnk = 1\n", "    left join po_data as p on p.facility_id = (case when a.facility_id is not null then a.facility_id else b.facility_id end)\n", "                          and p.item_id = a.item_id\n", "    )\n", "    \n", ",pre_base as (\n", "    select\n", "    date_,\n", "    city_name,\n", "    coalesce(a.grn_city,k.grn_city) as grn_city,\n", "    a.product_id,\n", "    a.item_id,\n", "    a.manufacturer_id,\n", "    a.manufacturer_name,\n", "    case when coalesce(a.Vendor_Name,k.Vendor_Name) = 'Zomato Hyperpure Private Limited' then 'Zomato Hyperpure Private Limited'\n", "         else coalesce(a.entity_name,k.entity_name) end as entity_final,\n", "    coalesce(a.vendor_id,k.Vendor_Id) as vendor_id,\n", "    coalesce(a.vendor_name,k.Vendor_Name) as vendor_name,\n", "    brand_fund as total_brand_fund\n", "\n", "    from raw_final as a\n", "    left join po_data_outlet as k on k.outlet_id = a.outlet_id\n", "                                  and k.item_id = a.item_id\n", "                                  and a.entity_name is null\n", ")\n", "\n", "\n", "\n", "select\n", "date(date_trunc('month',date_)) as current_month,\n", "city_name,\n", "grn_city,\n", "manufacturer_id,\n", "manufacturer_name,\n", "vendor_id,\n", "vendor_name,\n", "entity_final,\n", "sum(total_brand_fund) as total_brand_fund\n", "from pre_base\n", "group by 1,2,3,4,5,6,7,8\n", "\n", "\"\"\".replace(\n", "        \"%\", \"%%\"\n", "    )\n", "    .replace(\"{{\", \"{\")\n", "    .replace(\"}}\", \"}\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "cb472d46-c860-4907-ad2b-9f6b0c48da6d", "metadata": {}, "outputs": [], "source": ["# data = \"\"\"\n", "\n", "# with bfs as (\n", "#     select *\n", "#     from (\n", "#         select b.date,\n", "#         b.city,\n", "#         b.product_id,\n", "#         b.item_id,\n", "#         b.manufacturer_id,\n", "#         max(lower(b.manufacturer)) as manufacturer_name--,\n", "#         -- row_number() over(partition by date, city, product_id, item_id, manufacturer_id, manufacturer order by qty_sold desc) as rnk\n", "#         from pricing_v3.attribute_management_brandsfundsummary as b\n", "#         where b.partition_field >= ('{{start_date}}')\n", "#               and b.date >= date('{{start_date}}')\n", "#               and b.date <= date('{{end_date}}')\n", "#               and b.lake_active_record = True\n", "#               and is_active\n", "#               AND b.id NOT IN (SELECT id\n", "#                                  FROM pricing_v3.attribute_management_brandsfundsummary\n", "#                                  WHERE partition_field >= ('{{start_date}}')\n", "#                                  and is_active=FALSE)\n", "#         group by 1,2,3,4,5--,6--,7\n", "#         )\n", "#         -- where rnk > 1\n", "#     --   limit 100\n", "# ),\n", "\n", "# item_tax as (\n", "#     select\n", "#     item_id,\n", "#     max(case when coalesce(igst,0) = 0 then (COALESCE(cgst,0) + COALESCE(cess,0) + COALESCE(sgst,0)) else (coalesce(igst,0) + COALESCE(cess,0)) end) as product_tax,\n", "#     max(coalesce(additional_cess_value,0)) as add_cess_value\n", "#     from\n", "#         (\n", "#          select *, row_number() over(partition by item_id order by updated_at desc) as rnk\n", "#          from rpc.product_tax\n", "#         )\n", "#         where rnk=1\n", "#         group by 1\n", "# ),\n", "\n", "# item_map as (\n", "#  SELECT\n", "#         pp.item_id,\n", "#         pp.name as item_name,\n", "#         pp.brand_id,\n", "#         pm.id as manufacturer_id,\n", "#         pm.name as manufacturer,\n", "#         b.name as brand,\n", "#         pp.shelf_life\n", "#     FROM rpc.product_product pp\n", "#         INNER JOIN (SELECT item_id, max(id) as max_id\n", "#                     FROM rpc.product_product\n", "#                     WHERE approved=1 and active = 1 and lake_active_record = true\n", "#                     GROUP BY 1) a ON a.item_id = pp.item_id AND a.max_id = pp.id\n", "#         INNER JOIN rpc.product_brand b on b.id = pp.brand_id and b.active = 1 AND b.lake_active_record = true\n", "#         INNER JOIN rpc.product_manufacturer pm on pm.id = b.manufacturer_id and pm.active = 1 AND pm.lake_active_record = true\n", "\n", "#     GROUP BY 1,2,3,4,5,6,7\n", "# ),\n", "\n", "# map as (\n", "#     select\n", "#     product_id,\n", "#     item_id,\n", "#     avg_selling_price_ratio,\n", "#     avg_mrp_ratio\n", "#     from\n", "#         (select\n", "#         om.product_id,id.item_id,id.name,p.product_name,\n", "#         avg(om.avg_selling_price_ratio) avg_selling_price_ratio,\n", "#         avg(om.avg_mrp_ratio) as avg_mrp_ratio,\n", "#         avg(multiplier) multiplier\n", "#         from dwh.dim_item_product_offer_mapping om\n", "#         join rpc.item_details id on om.item_id=id.item_id\n", "#         join dwh.dim_product p on p.product_id=om.product_Id and p.is_current\n", "#         where om.is_current\n", "#         group by 1,2,3,4\n", "#         )\n", "#     -- where product_id in (548090,137060)\n", "\n", "# ),\n", "\n", "# fc_ds_outbound_quantity_flow AS (\n", "#     SELECT\n", "#         DATE(pi.pos_timestamp + INTERVAL '330' MINUTE) AS date_ist,\n", "#         co.facility_id,\n", "#         pp.item_id,\n", "#         fc.name AS fc_name,\n", "#         fc.identifier AS fc_identifier,\n", "#         con.id AS pos_outlet_id,\n", "\n", "#         COALESCE(\n", "#             SUM(CASE WHEN invoice_type_id IN (1, 9) THEN pd.quantity END)\n", "#         ,0) AS b2c_outbound,\n", "\n", "#         COALESCE(\n", "#             SUM(CASE WHEN invoice_type_id IN (5, 14, 16) AND con.business_type_id in (7, 8)  THEN pd.quantity END)\n", "#         ,0) AS b2b_ds_outbound\n", "\n", "#     FROM\n", "#         blinkit.pos.pos_invoice\n", "#             AS pi\n", "#     JOIN\n", "#         blinkit.retail.console_outlet\n", "#             AS co\n", "#             ON co.id = pi.outlet_id\n", "#     JOIN\n", "#         blinkit.retail.warehouse_facility\n", "#             AS fc\n", "#             ON fc.id = co.facility_id\n", "#     JOIN\n", "#         blinkit.pos.pos_invoice_product_details\n", "#             AS pd\n", "#             ON pi.id = pd.invoice_id\n", "#     JOIN\n", "#         rpc.product_product\n", "#             AS pp\n", "#             ON pp.variant_id=pd.variant_id\n", "#     LEFT JOIN\n", "#         blinkit.ims.ims_sto_details\n", "#             AS sto\n", "#             ON pi.grofers_order_id = CAST(sto.sto_id AS VARCHAR)\n", "#     LEFT JOIN\n", "#         blinkit.retail.console_outlet\n", "#             AS con\n", "#             ON con.id = sto.merchant_outlet_id\n", "\n", "#     WHERE\n", "#         con.name NOT LIKE '%infra%'\n", "#         AND pi.pos_timestamp >= date(date_trunc('month', date_add('month', -1, date('{{start_date}}'))))\n", "#         AND pi.pos_timestamp  < date(date_trunc('month',date'{{end_date}}'))\n", "#         AND pi.insert_ds_ist >= cast(date(date_trunc('month', date_add('month', -1, date('{{start_date}}')))) as varchar)\n", "#         AND pd.insert_ds_ist >= cast(date(date_trunc('month', date_add('month', -1, date('{{start_date}}')))) as varchar)\n", "\n", "#     GROUP BY\n", "#         1, 2, 3, 4, 5, 6\n", "# ),\n", "\n", "# base_raw AS (\n", "#    SELECT\n", "#         date_ist,\n", "#         facility_id,\n", "#         item_id,\n", "#         fc_name,\n", "#         fc_identifier,\n", "#         pos_outlet_id,\n", "#         (b2c_outbound + b2b_ds_outbound) AS total_outbound,\n", "#         SUM((b2c_outbound + b2b_ds_outbound))\n", "#             OVER(\n", "#                 PARTITION BY\n", "#                     facility_id,\n", "#                     date_ist\n", "#             ) AS daily_total_fc_outbounds_unit\n", "\n", "#     FROM\n", "#         fc_ds_outbound_quantity_flow\n", "\n", "#     WHERE\n", "#         (b2c_outbound + b2b_ds_outbound) <> 0\n", "# ),\n", "\n", "# base_prep as (\n", "# select *, row_number() over(partition by month, item_id, pos_outlet_id order by outbound desc) as rnk from (\n", "#     SELECT\n", "#     date(date_trunc('month',date_ist)) as month,\n", "#     item_id,\n", "#     facility_id,\n", "#     pos_outlet_id,\n", "#     sum(total_outbound) as outbound\n", "\n", "# FROM\n", "#     base_raw\n", "#     group by 1,2,3,4\n", "#     )\n", "# )\n", "\n", "# ,fin as (\n", "#     select\n", "#     date(fs.order_deliver_ts_ist) as date_,\n", "#     oi.city_name,\n", "#     oi.outlet_id,\n", "#     o.facility_id,\n", "#     oi.product_id,\n", "#     p.item_id,\n", "#     b.manufacturer_id,\n", "#     b.manufacturer_name,\n", "#     -- p.manufacturer,\n", "#     -- mm.manufacturer_name,\n", "#     -- mm.manufacturer_id,\n", "#     m.avg_selling_price_ratio,\n", "#     m.avg_mrp_ratio,\n", "#     sum(oi.procured_quantity*oi.unit_brand_fund*m.avg_selling_price_ratio) as brand_fund,\n", "#     sum(oi.total_mrp) as total_mrp,\n", "#     sum(oi.total_selling_price) as total_selling_price,\n", "#     sum(oi.procured_quantity) as qty_sold,\n", "#     sum(oi.total_mrp*avg_mrp_ratio) as mrp_gmv,\n", "#     sum(case when (oi.procured_quantity*oi.unit_brand_fund*m.avg_selling_price_ratio) = 0 then 0\n", "#              else ((((oi.procured_quantity*oi.unit_brand_fund*m.avg_selling_price_ratio) - coalesce(it.add_cess_value,0)*(oi.procured_quantity))/(1+coalesce(it.product_tax,0)/100))) end) as brand_fund_post_tax\n", "#     from dwh.fact_sales_order_item_details as oi\n", "#     inner join dwh.fact_sales_order_details as fs on oi.order_id = fs.order_id\n", "#     left join dwh.dim_product as p on p.product_id = oi.product_id and p.is_current\n", "#     left join map as m on m.product_id = oi.product_id\n", "#     left join item_map as p on p.item_id = m.item_id\n", "#     left join bfs as b on b.date = date(fs.order_deliver_ts_ist)\n", "#                        and (case when b.city = 'Gurgaon' then 'HR-NCR'\n", "#                                  when b.city = 'Ghaziabad' then 'UP-NCR'\n", "#                                  when b.city = 'Bahadurgarh' then 'HR-NCR'\n", "#                                  else b.city end) = oi.city_name\n", "#                        and b.product_id = oi.product_id\n", "#                        and b.item_id = p.item_id\n", "#     left join base_prep as o on oi.outlet_id = o.pos_outlet_id\n", "#                              and date_trunc('month', date_add('month', -1, date(fs.order_deliver_ts_ist))) = o.month\n", "#                              and p.item_id = o.item_id\n", "#                              and o.rnk = 1\n", "#     left join item_tax it on it.item_id=p.item_id\n", "\n", "#     where\n", "#     oi.order_create_dt_ist >= date'{{start_date}}' - interval '1' day\n", "#     and fs.order_create_dt_ist >= date'{{start_date}}' - interval '1' day\n", "#     and fs.order_deliver_ts_ist >= date'{{start_date}}'\n", "#     and fs.order_deliver_ts_ist < date'{{end_date}}' + interval '1' day\n", "#     and oi.order_create_dt_ist < date'{{end_date}}' + interval '1' day\n", "#     and fs.order_create_dt_ist < date'{{end_date}}' + interval '1' day\n", "#     and oi.order_current_status = 'DELIVERED'\n", "#     and oi.is_internal_order = FALSE\n", "#     and b.manufacturer_id in ({{manufacturer_id_placeholder}})\n", "#     and oi.procured_quantity > 0\n", "#     and oi.total_selling_price > 0\n", "#     group by 1,2,3,4,5,6,7,8,9,10\n", "#     having sum(oi.procured_quantity*oi.unit_brand_fund*m.avg_selling_price_ratio) > 0\n", "#     )\n", "\n", "# -- select * from fin\n", "\n", "# , common_fid as (\n", "#     select month,\n", "#     pos_outlet_id,\n", "#     facility_id,\n", "#     row_number() over(partition by month, pos_outlet_id order by outbound desc) as rnk\n", "#     from base_prep\n", "#     where pos_outlet_id in (select distinct outlet_id from fin where facility_id is null)\n", "#     and rnk = 1\n", "# )\n", "\n", "# , po_data as (\n", "#     select\n", "#     facility_id,\n", "#     item_id,\n", "#     entity_name,\n", "#     Vendor_Id,\n", "#     Vendor_Name,\n", "#     grn_city\n", "#     from (\n", "#     select\n", "#     date(po.issue_date + interval '330' minute) as po_date,\n", "#     po.outlet_id,\n", "#     poi.item_id as item_id,\n", "#     co.name as Outlet_Name,\n", "#     co.facility_id as facility_id,\n", "#     co.location as grn_city,\n", "#     lower(cti.legal_name) as entity_name,\n", "#     po.vendor_id as Vendor_Id,\n", "#     po.vendor_name as Vendor_Name,\n", "#     row_number() over(partition by co.facility_id ,item_id order by date(po.issue_date + interval '330' minute) desc) as rk\n", "#     from po.purchase_order po\n", "#     left join vms.vms_vendor_city_mapping vcm on vcm.vendor_id = po.destination_entity_vendor_id AND vcm.active = 1\n", "#     left join vms.vms_vendor_city_tax_info cti on cti.vendor_city_id = vcm.id AND cti.active = 1\n", "#     inner join po.purchase_order_items poi on po.id = poi.po_id\n", "#     inner join retail.console_outlet co on co.id = po.outlet_id\n", "#     inner join retail.console_location cl on cl.id = co.tax_location_id\n", "#     where lower(cti.legal_name) <> 'na'\n", "#     and po.po_type_id = 2\n", "#     and date(po.issue_date + interval '330' minute) >= date'2024-01-01'\n", "#     )\n", "#     where rk = 1\n", "#     group by 1,2,3,4,5,6\n", "# )\n", "\n", "\n", "# ,\n", "# po_data_outlet as (\n", "#     select\n", "#     outlet_id,\n", "#     item_id,\n", "#     entity_name,\n", "#     Vendor_Id,\n", "#     Vendor_Name,\n", "#     grn_city\n", "#     from (\n", "#     select\n", "#     date(po.issue_date + interval '330' minute) as po_date,\n", "#     po.outlet_id,\n", "#     poi.item_id as item_id,\n", "#     co.name as Outlet_Name,\n", "#     co.facility_id as facility_id,\n", "#     co.location as grn_city,\n", "#     lower(cti.legal_name) as entity_name,\n", "#     po.vendor_id as Vendor_Id,\n", "#     po.vendor_name as Vendor_Name,\n", "#     row_number() over(partition by outlet_id ,item_id order by date(po.issue_date + interval '330' minute) desc) as rk\n", "#     from po.purchase_order po\n", "#     left join vms.vms_vendor_city_mapping vcm on vcm.vendor_id = po.destination_entity_vendor_id AND vcm.active = 1\n", "#     left join vms.vms_vendor_city_tax_info cti on cti.vendor_city_id = vcm.id AND cti.active = 1\n", "#     inner join po.purchase_order_items poi on po.id = poi.po_id\n", "#     inner join retail.console_outlet co on co.id = po.outlet_id\n", "#     inner join retail.console_location cl on cl.id = co.tax_location_id\n", "#     where lower(cti.legal_name) <> 'na'\n", "#     and po.po_type_id = 2\n", "#     and date(po.issue_date + interval '330' minute) >= date'2024-01-01'\n", "#     )\n", "#     where rk = 1\n", "#     group by 1,2,3,4,5,6\n", "# )\n", "\n", "# , raw_final as (\n", "#     select\n", "#     date_,\n", "#     city_name,\n", "#     outlet_id,\n", "#     product_id,\n", "#     avg_selling_price_ratio,\n", "#     avg_mrp_ratio,\n", "#     a.item_id,\n", "#     manufacturer_name,\n", "#     manufacturer_id,\n", "#     brand_fund,\n", "#     qty_sold,\n", "#     mrp_gmv,\n", "#     brand_fund_post_tax,\n", "#     total_mrp,\n", "#     case when a.facility_id is not null then a.facility_id else b.facility_id end as facility_id,\n", "#     p.entity_name,\n", "#     Vendor_Name as vendor_name,\n", "#     Vendor_Id as vendor_id,\n", "#     grn_city\n", "\n", "\n", "#     from fin as a\n", "#     left join common_fid as b on b.month = date_trunc('month', date_add('month', -1, date(date_)))\n", "#                               and b.pos_outlet_id = a.outlet_id\n", "#                               and b.rnk = 1\n", "#     left join po_data as p on p.facility_id = (case when a.facility_id is not null then a.facility_id else b.facility_id end)\n", "#                           and p.item_id = a.item_id\n", "#     )\n", "\n", "# ,pre_base as (\n", "#     select\n", "#     date_,\n", "#     city_name,\n", "#     coalesce(a.grn_city,k.grn_city) as grn_city,\n", "#     a.product_id,\n", "#     a.item_id,\n", "#     a.manufacturer_id,\n", "#     a.manufacturer_name,\n", "#     case when coalesce(a.Vendor_Name,k.Vendor_Name) = 'Zomato Hyperpure Private Limited' then 'Zomato Hyperpure Private Limited'\n", "#          else coalesce(a.entity_name,k.entity_name) end as entity_final,\n", "#     coalesce(a.vendor_id,k.Vendor_Id) as vendor_id,\n", "#     coalesce(a.vendor_name,k.Vendor_Name) as vendor_name,\n", "#     brand_fund as total_brand_fund,\n", "#     brand_fund_post_tax\n", "\n", "#     from raw_final as a\n", "#     left join po_data_outlet as k on k.outlet_id = a.outlet_id\n", "#                                   and k.item_id = a.item_id\n", "#                                   and a.entity_name is null\n", "# )\n", "\n", "\n", "# select\n", "# date(date_trunc('month',date_)) as current_month,\n", "# city_name,\n", "# grn_city,\n", "# manufacturer_id,\n", "# manufacturer_name,\n", "# vendor_id,\n", "# vendor_name,\n", "# entity_final,\n", "# sum(total_brand_fund) as total_brand_fund,\n", "# sum(brand_fund_post_tax) as brand_fund_post_tax\n", "# from pre_base\n", "# group by 1,2,3,4,5,6,7,8\n", "\n", "# \"\"\".replace(\"%\", \"%%\").replace(\"{{\", \"{\").replace(\"}}\", \"}\")"]}, {"cell_type": "code", "execution_count": null, "id": "b34714af-9053-4035-bc74-af275d47a603", "metadata": {}, "outputs": [], "source": ["query = pd.read_sql(\n", "    data.format(\n", "        start_date=start_date,\n", "        end_date=end_date,\n", "        manufacturer_id_placeholder=manufacturer_id_placeholder,\n", "    ),\n", "    con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5dbf4a5f-cd29-4994-a9c6-562308da607a", "metadata": {}, "outputs": [], "source": ["query.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7e66dac8-af4a-4d5f-9f89-93c4822aef62", "metadata": {}, "outputs": [], "source": ["query.shape"]}, {"cell_type": "code", "execution_count": null, "id": "e147f290-a193-40c9-84e0-a92518ecfcc9", "metadata": {}, "outputs": [], "source": ["query = query.fillna(0)\n", "query[\"current_month\"] = pd.to_datetime(query[\"current_month\"])\n", "query[\"city_name\"] = query[\"city_name\"].astype(str)\n", "query[\"grn_city\"] = query[\"grn_city\"].astype(str)\n", "query[\"manufacturer_id\"] = query[\"manufacturer_id\"].astype(int)\n", "query[\"manufacturer_name\"] = query[\"manufacturer_name\"].astype(str)\n", "query[\"vendor_id\"] = query[\"vendor_id\"].astype(float)\n", "query[\"vendor_name\"] = query[\"vendor_name\"].astype(str)\n", "query[\"entity_final\"] = query[\"entity_final\"].astype(str)\n", "query[\"total_brand_fund\"] = query[\"total_brand_fund\"].astype(float)\n", "# query[\"brand_fund_post_tax\"] = query[\"brand_fund_post_tax\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "6eb3bcf9-b2cf-4f83-abe6-a81e6eab46e6", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"fin_etls\",\n", "    \"table_name\": \"brand_fund_split_pre_adjustment_apr_dp_new\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"current_month\", \"type\": \"DATE\", \"description\": \"date of creation of order\"},\n", "        {\"name\": \"city_name\", \"type\": \"VARCHAR\", \"description\": \"outlet id\"},\n", "        {\"name\": \"grn_city\", \"type\": \"VARCHAR\", \"description\": \"date of creation of order\"},\n", "        {\"name\": \"manufacturer_id\", \"type\": \"INTEGER\", \"description\": \"outlet id\"},\n", "        {\n", "            \"name\": \"manufacturer_name\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"date of creation of order\",\n", "        },\n", "        {\"name\": \"vendor_id\", \"type\": \"DOUBLE\", \"description\": \"date of creation of order\"},\n", "        {\"name\": \"vendor_name\", \"type\": \"VARCHAR\", \"description\": \"outlet id\"},\n", "        {\"name\": \"entity_final\", \"type\": \"VARCHAR\", \"description\": \"date of creation of order\"},\n", "        {\"name\": \"total_brand_fund\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        # {\"name\": \"brand_fund_post_tax\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "    ],\n", "    \"primary_key\": [\n", "        \"current_month\",\n", "        \"city_name\",\n", "        \"grn_city\",\n", "        \"manufacturer_id\",\n", "        \"vendor_id\",\n", "        \"entity_final\",\n", "    ],\n", "    \"partition_key\": [\"current_month\"],\n", "    \"incremental_key\": [],\n", "    \"load_type\": \"partition_overwrite\",\n", "    \"force_upsert_without_increment_check\": True,\n", "    \"table_description\": \"this table is made temporary data push\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "27cbc8a4-7270-4956-9d54-fea9d8d1586b", "metadata": {}, "outputs": [], "source": ["pb.to_trino(query, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}