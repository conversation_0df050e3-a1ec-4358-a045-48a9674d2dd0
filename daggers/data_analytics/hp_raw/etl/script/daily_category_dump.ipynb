{"cells": [{"cell_type": "code", "execution_count": null, "id": "c22b1fa5-b994-41a3-9862-0ba98e01141d", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "from datetime import date\n", "from datetime import timedelta\n", "from datetime import datetime\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\").connect()"]}, {"cell_type": "code", "execution_count": null, "id": "fc027a0a-e1bf-48a5-8a0e-fc374dce27f5", "metadata": {}, "outputs": [], "source": ["q = \"\"\"\n", "\n", "with loss as (\n", "select\n", "day,\n", "sum(net_dump_value+reinventorization_diff_value-reinventorization_diff_cp) net_dump_value,\n", "sum(case when category='fnv' then net_dump_value end) as fnv_net_dump_value,\n", "sum(case when category='perishable' then net_dump_value end) as perishable_net_dump_value,\n", "sum(case when category='packaged' then net_dump_value end) as packaged_net_dump_value,\n", "sum(pilferage_ds_value) pilferage_ds_value,\n", "sum(breakage_ds_value) breakage_ds_value,\n", "sum(pilferage_breakage_value) pilferage_breakage_value,\n", "sum(warehouse_variance_value) warehouse_variance_value,\n", "sum(case when outlet_id in (1644,4306,4305,481) then warehouse_variance_value end) as  warehouse_variance_value_3pl\n", "\n", "\n", "from ba_etls.inventory_dump_rough\n", "where day >= date_trunc('week',current_date - interval '35' day)\n", "and outlet_id not in (4146, 5154)\n", "group by 1\n", "),\n", "\n", "\n", "fr as (\n", "\n", "select\n", "date(dump_date_ist) as date_,\n", "\n", "    SUM(fwd_rejection_dump) AS fwd_rejection_dump, \n", "    SUM(fwd_rejection_dump_quan) AS fwd_rejection_dump_quan\n", "    \n", "from supply_etls.fnv_milk_perishables_dump_raw dr\n", "inner join rpc.item_category_details icd on icd.item_id = dr.item_id and icd.l0_id in (1487)\n", "and dump_date_ist >= date_trunc('week',current_date - interval '35' day) \n", "group by 1\n", "),\n", "\n", "-- gmv as (\n", "\n", "-- select\n", "-- date(fs.order_deliver_ts_ist) date_,\n", "-- sum(case when coalesce(ct0.category_group,ct1.category_group,ct2.category_group) = 'fnv' then oi.unit_selling_price*oi.procured_quantity end) as  fnv_gmv,\n", "-- sum(case when coalesce(ct0.category_group,ct1.category_group,ct2.category_group) = 'perishable' then oi.unit_selling_price*oi.procured_quantity end) as  perishabe_gmv,\n", "-- sum(case when coalesce(ct0.category_group,ct1.category_group,ct2.category_group) = 'packaged' then oi.unit_selling_price*oi.procured_quantity end) as  packaged_gmv,\n", "-- sum(oi.unit_selling_price*oi.procured_quantity) as sp_gmv,\n", "-- count(distinct oi.order_id) as orders,\n", "-- count(distinct case when coalesce(ct0.category_group,ct1.category_group,ct2.category_group) = 'fnv' then oi.order_id end) as  fnv_orders,\n", "-- count(distinct case when coalesce(ct0.category_group,ct1.category_group,ct2.category_group) = 'perishable' then oi.order_id end) as  perishabe_orders,\n", "-- count(distinct case when coalesce(ct0.category_group,ct1.category_group,ct2.category_group) = 'packaged' then oi.order_id end) as  packaged_orders\n", "\n", "-- from dwh.fact_sales_order_item_details oi\n", "-- inner join dwh.fact_sales_order_details fs on fs.order_id = oi.order_id\n", "-- left join dwh.dim_product c on c.product_id = oi.product_id and c.is_current\n", "-- left join ba_etls.pnl_all_category_tag_ ct0 on ct0.l0_category_id=cast(c.l0_category_id as varchar) and ct0.l1_category_id='-1' and ct0.l2_category_id='-1'\n", "-- left join ba_etls.pnl_all_category_tag_ ct1 on ct1.l1_category_id=cast(c.l1_category_id as varchar) and ct1.l2_category_id='-1'\n", "-- left join ba_etls.pnl_all_category_tag_ ct2 on ct2.l2_category_id=cast(c.l2_category_id as varchar)\n", "\n", "\n", "\n", "-- where oi.order_create_dt_ist >= date_trunc('month',current_date - interval '35' day)\n", "--     and fs.order_create_dt_ist >= date_trunc('month',current_date - interval '35' day)\n", "--     and fs.order_deliver_ts_ist >= date_trunc('month',current_date - interval '35' day)\n", "--     and fs.order_deliver_ts_ist < current_date\n", "--     and oi.is_internal_order = false\n", "--     and oi.procured_quantity > 0\n", "--     and oi.unit_selling_price > 0\n", "--     and oi.order_current_status = 'DELIVERED'\n", "--     and coalesce(c.brand_name,'blank') != 'Apple' \n", "-- group by 1\n", "\n", "-- ),\n", "\n", "\n", "goodness as (\n", "SELECT \n", "date_ist,\n", "SUM(refund_goodness_amount) AS refund_goodness_amount\n", "\n", "FROM dwh.flat_cancelled_order_item_unused_refunds\n", "WHERE date_ist >= date_trunc('week',current_date - interval '35' day) and date_ist>=date'2024-08-27'\n", "GROUP BY  1\n", "),\n", "\n", "rl as (\n", "select\n", "day,\n", "sum(bad_returns) bad_returns,\n", "sum(unaccounted) unaccounted,\n", "sum(return_loss) return_loss\n", "\n", "from dwh.flat_order_item_returns s\n", "where day >= date_trunc('week',current_date - interval '35' day)\n", "group by 1\n", "),\n", "\n", "scrap as (\n", "select \n", "date(pi.insert_ds_ist) as date_,\n", "sum(pipd.quantity * selling_price) as secondary_sale_value\n", "\n", "from pos.pos_invoice pi\n", "join pos.pos_invoice_product_details pipd on pipd.invoice_id = pi.id\n", "join pos.pos_customer_tax_details p on p.invoice_id = pipd.invoice_id\n", "join vms.vms_vendor v on v.id = p.vms_merchant_id and v.type_id = 3\n", "join retail.console_outlet r on r.id = pi.outlet_id and r.active = 1 --and r.device_id != 47\n", "join rpc.product_product prod on prod.variant_id = pipd.variant_id \n", "\n", "where invoice_type_id = 12\n", "and pi.insert_ds_ist >= cast(date_trunc('week',current_date - interval '35' day) as varchar)\n", "and pipd.insert_ds_ist >=  cast(date_trunc('week',current_date - interval '35' day) as varchar)\n", "and p.insert_ds_ist >=  cast(date_trunc('week',current_date - interval '35' day) as varchar)\n", "\n", "and r.business_type_id != 8\n", "        \n", "and (lower(r.name) like '%%scrap%%' or item_id in (10111098,10111099,10122393,10122392,10122394,10122395,10122396,10122397))\n", "        \n", "group by 1\n", ")\n", "\n", "\n", "select\n", "date(l.day) as day,\n", "l.net_dump_value,\n", "l.fnv_net_dump_value,\n", "l.perishable_net_dump_value,\n", "l.packaged_net_dump_value,\n", "l.pilferage_ds_value,\n", "l.breakage_ds_value,\n", "l.pilferage_breakage_value,\n", "l.warehouse_variance_value,\n", "l.warehouse_variance_value_3pl,\n", "fr.fwd_rejection_dump as fnv_fr,\n", "0 as fnv_gmv,\n", "0 as perishabe_gmv,\n", "0 as packaged_gmv,\n", "0 as sp_gmv,\n", "0 as orders,\n", "0 as fnv_orders,\n", "0 as perishabe_orders,\n", "0 as packaged_orders,\n", "rl.bad_returns,\n", "coalesce(rl.unaccounted,0) -  coalesce(gd.refund_goodness_amount,0) as unaccounted,\n", "coalesce(rl.return_loss,0) - coalesce(gd.refund_goodness_amount,0) as return_loss,\n", "sc.secondary_sale_value\n", "\n", "\n", "from loss l\n", "-- left join gmv g on l.day=g.date_\n", "left join rl on rl.day=l.day\n", "left join fr on fr.date_=l.day\n", "left join scrap sc on sc.date_=l.day\n", "left join goodness gd on gd.date_ist=l.day\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "9c5a8692-2521-4409-9e08-0e452a912af2", "metadata": {}, "outputs": [], "source": ["data = pd.read_sql(q, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "7da25bed-b2e6-4c30-8ede-da433f16bb97", "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0468ed9c-28e2-4596-bf72-bdfe9f1d1ca5", "metadata": {}, "outputs": [], "source": ["data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "0da2fd51-5fc6-4579-b9fc-fe135ba87763", "metadata": {}, "outputs": [], "source": ["data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "36a5cb8e-e272-4751-8840-0555fa319955", "metadata": {}, "outputs": [], "source": ["data[\"day\"] = pd.to_datetime(data[\"day\"]).dt.date\n", "data[\"day\"] = pd.to_datetime(data[\"day\"]).dt.strftime(\"%Y-%m-%d\")\n", "data[\"net_dump_value\"] = data[\"net_dump_value\"].astype(float)\n", "data[\"fnv_net_dump_value\"] = data[\"fnv_net_dump_value\"].astype(float)\n", "data[\"perishable_net_dump_value\"] = data[\"perishable_net_dump_value\"].astype(float)\n", "data[\"packaged_net_dump_value\"] = data[\"packaged_net_dump_value\"].astype(float)\n", "data[\"pilferage_ds_value\"] = data[\"pilferage_ds_value\"].astype(float)\n", "data[\"breakage_ds_value\"] = data[\"breakage_ds_value\"].astype(float)\n", "data[\"pilferage_breakage_value\"] = data[\"pilferage_breakage_value\"].astype(float)\n", "data[\"warehouse_variance_value\"] = data[\"warehouse_variance_value\"].astype(float)\n", "data[\"warehouse_variance_value_3pl\"] = data[\"warehouse_variance_value_3pl\"].astype(float)\n", "data[\"fnv_fr\"] = data[\"fnv_fr\"].astype(float)\n", "data[\"fnv_gmv\"] = data[\"fnv_gmv\"].astype(float)\n", "data[\"perishabe_gmv\"] = data[\"perishabe_gmv\"].astype(float)\n", "data[\"packaged_gmv\"] = data[\"packaged_gmv\"].astype(float)\n", "data[\"sp_gmv\"] = data[\"sp_gmv\"].astype(float)\n", "data[\"orders\"] = data[\"orders\"].astype(int)\n", "data[\"fnv_orders\"] = data[\"fnv_orders\"].astype(int)\n", "data[\"perishabe_orders\"] = data[\"perishabe_orders\"].astype(int)\n", "data[\"packaged_orders\"] = data[\"packaged_orders\"].astype(int)\n", "data[\"bad_returns\"] = data[\"bad_returns\"].astype(float)\n", "data[\"unaccounted\"] = data[\"unaccounted\"].astype(float)\n", "data[\"return_loss\"] = data[\"return_loss\"].astype(float)\n", "data[\"secondary_sale_value\"] = data[\"secondary_sale_value\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "6c8d7161-4ab2-4226-be50-1171209f686c", "metadata": {}, "outputs": [], "source": ["data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "95a29b8a-1dd1-4e26-8def-52faea226fa6", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1GoQqMSmxNUwPbBRaIetMgSW6PM2EAkcxRNtK_KyypvA\"\n", "sheet_name = \"daily_category_dump\"\n", "pb.to_sheets(data, sheet_id, sheet_name)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}