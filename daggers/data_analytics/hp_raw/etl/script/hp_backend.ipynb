{"cells": [{"cell_type": "code", "execution_count": null, "id": "c22b1fa5-b994-41a3-9862-0ba98e01141d", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "from datetime import date\n", "from datetime import timedelta\n", "from datetime import datetime\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\").connect()"]}, {"cell_type": "code", "execution_count": null, "id": "fc027a0a-e1bf-48a5-8a0e-fc374dce27f5", "metadata": {}, "outputs": [], "source": ["q = \"\"\"\n", "\n", "with perishable_skus_base as \n", "(SELECT item_id, \n", "       item_name, \n", "       l2\n", "FROM (\n", "    SELECT DISTINCT \n", "           ma.item_id, \n", "           icd.name AS item_name,\n", "           CASE \n", "               WHEN l0_id IN (1487) THEN 'FnV'\n", "               WHEN (l2_id IN (1425) AND ma.perishable = 1) THEN 'Batter'\n", "               WHEN (l2_id IN (31, 116, 198, 1097, 1956) AND ma.perishable = 1) THEN 'Breads'\n", "               WHEN (l2_id IN (949) AND ma.perishable = 1) THEN 'Curd'\n", "               WHEN (l2_id IN (1389, 1778) AND ma.perishable = 1) THEN 'Eggs'\n", "               WHEN (l2_id IN (63, 1367, 1369) AND ma.perishable = 1) THEN 'Meats'\n", "               WHEN (l2_id IN (1185) AND ma.perishable = 1) THEN 'Milk'\n", "               WHEN (l2_id IN (950) AND ma.perishable = 1) THEN 'Paneer'\n", "               WHEN (l2_id IN (138, 1091, 1093) AND ma.perishable = 1) THEN 'Perishable Dairy'\n", "               WHEN (l2_id IN (1094) AND ma.perishable = 1) THEN 'Yogurt'\n", "               WHEN (CAST(ma.storage_type AS INT) IN (3, 7) AND l1_id NOT IN (183)) THEN 'Frozen'\n", "               WHEN l1_id IN (183) THEN 'Ice Cream'\n", "               ELSE 'Other SKU'\n", "           END AS l2\n", "    FROM lake_rpc.product_product ma\n", "    JOIN (\n", "        SELECT item_id, MAX(id) AS id\n", "        FROM lake_rpc.product_product\n", "        WHERE active = 1 AND approved = 1\n", "        GROUP BY item_id\n", "    ) b ON ma.item_id = b.item_id\n", "    JOIN lake_rpc.item_category_details icd ON icd.item_id = ma.item_id\n", ") AS subquery\n", "WHERE l2 NOT IN ('Other SKU', 'Other Perishables')\n", ")\n", "\n", ",base as (\n", "select w.* from zomato.hyperpure_etls.hp_cdc_wastage as w \n", "where lower(w.warehouse_code) like '%%cpc%%'\n", "--w.parent_category in ('MS Frozen','MS Ice Creams','MS Fruits and Vegetables','MS Dairy','MS Chicken & Eggs','MS Bakery')\n", "and w.warehouse_code not like '%%TEST%%'\n", ")\n", "\n", ", final as (\n", "select k.dt as date_,\n", "extract(week from dt) as week, \n", "extract(month from dt) as month, \n", "ipom.outlet_id as warehouse_id, \n", "warehouse_code,\n", "icd.l2,\n", "Case when icd.l2 not in ('Ice Cream','Frozen','FnV') then 'All Perishables' \n", "when icd.l2 not in ('FnV',\n", "'Batter',\n", "'Breads',\n", "'Curd',\n", "'Eggs',\n", "'Meats',\n", "'Milk',\n", "'Paneer',\n", "'Perishable Dairy',\n", "'Yogurt') then 'All Frozen' else 'FnV' end as flag,\n", "sum((-1*auto_expiry_rtv)+(-1*auto_expiry_non_rtv)) as expiry, \n", "sum(-1*shrinkage) as weight_loss, \n", "sum(-1*damage) as damage, \n", "sum((-1*wh_pilferage)+(-1*normal_bin_pilferage)) as pilferage\n", "from base as k           \n", "INNER JOIN po.edi_integration_partner_outlet_mapping ipom ON lower(ipom.partner_outlet_id) = lower(k.warehouse_code) AND ipom.active\n", "INNER JOIN po.edi_integration_partner_item_mapping pi ON CAST(pi.partner_item_id as int) = k.product_number\n", "INNER JOIN  perishable_skus_base icd on icd.item_id = pi.item_id\n", "group by 1,2,3,4,5,6,7\n", ")\n", "\n", "\n", "select\n", "date(date_trunc('day',date_)) as day,\n", "date(date_trunc('week',date_)) as week,\n", "date(date_trunc('month',date_)) as month,\n", "warehouse_id,\n", "warehouse_code,\n", "flag,\n", "l2,\n", "sum(expiry) as expiry,\n", "sum(weight_loss) as weight_loss,\n", "sum(damage) as damage,\n", "sum(pilferage) as pilferage,\n", "sum(expiry+weight_loss+damage+pilferage) total_wastage\n", "from final\n", "where date_ >= date(current_date - interval '60' day)\n", "group by 1,2,3,4,5,6,7\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "9c5a8692-2521-4409-9e08-0e452a912af2", "metadata": {}, "outputs": [], "source": ["data = pd.read_sql(q, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "7da25bed-b2e6-4c30-8ede-da433f16bb97", "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0468ed9c-28e2-4596-bf72-bdfe9f1d1ca5", "metadata": {}, "outputs": [], "source": ["data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "0da2fd51-5fc6-4579-b9fc-fe135ba87763", "metadata": {}, "outputs": [], "source": ["data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "38292715-3556-4c47-abb1-ab222d136b84", "metadata": {}, "outputs": [], "source": ["data[\"day\"] = pd.to_datetime(data[\"day\"]).dt.date\n", "data[\"day\"] = pd.to_datetime(data[\"day\"]).dt.strftime(\"%Y-%m-%d\")\n", "data[\"week\"] = pd.to_datetime(data[\"week\"]).dt.date\n", "data[\"week\"] = pd.to_datetime(data[\"week\"]).dt.strftime(\"%Y-%m-%d\")\n", "data[\"month\"] = pd.to_datetime(data[\"month\"]).dt.date\n", "data[\"month\"] = pd.to_datetime(data[\"month\"]).dt.strftime(\"%Y-%m-%d\")\n", "data[\"warehouse_id\"] = data[\"warehouse_id\"].astype(int)\n", "data[\"warehouse_code\"] = data[\"warehouse_code\"].astype(str)\n", "data[\"flag\"] = data[\"flag\"].astype(str)\n", "data[\"l2\"] = data[\"l2\"].astype(str)\n", "data[\"expiry\"] = data[\"expiry\"].astype(float)\n", "data[\"weight_loss\"] = data[\"weight_loss\"].astype(float)\n", "data[\"damage\"] = data[\"damage\"].astype(float)\n", "data[\"pilferage\"] = data[\"pilferage\"].astype(float)\n", "data[\"total_wastage\"] = data[\"total_wastage\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "71673853-8b9e-4eab-9878-0745be1c484e", "metadata": {}, "outputs": [], "source": ["data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "95a29b8a-1dd1-4e26-8def-52faea226fa6", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1Qqz3hH6wjJdYxPDbgmBQ3xzn9D42lapPsAKKsVnI9FU\"\n", "sheet_name = \"hp_backend\"\n", "pb.to_sheets(data, sheet_id, sheet_name)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}