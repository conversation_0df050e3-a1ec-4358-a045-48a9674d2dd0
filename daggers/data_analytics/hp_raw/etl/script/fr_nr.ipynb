{"cells": [{"cell_type": "code", "execution_count": null, "id": "c22b1fa5-b994-41a3-9862-0ba98e01141d", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "from datetime import date\n", "from datetime import timedelta\n", "from datetime import datetime\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\").connect()"]}, {"cell_type": "code", "execution_count": null, "id": "fc027a0a-e1bf-48a5-8a0e-fc374dce27f5", "metadata": {}, "outputs": [], "source": ["q = \"\"\"\n", "\n", "with perishable_skus_base as \n", "(\n", "select \n", "distinct \n", "ma.item_id, \n", "icd.name as item_name, \n", "ma.variant_id,\n", "icd.product_type,\n", "Case    WHEN l0_id in (1487) THEN 'FnV'\n", "        WHEN  (l2_id IN (1425) and ma.perishable = 1) THEN 'Batter'\n", "        WHEN  (l2_id IN (31,116,198,1097,1956) and ma.perishable = 1) THEN 'Breads'\n", "        WHEN  (l2_id IN (949)  and ma.perishable = 1) THEN 'Curd'\n", "        WHEN  (l2_id IN (1389,1778) and ma.perishable = 1) THEN 'Eggs'\n", "        WHEN  (l2_id IN (63,1367,1369)  and ma.perishable = 1) THEN 'Meats'\n", "        WHEN  (l2_id IN (1185) and ma.perishable = 1) THEN 'Milk'\n", "        WHEN  (l2_id IN (950) and ma.perishable = 1) THEN 'Paneer'\n", "        WHEN  (l2_id IN (138,1091,1093) and ma.perishable = 1) THEN 'Perishable Dairy'\n", "        WHEN  (l2_id IN (1094) and ma.perishable = 1) THEN 'Yogurt'\n", "        when (ma.storage_type in ('3','7') and l1_id not in (183)) then 'Frozen'\n", "        when l1_id in (183) then 'Ice Cream' \n", "        else 'Other SKU' end as l2\n", "        \n", "from  rpc.product_product ma \n", "JOIN (\n", "        SELECT item_id, MAX(id) AS id\n", "        FROM rpc.product_product\n", "        WHERE active = 1 and approved = 1\n", "        GROUP BY 1\n", "    ) b ON ma.item_id = b.item_id\n", "join rpc.item_category_details icd on icd.item_id = ma.item_id\n", "),\n", "            \n", "perishable_skus as (Select * from perishable_skus_base where l2 in ('FnV')),\n", "\n", "wh_mapping as (\n", "SELECT \n", "DISTINCT \n", "item_id, \n", "tm.outlet_id, \n", "tag_value AS be_outlet_id, \n", "cb.facility_id AS be_facility_id, \n", "cb.business_type_id AS be_type,\n", "cl.name as city\n", "\n", "FROM rpc.item_outlet_tag_mapping tm\n", "INNER JOIN po.physical_facility_outlet_mapping pfom on pfom.outlet_id = tm.outlet_id and pfom.active = 1 and pfom.is_primary = 1 and pfom.ars_active = 1\n", "JOIN retail.console_outlet co ON co.id = tm.outlet_id AND co.active=1 AND co.business_type_id = 7\n", "JOIN retail.console_outlet cb ON cb.id = cast(tag_value as int) AND cb.active=1\n", "left join retail.console_location cl on cl.id=cb.tax_location_id\n", "JOIN po.bulk_facility_outlet_mapping bfom on bfom.outlet_id = tm.outlet_id and bfom.active = true\n", "AND tm.active = 1 AND tm.tag_type_id = 8 \n", "and tm.item_id in (Select distinct item_id from perishable_skus)\n", "),\n", "\n", "o as\n", "    (\n", "    select co.id as out_id,\n", "    co.name as ds\n", "\n", "    from  retail.console_outlet  co\n", "    where  co.active = 1 and co.business_type_id in (7)\n", "    and co.company_type_id <> 4    \n", "    group by 1,2\n", "    ),\n", "\n", "\n", "dump as (\n", "    select \n", "        order_date date_ist,\n", "        outlet_id, \n", "        ds,\n", "        l2, \n", "        item_id, \n", "        be_outlet_id,\n", "        product_type,\n", "        city,\n", "        \n", "        sum(case when type_ = 'others' then tot_dump end) others, \n", "        sum(case when type_ = 'Good_Return_to_Vendor' then tot_dump end) Good_Return_to_Vendor, \n", "        sum(case when type_ = 'bad_stock_expired' then tot_dump end) bad_stock_expired,\n", "        sum(case when type_ = 'fwd_rejection_dump' then tot_dump end) fwd_rejection_dump,\n", "        sum(case when type_ = 'near_expiry_dump' then tot_dump end) near_expiry_dump,\n", "        sum(case when type_ = 'weight_short_dump' then tot_dump end) weight_short_dump,\n", "        sum(case when type_ = 'not_received_dump' then tot_dump end) not_received_dump,\n", "        sum(case when type_ not in ('customer_dump_damaged','customer_dump_expiry','customer_dump_near_expiry') then tot_dump end) tot_dump\n", "    from\n", "        (\n", "        select \n", "            order_date,\n", "            outlet_id,\n", "            ds,\n", "            item_id, \n", "            l2,\n", "            product_type,\n", "            city,\n", "            type_,\n", "            be_outlet_id,\n", "            \n", "            sum(quan*weighted_lp) tot_dump\n", "        from\n", "            (\n", "            SELECT \n", "                date(i.pos_timestamp + interval '330' minute) AS order_date,\n", "                i.outlet_id,\n", "                ds,\n", "                it1.item_id,\n", "                product_type,\n", "                i.weighted_lp, \n", "                l2,\n", "                i.\"delta\" quan,\n", "                be_outlet_id,\n", "                city,\n", "                ibur.id,\n", "                i.inventory_update_type_id,\n", "                CASE WHEN \n", "                    (ibur.id in (1,2) and i.inventory_update_type_id=11) \n", "                    then 'others'\n", "                WHEN \n", "                    ((ibur.id in (14)) and i.inventory_update_type_id=11)\n", "                    then 'Good_Return_to_Vendor'\n", "                WHEN \n", "                    ((ibur.id in (3,10)) or (ibur.id=5 and i.inventory_update_type_id=12) \n", "                    or (i.inventory_update_type_id=12 and ibur.name is null))  \n", "                    then 'bad_stock_expired'\n", "                WHEN \n", "                    ((ibur.id=4) or \n", "                    (ibur.id in (5,17) and i.inventory_update_type_id=11) or \n", "                    (ibur.id in (6,7,11))) \n", "                    then 'fwd_rejection_dump'\n", "                WHEN \n", "                    ibur.id in (8,9) \n", "                    or i.inventory_update_type_id=64 \n", "                    then 'near_expiry_dump'\n", "                WHEN \n", "                    ibur.id=12 \n", "                    then 'not_received_dump'\n", "                WHEN \n", "                    ibur.id=13 \n", "                    then 'weight_short_dump'\n", "                WHEN \n", "                    i.inventory_update_type_id in (7,33) \n", "                    then 'customer_dump_damaged'\n", "                WHEN \n", "                    i.inventory_update_type_id in (9,34) \n", "                    then 'customer_dump_expiry'\n", "                WHEN \n", "                    i.inventory_update_type_id in (63,67) \n", "                    then 'customer_dump_near_expiry' \n", "                    \n", "                ELSE\n", "                    'others2'\n", "                \n", "                end as type_\n", "            FROM \n", "                ims.ims_inventory_log i\n", "            INNER JOIN \n", "                o \n", "            on\n", "                i.outlet_id = o.out_id\n", "            INNER JOIN\n", "                perishable_skus it1\n", "            ON\n", "                i.variant_id = it1.variant_id\n", "                \n", "                AND i.inventory_update_type_id in (11,12,13,64,   -- store dump\n", "                                                    7,9,33,34,63,67) -- customer dump\n", "                AND i.pos_timestamp > current_date-interval '100' day\n", "            LEFT JOIN \n", "                ims.ims_inventory_update_type i2  \n", "            ON  \n", "                i.inventory_update_type_id = i2.id\n", "            LEFT JOIN \n", "                ims.ims_bad_inventory_update_log ibil \n", "            ON  \n", "                i.inventory_update_id=ibil.inventory_update_id\n", "            LEFT join  \n", "                ims.ims_bad_update_reason ibur on ibil.reason_id=ibur.id\n", "            LEFT JOIN\n", "              wh_mapping wh on wh.outlet_id = i.outlet_id and wh.item_id = it1.item_id\n", "\n", "            WHERE\n", "                i.insert_ds_ist > cast(current_date - interval '56' day as varchar)\n", "                and ds not like '%%CPC%%HOT%%'\n", "            \n", "            -- GROUP BY 1,2,3,4, 5, 6, 7, 8, 9 ,10, 11, 12--, 13, 14, 15, 16\n", "            )\n", "            GROUP BY 1,2,3,4,5,6,7,8,9--,13,14,15,16\n", "        )\n", "        GROUP By 1,2,3,4,5,6,7,8\n", "    )\n", "\n", "----------------------newly added\n", ", not_dts_tagging as (\n", "    select partner_outlet_id, warehouse_code,outlet_id,\n", "           case when warehouse_type='BLINK<PERSON>' and operation_mode='MANDI' then 'DTS'\n", "                when warehouse_type='BLINKIT' and (operation_mode='' or operation_mode is null) then 'CPC'\n", "                when warehouse_type='COLLECTION_CENTER' then 'CC' end wh_type\n", "    \n", "    from blinkit.lake_po.edi_integration_partner_outlet_mapping ipom --on cast(ipom.outlet_id as varchar)=ace.current_tenant_id \n", "    left join blinkit.lake_retail.console_outlet co on co.id=ipom.outlet_id\n", "    left join zomato.hp_wms.warehouse w on w.warehouse_code=partner_outlet_id\n", "    where try_cast(partner_outlet_id as bigint) is null\n", ")\n", "\n", "select \n", "    date_ist,\n", "    extract(week from date_ist) as week,\n", "        extract(month from date_ist) as month,\n", "    cast(coalesce(cast(cloud_store_id as varchar), be_outlet_id) as int) as be_outlet_id,\n", "    cb.name,\n", "    l2,\n", "    sum(fwd_rejection_dump) fwd_rejection_dump,\n", "    sum(not_received_dump) not_received_dump,\n", "    product_type,\n", "    city,\n", "    dd.wh_type as dts_tag\n", "\n", "from \n", "    dump d\n", "left join (select distinct warehouse_id, cloud_store_id from retail.warehouse_outlet_mapping \n", "                where active = 1 \n", "                        ) wom on wom.warehouse_id = cast(d.be_outlet_id as int)\n", "                        \n", "LEFT JOIN retail.console_outlet cb ON cb.id = cast(coalesce(cast(cloud_store_id as varchar), be_outlet_id) as int) AND cb.active = 1        \n", "left join not_dts_tagging as dd on dd.outlet_id = cast(coalesce(cast(cloud_store_id as varchar), be_outlet_id) as int)\n", "where (fwd_rejection_dump>0 or not_received_dump > 0) and cast(coalesce(cast(cloud_store_id as varchar), be_outlet_id) as int) not in (3229,2729,2672,2666,1674,3834)\n", "group by\n", "1, 2, 3,4,5,6,9,10,11\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "9c5a8692-2521-4409-9e08-0e452a912af2", "metadata": {}, "outputs": [], "source": ["data = pd.read_sql(q, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "7da25bed-b2e6-4c30-8ede-da433f16bb97", "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0468ed9c-28e2-4596-bf72-bdfe9f1d1ca5", "metadata": {}, "outputs": [], "source": ["data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "0da2fd51-5fc6-4579-b9fc-fe135ba87763", "metadata": {}, "outputs": [], "source": ["data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "95a29b8a-1dd1-4e26-8def-52faea226fa6", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1Qqz3hH6wjJdYxPDbgmBQ3xzn9D42lapPsAKKsVnI9FU\"\n", "sheet_name = \"hp_fr_nr\"\n", "pb.to_sheets(data, sheet_id, sheet_name)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}