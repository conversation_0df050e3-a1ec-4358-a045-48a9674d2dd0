{"cells": [{"cell_type": "code", "execution_count": null, "id": "c22b1fa5-b994-41a3-9862-0ba98e01141d", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "from datetime import date\n", "from datetime import timedelta\n", "from datetime import datetime\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\").connect()"]}, {"cell_type": "code", "execution_count": null, "id": "fc027a0a-e1bf-48a5-8a0e-fc374dce27f5", "metadata": {}, "outputs": [], "source": ["q = \"\"\"\n", "\n", "with bill as(\n", "select \n", "    invoice_billed_date_ist, sender_outlet_id, \n", "    receiver_outlet_id, bulk_sto_id, sto_id, \n", "    upper(invoice_id) invoice_id, item_id, \n", "    coalesce(sum(billed_qty),0) as billed_qty, avg(landing_price) as lp\n", "from dwh.flat_invoice_item_billed_details\n", "where invoice_billed_date_ist >= date_trunc('week', current_date) - interval '35' day\n", "\n", "group by 1,2,3,4,5,6,7\n", ")\n", "\n", ",outlets as (select outlet_id, outlet_name, outlet_type, city from ba_etls.outlet_details group by 1,2,3,4)\n", "\n", ",base2 as(\n", "select\n", "    invoice_billed_date_ist,\n", "    b.sender_outlet_id,\n", "    b.receiver_outlet_id,\n", "    o1.outlet_type as sender_outlet_type,\n", "    o2.outlet_type as receiver_outlet_type,\n", "    b.sto_id,\n", "    b.invoice_id,\n", "    b.item_id,\n", "    b.lp,\n", "    coalesce(sum(billed_qty),0) as billed_qty, \n", "    coalesce(sum(billed_qty*lp),0) as billed_amt\n", "\n", "from bill b\n", "LEFT JOIN po.sto ps ON cast(b.sto_id AS varchar) = cast(ps.id AS varchar) and ps.lake_active_record = true\n", "LEFT JOIN vms.vms_vendor v ON v.id = ps.destination_entity_vendor_id and v.lake_active_record = true\n", "left join outlets o1 on o1.outlet_id = b.sender_outlet_id\n", "left join outlets o2 on o2.outlet_id = b.receiver_outlet_id\n", "\n", "where v.vendor_name not like '%%BCPL%%'\n", "and CASE WHEN invoice_billed_date_ist BETWEEN date '2023-11-10' AND date '2023-12-30' THEN b.bulk_sto_id is not null else b.sender_outlet_id is not null END\n", "group by 1,2,3,4,5,6,7,8,9\n", ")\n", "\n", "select\n", "    date(invoice_billed_date_ist) as day,\n", "    sender_outlet_type,\n", "    receiver_outlet_type,\n", "    sum(billed_qty) as billed_qty,\n", "    sum(billed_amt) as billed_amt\n", "\n", "from base2 f\n", "\n", "where (sender_outlet_id is not null or receiver_outlet_id is not null)\n", "and ((sender_outlet_type = 'BE' and receiver_outlet_type = 'FE') or (sender_outlet_type = 'FE' and receiver_outlet_type = 'BE'))\n", "\n", "group by 1,2,3\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "9c5a8692-2521-4409-9e08-0e452a912af2", "metadata": {}, "outputs": [], "source": ["data = pd.read_sql(q, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "7da25bed-b2e6-4c30-8ede-da433f16bb97", "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0468ed9c-28e2-4596-bf72-bdfe9f1d1ca5", "metadata": {}, "outputs": [], "source": ["data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "0da2fd51-5fc6-4579-b9fc-fe135ba87763", "metadata": {}, "outputs": [], "source": ["data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "36a5cb8e-e272-4751-8840-0555fa319955", "metadata": {}, "outputs": [], "source": ["data[\"day\"] = pd.to_datetime(data[\"day\"]).dt.date\n", "data[\"day\"] = pd.to_datetime(data[\"day\"]).dt.strftime(\"%Y-%m-%d\")\n", "data[\"sender_outlet_type\"] = data[\"sender_outlet_type\"].astype(str)\n", "data[\"receiver_outlet_type\"] = data[\"receiver_outlet_type\"].astype(str)\n", "data[\"billed_qty\"] = data[\"billed_qty\"].astype(int)\n", "data[\"billed_amt\"] = data[\"billed_amt\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "6c8d7161-4ab2-4226-be50-1171209f686c", "metadata": {}, "outputs": [], "source": ["data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "95a29b8a-1dd1-4e26-8def-52faea226fa6", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1GoQqMSmxNUwPbBRaIetMgSW6PM2EAkcxRNtK_KyypvA\"\n", "sheet_name = \"daily_billed\"\n", "pb.to_sheets(data, sheet_id, sheet_name)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}