{"cells": [{"cell_type": "code", "execution_count": null, "id": "c22b1fa5-b994-41a3-9862-0ba98e01141d", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "from datetime import date\n", "from datetime import timedelta\n", "from datetime import datetime\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\").connect()"]}, {"cell_type": "code", "execution_count": null, "id": "fc027a0a-e1bf-48a5-8a0e-fc374dce27f5", "metadata": {}, "outputs": [], "source": ["q = \"\"\"\n", "\n", "with base as(\n", "select\n", "    day,\n", "    date(date_trunc('week', day)) as week,\n", "    facility_id,\n", "    outlet_id,\n", "    outlet_name,\n", "    outlet_city,\n", "    outlet_type,\n", "    i.item_id,\n", "    id.rl_flag,\n", "    category,\n", "    coalesce(short_weight_value,0) short_weight_value,\n", "    coalesce(fr_value,0) fr_value,\n", "    coalesce(inhouse_expiry_value,0) as inhouse_expiry_value,\n", "    coalesce(inhouse_nte_value,0) as inhouse_nte_value,\n", "    coalesce(inhouse_dmg_value,0) as inhouse_dmg_value,\n", "    coalesce(sto_dmg_value,0) as sto_dmg_value,\n", "    coalesce(b2b_dmg_value,0) as b2b_dmg_value,\n", "    coalesce(rsto_dmg_amt,0) as rsto_dmg_amt,\n", "    coalesce(rsto_exp_amt,0) as rsto_exp_amt,\n", "    coalesce(rsto_nte_amt,0) as rsto_nte_amt,\n", "    coalesce(b2b_dmg_expiry_value,0) as b2b_dmg_expiry_value,\n", "    coalesce(b2b_dmg_nte_value,0) as b2b_dmg_nte_value,\n", "    coalesce(breakage_ds_value,0) as breakage_ds_value,\n", "    coalesce(purchase_return_value,0) as purchase_return_value,\n", "    coalesce(secondary_sale_value,0) as secondary_sale_value,\n", "    coalesce(reinventorization_diff_value,0) as reinventorization_diff_value,\n", "    coalesce(pilferage_ds_value,0) as pilferage_ds_value,\n", "    coalesce(warehouse_variance_value,0) as warehouse_variance_value,\n", "    coalesce(nr_value,0) as nr_value,\n", "    coalesce(b2b_good_value,0) as b2b_good_value,\n", "    coalesce(total_positive_update_value,0) as total_positive_update_value,\n", "    coalesce(total_negative_update_value,0) as total_negative_update_value,\n", "    coalesce(net_dump_value,0) as net_dump_value,\n", "    coalesce(grv_value,0) as grv_value\n", "\n", "from ba_etls.inventory_dump_rough i\n", "left join (select item_id, rl_flag from ba_etls.item_details group by 1,2) id on i.item_id = id.item_id\n", "\n", "where day >= date_trunc('week', current_date) - interval '35' day\n", "and outlet_id not in (5054,4146)\n", ")\n", "\n", "select\n", "    date(day) as day,\n", "    week,\n", "    category,\n", "    sum(fr_value) as fr,\n", "    sum(nr_value) as nr,\n", "    sum(short_weight_value) as short_wt,\n", "    sum(sto_dmg_value + b2b_dmg_value) as sto_dmg_value,\n", "    sum(case when outlet_type = 'BE' then inhouse_dmg_value else 0 end) as fc_damage,\n", "    sum(inhouse_expiry_value + inhouse_nte_value + rsto_exp_amt + rsto_nte_amt + b2b_dmg_nte_value + b2b_dmg_expiry_value) as exp_nte,\n", "    sum(rsto_dmg_amt) as rsto_dmg,\n", "    sum(breakage_ds_value) as store_damage,\n", "    sum(reinventorization_diff_value) as ri,\n", "    sum(b2b_good_value) as b2b_good,\n", "    sum(pilferage_ds_value) as ds_variance,\n", "    sum(warehouse_variance_value) as wh_variance,\n", "    sum(total_positive_update_value) as total_positive_update_value,\n", "    sum(total_negative_update_value) as total_negative_update_value,\n", "    sum(net_dump_value) as net_dump_value,\n", "    sum(grv_value) as grv_value,\n", "    sum(case when rl_flag = 'Others' then purchase_return_value else 0 end) as dump_prn,\n", "    sum(case when rl_flag = 'Others' then secondary_sale_value else 0 end) as dump_ss,\n", "    sum(case when rl_flag <> 'Others' then purchase_return_value else 0 end) as rl_prn,\n", "    sum(case when rl_flag <> 'Others' then secondary_sale_value else 0 end) as rl_ss\n", "\n", "from base\n", "group by 1,2,3\n", "order by 1,2,3\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "9c5a8692-2521-4409-9e08-0e452a912af2", "metadata": {}, "outputs": [], "source": ["data = pd.read_sql(q, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "7da25bed-b2e6-4c30-8ede-da433f16bb97", "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0468ed9c-28e2-4596-bf72-bdfe9f1d1ca5", "metadata": {}, "outputs": [], "source": ["data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "0da2fd51-5fc6-4579-b9fc-fe135ba87763", "metadata": {}, "outputs": [], "source": ["data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "36a5cb8e-e272-4751-8840-0555fa319955", "metadata": {}, "outputs": [], "source": ["data[\"day\"] = pd.to_datetime(data[\"day\"]).dt.date\n", "data[\"day\"] = pd.to_datetime(data[\"day\"]).dt.strftime(\"%Y-%m-%d\")\n", "data[\"week\"] = pd.to_datetime(data[\"week\"]).dt.date\n", "data[\"week\"] = pd.to_datetime(data[\"week\"]).dt.strftime(\"%Y-%m-%d\")\n", "data[\"category\"] = data[\"category\"].astype(str)\n", "data[\"fr\"] = data[\"fr\"].astype(float)\n", "data[\"nr\"] = data[\"nr\"].astype(float)\n", "data[\"short_wt\"] = data[\"short_wt\"].astype(float)\n", "data[\"sto_dmg_value\"] = data[\"sto_dmg_value\"].astype(float)\n", "data[\"fc_damage\"] = data[\"fc_damage\"].astype(float)\n", "data[\"exp_nte\"] = data[\"exp_nte\"].astype(float)\n", "data[\"rsto_dmg\"] = data[\"rsto_dmg\"].astype(float)\n", "data[\"store_damage\"] = data[\"store_damage\"].astype(float)\n", "data[\"ri\"] = data[\"ri\"].astype(float)\n", "data[\"b2b_good\"] = data[\"b2b_good\"].astype(float)\n", "data[\"ds_variance\"] = data[\"ds_variance\"].astype(float)\n", "data[\"wh_variance\"] = data[\"wh_variance\"].astype(float)\n", "data[\"total_positive_update_value\"] = data[\"total_positive_update_value\"].astype(float)\n", "data[\"total_negative_update_value\"] = data[\"total_negative_update_value\"].astype(float)\n", "data[\"net_dump_value\"] = data[\"net_dump_value\"].astype(float)\n", "data[\"grv_value\"] = data[\"grv_value\"].astype(float)\n", "data[\"dump_prn\"] = data[\"dump_prn\"].astype(float)\n", "data[\"dump_ss\"] = data[\"dump_ss\"].astype(float)\n", "data[\"rl_prn\"] = data[\"rl_prn\"].astype(float)\n", "data[\"rl_ss\"] = data[\"rl_ss\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "6c8d7161-4ab2-4226-be50-1171209f686c", "metadata": {}, "outputs": [], "source": ["data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "95a29b8a-1dd1-4e26-8def-52faea226fa6", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1GoQqMSmxNUwPbBRaIetMgSW6PM2EAkcxRNtK_KyypvA\"\n", "sheet_name = \"daily_loss_headers_raw\"\n", "pb.to_sheets(data, sheet_id, sheet_name)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}