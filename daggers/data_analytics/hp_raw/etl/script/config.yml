alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 3
dag_name: script
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_analytics
notebooks:
- alias: daily_billed
  executor_config:
    load_type: tiny
    node_type: spot
  name: daily_billed
  parameters: null
  tag: group_01
- alias: daily_category_dump
  executor_config:
    load_type: tiny
    node_type: spot
  name: daily_category_dump
  parameters: null
  tag: group_01
- alias: daily_category_gmv_orders
  executor_config:
    load_type: tiny
    node_type: spot
  name: daily_category_gmv_orders
  parameters: null
  tag: group_01
- alias: daily_loss_headers
  executor_config:
    load_type: tiny
    node_type: spot
  name: daily_loss_headers
  parameters: null
  tag: group_01
- alias: fr_nr
  executor_config:
    load_type: tiny
    node_type: spot
  name: fr_nr
  parameters: null
  tag: group_01
- alias: hp_backend
  executor_config:
    load_type: tiny
    node_type: spot
  name: hp_backend
  parameters: null
  tag: group_01
owner:
  email: <EMAIL>
  slack_id: U075XSJ2KK2
path: data_analytics/hp_raw/etl/script
paused: false
pool: data_analytics_pool
project_name: hp_raw
schedule:
  end_date: '2025-09-15T00:00:00'
  interval: 30 2 * * *
  start_date: '2025-06-23T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 6
