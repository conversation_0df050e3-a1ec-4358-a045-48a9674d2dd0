alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: daily_revenue
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_analytics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U05FNAX2W5T
path: data_analytics/fin_category_pnl/etl/daily_revenue
paused: false
pool: data_analytics_pool
project_name: fin_category_pnl
schedule:
  end_date: '2025-08-28T00:00:00'
  interval: 30 4 * * *
  start_date: '2025-05-04T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 19
