{"cells": [{"cell_type": "code", "execution_count": null, "id": "ebdf6bf5-6955-4066-ad80-c8438c7dba1e", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "ba129fa7-cf41-4bf5-818e-184d20235b54", "metadata": {}, "outputs": [], "source": ["day_week_month = \"day\""]}, {"cell_type": "code", "execution_count": null, "id": "8f7d1269-faee-44f7-96a6-e141ed9cfc41", "metadata": {}, "outputs": [], "source": ["chunk_size = <PERSON><PERSON>ta(days=5)"]}, {"cell_type": "code", "execution_count": null, "id": "cf9608d7-fdcb-4bcc-b6dd-38483f3b5343", "metadata": {}, "outputs": [], "source": ["start_date = datetime(2025, 6, 2).date()\n", "end_date = datetime(2025, 6, 4).date()"]}, {"cell_type": "markdown", "id": "7c476d31-4eb4-4e36-96bf-4cb8ad1490a6", "metadata": {}, "source": ["### RSTO"]}, {"cell_type": "code", "execution_count": null, "id": "ec5c3d0e-8dd2-42a8-bc0b-d3af9e9b9343", "metadata": {"jupyter": {"source_hidden": true}, "tags": []}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"day\", \"type\": \"DATE\", \"description\": \"day\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"outlet_id\"},\n", "    {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"facility_id\"},\n", "    {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"item_id\"},\n", "    {\"name\": \"rsto_dmg_qty\", \"type\": \"INTEGER\", \"description\": \"rsto_dmg_qty\"},\n", "    {\"name\": \"rsto_nte_qty\", \"type\": \"INTEGER\", \"description\": \"rsto_nte_qty\"},\n", "    {\"name\": \"rsto_exp_qty\", \"type\": \"INTEGER\", \"description\": \"rsto_exp_qty\"},\n", "    {\"name\": \"rsto_dmg_amt\", \"type\": \"DOUBLE\", \"description\": \"rsto_dmg_amt\"},\n", "    {\"name\": \"rsto_nte_amt\", \"type\": \"DOUBLE\", \"description\": \"rsto_nte_amt\"},\n", "    {\"name\": \"rsto_exp_amt\", \"type\": \"DOUBLE\", \"description\": \"rsto_exp_amt\"},\n", "    {\"name\": \"rsto_dmg_cp\", \"type\": \"DOUBLE\", \"description\": \"rsto_dmg_cp\"},\n", "    {\"name\": \"rsto_nte_cp\", \"type\": \"DOUBLE\", \"description\": \"rsto_nte_cp\"},\n", "    {\"name\": \"rsto_exp_cp\", \"type\": \"DOUBLE\", \"description\": \"rsto_exp_cp\"},\n", "]\n", "table_description = \"Base table for inventory dump table and this table stores rsto dump data\"\n", "\n", "kwargs = {\n", "    \"schema_name\": \"ba_etls\",\n", "    \"table_name\": \"inv_dump_base_rsto_dump\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"day\", \"facility_id\", \"outlet_id\", \"item_id\"],\n", "    \"incremental_key\": \"\",\n", "    \"sortkey\": [],\n", "    \"table_description\": table_description,\n", "    \"partition_key\": [\"day\"],\n", "    #     \"distkey\": \"GroupName\",\n", "    \"load_type\": \"partition_overwrite\",  # append, rebuild, truncate or upsert\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "b66eccfa-5003-4468-a220-98c43c5e0fe5", "metadata": {"jupyter": {"source_hidden": true}, "tags": []}, "outputs": [], "source": ["while start_date <= end_date:\n", "    chunk_end_date = min(start_date + chunk_size - timedelta(days=1), end_date)\n", "    sql_query = f\"\"\"\n", "    with \n", "    tax as(\n", "    with base as(\n", "    select \n", "    p.item_id,\n", "    coalesce(igst,0) as igst_,\n", "    (COALESCE(cgst,0) + COALESCE(cess,0) + COALESCE(sgst,0)) as tax1,\n", "    (coalesce(igst,0) + COALESCE(cess,0)) as tax2,\n", "    coalesce(additional_cess_value,0) as additional_cess_value,\n", "    case when igst = 0 then (COALESCE(cgst,0) + COALESCE(cess,0) + COALESCE(sgst,0)) else (coalesce(igst,0) + COALESCE(cess,0)) end as product_tax,\n", "    p.updated_at,\n", "    row_number() over(partition by item_id order by p.updated_at desc) as rank_1\n", "\n", "    from lake_rpc.product_tax p \n", "    where p.active = 1 \n", "    -- and lake_active_record = true\n", "    group by 1,2,3,4,5,6,7\n", "    )\n", "\n", "    select\n", "        item_id,\n", "        additional_cess_value,\n", "        case when product_tax is null then 0 else product_tax end as product_tax\n", "\n", "    from base\n", "    where rank_1 = 1\n", "    group by 1,2,3\n", "    order by 1,2,3\n", "    )\n", "\n", "\n", "    select\n", "        cast(date_trunc('{day_week_month}', rsto_date_ist) as date) as {day_week_month},\n", "        t.receiver_outlet_id as outlet_id,\n", "        ro.facility_id,\n", "        i.item_id,\n", "        coalesce(sum(rsto_dump_dmg_qty),0) as rsto_dmg_qty,\n", "        coalesce(sum(rsto_dump_near_expiry_qty),0) as rsto_nte_qty,\n", "        coalesce(sum(rsto_dump_expired_qty),0) as rsto_exp_qty,\n", "\n", "        cast(coalesce(sum(rsto_dump_dmg_qty*t.lp),0) as double) as rsto_dmg_amt,\n", "        cast(coalesce(sum(rsto_dump_near_expiry_qty*t.lp),0) as double) as rsto_nte_amt,\n", "        cast(coalesce(sum(rsto_dump_expired_qty*t.lp),0) as double) as rsto_exp_amt,\n", "\n", "        cast(coalesce(sum(case when (product_tax = 0 and (t1.additional_cess_value) = 0) then (t.lp*i.rsto_dump_dmg_qty) \n", "                    when (product_tax = 0 and (t1.additional_cess_value) > 0) then (t.lp*i.rsto_dump_dmg_qty - (t1.additional_cess_value*i.rsto_dump_dmg_qty))\n", "                    when (product_tax > 0 and (t1.additional_cess_value) = 0) then ((t.lp*i.rsto_dump_dmg_qty) / (1+product_tax/100)) \n", "                    else ((t.lp*i.rsto_dump_dmg_qty - (t1.additional_cess_value*i.rsto_dump_dmg_qty)) / (1+product_tax/100))\n", "                end),0) as double) rsto_dmg_cp,\n", "        cast(coalesce(sum(case when (product_tax = 0 and (t1.additional_cess_value) = 0) then (t.lp*i.rsto_dump_near_expiry_qty) \n", "                    when (product_tax = 0 and (t1.additional_cess_value) > 0) then (t.lp*i.rsto_dump_near_expiry_qty - (t1.additional_cess_value*i.rsto_dump_near_expiry_qty))\n", "                    when (product_tax > 0 and (t1.additional_cess_value) = 0) then ((t.lp*i.rsto_dump_near_expiry_qty) / (1+product_tax/100)) \n", "                    else ((t.lp*i.rsto_dump_near_expiry_qty - (t1.additional_cess_value*i.rsto_dump_near_expiry_qty)) / (1+product_tax/100))\n", "                end),0) as double) as rsto_nte_cp,\n", "        cast(coalesce(sum(case when (product_tax = 0 and (t1.additional_cess_value) = 0) then (t.lp*i.rsto_dump_expired_qty) \n", "                    when (product_tax = 0 and (t1.additional_cess_value) > 0) then (t.lp*i.rsto_dump_expired_qty - (t1.additional_cess_value*i.rsto_dump_expired_qty))\n", "                    when (product_tax > 0 and (t1.additional_cess_value) = 0) then ((t.lp*i.rsto_dump_expired_qty) / (1+product_tax/100)) \n", "                    else ((t.lp*i.rsto_dump_expired_qty - (t1.additional_cess_value*i.rsto_dump_expired_qty)) / (1+product_tax/100))\n", "                end),0) as double) rsto_exp_cp\n", "\n", "    from dwh.flat_invoice_item_rsto_details i\n", "    left join (select receiver_outlet_id, invoice_id, item_id, avg(landing_price) lp\n", "                from dwh.flat_invoice_item_billed_details\n", "                where invoice_billed_date_ist <= date'{end_date}'\n", "                group by 1,2,3\n", "                ) t on t.invoice_id = i.invoice_id and t.item_id = i.item_id\n", "    left join (select id, facility_id from lake_retail.console_outlet) ro ON ro.id = t.receiver_outlet_id\n", "    left join tax t1 on t1.item_id = i.item_id\n", "\n", "    where rsto_date_ist >= cast('{start_date}' as timestamp)\n", "        and rsto_date_ist < cast('{end_date}' as timestamp) + interval '1' day\n", "        and (i.invoice_id is not null\n", "        or i.invoice_id <> ''\n", "        or i.invoice_id <> 'na'\n", "        or i.invoice_id <> 'NA')\n", "        and t.receiver_outlet_id not in (5193, 5124, 5195) -- KS Outlets\n", "\n", "    group by 1,2,3,4\n", "    \"\"\".format(\n", "        start_date=start_date, end_date=chunk_end_date, day_week_month=day_week_month\n", "    )\n", "    pb.to_trino(sql_query, **kwargs)\n", "    pb.send_slack_message(\n", "        \"U03S75C25B7\",\n", "        \"Dump part RSTO Table updated for \" + str(start_date) + \" - \" + str(chunk_end_date),\n", "    )\n", "    start_date += timedelta(days=chunk_size.days)"]}, {"cell_type": "code", "execution_count": null, "id": "e231ab92-4b77-4bf1-b293-43518a0065b2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}