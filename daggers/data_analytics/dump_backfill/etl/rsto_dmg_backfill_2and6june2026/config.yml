alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 3
dag_name: rsto_dmg_backfill_2and6june2026
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_analytics
notebooks:
- executor_config:
    load_type: tiny
    node_type: spot
  name: notebook_1
  parameters: null
  retries: 1
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: notebook_2
  parameters: null
  retries: 1
  tag: second
owner:
  email: <EMAIL>
  slack_id: U03S75C25B7
path: data_analytics/dump_backfill/etl/rsto_dmg_backfill_2and6june2026
paused: false
pool: data_analytics_pool
project_name: dump_backfill
schedule:
  end_date: '2025-09-07T00:00:00'
  interval: 30 2 9 6 *
  start_date: '2025-06-09T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 1
