{"cells": [{"cell_type": "code", "execution_count": null, "id": "ebdf6bf5-6955-4066-ad80-c8438c7dba1e", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "ce8611ca-468b-49a9-bcc4-38eef58d098a", "metadata": {}, "outputs": [], "source": ["day_week_month = \"day\"\n", "chunk_size = <PERSON><PERSON>ta(days=5)"]}, {"cell_type": "code", "execution_count": null, "id": "ec6860cd-baf3-4f90-b1e5-1dba393c499e", "metadata": {}, "outputs": [], "source": ["start_date = datetime(2025, 5, 24).date()\n", "end_date = datetime(2025, 6, 19).date()"]}, {"cell_type": "markdown", "id": "39e0c62b-5cf6-4ba5-a680-915543c85754", "metadata": {}, "source": ["### Final Inventory Dump Push"]}, {"cell_type": "code", "execution_count": null, "id": "69d51af8-a97b-46d4-9180-e5032127e7a9", "metadata": {"jupyter": {"source_hidden": true}, "tags": []}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"day\", \"type\": \"DATE\", \"description\": \"day\"},\n", "    {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"facility_id\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"outlet_id\"},\n", "    {\"name\": \"outlet_name\", \"type\": \"VARCHAR\", \"description\": \"outlet_name\"},\n", "    {\"name\": \"outlet_city\", \"type\": \"VARCHAR\", \"description\": \"outlet_city\"},\n", "    {\"name\": \"outlet_type\", \"type\": \"VARCHAR\", \"description\": \"outlet_type\"},\n", "    {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"item_id\"},\n", "    {\"name\": \"item_name\", \"type\": \"VARCHAR\", \"description\": \"item_name\"},\n", "    {\"name\": \"l0\", \"type\": \"VARCHAR\", \"description\": \"l0\"},\n", "    {\"name\": \"l1\", \"type\": \"VARCHAR\", \"description\": \"l1\"},\n", "    {\"name\": \"product_type\", \"type\": \"VARCHAR\", \"description\": \"product_type\"},\n", "    {\"name\": \"category\", \"type\": \"VARCHAR\", \"description\": \"category\"},\n", "    {\"name\": \"rtv_elg_flag\", \"type\": \"DOUBLE\", \"description\": \"rtv_elg_flag\"},\n", "    {\"name\": \"fr_qty\", \"type\": \"INTEGER\", \"description\": \"fr_qty\"},\n", "    {\"name\": \"fr_value\", \"type\": \"DOUBLE\", \"description\": \"fr_value\"},\n", "    {\"name\": \"fr_cp\", \"type\": \"DOUBLE\", \"description\": \"fr_cp\"},\n", "    {\"name\": \"nr_qty\", \"type\": \"INTEGER\", \"description\": \"nr_qty\"},\n", "    {\"name\": \"nr_value\", \"type\": \"DOUBLE\", \"description\": \"nr_value\"},\n", "    {\"name\": \"nr_cp\", \"type\": \"DOUBLE\", \"description\": \"nr_cp\"},\n", "    {\"name\": \"sto_dmg_qty\", \"type\": \"INTEGER\", \"description\": \"sto_dmg_qty\"},\n", "    {\"name\": \"sto_dmg_value\", \"type\": \"DOUBLE\", \"description\": \"sto_dmg_value\"},\n", "    {\"name\": \"sto_dmg_cp\", \"type\": \"DOUBLE\", \"description\": \"sto_dmg_cp\"},\n", "    {\"name\": \"grv_qty\", \"type\": \"INTEGER\", \"description\": \"grv_qty\"},\n", "    {\"name\": \"grv_value\", \"type\": \"DOUBLE\", \"description\": \"grv_value\"},\n", "    {\"name\": \"grv_cp\", \"type\": \"DOUBLE\", \"description\": \"grv_cp\"},\n", "    {\"name\": \"b2b_dmg_qty\", \"type\": \"INTEGER\", \"description\": \"b2b_dmg_qty\"},\n", "    {\"name\": \"b2b_dmg_value\", \"type\": \"DOUBLE\", \"description\": \"b2b_dmg_value\"},\n", "    {\"name\": \"b2b_dmg_cp\", \"type\": \"DOUBLE\", \"description\": \"b2b_dmg_cp\"},\n", "    {\n", "        \"name\": \"b2b_dmg_expiry_qty\",\n", "        \"type\": \"INTEGER\",\n", "        \"description\": \"b2b_dmg_expiry_qty\",\n", "    },\n", "    {\n", "        \"name\": \"b2b_dmg_expiry_value\",\n", "        \"type\": \"DOUBLE\",\n", "        \"description\": \"b2b_dmg_expiry_value\",\n", "    },\n", "    {\"name\": \"b2b_dmg_expiry_cp\", \"type\": \"DOUBLE\", \"description\": \"b2b_dmg_expiry_cp\"},\n", "    {\"name\": \"b2b_dmg_nte_qty\", \"type\": \"INTEGER\", \"description\": \"b2b_dmg_nte_qty\"},\n", "    {\"name\": \"b2b_dmg_nte_value\", \"type\": \"DOUBLE\", \"description\": \"b2b_dmg_nte_value\"},\n", "    {\"name\": \"b2b_dmg_nte_cp\", \"type\": \"DOUBLE\", \"description\": \"b2b_dmg_nte_cp\"},\n", "    {\"name\": \"inhouse_dmg_qty\", \"type\": \"INTEGER\", \"description\": \"inhouse_dmg_qty\"},\n", "    {\"name\": \"inhouse_dmg_value\", \"type\": \"DOUBLE\", \"description\": \"inhouse_dmg_value\"},\n", "    {\"name\": \"inhouse_dmg_cp\", \"type\": \"DOUBLE\", \"description\": \"inhouse_dmg_cp\"},\n", "    {\n", "        \"name\": \"inhouse_expiry_qty\",\n", "        \"type\": \"INTEGER\",\n", "        \"description\": \"inhouse_expiry_qty\",\n", "    },\n", "    {\n", "        \"name\": \"inhouse_expiry_value\",\n", "        \"type\": \"DOUBLE\",\n", "        \"description\": \"inhouse_expiry_value\",\n", "    },\n", "    {\"name\": \"inhouse_expiry_cp\", \"type\": \"DOUBLE\", \"description\": \"inhouse_expiry_cp\"},\n", "    {\"name\": \"inhouse_nte_qty\", \"type\": \"INTEGER\", \"description\": \"inhouse_nte_qty\"},\n", "    {\"name\": \"inhouse_nte_value\", \"type\": \"DOUBLE\", \"description\": \"inhouse_nte_value\"},\n", "    {\"name\": \"inhouse_nte_cp\", \"type\": \"DOUBLE\", \"description\": \"inhouse_nte_cp\"},\n", "    {\"name\": \"short_weight_qty\", \"type\": \"INTEGER\", \"description\": \"short_weight_qty\"},\n", "    {\n", "        \"name\": \"short_weight_value\",\n", "        \"type\": \"DOUBLE\",\n", "        \"description\": \"short_weight_value\",\n", "    },\n", "    {\"name\": \"short_weight_cp\", \"type\": \"DOUBLE\", \"description\": \"short_weight_cp\"},\n", "    {\n", "        \"name\": \"total_positive_update_qty\",\n", "        \"type\": \"INTEGER\",\n", "        \"description\": \"total_positive_update_qty\",\n", "    },\n", "    {\n", "        \"name\": \"total_positive_update_value\",\n", "        \"type\": \"DOUBLE\",\n", "        \"description\": \"total_positive_update_value\",\n", "    },\n", "    {\n", "        \"name\": \"total_positive_update_cp\",\n", "        \"type\": \"DOUBLE\",\n", "        \"description\": \"total_positive_update_cp\",\n", "    },\n", "    {\n", "        \"name\": \"total_negative_update_qty\",\n", "        \"type\": \"INTEGER\",\n", "        \"description\": \"total_negative_update_qty\",\n", "    },\n", "    {\n", "        \"name\": \"total_negative_update_value\",\n", "        \"type\": \"DOUBLE\",\n", "        \"description\": \"total_negative_update_value\",\n", "    },\n", "    {\n", "        \"name\": \"total_negative_update_cp\",\n", "        \"type\": \"DOUBLE\",\n", "        \"description\": \"total_negative_update_cp\",\n", "    },\n", "    {\n", "        \"name\": \"reinventorization_diff_qty\",\n", "        \"type\": \"INTEGER\",\n", "        \"description\": \"reinventorization_diff_qty\",\n", "    },\n", "    {\n", "        \"name\": \"reinventorization_diff_value\",\n", "        \"type\": \"DOUBLE\",\n", "        \"description\": \"reinventorization_diff_value\",\n", "    },\n", "    {\n", "        \"name\": \"reinventorization_diff_cp\",\n", "        \"type\": \"DOUBLE\",\n", "        \"description\": \"reinventorization_diff_cp\",\n", "    },\n", "    {\n", "        \"name\": \"secondary_sale_quantity\",\n", "        \"type\": \"INTEGER\",\n", "        \"description\": \"secondary_sale_quantity\",\n", "    },\n", "    {\n", "        \"name\": \"secondary_sale_value\",\n", "        \"type\": \"DOUBLE\",\n", "        \"description\": \"secondary_sale_value\",\n", "    },\n", "    {\"name\": \"secondary_sale_cp\", \"type\": \"DOUBLE\", \"description\": \"secondary_sale_cp\"},\n", "    {\n", "        \"name\": \"purchase_return_quantity\",\n", "        \"type\": \"INTEGER\",\n", "        \"description\": \"purchase_return_quantity\",\n", "    },\n", "    {\n", "        \"name\": \"purchase_return_value\",\n", "        \"type\": \"DOUBLE\",\n", "        \"description\": \"purchase_return_value\",\n", "    },\n", "    {\n", "        \"name\": \"purchase_return_cp\",\n", "        \"type\": \"DOUBLE\",\n", "        \"description\": \"purchase_return_cp\",\n", "    },\n", "    {\n", "        \"name\": \"crwi_good_ret_quantity\",\n", "        \"type\": \"INTEGER\",\n", "        \"description\": \"crwi_good_ret_quantity\",\n", "    },\n", "    {\n", "        \"name\": \"crwi_good_ret_value\",\n", "        \"type\": \"DOUBLE\",\n", "        \"description\": \"crwi_good_ret_value\",\n", "    },\n", "    {\"name\": \"crwi_good_ret_cp\", \"type\": \"DOUBLE\", \"description\": \"crwi_good_ret_cp\"},\n", "    {\"name\": \"b2b_good_qty\", \"type\": \"INTEGER\", \"description\": \"b2b_good_qty\"},\n", "    {\"name\": \"b2b_good_value\", \"type\": \"DOUBLE\", \"description\": \"b2b_good_value\"},\n", "    {\"name\": \"b2b_good_cp\", \"type\": \"DOUBLE\", \"description\": \"b2b_good_cp\"},\n", "    {\"name\": \"gmv_qty\", \"type\": \"INTEGER\", \"description\": \"gmv_qty\"},\n", "    {\"name\": \"gmv_value\", \"type\": \"DOUBLE\", \"description\": \"gmv_value\"},\n", "    {\"name\": \"billed_qty\", \"type\": \"INTEGER\", \"description\": \"billed_qty\"},\n", "    {\"name\": \"billed_amt\", \"type\": \"DOUBLE\", \"description\": \"billed_amt\"},\n", "    {\"name\": \"ext_purchase_qty\", \"type\": \"INTEGER\", \"description\": \"ext_purchase_qty\"},\n", "    {\"name\": \"ext_purchase_lp\", \"type\": \"DOUBLE\", \"description\": \"ext_purchase_lp\"},\n", "    {\"name\": \"ext_purchase_cp\", \"type\": \"DOUBLE\", \"description\": \"ext_purchase_cp\"},\n", "    {\"name\": \"breakage_ds_qty\", \"type\": \"INTEGER\", \"description\": \"breakage_ds_qty\"},\n", "    {\"name\": \"breakage_ds_value\", \"type\": \"DOUBLE\", \"description\": \"breakage_ds_value\"},\n", "    {\"name\": \"breakage_ds_cp\", \"type\": \"DOUBLE\", \"description\": \"breakage_ds_cp\"},\n", "    {\"name\": \"pilferage_ds_qty\", \"type\": \"INTEGER\", \"description\": \"pilferage_ds_qty\"},\n", "    {\n", "        \"name\": \"pilferage_ds_value\",\n", "        \"type\": \"DOUBLE\",\n", "        \"description\": \"pilferage_ds_value\",\n", "    },\n", "    {\"name\": \"pilferage_ds_cp\", \"type\": \"DOUBLE\", \"description\": \"pilferage_ds_cp\"},\n", "    {\n", "        \"name\": \"warehouse_variance_qty\",\n", "        \"type\": \"INTEGER\",\n", "        \"description\": \"warehouse_variance_qty\",\n", "    },\n", "    {\n", "        \"name\": \"warehouse_variance_value\",\n", "        \"type\": \"DOUBLE\",\n", "        \"description\": \"warehouse_variance_value\",\n", "    },\n", "    {\n", "        \"name\": \"warehouse_variance_cp\",\n", "        \"type\": \"DOUBLE\",\n", "        \"description\": \"warehouse_variance_cp\",\n", "    },\n", "    {\"name\": \"total_losses_qty\", \"type\": \"INTEGER\", \"description\": \"total_losses_qty\"},\n", "    {\n", "        \"name\": \"total_losses_value\",\n", "        \"type\": \"DOUBLE\",\n", "        \"description\": \"total_losses_value\",\n", "    },\n", "    {\"name\": \"total_losses_cp\", \"type\": \"DOUBLE\", \"description\": \"total_losses_cp\"},\n", "    {\n", "        \"name\": \"pilferage_breakage_qty\",\n", "        \"type\": \"INTEGER\",\n", "        \"description\": \"pilferage_breakage_qty\",\n", "    },\n", "    {\n", "        \"name\": \"pilferage_breakage_value\",\n", "        \"type\": \"DOUBLE\",\n", "        \"description\": \"pilferage_breakage_value\",\n", "    },\n", "    {\n", "        \"name\": \"pilferage_breakage_cp\",\n", "        \"type\": \"DOUBLE\",\n", "        \"description\": \"pilferage_breakage_cp\",\n", "    },\n", "    {\"name\": \"rsto_dmg_qty\", \"type\": \"INTEGER\", \"description\": \"rsto_dmg_qty\"},\n", "    {\"name\": \"rsto_dmg_amt\", \"type\": \"DOUBLE\", \"description\": \"rsto_dmg_amt\"},\n", "    {\"name\": \"rsto_dmg_cp\", \"type\": \"DOUBLE\", \"description\": \"rsto_dmg_cp\"},\n", "    {\"name\": \"rsto_nte_qty\", \"type\": \"INTEGER\", \"description\": \"rsto_nte_qty\"},\n", "    {\"name\": \"rsto_nte_amt\", \"type\": \"DOUBLE\", \"description\": \"rsto_nte_amt\"},\n", "    {\"name\": \"rsto_nte_cp\", \"type\": \"DOUBLE\", \"description\": \"rsto_nte_cp\"},\n", "    {\"name\": \"rsto_exp_qty\", \"type\": \"INTEGER\", \"description\": \"rsto_exp_qty\"},\n", "    {\"name\": \"rsto_exp_amt\", \"type\": \"DOUBLE\", \"description\": \"rsto_exp_amt\"},\n", "    {\"name\": \"rsto_exp_cp\", \"type\": \"DOUBLE\", \"description\": \"rsto_exp_cp\"},\n", "    {\"name\": \"net_dump_qty\", \"type\": \"INTEGER\", \"description\": \"net_dump_qty\"},\n", "    {\"name\": \"net_dump_value\", \"type\": \"DOUBLE\", \"description\": \"net_dump_value\"},\n", "    {\"name\": \"net_dump_cp\", \"type\": \"DOUBLE\", \"description\": \"net_dump_cp\"},\n", "    {\"name\": \"gross_dump_qty\", \"type\": \"INTEGER\", \"description\": \"gross_dump_qty\"},\n", "    {\"name\": \"gross_dump_value\", \"type\": \"DOUBLE\", \"description\": \"gross_dump_value\"},\n", "    {\"name\": \"gross_dump_cp\", \"type\": \"DOUBLE\", \"description\": \"gross_dump_cp\"},\n", "    {\"name\": \"dmg_rtv_flag\", \"type\": \"DOUBLE\", \"description\": \"dmg_rtv_flag\"},\n", "    {\"name\": \"nte_rtv_flag\", \"type\": \"DOUBLE\", \"description\": \"nte_rtv_flag\"},\n", "    {\"name\": \"rl_flag\", \"type\": \"VARCHAR\", \"description\": \"rl_flag\"},\n", "    {\"name\": \"prn_for_dump_qty\", \"type\": \"INTEGER\", \"description\": \"prn_for_dump_qty\"},\n", "    {\"name\": \"prn_for_dump_value\", \"type\": \"DOUBLE\", \"description\": \"prn_for_dump_value\"},\n", "    {\"name\": \"prn_for_dump_cp\", \"type\": \"DOUBLE\", \"description\": \"prn_for_dump_cp\"},\n", "    {\"name\": \"ss_for_dump_qty\", \"type\": \"INTEGER\", \"description\": \"ss_for_dump_qty\"},\n", "    {\"name\": \"ss_for_dump_value\", \"type\": \"DOUBLE\", \"description\": \"ss_for_dump_value\"},\n", "    {\"name\": \"ss_for_dump_cp\", \"type\": \"DOUBLE\", \"description\": \"ss_for_dump_cp\"},\n", "    {\"name\": \"prn_for_rl_qty\", \"type\": \"INTEGER\", \"description\": \"prn_for_rl_qty\"},\n", "    {\"name\": \"prn_for_rl_value\", \"type\": \"DOUBLE\", \"description\": \"prn_for_rl_value\"},\n", "    {\"name\": \"prn_for_rl_cp\", \"type\": \"DOUBLE\", \"description\": \"prn_for_rl_cp\"},\n", "    {\"name\": \"ss_for_rl_qty\", \"type\": \"INTEGER\", \"description\": \"ss_for_rl_qty\"},\n", "    {\"name\": \"ss_for_rl_value\", \"type\": \"DOUBLE\", \"description\": \"ss_for_rl_value\"},\n", "    {\"name\": \"ss_for_rl_cp\", \"type\": \"DOUBLE\", \"description\": \"ss_for_rl_cp\"},\n", "]\n", "table_description = \"Contains various headers of inventory dump\"\n", "\n", "kwargs = {\n", "    \"schema_name\": \"ba_etls\",\n", "    \"table_name\": \"inventory_dump_rough\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"day\", \"outlet_id\", \"item_id\"],\n", "    \"incremental_key\": \"\",\n", "    \"sortkey\": [],\n", "    \"table_description\": table_description,\n", "    \"partition_key\": [\"day\"],\n", "    #     \"distkey\": \"GroupName\",\n", "    \"load_type\": \"partition_overwrite\",  # append, rebuild, truncate or upsert\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "65b1dfbf-1319-4706-97d4-48d57d53b2c5", "metadata": {"jupyter": {"source_hidden": true}, "tags": []}, "outputs": [], "source": ["while start_date <= end_date:\n", "    chunk_end_date = min(start_date + chunk_size - timedelta(days=1), end_date)\n", "\n", "    sql_query = f\"\"\"\n", "    with rtv_elg as\n", "-- (select facility_id, item_id, 1 as rtv_elg_flag from warehouse_etls.rtv_elg_inputs group by 1,2,3)\n", "(with tagg as (\n", "with manf_details as (\n", "    SELECT \n", "        pp.item_id,\n", "        pp.name as item_name,\n", "        pp.brand_id,\n", "        pm.id as manufacturer_id,\n", "        pm.name as manufacturer,\n", "        b.name as brand,\n", "        pp.shelf_life\n", "    FROM rpc.product_product pp \n", "        INNER JOIN (SELECT item_id, max(id) as max_id\n", "                    FROM rpc.product_product \n", "                    WHERE approved=1 and active = 1 and lake_active_record = true\n", "                    GROUP BY 1) a ON a.item_id = pp.item_id AND a.max_id = pp.id\n", "        INNER JOIN rpc.product_brand b on b.id = pp.brand_id and b.active = 1 AND b.lake_active_record = true\n", "        INNER JOIN rpc.product_manufacturer pm on pm.id = b.manufacturer_id and pm.active = 1 AND pm.lake_active_record = true\n", "\n", "    GROUP BY 1,2,3,4,5,6,7\n", "    )\n", "    \n", "select\n", "    coalesce(m.item_id,mm.item_id,mmm.item_id) as item_id,\n", "    0 as flag,\n", "    r.return_type,\n", "    max(case when rr.bucket_name=1 then 1 else 0 end) as good,\n", "    max(case when rr.bucket_name=2 then 1 else 0 end) as damage,\n", "    max(case when rr.bucket_name=3 then 1 else 0 end) as NTE\n", "\n", "from rpc.return_policy r\n", "inner join rpc.return_policy_bucket_attribute rr on r.id=rr.policy_id\n", "left join manf_details m on m.manufacturer_id=r.reference_id and r.scope_type=3\n", "left join manf_details mm on mm.brand_id=r.reference_id and r.scope_type=2\n", "left join manf_details mmm on mmm.item_id=r.reference_id\n", "\n", "where \n", "    r.active = true\n", "    and rr.active = true\n", "    and r.scope_type = 1\n", "group by 1,2,3\n", "\n", "UNION \n", "\n", "select\n", "    coalesce(m.item_id,mm.item_id,mmm.item_id) as item_id,\n", "    1 as flag,\n", "    r.return_type,\n", "    max(case when rr.bucket_name=1 then 1 else 0 end) as good,\n", "    max(case when rr.bucket_name=2 and r.reference_id not in \n", "    (318,326,514,516,533,628,774,807,1101,1418,1697,1712,1772,1798,1834,1847,1918,1944,1959,1973,2068,2197,2225,2310,2552,2790,2798,2809,2823,2862,2872,2879,2881,2891,2936,2937,2958,2959,2971,2972,3007,3024,3168,3169,3178,3199,3268,3269,3279,3310,3344,3350,3356,3407,3408,3443,3460,3462,3489,3532,3565,3589,3750,3786,3806,3821,3887,3981,4349,4359,4414,4446,4522,4556,4573,4620,4649)\n", "    then 1 else 0 end) as damage,\n", "    max(case when rr.bucket_name=3 then 1 else 0 end) as NTE\n", "\n", "from rpc.return_policy r\n", "inner join rpc.return_policy_bucket_attribute rr on r.id=rr.policy_id\n", "left join manf_details m on m.manufacturer_id=r.reference_id and r.scope_type=3\n", "left join manf_details mm on mm.brand_id=r.reference_id and r.scope_type=2\n", "left join manf_details mmm on mmm.item_id=r.reference_id\n", "\n", "where \n", "    r.active = true\n", "    and r.scope_type = 3\n", "    and rr.active = true\n", "group by 1,2,3\n", ")\n", "\n", "select tagg.item_id, 0 as rtv_elg_flag, tagg.damage as dmg_rtv_flag, tagg.nte as nte_rtv_flag, return_type\n", "from tagg\n", "join (select item_id, min(flag) as flag from tagg group by 1) f on tagg.item_id = f.item_id and tagg.flag = f.flag\n", "group by 1,2,3,4,5\n", ")\n", "\n", "            ,base as(\n", "            select * from\n", "            (select day, facility_id, outlet_id, item_id from ba_etls.inv_dump_base_dump where day between date'{start_date}' and date'{end_date}'\n", "            union all\n", "            select day, facility_id, outlet_id, item_id from ba_etls.inv_dump_base_manual_update where day between date'{start_date}' and date'{end_date}'\n", "            union all\n", "            select day, facility_id, outlet_id, item_id from ba_etls.inv_dump_base_reinv_v2 where day between date'{start_date}' and date'{end_date}'\n", "            union all\n", "            select day, facility_id, outlet_id, item_id from ba_etls.inv_dump_base_prn_sec_sales where day between date'{start_date}' and date'{end_date}'\n", "            union all\n", "            select day, facility_id, outlet_id, item_id from ba_etls.inv_dump_base_crwi_v2 where day between date'{start_date}' and date'{end_date}'\n", "            union all\n", "            select day, facility_id, outlet_id, item_id from ba_etls.inv_dump_base_b2b_good_v2 where day between date'{start_date}' and date'{end_date}'\n", "            union all\n", "            select day, facility_id, outlet_id, item_id from ba_etls.inv_dump_base_purchase where day between date'{start_date}' and date'{end_date}'\n", "            union all\n", "            select day, facility_id, outlet_id, item_id from ba_etls.inv_dump_base_rsto_dump where day between date'{start_date}' and date'{end_date}'\n", "            )\n", "            group by 1,2,3,4\n", "            order by 1,2,3,4\n", "            )\n", "\n", "            ,base2 as(\n", "            select \n", "                cast(b.day as date) as day,\n", "                b.facility_id,\n", "                b.outlet_id,\n", "                outlet_name,\n", "                o.city as outlet_city,\n", "                outlet_type,\n", "                b.item_id,\n", "                item_name,\n", "                l0,\n", "                l1,\n", "                l2_id,\n", "                product_type,\n", "                category,\n", "                rtv_elg_flag,\n", "\n", "                coalesce(instore_dmg_fr_fnv_value, 0.0) as instore_dmg_fr_fnv_value,\n", "                coalesce(instore_dmg_fr_fnv_cp, 0.0) as instore_dmg_fr_fnv_cp,\n", "                coalesce(instore_dmg_fr_fnv_qty, 0) as instore_dmg_fr_fnv_qty,\n", "\n", "                coalesce(instore_dmg_short_weight_fnv_value, 0.0) as short_weight_value,\n", "                coalesce(instore_dmg_short_weight_fnv_cp, 0.0) as short_weight_cp,\n", "                coalesce(instore_dmg_short_weight_fnv_qty, 0) as short_weight_qty,\n", "\n", "                coalesce(instore_dmg_fr_perishable_value, 0.0) as instore_dmg_fr_perishable_value,\n", "                coalesce(instore_dmg_fr_perishable_cp, 0.0) as instore_dmg_fr_perishable_cp,\n", "                coalesce(instore_dmg_fr_perishable_qty, 0) as instore_dmg_fr_perishable_qty,\n", "\n", "                coalesce(instore_dmg_fr_packaged_value, 0.0) as instore_dmg_fr_packaged_value,\n", "                coalesce(instore_dmg_fr_packaged_cp, 0.0) as instore_dmg_fr_packaged_cp,\n", "                coalesce(instore_dmg_fr_packaged_qty, 0) as instore_dmg_fr_packaged_qty,\n", "\n", "                coalesce(instore_dmg_nr_fnv_value, 0.0) as instore_dmg_nr_fnv_value,\n", "                coalesce(instore_dmg_nr_fnv_cp, 0.0) as instore_dmg_nr_fnv_cp,\n", "                coalesce(instore_dmg_nr_fnv_qty, 0) as instore_dmg_nr_fnv_qty,\n", "\n", "                coalesce(instore_dmg_nr_perishable_value, 0.0) as instore_dmg_nr_perishable_value,\n", "                coalesce(instore_dmg_nr_perishable_cp, 0.0) as instore_dmg_nr_perishable_cp,\n", "                coalesce(instore_dmg_nr_perishable_qty, 0) as instore_dmg_nr_perishable_qty,\n", "\n", "                coalesce(instore_dmg_nr_packaged_value, 0.0) as instore_dmg_nr_packaged_value,\n", "                coalesce(instore_dmg_nr_packaged_cp, 0.0) as instore_dmg_nr_packaged_cp,\n", "                coalesce(instore_dmg_nr_packaged_qty, 0) as instore_dmg_nr_packaged_qty,\n", "\n", "                coalesce(instore_dmg_fnv_sto_value, 0.0) as instore_dmg_fnv_sto_value,\n", "                coalesce(instore_dmg_fnv_sto_cp, 0.0) as instore_dmg_fnv_sto_cp,\n", "                coalesce(instore_dmg_fnv_sto_qty, 0) as instore_dmg_fnv_sto_qty,\n", "\n", "                coalesce(instore_dmg_perishable_sto_value, 0.0) as instore_dmg_perishable_sto_value,\n", "                coalesce(instore_dmg_perishable_sto_cp, 0.0) as instore_dmg_perishable_sto_cp,\n", "                coalesce(instore_dmg_perishable_sto_qty, 0) as instore_dmg_perishable_sto_qty,\n", "\n", "                coalesce(instore_dmg_packaged_sto_value, 0.0) as instore_dmg_packaged_sto_value,\n", "                coalesce(instore_dmg_packaged_sto_cp, 0.0) as instore_dmg_packaged_sto_cp,\n", "                coalesce(instore_dmg_packaged_sto_qty, 0) as instore_dmg_packaged_sto_qty,    \n", "\n", "                coalesce(instore_grv_fnv_value, 0.0) as instore_grv_fnv_value,\n", "                coalesce(instore_grv_fnv_cp, 0.0) as instore_grv_fnv_cp,\n", "                coalesce(instore_grv_fnv_qty, 0) as instore_grv_fnv_qty,\n", "\n", "                coalesce(instore_grv_packaged_value, 0.0) as instore_grv_packaged_value,\n", "                coalesce(instore_grv_packaged_cp, 0.0) as instore_grv_packaged_cp,\n", "                coalesce(instore_grv_packaged_qty, 0) as instore_grv_packaged_qty,\n", "\n", "                coalesce(instore_grv_perishable_value, 0.0) as instore_grv_perishable_value,\n", "                coalesce(instore_grv_perishable_cp, 0.0) as instore_grv_perishable_cp,\n", "                coalesce(instore_grv_perishable_qty, 0) as instore_grv_perishable_qty,\n", "\n", "                coalesce(instore_dmg_fnv_other_value, 0.0) as instore_dmg_fnv_other_value,\n", "                coalesce(instore_dmg_fnv_other_cp, 0.0) as instore_dmg_fnv_other_cp,\n", "                coalesce(instore_dmg_fnv_other_qty, 0) as instore_dmg_fnv_other_qty,\n", "\n", "                coalesce(instore_dmg_perishable_other_value, 0.0) as instore_dmg_perishable_other_value,\n", "                coalesce(instore_dmg_perishable_other_cp, 0.0) as instore_dmg_perishable_other_cp, \n", "                coalesce(instore_dmg_perishable_other_qty, 0) as instore_dmg_perishable_other_qty, \n", "\n", "                coalesce(instore_dmg_packaged_other_value, 0.0) as instore_dmg_packaged_other_value,\n", "                coalesce(instore_dmg_packaged_other_cp, 0.0) as instore_dmg_packaged_other_cp,\n", "                coalesce(instore_dmg_packaged_other_qty, 0) as instore_dmg_packaged_other_qty,\n", "\n", "                0.0 as b2b_dmg_fnv_value,\n", "                0.0 as b2b_dmg_fnv_cp,\n", "                0 as b2b_dmg_fnv_qty,\n", "\n", "                coalesce(b2b_dmg_perishable_value, 0.0) as b2b_dmg_perishable_value,\n", "                coalesce(b2b_dmg_perishable_cp, 0.0) as b2b_dmg_perishable_cp,\n", "                coalesce(b2b_dmg_perishable_qty, 0) as b2b_dmg_perishable_qty,\n", "\n", "                coalesce(b2b_dmg_packaged_value, 0.0) as b2b_dmg_packaged_value,\n", "                coalesce(b2b_dmg_packaged_cp, 0.0) as b2b_dmg_packaged_cp,\n", "                coalesce(b2b_dmg_packaged_qty, 0) as b2b_dmg_packaged_qty,\n", "\n", "                coalesce(instore_dmg_expiry_fnv_value, 0.0) as instore_dmg_expiry_fnv_value,\n", "                coalesce(instore_dmg_expiry_fnv_cp, 0.0) as instore_dmg_expiry_fnv_cp,\n", "                coalesce(instore_dmg_expiry_fnv_qty, 0) as instore_dmg_expiry_fnv_qty,\n", "\n", "                coalesce(instore_dmg_expiry_perishable_value, 0.0) as instore_dmg_expiry_perishable_value,\n", "                coalesce(instore_dmg_expiry_perishable_cp, 0.0) as instore_dmg_expiry_perishable_cp,\n", "                coalesce(instore_dmg_expiry_perishable_qty, 0) as instore_dmg_expiry_perishable_qty,\n", "\n", "                coalesce(instore_dmg_expiry_packaged_value, 0.0) as instore_dmg_expiry_packaged_value,\n", "                coalesce(instore_dmg_expiry_packaged_cp, 0.0) as instore_dmg_expiry_packaged_cp,\n", "                coalesce(instore_dmg_expiry_packaged_qty, 0) as instore_dmg_expiry_packaged_qty,\n", "\n", "                coalesce(b2b_dmg_expiry_fnv_value, 0.0) as b2b_dmg_expiry_fnv_value,\n", "                coalesce(b2b_dmg_expiry_fnv_cp, 0.0) as b2b_dmg_expiry_fnv_cp,\n", "                coalesce(b2b_dmg_expiry_fnv_qty, 0) as b2b_dmg_expiry_fnv_qty,\n", "\n", "                coalesce(b2b_dmg_expiry_perishable_value, 0.0) as b2b_dmg_expiry_perishable_value,\n", "                coalesce(b2b_dmg_expiry_perishable_cp, 0.0) as b2b_dmg_expiry_perishable_cp,\n", "                coalesce(b2b_dmg_expiry_perishable_qty, 0) as b2b_dmg_expiry_perishable_qty,\n", "\n", "                coalesce(b2b_dmg_expiry_packaged_value, 0.0) as b2b_dmg_expiry_packaged_value,\n", "                coalesce(b2b_dmg_expiry_packaged_cp, 0.0) as b2b_dmg_expiry_packaged_cp,\n", "                coalesce(b2b_dmg_expiry_packaged_qty, 0) as b2b_dmg_expiry_packaged_qty,\n", "\n", "                coalesce(instore_dmg_nte_fnv_value, 0.0) as instore_dmg_nte_fnv_value,\n", "                coalesce(instore_dmg_nte_fnv_cp, 0.0) as instore_dmg_nte_fnv_cp,\n", "                coalesce(instore_dmg_nte_fnv_qty, 0) as instore_dmg_nte_fnv_qty,\n", "\n", "                coalesce(instore_dmg_nte_perishable_value, 0.0) as instore_dmg_nte_perishable_value,\n", "                coalesce(instore_dmg_nte_perishable_cp, 0.0) as instore_dmg_nte_perishable_cp,\n", "                coalesce(instore_dmg_nte_perishable_qty, 0) as instore_dmg_nte_perishable_qty,\n", "\n", "                coalesce(instore_dmg_nte_packaged_value, 0.0) as instore_dmg_nte_packaged_value,\n", "                coalesce(instore_dmg_nte_packaged_cp, 0.0) as instore_dmg_nte_packaged_cp,\n", "                coalesce(instore_dmg_nte_packaged_qty, 0) as instore_dmg_nte_packaged_qty,\n", "\n", "                coalesce(b2b_dmg_nte_fnv_value, 0.0) as b2b_dmg_nte_fnv_value,\n", "                coalesce(b2b_dmg_nte_fnv_cp, 0.0) as b2b_dmg_nte_fnv_cp,\n", "                coalesce(b2b_dmg_nte_fnv_qty, 0) as b2b_dmg_nte_fnv_qty,\n", "\n", "                coalesce(b2b_dmg_nte_perishable_value, 0.0) as b2b_dmg_nte_perishable_value,\n", "                coalesce(b2b_dmg_nte_perishable_cp, 0.0) as b2b_dmg_nte_perishable_cp,\n", "                coalesce(b2b_dmg_nte_perishable_qty, 0) as b2b_dmg_nte_perishable_qty,\n", "\n", "                coalesce(b2b_dmg_nte_packaged_value, 0.0) as b2b_dmg_nte_packaged_value,\n", "                coalesce(b2b_dmg_nte_packaged_cp, 0.0) as b2b_dmg_nte_packaged_cp,\n", "                coalesce(b2b_dmg_nte_packaged_qty, 0) as b2b_dmg_nte_packaged_qty,\n", "\n", "                coalesce(total_gross_dump_value, 0.0) as total_gross_dump_value,\n", "                coalesce(total_gross_dump_cp, 0.0) as total_gross_dump_cp,\n", "                coalesce(total_gross_dump_qty, 0) as total_gross_dump_qty,\n", "\n", "                coalesce(total_positive_update_value, 0.0) as total_positive_update_value,\n", "                coalesce(total_positive_update_cp, 0.0) as total_positive_update_cp,\n", "                coalesce(total_positive_update_qty, 0) as total_positive_update_qty,\n", "\n", "                coalesce(total_negative_update_value, 0.0) as total_negative_update_value,\n", "                coalesce(total_negative_update_cp, 0.0) as total_negative_update_cp,\n", "                coalesce(total_negative_update_qty, 0) as total_negative_update_qty,\n", "\n", "                coalesce((case when category in ('packaged', 'perishable') then reinventorization_diff_qty else 0 end), 0.0) as reinventorization_diff_qty,\n", "                coalesce((case when category in ('packaged', 'perishable') then reinventorization_diff_value else 0 end), 0.0) as reinventorization_diff_value,\n", "                coalesce((case when category in ('packaged', 'perishable') then reinventorization_diff_cp else 0 end), 0) as reinventorization_diff_cp,\n", "\n", "                coalesce(secondary_sale_value, 0.0) as secondary_sale_value,\n", "                coalesce(secondary_sale_cp, 0.0) as secondary_sale_cp,\n", "                coalesce(secondary_sale_quantity, 0) as secondary_sale_quantity,\n", "                coalesce(purchase_return_value, 0.0) as purchase_return_value,\n", "                coalesce(purchase_return_cp, 0.0) as purchase_return_cp,\n", "                coalesce(purchase_return_quantity, 0) as purchase_return_quantity,\n", "\n", "                coalesce(case when category in ('packaged', 'perishable') then crwi_good_ret_value else 0.0 end, 0.0) as crwi_good_ret_value,\n", "                coalesce(case when category in ('packaged', 'perishable') then crwi_good_ret_cp else 0.0 end, 0.0) as crwi_good_ret_cp,\n", "                coalesce(case when category in ('packaged', 'perishable') then crwi_good_ret_quantity else 0 end, 0) as crwi_good_ret_quantity,\n", "\n", "                coalesce(b2b_good_value, 0.0) as b2b_good_value,\n", "                coalesce(b2b_good_cp, 0.0) as b2b_good_cp,\n", "                coalesce(b2b_good_qty, 0) as b2b_good_qty,\n", "\n", "                coalesce(ext_purchase_lp, 0.0) as ext_purchase_lp,\n", "                coalesce(ext_purchase_cp, 0.0) as ext_purchase_cp,\n", "                coalesce(ext_purchase_qty, 0) as ext_purchase_qty,\n", "\n", "                coalesce(rsto_dmg_qty, 0) as rsto_dmg_qty,\n", "                coalesce(rsto_dmg_amt, 0.0) as rsto_dmg_amt,\n", "                coalesce(rsto_dmg_cp, 0.0) as rsto_dmg_cp,\n", "\n", "                coalesce(rsto_nte_qty, 0) as rsto_nte_qty,\n", "                coalesce(rsto_nte_amt, 0.0) as rsto_nte_amt,\n", "                coalesce(rsto_nte_cp, 0.0) as rsto_nte_cp,\n", "\n", "                coalesce(rsto_exp_qty, 0) as rsto_exp_qty,\n", "                coalesce(rsto_exp_amt, 0.0) as rsto_exp_amt,\n", "                coalesce(rsto_exp_cp, 0.0) as rsto_exp_cp,\n", "\n", "                coalesce(g.quantity,0) as gmv_qty,\n", "                coalesce(gmv_value, 0.0) as gmv_value,\n", "\n", "                coalesce(bl.qty, 0) as billed_qty,\n", "                coalesce(bl.amt, 0,0) as billed_amt,\n", "                \n", "                dmg_rtv_flag,\n", "                nte_rtv_flag,\n", "                rl_flag,\n", "                case when return_type = 2 and product_type in ('<PERSON>u<PERSON>', '3 Flower Bouquet', '<PERSON> Flower', 'Rose Bouquet') then 1 else 0 end as sor_bouquet_flag\n", "\n", "            from base b\n", "\n", "            left join ba_etls.inv_dump_base_dump d \n", "                on d.facility_id = b.facility_id and d.outlet_id = b.outlet_id and d.item_id = b.item_id and d.day = b.day and d.day between date'{start_date}' and date'{end_date}'\n", "\n", "            left join ba_etls.inv_dump_base_manual_update m \n", "                on b.facility_id = m.facility_id and b.outlet_id = m.outlet_id and b.item_id = m.item_id and b.day = m.day and m.day between date'{start_date}' and date'{end_date}'\n", "\n", "            left join ba_etls.inv_dump_base_reinv_v2 ri \n", "                on b.facility_id = ri.facility_id and b.outlet_id = ri.outlet_id and b.item_id = ri.item_id and b.day = ri.day and ri.day between date'{start_date}' and date'{end_date}'\n", "\n", "            left join ba_etls.inv_dump_base_prn_sec_sales prn \n", "                on b.facility_id = prn.facility_id and b.outlet_id = prn.outlet_id and b.item_id = prn.item_id and b.day = prn.day and prn.day between date'{start_date}' and date'{end_date}'\n", "\n", "            left join ba_etls.inv_dump_base_crwi_v2 c \n", "                on b.facility_id = c.facility_id and b.outlet_id = c.outlet_id and b.item_id = c.item_id and b.day = c.day and c.day between date'{start_date}' and date'{end_date}'\n", "\n", "            left join ba_etls.inv_dump_base_b2b_good_v2 b2b \n", "                on b.facility_id = b2b.facility_id and b.outlet_id = b2b.outlet_id and b.item_id = b2b.item_id and b.day = b2b.day and b2b.day between date'{start_date}' and date'{end_date}'\n", "\n", "            left join ba_etls.inv_dump_base_purchase p\n", "                on b.facility_id = p.facility_id and b.outlet_id = p.outlet_id and b.item_id = p.item_id and b.day = p.day and p.day between date'{start_date}' and date'{end_date}'\n", "\n", "            left join ba_etls.inv_dump_base_rsto_dump r\n", "                on b.facility_id = r.facility_id and b.outlet_id = r.outlet_id and b.item_id = r.item_id and b.day = r.day and r.day between date'{start_date}' and date'{end_date}'\n", "\n", "            left join ba_etls.inv_dump_base_gmv g \n", "                on b.outlet_id = g.outlet_id and b.item_id = g.item_id and b.day = g.day and g.day between date'{start_date}' and date'{end_date}'\n", "\n", "            left join ba_etls.inv_dump_base_sto_billed bl\n", "                on b.outlet_id = bl.outlet_id and b.item_id = bl.item_id and b.day = bl.day and bl.day between date'{start_date}' and date'{end_date}'\n", "\n", "            left join rtv_elg re on b.item_id = re.item_id\n", "\n", "            left join ba_etls.outlet_details o on o.outlet_id = b.outlet_id\n", "\n", "            left join (select \n", "                        i.item_id, min(i.name) as item_name, id.l0, id.l1, ptype as product_type, rl_flag, id.l2_id,\n", "                        coalesce(ct0.category_group, ct1.category_group, ct2.category_group, 'packaged') as category,\n", "                        max(coalesce(handling_type,'0')) as handling_type\n", "\n", "                        from rpc.product_product i\n", "                        left join rpc.item_category_details c on i.item_id = c.item_id and i.lake_active_record = true and c.lake_active_record = true\n", "                        left join ba_etls.pnl_all_category_tag_ ct0 on ct0.l0_category_id = cast(c.l0_id as varchar) \n", "                            and ct0.l1_category_id = '-1' and ct0.l2_category_id = '-1'\n", "                        left join ba_etls.pnl_all_category_tag_ ct1 on ct1.l1_category_id = cast(c.l1_id as varchar) \n", "                            and ct1.l2_category_id = '-1'\n", "                        left join ba_etls.pnl_all_category_tag_ ct2 on ct2.l2_category_id = cast(c.l2_id as varchar)\n", "                        left join (select item_id, l0, l1, ptype, rl_flag, l2_id from ba_etls.item_details group by 1,2,3,4,5,6) id on id.item_id = i.item_id\n", "                        where i.active = 1\n", "                        group by 1,3,4,5,6,7,8\n", "                        ) i on i.item_id = b.item_id\n", "\n", "            where b.day is not null\n", "                and lower(outlet_name) not like '%%scrap%%'\n", "                and category not like '%%print%%service%%'\n", "                and handling_type <> '8'\n", "            )\n", "\n", "            ,base3 as(\n", "            select\n", "                day, \n", "                facility_id,\n", "                outlet_id,\n", "                outlet_name,\n", "                outlet_city,\n", "                outlet_type,\n", "                item_id,\n", "                item_name,\n", "                l0,\n", "                l1,\n", "                l2_id,\n", "                product_type,\n", "                category,\n", "                rtv_elg_flag,\n", "                rl_flag,\n", "\n", "                (instore_dmg_fr_fnv_qty + instore_dmg_fr_packaged_qty + instore_dmg_fr_perishable_qty) as fr_qty,\n", "                (instore_dmg_fr_fnv_value + instore_dmg_fr_packaged_value + instore_dmg_fr_perishable_value) as fr_value,\n", "                (instore_dmg_fr_fnv_cp + instore_dmg_fr_packaged_cp + instore_dmg_fr_perishable_cp) as fr_cp,\n", "\n", "                (instore_dmg_nr_fnv_qty + instore_dmg_nr_packaged_qty + instore_dmg_nr_perishable_qty) as nr_qty,\n", "                (instore_dmg_nr_fnv_value + instore_dmg_nr_packaged_value + instore_dmg_nr_perishable_value) as nr_value,\n", "                (instore_dmg_nr_fnv_cp + instore_dmg_nr_packaged_cp + instore_dmg_nr_perishable_cp) as nr_cp,\n", "\n", "                (instore_dmg_fnv_sto_qty + instore_dmg_perishable_sto_qty + instore_dmg_packaged_sto_qty) as sto_dmg_qty,\n", "                (instore_dmg_fnv_sto_value + instore_dmg_perishable_sto_value + instore_dmg_packaged_sto_value) as sto_dmg_value,\n", "                (instore_dmg_fnv_sto_cp + instore_dmg_perishable_sto_cp + instore_dmg_packaged_sto_cp) as sto_dmg_cp,\n", "\n", "                (instore_grv_fnv_qty + instore_grv_packaged_qty + instore_grv_perishable_qty) as grv_qty,\n", "                (instore_grv_fnv_value + instore_grv_packaged_value + instore_grv_perishable_value) as grv_value,\n", "                (instore_grv_fnv_cp + instore_grv_packaged_cp + instore_grv_perishable_cp) as grv_cp,\n", "\n", "                (b2b_dmg_fnv_qty + b2b_dmg_perishable_qty + b2b_dmg_packaged_qty) as b2b_dmg_qty,\n", "                (b2b_dmg_fnv_value + b2b_dmg_perishable_value + b2b_dmg_packaged_value) as b2b_dmg_value,\n", "                (b2b_dmg_fnv_cp + b2b_dmg_perishable_cp + b2b_dmg_packaged_cp) as b2b_dmg_cp,\n", "\n", "                (b2b_dmg_expiry_fnv_qty + b2b_dmg_expiry_perishable_qty + b2b_dmg_expiry_packaged_qty) as b2b_dmg_expiry_qty,\n", "                (b2b_dmg_expiry_fnv_value + b2b_dmg_expiry_perishable_value + b2b_dmg_expiry_packaged_value) as b2b_dmg_expiry_value,\n", "                (b2b_dmg_expiry_fnv_cp + b2b_dmg_expiry_perishable_cp + b2b_dmg_expiry_packaged_cp) as b2b_dmg_expiry_cp,\n", "\n", "                (b2b_dmg_nte_fnv_qty + b2b_dmg_nte_perishable_qty + b2b_dmg_nte_packaged_qty) as b2b_dmg_nte_qty,\n", "                (b2b_dmg_nte_fnv_value + b2b_dmg_nte_perishable_value + b2b_dmg_nte_packaged_value) as b2b_dmg_nte_value,\n", "                (b2b_dmg_nte_fnv_cp + b2b_dmg_nte_perishable_cp + b2b_dmg_nte_packaged_cp) as b2b_dmg_nte_cp,\n", "\n", "                (instore_dmg_fnv_other_qty + instore_dmg_perishable_other_qty + instore_dmg_packaged_other_qty) as inhouse_dmg_qty,\n", "                (instore_dmg_fnv_other_value + instore_dmg_perishable_other_value + instore_dmg_packaged_other_value) as inhouse_dmg_value,\n", "                (instore_dmg_fnv_other_cp + instore_dmg_perishable_other_cp + instore_dmg_packaged_other_cp) as inhouse_dmg_cp,\n", "\n", "                (instore_dmg_expiry_fnv_qty + instore_dmg_expiry_perishable_qty + instore_dmg_expiry_packaged_qty) as inhouse_expiry_qty,\n", "                (instore_dmg_expiry_fnv_value + instore_dmg_expiry_perishable_value + instore_dmg_expiry_packaged_value) as inhouse_expiry_value,\n", "                (instore_dmg_expiry_fnv_cp + instore_dmg_expiry_perishable_cp + instore_dmg_expiry_packaged_cp) as inhouse_expiry_cp,\n", "\n", "                (instore_dmg_nte_fnv_qty + instore_dmg_nte_perishable_qty + instore_dmg_nte_packaged_qty) as inhouse_nte_qty,\n", "                (instore_dmg_nte_fnv_value + instore_dmg_nte_perishable_value + instore_dmg_nte_packaged_value) as inhouse_nte_value,\n", "                (instore_dmg_nte_fnv_cp + instore_dmg_nte_perishable_cp + instore_dmg_nte_packaged_cp) as inhouse_nte_cp,\n", "\n", "                short_weight_qty,\n", "                short_weight_value,\n", "                short_weight_cp,\n", "\n", "                total_positive_update_qty,\n", "                total_positive_update_value,\n", "                total_positive_update_cp,\n", "\n", "                total_negative_update_qty,\n", "                total_negative_update_value,\n", "                total_negative_update_cp,\n", "\n", "                reinventorization_diff_qty,\n", "                reinventorization_diff_value,\n", "                reinventorization_diff_cp,\n", "\n", "                cast(secondary_sale_quantity as integer) as secondary_sale_quantity,\n", "                secondary_sale_value,\n", "                secondary_sale_cp,\n", "\n", "                cast(purchase_return_quantity as integer) as purchase_return_quantity,\n", "                purchase_return_value,\n", "                purchase_return_cp,\n", "\n", "                crwi_good_ret_quantity,\n", "                crwi_good_ret_value,\n", "                crwi_good_ret_cp,\n", "\n", "                b2b_good_qty,\n", "                b2b_good_value,\n", "                b2b_good_cp,\n", "\n", "                gmv_qty,\n", "                gmv_value,\n", "\n", "                billed_qty,\n", "                billed_amt,\n", "\n", "                ext_purchase_qty,\n", "                ext_purchase_lp,\n", "                ext_purchase_cp,\n", "\n", "                case when (outlet_type = 'FE' and category in ('packaged', 'perishable')) then (instore_dmg_fr_fnv_qty + instore_dmg_fr_perishable_qty + instore_dmg_fr_packaged_qty + instore_dmg_nr_fnv_qty \n", "                                + instore_dmg_nr_perishable_qty + instore_dmg_nr_packaged_qty + short_weight_qty \n", "                                + case when item_id in (10151164, 10151165, 10151166, 10151167, 10151168, 10151169,\n", "                                                        10116462,10116467,10116671,10116889,10116890,10116903,10116904,10116918,10116920,10117911,10117912,10145261,10145262,10145294,10145296,10145328,\n", "                                                        10156734,10116918,10145294,10116920,10145296,10116890,10145261,10116903,10156724,10116889,10156729,10156736,10156733,10156731,10117911,10116904,10117912,10116671\n", "                                                        ) then 0 else instore_grv_fnv_qty end\n", "                                + case when item_id in (10151164, 10151165, 10151166, 10151167, 10151168, 10151169,\n", "                                                        10116462,10116467,10116671,10116889,10116890,10116903,10116904,10116918,10116920,10117911,10117912,10145261,10145262,10145294,10145296,10145328,\n", "                                                        10156734,10116918,10145294,10116920,10145296,10116890,10145261,10116903,10156724,10116889,10156729,10156736,10156733,10156731,10117911,10116904,10117912,10116671\n", "                                                        ) then 0 else instore_grv_packaged_qty end\n", "                                + case when item_id in (10151164, 10151165, 10151166, 10151167, 10151168, 10151169,\n", "                                                        10116462,10116467,10116671,10116889,10116890,10116903,10116904,10116918,10116920,10117911,10117912,10145261,10145262,10145294,10145296,10145328,\n", "                                                        10156734,10116918,10145294,10116920,10145296,10116890,10145261,10116903,10156724,10116889,10156729,10156736,10156733,10156731,10117911,10116904,10117912,10116671\n", "                                                        ) then 0 else instore_grv_perishable_qty end\n", "                                + case when item_id in (10151164, 10151165, 10151166, 10151167, 10151168, 10151169,\n", "                                                        10116462,10116467,10116671,10116889,10116890,10116903,10116904,10116918,10116920,10117911,10117912,10145261,10145262,10145294,10145296,10145328,\n", "                                                        10156734,10116918,10145294,10116920,10145296,10116890,10145261,10116903,10156724,10116889,10156729,10156736,10156733,10156731,10117911,10116904,10117912,10116671\n", "                                                        ) then 0 else instore_dmg_fnv_other_qty end \n", "                                + case when item_id in (10151164, 10151165, 10151166, 10151167, 10151168, 10151169,\n", "                                                        10116462,10116467,10116671,10116889,10116890,10116903,10116904,10116918,10116920,10117911,10117912,10145261,10145262,10145294,10145296,10145328,\n", "                                                        10156734,10116918,10145294,10116920,10145296,10116890,10145261,10116903,10156724,10116889,10156729,10156736,10156733,10156731,10117911,10116904,10117912,10116671\n", "                                                        ) then 0 else instore_dmg_perishable_other_qty end \n", "                                + case when item_id in (10151164, 10151165, 10151166, 10151167, 10151168, 10151169,\n", "                                                        10116462,10116467,10116671,10116889,10116890,10116903,10116904,10116918,10116920,10117911,10117912,10145261,10145262,10145294,10145296,10145328,\n", "                                                        10156734,10116918,10145294,10116920,10145296,10116890,10145261,10116903,10156724,10116889,10156729,10156736,10156733,10156731,10117911,10116904,10117912,10116671\n", "                                                        ) then 0 else instore_dmg_packaged_other_qty end \n", "                                + b2b_dmg_fnv_qty + b2b_dmg_perishable_qty + b2b_dmg_packaged_qty\n", "                          ) else 0 end as breakage_ds_qty,\n", "\n", "                case when (outlet_type = 'FE' and category in ('packaged', 'perishable')) then (instore_dmg_fr_fnv_value + instore_dmg_fr_perishable_value + instore_dmg_fr_packaged_value + instore_dmg_nr_fnv_value \n", "                                + instore_dmg_nr_perishable_value + instore_dmg_nr_packaged_value + short_weight_value \n", "                                + case when item_id in (10151164, 10151165, 10151166, 10151167, 10151168, 10151169,\n", "                                                        10116462,10116467,10116671,10116889,10116890,10116903,10116904,10116918,10116920,10117911,10117912,10145261,10145262,10145294,10145296,10145328,\n", "                                                        10156734,10116918,10145294,10116920,10145296,10116890,10145261,10116903,10156724,10116889,10156729,10156736,10156733,10156731,10117911,10116904,10117912,10116671\n", "                                                        ) then 0.0 else instore_grv_fnv_value end\n", "                                + case when item_id in (10151164, 10151165, 10151166, 10151167, 10151168, 10151169,\n", "                                                        10116462,10116467,10116671,10116889,10116890,10116903,10116904,10116918,10116920,10117911,10117912,10145261,10145262,10145294,10145296,10145328,\n", "                                                        10156734,10116918,10145294,10116920,10145296,10116890,10145261,10116903,10156724,10116889,10156729,10156736,10156733,10156731,10117911,10116904,10117912,10116671\n", "                                                        ) then 0.0 else instore_grv_packaged_value end\n", "                                + case when item_id in (10151164, 10151165, 10151166, 10151167, 10151168, 10151169,\n", "                                                        10116462,10116467,10116671,10116889,10116890,10116903,10116904,10116918,10116920,10117911,10117912,10145261,10145262,10145294,10145296,10145328,\n", "                                                        10156734,10116918,10145294,10116920,10145296,10116890,10145261,10116903,10156724,10116889,10156729,10156736,10156733,10156731,10117911,10116904,10117912,10116671\n", "                                                        ) then 0.0 else instore_grv_perishable_value end\n", "                                + case when item_id in (10151164, 10151165, 10151166, 10151167, 10151168, 10151169,\n", "                                                        10116462,10116467,10116671,10116889,10116890,10116903,10116904,10116918,10116920,10117911,10117912,10145261,10145262,10145294,10145296,10145328,\n", "                                                        10156734,10116918,10145294,10116920,10145296,10116890,10145261,10116903,10156724,10116889,10156729,10156736,10156733,10156731,10117911,10116904,10117912,10116671\n", "                                                        ) then 0 else instore_dmg_fnv_other_value end \n", "                                + case when item_id in (10151164, 10151165, 10151166, 10151167, 10151168, 10151169,\n", "                                                        10116462,10116467,10116671,10116889,10116890,10116903,10116904,10116918,10116920,10117911,10117912,10145261,10145262,10145294,10145296,10145328,\n", "                                                        10156734,10116918,10145294,10116920,10145296,10116890,10145261,10116903,10156724,10116889,10156729,10156736,10156733,10156731,10117911,10116904,10117912,10116671\n", "                                                        ) then 0 else instore_dmg_perishable_other_value end \n", "                                + case when item_id in (10151164, 10151165, 10151166, 10151167, 10151168, 10151169,\n", "                                                        10116462,10116467,10116671,10116889,10116890,10116903,10116904,10116918,10116920,10117911,10117912,10145261,10145262,10145294,10145296,10145328,\n", "                                                        10156734,10116918,10145294,10116920,10145296,10116890,10145261,10116903,10156724,10116889,10156729,10156736,10156733,10156731,10117911,10116904,10117912,10116671\n", "                                                        ) then 0 else instore_dmg_packaged_other_value end\n", "                                + b2b_dmg_fnv_value + b2b_dmg_perishable_value + b2b_dmg_packaged_value\n", "                          ) else 0.0 end as breakage_ds_value,\n", "\n", "                case when (outlet_type = 'FE' and category in ('packaged', 'perishable')) then (instore_dmg_fr_fnv_cp + instore_dmg_fr_perishable_cp + instore_dmg_fr_packaged_cp + instore_dmg_nr_fnv_cp \n", "                                + instore_dmg_nr_perishable_cp + instore_dmg_nr_packaged_cp + short_weight_cp \n", "                                + case when item_id in (10151164, 10151165, 10151166, 10151167, 10151168, 10151169,\n", "                                                        10116462,10116467,10116671,10116889,10116890,10116903,10116904,10116918,10116920,10117911,10117912,10145261,10145262,10145294,10145296,10145328,\n", "                                                        10156734,10116918,10145294,10116920,10145296,10116890,10145261,10116903,10156724,10116889,10156729,10156736,10156733,10156731,10117911,10116904,10117912,10116671\n", "                                                        ) then 0.0 else instore_grv_fnv_cp end\n", "                                + case when item_id in (10151164, 10151165, 10151166, 10151167, 10151168, 10151169,\n", "                                                        10116462,10116467,10116671,10116889,10116890,10116903,10116904,10116918,10116920,10117911,10117912,10145261,10145262,10145294,10145296,10145328,\n", "                                                        10156734,10116918,10145294,10116920,10145296,10116890,10145261,10116903,10156724,10116889,10156729,10156736,10156733,10156731,10117911,10116904,10117912,10116671\n", "                                                        ) then 0.0 else instore_grv_packaged_cp end\n", "                                + case when item_id in (10151164, 10151165, 10151166, 10151167, 10151168, 10151169,\n", "                                                        10116462,10116467,10116671,10116889,10116890,10116903,10116904,10116918,10116920,10117911,10117912,10145261,10145262,10145294,10145296,10145328,\n", "                                                        10156734,10116918,10145294,10116920,10145296,10116890,10145261,10116903,10156724,10116889,10156729,10156736,10156733,10156731,10117911,10116904,10117912,10116671\n", "                                                        ) then 0.0 else instore_grv_perishable_cp end\n", "                                + case when item_id in (10151164, 10151165, 10151166, 10151167, 10151168, 10151169,\n", "                                                        10116462,10116467,10116671,10116889,10116890,10116903,10116904,10116918,10116920,10117911,10117912,10145261,10145262,10145294,10145296,10145328,\n", "                                                        10156734,10116918,10145294,10116920,10145296,10116890,10145261,10116903,10156724,10116889,10156729,10156736,10156733,10156731,10117911,10116904,10117912,10116671\n", "                                                        ) then 0 else instore_dmg_fnv_other_cp end \n", "                                + case when item_id in (10151164, 10151165, 10151166, 10151167, 10151168, 10151169,\n", "                                                        10116462,10116467,10116671,10116889,10116890,10116903,10116904,10116918,10116920,10117911,10117912,10145261,10145262,10145294,10145296,10145328,\n", "                                                        10156734,10116918,10145294,10116920,10145296,10116890,10145261,10116903,10156724,10116889,10156729,10156736,10156733,10156731,10117911,10116904,10117912,10116671\n", "                                                        ) then 0 else instore_dmg_perishable_other_cp end \n", "                                + case when item_id in (10151164, 10151165, 10151166, 10151167, 10151168, 10151169,\n", "                                                        10116462,10116467,10116671,10116889,10116890,10116903,10116904,10116918,10116920,10117911,10117912,10145261,10145262,10145294,10145296,10145328,\n", "                                                        10156734,10116918,10145294,10116920,10145296,10116890,10145261,10116903,10156724,10116889,10156729,10156736,10156733,10156731,10117911,10116904,10117912,10116671\n", "                                                        ) then 0 else instore_dmg_packaged_other_cp end\n", "                                + b2b_dmg_fnv_cp + b2b_dmg_perishable_cp + b2b_dmg_packaged_cp\n", "                          ) else 0.0 end as breakage_ds_cp,\n", "\n", "                case when (outlet_type = 'FE' and category = 'packaged') then (total_negative_update_qty - total_positive_update_qty) else 0 end as pilferage_ds_qty,\n", "                case when (outlet_type = 'FE' and category = 'packaged') then (total_negative_update_value - total_positive_update_value) else 0.0 end as pilferage_ds_value,\n", "                case when (outlet_type = 'FE' and category = 'packaged') then (total_negative_update_cp - total_positive_update_cp) else 0.0 end as pilferage_ds_cp,\n", "\n", "                case when outlet_type = 'BE' then (total_negative_update_qty - total_positive_update_qty) else 0 end as warehouse_variance_qty,\n", "                case when outlet_type = 'BE' then (total_negative_update_value - total_positive_update_value) else 0.0 end as warehouse_variance_value,\n", "                case when outlet_type = 'BE' then (total_negative_update_cp - total_positive_update_cp) else 0.0 end as warehouse_variance_cp,\n", "\n", "                cast((\n", "                -- instore_dmg_fr_fnv_qty + instore_dmg_fr_perishable_qty + instore_dmg_fr_packaged_qty \n", "                -- + instore_dmg_nr_fnv_qty + instore_dmg_nr_perishable_qty + instore_dmg_nr_packaged_qty + \n", "                short_weight_qty\n", "                + instore_dmg_fnv_sto_qty + instore_dmg_perishable_sto_qty + instore_dmg_packaged_sto_qty \n", "                + instore_grv_fnv_qty + instore_grv_packaged_qty + instore_grv_perishable_qty \n", "                + instore_dmg_fnv_other_qty + instore_dmg_perishable_other_qty + instore_dmg_packaged_other_qty \n", "                + b2b_dmg_fnv_qty + b2b_dmg_perishable_qty + b2b_dmg_packaged_qty \n", "                + instore_dmg_expiry_fnv_qty + instore_dmg_expiry_perishable_qty + instore_dmg_expiry_packaged_qty \n", "                + b2b_dmg_expiry_fnv_qty + b2b_dmg_expiry_perishable_qty + b2b_dmg_expiry_packaged_qty \n", "                + instore_dmg_nte_fnv_qty + instore_dmg_nte_perishable_qty + instore_dmg_nte_packaged_qty \n", "                + b2b_dmg_nte_fnv_qty + b2b_dmg_nte_perishable_qty + b2b_dmg_nte_packaged_qty \n", "                - total_positive_update_qty + total_negative_update_qty - reinventorization_diff_qty \n", "                - secondary_sale_quantity - purchase_return_quantity - b2b_good_qty) as integer) as total_losses_qty,\n", "                \n", "                (\n", "                -- instore_dmg_fr_fnv_value + instore_dmg_fr_perishable_value + instore_dmg_fr_packaged_value \n", "                -- + instore_dmg_nr_fnv_value + instore_dmg_nr_perishable_value + instore_dmg_nr_packaged_value + \n", "                short_weight_value\n", "                + instore_dmg_fnv_sto_value + instore_dmg_perishable_sto_value + instore_dmg_packaged_sto_value \n", "                + instore_grv_fnv_value + instore_grv_packaged_value + instore_grv_perishable_value \n", "                + instore_dmg_fnv_other_value + instore_dmg_perishable_other_value + instore_dmg_packaged_other_value \n", "                + b2b_dmg_fnv_value + b2b_dmg_perishable_value + b2b_dmg_packaged_value \n", "                + instore_dmg_expiry_fnv_value + instore_dmg_expiry_perishable_value + instore_dmg_expiry_packaged_value \n", "                + b2b_dmg_expiry_fnv_value + b2b_dmg_expiry_perishable_value + b2b_dmg_expiry_packaged_value \n", "                + instore_dmg_nte_fnv_value + instore_dmg_nte_perishable_value + instore_dmg_nte_packaged_value \n", "                + b2b_dmg_nte_fnv_value + b2b_dmg_nte_perishable_value + b2b_dmg_nte_packaged_value \n", "                - total_positive_update_value + total_negative_update_value - reinventorization_diff_value \n", "                - secondary_sale_value - purchase_return_value - b2b_good_value) as total_losses_value,\n", "\n", "                (\n", "                -- instore_dmg_fr_fnv_cp + instore_dmg_fr_perishable_cp + instore_dmg_fr_packaged_cp \n", "                -- + instore_dmg_nr_fnv_cp + instore_dmg_nr_perishable_cp + instore_dmg_nr_packaged_cp + \n", "                short_weight_cp\n", "                + instore_dmg_fnv_sto_cp + instore_dmg_perishable_sto_cp + instore_dmg_packaged_sto_cp + instore_grv_fnv_cp + instore_grv_packaged_cp \n", "                + instore_grv_perishable_cp + instore_dmg_fnv_other_cp + instore_dmg_perishable_other_cp + instore_dmg_packaged_other_cp \n", "                + b2b_dmg_fnv_cp + b2b_dmg_perishable_cp + b2b_dmg_packaged_cp + instore_dmg_expiry_fnv_cp + instore_dmg_expiry_perishable_cp \n", "                + instore_dmg_expiry_packaged_cp + b2b_dmg_expiry_fnv_cp + b2b_dmg_expiry_perishable_cp + b2b_dmg_expiry_packaged_cp + instore_dmg_nte_fnv_cp \n", "                + instore_dmg_nte_perishable_cp + instore_dmg_nte_packaged_cp + b2b_dmg_nte_fnv_cp + b2b_dmg_nte_perishable_cp + b2b_dmg_nte_packaged_cp \n", "                - total_positive_update_cp + total_negative_update_cp - reinventorization_diff_cp - secondary_sale_cp - purchase_return_cp - b2b_good_cp) as total_losses_cp,\n", "\n", "                rsto_dmg_qty,\n", "                rsto_dmg_amt,\n", "                rsto_dmg_cp,\n", "\n", "                rsto_nte_qty,\n", "                rsto_nte_amt,\n", "                rsto_nte_cp,\n", "\n", "                rsto_exp_qty,\n", "                rsto_exp_amt,\n", "                rsto_exp_cp,\n", "                \n", "                dmg_rtv_flag,\n", "                nte_rtv_flag\n", "\n", "            from base2\n", "            where sor_bouquet_flag = 0\n", "            )\n", "\n", "            select\n", "                day, \n", "                facility_id,\n", "                outlet_id,\n", "                outlet_name,\n", "                outlet_city,\n", "                outlet_type,\n", "                item_id,\n", "                item_name,\n", "                l0,\n", "                l1,\n", "                product_type,\n", "                category,\n", "                rtv_elg_flag,\n", "\n", "                sum(fr_qty) as fr_qty,\n", "                sum(fr_value) as fr_value,\n", "                sum(fr_cp) as fr_cp,\n", "\n", "                sum(nr_qty) as nr_qty,\n", "                sum(nr_value) as nr_value,\n", "                sum(nr_cp) as nr_cp,\n", "\n", "                sum(sto_dmg_qty) as sto_dmg_qty,\n", "                sum(sto_dmg_value) as sto_dmg_value,\n", "                sum(sto_dmg_cp) as sto_dmg_cp,\n", "\n", "                sum(grv_qty) as grv_qty,\n", "                sum(grv_value) as grv_value,\n", "                sum(grv_cp) as grv_cp,\n", "\n", "                sum(b2b_dmg_qty) as b2b_dmg_qty,\n", "                sum(b2b_dmg_value) as b2b_dmg_value,\n", "                sum(b2b_dmg_cp) as b2b_dmg_cp,\n", "\n", "                sum(b2b_dmg_expiry_qty) as b2b_dmg_expiry_qty,\n", "                sum(b2b_dmg_expiry_value) as b2b_dmg_expiry_value,\n", "                sum(b2b_dmg_expiry_cp) as b2b_dmg_expiry_cp,\n", "\n", "                sum(b2b_dmg_nte_qty) as b2b_dmg_nte_qty,\n", "                sum(b2b_dmg_nte_value) as b2b_dmg_nte_value,\n", "                sum(b2b_dmg_nte_cp) as b2b_dmg_nte_cp,\n", "\n", "                sum(case when (l2_id = 1185 and outlet_type = 'FE') then 0 else inhouse_dmg_qty end) as inhouse_dmg_qty,\n", "                sum(case when (l2_id = 1185 and outlet_type = 'FE') then 0.0 else inhouse_dmg_value end) as inhouse_dmg_value,\n", "                sum(case when (l2_id = 1185 and outlet_type = 'FE') then 0.0 else inhouse_dmg_cp end) as inhouse_dmg_cp,\n", "\n", "                sum(coalesce(case when l2_id = 1185 and outlet_type = 'FE' then inhouse_dmg_qty else 0 end,0) + coalesce(inhouse_expiry_qty,0)) as inhouse_expiry_qty,\n", "                sum(coalesce(case when l2_id = 1185 and outlet_type = 'FE' then inhouse_dmg_value else 0 end,0.0) + coalesce(inhouse_expiry_value,0.0)) as inhouse_expiry_value,\n", "                sum(coalesce(case when l2_id = 1185 and outlet_type = 'FE' then inhouse_dmg_cp else 0 end,0.0) + coalesce(inhouse_expiry_cp,0.0)) as inhouse_expiry_cp,\n", "\n", "                sum(inhouse_nte_qty) as inhouse_nte_qty,\n", "                sum(inhouse_nte_value) as inhouse_nte_value,\n", "                sum(inhouse_nte_cp) as inhouse_nte_cp,\n", "\n", "                sum(short_weight_qty) as short_weight_qty,\n", "                sum(short_weight_value) as short_weight_value,\n", "                sum(short_weight_cp) as short_weight_cp,\n", "\n", "                sum(total_positive_update_qty) as total_positive_update_qty,\n", "                sum(total_positive_update_value) as total_positive_update_value,\n", "                sum(total_positive_update_cp) as total_positive_update_cp,\n", "\n", "                sum(total_negative_update_qty) as total_negative_update_qty,\n", "                sum(total_negative_update_value) as total_negative_update_value,\n", "                sum(total_negative_update_cp) as total_negative_update_cp,\n", "                \n", "                sum(reinventorization_diff_qty) as reinventorization_diff_qty,\n", "                sum(reinventorization_diff_value) as reinventorization_diff_value,\n", "                sum(reinventorization_diff_cp) as reinventorization_diff_cp,\n", "\n", "                sum(secondary_sale_quantity) as secondary_sale_quantity,\n", "                sum(secondary_sale_value) as secondary_sale_value,\n", "                sum(secondary_sale_cp) as secondary_sale_cp,\n", "\n", "                sum(purchase_return_quantity) as purchase_return_quantity,\n", "                sum(purchase_return_value) as purchase_return_value,\n", "                sum(purchase_return_cp) as purchase_return_cp,\n", "\n", "                sum(crwi_good_ret_quantity) as crwi_good_ret_quantity,\n", "                sum(crwi_good_ret_value) as crwi_good_ret_value,\n", "                sum(crwi_good_ret_cp) as crwi_good_ret_cp,\n", "\n", "                sum(b2b_good_qty) as b2b_good_qty,\n", "                sum(b2b_good_value) as b2b_good_value,\n", "                sum(b2b_good_cp) as b2b_good_cp,\n", "\n", "                sum(gmv_qty) as gmv_qty,\n", "                sum(gmv_value) as gmv_value,\n", "\n", "                sum(billed_qty) as billed_qty,\n", "                sum(billed_amt) as billed_amt,\n", "\n", "                sum(ext_purchase_qty) as ext_purchase_qty,\n", "                sum(ext_purchase_lp) as ext_purchase_lp,\n", "                sum(ext_purchase_cp) as ext_purchase_cp,\n", "\n", "                sum(case when l2_id = 1185 then 0 else breakage_ds_qty end) as breakage_ds_qty,\n", "                sum(case when l2_id = 1185 then 0.0 else breakage_ds_value end) as breakage_ds_value,\n", "                sum(case when l2_id = 1185 then 0.0 else breakage_ds_cp end) as breakage_ds_cp,\n", "\n", "                sum(pilferage_ds_qty) as pilferage_ds_qty,\n", "                sum(pilferage_ds_value) as pilferage_ds_value,\n", "                sum(pilferage_ds_cp) as pilferage_ds_cp,\n", "\n", "                sum(warehouse_variance_qty) as warehouse_variance_qty,\n", "                sum(warehouse_variance_value) as warehouse_variance_value,\n", "                sum(warehouse_variance_cp) as warehouse_variance_cp,\n", "\n", "                sum(total_losses_qty) as total_losses_qty,\n", "                sum(total_losses_value) as total_losses_value,\n", "                sum(total_losses_cp) as total_losses_cp,\n", "\n", "                sum(pilferage_ds_qty + (case when l2_id = 1185 then 0 else breakage_ds_qty end)) as pilferage_breakage_qty,\n", "                sum(pilferage_ds_value + (case when l2_id = 1185 then 0.0 else breakage_ds_value end)) as pilferage_breakage_value,\n", "                sum(pilferage_ds_cp + (case when l2_id = 1185 then 0.0 else breakage_ds_cp end)) as pilferage_breakage_cp,\n", "\n", "                sum(rsto_dmg_qty) as rsto_dmg_qty,\n", "                sum(rsto_dmg_amt) as rsto_dmg_amt,\n", "                sum(rsto_dmg_cp) as rsto_dmg_cp,\n", "\n", "                sum(rsto_nte_qty) as rsto_nte_qty,\n", "                sum(rsto_nte_amt) as rsto_nte_amt,\n", "                sum(rsto_nte_cp) as rsto_nte_cp,\n", "\n", "                sum(rsto_exp_qty) as rsto_exp_qty,\n", "                sum(rsto_exp_amt) as rsto_exp_amt,\n", "                sum(rsto_exp_cp) as rsto_exp_cp,\n", "\n", "                sum(\n", "                case when category = 'packaged' then cast((inhouse_expiry_qty + inhouse_nte_qty + b2b_dmg_expiry_qty + b2b_dmg_nte_qty\n", "                                + rsto_exp_qty + rsto_nte_qty) as double) \n", "                    else cast((total_losses_qty - (pilferage_ds_qty \n", "                                                + (coalesce(case when l2_id = 1185 and outlet_type = 'FE' then 0.0 else breakage_ds_qty end,0.0))\n", "                                                ) - warehouse_variance_qty + rsto_dmg_qty + rsto_nte_qty + rsto_exp_qty\n", "                        ) as double) end)\n", "                    as net_dump_qty,\n", "                \n", "                sum(\n", "                case when category = 'packaged' then cast((inhouse_expiry_value + inhouse_nte_value + b2b_dmg_expiry_value + b2b_dmg_nte_value\n", "                                + rsto_exp_amt + rsto_nte_amt) as double) \n", "                    else cast((total_losses_value - (pilferage_ds_value \n", "                                                + (coalesce(case when l2_id = 1185 and outlet_type = 'FE' then 0.0 else breakage_ds_value end,0.0))\n", "                                                ) - warehouse_variance_value + rsto_dmg_amt + rsto_nte_amt + rsto_exp_amt\n", "                        ) as double) end)\n", "                    as net_dump_value,\n", "                \n", "                sum(\n", "                case when category = 'packaged' then cast((inhouse_expiry_cp + inhouse_nte_cp + b2b_dmg_expiry_cp + b2b_dmg_nte_cp\n", "                                + rsto_exp_cp + rsto_nte_cp) as double) \n", "                    else cast((total_losses_cp - (pilferage_ds_cp \n", "                                                + (coalesce(case when l2_id = 1185 and outlet_type = 'FE' then 0.0 else breakage_ds_cp end,0.0))\n", "                                                ) - warehouse_variance_cp + rsto_dmg_cp + rsto_nte_cp + rsto_exp_cp\n", "                        ) as double) end)\n", "                    as net_dump_cp,\n", "\n", "                sum(cast((total_losses_qty - (pilferage_ds_qty \n", "                                                + (coalesce(case when l2_id = 1185 and outlet_type = 'FE' then 0.0 else breakage_ds_qty end,0.0))\n", "                                                ) - warehouse_variance_qty) \n", "                    + reinventorization_diff_qty + secondary_sale_quantity + purchase_return_quantity\n", "                    + rsto_dmg_qty + rsto_nte_qty + rsto_exp_qty as integer)\n", "                    )\n", "                as gross_dump_qty,\n", "\n", "                sum(cast((total_losses_value - (pilferage_ds_value \n", "                                                + (coalesce(case when l2_id = 1185 and outlet_type = 'FE' then 0.0 else breakage_ds_value end,0.0))\n", "                                                ) - warehouse_variance_value) \n", "                    + reinventorization_diff_value + secondary_sale_value +  purchase_return_value\n", "                    + rsto_dmg_amt + rsto_nte_amt + rsto_exp_amt as double)\n", "                    ) as gross_dump_value,\n", "\n", "                sum(cast((total_losses_cp - (pilferage_ds_cp \n", "                                                + (coalesce(case when l2_id = 1185 and outlet_type = 'FE' then 0.0 else breakage_ds_cp end,0.0))\n", "                                                ) - warehouse_variance_cp) \n", "                    + reinventorization_diff_cp + secondary_sale_cp + purchase_return_cp\n", "                    + rsto_dmg_cp + rsto_nte_cp + rsto_exp_cp as double)\n", "                    ) as gross_dump_cp,\n", "                \n", "                dmg_rtv_flag,\n", "                nte_rtv_flag,\n", "                rl_flag,\n", "                \n", "                sum(case when rl_flag = 'Others' then purchase_return_quantity else 0 end) as prn_for_dump_qty,\n", "                sum(case when rl_flag = 'Others' then purchase_return_value else 0.0 end) as prn_for_dump_value,\n", "                sum(case when rl_flag = 'Others' then purchase_return_cp else 0.0 end) as prn_for_dump_cp,\n", "                \n", "                sum(case when rl_flag = 'Others' then secondary_sale_quantity else 0 end) as ss_for_dump_qty,\n", "                sum(case when rl_flag = 'Others' then secondary_sale_value else 0.0 end) as ss_for_dump_value,\n", "                sum(case when rl_flag = 'Others' then secondary_sale_cp else 0.0 end) as ss_for_dump_cp,\n", "                \n", "                sum(case when rl_flag != 'Others' then purchase_return_quantity else 0 end) as prn_for_rl_qty,\n", "                sum(case when rl_flag != 'Others' then purchase_return_value else 0.0 end) as prn_for_rl_value,\n", "                sum(case when rl_flag != 'Others' then purchase_return_cp else 0.0 end) as prn_for_rl_cp,\n", "                \n", "                sum(case when rl_flag != 'Others' then secondary_sale_quantity else 0 end) as ss_for_rl_qty,\n", "                sum(case when rl_flag != 'Others' then secondary_sale_value else 0.0 end) as ss_for_rl_value,\n", "                sum(case when rl_flag != 'Others' then secondary_sale_cp else 0.0 end) as ss_for_rl_cp\n", "                \n", "            from base3\n", "            where case when day = date '2024-09-14' then item_id not in (10147451, 10147454, 10156315) else item_id is not null end\n", "                and outlet_id not in (5193, 5124, 5195) -- KS Outlets\n", "                and outlet_id not in (4146, 5054)\n", "                and outlet_id not in (select id from retail.console_outlet where company_type_id = 771 group by 1)\n", "                and product_type not like '%%flyer%%'\n", "                \n", "            group by 1,2,3,4,5,6,7,8,9,10,11,12,13,dmg_rtv_flag,nte_rtv_flag,rl_flag\n", "            \"\"\".format(\n", "        start_date=start_date, end_date=chunk_end_date, day_week_month=day_week_month\n", "    )\n", "    pb.to_trino(sql_query, **kwargs)\n", "    pb.send_slack_message(\n", "        \"U03S75C25B7\",\n", "        \"Inventory Dump Table updated for \" + str(start_date) + \" - \" + str(chunk_end_date),\n", "    )\n", "\n", "    start_date += timedelta(days=chunk_size.days)"]}, {"cell_type": "code", "execution_count": null, "id": "8321cc8e-0555-4c76-bb8c-8363696a1b0b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}