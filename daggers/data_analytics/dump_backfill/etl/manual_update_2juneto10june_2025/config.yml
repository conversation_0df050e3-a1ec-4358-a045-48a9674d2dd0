alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 3
dag_name: manual_update_2juneto10june_2025
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_analytics
notebooks:
- executor_config:
    load_type: tiny
    node_type: spot
  name: notebook_1
  parameters: null
  retries: 1
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: notebook_2
  parameters: null
  retries: 1
  tag: second
owner:
  email: <EMAIL>
  slack_id: U03S75C25B7
path: data_analytics/dump_backfill/etl/manual_update_2juneto10june_2025
paused: false
pool: data_analytics_pool
project_name: dump_backfill
schedule:
  end_date: '2025-06-15T00:00:00'
  interval: 30 2 11 6 *
  start_date: '2025-06-11T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 1
