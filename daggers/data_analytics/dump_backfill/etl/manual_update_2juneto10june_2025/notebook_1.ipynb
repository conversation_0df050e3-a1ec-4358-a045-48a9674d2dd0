{"cells": [{"cell_type": "code", "execution_count": null, "id": "ebdf6bf5-6955-4066-ad80-c8438c7dba1e", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "ba129fa7-cf41-4bf5-818e-184d20235b54", "metadata": {}, "outputs": [], "source": ["day_week_month = \"day\""]}, {"cell_type": "code", "execution_count": null, "id": "8f7d1269-faee-44f7-96a6-e141ed9cfc41", "metadata": {}, "outputs": [], "source": ["chunk_size = <PERSON><PERSON>ta(days=5)"]}, {"cell_type": "code", "execution_count": null, "id": "cf9608d7-fdcb-4bcc-b6dd-38483f3b5343", "metadata": {}, "outputs": [], "source": ["start_date = datetime(2025, 6, 2).date()\n", "end_date = datetime(2025, 6, 10).date()"]}, {"cell_type": "markdown", "id": "7c476d31-4eb4-4e36-96bf-4cb8ad1490a6", "metadata": {}, "source": ["### MU"]}, {"cell_type": "code", "execution_count": null, "id": "ec5c3d0e-8dd2-42a8-bc0b-d3af9e9b9343", "metadata": {"jupyter": {"source_hidden": true}, "tags": []}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"day\", \"type\": \"DATE\", \"description\": \"day\"},\n", "    {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"facility_id\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"outlet_id\"},\n", "    {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"item_id\"},\n", "    {\n", "        \"name\": \"total_positive_update_value\",\n", "        \"type\": \"DOUBLE\",\n", "        \"description\": \"total_positive_update_value\",\n", "    },\n", "    {\n", "        \"name\": \"total_positive_update_cp\",\n", "        \"type\": \"DOUBLE\",\n", "        \"description\": \"total_positive_update_cp\",\n", "    },\n", "    {\n", "        \"name\": \"total_positive_update_qty\",\n", "        \"type\": \"INTEGER\",\n", "        \"description\": \"total_positive_update_qty\",\n", "    },\n", "    {\n", "        \"name\": \"total_negative_update_value\",\n", "        \"type\": \"DOUBLE\",\n", "        \"description\": \"total_negative_update_value\",\n", "    },\n", "    {\n", "        \"name\": \"total_negative_update_cp\",\n", "        \"type\": \"DOUBLE\",\n", "        \"description\": \"total_negative_update_cp\",\n", "    },\n", "    {\n", "        \"name\": \"total_negative_update_qty\",\n", "        \"type\": \"INTEGER\",\n", "        \"description\": \"total_negative_update_qty\",\n", "    },\n", "]\n", "table_description = \"Base table for inventory dump table and this table stores manual update data\"\n", "\n", "kwargs = {\n", "    \"schema_name\": \"ba_etls\",\n", "    \"table_name\": \"inv_dump_base_manual_update\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"day\", \"facility_id\", \"outlet_id\", \"item_id\"],\n", "    \"incremental_key\": \"\",\n", "    \"sortkey\": [],\n", "    \"table_description\": table_description,\n", "    \"partition_key\": [\"day\"],\n", "    #     \"distkey\": \"GroupName\",\n", "    \"load_type\": \"partition_overwrite\",  # append, rebuild, truncate or upsert\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "b66eccfa-5003-4468-a220-98c43c5e0fe5", "metadata": {"jupyter": {"source_hidden": true}, "tags": []}, "outputs": [], "source": ["while start_date <= end_date:\n", "    chunk_end_date = min(start_date + chunk_size - timedelta(days=1), end_date)\n", "    sql_query = f\"\"\"\n", "    with \n", "tax as(\n", "with base as(\n", "select \n", "    p.item_id,\n", "    coalesce(igst,0) as igst_,\n", "    (COALESCE(cgst,0) + COALESCE(cess,0) + COALESCE(sgst,0)) as tax1,\n", "    (coalesce(igst,0) + COALESCE(cess,0)) as tax2,\n", "    coalesce(additional_cess_value,0) as additional_cess_value,\n", "    case when igst = 0 then (COALESCE(cgst,0) + COALESCE(cess,0) + COALESCE(sgst,0)) else (coalesce(igst,0) + COALESCE(cess,0)) end as product_tax,\n", "    p.updated_at,\n", "    row_number() over(partition by item_id order by p.updated_at desc) as rank_1\n", "\n", "from rpc.product_tax p \n", "where p.active = 1 \n", "and lake_active_record = true\n", "group by 1,2,3,4,5,6,7\n", ")\n", "\n", "select\n", "    item_id,\n", "    additional_cess_value,\n", "    case when product_tax is null then 0 else product_tax end as product_tax\n", "\n", "from base\n", "where rank_1 = 1\n", "group by 1,2,3\n", "order by 1,2,3)\n", "\n", ",base as (\n", "SELECT\n", "    pos_timestamp,\n", "    date(date_trunc('{day_week_month}',(iil.pos_timestamp + interval '330' minute))) AS {day_week_month},\n", "    iil.outlet_id as outlet_id,\n", "    o.facility_id,\n", "    o.name as outlet_name,\n", "    o.location as city,\n", "    p.item_id,\n", "    p.name as item_name,\n", "    cd.l0,\n", "    dp.product_type,\n", "    CASE WHEN l0_id in (1487) then 'fnv'\n", "       WHEN l0_id in (888,15,4,14,12) and l1_id in (279,1425,1361,1362,1363,1388,922,123,923,1200,953,1184) \n", "        and l2_id in (1956,1425,63,1367,1369,1730,127,1094,949,138,1091,1185,123,1778,950,1389,31,1097,198,1093) then 'perishable'\n", "       ELSE 'packaged' end as category,\n", "    coalesce(handling_type,'0') as handling_type,\n", "    inventory_update_type_id,\n", "    case \n", "        when iiut.inventory_operation = '+' then 'positive_variance'\n", "        when iiut.inventory_operation = '-' then 'negative_variance'\n", "        when iil.inventory_update_type_id = 155 then 'negative_variance'\n", "        when iil.inventory_update_type_id = 158 then 'positive_variance'\n", "        when iil.inventory_update_type_id = 154 then '154'\n", "    end as manual_update,\n", "    iil.\"delta\" as qty,  \n", "    (iil.\"delta\"*iil.weighted_lp) as Total_landing_Price,\n", "    case when (product_tax = 0 and (t.additional_cess_value) = 0) then (iil.weighted_lp) \n", "            when (product_tax = 0 and (t.additional_cess_value) > 0) then (iil.weighted_lp - (t.additional_cess_value*iil.\"delta\"))\n", "            when (product_tax > 0 and (t.additional_cess_value) = 0) then ((iil.weighted_lp) / (1+product_tax/100)) \n", "            else ((iil.weighted_lp - (t.additional_cess_value*iil.\"delta\")) / (1+product_tax/100))\n", "        end as cp\n", "    \n", "    FROM ims.ims_inventory_log iil\n", "    INNER JOIN rpc.product_product p ON iil.variant_id=p.variant_id and iil.lake_active_record = true and p.lake_active_record = true\n", "    LEFT JOIN rpc.item_product_mapping ipm ON p.item_id = ipm.item_id and ipm.active=1 and ipm.lake_active_record = true\n", "    LEFT JOIN dwh.dim_product dp ON ipm.product_id = dp.product_id and is_current\n", "    LEFT JOIN rpc.item_category_details cd ON p.item_id = cd.item_id and cd.lake_active_record = true\n", "    INNER JOIN ims.ims_inventory_update_type iiut ON iiut.id=iil.inventory_update_type_id and iiut.lake_active_record = true\n", "    INNER JOIN retail.console_outlet o ON o.id=iil.outlet_id and business_type_id in (1,7,8,12,19,20) and o.lake_active_record = true\n", "    INNER JOIN crates.facility fc on o.facility_id = fc.id and fc.lake_active_record = true\n", "    LEFT JOIN tax t ON p.item_id = t.item_id\n", "    \n", "    WHERE iil.pos_timestamp >= timestamp'{start_date}' - interval '330' minute\n", "        AND iil.pos_timestamp < timestamp'{end_date}' - interval '330' minute + interval '1' day\n", "        AND p.item_id <> 10109877\n", "        and iil.insert_ds_ist >= '{start_date}'\n", "        and CASE \n", "            -- Inventory adjustment done to clear the pending putaway in the inventory update type id 42 & 47 by the user GR0001, \n", "            -- these should be included in reportings at BE\n", "            WHEN (iil.created_by_name = 'GR0001' AND business_type_id not in (7) and iil.insert_ds_ist between '2024-12-23' and '2024-12-29')\n", "            then iil.inventory_update_type_id IN (\n", "                    14, 26, 47, 44, 45, 46, 48, 74, 118, 112, --Positive\n", "                    15, 27, 39, 40, 41, 43, 42, 73, 117, 129, 130, 131, 132, 113 --negative\n", "                )\n", "            when (outlet_id = 4095 and iil.insert_ds_ist = '2025-02-12')\n", "            then iil.inventory_update_type_id IN (\n", "                    14, 26, 47, 44, 45, 46, 48, 74, 118, 112, --Positive\n", "                    15, 27, 40, 41, 43, 42, 73, 117, 129, 130, 131, 132, 113 --negative\n", "                )\n", "            WHEN \n", "                -- On below dates ESTO transfer loss was atrributed in \n", "                -- inventory update_id = 42\n", "                iil.pos_timestamp BETWEEN (timestamp'2022-07-30 00:00:00' - interval '330' minute) AND (timestamp'2022-07-30 23:59:59' - interval '330' minute)\n", "                OR iil.pos_timestamp BETWEEN (timestamp'2022-08-05 23:00:00' -  interval '330' minute) AND (timestamp'2022-08-06 23:59:59' - interval '330' minute)\n", "                OR iil.pos_timestamp BETWEEN (timestamp'2022-08-15 23:00:00' -  interval '330' minute) AND (timestamp'2022-08-18 23:59:59' - interval '330' minute)\n", "                OR iil.pos_timestamp BETWEEN (timestamp'2022-08-23 23:45:00' - interval '330' minute) AND (timestamp'2022-08-24 01:15:00' - interval '330' minute)\n", "                OR iil.pos_timestamp BETWEEN (timestamp'2022-08-24 22:00:00' - interval '330' minute) AND (timestamp'2022-08-25 03:00:00' - interval '330' minute)\n", "                OR iil.pos_timestamp BETWEEN (timestamp'2022-08-26 23:30:00' - interval '330' minute) AND (timestamp'2022-08-27 04:00:00' - interval '330' minute)\n", "                OR iil.pos_timestamp BETWEEN (timestamp'2022-08-28 23:30:00' - interval '330' minute) AND (timestamp'2022-08-29 02:00:00' - interval '330' minute)\n", "                OR iil.pos_timestamp BETWEEN (timestamp'2022-09-24 01:00:00' - interval '330' minute)  AND (timestamp'2022-09-24 03:30:00' - interval '330' minute)\n", "                OR (iil.created_by_name in ('GR0001') AND NOT (outlet_id = 1716 and iil.pos_timestamp BETWEEN (timestamp'2023-03-26' - interval '330' minute)  AND (timestamp'2023-03-26' - interval '330' minute + interval '1' day))) -- AND o.business_type_id = 7)\n", "                \n", "            THEN \n", "                iil.inventory_update_type_id IN (\n", "                    14, 26, 44, 45, 46, 48, 74, 118, 112, --Positive\n", "                    15, 27, 39, 40, 41, 43, 73, 117, 129, 130, 131, 132, 113, 154, 155 --negative\n", "                )\n", "            ELSE\n", "                iil.inventory_update_type_id IN (\n", "                    14, 26, 47, 44, 45, 46, 48, 74, 118, 112, 158, --Positive\n", "                    15, 27, 39, 40, 41, 43, 42, 73, 117, 129, 130, 131, 132, 113, 155 --negative\n", "                )\n", "        END\n", ")\n", "\n", "select \n", "    {day_week_month}, \n", "    facility_id,\n", "    b.outlet_id,\n", "    b.item_id,\n", "    cast(coalesce(sum(case when manual_update='positive_variance' and  outlet_id = 2574 and date(pos_timestamp + interval '330' minute) = date'2022-10-12' and b.item_id in (10032150,10079800,10108966,10110057,10107038,10073494,10111018,10115482,10115359,10115991,10115072,10115232,10115083,10115357,10004469,10115264,10111086,10005801,10115365,10037999,10110656,10115993,10115261,10115992,10005621,10034374,10115260,10002324,10011835,10116111,10010529,10115112,10112280,10115113,10116215,10114759,10112278,10006784,10107499,10115081,10079943,10110403,10113533,10115069,10108092,10113534,10113532,10070221,10092025,10115236,10110862,10110671,10012106,10115966,10017936,10116351,10115768,10115762,10108968,10116110,10110677,10014777,10115982,10108978,10107070,10097122,10114966,10003285,10111427,10079866,10114718,10115765,10010005,10116186,10045739,10116179,10115240,10045533,10115364,10110404,10091050,10116308,10066883,10012329,10066058,10112202,10114758,10110541,10110007,10111019,10116107,10110365,10066873,10106943,10005657,10079824,10112290,10087948,10017936,10010816,10115097,10115767,10105783,10109449,10116152,10115610,10116173,10006589,10110072,10115071,10107497,10110562,10104037,10003293,10076353,10035533,10090663,10109402,10109714,10027497,10027495,10106082,10078574,10116177,10110676,10103083,10108973,10108974,10115111,10017397,10115243,10114509,10115095,10109447,10113267,10034558,10115263,10116309,10115117,10012300,10116183,10049068,10115084,10115964,10115758,10110366,10110408,10110868,10063448,10114922,10090663,10115258,10114967,10111345,10108093,10105544,10112463,10115098,10115199,10066874,10116149,10115760,10012339,10112289,10020237,10115253,10107494,10050752,10109448,10115088,10078570,10115363,10003387,10068879,10115231,10115481,10115262,10111019,10110426,10111016,10110976,10105469,10115379,10004491,10092005,10001529,10010898,10115257,10116116,10116185,10107037,10116109,10083600,10083285,10107495,10106590,10016547,10109715,10006589,10005321,10017510,10115246,10111078,10113851,10012730,10017936,10047286,10115075,10112246,10115358,10116170,10115360,10011011,10016265,10110593,10108092,10092763,10038696,10110407,10002212,10111716,10097121,10111085,10115967,10105596,10097111,10115480,10107038,10089713,10111016,10110871,10092027,10115963,10007537,10109716,10115965,10062416,10115268,10110591,10116157,10116151,10066878,10049068,10113269,10066884,10013813,10115067,10095484,10114902,10092024,10110684,10115761,10115968,10115764,10115756,10111688,10099130,10002013,10115962,10109452,10107805,10006923,10110108,10115119,10103100,10015340,10042809,10116158,10115076,10005327,10115235,10012106,10115988,10001540,10063469,10005802,10116187,10116231,10104034,10097109,10110655,10063570,10110233,10110409,10114720,10066900,10089712,10079824,10075050,10075229,10114471,10079942,10115070,10112292,10116352,10001426,10110646,10006374,10115118,10116180,10001557,10001426,10110365,10115114,10000938,10115759,10115094,10087208,10116108,10006589,10115004,10030016,10116142,10115093,10115230,10107500,10115354,10115205,10090673,10028277,10115082,10114901,10006536,10001426,10114469,10115969,10114753,10115116,10107178,10110406,10079938,10115245,10107120,10001389,10025665,10102230,10109905,10115237,10115078,10114493,10116178,10035511,10001951,10110655,10037578,10076410,10010916,10017510,10115273,10110397,10001559,10107186,10109451,10107039,10029767,10110860,10115096,10115238,10108979,10003326,10115826,10002499,10025189,10012285,10115120,10110232,10106512,10110405,10115361,10097117,10110863,10079797,10110231,10109871,10115259,10103062,10102164,10010127,10076352,10035506,10110820,10075058,10108815,10113535,10049069,10111441,10076305,10111439,10003293,10016260,10090674,10091283,10111088,10011851,10026845,10042708,10011955,10111437,10098381,10090748,10026410,10009987,10108011,10114719,10017295,10107857,10087947,10016544,10110929,10110870,10114934,10016544,10010005,10107803,10076352,10017147,10110874,10101225,10042664,10114930,10110859,10114725,10010131,10114925,10067833,10047249,10107244,10111050,10104792,10106075,10110920,10012287,10107856,10006061,10010129,10016284,10107802,10114017,10091270,10107717,10106610,10092025,10010813,10008974,10108816,10012285,10089713,10107368,10110786,10111087,10034374,10114933,10112271,10109942,10005801,10098381,10111255,10111435,10063452,10110458,10087208,10042656,10010128,10114016,10046647,10017214,10003022,10025195,10092026,10074483,10106063,10043725,10105321,10110679,10011955,10002013)  then 0 \n", "            when manual_update='positive_variance' then b.Total_landing_Price end),0.0) as double) \n", "        total_positive_update_value,\n", "    cast(coalesce(sum(case when manual_update='positive_variance' and  outlet_id = 2574 and date(pos_timestamp + interval '330' minute) = date'2022-10-12' and b.item_id in (10032150,10079800,10108966,10110057,10107038,10073494,10111018,10115482,10115359,10115991,10115072,10115232,10115083,10115357,10004469,10115264,10111086,10005801,10115365,10037999,10110656,10115993,10115261,10115992,10005621,10034374,10115260,10002324,10011835,10116111,10010529,10115112,10112280,10115113,10116215,10114759,10112278,10006784,10107499,10115081,10079943,10110403,10113533,10115069,10108092,10113534,10113532,10070221,10092025,10115236,10110862,10110671,10012106,10115966,10017936,10116351,10115768,10115762,10108968,10116110,10110677,10014777,10115982,10108978,10107070,10097122,10114966,10003285,10111427,10079866,10114718,10115765,10010005,10116186,10045739,10116179,10115240,10045533,10115364,10110404,10091050,10116308,10066883,10012329,10066058,10112202,10114758,10110541,10110007,10111019,10116107,10110365,10066873,10106943,10005657,10079824,10112290,10087948,10017936,10010816,10115097,10115767,10105783,10109449,10116152,10115610,10116173,10006589,10110072,10115071,10107497,10110562,10104037,10003293,10076353,10035533,10090663,10109402,10109714,10027497,10027495,10106082,10078574,10116177,10110676,10103083,10108973,10108974,10115111,10017397,10115243,10114509,10115095,10109447,10113267,10034558,10115263,10116309,10115117,10012300,10116183,10049068,10115084,10115964,10115758,10110366,10110408,10110868,10063448,10114922,10090663,10115258,10114967,10111345,10108093,10105544,10112463,10115098,10115199,10066874,10116149,10115760,10012339,10112289,10020237,10115253,10107494,10050752,10109448,10115088,10078570,10115363,10003387,10068879,10115231,10115481,10115262,10111019,10110426,10111016,10110976,10105469,10115379,10004491,10092005,10001529,10010898,10115257,10116116,10116185,10107037,10116109,10083600,10083285,10107495,10106590,10016547,10109715,10006589,10005321,10017510,10115246,10111078,10113851,10012730,10017936,10047286,10115075,10112246,10115358,10116170,10115360,10011011,10016265,10110593,10108092,10092763,10038696,10110407,10002212,10111716,10097121,10111085,10115967,10105596,10097111,10115480,10107038,10089713,10111016,10110871,10092027,10115963,10007537,10109716,10115965,10062416,10115268,10110591,10116157,10116151,10066878,10049068,10113269,10066884,10013813,10115067,10095484,10114902,10092024,10110684,10115761,10115968,10115764,10115756,10111688,10099130,10002013,10115962,10109452,10107805,10006923,10110108,10115119,10103100,10015340,10042809,10116158,10115076,10005327,10115235,10012106,10115988,10001540,10063469,10005802,10116187,10116231,10104034,10097109,10110655,10063570,10110233,10110409,10114720,10066900,10089712,10079824,10075050,10075229,10114471,10079942,10115070,10112292,10116352,10001426,10110646,10006374,10115118,10116180,10001557,10001426,10110365,10115114,10000938,10115759,10115094,10087208,10116108,10006589,10115004,10030016,10116142,10115093,10115230,10107500,10115354,10115205,10090673,10028277,10115082,10114901,10006536,10001426,10114469,10115969,10114753,10115116,10107178,10110406,10079938,10115245,10107120,10001389,10025665,10102230,10109905,10115237,10115078,10114493,10116178,10035511,10001951,10110655,10037578,10076410,10010916,10017510,10115273,10110397,10001559,10107186,10109451,10107039,10029767,10110860,10115096,10115238,10108979,10003326,10115826,10002499,10025189,10012285,10115120,10110232,10106512,10110405,10115361,10097117,10110863,10079797,10110231,10109871,10115259,10103062,10102164,10010127,10076352,10035506,10110820,10075058,10108815,10113535,10049069,10111441,10076305,10111439,10003293,10016260,10090674,10091283,10111088,10011851,10026845,10042708,10011955,10111437,10098381,10090748,10026410,10009987,10108011,10114719,10017295,10107857,10087947,10016544,10110929,10110870,10114934,10016544,10010005,10107803,10076352,10017147,10110874,10101225,10042664,10114930,10110859,10114725,10010131,10114925,10067833,10047249,10107244,10111050,10104792,10106075,10110920,10012287,10107856,10006061,10010129,10016284,10107802,10114017,10091270,10107717,10106610,10092025,10010813,10008974,10108816,10012285,10089713,10107368,10110786,10111087,10034374,10114933,10112271,10109942,10005801,10098381,10111255,10111435,10063452,10110458,10087208,10042656,10010128,10114016,10046647,10017214,10003022,10025195,10092026,10074483,10106063,10043725,10105321,10110679,10011955,10002013)  then 0 \n", "            when manual_update='positive_variance' then b.cp end),0.0) as double) \n", "        total_positive_update_cp,\n", "    coalesce(sum(case when manual_update='positive_variance' and  outlet_id = 2574 and date(pos_timestamp + interval '330' minute) = date'2022-10-12' and b.item_id in (10032150,10079800,10108966,10110057,10107038,10073494,10111018,10115482,10115359,10115991,10115072,10115232,10115083,10115357,10004469,10115264,10111086,10005801,10115365,10037999,10110656,10115993,10115261,10115992,10005621,10034374,10115260,10002324,10011835,10116111,10010529,10115112,10112280,10115113,10116215,10114759,10112278,10006784,10107499,10115081,10079943,10110403,10113533,10115069,10108092,10113534,10113532,10070221,10092025,10115236,10110862,10110671,10012106,10115966,10017936,10116351,10115768,10115762,10108968,10116110,10110677,10014777,10115982,10108978,10107070,10097122,10114966,10003285,10111427,10079866,10114718,10115765,10010005,10116186,10045739,10116179,10115240,10045533,10115364,10110404,10091050,10116308,10066883,10012329,10066058,10112202,10114758,10110541,10110007,10111019,10116107,10110365,10066873,10106943,10005657,10079824,10112290,10087948,10017936,10010816,10115097,10115767,10105783,10109449,10116152,10115610,10116173,10006589,10110072,10115071,10107497,10110562,10104037,10003293,10076353,10035533,10090663,10109402,10109714,10027497,10027495,10106082,10078574,10116177,10110676,10103083,10108973,10108974,10115111,10017397,10115243,10114509,10115095,10109447,10113267,10034558,10115263,10116309,10115117,10012300,10116183,10049068,10115084,10115964,10115758,10110366,10110408,10110868,10063448,10114922,10090663,10115258,10114967,10111345,10108093,10105544,10112463,10115098,10115199,10066874,10116149,10115760,10012339,10112289,10020237,10115253,10107494,10050752,10109448,10115088,10078570,10115363,10003387,10068879,10115231,10115481,10115262,10111019,10110426,10111016,10110976,10105469,10115379,10004491,10092005,10001529,10010898,10115257,10116116,10116185,10107037,10116109,10083600,10083285,10107495,10106590,10016547,10109715,10006589,10005321,10017510,10115246,10111078,10113851,10012730,10017936,10047286,10115075,10112246,10115358,10116170,10115360,10011011,10016265,10110593,10108092,10092763,10038696,10110407,10002212,10111716,10097121,10111085,10115967,10105596,10097111,10115480,10107038,10089713,10111016,10110871,10092027,10115963,10007537,10109716,10115965,10062416,10115268,10110591,10116157,10116151,10066878,10049068,10113269,10066884,10013813,10115067,10095484,10114902,10092024,10110684,10115761,10115968,10115764,10115756,10111688,10099130,10002013,10115962,10109452,10107805,10006923,10110108,10115119,10103100,10015340,10042809,10116158,10115076,10005327,10115235,10012106,10115988,10001540,10063469,10005802,10116187,10116231,10104034,10097109,10110655,10063570,10110233,10110409,10114720,10066900,10089712,10079824,10075050,10075229,10114471,10079942,10115070,10112292,10116352,10001426,10110646,10006374,10115118,10116180,10001557,10001426,10110365,10115114,10000938,10115759,10115094,10087208,10116108,10006589,10115004,10030016,10116142,10115093,10115230,10107500,10115354,10115205,10090673,10028277,10115082,10114901,10006536,10001426,10114469,10115969,10114753,10115116,10107178,10110406,10079938,10115245,10107120,10001389,10025665,10102230,10109905,10115237,10115078,10114493,10116178,10035511,10001951,10110655,10037578,10076410,10010916,10017510,10115273,10110397,10001559,10107186,10109451,10107039,10029767,10110860,10115096,10115238,10108979,10003326,10115826,10002499,10025189,10012285,10115120,10110232,10106512,10110405,10115361,10097117,10110863,10079797,10110231,10109871,10115259,10103062,10102164,10010127,10076352,10035506,10110820,10075058,10108815,10113535,10049069,10111441,10076305,10111439,10003293,10016260,10090674,10091283,10111088,10011851,10026845,10042708,10011955,10111437,10098381,10090748,10026410,10009987,10108011,10114719,10017295,10107857,10087947,10016544,10110929,10110870,10114934,10016544,10010005,10107803,10076352,10017147,10110874,10101225,10042664,10114930,10110859,10114725,10010131,10114925,10067833,10047249,10107244,10111050,10104792,10106075,10110920,10012287,10107856,10006061,10010129,10016284,10107802,10114017,10091270,10107717,10106610,10092025,10010813,10008974,10108816,10012285,10089713,10107368,10110786,10111087,10034374,10114933,10112271,10109942,10005801,10098381,10111255,10111435,10063452,10110458,10087208,10042656,10010128,10114016,10046647,10017214,10003022,10025195,10092026,10074483,10106063,10043725,10105321,10110679,10011955,10002013)  then 0 \n", "            when manual_update='positive_variance' then b.qty end),0) \n", "        total_positive_update_qty,\n", "\n", "    cast(coalesce(sum(case when manual_update='negative_variance' and b.item_id in (10108099,10108100,10108101,10108102,10108103,10108104,10108105,10108106,10108107,10108108,10108109,10108110,10108111,10108112,10108113,10108114)     \n", "                            and date(pos_timestamp + interval '330' minute) between date'2022-10-21' and date'2022-10-23' then 0\n", "                    when manual_update='negative_variance' and b.item_id in (10111049,10031926)     \n", "                             and outlet_id in (3242,3236,2284,3232,3259,3249,3297,3590,2732,2735,2289,2738,1453,2886,3057,2450,3570,3574,2280,3573,1457,2708,1024,1823,2733) and date(pos_timestamp + interval '330' minute) between date'2022-10-20' and date'2022-10-23' then 0\n", "                    when manual_update='negative_variance' or manual_update='154' then b.Total_landing_Price end),0.0) as double) as \n", "        total_negative_update_value,\n", "    cast(coalesce(sum(case when manual_update='negative_variance' and b.item_id in (10108099,10108100,10108101,10108102,10108103,10108104,10108105,10108106,10108107,10108108,10108109,10108110,10108111,10108112,10108113,10108114)     \n", "                            and date(pos_timestamp + interval '330' minute) between  date'2022-10-21' and date'2022-10-23' then 0\n", "                      when manual_update='negative_variance' and b.item_id in (10111049,10031926)     \n", "                             and outlet_id in (3242,3236,2284,3232,3259,3249,3297,3590,2732,2735,2289,2738,1453,2886,3057,2450,3570,3574,2280,3573,1457,2708,1024,1823,2733) and date(pos_timestamp + interval '330' minute) between  date'2022-10-20' and date'2022-10-23' then 0\n", "                     when manual_update='negative_variance' or manual_update='154' then b.cp end),0.0) as double) as \n", "        total_negative_update_cp,\n", "    coalesce(sum(case when manual_update='negative_variance' and b.item_id in (10108099,10108100,10108101,10108102,10108103,10108104,10108105,10108106,10108107,10108108,10108109,10108110,10108111,10108112,10108113,10108114)     \n", "                            and date(pos_timestamp + interval '330' minute) between  date'2022-10-21' and date'2022-10-23' then 0\n", "                      when manual_update='negative_variance' and b.item_id in (10111049,10031926)     \n", "                             and outlet_id in (3242,3236,2284,3232,3259,3249,3297,3590,2732,2735,2289,2738,1453,2886,3057,2450,3570,3574,2280,3573,1457,2708,1024,1823,2733) and date(pos_timestamp + interval '330' minute) between  date'2022-10-20' and date'2022-10-23' then 0\n", "                     when manual_update='negative_variance' or manual_update='154' then b.qty end),0) as \n", "        total_negative_update_qty\n", "    \n", "from base b\n", "where handling_type <> '8'\n", "and b.outlet_id not in (5193, 5124, 5195) -- KS Outlets\n", "group by 1,2,3,4\n", "    \"\"\".format(\n", "        start_date=start_date, end_date=chunk_end_date, day_week_month=day_week_month\n", "    )\n", "    pb.to_trino(sql_query, **kwargs)\n", "    pb.send_slack_message(\n", "        \"U03S75C25B7\",\n", "        \"Dump part MU Table updated for \" + str(start_date) + \" - \" + str(chunk_end_date),\n", "    )\n", "    start_date += timedelta(days=chunk_size.days)"]}, {"cell_type": "code", "execution_count": null, "id": "e231ab92-4b77-4bf1-b293-43518a0065b2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}