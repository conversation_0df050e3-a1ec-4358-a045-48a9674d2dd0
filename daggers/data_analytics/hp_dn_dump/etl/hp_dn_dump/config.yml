alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: hp_dn_dump
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_analytics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U075XSJ2KK2
path: data_analytics/hp_dn_dump/etl/hp_dn_dump
paused: false
pool: data_analytics_pool
project_name: hp_dn_dump
schedule:
  end_date: '2025-09-09T00:00:00'
  interval: 0 2 * * *
  start_date: '2025-01-20T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
