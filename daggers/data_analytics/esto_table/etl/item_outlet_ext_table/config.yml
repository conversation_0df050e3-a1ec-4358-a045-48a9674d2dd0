alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: item_outlet_ext_table
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_analytics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03S75C25B7
path: data_analytics/esto_table/etl/item_outlet_ext_table
paused: false
pool: data_analytics_pool
project_name: esto_table
schedule:
  end_date: '2025-08-08T00:00:00'
  interval: 33 1 * * *
  start_date: '2025-02-13T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
