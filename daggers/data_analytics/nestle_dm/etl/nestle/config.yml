alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: nestle
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_analytics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U08JNRAUJAY
path: data_analytics/nestle_dm/etl/nestle
paused: false
pool: data_analytics_pool
project_name: nestle_dm
schedule:
  end_date: '2025-08-27T00:00:00'
  interval: 53 2 * * *
  start_date: '2025-06-25T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 3
