{"cells": [{"cell_type": "code", "execution_count": null, "id": "cf283f0c-fce2-45e9-a726-26eb0f375127", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "from datetime import date\n", "from datetime import timedelta\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "ad4e9423-7543-4651-9bf6-e15faddd0ed1", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1zVeM_PibPk7D8f_8mLSQx6nYsF8qwmCFbKlbT0ET2zA\"\n", "sheet_name = \"raw\"\n", "dff = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "f5233b27-ea09-4f8a-96cf-f0df5e0681eb", "metadata": {}, "outputs": [], "source": ["# dff = pd.read_csv('DM_PM_Perc_AR - raw.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "2406dc25-40dc-434c-aa37-380f9bf3adc4", "metadata": {}, "outputs": [], "source": ["dff.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b927ac89-a24f-45a1-9c11-6b1d44d8ec6b", "metadata": {}, "outputs": [], "source": ["dff.tail()"]}, {"cell_type": "code", "execution_count": null, "id": "f9c0f41b-60c3-44ea-9faf-4ca4f250e904", "metadata": {}, "outputs": [], "source": ["dff.shape"]}, {"cell_type": "code", "execution_count": null, "id": "fb03d857-923c-4af6-a47d-9d5faefd0cfd", "metadata": {}, "outputs": [], "source": ["dff.columns"]}, {"cell_type": "code", "execution_count": null, "id": "42f989e0-2b98-4a8a-8cb9-28241c9ccf34", "metadata": {}, "outputs": [], "source": ["dff.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "796c26d9-c0d1-4394-9a12-78da52305ede", "metadata": {}, "outputs": [], "source": ["dff[\"Item_id\"] = dff[\"Item_id\"].astype(int)\n", "dff[\"product_description\"] = dff[\"product_description\"].astype(str)\n", "dff[\"dm_perc\"] = dff[\"dm_perc\"].astype(str)\n", "dff[\"pm_perc\"] = dff[\"pm_perc\"].astype(str)\n", "dff[\"dm_split\"] = dff[\"dm_split\"].astype(float)\n", "dff[\"pm_split\"] = dff[\"pm_split\"].astype(float)\n", "dff[\"dm_number\"] = dff[\"dm_number\"].astype(float)\n", "dff[\"pm_number\"] = dff[\"pm_number\"].astype(float)\n", "\n", "# dff[\"start_date\"] = pd.to_datetime(dff[\"start_date\"])"]}, {"cell_type": "code", "execution_count": null, "id": "2e95d5db-a792-4d23-af5a-ad324b94a45e", "metadata": {}, "outputs": [], "source": ["dff.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "e51e700e-4ec1-4f02-9ff2-47fa13f1f864", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"ba_etls\",  # Redshift schema name\n", "    \"table_name\": \"dm_pm_perc\",  # Redshift table name\n", "    \"column_dtypes\": [\n", "        {\"name\": \"Item_id\", \"type\": \"INTEGER\", \"description\": \"date of creation of order\"},\n", "        {\"name\": \"product_description\", \"type\": \"VARCHAR\", \"description\": \"outlet id\"},\n", "        {\"name\": \"dm_perc\", \"type\": \"VARCHAR\", \"description\": \"cart id\"},\n", "        {\"name\": \"pm_perc\", \"type\": \"VARCHAR\", \"description\": \"cart id\"},\n", "        {\"name\": \"dm_split\", \"type\": \"DOUBLE\", \"description\": \"cart id\"},\n", "        {\"name\": \"pm_split\", \"type\": \"DOUBLE\", \"description\": \"cart id\"},\n", "        {\"name\": \"dm_number\", \"type\": \"DOUBLE\", \"description\": \"cart id\"},\n", "        {\"name\": \"pm_number\", \"type\": \"DOUBLE\", \"description\": \"cart id\"},\n", "    ],\n", "    \"primary_key\": [\"Item_id\"],  # list\n", "    \"incremental_key\": [],  # string\n", "    \"load_type\": \"truncate\",  # append, rebuild, truncate or upsert\n", "    \"force_upsert_without_increment_check\": True,\n", "    \"table_description\": \"this table is made temporary data push\",  # feedback\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "30336b83-6524-4995-8b9b-456b4ace0fae", "metadata": {}, "outputs": [], "source": ["pb.to_trino(dff, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}