alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: weekly_cr_dau_conv
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_analytics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U06T92RC7BM
path: data_analytics/store_rca_dashboard/etl/weekly_cr_dau_conv
paused: false
pool: data_analytics_pool
project_name: store_rca_dashboard
schedule:
  end_date: '2025-08-09T00:00:00'
  interval: 5 2 * * *
  start_date: '2024-12-19T00:00:00'
schedule_type: fixed
sla: 121 minutes
support_files: []
tags: []
template_name: notebook
version: 3
