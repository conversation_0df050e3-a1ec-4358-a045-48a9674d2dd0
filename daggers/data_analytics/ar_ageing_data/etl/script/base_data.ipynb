{"cells": [{"cell_type": "code", "execution_count": null, "id": "b90f26aa-4f10-448a-9ab5-ad6d8cee6569", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "from datetime import date\n", "from datetime import timedelta\n", "from datetime import datetime\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\").connect()"]}, {"cell_type": "code", "execution_count": null, "id": "6abf3239-4310-456f-9cd1-e0413a099985", "metadata": {}, "outputs": [], "source": ["# dt = pd.read_csv('invoices_payment_ageing_post_date - raw.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "4f0da21d-de1e-427b-bb68-25119b6108a7", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1VLJJgLw-DK9rUeBYjA6jE0xuCoHxXaKr95HS5i88b8k\"\n", "sheet_name = \"raw\"\n", "dt = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "70f51014-a0f3-4a8e-ab57-37d3848725e0", "metadata": {}, "outputs": [], "source": ["dt.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d0baf0f3-de8e-49d8-8f35-10c509a1fa23", "metadata": {}, "outputs": [], "source": ["start_date = dt.start_date[0]"]}, {"cell_type": "code", "execution_count": null, "id": "cd5defb0-df73-418f-a1f1-808c8acf8dde", "metadata": {}, "outputs": [], "source": ["print(start_date)"]}, {"cell_type": "code", "execution_count": null, "id": "ed373e13-062f-41c5-89e6-663abcc8f50a", "metadata": {}, "outputs": [], "source": ["data = \"\"\"\n", "\n", "\n", "WITH latest_ia AS (\n", "SELECT\n", "invoicenumber,\n", "total_amount_inr,\n", "ROW_NUMBER() OVER(PARTITION BY invoicenumber ORDER BY dt DESC) as rn\n", "FROM\n", "zomato.revenue_etls.blinkit_finance_zerp\n", "WHERE\n", "invoicenumber NOT LIKE 'ZH%%'\n", "AND dt = date_format(current_date - interval '1' day, '%%Y%%m%%d')\n", "AND invoice_date >= date '2024-11-01'\n", ")\n", "SELECT\n", "ag.ecc_sap_code,\n", "ag.customer_name,\n", "ag.brand,\n", "ag.identifier,\n", "ag.mapping,\n", "ag.posting_date,\n", "ag.invoice_no,\n", "ag.balance_due,\n", "0 AS base_amt,\n", "0 AS tax_amt,\n", "0 AS net_payable_amt,\n", "CASE\n", "WHEN ag.identifier <> 'IN' THEN ag.invoice_no\n", "ELSE 'NA'\n", "END AS payment_description,\n", "ag.mid,\n", "ag.income_type\n", "FROM\n", "fin_etls.ageing_dump AS ag\n", "LEFT JOIN\n", "latest_ia AS ia\n", "ON ag.invoice_no = ia.invoicenumber\n", "AND ia.rn = 1\n", "WHERE\n", "ag.mid IS NOT NULL\n", "AND (ia.total_amount_inr > 0 OR ia.total_amount_inr IS NULL)\n", "GROUP BY\n", "1,2,3,4,5,6,7,8,9,10,11,12,13,14\n", "\n", "UNION ALL\n", "\n", "\n", "select sap_code as ecc_sap_code,\n", "'NA' as customer_name,\n", "'NA' as brand,\n", "'RC' as identifier,\n", "'Collection' as mapping,\n", "py.date as posting_date,\n", "'NA' as invoice_no,\n", "(receipt_amount*(-1)) as balance_due,\n", "0 as base_amt,\n", "0 as tax_amt,\n", "0 as net_payable_amt,\n", "py.description as payment_description,\n", "py.mid,\n", "'NA' as income_type\n", "from fin_etls.payments_log_data as py\n", "where py.mid is not null \n", "and date >= date'{start_date}'\n", "\n", "UNION ALL\n", "\n", "select 0 as ecc_sap_code,\n", "merchant as customer_name,\n", "'NA' as brand,\n", "'IN' as identifier,\n", "'Invoice' as mapping,\n", "i.invoice_date as posting_date,\n", "invoicenumber as invoice_no,\n", "invoice_amt as balance_due,\n", "base_amt,\n", "tax_amt,\n", "net_payable_amt,\n", "'NA' as payment_description,\n", "i.merchant_id,\n", "case when i.income_type = 'SI' then 'Search Invoice'\n", "     when i.income_type = 'VI' then 'Visi Invoice'\n", "     else i.income_type end as income_type\n", "from fin_etls.invoices_ageing_data as i\n", "where invoicenumber is not null\n", "and invoicenumber not like 'ZH%%'\n", "and invoice_date >= date'{start_date}'\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14\n", "\n", "\"\"\"\n", "\n", "data = pd.read_sql(data.format(start_date=start_date), con)"]}, {"cell_type": "code", "execution_count": null, "id": "18cc7f59-0fe4-449a-a30d-4e9eeb77f8bd", "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d569274f-890b-4071-888f-8da561d07dc7", "metadata": {}, "outputs": [], "source": ["data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "d422a3ec-d454-42a4-90e6-47b2e38ab26c", "metadata": {}, "outputs": [], "source": ["data[\"posting_date\"] = pd.to_datetime(data[\"posting_date\"], errors=\"coerce\")"]}, {"cell_type": "code", "execution_count": null, "id": "0e3cf87e-bfbb-4acd-8a71-9949e368b797", "metadata": {}, "outputs": [], "source": ["data = data.sort_values(by=[\"mid\", \"posting_date\"], ascending=[\"True\", \"False\"])"]}, {"cell_type": "code", "execution_count": null, "id": "0d417d0d-1937-4a4f-830a-aae16f3c5764", "metadata": {}, "outputs": [], "source": ["data = data[data[\"mid\"] != 0]"]}, {"cell_type": "code", "execution_count": null, "id": "6aa52a9d-bac1-4553-a8b6-f7a4af215c0e", "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "26139177-0e24-424f-943c-aa47355e26b4", "metadata": {}, "outputs": [], "source": ["data.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "f312a95f-2a0a-4d19-a805-483a58689700", "metadata": {}, "outputs": [], "source": ["data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "4ec36773-59ea-4aa7-b490-18d8d8a68dd5", "metadata": {}, "outputs": [], "source": ["W1 = 0.02\n", "W2 = 0.05\n", "W3 = 0.1\n", "W4 = 0.003\n", "\n", "data[\"perc2_wotax\"] = np.where(\n", "    data[\"identifier\"] == \"IN\", data[\"balance_due\"] * (1 - W1), data[\"balance_due\"]\n", ").round(1)\n", "\n", "data[\"perc5_wotax\"] = np.where(\n", "    data[\"identifier\"] == \"IN\", data[\"balance_due\"] * (1 - W2), data[\"balance_due\"]\n", ").round(1)\n", "\n", "data[\"perc10_wotax\"] = np.where(\n", "    data[\"identifier\"] == \"IN\", data[\"balance_due\"] * (1 - W3), data[\"balance_due\"]\n", ").round(1)\n", "\n", "data[\"perc0_3_wotax\"] = np.where(\n", "    data[\"identifier\"] == \"IN\", data[\"balance_due\"] * (1 - W4), data[\"balance_due\"]\n", ").round(1)\n", "\n", "data[\"perc2_tax\"] = np.where(\n", "    data[\"identifier\"] == \"IN\",\n", "    (data[\"balance_due\"] / 1.18) * (1 - W1) + ((data[\"balance_due\"] / 1.18) * 0.18),\n", "    data[\"balance_due\"],\n", ").round(1)\n", "\n", "\n", "data[\"perc5_tax\"] = np.where(\n", "    data[\"identifier\"] == \"IN\",\n", "    (data[\"balance_due\"] / 1.18) * (1 - W2) + ((data[\"balance_due\"] / 1.18) * 0.18),\n", "    data[\"balance_due\"],\n", ").round(1)\n", "\n", "\n", "data[\"perc10_tax\"] = np.where(\n", "    data[\"identifier\"] == \"IN\",\n", "    (data[\"balance_due\"] / 1.18) * (1 - W3) + ((data[\"balance_due\"] / 1.18) * 0.18),\n", "    data[\"balance_due\"],\n", ").round(1)\n", "\n", "\n", "data[\"perc0_3_tax\"] = np.where(\n", "    data[\"identifier\"] == \"IN\",\n", "    (data[\"balance_due\"] / 1.18) * (1 - W4) + ((data[\"balance_due\"] / 1.18) * 0.18),\n", "    data[\"balance_due\"],\n", ").round(1)"]}, {"cell_type": "code", "execution_count": null, "id": "f495eb9c-e5fd-438c-9d7e-3810e61964c5", "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "711d918e-fce8-4be2-bacf-7c300d70f43e", "metadata": {}, "outputs": [], "source": ["data.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "9641949b-40a5-4569-9838-2749d02e71c5", "metadata": {}, "outputs": [], "source": ["data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "cea3c6fe-793a-48c1-aa20-5f925f641662", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"fin_etls\",\n", "    \"table_name\": \"ageing_data_knockoff\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"ecc_sap_code\", \"type\": \"INTEGER\", \"description\": \"date of creation of order\"},\n", "        {\"name\": \"customer_name\", \"type\": \"VARCHAR\", \"description\": \"outlet id\"},\n", "        {\"name\": \"brand\", \"type\": \"VARCHAR\", \"description\": \"outlet id\"},\n", "        {\"name\": \"identifier\", \"type\": \"VARCHAR\", \"description\": \"outlet id\"},\n", "        {\"name\": \"mapping\", \"type\": \"VARCHAR\", \"description\": \"outlet id\"},\n", "        {\"name\": \"posting_date\", \"type\": \"DATE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"invoice_no\", \"type\": \"VARCHAR\", \"description\": \"outlet id\"},\n", "        {\"name\": \"balance_due\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"base_amt\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"tax_amt\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"net_payable_amt\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"payment_description\", \"type\": \"VARCHAR\", \"description\": \"outlet id\"},\n", "        {\"name\": \"mid\", \"type\": \"INTEGER\", \"description\": \"outlet id\"},\n", "        {\"name\": \"income_type\", \"type\": \"VARCHAR\", \"description\": \"outlet id\"},\n", "        {\"name\": \"perc2_wotax\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"perc5_wotax\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"perc10_wotax\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"perc0_3_wotax\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"perc2_tax\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"perc5_tax\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"perc10_tax\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"perc0_3_tax\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "    ],\n", "    \"primary_key\": [\"mid\"],\n", "    \"incremental_key\": [],\n", "    \"load_type\": \"truncate\",\n", "    \"force_upsert_without_increment_check\": True,\n", "    \"table_description\": \"this table is made temporary data push\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "1e1485df-607a-44fd-b50b-c451d1d6a664", "metadata": {}, "outputs": [], "source": ["pb.to_trino(data, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}