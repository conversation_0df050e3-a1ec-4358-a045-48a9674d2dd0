{"cells": [{"cell_type": "code", "execution_count": null, "id": "8a358de0-c7db-4006-bdfe-66cd2780539c", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "from datetime import date\n", "from datetime import timedelta\n", "from datetime import datetime\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\").connect()"]}, {"cell_type": "code", "execution_count": null, "id": "83adf994-3ae6-4c73-95a2-4e51e19e03a1", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1IdXsvJjV5qUvSDNCpQ_9EEOJ2YCcytbpBNO9Viz6Xf0\"\n", "sheet_name = \"ingest\"\n", "data = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "ffb43194-2434-47e3-9516-cbbe1a2cdcdd", "metadata": {}, "outputs": [], "source": ["# data = pd.read_csv('Bank receipt Status 2024-25 - Input_file.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "baa5e16e-ba8f-4f9a-92c5-06138537032e", "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f48fca5c-0bf8-45b0-a7ad-af631a0bb55b", "metadata": {}, "outputs": [], "source": ["data[\"date\"] = pd.to_datetime(data[\"date\"])"]}, {"cell_type": "code", "execution_count": null, "id": "1c5cd4cd-befb-454d-94e7-d04f54dfe83b", "metadata": {}, "outputs": [], "source": ["data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "e22f24cb-eea1-4a6a-88a3-0a800fe15500", "metadata": {}, "outputs": [], "source": ["data[\"ecc_code\"] = data[\"ecc_code\"].astype(int)\n", "data[\"receipt_amount\"] = data[\"receipt_amount\"].astype(int)\n", "data[\"sap_code\"] = data[\"sap_code\"].astype(int)\n", "data[\"mid\"] = data[\"mid\"].astype(int)\n", "data[\"flag\"] = data[\"flag\"].astype(int)\n", "data[\"description\"] = data[\"description\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "70a101a8-df0d-4691-809c-6f7186f11399", "metadata": {}, "outputs": [], "source": ["data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "58beacad-e5d0-472a-bc8e-f1ae830a4f55", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"fin_etls\",\n", "    \"table_name\": \"payments_log_data\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"ecc_code\", \"type\": \"INTEGER\", \"description\": \"date of creation of order\"},\n", "        {\"name\": \"receipt_amount\", \"type\": \"INTEGER\", \"description\": \"outlet id\"},\n", "        {\"name\": \"date\", \"type\": \"DATE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"sap_code\", \"type\": \"INTEGER\", \"description\": \"outlet id\"},\n", "        {\"name\": \"mid\", \"type\": \"INTEGER\", \"description\": \"outlet id\"},\n", "        {\"name\": \"description\", \"type\": \"VARCHAR\", \"description\": \"outlet id\"},\n", "        {\"name\": \"flag\", \"type\": \"INTEGER\", \"description\": \"outlet id\"},\n", "    ],\n", "    \"primary_key\": [\"ecc_code\", \"date\", \"mid\"],\n", "    \"incremental_key\": [],\n", "    \"load_type\": \"truncate\",\n", "    \"force_upsert_without_increment_check\": True,\n", "    \"table_description\": \"this table is made temporary data push\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "5b97664b-b8d4-41f6-bab3-d7d708fd2bf9", "metadata": {}, "outputs": [], "source": ["pb.to_trino(data, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}