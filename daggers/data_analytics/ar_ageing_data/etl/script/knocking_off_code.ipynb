{"cells": [{"cell_type": "code", "execution_count": null, "id": "7601cc89-1e55-4e02-a459-293a55865c8b", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "from datetime import date\n", "from datetime import timedelta\n", "from datetime import datetime\n", "from itertools import combinations\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\").connect()"]}, {"cell_type": "code", "execution_count": null, "id": "ce257618-b557-42de-b69a-e16dddd7bc77", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1TXs5IDwzIUi16DFJJdgx50g0fqEOBNcbp942n6KuXiw\"\n", "sheet_name = \"raw\"\n", "column_mapping = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "3b3093d3-a61b-4f4f-8ed7-fde91cb0ba1a", "metadata": {}, "outputs": [], "source": ["# column_mapping = pd.read_csv('tds_ageing - raw (3).csv')"]}, {"cell_type": "code", "execution_count": null, "id": "4fbbdf04-2ec7-4731-9f5e-3e0584a8275d", "metadata": {}, "outputs": [], "source": ["column_mapping[\"tag\"].fillna(\"na\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "03d62894-7dfb-4813-affe-635f05226f0b", "metadata": {}, "outputs": [], "source": ["column_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "734879f4-e858-42a4-a50b-d14571686c83", "metadata": {}, "outputs": [], "source": ["# mid_column_map = column_mapping.set_index(\"mid\")[\"tax\"].to_dict()"]}, {"cell_type": "code", "execution_count": null, "id": "10f1ad56-7b31-4fbd-a32b-9a2123fdccc3", "metadata": {}, "outputs": [], "source": ["column_mapping.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "200d1065-8319-4118-afa3-bbe6673783c4", "metadata": {}, "outputs": [], "source": ["column_mapping[\"mid\"] = column_mapping[\"mid\"].astype(int)\n", "column_mapping[\"tax\"] = column_mapping[\"tax\"].astype(str)\n", "column_mapping[\"inclusive\"] = column_mapping[\"inclusive\"].astype(int)\n", "column_mapping[\"tag\"] = column_mapping[\"tag\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "7b198e3b-c776-4eeb-926d-91850ba51e10", "metadata": {}, "outputs": [], "source": ["column_mapping.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "098ad2bc-9bea-445d-9217-32084d070784", "metadata": {}, "outputs": [], "source": ["# Extract 'mid' values where tax = -1\n", "excluded_mids = column_mapping.loc[column_mapping[\"inclusive\"] == -1, \"mid\"].tolist()\n", "\n", "# Convert to SQL-friendly string format\n", "excluded_mids_str = \",\".join(map(str, excluded_mids))"]}, {"cell_type": "code", "execution_count": null, "id": "8952313d-4c00-46d1-a621-4c5cbfb499ca", "metadata": {}, "outputs": [], "source": ["excluded_mids"]}, {"cell_type": "code", "execution_count": null, "id": "6c56cb38-7521-441b-a9fc-017546adbd8c", "metadata": {}, "outputs": [], "source": ["data = f\"\"\"\n", "SELECT \n", "    ROW_NUMBER() OVER (PARTITION BY a.mid ORDER BY a.posting_date) AS RowNum, \n", "    a.*\n", "FROM fin_etls.ageing_data_knockoff_post_reduction AS a\n", "WHERE a.mid NOT IN ({excluded_mids_str})\n", "ORDER BY a.mid DESC\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "c527b6f3-5ab6-4b0f-8276-470ab30d54c8", "metadata": {}, "outputs": [], "source": ["data = pd.read_sql(data, con)"]}, {"cell_type": "code", "execution_count": null, "id": "b7da10dc-22ac-4239-a185-4f73d7b656bc", "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3db66369-a1f3-4c74-a7b1-f29d38b47ae1", "metadata": {}, "outputs": [], "source": ["data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "3feddde1-8e5c-4cc0-b624-c13b0849ad29", "metadata": {}, "outputs": [], "source": ["data.columns"]}, {"cell_type": "code", "execution_count": null, "id": "3dd9251e-75be-4576-bb4e-47a80d5e4350", "metadata": {}, "outputs": [], "source": ["# Separate invoices and collections\n", "invoices = data[data[\"mapping\"] == \"Invoice\"].copy()\n", "collections = data[data[\"mapping\"] != \"Invoice\"].copy()"]}, {"cell_type": "code", "execution_count": null, "id": "6ad577a5-3a57-4705-8d6c-4ce5aca9469f", "metadata": {}, "outputs": [], "source": ["# Convert posting dates to datetime\n", "invoices[\"posting_date\"] = pd.to_datetime(invoices[\"posting_date\"])\n", "collections[\"posting_date\"] = pd.to_datetime(collections[\"posting_date\"])"]}, {"cell_type": "code", "execution_count": null, "id": "b3308e02-45a9-4f7f-aad4-80025891e2f3", "metadata": {}, "outputs": [], "source": ["# Initialize logs\n", "results = []\n", "errors = []\n", "\n", "\n", "import pandas as pd  # Make sure pandas is imported in your script\n", "\n", "\n", "def find_invoice_combinations(target_amount, invoices, column, used_invoices):\n", "    # Original: Filter out already used invoices\n", "    invoice_df = invoices[~invoices[\"RowNum\"].isin(used_invoices)].copy()\n", "\n", "    # --- Modification 1: Robust Numeric Conversion START ---\n", "    numeric_column_values = pd.to_numeric(invoice_df[column], errors=\"coerce\")\n", "    invoice_df = invoice_df[numeric_column_values.notna()]\n", "    invoice_df.loc[:, column] = numeric_column_values[numeric_column_values.notna()]\n", "    # --- Modification 1: Robust Numeric Conversion END ---\n", "\n", "    # --- Modification 2: Optimization Filter START ---\n", "    # Only consider invoices whose amount is less than or equal to target_amount + 1.\n", "    invoice_df = invoice_df[invoice_df[column] <= (target_amount + 1)]\n", "    # --- Modification 2: Optimization Filter END ---\n", "\n", "    # Original: Sort the (now potentially much smaller) DataFrame\n", "    invoice_df = invoice_df.sort_values(by=column, ascending=True)\n", "\n", "    # These lines now use the further filtered and sorted invoice_df\n", "    invoice_values = invoice_df[column].tolist()\n", "    row_nums = invoice_df[\"RowNum\"].tolist()\n", "\n", "    best_match = None\n", "    best_subset = []\n", "\n", "    for r in range(1, min(5, len(invoice_values)) + 1):\n", "        for subset in combinations(enumerate(invoice_values), r):\n", "            subset_sum = sum(val for _, val in subset)\n", "\n", "            if abs(subset_sum - target_amount) < 1:  # Original sum tolerance\n", "                best_match = subset_sum\n", "                best_subset = [row_nums[idx] for idx, _ in subset]\n", "                break\n", "        if best_match is not None:\n", "            break\n", "\n", "    if best_subset:\n", "        return invoices[invoices[\"RowNum\"].isin(best_subset)].to_dict(orient=\"records\")\n", "\n", "    return []\n", "\n", "\n", "total_mids = len(invoices[\"mid\"].unique())\n", "processed_mids = 0\n", "\n", "# Process each MID\n", "for mid in invoices[\"mid\"].unique():\n", "    try:\n", "        processed_mids += 1  # ✅ Increment MID progress\n", "        print(f\"\\n🔄 Processing MID {processed_mids} out of {total_mids}: {mid}\")\n", "        print(f\"\\nDatatype is {mid.dtype}\")\n", "\n", "        row = column_mapping[column_mapping[\"mid\"] == mid]\n", "        if not row.empty:\n", "            column_to_check = row.iloc[0][\"tax\"]\n", "        else:\n", "            column_to_check = \"perc2_tax\"\n", "        print(f\"tds col is : {column_to_check}\")\n", "\n", "        used_collections = set()  # ✅ Reset per MID\n", "        used_invoices = set()  # ✅ Reset per MID\n", "        pending_invoices = {}  # ✅ Reset per MID\n", "        used_invoices_in_combo = set()  # ✅ Fix: Initialize before using\n", "\n", "        ecc_collections = collections[collections[\"mid\"] == mid]\n", "        ecc_invoices = invoices[invoices[\"mid\"] == mid]\n", "\n", "        # Determine the tax percentage based on the 'against' column\n", "        tax_percentage = \"\"\n", "        if column_to_check == \"balance_due\":\n", "            tax_percentage = \"0%\"\n", "        elif column_to_check == \"perc2_tax\":\n", "            tax_percentage = \"2%\"\n", "        elif column_to_check == \"perc5_tax\":\n", "            tax_percentage = \"5%\"\n", "        elif column_to_check == \"perc10_tax\":\n", "            tax_percentage = \"10%\"\n", "        elif column_to_check == \"perc0_3_tax\":\n", "            tax_percentage = \"0.3%\"\n", "\n", "        # If no collections, mark all invoices as Not Knocked-off\n", "        if ecc_collections.empty:\n", "            print(f\"No collections for MID: {mid}, marking invoices as Not Knocked-off\")\n", "            for _, invoice in ecc_invoices.iterrows():\n", "                results.append(\n", "                    {\n", "                        \"mid\": mid,\n", "                        \"customer_name\": invoice[\"customer_name\"],\n", "                        \"collection_row\": None,\n", "                        \"collection_description\": None,\n", "                        \"collection_date\": None,\n", "                        \"collection_amount\": None,\n", "                        \"invoice_row\": invoice[\"RowNum\"],\n", "                        \"invoice_number\": invoice[\"invoice_no\"],\n", "                        \"base_amt\": invoice[\"base_amt\"],\n", "                        \"tax_amt\": invoice[\"tax_amt\"],\n", "                        \"income_type\": invoice[\"income_type\"],\n", "                        \"invoice_date\": invoice[\"posting_date\"],\n", "                        \"invoice_amount\": invoice[\"balance_due\"],\n", "                        \"knock_off_amount\": 0,\n", "                        \"perc2_tax\": abs(invoice[\"perc2_tax\"]),\n", "                        \"perc5_tax\": abs(invoice[\"perc5_tax\"]),\n", "                        \"perc0_3_tax\": abs(invoice[\"perc0_3_tax\"]),\n", "                        \"perc10_tax\": abs(invoice[\"perc10_tax\"]),\n", "                        \"status\": \"Not Knocked-off\",\n", "                        \"against\": None,\n", "                        \"tax_percentage\": None,\n", "                        \"remaining_collection\": None,\n", "                        \"remaining_invoice\": invoice[\"balance_due\"],\n", "                        \"channel\": \"No collection\",\n", "                    }\n", "                )\n", "            continue\n", "\n", "        remaining_collections = ecc_collections.copy()\n", "\n", "        # ✅ Step 1: Process all collections for Direct Match\n", "        for _, collection in ecc_collections.iterrows():\n", "            collection_amount = abs(collection[\"balance_due\"])\n", "            remaining_collection = collection_amount\n", "\n", "            eligible_invoices = ecc_invoices[\n", "                ecc_invoices[\"posting_date\"] <= collection[\"posting_date\"]\n", "            ]\n", "            rounded_collection_amt = round(collection_amount, 0)\n", "\n", "            eligible_invoices = eligible_invoices.copy()  # ✅ Add this line before modifying\n", "            eligible_invoices.loc[:, \"column_to_check\"] = (\n", "                eligible_invoices[column_to_check].abs().round(0)\n", "            )\n", "\n", "            direct_match_candidates = (\n", "                eligible_invoices[  # Renamed for clarity, as it's a pool of candidates\n", "                    (~eligible_invoices[\"RowNum\"].isin(used_invoices))\n", "                    & (eligible_invoices[\"column_to_check\"] >= rounded_collection_amt - 1)\n", "                    & (eligible_invoices[\"column_to_check\"] <= rounded_collection_amt + 1)\n", "                ]\n", "            )\n", "\n", "            if not direct_match_candidates.empty:\n", "                # Sort the candidates: oldest first, then by <PERSON><PERSON><PERSON> as a tie-breaker\n", "                direct_match_sorted = direct_match_candidates.sort_values(\n", "                    by=[\"posting_date\", \"RowNum\"], ascending=[True, True]\n", "                )\n", "                invoice = direct_match_sorted.iloc[0]\n", "                print(\n", "                    f\"Direct Match Found: Invoice {invoice['RowNum']} with Amount {invoice[column_to_check]}\"\n", "                )\n", "\n", "                # Determine the tax percentage based on the 'against' column\n", "                tax_percentage = \"\"\n", "                if column_to_check == \"balance_due\":\n", "                    tax_percentage = \"0%\"\n", "                elif column_to_check == \"perc2_tax\":\n", "                    tax_percentage = \"2%\"\n", "                elif column_to_check == \"perc5_tax\":\n", "                    tax_percentage = \"5%\"\n", "                elif column_to_check == \"perc10_tax\":\n", "                    tax_percentage = \"10%\"\n", "                elif column_to_check == \"perc0_3_tax\":\n", "                    tax_percentage = \"0.3%\"\n", "\n", "                results.append(\n", "                    {\n", "                        \"mid\": mid,\n", "                        \"customer_name\": invoice[\"customer_name\"],\n", "                        \"collection_row\": collection[\"RowNum\"],\n", "                        \"collection_description\": collection[\"payment_description\"],\n", "                        \"collection_date\": collection[\"posting_date\"],\n", "                        \"collection_amount\": collection[\"balance_due\"],\n", "                        \"invoice_row\": invoice[\"RowNum\"],\n", "                        \"invoice_number\": invoice[\"invoice_no\"],\n", "                        \"base_amt\": invoice[\"base_amt\"],\n", "                        \"tax_amt\": invoice[\"tax_amt\"],\n", "                        \"income_type\": invoice[\"income_type\"],\n", "                        \"invoice_date\": invoice[\"posting_date\"],\n", "                        \"invoice_amount\": invoice[\"balance_due\"],\n", "                        \"knock_off_amount\": 0,\n", "                        \"perc2_tax\": abs(invoice[\"perc2_tax\"]),\n", "                        \"perc5_tax\": abs(invoice[\"perc5_tax\"]),\n", "                        \"perc0_3_tax\": abs(invoice[\"perc0_3_tax\"]),\n", "                        \"perc10_tax\": abs(invoice[\"perc10_tax\"]),\n", "                        \"status\": \"Direct Knock-off (One-to-One)\",\n", "                        \"against\": column_to_check,\n", "                        \"tax_percentage\": tax_percentage,\n", "                        \"remaining_collection\": 0,\n", "                        \"remaining_invoice\": 0,\n", "                        \"channel\": \"direct\",\n", "                    }\n", "                )\n", "\n", "                used_invoices.add(invoice[\"RowNum\"])\n", "                used_collections.add(collection[\"RowNum\"])\n", "                remaining_collections = remaining_collections[\n", "                    remaining_collections[\"RowNum\"] != collection[\"RowNum\"]\n", "                ]\n", "\n", "        print(f\"✅ Completed Direct Matches for MID: {mid}\")\n", "\n", "        remaining_collections = remaining_collections.sort_values(\n", "            by=\"RowNum\", ascending=True\n", "        ).copy()\n", "\n", "        # ✅ Step 2: Process remaining collections for Combination Match\n", "        for _, collection in remaining_collections.iterrows():\n", "            print(f\"✅ Entering in combo match for MID: {mid}\")\n", "            collection_amount = abs(collection[\"balance_due\"])\n", "\n", "            eligible_invoices = ecc_invoices[\n", "                ecc_invoices[\"posting_date\"] <= collection[\"posting_date\"]\n", "            ]\n", "\n", "            print(\n", "                f\"📌 Checking Combination Match for Collection RowNum: {collection['RowNum']}, Amount: {collection['balance_due']}\"\n", "            )\n", "\n", "            combo_match = find_invoice_combinations(\n", "                collection_amount, eligible_invoices, column_to_check, used_invoices\n", "            )  # 🔥 Use dynamic column\n", "\n", "            if combo_match:\n", "                matched_invoice_ids = [invoice[\"RowNum\"] for invoice in combo_match]\n", "                print(\n", "                    f\"🔗 Matched Invoices: {matched_invoice_ids}\"\n", "                )  # ✅ Shows matched invoice RowNums\n", "                print(f\"Combination Match Found: {len(combo_match)} invoices matched.\")\n", "                for invoice in combo_match:\n", "                    if invoice[\"RowNum\"] in used_invoices_in_combo:\n", "                        continue  # ✅ <PERSON><PERSON> already used invoices in combo match\n", "\n", "                    # Determine the tax percentage based on the 'against' column\n", "                    tax_percentage = \"\"\n", "                    if column_to_check == \"balance_due\":\n", "                        tax_percentage = \"0%\"\n", "                    elif column_to_check == \"perc2_tax\":\n", "                        tax_percentage = \"2%\"\n", "                    elif column_to_check == \"perc5_tax\":\n", "                        tax_percentage = \"5%\"\n", "                    elif column_to_check == \"perc10_tax\":\n", "                        tax_percentage = \"10%\"\n", "                    elif column_to_check == \"perc0_3_tax\":\n", "                        tax_percentage = \"0.3%\"\n", "\n", "                    results.append(\n", "                        {\n", "                            \"mid\": mid,\n", "                            \"customer_name\": invoice[\"customer_name\"],\n", "                            \"collection_row\": collection[\"RowNum\"],\n", "                            \"collection_description\": collection[\"payment_description\"],\n", "                            \"collection_date\": collection[\"posting_date\"],\n", "                            \"collection_amount\": collection[\"balance_due\"],\n", "                            \"invoice_row\": invoice[\"RowNum\"],\n", "                            \"invoice_number\": invoice[\"invoice_no\"],\n", "                            \"base_amt\": invoice[\"base_amt\"],\n", "                            \"tax_amt\": invoice[\"tax_amt\"],\n", "                            \"income_type\": invoice[\"income_type\"],\n", "                            \"invoice_date\": invoice[\"posting_date\"],\n", "                            \"invoice_amount\": invoice[\"balance_due\"],\n", "                            \"knock_off_amount\": 0,\n", "                            \"perc2_tax\": abs(invoice[\"perc2_tax\"]),\n", "                            \"perc5_tax\": abs(invoice[\"perc5_tax\"]),\n", "                            \"perc0_3_tax\": abs(invoice[\"perc0_3_tax\"]),\n", "                            \"perc10_tax\": abs(invoice[\"perc10_tax\"]),\n", "                            \"status\": \"Combination Knock-off\",\n", "                            \"against\": column_to_check,\n", "                            \"tax_percentage\": tax_percentage,\n", "                            \"remaining_collection\": 0,\n", "                            \"remaining_invoice\": 0,\n", "                            \"channel\": \"combo\",\n", "                        }\n", "                    )\n", "\n", "                    used_invoices_in_combo.add(\n", "                        invoice[\"RowNum\"]\n", "                    )  # ✅ Mark invoice as used in combination match\n", "\n", "                used_collections.add(collection[\"RowNum\"])  # ✅ Mark collection as used\n", "                remaining_collections = remaining_collections[\n", "                    remaining_collections[\"RowNum\"] != collection[\"RowNum\"]\n", "                ]\n", "                used_invoices.update(used_invoices_in_combo)\n", "\n", "        print(f\"\\n🔄 Entering FIFO Processing for MID: {mid}\")\n", "        # print(f\"📌 Remaining Collections for FIFO: {remaining_collections}\")\n", "\n", "        # ✅ Step 3: Process remaining collections for FIFO Knock-off\n", "        for _, collection in remaining_collections.iterrows():\n", "            collection_amount = abs(collection[\"balance_due\"])\n", "            remaining_collection = collection_amount\n", "\n", "            # ✅ Exclude invoices that were already used in Direct or Combo match\n", "            eligible_invoices = ecc_invoices[\n", "                (ecc_invoices[\"posting_date\"] <= collection[\"posting_date\"])\n", "                & (~ecc_invoices[\"RowNum\"].isin(used_invoices))\n", "                & (  # ✅ Exclude Direct Match invoices\n", "                    ~ecc_invoices[\"RowNum\"].isin(used_invoices_in_combo)\n", "                )  # ✅ Exclude Combo Match invoices\n", "            ].sort_values(\n", "                by=[\"posting_date\", \"RowNum\"], ascending=[True, True]\n", "            )  # FIFOfix\n", "\n", "            print(\"Attempting FIFO Knock-off...\")\n", "            for _, invoice in eligible_invoices.iterrows():\n", "                if collection[\"RowNum\"] in used_collections:\n", "                    continue  # ✅ Skip already processed collections\n", "\n", "                invoice_balance = pending_invoices.get(\n", "                    invoice[\"RowNum\"], abs(invoice[column_to_check])\n", "                )  # 🔥 Dynamic column\n", "                full_invoice_balance = abs(invoice[column_to_check])  # 🔥 Dynamic column\n", "\n", "                knockoff_amount = min(invoice_balance, remaining_collection)\n", "\n", "                # Determine the tax percentage based on the 'against' column\n", "                tax_percentage = \"\"\n", "                if column_to_check == \"balance_due\":\n", "                    tax_percentage = \"0%\"\n", "                elif column_to_check == \"perc2_tax\":\n", "                    tax_percentage = \"2%\"\n", "                elif column_to_check == \"perc5_tax\":\n", "                    tax_percentage = \"5%\"\n", "                elif column_to_check == \"perc10_tax\":\n", "                    tax_percentage = \"10%\"\n", "                elif column_to_check == \"perc0_3_tax\":\n", "                    tax_percentage = \"0.3%\"\n", "\n", "                results.append(\n", "                    {\n", "                        \"mid\": mid,\n", "                        \"customer_name\": invoice[\"customer_name\"],\n", "                        \"collection_row\": collection[\"RowNum\"],\n", "                        \"collection_description\": collection[\"payment_description\"],\n", "                        \"collection_date\": collection[\"posting_date\"],\n", "                        \"collection_amount\": collection[\"balance_due\"],\n", "                        \"invoice_row\": invoice[\"RowNum\"],\n", "                        \"invoice_number\": invoice[\"invoice_no\"],\n", "                        \"base_amt\": invoice[\"base_amt\"],\n", "                        \"tax_amt\": invoice[\"tax_amt\"],\n", "                        \"income_type\": invoice[\"income_type\"],\n", "                        \"invoice_date\": invoice[\"posting_date\"],\n", "                        \"invoice_amount\": invoice[\"balance_due\"],\n", "                        \"knock_off_amount\": knockoff_amount,  # ✅ Only the knocked-off amount\n", "                        \"perc2_tax\": abs(invoice[\"perc2_tax\"]),\n", "                        \"perc5_tax\": abs(invoice[\"perc5_tax\"]),\n", "                        \"perc0_3_tax\": abs(invoice[\"perc0_3_tax\"]),\n", "                        \"perc10_tax\": abs(invoice[\"perc10_tax\"]),\n", "                        \"full_invoice_amount\": full_invoice_balance,  # ✅ Reference to original invoice amount\n", "                        \"status\": (\n", "                            \"FIFO - Partial Knock-off\"\n", "                            if invoice_balance > knockoff_amount + 5  # tolerance\n", "                            else \"FIFO - Complete Knock-off\"\n", "                        ),\n", "                        \"against\": \"FIFO\",\n", "                        \"tax_percentage\": tax_percentage,\n", "                        \"remaining_collection\": remaining_collection - knockoff_amount,\n", "                        \"remaining_invoice\": max(0, invoice_balance - knockoff_amount),\n", "                        \"channel\": \"fifo\",\n", "                    }\n", "                )\n", "\n", "                invoice_balance -= knockoff_amount\n", "                remaining_collection -= knockoff_amount\n", "\n", "                if invoice_balance > 5:  # tolerance\n", "                    pending_invoices[invoice[\"RowNum\"]] = invoice_balance\n", "                else:\n", "                    used_invoices.add(\n", "                        invoice[\"RowNum\"]\n", "                    )  # ✅ Ensure invoice is marked as fully used\n", "\n", "                if remaining_collection <= 5:  # tolerance\n", "                    break  # ✅ Stop if collection is fully used\n", "\n", "    except Exception as e:\n", "        errors.append({\"mid\": mid, \"error\": str(e)})\n", "\n", "# Convert results to DataFrame\n", "results_df = pd.DataFrame(results)\n", "\n", "print(f\"Total Errors Encountered: {len(errors)}\")\n", "if errors:\n", "    print(errors)\n", "\n", "results_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9341a735-5513-42aa-b6c6-17643f67eadc", "metadata": {}, "outputs": [], "source": ["results_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "d54cf9b6-1fd5-4bb3-a23f-52d0afae27d0", "metadata": {}, "outputs": [], "source": ["# results_df.to_csv('data_new_t.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "f7d5ea78-430c-441e-83fc-0ac482325dd5", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1LdXZFWAQD9BJiblotK5LDcDjexI_t5P2HMSUYipEpk4\"\n", "sheet_name = \"knockoff\"\n", "pb.to_sheets(results_df, sheet_id, sheet_name)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}