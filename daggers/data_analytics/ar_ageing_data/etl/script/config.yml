alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 3
dag_name: script
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_analytics
notebooks:
- alias: ageing_upload
  executor_config:
    load_type: tiny
    node_type: spot
  name: ageing_upload
  parameters: null
  tag: group01
- alias: cat_lead
  executor_config:
    load_type: tiny
    node_type: spot
  name: cat_lead
  parameters: null
  tag: group01
- alias: hashed_pans
  executor_config:
    load_type: tiny
    node_type: spot
  name: hashed_pans
  parameters: null
  tag: group01
- alias: payment_file
  executor_config:
    load_type: tiny
    node_type: spot
  name: payment_file
  parameters: null
  tag: group01
- alias: adv_to_mid
  executor_config:
    load_type: tiny
    node_type: spot
  name: adv_to_mid
  parameters: null
  tag: group01
- alias: final_adv_mf_mapping
  executor_config:
    load_type: tiny
    node_type: spot
  name: final_adv_mf_mapping
  parameters: null
  tag: group02
- alias: invoice
  executor_config:
    load_type: tiny
    node_type: spot
  name: invoice
  parameters: null
  tag: group03
- alias: base_data
  executor_config:
    load_type: tiny
    node_type: spot
  name: base_data
  parameters: null
  tag: group04
- alias: base_file_output
  executor_config:
    load_type: tiny
    node_type: spot
  name: base_file_output
  parameters: null
  tag: group05
- alias: base_file_output_post_reduction
  executor_config:
    load_type: tiny
    node_type: spot
  name: base_file_output_post_reduction
  parameters: null
  tag: group06
- alias: knocking_off_code
  executor_config:
    load_type: tiny
    node_type: spot
  name: knocking_off_code
  parameters: null
  tag: group07
owner:
  email: <EMAIL>
  slack_id: U075XSJ2KK2
path: data_analytics/ar_ageing_data/etl/script
paused: false
pool: data_analytics_pool
project_name: ar_ageing_data
schedule:
  end_date: '2025-09-10T00:00:00'
  interval: 30 1 * * *
  start_date: '2025-06-17T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 14
