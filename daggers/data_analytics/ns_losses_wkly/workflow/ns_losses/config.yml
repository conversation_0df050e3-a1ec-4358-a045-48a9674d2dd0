alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: ns_losses
dag_type: workflow
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_analytics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U082AL2HQDT
path: data_analytics/ns_losses_wkly/workflow/ns_losses
paused: false
pool: data_analytics_pool
project_name: ns_losses_wkly
schedule:
  end_date: '2025-08-07T00:00:00'
  interval: 0 3 * * 1
  start_date: '2025-02-10T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 5
