alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: temp
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_analytics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U08K3R08XSQ
path: data_analytics/fin_bistro/etl/temp
paused: false
pool: data_analytics_pool
project_name: fin_bistro
schedule:
  end_date: '2025-09-17T00:00:00'
  interval: 0 1 * * *
  start_date: '2025-06-19T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
