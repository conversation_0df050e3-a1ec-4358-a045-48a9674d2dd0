{"cells": [{"cell_type": "code", "execution_count": null, "id": "0f7a7504-6b8b-4348-8826-9aafd30dba32", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import pencilbox as pb\n", "import numpy as np\n", "import itertools\n", "import datetime as dt\n", "from datetime import datetime\n", "from datetime import date\n", "from datetime import timedelta\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "bdbccc9f-14e3-4457-8139-29fdbbcead05", "metadata": {}, "outputs": [], "source": ["q0 = \"\"\"\n", "\n", "with pid_base as (\n", "select\n", "id as product_id\n", "from cms.gr_product\n", "where priority = 'bistro'\n", "group by 1\n", ")\n", "\n", "\n", "select\n", "date(cast(current_timestamp as timestamp)) as dt,\n", "cast(current_timestamp as timestamp) as snapshot_ist,\n", "cast(merchant_id as int) as merchant_id,\n", "cast(a.product_id as int) as product_id,\n", "cast(enabled as varchar) as enabled,\n", "a.updated_at,\n", "a.reference_type,\n", "a.reference_id,\n", "case when quantity is null then 0 else quantity end as quantity\n", "\n", "from dynamodb.blinkit_projection_inventory_service_rt_view a\n", "join pid_base b on a.product_id=cast(b.product_id as varchar)\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "e321783c-4565-43db-91fe-e0164366ff68", "metadata": {}, "outputs": [], "source": ["df = pd.read_sql(q0, con)\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "70e73e82-1531-4cf2-98f7-5eb4f0a60ef4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}