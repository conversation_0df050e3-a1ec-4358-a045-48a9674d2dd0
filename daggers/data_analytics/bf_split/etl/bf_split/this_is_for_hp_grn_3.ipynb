{"cells": [{"cell_type": "code", "execution_count": null, "id": "716ddc89-e808-47c4-a446-47c376e838aa", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "from datetime import date\n", "from datetime import timedelta\n", "from datetime import datetime\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\").connect()"]}, {"cell_type": "code", "execution_count": null, "id": "1685505f-a659-4632-9f5a-c34b3a8c8575", "metadata": {}, "outputs": [], "source": ["Start_date = \"2025-05-01\"\n", "End_date = \"2025-05-31\""]}, {"cell_type": "code", "execution_count": null, "id": "5ca3b960-0b7c-4d2b-8802-9ee8d257cb63", "metadata": {}, "outputs": [], "source": ["# data = \"\"\"\n", "\n", "\n", "# with user_data as (\n", "#     select po_id , max(case when new_po_status = 'CREATED' then u.name  end) as created_by,\n", "#     max(case when new_po_status = 'APPROVED' then u.name end) as approved_by,\n", "#     max(case when new_po_status = 'COMPLETED' then u.name end) as completed_by\n", "#     from (select po_id, new_po_status , updated_by_user_id , row_number() over (partition by new_po_status , po_id  order by id desc) as row\n", "#     from zomato.hp_wms.purchase_order_status_history\n", "#     where dt > '20240301' and new_po_status in ('CREATED','COMPLETED' , 'APPROVED')) a\n", "#     join zomato.hp_users.users u on u.id = a.updated_by_user_id\n", "#     where row = 1 group by 1\n", "# ),\n", "\n", "# request as (\n", "#     select distinct date(a.created_at),\n", "#     partner_order_id,\n", "#     b.vendor_name\n", "#     from po.edi_integration_partner_purchase_order_details as p\n", "#     inner join po.purchase_order_items as a on p.po_id = a.po_id\n", "#                                             and date(a.created_at) between date('{{Start_date}}') and date('{{End_date}}')\n", "#     inner join po.purchase_order as b on a.po_id = b.id\n", "#                                       and b.vendor_name = 'Zomato Hyperpure Private Limited'\n", "# ),\n", "\n", "# po as (\n", "# select distinct po_number from (\n", "#     select ref_purchase_order_number as po_number, r.partner_order_id, z.dt,  r.vendor_name\n", "#     from zomato.hp_wms.orders as z\n", "#     inner join zomato.hp_consumer.buyer_order_requests as a on z.id = a.orderid\n", "#                                                             and a.dt >= cast(date_format(DATE '{{Start_date}}', '%Y%m%d') as varchar)\n", "#     inner join request as r on r.partner_order_id = a.id\n", "#     where z.dt>= cast(date_format(DATE '{{Start_date}}', '%Y%m%d') as varchar)\n", "#     and z.order_tag in ('DIRECT_DELIVERY', 'MANDI_DD')\n", "\n", "# union\n", "\n", "#     select distinct purchase_order_number as po_number, r.partner_order_id, z.dt,  r.vendor_name\n", "#     from zomato.hp_wms.purchase_order as z\n", "#     inner join zomato.hp_wms.bin_inventory_logs as a on z.id = a.source_po\n", "#                                                      and a.reason_code = 'ORDER_CONSUME'\n", "#                                                      and a.dt >= cast(date_format(DATE '{{Start_date}}', '%Y%m%d') as varchar)\n", "#     inner join zomato.hp_wms.orders as b on a.destination_entity_id =b.order_number\n", "#                                          and b.dt >= cast(date_format(DATE '{{Start_date}}', '%Y%m%d') as varchar)\n", "#                                          and b.order_tag not in ('DIRECT_DELIVERY', '<PERSON>ND<PERSON>_DD')\n", "#     inner join zomato.hp_consumer.buyer_order_requests as c on b.id = c.orderid\n", "#                                                             and c.dt >= cast(date_format(DATE '{{Start_date}}', '%Y%m%d') as varchar)\n", "#     inner join request as r on r.partner_order_id = c.id\n", "#     where z.dt >= cast(date_format(DATE '{{Start_date}}', '%Y%m%d') as varchar)\n", "#     )\n", "# )\n", "# -- select * from po\n", "\n", "# ,grn_comp as (\n", "#     select po_id,\n", "#     max(case when new_po_status = 'COMPLETED' then u.name end) as completed_by\n", "#     from (\n", "#         select po_grn_mapping_id po_id,\n", "#         new_grn_status new_po_status,\n", "#         history_created_by updated_by_user_id,\n", "#         row_number() over (partition by new_grn_status , po_grn_mapping_id  order by id desc) as row\n", "#         from zomato.hp_wms.po_grn_mapping_status_history\n", "#         where dt >= cast(date_format(DATE '{{Start_date}}', '%Y%m%d') as varchar)\n", "#         and new_grn_status in ('COMPLETED') ) a\n", "#         join zomato.hp_users.users u on u.id = a.updated_by_user_id\n", "#         where row = 1\n", "#         group by 1\n", "# ),\n", "\n", "\n", "# map as (\n", "# select *,count(1) over (partition by product_id) cnt from\n", "#     (select\n", "#     om.product_id,id.item_id,id.name,p.product_name,avg(om.avg_selling_price_ratio) avg_selling_price_ratio,avg(multiplier) multiplier\n", "#     from dwh.dim_item_product_offer_mapping om\n", "#     join lake_rpc.item_details id on om.item_id=id.item_id\n", "#     join dwh.dim_product p on p.product_id=om.product_Id and p.is_current\n", "#     where om.is_current\n", "#     group by 1,2,3,4)\n", "# ),\n", "\n", "# manu as (\n", "#     select * from\n", "#     (\n", "#     select\n", "#     manufacturer_id,\n", "#     manufacturer_name,\n", "#     row_number() over (partition by manufacturer_id order by updated_at desc)  rn\n", "#     from po.purchase_order_items\n", "#     )\n", "#     where rn=1\n", "# ),\n", "\n", "# manu_map as (\n", "#     select m.*,id.manufacturer_id,mn.manufacturer_name\n", "#     from map m\n", "#     left join lake_rpc.item_details id on m.item_id=id.item_id\n", "#     left join manu mn on mn.manufacturer_id=id.manufacturer_id\n", "# ),\n", "\n", "# final as (\n", "#     select *\n", "#     from (select\n", "#         hp_wms.purchase_order.id as PurchaseOrderID,\n", "#         hp_wms.purchase_order.purchase_order_number as PurchaseOrderNumber,\n", "#         hp_wms.purchase_order.vendor_id as VendorID,\n", "#         sl.sap_vendor_code as sap_vendor_code ,\n", "#         sl.credit_period as credit_period ,\n", "#         hp_wms.po_grn_mapping.warehouse_code as WarehouseCode,\n", "#         wms_city.city_name as City,\n", "#         cl.name as city_blinkit,\n", "#         hp_wms.purchase_order_items.product_number as ProductNumber,\n", "#         product_name as ProductName,\n", "#         c.name as ParentCategoryName,\n", "#         grn_price_per_unit as PricePerUnit,\n", "#         quantity_ordered as QuantityOrdered,\n", "#         hp_wms.po_grn_item.delivered_quantity as QuantityDelivered,\n", "#         hp_wms.po_grn_item.sellable_quantity as SellableQuantity,\n", "#         A.grn_completion_date as CompletedDateTime,\n", "#         hp_wms.purchase_order_items.buy_unit as buy_unit,\n", "#         hp_wms.purchase_order_items.sell_unit as sell_unit,\n", "#         hp_wms.po_grn_mapping.created_at + interval '330' minute as OrderCreationDateTime,\n", "#         hp_wms.po_grn_mapping.grn_status as Status,\n", "#         hp_wms.seller_outlet.outlet_name as vendor_name,\n", "#         p.weight_per_packet as WeightPerPacket ,\n", "#         p.sub_uom as UoM,\n", "#         ' ' as DiscrepancyReason ,\n", "#         hp_wms.po_grn_mapping.grn_number as GRN_Number,\n", "#         hp_wms.po_grn_mapping.grn_amount  as \"GRN/PO_Value\" ,\n", "#         hp_wms.po_grn_mapping.supplier_bill_amount as TotalSupplierBillAmount,\n", "#         hp_wms.po_grn_mapping.supplier_bill_number as SupplierBillNumber,\n", "#         hp_wms.purchase_order_items.cess_rate as Cess_Rate ,\n", "#         hp_wms.purchase_order_items.igst_rate as IGST_Rate,\n", "#         hp_wms.purchase_order_items.cgst_rate as CGST_Rate,\n", "#         hp_wms.purchase_order_items.sgst_rate as SGST_Rate,\n", "#         sl.gstin as G<PERSON><PERSON>,\n", "#         p.hsn_code as HSN_Code,\n", "#         hp_wms.po_grn_item.delivered_quantity * grn_price_per_unit as PreTax_Value,\n", "#         hp_wms.po_grn_item.delivered_quantity * grn_price_per_unit * hp_wms.purchase_order_items.cess_rate / 100 as cess_amount,\n", "#         hp_wms.po_grn_item.delivered_quantity * grn_price_per_unit * hp_wms.purchase_order_items.sgst_rate / 100 as sgst_amount,\n", "#         hp_wms.po_grn_item.delivered_quantity * grn_price_per_unit * hp_wms.purchase_order_items.cgst_rate / 100 as cgst_amount,\n", "#         hp_wms.po_grn_item.delivered_quantity * grn_price_per_unit * hp_wms.purchase_order_items.igst_rate / 100 as igst_amount,\n", "#         (hp_wms.po_grn_item.delivered_quantity * grn_price_per_unit * hp_wms.purchase_order_items.gst_rate / 100)+ (hp_wms.po_grn_item.delivered_quantity * grn_price_per_unit * hp_wms.purchase_order_items.cess_rate / 100) as GST_amount,\n", "#         ' '  as BO_Number,u1.created_by , u1.approved_by , u2.completed_by ,\n", "#         po_amount , hp_wms.po_grn_mapping.grn_amount, payment_amount  , payment_date  , payment_number , po_payment_status--, pp.vendor_name as blinkit_vendor_name\n", "\n", "#         from zomato.hp_wms.po_grn_mapping\n", "#       inner join zomato.hp_wms.purchase_order on hp_wms.purchase_order.id = hp_wms.po_grn_mapping.purchase_order_id\n", "#       inner join zomato.hp_wms.po_grn_item on hp_wms.po_grn_mapping.id = hp_wms.po_grn_item.po_grn_mapping_id\n", "#       inner join zomato.hp_wms.purchase_order_items on hp_wms.po_grn_item.purchase_order_item_id = hp_wms.purchase_order_items.id\n", "#       inner join zomato.hp_wms.warehouse wms_warehouse on hp_wms.purchase_order.warehouse_code = wms_warehouse.warehouse_code\n", "#       inner join zomato.hp_wms.city wms_city on wms_warehouse.city_id = wms_city.id\n", "#       inner join po as pp on pp.po_number = hp_wms.purchase_order.purchase_order_number\n", "#       left join zomato.hp_wms.purchase_order_payment pop on pop.id = hp_wms.po_grn_mapping.purchase_order_payment_id and pop.dt > '20240301'\n", "#       left join zomato.hp_wms.seller_outlet sl ON  hp_wms.purchase_order.vendor_id = sl.id and sl.dt > '0'\n", "#       left join user_data u1 on u1.po_id = hp_wms.purchase_order.id\n", "#       left join grn_comp u2 on u2.po_id = hp_wms.po_grn_mapping.id\n", "#       left join po.edi_integration_partner_outlet_mapping ipom ON lower(ipom.partner_outlet_id) = lower(wms_warehouse.warehouse_code) AND ipom.active\n", "#       left join retail.console_outlet co ON co.id = ipom.outlet_id and co.lake_active_record and co.active = 1\n", "#       LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id and cl.lake_active_record\n", "#       inner join (\n", "#         select distinct po_grn_mapping_id,\n", "#           min(history_created_at + interval '330' minute) as grn_completion_date\n", "#         from zomato.hp_wms.po_grn_mapping_history\n", "#         where grn_status = 'COMPLETED' and zomato.hp_wms.po_grn_mapping_history.dt >= '20240301'\n", "#         group by po_grn_mapping_id ) A on zomato.hp_wms.po_grn_mapping.id = A.po_grn_mapping_id\n", "\n", "#           left JOIN zomato.hp_wms.product AS \"p\" ON zomato.hp_wms.purchase_order_items.product_id = p.id\n", "#           left JOIN zomato.hp_wms.category AS \"c\" ON p.parent_category_id = c.ID --left join hp_wms.purchase_order_payment on hp_wms.po_grn_mapping.purchase_order_payment_id = hp_wms.purchase_order_payment.id\n", "#           left JOIN zomato.hp_wms.seller_outlet ON zomato.hp_wms.purchase_order.vendor_id = zomato.hp_wms.seller_outlet.id and zomato.hp_wms.seller_outlet.dt >= '20180101'\n", "#           where\n", "#           zomato.hp_wms.po_grn_mapping.dt >= '20240301'\n", "#           and zomato.hp_wms.purchase_order.dt >='20240301'\n", "#           and zomato.hp_wms.po_grn_item.dt >= '20240301'\n", "#           and zomato.hp_wms.purchase_order_items.dt >= '20240301'\n", "#           order by zomato.hp_wms.po_grn_mapping.purchase_order_id desc, purchase_order_item_id\n", "#   ) final\n", "#   where CompletedDateTime >= date'{{Start_date}}'\n", "#   and CompletedDateTime <= date'{{End_date}}'\n", "# ),\n", "\n", "\n", "# main as (\n", "# select VendorID,\n", "# sap_vendor_code,\n", "# PurchaseOrderNumber ,\n", "# OrderCreationDateTime,\n", "# Status,\n", "# vendor_name,\n", "# credit_period,\n", "# WarehouseCode,\n", "# GRN_Number,\n", "# CompletedDateTime,\n", "# ap.created_by,\n", "# ap.approved_by,\n", "# ap.completed_by,\n", "# array_join(array_agg(distinct ParentCategoryName), ',') as category_name,\n", "# pim.item_id as blinkit_item_id,\n", "# manufacturer_id,\n", "# manufacturer_name,\n", "# City,\n", "# city_blinkit,\n", "# -- blinkit_vendor_name,\n", "# sum(PreTax_Value) as grnbasic_amount,\n", "# sum(cess_amount) as cess_amount,\n", "# sum(sgst_amount) as sgst_amount,\n", "# sum(cgst_amount) as cgst_amount,\n", "# sum(igst_amount) as igst_amount,\n", "# sum(GST_amount) as GST_amount,\n", "# payment_number,\n", "# po_payment_status\n", "\n", "# from final as ap\n", "# left join po.edi_integration_partner_item_mapping pim on cast(ap.ProductNumber as varchar) = pim.partner_item_id\n", "# left join manu_map mm on mm.item_id = pim.item_id\n", "# group by VendorID , sap_vendor_code , PurchaseOrderNumber ,OrderCreationDateTime, Status, vendor_name , credit_period , WarehouseCode , GRN_Number ,CompletedDateTime ,\n", "# ap.created_by , ap.approved_by ,ap.completed_by , payment_number , po_payment_status, pim.item_id, manufacturer_id, manufacturer_name, City,  city_blinkit --blinkit_vendor_name,\n", "# )\n", "\n", "\n", "# , map_base as (\n", "# select distinct\n", "# warehouse_code,buyer_outlet_id,buyer_outlet_name,\n", "# cl.name as Warehouse_city,\n", "# cb.name as outlet_city\n", "# from zomato.hyperpure_etls.hyperpure_order_history as a\n", "\n", "# left join po.edi_integration_partner_outlet_mapping ipom ON lower(ipom.partner_outlet_id) = lower(a.warehouse_code) AND ipom.active\n", "# left join retail.console_outlet co ON co.id = ipom.outlet_id and co.lake_active_record and co.active = 1\n", "# LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id and cl.lake_active_record\n", "# left join retail.console_outlet ca ON lower(ca.name) = lower(a.buyer_outlet_name) and ca.lake_active_record and ca.active = 1\n", "# LEFT JOIN retail.console_location cb ON cb.id = ca.tax_location_id and cb.lake_active_record\n", "\n", "# where dt>='20240101'\n", "# and target_delivery_date_ist between date('{{Start_date}}') and date('{{End_date}}')\n", "# and warehouse_code like 'CPC%'\n", "# and order_status!='CANCELLED'\n", "# and order_tag!='DIRECT_DELIVERY'\n", "# and lower(buyer_outlet_name) not like '%test outlet%'\n", "# )\n", "\n", "# , final_base as (\n", "#  select distinct\n", "#         warehouse_code,\n", "#         Warehouse_city,\n", "#         outlet_city,\n", "#         buyer_outlet_id,\n", "#         buyer_outlet_name\n", "\n", "#     from map_base\n", "# )\n", "\n", "# select month,\n", "# WarehouseCode,\n", "# manufacturer_name,\n", "# manufacturer_id,\n", "# City,\n", "# city_blinkit,\n", "# -- buyer_outlet_name,\n", "# outlet_city,\n", "# VendorID,\n", "# sap_vendor_code,\n", "# vendor_name,\n", "# grn_basic_amount,\n", "# cast(grn_basic_amount/sum(grn_basic_amount) over(partition by month, outlet_city, manufacturer_name) as double) as city_perc--,\n", "# -- cast(grn_basic_amount/sum(grn_basic_amount) over(partition by month, new_city, blinkit_vendor_name) as double) as new_city_perc\n", "\n", "\n", "# from (\n", "# select date(date_trunc('month',OrderCreationDateTime)) as month,\n", "# WarehouseCode,\n", "# manufacturer_name,\n", "# manufacturer_id,\n", "# City,\n", "# city_blinkit,\n", "# -- f.buyer_outlet_name,\n", "# f.outlet_city,\n", "# -- coalesce(case when City = 'Alwar' then 'Gurgaon'\n", "# --  when City = 'Anantapur' then 'Hyderabad'\n", "# --  when City = 'Bengaluru' then 'Bengaluru'\n", "# --  when City = 'Delhi' then 'Delhi'\n", "# --  when City = 'Gurugram' then 'Gurgaon'\n", "# --  when City = 'Hapur' then 'Ghaziabad'\n", "# --  when City = 'Jaipur' then 'Jaipur'\n", "# --  when City = 'Karnal' then 'Gurgaon'\n", "# --  when City = 'Kolar' then 'Bengaluru'\n", "# --  when City = 'Kolkata' then 'Kolkata'\n", "# --  when City = 'Mandya' then 'Mumbai'\n", "# --  when City = 'Meerut' then 'Ghaziabad'\n", "# --  when City = 'Mumbai' then 'Mumbai'\n", "# --  when City = 'Nashik' then 'Pune'\n", "# --  when City = 'Noida' then 'Noida'\n", "# --  when City = 'Panipat' then 'Gurgaon'\n", "# --  when City = 'Pune' then 'Pune'\n", "# --  when City = 'Ranga Reddy' then 'Hyderabad'\n", "# --  when City = 'Solapur' then 'Pune'\n", "# --  when City = 'Sonipat' then 'Gurgaon' end,city_blinkit,City)  as new_city,\n", "# VendorID,\n", "# sap_vendor_code,\n", "# vendor_name,\n", "# sum(grnbasic_amount) as grn_basic_amount\n", "# from main as m\n", "# inner join final_base as f on m.WarehouseCode = f.warehouse_code and f.outlet_city is not null\n", "# group by 1,2,3,4,5,6,7,8,9,10--,11\n", "\n", "# )\n", "# \"\"\".replace(\"%\", \"%%\").replace(\"{{\", \"{\").replace(\"}}\", \"}\")\n", "# query = pd.read_sql(data.format(\n", "#     Start_date=Start_date,  # Replace with actual start date\n", "#     End_date=End_date,    # Replace with actual end date\n", "# ),con)"]}, {"cell_type": "code", "execution_count": null, "id": "ae470216-65a7-48b0-8ff4-af4e37f5f4dd", "metadata": {}, "outputs": [], "source": ["data = (\n", "    \"\"\"\n", "\n", "with user_data as (\n", "    select po_id , max(case when new_po_status = 'CREATED' then u.name  end) as created_by,\n", "    max(case when new_po_status = 'APPROVED' then u.name end) as approved_by,\n", "    max(case when new_po_status = 'COMPLETED' then u.name end) as completed_by\n", "    from (select po_id, new_po_status , updated_by_user_id , row_number() over (partition by new_po_status , po_id  order by id desc) as row\n", "    from zomato.hp_wms.purchase_order_status_history \n", "    where dt > '20240301' and new_po_status in ('CREATED','COMPLETED' , 'APPROVED')) a\n", "    join zomato.hp_users.users u on u.id = a.updated_by_user_id\n", "    where row = 1 group by 1\n", "),\n", "\n", "request as (\n", "    select distinct date(a.created_at),\n", "    partner_order_id,\n", "    b.vendor_name\n", "    from po.edi_integration_partner_purchase_order_details as p\n", "    inner join po.purchase_order_items as a on p.po_id = a.po_id\n", "                                            and date(a.created_at) between date('{{Start_date}}') and date('{{End_date}}')\n", "    inner join po.purchase_order as b on a.po_id = b.id\n", "                                      and b.vendor_name = 'Zomato Hyperpure Private Limited'\n", ")\n", "\n", "\n", ", po as (\n", "select distinct po_number from (\n", "    select ref_purchase_order_number as po_number, r.partner_order_id, z.dt,  r.vendor_name\n", "    from zomato.hp_wms.orders as z\n", "    inner join (select distinct buyer_order_request_id as id,\n", "                       cast(coalesce(order_reference.hp_order_references[1].hp_order_id,0) as bigint) AS orderid\n", "                       from zomato.dynamodb.prod_hp_order_service\n", "                       where type = 'BLINKIT'\n", "                       and CAST(target_delivery_date + interval '330' minute AS timestamp) >= cast(DATE '{{Start_date}}' as timestamp)) as a on z.id = a.orderid\n", "                                         \n", "    inner join request as r on r.partner_order_id = a.id\n", "    where z.dt>= cast(date_format(DATE '{{Start_date}}', '%Y%m%d') as varchar)\n", "    and z.order_tag in ('DIRECT_DELIVERY', 'MANDI_DD')\n", "    \n", "\n", "\n", "union\n", "\n", "    select distinct purchase_order_number as po_number, r.partner_order_id, z.dt,  r.vendor_name\n", "    from zomato.hp_wms.purchase_order as z\n", "    inner join zomato.hp_wms.bin_inventory_logs as a on z.id = a.source_po\n", "                                                     and a.reason_code = 'ORDER_CONSUME'\n", "                                                     and a.dt >= cast(date_format(DATE '{{Start_date}}', '%Y%m%d') as varchar)\n", "    inner join zomato.hp_wms.orders as b on a.destination_entity_id =b.order_number\n", "                                         and b.dt >= cast(date_format(DATE '{{Start_date}}', '%Y%m%d') as varchar)\n", "                                         and b.order_tag not in ('DIRECT_DELIVERY', 'MANDI_DD')\n", "    inner join (select distinct buyer_order_request_id as id,\n", "                       cast(coalesce(order_reference.hp_order_references[1].hp_order_id,0) as bigint) AS orderid\n", "                       from zomato.dynamodb.prod_hp_order_service\n", "                       where type = 'BLINKIT'\n", "                       and CAST(target_delivery_date + interval '330' minute AS timestamp) >= cast(DATE '{{Start_date}}' as timestamp)) as c on b.id = c.orderid\n", "                                                     \n", "    inner join request as r on r.partner_order_id = c.id\n", "    where z.dt >= cast(date_format(DATE '{{Start_date}}', '%Y%m%d') as varchar)                                         \n", "    )\n", ")\n", "\n", "-- SELEct * from po limit 100\n", "\n", ",grn_comp as (\n", "    select po_id, \n", "    max(case when new_po_status = 'COMPLETED' then u.name end) as completed_by\n", "    from (\n", "        select po_grn_mapping_id po_id, \n", "        new_grn_status new_po_status, \n", "        history_created_by updated_by_user_id,\n", "        row_number() over (partition by new_grn_status , po_grn_mapping_id  order by id desc) as row\n", "        from zomato.hp_wms.po_grn_mapping_status_history\n", "        where dt >= cast(date_format(DATE '{{Start_date}}', '%Y%m%d') as varchar)\n", "        and new_grn_status in ('COMPLETED') ) a\n", "        join zomato.hp_users.users u on u.id = a.updated_by_user_id\n", "        where row = 1\n", "        group by 1\n", "),\n", "\n", "\n", "map as (\n", "select *,count(1) over (partition by product_id) cnt from\n", "    (select\n", "    om.product_id,id.item_id,id.name,p.product_name,avg(om.avg_selling_price_ratio) avg_selling_price_ratio,avg(multiplier) multiplier\n", "    from dwh.dim_item_product_offer_mapping om\n", "    join lake_rpc.item_details id on om.item_id=id.item_id\n", "    join dwh.dim_product p on p.product_id=om.product_Id and p.is_current\n", "    where om.is_current\n", "    group by 1,2,3,4)\n", "),\n", "\n", "manu as (\n", "    select * from\n", "    (\n", "    select\n", "    manufacturer_id,\n", "    manufacturer_name,\n", "    row_number() over (partition by manufacturer_id order by updated_at desc)  rn\n", "    from po.purchase_order_items\n", "    )\n", "    where rn=1\n", "),\n", "\n", "manu_map as (\n", "    select m.*,id.manufacturer_id,mn.manufacturer_name\n", "    from map m\n", "    left join lake_rpc.item_details id on m.item_id=id.item_id\n", "    left join manu mn on mn.manufacturer_id=id.manufacturer_id\n", "),\n", "\n", "final as (\n", "    select * \n", "    from (select \n", "        hp_wms.purchase_order.id as PurchaseOrderID, \n", "        hp_wms.purchase_order.purchase_order_number as PurchaseOrderNumber, \n", "        hp_wms.purchase_order.vendor_id as VendorID,\n", "        sl.sap_vendor_code as sap_vendor_code , \n", "        sl.credit_period as credit_period , \n", "        hp_wms.po_grn_mapping.warehouse_code as WarehouseCode, \n", "        wms_city.city_name as City,\n", "        cl.name as city_blinkit,\n", "        hp_wms.purchase_order_items.product_number as ProductNumber, \n", "        product_name as ProductName, \n", "        c.name as ParentCategoryName, \n", "        grn_price_per_unit as PricePerUnit,\n", "        quantity_ordered as QuantityOrdered, \n", "        hp_wms.po_grn_item.delivered_quantity as QuantityDelivered,\n", "        hp_wms.po_grn_item.sellable_quantity as SellableQuantity, \n", "        A.grn_completion_date as CompletedDateTime,\n", "        hp_wms.purchase_order_items.buy_unit as buy_unit,\n", "        hp_wms.purchase_order_items.sell_unit as sell_unit,\n", "        hp_wms.po_grn_mapping.created_at + interval '330' minute as OrderCreationDateTime, \n", "        hp_wms.po_grn_mapping.grn_status as Status, \n", "        hp_wms.seller_outlet.outlet_name as vendor_name, \n", "        p.weight_per_packet as WeightPerPacket , \n", "        p.sub_uom as Uo<PERSON>,\n", "        ' ' as DiscrepancyReason ,\n", "        hp_wms.po_grn_mapping.grn_number as GRN_Number,\n", "        hp_wms.po_grn_mapping.grn_amount  as \"GRN/PO_Value\" ,\n", "        hp_wms.po_grn_mapping.supplier_bill_amount as TotalSupplierBillAmount,\n", "        hp_wms.po_grn_mapping.supplier_bill_number as SupplierBillNumber, \n", "        hp_wms.purchase_order_items.cess_rate as Cess_Rate ,\n", "        hp_wms.purchase_order_items.igst_rate as IGST_Rate, \n", "        hp_wms.purchase_order_items.cgst_rate as CGST_Rate, \n", "        hp_wms.purchase_order_items.sgst_rate as SGST_Rate,\n", "        sl.gstin as GST<PERSON>, \n", "        p.hsn_code as HSN_Code,\n", "        hp_wms.po_grn_item.delivered_quantity * grn_price_per_unit as PreTax_Value,\n", "        hp_wms.po_grn_item.delivered_quantity * grn_price_per_unit * hp_wms.purchase_order_items.cess_rate / 100 as cess_amount, \n", "        hp_wms.po_grn_item.delivered_quantity * grn_price_per_unit * hp_wms.purchase_order_items.sgst_rate / 100 as sgst_amount, \n", "        hp_wms.po_grn_item.delivered_quantity * grn_price_per_unit * hp_wms.purchase_order_items.cgst_rate / 100 as cgst_amount, \n", "        hp_wms.po_grn_item.delivered_quantity * grn_price_per_unit * hp_wms.purchase_order_items.igst_rate / 100 as igst_amount, \n", "        (hp_wms.po_grn_item.delivered_quantity * grn_price_per_unit * hp_wms.purchase_order_items.gst_rate / 100)+ (hp_wms.po_grn_item.delivered_quantity * grn_price_per_unit * hp_wms.purchase_order_items.cess_rate / 100) as GST_amount, \n", "        ' '  as BO_Number,u1.created_by , u1.approved_by , u2.completed_by ,\n", "        po_amount , hp_wms.po_grn_mapping.grn_amount, payment_amount  , payment_date  , payment_number , po_payment_status--, pp.vendor_name as blinkit_vendor_name\n", "\n", "        from zomato.hp_wms.po_grn_mapping \n", "      inner join zomato.hp_wms.purchase_order on hp_wms.purchase_order.id = hp_wms.po_grn_mapping.purchase_order_id \n", "      inner join zomato.hp_wms.po_grn_item on hp_wms.po_grn_mapping.id = hp_wms.po_grn_item.po_grn_mapping_id \n", "      inner join zomato.hp_wms.purchase_order_items on hp_wms.po_grn_item.purchase_order_item_id = hp_wms.purchase_order_items.id \n", "      inner join zomato.hp_wms.warehouse wms_warehouse on hp_wms.purchase_order.warehouse_code = wms_warehouse.warehouse_code\n", "      inner join zomato.hp_wms.city wms_city on wms_warehouse.city_id = wms_city.id\n", "      inner join po as pp on pp.po_number = hp_wms.purchase_order.purchase_order_number\n", "      left join zomato.hp_wms.purchase_order_payment pop on pop.id = hp_wms.po_grn_mapping.purchase_order_payment_id and pop.dt > '20240301'\n", "      left join zomato.hp_wms.seller_outlet sl ON  hp_wms.purchase_order.vendor_id = sl.id and sl.dt > '0'\n", "      left join user_data u1 on u1.po_id = hp_wms.purchase_order.id\n", "      left join grn_comp u2 on u2.po_id = hp_wms.po_grn_mapping.id\n", "      left join po.edi_integration_partner_outlet_mapping ipom ON lower(ipom.partner_outlet_id) = lower(wms_warehouse.warehouse_code) AND ipom.active\n", "      left join retail.console_outlet co ON co.id = ipom.outlet_id and co.lake_active_record and co.active = 1\n", "      LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id and cl.lake_active_record\n", "      inner join (\n", "        select distinct po_grn_mapping_id, \n", "          min(history_created_at + interval '330' minute) as grn_completion_date \n", "        from zomato.hp_wms.po_grn_mapping_history \n", "        where grn_status = 'COMPLETED' and zomato.hp_wms.po_grn_mapping_history.dt >= '20240301'\n", "        group by po_grn_mapping_id ) A on zomato.hp_wms.po_grn_mapping.id = A.po_grn_mapping_id\n", "        \n", "          left JOIN zomato.hp_wms.product AS \"p\" ON zomato.hp_wms.purchase_order_items.product_id = p.id \n", "          left JOIN zomato.hp_wms.category AS \"c\" ON p.parent_category_id = c.ID --left join hp_wms.purchase_order_payment on hp_wms.po_grn_mapping.purchase_order_payment_id = hp_wms.purchase_order_payment.id\n", "          left JOIN zomato.hp_wms.seller_outlet ON zomato.hp_wms.purchase_order.vendor_id = zomato.hp_wms.seller_outlet.id and zomato.hp_wms.seller_outlet.dt >= '20180101' \n", "          where\n", "          zomato.hp_wms.po_grn_mapping.dt >= '20240301'\n", "          and zomato.hp_wms.purchase_order.dt >='20240301'\n", "          and zomato.hp_wms.po_grn_item.dt >= '20240301'\n", "          and zomato.hp_wms.purchase_order_items.dt >= '20240301'\n", "          order by zomato.hp_wms.po_grn_mapping.purchase_order_id desc, purchase_order_item_id\n", "  ) final\n", "  where CompletedDateTime >= date'{{Start_date}}'\n", "  and CompletedDateTime <= date'{{End_date}}'\n", "),\n", "\n", "\n", "main as (\n", "select VendorID, \n", "sap_vendor_code, \n", "Pur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ber ,\n", "OrderCreationDateTime, \n", "Status,\n", "vendor_name,\n", "credit_period,\n", "WarehouseCode,\n", "GRN_Number,\n", "CompletedDateTime, \n", "ap.created_by,\n", "ap.approved_by,\n", "ap.completed_by,\n", "array_join(array_agg(distinct ParentCategoryName), ',') as category_name,\n", "pim.item_id as blinkit_item_id, \n", "manufacturer_id,\n", "manufacturer_name, \n", "City,\n", "city_blinkit,\n", "sum(PreTax_Value) as grnbasic_amount,\n", "sum(cess_amount) as cess_amount,\n", "sum(sgst_amount) as sgst_amount,\n", "sum(cgst_amount) as cgst_amount,\n", "sum(igst_amount) as igst_amount,\n", "sum(GST_amount) as GST_amount, \n", "payment_number,\n", "po_payment_status\n", "\n", "from final as ap\n", "left join po.edi_integration_partner_item_mapping pim on cast(ap.ProductNumber as varchar) = pim.partner_item_id\n", "left join manu_map mm on mm.item_id = pim.item_id\n", "group by VendorID , sap_vendor_code , PurchaseOrderNumber ,OrderCreationDateTime, Status, vendor_name , credit_period , WarehouseCode , GRN_Number ,CompletedDateTime , \n", "ap.created_by , ap.approved_by ,ap.completed_by , payment_number , po_payment_status, pim.item_id, manufacturer_id, manufacturer_name, City,  city_blinkit --blinkit_vendor_name,\n", ")\n", "\n", "\n", ", map_base as (\n", "select distinct\n", "warehouse_code,buyer_outlet_id,buyer_outlet_name,\n", "cl.name as Warehouse_city,\n", "cb.name as outlet_city\n", "from zomato.hyperpure_etls.hyperpure_order_history as a\n", "\n", "left join po.edi_integration_partner_outlet_mapping ipom ON lower(ipom.partner_outlet_id) = lower(a.warehouse_code) AND ipom.active\n", "left join retail.console_outlet co ON co.id = ipom.outlet_id and co.lake_active_record and co.active = 1\n", "LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id and cl.lake_active_record\n", "left join retail.console_outlet ca ON lower(ca.name) = lower(a.buyer_outlet_name) and ca.lake_active_record and ca.active = 1\n", "LEFT JOIN retail.console_location cb ON cb.id = ca.tax_location_id and cb.lake_active_record\n", "\n", "where dt>='20240101'\n", "and target_delivery_date_ist between date('{{Start_date}}') and date('{{End_date}}')\n", "and warehouse_code like 'CPC%'\n", "and order_status!='CANCELLED'\n", "and order_tag!='DIRECT_DELIVERY'\n", "and lower(buyer_outlet_name) not like '%test outlet%'\n", ")\n", "\n", ", final_base as (\n", " select distinct \n", "        warehouse_code,\n", "        Warehouse_city,\n", "        outlet_city,\n", "        buyer_outlet_id,\n", "        buyer_outlet_name\n", "        \n", "    from map_base\n", ")\n", "-- SELEct * from final_base limit 100\n", "select month, \n", "WarehouseCode,\n", "manufacturer_name,\n", "manufacturer_id,\n", "City,\n", "city_blinkit,\n", "outlet_city,\n", "VendorID,\n", "sap_vendor_code,\n", "vendor_name,\n", "grn_basic_amount,\n", "cast(grn_basic_amount/sum(grn_basic_amount) over(partition by month, outlet_city, manufacturer_name) as double) as city_perc\n", "\n", "\n", "from (\n", "select date(date_trunc('month',OrderCreationDateTime)) as month, \n", "WarehouseCode,\n", "manufacturer_name,\n", "manufacturer_id,\n", "City,\n", "city_blinkit,\n", "f.outlet_city,\n", "VendorID,\n", "sap_vendor_code,\n", "vendor_name,\n", "sum(grnbasic_amount) as grn_basic_amount\n", "from main as m\n", "inner join final_base as f on m.WarehouseCode = f.warehouse_code and f.outlet_city is not null\n", "group by 1,2,3,4,5,6,7,8,9,10--,11\n", "\n", ")\n", "\n", "\n", "\n", "\"\"\".replace(\n", "        \"%\", \"%%\"\n", "    )\n", "    .replace(\"{{\", \"{\")\n", "    .replace(\"}}\", \"}\")\n", ")\n", "query = pd.read_sql(\n", "    data.format(\n", "        Start_date=Start_date,  # Replace with actual start date\n", "        End_date=End_date,  # Replace with actual end date\n", "    ),\n", "    con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5e5f85dd-5b2a-4158-a6c3-f335f7a22400", "metadata": {}, "outputs": [], "source": ["query.shape"]}, {"cell_type": "code", "execution_count": null, "id": "19b91081-61f7-40a0-8448-7d80eb0cbc6a", "metadata": {}, "outputs": [], "source": ["query.columns"]}, {"cell_type": "code", "execution_count": null, "id": "e2497d2e-d342-4e82-b4fa-a5fb212a4152", "metadata": {}, "outputs": [], "source": ["query.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "d515a84d-2572-4ed1-9cf3-1de77c92a175", "metadata": {}, "outputs": [], "source": ["query.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "5c123a5d-2b06-49b5-b84b-b69521b8de6d", "metadata": {}, "outputs": [], "source": ["query = query.dropna(subset=[\"manufacturer_id\"])\n", "query[\"manufacturer_id\"] = query[\"manufacturer_id\"].astype(int)\n", "query[\"manufacturer_id\"] = query[\"manufacturer_id\"].fillna(-1).astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "7a66f09d-669d-47cb-b717-59eeaf33f3f9", "metadata": {}, "outputs": [], "source": ["query[\"month\"] = pd.to_datetime(query[\"month\"])\n", "# query[\"manufacturer_id\"] = query[\"manufacturer_id\"].astype(int)\n", "query[\"manufacturer_name\"] = query[\"manufacturer_name\"].astype(str)\n", "query[\"city_perc\"] = pd.to_numeric(query[\"city_perc\"], errors=\"coerce\")"]}, {"cell_type": "code", "execution_count": null, "id": "0f1b2ba7-efff-4400-aaf4-a0c9ecadcfc2", "metadata": {}, "outputs": [], "source": ["query.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "e31379e9-a0f3-4a17-82ee-d2194fd3dc49", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"fin_etls\",\n", "    \"table_name\": \"hp_grn_april_dt\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"month\", \"type\": \"DATE\", \"description\": \"date of creation of order\"},\n", "        {\"name\": \"WarehouseCode\", \"type\": \"VARCHAR\", \"description\": \"outlet id\"},\n", "        {\n", "            \"name\": \"manufacturer_name\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"date of creation of order\",\n", "        },\n", "        {\"name\": \"manufacturer_id\", \"type\": \"INTEGER\", \"description\": \"outlet id\"},\n", "        {\"name\": \"City\", \"type\": \"VARCHAR\", \"description\": \"outlet id\"},\n", "        {\"name\": \"city_blinkit\", \"type\": \"VARCHAR\", \"description\": \"outlet id\"},\n", "        # {\"name\": \"buyer_outlet_name\", \"type\": \"VARCHAR\", \"description\": \"outlet id\"},\n", "        {\"name\": \"outlet_city\", \"type\": \"VARCHAR\", \"description\": \"outlet id\"},\n", "        {\"name\": \"VendorID\", \"type\": \"INTEGER\", \"description\": \"outlet id\"},\n", "        {\"name\": \"sap_vendor_code\", \"type\": \"INTEGER\", \"description\": \"outlet id\"},\n", "        {\"name\": \"vendor_name\", \"type\": \"VARCHAR\", \"description\": \"outlet id\"},\n", "        {\"name\": \"grn_basic_amount\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "        {\"name\": \"city_perc\", \"type\": \"DOUBLE\", \"description\": \"outlet id\"},\n", "    ],\n", "    \"primary_key\": [\"month\", \"manufacturer_id\", \"WarehouseCode\", \"VendorID\"],\n", "    \"partition_key\": [\"month\"],\n", "    \"incremental_key\": [],\n", "    \"load_type\": \"partition_overwrite\",\n", "    \"force_upsert_without_increment_check\": True,\n", "    \"table_description\": \"this table is made temporary data push\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "9c9673c9-e18a-49a6-b0b7-ed5502c8327f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1967f4a3-bb61-4db8-9cc7-687e0242c46e", "metadata": {}, "outputs": [], "source": ["pb.to_trino(query, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}