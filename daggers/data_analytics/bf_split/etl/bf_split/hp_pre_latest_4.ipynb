{"cells": [{"cell_type": "code", "execution_count": null, "id": "ec0a4063-2093-4b2f-b298-925f3a09ef5f", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "from datetime import date\n", "from datetime import timedelta\n", "from datetime import datetime\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\").connect()"]}, {"cell_type": "code", "execution_count": null, "id": "c5c3289a-e460-4f52-9a76-cdde50cd0558", "metadata": {}, "outputs": [], "source": ["blinkit_PO_Created_at_start = \"2025-05-01\"\n", "blinkit_PO_Created_at_end = \"2025-05-31\""]}, {"cell_type": "code", "execution_count": null, "id": "4983160f-1cb4-4c8f-a188-ca3e7510c69a", "metadata": {}, "outputs": [], "source": ["print(blinkit_PO_Created_at_start)\n", "print(blinkit_PO_Created_at_end)"]}, {"cell_type": "code", "execution_count": null, "id": "a4d9250c-1b5a-4ac2-a510-08616fe89342", "metadata": {}, "outputs": [], "source": ["data = (\n", "    \"\"\"\n", "\n", "with created_bf_base_agg as (\n", "    select \n", "    a.current_month as month,\n", "    a.manufacturer_id,\n", "    a.manufacturer_name,\n", "    sum(total_brand_fund) as total_brand_fund\n", "    from blinkit_staging.fin_etls.brand_fund_split_pre_adjustment_apr_dp_new as a\n", "    WHERE current_month IS NOT NULL\n", "    -- and a.manufacturer_id = 4441\n", "    group by 1,2,3\n", ")\n", "\n", ", delta_bf_actual_base as (\n", "\n", "    select a.month,\n", "    a.manufacturer_id,\n", "    a.manufacturer,\n", "    coalesce(a.total_brand_fund_bfs,0) as total_brand_fund_bfs,\n", "    coalesce(b.total_brand_fund,0) as total_brand_fund,\n", "    case when a.total_brand_fund_bfs >= b.total_brand_fund then 'add'\n", "         else 'subtract' end as tag,\n", "    case when coalesce(a.total_brand_fund_bfs,0) >= coalesce(b.total_brand_fund,0) then (coalesce(a.total_brand_fund_bfs,0) - coalesce(b.total_brand_fund,0))\n", "         else (coalesce(b.total_brand_fund,0) - coalesce(a.total_brand_fund_bfs,0)) end as actual_bf_adjustment\n", "    from blinkit_staging.fin_etls.bfs_data_agggg_april_new as a\n", "    join created_bf_base_agg as b on a.month = b.month\n", "                                  and a.manufacturer_id = b.manufacturer_id\n", "                                  and lower(a.manufacturer) = lower(b.manufacturer_name)\n", ")\n", "\n", ", base_data AS (\n", "    select \n", "    current_month,\n", "    city_name,\n", "    grn_city,\n", "    manufacturer_id,\n", "    manufacturer_name,\n", "    entity_final,\n", "    vendor_id,\n", "    vendor_name,\n", "    total_brand_fund,\n", "    sum(total_brand_fund) OVER (PARTITION BY current_month, city_name, grn_city, manufacturer_name, entity_final) AS mf_city_entity_bf,\n", "    sum(total_brand_fund) OVER (PARTITION BY current_month, city_name, grn_city, manufacturer_name) AS mf_city_bf,\n", "    (total_brand_fund)/(sum(total_brand_fund) OVER (PARTITION BY current_month, city_name, grn_city, manufacturer_name, entity_final)) as mf_city_entity_to_vendor_perc,\n", "    (sum(total_brand_fund) OVER (PARTITION BY current_month, city_name, grn_city, manufacturer_name, entity_final))/(sum(total_brand_fund) OVER (PARTITION BY current_month, city_name, grn_city, manufacturer_name)) as mf_city_to_entity_perc,\n", "    ((total_brand_fund)/(sum(total_brand_fund) OVER (PARTITION BY current_month, city_name, grn_city, manufacturer_name, entity_final)))*((sum(total_brand_fund) OVER (PARTITION BY current_month, city_name, grn_city, manufacturer_name, entity_final))/(sum(total_brand_fund) OVER (PARTITION BY current_month, city_name, grn_city, manufacturer_name))) as final_vendor_entity_perc,\n", "    row_number() over(partition by current_month, manufacturer_name order by total_brand_fund desc) as rnk\n", "    from (\n", "        SELECT \n", "            current_month,\n", "            city_name,\n", "            grn_city,\n", "            manufacturer_id,\n", "            manufacturer_name,\n", "            entity_final,\n", "            vendor_id,\n", "            vendor_name,\n", "            sum(total_brand_fund) as total_brand_fund\n", "        FROM blinkit_staging.fin_etls.brand_fund_split_pre_adjustment_apr_dp_new\n", "        WHERE current_month IS NOT NULL\n", "        AND entity_final <> '0'\n", "        -- and manufacturer_id in ({{manufacturer_id_placeholder}})\n", "        group by 1,2,3,4,5,6,7,8\n", "        )\n", ")\n", "\n", ", adjust_main_delta_in_top_city as (\n", "    select b.current_month,\n", "    b.city_name,\n", "    b.grn_city,\n", "    b.manufacturer_id,\n", "    b.manufacturer_name,\n", "    b.entity_final,\n", "    b.vendor_id,\n", "    b.vendor_name,\n", "    b.total_brand_fund as old_bf,\n", "    b.mf_city_entity_bf,\n", "    b.mf_city_bf,\n", "    b.mf_city_entity_to_vendor_perc,\n", "    b.mf_city_to_entity_perc,\n", "    b.final_vendor_entity_perc,\n", "    k.tag,\n", "    k.actual_bf_adjustment,\n", "    case when k.tag = 'add' then (coalesce(b.total_brand_fund,0) + coalesce(k.actual_bf_adjustment,0))\n", "                            else (coalesce(b.total_brand_fund,0) - coalesce(k.actual_bf_adjustment,0)) end as total_brand_fund\n", "    from base_data as b\n", "    left join delta_bf_actual_base as k on k.month = b.current_month\n", "                                        and k.manufacturer_id = b.manufacturer_id\n", "                                        and lower(k.manufacturer) = lower(b.manufacturer_name)\n", "                                        and b.rnk = 1\n", "    )\n", "\n", "\n", "\n", ", missing_bf as (\n", "    SELECT \n", "        current_month,\n", "        city_name,\n", "        grn_city,\n", "        manufacturer_id,\n", "        manufacturer_name,\n", "        sum(total_brand_fund) AS total_brand_fund\n", "    FROM blinkit_staging.fin_etls.brand_fund_split_pre_adjustment_apr_dp_new\n", "    WHERE current_month IS NOT NULL\n", "    AND entity_final = '0'\n", "    group by 1,2,3,4,5\n", ")\n", "\n", "\n", "\n", ", adjusted_bf_entity_level as (\n", "    select e.current_month,\n", "    e.city_name,\n", "    e.grn_city,\n", "    e.manufacturer_id,\n", "    e.manufacturer_name,\n", "    e.entity_final,\n", "    e.vendor_id,\n", "    e.vendor_name,\n", "    e.total_brand_fund,\n", "    e.final_vendor_entity_perc,\n", "    count(e.grn_city) over(partition by e.current_month, e.city_name, e.manufacturer_id, e.entity_final, e.vendor_id) as grn_cities,\n", "    coalesce(cast(e.final_vendor_entity_perc*(m.total_brand_fund/count(e.grn_city) over(partition by e.current_month, e.city_name, e.manufacturer_id, e.entity_final, e.vendor_id)) as double),0) as adjusted_bf,\n", "    (coalesce(cast(e.final_vendor_entity_perc*(m.total_brand_fund/count(e.grn_city) over(partition by e.current_month, e.city_name, e.manufacturer_id, e.entity_final, e.vendor_id)) as double),0) + cast(e.total_brand_fund as double)) as new_brand_fund\n", "    from adjust_main_delta_in_top_city as e\n", "    left join missing_bf as m on e.current_month = m.current_month\n", "                         and e.city_name = m.city_name\n", "                        --  and e.grn_city = m.grn_city\n", "                         and e.manufacturer_name = m.manufacturer_name\n", ")\n", "\n", "\n", "\n", ", delta_to_be_adj_in_top_city as (\n", "    select b.current_month,\n", "    b.manufacturer_name,\n", "    b.manufacturer_id,\n", "    missing_bf,\n", "    adjusted_bf,\n", "    (missing_bf-adjusted_bf) as delta\n", "    from (\n", "        select b.current_month,\n", "        b.manufacturer_id as manufacturer_id,\n", "        b.manufacturer_name,\n", "        sum(b.total_brand_fund) as missing_bf\n", "        from missing_bf as b\n", "        group by 1,2,3\n", "    ) b\n", "    join (select current_month,\n", "             manufacturer_id as manufacturer_id,\n", "             manufacturer_name,\n", "             sum(adjusted_bf) as adjusted_bf \n", "             from adjusted_bf_entity_level\n", "             group by 1,2,3) as a on b.current_month = a.current_month\n", "                                and b.manufacturer_name = a.manufacturer_name\n", ")\n", "\n", "\n", "\n", ", top_city_mf as (\n", "    select k.current_month,\n", "    k.city_name,\n", "    k.grn_city,\n", "    k.manufacturer_id,\n", "    k.manufacturer_name,\n", "    k.entity_final,\n", "    k.vendor_id,\n", "    k.vendor_name,\n", "    coalesce(a.delta,0) as delta,\n", "    k.new_brand_fund,\n", "    (k.new_brand_fund + coalesce(a.delta,0)) as new_top_city_adj_bf\n", "    from base_data as b\n", "    inner join adjusted_bf_entity_level as k on k.current_month = b.current_month\n", "                                             and k.city_name = b.city_name\n", "                                             and k.grn_city = b.grn_city\n", "                                             and k.manufacturer_name = b.manufacturer_name\n", "                                             and k.entity_final = b.entity_final\n", "                                             and k.vendor_name = b.vendor_name\n", "    inner join delta_to_be_adj_in_top_city as a on a.current_month = k.current_month\n", "                                             and a.manufacturer_name = k.manufacturer_name\n", " --   inner join delta_to_be_adj_in_top_city as a on b.current_month = b.current_month\n", " --                                               and a.manufacturer_name = b.manufacturer_name\n", "\n", "    where b.rnk = 1\n", ")\n", "\n", "\n", ", final_bf_allocation as (\n", "    select ad.current_month,\n", "    ad.city_name,\n", "    ad.grn_city,\n", "    ad.manufacturer_id,\n", "    ad.manufacturer_name,\n", "    ad.entity_final,\n", "    ad.vendor_id,\n", "    ad.vendor_name,\n", "    coalesce(m.new_top_city_adj_bf,ad.new_brand_fund) as final_bf\n", "    from adjusted_bf_entity_level as ad\n", "    left join top_city_mf as m on ad.current_month = m.current_month\n", "                                and ad.city_name = m.city_name\n", "                                and ad.grn_city = m.grn_city\n", "                                and ad.manufacturer_name = m.manufacturer_name\n", "                                and ad.entity_final = m.entity_final\n", "                                and ad.vendor_name = m.vendor_name\n", ")\n", "\n", "\n", "select * from final_bf_allocation \n", "where current_month = date'2025-05-01'\n", "-- and manufacturer_id = 4441\n", "order by 1,2,3,3,4,5,6,7\n", "\n", "\"\"\".replace(\n", "        \"%\", \"%%\"\n", "    )\n", "    .replace(\"{{\", \"{\")\n", "    .replace(\"}}\", \"}\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b34714af-9053-4035-bc74-af275d47a603", "metadata": {}, "outputs": [], "source": ["query = pd.read_sql(data, con)"]}, {"cell_type": "code", "execution_count": null, "id": "5dbf4a5f-cd29-4994-a9c6-562308da607a", "metadata": {}, "outputs": [], "source": ["query.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7e66dac8-af4a-4d5f-9f89-93c4822aef62", "metadata": {}, "outputs": [], "source": ["query.shape"]}, {"cell_type": "code", "execution_count": null, "id": "05224e19-c7f1-48f2-85ce-828ef0cbd39b", "metadata": {}, "outputs": [], "source": ["query.columns"]}, {"cell_type": "code", "execution_count": null, "id": "e09bc2ff-601a-474d-b40d-6e7eb19132c0", "metadata": {}, "outputs": [], "source": ["query.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "e147f290-a193-40c9-84e0-a92518ecfcc9", "metadata": {}, "outputs": [], "source": ["query = query.fillna(0)\n", "query[\"current_month\"] = pd.to_datetime(query[\"current_month\"])\n", "query[\"manufacturer_id\"] = (\n", "    pd.to_numeric(query[\"manufacturer_id\"], errors=\"coerce\").fillna(0).astype(int)\n", ")\n", "# query[\"manufacturer_id\"] = query[\"manufacturer_id\"].astype(int)\n", "query[\"manufacturer_name\"] = query[\"manufacturer_name\"].astype(str)\n", "query[\"city_name\"] = query[\"city_name\"].astype(str)\n", "query[\"grn_city\"] = query[\"grn_city\"].astype(str)\n", "query[\"entity_final\"] = query[\"entity_final\"].astype(str)\n", "query[\"vendor_id\"] = query[\"vendor_id\"].astype(int)\n", "query[\"vendor_name\"] = query[\"vendor_name\"].astype(str)\n", "query[\"final_bf\"] = query[\"final_bf\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "6eb3bcf9-b2cf-4f83-abe6-a81e6eab46e6", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"fin_etls\",\n", "    \"table_name\": \"final_bf_allocation_april_dte_dp_new\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"current_month\", \"type\": \"DATE\", \"description\": \"date of creation of order\"},\n", "        {\"name\": \"manufacturer_id\", \"type\": \"INTEGER\", \"description\": \"date of creation of order\"},\n", "        {\n", "            \"name\": \"manufacturer_name\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"date of creation of order\",\n", "        },\n", "        {\"name\": \"city_name\", \"type\": \"VARCHAR\", \"description\": \"outlet id\"},\n", "        {\"name\": \"grn_city\", \"type\": \"VARCHAR\", \"description\": \"outlet id\"},\n", "        {\"name\": \"entity_final\", \"type\": \"VARCHAR\", \"description\": \"outlet id\"},\n", "        {\"name\": \"vendor_id\", \"type\": \"INTEGER\", \"description\": \"date of creation of order\"},\n", "        {\"name\": \"vendor_name\", \"type\": \"VARCHAR\", \"description\": \"outlet id\"},\n", "        {\"name\": \"final_bf\", \"type\": \"DOUBLE\", \"description\": \"date of creation of order\"},\n", "    ],\n", "    \"primary_key\": [\"current_month\", \"manufacturer_id\", \"VendorID\", \"city_name\", \"grn_city\"],\n", "    \"partition_key\": [\"current_month\"],\n", "    \"incremental_key\": [],\n", "    \"load_type\": \"partition_overwrite\",\n", "    \"force_upsert_without_increment_check\": True,\n", "    \"table_description\": \"this table is made temporary data push\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "27cbc8a4-7270-4956-9d54-fea9d8d1586b", "metadata": {}, "outputs": [], "source": ["pb.to_trino(query, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}