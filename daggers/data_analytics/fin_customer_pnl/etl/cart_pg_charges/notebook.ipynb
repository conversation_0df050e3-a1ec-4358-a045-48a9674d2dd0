{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import time\n", "from datetime import datetime, timedelta\n", "import numpy as np\n", "import os"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "from datetime import datetime, timedelta, date\n", "import time\n", "\n", "con_trino = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.simplefilter(action=\"ignore\", category=FutureWarning)\n", "\n", "pd.set_option(\"mode.chained_assignment\", None)\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["code_run = pd.read_sql(\n", "    \"select * from ba_etls.run_control_sheet where code='pg_charges' and run_on_input='y'\",\n", "    con_trino,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if len(code_run) == 1:\n", "    start_date = pd.to_datetime(code_run[\"start_date\"][0])\n", "    end_date = pd.to_datetime(code_run[\"end_date\"][0])\n", "else:\n", "\n", "    end_date = pd.to_datetime(date.today() - <PERSON><PERSON><PERSON>(days=1))\n", "    start_date = pd.to_datetime(date.today() - <PERSON><PERSON><PERSON>(days=3))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date_str = start_date\n", "print(f\"Running for start date - {start_date_str} and end date - {end_date}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def query(start_date_str):\n", "\n", "    sql1 = f\"\"\"\n", "    \n", "    WITH base AS (\n", "        SELECT\n", "            cart_id,\n", "            MAX(cart_checkout_ts_ist) AS cart_checkout_ts_ist,\n", "            MAX(outlet_id) AS outlet_id\n", "\n", "        FROM\n", "            dwh.fact_sales_order_details\n", "\n", "        WHERE\n", "            order_create_dt_ist >= date'{start_date_str}' - interval '3' day\n", "            AND order_create_dt_ist < date'{start_date_str}' + interval '1' day\n", "            AND cart_checkout_ts_ist >= date'{start_date_str}'\n", "            AND cart_checkout_ts_ist < date'{start_date_str}' + interval '1' day\n", "            AND is_internal_order = False\n", "            AND outlet_id IS NOT NULL\n", "\n", "        GROUP BY 1\n", "    ),\n", "\n", "    payment_gpays AS(\n", "        SELECT\n", "            cart_id,\n", "            payment,\n", "            gateway_name,\n", "            bank_name,\n", "            other_details,\n", "\n", "            CASE\n", "                WHEN payment <= 2000 \n", "                    THEN TRUE\n", "                ELSE FALSE\n", "            END AS less_than_2K_flag,\n", "\n", "            CASE\n", "                WHEN gateway_name = 'zomato' \n", "                    THEN LOWER(json_value(\n", "                            json_query(\n", "                                json_query(\n", "                                    other_details,'lax $.event_data'\n", "                                ),\n", "                            'lax $.txns[last]'\n", "                            WITHOUT ARRAY WRAPPER\n", "                            NULL ON ERROR\n", "                        ),\n", "                        'lax $.payment_method_type'\n", "                    ))\n", "                ELSE \n", "                    LOWER(json_query(\n", "                        other_details,\n", "                        'lax $.payment_method_type'\n", "                        ))\n", "            END AS payment_method_type,\n", "\n", "            CASE\n", "                WHEN gateway_name = 'zomato' \n", "                    THEN LOWER(json_value(\n", "                            json_query(\n", "                                json_query(\n", "                                    other_details,'lax $.event_data'\n", "                                ),\n", "                            'lax $.txns[last]'\n", "                            WITHOUT ARRAY WRAPPER\n", "                            NULL ON ERROR\n", "                        ),\n", "                        'lax $.payment_source.issuing_bank'\n", "                    ))\n", "                ELSE \n", "                    LOWER(json_query(\n", "                        other_details,\n", "                        'lax $.card'\n", "                        ))\n", "            END AS card_issuer,\n", "\n", "            CASE\n", "                WHEN gateway_name = 'zomato' \n", "                    THEN LOWER(json_value(\n", "                            json_query(\n", "                                json_query(\n", "                                    other_details,'lax $.event_data'\n", "                                ),\n", "                            'lax $.txns[last]'\n", "                            WITHOUT ARRAY WRAPPER\n", "                            NULL ON ERROR\n", "                        ),\n", "                        'lax $.gateway_id'\n", "                    ))\n", "                ELSE \n", "                    LOWER(json_query(\n", "                        other_details,\n", "                        'lax $.gateway_id'\n", "                        ))\n", "            END AS gateway_id,\n", "\n", "            json_extract(\n", "                json_extract(\n", "                    other_details,\n", "                    '$.payment_gateway_response'\n", "                ),\n", "                'txn_id'\n", "            ) AS txn_id\n", "\n", "        FROM\n", "            payments_db.gr_payment\n", "\n", "        WHERE\n", "            received_ts >= date'{start_date_str}' - interval '1' day\n", "            AND received_ts < date'{start_date_str}' + interval '1' day\n", "            AND insert_ds_ist between cast(date'{start_date_str}' - interval '1' day as varchar) and cast(date'{start_date_str}' + interval '1' day as varchar)\n", "            AND is_active\n", "    ),\n", "\n", "    gpays AS (\n", "        SELECT\n", "            grp.cart_id,\n", "            od.cart_checkout_ts_ist,\n", "            od.outlet_id,\n", "            grp.payment,\n", "            grp.less_than_2K_flag,\n", "            grp.gateway_name,\n", "            grp.bank_name,\n", "            other_details,\n", "            payment_method_type,\n", "            card_issuer,\n", "            txn_id,\n", "            CAST(gateway_id AS INTEGER) AS gateway_id,\n", "\n", "            CASE\n", "                WHEN gateway_name = 'zomato'\n", "                      AND payment_method_type = 'card'  \n", "                    THEN LOWER(json_value(\n", "                            json_query(\n", "                                json_query(\n", "                                    other_details,'lax $.event_data'\n", "                                ),\n", "                            'lax $.txns[last]'\n", "                            WITHOUT ARRAY WRAPPER\n", "                            NULL ON ERROR\n", "                        ),\n", "                        'lax $.payment_source.card_type'\n", "                    ))\n", "                WHEN gateway_name = 'zomato'\n", "                      AND payment_method_type = 'netbanking'  \n", "                    THEN LOWER(json_value(\n", "                            json_query(\n", "                                json_query(\n", "                                    other_details,'lax $.event_data'\n", "                                ),\n", "                            'lax $.txns[last]'\n", "                            WITHOUT ARRAY WRAPPER\n", "                            NULL ON ERROR\n", "                        ),\n", "                        'lax $.payment_source.bank_name'\n", "                    ))\n", "                WHEN gateway_name = 'zomato'\n", "                      AND payment_method_type LIKE '%%%%wallet%%%%'   \n", "                    THEN LOWER(json_value(\n", "                            json_query(\n", "                                json_query(\n", "                                    other_details,'lax $.event_data'\n", "                                ),\n", "                            'lax $.txns[last]'\n", "                            WITHOUT ARRAY WRAPPER\n", "                            NULL ON ERROR\n", "                        ),\n", "                        'lax $.payment_source.vault'\n", "                    ))\n", "                ELSE \n", "                    LOWER(json_query(\n", "                        other_details,\n", "                        'lax $.payment_method'\n", "                        ))\n", "            END AS payment_method,\n", "\n", "            CASE\n", "                WHEN gateway_name = 'zomato'\n", "                      AND payment_method_type = 'card'  \n", "                    THEN LOWER(json_value(\n", "                            json_query(\n", "                                json_query(\n", "                                    other_details,'lax $.event_data'\n", "                                ),\n", "                            'lax $.txns[last]'\n", "                            WITHOUT ARRAY WRAPPER\n", "                            NULL ON ERROR\n", "                        ),\n", "                        'lax $.payment_source.type'\n", "                    ))\n", "                ELSE \n", "                    LOWER(json_query(\n", "                        other_details,\n", "                        'lax $.card_type'\n", "                        ))\n", "            END AS card_type\n", "\n", "        FROM\n", "            payment_gpays\n", "                AS grp\n", "\n", "        JOIN \n", "            base \n", "                AS od \n", "                ON od.cart_id = grp.cart_id\n", "    ),\n", "\n", "    refunds AS (\n", "        SELECT\n", "            grf.txn_id,\n", "            grf.refund_amount\n", "\n", "        FROM\n", "            gpays \n", "                AS grp\n", "\n", "        INNER JOIN\n", "            payments_db.gr_payment_refund\n", "                AS grf \n", "                ON grf.txn_id = CAST(grp.txn_id AS VARCHAR)\n", "\n", "        WHERE\n", "            grp.gateway_id IN (41, 28)\n", "            AND install_ts >= date'{start_date_str}' - interval '1' day\n", "            AND install_ts < date'{start_date_str}' + interval '1' day\n", "    ),\n", "\n", "    fpay AS (\n", "        SELECT\n", "            gpays.*,\n", "            (gpays.payment - COALESCE(rf.refund_amount, 0)) AS final_amt\n", "\n", "        FROM\n", "            gpays\n", "\n", "        LEFT JOIN \n", "            refunds \n", "                AS rf \n", "                ON rf.txn_id = CAST(gpays.txn_id AS VARCHAR)\n", "    )\n", "    ,\n", "\n", "    ff_base AS (\n", "        SELECT\n", "            outlet_id,\n", "            cart_id,\n", "            CAST(cart_checkout_ts_ist AS DATE) AS cart_checkout_date_ist,\n", "            LOWER(gateway_name) AS gateway_name,\n", "            payment_method,\n", "\n", "            CASE\n", "                WHEN gateway_id IN (161, 162) THEN 'razorpay'\n", "                WHEN gateway_id = 163 THEN 'paytm'\n", "                WHEN gateway_id = 172 THEN 'payu'\n", "                WHEN gateway_id IN (170, 171, 173) THEN 'sodexo'\n", "                WHEN gateway_id = 168 THEN 'mobikwik'\n", "                WHEN gateway_id = 169 THEN 'simpl'\n", "                WHEN gateway_id = 183 THEN 'paytm_postpaid'\n", "                WHEN gateway_id = 176 THEN 'pinelabs'\n", "                WHEN gateway_id = 189 THEN 'amazonpaywallet'\n", "                WHEN gateway_id = 197 THEN 'lazypay'\n", "                WHEN LOWER(payment_gateway) IN ('razorpay', 'ccavenue_v2', 'hdfc') THEN 'razorpay'\n", "                WHEN LOWER(payment_gateway) LIKE '%%paytm%%' THEN 'paytm'\n", "                ELSE LOWER(payment_gateway)\n", "            END AS payment_gateway,\n", "\n", "            CASE\n", "                WHEN payment_method_type LIKE '%%NB%%'\n", "                    OR payment_method_type LIKE '%%netbanking%%' THEN 'netbanking'\n", "                WHEN card_type LIKE '%%credit%%' THEN 'credit card'\n", "                WHEN card_type LIKE '%%debit%%' THEN 'debit card'\n", "                WHEN payment_method_type = 'upi_collect' THEN 'upi'\n", "                WHEN payment_method_type = 'sodexo_checkout' THEN 'sodexo'\n", "                ELSE LOWER(payment_method_type)\n", "            END AS payment_method_type,\n", "\n", "            CASE\n", "                WHEN LOWER(payment_method) LIKE 'nb_%%' THEN LOWER(SUBSTRING(payment_method, 4))\n", "                ELSE LOWER(payment_method)\n", "            END AS net_banking_bank,\n", "\n", "            CASE\n", "                WHEN card_type LIKE '%%credit%%' THEN 'credit card'\n", "                WHEN card_type LIKE '%%debit%%' THEN 'debit card'\n", "                ELSE LOWER(card_type)\n", "            END AS card_type,\n", "\n", "            LOWER(card_issuer) AS card_issuer,\n", "            LOWER(bank_name) AS bank_name,\n", "            less_than_2K_flag,\n", "\n", "            CASE\n", "                WHEN card_type LIKE '%%debit%%'\n", "                AND less_than_2K_flag = True THEN 'below 2000'\n", "                WHEN card_type LIKE '%%debit%%'\n", "                AND less_than_2K_flag = False THEN 'above 2000'\n", "                ELSE 'no threshold'\n", "            END AS transaction_amount_flag,\n", "\n", "            COUNT(cart_id) AS tot_carts,\n", "            SUM(coalesce(final_amt,0)) AS total_payment_sum\n", "\n", "        FROM\n", "            fpay\n", "\n", "        LEFT JOIN \n", "            blinkit_iceberg.interim.ss_pg_payment_gateway_information \n", "                AS fpgi \n", "                ON fpgi.payment_gate_way_id = fpay.gateway_id\n", "\n", "        GROUP BY\n", "            1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12,13\n", "    )\n", "    ,\n", "\n", "    ff AS (\n", "        SELECT\n", "            outlet_id,\n", "            cart_checkout_date_ist,\n", "            cart_id,\n", "            gateway_name,\n", "            payment_gateway,\n", "            payment_method_type,\n", "            net_banking_bank,\n", "            CASE\n", "                WHEN net_banking_bank = 'diners' THEN 'diners_club'\n", "                WHEN net_banking_bank = 'hdfc bank' THEN 'hdfc'\n", "                WHEN net_banking_bank = 'american_express' THEN 'amex'\n", "                WHEN \n", "                    LOWER(payment_method) NOT IN (\n", "                    'american_express', 'paytm', 'nb_icici', 'nb_hdfc', 'mobikwik', \n", "                    'diners_club', 'hdfc bank', 'googlepay', 'diners', 'amex', 'icici', \n", "                    'hdfc', 'upi', 'visa', 'visa', 'rupay', 'rupay',\n", "                    'simpl', 'sodexo', 'mastercard', 'lazypay', '', 'amazon_pay'\n", "                )\n", "                THEN 'others'\n", "                ELSE net_banking_bank\n", "            END AS payment_method,\n", "\n", "            card_type,\n", "\n", "            card_issuer,\n", "            bank_name,\n", "            less_than_2K_flag,\n", "\n", "            transaction_amount_flag,\n", "\n", "            tot_carts,\n", "            total_payment_sum\n", "\n", "        FROM\n", "            ff_base\n", "\n", "        WHERE\n", "            payment_gateway != ''\n", "\n", "    )\n", "\n", "\n", "\n", "        SELECT\n", "            ff.cart_checkout_date_ist as date_,\n", "            ff.cart_id,\n", "            SUM(coalesce(\n", "                ff.total_payment_sum * (\n", "                     COALESCE(fpgcd.applied_percentage, 0) + COALESCE(fpgcd.service_provider_charge, 0)\n", "                ) * 1.0 / 100\n", "            ,0)) AS total_pg_charges,\n", "\n", "            SUM(coalesce(\n", "                CASE\n", "                    WHEN fpgcd.transaction_type = 'card' \n", "                    THEN ff.total_payment_sum * (\n", "                         COALESCE(fpgcd.applied_percentage, 0) + COALESCE(fpgcd.service_provider_charge, 0)\n", "                    ) * 1.0 / 100\n", "                END\n", "            ,0)) AS pg_charges_card,\n", "\n", "            SUM(coalesce(\n", "                CASE\n", "                    WHEN fpgcd.transaction_type = 'upi'\n", "                    THEN ff.total_payment_sum * (\n", "                         COALESCE(fpgcd.applied_percentage, 0) + COALESCE(fpgcd.service_provider_charge, 0)\n", "                    ) * 1.0 / 100\n", "                END\n", "            ,0)) AS pg_charges_upi,\n", "\n", "            SUM(coalesce(\n", "                CASE\n", "                    WHEN fpgcd.transaction_type = 'wallet' \n", "                    THEN ff.total_payment_sum * (\n", "                         COALESCE(fpgcd.applied_percentage, 0) + COALESCE(fpgcd.service_provider_charge, 0)\n", "                    ) * 1.0 / 100\n", "                END\n", "            ,0)) AS pg_charges_wallet,\n", "\n", "            SUM(coalesce(\n", "                CASE\n", "                    WHEN fpgcd.transaction_type = 'net banking' \n", "                    THEN ff.total_payment_sum * (\n", "                        COALESCE(fpgcd.applied_percentage, 0) + COALESCE(fpgcd.service_provider_charge, 0)\n", "                    ) * 1.0 / 100\n", "                END\n", "            ,0)) AS pg_charges_netbanking,\n", "\n", "            SUM(coalesce(\n", "                CASE\n", "                    WHEN fpgcd.transaction_type = 'paylater' \n", "                    THEN ff.total_payment_sum * (\n", "                        COALESCE(fpgcd.applied_percentage, 0) + COALESCE(fpgcd.service_provider_charge, 0)\n", "                    ) * 1.0 / 100\n", "                END\n", "            ,0)) AS pg_charges_paylater\n", "\n", "        FROM\n", "            ff\n", "\n", "        LEFT JOIN\n", "            blinkit_iceberg.interim.snap_payment_gateway_charges\n", "                AS fpgcd\n", "                ON COALESCE(LOWER(fpgcd.service_provider), '') = COALESCE(LOWER(ff.gateway_name), '')\n", "                    AND COALESCE(LOWER(fpgcd.payment_gateway), '') = COALESCE(LOWER(ff.payment_gateway), '')\n", "                    AND COALESCE(LOWER(fpgcd.payment_method), '') = COALESCE(LOWER(ff.payment_method_type), '')\n", "                    AND COALESCE(LOWER(fpgcd.payment_network), '') = COALESCE(LOWER(ff.payment_method), '')\n", "                    AND COALESCE(LOWER(fpgcd.transaction_amount), '') = COALESCE(LOWER(ff.transaction_amount_flag), '')\n", "                    AND fpgcd.enabled_flag = True\n", "        GROUP BY\n", "            1,2\n", "        \"\"\"\n", "\n", "    column_dtypes = [\n", "        {\"name\": \"date_\", \"type\": \"DATE\", \"description\": \"date_\"},\n", "        {\"name\": \"cart_id\", \"type\": \"INTEGER\", \"description\": \"item_id\"},\n", "        {\"name\": \"total_pg_charges\", \"type\": \"DOUBLE\", \"description\": \"total_pg_charges\"},\n", "        {\"name\": \"pg_charges_card\", \"type\": \"DOUBLE\", \"description\": \"pg_charges_card\"},\n", "        {\"name\": \"pg_charges_upi\", \"type\": \"DOUBLE\", \"description\": \"pg_charges_upi\"},\n", "        {\"name\": \"pg_charges_wallet\", \"type\": \"DOUBLE\", \"description\": \"pg_charges_wallet\"},\n", "        {\"name\": \"pg_charges_netbanking\", \"type\": \"DOUBLE\", \"description\": \"pg_charges_netbanking\"},\n", "        {\"name\": \"pg_charges_paylater\", \"type\": \"DOUBLE\", \"description\": \"pg_charges_paylater\"},\n", "    ]\n", "\n", "    table_description = \"cart level PG charges\"\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"fin_etls\",\n", "        \"table_name\": \"daily_cart_pg_charges\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"date_\", \"cart_id\"],\n", "        \"table_description\": table_description,\n", "        \"load_type\": \"upsert\",\n", "    }\n", "\n", "    pb.to_trino(data_obj=sql1, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["while start_date <= end_date:\n", "\n", "    print(f\"Running this query for {str(start_date.date())}\")\n", "    start_time_1 = datetime.now()\n", "\n", "    start_date_str = start_date.strftime(\"%Y-%m-%d\")\n", "\n", "    query(start_date_str)\n", "\n", "    print(\"----------------------------------------------------------\")\n", "    print(f\"Total Time taken = {datetime.now()-start_time_1}\")\n", "    print(\"----------------------------------------------------------\")\n", "\n", "    start_date = start_date + <PERSON><PERSON><PERSON>(days=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}