alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: daily_ob
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_analytics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U05FNAX2W5T
path: data_analytics/fin_customer_pnl/etl/daily_ob
paused: false
pool: data_analytics_pool
project_name: fin_customer_pnl
schedule:
  end_date: '2025-09-23T00:00:00'
  interval: 0 1,10 * * *
  start_date: '2025-06-25T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
