{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import time\n", "from datetime import datetime, timedelta\n", "import numpy as np\n", "import os"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "from datetime import datetime, timedelta, date\n", "import time\n", "\n", "con_trino = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.simplefilter(action=\"ignore\", category=FutureWarning)\n", "\n", "pd.set_option(\"mode.chained_assignment\", None)\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["code_run = pd.read_sql(\n", "    \"select * from ba_etls.run_control_sheet where code='daily_ob' and run_on_input='y'\",\n", "    con_trino,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if len(code_run) == 1:\n", "    start_date = pd.to_datetime(code_run[\"start_date\"][0])\n", "    end_date = pd.to_datetime(code_run[\"end_date\"][0])\n", "else:\n", "\n", "    end_date = pd.to_datetime(date.today() - <PERSON><PERSON><PERSON>(days=1))\n", "    start_date = pd.to_datetime(date.today() - <PERSON><PERSON><PERSON>(days=5))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date_str = start_date\n", "print(f\"Running for start date - {start_date_str} and end date - {end_date}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def query(start_date_str):\n", "\n", "    sql1 = f\"\"\"\n", "\n", "        with ds as (\n", "        Select id \n", "        from retail.console_outlet\n", "        where \n", "        device_id <> 47\n", "        and facility_id IS NOT NULL\n", "        and facility_id <> 806\n", "        and business_type_id = 7\n", "        and active = 1\n", "        group by 1\n", "        ),\n", "\n", "        bill as (\n", "        select \n", "        grofers_order_id,\n", "        prod.item_id,\n", "        min(date(pi.updated_at)) as date_,\n", "        sum(pipd.quantity) billed_qty\n", "\n", "        from pos.pos_invoice pi\n", "        join pos.pos_invoice_product_details pipd on pipd.invoice_id = pi.id\n", "        join lake_rpc.product_product prod ON prod.variant_id=pipd.variant_id           \n", "\n", "        where pi.insert_ds_ist between cast(date'{start_date_str}' - interval '15' day as varchar) and cast(date'{start_date_str}' + interval '10' day as varchar)\n", "        and pipd.insert_ds_ist between cast(date'{start_date_str}' - interval '15' day as varchar) and cast(date'{start_date_str}' + interval '10' day as varchar)\n", "        and ( grofers_order_id is not null) and ( grofers_order_id <> '')\n", "        and invoice_type_id IN (5,14,16)\n", "\n", "        group BY 1,2\n", "        )\n", "    \n", "    select\n", "    cast(k.order_placed_at_ist + interval '330' minute as date) as date_,\n", "    k.warehouse_code,\n", "    ipom.outlet_id as be_outlet_id,\n", "    nn.outlet_id as fe_outlet_id,\n", "    pi.item_id,\n", "    'HP' as tagg,\n", "    sum(k.picked_quantity) as qty\n", "    \n", "    from zomato.hyperpure_etls.hyperpure_order_history as k\n", "    INNER JOIN po.edi_integration_partner_outlet_mapping ipom ON lower(ipom.partner_outlet_id) = lower(k.warehouse_code) AND ipom.active\n", "    INNER JOIN po.edi_integration_partner_item_mapping pi ON CAST(pi.partner_item_id as int) = k.product_number\n", "    INNER JOIN po.edi_integration_partner_outlet_mapping nn on nn.partner_outlet_id=cast(k.buyer_outlet_id as varchar)\n", "    join dwh.dim_outlet do on do.outlet_id=nn.outlet_id and do.is_bistro_outlet=false\n", "    \n", "    where dt >= DATE_FORMAT(date(date('{start_date_str}') - interval '10' day), '%%Y%%m%%d') \n", "    and dt <= DATE_FORMAT(date(date('{start_date_str}') + interval '1' day), '%%Y%%m%%d')\n", "    and k.order_placed_at_ist + interval '330' minute >= date'{start_date_str}' - interval '10' day\n", "    and k.order_placed_at_ist + interval '330' minute < date'{start_date_str}' + interval '1' day\n", "    \n", "    and k.warehouse_code like 'CPC%%'\n", "    and k.order_status!='CANCELLED'\n", "    and k.order_tag!='DIRECT_DELIVERY'\n", "    and lower(k.buyer_outlet_name) not like '%%test outlet%%'\n", "    and k.warehouse_code not like '%%TEST%%'\n", "    \n", "    group by 1,2,3,4,5,6\n", "    \n", "\n", "    UNION ALL\n", "\n", "\n", "    select\n", "    cast(bill.date_ AS date) as date_,\n", "    '',\n", "    j.outlet_id as be_outlet_id,\n", "    j.frontend_outlet_id as fe_outlet_id,\n", "    ii.item_id,\n", "    'BL' as tg,\n", "    sum(bill.billed_qty) as sto_units\n", "\n", "\n", "    from po.sto j\n", "    join po.sto_items ii on ii.sto_id = j.id\n", "    left join rpc.item_category_details pm ON pm.item_id = ii.item_id\n", "    left join ds as ds1 on ds1.id=j.outlet_id\n", "    left join ds as ds2 on ds2.id=j.frontend_outlet_id\n", "    left join bill on try_cast(bill.grofers_order_id as int)=j.id and try_cast(bill.item_id as int)=ii.item_id\n", "\n", "\n", "    where j.created_at >= cast(date'{start_date_str}' - interval '10' day AS TIMESTAMP) AND j.created_at <= cast(date'{start_date_str}' + interval '1' day AS TIMESTAMP)\n", "    and ii.created_at >= cast(date'{start_date_str}' - interval '10' day AS TIMESTAMP) AND ii.created_at <= cast(date'{start_date_str}' + interval '1' day AS TIMESTAMP)\n", "    and ds1.id is null\n", "    and ds2.id is not null\n", "\n", "    group by 1,2,3,4,5,6\n", "    \n", "    \n", "    union all\n", "    \n", "    select\n", "    date(pg.created_at + interval '330' minute) as date_,\n", "    '',\n", "    10000 as be_outlet_id,\n", "    pg.outlet_id,\n", "    pg.item_id,\n", "    'DTS' as tg,\n", "    sum(pg.quantity) as quantity\n", "    \n", "    \n", "    from po.po_grn pg\n", "    join po.purchase_order po on pg.po_id=po.id and po.vendor_name not like '%%Zomato%%'\n", "    join retail.console_outlet co on co.id=pg.outlet_id and co.business_type_id=7\n", "    \n", "    where pg.insert_ds_ist between  cast(date'{start_date_str}' - interval '5' day AS varchar) and cast(date'{start_date_str}' + interval '1' day AS varchar)\n", "    group by 1,2,3,4,5,6\n", "    \n", "        \"\"\"\n", "\n", "    column_dtypes = [\n", "        {\"name\": \"date_\", \"type\": \"DATE\", \"description\": \"date_\"},\n", "        {\"name\": \"warehouse_code\", \"type\": \"VARCHAR\", \"description\": \"warehouse_code\"},\n", "        {\"name\": \"be_outlet_id\", \"type\": \"DOUBLE\", \"description\": \"be_outlet_id\"},\n", "        {\"name\": \"fe_outlet_id\", \"type\": \"DOUBLE\", \"description\": \"fe_outlet_id\"},\n", "        {\"name\": \"item_id\", \"type\": \"DOUBLE\", \"description\": \"item_id\"},\n", "        {\"name\": \"tagg\", \"type\": \"VARCHAR\", \"description\": \"type tag\"},\n", "        {\"name\": \"qty\", \"type\": \"DOUBLE\", \"description\": \"qty\"},\n", "    ]\n", "\n", "    table_description = \"daily ob units\"\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"fin_etls\",\n", "        \"table_name\": \"daily_outlet_item_ob\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"date_\", \"be_outlet_id\", \"fe_outlet_id\", \"item_id\"],\n", "        \"table_description\": table_description,\n", "        \"load_type\": \"upsert\",\n", "    }\n", "\n", "    pb.to_trino(data_obj=sql1, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["while start_date <= end_date:\n", "\n", "    print(f\"Running this query for {str(start_date.date())}\")\n", "    start_time_1 = datetime.now()\n", "\n", "    start_date_str = start_date.strftime(\"%Y-%m-%d\")\n", "\n", "    query(start_date_str)\n", "\n", "    print(\"----------------------------------------------------------\")\n", "    print(f\"Total Time taken = {datetime.now()-start_time_1}\")\n", "    print(\"----------------------------------------------------------\")\n", "\n", "    start_date = start_date + <PERSON><PERSON><PERSON>(days=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}