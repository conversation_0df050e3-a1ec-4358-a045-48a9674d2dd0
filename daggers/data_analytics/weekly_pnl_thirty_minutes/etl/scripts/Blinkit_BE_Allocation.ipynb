{"cells": [{"cell_type": "code", "execution_count": null, "id": "4355c1b9-4f91-4ccc-a6e2-290be520a0a5", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "from datetime import date\n", "from datetime import timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "eb09d248-b204-48f8-b418-2411ec2cdcde", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "ca4bad1c-b024-436b-81c0-3955ca9ef71b", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1bW-l6CozOxuwHa8WEta_pgsTboJ03GPFrYKexQMe9_o\"\n", "sheet_name = \"duration\"\n", "df_input = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "bb6db439-a38c-4eca-8356-3e26589cd79c", "metadata": {}, "outputs": [], "source": ["from_date = df_input.start_date[0]\n", "to_date = df_input.end_date[0]"]}, {"cell_type": "code", "execution_count": null, "id": "73f8d24f-7a0b-4e69-8b25-33df54af5247", "metadata": {}, "outputs": [], "source": ["# start_date = str(date.today() - <PERSON><PERSON><PERSON>(days=67))\n", "# end_date = str(date.today() - <PERSON><PERSON><PERSON>(days=38))\n", "# from_date = '2024-10-01'\n", "# to_date = '2024-10-21'"]}, {"cell_type": "code", "execution_count": null, "id": "9751a3c2-e2b8-44ac-a0bb-4ce0fa12db10", "metadata": {}, "outputs": [], "source": ["print(from_date)\n", "print(to_date)"]}, {"cell_type": "code", "execution_count": null, "id": "52cfdf4a-115f-497f-8262-d3db7c607342", "metadata": {}, "outputs": [], "source": ["query = (\n", "    \"\"\"\n", "\n", "with distinct_outlets AS (\n", "    SELECT distinct f.date_ist, \n", "    f.facility_id,\n", "    f.pos_outlet_id\n", "    FROM\n", "        fin_etls.outbound_blinkit_sgm f\n", "        WHERE f.date_ist >= date(date_trunc('month',date'{{from_date}}')) \n", "        AND f.date_ist < date(DATE_TRUNC('month', DATE_ADD('month', 1, date'{{to_date}}')))\n", "    GROUP BY 1,2,3\n", ")\n", "\n", ", count_ids as (\n", "    SELECT f.date_ist, \n", "    f.facility_id,\n", "    count(distinct f.pos_outlet_id) as outlets\n", "    FROM\n", "        fin_etls.outbound_blinkit_sgm f\n", "        WHERE f.date_ist >= date(date_trunc('month',date'{{from_date}}')) \n", "        AND f.date_ist < date(DATE_TRUNC('month', DATE_ADD('month', 1, date'{{to_date}}')))\n", "    GROUP BY 1,2\n", ")\n", "\n", "--WEEK LEVEL FIXED COST POST 14 OCT'24\n", ", fixed_bl_cost as (\n", "    select a.facility_id,\n", "    a.wh,\n", "    a.week,\n", "    d.date_ist,\n", "    a.bl_fixed_cost_daily,\n", "    d.facility_id as fid,\n", "    d.pos_outlet_id,\n", "    c.outlets,\n", "    CAST((cast(a.bl_fixed_cost_daily as DECIMAL(10, 2))/cast(c.outlets as DECIMAL(10, 2))) AS DECIMAL(10, 2)) AS fe_daily_bl_be_cost_fixed\n", "    from fin_etls.stg_thirty_minutes_bl_be_week_cost_sgm as a\n", "    inner join distinct_outlets as d on a.week = date(date_trunc('week',d.date_ist)) and a.facility_id = d.facility_id\n", "    inner join count_ids as c on c.facility_id = d.facility_id and c.date_ist = d.date_ist\n", "    where a.week is not null)\n", "\n", ", final_fix_allocation as (\n", "    select\n", "    date_ist as day,\n", "    week,\n", "    facility_id,\n", "    pos_outlet_id,\n", "    outlets,\n", "    bl_fixed_cost_daily,\n", "    fe_daily_bl_be_cost_fixed as fixed_cost_allocated_at_outlet,\n", "    (case when st.outlet_id is not null and f.date_ist between date(st.live_from_date_yyyy_mm_dd) and date(st.live_to_date) then 1\n", "           else (l.lbh_ratio) end) as lbh_ratio,\n", "    ((case when st.outlet_id is not null and f.date_ist between date(st.live_from_date_yyyy_mm_dd) and date(st.live_to_date) then 1\n", "           else (l.lbh_ratio) end)*fe_daily_bl_be_cost_fixed) as tm_fixed_blinkit_cost\n", "    from fixed_bl_cost as f\n", "    inner join fin_etls.thirty_min_lbh_sgm as l on f.pos_outlet_id = l.outlet_id and f.date_ist = l.day\n", "    left join fin_etls.thirty_minutes_standalone_stores_sgm as st on st.outlet_id = f.pos_outlet_id and st.outlet_id is not null\n", "    )\n", "\n", ", outlet_level_cost_fixed_week as (\n", "    select day,\n", "    week as duration,\n", "    pos_outlet_id,\n", "    sum(tm_fixed_blinkit_cost) as tm_fixed_blinkit_cost\n", "    from final_fix_allocation\n", "    group by 1,2,3)\n", "    \n", "--MON<PERSON> LEVEL FIXED COST PRE 14 OCT'24\n", ", fixed_bl_cost_month as (\n", "    select a.facility_id,\n", "    a.wh,\n", "    a.month,\n", "    d.date_ist,\n", "    a.bl_fixed_cost_daily,\n", "    d.facility_id as fid,\n", "    d.pos_outlet_id,\n", "    c.outlets,\n", "    CAST((cast(a.bl_fixed_cost_daily as DECIMAL(10, 2))/cast(c.outlets as DECIMAL(10, 2))) AS DECIMAL(10, 2)) AS fe_daily_bl_be_cost_fixed\n", "    from fin_etls.stg_thirty_minutes_bl_be_cost_sgm as a\n", "    inner join distinct_outlets as d on a.month = date(date_trunc('month',d.date_ist)) and a.facility_id = d.facility_id\n", "    inner join count_ids as c on c.facility_id = d.facility_id and c.date_ist = d.date_ist\n", "    where a.month is not null\n", "    -- and a.month < date'2024-10-01'\n", "    and d.date_ist between date'2024-04-01' and date'2024-10-13'\n", ")\n", "\n", ", final_fix_allocation_month as (\n", "    select\n", "    date_ist as day,\n", "    month,\n", "    facility_id,\n", "    pos_outlet_id,\n", "    outlets,\n", "    bl_fixed_cost_daily,\n", "    fe_daily_bl_be_cost_fixed as fixed_cost_allocated_at_outlet,\n", "    (case when st.outlet_id is not null and f.date_ist between date(st.live_from_date_yyyy_mm_dd) and date(st.live_to_date) then 1\n", "           else (l.lbh_ratio) end) as lbh_ratio,\n", "    ((case when st.outlet_id is not null and f.date_ist between date(st.live_from_date_yyyy_mm_dd) and date(st.live_to_date) then 1\n", "           else (l.lbh_ratio) end)*fe_daily_bl_be_cost_fixed) as tm_fixed_blinkit_cost\n", "    from fixed_bl_cost_month as f\n", "    inner join fin_etls.thirty_min_lbh_sgm as l on f.pos_outlet_id = l.outlet_id and f.date_ist = l.day\n", "    left join fin_etls.thirty_minutes_standalone_stores_sgm as st on st.outlet_id = f.pos_outlet_id and st.outlet_id is not null\n", ")\n", "\n", ", outlet_level_cost_fixed_month as (\n", "    select day,\n", "    month as duration,\n", "    pos_outlet_id,\n", "    sum(tm_fixed_blinkit_cost) as tm_fixed_blinkit_cost\n", "    from final_fix_allocation_month\n", "    group by 1,2,3\n", ")\n", "\n", ", outlet_level_cost_fixed as (\n", "   select * from outlet_level_cost_fixed_month\n", "   where day <= date'2024-10-13'\n", "   UNION ALL\n", "   select * from outlet_level_cost_fixed_week\n", "   where day > date'2024-10-13'\n", ")\n", "\n", "\n", "--WEEK VARIABLE COST POST 14 OCT'24\n", ", week_ob as (\n", "    SELECT\n", "        date_trunc('week', f.date_ist) AS week,\n", "        f.facility_id,\n", "        SUM(f.total_outbound) AS week_outbound\n", "    FROM fin_etls.outbound_blinkit_sgm f\n", "    WHERE f.date_ist >= date(DATE_TRUNC('week', date'{{from_date}}'))\n", "    AND f.date_ist < date(DATE_ADD('week', 1, DATE_TRUNC('week', date'{{to_date}}')))\n", "    -- and f.facility_id = 92\n", "    GROUP BY 1, 2\n", ")\n", "\n", ", day_ob as (\n", "    SELECT\n", "        date_trunc('day', f.date_ist) AS day,\n", "        f.facility_id,\n", "        SUM(f.total_outbound) AS day_outbound\n", "    FROM fin_etls.outbound_blinkit_sgm f\n", "    WHERE f.date_ist BETWEEN date '{{from_date}}' AND date '{{to_date}}'\n", "    -- and f.facility_id = 92\n", "    GROUP BY 1, 2\n", ")\n", "\n", ", month_ob as (\n", "    SELECT\n", "        date_trunc('month', f.date_ist) AS month,\n", "        f.facility_id,\n", "        SUM(f.total_outbound) AS month_outbound\n", "    FROM fin_etls.outbound_blinkit_sgm f\n", "    WHERE f.date_ist >= date(date_trunc('month',date'{{from_date}}')) \n", "    AND f.date_ist < date(DATE_TRUNC('month', DATE_ADD('month', 1, date'{{to_date}}')))\n", "    -- and f.facility_id = 92\n", "    GROUP BY 1, 2\n", ")\n", "\n", ", month_ob_oct as (\n", "    SELECT\n", "        date_trunc('month', f.date_ist) AS month,\n", "        f.facility_id,\n", "        SUM(f.total_outbound) AS month_outbound\n", "    FROM fin_etls.outbound_blinkit_sgm f\n", "    WHERE f.date_ist >= date(date_trunc('month',date'{{from_date}}')) \n", "    AND f.date_ist < date(DATE_TRUNC('month', DATE_ADD('month', 1, date'{{to_date}}')))\n", "    and f.date_ist >= date'2024-10-01'\n", "    and f.date_ist <= date'2024-10-13'\n", "    GROUP BY 1, 2\n", ")\n", "\n", "\n", ", day_to_week_ob_ratio as (\n", "    select d.day,\n", "    w.week,\n", "    d.facility_id,\n", "    d.day_outbound,\n", "    w.week_outbound,\n", "    cast(d.day_outbound as double)/cast(w.week_outbound as double) as day_to_week_ob_ratio\n", "    from day_ob as d\n", "    join week_ob as w on w.week = date(date_trunc('week',d.day))\n", "                      and w.facility_id = d.facility_id\n", ")\n", "\n", ", day_to_month_ob_ratio as (\n", "    select d.day,\n", "    m.month,\n", "    d.facility_id,\n", "    d.day_outbound,\n", "    m.month_outbound,\n", "    cast(d.day_outbound as double)/cast(m.month_outbound as double) as day_to_month_ob_ratio\n", "    from day_ob as d\n", "    join month_ob as m on m.month = date(date_trunc('month',d.day))\n", "                      and m.facility_id = d.facility_id\n", ")\n", "\n", ", day_to_month_ob_ratio_oct as (\n", "    select d.day,\n", "    m.month,\n", "    d.facility_id,\n", "    d.day_outbound,\n", "    m.month_outbound,\n", "    cast(d.day_outbound as double)/cast(m.month_outbound as double) as day_to_month_ob_ratio\n", "    from day_ob as d\n", "    join month_ob_oct as m on m.month = date(date_trunc('month',d.day))\n", "                      and m.facility_id = d.facility_id\n", ")\n", "\n", "----WEEK VARIABLE COST POST 13 OCT'24\n", ", variable_cost_daily as (\n", "    select a.day,\n", "    a.facility_id,\n", "    a.day_to_week_ob_ratio,\n", "    a.day_outbound,\n", "    a.week_outbound,\n", "    b.bl_variable_cost,\n", "    (b.bl_variable_cost*coalesce(a.day_to_week_ob_ratio,0)) as variable_daily_cost\n", "    from fin_etls.stg_thirty_minutes_bl_be_week_cost_sgm as b\n", "    join day_to_week_ob_ratio as a on date(date_trunc('week',a.day)) = b.week \n", "                                   and a.facility_id = b.facility_id\n", "    where b.week is not null\n", ")\n", "\n", ", variable_cost_allocation as (\n", "   select a.day,\n", "   a.facility_id,\n", "   a.variable_daily_cost,\n", "   b.pos_outlet_id,\n", "   b.total_outbound,\n", "   b.daily_total_fc_outbounds_unit,\n", "   (a.variable_daily_cost*(b.total_outbound/b.daily_total_fc_outbounds_unit)) as variable_outlet_allocated\n", "   from variable_cost_daily as a\n", "   join fin_etls.outbound_blinkit_sgm as b on a.day = b.date_ist\n", "                      and a.facility_id = b.facility_id\n", ")\n", "\n", ", outlet_level_cost_variable_pan as (\n", "  select a.day,\n", "  a.pos_outlet_id,\n", "  sum(variable_outlet_allocated) as variable_outlet_allocated\n", "  from variable_cost_allocation as a\n", "  group by 1,2\n", ")\n", "\n", ", tm_variable as (\n", "  select a.day,\n", "  a.pos_outlet_id,\n", "  a.variable_outlet_allocated as pan_variable_outlet,\n", "  (case when st.outlet_id is not null and a.day between date(st.live_from_date_yyyy_mm_dd) and date(st.live_to_date) then 1\n", "        else (o.ob_ratio) end) as ob_ratio,\n", "  ((case when st.outlet_id is not null and a.day between date(st.live_from_date_yyyy_mm_dd) and date(st.live_to_date) then 1\n", "        else (o.ob_ratio) end)*a.variable_outlet_allocated) as variable_outlet_allocated_tm\n", "  from outlet_level_cost_variable_pan as a\n", "  join fin_etls.thirty_min_outbound_units_sgm as o on o.day = a.day\n", "                                                  and o.outlet_id = a.pos_outlet_id\n", "  left join fin_etls.thirty_minutes_standalone_stores_sgm as st on st.outlet_id = a.pos_outlet_id and st.outlet_id is not null\n", ")\n", "\n", "----MONTH VARIABLE COST PRE 13 OCT'24\n", ", variable_cost_daily_month as (\n", "    select a.day,\n", "    a.facility_id,\n", "    a.day_to_month_ob_ratio,\n", "    a.day_outbound,\n", "    a.month_outbound,\n", "    b.bl_variable_cost,\n", "    (b.bl_variable_cost*coalesce(a.day_to_month_ob_ratio,0)) as variable_daily_cost\n", "    from fin_etls.stg_thirty_minutes_bl_be_cost_sgm as b\n", "    join day_to_month_ob_ratio as a on date(date_trunc('month',a.day)) = b.month \n", "                                   and a.facility_id = b.facility_id\n", "    where b.month < date'2024-10-01'\n", ")\n", "\n", ", variable_cost_allocation_month as (\n", "   select a.day,\n", "   a.facility_id,\n", "   a.variable_daily_cost,\n", "   b.pos_outlet_id,\n", "   b.total_outbound,\n", "   b.daily_total_fc_outbounds_unit,\n", "   (a.variable_daily_cost*(b.total_outbound/b.daily_total_fc_outbounds_unit)) as variable_outlet_allocated\n", "   from variable_cost_daily_month as a\n", "   join fin_etls.outbound_blinkit_sgm as b on a.day = b.date_ist\n", "                      and a.facility_id = b.facility_id\n", ")\n", "\n", ", outlet_level_cost_variable_pan_month as (\n", "  select a.day,\n", "  a.pos_outlet_id,\n", "  sum(variable_outlet_allocated) as variable_outlet_allocated\n", "  from variable_cost_allocation_month as a\n", "  group by 1,2\n", ")\n", "\n", ", tm_variable_month as (\n", "  select a.day,\n", "  a.pos_outlet_id,\n", "  a.variable_outlet_allocated as pan_variable_outlet,\n", "  (case when st.outlet_id is not null and a.day between date(st.live_from_date_yyyy_mm_dd) and date(st.live_to_date) then 1\n", "        else (o.ob_ratio) end) as ob_ratio,\n", "  ((case when st.outlet_id is not null and a.day between date(st.live_from_date_yyyy_mm_dd) and date(st.live_to_date) then 1\n", "        else (o.ob_ratio) end)*a.variable_outlet_allocated) as variable_outlet_allocated_tm\n", "  from outlet_level_cost_variable_pan_month as a\n", "  join fin_etls.thirty_min_outbound_units_sgm as o on o.day = a.day\n", "                                                  and o.outlet_id = a.pos_outlet_id\n", "  left join fin_etls.thirty_minutes_standalone_stores_sgm as st on st.outlet_id = a.pos_outlet_id and st.outlet_id is not null\n", ")\n", "\n", "-----VARIABLE FOR 1 OCT'24 TO 13 OCT'24\n", ", variable_cost_daily_month_oct as (\n", "    select a.day,\n", "    a.facility_id,\n", "    a.day_to_month_ob_ratio,\n", "    a.day_outbound,\n", "    a.month_outbound,\n", "    b.bl_variable_cost as bl_variable_cost,\n", "    (b.bl_variable_cost*coalesce(a.day_to_month_ob_ratio,0)) as variable_daily_cost\n", "    from fin_etls.stg_thirty_minutes_bl_be_cost_sgm as b\n", "    join day_to_month_ob_ratio_oct as a on date(date_trunc('month',a.day)) = b.month \n", "                                   and a.facility_id = b.facility_id\n", "    where b.month = date'2024-10-01'\n", "    and a.day >= date'2024-10-01'\n", "    and a.day <= date'2024-10-13'\n", ")\n", "\n", ", variable_cost_allocation_month_oct as (\n", "   select a.day,\n", "   a.facility_id,\n", "   a.variable_daily_cost,\n", "   b.pos_outlet_id,\n", "   b.total_outbound,\n", "   b.daily_total_fc_outbounds_unit,\n", "   (a.variable_daily_cost*(b.total_outbound/b.daily_total_fc_outbounds_unit)) as variable_outlet_allocated\n", "   from variable_cost_daily_month_oct as a\n", "   join fin_etls.outbound_blinkit_sgm as b on a.day = b.date_ist\n", "                      and a.facility_id = b.facility_id\n", ")\n", "\n", ", outlet_level_cost_variable_pan_month_oct as (\n", "  select a.day,\n", "  a.pos_outlet_id,\n", "  sum(variable_outlet_allocated) as variable_outlet_allocated\n", "  from variable_cost_allocation_month_oct as a\n", "  group by 1,2\n", ")\n", "\n", ", tm_variable_month_oct as (\n", "  select a.day,\n", "  a.pos_outlet_id,\n", "  a.variable_outlet_allocated as pan_variable_outlet,\n", "  (case when st.outlet_id is not null and a.day between date(st.live_from_date_yyyy_mm_dd) and date(st.live_to_date) then 1\n", "        else (o.ob_ratio) end) as ob_ratio,\n", "  ((case when st.outlet_id is not null and a.day between date(st.live_from_date_yyyy_mm_dd) and date(st.live_to_date) then 1\n", "        else (o.ob_ratio) end)*a.variable_outlet_allocated) as variable_outlet_allocated_tm\n", "  from outlet_level_cost_variable_pan_month_oct as a\n", "  join fin_etls.thirty_min_outbound_units_sgm as o on o.day = a.day\n", "                                                  and o.outlet_id = a.pos_outlet_id\n", "  left join fin_etls.thirty_minutes_standalone_stores_sgm as st on st.outlet_id = a.pos_outlet_id and st.outlet_id is not null\n", ")\n", "\n", "----------------------------------------------------------\n", "\n", ", tm_variable_cost as (\n", "  select * from tm_variable\n", "  UNION ALL\n", "  select * from tm_variable_month\n", "  UNION ALL\n", "  select * from tm_variable_month_oct\n", ")\n", "\n", "\n", "--------------------------------------------------------------------------------------------------------------------------\n", ", final_output as (select coalesce(f.day,v.day) as day,\n", "coalesce(f.pos_outlet_id,v.pos_outlet_id) as outlet_id,\n", "coalesce(f.tm_fixed_blinkit_cost,0) as fixed_blinkit_cost,\n", "coalesce(v.variable_outlet_allocated_tm,0) as variable_blinkit_cost\n", "from outlet_level_cost_fixed as f\n", "full outer join tm_variable_cost as v on f.day = v.day\n", "                                 and f.pos_outlet_id = v.pos_outlet_id\n", ")\n", "\n", "select * from final_output\n", "\n", "\"\"\".replace(\n", "        \"%\", \"%%\"\n", "    )\n", "    .replace(\"{{\", \"{\")\n", "    .replace(\"}}\", \"}\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "eb485601-3f38-4ddd-b87f-d3bdedb307a1", "metadata": {}, "outputs": [], "source": ["df = pd.read_sql(\n", "    query.format(from_date=from_date, to_date=to_date),\n", "    con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8a832a9c-f41a-4e56-bb5d-c97241b49f6b", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "871b4de5-bcfb-4a47-9cd8-6f86a3b54adc", "metadata": {}, "outputs": [], "source": ["df = df.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "636d984d-02c8-4d72-a56f-cc1e647aa002", "metadata": {}, "outputs": [], "source": ["df[\"day\"] = pd.to_datetime(df[\"day\"])"]}, {"cell_type": "code", "execution_count": null, "id": "334649d0-363d-47d9-943c-c5966720e718", "metadata": {}, "outputs": [], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "975e8828-246c-471a-938b-934a58f4d7f5", "metadata": {}, "outputs": [], "source": ["df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "facaef18-ff25-4691-869d-74ce8d570865", "metadata": {}, "outputs": [], "source": ["len(df)"]}, {"cell_type": "code", "execution_count": null, "id": "e669d768-863f-4364-b6a0-fca05a47022d", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"fin_etls\",  # Redshift schema name\n", "    \"table_name\": \"thirty_minutes_blinkit_be_allocation_sgm\",  # Redshift table name\n", "    \"column_dtypes\": [\n", "        {\"name\": \"day\", \"type\": \"DATE\", \"description\": \"date of creation of order\"},\n", "        {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"outlet id\"},\n", "        {\"name\": \"fixed_blinkit_cost\", \"type\": \"DOUBLE\", \"description\": \"cart id\"},\n", "        {\"name\": \"variable_blinkit_cost\", \"type\": \"DOUBLE\", \"description\": \"order id\"},\n", "    ],\n", "    \"primary_key\": [\"day\", \"outlet_id\"],  # list\n", "    \"partition_key\": [\"day\"],  # list\n", "    \"incremental_key\": [],  # string\n", "    \"load_type\": \"partition_overwrite\",  # append, rebuild, truncate or upsert\n", "    \"force_upsert_without_increment_check\": True,\n", "    \"table_description\": \"this table is made temporary data push\",  # feedback\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "d7fbfd27-d8b8-45a0-b646-02953b5a5d7f", "metadata": {}, "outputs": [], "source": ["pb.to_trino(df, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}