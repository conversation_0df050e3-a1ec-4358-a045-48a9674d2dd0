alert_configs:
  slack:
  - channel: bl-personalisation-dag-failures
dag_name: frequently_bought_group_recommendations
dag_type: report
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: very-high-mem
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
  retries: 2
  retry_delay_in_seconds: 60
owner:
  email: <EMAIL>
  slack_id: U03RQJY8HAT
path: data_science/frequently_bought_group_recommendations/report/frequently_bought_group_recommendations
paused: false
pool: data_science_pool
project_name: frequently_bought_group_recommendations
schedule:
  end_date: '2025-09-15T00:00:00'
  interval: 30 23 * * *
  start_date: '2025-06-18T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- kafka_utils.py
tags: []
template_name: notebook
version: 17
