{"cells": [{"cell_type": "code", "execution_count": null, "id": "93704587-666e-4ef6-b79b-aa00680d1887", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys"]}, {"cell_type": "code", "execution_count": null, "id": "32bfa1eb-81c2-45f2-8f13-12ae71f08f50", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "acf85dea-f840-4c27-9939-b79a307d8186", "metadata": {"tags": []}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "69f90bbd-25ca-4235-9c80-675973c0d484", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "id": "080ff760-27b2-4329-b0d0-7b21d4e57b35", "metadata": {}, "outputs": [], "source": ["con_trino = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "259a9af2-2b30-42a0-99e9-600bba7f49b6", "metadata": {}, "outputs": [], "source": ["_retry_for = [429, 500, 503]\n", "\n", "\n", "def from_sheets(sheet_id, sheet_name, max_tries=5):\n", "    for attempt in range(max_tries):\n", "        print(f\"Trying to read attempt: {attempt}...\")\n", "        try:\n", "            return pb.from_sheets(sheet_id, sheet_name)\n", "            break\n", "        except APIError as e:\n", "            exception_code = e.response.json()[\"error\"][\"code\"]\n", "            print(e)\n", "            if exception_code in _retry_for:\n", "                time.sleep(60)"]}, {"cell_type": "markdown", "id": "6bcb740d-9d92-4ace-888c-2182b50384f7", "metadata": {}, "source": ["# Blacklist"]}, {"cell_type": "code", "execution_count": null, "id": "92aefa4b-89b5-443e-91cf-ea126834fe0b", "metadata": {}, "outputs": [], "source": ["blacklist = from_sheets(\"17Q4P7HGhYLMnW4AqRWL4Jh_a5nT5DCeuK176CM2jgoE\", \"Blacklist_v2\")\n", "# blacklist = pd.read_csv(\"blacklist.csv\")\n", "blacklist = blacklist[blacklist[\"widget\"].isin([\"Global\", \"Curated For You\"])]\n", "\n", "\n", "def checknull(st):\n", "    if len(st) == 0:\n", "        st = st + \"0\"\n", "    return st\n", "\n", "\n", "b_l0 = checknull(\",\".join([x for x in list(blacklist[\"l0\"].values) if x != \"\"]))\n", "b_l1 = checknull(\",\".join([x for x in list(blacklist[\"l1\"].values) if x != \"\"]))\n", "b_pids = checknull(\",\".join([x for x in list(blacklist[\"pid\"].values) if x != \"\"]))\n", "b_ptype_ids = checknull(\",\".join([x for x in list(blacklist[\"ptype\"].values) if x != \"\"]))\n", "print(b_l0, b_l1, b_pids, b_ptype_ids)"]}, {"cell_type": "markdown", "id": "0c299158-764e-4a4f-ad90-15c122372ae5", "metadata": {}, "source": ["# Check if the sheet has faulty data"]}, {"cell_type": "code", "execution_count": null, "id": "bb2b1541-dc25-439f-b0e0-6e566298ad9b", "metadata": {}, "outputs": [], "source": ["df_sheet_mapping = from_sheets(\"1p0N6KkgAQDn8GEi-FzOUBIKgSuMEz9iAWic4rXmVMWg\", \"Updated Prod Sheet\")\n", "# df_sheet_mapping = pd.read_csv('Ptype Grouping_ Prod  - Updated Prod Sheet.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "3c55aef0-4908-49d3-93e3-33f81a29a03a", "metadata": {}, "outputs": [], "source": ["sheet_columns = df_sheet_mapping.columns.tolist()"]}, {"cell_type": "code", "execution_count": null, "id": "ae229420-b0d5-4db8-a435-7969ba59feb9", "metadata": {}, "outputs": [], "source": ["columns_to_consider = [x for x in df_sheet_mapping.columns if \"id_\" in x]"]}, {"cell_type": "code", "execution_count": null, "id": "81fe08de-7088-46b1-9acf-5feae5482ae5", "metadata": {}, "outputs": [], "source": ["df_sheet_mapping = df_sheet_mapping[df_sheet_mapping[\"discard\"] != \"1\"]"]}, {"cell_type": "code", "execution_count": null, "id": "373fbab6-fe0f-439b-9d52-22c6f04153e1", "metadata": {}, "outputs": [], "source": ["melted_groups = pd.melt(\n", "    df_sheet_mapping, id_vars=[\"group_name\", \"group_id\"], value_vars=columns_to_consider\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "670c8958-e73b-49a9-bbfa-be2795d12623", "metadata": {}, "outputs": [], "source": ["melted_groups = melted_groups[melted_groups[\"value\"] != \"\"]"]}, {"cell_type": "code", "execution_count": null, "id": "7f53b2c3-9ebb-455b-9a8b-6a1f51641ea0", "metadata": {}, "outputs": [], "source": ["melted_groups = melted_groups[~melted_groups[\"value\"].isna()]"]}, {"cell_type": "code", "execution_count": null, "id": "3444168d-3172-49a1-85b2-df8bb2434d09", "metadata": {}, "outputs": [], "source": ["melted_groups\n", "melted_groups[\"extracted_ptype\"] = melted_groups[\"value\"].apply(lambda x: x.split(\"||\")[1])"]}, {"cell_type": "code", "execution_count": null, "id": "ab87c358-be4b-4a48-abf6-6bccb4038703", "metadata": {}, "outputs": [], "source": ["melted_groups[\"l1_flag\"] = melted_groups[\"value\"].apply(\n", "    lambda x: 1 if \"(L1)\" in x.split(\"||\")[0] else 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f140346b-250e-4f86-933e-3c3351708724", "metadata": {}, "outputs": [], "source": ["melted_groups = melted_groups[[\"group_name\", \"group_id\", \"extracted_ptype\", \"l1_flag\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "92334bcb-ae89-47fd-8752-1cefebf7216c", "metadata": {}, "outputs": [], "source": ["df_group_combinations = from_sheets(\n", "    \"1p0N6KkgAQDn8GEi-FzOUBIKgSuMEz9iAWic4rXmVMWg\", \"Updated group_combinations\"\n", ")\n", "# df_group_combinations = pd.read_csv('Ptype Grouping_ Prod  - Updated group_combinations.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "b3509ee0-6111-4f9c-afc5-ba4c0f924640", "metadata": {}, "outputs": [], "source": ["melted_groups = melted_groups.merge(df_group_combinations, how=\"left\")\n", "\n", "melted_groups = melted_groups[\n", "    [\n", "        \"merge_group\",\n", "        \"merge_group_id\",\n", "        \"group_name\",\n", "        \"group_id\",\n", "        \"extracted_ptype\",\n", "        \"l1_flag\",\n", "    ]\n", "]\n", "melted_groups = melted_groups[melted_groups[\"group_id\"] != \"\"]"]}, {"cell_type": "code", "execution_count": null, "id": "a1e85a89-8a71-485d-8f77-f5c6c20a9252", "metadata": {}, "outputs": [], "source": ["melted_groups.head()"]}, {"cell_type": "code", "execution_count": null, "id": "82873ffa-7d90-4c47-a303-1eeecb5a2e8b", "metadata": {}, "outputs": [], "source": ["sql_product_metadata = \"\"\"\n", "select\n", "    product_id,\n", "    product_type_id,\n", "    l1_category_id\n", "    from dwh.dim_product \n", "        as dp\n", "where is_current\n", "    and is_product_enabled\n", "\"\"\"\n", "# con = pb.get_connection(\"redpen\")"]}, {"cell_type": "code", "execution_count": null, "id": "04c04dde-15a0-4002-a1b8-ddcdbb0f2cac", "metadata": {}, "outputs": [], "source": ["df_product_info = pd.read_sql(sql_product_metadata, con_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "8451c739-011e-4669-923e-709cf9d117bf", "metadata": {}, "outputs": [], "source": ["ptypes = melted_groups.query(\"l1_flag==0\")"]}, {"cell_type": "code", "execution_count": null, "id": "790d75e9-3157-4bbd-9a13-eae62a41b145", "metadata": {}, "outputs": [], "source": ["l1 = melted_groups.query(\"l1_flag==1\")"]}, {"cell_type": "code", "execution_count": null, "id": "a0bd1d28-9d06-497d-ae79-a9f1d6348e3e", "metadata": {"tags": []}, "outputs": [], "source": ["l1[\"extracted_ptype\"] = l1[\"extracted_ptype\"].astype(int)\n", "ptypes[\"extracted_ptype\"] = ptypes[\"extracted_ptype\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "4d611421-425f-4d64-8ba8-c82ce3f76469", "metadata": {}, "outputs": [], "source": ["ptypes = ptypes.merge(df_product_info, left_on=\"extracted_ptype\", right_on=\"product_type_id\")"]}, {"cell_type": "code", "execution_count": null, "id": "b7df41dd-2eae-4aac-8169-21aa7f739606", "metadata": {}, "outputs": [], "source": ["l1 = l1.merge(df_product_info, left_on=\"extracted_ptype\", right_on=\"l1_category_id\")"]}, {"cell_type": "code", "execution_count": null, "id": "ee378349-8283-406f-8d38-d0c11781f512", "metadata": {}, "outputs": [], "source": ["try:\n", "    ptypes.groupby(\"product_id\").group_id.nunique().reset_index(name=\"group_count\").query(\n", "        \"group_count>1\"\n", "    ).shape[0] == 0\n", "except exception as e:\n", "    # pb.send_slack_message(\n", "    #     channel=\"bl-personalization-notifications\",\n", "    #     text=\"Product belongs to more than one ptype - grouping sheet\",\n", "    # )\n", "    notif_string1 = f\"\"\"\n", "    🟠 <PERSON><PERSON><PERSON><PERSON>.\n", "       Dag: data_science/frequently_bought_group_recommendations/report/frequently_bought_group_recommendations\n", "       Owner: <PERSON><PERSON>\n", "       Metadata: Product belongs to more than one ptype - grouping sheet\n", "    \"\"\"\n", "    pb.send_slack_message(channel=\"bl-personalization-notifications\", text=notif_string1)\n", "    raise exception(\"fail DAG\")"]}, {"cell_type": "code", "execution_count": null, "id": "cef872c5-e4ac-4853-8e84-bb641689e374", "metadata": {}, "outputs": [], "source": ["try:\n", "    l1.groupby(\"product_id\").group_id.nunique().reset_index(name=\"group_count\").query(\n", "        \"group_count>1\"\n", "    ).shape[0] == 0\n", "except exception as e:\n", "    # pb.send_slack_message(\n", "    #     channel=\"bl-personalization-notifications\",\n", "    #     text=\"Product belongs to more than one L1 - grouping sheet\",\n", "    # )\n", "    notif_string2 = f\"\"\"\n", "    🟠 <PERSON><PERSON><PERSON><PERSON>.\n", "       Dag: data_science/frequently_bought_group_recommendations/report/frequently_bought_group_recommendations\n", "       Owner: <PERSON><PERSON>\n", "       Metadata: Product belongs to more than one L1 - grouping sheet\n", "    \"\"\"\n", "    pb.send_slack_message(channel=\"bl-personalization-notifications\", text=notif_string2)\n", "    raise exception(\"fail DAG\")"]}, {"cell_type": "code", "execution_count": null, "id": "37f3666e-933c-4526-906d-fcdb61fe0864", "metadata": {}, "outputs": [], "source": ["final_check_df = pd.DataFrame()\n", "final_check_df = final_check_df.append(ptypes)\n", "final_check_df = final_check_df.append(l1)"]}, {"cell_type": "code", "execution_count": null, "id": "27309dc7-2ff6-4e03-81f6-7fb091f641db", "metadata": {}, "outputs": [], "source": ["final_check_df = final_check_df.drop_duplicates(subset=[\"product_id\", \"group_id\"], keep=\"first\")"]}, {"cell_type": "code", "execution_count": null, "id": "da959a14-c007-40e9-9c27-4be43c28f35b", "metadata": {}, "outputs": [], "source": ["final_check_df_agg = final_check_df.groupby(\"product_id\")[\"group_name\"].apply(list).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "1585bd3b-c79d-4384-b8c8-9f0c03968d68", "metadata": {}, "outputs": [], "source": ["final_check_df_agg[\"len\"] = final_check_df_agg[\"group_name\"].apply(len)"]}, {"cell_type": "code", "execution_count": null, "id": "d19a54f3-6542-4604-8ece-3b4c5b9d413d", "metadata": {}, "outputs": [], "source": ["final_check_df_agg[\"group_name\"] = final_check_df_agg[\"group_name\"].apply(str)"]}, {"cell_type": "code", "execution_count": null, "id": "9667ce0c-71dd-44d5-9d10-b52a0544c102", "metadata": {}, "outputs": [], "source": ["final_check_df_agg = final_check_df_agg[final_check_df_agg[\"len\"] > 1].drop_duplicates(\n", "    subset=[\"group_name\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6ada80a9-11b7-4ad5-af2c-90d4b7978ab3", "metadata": {}, "outputs": [], "source": ["try:\n", "    final_check_df_agg.shape[0] == 0\n", "except exception as e:\n", "    # pb.send_slack_message(\n", "    #     channel=\"bl-personalization-notifications\", text=\"Grouping check failed\"\n", "    # )\n", "    notif_string3 = f\"\"\"\n", "    🟠 <PERSON><PERSON><PERSON><PERSON>.\n", "       Dag: data_science/frequently_bought_group_recommendations/report/frequently_bought_group_recommendations\n", "       Owner: <PERSON><PERSON>\n", "       Metadata: Grouping check failed\n", "    \"\"\"\n", "    pb.send_slack_message(channel=\"bl-personalization-notifications\", text=notif_string3)\n", "\n", "    if final_check_df_agg.shape[0] > 0:\n", "        tables = []\n", "        n = 10\n", "        list_df = [final_check_df_agg[i : i + n] for i in range(0, final_check_df_agg.shape[0], n)]\n", "\n", "        for chunked_df in list_df:\n", "            ascii_table = (\n", "                \"```\"\n", "                + tabulate(\n", "                    chunked_df[\n", "                        [\n", "                            \"product_id\",\n", "                            \"group_name\",\n", "                            \"len\",\n", "                        ]\n", "                    ],\n", "                    tablefmt=\"pipe\",\n", "                    headers=\"keys\",\n", "                    showindex=False,\n", "                )\n", "                + \"```\"\n", "            )\n", "            tables.append(ascii_table)\n", "        # pb.send_slack_message(\n", "        #     channel=\"bl-personalization-notifications\",\n", "        #     text=\":exclamation: *Products which are in more than 1 group*\\n\",\n", "        # )\n", "        notif_string4 = f\"\"\"\n", "        🟠 <PERSON><PERSON><PERSON><PERSON>.\n", "           Dag: data_science/frequently_bought_group_recommendations/report/frequently_bought_group_recommendations\n", "           Owner: <PERSON><PERSON>\n", "           Metadata: :exclamation: *Products which are in more than 1 group*\\n\n", "        \"\"\"\n", "        pb.send_slack_message(channel=\"bl-personalization-notifications\", text=notif_string4)\n", "        for table in tables:\n", "            pb.send_slack_message(channel=\"bl-personalization-notifications\", text=table)\n", "\n", "        raise exception(\"fail DAG\")"]}, {"cell_type": "code", "execution_count": null, "id": "92aa5b04-772e-44d6-add0-74daee5d678b", "metadata": {}, "outputs": [], "source": ["final_group_id_name_merge_df = pd.DataFrame()\n", "final_group_id_name_merge_df = final_group_id_name_merge_df.append(\n", "    ptypes[[\"product_type_id\", \"group_id\", \"group_name\"]]\n", ")\n", "final_group_id_name_merge_df = final_group_id_name_merge_df.append(\n", "    l1[[\"product_type_id\", \"group_id\", \"group_name\"]]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "491dd59d-5ca1-4844-821a-c3e1949e7c3a", "metadata": {}, "outputs": [], "source": ["final_group_id_name_merge_df = final_group_id_name_merge_df.drop_duplicates(\n", "    subset=[\"product_type_id\", \"group_id\"], keep=\"first\"\n", ")"]}, {"cell_type": "raw", "id": "d4e771e2-6c5f-4ece-a362-6cd3dbd01f4b", "metadata": {}, "source": ["df_group_intelligence = from_sheets(\"1p0N6KkgAQDn8GEi-FzOUBIKgSuMEz9iAWic4rXmVMWg\", \"Group Intelligence\")"]}, {"cell_type": "raw", "id": "4f4e67c9-9295-46dc-8e44-ecd630efda6b", "metadata": {}, "source": ["df_group_intelligence = df_group_intelligence[df_group_intelligence[\"more_in_flag\"] != \"\"]\n", "df_group_intelligence = df_group_intelligence[df_group_intelligence[\"you_might_also_like_flag\"] != \"\"]\n", "df_group_intelligence = df_group_intelligence[df_group_intelligence[\"group_id\"] != \"\"]"]}, {"cell_type": "raw", "id": "0257063f-89a4-45a5-a405-285da44286ae", "metadata": {}, "source": ["df_group_intelligence[\"more_in_flag\"] = df_group_intelligence[\"more_in_flag\"].astype(int)\n", "df_group_intelligence[\"you_might_also_like_flag\"] = df_group_intelligence[\"you_might_also_like_flag\"].astype(int)\n", "df_group_intelligence[\"group_id\"] = df_group_intelligence[\"group_id\"].astype(int)"]}, {"cell_type": "markdown", "id": "a7dfcdfb-a794-465b-b2ab-b4bcac6c6fbb", "metadata": {"tags": []}, "source": ["# You might also like"]}, {"cell_type": "raw", "id": "0f97ca84-fbc6-4961-9e11-311f783e88d0", "metadata": {}, "source": ["df_complementary_ptypes = from_sheets(\"1-TAGan8KKBxBuR_lkDXZ6xxAzRqAoPpj2uCNnc_5Efw\", \"prod_v5\")"]}, {"cell_type": "raw", "id": "119c4488-c3c0-41bd-80e0-4787547ce704", "metadata": {}, "source": ["columns_list = df_complementary_ptypes.columns.tolist()"]}, {"cell_type": "raw", "id": "02f8923c-b6d2-4e5b-ad87-1ed0017ab355", "metadata": {}, "source": ["columns_to_melt = [x for x in columns_list if x.isnumeric()]"]}, {"cell_type": "raw", "id": "a335fe12-4fcd-44da-813e-bdac0ec97c92", "metadata": {}, "source": ["df_complementary_ptypes = df_complementary_ptypes.loc[\n", "    df_complementary_ptypes[\"product_type\"].str.contains(r\"\\|\\|\")\n", "]"]}, {"cell_type": "raw", "id": "d7f8a5cb-3244-43ff-bc0f-f8434e2f9089", "metadata": {}, "source": ["melted_complementary_ptypes = pd.melt(\n", "    df_complementary_ptypes, id_vars=[\"product_type\"], value_vars=columns_to_melt\n", ")"]}, {"cell_type": "raw", "id": "67c2a08a-aa58-4930-affa-cdbc19a5e682", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes[\n", "    ~melted_complementary_ptypes[\"value\"].isna()\n", "]"]}, {"cell_type": "raw", "id": "ef6672e9-8463-4173-8862-134d2ed511a4", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes[\n", "    melted_complementary_ptypes[\"value\"] != \"\"\n", "]"]}, {"cell_type": "raw", "id": "f7f2dcf1-3395-4087-9af2-988bc2244198", "metadata": {}, "source": ["melted_complementary_ptypes[\"product_type_id\"] = melted_complementary_ptypes[\"product_type\"].apply(\n", "    lambda x: x.split(\"||\")[1]\n", ")\n", "melted_complementary_ptypes[\"product_type\"] = melted_complementary_ptypes[\"product_type\"].apply(\n", "    lambda x: x.split(\"||\")[0]\n", ")\n", "melted_complementary_ptypes[\"value_id\"] = melted_complementary_ptypes[\"value\"].apply(\n", "    lambda x: x.split(\"||\")[1]\n", ")\n", "melted_complementary_ptypes[\"value\"] = melted_complementary_ptypes[\"value\"].apply(\n", "    lambda x: x.split(\"||\")[0]\n", ")"]}, {"cell_type": "raw", "id": "b622e929-782b-4bb1-abfa-67d8fac3178a", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes.sort_values(\n", "    by=[\"product_type\", \"variable\"], ascending=[True, True]\n", ")"]}, {"cell_type": "raw", "id": "38030fa5-0c24-43db-88fe-fe6bc8da726a", "metadata": {}, "source": ["melted_complementary_ptypes.drop(columns={\"variable\"}, inplace=True)"]}, {"cell_type": "raw", "id": "4a6c2c66-688b-4d57-867f-8d395ebc7a75", "metadata": {}, "source": ["melted_complementary_ptypes.rename(\n", "    columns={\"product_type\": \"ptype\", \"value\": \"recommended_ptype\"}, inplace=True\n", ")"]}, {"cell_type": "raw", "id": "f2a03d04-4848-4c83-b5a9-355e1f8bd5c1", "metadata": {}, "source": ["melted_complementary_ptypes[\"recommended_ptype_rank\"] = (\n", "    melted_complementary_ptypes.groupby([\"ptype\"]).cumcount() + 1\n", ")"]}, {"cell_type": "raw", "id": "3815cd1f-bbb3-4af0-9800-9598ae5bc78a", "metadata": {}, "source": ["# sql_product_metadata = \"\"\"\n", "# select\n", "#     product_type,\n", "#     product_type_id\n", "#     from dwh.dim_product\n", "#         as dp\n", "# where is_current\n", "#     and is_product_enabled\n", "# group by 1,2\n", "# \"\"\"\n", "# con_redshift = pb.get_connection(\"redpen\")\n", "# df_ptype_data = pd.read_sql(sql_product_metadata, con_redshift)"]}, {"cell_type": "raw", "id": "43fef4dc-8a1b-486d-b1b0-b0e5bae57998", "metadata": {}, "source": ["# melted_complementary_ptypes = melted_complementary_ptypes.merge(\n", "#     df_ptype_data, left_on=\"ptype\", right_on=\"product_type\"\n", "# )"]}, {"cell_type": "raw", "id": "002fb690-d31e-4012-8ccf-48c14e14e6a2", "metadata": {}, "source": ["# melted_complementary_ptypes.drop(columns=\"product_type\", inplace=True)"]}, {"cell_type": "raw", "id": "d2afd9ea-5e43-47f0-9690-6b62d38e4d76", "metadata": {}, "source": ["melted_complementary_ptypes.rename(columns={\"product_type_id\": \"ptype_id\"}, inplace=True)"]}, {"cell_type": "raw", "id": "a19c539f-6475-450e-9137-f688af08fe97", "metadata": {}, "source": ["# melted_complementary_ptypes = melted_complementary_ptypes.merge(\n", "#     df_ptype_data, left_on=\"recommended_ptype\", right_on=\"product_type\"\n", "# )"]}, {"cell_type": "raw", "id": "a4919212-80fe-4b4a-b042-30cc4dbf88d5", "metadata": {}, "source": ["# melted_complementary_ptypes.rename(\n", "#     columns={\"product_type_id\": \"recommended_ptype_id\"}, inplace=True\n", "# )\n", "melted_complementary_ptypes.rename(columns={\"value_id\": \"recommended_ptype_id\"}, inplace=True)"]}, {"cell_type": "raw", "id": "147db8d4-268a-4caa-ae73-fa5a2ed0c6eb", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes.sort_values(\n", "    by=[\"ptype\", \"recommended_ptype_rank\"], ascending=[True, True]\n", ")"]}, {"cell_type": "raw", "id": "d0e99b30-d03e-45a2-9e6c-a4a94df32f82", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes[\n", "    [\"ptype_id\", \"recommended_ptype_id\", \"recommended_ptype_rank\"]\n", "]"]}, {"cell_type": "raw", "id": "55764813-787b-47ce-8a28-1e906fa9b623", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes[\n", "    [\"ptype_id\", \"recommended_ptype_id\", \"recommended_ptype_rank\"]\n", "]"]}, {"cell_type": "raw", "id": "0fdb7b3f-e15d-428e-b9ab-0dd8bab6677c", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes.sort_values(\n", "    by=[\"ptype_id\", \"recommended_ptype_rank\"], ascending=[True, True]\n", ")"]}, {"cell_type": "raw", "id": "882a191c-0eb5-4250-8bb5-641d<PERSON>dab5d", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes.drop_duplicates(\n", "    subset=[\"ptype_id\", \"recommended_ptype_id\"], keep=\"first\"\n", ")"]}, {"cell_type": "raw", "id": "4af0ccb3-da7a-4667-901d-3933f0c00970", "metadata": {}, "source": ["melted_complementary_ptypes[\"ptype_id\"] = melted_complementary_ptypes[\"ptype_id\"].astype(int)\n", "melted_complementary_ptypes[\"recommended_ptype_id\"] = melted_complementary_ptypes[\n", "    \"recommended_ptype_id\"\n", "].astype(int)"]}, {"cell_type": "raw", "id": "eab806ce-063b-4b55-ae58-5bbfe9c0ed80", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes.sort_values(\n", "    by=[\"ptype_id\", \"recommended_ptype_rank\"], ascending=[True, True]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "777a407d-463b-46fb-a223-a0edacd34c5b", "metadata": {}, "outputs": [], "source": ["sql_sales = \"\"\"\n", "with stable_cities as (\n", "select distinct city_name from\n", "dwh.fact_sales_order_details\n", "where order_create_dt_ist >= current_date - interval '30' day\n", "and is_internal_order = False\n", "group by 1\n", "having count(distinct order_id) >= 1000)\n", "\n", "select * from\n", "((select\n", "    city_name,\n", "    dp.product_type_id,\n", "    dp.product_id,\n", "    count(distinct order_id) as orders_count\n", "from dwh.fact_sales_order_item_details as fsoid\n", "inner join dwh.dim_product as dp on fsoid.product_id = dp.product_id and dp.is_current and dp.is_product_enabled\n", "where fsoid.order_create_dt_ist >= current_date - interval '60' day\n", "and dp.l1_category_id not in ({l1_ids})\n", "and dp.l0_category_id not in ({l0_ids})\n", "and dp.product_type_id not in ({p_type_ids})\n", "and dp.product_id not in ({pids})\n", "and fsoid.city_name in (select city_name from stable_cities)\n", "group by 1,2,3\n", "order by 1 asc, 2 asc, 4 desc)\n", "union all\n", "(select\n", "    'pan_india' as city_name,\n", "    dp.product_type_id,\n", "    dp.product_id,\n", "    count(distinct order_id) as orders_count\n", "from dwh.fact_sales_order_item_details as fsoid\n", "inner join dwh.dim_product as dp on fsoid.product_id = dp.product_id and dp.is_current and dp.is_product_enabled\n", "where fsoid.order_create_dt_ist >= current_date - interval '60' day\n", "and dp.l1_category_id not in ({l1_ids})\n", "and dp.l0_category_id not in ({l0_ids})\n", "and dp.product_type_id not in ({p_type_ids})\n", "and dp.product_id not in ({pids})\n", "group by 1,2,3\n", "order by 1 asc, 2 asc, 4 desc))\n", "\"\"\".format(\n", "    l0_ids=b_l0, l1_ids=b_l1, pids=b_pids, p_type_ids=b_ptype_ids\n", ")\n", "df_product_sales = pd.read_sql(sql_sales, con_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "1bf7bc88-92c5-41c6-b4d5-f71d4c599d0d", "metadata": {}, "outputs": [], "source": ["df_product_sales.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "2fc43f95-880c-41c9-9fe2-fe5bd672505b", "metadata": {}, "outputs": [], "source": ["df_product_sales = df_product_sales[~df_product_sales[\"city_name\"].isna()]"]}, {"cell_type": "code", "execution_count": null, "id": "1499c1c6-8a31-40ec-aa00-288bdd07553e", "metadata": {}, "outputs": [], "source": ["df_product_sales[\"product_rank\"] = (\n", "    df_product_sales.groupby([\"city_name\", \"product_type_id\"]).cumcount() + 1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "13807cc8-69d2-49f1-8c40-65c63f4cfdbe", "metadata": {}, "outputs": [], "source": ["df_product_sales = df_product_sales[df_product_sales[\"product_rank\"] <= 50]"]}, {"cell_type": "code", "execution_count": null, "id": "34052fd7-7dae-448e-9e72-de200b4f0f0a", "metadata": {}, "outputs": [], "source": ["sql_products_info = \"\"\"\n", "select\n", "    dp.product_id,\n", "    dp.product_name\n", "    from dwh.dim_product \n", "        as dp\n", "where dp.is_current\n", "    and dp.is_product_enabled\n", "group by 1,2\n", "\"\"\"\n", "df_product_info = pd.read_sql(sql_products_info, con_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "98760dc0-e724-4bc0-87eb-7352ea22283e", "metadata": {}, "outputs": [], "source": ["final_group_id_name_merge_df = final_group_id_name_merge_df.merge(\n", "    df_product_sales, left_on=\"product_type_id\", right_on=\"product_type_id\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7593ba37-0f4b-4f68-906c-88246f88dd66", "metadata": {}, "outputs": [], "source": ["final_group_id_name_merge_df.head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "d0d91d4e-978e-4962-a918-334f67686878", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "raw", "id": "25dd52a2-7066-4a78-bc28-6012b077c5ce", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes.merge(\n", "    df_product_sales, left_on=\"recommended_ptype_id\", right_on=\"product_type_id\"\n", ")"]}, {"cell_type": "raw", "id": "979f38b6-692a-44bb-8609-b1d754ee7da0", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes[\n", "    melted_complementary_ptypes[\"ptype_id\"] != melted_complementary_ptypes[\"recommended_ptype_id\"]\n", "]"]}, {"cell_type": "raw", "id": "0f5006e3-d2a0-419e-bfa1-441f5b97bcc5", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes.sort_values(\n", "    by=[\"city_name\", \"ptype_id\", \"product_rank\", \"recommended_ptype_rank\"],\n", "    ascending=[True, True, True, True],\n", ")"]}, {"cell_type": "raw", "id": "0af338ca-2ba5-499e-a011-c1222d656726", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes.merge(\n", "    final_group_id_name_merge_df,\n", "    left_on=\"recommended_ptype_id\",\n", "    right_on=\"product_type_id\",\n", ")"]}, {"cell_type": "raw", "id": "a6f0e34f-6f39-4468-88f1-54bbf16505b0", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes.sort_values(\n", "    by=[\"city_name\", \"ptype_id\", \"product_rank\", \"recommended_ptype_rank\"],\n", "    ascending=[True, True, True, True],\n", ")"]}, {"cell_type": "raw", "id": "161c160f-eaac-4385-9fdf-51ee9f034915", "metadata": {}, "source": ["melted_complementary_ptypes[\"group_name_rank\"] = (\n", "    melted_complementary_ptypes.groupby([\"group_name\"])[\"ptype_id\"].cumcount() + 1\n", ")"]}, {"cell_type": "raw", "id": "f3dd6ab1-518f-4b1e-8d72-daede892f852", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes.sort_values(\n", "    by=[\"ptype_id\", \"city_name\", \"group_name_rank\", \"recommended_ptype_rank\"]\n", ")"]}, {"cell_type": "raw", "id": "20cbb8d0-d7c5-4877-9006-e9cb8e505c57", "metadata": {}, "source": ["melted_complementary_ptypes.drop(columns={\"product_type_id_x\", \"product_type_id_y\"}, inplace=True)"]}, {"cell_type": "raw", "id": "800d20a0-5532-40ee-9d0d-f66df1346ff9", "metadata": {}, "source": ["melted_complementary_ptypes.shape"]}, {"cell_type": "markdown", "id": "b12ad3ca-b7ab-4f9e-a180-1d72b965d4b9", "metadata": {}, "source": ["# Final output for you might also like "]}, {"cell_type": "code", "execution_count": null, "id": "b979e599-a092-4d90-8b0a-74b7ef1b037c", "metadata": {}, "outputs": [], "source": ["sql_city_id_mapping = \"\"\"\n", "select\n", "    city_id,\n", "    city_name\n", "    from dwh.dim_merchant\n", "where is_current\n", "group by 1,2\n", "\"\"\"\n", "df_city_id_mapping = pd.read_sql(sql_city_id_mapping, con_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "ef83ce66-9a18-483d-939f-3df9674791bc", "metadata": {}, "outputs": [], "source": ["df_city_id_mapping.loc[len(df_city_id_mapping)] = [0, \"pan_india\"]"]}, {"cell_type": "raw", "id": "1843aa7c-bd5b-45c8-bbd9-83f11a58a2b2", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes.merge(df_city_id_mapping, on=\"city_name\")"]}, {"cell_type": "raw", "id": "a405a933-43e1-48d3-882a-ef57f5b782aa", "metadata": {}, "source": ["sql_product_group_id = \"\"\"select\n", "    product_id,\n", "    group_id as cms_group_id\n", "    from cms.gr_group_product_mapping as gpm \n", "    where enabled_flag = True and lake_active_record = True\"\"\"\n", "df_product_group_mapping = pd.read_sql(sql_product_group_id, con_trino)"]}, {"cell_type": "raw", "id": "81d7bcf8-b29d-4de1-896e-276cb92d8eca", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes.merge(\n", "    df_product_group_mapping, on=\"product_id\"\n", ")"]}, {"cell_type": "raw", "id": "f2605590-ba44-470c-abf3-e63f338c3585", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes.sort_values(\n", "    by=[\"ptype_id\", \"city_name\", \"group_name_rank\", \"recommended_ptype_rank\"]\n", ")"]}, {"cell_type": "raw", "id": "f4b91888-cab9-4c6f-b219-69122c7160af", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes.drop_duplicates(\n", "    subset=[\"city_name\", \"ptype_id\", \"cms_group_id\"], keep=\"first\"\n", ")"]}, {"cell_type": "raw", "id": "9c83b574-ca73-4218-8297-7104c88b80c2", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes.sort_values(\n", "    by=[\"ptype_id\", \"city_name\", \"group_name_rank\", \"recommended_ptype_rank\"]\n", ")"]}, {"cell_type": "raw", "id": "c8ef26ad-0225-4910-b6cc-ee36ce1195fc", "metadata": {}, "source": ["melted_complementary_ptypes[\"final_rank\"] = (\n", "    melted_complementary_ptypes.groupby([\"city_name\", \"ptype_id\"]).cumcount() + 1\n", ")"]}, {"cell_type": "raw", "id": "208100e8-83d2-4187-99f7-0b6969382cf2", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes[\n", "    melted_complementary_ptypes[\"final_rank\"] <= 100\n", "]"]}, {"cell_type": "raw", "id": "78b7f422-ca93-4377-acac-4b78c98327ce", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes.sort_values(\n", "    by=[\"ptype_id\", \"city_name\", \"group_name_rank\", \"recommended_ptype_rank\"]\n", ")"]}, {"cell_type": "raw", "id": "a72c3d07-5a6a-4943-9185-cbf3050c041e", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes.sort_values(\n", "    by=[\"city_name\", \"ptype_id\", \"final_rank\"]\n", ")"]}, {"cell_type": "raw", "id": "564f4506-9345-49b7-82eb-d976d5e353f9", "metadata": {}, "source": ["melted_complementary_ptypes.drop(columns={\"group_id\", \"group_name\", \"cms_group_id\"}, inplace=True)"]}, {"cell_type": "raw", "id": "57661956-f55c-4387-92d9-9d9030d1377c", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes.merge(\n", "    final_check_df, on=\"product_id\", how=\"inner\"\n", ")"]}, {"cell_type": "raw", "id": "0481a40a-8690-4fb2-9ca4-58279a432bb0", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes.sort_values(\n", "    by=[\"city_name\", \"ptype_id\", \"final_rank\"]\n", ")"]}, {"cell_type": "raw", "id": "cb08f156-4b14-4472-9ea6-12b19263b460", "metadata": {}, "source": ["melted_complementary_ptypes[\"cityid_groupid\"] = melted_complementary_ptypes.apply(\n", "    lambda x: (str(x[\"city_id\"]) + \"_\" + str(x[\"group_id\"])), axis=1\n", ")"]}, {"cell_type": "raw", "id": "5c318fda-abe8-4a9f-b321-15d3b89dded8", "metadata": {}, "source": ["melted_complementary_ptypes[\"title\"] = \"Curated for you\""]}, {"cell_type": "raw", "id": "dc5d85a0-0096-4835-beca-b1abba4fd159", "metadata": {}, "source": ["melted_complementary_ptypes[\"group_id\"] = melted_complementary_ptypes[\"group_id\"].astype(int)"]}, {"cell_type": "raw", "id": "b9c7a664-ebbe-440b-af74-964f324e004b", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes.merge(\n", "    df_group_intelligence, on=[\"group_id\", \"group_name\"]\n", ")"]}, {"cell_type": "raw", "id": "be4c9bfe-2ab9-4c26-b187-50d891e7b8dd", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes[\n", "    melted_complementary_ptypes[\"you_might_also_like_flag\"] == 1\n", "]"]}, {"cell_type": "raw", "id": "79cc38d4-83a6-4d63-a9de-0db035be16c0", "metadata": {}, "source": ["melted_complementary_ptypes = melted_complementary_ptypes.drop_duplicates(\n", "    subset=[\"cityid_groupid\", \"product_id\"], keep=\"first\"\n", ")"]}, {"cell_type": "raw", "id": "95df7e6c-9354-4400-a544-e230ac8c5e88", "metadata": {}, "source": ["melted_complementary_ptypes = (\n", "    melted_complementary_ptypes.groupby([\"cityid_groupid\", \"title\"])[\"product_id\"]\n", "    .apply(list)\n", "    .reset_index()\n", ")"]}, {"cell_type": "raw", "id": "d1160fb4-d25b-4f0f-a637-5116fc5138d2", "metadata": {}, "source": ["melted_complementary_ptypes.rename(columns={\"product_id\": \"pids\"}, inplace=True)"]}, {"cell_type": "raw", "id": "032d8454-022b-4a96-a838-ed7a6e2fbcb4", "metadata": {}, "source": ["melted_complementary_ptypes[\"pids\"] = melted_complementary_ptypes[\"pids\"].apply(lambda x: x[:100])"]}, {"cell_type": "raw", "id": "ff36db3c-dd47-47f5-beef-f2aad1356884", "metadata": {}, "source": ["melted_complementary_ptypes[\"widget_information\"] = melted_complementary_ptypes[\n", "    [\"title\", \"pids\"]\n", "].to_dict(\"records\")"]}, {"cell_type": "markdown", "id": "6e96e68c-1d3d-4e12-bfe3-ab47b39d7fc4", "metadata": {}, "source": ["# More in group data"]}, {"cell_type": "code", "execution_count": null, "id": "20f22e93-aab2-4922-9531-4461ed624168", "metadata": {}, "outputs": [], "source": ["sql_product_sales = \"\"\"\n", "with stable_cities as (\n", "select distinct city_name from\n", "dwh.fact_sales_order_details\n", "where order_create_dt_ist >= current_date - interval '30' day\n", "and is_internal_order = False\n", "group by 1\n", "having count(distinct order_id) >= 1000)\n", "\n", "select * from\n", "((select\n", "    fsoid.city_name,\n", "    dp.product_type_id,\n", "    dp.product_type,\n", "    dp.product_id,\n", "    dp.product_name,\n", "    gp.group_id,\n", "    count(distinct order_id) as \"count\"\n", "    from dwh.fact_sales_order_item_details as fsoid\n", "    inner join dwh.dim_product as dp\n", "        on fsoid.product_id = dp.product_id\n", "        and dp.is_current\n", "        and dp.is_product_enabled\n", "    inner join cms.gr_group_product_mapping\n", "        as gp\n", "        on dp.product_id = gp.product_id\n", "        and gp.enabled_flag = True and gp.lake_active_record = True\n", "where order_create_dt_ist >= current_date - interval '60' day\n", "and dp.l1_category_id not in ({l1_ids})\n", "and dp.l0_category_id not in ({l0_ids})\n", "and dp.product_type_id not in ({p_type_ids})\n", "and dp.product_id not in ({pids})\n", "and fsoid.city_name in (select distinct city_name from stable_cities)\n", "group by 1,2,3,4,5,6\n", "order by 1 asc,3 asc,7 desc)\n", "union all\n", "(select\n", "    'pan_india' as city_name,\n", "    dp.product_type_id,\n", "    dp.product_type,\n", "    dp.product_id,\n", "    dp.product_name,\n", "    gp.group_id,\n", "    count(distinct order_id) as \"count\"\n", "    from dwh.fact_sales_order_item_details as fsoid\n", "    inner join dwh.dim_product as dp\n", "        on fsoid.product_id = dp.product_id\n", "        and dp.is_current\n", "        and dp.is_product_enabled\n", "    inner join cms.gr_group_product_mapping\n", "        as gp\n", "        on dp.product_id = gp.product_id\n", "        and gp.enabled_flag = True and gp.lake_active_record = True\n", "where order_create_dt_ist >= current_date - interval '60' day\n", "and dp.l1_category_id not in ({l1_ids})\n", "and dp.l0_category_id not in ({l0_ids})\n", "and dp.product_type_id not in ({p_type_ids})\n", "and dp.product_id not in ({pids})\n", "group by 1,2,3,4,5,6\n", "order by 1 asc,3 asc,7 desc))\n", "\"\"\".format(\n", "    l0_ids=b_l0, l1_ids=b_l1, pids=b_pids, p_type_ids=b_ptype_ids\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "791f2b63-59e3-4329-ac05-43590a9261b9", "metadata": {}, "outputs": [], "source": ["df_product_sales_in_group = pd.read_sql(sql_product_sales, con_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "8a56c253-2c21-40c8-aa81-82f34e3816a6", "metadata": {}, "outputs": [], "source": ["df_product_sales_in_group = df_product_sales_in_group[\n", "    ~df_product_sales_in_group[\"city_name\"].isna()\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "ee01f8c8-b350-4d6d-be7f-04c9bddb9c13", "metadata": {}, "outputs": [], "source": ["df_product_sales_in_group.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a2919525-360a-4892-be61-89d8330588c0", "metadata": {}, "outputs": [], "source": ["df_product_sales_in_group = df_product_sales_in_group.drop_duplicates(\n", "    subset=[\"city_name\", \"product_type_id\", \"group_id\"], keep=\"first\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6253f0f4-1963-41f0-8b22-e96ad7112218", "metadata": {}, "outputs": [], "source": ["df_product_sales_in_group.head()"]}, {"cell_type": "code", "execution_count": null, "id": "94d658c5-913f-49c0-8b27-ae12003389b7", "metadata": {}, "outputs": [], "source": ["df_product_sales_in_group.drop(columns={\"group_id\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "aeda44df-710d-4d8d-b9a6-31611b675525", "metadata": {}, "outputs": [], "source": ["df_product_sales_in_group = df_product_sales_in_group.merge(final_check_df, on=\"product_id\")"]}, {"cell_type": "code", "execution_count": null, "id": "2e248963-66e8-410a-99d4-88c1364e690c", "metadata": {}, "outputs": [], "source": ["df_product_sales_in_group.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0bb015b7-b968-4204-91d4-6bfd90e094de", "metadata": {}, "outputs": [], "source": ["df_product_sales_in_group.drop(columns={\"product_type_id_y\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "636ec9f2-55d7-4551-b5d0-3394df6ac5cb", "metadata": {}, "outputs": [], "source": ["df_product_sales_in_group.rename(columns={\"product_type_id_x\": \"product_type_id\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "b6905eb2-f0fc-4f97-93d9-dba91fe05d0d", "metadata": {}, "outputs": [], "source": ["df_product_sales_in_group = df_product_sales_in_group.sort_values(\n", "    by=[\"city_name\", \"product_type_id\", \"count\"], ascending=[True, True, False]\n", ")"]}, {"cell_type": "markdown", "id": "7f30ab01-b499-449a-9fd6-920948c1c959", "metadata": {}, "source": ["# Getting ptype mapping"]}, {"cell_type": "code", "execution_count": null, "id": "abda58b6-ecdc-4437-b4bf-ceb786dd2cb3", "metadata": {}, "outputs": [], "source": ["group_level_data = (\n", "    df_product_sales_in_group.groupby(\n", "        [\"city_name\", \"product_type_id\", \"group_id\", \"group_name\", \"product_id\"]\n", "    )[\"count\"]\n", "    .sum()\n", "    .reset_index()\n", "    .sort_values(by=[\"city_name\", \"product_type_id\", \"count\"], ascending=[True, True, False])\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "14b3ffdb-963d-4f33-a81a-740cd3416d93", "metadata": {}, "outputs": [], "source": ["group_level_data = group_level_data.sort_values(\n", "    by=[\"city_name\", \"group_id\", \"group_name\", \"count\"],\n", "    ascending=[True, True, True, False],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e7246782-4511-4cbc-866b-dea1995f297c", "metadata": {}, "outputs": [], "source": ["group_level_data[\"final_rank\"] = group_level_data.groupby([\"city_name\", \"group_id\"]).cumcount() + 1"]}, {"cell_type": "code", "execution_count": null, "id": "7ae3af3d-e83c-442e-9deb-c1ae948c90fa", "metadata": {}, "outputs": [], "source": ["group_level_data = group_level_data[group_level_data[\"final_rank\"] <= 100]"]}, {"cell_type": "code", "execution_count": null, "id": "c334cade-d107-4a9f-b840-038c9e6ccc4f", "metadata": {}, "outputs": [], "source": ["group_level_data[\"title\"] = \"Curated for you\""]}, {"cell_type": "code", "execution_count": null, "id": "6d95234a-6933-4067-bc76-1508e7060e37", "metadata": {}, "outputs": [], "source": ["group_level_data = group_level_data.merge(df_city_id_mapping, on=\"city_name\")"]}, {"cell_type": "code", "execution_count": null, "id": "6824560d-8cb5-4974-9c9c-33b966d42c5b", "metadata": {}, "outputs": [], "source": ["group_level_data[\"group_id\"] = group_level_data[\"group_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "1034af10-58ac-470c-b8e2-5087a3f4a9b1", "metadata": {}, "outputs": [], "source": ["# group_level_data = group_level_data.merge(df_group_intelligence, on=[\"group_id\", \"group_name\"])"]}, {"cell_type": "code", "execution_count": null, "id": "9cc37978-31c3-4132-9276-9e3a469a9187", "metadata": {}, "outputs": [], "source": ["# group_level_data = group_level_data[group_level_data[\"more_in_flag\"] == 1]"]}, {"cell_type": "code", "execution_count": null, "id": "8b5c63ed-36c9-4e9a-8060-9ac8e29f8119", "metadata": {}, "outputs": [], "source": ["group_level_data[\"cityid_groupid\"] = group_level_data.apply(\n", "    lambda x: (str(x[\"city_id\"]) + \"_\" + str(x[\"group_id\"])), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c385ae74-ab34-4229-9e65-3004e4208c01", "metadata": {}, "outputs": [], "source": ["group_level_data = group_level_data.drop_duplicates(\n", "    subset=[\"cityid_groupid\", \"product_id\"], keep=\"first\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6bfbcf79-d3dc-49cd-aef0-bd6ad2a8832c", "metadata": {}, "outputs": [], "source": ["group_level_data = (\n", "    group_level_data.groupby([\"cityid_groupid\", \"title\"])[\"product_id\"].apply(list).reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9dcf84e6-87e0-44f4-9f3a-19279c1d388b", "metadata": {}, "outputs": [], "source": ["group_level_data.rename(columns={\"product_id\": \"pids\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "67df798b-3e0d-49dc-8bec-80816562950b", "metadata": {}, "outputs": [], "source": ["group_level_data[\"pids\"] = group_level_data[\"pids\"].apply(lambda x: x[:100])"]}, {"cell_type": "code", "execution_count": null, "id": "027d9068-c763-446a-b19b-4abda73e863a", "metadata": {}, "outputs": [], "source": ["group_level_data[\"widget_information\"] = group_level_data[[\"title\", \"pids\"]].to_dict(\"records\")"]}, {"cell_type": "code", "execution_count": null, "id": "f3f37f4b-f0fc-4cef-8833-1b618acc6586", "metadata": {}, "outputs": [], "source": ["group_level_data.cityid_groupid.nunique()"]}, {"cell_type": "markdown", "id": "0b90e69c-a76e-47c7-b41d-8ea422ae9558", "metadata": {}, "source": ["# Merged groups"]}, {"cell_type": "code", "execution_count": null, "id": "cb4442d2-8a7c-47d6-8366-1f7b2f4e7aa0", "metadata": {}, "outputs": [], "source": ["merged_group_level_data = (\n", "    df_product_sales_in_group.groupby(\n", "        [\"city_name\", \"product_type_id\", \"merge_group_id\", \"merge_group\", \"product_id\"]\n", "    )[\"count\"]\n", "    .sum()\n", "    .reset_index()\n", "    .sort_values(by=[\"city_name\", \"product_type_id\", \"count\"], ascending=[True, True, False])\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "746682d3-a3f4-450e-8ff8-73af2ce9f9a2", "metadata": {}, "outputs": [], "source": ["merged_group_level_data = merged_group_level_data.sort_values(\n", "    by=[\"city_name\", \"merge_group_id\", \"merge_group\", \"count\"],\n", "    ascending=[True, True, True, False],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "478f43db-94d7-445d-a2de-dd089c22054d", "metadata": {}, "outputs": [], "source": ["merged_group_level_data[\"final_rank\"] = (\n", "    merged_group_level_data.groupby([\"city_name\", \"merge_group_id\"]).cumcount() + 1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f523b695-7e63-447a-902c-db65b20e0e1c", "metadata": {}, "outputs": [], "source": ["merged_group_level_data = merged_group_level_data[merged_group_level_data[\"final_rank\"] <= 200]"]}, {"cell_type": "code", "execution_count": null, "id": "bef2e54a-9ee1-4940-8e7e-851dc582c16d", "metadata": {}, "outputs": [], "source": ["merged_group_level_data[\"title\"] = \"Curated for you\""]}, {"cell_type": "code", "execution_count": null, "id": "2e254473-bf15-48cb-a72b-12b448a6f8d1", "metadata": {}, "outputs": [], "source": ["merged_group_level_data = merged_group_level_data.merge(df_city_id_mapping, on=\"city_name\")"]}, {"cell_type": "code", "execution_count": null, "id": "58b9c088-458d-488e-9e28-a0fc9fa1f12b", "metadata": {}, "outputs": [], "source": ["merged_group_level_data = merged_group_level_data[merged_group_level_data[\"merge_group_id\"] != \"\"]"]}, {"cell_type": "code", "execution_count": null, "id": "912e9373-dda4-43bb-9821-6ea98cb3a7a1", "metadata": {}, "outputs": [], "source": ["merged_group_level_data[\"merge_group_id\"] = merged_group_level_data[\"merge_group_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "023f76aa-1649-4e3f-a00e-46fb85a9d66e", "metadata": {}, "outputs": [], "source": ["merged_group_level_data[\"cityid_groupid\"] = merged_group_level_data.apply(\n", "    lambda x: (str(x[\"city_id\"]) + \"_\" + str(x[\"merge_group_id\"])), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1ec47af1-4fd0-4e46-b700-f220e4a2a7f5", "metadata": {}, "outputs": [], "source": ["merged_group_level_data = merged_group_level_data.drop_duplicates(\n", "    subset=[\"cityid_groupid\", \"product_id\"], keep=\"first\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "51041bbb-a7dd-4b93-8895-70b3201b0868", "metadata": {}, "outputs": [], "source": ["merged_group_level_data = (\n", "    merged_group_level_data.groupby([\"cityid_groupid\", \"title\"])[\"product_id\"]\n", "    .apply(list)\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c30854c7-f18e-4e6b-9076-1c633b1c8117", "metadata": {}, "outputs": [], "source": ["merged_group_level_data.rename(columns={\"product_id\": \"pids\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "d8fddfff-e301-40db-8536-87c36aba6fbe", "metadata": {}, "outputs": [], "source": ["merged_group_level_data[\"pids\"] = merged_group_level_data[\"pids\"].apply(lambda x: x[:100])"]}, {"cell_type": "code", "execution_count": null, "id": "aad1699b-7e6a-4832-98e3-c967d1bf18b1", "metadata": {}, "outputs": [], "source": ["merged_group_level_data[\"widget_information\"] = merged_group_level_data[[\"title\", \"pids\"]].to_dict(\n", "    \"records\"\n", ")"]}, {"cell_type": "markdown", "id": "55f5f918-1c3e-48eb-9163-ab8a94b7b9a7", "metadata": {}, "source": ["# Merging data of both widgets to push to feature store"]}, {"cell_type": "code", "execution_count": null, "id": "702f084e-c836-4510-aeb3-baea5d23609a", "metadata": {}, "outputs": [], "source": ["merged_group_level_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b466d281-9286-498c-a585-d61111b2e13f", "metadata": {}, "outputs": [], "source": ["# melted_complementary_ptypes[melted_complementary_ptypes[\"cityid_groupid\"] == \"3_204\"]"]}, {"cell_type": "code", "execution_count": null, "id": "9895e830-62d4-4dc9-a499-375bd630cf8b", "metadata": {}, "outputs": [], "source": ["final_op_data = pd.DataFrame()\n", "# final_op_data = final_op_data.append(\n", "#     melted_complementary_ptypes[[\"cityid_groupid\", \"widget_information\"]]\n", "# )\n", "final_op_data = final_op_data.append(group_level_data[[\"cityid_groupid\", \"widget_information\"]])\n", "final_op_data = final_op_data.append(\n", "    merged_group_level_data[[\"cityid_groupid\", \"widget_information\"]]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1ec18dd8-4332-4b4d-8e89-41d4dd5ecebc", "metadata": {}, "outputs": [], "source": ["final_op_data[\"cityid_groupid\"] = final_op_data[\"cityid_groupid\"].astype(\"object\")\n", "final_op_data[\"widget_information\"] = final_op_data[\"widget_information\"].astype(\"object\")"]}, {"cell_type": "code", "execution_count": null, "id": "32165fd5-dfb3-4026-a2e3-6cbcb41533cf", "metadata": {}, "outputs": [], "source": ["kafka_records_dict = final_op_data.to_dict(\"records\")"]}, {"cell_type": "code", "execution_count": null, "id": "6bc779fb-837b-402a-8a2f-794aa5186f21", "metadata": {}, "outputs": [], "source": ["entity_column = \"cityid_groupid\"\n", "entity_name = \"cityid_groupid\"\n", "context = \"order_again_city_group_recommendations_1_0_0\"\n", "ctx_value_col = \"widget_information\""]}, {"cell_type": "code", "execution_count": null, "id": "e9188e47-7cc2-400c-9dac-23af379a5ae5", "metadata": {}, "outputs": [], "source": ["from kafka_utils import *"]}, {"cell_type": "code", "execution_count": null, "id": "6dfa89b4-dfbb-44f8-8fcf-ecdf960db6e8", "metadata": {}, "outputs": [], "source": ["def np_encoder(object):\n", "    if isinstance(object, np.generic):\n", "        return object.item()\n", "\n", "\n", "push_to_kafka(\n", "    entities=[f\"{entity_name}:{i[entity_column]}\" for i in kafka_records_dict],\n", "    context=context,\n", "    ctx_properties=[\n", "        {\"ctx_value\": json.dumps(i[ctx_value_col], default=np_encoder)} for i in kafka_records_dict\n", "    ],\n", "    dry_run=False,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "35e5a9a1-93ec-43e7-b17d-751f4fd32df3", "metadata": {}, "outputs": [], "source": ["# pb.send_slack_message(\n", "#     channel=\"bl-personalization-notifications\",\n", "#     text=\"Frequently bought group recommendations has run successfully!\",\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "1320ea03-84d1-41ee-8d68-8210f2f8170c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1a4df55f-44eb-4596-a232-ededa1dd4255", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ba3d7afe-4bef-4fce-8592-a4b6f557a2d9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}