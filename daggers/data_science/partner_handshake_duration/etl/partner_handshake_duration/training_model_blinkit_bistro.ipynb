{"cells": [{"cell_type": "code", "execution_count": null, "id": "831a2c5a-c97b-46ec-a2ac-06fed194fa2f", "metadata": {}, "outputs": [], "source": ["!pip install openpyxl"]}, {"cell_type": "code", "execution_count": null, "id": "0351c3e7-5dd9-4bd2-954e-c784e9b38761", "metadata": {}, "outputs": [], "source": ["!pip install -q catboost\n", "!pip install -q joblib\n", "!pip install -q pytz\n", "!pip install h3==3.7.6\n", "!optuna==3.6.1\n", "!pip install haversine"]}, {"cell_type": "code", "execution_count": null, "id": "01c05008-b52a-4cac-884b-8d126519a6e9", "metadata": {}, "outputs": [], "source": ["!pip install shap"]}, {"cell_type": "code", "execution_count": null, "id": "966ef7fe-066f-4280-ac37-8331237c0276", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "\n", "con_trino = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "4b78e7b7-b992-4b01-ab0b-cb51c2b9fcc6", "metadata": {}, "outputs": [], "source": ["import asyncio\n", "import concurrent.futures\n", "import datetime as dt\n", "import os\n", "import threading\n", "import time\n", "from contextlib import contextmanager\n", "from datetime import datetime\n", "\n", "import boto3\n", "import pytz\n", "import sqlalchemy as sqla\n", "from sqlalchemy.dialects import postgresql\n", "from sqlalchemy.ext.automap import automap_base\n", "from sqlalchemy.orm import sessionmaker\n", "import gc\n", "\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "from catboost import CatBoostRegressor\n", "from sklearn.metrics import mean_absolute_error\n", "\n", "import statistics\n", "import json\n", "\n", "from collections import OrderedDict"]}, {"cell_type": "code", "execution_count": null, "id": "8df4ad61-8e08-473c-b99a-fcdde5cbec19", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)\n", "pd.set_option(\"display.max_rows\", None)\n", "pd.set_option(\"display.float_format\", lambda x: \"%.5f\" % x)\n", "pd.set_option(\"display.max_colwidth\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "ac5fd53a-701b-4f06-adb6-17553d1ee995", "metadata": {}, "outputs": [], "source": ["trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "8849ece5-4ab4-442c-bda7-b1dfdc5c107b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "fd06128e-e3cb-4ae4-a554-c81419e122cf", "metadata": {}, "source": ["## Adding Bistro data\n"]}, {"cell_type": "code", "execution_count": null, "id": "a809ef76-483e-47ed-896a-9d33692999e9", "metadata": {}, "outputs": [], "source": ["bistro_org_id = \"4\"\n", "blinkit_org_id = \"1\""]}, {"cell_type": "code", "execution_count": null, "id": "bdc779e8-1544-48a5-9d23-8399083ba97b", "metadata": {}, "outputs": [], "source": ["s3_bucket = \"prod-dse-projects\"\n", "training_data_local_path = \"training_data_partner_setup_model_bistro.parquet\"\n", "pb.from_s3(\n", "    s3_bucket,\n", "    \"A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Bistro/partner_setup_model/training_data/training_data_partner_setup_model_bistro.parquet\",\n", "    training_data_local_path,\n", ")\n", "\n", "training_data_bistro = pd.read_parquet(training_data_local_path, engine=\"pyarrow\")\n", "\n", "\n", "s3_bucket = \"prod-dse-projects\"\n", "validation_data_local_path = \"validation_data_partner_setup_model_bistro.parquet\"\n", "pb.from_s3(\n", "    s3_bucket,\n", "    \"A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Bistro/partner_setup_model/validation_data/validation_data_partner_setup_model_bistro.parquet\",\n", "    validation_data_local_path,\n", ")\n", "\n", "validation_data_bistro = pd.read_parquet(validation_data_local_path, engine=\"pyarrow\")\n", "\n", "\n", "s3_bucket = \"prod-dse-projects\"\n", "test_data_local_path = \"test_data_partner_setup_model_bistro.parquet\"\n", "pb.from_s3(\n", "    s3_bucket,\n", "    \"A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Bistro/partner_setup_model/test_data/test_data_partner_setup_model_bistro.parquet\",\n", "    test_data_local_path,\n", ")\n", "\n", "test_data_bistro = pd.read_parquet(test_data_local_path, engine=\"pyarrow\")"]}, {"cell_type": "code", "execution_count": null, "id": "59654d38-33dc-4c49-b6bb-80eb41e01c3f", "metadata": {}, "outputs": [], "source": ["training_data_bistro[\"org_id\"] = bistro_org_id\n", "validation_data_bistro[\"org_id\"] = bistro_org_id\n", "test_data_bistro[\"org_id\"] = bistro_org_id"]}, {"cell_type": "code", "execution_count": null, "id": "df0e6b29-2bc5-41a2-860b-e7f01e146090", "metadata": {}, "outputs": [], "source": ["training_data_bistro.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "b0923f9a-6300-44f0-b031-f74fd66ea252", "metadata": {}, "outputs": [], "source": ["validation_data_bistro.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "27686d40-89a8-4460-a8f0-45ea0b57f39c", "metadata": {}, "outputs": [], "source": ["print(training_data_bistro.shape)\n", "training_data_bistro.head()"]}, {"cell_type": "code", "execution_count": null, "id": "760d469f-5da6-4a5a-bc6e-b75a2cdda374", "metadata": {}, "outputs": [], "source": ["print(training_data_bistro[\"trip_start_timestamp\"].min())\n", "print(training_data_bistro[\"trip_start_timestamp\"].max())\n", "print()\n", "print(validation_data_bistro[\"trip_start_timestamp\"].min())\n", "print(validation_data_bistro[\"trip_start_timestamp\"].max())\n", "print()\n", "print(test_data_bistro[\"trip_start_timestamp\"].min())\n", "print(test_data_bistro[\"trip_start_timestamp\"].max())"]}, {"cell_type": "code", "execution_count": null, "id": "ed43baec-e302-42bc-9900-6e1a65e365a2", "metadata": {}, "outputs": [], "source": ["print(training_data_bistro.shape)\n", "print(validation_data_bistro.shape)\n", "print(test_data_bistro.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "ce135078-a235-43aa-bcd7-17813144e847", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "eac5bc4a-5965-4d97-8b9b-d80afe5a9a10", "metadata": {}, "source": ["## Adding Blinkit data\n"]}, {"cell_type": "code", "execution_count": null, "id": "24ebe898-2ef3-49eb-baaf-57f6a70ffe04", "metadata": {}, "outputs": [], "source": ["s3_bucket = \"prod-dse-projects\"\n", "training_data_local_path = \"training_data_partner_setup_model_blinkit.parquet\"\n", "pb.from_s3(\n", "    s3_bucket,\n", "    \"A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Blinkit/partner_setup_model/training_data/training_data_partner_setup_model_blinkit.parquet\",\n", "    training_data_local_path,\n", ")\n", "\n", "training_data_blinkit = pd.read_parquet(training_data_local_path, engine=\"pyarrow\")\n", "\n", "\n", "s3_bucket = \"prod-dse-projects\"\n", "validation_data_local_path = \"validation_data_partner_setup_model_blinkit.parquet\"\n", "pb.from_s3(\n", "    s3_bucket,\n", "    \"A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Blinkit/partner_setup_model/validation_data/validation_data_partner_setup_model_blinkit.parquet\",\n", "    validation_data_local_path,\n", ")\n", "\n", "validation_data_blinkit = pd.read_parquet(validation_data_local_path, engine=\"pyarrow\")\n", "\n", "\n", "s3_bucket = \"prod-dse-projects\"\n", "test_data_local_path = \"test_data_partner_setup_model_blinkit.parquet\"\n", "pb.from_s3(\n", "    s3_bucket,\n", "    \"A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Blinkit/partner_setup_model/test_data/test_data_partner_setup_model_blinkit.parquet\",\n", "    test_data_local_path,\n", ")\n", "\n", "test_data_blinkit = pd.read_parquet(test_data_local_path, engine=\"pyarrow\")"]}, {"cell_type": "code", "execution_count": null, "id": "d06465e9-4ec1-42b1-9234-272be19d544f", "metadata": {}, "outputs": [], "source": ["training_data_blinkit[\"org_id\"] = blinkit_org_id\n", "validation_data_blinkit[\"org_id\"] = blinkit_org_id\n", "test_data_blinkit[\"org_id\"] = blinkit_org_id"]}, {"cell_type": "code", "execution_count": null, "id": "b50fd01b-99b1-4eb5-98e1-9afeeba67307", "metadata": {}, "outputs": [], "source": ["print(training_data_blinkit.shape)\n", "print(validation_data_blinkit.shape)\n", "print(test_data_blinkit.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "17ea5829-6ece-4192-aa8f-b2c76a9ee8d6", "metadata": {}, "outputs": [], "source": ["print(training_data_blinkit[\"date\"].min())\n", "print(training_data_blinkit[\"date\"].max())\n", "print()\n", "print(validation_data_blinkit[\"date\"].min())\n", "print(validation_data_blinkit[\"date\"].max())\n", "print()\n", "print(test_data_blinkit[\"date\"].min())\n", "print(test_data_blinkit[\"date\"].max())"]}, {"cell_type": "code", "execution_count": null, "id": "b793537f-8a4b-4c72-94fa-87829a2676eb", "metadata": {}, "outputs": [], "source": ["training_data = pd.concat([training_data_bistro, training_data_blinkit], ignore_index=True)\n", "validation_data = pd.concat([validation_data_bistro, validation_data_blinkit], ignore_index=True)\n", "test_data = pd.concat([test_data_bistro, test_data_blinkit], ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "10be3a79-94f0-413d-abba-cabbbb4a59b7", "metadata": {}, "outputs": [], "source": ["print(training_data.shape)\n", "print(validation_data.shape)\n", "print(test_data.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "55b9a76e-41c6-42ae-82db-e044cadffabb", "metadata": {}, "outputs": [], "source": ["training_data.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "dd55fddc-628a-4a25-b71a-b5244cc70bf6", "metadata": {}, "outputs": [], "source": ["validation_data.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "1dd7e598-53fa-43ad-b5b1-04d8d49c1124", "metadata": {}, "outputs": [], "source": ["training_data[\"org_id\"].value_counts(normalize=True)"]}, {"cell_type": "code", "execution_count": null, "id": "e1198a40-4119-4169-9cc7-4ae0157ee92d", "metadata": {}, "outputs": [], "source": ["validation_data[\"org_id\"].value_counts(normalize=True)"]}, {"cell_type": "code", "execution_count": null, "id": "b5e43a9e-ecbc-41f3-ad3b-5be7474555fe", "metadata": {}, "outputs": [], "source": ["categorical_feature = [\n", "    \"frontend_merchant_id\",\n", "    \"city_name\",\n", "    \"order_weight_bucket\",\n", "    \"hour_of_day\",\n", "    \"org_id\",\n", "]\n", "continous_feature = [\n", "    \"distinct_items_ordered\",\n", "    \"total_cold_items_ordered\",\n", "    \"total_items_quantity_ordered\",\n", "    \"mean_last_1_day\",\n", "    \"mean_last_3_days\",\n", "    \"mean_last_7_days\",\n", "]\n", "features = continous_feature + categorical_feature\n", "prediction_feature = \"partner_setup_time\""]}, {"cell_type": "code", "execution_count": null, "id": "10559ed7-5de4-4d38-b358-0de33f909b74", "metadata": {}, "outputs": [], "source": ["training_data = training_data.dropna(\n", "    subset=[\n", "        \"distinct_items_ordered\",\n", "        \"total_cold_items_ordered\",\n", "        \"total_items_quantity_ordered\",\n", "        \"mean_last_1_day\",\n", "        \"mean_last_3_days\",\n", "        \"mean_last_7_days\",\n", "        \"frontend_merchant_id\",\n", "        \"city_name\",\n", "        \"order_weight_bucket\",\n", "        \"hour_of_day\",\n", "        \"org_id\",\n", "        \"partner_setup_time\",\n", "    ]\n", ")\n", "\n", "\n", "validation_data = validation_data.dropna(\n", "    subset=[\n", "        \"distinct_items_ordered\",\n", "        \"total_cold_items_ordered\",\n", "        \"total_items_quantity_ordered\",\n", "        \"mean_last_1_day\",\n", "        \"mean_last_3_days\",\n", "        \"mean_last_7_days\",\n", "        \"frontend_merchant_id\",\n", "        \"city_name\",\n", "        \"order_weight_bucket\",\n", "        \"hour_of_day\",\n", "        \"org_id\",\n", "        \"partner_setup_time\",\n", "    ]\n", ")\n", "\n", "\n", "test_data = test_data.dropna(\n", "    subset=[\n", "        \"distinct_items_ordered\",\n", "        \"total_cold_items_ordered\",\n", "        \"total_items_quantity_ordered\",\n", "        \"mean_last_1_day\",\n", "        \"mean_last_3_days\",\n", "        \"mean_last_7_days\",\n", "        \"frontend_merchant_id\",\n", "        \"city_name\",\n", "        \"order_weight_bucket\",\n", "        \"hour_of_day\",\n", "        \"org_id\",\n", "        \"partner_setup_time\",\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "677cba87-4918-4775-bb88-38d723dcc5e1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "531f5a68-af41-4df4-8541-e0ef40c9a99e", "metadata": {}, "outputs": [], "source": ["for col in categorical_feature:\n", "    training_data[col] = training_data[col].astype(\"category\")\n", "    validation_data[col] = validation_data[col].astype(\"category\")\n", "    test_data[col] = test_data[col].astype(\"category\")"]}, {"cell_type": "code", "execution_count": null, "id": "2d3908f6-7961-4e7c-80be-e8ef3b436ba4", "metadata": {}, "outputs": [], "source": ["training_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "b7063713-80e1-4dce-8f9c-6f7ca057b430", "metadata": {}, "outputs": [], "source": ["model_params = {\n", "    \"n_estimators\": 1500,\n", "    \"learning_rate\": 0.0110775870446307,\n", "    \"l2_leaf_reg\": 1,\n", "    \"depth\": 7,\n", "    \"loss_function\": \"MAE\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "4426d529-5aee-43ef-b474-e343d78c1d09", "metadata": {}, "outputs": [], "source": ["def train_catboost(\n", "    tr_data,\n", "    val_data,\n", "    categorical_features,\n", "    continous_features,\n", "    params,\n", "    dependent_variable=\"partner_setup_time\",\n", "):\n", "\n", "    features = continous_features + categorical_features\n", "\n", "    model = CatBoostRegressor(\n", "        approx_on_full_history=False,\n", "        allow_writing_files=True,\n", "        train_dir=\"/tmp/catboost_logs\",\n", "        **params,\n", "        cat_features=categorical_features,\n", "        custom_metric=[\"MAE\", \"R2\", \"RMSE\"],\n", "        save_snapshot=True,\n", "        verbose=200,\n", "    )\n", "\n", "    model.fit(\n", "        tr_data[features],\n", "        tr_data[dependent_variable],\n", "        eval_set=(\n", "            val_data[features],\n", "            val_data[dependent_variable],\n", "        ),\n", "        use_best_model=True,\n", "    )\n", "\n", "    return model"]}, {"cell_type": "code", "execution_count": null, "id": "94d559b3-f4d9-4654-b4eb-4527247bbb87", "metadata": {}, "outputs": [], "source": ["model = train_catboost(\n", "    training_data,\n", "    validation_data,\n", "    categorical_feature,\n", "    continous_feature,\n", "    model_params,\n", "    dependent_variable=prediction_feature,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8f962714-6d44-4b88-b370-c0bab9e2aef5", "metadata": {}, "outputs": [], "source": ["def get_feature_importance(model, features):\n", "    importance = model.get_feature_importance()\n", "    importance_df = pd.DataFrame({\"Feature\": features, \"Importance\": importance}).sort_values(\n", "        by=\"Importance\", ascending=False\n", "    )\n", "    return importance_df\n", "\n", "\n", "feature_importance_df = get_feature_importance(model, features)\n", "feature_importance_df"]}, {"cell_type": "code", "execution_count": null, "id": "e430cfc6-73b3-4575-bcd9-f4eb1ec49d83", "metadata": {}, "outputs": [], "source": ["import shap\n", "import joblib\n", "from joblib import load\n", "import pandas as pd\n", "\n", "\n", "explainer = shap.Explainer(model)\n", "shap_values = explainer(training_data[features])\n", "\n", "# Summary plot\n", "shap.summary_plot(shap_values, training_data[features])"]}, {"cell_type": "code", "execution_count": null, "id": "b4434c39-b49a-42e8-9757-5f49b42d01a3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "0834ec39-9985-4d90-aeb0-d51da0a4bd9b", "metadata": {}, "source": ["## Evaluation"]}, {"cell_type": "code", "execution_count": null, "id": "44f8839a-cb58-420c-ab0a-4b99869ea0e0", "metadata": {}, "outputs": [], "source": ["train_prediction = model.predict(training_data[features])\n", "validation_prediction = model.predict(validation_data[features])\n", "test_prediction = model.predict(test_data[features])\n", "\n", "training_data[\"predicted_partner_setup_time\"] = train_prediction\n", "validation_data[\"predicted_partner_setup_time\"] = validation_prediction\n", "test_data[\"predicted_partner_setup_time\"] = test_prediction"]}, {"cell_type": "code", "execution_count": null, "id": "cc34293b-e1e7-402b-aa14-111889b9b96e", "metadata": {}, "outputs": [], "source": ["model_train_mae = mean_absolute_error(training_data.partner_setup_time, train_prediction)\n", "model_validation_mae = mean_absolute_error(\n", "    validation_data.partner_setup_time, validation_prediction\n", ")\n", "model_test_mae = mean_absolute_error(test_data.partner_setup_time, test_prediction)\n", "\n", "model_train_me = np.mean(training_data.partner_setup_time - train_prediction)\n", "model_validation_me = np.mean(validation_data.partner_setup_time - validation_prediction)\n", "model_test_me = np.mean(test_data.partner_setup_time - test_prediction)\n", "\n", "print(\n", "    \"Model MAE:\",\n", "    model_train_mae,\n", "    model_validation_mae,\n", "    model_test_mae,\n", ")\n", "\n", "print(\n", "    \"Model ME:\",\n", "    model_train_me,\n", "    model_validation_me,\n", "    model_test_me,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "be443ef8-fe03-4a36-bd60-78d09b94d855", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    \"bl-personalization-notifications\",\n", "    f\"Partner Handshake Time Model:\\n\\tTrain MAE: {model_train_mae}\\n\\tValidation MAE: {model_validation_mae}\\n\\tTest MAE: {model_test_mae}\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "623a28cf-4ef0-452f-81b8-09845f4c644b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e2a642d9-ca15-48e7-87e8-25c25b2eafd4", "metadata": {}, "outputs": [], "source": ["# WITH BUSINESS METRIC\n", "import numpy as np\n", "from sklearn.metrics import mean_absolute_error, mean_squared_error\n", "\n", "\n", "def calculate_business_metrics(y_true, y_pred, threshold=0.5):\n", "    \"\"\"Calculate business compliance and accuracy metrics\"\"\"\n", "    # Business compliance +2 (actual must be at most 2 mins more than predicted)\n", "    compliance_mask = (y_true - y_pred) <= threshold\n", "\n", "    business_compliance = np.mean(compliance_mask) * 100\n", "\n", "    # Business accuracy ±2 (actual must at most have 2 mins deviation from predicted)\n", "    accuracy_mask = np.abs(y_true - y_pred) <= threshold\n", "    business_accuracy = np.mean(accuracy_mask) * 100\n", "\n", "    return business_compliance, business_accuracy"]}, {"cell_type": "code", "execution_count": null, "id": "14cba887-2d31-4d33-9a1f-fa1e5fdf1774", "metadata": {}, "outputs": [], "source": ["calculate_business_metrics(validation_data.partner_setup_time, validation_prediction)"]}, {"cell_type": "code", "execution_count": null, "id": "13da8330-cc1f-4e45-ae7d-ebac381f3cfb", "metadata": {}, "outputs": [], "source": ["calculate_business_metrics(test_data.partner_setup_time, test_prediction)"]}, {"cell_type": "code", "execution_count": null, "id": "7013c922-f131-49e0-a063-8933796bc6e8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "1fba89f9-a7ad-467d-94e1-c2bd1e3d8cbc", "metadata": {}, "source": ["## Pushing the Model Metadata"]}, {"cell_type": "code", "execution_count": null, "id": "1e159d74-f587-477b-97c1-eab71426532e", "metadata": {}, "outputs": [], "source": ["from catboost import CatBoostRegressor\n", "\n", "# model.save_model(\"model.cbm\")\n", "model.save_model(\"model.cbm\", format=\"cbm\")"]}, {"cell_type": "code", "execution_count": null, "id": "5444397f-02c3-486f-a3a7-c56640d31e1a", "metadata": {}, "outputs": [], "source": ["model_version = 1.1\n", "model_subid = datetime.now().strftime(\"%Y%m%d%H%M\")\n", "model_name = \"se_partner_handshake_time_estimation\"\n", "model_id = \"{}_v{}_{}\".format(model_name, model_version, model_subid)\n", "model_type = \"catboost_regressor\""]}, {"cell_type": "code", "execution_count": null, "id": "12dad2f2-6be5-45c3-b497-e18ebc67220e", "metadata": {}, "outputs": [], "source": ["print(model_subid)\n", "print(model_name)\n", "print(model_id)\n", "print(model_type)"]}, {"cell_type": "code", "execution_count": null, "id": "9edacd4c-1a50-465a-85a2-24ca4fe9584f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6adda7d4-f83a-48c5-a936-5ccf80e9e873", "metadata": {}, "outputs": [], "source": ["model_logs = {\n", "    \"training_data_start_date\": training_data.date.min(),\n", "    \"training_data_end_date\": training_data.date.max(),\n", "    \"validation_data_start_date\": validation_data.date.min(),\n", "    \"validation_data_end_date\": validation_data.date.max(),\n", "    \"test_data_start_date\": test_data.date.min(),\n", "    \"test_data_end_date\": test_data.date.max(),\n", "    \"model_id\": model_id,\n", "    \"model_name\": model_name,\n", "    \"model_version\": model_version,\n", "    \"model_subid\": model_subid,\n", "    \"model_type\": model_type,\n", "    \"model_train_mae\": round(model_train_mae, 2),\n", "    \"model_validation_mae\": round(model_validation_mae, 2),\n", "    \"model_test_mae\": round(model_test_mae, 2),\n", "    \"model_train_me\": round(model_train_me, 2),\n", "    \"model_validation_me\": round(model_validation_me, 2),\n", "    \"model_test_me\": round(model_test_me, 2),\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "490e8a94-eeec-46d4-b254-f294aa52e375", "metadata": {}, "outputs": [], "source": ["BUCKET_NAME = \"prod-dse-projects\"\n", "MODEL_CLOUDPATH = \"/eta_prediction/partner_handshake_time_estimation/models/{}/{}/model.cbm\".format(\n", "    f\"v{model_version}\", model_subid\n", ")\n", "MODEL_METADATA_CLOUDPATH = (\n", "    \"/eta_prediction/partner_handshake_time_estimation/models/{}/{}/metadata.json\".format(\n", "        f\"v{model_version}\", model_subid\n", "    )\n", ")\n", "MODEL_POINTER_CLOUDPATH = \"/eta_prediction/partner_handshake_time_estimation/models/current.json\"\n", "\n", "MODEL_LOCALPATH = \"model.cbm\"\n", "MODEL_POINTER_LOCALPATH = \"current.json\"\n", "MODEL_METADATA_LOCALPATH = \"metadata.json\"\n", "\n", "AUTHOR = \"<EMAIL>\""]}, {"cell_type": "code", "execution_count": null, "id": "a637d524-3efa-4731-a014-3bda75ff48b8", "metadata": {}, "outputs": [], "source": ["MODEL_METADATA_CLOUDPATH"]}, {"cell_type": "code", "execution_count": null, "id": "9ef5a0d6-cded-47bc-b9bc-8299af6fe95d", "metadata": {}, "outputs": [], "source": ["print(\"model cloud path:\", MODEL_CLOUDPATH)\n", "print(\"model metadata path:\", MODEL_METADATA_CLOUDPATH)\n", "print(\"model pointer:\", MODEL_POINTER_CLOUDPATH)"]}, {"cell_type": "code", "execution_count": null, "id": "542deac0-8994-493c-94b0-2a424e64984a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8d1a8f75-ee54-47ac-8380-835a7fd5b180", "metadata": {}, "outputs": [], "source": ["model_pointer = {\n", "    \"created_at\": datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "    \"created_by\": AUTH<PERSON>,\n", "    \"bucket_name\": BUCKET_NAME,\n", "    \"model_path\": MODEL_CLOUDPATH,\n", "    \"metadata_path\": MODEL_METADATA_CLOUDPATH,\n", "    \"model_id\": model_id,\n", "    \"model_name\": model_name,\n", "    \"model_version\": model_version,\n", "    \"model_type\": model_type,\n", "}\n", "\n", "model_metadata = model_pointer.copy()\n", "model_metadata[\"darkstore_ids\"] = training_data.frontend_merchant_id.unique().tolist()\n", "model_metadata[\"model_params\"] = model_params\n", "model_metadata[\"model_test_mae\"] = model_logs[\"model_test_mae\"]\n", "\n", "model_logs[\"s3_bucket\"] = BUCKET_NAME\n", "model_logs[\"s3_path\"] = MODEL_CLOUDPATH\n", "model_logs_df = pd.DataFrame([OrderedDict(model_logs)])"]}, {"cell_type": "code", "execution_count": null, "id": "959d44a3-5b2a-4bd0-9100-91a81bdad054", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9570e338-65ab-4062-9281-a904916843d4", "metadata": {}, "outputs": [], "source": ["model_logs_df"]}, {"cell_type": "code", "execution_count": null, "id": "885d7aa7-a21e-48bb-8b01-fdd3a5efd821", "metadata": {}, "outputs": [], "source": ["# dump model locally\n", "# joblib.dump(model, MODEL_LOCALPATH, compress=1)\n", "\n", "# model metadata\n", "with open(MODEL_METADATA_LOCALPATH, \"w\") as fp:\n", "    json.dump(model_metadata, fp)\n", "\n", "# model pointer\n", "with open(MODEL_POINTER_LOCALPATH, \"w\") as fp:\n", "    json.dump(model_pointer, fp)"]}, {"cell_type": "code", "execution_count": null, "id": "d43fa9f1-799e-437f-8b8a-b626b12df900", "metadata": {}, "outputs": [], "source": ["# push model to s3\n", "pb.to_s3(MODEL_LOCALPATH, BUCKET_NAME, MODEL_CLOUDPATH)\n", "\n", "# push metadata to s3 (darkstore ids trained on)\n", "pb.to_s3(MODEL_METADATA_LOCALPATH, BUCKET_NAME, MODEL_METADATA_CLOUDPATH)\n", "\n", "# push model pointer to s3 (current model filepath)\n", "pb.to_s3(MODEL_POINTER_LOCALPATH, BUCKET_NAME, MODEL_POINTER_CLOUDPATH)"]}, {"cell_type": "code", "execution_count": null, "id": "35d2781e-b87b-476d-926f-f2eacef82a6c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "10de2d1c-aa95-4aef-a470-4bdf6f3469da", "metadata": {}, "outputs": [], "source": ["def push_to_db(logs):\n", "\n", "    frame = logs.copy()\n", "    frame[\"updated_at\"] = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    frame = frame.astype({\"updated_at\": \"datetime64[ns]\"})\n", "    frame[\"status\"] = \"trained\"\n", "\n", "    column_dtypes = [\n", "        {\n", "            \"name\": \"training_data_start_date\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"training data start date\",\n", "        },\n", "        {\n", "            \"name\": \"training_data_end_date\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"training data end date\",\n", "        },\n", "        {\n", "            \"name\": \"validation_data_start_date\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"Validation data start date\",\n", "        },\n", "        {\n", "            \"name\": \"validation_data_end_date\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"validation data end date\",\n", "        },\n", "        {\n", "            \"name\": \"test_data_start_date\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"test data start date\",\n", "        },\n", "        {\n", "            \"name\": \"test_data_end_date\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"test data end date\",\n", "        },\n", "        {\n", "            \"name\": \"model_id\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"id of the generated model\",\n", "        },\n", "        {\n", "            \"name\": \"model_name\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"name of the model used for predicting the eta component\",\n", "        },\n", "        {\n", "            \"name\": \"model_version\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"version of the model\",\n", "        },\n", "        {\n", "            \"name\": \"model_subid\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"model subid is the date on which the model was generated\",\n", "        },\n", "        {\n", "            \"name\": \"model_type\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"type of the ml model\",\n", "        },\n", "        {\n", "            \"name\": \"model_train_mae\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"mean absolute error of model on training data\",\n", "        },\n", "        {\n", "            \"name\": \"model_validation_mae\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"mean absolute error of model on validation data\",\n", "        },\n", "        {\n", "            \"name\": \"model_test_mae\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"mean absolute error of model on test data\",\n", "        },\n", "        {\n", "            \"name\": \"model_train_me\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"mean error of model on training data\",\n", "        },\n", "        {\n", "            \"name\": \"model_validation_me\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"mean error of model on validation data\",\n", "        },\n", "        {\n", "            \"name\": \"model_test_me\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"mean error of model on test data\",\n", "        },\n", "        {\n", "            \"name\": \"s3_bucket\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"bucket name where artifacts are stores\",\n", "        },\n", "        {\n", "            \"name\": \"s3_path\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"path inside the bucket where the model is stored\",\n", "        },\n", "        {\n", "            \"name\": \"updated_at\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"table updation timestamp\",\n", "        },\n", "        {\n", "            \"name\": \"status\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"status of the model for deployement\",\n", "        },\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"consumer_etls\",\n", "        \"table_name\": \"serviceability_partner_handshake_time_model_retraining_logs\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"model_id\"],\n", "        \"sortkey\": [\"model_id\", \"updated_at\"],\n", "        \"incremental_key\": \"updated_at\",\n", "        \"load_type\": \"upsert\",\n", "        \"table_description\": \"Partner Handshake time estimation retraining pipeline logs\",\n", "    }\n", "\n", "    pb.to_trino(frame, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "c63213d6-5abe-4db6-89df-6c9360001c44", "metadata": {}, "outputs": [], "source": ["if len(model_logs_df) != 0:\n", "    push_to_db(model_logs_df)"]}, {"cell_type": "code", "execution_count": null, "id": "08fbd322-4d7a-48ec-88c7-2eede4aab18c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d1a72fcd-bd00-4e20-98f5-b45bd988f925", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    \"bl-personalization-notifications\",\n", "    f\"Partner Handshake Time Model DAG run completed. Model meta data pushed into Trino\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "cc691243-be16-4bf1-8fe8-29c8b8bd9670", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "323b1607-9513-4c21-9d51-0006f2878460", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "14a925ad-a7ed-4d4c-88c1-b1a3706fba73", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ed8988f3-69ff-45ff-9bc3-ab6af8113bdc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a05fc465-2083-4e2e-977f-d6b62c2eb3fc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e4bb32a6-4889-44cc-b0d7-71036be0913a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "298699b3-abfb-4330-911c-0a970feb3896", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "bf06b2fd-3a9e-4d24-9bf3-8c98c710cd1e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a0b903e5-8445-4483-b70a-89f99bfe0ca1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c0c9c200-b1a2-406f-a644-78f2342c837c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ecaea8db-e4a7-4cb5-b35f-30682a8b979f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4f1316fa-39ee-4f6d-8e97-aadd4eb37abc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d714895c-1bba-44ac-8305-40dee6475213", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3dbdc55a-e715-4596-b8c1-22cb27419c20", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cc9ba038-7af5-454a-ab49-4daaf6f7ad35", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "89f35c01-3e85-410b-91e3-457c3a0c4120", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9b9317e5-842e-45d3-ad66-1255ccd9b4f5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "16f32ef5-f860-4e47-82fa-dc7fd8efdf2f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}