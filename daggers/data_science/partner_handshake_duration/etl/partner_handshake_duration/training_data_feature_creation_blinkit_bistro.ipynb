{"cells": [{"cell_type": "code", "execution_count": null, "id": "831a2c5a-c97b-46ec-a2ac-06fed194fa2f", "metadata": {}, "outputs": [], "source": ["!pip install -q catboost\n", "!pip install -q joblib\n", "!pip install -q pytz\n", "!pip install h3==3.7.6\n", "!optuna==3.6.1\n", "!pip install haversine"]}, {"cell_type": "code", "execution_count": null, "id": "0351c3e7-5dd9-4bd2-954e-c784e9b38761", "metadata": {}, "outputs": [], "source": ["!pip install -q kafka-python==2.0.2\n", "!pip install -q scipy==1.8.0\n", "!pip install aiohttp==3.8.1\n", "!pip install fsspec==2023.1.0\n", "!pip install aiobotocore==2.4.2\n", "!pip install pymysql==1.0.2\n", "!pip install gremlinpython==3.6.2\n", "!pip install botocore==1.27.59\n", "!pip install progressbar2==4.2.0\n", "!pip install backoff==2.2.1\n", "!pip install pandas==1.5.1\n", "!pip install pg8000==1.29.4\n", "!pip install opensearch-py==2.1.1\n", "!pip install boto3==1.24.59\n", "!pip install requests-aws4auth==1.2.2\n", "!pip install s3transfer==0.6.0\n", "!pip install aenum==3.1.11\n", "!pip install scramp==1.4.4\n", "!pip install python-utils==3.5.2\n", "!pip install awswrangler==2.19.0\n", "!pip install s3fs==2023.1.0\n", "!pip install scikit-learn\n", "!pip install polars"]}, {"cell_type": "code", "execution_count": null, "id": "df649e09-0b7d-4e98-a550-bc136dc6c6ce", "metadata": {}, "outputs": [], "source": ["from concurrent.futures import ThreadPoolExecutor\n", "from sklearn.metrics import mean_absolute_error\n", "from datetime import date, datetime, timedelta\n", "from catboost import CatBoostRegressor\n", "from collections import OrderedDict\n", "from multiprocessing import Pool\n", "import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "from h3 import h3\n", "import warnings\n", "import requests\n", "import logging\n", "import pickle\n", "import joblib\n", "import random\n", "import shutil\n", "import glob\n", "import math\n", "import time\n", "import pytz\n", "import json\n", "import os\n", "import gc\n", "\n", "import awswrangler as wr\n", "\n", "import haversine as hs\n", "from haversine import Unit, haversine_vector\n", "\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "# tqdm.pandas()\n", "pd.set_option(\"display.max_rows\", 100)\n", "pd.set_option(\"display.max_columns\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "966ef7fe-066f-4280-ac37-8331237c0276", "metadata": {}, "outputs": [], "source": ["s3_bucket = \"prod-dse-projects\""]}, {"cell_type": "code", "execution_count": null, "id": "8df4ad61-8e08-473c-b99a-fcdde5cbec19", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)\n", "pd.set_option(\"display.max_rows\", None)\n", "pd.set_option(\"display.float_format\", lambda x: \"%.5f\" % x)\n", "pd.set_option(\"display.max_colwidth\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "ac5fd53a-701b-4f06-adb6-17553d1ee995", "metadata": {}, "outputs": [], "source": ["trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "markdown", "id": "86b83f17-cdf1-41e8-9847-6d11712b6400", "metadata": {}, "source": ["# Date settings"]}, {"cell_type": "markdown", "id": "ce9e1ece-4b64-4bcc-b08c-0cf2a4c787ac", "metadata": {}, "source": ["## Blinkit data"]}, {"cell_type": "code", "execution_count": null, "id": "e2555e73-a7ca-45e0-b249-24c886105ef0", "metadata": {}, "outputs": [], "source": ["# current_date = datetime.now(pytz.timezone(\"Asia/Kolkata\"))\n", "\n", "# start_date_blinkit = current_date - timed<PERSON><PERSON>(days=46)\n", "# validation_date_blinkit = current_date - timedelta(days=15)\n", "# test_date_blinkit = current_date - timed<PERSON>ta(days=8)\n", "# end_date_blinkit = current_date - timed<PERSON>ta(days=1)\n", "\n", "# START_DATE_blinkit, VALIDATION_DATE_blinkit, TEST_DATE_blinkit, END_DATE_blinkit = (\n", "#     str(start_date_blinkit.date()),\n", "#     str(validation_date_blinkit.date()),\n", "#     str(test_date_blinkit.date()),\n", "#     str(end_date_blinkit.date()),\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "e00101d7-9ad5-4ca2-82ca-072512d72182", "metadata": {}, "outputs": [], "source": ["current_date = datetime.now(pytz.timezone(\"Asia/Kolkata\"))\n", "\n", "start_date_blinkit = current_date - <PERSON><PERSON><PERSON>(days=51)\n", "start_date_blinkit1 = current_date - timed<PERSON><PERSON>(days=46)\n", "validation_date_blinkit = current_date - timed<PERSON>ta(days=15)\n", "test_date_blinkit = current_date - timed<PERSON><PERSON>(days=8)\n", "end_date_blinkit = current_date - <PERSON><PERSON><PERSON>(days=1)\n", "\n", "START_DATE_blinkit, VALIDATION_DATE_blinkit, TEST_DATE_blinkit, END_DATE_blinkit = (\n", "    str(start_date_blinkit.date()),\n", "    str(validation_date_blinkit.date()),\n", "    str(test_date_blinkit.date()),\n", "    str(end_date_blinkit.date()),\n", ")\n", "\n", "START_DATE_blinkit1 = str(start_date_blinkit1.date())"]}, {"cell_type": "code", "execution_count": null, "id": "1434c7b8-24ab-492e-a75d-ddfc204da3f7", "metadata": {}, "outputs": [], "source": ["# START_DATE_blinkit = \"2025-04-06\"\n", "# END_DATE_blinkit = \"2025-05-21\""]}, {"cell_type": "code", "execution_count": null, "id": "b959363c-2ff1-4291-8de6-2db1965aeb64", "metadata": {}, "outputs": [], "source": ["output_dir_blinkit = f\"dataset_blinkit_{START_DATE_blinkit}_to_{END_DATE_blinkit}\"\n", "output_dir_blinkit"]}, {"cell_type": "code", "execution_count": null, "id": "1329f1b3-e17b-4010-8bd7-b598c9eea7a2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "b3b4b1f8-b732-4375-a6ea-b40f41bcdd2c", "metadata": {}, "source": ["## Bistro data"]}, {"cell_type": "code", "execution_count": null, "id": "23910066-5836-4756-bcc5-e2ee37a007a7", "metadata": {}, "outputs": [], "source": ["current_date = datetime.now(pytz.timezone(\"Asia/Kolkata\"))\n", "\n", "ts = pd.Timestamp(\"2024-10-15 00:00:00\")\n", "\n", "# Convert to timezone-aware datetime in Asia/Kolkata\n", "dt = ts.tz_localize(\"Asia/Kolkata\")\n", "\n", "# Convert to Python datetime object (still timezone-aware)\n", "start_date_bistro = dt.to_pydatetime()\n", "\n", "start_date_bistro = pd.to_datetime(start_date_bistro)\n", "validation_date_bistro = current_date - <PERSON><PERSON><PERSON>(days=15)\n", "test_date_bistro = current_date - <PERSON><PERSON><PERSON>(days=8)\n", "end_date_bistro = current_date - <PERSON><PERSON><PERSON>(days=1)\n", "\n", "START_DATE_bistro, VALIDATION_DATE_bistro, TEST_DATE_bistro, END_DATE_bistro = (\n", "    str(start_date_bistro.date()),\n", "    str(validation_date_bistro.date()),\n", "    str(test_date_bistro.date()),\n", "    str(end_date_bistro.date()),\n", ")\n", "\n", "\n", "start_date_bistro1 = start_date_bistro + <PERSON><PERSON><PERSON>(days=7)\n", "START_DATE_bistro1 = str(start_date_bistro1.date())"]}, {"cell_type": "code", "execution_count": null, "id": "fa44d4ac-a63e-4a2b-bdb0-887ef10170dc", "metadata": {}, "outputs": [], "source": ["# END_DATE_bistro = \"2025-05-21\""]}, {"cell_type": "code", "execution_count": null, "id": "1551b220-6353-4d76-a583-e4544b5df593", "metadata": {}, "outputs": [], "source": ["output_dir_bistro = f\"dataset_bistro_{START_DATE_bistro}_to_{END_DATE_bistro}\"\n", "output_dir_bistro"]}, {"cell_type": "code", "execution_count": null, "id": "f65721b1-10a6-460e-ae0f-dd82b09d73f8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "e7302b57-0636-442a-85b8-5f340b8ad266", "metadata": {}, "source": ["# Read data"]}, {"cell_type": "markdown", "id": "3ca2191e-dcfd-4199-b4aa-5076716790fc", "metadata": {}, "source": ["## Blinkit Data"]}, {"cell_type": "code", "execution_count": null, "id": "7554752f-c23d-4a34-b9ac-bb1937cc5008", "metadata": {}, "outputs": [], "source": ["s3_paths_blinkit = f\"s3://prod-dse-projects/A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Blinkit/partner_setup_model/data_prep/{output_dir_blinkit}/\"\n", "s3_paths_obj_blinkit = wr.s3.list_objects(s3_paths_blinkit)\n", "s3_paths_obj_blinkit"]}, {"cell_type": "code", "execution_count": null, "id": "6b2e0c79-a1ea-4448-86bc-f1448ebfef06", "metadata": {}, "outputs": [], "source": ["partner_setup_data_blinkit = pd.DataFrame()\n", "for i in range(len(s3_paths_obj_blinkit)):\n", "    partner_setup_data_blinkit_temp = pd.read_parquet(s3_paths_obj_blinkit[i])\n", "\n", "    partner_setup_data_blinkit = pd.concat(\n", "        [partner_setup_data_blinkit, partner_setup_data_blinkit_temp]\n", "    )\n", "\n", "    del partner_setup_data_blinkit_temp"]}, {"cell_type": "code", "execution_count": null, "id": "c933a11a-128c-4184-9433-44b36068ee56", "metadata": {}, "outputs": [], "source": ["partner_setup_data_blinkit = partner_setup_data_blinkit.drop_duplicates(subset=[\"order_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "01673903-297b-4a8d-8a61-a0c90b1a714d", "metadata": {}, "outputs": [], "source": ["print(partner_setup_data_blinkit.shape)\n", "partner_setup_data_blinkit.head()"]}, {"cell_type": "code", "execution_count": null, "id": "fb1f2805-92d2-4f22-901c-104a49e6ffcd", "metadata": {}, "outputs": [], "source": ["partner_setup_data_blinkit.info()"]}, {"cell_type": "code", "execution_count": null, "id": "0c0bce13-de36-4f69-97e4-13abbfbbf78a", "metadata": {}, "outputs": [], "source": ["partner_setup_data_blinkit.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "f711b1a6-3573-44e0-b220-7f5ed704d644", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "9de7689e-d500-4c28-b9dc-0bf16ab5034f", "metadata": {}, "source": ["## Bistro Data"]}, {"cell_type": "code", "execution_count": null, "id": "cd22ef6f-a46a-4cbb-9fcd-8cd8ac7c5fcb", "metadata": {}, "outputs": [], "source": ["s3_paths_bistro = f\"s3://prod-dse-projects/A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Bistro/partner_setup_model/data_prep/{output_dir_bistro}/\"\n", "s3_paths_obj_bistro = wr.s3.list_objects(s3_paths_bistro)\n", "s3_paths_obj_bistro"]}, {"cell_type": "code", "execution_count": null, "id": "ebd82c2f-05a2-4b66-b63e-68be42f4c008", "metadata": {}, "outputs": [], "source": ["partner_setup_data_bistro = pd.DataFrame()\n", "for i in range(len(s3_paths_obj_bistro)):\n", "    partner_setup_data_bistro_temp = pd.read_parquet(s3_paths_obj_bistro[i])\n", "\n", "    partner_setup_data_bistro = pd.concat(\n", "        [partner_setup_data_bistro, partner_setup_data_bistro_temp]\n", "    )\n", "\n", "    del partner_setup_data_bistro_temp"]}, {"cell_type": "code", "execution_count": null, "id": "649cb44c-8858-4367-8533-48e3599c3f4d", "metadata": {}, "outputs": [], "source": ["partner_setup_data_bistro = partner_setup_data_bistro.drop_duplicates(subset=[\"order_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "020501e2-d05e-4940-acad-f2effe4bfd68", "metadata": {}, "outputs": [], "source": ["print(partner_setup_data_bistro.shape)\n", "partner_setup_data_bistro.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2954861e-7927-4718-95ab-34917e7dfd88", "metadata": {}, "outputs": [], "source": ["partner_setup_data_bistro.info()"]}, {"cell_type": "code", "execution_count": null, "id": "e881c22a-3d0d-4dc1-b29e-ced11f9aabf3", "metadata": {}, "outputs": [], "source": ["partner_setup_data_bistro.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "71896aa1-da5e-42c0-ab3f-c634110df319", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "bd280d91-8a98-4ea7-ac27-a19f8e8b4c75", "metadata": {}, "source": ["# Post Processing"]}, {"cell_type": "code", "execution_count": null, "id": "7233d304-4532-4445-923c-5e4abf68a5ca", "metadata": {}, "outputs": [], "source": ["def feature_creation_function(\n", "    eta_partner_setup_time_component, VALIDATION_DATE, TEST_DATE, org_name, START_DATE_1\n", "):\n", "\n", "    eta_partner_setup_time_component[\"order_checkout_ts_ist\"] = pd.to_datetime(\n", "        eta_partner_setup_time_component[\"order_checkout_ts_ist\"]\n", "    )\n", "    eta_partner_setup_time_component[\"date\"] = pd.to_datetime(\n", "        eta_partner_setup_time_component[\"date\"]\n", "    )\n", "\n", "    eta_partner_setup_time_component[\"hour_of_day\"] = eta_partner_setup_time_component[\n", "        \"order_checkout_ts_ist\"\n", "    ].dt.hour\n", "    eta_partner_setup_time_component[\"hour_of_day\"] = eta_partner_setup_time_component[\n", "        \"hour_of_day\"\n", "    ].astype(str)\n", "\n", "    eta_partner_setup_time_component[\"day_of_week\"] = eta_partner_setup_time_component[\n", "        \"order_checkout_ts_ist\"\n", "    ].dt.day_name()  # Full day name\n", "\n", "    eta_partner_setup_time_component[\"is_weekend\"] = eta_partner_setup_time_component[\n", "        \"day_of_week\"\n", "    ].isin(\n", "        [\"Saturday\", \"Sunday\"]\n", "    )  # Boolean for weekends\n", "    eta_partner_setup_time_component[\"is_weekend\"] = np.where(\n", "        eta_partner_setup_time_component[\"is_weekend\"] == True, 1, 0\n", "    )\n", "\n", "    print(eta_partner_setup_time_component.shape)\n", "\n", "    eta_partner_setup_time_component[\"frontend_merchant_id\"] = eta_partner_setup_time_component[\n", "        \"frontend_merchant_id\"\n", "    ].astype(str)\n", "    eta_partner_setup_time_component[\"partner_setup_time\"] = eta_partner_setup_time_component[\n", "        \"partner_setup_time\"\n", "    ].astype(float)\n", "\n", "    df_order_id_lag_features_final = pd.DataFrame()\n", "\n", "    df_order_id_lag_features_final_temp = eta_partner_setup_time_component[\n", "        [\"date\", \"frontend_merchant_id\", \"partner_setup_time\"]\n", "    ]\n", "\n", "    # Group by merchant_id and date, and aggregate the total partner_setup_time per date\n", "    agg_df = (\n", "        df_order_id_lag_features_final_temp.groupby([\"frontend_merchant_id\", \"date\"])\n", "        .agg({\"partner_setup_time\": \"mean\"})\n", "        .reset_index()\n", "    )\n", "\n", "    # Create an empty DataFrame to hold the results\n", "    result = []\n", "\n", "    # Iterate through each merchant_id group\n", "    for merchant, group in agg_df.groupby(\"frontend_merchant_id\"):\n", "        group = group.set_index(\"date\").sort_index()  # Sort by date for time-based rolling\n", "        group[\"mean_last_1_day\"] = group[\"partner_setup_time\"].shift(1).rolling(\"1D\").mean()\n", "        group[\"mean_last_3_days\"] = group[\"partner_setup_time\"].shift(1).rolling(\"3D\").mean()\n", "        group[\"mean_last_7_days\"] = group[\"partner_setup_time\"].shift(1).rolling(\"7D\").mean()\n", "        result.append(group)\n", "\n", "    # Combine all groups back into a single DataFrame\n", "    df_order_id_lag_features_final = pd.concat(result).reset_index()\n", "\n", "    df_order_id_lag_features_final = df_order_id_lag_features_final[\n", "        df_order_id_lag_features_final[\"mean_last_1_day\"].notna()\n", "    ]\n", "\n", "    eta_partner_setup_time_component_final = eta_partner_setup_time_component.merge(\n", "        df_order_id_lag_features_final[\n", "            [\n", "                \"date\",\n", "                \"frontend_merchant_id\",\n", "                \"mean_last_1_day\",\n", "                \"mean_last_3_days\",\n", "                \"mean_last_7_days\",\n", "            ]\n", "        ],\n", "        how=\"left\",\n", "    )\n", "\n", "    del eta_partner_setup_time_component\n", "    del df_order_id_lag_features_final\n", "    gc.collect()\n", "\n", "    eta_partner_setup_time_component_final = eta_partner_setup_time_component_final[\n", "        eta_partner_setup_time_component_final[\"date\"] >= START_DATE_1\n", "    ]\n", "\n", "    # eta_partner_setup_time_component_final = eta_partner_setup_time_component_final[eta_partner_setup_time_component_final['date']>='2024-12-07']\n", "\n", "    eta_partner_setup_time_component_final = eta_partner_setup_time_component_final[\n", "        eta_partner_setup_time_component_final[\"mean_last_1_day\"].notna()\n", "    ]\n", "\n", "    if org_name == \"Bistro\":\n", "        eta_partner_setup_time_component_final[\"order_weight\"] = 1000\n", "\n", "    q3 = 600\n", "    q6 = 1900\n", "    q9 = 5500\n", "    q95 = 8500\n", "    q99 = 15500\n", "\n", "    eta_partner_setup_time_component_final[\"order_weight_bucket\"] = np.where(\n", "        eta_partner_setup_time_component_final[\"order_weight\"] <= q3, \"Very_Low_weight\", \"Others\"\n", "    )\n", "    eta_partner_setup_time_component_final[\"order_weight_bucket\"] = np.where(\n", "        (eta_partner_setup_time_component_final[\"order_weight\"] > q3)\n", "        & (eta_partner_setup_time_component_final[\"order_weight\"] <= q6),\n", "        \"Low_weight\",\n", "        eta_partner_setup_time_component_final[\"order_weight_bucket\"],\n", "    )\n", "    eta_partner_setup_time_component_final[\"order_weight_bucket\"] = np.where(\n", "        (eta_partner_setup_time_component_final[\"order_weight\"] > q6)\n", "        & (eta_partner_setup_time_component_final[\"order_weight\"] <= q9),\n", "        \"Medium_weight\",\n", "        eta_partner_setup_time_component_final[\"order_weight_bucket\"],\n", "    )\n", "    eta_partner_setup_time_component_final[\"order_weight_bucket\"] = np.where(\n", "        (eta_partner_setup_time_component_final[\"order_weight\"] > q9)\n", "        & (eta_partner_setup_time_component_final[\"order_weight\"] <= q95),\n", "        \"Heavy_weight\",\n", "        eta_partner_setup_time_component_final[\"order_weight_bucket\"],\n", "    )\n", "    eta_partner_setup_time_component_final[\"order_weight_bucket\"] = np.where(\n", "        (eta_partner_setup_time_component_final[\"order_weight\"] > q95)\n", "        & (eta_partner_setup_time_component_final[\"order_weight\"] <= q99),\n", "        \"Very_Heavy_weight\",\n", "        eta_partner_setup_time_component_final[\"order_weight_bucket\"],\n", "    )\n", "    eta_partner_setup_time_component_final[\"order_weight_bucket\"] = np.where(\n", "        (eta_partner_setup_time_component_final[\"order_weight\"] > q99),\n", "        \"Extreme_Heavy_weight\",\n", "        eta_partner_setup_time_component_final[\"order_weight_bucket\"],\n", "    )\n", "\n", "    eta_partner_setup_time_component_final[\"hour_of_day_bucket\"] = np.where(\n", "        eta_partner_setup_time_component_final[\"hour_of_day\"].isin([\"0\", \"1\", \"2\"]),\n", "        \"mid_night\",\n", "        \"Others\",\n", "    )\n", "    eta_partner_setup_time_component_final[\"hour_of_day_bucket\"] = np.where(\n", "        eta_partner_setup_time_component_final[\"hour_of_day\"].isin([\"3\", \"4\", \"5\"]),\n", "        \"very_early_morning\",\n", "        eta_partner_setup_time_component_final[\"hour_of_day_bucket\"],\n", "    )\n", "    eta_partner_setup_time_component_final[\"hour_of_day_bucket\"] = np.where(\n", "        eta_partner_setup_time_component_final[\"hour_of_day\"].isin([\"6\", \"7\", \"8\"]),\n", "        \"early_morning\",\n", "        eta_partner_setup_time_component_final[\"hour_of_day_bucket\"],\n", "    )\n", "    eta_partner_setup_time_component_final[\"hour_of_day_bucket\"] = np.where(\n", "        eta_partner_setup_time_component_final[\"hour_of_day\"].isin([\"9\", \"10\", \"11\"]),\n", "        \"morning\",\n", "        eta_partner_setup_time_component_final[\"hour_of_day_bucket\"],\n", "    )\n", "    eta_partner_setup_time_component_final[\"hour_of_day_bucket\"] = np.where(\n", "        eta_partner_setup_time_component_final[\"hour_of_day\"].isin([\"12\", \"13\", \"14\"]),\n", "        \"afternoon\",\n", "        eta_partner_setup_time_component_final[\"hour_of_day_bucket\"],\n", "    )\n", "    eta_partner_setup_time_component_final[\"hour_of_day_bucket\"] = np.where(\n", "        eta_partner_setup_time_component_final[\"hour_of_day\"].isin([\"15\", \"16\", \"17\"]),\n", "        \"late_afternoon\",\n", "        eta_partner_setup_time_component_final[\"hour_of_day_bucket\"],\n", "    )\n", "    eta_partner_setup_time_component_final[\"hour_of_day_bucket\"] = np.where(\n", "        eta_partner_setup_time_component_final[\"hour_of_day\"].isin([\"18\", \"19\", \"20\"]),\n", "        \"evening\",\n", "        eta_partner_setup_time_component_final[\"hour_of_day_bucket\"],\n", "    )\n", "    eta_partner_setup_time_component_final[\"hour_of_day_bucket\"] = np.where(\n", "        eta_partner_setup_time_component_final[\"hour_of_day\"].isin([\"21\", \"22\", \"23\"]),\n", "        \"night\",\n", "        eta_partner_setup_time_component_final[\"hour_of_day_bucket\"],\n", "    )\n", "\n", "    df_extreme_values_merch_extreme_buckt = (\n", "        eta_partner_setup_time_component_final.groupby(\n", "            [\"frontend_merchant_id\", \"order_weight_bucket\"]\n", "        )[\"partner_setup_time\"]\n", "        .quantile(0.93)\n", "        .reset_index(name=\"extreme_partner_setup_time\")\n", "    )\n", "    eta_partner_setup_time_component_final = eta_partner_setup_time_component_final.merge(\n", "        df_extreme_values_merch_extreme_buckt, how=\"left\"\n", "    )\n", "\n", "    eta_partner_setup_time_component_final[\"outlier_flag_extreme\"] = np.where(\n", "        eta_partner_setup_time_component_final[\"partner_setup_time\"]\n", "        > eta_partner_setup_time_component_final[\"extreme_partner_setup_time\"],\n", "        1,\n", "        0,\n", "    )\n", "    print(\n", "        eta_partner_setup_time_component_final[\"outlier_flag_extreme\"].value_counts(normalize=True)\n", "    )\n", "\n", "    training_data = eta_partner_setup_time_component_final.query(f\"date <'{VALIDATION_DATE}'\")\n", "\n", "    validation_data = eta_partner_setup_time_component_final.query(\n", "        f\"date >= '{VALIDATION_DATE}' and date < '{TEST_DATE}'\"\n", "    )\n", "\n", "    test_data = eta_partner_setup_time_component_final.query(f\"date >= '{TEST_DATE}'\")\n", "\n", "    del eta_partner_setup_time_component_final\n", "    gc.collect()\n", "\n", "    # Removing outliers\n", "    training_data = training_data.query(\"outlier_flag_extreme==0\")\n", "\n", "    validation_data = validation_data.query(\"outlier_flag_extreme==0\")\n", "\n", "    return training_data, validation_data, test_data"]}, {"cell_type": "code", "execution_count": null, "id": "963a6e26-527a-445a-9472-9836db6233d3", "metadata": {}, "outputs": [], "source": ["training_data_bistro, validation_data_bistro, test_data_bistro = feature_creation_function(\n", "    partner_setup_data_bistro,\n", "    VALIDATION_DATE_bistro,\n", "    TEST_DATE_bistro,\n", "    \"Bistro\",\n", "    START_DATE_bistro1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d04db987-bed6-449a-9121-b5537bbc6160", "metadata": {}, "outputs": [], "source": ["print(training_data_bistro.shape)\n", "print(validation_data_bistro.shape)\n", "print(test_data_bistro.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "c20ceee4-90f9-4dc2-b09e-97c8afeb39c9", "metadata": {}, "outputs": [], "source": ["training_data_blinkit, validation_data_blinkit, test_data_blinkit = feature_creation_function(\n", "    partner_setup_data_blinkit,\n", "    VALIDATION_DATE_blinkit,\n", "    TEST_DATE_blinkit,\n", "    \"Blinkit\",\n", "    START_DATE_blinkit1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c0c9c200-b1a2-406f-a644-78f2342c837c", "metadata": {}, "outputs": [], "source": ["print(training_data_blinkit.shape)\n", "print(validation_data_blinkit.shape)\n", "print(test_data_blinkit.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "ecaea8db-e4a7-4cb5-b35f-30682a8b979f", "metadata": {}, "outputs": [], "source": ["bistro_training_data_rows = training_data_bistro.shape[0]\n", "bistro_validation_data_rows = validation_data_bistro.shape[0]"]}, {"cell_type": "code", "execution_count": null, "id": "4f1316fa-39ee-4f6d-8e97-aadd4eb37abc", "metadata": {}, "outputs": [], "source": ["training_data_blinkit_desired_rows = bistro_training_data_rows * 49\n", "validation_data_blinkit_desired_rows = bistro_validation_data_rows * 49"]}, {"cell_type": "code", "execution_count": null, "id": "d714895c-1bba-44ac-8305-40dee6475213", "metadata": {}, "outputs": [], "source": ["sampling_factor_training_blinkit = (\n", "    training_data_blinkit_desired_rows / training_data_blinkit.shape[0]\n", ")\n", "sampling_factor_validation_blinkit = (\n", "    validation_data_blinkit_desired_rows / validation_data_blinkit.shape[0]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3dbdc55a-e715-4596-b8c1-22cb27419c20", "metadata": {}, "outputs": [], "source": ["print(sampling_factor_training_blinkit)\n", "print(sampling_factor_validation_blinkit)"]}, {"cell_type": "code", "execution_count": null, "id": "2f9e5fa1-8209-4fe8-ac25-5eecd5cc604e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cc9ba038-7af5-454a-ab49-4daaf6f7ad35", "metadata": {}, "outputs": [], "source": ["training_data_blinkit[\"total_items_quantity_ordered_bucket\"] = np.where(\n", "    training_data_blinkit[\"total_items_quantity_ordered\"] == 1, \"1\", \"others\"\n", ")\n", "training_data_blinkit[\"total_items_quantity_ordered_bucket\"] = np.where(\n", "    training_data_blinkit[\"total_items_quantity_ordered\"] == 2,\n", "    \"2\",\n", "    training_data_blinkit[\"total_items_quantity_ordered_bucket\"],\n", ")\n", "training_data_blinkit[\"total_items_quantity_ordered_bucket\"] = np.where(\n", "    training_data_blinkit[\"total_items_quantity_ordered\"] == 3,\n", "    \"3\",\n", "    training_data_blinkit[\"total_items_quantity_ordered_bucket\"],\n", ")\n", "training_data_blinkit[\"total_items_quantity_ordered_bucket\"] = np.where(\n", "    training_data_blinkit[\"total_items_quantity_ordered\"] == 4,\n", "    \"4\",\n", "    training_data_blinkit[\"total_items_quantity_ordered_bucket\"],\n", ")\n", "training_data_blinkit[\"total_items_quantity_ordered_bucket\"] = np.where(\n", "    training_data_blinkit[\"total_items_quantity_ordered\"] == 5,\n", "    \"5\",\n", "    training_data_blinkit[\"total_items_quantity_ordered_bucket\"],\n", ")\n", "training_data_blinkit[\"total_items_quantity_ordered_bucket\"] = np.where(\n", "    training_data_blinkit[\"total_items_quantity_ordered\"] == 6,\n", "    \"6\",\n", "    training_data_blinkit[\"total_items_quantity_ordered_bucket\"],\n", ")\n", "training_data_blinkit[\"total_items_quantity_ordered_bucket\"] = np.where(\n", "    training_data_blinkit[\"total_items_quantity_ordered\"] == 7,\n", "    \"7\",\n", "    training_data_blinkit[\"total_items_quantity_ordered_bucket\"],\n", ")\n", "training_data_blinkit[\"total_items_quantity_ordered_bucket\"] = np.where(\n", "    training_data_blinkit[\"total_items_quantity_ordered\"] == 8,\n", "    \"8\",\n", "    training_data_blinkit[\"total_items_quantity_ordered_bucket\"],\n", ")\n", "training_data_blinkit[\"total_items_quantity_ordered_bucket\"] = np.where(\n", "    (training_data_blinkit[\"total_items_quantity_ordered\"] > 8)\n", "    & (training_data_blinkit[\"total_items_quantity_ordered\"] <= 10),\n", "    \"8_10\",\n", "    training_data_blinkit[\"total_items_quantity_ordered_bucket\"],\n", ")\n", "training_data_blinkit[\"total_items_quantity_ordered_bucket\"] = np.where(\n", "    (training_data_blinkit[\"total_items_quantity_ordered\"] > 10)\n", "    & (training_data_blinkit[\"total_items_quantity_ordered\"] <= 13),\n", "    \"10_13\",\n", "    training_data_blinkit[\"total_items_quantity_ordered_bucket\"],\n", ")\n", "training_data_blinkit[\"total_items_quantity_ordered_bucket\"] = np.where(\n", "    (training_data_blinkit[\"total_items_quantity_ordered\"] > 13)\n", "    & (training_data_blinkit[\"total_items_quantity_ordered\"] <= 16),\n", "    \"13_16\",\n", "    training_data_blinkit[\"total_items_quantity_ordered_bucket\"],\n", ")\n", "training_data_blinkit[\"total_items_quantity_ordered_bucket\"] = np.where(\n", "    (training_data_blinkit[\"total_items_quantity_ordered\"] > 16)\n", "    & (training_data_blinkit[\"total_items_quantity_ordered\"] <= 22),\n", "    \"16_22\",\n", "    training_data_blinkit[\"total_items_quantity_ordered_bucket\"],\n", ")\n", "training_data_blinkit[\"total_items_quantity_ordered_bucket\"] = np.where(\n", "    (training_data_blinkit[\"total_items_quantity_ordered\"] > 22),\n", "    \"beyond_22\",\n", "    training_data_blinkit[\"total_items_quantity_ordered_bucket\"],\n", ")\n", "\n", "\n", "validation_data_blinkit[\"total_items_quantity_ordered_bucket\"] = np.where(\n", "    validation_data_blinkit[\"total_items_quantity_ordered\"] == 1, \"1\", \"others\"\n", ")\n", "validation_data_blinkit[\"total_items_quantity_ordered_bucket\"] = np.where(\n", "    validation_data_blinkit[\"total_items_quantity_ordered\"] == 2,\n", "    \"2\",\n", "    validation_data_blinkit[\"total_items_quantity_ordered_bucket\"],\n", ")\n", "validation_data_blinkit[\"total_items_quantity_ordered_bucket\"] = np.where(\n", "    validation_data_blinkit[\"total_items_quantity_ordered\"] == 3,\n", "    \"3\",\n", "    validation_data_blinkit[\"total_items_quantity_ordered_bucket\"],\n", ")\n", "validation_data_blinkit[\"total_items_quantity_ordered_bucket\"] = np.where(\n", "    validation_data_blinkit[\"total_items_quantity_ordered\"] == 4,\n", "    \"4\",\n", "    validation_data_blinkit[\"total_items_quantity_ordered_bucket\"],\n", ")\n", "validation_data_blinkit[\"total_items_quantity_ordered_bucket\"] = np.where(\n", "    validation_data_blinkit[\"total_items_quantity_ordered\"] == 5,\n", "    \"5\",\n", "    validation_data_blinkit[\"total_items_quantity_ordered_bucket\"],\n", ")\n", "validation_data_blinkit[\"total_items_quantity_ordered_bucket\"] = np.where(\n", "    validation_data_blinkit[\"total_items_quantity_ordered\"] == 6,\n", "    \"6\",\n", "    validation_data_blinkit[\"total_items_quantity_ordered_bucket\"],\n", ")\n", "validation_data_blinkit[\"total_items_quantity_ordered_bucket\"] = np.where(\n", "    validation_data_blinkit[\"total_items_quantity_ordered\"] == 7,\n", "    \"7\",\n", "    validation_data_blinkit[\"total_items_quantity_ordered_bucket\"],\n", ")\n", "validation_data_blinkit[\"total_items_quantity_ordered_bucket\"] = np.where(\n", "    validation_data_blinkit[\"total_items_quantity_ordered\"] == 8,\n", "    \"8\",\n", "    validation_data_blinkit[\"total_items_quantity_ordered_bucket\"],\n", ")\n", "validation_data_blinkit[\"total_items_quantity_ordered_bucket\"] = np.where(\n", "    (validation_data_blinkit[\"total_items_quantity_ordered\"] > 8)\n", "    & (validation_data_blinkit[\"total_items_quantity_ordered\"] <= 10),\n", "    \"8_10\",\n", "    validation_data_blinkit[\"total_items_quantity_ordered_bucket\"],\n", ")\n", "validation_data_blinkit[\"total_items_quantity_ordered_bucket\"] = np.where(\n", "    (validation_data_blinkit[\"total_items_quantity_ordered\"] > 10)\n", "    & (validation_data_blinkit[\"total_items_quantity_ordered\"] <= 13),\n", "    \"10_13\",\n", "    validation_data_blinkit[\"total_items_quantity_ordered_bucket\"],\n", ")\n", "validation_data_blinkit[\"total_items_quantity_ordered_bucket\"] = np.where(\n", "    (validation_data_blinkit[\"total_items_quantity_ordered\"] > 13)\n", "    & (validation_data_blinkit[\"total_items_quantity_ordered\"] <= 16),\n", "    \"13_16\",\n", "    validation_data_blinkit[\"total_items_quantity_ordered_bucket\"],\n", ")\n", "validation_data_blinkit[\"total_items_quantity_ordered_bucket\"] = np.where(\n", "    (validation_data_blinkit[\"total_items_quantity_ordered\"] > 16)\n", "    & (validation_data_blinkit[\"total_items_quantity_ordered\"] <= 22),\n", "    \"16_22\",\n", "    validation_data_blinkit[\"total_items_quantity_ordered_bucket\"],\n", ")\n", "validation_data_blinkit[\"total_items_quantity_ordered_bucket\"] = np.where(\n", "    (validation_data_blinkit[\"total_items_quantity_ordered\"] > 22),\n", "    \"beyond_22\",\n", "    validation_data_blinkit[\"total_items_quantity_ordered_bucket\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c4dad51f-7097-4174-9584-d933d243d5f6", "metadata": {}, "outputs": [], "source": ["training_data_blinkit[\"total_items_quantity_ordered_bucket\"].value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "e5090b7d-94bb-413f-ae3d-439d34b7c7cc", "metadata": {}, "outputs": [], "source": ["validation_data_blinkit[\"total_items_quantity_ordered_bucket\"].value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "ca5fafdf-f331-4039-93c0-e1cb6eaf71bc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "771e8700-7d91-4f35-9ec2-716b821dccaa", "metadata": {}, "outputs": [], "source": ["q2 = training_data_blinkit[\"partner_setup_time\"].quantile(0.2)\n", "q4 = training_data_blinkit[\"partner_setup_time\"].quantile(0.4)\n", "q6 = training_data_blinkit[\"partner_setup_time\"].quantile(0.6)\n", "q8 = training_data_blinkit[\"partner_setup_time\"].quantile(0.8)\n", "q9 = training_data_blinkit[\"partner_setup_time\"].quantile(0.9)\n", "q95 = training_data_blinkit[\"partner_setup_time\"].quantile(0.95)\n", "q99 = training_data_blinkit[\"partner_setup_time\"].quantile(0.99)\n", "\n", "training_data_blinkit[\"partner_setup_time_bucket\"] = np.where(\n", "    training_data_blinkit[\"partner_setup_time\"] <= q2, \"less_than_q2\", \"others\"\n", ")\n", "training_data_blinkit[\"partner_setup_time_bucket\"] = np.where(\n", "    (training_data_blinkit[\"partner_setup_time\"] > q2)\n", "    & (training_data_blinkit[\"partner_setup_time\"] <= q4),\n", "    \"q2_q4\",\n", "    training_data_blinkit[\"partner_setup_time_bucket\"],\n", ")\n", "training_data_blinkit[\"partner_setup_time_bucket\"] = np.where(\n", "    (training_data_blinkit[\"partner_setup_time\"] > q4)\n", "    & (training_data_blinkit[\"partner_setup_time\"] <= q6),\n", "    \"q4_q6\",\n", "    training_data_blinkit[\"partner_setup_time_bucket\"],\n", ")\n", "training_data_blinkit[\"partner_setup_time_bucket\"] = np.where(\n", "    (training_data_blinkit[\"partner_setup_time\"] > q6)\n", "    & (training_data_blinkit[\"partner_setup_time\"] <= q8),\n", "    \"q6_q8\",\n", "    training_data_blinkit[\"partner_setup_time_bucket\"],\n", ")\n", "training_data_blinkit[\"partner_setup_time_bucket\"] = np.where(\n", "    (training_data_blinkit[\"partner_setup_time\"] > q8)\n", "    & (training_data_blinkit[\"partner_setup_time\"] <= q9),\n", "    \"q8_q9\",\n", "    training_data_blinkit[\"partner_setup_time_bucket\"],\n", ")\n", "training_data_blinkit[\"partner_setup_time_bucket\"] = np.where(\n", "    (training_data_blinkit[\"partner_setup_time\"] > q9)\n", "    & (training_data_blinkit[\"partner_setup_time\"] <= q95),\n", "    \"q9_q95\",\n", "    training_data_blinkit[\"partner_setup_time_bucket\"],\n", ")\n", "training_data_blinkit[\"partner_setup_time_bucket\"] = np.where(\n", "    (training_data_blinkit[\"partner_setup_time\"] > q95)\n", "    & (training_data_blinkit[\"partner_setup_time\"] <= q99),\n", "    \"q95_q99\",\n", "    training_data_blinkit[\"partner_setup_time_bucket\"],\n", ")\n", "training_data_blinkit[\"partner_setup_time_bucket\"] = np.where(\n", "    (training_data_blinkit[\"partner_setup_time\"] > q99),\n", "    \"beyond_q99\",\n", "    training_data_blinkit[\"partner_setup_time_bucket\"],\n", ")\n", "\n", "\n", "q2 = validation_data_blinkit[\"partner_setup_time\"].quantile(0.2)\n", "q4 = validation_data_blinkit[\"partner_setup_time\"].quantile(0.4)\n", "q6 = validation_data_blinkit[\"partner_setup_time\"].quantile(0.6)\n", "q8 = validation_data_blinkit[\"partner_setup_time\"].quantile(0.8)\n", "q9 = validation_data_blinkit[\"partner_setup_time\"].quantile(0.9)\n", "q95 = validation_data_blinkit[\"partner_setup_time\"].quantile(0.95)\n", "q99 = validation_data_blinkit[\"partner_setup_time\"].quantile(0.99)\n", "\n", "validation_data_blinkit[\"partner_setup_time_bucket\"] = np.where(\n", "    validation_data_blinkit[\"partner_setup_time\"] <= q2, \"less_than_q2\", \"others\"\n", ")\n", "validation_data_blinkit[\"partner_setup_time_bucket\"] = np.where(\n", "    (validation_data_blinkit[\"partner_setup_time\"] > q2)\n", "    & (validation_data_blinkit[\"partner_setup_time\"] <= q4),\n", "    \"q2_q4\",\n", "    validation_data_blinkit[\"partner_setup_time_bucket\"],\n", ")\n", "validation_data_blinkit[\"partner_setup_time_bucket\"] = np.where(\n", "    (validation_data_blinkit[\"partner_setup_time\"] > q4)\n", "    & (validation_data_blinkit[\"partner_setup_time\"] <= q6),\n", "    \"q4_q6\",\n", "    validation_data_blinkit[\"partner_setup_time_bucket\"],\n", ")\n", "validation_data_blinkit[\"partner_setup_time_bucket\"] = np.where(\n", "    (validation_data_blinkit[\"partner_setup_time\"] > q6)\n", "    & (validation_data_blinkit[\"partner_setup_time\"] <= q8),\n", "    \"q6_q8\",\n", "    validation_data_blinkit[\"partner_setup_time_bucket\"],\n", ")\n", "validation_data_blinkit[\"partner_setup_time_bucket\"] = np.where(\n", "    (validation_data_blinkit[\"partner_setup_time\"] > q8)\n", "    & (validation_data_blinkit[\"partner_setup_time\"] <= q9),\n", "    \"q8_q9\",\n", "    validation_data_blinkit[\"partner_setup_time_bucket\"],\n", ")\n", "validation_data_blinkit[\"partner_setup_time_bucket\"] = np.where(\n", "    (validation_data_blinkit[\"partner_setup_time\"] > q9)\n", "    & (validation_data_blinkit[\"partner_setup_time\"] <= q95),\n", "    \"q9_q95\",\n", "    validation_data_blinkit[\"partner_setup_time_bucket\"],\n", ")\n", "validation_data_blinkit[\"partner_setup_time_bucket\"] = np.where(\n", "    (validation_data_blinkit[\"partner_setup_time\"] > q95)\n", "    & (validation_data_blinkit[\"partner_setup_time\"] <= q99),\n", "    \"q95_q99\",\n", "    validation_data_blinkit[\"partner_setup_time_bucket\"],\n", ")\n", "validation_data_blinkit[\"partner_setup_time_bucket\"] = np.where(\n", "    (validation_data_blinkit[\"partner_setup_time\"] > q99),\n", "    \"beyond_q99\",\n", "    validation_data_blinkit[\"partner_setup_time_bucket\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "58cbf5e8-e3a4-4253-a62e-bd6221090673", "metadata": {}, "outputs": [], "source": ["training_data_blinkit[\"partner_setup_time_bucket\"].value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "eee25471-055e-4402-a4c8-49b87c02cbae", "metadata": {}, "outputs": [], "source": ["validation_data_blinkit[\"partner_setup_time_bucket\"].value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "235534aa-e49d-4dfa-989d-76b74ed864a8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "89f35c01-3e85-410b-91e3-457c3a0c4120", "metadata": {}, "outputs": [], "source": ["def stratified_sample_proportional(data, group_cols, frac, random_state=42):\n", "    # Add a group identifier\n", "    data[\"_group\"] = data.groupby(group_cols).ngroup()\n", "\n", "    # Get the number of samples to pick for each group\n", "    group_counts = data[\"_group\"].value_counts()\n", "    sample_counts = (group_counts * frac).round().astype(int)\n", "\n", "    # Randomly shuffle and select indices for each group, with error handling\n", "    np.random.seed(random_state)\n", "\n", "    def safe_sample(group):\n", "        try:\n", "            n_samples = sample_counts.loc[group.name]\n", "            if n_samples > 0 and n_samples <= len(group):\n", "                return group.sample(n=n_samples, random_state=random_state).index\n", "        except KeyError:\n", "            pass\n", "        return []  # return empty list if error or no sampling\n", "\n", "    selected_indices = data.groupby(\"_group\").apply(safe_sample).explode().dropna().astype(int)\n", "\n", "    # Drop the temporary column and return sampled data\n", "    sampled_data = data.loc[selected_indices].drop(columns=[\"_group\"])\n", "\n", "    return sampled_data.reset_index(drop=True)\n", "\n", "\n", "group_cols = [\n", "    \"frontend_merchant_id\",\n", "    \"order_weight_bucket\",\n", "    \"total_items_quantity_ordered_bucket\",\n", "    \"city_name\",\n", "    \"partner_setup_time_bucket\",\n", "]\n", "sampled_data_training_blinkit = stratified_sample_proportional(\n", "    training_data_blinkit, group_cols, frac=sampling_factor_training_blinkit\n", ")\n", "sampled_data_validation_blinkit = stratified_sample_proportional(\n", "    validation_data_blinkit, group_cols, frac=sampling_factor_validation_blinkit\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9b9317e5-842e-45d3-ad66-1255ccd9b4f5", "metadata": {}, "outputs": [], "source": ["sampled_data_training_blinkit.shape, sampled_data_validation_blinkit.shape, test_data_blinkit.shape"]}, {"cell_type": "code", "execution_count": null, "id": "16f32ef5-f860-4e47-82fa-dc7fd8efdf2f", "metadata": {}, "outputs": [], "source": ["training_data_after_sampling_blinkit = sampled_data_training_blinkit.copy()\n", "validation_data_after_sampling_blinkit = sampled_data_validation_blinkit.copy()\n", "\n", "del sampled_data_training_blinkit\n", "del sampled_data_validation_blinkit\n", "del training_data_blinkit\n", "del validation_data_blinkit\n", "\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "2912b116-4138-4199-aa99-3dc6186328e9", "metadata": {}, "outputs": [], "source": ["print(training_data_after_sampling_blinkit.shape)\n", "print(validation_data_after_sampling_blinkit.shape)\n", "print(test_data_blinkit.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "8e91f008-4510-478b-9029-e892a72ff575", "metadata": {}, "outputs": [], "source": ["print(training_data_after_sampling_blinkit[\"date\"].min())\n", "print(training_data_after_sampling_blinkit[\"date\"].max())\n", "\n", "print(validation_data_after_sampling_blinkit[\"date\"].min())\n", "print(validation_data_after_sampling_blinkit[\"date\"].max())\n", "\n", "print(test_data_blinkit[\"date\"].min())\n", "print(test_data_blinkit[\"date\"].max())"]}, {"cell_type": "code", "execution_count": null, "id": "ae93af59-821a-4f5c-9bbc-18d06146e947", "metadata": {}, "outputs": [], "source": ["print(training_data_bistro[\"date\"].min())\n", "print(training_data_bistro[\"date\"].max())\n", "\n", "print(validation_data_bistro[\"date\"].min())\n", "print(validation_data_bistro[\"date\"].max())\n", "\n", "print(test_data_bistro[\"date\"].min())\n", "print(test_data_bistro[\"date\"].max())"]}, {"cell_type": "code", "execution_count": null, "id": "940a0250-de9b-4d41-b04f-7da66eb03fd3", "metadata": {}, "outputs": [], "source": ["parquet_local_path_training = \"training_data_partner_setup_model_blinkit.parquet\"\n", "training_data_after_sampling_blinkit.to_parquet(\n", "    parquet_local_path_training, engine=\"pyarrow\", index=False\n", ")\n", "\n", "pb.to_s3(\n", "    parquet_local_path_training,\n", "    s3_bucket,\n", "    \"A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Blinkit/partner_setup_model/training_data/training_data_partner_setup_model_blinkit.parquet\",\n", ")\n", "\n", "\n", "parquet_local_path_validation = \"validation_data_partner_setup_model_blinkit.parquet\"\n", "validation_data_after_sampling_blinkit.to_parquet(\n", "    parquet_local_path_validation, engine=\"pyarrow\", index=False\n", ")\n", "\n", "pb.to_s3(\n", "    parquet_local_path_validation,\n", "    s3_bucket,\n", "    \"A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Blinkit/partner_setup_model/validation_data/validation_data_partner_setup_model_blinkit.parquet\",\n", ")\n", "\n", "\n", "parquet_local_path_test = \"test_data_partner_setup_model_blinkit.parquet\"\n", "test_data_blinkit.to_parquet(parquet_local_path_test, engine=\"pyarrow\", index=False)\n", "\n", "pb.to_s3(\n", "    parquet_local_path_test,\n", "    s3_bucket,\n", "    \"A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Blinkit/partner_setup_model/test_data/test_data_partner_setup_model_blinkit.parquet\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8adb7a58-b502-4081-a2db-1f2a91f68d8c", "metadata": {}, "outputs": [], "source": ["parquet_local_path_training = \"training_data_partner_setup_model_bistro.parquet\"\n", "training_data_bistro.to_parquet(parquet_local_path_training, engine=\"pyarrow\", index=False)\n", "\n", "pb.to_s3(\n", "    parquet_local_path_training,\n", "    s3_bucket,\n", "    \"A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Bistro/partner_setup_model/training_data/training_data_partner_setup_model_bistro.parquet\",\n", ")\n", "\n", "\n", "parquet_local_path_validation = \"validation_data_partner_setup_model_bistro.parquet\"\n", "validation_data_bistro.to_parquet(parquet_local_path_validation, engine=\"pyarrow\", index=False)\n", "\n", "pb.to_s3(\n", "    parquet_local_path_validation,\n", "    s3_bucket,\n", "    \"A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Bistro/partner_setup_model/validation_data/validation_data_partner_setup_model_bistro.parquet\",\n", ")\n", "\n", "\n", "parquet_local_path_test = \"test_data_partner_setup_model_bistro.parquet\"\n", "test_data_bistro.to_parquet(parquet_local_path_test, engine=\"pyarrow\", index=False)\n", "\n", "pb.to_s3(\n", "    parquet_local_path_test,\n", "    s3_bucket,\n", "    \"A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Bistro/partner_setup_model/test_data/test_data_partner_setup_model_bistro.parquet\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "55d95441-a43c-4dbc-92f4-3e9cc3caef1b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "968d96ce-c2c2-436f-8b58-2aa3d24d61aa", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "60e62453-8e9c-4ec3-ac64-c0725892fc8c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1e417f83-2067-4243-940f-99f022e44061", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}