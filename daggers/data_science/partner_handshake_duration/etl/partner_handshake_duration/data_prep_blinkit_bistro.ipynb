{"cells": [{"cell_type": "code", "execution_count": null, "id": "d8e8b31a-32f2-4638-a3d5-93eb01fc15a9", "metadata": {}, "outputs": [], "source": ["!pip install openpyxl\n", "!pip install pandas==1.5.1\n", "!pip install boto3==1.24.59\n", "!pip install requests-aws4auth==1.2.2\n", "!pip install s3transfer==0.6.0\n", "!pip install aenum==3.1.11\n", "!pip install scramp==1.4.4\n", "!pip install python-utils==3.5.2\n", "!pip install awswrangler==2.19.0\n", "!pip install s3fs==2023.1.0"]}, {"cell_type": "code", "execution_count": null, "id": "c8d289fb-7949-459f-9bbc-c2db9868a685", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "\n", "con_trino = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "50574248-d59c-42ab-93a8-2ae7b8ba4502", "metadata": {}, "outputs": [], "source": ["import asyncio\n", "import concurrent.futures\n", "import datetime as dt\n", "import os\n", "import threading\n", "import time\n", "from contextlib import contextmanager\n", "from datetime import datetime\n", "\n", "import boto3\n", "import pytz\n", "import sqlalchemy as sqla\n", "from sqlalchemy.dialects import postgresql\n", "from sqlalchemy.ext.automap import automap_base\n", "from sqlalchemy.orm import sessionmaker\n", "import gc\n", "\n", "import awswrangler as wr\n", "\n", "# import seaborn as sns\n", "# import matplotlib.pyplot as plt\n", "\n", "# from catboost import CatBoostRegressor\n", "# from sklearn.metrics import mean_absolute_error\n", "\n", "# import statistics\n", "from datetime import date, datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "36bacfb3-35c6-4278-ae1e-c376d18160b8", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)\n", "pd.set_option(\"display.max_rows\", None)\n", "pd.set_option(\"display.float_format\", lambda x: \"%.5f\" % x)\n", "pd.set_option(\"display.max_colwidth\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "dcb34447-8220-4e83-b406-2b3f332bf90c", "metadata": {}, "outputs": [], "source": ["trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "9850bdff-01c3-4e52-ba42-498e861b7628", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys"]}, {"cell_type": "code", "execution_count": null, "id": "ee369d8b-796a-4b9f-a96b-b4da689fa07f", "metadata": {}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "5ebdfce8-a418-4144-99d0-8d71ded9361d", "metadata": {}, "outputs": [], "source": ["cwd"]}, {"cell_type": "code", "execution_count": null, "id": "124d89ed-440e-433d-a180-101ec7b5ad8f", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "919c0806-c895-4da1-aee7-86e39c5792f0", "metadata": {}, "outputs": [], "source": ["s3_bucket = \"prod-dse-projects\""]}, {"cell_type": "markdown", "id": "06632143-4014-4fc1-becf-82f8c248b5ea", "metadata": {}, "source": ["## Data Pulling Pipeline"]}, {"cell_type": "markdown", "id": "85166a9d-a433-4702-b37f-e8444e787845", "metadata": {}, "source": ["### Blinkit Data"]}, {"cell_type": "code", "execution_count": null, "id": "5ed5cc9c-e291-4efd-a4e0-695010151c1a", "metadata": {"tags": []}, "outputs": [], "source": ["# current_date = datetime.now(pytz.timezone(\"Asia/Kolkata\"))\n", "\n", "# start_date_blinkit = current_date - timed<PERSON><PERSON>(days=46)\n", "# validation_date_blinkit = current_date - timedelta(days=15)\n", "# test_date_blinkit = current_date - timed<PERSON>ta(days=8)\n", "# end_date_blinkit = current_date - timed<PERSON>ta(days=1)\n", "\n", "# START_DATE_Blinkit, VALIDATION_DATE_Blinkit, TEST_DATE_Blinkit, END_DATE_Blinkit = (\n", "#     str(start_date_blinkit.date()),\n", "#     str(validation_date_blinkit.date()),\n", "#     str(test_date_blinkit.date()),\n", "#     str(end_date_blinkit.date()),\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "baef7332-4c30-40bb-a115-cdc3fd05fc38", "metadata": {}, "outputs": [], "source": ["current_date = datetime.now(pytz.timezone(\"Asia/Kolkata\"))\n", "\n", "start_date_blinkit = current_date - <PERSON><PERSON><PERSON>(days=51)\n", "validation_date_blinkit = current_date - timed<PERSON>ta(days=15)\n", "test_date_blinkit = current_date - timed<PERSON><PERSON>(days=8)\n", "end_date_blinkit = current_date - <PERSON><PERSON><PERSON>(days=1)\n", "\n", "START_DATE_Blinkit, VALIDATION_DATE_Blinkit, TEST_DATE_Blinkit, END_DATE_Blinkit = (\n", "    str(start_date_blinkit.date()),\n", "    str(validation_date_blinkit.date()),\n", "    str(test_date_blinkit.date()),\n", "    str(end_date_blinkit.date()),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "28ed3fea-de6c-4f23-b698-eba8743de40a", "metadata": {}, "outputs": [], "source": ["START_DATE_Blinkit, VALIDATION_DATE_Blinkit, TEST_DATE_Blinkit, END_DATE_Blinkit"]}, {"cell_type": "code", "execution_count": null, "id": "ede7a5c6-c51a-4a30-9307-ebf2b28ceb61", "metadata": {}, "outputs": [], "source": ["output_dir_blinkit = f\"dataset_blinkit_{START_DATE_Blinkit}_to_{END_DATE_Blinkit}\"\n", "output_dir_blinkit"]}, {"cell_type": "code", "execution_count": null, "id": "0993b01b-72b0-40db-a199-413c8b2aaa3a", "metadata": {}, "outputs": [], "source": ["def get_date_checkpoints(start_date_, end_date_, step_):\n", "    iter_date = start_date_\n", "    date_list_ = []\n", "    while iter_date < end_date_:\n", "        date_list_.append(iter_date)\n", "        iter_date += <PERSON><PERSON>ta(days=step_)\n", "    date_list_.append(end_date_)\n", "    return date_list_"]}, {"cell_type": "code", "execution_count": null, "id": "6019c42b-a4bc-408c-baa3-d62b6f034cd4", "metadata": {}, "outputs": [], "source": ["step = 5\n", "\n", "\n", "date_list_blinkit = get_date_checkpoints(start_date_blinkit, end_date_blinkit, step)"]}, {"cell_type": "code", "execution_count": null, "id": "1d375631-fb6a-4625-94ec-3c2b797cf772", "metadata": {}, "outputs": [], "source": ["print(len(date_list_blinkit))\n", "print(date_list_blinkit)"]}, {"cell_type": "code", "execution_count": null, "id": "21d37549-12b8-4d00-8e91-bc0e1a3eec00", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "from datetime import timedelta\n", "import logging\n", "\n", "logger = logging.getLogger(__name__)\n", "\n", "\n", "def get_data_in_chunks(\n", "    read_data,\n", "    start_date,\n", "    end_date,\n", "    li_date_list,\n", "    org_name,\n", "    output_dir,\n", "    connection=\"[Warehouse] Trino\",\n", "    variable_to_format={},\n", "):\n", "\n", "    if variable_to_format is None:\n", "        variable_to_format = {}\n", "\n", "    # Initialize results\n", "    final_result = []\n", "\n", "    # Iterate through date chunks\n", "    for i in range(len(li_date_list) - 1):\n", "        sd = li_date_list[i].strftime(\"%Y-%m-%d\")\n", "        ed = li_date_list[i + 1].strftime(\"%Y-%m-%d\")\n", "\n", "        if i + 2 >= len(li_date_list):\n", "            ed_new = (li_date_list[i + 1] + <PERSON><PERSON><PERSON>(days=1)).strftime(\"%Y-%m-%d\")\n", "        else:\n", "            ed_new = li_date_list[i + 1].strftime(\"%Y-%m-%d\")\n", "        print(f\"Processing start_date: {sd}, end_date: {ed}\")\n", "\n", "        print(f\"Fetching data from {sd} to {ed}\")\n", "        variable_to_format[\"start_date\"] = sd\n", "        variable_to_format[\"end_date\"] = ed_new\n", "\n", "        parquet_location = \"data_\" + str(org_name) + \"_\" + str(sd) + \"_to_\" + str(ed) + \".parquet\"\n", "\n", "        s3_paths_date_list = (\n", "            \"s3://prod-dse-projects/A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/\"\n", "            + str(org_name)\n", "            + \"/partner_setup_model/data_prep/\"\n", "            + str(output_dir)\n", "            + \"/\"\n", "        )\n", "        s3_paths_obj_date_list = wr.s3.list_objects(s3_paths_date_list)\n", "        exists_date_list = any(parquet_location in path for path in s3_paths_obj_date_list)\n", "\n", "        if exists_date_list:\n", "            print(f\"Skipping existing file: {parquet_location}\")\n", "\n", "        else:\n", "\n", "            # # Execute query for the current date range\n", "            # chunk_data = pd.read_sql(\n", "            #     query.format(**variable_to_format), con=pb.get_connection(connection)\n", "            # )\n", "\n", "            chunk_data = read_data(sd, ed_new, trino_con)\n", "\n", "            # print(chunk_data[\"order_id\"].value_counts())\n", "\n", "            parquet_location = (\n", "                \"data_\" + str(org_name) + \"_\" + str(sd) + \"_to_\" + str(ed) + \".parquet\"\n", "            )\n", "            chunk_data.to_parquet(parquet_location, engine=\"pyarrow\", index=False)\n", "\n", "            pb.to_s3(\n", "                parquet_location,\n", "                s3_bucket,\n", "                \"A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/\"\n", "                + str(org_name)\n", "                + \"/partner_setup_model/data_prep/\"\n", "                + str(output_dir)\n", "                + \"/\"\n", "                + \"data_\"\n", "                + str(org_name)\n", "                + \"_\"\n", "                + str(sd)\n", "                + \"_to_\"\n", "                + str(ed)\n", "                + \".parquet\",\n", "            )\n", "            print(f\" Processing complete for, {parquet_location}\")\n", "\n", "    return 1"]}, {"cell_type": "code", "execution_count": null, "id": "f79a2a12-b64e-4e1c-93d0-b06d52c2ba30", "metadata": {}, "outputs": [], "source": ["def read_data_blinkit(start_date, end_date, trino_connection):\n", "\n", "    query = f\"\"\"\n", "    with base as(select\n", "    order_checkout_dt_ist date,\n", "    dm.city_name,\n", "    dm.merchant_name,\n", "    scod.frontend_merchant_id,\n", "    fsod.order_id,\n", "    fsod.is_surge_order,\n", "    scod.distinct_items_ordered,\n", "    scod.total_cold_items_ordered,\n", "    scod.is_rain_order,\n", "    scod.order_weight,\n", "    scod.is_order_with_fnv,\n", "    scod.total_items_quantity_ordered,\n", "    order_checkout_ts_ist,\n", "    date_diff('second',order_picking_started_ts_ist,order_picking_completed_ts_ist)*1.00/60 as picking_time,\n", "    date_diff('second',greatest(order_billing_completed_ts_ist,order_partner_assigned_ts_ist),order_enroute_ts_ist)*1.00/60 as partner_setup_time  \n", "\n", "\n", "    from\n", "    dwh.fact_supply_chain_order_details scod\n", "    JOIN\n", "    dwh.fact_sales_order_details fsod\n", "    on fsod.order_id = scod.order_id\n", "    join\n", "    dwh.dim_merchant dm on dm.merchant_id = scod.frontend_merchant_id and is_current\n", "\n", "\n", "    where\n", "    scod.order_checkout_dt_ist >= date ('{start_date}')\n", "    and scod.order_checkout_dt_ist < date ('{end_date}')\n", "    and fsod.order_create_dt_ist  >= date ('{start_date}')\n", "    and fsod.order_create_dt_ist < date ('{end_date}')\n", "    and is_internal_order = False\n", "    and fsod.order_current_status = 'DELIVERED'\n", "\n", "     )\n", "\n", "    select \n", "    *\n", "\n", "    from\n", "    base\n", "    \n", "    \"\"\"\n", "    data_df = pd.read_sql_query(sql=query, con=trino_connection)\n", "\n", "    return data_df"]}, {"cell_type": "code", "execution_count": null, "id": "724e43e1-118e-4e18-a357-a90fa48cd875", "metadata": {}, "outputs": [], "source": ["partner_setup_details_blinkit = get_data_in_chunks(\n", "    read_data_blinkit,\n", "    start_date_blinkit,\n", "    end_date_blinkit,\n", "    date_list_blinkit,\n", "    org_name=\"Blinkit\",\n", "    output_dir=output_dir_blinkit,\n", "    connection=\"[Warehouse] Trino\",\n", "    variable_to_format={},\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ffd40f0d-2b1f-4518-84bd-3c1993a9a61b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "311778da-9db6-473d-b5c4-b4b4773d258a", "metadata": {}, "source": ["### Bistro Data"]}, {"cell_type": "code", "execution_count": null, "id": "419efb20-724d-4d80-ba8e-83b094db1eba", "metadata": {}, "outputs": [], "source": ["current_date = datetime.now(pytz.timezone(\"Asia/Kolkata\"))\n", "\n", "ts = pd.Timestamp(\"2024-10-15 00:00:00\")\n", "\n", "# Convert to timezone-aware datetime in Asia/Kolkata\n", "dt = ts.tz_localize(\"Asia/Kolkata\")\n", "\n", "# Convert to Python datetime object (still timezone-aware)\n", "start_date_bistro = dt.to_pydatetime()\n", "\n", "start_date_bistro = pd.to_datetime(start_date_bistro)\n", "validation_date_bistro = current_date - <PERSON><PERSON><PERSON>(days=15)\n", "test_date_bistro = current_date - <PERSON><PERSON><PERSON>(days=8)\n", "end_date_bistro = current_date - <PERSON><PERSON><PERSON>(days=1)\n", "\n", "START_DATE_Bistro, VALIDATION_DATE_Bistro, TEST_DATE_Bistro, END_DATE_Bistro = (\n", "    str(start_date_bistro.date()),\n", "    str(validation_date_bistro.date()),\n", "    str(test_date_bistro.date()),\n", "    str(end_date_bistro.date()),\n", ")\n", "\n", "\n", "start_date_bistro1 = start_date_bistro + <PERSON><PERSON><PERSON>(days=7)\n", "START_DATE_Bistro1 = str(start_date_bistro1.date())"]}, {"cell_type": "code", "execution_count": null, "id": "4aae85d3-54d4-41f1-962d-ce96363d3ac9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "544112b6-1c9d-4d5a-ae38-e5a95f9ea705", "metadata": {}, "outputs": [], "source": ["START_DATE_Bistro, VALIDATION_DATE_Bistro, TEST_DATE_Bistro, END_DATE_Bistro"]}, {"cell_type": "code", "execution_count": null, "id": "313d2d90-3564-4d10-95ca-0c035b261a37", "metadata": {}, "outputs": [], "source": ["output_dir_bistro = f\"dataset_bistro_{START_DATE_Bistro}_to_{END_DATE_Bistro}\"\n", "output_dir_bistro"]}, {"cell_type": "code", "execution_count": null, "id": "f6e2285b-9007-4123-9e22-3b44913227f0", "metadata": {}, "outputs": [], "source": ["step = 20\n", "date_list_bistro = get_date_checkpoints(start_date_bistro, end_date_bistro, step)"]}, {"cell_type": "code", "execution_count": null, "id": "c8f706f0-0020-499f-a144-3e7b5f75b7a9", "metadata": {}, "outputs": [], "source": ["print(len(date_list_bistro))\n", "print(date_list_bistro)"]}, {"cell_type": "code", "execution_count": null, "id": "72fe7642-771a-4bd0-8631-3d07550a887b", "metadata": {}, "outputs": [], "source": ["def read_data_bistro(start_date, end_date, trino_connection):\n", "\n", "    query = f\"\"\"\n", "    with base as(select\n", "        order_checkout_dt_ist date,\n", "        dm.city_name,\n", "        dm.merchant_name,\n", "        scod.frontend_merchant_id,\n", "        fsod.order_id,\n", "        fsod.is_surge_order,\n", "        scod.distinct_items_ordered,\n", "        scod.total_cold_items_ordered,\n", "        scod.total_items_quantity_ordered,\n", "        order_checkout_ts_ist,\n", "        date_diff('second',greatest(order_billing_completed_ts_ist,order_partner_assigned_ts_ist),order_enroute_ts_ist)*1.00/60 as partner_setup_time  \n", "\n", "\n", "    from\n", "        bistro_etls.fact_supply_chain_order_details_bistro scod\n", "    JOIN\n", "        bistro_etls.fact_sales_order_details_bistro fsod\n", "        on fsod.order_id = scod.order_id\n", "    join\n", "        dwh.dim_merchant dm on dm.merchant_id = scod.frontend_merchant_id and is_current\n", "\n", "\n", "    where\n", "    scod.order_checkout_dt_ist >= date ('{start_date}')\n", "    and scod.order_checkout_dt_ist < date ('{end_date}')\n", "    and fsod.order_create_dt_ist  >= date ('{start_date}')\n", "    and fsod.order_create_dt_ist < date ('{end_date}')\n", "    and is_internal_order = False\n", "    and fsod.order_current_status = 'DELIVERED'\n", "\n", "     )\n", "\n", "    select \n", "    *\n", "\n", "    from\n", "    base\n", "\n", "    \"\"\"\n", "    data_df = pd.read_sql_query(sql=query, con=trino_connection)\n", "\n", "    return data_df"]}, {"cell_type": "code", "execution_count": null, "id": "69e3909c-7f48-4a57-9357-a53d59f44ad2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6c433868-acca-4293-a390-3279ba92d979", "metadata": {"tags": []}, "outputs": [], "source": ["partner_setup_details_bistro = get_data_in_chunks(\n", "    read_data_bistro,\n", "    start_date_bistro,\n", "    end_date_bistro,\n", "    date_list_bistro,\n", "    org_name=\"Bistro\",\n", "    output_dir=output_dir_bistro,\n", "    connection=\"[Warehouse] Trino\",\n", "    variable_to_format={},\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "264b5bc0-a649-43cc-b625-99577e912053", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ce544c12-b829-460b-9eab-a08038853449", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a4e7e646-90a0-4f49-9408-f52ca9c59fb3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}