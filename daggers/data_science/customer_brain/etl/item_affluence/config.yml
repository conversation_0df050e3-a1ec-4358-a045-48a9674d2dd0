alert_configs:
  slack:
  - channel: bl-personalization-notifications
dag_name: item_affluence
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: low
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RZ3PCPSA
path: data_science/customer_brain/etl/item_affluence
paused: false
pool: data_science_pool
project_name: customer_brain
schedule:
  end_date: '2025-09-07T00:00:00'
  interval: 0 0 * * *
  start_date: '2024-06-30T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- queries/*
tags: []
template_name: notebook
version: 5
