alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: customer_gender_mapping
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: low
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U082DA8JB6X
path: data_science/customer_brain/etl/customer_gender_mapping
paused: false
pool: data_science_pool
project_name: customer_brain
schedule:
  end_date: '2025-07-21T00:00:00'
  interval: 0 0 * * 6
  start_date: '2024-12-23T00:00:00'
schedule_type: fixed
sla: 121 minutes
support_files: []
tags: []
template_name: notebook
version: 4
