{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Installing Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install -q catboost\n", "!pip install -q joblib\n", "!pip install -q pytz\n", "!pip install h3==3.7.6\n", "!optuna==3.6.1\n", "!pip install haversine"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install -q kafka-python==2.0.2\n", "!pip install -q scipy==1.8.0\n", "!pip install aiohttp==3.8.1\n", "!pip install fsspec==2023.1.0\n", "!pip install aiobotocore==2.4.2\n", "!pip install pymysql==1.0.2\n", "!pip install gremlinpython==3.6.2\n", "!pip install botocore==1.27.59\n", "!pip install progressbar2==4.2.0\n", "!pip install backoff==2.2.1\n", "!pip install pandas==1.5.1\n", "!pip install pg8000==1.29.4\n", "!pip install opensearch-py==2.1.1\n", "!pip install boto3==1.24.59\n", "!pip install requests-aws4auth==1.2.2\n", "!pip install s3transfer==0.6.0\n", "!pip install aenum==3.1.11\n", "!pip install scramp==1.4.4\n", "!pip install python-utils==3.5.2\n", "!pip install awswrangler==2.19.0\n", "!pip install s3fs==2023.1.0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install scikit-learn"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install polars"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from concurrent.futures import ThreadPoolExecutor\n", "from sklearn.metrics import mean_absolute_error\n", "from datetime import date, datetime, timedelta\n", "from catboost import CatBoostRegressor\n", "from collections import OrderedDict\n", "from multiprocessing import Pool\n", "import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "from h3 import h3\n", "import warnings\n", "import requests\n", "import logging\n", "import pickle\n", "import joblib\n", "import random\n", "import shutil\n", "import glob\n", "import math\n", "import time\n", "import pytz\n", "import json\n", "import os\n", "import gc\n", "\n", "import awswrangler as wr\n", "\n", "import haversine as hs\n", "from haversine import Unit, haversine_vector\n", "\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "# tqdm.pandas()\n", "pd.set_option(\"display.max_rows\", 100)\n", "pd.set_option(\"display.max_columns\", 100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["s3_bucket = \"prod-dse-projects\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Date settings"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Blinkit data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_date = datetime.now(pytz.timezone(\"Asia/Kolkata\"))\n", "\n", "start_date_blinkit = current_date - <PERSON><PERSON><PERSON>(days=46)\n", "validation_date_blinkit = current_date - timed<PERSON>ta(days=15)\n", "test_date_blinkit = current_date - timed<PERSON><PERSON>(days=8)\n", "end_date_blinkit = current_date - <PERSON><PERSON><PERSON>(days=1)\n", "\n", "START_DATE_blinkit, VALIDATION_DATE_blinkit, TEST_DATE_blinkit, END_DATE_blinkit = (\n", "    str(start_date_blinkit.date()),\n", "    str(validation_date_blinkit.date()),\n", "    str(test_date_blinkit.date()),\n", "    str(end_date_blinkit.date()),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output_dir_blinkit = f\"dataset_blinkit_{START_DATE_blinkit}_to_{END_DATE_blinkit}\"\n", "output_dir_blinkit"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Bistro data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_date = datetime.now(pytz.timezone(\"Asia/Kolkata\"))\n", "\n", "ts = pd.Timestamp(\"2024-10-15 00:00:00\")\n", "\n", "# Convert to timezone-aware datetime in Asia/Kolkata\n", "dt = ts.tz_localize(\"Asia/Kolkata\")\n", "\n", "# Convert to Python datetime object (still timezone-aware)\n", "start_date_bistro = dt.to_pydatetime()\n", "\n", "start_date_bistro = pd.to_datetime(start_date_bistro)\n", "validation_date_bistro = current_date - <PERSON><PERSON><PERSON>(days=15)\n", "test_date_bistro = current_date - <PERSON><PERSON><PERSON>(days=8)\n", "end_date_bistro = current_date - <PERSON><PERSON><PERSON>(days=1)\n", "\n", "START_DATE_bistro, VALIDATION_DATE_bistro, TEST_DATE_bistro, END_DATE_bistro = (\n", "    str(start_date_bistro.date()),\n", "    str(validation_date_bistro.date()),\n", "    str(test_date_bistro.date()),\n", "    str(end_date_bistro.date()),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output_dir_bistro = f\"dataset_bistro_{START_DATE_bistro}_to_{END_DATE_bistro}\"\n", "output_dir_bistro"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Read data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Blinkit data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["s3_paths_blinkit = f\"s3://prod-dse-projects/A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Blinkit/travel_model/data_prep/{output_dir_blinkit}/\"\n", "s3_paths_obj_blinkit = wr.s3.list_objects(s3_paths_blinkit)\n", "s3_paths_obj_blinkit"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["travel_data_blinkit = pd.DataFrame()\n", "for i in range(len(s3_paths_obj_blinkit)):\n", "    travel_data_blinkit_temp = pd.read_parquet(s3_paths_obj_blinkit[i])\n", "\n", "    travel_data_blinkit = pd.concat([travel_data_blinkit, travel_data_blinkit_temp])\n", "\n", "    del travel_data_blinkit_temp"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(travel_data_blinkit.shape)\n", "travel_data_blinkit.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["travel_data_blinkit.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["travel_data_blinkit.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["travel_data_blinkit.polygon_type.value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Bistro Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["s3_paths_bistro = f\"s3://prod-dse-projects/A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Bistro/travel_model/data_prep/{output_dir_bistro}/\"\n", "s3_paths_obj_bistro = wr.s3.list_objects(s3_paths_bistro)\n", "s3_paths_obj_bistro"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["travel_data_bistro = pd.DataFrame()\n", "for i in range(len(s3_paths_obj_bistro)):\n", "    travel_data_bistro_temp = pd.read_parquet(s3_paths_obj_bistro[i])\n", "\n", "    travel_data_bistro = pd.concat([travel_data_bistro, travel_data_bistro_temp])\n", "\n", "    del travel_data_bistro_temp"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(travel_data_bistro.shape)\n", "travel_data_bistro.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["travel_data_bistro.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["travel_data_bistro.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["travel_data_bistro.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["travel_data_bistro.polygon_type.value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Post Processing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def feature_creation_function(trip_details, VALIDATION_DATE, TEST_DATE, org_name):\n", "\n", "    #     trip_details = data.copy()\n", "\n", "    #     del data\n", "    #     gc.collect()\n", "\n", "    trip_details[[\"in_poi\", \"is_rain_order\", \"is_nearby_store_order\"]] = trip_details[\n", "        [\"in_poi\", \"is_rain_order\", \"is_nearby_store_order\"]\n", "    ].fillna(False)\n", "\n", "    trip_details = trip_details.dropna(\n", "        subset=[\n", "            \"trip_id\",\n", "            \"delivery_time\",\n", "            \"customer_merchant_dist\",\n", "            \"customer_location_latitude\",\n", "            \"customer_location_longitude\",\n", "            \"speed_km_h\",\n", "        ]\n", "    )\n", "\n", "    print(trip_details.shape, trip_details.dropna().shape)\n", "\n", "    trip_details = trip_details.astype(\n", "        {\"trip_start_timestamp\": \"datetime64[ns]\", \"speed_km_h\": float}\n", "    )\n", "\n", "    customer_hex_subset = trip_details[\n", "        [\n", "            \"customer_location_latitude\",\n", "            \"customer_location_longitude\",\n", "            \"merchant_store_latitude\",\n", "            \"merchant_store_longitude\",\n", "        ]\n", "    ].drop_duplicates()\n", "\n", "    customer_hex_subset[\"customer_hex9\"] = customer_hex_subset.apply(\n", "        lambda x: h3.geo_to_h3(\n", "            x[\"customer_location_latitude\"], x[\"customer_location_longitude\"], 9\n", "        ),\n", "        axis=1,\n", "    )\n", "\n", "    customer_hex_subset[\"customer_hex10\"] = customer_hex_subset.apply(\n", "        lambda x: h3.geo_to_h3(\n", "            x[\"customer_location_latitude\"], x[\"customer_location_longitude\"], 10\n", "        ),\n", "        axis=1,\n", "    )\n", "\n", "    customer_hex_subset[\"merchant_hex9\"] = customer_hex_subset.apply(\n", "        lambda x: h3.geo_to_h3(x[\"merchant_store_latitude\"], x[\"merchant_store_longitude\"], 9),\n", "        axis=1,\n", "    )\n", "\n", "    customer_hex_subset[\"merchant_hex10\"] = customer_hex_subset.apply(\n", "        lambda x: h3.geo_to_h3(x[\"merchant_store_latitude\"], x[\"merchant_store_longitude\"], 10),\n", "        axis=1,\n", "    )\n", "\n", "    trip_details_with_hex = trip_details.merge(customer_hex_subset, how=\"left\")\n", "\n", "    print(trip_details_with_hex.shape)\n", "\n", "    b = [i for i in range(25)]  # Bins from 0 to 24 hours\n", "    l = [i for i in range(24)]  # Labels for each hour\n", "    trip_details_with_hex[\"part_of_day\"] = pd.cut(\n", "        trip_details_with_hex[\"trip_start_timestamp\"].astype(\"datetime64[ns]\").dt.hour,\n", "        bins=b,\n", "        labels=l,\n", "        include_lowest=True,\n", "        right=False,  # Ensures the right edge is exclusive\n", "    )\n", "\n", "    trip_details_with_hex[\"day_of_week\"] = trip_details_with_hex.trip_start_timestamp.astype(\n", "        \"datetime64[ns]\"\n", "    ).dt.dayofweek\n", "\n", "    # print(trip_details_with_hex.is_nearby_store_order.value_counts())\n", "\n", "    del trip_details, customer_hex_subset\n", "    gc.collect()\n", "\n", "    # Prepare arrays of coordinates\n", "    customer_coords = trip_details_with_hex[\n", "        [\"customer_location_latitude\", \"customer_location_longitude\"]\n", "    ].to_numpy()\n", "    merchant_coords = trip_details_with_hex[\n", "        [\"merchant_store_latitude\", \"merchant_store_longitude\"]\n", "    ].to_numpy()\n", "\n", "    # Calculate distances using haversine_vector\n", "    trip_details_with_hex[\"latlong_distance_km\"] = haversine_vector(\n", "        customer_coords, merchant_coords, unit=Unit.KILOMETERS\n", "    )\n", "\n", "    relevant_trips = trip_details_with_hex[trip_details_with_hex.polygon_type != \"unicorn\"]\n", "\n", "    print(relevant_trips.shape)\n", "\n", "    trips = relevant_trips[\n", "        [\n", "            \"trip_id\",\n", "            \"order_id\",\n", "            \"trip_start_timestamp\",\n", "            \"darkstore_id\",\n", "            \"part_of_day\",\n", "            \"day_of_week\",\n", "            \"customer_merchant_dist\",\n", "            \"is_rain_order\",\n", "            \"is_nearby_store_order\",\n", "            \"distinct_items_ordered\",\n", "            \"total_items_quantity_ordered\",\n", "            \"city_name\",\n", "            \"polygon_type\",\n", "            \"customer_hex9\",\n", "            \"customer_hex10\",\n", "            \"in_poi\",\n", "            \"merchant_hex9\",\n", "            \"merchant_hex10\",\n", "            \"latlong_distance_km\",\n", "            \"speed_km_h\",\n", "            \"delivery_time\",\n", "        ]\n", "    ].dropna()\n", "\n", "    del trip_details_with_hex, relevant_trips\n", "    gc.collect()\n", "\n", "    trips[\"is_rain_order\"] = trips[\"is_rain_order\"].astype(int)\n", "    trips[\"is_nearby_store_order\"] = trips[\"is_nearby_store_order\"].astype(int)\n", "    trips[\"in_poi\"] = trips[\"in_poi\"].astype(int)\n", "\n", "    trips[\"polygon_type\"] = trips[\"polygon_type\"].apply(lambda x: 1 if x == \"express\" else 0)\n", "\n", "    print(trips.shape)\n", "\n", "    # Define the bin edges\n", "    delivery_time_bin = [\n", "        -float(\"inf\"),\n", "        5,\n", "        10,\n", "        15,\n", "        20,\n", "        25,\n", "        30,\n", "        40,\n", "        float(\"inf\"),\n", "    ]\n", "\n", "    # Apply pd.cut\n", "    trips[\"delivery_time_binned\"] = pd.cut(\n", "        trips[\"delivery_time\"],\n", "        bins=delivery_time_bin,\n", "        labels=[\n", "            \"within_5mins\",\n", "            \"5_to_10_mins\",\n", "            \"10_to_15_mins\",\n", "            \"15_to_20_mins\",\n", "            \"20_to_25_mins\",\n", "            \"25_to_30_mins\",\n", "            \"30_to_40_mins\",\n", "            \"beyond_40mins\",\n", "        ],\n", "        include_lowest=True,\n", "    )\n", "\n", "    # Define the bin edges\n", "    distance_bin = [\n", "        -float(\"inf\"),\n", "        0.3,\n", "        1,\n", "        3,\n", "        5,\n", "        7,\n", "        float(\"inf\"),\n", "    ]\n", "\n", "    # Apply pd.cut\n", "    trips[\"customer_merchant_dist_binned\"] = pd.cut(\n", "        trips[\"customer_merchant_dist\"],\n", "        bins=distance_bin,\n", "        labels=[\n", "            \"within_300m\",\n", "            \"300m_to_1km\",\n", "            \"1km_to_3km\",\n", "            \"3km_to_5km\",\n", "            \"5km_to_7km\",\n", "            \"beyond_7km\",\n", "        ],\n", "        include_lowest=True,\n", "    )\n", "\n", "    trips = trips[trips.delivery_time > 0.0]\n", "\n", "    print(trips.shape)\n", "\n", "    training_data = trips.query(f\"trip_start_timestamp <'{VALIDATION_DATE}'\")\n", "\n", "    validation_data = trips.query(\n", "        f\"trip_start_timestamp >= '{VALIDATION_DATE}' and trip_start_timestamp < '{TEST_DATE}'\"\n", "    )\n", "\n", "    test_data = trips.query(f\"trip_start_timestamp >= '{TEST_DATE}'\")\n", "\n", "    del trips\n", "    gc.collect()\n", "\n", "    if org_name == \"Blinkit\":\n", "\n", "        # Removing outliers\n", "        training_data = training_data.query(\n", "            f\"delivery_time != 0 and delivery_time <= 60 & delivery_time >= 2.2 & speed_km_h >= {np.percentile(training_data.speed_km_h.dropna(),2)} & speed_km_h <= 70.0 & customer_merchant_dist > 0.3 \"\n", "        )\n", "\n", "        validation_data = validation_data.query(\n", "            f\"delivery_time != 0 and delivery_time <= 60 & delivery_time >= 2.2 & speed_km_h >= {np.percentile(validation_data.speed_km_h.dropna(),2)} & speed_km_h <= 70.0 & customer_merchant_dist > 0.3 \"\n", "        )\n", "\n", "    if org_name == \"Bistro\":\n", "\n", "        # Removing outliers\n", "        training_data = training_data.query(\n", "            f\"delivery_time != 0 and delivery_time <= 30 & delivery_time >= 2 & speed_km_h >= {np.percentile(training_data.speed_km_h.dropna(),2)} & speed_km_h <= 70.0 \"\n", "        )\n", "\n", "        validation_data = validation_data.query(\n", "            f\"delivery_time != 0 and delivery_time <= 30 & delivery_time >= 2 & speed_km_h >= {np.percentile(validation_data.speed_km_h.dropna(),2)} & speed_km_h <= 70.0 \"\n", "        )\n", "\n", "    return training_data, validation_data, test_data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_data_bistro, validation_data_bistro, test_data_bistro = feature_creation_function(\n", "    travel_data_bistro, VALIDATION_DATE_bistro, TEST_DATE_bistro, \"Bistro\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(training_data_bistro.shape)\n", "print(validation_data_bistro.shape)\n", "print(test_data_bistro.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_data_blinkit, validation_data_blinkit, test_data_blinkit = feature_creation_function(\n", "    travel_data_blinkit, VALIDATION_DATE_blinkit, TEST_DATE_blinkit, \"Blinkit\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(training_data_blinkit.shape)\n", "print(validation_data_blinkit.shape)\n", "print(test_data_blinkit.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bistro_training_data_rows = training_data_bistro.shape[0]\n", "bistro_validation_data_rows = validation_data_bistro.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_data_blinkit_desired_rows = bistro_training_data_rows * 49\n", "validation_data_blinkit_desired_rows = bistro_validation_data_rows * 49"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sampling_factor_training_blinkit = (\n", "    training_data_blinkit_desired_rows / training_data_blinkit.shape[0]\n", ")\n", "sampling_factor_validation_blinkit = (\n", "    validation_data_blinkit_desired_rows / validation_data_blinkit.shape[0]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(sampling_factor_training_blinkit)\n", "print(sampling_factor_validation_blinkit)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def stratified_sample_proportional(data, group_cols, frac, random_state=42):\n", "    # Add a group identifier\n", "    data[\"_group\"] = data.groupby(group_cols).ngroup()\n", "\n", "    # Get the number of samples to pick for each group\n", "    group_counts = data[\"_group\"].value_counts()\n", "    sample_counts = (group_counts * frac).round().astype(int)\n", "\n", "    # Randomly shuffle and select indices for each group, with error handling\n", "    np.random.seed(random_state)\n", "\n", "    def safe_sample(group):\n", "        try:\n", "            n_samples = sample_counts.loc[group.name]\n", "            if n_samples > 0 and n_samples <= len(group):\n", "                return group.sample(n=n_samples, random_state=random_state).index\n", "        except KeyError:\n", "            pass\n", "        return []  # return empty list if error or no sampling\n", "\n", "    selected_indices = data.groupby(\"_group\").apply(safe_sample).explode().dropna().astype(int)\n", "\n", "    # Drop the temporary column and return sampled data\n", "    sampled_data = data.loc[selected_indices].drop(columns=[\"_group\"])\n", "\n", "    return sampled_data.reset_index(drop=True)\n", "\n", "\n", "group_cols = [\n", "    \"customer_hex10\",\n", "    \"merchant_hex10\",\n", "    \"city_name\",\n", "    \"delivery_time_binned\",\n", "    \"customer_merchant_dist_binned\",\n", "]\n", "\n", "sampled_data_training_blinkit = stratified_sample_proportional(\n", "    training_data_blinkit, group_cols, sampling_factor_training_blinkit\n", ")\n", "sampled_data_validation_blinkit = stratified_sample_proportional(\n", "    validation_data_blinkit, group_cols, sampling_factor_validation_blinkit\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sampled_data_training_blinkit.shape, sampled_data_validation_blinkit.shape, test_data_blinkit.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_data_after_sampling_blinkit = sampled_data_training_blinkit.copy()\n", "validation_data_after_sampling_blinkit = sampled_data_validation_blinkit.copy()\n", "\n", "del sampled_data_training_blinkit\n", "del sampled_data_validation_blinkit\n", "del training_data_blinkit\n", "del validation_data_blinkit\n", "\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(training_data_after_sampling_blinkit.shape)\n", "print(validation_data_after_sampling_blinkit.shape)\n", "print(test_data_blinkit.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(training_data_after_sampling_blinkit[\"trip_start_timestamp\"].min())\n", "print(training_data_after_sampling_blinkit[\"trip_start_timestamp\"].max())\n", "\n", "print(validation_data_after_sampling_blinkit[\"trip_start_timestamp\"].min())\n", "print(validation_data_after_sampling_blinkit[\"trip_start_timestamp\"].max())\n", "\n", "print(test_data_blinkit[\"trip_start_timestamp\"].min())\n", "print(test_data_blinkit[\"trip_start_timestamp\"].max())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(training_data_bistro[\"trip_start_timestamp\"].min())\n", "print(training_data_bistro[\"trip_start_timestamp\"].max())\n", "\n", "print(validation_data_bistro[\"trip_start_timestamp\"].min())\n", "print(validation_data_bistro[\"trip_start_timestamp\"].max())\n", "\n", "print(test_data_bistro[\"trip_start_timestamp\"].min())\n", "print(test_data_bistro[\"trip_start_timestamp\"].max())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["parquet_local_path_training = \"training_data_travel_model_blinkit.parquet\"\n", "training_data_after_sampling_blinkit.to_parquet(\n", "    parquet_local_path_training, engine=\"pyarrow\", index=False\n", ")\n", "\n", "pb.to_s3(\n", "    parquet_local_path_training,\n", "    s3_bucket,\n", "    \"A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Blinkit/travel_model/training_data/training_data_travel_model_blinkit.parquet\",\n", ")\n", "\n", "\n", "parquet_local_path_validation = \"validation_data_travel_model_blinkit.parquet\"\n", "validation_data_after_sampling_blinkit.to_parquet(\n", "    parquet_local_path_validation, engine=\"pyarrow\", index=False\n", ")\n", "\n", "pb.to_s3(\n", "    parquet_local_path_validation,\n", "    s3_bucket,\n", "    \"<PERSON><PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Blinkit/travel_model/validation_data/validation_data_travel_model_blinkit.parquet\",\n", ")\n", "\n", "\n", "parquet_local_path_test = \"test_data_travel_model_blinkit.parquet\"\n", "test_data_blinkit.to_parquet(parquet_local_path_test, engine=\"pyarrow\", index=False)\n", "\n", "pb.to_s3(\n", "    parquet_local_path_test,\n", "    s3_bucket,\n", "    \"A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Blinkit/travel_model/test_data/test_data_travel_model_blinkit.parquet\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["parquet_local_path_training = \"training_data_travel_model_bistro.parquet\"\n", "training_data_bistro.to_parquet(parquet_local_path_training, engine=\"pyarrow\", index=False)\n", "\n", "pb.to_s3(\n", "    parquet_local_path_training,\n", "    s3_bucket,\n", "    \"A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Bistro/travel_model/training_data/training_data_travel_model_bistro.parquet\",\n", ")\n", "\n", "\n", "parquet_local_path_validation = \"validation_data_travel_model_bistro.parquet\"\n", "validation_data_bistro.to_parquet(parquet_local_path_validation, engine=\"pyarrow\", index=False)\n", "\n", "pb.to_s3(\n", "    parquet_local_path_validation,\n", "    s3_bucket,\n", "    \"<PERSON><PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Bistro/travel_model/validation_data/validation_data_travel_model_bistro.parquet\",\n", ")\n", "\n", "\n", "parquet_local_path_test = \"test_data_travel_model_bistro.parquet\"\n", "test_data_bistro.to_parquet(parquet_local_path_test, engine=\"pyarrow\", index=False)\n", "\n", "pb.to_s3(\n", "    parquet_local_path_test,\n", "    s3_bucket,\n", "    \"A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Bistro/travel_model/test_data/test_data_travel_model_bistro.parquet\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}