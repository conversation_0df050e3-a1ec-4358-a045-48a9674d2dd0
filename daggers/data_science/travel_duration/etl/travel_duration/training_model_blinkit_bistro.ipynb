{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Installing Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install -q catboost\n", "!pip install -q joblib\n", "!pip install -q pytz\n", "!pip install h3==3.7.6\n", "!optuna==3.6.1\n", "!pip install haversine"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install -q kafka-python==2.0.2\n", "!pip install -q scipy==1.8.0\n", "!pip install aiohttp==3.8.1\n", "!pip install fsspec==2023.1.0\n", "!pip install aiobotocore==2.4.2\n", "!pip install pymysql==1.0.2\n", "!pip install gremlinpython==3.6.2\n", "!pip install botocore==1.27.59\n", "!pip install progressbar2==4.2.0\n", "!pip install backoff==2.2.1\n", "!pip install pandas==1.5.1\n", "!pip install pg8000==1.29.4\n", "!pip install opensearch-py==2.1.1\n", "!pip install boto3==1.24.59\n", "!pip install requests-aws4auth==1.2.2\n", "!pip install s3transfer==0.6.0\n", "!pip install aenum==3.1.11\n", "!pip install scramp==1.4.4\n", "!pip install python-utils==3.5.2\n", "!pip install awswrangler==2.19.0\n", "!pip install s3fs==2023.1.0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install scikit-learn"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from concurrent.futures import ThreadPoolExecutor\n", "from sklearn.metrics import mean_absolute_error\n", "from datetime import date, datetime, timedelta\n", "from catboost import CatBoostRegressor\n", "from collections import OrderedDict\n", "from multiprocessing import Pool\n", "import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "from h3 import h3\n", "import warnings\n", "import requests\n", "import logging\n", "import pickle\n", "import joblib\n", "import random\n", "import shutil\n", "import glob\n", "import math\n", "import time\n", "import pytz\n", "import json\n", "import os\n", "import gc\n", "\n", "import awswrangler as wr\n", "\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "# tqdm.pandas()\n", "pd.set_option(\"display.max_rows\", 100)\n", "pd.set_option(\"display.max_columns\", 100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bistro_org_id = \"4\"\n", "blinkit_org_id = \"1\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["s3_bucket = \"prod-dse-projects\"\n", "training_data_local_path = \"training_data_travel_model_bistro.parquet\"\n", "pb.from_s3(\n", "    s3_bucket,\n", "    \"A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Bistro/travel_model/training_data/training_data_travel_model_bistro.parquet\",\n", "    training_data_local_path,\n", ")\n", "\n", "training_data_bistro = pd.read_parquet(training_data_local_path, engine=\"pyarrow\")\n", "\n", "\n", "s3_bucket = \"prod-dse-projects\"\n", "validation_data_local_path = \"validation_data_travel_model_bistro.parquet\"\n", "pb.from_s3(\n", "    s3_bucket,\n", "    \"<PERSON><PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Bistro/travel_model/validation_data/validation_data_travel_model_bistro.parquet\",\n", "    validation_data_local_path,\n", ")\n", "\n", "validation_data_bistro = pd.read_parquet(validation_data_local_path, engine=\"pyarrow\")\n", "\n", "\n", "s3_bucket = \"prod-dse-projects\"\n", "test_data_local_path = \"test_data_travel_model_bistro.parquet\"\n", "pb.from_s3(\n", "    s3_bucket,\n", "    \"A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Bistro/travel_model/test_data/test_data_travel_model_bistro.parquet\",\n", "    test_data_local_path,\n", ")\n", "\n", "test_data_bistro = pd.read_parquet(test_data_local_path, engine=\"pyarrow\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_data_bistro[\"org_id\"] = bistro_org_id\n", "validation_data_bistro[\"org_id\"] = bistro_org_id\n", "test_data_bistro[\"org_id\"] = bistro_org_id"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_data_bistro.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(training_data_bistro.shape)\n", "training_data_bistro.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(training_data_bistro[\"trip_start_timestamp\"].min())\n", "print(training_data_bistro[\"trip_start_timestamp\"].max())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# training_data_bistro = training_data_bistro[\n", "#     (training_data_bistro[\"delivery_time\"] <= 30)\n", "#     & (training_data_bistro[\"delivery_time\"] >= 2)\n", "#     & (training_data_bistro[\"speed_km_h\"] <= 70)\n", "#     & (\n", "#         training_data_bistro[\"speed_km_h\"]\n", "#         >= np.percentile(training_data_bistro.speed_km_h.dropna(), 2)\n", "#     )\n", "# ]\n", "# validation_data_bistro = validation_data_bistro[\n", "#     (validation_data_bistro[\"delivery_time\"] <= 30)\n", "#     & (validation_data_bistro[\"delivery_time\"] >= 2)\n", "#     & (validation_data_bistro[\"speed_km_h\"] <= 70)\n", "#     & (\n", "#         validation_data_bistro[\"speed_km_h\"]\n", "#         >= np.percentile(validation_data_bistro.speed_km_h.dropna(), 2)\n", "#     )\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(training_data_bistro[\"trip_start_timestamp\"].min())\n", "print(training_data_bistro[\"trip_start_timestamp\"].max())\n", "print()\n", "print(validation_data_bistro[\"trip_start_timestamp\"].min())\n", "print(validation_data_bistro[\"trip_start_timestamp\"].max())\n", "print()\n", "print(test_data_bistro[\"trip_start_timestamp\"].min())\n", "print(test_data_bistro[\"trip_start_timestamp\"].max())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(training_data_bistro.shape)\n", "print(validation_data_bistro.shape)\n", "print(test_data_bistro.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Adding Blinkit data "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["s3_bucket = \"prod-dse-projects\"\n", "training_data_local_path = \"training_data_travel_model_blinkit.parquet\"\n", "pb.from_s3(\n", "    s3_bucket,\n", "    \"A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Blinkit/travel_model/training_data/training_data_travel_model_blinkit.parquet\",\n", "    training_data_local_path,\n", ")\n", "\n", "training_data_blinkit = pd.read_parquet(training_data_local_path, engine=\"pyarrow\")\n", "\n", "\n", "s3_bucket = \"prod-dse-projects\"\n", "validation_data_local_path = \"validation_data_travel_model_blinkit.parquet\"\n", "pb.from_s3(\n", "    s3_bucket,\n", "    \"<PERSON><PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Blinkit/travel_model/validation_data/validation_data_travel_model_blinkit.parquet\",\n", "    validation_data_local_path,\n", ")\n", "\n", "validation_data_blinkit = pd.read_parquet(validation_data_local_path, engine=\"pyarrow\")\n", "\n", "\n", "s3_bucket = \"prod-dse-projects\"\n", "test_data_local_path = \"test_data_travel_model_blinkit.parquet\"\n", "pb.from_s3(\n", "    s3_bucket,\n", "    \"A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Blinkit/travel_model/test_data/test_data_travel_model_blinkit.parquet\",\n", "    test_data_local_path,\n", ")\n", "\n", "test_data_blinkit = pd.read_parquet(test_data_local_path, engine=\"pyarrow\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_data_blinkit[\"org_id\"] = blinkit_org_id\n", "validation_data_blinkit[\"org_id\"] = blinkit_org_id\n", "test_data_blinkit[\"org_id\"] = blinkit_org_id"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(training_data_blinkit.shape)\n", "print(validation_data_blinkit.shape)\n", "print(test_data_blinkit.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(training_data_blinkit[\"trip_start_timestamp\"].min())\n", "print(training_data_blinkit[\"trip_start_timestamp\"].max())\n", "print()\n", "print(validation_data_blinkit[\"trip_start_timestamp\"].min())\n", "print(validation_data_blinkit[\"trip_start_timestamp\"].max())\n", "print()\n", "print(test_data_blinkit[\"trip_start_timestamp\"].min())\n", "print(test_data_blinkit[\"trip_start_timestamp\"].max())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_data = pd.concat([training_data_bistro, training_data_blinkit], ignore_index=True)\n", "validation_data = pd.concat([validation_data_bistro, validation_data_blinkit], ignore_index=True)\n", "test_data = pd.concat([test_data_bistro, test_data_blinkit], ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(training_data.shape)\n", "print(validation_data.shape)\n", "print(test_data.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_data.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["validation_data.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["categorical_feature = [\n", "    \"part_of_day\",\n", "    \"day_of_week\",\n", "    \"is_rain_order\",\n", "    \"is_nearby_store_order\",\n", "    \"city_name\",\n", "    \"polygon_type\",\n", "    \"customer_hex9\",\n", "    \"customer_hex10\",\n", "    \"in_poi\",\n", "    \"merchant_hex9\",\n", "    \"merchant_hex10\",\n", "    \"org_id\",\n", "]\n", "continous_feature = [\n", "    \"customer_merchant_dist\",\n", "    \"latlong_distance_km\",\n", "    \"distinct_items_ordered\",\n", "    \"total_items_quantity_ordered\",\n", "]\n", "features = continous_feature + categorical_feature\n", "prediction_feature = \"delivery_time\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(training_data.shape)\n", "print(validation_data.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_data[\"org_id\"].value_counts(normalize=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["validation_data[\"org_id\"].value_counts(normalize=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_data[\"org_id\"].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["validation_data[\"org_id\"].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for col in categorical_feature:\n", "    training_data[col] = training_data[col].astype(\"category\")\n", "    validation_data[col] = validation_data[col].astype(\"category\")\n", "    test_data[col] = test_data[col].astype(\"category\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_params = {\n", "    \"n_estimators\": 1555,\n", "    \"learning_rate\": 0.0710775870446307,\n", "    \"l2_leaf_reg\": 1,\n", "    \"depth\": 4,\n", "    \"loss_function\": \"MAE\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def train_catboost(\n", "    tr_data,\n", "    val_data,\n", "    categorical_features,\n", "    continous_features,\n", "    params,\n", "    dependent_variable=\"delivery_time\",\n", "):\n", "\n", "    features = continous_features + categorical_features\n", "\n", "    model = CatBoostRegressor(\n", "        approx_on_full_history=False,\n", "        allow_writing_files=True,\n", "        train_dir=\"/tmp/catboost_logs\",\n", "        **params,\n", "        cat_features=categorical_features,\n", "        custom_metric=[\"MAE\", \"R2\", \"RMSE\"],\n", "        save_snapshot=True,\n", "        verbose=200,\n", "    )\n", "\n", "    model.fit(\n", "        tr_data[features],\n", "        tr_data[dependent_variable],\n", "        eval_set=(\n", "            val_data[features],\n", "            val_data[dependent_variable],\n", "        ),\n", "        use_best_model=True,\n", "    )\n", "\n", "    return model"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["model = train_catboost(\n", "    training_data,\n", "    validation_data,\n", "    categorical_feature,\n", "    continous_feature,\n", "    model_params,\n", "    dependent_variable=prediction_feature,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_feature_importance(model, features):\n", "    importance = model.get_feature_importance()\n", "    importance_df = pd.DataFrame({\"Feature\": features, \"Importance\": importance}).sort_values(\n", "        by=\"Importance\", ascending=False\n", "    )\n", "    return importance_df\n", "\n", "\n", "feature_importance_df = get_feature_importance(model, features)\n", "feature_importance_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_prediction = model.predict(training_data[features])\n", "validation_prediction = model.predict(validation_data[features])\n", "test_prediction = model.predict(test_data[features])\n", "\n", "training_data[\"predicted_delivery_time\"] = train_prediction\n", "validation_data[\"predicted_delivery_time\"] = validation_prediction\n", "test_data[\"predicted_delivery_time\"] = test_prediction"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_train_mae = mean_absolute_error(training_data.delivery_time, train_prediction)\n", "model_validation_mae = mean_absolute_error(validation_data.delivery_time, validation_prediction)\n", "model_test_mae = mean_absolute_error(test_data.delivery_time, test_prediction)\n", "\n", "model_train_me = np.mean(training_data.delivery_time - train_prediction)\n", "model_validation_me = np.mean(validation_data.delivery_time - validation_prediction)\n", "model_test_me = np.mean(test_data.delivery_time - test_prediction)\n", "\n", "print(\n", "    \"Model MAE:\",\n", "    model_train_mae,\n", "    model_validation_mae,\n", "    model_test_mae,\n", ")\n", "\n", "print(\n", "    \"Model ME:\",\n", "    model_train_me,\n", "    model_validation_me,\n", "    model_test_me,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    \"bl-personalization-notifications\",\n", "    f\"Delivery Time Model:\\n\\tTrain MAE: {model_train_mae}\\n\\tValidation MAE: {model_validation_mae}\\n\\tTest MAE: {model_test_mae}\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# WITH BUSINESS METRIC\n", "import numpy as np\n", "from sklearn.metrics import mean_absolute_error, mean_squared_error\n", "\n", "\n", "def calculate_business_metrics(y_true, y_pred, threshold=2):\n", "    \"\"\"Calculate business compliance and accuracy metrics\"\"\"\n", "    # Business compliance +2 (actual must be at most 2 mins more than predicted)\n", "    compliance_mask = (y_true - y_pred) <= threshold\n", "\n", "    business_compliance = np.mean(compliance_mask) * 100\n", "\n", "    # Business accuracy ±2 (actual must at most have 2 mins deviation from predicted)\n", "    accuracy_mask = np.abs(y_true - y_pred) <= threshold\n", "    business_accuracy = np.mean(accuracy_mask) * 100\n", "\n", "    return business_compliance, business_accuracy"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calculate_business_metrics(validation_data.delivery_time, validation_prediction)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calculate_business_metrics(test_data.delivery_time, test_prediction)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Pushing the Model Metadata"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from catboost import CatBoostRegressor\n", "\n", "# model.save_model(\"model.cbm\")\n", "model.save_model(\"model.cbm\", format=\"cbm\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_version = 2.1\n", "model_subid = datetime.now().strftime(\"%Y%m%d%H%M\")\n", "model_name = \"se_travel_time_estimation\"\n", "model_id = \"{}_v{}_{}\".format(model_name, model_version, model_subid)\n", "model_type = \"catboost_regressor\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(model_subid)\n", "print(model_name)\n", "print(model_id)\n", "print(model_type)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["AUTHOR = \"<EMAIL>\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_logs = {\n", "    \"training_data_start_date\": training_data.trip_start_timestamp.min(),\n", "    \"training_data_end_date\": training_data.trip_start_timestamp.max(),\n", "    \"validation_data_start_date\": validation_data.trip_start_timestamp.min(),\n", "    \"validation_data_end_date\": validation_data.trip_start_timestamp.max(),\n", "    \"test_data_start_date\": test_data.trip_start_timestamp.min(),\n", "    \"test_data_end_date\": test_data.trip_start_timestamp.max(),\n", "    \"model_id\": model_id,\n", "    \"model_name\": model_name,\n", "    \"model_version\": model_version,\n", "    \"model_subid\": model_subid,\n", "    \"model_type\": model_type,\n", "    \"speed_lower_bound\": round(\n", "        np.percentile(\n", "            training_data.customer_merchant_dist / (training_data.delivery_time / 60),\n", "            5,\n", "        ),\n", "        2,\n", "    ),\n", "    \"speed_upper_bound\": round(\n", "        np.percentile(\n", "            training_data.customer_merchant_dist / (training_data.delivery_time / 60),\n", "            98,\n", "        ),\n", "        2,\n", "    ),\n", "    \"model_train_mae\": round(model_train_mae, 2),\n", "    \"model_validation_mae\": round(model_validation_mae, 2),\n", "    \"model_test_mae\": round(model_test_mae, 2),\n", "    \"model_train_me\": round(model_train_me, 2),\n", "    \"model_validation_me\": round(model_validation_me, 2),\n", "    \"model_test_me\": round(model_test_me, 2),\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["BUCKET_NAME = \"prod-dse-projects\"\n", "MODEL_CLOUDPATH = \"/eta_prediction/travel_time_estimation/models/{}/{}/model.cbm\".format(\n", "    f\"v{model_version}\", model_subid\n", ")\n", "MODEL_METADATA_CLOUDPATH = (\n", "    \"/eta_prediction/travel_time_estimation/models/{}/{}/metadata.json\".format(\n", "        f\"v{model_version}\", model_subid\n", "    )\n", ")\n", "MODEL_POINTER_CLOUDPATH = \"/eta_prediction/travel_time_estimation/models/current.json\"\n", "\n", "MODEL_LOCALPATH = \"model.cbm\"\n", "MODEL_POINTER_LOCALPATH = \"current.json\"\n", "MODEL_METADATA_LOCALPATH = \"metadata.json\"\n", "\n", "\n", "AUTHOR = \"<EMAIL>\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MODEL_METADATA_CLOUDPATH"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"model cloud path:\", MODEL_CLOUDPATH)\n", "print(\"model metadata path:\", MODEL_METADATA_CLOUDPATH)\n", "print(\"model pointer:\", MODEL_POINTER_CLOUDPATH)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_pointer = {\n", "    \"created_at\": datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "    \"created_by\": AUTH<PERSON>,\n", "    \"bucket_name\": BUCKET_NAME,\n", "    \"model_path\": MODEL_CLOUDPATH,\n", "    \"metadata_path\": MODEL_METADATA_CLOUDPATH,\n", "    \"model_id\": model_id,\n", "    \"model_name\": model_name,\n", "    \"model_version\": model_version,\n", "    \"model_type\": model_type,\n", "}\n", "\n", "model_metadata = model_pointer.copy()\n", "model_metadata[\"darkstore_ids\"] = training_data.darkstore_id.unique().tolist()\n", "model_metadata[\"model_params\"] = model_params\n", "model_metadata[\"model_test_mae\"] = model_logs[\"model_test_mae\"]\n", "\n", "\n", "model_logs[\"s3_bucket\"] = BUCKET_NAME\n", "model_logs[\"s3_path\"] = MODEL_CLOUDPATH\n", "model_logs_df = pd.DataFrame([OrderedDict(model_logs)])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_logs_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # dump model locally\n", "# joblib.dump(model, MODEL_LOCALPATH, compress=1)\n", "\n", "# model metadata\n", "with open(MODEL_METADATA_LOCALPATH, \"w\") as fp:\n", "    json.dump(model_metadata, fp)\n", "\n", "# model pointer\n", "with open(MODEL_POINTER_LOCALPATH, \"w\") as fp:\n", "    json.dump(model_pointer, fp)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# push model to s3\n", "pb.to_s3(MODEL_LOCALPATH, BUCKET_NAME, MODEL_CLOUDPATH)\n", "\n", "# push metadata to s3 (darkstore ids trained on)\n", "pb.to_s3(MODEL_METADATA_LOCALPATH, BUCKET_NAME, MODEL_METADATA_CLOUDPATH)\n", "\n", "# push model pointer to s3 (current model filepath)\n", "pb.to_s3(MODEL_POINTER_LOCALPATH, BUCKET_NAME, MODEL_POINTER_CLOUDPATH)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def push_to_db(logs):\n", "\n", "    frame = logs.copy()\n", "    frame[\"updated_at\"] = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    frame = frame.astype({\"updated_at\": \"datetime64[ns]\"})\n", "    frame[\"status\"] = \"trained\"\n", "\n", "    column_dtypes = [\n", "        {\n", "            \"name\": \"training_data_start_date\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"training data start date\",\n", "        },\n", "        {\n", "            \"name\": \"training_data_end_date\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"training data end date\",\n", "        },\n", "        {\n", "            \"name\": \"validation_data_start_date\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"Validation data start date\",\n", "        },\n", "        {\n", "            \"name\": \"validation_data_end_date\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"validation data end date\",\n", "        },\n", "        {\n", "            \"name\": \"test_data_start_date\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"test data start date\",\n", "        },\n", "        {\n", "            \"name\": \"test_data_end_date\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"test data end date\",\n", "        },\n", "        {\n", "            \"name\": \"model_id\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"id of the generated model\",\n", "        },\n", "        {\n", "            \"name\": \"model_name\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"name of the model used for predicting the eta component\",\n", "        },\n", "        {\n", "            \"name\": \"model_version\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"version of the model\",\n", "        },\n", "        {\n", "            \"name\": \"model_subid\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"model subid is the date on which the model was generated\",\n", "        },\n", "        {\n", "            \"name\": \"model_type\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"type of the ml model\",\n", "        },\n", "        {\n", "            \"name\": \"speed_lower_bound\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"lower bound of speed below which eta fallbacks\",\n", "        },\n", "        {\n", "            \"name\": \"speed_upper_bound\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"upper bound of speed above which eta fallbacks\",\n", "        },\n", "        {\n", "            \"name\": \"model_train_mae\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"mean absolute error of model on training data\",\n", "        },\n", "        {\n", "            \"name\": \"model_validation_mae\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"mean absolute error of model on validation data\",\n", "        },\n", "        {\n", "            \"name\": \"model_test_mae\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"mean absolute error of model on test data\",\n", "        },\n", "        {\n", "            \"name\": \"model_train_me\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"mean error of model on training data\",\n", "        },\n", "        {\n", "            \"name\": \"model_validation_me\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"mean error of model on validation data\",\n", "        },\n", "        {\n", "            \"name\": \"model_test_me\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"mean error of model on test data\",\n", "        },\n", "        {\n", "            \"name\": \"s3_bucket\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"bucket name where artifacts are stores\",\n", "        },\n", "        {\n", "            \"name\": \"s3_path\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"path inside the bucket where the model is stored\",\n", "        },\n", "        {\n", "            \"name\": \"updated_at\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"table updation timestamp\",\n", "        },\n", "        {\n", "            \"name\": \"status\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"status of the model for deployement\",\n", "        },\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"consumer_etls\",\n", "        \"table_name\": \"serviceability_travel_time_model_retraining_logs\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"model_id\"],\n", "        \"sortkey\": [\"model_id\", \"updated_at\"],\n", "        \"incremental_key\": \"updated_at\",\n", "        \"load_type\": \"upsert\",\n", "        \"table_description\": \"Travel time estimation retraining pipeline logs\",\n", "    }\n", "\n", "    pb.to_trino(frame, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if len(model_logs_df) != 0:\n", "    push_to_db(model_logs_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    \"bl-personalization-notifications\",\n", "    f\"Delivery Time Model DAG run completed. Model meta data pushed into Trino\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}