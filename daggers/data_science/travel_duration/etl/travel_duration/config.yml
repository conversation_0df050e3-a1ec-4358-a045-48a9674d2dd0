alert_configs:
  slack:
  - channel: bl-personalization-notifications
  - channel: bl-personalisation-dag-failures
  - channel: bl-data-airflow-alerts
dag_name: travel_duration
dag_type: etl
escalation_priority: high
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_science
nobuild: false
notebooks:
- executor_config:
    load_type: very-high-mem
    node_type: spot
  name: data_prep_blinkit_bistro
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level1
- executor_config:
    load_type: ultra-high-mem
    node_type: spot
  name: training_data_feature_creation_blinkit_bistro
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level2
- executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: training_model_blinkit_bistro
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level3
owner:
  email: <EMAIL>
  slack_id: U06V2LWHR60
path: data_science/travel_duration/etl/travel_duration
paused: false
pool: data_science_pool
project_name: travel_duration
schedule:
  end_date: '2025-07-15T00:00:00'
  interval: 0 22 19 * *
  start_date: '2025-05-24T00:00:00'
schedule_type: fixed
sla: 271  minutes
support_files: []
tags: []
template_name: multi_notebook
version: 1
concurrency: 3
