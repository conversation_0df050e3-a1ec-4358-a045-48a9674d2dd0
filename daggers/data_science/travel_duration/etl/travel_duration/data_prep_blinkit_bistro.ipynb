{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install -q catboost\n", "!pip install -q joblib\n", "!pip install -q pytz\n", "!pip install h3==3.7.6\n", "!optuna==3.6.1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install -q kafka-python==2.0.2\n", "!pip install -q scipy==1.8.0\n", "!pip install aiohttp==3.8.1\n", "!pip install fsspec==2023.1.0\n", "!pip install aiobotocore==2.4.2\n", "!pip install pymysql==1.0.2\n", "!pip install gremlinpython==3.6.2\n", "!pip install botocore==1.27.59\n", "!pip install progressbar2==4.2.0\n", "!pip install backoff==2.2.1\n", "!pip install pandas==1.5.1\n", "!pip install pg8000==1.29.4\n", "!pip install opensearch-py==2.1.1\n", "!pip install boto3==1.24.59\n", "!pip install requests-aws4auth==1.2.2\n", "!pip install s3transfer==0.6.0\n", "!pip install aenum==3.1.11\n", "!pip install scramp==1.4.4\n", "!pip install python-utils==3.5.2\n", "!pip install awswrangler==2.19.0\n", "!pip install s3fs==2023.1.0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install scikit-learn"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from concurrent.futures import ThreadPoolExecutor\n", "from sklearn.metrics import mean_absolute_error\n", "from datetime import date, datetime, timedelta\n", "from catboost import CatBoostRegressor\n", "from collections import OrderedDict\n", "from multiprocessing import Pool\n", "import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "from h3 import h3\n", "import warnings\n", "import requests\n", "import logging\n", "import pickle\n", "import joblib\n", "import random\n", "import shutil\n", "import glob\n", "import math\n", "import time\n", "import pytz\n", "import json\n", "import os\n", "import gc\n", "\n", "\n", "import awswrangler as wr\n", "\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["s3_bucket = \"prod-dse-projects\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Pulling Pipeline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Blinkit Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["current_date = datetime.now(pytz.timezone(\"Asia/Kolkata\"))\n", "\n", "start_date_blinkit = current_date - <PERSON><PERSON><PERSON>(days=46)\n", "validation_date_blinkit = current_date - timed<PERSON>ta(days=15)\n", "test_date_blinkit = current_date - timed<PERSON><PERSON>(days=8)\n", "end_date_blinkit = current_date - <PERSON><PERSON><PERSON>(days=1)\n", "\n", "START_DATE_BLINKIT, VALIDATION_DATE_BLINKIT, TEST_DATE_BLINKIT, END_DATE_BLINKIT = (\n", "    str(start_date_blinkit.date()),\n", "    str(validation_date_blinkit.date()),\n", "    str(test_date_blinkit.date()),\n", "    str(end_date_blinkit.date()),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["START_DATE_BLINKIT, VALIDATION_DATE_BLINKIT, TEST_DATE_BLINKIT, END_DATE_BLINKIT"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output_dir_blinkit = f\"dataset_blinkit_{START_DATE_BLINKIT}_to_{END_DATE_BLINKIT}\"\n", "output_dir_blinkit"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["step = 3\n", "\n", "\n", "def get_date_checkpoints(start_date_, end_date_, step_):\n", "    iter_date = start_date_\n", "    date_list_ = []\n", "    while iter_date < end_date_:\n", "        date_list_.append(iter_date)\n", "        iter_date += <PERSON><PERSON>ta(days=step_)\n", "    date_list_.append(end_date_)\n", "    return date_list_\n", "\n", "\n", "date_list_blinkit = get_date_checkpoints(start_date_blinkit, end_date_blinkit, step)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(len(date_list_blinkit))\n", "print(date_list_blinkit)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "from datetime import timedelta\n", "import logging\n", "\n", "logger = logging.getLogger(__name__)\n", "\n", "\n", "def get_data_in_chunks(\n", "    query,\n", "    start_date,\n", "    end_date,\n", "    li_date_list,\n", "    org_name,\n", "    output_dir,\n", "    connection=\"[Warehouse] Trino\",\n", "    variable_to_format={},\n", "):\n", "    if variable_to_format is None:\n", "        variable_to_format = {}\n", "\n", "    # Initialize results\n", "    final_result = []\n", "\n", "    # Iterate through date chunks\n", "    for i in range(len(li_date_list) - 1):\n", "        sd = li_date_list[i].strftime(\"%Y-%m-%d\")\n", "        ed = li_date_list[i + 1].strftime(\"%Y-%m-%d\")\n", "\n", "        if i + 2 >= len(li_date_list):\n", "            ed_new = (li_date_list[i + 1] + <PERSON><PERSON><PERSON>(days=1)).strftime(\"%Y-%m-%d\")\n", "        else:\n", "            ed_new = li_date_list[i + 1].strftime(\"%Y-%m-%d\")\n", "        print(f\"Processing start_date: {sd}, end_date: {ed}\")\n", "\n", "        print(f\"Fetching data from {sd} to {ed}\")\n", "        variable_to_format[\"start_date\"] = sd\n", "        variable_to_format[\"end_date\"] = ed_new\n", "\n", "        parquet_location = \"data_\" + str(org_name) + \"_\" + str(sd) + \"_to_\" + str(ed) + \".parquet\"\n", "\n", "        s3_paths_date_list = (\n", "            \"s3://prod-dse-projects/A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/\"\n", "            + str(org_name)\n", "            + \"/travel_model/data_prep/\"\n", "            + str(output_dir)\n", "            + \"/\"\n", "        )\n", "        s3_paths_obj_date_list = wr.s3.list_objects(s3_paths_date_list)\n", "        exists_date_list = any(parquet_location in path for path in s3_paths_obj_date_list)\n", "\n", "        if exists_date_list:\n", "            print(f\"Skipping existing file: {parquet_location}\")\n", "\n", "        else:\n", "\n", "            # Execute query for the current date range\n", "            chunk_data = pd.read_sql(\n", "                query.format(**variable_to_format), con=pb.get_connection(connection)\n", "            )\n", "\n", "            # Clean data before saving\n", "            chunk_data[\"speed_km_h\"] = pd.to_numeric(chunk_data[\"speed_km_h\"], errors=\"coerce\")\n", "\n", "            print(chunk_data[\"order_id\"].value_counts())\n", "\n", "            # Log invalid data handling\n", "            if chunk_data[\"speed_km_h\"].isna().any():\n", "                print(f\"Processing, Invalid 'speed_km_h' values found, replaced with NaN.\")\n", "\n", "            parquet_location = (\n", "                \"data_\" + str(org_name) + \"_\" + str(sd) + \"_to_\" + str(ed) + \".parquet\"\n", "            )\n", "            chunk_data.to_parquet(parquet_location, engine=\"pyarrow\", index=False)\n", "\n", "            pb.to_s3(\n", "                parquet_location,\n", "                s3_bucket,\n", "                \"A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/\"\n", "                + str(org_name)\n", "                + \"/travel_model/data_prep/\"\n", "                + str(output_dir)\n", "                + \"/\"\n", "                + \"data_\"\n", "                + str(org_name)\n", "                + \"_\"\n", "                + str(sd)\n", "                + \"_to_\"\n", "                + str(ed)\n", "                + \".parquet\",\n", "            )\n", "            print(f\" Processing complete for, {parquet_location}\")\n", "\n", "    return 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["trip_details_query_blinkit = \"\"\"\n", "    with all_order_base as (\n", "    Select \n", "        trip_id, \n", "        order_id, \n", "        frontend_merchant_id as darkstore_id,\n", "        order_checkout_ts_ist as  trip_start_timestamp,\n", "        CAST(date_diff('second', order_enroute_ts_ist, order_reached_doorstep_ts_ist) AS DOUBLE) / 60.0 AS delivery_time,\n", "        CAST(est_store_to_customer_location_kms/(CAST(date_diff('second', order_enroute_ts_ist, order_reached_doorstep_ts_ist) AS DOUBLE) / 60.0/60.0) AS DOUBLE) as speed_km_h,\n", "        est_store_to_customer_location_kms as customer_merchant_dist,\n", "        customer_location_latitude,\n", "        customer_location_longitude,\n", "        is_rain_order,\n", "        is_nearby_store_order,\n", "        distinct_items_ordered,\n", "        total_items_quantity_ordered\n", "      from \n", "        dwh.fact_supply_chain_order_details\n", "      WHERE \n", "        -- order_type = 'super_express' \n", "        order_current_status = 'DELIVERED' \n", "        AND is_order_cancelled = FALSE \n", "        AND is_order_fully_filled = TRUE \n", "        AND is_rescheduled = FALSE\n", "        AND CAST(order_checkout_ts_ist AS date) >= date '{start_date}'\n", "        AND CAST(order_checkout_ts_ist AS date) < date '{end_date}'\n", "        AND order_checkout_dt_ist >= date '{start_date}'\n", "    ), \n", "\n", "\n", "    \n", "    avm AS(\n", "    SELECT \n", "        order_id,\n", "        in_poi\n", "    from\n", "        (\n", "        SELECT distinct\n", "            order_id,\n", "            COALESCE(in_poi, FALSE) as in_poi,\n", "            rank() over(partition by order_id order by order_delivered_ts_ist desc) as rn \n", "        FROM \n", "            ds_etls.address_verification_master_v1\n", "        WHERE\n", "            force_completed = 0 and\n", "            order_delivered_ts_ist is not null\n", "            and order_id in (select distinct order_id from all_order_base)\n", "        )\n", "    where \n", "        rn = 1)\n", "    \n", "\n", "    SELECT distinct\n", "        aob.*, \n", "        merch.city_name,\n", "        merch.merchant_store_latitude,\n", "        merch.merchant_store_longitude,\n", "        fsod.polygon_type,\n", "        fsod.outlet_id,\n", "        fsod.is_surge_order,\n", "        avm.in_poi\n", "        FROM \n", "        all_order_base aob\n", "        LEFT JOIN dwh.dim_merchant merch \n", "            ON aob.darkstore_id = merch.merchant_id AND merch.is_current = TRUE\n", "        LEFT JOIN dwh.fact_sales_order_details fsod \n", "            ON fsod.order_id = aob.order_id  \n", "            AND fsod.order_create_dt_ist BETWEEN DATE '{start_date}' AND DATE '{end_date}'\n", "        LEFT JOIN avm \n", "            ON avm.order_id = fsod.order_id\n", "\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["trip_details_blinkit = get_data_in_chunks(\n", "    trip_details_query_blinkit,\n", "    start_date_blinkit,\n", "    end_date_blinkit,\n", "    date_list_blinkit,\n", "    org_name=\"Blinkit\",\n", "    output_dir=output_dir_blinkit,\n", "    connection=\"[Warehouse] Trino\",\n", "    variable_to_format={},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### Bistro Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_date = datetime.now(pytz.timezone(\"Asia/Kolkata\"))\n", "\n", "ts = pd.Timestamp(\"2024-10-15 00:00:00\")\n", "\n", "# Convert to timezone-aware datetime in Asia/Kolkata\n", "dt = ts.tz_localize(\"Asia/Kolkata\")\n", "\n", "# Convert to Python datetime object (still timezone-aware)\n", "start_date_bistro = dt.to_pydatetime()\n", "\n", "start_date_bistro = pd.to_datetime(start_date_bistro)\n", "validation_date_bistro = current_date - <PERSON><PERSON><PERSON>(days=15)\n", "test_date_bistro = current_date - <PERSON><PERSON><PERSON>(days=8)\n", "end_date_bistro = current_date - <PERSON><PERSON><PERSON>(days=1)\n", "\n", "START_DATE_BISTRO, VALIDATION_DATE_BISTRO, TEST_DATE_BISTRO, END_DATE_BISTRO = (\n", "    str(start_date_bistro.date()),\n", "    str(validation_date_bistro.date()),\n", "    str(test_date_bistro.date()),\n", "    str(end_date_bistro.date()),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["START_DATE_BISTRO, VALIDATION_DATE_BISTRO, TEST_DATE_BISTRO, END_DATE_BISTRO"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output_dir_bistro = f\"dataset_bistro_{START_DATE_BISTRO}_to_{END_DATE_BISTRO}\"\n", "output_dir_bistro"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["step = 20\n", "\n", "\n", "def get_date_checkpoints(start_date_, end_date_, step_):\n", "    iter_date = start_date_\n", "    date_list_ = []\n", "    while iter_date < end_date_:\n", "        date_list_.append(iter_date)\n", "        iter_date += <PERSON><PERSON>ta(days=step_)\n", "    date_list_.append(end_date_)\n", "    return date_list_\n", "\n", "\n", "date_list_bistro = get_date_checkpoints(start_date_bistro, end_date_bistro, step)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(len(date_list_bistro))\n", "print(date_list_bistro)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trip_details_query_bistro = \"\"\"\n", "    with all_order_base as (\n", "    Select \n", "        trip_id, \n", "        order_id, \n", "        frontend_merchant_id as darkstore_id,\n", "        order_checkout_ts_ist as  trip_start_timestamp,\n", "        CAST(date_diff('second', order_enroute_ts_ist, order_reached_doorstep_ts_ist) AS DOUBLE) / 60.0 AS delivery_time,\n", "        CAST(est_store_to_customer_location_kms/(CAST(date_diff('second', order_enroute_ts_ist, order_reached_doorstep_ts_ist) AS DOUBLE) / 60.0/60.0) AS DOUBLE) as speed_km_h,\n", "        est_store_to_customer_location_kms as customer_merchant_dist,\n", "        customer_location_latitude,\n", "        customer_location_longitude,\n", "        -- is_rain_order,\n", "        -- is_nearby_store_order,\n", "        distinct_items_ordered,\n", "        total_items_quantity_ordered\n", "      from \n", "        bistro_etls.fact_supply_chain_order_details_bistro\n", "      WHERE \n", "        -- order_type = 'super_express' \n", "        order_current_status = 'DELIVERED' \n", "        AND is_order_fully_filled = TRUE \n", "        AND is_rescheduled = FALSE\n", "        AND CAST(order_checkout_ts_ist AS date) >= date '{start_date}'\n", "        AND CAST(order_checkout_ts_ist AS date) < date '{end_date}'\n", "        AND order_checkout_dt_ist >= date '{start_date}'\n", "    ), \n", "\n", "\n", "    \n", "    avm AS(\n", "    SELECT \n", "        order_id,\n", "        in_poi\n", "    from\n", "        (\n", "        SELECT distinct\n", "            order_id,\n", "            COALESCE(in_poi, FALSE) as in_poi,\n", "            rank() over(partition by order_id order by order_delivered_ts_ist desc) as rn \n", "        FROM \n", "            ds_etls.address_verification_master_v1\n", "        WHERE\n", "            force_completed = 0 and\n", "            order_delivered_ts_ist is not null\n", "            and order_id in (select distinct order_id from all_order_base)\n", "        )\n", "    where \n", "        rn = 1),\n", "        \n", "        \n", "    is_nearby_stores as ( \n", "    SELECT id as order_id,CAST(json_extract_scalar(slot_properties, '$.serviceability.near_by_stores.is_enabled') = 'true' AND json_extract_scalar(slot_properties, '$.serviceability.merchant_rank') = '1'AS BOOLEAN) AS is_nearby_store_order \n", "    from oms_bifrost.oms_order oms \n", "    inner join \n", "    bistro_etls.fact_supply_chain_order_details_bistro scod\n", "    on oms.id = scod.order_id\n", "    WHERE \n", "    order_current_status = 'DELIVERED' \n", "    AND is_order_fully_filled = TRUE \n", "    AND is_rescheduled = FALSE\n", "    AND CAST(order_checkout_ts_ist AS date) >= date '{start_date}'\n", "    AND CAST(order_checkout_ts_ist AS date) <= date '{end_date}'\n", "    AND order_checkout_dt_ist >= date '{start_date}'\n", "    AND date(insert_ds_ist) >=date '{start_date}'\n", "    ),\n", "    \n", "    is_rain_order as (\n", "    SELECT id as order_id,json_extract_scalar(slot_properties, '$.serviceability.serviceability_reason') = 'DISRUPTION_RAINS' AS is_rain_order \n", "    from oms_bifrost.oms_order oms \n", "    inner join \n", "    bistro_etls.fact_supply_chain_order_details_bistro scod\n", "    on oms.id = scod.order_id\n", "    WHERE \n", "    order_current_status = 'DELIVERED' \n", "    AND is_order_fully_filled = TRUE \n", "    AND is_rescheduled = FALSE\n", "    AND CAST(order_checkout_ts_ist AS date) >= date '{start_date}'\n", "    AND CAST(order_checkout_ts_ist AS date)  <= date '{end_date}'\n", "    AND order_checkout_dt_ist >= date '{start_date}'\n", "    AND date(insert_ds_ist) >=date '{start_date}'\n", "    )\n", "\n", "    \n", "\n", "    SELECT distinct\n", "        aob.*, \n", "        merch.city_name,\n", "        merch.merchant_store_latitude,\n", "        merch.merchant_store_longitude,\n", "        fsod.polygon_type,\n", "        fsod.outlet_id,\n", "        fsod.is_surge_order,\n", "        avm.in_poi,\n", "        ins.is_nearby_store_order,\n", "        iro.is_rain_order\n", "        FROM \n", "        all_order_base aob\n", "        LEFT JOIN dwh.dim_merchant merch \n", "            ON aob.darkstore_id = merch.merchant_id AND merch.is_current = TRUE\n", "        LEFT JOIN bistro_etls.fact_sales_order_details_bistro fsod \n", "            ON fsod.order_id = aob.order_id  \n", "            AND fsod.order_create_dt_ist BETWEEN DATE '{start_date}' AND DATE '{end_date}'\n", "        LEFT JOIN avm \n", "            ON avm.order_id = fsod.order_id\n", "        LEFT JOIN is_nearby_stores ins \n", "            ON aob.order_id = ins.order_id\n", "        LEFT JOIN is_rain_order iro \n", "            ON aob.order_id = iro.order_id\n", "\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trip_details_bistro = get_data_in_chunks(\n", "    trip_details_query_bistro,\n", "    start_date_bistro,\n", "    end_date_bistro,\n", "    date_list_bistro,\n", "    org_name=\"Bistro\",\n", "    output_dir=output_dir_bistro,\n", "    connection=\"[Warehouse] Trino\",\n", "    variable_to_format={},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}