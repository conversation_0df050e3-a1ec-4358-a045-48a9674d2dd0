alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: t_minus_one_underhiring_roster_dag
dag_type: etl
escalation_priority: high
execution_timeout: 120
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U07UHBUFB62
path: data_science/store_ops/etl/t_minus_one_underhiring_roster_dag
paused: true
pool: data_science_pool
project_name: store_ops
schedule:
  end_date: '2025-07-22T00:00:00'
  interval: 45 6 * * *
  start_date: '2025-06-02T00:00:00'
schedule_type: fixed
sla: 125 minutes
support_files: []
tags: []
template_name: notebook
version: 3
