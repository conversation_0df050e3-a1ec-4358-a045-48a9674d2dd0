alert_configs:
  slack:
  - channel: roster_dag_alerts
concurrency: 3
dag_name: roster_weekly_data_prep_dag
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebooks:
- alias: absenteeism
  executor_config:
    load_type: medium
    node_type: spot
  name: master_notebook
  parameters:
    notebook_id: 0
  tag: group_1
- alias: iph
  executor_config:
    load_type: medium
    node_type: spot
  name: master_notebook
  parameters:
    notebook_id: 1
  tag: group_1
- alias: ipo
  executor_config:
    load_type: medium
    node_type: spot
  name: master_notebook
  parameters:
    notebook_id: 2
  tag: group_1
- alias: od_limit
  executor_config:
    load_type: medium
    node_type: spot
  name: master_notebook
  parameters:
    notebook_id: 3
  tag: group_1
- alias: picker_util
  executor_config:
    load_type: medium
    node_type: spot
  name: master_notebook
  parameters:
    notebook_id: 4
  tag: group_1
- alias: ppi
  executor_config:
    load_type: medium
    node_type: spot
  name: master_notebook
  parameters:
    notebook_id: 5
  tag: group_1
- alias: fnv_pred_data_prep
  executor_config:
    load_type: medium
    node_type: spot
  name: master_notebook
  parameters:
    notebook_id: 6
  tag: group_2
- alias: packaged_pred_data_prep
  executor_config:
    load_type: medium
    node_type: spot
  name: master_notebook
  parameters:
    notebook_id: 7
  tag: group_2
- alias: perishable_pred_data_prep
  executor_config:
    load_type: medium
    node_type: spot
  name: master_notebook
  parameters:
    notebook_id: 8
  tag: group_2
- alias: model_prediction
  executor_config:
    load_type: medium
    node_type: spot
  name: master_notebook
  parameters:
    notebook_id: 9
  tag: group_3
- alias: hourly_demand
  executor_config:
    load_type: medium
    node_type: spot
  name: master_notebook
  parameters:
    notebook_id: 10
  tag: group_4
- alias: packaged_hourly_data_prep
  executor_config:
    load_type: medium
    node_type: spot
  name: master_notebook
  parameters:
    notebook_id: 11
  tag: group_4
- alias: perishable_hourly_data_prep
  executor_config:
    load_type: medium
    node_type: spot
  name: master_notebook
  parameters:
    notebook_id: 12
  tag: group_4
- alias: fnv_hourly_data_prep
  executor_config:
    load_type: medium
    node_type: spot
  name: master_notebook
  parameters:
    notebook_id: 13
  tag: group_5
- alias: optimiser_input_data_prep
  executor_config:
    load_type: medium
    node_type: spot
  name: master_notebook
  parameters:
    notebook_id: 14
  tag: group_6
owner:
  email: <EMAIL>
  slack_id: U07UHBUFB62
path: data_science/store_ops/etl/roster_weekly_data_prep_dag
paused: false
pool: data_science_pool
project_name: store_ops
schedule:
  end_date: '2025-07-09T00:00:00'
  interval: null
  start_date: '2025-06-10T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- config.py
tags: []
template_name: multi_notebook
version: 1
