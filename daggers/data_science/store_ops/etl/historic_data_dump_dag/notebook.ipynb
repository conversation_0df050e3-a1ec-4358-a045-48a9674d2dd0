{"cells": [{"cell_type": "code", "execution_count": null, "id": "8c7b9a8e-af5c-4f40-9f3a-9ad35548741e", "metadata": {}, "outputs": [], "source": ["!pip install numpy==1.26.4\n", "!pip install papermill==2.3.0\n", "!pip install pandas==1.3.5\n", "!pip install awswrangler==3.9.0\n", "!pip install awscli==1.33.27"]}, {"cell_type": "code", "execution_count": null, "id": "2e6544f8-d55b-4910-976a-dbcccb615d02", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import math\n", "from datetime import datetime, timedelta, date\n", "import boto3\n", "import awswrangler as wr"]}, {"cell_type": "code", "execution_count": null, "id": "f94d8c75-b1f1-4587-9de7-4f579ace9637", "metadata": {}, "outputs": [], "source": ["def write_df_as_parquet_to_s3(data, s3_path, partition_cols=None, suffix=None):\n", "    mode = \"overwrite_partitions\" if partition_cols else \"overwrite\"\n", "    # LOGGER.info(f'writing data to s3 to path {s3_path}')\n", "\n", "    if suffix is not None:\n", "        s3_path = f\"{s3_path.strip('/')}/{suffix}\"\n", "\n", "    wr.s3.to_parquet(data, s3_path, dataset=True, mode=mode, partition_cols=partition_cols)"]}, {"cell_type": "code", "execution_count": null, "id": "9c461702-fee5-47c2-be70-7e237704f348", "metadata": {}, "outputs": [], "source": ["def get_next_monday():\n", "    today = datetime.today()\n", "    days_until_monday = (7 - today.weekday()) % 7  # Days until next Monday\n", "    if days_until_monday == 0:\n", "        days_until_monday = 7  # If today is Monday, get next week's Monday\n", "    next_monday = today + <PERSON><PERSON>ta(days=days_until_monday)\n", "    return next_monday.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "e6eaf783-e5d7-4d99-ad0c-608cffbc6b6c", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)\n", "pd.set_option(\"display.max_rows\", None)\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "8fe9b6e0-3d5e-43e1-8841-259e0fe4eea8", "metadata": {}, "outputs": [], "source": ["target_start_date = get_next_monday()\n", "print(\"Next week's Monday:\", target_start_date)"]}, {"cell_type": "code", "execution_count": null, "id": "54ef014e-2ce3-4952-a3ed-f62be4a35581", "metadata": {}, "outputs": [], "source": ["hour_data_final = pd.read_parquet(\n", "    f\"s3://prod-dse-projects/store_ops/pilot_data/{target_start_date}/final_output/hourly_data/\"\n", ")\n", "roster_data_final = pd.read_parquet(\n", "    f\"s3://prod-dse-projects/store_ops/pilot_data/{target_start_date}/final_output/roaster_data/\"\n", ")\n", "input_data_final = pd.read_parquet(\n", "    f\"s3://prod-dse-projects/store_ops/pilot_data/{target_start_date}/final_output/input_data/\"\n", ")\n", "metric_data_final = pd.read_parquet(\n", "    f\"s3://prod-dse-projects/store_ops/pilot_data/{target_start_date}/final_output/metric_data/\"\n", ")\n", "shift_start_final = pd.read_parquet(\n", "    f\"s3://prod-dse-projects/store_ops/pilot_data/{target_start_date}/final_output/shift_start_array/\"\n", ")"]}, {"cell_type": "markdown", "id": "cbece5bb-9eaf-4eca-a2c0-804b963c30bb", "metadata": {}, "source": ["#### Pushing Data to DS tables"]}, {"cell_type": "code", "execution_count": null, "id": "27148162-478e-4733-9767-c2cb8fec47f4", "metadata": {}, "outputs": [], "source": ["hourly_kwargs = {\n", "    \"schema_name\": \"ds_etls\",\n", "    \"table_name\": \"store_ops_roster_hourly\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"DATE\",\n", "            \"description\": \"Start date(Monday) of the week for which roster is created.\",\n", "        },\n", "        {\"name\": \"date\", \"type\": \"DATE\", \"description\": \"date for which roaster is created.\"},\n", "        {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"Unique identifier for the outlet\"},\n", "        {\"name\": \"hour\", \"type\": \"INTEGER\", \"description\": \"Hour of the day\"},\n", "        {\"name\": \"picking_mh\", \"type\": \"REAL\", \"description\": \"Man-hours spent on picking\"},\n", "        {\"name\": \"putaway_mh\", \"type\": \"REAL\", \"description\": \"Man-hours allocated for putaway\"},\n", "        {\n", "            \"name\": \"non_nego_mh\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"man-hours spent on non-negotiable tasks\",\n", "        },\n", "        {\n", "            \"name\": \"pkg_putaway_mh\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Man-hours spent on package putaway\",\n", "        },\n", "        {\"name\": \"shift_start\", \"type\": \"REAL\", \"description\": \"Shift start time\"},\n", "        {\"name\": \"od_mh\", \"type\": \"REAL\", \"description\": \"OD man-hours allocated\"},\n", "        {\"name\": \"fixed_mh\", \"type\": \"REAL\", \"description\": \"Fulltime man-hours allocated\"},\n", "        {\"name\": \"req_mh\", \"type\": \"REAL\", \"description\": \"Required man-hours\"},\n", "        {\"name\": \"fnv\", \"type\": \"INTEGER\", \"description\": \"fnv executive allocated\"},\n", "        {\n", "            \"name\": \"buffer\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"<PERSON><PERSON><PERSON> present. Used for audit and breaks\",\n", "        },\n", "        {\n", "            \"name\": \"picking_mh_raw\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Actual man-hours spent on picking before adding absenteeism buffer.\",\n", "        },\n", "        {\n", "            \"name\": \"fixed_mh_raw\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Fixed man-hours from optimiser. Before adding fnv and audits.\",\n", "        },\n", "        {\n", "            \"name\": \"shift_start_raw\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Shift start time. Before adding fnv and audits.\",\n", "        },\n", "        {\n", "            \"name\": \"is_roster\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Indicates if the data of  is used for roster or used as as filler days. Use 1 if want to extract roster data.\",\n", "        },\n", "        {\"name\": \"updated_ts\", \"type\": \"VARCHAR\", \"description\": \" Update Timestamp of the record\"},\n", "    ],\n", "    \"primary_key\": [\"week_start_date\", \"date\", \"outlet_id\", \"hour\"],\n", "    \"partition_key\": [\"date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"table_description\": \"This table contains daily roster of the stores.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "6074b3fa-cf88-403d-9010-1cd249100634", "metadata": {}, "outputs": [], "source": ["weekly_kwargs = {\n", "    \"schema_name\": \"ds_etls\",\n", "    \"table_name\": \"store_ops_roster_daily\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date\", \"type\": \"DATE\", \"description\": \"date for which roaster is created.\"},\n", "        {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"Unique identifier for the outlet\"},\n", "        {\n", "            \"name\": \"ft_mandays_req\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \" Daily full-time MANDAYS required\",\n", "        },\n", "        {\n", "            \"name\": \"od_manhours_req\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Daily OD MANHOURS required.\",\n", "        },\n", "        {\n", "            \"name\": \"active_ft_base_in_week\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Active full-time headcount required during week. Unique full time people  \",\n", "        },\n", "        {\n", "            \"name\": \"week_offs\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \" Number of week-offs allocated on that day.\",\n", "        },\n", "        {\"name\": \"updated_ts\", \"type\": \"VARCHAR\", \"description\": \" Update Timestamp of the record\"},\n", "    ],\n", "    \"primary_key\": [\"date\", \"outlet_id\"],\n", "    \"partition_key\": [\"date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"table_description\": \"This table contains weekly roster of the stores.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "c94b80db-4840-439c-afe0-af7e15703054", "metadata": {}, "outputs": [], "source": ["input_kwargs = {\n", "    \"schema_name\": \"ds_etls\",\n", "    \"table_name\": \"store_ops_roster_input\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"DATE\",\n", "            \"description\": \"Start date(Monday) of the week\",\n", "        },\n", "        {\"name\": \"order_date\", \"type\": \"DATE\", \"description\": \"date\"},\n", "        {\"name\": \"hour\", \"type\": \"INTEGER\", \"description\": \"Hour of the day\"},\n", "        {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"outlet_id\"},\n", "        {\"name\": \"qty_to_pick\", \"type\": \"INTEGER\", \"description\": \"Total quantity to be picked\"},\n", "        {\n", "            \"name\": \"perishable_qty_accumulated_final\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"perishable grn qty\",\n", "        },\n", "        {\n", "            \"name\": \"packaged_qty_accumulated_final\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"packaged grn qty\",\n", "        },\n", "        {\"name\": \"mavg_ppi\", \"type\": \"REAL\", \"description\": \"ft employee ppi\"},\n", "        {\"name\": \"mavg_pick_util\", \"type\": \"REAL\", \"description\": \"ft employee picker util\"},\n", "        {\n", "            \"name\": \"mavg_iph_packaged\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"items picked per hour (packaged)\",\n", "        },\n", "        {\n", "            \"name\": \"mavg_iph_perishable\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"items picked per hour (perishable)\",\n", "        },\n", "        {\"name\": \"ppi_od\", \"type\": \"REAL\", \"description\": \"OD ppi\"},\n", "        {\"name\": \"od_util\", \"type\": \"REAL\", \"description\": \"OD picker util\"},\n", "        {\"name\": \"od_eph\", \"type\": \"REAL\", \"description\": \"OD eph\"},\n", "        {\"name\": \"fixed_eph\", \"type\": \"REAL\", \"description\": \"fulltime eph\"},\n", "        {\"name\": \"od_prod\", \"type\": \"REAL\", \"description\": \"OD productivity\"},\n", "        {\"name\": \"absent_predict\", \"type\": \"REAL\", \"description\": \"Predicted absenteeism metric\"},\n", "        {\"name\": \"avg_ipo\", \"type\": \"REAL\", \"description\": \"Average items picked per order\"},\n", "        {\n", "            \"name\": \"grn_difficulty_flag\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Flag indicating difficulty in GRN\",\n", "        },\n", "        {\n", "            \"name\": \"mh_limit\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \" Ful time Man-hours limit for a the hour\",\n", "        },\n", "        {\"name\": \"od_max_evening\", \"type\": \"REAL\", \"description\": \"max OD limit for evening slot \"},\n", "        {\"name\": \"od_max_morning\", \"type\": \"REAL\", \"description\": \"max OD limit for morning slot \"},\n", "        {\"name\": \"od_max_noon\", \"type\": \"REAL\", \"description\": \"max OD limit for noon slot \"},\n", "        {\"name\": \"od_min_day\", \"type\": \"REAL\", \"description\": \"min OD limit for the day \"},\n", "        {\"name\": \"opm_avg_raw\", \"type\": \"REAL\", \"description\": \"average orders per minute\"},\n", "        {\"name\": \"opm_avg\", \"type\": \"REAL\", \"description\": \"orders per minute after bump-up\"},\n", "        {\n", "            \"name\": \"mh_required_for_pick_qt\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Man-hours required to pick after queuing theory.\",\n", "        },\n", "        {\"name\": \"updated_ts\", \"type\": \"VARCHAR\", \"description\": \"Timestamp of the last update\"},\n", "    ],\n", "    \"primary_key\": [\"week_start_date\", \"order_date\", \"outlet_id\", \"hour\"],\n", "    \"partition_key\": [\"order_date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"table_description\": \"This table contains input data of the optimiser.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "5a34a132-9fa5-446a-b51b-e92637f7f97a", "metadata": {}, "outputs": [], "source": ["metric_kwargs = {\n", "    \"schema_name\": \"ds_etls\",\n", "    \"table_name\": \"store_ops_roster_sla\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"DATE\",\n", "            \"description\": \"Start date (Monday) of the week\",\n", "        },\n", "        {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"Unique identifier for the outlet\"},\n", "        {\"name\": \"planned_store_ppi\", \"type\": \"REAL\", \"description\": \"Planned PPI for the store\"},\n", "        {\n", "            \"name\": \"planned_ft_ppi\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Planned PPI for the full-time employee. \",\n", "        },\n", "        {\"name\": \"planned_od_ppi\", \"type\": \"REAL\", \"description\": \"Planned PPI for OD\"},\n", "        {\n", "            \"name\": \"planned_store_util\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Planned store picker utilization\",\n", "        },\n", "        {\n", "            \"name\": \"planned_ft_util\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Planned full-time picker utilization\",\n", "        },\n", "        {\"name\": \"planned_od_util\", \"type\": \"REAL\", \"description\": \"Planned OD picker utilization\"},\n", "        {\"name\": \"planned_store_iph\", \"type\": \"REAL\", \"description\": \"Planned IPH for the store.\"},\n", "        {\n", "            \"name\": \"planned_od_item_share\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Planned od item contribution.\",\n", "        },\n", "        {\"name\": \"updated_ts\", \"type\": \"VARCHAR\", \"description\": \"Update timestamp of the record\"},\n", "    ],\n", "    \"primary_key\": [\"week_start_date\", \"outlet_id\"],\n", "    \"partition_key\": [\"week_start_date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"table_description\": \"This table contains weekly planned sla for the stores.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "58580c75-1abd-4591-b46c-6f8575f86eb1", "metadata": {}, "outputs": [], "source": ["output1_final = hour_data_final.copy()\n", "min_date = output1_final[\"order_date\"].min()\n", "max_date = output1_final[\"order_date\"].max()\n", "update_ts_ist = datetime.now() + <PERSON><PERSON>ta(hours=5.5)\n", "update_ts_ist = update_ts_ist.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "output1_final[\"week_start_date\"] = pd.to_datetime(target_start_date)\n", "output1_final[\"putaway_mh\"] = (\n", "    output1_final[\"unloading_perishable_putaway_mh\"] + output1_final[\"pckg_mh_allocation\"]\n", ")\n", "output1_final[\"fnv\"] = output1_final[\"fnv_mh\"]\n", "output1_final[\"buffer\"] = (\n", "    (output1_final[\"od_mh\"] * output1_final[\"od_prod\"])\n", "    + output1_final[\"fixed_mh\"]\n", "    - output1_final[\"req_mh\"]\n", "    - output1_final[\"fnv\"]\n", ")\n", "# output1_final['fixed_mh_allocated'] = output1_final['fixed_mh_allocated'] - output1_final['fnv']\n", "output1_final[\"is_roster\"] = np.where(\n", "    (output1_final.order_date == min_date) | (output1_final.order_date == max_date), 0, 1\n", ")\n", "\n", "output1_final[\"updated_ts\"] = update_ts_ist\n", "\n", "columns = [\n", "    \"week_start_date\",\n", "    \"order_date\",\n", "    \"outlet_id\",\n", "    \"hour\",\n", "    \"picking_mh\",\n", "    \"putaway_mh\",\n", "    \"non_nego_mh\",\n", "    \"pckg_mh_allocation\",\n", "    \"employee_starting_shift\",\n", "    \"od_mh\",\n", "    \"fixed_mh\",\n", "    \"req_mh\",\n", "    \"fnv\",\n", "    \"buffer\",\n", "    \"actual_pick_mh\",\n", "    \"fixed_mh_allocated\",\n", "    \"shift_start_raw\",\n", "    \"is_roster\",\n", "    \"updated_ts\",\n", "]\n", "output1_final = output1_final[columns]\n", "output1_final = output1_final.rename(\n", "    columns={\n", "        \"order_date\": \"date\",\n", "        \"pckg_mh_allocation\": \"pkg_putaway_mh\",\n", "        \"employee_starting_shift\": \"shift_start\",\n", "        \"fixed_mh_allocated\": \"fixed_mh_raw\",\n", "        \"actual_pick_mh\": \"picking_mh_raw\",\n", "    }\n", ")\n", "\n", "pb.to_trino(data_obj=output1_final, **hourly_kwargs)\n", "\n", "output2_final = roster_data_final.copy()\n", "# display(output2_final.info())\n", "output2_final[\"updated_ts\"] = update_ts_ist\n", "columns = [\n", "    \"order_date\",\n", "    \"outlet_id\",\n", "    \"fixed_md_required\",\n", "    \"od_mh_required\",\n", "    \"active_base_in_week\",\n", "    \"week_offs\",\n", "    \"updated_ts\",\n", "]\n", "output2_final = output2_final[columns]\n", "\n", "output2_final = output2_final.rename(\n", "    columns={\n", "        \"order_date\": \"date\",\n", "        \"fixed_md_required\": \"ft_mandays_req\",\n", "        \"od_mh_required\": \"od_manhours_req\",\n", "        \"active_base_in_week\": \"active_ft_base_in_week\",\n", "    }\n", ")\n", "pb.to_trino(data_obj=output2_final, **weekly_kwargs)\n", "\n", "input_data = input_data_final.copy()\n", "input_data[\"week_start_date\"] = pd.to_datetime(target_start_date)\n", "input_data[\"updated_ts\"] = update_ts_ist\n", "columns = [\n", "    \"week_start_date\",\n", "    \"order_date\",\n", "    \"hour\",\n", "    \"outlet_id\",\n", "    \"qty_to_pick\",\n", "    \"perishable_qty_accumulated_final\",\n", "    \"packaged_qty_accumulated_final\",\n", "    \"mavg_ppi\",\n", "    \"mavg_pick_util\",\n", "    \"mavg_iph_packaged\",\n", "    \"mavg_iph_perishable\",\n", "    \"ppi_od\",\n", "    \"od_util\",\n", "    \"od_eph\",\n", "    \"fixed_eph\",\n", "    \"od_prod\",\n", "    \"absent_predict\",\n", "    \"avg_ipo\",\n", "    \"grn_difficulty_flag\",\n", "    \"mh_limit\",\n", "    \"od_max_evening\",\n", "    \"od_max_morning\",\n", "    \"od_max_noon\",\n", "    \"od_min_day\",\n", "    \"opm_avg_raw\",\n", "    \"opm_avg\",\n", "    \"mh_required_for_pick_qt\",\n", "    \"updated_ts\",\n", "]\n", "\n", "\n", "input_data = input_data[columns]\n", "\n", "pb.to_trino(data_obj=input_data, **input_kwargs)\n", "\n", "result_df = metric_data_final.copy()\n", "result_df[\"week_start_date\"] = pd.to_datetime(target_start_date)\n", "if \"planned_od_item_share\" not in result_df.columns:\n", "    result_df[\"planned_od_item_share\"] = -1\n", "result_df[\"updated_ts\"] = update_ts_ist\n", "columns = [\n", "    \"week_start_date\",\n", "    \"outlet_id\",\n", "    \"planned_store_ppi\",\n", "    \"planned_ft_ppi\",\n", "    \"planned_od_ppi\",\n", "    \"planned_store_util\",\n", "    \"planned_ft_util\",\n", "    \"planned_od_util\",\n", "    \"planned_store_iph\",\n", "    \"planned_od_item_share\",\n", "    \"updated_ts\",\n", "]\n", "result_df = result_df[columns]\n", "pb.to_trino(data_obj=result_df, **metric_kwargs)\n", "\n", "print(\"data pushed\")"]}, {"cell_type": "markdown", "id": "f00ed380-356e-4e9a-ac0d-f975086b271e", "metadata": {}, "source": ["#### Pushing data to planner"]}, {"cell_type": "code", "execution_count": null, "id": "0cb6b9f7-f180-45e2-8f5e-6512d9b978bb", "metadata": {}, "outputs": [], "source": ["hourly_kwargs = {\n", "    \"schema_name\": \"storeops_etls\",\n", "    \"table_name\": \"planner_instore_forecasts_hourly_m1\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"DATE\",\n", "            \"description\": \"Start date(Monday) of the week for which roster is created.\",\n", "        },\n", "        {\"name\": \"date\", \"type\": \"DATE\", \"description\": \"date for which roaster is created.\"},\n", "        {\"name\": \"entity_id\", \"type\": \"INTEGER\", \"description\": \"Unique identifier for the outlet\"},\n", "        {\"name\": \"hour\", \"type\": \"INTEGER\", \"description\": \"Hour of the day\"},\n", "        {\"name\": \"manhours_picking\", \"type\": \"REAL\", \"description\": \"Man-hours spent on picking\"},\n", "        {\n", "            \"name\": \"manhours_putaway\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Man-hours allocated for putaway\",\n", "        },\n", "        {\"name\": \"headcount_shift_start\", \"type\": \"REAL\", \"description\": \"Shift start time\"},\n", "        {\"name\": \"manhours_od\", \"type\": \"REAL\", \"description\": \"OD man-hours allocated\"},\n", "        {\"name\": \"manhours_fixed\", \"type\": \"REAL\", \"description\": \"Fulltime man-hours allocated\"},\n", "        {\"name\": \"manhours_fnv\", \"type\": \"INTEGER\", \"description\": \"fnv executive allocated\"},\n", "        {\n", "            \"name\": \"manhours_buffer\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"<PERSON><PERSON><PERSON> present. Used for audit and breaks\",\n", "        },\n", "        {\"name\": \"updated_ts\", \"type\": \"VARCHAR\", \"description\": \" Update Timestamp of the record\"},\n", "    ],\n", "    \"primary_key\": [\"week_start_date\", \"date\", \"entity_id\", \"hour\"],\n", "    \"partition_key\": [\"date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"table_description\": \"This table contains daily roster of the stores.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "1eef6352-4af6-42f8-a547-c82ca5fdb816", "metadata": {}, "outputs": [], "source": ["update_ts_ist = datetime.now() + <PERSON><PERSON>ta(hours=5.5)\n", "update_ts_ist = update_ts_ist.strftime(\"%Y-%m-%d %H:%M:%S\")"]}, {"cell_type": "code", "execution_count": null, "id": "f71ab082-bbca-43aa-b51f-10311ad46c2c", "metadata": {}, "outputs": [], "source": ["output1_final = hour_data_final.copy()\n", "min_date = output1_final[\"order_date\"].min()\n", "max_date = output1_final[\"order_date\"].max()\n", "output1_final[\"week_start_date\"] = pd.to_datetime(target_start_date)\n", "output1_final[\"putaway_mh\"] = (\n", "    output1_final[\"unloading_perishable_putaway_mh\"] + output1_final[\"pckg_mh_allocation\"]\n", ")\n", "output1_final[\"fnv\"] = output1_final[\"fnv_mh\"]\n", "output1_final[\"buffer\"] = (\n", "    (output1_final[\"od_mh\"] * output1_final[\"od_prod\"])\n", "    + output1_final[\"fixed_mh\"]\n", "    - output1_final[\"req_mh\"]\n", "    - output1_final[\"fnv\"]\n", ")\n", "output1_final[\"is_roster\"] = np.where(\n", "    (output1_final.order_date == min_date) | (output1_final.order_date == max_date), 0, 1\n", ")\n", "output1_final[\"updated_ts\"] = update_ts_ist"]}, {"cell_type": "code", "execution_count": null, "id": "2e2207a5-3897-440f-9903-f243ab0fae44", "metadata": {}, "outputs": [], "source": ["output1_final = output1_final[output1_final.order_date != output1_final.order_date.min()]\n", "output1_final = output1_final[output1_final.order_date != output1_final.order_date.max()]"]}, {"cell_type": "code", "execution_count": null, "id": "bf109fb8-91c3-4a72-9cde-74a97b8357c9", "metadata": {}, "outputs": [], "source": ["columns = [\n", "    \"week_start_date\",\n", "    \"order_date\",\n", "    \"outlet_id\",\n", "    \"hour\",\n", "    \"picking_mh\",\n", "    \"putaway_mh\",\n", "    \"employee_starting_shift\",\n", "    \"od_mh\",\n", "    \"fixed_mh\",\n", "    \"fnv\",\n", "    \"buffer\",\n", "    \"updated_ts\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "51ef422d-1522-4c04-b97b-a3e64b1bea24", "metadata": {}, "outputs": [], "source": ["output1_final = output1_final[columns]\n", "output1_final = output1_final.rename(\n", "    columns={\n", "        \"order_date\": \"date\",\n", "        \"outlet_id\": \"entity_id\",\n", "        \"picking_mh\": \"manhours_picking\",\n", "        \"putaway_mh\": \"manhours_putaway\",\n", "        \"employee_starting_shift\": \"headcount_shift_start\",\n", "        \"od_mh\": \"manhours_od\",\n", "        \"fixed_mh\": \"manhours_fixed\",\n", "        \"fnv\": \"manhours_fnv\",\n", "        \"buffer\": \"manhours_buffer\",\n", "    }\n", ")\n", "output1_final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "72c24bf5-f25a-4abd-bb58-aeccdf646532", "metadata": {}, "outputs": [], "source": ["pb.to_trino(data_obj=output1_final, **hourly_kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "cbdfb300-b186-48df-b60a-e4c328821e36", "metadata": {}, "outputs": [], "source": ["weekly_kwargs = {\n", "    \"schema_name\": \"storeops_etls\",\n", "    \"table_name\": \"planner_instore_forecasts_daily_m1\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"DATE\",\n", "            \"description\": \"Start date(Monday) of the week for which roster is created.\",\n", "        },\n", "        {\"name\": \"date\", \"type\": \"DATE\", \"description\": \"date for which roaster is created.\"},\n", "        {\"name\": \"entity_id\", \"type\": \"INTEGER\", \"description\": \"Unique identifier for the outlet\"},\n", "        {\n", "            \"name\": \"mandays_fixed\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \" Daily full-time MANDAYS required\",\n", "        },\n", "        {\n", "            \"name\": \"manhours_od\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Daily OD MANHOURS required.\",\n", "        },\n", "        {\n", "            \"name\": \"headcount\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Active full-time headcount available after week_offs  \",\n", "        },\n", "        {\n", "            \"name\": \"week_offs\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \" Number of week-offs allocated on that day.\",\n", "        },\n", "        {\"name\": \"updated_ts\", \"type\": \"VARCHAR\", \"description\": \" Update Timestamp of the record\"},\n", "    ],\n", "    \"primary_key\": [\"week_start_date\", \"date\", \"entity_id\"],\n", "    \"partition_key\": [\"date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"table_description\": \"This table contains weekly roster of the stores.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "b191cc4b-fc64-4fd2-84d9-39af9f69e9a4", "metadata": {}, "outputs": [], "source": ["output2_final = roster_data_final.copy()\n", "output2_final[\"updated_ts\"] = update_ts_ist\n", "output2_final[\"week_start_date\"] = pd.to_datetime(target_start_date)\n", "\n", "columns = [\n", "    \"week_start_date\",\n", "    \"order_date\",\n", "    \"outlet_id\",\n", "    \"fixed_md_required\",\n", "    \"od_mh_required\",\n", "    \"final_mp_at_store_after_weekoffs\",\n", "    \"week_offs\",\n", "    \"updated_ts\",\n", "]\n", "output2_final = output2_final[columns]\n", "output2_final = output2_final.rename(\n", "    columns={\n", "        \"order_date\": \"date\",\n", "        \"outlet_id\": \"entity_id\",\n", "        \"fixed_md_required\": \"mandays_fixed\",\n", "        \"od_mh_required\": \"manhours_od\",\n", "        \"final_mp_at_store_after_weekoffs\": \"headcount\",\n", "    }\n", ")\n", "output2_final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d2ab6ea4-56e5-4158-b182-28b2827915c8", "metadata": {}, "outputs": [], "source": ["pb.to_trino(data_obj=output2_final, **weekly_kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "3c887d7f-e22a-4fcb-94f1-cbaebc30b1b4", "metadata": {}, "outputs": [], "source": ["metric_kwargs = {\n", "    \"schema_name\": \"storeops_etls\",\n", "    \"table_name\": \"planner_instore_forecasts_weekly_m1\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"DATE\",\n", "            \"description\": \"Start date (Monday) of the week\",\n", "        },\n", "        {\"name\": \"entity_id\", \"type\": \"INTEGER\", \"description\": \"Unique identifier for the outlet\"},\n", "        {\"name\": \"ppi\", \"type\": \"REAL\", \"description\": \"Planned PPI for the fixed employee\"},\n", "        {\n", "            \"name\": \"total_picking_time_sec\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"total_picking_time_fixed \",\n", "        },\n", "        {\n", "            \"name\": \"total_items_quantity_ordered\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"total_items_quantity_ordered\",\n", "        },\n", "        {\n", "            \"name\": \"picker_util_fixed_wt_avg\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \" sum-product of qty x fixed_util\",\n", "        },\n", "        {\n", "            \"name\": \"picker_util_numerator_fixed\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"sum-product of the util and qty.\",\n", "        },\n", "        {\n", "            \"name\": \"items_put_away_per_hour\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"items_put_away_per_hour\",\n", "        },\n", "        {\n", "            \"name\": \"putaway_qty\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"putaway_qty\",\n", "        },\n", "        {\n", "            \"name\": \"putter_active_time_mins\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"putter_active_time_mins\",\n", "        },\n", "        {\n", "            \"name\": \"od_contribution_pct\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"od_contribution_pct\",\n", "        },\n", "        {\"name\": \"qty_ordered_od\", \"type\": \"REAL\", \"description\": \"qty_ordered_od\"},\n", "        {\n", "            \"name\": \"qty_ordered_fixed\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"qty_ordered_fixed\",\n", "        },\n", "        {\n", "            \"name\": \"headcount\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \" active base for the week.\",\n", "        },\n", "        {\"name\": \"updated_ts\", \"type\": \"VARCHAR\", \"description\": \"Update timestamp of the record\"},\n", "    ],\n", "    \"primary_key\": [\"week_start_date\", \"entity_id\"],\n", "    \"partition_key\": [\"week_start_date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"table_description\": \"This table contains weekly planned sla for the stores.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "b8a0716d-cd1f-4ed2-a07c-c88c7f186747", "metadata": {}, "outputs": [], "source": ["input_df = input_data_final.copy()\n", "hourly_df = hour_data_final.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "ab1fed54-618b-41dd-8a28-45de700dab41", "metadata": {}, "outputs": [], "source": ["input_df = input_df.merge(\n", "    hourly_df[\n", "        [\n", "            \"order_date\",\n", "            \"outlet_id\",\n", "            \"hour\",\n", "            \"picking_mh\",\n", "            \"od_mh\",\n", "            \"pckg_mh_allocation\",\n", "            \"unloading_perishable_putaway_mh\",\n", "        ]\n", "    ]\n", ")\n", "input_df[\"od_picking_time\"] = input_df[\"od_mh\"] * input_df[\"od_prod\"]\n", "input_df[\"fixed_picking_time\"] = input_df[\"picking_mh\"] - input_df[\"od_picking_time\"]\n", "input_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "03d3e2c2-3f3b-4683-aa08-b4e105b1d9de", "metadata": {}, "outputs": [], "source": ["input_df[\"ppi_numerator\"] = input_df[\"mavg_ppi\"] * input_df[\"qty_to_pick\"]\n", "input_df[\"util_numerator\"] = input_df[\"mavg_pick_util\"] * input_df[\"qty_to_pick\"]"]}, {"cell_type": "code", "execution_count": null, "id": "5a7321b9-3a33-4799-93f3-c4ab186bba03", "metadata": {}, "outputs": [], "source": ["start_date = (datetime.strptime(target_start_date, \"%Y-%m-%d\") - timedelta(days=0)).strftime(\n", "    \"%Y-%m-%d\"\n", ")\n", "end_date = (datetime.strptime(target_start_date, \"%Y-%m-%d\") + timedelta(days=6)).strftime(\n", "    \"%Y-%m-%d\"\n", ")\n", "print(start_date, end_date)"]}, {"cell_type": "code", "execution_count": null, "id": "059b1ca7-c969-424b-ab19-accdd7af2e88", "metadata": {}, "outputs": [], "source": ["df = input_df[(input_df.order_date >= start_date) & (input_df.order_date <= end_date)].copy()\n", "df[\"fixed_picking_time\"] = np.where(df[\"fixed_picking_time\"] < 0, 0, df[\"fixed_picking_time\"])"]}, {"cell_type": "code", "execution_count": null, "id": "919b2db8-5f65-4f6b-889a-c2c2b4e3fa89", "metadata": {}, "outputs": [], "source": ["df[\"fixed_item\"] = df[\"qty_to_pick\"] * (df[\"fixed_picking_time\"] / df[\"picking_mh\"])\n", "df[\"od_item\"] = df[\"qty_to_pick\"] * (df[\"od_picking_time\"] / df[\"picking_mh\"])\n", "df[\"fixed_num\"] = df[\"fixed_item\"] * df[\"mavg_ppi\"]\n", "df[\"od_num\"] = df[\"od_item\"] * df[\"ppi_od\"]\n", "agg_df = (\n", "    df.groupby([\"outlet_id\"])[[\"fixed_num\", \"od_num\", \"qty_to_pick\", \"fixed_item\", \"od_item\"]]\n", "    .sum()\n", "    .reset_index()\n", ")\n", "agg_df[\"ppi\"] = (agg_df[\"od_num\"] + agg_df[\"fixed_num\"]) / agg_df[\"qty_to_pick\"]\n", "agg_df[\"day_ft_ppi\"] = (agg_df[\"fixed_num\"]) / agg_df[\"fixed_item\"]\n", "agg_df[\"day_od_ppi\"] = (agg_df[\"od_num\"]) / agg_df[\"od_item\"]\n", "agg_df[\"picking_time_sec\"] = agg_df[\"fixed_num\"] + agg_df[\"od_num\"]\n", "agg_df[\"total_items_quantity_ordered\"] = agg_df[\"qty_to_pick\"]\n", "ppi_df = agg_df[[\"outlet_id\", \"ppi\", \"picking_time_sec\", \"total_items_quantity_ordered\"]]\n", "ppi_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a4fa7d57-ca87-44f3-ac93-deaedaf8c73b", "metadata": {}, "outputs": [], "source": ["contri_df = df.groupby([\"outlet_id\"])[[\"fixed_item\", \"od_item\"]].sum().reset_index()\n", "contri_df[\"od_contribution_pct\"] = contri_df[\"od_item\"] / (\n", "    contri_df[\"od_item\"] + contri_df[\"fixed_item\"]\n", ")\n", "contri_df[\"qty_ordered_od\"] = np.round(contri_df[\"od_item\"])\n", "contri_df[\"qty_ordered_fixed\"] = np.round(contri_df[\"fixed_item\"])\n", "contri_df = contri_df[[\"outlet_id\", \"od_contribution_pct\", \"qty_ordered_od\", \"qty_ordered_fixed\"]]\n", "contri_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ad17710b-0d77-4908-9739-2d2cc27ed5a1", "metadata": {}, "outputs": [], "source": ["df[\"od_active_time\"] = df[\"od_picking_time\"] / df[\"od_prod\"]\n", "df[\"od_busy_time\"] = df[\"od_active_time\"] * df[\"od_util\"]\n", "\n", "df[\"fixed_active_time\"] = df[\"picking_mh\"] - df[\"od_active_time\"]\n", "df[\"fixed_active_time\"] = np.where(df[\"fixed_active_time\"] < 0, 0, df[\"fixed_active_time\"])\n", "df[\"fixed_busy_time\"] = df[\"fixed_active_time\"] * df[\"mavg_pick_util\"]\n", "\n", "\n", "agg_df = (\n", "    df.groupby([\"outlet_id\"])[\n", "        [\"fixed_busy_time\", \"od_busy_time\", \"qty_to_pick\", \"picking_mh\", \"fixed_active_time\"]\n", "    ]\n", "    .sum()\n", "    .reset_index()\n", ")\n", "agg_df[\"day_util\"] = (agg_df[\"od_busy_time\"] + agg_df[\"fixed_busy_time\"]) / agg_df[\"picking_mh\"]\n", "agg_df[\"picker_utilization_fixed\"] = agg_df[\"fixed_busy_time\"] / agg_df[\"fixed_active_time\"]\n", "agg_df[\"picker_busy_time_fixed_sec\"] = agg_df[\"fixed_busy_time\"] * 3600\n", "agg_df[\"picker_active_time_fixed_sec\"] = agg_df[\"fixed_active_time\"] * 3600\n", "util_df = agg_df[\n", "    [\n", "        \"outlet_id\",\n", "        \"picker_utilization_fixed\",\n", "        \"picker_busy_time_fixed_sec\",\n", "        \"picker_active_time_fixed_sec\",\n", "    ]\n", "]\n", "util_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "648b4aec-160b-435c-a219-cf97e987e4b5", "metadata": {}, "outputs": [], "source": ["df[\"pkg_qty_putaway\"] = df[\"pckg_mh_allocation\"] * df[\"mavg_iph_packaged\"]\n", "agg_df = (\n", "    df.groupby([\"outlet_id\"])[\n", "        [\n", "            \"perishable_qty_accumulated_final\",\n", "            \"pkg_qty_putaway\",\n", "            \"pckg_mh_allocation\",\n", "            \"unloading_perishable_putaway_mh\",\n", "        ]\n", "    ]\n", "    .sum()\n", "    .reset_index()\n", ")\n", "agg_df[\"total_putaway_qty\"] = agg_df[\"pkg_qty_putaway\"] + agg_df[\"perishable_qty_accumulated_final\"]\n", "agg_df[\"total_putaway_mh\"] = (\n", "    agg_df[\"pckg_mh_allocation\"] + agg_df[\"unloading_perishable_putaway_mh\"]\n", ")\n", "agg_df[\"items_put_away_per_hour\"] = np.floor(\n", "    agg_df[\"total_putaway_qty\"] / agg_df[\"total_putaway_mh\"]\n", ")\n", "agg_df[\"putter_active_time_mins\"] = np.round(agg_df[\"total_putaway_mh\"] * 60)\n", "agg_df[\"putaway_qty\"] = agg_df[\"total_putaway_qty\"]\n", "\n", "iph_df = agg_df[[\"outlet_id\", \"items_put_away_per_hour\", \"putter_active_time_mins\", \"putaway_qty\"]]\n", "iph_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "81e46594-1f62-4add-9f21-1b7002b17280", "metadata": {}, "outputs": [], "source": ["result_df = ppi_df.merge(util_df, on=[\"outlet_id\"])\n", "result_df = result_df.merge(iph_df, on=[\"outlet_id\"])\n", "result_df = result_df.merge(contri_df, on=[\"outlet_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "8b040581-ca15-4b02-8650-29a3ef85c072", "metadata": {}, "outputs": [], "source": ["output2_final = roster_data_final.copy()\n", "output2_final = output2_final[[\"outlet_id\", \"active_base_in_week\"]].drop_duplicates()\n", "output2_final = output2_final.rename(columns={\"active_base_in_week\": \"headcount\"})\n", "output2_final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "99b371a1-bb36-4dfe-924d-5131084339a3", "metadata": {}, "outputs": [], "source": ["result_df = result_df.merge(output2_final, on=[\"outlet_id\"])\n", "agg_df = df.groupby([\"outlet_id\"])[[\"ppi_numerator\", \"util_numerator\"]].sum().reset_index()\n", "result_df = result_df.merge(agg_df, on=[\"outlet_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "d038b235-19b9-44af-963e-9aaa2101fbd7", "metadata": {}, "outputs": [], "source": ["result_df[\"ppi_new\"] = result_df[\"ppi_numerator\"] / result_df[\"total_items_quantity_ordered\"]\n", "result_df[\"util_new\"] = result_df[\"util_numerator\"] / result_df[\"total_items_quantity_ordered\"]"]}, {"cell_type": "code", "execution_count": null, "id": "9a212b2d-136d-4ed9-bac8-e69c8592ef20", "metadata": {}, "outputs": [], "source": ["result_df[\"diff\"] = (result_df[\"picking_time_sec\"] - result_df[\"ppi_numerator\"]) / result_df[\n", "    \"picking_time_sec\"\n", "]\n", "result_df[\"diff\"].describe()"]}, {"cell_type": "code", "execution_count": null, "id": "baa1a495-2c55-49a7-9974-c4349a313647", "metadata": {}, "outputs": [], "source": ["result_df[\"picker_util_numerator_fixed\"] = result_df[\"util_numerator\"]\n", "result_df[\"total_picking_time_sec\"] = result_df[\"ppi_numerator\"]\n", "result_df[\"ppi\"] = result_df[\"picking_time_sec\"] / result_df[\"total_items_quantity_ordered\"]\n", "result_df[\"picker_util_fixed_wt_avg\"] = (\n", "    result_df[\"picker_util_numerator_fixed\"] / result_df[\"total_items_quantity_ordered\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f26bf150-b668-44d7-8c45-c5b02da9880b", "metadata": {}, "outputs": [], "source": ["result_df = result_df.drop(\n", "    columns=[\n", "        \"ppi_new\",\n", "        \"util_new\",\n", "        \"picker_busy_time_fixed_sec\",\n", "        \"picker_active_time_fixed_sec\",\n", "        \"ppi_numerator\",\n", "        \"util_numerator\",\n", "        \"picker_utilization_fixed\",\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "80e96c92-7793-40c6-bba7-ec3da31a292e", "metadata": {}, "outputs": [], "source": ["result_df = result_df.rename(columns={\"outlet_id\": \"entity_id\"})\n", "result_df[\"updated_ts\"] = update_ts_ist\n", "result_df[\"week_start_date\"] = pd.to_datetime(target_start_date)"]}, {"cell_type": "code", "execution_count": null, "id": "c249a9ea-c13b-4e3f-bf87-2eceace98019", "metadata": {}, "outputs": [], "source": ["columns = [\n", "    \"entity_id\",\n", "    \"week_start_date\",\n", "    \"updated_ts\",\n", "    \"ppi\",\n", "    \"total_picking_time_sec\",\n", "    \"total_items_quantity_ordered\",\n", "    \"picker_util_fixed_wt_avg\",\n", "    \"picker_util_numerator_fixed\",\n", "    \"od_contribution_pct\",\n", "    \"qty_ordered_od\",\n", "    \"qty_ordered_fixed\",\n", "    \"items_put_away_per_hour\",\n", "    \"putaway_qty\",\n", "    \"putter_active_time_mins\",\n", "    \"headcount\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "064e5f2c-3cf5-470a-b0d7-c739302733c7", "metadata": {}, "outputs": [], "source": ["result_df = result_df[columns]\n", "result_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "79c970b8-f892-4e2e-9c7e-6e152991055f", "metadata": {}, "outputs": [], "source": ["pb.to_trino(data_obj=result_df, **metric_kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "254cd24d-a6cd-4070-8627-1f43a37e7fd8", "metadata": {}, "outputs": [], "source": ["metric_kwargs = {\n", "    \"schema_name\": \"storeops_etls\",\n", "    \"table_name\": \"planner_core_forecasts_weekly_m1\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"DATE\",\n", "            \"description\": \"Start date (Monday) of the week\",\n", "        },\n", "        {\"name\": \"entity_id\", \"type\": \"INTEGER\", \"description\": \"Unique identifier for the outlet\"},\n", "        {\n", "            \"name\": \"total_items_quantity_ordered\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"total_items_quantity_ordered\",\n", "        },\n", "        {\"name\": \"instore_orders\", \"type\": \"REAL\", \"description\": \"orders\"},\n", "        {\n", "            \"name\": \"items_per_order\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"items_per_order\",\n", "        },\n", "        {\"name\": \"total_quantity_indent\", \"type\": \"REAL\", \"description\": \"total_quantity_indent.\"},\n", "        {\"name\": \"updated_ts\", \"type\": \"VARCHAR\", \"description\": \"Update timestamp of the record\"},\n", "    ],\n", "    \"primary_key\": [\"week_start_date\", \"entity_id\"],\n", "    \"partition_key\": [\"week_start_date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"table_description\": \"This table contains weekly orders and indent related metrics for the stores.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "2c3142df-20a8-41da-bbc0-c070d0da2d2f", "metadata": {}, "outputs": [], "source": ["input_df = input_data_final.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "c08e321e-ff32-495d-bb53-d25f3efc83af", "metadata": {}, "outputs": [], "source": ["input_df = input_df[\n", "    [\n", "        \"order_date\",\n", "        \"hour\",\n", "        \"outlet_id\",\n", "        \"qty_to_pick\",\n", "        \"avg_ipo\",\n", "        \"perishable_qty_accumulated_final\",\n", "        \"packaged_qty_accumulated_final\",\n", "    ]\n", "]\n", "start_date = (datetime.strptime(target_start_date, \"%Y-%m-%d\") - timedelta(days=0)).strftime(\n", "    \"%Y-%m-%d\"\n", ")\n", "end_date = (datetime.strptime(target_start_date, \"%Y-%m-%d\") + timedelta(days=6)).strftime(\n", "    \"%Y-%m-%d\"\n", ")\n", "print(start_date, end_date)"]}, {"cell_type": "code", "execution_count": null, "id": "3e7eaabe-06c0-4b64-b2b5-9b4f88d3f211", "metadata": {}, "outputs": [], "source": ["df = input_df[(input_df.order_date >= start_date) & (input_df.order_date <= end_date)].copy()\n", "df[\"indent\"] = df[\"perishable_qty_accumulated_final\"] + df[\"packaged_qty_accumulated_final\"]\n", "df[\"orders\"] = df[\"qty_to_pick\"] / df[\"avg_ipo\"]\n", "df[\"orders\"] = df[\"orders\"].replace([np.inf, -np.inf], 0).fillna(0)\n", "df[\"orders\"] = np.round(df[\"orders\"])"]}, {"cell_type": "code", "execution_count": null, "id": "90aebedf-8ba5-4854-b45c-33908113ede9", "metadata": {}, "outputs": [], "source": ["df.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "18f9c932-105b-41b0-af3a-53ade5463fcf", "metadata": {}, "outputs": [], "source": ["agg_df = df.groupby([\"outlet_id\"])[[\"qty_to_pick\", \"orders\", \"indent\"]].sum().reset_index()\n", "agg_df[\"updated_ts\"] = update_ts_ist\n", "agg_df[\"week_start_date\"] = pd.to_datetime(target_start_date)"]}, {"cell_type": "code", "execution_count": null, "id": "f059e50c-008e-4900-8bc3-2245ca081523", "metadata": {}, "outputs": [], "source": ["agg_df = agg_df.rename(\n", "    columns={\n", "        \"outlet_id\": \"entity_id\",\n", "        \"qty_to_pick\": \"total_items_quantity_ordered\",\n", "        \"orders\": \"instore_orders\",\n", "        \"indent\": \"total_quantity_indent\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a8d9f67c-e6d0-41bc-9030-38d0938ebcef", "metadata": {}, "outputs": [], "source": ["agg_df[\"items_per_order\"] = np.round(\n", "    agg_df[\"total_items_quantity_ordered\"] / agg_df[\"instore_orders\"], 2\n", ")\n", "pb.to_trino(data_obj=agg_df, **metric_kwargs)"]}, {"cell_type": "markdown", "id": "18b15c01-a644-40e9-a6c3-a5b1636399da", "metadata": {"tags": []}, "source": ["### Pushing arrays data"]}, {"cell_type": "code", "execution_count": null, "id": "c858b24d-13e4-43ad-9d2d-91588f2d39a4", "metadata": {}, "outputs": [], "source": ["master = shift_start_final.copy()\n", "\n", "\n", "master[\"week_off\"] = np.where(master[\"hour\"].isnull(), 1, 0)\n", "\n", "master[\"date\"] = pd.to_datetime(master[\"date\"])  # ensure datetime\n", "master[\"start_ts\"] = master[\"date\"] + pd.to_timedelta(master[\"hour\"], unit=\"h\")\n", "master[\"end_ts\"] = master[\"start_ts\"] + pd.<PERSON><PERSON><PERSON>(hours=9)\n", "\n", "master[\"group_id\"] = \"employee_\" + master[\"employee\"].astype(str)\n", "master[\"entity_id\"] = master[\"outlet_id\"]\n", "\n", "master[\"start_ts\"] = np.where(pd.isnull(master[\"start_ts\"]), master[\"date\"], master[\"start_ts\"])\n", "\n", "master[\"end_ts\"] = np.where(\n", "    pd.isnull(master[\"end_ts\"]),\n", "    master[\"date\"] + pd.<PERSON><PERSON><PERSON>(hours=23, minutes=59),\n", "    master[\"end_ts\"],\n", ")\n", "\n", "# format as ISO 8601 strings\n", "master[\"start_ts\"] = (\n", "    pd.to_datetime(master[\"start_ts\"])\n", "    .dt.tz_localize(\"Asia/Kolkata\")\n", "    .dt.tz_convert(\"UTC\")\n", "    .dt.strftime(\"%Y-%m-%dT%H:%M:%S\")\n", ")\n", "\n", "master[\"end_ts\"] = (\n", "    pd.to_datetime(master[\"end_ts\"])\n", "    .dt.tz_localize(\"Asia/Kolkata\")\n", "    .dt.tz_convert(\"UTC\")\n", "    .dt.strftime(\"%Y-%m-%dT%H:%M:%S\")\n", ")\n", "\n", "master[\"week_start_date\"] = pd.to_datetime(start_date)\n", "master[\"date\"] = (\n", "    pd.to_datetime(master[\"start_ts\"], format=\"%Y-%m-%dT%H:%M:%S\")\n", "    .dt.tz_localize(\"UTC\")\n", "    .dt.tz_convert(\"Asia/Kolkata\")\n", "    .dt.date\n", ")\n", "master[\"updated_ts\"] = update_ts_ist"]}, {"cell_type": "code", "execution_count": null, "id": "88714c95-46aa-4c17-8641-6dfe7124c1a6", "metadata": {}, "outputs": [], "source": ["columns = [\n", "    \"week_start_date\",\n", "    \"group_id\",\n", "    \"entity_id\",\n", "    \"start_ts\",\n", "    \"end_ts\",\n", "    \"week_off\",\n", "    \"updated_ts\",\n", "    \"date\",\n", "]\n", "master = master[columns]\n", "master.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "d9f20f75-7376-47fc-af9c-b465b27d8b7d", "metadata": {}, "outputs": [], "source": ["master[\"start_ts\"] = master[\"start_ts\"].astype(str)\n", "master[\"end_ts\"] = master[\"end_ts\"].astype(str)\n", "master[\"date\"] = pd.to_datetime(master[\"date\"])"]}, {"cell_type": "code", "execution_count": null, "id": "b573f983-6cda-46aa-b0b2-3fed97abf71f", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"storeops_etls\",\n", "    \"table_name\": \"planner_instore_forecasts_custom_m1\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"entity_id\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Unique identifier for the store or group\",\n", "        },\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"DATE\",\n", "            \"description\": \"Start date (Monday) of the week\",\n", "        },\n", "        {\"name\": \"date\", \"type\": \"DATE\", \"description\": \"date of the day\"},\n", "        {\n", "            \"name\": \"group_id\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"Unique identifier for the employee\",\n", "        },\n", "        {\"name\": \"start_ts\", \"type\": \"VARCHAR\", \"description\": \"Shift start timestamp\"},\n", "        {\"name\": \"end_ts\", \"type\": \"VARCHAR\", \"description\": \"Shift end timestamp\"},\n", "        {\n", "            \"name\": \"week_off\",\n", "            \"type\": \"BOOLEAN\",\n", "            \"description\": \"1 if the employee had a week off, 0 otherwise\",\n", "        },\n", "        {\"name\": \"updated_ts\", \"type\": \"VARCHAR\", \"description\": \"Record update timestamp\"},\n", "    ],\n", "    \"primary_key\": [\"entity_id\", \"group_id\", \"date\"],\n", "    \"partition_key\": [\"date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"table_description\": \"Roster-level employee scheduling and week-off tracking for each store.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "f00d1d22-198c-47c2-aae6-4bf7ee2f4e0e", "metadata": {}, "outputs": [], "source": ["pb.to_trino(data_obj=master, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "0d81d36d-57c3-489c-9a93-4c57e8878c59", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f681679c-3601-44fc-b396-699fad8631c2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "df49aa2f-7ed0-488f-bebf-31a97c8b0084", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f36cd6ae-7f51-484c-a32a-1846ca8e7485", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "02226e1e-d0d7-41d6-b7de-9a7d1eb4c517", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}