alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: historic_data_dump_dag
dag_type: etl
escalation_priority: low
execution_timeout: 120
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U07UHBUFB62
path: data_science/store_ops/etl/historic_data_dump_dag
paused: false
pool: data_science_pool
project_name: store_ops
schedule:
  end_date: '2025-07-30T00:00:00'
  interval: null
  start_date: '2025-06-20T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
