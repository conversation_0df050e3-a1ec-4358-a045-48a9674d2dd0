alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 3
dag_name: t_minus_one_actions_dag
dag_type: etl
escalation_priority: high
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebooks:
- alias: od_action
  executor_config:
    load_type: medium
    node_type: spot
  name: master_notebook
  parameters:
    dag_name: t_minus_one_actions_dag
    notebook_id: 0
  tag: group_1
- alias: underhiring_action
  executor_config:
    load_type: medium
    node_type: spot
  name: master_notebook
  parameters:
    dag_name: t_minus_one_actions_dag
    notebook_id: 1
  tag: group_2
owner:
  email: <EMAIL>
  slack_id: U07UHBUFB62
path: data_science/store_ops/etl/t_minus_one_actions_dag
paused: false
pool: data_science_pool
project_name: store_ops
schedule:
  end_date: '2025-08-31T00:00:00'
  interval: null
  start_date: '2025-06-05T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- config.py
tags: []
template_name: multi_notebook
version: 3
