{"cells": [{"cell_type": "code", "execution_count": null, "id": "1ddf6c81-a250-4f54-a1b6-c7685a6ba293", "metadata": {}, "outputs": [], "source": ["!pip install numpy==1.26.4\n", "!pip install papermill==2.3.0\n", "!pip install pandas==1.3.5\n", "!pip install awswrangler==3.9.0\n", "!pip install awscli==1.33.27\n", "!pip install pulp"]}, {"cell_type": "code", "execution_count": null, "id": "b26791f6-62fb-4c30-8c70-728385b4b391", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import papermill as pm\n", "import pandas as pd\n", "import numpy as np\n", "import itertools\n", "from datetime import datetime, timedelta, date\n", "import time\n", "import boto3\n", "import awswrangler as wr\n", "import sys\n", "import os"]}, {"cell_type": "code", "execution_count": null, "id": "37abd4cf-7bdb-4166-840b-5a035a1153d0", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["notebook_id = 0\n", "dag_name = \"t_minus_one_actions_dag\""]}, {"cell_type": "code", "execution_count": null, "id": "ae3ed4de-89ac-4cee-86ec-46b826be694b", "metadata": {}, "outputs": [], "source": ["cwd = f\"/usr/local/airflow/dags/repo/dags/data_science/store_ops/etl/{dag_name}\"\n", "sys.path.append(cwd)\n", "from config import notebook_to_script_map"]}, {"cell_type": "code", "execution_count": null, "id": "00c50c0b-63a1-40f4-876c-c0e824af5374", "metadata": {}, "outputs": [], "source": ["script_path = notebook_to_script_map[notebook_id]\n", "print(script_path)"]}, {"cell_type": "code", "execution_count": null, "id": "ce291d71-7c5f-4852-8616-4d572508798d", "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.clone_repo(\"data-science-models\", \"/tmp\", \"master\")\n", "except:\n", "    pb.send_slack_message(\n", "        channel=\"t_minus_one_actions_alert\",\n", "        text=f\"Not able to clone repo or repo already cloned!!\",\n", "    )\n", "    pass"]}, {"cell_type": "code", "execution_count": null, "id": "2f214aea-f34a-4225-9702-8ff5ce41fcb5", "metadata": {}, "outputs": [], "source": ["os.chdir(\n", "    \"/tmp/data-science-models/projects/store_ops/instore_roster/optimiser_code/t_minus_one_optimiser\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9baaa5d6-fd35-4c1c-9705-a52c82997571", "metadata": {}, "outputs": [], "source": ["try:\n", "    pm.execute_notebook(input_path=script_path, output_path=\"./output.ipynb\", parameters={})\n", "except Exception as e:\n", "    pb.send_slack_message(\n", "        channel=\"t_minus_one_actions_alert\",\n", "        text=f\"❌ ERROR in T-1 actions | notebook_id: {notebook_id} | dag_name: {dag_name} | Error: {e}\",\n", "    )"]}, {"cell_type": "markdown", "id": "67c8af2d-e705-4a5d-8d67-6210c8d1136e", "metadata": {}, "source": ["## End of script"]}, {"cell_type": "code", "execution_count": null, "id": "f1c154c3-4826-4ee0-80ad-e8d0f65ae41a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}