{"cells": [{"cell_type": "code", "execution_count": null, "id": "ce9f928c-30a9-43df-853f-047a4226b331", "metadata": {}, "outputs": [], "source": ["!pip install numpy==1.26.4\n", "!pip install papermill==2.3.0\n", "!pip install pandas==1.3.5\n", "!pip install awswrangler==3.9.0\n", "!pip install awscli==1.33.27\n", "!pip install pulp\n", "!pip install scipy\n", "!pip install ortools==9.6.2534"]}, {"cell_type": "code", "execution_count": null, "id": "0c48b169-2698-4e0f-b787-24055c91f7eb", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import papermill as pm\n", "import pandas as pd\n", "import numpy as np\n", "import itertools\n", "from datetime import datetime, timedelta, date\n", "import time\n", "import boto3\n", "import awswrangler as wr\n", "import sys\n", "import os"]}, {"cell_type": "code", "execution_count": null, "id": "023f5c72-712a-4d20-bd0e-935882ee6a42", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["notebook_id = 0\n", "dag_name = \"roster_weekly_optimiser_dag\""]}, {"cell_type": "code", "execution_count": null, "id": "d032a597-4d5d-4be0-9bb8-7c642ad14c8f", "metadata": {}, "outputs": [], "source": ["cwd = f\"/usr/local/airflow/dags/repo/dags/data_science/store_ops/etl/{dag_name}\"\n", "sys.path.append(cwd)\n", "from config import notebook_to_script_map"]}, {"cell_type": "code", "execution_count": null, "id": "3ab8f86e-13a3-4762-a446-3d9ddb8730b5", "metadata": {}, "outputs": [], "source": ["script_path = notebook_to_script_map[notebook_id]\n", "print(script_path)"]}, {"cell_type": "code", "execution_count": null, "id": "d6ae6a9a-58d7-421b-9778-a174c2aa51d7", "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.clone_repo(\"data-science-models\", \"/tmp\", \"master\")\n", "except:\n", "    pb.send_slack_message(\n", "        channel=\"roster_dag_alerts\",\n", "        text=f\"Not able to clone repo or repo already cloned!!\",\n", "    )\n", "    pass"]}, {"cell_type": "code", "execution_count": null, "id": "aca2192e-53f3-44fd-8292-37891cc30853", "metadata": {}, "outputs": [], "source": ["os.chdir(\n", "    \"/tmp/data-science-models/projects/store_ops/instore_roster/optimiser_code/weekly_optimiser\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6598b6dc-2de8-4ee2-b6ec-9f7f625deb4d", "metadata": {}, "outputs": [], "source": ["try:\n", "    pm.execute_notebook(input_path=script_path, output_path=\"./output.ipynb\", parameters={})\n", "except Exception as e:\n", "    pb.send_slack_message(\n", "        channel=\"roster_dag_alerts\",\n", "        text=f\"❌ ERROR in {dag_name} | notebook_id: {notebook_id} | script: {script_path} | Error: {e}\",\n", "    )\n", "    raise"]}, {"cell_type": "markdown", "id": "aaf10236-2723-42ee-832d-ca5a3535673d", "metadata": {}, "source": ["### End of script"]}, {"cell_type": "code", "execution_count": null, "id": "e324f05b-d3c3-4eb1-9ee8-76c636cc42ed", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}