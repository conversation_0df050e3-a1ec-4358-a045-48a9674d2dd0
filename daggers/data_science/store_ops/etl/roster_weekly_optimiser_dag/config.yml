alert_configs:
  slack:
  - channel: roster_dag_alerts
concurrency: 3
dag_name: roster_weekly_optimiser_dag
dag_type: etl
escalation_priority: high
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebooks:
- alias: optimiser_all_stores
  executor_config:
    load_type: very-high-cpu
    node_type: spot
  name: master_notebook
  parameters:
    notebook_id: 0
  retries: 1
  tag: group_1
- alias: optimiser_non_24_7_stores
  executor_config:
    load_type: very-high-cpu
    node_type: spot
  name: master_notebook
  parameters:
    notebook_id: 1
  retries: 1
  tag: group_2
- alias: merge_data
  executor_config:
    load_type: medium
    node_type: spot
  name: master_notebook
  parameters:
    notebook_id: 2
  retries: 1
  tag: group_3
- alias: optimiser_shift_start
  executor_config:
    load_type: very-high-cpu
    node_type: spot
  name: master_notebook
  parameters:
    notebook_id: 3
  retries: 1
  tag: group_4
owner:
  email: <EMAIL>
  slack_id: U07UHBUFB62
path: data_science/store_ops/etl/roster_weekly_optimiser_dag
paused: false
pool: data_science_pool
project_name: store_ops
schedule:
  end_date: '2025-09-13T00:00:00'
  interval: null
  start_date: '2025-06-17T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- config.py
tags: []
template_name: multi_notebook
version: 1
