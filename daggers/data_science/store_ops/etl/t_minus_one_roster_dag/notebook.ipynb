{"cells": [{"cell_type": "code", "execution_count": null, "id": "c2ee14fc-dc12-4b01-bb36-b973c09acc97", "metadata": {}, "outputs": [], "source": ["!pip install numpy==1.26.4\n", "!pip install papermill==2.3.0\n", "!pip install pandas==1.3.5\n", "!pip install awswrangler==3.9.0\n", "!pip install awscli==1.33.27\n", "!pip install pulp"]}, {"cell_type": "code", "execution_count": null, "id": "28ddd212-dd35-4546-9ac4-58b63a5b9044", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import papermill as pm\n", "import pandas as pd\n", "import numpy as np\n", "import itertools\n", "import math\n", "from datetime import datetime, timedelta, date\n", "from pulp import LpProblem, LpMinimize, LpVariable, lpSum, LpStatus, pulp\n", "from pulp import *\n", "import time\n", "import boto3\n", "import awswrangler as wr"]}, {"cell_type": "code", "execution_count": null, "id": "539ddd20-00dd-4b3b-9aa5-c1442cbe3959", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)\n", "pd.set_option(\"display.max_rows\", None)\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "3c4a37bd-4cfd-4d55-8fad-c5942254ea1b", "metadata": {}, "outputs": [], "source": ["def write_df_as_parquet_to_s3(data, s3_path, partition_cols=None, suffix=None):\n", "    mode = \"overwrite_partitions\" if partition_cols else \"overwrite\"\n", "    # LOGGER.info(f'writing data to s3 to path {s3_path}')\n", "\n", "    if suffix is not None:\n", "        s3_path = f\"{s3_path.strip('/')}/{suffix}\"\n", "\n", "    wr.s3.to_parquet(data, s3_path, dataset=True, mode=mode, partition_cols=partition_cols)"]}, {"cell_type": "code", "execution_count": null, "id": "bd3f4a4e-c440-40d9-be21-4960d827755a", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, conn):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, conn)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "fb09b6a4-9473-40a8-b401-8be94dabd2a2", "metadata": {}, "outputs": [], "source": ["optimizer_data_v2 = pd.read_parquet(\n", "    \"s3://prod-dse-projects/store_ops/pilot_data/current_active/input_data/\"\n", ")\n", "outlet_ids = list(set(optimizer_data_v2[\"outlet_id\"]))\n", "outlet_ids_str = \", \".join(map(str, outlet_ids))\n", "outlet_ids_str"]}, {"cell_type": "markdown", "id": "33917946-c440-466f-951e-5fb90a44e11a", "metadata": {}, "source": ["### Get latest forecasting data"]}, {"cell_type": "code", "execution_count": null, "id": "0f604f9c-485b-44c5-976c-c664417b15f4", "metadata": {}, "outputs": [], "source": ["target_date = (datetime.today() + timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "start_date = end_date = target_date\n", "start_date, end_date\n", "print(target_date)"]}, {"cell_type": "code", "execution_count": null, "id": "921988ee-61b7-4fca-a0c9-dd2241290d02", "metadata": {}, "outputs": [], "source": ["festival_df = pd.read_parquet(\"s3://prod-dse-projects/store_ops/extra_data/festival_data_2025/\")\n", "festival_df[\"checkout_date\"] = pd.to_datetime(festival_df[\"checkout_date\"])\n", "anomaly_dates = list(\n", "    festival_df[(festival_df.run_up == 1) & (festival_df.date_is_event == 1)][\n", "        \"checkout_date\"\n", "    ].astype(str)\n", ")\n", "anomaly_dates = anomaly_dates + [\"2025-03-14\"]\n", "len(anomaly_dates)"]}, {"cell_type": "code", "execution_count": null, "id": "1882792b-32cd-4b3d-8fc4-0a6a9d2747e5", "metadata": {}, "outputs": [], "source": ["forecast_query = \"\"\"\n", "WITH raw AS (\n", "    SELECT DISTINCT\n", "        date AS order_date,\n", "        DATE(updated_on) AS updated_on,\n", "        outletid AS outlet_id,\n", "        carts AS forecast_orders,\n", "        ipc AS ipo,\n", "        ROW_NUMBER() OVER (PARTITION BY outletid, date ORDER BY updated_on DESC) AS rk\n", "    FROM logistics_data_etls.cart_projections l\n", "    WHERE date >= DATE('{start_date}')\n", "      AND date <= DATE('{end_date}')\n", "      AND DATE(updated_on) <= date - INTERVAL '1' DAY\n", "      AND outletid IN ({outlet_ids_str})\n", ")\n", "SELECT DISTINCT\n", "    order_date, updated_on, outlet_id, forecast_orders, ipo\n", "FROM raw\n", "WHERE rk = 1\n", "ORDER BY 1\n", "\"\"\"\n", "\n", "hitoric_hour_demand_query = \"\"\"\n", "\n", " select order_create_dt_ist as order_date, \n", " substr(cast(order_schedule_ts_ist as varchar),12,2) as hour ,\n", " outlet_id,\n", "sum(total_product_quantity) as hourly_orders\n", "from dwh.fact_sales_order_details\n", " where order_create_dt_ist >=  DATE('{start_date}') - interval '60' day\n", " and  order_create_dt_ist <=  DATE('{start_date}') - interval '8' day\n", " and outlet_id IN ({outlet_ids_str})\n", " group by 1,2,3\n", " order by 1,2\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "ceceb429-b586-409f-8291-c122ab3270ae", "metadata": {}, "outputs": [], "source": ["def get_forecast_demand_data():\n", "\n", "    print(\"getting forecasting data.....\")\n", "    forecast_df = read_sql_query(\n", "        forecast_query.format(\n", "            start_date=target_date, end_date=target_date, outlet_ids_str=outlet_ids_str\n", "        ),\n", "        CON_TRINO,\n", "    )\n", "    # display(forecast_df.head())\n", "    print(f\"total outlet_ids:{forecast_df.outlet_id.nunique()}\")\n", "    print(f\"total date:{forecast_df.order_date.nunique()}\")\n", "    print(f\"updated_on :{forecast_df.updated_on.unique()}\")\n", "    forecast_df[\"forecast_total_items\"] = np.round(\n", "        forecast_df[\"forecast_orders\"] * forecast_df[\"ipo\"]\n", "    )\n", "    return forecast_df\n", "\n", "\n", "def get_hostoric_hourly_share():\n", "\n", "    print(\"getting historic hourly share....\")\n", "    hour_df = read_sql_query(\n", "        hitoric_hour_demand_query.format(\n", "            start_date=start_date, end_date=end_date, outlet_ids_str=outlet_ids_str\n", "        ),\n", "        CON_TRINO,\n", "    )\n", "    hour_df[\"day_of_week\"] = pd.to_datetime(hour_df[\"order_date\"]).dt.day_name()\n", "    total_order_df = (\n", "        hour_df.groupby([\"outlet_id\", \"order_date\"])\n", "        .agg(total_daily_order=(\"hourly_orders\", sum))\n", "        .reset_index()\n", "    )\n", "    hour_df = hour_df.merge(total_order_df)\n", "    hour_df[\"hourly_share\"] = np.round(hour_df[\"hourly_orders\"] / hour_df[\"total_daily_order\"], 4)\n", "    hour_df = hour_df[~(hour_df.order_date.isin(anomaly_dates))]  # removing anomaly dates\n", "    hour_df = hour_df.sort_values(\"order_date\", ascending=False)\n", "    temp_df = hour_df.groupby([\"outlet_id\", \"hour\", \"day_of_week\"]).head(4).reset_index()\n", "    agg_df = (\n", "        temp_df.groupby([\"outlet_id\", \"hour\", \"day_of_week\"])[\"hourly_share\"].median().reset_index()\n", "    )\n", "    return agg_df\n", "\n", "\n", "def get_forecasted_hourly_share():\n", "\n", "    print(\"getting forecasted hourly share....\")\n", "    final_df = get_forecast_demand_data()\n", "    final_df[\"day_of_week\"] = pd.to_datetime(final_df[\"order_date\"]).dt.day_name()\n", "    agg_df = get_hostoric_hourly_share()\n", "    final_df = final_df.merge(\n", "        agg_df[[\"outlet_id\", \"hour\", \"day_of_week\", \"hourly_share\"]],\n", "        on=[\"outlet_id\", \"day_of_week\"],\n", "        how=\"left\",\n", "    )\n", "    final_df[\"hourly_share\"] = final_df[\"hourly_share\"].fillna(0)\n", "    final_df[\"forecast_hourly_item\"] = np.round(\n", "        final_df[\"forecast_total_items\"] * final_df[\"hourly_share\"]\n", "    )\n", "    final_df = final_df.sort_values([\"order_date\", \"hour\"])\n", "    return final_df\n", "\n", "\n", "def get_final_data():\n", "\n", "    final_df = get_forecasted_hourly_share()\n", "    # print(final_df.head())\n", "    print(\"postprocessing...\")\n", "    final_df = final_df[[\"order_date\", \"hour\", \"outlet_id\", \"forecast_hourly_item\"]].rename(\n", "        columns={\n", "            \"order_date\": \"checkout_date\",\n", "            \"forecast_hourly_item\": \"forecast_hourly_item_latest\",\n", "        }\n", "    )\n", "    final_df[\"hour\"] = final_df[\"hour\"].astype(int)\n", "\n", "    sdate = datetime.strptime(start_date, \"%Y-%m-%d\")\n", "    edate = datetime.strptime(end_date, \"%Y-%m-%d\")\n", "    date_list = [\n", "        (sdate + timedelta(days=i)).strftime(\"%Y-%m-%d\") for i in range((edate - sdate).days + 1)\n", "    ]\n", "    hour_list = [i for i in range(0, 24)]\n", "\n", "    date_df = pd.DataFrame()\n", "    date_df[\"checkout_date\"] = date_list\n", "    hour_df = pd.DataFrame()\n", "    hour_df[\"hour\"] = hour_list\n", "    outletid_df = pd.DataFrame()\n", "    outletid_df[\"outlet_id\"] = outlet_ids\n", "    master_df = date_df.merge(hour_df, how=\"cross\")\n", "    master_df = master_df.merge(outletid_df, how=\"cross\")\n", "\n", "    save_df = master_df.merge(final_df, how=\"left\")\n", "    save_df[[\"forecast_hourly_item_latest\"]] = save_df[[\"forecast_hourly_item_latest\"]].fillna(0)\n", "    return save_df"]}, {"cell_type": "code", "execution_count": null, "id": "42ca642f-4205-4a9e-92f8-d46af99ccbe3", "metadata": {}, "outputs": [], "source": ["demand_df = get_final_data()"]}, {"cell_type": "code", "execution_count": null, "id": "0cef050a-818e-404e-8a84-9e50859fd7f1", "metadata": {}, "outputs": [], "source": ["demand_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b8de20d3-f834-4c68-9222-164d3e56d6ee", "metadata": {}, "outputs": [], "source": ["demand_df[\"order_date\"] = pd.to_datetime(demand_df[\"checkout_date\"])\n", "demand_df = demand_df.drop([\"checkout_date\"], axis=1)"]}, {"cell_type": "markdown", "id": "98e66886-e2f1-421d-a560-61411c4267b9", "metadata": {}, "source": ["### getting PPI change data\n"]}, {"cell_type": "code", "execution_count": null, "id": "d69b898a-bdcb-438f-be95-ca2971357b1e", "metadata": {}, "outputs": [], "source": ["curr_date = datetime.now()\n", "\n", "sdate = (curr_date - timedelta(days=2)).strftime(\"%Y-%m-%d\")\n", "sdate2 = (curr_date - timedelta(days=100)).strftime(\"%Y-%m-%d\")\n", "edate = (curr_date - timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "edate2 = (curr_date - timedelta(days=3)).strftime(\"%Y-%m-%d\")\n", "print(sdate, edate)"]}, {"cell_type": "code", "execution_count": null, "id": "6b28f0f0-eac3-4e1f-b9c2-871681c346bb", "metadata": {}, "outputs": [], "source": ["ft = \"'Captain','Fnv Executive','<PERSON> Picker','Assistant','Captain ERT','Inventory Executive','Captain O<PERSON>'\"\n", "od = \"'Captain OD'\"\n", "emp_set = ft + \",\" + od"]}, {"cell_type": "code", "execution_count": null, "id": "e213990b-4c8c-4d8e-99c4-ec574870bd89", "metadata": {}, "outputs": [], "source": ["input_metric_query = \"\"\"\n", "\n", "WITH base AS (\n", "    SELECT \n", "        date_ AS order_date, \n", "        city_name, \n", "        outlet_id, \n", "        outlet_name, \n", "        TRY(SUM(picking_time) * 1.00 / SUM(total_quantity_picked)) AS ppi\n", "    FROM storeops_etls.employee_metrics_hourly\n", "    WHERE date_ BETWEEN DATE('{start_date}') AND DATE('{end_date}')\n", "        AND designation IN ({emp_type})\n", "        AND total_quantity_picked >= 50\n", "    GROUP BY 1, 2, 3, 4\n", ")\n", "\n", "SELECT \n", "    order_date,  \n", "    outlet_id, \n", "    ppi\n", "FROM base\n", "WHERE outlet_id IN ({outlet_ids_str})\n", "\n", "    \n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "126732ba-9fc4-47ae-abaf-e948a88f55ec", "metadata": {}, "outputs": [], "source": ["input_metric_ft_hr_raw = read_sql_query(\n", "    input_metric_query.format(\n", "        start_date=sdate2, end_date=edate2, emp_type=emp_set, outlet_ids_str=outlet_ids_str\n", "    ),\n", "    CON_TRINO,\n", ")\n", "input_metric_ft_hr = read_sql_query(\n", "    input_metric_query.format(\n", "        start_date=sdate, end_date=edate, emp_type=emp_set, outlet_ids_str=outlet_ids_str\n", "    ),\n", "    CON_TRINO,\n", ")\n", "planned_ppi = pd.read_parquet(\n", "    \"s3://prod-dse-projects/store_ops/pilot_data/current_active/metric_data/\"\n", ")\n", "planned_ppi = planned_ppi[[\"outlet_id\", \"planned_store_ppi\"]]\n", "planned_ppi.head()"]}, {"cell_type": "code", "execution_count": null, "id": "cd888198-17ca-4d43-9617-2dd304c1d11c", "metadata": {}, "outputs": [], "source": ["agg_df = (\n", "    input_metric_ft_hr.groupby(\"outlet_id\")\n", "    .agg(\n", "        median_ppi=(\"ppi\", \"median\"),\n", "        latest_order_date=(\"order_date\", \"max\"),  # Get the latest order date\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "# Merge to get the ppi corresponding to the latest order_date\n", "agg_df = agg_df.merge(\n", "    input_metric_ft_hr,\n", "    left_on=[\"outlet_id\", \"latest_order_date\"],\n", "    right_on=[\"outlet_id\", \"order_date\"],\n", "    how=\"left\",\n", ")\n", "\n", "# Rename column for clarity\n", "agg_df = agg_df.rename(columns={\"ppi\": \"ppi_latest_date\"}).drop(columns=[\"order_date\"])\n", "\n", "\n", "input_metric_ft_hr = agg_df\n", "# input_metric_ft_hr = input_metric_ft_hr.groupby(\"outlet_id\").agg(median_ppi=(\"ppi\", \"median\")).reset_index()\n", "input_metric_ft_hr[\"median_ppi\"] = np.where(\n", "    input_metric_ft_hr[\"median_ppi\"] > input_metric_ft_hr[\"ppi_latest_date\"],\n", "    input_metric_ft_hr[\"ppi_latest_date\"],\n", "    input_metric_ft_hr[\"median_ppi\"],\n", ")\n", "input_metric_ft_hr.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ddb0d88a-19c6-477f-861c-07dab9769dce", "metadata": {}, "outputs": [], "source": ["delta = pd.merge(planned_ppi, input_metric_ft_hr, on=[\"outlet_id\"], how=\"left\")\n", "delta[\"median_ppi\"] = np.where(\n", "    delta[\"median_ppi\"].isnull(), delta[\"planned_store_ppi\"], delta[\"median_ppi\"]\n", ")\n", "\n", "delta[\"per_factor\"] = (delta[\"median_ppi\"] - delta[\"planned_store_ppi\"]) / delta[\n", "    \"planned_store_ppi\"\n", "]\n", "delta[\"per_factor_final\"] = np.where(\n", "    delta[\"per_factor\"] > 0.1, 1 + round(delta[\"per_factor\"], 2), 1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "00757ecf-c236-4016-bb20-68c6b2072398", "metadata": {}, "outputs": [], "source": ["input_metric_ft_hr_raw = input_metric_ft_hr_raw.sort_values(by=[\"outlet_id\", \"order_date\"])\n", "input_metric_ft_hr_raw[\"next_ppi\"] = input_metric_ft_hr_raw.groupby(\"outlet_id\")[\"ppi\"].shift(-1)"]}, {"cell_type": "code", "execution_count": null, "id": "4c3c1759-01c1-43e4-9038-dc93d121e4c9", "metadata": {}, "outputs": [], "source": ["temp = pd.merge(delta, input_metric_ft_hr_raw, on=[\"outlet_id\"], how=\"left\")\n", "\n", "temp[\"target_delta\"] = abs(temp[\"median_ppi\"] - temp[\"ppi\"]) / temp[\"median_ppi\"]\n", "temp[\"row_number\"] = temp.groupby(\"outlet_id\")[\"target_delta\"].rank(method=\"first\").astype(int)\n", "temp = temp[temp.row_number <= 5].sort_values(by=[\"outlet_id\", \"row_number\"])\n", "temp[\"improvement\"] = (temp[\"next_ppi\"] - temp[\"ppi\"]) / temp[\"ppi\"]\n", "\n", "temp = (\n", "    temp.groupby([\"outlet_id\", \"planned_store_ppi\", \"median_ppi\", \"per_factor\", \"per_factor_final\"])\n", "    .agg(improvement=(\"improvement\", \"mean\"))\n", "    .reset_index()\n", ")\n", "\n", "# temp['improvement'] = np.where(temp['improvement']<0,temp['improvement'],0)\n", "temp[\"improvement\"] = np.where(temp[\"improvement\"] < (-0.20), temp[\"improvement\"], -0.20)\n", "temp[\"median_ppi_u\"] = temp[\"planned_store_ppi\"] + (\n", "    (temp[\"median_ppi\"] - temp[\"planned_store_ppi\"]) * (1 + temp[\"improvement\"])\n", ")\n", "temp[\"median_ppi_u\"] = np.where(temp[\"per_factor_final\"] > 1, temp[\"median_ppi_u\"], 0)\n", "temp[\"median_ppi\"] = np.where(\n", "    temp[\"per_factor_final\"] > 1, temp[\"median_ppi_u\"], temp[\"median_ppi\"]\n", ")\n", "\n", "temp[\"per_factor\"] = (temp[\"median_ppi\"] - temp[\"planned_store_ppi\"]) / temp[\"planned_store_ppi\"]\n", "temp[\"per_factor_final\"] = np.where(temp[\"per_factor\"] > 0.1, 1 + round(temp[\"per_factor\"], 2), 1)"]}, {"cell_type": "code", "execution_count": null, "id": "b9fb84e6-d1ce-4c2c-8edd-afd1317662ef", "metadata": {}, "outputs": [], "source": ["ppi_df = temp[[\"outlet_id\", \"planned_store_ppi\", \"median_ppi\", \"per_factor_final\"]]\n", "ppi_df.columns = [\"outlet_id\", \"planned_ppi_t5\", \"median_ppi_past_2_day\", \"factor_to_multiply_t1\"]\n", "ppi_df = ppi_df[[\"outlet_id\", \"factor_to_multiply_t1\", \"median_ppi_past_2_day\"]]\n", "ppi_df.head()"]}, {"cell_type": "markdown", "id": "4144fa2a-3ce0-4c30-872b-f5f68f41157e", "metadata": {}, "source": ["### Merging it with main data"]}, {"cell_type": "code", "execution_count": null, "id": "0cbb89b3-c29d-4788-bc6f-95ed300bab02", "metadata": {}, "outputs": [], "source": ["optimizer_data_v2 = optimizer_data_v2.merge(ppi_df, on=[\"outlet_id\"])\n", "optimizer_data_v2[\"factor_to_multiply_t1\"] = np.where(\n", "    (optimizer_data_v2[\"order_date\"] == target_date)\n", "    & (optimizer_data_v2[\"hour\"] >= 8)\n", "    & (optimizer_data_v2[\"hour\"] < 22),\n", "    optimizer_data_v2[\"factor_to_multiply_t1\"],\n", "    1,\n", ")\n", "optimizer_data_v2[\"mavg_ppi_new\"] = np.where(\n", "    (optimizer_data_v2[\"hour\"] >= 8)\n", "    & (optimizer_data_v2[\"hour\"] < 22)\n", "    & (optimizer_data_v2[\"order_date\"] == target_date),\n", "    optimizer_data_v2[\"mavg_ppi\"] * optimizer_data_v2[\"factor_to_multiply_t1\"],\n", "    optimizer_data_v2[\"mavg_ppi\"],\n", ")\n", "optimizer_data_v2[\"mavg_ppi_new\"] = np.where(\n", "    (optimizer_data_v2[\"mavg_ppi_new\"] > optimizer_data_v2[\"median_ppi_past_2_day\"])\n", "    & (optimizer_data_v2[\"hour\"] >= 8)\n", "    & (optimizer_data_v2[\"hour\"] < 22)\n", "    & (optimizer_data_v2[\"order_date\"] == target_date),\n", "    optimizer_data_v2[\"median_ppi_past_2_day\"],\n", "    optimizer_data_v2[\"mavg_ppi_new\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4ecfd4d5-4207-41c4-9a59-a410b56c405f", "metadata": {}, "outputs": [], "source": ["optimizer_data_v2 = optimizer_data_v2.merge(\n", "    demand_df, on=[\"order_date\", \"hour\", \"outlet_id\"], how=\"left\"\n", ")\n", "optimizer_data_v2[[\"forecast_hourly_item_latest\"]] = optimizer_data_v2[\n", "    [\"forecast_hourly_item_latest\"]\n", "].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "c8b7ee59-f757-450a-81a2-528ea294bd50", "metadata": {}, "outputs": [], "source": ["pilot_hourly_df = pd.read_parquet(\n", "    \"s3://prod-dse-projects/store_ops/pilot_data/current_active/hourly_data/\"\n", ")[\n", "    [\n", "        \"order_date\",\n", "        \"outlet_id\",\n", "        \"hour\",\n", "        \"fixed_mh_allocated\",\n", "        \"fixed_mh\",\n", "        \"od_mh\",\n", "        \"pckg_mh_allocation\",\n", "        \"employee_starting_shift\",\n", "    ]\n", "]\n", "pilot_hourly_df[\"fixed_mh_final\"] = pilot_hourly_df[\"fixed_mh\"]\n", "pilot_hourly_df[\"fixed_mh\"] = pilot_hourly_df[\"fixed_mh_allocated\"]\n", "pilot_hourly_df[\"order_date\"] = pd.to_datetime(pilot_hourly_df[\"order_date\"])\n", "pilot_hourly_df = pilot_hourly_df.rename(columns={\"employee_starting_shift\": \"shift_start_final\"})\n", "pilot_hourly_df = pilot_hourly_df.drop(columns=[\"fixed_mh_allocated\"])\n", "pilot_hourly_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4ba06292-928a-45fd-ab6a-e64a1446f2bc", "metadata": {}, "outputs": [], "source": ["optimizer_data_v2 = optimizer_data_v2.merge(\n", "    pilot_hourly_df, on=[\"order_date\", \"outlet_id\", \"hour\"], how=\"left\"\n", ")\n", "optimizer_data_v2[\"qty_to_pick\"] = np.where(\n", "    optimizer_data_v2[\"fixed_mh_final\"].isnull(), 0, optimizer_data_v2[\"qty_to_pick\"]\n", ")\n", "optimizer_data_v2[\"to_remove\"] = np.where(optimizer_data_v2[\"fixed_mh_final\"].isnull(), 1, 0)\n", "\n", "optimizer_data_v2 = optimizer_data_v2.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "720921b3-90f0-49f8-befe-2de66db2833c", "metadata": {}, "outputs": [], "source": ["optimizer_data_v2.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "df3a2dca-ff42-4db1-bc4e-b350511a12ec", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "be6f8ca8-8caf-45dc-b2be-c84d381b709e", "metadata": {}, "outputs": [], "source": ["optimizer_data_v2[\"od_limit\"] = np.where(\n", "    optimizer_data_v2[\"order_date\"] == target_date,\n", "    optimizer_data_v2[\"od_mh\"],\n", "    optimizer_data_v2[\"od_mh\"],\n", ")\n", "optimizer_data_v2[\"fixed_limit\"] = optimizer_data_v2[\"fixed_mh\"]\n", "optimizer_data_v2[\"qty_to_pick_new\"] = np.where(\n", "    (optimizer_data_v2[\"order_date\"] == target_date)\n", "    & (optimizer_data_v2.hour >= 8)\n", "    & (optimizer_data_v2.hour < 22),\n", "    np.maximum(optimizer_data_v2[\"forecast_hourly_item_latest\"], optimizer_data_v2[\"qty_to_pick\"]),\n", "    optimizer_data_v2[\"qty_to_pick\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "491b12dc-8346-424e-a24a-71b7fe7f9d99", "metadata": {}, "outputs": [], "source": ["optimizer_data_v2[\"opm_avg_raw_new\"] = np.round(\n", "    optimizer_data_v2[\"qty_to_pick_new\"] / (60 * optimizer_data_v2[\"avg_ipo\"]), 2\n", ")\n", "optimizer_data_v2[\"opm_avg_raw_new\"] = optimizer_data_v2[\"opm_avg_raw_new\"].fillna(0)\n", "optimizer_data_v2[\"opm_avg_new\"] = optimizer_data_v2[\"opm_avg_raw_new\"] + (\n", "    optimizer_data_v2[\"opm_avg_raw_new\"] * 32 / 100\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "317361ba-05fe-432a-a817-d8895fc0c344", "metadata": {}, "outputs": [], "source": ["def erlang_c(c, lambda_s, mu):\n", "    \"\"\"\n", "    Compute the Erlang C probability that an order has to wait.\n", "\n", "    Parameters:\n", "        c (int): Number of employees (servers).\n", "        lambda_s (float): Arrival rate (orders per second).\n", "        mu (float): Service rate per employee (orders per second).\n", "\n", "    Returns:\n", "        float: The probability that an arriving order must wait.\n", "    \"\"\"\n", "    rho = lambda_s / (c * mu)\n", "    if rho >= 1:\n", "        return 1.0  # System is unstable if utilization >= 100%\n", "    numerator = (c * rho) ** c / (math.factorial(c) * (1 - rho))\n", "    denominator = sum((c * rho) ** n / math.factorial(n) for n in range(c)) + numerator\n", "    return numerator / denominator\n", "\n", "\n", "def overall_probability(c, lambda_s, mu, threshold=10):\n", "    \"\"\"\n", "    Compute the overall probability that an order is assigned within the threshold (seconds).\n", "\n", "    Parameters:\n", "        c (int): Number of employees.\n", "        lambda_s (float): Arrival rate (orders/second).\n", "        mu (float): Service rate per employee (orders/second).\n", "        threshold (float): Time threshold in seconds.\n", "\n", "    Returns:\n", "        float: Overall probability that an order is assigned within 'threshold' seconds.\n", "    \"\"\"\n", "    P_wait = erlang_c(c, lambda_s, mu)\n", "    p_if_wait = 1 - math.exp(-(c * mu - lambda_s) * threshold)\n", "    return (1 - P_wait) + P_wait * p_if_wait\n", "\n", "\n", "def compute_required_employees(row):\n", "    \"\"\"\n", "    Given a row with service parameters, compute the required number of employees\n", "    and corresponding manhours to meet the R2A target.\n", "\n", "    Expected row columns:\n", "        - 'opm': Design orders per minute (e.g., 90th percentile)\n", "        - 'IPO': Average Items per Order\n", "        - 'PPI': Picking Time per Item (in seconds)\n", "        - 'R2A_target': R2A target (e.g., 0.85 for 85%)\n", "        - 'threshold': Waiting time threshold in seconds (e.g., 10)\n", "        - 'util_target': Maximum utilization target (e.g., 0.55)\n", "\n", "    Returns:\n", "        pandas.Series: A series with 'required_employees' and 'manhours'\n", "    \"\"\"\n", "\n", "    opm_avg = row[\"opm_avg_raw\"]\n", "    opm_design = row[\"opm_avg_new\"]\n", "    IPO = row[\"avg_ipo\"]\n", "    PPI = row[\"mavg_ppi_new\"]\n", "    R2A_target = 0.85\n", "    threshold = 10\n", "    util_target = row[\"mavg_pick_util\"]\n", "\n", "    # Derived Parameters\n", "    lambda_s = opm_design / 60.0\n", "    service_time = IPO * PPI\n", "    if (lambda_s == 0) | (service_time == 0):\n", "        return pd.Series({\"mh_required_for_pick_qt\": 0})\n", "    mu = 1 / service_time\n", "\n", "    if lambda_s == 0:\n", "        return pd.Series({\"mh_required_for_pick_qt\": 0})\n", "\n", "    # Minimum number of employees based on utilization constraint:\n", "    c_min_util = math.ceil((opm_avg / 60) / (util_target * mu))\n", "\n", "    # Search for the minimum number of employees 'c' that meets the R2A target\n", "    required_c = None\n", "    for c in range(c_min_util, 100):\n", "        # Check system stability: must have c*mu > lambda_s\n", "        if c * mu <= lambda_s:\n", "            continue\n", "        p = overall_probability(c, lambda_s, mu, threshold)\n", "        if p >= R2A_target:\n", "            required_c = c\n", "            break\n", "\n", "    return pd.Series({\"mh_required_for_pick_qt\": required_c})"]}, {"cell_type": "code", "execution_count": null, "id": "2123934c-76e7-4c41-a871-e0a3079efac8", "metadata": {}, "outputs": [], "source": ["optimizer_data_v2[\"mh_required_for_pick_qt_new\"] = optimizer_data_v2.apply(\n", "    compute_required_employees, axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ac9a8267-710d-4dd1-98c3-ac32d8d7cc2a", "metadata": {}, "outputs": [], "source": ["optimizer_data_v2 = optimizer_data_v2[\n", "    [\n", "        \"order_date\",\n", "        \"hour\",\n", "        \"outlet_id\",\n", "        \"qty_to_pick\",\n", "        \"perishable_qty_accumulated_final\",\n", "        \"packaged_qty_accumulated_final\",\n", "        \"mavg_ppi\",\n", "        \"mavg_pick_util\",\n", "        \"mavg_iph_packaged\",\n", "        \"mavg_iph_perishable\",\n", "        \"ppi_od\",\n", "        \"od_util\",\n", "        \"od_eph\",\n", "        \"fixed_eph\",\n", "        \"od_prod\",\n", "        \"od_limit\",\n", "        \"absent_predict\",\n", "        \"fixed_limit\",\n", "        \"qty_to_pick_new\",\n", "        \"mh_limit\",\n", "        \"mavg_ppi_new\",\n", "        \"mh_required_for_pick_qt_new\",\n", "        \"mh_required_for_pick_qt\",\n", "        \"grn_difficulty_flag\",\n", "        \"pckg_mh_allocation\",\n", "        \"perishable_frozen_qty\",\n", "        \"fixed_mh_final\",\n", "        \"shift_start_final\",\n", "        \"to_remove\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "e79c5eba-cb96-433e-a111-db0dcec8c912", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2eafdd65-899d-469e-966f-62098ac636ff", "metadata": {}, "outputs": [], "source": ["optimizer_data_v2[\"fixed_limit\"] = optimizer_data_v2[\"fixed_limit\"].fillna(0)\n", "optimizer_data_v2[\"pckg_mh_allocation\"] = optimizer_data_v2[\"pckg_mh_allocation\"].fillna(0)\n", "optimizer_data_v2[\"order_date\"] = pd.to_datetime(optimizer_data_v2[\"order_date\"])\n", "optimizer_data_v2[\"hour\"] = optimizer_data_v2[\"hour\"].astype(int)\n", "optimizer_data_v2[\"qty_to_pick\"] = optimizer_data_v2[\"qty_to_pick\"].astype(int)\n", "optimizer_data_v2[\"packaged_qty_accumulated_final\"] = (\n", "    optimizer_data_v2[\"packaged_qty_accumulated_final\"].fillna(0).astype(int)\n", ")\n", "optimizer_data_v2[\"mavg_pick_util\"] = optimizer_data_v2[\"mavg_pick_util\"].astype(float)\n", "optimizer_data_v2[\"perishable_qty_accumulated_final\"] = (\n", "    optimizer_data_v2[\"perishable_qty_accumulated_final\"].fillna(0).astype(int)\n", ")\n", "optimizer_data_v2[\"mavg_ppi\"] = optimizer_data_v2[\"mavg_ppi\"].astype(float)\n", "optimizer_data_v2[\"mavg_iph_packaged\"] = optimizer_data_v2[\"mavg_iph_packaged\"].astype(float)\n", "optimizer_data_v2[\"mavg_iph_perishable\"] = optimizer_data_v2[\"mavg_iph_perishable\"].astype(float)\n", "optimizer_data_v2[\"od_limit\"] = optimizer_data_v2[\"od_limit\"].astype(int)\n", "optimizer_data_v2[\"fixed_limit\"] = optimizer_data_v2[\"fixed_limit\"].astype(int)\n", "optimizer_data_v2[\"mavg_ppi_new\"] = optimizer_data_v2[\"mavg_ppi_new\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "463a4690-941d-40da-be1d-a6c5d4796522", "metadata": {}, "outputs": [], "source": ["assert optimizer_data_v2.isna().sum().sum() == 0, \"NUll value error\""]}, {"cell_type": "code", "execution_count": null, "id": "017f61e1-6c4c-4ff8-ae17-f2d6b7e88614", "metadata": {}, "outputs": [], "source": ["big_M = 100\n", "pkg_window = 6\n", "skipped_stores = []\n", "output1_final = pd.DataFrame()\n", "output2_final = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "id": "03fd40c4-73bf-4710-a01a-2999ee137b5a", "metadata": {}, "outputs": [], "source": ["time_df = pd.DataFrame(columns=[\"outlet_id\", \"vol_model_time\", \"od_model_time\"])\n", "time_df"]}, {"cell_type": "code", "execution_count": null, "id": "db247d37-30c8-4f1f-b917-a1fe6debb0ce", "metadata": {}, "outputs": [], "source": ["optimizer_data_v2 = optimizer_data_v2[optimizer_data_v2[\"outlet_id\"].isin(outlet_ids)]\n", "store_list = list(optimizer_data_v2[\"outlet_id\"].drop_duplicates())"]}, {"cell_type": "code", "execution_count": null, "id": "215a4c33-b8d2-4b66-a0ba-19e93066f525", "metadata": {}, "outputs": [], "source": ["def data_preprocess(df):\n", "\n", "    df = df.copy()\n", "    grn_difficulty_flag = df[\"grn_difficulty_flag\"].max()\n", "    print()\n", "\n", "    df = df.sort_values(by=[\"order_date\", \"hour\"])\n", "\n", "    ## picking manpower required\n", "    df[\"mh_required_for_pick\"] = (df[\"qty_to_pick\"] * df[\"mavg_ppi\"]) / (\n", "        df[\"mavg_pick_util\"] * 3600\n", "    )\n", "    df[\"mh_required_for_pick_new\"] = (df[\"qty_to_pick_new\"] * df[\"mavg_ppi_new\"]) / (\n", "        df[\"mavg_pick_util\"] * 3600\n", "    )\n", "\n", "    ## perishable manpower required\n", "    df[\"mh_required_for_perishable_putaway\"] = (\n", "        df[\"perishable_frozen_qty\"] / df[\"mavg_iph_perishable\"]\n", "    )\n", "\n", "    ##unloading preishable mpower required\n", "    df[\"mh_required_for_perishable_putaway_unloading\"] = (\n", "        ((df[\"perishable_qty_accumulated_final\"] / 50) * 50) / 3600\n", "    ) / 1.5\n", "\n", "    ##unloading packaged mpower required\n", "    df[\"pkg_unloading_mh\"] = (((df[\"packaged_qty_accumulated_final\"] / 25) * 50) / 3600) / 1.5\n", "\n", "    if grn_difficulty_flag == 0:\n", "        df[\"pkg_unloading_mh_66\"] = df[\"pkg_unloading_mh\"] * 0.50\n", "        df[\"pkg_unloading_mh_33\"] = df[\"pkg_unloading_mh\"] * 0.50\n", "        df[\"pkg_unloading_mh_spillover\"] = df[\"pkg_unloading_mh_33\"].shift(1)\n", "        df[\"pkg_unloading_mh_spillover\"] = df[\"pkg_unloading_mh_spillover\"].fillna(0)\n", "        df[\"mh_required_for_packaged_putaway_unloading\"] = (\n", "            df[\"pkg_unloading_mh_66\"] + df[\"pkg_unloading_mh_spillover\"]\n", "        )\n", "\n", "        #### removing unloading mh during night\n", "\n", "        df[\"mh_required_for_packaged_putaway_unloading_plus_1\"] = df[\"pkg_unloading_mh_33\"].shift(1)\n", "        df[\"mh_required_for_packaged_putaway_unloading_plus_2\"] = df[\"pkg_unloading_mh_66\"].shift(2)\n", "        df[\"mh_required_for_packaged_putaway_unloading_plus_3\"] = df[\"pkg_unloading_mh_33\"].shift(3)\n", "        df[\"mh_required_for_packaged_putaway_unloading_minus_1\"] = df[\"pkg_unloading_mh_66\"].shift(\n", "            -1\n", "        )\n", "        df[\"mh_required_for_packaged_putaway_unloading_minus_2\"] = df[\"pkg_unloading_mh_66\"].shift(\n", "            -2\n", "        )\n", "    else:\n", "        df[\"mh_required_for_packaged_putaway_unloading\"] = df[\"pkg_unloading_mh\"] + (\n", "            df[\"pkg_unloading_mh\"] > 0\n", "        ).astype(int)\n", "\n", "        df[\"mh_required_for_packaged_putaway_unloading_plus_1\"] = (\n", "            df[\"pkg_unloading_mh\"].shift(1) * 0.66\n", "        )\n", "        df[\"mh_required_for_packaged_putaway_unloading_plus_2\"] = (\n", "            df[\"pkg_unloading_mh\"].shift(2) * 0.66\n", "        )\n", "        df[\"mh_required_for_packaged_putaway_unloading_plus_3\"] = 0\n", "        df[\"mh_required_for_packaged_putaway_unloading_minus_1\"] = (\n", "            df[\"pkg_unloading_mh\"].shift(-1) * 0.66\n", "        )\n", "        df[\"mh_required_for_packaged_putaway_unloading_minus_2\"] = (\n", "            df[\"pkg_unloading_mh\"].shift(-2) * 0.66\n", "        )\n", "\n", "    df[\"mh_required_for_perishable_putaway_unloading\"] = np.where(\n", "        (df.hour >= 23) | (df.hour <= 4), 0, df[\"mh_required_for_perishable_putaway_unloading\"]\n", "    )\n", "    df[\"mh_required_for_packaged_putaway_unloading\"] = np.where(\n", "        (df.hour >= 23) | (df.hour <= 4), 0, df[\"mh_required_for_packaged_putaway_unloading\"]\n", "    )\n", "\n", "    df[\"absent_predict\"] = np.where((df[\"hour\"] >= 23) | (df[\"hour\"] <= 5), 0, df[\"absent_predict\"])\n", "\n", "    for col in [\n", "        \"mh_required_for_packaged_putaway_unloading_plus_1\",\n", "        \"mh_required_for_packaged_putaway_unloading_plus_2\",\n", "        \"mh_required_for_packaged_putaway_unloading_minus_1\",\n", "        \"mh_required_for_packaged_putaway_unloading_minus_2\",\n", "        \"mh_required_for_packaged_putaway_unloading_plus_3\",\n", "    ]:\n", "        df[col] = df[col].fillna(0)\n", "\n", "    df[\"buffer\"] = (\n", "        df[\"mh_required_for_packaged_putaway_unloading_plus_1\"]\n", "        + df[\"mh_required_for_packaged_putaway_unloading_minus_1\"]\n", "        + df[\"mh_required_for_packaged_putaway_unloading_plus_2\"]\n", "    )\n", "\n", "    df = df.sort_values([\"order_date\", \"hour\"])\n", "    df[\"row_number\"] = range(1, len(df) + 1)\n", "\n", "    ## Final perishable Putaway mh required\n", "\n", "    def compute_putaway(row):\n", "        diff = 6 - row[\"hour\"]\n", "\n", "        if diff <= 1:\n", "            row[\"mh_required_for_perishable_putaway_current_hr\"] = (\n", "                row[\"mh_required_for_perishable_putaway\"] * 0.5\n", "            )\n", "            row[\"mh_required_for_perishable_putaway_spillover\"] = (\n", "                row[\"mh_required_for_perishable_putaway\"] * 0.5\n", "            )\n", "            row[\"mh_required_for_perishable_putaway_spillover_2\"] = 0\n", "\n", "        elif diff == 2:\n", "            row[\"mh_required_for_perishable_putaway_current_hr\"] = (\n", "                row[\"mh_required_for_perishable_putaway\"] * 0.5\n", "            )\n", "            row[\"mh_required_for_perishable_putaway_spillover\"] = row[\n", "                \"mh_required_for_perishable_putaway_current_hr\"\n", "            ]\n", "            row[\"mh_required_for_perishable_putaway_spillover_2\"] = 0\n", "\n", "        else:\n", "            row[\"mh_required_for_perishable_putaway_current_hr\"] = (\n", "                row[\"mh_required_for_perishable_putaway\"] * 0.33\n", "            )\n", "            row[\"mh_required_for_perishable_putaway_spillover\"] = row[\n", "                \"mh_required_for_perishable_putaway_current_hr\"\n", "            ]\n", "            row[\"mh_required_for_perishable_putaway_spillover_2\"] = row[\n", "                \"mh_required_for_perishable_putaway_current_hr\"\n", "            ]\n", "\n", "        return row\n", "\n", "    df = df.apply(compute_putaway, axis=1)\n", "    df[\"mh_required_for_perishable_putaway_spillover\"] = (\n", "        df[\"mh_required_for_perishable_putaway_spillover\"].shift(1).fillna(0)\n", "    )\n", "    df[\"mh_required_for_perishable_putaway_spillover_2\"] = (\n", "        df[\"mh_required_for_perishable_putaway_spillover_2\"].shift(2).fillna(0)\n", "    )\n", "\n", "    ## Final Non negotial mh req\n", "    df[\"non_nego_mh_req\"] = (\n", "        df[\"mh_required_for_packaged_putaway_unloading\"]\n", "        + df[\"mh_required_for_perishable_putaway_unloading\"]\n", "        + df[\"mh_required_for_pick\"]\n", "        + df[\"mh_required_for_perishable_putaway_current_hr\"]\n", "        + df[\"mh_required_for_perishable_putaway_spillover\"]\n", "        + df[\"mh_required_for_perishable_putaway_spillover_2\"]\n", "    )\n", "    df[\"total_fnv_audit_mh\"] = 0\n", "    df[\"total_unloading_perishable_putaway_mh\"] = (\n", "        df[\"mh_required_for_perishable_putaway_current_hr\"]\n", "        + df[\"mh_required_for_perishable_putaway_spillover\"]\n", "        + df[\"mh_required_for_perishable_putaway_spillover_2\"]\n", "        + df[\"mh_required_for_perishable_putaway_unloading\"]\n", "        + df[\"mh_required_for_packaged_putaway_unloading\"]\n", "    )\n", "\n", "    df[\"actual_pick_mh\"] = df[\"mh_required_for_pick_new\"]\n", "    df[\"mh_required_for_pick\"] = np.where(\n", "        (df[\"mh_required_for_pick\"] > 0) & (df[\"mh_required_for_pick\"] < 1),\n", "        1,\n", "        df[\"mh_required_for_pick\"],\n", "    )\n", "    df[\"mh_required_for_pick\"] = np.ceil(\n", "        df[\"mh_required_for_pick\"] + (df[\"mh_required_for_pick\"] * df[\"absent_predict\"] / 100)\n", "    )\n", "\n", "    df[\"mh_required_for_pick\"] = np.where(\n", "        df[\"hour\"].isin([6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]),\n", "        np.maximum(df[\"mh_required_for_pick\"], df[\"mh_required_for_pick_qt\"]),\n", "        df[\"mh_required_for_pick\"],\n", "    )\n", "\n", "    df[\"mh_required_for_pick_new\"] = np.where(\n", "        (df[\"mh_required_for_pick_new\"] > 0) & (df[\"mh_required_for_pick_new\"] < 1),\n", "        1,\n", "        df[\"mh_required_for_pick_new\"],\n", "    )\n", "\n", "    df[\"mh_required_for_pick_new\"] = np.ceil(\n", "        df[\"mh_required_for_pick_new\"]\n", "        + (df[\"mh_required_for_pick_new\"] * df[\"absent_predict\"] / 100)\n", "    )\n", "\n", "    df[\"mh_required_for_pick_new\"] = np.where(\n", "        df[\"hour\"].isin([8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21]),\n", "        np.maximum(df[\"mh_required_for_pick_new\"], df[\"mh_required_for_pick_qt_new\"]),\n", "        df[\"mh_required_for_pick_new\"],\n", "    )\n", "\n", "    df[\"mh_required_for_pick\"] = np.maximum(\n", "        df[\"mh_required_for_pick_new\"], df[\"mh_required_for_pick\"]\n", "    )\n", "    df[\"total_fixed_mh\"] = (\n", "        df[\"total_fnv_audit_mh\"]\n", "        + df[\"mh_required_for_pick\"]\n", "        + df[\"total_unloading_perishable_putaway_mh\"]\n", "    )\n", "\n", "    df = df.reset_index()\n", "    del df[\"index\"]\n", "\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "be49d06b-eac2-4cc1-92ef-59ae55124ea8", "metadata": {"tags": []}, "outputs": [], "source": ["def optimiser(\n", "    df, potential_shift_starts, pkg_window=6, big_M=100, max_multiplier=1.1, min_multiplier=0.9\n", "):\n", "\n", "    model = LpProblem(\"Manpower_Optimization\", LpMinimize)\n", "\n", "    # Decision variables\n", "    x = LpVariable.dicts(\n", "        \"Shift_Start\", potential_shift_starts, lowBound=0, cat=\"Integer\"\n", "    )  # Shift workers starting points\n", "    y = LpVariable.dicts(\n", "        \"Hourly_Workers\", range(len(df)), lowBound=0, cat=\"Integer\"\n", "    )  # Hourly workers\n", "    od_active = LpVariable.dicts(\"OnDemand_Workers\", range(len(df)), lowBound=0, cat=\"Integer\")\n", "    od_start = LpVariable.dicts(\"od_start\", range(len(df)), lowBound=0, cat=\"Integer\")\n", "\n", "    model += (\n", "        lpSum(od_active[t] * df.loc[t, \"od_eph\"] for t in range(len(df)))\n", "        + lpSum(  # Cost for on-demand workers\n", "            y[t] * df.loc[t, \"fixed_eph\"] for t in range(len(df))\n", "        )  # Cost for hourly workers\n", "    ), \"Minimize_Total_Cost\"\n", "\n", "    for t in range(len(df)):\n", "\n", "        model += (\n", "            y[t] == lpSum(x[s] for s in potential_shift_starts if s <= t < s + 9),\n", "            f\"Worker_Availability_Hour_{t}\",\n", "        )\n", "\n", "        model += (\n", "            od_active[t]\n", "            == lpSum(\n", "                od_start[k] for k in range(max(0, t - 3), t + 1) if df.loc[k, \"hour\"] % 2 == 0\n", "            ),\n", "            f\"OD_Active_Sum_Hour_{t}\",\n", "        )\n", "\n", "        fixed = df.loc[t, \"fixed_mh_req\"]\n", "        model += y[t] + od_active[t] * df.loc[t, \"od_prod\"] >= fixed, f\"Task_Coverage_Hour_{t}\"\n", "        # model += y[t] >= fixed - df.loc[t, \"pick_hour\"], f\"FT_Employee_Task_Coverage_Hour_{t}\"\n", "        model += y[t] == df.loc[t, \"fixed_limit\"], f\"fixed_constraint_{t}\"\n", "\n", "        model += od_active[t] >= df.loc[t, \"od_limit\"], f\"OD_upper_Limit_{t}\"\n", "\n", "        if df.loc[t, \"hour\"] % 2 != 0:\n", "            model += od_start[t] == 0, f\"OD_Start_OddHour_Zero_{t}\"\n", "\n", "        if df.loc[t, \"hour\"] in [22, 23, 0, 1, 2, 3, 4, 5, 6]:\n", "            model += od_active[t] == 0, f\"OD_Active_Night_Zero_{t}\"\n", "            model += od_start[t] == 0, f\"OD_Start_Night_Zero_{t}\"\n", "\n", "        if df.loc[t, \"hour\"] <= 18:\n", "            max_pick_hour = max(\n", "                df.loc[t, \"pick_hour\"],\n", "                df.loc[t + 1, \"pick_hour\"],\n", "                df.loc[t + 2, \"pick_hour\"],\n", "                df.loc[t + 3, \"pick_hour\"],\n", "            )\n", "            model += od_active[t] * df.loc[t, \"od_prod\"] <= max_pick_hour + 4, f\"OD_Max_Even_{t}\"\n", "\n", "    solver = pulp.PULP_CBC_CMD(msg=False, timeLimit=20)\n", "    status = model.solve(solver)\n", "\n", "    if LpStatus[status] != \"Optimal\":\n", "        print(f\"infeasible for max_multiplier: {max_multiplier}\")\n", "        return None, 0\n", "\n", "    df[\"reduced_shift_workers\"] = 0\n", "    for s in potential_shift_starts:\n", "        if x[s].varValue is not None:\n", "            df.loc[df.index == s, \"reduced_shift_workers\"] = x[s].varValue\n", "    df[\"on_demand_workers\"] = [\n", "        od_active[t].varValue if od_active[t].varValue is not None else 0 for t in range(len(df))\n", "    ]\n", "    df[\"adjusted_hourly_workers\"] = [\n", "        y[t].varValue if y[t].varValue is not None else 0 for t in range(len(df))\n", "    ]\n", "    df[\"truck_distribution\"] = df[\"pckg_mh_allocation\"]\n", "\n", "    df[\"new_residual_mh\"] = [0 for t in range(len(df))]\n", "    df[\"od_cost\"] = df[\"on_demand_workers\"] * df[\"od_eph\"]\n", "    df[\"fixed_cost\"] = df[\"adjusted_hourly_workers\"] * df[\"fixed_eph\"]\n", "    df[\"total_cost\"] = df[\"od_cost\"] + df[\"fixed_cost\"]\n", "\n", "    return df, 1"]}, {"cell_type": "code", "execution_count": null, "id": "3b632cbb-429f-4369-82f1-7eae2a642d4e", "metadata": {"tags": []}, "outputs": [], "source": ["for store in store_list:\n", "\n", "    print(\"\\nstarting stores:\", store)\n", "\n", "    optimizer_data = optimizer_data_v2.copy()\n", "    optimizer_data = optimizer_data[optimizer_data.outlet_id == store]\n", "    optimizer_data = data_preprocess(optimizer_data)\n", "\n", "    #### lp data input preparation\n", "\n", "    df = optimizer_data.copy()\n", "    df = df[df.row_number >= 6].reset_index()\n", "    del df[\"index\"]\n", "    df.insert(0, \"RowNumber\", range(0, len(df)))\n", "\n", "    df[\"pick_hour\"] = df[\"mh_required_for_pick\"].apply(np.round).astype(int)\n", "    df[\"fixed_mh_req\"] = (\n", "        df[\"total_fixed_mh\"].apply(np.ceil).astype(int) + df[\"pckg_mh_allocation\"] + df[\"buffer\"]\n", "    )\n", "\n", "    df[\"fixed_mh_req\"] = np.where((df.to_remove == 1), 0, df[\"fixed_mh_req\"])\n", "    df[\"pick_hour\"] = np.where((df.to_remove == 1), 0, df[\"pick_hour\"])\n", "\n", "    potential_shift_starts = list(\n", "        df[\n", "            df.hour.isin([4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23])\n", "        ][\"RowNumber\"]\n", "    )\n", "\n", "    res_df, is_success = optimiser(df, potential_shift_starts, max_multiplier=1.1)\n", "    if is_success:\n", "        df = res_df.copy()\n", "    else:\n", "        skipped_stores.append(store)\n", "        print(f\"Timeout or infeasible at OD allocation : {store}\")\n", "        continue\n", "\n", "    output_v1 = df[\n", "        [\n", "            \"order_date\",\n", "            \"hour\",\n", "            \"packaged_qty_accumulated_final\",\n", "            \"mavg_iph_packaged\",\n", "            \"total_fnv_audit_mh\",\n", "            \"mh_required_for_pick\",\n", "            \"total_unloading_perishable_putaway_mh\",\n", "            \"total_fixed_mh\",\n", "            \"truck_distribution\",\n", "            \"reduced_shift_workers\",\n", "            \"on_demand_workers\",\n", "            \"adjusted_hourly_workers\",\n", "            \"new_residual_mh\",\n", "            \"od_cost\",\n", "            \"fixed_cost\",\n", "            \"total_cost\",\n", "            \"od_prod\",\n", "            \"actual_pick_mh\",\n", "            \"od_limit\",\n", "            \"buffer\",\n", "            \"fixed_mh_final\",\n", "            \"shift_start_final\",\n", "        ]\n", "    ]\n", "\n", "    output_v1 = output_v1.copy()\n", "\n", "    output_v1[\"fixed_mh_allocated\"] = output_v1[\"adjusted_hourly_workers\"]\n", "    output_v1[\"final_mh_req\"] = output_v1[\"total_fixed_mh\"] + output_v1[\"truck_distribution\"]\n", "    output_v1[\"shift_start_raw\"] = output_v1[\"reduced_shift_workers\"]\n", "    output_v1[\"adjusted_hourly_workers\"] = output_v1[\"fixed_mh_final\"]\n", "    output_v1[\"reduced_shift_workers\"] = output_v1[\"shift_start_final\"]\n", "\n", "    output_v1[\"outlet_id\"] = store\n", "    output_v1[\"eph_od\"] = df[\"od_eph\"]\n", "    output_v1[\"eph_fixed\"] = df[\"fixed_eph\"]\n", "\n", "    output1_final = pd.concat([output1_final, output_v1], ignore_index=True)\n", "    print(\"outlet_id: \", store, \" Done\")"]}, {"cell_type": "code", "execution_count": null, "id": "48a6c54b-d918-4659-af74-25733b19959a", "metadata": {}, "outputs": [], "source": ["skipped_stores"]}, {"cell_type": "code", "execution_count": null, "id": "1c32c097-f0c2-4bd1-84a8-6e0251bdd105", "metadata": {}, "outputs": [], "source": ["# Selecting relevant columns\n", "output1_final = output1_final[\n", "    [\n", "        \"outlet_id\",\n", "        \"order_date\",\n", "        \"hour\",\n", "        \"mh_required_for_pick\",\n", "        \"total_unloading_perishable_putaway_mh\",\n", "        \"total_fixed_mh\",\n", "        \"truck_distribution\",\n", "        \"reduced_shift_workers\",\n", "        \"on_demand_workers\",\n", "        \"adjusted_hourly_workers\",\n", "        \"final_mh_req\",\n", "        \"od_prod\",\n", "        \"actual_pick_mh\",\n", "        \"fixed_mh_allocated\",\n", "        \"shift_start_raw\",\n", "    ]\n", "]\n", "\n", "# Renaming columns for better readability\n", "output1_final.columns = [\n", "    \"outlet_id\",\n", "    \"order_date\",\n", "    \"hour\",\n", "    \"picking_mh\",\n", "    \"unloading_perishable_putaway_mh\",\n", "    \"non_nego_mh\",\n", "    \"pckg_mh_allocation\",\n", "    \"employee_starting_shift\",\n", "    \"od_mh\",\n", "    \"fixed_mh\",\n", "    \"req_mh\",\n", "    \"od_prod\",\n", "    \"actual_pick_mh\",\n", "    \"fixed_mh_allocated\",\n", "    \"shift_start_raw\",\n", "]\n", "\n", "\n", "output1_final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9931cf26-0a0e-4e7b-8f51-705f328fcbee", "metadata": {}, "outputs": [], "source": ["output_pivot = output1_final.pivot_table(\n", "    index=[\"outlet_id\", \"order_date\"],  # Keep these as index\n", "    columns=\"hour\",  # Make 'hour' the columns\n", "    values=\"od_mh\",  # Fill with 'employee_starting_shift' values\n", "    aggfunc=\"sum\",  # Assuming sum is appropriate, otherwise change it\n", ").reset_index()\n", "output_pivot.columns.name = None"]}, {"cell_type": "code", "execution_count": null, "id": "384418fb-f2bf-4752-a67c-9db9dad03c38", "metadata": {}, "outputs": [], "source": ["output_pivot.head()"]}, {"cell_type": "code", "execution_count": null, "id": "22b0939a-77b6-451d-9e88-28e92ddd90ac", "metadata": {}, "outputs": [], "source": ["# output2_final = output2_final.merge(output_pivot, on=[\"outlet_id\", \"order_date\"])\n", "output2_final = output_pivot.copy()\n", "\n", "output2_final = output2_final.rename(columns={i: f\"{i}\" for i in range(24)})\n", "output2_final = output2_final.fillna(0)\n", "output2_final[\"new_od_mh_required\"] = output2_final[[str(i) for i in range(24)]].sum(axis=1)\n", "output2_final = output2_final[output2_final.order_date == target_date]\n", "output2_final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4b12a7ad-1748-4110-912b-36111b924783", "metadata": {}, "outputs": [], "source": ["curr_roaster_df = pd.read_parquet(\n", "    \"s3://prod-dse-projects/store_ops/pilot_data/current_active/roaster_data/\"\n", ")[[\"order_date\", \"outlet_id\", \"od_mh_required\", \"final_mp_at_store_after_weekoffs\"]].rename(\n", "    columns={\n", "        \"final_mp_at_store_after_weekoffs\": \"fixed_md_alloted\",\n", "        \"od_mh_required\": \"od_mh_alotted\",\n", "    }\n", ")\n", "curr_roaster_df[\"order_date\"] = pd.to_datetime(curr_roaster_df[\"order_date\"])\n", "curr_roaster_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8075cd88-ebdf-4d07-aa68-1ff06569671c", "metadata": {}, "outputs": [], "source": ["hour_column = [f\"{i}\" for i in range(24)]\n", "output2_final = output2_final.merge(curr_roaster_df, on=[\"order_date\", \"outlet_id\"])\n", "output2_final = output2_final[\n", "    [\n", "        \"order_date\",\n", "        \"outlet_id\",\n", "        \"new_od_mh_required\",\n", "        \"od_mh_alotted\",\n", "    ]\n", "    + hour_column\n", "]\n", "output2_final = output2_final[output2_final.new_od_mh_required > output2_final.od_mh_alotted]\n", "output2_final = output2_final.drop([\"od_mh_alotted\", \"new_od_mh_required\"], axis=1)\n", "output2_final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4f5bfe19-b1e6-44d5-b91b-e1135f4cfa35", "metadata": {}, "outputs": [], "source": ["target_store_list = list(output2_final[\"outlet_id\"])\n", "len(target_store_list)"]}, {"cell_type": "code", "execution_count": null, "id": "acd2bf73-bde9-4430-98ec-4db2e1f0d6a4", "metadata": {"tags": []}, "outputs": [], "source": ["df_melted = output2_final.melt(\n", "    id_vars=[\"order_date\", \"outlet_id\"], var_name=\"hour\", value_name=\"hourly_value\"\n", ")\n", "df_melted[\"hour\"] = df_melted[\"hour\"].astype(int)\n", "df_melted = df_melted.merge(\n", "    optimizer_data_v2[[\"order_date\", \"hour\", \"outlet_id\", \"qty_to_pick_new\"]],\n", "    on=[\"order_date\", \"hour\", \"outlet_id\"],\n", ")\n", "df_melted.sort_values([\"outlet_id\", \"order_date\", \"hour\"]).head()"]}, {"cell_type": "code", "execution_count": null, "id": "1ab97285-6317-4cb5-85c8-14bd1cb71e98", "metadata": {}, "outputs": [], "source": ["day_of_week = pd.to_datetime(target_date).day_name()\n", "print(day_of_week)\n", "\n", "s3_path = f\"s3://prod-dse-projects/store_ops/pilot_data/current_active/t-1/{day_of_week}\"\n", "write_df_as_parquet_to_s3(df_melted, s3_path)\n", "s3_path"]}, {"cell_type": "code", "execution_count": null, "id": "f48976d8-e821-4486-96a7-bce6a4fe59cc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "8dcf4c33-9a4f-467c-9af5-29c494638b65", "metadata": {}, "source": ["### Pushing Data to <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "id": "94fb65d8-2410-4020-81bc-e96af9ffa395", "metadata": {}, "outputs": [], "source": ["print(target_date)\n", "update_ts_ist = datetime.now() + <PERSON><PERSON>ta(hours=5.5)\n", "update_ts_ist = update_ts_ist.strftime(\"%Y-%m-%d %H:%M:%S\")"]}, {"cell_type": "code", "execution_count": null, "id": "25a56dd9-95df-464c-b181-0db587884fbc", "metadata": {}, "outputs": [], "source": ["data = output1_final[\n", "    (output1_final.order_date == target_date) & (output1_final.outlet_id.isin(target_store_list))\n", "].copy()\n", "data[\"putaway_mh\"] = data[\"unloading_perishable_putaway_mh\"] + data[\"pckg_mh_allocation\"]\n", "data[\"fnv\"] = np.where((data.hour >= 5) & (data.hour <= 22), 1, 0)\n", "data[\"buffer\"] = (data[\"od_mh\"] * data[\"od_prod\"]) + data[\"fixed_mh\"] - data[\"req_mh\"] - data[\"fnv\"]\n", "data[\"action_type\"] = \"od\"\n", "data[\"updated_ts\"] = update_ts_ist"]}, {"cell_type": "code", "execution_count": null, "id": "7cd009e0-720a-4841-93c7-85f7deb21198", "metadata": {}, "outputs": [], "source": ["data = data.merge(\n", "    optimizer_data_v2[[\"order_date\", \"outlet_id\", \"hour\", \"qty_to_pick_new\", \"mavg_ppi_new\"]],\n", "    on=[\"order_date\", \"outlet_id\", \"hour\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "00bd5efc-a3cb-4a53-9f49-79ec499f8531", "metadata": {}, "outputs": [], "source": ["columns = [\n", "    \"order_date\",\n", "    \"outlet_id\",\n", "    \"hour\",\n", "    \"picking_mh\",\n", "    \"putaway_mh\",\n", "    \"fnv\",\n", "    \"buffer\",\n", "    \"employee_starting_shift\",\n", "    \"od_mh\",\n", "    \"fixed_mh\",\n", "    \"qty_to_pick_new\",\n", "    \"mavg_ppi_new\",\n", "    \"action_type\",\n", "    \"updated_ts\",\n", "]\n", "data = data[columns]"]}, {"cell_type": "code", "execution_count": null, "id": "5a6919c1-9f0a-4a38-93b9-c8a04ea32e93", "metadata": {}, "outputs": [], "source": ["data = data.rename(\n", "    columns={\n", "        \"order_date\": \"date\",\n", "        \"employee_starting_shift\": \"shift_start\",\n", "        \"qty_to_pick_new\": \"qty_pick_new\",\n", "        \"mavg_ppi_new\": \"ppi_new\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f0069422-3096-40f4-865c-b7a4d8e1e5cf", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"ds_etls\",\n", "    \"table_name\": \"store_ops_daily_roster_action\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date\", \"type\": \"DATE\", \"description\": \"date for which roaster is created.\"},\n", "        {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"Unique identifier for the outlet\"},\n", "        {\"name\": \"hour\", \"type\": \"INTEGER\", \"description\": \"Hour of the day\"},\n", "        {\"name\": \"picking_mh\", \"type\": \"INTEGER\", \"description\": \"Man-hours spent on picking\"},\n", "        {\"name\": \"putaway_mh\", \"type\": \"REAL\", \"description\": \"Man-hours allocated for putaway\"},\n", "        {\"name\": \"shift_start\", \"type\": \"INTEGER\", \"description\": \"Shift start time\"},\n", "        {\"name\": \"od_mh\", \"type\": \"INTEGER\", \"description\": \"OD man-hours allocated\"},\n", "        {\"name\": \"fixed_mh\", \"type\": \"INTEGER\", \"description\": \"Fulltime man-hours allocated\"},\n", "        {\"name\": \"fnv\", \"type\": \"INTEGER\", \"description\": \"fnv executive allocated\"},\n", "        {\n", "            \"name\": \"buffer\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"<PERSON><PERSON><PERSON> present. Used for audit and breaks\",\n", "        },\n", "        {\"name\": \"qty_pick_new\", \"type\": \"INTEGER\", \"description\": \"Latest order projections\"},\n", "        {\"name\": \"ppi_new\", \"type\": \"REAL\", \"description\": \"New ppi\"},\n", "        {\"name\": \"action_type\", \"type\": \"VARCHAR\", \"description\": \"Type of action.\"},\n", "        {\"name\": \"updated_ts\", \"type\": \"VARCHAR\", \"description\": \" Update Timestamp of the record\"},\n", "    ],\n", "    \"primary_key\": [\"date\", \"outlet_id\", \"hour\"],\n", "    \"partition_key\": [\"date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"table_description\": \"This table contains t-1 roster changes for stores.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "29786d22-5e18-4101-ad67-26c7f8a68228", "metadata": {}, "outputs": [], "source": ["pb.to_trino(data_obj=data, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "6444ceb0-ae19-48e2-be4d-4ed86e2ab27f", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(channel=\"t_minus_one_actions_alert\", text=\"T-1 actions updated..\")\n", "if len(skipped_stores) > 0:\n", "    pb.send_slack_message(\n", "        channel=\"t_minus_one_actions_alert\",\n", "        text=f\"Stores with infeasible solution:{skipped_stores}\",\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "bbeced5e-6b29-48de-b43c-41e7ddde8227", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}