{"cells": [{"cell_type": "code", "execution_count": null, "id": "e4ce20cf-8499-46bc-acd0-6e900f88ea7e", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# parameters"]}, {"cell_type": "code", "execution_count": null, "id": "8389713b-7615-4707-871a-e0efc2416225", "metadata": {}, "outputs": [], "source": ["# Set to True when debugging | Provide the ID you want to Debug for in DEBUG_ID\n", "DEBUG_MODE = False\n", "DEBUG_ID = 1\n", "\n", "CAN_RUN_NEXT_CELL = True\n", "\n", "\n", "def should_continue():\n", "    return CAN_RUN_NEXT_CELL\n", "\n", "\n", "def should_stop():\n", "    global CAN_RUN_NEXT_CELL\n", "    CAN_RUN_NEXT_CELL = False"]}, {"cell_type": "code", "execution_count": null, "id": "b53164b1-d2ba-470e-90dd-47f2a3633515", "metadata": {}, "outputs": [], "source": ["!pip install pyproj\n", "!pip install fiona\n", "!pip install --upgrade --upgrade-strategy only-if-needed geopandas==0.13.2\n", "!pip install shapely==2.0.3\n", "\n", "# !pip install pandas==1.3.5\n", "!pip install h3==3.7.6"]}, {"cell_type": "code", "execution_count": null, "id": "06822696-68f8-48cd-9565-3e54f6f498cd", "metadata": {}, "outputs": [], "source": ["import h3\n", "import pyproj\n", "import fiona\n", "import numpy as np\n", "import pandas as pd\n", "from pyproj import CRS\n", "import geopandas as gpd\n", "from pyproj import Transformer\n", "from shapely.wkt import loads\n", "from shapely.ops import transform\n", "from shapely.geometry import Point, Polygon\n", "import io\n", "import json\n", "import tenacity\n", "\n", "import pencilbox as pb\n", "\n", "tricon = pb.get_connection(\"[Warehouse] Trino\")\n", "fiona.supported_drivers[\"KML\"] = \"rw\"\n", "\n", "NI_HOST = \"https://sauron-api-locations.prod-sgp-k8s.grofer.io\"\n", "API_KEY = pb.get_secret(\"dse/ds/network-design/auth\").get(\"api_key\")"]}, {"cell_type": "code", "execution_count": null, "id": "da6c28ea-b997-40a4-9bed-605d5050c8bc", "metadata": {}, "outputs": [], "source": ["def xyz_to_xy(geom):\n", "    return transform(lambda y, x, z: (y, x), geom)\n", "\n", "\n", "def get_city_string(city_list):\n", "    if len(city_list) == 1:\n", "        city_str = f\"city_name = '{city_list[0]}'\"\n", "    else:\n", "        city_str = f\"city_name in {tuple(city_list)}\"\n", "    return city_str\n", "\n", "\n", "import requests\n", "\n", "\n", "@tenacity.retry(\n", "    stop=tenacity.stop_after_attempt(3),\n", "    wait=tenacity.wait_exponential(multiplier=1, max=10),\n", "    reraise=True,\n", "    retry=tenacity.retry_if_exception_type(Exception),\n", ")\n", "def get_km_dist_osrm_z(Point_1, Point_2):\n", "    url = \"http://zomato-eternal-http.prod.grofer.io:443/zomato.osrm_aggregator.Route/GetRoute\"\n", "    headers = {\"Content-Type\": \"application/json\"}\n", "    json_data = {\n", "        \"coordinates\": [\n", "            {\n", "                \"latitude\": Point_1.y,\n", "                \"longitude\": Point_1.x,\n", "            },\n", "            {\n", "                \"latitude\": Point_2.y,\n", "                \"longitude\": Point_2.x,\n", "            },\n", "        ],\n", "        \"annotations\": True,\n", "        \"geometries\": 1,\n", "        \"overview\": 1,\n", "        \"precision\": 1,\n", "    }\n", "\n", "    response = requests.post(url, headers=headers, json=json_data)\n", "    data = response.json()\n", "    distance_km = data[\"response\"][\"routes\"][0][\"distance\"] / 1000\n", "    return distance_km\n", "\n", "\n", "def get_polygon_area(geom):\n", "    \"\"\"Polygon area in km^2\"\"\"\n", "    crs_4326 = CRS(\"epsg:4326\")\n", "    crs_54009 = CRS(\"esri:54009\")\n", "    transformer = Transformer.from_crs(crs_4326, crs_54009, always_xy=True)\n", "    geom_area = transform(transformer.transform, geom)\n", "    return geom_area.area / 10**6"]}, {"cell_type": "code", "execution_count": null, "id": "e2f0e2b7-5d70-4eb0-95f3-04ee431ba670", "metadata": {}, "outputs": [], "source": ["def get_store_polygons():\n", "    q = f\"\"\"\n", "    \n", "        WITH active_stores AS\n", "        (\n", "        SELECT DISTINCT CAST(sod.frontend_merchant_id AS VARCHAR) as merchant_id,\n", "               sod.city_name,\n", "               COUNT(DISTINCT sod.cart_id) as count_orders\n", "\n", "        FROM  dwh.fact_sales_order_details sod\n", "        WHERE sod.order_create_dt_ist >= CURRENT_DATE - INTERVAL '7' DAY\n", "        AND   sod.order_create_dt_ist <= CURRENT_DATE - INTERVAL '1' DAY\n", "        AND   (sod.polygon_type = 'express' OR sod.polygon_type IS NULL)\n", "        AND   sod.frontend_merchant_id <> -1\n", "        AND   is_internal_order = false\n", "        GROUP BY 1,2\n", "        HAVING COUNT(DISTINCT sod.cart_id) > 50\n", "        )\n", "\n", "        SELECT\n", "        sp.merchant_id,\n", "        sp.name,\n", "        sp.city_name , \n", "        ST_Point(cast(sp.longitude as real),cast(sp.latitude as real)) as location,\n", "        sp.polygon\n", "        FROM ds_etls.ds_live_store_polygons sp\n", "        INNER JOIN active_stores a ON a.merchant_id = sp.merchant_id\n", "        WHERE sp.type='STORE_POLYGON'\n", "        AND sp.city_name <> 'temp'\n", "    \n", "    \"\"\"\n", "\n", "    df_ = pd.read_sql(q, tricon)\n", "    df_[\"polygon\"] = df_[\"polygon\"].apply(loads)\n", "    df_ = gpd.GeoDataFrame(\n", "        df_[[\"city_name\", \"merchant_id\", \"name\", \"location\", \"polygon\"]],\n", "        crs=4326,\n", "        geometry=\"polygon\",\n", "    )\n", "    return df_\n", "\n", "\n", "def get_store_orders(city_string, num_days):\n", "    q = f\"\"\"\n", "\n", "    select distinct scod.frontend_merchant_id, scod.cart_id, order_create_dt_ist,  scod.customer_location_latitude, \n", "    scod.customer_location_longitude, \n", "    scod.distance_store_to_delivery_marked_kms, scod.distance_delivery_marked_to_customer_location_kms,\n", "    scod.est_store_to_customer_location_kms\n", "    from dwh.fact_supply_chain_order_details scod\n", "    join dwh.fact_sales_order_details sod\n", "    on scod.cart_id = sod.cart_id AND scod.order_id = sod.order_id\n", "    where sod.order_create_dt_ist >= current_date - interval '{num_days}' day\n", "    and sod.order_create_dt_ist <= current_date - interval '1' day\n", "    and {city_string}\n", "    and scod.order_checkout_dt_ist >= current_date - interval '{num_days}' day\n", "    and scod.order_checkout_dt_ist <= current_date - interval '1' day\n", "    and scod.is_marked_delivery_failed = false\n", "    and scod.order_current_status = 'DELIVERED'\n", "    and sod.polygon_type = 'express'\n", "            \"\"\"\n", "    df = pd.read_sql(q, tricon)\n", "\n", "    if len(df) != 0:\n", "\n", "        df[\"geometry\"] = df.apply(\n", "            lambda r: Point(r[\"customer_location_longitude\"], r[\"customer_location_latitude\"]),\n", "            axis=1,\n", "        )\n", "\n", "        df = gpd.GeoDataFrame(df, geometry=\"geometry\", crs=4326)\n", "\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "1500953d-f848-4b9c-9932-48f78f27ecb2", "metadata": {}, "outputs": [], "source": ["# Request\n", "\n", "import requests\n", "\n", "\n", "class StopExecution(Exception):\n", "    def _render_traceback_(self):\n", "        print(\"Stopping Execution of notebook\")\n", "        pass\n", "\n", "\n", "class MoveRequestToError(Exception):\n", "    pass\n", "\n", "\n", "def is_request_processing():\n", "    url = f\"{NI_HOST}/ni/run_request/?page=1&page_size=1&state=PROCESSING_STARTED&config_type=POLYGON_PROJECTION\"\n", "    headers = {\n", "        \"accept\": \"application/json\",\n", "        \"x-api-key\": API_KEY,\n", "    }\n", "    response = requests.request(\"GET\", url, headers=headers)\n", "\n", "    if response.status_code < 200 or response.status_code >= 300:\n", "        print(\"ERROR IN FETCHING RUN REQUESTS\", response.text.encode(\"utf8\"))\n", "        return None\n", "\n", "    results = response.json().get(\"results\", []) or []\n", "    if results:\n", "        return True\n", "    return False\n", "\n", "\n", "def get_polygon_projection_run_requests():\n", "    fields = [\"id\", \"run_config\", \"name\"]\n", "    fields_param = \"&\".join([f\"fields={x}\" for x in fields])\n", "    url = f\"{NI_HOST}/ni/run_request/?page=1&page_size=1&{fields_param}&state=CREATED&order_by=id&config_type=POLYGON_PROJECTION\"\n", "    headers = {\n", "        \"accept\": \"application/json\",\n", "        \"x-api-key\": API_KEY,\n", "    }\n", "    response = requests.request(\"GET\", url, headers=headers)\n", "\n", "    if response.status_code < 200 or response.status_code >= 300:\n", "        print(\"ERROR IN FETCHING RUN REQUESTS\", response.text.encode(\"utf8\"))\n", "        return None\n", "\n", "    results = response.json().get(\"results\", []) or []\n", "    if results:\n", "        return results[0]\n", "    return None\n", "\n", "\n", "def get_polygon_projection_run_requests_from_id(id):\n", "    url = f\"{NI_HOST}/ni/run_request/{id}\"\n", "    headers = {\n", "        \"accept\": \"application/json\",\n", "        \"x-api-key\": API_KEY,\n", "    }\n", "    response = requests.request(\"GET\", url, headers=headers)\n", "\n", "    if response.status_code < 200 or response.status_code >= 300:\n", "        print(\"ERROR IN FETCHING RUN REQUESTS\", response.text.encode(\"utf8\"))\n", "        return None\n", "\n", "    result = response.json()\n", "    return result or None\n", "\n", "\n", "def move_to_processing_started_state(run_request_id):\n", "    url = f\"{NI_HOST}/ni/run_request/{run_request_id}?transition_to=PROCESSING_STARTED\"\n", "    headers = {\n", "        \"accept\": \"application/json\",\n", "        \"Content-Type\": \"application/json\",\n", "        \"x-api-key\": API_KEY,\n", "    }\n", "    response = requests.request(\"PUT\", url, headers=headers)\n", "    if response.status_code < 200 or response.status_code >= 300:\n", "        print(\n", "            f\"ERROR IN TRANSITIONING {run_request_id} to PROCESSING_STARTED state\",\n", "            response.text.encode(\"utf8\"),\n", "        )\n", "        raise Exception(\"Failed to move request to processing started state\")\n", "\n", "\n", "def move_to_error_state(run_request_id, e):\n", "    url = f\"{NI_HOST}/ni/run_request/{run_request_id}?transition_to=ERROR\"\n", "    headers = {\n", "        \"accept\": \"application/json\",\n", "        \"Content-Type\": \"application/json\",\n", "        \"x-api-key\": API_KEY,\n", "    }\n", "    payload = json.dumps({\"error_detail\": {\"error\": e}, \"error\": \"Error in executing notebook\"})\n", "    response = requests.request(\"PUT\", url, headers=headers, data=payload)\n", "    if response.status_code < 200 or response.status_code >= 300:\n", "        print(\n", "            f\"ERROR IN TRANSITIONING {run_request_id} to ERROR state\",\n", "            response.text.encode(\"utf8\"),\n", "        )\n", "        raise Exception(\"Failed to move request to error state\")\n", "\n", "\n", "def move_to_completed_state(run_request_id, data):\n", "    url = (\n", "        f\"{NI_HOST}/ni/run_request/{run_request_id}?transition_to=COMPLETED&result_type=FILE_RESULT\"\n", "    )\n", "    payload = json.dumps({\"data\": data})\n", "    headers = {\n", "        \"accept\": \"application/json\",\n", "        \"Content-Type\": \"application/json\",\n", "        \"x-api-key\": API_KEY,\n", "    }\n", "    response = requests.request(\"PUT\", url, headers=headers, data=payload)\n", "    if response.status_code < 200 or response.status_code >= 300:\n", "        print(\n", "            f\"ERROR IN TRANSITIONING {run_request_id} to COMPLETED state\",\n", "            response.text.encode(\"utf8\"),\n", "        )\n", "        raise Exception(\"Failed to move request to completed state\")"]}, {"cell_type": "code", "execution_count": null, "id": "a8c5eeb4-dc47-4615-9723-144803b65637", "metadata": {}, "outputs": [], "source": ["def h3_to_polygon(hex_id):\n", "    return Polygon(h3.h3_to_geo_boundary(hex_id, geo_json=True))\n", "\n", "\n", "def lat_long_to_h3(lat, lon, resolution):\n", "    return h3.geo_to_h3(lat, lon, resolution)\n", "\n", "\n", "def get_nsa_data(min_latitude, max_latitude, min_longitude, max_longitude):\n", "\n", "    nsa_query = f\"\"\"\n", "\n", "           SELECT DISTINCT \n", "           nsa.at_date_ist,\n", "           nsa.platform,\n", "           nsa.device_uuid,\n", "           nsa.latitude,\n", "           nsa.longitude\n", "           FROM ds_etls.nsa_pings_raw nsa\n", "           WHERE nsa.at_date_ist >= CAST(CURRENT_DATE - INTERVAL '30' DAY AS VARCHAR)\n", "           AND   nsa.latitude >= {min_latitude}\n", "           AND   nsa.latitude <= {max_latitude}\n", "           AND   nsa.longitude >= {min_longitude}\n", "           AND   nsa.longitude <= {max_longitude}\n", "\n", "           \"\"\"\n", "    nsa_df = pd.read_sql(nsa_query, tricon)\n", "\n", "    if len(nsa_df) != 0:\n", "\n", "        nsa_df[\"geometry\"] = nsa_df.apply(lambda r: Point(r[\"longitude\"], r[\"latitude\"]), axis=1)\n", "        nsa_df = gpd.GeoDataFrame(nsa_df, geometry=\"geometry\", crs=4326)\n", "\n", "    return nsa_df"]}, {"cell_type": "markdown", "id": "160224cb-a6a6-4862-950c-ce7414b140b4", "metadata": {}, "source": ["# Polygon Upload & Serviceable Area Overlap Check"]}, {"cell_type": "code", "execution_count": null, "id": "b33861bd-**************-b6e1ecdd998e", "metadata": {}, "outputs": [], "source": ["try:\n", "    if is_request_processing():\n", "        raise Exception(\"Run Request still processing\")\n", "\n", "    if DEBUG_MODE:\n", "        run_request = get_polygon_projection_run_requests_from_id(DEBUG_ID)\n", "    else:\n", "        run_request = get_polygon_projection_run_requests()\n", "\n", "    run_request_id = run_request[\"id\"]\n", "    if not run_request_id:\n", "        raise MoveRequestToError(\"Valid run request id not present\")\n", "\n", "    control = run_request[\"run_config\"]\n", "    control[\"remarks\"] = f'{run_request[\"name\"]} ({run_request[\"id\"]})'\n", "\n", "    if not control:\n", "        raise MoveRequestToError(\"Valid run config not present\")\n", "\n", "except MoveRequestToError as e:\n", "    print(str(e))\n", "    should_stop()\n", "    if not DEBUG_MODE and run_request_id:\n", "        move_to_error_state(run_request_id, str(e))\n", "\n", "except Exception as e:\n", "    print(str(e))\n", "    should_stop()"]}, {"cell_type": "code", "execution_count": null, "id": "5a15f5f7-6aac-4680-88f5-b38964dde173", "metadata": {}, "outputs": [], "source": ["if should_continue():\n", "    if not DEBUG_MODE:\n", "        move_to_processing_started_state(run_request_id)"]}, {"cell_type": "code", "execution_count": null, "id": "305c8728-c1b4-4986-8caa-45324a8b6211", "metadata": {}, "outputs": [], "source": ["if should_continue():\n", "    try:\n", "        ds_loc = (0, 0)\n", "        num_days = 14\n", "        conversion_ratio = 0.2\n", "        excluded_neighbour_merchant_id = -1\n", "\n", "        request_ds_location = control.get(\"dark_store_location\", {})\n", "        if (\n", "            request_ds_location\n", "            and \"latitude\" in request_ds_location\n", "            and \"longitude\" in request_ds_location\n", "        ):\n", "            ds_loc = (\n", "                request_ds_location.get(\"latitude\"),\n", "                request_ds_location.get(\"longitude\"),\n", "            )\n", "        else:\n", "            raise Exception(\"Store Coordinates not present\")\n", "\n", "        if control.get(\"num_days\"):\n", "            num_days = control.get(\"num_days\")\n", "\n", "        if control.get(\"conversion_ratio\"):\n", "            conversion_ratio = control.get(\"conversion_ratio\")\n", "\n", "        if control.get(\"excluded_neighbour_merchant_id\"):\n", "            excluded_neighbour_merchant_id = control.get(\"excluded_neighbour_merchant_id\")\n", "\n", "        ds_loc = Point(ds_loc[1], ds_loc[0])\n", "    except Exception as e:\n", "        print(str(e))\n", "        should_stop()\n", "        if not DEBUG_MODE:\n", "            move_to_error_state(run_request_id, str(e))"]}, {"cell_type": "code", "execution_count": null, "id": "c0323f1d-84bf-4829-9897-09a5595d0d63", "metadata": {}, "outputs": [], "source": ["orders_in_new_ds = pd.DataFrame()\n", "neighbour_metrics = pd.DataFrame()\n", "percent_nsa_area = 6\n", "expected_order_opd = 0\n", "\n", "uploaded_polygon_name = \"uploaded_polygon\""]}, {"cell_type": "code", "execution_count": null, "id": "592a61e9-1642-4518-b334-7526877afe13", "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "if should_continue():\n", "    try:\n", "        geojson_file_like = io.StringIO(json.dumps(control.get(\"geometry\")))\n", "\n", "        df = (\n", "            gpd.read_file(geojson_file_like)\n", "            # .rename(columns={\"Name\": \"name\"})\n", "            .assign(geometry=lambda x: x[\"geometry\"]).set_crs(  # .apply(xyz_to_xy)\n", "                4326, allow_override=True\n", "            )\n", "        )\n", "\n", "        # Removing Z Coordinate from Polygon if it is present in the KML File\n", "\n", "        if len(df[\"geometry\"].get_coordinates(include_z=True).dropna()) != 0:\n", "            df[\"geometry\"] = df[\"geometry\"].apply(xyz_to_xy)\n", "\n", "        df[\"geometry\"] = df[\"geometry\"].make_valid()\n", "\n", "        df = df[[\"geometry\"]]\n", "        df[\"name\"] = uploaded_polygon_name\n", "        df = df[[\"name\", \"geometry\"]].reset_index(drop=True)\n", "\n", "    except Exception as e:\n", "        print((str(e)))\n", "        should_stop()\n", "        if not DEBUG_MODE:\n", "            move_to_error_state(run_request_id, str(e))"]}, {"cell_type": "code", "execution_count": null, "id": "ccebb1bc-6f83-4fcd-a59e-1bca2d4d250e", "metadata": {}, "outputs": [], "source": ["# Checking for No. of Polygons in KML file and the location of DS inside polygon\n", "if should_continue():\n", "    try:\n", "        df.loc[:, \"len\"] = df[\"geometry\"].apply(lambda r: r.length)\n", "        df = df.query(\"len!=0\").drop(columns=\"len\").reset_index(drop=True)\n", "\n", "        if len(df) != 1:\n", "            raise Exception(\"Issue with KML file. The KML should contain only 1 polygon\")\n", "\n", "        if df[\"geometry\"].iloc[0].intersection(ds_loc).is_empty:\n", "            raise Exception(\"The ds locations is not inside the polygon uploaded!\")\n", "\n", "    except Exception as e:\n", "        print((str(e)))\n", "        should_stop()\n", "        if not DEBUG_MODE:\n", "            move_to_error_state(run_request_id, str(e))"]}, {"cell_type": "code", "execution_count": null, "id": "66025cce-21fa-44c7-a182-690dbebec240", "metadata": {}, "outputs": [], "source": ["# Checking for Neighbour Polygons\n", "if should_continue():\n", "    try:\n", "        # Fetching Store Polygon Data\n", "        store_polygons = get_store_polygons()\n", "        neighbour_polygons = store_polygons.sjoin(df)\n", "\n", "        if len(neighbour_polygons) != 0:\n", "            neighbour_polygons = (\n", "                neighbour_polygons.drop(columns=\"index_right\")\n", "                .assign(\n", "                    same_store=lambda x: x.apply(\n", "                        lambda r: 1 if r[\"name_left\"] == r[\"name_right\"] else 0, axis=1\n", "                    )\n", "                )\n", "                .drop(columns=\"name_right\")\n", "                .rename(columns={\"name_left\": \"name\"})\n", "                .assign(\n", "                    location=lambda x: x[\"location\"].apply(loads),\n", "                    merchant_id=lambda x: x[\"merchant_id\"].astype(\"int\"),\n", "                )\n", "                .reset_index(drop=True)\n", "            )\n", "\n", "            # Adding Same Store Neighbour Exclusion\n", "\n", "            neighbour_polygons = neighbour_polygons.drop(\n", "                neighbour_polygons[\n", "                    neighbour_polygons[\"merchant_id\"] == excluded_neighbour_merchant_id\n", "                ].index\n", "            ).reset_index(drop=True)\n", "\n", "            if len(neighbour_polygons) != 0:\n", "\n", "                city_list = list(neighbour_polygons[\"city_name\"].drop_duplicates())\n", "                city_str = get_city_string(city_list)\n", "\n", "                # Getting city orders for num_days\n", "\n", "                order_df = get_store_orders(city_str, num_days)\n", "\n", "                if len(order_df) != 0:\n", "\n", "                    # Checking for Orders in the uploaded polygon\n", "\n", "                    orders_in_new_ds = order_df.sjoin(df)\n", "\n", "    except Exception as e:\n", "        print((str(e)))\n", "        should_stop()\n", "        if not DEBUG_MODE:\n", "            move_to_error_state(run_request_id, str(e))"]}, {"cell_type": "code", "execution_count": null, "id": "c05576c5-fab3-4264-9d81-d8d8a78c6566", "metadata": {}, "outputs": [], "source": ["# Creating Bounding Box for NSA Data\n", "if should_continue():\n", "    try:\n", "        if len(neighbour_polygons) != 0:\n", "\n", "            bounds_gdf = gpd.GeoDataFrame(\n", "                pd.concat(\n", "                    [\n", "                        df[[\"name\", \"geometry\"]].rename(columns={\"geometry\": \"polygon\"}),\n", "                        neighbour_polygons[[\"name\", \"polygon\"]],\n", "                    ]\n", "                ),\n", "                geometry=\"polygon\",\n", "                crs=4326,\n", "            )\n", "\n", "            # Creating Combined Neighbour Polygon\n", "\n", "            combined_neighbours = (\n", "                neighbour_polygons[[\"name\", \"polygon\"]].dissolve().assign(name=\"combined_polygon\")\n", "            )\n", "\n", "            nsa_area = df[\"geometry\"].difference(combined_neighbours[\"polygon\"])\n", "\n", "            percent_nsa_area = (\n", "                (get_polygon_area(nsa_area[0])) / (get_polygon_area(df[\"geometry\"].iloc[0]))\n", "            ) * 100\n", "\n", "        else:\n", "\n", "            bounds_gdf = df[[\"name\", \"geometry\"]]\n", "\n", "        # Creating Bounding Box Variables. x is longitude, y is latitude\n", "\n", "        min_longitude = bounds_gdf.dissolve().bounds[\"minx\"].values[0]\n", "        max_longitude = bounds_gdf.dissolve().bounds[\"maxx\"].values[0]\n", "\n", "        min_latitude = bounds_gdf.dissolve().bounds[\"miny\"].values[0]\n", "        max_latitude = bounds_gdf.dissolve().bounds[\"maxy\"].values[0]\n", "    except Exception as e:\n", "        print((str(e)))\n", "        should_stop()\n", "        if not DEBUG_MODE:\n", "            move_to_error_state(run_request_id, str(e))"]}, {"cell_type": "code", "execution_count": null, "id": "174254a0-7cb4-4323-a8d6-3184cdc5fd60", "metadata": {}, "outputs": [], "source": ["if should_continue():\n", "    try:\n", "        if len(orders_in_new_ds) != 0:\n", "\n", "            # Calculating distance as per new store location\n", "\n", "            orders_in_new_ds[\"new_ds_loc\"] = ds_loc\n", "\n", "            try:\n", "\n", "                orders_in_new_ds[\"osrm_dist\"] = orders_in_new_ds.apply(\n", "                    lambda r: get_km_dist_osrm_z(r[\"new_ds_loc\"], r[\"geometry\"]), axis=1\n", "                )\n", "\n", "            except Exception as e:\n", "\n", "                raise Exception(f\"OSRM failure! please wait some time and re-run\")\n", "\n", "            dd_saving_df = (\n", "                pd.DataFrame(orders_in_new_ds.est_store_to_customer_location_kms.describe())\n", "                .reset_index()\n", "                .merge(\n", "                    pd.DataFrame(orders_in_new_ds[\"osrm_dist\"].describe().reset_index()),\n", "                    on=\"index\",\n", "                )\n", "            )\n", "\n", "            order_mean_dd = dd_saving_df.query(\"index == 'mean'\").osrm_dist.values[0]\n", "\n", "            expected_order_opd = (\n", "                orders_in_new_ds.groupby([\"name\", \"order_create_dt_ist\"], as_index=False)\n", "                .agg({\"cart_id\": \"count\"})\n", "                .groupby([\"name\"], as_index=False)\n", "                .agg({\"cart_id\": \"mean\"})\n", "                .rename(columns={\"cart_id\": \"opd\"})\n", "                .assign(opd=lambda x: round(x[\"opd\"]))\n", "                .opd.values[0]\n", "            )\n", "\n", "            # Calculating orders at hex level\n", "\n", "            orders_poly_data = (\n", "                orders_in_new_ds[\n", "                    [\n", "                        \"frontend_merchant_id\",\n", "                        \"cart_id\",\n", "                        \"order_create_dt_ist\",\n", "                        \"customer_location_latitude\",\n", "                        \"customer_location_longitude\",\n", "                        \"name\",\n", "                        \"new_ds_loc\",\n", "                        \"osrm_dist\",\n", "                    ]\n", "                ]\n", "            ).copy(deep=True)\n", "\n", "            orders_poly_data[\"hex_id\"] = orders_poly_data.apply(\n", "                lambda r: lat_long_to_h3(\n", "                    r[\"customer_location_latitude\"], r[\"customer_location_longitude\"], 9\n", "                ),\n", "                axis=1,\n", "            )\n", "\n", "            orders_hex_data = (\n", "                orders_poly_data.groupby([\"hex_id\", \"name\", \"new_ds_loc\"], as_index=False)\n", "                .agg({\"cart_id\": \"count\", \"osrm_dist\": \"mean\"})\n", "                .reset_index(drop=True)\n", "            )\n", "\n", "            # Calculating Neighbour Metrics\n", "\n", "            neighbour_metrics = (\n", "                neighbour_polygons[[\"merchant_id\", \"name\", \"same_store\"]]\n", "                .astype({\"merchant_id\": \"int\"})\n", "                .merge(\n", "                    orders_in_new_ds.groupby(\n", "                        [\"frontend_merchant_id\", \"order_create_dt_ist\"], as_index=False\n", "                    )\n", "                    .agg({\"cart_id\": \"count\"})\n", "                    .groupby([\"frontend_merchant_id\"], as_index=False)\n", "                    .agg({\"cart_id\": \"mean\"})\n", "                    .rename(\n", "                        columns={\n", "                            \"cart_id\": \"order_split\",\n", "                            \"frontend_merchant_id\": \"merchant_id\",\n", "                        }\n", "                    )\n", "                    .query(\"order_split>10\")\n", "                )\n", "                .assign(order_split=lambda x: round(x[\"order_split\"]))\n", "            )\n", "\n", "            if len(neighbour_metrics) != 0:\n", "\n", "                area_intersection = neighbour_polygons.merge(df[[\"geometry\"]], how=\"cross\")\n", "                area_intersection[\"new_polygons\"] = area_intersection.apply(\n", "                    lambda r: r[\"polygon\"].difference(r[\"geometry\"]), axis=1\n", "                )\n", "\n", "                subtracted_area = gpd.GeoDataFrame(\n", "                    area_intersection[[\"merchant_id\", \"location\", \"new_polygons\"]],\n", "                    geometry=\"new_polygons\",\n", "                    crs=4326,\n", "                )\n", "\n", "                orders_in_subtracted_area = order_df.sjoin(subtracted_area)\n", "\n", "                try:\n", "\n", "                    orders_in_subtracted_area[\"osrm_dist\"] = orders_in_subtracted_area.apply(\n", "                        lambda r: (\n", "                            r[\"est_store_to_customer_location_kms\"]\n", "                            if r[\"frontend_merchant_id\"] == r[\"merchant_id\"]\n", "                            else get_km_dist_osrm_z(r[\"location\"], r[\"geometry\"])\n", "                        ),\n", "                        axis=1,\n", "                    )\n", "\n", "                except Exception as e:\n", "                    raise Exception(f\"OSRM failure! please wait some time and re-run\")\n", "\n", "                neighbour_opd_dd_change = (\n", "                    orders_in_subtracted_area.groupby(\"merchant_id\", as_index=False)\n", "                    .agg({\"osrm_dist\": \"mean\"})\n", "                    .merge(\n", "                        order_df.groupby(\"frontend_merchant_id\", as_index=False)\n", "                        .agg({\"est_store_to_customer_location_kms\": \"mean\"})\n", "                        .rename(columns={\"frontend_merchant_id\": \"merchant_id\"}),\n", "                        on=\"merchant_id\",\n", "                    )\n", "                    .merge(neighbour_polygons[[\"merchant_id\", \"name\"]], on=\"merchant_id\")\n", "                    .rename(\n", "                        columns={\n", "                            \"osrm_dist\": \"new_avg_dd\",\n", "                            \"est_store_to_customer_location_kms\": \"old_avg_dd\",\n", "                        }\n", "                    )\n", "                )\n", "\n", "                neighbour_metrics = neighbour_metrics.merge(neighbour_opd_dd_change)\n", "\n", "                subtracted_area[\"new_area_sqkm\"] = subtracted_area[\"new_polygons\"].apply(\n", "                    get_polygon_area\n", "                )\n", "                subtracted_area[\"new_area_sqkm\"] = round(subtracted_area[\"new_area_sqkm\"], 2)\n", "                neighbour_polygons[\"old_area_sqkm\"] = neighbour_polygons[\"polygon\"].apply(\n", "                    get_polygon_area\n", "                )\n", "                neighbour_polygons[\"old_area_sqkm\"] = round(neighbour_polygons[\"old_area_sqkm\"], 2)\n", "\n", "                subtracted_area = subtracted_area.merge(\n", "                    neighbour_polygons[[\"merchant_id\", \"old_area_sqkm\"]]\n", "                )\n", "                neighbour_metrics = neighbour_metrics.merge(\n", "                    subtracted_area[[\"merchant_id\", \"new_area_sqkm\", \"old_area_sqkm\"]]\n", "                )\n", "\n", "                # Change Made Here in .merge() condition\n", "\n", "                opd_diff = (\n", "                    orders_in_subtracted_area.groupby(\n", "                        [\"merchant_id\", \"order_create_dt_ist\"], as_index=False\n", "                    )\n", "                    .agg({\"cart_id\": \"count\"})\n", "                    .groupby([\"merchant_id\"], as_index=False)\n", "                    .agg({\"cart_id\": \"mean\"})\n", "                    .rename(columns={\"cart_id\": \"new_opd\"})\n", "                    .merge(\n", "                        neighbour_polygons.sjoin(order_df)\n", "                        .groupby([\"merchant_id\", \"order_create_dt_ist\"], as_index=False)\n", "                        .agg({\"cart_id\": \"count\"})\n", "                        .groupby(\"merchant_id\", as_index=False)\n", "                        .agg({\"cart_id\": \"mean\"})\n", "                        .rename(columns={\"cart_id\": \"old_opd\"})\n", "                    )\n", "                    .assign(\n", "                        new_opd=lambda x: round(x[\"new_opd\"]),\n", "                        old_opd=lambda x: round(x[\"old_opd\"]),\n", "                    )\n", "                )\n", "\n", "                neighbour_metrics = neighbour_metrics.merge(opd_diff)\n", "    except Exception as e:\n", "        print(\"Request Failed\")\n", "        print(str(e))\n", "        should_stop()\n", "\n", "        if not DEBUG_MODE:\n", "            move_to_error_state(run_request_id, str(e))"]}, {"cell_type": "markdown", "id": "3a296076-ac3f-418a-943e-624b83a64f15", "metadata": {}, "source": ["## Checking NSA Data"]}, {"cell_type": "code", "execution_count": null, "id": "3b7a9a42-e746-4523-8396-7042d51fb548", "metadata": {}, "outputs": [], "source": ["if should_continue():\n", "    try:\n", "        unique_nsa_pings_28_days = 0\n", "\n", "        if percent_nsa_area > 5.0:\n", "\n", "            nsa_data = get_nsa_data(min_latitude, max_latitude, min_longitude, max_longitude)\n", "\n", "            if len(nsa_data) != 0:\n", "\n", "                unique_nsa_pings_28_days = (\n", "                    nsa_data.sjoin(df)\n", "                    .drop(columns=\"index_right\")\n", "                    .reset_index(drop=True)\n", "                    .device_uuid.nunique()\n", "                )\n", "\n", "        print(unique_nsa_pings_28_days)\n", "\n", "    except Exception as e:\n", "        print(str(e))\n", "        should_stop()\n", "\n", "        if not DEBUG_MODE:\n", "            move_to_error_state(run_request_id, str(e))"]}, {"cell_type": "code", "execution_count": null, "id": "9ac7a044-3f82-4ac5-b37e-4bdd3bff2ae9", "metadata": {}, "outputs": [], "source": ["if should_continue():\n", "    try:\n", "        expected_nsa_opd = 0\n", "\n", "        if unique_nsa_pings_28_days > 250:\n", "\n", "            nsa_poly_data = nsa_data.sjoin(df).drop(columns=\"index_right\").reset_index(drop=True)\n", "\n", "            # Calculating average distance from the new ds location\n", "\n", "            nsa_poly_data[\"store_loc\"] = ds_loc\n", "\n", "            try:\n", "\n", "                nsa_poly_data[\"osrm_dist\"] = nsa_poly_data.apply(\n", "                    lambda r: get_km_dist_osrm_z(r[\"store_loc\"], r[\"geometry\"]), axis=1\n", "                )\n", "\n", "            except Exception as e:\n", "\n", "                raise Exception(f\"OSRM failure! please wait some time and re-run\")\n", "\n", "            # Calculating Expected OPD from NSA assuming conversion = conversion_ratio\n", "\n", "            unique_nsa_pings = nsa_poly_data.device_uuid.nunique()\n", "            expected_nsa_opd = unique_nsa_pings * conversion_ratio\n", "\n", "            # Adding Hex Id to NSA Data\n", "\n", "            nsa_poly_data[\"hex_id\"] = nsa_poly_data.apply(\n", "                lambda r: lat_long_to_h3(r[\"latitude\"], r[\"longitude\"], 9), axis=1\n", "            )\n", "\n", "            nsa_hex_data = nsa_poly_data.copy(deep=True)\n", "\n", "            # Calculating Mean DD by only NSA Data\n", "\n", "            nsa_hex_data = (\n", "                nsa_hex_data.groupby([\"hex_id\", \"name\", \"store_loc\"], as_index=False)\n", "                .agg({\"device_uuid\": \"nunique\", \"osrm_dist\": \"mean\"})\n", "                .sort_values([\"osrm_dist\", \"device_uuid\"], ascending=[True, False])\n", "                .reset_index(drop=True)\n", "            )\n", "\n", "            nsa_mean_dd = nsa_hex_data.osrm_dist.describe().loc[\"mean\"]\n", "        print(expected_nsa_opd)\n", "    except Exception as e:\n", "        print(\"Request Failed\")\n", "        print(str(e))\n", "        should_stop()\n", "\n", "        if not DEBUG_MODE:\n", "            move_to_error_state(run_request_id, str(e))"]}, {"cell_type": "code", "execution_count": null, "id": "d7bf25a0-5770-48db-b945-a916e3aa6b6f", "metadata": {}, "outputs": [], "source": ["if should_continue():\n", "    try:\n", "        if len(orders_in_new_ds) != 0 and unique_nsa_pings_28_days > 250:\n", "\n", "            # Calculating Mean DD by using both Orders & NSA Data\n", "\n", "            combined_hex_data = nsa_hex_data.merge(orders_hex_data, on=\"hex_id\", how=\"outer\")\n", "\n", "            combined_hex_data = combined_hex_data[\n", "                [\n", "                    \"hex_id\",\n", "                    \"name_x\",\n", "                    \"store_loc\",\n", "                    \"device_uuid\",\n", "                    \"osrm_dist_x\",\n", "                    \"cart_id\",\n", "                    \"osrm_dist_y\",\n", "                ]\n", "            ].rename(\n", "                columns={\n", "                    \"name_x\": \"store_name\",\n", "                    \"osrm_dist_x\": \"nsa_osrm_dist\",\n", "                    \"osrm_dist_y\": \"order_osrm_dist\",\n", "                }\n", "            )\n", "\n", "            combined_hex_data[\"comb_osrm_dist\"] = combined_hex_data[\"order_osrm_dist\"].fillna(\n", "                combined_hex_data[\"nsa_osrm_dist\"]\n", "            )\n", "\n", "            combined_mean_dd = combined_hex_data.comb_osrm_dist.describe().loc[\"mean\"]\n", "    except Exception as e:\n", "        print(str(e))\n", "        should_stop()\n", "\n", "        if not DEBUG_MODE:\n", "            move_to_error_state(run_request_id, str(e))"]}, {"cell_type": "code", "execution_count": null, "id": "301c018c-4179-44af-ad78-80573e37a9ec", "metadata": {}, "outputs": [], "source": ["if should_continue():\n", "    try:\n", "        if len(orders_in_new_ds) != 0:\n", "            total_expected_opd = expected_order_opd + expected_nsa_opd\n", "            percent_nsa_contri = (expected_nsa_opd / total_expected_opd) * 100\n", "\n", "            if percent_nsa_contri >= 20.00:\n", "                mean_dd = combined_mean_dd\n", "            else:\n", "                mean_dd = order_mean_dd\n", "\n", "        else:\n", "            mean_dd = nsa_mean_dd\n", "    except Exception as e:\n", "        print(str(e))\n", "        should_stop()\n", "\n", "        if not DEBUG_MODE:\n", "            move_to_error_state(run_request_id, str(e))"]}, {"cell_type": "code", "execution_count": null, "id": "50a5b242-2898-44f9-84c1-2147a1c00df3", "metadata": {}, "outputs": [], "source": ["if should_continue():\n", "    try:\n", "        new_polygon_metrics = df.copy()\n", "\n", "        if len(neighbour_metrics) != 0:\n", "\n", "            output_df = (\n", "                neighbour_metrics[[\"merchant_id\", \"name\", \"order_split\", \"new_avg_dd\"]]\n", "                .assign(order_split=lambda r: r[\"order_split\"] * -1)\n", "                .sort_values([\"order_split\"], ascending=[True])\n", "                .reset_index(drop=True)\n", "                .rename(\n", "                    columns={\n", "                        \"order_split\": \"estimated_opd_change\",\n", "                        \"new_avg_dd\": \"estimated_dd\",\n", "                    }\n", "                )\n", "            )\n", "\n", "            new_polygon_metrics[\"merchant_id\"] = \"\"\n", "            new_polygon_metrics[\"estimated_opd_change\"] = expected_order_opd + expected_nsa_opd\n", "            new_polygon_metrics[\"estimated_dd\"] = mean_dd\n", "\n", "            new_polygon_metrics = new_polygon_metrics[\n", "                [\"merchant_id\", \"name\", \"estimated_opd_change\", \"estimated_dd\"]\n", "            ]\n", "\n", "            new_polygon_metrics = new_polygon_metrics.append(output_df)\n", "\n", "            new_polygon_metrics = new_polygon_metrics.sort_values(\n", "                \"estimated_opd_change\", ascending=False\n", "            ).reset_index(drop=True)\n", "\n", "        else:\n", "\n", "            new_polygon_metrics[\"merchant_id\"] = \"\"\n", "            new_polygon_metrics[\"estimated_opd_change\"] = expected_nsa_opd\n", "            new_polygon_metrics[\"estimated_dd\"] = mean_dd\n", "\n", "            new_polygon_metrics = new_polygon_metrics[\n", "                [\"merchant_id\", \"name\", \"estimated_opd_change\", \"estimated_dd\"]\n", "            ]\n", "\n", "        print(new_polygon_metrics)\n", "    except Exception as e:\n", "        print(str(e))\n", "        should_stop()\n", "\n", "        if not DEBUG_MODE:\n", "            move_to_error_state(run_request_id, str(e))"]}, {"cell_type": "code", "execution_count": null, "id": "46217777-32ee-4584-a50f-66556b63d61f", "metadata": {}, "outputs": [], "source": ["if should_continue():\n", "    try:\n", "        updated_neighbour_polygons_list = []\n", "        if len(neighbour_metrics) != 0:\n", "\n", "            updated_neighbour_polygons = subtracted_area[\n", "                [\"merchant_id\", \"location\", \"new_polygons\"]\n", "            ].merge(\n", "                neighbour_polygons[[\"merchant_id\", \"name\", \"city_name\"]],\n", "                how=\"inner\",\n", "                on=\"merchant_id\",\n", "            )\n", "\n", "            updated_neighbour_polygons = updated_neighbour_polygons[\n", "                [\"merchant_id\", \"name\", \"city_name\", \"location\", \"new_polygons\"]\n", "            ]\n", "\n", "            # updated_neighbour_polygons\n", "            updated_neighbour_polygons_list\n", "\n", "            for i in range(len(updated_neighbour_polygons)):\n", "                temp_gdf = updated_neighbour_polygons[updated_neighbour_polygons.index == i][\n", "                    [\"merchant_id\", \"name\", \"new_polygons\"]\n", "                ]\n", "                updated_neighbour_polygons_list.append(temp_gdf)\n", "    except Exception as e:\n", "        print(str(e))\n", "        should_stop()\n", "\n", "        if not DEBUG_MODE:\n", "            move_to_error_state(run_request_id, str(e))"]}, {"cell_type": "code", "execution_count": null, "id": "ddea5bef-22aa-48d4-a9b7-13ff95c3de22", "metadata": {}, "outputs": [], "source": ["if should_continue():\n", "    try:\n", "        if DEBUG_MODE:\n", "            print(\"Stopping Execution | Debug Mode is enabled\")\n", "        else:\n", "            move_to_completed_state(\n", "                run_request_id,\n", "                [\n", "                    {\n", "                        \"result\": {\"projection\": new_polygon_metrics.to_dict(\"records\")},\n", "                        \"result_type\": \"POLYGON_PROJECTION_DATA\",\n", "                    }\n", "                ],\n", "            )\n", "    except Exception as e:\n", "        print(str(e))\n", "        should_stop()\n", "\n", "        if not DEBUG_MODE:\n", "            move_to_error_state(run_request_id, str(e))"]}, {"cell_type": "code", "execution_count": null, "id": "fcf9aa2e-44b7-40eb-bc23-b112cfd0f51b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}