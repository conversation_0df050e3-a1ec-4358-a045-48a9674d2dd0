alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: opd_dd_estimation_kml
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U06G6N2CA4Q
path: data_science/network_intelligence/etl/opd_dd_estimation_kml
paused: false
pool: data_science_pool
project_name: network_intelligence
schedule:
  end_date: '2025-09-01T00:00:00'
  interval: 10 1 1 1 *
  start_date: '2025-03-10T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 4
