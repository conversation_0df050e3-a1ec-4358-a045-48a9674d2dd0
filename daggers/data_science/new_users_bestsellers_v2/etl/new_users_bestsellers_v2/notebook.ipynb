{"cells": [{"cell_type": "code", "execution_count": null, "id": "9af00853-d9b4-4911-927b-f02468a86860", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys"]}, {"cell_type": "code", "execution_count": null, "id": "63602718-fba3-47f6-ac5b-5bb8d6297b92", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "4fe505c8-3630-48b9-91e6-7f9005de75ab", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "0171c8d5-cdd1-4e59-a465-8808cebca3d4", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import json\n", "import random\n", "import string\n", "from datetime import datetime\n", "from pytz import timezone\n", "from kafka_utils import *"]}, {"cell_type": "code", "execution_count": null, "id": "4d0147c8-aeca-4ed8-844d-400d327cc080", "metadata": {}, "outputs": [], "source": ["!pip install -q tabulate\n", "from tabulate import tabulate"]}, {"cell_type": "code", "execution_count": null, "id": "ac251872-94a2-4a8e-a76f-68a4d6346109", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "ea6fe256-1d16-4c6d-b21f-8e5a34fdc08b", "metadata": {}, "outputs": [], "source": ["blacklist = pb.from_sheets(\"17Q4P7HGhYLMnW4AqRWL4Jh_a5nT5DCeuK176CM2jgoE\", \"Blacklist_v2\")\n", "blacklist = blacklist[blacklist[\"widget\"].isin([\"Global\", \"New User Bestsellers\"])]\n", "\n", "\n", "# blacklist = pd.read_csv(\"blacklist.csv\")\n", "def checknull(st):\n", "    if len(st) == 0:\n", "        st = st + \"0\"\n", "    return st\n", "\n", "\n", "b_l0 = checknull(\",\".join([x for x in list(blacklist[\"l0\"].values) if x != \"\"]))\n", "b_l1 = checknull(\",\".join([x for x in list(blacklist[\"l1\"].values) if x != \"\"]))\n", "b_pids = checknull(\",\".join([x for x in list(blacklist[\"pid\"].values) if x != \"\"]))\n", "b_ptype_ids = checknull(\",\".join([x for x in list(blacklist[\"ptype\"].values) if x != \"\"]))\n", "print(\"\\n L0: \", b_l0, \"\\n L1: \", b_l1, \"\\n PIDs: \", b_pids, \"\\n Ptypes: \", b_ptype_ids)"]}, {"cell_type": "markdown", "id": "274d89c2-a1e6-45fd-a110-239dce8c1876", "metadata": {"tags": []}, "source": ["# New Revamped Version"]}, {"cell_type": "markdown", "id": "ff0fff94-08e4-4119-98d1-eab8f0facb1a", "metadata": {"tags": []}, "source": ["## Getting group id to names mapping"]}, {"cell_type": "code", "execution_count": null, "id": "63810134-87ae-44e0-84cf-247a4ef23952", "metadata": {}, "outputs": [], "source": ["ptype_mappings = pb.from_sheets(\"16b-AbubMta4MjxsrzFkDK_JyIrdRQznobTwrKNwGjck\", \"Master\")"]}, {"cell_type": "code", "execution_count": null, "id": "b580fb18-5172-4d13-9053-7d5efdadabf4", "metadata": {}, "outputs": [], "source": ["ptype_mappings = ptype_mappings.melt(\n", "    id_vars=[\"Group Name\"],\n", "    value_vars=ptype_mappings.drop(columns=[\"Group Name\", \"master_ptype_id\"]).columns.tolist(),\n", ").reset_index(drop=True)\n", "ptype_mappings.drop(columns=[\"variable\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "5fa5abb9-d118-4e7f-b559-ce0059b89e01", "metadata": {}, "outputs": [], "source": ["ptype_mappings = ptype_mappings.drop_duplicates()\n", "ptype_mappings = ptype_mappings.dropna(subset=[\"value\"])\n", "ptype_mappings = ptype_mappings[ptype_mappings[\"value\"] != \"\"]"]}, {"cell_type": "code", "execution_count": null, "id": "5e0360d9-2b6b-437a-99ab-e210de3f77f5", "metadata": {}, "outputs": [], "source": ["ptype_mappings[\"product_type\"] = ptype_mappings[\"value\"].apply(lambda x: x.split(\"||\")[0].strip())\n", "ptype_mappings[\"product_type_id\"] = ptype_mappings[\"value\"].apply(\n", "    lambda x: int(x.split(\"||\")[1].strip())\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0aa862d2-7218-4569-9bb3-76e64a3abb00", "metadata": {}, "outputs": [], "source": ["ptype_mappings.drop(columns=[\"value\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "5142cc10-615e-440f-bee6-446fbba730c8", "metadata": {}, "outputs": [], "source": ["prod_info = pd.read_sql(\n", "    \"\"\"\n", "select\n", "    product_id,\n", "    product_type_id,\n", "    product_type,\n", "    l0_category_id,\n", "    l1_category_id\n", "    from dwh.dim_product\n", "where is_current\n", "    and is_product_enabled\n", "\"\"\",\n", "    con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c9159049-dc5f-47a1-a06c-9b15265008b3", "metadata": {}, "outputs": [], "source": ["prod_info = pd.merge(prod_info, ptype_mappings, on=[\"product_type_id\", \"product_type\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "61fa877a-5bb8-43c5-94e7-64eb0bdadb19", "metadata": {}, "outputs": [], "source": ["# prod_info['group_name'] = prod_info['product_type']\n", "# prod_info['group_id'] = prod_info['product_type_id']"]}, {"cell_type": "code", "execution_count": null, "id": "7edd54b8-f18a-4f94-93e8-5324bae07157", "metadata": {}, "outputs": [], "source": ["prod_info[\"group_name\"] = prod_info[\"Group Name\"].combine_first(prod_info[\"product_type\"])\n", "# prod_info[\"group_id\"] = prod_info[\"master_ptype_id\"].combine_first(\n", "#     prod_info[\"product_type_id\"]\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "86255b65-0ef4-409f-9488-14582ec1d7c9", "metadata": {}, "outputs": [], "source": ["prod_info.drop(columns=[\"Group Name\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "d260f039-b406-4e30-a402-f250403b93ac", "metadata": {}, "outputs": [], "source": ["prod_info.head()"]}, {"cell_type": "markdown", "id": "a63220e8-27af-4589-84f9-cdf5aea1ed58", "metadata": {}, "source": ["## Product metadata"]}, {"cell_type": "code", "execution_count": null, "id": "517c04ee-bcd7-4b6d-a234-6c7fed573723", "metadata": {}, "outputs": [], "source": ["sql_product_name = \"\"\"\n", "select\n", "    product_id,\n", "    product_name\n", "    from dwh.dim_product\n", "        as dp\n", "where is_current\n", "    and is_product_enabled\n", "\"\"\"\n", "df_product_name = pd.read_sql(sql_product_name, con)"]}, {"cell_type": "markdown", "id": "90138323-39ed-4327-86cd-61e5af89e4d8", "metadata": {}, "source": ["## Product sales"]}, {"cell_type": "code", "execution_count": null, "id": "1aaa43e0-3bdf-45ba-be2c-7258126466c7", "metadata": {"tags": []}, "outputs": [], "source": ["merchants = pd.read_sql_query(\n", "    f\"\"\"\n", "select\n", "    f.frontend_merchant_id,\n", "    f.city_name,\n", "    count(distinct f.order_id) as orders\n", "from dwh.fact_sales_order_details f\n", "where order_create_dt_ist >= current_date - interval '7' day\n", "and f.is_internal_order = False and f.frontend_merchant_id != -1\n", "group by 1,2\"\"\",\n", "    con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b8fc61bd-0e9b-4c88-b167-c5556e3e8cbf", "metadata": {}, "outputs": [], "source": ["fms = list(merchants[merchants.orders >= 1000].frontend_merchant_id.unique())\n", "fms = \",\".join([str(x) for x in fms])"]}, {"cell_type": "code", "execution_count": null, "id": "ea6a5e28-f2cb-4cec-838a-37cc35c2890d", "metadata": {}, "outputs": [], "source": ["sql_orders_data = \"\"\"\n", "select\n", "    fsoid.frontend_merchant_id,\n", "    fsoid.product_id,\n", "    dp.product_type_id,\n", "    case when hour(fsoid.cart_checkout_ts_ist) >= 6 and hour(fsoid.cart_checkout_ts_ist) < 12 then 'morning'\n", "         when hour(fsoid.cart_checkout_ts_ist) >= 12 and hour(fsoid.cart_checkout_ts_ist) < 18 then 'afternoon'\n", "         when hour(fsoid.cart_checkout_ts_ist) >= 18 and hour(fsoid.cart_checkout_ts_ist) < 23 then 'evening'\n", "         else 'night' end as timeblock,\n", "    count(distinct fsoid.order_id) as orders_count,\n", "    avg(unit_selling_price) as average_price\n", "    from dwh.fact_sales_order_item_details as fsoid\n", "    inner join dwh.fact_sales_order_details f on f.order_id = fsoid.order_id \n", "    inner join dwh.dim_product\n", "        as dp\n", "        on fsoid.product_id = dp.product_id\n", "        and dp.is_current\n", "        and dp.is_product_enabled\n", "where fsoid.order_create_dt_ist >= current_date - interval '14' day\n", "    and f.order_create_dt_ist >= current_date - interval '14' day\n", "    and dp.l1_category_id not in ({l1_ids})\n", "    and dp.l0_category_id not in ({l0_ids})\n", "    and dp.product_type_id not in ({p_type_ids})\n", "    and dp.product_id not in ({pids})\n", "    and f.frontend_merchant_id != -1\n", "    and f.frontend_merchant_id in ({fms})\n", "    and f.is_internal_order = False\n", "group by 1,2,3,4\n", "order by 1,4 asc, 5 desc\n", "\"\"\".format(\n", "    l0_ids=b_l0, l1_ids=b_l1, pids=b_pids, p_type_ids=b_ptype_ids, fms=fms\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ca8dae69-6244-4bc4-8121-615ae678d148", "metadata": {"tags": []}, "outputs": [], "source": ["df_product_sales = pd.read_sql(sql_orders_data, con)"]}, {"cell_type": "code", "execution_count": null, "id": "c4284fe8-ce2f-4fce-b280-cfc8f16d38cf", "metadata": {}, "outputs": [], "source": ["df_product_sales.head()"]}, {"cell_type": "code", "execution_count": null, "id": "96cfb0de-147e-449d-bef4-7cc188202521", "metadata": {}, "outputs": [], "source": ["cities = merchants.groupby(\"city_name\").orders.sum().reset_index()\n", "cities = \",\".join(\n", "    [\"'\" + str(x) + \"'\" for x in list(cities[cities.orders >= 1000].city_name.unique())]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d27de109-5197-4453-85f7-71769faa29fe", "metadata": {}, "outputs": [], "source": ["cities"]}, {"cell_type": "code", "execution_count": null, "id": "aec1fe8e-8027-4a65-b46e-e2e2a66ec109", "metadata": {}, "outputs": [], "source": ["city_level = pd.read_sql(\n", "    \"\"\"\n", "select\n", "    dm.city_id frontend_merchant_id,\n", "    fsoid.product_id,\n", "    dp.product_type_id,\n", "    case when hour(fsoid.cart_checkout_ts_ist) >= 6 and hour(fsoid.cart_checkout_ts_ist) < 12 then 'morning'\n", "         when hour(fsoid.cart_checkout_ts_ist) >= 12 and hour(fsoid.cart_checkout_ts_ist) < 18 then 'afternoon'\n", "         when hour(fsoid.cart_checkout_ts_ist) >= 18 and hour(fsoid.cart_checkout_ts_ist) < 23 then 'evening'\n", "         else 'night' end as timeblock,\n", "    count(distinct fsoid.order_id) as orders_count,\n", "    avg(unit_selling_price) as average_price\n", "    from dwh.fact_sales_order_item_details as fsoid\n", "    inner join dwh.fact_sales_order_details f on f.order_id = fsoid.order_id\n", "    inner join dwh.dim_product\n", "        as dp\n", "        on fsoid.product_id = dp.product_id\n", "        and dp.is_current\n", "        and dp.is_product_enabled\n", "    join dwh.dim_merchant dm on dm.merchant_id = fsoid.frontend_merchant_id\n", "    and dm.is_current\n", "where fsoid.order_create_dt_ist >= current_date - interval '14' day\n", "    and f.order_create_dt_ist >= current_date - interval '14' day\n", "    and dp.l1_category_id not in ({l1_ids})\n", "    and dp.l0_category_id not in ({l0_ids})\n", "    and dp.product_type_id not in ({p_type_ids})\n", "    and dp.product_id not in ({pids})\n", "    and f.city_name in ({cities})\n", "    and fsoid.frontend_merchant_id != -1\n", "group by 1,2,3,4\n", "order by 1,4 asc, 5 desc\n", "\"\"\".format(\n", "        l0_ids=b_l0, l1_ids=b_l1, pids=b_pids, p_type_ids=b_ptype_ids, cities=cities\n", "    ),\n", "    con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6c8214c2-e8a4-4deb-9783-1c29c7dd116a", "metadata": {}, "outputs": [], "source": ["city_level.head()"]}, {"cell_type": "code", "execution_count": null, "id": "446cf8e8-0893-44d2-8bba-83cb8774961b", "metadata": {}, "outputs": [], "source": ["pan_ind_level = pd.read_sql(\n", "    \"\"\"\n", "select\n", "    0 frontend_merchant_id,\n", "    fsoid.product_id,\n", "    dp.product_type_id,\n", "    case when hour(fsoid.cart_checkout_ts_ist) >= 6 and hour(fsoid.cart_checkout_ts_ist) < 12 then 'morning'\n", "         when hour(fsoid.cart_checkout_ts_ist) >= 12 and hour(fsoid.cart_checkout_ts_ist) < 18 then 'afternoon'\n", "         when hour(fsoid.cart_checkout_ts_ist) >= 18 and hour(fsoid.cart_checkout_ts_ist) < 23 then 'evening'\n", "         else 'night' end as timeblock,\n", "    count(distinct fsoid.order_id) as orders_count,\n", "    avg(unit_selling_price) as average_price\n", "    from dwh.fact_sales_order_item_details as fsoid\n", "    inner join dwh.fact_sales_order_details f on f.order_id = fsoid.order_id\n", "    inner join dwh.dim_product\n", "        as dp\n", "        on fsoid.product_id = dp.product_id\n", "        and dp.is_current\n", "        and dp.is_product_enabled\n", "    join dwh.dim_merchant dm on dm.merchant_id = fsoid.frontend_merchant_id\n", "    and dm.is_current\n", "where fsoid.order_create_dt_ist >= current_date - interval '14' day\n", "    and f.order_create_dt_ist >= current_date - interval '14' day\n", "    and dp.l1_category_id not in ({l1_ids})\n", "    and dp.l0_category_id not in ({l0_ids})\n", "    and dp.product_type_id not in ({p_type_ids})\n", "    and dp.product_id not in ({pids})\n", "    and fsoid.frontend_merchant_id != -1\n", "group by 1,2,3,4\n", "order by 1,4 asc, 5 desc\n", "\"\"\".format(\n", "        l0_ids=b_l0, l1_ids=b_l1, pids=b_pids, p_type_ids=b_ptype_ids\n", "    ),\n", "    con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c547718a-801f-4202-a7f3-632440c1bebd", "metadata": {}, "outputs": [], "source": ["pan_ind_level.head()"]}, {"cell_type": "code", "execution_count": null, "id": "5ad0de5f-d01c-4913-8699-c864113a20ad", "metadata": {}, "outputs": [], "source": ["df_product_sales = pd.concat([df_product_sales, city_level, pan_ind_level], axis=0)"]}, {"cell_type": "code", "execution_count": null, "id": "ae6150bb-662f-4c1f-945f-545fb8b3057b", "metadata": {}, "outputs": [], "source": ["df_product_sales.frontend_merchant_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "d568896e-e810-42f4-a19e-eac25b48d96f", "metadata": {}, "outputs": [], "source": ["df_product_sales[\"final_rank\"] = (\n", "    df_product_sales.groupby([\"frontend_merchant_id\", \"timeblock\"]).cumcount() + 1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b9f30275-6fc2-40b8-8557-16b1020efcdd", "metadata": {}, "outputs": [], "source": ["df_product_sales = df_product_sales.merge(\n", "    prod_info[[\"product_id\", \"group_name\", \"l1_category_id\", \"l0_category_id\"]],\n", "    on=\"product_id\",\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8967cd2c-f1d8-4c2c-9cfb-449f5895287a", "metadata": {}, "outputs": [], "source": ["df_product_sales[\"within_group_rank\"] = (\n", "    df_product_sales.groupby([\"frontend_merchant_id\", \"timeblock\", \"group_name\"]).cumcount() + 1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d96ead71-0649-4eb3-af3f-ad69ed69b437", "metadata": {}, "outputs": [], "source": ["df_product_sales[df_product_sales[\"group_name\"].isna()].shape, df_product_sales.shape"]}, {"cell_type": "markdown", "id": "a3970020-9093-4445-8b53-00fd9c85f357", "metadata": {}, "source": ["## Group ranking & adding diversity score to groups"]}, {"cell_type": "code", "execution_count": null, "id": "f1194dbe-9eae-4747-9b52-662aa5963fa3", "metadata": {}, "outputs": [], "source": ["group_ranking = (\n", "    df_product_sales.groupby([\"frontend_merchant_id\", \"timeblock\", \"group_name\"])\n", "    .final_rank.min()\n", "    .reset_index()\n", ")\n", "\n", "group_ranking[\"rank\"] = group_ranking.groupby([\"frontend_merchant_id\", \"timeblock\"])[\n", "    \"final_rank\"\n", "].rank(ascending=True, method=\"first\")\n", "\n", "# group_ranking = group_ranking.merge(\n", "#     l1_diversity[[\"group_id\", \"timeblock\", \"product_id\"]].rename(\n", "#         columns={\"product_id\": \"product_count\"}\n", "#     ),\n", "#     how=\"left\",\n", "#     on=[\"group_id\", \"timeblock\"],\n", "# )\n", "\n", "group_ranking = group_ranking.sort_values(by=[\"frontend_merchant_id\", \"timeblock\", \"rank\"])"]}, {"cell_type": "code", "execution_count": null, "id": "338ed590-dbcc-4a86-add7-52d9a8705a67", "metadata": {}, "outputs": [], "source": ["group_ranking.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6b88e505-6733-4e3f-bd1f-209cd790162b", "metadata": {}, "outputs": [], "source": ["final = group_ranking.merge(\n", "    df_product_sales[\n", "        [\n", "            \"frontend_merchant_id\",\n", "            \"timeblock\",\n", "            \"product_id\",\n", "            \"group_name\",\n", "            \"orders_count\",\n", "            \"within_group_rank\",\n", "            \"l1_category_id\",\n", "            \"l0_category_id\",\n", "        ]\n", "    ],\n", "    on=[\"frontend_merchant_id\", \"group_name\", \"timeblock\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "35639c40-94b1-4a54-8103-d8eec9534a88", "metadata": {}, "outputs": [], "source": ["final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "77d1fe63-1a52-48b4-b734-87d5e0ff7b6d", "metadata": {}, "outputs": [], "source": ["final = final.sort_values([\"frontend_merchant_id\", \"timeblock\", \"within_group_rank\", \"rank\"])"]}, {"cell_type": "code", "execution_count": null, "id": "51091d4c-d4fc-4581-89a8-d96f6d0edab9", "metadata": {}, "outputs": [], "source": ["final[\"product_rank\"] = final.groupby([\"frontend_merchant_id\", \"timeblock\"]).cumcount() + 1"]}, {"cell_type": "code", "execution_count": null, "id": "2c948c1b-3ad5-41ee-aae3-57b9a2d7fb54", "metadata": {}, "outputs": [], "source": ["final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "03f9093e-a97a-4c8b-893f-2beb2f33b866", "metadata": {}, "outputs": [], "source": ["final = final[final[\"product_rank\"] <= 200]"]}, {"cell_type": "markdown", "id": "e7ba1621-9d9d-4bf6-8a00-056ea2d38108", "metadata": {}, "source": ["# FS Push"]}, {"cell_type": "code", "execution_count": null, "id": "ddb8b0f4-958b-402f-89de-23ade09429ec", "metadata": {}, "outputs": [], "source": ["gbs = final.groupby([\"frontend_merchant_id\", \"timeblock\"])[\"product_id\"].apply(list).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "846e7775-bc4e-47ba-9e6d-0a7f8b56cfe8", "metadata": {}, "outputs": [], "source": ["gbs.columns = [\"frontend_merchant_id\", \"tod\", \"product_ids\"]"]}, {"cell_type": "code", "execution_count": null, "id": "d0a34434-9052-41f9-b9f2-da0a1b195d45", "metadata": {}, "outputs": [], "source": ["gbs1 = gbs.groupby(\"frontend_merchant_id\").agg({\"tod\": list, \"product_ids\": list}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "8a4619f8-58df-49be-be29-fa80bff1d629", "metadata": {}, "outputs": [], "source": ["# Merge 'tod' and 'pid' lists into a dictionary\n", "gbs1[\"product_id\"] = gbs1.apply(\n", "    lambda row: [\n", "        {\"tod\": tod, \"product_ids\": pid} for tod, pid in zip(row[\"tod\"], row[\"product_ids\"])\n", "    ],\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e01501d9-de14-4318-9724-04ed48928cba", "metadata": {}, "outputs": [], "source": ["gbs1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "454ce17c-f9d9-4170-bba0-7cb73f494335", "metadata": {}, "outputs": [], "source": ["gbs1.drop(columns=[\"tod\", \"product_ids\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "ec40340a-6297-484d-956e-3f85777193c6", "metadata": {"tags": []}, "outputs": [], "source": ["kafka_records_dict = gbs1.reset_index(drop=True).to_dict(\"records\")\n", "len(kafka_records_dict)"]}, {"cell_type": "code", "execution_count": null, "id": "c31e05ad-9952-4d27-b368-d2b6a642eec1", "metadata": {"tags": []}, "outputs": [], "source": ["def np_encoder(object):\n", "    if isinstance(object, np.generic):\n", "        return object.item()\n", "\n", "\n", "entity_column = \"frontend_merchant_id\"\n", "entity_name = \"frontend\"\n", "context = \"new_users_bestsellers_4_0_0\"\n", "ctx_value_col = \"product_id\"\n", "\n", "push_to_kafka(\n", "    entities=[f\"{entity_name}:{i[entity_column]}\" for i in kafka_records_dict],\n", "    context=context,\n", "    ctx_properties=[\n", "        {\"ctx_value\": json.dumps(i[ctx_value_col], default=np_encoder)} for i in kafka_records_dict\n", "    ],\n", "    dry_run=False,\n", ")"]}, {"cell_type": "markdown", "id": "da433f08-1fa0-4cde-aad2-122d35a1ed6d", "metadata": {}, "source": ["# Bestseller groups"]}, {"cell_type": "code", "execution_count": null, "id": "b16a8216-1141-4ac3-a080-8c765895744f", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import sqlalchemy as sa\n", "import pencilbox as pb\n", "import json\n", "import random\n", "import string\n", "from datetime import datetime\n", "from pytz import timezone"]}, {"cell_type": "code", "execution_count": null, "id": "2c25341d-44dc-4513-af98-2963284d2862", "metadata": {}, "outputs": [], "source": ["!pip install tabulate\n", "from tabulate import tabulate"]}, {"cell_type": "code", "execution_count": null, "id": "7eb4d832-9ed1-489a-9b4f-6ac6a2b7e25c", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "markdown", "id": "dc137d94-52ef-4264-afc1-32bc344998c7", "metadata": {}, "source": ["### Getting group id to names mapping"]}, {"cell_type": "code", "execution_count": null, "id": "a89bdaba-ecfc-4a17-a308-95b3730553d5", "metadata": {"tags": []}, "outputs": [], "source": ["df_sheet_mapping = pb.from_sheets(\n", "    \"1XtlWEB4dxwSRiA4XJNj6tmfyPmFgGnYepoOFvq1iRR0\", \"groups_meta_data\"\n", ")\n", "df_sheet_mapping[\"l1_category_id\"] = df_sheet_mapping[\"l1_category_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "edbfa9b4-0655-4107-84c8-96ba9e92b1af", "metadata": {}, "outputs": [], "source": ["df_sheet_mapping.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "cb7df3c7-91f6-462c-8afe-45fb4510c821", "metadata": {}, "outputs": [], "source": ["## Order again Grouping\n", "df_sheet_mapping = pb.from_sheets(\"1p0N6KkgAQDn8GEi-FzOUBIKgSuMEz9iAWic4rXmVMWg\", \"Prod Sheet\")\n", "df_group_combinations = pb.from_sheets(\n", "    \"1p0N6KkgAQDn8GEi-FzOUBIKgSuMEz9iAWic4rXmVMWg\", \"group_combinations\"\n", ")\n", "sheet_columns = df_sheet_mapping.columns.tolist()\n", "columns_to_consider = [x for x in df_sheet_mapping.columns if \"id_\" in x]\n", "df_sheet_mapping = df_sheet_mapping[df_sheet_mapping[\"discard\"] != \"1\"]\n", "melted_groups = pd.melt(\n", "    df_sheet_mapping, id_vars=[\"group_name\", \"group_id\"], value_vars=columns_to_consider\n", ")\n", "melted_groups = melted_groups[melted_groups[\"value\"] != \"\"]\n", "melted_groups = melted_groups[~melted_groups[\"value\"].isna()]\n", "melted_groups[\"extracted_ptype\"] = melted_groups[\"value\"].apply(lambda x: x.split(\"||\")[1])\n", "melted_groups[\"l1_flag\"] = melted_groups[\"value\"].apply(\n", "    lambda x: 1 if \"(L1)\" in x.split(\"||\")[0] else 0\n", ")\n", "melted_groups = melted_groups[[\"group_name\", \"group_id\", \"extracted_ptype\", \"l1_flag\"]]\n", "melted_groups = melted_groups.merge(df_group_combinations, how=\"left\")\n", "\n", "melted_groups = melted_groups[\n", "    [\"merge_group\", \"merge_group_id\", \"extracted_ptype\", \"l1_flag\"]\n", "].rename(columns={\"merge_group\": \"group_name\", \"merge_group_id\": \"group_id\"})\n", "melted_groups = melted_groups[melted_groups[\"group_id\"] != \"\"]"]}, {"cell_type": "code", "execution_count": null, "id": "76977470-40aa-4f33-a5d8-afc917b2a38c", "metadata": {}, "outputs": [], "source": ["df_product_info = pd.read_sql_query(\n", "    f\"\"\"\n", "    select product_id,\n", "           product_name,\n", "           product_type,\n", "           product_type_id,\n", "           l0_category,\n", "           l0_category_id,\n", "           l1_category,\n", "           l1_category_id\n", "    from dwh.dim_product \n", "    where is_current and is_product_enabled\"\"\",\n", "    pb.get_connection(\"[Warehouse] Trino\"),\n", ")\n", "print(df_product_info.shape)\n", "# df_product_info = df_product_info.merge(\n", "#     ptype_mappings[[\"product_type_id\", \"Group Name\"]],\n", "#     on=[\"product_type_id\"],\n", "#     how=\"left\",\n", "# )\n", "# df_product_info[\"Group Name\"] = np.where(\n", "#     df_product_info[\"Group Name\"].isna(),\n", "#     df_product_info[\"product_type\"],\n", "#     df_product_info[\"Group Name\"],\n", "# )\n", "ptypes = melted_groups.query(\"l1_flag==0\")\n", "l1 = melted_groups.query(\"l1_flag==1\")\n", "l1[\"extracted_ptype\"] = l1[\"extracted_ptype\"].astype(int)\n", "ptypes[\"extracted_ptype\"] = ptypes[\"extracted_ptype\"].astype(int)\n", "ptypes = df_product_info.merge(ptypes, right_on=\"extracted_ptype\", left_on=\"product_type_id\")\n", "l1 = df_product_info.merge(l1, right_on=\"extracted_ptype\", left_on=\"l1_category_id\")\n", "final_check_df = pd.DataFrame()\n", "final_check_df = final_check_df.append(ptypes)\n", "final_check_df = final_check_df.append(l1)\n", "final_check_df = final_check_df.drop_duplicates(subset=[\"product_id\", \"group_id\"], keep=\"first\")"]}, {"cell_type": "code", "execution_count": null, "id": "cf84e377-aab8-476a-9634-03e6f98b9631", "metadata": {}, "outputs": [], "source": ["groups = final_check_df[[\"product_id\", \"group_name\", \"group_id\"]].drop_duplicates()\n", "groups = groups.drop_duplicates(subset=[\"product_id\"], keep=\"first\")"]}, {"cell_type": "code", "execution_count": null, "id": "79e83c95-ec78-4590-834f-e836974c118b", "metadata": {}, "outputs": [], "source": ["groups[\"group_id\"] = groups[\"group_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "70ebf2e6-25d8-4a59-b26b-41339706becd", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"consumer_intelligence_etls\",\n", "    \"table_name\": \"order_again_group_definition\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"product_id\", \"type\": \"INTEGER\", \"description\": \"product_id\"},\n", "        {\"name\": \"group_name\", \"type\": \"Varchar\", \"description\": \"group_name\"},\n", "        {\"name\": \"group_id\", \"type\": \"INTEGER\", \"description\": \"group_id\"},\n", "    ],\n", "    \"primary_key\": [\"product_id\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"Something about the table\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "0617a5a8-d82d-425b-b491-10ee51fd924c", "metadata": {}, "outputs": [], "source": ["pb.to_trino(groups, **kwargs)"]}, {"cell_type": "markdown", "id": "e62d7b99-c63e-49c0-8450-4e5207f6959e", "metadata": {}, "source": ["## Product sales"]}, {"cell_type": "code", "execution_count": null, "id": "c0b86dbd-b29b-4061-a101-d4218a8d8f8e", "metadata": {}, "outputs": [], "source": ["sql_orders_data = \"\"\"\n", "(select\n", "    fsoid.city_name,\n", "    og.group_name,\n", "    case when hour(fsoid.cart_checkout_ts_ist) >= 6 and hour(fsoid.cart_checkout_ts_ist) < 12 then 'morning'\n", "         when hour(fsoid.cart_checkout_ts_ist) >= 12 and hour(fsoid.cart_checkout_ts_ist) < 18 then 'afternoon'\n", "         when hour(fsoid.cart_checkout_ts_ist) >= 18 and hour(fsoid.cart_checkout_ts_ist) < 23 then 'evening'\n", "         else 'night' end as timeblock,\n", "    count(distinct fsoid.order_id) as orders_count,\n", "    avg(unit_selling_price) as average_price\n", "    from dwh.fact_sales_order_item_details as fsoid\n", "    inner join dwh.fact_sales_order_details f on f.order_id = fsoid.order_id\n", "    inner join dwh.dim_product\n", "        as dp\n", "        on fsoid.product_id = dp.product_id\n", "        and dp.is_current\n", "        and dp.is_product_enabled\n", "    inner join consumer_intelligence_etls.order_again_group_definition og on og.product_id = dp.product_id\n", "    inner join lake_cms.gr_group_product_mapping as gpm\n", "      on dp.product_id = gpm.product_id and gpm.enabled_flag = True and gpm.lake_active_record = True\n", "where fsoid.order_create_dt_ist >= current_date - interval '21' day\n", "    and f.order_create_dt_ist >= current_date - interval '21' day\n", "    and dp.l1_category_id not in ({l1_ids})\n", "    and dp.l0_category_id not in ({l0_ids})\n", "    and dp.product_type_id not in ({p_type_ids})\n", "    and dp.product_id not in ({pids})\n", "    and f.city_name in ({cities})\n", "    and fsoid.frontend_merchant_id != -1\n", "group by 1,2,3\n", "order by 1,3 asc,4 desc)\n", "union all\n", "(select\n", "    'pan_india' as city_name,\n", "    og.group_name,\n", "    case when hour(fsoid.cart_checkout_ts_ist) >= 6 and hour(fsoid.cart_checkout_ts_ist) < 12 then 'morning'\n", "         when hour(fsoid.cart_checkout_ts_ist) >= 12 and hour(fsoid.cart_checkout_ts_ist) < 18 then 'afternoon'\n", "         when hour(fsoid.cart_checkout_ts_ist) >= 18 and hour(fsoid.cart_checkout_ts_ist) < 23 then 'evening'\n", "         else 'night' end as timeblock,\n", "    count(distinct fsoid.order_id) as orders_count,\n", "    avg(unit_selling_price) as average_price\n", "    from dwh.fact_sales_order_item_details as fsoid\n", "    inner join dwh.fact_sales_order_details f on f.order_id = fsoid.order_id\n", "    inner join dwh.dim_product\n", "        as dp\n", "        on fsoid.product_id = dp.product_id\n", "        and dp.is_current\n", "        and dp.is_product_enabled\n", "    inner join consumer_intelligence_etls.order_again_group_definition og on og.product_id = dp.product_id\n", "    inner join lake_cms.gr_group_product_mapping as gpm\n", "      on dp.product_id = gpm.product_id and gpm.enabled_flag = True and gpm.lake_active_record = True\n", "where fsoid.order_create_dt_ist >= current_date - interval '21' day\n", "    and f.order_create_dt_ist >= current_date - interval '21' day\n", "    and dp.l1_category_id not in ({l1_ids})\n", "    and dp.l0_category_id not in ({l0_ids})\n", "    and dp.product_type_id not in ({p_type_ids})\n", "    and dp.product_id not in ({pids})\n", "    and fsoid.frontend_merchant_id != -1\n", "group by 1,2,3\n", "order by 1,3 asc,4 desc)\"\"\".format(\n", "    l0_ids=b_l0, l1_ids=b_l1, pids=b_pids, p_type_ids=b_ptype_ids, cities=cities\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8353d81c-4830-4470-809f-69a5063eec67", "metadata": {}, "outputs": [], "source": ["df_product_sales = pd.read_sql(sql_orders_data, con)\n", "print(df_product_sales.city_name.nunique())\n", "df_product_sales.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "96ca024b-2090-4cc3-918c-14865242f402", "metadata": {}, "outputs": [], "source": ["df_sheet_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "dc697b69-fb2b-410f-80a4-e4a72eb57c4c", "metadata": {}, "outputs": [], "source": ["# df_product_sales = df_product_sales.merge(df_sheet_mapping[[\"group_name\", \"l1_category_id\"]])"]}, {"cell_type": "code", "execution_count": null, "id": "10de07ee-e0f0-4402-b6f8-2ffca4143322", "metadata": {}, "outputs": [], "source": ["df_product_sales[\"rank\"] = df_product_sales.groupby([\"city_name\", \"timeblock\"])[\n", "    \"orders_count\"\n", "].rank(ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "082cf483-9060-4587-9d9c-c2b5d0cd4c91", "metadata": {}, "outputs": [], "source": ["df_product_sales = df_product_sales[(df_product_sales[\"rank\"] <= 20)].sort_values(by=[\"rank\"])"]}, {"cell_type": "code", "execution_count": null, "id": "56e273df-0b06-4902-a777-8129b29ee8ca", "metadata": {}, "outputs": [], "source": ["mapping = [\n", "    (\"morning\", \"early_morning\"),\n", "    (\"morning\", \"morning\"),\n", "    (\"morning\", \"afternoon\"),\n", "    (\"afternoon\", \"meal_accompaniment_lunch\"),\n", "    (\"evening\", \"evening\"),\n", "    (\"evening\", \"meal_accompaniment_dinner\"),\n", "    (\"night\", \"night\"),\n", "]\n", "mappings_df = pd.DataFrame(mapping, columns=[\"timeblock\", \"timeblock_new\"])"]}, {"cell_type": "code", "execution_count": null, "id": "8cb5a9ad-a7e8-4f77-83e3-aff1ad0778a6", "metadata": {}, "outputs": [], "source": ["df_product_sales.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bbda6a64-e563-4a20-b972-6bb2d66fc65b", "metadata": {}, "outputs": [], "source": ["# df = pd.read_sql_query(\"\"\"\n", "# select\n", "#     'pan_india' as city_name,\n", "#     dp.l1_category_id,\n", "#     dp.product_id,\n", "#     count(distinct fsoid.order_id) as orders_count\n", "#     from dwh.fact_sales_order_item_details as fsoid\n", "#     inner join dwh.fact_sales_order_details f on f.order_id = fsoid.order_id\n", "#     inner join dwh.dim_product\n", "#         as dp\n", "#         on fsoid.product_id = dp.product_id\n", "#         and dp.is_current\n", "#         and dp.is_product_enabled\n", "#     inner join lake_cms.gr_group_product_mapping as gpm\n", "#       on dp.product_id = gpm.product_id and gpm.enabled_flag = True and gpm.lake_active_record = True\n", "# where fsoid.order_create_dt_ist >= current_date - interval '21' day\n", "#     and f.order_create_dt_ist >= current_date - interval '21' day\n", "#     and dp.l1_category_id not in ({l1_ids})\n", "#     and dp.l0_category_id not in ({l0_ids})\n", "#     and dp.product_type_id not in ({p_type_ids})\n", "#     and dp.product_id not in ({pids})\n", "#     and fsoid.frontend_merchant_id != -1\n", "# group by 1,2,3\n", "# order by 4 desc\"\"\".format(\n", "#     l0_ids = b_l0, l1_ids = b_l1, pids = b_pids, p_type_ids = b_ptype_ids\n", "# ),con)\n", "\n", "# df = df.merge(df_sheet_mapping[['collection','l1_category_id']])\n", "\n", "# df['rank'] = df.groupby(['collection']).orders_count.rank(ascending = False)\n", "\n", "# df_temp = df[df['rank']<=10][['collection','product_id','orders_count']]\n", "\n", "# pb.to_sheets(df_temp,'1XtlWEB4dxwSRiA4XJNj6tmfyPmFgGnYepoOFvq1iRR0','Sheet8')"]}, {"cell_type": "code", "execution_count": null, "id": "21f727e0-fb06-4cee-8164-d87913312bdb", "metadata": {}, "outputs": [], "source": ["city_names = pd.read_sql_query(\n", "    f\"\"\"(select distinct city_id, city_name from dwh.dim_merchant where is_current = True and chain_id = 1383) union all (select 0 as city_id, 'pan_india' as city_name)\"\"\",\n", "    pb.get_connection(\"[Warehouse] Trino\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "62650f19-554f-4330-a850-db0ccb359b96", "metadata": {}, "outputs": [], "source": ["final = df_product_sales.merge(city_names)"]}, {"cell_type": "code", "execution_count": null, "id": "43fdcdb0-47ec-4b72-9e29-eb849ff8fb91", "metadata": {}, "outputs": [], "source": ["final_cities = final[[\"city_id\", \"city_name\", \"group_name\", \"timeblock\", \"rank\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "bd7efe02-5490-456d-a9ce-494cc57c5676", "metadata": {}, "outputs": [], "source": ["df_exploded = final_cities.merge(mappings_df)\n", "df_exploded = df_exploded.drop(columns=[\"timeblock\"])"]}, {"cell_type": "code", "execution_count": null, "id": "1696f6c9-c0fb-496f-86d9-964fd66ddeb8", "metadata": {}, "outputs": [], "source": ["df_exploded = df_exploded.rename(columns={\"timeblock_new\": \"usecase\", \"group_name\": \"group_names\"})"]}, {"cell_type": "code", "execution_count": null, "id": "50cf3792-402a-4941-919b-a08d7191f42c", "metadata": {}, "outputs": [], "source": ["df_exploded.city_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "5f6f20ec-471c-433a-87ab-ee564a1f64a4", "metadata": {}, "outputs": [], "source": ["!pip install re\n", "import re\n", "\n", "\n", "def clean_string(input_string):\n", "    cleaned_string = re.sub(r\"[^a-zA-Z0-9]+\", \"_\", input_string)\n", "    cleaned_string = re.sub(r\"^_+|_+$\", \"\", cleaned_string)\n", "    return cleaned_string.lower()"]}, {"cell_type": "code", "execution_count": null, "id": "431d4741-7592-4bb6-9647-054a54a73c8f", "metadata": {}, "outputs": [], "source": ["df_exploded[\"group_names\"] = df_exploded[\"group_names\"].apply(lambda x: clean_string(x))"]}, {"cell_type": "code", "execution_count": null, "id": "f82e2c7a-cbc3-47c1-b354-6710d68f7c36", "metadata": {}, "outputs": [], "source": ["df_sheet_mapping1 = pb.from_sheets(\n", "    \"1XtlWEB4dxwSRiA4XJNj6tmfyPmFgGnYepoOFvq1iRR0\", \"new_collections\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "42a7271b-19de-4fd0-8754-bed291d87d3f", "metadata": {}, "outputs": [], "source": ["df_sheet_mapping1[\"group_display_name\"] = df_sheet_mapping1[\"group_display_name\"].apply(\n", "    lambda x: clean_string(x)\n", ")\n", "df_sheet_mapping1[\"collection_uuid\"] = df_sheet_mapping1[\"see_all_deeplink\"].apply(\n", "    lambda x: x.split(\"collection_uuid=\")[1]\n", ")\n", "df_sheet_mapping1 = df_sheet_mapping1.rename(columns={\"group_display_name\": \"primary_group_name\"})\n", "\n", "df_sheet_mapping1[\"relation\"] = (\n", "    df_sheet_mapping1[\"primary_group_name\"] + \"_\" + df_sheet_mapping1[\"collection_uuid\"]\n", ")\n", "df_sheet_mapping1[\"usecase\"] = \"qb_bestseller\""]}, {"cell_type": "code", "execution_count": null, "id": "cd1e2a30-3c73-416d-ab14-7f63919bbc2f", "metadata": {}, "outputs": [], "source": ["df_exploded = df_exploded.merge(\n", "    df_sheet_mapping1[[\"primary_group_name\", \"collection_uuid\"]],\n", "    left_on=[\"group_names\"],\n", "    right_on=[\"primary_group_name\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c86f9387-7687-4ed2-90f7-023ad2fff799", "metadata": {}, "outputs": [], "source": ["df_exploded = df_exploded.groupby([\"city_id\", \"city_name\", \"usecase\", \"collection_uuid\"]).head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "0fc13695-e200-40e6-9ec0-cf3650882bc0", "metadata": {}, "outputs": [], "source": ["final_db_table = (\n", "    df_exploded.sort_values(by=[\"rank\"])\n", "    .groupby([\"city_id\", \"city_name\", \"usecase\"])\n", "    .group_names.apply(list)\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e35374c9-8338-468d-8db7-01d0dc8d76c3", "metadata": {}, "outputs": [], "source": ["final_db_table.city_id.unique()"]}, {"cell_type": "code", "execution_count": null, "id": "a3455861-5bf4-4872-ace4-003b750b4ede", "metadata": {}, "outputs": [], "source": ["final_db_table.head()"]}, {"cell_type": "markdown", "id": "7c1563c6-366d-4f3b-b723-7ae1569135ef", "metadata": {"tags": []}, "source": ["## Pushing to table"]}, {"cell_type": "code", "execution_count": null, "id": "8f9496da-695d-4b65-b521-0b8d313a9f30", "metadata": {}, "outputs": [], "source": ["import asyncio\n", "import concurrent.futures\n", "import datetime as dt\n", "import os\n", "import threading\n", "import time\n", "from contextlib import contextmanager\n", "from datetime import datetime\n", "\n", "import boto3\n", "import pytz\n", "import sqlalchemy as sqla\n", "from sqlalchemy.dialects import postgresql\n", "from sqlalchemy.ext.automap import automap_base\n", "from sqlalchemy.orm import sessionmaker\n", "\n", "\n", "@contextmanager\n", "def session_scope(s):\n", "    \"\"\"Provide a transactional scope around a series of operations.\"\"\"\n", "    session = s()\n", "    try:\n", "        yield session\n", "        session.commit()\n", "    except Exception as e:\n", "        session.rollback()\n", "        raise e\n", "    finally:\n", "        session.close()\n", "\n", "\n", "class PostgresSQLSink:\n", "    def __init__(self, engine):\n", "        self.engine = engine\n", "        self.session = sessionmaker(\n", "            autocommit=False, autoflush=False, expire_on_commit=False, bind=engine\n", "        )\n", "\n", "    def bulk_execute(self, queries, workers=2):\n", "        responses = []\n", "        with concurrent.futures.ThreadPoolExecutor(max_workers=workers) as executor:\n", "            futures = []\n", "            for i, query in enumerate(queries):\n", "                task_id = f\"{i + 1}\"\n", "                futures.append(executor.submit(self.execute, query, task_id=task_id))\n", "\n", "            for future in concurrent.futures.as_completed(futures):\n", "                result = future.result()\n", "                responses.append(result)\n", "                print(f\"{result['task_id']} complete... [{result['time_taken']}]\")\n", "        return responses\n", "\n", "    def execute(self, query, task_id=None):\n", "        if task_id is None:\n", "            task_id = \"execute\"\n", "\n", "        with session_scope(self.session) as session:\n", "            error = None\n", "            start = time.time()\n", "            try:\n", "                res = session.execute(query)\n", "            except Exception as e:\n", "                error = e\n", "\n", "            try:\n", "                result = res.rowcount\n", "            except:\n", "                result = None\n", "\n", "            return {\n", "                \"task_id\": task_id,\n", "                \"time_taken\": time.time() - start,\n", "                \"error\": error,\n", "                \"result\": result,\n", "                \"query\": query,\n", "            }\n", "\n", "\n", "def get_espina_db_connection():\n", "    dse_db_conn_params = pb.get_secret(\"dse/postgres/dse_db/airflow_etl_user\")\n", "    engine_url = dse_db_conn_params[\"uri\"]\n", "    engine = sqla.create_engine(engine_url, pool_size=1, echo=False)\n", "    return engine\n", "\n", "\n", "espina_db_connection = get_espina_db_connection()"]}, {"cell_type": "markdown", "id": "de28e2cc-7146-441c-8018-3d4440d231e5", "metadata": {}, "source": ["## Qb Bestsellers Data Table - ESPINA"]}, {"cell_type": "code", "execution_count": null, "id": "4bbd6bdc-8735-47fd-9cfd-99c5859b55c1", "metadata": {}, "outputs": [], "source": ["table_name_edb = \"qb_tod_bestsellers\"\n", "staging_table_name_edb = \"qb_tod_bestsellers_staging\"\n", "TABLE_CREATION_QUERY = f\"\"\"\n", "DROP TABLE {staging_table_name_edb};\n", "\n", "CREATE TABLE IF NOT EXISTS {staging_table_name_edb} (\n", "    city_id int NOT NULL,\n", "    city_name varchar NOT NULL,\n", "    usecase varchar NULL,\n", "    group_names text [] NULL);\n", "\n", "DROP TABLE {table_name_edb};\n", "\n", "CREATE TABLE IF NOT EXISTS {table_name_edb} (\n", "    city_id int NOT NULL,\n", "    city_name varchar NOT NULL,\n", "    usecase varchar NULL,\n", "    group_names text [] NULL);\n", "\n", "TRUNCATE TABLE {staging_table_name_edb};\n", "\"\"\"\n", "print(TABLE_CREATION_QUERY)\n", "with espina_db_connection.begin() as connection:\n", "    connection.execute(TABLE_CREATION_QUERY)"]}, {"cell_type": "code", "execution_count": null, "id": "a2ec0d53-94f7-48ef-abc2-39d0886b6f33", "metadata": {}, "outputs": [], "source": ["final_db_table.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "e4e623c3-5fdd-44ad-add3-8d43ec9f91d8", "metadata": {}, "outputs": [], "source": ["queries = []\n", "query = f\"INSERT INTO {staging_table_name_edb} (city_id, city_name, usecase, group_names) VALUES\\n\"\n", "chunksize = 1\n", "chunks = [final_db_table[i : i + chunksize] for i in range(0, final_db_table.shape[0], chunksize)]\n", "for chunk in chunks:\n", "    records = chunk.to_dict(\"records\")\n", "    values = \",\\n\".join(\n", "        [\n", "            f\"\"\"({record[\"city_id\"]}, e'{record[\"city_name\"]}', e'{record[\"usecase\"]}', ARRAY{list(record[\"group_names\"])})\"\"\"\n", "            for record in records\n", "        ]\n", "    )\n", "    queries.append(query + values + \" ON CONFLICT DO NOTHING;\")\n", "sink = PostgresSQLSink(espina_db_connection)\n", "start = time.time()\n", "results = sink.bulk_execute(queries, workers=2)\n", "print(f\"took .... {time.time() - start}\")"]}, {"cell_type": "code", "execution_count": null, "id": "7010d35d-1e1d-4b44-8b19-584595790c1d", "metadata": {}, "outputs": [], "source": ["queries[0]"]}, {"cell_type": "code", "execution_count": null, "id": "b7c36d24-de1b-427d-8dd3-e4e4bb8ae854", "metadata": {}, "outputs": [], "source": ["count_query = f\"\"\"select count(*) as count from {staging_table_name_edb}\"\"\"\n", "count = pd.read_sql_query(count_query, con=espina_db_connection)[\"count\"][0]\n", "print(count, len(final_db_table))\n", "if len(final_db_table) == count:\n", "    RENAME_SWAP_QUERY = f\"\"\"\n", "    ALTER TABLE {table_name_edb} RENAME TO _tmp_{table_name_edb};\n", "    ALTER TABLE {staging_table_name_edb} RENAME TO {table_name_edb};\n", "    ALTER TABLE _tmp_{table_name_edb} RENAME TO {staging_table_name_edb};\n", "    \"\"\"\n", "    with espina_db_connection.begin() as connection:\n", "        connection.execute(RENAME_SWAP_QUERY)\n", "else:\n", "    print(\"Drop!\")"]}, {"cell_type": "markdown", "id": "6452fd99-bf45-44bb-96c1-27d90d1666d0", "metadata": {}, "source": ["## updating collections"]}, {"cell_type": "code", "execution_count": null, "id": "3f89ea22-8470-49e1-9aa3-b38452592f43", "metadata": {}, "outputs": [], "source": ["import sqlalchemy as sqla\n", "\n", "\n", "def write_table_to_espina_db(table_name_edb, staging_table_name_edb, data_to_push):\n", "\n", "    espina_db_connection = get_espina_db_connection()\n", "\n", "    TABLE_CREATION_QUERY = f\"\"\"\n", "    CREATE TABLE IF NOT EXISTS {staging_table_name_edb}\n", "    (\n", "        relation varchar,\n", "        primary_group_name varchar,\n", "        usecase varchar,\n", "        is_enabled int,              \n", "        collection_uuid varchar,\n", "        CONSTRAINT {staging_table_name_edb}_group PRIMARY KEY (usecase, primary_group_name, is_enabled)\n", "    );\n", "\n", "\n", "    CREATE TABLE IF NOT EXISTS {table_name_edb}\n", "    (\n", "        relation varchar,\n", "        primary_group_name varchar,\n", "        usecase varchar,\n", "        is_enabled int,              \n", "        collection_uuid varchar,\n", "        CONSTRAINT {table_name_edb}_group PRIMARY KEY (usecase, primary_group_name, is_enabled)\n", "\n", "    );\n", "\n", "    TRUNCATE TABLE {staging_table_name_edb};\n", "    \"\"\"\n", "\n", "    with espina_db_connection.begin() as connection:\n", "        connection.execute(TABLE_CREATION_QUERY)\n", "\n", "    sink = PostgresSQLSink(espina_db_connection)\n", "\n", "    queries = []\n", "    query = f\"INSERT INTO {staging_table_name_edb} (relation, primary_group_name, usecase, is_enabled, collection_uuid) VALUES\\n\"\n", "    chunksize = 100\n", "    chunks = [data_to_push[i : i + chunksize] for i in range(0, data_to_push.shape[0], chunksize)]\n", "    for chunk in chunks:\n", "        records = chunk.to_dict(\"records\")\n", "        values = \",\\n\".join(\n", "            [\n", "                f\"\"\"(e'{record[\"relation\"]}', e'{record[\"primary_group_name\"]}', e'{record[\"usecase\"]}', {record[\"is_enabled\"]}, e'{record[\"collection_uuid\"]}')\"\"\"\n", "                for record in records\n", "            ]\n", "        )\n", "        queries.append(query + values + \" ON CONFLICT DO NOTHING;\")\n", "\n", "    start = time.time()\n", "    results = sink.bulk_execute(queries, workers=2)\n", "\n", "    count_query = f\"\"\"select count(*) as count from {staging_table_name_edb}\"\"\"\n", "    count = pd.read_sql_query(count_query, con=espina_db_connection)[\"count\"][0]\n", "\n", "    if len(data_to_push) == count:\n", "        RENAME_SWAP_QUERY = f\"\"\"\n", "        ALTER TABLE {table_name_edb} RENAME TO _tmp_{table_name_edb};\n", "        ALTER TABLE {staging_table_name_edb} RENAME TO {table_name_edb};\n", "        ALTER TABLE _tmp_{table_name_edb} RENAME TO {staging_table_name_edb};\n", "        \"\"\"\n", "        with espina_db_connection.begin() as connection:\n", "            connection.execute(RENAME_SWAP_QUERY)\n", "    else:\n", "        raise Exception(\"Write failed. Not replacing table\")"]}, {"cell_type": "code", "execution_count": null, "id": "f967d6ba-7841-42f4-9978-f6f135da579d", "metadata": {}, "outputs": [], "source": ["staging_table_name_edb = \"quick_buys_staging\"\n", "table_name_edb = \"quick_buys\""]}, {"cell_type": "code", "execution_count": null, "id": "bbcf108f-2b45-4f1a-ba3d-a9b5480393bf", "metadata": {}, "outputs": [], "source": ["write_table_to_espina_db(table_name_edb, staging_table_name_edb, df_sheet_mapping1)"]}, {"cell_type": "code", "execution_count": null, "id": "6614e8fa-2a82-47db-b88e-87dfdf6da2ef", "metadata": {}, "outputs": [], "source": ["notif_string5 = f\"\"\"\n", "🟢 DagRun Success.\n", "   Dag: data_science/new_user_bestsellers_v2/etl/new_user_bestsellers_v2\n", "   Owner: <PERSON><PERSON><PERSON>\n", "   Metadata: New users bestsellers DAG run completed successfully!\n", "\"\"\"\n", "pb.send_slack_message(\n", "    channel=\"bl-personalization-notifications\",\n", "    text=notif_string5,\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}