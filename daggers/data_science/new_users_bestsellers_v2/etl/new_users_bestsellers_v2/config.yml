alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
  - channel: bl-personalization-notifications
dag_name: new_users_bestsellers_v2
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: high-mem
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
  retries: 2
  retry_delay_in_seconds: 15
owner:
  email: <EMAIL>
  slack_id: U03RQJY8HAT
path: data_science/new_users_bestsellers_v2/etl/new_users_bestsellers_v2
paused: false
pool: data_science_pool
project_name: new_users_bestsellers_v2
schedule:
  end_date: '2025-09-07T00:00:00'
  interval: 0 23 * * *
  start_date: '2024-12-18T09:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- kafka_utils.py
tags: []
template_name: notebook
version: 15
