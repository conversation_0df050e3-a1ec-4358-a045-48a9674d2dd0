alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: optimization_weekly
dag_type: etl
escalation_priority: low
execution_timeout: 1680
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: very-high-cpu
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
  retries: 3
owner:
  email: <EMAIL>
  slack_id: U05K16ULBPH
path: data_science/lm_planning/etl/optimization_weekly
paused: false
pool: data_science_pool
project_name: lm_planning
schedule:
  end_date: '2025-09-08T00:00:00'
  interval: 0 7 * * 2
  start_date: '2025-06-10T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
