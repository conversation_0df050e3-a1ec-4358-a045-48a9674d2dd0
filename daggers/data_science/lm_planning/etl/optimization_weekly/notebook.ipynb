{"cells": [{"cell_type": "code", "execution_count": null, "id": "a273926f-7391-4f84-a144-13dcfe4258d2", "metadata": {}, "outputs": [], "source": ["!pip install papermill"]}, {"cell_type": "code", "execution_count": null, "id": "ef02feb7-d36c-40e8-a289-4f705d91ec64", "metadata": {}, "outputs": [], "source": ["import os\n", "import pencilbox as pb\n", "import papermill as pm\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "62ad46f3-ec75-466c-a099-f8ff65ebddf3", "metadata": {}, "outputs": [], "source": ["pb.clone_repo(\"data-science-models\", \"/tmp/optimization_weeklevel/\", branch_to_fetch=\"master\")"]}, {"cell_type": "code", "execution_count": null, "id": "341fe672-9191-40e3-9b02-297c7f00acbc", "metadata": {}, "outputs": [], "source": ["os.chdir(\"/tmp/optimization_weeklevel/data-science-models\")"]}, {"cell_type": "code", "execution_count": null, "id": "9f921f10-8e0b-4c0c-bbc1-0ca4c0708c28", "metadata": {}, "outputs": [], "source": ["!pip install -r projects/lm_planning/requirements.txt"]}, {"cell_type": "code", "execution_count": null, "id": "1b509544-38a2-4a08-b61d-1685b7d34a3f", "metadata": {}, "outputs": [], "source": ["pm.execute_notebook(\n", "    input_path=\"projects/lm_planning/clustering_dags/optimization_weeklevel.ipynb\",\n", "    output_path=\"./output_optimization_weeklevel.ipynb\",\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}