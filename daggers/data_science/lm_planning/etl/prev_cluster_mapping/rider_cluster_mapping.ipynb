{"cells": [{"cell_type": "code", "execution_count": null, "id": "09147173-d2d6-47d3-8799-bd745e5f7c3d", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["!pip install papermill"]}, {"cell_type": "code", "execution_count": null, "id": "456f8f28-28a2-4666-b5b6-9437f7f5cf0e", "metadata": {}, "outputs": [], "source": ["import os\n", "import pencilbox as pb\n", "import papermill as pm\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "7027874e-f0b7-4e07-9a28-02285f18d32a", "metadata": {}, "outputs": [], "source": ["pb.clone_repo(\"data-science-models\", \"/tmp/rider_cluster_mapping/\", branch_to_fetch=\"master\")"]}, {"cell_type": "code", "execution_count": null, "id": "e997bc0c-9766-42e4-9de3-48fd56e0ed31", "metadata": {}, "outputs": [], "source": ["os.chdir(\"/tmp/rider_cluster_mapping/data-science-models\")"]}, {"cell_type": "code", "execution_count": null, "id": "91c4af3a-98ee-4370-91fa-d90a9cac79ee", "metadata": {}, "outputs": [], "source": ["!pip install -r projects/lm_planning/requirements.txt"]}, {"cell_type": "code", "execution_count": null, "id": "f872ec64-1ef5-4d77-9903-f6e045cbdf98", "metadata": {}, "outputs": [], "source": ["pm.execute_notebook(\n", "    input_path=\"projects/lm_planning/dags/clustering_prev/identifying_rider_clusters.ipynb\",\n", "    output_path=\"./output_identifying_rider_clusters.ipynb\",\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}