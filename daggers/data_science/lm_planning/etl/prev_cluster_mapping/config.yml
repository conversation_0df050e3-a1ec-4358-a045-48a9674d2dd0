alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 3
dag_name: prev_cluster_mapping
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebooks:
- alias: daily_slots_combined
  executor_config:
    load_type: medium
    node_type: spot
  name: daily_slots_combined
  parameters: null
  retries: 3
  tag: group_1
- alias: rider_cluster_mapping
  executor_config:
    load_type: medium
    node_type: spot
  name: rider_cluster_mapping
  parameters: null
  retries: 3
  tag: group_2
owner:
  email: <EMAIL>
  slack_id: U05K16ULBPH
path: data_science/lm_planning/etl/prev_cluster_mapping
paused: false
pool: data_science_pool
project_name: lm_planning
schedule:
  end_date: '2025-08-30T00:00:00'
  interval: 0 8 * * *
  start_date: '2025-06-01T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 1
