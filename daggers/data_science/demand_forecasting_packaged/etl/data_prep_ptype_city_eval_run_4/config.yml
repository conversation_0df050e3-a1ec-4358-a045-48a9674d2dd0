alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 10
dag_name: data_prep_ptype_city_eval_run_4
dag_type: etl
escalation_priority: low
execution_timeout: 4320
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebooks:
- alias: notebook_0
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_0
  parameters:
    dag_name: data_prep_ptype_city_eval_run_4
    dagger_id: 4
    folder_name: 2024_12_26_festive_ptypes_eval_run
    notebook_id: 0
  retries: 3
  tag: group_1
- alias: notebook_1
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_0
  parameters:
    dag_name: data_prep_ptype_city_eval_run_4
    dagger_id: 4
    folder_name: 2024_12_26_festive_ptypes_eval_run
    notebook_id: 1
  retries: 3
  tag: group_1
- alias: notebook_2
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_0
  parameters:
    dag_name: data_prep_ptype_city_eval_run_4
    dagger_id: 4
    folder_name: 2024_12_26_festive_ptypes_eval_run
    notebook_id: 2
  retries: 3
  tag: group_1
owner:
  email: <EMAIL>
  slack_id: U07CHK59SPN
path: data_science/demand_forecasting_packaged/etl/data_prep_ptype_city_eval_run_4
paused: false
pool: data_science_pool
project_name: demand_forecasting_packaged
schedule:
  end_date: '2025-09-07T00:00:00'
  interval: null
  start_date: '2024-12-27T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- config.py
tags: []
template_name: multi_notebook
version: 2
