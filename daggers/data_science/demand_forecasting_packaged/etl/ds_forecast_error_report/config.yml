alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 1
dag_name: ds_forecast_error_report
dag_type: etl
escalation_priority: low
execution_timeout: 1000
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebooks:
- alias: notebook_achievement
  executor_config:
    load_type: medium
    node_type: spot
  name: notebook_achievement
  parameters:
    params: '{{ params }}'
  tag: group_1
- alias: notebook_error_report
  executor_config:
    load_type: medium
    node_type: spot
  name: notebook_error_report
  parameters:
    params: '{{ params }}'
  tag: group_2
owner:
  email: <EMAIL>
  slack_id: U083VSKK7KJ
path: data_science/demand_forecasting_packaged/etl/ds_forecast_error_report
paused: false
pool: data_science_pool
project_name: demand_forecasting_packaged
schedule:
  end_date: '2025-08-11T00:00:00'
  interval: null
  start_date: '2025-06-06T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 5
