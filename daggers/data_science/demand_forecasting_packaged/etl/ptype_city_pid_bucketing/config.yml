alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: ptype_city_pid_bucketing
dag_type: etl
escalation_priority: low
execution_timeout: 4320
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: low
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U07CHK59SPN
path: data_science/demand_forecasting_packaged/etl/ptype_city_pid_bucketing
paused: false
pool: data_science_pool
project_name: demand_forecasting_packaged
schedule:
  end_date: '2025-09-15T00:00:00'
  interval: 59 23 * * *
  start_date: '2025-03-28T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- support_functions.py
- queries.py
- ptype_city_pid_bucketing_schema.py
tags: []
template_name: notebook
version: 4
