alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 10
dag_name: city_ptype_training
dag_type: etl
escalation_priority: high
execution_timeout: 4320
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: ultra-high-cpu
    node_type: od
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebooks:
- alias: notebook_0
  executor_config:
    load_type: ultra-high-cpu
    node_type: od
  name: notebook
  parameters:
    notebook_id: 0
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_1
- alias: notebook_1
  executor_config:
    load_type: ultra-high-cpu
    node_type: od
  name: notebook
  parameters:
    notebook_id: 1
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_1
- alias: notebook_2
  executor_config:
    load_type: ultra-high-cpu
    node_type: od
  name: notebook
  parameters:
    notebook_id: 2
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_1
- alias: notebook_3
  executor_config:
    load_type: ultra-high-cpu
    node_type: od
  name: notebook
  parameters:
    notebook_id: 3
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_1
- alias: notebook_4
  executor_config:
    load_type: ultra-high-cpu
    node_type: od
  name: notebook
  parameters:
    notebook_id: 4
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_1
- alias: notebook_5
  executor_config:
    load_type: ultra-high-cpu
    node_type: od
  name: notebook
  parameters:
    notebook_id: 5
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_1
- alias: notebook_6
  executor_config:
    load_type: ultra-high-cpu
    node_type: od
  name: notebook
  parameters:
    notebook_id: 6
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_1
- alias: notebook_7
  executor_config:
    load_type: ultra-high-cpu
    node_type: od
  name: notebook
  parameters:
    notebook_id: 7
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_1
- alias: notebook_8
  executor_config:
    load_type: ultra-high-cpu
    node_type: od
  name: notebook
  parameters:
    notebook_id: 8
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_1
- alias: notebook_9
  executor_config:
    load_type: ultra-high-cpu
    node_type: od
  name: notebook
  parameters:
    notebook_id: 9
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_1
owner:
  email: <EMAIL>
  slack_id: U07CHK59SPN
path: data_science/demand_forecasting_packaged/etl/city_ptype_training
paused: false
pool: data_science_pool
project_name: demand_forecasting_packaged
schedule:
  end_date: '2025-09-03T00:00:00'
  interval: null
  start_date: '2025-02-12T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- config.py
tags: []
template_name: multi_notebook
version: 13
