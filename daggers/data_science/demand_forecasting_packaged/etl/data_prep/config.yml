alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 5
dag_name: data_prep
dag_type: etl
escalation_priority: low
execution_timeout: 4320
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_science
notebooks:
- alias: weather_features_creation
  executor_config:
    load_type: tiny
    node_type: od
  name: Base_Feature_Creation_Weather
  parameters: {}
  tag: group_1
- alias: Daily_Device_Activity_Raw
  executor_config:
    load_type: ultra-high-mem
    node_type: spot
  name: Daily_Device_Activity_Raw
  parameters: {}
  retries: 2
  tag: group_1
- alias: User_Cohort_Creation
  executor_config:
    load_type: ultra-high-mem
    node_type: spot
  name: User_Cohort_Creation
  parameters: {}
  retries: 3
  tag: group_2
- alias: ptype_sales
  executor_config:
    load_type: tiny
    node_type: spot
  name: Base_Feature_Creation_Ptype_Sales
  parameters: {}
  retries: 3
  tag: group_2
- alias: ptype_availability
  executor_config:
    load_type: tiny
    node_type: spot
  name: Base_Feature_Creation_Ptype_Availability
  parameters: {}
  retries: 3
  tag: group_2
- alias: ptype_funnel
  executor_config:
    load_type: tiny
    node_type: spot
  name: Base_Feature_Creation_Ptype_Funnel
  parameters: {}
  retries: 3
  tag: group_2
- alias: ptype_searchers
  executor_config:
    load_type: tiny
    node_type: spot
  name: Base_Feature_Creation_Ptype_Searchers
  parameters: {}
  retries: 3
  tag: group_2
- alias: global_feature_creation
  executor_config:
    load_type: low
    node_type: spot
  name: Base_Feature_Creation_Global
  parameters: {}
  retries: 3
  tag: group_3
- alias: table_repartitioning
  executor_config:
    load_type: tiny
    node_type: spot
  name: table_repartitioning
  parameters: {}
  retries: 3
  tag: group_4
owner:
  email: <EMAIL>
  slack_id: U066FKJM8KC
path: data_science/demand_forecasting_packaged/etl/data_prep
paused: false
pool: data_science_pool
project_name: demand_forecasting_packaged
schedule:
  end_date: '2025-08-09T00:00:00'
  interval: 55 00 * * *
  start_date: '2025-06-25T00:00:00'
schedule_type: fixed
sla: 4320 minutes
support_files:
- ptype_feature_queries.py
- global_feature_queries.py
- weather_cities.py
- table_schemas.py
- support_functions.py
- daily_activity_query.py
- ptype_features_master_query.py
tags: []
template_name: multi_notebook
version: 18