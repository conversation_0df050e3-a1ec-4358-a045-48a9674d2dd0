device_daily_activity_log_query = str(
    """
WITH

mobile_events_base AS (
    SELECT
        CAST(sd.at_date_ist AS DATE) AS at_date_ist,
        LOWER(coalesce(coalesce(device_uuid, advertising_id), cast(traits__user_id as varchar))) AS device_uuid,
        case
            when traits__city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
            when traits__city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa') then 'Goa'
            when traits__city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
            when traits__city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
            when traits__city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
            when traits__city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
            when traits__city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
            when traits__city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
            when traits__city_name in ('SOLAN', 'Solan') then 'Solan'
            when traits__city_name in ('Sundar_Nagar' ,'Sundar Nagar') then 'Sundar Nagar'
            when traits__city_name in ('bikaner', 'Bikaner') then 'Bikaner'
            when traits__city_name in ('Purnia', 'purnia') then 'Purnia'
            else traits__city_name
        end as city,
        sd.name

    FROM  
        lake_events.mobile_event_data AS sd

    WHERE 
        1=1
        AND at_date_ist >= DATE('{start_date}')
        AND at_date_ist <= DATE('{end_date}')
        AND sd.name IN (
            'Search Results Viewed', 'Product Added', 'Cart Viewed',
            'Checkout Step Viewed', 'App Launch', 'App Launch Traits', 'Homepage Visit', 'Phone Number Entered', 'Address Added',
            'Product Searched', 'Deeplink Fired', 'Location Permission Visit', 'Cart Footer Strip Clicked'
        )
        AND sd.device_uuid IS NOT NULL
        AND sd.device_uuid <> ''
        AND sd.platform IN ('android', 'ios')
        AND sd.traits__merchant_id is not null
        AND sd.traits__city_name not in ('Not in service area')
--            AND dm.city_name = 'HR-NCR'
),

sales_base AS (
    SELECT 
        order_create_dt_ist,
        lower(coalesce(device_id, cast(dim_customer_key as varchar))) as device_id,
        case
            when city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
            when city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa') then 'Goa'
            when city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
            else city_name
        end as city,
        COUNT(DISTINCT order_id) AS orders

    FROM
        dwh.fact_sales_order_details AS fso

    WHERE
        order_create_dt_ist >= DATE('{start_date}')
        AND order_create_dt_ist <= DATE('{end_date}')
        AND is_internal_order = FALSE
        AND order_current_status = 'DELIVERED'
        AND coalesce(device_id, cast(dim_customer_key as varchar)) is not null
        AND device_id <> ''

    GROUP BY 
        1, 2, 3
),

mobile_base AS (
    SELECT 
        at_date_ist,
        city,
        device_uuid as device_id,

        1 AS active,

        COUNT(
                CASE WHEN name in ('App Launch', 'App Launch Traits') THEN device_uuid END
        ) AS app_launch,

        COUNT(
                CASE WHEN name = 'Product Added' THEN device_uuid END
        ) AS add_to_cart,

        COUNT(
                CASE WHEN name = 'Cart Viewed' THEN device_uuid END
        ) AS cart_visit,

        COUNT(
                CASE WHEN name = 'Search Results Viewed' THEN device_uuid END
        ) AS searches,

        COUNT(
                CASE WHEN name = 'Address Added' THEN device_uuid END
        ) AS address_added

    FROM
        mobile_events_base
    GROUP BY 
        1, 2, 3
)

SELECT 
    cast(mb.at_date_ist as varchar) as at_date_ist,
    mb.city,
    mb.device_id, 
    cast(SUM(active) as bigint) AS activity,
    cast(SUM(app_launch) as bigint) AS app_lanched,
    cast(SUM(add_to_cart) as bigint) AS atc,
    cast(SUM(cart_visit) as bigint) AS cart_visit,
    cast(SUM(orders) as bigint) AS num_orders,
    cast(SUM(searches) as bigint) AS search_result_viewed,
    cast(SUM(address_added) as bigint) as address_added,
    CAST(max(current_timestamp) AS timestamp(6)) as snapshot_dt_ist

FROM
    mobile_base AS mb
LEFT JOIN
    sales_base AS sb
ON 
    sb.order_create_dt_ist = mb.at_date_ist
    AND 
    sb.city = mb.city
    and
    sb.device_id = mb.device_id

GROUP BY 
    1, 2, 3

"""
)