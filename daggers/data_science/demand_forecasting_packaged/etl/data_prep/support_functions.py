import os
import subprocess

try:
    import awswrangler as wr
except ImportError:
    subprocess.check_call([os.sys.executable, '-m', 'pip', 'install', 'awswrangler'])

import os
import pytz
import numpy as np
import pandas as pd
import pencilbox as pb
import awswrangler as wr
from datetime import datetime
from tqdm.notebook import tqdm
import botocore
from dateutil.relativedelta import relativedelta
import boto3
import sys

wr.config.botocore_config = botocore.config.Config(
    retries={"max_attempts": 10}, connect_timeout=20, max_pool_connections=100
)

run_id = datetime.now(pytz.timezone("Asia/Kolkata")).strftime("%Y%m%d-%H%M%S]<>")

import boto3
from botocore.exceptions import NoCredentialsError, PartialCredentialsError, ClientError

s3_client = boto3.client("s3")
def send_log(*args, linspace=1, module = None):
    return (
        f"[{module}]<>"
        + "[run_ts:"
        + run_id
        + datetime.now(pytz.timezone("Asia/Kolkata")).strftime("[ts: %Y-%m-%d %H:%M:%S] --> ")
        + " ".join(map(str, args))
    )

def print_log(*args, linspace=1, module = None):
    print(
        f"[{module}]<>"
        + "[run_ts::"
        + run_id
        + datetime.now(pytz.timezone("Asia/Kolkata")).strftime("[ts]:[%Y-%m-%d %H:%M:%S] --> ")
        + " ".join(map(str, args))
    )

def to_date(col):
    return pd.to_datetime(col).dt.date

def to_int(col):
    return col.astype(int)

def send_to_slack(msg):
    try:
        pb.send_slack_message(
            channel=alerts_channel,
            text=msg,
        )
    except Exception as e:
        send_log(f"Unable to push message {msg} to slack, got error = {e}")

def upload_parquet_to_s3(df: pd.DataFrame, path: str, **kwargs) -> None:
    wr.s3.to_parquet(df, path, **kwargs)


def read_parquet_from_s3(s3_path: str) -> pd.DataFrame:
    return wr.s3.read_parquet(s3_path)

def fetch_data(raw_query, **kwargs):
    _trino_conn = pb.get_connection("[Warehouse] Trino")
    df = pd.read_sql(raw_query.format(**kwargs), _trino_conn)
    del _trino_conn
    return df

def check_s3_file_exists(bucket_name, file_key):
    try:
        s3_client.head_object(Bucket=bucket_name, Key=file_key)
        return True
    except ClientError as e:
        error_code = int(e.response["Error"]["Code"])
        if error_code == 404:
            return False
        else:
            print_log(f"An error occurred: {e}")
            return False
    except NoCredentialsError:
        print_log("Credentials not available.")
        return False
    except PartialCredentialsError:
        print_log("Incomplete credentials provided.")
        return False
    
api_mb = "887862244de732279cef110f321ed755"
api_sg = "7dade6a71fed80e6a7774ff5fcf63052"
api_rk = "b43cf4fac08e460e60b29ca2fa6ab8be"
api_ps = "720eaabc9c92bee78cc1c651ab858f6c"
api_aa = "9512099aba3ad80a7386166d1e7fe100"
# api_yd = "6b4066f826e7e4b8a78d5ea5a625cd83"
api_ap = "7b8927b7be64343f3cc928311b42f566"
api_nv = "0b29d36a07ffef45283791c41d778021"
api_ds = "035f1e8a1718d3da8a22cd9ca16c0405"
api_ps2 = "49199b308a5da216b28764ba8d5ef2fa"
api_ul = "a31a125a937d54766d751304aa6d97ff"
api_as = "a85da41aa69453993ce4a5acba9b109b"

api_dict = {"ps": api_ps, 
            "mb": api_mb, 
            "sg": api_sg, 
            "rk": api_rk, 
            "aa": api_aa, 
            # "yd": api_yd,
            "ap": api_ap,
            "nv": api_nv,
            "ds": api_ds,
            "ps2": api_ps2, 
            "ul": api_ul,
            "as": api_as}
