{"cells": [{"cell_type": "markdown", "id": "53be0c02-788b-4998-8a6f-8ffa046272d7", "metadata": {}, "source": ["# Existing Tables Re-partitioning"]}, {"cell_type": "code", "execution_count": null, "id": "d6e1de27-7adc-4a28-9b75-15642df0b750", "metadata": {}, "outputs": [], "source": ["module = \"Table Re-partitioning\""]}, {"cell_type": "code", "execution_count": null, "id": "d9c6b985-128d-479b-8644-5f3bc5a833f5", "metadata": {}, "outputs": [], "source": ["import os\n", "import pytz\n", "import pandas as pd\n", "import pencilbox as pb\n", "from pytz import timezone\n", "from functools import reduce\n", "from datetime import datetime\n", "from dateutil.relativedelta import relativedelta\n", "import itertools, sys\n", "import numpy as np\n", "import gc"]}, {"cell_type": "code", "execution_count": null, "id": "afd18a00-f8ed-4d36-995a-cc610cd694ea", "metadata": {}, "outputs": [], "source": ["cwd = \"/usr/local/airflow/dags/repo/dags/data_science/demand_forecasting_packaged/etl/data_prep\"\n", "sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "5a329cc4-d0a8-4e19-8c7d-a786f7aeda6a", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["at_date_ist = datetime.now().date()"]}, {"cell_type": "code", "execution_count": null, "id": "d9309b54-018e-41ac-908c-51bd850427d0", "metadata": {}, "outputs": [], "source": ["run_id = datetime.now(pytz.timezone(\"Asia/Kolkata\"))"]}, {"cell_type": "code", "execution_count": null, "id": "bb49a775-d16f-456c-bb3b-190f987290a6", "metadata": {}, "outputs": [], "source": ["alerts_channel = \"bl_forecasting_global_data_prep_alerts\""]}, {"cell_type": "code", "execution_count": null, "id": "3e120a1f-4790-4ec8-9a56-5664f59f4529", "metadata": {}, "outputs": [], "source": ["def send_log(*args, linspace=1):\n", "    return (\n", "        f\"[{module}]<>\"\n", "        + \"[run_ts=\"\n", "        + run_id.strftime(\"%Y%m%d-%H%M%S]<>\")\n", "        + datetime.now(pytz.timezone(\"Asia/Kolkata\")).strftime(\"[ts=%Y%m%d %H%M%S] --> \")\n", "        + \" \".join(map(str, args))\n", "    )\n", "\n", "\n", "def print_log(*args, linspace=1, task=None, Stage=None, Outcome=None):\n", "    print(\n", "        f\"[{module}]<>\"\n", "        + \"[run_ts=\"\n", "        + run_id.strftime(\"%Y%m%d-%H%M%S]<>\")\n", "        + datetime.now(pytz.timezone(\"Asia/Kolkata\")).strftime(\"[ts=%Y%m%d %H%M%S] --> \")\n", "        + \" \".join(map(str, args))\n", "    )\n", "\n", "\n", "def send_to_slack(msg):\n", "    try:\n", "        # 1==1\n", "        pb.send_slack_message(\n", "            channel=alerts_channel,\n", "            text=msg,\n", "        )\n", "    except Exception as e:\n", "        send_log(f\"Unable to push message {msg} to slack, got error = {e}\")"]}, {"cell_type": "code", "execution_count": null, "id": "b093b079-18ee-46e8-80e1-1eac6313c3be", "metadata": {}, "outputs": [], "source": ["import table_schemas as schema"]}, {"cell_type": "code", "execution_count": null, "id": "795a5f89-ae5f-4a7f-a06a-eb310b91eb58", "metadata": {}, "outputs": [], "source": ["_trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "c082d632-9115-4fd8-a923-214320786c52", "metadata": {}, "outputs": [], "source": ["all_schemas = sorted([m for m in list(schema.__dict__.keys()) if \"__\" not in m])\n", "all_schemas"]}, {"cell_type": "code", "execution_count": null, "id": "104b97dd-7cdd-42ed-83dc-c51152439cc5", "metadata": {"tags": []}, "outputs": [], "source": ["if len(all_schemas) > 0:\n", "    for x in all_schemas:\n", "\n", "        original_schema = getattr(schema, x)\n", "        original_schema_name = original_schema[\"schema_name\"]\n", "        original_table_name = original_schema[\"table_name\"]\n", "        original_load_type = original_schema[\"load_type\"]\n", "        partition_key = original_schema[\"partition_key\"]\n", "\n", "        print_log(\n", "            f\"\\n\\n{original_schema_name=},\\n{original_table_name=},\\n{original_load_type=},\\n{partition_key=}\\n\"\n", "        )\n", "\n", "        if datetime.now().date().weekday() % 3 == 0 and original_table_name in [\n", "            # \"dim_city_date_device_activity_log\",\n", "            # \"dim_device_history\",\n", "            \"demand_forecasting_global_ft_base\",\n", "            \"demand_forecasting_ptype_city_daily_sales\",\n", "            \"demand_forecasting_ptype_city_daily_availability\",\n", "            \"demand_forecasting_ptype_city_daily_funnel\",\n", "            \"demand_forecasting_ptype_city_daily_searchers\",\n", "            # \"demand_forecasting_sku_ft_base\",\n", "            \" \",\n", "        ]:\n", "\n", "            interim_schema_name = \"interim\"\n", "            msg = f\"Starting the re-partitioning of {original_table_name}\"\n", "            print_log(msg)\n", "            send_to_slack(send_log(msg))\n", "\n", "            interim_schema = original_schema.copy()\n", "            interim_schema[\"schema_name\"] = \"interim\"\n", "            interim_schema[\"load_type\"] = \"truncate\"\n", "            interim_schema[\"partition_key\"] = []\n", "\n", "            if len(partition_key) > 1:\n", "                move_to_interim_query = f\"select * from {original_schema_name}.{original_table_name} where {partition_key[0]} is not null and {partition_key[1]} is not null\"\n", "            else:\n", "                move_to_interim_query = f\"select * from {original_schema_name}.{original_table_name} where {partition_key[0]} is not null\"\n", "\n", "            # For restoring back-up, skip the movement of current table to interim\n", "            if datetime.now().date().weekday() % 3 == 0 and original_table_name not in [\n", "                # \"demand_forecasting_ptype_city_daily_sales\",\n", "                # \"demand_forecasting_ptype_city_daily_availability\",\n", "                # \"demand_forecasting_ptype_city_daily_funnel\",\n", "                # \"demand_forecasting_ptype_city_daily_searchers\",\n", "                # \"demand_forecasting_global_ft_base\",\n", "                \" \",\n", "            ]:\n", "\n", "                print_log(\"Query to move data from original table : \", move_to_interim_query)\n", "\n", "                msg = f\"Copying all records from original table to interim table for {x} in {interim_schema['load_type']} load_type mode\"\n", "                print_log(msg)\n", "                send_to_slack(send_log(msg))\n", "\n", "                pb.to_trino(\n", "                    move_to_interim_query,\n", "                    **interim_schema,\n", "                )\n", "\n", "                msg = \"All records from original table moved to interim table\"\n", "                print_log(msg)\n", "                send_to_slack(send_log(msg))\n", "\n", "            msg = \"Moving the full data from interim table back into the original table\"\n", "            print_log(msg)\n", "            send_to_slack(send_log(msg))\n", "\n", "            if original_table_name in [\"demand_forecasting_global_ft_base\"]:\n", "\n", "                full_move_query = f\"select date, case when city in ('Gurgaon','Gurugram') then 'HR-NCR' when city in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa', 'Panaji') then 'Goa' when city in ('Noida', 'Ghaziabad') then 'UP-NCR' when city in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar' when city in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli' when city in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh' when city in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur' when city in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar' when city in ('SOLAN', 'Solan') then 'Solan' when city in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar' when city in ('bikaner', 'Bikaner') then 'Bikaner' when city in ('Purnia', 'purnia') then 'Purnia' else city end as city, feature_name, feature_value, snapshot_dt_ist from {interim_schema['schema_name']}.{interim_schema['table_name']} where city is not null and feature_name is not null and date is not null\"\n", "\n", "            elif original_table_name == \"demand_forecasting_ptype_city_daily_sales\":\n", "                full_move_query = f\"with cte as (select date, case when city in ('Gurgaon','Gurugram') then 'HR-NCR' when city in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa', 'Panaji') then 'Goa' when city in ('Noida', 'Ghaziabad') then 'UP-NCR' when city in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar' when city in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli' when city in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh' when city in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur' when city in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar' when city in ('SOLAN', 'Solan') then 'Solan' when city in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar' when city in ('bikaner', 'Bikaner') then 'Bikaner' when city in ('Purnia', 'purnia') then 'Purnia' else city end as city, product_type, quantity_sold, total_mrp, sales_value, ptype_carts, ptype_customers, retail_discount_per_unit, retail_price_per_unit, snapshot_dt_ist from {interim_schema['schema_name']}.{interim_schema['table_name']} union all select date, city, case when product_type = 'Bhaiya Bhabhi' then 'Bhaiya Bhabhi Rakhi' when product_type = 'Lumba' then 'Lumba Rakhi' else product_type end as product_type, quantity_sold, total_mrp, sales_value, ptype_carts, ptype_customers, retail_discount_per_unit, retail_price_per_unit, snapshot_dt_ist from {original_schema['schema_name']}.{original_schema['table_name']} where {partition_key[0]} is not null) select date, city, product_type, avg(quantity_sold) quantity_sold, avg(total_mrp) as total_mrp, avg(sales_value) as sales_value, avg(ptype_carts) as ptype_carts, avg(ptype_customers) as ptype_customers, avg(retail_discount_per_unit) as retail_discount_per_unit, avg(retail_price_per_unit) as retail_price_per_unit, max(snapshot_dt_ist) as snapshot_dt_ist from cte group by 1,2,3 \"\n", "\n", "            elif original_table_name == \"demand_forecasting_ptype_city_daily_availability\":\n", "                full_move_query = f\"with cte as (select date, case when city in ('Gurgaon','Gurugram') then 'HR-NCR' when city in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa', 'Panaji') then 'Goa' when city in ('Noida', 'Ghaziabad') then 'UP-NCR' when city in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar' when city in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli' when city in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh' when city in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur' when city in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar' when city in ('SOLAN', 'Solan') then 'Solan' when city in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar' when city in ('bikaner', 'Bikaner') then 'Bikaner' when city in ('Purn<PERSON>', 'purnia') then 'Purnia' else city end as city, product_type, ptype_avg_availability, snapshot_dt_ist from {interim_schema['schema_name']}.{interim_schema['table_name']} union all select date, city, case when product_type = 'Bhaiya Bhabhi' then 'Bhaiya Bhabhi Rakhi' when product_type = 'Lumba' then 'Lumba Rakhi' else product_type end as product_type, ptype_avg_availability, snapshot_dt_ist from {original_schema['schema_name']}.{original_schema['table_name']} where {partition_key[0]} is not null) select date, city, product_type, avg(ptype_avg_availability) as ptype_avg_availability, max(snapshot_dt_ist) as snapshot_dt_ist from cte group by 1,2,3 \"\n", "\n", "            elif original_table_name == \"demand_forecasting_ptype_city_daily_funnel\":\n", "                full_move_query = f\"with cte as (select date, case when city in ('Gurgaon','Gurugram') then 'HR-NCR' when city in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa', 'Panaji') then 'Goa' when city in ('Noida', 'Ghaziabad') then 'UP-NCR' when city in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar' when city in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli' when city in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh' when city in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur' when city in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar' when city in ('SOLAN', 'Solan') then 'Solan' when city in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar' when city in ('bikan<PERSON>', 'Bikaner') then 'Bikaner' when city in ('Purnia', 'purnia') then 'Purnia' else city end as city, product_type, instock_non_search_viewers_ptype, instock_ptype_num_pids, instock_ptype_width, instock_search_viewers_ptype, instock_viewers_ptype, non_search_viewers_ptype, ptype_num_pids, ptype_width, search_atcs_ptype, search_viewers_ptype, total_atcs_ptype, viewers_ptype, snapshot_dt_ist from {interim_schema['schema_name']}.{interim_schema['table_name']} union all select date, city, case when product_type = 'Bhaiya Bhabhi' then 'Bhaiya Bhabhi Rakhi' when product_type = 'Lumba' then 'Lumba Rakhi' else product_type end as product_type, instock_non_search_viewers_ptype, instock_ptype_num_pids, instock_ptype_width, instock_search_viewers_ptype, instock_viewers_ptype, non_search_viewers_ptype, ptype_num_pids, ptype_width, search_atcs_ptype, search_viewers_ptype, total_atcs_ptype, viewers_ptype, snapshot_dt_ist from {original_schema['schema_name']}.{original_schema['table_name']} where {partition_key[0]} is not null) select date, city, product_type, avg(instock_non_search_viewers_ptype) instock_non_search_viewers_ptype, avg(instock_ptype_num_pids) instock_ptype_num_pids, avg(instock_ptype_width) as instock_ptype_width, avg(instock_search_viewers_ptype) instock_search_viewers_ptype, avg(instock_viewers_ptype) instock_viewers_ptype, avg(non_search_viewers_ptype) non_search_viewers_ptype, avg(ptype_num_pids) ptype_num_pids, avg(ptype_width) ptype_width, avg(search_atcs_ptype) search_atcs_ptype, avg(search_viewers_ptype) as search_viewers_ptype, avg(total_atcs_ptype) total_atcs_ptype, avg(viewers_ptype) viewers_ptype, max(snapshot_dt_ist) snapshot_dt_ist from cte group by 1,2,3 \"\n", "\n", "            elif original_table_name == \"demand_forecasting_ptype_city_daily_searchers\":\n", "                full_move_query = f\"with cte as (select date, case when city in ('Gurgaon','Gurugram') then 'HR-NCR' when city in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa', 'Panaji') then 'Goa' when city in ('Noida', 'Ghaziabad') then 'UP-NCR' when city in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar' when city in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli' when city in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh' when city in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur' when city in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar' when city in ('SOLAN', 'Solan') then 'Solan' when city in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar' when city in ('bikan<PERSON>', 'Bikaner') then 'Bikaner' when city in ('Purnia', 'purnia') then 'Purnia' else city end as city, product_type, num_keywords, ptype_searchers, snapshot_dt_ist from {interim_schema['schema_name']}.{interim_schema['table_name']} union all select date, city, case when product_type = 'Bhaiya Bhabhi' then 'Bhaiya Bhabhi Rakhi' when product_type = 'Lumba' then 'Lumba Rakhi' else product_type end as product_type, num_keywords, ptype_searchers, snapshot_dt_ist from {original_schema['schema_name']}.{original_schema['table_name']} where {partition_key[0]} is not null) select date, city, product_type, avg(num_keywords) as num_keywords, avg(ptype_searchers) as ptype_searchers, max(snapshot_dt_ist) as snapshot_dt_ist from cte group by 1,2,3 \"\n", "\n", "            original_schema[\"load_type\"] = \"truncate\"\n", "            print(\"Original Partitions: \", original_schema[\"partition_key\"])\n", "\n", "            existing_partitions = original_schema[\"partition_key\"]\n", "\n", "            original_schema[\"partition_key\"] = [\n", "                x if x != \"feature_name\" else \"date\" for x in existing_partitions\n", "            ]\n", "\n", "            msg = f\"Running \\nquery = {full_move_query} \\nto insert full data \\nfrom {interim_schema['schema_name']}.{interim_schema['table_name']} \\nto {original_schema['schema_name']}.{original_schema['table_name']} in {original_schema['load_type']} mode. \\n\\nNew Partition keys = {original_schema['partition_key']}\\n\"\n", "            print_log(msg)\n", "            send_to_slack(send_log(msg))\n", "\n", "            # Copying from interim to main table\n", "            pb.to_trino(\n", "                full_move_query,\n", "                **original_schema,\n", "            )\n", "            msg = f\"All the data moved back into the partitioned enforeced original table {original_schema['table_name']}\\n\\n\"\n", "            send_to_slack(send_log(msg))\n", "            print_log(msg)\n", "\n", "            if datetime.now().date().weekday() % 3 == 0 and original_table_name in [\n", "                \"demand_forecasting_global_ft_base\",\n", "                \"demand_forecasting_ptype_city_daily_sales\",\n", "                \"demand_forecasting_ptype_city_daily_availability\",\n", "                \"demand_forecasting_ptype_city_daily_funnel\",\n", "                \"demand_forecasting_ptype_city_daily_searchers\",\n", "                \" \",\n", "            ]:\n", "                msg = f\"Running \\nquery = {full_move_query} \\nto insert full data \\nfrom {interim_schema['schema_name']}.{interim_schema['table_name']} \\nto {original_schema['schema_name']}.{original_schema['table_name']} in {original_schema['load_type']} mode. \\n\\nNew Partition keys = {original_schema['partition_key']}\\n\\n CREATING A BACK-UP\"\n", "                print_log(msg)\n", "                send_to_slack(send_log(msg))\n", "                original_schema[\"table_name\"] = original_schema[\"table_name\"] + \"_bkp\"\n", "                pb.to_trino(\n", "                    full_move_query,\n", "                    **original_schema,\n", "                )\n", "                msg = f\"Back-up created for the original table {original_schema['table_name']}\\n\\n\"\n", "                send_to_slack(send_log(msg))\n", "                print_log(msg)\n", "\n", "            del (\n", "                original_schema,\n", "                original_load_type,\n", "                original_schema_name,\n", "                original_table_name,\n", "                interim_schema,\n", "                interim_schema_name,\n", "                move_to_interim_query,\n", "                full_move_query,\n", "            )\n", "            gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "477ff383-8c1d-4eaa-8cfe-57f1b454eb26", "metadata": {}, "outputs": [], "source": ["if datetime.now().date().weekday() % 3 == 0:\n", "    msg = f\"Tables re-partitioned at {str(at_date_ist)} !!!\"\n", "    print_log(msg)\n", "    send_to_slack(send_log(msg))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}