{"cells": [{"cell_type": "markdown", "id": "faf4c18d-0eb9-4616-9177-6530a919e737", "metadata": {}, "source": ["# WeatherFeature Creation"]}, {"cell_type": "markdown", "id": "df142049-51c8-4399-99ff-9a4037bda859", "metadata": {"tags": []}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": null, "id": "625dcd28-1b04-4b8b-abee-79c794ae6dbb", "metadata": {}, "outputs": [], "source": ["module = \"Weather Ft Creation\""]}, {"cell_type": "code", "execution_count": null, "id": "b017486a-0192-4bd8-b261-e62101e0612d", "metadata": {"tags": []}, "outputs": [], "source": ["!pip install awswrangler\n", "!pip install numpy==1.26.4"]}, {"cell_type": "code", "execution_count": null, "id": "1fd4e028-0370-4dfd-9332-59ef90b9a3da", "metadata": {}, "outputs": [], "source": ["import os\n", "import pytz\n", "import numpy as np\n", "import pandas as pd\n", "import pencilbox as pb\n", "\n", "import awswrangler as wr\n", "from pytz import timezone\n", "from functools import reduce\n", "from datetime import datetime, timedelta\n", "from dateutil.relativedelta import relativedelta\n", "import itertools, sys\n", "from datetime import timedelta\n", "import gc"]}, {"cell_type": "code", "execution_count": null, "id": "23b6c09d-8e59-462e-b0ca-5d21d8a07290", "metadata": {}, "outputs": [], "source": ["cwd = \"/usr/local/airflow/dags/repo/dags/data_science/demand_forecasting_packaged/etl/data_prep\"\n", "sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "482892c9-9994-4f00-b70e-16ef24793b63", "metadata": {}, "outputs": [], "source": ["import support_functions as sf"]}, {"cell_type": "code", "execution_count": null, "id": "f18a75e7-96c1-4148-8128-6d18a71d7820", "metadata": {}, "outputs": [], "source": ["import global_feature_queries as gfq\n", "import table_schemas as schema"]}, {"cell_type": "code", "execution_count": null, "id": "0dd6bac7-bc45-4238-b8b1-9c155d187444", "metadata": {}, "outputs": [], "source": ["import requests\n", "import weather_cities as wc"]}, {"cell_type": "markdown", "id": "6e348449-7faa-46fe-950e-1fb76084bf43", "metadata": {"tags": []}, "source": ["## Utils"]}, {"cell_type": "code", "execution_count": null, "id": "44b5d786-e616-443d-a37f-1a7715d5089a", "metadata": {}, "outputs": [], "source": ["def send_log(*args, linspace=1):\n", "    return (\n", "        f\"[{module}]<>\"\n", "        + \"[run_ts=\"\n", "        + sf.run_id\n", "        + datetime.now(pytz.timezone(\"Asia/Kolkata\")).strftime(\"[ts=%Y%m%d %H%M%S] --> \")\n", "        + \" \".join(map(str, args))\n", "    )\n", "\n", "\n", "def print_log(*args, linspace=1, task=None, Stage=None, Outcome=None):\n", "    print(\n", "        f\"[{module}]<>\"\n", "        + \"[run_ts=\"\n", "        + sf.run_id\n", "        + datetime.now(pytz.timezone(\"Asia/Kolkata\")).strftime(\"[ts=%Y%m%d %H%M%S] --> \")\n", "        + \" \".join(map(str, args))\n", "    )\n", "\n", "\n", "def send_to_slack(msg):\n", "    try:\n", "        # 1 == 1\n", "        pb.send_slack_message(\n", "            channel=alerts_channel,\n", "            text=msg,\n", "        )\n", "    except Exception as e:\n", "        send_log(f\"Unable to push message {msg} to slack, got error = {e}\")"]}, {"cell_type": "code", "execution_count": null, "id": "65e884b7-3956-49f9-aad7-1c8b7a0e93c8", "metadata": {}, "outputs": [], "source": ["def get_historical_data(lat, long, date, api_key):\n", "    timestamp = int(datetime.strptime(date, \"%Y-%m-%d\").timestamp())\n", "    # Construct the API URL\n", "    url = f\"https://api.openweathermap.org/data/3.0/onecall/day_summary?lat={lat}&lon={lon}&date={date}&appid={api_key}\"\n", "    # Make the API request\n", "    response = requests.get(url)\n", "    # Check if the response is successful\n", "    if response.status_code == 200:\n", "        return response.json()\n", "    else:\n", "        return None"]}, {"cell_type": "code", "execution_count": null, "id": "3f377b82-a568-420a-9e5c-6354742ecbc3", "metadata": {}, "outputs": [], "source": ["def fetch_data(raw_query, **kwargs):\n", "    _trino_conn = pb.get_connection(\"[Warehouse] Trino\")\n", "    df = pd.read_sql(raw_query.format(**kwargs), _trino_conn)\n", "    del _trino_conn\n", "    return df\n", "\n", "\n", "def upload_parquet_to_s3(df, path: str, **kwargs) -> None:\n", "    wr.s3.to_parquet(df, path, **kwargs)\n", "\n", "\n", "def read_parquet_from_s3(s3_path: str, **kwargs) -> pd.DataFrame:\n", "    return wr.s3.read_parquet(s3_path, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "641eb1d8-065e-4b76-bb43-e7be3a739f64", "metadata": {"tags": []}, "outputs": [], "source": ["def sanitise_string(s):\n", "    return \"_\".join(s.replace(\",\", \"\").replace(\"&\", \"\").split())\n", "\n", "\n", "def get_value(info_dict, key):\n", "    if info_dict == 0:\n", "        return 0\n", "    return info_dict.get(key, None)"]}, {"cell_type": "code", "execution_count": null, "id": "f703881f-27af-48a1-96fa-73673b5d709b", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["run_date = datetime.now(timezone(\"Asia/Kolkata\")).date()\n", "run_date_utc = datetime.now().date()\n", "yesterday = run_date - relativedelta(days=1)\n", "alerts_channel = \"bl_forecasting_global_data_prep_alerts\""]}, {"cell_type": "code", "execution_count": null, "id": "a7741d85-46ab-425a-b91a-30d82d17060c", "metadata": {}, "outputs": [], "source": ["run_id = datetime.now() + <PERSON><PERSON><PERSON>(hours=5, minutes=30)\n", "run_id = run_id.replace(microsecond=0)\n", "run_id"]}, {"cell_type": "code", "execution_count": null, "id": "417a59f8-58f1-4db4-bd0c-e2c03d17a1ee", "metadata": {"tags": []}, "outputs": [], "source": ["s3_bucket = \"prod-dse-projects\"\n", "\n", "s3_folder_lod_cache = \"demand_forecasting/packaged/data/lod_cache\"\n", "s3_folder_device_activity = \"demand_forecasting/packaged/data/device_activity\"\n", "s3_folder_cohorts = \"demand_forecasting/packaged/data/user_cohorts\"\n", "s3_global_features = \"demand_forecasting/packaged/data/global_features\"\n", "\n", "_LOD_CACHE_PATH = f\"s3://{s3_bucket}/{s3_folder_lod_cache}\"\n", "_DEVICE_ACTIVITY_PATH = f\"s3://{s3_bucket}/{s3_folder_device_activity}\"\n", "_USER_COHORTS_PATH = f\"s3://{s3_bucket}/{s3_folder_cohorts}\"\n", "_GLOBAL_FEATURES_PATH = f\"s3://{s3_bucket}/{s3_global_features}\""]}, {"cell_type": "code", "execution_count": null, "id": "a77b88a7-9413-4d84-bd11-2ace9bca023e", "metadata": {}, "outputs": [], "source": ["print_log(run_date)\n", "print_log(yesterday)"]}, {"cell_type": "code", "execution_count": null, "id": "12e2ff13-9c10-4265-aced-e9ad9e0d21b3", "metadata": {}, "outputs": [], "source": ["send_to_slack(send_log(f\"Commencing Weather Features Prep at {str(run_date)}...\"))\n", "print_log(f\"Commencing Weather Features Prep at {str(run_date)}...\")"]}, {"cell_type": "markdown", "id": "2a592bce-94be-410d-92b0-1aeb40373014", "metadata": {"tags": []}, "source": ["### Initiating Execution Setup"]}, {"cell_type": "code", "execution_count": null, "id": "5d22202d-cd9e-4007-bcfe-23a0ebbb5372", "metadata": {}, "outputs": [], "source": ["if datetime.now().date().weekday() % 2 == 0:\n", "    city_asc_order = True\n", "else:\n", "    city_asc_order = False\n", "print_log(\"City names in ascending?\", city_asc_order)"]}, {"cell_type": "code", "execution_count": null, "id": "5ae2c9bb-4d5a-4f0a-91ce-9fe4ad873bee", "metadata": {}, "outputs": [], "source": ["full_reset = 1  # ALWAYS ON\n", "full_reset_date = \"2024-12-01\""]}, {"cell_type": "code", "execution_count": null, "id": "fe38a268-502c-40ce-9904-7dbf4ddfc02d", "metadata": {"tags": []}, "outputs": [], "source": ["city_dict = wc.city_dict"]}, {"cell_type": "code", "execution_count": null, "id": "a4400823-b74f-4af6-bae4-4a91340b2f58", "metadata": {}, "outputs": [], "source": ["city_df = pd.DataFrame(city_dict)\n", "city_df[\"city_name\"] = np.where(\n", "    city_df.city.isin([\"Gurugram\", \"Gurgaon\"]),\n", "    \"HR-NCR\",\n", "    np.where(\n", "        city_df.city.isin([\"Noida\", \"Ghaziabad\"]),\n", "        \"UP-NCR\",\n", "        np.where(\n", "            city_df.city.isin(\n", "                [\"Goa\", \"NorthGoa\", \"SouthGoa\", \"North-Goa\", \"North Goa\", \"South-Goa\", \"South Goa\"]\n", "            ),\n", "            \"Goa\",\n", "            city_df.city,\n", "        ),\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "156b6c03-1f36-4741-9e48-704537ffd2a2", "metadata": {}, "outputs": [], "source": ["weather_vars = [\"temp\", \"temp_min\", \"temp_max\"]\n", "city_list = [city_dict[i][\"city\"] for i in range(len(city_dict))]\n", "city_list = [\n", "    (\n", "        \"HR-NCR\"\n", "        if x == \"Gurugram\" or x == \"Gurgaon\" or x == \"HR-NCR\"\n", "        else (\n", "            \"UP-NCR\"\n", "            if x in [\"Noida\", \"Ghaziabad\"]\n", "            else (\n", "                \"Goa\"\n", "                if x\n", "                in [\n", "                    \"Goa\",\n", "                    \"NorthGoa\",\n", "                    \"SouthGoa\",\n", "                    \"North-Goa\",\n", "                    \"North Goa\",\n", "                    \"South-Goa\",\n", "                    \"South Goa\",\n", "                ]\n", "                else x\n", "            )\n", "        )\n", "    )\n", "    for x in city_list\n", "]\n", "city_list = sorted(list(set(city_list)))"]}, {"cell_type": "markdown", "id": "0b99ac1a-0a53-44c3-96b2-0780e4b8aa14", "metadata": {"tags": []}, "source": ["### API Limits Status Check"]}, {"cell_type": "code", "execution_count": null, "id": "c679827c-44cf-4869-a1b6-c2d325e5540b", "metadata": {}, "outputs": [], "source": ["msg = f\"Checking the status of API limits at {str(run_date)}\\n\\n\"\n", "print_log(msg)\n", "send_to_slack(send_log(msg))\n", "\n", "api_call_counter = {}\n", "api_call_counter_df = {}\n", "api_counter_log = {}\n", "api_counter_log_exists = {}\n", "api_counter_remaining = {}\n", "\n", "for k, api_key in sf.api_dict.items():\n", "\n", "    api_counter_log[k] = f\"weather_api_counter_log_{k}.csv\"\n", "    api_counter_log_exists[k] = sf.check_s3_file_exists(\n", "        s3_bucket, f\"{s3_global_features}/{api_counter_log[k]}\"\n", "    )\n", "    print_log(f\"API counter log exists for {k} : \" + str(api_counter_log_exists[k]))\n", "\n", "    if api_counter_log_exists[k]:\n", "        msg = f\"Checking the count of already executed calls of {k} API on {run_date.strftime('%Y-%m-%d')}\"\n", "        print_log(msg)\n", "        send_to_slack(send_log(msg))\n", "        api_call_counter_df[k] = wr.s3.read_csv(\n", "            f\"s3://{s3_bucket}/{s3_global_features}/{api_counter_log[k]}\"\n", "        )\n", "        api_call_counter[k] = api_call_counter_df[k][\n", "            api_call_counter_df[k].run_date == run_date_utc.strftime(\"%Y-%m-%d\")\n", "        ][\"api_call_counter\"].max()\n", "        if api_call_counter[k] is None or pd.isna(api_call_counter[k]):\n", "            api_call_counter[k] = 0\n", "    else:\n", "        api_call_counter[k] = 0\n", "\n", "    api_counter_remaining[k] = 1000 - api_call_counter[k]\n", "    msg = f\"Number of calls of {k} API at {str(run_date)} = {api_call_counter[k]}. Remaining hits = {1000 - api_call_counter[k]}\\n\"\n", "    print_log(msg)\n", "    send_to_slack(send_log(msg))\n", "del k, api_key"]}, {"cell_type": "markdown", "id": "ebbe0bd2-7590-45e1-9849-60e196707930", "metadata": {"tags": []}, "source": ["### Getting already done city x date combinations FOR HISTORY"]}, {"cell_type": "code", "execution_count": null, "id": "e56f90c9-c420-4bfd-b44c-ca5bd6bd1641", "metadata": {}, "outputs": [], "source": ["if sum(api_counter_remaining.values()) >= 0:\n", "    # if sum(api_counter_remaining.values()) >=0:\n", "    if full_reset > 0:\n", "        # edate_fixed = yesterday.strftime('%Y-%m-%d')\n", "        msg = f\"Getting already completed city x date combinations for weather features FOR HISTORY\"\n", "        print_log(msg)\n", "        send_to_slack(send_log(msg))\n", "\n", "        edate_fixed = yesterday.strftime(\"%Y-%m-%d\")  # \"2024-12-05\"\n", "\n", "        start_date = datetime.strptime(\"2022-01-01\", \"%Y-%m-%d\").date()\n", "        end_date = datetime.strptime(edate_fixed, \"%Y-%m-%d\").date()\n", "\n", "        weather_status_query = f\"select city, date from ds_etls.demand_forecasting_global_ft_base where city is not null and date is not null and feature_name in ('humidity', 'precipitation') and date <= '{edate_fixed}' and date(date) < date(snapshot_dt_ist) group by 1,2\"\n", "\n", "        done_dates = sf.fetch_data(weather_status_query)\n", "        done_dates[[\"date\"]] = done_dates[[\"date\"]].apply(sf.to_date)\n", "        done_dates[\"present\"] = 1\n", "        print_log(f\"Already completed city x date combinations: {len(done_dates)}\")\n", "    else:\n", "        start_date = datetime.now(timezone(\"Asia/Kolkata\")).date() - relativedelta(days=7)\n", "        end_date = start_date + relativedelta(days=180)\n", "        done_dates = pd.DataFrame()\n", "\n", "    dt = start_date\n", "    start_dates = []\n", "\n", "    while dt <= end_date:\n", "        start_dates.append(dt)\n", "        dt = dt + relativedelta(days=1)\n", "    start_dates.reverse()\n", "else:\n", "    msg = f\"No API calls remaining for {str(run_date)}. Skipping the API dates creation\"\n", "    print_log(msg)\n", "    send_to_slack(send_log(msg))\n", "    done_dates = pd.DataFrame()"]}, {"cell_type": "markdown", "id": "a9441f68-0ab9-479d-8748-1f5549f4e5d0", "metadata": {"tags": []}, "source": ["### Getting total and final combinations to run the weather data prep"]}, {"cell_type": "code", "execution_count": null, "id": "430a748f-8b28-425b-9a36-86bc8f7b37e5", "metadata": {"tags": []}, "outputs": [], "source": ["if sum(api_counter_remaining.values()) >= 0:\n", "    # if sum(api_counter_remaining.values()) >=0:\n", "\n", "    msg = f\"Creating the city x date grid for weather features\"\n", "    print_log(msg)\n", "    send_to_slack(send_log(msg))\n", "\n", "    ft_dt_combos = pd.DataFrame(itertools.product(city_list, start_dates))\n", "    ft_dt_combos.drop_duplicates(inplace=True)\n", "    ft_dt_combos.columns = [\"city\", \"date\"]\n", "    ft_dt_combos = ft_dt_combos.sort_values([\"city\", \"date\"]).reset_index(drop=True)\n", "\n", "    msg = f\"Total City x date combinations : \\n\\n {ft_dt_combos.groupby(['city'], as_index = False).agg({'date': 'nunique'}).sort_values('date', ascending = False).reset_index(drop = True)}\\n\\n\"\n", "    print_log(msg)\n", "    send_to_slack(send_log(msg))\n", "\n", "    if len(done_dates) > 0:\n", "        ft_dt_combos = ft_dt_combos.merge(done_dates, on=[\"city\", \"date\"], how=\"left\")\n", "        ft_dt_combos = ft_dt_combos[ft_dt_combos.present != 1]\n", "        ft_dt_combos.drop(columns=\"present\", inplace=True)\n", "        msg = f\"Remaining City x date combinations : \\n\\n {ft_dt_combos.groupby(['city'], as_index = False).agg({'date': 'nunique'}).sort_values('date', ascending = False).reset_index(drop = True)}\\n\\n\"\n", "        print_log(msg)\n", "        send_to_slack(send_log(msg))\n", "\n", "    ft_dt_combos = ft_dt_combos.merge(\n", "        city_df[[\"city_name\", \"lat\", \"long\"]], how=\"left\", left_on=\"city\", right_on=\"city_name\"\n", "    )\n", "\n", "    ft_dt_combos = ft_dt_combos.sort_values(\n", "        by=[\"city\", \"date\"], ascending=[city_asc_order, True]\n", "    ).reset_index(drop=True)\n", "\n", "    ft_dt_combos_bkp = ft_dt_combos.copy()\n", "\n", "    api_assignment = []\n", "    [\n", "        api_assignment.extend(np.repeat(k, max(1000 - api_call_counter[k], 0)))\n", "        for k in list(sf.api_dict.keys())\n", "    ]\n", "\n", "    if len(api_assignment) < len(ft_dt_combos):\n", "        ft_dt_combos = ft_dt_combos.loc[range(len(api_assignment)),]\n", "        ft_dt_combos[\"k\"] = api_assignment\n", "    else:\n", "        ft_dt_combos[\"k\"] = api_assignment[0 : len(ft_dt_combos)]\n", "\n", "    ft_dt_combos = ft_dt_combos[~pd.isna(ft_dt_combos[\"k\"])]\n", "    ft_dt_combos = ft_dt_combos.sort_values(\n", "        by=[\"city\", \"date\"], ascending=[city_asc_order, True]\n", "    ).reset_index(drop=True)\n", "\n", "    msg = f\"Current execution's City x date combinations : \\n\\n {ft_dt_combos.groupby(['city'], as_index = False).agg({'date': 'nunique'}).sort_values('date', ascending = False).reset_index(drop = True)}\\n\\n\"\n", "\n", "    print_log(msg)\n", "    send_to_slack(send_log(msg))\n", "\n", "    msg = f\"API-wise calls to be made: \\n\\n{ft_dt_combos.groupby(['k'], as_index = False).agg({'date': 'count'}).sort_values('date', ascending = False).reset_index(drop = True)}\"\n", "    print_log(msg)\n", "    send_to_slack(send_log(msg))\n", "else:\n", "    msg = f\"No API calls remaining for {str(run_date)}. Skipping the API Calls assignment\"\n", "    ft_dt_combos = pd.DataFrame()\n", "    print_log(msg)\n", "    send_to_slack(send_log(msg))"]}, {"cell_type": "code", "execution_count": null, "id": "b62846a6-5a18-4278-a213-920c8f2ee21f", "metadata": {}, "outputs": [], "source": ["raw_files = [\n", "    # \"raw_weather_data_Zirakpur_from_20250104_to_20250404_on_20250104.parquet\",\n", "    # 'raw_weather_data_Delhi_from_20220101_to_20241202_on_20241203.parquet',\n", "    # 'raw_weather_data_Faridabad_from_20220101_to_20241202_on_20241203.parquet',\n", "    # 'raw_weather_data_HR-NCR_from_20220101_to_20241202_on_20241203.parquet',\n", "    # 'raw_weather_data_Hyderabad_from_20220101_to_20241202_on_20241203.parquet',\n", "    # 'raw_weather_data_Kolkata_from_20220101_to_20231027_on_20241203.parquet',\n", "    # 'raw_weather_data_Bengaluru_from_20220101_to_20241202_on_20241204.parquet',\n", "    # 'raw_weather_data_Delhi_from_20220101_to_20241202_on_20241204.parquet',\n", "    # 'raw_weather_data_Faridabad_from_20220101_to_20241202_on_20241204.parquet',\n", "    # 'raw_weather_data_HR-NCR_from_20220101_to_20241202_on_20241204.parquet',\n", "    # 'raw_weather_data_Hyderabad_from_20220101_to_20241202_on_20241204.parquet',\n", "    # 'raw_weather_data_Kolkata_from_20220101_to_20231027_on_20241204.parquet',\n", "    # 'raw_weather_data_Bengaluru_from_20220101_to_20241202_on_20241205.parquet',\n", "    # 'raw_weather_data_Delhi_from_20220101_to_20241202_on_20241205.parquet',\n", "    # 'raw_weather_data_Faridabad_from_20220101_to_20241202_on_20241205.parquet',\n", "    # 'raw_weather_data_HR-NCR_from_20220101_to_20241202_on_20241205.parquet',\n", "    # 'raw_weather_data_Hyderabad_from_20220101_to_20241202_on_20241205.parquet',\n", "    # 'raw_weather_data_Kolkata_from_20220101_to_20231027_on_20241205.parquet'\n", "    # \"raw_weather_data_Zirakpur_from_20241216_to_20250316_on_20241216.parquet\"\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "b519b469-0773-46e5-9a2f-ef06fee15844", "metadata": {}, "outputs": [], "source": ["# raw_files = [\n", "#     'weather_data_20220101_20250131.parquet',\n", "#     'weather_data_20220102_20250131.parquet',\n", "#     'weather_data_20220102_20241117.parquet',\n", "#     'weather_data_20241118_20250131.parquet',\n", "#     'weather_data_2024_11_19.parquet',\n", "#     'weather_data_2024_11_20.parquet'\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "id": "3f0d8ccc-4da7-4a90-8dd0-1ee6099a2825", "metadata": {}, "outputs": [], "source": ["all_cities_df1 = []"]}, {"cell_type": "code", "execution_count": null, "id": "bcf5bd53-7d87-4f2a-b0c9-e01af6427766", "metadata": {}, "outputs": [], "source": ["if len(raw_files) > 1:\n", "    for x in raw_files:\n", "        all_cities_df1.append(read_parquet_from_s3(f\"{_GLOBAL_FEATURES_PATH}/{x}\"))\n", "    all_cities_df1 = pd.concat(all_cities_df1, axis=0)\n", "    all_cities_df1.drop_duplicates(inplace=True)\n", "    all_cities_df1.reset_index(drop=True, inplace=True)\n", "    all_cities_df1.head()\n", "elif len(raw_files) == 1:\n", "    all_cities_df1 = read_parquet_from_s3(f\"{_GLOBAL_FEATURES_PATH}/{x}\")\n", "else:\n", "    all_cities_df1 = pd.DataFrame()\n", "\n", "if len(done_dates) > 0 and len(all_cities_df1) > 0:\n", "    done_dates[\"date\"] = done_dates[\"date\"].astype(str)\n", "    all_cities_df1 = all_cities_df1.merge(done_dates, on=[\"city\", \"date\"], how=\"left\")\n", "    all_cities_df1 = all_cities_df1[all_cities_df1.present != 1]\n", "    all_cities_df1.drop(columns=\"present\", inplace=True)\n", "    all_cities_df1.reset_index(drop=True, inplace=True)\n", "    msg = f\"Remaining City x date combinations : \\n\\n {all_cities_df1.groupby(['city'], as_index = False).agg({'date': 'nunique'}).sort_values('date', ascending = False).reset_index(drop = True)}\\n\\n\"\n", "    print_log(msg)\n", "    send_to_slack(send_log(msg))"]}, {"cell_type": "markdown", "id": "e06b52c0-1498-4a55-ac1c-169815692957", "metadata": {"tags": []}, "source": ["## API Data Pull"]}, {"cell_type": "markdown", "id": "24d36dcf-b088-4653-8de6-3d7f4b65d9bb", "metadata": {}, "source": ["### Generating weather data for each city and date"]}, {"cell_type": "code", "execution_count": null, "id": "4a7dd81b-f213-4e58-8576-877e28da6c10", "metadata": {}, "outputs": [], "source": ["if len(ft_dt_combos) > 0:\n", "    temp = (\n", "        ft_dt_combos.groupby([\"city_name\"], as_index=False)\n", "        .agg({\"date\": \"nunique\"})\n", "        .sort_values(\"date\", ascending=False)\n", "        .reset_index(drop=True)\n", "    )\n", "else:\n", "    temp = (\n", "        ft_dt_combos_bkp.groupby([\"city_name\"], as_index=False)\n", "        .agg({\"date\": \"nunique\"})\n", "        .sort_values(\"date\", ascending=False)\n", "        .reset_index(drop=True)\n", "    )\n", "all_cities_df = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "id": "5b8fae93-643f-4355-8170-3e6c57f81c9e", "metadata": {}, "outputs": [], "source": ["if sum(api_counter_remaining.values()) >= 0:\n", "\n", "    if len(ft_dt_combos) > 0:\n", "        msg = f\"Creating Weather features for {len(ft_dt_combos)} city x days from {ft_dt_combos.date.min()} to {ft_dt_combos.date.max()}\"\n", "\n", "        print_log(msg)\n", "        send_to_slack(send_log(msg))\n", "        done_cities = 1\n", "        for city in temp.city_name.unique():\n", "\n", "            city_ddf = pd.DataFrame()\n", "\n", "            lat = ft_dt_combos.loc[ft_dt_combos.city_name == city, \"lat\"].unique()[0]\n", "            long = ft_dt_combos.loc[ft_dt_combos.city_name == city, \"long\"].unique()[0]\n", "\n", "            ft_dts = ft_dt_combos[ft_dt_combos.city_name == city].date.unique()\n", "\n", "            msg = f\"Working on {city=}({done_cities}/{ft_dt_combos.city.nunique()}), total dates = {len(ft_dts)}\"\n", "            print_log(msg)\n", "            send_to_slack(send_log(msg))\n", "\n", "            raw_weather_file = f\"raw_weather_data_{city}_from_{min(ft_dts).strftime('%Y%m%d')}_to_{max(ft_dts).strftime('%Y%m%d')}_on_{run_date.strftime('%Y%m%d')}.parquet\"\n", "            raw_file_exists = sf.check_s3_file_exists(\n", "                s3_bucket, f\"{s3_global_features}/{raw_weather_file}\"\n", "            )\n", "\n", "            print_log(\n", "                f\"Raw weather file : {s3_global_features}/{raw_weather_file} exists : \"\n", "                + str(raw_file_exists)\n", "            )\n", "\n", "            if not raw_file_exists:\n", "\n", "                for date in ft_dts:\n", "\n", "                    k = ft_dt_combos.loc[\n", "                        (ft_dt_combos.city_name == city) & (ft_dt_combos.date == date), \"k\"\n", "                    ].unique()[0]\n", "                    api_key = sf.api_dict[k]\n", "\n", "                    url = f\"https://api.openweathermap.org/data/3.0/onecall/day_summary?lat={lat}&lon={long}&date={date}&appid={api_key}&units=metric&tz=+05:30\"\n", "                    try:\n", "                        response = requests.get(url)\n", "                        api_call_counter[k] = api_call_counter[k] + 1\n", "                    except Exception as e:\n", "                        print_log(\n", "                            f\"Could not fetch data from weather API for {city=}({done_cities}/{ft_dt_combos.city.nunique()}) and {date=}\"\n", "                        )\n", "\n", "                    if response.status_code == 200:\n", "                        res = response.json()\n", "                        df = pd.DataFrame(res)\n", "                        df = df.reset_index()  # .drop(columns=\"wind\")\n", "                        df[\"city\"] = city\n", "                        df.fillna(0, inplace=True)\n", "                        city_ddf = pd.concat([city_ddf, df])\n", "                        del df\n", "                    else:\n", "                        print_log(\n", "                            f\"API call didn't return data of {city=}({done_cities}/{ft_dt_combos.city.nunique()}) for {date=}. Moving to next dates\"\n", "                        )\n", "                    del k, api_key\n", "\n", "                if len(city_ddf) > 0:\n", "                    msg = f\"Captured {len(city_ddf)} rows of weather data for {city=}({done_cities}/{ft_dt_combos.city.nunique()}) between dates : {city_ddf.date.min()} and {city_ddf.date.max()}.\"\n", "                    print_log(msg)\n", "                    send_to_slack(send_log(msg))\n", "\n", "                    city_ddf[\"wind\"] = city_ddf[\"wind\"].apply(lambda x: get_value(x, \"speed\"))\n", "                    upload_parquet_to_s3(\n", "                        df=city_ddf,\n", "                        path=f\"s3://{s3_bucket}/{s3_global_features}/{raw_weather_file}\",\n", "                    )\n", "\n", "                    all_cities_df = pd.concat([all_cities_df, city_ddf])\n", "                    del city_ddf\n", "\n", "            else:\n", "                msg = f\"Raw weather data of {city=}({done_cities}/{ft_dt_combos.city.nunique()}) already present on S3 for the current run, skipping the API data pull and reading data from S3\"\n", "                print_log(msg)\n", "                send_to_slack(send_log(msg))\n", "                city_ddf = read_parquet_from_s3(\n", "                    f\"s3://{s3_bucket}/{s3_global_features}/{raw_weather_file}\"\n", "                )\n", "\n", "                msg = f\"Read {len(city_ddf)} rows of weather data for {city=}({done_cities}/{ft_dt_combos.city.nunique()}) between dates : {city_ddf.date.min()} and {city_ddf.date.max()}.\"\n", "                print_log(msg)\n", "                send_to_slack(send_log(msg))\n", "                all_cities_df = pd.concat([all_cities_df, city_ddf])\n", "                del city_ddf\n", "\n", "            del lat, long, ft_dts\n", "            done_cities = done_cities + 1\n", "    elif len(ft_dt_combos_bkp) > 0:\n", "        msg = f\"No city x date combinations to be run on {str(run_date)} via API calls. Checking if already saved data is available to push to DB\"\n", "        print_log(msg)\n", "        send_to_slack(send_log(msg))\n", "        done_cities = 1\n", "        for city in temp.city_name.unique():\n", "\n", "            city_ddf = pd.DataFrame()\n", "\n", "            lat = ft_dt_combos_bkp.loc[ft_dt_combos_bkp.city_name == city, \"lat\"].unique()[0]\n", "            long = ft_dt_combos_bkp.loc[ft_dt_combos_bkp.city_name == city, \"long\"].unique()[0]\n", "\n", "            ft_dts = ft_dt_combos_bkp[ft_dt_combos_bkp.city_name == city].date.unique()\n", "\n", "            msg = f\"Working on {city=}({done_cities}/{ft_dt_combos_bkp.city.nunique()}), total dates = {len(ft_dts)}\"\n", "            print_log(msg)\n", "            send_to_slack(send_log(msg))\n", "\n", "            raw_weather_file = f\"raw_weather_data_{city}_from_{min(ft_dts).strftime('%Y%m%d')}_to_{max(ft_dts).strftime('%Y%m%d')}_on_{run_date.strftime('%Y%m%d')}.parquet\"\n", "            raw_file_exists = sf.check_s3_file_exists(\n", "                s3_bucket, f\"{s3_global_features}/{raw_weather_file}\"\n", "            )\n", "\n", "            print_log(\n", "                f\"Raw weather file : {s3_global_features}/{raw_weather_file} exists : \"\n", "                + str(raw_file_exists)\n", "            )\n", "\n", "            if not raw_file_exists:\n", "                msg = f\"Raw weather data of {city=}({done_cities}/{ft_dt_combos.city.nunique()}) is NOT present on S3 for the current run, skipping this city\"\n", "                print_log(msg)\n", "                send_to_slack(send_log(msg))\n", "            else:\n", "                msg = f\"Raw weather data of {city=}({done_cities}/{ft_dt_combos.city.nunique()}) already present on S3 for the current run, skipping the API data pull and reading data from S3\"\n", "                print_log(msg)\n", "                send_to_slack(send_log(msg))\n", "                city_ddf = read_parquet_from_s3(\n", "                    f\"s3://{s3_bucket}/{s3_global_features}/{raw_weather_file}\"\n", "                )\n", "\n", "                msg = f\"Read {len(city_ddf)} rows of weather data for {city=}({done_cities}/{ft_dt_combos.city.nunique()}) between dates : {city_ddf.date.min()} and {city_ddf.date.max()}.\"\n", "                print_log(msg)\n", "                send_to_slack(send_log(msg))\n", "                all_cities_df = pd.concat([all_cities_df, city_ddf])\n", "                del city_ddf\n", "\n", "            del lat, long, ft_dts\n", "            done_cities = done_cities + 1\n", "    else:\n", "        msg = f\"No more historical dates remaining for any city. Moving on !!!\\n\"\n", "        print_log(msg)\n", "        send_to_slack(send_log(msg))\n", "else:\n", "    msg = f\"No API calls remaining for {str(run_date)}. Exiting the weather creation\""]}, {"cell_type": "markdown", "id": "d959c9a2-ff46-4833-ba54-b4e8d0677de2", "metadata": {"tags": []}, "source": ["### Updating the API counters"]}, {"cell_type": "code", "execution_count": null, "id": "7b9ba9de-d17c-4d6c-9897-1bff2768a8d3", "metadata": {}, "outputs": [], "source": ["if sum(api_counter_remaining.values()) > 0 and len(ft_dt_combos) > 0:\n", "    # if sum(api_counter_remaining.values()) >=0 and len(ft_dt_combos) >0:\n", "    msg = f\"Updating the API counters for {str(run_date)}\"\n", "    print_log(msg)\n", "    send_to_slack(send_log(msg))\n", "    for k in list(ft_dt_combos.k.unique()):\n", "        temp = pd.DataFrame(\n", "            {\n", "                \"run_date\": [run_date_utc.strftime(\"%Y-%m-%d\")],\n", "                \"api_call_counter\": [\n", "                    ft_dt_combos[ft_dt_combos.k == k]\n", "                    .groupby(\"k\", as_index=False)\n", "                    .agg({\"date\": \"count\"})[\"date\"][0]\n", "                ],\n", "            }\n", "        )\n", "\n", "        if api_counter_log_exists[k]:\n", "            api_call_counter_df[k] = pd.concat([api_call_counter_df[k], temp])\n", "            wr.s3.to_csv(\n", "                api_call_counter_df[k],\n", "                f\"s3://{s3_bucket}/{s3_global_features}/{api_counter_log[k]}\",\n", "            )\n", "        else:\n", "            wr.s3.to_csv(temp, f\"s3://{s3_bucket}/{s3_global_features}/{api_counter_log[k]}\")\n", "    print_log(\"Done\")\n", "    send_to_slack(send_log(\"Done\"))\n", "else:\n", "    msg = f\"No API calls remaining for {str(run_date)}. Skipping the API counter updation\"\n", "    print_log(msg)\n", "    send_to_slack(send_log(msg))"]}, {"cell_type": "code", "execution_count": null, "id": "35ec6072-6967-4513-97ea-19d8a78ad5f9", "metadata": {}, "outputs": [], "source": ["if len(all_cities_df1) > 0:\n", "    if len(all_cities_df) > 0:\n", "        all_cities_df = pd.concat([all_cities_df, all_cities_df1])\n", "        all_cities_df.drop_duplicates(subset=[\"city\", \"date\"], inplace=True)\n", "    else:\n", "        all_cities_df = all_cities_df1"]}, {"cell_type": "markdown", "id": "a2a09f5f-a10f-48d0-9fd2-9eb8a3070227", "metadata": {"tags": []}, "source": ["## Aggregating the weather features and uploading to DB"]}, {"cell_type": "code", "execution_count": null, "id": "5c11c0d2-6bb7-4db4-af1e-20b764094f2c", "metadata": {}, "outputs": [], "source": ["actual_weather_file = (\n", "    f\"{_GLOBAL_FEATURES_PATH}/actual_weather_data_{str(run_date).replace('-', '_')}.parquet\"\n", ")\n", "actual_weather_file_exists = sf.check_s3_file_exists(\n", "    s3_bucket, f\"{s3_global_features}/actual_weather_data_{str(run_date).replace('-', '_')}.parquet\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a781a6ba-431f-4509-af3d-a90bff0ca50e", "metadata": {}, "outputs": [], "source": ["if actual_weather_file_exists:\n", "    ft_df_main_old = read_parquet_from_s3(actual_weather_file)"]}, {"cell_type": "code", "execution_count": null, "id": "133d3002-4229-432e-973b-b15d919584a6", "metadata": {}, "outputs": [], "source": ["if len(all_cities_df) > 0 and actual_weather_file_exists:\n", "    msg = f\"Aggregating the weather features and uploading to DB\"\n", "    print_log(msg)\n", "    send_to_slack(send_log(msg))\n", "\n", "    all_cities_df_non_temp = all_cities_df.groupby([\"city\", \"date\"], as_index=False).agg(\n", "        {\n", "            \"humidity\": \"max\",\n", "            \"precipitation\": \"sum\",\n", "            \"pressure\": \"max\",\n", "            \"wind\": \"max\",\n", "        }\n", "    )\n", "    all_cities_df_non_temp.columns = (\n", "        all_cities_df_non_temp.columns.str.replace(\"humidity_max\", \"humidity\")\n", "        .str.replace(\"precipitation_sum\", \"precipitation\")\n", "        .str.replace(\"pressure_max\", \"pressure\")\n", "        .str.replace(\"wind_max\", \"wind\")\n", "    )\n", "\n", "    all_cities_df_temp = (\n", "        all_cities_df[(all_cities_df[\"index\"] != \"total\")]\n", "        .groupby([\"city\", \"date\"], as_index=False)\n", "        .agg({\"temperature\": [\"min\", \"mean\", \"max\"]})\n", "    )\n", "    all_cities_df_temp.columns = [\"_\".join(a) for a in all_cities_df_temp.columns.to_flat_index()]\n", "\n", "    all_cities_df_temp.columns = (\n", "        all_cities_df_temp.columns.str.replace(\"city_\", \"city\")\n", "        .str.replace(\"date_\", \"date\")\n", "        .str.replace(\"temperature_min\", \"temp_min\")\n", "        .str.replace(\"temperature_max\", \"temp_max\")\n", "        .str.replace(\"temperature_mean\", \"temp\")\n", "    )\n", "    all_cities_df = all_cities_df_non_temp.merge(all_cities_df_temp)\n", "\n", "    ft_df = all_cities_df.melt(\n", "        id_vars=[\"city\", \"date\"],\n", "        var_name=\"feature_name\",\n", "        value_name=\"feature_value\",\n", "    )\n", "    print_log(ft_df.shape)\n", "    ft_df = ft_df.groupby([\"city\", \"date\", \"feature_name\"], as_index=False).agg(\n", "        {\"feature_value\": \"mean\"}\n", "    )\n", "    ft_df[\"snapshot_dt_ist\"] = run_id\n", "    print_log(ft_df.shape)\n", "\n", "    print_log(ft_df.date.min())\n", "    print_log(ft_df.date.max())\n", "\n", "    x = ft_df[[\"city\", \"date\"]].drop_duplicates()\n", "    x.reset_index(drop=True, inplace=True)\n", "\n", "    x[\"partition\"] = x[\"city\"] + \"-\" + x[\"date\"].astype(str)\n", "    x[\"chunk\"] = None\n", "    idx = range(len(x))\n", "\n", "    groups = idx[:: min(290, len(x) - 1)]\n", "    for i in range(len(groups) - 1):\n", "        x.loc[groups[i] : groups[(i + 1)], \"chunk\"] = i\n", "    x.reset_index(drop=True, inplace=True)\n", "\n", "    ft_df_main = ft_df.merge(x[[\"city\", \"date\", \"chunk\"]], on=[\"city\", \"date\"])\n", "    # ft_df_main = ft_df_main[~pd.isna(ft_df_main.chunk)]\n", "    chunk_max = ft_df_main.chunk.max()\n", "    ft_df_main.loc[pd.isna(ft_df_main.chunk), \"chunk\"] = chunk_max + 1\n", "\n", "    min_date = ft_df_main.date.min().replace(\"-\", \"\")\n", "    max_date = ft_df_main.date.max().replace(\"-\", \"\")\n", "\n", "    if len(ft_df_main) > len(ft_df_main_old):\n", "        msg = \"Got more data from current API calls, saving the aggregates to S3\"\n", "        print_log(msg)\n", "        send_to_slack(send_log(msg))\n", "        upload_parquet_to_s3(ft_df_main, actual_weather_file)"]}, {"cell_type": "code", "execution_count": null, "id": "30ef958e-4e88-4b9a-90ec-b0b4133b0ad7", "metadata": {}, "outputs": [], "source": ["if actual_weather_file_exists:\n", "\n", "    msg = f\"Aggregated weather data of actuals already present in S3 for {str(run_date)}. Reading and pushing to DB\"\n", "    print_log(msg)\n", "    send_to_slack(send_log(msg))\n", "\n", "    ft_df_main = read_parquet_from_s3(actual_weather_file)\n", "    min_date = ft_df_main.date.min().replace(\"-\", \"\")\n", "    max_date = ft_df_main.date.max().replace(\"-\", \"\")\n", "\n", "    for chunk in ft_df_main.chunk.unique():\n", "        ft_df = ft_df_main[ft_df_main.chunk == chunk]\n", "        s3_weather_path = (\n", "            f\"{_GLOBAL_FEATURES_PATH}/weather_data_{min_date}_{max_date}_{chunk=}.parquet\"\n", "        )\n", "        try:\n", "            msg = f\"{chunk=}/{ft_df_main.chunk.nunique()}: Pushing {len(ft_df)} rows of weather features for {ft_df.city.nunique()} cities and {ft_df.date.nunique()} days.\"\n", "            print_log(msg)\n", "            send_to_slack(send_log(msg))\n", "            pb.to_trino(\n", "                ft_df[[rec[\"name\"] for rec in schema.global_table_schema[\"column_dtypes\"]]],\n", "                **schema.global_table_schema,\n", "            )\n", "            msg = f\"Done. Saving to Weather File on S3 = {s3_weather_path}\"\n", "            print_log(msg)\n", "            send_to_slack(send_log(msg))\n", "            upload_parquet_to_s3(\n", "                df=ft_df,\n", "                path=s3_weather_path,\n", "            )\n", "            print_log(\"Done !!!\\n\\n\")\n", "            send_to_slack(send_log(f\"Done !!!\"))\n", "        except Exception as e:\n", "            error_msg = str(e).split(\"cause\")[0]\n", "            msg = (\n", "                f\"Could not upload weather features, got error = {error_msg}. Saving to S3 instead\"\n", "            )\n", "            print_log(msg)\n", "            send_to_slack(send_log(msg))\n", "            upload_parquet_to_s3(\n", "                df=ft_df,\n", "                path=s3_weather_path,\n", "            )\n", "            print_log(f\"Saved data in Weather File = {s3_weather_path}\")\n", "            send_to_slack(send_log(f\"Saved data in Weather File = {s3_weather_path}\"))\n", "    msg = f\"Weather Feature creation between {str(min_date)} and {str(max_date)} Done !!!\"\n", "    send_to_slack(send_log(msg))\n", "    print_log(msg)\n", "\n", "else:\n", "    if len(all_cities_df) > 0:\n", "\n", "        msg = f\"Aggregating the weather features and uploading to DB\"\n", "        print_log(msg)\n", "        send_to_slack(send_log(msg))\n", "\n", "        all_cities_df_non_temp = all_cities_df.groupby([\"city\", \"date\"], as_index=False).agg(\n", "            {\n", "                \"humidity\": \"max\",\n", "                \"precipitation\": \"sum\",\n", "                \"pressure\": \"max\",\n", "                \"wind\": \"max\",\n", "            }\n", "        )\n", "        all_cities_df_non_temp.columns = (\n", "            all_cities_df_non_temp.columns.str.replace(\"humidity_max\", \"humidity\")\n", "            .str.replace(\"precipitation_sum\", \"precipitation\")\n", "            .str.replace(\"pressure_max\", \"pressure\")\n", "            .str.replace(\"wind_max\", \"wind\")\n", "        )\n", "\n", "        all_cities_df_temp = (\n", "            all_cities_df[(all_cities_df[\"index\"] != \"total\")]\n", "            .groupby([\"city\", \"date\"], as_index=False)\n", "            .agg({\"temperature\": [\"min\", \"mean\", \"max\"]})\n", "        )\n", "        all_cities_df_temp.columns = [\n", "            \"_\".join(a) for a in all_cities_df_temp.columns.to_flat_index()\n", "        ]\n", "\n", "        all_cities_df_temp.columns = (\n", "            all_cities_df_temp.columns.str.replace(\"city_\", \"city\")\n", "            .str.replace(\"date_\", \"date\")\n", "            .str.replace(\"temperature_min\", \"temp_min\")\n", "            .str.replace(\"temperature_max\", \"temp_max\")\n", "            .str.replace(\"temperature_mean\", \"temp\")\n", "        )\n", "        all_cities_df = all_cities_df_non_temp.merge(all_cities_df_temp)\n", "\n", "        ft_df = all_cities_df.melt(\n", "            id_vars=[\"city\", \"date\"],\n", "            var_name=\"feature_name\",\n", "            value_name=\"feature_value\",\n", "        )\n", "        print_log(ft_df.shape)\n", "        ft_df = ft_df.groupby([\"city\", \"date\", \"feature_name\"], as_index=False).agg(\n", "            {\"feature_value\": \"mean\"}\n", "        )\n", "        ft_df[\"snapshot_dt_ist\"] = run_id\n", "        print_log(ft_df.shape)\n", "\n", "        print_log(ft_df.date.min())\n", "        print_log(ft_df.date.max())\n", "\n", "        x = ft_df[[\"city\", \"date\"]].drop_duplicates()\n", "        x.reset_index(drop=True, inplace=True)\n", "\n", "        x[\"partition\"] = x[\"city\"] + \"-\" + x[\"date\"].astype(str)\n", "        x[\"chunk\"] = None\n", "        idx = range(len(x))\n", "\n", "        groups = idx[:: min(290, len(x) - 1)]\n", "        for i in range(len(groups) - 1):\n", "            x.loc[groups[i] : groups[(i + 1)], \"chunk\"] = i\n", "        x.reset_index(drop=True, inplace=True)\n", "\n", "        ft_df_main = ft_df.merge(x[[\"city\", \"date\", \"chunk\"]], on=[\"city\", \"date\"])\n", "        # ft_df_main = ft_df_main[~pd.isna(ft_df_main.chunk)]\n", "        chunk_max = ft_df_main.chunk.max()\n", "        ft_df_main.loc[pd.isna(ft_df_main.chunk), \"chunk\"] = chunk_max + 1\n", "\n", "        min_date = ft_df_main.date.min().replace(\"-\", \"\")\n", "        max_date = ft_df_main.date.max().replace(\"-\", \"\")\n", "\n", "        upload_parquet_to_s3(ft_df_main, actual_weather_file)\n", "\n", "        for chunk in ft_df_main.chunk.unique():\n", "            ft_df = ft_df_main[ft_df_main.chunk == chunk]\n", "            s3_weather_path = (\n", "                f\"{_GLOBAL_FEATURES_PATH}/weather_data_{min_date}_{max_date}_{chunk=}.parquet\"\n", "            )\n", "            try:\n", "                msg = f\"{chunk=}/{ft_df_main.chunk.nunique()}: Pushing {len(ft_df)} rows of weather features for {ft_df.city.nunique()} cities and {ft_df.date.nunique()} days.\"\n", "                print_log(msg)\n", "                send_to_slack(send_log(msg))\n", "                pb.to_trino(\n", "                    ft_df[[rec[\"name\"] for rec in schema.global_table_schema[\"column_dtypes\"]]],\n", "                    **schema.global_table_schema,\n", "                )\n", "                msg = f\"Done. Saving to Weather File on S3 = {s3_weather_path}\"\n", "                print_log(msg)\n", "                send_to_slack(send_log(msg))\n", "                upload_parquet_to_s3(\n", "                    df=ft_df,\n", "                    path=s3_weather_path,\n", "                )\n", "                print_log(\"Done !!!\\n\\n\")\n", "                send_to_slack(send_log(f\"Done !!!\"))\n", "            except Exception as e:\n", "                error_msg = str(e).split(\"cause\")[0]\n", "                msg = f\"Could not upload weather features, got error = {error_msg}. Saving to S3 instead\"\n", "                print_log(msg)\n", "                send_to_slack(send_log(msg))\n", "                upload_parquet_to_s3(\n", "                    df=ft_df,\n", "                    path=s3_weather_path,\n", "                )\n", "                print_log(f\"Saved data in Weather File = {s3_weather_path}\")\n", "                send_to_slack(send_log(f\"Saved data in Weather File = {s3_weather_path}\"))\n", "        msg = f\"Weather Feature creation between {str(min_date)} and {str(max_date)} Done !!!\"\n", "        send_to_slack(send_log(msg))\n", "        print_log(msg)\n", "    else:\n", "        msg = f\"Weather Feature creation for actual dates is done !!!\\n\\n\"\n", "        send_to_slack(send_log(msg))\n", "        print_log(msg)\n", "\n", "    gc.collect()"]}, {"cell_type": "markdown", "id": "419ef1ba-a66c-4a24-88b9-1fb704a6b44d", "metadata": {}, "source": ["## Moving back to API based data collection : FOR FUTURE DATES"]}, {"cell_type": "markdown", "id": "a72f5629-1444-45d0-b02d-4d821c9a6302", "metadata": {}, "source": ["### Getting the already done combinations of city x date"]}, {"cell_type": "code", "execution_count": null, "id": "863206f3-ebcc-481c-8b72-3af6ff6d402a", "metadata": {}, "outputs": [], "source": ["full_reset = 0"]}, {"cell_type": "markdown", "id": "377c95d4-85be-46df-b17b-039f5b101909", "metadata": {"tags": []}, "source": ["### API Limits Status Check"]}, {"cell_type": "code", "execution_count": null, "id": "27849699-3f0b-452d-8f8e-24c0b09537ac", "metadata": {}, "outputs": [], "source": ["msg = f\"Checking the status of API limits at {str(run_date)}\\n\\n\"\n", "print_log(msg)\n", "send_to_slack(send_log(msg))\n", "\n", "api_call_counter = {}\n", "api_call_counter_df = {}\n", "api_counter_log = {}\n", "api_counter_log_exists = {}\n", "api_counter_remaining = {}\n", "\n", "for k, api_key in sf.api_dict.items():\n", "\n", "    api_counter_log[k] = f\"weather_api_counter_log_{k}.csv\"\n", "    api_counter_log_exists[k] = sf.check_s3_file_exists(\n", "        s3_bucket, f\"{s3_global_features}/{api_counter_log[k]}\"\n", "    )\n", "    print_log(f\"API counter log exists for {k} : \" + str(api_counter_log_exists[k]))\n", "\n", "    if api_counter_log_exists[k]:\n", "        msg = f\"Checking the count of already executed calls of {k} API on {run_date.strftime('%Y-%m-%d')}\"\n", "        print_log(msg)\n", "        send_to_slack(send_log(msg))\n", "        api_call_counter_df[k] = wr.s3.read_csv(\n", "            f\"s3://{s3_bucket}/{s3_global_features}/{api_counter_log[k]}\"\n", "        )\n", "        api_call_counter[k] = api_call_counter_df[k][\n", "            api_call_counter_df[k].run_date == run_date_utc.strftime(\"%Y-%m-%d\")\n", "        ][\"api_call_counter\"].max()\n", "        if api_call_counter[k] is None or pd.isna(api_call_counter[k]):\n", "            api_call_counter[k] = 0\n", "    else:\n", "        api_call_counter[k] = 0\n", "\n", "    api_counter_remaining[k] = 1000 - api_call_counter[k]\n", "    msg = f\"Number of calls of {k} API at {str(run_date)} = {api_call_counter[k]}. Remaining hits = {1000 - api_call_counter[k]}\\n\"\n", "    print_log(msg)\n", "    send_to_slack(send_log(msg))\n", "del k, api_key"]}, {"cell_type": "code", "execution_count": null, "id": "a266ec0d-9a97-4652-a9e4-8b25cac05740", "metadata": {}, "outputs": [], "source": ["if sum(api_counter_remaining.values()) > 0:\n", "\n", "    if full_reset > 0:\n", "\n", "        msg = f\"Getting already captured city x date combinations for weather features FOR FUTURE DATES\"\n", "        print_log(msg)\n", "        send_to_slack(send_log(msg))\n", "\n", "        start_date = datetime.now(timezone(\"Asia/Kolkata\")).date() - relativedelta(days=0)\n", "        # end_date = start_date + relativedelta(days=180)\n", "        end_date = datetime.strptime(\"2025-12-31\", \"%Y-%m-%d\").date()\n", "\n", "        sdate_fixed = start_date.strftime(\"%Y-%m-%d\")  # \"2024-12-05\"\n", "\n", "        weather_status_query = f\"select city, date from ds_etls.demand_forecasting_global_ft_base where city is not null and date is not null and feature_name in {tuple(weather_vars)} and date >= '{sdate_fixed}' group by 1,2\"\n", "\n", "        done_dates = sf.fetch_data(weather_status_query)\n", "        done_dates[[\"date\"]] = done_dates[[\"date\"]].apply(sf.to_date)\n", "        done_dates[\"present\"] = 1\n", "        print_log(f\"Already completed city x date combinations: {len(done_dates)}\")\n", "    else:\n", "        start_date = datetime.now(timezone(\"Asia/Kolkata\")).date() - relativedelta(days=1)\n", "        end_date = start_date + relativedelta(days=180)\n", "        done_dates = pd.DataFrame()\n", "\n", "    dt = start_date\n", "    start_dates = []\n", "\n", "    while dt <= end_date:\n", "        start_dates.append(dt)\n", "        dt = dt + relativedelta(days=1)\n", "    start_dates.reverse()\n", "else:\n", "    msg = f\"No API calls remaining for {str(run_date)}. Exiting the weather creation\""]}, {"cell_type": "markdown", "id": "fea8d482-6f2e-46f3-aa58-28e1fb5c5dab", "metadata": {}, "source": ["### Re-creating effective combinations of city x date"]}, {"cell_type": "code", "execution_count": null, "id": "46a825c5-1f7d-4c3e-8815-c277680f384a", "metadata": {}, "outputs": [], "source": ["msg = f\"Creating the city x date grid for weather features\"\n", "print_log(msg)\n", "send_to_slack(send_log(msg))\n", "\n", "ft_dt_combos = pd.DataFrame(itertools.product(city_list, start_dates))\n", "ft_dt_combos.drop_duplicates(inplace=True)\n", "ft_dt_combos.columns = [\"city\", \"date\"]\n", "ft_dt_combos = ft_dt_combos.sort_values([\"city\", \"date\"]).reset_index(drop=True)\n", "\n", "msg = f\"Total City x date combinations : \\n\\n {ft_dt_combos.groupby(['city'], as_index = False).agg({'date': 'nunique'}).sort_values('date', ascending = False).reset_index(drop = True)}\\n\\n\"\n", "print_log(msg)\n", "send_to_slack(send_log(msg))"]}, {"cell_type": "code", "execution_count": null, "id": "bdc7ef45-f41c-4166-92f6-f3885e6579b5", "metadata": {}, "outputs": [], "source": ["if len(done_dates) > 0:\n", "    ft_dt_combos = ft_dt_combos.merge(done_dates, on=[\"city\", \"date\"], how=\"left\")\n", "    ft_dt_combos = ft_dt_combos[ft_dt_combos.present != 1]\n", "    ft_dt_combos.drop(columns=\"present\", inplace=True)\n", "    msg = f\"Remaining City x date combinations : \\n\\n {ft_dt_combos.groupby(['city'], as_index = False).agg({'date': 'nunique'}).sort_values('date', ascending = False).reset_index(drop = True)}\\n\\n\"\n", "    print_log(msg)\n", "    send_to_slack(send_log(msg))\n", "\n", "ft_dt_combos = ft_dt_combos.merge(\n", "    city_df[[\"city_name\", \"lat\", \"long\"]], how=\"left\", left_on=\"city\", right_on=\"city_name\"\n", ")\n", "ft_dt_combos = ft_dt_combos.sort_values(\n", "    by=[\"city\", \"date\"], ascending=[city_asc_order, False]\n", ").reset_index(drop=True)"]}, {"cell_type": "markdown", "id": "0d586d9e-0947-4759-8917-698aa24cae55", "metadata": {}, "source": ["### Custom City Order"]}, {"cell_type": "code", "execution_count": null, "id": "61c8c9d4-cfa8-41a9-b1f5-716d73352ddd", "metadata": {}, "outputs": [], "source": ["priority_city_list = [\n", "    \"Delhi\",\n", "    \"HR-NCR\",\n", "    \"Gurgaon\",\n", "    \"Gurugram\",\n", "    \"Bengaluru\",\n", "    \"UP-NCR\",\n", "    \"Mumbai\",\n", "    \"Chennai\",\n", "    \"Kolkata\",\n", "    \"Jaipur\",\n", "    \"Ahmedabad\",\n", "    \"Hyderabad\",\n", "    \"Pune\",\n", "    \"Lucknow\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "a6976f79-73a9-4a84-a3e3-19525dea9d50", "metadata": {}, "outputs": [], "source": ["priority_city_list = list(ft_dt_combos.city.unique())"]}, {"cell_type": "code", "execution_count": null, "id": "5a326a64-6ace-410b-9d7b-1f82dad37a86", "metadata": {}, "outputs": [], "source": ["priority_dates_end = run_date + relativedelta(days=50)"]}, {"cell_type": "code", "execution_count": null, "id": "1c1b8235-e4d0-45c0-85f3-7ac5089a285c", "metadata": {}, "outputs": [], "source": ["ft_dt_combos[\"priority\"] = np.where(\n", "    (ft_dt_combos.city.isin(priority_city_list)) & (ft_dt_combos.date <= priority_dates_end),\n", "    \"A\",\n", "    \"B\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "43af887c-d9d6-47a1-bb22-1167cb20029f", "metadata": {"tags": []}, "outputs": [], "source": ["api_assignment = []\n", "[\n", "    api_assignment.extend(np.repeat(k, max(1000 - api_call_counter[k], 0)))\n", "    for k in list(sf.api_dict.keys())\n", "]\n", "\n", "if len(api_assignment) > 0:\n", "\n", "    ft_dt_combos = ft_dt_combos.sort_values(\n", "        by=[\"priority\", \"city\", \"date\"], ascending=[True, city_asc_order, True]\n", "    ).reset_index(drop=True)\n", "\n", "    ft_dt_combos.drop(columns=\"priority\", inplace=True, errors=\"ignore\")\n", "\n", "    if len(api_assignment) < len(ft_dt_combos):\n", "        ft_dt_combos = ft_dt_combos.loc[range(len(api_assignment)),]\n", "        ft_dt_combos[\"k\"] = api_assignment\n", "    else:\n", "        ft_dt_combos[\"k\"] = api_assignment[0 : len(ft_dt_combos)]\n", "\n", "    ft_dt_combos = ft_dt_combos[~pd.isna(ft_dt_combos[\"k\"])]\n", "\n", "    msg = f\"Current execution's City x date combinations : \\n\\n {ft_dt_combos.groupby(['city'], as_index = False).agg({'date': 'nunique'}).sort_values('date', ascending = False).reset_index(drop = True)}\\n\\n\"\n", "\n", "    print_log(msg)\n", "    send_to_slack(send_log(msg))\n", "\n", "    msg = f\"API-wise calls to be made: \\n\\n{ft_dt_combos.groupby(['k'], as_index = False).agg({'date': 'count'}).sort_values('date', ascending = False).reset_index(drop = True)}\"\n", "    print_log(msg)\n", "    send_to_slack(send_log(msg))"]}, {"cell_type": "markdown", "id": "7d0514e4-3f3a-4f87-96e2-febd99fb0a1c", "metadata": {}, "source": ["### Generating weather data for each city and date"]}, {"cell_type": "code", "execution_count": null, "id": "34e71774-d35a-4f66-bd15-c01036ea876d", "metadata": {}, "outputs": [], "source": ["temp = (\n", "    ft_dt_combos.groupby([\"city_name\"], as_index=False)\n", "    .agg({\"date\": \"nunique\"})\n", "    .sort_values(\"date\", ascending=False)\n", "    .reset_index(drop=True)\n", ")\n", "temp"]}, {"cell_type": "code", "execution_count": null, "id": "ae5ccfef-5832-448c-b5bb-30ea552464d9", "metadata": {}, "outputs": [], "source": ["all_cities_df = pd.DataFrame()\n", "\n", "if sum(api_counter_remaining.values()) > 0:\n", "    if len(ft_dt_combos) > 0:\n", "        i = 0\n", "        num_cities = ft_dt_combos.city.nunique()\n", "        for city in sorted(temp.city_name.unique(), reverse=~city_asc_order):\n", "\n", "            i = i + 1\n", "            city_ddf = pd.DataFrame()\n", "\n", "            lat = ft_dt_combos.loc[ft_dt_combos.city_name == city, \"lat\"].unique()[0]\n", "            long = ft_dt_combos.loc[ft_dt_combos.city_name == city, \"long\"].unique()[0]\n", "\n", "            ft_dts = ft_dt_combos[ft_dt_combos.city_name == city].date.unique()\n", "\n", "            msg = f\"Working on {city=}, total dates = {len(ft_dts)}\"\n", "            print_log(msg)\n", "            send_to_slack(send_log(msg))\n", "\n", "            raw_weather_file = f\"raw_weather_data_{city}_from_{min(ft_dts).strftime('%Y%m%d')}_to_{max(ft_dts).strftime('%Y%m%d')}_on_{run_date.strftime('%Y%m%d')}.parquet\"\n", "            raw_file_exists = sf.check_s3_file_exists(\n", "                s3_bucket, f\"{s3_global_features}/{raw_weather_file}\"\n", "            )\n", "\n", "            print_log(\n", "                f\"Raw weather file : {s3_global_features}/{raw_weather_file} exists : \"\n", "                + str(raw_file_exists)\n", "            )\n", "\n", "            if not raw_file_exists:\n", "\n", "                for date in ft_dts:\n", "\n", "                    k = ft_dt_combos.loc[\n", "                        (ft_dt_combos.city_name == city) & (ft_dt_combos.date == date), \"k\"\n", "                    ].unique()[0]\n", "                    api_key = sf.api_dict[k]\n", "\n", "                    url = f\"https://api.openweathermap.org/data/3.0/onecall/day_summary?lat={lat}&lon={long}&date={date}&appid={api_key}&units=metric&tz=+05:30\"\n", "                    try:\n", "                        response = requests.get(url)\n", "                        api_call_counter[k] = api_call_counter[k] + 1\n", "                    except Exception as e:\n", "                        print_log(f\"Could not fetch data from weather API for {city=} and {date=}\")\n", "\n", "                    if response.status_code == 200:\n", "                        res = response.json()\n", "                        df = pd.DataFrame(res)\n", "                        df = df.reset_index()  # .drop(columns=\"wind\")\n", "                        df[\"city\"] = city\n", "                        df.fillna(0, inplace=True)\n", "                        city_ddf = pd.concat([city_ddf, df])\n", "                        del df\n", "                    else:\n", "                        print_log(\n", "                            f\"API call didn't return data of {city=} for {date=}. Moving to next dates\"\n", "                        )\n", "                    del k, api_key\n", "\n", "                if len(city_ddf) > 0:\n", "                    msg = f\"Captured {len(city_ddf)} rows of weather data for {city=}({i}/{num_cities}) between dates : {city_ddf.date.min()} and {city_ddf.date.max()}.\"\n", "                    print_log(msg)\n", "                    send_to_slack(send_log(msg))\n", "\n", "                    city_ddf[\"wind\"] = city_ddf[\"wind\"].apply(lambda x: get_value(x, \"speed\"))\n", "                    upload_parquet_to_s3(\n", "                        df=city_ddf,\n", "                        path=f\"s3://{s3_bucket}/{s3_global_features}/{raw_weather_file}\",\n", "                    )\n", "\n", "                    all_cities_df = pd.concat([all_cities_df, city_ddf])\n", "                    del city_ddf\n", "\n", "            else:\n", "                msg = f\"Raw weather data of {city=} already present on S3 for the current run, skipping the API data pull and reading data from S3\"\n", "                print_log(msg)\n", "                send_to_slack(send_log(msg))\n", "                city_ddf = read_parquet_from_s3(\n", "                    f\"s3://{s3_bucket}/{s3_global_features}/{raw_weather_file}\"\n", "                )\n", "\n", "                msg = f\"Read {len(city_ddf)} rows of weather data for {city=} between dates : {city_ddf.date.min()} and {city_ddf.date.max()}.\"\n", "                print_log(msg)\n", "                send_to_slack(send_log(msg))\n", "                all_cities_df = pd.concat([all_cities_df, city_ddf])\n", "                del city_ddf\n", "\n", "            del lat, long, ft_dts"]}, {"cell_type": "markdown", "id": "8ba6bb09-0955-4807-9bba-4f15672d4bfe", "metadata": {"tags": []}, "source": ["### Updating the API counters"]}, {"cell_type": "code", "execution_count": null, "id": "31baf04f-5eba-4bdd-9a85-0944a58cd935", "metadata": {}, "outputs": [], "source": ["if sum(api_counter_remaining.values()) > 0 and len(ft_dt_combos) > 0:\n", "    # if sum(api_counter_remaining.values()) >=0 and len(ft_dt_combos) >0:\n", "    msg = f\"Updating the API counters for {str(run_date)}\"\n", "    print_log(msg)\n", "    send_to_slack(send_log(msg))\n", "    for k in list(ft_dt_combos.k.unique()):\n", "        temp = pd.DataFrame(\n", "            {\n", "                \"run_date\": [run_date_utc.strftime(\"%Y-%m-%d\")],\n", "                \"api_call_counter\": [\n", "                    ft_dt_combos[ft_dt_combos.k == k]\n", "                    .groupby(\"k\", as_index=False)\n", "                    .agg({\"date\": \"count\"})[\"date\"][0]\n", "                ],\n", "            }\n", "        )\n", "\n", "        if api_counter_log_exists[k]:\n", "            api_call_counter_df[k] = pd.concat([api_call_counter_df[k], temp])\n", "            wr.s3.to_csv(\n", "                api_call_counter_df[k],\n", "                f\"s3://{s3_bucket}/{s3_global_features}/{api_counter_log[k]}\",\n", "            )\n", "        else:\n", "            wr.s3.to_csv(temp, f\"s3://{s3_bucket}/{s3_global_features}/{api_counter_log[k]}\")\n", "    print_log(\"Done\")\n", "    send_to_slack(send_log(\"Done\"))\n", "else:\n", "    msg = f\"No API calls remaining for {str(run_date)}. Exiting the weather creation\""]}, {"cell_type": "markdown", "id": "85be2e1f-9f1a-4bb2-8633-195eab76b62e", "metadata": {"tags": []}, "source": ["### Aggregating the weather features and uploading to DB"]}, {"cell_type": "code", "execution_count": null, "id": "ee7d7930-9bf9-4802-87ec-f59263f330bb", "metadata": {}, "outputs": [], "source": ["forecast_weather_file = (\n", "    f\"{_GLOBAL_FEATURES_PATH}/forecast_weather_data_{str(run_date).replace('-', '_')}.parquet\"\n", ")\n", "forecast_weather_file_exists = sf.check_s3_file_exists(\n", "    s3_bucket,\n", "    f\"{s3_global_features}/forecast_weather_data_{str(run_date).replace('-', '_')}.parquet\",\n", ")\n", "\n", "if forecast_weather_file_exists:\n", "    msg = f\"Aggregated weather data of future dates already present in S3 for {str(run_date)}. Reading and pushing to DB\"\n", "    print_log(msg)\n", "    send_to_slack(send_log(msg))\n", "\n", "    ft_df_main = read_parquet_from_s3(forecast_weather_file)\n", "\n", "    min_date = ft_df_main.date.min().replace(\"-\", \"\")\n", "    max_date = ft_df_main.date.max().replace(\"-\", \"\")\n", "\n", "    num_chunks = ft_df_main.chunk.max()\n", "    k = 0\n", "    for chunk in ft_df_main.chunk.unique():\n", "        ft_df = ft_df_main[ft_df_main.chunk == chunk]\n", "        s3_weather_path = (\n", "            f\"{_GLOBAL_FEATURES_PATH}/weather_data_forecast_{min_date}_{max_date}_{chunk=}.parquet\"\n", "        )\n", "        try:\n", "            msg = f\"{chunk=}/{num_chunks}: Pushing {len(ft_df)} rows of weather features for {ft_df.city.nunique()} cities and {ft_df.date.nunique()} days.\"\n", "            print_log(msg)\n", "            send_to_slack(send_log(msg))\n", "            pb.to_trino(\n", "                ft_df[[rec[\"name\"] for rec in schema.global_table_schema[\"column_dtypes\"]]],\n", "                **schema.global_table_schema,\n", "            )\n", "            msg = f\"Done. Saving to Weather File on S3 = {s3_weather_path}\"\n", "            print_log(msg)\n", "            send_to_slack(send_log(msg))\n", "            upload_parquet_to_s3(\n", "                df=ft_df,\n", "                path=s3_weather_path,\n", "            )\n", "            print_log(\"Done !!!\\n\\n\")\n", "            send_to_slack(send_log(f\"Done !!!\"))\n", "        except Exception as e:\n", "            error_msg = str(e).split(\"cause\")[0]\n", "            msg = (\n", "                f\"Could not upload weather features, got error = {error_msg}. Saving to S3 instead\"\n", "            )\n", "            print_log(msg)\n", "            send_to_slack(send_log(msg))\n", "            upload_parquet_to_s3(\n", "                df=ft_df,\n", "                path=s3_weather_path,\n", "            )\n", "            print_log(f\"Weather File = {s3_weather_path}\")\n", "            send_to_slack(send_log(f\"Weather File = {s3_weather_path}\"))\n", "    msg = f\"Weather Feature creation between {str(min_date)} and {str(max_date)} Done !!!\"\n", "    send_to_slack(send_log(msg))\n", "    print_log(msg)\n", "\n", "else:\n", "    if len(all_cities_df) > 0:\n", "\n", "        msg = f\"Aggregating the weather features and uploading to DB\"\n", "        print_log(msg)\n", "        send_to_slack(send_log(msg))\n", "\n", "        all_cities_df_non_temp = all_cities_df.groupby([\"city\", \"date\"], as_index=False).agg(\n", "            {\n", "                \"humidity\": \"max\",\n", "                \"precipitation\": \"sum\",\n", "                \"pressure\": \"max\",\n", "                \"wind\": \"max\",\n", "            }\n", "        )\n", "        all_cities_df_non_temp.columns = (\n", "            all_cities_df_non_temp.columns.str.replace(\"humidity_max\", \"humidity\")\n", "            .str.replace(\"precipitation_sum\", \"precipitation\")\n", "            .str.replace(\"pressure_max\", \"pressure\")\n", "            .str.replace(\"wind_max\", \"wind\")\n", "        )\n", "\n", "        all_cities_df_temp = (\n", "            all_cities_df[(all_cities_df[\"index\"] != \"total\")]\n", "            .groupby([\"city\", \"date\"], as_index=False)\n", "            .agg({\"temperature\": [\"min\", \"mean\", \"max\"]})\n", "        )\n", "        all_cities_df_temp.columns = [\n", "            \"_\".join(a) for a in all_cities_df_temp.columns.to_flat_index()\n", "        ]\n", "\n", "        all_cities_df_temp.columns = (\n", "            all_cities_df_temp.columns.str.replace(\"city_\", \"city\")\n", "            .str.replace(\"date_\", \"date\")\n", "            .str.replace(\"temperature_min\", \"temp_min\")\n", "            .str.replace(\"temperature_max\", \"temp_max\")\n", "            .str.replace(\"temperature_mean\", \"temp\")\n", "        )\n", "        all_cities_df = all_cities_df_non_temp.merge(all_cities_df_temp)\n", "\n", "        ft_df = all_cities_df.melt(\n", "            id_vars=[\"city\", \"date\"],\n", "            var_name=\"feature_name\",\n", "            value_name=\"feature_value\",\n", "        )\n", "        print_log(ft_df.shape)\n", "        ft_df = ft_df.groupby([\"city\", \"date\", \"feature_name\"], as_index=False).agg(\n", "            {\"feature_value\": \"mean\"}\n", "        )\n", "        ft_df[\"snapshot_dt_ist\"] = run_id\n", "        print_log(ft_df.shape)\n", "\n", "        print_log(ft_df.date.min())\n", "        print_log(ft_df.date.max())\n", "\n", "        min_date = ft_df.date.min().replace(\"-\", \"\")\n", "        max_date = ft_df.date.max().replace(\"-\", \"\")\n", "\n", "        x = ft_df[[\"city\", \"date\"]].drop_duplicates()\n", "        x.reset_index(drop=True, inplace=True)\n", "\n", "        x[\"partition\"] = x[\"city\"] + \"-\" + x[\"date\"].astype(str)\n", "        x[\"chunk\"] = None\n", "        idx = range(len(x))\n", "\n", "        groups = idx[:: min(290, len(x) - 1)]\n", "        for i in range(len(groups) - 1):\n", "            x.loc[groups[i] : groups[(i + 1)], \"chunk\"] = i\n", "        x.reset_index(drop=True, inplace=True)\n", "\n", "        ft_df_main = ft_df.merge(x[[\"city\", \"date\", \"chunk\"]], on=[\"city\", \"date\"])\n", "        # ft_df_main = ft_df_main[~pd.isna(ft_df_main.chunk)]\n", "        chunk_max = ft_df_main.chunk.max()\n", "        ft_df_main.loc[pd.isna(ft_df_main.chunk), \"chunk\"] = chunk_max + 1\n", "        upload_parquet_to_s3(ft_df_main, forecast_weather_file)\n", "\n", "        num_chunks = ft_df_main.chunk.max()\n", "        for chunk in ft_df_main.chunk.unique():\n", "            ft_df = ft_df_main[ft_df_main.chunk == chunk]\n", "            s3_weather_path = f\"{_GLOBAL_FEATURES_PATH}/weather_data_forecast_{min_date}_{max_date}_{chunk=}.parquet\"\n", "            try:\n", "                msg = f\"{chunk=}/{num_chunks}: Pushing {len(ft_df)} rows of weather features for {ft_df.city.nunique()} cities and {ft_df.date.nunique()} days.\"\n", "                print_log(msg)\n", "                send_to_slack(send_log(msg))\n", "                pb.to_trino(\n", "                    ft_df[[rec[\"name\"] for rec in schema.global_table_schema[\"column_dtypes\"]]],\n", "                    **schema.global_table_schema,\n", "                )\n", "                msg = f\"Done. Saving to Weather File on S3 = {s3_weather_path}\"\n", "                print_log(msg)\n", "                send_to_slack(send_log(msg))\n", "                upload_parquet_to_s3(\n", "                    df=ft_df,\n", "                    path=s3_weather_path,\n", "                )\n", "                print_log(\"Done !!!\\n\\n\")\n", "                send_to_slack(send_log(f\"Done !!!\"))\n", "            except Exception as e:\n", "                error_msg = str(e).split(\"cause\")[0]\n", "                msg = f\"Could not upload weather features, got error = {error_msg}. Saving to S3 instead\"\n", "                print_log(msg)\n", "                send_to_slack(send_log(msg))\n", "                upload_parquet_to_s3(\n", "                    df=ft_df,\n", "                    path=s3_weather_path,\n", "                )\n", "                print_log(f\"Weather File = {s3_weather_path}\")\n", "                send_to_slack(send_log(f\"Weather File = {s3_weather_path}\"))\n", "        msg = f\"Weather Feature creation between {str(min_date)} and {str(max_date)} Done !!!\"\n", "        send_to_slack(send_log(msg))\n", "        print_log(msg)\n", "    else:\n", "        msg = f\"Weather Feature creation for future dates is done !!!\\n\\n\"\n", "        send_to_slack(send_log(msg))\n", "        print_log(msg)\n", "\n", "    gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "facf7941-050b-46aa-9e1b-37cc8db76cbc", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}