city_list_query = """
SELECT 
    distinct 
    case
        when city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
        when city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa') then 'Goa'
        when city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
        when city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
        when city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
        when city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
        when city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
        when city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
        when city_name in ('SOLAN', 'Solan') then 'Solan'
        when city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
        when city_name in ('bikaner', 'Bikaner') then 'Bikaner'
        when city_name in ('Purnia', 'purnia') then 'Purnia'
        else city_name
    end as city
FROM 
    dwh.fact_sales_order_details
WHERE 
    1=1
    and order_create_dt_ist >= date '{start_date}'
    and order_create_dt_ist <= date '{end_date}'
    and is_internal_order = False
    and order_current_status = 'DELIVERED'
    and coalesce(city_name, '') not in ('None', '', 'Not in service area')
"""

ptype_city_availability_query = """
with
availability as (
select
    date(insert_ds_ist) as date,
    extract(hour from his.created_at) as hour,
    his.item_id,
    outlet_id,
    case
        when cl.name in ('Gurgaon', 'Gurugram') then 'HR-NCR'
        when cl.name in ('Ghaziabad', 'Noida') then 'UP-NCR'
        when cl.name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa') then 'Goa'
        when cl.name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
        when cl.name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
        when cl.name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
        when cl.name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
        when cl.name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
        when cl.name in ('SOLAN', 'Solan') then 'Solan'
        when cl.name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
        when cl.name in ('bikaner', 'Bikaner') then 'Bikaner'
        when cl.name in ('Purnia', 'purnia') then 'Purnia'
        else cl.name
    end as city,
    case when actual_quantity-blocked_quantity > 0 then 1 else 0 end as available_flag,
    1 as total_flag,
    max(replace(dp.product_type, '''', '')) as product_type_dwh,
    max(replace(id.p_type, '''', '')) as product_type_rpc
    
from 
    ims.ims_item_inventory_log his
join 
    retail.console_outlet co 
on 
    co.id = his.outlet_id 
    and co.business_type_id = 7 
    and co.active=1 
    and co.lake_active_record
left join 
    retail.console_location cl 
on 
    cl.id = co.tax_location_id 
    and cl.lake_active_record
join 
    dwh.dim_item_product_offer_mapping ipom 
on 
    ipom.item_id = his.item_id 
    and 
    ipom.is_current = true
left join
    supply_etls.item_details id
on
    ipom.item_id = id.item_id
join 
    dwh.dim_product dp 
on 
    dp.product_id = ipom.product_id 
    and 
    date(his.insert_ds_ist) between date(dp.valid_from_ts_ist_enriched) and date(dp.valid_to_ts_ist_enriched)

where
    1 = 1 
    and insert_ds_ist >= '{start_date}'
    and insert_ds_ist <= '{end_date}'
    and insert_ds_ist in ({specific_dates})
    and cl.name is not null
    and cl.name <> 'Not in service area'
    and (case
            when cl.name in ('Gurgaon', 'Gurugram') then 'HR-NCR'
            when cl.name in ('Ghaziabad', 'Noida') then 'UP-NCR'
            when cl.name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa') then 'Goa'
            when cl.name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
            when cl.name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
            when cl.name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
            when cl.name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
            when cl.name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
            when cl.name in ('SOLAN', 'Solan') then 'Solan'
            when cl.name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
            when cl.name in ('bikaner', 'Bikaner') then 'Bikaner'
            when cl.name in ('Purnia', 'purnia') then 'Purnia'
            else cl.name
        end) in {city_list}
group by 1,2,3,4,5,6,7
)
select
    cast(date as varchar) as date,
    case 
        when lower(product_type_dwh) like '%%combo%%' or lower(product_type_dwh) like '%%special%%' or lower(product_type_dwh) like '%%gift%%' then product_type_rpc
        else product_type_dwh
    end as product_type,
    city,
    max(CAST(current_timestamp AS timestamp(6))) as snapshot_dt_ist,
    sum(available_flag) * 1.000/sum(total_flag) as ptype_avg_availability
from 
    availability
where
    1=1
    and date is not null
    and city is not null
    and (product_type_dwh is not null or product_type_rpc is not null)
group by 1,2,3
"""

ptype_city_sales_query = """
with base as
(
select
    cast(order_create_dt_ist as varchar) as date,
    
    case
        when city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
        when city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa') then 'Goa'
        when city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
        when city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
        when city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
        when city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
        when city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
        when city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
        when city_name in ('SOLAN', 'Solan') then 'Solan'
        when city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
        when city_name in ('bikaner', 'Bikaner') then 'Bikaner'
        when city_name in ('Purnia', 'purnia') then 'Purnia'
        else city_name
    end as city,

    fsoid.product_id,
    ipom.item_id,
    fsoid.cart_id,
    fsoid.dim_customer_key,
    
    max(replace(dp.product_type, '''', '')) as product_type_dwh,
    max(replace(id.p_type, '''', '')) as product_type_rpc,
    
    sum(coalesce(fsoid.product_quantity*ipom.multiplier, 0)) as quantity_sold,
    sum(coalesce(fsoid.unit_mrp*ipom.avg_mrp_ratio*fsoid.product_quantity*ipom.multiplier,0)) as total_mrp,
    sum(coalesce(fsoid.unit_selling_price*ipom.avg_selling_price_ratio*fsoid.product_quantity*ipom.multiplier,0)) as sales_value
        
from 
    dwh.fact_sales_order_item_details fsoid
left join
    dwh.dim_item_product_offer_mapping ipom
on
    fsoid.product_id = ipom.product_id and ipom.is_current = True
left join
    supply_etls.item_details id
on
    ipom.item_id = id.item_id
join
    dwh.dim_product dp 
on 
    dp.product_id = fsoid.product_id 
    and 
    DATE_TRUNC('SECOND', fsoid.order_create_ts_ist) >= DATE_TRUNC('SECOND', dp.valid_from_ts_ist_enriched)
    and 
    DATE_TRUNC('SECOND', fsoid.order_create_ts_ist) < DATE_TRUNC('SECOND', dp.valid_to_ts_ist_enriched)

where
    1=1
    and order_create_dt_ist >= date '{start_date}'
    and order_create_dt_ist <= date '{end_date}'
    and order_create_dt_ist in ({specific_dates})
    and fsoid.is_internal_order = false
    and fsoid.order_current_status = 'DELIVERED'
    and city_name is not null
    and city_name not in ('Not in service area')
    and (case
            when city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
            when city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa') then 'Goa'
            when city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
            when city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
            when city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
            when city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
            when city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
            when city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
            when city_name in ('SOLAN', 'Solan') then 'Solan'
            when city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
            when city_name in ('bikaner', 'Bikaner') then 'Bikaner'
            when city_name in ('Purnia', 'purnia') then 'Purnia'
            else city_name
        end) in {city_list}
group by 1, 2, 3, 4, 5, 6
),

sale_agg as
(
select
    a.city,
    a.date,
    case 
        when lower(a.product_type_dwh) like '%%combo%%' or lower(a.product_type_dwh) like '%%special%%' or lower(a.product_type_dwh) like '%%gift%%' then a.product_type_rpc
        else product_type_dwh
    end as product_type,
    
    coalesce(sum(a.quantity_sold), 0) as quantity_sold,
    coalesce(sum(a.total_mrp),0) as total_mrp,
    coalesce(sum(a.sales_value),0) as sales_value,

    cast(coalesce((sum(1.0000*total_mrp) - sum(1.0000*sales_value))/sum(1.0000*total_mrp),0.0000) as double) as retail_discount_per_unit,
    cast(coalesce(1.0000*sum(sales_value)/sum(quantity_sold), 0.00) as double) as retail_price_per_unit,
    
    cast(coalesce(count(distinct a.cart_id),0) as bigint) as ptype_carts,
    cast(coalesce(count(distinct a.dim_customer_key),0) as bigint) as ptype_customers,
    
    CAST(max(current_timestamp) AS timestamp(6)) as snapshot_dt_ist

from
    base a
where
    1=1
group by 1, 2, 3
)

select 
    date, city, product_type,
    quantity_sold, total_mrp, sales_value, 
    ptype_carts, ptype_customers, 
    retail_discount_per_unit, retail_price_per_unit, snapshot_dt_ist 
from
    sale_agg
where 
    1=1
"""

ptype_city_viewers_query = """
select 
    at_date_ist as date,
    case
        when traits__city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
        when traits__city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa') then 'Goa'
        when traits__city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
        when traits__city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
        when traits__city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
        when traits__city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
        when traits__city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
        when traits__city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
        when traits__city_name in ('SOLAN', 'Solan') then 'Solan'
        when traits__city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
        when traits__city_name in ('bikaner', 'Bikaner') then 'Bikaner'
        when traits__city_name in ('Purnia', 'purnia') then 'Purnia'
        else traits__city_name
    end as city,

    case 
        when lower(replace(dp.product_type, '''', '')) like '%%combo%%' 
                or lower(replace(dp.product_type, '''', '')) like '%%special%%' 
                or lower(replace(dp.product_type, '''', '')) like '%%gift%%' 
            then replace(id.p_type, '''', '')
        else replace(dp.product_type, '''', '')
    end as product_type

    -- replace(dp.product_type, '''', '') as product_type

    ,coalesce(count(distinct cast(traits__merchant_id as varchar) || coalesce(properties__product_id,properties__widget_id,properties__child_widget_id)),0) as ptype_width

    ,coalesce(count(distinct cast(traits__merchant_id as varchar) || coalesce(properties__product_id,properties__widget_id,properties__child_widget_id)) filter (where properties__inventory > 0),0) as instock_ptype_width

    ,coalesce(count(distinct coalesce(properties__product_id,properties__widget_id,properties__child_widget_id)),0) as ptype_num_pids    

    ,coalesce(count(distinct coalesce(properties__product_id,properties__widget_id,properties__child_widget_id)) filter (where properties__inventory > 0),0) as instock_ptype_num_pids

    ,coalesce(count(distinct lower(coalesce(coalesce(device_uuid, advertising_id), traits__user_id))),0) as viewers_ptype

    ,coalesce(count(distinct case 
                        when (properties__page_name like '%%Search%%' or properties__page_name like '%%search%%')  then lower(coalesce(coalesce(device_uuid, advertising_id), traits__user_id)) 
                        else null end
    ),0) as search_viewers_ptype

    ,coalesce(count(distinct case 
                        when (properties__page_name not like '%%Search%%' and properties__page_name not like '%%search%%')  then lower(coalesce(coalesce(device_uuid, advertising_id), traits__user_id)) 
                        else null end
    ),0) as non_search_viewers_ptype

    ,coalesce(count(distinct case 
                        when mi.properties__inventory > 0 then lower(coalesce(coalesce(device_uuid, advertising_id), traits__user_id)) 
                        else null end
    ),0) as instock_viewers_ptype

    ,coalesce(count(distinct case 
                        when mi.properties__inventory > 0 and (properties__page_name like '%%Search%%' or properties__page_name like '%%search%%')  then lower(coalesce(coalesce(device_uuid, advertising_id), traits__user_id)) 
                        else null end
    ),0) as instock_search_viewers_ptype

    ,coalesce(count(distinct case 
                        when mi.properties__inventory > 0 and (properties__page_name not like '%%Search%%' and properties__page_name not like '%%search%%')  then lower(coalesce(coalesce(device_uuid, advertising_id), traits__user_id)) 
                        else null end
    ),0) as instock_non_search_viewers_ptype
    
    ,CAST(current_timestamp AS timestamp(6)) as snapshot_dt_ist

from 
    lake_events.mobile_impression_data mi
left join
    dwh.dim_item_product_offer_mapping ipom
on
    coalesce(properties__product_id,properties__widget_id,properties__child_widget_id) = cast(ipom.product_id as varchar) and ipom.is_current = True
left join
    supply_etls.item_details id
on
    ipom.item_id = id.item_id
left join 
    dwh.dim_product dp
on
    coalesce(properties__product_id,properties__widget_id,properties__child_widget_id) = cast(dp.product_id as varchar)
    and
    at_date_ist between date(dp.valid_from_ts_ist_enriched) and date(dp.valid_to_ts_ist_enriched)

where 
    name = 'Product Shown'
    and at_date_ist >= date '{start_date}'
    and at_date_ist <= date '{end_date}'
    and at_date_ist in ({specific_dates})
    and (
        REGEXP_LIKE(properties__page_name, '(?i)List|Home|Search|Cart|Collection|Landing|tab')
        )
    and traits__merchant_id is not NULL
    and platform IN ('android', 'ios')
    and traits__user_id not in ('-1','0')
    and traits__city_name not in ('Not in service area')
    and traits__city_name is not null
    and (case
            when traits__city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
            when traits__city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa') then 'Goa'
            when traits__city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
            when traits__city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
            when traits__city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
            when traits__city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
            when traits__city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
            when traits__city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
            when traits__city_name in ('SOLAN', 'Solan') then 'Solan'
            when traits__city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
            when traits__city_name in ('bikaner', 'Bikaner') then 'Bikaner'
            when traits__city_name in ('Purnia', 'purnia') then 'Purnia'
            else traits__city_name
        end) in {city_list}
    -- and product_type in ('Atta')--, 'Maida', 'Mustard Oil', 'Mango Drink')
    -- and traits__city_name = 'Faridabad'
    -- and coalesce(product_type, '') not in ('', 'Free Gift', 'Combo')
group by 1, 2, 3
"""

ptype_city_atcs_query = """
select 
    at_date_ist as date,
    case
        when traits__city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
        when traits__city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa') then 'Goa'
        when traits__city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
        when traits__city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
        when traits__city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
        when traits__city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
        when traits__city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
        when traits__city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
        when traits__city_name in ('SOLAN', 'Solan') then 'Solan'
        when traits__city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
        when traits__city_name in ('bikaner', 'Bikaner') then 'Bikaner'
        when traits__city_name in ('Purnia', 'purnia') then 'Purnia'
        else traits__city_name
    end as city,

    case 
        when lower(replace(dp.product_type, '''', '')) like '%%combo%%' 
                or lower(replace(dp.product_type, '''', '')) like '%%special%%' 
                or lower(replace(dp.product_type, '''', '')) like '%%gift%%' 
            then replace(id.p_type, '''', '')
        else replace(dp.product_type, '''', '')
    end as product_type

    --replace(dp.product_type, '''', '') as product_type

    ,coalesce(count(distinct lower(coalesce(coalesce(device_uuid, advertising_id), traits__user_id))),0) as total_atcs_ptype

    ,coalesce(count(distinct case 
                        when properties__page_name like '%%Search%%' or properties__page_name like '%%search%%' then lower(coalesce(coalesce(device_uuid, advertising_id), traits__user_id)) 
                        else null end
    ),0) as search_atcs_ptype
    
    ,CAST(current_timestamp AS timestamp(6)) as snapshot_dt_ist

from 
    lake_events.mobile_event_data mi
left join
    dwh.dim_item_product_offer_mapping ipom
on
    coalesce(properties__product_id,properties__widget_id,properties__child_widget_id) = cast(ipom.product_id as varchar) and ipom.is_current = True
left join
    supply_etls.item_details id
on
    ipom.item_id = id.item_id
left join 
    dwh.dim_product dp
on
    coalesce(properties__product_id,properties__widget_id,properties__child_widget_id) = cast(dp.product_id as varchar)
    and
    at_date_ist between dp.valid_from_ts_ist_enriched and dp.valid_to_ts_ist_enriched

where 
    name = 'Product Added'
    and at_date_ist >= date '{start_date}'
    and at_date_ist <= date '{end_date}'
    and at_date_ist in ({specific_dates})
    and (
        REGEXP_LIKE(properties__page_name, '(?i)List|Home|Search|Cart|Collection|Landing|tab')
        )
    and platform IN ('android', 'ios')
    and traits__merchant_id is not NULL
    and traits__user_id not in ('-1','0')
    and traits__city_name not in ('Not in service area')
    and traits__city_name is not null
    and (case
            when traits__city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
            when traits__city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa') then 'Goa'
            when traits__city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
            when traits__city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
            when traits__city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
            when traits__city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
            when traits__city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
            when traits__city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
            when traits__city_name in ('SOLAN', 'Solan') then 'Solan'
            when traits__city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
            when traits__city_name in ('bikaner', 'Bikaner') then 'Bikaner'
            when traits__city_name in ('Purnia', 'purnia') then 'Purnia'
            else traits__city_name
        end) in {city_list}
    --and product_type in ('Atta')--, 'Maida', 'Mustard Oil', 'Mango Drink')
    --and traits__city_name = 'Faridabad'
    -- and coalesce(product_type, '') not in ('', 'Free Gift', 'Combo')
group by 1, 2, 3

"""

ptype_city_funnel_query = """
select 
    cast(a.date as varchar) as date,
    a.city,
    a.product_type
    
    ,cast(coalesce(a.ptype_width, 0) as bigint) as ptype_width
    ,cast(coalesce(a.instock_ptype_width, 0) as bigint) as instock_ptype_width

    ,cast(coalesce(a.ptype_num_pids, 0) as bigint) as ptype_num_pids
    ,cast(coalesce(a.instock_ptype_num_pids, 0) as bigint) as instock_ptype_num_pids

    ,cast(coalesce(a.viewers_ptype,0) as bigint) as viewers_ptype
    ,cast(coalesce(a.instock_viewers_ptype,0) as bigint) as instock_viewers_ptype
    
    ,cast(coalesce(a.search_viewers_ptype,0) as bigint) as search_viewers_ptype
    ,cast(coalesce(a.instock_search_viewers_ptype,0) as bigint) as instock_search_viewers_ptype
    
    ,cast(coalesce(a.non_search_viewers_ptype,0) as bigint) as non_search_viewers_ptype
    ,cast(coalesce(a.instock_non_search_viewers_ptype, 0) as bigint) as instock_non_search_viewers_ptype

    ,cast(coalesce(b.total_atcs_ptype, 0) as bigint) total_atcs_ptype
    ,cast(coalesce(b.search_atcs_ptype, 0) as bigint) search_atcs_ptype
    
    ,CAST(current_timestamp AS timestamp(6)) as snapshot_dt_ist

from
    interim.ptype_city_viewers a
left join
    interim.ptype_city_atcs b
on
    a.date = b.date
    and a.city = b.city
    and a.product_type = b.product_type
where
    1=1
    and a.date >= date('{start_date}')
    and a.date <= date('{end_date}')
    and a.date in ({specific_dates})
    and a.city in {city_list}

"""

# ptype_city_funnel_query = """
# with 
# ptype_viewers as (
#     select 
#         at_date_ist as date,
#         case
#             when traits__city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
#             when traits__city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa') then 'Goa'
#             when traits__city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
#             when traits__city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
#             when traits__city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
#             when traits__city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
#             when traits__city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
#             else traits__city_name
#         end as city,

#         case 
#             when lower(replace(dp.product_type, '''', '')) like '%%combo%%' 
#                     or lower(replace(dp.product_type, '''', '')) like '%%special%%' 
#                     or lower(replace(dp.product_type, '''', '')) like '%%gift%%' 
#                 then replace(id.p_type, '''', '')
#             else replace(dp.product_type, '''', '')
#         end as product_type

#         -- replace(dp.product_type, '''', '') as product_type

#         ,coalesce(count(distinct cast(traits__merchant_id as varchar) || coalesce(properties__product_id,properties__widget_id,properties__child_widget_id)),0) as ptype_width

#         ,coalesce(count(distinct cast(traits__merchant_id as varchar) || coalesce(properties__product_id,properties__widget_id,properties__child_widget_id)) filter (where properties__inventory > 0),0) as instock_ptype_width

#         ,coalesce(count(distinct coalesce(properties__product_id,properties__widget_id,properties__child_widget_id)),0) as ptype_num_pids    

#         ,coalesce(count(distinct coalesce(properties__product_id,properties__widget_id,properties__child_widget_id)) filter (where properties__inventory > 0),0) as instock_ptype_num_pids

#         ,coalesce(count(distinct lower(coalesce(coalesce(device_uuid, advertising_id), traits__user_id))),0) as viewers_ptype

#         ,coalesce(count(distinct case 
#                             when (properties__page_name like '%%Search%%' or properties__page_name like '%%search%%')  then lower(coalesce(coalesce(device_uuid, advertising_id), traits__user_id)) 
#                             else null end
#         ),0) as search_viewers_ptype
        
#         ,coalesce(count(distinct case 
#                             when (properties__page_name not like '%%Search%%' and properties__page_name not like '%%search%%')  then lower(coalesce(coalesce(device_uuid, advertising_id), traits__user_id)) 
#                             else null end
#         ),0) as non_search_viewers_ptype
        
#         ,coalesce(count(distinct case 
#                             when mi.properties__inventory > 0 then lower(coalesce(coalesce(device_uuid, advertising_id), traits__user_id)) 
#                             else null end
#         ),0) as instock_viewers_ptype

#         ,coalesce(count(distinct case 
#                             when mi.properties__inventory > 0 and (properties__page_name like '%%Search%%' or properties__page_name like '%%search%%')  then lower(coalesce(coalesce(device_uuid, advertising_id), traits__user_id)) 
#                             else null end
#         ),0) as instock_search_viewers_ptype
        
#         ,coalesce(count(distinct case 
#                             when mi.properties__inventory > 0 and (properties__page_name not like '%%Search%%' and properties__page_name not like '%%search%%')  then lower(coalesce(coalesce(device_uuid, advertising_id), traits__user_id)) 
#                             else null end
#         ),0) as instock_non_search_viewers_ptype

#     from 
#         lake_events.mobile_impression_data mi
#     left join
#         dwh.dim_item_product_offer_mapping ipom
#     on
#         coalesce(properties__product_id,properties__widget_id,properties__child_widget_id) = cast(ipom.product_id as varchar) and ipom.is_current = True
#     left join
#         supply_etls.item_details id
#     on
#         ipom.item_id = id.item_id
#     left join 
#         dwh.dim_product dp
#     on
#         coalesce(properties__product_id,properties__widget_id,properties__child_widget_id) = cast(dp.product_id as varchar)
#         and
#         at_date_ist between date(dp.valid_from_ts_ist_enriched) and date(dp.valid_to_ts_ist_enriched)

#     where 
#         name = 'Product Shown'
#         and at_date_ist >= date '{start_date}'
#         and at_date_ist <= date '{end_date}'
#         and at_date_ist in ({specific_dates})
#         and (
#             REGEXP_LIKE(properties__page_name, '(?i)List|Home|Search|Cart|Collection|Landing|tab')
#             )
#         and traits__merchant_id is not NULL
#         and platform IN ('android', 'ios')
#         and traits__user_id not in ('-1','0')
#         and traits__city_name not in ('Not in service area')
#         and traits__city_name is not null
#         and (case
#                 when traits__city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
#                 when traits__city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa') then 'Goa'
#                 when traits__city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
#                 when traits__city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
#                 when traits__city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
#                 when traits__city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
#                 when traits__city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
#                 else traits__city_name
#             end) in {city_list}
#         -- and product_type in ('Atta')--, 'Maida', 'Mustard Oil', 'Mango Drink')
#         -- and traits__city_name = 'Faridabad'
#         -- and coalesce(product_type, '') not in ('', 'Free Gift', 'Combo')
#     group by 1, 2, 3
# ),

# ptype_atcs as
# (
#     select 
#         at_date_ist as date,
#         case
#             when traits__city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
#             when traits__city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa') then 'Goa'
#             when traits__city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
#             when traits__city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
#             when traits__city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
#             when traits__city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
#             when traits__city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
#             else traits__city_name
#         end as city,

#         case 
#             when lower(replace(dp.product_type, '''', '')) like '%%combo%%' 
#                     or lower(replace(dp.product_type, '''', '')) like '%%special%%' 
#                     or lower(replace(dp.product_type, '''', '')) like '%%gift%%' 
#                 then replace(id.p_type, '''', '')
#             else replace(dp.product_type, '''', '')
#         end as product_type

#         --replace(dp.product_type, '''', '') as product_type
        
#         ,coalesce(count(distinct lower(coalesce(coalesce(device_uuid, advertising_id), traits__user_id))),0) as total_atcs_ptype

#         ,coalesce(count(distinct case 
#                             when properties__page_name like '%%Search%%' or properties__page_name like '%%search%%' then lower(coalesce(coalesce(device_uuid, advertising_id), traits__user_id)) 
#                             else null end
#         ),0) as search_atcs_ptype
    
#     from 
#         lake_events.mobile_event_data mi
#     left join
#         dwh.dim_item_product_offer_mapping ipom
#     on
#         coalesce(properties__product_id,properties__widget_id,properties__child_widget_id) = cast(ipom.product_id as varchar) and ipom.is_current = True
#     left join
#         supply_etls.item_details id
#     on
#         ipom.item_id = id.item_id
#     left join 
#         dwh.dim_product dp
#     on
#         coalesce(properties__product_id,properties__widget_id,properties__child_widget_id) = cast(dp.product_id as varchar)
#         and
#         at_date_ist between dp.valid_from_ts_ist_enriched and dp.valid_to_ts_ist_enriched

#     where 
#         name = 'Product Added'
#         and at_date_ist >= date '{start_date}'
#         and at_date_ist <= date '{end_date}'
#         and at_date_ist in ({specific_dates})
#         and (
#             REGEXP_LIKE(properties__page_name, '(?i)List|Home|Search|Cart|Collection|Landing|tab')
#             )
#         and platform IN ('android', 'ios')
#         and traits__merchant_id is not NULL
#         and traits__user_id not in ('-1','0')
#         and traits__city_name not in ('Not in service area')
#         and traits__city_name is not null
#         and (case
#                 when traits__city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
#                 when traits__city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa') then 'Goa'
#                 when traits__city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
#                 when traits__city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
#                 when traits__city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
#                 when traits__city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
#                 when traits__city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
#                 else traits__city_name
#             end) in {city_list}
#         --and product_type in ('Atta')--, 'Maida', 'Mustard Oil', 'Mango Drink')
#         --and traits__city_name = 'Faridabad'
#         -- and coalesce(product_type, '') not in ('', 'Free Gift', 'Combo')
#     group by 1, 2, 3
# ),

# final as
# (
# select 
#     cast(a.date as varchar) as date,
#     a.city,
#     a.product_type
    
#     ,cast(coalesce(a.ptype_width, 0) as bigint) as ptype_width
#     ,cast(coalesce(a.instock_ptype_width, 0) as bigint) as instock_ptype_width

#     ,cast(coalesce(a.ptype_num_pids, 0) as bigint) as ptype_num_pids
#     ,cast(coalesce(a.instock_ptype_num_pids, 0) as bigint) as instock_ptype_num_pids

#     ,cast(coalesce(a.viewers_ptype,0) as bigint) as viewers_ptype
#     ,cast(coalesce(a.instock_viewers_ptype,0) as bigint) as instock_viewers_ptype
    
#     ,cast(coalesce(a.search_viewers_ptype,0) as bigint) as search_viewers_ptype
#     ,cast(coalesce(a.instock_search_viewers_ptype,0) as bigint) as instock_search_viewers_ptype
    
#     ,cast(coalesce(a.non_search_viewers_ptype,0) as bigint) as non_search_viewers_ptype
#     ,cast(coalesce(a.instock_non_search_viewers_ptype, 0) as bigint) as instock_non_search_viewers_ptype

#     ,cast(coalesce(b.total_atcs_ptype, 0) as bigint) total_atcs_ptype
#     ,cast(coalesce(b.search_atcs_ptype, 0) as bigint) search_atcs_ptype
    
#     ,CAST(current_timestamp AS timestamp(6)) as snapshot_dt_ist

# from
#     ptype_viewers a
# left join
#     ptype_atcs b
# on
#     1 = 1
#     and a.date = b.date
#     and a.city = b.city
#     and a.product_type = b.product_type
#     --and a.is_done is null
#     --and b.is_done is null
# )
# select * from final

# """

keyword_search_devices_query = """
select 
    e.at_date_ist,
    case
        when e.traits__city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
        when e.traits__city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa') then 'Goa'
        when e.traits__city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
        when e.traits__city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
        when e.traits__city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
        when e.traits__city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
        when e.traits__city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
        when e.traits__city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
        when e.traits__city_name in ('SOLAN', 'Solan') then 'Solan'
        when e.traits__city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
        when e.traits__city_name in ('bikaner', 'Bikaner') then 'Bikaner'
        when e.traits__city_name in ('Purnia', 'purnia') then 'Purnia'
        else e.traits__city_name
    end as city,
    replace(trim(lower(e.properties__search_actual_keyword)), '''', '') as keyword,
    lower(coalesce(coalesce(e.device_uuid, e.advertising_id), e.traits__user_id)) as device_uuid,
    CAST(current_timestamp AS timestamp(6)) as snapshot_dt_ist
from 
    lake_events.mobile_event_data e
where
    1 = 1
    and (name like '%%Search%%' or name like '%%search%%')
    and at_date_ist >= date '{start_date}'
    and at_date_ist <= date '{end_date}'
    AND (properties__page_name like '%%Search%%' or properties__page_name like '%%search%%' or name in ('Search Results Viewed','Product Searched'))
    -- and traits__user_id not in ('14647274', '9961423','9709403','13957980','13605597','3927621','14740725','4144617','10045662')
    -- and traits__city_id is not NULL
    and traits__city_name is not null
    and traits__city_name <> 'Not in service area'
    and (case
            when e.traits__city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
            when e.traits__city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa') then 'Goa'
            when e.traits__city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
            when e.traits__city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
            when e.traits__city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
            when e.traits__city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
            when e.traits__city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
            when e.traits__city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
            when e.traits__city_name in ('SOLAN', 'Solan') then 'Solan'
            when e.traits__city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
            when e.traits__city_name in ('bikaner', 'Bikaner') then 'Bikaner'
            when e.traits__city_name in ('Purnia', 'purnia') then 'Purnia'
            else e.traits__city_name
        end) in {city_list}
    and platform IN ('android', 'ios')
    --and traits__merchant_id is not NULL
    --and traits__merchant_name is not NULL
    and lower(properties__search_actual_keyword) not in ('',' ','#-na', '-na-', '-', 'na')
    and length(trim(lower(e.properties__search_actual_keyword))) > 2
    --and traits__city_name in ('Faridabad')
group by 1,2,3,4

"""

keyword_ptype_atc_devices_query = """
select 
    e.at_date_ist,
    case
        when e.traits__city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
        when e.traits__city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa') then 'Goa'
        when e.traits__city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
        when e.traits__city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
        when e.traits__city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
        when e.traits__city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
        when e.traits__city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
        when e.traits__city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
        when e.traits__city_name in ('SOLAN', 'Solan') then 'Solan'
        when e.traits__city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
        when e.traits__city_name in ('bikaner', 'Bikaner') then 'Bikaner'
        when e.traits__city_name in ('Purnia', 'purnia') then 'Purnia'
        else e.traits__city_name
    end as city,
    replace(trim(lower(e.properties__search_actual_keyword)), '''', '') as keyword,
    case 
        when lower(replace(dp.product_type, '''', '')) like '%%combo%%' 
                or lower(replace(dp.product_type, '''', '')) like '%%special%%' 
                or lower(replace(dp.product_type, '''', '')) like '%%gift%%' 
            then replace(id.p_type, '''', '')
        else replace(dp.product_type, '''', '')
    end as product_type,

    lower(coalesce(coalesce(e.device_uuid, e.advertising_id), e.traits__user_id)) as device_uuid,
    CAST(current_timestamp AS timestamp(6)) as snapshot_dt_ist
from
    lake_events.mobile_event_data e
left join
    dwh.dim_product dp
on
    coalesce(coalesce(properties__product_id,properties__widget_id),properties__child_widget_id) = cast(dp.product_id as varchar)
    and
    at_date_ist between date(dp.valid_from_ts_ist_enriched) and date(dp.valid_to_ts_ist_enriched)
left join
    dwh.dim_item_product_offer_mapping ipom
on
    coalesce(properties__product_id,properties__widget_id,properties__child_widget_id) = cast(ipom.product_id as varchar) and ipom.is_current = True
left join
    supply_etls.item_details id
on
    ipom.item_id = id.item_id

where
    1 = 1
    and e.name = 'Product Added'
    and at_date_ist >= date('{start_date}') - interval '10' day 
    and at_date_ist <= date('{end_date}')
    AND (properties__page_name like '%%Search%%' or properties__page_name like '%%search%%')
    AND platform IN ('android', 'ios')
    AND properties__search_actual_keyword IS NOT NULL
    -- and traits__user_id not in ('14647274', '9961423','9709403','13957980','13605597','3927621','14740725','4144617','10045662')
    -- and traits__city_id is not NULL
    and traits__city_name is not NULL
    and (case
            when e.traits__city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
            when e.traits__city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa') then 'Goa'
            when e.traits__city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
            when e.traits__city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
            when e.traits__city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
            when e.traits__city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
            when e.traits__city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
            when e.traits__city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
            when e.traits__city_name in ('SOLAN', 'Solan') then 'Solan'
            when e.traits__city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
            when e.traits__city_name in ('bikaner', 'Bikaner') then 'Bikaner'
            when e.traits__city_name in ('Purnia', 'purnia') then 'Purnia'
            else e.traits__city_name
        end) in {city_list}
    and traits__city_name <> 'Not in service area'
    -- and traits__merchant_id is not NULL
    -- and traits__merchant_name is not NULL
    and lower(properties__search_actual_keyword) not in ('',' ','#-na', '-na-', '-', 'na')
    and length(trim(lower(e.properties__search_actual_keyword))) between 3 and 50
    -- and traits__city_name in ('Faridabad')
group by 1, 2, 3, 4, 5
"""

ptype_city_searchers_query = """
with
    keyword_searches as
    (
        select
            a.at_date_ist,
            a.city,
            a.keyword,
            coalesce(count(distinct a.device_uuid),0) as keyword_searchers
        from 
            interim.keyword_search_devices a
        where
            1 = 1
            and a.at_date_ist >= date('{start_date}')
            and a.at_date_ist <= date('{end_date}')
            and a.city in {city_list}
        group by 1,2,3
    ),

    keyword_total_atcs as
    (
        select
            a.city,
            a.keyword,
            a.at_date_ist,
            coalesce(count(distinct b.device_uuid),0) as keyword_total_atcs
        from
            keyword_searches a
        join
            interim.keyword_ptype_atc_devices b
        on
            a.city = b.city
            and a.keyword = b.keyword
            and a.at_date_ist >= b.at_date_ist
            and a.at_date_ist <= b.at_date_ist + interval '10' day
        where
            1 = 1
            and a.at_date_ist >= date('{start_date}')
            and a.at_date_ist <= date('{end_date}')
            and a.city in {city_list}
            --and coalesce(a.is_done, False) = False
        group by 1,2,3
        having count(distinct b.device_uuid) >= 10
    ),

    keyword_ptype_atcs as 
    (
        select
            a.city,
            a.keyword,
            a.at_date_ist,
            b.product_type,
            coalesce(count(distinct b.device_uuid),0) as keyword_ptype_atcs
        from
            keyword_searches a
        join
            interim.keyword_ptype_atc_devices b
        on
            a.city = b.city
            and a.keyword = b.keyword
            and a.at_date_ist >= b.at_date_ist
            and a.at_date_ist <= b.at_date_ist + interval '10' day
        where
            1 = 1
            and a.at_date_ist >= date('{start_date}')
            and a.at_date_ist <= date('{end_date}')
            and a.city in {city_list}
            --and coalesce(a.is_done, False) = False
        group by 1,2,3,4
        having count(distinct b.device_uuid) >= 10
    ),

    final_base as
    (
        select
            x.at_date_ist,
            x.city,
            x.keyword,
            x.keyword_searchers,
            y.keyword_total_atcs,
            z.product_type,
            z.keyword_ptype_atcs,
            coalesce(1.0000*z.keyword_ptype_atcs/y.keyword_total_atcs, 0) as ptype_atc_contrib,
            coalesce(case 
                when trim(lower(z.product_type)) = x.keyword then x.keyword_searchers
                else coalesce(1.0000*z.keyword_ptype_atcs/y.keyword_total_atcs, 0) * x.keyword_searchers 
            end,0) as ptype_searchers

        from
            keyword_searches x
        join
            keyword_total_atcs y
        on
            x.keyword = y.keyword
            and x.city = y.city
            and x.at_date_ist = y.at_date_ist
        join
            keyword_ptype_atcs z
        on
            x.keyword = z.keyword
            and x.city = z.city
            and x.at_date_ist = z.at_date_ist
    )
    select
        cast(at_date_ist as varchar) as date,
        city,
        product_type,
        cast(ceil(sum(coalesce(ptype_searchers,0))) as bigint) as ptype_searchers,
        cast(coalesce(count(distinct keyword),0) as bigint) as num_keywords,
        max(CAST(current_timestamp AS timestamp(6))) as snapshot_dt_ist
    from
        final_base
    where 
        1=1
        and city is not null 
        and at_date_ist is not null 
        and product_type is not null
    group by 1, 2, 3
"""

item_min_order_date_query = """
with 
carts as
(
select cart_id from dwh.fact_sales_order_item_details od
where
    od.order_create_dt_ist <= date('{yesterday}')
    and
    od.order_current_status = 'DELIVERED'
    AND
    od.is_internal_order = False
    group by 1
),
final as
(select 
    case
        when inv.frontend_merchant_city in ('Gurgaon','Gurugram') then 'HR-NCR'
        when inv.frontend_merchant_city in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa') then 'Goa'
        when inv.frontend_merchant_city in ('Noida', 'Ghaziabad') then 'UP-NCR'
        when inv.frontend_merchant_city in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
        when inv.frontend_merchant_city in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
        when inv.frontend_merchant_city in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
        when inv.frontend_merchant_city in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
        when inv.frontend_merchant_city in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
        when inv.frontend_merchant_city in ('SOLAN', 'Solan') then 'Solan'
        when inv.frontend_merchant_city in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
        when inv.frontend_merchant_city in ('bikaner', 'Bikaner') then 'Bikaner'
        when inv.frontend_merchant_city in ('Purnia', 'purnia') then 'Purnia'
        else inv.frontend_merchant_city
    end as city,
    inv.item_id,
    min(order_create_date_ist) min_order_date_item

FROM 
    dwh.fact_sales_invoice_item_details inv
join
    carts od
on
    inv.cart_id = od.cart_id

WHERE
    inv.order_create_date_ist <= date('{yesterday}')
    and
    inv.is_internal_order = false
    -- and
    -- inv.frontend_merchant_city in ('Gurgaon', 'HR-NCR')
    -- and
    -- pd.product_type = 'Maida'
GROUP BY 1,2
)
select * from final
"""

