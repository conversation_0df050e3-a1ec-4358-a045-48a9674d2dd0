dau_query = """
select
    city,
    at_date_ist as date,
    count(distinct device_id) as dau
from 
    ds_etls.dim_device_history
where
    1=1
    and city is not null 
    and at_date_ist >= '{start_date}'
    and at_date_ist <= '{end_date}'
    and at_date_ist in ({specific_dates})
    and city in {city_list}
group by 1,2
"""

dau_cohorts_query = """
select
    at_date_ist as date,
    city,
    coalesce(avg(existing_user_nbu),0) as existing_user_nbu,
    coalesce(avg(existing_user_lapsed_bu),0) as existing_user_lapsed_bu,
    coalesce(avg(existing_user_reg_bu),0) as existing_user_reg_bu,
    coalesce(avg(new_user_bu),0) as new_user_bu,
    coalesce(avg(new_user_nbu),0) as new_user_nbu
from
    ds_etls.dim_user_activity_and_buying_cohorts
where
    1=1
    and at_date_ist >= '{start_date}'
    and at_date_ist <= '{end_date}'
    and at_date_ist in ({specific_dates})
    and city is not null
    and city <> 'Not in service area'
    and city in {city_list}
group by 1,2
"""

orders_query = """
select
    od.order_create_dt_ist as date,
    case
        when city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
        when city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa', 'Panaji') then 'Goa'
        when city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
        when city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
        when city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
        when city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
        when city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
        when city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
        when city_name in ('SOLAN', 'Solan') then 'Solan'
        when city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
        when city_name in ('bikaner', 'Bikaner') then 'Bikaner'
        when city_name in ('Purnia', 'purnia') then 'Purnia'
        else city_name
    end as city,
    coalesce(count(distinct order_id),0) as carts,
    coalesce(count(distinct outlet_id),0) as num_serving_outlets
from
    dwh.fact_sales_order_details od
where
    1=1
    and od.order_create_dt_ist >= date '{start_date}'
    and od.order_create_dt_ist <= date '{end_date}'
    and od.order_create_dt_ist in ({specific_dates})
    and od.is_internal_order = false
    and od.order_current_status = 'DELIVERED'
    and city_name is not null
    and coalesce(city_name, '0') not in ('0', '', ' ', 'Not in service area')
    and city_name not like '%%B2B%%'
    and (case
        when city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
        when city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa', 'Panaji') then 'Goa'
        when city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
        when city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
        when city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
        when city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
        when city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
        when city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
        when city_name in ('SOLAN', 'Solan') then 'Solan'
        when city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
        when city_name in ('bikaner', 'Bikaner') then 'Bikaner'
        when city_name in ('Purnia', 'purnia') then 'Purnia'
        else city_name
    end) in {city_list}
group by 1, 2
"""

atc_query = """
select
    at_date_ist as date,
    case
        when traits__city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
        when traits__city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa', 'Panaji') then 'Goa'
        when traits__city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
        when traits__city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
        when traits__city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
        when traits__city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
        when traits__city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
        when traits__city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
        when traits__city_name in ('SOLAN', 'Solan') then 'Solan'
        when traits__city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
        when traits__city_name in ('bikaner', 'Bikaner') then 'Bikaner'
        when traits__city_name in ('Purnia', 'purnia') then 'Purnia'
        else traits__city_name
    end as city,
    coalesce(count(distinct lower(coalesce(coalesce(device_uuid, advertising_id), traits__user_id))),0) atcs
from
    lake_events.mobile_event_data
where 
    1=1
    and name = 'Product Added'
    and at_date_ist >= date '{start_date}'
    and at_date_ist <= date '{end_date}'
    and at_date_ist in ({specific_dates})
    and platform IN ('android', 'ios')
    and coalesce(traits__city_id,0) <> 0
    and coalesce(traits__city_name, '0') not in ('Not in service area', '0', ' ')
    and traits__merchant_id is not null
    and (case
        when traits__city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
        when traits__city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa', 'Panaji') then 'Goa'
        when traits__city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
        when traits__city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
        when traits__city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
        when traits__city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
        when traits__city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
        when traits__city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
        when traits__city_name in ('SOLAN', 'Solan') then 'Solan'
        when traits__city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
        when traits__city_name in ('bikaner', 'Bikaner') then 'Bikaner'
        when traits__city_name in ('Purnia', 'purnia') then 'Purnia'
        else traits__city_name
    end) in {city_list}
group by 1,2
"""

sla_query = """
select
    order_checkout_dt_ist as date,
    case
        when fsod.city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
        when fsod.city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa', 'Panaji') then 'Goa'
        when fsod.city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
        when fsod.city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
        when fsod.city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
        when fsod.city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
        when fsod.city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
        when fsod.city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
        when fsod.city_name in ('SOLAN', 'Solan') then 'Solan'
        when fsod.city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
        when fsod.city_name in ('bikaner', 'Bikaner') then 'Bikaner'
        when fsod.city_name in ('Purnia', 'purnia') then 'Purnia'
        else fsod.city_name
    end as city,
    approx_percentile(date_diff('minute', order_checkout_ts_ist, order_delivered_ts_ist), 0.5) as median_eta_actual_minutes,
    approx_percentile(eta_shown_mins, 0.5) as median_eta_shown_minutes
from
    dwh.fact_supply_chain_order_details fscod
left join 
    dwh.fact_sales_order_details fsod
on 
    fscod.order_id = fsod.order_id
where 
    1=1
    and fsod.order_create_dt_ist >= date '{start_date}'
    and fsod.order_create_dt_ist <= date '{end_date}' + interval '1' day
    and fscod.order_checkout_dt_ist >= date '{start_date}'
    and fscod.order_checkout_dt_ist <= date '{end_date}'
    and fscod.order_checkout_dt_ist in ({specific_dates})
    and fscod.order_current_status = 'DELIVERED'
    and fsod.is_internal_order = false
    and fsod.city_name is not null
    and coalesce(fsod.city_name,'0') not in ('0', '', ' ', 'Not in service area')
    and fsod.city_name not like '%%B2B%%'
    and (case
        when fsod.city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
        when fsod.city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa', 'Panaji') then 'Goa'
        when fsod.city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
        when fsod.city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
        when fsod.city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
        when fsod.city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
        when fsod.city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
        when fsod.city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
        when fsod.city_name in ('SOLAN', 'Solan') then 'Solan'
        when fsod.city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
        when fsod.city_name in ('bikaner', 'Bikaner') then 'Bikaner'
        when fsod.city_name in ('Purnia', 'purnia') then 'Purnia'
        else fsod.city_name
    end) in {city_list}
    group by 1, 2
"""

wallet_share_query = """
WITH base AS
(
SELECT 
    order_create_dt_ist as date,
    case
        when city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
        when city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa', 'Panaji') then 'Goa'
        when city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
        when city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
        when city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
        when city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
        when city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
        when city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
        when city_name in ('SOLAN', 'Solan') then 'Solan'
        when city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
        when city_name in ('bikaner', 'Bikaner') then 'Bikaner'
        when city_name in ('Purnia', 'purnia') then 'Purnia'
        else city_name
    end as city,
    dim_customer_key AS customer_id,
    sum(coalesce(total_selling_price,0)) AS total_value,
    sum(coalesce(total_retained_margin,0)) AS total_rm
FROM 
    dwh.fact_sales_order_details
WHERE 
    1=1
    and order_create_dt_ist >= date '{start_date}'
    and order_create_dt_ist <= date '{end_date}'
    and order_create_dt_ist in ({specific_dates})
    and is_internal_order = FALSE
    and order_current_status = 'DELIVERED'
    and coalesce(city_name, '0') not in ('0', '', ' ', 'Not in service area')
    and city_name not like '%%B2B%%'
    and (case
        when city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
        when city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa', 'Panaji') then 'Goa'
        when city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
        when city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
        when city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
        when city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
        when city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
        when city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
        when city_name in ('SOLAN', 'Solan') then 'Solan'
        when city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
        when city_name in ('bikaner', 'Bikaner') then 'Bikaner'
        when city_name in ('Purnia', 'purnia') then 'Purnia'
        else city_name
    end) in {city_list}
GROUP BY 1,2,3
)
SELECT 
    date,
    city,
    CASE
        WHEN total_value <= 100 THEN 'daily_spend_0_100'
        WHEN total_value > 100 and total_value <= 200 THEN 'daily_spend_101_200'
        WHEN total_value > 200 and total_value <= 500 THEN 'daily_spend_201_500'
        WHEN total_value > 500 and total_value <= 1000 THEN 'daily_spend_501_1000'
        WHEN total_value > 1000 and total_value <= 1500 THEN 'daily_spend_1001_1500'
        WHEN total_value > 1500 and total_value <= 2000 THEN 'daily_spend_1501_2000'
        WHEN total_value > 2000 and total_value <= 5000 THEN 'daily_spend_2001_5000'
        WHEN total_value > 5000 THEN 'daily_spend_5001_inf'
   END AS total_value_bucket,
   count(DISTINCT customer_id) AS customer_count
FROM base
GROUP BY 1,
         2,
         3
"""

l0_customer_query = """
SELECT 
    order_create_dt_ist as date,
    case
        when fsod.city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
        when fsod.city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa', 'Panaji') then 'Goa'
        when fsod.city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
        when fsod.city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
        when fsod.city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
        when fsod.city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
        when fsod.city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
        when fsod.city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
        when fsod.city_name in ('SOLAN', 'Solan') then 'Solan'
        when fsod.city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
        when fsod.city_name in ('bikaner', 'Bikaner') then 'Bikaner'
        when fsod.city_name in ('Purnia', 'purnia') then 'Purnia'
        else fsod.city_name
    end as city,
    CASE  
        WHEN id.l0_category IN ('Atta, Rice & Dal', 'Dry Fruits, Masala & Oil', 'Organic & Premium', 'Sauces & Spreads')  
            THEN 'Groceries_Staples'  
        WHEN id.l0_category IN ('Dairy & Breakfast', 'Ice Creams & Frozen Desserts', 'Instant & Frozen Food')  
            THEN 'Dairy_Frozen_Foods'  
        WHEN id.l0_category IN ('Tea, Coffee & Health Drinks', 'Cold Drinks & Juices')  
            THEN 'Tea_Coffee_Beverages'  
        WHEN id.l0_category IN ('Bakery & Biscuits', 'Biscuits, Snacks & Chocolates', 'Munchies', 'Sweet Tooth', 'Paan Corner', 'biscuits, namkeen & chocolates')  
            THEN 'Snacks_Packaged_Foods'  
        WHEN id.l0_category IN ('Vegetables & Fruits', 'Chicken, Meat & Fish')  
            THEN 'Meat_Fresh_Produce'  
        WHEN id.l0_category IN ('Baby Care', 'Personal Care', 'Beauty & Cosmetics', 'Face Cosmetics', 'Pharma & Wellness', 'Other Health Concerns', 'Pet Care')  
            THEN 'Personal_Wellness_Care'  
        WHEN id.l0_category IN ('Cleaning Essentials', 'Home & Office', 'Household Needs', 'cleaning & bathroom essentials', 'home appliances')  
            THEN 'Home_Essentials_Cleaning'  
        WHEN id.l0_category IN ('Books', 'Magazines', 'Toys & Games', 'Stationery Needs', 'dolls & accessories')  
            THEN 'Books_Toys_Stationery'  
        WHEN id.l0_category IN ('Fashion & Accessories', 'Digital Goods', 'Electronics & Electricals', 'fashion & lifestyle')  
            THEN 'Fashion_Electronics_Digital_Goods'  
        ELSE 'Other'  -- Default case for uncategorized items  
    END AS l0_category,
    coalesce(count(distinct dim_customer_key),0) as num_customers
FROM 
    dwh.fact_sales_order_item_details fsod
left join
    dwh.dim_item_product_offer_mapping ipom
on
    fsod.product_id = ipom.product_id and ipom.is_current = True
left join
    dwh.dim_product dp 
on 
    dp.product_id = fsod.product_id 
    and 
    DATE_TRUNC('SECOND', fsod.order_create_ts_ist) >= DATE_TRUNC('SECOND', dp.valid_from_ts_ist_enriched)
    and 
    DATE_TRUNC('SECOND', fsod.order_create_ts_ist) < DATE_TRUNC('SECOND', dp.valid_to_ts_ist_enriched)
left join
    supply_etls.item_details id
on
    ipom.item_id = id.item_id
WHERE 
    1=1
    and order_create_dt_ist >= date '{start_date}'
    and order_create_dt_ist <= date '{end_date}'
    and order_create_dt_ist in ({specific_dates})
    and is_internal_order = FALSE
    and order_current_status = 'DELIVERED'
    and id.l0_category not in ('Specials', 'Combo')
    and coalesce(fsod.city_name, '0') not in ('0', '', 'Not in service area')
    and fsod.city_name not like '%%B2B%%'
    and (case
        when fsod.city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
        when fsod.city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa', 'Panaji') then 'Goa'
        when fsod.city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
        when fsod.city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
        when fsod.city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
        when fsod.city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
        when fsod.city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
        when fsod.city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
        when fsod.city_name in ('SOLAN', 'Solan') then 'Solan'
        when fsod.city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
        when fsod.city_name in ('bikaner', 'Bikaner') then 'Bikaner'
        when fsod.city_name in ('Purnia', 'purnia') then 'Purnia'
        else fsod.city_name
    end) in {city_list}
GROUP BY 1,2,3
"""

l0_customer_query_old = """
with 
ipm as 
(
    select 
        icd.item_id, product_id, l0 as l0_category
    from 
        rpc.item_product_mapping ipm
    join 
        rpc.item_category_details icd
    on 
        ipm.item_id = icd.item_id
    where 
        active = 1
        and icd.lake_active_record = true
    group by 1,2,3
)

SELECT order_create_dt_ist as date,
        case
            when fsod.city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
            when fsod.city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa', 'Panaji') then 'Goa'
            when fsod.city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
            when fsod.city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
            when fsod.city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
            when fsod.city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
            when fsod.city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
            when fsod.city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
            when fsod.city_name in ('SOLAN', 'Solan') then 'Solan'
            when fsod.city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
            when fsod.city_name in ('bikaner', 'Bikaner') then 'Bikaner'
            when fsod.city_name in ('Purnia', 'purnia') then 'Purnia'
            else fsod.city_name
        end as city,
       l0_category,
       coalesce(count(distinct dim_customer_key),0) as num_customers
FROM 
    dwh.fact_sales_order_item_details fsod
JOIN 
    ipm 
ON 
    fsod.product_id = ipm.product_id
WHERE 
    1=1
    and order_create_dt_ist >= date '{start_date}'
    and order_create_dt_ist <= date '{end_date}'
    and order_create_dt_ist in ({specific_dates})
    and is_internal_order = FALSE
    and order_current_status = 'DELIVERED'
    and l0_category not in ('Specials', 'Combo')
    and coalesce(fsod.city_name, '0') not in ('0', '', 'Not in service area')
    and fsod.city_name not like '%%B2B%%'
GROUP BY 1,2,3
"""

dslo_query = """
select 
    at_date_ist as date, 
    city

    ,coalesce(count(distinct case when dslo <=1 then device_id else null end),0) as all_devices_dslo_00_le_1_days

    ,coalesce(count(distinct case when dslo <=7 then device_id else null end),0) as all_devices_dslo_01_le_7_days
    ,coalesce(count(distinct case when dslo <=30 then device_id else null end),0) as all_devices_dslo_02_le_30_days
    ,coalesce(count(distinct case when dslo <=90 then device_id else null end),0) as all_devices_dslo_03_le_90_days
    ,coalesce(count(distinct case when dslo <=180 then device_id else null end),0) as all_devices_dslo_04_le_180_days
    ,coalesce(count(distinct case when dslo <=365 then device_id else null end),0) as all_devices_dslo_05_le_365_days
    
    ,coalesce(count(distinct case when dslo >365 then device_id else null end),0) as all_devices_dslo_05_gt_1_yr

    ,coalesce(count(distinct case when dslo >0 and dslo <=7 then device_id else null end),0) as devices_dslo_01_1_7_days
    ,coalesce(count(distinct case when dslo >7 and dslo <=30 then device_id else null end),0) as devices_dslo_02_7_30_days
    ,coalesce(count(distinct case when dslo >30 and dslo <=90 then device_id else null end),0) as devices_dslo_03_30_90_days
    ,coalesce(count(distinct case when dslo >90 and dslo <=180 then device_id else null end),0) as devices_dslo_04_90_180_days
    ,coalesce(count(distinct case when dslo >180 and dslo <=365 then device_id else null end),0) as devices_dslo_05_180_365_days

-- case 
--     when dslo <= 1 then 'dslo_0_1'
--     when dslo <= 3 then 'dslo_1_3'
--     when dslo <= 14 then 'dslo_3_14'
--     when dslo <= 21 then 'dslo_14_21'
--     when dslo <= 28 then 'dslo_21_28'
--     when dslo <= 365 then 'dslo_28_365'
--     else 'dslo_365_inf'
-- end as dslo_bucket,
-- count(distinct device_id) as counts

from 
    ds_etls.dim_device_history
where 
    1=1
    and at_date_ist >=  '{start_date}'
    and at_date_ist <=  '{end_date}'
    and at_date_ist in ({specific_dates})
    and dslo is not null
    and city is not null
    and city <> 'Not in service area'
    and city in {city_list}
group by 1,2
"""

outlet_age_buckets_query = """
with 
outlet_age as
(
    select
        case
            when city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
            when city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa', 'Panaji') then 'Goa'
            when city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
            when city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
            when city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
            when city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
            when city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
            when city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
            when city_name in ('SOLAN', 'Solan') then 'Solan'
            when city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
            when city_name in ('bikaner', 'Bikaner') then 'Bikaner'
            when city_name in ('Purnia', 'purnia') then 'Purnia'
            else city_name
        end as city,
        a.outlet_id,
        order_create_dt_ist,
        date_diff('day', min(go_live_date), order_create_dt_ist) as selling_days
    from
        dwh.fact_sales_order_details a
    left join
        supply_etls.e3_live_darkstores b
    on
        a.outlet_id = b.outlet_id and b.active = 1
    where
        1 = 1
        and order_create_dt_ist >= date '{start_date}'
        and order_create_dt_ist <= date '{end_date}'
        and order_create_dt_ist in ({specific_dates})
        and order_current_status = 'DELIVERED'
        and is_internal_order = false
        and city_name is not null
        and coalesce(city_name,'0') not in ('0', '', 'Not in service area')
        and city_name not like '%%B2B%%'
        and (case
            when city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
            when city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa', 'Panaji') then 'Goa'
            when city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
            when city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
            when city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
            when city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
            when city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
            when city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
            when city_name in ('SOLAN', 'Solan') then 'Solan'
            when city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
            when city_name in ('bikaner', 'Bikaner') then 'Bikaner'
            when city_name in ('Purnia', 'purnia') then 'Purnia'
            else city_name
        end) in {city_list}
    group by 1,2,3
)
select
    city,
    order_create_dt_ist as date

    ,coalesce(count(distinct case when (selling_days <=7) then outlet_id else null end),0) as all_outlets_00_le_7_days
    
    ,coalesce(count(distinct case when selling_days <=30 then outlet_id else null end),0) as all_outlets_01_le_30_days
    ,coalesce(count(distinct case when selling_days <=90 then outlet_id else null end),0) as all_outlets_02_le_90_days
    ,coalesce(count(distinct case when selling_days <=180 then outlet_id else null end),0) as all_outlets_03_le_180_days
    ,coalesce(count(distinct case when selling_days <=365 then outlet_id else null end),0) as all_outlets_04_le_365_days
    
    ,coalesce(count(distinct case when selling_days >365 then outlet_id else null end),0) as all_outlets_05_gt_1_yr

    ,coalesce(count(distinct case when selling_days >7 and selling_days <=30 then outlet_id else null end),0) as outlets_01_7_30_days
    ,coalesce(count(distinct case when selling_days >30 and selling_days <=90 then outlet_id else null end),0) as outlets_02_30_90_days
    ,coalesce(count(distinct case when selling_days >90 and selling_days <=180 then outlet_id else null end),0) as outlets_03_90_180_days
    ,coalesce(count(distinct case when selling_days >180 and selling_days <=365 then outlet_id else null end),0) as outlets_04_180_365_days

from
    outlet_age
group by 1,2
"""

hex_min_order_date_query = """
with hex_min_dates as
(
select 
    case
        when city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
        when city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa', 'Panaji') then 'Goa'
        when city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
        when city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
        when city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
        when city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
        when city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
        when city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
        when city_name in ('SOLAN', 'Solan') then 'Solan'
        when city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
        when city_name in ('bikaner', 'Bikaner') then 'Bikaner'
        when city_name in ('Purnia', 'purnia') then 'Purnia'
        else city_name
    end as city,
    cast(a.hex9 as varchar) AS hex_id,
    min(f.order_create_dt_ist) as first_order_date
FROM 
    dwh.fact_sales_order_details f
JOIN 
    dwh.dim_customer_address a 
ON 
    a.address_id=f.dim_customer_address_key
where
    1=1
    and f.order_create_dt_ist >= date '{start_date}'
    and f.order_create_dt_ist <= date '{end_date}'
    and order_current_status = 'DELIVERED'
    and is_internal_order = false
    and city_name is not null
    and cast(city_name as varchar) not in ('0', '')
    and city_name not like '%%B2B%%'
    and city_name <> 'Not in service area'
    and DATE_DIFF('MINUTE', f.order_create_ts_ist, order_deliver_ts_ist) <60
group BY 1,2
),

existing_hex as
(
select
    city, 
    hex_id, 
    1 as present_flag 
from 
    ds_etls.demand_forecasting_hex_min_order_dates
    where city is not null
group by 1,2,3
),

combined as
(select
    a.*,
    coalesce(present_flag, 0) as present_flag

from
    hex_min_dates a
left join
    existing_hex b
on
    a.city = b.city
    and
    a.hex_id = b.hex_id
)

select city, hex_id, first_order_date from combined where present_flag = 0

"""
hex_min_order_date_query1 = """
select 
    case
        when city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
        when city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa', 'Panaji') then 'Goa'
        when city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
        when city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
        when city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
        when city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
        when city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
        when city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
        when city_name in ('SOLAN', 'Solan') then 'Solan'
        when city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
        when city_name in ('bikaner', 'Bikaner') then 'Bikaner'
        when city_name in ('Purnia', 'purnia') then 'Purnia'
        else city_name
    end as city,
    cast(a.hex9 as varchar) AS hex_id,
    min(f.order_create_dt_ist) as first_order_date
from 
    dwh.fact_sales_order_details f
join 
    dwh.dim_customer_address a 
on
    a.address_id=f.dim_customer_address_key
where
    1 = 1
    and f.order_create_dt_ist >= date '{start_date}'
    and f.order_create_dt_ist <= date '{end_date}'
    and order_current_status = 'DELIVERED'
    and is_internal_order = false
    and city_name is not null
    and cast(city_name as varchar) not in ('0', '')
    and city_name not like '%%B2B%%'
    and city_name <> 'Not in service area'
    and DATE_DIFF('MINUTE', f.order_create_ts_ist, order_deliver_ts_ist) <60
group BY 1,2
"""

hex_age_query = """
with hex_days AS
(
    SELECT 
        city,
        hex_id,
        date_diff('day', date(first_order_date), date ('{yesterday}')) AS selling_days
    FROM 
        ds_etls.demand_forecasting_hex_min_order_dates
    WHERE 
        1=1
        and city is not null
        and city in {city_list}
    GROUP BY 1,2,3
)
SELECT 
    city,
    date '{yesterday}' as date

    ,coalesce(count(distinct hex_id),0) as num_servicable_hex
    
    ,coalesce(count(distinct case when (selling_days >=0 and selling_days <=7) then hex_id else null end),0) as all_hex_00_le_7_days

    ,coalesce(count(distinct case when selling_days <=30 then hex_id else null end),0) as all_hex_01_le_30_days
    ,coalesce(count(distinct case when selling_days <=90 then hex_id else null end),0) as all_hex_02_le_90_days
    ,coalesce(count(distinct case when selling_days <=180 then hex_id else null end),0) as all_hex_03_le_180_days
    ,coalesce(count(distinct case when selling_days <=365 then hex_id else null end),0) as all_hex_04_le_365_days

    ,coalesce(count(distinct case when selling_days >365 then hex_id else null end),0) as all_hex_05_gt_1_yr

    ,coalesce(count(distinct case when selling_days >7 and selling_days <=30 then hex_id else null end),0) as hex_01_7_30_days
    ,coalesce(count(distinct case when selling_days >30 and selling_days <=90 then hex_id else null end),0) as hex_02_30_90_days
    ,coalesce(count(distinct case when selling_days >90 and selling_days <=180 then hex_id else null end),0) as hex_03_90_180_days
    ,coalesce(count(distinct case when selling_days >180 and selling_days <=365 then hex_id else null end),0) as hex_04_180_365_days

FROM 
    hex_days
group by 1,2
 
"""

device_age_query = """
with device_days as
(
    select
        city,
        at_date_ist as date,
        device_id,
        date_diff('day', 
                    CASE
                        WHEN first_seen_date LIKE '%%-%%-%%' THEN date(first_seen_date)
                        WHEN first_seen_date is null or first_seen_date in ('<NA>', '', 'nan') then date(at_date_ist)
                        ELSE cast(from_unixtime(cast(first_seen_date as bigint) / 1000) as date)
                    END,
                    date(at_date_ist)
                    ) as device_age
    from
        ds_etls.dim_device_history
    where
        1=1
        and city is not null
        and city in {city_list}
        and at_date_ist >= '{start_date}'
        and at_date_ist <= '{end_date}'
        and at_date_ist in ({specific_dates})
)
select
    city,
    date
    
    ,coalesce(count(distinct case when device_age =0 then device_id else null end),0) as all_devices_00_day_1
    
    ,coalesce(count(distinct case when device_age <=7 then device_id else null end),0) as all_devices_01_le_7_days
    ,coalesce(count(distinct case when device_age <=30 then device_id else null end),0) as all_devices_02_le_30_days
    ,coalesce(count(distinct case when device_age <=90 then device_id else null end),0) as all_devices_03_le_90_days
    ,coalesce(count(distinct case when device_age <=180 then device_id else null end),0) as all_devices_04_le_180_days
    ,coalesce(count(distinct case when device_age <=365 then device_id else null end),0) as all_devices_05_le_365_days
    
    ,coalesce(count(distinct case when device_age >365 then device_id else null end),0) as all_devices_06_gt_1_yr

    ,coalesce(count(distinct case when device_age >0 and device_age <=7 then device_id else null end),0) as devices_01_1_7_days
    ,coalesce(count(distinct case when device_age >7 and device_age <=30 then device_id else null end),0) as devices_02_7_30_days
    ,coalesce(count(distinct case when device_age >30 and device_age <=90 then device_id else null end),0) as devices_03_30_90_days
    ,coalesce(count(distinct case when device_age >90 and device_age <=180 then device_id else null end),0) as devices_04_90_180_days
    ,coalesce(count(distinct case when device_age >180 and device_age <=365 then device_id else null end),0) as devices_05_180_365_days

from 
    device_days
group by 1,2

"""

ptype_min_order_date_query = """
with
ptype_min_dates as
(
select 
    case
        when city_name in ('Gurgaon','Gurugram') then 'HR-NCR'
        when city_name in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa', 'Panaji') then 'Goa'
        when city_name in ('Noida', 'Ghaziabad') then 'UP-NCR'
        when city_name in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
        when city_name in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
        when city_name in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
        when city_name in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
        when city_name in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
        when city_name in ('SOLAN', 'Solan') then 'Solan'
        when city_name in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
        when city_name in ('bikaner', 'Bikaner') then 'Bikaner'
        when city_name in ('Purnia', 'purnia') then 'Purnia'
        else city_name
    end as city,
    case 
        when lower(replace(dp.product_type, '''', '')) like '%%combo%%' 
                or lower(replace(dp.product_type, '''', '')) like '%%special%%' 
                or lower(replace(dp.product_type, '''', '')) like '%%gift%%' 
            then replace(id.p_type, '''', '')
        else replace(dp.product_type, '''', '')
    end as product_type,
    min(order_create_dt_ist) first_order_date
from 
    dwh.fact_sales_order_item_details fsoid
left join
    dwh.dim_item_product_offer_mapping ipom
on
    fsoid.product_id = ipom.product_id and ipom.is_current = True
left join
    supply_etls.item_details id
on
    ipom.item_id = id.item_id
join
    dwh.dim_product dp 
on 
    dp.product_id = fsoid.product_id 
    and 
    DATE_TRUNC('SECOND', fsoid.order_create_ts_ist) >= DATE_TRUNC('SECOND', dp.valid_from_ts_ist_enriched)
    and 
    DATE_TRUNC('SECOND', fsoid.order_create_ts_ist) < DATE_TRUNC('SECOND', dp.valid_to_ts_ist_enriched)
where 
    1=1
    and order_create_dt_ist >= date '{start_date}'
    and order_create_dt_ist <= date '{end_date}'
    and fsoid.is_internal_order = false
    and fsoid.order_current_status = 'DELIVERED'
    and city_name is not null
    and cast(city_name as varchar) not in ('0', '')
    and city_name not like '%%B2B%%'
    and city_name <> 'Not in service area'
--    and pid_map.product_type = 'Atta'
--    and city_name = 'HR-NCR'
group by 1, 2
),

existing_ptypes as
(
select
    city, 
    product_type, 
    1 as present_flag 
from 
    ds_etls.demand_forecasting_ptype_min_order_dates
    where city is not null -- in (select distinct city from ptype_min_dates)
group by 1,2,3
),

combined as
(select
    a.*,
    coalesce(present_flag, 0) as present_flag

from
    ptype_min_dates a
left join
    existing_ptypes b
on
    a.city = b.city
    and
    a.product_type = b.product_type
)

select city, product_type, first_order_date from combined where present_flag = 0

"""

hierarchy_min_order_date_query = """
with 
carts as
(
select cart_id from dwh.fact_sales_order_item_details od
where
    od.order_create_dt_ist >= date('{yesterday}') - interval '7' day
    and
    od.order_current_status = 'DELIVERED'
    AND
    od.is_internal_order = False
    group by 1
),

item_category_details as
(
select
    l0, 
    l0_id,
    l1, 
    l1_id, 
    l2, 
    l2_id,
    item_id, 
    name as item_name,
    min(created_at) as min_created_at
from
    rpc.item_category_details
where lake_active_record = True
group by 1,2,3,4,5,6,7,8
),
sku_min_dates as
(SELECT
    case
        when inv.frontend_merchant_city in ('Gurgaon', 'Gurugram') then 'HR-NCR'
        when inv.frontend_merchant_city in ('Goa', 'NorthGoa', 'SouthGoa', 'North-Goa', 'North Goa', 'South-Goa', 'South Goa', 'Panaji') then 'Goa'
        when inv.frontend_merchant_city in ('Noida', 'Ghaziabad') then 'UP-NCR'
        when inv.frontend_merchant_city in ('Muzzaffarnagar', 'Muzaffarnagar') then 'Muzaffarnagar'
        when inv.frontend_merchant_city in ('Tiruchirappalli', 'Tiruchirapalli') then 'Tiruchirapalli'
        when inv.frontend_merchant_city in ('New Chandigarh', 'NewChandigarh') then 'New Chandigarh'
        when inv.frontend_merchant_city in ('Muzaffarpur', 'Muzzaffarpur') then 'Muzaffarpur'
        when inv.frontend_merchant_city in ('Sri_Ganganagar', 'Sri Ganganagar') then 'Sri Ganganagar'
        when inv.frontend_merchant_city in ('SOLAN', 'Solan') then 'Solan'
        when inv.frontend_merchant_city in ('Sundar_Nagar', 'Sundar Nagar') then 'Sundar Nagar'
        when inv.frontend_merchant_city in ('bikaner', 'Bikaner') then 'Bikaner'
        when inv.frontend_merchant_city in ('Purnia', 'purnia') then 'Purnia'
        else inv.frontend_merchant_city
    end as city,
    icd.l0,
    icd.l1,
    icd.l2,
    inv.item_id,
    icd.item_name,
    min(icd.min_created_at) as created_at,
    min(order_create_date_ist) first_order_date
    
FROM 
    dwh.fact_sales_invoice_item_details inv
join
    carts od
on
    inv.cart_id = od.cart_id
join
    item_category_details icd
ON 
    inv.item_id = icd.item_id
WHERE
    inv.order_create_date_ist >= date('{yesterday}') - interval '7' day
    and inv.is_internal_order = false
    and coalesce(inv.frontend_merchant_city, '') not in ('', 'Not in service area')
    and inv.frontend_merchant_city not like '%%B2B%%'
--    and inv.frontend_merchant_city = 'HR-NCR'
--    and cast(inv.item_id as varchar) = '10008305'
GROUP BY 1,2,3,4,5,6
),

existing_skus as 
(
select
    city, l0, l1, l2, item_id, 1 as present_flag
from
    ds_etls.demand_forecasting_item_min_order_dates
where 
    city is not null
group by 1,2,3,4,5,6

),

combined as 
(
select
    a.*, 
    coalesce(present_flag, 0) as present_flag
from
    sku_min_dates a
left join
    existing_skus b
on
    a.city = b.city
    and a.l0 = a.l0
    and a.l1 = a.l1
    and a.l2 = a.l2
    and a.item_id = a.item_id
)

select city, l0, l1, l2, item_id, item_name, created_at, first_order_date from combined where present_flag = 0
"""