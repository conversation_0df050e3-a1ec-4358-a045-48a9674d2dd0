{"cells": [{"cell_type": "markdown", "id": "b26eef74-9b0f-4546-a289-13a04aa9a799", "metadata": {}, "source": ["# Ptype Subgroup Mapping"]}, {"cell_type": "markdown", "id": "be757aba-1a31-4c79-a59f-bf30b352ce4f", "metadata": {"tags": []}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": null, "id": "6fcf4811-e2ba-4d3d-9e67-c6cb5e94b26f", "metadata": {}, "outputs": [], "source": ["module = \"Ptype Subgroup Mapping\""]}, {"cell_type": "code", "execution_count": null, "id": "8b5e29be-7bae-4fd2-b6f6-4b917205f568", "metadata": {}, "outputs": [], "source": ["!pip install numpy==1.26.4"]}, {"cell_type": "code", "execution_count": null, "id": "eef4d90e-041f-4f33-9f00-598c5c76c8f6", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import gc\n", "import pdb\n", "import os\n", "import pytz\n", "import pencilbox as pb\n", "from pytz import timezone\n", "from functools import reduce\n", "from datetime import datetime, timedelta\n", "from dateutil.relativedelta import relativedelta\n", "import itertools, sys\n", "import yaml"]}, {"cell_type": "code", "execution_count": null, "id": "e5c265d0-dea5-41f3-94e5-4d2a705b6d01", "metadata": {}, "outputs": [], "source": ["cwd = \"/usr/local/airflow/dags/repo/dags/data_science/demand_forecasting_packaged/etl/ptype_item_subgroups\"\n", "sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "ee307cf7-0eb6-4e52-8ac4-abc1dd33d860", "metadata": {}, "outputs": [], "source": ["import support_functions as sf\n", "import table_schemas as schema\n", "import ptype_feature_queries as pfq"]}, {"cell_type": "code", "execution_count": null, "id": "1e7f74b4-5c6b-4928-9f18-f02c46245fea", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_rows\", None)"]}, {"cell_type": "markdown", "id": "6fabb5c1-87e3-4d59-9924-f5882e6e3bf7", "metadata": {"tags": []}, "source": ["## Utils"]}, {"cell_type": "code", "execution_count": null, "id": "fc1189c7-df25-4cf2-a7c0-b1c303fe1f62", "metadata": {}, "outputs": [], "source": ["def send_log(*args, linspace=1):\n", "    return (\n", "        f\"[{module}]<>\"\n", "        + \"[run=\"\n", "        + sf.run_id\n", "        + datetime.now(pytz.timezone(\"Asia/Kolkata\")).strftime(\"[ts=%Y%m%d %H%M%S] --> \")\n", "        + \" \".join(map(str, args))\n", "    )\n", "\n", "\n", "def print_log(*args, linspace=1, task=None, Stage=None, Outcome=None):\n", "    print(\n", "        f\"[{module}]<>\"\n", "        + \"[run=\"\n", "        + sf.run_id\n", "        + datetime.now(pytz.timezone(\"Asia/Kolkata\")).strftime(\"[ts=%Y%m%d %H%M%S] --> \")\n", "        + \" \".join(map(str, args))\n", "    )\n", "\n", "\n", "def send_to_slack(msg):\n", "    try:\n", "        # 1 == 1\n", "        pb.send_slack_message(\n", "            channel=alerts_channel,\n", "            text=msg,\n", "        )\n", "    except Exception as e:\n", "        error_msg = str(e).split(\"\\n\")[0]\n", "        msg = f\"Unable to push message {msg} to slack, got error = {error_msg}\"\n", "        send_log(msg)\n", "\n", "\n", "def sanitise_string(s):\n", "    return \"_\".join(s.replace(\",\", \"\").replace(\"&\", \"\").split())"]}, {"cell_type": "code", "execution_count": null, "id": "8320eb8f-10ef-420f-a9a9-6b8c279c6efb", "metadata": {}, "outputs": [], "source": ["alerts_channel = \"bl_forecasting_ptype_data_prep_alerts\""]}, {"cell_type": "code", "execution_count": null, "id": "eac40a30-a959-41f4-a74b-1aeb617a015a", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["run_date = datetime.now(timezone(\"Asia/Kolkata\")).date()\n", "yesterday = run_date - relativedelta(days=1)"]}, {"cell_type": "code", "execution_count": null, "id": "1469a225-3ce9-453f-a602-868c64765754", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["run_id = datetime.now() + <PERSON><PERSON><PERSON>(hours=5, minutes=30)\n", "run_id = run_id.replace(microsecond=0)\n", "run_id"]}, {"cell_type": "code", "execution_count": null, "id": "cbfe9ee3-de69-4081-bb72-49856a493526", "metadata": {}, "outputs": [], "source": ["_trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "d3faa730-b2a4-49c5-b377-6d6e7500e591", "metadata": {}, "outputs": [], "source": ["print_log(os.listdir())"]}, {"cell_type": "code", "execution_count": null, "id": "55ac2d0c-7d57-4e69-b8ab-70f03fd16d05", "metadata": {}, "outputs": [], "source": ["try:\n", "    msg = f\"Reading the item to subgroup mapping from disk\"\n", "    print_log(msg)\n", "    send_to_slack(send_log(msg))\n", "\n", "    with open(\n", "        \"/usr/local/airflow/dags/repo/dags/data_science/demand_forecasting_packaged/etl/ptype_item_subgroups/Rakhi_item_types.yaml\",\n", "        \"r\",\n", "    ) as f:\n", "        ptype_item_type_df = yaml.safe_load(f)\n", "\n", "    msg = f\"Done\"\n", "    print_log(msg)\n", "    send_to_slack(send_log(msg))\n", "\n", "    ptype_item_type_df = pd.DataFrame(ptype_item_type_df)\n", "    ptype_item_type_df[\"snapshot_dt_ist\"] = run_id\n", "    ptype_item_type_df.head()\n", "except Exception as e:\n", "    msg = f\"Could not read YAML, stopping execution\"\n", "    print_log(msg)\n", "    send_to_slack(send_log(msg))\n", "    raise Exception\n", "\n", "ptype_item_type_df = ptype_item_type_df.drop_duplicates()\n", "ptype_item_type_df.reset_index(inplace=True, drop=True)\n", "ptype_item_type_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bb3e9bab-3181-45d5-bce1-8080bd5b4b9b", "metadata": {}, "outputs": [], "source": ["msg = f\"Pushing the pid to ptype sub-group mapping to DB\"\n", "print_log(msg)\n", "send_to_slack(send_log(msg))\n", "pb.to_trino(ptype_item_type_df, **schema.mapping_schema_ptype_item_subgroups)\n", "print_log(\"Done\")\n", "send_to_slack(send_log(\"Done\"))"]}, {"cell_type": "code", "execution_count": null, "id": "77c26241-3baa-45c4-82f4-2d6f71149c5d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d19f5f67-80f6-45b4-b687-1c6ee788f2f5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}