{"cells": [{"cell_type": "markdown", "id": "4cedd39c-ca2a-4038-b2ad-3f0448ded87c", "metadata": {}, "source": ["# Ptype Subgroup Searchers"]}, {"cell_type": "markdown", "id": "89db9342-bc2c-4e46-af3f-cf6e411a904a", "metadata": {"tags": []}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": null, "id": "fbe26258-95e8-4bee-94a3-b421083486e6", "metadata": {}, "outputs": [], "source": ["module = \"Ptype Subgroup Searchers\""]}, {"cell_type": "code", "execution_count": null, "id": "fb7e76be-045c-4141-815a-230f25599e98", "metadata": {}, "outputs": [], "source": ["!pip install numpy==1.26.4"]}, {"cell_type": "code", "execution_count": null, "id": "8ca33292-e2f4-48e3-93dc-d6081e8cbcfa", "metadata": {}, "outputs": [], "source": ["import os\n", "import pytz\n", "import pandas as pd\n", "import pencilbox as pb\n", "from pytz import timezone\n", "from functools import reduce\n", "from datetime import datetime, timedelta\n", "from dateutil.relativedelta import relativedelta\n", "import itertools, sys\n", "import numpy as np\n", "import gc\n", "import pdb"]}, {"cell_type": "code", "execution_count": null, "id": "e72d0aac-a1c3-4951-9c5d-8ad893fea220", "metadata": {}, "outputs": [], "source": ["cwd = \"/usr/local/airflow/dags/repo/dags/data_science/demand_forecasting_packaged/etl/ptype_item_subgroups\"\n", "sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "a7bd3a4e-97a5-42a7-acf8-e130f8725bcf", "metadata": {}, "outputs": [], "source": ["import support_functions as sf\n", "import table_schemas as schema"]}, {"cell_type": "code", "execution_count": null, "id": "85b2862c-8f49-463c-9602-1b915a88a55a", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_rows\", None)"]}, {"cell_type": "markdown", "id": "94e985f2-73f2-4181-82cf-7e282ba62e80", "metadata": {"tags": []}, "source": ["## Utils"]}, {"cell_type": "code", "execution_count": null, "id": "6c627148-58b5-4db3-be3f-eccb35d1b92b", "metadata": {}, "outputs": [], "source": ["def send_log(*args, linspace=1):\n", "    return (\n", "        f\"[{module}]<>\"\n", "        + \"[run=\"\n", "        + sf.run_id\n", "        + datetime.now(pytz.timezone(\"Asia/Kolkata\")).strftime(\"[ts=%Y%m%d %H%M%S] --> \")\n", "        + \" \".join(map(str, args))\n", "    )\n", "\n", "\n", "def print_log(*args, linspace=1, task=None, Stage=None, Outcome=None):\n", "    print(\n", "        f\"[{module}]<>\"\n", "        + \"[run=\"\n", "        + sf.run_id\n", "        + datetime.now(pytz.timezone(\"Asia/Kolkata\")).strftime(\"[ts=%Y%m%d %H%M%S] --> \")\n", "        + \" \".join(map(str, args))\n", "    )\n", "\n", "\n", "def send_to_slack(msg):\n", "    try:\n", "        # 1 == 1\n", "        pb.send_slack_message(\n", "            channel=alerts_channel,\n", "            text=msg,\n", "        )\n", "    except Exception as e:\n", "        error_msg = str(e).split(\"\\n\")[0]\n", "        msg = f\"Unable to push message {msg} to slack, got error = {error_msg}\"\n", "        send_log(msg)\n", "\n", "\n", "def sanitise_string(s):\n", "    return \"_\".join(s.replace(\",\", \"\").replace(\"&\", \"\").split())"]}, {"cell_type": "code", "execution_count": null, "id": "eaf73104-51b4-464e-81a7-188b816cccf8", "metadata": {}, "outputs": [], "source": ["alerts_channel = \"bl_forecasting_ptype_data_prep_alerts\""]}, {"cell_type": "code", "execution_count": null, "id": "0a251f74-e92c-4508-b60d-47e693ad27b6", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["run_date = datetime.now(timezone(\"Asia/Kolkata\")).date()\n", "yesterday = run_date - relativedelta(days=1)"]}, {"cell_type": "code", "execution_count": null, "id": "a0b81fb6-f259-4e5a-a6bf-9b6e51c19081", "metadata": {}, "outputs": [], "source": ["run_id = datetime.now() + <PERSON><PERSON><PERSON>(hours=5, minutes=30)\n", "run_id = run_id.replace(microsecond=0)\n", "run_id"]}, {"cell_type": "code", "execution_count": null, "id": "07ba292f-6516-4079-b97d-b03f21f24deb", "metadata": {}, "outputs": [], "source": ["if \"000\" not in str(run_id):\n", "    run_id = str(run_id) + \".000\"\n", "run_id"]}, {"cell_type": "code", "execution_count": null, "id": "b468d8a4-a36d-4b24-a6e4-db442722db8f", "metadata": {}, "outputs": [], "source": ["msg = f\"\\n\\nCommencing data prep for {module} on {str(run_date)} for {module}\\n\"\n", "print_log(msg)\n", "send_to_slack(send_log(msg))"]}, {"cell_type": "markdown", "id": "dc963902-77b7-4304-b0bf-0b7cee540fb0", "metadata": {"tags": []}, "source": ["## Ptype Features Queries and Table Schema"]}, {"cell_type": "code", "execution_count": null, "id": "085e9cc7-c6cd-4b1e-9490-e459742e76e6", "metadata": {}, "outputs": [], "source": ["# del base_ft_query_dict\n", "try:\n", "    import ptype_feature_queries as pfq\n", "\n", "    print_log(\"Imported queries from support files\")\n", "    base_ft_query_dict = {\n", "        \"ptype_carts\": pfq.ptype_city_sales_query,\n", "        \"ptype_avg_availability\": pfq.ptype_city_availability_query,\n", "        \"ptype_funnel\": pfq.ptype_city_funnel_query,\n", "        \"ptype_searchers\": pfq.ptype_city_searchers_query,\n", "    }\n", "except Exception as e:\n", "    error_msg = str(e).split(\"\\n\")[0]\n", "    msg = f\"Support files couldn't be imported, adding queries from the main notebook, got error = {error_msg}\"\n", "    print_log(msg)\n", "    send_to_slack(send_log(msg))"]}, {"cell_type": "code", "execution_count": null, "id": "586c9817-ec32-40d7-bc40-a7365b080497", "metadata": {}, "outputs": [], "source": ["list(base_ft_query_dict.keys())"]}, {"cell_type": "code", "execution_count": null, "id": "c338f148-43a6-4781-b2f1-00dc977d6999", "metadata": {}, "outputs": [], "source": ["def create_interim_search_data(start_date, end_date, city_list):\n", "    # Creating interim table for keyword-searching devices\n", "    try:\n", "        msg = f\"Creating interim table for collecting searching devices between {str(start_date)} and {str(end_date)}\"\n", "        print_log(msg)\n", "        send_to_slack(send_log(msg))\n", "        _trino_con = pb.get_connection(\"[Warehouse] Trino\")\n", "        pb.to_trino(\n", "            pfq.keyword_search_devices_query.format(\n", "                start_date=start_date, end_date=end_date, city_list=city_list\n", "            ),\n", "            **schema.keyword_search_devices_schema,\n", "        )\n", "        del _trino_con\n", "        print_log(\"Done\")\n", "        send_to_slack(send_log(\"Done\"))\n", "    except Exception as e:\n", "        msg = f\"Failed to push keyword searching devices to interim DB between {str(start_date)} and {str(end_date)}, got error = {e}\"\n", "        print_log(msg)\n", "        send_to_slack(send_log(msg))\n", "        raise Exception(msg)\n", "\n", "    # Creating interim table for ATCed-via-search devices\n", "    try:\n", "        msg = f\"Creating interim table for collecting ATCed-via-search devices between {str(start_date)} and {str(end_date)}\"\n", "        print_log(msg)\n", "        send_to_slack(send_log(msg))\n", "        _trino_con = pb.get_connection(\"[Warehouse] Trino\")\n", "        pb.to_trino(\n", "            pfq.keyword_ptype_atc_devices_query.format(\n", "                start_date=start_date, end_date=end_date, city_list=city_list\n", "            ),\n", "            **schema.keyword_ptype_atc_devices_schema,\n", "        )\n", "        del _trino_con\n", "        print_log(\"Done\")\n", "        send_to_slack(send_log(\"Done\"))\n", "    except Exception as e:\n", "        msg = f\"Failed to push ATCed-via-search devices data to interim DB between {str(start_date)} and {str(end_date)}, got error = {e}\"\n", "        print_log(msg)\n", "        send_to_slack(send_log(msg))\n", "        raise Exception(msg)"]}, {"cell_type": "markdown", "id": "022871a2-2897-4610-b14d-d5bf1f8c2ba5", "metadata": {"tags": []}, "source": ["## Execution"]}, {"cell_type": "code", "execution_count": null, "id": "e0cf4d96-80f2-4747-a223-72c108279d7d", "metadata": {}, "outputs": [], "source": ["full_reset = 0\n", "last_full_refresh_trigger_dt = \"2025-05-28\""]}, {"cell_type": "code", "execution_count": null, "id": "136472d4-9dbf-4f54-8cd7-87225d7a541b", "metadata": {"tags": []}, "outputs": [], "source": ["end_date = yesterday\n", "if full_reset > 0:\n", "    sdate = datetime.strptime(\"2022-01-01\", \"%Y-%m-%d\").date()\n", "else:\n", "    # sdate = datetime.strptime(\"2022-01-01\", \"%Y-%m-%d\").date()\n", "    sdate = yesterday - relativedel<PERSON>(days=150)\n", "\n", "dt = sdate\n", "start_dates = []\n", "\n", "while dt <= end_date:\n", "    start_dates.append(dt)\n", "    dt = dt + relativedelta(days=1)\n", "start_dates.reverse()\n", "print_log(len(start_dates))"]}, {"cell_type": "markdown", "id": "4990e900-0bab-4020-ba98-8da123216621", "metadata": {}, "source": ["### Checking for existing dates"]}, {"cell_type": "code", "execution_count": null, "id": "54543e55-bcf9-4342-954e-3484b56dad1f", "metadata": {}, "outputs": [], "source": ["_trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "cc10c937-5c15-4606-83ac-e7323cc0ce29", "metadata": {"tags": []}, "outputs": [], "source": ["msg = f\"Checking for existing city x date combinations for {module}\"\n", "print_log(msg)\n", "send_to_slack(send_log(msg))\n", "if full_reset == 0:\n", "    status_query = \"select city, date, true as is_done from ds_etls.demand_forecasting_ptype_city_daily_searchers where city is not null and date is not null and product_type is not null group by 1,2\"\n", "    try:\n", "        done_dates = pd.read_sql(\n", "            status_query,\n", "            _trino_con,\n", "        )\n", "        print_log(\"Done\")\n", "        send_to_slack(send_log(\"Done\"))\n", "    except Exception as e:\n", "        msg = f\"Could not get dates for existing data on p-type searchers, got error = {str(e).split('errorCode')[0]}\"\n", "        print_log(msg)\n", "        send_to_slack(send_log(msg))\n", "        done_dates = pd.DataFrame({\"city\": [\"PAN_India\"], \"date\": [\"1970-01-01\"], \"done\": [True]})\n", "else:\n", "    status_query = f\"select city, date, true as is_done from ds_etls.demand_forecasting_ptype_city_daily_searchers where city is not null and date is not null and snapshot_dt_ist >=  cast('{last_full_refresh_trigger_dt}' as timestamp) and product_type is not null group by 1,2\"\n", "    try:\n", "        done_dates = pd.read_sql(\n", "            status_query,\n", "            _trino_con,\n", "        )\n", "        print_log(\"Done\")\n", "        send_to_slack(send_log(\"Done\"))\n", "    except Exception as e:\n", "        msg = f\"Could not get dates for existing data on p-type searchers, got error = {str(e).split('errorCode')[0]}\"\n", "        print_log(msg)\n", "        send_to_slack(send_log(msg))\n", "        done_dates = pd.DataFrame({\"city\": [\"PAN_India\"], \"date\": [\"1970-01-01\"], \"done\": [True]})\n", "\n", "msg = f\"Number of done city x dates: \\n\\n{done_dates.groupby('city', as_index = False).agg({'date':'nunique'}).sort_values('date', ascending = False).reset_index(drop = True)}\"\n", "print_log(msg)\n", "send_to_slack(send_log(msg))"]}, {"cell_type": "code", "execution_count": null, "id": "5e013ff3-b719-4c83-8779-8e76c254d4fa", "metadata": {}, "outputs": [], "source": ["msg = f\"Creating data of {module} for {len(start_dates)} days\\n\\n\"\n", "print_log(msg)\n", "send_to_slack(send_log(msg))"]}, {"cell_type": "code", "execution_count": null, "id": "1fdfb0fa-8ffe-40f6-9ed4-576ba5e03faf", "metadata": {}, "outputs": [], "source": ["ft_dts = sorted(start_dates)\n", "date_diff = 0\n", "\n", "sdate_fixed = min(ft_dts)\n", "sdate = sdate_fixed\n", "edate = max(ft_dts)\n", "first_date = sdate_fixed\n", "\n", "date_boundaries = []\n", "\n", "if sdate_fixed == edate:\n", "    date_boundaries.append({\"start_date\": str(sdate), \"end_date\": str(edate)})\n", "else:\n", "    while sdate >= first_date and edate > first_date:\n", "        sdate = edate - relativedelta(days=date_diff)\n", "        batch_dates = {\"start_date\": str(sdate), \"end_date\": str(edate)}\n", "        date_boundaries.append(batch_dates)\n", "        edate = sdate - relativedelta(days=1)\n", "date_boundaries[-1][\"start_date\"] = str(sdate_fixed)\n", "date_boundaries.reverse()\n", "date_boundaries[0:3]"]}, {"cell_type": "markdown", "id": "b452c435-fe91-41cc-b290-68c0b83d03b3", "metadata": {"tags": []}, "source": ["### Pulling P-type keyword Searchers data for each of the date boundaries"]}, {"cell_type": "code", "execution_count": null, "id": "b64d90e2-4a6b-4fb4-8130-03ae1d87297c", "metadata": {}, "outputs": [], "source": ["for i in range(len(date_boundaries)):\n", "\n", "    start_date = str(date_boundaries[i][\"start_date\"])\n", "    end_date = str(date_boundaries[i][\"end_date\"])\n", "    start_dates = [\n", "        datetime.strftime(x, \"%Y-%m-%d\") for x in pd.date_range(start_date, end_date, freq=\"d\")\n", "    ]\n", "\n", "    dates = [\n", "        str(x)\n", "        for x in ft_dts\n", "        if (str(x) >= start_date and str(x) <= end_date and str(x) not in done_dates.date.unique())\n", "    ]\n", "\n", "    if len(dates) > 0:\n", "        if len(dates) == 1:\n", "            dates = dates + [\"1970-01-01\"]\n", "        dates = tuple(dates)\n", "\n", "        _trino_con = pb.get_connection(\"[Warehouse] Trino\")\n", "        msg = f\"Getting the list of all cities active between {str(start_date)} and {str(end_date)}\"\n", "        print_log(msg)\n", "        send_to_slack(send_log(msg))\n", "\n", "        all_cities = pd.read_sql(\n", "            pfq.city_list_query.format(start_date=start_date, end_date=end_date),\n", "            con=_trino_con,\n", "        ).city.to_list()\n", "\n", "        if len(all_cities) > 0:\n", "            all_cities = sorted(list(set(all_cities)))\n", "            all_city_dates = pd.DataFrame(itertools.product(all_cities, start_dates))\n", "            all_city_dates.columns = [\"city\", \"date\"]\n", "            all_city_dates = all_city_dates.merge(done_dates, on=[\"city\", \"date\"], how=\"left\")\n", "            all_city_dates_full = all_city_dates.copy()\n", "            all_city_dates = all_city_dates[~(all_city_dates.is_done == True)]\n", "        else:\n", "            msg = f\"Unable to find any cities between {start_date} and {end_date} dates, moving on\\n\\n\"\n", "            print_log(msg)\n", "            send_to_slack(send_log(msg))\n", "\n", "        if len(all_city_dates) > 0:\n", "\n", "            all_cities = sorted(list(all_city_dates.city.unique()))\n", "            all_cities_full = sorted(list(all_city_dates_full.city.unique()))\n", "\n", "            msg = f\"Found {len(all_cities)} cities between {str(start_date)} and {str(end_date)}.\\n\\nAll cities : \\n{all_cities}\\n\\nBUT : Generating data for all {len(all_cities_full)} cities and pushing to DB via direct query\\n\"\n", "            print_log(msg)\n", "            send_to_slack(send_log(msg))\n", "\n", "            required_dates = \", \".join([f\"date('{date}')\" for date in dates])\n", "            required_dates_str = \", \".join([f\"'{date}'\" for date in dates])\n", "\n", "            if len(all_cities) > 1:\n", "                city_list = tuple(all_cities_full)\n", "            else:\n", "                city_list = tuple(all_cities_full + [\"\"])\n", "\n", "            del all_cities, all_cities_full\n", "            gc.collect()\n", "\n", "            # Creating interim table for searching devices\n", "            create_interim_search_data(\n", "                start_date=start_date, end_date=end_date, city_list=city_list\n", "            )\n", "\n", "            try:\n", "                _trino_con = pb.get_connection(\"[Warehouse] Trino\")\n", "                pb.to_trino(\n", "                    pfq.ptype_city_searchers_query.format(\n", "                        start_date=start_date,\n", "                        end_date=end_date,\n", "                        specific_dates=required_dates,\n", "                        city_list=city_list,\n", "                    ),\n", "                    **schema.ptype_table_schema_searchers,\n", "                )\n", "                msg = f\"Pushed the data between dates : {str(start_date)} and {str(end_date)} to DB\"\n", "                send_to_slack(send_log(msg))\n", "                print_log(msg)\n", "\n", "            except Exception as e:\n", "                error_msg = str(e).split(\"errorCode\")[0]\n", "                msg = f\"Failed to upload data between {str(start_date)} and {str(end_date)} dates to DB, \\ngot error = \\n\\n{error_msg}\\n\"\n", "                print_log(msg)\n", "                send_to_slack(send_log(msg))\n", "                if any(\n", "                    [\n", "                        x in error_msg.lower()\n", "                        for x in [\"reset\", \"aborted\", \"error 502\", \"code 502\", \"bad gateway\"]\n", "                    ]\n", "                ):\n", "                    msg = \"Establishing DB connection again and re-trying\"\n", "                    print_log(msg)\n", "                    send_to_slack(send_log(msg))\n", "                    try:\n", "                        _trino_con = pb.get_connection(\"[Warehouse] Trino\")\n", "                        pb.to_trino(\n", "                            pfq.ptype_city_searchers_query.format(\n", "                                start_date=start_date,\n", "                                end_date=end_date,\n", "                                specific_dates=required_dates,\n", "                                city_list=city_list,\n", "                            ),\n", "                            **schema.ptype_table_schema_searchers,\n", "                        )\n", "                        msg = f\"Pushed the data between dates : {str(start_date)} and {str(end_date)} to DB\"\n", "                        send_to_slack(send_log(msg))\n", "                        print_log(msg)\n", "                    except Exception as e:\n", "                        error_msg = str(e).split(\"errorCode\")[0]\n", "                        msg = f\"Failed to upload data between {str(start_date)} and {str(end_date)} dates to DB, \\ngot error = \\n\\n{error_msg}\\n\"\n", "                        print_log(msg)\n", "                        send_to_slack(send_log(msg))\n", "\n", "                else:\n", "                    msg = f\"Unable to fill data between {str(start_date)} and {str(end_date)}, MOVING ON\"\n", "                    print_log(msg)\n", "                    send_to_slack(send_log(msg))\n", "                    continue\n", "            msg = f\"Done with all cities\"\n", "            print_log(msg)\n", "            send_to_slack(send_log(msg))\n", "        else:\n", "            msg = f\"All cities already captured between {start_date} and {end_date}. Moving to the next dates\\n\\n\"\n", "            print_log(msg)\n", "            # send_to_slack(send_log(msg))\n", "    else:\n", "        msg = f\"No dates remaining between {str(start_date)} and {str(end_date)}\"\n", "        print_log(msg)\n", "        # send_to_slack(send_log(msg))\n", "\n", "    del dates\n", "    gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "01994938-1126-476c-9cdd-5cd4b32f6d73", "metadata": {}, "outputs": [], "source": ["msg = f\"{module} finished !!!\\n\\n\"\n", "print_log(msg)\n", "send_to_slack(send_log(msg))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}