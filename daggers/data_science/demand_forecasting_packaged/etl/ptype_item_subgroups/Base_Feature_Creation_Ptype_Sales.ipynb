{"cells": [{"cell_type": "markdown", "id": "65b3256e-cb37-448b-8ce2-10646dde4d92", "metadata": {}, "source": ["# Ptype Subgroup Sales"]}, {"cell_type": "markdown", "id": "c55d3587-a8c6-4ab7-98c5-68e860a5bb4a", "metadata": {"tags": []}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": null, "id": "7eb5a627-caad-4aff-aefb-a5de012371a3", "metadata": {}, "outputs": [], "source": ["module = \"Ptype Subgroup Sales\""]}, {"cell_type": "code", "execution_count": null, "id": "e99fa2a0-368c-413d-bc23-86147ee8756c", "metadata": {}, "outputs": [], "source": ["!pip install numpy==1.26.4"]}, {"cell_type": "code", "execution_count": null, "id": "77d85f27-d62d-42c1-8c23-a84c15a7da0f", "metadata": {}, "outputs": [], "source": ["import os\n", "import pytz\n", "import pandas as pd\n", "import pencilbox as pb\n", "from pytz import timezone\n", "from functools import reduce\n", "from datetime import datetime, timedelta\n", "from dateutil.relativedelta import relativedelta\n", "import itertools, sys\n", "import numpy as np\n", "import gc\n", "import pdb"]}, {"cell_type": "code", "execution_count": null, "id": "d87858cf-bc16-432c-9a04-c4f150162c43", "metadata": {}, "outputs": [], "source": ["cwd = \"/usr/local/airflow/dags/repo/dags/data_science/demand_forecasting_packaged/etl/ptype_item_subgroups\"\n", "sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "4b94bbdf-033b-42e8-aa8c-d99e07c316e3", "metadata": {}, "outputs": [], "source": ["import support_functions as sf"]}, {"cell_type": "code", "execution_count": null, "id": "f0cf3a20-060c-463f-9224-fbebdace14c2", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_rows\", None)"]}, {"cell_type": "markdown", "id": "811d6f16-d9f8-47da-83d7-029bc525aef0", "metadata": {"tags": []}, "source": ["## Utils"]}, {"cell_type": "code", "execution_count": null, "id": "ec039c3f-b2a4-42c6-a7fc-5ea013ed08c0", "metadata": {}, "outputs": [], "source": ["def send_log(*args, linspace=1):\n", "    return (\n", "        f\"[{module}]<>\"\n", "        + \"[run_ts=\"\n", "        + sf.run_id\n", "        + datetime.now(pytz.timezone(\"Asia/Kolkata\")).strftime(\"[ts=%Y%m%d %H%M%S] --> \")\n", "        + \" \".join(map(str, args))\n", "    )\n", "\n", "\n", "def print_log(*args, linspace=1, task=None, Stage=None, Outcome=None):\n", "    print(\n", "        f\"[{module}]<>\"\n", "        + \"[run_ts=\"\n", "        + sf.run_id\n", "        + datetime.now(pytz.timezone(\"Asia/Kolkata\")).strftime(\"[ts=%Y%m%d %H%M%S] --> \")\n", "        + \" \".join(map(str, args))\n", "    )\n", "\n", "\n", "def send_to_slack(msg):\n", "    try:\n", "        # 1 == 1\n", "        pb.send_slack_message(\n", "            channel=alerts_channel,\n", "            text=msg,\n", "        )\n", "    except Exception as e:\n", "        error_msg = str(e).split(\"\\n\")[0]\n", "        msg = f\"Unable to push message {msg} to slack, got error = {error_msg}\"\n", "        send_log(msg)\n", "\n", "\n", "def sanitise_string(s):\n", "    return \"_\".join(s.replace(\",\", \"\").replace(\"&\", \"\").split())"]}, {"cell_type": "code", "execution_count": null, "id": "7afab4bc-d34e-4791-8cee-9047c9ee4346", "metadata": {}, "outputs": [], "source": ["alerts_channel = \"bl_forecasting_ptype_data_prep_alerts\""]}, {"cell_type": "code", "execution_count": null, "id": "6b7ad76a-3a49-44df-91ea-81df183229cf", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["run_date = datetime.now(timezone(\"Asia/Kolkata\")).date()\n", "yesterday = run_date - relativedelta(days=1)"]}, {"cell_type": "code", "execution_count": null, "id": "1c4f5aeb-5df1-4f24-9601-730d3e0f2dc2", "metadata": {}, "outputs": [], "source": ["run_id = datetime.now() + <PERSON><PERSON><PERSON>(hours=5, minutes=30)\n", "run_id = run_id.replace(microsecond=0)\n", "run_id"]}, {"cell_type": "code", "execution_count": null, "id": "26253430-7e88-43d3-a8a6-b10634935a06", "metadata": {}, "outputs": [], "source": ["if \"000\" not in str(run_id):\n", "    run_id = str(run_id) + \".000\"\n", "run_id"]}, {"cell_type": "code", "execution_count": null, "id": "63004127-f9e6-4dca-906a-760513c444d1", "metadata": {}, "outputs": [], "source": ["msg = f\"\\n\\nCommencing data prep for {module} on {str(run_date)} for {module}\\n\"\n", "print_log(msg)\n", "send_to_slack(send_log(msg))"]}, {"cell_type": "markdown", "id": "93b4f382-b905-4adf-8c06-359750925865", "metadata": {"tags": []}, "source": ["## Ptype Features Queries and Table Schema"]}, {"cell_type": "code", "execution_count": null, "id": "c08c19f9-9a93-4dec-9387-fdd7d90a1442", "metadata": {}, "outputs": [], "source": ["import table_schemas as schema"]}, {"cell_type": "markdown", "id": "54ca6f03-13df-4585-a609-ff31e0470c18", "metadata": {"tags": []}, "source": ["## Execution"]}, {"cell_type": "code", "execution_count": null, "id": "7b274309-a35a-4a88-a1c8-602f609d0d6f", "metadata": {}, "outputs": [], "source": ["# del base_ft_query_dict\n", "try:\n", "    import ptype_feature_queries as pfq\n", "\n", "    print_log(\"Imported queries from support files\")\n", "    base_ft_query_dict = {\n", "        \"ptype_carts\": pfq.ptype_city_sales_query,\n", "        \"ptype_avg_availability\": pfq.ptype_city_availability_query,\n", "        \"ptype_funnel\": pfq.ptype_city_funnel_query,\n", "        \"ptype_searchers\": pfq.ptype_city_searchers_query,\n", "    }\n", "except Exception as e:\n", "    error_msg = str(e).split(\"\\n\")[0]\n", "    msg = f\"Support files couldn't be imported, adding queries from the main notebook, got error = {error_msg}\"\n", "    print_log(msg)\n", "    send_to_slack(send_log(msg))"]}, {"cell_type": "code", "execution_count": null, "id": "71a93535-e42a-4994-999c-324a6aefc0b4", "metadata": {}, "outputs": [], "source": ["list(base_ft_query_dict.keys())"]}, {"cell_type": "code", "execution_count": null, "id": "bf21be20-5a60-42ab-96dd-934ce0affb40", "metadata": {}, "outputs": [], "source": ["_trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "b0201ea8-02b9-45eb-8feb-5548940b69bb", "metadata": {}, "outputs": [], "source": ["full_reset = 0\n", "last_full_refresh_trigger_dt = \"2025-05-28\""]}, {"cell_type": "code", "execution_count": null, "id": "b9b78fa6-49a0-4e9b-a698-5254ba6cf396", "metadata": {"tags": []}, "outputs": [], "source": ["end_date = yesterday\n", "if full_reset > 0:\n", "    sdate = datetime.strptime(\"2022-01-01\", \"%Y-%m-%d\").date()\n", "else:\n", "    sdate = yesterday - relativedel<PERSON>(days=150)\n", "    # sdate = datetime.strptime(\"2022-01-01\", \"%Y-%m-%d\").date()\n", "\n", "dt = sdate\n", "start_dates = []\n", "\n", "while dt <= end_date:\n", "    start_dates.append(dt)\n", "    dt = dt + relativedelta(days=1)\n", "start_dates.reverse()\n", "print_log(len(start_dates))"]}, {"cell_type": "markdown", "id": "71efd11a-ba22-413e-a447-b6003a962d2d", "metadata": {}, "source": ["### Checking for existing dates"]}, {"cell_type": "code", "execution_count": null, "id": "2dcf0536-f1ac-4cae-ac37-5259c8e2dbd2", "metadata": {"tags": []}, "outputs": [], "source": ["msg = f\"Checking for existing city x date combinations for {module}\"\n", "print_log(msg)\n", "send_to_slack(send_log(msg))\n", "if full_reset == 0:\n", "    status_query = \"select city, date, true as is_done from ds_etls.demand_forecasting_ptype_city_daily_sales where city is not null and date is not null and product_type is not null group by 1,2\"\n", "    try:\n", "        done_dates = pd.read_sql(\n", "            status_query,\n", "            _trino_con,\n", "        )\n", "        done_dates[\"date\"] = done_dates[\"date\"].astype(str)\n", "    except Exception as e:\n", "        msg = f\"Could not get dates for existing data on p-type sales, got error = {str(e).split('errorCode')[0]}\"\n", "        print_log(msg)\n", "        send_to_slack(send_log(msg))\n", "        done_dates = pd.DataFrame({\"city\": [\"PAN_India\"], \"date\": [\"1970-01-01\"]})\n", "else:\n", "    status_query = f\"select city, date, true as is_done from ds_etls.demand_forecasting_ptype_city_daily_sales where city is not null and date is not null and snapshot_dt_ist >=  cast('{last_full_refresh_trigger_dt}' as timestamp) and product_type is not null group by 1,2\"\n", "    try:\n", "        done_dates = pd.read_sql(\n", "            status_query,\n", "            _trino_con,\n", "        )\n", "        done_dates[\"date\"] = done_dates[\"date\"].astype(str)\n", "    except Exception as e:\n", "        msg = f\"Could not get dates for existing data on p-type sales, got error = {str(e).split('errorCode')[0]}\"\n", "        print_log(msg)\n", "        send_to_slack(send_log(msg))\n", "        done_dates = pd.DataFrame({\"city\": [\"PAN_India\"], \"date\": [\"1970-01-01\"]})\n", "msg = f\"Number of done city x dates: \\n\\n{done_dates.groupby('city', as_index = False).agg({'date':'nunique'}).sort_values('date', ascending = False).reset_index(drop = True)}\"\n", "print_log(msg)\n", "send_to_slack(send_log(msg))"]}, {"cell_type": "code", "execution_count": null, "id": "f8d43dbd-b6c3-4108-a3ab-71a19ff4eea6", "metadata": {}, "outputs": [], "source": ["msg = f\"Creating data of {module} for {len(start_dates)} days\\n\\n\"\n", "print_log(msg)\n", "send_to_slack(send_log(msg))"]}, {"cell_type": "code", "execution_count": null, "id": "ea212315-2c87-4b73-b41b-9be4544a40ea", "metadata": {}, "outputs": [], "source": ["ft_dts = sorted(start_dates)\n", "\n", "if full_reset > 0:\n", "    date_diff = 6\n", "else:\n", "    date_diff = 1\n", "\n", "sdate_fixed = min(ft_dts)\n", "sdate = sdate_fixed\n", "edate = max(ft_dts)\n", "first_date = sdate_fixed\n", "\n", "date_boundaries = []\n", "\n", "if sdate_fixed == edate:\n", "    date_boundaries.append({\"start_date\": str(sdate), \"end_date\": str(edate)})\n", "else:\n", "    while sdate >= first_date and edate > first_date:\n", "        sdate = edate - relativedelta(days=date_diff)\n", "        batch_dates = {\"start_date\": str(sdate), \"end_date\": str(edate)}\n", "        date_boundaries.append(batch_dates)\n", "        edate = sdate - relativedelta(days=1)\n", "date_boundaries[-1][\"start_date\"] = str(sdate_fixed)\n", "print(date_boundaries[0:3])\n", "# date_boundaries.reverse()\n", "# print(date_boundaries[0:3])"]}, {"cell_type": "markdown", "id": "52a6295e-4906-45b6-87d5-78da18f08f9c", "metadata": {"tags": []}, "source": ["### Pulling p-type sales data for each of the date boundaries"]}, {"cell_type": "code", "execution_count": null, "id": "36be9a0d-dd4c-44bf-ba5d-8bdb26d7d385", "metadata": {"tags": []}, "outputs": [], "source": ["for i in range(len(date_boundaries)):\n", "\n", "    start_date = str(date_boundaries[i][\"start_date\"])\n", "    end_date = str(date_boundaries[i][\"end_date\"])\n", "    start_dates = [\n", "        datetime.strftime(x, \"%Y-%m-%d\") for x in pd.date_range(start_date, end_date, freq=\"d\")\n", "    ]\n", "\n", "    dates = [str(x) for x in ft_dts if (str(x) >= start_date and str(x) <= end_date)]\n", "\n", "    if len(dates) > 0:\n", "        # if len(dates) == 1:\n", "        #     dates = dates + [\"1970-01-01\"]\n", "        # dates = tuple(dates)\n", "\n", "        _trino_con = pb.get_connection(\"[Warehouse] Trino\")\n", "        msg = f\"Getting the list of all cities active between {str(start_date)} and {str(end_date)}\"\n", "        print_log(msg)\n", "        send_to_slack(send_log(msg))\n", "\n", "        all_cities = pd.read_sql(\n", "            pfq.city_list_query.format(start_date=start_date, end_date=end_date),\n", "            con=_trino_con,\n", "        ).city.to_list()\n", "\n", "        if len(all_cities) > 0:\n", "            all_cities = sorted(list(set(all_cities)))\n", "            all_city_dates = pd.DataFrame(itertools.product(all_cities, dates))\n", "            all_city_dates.columns = [\"city\", \"date\"]\n", "            all_city_dates[\"date\"] = all_city_dates[\"date\"].astype(str)\n", "\n", "            all_city_dates = all_city_dates.merge(done_dates, on=[\"city\", \"date\"], how=\"left\")\n", "            all_city_dates_full = all_city_dates.copy()\n", "            all_city_dates = all_city_dates[~(all_city_dates.is_done == True)]\n", "        else:\n", "            msg = f\"Unable to find any cities between {start_date} and {end_date} dates, moving on\\n\\n\"\n", "            print_log(msg)\n", "            send_to_slack(send_log(msg))\n", "\n", "        if len(all_city_dates) > 0:\n", "\n", "            all_cities = sorted(list(all_city_dates.city.unique()))\n", "            all_cities_full = sorted(list(all_city_dates_full.city.unique()))\n", "            dates = [x for x in dates if str(x) in all_city_dates.date.astype(str).unique()]\n", "\n", "            msg = f\"Found {len(all_cities)} cities and {len(dates)} days between {str(start_date)} and {str(end_date)}.\\n\\nAll cities : \\n{all_cities}\\n\\nBUT : Generating data for all {len(all_cities_full)} cities and pushing to DB via direct query\\n\"\n", "            print_log(msg)\n", "            send_to_slack(send_log(msg))\n", "\n", "            # if len(dates) == 1:\n", "            #     dates = dates + [\"1970-01-01\"]\n", "\n", "            required_dates = \", \".join([f\"date('{date}')\" for date in dates])\n", "            required_dates_str = \", \".join([f\"'{date}'\" for date in dates])\n", "\n", "            if len(all_cities) > 1:\n", "                city_list = tuple(all_cities_full)\n", "            else:\n", "                city_list = tuple(all_cities_full + [\"\"])\n", "\n", "            del all_cities, all_cities_full\n", "            gc.collect()\n", "\n", "            try:\n", "                _trino_con = pb.get_connection(\"[Warehouse] Trino\")\n", "                pb.to_trino(\n", "                    pfq.ptype_city_sales_query.format(\n", "                        start_date=start_date,\n", "                        end_date=end_date,\n", "                        specific_dates=required_dates,\n", "                        city_list=city_list,\n", "                    ),\n", "                    **schema.ptype_table_schema_sales,\n", "                )\n", "                msg = f\"Pushed the data between dates : {str(start_date)} and {str(end_date)} to DB\"\n", "                send_to_slack(send_log(msg))\n", "                print_log(msg)\n", "\n", "            except Exception as e:\n", "                error_msg = str(e).split(\"errorCode\")[0]\n", "                msg = f\"Failed to upload data between {str(start_date)} and {str(end_date)} dates to DB, \\ngot error = \\n\\n{error_msg}\\n\"\n", "                print_log(msg)\n", "                send_to_slack(send_log(msg))\n", "                if any(\n", "                    [\n", "                        x in error_msg.lower()\n", "                        for x in [\"reset\", \"aborted\", \"error 502\", \"code 502\", \"bad gateway\"]\n", "                    ]\n", "                ):\n", "                    msg = \"Establishing DB connection again and re-trying\"\n", "                    print_log(msg)\n", "                    send_to_slack(send_log(msg))\n", "                    try:\n", "                        _trino_con = pb.get_connection(\"[Warehouse] Trino\")\n", "                        pb.to_trino(\n", "                            pfq.ptype_city_sales_query.format(\n", "                                start_date=start_date,\n", "                                end_date=end_date,\n", "                                specific_dates=required_dates,\n", "                                city_list=city_list,\n", "                            ),\n", "                            **schema.ptype_table_schema_sales,\n", "                        )\n", "                        msg = f\"Pushed the data between dates : {str(start_date)} and {str(end_date)} to DB\"\n", "                        send_to_slack(send_log(msg))\n", "                        print_log(msg)\n", "                    except Exception as e:\n", "                        error_msg = str(e).split(\"errorCode\")[0]\n", "                        msg = f\"Failed to upload data between {str(start_date)} and {str(end_date)} dates to DB, \\ngot error = \\n\\n{error_msg}\\n\"\n", "                        print_log(msg)\n", "                        send_to_slack(send_log(msg))\n", "\n", "                else:\n", "                    msg = f\"Unable to fill data between {str(start_date)} and {str(end_date)}, MOVING ON\"\n", "                    print_log(msg)\n", "                    send_to_slack(send_log(msg))\n", "                    continue\n", "            msg = f\"Done with all cities\"\n", "            print_log(msg)\n", "            send_to_slack(send_log(msg))\n", "        else:\n", "            msg = f\"All cities already captured between {start_date} and {end_date}. Moving to the next dates\\n\\n\"\n", "            print_log(msg)\n", "            # send_to_slack(send_log(msg))\n", "    else:\n", "        msg = f\"No dates remaining between {str(start_date)} and {str(end_date)}\"\n", "        print_log(msg)\n", "        send_to_slack(send_log(msg))\n", "\n", "    del dates\n", "    gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "4a668a9a-d32d-4a70-80a9-726293319bf5", "metadata": {}, "outputs": [], "source": ["all_city_dates"]}, {"cell_type": "code", "execution_count": null, "id": "5f655b88-06fe-4808-9e47-55f4d6dd2b29", "metadata": {}, "outputs": [], "source": ["msg = f\"{module} finished !!!\\n\\n\"\n", "print_log(msg)\n", "send_to_slack(send_log(msg))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}