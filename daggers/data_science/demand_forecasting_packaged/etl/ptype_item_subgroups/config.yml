alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 5
dag_name: ptype_item_subgroups
dag_type: etl
escalation_priority: low
execution_timeout: 4320
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebooks:
- alias: Base_Feature_Creation_Ptype_Availability
  executor_config:
    load_type: tiny
    node_type: spot
  name: Base_Feature_Creation_Ptype_Availability
  parameters: {}
  tag: group_1
- alias: Base_Feature_Creation_Ptype_Sales
  executor_config:
    load_type: tiny
    node_type: spot
  name: Base_Feature_Creation_Ptype_Sales
  parameters: {}
  tag: group_1
- alias: Base_Feature_Creation_Ptype_Funnel
  executor_config:
    load_type: tiny
    node_type: spot
  name: Base_Feature_Creation_Ptype_Funnel
  parameters: {}
  tag: group_1
- alias: Base_Feature_Creation_Ptype_Searchers
  executor_config:
    load_type: tiny
    node_type: spot
  name: Base_Feature_Creation_Ptype_Searchers
  parameters: {}
  tag: group_1
owner:
  email: <EMAIL>
  slack_id: U066FKJM8KC
path: data_science/demand_forecasting_packaged/etl/ptype_item_subgroups
paused: false
pool: data_science_pool
project_name: demand_forecasting_packaged
schedule:
  end_date: '2025-08-02T00:00:00'
  interval: 00 1 * * *
  start_date: '2025-06-24T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- ptype_feature_queries.py
- Rakhi_item_types.yaml
- table_schemas.py
- support_functions.py
tags: []
template_name: multi_notebook
version: 5
