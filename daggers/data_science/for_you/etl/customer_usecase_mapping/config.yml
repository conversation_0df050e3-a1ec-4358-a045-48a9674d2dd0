alert_configs:
  slack:
  - channel: bl-personalization-notifications
  - channel: bl-personalisation-dag-failures
concurrency: 5
dag_name: customer_usecase_mapping
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebooks:
- executor_config:
    load_type: medium
    node_type: spot
  name: usecase_keyterm_pid_mapping
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level1
- executor_config:
    load_type: very-high-mem
    node_type: spot
  name: customer_usecase_mapping_mod_4
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level2
owner:
  email: <EMAIL>
  slack_id: U03SJ8DPFND
path: data_science/for_you/etl/customer_usecase_mapping
paused: false
pool: data_science_pool
project_name: for_you
schedule:
  end_date: '2025-08-27T00:00:00'
  interval: 30 22 * * *
  start_date: '2025-06-03T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- queries/*
- schema_definition.py
tags: []
template_name: multi_notebook
version: 4
