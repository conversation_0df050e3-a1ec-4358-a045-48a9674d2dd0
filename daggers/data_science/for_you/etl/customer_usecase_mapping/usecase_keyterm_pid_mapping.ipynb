{"cells": [{"cell_type": "code", "execution_count": null, "id": "62edafef-af80-4c7c-ba11-c885304da15e", "metadata": {}, "outputs": [], "source": ["!pip install openpyxl"]}, {"cell_type": "code", "execution_count": null, "id": "833eef12-7a36-47ee-8190-f4838922f05f", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys"]}, {"cell_type": "code", "execution_count": null, "id": "9b1fa795-34f7-4ff4-94a7-c168de34215e", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "38fb7ed8-393d-4608-a7dd-fc5f0073dbdf", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "449a41b4-cbc8-40cb-91ff-ea62b109d050", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import ast"]}, {"cell_type": "code", "execution_count": null, "id": "49ff8d0d-3065-4344-b08e-33871d644408", "metadata": {}, "outputs": [], "source": ["trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "bd1390fb-e9dc-413c-8702-73c3482841b5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "9a178694-4450-4ef8-873e-02c751e91c62", "metadata": {}, "source": ["## PID to Keyterm mapping"]}, {"cell_type": "code", "execution_count": null, "id": "b5b0dbec-47fa-4d20-8cdd-2f1e80139f40", "metadata": {}, "outputs": [], "source": ["pid_to_keyterm_mapping_query = f\"\"\"\n", "    \n", "    with max_dt as (\n", "      select\n", "        metadata_id,\n", "        metadata_type,\n", "        max(snapshot_ist) max_snapshot_ist\n", "      from\n", "        search_etls.meta_entity_product_relationship\n", "      where\n", "        snapshot_ist is not null\n", "        and metadata_type = 'KEYTERM'\n", "      group by 1,2\n", "    ),\n", "    \n", "    sales as (\n", "      select\n", "        product_id,\n", "        sum(total_selling_price) as pid_sales,\n", "        sum(product_quantity) as pid_qty,\n", "        count(distinct cart_id) as pid_carts\n", "      from\n", "        dwh.fact_sales_order_item_details o\n", "      WHERE\n", "        order_create_dt_ist > current_date - INTERVAL '60' DAY\n", "        AND order_create_dt_ist <= current_date - INTERVAL '1' DAY\n", "        AND o.city_name != ''\n", "        AND o.city_name is not null\n", "        AND o.dim_customer_key IS NOT NULL\n", "        AND o.order_current_status = 'DELIVERED'\n", "      group by\n", "        1\n", "    ),\n", "    \n", "    metadata_agg as (\n", "      select\n", "        p.metadata_id,\n", "        p.metadata_name,\n", "        p.metadata_type,\n", "        p.tagged_product_id as product_id,\n", "        product_name,\n", "        l0_category, \n", "        l0_category_id\n", "      from\n", "        search_etls.meta_entity_product_relationship p\n", "        join max_dt m on m.metadata_id = p.metadata_id\n", "        and m.max_snapshot_ist = p.snapshot_ist\n", "        and m.metadata_type = p.metadata_type\n", "        join dwh.dim_product dp on dp.product_id = p.tagged_product_id\n", "        and dp.is_current\n", "        and dp.is_product_enabled\n", "      where\n", "        p.snapshot_ist is not null\n", "      group by 1,2,3,4,5,6,7\n", "    )\n", "    \n", "    select\n", "      a.metadata_id,\n", "      a.metadata_name,\n", "      a.metadata_type,\n", "      a.product_id,\n", "      COALESCE(b.pid_sales,0) as pid_sales,\n", "      COALESCE(b.pid_qty,0) as pid_qty,\n", "      COALESCE(b.pid_carts,0) as pid_carts\n", "    from\n", "      metadata_agg a\n", "      left join sales b on b.product_id = a.product_id\n", "      \n", "    \"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "3347c61e-0c6a-4872-83fe-07a577c77d08", "metadata": {}, "outputs": [], "source": ["keyterms_data = pd.read_sql(pid_to_keyterm_mapping_query, con=trino_con)"]}, {"cell_type": "code", "execution_count": null, "id": "055806b3-2f84-4a81-9766-d789491f94b4", "metadata": {}, "outputs": [], "source": ["keyterms_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "acc201a6-82b3-48e1-bae0-18266cc45c51", "metadata": {}, "outputs": [], "source": ["keyterms_data.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "0e2a5275-4201-4e97-9018-dee1db84db5e", "metadata": {}, "outputs": [], "source": ["pid_keyterm_mapping_table_name = \"personalisation_for_you_pid_keyterm_mapping\"\n", "kwargs_pid_keyterm_mapping_tb = {\n", "    \"schema_name\": \"consumer_intelligence_etls\",\n", "    \"table_name\": pid_keyterm_mapping_table_name,\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"metadata_id\",\n", "            \"type\": \"bigint\",\n", "            \"description\": \"metadata_id\",\n", "        },\n", "        {\n", "            \"name\": \"metadata_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"metadata_name\",\n", "        },\n", "        {\n", "            \"name\": \"metadata_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"metadata_type\",\n", "        },\n", "        {\n", "            \"name\": \"product_id\",\n", "            \"type\": \"bigint\",\n", "            \"description\": \"product_id\",\n", "        },\n", "        {\n", "            \"name\": \"pid_sales\",\n", "            \"type\": \"real\",\n", "            \"description\": \"pid_sales\",\n", "        },\n", "        {\n", "            \"name\": \"pid_qty\",\n", "            \"type\": \"bigint\",\n", "            \"description\": \"pid_qty\",\n", "        },\n", "        {\n", "            \"name\": \"pid_carts\",\n", "            \"type\": \"bigint\",\n", "            \"description\": \"pid_carts\",\n", "        },\n", "    ],\n", "    # \"primary_key\": [],\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"Intermediate table for storing pid to keyterm mapping for personalisation usecase\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "f49d9c8c-8899-48a1-9edc-e1983f440649", "metadata": {}, "outputs": [], "source": ["pb.to_trino(keyterms_data, **kwargs_pid_keyterm_mapping_tb)"]}, {"cell_type": "code", "execution_count": null, "id": "9d2169d1-3a17-4076-b6b1-38e674c40bcc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "68fa97a1-acf3-426e-9377-51922c31a888", "metadata": {}, "source": ["## Creating table for usecase to entity mapping"]}, {"cell_type": "code", "execution_count": null, "id": "6bfabbd9-4186-4ed1-9394-3811bf5a4e5c", "metadata": {}, "outputs": [], "source": ["# use_cases = pd.read_csv(\"usecases_selected_clusters.csv\")\n", "use_cases = pb.from_sheets(\"1iexXmObcahsdqQyIDKakBPlZ6cTmBRddgR3QEIeKE0I\", sheetname=\"Sheet2\")"]}, {"cell_type": "code", "execution_count": null, "id": "7e1cf663-ba8a-42fb-a50b-66448acd2edf", "metadata": {}, "outputs": [], "source": ["use_cases.shape"]}, {"cell_type": "code", "execution_count": null, "id": "bbe70b75-ab08-4e54-aef9-f015738aaa67", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["use_cases = use_cases[[\"new_name\", \"original_item\", \"primary_items\", \"l0\", \"is_personalised\"]]\n", "use_cases = use_cases.rename(\n", "    columns={\n", "        \"new_name\": \"cluster_theme\",\n", "        \"original_item\": \"metadata_name\",\n", "        \"l0\": \"cluster_category\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fde1006d-01cc-4252-a9b8-fda394d283f3", "metadata": {}, "outputs": [], "source": ["use_cases.iloc[0][\"metadata_name\"]"]}, {"cell_type": "code", "execution_count": null, "id": "c770286a-a93e-4ca0-88ec-cb545b322dc0", "metadata": {}, "outputs": [], "source": ["use_cases.iloc[0][\"primary_items\"]"]}, {"cell_type": "code", "execution_count": null, "id": "17243250-d643-4efb-9c35-831c7d18b4e6", "metadata": {}, "outputs": [], "source": ["use_cases[\"metadata_name\"] = use_cases[\"metadata_name\"].apply(lambda x: ast.literal_eval(x))\n", "use_cases[\"primary_items\"] = use_cases[\"primary_items\"].apply(lambda x: ast.literal_eval(x))"]}, {"cell_type": "code", "execution_count": null, "id": "7c7818b4-d2fa-4478-a850-92d8a944fa35", "metadata": {}, "outputs": [], "source": ["use_cases[\"is_personalised\"] = use_cases[\"is_personalised\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "dd0966dd-d62b-4678-a8c0-7010a483d172", "metadata": {}, "outputs": [], "source": ["use_cases.iloc[0][\"metadata_name\"]"]}, {"cell_type": "code", "execution_count": null, "id": "791f225f-b489-4069-be2d-ad946e1518e7", "metadata": {}, "outputs": [], "source": ["use_cases.iloc[0][\"primary_items\"]"]}, {"cell_type": "code", "execution_count": null, "id": "3276e843-01c9-4de8-861e-f13d169f69e3", "metadata": {}, "outputs": [], "source": ["use_cases.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a7e36d52-6916-405e-8ab6-d18e78d4e5c8", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["use_cases = use_cases.explode(\"metadata_name\")"]}, {"cell_type": "code", "execution_count": null, "id": "17708703-45ec-4fca-a8a9-b0bed75f4157", "metadata": {}, "outputs": [], "source": ["use_cases[\"is_primary\"] = use_cases.apply(\n", "    lambda x: 1 if x[\"metadata_name\"] in x[\"primary_items\"] else 0, axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "28c8b1b1-8317-430d-9790-8e3ea6475b52", "metadata": {}, "outputs": [], "source": ["use_cases = use_cases.drop(\"primary_items\", axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "234616fd-2198-49bf-a613-bef499365e1c", "metadata": {}, "outputs": [], "source": ["use_cases.shape"]}, {"cell_type": "code", "execution_count": null, "id": "bff279d7-d53a-4be3-9cf3-494cea2d7226", "metadata": {}, "outputs": [], "source": ["use_cases.head()"]}, {"cell_type": "code", "execution_count": null, "id": "156d511f-2cc7-43a8-8c40-ac7876ae6936", "metadata": {}, "outputs": [], "source": ["use_cases.drop_duplicates().shape"]}, {"cell_type": "code", "execution_count": null, "id": "10d2b3a0-ced3-494b-9679-66978f1c54e2", "metadata": {}, "outputs": [], "source": ["use_cases = use_cases.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "148cec0e-c536-4da5-9259-c169ab0b6cf3", "metadata": {}, "outputs": [], "source": ["use_cases.shape"]}, {"cell_type": "code", "execution_count": null, "id": "4628fc2e-bf3e-4ba4-b918-f65cbeedf983", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["use_cases = use_cases.merge(keyterms_data, on=\"metadata_name\", how=\"left\")[\n", "    [\n", "        \"cluster_theme\",\n", "        \"metadata_name\",\n", "        \"metadata_id\",\n", "        \"is_primary\",\n", "        \"cluster_category\",\n", "        \"is_personalised\",\n", "    ]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "19236997-9d66-4ca8-bbfe-312bf3523476", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["use_cases.shape"]}, {"cell_type": "code", "execution_count": null, "id": "5f291a96-b5f9-4fa7-a71b-c4102713030a", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["use_cases.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "457b837b-acf8-478b-8f9d-a011256c8cff", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["use_cases[use_cases[\"metadata_id\"].isna()]"]}, {"cell_type": "code", "execution_count": null, "id": "6a1878f5-798e-4108-8ce5-9e81517b63fd", "metadata": {}, "outputs": [], "source": ["use_cases = use_cases[~use_cases[\"metadata_id\"].isna()]"]}, {"cell_type": "code", "execution_count": null, "id": "3763c189-302c-488d-91e7-210a79014a56", "metadata": {}, "outputs": [], "source": ["use_cases.shape"]}, {"cell_type": "code", "execution_count": null, "id": "696ddf96-76cf-4d75-bdfe-42c7f46dace6", "metadata": {}, "outputs": [], "source": ["use_cases.drop_duplicates().shape"]}, {"cell_type": "code", "execution_count": null, "id": "b377e2d3-148a-4a19-b1da-a6a1954e39db", "metadata": {}, "outputs": [], "source": ["use_cases[\"cluster_theme\"].nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "aa481187-b151-40e2-b108-7a13b3d83bc1", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["use_cases[\"metadata_id\"] = use_cases[\"metadata_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "931af275-8e98-4d99-8cae-4c2d11920916", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["use_cases.head()"]}, {"cell_type": "code", "execution_count": null, "id": "26fa15b5-c378-4939-9716-88d4fdc0a574", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1c1846e1-abd1-49d4-8c31-22b5fe2fa884", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["## mapping candidate pids\n", "topn = 5\n", "candidate_pids = (\n", "    use_cases.merge(keyterms_data, on=[\"metadata_id\", \"metadata_name\"], how=\"left\")\n", "    .query(\"is_primary==1\")\n", "    .sort_values([\"cluster_theme\", \"pid_carts\"], ascending=[True, False])\n", "    .drop_duplicates(subset=[\"cluster_theme\", \"product_id\"], keep=\"first\")\n", ")\n", "candidate_pids[\"keyterm_slot\"] = (\n", "    candidate_pids.groupby([\"cluster_theme\", \"metadata_name\", \"metadata_id\"]).cumcount() + 1\n", ")\n", "candidate_pids = (\n", "    candidate_pids.sort_values(\n", "        [\"cluster_theme\", \"keyterm_slot\", \"pid_carts\"], ascending=[True, True, False]\n", "    )\n", "    .groupby([\"cluster_theme\"])\n", "    .head(topn)\n", ")\n", "candidate_pids = candidate_pids.groupby([\"cluster_theme\"]).agg({\"product_id\": list}).reset_index()\n", "candidate_pids = candidate_pids.rename(columns={\"product_id\": \"candidate_pids\"})\n", "candidate_pids[\"len\"] = candidate_pids[\"candidate_pids\"].apply(lambda x: len(x))\n", "print(candidate_pids.shape)\n", "print(use_cases[\"cluster_theme\"].nunique())\n", "print(candidate_pids[\"len\"].unique())\n", "assert candidate_pids.shape[0] == use_cases[\"cluster_theme\"].nunique()\n", "use_cases = use_cases.merge(\n", "    candidate_pids[[\"cluster_theme\", \"candidate_pids\"]], on=\"cluster_theme\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "51fcc241-7ee5-4f38-9db0-03111074bff2", "metadata": {}, "outputs": [], "source": ["use_cases.shape"]}, {"cell_type": "code", "execution_count": null, "id": "ea640395-47a1-423f-a600-9a167613fe5d", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["use_cases.head()"]}, {"cell_type": "code", "execution_count": null, "id": "94196415-d171-437f-9470-f9ae72a3406d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f64d533c-b446-4b5b-8106-65611145033c", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["usecase_entity_mapping_table_name = \"personalisation_for_you_usecase_entity_mapping\"\n", "kwargs_usecase_entity_mapping_tb = {\n", "    \"schema_name\": \"consumer_intelligence_etls\",\n", "    \"table_name\": usecase_entity_mapping_table_name,\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"cluster_theme\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"cluster_theme\",\n", "        },\n", "        {\n", "            \"name\": \"metadata_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"metadata_name\",\n", "        },\n", "        {\n", "            \"name\": \"metadata_id\",\n", "            \"type\": \"bigint\",\n", "            \"description\": \"metadata_id\",\n", "        },\n", "        {\n", "            \"name\": \"is_primary\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"is_primary\",\n", "        },\n", "        {\n", "            \"name\": \"cluster_category\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"cluster_category\",\n", "        },\n", "        {\n", "            \"name\": \"is_personalised\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"is_personalised\",\n", "        },\n", "        {\n", "            \"name\": \"candidate_pids\",\n", "            \"type\": \"ARRAY(BIGINT)\",\n", "            \"description\": \"candidate_pids\",\n", "        },\n", "    ],\n", "    # \"primary_key\": [],\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"Intermediate table for storing usecase to entity mapping for personalisation usecase\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "699e4524-ad83-41fc-9ffa-11bedb57612e", "metadata": {}, "outputs": [], "source": ["pb.to_trino(use_cases, **kwargs_usecase_entity_mapping_tb)"]}, {"cell_type": "code", "execution_count": null, "id": "e0090d6c-fb21-42db-acd6-fe35f983d746", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "68cf0ed0-15a1-4fb7-9736-b149f9546298", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5205d04e-22b7-461f-a211-ac9cee2e4c0c", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b8d5f49e-3b08-4e5a-af42-f6f0ce22d1a6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "001f20b6-27d1-43a8-a7aa-998a3d58c79a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "84bed7c5-15c2-4e8a-b0e4-303caff01c9b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}