{"cells": [{"cell_type": "code", "execution_count": null, "id": "a5a349c8-f865-4533-bbb1-5d64fdf1ceca", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["import os\n", "import sys"]}, {"cell_type": "code", "execution_count": null, "id": "cc2acaee-a503-4814-9b78-0a7f2ffb7d8d", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "bd70912d-0a03-4cc2-8ebe-7a26bc16144e", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "f1aa7c77-f15f-41c6-a1a5-a295d402ce35", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import ast\n", "import gc"]}, {"cell_type": "code", "execution_count": null, "id": "5174ca77-45b9-41bd-b332-dd8170649f43", "metadata": {}, "outputs": [], "source": ["from schema_definition import *"]}, {"cell_type": "code", "execution_count": null, "id": "7eaf3dd3-578d-4c55-920c-507e16f2ac9d", "metadata": {}, "outputs": [], "source": ["trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "1ec657b5-c4f9-464a-8cad-9b18f7fc3219", "metadata": {}, "outputs": [], "source": ["def read_sql_query(path):\n", "    with open(path) as f:\n", "        query = f.read()\n", "    return query"]}, {"cell_type": "code", "execution_count": null, "id": "2d29d957-b33b-46a4-aae5-4251715c46d4", "metadata": {}, "outputs": [], "source": ["mod_value = 2"]}, {"cell_type": "code", "execution_count": null, "id": "517c3307-8d9b-4143-8eb7-1ed1eec45671", "metadata": {}, "outputs": [], "source": ["pb.to_trino(\n", "    read_sql_query(f\"{cwd}/queries/fetch_customer_usecase_mapping.sql\").format(mod_value=mod_value),\n", "    **get_customer_usecase_mapping_schema_interim(mod_value=mod_value),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "774c2e0f-8847-4ec9-bad3-0b1d3cf71bd9", "metadata": {}, "outputs": [], "source": ["tmp_customer_usecase_mapping = pd.read_sql(\n", "    f\"\"\"\n", "    select dim_customer_key, \n", "           cluster_theme \n", "    from consumer_intelligence_etls.personalisation_for_you_cust_usecase_map_interim_mod_{mod_value}\n", "    group by 1,2\n", "\"\"\",\n", "    con=trino_con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7706499e-569d-444f-88db-dfb2cdfc3f54", "metadata": {}, "outputs": [], "source": ["tmp_customer_usecase_mapping.shape"]}, {"cell_type": "code", "execution_count": null, "id": "7d497ff2-ba87-4247-88f5-4efcbf477d05", "metadata": {}, "outputs": [], "source": ["tmp_customer_usecase_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "91359858-ce6b-4169-8975-d3400e5a2f8e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "2f76ba9d-bdb0-486f-9417-f31a0d0a7dcf", "metadata": {}, "source": ["## Mapping critical pids"]}, {"cell_type": "code", "execution_count": null, "id": "733b0c39-cd81-4222-8f7c-30ba1efa7230", "metadata": {}, "outputs": [], "source": ["usecase_entity_mapping = pd.read_sql(\n", "    f\"\"\"\n", "    select * from consumer_intelligence_etls.personalisation_for_you_usecase_entity_mapping\n", "\"\"\",\n", "    con=trino_con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9bf0b58e-a094-439e-8e16-97c528271762", "metadata": {}, "outputs": [], "source": ["usecase_entity_mapping.shape"]}, {"cell_type": "code", "execution_count": null, "id": "cb8e5a36-03b7-48a5-a3df-7d01e5bcafca", "metadata": {}, "outputs": [], "source": ["usecase_entity_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "dafe1c52-9971-471b-9f2f-e1e7f656afd1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5fc05243-bf91-47be-9ea5-b0491d7b1f63", "metadata": {}, "outputs": [], "source": ["keyterm_pid_mapping = pd.read_sql(\n", "    f\"\"\"\n", "    select * from consumer_intelligence_etls.personalisation_for_you_pid_keyterm_mapping\n", "\"\"\",\n", "    con=trino_con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "05ee3c39-0fdd-4a7e-9bab-4c2d8d77041d", "metadata": {}, "outputs": [], "source": ["keyterm_pid_mapping.shape"]}, {"cell_type": "code", "execution_count": null, "id": "0bdeab3c-423b-4e19-8fda-24c0b53fb8e4", "metadata": {}, "outputs": [], "source": ["keyterm_pid_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8f3f77e9-d801-4a18-bd49-1e523759f956", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "84b13f61-8790-4fea-b288-4e5e1d2a7227", "metadata": {}, "outputs": [], "source": ["pid_group_id_mapping = pd.read_sql(\n", "    f\"\"\"\n", "    select product_id, group_id from consumer_intelligence_etls.personalisation_prod_meta_table_v2 group by 1,2\n", "\"\"\",\n", "    con=trino_con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fffd0c5e-8344-43e8-8e81-6abf9deb8668", "metadata": {}, "outputs": [], "source": ["pid_group_id_mapping.shape"]}, {"cell_type": "code", "execution_count": null, "id": "6cfead12-985d-47d8-ad84-f8fbf3e344d7", "metadata": {}, "outputs": [], "source": ["pid_group_id_mapping.product_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "11ede88b-a5c1-4a6b-9a37-2dabe72fe707", "metadata": {}, "outputs": [], "source": ["pid_group_id_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "87f9f855-ed9e-4ac8-9b37-5884870054f2", "metadata": {}, "outputs": [], "source": ["pid_group_id_mapping = pid_group_id_mapping.set_index(\"product_id\").T.to_dict(orient=\"records\")[0]"]}, {"cell_type": "code", "execution_count": null, "id": "14c5a791-a742-4148-9e34-2f157cbfd672", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["len(pid_group_id_mapping)"]}, {"cell_type": "code", "execution_count": null, "id": "05a36e26-d5dd-4a0e-a903-71203099090c", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "faf92335-02eb-4479-83fc-c320a3d8fc9e", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["prod_emb = pd.read_sql(\n", "    f\"\"\"\n", "    select * from consumer_intelligence_etls.personalisation_ranking_prod_emb\n", "\"\"\",\n", "    con=trino_con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "44e97307-9293-4990-8c90-3727a0a7512b", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["prod_emb.shape"]}, {"cell_type": "code", "execution_count": null, "id": "1a813ce4-7598-4601-acf0-38d059092fbe", "metadata": {}, "outputs": [], "source": ["prod_emb.product_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "72d5bd7f-be43-4b9b-bdd6-26bd695e5e2a", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["prod_emb[\"group_id\"] = prod_emb[\"product_id\"].map(pid_group_id_mapping)"]}, {"cell_type": "code", "execution_count": null, "id": "07d3811a-4562-4ba6-a33d-e9018319fa67", "metadata": {}, "outputs": [], "source": ["prod_emb.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "071746db-2a78-4eaf-a50e-a9930dea644b", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["prod_emb[prod_emb[\"group_id\"].isna()]"]}, {"cell_type": "code", "execution_count": null, "id": "3bb93c96-211e-46fa-86c7-b7fb62f90f6d", "metadata": {}, "outputs": [], "source": ["prod_emb = prod_emb[~prod_emb[\"group_id\"].isna()]"]}, {"cell_type": "code", "execution_count": null, "id": "988c14ed-99e8-4c56-9cf9-9bbbd1643a41", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["prod_emb.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "1264b059-3882-4dcc-be8d-a05234e1d3ab", "metadata": {}, "outputs": [], "source": ["prod_emb[\"group_id\"] = prod_emb[\"group_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "c075f350-ea83-4bd9-a50a-31cba639b3bd", "metadata": {}, "outputs": [], "source": ["prod_emb.shape"]}, {"cell_type": "code", "execution_count": null, "id": "ba44f97f-6920-4691-b9fb-25041d0335ef", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["prod_emb = prod_emb.merge(\n", "    keyterm_pid_mapping[[\"product_id\", \"pid_carts\"]].drop_duplicates(), on=\"product_id\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3088b325-6798-43e4-9c9e-d1d39f5739c5", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["prod_emb[\"pid_carts\"] = prod_emb[\"pid_carts\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "5ba91ec1-0a80-43e2-869d-0b9a2cc0fedc", "metadata": {}, "outputs": [], "source": ["prod_emb.head()"]}, {"cell_type": "code", "execution_count": null, "id": "986b4eda-12c1-4a6f-a09d-6d1a9e5fa0b8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d6fc1d61-c287-4f83-8776-f71d8fa41351", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["# group id to pid map\n", "group_id_pid_map = (\n", "    prod_emb.sort_values([\"group_id\", \"pid_carts\", \"product_id\"], ascending=[True, False, True])\n", "    .drop_duplicates(subset=[\"group_id\"], keep=\"first\")[[\"group_id\", \"product_id\"]]\n", "    .drop_duplicates()\n", "    .set_index(\"group_id\")\n", "    .T.to_dict(orient=\"records\")[0]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "58034072-5e84-49f3-bd2f-11cdfd8decc3", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["len(group_id_pid_map)"]}, {"cell_type": "code", "execution_count": null, "id": "f5572bf9-3973-46a9-a52e-e8a012bacfbe", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "32f0297a-1e00-4e5e-b81d-056d6437187b", "metadata": {}, "outputs": [], "source": ["user_emb = pd.read_sql(\n", "    f\"\"\"\n", "\n", "    WITH user_base AS (\n", "        SELECT DISTINCT CAST(dim_customer_key AS VARCHAR) AS dim_customer_key\n", "        FROM consumer_intelligence_etls.personalisation_for_you_base_data_tmp\n", "        WHERE dim_customer_key%%10={mod_value}\n", "    )\n", "    \n", "    SELECT emb.entity_id, \n", "           JSON_EXTRACT(feature_ctx_value, '$.user_embedding') AS user_embedding\n", "    FROM feature_store.user_user_personalised_emb_lightfm_1_0_0 emb\n", "    JOIN user_base ub ON emb.entity_id = ub.dim_customer_key\n", "    WHERE JSON_ARRAY_LENGTH(JSON_EXTRACT(feature_ctx_value, '$.user_embedding')) > 0\n", "    \n", "\"\"\",\n", "    con=trino_con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "549b20ec-bc49-4810-916a-8e87ce96c367", "metadata": {}, "outputs": [], "source": ["user_emb.shape"]}, {"cell_type": "code", "execution_count": null, "id": "30f8c145-6790-4edb-98d8-ee223c62a125", "metadata": {}, "outputs": [], "source": ["user_emb[\"entity_id\"].nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "05a56c9e-6318-4b09-a6af-660b92114956", "metadata": {}, "outputs": [], "source": ["user_emb[\"user_embedding\"] = user_emb[\"user_embedding\"].apply(lambda x: ast.literal_eval(x))"]}, {"cell_type": "code", "execution_count": null, "id": "dc4ed09e-aa62-441e-8c9b-b0daf584526b", "metadata": {}, "outputs": [], "source": ["user_emb[\"entity_id\"] = user_emb[\"entity_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "b2cf2da4-1bdc-4b11-a66a-a4f959654f49", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["user_emb.head()"]}, {"cell_type": "code", "execution_count": null, "id": "41da8b3d-1c55-432b-9a70-07021185fa8c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0a78430d-9408-441e-89e9-7e5b8cf2e26e", "metadata": {}, "outputs": [], "source": ["## pids within cluster\n", "\n", "usecase_keyterm_pid_mapping = usecase_entity_mapping.merge(\n", "    keyterm_pid_mapping[[\"metadata_id\", \"metadata_name\", \"product_id\"]].drop_duplicates(),\n", "    on=[\"metadata_id\", \"metadata_name\"],\n", "    how=\"left\",\n", ")\n", "\n", "pid_within_cluster = (\n", "    usecase_keyterm_pid_mapping[[\"cluster_theme\", \"is_personalised\", \"product_id\"]]\n", "    .drop_duplicates()\n", "    .groupby([\"cluster_theme\"])\n", "    .agg({\"is_personalised\": \"max\", \"product_id\": list})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f2f9d3c6-5623-4887-b4b7-2203e72d278b", "metadata": {}, "outputs": [], "source": ["pid_within_cluster.shape"]}, {"cell_type": "code", "execution_count": null, "id": "90bc1c50-3ec6-4be9-9524-31546564523b", "metadata": {}, "outputs": [], "source": ["pid_within_cluster = pid_within_cluster[pid_within_cluster[\"is_personalised\"] == 1]"]}, {"cell_type": "code", "execution_count": null, "id": "609392e6-0c11-48fc-a989-92d2dbb2c2fe", "metadata": {}, "outputs": [], "source": ["pid_within_cluster.shape"]}, {"cell_type": "code", "execution_count": null, "id": "97b9acf5-a663-4bf4-892a-68b28485a559", "metadata": {}, "outputs": [], "source": ["pid_within_cluster.head()"]}, {"cell_type": "code", "execution_count": null, "id": "75b64d84-027a-4f4e-bc1f-4715094d0297", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "47f32c69-f496-4fd3-83a1-515292f1ba05", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["def get_dot_product_matrix(user_df, prod_df):\n", "    group_embeddings_df = prod_emb.sort_values(\n", "        [\"group_id\", \"pid_carts\", \"product_id\"], ascending=[True, False, True]\n", "    ).drop_duplicates(subset=[\"group_id\"], keep=\"first\")\n", "    user_embeddings = np.array(user_df[\"user_embedding\"].to_list())\n", "    group_embeddings = np.array(group_embeddings_df[\"item_embedding\"].to_list())\n", "    dot_product_matrix = np.dot(user_embeddings, group_embeddings.T)\n", "    dot_product_matrix_df = pd.DataFrame(\n", "        dot_product_matrix, columns=group_embeddings_df[\"group_id\"], index=user_df[\"entity_id\"]\n", "    )\n", "    return dot_product_matrix_df"]}, {"cell_type": "code", "execution_count": null, "id": "dc93b822-827c-4320-9e84-85f2b5da428f", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["def top_products_within_cluster(chunk, prod_emb, row, similarity_df, topn=10):\n", "    cluster_name = row[\"cluster_theme\"]\n", "    cluster_pids = row[\"product_id\"]\n", "    cluster_gids = list(prod_emb[prod_emb[\"product_id\"].isin(cluster_pids)][\"group_id\"].unique())\n", "    # users mapped to current usecase\n", "    tmp_users_mapped_to_cluster = tmp_customer_usecase_mapping[\n", "        tmp_customer_usecase_mapping[\"cluster_theme\"] == cluster_name\n", "    ][\"dim_customer_key\"].unique()\n", "    # user mapped present in chunk\n", "    tmp_user_mapped_to_chunk = chunk[chunk[\"entity_id\"].isin(tmp_users_mapped_to_cluster)][\n", "        \"entity_id\"\n", "    ].unique()\n", "    if len(tmp_user_mapped_to_chunk) > 0:\n", "        similarity_df = similarity_df.loc[tmp_user_mapped_to_chunk]\n", "        # subset on cluster pids\n", "        tmp_cluster_df = similarity_df[cluster_gids]\n", "        # get argsort (column positions sorted by value per row)\n", "        sorted_col_indices = np.argsort(-tmp_cluster_df.values, axis=1)\n", "        # map column indices back to column names\n", "        sorted_col_names = tmp_cluster_df.columns.values[sorted_col_indices]\n", "        # create a new DataFrame with the sorted column names\n", "        tmp_cluster_df_ranked = pd.DataFrame(sorted_col_names, index=tmp_cluster_df.index)\n", "        # subset to take top 10 pids\n", "        tmp_cluster_df_ranked = tmp_cluster_df_ranked.iloc[:, :topn]\n", "        # mapping to convert gid back to pid\n", "        mapper = np.vectorize(group_id_pid_map.get)\n", "        mapped_arr = mapper(tmp_cluster_df_ranked.to_numpy())\n", "        tmp_cluster_df_ranked = pd.DataFrame(\n", "            mapped_arr, columns=tmp_cluster_df_ranked.columns, index=tmp_cluster_df_ranked.index\n", "        )\n", "        tmp_top_col_names = list(tmp_cluster_df_ranked.columns)\n", "        # agg back to list\n", "        tmp_cluster_df_ranked = (\n", "            tmp_cluster_df_ranked[tmp_top_col_names].agg(list, axis=1).reset_index()\n", "        )\n", "        tmp_cluster_df_ranked[\"cluster_theme\"] = cluster_name\n", "        tmp_cluster_df_ranked.columns = [\"dim_customer_key\", \"critical_pids\", \"cluster_theme\"]\n", "        return tmp_cluster_df_ranked\n", "    else:\n", "        return pd.DataFrame(columns=[\"dim_customer_key\", \"critical_pids\", \"cluster_theme\"])"]}, {"cell_type": "code", "execution_count": null, "id": "2fb6a38f-fbc6-410b-a9e9-6c2b78f27d75", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["chunk_size = 25000\n", "chunks = [user_emb.iloc[i : i + chunk_size] for i in range(0, len(user_emb), chunk_size)]"]}, {"cell_type": "code", "execution_count": null, "id": "e44748f0-a829-47fb-90ab-da1c543daf83", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["user_cluster_pid_df = pd.DataFrame()\n", "for chunk in chunks:\n", "    similarity_df = get_dot_product_matrix(chunk, prod_emb)\n", "    for _, row in pid_within_cluster.iterrows():\n", "        user_cluster_pid_tmp = top_products_within_cluster(chunk, prod_emb, row, similarity_df)\n", "        user_cluster_pid_df = user_cluster_pid_df.append(user_cluster_pid_tmp)"]}, {"cell_type": "code", "execution_count": null, "id": "ef868367-2ad4-4718-b8e8-40a8e199b070", "metadata": {}, "outputs": [], "source": ["user_cluster_pid_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "deb25f5a-f4e9-4969-8204-434c19f75daa", "metadata": {}, "outputs": [], "source": ["user_cluster_pid_df.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "bed05957-3be0-4a11-b050-13cb628a1ef5", "metadata": {}, "outputs": [], "source": ["user_cluster_pid_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e6faff99-be4c-46d5-84ff-6c2d6cf67926", "metadata": {}, "outputs": [], "source": ["pb.to_trino(user_cluster_pid_df, **get_critical_pids_schema(mod_value=mod_value))"]}, {"cell_type": "code", "execution_count": null, "id": "6dcfbccc-e84f-4cb5-8f89-396123ced6b1", "metadata": {}, "outputs": [], "source": ["pb.to_trino(\n", "    read_sql_query(f\"{cwd}/queries/merge_critical_pids.sql\").format(mod_value=mod_value),\n", "    **get_customer_usecase_mapping_schema(mod_value=mod_value),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "668bdb4e-e041-4e19-a334-287b163fc650", "metadata": {}, "outputs": [], "source": ["print(\"Completed...\")"]}, {"cell_type": "code", "execution_count": null, "id": "aaba34b5-0496-42b3-a806-41a1a3ee7f35", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9380bba7-4757-41de-90b7-80583d937b13", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "87eb8dc8-68b0-4055-9898-f492d3b8355c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}