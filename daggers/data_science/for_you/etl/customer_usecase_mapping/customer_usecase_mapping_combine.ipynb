{"cells": [{"cell_type": "code", "execution_count": null, "id": "ba6402bf-fc41-4b01-994a-3aee16baa9f3", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["import os\n", "import sys"]}, {"cell_type": "code", "execution_count": null, "id": "8212bf42-fd4f-48d7-9a4a-0598b0c4c316", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "c4962b15-c942-4da1-9b9f-5024034fe247", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "6c3c350b-c29f-4cfe-887e-fa8057705585", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import ast\n", "import gc"]}, {"cell_type": "code", "execution_count": null, "id": "fbd9edfd-b2b0-4ad2-8482-42b02edbf93d", "metadata": {}, "outputs": [], "source": ["trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "512bea78-4ed1-44d8-8042-6733fa820173", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7b8c9261-2830-4073-8546-96eeeb0dda4d", "metadata": {}, "outputs": [], "source": ["def get_schema(loadtype=\"truncate\"):\n", "    customer_usecase_mapping_table_name = \"personalisation_for_you_customer_usecase_mapping\"\n", "    print(customer_usecase_mapping_table_name)\n", "    kwargs_customer_usecase_mapping_tb = {\n", "        \"schema_name\": \"consumer_intelligence_etls\",\n", "        \"table_name\": customer_usecase_mapping_table_name,\n", "        \"column_dtypes\": [\n", "            {\"name\": \"dim_customer_key\", \"type\": \"BIGINT\", \"description\": \"dim_customer_key\"},\n", "            {\"name\": \"cluster_theme\", \"type\": \"VARCHAR\", \"description\": \"cluster_theme\"},\n", "            {\"name\": \"score\", \"type\": \"REAL\", \"description\": \"score\"},\n", "            {\"name\": \"normalised_score\", \"type\": \"DOUBLE\", \"description\": \"normalised_score\"},\n", "            {\"name\": \"critical_pids\", \"type\": \"ARRAY(BIGINT)\", \"description\": \"critical_pids\"},\n", "            {\"name\": \"user_mod\", \"type\": \"INTEGER\", \"description\": \"user_mod\"},\n", "        ],\n", "        \"partition_key\": [\"user_mod\"],\n", "        \"primary_key\": [\"dim_customer_key\", \"cluster_theme\"],\n", "        \"load_type\": loadtype,  # append, truncate or upsert,\n", "        \"table_description\": \"Table for storing customer to usecase mapping for personalisation usecase\",\n", "    }\n", "    return kwargs_customer_usecase_mapping_tb"]}, {"cell_type": "code", "execution_count": null, "id": "26ad19da-954f-4960-a9c8-cef43a937d80", "metadata": {}, "outputs": [], "source": ["table_schema = get_schema()"]}, {"cell_type": "code", "execution_count": null, "id": "1a8ab138-1298-4ca2-8054-7f91a5d36c86", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["query = f\"\"\"\n", "    (\n", "        select dim_customer_key, cluster_theme, score, normalised_score, critical_pids, dim_customer_key%%10 as user_mod \n", "        from consumer_intelligence_etls.personalisation_for_you_cust_usecase_map_agg_mod_1\n", "    )\n", "    union all (\n", "        select dim_customer_key, cluster_theme, score, normalised_score, critical_pids, dim_customer_key%%10 as user_mod \n", "        from consumer_intelligence_etls.personalisation_for_you_cust_usecase_map_agg_mod_2\n", "    )\n", "    union all (\n", "        select dim_customer_key, cluster_theme, score, normalised_score, critical_pids, dim_customer_key%%10 as user_mod \n", "        from consumer_intelligence_etls.personalisation_for_you_cust_usecase_map_agg_mod_3\n", "    )\n", "    union all (\n", "        select dim_customer_key, cluster_theme, score, normalised_score, critical_pids, dim_customer_key%%10 as user_mod \n", "        from consumer_intelligence_etls.personalisation_for_you_cust_usecase_map_agg_mod_4\n", "    )\n", "    union all (\n", "        select dim_customer_key, cluster_theme, score, normalised_score, critical_pids, dim_customer_key%%10 as user_mod \n", "        from consumer_intelligence_etls.personalisation_for_you_cust_usecase_map_agg_mod_9\n", "    )\n", "\"\"\"\n", "pb.to_trino(query, **table_schema)"]}, {"cell_type": "code", "execution_count": null, "id": "191edc5b-6ff6-4981-a22a-c1225187ea27", "metadata": {}, "outputs": [], "source": ["print(\"Completed..\")"]}, {"cell_type": "code", "execution_count": null, "id": "6264bde2-6c1e-4a27-8bb1-6fd9929ce220", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f59ff8bd-5559-4d9f-946a-87c12800a22e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e64a4bdb-3738-4a50-ad54-4bc4fd7f3c3d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e996d23b-2ce3-470f-a72f-07af2f375b44", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f3c48cb0-b213-4358-913c-fe0ed74271f5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b91bf21a-ef78-4424-81ed-73cdf6c74e47", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}