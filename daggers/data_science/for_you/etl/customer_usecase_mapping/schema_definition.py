
def get_customer_usecase_mapping_schema_interim(mod_value, loadtype="truncate"):
    table_name = (
        "personalisation_for_you_cust_usecase_map_interim" + "_mod_" + str(mod_value)
    )
    print(table_name)
    kwargs_tb = {
        "schema_name": "consumer_intelligence_etls",
        "table_name": table_name,
        "column_dtypes": [
            {"name": "dim_customer_key", "type": "BIGINT", "description": "dim_customer_key"},
            {"name": "cluster_theme", "type": "VARCHAR", "description": "cluster_theme"},
            {"name": "customer_platform_carts", "type": "BIGINT", "description": "customer_platform_carts"},
            {"name": "customer_last_trans_dt_platform", "type": "TIMESTAMP(6)", "description": "customer_last_trans_dt_platform"},
            {"name": "carts_last_7d", "type": "BIGINT", "description": "carts_last_7d"},
            {"name": "pid_last_7d", "type": "BIGINT", "description": "pid_last_7d"},
            {"name": "ptypes_last_7d", "type": "BIGINT", "description": "ptypes_last_7d"},
            {"name": "qty_last_7d", "type": "BIGINT", "description": "qty_last_7d"},
            {"name": "carts_last_30d", "type": "BIGINT", "description": "carts_last_30d"},
            {"name": "pid_last_30d", "type": "BIGINT", "description": "pid_last_30d"},
            {"name": "ptypes_last_30d", "type": "BIGINT", "description": "ptypes_last_30d"},
            {"name": "qty_last_30d", "type": "BIGINT", "description": "qty_last_30d"},
            {
                "name": "qty_purchased_in_cluster",
                "type": "BIGINT",
                "description": "qty_purchased_in_cluster",
            },
            {
                "name": "cart_count_in_cluster",
                "type": "BIGINT",
                "description": "cart_count_in_cluster",
            },
            {
                "name": "pid_count_in_cluster",
                "type": "BIGINT",
                "description": "pid_count_in_cluster",
            },
            {
                "name": "first_transaction_date_in_cluster",
                "type": "TIMESTAMP(6)",
                "description": "first_transaction_date_in_cluster",
            },
            {
                "name": "last_transaction_date_in_cluster",
                "type": "TIMESTAMP(6)",
                "description": "last_transaction_date_in_cluster",
            },
            {
                "name": "keyterms_purchased_in_cluster",
                "type": "BIGINT",
                "description": "keyterms_purchased_in_cluster",
            },
            {"name": "date_diff_days", "type": "BIGINT", "description": "date_diff_days"},
            {"name": "date_diff_per_cart", "type": "REAL", "description": "date_diff_per_cart"},
            {
                "name": "days_since_last_trans_in_cluster",
                "type": "BIGINT",
                "description": "days_since_last_trans_in_cluster",
            },
            {
                "name": "days_since_last_bought_in_cluster",
                "type": "BIGINT",
                "description": "days_since_last_bought_in_cluster",
            },
            {"name": "qty_per_cart", "type": "REAL", "description": "qty_per_cart"},
            {"name": "pid_per_cart", "type": "REAL", "description": "pid_per_cart"},
            {
                "name": "cluster_carts_per_platform_carts",
                "type": "REAL",
                "description": "cluster_carts_per_platform_carts",
            },
            {"name": "decay_score", "type": "DOUBLE", "description": "decay_score"},
            {
                "name": "engagement_ratio_7d_30d",
                "type": "REAL",
                "description": "engagement_ratio_7d_30d",
            },
            {
                "name": "exploration_ratio_7d_30d",
                "type": "REAL",
                "description": "exploration_ratio_7d_30d",
            },
            {
                "name": "cart_count_ratio_7d_30d",
                "type": "REAL",
                "description": "cart_count_ratio_7d_30d",
            },
            {"name": "score", "type": "REAL", "description": "score"},
            {"name": "min_score", "type": "REAL", "description": "min_score"},
            {"name": "max_score", "type": "REAL", "description": "max_score"},
            {"name": "normalised_score", "type": "DOUBLE", "description": "normalised_score"},
            {"name": "cluster_category", "type": "VARCHAR", "description": "cluster_category"},
            {
                "name": "sqrt_cluster_carts_per_platform_carts",
                "type": "REAL",
                "description": "sqrt_cluster_carts_per_platform_carts",
            },
            {"name": "explore_score_norm", "type": "DOUBLE", "description": "explore_score_norm"},
            {"name": "rank", "type": "INTEGER", "description": "rank"},
            {"name": "diversity_score", "type": "REAL", "description": "diversity_score"},
            {"name": "is_valid_cluster", "type": "INTEGER", "description": "is_valid_cluster"},
        ],
        "primary_key": ["dim_customer_key", "cluster_theme"],
        "load_type": loadtype,  # append, truncate or upsert,
        "table_description": "Intermediate table for storing customer to usecase mapping for personalisation usecase",
    }
    return kwargs_tb

def get_critical_pids_schema(mod_value, loadtype='truncate'):
    table_name = (
        "personalisation_for_you_cust_usecase_cpids_interim" + "_mod_" + str(mod_value)
    )
    print(table_name)
    kwargs_tb = {
        "schema_name": "consumer_intelligence_etls",
        "table_name": table_name,
        "column_dtypes": [
            {"name": "dim_customer_key", "type": "BIGINT", "description": "dim_customer_key"},
            {"name": "cluster_theme", "type": "VARCHAR", "description": "cluster_theme"},
            {"name": "critical_pids", "type": "ARRAY(BIGINT)", "description": "critical_pids"},
        ],
        "primary_key": ["dim_customer_key", "cluster_theme"],
        "load_type": loadtype,  # append, truncate or upsert,
        "table_description": "Intermediate table for storing customer to usecase mapping for personalisation usecase",
    }
    return kwargs_tb

def get_customer_usecase_mapping_schema(mod_value, loadtype="truncate"):
    table_name = (
        "personalisation_for_you_cust_usecase_map_agg" + "_mod_" + str(mod_value)
    )
    print(table_name)
    kwargs_tb = {
        "schema_name": "consumer_intelligence_etls",
        "table_name": table_name,
        "column_dtypes": [
            {"name": "dim_customer_key", "type": "BIGINT", "description": "dim_customer_key"},
            {"name": "cluster_theme", "type": "VARCHAR", "description": "cluster_theme"},
            {"name": "customer_platform_carts", "type": "BIGINT", "description": "customer_platform_carts"},
            {"name": "customer_last_trans_dt_platform", "type": "TIMESTAMP(6)", "description": "customer_last_trans_dt_platform"},
            {"name": "carts_last_7d", "type": "BIGINT", "description": "carts_last_7d"},
            {"name": "pid_last_7d", "type": "BIGINT", "description": "pid_last_7d"},
            {"name": "ptypes_last_7d", "type": "BIGINT", "description": "ptypes_last_7d"},
            {"name": "qty_last_7d", "type": "BIGINT", "description": "qty_last_7d"},
            {"name": "carts_last_30d", "type": "BIGINT", "description": "carts_last_30d"},
            {"name": "pid_last_30d", "type": "BIGINT", "description": "pid_last_30d"},
            {"name": "ptypes_last_30d", "type": "BIGINT", "description": "ptypes_last_30d"},
            {"name": "qty_last_30d", "type": "BIGINT", "description": "qty_last_30d"},
            {
                "name": "qty_purchased_in_cluster",
                "type": "BIGINT",
                "description": "qty_purchased_in_cluster",
            },
            {
                "name": "cart_count_in_cluster",
                "type": "BIGINT",
                "description": "cart_count_in_cluster",
            },
            {
                "name": "pid_count_in_cluster",
                "type": "BIGINT",
                "description": "pid_count_in_cluster",
            },
            {
                "name": "first_transaction_date_in_cluster",
                "type": "TIMESTAMP(6)",
                "description": "first_transaction_date_in_cluster",
            },
            {
                "name": "last_transaction_date_in_cluster",
                "type": "TIMESTAMP(6)",
                "description": "last_transaction_date_in_cluster",
            },
            {
                "name": "keyterms_purchased_in_cluster",
                "type": "BIGINT",
                "description": "keyterms_purchased_in_cluster",
            },
            {"name": "date_diff_days", "type": "BIGINT", "description": "date_diff_days"},
            {"name": "date_diff_per_cart", "type": "REAL", "description": "date_diff_per_cart"},
            {
                "name": "days_since_last_trans_in_cluster",
                "type": "BIGINT",
                "description": "days_since_last_trans_in_cluster",
            },
            {
                "name": "days_since_last_bought_in_cluster",
                "type": "BIGINT",
                "description": "days_since_last_bought_in_cluster",
            },
            {"name": "qty_per_cart", "type": "REAL", "description": "qty_per_cart"},
            {"name": "pid_per_cart", "type": "REAL", "description": "pid_per_cart"},
            {
                "name": "cluster_carts_per_platform_carts",
                "type": "REAL",
                "description": "cluster_carts_per_platform_carts",
            },
            {"name": "decay_score", "type": "DOUBLE", "description": "decay_score"},
            {
                "name": "engagement_ratio_7d_30d",
                "type": "REAL",
                "description": "engagement_ratio_7d_30d",
            },
            {
                "name": "exploration_ratio_7d_30d",
                "type": "REAL",
                "description": "exploration_ratio_7d_30d",
            },
            {
                "name": "cart_count_ratio_7d_30d",
                "type": "REAL",
                "description": "cart_count_ratio_7d_30d",
            },
            {"name": "score", "type": "REAL", "description": "score"},
            {"name": "min_score", "type": "REAL", "description": "min_score"},
            {"name": "max_score", "type": "REAL", "description": "max_score"},
            {"name": "normalised_score", "type": "DOUBLE", "description": "normalised_score"},
            {"name": "cluster_category", "type": "VARCHAR", "description": "cluster_category"},
            {"name": "critical_pids", "type": "ARRAY(BIGINT)", "description": "critical_pids"},
            {
                "name": "sqrt_cluster_carts_per_platform_carts",
                "type": "REAL",
                "description": "sqrt_cluster_carts_per_platform_carts",
            },
            {"name": "explore_score_norm", "type": "DOUBLE", "description": "explore_score_norm"},
            {"name": "rank", "type": "INTEGER", "description": "rank"},
            {"name": "diversity_score", "type": "REAL", "description": "diversity_score"},
            {"name": "is_valid_cluster", "type": "INTEGER", "description": "is_valid_cluster"},
        ],
        "primary_key": ["dim_customer_key", "cluster_theme"],
        "load_type": loadtype,  # append, truncate or upsert,
        "table_description": "Intermediate table for storing customer to usecase mapping for personalisation usecase",
    }
    return kwargs_tb

