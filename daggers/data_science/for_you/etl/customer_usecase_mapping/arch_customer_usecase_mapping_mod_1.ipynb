{"cells": [{"cell_type": "code", "execution_count": null, "id": "a5a349c8-f865-4533-bbb1-5d64fdf1ceca", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["import os\n", "import sys"]}, {"cell_type": "code", "execution_count": null, "id": "cc2acaee-a503-4814-9b78-0a7f2ffb7d8d", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "bd70912d-0a03-4cc2-8ebe-7a26bc16144e", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "f1aa7c77-f15f-41c6-a1a5-a295d402ce35", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import ast\n", "import gc"]}, {"cell_type": "code", "execution_count": null, "id": "7eaf3dd3-578d-4c55-920c-507e16f2ac9d", "metadata": {}, "outputs": [], "source": ["trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "563a9621-af8b-4eb1-b84c-47a6b2bedb4c", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1097376f-42b3-42f3-9c6c-be3d5f6a226c", "metadata": {}, "outputs": [], "source": ["def get_customer_usecase_mapping_schema(mod_value, loadtype=\"truncate\"):\n", "    customer_usecase_mapping_table_name = (\n", "        \"personalisation_for_you_cust_usecase_map_agg\" + \"_mod_\" + str(mod_value)\n", "    )\n", "    print(customer_usecase_mapping_table_name)\n", "    kwargs_customer_usecase_mapping_tb = {\n", "        \"schema_name\": \"consumer_intelligence_etls\",\n", "        \"table_name\": customer_usecase_mapping_table_name,\n", "        \"column_dtypes\": [\n", "            {\"name\": \"dim_customer_key\", \"type\": \"BIGINT\", \"description\": \"dim_customer_key\"},\n", "            {\"name\": \"cluster_theme\", \"type\": \"VARCHAR\", \"description\": \"cluster_theme\"},\n", "            {\n", "                \"name\": \"customer_platform_carts\",\n", "                \"type\": \"BIGINT\",\n", "                \"description\": \"customer_platform_carts\",\n", "            },\n", "            {\n", "                \"name\": \"customer_last_trans_dt_platform\",\n", "                \"type\": \"TIMESTAMP(6)\",\n", "                \"description\": \"customer_last_trans_dt_platform\",\n", "            },\n", "            {\"name\": \"carts_last_7d\", \"type\": \"BIGINT\", \"description\": \"carts_last_7d\"},\n", "            {\"name\": \"pid_last_7d\", \"type\": \"BIGINT\", \"description\": \"pid_last_7d\"},\n", "            {\"name\": \"ptypes_last_7d\", \"type\": \"BIGINT\", \"description\": \"ptypes_last_7d\"},\n", "            {\"name\": \"qty_last_7d\", \"type\": \"BIGINT\", \"description\": \"qty_last_7d\"},\n", "            {\"name\": \"carts_last_30d\", \"type\": \"BIGINT\", \"description\": \"carts_last_30d\"},\n", "            {\"name\": \"pid_last_30d\", \"type\": \"BIGINT\", \"description\": \"pid_last_30d\"},\n", "            {\"name\": \"ptypes_last_30d\", \"type\": \"BIGINT\", \"description\": \"ptypes_last_30d\"},\n", "            {\"name\": \"qty_last_30d\", \"type\": \"BIGINT\", \"description\": \"qty_last_30d\"},\n", "            {\n", "                \"name\": \"qty_purchased_in_cluster\",\n", "                \"type\": \"BIGINT\",\n", "                \"description\": \"qty_purchased_in_cluster\",\n", "            },\n", "            {\n", "                \"name\": \"cart_count_in_cluster\",\n", "                \"type\": \"BIGINT\",\n", "                \"description\": \"cart_count_in_cluster\",\n", "            },\n", "            {\n", "                \"name\": \"pid_count_in_cluster\",\n", "                \"type\": \"BIGINT\",\n", "                \"description\": \"pid_count_in_cluster\",\n", "            },\n", "            {\n", "                \"name\": \"first_transaction_date_in_cluster\",\n", "                \"type\": \"TIMESTAMP(6)\",\n", "                \"description\": \"first_transaction_date_in_cluster\",\n", "            },\n", "            {\n", "                \"name\": \"last_transaction_date_in_cluster\",\n", "                \"type\": \"TIMESTAMP(6)\",\n", "                \"description\": \"last_transaction_date_in_cluster\",\n", "            },\n", "            {\n", "                \"name\": \"keyterms_purchased_in_cluster\",\n", "                \"type\": \"BIGINT\",\n", "                \"description\": \"keyterms_purchased_in_cluster\",\n", "            },\n", "            {\"name\": \"date_diff_days\", \"type\": \"BIGINT\", \"description\": \"date_diff_days\"},\n", "            {\"name\": \"date_diff_per_cart\", \"type\": \"REAL\", \"description\": \"date_diff_per_cart\"},\n", "            {\n", "                \"name\": \"days_since_last_trans_in_cluster\",\n", "                \"type\": \"BIGINT\",\n", "                \"description\": \"days_since_last_trans_in_cluster\",\n", "            },\n", "            {\n", "                \"name\": \"days_since_last_bought_in_cluster\",\n", "                \"type\": \"BIGINT\",\n", "                \"description\": \"days_since_last_bought_in_cluster\",\n", "            },\n", "            {\"name\": \"qty_per_cart\", \"type\": \"REAL\", \"description\": \"qty_per_cart\"},\n", "            {\"name\": \"pid_per_cart\", \"type\": \"REAL\", \"description\": \"pid_per_cart\"},\n", "            {\n", "                \"name\": \"cluster_carts_per_platform_carts\",\n", "                \"type\": \"REAL\",\n", "                \"description\": \"cluster_carts_per_platform_carts\",\n", "            },\n", "            {\"name\": \"decay_score\", \"type\": \"DOUBLE\", \"description\": \"decay_score\"},\n", "            {\n", "                \"name\": \"engagement_ratio_7d_30d\",\n", "                \"type\": \"REAL\",\n", "                \"description\": \"engagement_ratio_7d_30d\",\n", "            },\n", "            {\n", "                \"name\": \"exploration_ratio_7d_30d\",\n", "                \"type\": \"REAL\",\n", "                \"description\": \"exploration_ratio_7d_30d\",\n", "            },\n", "            {\n", "                \"name\": \"cart_count_ratio_7d_30d\",\n", "                \"type\": \"REAL\",\n", "                \"description\": \"cart_count_ratio_7d_30d\",\n", "            },\n", "            {\"name\": \"score\", \"type\": \"REAL\", \"description\": \"score\"},\n", "            {\"name\": \"min_score\", \"type\": \"REAL\", \"description\": \"min_score\"},\n", "            {\"name\": \"max_score\", \"type\": \"REAL\", \"description\": \"max_score\"},\n", "            {\"name\": \"normalised_score\", \"type\": \"DOUBLE\", \"description\": \"normalised_score\"},\n", "            {\"name\": \"cluster_category\", \"type\": \"VARCHAR\", \"description\": \"cluster_category\"},\n", "            {\"name\": \"critical_pids\", \"type\": \"ARRAY(BIGINT)\", \"description\": \"critical_pids\"},\n", "        ],\n", "        \"primary_key\": [\"dim_customer_key\", \"cluster_theme\"],\n", "        \"load_type\": loadtype,  # append, truncate or upsert,\n", "        \"table_description\": \"Intermediate table for storing customer to usecase mapping for personalisation usecase\",\n", "    }\n", "    return kwargs_customer_usecase_mapping_tb"]}, {"cell_type": "code", "execution_count": null, "id": "0bc9e7b6-59eb-40e1-9295-55f0a3e981b1", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["def generate_customer_usecase_mapping(mod_value):\n", "\n", "    customer_usecase_mapping_query = f\"\"\"\n", "\n", "        with customer_platform_agg as (\n", "            select dim_customer_key,\n", "                   count(distinct cart_id) as customer_platform_carts,\n", "                   max(cart_checkout_ts_ist) as customer_last_trans_dt_platform\n", "            from consumer_intelligence_etls.personalisation_for_you_base_data_tmp\n", "            where user_mod={mod_value}\n", "            group by 1\n", "        ),\n", "        \n", "        base_data as (\n", "            select bd.*,\n", "                   cpa.customer_platform_carts,\n", "                   cpa.customer_last_trans_dt_platform,\n", "                   pkm.metadata_id,\n", "                   pkm.metadata_name,\n", "                   pkm.metadata_type,\n", "                   pid_sales,\n", "                   pid_qty,\n", "                   pid_carts,\n", "                   cluster_theme,\n", "                   cluster_category,\n", "                   is_primary,\n", "                   date_diff('day', cart_checkout_ts_ist, current_date) as days_ago,\n", "                   date_diff('day', cart_checkout_ts_ist, customer_last_trans_dt_platform) as days_ago_since_last_transaction\n", "                from consumer_intelligence_etls.personalisation_for_you_base_data_tmp bd\n", "                join customer_platform_agg cpa on cpa.dim_customer_key=bd.dim_customer_key\n", "                join consumer_intelligence_etls.personalisation_for_you_pid_keyterm_mapping pkm on pkm.product_id=bd.product_id\n", "                join consumer_intelligence_etls.personalisation_for_you_usecase_entity_mapping uem on uem.metadata_id=pkm.metadata_id and uem.metadata_name=pkm.metadata_name\n", "                where bd.user_mod={mod_value}\n", "                and cart_checkout_ts_ist is not null\n", "        ),\n", "\n", "        valid_clusters as (\n", "            select dim_customer_key, \n", "                   cluster_theme,\n", "                   1 as is_valid_cluster\n", "            from base_data\n", "            where is_primary=1\n", "            group by 1,2,3\n", "        ),\n", "    \n", "        last_7_days_agg as (\n", "            select dim_customer_key,\n", "                   cluster_theme,\n", "                   count(distinct cart_id) as carts_last_7d,\n", "                   count(distinct product_id) as pid_last_7d,\n", "                   count(distinct product_type_id) as ptypes_last_7d,\n", "                   sum(total_item_qty) as qty_last_7d\n", "            from base_data\n", "            --where days_ago<=7\n", "            where days_ago_since_last_transaction<=7\n", "            group by 1,2\n", "        ),\n", "    \n", "        last_30_days_agg as (\n", "            select dim_customer_key,\n", "                   cluster_theme,\n", "                   count(distinct cart_id) as carts_last_30d,\n", "                   count(distinct product_id) as pid_last_30d,\n", "                   count(distinct product_type_id) as ptypes_last_30d,\n", "                   sum(total_item_qty) as qty_last_30d\n", "            from base_data\n", "            --where days_ago<=30\n", "            where days_ago_since_last_transaction<=30\n", "            group by 1,2\n", "        ),\n", "    \n", "        inter_agg1 as (\n", "            select bd1.dim_customer_key, \n", "                   bd1.cluster_theme,\n", "                   bd1.cluster_category,\n", "                   customer_platform_carts,\n", "                   customer_last_trans_dt_platform,\n", "                   coalesce(carts_last_7d, 0) as carts_last_7d,\n", "                   coalesce(pid_last_7d, 0) as pid_last_7d,\n", "                   coalesce(ptypes_last_7d, 0) as ptypes_last_7d,\n", "                   coalesce(qty_last_7d, 0) as qty_last_7d,\n", "                   coalesce(carts_last_30d, 1) as carts_last_30d,\n", "                   coalesce(pid_last_30d, 1) as pid_last_30d,\n", "                   coalesce(ptypes_last_30d, 1) as ptypes_last_30d,\n", "                   coalesce(qty_last_30d, 1) as qty_last_30d,\n", "                   sum(total_item_qty) as qty_purchased_in_cluster, \n", "                   count(distinct cart_id) as cart_count_in_cluster,\n", "                   count(distinct product_id) as pid_count_in_cluster,\n", "                   min(cart_checkout_ts_ist) as first_transaction_date_in_cluster,\n", "                   max(cart_checkout_ts_ist) as last_transaction_date_in_cluster,\n", "                   count(distinct metadata_name) as keyterms_purchased_in_cluster,\n", "                   date_diff('day', min(cart_checkout_ts_ist), max(cart_checkout_ts_ist)) as date_diff_days,\n", "                   CAST(COALESCE(((1.00000 * date_diff('day', min(cart_checkout_ts_ist), max(cart_checkout_ts_ist))) / NULLIF(count(distinct cart_id),0)),0.0) AS REAL) as date_diff_per_cart,\n", "                   date_diff('day', max(cart_checkout_ts_ist), customer_last_trans_dt_platform) as days_since_last_trans_in_cluster,\n", "                   date_diff('day', max(cart_checkout_ts_ist), current_date) as days_since_last_bought_in_cluster\n", "            from base_data bd1\n", "            left join last_7_days_agg l7d on l7d.dim_customer_key=bd1.dim_customer_key and l7d.cluster_theme=bd1.cluster_theme\n", "            left join last_30_days_agg l30d on l30d.dim_customer_key=bd1.dim_customer_key and l30d.cluster_theme=bd1.cluster_theme\n", "            group by 1,2,3,4,5,6,7,8,9,10,11,12,13\n", "        ),\n", "    \n", "        inter_agg2 as (\n", "            select ia1.*,\n", "                   CAST(COALESCE(((1.00000 * qty_purchased_in_cluster) / NULLIF(cart_count_in_cluster,0)),0.0) AS REAL) as qty_per_cart,\n", "                   CAST(COALESCE(((1.00000 * pid_count_in_cluster) / NULLIF(cart_count_in_cluster,0)),0.0) AS REAL) as pid_per_cart,\n", "                   CAST(COALESCE(((1.00000 * cart_count_in_cluster) / NULLIF(customer_platform_carts,0)),0.0) AS REAL) as cluster_carts_per_platform_carts,\n", "                   power(0.95, days_since_last_trans_in_cluster) as decay_score,\n", "                   CAST(COALESCE(((1.00000 * qty_last_7d) / NULLIF(qty_last_30d,0)),0.0) AS REAL) as engagement_ratio_7d_30d,\n", "                   CAST(COALESCE(((1.00000 * ptypes_last_7d) / NULLIF(ptypes_last_30d,0)),0.0) AS REAL) as exploration_ratio_7d_30d,\n", "                   CAST(COALESCE(((1.00000 * carts_last_7d) / NULLIF(carts_last_30d,0)),0.0) AS REAL) as cart_count_ratio_7d_30d\n", "            from inter_agg1 ia1\n", "        ),\n", "\n", "        inter_agg3 as (\n", "            select ia2.*,\n", "                   (1.00000 * ((power(cluster_carts_per_platform_carts,0.5) * decay_score) + (1.00000 + exploration_ratio_7d_30d) + (1.00000 + cart_count_ratio_7d_30d))) as score\n", "            from inter_agg2 ia2\n", "        ),\n", "\n", "        inter_agg4 as (\n", "            select dim_customer_key, \n", "                   min(score) as min_score, \n", "                   max(score) as max_score\n", "            from inter_agg3\n", "            group by 1\n", "        )\n", "\n", "        select ia3.*,\n", "               min_score,\n", "               max_score,\n", "               --cast(((score - min_score) / (max_score - min_score)) as double) as normalised_score,\n", "               CAST(COALESCE(((score - min_score)  / NULLIF((max_score - min_score),0)),0.0) AS DOUBLE) as normalised_score--,\n", "               --is_valid_cluster\n", "        from inter_agg3 ia3\n", "        join inter_agg4 ia4 on ia4.dim_customer_key=ia3.dim_customer_key\n", "        left join valid_clusters vc on vc.dim_customer_key=ia3.dim_customer_key and vc.cluster_theme=ia3.cluster_theme\n", "               \n", "                \n", "    \"\"\"\n", "\n", "    # customer_usecase_mapping_schema = get_customer_usecase_mapping_schema(mod_value)\n", "    # pb.to_trino(customer_usecase_mapping_query, **customer_usecase_mapping_schema)\n", "    # print(\"customer usecase mapping data pushed for mod:: \", mod_value)\n", "    df = pd.read_sql(customer_usecase_mapping_query, con=trino_con)\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "2d29d957-b33b-46a4-aae5-4251715c46d4", "metadata": {}, "outputs": [], "source": ["mod_value = 1"]}, {"cell_type": "code", "execution_count": null, "id": "6757be00-a959-4493-a1db-3ebad22e8a58", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["customer_usecase_mapping = generate_customer_usecase_mapping(mod_value=mod_value)"]}, {"cell_type": "code", "execution_count": null, "id": "92450099-d2d3-4e24-898a-1550499cd21d", "metadata": {}, "outputs": [], "source": ["customer_usecase_mapping.shape"]}, {"cell_type": "code", "execution_count": null, "id": "5fa09ef2-ef90-432d-a913-a1e8c7e5ea80", "metadata": {}, "outputs": [], "source": ["customer_usecase_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "5db8f0f3-acf1-408d-b053-a4481c41dec7", "metadata": {}, "outputs": [], "source": ["tmp_customer_usecase_mapping = customer_usecase_mapping[\n", "    [\"dim_customer_key\", \"cluster_theme\"]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "91359858-ce6b-4169-8975-d3400e5a2f8e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "2f76ba9d-bdb0-486f-9417-f31a0d0a7dcf", "metadata": {}, "source": ["## Mapping critical pids"]}, {"cell_type": "code", "execution_count": null, "id": "733b0c39-cd81-4222-8f7c-30ba1efa7230", "metadata": {}, "outputs": [], "source": ["usecase_entity_mapping = pd.read_sql(\n", "    f\"\"\"\n", "    select * from consumer_intelligence_etls.personalisation_for_you_usecase_entity_mapping\n", "\"\"\",\n", "    con=trino_con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9bf0b58e-a094-439e-8e16-97c528271762", "metadata": {}, "outputs": [], "source": ["usecase_entity_mapping.shape"]}, {"cell_type": "code", "execution_count": null, "id": "cb8e5a36-03b7-48a5-a3df-7d01e5bcafca", "metadata": {}, "outputs": [], "source": ["usecase_entity_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "dafe1c52-9971-471b-9f2f-e1e7f656afd1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5fc05243-bf91-47be-9ea5-b0491d7b1f63", "metadata": {}, "outputs": [], "source": ["keyterm_pid_mapping = pd.read_sql(\n", "    f\"\"\"\n", "    select * from consumer_intelligence_etls.personalisation_for_you_pid_keyterm_mapping\n", "\"\"\",\n", "    con=trino_con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "05ee3c39-0fdd-4a7e-9bab-4c2d8d77041d", "metadata": {}, "outputs": [], "source": ["keyterm_pid_mapping.shape"]}, {"cell_type": "code", "execution_count": null, "id": "0bdeab3c-423b-4e19-8fda-24c0b53fb8e4", "metadata": {}, "outputs": [], "source": ["keyterm_pid_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8f3f77e9-d801-4a18-bd49-1e523759f956", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "84b13f61-8790-4fea-b288-4e5e1d2a7227", "metadata": {}, "outputs": [], "source": ["pid_group_id_mapping = pd.read_sql(\n", "    f\"\"\"\n", "    select product_id, group_id from consumer_intelligence_etls.personalisation_prod_meta_table_v2 group by 1,2\n", "\"\"\",\n", "    con=trino_con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fffd0c5e-8344-43e8-8e81-6abf9deb8668", "metadata": {}, "outputs": [], "source": ["pid_group_id_mapping.shape"]}, {"cell_type": "code", "execution_count": null, "id": "6cfead12-985d-47d8-ad84-f8fbf3e344d7", "metadata": {}, "outputs": [], "source": ["pid_group_id_mapping.product_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "11ede88b-a5c1-4a6b-9a37-2dabe72fe707", "metadata": {}, "outputs": [], "source": ["pid_group_id_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "87f9f855-ed9e-4ac8-9b37-5884870054f2", "metadata": {}, "outputs": [], "source": ["pid_group_id_mapping = pid_group_id_mapping.set_index(\"product_id\").T.to_dict(orient=\"records\")[0]"]}, {"cell_type": "code", "execution_count": null, "id": "14c5a791-a742-4148-9e34-2f157cbfd672", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["len(pid_group_id_mapping)"]}, {"cell_type": "code", "execution_count": null, "id": "05a36e26-d5dd-4a0e-a903-71203099090c", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "faf92335-02eb-4479-83fc-c320a3d8fc9e", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["prod_emb = pd.read_sql(\n", "    f\"\"\"\n", "    select * from consumer_intelligence_etls.personalisation_ranking_prod_emb\n", "\"\"\",\n", "    con=trino_con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "44e97307-9293-4990-8c90-3727a0a7512b", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["prod_emb.shape"]}, {"cell_type": "code", "execution_count": null, "id": "1a813ce4-7598-4601-acf0-38d059092fbe", "metadata": {}, "outputs": [], "source": ["prod_emb.product_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "72d5bd7f-be43-4b9b-bdd6-26bd695e5e2a", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["prod_emb[\"group_id\"] = prod_emb[\"product_id\"].map(pid_group_id_mapping)"]}, {"cell_type": "code", "execution_count": null, "id": "07d3811a-4562-4ba6-a33d-e9018319fa67", "metadata": {}, "outputs": [], "source": ["prod_emb.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "071746db-2a78-4eaf-a50e-a9930dea644b", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["prod_emb[prod_emb[\"group_id\"].isna()]"]}, {"cell_type": "code", "execution_count": null, "id": "3bb93c96-211e-46fa-86c7-b7fb62f90f6d", "metadata": {}, "outputs": [], "source": ["prod_emb = prod_emb[~prod_emb[\"group_id\"].isna()]"]}, {"cell_type": "code", "execution_count": null, "id": "988c14ed-99e8-4c56-9cf9-9bbbd1643a41", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["prod_emb.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "1264b059-3882-4dcc-be8d-a05234e1d3ab", "metadata": {}, "outputs": [], "source": ["prod_emb[\"group_id\"] = prod_emb[\"group_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "c075f350-ea83-4bd9-a50a-31cba639b3bd", "metadata": {}, "outputs": [], "source": ["prod_emb.shape"]}, {"cell_type": "code", "execution_count": null, "id": "ba44f97f-6920-4691-b9fb-25041d0335ef", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["prod_emb = prod_emb.merge(\n", "    keyterm_pid_mapping[[\"product_id\", \"pid_carts\"]].drop_duplicates(), on=\"product_id\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3088b325-6798-43e4-9c9e-d1d39f5739c5", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["prod_emb[\"pid_carts\"] = prod_emb[\"pid_carts\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "5ba91ec1-0a80-43e2-869d-0b9a2cc0fedc", "metadata": {}, "outputs": [], "source": ["prod_emb.head()"]}, {"cell_type": "code", "execution_count": null, "id": "986b4eda-12c1-4a6f-a09d-6d1a9e5fa0b8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d6fc1d61-c287-4f83-8776-f71d8fa41351", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["# group id to pid map\n", "group_id_pid_map = (\n", "    prod_emb.sort_values([\"group_id\", \"pid_carts\", \"product_id\"], ascending=[True, False, True])\n", "    .drop_duplicates(subset=[\"group_id\"], keep=\"first\")[[\"group_id\", \"product_id\"]]\n", "    .drop_duplicates()\n", "    .set_index(\"group_id\")\n", "    .T.to_dict(orient=\"records\")[0]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "58034072-5e84-49f3-bd2f-11cdfd8decc3", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["len(group_id_pid_map)"]}, {"cell_type": "code", "execution_count": null, "id": "f5572bf9-3973-46a9-a52e-e8a012bacfbe", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "32f0297a-1e00-4e5e-b81d-056d6437187b", "metadata": {}, "outputs": [], "source": ["user_emb = pd.read_sql(\n", "    f\"\"\"\n", "\n", "    WITH user_base AS (\n", "        SELECT DISTINCT CAST(dim_customer_key AS VARCHAR) AS dim_customer_key\n", "        FROM consumer_intelligence_etls.personalisation_for_you_base_data_tmp\n", "        WHERE dim_customer_key%%10={mod_value}\n", "    )\n", "    \n", "    SELECT emb.entity_id, \n", "           JSON_EXTRACT(feature_ctx_value, '$.user_embedding') AS user_embedding\n", "    FROM feature_store.user_user_personalised_emb_lightfm_1_0_0 emb\n", "    JOIN user_base ub ON emb.entity_id = ub.dim_customer_key\n", "    WHERE JSON_ARRAY_LENGTH(JSON_EXTRACT(feature_ctx_value, '$.user_embedding')) > 0\n", "    \n", "\"\"\",\n", "    con=trino_con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "549b20ec-bc49-4810-916a-8e87ce96c367", "metadata": {}, "outputs": [], "source": ["user_emb.shape"]}, {"cell_type": "code", "execution_count": null, "id": "30f8c145-6790-4edb-98d8-ee223c62a125", "metadata": {}, "outputs": [], "source": ["user_emb[\"entity_id\"].nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "05a56c9e-6318-4b09-a6af-660b92114956", "metadata": {}, "outputs": [], "source": ["user_emb[\"user_embedding\"] = user_emb[\"user_embedding\"].apply(lambda x: ast.literal_eval(x))"]}, {"cell_type": "code", "execution_count": null, "id": "dc4ed09e-aa62-441e-8c9b-b0daf584526b", "metadata": {}, "outputs": [], "source": ["user_emb[\"entity_id\"] = user_emb[\"entity_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "b2cf2da4-1bdc-4b11-a66a-a4f959654f49", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["user_emb.head()"]}, {"cell_type": "code", "execution_count": null, "id": "41da8b3d-1c55-432b-9a70-07021185fa8c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0a78430d-9408-441e-89e9-7e5b8cf2e26e", "metadata": {}, "outputs": [], "source": ["## pids within cluster\n", "\n", "usecase_keyterm_pid_mapping = usecase_entity_mapping.merge(\n", "    keyterm_pid_mapping[[\"metadata_id\", \"metadata_name\", \"product_id\"]].drop_duplicates(),\n", "    on=[\"metadata_id\", \"metadata_name\"],\n", "    how=\"left\",\n", ")\n", "\n", "pid_within_cluster = (\n", "    usecase_keyterm_pid_mapping[[\"cluster_theme\", \"is_personalised\", \"product_id\"]]\n", "    .drop_duplicates()\n", "    .groupby([\"cluster_theme\"])\n", "    .agg({\"is_personalised\": \"max\", \"product_id\": list})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f2f9d3c6-5623-4887-b4b7-2203e72d278b", "metadata": {}, "outputs": [], "source": ["pid_within_cluster.shape"]}, {"cell_type": "code", "execution_count": null, "id": "90bc1c50-3ec6-4be9-9524-31546564523b", "metadata": {}, "outputs": [], "source": ["pid_within_cluster = pid_within_cluster[pid_within_cluster[\"is_personalised\"] == 1]"]}, {"cell_type": "code", "execution_count": null, "id": "609392e6-0c11-48fc-a989-92d2dbb2c2fe", "metadata": {}, "outputs": [], "source": ["pid_within_cluster.shape"]}, {"cell_type": "code", "execution_count": null, "id": "97b9acf5-a663-4bf4-892a-68b28485a559", "metadata": {}, "outputs": [], "source": ["pid_within_cluster.head()"]}, {"cell_type": "code", "execution_count": null, "id": "75b64d84-027a-4f4e-bc1f-4715094d0297", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "47f32c69-f496-4fd3-83a1-515292f1ba05", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["def get_dot_product_matrix(user_df, prod_df):\n", "    group_embeddings_df = prod_emb.sort_values(\n", "        [\"group_id\", \"pid_carts\", \"product_id\"], ascending=[True, False, True]\n", "    ).drop_duplicates(subset=[\"group_id\"], keep=\"first\")\n", "    user_embeddings = np.array(user_df[\"user_embedding\"].to_list())\n", "    group_embeddings = np.array(group_embeddings_df[\"item_embedding\"].to_list())\n", "    dot_product_matrix = np.dot(user_embeddings, group_embeddings.T)\n", "    dot_product_matrix_df = pd.DataFrame(\n", "        dot_product_matrix, columns=group_embeddings_df[\"group_id\"], index=user_df[\"entity_id\"]\n", "    )\n", "    return dot_product_matrix_df"]}, {"cell_type": "code", "execution_count": null, "id": "dc93b822-827c-4320-9e84-85f2b5da428f", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["def top_products_within_cluster(chunk, prod_emb, row, similarity_df, topn=10):\n", "    cluster_name = row[\"cluster_theme\"]\n", "    cluster_pids = row[\"product_id\"]\n", "    cluster_gids = list(prod_emb[prod_emb[\"product_id\"].isin(cluster_pids)][\"group_id\"].unique())\n", "    # users mapped to current usecase\n", "    tmp_users_mapped_to_cluster = tmp_customer_usecase_mapping[\n", "        tmp_customer_usecase_mapping[\"cluster_theme\"] == cluster_name\n", "    ][\"dim_customer_key\"].unique()\n", "    # user mapped present in chunk\n", "    tmp_user_mapped_to_chunk = chunk[chunk[\"entity_id\"].isin(tmp_users_mapped_to_cluster)][\n", "        \"entity_id\"\n", "    ].unique()\n", "    similarity_df = similarity_df.loc[tmp_user_mapped_to_chunk]\n", "    # subset on cluster pids\n", "    tmp_cluster_df = similarity_df[cluster_gids]\n", "    # get argsort (column positions sorted by value per row)\n", "    sorted_col_indices = np.argsort(-tmp_cluster_df.values, axis=1)\n", "    # map column indices back to column names\n", "    sorted_col_names = tmp_cluster_df.columns.values[sorted_col_indices]\n", "    # create a new DataFrame with the sorted column names\n", "    tmp_cluster_df_ranked = pd.DataFrame(sorted_col_names, index=tmp_cluster_df.index)\n", "    # subset to take top 10 pids\n", "    tmp_cluster_df_ranked = tmp_cluster_df_ranked.iloc[:, :topn]\n", "    # mapping to convert gid back to pid\n", "    mapper = np.vectorize(group_id_pid_map.get)\n", "    mapped_arr = mapper(tmp_cluster_df_ranked.to_numpy())\n", "    tmp_cluster_df_ranked = pd.DataFrame(\n", "        mapped_arr, columns=tmp_cluster_df_ranked.columns, index=tmp_cluster_df_ranked.index\n", "    )\n", "    tmp_top_col_names = list(tmp_cluster_df_ranked.columns)\n", "    # agg back to list\n", "    tmp_cluster_df_ranked = tmp_cluster_df_ranked[tmp_top_col_names].agg(list, axis=1).reset_index()\n", "    tmp_cluster_df_ranked[\"cluster_theme\"] = cluster_name\n", "    tmp_cluster_df_ranked.columns = [\"dim_customer_key\", \"critical_pids\", \"cluster_theme\"]\n", "    return tmp_cluster_df_ranked"]}, {"cell_type": "code", "execution_count": null, "id": "2fb6a38f-fbc6-410b-a9e9-6c2b78f27d75", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["chunk_size = 25000\n", "chunks = [user_emb.iloc[i : i + chunk_size] for i in range(0, len(user_emb), chunk_size)]"]}, {"cell_type": "code", "execution_count": null, "id": "e44748f0-a829-47fb-90ab-da1c543daf83", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["user_cluster_pid_df = pd.DataFrame()\n", "for chunk in chunks:\n", "    similarity_df = get_dot_product_matrix(chunk, prod_emb)\n", "    for _, row in pid_within_cluster.iterrows():\n", "        user_cluster_pid_tmp = top_products_within_cluster(chunk, prod_emb, row, similarity_df)\n", "        user_cluster_pid_df = user_cluster_pid_df.append(user_cluster_pid_tmp)"]}, {"cell_type": "code", "execution_count": null, "id": "844dc752-6466-4afe-8566-525424d8cd4c", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["customer_usecase_mapping = customer_usecase_mapping.merge(\n", "    user_cluster_pid_df, on=[\"dim_customer_key\", \"cluster_theme\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9fbca355-0e94-4ec2-8235-23e08543c4cd", "metadata": {}, "outputs": [], "source": ["del user_emb, prod_emb, user_cluster_pid_df, similarity_df, user_cluster_pid_tmp, chunks, chunk\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "cdf9a787-d21e-46a7-a927-b54a01bd658a", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["## get candidate pids\n", "candidate_pids_as_fallback = usecase_entity_mapping[\n", "    [\"cluster_theme\", \"candidate_pids\"]\n", "].drop_duplicates(subset=[\"cluster_theme\"], keep=\"first\")\n", "print(candidate_pids_as_fallback.shape)\n", "candidate_pids_as_fallback.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3fe1f274-2647-4b64-9f81-ccb37ba45837", "metadata": {}, "outputs": [], "source": ["customer_usecase_mapping = customer_usecase_mapping.merge(\n", "    candidate_pids_as_fallback, on=\"cluster_theme\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "08c9ba52-d113-4727-b775-4099f1a2bc30", "metadata": {}, "outputs": [], "source": ["customer_usecase_mapping[\"critical_pids\"] = np.where(\n", "    customer_usecase_mapping[\"critical_pids\"].isna(),\n", "    customer_usecase_mapping[\"candidate_pids\"],\n", "    customer_usecase_mapping[\"critical_pids\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7d0542ca-73dd-4c17-a9de-2df0454ab3e8", "metadata": {}, "outputs": [], "source": ["customer_usecase_mapping.shape"]}, {"cell_type": "code", "execution_count": null, "id": "14b3be93-85d0-4e96-8219-864eeed85155", "metadata": {}, "outputs": [], "source": ["customer_usecase_mapping.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "9d4ed85d-2838-4013-b830-4d9798ddd504", "metadata": {}, "outputs": [], "source": ["customer_usecase_mapping = customer_usecase_mapping.drop(\"candidate_pids\", axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "6a754ee2-7cfc-4db9-8914-b3c426766be7", "metadata": {}, "outputs": [], "source": ["gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "ee4881b1-dd05-40b7-94c0-a062a22349df", "metadata": {}, "outputs": [], "source": ["customer_usecase_mapping[\"customer_last_trans_dt_platform\"] = pd.to_datetime(\n", "    customer_usecase_mapping[\"customer_last_trans_dt_platform\"]\n", ")\n", "customer_usecase_mapping[\"first_transaction_date_in_cluster\"] = pd.to_datetime(\n", "    customer_usecase_mapping[\"first_transaction_date_in_cluster\"]\n", ")\n", "customer_usecase_mapping[\"last_transaction_date_in_cluster\"] = pd.to_datetime(\n", "    customer_usecase_mapping[\"last_transaction_date_in_cluster\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "39de41dc-4a86-4a97-a98c-a8300d58c402", "metadata": {}, "outputs": [], "source": ["customer_usecase_mapping[\"decay_score\"] = customer_usecase_mapping[\"decay_score\"].round(15)"]}, {"cell_type": "code", "execution_count": null, "id": "33ff6382-e153-451a-8623-db05ee9db63b", "metadata": {}, "outputs": [], "source": ["customer_usecase_mapping_schema = get_customer_usecase_mapping_schema(mod_value=mod_value)\n", "pb.to_trino(customer_usecase_mapping, **customer_usecase_mapping_schema)"]}, {"cell_type": "code", "execution_count": null, "id": "c3878439-061a-40ba-b306-7d2186836e5f", "metadata": {}, "outputs": [], "source": ["print(\"Completed..\")"]}, {"cell_type": "code", "execution_count": null, "id": "2825de04-1be7-4c7d-b28b-c092f39fa86c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "87eb8dc8-68b0-4055-9898-f492d3b8355c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}