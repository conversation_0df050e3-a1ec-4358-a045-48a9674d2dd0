with cust_usecase_cpids as 
    (
        select t1.*,
               critical_pids as critical_pids_tmp
        from consumer_intelligence_etls.personalisation_for_you_cust_usecase_map_interim_mod_{mod_value} t1
        left join consumer_intelligence_etls.personalisation_for_you_cust_usecase_cpids_interim_mod_{mod_value} t2 on t1.dim_customer_key=t2.dim_customer_key and t1.cluster_theme=t2.cluster_theme
    ),

    usecase_fallback_pids as 
    (
        select cluster_theme, candidate_pids
        from consumer_intelligence_etls.personalisation_for_you_usecase_entity_mapping
        group by 1,2
    ),

    final as (
        select cpids.*,
               candidate_pids,
               coalesce(critical_pids_tmp, candidate_pids) as critical_pids
        from cust_usecase_cpids cpids
        left join usecase_fallback_pids ufp on ufp.cluster_theme=cpids.cluster_theme
    )
    
    select * 
    from TABLE(
        exclude_columns(
                        input => TABLE(final),
                        columns => DESCRIPTOR(critical_pids_tmp, candidate_pids)
                        )
        )