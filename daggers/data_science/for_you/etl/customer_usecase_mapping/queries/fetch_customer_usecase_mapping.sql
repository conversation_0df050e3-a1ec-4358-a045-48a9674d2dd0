with customer_platform_agg as (
    select dim_customer_key,
           count(distinct cart_id) as customer_platform_carts,
           max(cart_checkout_ts_ist) as customer_last_trans_dt_platform
    from consumer_intelligence_etls.personalisation_for_you_base_data_tmp
    where user_mod={mod_value}
    group by 1
),

cluster_theme_mapping_tb as (
    select theme, cluster_theme, 1 as cluster_theme_mapping from
    (
    select pkm.product_id, pkm.metadata_id, pkm.metadata_name, theme, cluster_theme, is_primary, cluster_category, candidate_pids
    from consumer_intelligence_etls.personalisation_for_you_pid_keyterm_mapping pkm
    join consumer_intelligence_etls.personalisation_for_you_usecase_entity_mapping uem on uem.metadata_id=pkm.metadata_id and uem.metadata_name=pkm.metadata_name
    join consumer_intelligence_etls.personalisation_prod_meta_table_v2 pmt on pmt.product_id=pkm.product_id
    where is_primary=1
    and l0_category=cluster_category
    )
    group by 1,2,3
    order by 1,2,3
),

base_data as (
    select bd.*,
           cpa.customer_platform_carts,
           cpa.customer_last_trans_dt_platform,
           pkm.metadata_id,
           pkm.metadata_name,
           pkm.metadata_type,
           pid_sales,
           pid_qty,
           pid_carts,
           uem.cluster_theme,
           cluster_category,
           is_primary,
           cluster_theme_mapping,
           explore_score_norm,
           date_diff('day', cart_checkout_ts_ist, current_date) as days_ago,
           date_diff('day', cart_checkout_ts_ist, customer_last_trans_dt_platform) as days_ago_since_last_transaction
        from consumer_intelligence_etls.personalisation_for_you_base_data_tmp bd
        join customer_platform_agg cpa on cpa.dim_customer_key=bd.dim_customer_key
        join consumer_intelligence_etls.personalisation_for_you_pid_keyterm_mapping pkm on pkm.product_id=bd.product_id
        join consumer_intelligence_etls.personalisation_for_you_usecase_entity_mapping uem on uem.metadata_id=pkm.metadata_id and uem.metadata_name=pkm.metadata_name
        left join cluster_theme_mapping_tb ctm on ctm.theme=bd.theme and ctm.cluster_theme=uem.cluster_theme 
        left join consumer_intelligence_etls.personalisation_for_you_explore exp on exp.dim_customer_key=bd.dim_customer_key and exp.theme=ctm.theme and exp.user_mod={mod_value}
        where bd.user_mod={mod_value}
        and cart_checkout_ts_ist is not null
),

last_7_days_agg as (
    select dim_customer_key,
           cluster_theme,
           count(distinct cart_id) as carts_last_7d,
           count(distinct product_id) as pid_last_7d,
           count(distinct product_type_id) as ptypes_last_7d,
           sum(total_item_qty) as qty_last_7d
    from base_data
    --where days_ago<=7
    where days_ago_since_last_transaction<=7
    group by 1,2
),

last_30_days_agg as (
    select dim_customer_key,
           cluster_theme,
           count(distinct cart_id) as carts_last_30d,
           count(distinct product_id) as pid_last_30d,
           count(distinct product_type_id) as ptypes_last_30d,
           sum(total_item_qty) as qty_last_30d
    from base_data
    --where days_ago<=30
    where days_ago_since_last_transaction<=40
    group by 1,2
),

inter_agg1 as (
    select bd1.dim_customer_key, 
           bd1.cluster_theme,
           bd1.cluster_category,
           customer_platform_carts,
           customer_last_trans_dt_platform,
           coalesce(carts_last_7d, 0) as carts_last_7d,
           coalesce(pid_last_7d, 0) as pid_last_7d,
           coalesce(ptypes_last_7d, 0) as ptypes_last_7d,
           coalesce(qty_last_7d, 0) as qty_last_7d,
           coalesce(carts_last_30d, 1) as carts_last_30d,
           coalesce(pid_last_30d, 1) as pid_last_30d,
           coalesce(ptypes_last_30d, 1) as ptypes_last_30d,
           coalesce(qty_last_30d, 1) as qty_last_30d,
           sum(total_item_qty) as qty_purchased_in_cluster, 
           count(distinct cart_id) as cart_count_in_cluster,
           count(distinct product_id) as pid_count_in_cluster,
           min(cart_checkout_ts_ist) as first_transaction_date_in_cluster,
           max(cart_checkout_ts_ist) as last_transaction_date_in_cluster,
           count(distinct metadata_name) as keyterms_purchased_in_cluster,
           date_diff('day', min(cart_checkout_ts_ist), max(cart_checkout_ts_ist)) as date_diff_days,
           CAST(COALESCE(((1.00000 * date_diff('day', min(cart_checkout_ts_ist), max(cart_checkout_ts_ist))) / NULLIF(count(distinct cart_id),0)),0.0) AS REAL) as date_diff_per_cart,
           date_diff('day', max(cart_checkout_ts_ist), customer_last_trans_dt_platform) as days_since_last_trans_in_cluster,
           date_diff('day', max(cart_checkout_ts_ist), current_date) as days_since_last_bought_in_cluster,
           coalesce(max(cluster_theme_mapping), 0) as cluster_theme_mapping,
           coalesce(max(explore_score_norm), 0) as explore_score_norm
    from base_data bd1
    left join last_7_days_agg l7d on l7d.dim_customer_key=bd1.dim_customer_key and l7d.cluster_theme=bd1.cluster_theme
    left join last_30_days_agg l30d on l30d.dim_customer_key=bd1.dim_customer_key and l30d.cluster_theme=bd1.cluster_theme
    group by 1,2,3,4,5,6,7,8,9,10,11,12,13
),

inter_agg2 as (
    select ia1.*,
           CAST(COALESCE(((1.00000 * qty_purchased_in_cluster) / NULLIF(cart_count_in_cluster,0)),0.0) AS REAL) as qty_per_cart,
           CAST(COALESCE(((1.00000 * pid_count_in_cluster) / NULLIF(cart_count_in_cluster,0)),0.0) AS REAL) as pid_per_cart,
           CAST(COALESCE(((1.00000 * cart_count_in_cluster) / NULLIF(customer_platform_carts,0)),0.0) AS REAL) as cluster_carts_per_platform_carts,
           power(CAST(COALESCE(((1.00000 * cart_count_in_cluster) / NULLIF(customer_platform_carts,0)),0.0) AS REAL),0.5) as sqrt_cluster_carts_per_platform_carts,
           power(0.95, days_since_last_trans_in_cluster) as decay_score,
           CAST(COALESCE(((1.00000 * qty_last_7d) / NULLIF(qty_last_30d,0)),0.0) AS REAL) as engagement_ratio_7d_30d,
           CAST(COALESCE(((1.00000 * ptypes_last_7d) / NULLIF(ptypes_last_30d,0)),0.0) AS REAL) as exploration_ratio_7d_30d,
           CAST(COALESCE(((1.00000 * carts_last_7d) / NULLIF(carts_last_30d,0)),0.0) AS REAL) as cart_count_ratio_7d_30d
    from inter_agg1 ia1
),

inter_agg3 as (
    select ia2.*,
           (1.00000 * ((sqrt_cluster_carts_per_platform_carts * decay_score) + (1.00000 + explore_score_norm) + (1.00000 + exploration_ratio_7d_30d) + (1.00000 + cart_count_ratio_7d_30d))) as score
    from inter_agg2 ia2
),

inter_agg4 as (
    select ia3.*,
           row_number() over (partition by dim_customer_key, cluster_category order by score desc) as rank
    from inter_agg3 ia3
),

inter_agg5 as (
    select ia4.*,
           (0.80 * score) - (0.20 * rank) as diversity_score
    from inter_agg4 ia4
),

inter_agg6 as (
    select dim_customer_key, 
           min(diversity_score) as min_score, 
           max(diversity_score) as max_score
    from inter_agg5
    group by 1
),

valid_clusters as (
    select dim_customer_key, 
           cluster_theme,
           1 as is_valid_cluster
    from base_data
    where is_primary=1
    group by 1,2,3
)

select ia5.*,
       min_score,
       max_score,
       CAST(COALESCE(((diversity_score - min_score)  / NULLIF((max_score - min_score),0)),0.0) AS DOUBLE) as normalised_score,
       COALESCE(is_valid_cluster,0) is_valid_cluster
from inter_agg5 ia5
join inter_agg6 ia6 on ia6.dim_customer_key=ia5.dim_customer_key
left join valid_clusters vc on vc.dim_customer_key=ia5.dim_customer_key and vc.cluster_theme=ia5.cluster_theme
       
        