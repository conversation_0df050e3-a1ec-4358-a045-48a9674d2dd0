{"cells": [{"cell_type": "code", "execution_count": null, "id": "72832767-021f-4d3f-bf16-9457e5d1a220", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys"]}, {"cell_type": "code", "execution_count": null, "id": "9e12da0e-b975-47d1-8394-c41784000827", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "c88559a4-cd65-4c77-993e-94236f2b69b3", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "198cbf8d-7220-480a-a8b6-770cd790d64b", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "id": "d17105dd-1fbb-47e7-ac76-61f49871a45a", "metadata": {}, "outputs": [], "source": ["trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "668bc6a2-9c07-4814-83f7-f7947216b9a5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "2415f7aa-94c2-4a75-bb16-25e71bef1b18", "metadata": {}, "source": ["## Generate explore themes for users and push to trino table"]}, {"cell_type": "code", "execution_count": null, "id": "5b6714ad-eaa5-4df1-9a6a-4a8fe683aa34", "metadata": {}, "outputs": [], "source": ["explore_table_name = \"personalisation_for_you_explore_interim\"\n", "kwargs_explore_tb = {\n", "    \"schema_name\": \"consumer_intelligence_etls\",\n", "    \"table_name\": explore_table_name,\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"dim_customer_key\",\n", "            \"type\": \"bigint\",\n", "            \"description\": \"dim_customer_key\",\n", "        },\n", "        {\n", "            \"name\": \"rfm_segment\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"rfm_segment\",\n", "        },\n", "        {\n", "            \"name\": \"theme\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"theme\",\n", "        },\n", "        {\n", "            \"name\": \"num_unique_pids\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"num_unique_pids\",\n", "        },\n", "        {\n", "            \"name\": \"num_carts\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"num_carts\",\n", "        },\n", "        {\n", "            \"name\": \"num_unique_brands\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"num_unique_brands\",\n", "        },\n", "        {\n", "            \"name\": \"num_unique_ptypes\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"num_unique_ptypes\",\n", "        },\n", "        {\n", "            \"name\": \"num_product_repeated\",\n", "            \"type\": \"double\",\n", "            \"description\": \"num_product_repeated\",\n", "        },\n", "        {\n", "            \"name\": \"num_brand_repeated\",\n", "            \"type\": \"double\",\n", "            \"description\": \"num_brand_repeated\",\n", "        },\n", "        {\n", "            \"name\": \"num_carts_mean\",\n", "            \"type\": \"double\",\n", "            \"description\": \"num_carts_mean\",\n", "        },\n", "        {\n", "            \"name\": \"num_carts_std\",\n", "            \"type\": \"double\",\n", "            \"description\": \"num_carts_std\",\n", "        },\n", "        {\n", "            \"name\": \"pop_explore_score\",\n", "            \"type\": \"double\",\n", "            \"description\": \"pop_explore_score\",\n", "        },\n", "        {\n", "            \"name\": \"pop_explore_score_norm\",\n", "            \"type\": \"double\",\n", "            \"description\": \"pop_explore_score_norm\",\n", "        },\n", "        {\n", "            \"name\": \"pid_per_cart\",\n", "            \"type\": \"double\",\n", "            \"description\": \"pid_per_cart\",\n", "        },\n", "        {\n", "            \"name\": \"brand_per_cart\",\n", "            \"type\": \"double\",\n", "            \"description\": \"brand_per_cart\",\n", "        },\n", "        {\n", "            \"name\": \"repeat_ratio\",\n", "            \"type\": \"double\",\n", "            \"description\": \"repeat_ratio\",\n", "        },\n", "        {\n", "            \"name\": \"explore_score\",\n", "            \"type\": \"double\",\n", "            \"description\": \"explore_score\",\n", "        },\n", "        {\n", "            \"name\": \"min_explore_score\",\n", "            \"type\": \"double\",\n", "            \"description\": \"min_explore_score\",\n", "        },\n", "        {\n", "            \"name\": \"max_explore_score\",\n", "            \"type\": \"double\",\n", "            \"description\": \"max_explore_score\",\n", "        },\n", "        {\n", "            \"name\": \"explore_score_norm\",\n", "            \"type\": \"double\",\n", "            \"description\": \"explore_score_norm\",\n", "        },\n", "        {\n", "            \"name\": \"user_mod\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"user_mod\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"dim_customer_key\", \"theme\"],\n", "    \"partition_key\": [\"user_mod\"],\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"Table for storing exploratory themes data needed for personalisation usecase\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "4109a0f9-cf37-4a29-bb2f-c60ae5f5a733", "metadata": {}, "outputs": [], "source": ["explore_query = f\"\"\"\n", "    with population_data as (\n", "        select rfm_segment,\n", "               theme,\n", "               avg(num_carts) as num_carts_mean, \n", "               stddev_pop(num_carts) as num_carts_std\n", "        from consumer_intelligence_etls.personalisation_for_you_base_data_agg\n", "        group by 1,2\n", "    ),\n", "    \n", "    explore_base as (\n", "        select dim_customer_key, \n", "               bd.rfm_segment, \n", "               bd.theme, \n", "               num_unique_pids, \n", "               num_carts, \n", "               num_unique_brands, \n", "               num_unique_ptypes, \n", "               num_product_repeated, \n", "               num_brand_repeated, \n", "               num_carts_mean,\n", "               num_carts_std,\n", "               (num_carts - num_carts_mean)/num_carts_std as pop_explore_score\n", "        from consumer_intelligence_etls.personalisation_for_you_base_data_agg bd\n", "        left join population_data pd on pd.rfm_segment=bd.rfm_segment and pd.theme=bd.theme\n", "    ),\n", "\n", "    tmp_customer_exp_score as (\n", "        select dim_customer_key, \n", "               min(pop_explore_score) as min_score, \n", "               max(pop_explore_score) as max_score\n", "        from explore_base\n", "        group by 1\n", "    ),\n", "\n", "    explore_base_2 as (\n", "        select eb.dim_customer_key, \n", "               rfm_segment, \n", "               theme, \n", "               num_unique_pids, \n", "               num_carts, \n", "               num_unique_brands, \n", "               num_unique_ptypes, \n", "               num_product_repeated, \n", "               num_brand_repeated, \n", "               num_carts_mean,\n", "               num_carts_std, \n", "               pop_explore_score, \n", "               (num_unique_pids*1.000/num_carts) as pid_per_cart,\n", "               (num_unique_brands*1.000/num_carts) as brand_per_cart,\n", "               (1-(coalesce(num_product_repeated,0)*1.000/num_unique_pids)) as repeat_ratio,\n", "               case when min_score = max_score then 1 else (pop_explore_score - min_score) / (max_score - min_score) end as pop_explore_score_norm\n", "        from explore_base eb\n", "        join tmp_customer_exp_score cs on eb.dim_customer_key=cs.dim_customer_key\n", "    ),\n", "\n", "    explore_base_3 as (\n", "        select *,\n", "               dim_customer_key%%10 as user_mod,\n", "               ((0.5 * pop_explore_score_norm) + (0.15 * pid_per_cart) + (0.15 * brand_per_cart) + (0.2 * repeat_ratio)) as explore_score\n", "        from explore_base_2\n", "    ),\n", "\n", "    explore_base_4 as (\n", "        select dim_customer_key,\n", "               min(explore_score) as min_explore_score,\n", "               max(explore_score) as max_explore_score\n", "        from explore_base_3\n", "        group by 1\n", "    )\n", "\n", "    select eb3.dim_customer_key, \n", "           rfm_segment, \n", "           theme, \n", "           num_unique_pids, \n", "           num_carts, \n", "           num_unique_brands, \n", "           num_unique_ptypes, \n", "           num_product_repeated, \n", "           num_brand_repeated, \n", "           num_carts_mean,\n", "           num_carts_std, \n", "           pop_explore_score, \n", "           pid_per_cart,\n", "           brand_per_cart,\n", "           repeat_ratio,\n", "           pop_explore_score_norm,\n", "           user_mod,\n", "           explore_score,\n", "           min_explore_score,\n", "           max_explore_score,\n", "           case when min_explore_score = max_explore_score then 1 else (explore_score - min_explore_score) / (max_explore_score - min_explore_score) end as explore_score_norm\n", "    from explore_base_3 eb3\n", "    join explore_base_4 eb4 on eb4.dim_customer_key=eb3.dim_customer_key\n", "    \n", "    \"\"\"\n", "pb.to_trino(explore_query, **kwargs_explore_tb)"]}, {"cell_type": "code", "execution_count": null, "id": "17cba10d-1a6b-45d3-b7b2-8e3dc185bb7e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "d5f5f48e-9168-402f-94fe-96509d149e66", "metadata": {}, "source": ["## Filter the explore interim table to get shortlisted themes"]}, {"cell_type": "code", "execution_count": null, "id": "485016e8-bf0c-4cb8-86ce-bc5d7d55387a", "metadata": {}, "outputs": [], "source": ["explore_filter_table_name = \"personalisation_for_you_explore\"\n", "kwargs_explore_filter_tb = {\n", "    \"schema_name\": \"consumer_intelligence_etls\",\n", "    \"table_name\": explore_filter_table_name,\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"user_mod\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"user_mod\",\n", "        },\n", "        {\n", "            \"name\": \"dim_customer_key\",\n", "            \"type\": \"bigint\",\n", "            \"description\": \"dim_customer_key\",\n", "        },\n", "        {\n", "            \"name\": \"theme\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"theme\",\n", "        },\n", "        {\n", "            \"name\": \"explore_score_norm\",\n", "            \"type\": \"double\",\n", "            \"description\": \"explore_score_norm\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"dim_customer_key\", \"theme\"],\n", "    \"partition_key\": [\"user_mod\"],\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"Table for storing exploratory themes data needed for personalisation usecase\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "aef0139a-bf1e-4efa-bb02-8bba39267901", "metadata": {}, "outputs": [], "source": ["explore_filter_query = f\"\"\"\n", "    \n", "    select user_mod,\n", "           dim_customer_key,\n", "           theme,\n", "           explore_score_norm\n", "    from consumer_intelligence_etls.personalisation_for_you_explore_interim\n", "    where user_mod is not null\n", "    and explore_score_norm>=0.6\n", "    and num_carts>3\n", "    \n", "    \"\"\"\n", "pb.to_trino(explore_filter_query, **kwargs_explore_filter_tb)"]}, {"cell_type": "code", "execution_count": null, "id": "f0f3858c-558b-4d79-af2f-3ff5a5b327dc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "51d0fb1a-3d5f-4df9-a365-a918ead19c9e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6884d1c7-40f8-4acb-8fea-46c4af6ca3ae", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}