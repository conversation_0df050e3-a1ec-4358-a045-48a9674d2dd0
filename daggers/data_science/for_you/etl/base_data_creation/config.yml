alert_configs:
  slack:
  - channel: bl-personalization-notifications
  - channel: bl-personalisation-dag-failures
concurrency: 3
dag_name: base_data_creation
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebooks:
- executor_config:
    load_type: medium
    node_type: spot
  name: base_data
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level1
- executor_config:
    load_type: low
    node_type: spot
  name: aggregate_data_mod_0
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level2
- executor_config:
    load_type: low
    node_type: spot
  name: aggregate_data_mod_1
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level2
- executor_config:
    load_type: low
    node_type: spot
  name: aggregate_data_mod_2
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level2
- executor_config:
    load_type: low
    node_type: spot
  name: aggregate_data_mod_3
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level2
- executor_config:
    load_type: low
    node_type: spot
  name: aggregate_data_mod_4
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level2
- executor_config:
    load_type: low
    node_type: spot
  name: aggregate_data_mod_5
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level2
- executor_config:
    load_type: low
    node_type: spot
  name: aggregate_data_mod_6
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level2
- executor_config:
    load_type: low
    node_type: spot
  name: aggregate_data_mod_7
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level2
- executor_config:
    load_type: low
    node_type: spot
  name: aggregate_data_mod_8
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level2
- executor_config:
    load_type: low
    node_type: spot
  name: aggregate_data_mod_9
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level2
- executor_config:
    load_type: medium
    node_type: spot
  name: aggregate_data_combine
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level3
- executor_config:
    load_type: low
    node_type: spot
  name: customer_explore_data
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level4
owner:
  email: <EMAIL>
  slack_id: U03SJ8DPFND
path: data_science/for_you/etl/base_data_creation
paused: false
pool: data_science_pool
project_name: for_you
schedule:
  end_date: '2025-08-31T00:00:00'
  interval: 45 19 * * *
  start_date: '2025-06-13T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 1
