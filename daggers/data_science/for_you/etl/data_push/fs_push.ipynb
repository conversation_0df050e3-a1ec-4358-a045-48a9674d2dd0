{"cells": [{"cell_type": "code", "execution_count": null, "id": "0b8405f2-19f9-4ef3-9927-44a209a8febe", "metadata": {}, "outputs": [], "source": ["!pip install -q kafka-python==2.0.2\n", "!pip install -q scipy==1.8.0\n", "!pip install -q aiohttp==3.8.1\n", "!pip install -q fsspec==2023.1.0\n", "!pip install -q aiobotocore==2.4.2\n", "!pip install -q pymysql==1.0.2\n", "!pip install -q gremlinpython==3.6.2\n", "!pip install -q botocore==1.27.59\n", "!pip install -q progressbar2==4.2.0\n", "!pip install -q backoff==2.2.1\n", "!pip install -q pandas==1.5.1\n", "!pip install -q pg8000==1.29.4\n", "!pip install -q opensearch-py==2.1.1\n", "!pip install -q boto3==1.24.59\n", "!pip install -q requests-aws4auth==1.2.2\n", "!pip install -q s3transfer==0.6.0\n", "!pip install -q aenum==3.1.11\n", "!pip install -q scramp==1.4.4\n", "!pip install -q python-utils==3.5.2\n", "!pip install -q awswrangler==2.19.0\n", "!pip install -q s3fs==2023.1.0\n", "!pip install -q sqlalchemy==1.3.23"]}, {"cell_type": "code", "execution_count": null, "id": "6b58f99c-c24e-4663-a489-80b390efcfd7", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys"]}, {"cell_type": "code", "execution_count": null, "id": "5ab653ad-dcd0-40cd-aea9-c93b02b3ad21", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "955c912c-cf5b-4504-8662-a263c630d01c", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "db093e73-91e9-4a54-8ca1-f99aa078e5c6", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import hashlib\n", "import json\n", "import numpy as np\n", "from kafka_utils import *"]}, {"cell_type": "code", "execution_count": null, "id": "0d7dc7f1-b53a-466c-ad02-1a70f4f6d57c", "metadata": {}, "outputs": [], "source": ["def generate_short_hash_key(data):\n", "    sorted_data = {k: sorted(v) for k, v in data.items()}\n", "    json_str = json.dumps(sorted_data, separators=(\",\", \":\"), sort_keys=True)\n", "    full_hash = hashlib.sha256(json_str.encode()).hexdigest()\n", "    return sorted_data, full_hash[:16]\n", "\n", "\n", "def create_brand_hash_lookup(df):\n", "    unique_brands = df[\"brand_id\"].dropna().unique()\n", "\n", "    lookup_data = []\n", "    for brand_id in unique_brands:\n", "        data = {\"brand\": [int(brand_id)]}\n", "        sorted_data, hashkey = generate_short_hash_key(data)\n", "        lookup_data.append(\n", "            {\"brand_id\": brand_id, \"collection_attribute\": sorted_data, \"hash_key\": hashkey}\n", "        )\n", "\n", "    return pd.DataFrame(lookup_data)\n", "\n", "\n", "def merge_with_hashkeys(df):\n", "    brand_hash_lookup = create_brand_hash_lookup(df)\n", "    df_merged = df.merge(brand_hash_lookup, on=\"brand_id\", how=\"left\")\n", "    return df_merged"]}, {"cell_type": "code", "execution_count": null, "id": "5ef86512-20f4-461e-ba54-12cfa645163a", "metadata": {"jupyter": {"outputs_hidden": true}, "tags": []}, "outputs": [], "source": ["## feature store constants\n", "fs_context_name = \"personalisation_for_you_1_0_0\"\n", "cdp_secrets = pb.get_secret(\n", "    \"dse/personalisation/recommendations-product-shelves-rankings/feature-store\"\n", ")\n", "broker = cdp_secrets[\"FEATURE_STORE_REALTIME_BOOTSTRAP_SERVERS\"]\n", "topic = \"profilestore.sql.properties\""]}, {"cell_type": "code", "execution_count": null, "id": "4a7047ee-6af1-4133-bc45-76a0789c481f", "metadata": {}, "outputs": [], "source": ["query = f\"SELECT * FROM consumer_intelligence_etls.personalisation_story_metadata_v1\"\n", "usecases_hash_map = pd.read_sql(query, con=pb.get_connection(\"[Warehouse] Trino\"))"]}, {"cell_type": "code", "execution_count": null, "id": "efdbdfdd-07a5-44a5-9a2d-b8f2f9dff393", "metadata": {}, "outputs": [], "source": ["selected_users = [14705771, 1088684, 6283992, 284259, 5020682]\n", "selected_users = \",\".join(map(str, selected_users))"]}, {"cell_type": "code", "execution_count": null, "id": "cf0323e1-5dfe-4e06-b1d4-012c1b281efe", "metadata": {}, "outputs": [], "source": ["selected_users"]}, {"cell_type": "code", "execution_count": null, "id": "3c6ca08c-7e17-4b75-87be-5dea6c45a3e0", "metadata": {}, "outputs": [], "source": ["def get_brand_loyalty_crtical_pids(user_mod):\n", "    query = f\"SELECT * FROM consumer_intelligence_etls.brand_loyalty_candidate_set_all_users where user_mod ={user_mod}\"\n", "    brand_loyal_chunk = pd.read_sql(query, con=pb.get_connection(\"[Warehouse] Trino\"))\n", "    brand_loyal_chunk = merge_with_hashkeys(brand_loyal_chunk)\n", "    user_brand_unique_brands = brand_loyal_chunk[[\"brand_id\", \"hash_key\"]].drop_duplicates(\n", "        subset=[\"brand_id\", \"hash_key\"]\n", "    )\n", "    hash_gen_candidate_set = user_brand_unique_brands[[\"hash_key\"]].merge(\n", "        brand_loyal_chunk[[\"hash_key\", \"dim_customer_key\", \"candidate_set\", \"theme\"]]\n", "    )\n", "    hash_gen_candidate_set.rename(columns={\"candidate_set\": \"critical_pids\"}, inplace=True)\n", "    return hash_gen_candidate_set"]}, {"cell_type": "code", "execution_count": null, "id": "e0277a99-d972-480f-b8d6-ba14cd267166", "metadata": {}, "outputs": [], "source": ["def get_usecase_crtical_pids(user_mod, usecases_hash_map):\n", "    query = f\"\"\"SELECT * FROM consumer_intelligence_etls.personalisation_for_you_customer_usecase_mapping where user_mod ={user_mod} \n", "    and normalised_score > 0.5 and dim_customer_key in ({selected_users})\"\"\"\n", "    customer_usecases_df = pd.read_sql(query, con=pb.get_connection(\"[Warehouse] Trino\"))\n", "    customer_usecases_df = customer_usecases_df.merge(\n", "        usecases_hash_map, left_on=\"cluster_theme\", right_on=\"name\"\n", "    )\n", "    user_unique_usecases = customer_usecases_df[[\"name\", \"hash_key\"]].drop_duplicates(\n", "        subset=[\"name\", \"hash_key\"]\n", "    )\n", "    hash_gen_candidate_set = user_unique_usecases[[\"hash_key\"]].merge(\n", "        customer_usecases_df[[\"hash_key\", \"dim_customer_key\", \"critical_pids\"]]\n", "    )\n", "    return hash_gen_candidate_set"]}, {"cell_type": "code", "execution_count": null, "id": "49eb0c88-e263-4ab2-be30-9b0e7a5e502c", "metadata": {}, "outputs": [], "source": ["# Push to <PERSON><PERSON><PERSON> (Feature Store)\n", "fs_context_name = \"personalisation_for_you_1_0_0\"\n", "entity_column = \"dim_customer_key\"\n", "entity_name = \"user\"\n", "context = fs_context_name\n", "ctx_value_col = \"user_information\""]}, {"cell_type": "code", "execution_count": null, "id": "72fc4134-5e01-4e4e-aee5-31ce0a125e29", "metadata": {"jupyter": {"outputs_hidden": true}, "tags": []}, "outputs": [], "source": ["for user_mod in range(10):\n", "    print(\"Generating mod:\", {user_mod})\n", "    loyal_brand_candidate_set_df = get_brand_loyalty_crtical_pids(user_mod)\n", "    usecases_candidate_set_df = get_usecase_crtical_pids(user_mod, usecases_hash_map)\n", "    hash_gen_candidate_set = pd.concat([loyal_brand_candidate_set_df, usecases_candidate_set_df])\n", "    hash_gen_candidate_set.rename(columns={\"candidate_set\": \"critical_pids\"}, inplace=True)\n", "    tmp_fs_data = hash_gen_candidate_set[[\"dim_customer_key\", \"hash_key\", \"critical_pids\"]]\n", "    tmp_fs_data[\"critical_pids\"] = tmp_fs_data[[\"critical_pids\"]].to_dict(orient=\"records\")\n", "    tmp_fs_data = (\n", "        tmp_fs_data.groupby([\"dim_customer_key\"])\n", "        .apply(lambda x: dict(zip(x[\"hash_key\"], x[\"critical_pids\"])))\n", "        .reset_index()\n", "    )\n", "    tmp_fs_data.columns = [\"dim_customer_key\", \"user_information\"]\n", "    tmp_fs_data[\"type\"] = \"stories\"\n", "    tmp_fs_data = (\n", "        tmp_fs_data.groupby([\"dim_customer_key\"])\n", "        .apply(lambda x: dict(zip(x[\"type\"], x[\"user_information\"])))\n", "        .reset_index()\n", "    )\n", "    tmp_fs_data.columns = [\"dim_customer_key\", \"user_information\"]\n", "    kafka_records_dict = tmp_fs_data.reset_index(drop=True).to_dict(\"records\")\n", "\n", "    print(len(kafka_records_dict))\n", "    push_to_kafka(\n", "        entities=[f\"{entity_name}:{i[entity_column]}\" for i in kafka_records_dict],\n", "        context=context,\n", "        ctx_properties=[{\"ctx_value\": json.dumps(i[ctx_value_col])} for i in kafka_records_dict],\n", "        dry_run=False,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "071001c8-85d8-4014-bf5c-6d6d7c1eb221", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "23499843-547e-46ce-81c6-06363ec91876", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}