{"cells": [{"cell_type": "code", "execution_count": null, "id": "e08682ea-e4b7-4cea-b1dd-abebbedcc4c0", "metadata": {}, "outputs": [], "source": ["!pip install -q kafka-python==2.0.2\n", "!pip install -q sqlalchemy==1.3.23\n", "# !pip install -q boto3==1.24.59\n", "# !pip install -q requests-aws4auth==1.2.2\n", "# !pip install -q s3transfer==0.6.0\n", "# !pip install -q awswrangler==2.19.0\n", "# !pip install -q s3fs==2023.1.0"]}, {"cell_type": "code", "execution_count": null, "id": "079c4493-cde5-449e-8e5c-842aa8df7a5a", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys"]}, {"cell_type": "code", "execution_count": null, "id": "93c1f1db-0cc6-4cbd-ba5e-357e2d1fc496", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "d0955197-2443-4713-8414-16d8b6d40f8b", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "06b8a79a-9e63-40c0-94e9-f94dfa300900", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "from datetime import datetime, timedelta\n", "import hashlib\n", "import time\n", "import json\n", "import ast\n", "import re\n", "from kafka_utils import *"]}, {"cell_type": "code", "execution_count": null, "id": "2290a25e-6151-44d5-989d-1e72bebfda1e", "metadata": {}, "outputs": [], "source": ["import asyncio\n", "import concurrent.futures\n", "import datetime as dt\n", "import threading\n", "import time\n", "from contextlib import contextmanager\n", "from datetime import datetime\n", "import sqlalchemy as sqla\n", "from sqlalchemy.dialects import postgresql\n", "from sqlalchemy.ext.automap import automap_base\n", "from sqlalchemy.orm import sessionmaker"]}, {"cell_type": "code", "execution_count": null, "id": "e9ac23ab-8abc-4ed5-8fef-f101502eedd1", "metadata": {"jupyter": {"outputs_hidden": true}, "tags": []}, "outputs": [], "source": ["story_metadata = pb.from_sheets(\n", "    \"1-9y1rhdjGYPVGS0AiggVEpFBJCb5HRIlQ5OSf-AE3jU\",\n", "    sheetname=\"dse_db_snapshot_story_metadata\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "390e50f4-cca3-4f8d-b5bb-5e28e407f2b1", "metadata": {}, "outputs": [], "source": ["# story_metadata = pd.read_csv('/home/<USER>/for you/candidate_set_generation/dse_db_snapshot_story_metadata.csv')\n", "# story_metadata = story_metadata[['hash_key','story_type', 'name', 'candidate_list','created_at','updated_at']]"]}, {"cell_type": "code", "execution_count": null, "id": "1d4940ba-0400-4f4b-831d-c69aaf7b2ed9", "metadata": {}, "outputs": [], "source": ["story_metadata[\"candidate_list\"] = story_metadata[\"candidate_list\"].apply(ast.literal_eval)\n", "# story_metadata['created_at'] = story_metadata['created_at'].apply(pd.Timestamp)\n", "# story_metadata['updated_at'] = story_metadata['updated_at'].apply(pd.Timestamp)"]}, {"cell_type": "code", "execution_count": null, "id": "3110a014-35f9-42db-84f3-7d97afdb5a81", "metadata": {}, "outputs": [], "source": ["story_metadata[\"created_at\"] = story_metadata[\"created_at\"].astype(int)\n", "story_metadata[\"updated_at\"] = story_metadata[\"updated_at\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "c3b16940-8c88-4a96-94da-a4d26570d3f0", "metadata": {}, "outputs": [], "source": ["def get_schema(loadtype):\n", "    base_data_table_name = \"personalisation_story_metadata_v1\"\n", "\n", "    kwargs_base_data_tb = {\n", "        \"schema_name\": \"consumer_intelligence_etls\",\n", "        \"table_name\": base_data_table_name,\n", "        \"column_dtypes\": [\n", "            {\n", "                \"name\": \"hash_key\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"brand hash key\",\n", "            },\n", "            {\n", "                \"name\": \"story_type\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"story type\",\n", "            },\n", "            {\n", "                \"name\": \"name\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"brand name\",\n", "            },\n", "            {\n", "                \"name\": \"candidate_list\",\n", "                \"type\": \"ARRAY(INTEGER)\",\n", "                \"description\": \"list of recommended product_ids\",\n", "            },\n", "            {\n", "                \"name\": \"created_at\",\n", "                \"type\": \"BIGINT\",\n", "                \"description\": \"create time\",\n", "            },\n", "            {\n", "                \"name\": \"updated_at\",\n", "                \"type\": \"BIGINT\",\n", "                \"description\": \"update time\",\n", "                \"default\": \"CURRENT_TIMESTAMP\",\n", "            },\n", "        ],\n", "        \"load_type\": loadtype,  # append, truncate or upsert\n", "        \"table_description\": \"Intermediate table for storing aggregated data needed for personalisation usecase\",\n", "        \"primary_key\": [\"hash_key\"],\n", "    }\n", "\n", "    return kwargs_base_data_tb"]}, {"cell_type": "code", "execution_count": null, "id": "9a4b1ea2-4a46-4fd4-a44a-1b06b3183be1", "metadata": {}, "outputs": [], "source": ["schema = get_schema(\"upsert\")\n", "pb.to_trino(story_metadata, **schema)"]}, {"cell_type": "code", "execution_count": null, "id": "b4222188-82b7-46a1-9509-9f1aecb19404", "metadata": {}, "outputs": [], "source": ["current_epoch = int(time.time())\n", "user_mods = \",\".join(map(str, [num for num in range(10)]))"]}, {"cell_type": "code", "execution_count": null, "id": "f2f1ad2c-9f48-46c9-8d10-ca9b1892e338", "metadata": {}, "outputs": [], "source": ["prod_df = pd.read_sql(\n", "    \"\"\"\n", "select * from dwh.dim_product where is_current and is_product_enabled\n", "\"\"\",\n", "    con=pb.get_connection(\"[Warehouse] Trino\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d142a6ba-5a2d-4343-b520-d1cb0d6e5c3b", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"select distinct brand_id from consumer_intelligence_etls.personalisation_for_you_brand_loyalty_metrics_all_users\n", "            where brand_loyal = True and user_mod in ({user_mods})\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "654f4991-fc17-4131-b103-5fcec649a0e4", "metadata": {}, "outputs": [], "source": ["brands = pd.read_sql(query, con=pb.get_connection(\"[Warehouse] Trino\"))\n", "story_metadata = pd.read_sql(\n", "    f\"\"\"select * from consumer_intelligence_etls.personalisation_story_metadata_v1\"\"\",\n", "    con=pb.get_connection(\"[Warehouse] Trino\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1d268bff-0e59-40f8-be37-94ea51cc8bef", "metadata": {}, "outputs": [], "source": ["brand_top_sales = pd.read_sql(\n", "    f\"\"\"\n", "    WITH ranked_products AS (\n", "        SELECT \n", "            dp.brand_id,\n", "            fsoid.product_id, \n", "            COUNT(DISTINCT cart_id) AS cart_count,  \n", "            ROW_NUMBER() OVER (PARTITION BY dp.brand_id ORDER BY COUNT(DISTINCT cart_id) DESC) AS rank\n", "        FROM \n", "            dwh.fact_sales_order_item_details fsoid\n", "        JOIN\n", "            dwh.dim_product dp ON dp.product_id = fsoid.product_id\n", "        WHERE \n", "            order_create_dt_ist > DATE_ADD('day', -90, CURRENT_DATE)\n", "            AND is_current \n", "            AND is_product_enabled\n", "        GROUP BY \n", "            dp.brand_id, fsoid.product_id\n", "    ),\n", "    \n", "    top_products AS (\n", "        SELECT \n", "            brand_id,\n", "            product_id\n", "        FROM ranked_products\n", "        WHERE rank <= 5\n", "    )\n", "    \n", "    SELECT \n", "        brand_id,\n", "        ARRAY_AGG(product_id) AS candidate_list\n", "    FROM top_products\n", "    GROUP BY brand_id\n", "    \"\"\",\n", "    con=pb.get_connection(\"[Warehouse] Trino\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2d21b8cb-0a7f-4265-b189-ec020370d651", "metadata": {}, "outputs": [], "source": ["def generate_short_hash_key(data):\n", "    # Ensure keys are sorted, and arrays are sorted for consistency\n", "    sorted_data = {k: sorted(v) for k, v in data.items()}\n", "    # print(sorted_data)\n", "\n", "    # Convert to JSON string with sorted keys\n", "    json_str = json.dumps(sorted_data, separators=(\",\", \":\"), sort_keys=True)\n", "\n", "    # Generate SHA256 hash\n", "    full_hash = hashlib.sha256(json_str.encode()).hexdigest()\n", "\n", "    # Return only the first 16 characters of the hash\n", "    return sorted_data, full_hash[:16]\n", "\n", "\n", "def generate_collection_combination_and_hashkey(x):\n", "    brand_id = int(x[\"brand_id\"])\n", "    data = {\"brand\": [brand_id]}\n", "    sorted_data, hashkey = generate_short_hash_key(data)\n", "    return sorted_data, hashkey"]}, {"cell_type": "code", "execution_count": null, "id": "a93b4a8f-1bb7-449c-a13c-d06562462669", "metadata": {"tags": []}, "outputs": [], "source": ["brands[[\"collection_attributes\", \"hash_key\"]] = brands.apply(\n", "    lambda x: generate_collection_combination_and_hashkey(x), axis=1, result_type=\"expand\"\n", ")\n", "new_brands = brands[~np.isin(brands[\"hash_key\"], story_metadata[\"hash_key\"])]"]}, {"cell_type": "code", "execution_count": null, "id": "21f847be-d5a8-4961-a087-d194d5de35a1", "metadata": {}, "outputs": [], "source": ["new_brands = prod_df.merge(new_brands)[\n", "    [\"brand_id\", \"brand_name\", \"hash_key\", \"collection_attributes\"]\n", "]\n", "new_brands.drop_duplicates(subset=[\"brand_id\", \"brand_name\", \"hash_key\"], inplace=True)\n", "new_brands[\"created_at\"] = current_epoch\n", "new_brands[\"updated_at\"] = current_epoch"]}, {"cell_type": "code", "execution_count": null, "id": "5e9683d8-e154-4552-b1f3-3d547cbb1862", "metadata": {}, "outputs": [], "source": ["new_brands = new_brands.merge(brand_top_sales)"]}, {"cell_type": "code", "execution_count": null, "id": "b643f06a-c78b-4bc9-818d-2d5894919697", "metadata": {}, "outputs": [], "source": ["new_brands.rename(columns={\"brand_name\": \"name\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "9154e102-68b0-4296-947f-84b3773c2a85", "metadata": {}, "outputs": [], "source": ["new_brands = new_brands[new_brands[\"candidate_list\"].apply(len) == 5]"]}, {"cell_type": "code", "execution_count": null, "id": "c210d5e9-89d6-40d9-8a1e-789050c8732e", "metadata": {"tags": []}, "outputs": [], "source": ["new_brands[\"story_type\"] = \"brand\""]}, {"cell_type": "code", "execution_count": null, "id": "05c0a980-493d-4dce-a870-e0cfe927ad92", "metadata": {"tags": []}, "outputs": [], "source": ["pb.to_trino(\n", "    new_brands[[\"hash_key\", \"name\", \"story_type\", \"candidate_list\", \"created_at\", \"updated_at\"]],\n", "    **schema\n", ")"]}, {"cell_type": "markdown", "id": "147913f8-d89a-4a09-82bb-3f77ce87fd5f", "metadata": {}, "source": ["#### Push brand story data to DSE DB"]}, {"cell_type": "code", "execution_count": null, "id": "4fabf0a7-97e3-4466-9ee6-6ca0cab0d5bf", "metadata": {}, "outputs": [], "source": ["@contextmanager\n", "def session_scope(s):\n", "    \"\"\"Provide a transactional scope around a series of operations.\"\"\"\n", "    session = s()\n", "    try:\n", "        yield session\n", "        session.commit()\n", "    except Exception as e:\n", "        session.rollback()\n", "        raise e\n", "    finally:\n", "        session.close()\n", "\n", "\n", "class PostgresSQLSink:\n", "    def __init__(self, engine):\n", "        self.engine = engine\n", "        self.session = sessionmaker(\n", "            autocommit=False, autoflush=False, expire_on_commit=False, bind=engine\n", "        )\n", "\n", "    def bulk_execute(self, queries, workers=2):\n", "        responses = []\n", "        with concurrent.futures.ThreadPoolExecutor(max_workers=workers) as executor:\n", "            futures = []\n", "            for i, query in enumerate(queries):\n", "                task_id = f\"{i + 1}\"\n", "                futures.append(executor.submit(self.execute, query, task_id=task_id))\n", "\n", "            for future in concurrent.futures.as_completed(futures):\n", "                result = future.result()\n", "                responses.append(result)\n", "                print(f\"{result['task_id']} complete... [{result['time_taken']}]\")\n", "        return responses\n", "\n", "    def execute(self, query, task_id=None):\n", "        if task_id is None:\n", "            task_id = \"execute\"\n", "\n", "        with session_scope(self.session) as session:\n", "            error = None\n", "            start = time.time()\n", "            try:\n", "                res = session.execute(query)\n", "            except Exception as e:\n", "                error = e\n", "\n", "            try:\n", "                result = res.rowcount\n", "            except:\n", "                result = None\n", "\n", "            return {\n", "                \"task_id\": task_id,\n", "                \"time_taken\": time.time() - start,\n", "                \"error\": error,\n", "                \"result\": result,\n", "                \"query\": query,\n", "            }"]}, {"cell_type": "code", "execution_count": null, "id": "d6cc459a-a36b-46ff-9769-1a31d72ce9d7", "metadata": {"tags": []}, "outputs": [], "source": ["def get_espina_db_connection():\n", "    dse_db_conn_params = pb.get_secret(\"dse/postgres/dse_db/airflow_etl_user\")\n", "    engine_url = dse_db_conn_params[\"uri\"]\n", "    engine = sqla.create_engine(engine_url, pool_size=1, echo=False)\n", "    return engine\n", "\n", "\n", "espina_db_connection = get_espina_db_connection()"]}, {"cell_type": "code", "execution_count": null, "id": "79b87b1a-d38d-4842-b0fa-2d1add7d2538", "metadata": {"tags": []}, "outputs": [], "source": ["table_name = \"personalisation_story_metadata\"\n", "hashkey_df_espina_push = new_brands"]}, {"cell_type": "code", "execution_count": null, "id": "aaaea5d8-27c9-4ddc-be5b-b495c58122e8", "metadata": {"tags": []}, "outputs": [], "source": ["# ####Test\n", "# hashkey_df_espina_push = story_metadata\n", "# hashkey_df_espina_push[\"story_type\"] = \"brand\"\n", "# hashkey_df_espina_push[\"collection_attributes\"] = ''"]}, {"cell_type": "code", "execution_count": null, "id": "09fd1d7a-5120-4c71-9a59-60cd0fff1428", "metadata": {}, "outputs": [], "source": ["hashkey_df_espina_push[\"name\"] = hashkey_df_espina_push[\"name\"].str.replace(\"'\", \"''\")"]}, {"cell_type": "code", "execution_count": null, "id": "b3eba7a4-5728-483a-8b09-b517a4565140", "metadata": {"tags": []}, "outputs": [], "source": ["hashkey_df_espina_push[\"story_type\"] = \"brand\"\n", "hashkey_df_espina_push.drop(\"brand_id\", axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "1dba7196-4d9c-4e9b-a7e3-852960866a08", "metadata": {}, "outputs": [], "source": ["queries = []\n", "query = f\"INSERT INTO {table_name} (hash_key, name, story_type, collection_attributes, candidate_list, created_at, updated_at) VALUES\\n\"\n", "chunksize = 1\n", "chunks = [\n", "    hashkey_df_espina_push[i : i + chunksize]\n", "    for i in range(0, hashkey_df_espina_push.shape[0], chunksize)\n", "]\n", "for chunk in chunks:\n", "    records = chunk.to_dict(\"records\")\n", "    values = \",\\n\".join(\n", "        [\n", "            f\"\"\"('{record[\"hash_key\"]}', '{record[\"name\"]}', '{record[\"story_type\"]}', '{json.dumps(record[\"collection_attributes\"])}', '{json.dumps(record[\"candidate_list\"])}', {record[\"created_at\"]}, {record[\"updated_at\"]})\"\"\"\n", "            for record in records\n", "        ]\n", "    )\n", "    queries.append(\n", "        query\n", "        + values\n", "        + \" ON CONFLICT (hash_key) DO UPDATE SET hash_key = EXCLUDED.hash_key, name = EXCLUDED.name, story_type = EXCLUDED.story_type, collection_attributes = EXCLUDED.collection_attributes, candidate_list = EXCLUDED.candidate_list, updated_at = EXCLUDED.updated_at;\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "765ad104-a4b1-496b-b68a-bc615657ed59", "metadata": {}, "outputs": [], "source": ["sink = PostgresSQLSink(espina_db_connection)\n", "start = time.time()\n", "results = sink.bulk_execute(queries, workers=2)\n", "print(f\"took .... {time.time() - start}\")"]}, {"cell_type": "code", "execution_count": null, "id": "65e57fb3-3860-4485-bf10-1a4ef2db028c", "metadata": {}, "outputs": [], "source": ["count_query = f\"\"\"select count(*) as count from {table_name}\"\"\"\n", "count = pd.read_sql_query(count_query, con=espina_db_connection)[\"count\"][0]\n", "count, hashkey_df_espina_push.shape[0]"]}, {"cell_type": "markdown", "id": "e67db2ce-716f-40ee-b97e-65b435c97072", "metadata": {"tags": []}, "source": ["#### Generate user brand interaction data for fs push"]}, {"cell_type": "code", "execution_count": null, "id": "06378d5a-9ce6-48c2-a1d3-97f886947029", "metadata": {}, "outputs": [], "source": ["user_mod_tables = [\n", "    f\"SELECT * FROM consumer_intelligence_etls.brand_loyalty_candidate_set_all_users where user_mod ={user_mod}\"\n", "    for user_mod in range(10)\n", "]\n", "union_all_query = \" UNION ALL \".join(user_mod_tables)"]}, {"cell_type": "code", "execution_count": null, "id": "d2ce4815-2cb0-472c-9000-951d94d0f0af", "metadata": {}, "outputs": [], "source": ["brand_loyal_chunk = pd.read_sql(union_all_query, con=pb.get_connection(\"[Warehouse] Trino\"))"]}, {"cell_type": "code", "execution_count": null, "id": "12be3667-8401-462d-9ac1-5effa03b73cd", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import hashlib\n", "import json\n", "\n", "\n", "# Step 1: Define the hashing functions\n", "def generate_short_hash_key(data):\n", "    sorted_data = {k: sorted(v) for k, v in data.items()}\n", "    json_str = json.dumps(sorted_data, separators=(\",\", \":\"), sort_keys=True)\n", "    full_hash = hashlib.sha256(json_str.encode()).hexdigest()\n", "    return sorted_data, full_hash[:16]\n", "\n", "\n", "def create_brand_hash_lookup(df):\n", "    unique_brands = df[\"brand_id\"].dropna().unique()\n", "\n", "    lookup_data = []\n", "    for brand_id in unique_brands:\n", "        data = {\"brand\": [int(brand_id)]}\n", "        sorted_data, hashkey = generate_short_hash_key(data)\n", "        lookup_data.append(\n", "            {\"brand_id\": brand_id, \"collection_attribute\": sorted_data, \"hash_key\": hashkey}\n", "        )\n", "\n", "    return pd.DataFrame(lookup_data)\n", "\n", "\n", "def merge_with_hashkeys(df):\n", "    brand_hash_lookup = create_brand_hash_lookup(df)\n", "    df_merged = df.merge(brand_hash_lookup, on=\"brand_id\", how=\"left\")\n", "    return df_merged\n", "\n", "\n", "# Suppose df is your full dataset with a \"brand_id\" column\n", "brand_loyal_chunk = merge_with_hashkeys(brand_loyal_chunk)"]}, {"cell_type": "code", "execution_count": null, "id": "e382f823-168a-4f22-9110-3287a8cae4c3", "metadata": {}, "outputs": [], "source": ["user_brand_unique_brands = brand_loyal_chunk[\n", "    [\"brand_id\", \"hash_key\", \"collection_attribute\"]\n", "].drop_duplicates(subset=[\"brand_id\", \"hash_key\"])"]}, {"cell_type": "code", "execution_count": null, "id": "c759a20e-9184-472d-93ae-05d93902cb21", "metadata": {}, "outputs": [], "source": ["hash_gen_candidate_set = user_brand_unique_brands[[\"collection_attribute\", \"hash_key\"]].merge(\n", "    brand_loyal_chunk[[\"hash_key\", \"dim_customer_key\", \"candidate_set\", \"theme\"]]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b7b914f4-f45d-4e0b-b1f7-579040b0cdf3", "metadata": {}, "outputs": [], "source": ["hash_gen_candidate_set.rename(columns={\"candidate_set\": \"critical_pids\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "971ff01b-93fe-4ab7-872c-339a5c087366", "metadata": {"tags": []}, "outputs": [], "source": ["tmp_fs_data = hash_gen_candidate_set[[\"dim_customer_key\", \"hash_key\", \"critical_pids\"]]\n", "tmp_fs_data[\"critical_pids\"] = tmp_fs_data[[\"critical_pids\"]].to_dict(orient=\"records\")\n", "tmp_fs_data = (\n", "    tmp_fs_data.groupby([\"dim_customer_key\"])\n", "    .apply(lambda x: dict(zip(x[\"hash_key\"], x[\"critical_pids\"])))\n", "    .reset_index()\n", ")\n", "tmp_fs_data.columns = [\"dim_customer_key\", \"user_information\"]\n", "tmp_fs_data[\"type\"] = \"stories\"\n", "tmp_fs_data = (\n", "    tmp_fs_data.groupby([\"dim_customer_key\"])\n", "    .apply(lambda x: dict(zip(x[\"type\"], x[\"user_information\"])))\n", "    .reset_index()\n", ")\n", "tmp_fs_data.columns = [\"dim_customer_key\", \"user_information\"]\n", "kafka_records_dict = tmp_fs_data.reset_index(drop=True).to_dict(\"records\")"]}, {"cell_type": "code", "execution_count": null, "id": "57ea6686-cb03-453a-a031-58990e8b7849", "metadata": {"tags": []}, "outputs": [], "source": ["len(kafka_records_dict)"]}, {"cell_type": "code", "execution_count": null, "id": "7434f81d-6e9e-4b5c-9cb6-c9b7730c820c", "metadata": {}, "outputs": [], "source": ["## feature store constants\n", "fs_context_name = \"personalisation_for_you_1_0_0\"\n", "cdp_secrets = pb.get_secret(\n", "    \"dse/personalisation/recommendations-product-shelves-rankings/feature-store\"\n", ")\n", "broker = cdp_secrets[\"FEATURE_STORE_REALTIME_BOOTSTRAP_SERVERS\"]\n", "topic = \"profilestore.sql.properties\""]}, {"cell_type": "code", "execution_count": null, "id": "7137ac99-752a-4527-be09-6c40c4e42ca1", "metadata": {}, "outputs": [], "source": ["# Push to <PERSON><PERSON><PERSON> (Feature Store)\n", "entity_column = \"dim_customer_key\"\n", "entity_name = \"user\"\n", "context = fs_context_name\n", "ctx_value_col = \"user_information\"\n", "\n", "push_to_kafka(\n", "    entities=[f\"{entity_name}:{i[entity_column]}\" for i in kafka_records_dict],\n", "    context=context,\n", "    ctx_properties=[{\"ctx_value\": json.dumps(i[ctx_value_col])} for i in kafka_records_dict],\n", "    dry_run=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "45645226-9837-4b98-90cd-ea8a1649bbc7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "44a9b819-06eb-420e-a41b-476830012136", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}