{"cells": [{"cell_type": "code", "execution_count": null, "id": "1ee3970b-ab48-4348-aed9-8a7e9bdb1ad7", "metadata": {}, "outputs": [], "source": ["!pip install -q kafka-python==2.0.2\n", "!pip install -q scipy==1.8.0\n", "!pip install -q aiohttp==3.8.1\n", "!pip install -q fsspec==2023.1.0\n", "!pip install -q aiobotocore==2.4.2\n", "!pip install -q pymysql==1.0.2\n", "!pip install -q gremlinpython==3.6.2\n", "!pip install -q botocore==1.27.59\n", "!pip install -q progressbar2==4.2.0\n", "!pip install -q backoff==2.2.1\n", "!pip install -q pandas==1.5.1\n", "!pip install -q pg8000==1.29.4\n", "!pip install -q opensearch-py==2.1.1\n", "!pip install -q boto3==1.24.59\n", "!pip install -q requests-aws4auth==1.2.2\n", "!pip install -q s3transfer==0.6.0\n", "!pip install -q aenum==3.1.11\n", "!pip install -q scramp==1.4.4\n", "!pip install -q python-utils==3.5.2\n", "!pip install -q awswrangler==2.19.0\n", "!pip install -q s3fs==2023.1.0\n", "!pip install -q sqlalchemy==1.3.23"]}, {"cell_type": "code", "execution_count": null, "id": "546315b7-e59e-4601-9f0b-fbd80abe5cee", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys"]}, {"cell_type": "code", "execution_count": null, "id": "bf99d1ee-1f5b-44ac-9e04-3bd34f7e21a1", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "6fa34a0b-2401-449e-aeee-c0aa88e4a32e", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "e8eda416-271f-4037-8033-b70f0bdff280", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "id": "d42e715a-4400-437c-8f05-4f0d3ccb784b", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import time"]}, {"cell_type": "code", "execution_count": null, "id": "5896ec72-69b7-4ef8-bd1b-8c5b103ab514", "metadata": {}, "outputs": [], "source": ["import json\n", "import hashlib"]}, {"cell_type": "code", "execution_count": null, "id": "d0abf5b3-e074-4dda-8698-39ddd5076781", "metadata": {}, "outputs": [], "source": ["import json\n", "import re"]}, {"cell_type": "code", "execution_count": null, "id": "8ff5e6ec-2938-4075-8c1d-d1ae79f340c5", "metadata": {}, "outputs": [], "source": ["import ast\n", "import gc"]}, {"cell_type": "code", "execution_count": null, "id": "8e537807-a44c-4657-997b-2bead1782df8", "metadata": {}, "outputs": [], "source": ["import sqlalchemy as sa\n", "import awswrangler as wr"]}, {"cell_type": "code", "execution_count": null, "id": "3c7c86b3-5109-490b-809f-174d316d1500", "metadata": {}, "outputs": [], "source": ["import asyncio\n", "import concurrent.futures\n", "import datetime as dt\n", "import threading\n", "import time\n", "from contextlib import contextmanager\n", "from datetime import datetime\n", "\n", "import boto3\n", "import pytz\n", "import sqlalchemy as sqla\n", "from sqlalchemy.dialects import postgresql\n", "from sqlalchemy.ext.automap import automap_base\n", "from sqlalchemy.orm import sessionmaker"]}, {"cell_type": "code", "execution_count": null, "id": "904de896-f5cd-46fc-9d12-b47998c4ef78", "metadata": {}, "outputs": [], "source": ["from kafka_utils import *"]}, {"cell_type": "code", "execution_count": null, "id": "a8cd3a8a-02b4-4a4c-9bf4-f8f694e79687", "metadata": {}, "outputs": [], "source": ["trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "51bb3703-fdc4-4ba4-9863-cbebc1337045", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "278e2d18-dabc-464d-bea1-105c410ed31f", "metadata": {}, "outputs": [], "source": ["def generate_short_hash_key(data):\n", "    # Ensure keys are sorted, and arrays are sorted for consistency\n", "    sorted_data = {k: sorted(v) for k, v in data.items()}\n", "    # print(sorted_data)\n", "\n", "    # Convert to JSON string with sorted keys\n", "    json_str = json.dumps(sorted_data, separators=(\",\", \":\"), sort_keys=True)\n", "\n", "    # Generate SHA256 hash\n", "    full_hash = hashlib.sha256(json_str.encode()).hexdigest()\n", "\n", "    # Return only the first 16 characters of the hash\n", "    return sorted_data, full_hash[:16]"]}, {"cell_type": "code", "execution_count": null, "id": "d3ec816e-caa0-4ade-a5f6-3d315e8f792b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a0ffa03a-c7b2-4d55-8bdd-03c624ab6675", "metadata": {}, "outputs": [], "source": ["tmp_dict_x = [\n", "    {\n", "        \"dim_customer_key\": 11113,\n", "        \"brand\": [\n", "            {\"brand_id\": 15599, \"critical_pids\": [530687, 545760, 508424]},\n", "            {\"brand_id\": 18253, \"critical_pids\": [584702, 597575, 584928]},\n", "            {\"brand_id\": 11180, \"critical_pids\": [460621, 460622, 460623]},\n", "            {\n", "                \"brand_id\": 10963,\n", "                \"critical_pids\": [\n", "                    537926,\n", "                    481211,\n", "                    531414,\n", "                    528797,\n", "                    457040,\n", "                ],\n", "            },\n", "        ],\n", "    },\n", "    {\n", "        \"dim_customer_key\": 1088684,\n", "        \"brand\": [\n", "            {\"brand_id\": 11882, \"critical_pids\": [500364, 543368, 627345, 578867, 500359]},\n", "            {\"brand_id\": 11932, \"critical_pids\": [490538, 490535, 490540]},\n", "            {\"brand_id\": 4772, \"critical_pids\": [541950, 287023, 337751, 546529, 563827]},\n", "            {\"brand_id\": 2221, \"critical_pids\": [437583, 16727, 16728, 381969, 381957]},\n", "        ],\n", "    },\n", "    {\n", "        \"dim_customer_key\": 14705771,\n", "        \"brand\": [\n", "            {\"brand_id\": 4019, \"critical_pids\": [347429, 28250]},\n", "            {\"brand_id\": 11496, \"critical_pids\": [507454, 513174, 567579]},\n", "            {\"brand_id\": 5755, \"critical_pids\": [357445, 94563, 103182, 529946]},\n", "        ],\n", "    },\n", "    {\n", "        \"dim_customer_key\": 17270352,\n", "        \"brand\": [\n", "            {\"brand_id\": 15599, \"critical_pids\": [530687, 545760, 508424]},\n", "            {\"brand_id\": 18253, \"critical_pids\": [584702, 597575, 584928]},\n", "            {\"brand_id\": 11180, \"critical_pids\": [460621, 460622, 460623]},\n", "            {\n", "                \"brand_id\": 10963,\n", "                \"critical_pids\": [\n", "                    537926,\n", "                    481211,\n", "                    531414,\n", "                    528797,\n", "                    457040,\n", "                ],\n", "            },\n", "        ],\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "a9cdd3ef-4d38-4252-8977-02c73f9c98e2", "metadata": {}, "outputs": [], "source": ["tmp_df = pd.DataFrame.from_dict(tmp_dict_x).explode(\"brand\")"]}, {"cell_type": "code", "execution_count": null, "id": "3d029e0d-1563-44d0-b5c2-de2eaa113c92", "metadata": {}, "outputs": [], "source": ["tmp_df[[\"brand_id\", \"critical_pids\"]] = tmp_df[\"brand\"].apply(pd.Series)"]}, {"cell_type": "code", "execution_count": null, "id": "baf7bb05-3c6b-4505-b044-1228a3aa2dd6", "metadata": {}, "outputs": [], "source": ["tmp_df"]}, {"cell_type": "code", "execution_count": null, "id": "d003711b-9898-4092-9232-c035a9ea6469", "metadata": {}, "outputs": [], "source": ["tmp_df[\"story_type\"] = \"brand\""]}, {"cell_type": "code", "execution_count": null, "id": "0956076b-f6ec-4ec8-a24c-92c1ca81b7d9", "metadata": {}, "outputs": [], "source": ["tmp_df"]}, {"cell_type": "code", "execution_count": null, "id": "8606c929-8692-4710-94ee-1e5a56bc4d20", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2d906076-18ec-447c-90e9-4e45b45c541b", "metadata": {}, "outputs": [], "source": ["tmp_df.brand_id.unique()"]}, {"cell_type": "code", "execution_count": null, "id": "9754a94e-62b1-4f09-9658-20a68801c0c8", "metadata": {}, "outputs": [], "source": ["brand_pop_pids = pd.read_sql(\n", "    f\"\"\"\n", "    \n", "    WITH product_ranked AS (\n", "    SELECT \n", "        dp.brand_id, \n", "        dp.product_id, \n", "        COUNT(DISTINCT fsoid.cart_id) AS num_carts,\n", "        RANK() OVER (PARTITION BY dp.brand_id ORDER BY COUNT(DISTINCT fsoid.cart_id) DESC) AS rnk\n", "    FROM dwh.fact_sales_order_item_details fsoid\n", "    JOIN dwh.dim_product dp ON dp.product_id = fsoid.product_id\n", "    WHERE fsoid.order_create_dt_ist >= CURRENT_DATE - INTERVAL '90' DAY\n", "    AND dp.is_current \n", "    AND dp.is_product_enabled\n", "    AND dp.brand_id IN (15599, 18253, 11180, 10963, 11882, 11932, 4772, 2221, 4019, 11496, 5755)\n", "    GROUP BY dp.brand_id, dp.product_id\n", "    )\n", "    \n", "    SELECT brand_id, product_id, num_carts\n", "    FROM product_ranked\n", "    WHERE rnk <= 5\n", "\n", "\"\"\",\n", "    con=trino_con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ebbefeeb-99fd-43b7-a276-d46b1c0acf3a", "metadata": {}, "outputs": [], "source": ["brand_pop_pids = brand_pop_pids.groupby([\"brand_id\"]).agg({\"product_id\": list}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "b51f68d6-8b6b-45bf-a408-480483282b14", "metadata": {}, "outputs": [], "source": ["brand_pop_pids = brand_pop_pids.rename(columns={\"product_id\": \"candidate_list\"})"]}, {"cell_type": "code", "execution_count": null, "id": "20adb161-4518-4afd-a473-422989bcb572", "metadata": {}, "outputs": [], "source": ["brand_pop_pids"]}, {"cell_type": "code", "execution_count": null, "id": "14c5cda4-f37d-4ac6-b63c-8e05fb08a8dd", "metadata": {}, "outputs": [], "source": ["unique_brand_list = brand_pop_pids[\"brand_id\"].unique().tolist()\n", "unique_brand_list"]}, {"cell_type": "code", "execution_count": null, "id": "98802c81-5116-4b14-bd3b-34761cbe9f7d", "metadata": {}, "outputs": [], "source": ["hashkey_dict = {}\n", "for i in unique_brand_list:\n", "    data = {\"brand\": [i]}\n", "    sorted_data, hashkey = generate_short_hash_key(data)\n", "    candidate_list = brand_pop_pids[brand_pop_pids[\"brand_id\"] == i][\n", "        \"candidate_list\"\n", "    ].values.tolist()[0]\n", "    hashkey_dict[i] = {\n", "        \"collection_attributes\": sorted_data,\n", "        \"hash_key\": hashkey,\n", "        \"candidate_list\": candidate_list,\n", "    }"]}, {"cell_type": "code", "execution_count": null, "id": "346a4473-4c8f-4180-99df-604f13e7de59", "metadata": {}, "outputs": [], "source": ["hashkey_dict"]}, {"cell_type": "code", "execution_count": null, "id": "a3ee1cb8-7280-4511-9ede-7767bc213ffa", "metadata": {}, "outputs": [], "source": ["hashkey_df = pd.DataFrame(hashkey_dict.items(), columns=[\"brand_id\", \"hash_key\"])"]}, {"cell_type": "code", "execution_count": null, "id": "8209ffb0-a96d-4b55-948a-18fb402b4fbe", "metadata": {}, "outputs": [], "source": ["hashkey_df[[\"collection_attributes\", \"hash_key\", \"candidate_list\"]] = hashkey_df[\"hash_key\"].apply(\n", "    pd.Series\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "aadd31ae-6d77-48c4-b185-9f46a7d3182f", "metadata": {}, "outputs": [], "source": ["hashkey_df"]}, {"cell_type": "code", "execution_count": null, "id": "f026d43e-a69e-427e-b489-73b1bb288da8", "metadata": {}, "outputs": [], "source": ["hashkey_df[\"story_type\"] = \"brand\""]}, {"cell_type": "code", "execution_count": null, "id": "99175ec1-054d-4de1-a186-121c195fdebb", "metadata": {}, "outputs": [], "source": ["current_epoch = int(time.time())  # + (19800)  # 19800 is converting to IST, adding 5:30\n", "current_epoch"]}, {"cell_type": "code", "execution_count": null, "id": "bffc0ef7-82db-4883-9042-808c89e619e3", "metadata": {}, "outputs": [], "source": ["hashkey_df[\"created_at\"] = current_epoch\n", "hashkey_df[\"updated_at\"] = current_epoch"]}, {"cell_type": "code", "execution_count": null, "id": "6601691b-2f4d-48a4-9a32-d9eae3c072a7", "metadata": {}, "outputs": [], "source": ["hashkey_df[\"name\"] = \"test\""]}, {"cell_type": "code", "execution_count": null, "id": "e80ef274-939b-4e81-aa55-51a9bf4c2cd8", "metadata": {}, "outputs": [], "source": ["hashkey_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "e627fea4-aa96-4b69-bf15-447525ed83eb", "metadata": {}, "outputs": [], "source": ["hashkey_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9a96521e-7466-4f4d-9c70-b5f38df36b9c", "metadata": {}, "outputs": [], "source": ["hashkey_df_espina_push = hashkey_df[\n", "    [\n", "        \"hash_key\",\n", "        \"name\",\n", "        \"story_type\",\n", "        \"collection_attributes\",\n", "        \"candidate_list\",\n", "        \"created_at\",\n", "        \"updated_at\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "3717f6ee-61d0-4bb5-9566-109cf92fb87b", "metadata": {}, "outputs": [], "source": ["hashkey_df_espina_push.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c754b6fc-160c-43c8-a9b7-174a77bb559f", "metadata": {}, "outputs": [], "source": ["hashkey_df_espina_push.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d0f67231-4982-4d5c-ba10-3be87b4d1048", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "c341925d-df18-442e-ae74-678e962c9f89", "metadata": {}, "source": ["## push to DSE DB"]}, {"cell_type": "code", "execution_count": null, "id": "c4c23b95-859c-49da-b9c6-a2ff5e46728e", "metadata": {}, "outputs": [], "source": ["@contextmanager\n", "def session_scope(s):\n", "    \"\"\"Provide a transactional scope around a series of operations.\"\"\"\n", "    session = s()\n", "    try:\n", "        yield session\n", "        session.commit()\n", "    except Exception as e:\n", "        session.rollback()\n", "        raise e\n", "    finally:\n", "        session.close()\n", "\n", "\n", "class PostgresSQLSink:\n", "    def __init__(self, engine):\n", "        self.engine = engine\n", "        self.session = sessionmaker(\n", "            autocommit=False, autoflush=False, expire_on_commit=False, bind=engine\n", "        )\n", "\n", "    def bulk_execute(self, queries, workers=2):\n", "        responses = []\n", "        with concurrent.futures.ThreadPoolExecutor(max_workers=workers) as executor:\n", "            futures = []\n", "            for i, query in enumerate(queries):\n", "                task_id = f\"{i + 1}\"\n", "                futures.append(executor.submit(self.execute, query, task_id=task_id))\n", "\n", "            for future in concurrent.futures.as_completed(futures):\n", "                result = future.result()\n", "                responses.append(result)\n", "                print(f\"{result['task_id']} complete... [{result['time_taken']}]\")\n", "        return responses\n", "\n", "    def execute(self, query, task_id=None):\n", "        if task_id is None:\n", "            task_id = \"execute\"\n", "\n", "        with session_scope(self.session) as session:\n", "            error = None\n", "            start = time.time()\n", "            try:\n", "                res = session.execute(query)\n", "            except Exception as e:\n", "                error = e\n", "\n", "            try:\n", "                result = res.rowcount\n", "            except:\n", "                result = None\n", "\n", "            return {\n", "                \"task_id\": task_id,\n", "                \"time_taken\": time.time() - start,\n", "                \"error\": error,\n", "                \"result\": result,\n", "                \"query\": query,\n", "            }"]}, {"cell_type": "code", "execution_count": null, "id": "28e8ff7f-d46b-407c-b0ce-eeea87d6f822", "metadata": {}, "outputs": [], "source": ["def get_espina_db_connection():\n", "    dse_db_conn_params = pb.get_secret(\"dse/postgres/dse_db/airflow_etl_user\")\n", "    engine_url = dse_db_conn_params[\"uri\"]\n", "    engine = sqla.create_engine(engine_url, pool_size=1, echo=False)\n", "    return engine"]}, {"cell_type": "code", "execution_count": null, "id": "b04c65a1-bf73-4ef5-a85f-8facca52078d", "metadata": {}, "outputs": [], "source": ["espina_db_connection = get_espina_db_connection()"]}, {"cell_type": "code", "execution_count": null, "id": "7703e307-3073-454c-b2ad-71cc204b8dd2", "metadata": {}, "outputs": [], "source": ["table_name = \"personalisation_story_metadata\""]}, {"cell_type": "code", "execution_count": null, "id": "d7687872-d525-43ae-8d99-3b66b2302560", "metadata": {}, "outputs": [], "source": ["queries = []\n", "query = f\"INSERT INTO {table_name} (hash_key, name, story_type, collection_attributes, candidate_list, created_at, updated_at) VALUES\\n\"\n", "chunksize = 1000\n", "chunks = [\n", "    hashkey_df_espina_push[i : i + chunksize]\n", "    for i in range(0, hashkey_df_espina_push.shape[0], chunksize)\n", "]\n", "for chunk in chunks:\n", "    records = chunk.to_dict(\"records\")\n", "    values = \",\\n\".join(\n", "        [\n", "            f\"\"\"('{record[\"hash_key\"]}', '{record[\"name\"]}', '{record[\"story_type\"]}', '{json.dumps(record[\"collection_attributes\"])}', '{json.dumps(record[\"candidate_list\"])}', {record[\"created_at\"]}, {record[\"updated_at\"]})\"\"\"\n", "            for record in records\n", "        ]\n", "    )\n", "    queries.append(query + values + \" ON CONFLICT (hash_key) DO NOTHING;\")"]}, {"cell_type": "code", "execution_count": null, "id": "b6401b15-c766-487a-adbc-0b3a78b8795c", "metadata": {}, "outputs": [], "source": ["sink = PostgresSQLSink(espina_db_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "e49e0ddc-0966-40b7-9996-6f748a9d2966", "metadata": {}, "outputs": [], "source": ["start = time.time()\n", "results = sink.bulk_execute(queries, workers=2)\n", "print(f\"took .... {time.time() - start}\")"]}, {"cell_type": "code", "execution_count": null, "id": "996b0ea5-5def-4dfc-a350-c78eeb6f3576", "metadata": {}, "outputs": [], "source": ["count_query = f\"\"\"select count(*) as count from {table_name}\"\"\"\n", "count = pd.read_sql_query(count_query, con=espina_db_connection)[\"count\"][0]"]}, {"cell_type": "code", "execution_count": null, "id": "544471b0-8d45-44ca-87e4-06d9f613d6b5", "metadata": {}, "outputs": [], "source": ["count, hashkey_df_espina_push.shape[0]"]}, {"cell_type": "code", "execution_count": null, "id": "daa6a82c-ff2e-4cf7-8a04-92e26c4e073a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f884a16b-fe90-4789-853e-fba99daecc03", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "d3f83d47-d61c-4a5f-ba35-83891bdec317", "metadata": {}, "source": ["##  Feature store push"]}, {"cell_type": "code", "execution_count": null, "id": "3e348b9d-df3f-47dd-8d78-705533365ad7", "metadata": {}, "outputs": [], "source": ["## feature store constants\n", "cdp_secrets = pb.get_secret(\n", "    \"dse/personalisation/recommendations-product-shelves-rankings/feature-store\"\n", ")\n", "broker = cdp_secrets[\"FEATURE_STORE_REALTIME_BOOTSTRAP_SERVERS\"]\n", "topic = \"profilestore.sql.properties\""]}, {"cell_type": "code", "execution_count": null, "id": "21240409-010a-4126-baa8-d635cb56352b", "metadata": {}, "outputs": [], "source": ["fs_context_name = \"personalisation_for_you_1_0_0\""]}, {"cell_type": "code", "execution_count": null, "id": "c255d148-4812-4cff-9e4a-2db7b9d49a73", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "132258c2-0eda-4497-8098-1f1d894bf48d", "metadata": {}, "outputs": [], "source": ["hashkey_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "93974833-a246-4445-bdc5-79bf1be4926f", "metadata": {}, "outputs": [], "source": ["tmp_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6daa89f9-b75f-4c33-945a-781efe8fbb16", "metadata": {}, "outputs": [], "source": ["tmp_fs_data = tmp_df[[\"dim_customer_key\", \"brand_id\", \"critical_pids\"]].merge(\n", "    hashkey_df[[\"brand_id\", \"hash_key\"]].drop_duplicates(), on=\"brand_id\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d0cf4b9c-76b2-457f-a12a-9eef992ae84d", "metadata": {}, "outputs": [], "source": ["tmp_fs_data = tmp_fs_data[[\"dim_customer_key\", \"hash_key\", \"critical_pids\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "125707b7-1ecb-493d-82e7-d7c40fb44c11", "metadata": {}, "outputs": [], "source": ["tmp_fs_data[\"critical_pids\"] = tmp_fs_data[[\"critical_pids\"]].to_dict(orient=\"records\")"]}, {"cell_type": "code", "execution_count": null, "id": "73e2ac56-3a8e-4284-acde-e231030fe689", "metadata": {}, "outputs": [], "source": ["tmp_fs_data = (\n", "    tmp_fs_data.groupby([\"dim_customer_key\"])\n", "    .apply(lambda x: dict(zip(x[\"hash_key\"], x[\"critical_pids\"])))\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "79365eb5-a7d5-4a3e-936f-6f76b3783e66", "metadata": {}, "outputs": [], "source": ["tmp_fs_data.columns = [\"dim_customer_key\", \"user_information\"]"]}, {"cell_type": "code", "execution_count": null, "id": "b2323348-3370-4e82-b30f-43c05924daf0", "metadata": {}, "outputs": [], "source": ["tmp_fs_data[\"type\"] = \"stories\""]}, {"cell_type": "code", "execution_count": null, "id": "77e8c3e7-bc07-42c0-9058-274e3bf500be", "metadata": {}, "outputs": [], "source": ["tmp_fs_data = (\n", "    tmp_fs_data.groupby([\"dim_customer_key\"])\n", "    .apply(lambda x: dict(zip(x[\"type\"], x[\"user_information\"])))\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b05bef96-b4d7-412e-b8ca-6bed38f76a02", "metadata": {}, "outputs": [], "source": ["tmp_fs_data.columns = [\"dim_customer_key\", \"user_information\"]"]}, {"cell_type": "code", "execution_count": null, "id": "58522526-a957-4805-8b96-0be82ed4ec3f", "metadata": {}, "outputs": [], "source": ["tmp_fs_data"]}, {"cell_type": "code", "execution_count": null, "id": "7be96c94-848f-442c-839c-37b85a008bd7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6033c27e-f577-4dec-b670-15a9fd5c0bf0", "metadata": {}, "outputs": [], "source": ["kafka_records_dict = tmp_fs_data.reset_index(drop=True).to_dict(\"records\")"]}, {"cell_type": "code", "execution_count": null, "id": "d42c7075-0690-4d82-ad9e-0c6c92ffa3b6", "metadata": {}, "outputs": [], "source": ["len(kafka_records_dict)"]}, {"cell_type": "code", "execution_count": null, "id": "dce29312-9f5a-4554-810d-7309cc2c9d57", "metadata": {}, "outputs": [], "source": ["kafka_records_dict[0]"]}, {"cell_type": "code", "execution_count": null, "id": "24fba731-dcf2-4609-bbfc-9fe0e53ae34d", "metadata": {}, "outputs": [], "source": ["# Push to <PERSON><PERSON><PERSON> (Feature Store)\n", "entity_column = \"dim_customer_key\"\n", "entity_name = \"user\"\n", "context = fs_context_name\n", "ctx_value_col = \"user_information\"\n", "\n", "push_to_kafka(\n", "    entities=[f\"{entity_name}:{i[entity_column]}\" for i in kafka_records_dict],\n", "    context=context,\n", "    ctx_properties=[{\"ctx_value\": json.dumps(i[ctx_value_col])} for i in kafka_records_dict],\n", "    dry_run=False,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2611d27a-b8d7-4b79-bf31-b78ed738dce1", "metadata": {}, "outputs": [], "source": ["print(\"completed..\")"]}, {"cell_type": "code", "execution_count": null, "id": "ce146950-ffa8-4c7b-ad82-08cda7cb9d9e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4553795c-40c9-4f35-b3aa-4d6770c43d10", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0c3adfda-0b85-4c02-be39-2a5cc48fbc1b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}