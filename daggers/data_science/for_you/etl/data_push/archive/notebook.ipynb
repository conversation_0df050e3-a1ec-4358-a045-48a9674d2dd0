{"cells": [{"cell_type": "code", "execution_count": null, "id": "dad52311-3786-4773-8118-9bcc9d67b923", "metadata": {"tags": []}, "outputs": [], "source": ["!pip install -q kafka-python==2.0.2\n", "!pip install -q sqlalchemy==1.3.23\n", "# !pip install -q boto3==1.24.59\n", "# !pip install -q requests-aws4auth==1.2.2\n", "# !pip install -q s3transfer==0.6.0\n", "# !pip install -q awswrangler==2.19.0\n", "# !pip install -q s3fs==2023.1.0"]}, {"cell_type": "code", "execution_count": null, "id": "2b13987e-e1ff-4901-9e86-1881a75eca9a", "metadata": {}, "outputs": [], "source": ["!pip install openpyxl"]}, {"cell_type": "code", "execution_count": null, "id": "d5682bd0-ffd3-4bd9-b93f-393d69e84516", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys"]}, {"cell_type": "code", "execution_count": null, "id": "78e610d1-ff58-454e-91d1-d1fa79c59754", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "964d36cd-7daa-4ccc-a306-d3d3a0599689", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "001da17e-0dcd-4415-84c0-6e069836bfb5", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "from datetime import datetime, timedelta\n", "import hashlib\n", "import time\n", "import json\n", "import ast\n", "import re\n", "from kafka_utils import *"]}, {"cell_type": "code", "execution_count": null, "id": "8dabddf4-8cab-4a3a-bb46-70174ab7afce", "metadata": {}, "outputs": [], "source": ["import asyncio\n", "import concurrent.futures\n", "import datetime as dt\n", "import threading\n", "import time\n", "from contextlib import contextmanager\n", "from datetime import datetime\n", "import sqlalchemy as sqla\n", "from sqlalchemy.dialects import postgresql\n", "from sqlalchemy.ext.automap import automap_base\n", "from sqlalchemy.orm import sessionmaker"]}, {"cell_type": "code", "execution_count": null, "id": "2a07b67d-6730-4f48-8f6a-b5671086e950", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)\n", "pd.set_option(\"display.max_rows\", None)\n", "pd.set_option(\"display.float_format\", lambda x: \"%.5f\" % x)\n", "pd.set_option(\"display.max_colwidth\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "e69f2057-1a5a-4ab9-a8e9-7bd83e7bcc51", "metadata": {}, "outputs": [], "source": ["def safe_literal_eval(val):\n", "    if isinstance(val, list):\n", "        return val  # Already a list, no need to parse\n", "    if pd.isna(val) or val in [\"nan\", \"NaN\", None, \"None\", \"\"]:\n", "        return \" \"\n", "\n", "    try:\n", "        # Replace unquoted `nan`/`NaN` with a placeholder string or None\n", "        val = str(val).replace(\"nan\", \"' '\").replace(\"NaN\", \"' '\")\n", "        return ast.literal_eval(val)\n", "    except (<PERSON><PERSON><PERSON><PERSON>, SyntaxError):\n", "        return \" \""]}, {"cell_type": "code", "execution_count": null, "id": "9fe97df7-7bed-4df6-8a7f-646d9d38571e", "metadata": {"tags": []}, "outputs": [], "source": ["query = f\"\"\"\n", "with max_dt as (\n", "  select\n", "    metadata_id,\n", "    metadata_type,\n", "    max(snapshot_ist) max_snapshot_ist\n", "  from\n", "    search_etls.meta_entity_product_relationship\n", "  where\n", "    snapshot_ist is not null\n", "    and metadata_type = 'KEYTERM'\n", "  group by\n", "    1,\n", "    2\n", "),\n", "sales as (\n", "  select\n", "    product_id,\n", "    sum(total_selling_price) as rev,\n", "    sum(product_quantity) as items\n", "  from\n", "    dwh.fact_sales_order_item_details o\n", "  WHERE\n", "    order_create_dt_ist > current_date - INTERVAL '60' DAY\n", "    AND order_create_dt_ist <= current_date - INTERVAL '1' DAY\n", "    AND o.city_name != ''\n", "    AND o.dim_customer_key IS NOT NULL\n", "    AND o.order_current_status = 'DELIVERED'\n", "  group by\n", "    1\n", "),\n", "metadata_agg as (\n", "  select\n", "    p.metadata_id,\n", "    p.metadata_name,\n", "    p.metadata_type,\n", "    p.tagged_product_id as product_id,\n", "    product_name,\n", "    l0_category, \n", "    l0_category_id\n", "  from\n", "    search_etls.meta_entity_product_relationship p\n", "    join max_dt m on m.metadata_id = p.metadata_id\n", "    and m.max_snapshot_ist = p.snapshot_ist\n", "    and m.metadata_type = p.metadata_type\n", "    join dwh.dim_product dp on dp.product_id = p.tagged_product_id\n", "    and dp.is_current\n", "    and dp.is_product_enabled\n", "  where\n", "    p.snapshot_ist is not null\n", "  group by\n", "    1,\n", "    2,\n", "    3,\n", "    4,\n", "    5,\n", "    6,\n", "    7\n", ")\n", "select\n", "  a.metadata_id,\n", "  a.metadata_name,\n", "  a.metadata_type,\n", "  a.product_id,\n", "  a.product_name,\n", "  a.l0_category, \n", "  a.l0_category_id, \n", "  COALESCE(b.rev,0) as rev,\n", "  COALESCE(b.items,0) as items\n", "from\n", "  metadata_agg a\n", "  left join sales b on b.product_id = a.product_id\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "79621890-e402-4dae-ac95-dc77f04919c8", "metadata": {}, "outputs": [], "source": ["keyterms_data = pd.read_sql(query, con=pb.get_connection(\"[Warehouse] Trino\"))"]}, {"cell_type": "code", "execution_count": null, "id": "6b37aa55-5333-4fb9-8695-8a36dcbc064f", "metadata": {}, "outputs": [], "source": ["top_pids = pd.read_sql(\n", "    \"\"\"\n", "select product_id, count(distinct cart_id) as cart_count from dwh.fact_sales_order_item_details\n", "where order_create_dt_ist > CURRENT_TIMESTAMP - INTERVAL '90' DAY\n", "group by product_id \n", "\"\"\",\n", "    con=pb.get_connection(\"[Warehouse] Trino\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "35ba8a8d-ebaa-4322-a11f-2cb3b49974ab", "metadata": {}, "outputs": [], "source": ["keyterms_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "457adc73-ecc4-4920-8c53-b9c4a6bc5633", "metadata": {}, "outputs": [], "source": ["# use_cases = pd.read_excel('filtered_usecases.xlsx', sheet_name = 'Sheet1')"]}, {"cell_type": "code", "execution_count": null, "id": "9487cfc8-2f52-4c1d-8547-2020d7f41bb9", "metadata": {"tags": []}, "outputs": [], "source": ["use_cases = pb.from_sheets(\n", "    \"1O4pJNS_PX7ibx4hkVtStxrlhZHvvwYg11orXfCNpDXw\",\n", "    sheetname=\"Sheet1\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "83efb9d7-7ff3-49a3-8a30-63b090a5d05c", "metadata": {}, "outputs": [], "source": ["use_cases.columns"]}, {"cell_type": "code", "execution_count": null, "id": "9867b814-0e7f-4a7b-bde1-4afc44511ac1", "metadata": {}, "outputs": [], "source": ["use_cases[\"sub_theme_items\"] = use_cases[\"sub_theme_items\"].apply(safe_literal_eval)\n", "use_cases[\"all_values\"] = use_cases[\"sub_theme_items\"].apply(\n", "    lambda d: [item for sublist in d.values() for item in sublist]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "abd0cdfc-5caa-4085-b112-fa29e0e24dcb", "metadata": {}, "outputs": [], "source": ["all_keyterms = use_cases[[\"cluster_theme\", \"all_values\", \"cluster_description\", \"theme_type\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "19aec9f8-e3c3-4518-a788-85974bb28953", "metadata": {}, "outputs": [], "source": ["all_keyterms = all_keyterms.explode(\"all_values\")"]}, {"cell_type": "code", "execution_count": null, "id": "61b12d21-1d2d-4494-88b9-1516b65e74cd", "metadata": {}, "outputs": [], "source": ["keyterms_data.columns"]}, {"cell_type": "code", "execution_count": null, "id": "5e0be1c7-dd08-40ed-9049-b8b67d230af0", "metadata": {}, "outputs": [], "source": ["all_keyterms.head()"]}, {"cell_type": "code", "execution_count": null, "id": "26406162-be79-484b-9c25-9819fa24075f", "metadata": {}, "outputs": [], "source": ["keyterms_data = keyterms_data.merge(all_keyterms, left_on=\"metadata_name\", right_on=\"all_values\")"]}, {"cell_type": "code", "execution_count": null, "id": "603d65a7-6318-4503-8fd7-f3645d2df148", "metadata": {"tags": []}, "outputs": [], "source": ["keyterms_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "839282ee-fdd9-48b8-a030-968283b70e86", "metadata": {}, "outputs": [], "source": ["# internal_user_ids = pd.read_csv('/home/<USER>/for you/champion_vip_brand_loyalty_metrics - internal_users_candidate_set.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "e954fc2f-bc93-41df-9cb6-288caf9b73b6", "metadata": {}, "outputs": [], "source": ["# user_ids = internal_user_ids.sample(200)['dim_customer_key'].to_list()"]}, {"cell_type": "code", "execution_count": null, "id": "b230fa00-61c7-4283-9a81-3a3da5f4f51b", "metadata": {"tags": []}, "outputs": [], "source": ["# user_ids = \",\".join(map(str,user_ids))"]}, {"cell_type": "code", "execution_count": null, "id": "12c44447-23f8-462b-890d-72af3511eda4", "metadata": {}, "outputs": [], "source": ["user_ids = \"1377490,14705771,59961558,284259,6283992,5777071,1088684,6983817,16146078,3852146,35055484,21433787,17951197,7503269,20496183,17356777,25350,28401920,3842774,8730103,4279596,662202,2498557,17139773,15721114,24788887,17266789,2048825,11620597,39978499,20150619,1731673,38879543,40774394,29798985,14762007,254,4570987,12649225,1031298,1348075,15563365,8666630,6670736,17684335,18177841,5817837,18947477,3812550,17573438,14097477,12043755,3841277,14971240,14894942,7503269,17939043,3462564,17769164,19762745,20935690,15822665,50859174,271624,11192686,56458956,1769176,35346526,23230150,53815283,1742254,54929293,23709725,3399891,45094389,5965183,65186138,15721114,16434939,14594983,8666630,17999078,253501,6849238,10954799,12043755,3565679,16146078,4843289,10631157,2783,7538937,16146078,9693518,17491139,14742349,12804018,17951197,10879,281074,17812043,14540845,54929293,2789670,1628956,36318053,23487292,5817837,326755,17144418,5481983,10455043,10767515,19730340,14044644,17443989,17325714,16260872,388429,874838,22101101,17325714,13203196,17356777,17270352,11996851,10920661,16930476,386253,239059,6493405,2048825,33657527,21134044,4423158,35346526,62866920,5100869,700822,22047861,26551539,10992475,23709725,22564864,23556158,35773346,2433416,29018802,29266767,20150619,15721114,17989225,19296553,662202,17571738,33162035,20720881,25694137,12751895,4207106,14758967,21433787,606173,13445,37211039,16266632,1088684,56458956,20779288,10401675,22330390,20191087,38873740,1830365,527030,248775,3444224,15721114,16050905,4144617,35055484,3578495,14894942,18947477,15637455,8973377,1228513,17491222,15435284,21009121,19730340,23355621,40774394,15745419,20720881,3462564,6925746,14812582,20150619,6298672,22101101,11312380,63242229,4741481,27371164,64195886,11113\""]}, {"cell_type": "code", "execution_count": null, "id": "38cf916a-d7f7-4703-a181-482d4a49eba4", "metadata": {}, "outputs": [], "source": ["user_ids = \"90057028, 5020682, 17270352, 10226560,6283992,284259,1088684,14705771,5777071\""]}, {"cell_type": "code", "execution_count": null, "id": "ccde801d-ed17-4665-9e3a-953c517b8993", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "select * from consumer_intelligence_etls.personalisation_for_you_base_data_tmp where dim_customer_key in ({user_ids})\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "3b1f5606-6118-4a0f-9310-486b51ffb209", "metadata": {}, "outputs": [], "source": ["tmp_trans = pd.read_sql(query, con=pb.get_connection(\"[Warehouse] Trino\"))"]}, {"cell_type": "code", "execution_count": null, "id": "e210222b-30fa-4a15-876e-4ea6369ff1d0", "metadata": {}, "outputs": [], "source": ["tmp_trans[\"cart_checkout_ts_ist\"] = pd.to_datetime(tmp_trans[\"cart_checkout_ts_ist\"])"]}, {"cell_type": "code", "execution_count": null, "id": "a80554f8-2b60-4bd8-be25-6417b7863c7c", "metadata": {}, "outputs": [], "source": ["tmp_trans.columns"]}, {"cell_type": "code", "execution_count": null, "id": "59c66380-d016-4dbd-b2c5-d486a9c90c66", "metadata": {}, "outputs": [], "source": ["tmp_trans.shape, keyterms_data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "98ab7867-dd47-4d59-a7c8-67fbf65c6a0f", "metadata": {}, "outputs": [], "source": ["temp = tmp_trans.merge(keyterms_data).drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "747b0a3d-c034-4c95-8621-99a161d83fd6", "metadata": {"tags": []}, "outputs": [], "source": ["temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "21f3f5d8-973e-42a1-a4a1-9ab7fea5e457", "metadata": {}, "outputs": [], "source": ["temp[\"cart_checkout_ts_ist\"] = pd.to_datetime(temp[\"cart_checkout_ts_ist\"])"]}, {"cell_type": "code", "execution_count": null, "id": "2ffccb37-bfab-4f4a-9356-fe57e524925f", "metadata": {}, "outputs": [], "source": ["agg_df = (\n", "    temp.groupby([\"dim_customer_key\", \"cluster_theme\", \"metadata_id\", \"metadata_name\"])\n", "    .agg(\n", "        {\n", "            \"total_item_qty\": \"sum\",\n", "            \"cart_id\": \"nunique\",\n", "            \"cart_checkout_ts_ist\": [\"min\", \"max\"],\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "37537f00-f066-4f31-ad16-6987bfb6aa4e", "metadata": {}, "outputs": [], "source": ["agg_df.columns = [\"_\".join(col).strip(\"_\") for col in agg_df.columns.values]\n", "agg_df[\"date_diff_days\"] = (\n", "    pd.to_datetime(agg_df[\"cart_checkout_ts_ist_max\"])\n", "    - pd.to_datetime(agg_df[\"cart_checkout_ts_ist_min\"])\n", ").dt.days"]}, {"cell_type": "code", "execution_count": null, "id": "28e0e68b-222d-475c-a28e-cdd68a976835", "metadata": {}, "outputs": [], "source": ["agg_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e41e0557-0de7-406f-ad4d-897d40d89bff", "metadata": {}, "outputs": [], "source": ["agg_df[\"date_diff_per_cart\"] = agg_df[\"date_diff_days\"] / agg_df[\"cart_id_nunique\"]"]}, {"cell_type": "code", "execution_count": null, "id": "0db40cb3-678a-426e-95de-15b324933fd5", "metadata": {}, "outputs": [], "source": ["last_trn = (\n", "    temp.groupby(\"dim_customer_key\")[\"cart_checkout_ts_ist\"]\n", "    .max()\n", "    .rename(\"last_trn_date\")\n", "    .reset_index()\n", ")\n", "agg_df = agg_df.merge(last_trn, on=\"dim_customer_key\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "59737a2a-79b3-4005-9c40-80c6a9c8c318", "metadata": {}, "outputs": [], "source": ["agg_df[\"days_since_last_trn_in_cluster\"] = (\n", "    agg_df[\"last_trn_date\"] - agg_df[\"cart_checkout_ts_ist_max\"]\n", ").dt.days"]}, {"cell_type": "code", "execution_count": null, "id": "e4d3ccb2-6e52-418a-bd61-cd5c69d18aff", "metadata": {}, "outputs": [], "source": ["temp[\"days_ago\"] = (pd.Timestamp.now() - temp[\"cart_checkout_ts_ist\"]).dt.days\n", "last_7d = temp[temp[\"days_ago\"] <= 7]\n", "last_30d = temp[temp[\"days_ago\"] <= 30]"]}, {"cell_type": "code", "execution_count": null, "id": "d5a5b8f9-3385-44fd-89e4-1fef54579303", "metadata": {}, "outputs": [], "source": ["agg_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e0e2c4b2-7def-4011-a51a-0afa0d8b2d45", "metadata": {}, "outputs": [], "source": ["agg_7d = (\n", "    last_7d.groupby([\"dim_customer_key\", \"cluster_theme\"])\n", "    .agg(\n", "        carts_last_7d=(\"cart_id\", \"nunique\"),\n", "        qty_last_7d=(\"total_item_qty\", \"sum\"),\n", "        ptypes_last_7d=(\"product_type\", \"nunique\"),\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "agg_30d = (\n", "    last_30d.groupby([\"dim_customer_key\", \"cluster_theme\"])\n", "    .agg(\n", "        carts_last_30d=(\"cart_id\", \"nunique\"),\n", "        qty_last_30d=(\"total_item_qty\", \"sum\"),\n", "        ptypes_last_30d=(\"product_type\", \"nunique\"),\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "agg_df = agg_df.merge(agg_7d, on=[\"dim_customer_key\", \"cluster_theme\"], how=\"left\")\n", "agg_df = agg_df.merge(agg_30d, on=[\"dim_customer_key\", \"cluster_theme\"], how=\"left\")\n", "\n", "# Fill NaNs for users with no 7d/30d data\n", "agg_df.fillna(\n", "    {\n", "        \"carts_last_7d\": 0,\n", "        \"qty_last_7d\": 0,\n", "        \"ptypes_last_7d\": 0,\n", "        \"carts_last_30d\": 1,\n", "        \"qty_last_30d\": 1,\n", "        \"ptypes_last_30d\": 1,  # avoid divide-by-zero\n", "    },\n", "    inplace=True,\n", ")\n", "\n", "# Momentum features\n", "agg_df[\"engagement_ratio_7d_30d\"] = agg_df[\"qty_last_7d\"] / agg_df[\"qty_last_30d\"]\n", "agg_df[\"exploration_ratio_7d_30d\"] = agg_df[\"ptypes_last_7d\"] / agg_df[\"ptypes_last_30d\"]\n", "agg_df[\"cart_count_ratio_7d_30d\"] = agg_df[\"carts_last_7d\"] / agg_df[\"carts_last_30d\"]\n", "\n", "\n", "agg_df[\"score\"] = (\n", "    (1 / (1 + agg_df[\"date_diff_per_cart\"]))\n", "    * (agg_df[\"total_item_qty_sum\"] / agg_df[\"cart_id_nunique\"])\n", "    * (0.95 ** agg_df[\"days_since_last_trn_in_cluster\"])\n", "    * (1 + agg_df[\"engagement_ratio_7d_30d\"])\n", "    * (1 + agg_df[\"exploration_ratio_7d_30d\"])\n", "    * (1 + agg_df[\"cart_count_ratio_7d_30d\"])\n", ") * 1\n", "\n", "\n", "filtered_df = agg_df.dropna().sort_values(\"score\", ascending=False)\n", "assigned_clusters = filtered_df.groupby(\"dim_customer_key\")[\"cluster_theme\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "9acbd391-ed53-4286-9780-2c521cc0403b", "metadata": {}, "outputs": [], "source": ["filtered_df = filtered_df[filtered_df[\"cart_id_nunique\"] > 1]"]}, {"cell_type": "code", "execution_count": null, "id": "96ab7e63-9c61-4c18-9b01-42ea8c912ba2", "metadata": {}, "outputs": [], "source": ["filtered_df.sort_values([\"dim_customer_key\", \"score\"], ascending=False).head()"]}, {"cell_type": "code", "execution_count": null, "id": "a52f7941-acfb-435f-a237-e2a5ba63e944", "metadata": {}, "outputs": [], "source": ["temp = keyterms_data.merge(top_pids).sort_values([\"cluster_theme\", \"cart_count\"], ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "ed849235-a1b1-42a1-8075-46568bc2456b", "metadata": {"tags": []}, "outputs": [], "source": ["temp = temp.groupby([\"cluster_theme\", \"metadata_id\"]).tail(1).reset_index(drop=True)\n", "temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bb7822b9-f00b-4a0e-96de-5095b76dd808", "metadata": {}, "outputs": [], "source": ["temp = temp.groupby([\"cluster_theme\"])[\"product_id\"].unique().reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "c5f37652-958e-433d-97ad-ac26d341f465", "metadata": {}, "outputs": [], "source": ["temp[\"product_id\"] = temp[\"product_id\"].apply(lambda x: x[:5])"]}, {"cell_type": "code", "execution_count": null, "id": "e1e7ff68-bfcf-4c8b-8dd8-b2acc10f4cb1", "metadata": {}, "outputs": [], "source": ["temp.rename(columns={\"product_id\": \"candidate_list\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "f659d60c-c376-4cf4-9205-6aefff91330e", "metadata": {}, "outputs": [], "source": ["usecases_keyterms = keyterms_data.groupby(\"cluster_theme\")[\"metadata_id\"].unique().reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "9d025a14-0ff0-42e5-9c01-2d6b2af3e76b", "metadata": {}, "outputs": [], "source": ["usecases_keyterms.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7637a5c6-de03-4575-9408-f9fbe1f00467", "metadata": {}, "outputs": [], "source": ["import hashlib"]}, {"cell_type": "code", "execution_count": null, "id": "8e7e8a36-71de-4993-92b5-850809202828", "metadata": {}, "outputs": [], "source": ["def generate_short_hash_key(data: dict) -> tuple[dict, str]:\n", "    # Convert all list elements to native Python types (int, str, etc.)\n", "    normalized_data = {k: sorted([int(x) for x in v]) for k, v in data.items()}\n", "\n", "    # Serialize to JSON with sorted keys\n", "    json_str = json.dumps(normalized_data, separators=(\",\", \":\"), sort_keys=True)\n", "\n", "    # Generate SHA256 hash\n", "    full_hash = hashlib.sha256(json_str.encode()).hexdigest()\n", "\n", "    # Return sorted data and first 16 chars of hash\n", "    return normalized_data, full_hash[:16]\n", "\n", "\n", "def generate_collection_combination_and_hashkey(row):\n", "    keyterm_ids = row[\"metadata_id\"]\n", "    data = {\"keyterm_id\": keyterm_ids}\n", "    return generate_short_hash_key(data)"]}, {"cell_type": "code", "execution_count": null, "id": "13261f99-4122-4f87-9cdc-fb85d47373d0", "metadata": {}, "outputs": [], "source": ["temp[\"created_at\"] = int(time.time())\n", "temp[\"updated_at\"] = int(time.time())"]}, {"cell_type": "code", "execution_count": null, "id": "b206e328-ecc6-45e1-9df3-79d61f3549db", "metadata": {"tags": []}, "outputs": [], "source": ["usecases_keyterms[[\"collection_attributes\", \"hash_key\"]] = usecases_keyterms.apply(\n", "    lambda x: generate_collection_combination_and_hashkey(x), axis=1, result_type=\"expand\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d1873b33-94c7-4888-95da-5ea24e0bd2be", "metadata": {}, "outputs": [], "source": ["temp = temp.merge(usecases_keyterms, on=\"cluster_theme\").rename(columns={\"cluster_theme\": \"name\"})"]}, {"cell_type": "code", "execution_count": null, "id": "1df1d9c1-26df-4327-a514-3843a73c2ece", "metadata": {}, "outputs": [], "source": ["temp[\"story_type\"] = \"usecases\""]}, {"cell_type": "code", "execution_count": null, "id": "07b162fd-a292-444d-b45a-5f12226b50ea", "metadata": {}, "outputs": [], "source": ["temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "da2e0af8-9bc4-4392-a453-340cc1a67958", "metadata": {}, "outputs": [], "source": ["temp[\"candidate_list\"] = temp[\"candidate_list\"].apply(list)"]}, {"cell_type": "code", "execution_count": null, "id": "7c973ea0-9716-4e6e-97dd-7484a7cc1fa8", "metadata": {}, "outputs": [], "source": ["temp[\"candidate_list\"] = temp[\"candidate_list\"].apply(lambda x: [int(i) for i in x])"]}, {"cell_type": "code", "execution_count": null, "id": "a735da07-1e4e-4ed1-8ed2-1cc1034f3b57", "metadata": {}, "outputs": [], "source": ["story_metadata = temp[\n", "    [\"hash_key\", \"story_type\", \"name\", \"candidate_list\", \"created_at\", \"updated_at\"]\n", "]"]}, {"cell_type": "markdown", "id": "dfac7eaf-12ee-40a6-a0ab-91860dcc4705", "metadata": {}, "source": ["#### Push to trino"]}, {"cell_type": "code", "execution_count": null, "id": "431ba879-032b-43de-a95e-b8ceca429523", "metadata": {}, "outputs": [], "source": ["def get_schema(loadtype):\n", "    base_data_table_name = \"personalisation_story_metadata_v1\"\n", "\n", "    kwargs_base_data_tb = {\n", "        \"schema_name\": \"consumer_intelligence_etls\",\n", "        \"table_name\": base_data_table_name,\n", "        \"column_dtypes\": [\n", "            {\n", "                \"name\": \"hash_key\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"brand hash key\",\n", "            },\n", "            {\n", "                \"name\": \"story_type\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"story type\",\n", "            },\n", "            {\n", "                \"name\": \"name\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"brand name\",\n", "            },\n", "            {\n", "                \"name\": \"candidate_list\",\n", "                \"type\": \"ARRAY(INTEGER)\",\n", "                \"description\": \"list of recommended product_ids\",\n", "            },\n", "            {\n", "                \"name\": \"created_at\",\n", "                \"type\": \"BIGINT\",\n", "                \"description\": \"create time\",\n", "            },\n", "            {\n", "                \"name\": \"updated_at\",\n", "                \"type\": \"BIGINT\",\n", "                \"description\": \"update time\",\n", "                \"default\": \"CURRENT_TIMESTAMP\",\n", "            },\n", "        ],\n", "        \"load_type\": loadtype,  # append, truncate or upsert\n", "        \"table_description\": \"Intermediate table for storing aggregated data needed for personalisation usecase\",\n", "        \"primary_key\": [\"hash_key\"],\n", "    }\n", "\n", "    return kwargs_base_data_tb\n", "\n", "\n", "schema = get_schema(\"upsert\")\n", "pb.to_trino(story_metadata, **schema)"]}, {"cell_type": "code", "execution_count": null, "id": "8cbfcda5-384e-40e1-b7b2-4d016a40f7f0", "metadata": {}, "outputs": [], "source": ["temp.head()"]}, {"cell_type": "markdown", "id": "e6a22426-1d20-4d29-a605-dd66b1854732", "metadata": {}, "source": ["#### Push to DSE DB"]}, {"cell_type": "code", "execution_count": null, "id": "4fabf0a7-97e3-4466-9ee6-6ca0cab0d5bf", "metadata": {}, "outputs": [], "source": ["@contextmanager\n", "def session_scope(s):\n", "    \"\"\"Provide a transactional scope around a series of operations.\"\"\"\n", "    session = s()\n", "    try:\n", "        yield session\n", "        session.commit()\n", "    except Exception as e:\n", "        session.rollback()\n", "        raise e\n", "    finally:\n", "        session.close()\n", "\n", "\n", "class PostgresSQLSink:\n", "    def __init__(self, engine):\n", "        self.engine = engine\n", "        self.session = sessionmaker(\n", "            autocommit=False, autoflush=False, expire_on_commit=False, bind=engine\n", "        )\n", "\n", "    def bulk_execute(self, queries, workers=2):\n", "        responses = []\n", "        with concurrent.futures.ThreadPoolExecutor(max_workers=workers) as executor:\n", "            futures = []\n", "            for i, query in enumerate(queries):\n", "                task_id = f\"{i + 1}\"\n", "                futures.append(executor.submit(self.execute, query, task_id=task_id))\n", "\n", "            for future in concurrent.futures.as_completed(futures):\n", "                result = future.result()\n", "                responses.append(result)\n", "                print(f\"{result['task_id']} complete... [{result['time_taken']}]\")\n", "        return responses\n", "\n", "    def execute(self, query, task_id=None):\n", "        if task_id is None:\n", "            task_id = \"execute\"\n", "\n", "        with session_scope(self.session) as session:\n", "            error = None\n", "            start = time.time()\n", "            try:\n", "                res = session.execute(query)\n", "            except Exception as e:\n", "                error = e\n", "\n", "            try:\n", "                result = res.rowcount\n", "            except:\n", "                result = None\n", "\n", "            return {\n", "                \"task_id\": task_id,\n", "                \"time_taken\": time.time() - start,\n", "                \"error\": error,\n", "                \"result\": result,\n", "                \"query\": query,\n", "            }"]}, {"cell_type": "code", "execution_count": null, "id": "d6cc459a-a36b-46ff-9769-1a31d72ce9d7", "metadata": {"tags": []}, "outputs": [], "source": ["def get_espina_db_connection():\n", "    dse_db_conn_params = pb.get_secret(\"dse/postgres/dse_db/airflow_etl_user\")\n", "    engine_url = dse_db_conn_params[\"uri\"]\n", "    engine = sqla.create_engine(engine_url, pool_size=1, echo=False)\n", "    return engine\n", "\n", "\n", "espina_db_connection = get_espina_db_connection()"]}, {"cell_type": "code", "execution_count": null, "id": "79b87b1a-d38d-4842-b0fa-2d1add7d2538", "metadata": {"tags": []}, "outputs": [], "source": ["table_name = \"personalisation_story_metadata\"\n", "hashkey_df_espina_push = temp"]}, {"cell_type": "code", "execution_count": null, "id": "aaaea5d8-27c9-4ddc-be5b-b495c58122e8", "metadata": {"tags": []}, "outputs": [], "source": ["# ####Test\n", "# hashkey_df_espina_push = story_metadata\n", "# hashkey_df_espina_push[\"story_type\"] = \"brand\"\n", "# hashkey_df_espina_push[\"collection_attributes\"] = ''"]}, {"cell_type": "code", "execution_count": null, "id": "09fd1d7a-5120-4c71-9a59-60cd0fff1428", "metadata": {}, "outputs": [], "source": ["hashkey_df_espina_push[\"name\"] = hashkey_df_espina_push[\"name\"].str.replace(\"'\", \"''\")"]}, {"cell_type": "code", "execution_count": null, "id": "b3eba7a4-5728-483a-8b09-b517a4565140", "metadata": {"tags": []}, "outputs": [], "source": ["hashkey_df_espina_push[\"story_type\"] = \"usecases\""]}, {"cell_type": "code", "execution_count": null, "id": "1dba7196-4d9c-4e9b-a7e3-852960866a08", "metadata": {}, "outputs": [], "source": ["queries = []\n", "query = f\"INSERT INTO {table_name} (hash_key, name, story_type, collection_attributes, candidate_list, created_at, updated_at) VALUES\\n\"\n", "chunksize = 1\n", "chunks = [\n", "    hashkey_df_espina_push[i : i + chunksize]\n", "    for i in range(0, hashkey_df_espina_push.shape[0], chunksize)\n", "]\n", "for chunk in chunks:\n", "    records = chunk.to_dict(\"records\")\n", "    values = \",\\n\".join(\n", "        [\n", "            f\"\"\"('{record[\"hash_key\"]}', '{record[\"name\"]}', '{record[\"story_type\"]}', '{json.dumps(record[\"collection_attributes\"])}', '{json.dumps(record[\"candidate_list\"])}', {record[\"created_at\"]}, {record[\"updated_at\"]})\"\"\"\n", "            for record in records\n", "        ]\n", "    )\n", "    queries.append(\n", "        query\n", "        + values\n", "        + \" ON CONFLICT (hash_key) DO UPDATE SET hash_key = EXCLUDED.hash_key, name = EXCLUDED.name, story_type = EXCLUDED.story_type, collection_attributes = EXCLUDED.collection_attributes, candidate_list = EXCLUDED.candidate_list, updated_at = EXCLUDED.updated_at;\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "765ad104-a4b1-496b-b68a-bc615657ed59", "metadata": {}, "outputs": [], "source": ["sink = PostgresSQLSink(espina_db_connection)\n", "start = time.time()\n", "results = sink.bulk_execute(queries, workers=2)\n", "print(f\"took .... {time.time() - start}\")"]}, {"cell_type": "code", "execution_count": null, "id": "65e57fb3-3860-4485-bf10-1a4ef2db028c", "metadata": {}, "outputs": [], "source": ["count_query = f\"\"\"select count(*) as count from {table_name}\"\"\"\n", "count = pd.read_sql_query(count_query, con=espina_db_connection)[\"count\"][0]\n", "count, hashkey_df_espina_push.shape[0]"]}, {"cell_type": "markdown", "id": "9602a1bb-2116-4111-880d-3e91afea04ea", "metadata": {}, "source": ["#### Push to <PERSON><PERSON><PERSON>"]}, {"cell_type": "raw", "id": "7426e8bd-7c84-4418-81e4-6cdc1161a863", "metadata": {}, "source": ["- Select top 20 disnctive use cases\n", "- Get all keterms associated to these\n", "- Generate hash for use cases, basis all keyterm ids\n", "- For top 5 keyterms across each use case get pids representing them as candidate list\n", "- Generate structure as required"]}, {"cell_type": "code", "execution_count": null, "id": "0461d49d-a922-43c2-98cc-5ba0bd86944c", "metadata": {}, "outputs": [], "source": ["filtered_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6ce39ef1-5b4f-4244-9af1-a831ef0e5657", "metadata": {}, "outputs": [], "source": ["filtered_df_kafka = filtered_df[[\"dim_customer_key\", \"cluster_theme\"]].merge(\n", "    temp, left_on=\"cluster_theme\", right_on=\"name\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "914bb563-0e27-4c31-bd6a-4d08512c85ff", "metadata": {}, "outputs": [], "source": ["filtered_df_kafka.shape"]}, {"cell_type": "code", "execution_count": null, "id": "61902012-3f98-44ac-a2ff-13bbed92a232", "metadata": {"tags": []}, "outputs": [], "source": ["filtered_df_kafka.drop_duplicates(subset=[\"dim_customer_key\", \"cluster_theme\"], inplace=True)\n", "filtered_df_kafka.shape"]}, {"cell_type": "code", "execution_count": null, "id": "b0cdf419-1702-4f4f-8b0f-2d4c8fe349ce", "metadata": {}, "outputs": [], "source": ["filtered_df_kafka = filtered_df_kafka[filtered_df_kafka[\"candidate_list\"].apply(len) == 5]"]}, {"cell_type": "code", "execution_count": null, "id": "11bd7dac-dbdc-4da7-a1ce-2f3ce950e65f", "metadata": {}, "outputs": [], "source": ["user_ids = [90057028, 5020682, 17270352, 10226560, 6283992, 284259, 1088684, 14705771, 5777071]"]}, {"cell_type": "code", "execution_count": null, "id": "e382f823-168a-4f22-9110-3287a8cae4c3", "metadata": {}, "outputs": [], "source": ["user_brand_unique_use_cases = filtered_df_kafka[\n", "    [\"metadata_id\", \"hash_key\", \"collection_attributes\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "5547bc5d-6ef7-463a-8edf-2de62bdaf064", "metadata": {}, "outputs": [], "source": ["user_brand_unique_use_cases.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c759a20e-9184-472d-93ae-05d93902cb21", "metadata": {}, "outputs": [], "source": ["hash_gen_candidate_set = user_brand_unique_use_cases[[\"collection_attributes\", \"hash_key\"]].merge(\n", "    filtered_df_kafka[[\"hash_key\", \"dim_customer_key\", \"candidate_list\", \"name\"]], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b7b914f4-f45d-4e0b-b1f7-579040b0cdf3", "metadata": {}, "outputs": [], "source": ["hash_gen_candidate_set.rename(columns={\"candidate_list\": \"critical_pids\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "861ec44d-919d-4ba8-8293-f18d41b48465", "metadata": {}, "outputs": [], "source": ["hash_gen_candidate_set[\"critical_pids\"] = hash_gen_candidate_set[\"critical_pids\"].apply(list)"]}, {"cell_type": "code", "execution_count": null, "id": "d1950ab3-2bfa-49af-9db6-e87fd85fcdbd", "metadata": {}, "outputs": [], "source": ["# hash_gen_candidate_set.drop_duplicates(subset = ['dim_customer_key','hash_key']).to_parquet('sample_data_usecases/user_level_story_candidate_sets.parquet')"]}, {"cell_type": "code", "execution_count": null, "id": "e1e8b222-5293-4589-8327-ac7f650ffa35", "metadata": {}, "outputs": [], "source": ["hash_gen_candidate_set.drop_duplicates(subset=[\"dim_customer_key\", \"hash_key\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "971ff01b-93fe-4ab7-872c-339a5c087366", "metadata": {"tags": []}, "outputs": [], "source": ["tmp_fs_data = hash_gen_candidate_set[[\"dim_customer_key\", \"hash_key\", \"critical_pids\"]]\n", "tmp_fs_data[\"critical_pids\"] = tmp_fs_data[[\"critical_pids\"]].to_dict(orient=\"records\")\n", "tmp_fs_data = (\n", "    tmp_fs_data.groupby([\"dim_customer_key\"])\n", "    .apply(lambda x: dict(zip(x[\"hash_key\"], x[\"critical_pids\"])))\n", "    .reset_index()\n", ")\n", "tmp_fs_data.columns = [\"dim_customer_key\", \"user_information\"]"]}, {"cell_type": "code", "execution_count": null, "id": "34694932-6b6a-4de6-bf11-d864375f9d93", "metadata": {}, "outputs": [], "source": ["additional_user_list = list(\n", "    set(user_ids).difference(set(filtered_df[\"dim_customer_key\"].unique().tolist()))\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "33d9c41e-78e9-4d88-b173-e412f4e3e2e4", "metadata": {}, "outputs": [], "source": ["sample_df = tmp_fs_data[tmp_fs_data[\"dim_customer_key\"].isin([1088684, 14705771, 11113])]\n", "additional_user_data = []\n", "for user_id in additional_user_list:\n", "    user_info = sample_df.iloc[user_id % len(sample_df)][\n", "        \"user_information\"\n", "    ]  # Loop over existing users\n", "    new_dim_customer_key = user_id\n", "    additional_user_data.append(\n", "        {\"dim_customer_key\": new_dim_customer_key, \"user_information\": user_info}\n", "    )\n", "\n", "additional_user_df = pd.DataFrame(additional_user_data)\n", "tmp_fs_data = pd.concat([tmp_fs_data, additional_user_df])"]}, {"cell_type": "code", "execution_count": null, "id": "a5340dbb-d8da-495a-b0b7-bc3d4a75c94a", "metadata": {}, "outputs": [], "source": ["tmp_fs_data[\"type\"] = \"stories\"\n", "tmp_fs_data = (\n", "    tmp_fs_data.groupby([\"dim_customer_key\"])\n", "    .apply(lambda x: dict(zip(x[\"type\"], x[\"user_information\"])))\n", "    .reset_index()\n", ")\n", "tmp_fs_data.columns = [\"dim_customer_key\", \"user_information\"]\n", "kafka_records_dict = tmp_fs_data.reset_index(drop=True).to_dict(\"records\")"]}, {"cell_type": "code", "execution_count": null, "id": "de2c2a42-e7fb-465a-be4d-8236e4205ed3", "metadata": {"tags": []}, "outputs": [], "source": ["len(kafka_records_dict)"]}, {"cell_type": "code", "execution_count": null, "id": "7434f81d-6e9e-4b5c-9cb6-c9b7730c820c", "metadata": {}, "outputs": [], "source": ["## feature store constants\n", "fs_context_name = \"personalisation_for_you_1_0_0\"\n", "cdp_secrets = pb.get_secret(\n", "    \"dse/personalisation/recommendations-product-shelves-rankings/feature-store\"\n", ")\n", "broker = cdp_secrets[\"FEATURE_STORE_REALTIME_BOOTSTRAP_SERVERS\"]\n", "topic = \"profilestore.sql.properties\""]}, {"cell_type": "code", "execution_count": null, "id": "7137ac99-752a-4527-be09-6c40c4e42ca1", "metadata": {}, "outputs": [], "source": ["# Push to <PERSON><PERSON><PERSON> (Feature Store)\n", "entity_column = \"dim_customer_key\"\n", "entity_name = \"user\"\n", "context = fs_context_name\n", "ctx_value_col = \"user_information\"\n", "\n", "push_to_kafka(\n", "    entities=[f\"{entity_name}:{i[entity_column]}\" for i in kafka_records_dict],\n", "    context=context,\n", "    ctx_properties=[{\"ctx_value\": json.dumps(i[ctx_value_col])} for i in kafka_records_dict],\n", "    dry_run=False,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9b71bf87-a434-4b12-8a24-b2fd8f19db4a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}