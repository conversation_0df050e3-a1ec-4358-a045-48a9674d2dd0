{"cells": [{"cell_type": "code", "execution_count": null, "id": "e7550250-4cc7-479d-b43e-a07e83dbb6ac", "metadata": {}, "outputs": [], "source": ["!pip install -q kafka-python==2.0.2\n", "!pip install -q scipy==1.8.0\n", "!pip install -q aiohttp==3.8.1\n", "!pip install -q fsspec==2023.1.0\n", "!pip install -q aiobotocore==2.4.2\n", "!pip install -q pymysql==1.0.2\n", "!pip install -q gremlinpython==3.6.2\n", "!pip install -q botocore==1.27.59\n", "!pip install -q progressbar2==4.2.0\n", "!pip install -q backoff==2.2.1\n", "!pip install -q pandas==1.5.1\n", "!pip install -q pg8000==1.29.4\n", "!pip install -q opensearch-py==2.1.1\n", "!pip install -q boto3==1.24.59\n", "!pip install -q requests-aws4auth==1.2.2\n", "!pip install -q s3transfer==0.6.0\n", "!pip install -q aenum==3.1.11\n", "!pip install -q scramp==1.4.4\n", "!pip install -q python-utils==3.5.2\n", "!pip install -q awswrangler==2.19.0\n", "!pip install -q s3fs==2023.1.0\n", "!pip install -q sqlalchemy==1.3.23"]}, {"cell_type": "code", "execution_count": null, "id": "ca365ae3-78db-4b30-8d84-7de5db654073", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys"]}, {"cell_type": "code", "execution_count": null, "id": "f840d884-d15f-40e4-b1df-348fa22cecbd", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "ade0ca3b-b83e-4a2b-a2fe-cdfb2812a7be", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "b068e29c-d8f1-40a6-b645-30e5ab13ae26", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "from datetime import datetime, timedelta\n", "import hashlib\n", "import time\n", "import json\n", "import ast\n", "import re\n", "from kafka_utils import *"]}, {"cell_type": "code", "execution_count": null, "id": "4b0da465-ae60-4e9c-9d76-cc7591be7100", "metadata": {}, "outputs": [], "source": ["import asyncio\n", "import concurrent.futures\n", "import datetime as dt\n", "import threading\n", "import time\n", "from contextlib import contextmanager\n", "from datetime import datetime\n", "import awswrangler as wr\n", "import boto3\n", "import pytz\n", "import sqlalchemy as sqla\n", "from sqlalchemy.dialects import postgresql\n", "from sqlalchemy.ext.automap import automap_base\n", "from sqlalchemy.orm import sessionmaker"]}, {"cell_type": "code", "execution_count": null, "id": "c0af4e3e-315f-4f3e-b47a-8a8b917045a5", "metadata": {}, "outputs": [], "source": ["trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "18a23a3d-da2e-4b9c-8e1f-a67742e37958", "metadata": {}, "outputs": [], "source": ["# all_brands = pd.read_csv('top_pids_unique_brands_list - brand_pids_list.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "268da9a1-3eaa-4307-a150-a8e8c382678d", "metadata": {}, "outputs": [], "source": ["all_brands = pb.from_sheets(\n", "    \"1yI7rgQk6_5dt27j7wP_6lNu6SmHemWljDVCc1nD4XqE\", sheetname=\"brand_pids_list\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ba9387bd-ca79-4801-9344-c12110e9d4da", "metadata": {}, "outputs": [], "source": ["all_brands = all_brands.rename(columns={\"product_id\": \"candidate_list\"})"]}, {"cell_type": "code", "execution_count": null, "id": "d395945a-4fdd-4159-9020-21242b75e882", "metadata": {"tags": []}, "outputs": [], "source": ["all_brands"]}, {"cell_type": "code", "execution_count": null, "id": "a5c83bea-1a0a-4cdc-9b2e-c9a467212a0f", "metadata": {}, "outputs": [], "source": ["all_brands[\"candidate_list\"] = all_brands[\"candidate_list\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "94b070e7-ef2c-4f8d-9bf8-be71649795e6", "metadata": {}, "outputs": [], "source": ["all_brands[\"candidate_list\"] = all_brands[\"candidate_list\"].apply(lambda x: x.replace(\"[\", \"\"))\n", "all_brands[\"candidate_list\"] = all_brands[\"candidate_list\"].apply(lambda x: x.replace(\"]\", \"\"))\n", "all_brands[\"candidate_list\"] = all_brands[\"candidate_list\"].apply(lambda x: x.strip().split())\n", "all_brands[\"candidate_list\"] = all_brands[\"candidate_list\"].apply(lambda x: [int(i) for i in x])"]}, {"cell_type": "code", "execution_count": null, "id": "00d6e87b-7a37-4245-a6be-978774d43888", "metadata": {}, "outputs": [], "source": ["def generate_short_hash_key(data):\n", "    # Ensure keys are sorted, and arrays are sorted for consistency\n", "    sorted_data = {k: sorted(v) for k, v in data.items()}\n", "    # print(sorted_data)\n", "\n", "    # Convert to JSON string with sorted keys\n", "    json_str = json.dumps(sorted_data, separators=(\",\", \":\"), sort_keys=True)\n", "\n", "    # Generate SHA256 hash\n", "    full_hash = hashlib.sha256(json_str.encode()).hexdigest()\n", "\n", "    # Return only the first 16 characters of the hash\n", "    return sorted_data, full_hash[:16]"]}, {"cell_type": "code", "execution_count": null, "id": "2a6883a6-fc3a-4124-aadf-b905498f40ad", "metadata": {}, "outputs": [], "source": ["def generate_collection_combination_and_hashkey(x):\n", "    brand_id = int(x[\"brand_id\"])\n", "    data = {\"brand\": [brand_id]}\n", "    sorted_data, hashkey = generate_short_hash_key(data)\n", "    return sorted_data, hashkey"]}, {"cell_type": "code", "execution_count": null, "id": "9e0ea054-e99e-4eb6-a71e-56cdfd09b100", "metadata": {}, "outputs": [], "source": ["all_brands[[\"collection_attributes\", \"hash_key\"]] = all_brands.apply(\n", "    lambda x: generate_collection_combination_and_hashkey(x), axis=1, result_type=\"expand\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ca508528-363e-4a1b-9aca-27c626694af9", "metadata": {}, "outputs": [], "source": ["current_epoch = int(time.time())\n", "current_epoch"]}, {"cell_type": "code", "execution_count": null, "id": "c6ff9acd-4db6-40fc-8c78-c15c079cb273", "metadata": {}, "outputs": [], "source": ["all_brands[\"created_at\"] = current_epoch\n", "all_brands[\"updated_at\"] = current_epoch"]}, {"cell_type": "code", "execution_count": null, "id": "29bc621e-8acf-4015-9647-7af74d426906", "metadata": {}, "outputs": [], "source": ["all_brands[\"story_type\"] = \"brand\""]}, {"cell_type": "code", "execution_count": null, "id": "953e5494-c2a5-4ad6-ad0c-b93782267fae", "metadata": {}, "outputs": [], "source": ["hashkey_df = all_brands[\n", "    [\n", "        \"collection_attributes\",\n", "        \"hash_key\",\n", "        \"created_at\",\n", "        \"updated_at\",\n", "        \"story_type\",\n", "        \"candidate_list\",\n", "        \"brand_name\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "1d31ae26-232d-4100-8e1b-c1b63c661422", "metadata": {}, "outputs": [], "source": ["hashkey_df[\"brand_name\"] = hashkey_df[\"brand_name\"].str.replace(\"'\", \"''\")\n", "hashkey_df.rename(columns={\"brand_name\": \"name\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "aece492a-4694-47ad-ad29-f75834c30e52", "metadata": {}, "outputs": [], "source": ["hashkey_df_espina_push = hashkey_df[\n", "    [\n", "        \"hash_key\",\n", "        \"name\",\n", "        \"story_type\",\n", "        \"collection_attributes\",\n", "        \"candidate_list\",\n", "        \"created_at\",\n", "        \"updated_at\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "7d6bcd91-f4d3-4db0-a822-3a9e446e9e4e", "metadata": {}, "outputs": [], "source": ["hashkey_df_espina_push.head()"]}, {"cell_type": "markdown", "id": "34530c75-2ba5-48a1-92d9-16f4bd94cf17", "metadata": {}, "source": ["#### Push to brand story metadata table"]}, {"cell_type": "code", "execution_count": null, "id": "9f3e05b3-29a5-4cf5-af72-3e7b8c7b283b", "metadata": {}, "outputs": [], "source": ["@contextmanager\n", "def session_scope(s):\n", "    \"\"\"Provide a transactional scope around a series of operations.\"\"\"\n", "    session = s()\n", "    try:\n", "        yield session\n", "        session.commit()\n", "    except Exception as e:\n", "        session.rollback()\n", "        raise e\n", "    finally:\n", "        session.close()\n", "\n", "\n", "class PostgresSQLSink:\n", "    def __init__(self, engine):\n", "        self.engine = engine\n", "        self.session = sessionmaker(\n", "            autocommit=False, autoflush=False, expire_on_commit=False, bind=engine\n", "        )\n", "\n", "    def bulk_execute(self, queries, workers=2):\n", "        responses = []\n", "        with concurrent.futures.ThreadPoolExecutor(max_workers=workers) as executor:\n", "            futures = []\n", "            for i, query in enumerate(queries):\n", "                task_id = f\"{i + 1}\"\n", "                futures.append(executor.submit(self.execute, query, task_id=task_id))\n", "\n", "            for future in concurrent.futures.as_completed(futures):\n", "                result = future.result()\n", "                responses.append(result)\n", "                print(f\"{result['task_id']} complete... [{result['time_taken']}]\")\n", "        return responses\n", "\n", "    def execute(self, query, task_id=None):\n", "        if task_id is None:\n", "            task_id = \"execute\"\n", "\n", "        with session_scope(self.session) as session:\n", "            error = None\n", "            start = time.time()\n", "            try:\n", "                res = session.execute(query)\n", "            except Exception as e:\n", "                error = e\n", "\n", "            try:\n", "                result = res.rowcount\n", "            except:\n", "                result = None\n", "\n", "            return {\n", "                \"task_id\": task_id,\n", "                \"time_taken\": time.time() - start,\n", "                \"error\": error,\n", "                \"result\": result,\n", "                \"query\": query,\n", "            }"]}, {"cell_type": "code", "execution_count": null, "id": "b9dfbfcd-e19c-4086-89c3-238e6d802cc0", "metadata": {}, "outputs": [], "source": ["def get_espina_db_connection():\n", "    dse_db_conn_params = pb.get_secret(\"dse/postgres/dse_db/airflow_etl_user\")\n", "    engine_url = dse_db_conn_params[\"uri\"]\n", "    engine = sqla.create_engine(engine_url, pool_size=1, echo=False)\n", "    return engine"]}, {"cell_type": "code", "execution_count": null, "id": "4889efc9-5e33-4e11-b29e-c8dd722b7956", "metadata": {}, "outputs": [], "source": ["espina_db_connection = get_espina_db_connection()"]}, {"cell_type": "code", "execution_count": null, "id": "52955f58-0903-4f9f-be32-1e77b7afd11f", "metadata": {}, "outputs": [], "source": ["table_name = \"personalisation_story_metadata\""]}, {"cell_type": "code", "execution_count": null, "id": "a25187e7-fed2-4163-8bab-8cba81cd116d", "metadata": {}, "outputs": [], "source": ["# queries = []\n", "# query = f\"INSERT INTO {table_name} (hash_key, name, story_type, collection_attributes, candidate_list, created_at, updated_at) VALUES\\n\"\n", "# chunksize = 1000\n", "# chunks = [\n", "#     hashkey_df_espina_push[i : i + chunksize]\n", "#     for i in range(0, hashkey_df_espina_push.shape[0], chunksize)\n", "# ]\n", "# for chunk in chunks:\n", "#     records = chunk.to_dict(\"records\")\n", "#     values = \",\\n\".join(\n", "#         [\n", "#             f\"\"\"('{record[\"hash_key\"]}', '{record[\"name\"]}', '{record[\"story_type\"]}', '{json.dumps(record[\"collection_attributes\"])}', '{json.dumps(record[\"candidate_list\"])}', {record[\"created_at\"]}, {record[\"updated_at\"]})\"\"\"\n", "#             for record in records\n", "#         ]\n", "#     )\n", "#     queries.append(query + values + \" ON CONFLICT (hash_key) DO NOTHING;\")"]}, {"cell_type": "code", "execution_count": null, "id": "163d67db-22ce-4f92-9344-e941b83f85ea", "metadata": {}, "outputs": [], "source": ["queries = []\n", "query = f\"INSERT INTO {table_name} (hash_key, name, story_type, collection_attributes, candidate_list, created_at, updated_at) VALUES\\n\"\n", "chunksize = 1\n", "chunks = [\n", "    hashkey_df_espina_push[i : i + chunksize]\n", "    for i in range(0, hashkey_df_espina_push.shape[0], chunksize)\n", "]\n", "for chunk in chunks:\n", "    records = chunk.to_dict(\"records\")\n", "    values = \",\\n\".join(\n", "        [\n", "            f\"\"\"('{record[\"hash_key\"]}', '{record[\"name\"]}', '{record[\"story_type\"]}', '{json.dumps(record[\"collection_attributes\"])}', '{json.dumps(record[\"candidate_list\"])}', {record[\"created_at\"]}, {record[\"updated_at\"]})\"\"\"\n", "            for record in records\n", "        ]\n", "    )\n", "    queries.append(\n", "        query\n", "        + values\n", "        + \" ON CONFLICT (hash_key) DO UPDATE SET hash_key = EXCLUDED.hash_key, name = EXCLUDED.name, story_type = EXCLUDED.story_type, collection_attributes = EXCLUDED.collection_attributes, candidate_list = EXCLUDED.candidate_list, updated_at = EXCLUDED.updated_at;\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "f93d864e-40c7-49cb-a632-75dae25390c3", "metadata": {}, "outputs": [], "source": ["sink = PostgresSQLSink(espina_db_connection)\n", "start = time.time()\n", "results = sink.bulk_execute(queries, workers=2)\n", "print(f\"took .... {time.time() - start}\")"]}, {"cell_type": "code", "execution_count": null, "id": "834ef924-f40e-4ee3-b565-39aa1bb4bffe", "metadata": {}, "outputs": [], "source": ["count_query = f\"\"\"select count(*) as count from {table_name}\"\"\"\n", "count = pd.read_sql_query(count_query, con=espina_db_connection)[\"count\"][0]"]}, {"cell_type": "code", "execution_count": null, "id": "2319028a-dccf-4122-b9e7-3cc2357febbd", "metadata": {}, "outputs": [], "source": ["count, hashkey_df_espina_push.shape[0]"]}, {"cell_type": "markdown", "id": "bcd6878c-940d-4d51-b49f-60a420455c2b", "metadata": {}, "source": ["#### User brand data transformation"]}, {"cell_type": "code", "execution_count": null, "id": "a41368d1-6642-4bf5-8ecd-e1a65d910c6b", "metadata": {"tags": []}, "outputs": [], "source": ["# user_brand_data = pd.read_csv(\"champion_vip_brand_loyalty_metrics - internal_users_candidate_set.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "2f2b5730-7d71-4d0b-8d0c-3b0266908639", "metadata": {"tags": []}, "outputs": [], "source": ["user_brand_data = pb.from_sheets(\n", "    \"1q0TfCZQ176OXQ1WTUO3QaxgTd1HxO4qWwGPbNaGVPvI\", sheetname=\"internal_users_candidate_set\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f42a85b3-f0be-4251-ac11-b03df63aac01", "metadata": {}, "outputs": [], "source": ["user_brand_data[\"candidate_set\"] = user_brand_data[\"candidate_set\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "c026ae53-2a65-468d-8796-fd481afb42db", "metadata": {}, "outputs": [], "source": ["user_brand_data[\"candidate_set\"] = user_brand_data[\"candidate_set\"].apply(\n", "    lambda x: x.replace(\"[\", \"\")\n", ")\n", "user_brand_data[\"candidate_set\"] = user_brand_data[\"candidate_set\"].apply(\n", "    lambda x: x.replace(\"]\", \"\")\n", ")\n", "user_brand_data[\"candidate_set\"] = user_brand_data[\"candidate_set\"].apply(\n", "    lambda x: x.replace(\",\", \"\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "93fe19c0-282c-404c-b0ee-ee44f6847b27", "metadata": {}, "outputs": [], "source": ["user_brand_data[\"candidate_set\"] = user_brand_data[\"candidate_set\"].apply(\n", "    lambda x: x.strip().split()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8f108cce-7f06-4944-978c-7aa2ad64c2a3", "metadata": {}, "outputs": [], "source": ["user_brand_data[\"candidate_set\"] = user_brand_data[\"candidate_set\"].apply(\n", "    lambda x: [int(i) for i in x]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e1a23525-0edf-4ebb-b62a-a19f0fe53a48", "metadata": {}, "outputs": [], "source": ["user_brand_data[\"brand_id\"] = user_brand_data[\"brand_id\"].astype(int)\n", "user_brand_data[\"dim_customer_key\"] = user_brand_data[\"dim_customer_key\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "3e66d869-a831-4075-86fd-9de045ceda3e", "metadata": {}, "outputs": [], "source": ["user_brand_data[[\"collection_attributes\", \"hash_key\"]] = user_brand_data.apply(\n", "    lambda x: generate_collection_combination_and_hashkey(x), axis=1, result_type=\"expand\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7009a874-4235-49da-b115-6bd6f5c6fd25", "metadata": {}, "outputs": [], "source": ["user_brand_data.dropna()"]}, {"cell_type": "code", "execution_count": null, "id": "dc13c5df-6fe5-4e03-9b85-1f473b0dafea", "metadata": {}, "outputs": [], "source": ["user_brand_unique_brands = user_brand_data[[\"brand_id\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "b9c71bec-2c47-4f98-8043-6f95b029f337", "metadata": {}, "outputs": [], "source": ["user_brand_unique_brands[[\"collection_attributes\", \"hash_key\"]] = user_brand_unique_brands.apply(\n", "    lambda x: generate_collection_combination_and_hashkey(x), axis=1, result_type=\"expand\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f8dee969-02ca-4903-9981-2eecb0335f19", "metadata": {}, "outputs": [], "source": ["all_brands[\"already_present\"] = 1"]}, {"cell_type": "code", "execution_count": null, "id": "d5243691-3fef-4bb9-83c6-abc16a47d4aa", "metadata": {}, "outputs": [], "source": ["user_brand_unique_brands = user_brand_unique_brands.merge(\n", "    all_brands[[\"hash_key\", \"already_present\"]].drop_duplicates(), on=\"hash_key\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ae270120-ee8d-4c40-92ce-8e87b8844bdb", "metadata": {}, "outputs": [], "source": ["hash_gen_candidate_set = user_brand_unique_brands[[\"collection_attributes\", \"hash_key\"]].merge(\n", "    user_brand_data[[\"hash_key\", \"dim_customer_key\", \"candidate_set\"]]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "740b4f7a-f445-4b0c-9392-f612ab503113", "metadata": {}, "outputs": [], "source": ["hash_gen_candidate_set.rename(columns={\"candidate_set\": \"critical_pids\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "6b7ea1f1-5729-44e7-81d9-66b2d0dfa22a", "metadata": {"tags": []}, "outputs": [], "source": ["hash_gen_candidate_set[\"critical_pids\"] = hash_gen_candidate_set.merge(\n", "    hashkey_df[[\"hash_key\", \"candidate_list\"]]\n", ").apply(lambda row: (row[\"critical_pids\"] + row[\"candidate_list\"])[:5], axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "d081c837-8f60-4fb4-9202-a7348bc3609a", "metadata": {}, "outputs": [], "source": ["internals = [\n", "    77919715,\n", "    40449497,\n", "    19928297,\n", "    9279956,\n", "    24337881,\n", "    15574766,\n", "    9103333,\n", "    41875223,\n", "    6281269,\n", "    10226560,\n", "    6283992,\n", "    284259,\n", "    3439489,\n", "    4340078,\n", "    14528635,\n", "    5020682,\n", "    14200423,\n", "    16374200,\n", "    5670862,\n", "    1088684,\n", "    49745457,\n", "    4461098,\n", "    90057028,\n", "    59961558,\n", "    12699317,\n", "    17270352,\n", "    8551627,\n", "    1355710,\n", "    1154412,\n", "    19730340,\n", "    58868013,\n", "    5777071,\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "2fa0eb79-0151-4a07-aaf6-ff13b8134d7e", "metadata": {}, "outputs": [], "source": ["additional_user_list = list(\n", "    set(internals).difference(set(hash_gen_candidate_set[\"dim_customer_key\"].values))\n", ")"]}, {"cell_type": "markdown", "id": "7fd3fdf6-d2b1-43fe-9800-cf2bb8999d87", "metadata": {}, "source": ["#### FS push"]}, {"cell_type": "code", "execution_count": null, "id": "750a5d64-37e1-4a39-9e1a-8598459c6f94", "metadata": {}, "outputs": [], "source": ["tmp_fs_data = hash_gen_candidate_set[[\"dim_customer_key\", \"hash_key\", \"critical_pids\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "d671ee08-8c6a-4cf2-85e8-c7234c820400", "metadata": {"tags": []}, "outputs": [], "source": ["tmp_fs_data[\"critical_pids\"] = tmp_fs_data[[\"critical_pids\"]].to_dict(orient=\"records\")"]}, {"cell_type": "code", "execution_count": null, "id": "30493699-1d92-4626-9c5f-1685431c5f78", "metadata": {}, "outputs": [], "source": ["tmp_fs_data = (\n", "    tmp_fs_data.groupby([\"dim_customer_key\"])\n", "    .apply(lambda x: dict(zip(x[\"hash_key\"], x[\"critical_pids\"])))\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8fb21d19-109f-44e9-959f-d673c0ceb3f8", "metadata": {}, "outputs": [], "source": ["tmp_fs_data.columns = [\"dim_customer_key\", \"user_information\"]"]}, {"cell_type": "code", "execution_count": null, "id": "93c8838d-b811-4797-8d4b-0a9af0c447c1", "metadata": {}, "outputs": [], "source": ["sample_df = tmp_fs_data[tmp_fs_data[\"dim_customer_key\"].isin([1088684, 14705771, 11113])]"]}, {"cell_type": "code", "execution_count": null, "id": "8869951a-9ea5-4aa1-99f5-a8501a291446", "metadata": {}, "outputs": [], "source": ["additional_user_data = []\n", "for user_id in additional_user_list:\n", "    user_info = sample_df.iloc[user_id % len(sample_df)][\n", "        \"user_information\"\n", "    ]  # Loop over existing users\n", "    new_dim_customer_key = user_id\n", "    additional_user_data.append(\n", "        {\"dim_customer_key\": new_dim_customer_key, \"user_information\": user_info}\n", "    )\n", "\n", "additional_user_df = pd.DataFrame(additional_user_data)"]}, {"cell_type": "code", "execution_count": null, "id": "8514b7a0-6a2c-4532-90ce-8b3a522acea4", "metadata": {}, "outputs": [], "source": ["tmp_fs_data = pd.concat([tmp_fs_data, additional_user_df])"]}, {"cell_type": "code", "execution_count": null, "id": "4beb0d4d-6187-40ee-a37f-b51090fe1e63", "metadata": {}, "outputs": [], "source": ["tmp_fs_data[\"type\"] = \"stories\""]}, {"cell_type": "code", "execution_count": null, "id": "8866738f-60cc-4d98-bc0d-3c8d3bf8e293", "metadata": {}, "outputs": [], "source": ["tmp_fs_data = (\n", "    tmp_fs_data.groupby([\"dim_customer_key\"])\n", "    .apply(lambda x: dict(zip(x[\"type\"], x[\"user_information\"])))\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ded0109c-9a8a-4fc7-aecf-7b8466f74561", "metadata": {}, "outputs": [], "source": ["tmp_fs_data.columns = [\"dim_customer_key\", \"user_information\"]"]}, {"cell_type": "code", "execution_count": null, "id": "4b80c705-284b-49cd-a207-a26f93c65c88", "metadata": {}, "outputs": [], "source": ["kafka_records_dict = tmp_fs_data.reset_index(drop=True).to_dict(\"records\")"]}, {"cell_type": "code", "execution_count": null, "id": "4a79dec9-194b-4c5c-9aea-3a94f22f168b", "metadata": {"tags": []}, "outputs": [], "source": ["kafka_records_dict"]}, {"cell_type": "code", "execution_count": null, "id": "8339a404-2541-48b2-bec3-f28e63b3a732", "metadata": {}, "outputs": [], "source": ["## feature store constants\n", "fs_context_name = \"personalisation_for_you_1_0_0\"\n", "cdp_secrets = pb.get_secret(\n", "    \"dse/personalisation/recommendations-product-shelves-rankings/feature-store\"\n", ")\n", "broker = cdp_secrets[\"FEATURE_STORE_REALTIME_BOOTSTRAP_SERVERS\"]\n", "topic = \"profilestore.sql.properties\""]}, {"cell_type": "code", "execution_count": null, "id": "210ca542-8bed-4c4f-a018-ea3a171c7192", "metadata": {}, "outputs": [], "source": ["# Push to <PERSON><PERSON><PERSON> (Feature Store)\n", "entity_column = \"dim_customer_key\"\n", "entity_name = \"user\"\n", "context = fs_context_name\n", "ctx_value_col = \"user_information\"\n", "\n", "push_to_kafka(\n", "    entities=[f\"{entity_name}:{i[entity_column]}\" for i in kafka_records_dict],\n", "    context=context,\n", "    ctx_properties=[{\"ctx_value\": json.dumps(i[ctx_value_col])} for i in kafka_records_dict],\n", "    dry_run=False,\n", ")"]}, {"cell_type": "markdown", "id": "d64a669b-9855-465e-a781-fa70a96e04d8", "metadata": {}, "source": ["#### Generate hashkey for unhashed brands"]}, {"cell_type": "code", "execution_count": null, "id": "14f405ac-2fcb-41e9-b9f8-aac732d68a49", "metadata": {}, "outputs": [], "source": ["all_brands"]}, {"cell_type": "code", "execution_count": null, "id": "b59c8309-a48a-41e9-97cc-063113ed31eb", "metadata": {"tags": []}, "outputs": [], "source": ["user_brand_unique_brands[user_brand_unique_brands[\"already_present\"] != 1]"]}, {"cell_type": "code", "execution_count": null, "id": "fbf27cdc-8f31-4aed-a274-9e045c190893", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5695e351-b3d9-4e4a-94e9-35f95b6f21fe", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4d1fa275-a635-4f89-83ef-7cb1aabb8263", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "00aff44a-b5e3-4beb-a424-77f95e0f7b90", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "83bcb1d5-2f97-4d5e-8191-2c8ee8ff4baf", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b847cc5f-3c57-4d56-9f5e-b47602d91c5d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "baafd93e-0e6a-47d0-9af3-1c19f541b479", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5a9ff8fe-cf32-4d95-8d55-33821b412b00", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1732e6ea-ee46-4b2b-9ae1-a92fd6c40557", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "96f73794-a612-447e-ad87-2a5148609ddb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6fea4851-9a2e-46e0-bf89-18f4d5a04702", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}