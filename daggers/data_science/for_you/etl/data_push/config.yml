alert_configs:
  slack:
  - channel: bl-personalization-notifications
  - channel: bl-personalisation-dag-failures
concurrency: 3
dag_name: data_push
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebooks:
- executor_config:
    load_type: very-high-cpu
    node_type: spot
  name: brand_hash_key_generator
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level1
- executor_config:
    load_type: very-high-cpu
    node_type: spot
  name: fs_push
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level2
owner:
  email: <EMAIL>
  slack_id: U082DA8JB6X
path: data_science/for_you/etl/data_push
paused: false
pool: data_science_pool
project_name: for_you
schedule:
  end_date: '2025-09-15T00:00:00'
  interval: 30 4 * * *
  start_date: '2025-06-10T00:00:00'
schedule_type: fixed
sla: 121 minutes
support_files:
- kafka_utils.py
tags: []
template_name: multi_notebook
version: 13
