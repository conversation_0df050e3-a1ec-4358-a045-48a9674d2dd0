{"cells": [{"cell_type": "code", "execution_count": null, "id": "18f126e6-fedd-4d48-a1b6-8b93c080f20f", "metadata": {}, "outputs": [], "source": ["!pip install -q kafka-python==2.0.2\n", "!pip install -q scipy==1.8.0\n", "!pip install -q aiohttp==3.8.1\n", "!pip install -q fsspec==2023.1.0\n", "!pip install -q aiobotocore==2.4.2\n", "!pip install -q pymysql==1.0.2\n", "!pip install -q gremlinpython==3.6.2\n", "!pip install -q botocore==1.27.59\n", "!pip install -q progressbar2==4.2.0\n", "!pip install -q backoff==2.2.1\n", "!pip install -q pandas==1.5.1\n", "!pip install -q pg8000==1.29.4\n", "!pip install -q opensearch-py==2.1.1\n", "!pip install -q boto3==1.24.59\n", "!pip install -q requests-aws4auth==1.2.2\n", "!pip install -q s3transfer==0.6.0\n", "!pip install -q aenum==3.1.11\n", "!pip install -q scramp==1.4.4\n", "!pip install -q python-utils==3.5.2\n", "!pip install -q awswrangler==2.19.0\n", "!pip install -q s3fs==2023.1.0\n", "!pip install -q sqlalchemy==1.3.23"]}, {"cell_type": "code", "execution_count": null, "id": "6b58f99c-c24e-4663-a489-80b390efcfd7", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys"]}, {"cell_type": "code", "execution_count": null, "id": "5ab653ad-dcd0-40cd-aea9-c93b02b3ad21", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "955c912c-cf5b-4504-8662-a263c630d01c", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "31cb63a7-8321-4cec-9c10-37a09a1b256a", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "from datetime import datetime, timedelta\n", "import hashlib\n", "import time\n", "import json\n", "import ast\n", "import re\n", "from kafka_utils import *"]}, {"cell_type": "code", "execution_count": null, "id": "ef1d95aa-015b-4618-a3b6-80f4a57d8c17", "metadata": {}, "outputs": [], "source": ["import asyncio\n", "import concurrent.futures\n", "import datetime as dt\n", "import threading\n", "import time\n", "from contextlib import contextmanager\n", "from datetime import datetime\n", "import sqlalchemy as sqla\n", "from sqlalchemy.dialects import postgresql\n", "from sqlalchemy.ext.automap import automap_base\n", "from sqlalchemy.orm import sessionmaker"]}, {"cell_type": "code", "execution_count": null, "id": "b4222188-82b7-46a1-9509-9f1aecb19404", "metadata": {}, "outputs": [], "source": ["current_epoch = int(time.time())\n", "user_mods = \",\".join(map(str, [num for num in range(10)]))"]}, {"cell_type": "code", "execution_count": null, "id": "f2f1ad2c-9f48-46c9-8d10-ca9b1892e338", "metadata": {}, "outputs": [], "source": ["prod_df = pd.read_sql(\n", "    \"\"\"\n", "select * from dwh.dim_product where is_current and is_product_enabled\n", "\"\"\",\n", "    con=pb.get_connection(\"[Warehouse] Trino\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d142a6ba-5a2d-4343-b520-d1cb0d6e5c3b", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"select distinct brand_id as ids from consumer_intelligence_etls.personalisation_for_you_brand_loyalty_metrics_all_users\n", "            where brand_loyal = True and user_mod in ({user_mods})\"\"\"\n", "brands = pd.read_sql(query, con=pb.get_connection(\"[Warehouse] Trino\"))\n", "brands = brands.astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "1e653fde-0005-464e-894c-f24e2c90c2c5", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "        with cluster_keyterms_collection as\n", "        ( \n", "        SELECT\n", "        cluster_theme,\n", "        array_agg(metadata_id ORDER BY metadata_id) AS ids\n", "        FROM consumer_intelligence_etls.personalisation_for_you_usecase_entity_mapping\n", "        GROUP BY cluster_theme\n", "        ),\n", "        \n", "        cluster_candidates as\n", "        (\n", "        select distinct cluster_theme, candidate_pids from consumer_intelligence_etls.personalisation_for_you_usecase_entity_mapping\n", "        )\n", "        \n", "        select ckc.cluster_theme , ids, candidate_pids as candidate_list from \n", "        cluster_keyterms_collection ckc join cluster_candidates cc\n", "        on ckc.cluster_theme = cc.cluster_theme\n", "\"\"\"\n", "usecases = pd.read_sql(query, con=pb.get_connection(\"[Warehouse] Trino\"))"]}, {"cell_type": "code", "execution_count": null, "id": "aa0279e5-cafd-4925-93d8-c8d56b566f0a", "metadata": {}, "outputs": [], "source": ["brands[\"story_type\"] = \"brands\"\n", "usecases[\"story_type\"] = \"usecases\""]}, {"cell_type": "code", "execution_count": null, "id": "654f4991-fc17-4131-b103-5fcec649a0e4", "metadata": {}, "outputs": [], "source": ["story_metadata = pd.read_sql(\n", "    f\"\"\"select * from consumer_intelligence_etls.personalisation_story_metadata_v1\"\"\",\n", "    con=pb.get_connection(\"[Warehouse] Trino\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1d268bff-0e59-40f8-be37-94ea51cc8bef", "metadata": {}, "outputs": [], "source": ["brand_top_sales = pd.read_sql(\n", "    f\"\"\"\n", "    WITH ranked_products AS (\n", "        SELECT \n", "            dp.brand_id,\n", "            fsoid.product_id, \n", "            COUNT(DISTINCT cart_id) AS cart_count,  \n", "            ROW_NUMBER() OVER (PARTITION BY dp.brand_id ORDER BY COUNT(DISTINCT cart_id) DESC) AS rank\n", "        FROM \n", "            dwh.fact_sales_order_item_details fsoid\n", "        JOIN\n", "            dwh.dim_product dp ON dp.product_id = fsoid.product_id\n", "        WHERE \n", "            order_create_dt_ist > DATE_ADD('day', -90, CURRENT_DATE)\n", "            AND order_create_dt_ist <= current_date - INTERVAL '1' DAY\n", "            AND fsoid.city_name != ''\n", "            AND fsoid.dim_customer_key IS NOT NULL\n", "            AND fsoid.order_current_status = 'DELIVERED'\n", "            AND is_current \n", "            AND is_product_enabled\n", "        GROUP BY \n", "            dp.brand_id, fsoid.product_id\n", "    ),\n", "    \n", "    top_products AS (\n", "        SELECT \n", "            brand_id,\n", "            product_id\n", "        FROM ranked_products\n", "        WHERE rank <= 5\n", "    )\n", "    \n", "    SELECT \n", "        brand_id,\n", "        ARRAY_AGG(product_id) AS candidate_list\n", "    FROM top_products\n", "    GROUP BY brand_id\n", "    \"\"\",\n", "    con=pb.get_connection(\"[Warehouse] Trino\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a8382ef7-9b4a-443a-aca8-510b36bf61e4", "metadata": {"tags": []}, "outputs": [], "source": ["# query = f\"\"\"\n", "# with max_dt as (\n", "#   select\n", "#     metadata_id,\n", "#     metadata_type,\n", "#     max(snapshot_ist) max_snapshot_ist\n", "#   from\n", "#     search_etls.meta_entity_product_relationship\n", "#   where\n", "#     snapshot_ist is not null\n", "#     and metadata_type = 'KEYTERM'\n", "#   group by\n", "#     1,\n", "#     2\n", "# ),\n", "# sales as (\n", "#   select\n", "#     product_id,\n", "#     sum(total_selling_price) as rev,\n", "#     sum(product_quantity) as items,\n", "#     count (distinct cart_id) as cart_count\n", "#   from\n", "#     dwh.fact_sales_order_item_details o\n", "#   WHERE\n", "#     order_create_dt_ist > current_date - INTERVAL '60' DAY\n", "#     AND order_create_dt_ist <= current_date - INTERVAL '1' DAY\n", "#     AND o.city_name != ''\n", "#     AND o.dim_customer_key IS NOT NULL\n", "#     AND o.order_current_status = 'DELIVERED'\n", "#   group by\n", "#     1\n", "# ),\n", "# metadata_agg as (\n", "#   select\n", "#     p.metadata_id,\n", "#     p.metadata_name,\n", "#     p.metadata_type,\n", "#     p.tagged_product_id as product_id,\n", "#     product_name,\n", "#     l0_category,\n", "#     l0_category_id\n", "#   from\n", "#     search_etls.meta_entity_product_relationship p\n", "#     join max_dt m on m.metadata_id = p.metadata_id\n", "#     and m.max_snapshot_ist = p.snapshot_ist\n", "#     and m.metadata_type = p.metadata_type\n", "#     join dwh.dim_product dp on dp.product_id = p.tagged_product_id\n", "#     and dp.is_current\n", "#     and dp.is_product_enabled\n", "#   where\n", "#     p.snapshot_ist is not null\n", "#   group by\n", "#     1,\n", "#     2,\n", "#     3,\n", "#     4,\n", "#     5,\n", "#     6,\n", "#     7\n", "# ),\n", "\n", "# sale_metadata as(\n", "# select\n", "#   a.metadata_id,\n", "#   a.product_id,\n", "#   COALESCE(b.cart_count,0) as cart_count\n", "# from\n", "#   metadata_agg a\n", "#   left join sales b on b.product_id = a.product_id),\n", "\n", "# ranked_sales as (\n", "#     select\n", "#         cluster_theme, product_id, row_number() over(partition by cluster_theme order by cart_count desc) as rank\n", "#     from\n", "#         sale_metadata a join consumer_intelligence_etls.personalisation_for_you_usecase_entity_mapping b\n", "#         on a.metadata_id = b.metadata_id\n", "\n", "# )\n", "\n", "# select\n", "#     cluster_theme,\n", "#     array_agg(product_id) AS candidate_list\n", "#     from ranked_sales\n", "#     where rank <= 5\n", "#     group by cluster_theme\n", "# \"\"\"\n", "\n", "# usecases_top_products = pd.read_sql(query, con=pb.get_connection(\"[Warehouse] Trino\"))"]}, {"cell_type": "code", "execution_count": null, "id": "4f06df94-27c0-4ebf-b6d1-18e6e43a346a", "metadata": {"tags": []}, "outputs": [], "source": ["brands = brands.merge(brand_top_sales, left_on=\"ids\", right_on=\"brand_id\")\n", "usecases.rename(columns={\"cluster_theme\": \"name\"}, inplace=True)\n", "brands = brands.merge(prod_df[[\"brand_id\", \"brand_name\"]])\n", "brands.rename(columns={\"brand_name\": \"name\"}, inplace=True)\n", "story_metadata_current = pd.concat(\n", "    [\n", "        usecases[[\"ids\", \"story_type\", \"candidate_list\", \"name\"]],\n", "        brands[[\"ids\", \"story_type\", \"candidate_list\", \"name\"]],\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2d21b8cb-0a7f-4265-b189-ec020370d651", "metadata": {}, "outputs": [], "source": ["def generate_short_hash_key(data):\n", "    # Ensure keys are sorted, and arrays are sorted for consistency\n", "    sorted_data = {k: sorted(v) for k, v in data.items()}\n", "    # print(sorted_data)\n", "\n", "    # Convert to JSON string with sorted keys\n", "    json_str = json.dumps(sorted_data, separators=(\",\", \":\"), sort_keys=True)\n", "\n", "    # Generate SHA256 hash\n", "    full_hash = hashlib.sha256(json_str.encode()).hexdigest()\n", "\n", "    # Return only the first 16 characters of the hash\n", "    return sorted_data, full_hash[:16]\n", "\n", "\n", "def generate_collection_combination_and_hashkey(x):\n", "    if x[\"story_type\"] == \"brands\":\n", "        data = {\"brand\": [x[\"ids\"]]}\n", "    elif x[\"story_type\"] == \"usecases\":\n", "        data = {\"keyterm_id\": x[\"ids\"]}\n", "    sorted_data, hashkey = generate_short_hash_key(data)\n", "    return sorted_data, hashkey"]}, {"cell_type": "code", "execution_count": null, "id": "a93b4a8f-1bb7-449c-a13c-d06562462669", "metadata": {"tags": []}, "outputs": [], "source": ["story_metadata_current[[\"collection_attributes\", \"hash_key\"]] = story_metadata_current.apply(\n", "    lambda x: generate_collection_combination_and_hashkey(x), axis=1, result_type=\"expand\"\n", ")\n", "new_entries = story_metadata_current[\n", "    ~np.isin(story_metadata_current[\"hash_key\"], story_metadata[\"hash_key\"])\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "21f847be-d5a8-4961-a087-d194d5de35a1", "metadata": {"tags": []}, "outputs": [], "source": ["new_entries.drop_duplicates(subset=[\"hash_key\"], inplace=True)\n", "new_entries[\"created_at\"] = current_epoch\n", "new_entries[\"updated_at\"] = current_epoch"]}, {"cell_type": "code", "execution_count": null, "id": "69962fcc-59a3-4094-9c3f-d158cc333b45", "metadata": {}, "outputs": [], "source": ["new_entries.shape"]}, {"cell_type": "code", "execution_count": null, "id": "9023b597-2e1b-403f-bbeb-498e992073b8", "metadata": {}, "outputs": [], "source": ["new_entries.head()"]}, {"cell_type": "markdown", "id": "03d0c383-e682-46c0-8758-7d7986cb4f54", "metadata": {}, "source": ["#### push brand story meta data one time"]}, {"cell_type": "code", "execution_count": null, "id": "d5eb6e17-1874-4840-85ae-a7c40091e064", "metadata": {}, "outputs": [], "source": ["def get_schema(loadtype):\n", "    base_data_table_name = \"personalisation_story_metadata_v1\"\n", "\n", "    kwargs_base_data_tb = {\n", "        \"schema_name\": \"consumer_intelligence_etls\",\n", "        \"table_name\": base_data_table_name,\n", "        \"column_dtypes\": [\n", "            {\n", "                \"name\": \"hash_key\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"brand hash key\",\n", "            },\n", "            {\n", "                \"name\": \"story_type\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"story type\",\n", "            },\n", "            {\n", "                \"name\": \"name\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"brand name\",\n", "            },\n", "            {\n", "                \"name\": \"candidate_list\",\n", "                \"type\": \"ARRAY(INTEGER)\",\n", "                \"description\": \"list of recommended product_ids\",\n", "            },\n", "            {\n", "                \"name\": \"created_at\",\n", "                \"type\": \"bigint\",\n", "                \"description\": \"cart count for brand\",\n", "            },\n", "            {\n", "                \"name\": \"updated_at\",\n", "                \"type\": \"bigint\",\n", "                \"description\": \"update timestamp\",\n", "                \"default\": \"CURRENT_TIMESTAMP\",\n", "            },\n", "        ],\n", "        \"load_type\": loadtype,  # append, truncate or upsert\n", "        \"table_description\": \"Intermediate table for storing aggregated data needed for personalisation usecase\",\n", "        \"primary_key\": [\"hash_key\"],\n", "    }\n", "\n", "    return kwargs_base_data_tb"]}, {"cell_type": "code", "execution_count": null, "id": "05c0a980-493d-4dce-a870-e0cfe927ad92", "metadata": {"tags": []}, "outputs": [], "source": ["schema = get_schema(\"upsert\")\n", "pb.to_trino(new_entries.drop([\"collection_attributes\", \"ids\"], axis=1), **schema)"]}, {"cell_type": "markdown", "id": "ae9137d9-43ac-4b74-9f84-c45f16d70cf6", "metadata": {}, "source": ["#### Push brand story data to DSE DB"]}, {"cell_type": "code", "execution_count": null, "id": "4fabf0a7-97e3-4466-9ee6-6ca0cab0d5bf", "metadata": {}, "outputs": [], "source": ["@contextmanager\n", "def session_scope(s):\n", "    \"\"\"Provide a transactional scope around a series of operations.\"\"\"\n", "    session = s()\n", "    try:\n", "        yield session\n", "        session.commit()\n", "    except Exception as e:\n", "        session.rollback()\n", "        raise e\n", "    finally:\n", "        session.close()\n", "\n", "\n", "class PostgresSQLSink:\n", "    def __init__(self, engine):\n", "        self.engine = engine\n", "        self.session = sessionmaker(\n", "            autocommit=False, autoflush=False, expire_on_commit=False, bind=engine\n", "        )\n", "\n", "    def bulk_execute(self, queries, workers=2):\n", "        responses = []\n", "        with concurrent.futures.ThreadPoolExecutor(max_workers=workers) as executor:\n", "            futures = []\n", "            for i, query in enumerate(queries):\n", "                task_id = f\"{i + 1}\"\n", "                futures.append(executor.submit(self.execute, query, task_id=task_id))\n", "\n", "            for future in concurrent.futures.as_completed(futures):\n", "                result = future.result()\n", "                responses.append(result)\n", "                print(f\"{result['task_id']} complete... [{result['time_taken']}]\")\n", "        return responses\n", "\n", "    def execute(self, query, task_id=None):\n", "        if task_id is None:\n", "            task_id = \"execute\"\n", "\n", "        with session_scope(self.session) as session:\n", "            error = None\n", "            start = time.time()\n", "            try:\n", "                res = session.execute(query)\n", "            except Exception as e:\n", "                error = e\n", "\n", "            try:\n", "                result = res.rowcount\n", "            except:\n", "                result = None\n", "\n", "            return {\n", "                \"task_id\": task_id,\n", "                \"time_taken\": time.time() - start,\n", "                \"error\": error,\n", "                \"result\": result,\n", "                \"query\": query,\n", "            }"]}, {"cell_type": "code", "execution_count": null, "id": "d6cc459a-a36b-46ff-9769-1a31d72ce9d7", "metadata": {"tags": []}, "outputs": [], "source": ["def get_espina_db_connection():\n", "    dse_db_conn_params = pb.get_secret(\"dse/postgres/dse_db/airflow_etl_user\")\n", "    engine_url = dse_db_conn_params[\"uri\"]\n", "    engine = sqla.create_engine(engine_url, pool_size=1, echo=False)\n", "    return engine\n", "\n", "\n", "espina_db_connection = get_espina_db_connection()"]}, {"cell_type": "code", "execution_count": null, "id": "79b87b1a-d38d-4842-b0fa-2d1add7d2538", "metadata": {"tags": []}, "outputs": [], "source": ["table_name = \"personalisation_story_metadata\"\n", "hashkey_df_espina_push = new_entries"]}, {"cell_type": "code", "execution_count": null, "id": "aaaea5d8-27c9-4ddc-be5b-b495c58122e8", "metadata": {"tags": []}, "outputs": [], "source": ["# ####Test\n", "# hashkey_df_espina_push = story_metadata\n", "# hashkey_df_espina_push[\"story_type\"] = \"brand\"\n", "# hashkey_df_espina_push[\"collection_attributes\"] = ''"]}, {"cell_type": "code", "execution_count": null, "id": "09fd1d7a-5120-4c71-9a59-60cd0fff1428", "metadata": {}, "outputs": [], "source": ["hashkey_df_espina_push[\"name\"] = hashkey_df_espina_push[\"name\"].str.replace(\"'\", \"''\")"]}, {"cell_type": "code", "execution_count": null, "id": "b3eba7a4-5728-483a-8b09-b517a4565140", "metadata": {"tags": []}, "outputs": [], "source": ["# hashkey_df_espina_push[\"story_type\"] = \"brand\"\n", "# hashkey_df_espina_push.drop(\"brand_id\", axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "1dba7196-4d9c-4e9b-a7e3-852960866a08", "metadata": {}, "outputs": [], "source": ["queries = []\n", "query = f\"INSERT INTO {table_name} (hash_key, name, story_type, collection_attributes, candidate_list, created_at, updated_at) VALUES\\n\"\n", "chunksize = 1\n", "chunks = [\n", "    hashkey_df_espina_push[i : i + chunksize]\n", "    for i in range(0, hashkey_df_espina_push.shape[0], chunksize)\n", "]\n", "for chunk in chunks:\n", "    records = chunk.to_dict(\"records\")\n", "    values = \",\\n\".join(\n", "        [\n", "            f\"\"\"('{record[\"hash_key\"]}', '{record[\"name\"]}', '{record[\"story_type\"]}', '{json.dumps(record[\"collection_attributes\"])}', '{json.dumps(record[\"candidate_list\"])}', {record[\"created_at\"]}, {record[\"updated_at\"]})\"\"\"\n", "            for record in records\n", "        ]\n", "    )\n", "    queries.append(\n", "        query\n", "        + values\n", "        + \" ON CONFLICT (hash_key) DO UPDATE SET hash_key = EXCLUDED.hash_key, name = EXCLUDED.name, story_type = EXCLUDED.story_type, collection_attributes = EXCLUDED.collection_attributes, candidate_list = EXCLUDED.candidate_list, updated_at = EXCLUDED.updated_at;\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "765ad104-a4b1-496b-b68a-bc615657ed59", "metadata": {}, "outputs": [], "source": ["sink = PostgresSQLSink(espina_db_connection)\n", "start = time.time()\n", "results = sink.bulk_execute(queries, workers=2)\n", "print(f\"took .... {time.time() - start}\")"]}, {"cell_type": "code", "execution_count": null, "id": "65e57fb3-3860-4485-bf10-1a4ef2db028c", "metadata": {}, "outputs": [], "source": ["count_query = f\"\"\"select count(*) as count from {table_name}\"\"\"\n", "count = pd.read_sql_query(count_query, con=espina_db_connection)[\"count\"][0]\n", "count, hashkey_df_espina_push.shape[0]"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}