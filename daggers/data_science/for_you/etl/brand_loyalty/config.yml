alert_configs:
  slack:
  - channel: bl-personalization-notifications
  - channel: bl-personalisation-dag-failures
concurrency: 5
dag_name: brand_loyalty
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebooks:
- executor_config:
    load_type: low
    node_type: spot
  name: population_metrics_creator
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level1
- executor_config:
    load_type: low
    node_type: spot
  name: brand_loyalty_refined_user_mod_0
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level2
- executor_config:
    load_type: low
    node_type: spot
  name: brand_loyalty_refined_user_mod_1
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level2
- executor_config:
    load_type: low
    node_type: spot
  name: brand_loyalty_refined_user_mod_2
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level2
- executor_config:
    load_type: low
    node_type: spot
  name: brand_loyalty_refined_user_mod_3
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level2
- executor_config:
    load_type: low
    node_type: spot
  name: brand_loyalty_refined_user_mod_4
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level2
- executor_config:
    load_type: low
    node_type: spot
  name: brand_loyalty_refined_user_mod_5
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level2
- executor_config:
    load_type: low
    node_type: spot
  name: brand_loyalty_refined_user_mod_6
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level2
- executor_config:
    load_type: low
    node_type: spot
  name: brand_loyalty_refined_user_mod_7
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level2
- executor_config:
    load_type: low
    node_type: spot
  name: brand_loyalty_refined_user_mod_8
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level2
- executor_config:
    load_type: low
    node_type: spot
  name: brand_loyalty_refined_user_mod_9
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level2
- executor_config:
    load_type: medium
    node_type: spot
  name: combined_data_with_brand_loyalty_flag
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level3
- executor_config:
    load_type: very-high-cpu
    node_type: od
  name: candidate_generation_user_mod_0
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level4
- executor_config:
    load_type: very-high-cpu
    node_type: od
  name: candidate_generation_user_mod_1
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level4
- executor_config:
    load_type: very-high-cpu
    node_type: od
  name: candidate_generation_user_mod_2
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level4
- executor_config:
    load_type: very-high-cpu
    node_type: od
  name: candidate_generation_user_mod_3
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level4
- executor_config:
    load_type: very-high-cpu
    node_type: od
  name: candidate_generation_user_mod_4
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level4
- executor_config:
    load_type: very-high-cpu
    node_type: od
  name: candidate_generation_user_mod_5
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level4
- executor_config:
    load_type: very-high-cpu
    node_type: od
  name: candidate_generation_user_mod_6
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level4
- executor_config:
    load_type: very-high-cpu
    node_type: od
  name: candidate_generation_user_mod_7
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level4
- executor_config:
    load_type: very-high-cpu
    node_type: od
  name: candidate_generation_user_mod_8
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level4
- executor_config:
    load_type: very-high-cpu
    node_type: od
  name: candidate_generation_user_mod_9
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level4
- executor_config:
    load_type: medium
    node_type: spot
  name: combined_data_candidate_set
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level5
owner:
  email: <EMAIL>
  slack_id: U082DA8JB6X
path: data_science/for_you/etl/brand_loyalty
paused: false
pool: data_science_pool
project_name: for_you
schedule:
  end_date: '2025-07-24T00:00:00'
  interval: 30 21 * * *
  start_date: '2025-06-06T00:00:00'
schedule_type: fixed
sla: 121 minutes
support_files:
- queries.py
- helper.py
- kafka_utils.py
tags: []
template_name: multi_notebook
version: 11
