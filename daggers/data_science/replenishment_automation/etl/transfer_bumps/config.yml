alert_configs:
  opsgenie:
    alerts:
    - type: user
      username: aman.a<PERSON><EMAIL>
    - type: user
      username: <EMAIL>
    priority: P1
  pagerduty:
    alerts:
    - name: ars
      type: service
    severity: critical
  slack:
  - channel: bl-data-airflow-alerts
dag_name: transfer_bumps
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_science
notebook:
  parameters:
    run_id: '{{ run_id }}'
    task_instance_key_str: '{{ task_instance_key_str }}'
  priority_weight: 5
owner:
  email: <EMAIL>
  slack_id: S07DN04EBC0
path: data_science/replenishment_automation/etl/transfer_bumps
paused: false
pool: priority_pool
project_name: replenishment_automation
schedule:
  end_date: '2025-09-20T00:00:00'
  interval: 00 15,1 * * *
  start_date: '2025-06-24T00:00:00'
schedule_type: fixed
sla: 174 minutes
support_files: []
tags: []
template_name: notebook
version: 24
