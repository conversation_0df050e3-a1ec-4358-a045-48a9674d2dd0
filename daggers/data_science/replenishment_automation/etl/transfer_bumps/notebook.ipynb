{"cells": [{"cell_type": "code", "execution_count": null, "id": "5b8e8438-df56-4f4a-8ded-9faf9df2499d", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "636d6bbe-f5df-41cf-a8a7-3cc2bc1002ab", "metadata": {}, "outputs": [], "source": ["dag_id = task_instance_key_str.split(\"__\")[0]\n", "task_id = task_instance_key_str.split(\"__\")[1]\n", "print(dag_id, task_id)"]}, {"cell_type": "code", "execution_count": null, "id": "4f05957b-e258-4be6-9eae-a9e04bcbc519", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["parameters"]}, "outputs": [], "source": ["print(run_id)"]}, {"cell_type": "code", "execution_count": null, "id": "5b2ae7f3-29a0-483c-bd90-e4828d84e67c", "metadata": {"tags": []}, "outputs": [], "source": ["! pip install -q awswrangler==3.9.0"]}, {"cell_type": "code", "execution_count": null, "id": "df24cfcb-a247-46ac-9264-4cc672cd1e71", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "\n", "from datetime import datetime, timedelta\n", "from pytz import timezone\n", "import awswrangler as wr"]}, {"cell_type": "code", "execution_count": null, "id": "0d3b8b7a-d5c1-45e8-8a67-88e2a327087e", "metadata": {}, "outputs": [], "source": ["dag_run_id = run_id\n", "\n", "if \"manual\" in run_id:\n", "    SHEET_ID = \"1ylx9iyrw_HM9cT9jzh0fx53OL5ICllF4kV1WiACH_x4\"\n", "    SHEET_NAME = \"bump_run?\"\n", "    df = pb.from_sheets(sheetid=SHEET_ID, sheetname=SHEET_NAME)\n", "    for i, row in df.iterrows():\n", "        param = row[\"param\"]\n", "        value = row[\"value\"]\n", "        print(f\"{param} = {value}\")\n", "        exec(f\"{param} = value\")\n", "    df[\"value\"] = df.apply(lambda x: \"default\" if x.param == \"temp_run_id\" else x.value, axis=1)\n", "    pb.to_sheets(df, sheetid=SHEET_ID, sheetname=SHEET_NAME)\n", "    sheet_link = f\"https://docs.google.com/spreadsheets/d/{SHEET_ID}/\"\n", "    print(sheet_link)\n", "    if temp_run_id != \"default\":\n", "        run_id = temp_run_id\n", "\n", "print(f\"run_id== {run_id}\")\n", "print(f\"dag_run_id== {run_id}\")\n", "\n", "# faltucode no need will remove when refactoring\n", "df = pb.from_sheets(\"1ylx9iyrw_HM9cT9jzh0fx53OL5ICllF4kV1WiACH_x4\", \"bump_run?\")\n", "if df[\"2\"].values[0] == \"TRUE\":\n", "    pass\n", "else:\n", "    raise Exception(\"Nahi ho payega\")"]}, {"cell_type": "code", "execution_count": null, "id": "f9e9f3ae-4dab-42fb-ad8b-2fe237bef8de", "metadata": {}, "outputs": [], "source": ["dag_link = f\"https://dse-commuter.grofer.io/view/{dag_id}/{dag_run_id}/{task_id}.ipynb\"\n", "print(dag_link)"]}, {"cell_type": "code", "execution_count": null, "id": "60f227ca-584c-445d-adce-d1ce8645ca25", "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.send_slack_message(\n", "        channel=\"#bl-fnv-replenishment-dag-run-alerts\",\n", "        text=f\":large_blue_square: Fnv transfers dag started running as {run_id} . \\n dag_link:- {dag_link} \\n\",\n", "    )\n", "except Exception as e:\n", "    print(f\"Failed to send Slack message: {e}\")"]}, {"cell_type": "code", "execution_count": null, "id": "0c6bafaf-5fa7-43a8-8ceb-8ab3109152cc", "metadata": {}, "outputs": [], "source": ["current_hour = datetime.now(timezone(\"Asia/Kolkata\")).hour\n", "if current_hour < 14 and current_hour >= 2:\n", "    current_slot = \"slotb\"\n", "else:\n", "    current_slot = \"slota\"\n", "print(current_slot)"]}, {"cell_type": "code", "execution_count": null, "id": "8d812f8e-ccaf-4173-ac46-8874b77fa0ca", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["from datetime import datetime\n", "\n", "from pytz import timezone\n", "from sqlalchemy.exc import ProgrammingError\n", "\n", "# from replenishment_model.utils import warner\n", "\n", "# try:\n", "\n", "#     def send_to_redshift(\n", "#         rep_df,\n", "#         schema=\"playground\",\n", "#         normal_table_name=\"demand_forecast_item_min_max_quantity_io\",\n", "#         send_to_table=False,\n", "#         changed_log_table_name=\"demand_forecast_item_min_max_quantity_details_io\",\n", "#         log_extra_columns_dict=dict(),\n", "#         log_extra_dtypes_dict=dict(),\n", "#         log_extra_primary_key_list=[\"updated_at_ist\"],\n", "#         log_extra_sortkey_list=[\"updated_at_ist\"],\n", "#         send_to_log_table=True,\n", "#     ):\n", "\n", "#         to_push_df = rep_df.copy()\n", "\n", "#         normal_kwargs = {\n", "#             \"schema_name\": f\"{schema}\",\n", "#             \"table_name\": f\"{normal_table_name}\",\n", "#             \"column_dtypes\": [\n", "#                 {\"name\": \"facility_id\", \"type\": \"int\", \"description\": \"facility id\"},\n", "#                 {\"name\": \"outlet_id\", \"type\": \"int\", \"description\": \"outlet id\"},\n", "#                 {\"name\": \"item_id\", \"type\": \"int\", \"description\": \"item id\"},\n", "#                 {\"name\": \"name\", \"type\": \"varchar\", \"description\": \"item name\"},\n", "#                 {\"name\": \"item_type\", \"type\": \"varchar\", \"description\": \"item type\"},\n", "#                 {\"name\": \"quantile\", \"type\": \"float\", \"description\": \"quantile\"},\n", "#                 {\"name\": \"min_qty\", \"type\": \"int\", \"description\": \"Min Stock level\"},\n", "#                 {\"name\": \"max_qty\", \"type\": \"int\", \"description\": \"Max Stock level\"},\n", "#                 {\n", "#                     \"name\": \"current_replenishment_ts_ist\",\n", "#                     \"type\": \"timestamp\",\n", "#                     \"description\": \"Current Replenishment Timestamp\",\n", "#                 },\n", "#                 {\n", "#                     \"name\": \"updated_at_ist\",\n", "#                     \"type\": \"timestamp\",\n", "#                     \"description\": \"Latest update time in IST\",\n", "#                 },\n", "#             ],\n", "#             \"primary_key\": [\"facility_id\", \"item_id\"],\n", "#             \"sortkey\": [\"facility_id\", \"item_id\"],\n", "#             \"incremental_key\": \"updated_at_ist\",\n", "#             \"load_type\": \"rebuild\",\n", "#             \"table_description\": \"demand_forecast_item_min_max_quantity\",\n", "#         }\n", "\n", "#         if send_to_table:\n", "\n", "#             update_table_on_redshift(\n", "#                 normal_df=to_push_df,\n", "#                 normal_kwargs=normal_kwargs,\n", "#                 changed_log_table_name=changed_log_table_name,\n", "#                 log_extra_columns_dict=log_extra_columns_dict,\n", "#                 log_extra_dtypes_dict=log_extra_dtypes_dict,\n", "#                 log_extra_primary_key_list=log_extra_primary_key_list,\n", "#                 log_extra_sortkey_list=log_extra_sortkey_list,\n", "#                 send_to_log_table=send_to_log_table,\n", "#             )\n", "\n", "#         return to_push_df\n", "\n", "#     def update_table_on_redshift(\n", "#         normal_df,\n", "#         normal_kwargs,\n", "#         changed_log_table_name=None,\n", "#         log_extra_columns_dict=dict(),\n", "#         log_extra_dtypes_dict=dict(),\n", "#         log_extra_primary_key_list=list(),\n", "#         log_extra_sortkey_list=list(),\n", "#         send_to_log_table=True,\n", "#     ):\n", "#         if changed_log_table_name == normal_kwargs[\"table_name\"]:\n", "#             raise Exception(\"Log and normal table names are same!\")\n", "#         if not set(log_extra_columns_dict.keys()) == set(log_extra_dtypes_dict.keys()):\n", "#             raise Exception(\"Log extra column and dtype do not match\")\n", "\n", "#         try:\n", "#             pb.to_redshift(normal_df, **normal_kwargs)\n", "#             print(\n", "#                 f\"Updated {normal_kwargs['schema_name']}.{normal_kwargs['table_name']}\"\n", "#             )\n", "#         except Exception as e:\n", "#             raise ValueError(\n", "#                 f\"Error in populating {normal_kwargs['schema_name']}.{normal_kwargs['table_name']}: {e}\"\n", "#             )\n", "\n", "#         if send_to_log_table:\n", "#             log_kwargs = normal_kwargs\n", "\n", "#             if changed_log_table_name:\n", "#                 log_kwargs[\"table_name\"] = changed_log_table_name\n", "\n", "#             log_kwargs[\"table_name\"] = log_kwargs[\"table_name\"] + \"_log\"\n", "\n", "#             for col, value in log_extra_columns_dict.items():\n", "#                 normal_df[col] = value\n", "#                 dtype_dict = {\n", "#                     \"name\": col,\n", "#                     \"type\": log_extra_dtypes_dict[col],\n", "#                     \"description\": col,\n", "#                 }\n", "#                 log_kwargs[\"column_dtypes\"].append(dtype_dict)\n", "\n", "#             if \"frontend_id\" in [d[\"name\"] for d in normal_kwargs[\"column_dtypes\"]]:\n", "#                 ind = [\n", "#                     i\n", "#                     for i in range(len(normal_kwargs[\"column_dtypes\"]))\n", "#                     if log_kwargs[\"column_dtypes\"][i][\"name\"] == \"frontend_id\"\n", "#                 ][0]\n", "#                 log_kwargs[\"column_dtypes\"].insert(\n", "#                     0, log_kwargs[\"column_dtypes\"].pop(ind)\n", "#                 )\n", "#                 normal_df.insert(0, \"frontend_id\", normal_df.pop(\"frontend_id\"))\n", "\n", "#             log_kwargs[\"load_type\"] = \"append\"\n", "\n", "#             try:\n", "#                 pb.to_redshift(normal_df, **log_kwargs)\n", "#                 print(f\"Updated {log_kwargs['schema_name']}.{log_kwargs['table_name']}\")\n", "#             except Exception as e:\n", "#                 raise ValueError(\n", "#                     f\"Error in populating {log_kwargs['schema_name']}.{log_kwargs['table_name']}\"\n", "#                 )\n", "\n", "#         return normal_df\n", "\n", "# except Exception as e:\n", "#     print(e)"]}, {"cell_type": "code", "execution_count": null, "id": "e9ca6653-a4e2-4eb3-9423-1e94321beb1f", "metadata": {}, "outputs": [], "source": ["# def send_to_trino(\n", "#     rep_df,\n", "#     schema=\"ds_etls\",\n", "#     normal_table_name=\"demand_forecast_item_min_max_quantity_io\",\n", "#     send_to_table=False,\n", "#     changed_log_table_name=\"demand_forecast_item_min_max_quantity_details_io\",\n", "#     log_extra_columns_dict=dict(),\n", "#     log_extra_dtypes_dict=dict(),\n", "#     log_extra_primary_key_list=[\"updated_at_ist\"],\n", "#     log_extra_sortkey_list=[\"updated_at_ist\"],\n", "#     send_to_log_table=True,\n", "# ):\n", "\n", "#     to_push_df = rep_df.copy()\n", "\n", "#     normal_kwargs = {\n", "#         \"schema_name\": f\"{schema}\",\n", "#         \"table_name\": f\"{normal_table_name}\",\n", "#         \"column_dtypes\": [\n", "#             {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"facility_id\"},\n", "#             {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"outlet id\"},\n", "#             {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"item id\"},\n", "#             {\"name\": \"name\", \"type\": \"VARCHAR\", \"description\": \"item name\"},\n", "#             {\"name\": \"item_type\", \"type\": \"VARCHAR\", \"description\": \"item type\"},\n", "#             {\"name\": \"quantile\", \"type\": \"REAL\", \"description\": \"quantile\"},\n", "#             {\"name\": \"min_qty\", \"type\": \"INTEGER\", \"description\": \"Min Stock level\"},\n", "#             {\"name\": \"max_qty\", \"type\": \"INTEGER\", \"description\": \"Max Stock level\"},\n", "#             {\n", "#                 \"name\": \"current_replenishment_ts_ist\",\n", "#                 \"type\": \"TIMESTAMP(6)\",\n", "#                 \"description\": \"Current Replenishment Timestamp\",\n", "#             },\n", "#             {\n", "#                 \"name\": \"updated_at_ist\",\n", "#                 \"type\": \"TIMESTAMP(6)\",\n", "#                 \"description\": \"Latest update time in IST\",\n", "#             },\n", "#         ],\n", "#         \"primary_key\": [\"facility_id\", \"item_id\"],\n", "#         \"sort_key\": [\"facility_id\", \"item_id\"],\n", "#         \"incremental_key\": \"updated_at_ist\",\n", "#         \"load_type\": \"upsert\",\n", "#         \"table_description\": \"demand_forecast_item_min_max_quantity\",\n", "#         \"force_upsert_without_increment_check\": True,\n", "#         \"run_maintenance\": True,\n", "#     }\n", "\n", "#     if send_to_table:\n", "\n", "#         update_table_on_trino(\n", "#             normal_df=to_push_df,\n", "#             normal_kwargs=normal_kwargs,\n", "#             changed_log_table_name=changed_log_table_name,\n", "#             log_extra_columns_dict=log_extra_columns_dict,\n", "#             log_extra_dtypes_dict=log_extra_dtypes_dict,\n", "#             log_extra_primary_key_list=log_extra_primary_key_list,\n", "#             log_extra_sortkey_list=log_extra_sortkey_list,\n", "#             send_to_log_table=send_to_log_table,\n", "#         )\n", "\n", "#     return to_push_df\n", "\n", "\n", "# def update_table_on_trino(\n", "#     normal_df,\n", "#     normal_kwargs,\n", "#     changed_log_table_name=None,\n", "#     log_extra_columns_dict=dict(),\n", "#     log_extra_dtypes_dict=dict(),\n", "#     log_extra_primary_key_list=list(),\n", "#     log_extra_sortkey_list=list(),\n", "#     send_to_log_table=True,\n", "# ):\n", "#     if changed_log_table_name == normal_kwargs[\"table_name\"]:\n", "#         raise Exception(\"Log and normal table names are same!\")\n", "#     if not set(log_extra_columns_dict.keys()) == set(log_extra_dtypes_dict.keys()):\n", "#         raise Exception(\"Log extra column and dtype do not match\")\n", "\n", "#     try:\n", "#         pb.to_trino(normal_df, **normal_kwargs)\n", "#         print(f\"Updated {normal_kwargs['schema_name']}.{normal_kwargs['table_name']}\")\n", "#     except Exception as e:\n", "#         raise ValueError(\n", "#             f\"Error in populating {normal_kwargs['schema_name']}.{normal_kwargs['table_name']}: {e}\"\n", "#         )\n", "\n", "#     if send_to_log_table:\n", "#         log_kwargs = normal_kwargs\n", "\n", "#         if changed_log_table_name:\n", "#             log_kwargs[\"table_name\"] = changed_log_table_name\n", "\n", "#         log_kwargs[\"table_name\"] = log_kwargs[\"table_name\"] + \"_log\"\n", "\n", "#         for col, value in log_extra_columns_dict.items():\n", "#             normal_df[col] = value\n", "#             dtype_dict = {\n", "#                 \"name\": col,\n", "#                 \"type\": log_extra_dtypes_dict[col],\n", "#                 \"description\": col,\n", "#             }\n", "#             log_kwargs[\"column_dtypes\"].append(dtype_dict)\n", "\n", "#         if \"frontend_id\" in [d[\"name\"] for d in normal_kwargs[\"column_dtypes\"]]:\n", "#             ind = [\n", "#                 i\n", "#                 for i in range(len(normal_kwargs[\"column_dtypes\"]))\n", "#                 if log_kwargs[\"column_dtypes\"][i][\"name\"] == \"frontend_id\"\n", "#             ][0]\n", "#             log_kwargs[\"column_dtypes\"].insert(0, log_kwargs[\"column_dtypes\"].pop(ind))\n", "#             normal_df.insert(0, \"frontend_id\", normal_df.pop(\"frontend_id\"))\n", "\n", "#         log_kwargs[\"load_type\"] = \"append\"\n", "#         log_kwargs[\"partition_key\"] = [\"current_replenishment_ts_ist\"]\n", "\n", "#         try:\n", "#             pb.to_trino(normal_df, **log_kwargs)\n", "#             print(f\"Updated {log_kwargs['schema_name']}.{log_kwargs['table_name']}\")\n", "#         except Exception as e:\n", "#             raise ValueError(\n", "#                 f\"Error in populating {log_kwargs['schema_name']}.{log_kwargs['table_name']}\"\n", "#             )\n", "\n", "#     return normal_df"]}, {"cell_type": "code", "execution_count": null, "id": "0353300b-4ff7-4791-887d-daea4d7fc5d6", "metadata": {}, "outputs": [], "source": ["try:\n", "    redshift_conn = pb.get_connection(\"redpen\")\n", "    trino_conn = pb.get_connection(\"[Warehouse] Trino\")\n", "except:\n", "    trino_conn = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "b6bd3bcd-a7c9-475f-85e4-699ccf4abf2f", "metadata": {}, "outputs": [], "source": ["def get_results_from_query(sql: str, con: str = \"[Warehouse] Trino\", no_retries=3):\n", "    # LOGGER.info(f\"Fetching data for the query \\n{sql[:50] + ' ...'}\")\n", "    for i in range(no_retries):\n", "        try:\n", "            data = pd.read_sql(sql, pb.get_connection(con))\n", "            return data\n", "        except Exception as e:\n", "            print(\"retriing iteration\", i + 1)\n", "            print(e, \"\\n\", sql[:200])"]}, {"cell_type": "code", "execution_count": null, "id": "dcd5024b-03c9-4975-8c5d-77f82602a5fc", "metadata": {}, "outputs": [], "source": ["transfer_table_name = \"demand_forecast_item_min_max_quantity\"\n", "schema_input = \"ds_etls\""]}, {"cell_type": "code", "execution_count": null, "id": "bab4d244-98f6-4e07-b286-7365560bdc2e", "metadata": {}, "outputs": [], "source": ["base_data_table = transfer_table_name\n", "base_data = get_results_from_query(\n", "    f\"select  dfimm.*, icd.l2_id  from {schema_input}.{base_data_table} dfimm join lake_rpc.item_category_details icd on icd.item_id=dfimm.item_id\"\n", ")\n", "\n", "base_data[\"current_replenishment_ts_ist\"] = pd.to_datetime(\n", "    base_data[\"current_replenishment_ts_ist\"]\n", ")\n", "base_data[\"updated_at_ist\"] = pd.to_datetime(base_data[\"updated_at_ist\"])"]}, {"cell_type": "code", "execution_count": null, "id": "4794a92e-5f54-4a66-8dc7-5386cba92f21", "metadata": {}, "outputs": [], "source": ["today = datetime.now(timezone(\"Asia/Kolkata\")).replace(tzinfo=None)\n", "today_date = pd.to_datetime(today.date())"]}, {"cell_type": "code", "execution_count": null, "id": "e13d30cf-d261-4578-b51a-9fdac0aba89f", "metadata": {}, "outputs": [], "source": ["def read_parquet_data_from_s3(s3_path: str, suffix_list: list = None, verbose=True) -> pd.DataFrame:\n", "    if suffix_list is None:\n", "        data = wr.s3.read_parquet(s3_path, dataset=True)\n", "    else:\n", "        suffix_list = list(suffix_list)\n", "        data_list = []\n", "        if s3_path[-1] != \"/\":\n", "            s3_path += \"/\"\n", "        for suffix in suffix_list:\n", "            data_list.append(wr.s3.read_parquet(s3_path + str(suffix) + \"/\", dataset=True))\n", "        data = pd.concat(data_list)\n", "    return data\n", "\n", "\n", "def write_df_as_parquet_to_s3(data, s3_path, partition_cols=None, suffix=None):\n", "    mode = \"overwrite_partitions\" if partition_cols else \"overwrite\"\n", "    # LOGGER.info(f'writing data to s3 to path {s3_path}')\n", "\n", "    if suffix is not None:\n", "        s3_path = f\"{s3_path.strip('/')}/{suffix}\"\n", "\n", "    wr.s3.to_parquet(data, s3_path, dataset=True, mode=mode, partition_cols=partition_cols)\n", "\n", "\n", "import re\n", "\n", "date_time_now = re.sub(r\"[- :]\", \"_\", str(today).split(\".\")[0])\n", "checkout_date = str(base_data[\"current_replenishment_ts_ist\"].iloc[0].date()).replace(\"-\", \"/\")\n", "\n", "\n", "def read_sheet_data(file_name=None, sheet_id=None, sheet_name=None):\n", "    base_path = (\n", "        f\"s3://prod-dse-projects/replenishment/{checkout_date}/transfer_bumps_sheet_data_store\"\n", "    )\n", "    file_name_for_s3 = (\n", "        (f\"{sheet_name}\")\n", "        .replace(\":\", \"\")\n", "        .replace(\"[\", \"\")\n", "        .replace(\"]\", \"\")\n", "        .replace(\"'\", \"\")\n", "        .replace(\"-\", \"\")\n", "        .replace(\" \", \"_\")\n", "    )\n", "    try:\n", "        df_sheet = pb.from_sheets(sheetid=sheet_id, sheetname=sheet_name, clear_cache=True)\n", "        for subpath in [date_time_now, \"latest\"]:\n", "            write_df_as_parquet_to_s3(\n", "                data=df_sheet, s3_path=f\"{base_path}/{subpath}/{file_name_for_s3}\"\n", "            )\n", "    except Exception as e:\n", "        if str(e) == \"Google sheet operations are disabled in JupyterHub\":\n", "            df_sheet = read_parquet_data_from_s3(f\"{base_path}/latest/{file_name_for_s3}/\")\n", "            # Handle the specific Google Sheets error\n", "            print(\"Google Sheets operations are disabled in JupyterHub. This error is handled.\")\n", "        else:\n", "            raise\n", "\n", "    return df_sheet"]}, {"cell_type": "code", "execution_count": null, "id": "9f36bf30-6967-467a-9cff-00466f4736ef", "metadata": {"tags": []}, "outputs": [], "source": ["def get_sheet_df(sheet_name, factor_col=\"factor\"):\n", "    bump_df = read_sheet_data(\n", "        sheet_id=\"1ylx9iyrw_HM9cT9jzh0fx53OL5ICllF4kV1WiACH_x4\", sheet_name=sheet_name\n", "    )\n", "    # print(bump_df)\n", "    if sheet_name in [\n", "        \"backend_bumps\",\n", "        \"item_outlet_bumps\",\n", "        \"l2_bumps\",\n", "        \"outlet_bumps\",\n", "        \"backend_bumps_all_items\",\n", "        \"backend_bumps_items_ab\",\n", "    ]:\n", "        if \"manual\" in run_id:\n", "            temp_bump_df = bump_df.copy()\n", "            temp_bump_df[\"scheduled\"] = temp_bump_df[\"scheduled\"].apply(\n", "                lambda x: \"adhoc-ran\" if x == \"adhoc\" else x\n", "            )\n", "            try:\n", "                pb.to_sheets(\n", "                    temp_bump_df,\n", "                    sheetid=\"1ylx9iyrw_HM9cT9jzh0fx53OL5ICllF4kV1WiACH_x4\",\n", "                    sheetname=sheet_name,\n", "                )\n", "            except Exception as e:\n", "                if str(e) == \"Google sheet operations are disabled in JupyterHub\":\n", "                    print(\n", "                        \"Google Sheets operations are disabled in JupyterHub. This error is handled.\"\n", "                    )\n", "                else:\n", "                    raise\n", "    bump_df[\"start_date\"] = pd.to_datetime(bump_df[\"start_date\"])\n", "    bump_df[\"end_date\"] = pd.to_datetime(bump_df[\"end_date\"])\n", "\n", "    bump_df = bump_df[(bump_df[\"start_date\"] <= today_date) & (bump_df[\"end_date\"] >= today_date)]\n", "\n", "    bump_df.drop(columns=[\"start_date\", \"end_date\"], inplace=True)\n", "\n", "    bump_df[factor_col] = bump_df[factor_col].astype(float)\n", "    if sheet_name not in [\n", "        \"backend_bumps\",\n", "        \"backend_bumps_all_items\",\n", "        \"backend_bumps_items_ab\",\n", "    ]:\n", "        bump_df[\"outlet_id\"] = bump_df[\"outlet_id\"].astype(float)\n", "\n", "    if sheet_name == \"item_outlet_bumps\":\n", "        bump_df[\"item_id\"] = bump_df[\"item_id\"].astype(int)\n", "    elif sheet_name == \"backend_bumps\":\n", "        bump_df[\"item_id\"] = bump_df[\"item_id\"].astype(int)\n", "    elif sheet_name == \"l2_bumps\":\n", "        bump_df[\"l2_id\"] = bump_df[\"l2_id\"].astype(int)\n", "    print(bump_df)\n", "    if \"scheduled\" in bump_df.columns:\n", "        bump_df[\"scheduled\"] = bump_df[\"scheduled\"].apply(lambda x: x.lower())\n", "        if \"manual\" in run_id:\n", "            bump_df = bump_df.query(\"scheduled == 'adhoc'\")\n", "        else:\n", "            bump_df = bump_df.query(\"scheduled == 'true'\")\n", "        bump_df = bump_df.drop(columns=\"scheduled\")\n", "    print(bump_df)\n", "    return bump_df"]}, {"cell_type": "code", "execution_count": null, "id": "f3ffff31-8b16-4db8-806a-4d721e633581", "metadata": {"tags": []}, "outputs": [], "source": ["def get_mod_df(bump_df, on=[\"outlet_id\", \"l2_id\"]):\n", "    sub_base_data = base_data.merge(bump_df, on=on)\n", "\n", "    sub_base_data[\"max_qty\"] = sub_base_data[\"max_qty\"] * sub_base_data[\"factor\"]\n", "    sub_base_data[\"min_qty\"] = sub_base_data[\"min_qty\"] * sub_base_data[\"factor\"]\n", "\n", "    sub_base_data = sub_base_data.drop(columns=[\"l2_id\", \"factor\"])\n", "\n", "    return sub_base_data"]}, {"cell_type": "code", "execution_count": null, "id": "2f88baee-330c-4118-960c-1e42f2613cf5", "metadata": {}, "outputs": [], "source": ["outlet_cluster_map_query = \"\"\"\n", "with dark_stores as (\n", "SELECT \n", "    fa.cluster,\n", "    fa.is_hp,\n", "    fa.item_id,\n", "    fa.ssc_id AS outlet_id\n", "FROM \n", "    supply_etls.dark_stores_fnv_assortment fa\n", "LEFT JOIN \n", "    lake_retail.console_outlet co \n", "    ON co.id = fa.ssc_id\n", "WHERE \n", "    co.active = 1\n", "    AND date_ist >= current_date - interval '15' day\n", "    AND fa.date_ist =\n", "    (\n", "    SELECT \n", "        max(date_ist)\n", "    FROM \n", "        supply_etls.dark_stores_fnv_assortment\n", "    )\n", "GROUP BY 1,2,3,4\n", "),\n", "\n", "pfma as (\n", "select\n", "    co.id outlet_id,\n", "    ic.item_id\n", "from\n", "    rpc.product_facility_master_assortment pfma\n", "join\n", "    retail.console_outlet co\n", "    on pfma.facility_id = co.facility_id\n", "    and co.active = 1\n", "join\n", "    rpc.item_category_details ic\n", "    on pfma.item_id = ic.item_id\n", "    and ic.l0_id = 1487\n", "where\n", "    master_assortment_substate_id in (1,3)\n", "    and pfma.lake_active_record\n", "    and co.id in (select distinct outlet_id from dark_stores)\n", "group by 1,2   \n", ")\n", "\n", "SELECT \n", "    distinct case when is_hp = 1 then concat(coalesce(ds.cluster,ds2.cluster), '_HP') ELSE coalesce(ds.cluster,ds2.cluster) END AS cluster,\n", "    pfma.outlet_id,\n", "    pfma.item_id\n", "FROM\n", "    pfma\n", "left join\n", "    dark_stores ds\n", "    on pfma.outlet_id = ds.outlet_id\n", "    and pfma.item_id = ds.item_id\n", "left join\n", "    (\n", "    select\n", "        outlet_id,\n", "        max(cluster) cluster\n", "    from\n", "        dark_stores\n", "    group by 1\n", "    ) ds2\n", "    on pfma.outlet_id = ds2.outlet_id\n", "\"\"\"\n", "outlet_cluster_mapping_df = get_results_from_query(outlet_cluster_map_query)"]}, {"cell_type": "code", "execution_count": null, "id": "f350c2d0-76d3-43f9-877b-158e03ce58b6", "metadata": {}, "outputs": [], "source": ["temp_ls = [outlet_cluster_mapping_df]\n", "for all_key in [\"ALL\"]:\n", "    temp = outlet_cluster_mapping_df.copy()\n", "    temp[\"cluster\"] = all_key\n", "    temp_ls.append(temp)\n", "outlet_cluster_mapping_df = pd.concat(temp_ls)"]}, {"cell_type": "code", "execution_count": null, "id": "c1f78c76-8b18-483b-b9a2-ac49d1846aaa", "metadata": {}, "outputs": [], "source": ["item_bump_df = get_sheet_df(\"item_outlet_bumps\")\n", "item_base_data = get_mod_df(item_bump_df, on=[\"outlet_id\", \"item_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "473dc128-e136-46aa-a48a-4d35d6376f9b", "metadata": {}, "outputs": [], "source": ["l2_bump_df = get_sheet_df(\"l2_bumps\")\n", "l2_base_data = get_mod_df(l2_bump_df, on=[\"outlet_id\", \"l2_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "bf1c1021-73fa-410a-bfc3-5b3c47f7a661", "metadata": {}, "outputs": [], "source": ["outlet_bump_df = get_sheet_df(\"outlet_bumps\")\n", "outlet_base_data = get_mod_df(outlet_bump_df, on=[\"outlet_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "27d8356d-5178-4349-a2eb-591b78d13d43", "metadata": {"tags": []}, "outputs": [], "source": ["backend_bump_df = get_sheet_df(\"backend_bumps\")\n", "backend_bump_df = backend_bump_df.merge(\n", "    outlet_cluster_mapping_df[[\"cluster\", \"outlet_id\", \"item_id\"]],\n", "    left_on=[\"backend\", \"item_id\"],\n", "    right_on=[\"cluster\", \"item_id\"],\n", "    how=\"inner\",\n", ")[[\"item_id\", \"factor\", \"outlet_id\"]]\n", "\n", "backend_base_data1 = get_mod_df(backend_bump_df, on=[\"outlet_id\", \"item_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "d221eb5a-b6a2-42c2-86af-faa23fbfc204", "metadata": {}, "outputs": [], "source": ["backend_bump_all_items_df = get_sheet_df(\"backend_bumps_all_items\")\n", "\n", "backend_bump_all_items_df = backend_bump_all_items_df.merge(\n", "    outlet_cluster_mapping_df[[\"cluster\", \"outlet_id\", \"item_id\"]],\n", "    left_on=[\"backend\"],\n", "    right_on=[\"cluster\"],\n", "    how=\"inner\",\n", ")[[\"item_id\", \"factor\", \"outlet_id\"]]\n", "\n", "backend_base_data2 = get_mod_df(backend_bump_all_items_df, on=[\"item_id\", \"outlet_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "61a93a98-7222-44e5-a546-6b3cf2e3d114", "metadata": {}, "outputs": [], "source": ["backend_bump_ab_items_df = get_sheet_df(\"backend_bumps_items_ab\", factor_col=current_slot)\n", "backend_bump_ab_items_df.rename(columns={current_slot: \"factor\"}, inplace=True)\n", "\n", "backend_bump_ab_items_df = backend_bump_ab_items_df.merge(\n", "    outlet_cluster_mapping_df[[\"cluster\", \"outlet_id\", \"item_id\"]],\n", "    left_on=[\"backend\", \"item_id\"],\n", "    right_on=[\"cluster\", \"item_id\"],\n", "    how=\"inner\",\n", ")\n", "\n", "if current_slot == \"slotb\":\n", "    backend_bump_ab_items_df[\"factor\"] = backend_bump_ab_items_df[\n", "        \"factor\"\n", "    ] / backend_bump_ab_items_df[\"slota\"].astype(float)\n", "\n", "backend_bump_ab_items_df = backend_bump_ab_items_df[[\"factor\", \"item_id\", \"outlet_id\"]]\n", "backend_bump_ab_items_df[\"item_id\"] = backend_bump_ab_items_df[\"item_id\"].astype(int)\n", "backend_base_data3 = get_mod_df(\n", "    backend_bump_ab_items_df, on=[\"outlet_id\", \"item_id\"]\n", ").drop_duplicates([\"outlet_id\", \"item_id\"], keep=\"first\")"]}, {"cell_type": "code", "execution_count": null, "id": "7d3c61d2-92d1-4fed-8ab4-45f15bebbcea", "metadata": {"tags": []}, "outputs": [], "source": ["def update_base_data(base_data, updated_data, run_bump_forecasts, run_assertions=True):\n", "    updated_df = pd.concat([base_data, updated_data], axis=0).drop_duplicates(\n", "        subset=[\"facility_id\", \"item_id\", \"current_replenishment_ts_ist\"], keep=\"last\"\n", "    )\n", "\n", "    updated_df[\"min_qty\"] = np.where(\n", "        updated_df[\"min_qty\"] > updated_df[\"max_qty\"],\n", "        updated_df[\"max_qty\"],\n", "        updated_df[\"min_qty\"],\n", "    )\n", "\n", "    if run_bump_forecasts:\n", "        updated_df[\"min_qty\"] = updated_df[\"min_qty\"].apply(np.round).astype(int)\n", "        updated_df[\"max_qty\"] = updated_df[\"max_qty\"].apply(np.round).astype(int)\n", "    else:\n", "        updated_df[\"min_qty\"] = updated_df[\"min_qty\"].apply(np.ceil).astype(int)\n", "        updated_df[\"max_qty\"] = updated_df[\"max_qty\"].apply(np.ceil).astype(int)\n", "\n", "    updated_df[\"min_qty\"] = np.where(updated_df[\"min_qty\"] < 0, 0, updated_df[\"min_qty\"])\n", "    updated_df[\"max_qty\"] = np.where(updated_df[\"max_qty\"] < 0, 0, updated_df[\"max_qty\"])\n", "\n", "    print(\n", "        \"Total difference of max_qty between updated data and base data : \",\n", "        np.round(updated_df[\"max_qty\"].sum() - base_data[\"max_qty\"].sum(), 0),\n", "    )\n", "\n", "    updated_df[\"updated_at_ist\"] = datetime.now(timezone(\"Asia/Kolkata\")).replace(tzinfo=None)\n", "\n", "    if run_assertions:\n", "        assert (\n", "            updated_df.shape == base_data.shape\n", "        ), f\"Processed dataframe shape {updated_df.shape} is different than base data shape {base_data.shape}, please check\"\n", "    else:\n", "        print(\"Difference in rows :\", updated_df.shape[0] - base_data.shape[0])\n", "    return updated_df"]}, {"cell_type": "code", "execution_count": null, "id": "5dbfdc95-cda0-4f20-99ea-1ae48328d869", "metadata": {}, "outputs": [], "source": ["if current_slot == \"slotb\" and \"scheduled\" in run_id:\n", "    print(f\"mode {current_slot},{run_id}, 1\")\n", "    final_dropped_updated_df = pd.concat([backend_base_data3]).drop_duplicates(\n", "        subset=[\"item_id\", \"outlet_id\"], keep=\"first\"\n", "    )\n", "elif current_slot == \"slota\" and \"scheduled\" in run_id:\n", "    print(f\"mode {current_slot},{run_id}, 2\")\n", "    final_dropped_updated_df = pd.concat(\n", "        [\n", "            item_base_data,\n", "            l2_base_data,\n", "            outlet_base_data,\n", "            backend_base_data1,\n", "            backend_base_data2,\n", "            backend_base_data3,\n", "        ]\n", "    ).drop_duplicates(subset=[\"item_id\", \"outlet_id\"], keep=\"first\")\n", "else:  ## this is for all adhoc runs\n", "    print(f\"mode {current_slot},{run_id}, 3\")\n", "    final_dropped_updated_df = pd.concat(\n", "        [\n", "            item_base_data,\n", "            l2_base_data,\n", "            outlet_base_data,\n", "            backend_base_data1,\n", "            backend_base_data2,\n", "        ]\n", "    ).drop_duplicates(subset=[\"item_id\", \"outlet_id\"], keep=\"first\")"]}, {"cell_type": "code", "execution_count": null, "id": "0ac18171-e3aa-4254-bb4c-23e91c44fd94", "metadata": {}, "outputs": [], "source": ["#\n", "updated_at_ist = pd.to_datetime(datetime.now(timezone(\"Asia/Kolkata\")).replace(tzinfo=None))\n", "final_updated_df = update_base_data(base_data, final_dropped_updated_df, True)\n", "final_updated_df.drop(columns=\"l2_id\", inplace=True)\n", "final_dropped_updated_df[\"updated_at_ist\"] = updated_at_ist\n", "final_updated_df[\"updated_at_ist\"] = updated_at_ist\n", "final_dropped_updated_df = final_dropped_updated_df.astype(final_updated_df.dtypes.to_dict())"]}, {"cell_type": "code", "execution_count": null, "id": "8d812f88-26aa-4f38-bf66-e161d118e435", "metadata": {}, "outputs": [], "source": ["def update_transfers_table(append_data=final_updated_df, upsert_data=final_dropped_updated_df):\n", "    normal_kwargs = {\n", "        \"schema_name\": f\"ds_etls\",\n", "        \"table_name\": f\"demand_forecast_item_min_max_quantity\",\n", "        \"column_dtypes\": [\n", "            {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"facility_id\"},\n", "            {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"outlet id\"},\n", "            {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"item id\"},\n", "            {\"name\": \"name\", \"type\": \"VARCHAR\", \"description\": \"item name\"},\n", "            {\"name\": \"item_type\", \"type\": \"VARCHAR\", \"description\": \"item type\"},\n", "            {\"name\": \"quantile\", \"type\": \"REAL\", \"description\": \"quantile\"},\n", "            {\"name\": \"min_qty\", \"type\": \"INTEGER\", \"description\": \"Min Stock level\"},\n", "            {\"name\": \"max_qty\", \"type\": \"INTEGER\", \"description\": \"Max Stock level\"},\n", "            {\n", "                \"name\": \"current_replenishment_ts_ist\",\n", "                \"type\": \"TIMESTAMP(6)\",\n", "                \"description\": \"Current Replenishment Timestamp\",\n", "            },\n", "            {\n", "                \"name\": \"updated_at_ist\",\n", "                \"type\": \"TIMESTAMP(6)\",\n", "                \"description\": \"Latest update time in IST\",\n", "            },\n", "        ],\n", "        \"primary_key\": [\"facility_id\", \"item_id\", \"current_replenishment_ts_ist\"],\n", "        \"sort_key\": [\"facility_id\", \"item_id\"],\n", "        # \"incremental_key\": \"updated_at_ist\",\n", "        \"load_type\": \"upsert\",\n", "        \"force_upsert_without_increment_check\": True,\n", "        \"run_maintenance\": True,\n", "        \"table_description\": \"demand_forecast_item_min_max_quantity\",\n", "    }\n", "    normal_kwargs[\"table_name\"] = \"demand_forecast_item_min_max_quantity\"\n", "    normal_kwargs[\"load_type\"] = \"upsert\"\n", "    pb.to_trino(upsert_data, **normal_kwargs)\n", "    normal_kwargs[\"table_name\"] = \"demand_forecast_item_min_max_quantity_log\"\n", "    normal_kwargs[\"load_type\"] = \"append\"\n", "    append_data[\"frontend_id\"] = -1\n", "    normal_kwargs[\"column_dtypes\"] = normal_kwargs[\"column_dtypes\"] + [\n", "        {\"name\": \"frontend_id\", \"type\": \"INTEGER\", \"description\": \"depreciated\"}\n", "    ]\n", "    pb.to_trino(append_data, **normal_kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "8043e6fd-3fe7-46da-afda-a39de47c8071", "metadata": {}, "outputs": [], "source": ["for i in range(3):\n", "    try:\n", "        update_transfers_table(append_data=final_updated_df, upsert_data=final_dropped_updated_df)\n", "        break\n", "    except Exception as e:\n", "        print(f\"Retrying iteration {i + 1}\")\n", "        print(e)\n", "        raise\n", "else:\n", "    print(\"All push attempts failed.\")"]}, {"cell_type": "code", "execution_count": null, "id": "1015b0e7-5d68-4b3f-b7c4-eac5b785fe94", "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.send_slack_message(\n", "        channel=\"#bl-fnv-replenishment-dag-run-alerts\",\n", "        text=f\":large_blue_square: \\nFnv transfers dag ended . \\n dag_link:- {dag_link} \\n\",\n", "    )\n", "except Exception as e:\n", "    print(f\"Failed to send Slack message: {e}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}