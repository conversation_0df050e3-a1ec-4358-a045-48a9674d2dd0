alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: transfer_numbers_using_ordering
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_science
notebook:
  parameters:
    run_id: '{{ run_id }}'
    task_instance_key_str: '{{ task_instance_key_str }}'
  priority_weight: 2
owner:
  email: <EMAIL>
  slack_id: U071XRARZAS
path: data_science/replenishment_automation/etl/transfer_numbers_using_ordering
paused: false
pool: data_science_pool
project_name: replenishment_automation
schedule:
  end_date: '2025-08-29T00:00:00'
  interval: 0 0 22 9 *
  start_date: '2025-01-08T00:00:00'
schedule_type: fixed
sla: 122 minutes
support_files: []
tags: []
template_name: notebook
version: 1
