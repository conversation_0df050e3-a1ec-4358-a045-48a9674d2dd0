alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
  - channel: bl-personalization-notifications
concurrency: 3
dag_name: user_product_lightfm_v1_incremental
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_science
notebooks:
- executor_config:
    load_type: very-high-mem
    node_type: spot
  name: data_fetch_incremental_v1
  parameters:
    last_success_timestamp: '{{ prev_start_date_success }}'
  retries: 3
  retry_delay_in_seconds: 15
  tag: level1
- executor_config:
    load_type: very-high-mem
    node_type: spot
  name: lightfm_model_training_partial_v1
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level2
- executor_config:
    load_type: very-high-mem
    node_type: spot
  name: data_push_incremental_v1
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level3
owner:
  email: <EMAIL>
  slack_id: U03SJ8DPFND
path: data_science/user_product_lightfm_v1_incremental/etl/user_product_lightfm_v1_incremental
paused: false
pool: data_science_pool
project_name: user_product_lightfm_v1_incremental
schedule:
  end_date: '2025-08-31T00:00:00'
  interval: 0 20 * * *
  start_date: '2025-03-17T00:00:00'
schedule_type: fixed
sla: 121 minutes
support_files:
- queries/*
- utils.py
- kafka_utils.py
tags: []
template_name: multi_notebook
version: 19
