alert_configs:
  slack:
  - channel: bl-personalization-notifications
dag_name: personalisation_monet_user_history_incremental
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_science
notebook:
  parameters:
    last_success_timestamp: '{{ prev_start_date_success }}'
  retries: 3
  retry_delay_in_seconds: 15
owner:
  email: <EMAIL>
  slack_id: U07AD773UJZ
path: data_science/personalisation/etl/personalisation_monet_user_history_incremental
paused: false
pool: data_science_pool
project_name: personalisation
schedule:
  end_date: '2025-09-17T00:00:00'
  interval: 30 0 * * *
  start_date: '2024-09-06T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- utils.py
- Queries/*
tags: []
template_name: notebook
version: 4
