{"cells": [{"cell_type": "code", "execution_count": null, "id": "df7a1f5b-1918-4d6d-b7ed-3290cde3646a", "metadata": {}, "outputs": [], "source": ["!pip install -q pqdm"]}, {"cell_type": "code", "execution_count": null, "id": "44e38e19-8e37-4979-b390-5197cfc72fef", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import os\n", "import sys\n", "import numpy as np\n", "import gc\n", "import json\n", "import time\n", "from sqlalchemy.exc import DatabaseError as SA_DBError  # SQLAlchemy's wrapped error\n", "from pyhive.exc import DatabaseError as Pyhive_DBError  # Actual driver-level error\n", "from pqdm.processes import pqdm\n", "import random\n", "\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "import logging\n", "import multiprocessing\n", "\n", "\n", "def setup_logger():\n", "    logger = multiprocessing.get_logger()\n", "    logger.setLevel(logging.INFO)\n", "    handler = logging.StreamHandler()\n", "    formatter = logging.Formatter(\"[%(processName)s | %(asctime)s] %(message)s\", datefmt=\"%H:%M:%S\")\n", "    handler.set<PERSON><PERSON><PERSON><PERSON>(formatter)\n", "    if not logger.hasHandlers():\n", "        logger.add<PERSON><PERSON><PERSON>(handler)\n", "    return logger\n", "\n", "\n", "def sheets_retry_call(\n", "    fn, args=(), kwargs={}, max_retries=5, base_delay=60, max_delay=900, jitter=True\n", "):\n", "    delay = base_delay\n", "    for attempt in range(1, max_retries + 1):\n", "        try:\n", "            return fn(*args, **kwargs)\n", "        except Exception as e:\n", "            if attempt == max_retries:\n", "                raise\n", "            sleep_time = delay + (random.uniform(0, delay) / 60) if jitter else delay\n", "            print(f\"[Retry {attempt}/{max_retries}] Error: {e}. Retrying in {sleep_time:.2f}s...\")\n", "            time.sleep(min(sleep_time, max_delay))\n", "            delay = min(delay * 2, max_delay)"]}, {"cell_type": "code", "execution_count": null, "id": "5b67ba34-dd9e-4d87-931a-25cd7f87e780", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "9e82ddf2-ca51-46f6-ac26-1b2d7063e26b", "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "\n", "# time_stamp = pd.read_csv('Logs - Monet timestamp log.csv')\n", "\n", "time_stamp = sheets_retry_call(\n", "    pb.from_sheets, args=(\"1NGmZryChDxAqpB7Ty-pYhNpkCEoMeRD4l96kWKJJ08Y\", \"Monet_timestamp_log\")\n", ")\n", "\n", "print(time_stamp)\n", "\n", "# Given date\n", "last_run_date = time_stamp[\"last successful run\"][0]\n", "# last_run_date = \"2025-04-19\"\n", "given_date = datetime.strptime(last_run_date, \"%Y-%m-%d\")\n", "\n", "# Current date\n", "current_date = datetime.now()\n", "\n", "# Difference in days\n", "delta_days = (current_date - given_date).days\n", "\n", "print(\"last successfully ran on :\", given_date)\n", "print(\"running for :\", delta_days)"]}, {"cell_type": "code", "execution_count": null, "id": "c693e390-d5b3-4f4e-9ca4-763a571e1d90", "metadata": {}, "outputs": [], "source": ["if delta_days <= 2:\n", "    total_buckets = 6\n", "elif delta_days >= 3 and delta_days < 7:\n", "    total_buckets = 8\n", "else:\n", "    total_buckets = 30\n", "\n", "print(total_buckets)"]}, {"cell_type": "code", "execution_count": null, "id": "76376f8e-5abb-4cb8-ac45-c54087792a2a", "metadata": {"tags": []}, "outputs": [], "source": ["backfill_status = sheets_retry_call(\n", "    pb.from_sheets, args=(\"1NGmZryChDxAqpB7Ty-pYhNpkCEoMeRD4l96kWKJJ08Y\", \"monet_backfill_log\")\n", ")\n", "\n", "if len(backfill_status) == 0:\n", "    print(\"*\" * 50)\n", "    print(\"running for a new run\")\n", "    print(\"*\" * 50)\n", "\n", "    for i in range(total_buckets):\n", "        backfill_status.loc[len(backfill_status)] = {\n", "            \"bucket_no\": i + 1,\n", "            \"status\": \"not done\",\n", "        }\n", "\n", "    sheets_retry_call(\n", "        pb.to_sheets,\n", "        args=(\n", "            backfill_status,\n", "            \"1NGmZryChDxAqpB7Ty-pYhNpkCEoMeRD4l96kWKJJ08Y\",\n", "            \"monet_backfill_log\",\n", "        ),\n", "    )\n", "\n", "\n", "# backfill_status = pd.read_csv('Logs - monet - backfill log.csv')\n", "new_buckets = backfill_status[backfill_status.status == \"not done\"].bucket_no.to_list()\n", "print(new_buckets)"]}, {"cell_type": "code", "execution_count": null, "id": "5f2cd0e2-b43d-4db1-9d19-da9a87fa413c", "metadata": {}, "outputs": [], "source": ["import ast\n", "\n", "\n", "def update_logs(bucket_no):\n", "\n", "    backfill_status = sheets_retry_call(\n", "        pb.from_sheets, args=(\"1NGmZryChDxAqpB7Ty-pYhNpkCEoMeRD4l96kWKJJ08Y\", \"monet_backfill_log\")\n", "    )\n", "    ind = backfill_status[backfill_status.bucket_no == bucket_no].index[0]\n", "    backfill_status.loc[ind, \"status\"] = \"done\"\n", "    sheets_retry_call(\n", "        pb.to_sheets,\n", "        args=(\n", "            backfill_status,\n", "            \"1NGmZryChDxAqpB7Ty-pYhNpkCEoMeRD4l96kWKJJ08Y\",\n", "            \"monet_backfill_log\",\n", "        ),\n", "    )\n", "    print(\"logs pushed to sheets for bucket no :\", bucket_no)\n", "    return\n", "\n", "\n", "sys.path.append(cwd)\n", "\n", "\n", "def pull_data(i, days, attribute, attribute_id, job_id):\n", "    logger.info(f\"{job_id}, processing {attribute}, {days}, bucket no : {i}\")\n", "    params_dict = {\n", "        \"total_buckets\": total_buckets,\n", "        \"last_run\": delta_days,\n", "        \"start\": days,\n", "        \"bucket_no\": i,\n", "        \"attribute_name\": attribute,\n", "        \"attribute_id\": attribute_id,\n", "    }\n", "    temp_df = query_with_retries(escaped_sql_code.format(**params_dict))\n", "\n", "    if days == 360:\n", "        df = (\n", "            temp_df.groupby(\"user_id\")\n", "            .apply(\n", "                lambda group: pd.Series(\n", "                    {\n", "                        f'{days}_{params_dict[\"attribute_id\"]}': str(\n", "                            group.sort_values(\"rnk\")[params_dict[\"attribute_id\"]].tolist()\n", "                        ),\n", "                        f'{params_dict[\"attribute_id\"]}_lb': dict(\n", "                            zip(group[params_dict[\"attribute_id\"]], group[\"max_ordered_date\"])\n", "                        ),\n", "                    }\n", "                )\n", "            )\n", "            .reset_index()\n", "        )\n", "\n", "    else:\n", "        # Group by user and create the list\n", "        df = (\n", "            temp_df.groupby(\"user_id\")\n", "            .apply(\n", "                lambda group: pd.Series(\n", "                    {\n", "                        f'{days}_{params_dict[\"attribute_id\"]}': str(\n", "                            group.sort_values(\"rnk\")[params_dict[\"attribute_id\"]].tolist()\n", "                        )\n", "                    }\n", "                )\n", "            )\n", "            .reset_index()\n", "        )\n", "\n", "    df.rename(columns={\"user_id\": \"dim_customer_key\"}, inplace=True)\n", "    return df\n", "\n", "\n", "def read_sql_query(path):\n", "    with open(path) as f:\n", "        query = f.read()\n", "    return query\n", "\n", "\n", "PYHIVE_BACKOFF_SCHEDULE = {1: 10, 2: 15, 3: 25}\n", "\n", "\n", "def safe_eval(x):\n", "    \"\"\"\n", "    • If x is a non-empty string – try literal_eval\n", "    • Otherwise return x unchanged (or np.nan / [] if you prefer)\n", "    \"\"\"\n", "    if isinstance(x, str) and x.strip():\n", "        try:\n", "            return ast.literal_eval(x)\n", "        except (<PERSON><PERSON><PERSON><PERSON>, SyntaxError):\n", "            # decide what you want here – raise, log, or replace\n", "            return np.nan\n", "    return x\n", "\n", "\n", "def query_with_retries(\n", "    query, con=pb.get_connection(\"[Warehouse] Trino\"), max_retries=3, base_delay=10\n", "):\n", "    retry_count = 1\n", "    while retry_count <= max_retries:\n", "        try:\n", "            df = pd.read_sql_query(query, con)\n", "            return df\n", "        except SA_DBError as e:\n", "            orig = getattr(e, \"orig\", None)\n", "            if isinstance(orig, Pyhive_DBError):\n", "                sleep_minutes = PYHIVE_BACKOFF_SCHEDULE.get(retry_count, 25)\n", "                jitter = random.randint(15, 60)  # jitter in seconds\n", "                total_sleep = (sleep_minutes * 60) + jitter\n", "                logger.info(\n", "                    f\"{job_id} Attempt {retry_count}: PyHive DBError: Sleeping {sleep_minutes} min + {jitter}s jitter before retrying (due to DB error)...\"\n", "                )\n", "                time.sleep(total_sleep)\n", "                retry_count += 1\n", "            else:\n", "                print(f\"[Attempt {retry_count}] SQLAlchemy DBError (non-PyHive): {e}\")\n", "                raise\n", "        except Exception as e:\n", "            delay = (base_delay**retry_count) + random.randint(1, 10)\n", "            print(f\"[Attempt {retry_count}] General Exception: {e}\")\n", "            print(f\"Retrying in {delay} seconds...\")\n", "            time.sleep(delay)\n", "            retry_count += 1\n", "\n", "    print(\"All retries exhausted. Query failed.\")\n", "    raise RuntimeError(\"Query failed after maximum retries.\")\n", "\n", "\n", "# getting sql code\n", "sql_code = read_sql_query(f\"{cwd}/Queries/sql_code.sql\")\n", "escaped_sql_code = sql_code.replace(\"%\", \"%%\")\n", "\n", "# running for all buckets\n", "bucket_no = list(range(1, (total_buckets + 1)))\n", "days = [90, 180, 360]\n", "\n", "attributes = {\n", "    \"brand_name\": \"brand_id\",\n", "    \"l1_category\": \"l1_category_id\",\n", "    \"l2_category\": \"l2_category_id\",\n", "}\n", "\n", "from kafka_utils import *\n", "\n", "context = \"personalisation_monet_user_history_2_0_0\"\n", "logger = setup_logger()\n", "\n", "\n", "def np_encoder(object):\n", "    if isinstance(object, np.generic):\n", "        return object.item()"]}, {"cell_type": "code", "execution_count": null, "id": "5e90a2b5-1028-4ab4-a33d-0128f4255bb9", "metadata": {"tags": []}, "outputs": [], "source": ["def process_bucket(bucket):\n", "    final_df = pd.DataFrame(columns=[\"dim_customer_key\"])\n", "\n", "    for keys in attributes.keys():\n", "        for day in days:\n", "            job_id = f\"[PID {os.getpid()} | bucket {bucket} | {keys} | {day}]\"\n", "            df = pull_data(bucket, day, keys, attributes[keys], job_id)\n", "            if df is not None and not df.empty:\n", "                final_df = pd.merge(final_df, df, on=\"dim_customer_key\", how=\"outer\").reset_index(\n", "                    drop=True\n", "                )\n", "            logger.info(f\"{job_id} bucket={bucket}, shape={final_df.shape}\")\n", "\n", "    for col in [\n", "        \"90_brand_id\",\n", "        \"180_brand_id\",\n", "        \"360_brand_id\",\n", "        \"90_l1_category_id\",\n", "        \"180_l1_category_id\",\n", "        \"360_l1_category_id\",\n", "        \"90_l2_category_id\",\n", "        \"180_l2_category_id\",\n", "        \"360_l2_category_id\",\n", "    ]:\n", "        final_df[col] = final_df[col].apply(lambda x: x if pd.notna(x) else [])\n", "\n", "    for col in [\"brand_id_lb\", \"l1_category_id_lb\", \"l2_category_id_lb\"]:\n", "        final_df[col] = final_df[col].apply(lambda x: x if pd.notna(x) else {})\n", "\n", "    final_df.rename(\n", "        columns={\n", "            \"90_brand_id\": \"bought_90_days_brand_id\",\n", "            \"180_brand_id\": \"bought_180_days_brand_id\",\n", "            \"360_brand_id\": \"bought_360_days_brand_id\",\n", "            \"90_l1_category_id\": \"bought_90_days_l1_category_id\",\n", "            \"180_l1_category_id\": \"bought_180_days_l1_category_id\",\n", "            \"360_l1_category_id\": \"bought_360_days_l1_category_id\",\n", "            \"90_l2_category_id\": \"bought_90_days_l2_category_id\",\n", "            \"180_l2_category_id\": \"bought_180_days_l2_category_id\",\n", "            \"360_l2_category_id\": \"bought_360_days_l2_category_id\",\n", "        },\n", "        inplace=True,\n", "    )\n", "\n", "    final_df.bought_90_days_brand_id = final_df.bought_90_days_brand_id.apply(safe_eval)\n", "    final_df.bought_180_days_brand_id = final_df.bought_180_days_brand_id.apply(safe_eval)\n", "    final_df.bought_360_days_brand_id = final_df.bought_360_days_brand_id.apply(safe_eval)\n", "\n", "    final_df.bought_90_days_l1_category_id = final_df.bought_90_days_l1_category_id.apply(safe_eval)\n", "    final_df.bought_180_days_l1_category_id = final_df.bought_180_days_l1_category_id.apply(\n", "        safe_eval\n", "    )\n", "    final_df.bought_360_days_l1_category_id = final_df.bought_360_days_l1_category_id.apply(\n", "        safe_eval\n", "    )\n", "\n", "    final_df.bought_90_days_l2_category_id = final_df.bought_90_days_l2_category_id.apply(safe_eval)\n", "    final_df.bought_180_days_l2_category_id = final_df.bought_180_days_l2_category_id.apply(\n", "        safe_eval\n", "    )\n", "    final_df.bought_360_days_l2_category_id = final_df.bought_360_days_l2_category_id.apply(\n", "        safe_eval\n", "    )\n", "\n", "    push_dict = final_df.set_index(\"dim_customer_key\").to_dict(orient=\"index\")\n", "    print(f\"bucket={bucket}, final length={len(push_dict)}\")\n", "\n", "    temp = [[key, value] for key, value in push_dict.items()]\n", "    final_df_push = pd.DataFrame(temp, columns=[\"customer_id\", \"value\"])\n", "    kafka_dict = final_df_push.to_dict(orient=\"records\")\n", "\n", "    push_to_kafka(\n", "        entities=[f\"user:{i['customer_id']}\" for i in kafka_dict],\n", "        context=context,\n", "        ctx_properties=[\n", "            {\"ctx_value\": json.dumps(i[\"value\"], default=np_encoder)} for i in kafka_dict\n", "        ],\n", "        dry_run=False,\n", "    )\n", "\n", "    print(f\"Done for bucket no : {bucket}\")\n", "\n", "    update_logs(bucket)\n", "\n", "    del final_df, df, push_dict, kafka_dict, final_df_push\n", "    gc.collect()\n", "    return\n", "\n", "\n", "# Now run with 3 parallel jobs\n", "res = pqdm(new_buckets, process_bucket, n_jobs=3)"]}, {"cell_type": "code", "execution_count": null, "id": "466eb2c1-01ba-48b7-af29-5760308d6bbf", "metadata": {}, "outputs": [], "source": ["time_stamp.loc[0, \"last successful run\"] = datetime.now().strftime(\"%Y-%m-%d\")\n", "time_stamp"]}, {"cell_type": "code", "execution_count": null, "id": "942b1d38-08f2-43f7-826b-1e724284ed33", "metadata": {}, "outputs": [], "source": ["sheets_retry_call(\n", "    pb.to_sheets,\n", "    args=(\n", "        time_stamp,\n", "        \"1NGmZryChDxAqpB7Ty-pYhNpkCEoMeRD4l96kWKJJ08Y\",\n", "        \"Monet_timestamp_log\",\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "94eebbed-f795-49d8-9f8e-6994b5e3636d", "metadata": {}, "outputs": [], "source": ["sheets_retry_call(\n", "    pb.to_sheets,\n", "    args=(\n", "        backfill_status.iloc[0:0],\n", "        \"1NGmZryChDxAqpB7Ty-pYhNpkCEoMeRD4l96kWKJJ08Y\",\n", "        \"monet_backfill_log\",\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2d187fd6-c926-4c66-a96c-2860e8de8cb8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}