WITH customers AS
  (SELECT DISTINCT o.dim_customer_key AS customer_id,
                   (o.dim_customer_key % {total_buckets}) + 1 AS bucket_no
   FROM dwh.fact_sales_order_item_details o
   WHERE order_create_dt_ist >= current_date - INTERVAL '{last_run}' DAY
     AND order_create_dt_ist <= current_date - INTERVAL '1' DAY
     AND o.city_name!=''
     AND o.dim_customer_key IS NOT NULL
     AND o.order_current_status='DELIVERED'),
     product AS
  (SELECT DISTINCT product_id,
                   {attribute_name},
                   {attribute_id}
   FROM dwh.dim_product
   WHERE is_current
     AND is_product_enabled
     AND brand_id IS NOT NULL
     AND l0_category NOT IN ('Specials',
                             'Trial new tree')
     AND product_type_id NOT IN (11780, -- share deal
 459, -- Freebie
 11939, -- Sample
 11778, -- Free
 11791, -- Grofers Go Gif bt Items
 11927, -- Special Deal
 11929, -- VIP Deals
 11930, -- Click Deals
 11932, -- Whatsapp Deals
 11936, -- List Deals,
 12184 -- Print as a service
 )),
     agg1 AS
  (SELECT o.dim_customer_key AS user_id,
          {attribute_name},
          {attribute_id},
          order_create_dt_ist,
          date_diff('day', date(order_create_dt_ist), CURRENT_DATE) AS days_diff,
          count(DISTINCT o.cart_id) AS no_of_orders
   FROM dwh.fact_sales_order_item_details o
   JOIN product p ON o.product_id=p.product_id
   JOIN customers c ON c.customer_id = o.dim_customer_key
   WHERE order_create_dt_ist > current_date - INTERVAL '{start}' DAY
     AND order_create_dt_ist <= current_date - INTERVAL '1' DAY
     AND o.city_name!=''
     AND o.dim_customer_key IS NOT NULL
     AND c.bucket_no = {bucket_no}
     AND o.order_current_status='DELIVERED'
     AND o.order_type NOT IN ('InternalForwardOrder',
                              'InternalReverseOrder',
                              'DropShippingInternalReverseOrder')
   GROUP BY 1,
            2,
            3,
            4,
            5)
SELECT *,
       ROW_NUMBER() OVER (PARTITION BY user_id
                          ORDER BY weighted_value DESC) AS rnk
FROM
  (SELECT user_id,
          {attribute_name},
          {attribute_id},
          max(order_create_dt_ist) as max_ordered_date,
          sum(no_of_orders * exp(-(ln(2) / 45) * days_diff)) AS weighted_value
   FROM agg1
   GROUP BY 1,
            2,
            3)