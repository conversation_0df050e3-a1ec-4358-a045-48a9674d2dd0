alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: personalisation_monet_user_history_v2
dag_type: etl
escalation_priority: low
execution_timeout: 3000
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: very-high-mem
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
  retries: 3
  retry_delay_in_seconds: 100
owner:
  email: <EMAIL>
  slack_id: U07AD773UJZ
path: data_science/personalisation/etl/personalisation_monet_user_history_v2
paused: false
pool: data_science_pool
project_name: personalisation
schedule:
  end_date: '2025-07-09T00:00:00'
  interval: 30 0 * * *
  start_date: '2025-06-06T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- kafka_utils.py
- Queries/*
tags: []
template_name: notebook
version: 1
