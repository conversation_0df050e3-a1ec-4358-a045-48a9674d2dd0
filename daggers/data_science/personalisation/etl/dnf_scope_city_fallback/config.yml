alert_configs:
  slack:
  - channel: bl-personalization-notifications
dag_name: dnf_scope_city_fallback
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: very-high-mem
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03SV2AN82C
path: data_science/personalisation/etl/dnf_scope_city_fallback
paused: false
pool: data_science_pool
project_name: personalisation
schedule:
  end_date: '2025-08-08T00:00:00'
  interval: 0 18 * * *
  start_date: '2024-08-22T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 3
