alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 3
dag_name: gift_assortment_recos_blinkgpt
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebooks:
- executor_config:
    load_type: very-high-mem
    node_type: spot
  name: gift_assortment_recos
  parameters: null
  priority_weight: 6
  retries: 1
  retry_delay_in_seconds: 5
  tag: level1
owner:
  email: <EMAIL>
  slack_id: U05UDE332QY
path: data_science/personalisation/etl/gift_assortment_recos_blinkgpt
paused: false
pool: data_science_pool
project_name: personalisation
schedule:
  end_date: '2025-06-21T00:00:00'
  interval: 30 4 * * TUE
  start_date: '2025-03-04T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- queries/product_meta_data_properties.sql
- queries/new_item_pricing.sql
- src/models/optim_sentence_transformers.py
- utils.py
- utils_pull_product_data.py
tags: []
template_name: multi_notebook
version: 1
