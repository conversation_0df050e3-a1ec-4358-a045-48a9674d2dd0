{"cells": [{"cell_type": "code", "execution_count": null, "id": "06b75f49-de12-4de4-9ef4-fba7902608e6", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "4786acb3-bd44-4ad3-b30e-be24c82c2f10", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "d12bb3c7-c545-44a7-b2c5-211046bc4ac7", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "bfd49558-e5f7-43de-926e-104ff1d81406", "metadata": {}, "outputs": [], "source": ["cwd"]}, {"cell_type": "code", "execution_count": null, "id": "37feae35-b97b-4672-b4c6-3bc1f2e3d38a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "248771f1-a6e8-48ed-af77-5ab938acd0b1", "metadata": {"tags": []}, "outputs": [], "source": ["# %%capture\n", "!pip install -q scipy==1.8.0\n", "!pip install -q aiohttp==3.8.1\n", "!pip install -q fsspec==2023.1.0\n", "!pip install -q aiobotocore==2.4.2\n", "!pip install -q pymysql==1.0.2\n", "!pip install -q gremlinpython==3.6.2\n", "!pip install -q botocore==1.27.59\n", "!pip install -q progressbar2==4.2.0\n", "!pip install -q backoff==2.2.1\n", "!pip install -q pandas==1.5.1\n", "!pip install -q pg8000==1.29.4\n", "!pip install -q opensearch-py==2.1.1\n", "!pip install -q boto3==1.24.59\n", "!pip install -q requests-aws4auth==1.2.2\n", "!pip install -q s3transfer==0.6.0\n", "!pip install -q aenum==3.1.11\n", "!pip install -q scramp==1.4.4\n", "!pip install -q python-utils==3.5.2\n", "!pip install -q awswrangler==2.19.0\n", "!pip install -q s3fs==2023.1.0\n", "!pip install -q fasttext\n", "!pip install -q scikit-learn\n", "\n", "!pip install -U sentence-transformers\n", "!pip install faiss-cpu==1.8.0 --no-cache"]}, {"cell_type": "code", "execution_count": null, "id": "d46477a1-b160-4b2f-98b0-5d9d2edf89fa", "metadata": {}, "outputs": [], "source": ["!pip install huggingface_hub==0.25.2"]}, {"cell_type": "code", "execution_count": null, "id": "3baca100-796b-4999-b8c9-abb334dbde75", "metadata": {"tags": []}, "outputs": [], "source": ["# %%capture\n", "!pip install -q optimum[onnxruntime]>=1.10.0\n", "\n", "!pip uninstall awswrangler --y\n", "!pip install -q awswrangler==2.19.0"]}, {"cell_type": "code", "execution_count": null, "id": "8564cc54-bfe8-4556-b0cc-7326aad160f5", "metadata": {"tags": []}, "outputs": [], "source": ["# %%capture\n", "!pip install txtai[ann] --no-cache-dir"]}, {"cell_type": "code", "execution_count": null, "id": "02b3bd09-7ec3-4ee9-aee2-42425738872b", "metadata": {}, "outputs": [], "source": ["# %%capture\n", "!pip uninstall sqlalchemy -y\n", "!pip install sqlalchemy==1.3.23"]}, {"cell_type": "code", "execution_count": null, "id": "f1243c26-9e05-4229-99f5-a9300594879c", "metadata": {}, "outputs": [], "source": ["# %%capture\n", "!pip uninstall numpy -y\n", "!pip install -q numpy==1.23.4"]}, {"cell_type": "code", "execution_count": null, "id": "0153d007-98b3-4d31-8a07-d6a85704e240", "metadata": {}, "outputs": [], "source": ["!pip uninstall awswrangler --y\n", "!pip install -q awswrangler==2.19.0"]}, {"cell_type": "code", "execution_count": null, "id": "a34696d5-54db-4837-8cb9-2b69a73efebf", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1a9aa74f-d981-46a6-8f60-1d5af4e953e9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4d3baa0e-0890-44e3-8682-03d2a7756bd6", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import re\n", "import ast\n", "from datetime import datetime, timedelta\n", "import utils as utls\n", "import pencilbox as pb\n", "import itertools\n", "\n", "# import fasttext\n", "import pickle\n", "\n", "# import sqlalchemy as sa\n", "# from src.models.autocomplete import Autocomplete, AutoTrieNode\n", "# from src.models.tokenization import TrieToken<PERSON>, TrieNode"]}, {"cell_type": "code", "execution_count": null, "id": "66cfb15c-3351-44ff-a374-b26e6cb09a66", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "975db552-cbe9-4bb7-bc20-a57114aa7b1e", "metadata": {}, "outputs": [], "source": ["from txtai.ann import ANNFactory"]}, {"cell_type": "code", "execution_count": null, "id": "c909e6d5-f491-4058-9629-fa0a923b29b5", "metadata": {}, "outputs": [], "source": ["from src.models.optim_sentence_transformers import (\n", "    SentenceTransformerOptim,\n", "    optimize_model,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "cefbe7e3-e250-4598-ac3b-0b2375e4b816", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "85ca109a-ceaf-4c3c-9f14-c091368890a2", "metadata": {}, "outputs": [], "source": ["import json"]}, {"cell_type": "code", "execution_count": null, "id": "0aae84c3-cdb2-4c08-8c28-2da463a8ff38", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)\n", "pd.set_option(\"display.max_rows\", None)\n", "pd.set_option(\"display.float_format\", lambda x: \"%.5f\" % x)\n", "pd.set_option(\"display.max_colwidth\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "c5f4ee53-4955-474c-884f-0dd473095666", "metadata": {}, "outputs": [], "source": ["s3_access_iam_role = \"arn:aws:iam::442534439095:role/redshift-unload-copy-role\"\n", "# bucket = \"prod-dse-redshift-adhoc\"\n", "bucket = \"prod-dse-projects\"\n", "trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "aeaa52e3-a07f-42a6-83ea-98b7ffa1dde2", "metadata": {}, "outputs": [], "source": ["date = datetime.now().strftime(\"%Y_%m_%d\")\n", "# date = (datetime.now() - <PERSON><PERSON><PERSON>(days=1)).strftime(\"%Y_%m_%d\")\n", "# date = '2023_10_25'\n", "date"]}, {"cell_type": "code", "execution_count": null, "id": "732f33bc-e62c-4f84-a7ca-6809bfcc0aed", "metadata": {}, "outputs": [], "source": ["from tqdm import tqdm"]}, {"cell_type": "code", "execution_count": null, "id": "0b5983e2-13ef-4da5-9412-360c880e4d5f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1166515e-5d7c-4176-8369-efbdd68092dc", "metadata": {}, "outputs": [], "source": ["!pip install -q pqdm"]}, {"cell_type": "code", "execution_count": null, "id": "25d4d211-320c-4521-9a1a-e114613e14ae", "metadata": {}, "outputs": [], "source": ["from pqdm.processes import pqdm"]}, {"cell_type": "code", "execution_count": null, "id": "1075880a-28ea-4fdf-8d9c-d5437a7ed722", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "09f5e2a9-6b81-4554-96ae-58fcb2e195c9", "metadata": {"tags": []}, "source": ["# Helper Functions"]}, {"cell_type": "code", "execution_count": null, "id": "7b9eea17-7514-4cdf-9547-175b1f4f94e9", "metadata": {}, "outputs": [], "source": ["def get_text_embedding(query_list, model, batch_size=2048):\n", "    assert type(query_list) == list, \"1st arguement should be a list[str]\"\n", "    return model.encode(query_list, show_progress_bar=True, batch_size=512)"]}, {"cell_type": "code", "execution_count": null, "id": "7a0e228e-a8a7-46be-9fcd-0772c2e28fac", "metadata": {}, "outputs": [], "source": ["from numpy import dot\n", "from numpy.linalg import norm\n", "\n", "import sentence_transformers\n", "\n", "\n", "def get_similarity_scores(a, b):\n", "    cos_sim = dot(a, b) / (norm(a) * norm(b))\n", "    return cos_sim"]}, {"cell_type": "code", "execution_count": null, "id": "57f8d7d9-c3c9-43ed-ae97-68857049eeb7", "metadata": {}, "outputs": [], "source": ["from sentence_transformers import SentenceTransformer"]}, {"cell_type": "code", "execution_count": null, "id": "b9406cce-1c0a-49f5-9837-22f303eb557a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "8a37e857-9dc4-463b-8978-c2478fa6fe8b", "metadata": {"tags": []}, "source": ["## Pull Mxbai Onnx model from s3"]}, {"cell_type": "code", "execution_count": null, "id": "bc046ec0-219d-4c7c-91be-463236a3e5dd", "metadata": {}, "outputs": [], "source": ["import awswrangler as wr"]}, {"cell_type": "code", "execution_count": null, "id": "0dcde4d1-f697-4054-92e9-e1c5e112a501", "metadata": {}, "outputs": [], "source": ["# MODEL_DIRECTORY = \"mxbai_embed_large_v1_exp_03_2024_09_19\"\n", "MODEL_DIRECTORY = \"mxbai_embed_large_v1_exp_05_2024_12_15\"\n", "\n", "ONNX_MODEL_DIRECTORY = f\"onnx_{MODEL_DIRECTORY}\"\n", "print(ONNX_MODEL_DIRECTORY)"]}, {"cell_type": "code", "execution_count": null, "id": "74c77573-4687-4e0f-b36a-31e469b411c0", "metadata": {}, "outputs": [], "source": ["wr.s3.list_objects(\n", "    \"s3://prod-dse-projects/data-science-gpu-write/data-science/ner/qtp_model_trials/\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5f3da8e3-a7ff-44f7-8049-4c9ef5daafce", "metadata": {}, "outputs": [], "source": ["MODEL_S3_PATH = f\"s3://prod-dse-projects/data-science-gpu-write/data-science/ner/qtp_model_trials/{ONNX_MODEL_DIRECTORY}.zip\"\n", "MODEL_S3_PATH"]}, {"cell_type": "code", "execution_count": null, "id": "16bc12c2-6b6d-4a07-87ca-e4459041e4c5", "metadata": {}, "outputs": [], "source": ["wr.s3.download(MODEL_S3_PATH, f\"/tmp/{ONNX_MODEL_DIRECTORY}.zip\")"]}, {"cell_type": "code", "execution_count": null, "id": "1cd303da-228b-41a8-ac8d-a30f6acfebc7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "75cec97c-f757-47e7-9dd4-727df49f1fb0", "metadata": {"tags": []}, "source": ["## unzip model file"]}, {"cell_type": "code", "execution_count": null, "id": "8a1c8ef1-9034-4aa8-adef-6d3095689718", "metadata": {}, "outputs": [], "source": ["import shutil\n", "\n", "shutil.unpack_archive(f\"/tmp/{ONNX_MODEL_DIRECTORY}.zip\", f\"/tmp/{ONNX_MODEL_DIRECTORY}\", \"zip\")"]}, {"cell_type": "code", "execution_count": null, "id": "cbda9037-fda3-4616-9434-1a95fd2d1d33", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "e3e00cb0-d21f-4168-976e-8a1c043b4fb6", "metadata": {"tags": []}, "source": ["## Load model file"]}, {"cell_type": "code", "execution_count": null, "id": "939e9df5-f17e-4d17-a86f-143fcaa7e1e7", "metadata": {}, "outputs": [], "source": ["optim_model = SentenceTransformerOptim(f\"/tmp/{ONNX_MODEL_DIRECTORY}\")"]}, {"cell_type": "code", "execution_count": null, "id": "4af7d8ed-f775-4325-b18c-49bd9762c618", "metadata": {}, "outputs": [], "source": ["optim_model"]}, {"cell_type": "markdown", "id": "d4b113a2-bd51-4188-b89d-5fdcc254cf86", "metadata": {"tags": []}, "source": ["# Load Product Data"]}, {"cell_type": "code", "execution_count": null, "id": "9ae89662-7d72-4629-b7f9-29b0dce14fbb", "metadata": {}, "outputs": [], "source": ["import utils_pull_product_data"]}, {"cell_type": "code", "execution_count": null, "id": "96da39ec-7b25-493b-9539-cbbf681f16f8", "metadata": {}, "outputs": [], "source": ["(\n", "    product_df,\n", "    brand_id_mapping,\n", "    keyterm_id_mapping,\n", "    attribute_id_mapping,\n", ") = utils_pull_product_data.get_data(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "29d279f0-e5c9-461a-a5c0-ab5a2fef03f4", "metadata": {}, "outputs": [], "source": ["product_df[\"keyterm\"] = (\n", "    product_df[\"keyterm\"].fillna(\"\").apply(lambda x: list(x) if not isinstance(x, str) else [])\n", ")\n", "product_df[\"attribute\"] = (\n", "    product_df[\"attribute\"].fillna(\"\").apply(lambda x: list(x) if not isinstance(x, str) else [])\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f4a033eb-9451-4f4b-8099-613fec00d57a", "metadata": {}, "outputs": [], "source": ["product_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "bb234ac0-139f-467b-9fcb-879e50704ad0", "metadata": {}, "outputs": [], "source": ["product_df.sample(5)"]}, {"cell_type": "code", "execution_count": null, "id": "6ea78e7a-90af-438d-a144-fa6a21e6f377", "metadata": {}, "outputs": [], "source": ["product_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "fc8fd9bf-5990-4a61-9ac0-d8fb3792c43b", "metadata": {}, "outputs": [], "source": ["product_df[~product_df.l0_category.isin([\"books\", \"magazines\", \"trial new tree\"])].shape"]}, {"cell_type": "code", "execution_count": null, "id": "81efaa16-454c-40b0-a2a7-e52e1005af70", "metadata": {}, "outputs": [], "source": ["nonbook_product_df = product_df[\n", "    ~product_df.l0_category.isin([\"books\", \"magazines\", \"trial new tree\", \"bistro\"])\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "0ec032b4-37c6-4fcc-bb87-228f6582cedf", "metadata": {}, "outputs": [], "source": ["nonbook_product_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "8daf6951-a66f-4671-b59a-a42fd8b4f8af", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "62b49599-a759-48e7-97df-5859f9fe0000", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c4cd9350-2b2e-432b-a41b-9313ca40f6f6", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select distinct product_id,l0_category actual_l0, l1_category actual_l1, product_name actual_pname\n", "from dwh.dim_product\n", "where is_current and is_product_enabled\n", "\"\"\"\n", "\n", "temp = pd.read_sql(query, trino_con)\n", "print(temp.shape, \"\\n\", temp.product_id.nunique())"]}, {"cell_type": "code", "execution_count": null, "id": "e7c77ffd-6a42-44f0-98cb-8beea309e787", "metadata": {}, "outputs": [], "source": ["product_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "faee5992-cb40-4428-a251-45567b7a05a2", "metadata": {}, "outputs": [], "source": ["product_df = product_df.merge(temp, on=\"product_id\", how=\"left\")\n", "product_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "b3148c1e-2874-4dd0-bbd3-a8bcfa70d7c7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "dd16565a-3152-4972-b278-8af54a2f6cf1", "metadata": {}, "outputs": [], "source": ["if os.path.exists(\"Gift Assortment Recos - BlinkGPT - Blacklisted categories for gifting.csv\"):\n", "    blacklisted_categories = pd.read_csv(\n", "        \"Gift Assortment Recos - BlinkGPT - Blacklisted categories for gifting.csv\"\n", "    )\n", "else:\n", "    blacklisted_categories = pb.from_sheets(\n", "        \"1LsUtbXqBe1Dy1i_uWgOHLVygv01N3DU5-wucTCf1vEc\", \"Blacklisted categories for gifting\"\n", "    )\n", "\n", "blacklisted_categories.columns = [\"actual_l0\", \"actual_l1\"]\n", "blacklisted_categories[\"blacklisted_pid\"] = True\n", "blacklisted_categories.shape"]}, {"cell_type": "code", "execution_count": null, "id": "4b1bcb18-defb-46ff-b7ad-baccad22b1cf", "metadata": {}, "outputs": [], "source": ["blacklisted_categories.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "708fbe27-a882-4cb7-a50d-71c6a1758faf", "metadata": {}, "outputs": [], "source": ["product_df = product_df.merge(blacklisted_categories, on=[\"actual_l0\", \"actual_l1\"], how=\"left\")\n", "product_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "5901e547-2278-46fb-8b36-0248cc025390", "metadata": {}, "outputs": [], "source": ["product_df[\"blacklisted_pid\"] = product_df[\"blacklisted_pid\"].fillna(False)"]}, {"cell_type": "code", "execution_count": null, "id": "90ed6d10-8dc3-4208-b81f-e5fc0f3d5882", "metadata": {}, "outputs": [], "source": ["product_df[\"blacklisted_pid\"].value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "4005da7f-c08b-410a-9ca8-a1cd20f7c5ea", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1865ac0a-6074-45eb-8ce8-52fc18558214", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "3889c5ae-d183-429a-a81f-1691cef3ead3", "metadata": {}, "source": ["# Pull all product metadata"]}, {"cell_type": "code", "execution_count": null, "id": "e0020715-3c23-4b39-94c5-d1e636a2ac14", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"with\n", "max_dt as (\n", "    select metadata_id,metadata_type,max(snapshot_ist) max_snapshot_ist \n", "    from search_etls.meta_entity_product_relationship \n", "    where snapshot_ist >= current_date-interval '45' day\n", "    group by 1,2),\n", "        \n", "meta_base as (\n", "    select \n", "        p.metadata_id, \n", "        p.metadata_name,\n", "        p.metadata_type,\n", "        cast(p.is_pid_active as varchar) mapped_pid\n", "    from \n", "        search_etls.meta_entity_product_relationship p\n", "        join max_dt m on m.metadata_id = p.metadata_id and m.max_snapshot_ist = p.snapshot_ist and m.metadata_type = p.metadata_type\n", "    where\n", "        p.snapshot_ist is not null\n", "    group by 1,2,3,4)\n", "    \n", "select distinct * from meta_base\n", "where metadata_name like '%%gift%%' and metadata_type not in ('GENERATED_TAG','AUTHOR','KEYWORD')\n", "-- where metadata_type in ('KEYTERM','BRAND','ATTRIBUTE')\n", "\"\"\"\n", "\n", "base_entities_raw_df = pd.read_sql(query, trino_con)\n", "print(base_entities_raw_df.shape, \"\\n\", base_entities_raw_df.metadata_type.unique())"]}, {"cell_type": "code", "execution_count": null, "id": "7e607926-1c9d-4f90-a297-fe9154e7a381", "metadata": {}, "outputs": [], "source": ["# query = f\"\"\"\n", "# with\n", "# max_dt as (\n", "#     select metadata_id,metadata_type,max(snapshot_ist) max_snapshot_ist\n", "#     from search_etls.meta_entity_product_relationship\n", "#     where snapshot_ist >= current_date-interval '45' day\n", "#     group by 1,2),\n", "\n", "# meta_base as (\n", "#     select\n", "#         p.metadata_id,\n", "#         p.metadata_name,\n", "#         p.metadata_type,\n", "#         cast(p.tagged_product_id as varchar) tagged_product_id\n", "#     from\n", "#         search_etls.meta_entity_product_relationship p\n", "#         join max_dt m on m.metadata_id = p.metadata_id and m.max_snapshot_ist = p.snapshot_ist and m.metadata_type = p.metadata_type\n", "#     where\n", "#         p.snapshot_ist >= current_date-interval '45' day\n", "#     group by 1,2,3,4),\n", "\n", "# metadata as (\n", "#     select metadata_id, metadata_name,metadata_type,count(distinct case when cast(tagged_product_id as bigint) >= 1 then tagged_product_id end) tagged_pids\n", "#     from meta_base\n", "#     group by 1,2,3\n", "# )\n", "\n", "# -- select * from metadata\n", "\n", "# select\n", "#     metadata_id,\n", "#     metadata_name,\n", "#     metadata_type,\n", "#     tagged_pids mapped_pid,\n", "#     primary_entity_id,\n", "#     secondary_entity_id,\n", "#     primary_entity_type,\n", "#     secondary_entity_type\n", "# from\n", "#     metadata r\n", "#     left join product_knowledge_metadata.metadata_relationship mr on mr.id = r.metadata_id\n", "#         and mr.is_enabled and mr.lake_active_record and mr.relationship not in ('IS_SIMILAR','IS_PARENT')\n", "#         and mr.primary_entity_type in ('ATTRIBUTE', 'KEYTERM') and mr.secondary_entity_type in ('ATTRIBUTE', 'KEYTERM')\n", "#     where metadata_name like '%%gift%%' and metadata_type not in ('GENERATED_TAG','AUTHOR')\n", "# group by 1,2,3,4,5,6,7,8\n", "# order by 1\n", "# \"\"\"\n", "\n", "# base_entities_df = pd.read_sql(query,trino_con)\n", "# print(base_entities_df.shape,'\\n',base_entities_df.metadata_type.unique())"]}, {"cell_type": "code", "execution_count": null, "id": "6ab2882c-b5fd-4308-8ff8-26cf6f4e1823", "metadata": {}, "outputs": [], "source": ["base_entities_raw_df.sample(5)"]}, {"cell_type": "code", "execution_count": null, "id": "58030388-cf6b-4907-be55-0f0d0b15efa5", "metadata": {}, "outputs": [], "source": ["base_entities_raw_df.metadata_type.unique()"]}, {"cell_type": "code", "execution_count": null, "id": "3f74cb8c-f8df-4b88-91e7-82fe249e8587", "metadata": {}, "outputs": [], "source": ["from collections import Counter\n", "\n", "Counter(base_entities_raw_df.metadata_type.tolist())"]}, {"cell_type": "code", "execution_count": null, "id": "71d4f1d8-6d57-4aeb-9e3f-104ad74f205d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5f8777fd-78ed-4c3c-88be-3ca6dbe6bc96", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a33ce6e6-7359-4974-821c-36d1570e9ec9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "416f1639-7796-4cc2-af1c-a8e453ea41f3", "metadata": {}, "outputs": [], "source": ["print(base_entities_raw_df.shape)\n", "base_entities_raw_df.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "33c83263-b907-4565-bca3-8ab705c5e101", "metadata": {}, "outputs": [], "source": ["def finalize_metatype(x):\n", "    if \"COMPOSITE_KEYWORD\" in x:\n", "        return \"COMPOSITE_KEYWORD\"\n", "    elif \"KEYTERM\" in x:\n", "        return \"KEYTERM\"\n", "    else:\n", "        return x[0]\n", "\n", "\n", "temp = base_entities_raw_df.groupby([\"metadata_name\"], as_index=False).agg(\n", "    {\"metadata_type\": lambda x: list(set(x))}, axis=1\n", ")\n", "temp[\"metadata_type_count\"] = temp[\"metadata_type\"].apply(len)\n", "temp = temp.sort_values(by=[\"metadata_type_count\"], ascending=[False])\n", "\n", "temp[\"metadata_type\"] = temp.apply(lambda x: finalize_metatype(x[\"metadata_type\"]), axis=1)\n", "\n", "temp = temp.drop([\"metadata_type_count\"], axis=1)\n", "\n", "temp.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "4e0e5ef2-cab6-4d3c-b139-8808531addb9", "metadata": {}, "outputs": [], "source": ["base_entities_raw_df.merge(temp, on=[\"metadata_name\", \"metadata_type\"], how=\"inner\").shape"]}, {"cell_type": "code", "execution_count": null, "id": "3f43deb5-3549-4080-9c17-c25703bdad67", "metadata": {}, "outputs": [], "source": ["base_entities_df = base_entities_raw_df.merge(\n", "    temp, on=[\"metadata_name\", \"metadata_type\"], how=\"inner\"\n", ")\n", "base_entities_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "5c2628db-5710-4db1-ac20-847db4747621", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "001f1b05-4ff7-4309-9e77-654c8f8e1a19", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "172c2c3e-5c23-441b-af4e-1de166b5c181", "metadata": {}, "outputs": [], "source": ["all_composite_keyword_ids = sorted(\n", "    base_entities_df[base_entities_df.metadata_type == \"COMPOSITE_KEYWORD\"]\n", "    .metadata_id.unique()\n", "    .tolist()\n", ")\n", "print(len(all_composite_keyword_ids))\n", "all_composite_keyword_ids_string = \", \".join([str(x) for x in all_composite_keyword_ids])"]}, {"cell_type": "code", "execution_count": null, "id": "d8a02713-0d36-4da3-b598-58e027896825", "metadata": {}, "outputs": [], "source": ["base_entities_df[base_entities_df.metadata_name == \"rakhi gift for sister\"][\n", "    [\"metadata_id\", \"metadata_name\", \"metadata_type\"]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "f5c69b4a-c36b-41af-b1c3-3f34e58a46a2", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "SELECT k.id AS keyword_id,\n", "       lower(k.name) AS keyword_name,\n", "       kc.is_enabled,\n", "       kc.entity_id AS entity_id,\n", "       kc.entity_type AS entity_type,\n", "       CASE\n", "           WHEN kc.entity_type = 'BRAND' THEN lower(gb.display_name)\n", "           ELSE coalesce(m.name, '')\n", "       END AS entity_name\n", "FROM product_knowledge_metadata.keyword k\n", "INNER JOIN product_knowledge_metadata.keyword_composition kc ON k.id = kc.keyword_id\n", "LEFT JOIN product_knowledge_metadata.metadata m ON kc.entity_id = m.id\n", "AND cast(kc.entity_type AS varchar) = cast(m.metadata_type AS varchar)\n", "LEFT JOIN cms.gr_brand gb ON gb.id = kc.entity_id\n", "AND cast(kc.entity_type AS varchar) = 'BRAND'\n", "WHERE\n", "    (k.kind is null or k.kind!='FOOD') and\n", "    (kc.is_enabled) and\n", "   -- k.kind<>'METADATA' and\n", "  ( (kc.entity_type NOT IN ('BRAND')\n", "         AND m.is_enabled)\n", "       OR (kc.entity_type IN ('BRAND')\n", "           AND gb.enabled_flag) )\n", "           and \n", "           (entity_type != 'LEVEL1_CATEGORY')\n", "order by k.id\n", "\"\"\"\n", "\n", "all_keyword_breakup_df_raw = pd.read_sql(query, trino_con)\n", "print(all_keyword_breakup_df_raw.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "3f26f6e0-fdfb-4018-b857-0582736db28d", "metadata": {}, "outputs": [], "source": ["all_keyword_breakup_df_raw[all_keyword_breakup_df_raw.keyword_id == 64496]"]}, {"cell_type": "code", "execution_count": null, "id": "c78d98fa-b0dd-4730-97ce-896df7071424", "metadata": {"tags": []}, "outputs": [], "source": ["all_keyword_breakup_df_raw.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "b74624f4-87d6-461e-91bd-5b823f2b1ad6", "metadata": {}, "outputs": [], "source": ["all_keyword_breakup_df_raw2 = (\n", "    all_keyword_breakup_df_raw.sort_values(\n", "        [\"keyword_name\", \"is_enabled\", \"entity_type\"], ascending=[False, False, False]\n", "    )\n", "    .groupby([\"keyword_name\", \"entity_type\"], as_index=False)\n", "    .head(1)\n", ")\n", "all_keyword_breakup_df_raw2.shape"]}, {"cell_type": "code", "execution_count": null, "id": "5caec6b2-963d-4061-9dd3-55666546a681", "metadata": {}, "outputs": [], "source": ["all_keyword_breakup_df_raw2.sample(5)"]}, {"cell_type": "code", "execution_count": null, "id": "bd6b501d-b8ba-4dd9-8f3d-c2587c10f00b", "metadata": {}, "outputs": [], "source": ["all_keyword_breakup_df_raw2[all_keyword_breakup_df_raw2.keyword_name == \"birthday gift for fiance\"]"]}, {"cell_type": "code", "execution_count": null, "id": "8503380d-55a4-45ad-9339-1fab7f7362d0", "metadata": {}, "outputs": [], "source": ["all_keyword_breakup_df_raw2[\n", "    all_keyword_breakup_df_raw2.keyword_name == \"farewell gift for colleagues\"\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "4a446aa1-10ef-47b7-9f9e-2077383321f8", "metadata": {}, "outputs": [], "source": ["all_keyword_breakup_df_raw2[all_keyword_breakup_df_raw2.keyword_name == \"strauss gift\"]"]}, {"cell_type": "code", "execution_count": null, "id": "c673a4fc-39d4-4ee6-9d73-8ed106b130aa", "metadata": {}, "outputs": [], "source": ["all_keyword_breakup_df_raw2.entity_type.unique()"]}, {"cell_type": "code", "execution_count": null, "id": "3a046cf2-7d0d-41cc-806e-20b841addb91", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4aa6d7e4-8355-4c64-88dc-28c6f32cc230", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "44a4ae12-0785-4b0f-9588-4e173c377d76", "metadata": {}, "outputs": [], "source": ["brand_gift_keywords = (\n", "    all_keyword_breakup_df_raw2[all_keyword_breakup_df_raw2.keyword_name.str.contains(\"gift\")]\n", "    .groupby([\"keyword_id\", \"keyword_name\"], as_index=False)\n", "    .agg({\"entity_type\": lambda x: sorted(set(x))}, axis=1)\n", ")\n", "brand_gift_keywords = brand_gift_keywords[\n", "    brand_gift_keywords.entity_type.apply(lambda x: x == sorted([\"BRAND\", \"USECASE\"]))\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "ee5112c4-3c50-4d41-98f7-c6c1e2899652", "metadata": {}, "outputs": [], "source": ["brand_gift_keywords.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "2fbe9cd4-318b-4acc-8d9f-b753d5465b0e", "metadata": {}, "outputs": [], "source": ["brand_gift_keywords.shape"]}, {"cell_type": "code", "execution_count": null, "id": "4f3d7a5c-b1e6-4962-8838-3cdd9277a06e", "metadata": {}, "outputs": [], "source": ["brand_gift_keywords[brand_gift_keywords.keyword_name == \"strauss gift\"]"]}, {"cell_type": "code", "execution_count": null, "id": "6220cc8d-c2c9-43f3-9860-ecd319b822b0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "95abde30-9625-4a52-8364-f53106331af9", "metadata": {}, "outputs": [], "source": ["remove_brand_gift_keywords = brand_gift_keywords.keyword_name.unique().tolist()\n", "print(len(remove_brand_gift_keywords))"]}, {"cell_type": "code", "execution_count": null, "id": "6ff880c1-4d27-4594-88a3-7b76e5cb6f40", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6dce57bf-658e-4fa2-a8eb-3c217878219a", "metadata": {}, "outputs": [], "source": ["print(all_keyword_breakup_df_raw2.shape)\n", "all_keyword_breakup_df_raw2 = all_keyword_breakup_df_raw2[\n", "    ~all_keyword_breakup_df_raw2.keyword_name.isin(remove_brand_gift_keywords)\n", "]\n", "print(all_keyword_breakup_df_raw2.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "cdf1cb08-9f92-4405-bc0c-53976a970ea0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1b31263e-3531-4d16-8a34-cc63127c751b", "metadata": {}, "outputs": [], "source": ["base_entities_df.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "301af830-ce3a-4923-b0c6-3511e341aa53", "metadata": {}, "outputs": [], "source": ["print(base_entities_df.shape)\n", "print(base_entities_df[~base_entities_df.metadata_name.isin(remove_brand_gift_keywords)].shape)"]}, {"cell_type": "code", "execution_count": null, "id": "c0cf746a-defb-47c8-8dfb-aef15ceb73c2", "metadata": {}, "outputs": [], "source": ["base_entities_df = base_entities_df[\n", "    ~base_entities_df.metadata_name.isin(remove_brand_gift_keywords)\n", "]\n", "print(base_entities_df.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "ed945b4b-dc58-4aad-ac7b-09d7a4da6267", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d6ce3d43-8120-43ad-82cb-25c43150a6e2", "metadata": {}, "outputs": [], "source": ["all_keyword_breakup_df_raw2[all_keyword_breakup_df_raw2.keyword_name == \"strauss gift\"]"]}, {"cell_type": "code", "execution_count": null, "id": "b3c4ca24-0c72-42a1-ac66-8f2c74c2527c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0c774b07-7aea-42d7-bc1e-fcc8e882ca9c", "metadata": {}, "outputs": [], "source": ["all_keyword_breakup_df = all_keyword_breakup_df_raw2.pivot_table(\n", "    index=[\"keyword_name\", \"keyword_id\"],\n", "    columns=\"entity_type\",\n", "    values=\"entity_name\",\n", "    aggfunc=lambda x: list(x)[0],\n", ").reset_index()\n", "print(all_keyword_breakup_df.shape)\n", "all_keyword_breakup_df.sample(5)"]}, {"cell_type": "code", "execution_count": null, "id": "ac4b12aa-18ba-4636-9e3f-d2a4cedd9cc6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7b310f44-ec28-4a98-ac19-59c61f13d2fa", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e26c4e7a-de50-4167-ab1a-8638e6e4b705", "metadata": {}, "outputs": [], "source": ["all_keyword_breakup_df.keyword_name.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "633e6b93-ef4a-4583-a531-6948b012ed6c", "metadata": {}, "outputs": [], "source": ["all_keyword_breakup_df.keyword_name.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "e1194c05-4adf-4bb1-8fae-f348f5a7e844", "metadata": {}, "outputs": [], "source": ["multiple_entities = all_keyword_breakup_df.keyword_name.value_counts()\n", "multiple_entities = multiple_entities[: sum(multiple_entities > 1)]\n", "multiple_entities"]}, {"cell_type": "code", "execution_count": null, "id": "c37374d1-686e-4944-b026-8c83d0a21407", "metadata": {}, "outputs": [], "source": ["assert len(multiple_entities) == 0"]}, {"cell_type": "code", "execution_count": null, "id": "71b954dd-7292-44d7-b34f-5ea86dd8b9fc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3f37bcb0-3560-430a-a028-b5437dc233a9", "metadata": {}, "outputs": [], "source": ["base_entities_df.sample(5)"]}, {"cell_type": "code", "execution_count": null, "id": "191a2eac-0209-4a19-8df3-a8d8d89d56d0", "metadata": {}, "outputs": [], "source": ["base_entities_df.metadata_type.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "9a8c3f0a-e8dc-478f-a9d0-a530575bdbdb", "metadata": {}, "outputs": [], "source": ["base_entities_df[\"mapped_pid\"] = base_entities_df[\"mapped_pid\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "a30705ae-9f11-4418-8f88-f730fab690cd", "metadata": {}, "outputs": [], "source": ["base_entities_df.sample(5)"]}, {"cell_type": "code", "execution_count": null, "id": "75105a3d-c42c-4cde-aeff-645ace0a9fc6", "metadata": {}, "outputs": [], "source": ["base_entities_df[[\"metadata_id\", \"metadata_name\"]].drop_duplicates().shape"]}, {"cell_type": "code", "execution_count": null, "id": "a6dbc402-896c-4789-94b0-48e46470f5cb", "metadata": {}, "outputs": [], "source": ["metadata_name_type_id_df = base_entities_df.groupby(\n", "    [\"metadata_name\", \"metadata_type\"], as_index=False\n", ").agg({\"metadata_id\": lambda x: list(set(x))}, axis=1)\n", "metadata_name_type_id_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "77ea670b-aec2-40a6-846b-a51422c313b2", "metadata": {}, "outputs": [], "source": ["# metadata_name_mid_dict = metadata_name_mid_dict.to_dict()[\"metadata_id\"]\n", "# len(metadata_name_mid_dict)"]}, {"cell_type": "code", "execution_count": null, "id": "28876994-b1e9-4bd4-afe5-6febc8608aad", "metadata": {}, "outputs": [], "source": ["# metadata_name_mid_dict[\"1 to 2 pieces home gifting\"]"]}, {"cell_type": "code", "execution_count": null, "id": "39f5546d-f81e-4d93-9dc7-5c8751370d34", "metadata": {}, "outputs": [], "source": ["# pd.Series(metadata_name_mid_dict.values()).apply(len).value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "01f8f703-e006-4c24-84b8-56d96051103f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "68816880-e663-4dce-a163-775b54cbee5b", "metadata": {}, "outputs": [], "source": ["base_entities_df[[\"metadata_id\"]].drop_duplicates().shape"]}, {"cell_type": "code", "execution_count": null, "id": "212b5e1e-1685-4ed7-8e7e-66f895134f08", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7a9477be-4f99-40e4-a779-bd020ed589e7", "metadata": {}, "outputs": [], "source": ["base_entities_df.mapped_pid.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "71ab16af-b061-46b7-809a-a540c1064f27", "metadata": {}, "outputs": [], "source": ["use_metadata_types = [\n", "    \"USECASE\",\n", "    \"KEYTERM\",\n", "    \"COMPOSITE_KEYWORD\",\n", "    \"ATTRIBUTE\",\n", "    # 'KEYWORD'\n", "]\n", "pid_base_entities_df = (\n", "    base_entities_df[base_entities_df.metadata_type.isin(use_metadata_types)]\n", "    .groupby([\"mapped_pid\", \"metadata_type\"], as_index=False)\n", "    .agg({\"metadata_name\": lambda x: list(set(x))}, axis=1)\n", ")\n", "pid_base_entities_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c7535b55-51ad-4055-b4aa-68d0c8f64c0f", "metadata": {}, "outputs": [], "source": ["pid_base_entities_df.sample(5)"]}, {"cell_type": "code", "execution_count": null, "id": "32908690-9e05-49cc-be7e-492bd8ffdc0a", "metadata": {}, "outputs": [], "source": ["product_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "70b594c3-ee56-4817-9cbe-d014e1ad5be5", "metadata": {}, "outputs": [], "source": ["product_df2 = product_df.merge(\n", "    pid_base_entities_df, how=\"left\", left_on=\"product_id\", right_on=\"mapped_pid\"\n", ").drop([\"mapped_pid\"], axis=1)\n", "product_df2.shape"]}, {"cell_type": "code", "execution_count": null, "id": "6f4caa99-ed8a-417e-b28b-2e5efe4e21a1", "metadata": {}, "outputs": [], "source": ["product_df2_nonbook = product_df2[product_df2.product_type != \"book\"].drop(\n", "    [\"l0_category_id\", \"l1_category_id\", \"l2_category_id\", \"product_type_id\", \"brand_id\"], axis=1\n", ")\n", "product_df2_nonbook.shape"]}, {"cell_type": "code", "execution_count": null, "id": "b402c469-a15c-4a87-a8d6-ac4eb46b21c8", "metadata": {}, "outputs": [], "source": ["product_df2_nonbook = product_df2_nonbook[product_df2_nonbook.l0_category != \"bistro\"]\n", "product_df2_nonbook.shape"]}, {"cell_type": "code", "execution_count": null, "id": "84a6be83-0fd5-4db9-bcb2-e77ba059a5a9", "metadata": {}, "outputs": [], "source": ["product_df2_nonbook_whitelisted = product_df2_nonbook[(~product_df2.blacklisted_pid)]\n", "product_df2_nonbook_whitelisted.shape"]}, {"cell_type": "code", "execution_count": null, "id": "809499d9-83ba-4098-8de0-fe123984d27c", "metadata": {}, "outputs": [], "source": ["product_df2_nonbook_whitelisted.metadata_name.isna().value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "79d5c0f6-5571-4783-8c68-cbe26ee77538", "metadata": {"tags": []}, "outputs": [], "source": ["product_df2_nonbook_whitelisted.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e822731b-8979-47c2-a24b-608ecbe3491d", "metadata": {}, "outputs": [], "source": ["exisiting_gift_pdt_df = product_df2_nonbook_whitelisted[\n", "    (product_df2_nonbook_whitelisted.metadata_name.notna())\n", "]\n", "print(exisiting_gift_pdt_df.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "a942c445-8183-4d9e-a5d7-56f2205decb7", "metadata": {}, "outputs": [], "source": ["exisiting_gift_pdt_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "898c0364-9e77-4c54-b2e4-492fdb4f16e9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2cf8ce4c-0c21-4af8-b5ec-40a6be96d3eb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ec144a0f-ca0a-454a-a905-450cb1bdb8f4", "metadata": {}, "outputs": [], "source": ["prefix = f\"data-science/ner/{ONNX_MODEL_DIRECTORY}_embeddings_dict_old/\"\n", "\n", "try:\n", "    pb.from_s3(bucket, prefix, f\"/tmp/{ONNX_MODEL_DIRECTORY}_embeddings_dict.pkl\")\n", "\n", "    with open(f\"/tmp/{ONNX_MODEL_DIRECTORY}_embeddings_dict.pkl\", \"rb\") as f:\n", "        all_embeddings_dict = pickle.load(f)\n", "\n", "    print(len(all_embeddings_dict))\n", "except:\n", "    all_embeddings_dict = {}\n", "    print(len(all_embeddings_dict))"]}, {"cell_type": "code", "execution_count": null, "id": "f6377c18-b30e-4d18-b84f-cfc3c55d3df0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d79536c3-a9fe-4ca1-a78d-53598ce64e10", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "24305301-bd41-4f0f-9682-830868e0e8d1", "metadata": {}, "outputs": [], "source": ["# import faiss\n", "from sentence_transformers import SentenceTransformer\n", "import numpy as np\n", "\n", "\n", "def create_hnsw_index(embeddings):\n", "\n", "    index = ANNFactory.create({\"backend\": \"hnsw\", \"dimensions\": embeddings.shape[1]})\n", "    index.index(embeddings)\n", "\n", "    return index"]}, {"cell_type": "code", "execution_count": null, "id": "c1deb110-2965-40ef-8814-d7a531d51604", "metadata": {}, "outputs": [], "source": ["def encode_query(query):\n", "    return optim_model.encode(query, normalize_embeddings=True)"]}, {"cell_type": "code", "execution_count": null, "id": "539a1bd9-1959-4fe9-b369-54627af78f09", "metadata": {}, "outputs": [], "source": ["base_entities_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "85680d24-9f67-43b7-bde1-da133c854a8c", "metadata": {}, "outputs": [], "source": ["base_entities_df.sample(5)"]}, {"cell_type": "code", "execution_count": null, "id": "aeec2846-6684-48ee-a286-03b66159422e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "deebc7b6-a347-4a67-bbc6-0c3805b0b715", "metadata": {}, "outputs": [], "source": ["print(len(all_embeddings_dict))"]}, {"cell_type": "code", "execution_count": null, "id": "721a2878-575c-4b92-986e-69909ef0cede", "metadata": {}, "outputs": [], "source": ["unique_entities = list(\n", "    set(\n", "        base_entities_df.metadata_name.unique().tolist()\n", "        + product_df.product_name.unique().tolist()\n", "        + product_df.l0_category.unique().tolist()\n", "    )\n", ")\n", "print(len(unique_entities))"]}, {"cell_type": "code", "execution_count": null, "id": "e60c7beb-22af-4069-944c-d699c69c12dc", "metadata": {}, "outputs": [], "source": ["entities_to_encode = [x for x in unique_entities if x not in all_embeddings_dict]\n", "print(len(entities_to_encode))\n", "\n", "if len(entities_to_encode) > 0:\n", "\n", "    entities_embeddings = np.array(pqdm(entities_to_encode, encode_query, n_jobs=4))\n", "\n", "    all_embeddings_dict.update(dict(zip(entities_to_encode, entities_embeddings)))\n", "\n", "    # with open('mxbai_exp05_embeddings_dict.pkl','wb') as f:\n", "    #     pickle.dump(all_embeddings_dict,f)\n", "\n", "    import pickle\n", "\n", "    with open(f\"/tmp/{ONNX_MODEL_DIRECTORY}_embeddings_dict.pkl\", \"wb\") as f:\n", "        pickle.dump(all_embeddings_dict, f)\n", "\n", "    prefix = f\"data-science/ner/{ONNX_MODEL_DIRECTORY}_embeddings_dict_old/\"\n", "    pb.to_s3(f\"/tmp/{ONNX_MODEL_DIRECTORY}_embeddings_dict.pkl\", bucket, prefix)"]}, {"cell_type": "code", "execution_count": null, "id": "b2a55e27-cae8-4a5c-9c0c-2ebcacc446ff", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "179d632b-c0a3-4915-9ff9-2a33f0801206", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e95ed1b1-1ccb-4b1e-8402-42ce3f03d131", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ef57e993-2128-47e4-b02a-e54781c0f0a5", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select combo_product_id product_id\n", "from search_etls.search_combo_products\n", "group by 1\n", "\"\"\"\n", "\n", "combo_offer_pids = pd.read_sql(query, trino_con)\n", "print(combo_offer_pids.shape, \"\\n\", combo_offer_pids.product_id.nunique())"]}, {"cell_type": "code", "execution_count": null, "id": "dc3407e5-553f-4889-9f3f-3b0310d043c1", "metadata": {}, "outputs": [], "source": ["combo_offer_pids = set(combo_offer_pids.product_id.tolist())\n", "print(len(combo_offer_pids))"]}, {"cell_type": "code", "execution_count": null, "id": "0336563c-ac2b-4b71-af6f-65ebf7e4d701", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7394ddf6-0fcd-4bc3-ac97-038f3c0f7d65", "metadata": {}, "outputs": [], "source": ["# with open(f\"{cwd}/queries/new_item_pricing.sql\", \"r\") as f:\n", "#     query = f.read()\n", "#     price_data = pd.read_sql_query(query, con=trino_con)\n", "# f.close()"]}, {"cell_type": "code", "execution_count": null, "id": "fbc50fb6-b9f3-43c3-9d06-72d75ce08491", "metadata": {}, "outputs": [], "source": ["price_data = utls.run_sql_query(\n", "    f\"{cwd}/queries/new_item_pricing.sql\", con=pb.get_connection(\"[Warehouse] Trino\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "710eada1-b015-4e59-bc65-dfbcce631f26", "metadata": {}, "outputs": [], "source": ["price_data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "3fa05091-6fe2-4442-8638-d68300090c9f", "metadata": {}, "outputs": [], "source": ["price_data.sample(5)"]}, {"cell_type": "code", "execution_count": null, "id": "a8d04a37-b1c1-4698-8817-f58329d90be7", "metadata": {}, "outputs": [], "source": ["price_data.item_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "2a3a1b80-ab66-4ea5-8524-6fa721ef41b2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cc84ad21-bfef-46d6-82dc-cec2be3a48c6", "metadata": {"tags": []}, "outputs": [], "source": ["query = \"\"\"\n", "  SELECT \n", "    DISTINCT ipr.product_id, \n", "    CASE WHEN ipr.item_id IS NULL THEN ipom_0.item_id ELSE ipr.item_id END AS item_id\n", "  FROM \n", "    lake_rpc.item_product_mapping ipr \n", "    LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipr.product_id = ipom.product_id AND ipr.item_id = ipom.item_id \n", "    LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipr.product_id = ipom_0.product_id\n", "\"\"\"\n", "\n", "itemid_pid_map_df = pd.read_sql(query, trino_con)\n", "print(itemid_pid_map_df.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "34a2b39c-f3fb-44d0-82c8-c4d2c7276ad4", "metadata": {}, "outputs": [], "source": ["itemid_pid_map_df.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "26a54325-d625-405e-9c4c-d1939aae4089", "metadata": {}, "outputs": [], "source": ["print(price_data.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "f536d313-43dd-49b4-9c29-755230d6b3d3", "metadata": {}, "outputs": [], "source": ["price_data2 = price_data.merge(itemid_pid_map_df, on=\"item_id\", how=\"inner\")\n", "print(price_data2.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "6d0a1ede-61c0-4e99-aa40-50dae92b04f4", "metadata": {}, "outputs": [], "source": ["price_data2 = (\n", "    price_data2[[\"product_id\", \"l0\", \"l1\", \"l2\", \"item_name\", \"item_asp\"]]\n", "    .sort_values(by=[\"item_asp\"], ascending=[False])\n", "    .groupby([\"product_id\"], as_index=False)\n", "    .head(1)\n", ")\n", "print(price_data2.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "97bc187a-bf58-404f-ae53-286d28c7eaa6", "metadata": {}, "outputs": [], "source": ["price_data2.sample(5)"]}, {"cell_type": "code", "execution_count": null, "id": "5eb7f803-9df3-41e7-ae4f-640d09a0326a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e903af5a-8e37-4fd4-8ea4-e464743d15e2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "95af6085-e38f-4c0b-8323-10187734e748", "metadata": {"tags": []}, "source": ["# Similar Pdts based"]}, {"cell_type": "code", "execution_count": null, "id": "598f67d6-ed39-47e6-9593-beca2a808e92", "metadata": {}, "outputs": [], "source": ["product_df2_nonbook_whitelisted.shape"]}, {"cell_type": "code", "execution_count": null, "id": "583ac452-e5d5-4fa5-8fda-2a8746828bc5", "metadata": {}, "outputs": [], "source": ["product_df2_nonbook_whitelisted.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "bf707bf9-6bb2-4b1b-9704-6c12cd086668", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "29310b8c-e218-4ad1-94cb-e8dd008f0141", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6f8ef631-aefb-4294-b11d-f76203003003", "metadata": {}, "outputs": [], "source": ["all_unique_product_names = product_df2_nonbook_whitelisted.product_name.unique()\n", "len(all_unique_product_names)"]}, {"cell_type": "code", "execution_count": null, "id": "15e9c820-7d3d-45aa-9b86-35f38d71952a", "metadata": {}, "outputs": [], "source": ["all_unique_product_names_embeddings = np.array(\n", "    [all_embeddings_dict[x] for x in all_unique_product_names]\n", ")\n", "print(all_unique_product_names_embeddings.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "08035894-8089-4476-8f37-9f50b13782bb", "metadata": {}, "outputs": [], "source": ["pname_index = create_hnsw_index(all_unique_product_names_embeddings)"]}, {"cell_type": "code", "execution_count": null, "id": "d1e52cce-0bdc-4808-b073-cf72e1873465", "metadata": {}, "outputs": [], "source": ["temp = pname_index.search(all_unique_product_names_embeddings, 750)\n", "temp = [x[:100] for x in temp]\n", "temp = [list(zip(*x)) for x in temp]\n", "\n", "indices = np.array([np.array(x[0]) for x in temp])\n", "cosine_similarities = np.array([np.array(x[1]) for x in temp])\n", "filter_cosine_similarities = cosine_similarities >= 0.90\n", "\n", "indices_filtered = [indices[i][x] for i, x in enumerate(filter_cosine_similarities)]\n", "cosine_similarities_filtered = [\n", "    cosine_similarities[i][x] for i, x in enumerate(filter_cosine_similarities)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "e595f7a0-721f-4dea-80c8-ecd6017dfe05", "metadata": {}, "outputs": [], "source": ["query_top_pnames = [all_unique_product_names[i] for i in indices_filtered]"]}, {"cell_type": "code", "execution_count": null, "id": "b689669a-4e04-4cb7-b353-8680e65cce66", "metadata": {}, "outputs": [], "source": ["pnames_sim_pname_dict = dict(zip(all_unique_product_names, query_top_pnames))"]}, {"cell_type": "code", "execution_count": null, "id": "b948a693-7957-4a5c-b57c-7ea5aed032b2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9df25de9-4598-4d2b-8fa1-b2d0d53859ed", "metadata": {}, "outputs": [], "source": ["product_df2_nonbook_whitelisted.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "f736a0a8-4d9a-4ff1-baee-c932bbe1317c", "metadata": {}, "outputs": [], "source": ["temp = (\n", "    product_df2_nonbook_whitelisted[product_df2_nonbook_whitelisted.metadata_name.notna()]\n", "    .groupby([\"product_name\"])\n", "    .agg({\"metadata_name\": lambda x: list(set(itertools.chain.from_iterable(x)))}, axis=1)\n", ")\n", "pname_gift_entities_dict = dict(zip(temp.index, temp.metadata_name))"]}, {"cell_type": "code", "execution_count": null, "id": "da6ac34d-836e-4c15-9cec-2059d5fe2442", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ae872613-2d24-48f8-b36b-38e73fd8967d", "metadata": {}, "outputs": [], "source": ["new_products = product_df2_nonbook_whitelisted[product_df2_nonbook_whitelisted.metadata_name.isna()]"]}, {"cell_type": "code", "execution_count": null, "id": "b3b29468-0530-4dbf-bef7-5de19e5d56a5", "metadata": {}, "outputs": [], "source": ["new_products.shape"]}, {"cell_type": "code", "execution_count": null, "id": "0ea5706c-dff4-43c6-a638-9d1de1fb1831", "metadata": {}, "outputs": [], "source": ["new_products[\"sim_pdts\"] = new_products.product_name.map(pnames_sim_pname_dict)"]}, {"cell_type": "code", "execution_count": null, "id": "eb2ec2f9-b756-467a-b758-ec0a750df4a8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d58b9373-e7e5-4596-8d46-2f2bf855186b", "metadata": {}, "outputs": [], "source": ["# new_products['nearest_gift_pdts'] = new_products.apply(lambda x:[y for y in x['sim_pdts'] if y in pname_gift_entities_dict],axis=1)\n", "# new_products['pred_gift_entities'] = new_products.apply(lambda x:[pname_gift_entities_dict[y] for y in x['nearest_gift_pdts']],axis=1)\n", "# new_products['pred_gift_entities'] = new_products['pred_gift_entities'].apply(lambda x: list(set(itertools.chain.from_iterable(x))))"]}, {"cell_type": "code", "execution_count": null, "id": "52e53bc2-5fde-462f-a576-9fdd3522a006", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2f15eb8a-8800-43b5-af04-523862cc03d3", "metadata": {}, "outputs": [], "source": ["new_products[\"nearest_gift_pdts\"] = new_products.apply(\n", "    lambda x: [y for y in x[\"sim_pdts\"] if y in pname_gift_entities_dict], axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "712d71e5-3d2c-4e0f-9cfc-e1e80b14235b", "metadata": {}, "outputs": [], "source": ["new_products2 = new_products.explode(\"nearest_gift_pdts\").drop([\"sim_pdts\"], axis=1)\n", "new_products2 = new_products2[new_products2.nearest_gift_pdts.notna()]\n", "new_products2[\"pred_gift_entities\"] = new_products2.apply(\n", "    lambda x: pname_gift_entities_dict[x[\"nearest_gift_pdts\"]], axis=1\n", ")\n", "print(new_products2.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "a491f40b-7154-4b2c-942f-be5b88e19c95", "metadata": {"tags": []}, "outputs": [], "source": ["new_products2.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "48540ca5-508d-432d-991d-c130b6787d7b", "metadata": {}, "outputs": [], "source": ["# new_products2 = new_products2[\n", "#     (~new_products2.product_name.str.lower().str.contains(\"pack\"))\n", "#     & (~new_products2.nearest_gift_pdts.str.lower().str.contains(\"pack\"))\n", "# ]\n", "# print(new_products2.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "62ce2b1d-8cde-4c78-b178-28762fdd80e0", "metadata": {}, "outputs": [], "source": ["new_products2.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "034e2de9-15e6-43da-aeb7-33604db24178", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "773ec45e-db46-4759-8df2-65968183fcee", "metadata": {}, "outputs": [], "source": ["new_products2 = new_products2.explode(\"pred_gift_entities\")\n", "print(new_products2.shape)\n", "new_products2 = new_products2[new_products2.pred_gift_entities.notna()]\n", "print(new_products2.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "f08c0724-d27d-404a-8f43-ac8a037456a8", "metadata": {}, "outputs": [], "source": ["new_products2.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "51cd6d5a-ae48-4a82-84ec-ea5509923c2d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a59f8687-c742-4582-b494-c24f2e9f50ba", "metadata": {}, "outputs": [], "source": ["assert (\n", "    base_entities_df[[\"metadata_type\", \"metadata_name\"]].drop_duplicates().shape[0]\n", "    == base_entities_df[[\"metadata_name\"]].drop_duplicates().shape[0]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c5e15735-a177-4100-923b-3f608991a2af", "metadata": {}, "outputs": [], "source": ["new_products2.shape"]}, {"cell_type": "code", "execution_count": null, "id": "1199b807-0220-48d1-aa80-b005d9ed1899", "metadata": {}, "outputs": [], "source": ["new_products2 = new_products2.merge(\n", "    base_entities_df[[\"metadata_type\", \"metadata_name\"]]\n", "    .drop_duplicates()\n", "    .rename(\n", "        {\"metadata_type\": \"pred_gift_entities_type\", \"metadata_name\": \"pred_gift_entities\"}, axis=1\n", "    ),\n", "    on=\"pred_gift_entities\",\n", "    how=\"left\",\n", ")\n", "new_products2.shape"]}, {"cell_type": "code", "execution_count": null, "id": "aa5bdd0e-6d79-4d2b-9efe-dcb055d7b958", "metadata": {}, "outputs": [], "source": ["new_products2.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "1c6a1868-60a2-40bd-87fe-ceca2c75ff25", "metadata": {}, "outputs": [], "source": ["assert new_products2.pred_gift_entities_type.isna().sum() == 0"]}, {"cell_type": "code", "execution_count": null, "id": "64481ef5-a88f-4df3-962d-f86d75628fff", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9c213b8c-b899-4c70-8059-a1fc52f30359", "metadata": {}, "outputs": [], "source": ["all_keyword_breakup_df.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "0fec0762-e5ec-4767-a5d7-b3a685302bb2", "metadata": {}, "outputs": [], "source": ["all_keyword_breakup_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "219283dc-b9e0-4121-8ea4-205ba0da5414", "metadata": {}, "outputs": [], "source": ["all_keyword_breakup_df[\"keyword_name\"].nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "e287db29-ef97-4728-a960-4e50f0ff642c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "55b7b0df-18ef-4f27-a43f-8c16225300c9", "metadata": {}, "outputs": [], "source": ["new_products2[new_products2.pred_gift_entities == \"strauss gift\"]"]}, {"cell_type": "code", "execution_count": null, "id": "3f2398e3-245c-46f8-bd44-d22bb84d2a35", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e14907d1-5256-41e7-86dd-d80fd426dc35", "metadata": {}, "outputs": [], "source": ["new_products2.merge(\n", "    all_keyword_breakup_df, left_on=\"pred_gift_entities\", right_on=\"keyword_name\", how=\"inner\"\n", ").drop([\"keyword_name\"], axis=1).shape"]}, {"cell_type": "code", "execution_count": null, "id": "9c9bc0ff-d86d-4dde-b1bb-155b131bdb74", "metadata": {}, "outputs": [], "source": ["new_products2.merge(\n", "    all_keyword_breakup_df, left_on=\"pred_gift_entities\", right_on=\"keyword_name\", how=\"left\"\n", ").drop([\"keyword_name\"], axis=1).shape"]}, {"cell_type": "code", "execution_count": null, "id": "8b5fd47b-c621-44d8-b917-be8cba3eaec1", "metadata": {}, "outputs": [], "source": ["new_products2[\n", "    ~new_products2.pred_gift_entities.isin(all_keyword_breakup_df.keyword_name.unique().tolist())\n", "].shape"]}, {"cell_type": "code", "execution_count": null, "id": "97f02904-dfa3-4238-b85d-5e56bade1a01", "metadata": {}, "outputs": [], "source": ["new_products2[\n", "    ~new_products2.pred_gift_entities.isin(all_keyword_breakup_df.keyword_name.unique().tolist())\n", "].head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "f00e7171-f48e-469d-9884-c99cc293fa58", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "af8bfb4c-2568-4173-960f-8199b8f36548", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a837dbb8-a4c5-4163-a922-eca8d3bf2cf7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3713de28-c1d3-45fd-8ccf-60660eafe0ee", "metadata": {}, "outputs": [], "source": ["new_products3 = new_products2.merge(\n", "    all_keyword_breakup_df, left_on=\"pred_gift_entities\", right_on=\"keyword_name\", how=\"left\"\n", ").drop([\"keyword_name\"], axis=1)\n", "\n", "new_products3.shape"]}, {"cell_type": "code", "execution_count": null, "id": "21c91ae6-3490-4b5c-9d46-102d5529fa24", "metadata": {}, "outputs": [], "source": ["new_products3.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "925d149e-ac20-4d53-b6df-c2927720648f", "metadata": {}, "outputs": [], "source": ["new_products3.head(10)"]}, {"cell_type": "markdown", "id": "96446eb4-9a4c-4011-8c70-e43bab834a41", "metadata": {"tags": []}, "source": ["## remove non-common ptypes"]}, {"cell_type": "code", "execution_count": null, "id": "f19488c5-cda5-47b4-beda-4d355461c9a8", "metadata": {}, "outputs": [], "source": ["pname_ptype_dict = product_df.groupby([\"product_name\"], as_index=False).agg(\n", "    {\"product_type\": lambda x: set(list(x))}, axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7fdcbb5b-a390-48b9-822a-d27c1303b53d", "metadata": {}, "outputs": [], "source": ["pname_ptype_dict = dict(zip(pname_ptype_dict.product_name, pname_ptype_dict.product_type))"]}, {"cell_type": "code", "execution_count": null, "id": "2f290002-1c00-4b7c-83af-d1280885bce7", "metadata": {}, "outputs": [], "source": ["new_products4 = new_products3.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "67275a87-149d-4380-aa53-4102f2e068eb", "metadata": {}, "outputs": [], "source": ["new_products4[\"nearest_gift_pdt_ptype\"] = new_products4[\"nearest_gift_pdts\"].map(pname_ptype_dict)"]}, {"cell_type": "code", "execution_count": null, "id": "d63928a0-4422-45d4-8387-95c0b2ba4185", "metadata": {}, "outputs": [], "source": ["new_products4.shape"]}, {"cell_type": "code", "execution_count": null, "id": "d341f836-cf1a-4d9f-b034-acc12dda8e0f", "metadata": {}, "outputs": [], "source": ["temp = new_products4[\n", "    new_products4.apply(lambda x: x[\"product_type\"] not in x[\"nearest_gift_pdt_ptype\"], axis=1)\n", "]\n", "temp.shape"]}, {"cell_type": "code", "execution_count": null, "id": "644b2cec-ad64-4c19-92ac-c16c49f8c939", "metadata": {}, "outputs": [], "source": ["temp.sample(5)"]}, {"cell_type": "code", "execution_count": null, "id": "90242270-3d5d-4e16-a5e5-41c07029a7b3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "63964ad3-75d1-4b73-80db-cfd52bca8eea", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "979b8569-572b-4ef4-bef7-57b3df241f43", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ee9aae88-1009-453f-9608-20e93d6e0b81", "metadata": {}, "outputs": [], "source": ["display_df2 = (\n", "    new_products3[(new_products3[\"pred_gift_entities\"].apply(len) > 0)]\n", "    .drop([\"variant\", \"keyterm\", \"actual_l0\", \"actual_l1\", \"metadata_name\"], axis=1)\n", "    .drop([\"occasion\", \"recipient\", \"usecase\", \"l0_category\"], axis=1)\n", "    .drop([\"blacklisted_pid\"], axis=1)\n", "    .drop(\n", "        [\n", "            \"attribute\",  #'sim_pdts'\n", "        ],\n", "        axis=1,\n", "    )\n", "    .fillna(\"\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ad6bed28-bc63-4251-826b-8b0478a63fd8", "metadata": {}, "outputs": [], "source": ["display_df2 = display_df2[\n", "    display_df2.apply(\n", "        lambda x: True if x[\"BRAND\"] == \"\" or x[\"BRAND\"] == x[\"brand\"] else False, axis=1\n", "    )\n", "]\n", "display_df2.shape"]}, {"cell_type": "code", "execution_count": null, "id": "73029cbd-1e30-4ffc-8fc6-3f4591c7c089", "metadata": {}, "outputs": [], "source": ["display_df2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "fd9c426b-c52d-4b55-9c5c-5fd845c22174", "metadata": {}, "outputs": [], "source": ["display_df2[\n", "    display_df2.apply(\n", "        lambda x: (\n", "            False\n", "            if (\"pack\" in str(x[\"pred_gift_entities\"]) and \"pack\" not in str(x[\"product_name\"]))\n", "            else True\n", "        ),\n", "        axis=1,\n", "    )\n", "].shape"]}, {"cell_type": "code", "execution_count": null, "id": "b41cb70b-999b-4547-b43c-c02fa1417e03", "metadata": {}, "outputs": [], "source": ["display_df2[\n", "    ~display_df2.apply(\n", "        lambda x: (\n", "            False\n", "            if (\"pack\" in str(x[\"pred_gift_entities\"]) and \"pack\" not in str(x[\"product_name\"]))\n", "            else True\n", "        ),\n", "        axis=1,\n", "    )\n", "].shape"]}, {"cell_type": "code", "execution_count": null, "id": "36d71128-45a3-44d3-96f9-ab55ea9adac3", "metadata": {}, "outputs": [], "source": ["display_df2[\n", "    ~display_df2.apply(\n", "        lambda x: (\n", "            False\n", "            if (\n", "                \"pack\" in str(x[\"pred_gift_entities\"]) and \"gift pack\" not in str(x[\"product_name\"])\n", "            )\n", "            else True\n", "        ),\n", "        axis=1,\n", "    )\n", "].shape"]}, {"cell_type": "code", "execution_count": null, "id": "c488d8b7-6617-42e5-b833-f95ff734009b", "metadata": {}, "outputs": [], "source": ["display_df2[\n", "    ~display_df2.apply(\n", "        lambda x: (\n", "            False\n", "            if (\n", "                \"gift pack\" in str(x[\"pred_gift_entities\"])\n", "                and \"gift pack\" not in str(x[\"product_name\"])\n", "            )\n", "            else True\n", "        ),\n", "        axis=1,\n", "    )\n", "].shape"]}, {"cell_type": "code", "execution_count": null, "id": "43edf1ea-298a-4fed-9412-d7903825d53b", "metadata": {}, "outputs": [], "source": ["display_df2[\n", "    ~display_df2.apply(\n", "        lambda x: (\n", "            False\n", "            if (\n", "                \"gift set\" in str(x[\"pred_gift_entities\"])\n", "                and \"gift set\" not in str(x[\"product_name\"])\n", "            )\n", "            else True\n", "        ),\n", "        axis=1,\n", "    )\n", "].shape"]}, {"cell_type": "code", "execution_count": null, "id": "7bb66dcb-13c5-4dd9-8fc2-2679a8e49b67", "metadata": {}, "outputs": [], "source": ["display_df2[\n", "    ~display_df2.apply(\n", "        lambda x: (\n", "            False\n", "            if (\n", "                \" e gift card\" in str(x[\"pred_gift_entities\"])\n", "                and \"egift card\" not in str(x[\"product_name\"])\n", "            )\n", "            else True\n", "        ),\n", "        axis=1,\n", "    )\n", "].shape"]}, {"cell_type": "code", "execution_count": null, "id": "2283d8bf-51a6-43ae-812b-8654b0d4240a", "metadata": {}, "outputs": [], "source": ["display_df2[\n", "    ~display_df2.apply(\n", "        lambda x: (\n", "            False\n", "            if (\"pack\" in str(x[\"pred_gift_entities\"]) and \"pack\" not in str(x[\"product_name\"]))\n", "            else True\n", "        ),\n", "        axis=1,\n", "    )\n", "].sample(5)"]}, {"cell_type": "code", "execution_count": null, "id": "c007c651-d664-446c-952a-b0dea4e1e3d9", "metadata": {}, "outputs": [], "source": ["display_df2[\n", "    ~display_df2.apply(\n", "        lambda x: (\n", "            False\n", "            if (\n", "                \"gift pack\" in str(x[\"pred_gift_entities\"])\n", "                and \"gift pack\" not in str(x[\"product_name\"])\n", "            )\n", "            else True\n", "        ),\n", "        axis=1,\n", "    )\n", "].sample(5)"]}, {"cell_type": "code", "execution_count": null, "id": "27c9a868-087f-4499-84e1-12795804ae06", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f047ce31-fc10-4276-bcaa-7f91282c67d7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "694afebb-4724-48e5-9eae-5686585a097c", "metadata": {}, "outputs": [], "source": ["display_df2 = display_df2[\n", "    display_df2.apply(\n", "        lambda x: (\n", "            False\n", "            if (\n", "                \"gift pack\" in str(x[\"pred_gift_entities\"])\n", "                and \"gift pack\" not in str(x[\"product_name\"])\n", "            )\n", "            else True\n", "        ),\n", "        axis=1,\n", "    )\n", "]\n", "print(display_df2.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "156b143e-8cab-4313-88f6-ba377c5f2956", "metadata": {}, "outputs": [], "source": ["display_df2 = display_df2[\n", "    display_df2.apply(\n", "        lambda x: (\n", "            False\n", "            if (\n", "                \"gift set\" in str(x[\"pred_gift_entities\"])\n", "                and \"gift set\" not in str(x[\"product_name\"])\n", "            )\n", "            else True\n", "        ),\n", "        axis=1,\n", "    )\n", "]\n", "print(display_df2.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "428b9cea-b01c-4332-854c-cf01e1b6a845", "metadata": {}, "outputs": [], "source": ["display_df2 = display_df2[\n", "    display_df2.apply(\n", "        lambda x: (\n", "            False\n", "            if (\n", "                \" e gift card\" in str(x[\"pred_gift_entities\"])\n", "                and \"egift card\" not in str(x[\"product_name\"])\n", "            )\n", "            else True\n", "        ),\n", "        axis=1,\n", "    )\n", "]\n", "print(display_df2.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "7a9819a8-2df4-4122-97c0-9bdc06663909", "metadata": {}, "outputs": [], "source": ["display_df2[display_df2.pred_gift_entities == \"strauss gift\"]"]}, {"cell_type": "code", "execution_count": null, "id": "b80b942d-68bc-4ffd-9f88-d6087542ca5b", "metadata": {}, "outputs": [], "source": ["display_df2.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "c30c0112-75fc-4e0d-973c-585e754735c0", "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "\n", "def contains_keywords(text):\n", "\n", "    pattern = r\"\\b(pet|pets|dog|cat|dogs|cats|kitten)\\b\"\n", "\n", "    if re.search(pattern, text, re.IGNORECASE):\n", "        return True\n", "    return False"]}, {"cell_type": "code", "execution_count": null, "id": "73b74f62-9619-4640-bec7-3c0fe2d2514b", "metadata": {}, "outputs": [], "source": ["display_df2.product_name.apply(contains_keywords).value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "831acfa1-91ed-4fd1-b2e6-d30f6e0fc9e0", "metadata": {}, "outputs": [], "source": ["print(display_df2.shape)\n", "display_df2 = display_df2[~display_df2.product_name.apply(contains_keywords)]\n", "print(display_df2.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "97fa5451-64f8-4b13-bce4-4d1c5c833995", "metadata": {}, "outputs": [], "source": ["print(display_df2.shape)\n", "display_df2 = display_df2[~display_df2.nearest_gift_pdts.apply(contains_keywords)]\n", "print(display_df2.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "c89ce32c-487e-4f78-beb2-4424e9c33603", "metadata": {}, "outputs": [], "source": ["print(display_df2.shape)\n", "display_df2 = display_df2[~display_df2.pred_gift_entities.apply(contains_keywords)]\n", "print(display_df2.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "f209bbd1-b363-4757-b254-7864446ca87c", "metadata": {}, "outputs": [], "source": ["display_df2[display_df2.product_id == 22831]"]}, {"cell_type": "code", "execution_count": null, "id": "e172afd5-fbd3-4f4f-8341-eaa334f1b775", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4c26dccf-8388-4fe3-986c-5c7e3e44ff0f", "metadata": {}, "outputs": [], "source": ["metadata_name_type_id_df.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "1700f788-dc98-439b-87ae-ac077ec1acfb", "metadata": {}, "outputs": [], "source": ["metadata_name_type_id_df.metadata_id.apply(len).value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "8ced718e-4bfb-4fac-bead-c0500a0c42b4", "metadata": {"tags": []}, "outputs": [], "source": ["print(metadata_name_type_id_df.shape)\n", "metadata_name_type_id_df = metadata_name_type_id_df[\n", "    metadata_name_type_id_df.metadata_id.apply(len) == 1\n", "]\n", "print(metadata_name_type_id_df.shape)\n", "metadata_name_type_id_df.metadata_id = metadata_name_type_id_df.metadata_id.apply(lambda x: x[0])"]}, {"cell_type": "code", "execution_count": null, "id": "1afa498b-a75d-4b7d-9ade-a7b69b5e99c0", "metadata": {}, "outputs": [], "source": ["metadata_name_type_id_df.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "6aae44e5-acf0-459e-a2db-469ac1b1d815", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a8aecf84-5a6d-431e-941e-a1a2a0f0be4a", "metadata": {}, "outputs": [], "source": ["all_keyword_breakup_df_raw2[all_keyword_breakup_df_raw2.keyword_name == \"strauss gift\"]"]}, {"cell_type": "code", "execution_count": null, "id": "ae92da8c-b5ae-48e3-aec8-74700d25f1dd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2fbf5ad8-5732-4af9-9448-b2f722678f3c", "metadata": {}, "outputs": [], "source": ["display_df2.shape"]}, {"cell_type": "code", "execution_count": null, "id": "9630ff91-0526-4f19-b05f-7de42cb1b2ba", "metadata": {}, "outputs": [], "source": ["display_df2.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "b26c9f47-7b37-4389-bcbe-a39e368ca350", "metadata": {}, "outputs": [], "source": ["display_df2.merge(\n", "    metadata_name_type_id_df.add_suffix(\"_right\"),\n", "    left_on=[\"pred_gift_entities\", \"pred_gift_entities_type\"],\n", "    right_on=[\"metadata_name_right\", \"metadata_type_right\"],\n", "    how=\"inner\",\n", ").drop([\"metadata_name_right\", \"metadata_type_right\"], axis=1).rename(\n", "    {\"metadata_id_right\": \"pred_gift_entities_id\"}, axis=1\n", ").shape"]}, {"cell_type": "code", "execution_count": null, "id": "a2005d0d-1340-4827-a633-b7e6ce318359", "metadata": {}, "outputs": [], "source": ["display_df2.merge(\n", "    metadata_name_type_id_df.add_suffix(\"_right\"),\n", "    left_on=[\"pred_gift_entities\", \"pred_gift_entities_type\"],\n", "    right_on=[\"metadata_name_right\", \"metadata_type_right\"],\n", "    how=\"inner\",\n", ").drop([\"metadata_name_right\", \"metadata_type_right\"], axis=1).rename(\n", "    {\"metadata_id_right\": \"pred_gift_entities_id\"}, axis=1\n", ").sample(\n", "    2\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3865c610-eba2-4467-97cf-64e8f78a1e43", "metadata": {}, "outputs": [], "source": ["display_df2 = (\n", "    display_df2.merge(\n", "        metadata_name_type_id_df.add_suffix(\"_right\"),\n", "        left_on=[\"pred_gift_entities\", \"pred_gift_entities_type\"],\n", "        right_on=[\"metadata_name_right\", \"metadata_type_right\"],\n", "        how=\"inner\",\n", "    )\n", "    .drop([\"metadata_name_right\", \"metadata_type_right\"], axis=1)\n", "    .rename({\"metadata_id_right\": \"pred_gift_entities_id\"}, axis=1)\n", ")\n", "print(display_df2.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "fdb7bd9c-55cc-4c07-9d15-f2f05d13e35e", "metadata": {}, "outputs": [], "source": ["display_df2.pred_gift_entities_type.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "6e4899b6-111a-4623-82f1-04ef0aeec326", "metadata": {}, "outputs": [], "source": ["# display_df2 = display_df2"]}, {"cell_type": "code", "execution_count": null, "id": "7722fa90-302f-4533-a38b-2b8b6d1cedf4", "metadata": {}, "outputs": [], "source": ["display_df2.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "e633e5cf-79cd-405a-b611-b50104e81ecb", "metadata": {}, "outputs": [], "source": ["# display_df2.columns"]}, {"cell_type": "code", "execution_count": null, "id": "6289d28d-cf20-4f6f-9097-736ed772da62", "metadata": {}, "outputs": [], "source": ["# display_df3 = display_df2.groupby(\n", "#     [\n", "#         \"l1_category\",\n", "#         \"l2_category\",\n", "#         \"product_type\",\n", "#         \"brand\",\n", "#         \"product_name\",\n", "#         \"product_id\",\n", "#         \"actual_pname\",\n", "#         \"nearest_gift_pdts\",\n", "#         \"pred_gift_entities_type\",\n", "#     ],\n", "#     as_index=False,\n", "# ).agg(\n", "#     {\n", "#         \"pred_gift_entities\": lambda x: list(set(x) - set([\"\"])),\n", "#         \"ATTRIBUTE\": lambda x: list(set(x) - set([\"\"])),\n", "#         \"BRAND\": lambda x: list(set(x) - set([\"\"])),\n", "#         \"OCCASION\": lambda x: list(set(x) - set([\"\"])),\n", "#         \"RECIPIENT\": lambda x: list(set(x) - set([\"\"])),\n", "#         \"USECASE\": lambda x: list(set(x) - set([\"\"])),\n", "#     },\n", "#     axis=1,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "0975ca9f-3045-4038-8962-bfe1ad8c5b9d", "metadata": {}, "outputs": [], "source": ["# display_df3.shape"]}, {"cell_type": "code", "execution_count": null, "id": "f650cb2b-3ba4-4e57-b194-5f3f7356d4ce", "metadata": {}, "outputs": [], "source": ["# display_df3[display_df3.product_id == 29713]"]}, {"cell_type": "code", "execution_count": null, "id": "86b0890e-27b8-4974-85f6-faf0b0e039f6", "metadata": {}, "outputs": [], "source": ["# display_df3.product_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "86318c0e-eae5-47e9-9bee-fd8e28ca572d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2429bff0-bf63-4614-ae2d-b2814a607ddd", "metadata": {"tags": []}, "outputs": [], "source": ["# display_df3.sample(10)"]}, {"cell_type": "code", "execution_count": null, "id": "6b0170ac-15ee-48f1-b1fb-4e18b84abb6b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "eb180228-08d9-4d54-a19e-0daaf4f4cf35", "metadata": {}, "outputs": [], "source": ["gift_whitelisted_pname_pid_dict = product_df2_nonbook_whitelisted.groupby(\n", "    [\"product_name\"], as_index=False\n", ").agg({\"product_id\": lambda x: list(set(x))}, axis=1)\n", "\n", "gift_whitelisted_pname_pid_dict = dict(\n", "    zip(\n", "        gift_whitelisted_pname_pid_dict[\"product_name\"].apply(str),\n", "        gift_whitelisted_pname_pid_dict[\"product_id\"],\n", "    )\n", ")\n", "print(len(gift_whitelisted_pname_pid_dict))"]}, {"cell_type": "code", "execution_count": null, "id": "658048ce-cd5a-4fd6-8e64-58b1172a1894", "metadata": {}, "outputs": [], "source": ["gift_whitelisted_pname_pid_dict[\"quace floral design 2025 desk calendar with stand multicolor\"]"]}, {"cell_type": "code", "execution_count": null, "id": "f68622d2-7270-4e83-a988-b107200bb672", "metadata": {}, "outputs": [], "source": ["display_df3 = display_df2[~display_df2.product_id.isin(combo_offer_pids)]\n", "\n", "display_df3[\"nearest_gift_pids\"] = display_df3.nearest_gift_pdts.apply(\n", "    lambda x: (\n", "        tuple([str(y) for y in set(gift_whitelisted_pname_pid_dict[x])])\n", "        if x in gift_whitelisted_pname_pid_dict\n", "        else tuple([])\n", "    )\n", ")\n", "\n", "print(display_df3.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "5e5a9709-ef9d-47d8-8a8f-6faab65db03c", "metadata": {}, "outputs": [], "source": ["display_df3 = display_df3[display_df3[\"nearest_gift_pids\"].apply(len) > 0]\n", "print(display_df3.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "3a133ebe-e0ea-4870-84e6-43d99751fda4", "metadata": {}, "outputs": [], "source": ["display_df3.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "0ebc5c21-72b8-4251-938c-07bb16858bff", "metadata": {}, "outputs": [], "source": ["display_df3 = display_df3.rename(\n", "    {\"nearest_gift_pdts\": \"anchor_product_name\", \"nearest_gift_pids\": \"anchor_product_ids\"}, axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9496c16d-f097-4b60-aa1d-d0ef2d5df5a8", "metadata": {}, "outputs": [], "source": ["display_df3.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "8b73cf64-c46c-4760-b53b-493eebe1104e", "metadata": {}, "outputs": [], "source": ["display_df3 = display_df3[\n", "    [\n", "        \"product_id\",\n", "        \"product_name\",\n", "        \"pred_gift_entities_type\",\n", "        \"pred_gift_entities\",\n", "        \"pred_gift_entities_id\",\n", "        \"anchor_product_name\",\n", "        \"anchor_product_ids\",\n", "    ]\n", "].drop_duplicates()\n", "print(display_df3.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "361fdf44-d4b4-42f5-aea6-f90180ebe7d9", "metadata": {}, "outputs": [], "source": ["display_df3[\"pred_gift_meta\"] = display_df3.apply(\n", "    lambda x: tuple([x[\"pred_gift_entities\"], str(x[\"pred_gift_entities_id\"])]), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "132ead92-e66e-40c4-9e70-d010f97386b2", "metadata": {}, "outputs": [], "source": ["display_df3.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "31467ee4-af53-4128-9f4e-6360278cd139", "metadata": {}, "outputs": [], "source": ["display_df4 = display_df3.groupby(\n", "    [\n", "        \"product_id\",\n", "        \"product_name\",\n", "        \"anchor_product_name\",\n", "        \"anchor_product_ids\",\n", "        \"pred_gift_entities_type\",\n", "    ],\n", "    as_index=False,\n", ").agg(\n", "    {\n", "        # \"pred_gift_entities\": list,\n", "        # \"pred_gift_entities_id\": list,\n", "        \"pred_gift_meta\": lambda x: tuple(list(set(list(x))))\n", "    },\n", "    axis=1,\n", ")\n", "print(display_df4.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "778baabb-3477-44b8-9b00-2634d5f882c6", "metadata": {"tags": []}, "outputs": [], "source": ["display_df4.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "6d90be53-0205-4355-a22c-8207422daca6", "metadata": {}, "outputs": [], "source": ["display_df4.sample(5)"]}, {"cell_type": "code", "execution_count": null, "id": "539d01cd-6b20-4b0c-9766-d15e91c94680", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ab3cf52a-9c29-43b6-9e38-196ef527da67", "metadata": {}, "outputs": [], "source": ["prefix = (\n", "    f\"data-science/ner/gift_assortment_recos_blinkgpt/similar_products_based_recos/\" + date + \"/\"\n", ")\n", "s3_dump_path = f\"s3://{bucket}/{prefix}\"\n", "print(s3_dump_path)"]}, {"cell_type": "code", "execution_count": null, "id": "ab0a7d67-6d5e-4a55-8e48-239bdab13504", "metadata": {}, "outputs": [], "source": ["display_df4.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "2a14253c-4d35-4fd2-aa43-c65d6c7b3c25", "metadata": {}, "outputs": [], "source": ["display_df4.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "84f298c9-8a47-463a-b6f7-6583f057f36a", "metadata": {}, "outputs": [], "source": ["display_df4.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "e1521292-2b31-4216-a14c-85205322d81a", "metadata": {}, "outputs": [], "source": ["utls.write_df_as_parquet_to_s3(display_df4, s3_dump_path)"]}, {"cell_type": "code", "execution_count": null, "id": "069f9c82-9bb8-4c1a-be1d-1069a6dd1935", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8837e252-b0ad-4a0e-9fb4-1ca4e18400ed", "metadata": {}, "outputs": [], "source": ["display_df4.sample(20)"]}, {"cell_type": "code", "execution_count": null, "id": "84a311f6-84c5-4415-9ce1-2e3673e75c5b", "metadata": {}, "outputs": [], "source": ["display_df4.shape"]}, {"cell_type": "code", "execution_count": null, "id": "48fa5b89-61f9-4a4c-99d1-1ef7c5623f36", "metadata": {}, "outputs": [], "source": ["display_df4.to_csv(\"similar_product_based_reco.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "6f31f64e-2102-4c01-930c-6e1c8b151edf", "metadata": {"tags": []}, "outputs": [], "source": ["pb.to_sheets(\n", "    display_df4, \"1LsUtbXqBe1Dy1i_uWgOHLVygv01N3DU5-wucTCf1vEc\", \"Simillar_Products_Based_Recos\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0ee91450-2646-4e8a-b38e-575e22be4e55", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6c978dc8-6f3b-4d45-abf6-9809a5f6e866", "metadata": {}, "outputs": [], "source": ["display_df4.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "6618a3f5-0c37-49b1-93bd-bbbf43bd9d20", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9b972b9f-f603-42f5-90fe-7b6a13137043", "metadata": {}, "outputs": [], "source": ["# display_df4.to_csv('Similar_Products_Gift_Tags.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "2171f937-afd1-445a-ad32-1c652e4016af", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "c596edd8-d9b6-4f88-bb15-edb17a0e3d6f", "metadata": {}, "source": ["# QTP Based"]}, {"cell_type": "code", "execution_count": null, "id": "61137aaf-9101-4b2f-927f-22753320a684", "metadata": {}, "outputs": [], "source": ["type(combo_offer_pids)"]}, {"cell_type": "code", "execution_count": null, "id": "cb0af3c1-456a-4215-94a2-8704224e3aec", "metadata": {}, "outputs": [], "source": ["gift_whitelisted_pname_pid_dict = product_df2_nonbook_whitelisted.groupby(\n", "    [\"product_name\"], as_index=False\n", ").agg({\"product_id\": lambda x: list(set(x))}, axis=1)\n", "\n", "gift_whitelisted_pname_pid_dict = dict(\n", "    zip(\n", "        gift_whitelisted_pname_pid_dict[\"product_name\"],\n", "        gift_whitelisted_pname_pid_dict[\"product_id\"],\n", "    )\n", ")\n", "print(len(gift_whitelisted_pname_pid_dict))"]}, {"cell_type": "code", "execution_count": null, "id": "5a1c2dcb-2bdd-4252-88da-d0e84200abd0", "metadata": {}, "outputs": [], "source": ["base_entities_df.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "99836d29-9524-4070-9f8b-ffaff9a5ba43", "metadata": {}, "outputs": [], "source": ["exisiting_gift_mapping_df = base_entities_df[[\"metadata_name\", \"mapped_pid\"]].rename(\n", "    {\"metadata_name\": \"gift_entity\", \"mapped_pid\": \"product_id\"}, axis=1\n", ")\n", "exisiting_gift_mapping_df[\"product_exists\"] = True\n", "print(exisiting_gift_mapping_df.shape)\n", "\n", "# exisiting_gift_mapping_df = base_entities_df.groupby([\"metadata_name\"], as_index=False).agg(\n", "#     {\"mapped_pid\": lambda x: list(set(x))}, axis=1\n", "# )\n", "\n", "# exisiting_gift_mapping_dict = dict(\n", "#     zip(exisiting_gift_mapping_df[\"metadata_name\"], exisiting_gift_mapping_df[\"mapped_pid\"])\n", "# )\n", "# print(len(exisiting_gift_mapping_dict))"]}, {"cell_type": "code", "execution_count": null, "id": "14d3a7fe-bcf2-4004-a2ce-50e574f90cbc", "metadata": {}, "outputs": [], "source": ["exisiting_gift_mapping_df.sample(5)"]}, {"cell_type": "code", "execution_count": null, "id": "7a0d0e37-ac7f-449c-83b3-c2d5c77313fa", "metadata": {}, "outputs": [], "source": ["display_df4.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "53acad3d-db09-466b-bf7c-3a0811d38a9d", "metadata": {}, "outputs": [], "source": ["tmp_display_df4 = display_df4[[\"pred_gift_meta\", \"product_id\"]].explode(\"pred_gift_meta\")\n", "tmp_display_df4[\"pred_gift_entities\"] = tmp_display_df4[\"pred_gift_meta\"].apply(lambda x: x[0])\n", "tmp_display_df4[\"product_id\"] = tmp_display_df4[\"product_id\"].astype(int)\n", "tmp_display_df4.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "34814623-9aee-4c3d-a13a-172763dfde7c", "metadata": {}, "outputs": [], "source": ["tmp_display_df4.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "7d76005d-0566-45d6-85df-b167a9fbea0a", "metadata": {}, "outputs": [], "source": ["similar_pdts_based_recos_df = (\n", "    tmp_display_df4[[\"pred_gift_entities\", \"product_id\"]]\n", "    .explode(\"pred_gift_entities\")\n", "    .rename({\"pred_gift_entities\": \"gift_entity\"}, axis=1)\n", ")\n", "similar_pdts_based_recos_df[\"product_exists\"] = True\n", "print(similar_pdts_based_recos_df.shape)\n", "\n", "# similar_pdts_based_recos_df = new_products2.groupby([\"pred_gift_entities\"], as_index=False).agg(\n", "#     {\"product_id\": lambda x: list(set(x))}, axis=1\n", "# )\n", "\n", "# similar_pdts_based_recos_dict = dict(\n", "#     zip(\n", "#         similar_pdts_based_recos_df[\"pred_gift_entities\"],\n", "#         similar_pdts_based_recos_df[\"product_id\"],\n", "#     )\n", "# )\n", "# print(len(similar_pdts_based_recos_dict))"]}, {"cell_type": "code", "execution_count": null, "id": "ced55971-11b2-459c-8c8b-d276ea746183", "metadata": {}, "outputs": [], "source": ["similar_pdts_based_recos_df.sample(5)"]}, {"cell_type": "code", "execution_count": null, "id": "ad08ea04-7579-4d10-af6d-df667fd0d108", "metadata": {}, "outputs": [], "source": ["base_entities_df.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "f3db519f-e34a-4906-be3f-c63a1b4b3380", "metadata": {}, "outputs": [], "source": ["unique_base_entities_df = base_entities_df[\n", "    [\"metadata_id\", \"metadata_name\", \"metadata_type\"]\n", "].drop_duplicates()\n", "print(unique_base_entities_df.shape, unique_base_entities_df.metadata_name.nunique())"]}, {"cell_type": "code", "execution_count": null, "id": "d0d62f7c-ddb7-4e1b-95a7-0f519a66a9e7", "metadata": {}, "outputs": [], "source": ["gift_base_entities = unique_base_entities_df.metadata_name.tolist()\n", "gift_base_entities_id = unique_base_entities_df.metadata_id.tolist()\n", "gift_base_entities_type = unique_base_entities_df.metadata_type.tolist()\n", "\n", "gift_base_entities_embeddings = np.array([all_embeddings_dict[x] for x in gift_base_entities])\n", "print(gift_base_entities_embeddings.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "5caa1b30-dccf-49d7-a2a4-7118f241dbc5", "metadata": {}, "outputs": [], "source": ["gift_whitelisted_pnames = product_df2_nonbook_whitelisted.product_name.unique()\n", "gift_whitelisted_pnames_embeddings = np.array(\n", "    [all_embeddings_dict[x] for x in gift_whitelisted_pnames]\n", ")\n", "print(gift_whitelisted_pnames_embeddings.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "82f41e97-154d-4174-adee-2b5eb3d6f58c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9fec224e-79f3-4983-8ce1-2e62fea1ff78", "metadata": {}, "outputs": [], "source": ["product_name_index = create_hnsw_index(gift_whitelisted_pnames_embeddings)"]}, {"cell_type": "code", "execution_count": null, "id": "8e513116-3370-494b-87c8-6dbd88d52f7b", "metadata": {}, "outputs": [], "source": ["temp = pname_index.search(gift_base_entities_embeddings, 1500)\n", "temp = [list(zip(*x)) for x in temp]\n", "\n", "indices = np.array([np.array(x[0]) for x in temp])\n", "cosine_similarities = np.array([np.array(x[1]) for x in temp])\n", "filter_cosine_similarities = cosine_similarities >= 0.80\n", "\n", "indices_filtered = [indices[i][x] for i, x in enumerate(filter_cosine_similarities)]\n", "cosine_similarities_filtered = [\n", "    cosine_similarities[i][x] for i, x in enumerate(filter_cosine_similarities)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "2681777c-97da-4fda-b5ac-762931c8644f", "metadata": {}, "outputs": [], "source": ["query_top_pnames = [gift_whitelisted_pnames[i] for i in indices_filtered]"]}, {"cell_type": "code", "execution_count": null, "id": "96dbbfae-f1a3-47cf-9cf9-f891bed32a31", "metadata": {}, "outputs": [], "source": ["pnames_sim_pname_dict = dict(zip(all_unique_product_names, query_top_pnames))"]}, {"cell_type": "code", "execution_count": null, "id": "71125d23-1fae-4fe4-92a0-1f65c7e76541", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "46dd927f-0c08-46c4-a3ba-bc2bcca91778", "metadata": {}, "outputs": [], "source": ["new_tags_df = pd.DataFrame()\n", "new_tags_df[\"gift_entity\"] = gift_base_entities\n", "new_tags_df[\"gift_entity_id\"] = gift_base_entities_id\n", "new_tags_df[\"gift_entity_type\"] = gift_base_entities_type\n", "new_tags_df[\"sim\"] = cosine_similarities_filtered\n", "new_tags_df[\"pname\"] = query_top_pnames\n", "\n", "new_tags_df = new_tags_df.explode([\"sim\", \"pname\"])\n", "print(new_tags_df.shape)\n", "\n", "new_tags_df[\"product_id\"] = new_tags_df[\"pname\"].map(gift_whitelisted_pname_pid_dict)\n", "\n", "new_tags_df = new_tags_df.explode([\"product_id\"])\n", "\n", "print(new_tags_df.shape)\n", "\n", "new_tags_df = new_tags_df.dropna()\n", "print(new_tags_df.shape)\n", "\n", "new_tags_df.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "83c046e1-f490-467a-bbc6-03d6288cff4c", "metadata": {}, "outputs": [], "source": ["new_tags_df.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "7d533015-4cc4-4c12-aa01-ac376cbdaf03", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d125f832-3719-4453-90c2-d2c0c3a911cb", "metadata": {}, "outputs": [], "source": ["exisiting_gift_mapping_df.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "f50bb632-79d3-4010-bc44-6f86fe4f1a2a", "metadata": {}, "outputs": [], "source": ["new_tags_df = new_tags_df.merge(\n", "    exisiting_gift_mapping_df, on=[\"gift_entity\", \"product_id\"], how=\"outer\"\n", ")\n", "new_tags_df = new_tags_df[new_tags_df.product_exists.isna()].drop(\"product_exists\", axis=1)\n", "\n", "print(new_tags_df.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "88990806-7a73-47c2-ab3a-dbd8c5fdb8bf", "metadata": {}, "outputs": [], "source": ["new_tags_df = new_tags_df.merge(\n", "    similar_pdts_based_recos_df, on=[\"gift_entity\", \"product_id\"], how=\"outer\"\n", ")\n", "new_tags_df = new_tags_df[new_tags_df.product_exists.isna()].drop(\"product_exists\", axis=1)\n", "\n", "print(new_tags_df.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "10eb612a-2708-4f3c-8cb6-21a0a6f5bf76", "metadata": {}, "outputs": [], "source": ["new_tags_df = new_tags_df[~new_tags_df.product_id.isin(combo_offer_pids)]\n", "print(new_tags_df.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "382d974e-c14d-4a51-bcc2-6334a2cf05b5", "metadata": {}, "outputs": [], "source": ["new_tags_df.sample(5)"]}, {"cell_type": "code", "execution_count": null, "id": "d00df98d-2510-49be-8752-834f7ad3460f", "metadata": {}, "outputs": [], "source": ["new_tags_df[\"product_id\"] = new_tags_df[\"product_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "f6389042-72d1-4525-a0ed-3351fc30b0dd", "metadata": {}, "outputs": [], "source": ["new_tags_df.sample(5)"]}, {"cell_type": "code", "execution_count": null, "id": "c887d4dd-ac80-4fe2-be9d-e2c1b0f06ad3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b972eb18-f83c-41b0-8dcc-8984e33275f8", "metadata": {}, "outputs": [], "source": ["all_keyword_breakup_df.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "4273fa79-731e-427c-9dee-3bc53fd52ccd", "metadata": {}, "outputs": [], "source": ["all_keyword_breakup_df.shape, all_keyword_breakup_df.keyword_name.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "0edeff11-c11e-4014-afa4-2e68cd676f9b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8fd3b118-f5ba-4309-83c4-be0631f3ae4e", "metadata": {}, "outputs": [], "source": ["all_keyword_breakup_df[all_keyword_breakup_df.keyword_name == \"strauss gift\"]"]}, {"cell_type": "code", "execution_count": null, "id": "a5a2e8bb-33d6-4d62-bf44-a517c676442e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "79ef4bae-1ee9-4ad0-88a1-ff3a2437aecf", "metadata": {}, "outputs": [], "source": ["print(new_tags_df.shape)\n", "new_tags_df2 = (\n", "    new_tags_df.merge(\n", "        all_keyword_breakup_df.drop([\"keyword_id\"], axis=1),\n", "        left_on=\"gift_entity\",\n", "        right_on=\"keyword_name\",\n", "        how=\"inner\",\n", "    )\n", "    .drop(\"keyword_name\", axis=1)\n", "    .fillna(\"\")\n", ")\n", "\n", "new_tags_df2 = new_tags_df2.merge(\n", "    product_df[[\"product_id\", \"brand\"]].rename({\"brand\": \"true_brand\"}, axis=1),\n", "    on=\"product_id\",\n", "    how=\"left\",\n", ")\n", "\n", "new_tags_df2[\"BRAND\"] = new_tags_df2[\"BRAND\"].apply(utls.preprocess_str)\n", "\n", "new_tags_df2[\"product_id\"] = new_tags_df2[\"product_id\"].astype(int)\n", "new_tags_df2[\"gift_entity_id\"] = new_tags_df2[\"gift_entity_id\"].astype(int)\n", "\n", "print(new_tags_df2.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "8a18be47-7dd6-4f8f-a4e1-18833984c5e8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5fefe5a3-7fad-45df-b8d0-783879443eae", "metadata": {}, "outputs": [], "source": ["new_tags_df2.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "743401de-aec8-4b98-8404-f83242daec08", "metadata": {}, "outputs": [], "source": ["new_tags_df2[\n", "    ~new_tags_df2.apply(\n", "        lambda x: False if x[\"BRAND\"] != \"\" and x[\"BRAND\"] != x[\"true_brand\"] else True, axis=1\n", "    )\n", "].shape"]}, {"cell_type": "code", "execution_count": null, "id": "6dbedb1e-76fa-4be1-9fdc-57a4a24e263f", "metadata": {}, "outputs": [], "source": ["new_tags_df2[\n", "    ~new_tags_df2.apply(\n", "        lambda x: False if x[\"BRAND\"] != \"\" and x[\"BRAND\"] not in x[\"true_brand\"] else True, axis=1\n", "    )\n", "].shape"]}, {"cell_type": "code", "execution_count": null, "id": "5242ed9d-cd1c-4391-9c57-3eaecd92a6e9", "metadata": {}, "outputs": [], "source": ["new_tags_df2[\n", "    ~new_tags_df2.apply(\n", "        lambda x: (\n", "            False\n", "            if x[\"BRAND\"] != \"\"\n", "            and set(x[\"BRAND\"].split(\" \")).intersection(set(x[\"true_brand\"].split(\" \"))) == set()\n", "            else True\n", "        ),\n", "        axis=1,\n", "    )\n", "].shape"]}, {"cell_type": "code", "execution_count": null, "id": "0146689d-ac1a-4f05-b6e2-24bae9bc1aba", "metadata": {}, "outputs": [], "source": ["tmp1 = new_tags_df2[\n", "    ~new_tags_df2.apply(\n", "        lambda x: False if x[\"BRAND\"] != \"\" and x[\"BRAND\"] not in x[\"true_brand\"] else True, axis=1\n", "    )\n", "]\n", "print(tmp1.shape)\n", "tmp2 = new_tags_df2[\n", "    ~new_tags_df2.apply(\n", "        lambda x: (\n", "            False\n", "            if x[\"BRAND\"] != \"\"\n", "            and set(x[\"BRAND\"].split(\" \")).intersection(set(x[\"true_brand\"].split(\" \"))) == set()\n", "            else True\n", "        ),\n", "        axis=1,\n", "    )\n", "]\n", "print(tmp2.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "91a666f0-e0da-4e68-b3d2-b9d5f45e1fb6", "metadata": {}, "outputs": [], "source": ["tmp1[tmp1.product_id == 587150]"]}, {"cell_type": "code", "execution_count": null, "id": "cd18e8e2-1d6f-4340-a786-01b37639f60c", "metadata": {}, "outputs": [], "source": ["tmp1[~tmp1.index.isin(tmp2.index.tolist())].shape"]}, {"cell_type": "code", "execution_count": null, "id": "30d8364c-2a46-4f71-a4ee-64c9a2f7d04e", "metadata": {}, "outputs": [], "source": ["tmp1[~tmp1.index.isin(tmp2.index.tolist())].sample(10)"]}, {"cell_type": "code", "execution_count": null, "id": "7d208b88-8ff0-468c-87d2-560670470e77", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5175a468-532e-4d6c-840a-bd05600f216f", "metadata": {}, "outputs": [], "source": ["new_tags_df2 = new_tags_df2[\n", "    new_tags_df2.apply(\n", "        lambda x: (\n", "            False\n", "            if x[\"BRAND\"] != \"\"\n", "            and set(x[\"BRAND\"].split(\" \")).intersection(set(x[\"true_brand\"].split(\" \"))) == set()\n", "            else True\n", "        ),\n", "        axis=1,\n", "    )\n", "]\n", "print(new_tags_df2.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "9fef8c80-d9fa-4238-949b-197a1e0e38f7", "metadata": {}, "outputs": [], "source": ["new_tags_df2[new_tags_df2.product_id == 587150]"]}, {"cell_type": "code", "execution_count": null, "id": "2504dc5e-b0b1-4fce-854a-8eff0ef181f8", "metadata": {}, "outputs": [], "source": ["new_tags_df2[\n", "    new_tags_df2.apply(\n", "        lambda x: x[\"BRAND\"] != \"\" and x[\"true_brand\"] not in x[\"gift_entity\"] and x[\"sim\"] < 0.85,\n", "        axis=1,\n", "    )\n", "].shape"]}, {"cell_type": "code", "execution_count": null, "id": "996c54fb-6159-4559-9b46-6275c3face4e", "metadata": {}, "outputs": [], "source": ["new_tags_df2[\n", "    new_tags_df2.apply(\n", "        lambda x: x[\"BRAND\"] != \"\" and x[\"true_brand\"] not in x[\"gift_entity\"] and x[\"sim\"] < 0.85,\n", "        axis=1,\n", "    )\n", "].query(\"product_id==587150\")"]}, {"cell_type": "code", "execution_count": null, "id": "5d4f0382-7e1c-4fb7-adc7-ee2dcfe52e8d", "metadata": {}, "outputs": [], "source": ["new_tags_df2[\n", "    new_tags_df2.apply(\n", "        lambda x: x[\"BRAND\"] != \"\" and x[\"true_brand\"] not in x[\"gift_entity\"] and x[\"sim\"] < 0.85,\n", "        axis=1,\n", "    )\n", "].sample(5)"]}, {"cell_type": "code", "execution_count": null, "id": "9667a18f-4e8d-4e27-b2e0-7be80e84860c", "metadata": {}, "outputs": [], "source": ["new_tags_df2[new_tags_df2.apply(lambda x: x[\"true_brand\"] not in x[\"pname\"], axis=1)].sample(16)"]}, {"cell_type": "code", "execution_count": null, "id": "ebfc831c-ff8a-4897-af6a-f712a8ae305a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "70c955ee-79dd-4ca5-bdac-da8b0050915b", "metadata": {}, "outputs": [], "source": ["print(new_tags_df2.shape)\n", "new_tags_df2 = new_tags_df2[\n", "    ~new_tags_df2.apply(\n", "        lambda x: x[\"BRAND\"] != \"\" and x[\"true_brand\"] not in x[\"gift_entity\"] and x[\"sim\"] < 0.85,\n", "        axis=1,\n", "    )\n", "]\n", "print(new_tags_df2.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "b23c9bb9-ff5a-40c2-b061-ce32994ce9b9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "25b59747-d9ed-4826-a144-ec83e194eb79", "metadata": {}, "outputs": [], "source": ["new_tags_df2[\"gift_entity_id_type_tuple\"] = new_tags_df2.apply(\n", "    lambda x: (x[\"gift_entity\"], int(x[\"gift_entity_id\"]), x[\"gift_entity_type\"]), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "beb014f5-85a9-4ac8-8bfa-a0ec468205bd", "metadata": {}, "outputs": [], "source": ["new_tags_df2.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "40e4a1d8-6b54-46db-b48d-afd98484beed", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "998e5afb-0207-4e92-a0f5-6255de1923a4", "metadata": {}, "outputs": [], "source": ["# new_tags_df3 = new_tags_df2[\n", "#     (~new_tags_df2.pname.str.lower().str.contains(\"pack\"))\n", "#     & (~new_tags_df2.gift_entity.str.lower().str.contains(\"pack\"))\n", "# ]\n", "# print(new_tags_df3.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "b4828c28-0ccc-4018-8c82-9c454a2e63ec", "metadata": {}, "outputs": [], "source": ["new_tags_df3 = new_tags_df2[\n", "    new_tags_df2.apply(\n", "        lambda x: (\n", "            False\n", "            if (\"gift pack\" in str(x[\"gift_entity\"]) and \"gift pack\" not in str(x[\"pname\"]))\n", "            else True\n", "        ),\n", "        axis=1,\n", "    )\n", "]\n", "print(new_tags_df3.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "8af3fac4-d0a1-4e6c-a387-f094144cce7a", "metadata": {}, "outputs": [], "source": ["new_tags_df3 = new_tags_df3[\n", "    new_tags_df3.apply(\n", "        lambda x: (\n", "            False if (\"pack\" in str(x[\"gift_entity\"]) and \"pack\" not in str(x[\"pname\"])) else True\n", "        ),\n", "        axis=1,\n", "    )\n", "]\n", "print(new_tags_df3.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "05160c3e-353d-4ae9-957c-b473dd32a400", "metadata": {}, "outputs": [], "source": ["new_tags_df3 = new_tags_df3[\n", "    new_tags_df3.apply(\n", "        lambda x: (\n", "            False\n", "            if (\"gift set\" in str(x[\"gift_entity\"]) and \"gift set\" not in str(x[\"pname\"]))\n", "            else True\n", "        ),\n", "        axis=1,\n", "    )\n", "]\n", "print(new_tags_df3.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "7a218efa-691a-4bbe-b1f7-ed727d744558", "metadata": {}, "outputs": [], "source": ["new_tags_df3 = new_tags_df3[\n", "    new_tags_df3.apply(\n", "        lambda x: (\n", "            False\n", "            if (\" e gift card\" in str(x[\"gift_entity\"]) and \"egift card\" not in str(x[\"pname\"]))\n", "            else True\n", "        ),\n", "        axis=1,\n", "    )\n", "]\n", "print(new_tags_df3.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "d212be99-a795-41db-be94-132c62fc15f8", "metadata": {}, "outputs": [], "source": ["new_tags_df3.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "37734f73-a24d-4ebe-99ed-752f842179d0", "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "\n", "def contains_keywords(text):\n", "\n", "    pattern = r\"\\b(pet|pets|dog|cat|dogs|cats|kitten)\\b\"\n", "\n", "    if re.search(pattern, text, re.IGNORECASE):\n", "        return True\n", "    return False"]}, {"cell_type": "code", "execution_count": null, "id": "83931eec-d9a3-446a-aa54-67787cc7d766", "metadata": {}, "outputs": [], "source": ["new_tags_df3.gift_entity.apply(contains_keywords).value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "8519cdd6-b1bf-4ad0-a5f1-995bab96ea34", "metadata": {}, "outputs": [], "source": ["print(new_tags_df3.shape)\n", "new_tags_df3 = new_tags_df3[~new_tags_df3.gift_entity.apply(contains_keywords)]\n", "print(new_tags_df3.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "15026114-682e-41ce-9e07-78cc74106c56", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "06d77022-710e-47b7-8678-18d660bdcb38", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9d21c0e2-febf-4b98-828d-e24d8bd13143", "metadata": {}, "outputs": [], "source": ["new_tags_df3 = new_tags_df3.groupby(\n", "    [\"product_id\", \"pname\", \"gift_entity_type\"], as_index=False\n", ").agg(\n", "    {\n", "        \"gift_entity\": lambda x: tuple(list(x)),\n", "        \"gift_entity_id\": lambda x: tuple(list(x)),\n", "        # 'gift_entity_id_tuple':lambda x:tuple(list(x)),\n", "        \"sim\": lambda x: tuple(list(x)),\n", "    },\n", "    axis=1,\n", ")\n", "print(new_tags_df3.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "24dc6d0e-9a8e-42b7-86bf-35d8b173aca0", "metadata": {}, "outputs": [], "source": ["new_tags_df3.head(20)"]}, {"cell_type": "code", "execution_count": null, "id": "63995877-838f-4623-95bc-0967d32094ce", "metadata": {}, "outputs": [], "source": ["new_tags_df3.sample(5)"]}, {"cell_type": "code", "execution_count": null, "id": "c77522cf-f8ed-4db7-a6c1-0f9304b6b7d2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c1182bf8-4b54-444f-afcc-5dc3cedf8f05", "metadata": {}, "outputs": [], "source": ["new_tags_df3[new_tags_df3.gift_entity.apply(str).str.contains(\"strauss gift\")]"]}, {"cell_type": "code", "execution_count": null, "id": "e52307e0-8fef-46d0-9a72-3d92a8897f99", "metadata": {}, "outputs": [], "source": ["new_tags_df3[new_tags_df3.gift_entity.apply(str).str.contains(\"e gift card\")].head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "3f63016e-926a-457a-999e-56e2a8612c68", "metadata": {}, "outputs": [], "source": ["new_tags_df3[new_tags_df3.gift_entity.apply(str).str.contains(\"gift pack\")].head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "171d658a-82aa-4698-a464-732e5a7aadf8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6d5499f9-7caf-43e8-887f-6dd6864747fb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fb019d3b-c26e-43b7-aa48-998bafb5c7b1", "metadata": {}, "outputs": [], "source": ["new_tags_df3.to_csv(\"new_gift_tags_all_pdts.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "d50ef47e-7eb6-46c9-bfb4-6f4ad27879a4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f68518ae-b0ca-40ab-b6cf-38f5d696de8a", "metadata": {}, "outputs": [], "source": ["prefix = f\"data-science/ner/gift_assortment_recos_blinkgpt/qtp_based_recos/\" + date + \"/\"\n", "s3_dump_path = f\"s3://{bucket}/{prefix}\"\n", "print(s3_dump_path)"]}, {"cell_type": "code", "execution_count": null, "id": "bcf0e8c3-b786-4096-a480-ca87855f34f5", "metadata": {}, "outputs": [], "source": ["utls.write_df_as_parquet_to_s3(new_tags_df3, s3_dump_path)"]}, {"cell_type": "code", "execution_count": null, "id": "ec4fa23b-506c-41c1-a610-f12b7bf98ffd", "metadata": {}, "outputs": [], "source": ["new_tags_df3.head(20)"]}, {"cell_type": "code", "execution_count": null, "id": "718acfe9-b2b2-4b39-bbcf-0af7b919befd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c8e5404e-f73c-4050-80ad-f84f25fcc64c", "metadata": {"tags": []}, "outputs": [], "source": ["pb.to_sheets(new_tags_df3, \"1LsUtbXqBe1Dy1i_uWgOHLVygv01N3DU5-wucTCf1vEc\", \"QTP_Based_Recos\")"]}, {"cell_type": "code", "execution_count": null, "id": "f4233bcf-ad0b-467b-983d-e92c07f04678", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d716e9bf-f244-43f3-bef4-027fe32108c0", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"gift-assortment-identification\",\n", "    # channel=\"search-data-science\",\n", "    text=f\"\"\"Gift Recommendation data has been pushed to the following Google sheet:\n", "https://docs.google.com/spreadsheets/u/2/d/1LsUtbXqBe1Dy1i_uWgOHLVygv01N3DU5-wucTCf1vEc/edit\n", "\"\"\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "24262cc2-3032-4c86-b4a3-76631dd2c5a7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}