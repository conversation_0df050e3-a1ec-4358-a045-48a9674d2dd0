alert_configs:
  slack:
  - channel: bl-personalisation-dag-failures
dag_name: rfm_customer_segmentation
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: high-mem
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
  retries: 3
  retry_delay_in_seconds: 30
owner:
  email: <EMAIL>
  slack_id: U07AD773UJZ
path: data_science/personalisation/etl/rfm_customer_segmentation
paused: false
pool: data_science_pool
project_name: personalisation
schedule:
  end_date: '2025-09-14T00:00:00'
  interval: 0 18 * * 1,4
  start_date: '2025-05-06T00:00:00'
schedule_type: fixed
sla: 35 minutes
support_files:
- support_utils.py
- Queries/*
tags: []
template_name: notebook
version: 21
