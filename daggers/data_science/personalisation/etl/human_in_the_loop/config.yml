alert_configs:
  slack:
  - channel: bl-personalization-notifications
dag_name: human_in_the_loop
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: low
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03SV2AN82C
path: data_science/personalisation/etl/human_in_the_loop
paused: false
pool: data_science_pool
project_name: personalisation
schedule:
  end_date: '2025-08-02T00:00:00'
  interval: 0 14 1 1 *
  start_date: '2025-01-16T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 3
