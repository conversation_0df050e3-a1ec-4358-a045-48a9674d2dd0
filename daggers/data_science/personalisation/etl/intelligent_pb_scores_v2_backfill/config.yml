alert_configs:
  slack:
  - channel: bl-personalization-notifications
dag_name: intelligent_pb_scores_v2_backfill
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: very-high-mem
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U07MX3HK4M7
path: data_science/personalisation/etl/intelligent_pb_scores_v2_backfill
paused: False
pool: data_science_pool
project_name: personalisation
schedule:
  end_date: '2025-07-10T00:00:00'
  interval: 0 15 15 6 *
  start_date: '2025-06-03T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- kafka_utils.py
tags: []
template_name: notebook
version: 4
