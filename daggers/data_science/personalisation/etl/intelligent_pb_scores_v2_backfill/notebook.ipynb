{"cells": [{"cell_type": "code", "execution_count": null, "id": "4708325a-151f-4377-ab7b-50684711b4d1", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys"]}, {"cell_type": "code", "execution_count": null, "id": "d4219d96-9ea8-412a-936f-0266cab8c32a", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "a450a113-01c5-48c5-83b7-392b30dda9f9", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "00cee4c7-52ab-43a0-ae7d-d0fb33c74360", "metadata": {"tags": []}, "outputs": [], "source": ["!pip install -q kafka-python==2.0.2\n", "!pip install -q scipy==1.8.0\n", "!pip install -q aiohttp==3.8.1\n", "!pip install -q fsspec==2023.1.0\n", "!pip install -q aiobotocore==2.4.2\n", "!pip install -q pymysql==1.0.2\n", "!pip install -q gremlinpython==3.6.2\n", "!pip install -q botocore==1.27.59\n", "!pip install -q progressbar2==4.2.0\n", "!pip install -q backoff==2.2.1\n", "# !pip install -q pandas==1.5.1\n", "!pip install -q pg8000==1.29.4\n", "!pip install -q opensearch-py==2.1.1\n", "!pip install -q boto3==1.24.59\n", "!pip install -q requests-aws4auth==1.2.2\n", "!pip install -q s3transfer==0.6.0\n", "!pip install -q aenum==3.1.11\n", "!pip install -q scramp==1.4.4\n", "!pip install -q python-utils==3.5.2\n", "!pip install -q awswrangler==2.19.0\n", "!pip install -q s3fs==2023.1.0\n", "!pip install -q scikit-learn==1.0.2"]}, {"cell_type": "code", "execution_count": null, "id": "3976096e-8df8-42af-8542-d9ffe2671da3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "078b441f-a951-4c70-bfb3-812481b228a4", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "\n", "pd.set_option(\"display.max_columns\", None)\n", "# pd.set_option(\"display.max_rows\", None)\n", "pd.set_option(\"display.float_format\", lambda x: \"%.5f\" % x)\n", "pd.set_option(\"display.max_colwidth\", None)\n", "import random\n", "import os\n", "import time\n", "import warnings\n", "import gc\n", "import json\n", "\n", "from sklearn.preprocessing import MinMaxScaler\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "import pencilbox as pb\n", "\n", "# from kafka import KafkaProducer\n", "# from utils import *\n", "from kafka_utils import *\n", "\n", "t_conn = pb.get_connection(\"[Warehouse] Trino\")\n", "use_sheet = True"]}, {"cell_type": "code", "execution_count": null, "id": "e4bfbb73-18a9-4cfe-acc1-8ca01b00ab2e", "metadata": {}, "outputs": [], "source": ["def run_query_with_retries(query, con=pb.get_connection(\"[Warehouse] Trino\"), retries=3):\n", "    count = 1\n", "    while count <= retries:\n", "        try:\n", "            df = pd.read_sql_query(query, con)\n", "            break\n", "        except Exception as e:\n", "            print(e)\n", "            count += 1\n", "    return df\n", "\n", "\n", "def scale(user_pid_df, g_cols, a_col, scaled_col, range_min=0, range_max=1):\n", "    group_stats = user_pid_df.groupby(g_cols)[a_col].agg([\"min\", \"max\"]).reset_index()\n", "    group_stats.rename(columns={\"min\": \"min_val\", \"max\": \"max_val\"}, inplace=True)\n", "\n", "    user_pid_df = user_pid_df.merge(group_stats, on=g_cols)\n", "\n", "    # range_min, range_max = 0, 1\n", "    scale = range_max - range_min\n", "    user_pid_df[scaled_col] = (\n", "        (user_pid_df[a_col] - user_pid_df[\"min_val\"])\n", "        / (user_pid_df[\"max_val\"] - user_pid_df[\"min_val\"])\n", "    ).clip(lower=0) * scale + range_min\n", "\n", "    user_pid_df.loc[user_pid_df[\"min_val\"] == user_pid_df[\"max_val\"], scaled_col] = range_max\n", "\n", "    user_pid_df.drop(columns=[\"min_val\", \"max_val\"], inplace=True)\n", "\n", "    return user_pid_df"]}, {"cell_type": "code", "execution_count": null, "id": "dd649c37-83e1-405a-bbc2-c5d067c1168c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e889d07d-5a99-475b-9e9c-532af65f791a", "metadata": {}, "outputs": [], "source": ["prod_data = run_query_with_retries(\n", "    f\"\"\"\n", "select product_id,\n", "product_name,\n", "product_type,\n", "product_type_id,\n", "l0_category,\n", "l0_category_id,\n", "l1_category,\n", "l1_category_id,\n", "l2_category\n", "from dwh.dim_product\n", "where is_current and is_product_enabled\n", "group by 1,2,3,4,5,6,7,8,9\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3f552674-56ab-442b-95e3-bcb4f20c08ce", "metadata": {}, "outputs": [], "source": ["active_ptypes = prod_data[[\"product_type\"]].drop_duplicates().reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "e520762d-7b31-4ad8-b730-ff16f8cd653b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7afe929f-7280-4f67-95be-4307c9b7cd6a", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "with pid_gid_map as (\n", "select gpm.product_id,\n", "gpm.group_id \n", "from lake_cms.gr_group_product_mapping gpm\n", "join cms.gr_group gg on gg.id = gpm.group_id and gg.is_active\n", "where gpm.enabled_flag=True and gpm.lake_active_record=true \n", "group by 1,2\n", "),\n", "\n", "cust_ptype_counts as(\n", "select dim_customer_key,\n", "product_type,\n", "-- count(distinct cart_id) as user_ptype_cart_count,\n", "-- count(distinct group_id) as user_ptype_group_count,\n", "count(distinct cart_id)*1.0000 / count(distinct group_id) as user_ptype_gid_cart_ratio\n", "from dwh.fact_sales_order_item_details fsoid\n", "join dwh.dim_product dp on dp.product_id = fsoid.product_id and is_current and is_product_enabled\n", "join pid_gid_map gp on gp.product_id = fsoid.product_id \n", "where order_create_dt_ist > current_date - INTERVAL '180' Day\n", "and city_name = 'Bengaluru'\n", "group by 1,2\n", "having count(distinct cart_id) >= 2\n", ")\n", "\n", "-- select * from cust_ptype_counts\n", "\n", "select \n", "product_type,\n", "CAST(AVG(user_ptype_gid_cart_ratio) as REAL) as ptype_gid_cart_ratio\n", "from \n", "cust_ptype_counts\n", "group by 1\n", "\n", "\n", "\"\"\"\n", "agg_df = run_query_with_retries(query)\n", "agg_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "a3040620-5479-434c-a7f1-e7d20437e2d1", "metadata": {}, "outputs": [], "source": ["agg_df = active_ptypes.merge(agg_df, on=[\"product_type\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "11b57a28-344b-4350-8337-91f608a6f370", "metadata": {}, "outputs": [], "source": ["# agg_df[\"ptype_cust_count\"].fillna(0, inplace=True)\n", "agg_df[\"ptype_gid_cart_ratio\"].fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "c242b5ff-59d9-4888-b400-bc70696dd01e", "metadata": {}, "outputs": [], "source": ["agg_df.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "45aecaed-1760-4f5f-9c46-4fe7046dd082", "metadata": {}, "outputs": [], "source": ["agg_df.sort_values(\"ptype_gid_cart_ratio\", ascending=True, inplace=True)\n", "agg_df = agg_df.reset_index(drop=True)\n", "agg_df[\"ptype_gid_rank\"] = agg_df.index\n", "\n", "scaler = MinMaxScaler(feature_range=(0, 1))\n", "agg_df[[\"ptype_gid_cart_ratio_scale\"]] = scaler.fit_transform(agg_df[[\"ptype_gid_cart_ratio\"]])\n", "agg_df[[\"ptype_gid_rank_scale\"]] = scaler.fit_transform(agg_df[[\"ptype_gid_rank\"]])"]}, {"cell_type": "code", "execution_count": null, "id": "a2892b34-110d-46c0-9c51-e26a97babacd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f93311b4-cef4-42b6-951e-06386f05e74e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "925b82cb-4b15-4ab1-936b-396f5e4d8859", "metadata": {}, "outputs": [], "source": ["def get_pb_data(start, end, flag):\n", "    if flag == \"yes\":\n", "        users = list(\n", "            pb.from_sheets(\"1ztI7Txkf8jRsgnOn59gPEbrJGyEQF03XbhRCeKqdB88\", \"user IDs\")\n", "            .user_id.astype(int)\n", "            .unique()\n", "        )\n", "    else:\n", "        users = [-1, -2]\n", "    query = f\"\"\"\n", "with prev_day_transacting_users as(\n", "    select\n", "        fsod.dim_customer_key\n", "        from dwh.fact_sales_order_details fsod\n", "        where \n", "        ORDER_CREATE_DT_IST >= current_date - INTERVAL '365' DAY\n", "        and ((dim_customer_key %% 100 >= {start}\n", "        and dim_customer_key %% 100 <= {end}) or (dim_customer_key in {*users,}))\n", "        group by 1    \n", "    ),\n", "\n", "cust_max_date as (\n", "    select fsod.dim_customer_key,\n", "    max(cart_rank) as cust_max_cr,\n", "    max(order_create_dt_ist) as cust_max_date\n", "    from dwh.fact_sales_order_details fsod\n", "    join prev_day_transacting_users pd on pd.dim_customer_key = fsod.dim_customer_key\n", "    where \n", "    order_create_dt_ist >= current_date - INTERVAL '365' DAY\n", "    group by 1\n", "    ),\n", "\n", "cust_pid_max_date as(\n", "    select od.dim_customer_key,\n", "           oid.product_id,\n", "           max(od.order_create_dt_ist) as cust_max_pid_purchase,\n", "           max(od.cart_rank) as cust_max_pid_cr\n", "    from dwh.fact_sales_order_item_details oid\n", "    join dwh.fact_sales_order_details od on od.order_id = oid.order_id \n", "    join prev_day_transacting_users pd on pd.dim_customer_key = od.dim_customer_key\n", "    where oid.order_create_dt_ist >= current_date - INTERVAL '365' DAY\n", "    and od.order_create_dt_ist >= current_date - INTERVAL '365' DAY\n", "    group by 1,2\n", "    ),\n", "\n", "cms_pid_group_mapping as (select dp.product_id, group_id\n", "    from dwh.dim_product dp\n", "    join cms.gr_group_product_mapping gpm on gpm.product_id=dp.product_id\n", "    join cms.gr_group grg on grg.id=gpm.group_id\n", "    where dp.is_current and\n", "          dp.is_product_enabled and\n", "          grg.is_active and\n", "          grg.lake_active_record=true and\n", "          gpm.enabled_flag=true\n", "          and gpm.lake_active_record=true\n", "    group by 1,2),\n", "\n", "final_view as (select \n", "           od.dim_customer_key,\n", "           oid.product_id,\n", "           dp.l0_category_id,\n", "           dp.l1_category_id,\n", "           dp.product_type_id,\n", "           oid.cart_id,\n", "           od.cart_rank,\n", "           oid.order_create_dt_ist,\n", "           oid.product_quantity,\n", "           b.cust_max_date,\n", "           b.cust_max_cr,\n", "           cm.group_id,\n", "           cpmd.cust_max_pid_purchase,\n", "           cpmd.cust_max_pid_cr\n", "    from dwh.fact_sales_order_item_details oid\n", "    join prev_day_transacting_users pdtu on pdtu.dim_customer_key = oid.dim_customer_key\n", "    join dwh.fact_sales_order_details od on od.order_id = oid.order_id\n", "    join cust_max_date b on oid.dim_customer_key = b.dim_customer_key\n", "    join dwh.dim_product dp on dp.product_id = oid.product_id and is_product_enabled and is_current\n", "    join cms_pid_group_mapping cm on cm.product_id = dp.product_id\n", "    join cust_pid_max_date cpmd on cpmd.dim_customer_key = od.dim_customer_key and cpmd.product_id = oid.product_id\n", "    where \n", "    od.order_create_dt_ist >= current_date - INTERVAL '365' DAY\n", "    and oid.order_create_dt_ist >= current_date - INTERVAL '365' DAY\n", "    and oid.order_type = 'RetailForwardOrder' and \n", "    ((od.is_archived_order = False) or (od.is_archived_order is null))\n", "    and l0_category not in ('Specials')\n", "                      and product_type_id NOT IN (\n", "                            11780, -- share deal\n", "                            459, -- <PERSON><PERSON>\n", "                            11939, -- <PERSON><PERSON>\n", "                            11778, -- Free\n", "                            11791, -- G<PERSON>fers Go Gift Items\n", "                            11927, -- Special Deal\n", "                            11929, -- VIP Deals\n", "                            11930, -- <PERSON><PERSON>s\n", "                            11932, -- What<PERSON>pp Deals\n", "                            11936, -- List Deals,\n", "                            12184-- Print as a service\n", "                      )\n", "    group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14),\n", "    \n", "fin as (select \n", "    dim_customer_key,\n", "    product_id,\n", "    group_id,\n", "    cust_max_pid_purchase as last_bought_date,\n", "    count(distinct cart_id) as orders_count\n", "from final_view   \n", "group by 1,2,3,4),\n", "\n", "fin2 as (select\n", "    dim_customer_key,\n", "    group_id,\n", "    round(sum(pow(0.95,(cust_max_cr - cart_rank)*100/(cust_max_cr))),5) as score\n", "from final_view\n", "group by 1,2)\n", "\n", "select \n", "    fin.dim_customer_key,\n", "    product_id,\n", "    last_bought_date,\n", "    orders_count,\n", "    score\n", "from fin \n", "join fin2 on fin2.dim_customer_key = fin.dim_customer_key and fin.group_id = fin2.group_id\n", "\"\"\"\n", "    # df = pd.read_sql_query(query, con=pb.get_connection(\"[Warehouse] Trino\"))\n", "    return query"]}, {"cell_type": "code", "execution_count": null, "id": "13690be9-8dec-434a-98f3-dc3a0d0bcac7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f9b3990d-b318-4f0a-8f2d-134f474d27e2", "metadata": {"tags": []}, "outputs": [], "source": ["if use_sheet:\n", "    sheet_df = pb.from_sheets(\"18A2gdrCSyagsE9HpbfNpSl9_ubw1gUNCc6jT8QuGXhM\", \"Sheet1\")\n", "else:\n", "    sheet_df = pd.read_csv(\"pb_v2_backfill - Sheet1.csv\")\n", "\n", "sheet_df[\"start\"] = sheet_df[\"start\"].astype(\"int\")\n", "sheet_df[\"end\"] = sheet_df[\"end\"].astype(\"int\")\n", "sheet_df[\"step\"] = sheet_df[\"step\"].astype(\"int\")\n", "\n", "mod_start = sheet_df[\"start\"].values[0]\n", "mod_end = sheet_df[\"end\"].values[0]\n", "step = sheet_df[\"step\"].values[0]\n", "flag = sheet_df[\"flag\"].values[0]\n", "mod_start, mod_end, step"]}, {"cell_type": "code", "execution_count": null, "id": "cc7cf898-5477-4246-8701-1cf92a3576e1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2db52aa9-b60a-4451-9979-4dd9253b70ea", "metadata": {}, "outputs": [], "source": ["def rescale_scores_with_merge(\n", "    user_pid_df, user_col=\"dim_customer_key\", score_col=\"intelligent_pb_score_v2\"\n", "):\n", "    \"\"\"\n", "    In-place score rescaling:\n", "      - If _merge == 'both' → score ∈ (-1, 0)\n", "      - If _merge == 'left_only' → score ∈ [0, 1]\n", "    Keeps relative order within each group.\n", "    Adds/overwrites column 'rescaled_score' in the input user_pid_df.\n", "    \"\"\"\n", "\n", "    # 1. Precompute group size and rank\n", "    is_s = user_pid_df[\"_merge\"].eq(\"both\")  # Boolean Series\n", "    group_keys = [user_col, is_s]\n", "\n", "    rank = user_pid_df.groupby(group_keys)[score_col].rank(method=\"first\", ascending=False)\n", "    size = user_pid_df.groupby(group_keys)[score_col].transform(\"count\")\n", "\n", "    # 2. Prepare rescaled_score with default value\n", "    rescaled = pd.Series(0.0, index=user_pid_df.index)\n", "\n", "    # 3. S group: strictly negative scores in (−1, 0)\n", "    rescaled[is_s] = -rank[is_s] / size[is_s]\n", "\n", "    # 4. Non-S group: scores in [0, 1]\n", "    not_s = ~is_s\n", "    one_item_mask = (size == 1) & not_s\n", "    multi_item_mask = (size > 1) & not_s\n", "\n", "    rescaled[one_item_mask] = 1.0\n", "    rescaled[multi_item_mask] = (size[multi_item_mask] - rank[multi_item_mask]) / (\n", "        size[multi_item_mask] - 1\n", "    )\n", "\n", "    # 5. Assign final result\n", "    user_pid_df[\"rescaled_score\"] = rescaled"]}, {"cell_type": "code", "execution_count": null, "id": "657ec73d-2265-40f6-8fc3-6a9e5a2764b8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "455c8f3f-1f38-4d3a-baf2-9da9b04cc1d5", "metadata": {}, "outputs": [], "source": ["# step = 5\n", "for mod in range(mod_start, mod_end + 1, step):\n", "    print(mod)\n", "    start = mod\n", "    end = mod + step - 1\n", "    if end >= mod_end:\n", "        end = mod_end\n", "    print(start, end)\n", "\n", "    user_pid_df = run_query_with_retries(get_pb_data(start, end, flag))\n", "\n", "    print(user_pid_df.dim_customer_key.nunique())\n", "\n", "    user_pid_df[\"product_id\"] = user_pid_df.product_id.astype(int)\n", "    user_pid_df[\"orders_count\"] = user_pid_df.orders_count.astype(int)\n", "    user_pid_df[\"last_bought_date\"] = user_pid_df.last_bought_date.astype(str)\n", "    user_pid_df[\"score\"] = user_pid_df.score.astype(\"float\")\n", "\n", "    user_pid_df = user_pid_df.merge(prod_data, on=[\"product_id\"], how=\"left\")\n", "    print(1)\n", "\n", "    user_pid_df = scale(\n", "        user_pid_df,\n", "        [\"dim_customer_key\", \"product_type\"],\n", "        \"score\",\n", "        \"user_ptype_scaled_score\",\n", "        range_min=0,\n", "        range_max=1,\n", "    )\n", "    user_pid_df = scale(\n", "        user_pid_df,\n", "        [\"dim_customer_key\", \"l1_category\"],\n", "        \"score\",\n", "        \"user_l1_scaled_score\",\n", "        range_min=0,\n", "        range_max=1,\n", "    )\n", "    user_pid_df = scale(\n", "        user_pid_df, [\"dim_customer_key\"], \"score\", \"user_scaled_score\", range_min=0, range_max=1\n", "    )\n", "\n", "    print(2)\n", "\n", "    user_pid_df.sort_values(\"user_ptype_scaled_score\", ascending=True, inplace=True)\n", "    user_pid_df.reset_index(inplace=True, drop=True)\n", "    user_pid_df[\"user_ptype_scaled_score_rank\"] = (\n", "        user_pid_df.groupby([\"dim_customer_key\", \"product_type\"]).cumcount() + 1\n", "    )\n", "\n", "    user_pid_df.sort_values(\"user_l1_scaled_score\", ascending=True, inplace=True)\n", "    user_pid_df.reset_index(inplace=True, drop=True)\n", "    user_pid_df[\"user_l1_scaled_score_rank\"] = (\n", "        user_pid_df.groupby([\"dim_customer_key\", \"l1_category\"]).cumcount() + 1\n", "    )\n", "\n", "    user_pid_df.sort_values(\"user_scaled_score\", ascending=True, inplace=True)\n", "    user_pid_df.reset_index(inplace=True, drop=True)\n", "    # user_pid_df['user_scaled_score_rank'] = user_pid_df.index + 1\n", "    user_pid_df[\"user_scaled_score_rank\"] = user_pid_df.groupby([\"dim_customer_key\"]).cumcount() + 1\n", "\n", "    user_pid_df[\"user_scaled_score_rank_\"] = (\n", "        user_pid_df[\"user_scaled_score_rank\"] * 0.4\n", "        + user_pid_df[\"user_l1_scaled_score_rank\"] * 0.3\n", "        + user_pid_df[\"user_ptype_scaled_score_rank\"] * 0.3\n", "    )\n", "\n", "    print(3)\n", "\n", "    user_pid_df = scale(\n", "        user_pid_df,\n", "        g_cols=[\"dim_customer_key\"],\n", "        a_col=\"user_scaled_score_rank_\",\n", "        scaled_col=\"user_scaled_score_rank_\",\n", "        range_min=0,\n", "        range_max=1,\n", "    )\n", "    print(4)\n", "    user_pid_df = user_pid_df.merge(agg_df, on=[\"product_type\"], how=\"left\")\n", "    print(5)\n", "\n", "    user_pid_df[\"pb_stickiness_contri_factor\"] = -1\n", "\n", "    user_pid_df.loc[user_pid_df.ptype_gid_cart_ratio <= 1.6, \"pb_stickiness_contri_factor\"] = 0.1\n", "    user_pid_df.loc[\n", "        user_pid_df.ptype_gid_cart_ratio.between(1.6, 2, inclusive=\"right\"),\n", "        \"pb_stickiness_contri_factor\",\n", "    ] = 0.2\n", "    user_pid_df.loc[\n", "        user_pid_df.ptype_gid_cart_ratio.between(2, 2.5, inclusive=\"right\"),\n", "        \"pb_stickiness_contri_factor\",\n", "    ] = 0.4\n", "    user_pid_df.loc[user_pid_df.ptype_gid_cart_ratio >= 2.5, \"pb_stickiness_contri_factor\"] = 0.6\n", "\n", "    user_pid_df[\"user_pid_score_rank\"] = (\n", "        user_pid_df[\"user_scaled_score_rank_\"] * (1 - user_pid_df[\"pb_stickiness_contri_factor\"])\n", "    ) + (user_pid_df[\"ptype_gid_rank_scale\"] * user_pid_df[\"pb_stickiness_contri_factor\"])\n", "\n", "    user_pid_df = scale(\n", "        user_pid_df,\n", "        g_cols=[\"dim_customer_key\"],\n", "        a_col=\"user_pid_score_rank\",\n", "        scaled_col=\"user_pid_scaled_score_rank\",\n", "        range_min=-1,\n", "        range_max=1,\n", "    )\n", "    print(6)\n", "\n", "    deboost_ptypes = pb.from_sheets(\"1DANnQ10CAX6x8O44N3IMItEz5XLxRrWojPCAn3fVjp4\", \"Sheet1\")\n", "    deboost_ptypes[\"product_type_id\"] = deboost_ptypes[\"product_type_id\"].astype(int)\n", "    deboost_ptypes = deboost_ptypes[[\"product_type\", \"product_type_id\"]]\n", "\n", "    deboost_ptypes = deboost_ptypes.merge(active_ptypes, on=\"product_type\", how=\"inner\")\n", "\n", "    user_pid_df = user_pid_df[\n", "        [\n", "            \"dim_customer_key\",\n", "            \"product_id\",\n", "            \"last_bought_date\",\n", "            \"orders_count\",\n", "            \"score\",\n", "            \"product_type_id\",\n", "            \"user_pid_scaled_score_rank\",\n", "        ]\n", "    ]\n", "    user_pid_df = user_pid_df.merge(\n", "        deboost_ptypes, on=\"product_type_id\", how=\"left\", indicator=True\n", "    )\n", "\n", "    user_pid_df.rename(\n", "        columns={\"user_pid_scaled_score_rank\": \"intelligent_pb_score_v2\"}, inplace=True\n", "    )\n", "    rescale_scores_with_merge(user_pid_df)\n", "    user_pid_df = user_pid_df[\n", "        [\n", "            \"dim_customer_key\",\n", "            \"product_id\",\n", "            \"last_bought_date\",\n", "            \"orders_count\",\n", "            \"score\",\n", "            \"rescaled_score\",\n", "        ]\n", "    ]\n", "\n", "    user_pid_df.columns = [\n", "        \"dim_customer_key\",\n", "        \"product_id\",\n", "        \"last_bought_date\",\n", "        \"orders_count\",\n", "        \"intelligent_pb_score_v1\",\n", "        \"intelligent_pb_score_v2\",\n", "    ]\n", "\n", "    user_pid_df[\"intelligent_pb_score_v2\"] = user_pid_df.intelligent_pb_score_v2.round(5)\n", "    print(7)\n", "\n", "    user_pid_df[\"product_information\"] = user_pid_df[\n", "        [\n", "            \"product_id\",\n", "            \"last_bought_date\",\n", "            \"orders_count\",\n", "            \"intelligent_pb_score_v1\",\n", "            \"intelligent_pb_score_v2\",\n", "        ]\n", "    ].to_dict(\"records\")\n", "    print(8)\n", "\n", "    record_list = (\n", "        user_pid_df.groupby(\"dim_customer_key\")[\"product_information\"].apply(list).reset_index()\n", "    )\n", "    print(9)\n", "\n", "    user_pid_df = []\n", "    del user_pid_df\n", "    gc.collect()\n", "\n", "    kafka_records_dict = record_list.to_dict(\"records\")\n", "    print(len(kafka_records_dict))\n", "    print(10)\n", "\n", "    record_list = []\n", "    del record_list\n", "    gc.collect()\n", "\n", "    # Push to <PERSON><PERSON><PERSON> (Feature Store)\n", "    entity_column = \"dim_customer_key\"\n", "    entity_name = \"user\"\n", "    context = \"intelligent_pb_boosting_2_0_0\"\n", "    ctx_value_col = \"product_information\"\n", "\n", "    push_to_kafka(\n", "        entities=[f\"{entity_name}:{i[entity_column]}\" for i in kafka_records_dict],\n", "        context=context,\n", "        ctx_properties=[{\"ctx_value\": json.dumps(i[ctx_value_col])} for i in kafka_records_dict],\n", "        dry_run=False,\n", "    )\n", "    kafka_records_dict = []\n", "    del kafka_records_dict\n", "    gc.collect()\n", "\n", "    msg = \"Intelligent PB boosting V2 pushed for mod: \" + str(start) + \" \" + str(end)\n", "    pb.send_slack_message(\"bl-wh-test-ch\", msg)"]}, {"cell_type": "code", "execution_count": null, "id": "4837bfbb-3a3f-4cb2-9f5f-c4d8d477d894", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}