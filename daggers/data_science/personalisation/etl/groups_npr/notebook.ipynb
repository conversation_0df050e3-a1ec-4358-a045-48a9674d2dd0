{"cells": [{"cell_type": "code", "execution_count": null, "id": "26fb6e20-24ac-47c9-962a-308b34c153f5", "metadata": {}, "outputs": [], "source": ["import os, sys"]}, {"cell_type": "code", "execution_count": null, "id": "b567ec4d-aacb-4efa-90ed-456549dea963", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "4526b8c2-4c8f-4531-bdde-594dc51b4eda", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "7662e5c1-3fb6-47ea-bd7f-0ffd03fbba19", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "634539f0-c454-42ac-a374-3bed74f38877", "metadata": {}, "outputs": [], "source": ["!pip install -q rapidfuzz"]}, {"cell_type": "code", "execution_count": null, "id": "b815f76e-c65e-4fa5-852b-4768edaf8cae", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "from rapidfuzz import fuzz\n", "import ast\n", "import db_utils\n", "import time\n", "import json\n", "from datetime import datetime, timedelta\n", "\n", "\n", "pd.set_option(\"display.width\", None)\n", "pd.set_option(\"max_colwidth\", -1)\n", "pd.set_option(\"display.expand_frame_repr\", False)\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "use_sheet = True"]}, {"cell_type": "code", "execution_count": null, "id": "8f1378c1-440d-4033-b9b8-b5caba278f81", "metadata": {}, "outputs": [], "source": ["def run_query_with_retries(query, con=pb.get_connection(\"[Warehouse] Trino\"), retries=3):\n", "    count = 1\n", "    while count <= retries:\n", "        try:\n", "            df = pd.read_sql_query(query, con)\n", "            break\n", "        except Exception as e:\n", "            print(e)\n", "            count += 1\n", "    return df\n", "\n", "\n", "def read_sheets_with_retries(sheet_id, sheet_name, max_tries=3):\n", "    for attempt in range(max_tries):\n", "        print(f\"Trying to read attempt: {attempt + 1}...\")\n", "        try:\n", "            return pb.from_sheets(sheet_id, sheet_name)\n", "        except Exception as e:\n", "            print(f\"Error occurred: {str(e)}\")\n", "            if hasattr(e, \"response\") and hasattr(e.response, \"json\"):\n", "                try:\n", "                    exception_code = (\n", "                        e.response.json().get(\"error\", {}).get(\"code\", \"Unknown error code\")\n", "                    )\n", "                    print(f\"API Error Code: {exception_code}\")\n", "                except Exception as inner_e:\n", "                    print(f\"Failed to extract error code: {str(inner_e)}\")\n", "            time.sleep(60)"]}, {"cell_type": "code", "execution_count": null, "id": "87d35f89-5d7c-4248-ba1f-7aaaaa224961", "metadata": {}, "outputs": [], "source": ["def get_blacklist_data(widget, from_sheet=True):\n", "\n", "    if from_sheet:\n", "        blacklist_df = read_sheets_with_retries(\n", "            \"17Q4P7HGhYLMnW4AqRWL4Jh_a5nT5DCeuK176CM2jgoE\", \"Blacklist_v2\"\n", "        )\n", "    else:\n", "        blacklist_df = pd.read_csv(\"BLACKLIST - Blacklist_v2.csv\")\n", "\n", "    blacklist_df = blacklist_df.query(\"widget in ('Global', @widget)\")\n", "\n", "    exclusion_all_l0 = list(\n", "        set(ast.literal_eval(\",\".join(list(blacklist_df.query(\"level == 'all'\").l0.values)) + \",\"))\n", "    )\n", "    exclusion_all_l1 = list(\n", "        set(ast.literal_eval(\",\".join(list(blacklist_df.query(\"level == 'all'\").l1.values)) + \",\"))\n", "    )\n", "    exclusion_all_ptype = list(\n", "        set(\n", "            ast.literal_eval(\n", "                \",\".join(list(blacklist_df.query(\"level == 'all'\").ptype.values)) + \",\"\n", "            )\n", "        )\n", "    )\n", "    exclusion_all_pid = list(\n", "        set(ast.literal_eval(\",\".join(list(blacklist_df.query(\"level == 'all'\").pid.values)) + \",\"))\n", "    )\n", "\n", "    exclusion_antecedent_l0 = list(\n", "        set(\n", "            ast.literal_eval(\n", "                \",\".join(list(blacklist_df.query(\"level == 'antecedent'\").l0.values)) + \",\"\n", "            )\n", "        )\n", "    )\n", "    exclusion_antecedent_l1 = list(\n", "        set(\n", "            ast.literal_eval(\n", "                \",\".join(list(blacklist_df.query(\"level == 'antecedent'\").l1.values)) + \",\"\n", "            )\n", "        )\n", "    )\n", "    exclusion_antecedent_ptype = list(\n", "        set(\n", "            ast.literal_eval(\n", "                \",\".join(list(blacklist_df.query(\"level == 'antecedent'\").ptype.values)) + \",\"\n", "            )\n", "        )\n", "    )\n", "    exclusion_antecedent_pid = list(\n", "        set(\n", "            ast.literal_eval(\n", "                \",\".join(list(blacklist_df.query(\"level == 'antecedent'\").pid.values)) + \",\"\n", "            )\n", "        )\n", "    )\n", "\n", "    exclusion_consequent_l0 = list(\n", "        set(\n", "            ast.literal_eval(\n", "                \",\".join(list(blacklist_df.query(\"level == 'consequent'\").l0.values)) + \",\"\n", "            )\n", "        )\n", "    )\n", "    exclusion_consequent_l1 = list(\n", "        set(\n", "            ast.literal_eval(\n", "                \",\".join(list(blacklist_df.query(\"level == 'consequent'\").l1.values)) + \",\"\n", "            )\n", "        )\n", "    )\n", "    exclusion_consequent_ptype = list(\n", "        set(\n", "            ast.literal_eval(\n", "                \",\".join(list(blacklist_df.query(\"level == 'consequent'\").ptype.values)) + \",\"\n", "            )\n", "        )\n", "    )\n", "    exclusion_consequent_pid = list(\n", "        set(\n", "            ast.literal_eval(\n", "                \",\".join(list(blacklist_df.query(\"level == 'consequent'\").pid.values)) + \",\"\n", "            )\n", "        )\n", "    )\n", "\n", "    return (\n", "        exclusion_all_l0,\n", "        exclusion_all_l1,\n", "        exclusion_all_ptype,\n", "        exclusion_all_pid,\n", "        exclusion_antecedent_l0,\n", "        exclusion_antecedent_l1,\n", "        exclusion_antecedent_ptype,\n", "        exclusion_antecedent_pid,\n", "        exclusion_consequent_l0,\n", "        exclusion_consequent_l1,\n", "        exclusion_consequent_ptype,\n", "        exclusion_consequent_pid,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "d14db955-21c9-43f6-859f-4fd9d95c2718", "metadata": {}, "outputs": [], "source": ["def get_festival_df(from_sheet=True):\n", "    if from_sheet:\n", "        festival_df = read_sheets_with_retries(\n", "            \"1sxAcHxNyTdNozzl8wFC4PtdO56xFce0Nkpc0blATFpo\", \"Festival Dates\"\n", "        )\n", "    else:\n", "        festival_df = pd.read_csv(\"Festive_prod_v4 - Festival Dates.csv\")\n", "\n", "    date = pd.to_datetime(datetime.now() + timedelta(days=1)).date()\n", "    print(\"current_date: \", date)\n", "\n", "    festival_df[\"start\"] = pd.to_datetime(festival_df[\"start\"])\n", "    festival_df[\"end\"] = pd.to_datetime(festival_df[\"end\"])\n", "    festival_df[\"curr_festival\"] = festival_df.apply(\n", "        lambda row: 1 if date >= row[\"start\"] and date <= row[\"end\"] else 0, axis=1\n", "    )\n", "\n", "    gifting_df = festival_df.query(\"festival == 'Gifting'\").reset_index(drop=True)\n", "    fnv_df = festival_df.query(\"festival == 'FnV'\").reset_index(drop=True)\n", "    festival_df = festival_df.query(\"festival != 'Gifting' and festival != 'FnV'\").reset_index(\n", "        drop=True\n", "    )\n", "\n", "    return gifting_df, festival_df, fnv_df"]}, {"cell_type": "code", "execution_count": null, "id": "cb07e842-3653-413b-815d-5f55cd227f94", "metadata": {}, "outputs": [], "source": ["def get_complimentary_data(\n", "    festival_df, gifting_df, fnv_df, festive=True, gifting=True, fnv=True, from_sheet=True\n", "):\n", "\n", "    if festive:\n", "        try:\n", "            festival = festival_df.query(\"curr_festival == 1\")[\"festival\"].values[0]\n", "        except:\n", "            print(\"No festival\")\n", "            return pd.DataFrame()\n", "        if from_sheet:\n", "            ptype_data = read_sheets_with_retries(\n", "                \"1sxAcHxNyTdNozzl8wFC4PtdO56xFce0Nkpc0blATFpo\", festival\n", "            )\n", "        else:\n", "            ptype_data = pd.read_csv(\"Festive_prod_v4 - \" + festival + \".csv\")\n", "\n", "    elif gifting:\n", "        try:\n", "            gifting = gifting_df.query(\"curr_gifting == 1\")[\"gifting\"].values[0]\n", "        except:\n", "            print(\"Gifting not active\")\n", "            return pd.DataFrame()\n", "        if from_sheet:\n", "            ptype_data = read_sheets_with_retries(\n", "                \"1sxAcHxNyTdNozzl8wFC4PtdO56xFce0Nkpc0blATFpo\", gifting\n", "            )\n", "        else:\n", "            ptype_data = pd.read_csv(\"Festive_prod_v4 - \" + gifting + \".csv\")\n", "\n", "    elif fnv:\n", "        try:\n", "            fn = fnv_df.query(\"curr_festival == 1\")[\"festival\"].values[0]\n", "        except:\n", "            print(\"Fnv not active\")\n", "            return pd.DataFrame()\n", "        if from_sheet:\n", "            ptype_data = read_sheets_with_retries(\n", "                \"1sxAcHxNyTdNozzl8wFC4PtdO56xFce0Nkpc0blATFpo\", fn\n", "            )\n", "        else:\n", "            ptype_data = pd.read_csv(\"Festive_prod_v4 - \" + fn + \".csv\")\n", "\n", "    else:\n", "        if from_sheet:\n", "            ptype_data = read_sheets_with_retries(\n", "                \"1-TAGan8KKBxBuR_lkDXZ6xxAzRqAoPpj2uCNnc_5Efw\", \"prod_v5\"\n", "            )\n", "        else:\n", "            ptype_data = pd.read_csv(\"Complementary Ptypes_ Prod - prod_v5.csv\")\n", "\n", "    ptype_data[\"product_type\"] = ptype_data[\"product_type\"].apply(lambda x: x.split(\"||\")[0])\n", "\n", "    ptype_data = ptype_data.melt(\n", "        id_vars=\"product_type\",\n", "        value_vars=ptype_data.drop(columns=[\"product_type\"]).columns.tolist(),\n", "    ).reset_index(drop=True)\n", "\n", "    ptype_data.dropna(subset=[\"value\"], inplace=True)\n", "    ptype_data = ptype_data[ptype_data[\"value\"] != \"\"]\n", "\n", "    ptype_data[\"cons_ptype_rank\"] = ptype_data.groupby([\"product_type\"]).cumcount()\n", "\n", "    ptype_data.sort_values([\"product_type\", \"cons_ptype_rank\"], inplace=True)\n", "    ptype_data.reset_index(drop=True, inplace=True)\n", "    ptype_data.rename(columns={\"product_type\": \"ptype\"}, inplace=True)\n", "    ptype_data = ptype_data.drop_duplicates(subset=[\"ptype\", \"value\"])\n", "\n", "    ptype_data.sort_values([\"ptype\", \"cons_ptype_rank\"], inplace=True)\n", "    ptype_data.reset_index(drop=True, inplace=True)\n", "    ptype_data[\"value\"] = ptype_data[\"value\"].apply(lambda x: x.split(\"||\")[0])\n", "\n", "    return ptype_data"]}, {"cell_type": "code", "execution_count": null, "id": "21855d59-bcbf-4cd1-b2ad-967a3f2eab8f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "beac20cf-d9d9-4f25-83b8-6ae76732ea2d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "33349f0a-2e44-4f65-952e-2bf43a78c6ca", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "with max_dt as (\n", "    select metadata_id, metadata_type, max(snapshot_ist) max_snapshot_ist \n", "    from search_etls.meta_entity_product_relationship \n", "    where \n", "    snapshot_ist is not null \n", "    and metadata_type in ('KEYTERM','COMPOSITE_KEYWORD')\n", "    group by 1,2\n", "), \n", "pid_sales as(\n", "select product_id, count(distinct cart_id) as cart_count\n", "from dwh.fact_sales_order_item_details\n", "where order_create_dt_ist > current_date - INTERVAL '30' DAY\n", "and city_name='Bengaluru'\n", "group by 1\n", ")\n", "select \n", "p.metadata_id, \n", "p.metadata_name,\n", "p.metadata_type,\n", "p.tagged_product_id as product_id,\n", "product_name,\n", "product_type,\n", "product_type_id,\n", "l0_category,\n", "l0_category_id,\n", "l1_category,\n", "l1_category_id,\n", "l2_category,\n", "l2_category_id, \n", "cart_count\n", "from \n", "    search_etls.meta_entity_product_relationship p\n", "    join max_dt m on m.metadata_id = p.metadata_id and m.max_snapshot_ist = p.snapshot_ist and m.metadata_type = p.metadata_type\n", "    join dwh.dim_product dp on dp.product_id = p.tagged_product_id and dp.is_current and dp.is_product_enabled\n", "    join pid_sales ps on p.tagged_product_id = ps.product_id\n", "where p.snapshot_ist is not null\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14\n", "\"\"\"\n", "product_data = run_query_with_retries(query)"]}, {"cell_type": "code", "execution_count": null, "id": "2d5c2692-5622-487e-bfc8-655ed5d75f86", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a8f50518-ca35-4cdb-bc8e-ff0581eb761b", "metadata": {}, "outputs": [], "source": ["product_data[\"ptype_kt_ratio_match\"] = product_data.apply(\n", "    lambda row: fuzz.ratio(row[\"product_type\"].lower(), row[\"metadata_name\"]), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5b8ced09-1ce9-4bb9-b287-88293e68c9ea", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a43cf221-2868-470f-abe3-7598ac6b1dee", "metadata": {}, "outputs": [], "source": ["ptype_kt_sim_df = (\n", "    product_data.sort_values([\"ptype_kt_ratio_match\"], ascending=False)[\n", "        [\"product_type\", \"metadata_name\", \"metadata_type\", \"metadata_id\"]\n", "    ]\n", "    .drop_duplicates(subset=[\"product_type\"])\n", "    .reset_index(drop=True)\n", ")\n", "ptype_kt_sim_dict = dict(zip(ptype_kt_sim_df[\"product_type\"], ptype_kt_sim_df[\"metadata_name\"]))\n", "ptype_kt_type_sim_dict = dict(\n", "    zip(ptype_kt_sim_df[\"product_type\"], ptype_kt_sim_df[\"metadata_type\"])\n", ")\n", "ptype_kt_id_sim_dict = dict(zip(ptype_kt_sim_df[\"product_type\"], ptype_kt_sim_df[\"metadata_id\"]))"]}, {"cell_type": "code", "execution_count": null, "id": "dfc194d3-f109-4889-94f2-eb4b6a7bb8b0", "metadata": {}, "outputs": [], "source": ["ptype_kt_sim_df.sample(10)"]}, {"cell_type": "code", "execution_count": null, "id": "843664a0-a7ab-4d32-9369-8f10a4fc4b35", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d9510397-fb55-4486-ab54-0140e0bc05db", "metadata": {}, "outputs": [], "source": ["ptype_id_dict = dict(zip(product_data[\"product_type\"], product_data[\"product_type_id\"]))\n", "l0_id_dict = dict(zip(product_data[\"l0_category\"], product_data[\"l0_category_id\"]))\n", "l1_id_dict = dict(zip(product_data[\"l1_category\"], product_data[\"l1_category_id\"]))\n", "# kt_id_dict = dict(zip(product_data[\"keyterm\"], product_data[\"keyterm_id\"]))\n", "\n", "ptype_l0_df = (\n", "    product_data.query(\"l0_category != 'Organic & Premium'\")\n", "    .groupby([\"l0_category\", \"product_type\"])[\"product_id\"]\n", "    .nunique()\n", "    .reset_index()\n", "    .sort_values(\"product_id\", ascending=False)\n", "    .drop_duplicates(\"product_type\")\n", "    .reset_index(drop=True)\n", ")\n", "ptype_l0_dict = dict(zip(ptype_l0_df[\"product_type\"], ptype_l0_df[\"l0_category\"]))\n", "\n", "ptype_l1_df = (\n", "    product_data.groupby([\"l1_category\", \"product_type\"])[\"product_id\"]\n", "    .nunique()\n", "    .reset_index()\n", "    .sort_values(\"product_id\", ascending=False)\n", "    .drop_duplicates(\"product_type\")\n", "    .reset_index(drop=True)\n", ")\n", "ptype_l1_dict = dict(zip(product_data[\"product_type\"], product_data[\"l1_category\"]))\n", "\n", "kt_pid_df = (\n", "    product_data.groupby([\"metadata_name\", \"metadata_id\", \"metadata_type\"])[\"product_id\"]\n", "    .unique()\n", "    .reset_index(name=\"pid_list\")\n", ")\n", "# kt_pid_dict = dict(zip(kt_pid_df[\"keyterm\"], kt_pid_df[\"pid_list\"]))\n", "\n", "l0_kt_df = (\n", "    product_data.groupby([\"l0_category\", \"metadata_name\", \"metadata_id\", \"metadata_type\"])[\n", "        \"product_id\"\n", "    ]\n", "    .nunique()\n", "    .reset_index()\n", "    .sort_values(\"product_id\", ascending=False)\n", "    .drop_duplicates([\"metadata_name\", \"metadata_id\", \"metadata_type\"])\n", "    .reset_index(drop=True)\n", ")\n", "# l0_kt_dict = dict(zip(l0_kt_df[\"keyterm\"], l0_kt_df[\"l0_category\"]))\n", "\n", "l1_kt_df = (\n", "    product_data.groupby([\"l1_category\", \"metadata_name\", \"metadata_id\", \"metadata_type\"])[\n", "        \"product_id\"\n", "    ]\n", "    .nunique()\n", "    .reset_index()\n", "    .sort_values(\"product_id\", ascending=False)\n", "    .drop_duplicates([\"metadata_name\", \"metadata_id\", \"metadata_type\"])\n", "    .reset_index(drop=True)\n", ")\n", "# l1_kt_dict = dict(zip(l1_kt_df[\"keyterm\"], l1_kt_df[\"l1_category\"]))"]}, {"cell_type": "code", "execution_count": null, "id": "793c3853-f57a-4ccb-a91c-c793b63d924a", "metadata": {}, "outputs": [], "source": ["entity_bestseller_df = (\n", "    product_data.sort_values([\"cart_count\"], ascending=False)[\n", "        [\"metadata_id\", \"metadata_name\", \"metadata_type\", \"product_id\"]\n", "    ]\n", "    .drop_duplicates([\"metadata_id\", \"metadata_name\", \"metadata_type\"])\n", "    .reset_index(drop=True)\n", ")\n", "entity_bestseller_df"]}, {"cell_type": "code", "execution_count": null, "id": "5bc1542b-e606-4976-a41e-52923eeecefa", "metadata": {}, "outputs": [], "source": ["entity_pid_count_df = (\n", "    product_data.groupby([\"metadata_id\", \"metadata_name\", \"metadata_type\"])[\"product_id\"]\n", "    .nunique()\n", "    .reset_index(name=\"pid_count\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c1332b4b-92e8-4b31-b672-0bd4a4d719be", "metadata": {}, "outputs": [], "source": ["entity_pid_count_df"]}, {"cell_type": "code", "execution_count": null, "id": "23b3ddba-449a-4e9a-ad2e-df6267d21c97", "metadata": {}, "outputs": [], "source": ["# product_data.sort_values(\"cart_count\", ascending=False).groupby([\"metadata_name\", \"metadata_id\", \"metadata_type\"]).head(5).groupby([\"metadata_name\", \"metadata_id\", \"metadata_type\"])[\"product_id\"].unique().reset_index(name=\"pid_list\").query(\"metadata_name in ('nutrition bar', 'energy bar')\")"]}, {"cell_type": "code", "execution_count": null, "id": "1efc269f-528d-47e9-b027-6889d4280096", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "dcb5c358-7c47-4993-ac92-7995662ca418", "metadata": {}, "outputs": [], "source": ["## reading blacklist data\n", "(\n", "    exclusion_all_l0,\n", "    exclusion_all_l1,\n", "    exclusion_all_ptype,\n", "    exclusion_all_pid,\n", "    exclusion_antecedent_l0,\n", "    exclusion_antecedent_l1,\n", "    exclusion_antecedent_ptype,\n", "    exclusion_antecedent_pid,\n", "    exclusion_consequent_l0,\n", "    exclusion_consequent_l1,\n", "    exclusion_consequent_ptype,\n", "    exclusion_consequent_pid,\n", ") = get_blacklist_data(widget=\"NPR\", from_sheet=use_sheet)"]}, {"cell_type": "code", "execution_count": null, "id": "95152669-7857-4ca7-baca-a885b1833954", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "141ad870-932a-4cf4-8045-03bd2d7e8523", "metadata": {}, "outputs": [], "source": ["## festive and gifting support\n", "gifting_df, festival_df, fnv_df = get_festival_df(from_sheet=use_sheet)"]}, {"cell_type": "code", "execution_count": null, "id": "13240a87-fd40-427e-9c4b-81656502b310", "metadata": {}, "outputs": [], "source": ["festival_df"]}, {"cell_type": "code", "execution_count": null, "id": "69bb95f2-5583-4b9c-8a18-bb2c54edbb13", "metadata": {}, "outputs": [], "source": ["gifting_df"]}, {"cell_type": "code", "execution_count": null, "id": "926d9fcb-e157-4710-bc63-2cf673be8cd1", "metadata": {}, "outputs": [], "source": ["fnv_df"]}, {"cell_type": "code", "execution_count": null, "id": "a498adea-25b8-4aff-963d-afccd667ffdf", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8e088b7b-f1c1-4f42-820f-5b0b66b6ec17", "metadata": {}, "outputs": [], "source": ["## reading complimentary sheet data\n", "comp_sheet = get_complimentary_data(\n", "    festival_df, gifting_df, fnv_df, festive=False, gifting=False, fnv=False, from_sheet=use_sheet\n", ")\n", "\n", "festive_ptype_data = get_complimentary_data(\n", "    festival_df, gifting_df, fnv_df, festive=True, gifting=False, fnv=False, from_sheet=use_sheet\n", ")\n", "\n", "gifting_ptype_data = get_complimentary_data(\n", "    festival_df, gifting_df, fnv_df, festive=False, gifting=True, fnv=False, from_sheet=use_sheet\n", ")\n", "\n", "fnv_ptype_data = get_complimentary_data(\n", "    festival_df, gifting_df, fnv_df, festive=False, gifting=False, fnv=True, from_sheet=use_sheet\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "24e9f00d-8b06-48cc-a604-75c274698a31", "metadata": {}, "outputs": [], "source": ["if len(fnv_ptype_data) > 0:\n", "    print(fnv_ptype_data.ptype.nunique())\n", "    comp_sheet = comp_sheet.loc[\n", "        ~comp_sheet[\"ptype\"].isin(fnv_ptype_data.ptype.unique())\n", "    ].reset_index(drop=True)\n", "    comp_sheet = pd.concat([comp_sheet, fnv_ptype_data]).reset_index(drop=True)\n", "\n", "if len(gifting_ptype_data) > 0:\n", "    print(gifting_ptype_data.ptype.nunique())\n", "    comp_sheet = comp_sheet.loc[\n", "        ~comp_sheet[\"ptype\"].isin(gifting_ptype_data.ptype.unique())\n", "    ].reset_index(drop=True)\n", "    comp_sheet = pd.concat([comp_sheet, gifting_ptype_data]).reset_index(drop=True)\n", "\n", "if len(festive_ptype_data) > 0:\n", "    print(festive_ptype_data.ptype.nunique())\n", "    comp_sheet = comp_sheet.loc[\n", "        ~comp_sheet[\"ptype\"].isin(festive_ptype_data.ptype.unique())\n", "    ].reset_index(drop=True)\n", "    comp_sheet = pd.concat([comp_sheet, festive_ptype_data]).reset_index(drop=True)\n", "\n", "\n", "comp_sheet = comp_sheet[[\"ptype\", \"value\", \"cons_ptype_rank\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "b0094453-9b6b-4be8-a570-9e91b7acfe9b", "metadata": {}, "outputs": [], "source": ["comp_sheet.ptype.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "e7fe0ab2-6fd8-4fe5-b150-e8fd9bf94bec", "metadata": {}, "outputs": [], "source": ["# comp_sheet = get_complimentary_data(None, None, False, False, use_sheet)\n", "# comp_sheet = comp_sheet[[\"ptype\", \"value\", \"cons_ptype_rank\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "f2513982-0173-4d64-b755-0d715f2591b2", "metadata": {}, "outputs": [], "source": ["comp_sheet[\"ant_l0\"] = comp_sheet[\"ptype\"].map(ptype_l0_dict)\n", "comp_sheet[\"ant_l1\"] = comp_sheet[\"ptype\"].map(ptype_l1_dict)\n", "\n", "comp_sheet[\"cons_l0\"] = comp_sheet[\"value\"].map(ptype_l0_dict)\n", "comp_sheet[\"cons_l1\"] = comp_sheet[\"value\"].map(ptype_l1_dict)"]}, {"cell_type": "code", "execution_count": null, "id": "0ee03352-f9b8-475a-8668-dd335b2d667f", "metadata": {}, "outputs": [], "source": ["comp_sheet[\"ant_l0_id\"] = comp_sheet[\"ant_l0\"].map(l0_id_dict)\n", "comp_sheet[\"ant_l1_id\"] = comp_sheet[\"ant_l1\"].map(l1_id_dict)\n", "\n", "comp_sheet[\"cons_l0_id\"] = comp_sheet[\"cons_l0\"].map(l0_id_dict)\n", "comp_sheet[\"cons_l1_id\"] = comp_sheet[\"cons_l1\"].map(l1_id_dict)\n", "\n", "comp_sheet[\"ant_ptype_id\"] = comp_sheet[\"ptype\"].map(ptype_id_dict)\n", "comp_sheet[\"cons_ptype_id\"] = comp_sheet[\"value\"].map(ptype_id_dict)"]}, {"cell_type": "code", "execution_count": null, "id": "203e41fb-97ae-4cb8-b600-d7ccd4f3621e", "metadata": {}, "outputs": [], "source": ["comp_sheet"]}, {"cell_type": "code", "execution_count": null, "id": "59e78f78-e904-48e0-9002-ff7990e77fa3", "metadata": {}, "outputs": [], "source": ["comp_sheet.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "09dbbf86-689b-4ea1-8165-e0a1926a6b4c", "metadata": {}, "outputs": [], "source": ["comp_sheet.dropna(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "6e00ba2a-1c01-4890-b7aa-69e9408eb486", "metadata": {}, "outputs": [], "source": ["## removing blacklist data - antecedent and consequent specific\n", "comp_sheet = comp_sheet[\n", "    ~comp_sheet[\"cons_ptype_id\"].isin(exclusion_all_ptype + exclusion_consequent_ptype)\n", "]\n", "comp_sheet = comp_sheet[\n", "    ~comp_sheet[\"ant_ptype_id\"].isin(exclusion_all_ptype + exclusion_antecedent_ptype)\n", "]\n", "\n", "comp_sheet = comp_sheet[~comp_sheet[\"cons_l0_id\"].isin(exclusion_all_l0 + exclusion_consequent_l0)]\n", "comp_sheet = comp_sheet[~comp_sheet[\"ant_l0_id\"].isin(exclusion_all_l0 + exclusion_antecedent_l0)]\n", "\n", "comp_sheet = comp_sheet[~comp_sheet[\"cons_l1_id\"].isin(exclusion_all_l1 + exclusion_consequent_l1)]\n", "comp_sheet = comp_sheet[~comp_sheet[\"ant_l1_id\"].isin(exclusion_all_l1 + exclusion_antecedent_l1)]\n", "comp_sheet.reset_index(drop=True, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "691d619d-02b2-4613-ac1c-dd105b6d4972", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6dc31ea8-ab46-4e5b-9317-8d0d94251b07", "metadata": {}, "outputs": [], "source": ["ptype_kt_sim_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "591856dd-67e8-46d7-8168-b04728deb00f", "metadata": {}, "outputs": [], "source": ["comp_sheet.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "62960622-afd1-44db-a947-9e5844e7f649", "metadata": {}, "outputs": [], "source": ["comp_sheet[\"sim_cons_kt\"] = comp_sheet[\"value\"].map(ptype_kt_sim_dict)\n", "comp_sheet[\"sim_ant_kt\"] = comp_sheet[\"ptype\"].map(ptype_kt_sim_dict)\n", "\n", "comp_sheet[\"sim_cons_kt_id\"] = comp_sheet[\"value\"].map(ptype_kt_id_sim_dict)\n", "comp_sheet[\"sim_ant_kt_id\"] = comp_sheet[\"ptype\"].map(ptype_kt_id_sim_dict)\n", "\n", "comp_sheet[\"sim_cons_kt_type\"] = comp_sheet[\"value\"].map(ptype_kt_type_sim_dict)\n", "comp_sheet[\"sim_ant_kt_type\"] = comp_sheet[\"ptype\"].map(ptype_kt_type_sim_dict)"]}, {"cell_type": "code", "execution_count": null, "id": "26e324f9-6300-42b4-baed-7187d7988c76", "metadata": {}, "outputs": [], "source": ["# comp_sheet[\"sim_cons_kt_l0\"] = comp_sheet[\"sim_cons_kt\"].map(l0_kt_dict)\n", "# comp_sheet[\"sim_cons_kt_l1\"] = comp_sheet[\"sim_cons_kt\"].map(l1_kt_dict)"]}, {"cell_type": "code", "execution_count": null, "id": "49f321cb-c139-42ed-9ac2-f23282c53ae1", "metadata": {}, "outputs": [], "source": ["comp_sheet[\"ant_ptype_l0\"] = comp_sheet[\"ptype\"].map(ptype_l0_dict)\n", "comp_sheet[\"ant_ptype_l1\"] = comp_sheet[\"ptype\"].map(ptype_l1_dict)\n", "\n", "comp_sheet[\"cons_ptype_l0\"] = comp_sheet[\"value\"].map(ptype_l0_dict)\n", "comp_sheet[\"cons_ptype_l1\"] = comp_sheet[\"value\"].map(ptype_l1_dict)"]}, {"cell_type": "code", "execution_count": null, "id": "69dbb4cc-3601-4b9e-878b-ce50dae53d46", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fc1ca488-5be1-41bd-a26c-170a979696d7", "metadata": {}, "outputs": [], "source": ["comp_sheet.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7271507f-898a-4836-8934-96856fc5d26e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "60e284a7-7c35-4d69-bfae-986f10d0b10a", "metadata": {}, "source": ["### Fetching required antecedents "]}, {"cell_type": "code", "execution_count": null, "id": "48c2c3d7-f63f-4160-b0fd-bcb8f057551c", "metadata": {}, "outputs": [], "source": ["if use_sheet:\n", "    select_ant_ptypes = (\n", "        read_sheets_with_retries(\n", "            \"1CJMfCflGl1GqNYJI7_s9hVOsjQq2U-dmsmBRD2nsykM\",\n", "            \"antecedent_selection\",\n", "        )[[\"antecedent_ptype\"]]\n", "        .drop_duplicates()\n", "        .reset_index(drop=True)\n", "    )\n", "else:\n", "    select_ant_ptypes = (\n", "        pd.read_csv(\"NPR KT groups - antecedent_selection.csv\")[[\"antecedent_ptype\"]]\n", "        .drop_duplicates()\n", "        .reset_index(drop=True)\n", "    )\n", "\n", "select_ant_ptypes_list = select_ant_ptypes.antecedent_ptype.unique()"]}, {"cell_type": "code", "execution_count": null, "id": "4e3dc21b-c9ab-4af2-ba37-fa916bf33c77", "metadata": {}, "outputs": [], "source": ["select_ant_ptypes"]}, {"cell_type": "code", "execution_count": null, "id": "d103e81c-45ab-4257-91de-225cfae0800d", "metadata": {}, "outputs": [], "source": ["product_data.product_type.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "be11d1e7-9ea5-49ba-83d7-51eb809f3dac", "metadata": {}, "outputs": [], "source": ["select_comp_sheet = comp_sheet.query(\"ptype in @select_ant_ptypes_list\").reset_index(drop=True)\n", "select_comp_sheet.shape, select_comp_sheet.ptype.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "07cceae4-143c-4140-96d1-25c029cf41ac", "metadata": {}, "outputs": [], "source": ["select_comp_sheet.head()"]}, {"cell_type": "code", "execution_count": null, "id": "30bd1778-0fc0-4498-952c-2f178e5f2879", "metadata": {}, "outputs": [], "source": ["# select_comp_sheet = select_comp_sheet.merge(product_data[['product_type','product_type_id']].drop_duplicates(), left_on='ptype', right_on='product_type')\n", "select_comp_sheet.shape, select_comp_sheet.ptype.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "40294b20-a5e9-4b1c-858b-1abe09d7b6bb", "metadata": {}, "outputs": [], "source": ["select_comp_sheet.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "29dfda31-0527-44ef-8f8b-c01f6462504d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7e4c2b6e-db4d-499e-b5a7-ce066f7cd6e4", "metadata": {}, "outputs": [], "source": ["select_comp_sheet = select_comp_sheet.query(\"sim_ant_kt != sim_cons_kt\").reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "43c8edca-408f-4853-8039-da5606200067", "metadata": {}, "outputs": [], "source": ["# ans_df = select_comp_sheet.query(\n", "#     \"ant_ptype_l0==sim_cons_kt_l0 or ant_ptype_l1==sim_cons_kt_l1\"\n", "# ).reset_index(drop=True)\n", "\n", "ans_df = select_comp_sheet.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "a93554b6-2485-4518-a872-d394daedeb03", "metadata": {}, "outputs": [], "source": ["ans_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "8bebf28a-aa63-4845-81e0-251d2ddfc26a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "16df3be9-1793-4541-a0eb-d8eb2b348816", "metadata": {}, "outputs": [], "source": ["ans_df = ans_df.merge(\n", "    kt_pid_df,\n", "    left_on=[\"sim_ant_kt\", \"sim_ant_kt_id\", \"sim_ant_kt_type\"],\n", "    right_on=[\"metadata_name\", \"metadata_id\", \"metadata_type\"],\n", "    how=\"left\",\n", ").drop(columns=[\"metadata_name\", \"metadata_id\", \"metadata_type\"])\n", "\n", "ans_df = ans_df.merge(\n", "    kt_pid_df,\n", "    left_on=[\"sim_cons_kt\", \"sim_cons_kt_id\", \"sim_cons_kt_type\"],\n", "    right_on=[\"metadata_name\", \"metadata_id\", \"metadata_type\"],\n", "    how=\"left\",\n", ").drop(columns=[\"metadata_name\", \"metadata_id\", \"metadata_type\"])"]}, {"cell_type": "code", "execution_count": null, "id": "4b3c3850-ee4b-4b5d-bfd6-776313a97009", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c0594113-c069-4318-be43-2084f7b3b730", "metadata": {}, "outputs": [], "source": ["ans_df[\"kt_pid_common\"] = ans_df.apply(\n", "    lambda row: len(set(row.pid_list_x).intersection(row.pid_list_y)) / len(set(row.pid_list_x)),\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8d595403-c647-47b1-84da-c2d767360254", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0094d7ad-9c04-4fe9-96bd-0f416a0c1a5e", "metadata": {}, "outputs": [], "source": ["print(ans_df.shape)\n", "ans_df = ans_df.query(\"kt_pid_common != 1\")\n", "print(ans_df.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "f6b716eb-56db-4c1d-9ec8-2f369dc0af6b", "metadata": {}, "outputs": [], "source": ["print(ans_df.shape)\n", "ans_df = ans_df.query(\"sim_cons_kt not in ('gift set')\")\n", "print(ans_df.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "acc76d52-b857-4113-b842-1a41e5daee8a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "caa9732a-2b01-4b6c-aa63-9e958b2466b5", "metadata": {}, "outputs": [], "source": ["ans_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "7a461644-4a9c-4eef-b168-51181b40b3b0", "metadata": {}, "outputs": [], "source": ["cols = [\n", "    \"ptype\",\n", "    \"ant_ptype_id\",\n", "    \"value\",\n", "    \"cons_ptype_rank\",\n", "    \"sim_cons_kt\",\n", "    \"sim_cons_kt_id\",\n", "    \"sim_cons_kt_type\",\n", "]\n", "ans_df = ans_df[cols]\n", "ans_df.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "d0e0c810-1426-45d6-915a-c1a95547c9c3", "metadata": {}, "outputs": [], "source": ["ans_df.columns = [\n", "    \"ptype\",\n", "    \"ptype_id\",\n", "    \"cons_ptype\",\n", "    \"cons_ptype_rank\",\n", "    \"attribute_name\",\n", "    \"attribute_id\",\n", "    \"attribute_type\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "f5258836-2c91-4c76-8c18-f0830fae5856", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3ef05bc7-c1ce-4a89-b8f2-d2e52969427c", "metadata": {}, "outputs": [], "source": ["tmp_df = (\n", "    ans_df[[\"attribute_id\", \"attribute_name\", \"attribute_type\"]]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "tmp_df.columns = [\"metadata_id\", \"metadata_name\", \"metadata_type\"]\n", "dup_df = (\n", "    product_data[[\"metadata_id\", \"metadata_name\", \"metadata_type\", \"product_id\"]]\n", "    .merge(tmp_df, on=[\"metadata_id\", \"metadata_name\", \"metadata_type\"])\n", "    .reset_index(drop=True)\n", ")\n", "dup_df"]}, {"cell_type": "code", "execution_count": null, "id": "85d7e550-dfcb-49b9-9b15-46af2e147423", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "bfbad842-e752-459a-acf4-75f50fe64173", "metadata": {}, "outputs": [], "source": ["dup_df = (\n", "    dup_df.groupby([\"metadata_id\", \"metadata_name\", \"metadata_type\"])[\"product_id\"]\n", "    .unique()\n", "    .reset_index()\n", ")\n", "dup_df[\"merge_col\"] = 1"]}, {"cell_type": "code", "execution_count": null, "id": "6a0afee6-5fed-48b9-a5a9-54cb7eec0820", "metadata": {}, "outputs": [], "source": ["dup_df = dup_df.merge(dup_df, on=[\"merge_col\"])"]}, {"cell_type": "code", "execution_count": null, "id": "b0fb0100-72d8-44e8-8771-b9d3690a2343", "metadata": {}, "outputs": [], "source": ["dup_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "1eb3d711-44b1-42a9-84ea-8e73f5d170e3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2b4d2db4-af48-4087-946d-767405ef9fc6", "metadata": {}, "outputs": [], "source": ["dup_df = dup_df.query(\n", "    \"~(metadata_id_x==metadata_id_y and metadata_name_x==metadata_name_y and metadata_type_x==metadata_type_y)\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "aef2ab3d-1de5-49d4-a3e4-8962539a9ef0", "metadata": {}, "outputs": [], "source": ["dup_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "47a8b69d-a2e2-4e1c-8b2a-9531f58095c1", "metadata": {}, "outputs": [], "source": ["dup_df[\"pid_similarity\"] = dup_df.apply(\n", "    lambda row: len(set(row[\"product_id_x\"]).intersection(row[\"product_id_y\"]))\n", "    / min(len(row[\"product_id_x\"]), len(row[\"product_id_y\"])),\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8b6e186a-8f26-4326-b72b-5d221ccce36f", "metadata": {}, "outputs": [], "source": ["dup_df[\"len_product_id_x\"] = dup_df.apply(lambda row: len(row[\"product_id_x\"]), axis=1)\n", "dup_df[\"len_product_id_y\"] = dup_df.apply(lambda row: len(row[\"product_id_y\"]), axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "348f5bbc-bfa0-432e-95cd-1ac7bb28c2d1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f65b1655-f803-4eb4-99f8-97b65743b92f", "metadata": {}, "outputs": [], "source": ["final_dup_df = dup_df.query(\"pid_similarity == 1 and len_product_id_x==len_product_id_y\")[\n", "    [\n", "        \"metadata_id_x\",\n", "        \"metadata_name_x\",\n", "        \"metadata_type_x\",\n", "        \"metadata_id_y\",\n", "        \"metadata_name_y\",\n", "        \"metadata_type_y\",\n", "        \"pid_similarity\",\n", "        \"len_product_id_x\",\n", "        \"len_product_id_y\",\n", "    ]\n", "].drop_duplicates()\n", "final_dup_df[\"dup_key\"] = final_dup_df[\"metadata_name_x\"] + \" \" + final_dup_df[\"metadata_name_y\"]\n", "final_dup_df[\"sorted_dup_key\"] = final_dup_df[\"dup_key\"].apply(\n", "    lambda x: \" \".join(sorted(x.split()))\n", ")\n", "final_dup_df = final_dup_df.drop_duplicates([\"sorted_dup_key\"])\n", "final_dup_df = final_dup_df.query(\"metadata_name_x!='women thermal'\")"]}, {"cell_type": "code", "execution_count": null, "id": "eb17d4d8-46b8-44e6-aa69-45d244693d72", "metadata": {}, "outputs": [], "source": ["final_dup_df"]}, {"cell_type": "code", "execution_count": null, "id": "92e9149e-e617-46db-b96d-b52d25a8b86f", "metadata": {}, "outputs": [], "source": ["dup_dict = dict(zip(final_dup_df[\"metadata_name_x\"], final_dup_df[\"metadata_name_y\"]))"]}, {"cell_type": "code", "execution_count": null, "id": "fe5a93a8-d764-4cd9-a0e2-664d98e3c3b0", "metadata": {}, "outputs": [], "source": ["dup_dict"]}, {"cell_type": "code", "execution_count": null, "id": "957ca80e-bfec-4ba1-af07-17ce2e78087d", "metadata": {}, "outputs": [], "source": ["ans_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b84aa279-b287-4df4-bc6b-78713ed3e212", "metadata": {}, "outputs": [], "source": ["ans_df[\"dup_col\"] = ans_df[\"attribute_name\"].map(dup_dict)"]}, {"cell_type": "code", "execution_count": null, "id": "49ff34da-19a9-4fc4-8cac-60ed8269ff1c", "metadata": {}, "outputs": [], "source": ["ans_df.loc[ans_df[\"dup_col\"].isna(), \"dup_col\"] = ans_df.loc[\n", "    ans_df[\"dup_col\"].isna(), \"attribute_name\"\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "d34680ec-1ac2-49dc-a587-9c0857ff7e47", "metadata": {}, "outputs": [], "source": ["ans_df[\"dup_count\"] = ans_df.groupby([\"ptype\", \"dup_col\"]).cumcount()"]}, {"cell_type": "code", "execution_count": null, "id": "5a6ea5c7-f82d-4a3d-9122-7e135a7089c9", "metadata": {}, "outputs": [], "source": ["ans_df = ans_df.query(\"dup_count==0\").reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "16611cfc-53e2-425d-a1ec-4c0ef7240f69", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "244de606-43e2-4fd6-800f-b0a718cc537e", "metadata": {}, "outputs": [], "source": ["ans_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "08344cdf-3483-41ed-a743-d8b1014d5708", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7fa4f226-6396-42a9-9151-c307c88d74bd", "metadata": {}, "outputs": [], "source": ["if use_sheet:\n", "    noise_data = read_sheets_with_retries(\"1CJMfCflGl1GqNYJI7_s9hVOsjQq2U-dmsmBRD2nsykM\", \"noise\")\n", "else:\n", "    noise_data = pd.read_csv(\"NPR KT groups - noise.csv\")\n", "\n", "noise_data.columns = [\"ptype\", \"attribute_name\"]\n", "noise_data[\"is_noise\"] = 1"]}, {"cell_type": "code", "execution_count": null, "id": "ffa8a3ec-5e75-4bba-80a4-3c9da107d73b", "metadata": {}, "outputs": [], "source": ["noise_data"]}, {"cell_type": "code", "execution_count": null, "id": "d2de74d6-f515-4719-98d5-2506f94cca82", "metadata": {}, "outputs": [], "source": ["ans_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "13ecdb86-968d-4028-b3b8-10ee4b09c61f", "metadata": {}, "outputs": [], "source": ["ans_df = ans_df.merge(noise_data, on=[\"ptype\", \"attribute_name\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "f67557dc-1521-4e49-99a4-4d9306946d42", "metadata": {}, "outputs": [], "source": ["ans_df.shape, ans_df.drop_duplicates(\n", "    [\"ptype_id\", \"attribute_name\", \"attribute_id\", \"attribute_type\"]\n", ").shape"]}, {"cell_type": "code", "execution_count": null, "id": "c5dc5a4c-e339-4f2e-aea6-6b591d233c11", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8d6f5d43-3324-4c0f-bd90-b09411388ca2", "metadata": {}, "outputs": [], "source": ["ans_df = ans_df.query(\"is_noise != is_noise\").reset_index(drop=True)\n", "ans_df.drop(columns=[\"is_noise\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "478f07f2-7069-40f4-bf84-f8d315b769ce", "metadata": {}, "outputs": [], "source": ["ans_df"]}, {"cell_type": "code", "execution_count": null, "id": "1237cbd6-9361-4ddc-abd5-8c2b1de46118", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"ptype=='Toilet Brush'\")"]}, {"cell_type": "code", "execution_count": null, "id": "2e2f5202-3b56-43c3-8df2-32ab78a8fb30", "metadata": {}, "outputs": [], "source": ["ans_df.drop_duplicates(\n", "    [\"ptype_id\", \"attribute_name\", \"attribute_id\", \"attribute_type\"], inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3666a86a-6f7d-4c61-bc91-ffb5411c9450", "metadata": {}, "outputs": [], "source": ["ans_df[\"cons_rank\"] = ans_df.groupby([\"ptype\", \"ptype_id\"]).cumcount()"]}, {"cell_type": "code", "execution_count": null, "id": "4536b531-50a9-41c5-9f48-7cebcc72aa47", "metadata": {}, "outputs": [], "source": ["ans_df.groupby(\"ptype\")[\"cons_rank\"].max().reset_index()[\"cons_rank\"].value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "a7260e9e-5e72-4894-b0e5-44f155ea2aca", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "937a6294-814a-467b-bb92-a6fc7905be2f", "metadata": {}, "outputs": [], "source": ["ans_df[\"ptype_id\"] = ans_df[\"ptype_id\"].astype(\"int\")\n", "ans_df[\"attribute_id\"] = ans_df[\"attribute_id\"].astype(\"int\")\n", "ans_df[\"cons_rank\"] = ans_df[\"cons_rank\"].astype(\"int\")"]}, {"cell_type": "code", "execution_count": null, "id": "f27f8bd0-b6b4-40ce-bcf4-7eedece45f37", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"cons_rank != cons_rank\")"]}, {"cell_type": "code", "execution_count": null, "id": "03d70dde-97fd-4b5d-b1c2-8179fe5bdf83", "metadata": {}, "outputs": [], "source": ["select_ant_ptypes = ans_df.query(\"cons_rank>=2\")[\"ptype\"].unique()\n", "ans_df = ans_df.query(\"ptype in @select_ant_ptypes\")"]}, {"cell_type": "code", "execution_count": null, "id": "f9d7da1f-e216-499a-837d-430243cdf8d8", "metadata": {}, "outputs": [], "source": ["ans_df"]}, {"cell_type": "code", "execution_count": null, "id": "68011e29-5fa9-4ea1-be09-b2c63b8969e3", "metadata": {}, "outputs": [], "source": ["ans_df[\"is_enabled\"] = True\n", "ans_df[\"display_name\"] = ans_df[\"attribute_name\"].str.title()"]}, {"cell_type": "code", "execution_count": null, "id": "0ed02179-3122-42bf-9b6c-ee911fe4e1c8", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"ptype == 'Packaged Water'\")"]}, {"cell_type": "code", "execution_count": null, "id": "5a5748c6-bde3-4096-9f74-0bb969491617", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8d2445af-df2d-4b3c-a567-ea49df3429a1", "metadata": {}, "outputs": [], "source": ["ans_df.groupby(\"ptype\")[\"attribute_name\"].nunique().value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "ed90dbf5-c8b5-4ef0-855b-7544707ae71a", "metadata": {}, "outputs": [], "source": ["ans_df[\n", "    [\"is_enabled\", \"ptype_id\", \"attribute_id\", \"attribute_type\"]\n", "].drop_duplicates().shape, ans_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "9db36283-546b-41cf-805c-624abe4308cc", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"attribute_type == 'COMPOSITE_KEYWORD'\")[\"attribute_id\"].nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "d6fb763f-5d18-416d-ba1d-3592dc34467a", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"ptype=='Gym Belt'\")"]}, {"cell_type": "code", "execution_count": null, "id": "8b69c7d1-6818-478a-96d1-4137227f5d9d", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"ptype == 'Toilet Brush'\")"]}, {"cell_type": "markdown", "id": "37512c5d-dacb-405e-a2c8-fc105d7783f6", "metadata": {}, "source": ["### Composite keyword composition"]}, {"cell_type": "code", "execution_count": null, "id": "6a75216f-faf0-4947-97af-e4f6f6f87e38", "metadata": {}, "outputs": [], "source": ["composition_df = run_query_with_retries(\n", "    f\"\"\"\n", "select keyword_id, entity_id, entity_type\n", "from product_knowledge_metadata.keyword_composition\n", "where is_enabled and lake_active_record\n", "and keyword_id in {tuple(ans_df.attribute_id.unique())}\n", "group by 1,2,3\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "51eff696-30e9-4bd3-b643-c1443f550e00", "metadata": {}, "outputs": [], "source": ["composition_df.shape, composition_df.keyword_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "75d06b27-6917-4b67-abe1-063a36c1f543", "metadata": {}, "outputs": [], "source": ["composition_df"]}, {"cell_type": "code", "execution_count": null, "id": "118afb57-a515-4ba0-a627-24077a594d30", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4dac1082-94cb-450a-9d1f-eb2bce68942d", "metadata": {}, "outputs": [], "source": ["ck_keyterm_df = composition_df.query(\"entity_type == 'KEYTERM'\")\n", "ck_keyterm_dict = dict(zip(ck_keyterm_df[\"keyword_id\"], ck_keyterm_df[\"entity_id\"]))"]}, {"cell_type": "code", "execution_count": null, "id": "5e67a9aa-641f-4638-8a37-7b4edd7dce46", "metadata": {}, "outputs": [], "source": ["ck_brand_df = composition_df.query(\"entity_type == 'BRAND'\")\n", "ck_brand_dict = dict(zip(ck_brand_df[\"keyword_id\"], ck_brand_df[\"entity_id\"]))"]}, {"cell_type": "code", "execution_count": null, "id": "471170b6-e900-4e30-8f52-67f57c7a5814", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8de14930-0b6e-478c-b60d-5abb96e37212", "metadata": {}, "outputs": [], "source": ["# replacing brand+keyterm to only keyterm\n", "ans_df.loc[ans_df[\"attribute_type\"] == \"COMPOSITE_KEYWORD\", \"derived_brand\"] = ans_df.loc[\n", "    ans_df[\"attribute_type\"] == \"COMPOSITE_KEYWORD\", \"attribute_id\"\n", "].map(ck_brand_dict)\n", "\n", "ans_df.loc[~ans_df[\"derived_brand\"].isna(), \"derived_brand_keyterm\"] = ans_df.loc[\n", "    ~ans_df[\"derived_brand\"].isna(), \"attribute_id\"\n", "].map(ck_keyterm_dict)"]}, {"cell_type": "code", "execution_count": null, "id": "08397831-8dee-4a8a-abd0-a186980d9571", "metadata": {}, "outputs": [], "source": ["keyterm_df = product_data.query(\"metadata_type=='KEYTERM'\")[\n", "    [\"metadata_id\", \"metadata_name\"]\n", "].drop_duplicates()\n", "kt_id_dict = dict(zip(keyterm_df[\"metadata_id\"], keyterm_df[\"metadata_name\"]))"]}, {"cell_type": "code", "execution_count": null, "id": "e063935d-05a1-40a1-b4b7-75ac90cbb5cb", "metadata": {}, "outputs": [], "source": ["ans_df.loc[~ans_df[\"derived_brand_keyterm\"].isna(), \"attribute_type\"] = \"KEYTERM\"\n", "ans_df.loc[~ans_df[\"derived_brand_keyterm\"].isna(), \"attribute_id\"] = ans_df.loc[\n", "    ~ans_df[\"derived_brand_keyterm\"].isna(), \"derived_brand_keyterm\"\n", "]\n", "ans_df.loc[~ans_df[\"derived_brand_keyterm\"].isna(), \"attribute_name\"] = ans_df.loc[\n", "    ~ans_df[\"derived_brand_keyterm\"].isna(), \"derived_brand_keyterm\"\n", "].map(kt_id_dict)"]}, {"cell_type": "code", "execution_count": null, "id": "06b685e2-dc8d-4d5a-859f-cc47ec17209a", "metadata": {}, "outputs": [], "source": ["ans_df"]}, {"cell_type": "code", "execution_count": null, "id": "4defaf99-b20d-4a67-aeca-27bed3096c25", "metadata": {}, "outputs": [], "source": ["ans_df.drop_duplicates(\n", "    [\"ptype_id\", \"attribute_name\", \"attribute_id\", \"attribute_type\"], inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3e43c166-d1c5-4f18-aec0-b3b3e6a09747", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"ptype == 'Toilet Brush'\")"]}, {"cell_type": "code", "execution_count": null, "id": "dd204ab0-5389-4b6a-ba53-8229de85f97b", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"ptype == 'Callus Remover'\")"]}, {"cell_type": "code", "execution_count": null, "id": "053daf32-36a1-42b1-adcb-701c737d2551", "metadata": {}, "outputs": [], "source": ["# removing duplicates among composite_keywords and keyterms\n", "ans_df[\"is_keyterm\"] = 1\n", "ans_df.loc[ans_df[\"attribute_type\"] == \"COMPOSITE_KEYWORD\", \"is_keyterm\"] = 0\n", "ans_df[\"derived_keyterm\"] = ans_df[\"attribute_id\"]\n", "ans_df.loc[ans_df[\"attribute_type\"] == \"COMPOSITE_KEYWORD\", \"derived_keyterm\"] = ans_df.loc[\n", "    ans_df[\"attribute_type\"] == \"COMPOSITE_KEYWORD\", \"attribute_id\"\n", "].map(ck_keyterm_dict)"]}, {"cell_type": "code", "execution_count": null, "id": "47306482-2bbb-46f8-8940-b3b5c1e5cd8c", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"ptype == 'Toilet Brush'\")"]}, {"cell_type": "code", "execution_count": null, "id": "07fa140a-7da0-4c06-ba0e-b25e79744410", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"ptype == 'Callus Remover'\")"]}, {"cell_type": "code", "execution_count": null, "id": "aa58a439-75b6-427d-9313-0d523d4b5cce", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"ptype == 'Oats'\")"]}, {"cell_type": "code", "execution_count": null, "id": "4fec8e70-9d46-4384-8564-173be6b6f712", "metadata": {}, "outputs": [], "source": ["ans_df"]}, {"cell_type": "code", "execution_count": null, "id": "44f19859-82e8-4bd4-b467-ccec8621126a", "metadata": {}, "outputs": [], "source": ["def deduplicate_preserve_composite_ranks(df):\n", "    final_rows = []\n", "\n", "    for (ptype, dkey), group in df.groupby([\"ptype\", \"derived_keyterm\"]):\n", "        composite_rows = group[group[\"is_keyterm\"] == 0].copy()\n", "        keyterm_rows = group[group[\"is_keyterm\"] == 1].copy()\n", "\n", "        if not composite_rows.empty:\n", "            # All ranks (keyterm + composite)\n", "            all_ranks = (\n", "                pd.concat([composite_rows, keyterm_rows])[\"cons_rank\"].sort_values().tolist()\n", "            )\n", "\n", "            # Sort composites (e.g., alphabetically, or by original order if preferred)\n", "            composite_rows = composite_rows.sort_values(\"cons_ptype\").reset_index(drop=True)\n", "\n", "            # Assign lowest available ranks to composites\n", "            for i in range(min(len(composite_rows), len(all_ranks))):\n", "                row = composite_rows.iloc[i].copy()\n", "                row[\"cons_rank\"] = all_ranks[i]\n", "                final_rows.append(row.to_dict())\n", "        else:\n", "            # No composites, keep all keyterms\n", "            final_rows.extend(group.to_dict(\"records\"))\n", "\n", "    final_df = pd.DataFrame(final_rows)\n", "    return final_df.sort_values([\"ptype\", \"cons_rank\"]).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "f70365b3-b1d6-4ef9-bf91-2d68b1a2a68b", "metadata": {}, "outputs": [], "source": ["ans_df.shape, ans_df.ptype.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "d55824c8-f1d6-4729-a185-e40e56397184", "metadata": {}, "outputs": [], "source": ["ans_df = deduplicate_preserve_composite_ranks(ans_df)"]}, {"cell_type": "code", "execution_count": null, "id": "cb128625-2c0f-4940-9850-30ecd9cc2f11", "metadata": {}, "outputs": [], "source": ["ans_df.shape, ans_df.ptype.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "10056c59-4a5e-4691-9da4-39b711dc38ed", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"ptype=='Oats'\")"]}, {"cell_type": "code", "execution_count": null, "id": "35eaae2b-af1a-4423-91e2-ea912ab7eba0", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"ptype=='Toilet Brush'\")"]}, {"cell_type": "code", "execution_count": null, "id": "fe3658ac-ab1c-4155-ae52-41338f0cc1c5", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"ptype=='Callus Remover'\")"]}, {"cell_type": "code", "execution_count": null, "id": "61f20694-780d-44e0-bb63-67f47dc31745", "metadata": {}, "outputs": [], "source": ["ans_df"]}, {"cell_type": "code", "execution_count": null, "id": "f997e157-88d0-4f44-8805-490a89af5909", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c2f2cf8b-4b9b-4312-8975-a22d32f92cde", "metadata": {}, "outputs": [], "source": ["# ans_df = (\n", "#     ans_df.merge(\n", "#         (\n", "#             ans_df.groupby([\"ptype_id\", \"derived_keyterm\"])[\"attribute_id\"]\n", "#             .nunique()\n", "#             .reset_index(name=\"attr_count\")\n", "#         )\n", "#     )\n", "#     .merge(\n", "#         ans_df.groupby([\"ptype_id\", \"derived_keyterm\"])[\"cons_rank\"]\n", "#         .min()\n", "#         .reset_index(name=\"min_cons_rank_ck_kt\")\n", "#     )\n", "#     .query(\"~(attr_count>1 and is_keyterm==1)\")\n", "#     .sort_values(\"cons_rank\")\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "5b134e61-372d-4fb0-8e6b-2cba8b180c92", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "12598000-8203-477e-bb99-1f8cdca077c0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "25b2bc1f-889a-4aad-bbf2-6894bf0afe23", "metadata": {}, "outputs": [], "source": ["ans_df.sort_values([\"cons_rank\"], inplace=True)\n", "ans_df[\"cons_rank\"] = ans_df.groupby([\"ptype\", \"ptype_id\"]).cumcount()"]}, {"cell_type": "code", "execution_count": null, "id": "e73a77cb-dcc6-404e-bf0b-bbb15d5af16d", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"ptype == 'Toilet Brush'\")"]}, {"cell_type": "code", "execution_count": null, "id": "e20b38c0-031c-4ded-a872-e986d85e677f", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"ptype == 'Oats'\")"]}, {"cell_type": "code", "execution_count": null, "id": "ee47dbbb-73c5-4254-bf96-0402f6e5f4f5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "34740a0a-d058-4696-bc0b-28dffd666273", "metadata": {}, "outputs": [], "source": ["entity_bestseller_df.columns = [\"attribute_id\", \"attribute_name\", \"attribute_type\", \"product_id\"]\n", "entity_pid_count_df.columns = [\"attribute_id\", \"attribute_name\", \"attribute_type\", \"pid_count\"]"]}, {"cell_type": "code", "execution_count": null, "id": "80970264-96de-4748-a225-06da4d07f6e2", "metadata": {}, "outputs": [], "source": ["ans_df = ans_df.merge(\n", "    entity_bestseller_df, on=[\"attribute_id\", \"attribute_name\", \"attribute_type\"], how=\"left\"\n", ").merge(entity_pid_count_df, on=[\"attribute_id\", \"attribute_name\", \"attribute_type\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "8d33c761-eba1-41e6-81d5-46087d9c2e65", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b369f531-307b-4b4f-a14d-e6db037479a9", "metadata": {}, "outputs": [], "source": ["ans_df.sort_values(\"pid_count\", ascending=False, inplace=True)\n", "ans_df[\"pid_count_rank\"] = ans_df.groupby([\"ptype\", \"ptype_id\", \"product_id\"]).cumcount()\n", "ans_df.sort_values(\"cons_rank\", inplace=True)\n", "# .query(\"ptype=='Protein Bar'\")"]}, {"cell_type": "code", "execution_count": null, "id": "a59a17db-32e9-42f4-9238-df927910d552", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"pid_count_rank > 0\")"]}, {"cell_type": "code", "execution_count": null, "id": "a2032cfc-3cfb-43cf-a376-0ac8c2ce53e5", "metadata": {}, "outputs": [], "source": ["ans_df = ans_df.merge(\n", "    ans_df.groupby([\"ptype\", \"ptype_id\", \"product_id\"])[\"cons_rank\"]\n", "    .min()\n", "    .reset_index(name=\"bestseller_min_rank\"),\n", "    on=[\"ptype\", \"ptype_id\", \"product_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1ea21bca-663f-4026-bf3f-18495c94fa9d", "metadata": {}, "outputs": [], "source": ["ans_df = ans_df.query(\"pid_count_rank==0\").reset_index(drop=True)\n", "ans_df.sort_values(\"bestseller_min_rank\", inplace=True)\n", "ans_df[\"cons_rank\"] = ans_df.groupby([\"ptype\", \"ptype_id\"]).cumcount()"]}, {"cell_type": "code", "execution_count": null, "id": "e0696743-5ad6-42a8-a860-0176ad4003c8", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"ptype=='Protein Bar'\")"]}, {"cell_type": "code", "execution_count": null, "id": "85df519b-b1ce-4825-bd41-477323c37717", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"ptype=='Sunscreen Spray'\")"]}, {"cell_type": "code", "execution_count": null, "id": "2b49da2c-9f6a-42dd-bec5-eb3953acc4fe", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ea43ded5-cd9f-4789-8897-a85a8a90ee12", "metadata": {}, "outputs": [], "source": ["select_ant_ptypes = ans_df.query(\"cons_rank>=2\")[\"ptype\"].unique()\n", "ans_df = ans_df.query(\"ptype in @select_ant_ptypes\")"]}, {"cell_type": "code", "execution_count": null, "id": "27784997-9bc8-4b7f-bf44-2f50e619a941", "metadata": {}, "outputs": [], "source": ["ans_df[\"ptype_id\"] = ans_df[\"ptype_id\"].astype(\"int\")\n", "ans_df[\"attribute_id\"] = ans_df[\"attribute_id\"].astype(\"int\")\n", "ans_df[\"cons_rank\"] = ans_df[\"cons_rank\"].astype(\"int\")\n", "\n", "ans_df[\"display_name\"] = ans_df[\"attribute_name\"].str.title()"]}, {"cell_type": "code", "execution_count": null, "id": "32791cad-1876-4085-a434-9eaff7a2555f", "metadata": {}, "outputs": [], "source": ["ans_df.ptype.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "2d7df1dd-a08e-4a9c-9475-ce457eab1fd8", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"ptype == 'Oats'\").reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "c92f5917-0dbd-4682-8ed0-47c47862def1", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"ptype == 'Callus Remover'\").reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "73129aa4-526f-499e-917b-fecee758b91a", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"ptype=='Sunscreen Spray'\")"]}, {"cell_type": "code", "execution_count": null, "id": "952f29ac-8375-47db-8ddf-265392ae5a3b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fad83049-f2a5-4e74-afe4-3a2f28358735", "metadata": {}, "outputs": [], "source": ["entity_key_map = {\n", "    \"USECASE\": \"usecase_id\",\n", "    \"RECIPIENT\": \"recipient_id\",\n", "    \"OCCASION\": \"occasion_id\",\n", "    \"KEYTERM\": \"keyterm_id\",\n", "    \"BRAND\": \"brand_id\",\n", "    \"ATTRIBUTE\": \"attribute_id\",\n", "}\n", "\n", "\n", "def transform(df):\n", "    # Apply the entity_key mapping\n", "    df[\"entity_key\"] = df[\"entity_type\"].map(entity_key_map)\n", "\n", "    # Group by keyword_id and construct the desired output\n", "    result = (\n", "        df.groupby(\"keyword_id\")\n", "        .apply(\n", "            lambda group: json.dumps(\n", "                [\n", "                    {\n", "                        \"entity_values\": group[group[\"entity_type\"] == et][\"entity_id\"].tolist(),\n", "                        \"entity_key\": entity_key_map[et],\n", "                    }\n", "                    for et in group[\"entity_type\"].unique()\n", "                ]\n", "            )\n", "        )\n", "        .to_dict()\n", "    )\n", "\n", "    return result\n", "\n", "\n", "output = transform(composition_df)"]}, {"cell_type": "code", "execution_count": null, "id": "4bd17808-cf49-4639-b30c-ea61dffe5a7a", "metadata": {}, "outputs": [], "source": ["# output[25519]"]}, {"cell_type": "code", "execution_count": null, "id": "23ac1122-e301-49ff-a3c5-660239f06a36", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c35e1433-ff74-4032-b40e-8d9227c746b0", "metadata": {}, "outputs": [], "source": ["ans_df[\"collection_filters\"] = ans_df.apply(\n", "    lambda row: json.dumps([{\"entity_values\": [row.attribute_id], \"entity_key\": \"keyterm_id\"}]),\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4bc02874-c762-4ab7-8ad8-e8c0da14d3ef", "metadata": {}, "outputs": [], "source": ["ans_df.loc[ans_df.attribute_type == \"COMPOSITE_KEYWORD\", \"collection_filters\"] = ans_df.loc[\n", "    ans_df.attribute_type == \"COMPOSITE_KEYWORD\", \"attribute_id\"\n", "].map(output)"]}, {"cell_type": "code", "execution_count": null, "id": "04582075-58ef-4b09-8f9d-cf8002ba5363", "metadata": {}, "outputs": [], "source": ["ans_df.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "9b4a7f04-4ff1-4c71-8b1b-ce20ae48684d", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"collection_filters!=collection_filters\")"]}, {"cell_type": "code", "execution_count": null, "id": "84c2f16f-b384-456a-9910-d913f6370ef7", "metadata": {}, "outputs": [], "source": ["ans_df = ans_df[\n", "    [\n", "        \"ptype_id\",\n", "        \"ptype\",\n", "        \"cons_ptype\",\n", "        \"attribute_name\",\n", "        \"attribute_id\",\n", "        \"attribute_type\",\n", "        \"is_enabled\",\n", "        \"display_name\",\n", "        \"cons_rank\",\n", "        \"collection_filters\",\n", "    ]\n", "].drop_duplicates()"]}, {"cell_type": "markdown", "id": "3b17104f-f4a7-4ae0-9595-026c1aa84c5c", "metadata": {}, "source": ["### Remove bad performance ptypes"]}, {"cell_type": "code", "execution_count": null, "id": "e874e945-529c-4a09-8cb5-72fa7ff31c6a", "metadata": {}, "outputs": [], "source": ["if use_sheet:\n", "    bad_perf_df = read_sheets_with_retries(\n", "        sheet_id=\"1CJMfCflGl1GqNYJI7_s9hVOsjQq2U-dmsmBRD2nsykM\", sheet_name=\"remove_ptypes_perf\"\n", "    )\n", "else:\n", "    bad_perf_df = pd.read_csv(\"NPR KT groups - remove_ptypes_perf.csv\")\n", "bad_perf_df[\"ptype_id\"] = bad_perf_df[\"ptype_id\"].astype(\"int\")"]}, {"cell_type": "code", "execution_count": null, "id": "6667bc5c-3057-4870-b137-cda9d14f50e1", "metadata": {}, "outputs": [], "source": ["bad_perf_df"]}, {"cell_type": "code", "execution_count": null, "id": "ef9e94de-6a08-4c20-8a3a-0fd5421b3647", "metadata": {}, "outputs": [], "source": ["ans_df.is_enabled.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "955ed30a-d170-4cc5-a9f3-664d326324a8", "metadata": {}, "outputs": [], "source": ["ans_df.loc[ans_df.ptype_id.isin(bad_perf_df.ptype_id.unique()), \"is_enabled\"] = False"]}, {"cell_type": "code", "execution_count": null, "id": "d1a67cef-9fc9-49da-b82a-3d469bec1193", "metadata": {}, "outputs": [], "source": ["ans_df.is_enabled.value_counts(), ans_df.query(\"is_enabled==True\")[\"ptype\"].nunique(), ans_df.query(\n", "    \"is_enabled==False\"\n", ")[\"ptype\"].nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "a77d6e1e-5fb4-42f5-90ed-608e291b692b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "02981488-a204-479e-9bc4-db12dffdc482", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3eb59f7a-2bba-41a8-a6d7-6a0f64a38d8a", "metadata": {}, "outputs": [], "source": ["ans_df.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "3e94fcdc-d9c0-4802-8919-8ff44ad981d2", "metadata": {}, "outputs": [], "source": ["ans_df.dropna(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "f3d07fcf-f158-4b70-9326-1c636f3e67b8", "metadata": {}, "outputs": [], "source": ["ans_df[\"rank\"] = ans_df.groupby([\"ptype\", \"ptype_id\"]).cumcount()"]}, {"cell_type": "code", "execution_count": null, "id": "2806032f-1be4-4e9f-8c31-9a6d249e1bee", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"ptype == 'Sunscreen Spray'\")"]}, {"cell_type": "code", "execution_count": null, "id": "1ec87baf-28a9-4a46-88b7-0bdb4b7fa6f2", "metadata": {}, "outputs": [], "source": ["ans_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "ce430ae8-8838-4371-a03b-e2301400b3dd", "metadata": {}, "outputs": [], "source": ["ans_df.shape, ans_df.ptype.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "f9f4e396-47d7-46b8-8921-852587eb6ce4", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"ptype=='Shaped Chocolates'\")"]}, {"cell_type": "code", "execution_count": null, "id": "c3883dec-4844-47dd-8c7f-3d5f2ee68c25", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"ptype=='Toilet Brush'\")"]}, {"cell_type": "code", "execution_count": null, "id": "b085bfd8-8d3d-4345-952d-5d07f28c77d0", "metadata": {}, "outputs": [], "source": ["table_kwargs = {\n", "    \"schema_name\": \"consumer_intelligence_etls\",\n", "    \"table_name\": \"complementary_ptype_collection_recommendations_v1\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"ptype_id\", \"type\": \"INTEGER\", \"description\": \"ant ptype_id\"},\n", "        {\"name\": \"ptype\", \"type\": \"VARCHAR\", \"description\": \"ant ptype\"},\n", "        {\"name\": \"cons_ptype\", \"type\": \"VARCHAR\", \"description\": \"cons_ptype\"},\n", "        {\n", "            \"name\": \"attribute_name\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"cons attribute_name\",\n", "        },\n", "        {\"name\": \"attribute_id\", \"type\": \"INTEGER\", \"description\": \"cons attribute_id\"},\n", "        {\n", "            \"name\": \"attribute_type\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"cons attribute_type\",\n", "        },\n", "        {\n", "            \"name\": \"is_enabled\",\n", "            \"type\": \"BOOLEAN\",\n", "            \"description\": \"is_enabled flag\",\n", "        },\n", "        {\n", "            \"name\": \"display_name\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"display_name\",\n", "        },\n", "        {\n", "            \"name\": \"rank\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"cons rank\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"ptype_id\", \"attribute_id\", \"attribute_type\", \"is_enabled\"],\n", "    \"partition_key\": [\"ptype\"],\n", "    \"load_type\": \"truncate\",  # append, overwrite, truncate or upsert,\n", "    \"table_description\": \"NPR groups\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "d153027c-412a-4c02-bb38-b3b9b23bc730", "metadata": {}, "outputs": [], "source": ["pb.to_trino(\n", "    ans_df[\n", "        [\n", "            \"ptype_id\",\n", "            \"ptype\",\n", "            \"cons_ptype\",\n", "            \"attribute_name\",\n", "            \"attribute_id\",\n", "            \"attribute_type\",\n", "            \"is_enabled\",\n", "            \"display_name\",\n", "            \"rank\",\n", "        ]\n", "    ],\n", "    **table_kwargs\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f543cdb2-8cec-440a-a97e-26be76b15600", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7d7faa94-3f8c-447c-9679-3c1c3ddceffc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ce7b7ad9-60e8-4c2d-aee9-63e88efe021c", "metadata": {}, "outputs": [], "source": ["push_df = ans_df[[\"is_enabled\", \"ptype_id\", \"collection_filters\", \"display_name\", \"rank\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "f4a760f1-1ca1-40ff-8492-5f5e28602251", "metadata": {}, "outputs": [], "source": ["push_df.columns = [\"is_enabled\", \"ptype_id\", \"collection_filters\", \"display_name\", \"rank\"]"]}, {"cell_type": "code", "execution_count": null, "id": "5f6ab626-b81c-435d-8203-a1776a5ecccf", "metadata": {}, "outputs": [], "source": ["push_df"]}, {"cell_type": "code", "execution_count": null, "id": "defab300-5c6f-4fef-9118-52eb9202f055", "metadata": {}, "outputs": [], "source": ["ans_df.query(\"ptype == 'Sanitary Pads'\")"]}, {"cell_type": "code", "execution_count": null, "id": "8f1e7bdd-b3c0-4d8a-be41-0a5d6fea49da", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "a12b446c-222a-41ea-87af-9c0408f8914c", "metadata": {}, "source": ["### ESPINA push\n"]}, {"cell_type": "code", "execution_count": null, "id": "6121bd0f-6a14-4af5-93a3-bbe14cdd385a", "metadata": {}, "outputs": [], "source": ["push_df[\"is_enabled\"] = push_df[\"is_enabled\"].astype(bool)\n", "push_df[\"ptype_id\"] = push_df[\"ptype_id\"].astype(int)\n", "push_df[\"rank\"] = push_df[\"rank\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "53a87575-1a86-4228-ada2-3fa982832086", "metadata": {}, "outputs": [], "source": ["push_df.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "9760de55-1438-4acc-82f3-fd116118ab1c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ddfb9fc5-a991-47da-a135-1bcae36d69dd", "metadata": {}, "outputs": [], "source": ["push_df.shape, push_df.ptype_id.nunique(), push_df[\n", "    [\"is_enabled\", \"ptype_id\", \"rank\"]\n", "].drop_duplicates().shape"]}, {"cell_type": "code", "execution_count": null, "id": "fb1a8dee-7fdc-48bc-b00e-29cd594ae300", "metadata": {}, "outputs": [], "source": ["push_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "6ec88621-84a8-4e83-b4ea-c837eda2d1e7", "metadata": {}, "outputs": [], "source": ["push_cols = [\"is_enabled\", \"ptype_id\", \"collection_filters\", \"display_name\", \"rank\"]"]}, {"cell_type": "code", "execution_count": null, "id": "0a735927-a61d-418c-b615-9bd5a1711cf1", "metadata": {}, "outputs": [], "source": ["table_name_edb = \"complementary_ptype_collection_recommendations_v1\"\n", "staging_table_name_edb = \"complementary_ptype_collection_recommendations_v1_staging\"\n", "index_name = \"idx\"\n", "index_columns = []\n", "# [\"is_enabled\", \"ptype_id\", \"rank\"]"]}, {"cell_type": "code", "execution_count": null, "id": "2be33cb4-8c94-47b8-ba03-5576f44054b1", "metadata": {}, "outputs": [], "source": ["if index_columns:\n", "    print(1)"]}, {"cell_type": "code", "execution_count": null, "id": "c42a20fa-b63f-4be3-8317-ed3536dd1039", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "99aaa77c-27ad-4430-8809-61fa54b2d1bf", "metadata": {}, "outputs": [], "source": ["db_utils.push_to_database(\n", "    table_name_edb=table_name_edb,\n", "    staging_table_name_edb=staging_table_name_edb,\n", "    dataframe=push_df[push_cols],\n", "    index_name=index_name,\n", "    index_columns=index_columns,\n", "    dry_run=False,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "45e74f27-b63c-4c80-9863-384b77ea0b2a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f8771d99-c12f-483e-9e58-287acff9d1bc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "0a87f89e-1f19-474a-9867-13180911ab40", "metadata": {}, "source": ["### DATADOG"]}, {"cell_type": "code", "execution_count": null, "id": "1b8f75d5-1e12-458c-9766-0dd830d77fcc", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["dag_id = task_instance.split(\"__\")[0]\n", "task_id = task_instance.split(\"__\")[1]"]}, {"cell_type": "code", "execution_count": null, "id": "971e9451-b5df-450f-a999-6499de895b5f", "metadata": {}, "outputs": [], "source": ["!pip install -q datadog\n", "import enum\n", "import threading\n", "import pencilbox as pb\n", "from datadog import initialize, api\n", "\n", "\n", "class EventAlertType(enum.Enum):\n", "    Info = \"info\"\n", "    Warning = \"warning\"\n", "    Error = \"error\"\n", "    Success = \"success\"\n", "\n", "\n", "class EventPriority(enum.Enum):\n", "    Low = \"low\"\n", "    Normal = \"normal\"\n", "\n", "\n", "class DatadogPublishEvent:\n", "    _instance = None\n", "    _lock = threading.Lock()  # Class-level lock for thread safety\n", "\n", "    def __new__(cls, *args, **kwargs):\n", "        \"\"\"Ensure only one instance of the class is created (<PERSON><PERSON>).\"\"\"\n", "        if cls._instance is None:\n", "            with cls._lock:  # Ensure only one thread can create the instance\n", "                if cls._instance is None:  # Double-checked locking pattern\n", "                    cls._instance = super(DatadogPublishEvent, cls).__new__(cls)\n", "        return cls._instance\n", "\n", "    def __init__(self):\n", "        \"\"\"Initialize Datadog only once.\"\"\"\n", "        if not hasattr(self, \"initialized\"):\n", "            try:\n", "                self.options = {\n", "                    \"api_key\": pb.get_secret(\"dse/personalisation/datadog\")[\"api_key\"],\n", "                }\n", "                initialize(**self.options)\n", "                self.initialized = True  # Mark as initialized to avoid re-initialization\n", "            except Exception as e:\n", "                print(f\"Failed to initialize DatadogPublishEvent, error: {str(e)}\")\n", "\n", "    @staticmethod\n", "    def publish_event(\n", "        dag_id: str,\n", "        task_id: str,\n", "        run_id: str,\n", "        task_status,\n", "        message: str,\n", "        tags: list = None,\n", "        priority: EventPriority = EventPriority.Normal,\n", "        alert_type: EventAlertType = EventAlertType.Info,\n", "    ):\n", "        try:\n", "            title = f\"AIRFLOW_{dag_id}_{task_id}_{task_status}\"\n", "            aggregation_key = f\"{dag_id}_{run_id}\"\n", "            if not tags or len(tags) == 0:\n", "                tags = [\"environment:production\", \"source:airflow\", f\"dag_id: {dag_id}\"]\n", "\n", "            res = api.Event.create(\n", "                title=title,\n", "                text=message,\n", "                aggregation_key=aggregation_key,\n", "                priority=priority.value,\n", "                alert_type=alert_type.value,\n", "                service=dag_id,\n", "                tags=tags,\n", "            )\n", "            print(res)\n", "        except Exception as e:\n", "            print(\n", "                f\"Failed to publish datadog event, dag_id: {dag_id}, task_id: {task_id},run_id: {run_id}, task_status: {task_status} \"\n", "                f\"error: {str(e)}\"\n", "            )"]}, {"cell_type": "code", "execution_count": null, "id": "bcb11879-e282-4a8e-8912-057a3aef78bf", "metadata": {}, "outputs": [], "source": ["DatadogPublishEvent().publish_event(\n", "    dag_id=dag_id,\n", "    task_id=task_id,\n", "    run_id=run_id,\n", "    task_status=\"SUCCESS\",\n", "    message=\"..... dag ran successfully\",\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}