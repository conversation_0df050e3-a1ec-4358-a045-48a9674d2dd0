alert_configs:
  slack:
  - channel: bl-personalisation-dag-failures
dag_name: groups_npr
dag_type: etl
escalation_priority: low
execution_timeout: 831
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebook:
  parameters:
    run_id: '{{ run_id }}'
    task_instance: '{{ task_instance_key_str }}'
owner:
  email: <EMAIL>
  slack_id: U03SV2AN82C
path: data_science/personalisation/etl/groups_npr
paused: true
pool: data_science_pool
project_name: personalisation
schedule:
  end_date: '2025-07-18T00:00:00'
  interval: 30 21 * * *
  start_date: '2025-06-16T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- db_utils.py
tags: []
template_name: notebook
version: 21
