alert_configs:
  slack:
  - channel: bl-personalisation-dag-failures
dag_name: intelligent_pb_scores_v2
dag_type: etl
escalation_priority: low
execution_timeout: 836
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: very-high-mem
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
owner:
  email: <EMAIL>
  slack_id: U07MX3HK4M7
path: data_science/personalisation/etl/intelligent_pb_scores_v2
paused: false
pool: data_science_pool
project_name: personalisation
schedule:
  end_date: '2025-08-10T00:00:00'
  interval: 0 20 * * *
  start_date: '2025-06-11T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- kafka_utils.py
tags: []
template_name: notebook
version: 7
