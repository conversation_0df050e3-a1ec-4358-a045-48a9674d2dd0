alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: master_ptype_purchase_cycle
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: very-high-mem
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03SV2AN82C
path: data_science/personalisation/etl/master_ptype_purchase_cycle
paused: false
pool: data_science_pool
project_name: personalisation
schedule:
  end_date: '2025-08-08T00:00:00'
  interval: 30 23 * * *
  start_date: '2025-04-04T00:00:00'
schedule_type: fixed
sla: 121 minutes
support_files:
- db_utils.py
tags: []
template_name: notebook
version: 10
