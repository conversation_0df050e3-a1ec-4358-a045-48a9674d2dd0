alert_configs:
  slack:
  - channel: bl-personalisation-dag-failures
dag_name: npr_missing_or_new_ptypes_alerts
dag_type: etl
escalation_priority: low
execution_timeout: 1680
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: high-mem
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
owner:
  email: <EMAIL>
  slack_id: U07AD773UJZ
path: data_science/personalisation/etl/npr_missing_or_new_ptypes_alerts
paused: false
pool: data_science_pool
project_name: personalisation
schedule:
  end_date: '2025-09-24T00:00:00'
  interval: 30 3 * * *
  start_date: '2025-05-04T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 16
