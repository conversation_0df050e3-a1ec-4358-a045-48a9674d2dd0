{"cells": [{"cell_type": "code", "execution_count": null, "id": "4e9bbac8-00b6-40ef-83e1-3f2ca924b536", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "ac5f27c8-0f18-4dfd-9d17-15c40d33900b", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "d1039b46-b36f-4a65-b48a-b2d281144c29", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "50d889a2-e5da-45a8-babf-5c42fe942cf4", "metadata": {"tags": []}, "outputs": [], "source": ["!pip install -q scipy==1.8.0\n", "!pip install -q aiohttp==3.8.1\n", "!pip install -q fsspec==2023.1.0\n", "!pip install -q aiobotocore==2.4.2\n", "!pip install -q pymysql==1.0.2\n", "!pip install -q gremlinpython==3.6.2\n", "!pip install -q botocore==1.27.59\n", "!pip install -q progressbar2==4.2.0\n", "!pip install -q backoff==2.2.1\n", "!pip install -q pandas==1.5.1\n", "!pip install -q pg8000==1.29.4\n", "!pip install -q opensearch-py==2.1.1\n", "!pip install -q boto3==1.24.59\n", "!pip install -q requests-aws4auth==1.2.2\n", "!pip install -q s3transfer==0.6.0\n", "!pip install -q aenum==3.1.11\n", "!pip install -q scramp==1.4.4\n", "!pip install -q python-utils==3.5.2\n", "!pip install -q awswrangler==2.19.0\n", "!pip install -q s3fs==2023.1.0\n", "!pip install -q fasttext\n", "!pip install -q scikit-learn"]}, {"cell_type": "code", "execution_count": null, "id": "421c026c-70a1-4d69-a248-8584815faf42", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import re\n", "import ast\n", "from datetime import datetime\n", "import utils as utls\n", "import pencilbox as pb\n", "import itertools\n", "import fasttext\n", "import pickle\n", "import sqlalchemy as sa\n", "from src.models.autocomplete import Autocomplete, AutoTrieNode\n", "from src.models.tokenization import TrieTokenizer, TrieNode"]}, {"cell_type": "code", "execution_count": null, "id": "092ecb10-fe80-400d-84a2-c7cc658b825e", "metadata": {}, "outputs": [], "source": ["s3_access_iam_role = \"arn:aws:iam::442534439095:role/redshift-unload-copy-role\"\n", "# bucket = \"prod-dse-redshift-adhoc\"\n", "bucket = \"prod-dse-projects\"\n", "con = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "6612acd0-4c55-4941-8da8-9905bad958ba", "metadata": {}, "outputs": [], "source": ["date = datetime.now().strftime(\"%Y_%m_%d\")\n", "# date = '2023_08_11'\n", "date"]}, {"cell_type": "code", "execution_count": null, "id": "16d50f2b-c98b-4119-a751-0d2ecb84ad04", "metadata": {}, "outputs": [], "source": ["prefix = f\"data-science/ner/autocomplete_model/\" + date + \"/\"\n", "s3_dump_path = f\"s3://{bucket}/{prefix}\"\n", "print(s3_dump_path)\n", "pb.from_s3(bucket, prefix, \"/tmp/autocomplete.pkl\")\n", "\n", "# prefix = f\"data-science/ner/spell_correct_autocomplete_model/\" + date + \"/\"\n", "# s3_dump_path = f\"s3://{bucket}/{prefix}\"\n", "# print(s3_dump_path)\n", "# pb.from_s3(bucket, prefix, \"/tmp/spell_correct_autocomplete.pkl\")\n", "\n", "prefix = f\"data-science/ner/tokenization_model/\" + date + \"/\"\n", "s3_dump_path = f\"s3://{bucket}/{prefix}\"\n", "print(s3_dump_path)\n", "pb.from_s3(bucket, prefix, \"/tmp/tokenization_model.pkl\")\n", "\n", "# prefix = f\"data-science/ner/ft_mdata_model/\" + date + \"/\"\n", "# s3_dump_path = f\"s3://{bucket}/{prefix}\"\n", "# print(s3_dump_path)\n", "# pb.from_s3(bucket, prefix, \"/tmp/ft_mdata_model.bin\")\n", "\n", "prefix = f\"data-science/ner/ft_mdata_model_quantized/\" + date + \"/\"\n", "s3_dump_path = f\"s3://{bucket}/{prefix}\"\n", "print(s3_dump_path)\n", "pb.from_s3(bucket, prefix, \"/tmp/ft_mdata_model_quantized.bin\")"]}, {"cell_type": "code", "execution_count": null, "id": "e5e8ae5f-b268-4cf9-9f39-b5a096677d19", "metadata": {}, "outputs": [], "source": ["with open(\"/tmp/autocomplete.pkl\", \"rb\") as f:\n", "    autocomplete = pickle.load(f)\n", "\n", "# with open(\"/tmp/spell_correct_autocomplete.pkl\", \"rb\") as f:\n", "#     spell_correct_autocomplete = pickle.load(f)\n", "\n", "with open(\"/tmp/tokenization_model.pkl\", \"rb\") as f:\n", "    tokenization_model = pickle.load(f)\n", "\n", "# model = fasttext.load_model('/tmp/ft_mdata_model.bin')\n", "model_quantized = fasttext.load_model(\"/tmp/ft_mdata_model_quantized.bin\")"]}, {"cell_type": "markdown", "id": "1ca78234-5391-41fc-9ead-bc9cc80bb084", "metadata": {}, "source": ["### Steps:\n", "     0 - autocomplete gave results\n", "     1 - no valid tokens found\n", "     2 - fasttext model gave results"]}, {"cell_type": "code", "execution_count": null, "id": "1aa1d4ad-7279-46c7-a453-8f2f87337658", "metadata": {}, "outputs": [], "source": ["def predict_ner(query):\n", "    step = 0\n", "    complete_queries, ner_res = autocomplete.search(query)\n", "    if len(ner_res) == 0:\n", "        step = 1\n", "        # complete_queries,spell_ner_res = spell_correct_autocomplete.search(query)\n", "        # if len(spell_ner_res) == 0:\n", "        # step = 2\n", "        valid_tokens_found = tokenization_model.tokenize(query)\n", "        if len(valid_tokens_found) == 0:\n", "            return (), np.array([]), step\n", "        elif len(valid_tokens_found) == 1:\n", "            step = 0\n", "            complete_queries, ner_res = autocomplete.search(valid_tokens_found[0])\n", "            if len(ner_res) == 0:\n", "                step = 2\n", "                return (\n", "                    model_quantized.predict(\" \".join(valid_tokens_found), k=5, threshold=0.01),\n", "                    valid_tokens_found,\n", "                    step,\n", "                )\n", "            return complete_queries, ner_res, step\n", "        else:\n", "            step = 2\n", "            # return model.predict(' '.join(valid_tokens_found),k=-1,threshold=0.5), step\n", "            return (\n", "                model_quantized.predict(\" \".join(valid_tokens_found), k=5, threshold=0.01),\n", "                valid_tokens_found,\n", "                step,\n", "            )\n", "    else:\n", "        return complete_queries, ner_res, step\n", "    # else:\n", "    #     return complete_queries,ner_res, step"]}, {"cell_type": "code", "execution_count": null, "id": "076cc570-bd18-4d39-b447-eed62b31bd22", "metadata": {}, "outputs": [], "source": ["predict_ner(\"falhari\")"]}, {"cell_type": "code", "execution_count": null, "id": "225cfd6a-3efa-484a-89e8-676c7ed73995", "metadata": {}, "outputs": [], "source": ["predict_ner(\"falhari n\")"]}, {"cell_type": "code", "execution_count": null, "id": "513b6f22-6bb8-40bd-b0a2-69932f78540c", "metadata": {}, "outputs": [], "source": ["predict_ner(\"falhai n\")"]}, {"cell_type": "code", "execution_count": null, "id": "c3a2d3e0-d0db-47d3-a4c0-179a47501728", "metadata": {}, "outputs": [], "source": ["predict_ner(\"multicolor kite\")"]}, {"cell_type": "code", "execution_count": null, "id": "69e5c40b-1e70-435d-9c73-94e6e6f3fa00", "metadata": {}, "outputs": [], "source": ["ner_keywords_query = utls.read_sql_query(f\"{cwd}/queries/ner_keywords.sql\")\n", "ner_keywords = pd.read_sql_query(sa.text(ner_keywords_query), con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "834ed912-6b8a-4b3d-8b7f-ca13419ac6ff", "metadata": {}, "outputs": [], "source": ["ner_keywords = (\n", "    ner_keywords.groupby([\"input_keyword\", \"platform\"])\n", "    .agg(\n", "        {\n", "            \"control_searches\": sum,\n", "            \"test_searches\": sum,\n", "            \"control_atc\": sum,\n", "            \"test_atc\": sum,\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7253436d-7ed0-4165-af5f-9f6ccb102e5b", "metadata": {}, "outputs": [], "source": ["ner_keywords"]}, {"cell_type": "code", "execution_count": null, "id": "56fa1640-b19e-459a-b048-92e289781b90", "metadata": {}, "outputs": [], "source": ["ner_keywords[\"module_classification\"] = ner_keywords.apply(\n", "    lambda row: predict_ner(row.input_keyword)[2], axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3dbcc7d0-0f76-4126-9fb5-444509249f33", "metadata": {}, "outputs": [], "source": ["ner_keywords.loc[\n", "    ner_keywords.module_classification == 0, \"autocomplete_results\"\n", "] = ner_keywords.loc[ner_keywords.module_classification == 0].apply(\n", "    lambda row: predict_ner(row.input_keyword)[0], axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2d608243-55b3-4a48-9a3e-682b5464cd0a", "metadata": {}, "outputs": [], "source": ["prefix = f\"data-science/ner/autocomplete_final_input_data/\" + date + \"/\"\n", "s3_dump_path = f\"s3://{bucket}/{prefix}\"\n", "print(s3_dump_path)\n", "query_data = utls.read_parquet_data_from_s3(s3_dump_path)"]}, {"cell_type": "code", "execution_count": null, "id": "db971efc-a7df-484d-8dc2-059a1a20f224", "metadata": {}, "outputs": [], "source": ["query_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8016c8ae-e11e-4085-b83d-01c7135b9260", "metadata": {}, "outputs": [], "source": ["final_df = ner_keywords.merge(\n", "    query_data[[\"query_name\", \"priority_ac\"]].drop_duplicates(),\n", "    left_on=[\"autocomplete_results\"],\n", "    right_on=[\"query_name\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5aa824a0-bd7c-4eb7-aa08-48e7c392ab87", "metadata": {}, "outputs": [], "source": ["final_df.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "096acbdc-d5e2-4206-9e97-9bbc84f20761", "metadata": {}, "outputs": [], "source": ["final_df.loc[final_df.module_classification == 2, \"priority_ac\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "f209b5ad-dc5a-44a8-ba30-0d06798848bf", "metadata": {}, "outputs": [], "source": ["final_df.loc[final_df.priority_ac.isna(), \"module_classification\"].value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "9bc4d672-1983-4551-862b-e7b44b9cb11b", "metadata": {}, "outputs": [], "source": ["final_df.loc[(final_df.priority_ac.isna()) & (final_df.module_classification == 0)]"]}, {"cell_type": "code", "execution_count": null, "id": "59fe4a78-3de9-4fe4-93f3-d83dcf33d0b5", "metadata": {}, "outputs": [], "source": ["final_df.loc[final_df.autocomplete_results.isna(), \"module_classification\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "a54963b1-f1c8-4aba-a6f9-11a96083d9b2", "metadata": {}, "outputs": [], "source": ["final_df[\"priority_ac\"] = final_df.priority_ac.fillna(-1)"]}, {"cell_type": "code", "execution_count": null, "id": "80c1da62-fd01-4ecf-9a14-f5ceb3d0bf1c", "metadata": {}, "outputs": [], "source": ["final_df[\"autocomplete_results\"] = final_df.autocomplete_results.fillna(\"None\")"]}, {"cell_type": "code", "execution_count": null, "id": "1c91ac63-4569-4b9b-a0eb-332eb6a03107", "metadata": {}, "outputs": [], "source": ["final_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "535bc161-a523-4591-95da-63dace6c7afe", "metadata": {}, "outputs": [], "source": ["final_df[\"priority_ac\"] = final_df.priority_ac.astype(\"int\")"]}, {"cell_type": "code", "execution_count": null, "id": "15ed3211-addc-4fd5-b381-8241848bf913", "metadata": {}, "outputs": [], "source": ["final_df.rename(columns={\"priority_ac\": \"synthetic_tag_type\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "35740e58-6bdf-4993-ac3d-033fef5d2c97", "metadata": {}, "outputs": [], "source": ["columns = [\n", "    \"input_keyword\",\n", "    \"autocomplete_results\",\n", "    \"platform\",\n", "    \"control_searches\",\n", "    \"test_searches\",\n", "    \"control_atc\",\n", "    \"test_atc\",\n", "    \"module_classification\",\n", "    \"synthetic_tag_type\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "1ebf58f0-dcbc-4e5d-896d-a909735a2aaf", "metadata": {}, "outputs": [], "source": ["final_df[columns]"]}, {"cell_type": "code", "execution_count": null, "id": "dfa7ce9d-49e0-4509-98c5-5f4013a7e608", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"playground\",\n", "    \"table_name\": \"keyword_classification_ner\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"input_keyword\", \"type\": \"varchar\", \"description\": \"input_keyword\"},\n", "        {\n", "            \"name\": \"autocomplete_results\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"autocomplete_results\",\n", "        },\n", "        {\"name\": \"platform\", \"type\": \"varchar\", \"description\": \"platform\"},\n", "        {\n", "            \"name\": \"control_searches\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"control_searches\",\n", "        },\n", "        {\"name\": \"test_searches\", \"type\": \"integer\", \"description\": \"test_searches\"},\n", "        {\"name\": \"control_atc\", \"type\": \"integer\", \"description\": \"control_atc\"},\n", "        {\"name\": \"test_atc\", \"type\": \"integer\", \"description\": \"test_atc\"},\n", "        {\n", "            \"name\": \"module_classification\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"module_classification\",\n", "        },\n", "        # {\"name\": \"autocomplete_results\", \"type\": \"varchar\", \"description\": \"autocomplete_results\"},\n", "        # {\"name\": \"query_name\", \"type\": \"float\", \"varchar\": \"query_name\"},\n", "        {\n", "            \"name\": \"synthetic_tag_type\",\n", "            \"type\": \"varchar\",\n", "            \"integer\": \"tag_type\",\n", "            \"description\": \"keyword_synthetic_tag_type\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"input_keyword\", \"platform\"],\n", "    \"sortkey\": [\"test_searches\"],\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Keyword classification NER\",\n", "}\n", "\n", "pb.to_redshift(final_df[columns], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "7e1392a9-97cd-45e5-b040-c5510688a58a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}