with mapping as(
    select
        m.metadata_type tag_type,
        m.name,
        pm.metadata_id,
        pm.product_id pids_mapped,
        pm.source
    from
        lake_product_knowledge_metadata.product_metadata_relationship pm
        join lake_product_knowledge_metadata.metadata m on m.id = pm.metadata_id
        and pm.active and m.is_enabled and pm.lake_active_record and m.lake_active_record and m.kind = 'METADATA'
    group by
        1,
        2,
        3,
        4,
        5
),
group_id_pid_map as (
    select
        product_id,
        group_id
    from
        lake_cms.gr_group_product_mapping
    group by
        1,
        2
),
product_properties as (
    select
        dp.product_id,
        dp.product_name,
        dp.l0_category,
        dp.l1_category,
        dp.l2_category,
        dp.product_type,
        dp.brand_name brand,
        dp.l0_category_id,
        dp.l1_category_id,
        dp.l2_category_id,
        dp.product_type_id,
        dp.brand_id,
        dp.is_combo,
        p.variant,
        g.group_id,
        -- lmt.relevant_only_if_bought_before,
        -- lmt.non_staple_grocery,
        case
            when (dp.brand_name = '') or (dp.brand_name = ' ') or (dp.brand_name is null) then 'none'
            else dp.brand_name
        end as brand_,
        case
            when (p.variant = '')
            or (p.variant = ' ')
            or (p.variant is null) then 'none'
            else p.variant
        end as variant_
    from
        dwh.dim_product dp
        join lake_cms.gr_product p on dp.product_id = p.id
        join group_id_pid_map as g on dp.product_id = g.product_id
        -- join consumer.personlization_team_l1_metadata_tags lmt on dp.l1_category_id = lmt.l1_category_id
    where
        is_current
        and is_product_enabled
        and dp.product_type_id not in (
            -- excluded ptypes from previously bought
            11780,
            -- share deal
            459,
            -- Freebie
            11778,
            -- Free
            11791,
            -- Grofers Go Gift Items
            11927,
            -- Special Deal
            11929,
            -- VIP Deals
            11930,
            -- Click Deals
            11932,
            -- Whatsapp Deals
            11936,
            -- List Deals,
            12184
            -- Print as a service
            -- 10319,
            -- -- cigarettes
            -- 9851,
            -- --Cigar
            -- 119 --Tobacco
        )
        and p.enabled_flag
        and lower(dp.l0_category) not in ('specials', 'best value','bistro','hp','trial new tree')
        and dp.brand_name not in ('Blinkit')
        -- and dp.product_type not in ('Book')
)
select
    p.*,
    lower(
        p.product_type || ' ' || p.brand_ || ' ' || p.variant_
    ) as derived_product_name, 
    m.tag_type,
    m.metadata_id,
    m.name tag_name,
    m.source tag_source
from
    product_properties p
    inner join mapping m on p.product_id = m.pids_mapped