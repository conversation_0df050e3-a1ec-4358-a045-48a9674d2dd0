{"cells": [{"cell_type": "code", "execution_count": null, "id": "06b75f49-de12-4de4-9ef4-fba7902608e6", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import warnings\n", "\n", "# warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "4786acb3-bd44-4ad3-b30e-be24c82c2f10", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "d12bb3c7-c545-44a7-b2c5-211046bc4ac7", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "bfd49558-e5f7-43de-926e-104ff1d81406", "metadata": {}, "outputs": [], "source": ["cwd"]}, {"cell_type": "code", "execution_count": null, "id": "a1384695-ee95-4bef-a090-afb0835cb558", "metadata": {"tags": []}, "outputs": [], "source": ["!pip install -q scipy==1.8.0\n", "!pip install -q aiohttp==3.8.1\n", "!pip install -q fsspec==2023.1.0\n", "!pip install -q aiobotocore==2.4.2\n", "!pip install -q pymysql==1.0.2\n", "!pip install -q gremlinpython==3.6.2\n", "!pip install -q botocore==1.27.59\n", "!pip install -q progressbar2==4.2.0\n", "!pip install -q backoff==2.2.1\n", "!pip install -q pandas==1.5.1\n", "!pip install -q pg8000==1.29.4\n", "!pip install -q opensearch-py==2.1.1\n", "!pip install -q boto3==1.24.59\n", "!pip install -q requests-aws4auth==1.2.2\n", "!pip install -q s3transfer==0.6.0\n", "!pip install -q aenum==3.1.11\n", "!pip install -q scramp==1.4.4\n", "!pip install -q python-utils==3.5.2\n", "!pip install -q awswrangler==2.19.0\n", "!pip install -q s3fs==2023.1.0"]}, {"cell_type": "code", "execution_count": null, "id": "54650d3b-b4b3-42da-b409-7bc82cfe463a", "metadata": {"tags": []}, "outputs": [], "source": ["# !pip install -U sentence-transformers\n", "# !pip install faiss-cpu==1.8.0"]}, {"cell_type": "code", "execution_count": null, "id": "962f43c3-06a4-4ffb-a80a-9ba333cd14bb", "metadata": {}, "outputs": [], "source": ["# !pip install huggingface_hub==0.25.2"]}, {"cell_type": "code", "execution_count": null, "id": "6f06d4a2-b15a-454a-93ee-292c5092f8ef", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9a2591ea-a9fa-4ad4-bdaf-f5bc9b90c0fa", "metadata": {}, "outputs": [], "source": ["!pip install onnxruntime"]}, {"cell_type": "code", "execution_count": null, "id": "47e526e0-9252-4ed9-88bc-bde93e1e12db", "metadata": {}, "outputs": [], "source": ["!pip install transformers==4.46.3"]}, {"cell_type": "code", "execution_count": null, "id": "fa0730da-1328-4273-9e37-247d4da52142", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d4f5090d-c242-4776-a37f-c50fdf61f69d", "metadata": {"tags": []}, "outputs": [], "source": ["# !pip install -q optimum[onnxruntime]>=1.10.0\n", "\n", "!pip uninstall awswrangler --y\n", "!pip install -q awswrangler==2.19.0"]}, {"cell_type": "code", "execution_count": null, "id": "0a0501bc-fbf2-41c7-a444-0bbe4cb22b1d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6a575ec3-84de-4aaa-bde7-d9c99046d332", "metadata": {}, "outputs": [], "source": ["# !pip install txtai[ann] --no-cache-dir"]}, {"cell_type": "code", "execution_count": null, "id": "8f711e46-db6f-4a7f-bcd3-264e166d908c", "metadata": {}, "outputs": [], "source": ["!pip uninstall sqlalchemy -y\n", "!pip install sqlalchemy==1.3.23"]}, {"cell_type": "code", "execution_count": null, "id": "a111cf67-156a-4e31-9c2a-32f56fd0b6c9", "metadata": {}, "outputs": [], "source": ["!pip install hnswlib --no-cache-dir"]}, {"cell_type": "code", "execution_count": null, "id": "8fb7d420-616a-407d-b1c1-e8d641cf9d1f", "metadata": {}, "outputs": [], "source": ["!pip install -q pqdm"]}, {"cell_type": "code", "execution_count": null, "id": "1a9aa74f-d981-46a6-8f60-1d5af4e953e9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "13e039a2-faf4-40d7-80b3-72c8d2eba312", "metadata": {"tags": []}, "outputs": [], "source": ["!pip list"]}, {"cell_type": "code", "execution_count": null, "id": "6e6e0083-ce3a-48b3-93e8-53ed487def47", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4d3baa0e-0890-44e3-8682-03d2a7756bd6", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import re\n", "import ast\n", "from datetime import datetime, timedelta\n", "import utils as utls\n", "import pencilbox as pb\n", "import itertools\n", "\n", "# import fasttext\n", "import pickle\n", "\n", "# import sqlalchemy as sa\n", "# from src.models.autocomplete import Autocomplete, AutoTrieNode\n", "# from src.models.tokenization import TrieToken<PERSON>, TrieNode"]}, {"cell_type": "code", "execution_count": null, "id": "66cfb15c-3351-44ff-a374-b26e6cb09a66", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "975db552-cbe9-4bb7-bc20-a57114aa7b1e", "metadata": {}, "outputs": [], "source": ["# from txtai.ann import ANNFactory"]}, {"cell_type": "code", "execution_count": null, "id": "c909e6d5-f491-4058-9629-fa0a923b29b5", "metadata": {}, "outputs": [], "source": ["from src.models.optim_sentence_transformers import (\n", "    SentenceTransformerOptim,\n", "    # optimize_model,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "cefbe7e3-e250-4598-ac3b-0b2375e4b816", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "85ca109a-ceaf-4c3c-9f14-c091368890a2", "metadata": {}, "outputs": [], "source": ["import json"]}, {"cell_type": "code", "execution_count": null, "id": "0aae84c3-cdb2-4c08-8c28-2da463a8ff38", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)\n", "pd.set_option(\"display.max_rows\", None)\n", "pd.set_option(\"display.float_format\", lambda x: \"%.5f\" % x)\n", "pd.set_option(\"display.max_colwidth\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "c5f4ee53-4955-474c-884f-0dd473095666", "metadata": {}, "outputs": [], "source": ["s3_access_iam_role = \"arn:aws:iam::442534439095:role/redshift-unload-copy-role\"\n", "# bucket = \"prod-dse-redshift-adhoc\"\n", "bucket = \"prod-dse-projects\"\n", "# con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "aeaa52e3-a07f-42a6-83ea-98b7ffa1dde2", "metadata": {}, "outputs": [], "source": ["date = datetime.now().strftime(\"%Y_%m_%d\")\n", "# date = (datetime.now() - <PERSON><PERSON><PERSON>(days=1)).strftime(\"%Y_%m_%d\")\n", "# date = \"2025_03_21\"\n", "date"]}, {"cell_type": "code", "execution_count": null, "id": "732f33bc-e62c-4f84-a7ca-6809bfcc0aed", "metadata": {}, "outputs": [], "source": ["from tqdm import tqdm"]}, {"cell_type": "code", "execution_count": null, "id": "1166515e-5d7c-4176-8369-efbdd68092dc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "25d4d211-320c-4521-9a1a-e114613e14ae", "metadata": {}, "outputs": [], "source": ["from pqdm.processes import pqdm"]}, {"cell_type": "code", "execution_count": null, "id": "d77d5eb8-f896-497f-a380-ed95548dd3a9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "50711f10-057d-4112-9155-84810e283da5", "metadata": {}, "outputs": [], "source": ["# import faiss\n", "# from sentence_transformers import SentenceTransformer\n", "import numpy as np\n", "import hnswlib\n", "\n", "\n", "# def create_hnsw_index(embeddings):\n", "\n", "#     index = ANNFactory.create({\"backend\": \"hnsw\", \"dimensions\": embeddings.shape[1]})\n", "#     index.index(embeddings)\n", "\n", "#     return index\n", "\n", "\n", "def create_hnswlib_index(embeddings):\n", "\n", "    # Initialize the index\n", "    hnsw_index = hnswlib.Index(space=hnsw_index_config[\"space\"], dim=hnsw_index_config[\"dim\"])\n", "    # We must specify the maximum number of elements (capacity) for the index at initialization.\n", "    max_elements = 500000  # for example, we plan to index up to 10,000 vectors\n", "    hnsw_index.init_index(\n", "        max_elements=hnsw_index_config[\"max_elements\"],\n", "        M=hnsw_index_config[\"M\"],\n", "        ef_construction=hnsw_index_config[\"ef_construction\"],\n", "        random_seed=hnsw_index_config[\"random_seed\"],\n", "    )\n", "\n", "    print(\n", "        \"Index initialized with parameters:\",\n", "        f\"space={hnsw_index_config['space']}, dim={hnsw_index_config['dim']}, M={hnsw_index_config['M']}, efConstruction={hnsw_index_config['ef_construction']}, seed={hnsw_index_config['random_seed']}\",\n", "    )\n", "\n", "    hnsw_index.add_items(embeddings, np.arange(embeddings.shape[0]))\n", "    print(hnsw_index.element_count)\n", "\n", "    return hnsw_index"]}, {"cell_type": "code", "execution_count": null, "id": "fd36d1d9-589b-4c72-886f-db2db2264aa0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "09f5e2a9-6b81-4554-96ae-58fcb2e195c9", "metadata": {"tags": []}, "source": ["# Helper Functions"]}, {"cell_type": "code", "execution_count": null, "id": "7b9eea17-7514-4cdf-9547-175b1f4f94e9", "metadata": {}, "outputs": [], "source": ["# def get_text_embedding(query_list, model, batch_size=2048):\n", "#     assert type(query_list) == list, \"1st arguement should be a list[str]\"\n", "#     return model.encode(query_list, show_progress_bar=True, batch_size=512)"]}, {"cell_type": "code", "execution_count": null, "id": "7a0e228e-a8a7-46be-9fcd-0772c2e28fac", "metadata": {}, "outputs": [], "source": ["# from numpy import dot\n", "# from numpy.linalg import norm\n", "\n", "# import sentence_transformers\n", "\n", "\n", "# def get_similarity_scores(a, b):\n", "#     cos_sim = dot(a, b) / (norm(a) * norm(b))\n", "#     return cos_sim"]}, {"cell_type": "code", "execution_count": null, "id": "57f8d7d9-c3c9-43ed-ae97-68857049eeb7", "metadata": {}, "outputs": [], "source": ["# from sentence_transformers import SentenceTransformer"]}, {"cell_type": "code", "execution_count": null, "id": "b9406cce-1c0a-49f5-9837-22f303eb557a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fb578859-5543-4144-a387-61707e8ff623", "metadata": {}, "outputs": [], "source": ["# MODEL_DIRECTORY = \"UserQueryCategoryV2\""]}, {"cell_type": "code", "execution_count": null, "id": "0dcde4d1-f697-4054-92e9-e1c5e112a501", "metadata": {}, "outputs": [], "source": ["# ONNX_MODEL_DIRECTORY = f\"onnx_model_{MODEL_DIRECTORY}\"\n", "# print(ONNX_MODEL_DIRECTORY)"]}, {"cell_type": "markdown", "id": "c94d3251-1d8b-4675-9eb9-dc77bea5ba34", "metadata": {"tags": []}, "source": ["## Pull Onnx model from s3"]}, {"cell_type": "code", "execution_count": null, "id": "1eac4fe7-545f-441e-8c27-c129fbc6fff8", "metadata": {}, "outputs": [], "source": ["import awswrangler as wr"]}, {"cell_type": "code", "execution_count": null, "id": "183d4d9f-c118-4fe7-adb3-e9b70f9abfa5", "metadata": {}, "outputs": [], "source": ["wr.s3.list_objects(\n", "    \"s3://prod-dse-projects/data-science-gpu-write/data-science/ner/qtp_model_trials/\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "cbc09311-1298-48df-af34-5644cde0418d", "metadata": {}, "outputs": [], "source": ["MODEL_DIRECTORY = \"mxbai_embed_large_v1_exp_05_2024_12_15\"\n", "\n", "ONNX_MODEL_DIRECTORY = f\"onnx_{MODEL_DIRECTORY}\"\n", "print(ONNX_MODEL_DIRECTORY)"]}, {"cell_type": "code", "execution_count": null, "id": "9788e8b9-534e-4136-b241-02a5e0c191a3", "metadata": {}, "outputs": [], "source": ["MODEL_S3_PATH = f\"s3://prod-dse-projects/data-science-gpu-write/data-science/ner/qtp_model_trials/{ONNX_MODEL_DIRECTORY}.zip\"\n", "print(MODEL_S3_PATH)"]}, {"cell_type": "code", "execution_count": null, "id": "01778e5c-e4d3-48f5-9c3b-ef3599757ebf", "metadata": {}, "outputs": [], "source": ["wr.s3.download(MODEL_S3_PATH, f\"/tmp/{ONNX_MODEL_DIRECTORY}.zip\")"]}, {"cell_type": "code", "execution_count": null, "id": "4f6c042d-843e-4ddc-b4c5-67bc24387bee", "metadata": {}, "outputs": [], "source": ["# prefix = f\"data-science/ner/query_to_product/{ONNX_MODEL_DIRECTORY}/\"\n", "# print(prefix)"]}, {"cell_type": "code", "execution_count": null, "id": "9fa5ed0c-1817-44ed-9dfb-3679ab99fab3", "metadata": {}, "outputs": [], "source": ["# pb.from_s3(bucket, prefix, f\"/tmp/{ONNX_MODEL_DIRECTORY}.zip\")"]}, {"cell_type": "code", "execution_count": null, "id": "81f1353f-197f-4678-817a-3a8079c96563", "metadata": {}, "outputs": [], "source": ["!ls -lh /tmp/"]}, {"cell_type": "code", "execution_count": null, "id": "16bc12c2-6b6d-4a07-87ca-e4459041e4c5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "75cec97c-f757-47e7-9dd4-727df49f1fb0", "metadata": {"tags": []}, "source": ["## unzip model file"]}, {"cell_type": "code", "execution_count": null, "id": "8a1c8ef1-9034-4aa8-adef-6d3095689718", "metadata": {}, "outputs": [], "source": ["import shutil\n", "\n", "shutil.unpack_archive(f\"/tmp/{ONNX_MODEL_DIRECTORY}.zip\", f\"/tmp/{ONNX_MODEL_DIRECTORY}\", \"zip\")"]}, {"cell_type": "code", "execution_count": null, "id": "cbda9037-fda3-4616-9434-1a95fd2d1d33", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "e3e00cb0-d21f-4168-976e-8a1c043b4fb6", "metadata": {"tags": []}, "source": ["## Load model file"]}, {"cell_type": "code", "execution_count": null, "id": "939e9df5-f17e-4d17-a86f-143fcaa7e1e7", "metadata": {}, "outputs": [], "source": ["optim_model = SentenceTransformerOptim(f\"/tmp/{ONNX_MODEL_DIRECTORY}\")"]}, {"cell_type": "markdown", "id": "18441e60-33d9-43bd-bc9b-7c89aafaa52a", "metadata": {"tags": []}, "source": ["# Load Semantic-NER Index "]}, {"cell_type": "code", "execution_count": null, "id": "cacb3823-c836-49fd-bc84-4ca1dd43c259", "metadata": {}, "outputs": [], "source": ["date"]}, {"cell_type": "code", "execution_count": null, "id": "7e35c982-72de-490b-a55a-89774a1c6d11", "metadata": {}, "outputs": [], "source": ["prefix = f\"data-science/ner/semantic_synthetic_data/\" + date + \"/\"\n", "s3_dump_path = f\"s3://{bucket}/{prefix}\"\n", "print(s3_dump_path)\n", "\n", "semantic_synthetic_data = utls.read_parquet_data_from_s3(s3_dump_path)\n", "print(semantic_synthetic_data.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "b46dd515-89a4-493d-a5dd-0948111c5298", "metadata": {}, "outputs": [], "source": ["semantic_synthetic_data[\"len_label\"] = semantic_synthetic_data.label_string.str.len()\n", "semantic_synthetic_data = semantic_synthetic_data.sort_values(\"len_label\", ascending=False)\n", "semantic_synthetic_data = semantic_synthetic_data.drop_duplicates(\"query_string\", keep=\"first\")\n", "print(semantic_synthetic_data.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "330802d3-678b-49d5-a61d-22d9ccbc5cb7", "metadata": {}, "outputs": [], "source": ["# semantic_synthetic_data = semantic_synthetic_data.head(5000)"]}, {"cell_type": "code", "execution_count": null, "id": "1fb1bc90-685d-4c58-93ff-9c8e0a6c0b1f", "metadata": {}, "outputs": [], "source": ["semantic_synthetic_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "b43798f5-80ea-4644-8422-30de90af6e95", "metadata": {}, "outputs": [], "source": ["sentences = semantic_synthetic_data.query_string.values"]}, {"cell_type": "code", "execution_count": null, "id": "a0c7eb0e-b045-4126-886b-f1ec4bd4bef8", "metadata": {}, "outputs": [], "source": ["synthetic_queries = np.array(semantic_synthetic_data.label_string.values)"]}, {"cell_type": "code", "execution_count": null, "id": "0d3b29c9-9562-4da7-9156-7a97fc5df510", "metadata": {}, "outputs": [], "source": ["synthetic_queries_pids = np.array(semantic_synthetic_data.product_id.values)"]}, {"cell_type": "code", "execution_count": null, "id": "f1eb6ba4-6b80-41e9-94fb-aba1f1d7c667", "metadata": {}, "outputs": [], "source": ["len(synthetic_queries)"]}, {"cell_type": "code", "execution_count": null, "id": "2c376c53-ea54-4971-80ef-b310933fb9bb", "metadata": {}, "outputs": [], "source": ["print(semantic_synthetic_data.shape)"]}, {"cell_type": "markdown", "id": "312ef1d9-8f80-4df5-a387-cf7c8fe249c1", "metadata": {"tags": []}, "source": ["# Encode Synthetic Queries"]}, {"cell_type": "code", "execution_count": null, "id": "f2eb73cc-da46-49f8-93fc-ff6d44d2762d", "metadata": {}, "outputs": [], "source": ["len(sentences)"]}, {"cell_type": "code", "execution_count": null, "id": "413b6672-5f81-4330-8bb6-22522cec1a02", "metadata": {"tags": []}, "outputs": [], "source": ["# embeddings = optim_model.encode(sentences, show_progress_bar=True, batch_size=1, normalize_embeddings=True)\n", "# embeddings = model.encode(sentences, show_progress_bar=True, batch_size=1024)"]}, {"cell_type": "code", "execution_count": null, "id": "9d75f3ae-3f9c-414c-9d0b-1c1bb103a586", "metadata": {}, "outputs": [], "source": ["def encode_query(query):\n", "    return optim_model.encode(query, normalize_embeddings=True)"]}, {"cell_type": "code", "execution_count": null, "id": "a69c2191-b2cb-4f1c-806f-9ce2ad33f491", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "af926ac0-c1f4-4a5e-be24-cc1810f18020", "metadata": {}, "outputs": [], "source": ["prefix = f\"data-science/ner/{ONNX_MODEL_DIRECTORY}_embeddings_dict_v2/\"\n", "\n", "try:\n", "    pb.from_s3(bucket, prefix, f\"/tmp/{ONNX_MODEL_DIRECTORY}_embeddings_dict_v2.pkl\")\n", "\n", "    with open(f\"/tmp/{ONNX_MODEL_DIRECTORY}_embeddings_dict_v2.pkl\", \"rb\") as f:\n", "        all_embeddings_dict = pickle.load(f)\n", "\n", "    print(len(all_embeddings_dict))\n", "except:\n", "    all_embeddings_dict = {}\n", "    print(len(all_embeddings_dict))"]}, {"cell_type": "code", "execution_count": null, "id": "7659c290-2cae-4ef7-ad3a-db8bd022313f", "metadata": {}, "outputs": [], "source": ["entities_to_encode = [x for x in set(sentences) if x not in all_embeddings_dict]\n", "print(len(entities_to_encode))\n", "\n", "if len(entities_to_encode) > 0:\n", "\n", "    entities_embeddings = np.array(pqdm(entities_to_encode, encode_query, n_jobs=8))\n", "\n", "    all_embeddings_dict.update(dict(zip(entities_to_encode, entities_embeddings)))\n", "\n", "    import pickle\n", "\n", "    with open(f\"/tmp/{ONNX_MODEL_DIRECTORY}_embeddings_dict_v2.pkl\", \"wb\") as f:\n", "        pickle.dump(all_embeddings_dict, f)\n", "\n", "    prefix = f\"data-science/ner/{ONNX_MODEL_DIRECTORY}_embeddings_dict_v2/\"\n", "\n", "    pb.to_s3(f\"/tmp/{ONNX_MODEL_DIRECTORY}_embeddings_dict_v2.pkl\", bucket, prefix)"]}, {"cell_type": "code", "execution_count": null, "id": "28e57abb-81a4-4b80-b5c8-4bc5766b839a", "metadata": {}, "outputs": [], "source": ["embeddings = np.array([all_embeddings_dict[x] for x in sentences])\n", "print(embeddings.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "f1622fec-5cfa-4490-876e-5fc2ada4e8db", "metadata": {"tags": []}, "outputs": [], "source": ["# embeddings = np.array(pqdm(sentences, encode_query, n_jobs=4))"]}, {"cell_type": "code", "execution_count": null, "id": "7dd480f4-1d19-4e10-9b82-28ecfc962424", "metadata": {}, "outputs": [], "source": ["print(embeddings.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "0769d779-0b3f-49ee-bff3-c0f20249a227", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "40b464a3-6c9f-49a7-bbfa-b217c5fc9c4e", "metadata": {}, "source": ["# Try txtai indexing and searching"]}, {"cell_type": "code", "execution_count": null, "id": "743221ad-f8ec-48ba-8c85-d386a6f64d75", "metadata": {}, "outputs": [], "source": ["hnsw_index_config = {\n", "    \"dim\": embeddings.shape[1],  # Dimension of each vector (embedding size)\n", "    \"space\": \"ip\",  # Distance metric - 'ip' for inner product\n", "    \"M\": 16,  # Max number of connections for each element (graph degree)\n", "    \"ef_construction\": 200,  # Controls index construction trade-off between speed and accuracy\n", "    \"random_seed\": 100,\n", "    \"max_elements\": 500000,\n", "    \"space\": \"ip\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "3a05d3da-62ab-493b-8522-f05325a396af", "metadata": {}, "outputs": [], "source": ["# import faiss\n", "# from sentence_transformers import SentenceTransformer\n", "import numpy as np\n", "import hnswlib\n", "\n", "\n", "# def create_hnsw_index(embeddings):\n", "\n", "#     index = ANNFactory.create({\"backend\": \"hnsw\", \"dimensions\": embeddings.shape[1]})\n", "#     index.index(embeddings)\n", "\n", "#     return index\n", "\n", "\n", "def create_hnswlib_index(embeddings):\n", "\n", "    # Initialize the index\n", "    hnsw_index = hnswlib.Index(space=hnsw_index_config[\"space\"], dim=hnsw_index_config[\"dim\"])\n", "    # We must specify the maximum number of elements (capacity) for the index at initialization.\n", "    max_elements = 500000  # for example, we plan to index up to 10,000 vectors\n", "    hnsw_index.init_index(\n", "        max_elements=hnsw_index_config[\"max_elements\"],\n", "        M=hnsw_index_config[\"M\"],\n", "        ef_construction=hnsw_index_config[\"ef_construction\"],\n", "        random_seed=hnsw_index_config[\"random_seed\"],\n", "    )\n", "\n", "    print(\n", "        \"Index initialized with parameters:\",\n", "        f\"space={hnsw_index_config['space']}, dim={hnsw_index_config['dim']}, M={hnsw_index_config['M']}, efConstruction={hnsw_index_config['ef_construction']}, seed={hnsw_index_config['random_seed']}\",\n", "    )\n", "\n", "    hnsw_index.add_items(embeddings, np.arange(embeddings.shape[0]))\n", "    print(hnsw_index.element_count)\n", "\n", "    return hnsw_index"]}, {"cell_type": "code", "execution_count": null, "id": "2533e6e2-0206-4c92-896a-d56b2ffdf137", "metadata": {}, "outputs": [], "source": ["index = create_hnswlib_index(embeddings)"]}, {"cell_type": "code", "execution_count": null, "id": "238e74cb-981e-4b14-81b8-a1ccb84a80cb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f04f6e9e-0cfd-4c4c-966f-fbe27694914a", "metadata": {}, "outputs": [], "source": ["# from txtai.ann import ANNFactory"]}, {"cell_type": "code", "execution_count": null, "id": "e35c4c49-45af-4f0f-be41-8bc24e4480ca", "metadata": {}, "outputs": [], "source": ["# index = ANNFactory.create({\"backend\": \"hnsw\", \"dimensions\": 384})\n", "# index.index(embeddings)\n", "\n", "# # Show total\n", "# index.count()"]}, {"cell_type": "code", "execution_count": null, "id": "c4a08580-ef09-45d3-a780-c75f0e7bbedf", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "1c490327-dd16-4baa-b0ad-c53344d7f5c7", "metadata": {"tags": []}, "source": ["# Semantic NER functions"]}, {"cell_type": "code", "execution_count": null, "id": "2ee47555-6767-41b8-b253-ee6389f9052e", "metadata": {}, "outputs": [], "source": ["def index_search(iq, top_k_neighbours):\n", "    labels, distances = index.knn_query(iq, top_k_neighbours)\n", "    sim = 1 - distances\n", "    return [list(zip(*labels, *sim))]"]}, {"cell_type": "code", "execution_count": null, "id": "b46a5fee-1b99-4607-8760-7ab8fd0108fe", "metadata": {}, "outputs": [], "source": ["index_search(optim_model.encode(\"organic pyaz\", normalize_embeddings=True), 5)"]}, {"cell_type": "code", "execution_count": null, "id": "c01c2e23-d729-41fd-b26c-28483339fb65", "metadata": {}, "outputs": [], "source": ["# no_res_df = pd.DataFrame()\n", "# no_res_df['col'] = ['No Results']\n", "no_res_df = None"]}, {"cell_type": "code", "execution_count": null, "id": "e5cec0b9-217b-4b37-93e9-2634ad0a0a4b", "metadata": {}, "outputs": [], "source": ["def run_semantic_return_top_neighbours_with_names(\n", "    input_query, sim_thresh=0.75, top_k_neighbours=200, matched_labels_sim_thresh=0.3\n", "):\n", "\n", "    # get embedding\n", "    iq = optim_model.encode(input_query, normalize_embeddings=True)\n", "\n", "    res = index_search(iq, top_k_neighbours)[0]\n", "    res2 = [j for j in res if j[1] >= sim_thresh]\n", "    I, sim = list(zip(*res2))\n", "    I = list(I)\n", "\n", "    # get the labels\n", "    temp = semantic_synthetic_data.iloc[I].drop([\"label_string_len\", \"len_label\"], axis=1)\n", "\n", "    if len(temp) == 0:\n", "        return no_res_df\n", "\n", "    temp = temp.rename({\"label_string\": \"labels\"}, axis=1)\n", "\n", "    # temp = pd.DataFrame()\n", "    temp[\"sim\"] = sim\n", "    # temp['frequency'] = 1-distance\n", "    # temp['labels'] = matched_lables\n", "    temp[\"labels\"] = temp[\"labels\"].str.split()\n", "    temp = temp.sort_values(by=[\"sim\"], ascending=False)\n", "\n", "    temp = temp[temp.sim >= matched_labels_sim_thresh]\n", "    # print(temp)\n", "\n", "    if len(temp) == 0:\n", "        return no_res_df\n", "\n", "    temp = temp.head(top_k_neighbours)\n", "\n", "    return temp\n", "\n", "\n", "def run_semantic_return_top_neighbours(\n", "    input_query, sim_thresh=0.75, top_k_neighbours=200, matched_labels_sim_thresh=0.3\n", "):\n", "\n", "    # get embedding\n", "    iq = optim_model.encode(input_query, normalize_embeddings=True)\n", "\n", "    res = index.search(iq, top_k_neighbours)[0]\n", "    # print('res',res[:10])\n", "    res2 = [j for j in res if j[1] >= sim_thresh]\n", "    # print('res2',res2)\n", "    I, sim = list(zip(*res2))\n", "    I = list(I)\n", "\n", "    # get the labels\n", "    matched_lables = synthetic_queries[I]\n", "\n", "    temp = pd.DataFrame()\n", "    temp[\"sim\"] = sim\n", "    temp[\"frequency\"] = sim\n", "    temp[\"labels\"] = matched_lables\n", "    temp[\"labels\"] = temp[\"labels\"].str.split()\n", "    temp = temp.sort_values(by=[\"sim\"], ascending=False)\n", "\n", "    temp = temp[temp.sim >= matched_labels_sim_thresh]\n", "    temp = temp.head(top_k_neighbours)\n", "\n", "    return temp\n", "\n", "\n", "def run_semantic_return_top_n_neighbours_entities(\n", "    input_query,\n", "    top_n_neigbours_entities=10,\n", "    sim_thresh=0.75,\n", "    top_k_neighbours=200,\n", "    matched_labels_sim_thresh=0.3,\n", "):\n", "\n", "    matched_df = run_semantic_return_top_neighbours(\n", "        input_query, sim_thresh, top_k_neighbours, matched_labels_sim_thresh\n", "    )\n", "    # print(matched_df)\n", "\n", "    if \"RESULT\" in list(matched_df.columns):\n", "        return no_res_df\n", "\n", "    temp = matched_df.head(top_n_neigbours_entities)\n", "    # print(temp)\n", "    # temp['label'] = temp.label_string.str.split()\n", "    temp = (\n", "        temp.explode(\"labels\")\n", "        .groupby(\"labels\", as_index=False)\n", "        .agg({\"frequency\": \"count\", \"sim\": \"max\"}, axis=1)\n", "        .sort_values(by=[\"frequency\", \"sim\"], ascending=[False, False])\n", "    )\n", "\n", "    result = [tuple(temp[col]) for col in temp.columns]\n", "    result_type = [x.split(\"::\")[1] for x in result[0]]\n", "\n", "    # print(result)\n", "\n", "    labels = []\n", "    keyterm_thresh = None\n", "    keyterm_idx = None\n", "    if \"keyterm\" in result_type:\n", "        idx = result_type.index(\"keyterm\")\n", "        keyterm_thresh = result[2][idx]\n", "        keyterm_idx = idx\n", "        labels.append((result[0][idx], result[1][idx], result[2][idx]))\n", "\n", "    if \"brand\" in result_type:\n", "        idx = result_type.index(\"brand\")\n", "        if result[2][idx] >= 0.90 or result[1][idx] >= 3:\n", "            labels.append((result[0][idx], result[1][idx], result[2][idx]))\n", "\n", "    if \"attribute\" in result_type:\n", "        idx = result_type.index(\"attribute\")\n", "        if result[2][idx] >= 0.90 or result[1][idx] >= 2:\n", "            labels.append((result[0][idx], result[1][idx], result[2][idx]))\n", "\n", "    keyterms2 = []\n", "    for i, result_type_ele in enumerate(result_type):\n", "        if \"keyterm\" == result_type_ele:\n", "            if (\n", "                keyterm_idx is not None\n", "                and keyterm_thresh is not None\n", "                and result[2][i] >= keyterm_thresh\n", "                and result[0][i] != result[0][keyterm_idx]\n", "            ):\n", "                keyterms2.append((result[0][i], result[1][i], result[2][i]))\n", "\n", "    print(len(labels + keyterms2))\n", "    if len(labels + keyterms2) == 0:\n", "        return no_res_df\n", "\n", "    temp = pd.DataFrame(labels + keyterms2)\n", "    temp.columns = [\"label\", \"freq\", \"sim\"]\n", "    #     temp['label_type'] = temp.label.apply(lambda x:x.split('::')[1])\n", "\n", "    #     temp = temp.sort_values(by=['label_type','sim',''])\n", "\n", "    return temp"]}, {"cell_type": "code", "execution_count": null, "id": "f9ce0bb9-d61e-4ceb-b396-3620e547a784", "metadata": {}, "outputs": [], "source": ["def run_semantic_return_top_neighbours_ProductNames(\n", "    input_query, sim_thresh=0.75, top_k_neighbours=10000, matched_labels_sim_thresh=0.3\n", "):\n", "\n", "    # #get embedding\n", "    iq = optim_model.encode(input_query, normalize_embeddings=True)\n", "\n", "    res = index_search(iq, 10)[0]\n", "    # print(res)\n", "    res2 = [j for j in res if j[1] >= sim_thresh]\n", "    I, sim = list(zip(*res2))\n", "    I = list(I)\n", "    # print(I)\n", "\n", "    # get the labels\n", "    temp = semantic_synthetic_data.iloc[I].drop([\"label_string_len\", \"len_label\"], axis=1)\n", "\n", "    if len(temp) == 0:\n", "        return no_res_df\n", "\n", "    temp = temp.rename({\"label_string\": \"labels\"}, axis=1)\n", "\n", "    # temp = pd.DataFrame()\n", "    temp[\"sim\"] = sim\n", "    # temp['frequency'] = 1-distance\n", "    # temp['labels'] = matched_lables\n", "    temp[\"labels\"] = temp[\"labels\"].str.split()\n", "    temp = temp.sort_values(by=[\"sim\"], ascending=False)\n", "\n", "    temp = temp[temp.source == 15]\n", "\n", "    # temp = temp[temp.sim>=matched_labels_sim_thresh]\n", "    # print(temp)\n", "\n", "    if len(temp) == 0:\n", "        return no_res_df\n", "\n", "    return temp"]}, {"cell_type": "code", "execution_count": null, "id": "63b609e2-c0ec-405d-969f-b3829651d0f4", "metadata": {}, "outputs": [], "source": ["res = run_semantic_return_top_neighbours_with_names(\"organic pyaz\", matched_labels_sim_thresh=0.3)\n", "\n", "if res is not None:\n", "    res.head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "98a70397-76fb-46a3-b14e-70b5cb718a02", "metadata": {}, "outputs": [], "source": ["res = run_semantic_return_top_neighbours_ProductNames(\n", "    \"child proofing\", matched_labels_sim_thresh=0.3\n", ")\n", "\n", "if res is not None:\n", "    res.head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "29a79ebf-3d3f-4937-ba68-821e9fe0db9d", "metadata": {}, "outputs": [], "source": ["res = run_semantic_return_top_neighbours_with_names(\n", "    \"child protection\", matched_labels_sim_thresh=0.3\n", ").head(10)\n", "\n", "if res is not None:\n", "    res.head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "90dde72b-a693-4d88-b013-8fd982612b6c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "d4b113a2-bd51-4188-b89d-5fdcc254cf86", "metadata": {"tags": []}, "source": ["# Load Product Data"]}, {"cell_type": "code", "execution_count": null, "id": "9ae89662-7d72-4629-b7f9-29b0dce14fbb", "metadata": {}, "outputs": [], "source": ["import utils_pull_product_data"]}, {"cell_type": "code", "execution_count": null, "id": "96da39ec-7b25-493b-9539-cbbf681f16f8", "metadata": {}, "outputs": [], "source": ["(\n", "    product_df,\n", "    brand_id_mapping,\n", "    keyterm_id_mapping,\n", "    attribute_id_mapping,\n", ") = utils_pull_product_data.get_data(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "29d279f0-e5c9-461a-a5c0-ab5a2fef03f4", "metadata": {}, "outputs": [], "source": ["product_df[\"keyterm\"] = (\n", "    product_df[\"keyterm\"].fillna(\"\").apply(lambda x: list(x) if not isinstance(x, str) else [])\n", ")\n", "product_df[\"attribute\"] = (\n", "    product_df[\"attribute\"].fillna(\"\").apply(lambda x: list(x) if not isinstance(x, str) else [])\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f4a033eb-9451-4f4b-8099-613fec00d57a", "metadata": {}, "outputs": [], "source": ["product_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "bb234ac0-139f-467b-9fcb-879e50704ad0", "metadata": {}, "outputs": [], "source": ["product_df.sample(5)"]}, {"cell_type": "code", "execution_count": null, "id": "ee8e4333-af8c-40c3-a805-6b5fd3fbabba", "metadata": {}, "outputs": [], "source": ["list(product_df.l0_category.unique())"]}, {"cell_type": "code", "execution_count": null, "id": "6ea78e7a-90af-438d-a144-fa6a21e6f377", "metadata": {}, "outputs": [], "source": ["product_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "fc8fd9bf-5990-4a61-9ac0-d8fb3792c43b", "metadata": {}, "outputs": [], "source": ["product_df[~product_df.l0_category.isin([\"books\", \"magazines\", \"trial new tree\"])].shape"]}, {"cell_type": "code", "execution_count": null, "id": "c6d16613-7a2b-4bc3-8e80-8437c72c765e", "metadata": {}, "outputs": [], "source": ["product_df = utls.preprocess_product_data(\n", "    product_df, columns_to_clean=[\"l0_category\", \"l1_category\", \"l2_category\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "81efaa16-454c-40b0-a2a7-e52e1005af70", "metadata": {}, "outputs": [], "source": ["product_df = product_df[~product_df.l0_category.isin([\"books\", \"magazines\", \"trial new tree\"])]"]}, {"cell_type": "code", "execution_count": null, "id": "0ec032b4-37c6-4fcc-bb87-228f6582cedf", "metadata": {}, "outputs": [], "source": ["product_df.shape"]}, {"cell_type": "markdown", "id": "ce3f3c97-a4b8-4d12-b9f4-3b70f0a815ca", "metadata": {}, "source": ["# Create indexes"]}, {"cell_type": "code", "execution_count": null, "id": "3a7b6c6d-d8a2-4c40-844a-f2d515166657", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "07ad747d-f444-4999-8b33-f05f51eb7b6b", "metadata": {}, "outputs": [], "source": ["index_id_columns = {\n", "    \"l1_category\": \"l1_category_id\",\n", "    \"l2_category\": \"l2_category_id\",\n", "    \"product_type\": \"product_type_id\",\n", "    \"brand\": \"brand_id\",\n", "    \"product_name\": \"product_id\",\n", "    \"keyterm\": \"keyterm_id\",\n", "}\n", "\n", "entity_cols = [\"keyterm\"]"]}, {"cell_type": "code", "execution_count": null, "id": "ec2808d8-fac1-40e5-967b-cc0844c92e2e", "metadata": {}, "outputs": [], "source": ["all_metadata_columns_to_index = list(index_id_columns.keys())\n", "print(all_metadata_columns_to_index)"]}, {"cell_type": "code", "execution_count": null, "id": "237ceb37-7a3c-40a4-8bd2-72a6d75f6c4f", "metadata": {}, "outputs": [], "source": ["# all_embeddings_id_values_dict = {}\n", "\n", "# all_embeddings_id_values_dict[\"embeddings\"] = {}\n", "# all_embeddings_id_values_dict[\"values\"] = {}\n", "# all_embeddings_id_values_dict[\"ids\"] = {}\n", "\n", "\n", "# for col_name in all_metadata_columns_to_index:\n", "#     print(col_name)\n", "#     if col_name == \"keyterm\":\n", "#         temp1 = list(product_df.explode(col_name)[col_name].dropna().unique())\n", "#         temp2 = [keyterm_id_mapping[x] for x in temp1]\n", "#         temp_dict = dict(zip(temp1, temp2))\n", "#     else:\n", "#         id_col_name = index_id_columns[col_name]\n", "#         temp_dict = (\n", "#             product_df[[col_name, id_col_name]]\n", "#             .drop_duplicates()\n", "#             .sort_values(by=[id_col_name])\n", "#             .set_index([col_name])\n", "#             .to_dict()[id_col_name]\n", "#         )\n", "#     print(len(temp_dict))\n", "#     all_embeddings_id_values_dict[\"values\"][col_name] = list(temp_dict.keys())\n", "#     all_embeddings_id_values_dict[\"ids\"][col_name] = list(temp_dict.values())\n", "#     all_embeddings_id_values_dict[\"embeddings\"][col_name] = np.array(\n", "#         pqdm(list(temp_dict.keys()), encode_query, n_jobs=4)\n", "#     )"]}, {"cell_type": "code", "execution_count": null, "id": "c8744199-2cc9-41a5-8bb6-9e8eac0697eb", "metadata": {}, "outputs": [], "source": ["unique_values = set()\n", "\n", "for col_name in all_metadata_columns_to_index:\n", "    print(col_name)\n", "    id_col_name = index_id_columns[col_name]\n", "\n", "    if col_name == \"keyterm\":\n", "        temp1 = list(product_df.explode(col_name)[col_name].dropna().unique())\n", "        temp2 = [keyterm_id_mapping[x] for x in temp1]\n", "\n", "        temp_df = pd.DataFrame()\n", "        temp_df[col_name] = temp1\n", "        temp_df[id_col_name] = temp2\n", "\n", "    else:\n", "        temp_df = product_df[[col_name, id_col_name]].drop_duplicates()\n", "\n", "    unique_values = unique_values.union(set(temp_df[col_name].unique().tolist()))\n", "\n", "unique_values = list(unique_values)\n", "\n", "entities_to_encode = [x for x in unique_values if x not in all_embeddings_dict]\n", "print(len(entities_to_encode))\n", "\n", "if len(entities_to_encode) > 0:\n", "    entities_embeddings = np.array(pqdm(entities_to_encode, encode_query, n_jobs=8))\n", "    all_embeddings_dict.update(dict(zip(entities_to_encode, entities_embeddings)))\n", "\n", "    import pickle\n", "\n", "    with open(f\"/tmp/{ONNX_MODEL_DIRECTORY}_embeddings_dict_v2.pkl\", \"wb\") as f:\n", "        pickle.dump(all_embeddings_dict, f)\n", "    prefix = f\"data-science/ner/{ONNX_MODEL_DIRECTORY}_embeddings_dict_v2/\"\n", "    pb.to_s3(f\"/tmp/{ONNX_MODEL_DIRECTORY}_embeddings_dict_v2.pkl\", bucket, prefix)"]}, {"cell_type": "code", "execution_count": null, "id": "4e0fa44d-7668-4e44-a176-3f6210075f10", "metadata": {"tags": []}, "outputs": [], "source": ["all_embeddings_id_values_dict = {}\n", "\n", "all_embeddings_id_values_dict[\"embeddings\"] = {}\n", "all_embeddings_id_values_dict[\"values\"] = {}\n", "all_embeddings_id_values_dict[\"ids\"] = {}\n", "\n", "\n", "for col_name in all_metadata_columns_to_index:\n", "    print(col_name)\n", "    id_col_name = index_id_columns[col_name]\n", "\n", "    if col_name == \"keyterm\":\n", "        temp1 = list(product_df.explode(col_name)[col_name].dropna().unique())\n", "        temp2 = [keyterm_id_mapping[x] for x in temp1]\n", "\n", "        temp_df = pd.DataFrame()\n", "        temp_df[col_name] = temp1\n", "        temp_df[id_col_name] = temp2\n", "\n", "    else:\n", "        temp_df = product_df[[col_name, id_col_name]].drop_duplicates()\n", "\n", "    unique_values = temp_df[col_name].unique().tolist()\n", "\n", "    entities_to_encode = [x for x in unique_values if x not in all_embeddings_dict]\n", "    print(len(entities_to_encode))\n", "\n", "    if len(entities_to_encode) > 0:\n", "        entities_embeddings = np.array(pqdm(entities_to_encode, encode_query, n_jobs=8))\n", "        all_embeddings_dict.update(dict(zip(entities_to_encode, entities_embeddings)))\n", "\n", "        import pickle\n", "\n", "        with open(f\"/tmp/{ONNX_MODEL_DIRECTORY}_embeddings_dict_v2.pkl\", \"wb\") as f:\n", "            pickle.dump(all_embeddings_dict, f)\n", "        prefix = f\"data-science/ner/{ONNX_MODEL_DIRECTORY}_embeddings_dict_v2/\"\n", "        pb.to_s3(f\"/tmp/{ONNX_MODEL_DIRECTORY}_embeddings_dict_v2.pkl\", bucket, prefix)\n", "\n", "    all_embeddings = np.array([all_embeddings_dict[x] for x in unique_values])\n", "    print(all_embeddings.shape)\n", "\n", "    # all_embeddings = np.array(pqdm(unique_values, encode_query, n_jobs=8))\n", "\n", "    value_embedding_dict = dict(zip(unique_values, all_embeddings))\n", "    temp_df[\"embeddings\"] = temp_df[col_name].map(value_embedding_dict)\n", "\n", "    all_embeddings_id_values_dict[\"values\"][col_name] = list(temp_df[col_name])\n", "    all_embeddings_id_values_dict[\"ids\"][col_name] = list(temp_df[id_col_name])\n", "    all_embeddings_id_values_dict[\"embeddings\"][col_name] = np.array(temp_df[\"embeddings\"].tolist())"]}, {"cell_type": "code", "execution_count": null, "id": "1bdd00ea-cd98-486e-beed-0c676719c00f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "b7efc40a-fa9f-4daa-8656-e6f75ac8a405", "metadata": {}, "source": ["# Create embedding for ANTI-Index"]}, {"cell_type": "code", "execution_count": null, "id": "edb10989-a2f0-4ade-b25d-15eec92e9583", "metadata": {}, "outputs": [], "source": ["if os.path.exists(\"QTP Issues - Hard Checks & Others - HP_Disable_ANTI_INDEX.csv\"):\n", "    anti_hp_disable_pids_df = pd.read_csv(\n", "        \"QTP Issues - Hard Checks & Others - HP_Disable_ANTI_INDEX.csv\"\n", "    )\n", "    anti_hp_disable_pids_df[\"metadata_ids\"] = anti_hp_disable_pids_df[\"metadata_ids\"].apply(eval)\n", "else:\n", "    anti_hp_disable_pids_df = pb.from_sheets(\n", "        \"10gfWKDZeInlSfFQseHGipP245Hw5elXPmn1se_CfhYw\", \"HP_Disable_ANTI_INDEX\"\n", "    )\n", "    anti_hp_disable_pids_df[\"metadata_ids\"] = anti_hp_disable_pids_df[\"metadata_ids\"].apply(eval)\n", "\n", "anti_hp_disable_pids_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "58e6bcab-65c7-49f0-8f02-30cc0b642a43", "metadata": {}, "outputs": [], "source": ["anti_hp_disable_pids_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "eb9a13d8-d351-4b70-9c93-49009a51abc2", "metadata": {}, "outputs": [], "source": ["all_values = set()\n", "for col_name in anti_hp_disable_pids_df.action_type.unique().tolist():\n", "    print(col_name)\n", "    temp_df = anti_hp_disable_pids_df[anti_hp_disable_pids_df.action_type == col_name]\n", "\n", "    all_embeddings_id_values_dict[\"values\"][col_name] = np.array(\n", "        list(temp_df.search_keyword.tolist())\n", "    )\n", "    all_embeddings_id_values_dict[\"ids\"][col_name] = np.array(list(temp_df.metadata_ids.tolist()))\n", "\n", "    all_values = all_values.union(set(all_embeddings_id_values_dict[\"values\"][col_name].tolist()))\n", "\n", "all_values = list(all_values)\n", "entities_to_encode = [x for x in all_values if x not in all_embeddings_dict]\n", "print(len(entities_to_encode))\n", "\n", "if len(entities_to_encode) > 0:\n", "    entities_embeddings = np.array(pqdm(entities_to_encode, encode_query, n_jobs=8))\n", "    all_embeddings_dict.update(dict(zip(entities_to_encode, entities_embeddings)))\n", "\n", "    import pickle\n", "\n", "    with open(f\"/tmp/{ONNX_MODEL_DIRECTORY}_embeddings_dict_v2.pkl\", \"wb\") as f:\n", "        pickle.dump(all_embeddings_dict, f)\n", "    prefix = f\"data-science/ner/{ONNX_MODEL_DIRECTORY}_embeddings_dict_v2/\"\n", "    pb.to_s3(f\"/tmp/{ONNX_MODEL_DIRECTORY}_embeddings_dict_v2.pkl\", bucket, prefix)"]}, {"cell_type": "code", "execution_count": null, "id": "08bded41-324d-4771-beaf-0e4a840d39fc", "metadata": {}, "outputs": [], "source": ["for col_name in anti_hp_disable_pids_df.action_type.unique().tolist():\n", "    print(col_name)\n", "    temp_df = anti_hp_disable_pids_df[anti_hp_disable_pids_df.action_type == col_name]\n", "\n", "    all_embeddings_id_values_dict[\"values\"][col_name] = np.array(\n", "        list(temp_df.search_keyword.tolist())\n", "    )\n", "    all_embeddings_id_values_dict[\"ids\"][col_name] = np.array(list(temp_df.metadata_ids.tolist()))\n", "\n", "    all_values = all_embeddings_id_values_dict[\"values\"][col_name].tolist()\n", "\n", "    entities_to_encode = [x for x in all_values if x not in all_embeddings_dict]\n", "    print(len(entities_to_encode))\n", "\n", "    if len(entities_to_encode) > 0:\n", "        entities_embeddings = np.array(pqdm(entities_to_encode, encode_query, n_jobs=8))\n", "        all_embeddings_dict.update(dict(zip(entities_to_encode, entities_embeddings)))\n", "\n", "        import pickle\n", "\n", "        with open(f\"/tmp/{ONNX_MODEL_DIRECTORY}_embeddings_dict_v2.pkl\", \"wb\") as f:\n", "            pickle.dump(all_embeddings_dict, f)\n", "        prefix = f\"data-science/ner/{ONNX_MODEL_DIRECTORY}_embeddings_dict_v2/\"\n", "        pb.to_s3(f\"/tmp/{ONNX_MODEL_DIRECTORY}_embeddings_dict_v2.pkl\", bucket, prefix)\n", "\n", "    all_embeddings = np.array([all_embeddings_dict[x] for x in all_values])\n", "    print(all_embeddings.shape)\n", "\n", "    all_embeddings_id_values_dict[\"embeddings\"][col_name] = all_embeddings"]}, {"cell_type": "code", "execution_count": null, "id": "48044e7d-831d-4659-bbd4-b6b6e1b1448e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "63c504dc-f5b2-4fa6-bcff-0cba96711924", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f71df2db-8c1c-4672-a907-a2879fc3c1c1", "metadata": {}, "outputs": [], "source": ["model_data = {}\n", "\n", "model_data[\"indexes\"] = {}\n", "model_data[\"values\"] = {}\n", "model_data[\"ids\"] = {}"]}, {"cell_type": "code", "execution_count": null, "id": "7ad98361-2384-413d-aec6-75bfc8a6e5a0", "metadata": {}, "outputs": [], "source": ["list(index_id_columns.items())[-1:]"]}, {"cell_type": "code", "execution_count": null, "id": "27b643c9-b677-48e2-881e-2c1322d2bcc0", "metadata": {"tags": []}, "outputs": [], "source": ["for col_name, id_col_name in list(index_id_columns.items())[:]:\n", "    print(col_name)\n", "\n", "    model_data[\"values\"][col_name] = np.array(all_embeddings_id_values_dict[\"values\"][col_name])\n", "    model_data[\"indexes\"][col_name] = create_hnswlib_index(\n", "        all_embeddings_id_values_dict[\"embeddings\"][col_name]\n", "    )\n", "    print(model_data[\"indexes\"][col_name].element_count)\n", "    model_data[\"ids\"][col_name] = np.array(all_embeddings_id_values_dict[\"ids\"][col_name])\n", "\n", "    assert (model_data[\"indexes\"][col_name].element_count == len(model_data[\"ids\"][col_name])) & (\n", "        model_data[\"indexes\"][col_name].element_count == len(model_data[\"values\"][col_name])\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "57ab3e76-daf1-4281-b61e-ead92c0a4d71", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e9eb24b3-8024-40d8-9351-48edb688ff5e", "metadata": {}, "outputs": [], "source": ["product_df.sample(1)"]}, {"cell_type": "code", "execution_count": null, "id": "3f7e8545-7247-4127-8e65-a7a018b0caf6", "metadata": {}, "outputs": [], "source": ["product_name_to_ptype_map = {}\n", "\n", "temp1 = product_df.product_name.apply(lambda x: x.replace(\" \", \"\"))\n", "temp1 = [f\"{x}__{y}\" for x, y in zip(temp1, product_df.product_id)]\n", "\n", "temp2 = product_df.product_type.apply(lambda x: x.replace(\" \", \"\"))\n", "temp2 = [f\"{x}__{y}\" for x, y in zip(temp2, product_df.product_type_id)]\n", "\n", "product_name_to_ptype_map = dict(zip(temp1, temp2))"]}, {"cell_type": "code", "execution_count": null, "id": "e3d8ead5-23dd-4ff5-a64a-f74c910dc130", "metadata": {}, "outputs": [], "source": ["product_df.sample(2)"]}, {"cell_type": "code", "execution_count": null, "id": "6a344790-9c69-46cf-be7a-1f8a3a9b0a40", "metadata": {}, "outputs": [], "source": ["temp = product_df.copy()\n", "for col, id_col in zip(\n", "    [\"product_name\", \"l1_category\", \"l2_category\", \"product_type\"],\n", "    [\"product_id\", \"l1_category_id\", \"l2_category_id\", \"product_type_id\"],\n", "):\n", "    temp1 = temp[col].apply(lambda x: x.replace(\" \", \"\"))\n", "    temp[f\"{col}_idv\"] = [f\"{x}__{y}\" for x, y in zip(temp1, product_df[id_col])]"]}, {"cell_type": "code", "execution_count": null, "id": "a8bece44-b715-4dea-949d-f105c95cd058", "metadata": {}, "outputs": [], "source": ["idv_cols = [f\"{col}_idv\" for col in [\"product_name\", \"l1_category\", \"l2_category\", \"product_type\"]]\n", "temp = temp[idv_cols]"]}, {"cell_type": "code", "execution_count": null, "id": "c535ef38-eced-40c6-a10d-e2f8c916a36c", "metadata": {"tags": []}, "outputs": [], "source": ["idv_cols_map = temp.set_index(\"product_name_idv\").to_dict(\"series\")"]}, {"cell_type": "code", "execution_count": null, "id": "262807fb-38e2-451e-a594-bfcc727091b7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "d2c02364-4daa-43f0-8d85-73658cb1f8a6", "metadata": {}, "source": ["# Create multi - indexes"]}, {"cell_type": "code", "execution_count": null, "id": "f79d50b9-c4a2-4a28-9afe-deb42e6fc7ba", "metadata": {}, "outputs": [], "source": ["multi_index_model_data = {}\n", "\n", "multi_index_model_data[\"indexes\"] = {}\n", "multi_index_model_data[\"values\"] = {}\n", "multi_index_model_data[\"ids\"] = {}\n", "multi_index_model_data[\"metadata_type\"] = {}"]}, {"cell_type": "code", "execution_count": null, "id": "3105578d-b79f-4b67-b02e-d255f8e57776", "metadata": {}, "outputs": [], "source": ["multi_index_columns_dict = {\n", "    # \"pname_ptype\": [\"product_name\", \"product_type\"],\n", "    # \"pname_keyterm\": [\"product_name\", \"keyterm\"],\n", "    # \"pname_ptype_keyterm\": [\"product_name\", \"product_type\", \"keyterm\"],\n", "    # \"pname_ptype_l2\": [\"product_name\", \"product_type\", \"l2_category\"],\n", "    # \"pname_keyterm_l2\": [\"product_name\", \"keyterm\", \"l2_category\"],\n", "    # \"pname\": [\"product_name\"],\n", "    # \"pname_ANTI\": [\"product_name\", \"anti_query\"],\n", "    \"pname_ANTI_HP_DISABLE\": [\"product_name\", \"anti_query\", \"hard_positive\", \"disable\"],\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "985a7b33-d233-406d-855d-0c2e89e7dad5", "metadata": {}, "outputs": [], "source": ["for metadata_index_name, columns in multi_index_columns_dict.items():\n", "    print(metadata_index_name)\n", "    values = []\n", "    ids = []\n", "    metadata_type = []\n", "    embeddings = []\n", "    for col_name in columns:\n", "        print(\"---\", col_name)\n", "\n", "        values.append(all_embeddings_id_values_dict[\"values\"][col_name])\n", "        ids.append(all_embeddings_id_values_dict[\"ids\"][col_name])\n", "        metadata_type.append([col_name] * len(all_embeddings_id_values_dict[\"values\"][col_name]))\n", "        embeddings.append(list(all_embeddings_id_values_dict[\"embeddings\"][col_name]))\n", "\n", "    values = np.array(list(itertools.chain.from_iterable(values)))\n", "    ids = np.array(list(itertools.chain.from_iterable(ids)))\n", "    metadata_type = np.array(list(itertools.chain.from_iterable(metadata_type)))\n", "    embeddings = np.array(list(itertools.chain.from_iterable(embeddings)))\n", "\n", "    multi_index_model_data[\"values\"][metadata_index_name] = values\n", "    multi_index_model_data[\"indexes\"][metadata_index_name] = create_hnswlib_index(embeddings)\n", "    print(multi_index_model_data[\"indexes\"][metadata_index_name].element_count)\n", "    multi_index_model_data[\"ids\"][metadata_index_name] = ids\n", "    multi_index_model_data[\"metadata_type\"][metadata_index_name] = metadata_type\n", "\n", "    assert (\n", "        multi_index_model_data[\"indexes\"][metadata_index_name].element_count\n", "        == len(multi_index_model_data[\"ids\"][metadata_index_name])\n", "        & multi_index_model_data[\"indexes\"][metadata_index_name].element_count\n", "        == len(multi_index_model_data[\"values\"][metadata_index_name])\n", "        & multi_index_model_data[\"indexes\"][metadata_index_name].element_count\n", "        == len(multi_index_model_data[\"metadata_type\"][metadata_index_name])\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "2c171003-6e4c-464a-b498-5565ef634f0d", "metadata": {}, "outputs": [], "source": ["metadata_type"]}, {"cell_type": "code", "execution_count": null, "id": "cbd621f4-4283-4905-8705-11f450eb8bfe", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "d49f588f-8901-45ac-b4ca-2ae8e7a52ee7", "metadata": {}, "source": ["# Save Data Files"]}, {"cell_type": "code", "execution_count": null, "id": "1fd6a42d-003f-424a-be0d-6059d3fc7949", "metadata": {}, "outputs": [], "source": ["MODEL_NAME = \"_\".join(ONNX_MODEL_DIRECTORY.split(\"_\")[:-3])\n", "MODEL_NAME"]}, {"cell_type": "code", "execution_count": null, "id": "e6bb86e1-8774-4874-83bd-4d885dc425ee", "metadata": {}, "outputs": [], "source": ["DATA_FILES_DIRECTORY = f\"QTP_data_files_{MODEL_NAME}\"\n", "DATA_FILES_DIRECTORY"]}, {"cell_type": "code", "execution_count": null, "id": "b14857be-47db-4ab1-9c04-9aeac4a4831f", "metadata": {}, "outputs": [], "source": ["!ls -lh /tmp/"]}, {"cell_type": "code", "execution_count": null, "id": "c70a38a1-6844-4c11-93c2-3b01449c8ee4", "metadata": {}, "outputs": [], "source": ["ENV = \"PROD\"\n", "\n", "if ENV == \"PROD\":\n", "    DATA_FILES_DIRECTORY = f\"/tmp/{DATA_FILES_DIRECTORY}\""]}, {"cell_type": "code", "execution_count": null, "id": "32e73bf4-a019-47ae-bb75-919d315b5ce7", "metadata": {}, "outputs": [], "source": ["if not os.path.exists(DATA_FILES_DIRECTORY):\n", "    os.makedirs(DATA_FILES_DIRECTORY)"]}, {"cell_type": "code", "execution_count": null, "id": "d5e3722c-90ab-4b33-9c54-5ee2f6ed54c0", "metadata": {}, "outputs": [], "source": ["!ls -lh /tmp/"]}, {"cell_type": "code", "execution_count": null, "id": "e874ed5e-8ec9-42dc-b3bb-e51e4a27a6ff", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b6b569ae-48cc-48a9-85d6-c4aebb28f1da", "metadata": {}, "outputs": [], "source": ["# data_files_objects = [\n", "#     product_df,brand_id_mapping,keyterm_id_mapping,attribute_id_mapping,\n", "#     semantic_synthetic_data, synthetic_queries, embeddings, index,\n", "#     model_data, product_name_to_ptype_map, idv_cols_map\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "id": "11b8db0a-9d2a-4d78-b090-0f5173a55c34", "metadata": {}, "outputs": [], "source": ["# del data_files_objects"]}, {"cell_type": "code", "execution_count": null, "id": "bb53bbe2-02d5-45e9-8783-c905445328ca", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8ee33505-fff1-4f7c-9685-cb2ca419e27b", "metadata": {}, "outputs": [], "source": ["with open(f\"{DATA_FILES_DIRECTORY}/product_df.pkl\", \"wb\") as f:\n", "    pickle.dump(product_df, f)"]}, {"cell_type": "code", "execution_count": null, "id": "e55657ea-d2ad-4d8e-b655-d34418551ef2", "metadata": {}, "outputs": [], "source": ["with open(f\"{DATA_FILES_DIRECTORY}/brand_id_mapping.pkl\", \"wb\") as f:\n", "    pickle.dump(brand_id_mapping, f)"]}, {"cell_type": "code", "execution_count": null, "id": "1fe9239d-3918-491e-9490-4081819a0d7d", "metadata": {}, "outputs": [], "source": ["with open(f\"{DATA_FILES_DIRECTORY}/keyterm_id_mapping.pkl\", \"wb\") as f:\n", "    pickle.dump(keyterm_id_mapping, f)"]}, {"cell_type": "code", "execution_count": null, "id": "01e75206-4559-488d-8e77-8fe41f4ad224", "metadata": {}, "outputs": [], "source": ["with open(f\"{DATA_FILES_DIRECTORY}/attribute_id_mapping.pkl\", \"wb\") as f:\n", "    pickle.dump(attribute_id_mapping, f)"]}, {"cell_type": "code", "execution_count": null, "id": "b6b8a8ea-2321-4e58-b9c0-0b9b6701fade", "metadata": {}, "outputs": [], "source": ["with open(f\"{DATA_FILES_DIRECTORY}/semantic_synthetic_data.pkl\", \"wb\") as f:\n", "    pickle.dump(semantic_synthetic_data, f)"]}, {"cell_type": "code", "execution_count": null, "id": "d0e4496b-eaff-4b4e-ab43-4300d8bf7601", "metadata": {}, "outputs": [], "source": ["with open(f\"{DATA_FILES_DIRECTORY}/synthetic_queries.pkl\", \"wb\") as f:\n", "    pickle.dump(synthetic_queries, f)"]}, {"cell_type": "code", "execution_count": null, "id": "08fe903f-dfd4-437b-a2d8-76a551532f7d", "metadata": {}, "outputs": [], "source": ["with open(f\"{DATA_FILES_DIRECTORY}/synthetic_queries_pids.pkl\", \"wb\") as f:\n", "    pickle.dump(synthetic_queries_pids, f)"]}, {"cell_type": "code", "execution_count": null, "id": "a6825068-9062-43d6-9a81-9b0523408d65", "metadata": {}, "outputs": [], "source": ["with open(f\"{DATA_FILES_DIRECTORY}/embeddings.pkl\", \"wb\") as f:\n", "    pickle.dump(embeddings, f)"]}, {"cell_type": "code", "execution_count": null, "id": "e2abf589-b3d8-4903-addf-38ecb1acbaca", "metadata": {}, "outputs": [], "source": ["# with open(f\"{DATA_FILES_DIRECTORY}/index.pkl\", \"wb\") as f:\n", "#     pickle.dump(index, f)"]}, {"cell_type": "code", "execution_count": null, "id": "642be890-4787-4fa3-a6da-d40ae4879a14", "metadata": {}, "outputs": [], "source": ["with open(f\"{DATA_FILES_DIRECTORY}/model_data.pkl\", \"wb\") as f:\n", "    pickle.dump(model_data, f)"]}, {"cell_type": "code", "execution_count": null, "id": "ba6b1f8d-d1fe-4204-9b6a-d3260b38e9b8", "metadata": {}, "outputs": [], "source": ["with open(f\"{DATA_FILES_DIRECTORY}/product_name_to_ptype_map.pkl\", \"wb\") as f:\n", "    pickle.dump(product_name_to_ptype_map, f)"]}, {"cell_type": "code", "execution_count": null, "id": "fd4a71d1-a8d3-40cf-967c-03c18cd8bb61", "metadata": {}, "outputs": [], "source": ["with open(f\"{DATA_FILES_DIRECTORY}/idv_cols_map.pkl\", \"wb\") as f:\n", "    pickle.dump(idv_cols_map, f)"]}, {"cell_type": "code", "execution_count": null, "id": "a4088c3e-4ab9-4f07-9df4-5bf8570de6a0", "metadata": {}, "outputs": [], "source": ["# with open(f\"{DATA_FILES_DIRECTORY}/multi_index_model_data.pkl\", \"wb\") as f:\n", "#     pickle.dump(multi_index_model_data, f)"]}, {"cell_type": "code", "execution_count": null, "id": "9718720a-a886-4c46-9eb3-cdc0cb8122bc", "metadata": {}, "outputs": [], "source": ["with open(f\"{DATA_FILES_DIRECTORY}/all_embeddings_id_values_dict.pkl\", \"wb\") as f:\n", "    pickle.dump(all_embeddings_id_values_dict, f)"]}, {"cell_type": "code", "execution_count": null, "id": "97b7d846-714f-4733-885f-60745b7f92ce", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "95d64bb6-3446-476d-b231-65be034ffafd", "metadata": {}, "outputs": [], "source": ["# with open(f\"{DATA_FILES_DIRECTORY}/q2p_synthetic_queries_mxbaiExp05.pkl\", \"wb\") as f:\n", "#     pickle.dump(synthetic_queries, f)\n", "\n", "index.save_index(f\"{DATA_FILES_DIRECTORY}/index.bin\")\n", "\n", "with open(f\"{DATA_FILES_DIRECTORY}/hnsw_index_config.json\", \"w\") as f:\n", "    json.dump(hnsw_index_config, f)"]}, {"cell_type": "code", "execution_count": null, "id": "2c6ac59f-a067-411e-a2bd-814dccf380cc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "bf05f1d3-6d3a-4d2e-a87f-6f6103dd4e4c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9312e8db-ca08-4af9-bf3e-8c284c458efc", "metadata": {}, "outputs": [], "source": ["!ls -lh {DATA_FILES_DIRECTORY}/"]}, {"cell_type": "code", "execution_count": null, "id": "eeb65c87-2992-49f1-9273-2164ca297aa4", "metadata": {}, "outputs": [], "source": ["import shutil\n", "\n", "shutil.make_archive(DATA_FILES_DIRECTORY, \"zip\", DATA_FILES_DIRECTORY)"]}, {"cell_type": "code", "execution_count": null, "id": "b48bd482-d579-4f8a-b877-f002ba16eb3d", "metadata": {}, "outputs": [], "source": ["!ls -lh /tmp/"]}, {"cell_type": "code", "execution_count": null, "id": "4356105c-e994-478b-8645-183b975e4c93", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6ec75e2e-9fe8-4525-a715-5a8bd3fa138c", "metadata": {}, "outputs": [], "source": ["# Q2P_PRODUCT_INDEX = \"product_index.pkl\" - Not Required\n", "# Q2P_PRODUCT_FAISS_INDEX = \"product_faiss.index\" - Not Required\n", "\n", "# Q2P_SYTHETIC_QUERIES = \"q2p_synthetic_queries_v1.pkl\" - DONE\n", "# Q2P_SYNTHETIC_INDEX = \"q2p_synthetic_index_v1.pkl\" - DONE\n", "# Q2P_MULTI_INDEX = 'index_data_pname_ptype_HNSW.pkl' - DONE\n", "# IDV_COLS_MAP = 'QTP_idv_cols_map.pkl' - DONE\n", "# HARD_NEGATIVES = 'hard_negatives_ID.pkl' - DONE\n", "# NER_SYNTHETIC_PIDS = 'ner_synthetic_queries_pids_HNSW.pkl' - DONE"]}, {"cell_type": "code", "execution_count": null, "id": "7955908a-7078-4f2f-b843-1a730cd3e15d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c9be9ce1-5a9c-45cb-ac53-18d5a9c408ef", "metadata": {}, "outputs": [], "source": ["# with open(f\"/tmp/QTP_idv_cols_map.pkl\", \"wb\") as f:\n", "#     pickle.dump(idv_cols_map, f)"]}, {"cell_type": "code", "execution_count": null, "id": "8c8a9e81-ce6e-4d67-bc4b-53e7bf8b6091", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2ed1862c-97fc-49de-83c8-821cc1691a8e", "metadata": {}, "outputs": [], "source": ["# with open(f\"/tmp/ner_synthetic_queries_pids_HNSW.pkl\", \"wb\") as f:\n", "#     pickle.dump(synthetic_queries_pids, f)"]}, {"cell_type": "code", "execution_count": null, "id": "9cf24459-47c3-41c6-be26-2bd6b6ef1030", "metadata": {}, "outputs": [], "source": ["!ls -lh /tmp/"]}, {"cell_type": "code", "execution_count": null, "id": "08dfbe4e-8077-4df5-88f5-fecbd0bbb0f7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a0ab5730-ddbb-43fe-8a9e-7457e629f92b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "d498cc96-6691-4938-841e-4e001322b130", "metadata": {}, "source": ["# Butterfly Local Folder"]}, {"cell_type": "code", "execution_count": null, "id": "563398b9-826f-4e76-95f6-4ec0f3b7f983", "metadata": {}, "outputs": [], "source": ["BUTTERFLY_LOCAL_FOLDER = \"data/blinkit/search-query-to-product/v2\""]}, {"cell_type": "code", "execution_count": null, "id": "dd88167a-4492-4eed-accf-0ab76b804fea", "metadata": {}, "outputs": [], "source": ["ENV = \"PROD\"\n", "\n", "if ENV == \"PROD\":\n", "    BUTTERFLY_LOCAL_FOLDER = f\"/tmp/{BUTTERFLY_LOCAL_FOLDER}\""]}, {"cell_type": "code", "execution_count": null, "id": "06fb71b5-fa62-4b3e-8d8a-52db6e3abcb9", "metadata": {}, "outputs": [], "source": ["if not os.path.exists(BUTTERFLY_LOCAL_FOLDER):\n", "    os.makedirs(BUTTERFLY_LOCAL_FOLDER)"]}, {"cell_type": "code", "execution_count": null, "id": "d7530fec-69a0-4bd0-a8d0-cd7706d3f970", "metadata": {}, "outputs": [], "source": ["!ls -lh /tmp/"]}, {"cell_type": "code", "execution_count": null, "id": "36e47fcd-3117-4723-8cb4-ebf43bba45e8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9e24a67e-9a8b-490e-85f0-ed9a3e154e87", "metadata": {}, "outputs": [], "source": ["BUTTERFLY_LOCAL_FOLDER"]}, {"cell_type": "code", "execution_count": null, "id": "0c3ecf0e-1513-401d-82b5-edae1319361f", "metadata": {}, "outputs": [], "source": ["with open(f\"{BUTTERFLY_LOCAL_FOLDER}/q2p_synthetic_queries.pkl\", \"wb\") as f:\n", "    pickle.dump(synthetic_queries, f)"]}, {"cell_type": "code", "execution_count": null, "id": "ad5095fa-5374-4367-824e-4236402f5321", "metadata": {}, "outputs": [], "source": ["index.save_index(f\"{BUTTERFLY_LOCAL_FOLDER}/q2p_synthetic_index.bin\")"]}, {"cell_type": "code", "execution_count": null, "id": "38e90f1b-78d3-4e8d-a3b6-b977c478ac05", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a48d8896-317f-44a2-9800-7a86f319bf30", "metadata": {}, "outputs": [], "source": ["prefix = f\"data-science/ner/autocomplete_v2/\" + date + \"/\"\n", "s3_dump_path = f\"s3://{bucket}/{prefix}\"\n", "print(s3_dump_path)\n", "pb.from_s3(bucket, prefix, f\"{BUTTERFLY_LOCAL_FOLDER}/autocomplete_v2.pkl\")"]}, {"cell_type": "code", "execution_count": null, "id": "8efc2bb2-82f9-4499-83ba-f41cb0c14307", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "74da58f7-f44f-45e2-90b4-06e314e2b9da", "metadata": {}, "source": ["# Seperate PName-ptype Multi Index for Saving"]}, {"cell_type": "code", "execution_count": null, "id": "69bd2eae-08c9-4bc9-964e-929fff3470ac", "metadata": {}, "outputs": [], "source": ["import copy\n", "\n", "index_data_pname_MULTI = copy.deepcopy(multi_index_model_data)"]}, {"cell_type": "code", "execution_count": null, "id": "d87d8cc5-22b2-4ef8-b567-17ccdf6b9c48", "metadata": {}, "outputs": [], "source": ["print(index_data_pname_MULTI.keys())\n", "print(index_data_pname_MULTI[\"indexes\"].keys())"]}, {"cell_type": "code", "execution_count": null, "id": "b35584c1-3434-4cb2-9488-89f5f2ac7f47", "metadata": {}, "outputs": [], "source": ["for metadata_col_name in multi_index_model_data[\"indexes\"].keys():\n", "    if metadata_col_name == \"pname_ANTI_HP_DISABLE\":\n", "        continue\n", "    for key in model_data.keys():\n", "        del index_data_pname_MULTI[key][metadata_col_name]"]}, {"cell_type": "code", "execution_count": null, "id": "0fc00bc2-2576-421a-8b83-0f8485b19f6d", "metadata": {}, "outputs": [], "source": ["print(index_data_pname_MULTI.keys())\n", "print(index_data_pname_MULTI[\"indexes\"].keys())"]}, {"cell_type": "code", "execution_count": null, "id": "98528e44-104e-4412-aac1-83a5deba8f25", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4d9377bb-6a58-4267-b76d-11bdc78d31ce", "metadata": {}, "outputs": [], "source": ["index_data_pname_MULTI[\"indexes\"][\"pname_ANTI_HP_DISABLE\"].save_index(\n", "    f\"{BUTTERFLY_LOCAL_FOLDER}/index_pname_MULTI.bin\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ecd7b400-0c65-4f40-b7ce-ef8a38e9576e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "729f6423-9e86-44dd-a890-d8aa5b0d2baf", "metadata": {}, "outputs": [], "source": ["del index_data_pname_MULTI[\"indexes\"]"]}, {"cell_type": "code", "execution_count": null, "id": "2ec86677-0adf-4e6a-867e-73cf29fadd43", "metadata": {}, "outputs": [], "source": ["print(index_data_pname_MULTI.keys())"]}, {"cell_type": "code", "execution_count": null, "id": "070e1502-8d07-4227-a6e4-569f3532ba19", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6542daa6-fbc2-486e-a40f-477fa9941ce3", "metadata": {}, "outputs": [], "source": ["with open(f\"{BUTTERFLY_LOCAL_FOLDER}/index_data_pname_MULTI.pkl\", \"wb\") as f:\n", "    pickle.dump(index_data_pname_MULTI, f)"]}, {"cell_type": "code", "execution_count": null, "id": "c8d49bf4-ab22-415d-8c3b-25147081c68b", "metadata": {}, "outputs": [], "source": ["with open(f\"{BUTTERFLY_LOCAL_FOLDER}/hnsw_index_config.json\", \"w\") as f:\n", "    json.dump(hnsw_index_config, f)"]}, {"cell_type": "code", "execution_count": null, "id": "db372502-eb22-4c0b-9c3a-a04e14675ef9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6f68449e-bfe8-4404-b73f-c6e52409c5d1", "metadata": {}, "outputs": [], "source": ["!ls -lh {BUTTERFLY_LOCAL_FOLDER}"]}, {"cell_type": "code", "execution_count": null, "id": "746bbe8c-8ec5-47e4-9d60-b973f4606f69", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "002a66ca-2bd0-4f41-994b-c299ba6b68ed", "metadata": {}, "source": ["# Test PUSH"]}, {"cell_type": "code", "execution_count": null, "id": "3145dcf3-2617-47d4-87cb-6afef614524b", "metadata": {}, "outputs": [], "source": ["s3_path = f\"s3://prod-dse-projects/data-science/ner/search_query_to_product/v2/{date}/\""]}, {"cell_type": "code", "execution_count": null, "id": "dd0955e1-7337-405a-82d7-9135407ddcc3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2bd02bce-6a19-47e4-adff-cc97a730dd84", "metadata": {}, "outputs": [], "source": ["wr.s3.list_objects(s3_path)"]}, {"cell_type": "code", "execution_count": null, "id": "a8735d25-95df-4cf6-8f69-36a028bb05a3", "metadata": {}, "outputs": [], "source": ["# List local files\n", "local_files = []\n", "for root, dirs, files in os.walk(BUTTERFLY_LOCAL_FOLDER):\n", "    for file in files:\n", "        local_files.append(os.path.join(root, file))\n", "local_files"]}, {"cell_type": "code", "execution_count": null, "id": "2bb6a45e-f14b-44f9-b904-109ad6db34be", "metadata": {}, "outputs": [], "source": ["# Upload files to S3\n", "for file in local_files:\n", "    relative_path = os.path.relpath(file, start=BUTTERFLY_LOCAL_FOLDER)\n", "    print(relative_path)\n", "    if \"checkpoint\" in relative_path:\n", "        print(\"-- checkpoint file found, skipping\")\n", "        continue\n", "    s3_file_path = f\"{s3_path}/{relative_path}\"\n", "    if not wr.s3.list_objects(s3_file_path):\n", "        wr.s3.upload(\n", "            local_file=file,\n", "            path=s3_file_path,\n", "            s3_additional_kwargs={\"ACL\": \"bucket-owner-full-control\"},\n", "        )\n", "    print(f\"uploaded file {relative_path}\")"]}, {"cell_type": "code", "execution_count": null, "id": "6ce20b9e-ef46-4986-993b-fb5582f40e2c", "metadata": {}, "outputs": [], "source": ["wr.s3.list_objects(s3_path)"]}, {"cell_type": "code", "execution_count": null, "id": "152a00db-fb3d-4a04-beca-4483757ade1b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "2540c31e-1dab-495a-836a-37eed2f71618", "metadata": {"tags": []}, "source": ["# Push Data File to Zomato S3 bucket"]}, {"cell_type": "code", "execution_count": null, "id": "44dc0751-3097-48e0-963e-b8636035b620", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a48aae7f-7132-45a8-a487-b06b277d7bf4", "metadata": {}, "outputs": [], "source": ["s3_path = \"s3://ml-blinkit-models-prod/models/search-query-to-product/v2\""]}, {"cell_type": "code", "execution_count": null, "id": "eef11927-49df-4c43-be71-798ef1c6a33b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "43247c9f-6967-416f-8635-58cc1f6891dd", "metadata": {}, "outputs": [], "source": ["wr.s3.list_objects(s3_path)"]}, {"cell_type": "code", "execution_count": null, "id": "dca61efc-184b-4c65-9177-8b89320b137f", "metadata": {}, "outputs": [], "source": ["# List local files\n", "local_files = []\n", "for root, dirs, files in os.walk(BUTTERFLY_LOCAL_FOLDER):\n", "    for file in files:\n", "        local_files.append(os.path.join(root, file))\n", "local_files"]}, {"cell_type": "code", "execution_count": null, "id": "cf8b4ec3-7ab3-46e8-9e35-d1a2177e49cb", "metadata": {}, "outputs": [], "source": ["# Upload files to S3\n", "for file in local_files:\n", "    relative_path = os.path.relpath(file, start=BUTTERFLY_LOCAL_FOLDER)\n", "    print(relative_path)\n", "    if \"checkpoint\" in relative_path:\n", "        print(\"-- checkpoint file found, skipping\")\n", "        continue\n", "    s3_file_path = f\"{s3_path}/{relative_path}\"\n", "\n", "    print(\"uploading now....\")\n", "    wr.s3.upload(\n", "        local_file=file,\n", "        path=s3_file_path,\n", "        s3_additional_kwargs={\"ACL\": \"bucket-owner-full-control\"},\n", "    )\n", "    print(f\"uploaded file {relative_path}\")"]}, {"cell_type": "code", "execution_count": null, "id": "77cc81ca-1a32-4926-b366-6f8dc4077897", "metadata": {}, "outputs": [], "source": ["wr.s3.list_objects(s3_path)"]}, {"cell_type": "code", "execution_count": null, "id": "5cca3bc2-fd5a-4ecb-b642-1dd3a19d385a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9267bbbb-bc7e-48c0-bea7-9d7bd02ad825", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "747783d5-5860-48fd-bb48-6d9ff0b026ca", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c3f62712-632d-47e4-b484-de7c4e10ed0a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "690427b9-f57c-43be-ad0c-119d0a0b649c", "metadata": {}, "source": ["# Inference"]}, {"cell_type": "code", "execution_count": null, "id": "e1c91d6c-84df-4cac-8d30-643b33d245f0", "metadata": {}, "outputs": [], "source": ["s3_objects = wr.s3.list_objects(s3_path)"]}, {"cell_type": "code", "execution_count": null, "id": "adbca5c4-69a8-4716-937d-60241e032bc0", "metadata": {}, "outputs": [], "source": ["s3_objects"]}, {"cell_type": "code", "execution_count": null, "id": "25c25e42-1f96-4c51-b6a3-531d3c9bc72a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "02b71ab3-f715-44e7-9ff2-7b9b6a42ac2f", "metadata": {}, "outputs": [], "source": ["DOWNLOAD_FOLDER = f\"{BUTTERFLY_LOCAL_FOLDER}_TEST\"\n", "\n", "print(DOWNLOAD_FOLDER)\n", "\n", "if not os.path.exists(DOWNLOAD_FOLDER):\n", "    os.makedirs(DOWNLOAD_FOLDER)"]}, {"cell_type": "code", "execution_count": null, "id": "34de65a8-35e5-464e-b352-6eace7198be2", "metadata": {}, "outputs": [], "source": ["!ls -lh {DOWNLOAD_FOLDER}"]}, {"cell_type": "code", "execution_count": null, "id": "16b74858-3546-48c9-a381-263cc59c987e", "metadata": {"tags": []}, "outputs": [], "source": ["# Download objects\n", "for obj in s3_objects:\n", "\n", "    relative_path = os.path.relpath(obj, start=s3_path)\n", "    if \"checkpoint\" in relative_path:\n", "        print(\"-- checkpoint file found, skipping\")\n", "        continue\n", "    local_file_path = os.path.join(DOWNLOAD_FOLDER, relative_path)\n", "\n", "    wr.s3.download(path=obj, local_file=local_file_path)\n", "    print(f\"downloaded file {relative_path}\")"]}, {"cell_type": "code", "execution_count": null, "id": "6242fa0a-dbc9-40a5-a649-b74416828122", "metadata": {}, "outputs": [], "source": ["!ls -lh {DOWNLOAD_FOLDER}"]}, {"cell_type": "code", "execution_count": null, "id": "dc13a2ff-1251-4630-8ac1-437108017b4c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fcb71ab2-d175-4b0d-82fc-31901a23c3d8", "metadata": {}, "outputs": [], "source": ["with open(f\"{DOWNLOAD_FOLDER}/hnsw_index_config.json\", \"r\") as f:\n", "    config = json.load(f)\n", "\n", "# Reinitialize the index with the loaded configuration\n", "index = hnswlib.Index(space=config[\"space\"], dim=config[\"dim\"])\n", "index.init_index(\n", "    max_elements=config[\"max_elements\"],\n", "    M=config[\"M\"],\n", "    ef_construction=config[\"ef_construction\"],\n", "    random_seed=config[\"random_seed\"],\n", ")\n", "\n", "# Load the previously saved index\n", "index.load_index(f\"{DOWNLOAD_FOLDER}/q2p_synthetic_index.bin\")"]}, {"cell_type": "code", "execution_count": null, "id": "5b3a3b11-29a9-4309-8fb1-fa1e6b03ade0", "metadata": {}, "outputs": [], "source": ["# Reinitialize the index with the loaded configuration\n", "multi_index = hnswlib.Index(space=config[\"space\"], dim=config[\"dim\"])\n", "multi_index.init_index(\n", "    max_elements=config[\"max_elements\"],\n", "    M=config[\"M\"],\n", "    ef_construction=config[\"ef_construction\"],\n", "    random_seed=config[\"random_seed\"],\n", ")\n", "\n", "# Load the previously saved index\n", "multi_index.load_index(f\"{DOWNLOAD_FOLDER}/index_pname_MULTI.bin\")"]}, {"cell_type": "code", "execution_count": null, "id": "ed4b45da-6542-4d0f-a10d-edeecf9fcb38", "metadata": {}, "outputs": [], "source": ["with open(f\"{DOWNLOAD_FOLDER}/index_data_pname_MULTI.pkl\", \"rb\") as f:\n", "    index_data_pname_MULTI = pickle.load(f)"]}, {"cell_type": "code", "execution_count": null, "id": "4adb08a5-9c3e-474e-91a8-8ba7bd821fbb", "metadata": {}, "outputs": [], "source": ["with open(f\"{DOWNLOAD_FOLDER}/q2p_synthetic_queries.pkl\", \"rb\") as f:\n", "    q2p_synthetic_queries = pickle.load(f)"]}, {"cell_type": "code", "execution_count": null, "id": "04167688-3e47-4a96-8e21-fb91ad185f6f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d23b7340-07e5-4c06-9dd6-ce6b8349154c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "210f007d-8276-4af1-9bbd-8e534700192a", "metadata": {}, "outputs": [], "source": ["multi_index.knn_query(all_embeddings_dict[\"milk\"], 5)"]}, {"cell_type": "code", "execution_count": null, "id": "0bbe72af-8bbf-46fe-a9a7-14b5335d1e1f", "metadata": {}, "outputs": [], "source": ["labels, distances = multi_index.knn_query(all_embeddings_dict[\"milk\"], 1000)\n", "sim = 1 - distances\n", "res = list(zip(*labels, *sim))[:5]\n", "I, sim = list(zip(*res))\n", "\n", "I, sim"]}, {"cell_type": "code", "execution_count": null, "id": "20acb08c-315b-4dc5-9025-204ff8740a86", "metadata": {}, "outputs": [], "source": ["index_data_pname_MULTI[\"values\"][\"pname_ANTI_HP_DISABLE\"][list(I)]"]}, {"cell_type": "code", "execution_count": null, "id": "3a93af21-199d-4d60-869a-4e408db1c83f", "metadata": {}, "outputs": [], "source": ["index_data_pname_MULTI[\"ids\"][\"pname_ANTI_HP_DISABLE\"][list(I)]"]}, {"cell_type": "code", "execution_count": null, "id": "3619b9d4-8660-4035-8693-4764367f925c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "77cb2886-3852-4eb9-afb2-6fa9e83ba2ef", "metadata": {}, "outputs": [], "source": ["labels, distances = index.knn_query(all_embeddings_dict[\"milk\"], 1000)\n", "sim = 1 - distances\n", "res = list(zip(*labels, *sim))[:5]\n", "I, sim = list(zip(*res))\n", "\n", "I, sim"]}, {"cell_type": "code", "execution_count": null, "id": "ac2232a9-528c-41fb-ba69-4df218fbed11", "metadata": {}, "outputs": [], "source": ["q2p_synthetic_queries[list(I)]"]}, {"cell_type": "code", "execution_count": null, "id": "7bd4152b-45b2-4761-9560-5806ffabc1ba", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cd53abfb-a456-4630-926f-fa2084badbf5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3272d469-3aad-4109-b7fd-b421ab09a505", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}