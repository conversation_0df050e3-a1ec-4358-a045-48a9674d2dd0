{"cells": [{"cell_type": "markdown", "id": "c31bf150-ba1d-451a-909b-caa97ab4d58d", "metadata": {}, "source": ["# Install Libraries"]}, {"cell_type": "code", "execution_count": null, "id": "f9890667-7aa1-4c7d-ab87-efd38769958d", "metadata": {"tags": []}, "outputs": [], "source": ["!pip install -q scipy==1.8.0\n", "!pip install -q aiohttp==3.8.1\n", "!pip install -q fsspec==2023.1.0\n", "!pip install -q aiobotocore==2.4.2\n", "!pip install -q pymysql==1.0.2\n", "!pip install -q gremlinpython==3.6.2\n", "!pip install -q botocore==1.27.59\n", "!pip install -q progressbar2==4.2.0\n", "!pip install -q backoff==2.2.1\n", "!pip install -q pg8000==1.29.4\n", "!pip install -q opensearch-py==2.1.1\n", "!pip install -q boto3==1.24.59\n", "!pip install -q requests-aws4auth==1.2.2\n", "!pip install -q s3transfer==0.6.0\n", "!pip install -q aenum==3.1.11\n", "!pip install -q scramp==1.4.4\n", "!pip install -q python-utils==3.5.2\n", "!pip install -q awswrangler==2.19.0\n", "!pip install -q s3fs==2023.1.0\n", "!pip install -q sqlalchemy==1.4.16"]}, {"cell_type": "markdown", "id": "912bb72b-238f-4bdf-86f6-74c201cf5457", "metadata": {"tags": []}, "source": ["# Append current working directory and Imports"]}, {"cell_type": "code", "execution_count": null, "id": "3d6c8b17-5571-4eb7-a5b0-fad6d16c5902", "metadata": {}, "outputs": [], "source": ["import os, sys"]}, {"cell_type": "code", "execution_count": null, "id": "1b1fea6a-d116-402b-b1de-d46c9d4ae1d5", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "f1031c65-5512-4dfa-9771-fe47cc90d2d7", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "1b935952-9ae8-4e97-b438-461ea299a27b", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "\n", "import random, sys\n", "import os\n", "import ast\n", "\n", "import helper\n", "import queries\n", "from datetime import datetime, timedelta\n", "import gc\n", "\n", "use_sheet = True"]}, {"cell_type": "markdown", "id": "9ef490c9-aee1-4a67-a653-ef6862bd1061", "metadata": {}, "source": ["# Product Metadata"]}, {"cell_type": "code", "execution_count": null, "id": "9f434e92-a58e-40b0-a444-ab79b8c89ec6", "metadata": {}, "outputs": [], "source": ["# Reading all products metadata info from dim_product table\n", "dim_prod = helper.run_query_with_retries(\n", "    \"\"\"\n", "    select distinct \n", "        product_id,\n", "        product_name,\n", "        product_type,\n", "        product_type_id,\n", "        l0_category,\n", "        l0_category_id,\n", "        l1_category,\n", "        l1_category_id\n", "    from dwh.dim_product\n", "    where is_current \n", "    and is_product_enabled\n", "    \"\"\"\n", ")\n", "\n", "prod_info = dim_prod[\n", "    [\n", "        \"product_id\",\n", "        \"product_name\",\n", "        \"product_type_id\",\n", "        \"product_type\",\n", "        \"l1_category_id\",\n", "        \"l0_category_id\",\n", "    ]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "1b1e224e-7044-49c6-b9c5-76aaf6928507", "metadata": {}, "outputs": [], "source": ["# Bistro data size\n", "prod_info.query(\"l0_category_id == 4265\").shape"]}, {"cell_type": "markdown", "id": "bad227ac-0b8f-4162-be00-61edd6af0733", "metadata": {}, "source": ["# Bistro metadata"]}, {"cell_type": "code", "execution_count": null, "id": "c4b0b450-24e8-4794-9912-4f1f8c4860fb", "metadata": {}, "outputs": [], "source": ["# Filtering Bistro Products\n", "bistro_prod_info = prod_info.query(\"l0_category_id == 4265\").reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "085c84cc-eed3-413f-bff7-f50ec28fdf31", "metadata": {}, "outputs": [], "source": ["bistro_prod_info"]}, {"cell_type": "markdown", "id": "2bc339e6-5cae-4b3f-8cbe-da121c10b3e8", "metadata": {}, "source": ["# Blacklisted products"]}, {"cell_type": "code", "execution_count": null, "id": "f642429b-a991-4753-960b-67255facdf10", "metadata": {}, "outputs": [], "source": ["# Reading various levels of blacklist data (product/category exclusions) for both antecedents and consequents\n", "(\n", "    exclusion_all_l0,\n", "    exclusion_all_l1,\n", "    exclusion_all_ptype,\n", "    exclusion_all_pid,\n", "    exclusion_antecedent_l0,\n", "    exclusion_antecedent_l1,\n", "    exclusion_antecedent_ptype,\n", "    exclusion_antecedent_pid,\n", "    exclusion_consequent_l0,\n", "    exclusion_consequent_l1,\n", "    exclusion_consequent_ptype,\n", "    exclusion_consequent_pid,\n", ") = helper.get_blacklist_data(widget=\"NPR\", from_sheet=use_sheet)"]}, {"cell_type": "code", "execution_count": null, "id": "16b10427-8435-4fc6-87f8-b634e9156e5e", "metadata": {}, "outputs": [], "source": ["# Merging master product type (ptype) mapping data with product metadata info\n", "prod_info = prod_info.loc[\n", "    (~prod_info.product_id.isin(exclusion_all_pid))\n", "    & (~prod_info.product_type_id.isin(exclusion_all_ptype))\n", "    & (\n", "        ~prod_info.l1_category_id.isin(exclusion_all_l1)\n", "        & (~prod_info.l0_category_id.isin(exclusion_all_l0))\n", "    )\n", "]\n", "\n", "# Dropping the L0 and L1 category ID columns after filtering, as they are no longer needed\n", "prod_info.drop(columns=[\"l1_category_id\", \"l0_category_id\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "c49ccb73-e649-4ecb-bf51-0be7009f1dcb", "metadata": {}, "outputs": [], "source": ["# Sanity check\n", "prod_info.merge(bistro_prod_info, on=[\"product_id\"])"]}, {"cell_type": "markdown", "id": "67491f78-202d-45af-bafa-1790bcda83f4", "metadata": {}, "source": ["# Master product type"]}, {"cell_type": "code", "execution_count": null, "id": "da36bc9d-4c33-4b82-a4ac-8bc1ea5faa62", "metadata": {}, "outputs": [], "source": ["# Reading master ptype data\n", "ptype_mappings = helper.get_master_ptype_data(from_sheet=use_sheet)"]}, {"cell_type": "code", "execution_count": null, "id": "70460703-9960-448b-a5b9-9266c52d306c", "metadata": {}, "outputs": [], "source": ["# Merging master product type (ptype) mapping data with product metadata info\n", "prod_info = pd.merge(prod_info, ptype_mappings, on=[\"product_type_id\", \"product_type\"], how=\"left\")\n", "\n", "# Use \"Group Name\" from the master ptype mapping if available; otherwise fall back to the original \"product_type\"\n", "prod_info[\"product_type_final\"] = prod_info[\"Group Name\"].combine_first(prod_info[\"product_type\"])\n", "\n", "# Use \"master_ptype_id\" if available; otherwise fall back to the original \"product_type_id\"\n", "prod_info[\"product_type_id_final\"] = prod_info[\"master_ptype_id\"].combine_first(\n", "    prod_info[\"product_type_id\"]\n", ")\n", "\n", "# Drop the now redundant intermediate columns used for merging\n", "prod_info.drop(columns=[\"master_ptype_id\", \"Group Name\"], inplace=True)\n", "\n", "# Ensure the final product type ID column is of integer type\n", "prod_info[\"product_type_id_final\"] = prod_info[\"product_type_id_final\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "27dd451d-6bfd-433e-bc16-2dbf0c09fa5b", "metadata": {}, "outputs": [], "source": ["prod_info.isna().sum()"]}, {"cell_type": "markdown", "id": "7d2eafa7-c47c-4740-9b07-925084dfdf3c", "metadata": {}, "source": ["# Push pre-processed product metadata to s3"]}, {"cell_type": "code", "execution_count": null, "id": "02eb3549-cfba-4138-87bd-3a8b273348a4", "metadata": {}, "outputs": [], "source": ["# push to s3\n", "prod_info.to_csv(\"/tmp/prod_info_data.csv\", index=False)\n", "pb.to_s3(\n", "    \"/tmp/prod_info_data.csv\",\n", "    \"prod-dse-projects\",\n", "    \"recommendations/NPR/exp_a/supporting_files/prod_info_data.csv\",\n", ")"]}, {"cell_type": "markdown", "id": "55a276bf-25eb-469f-b87a-c2d26122afe9", "metadata": {}, "source": ["# Creating dim product with master ptype"]}, {"cell_type": "code", "execution_count": null, "id": "63b12ab1-3dba-4c0d-a49e-e1a673e69dc4", "metadata": {}, "outputs": [], "source": ["# creating dim product with master ptype with id on trino\n", "dim_prod = dim_prod.merge(\n", "    prod_info[\n", "        [\n", "            \"product_type_id\",\n", "            \"product_type\",\n", "            \"product_type_final\",\n", "            \"product_type_id_final\",\n", "        ]\n", "    ].drop_duplicates(),\n", "    on=[\"product_type_id\", \"product_type\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1bd0a0e3-0796-45e3-a721-191ee2e34306", "metadata": {}, "outputs": [], "source": ["dim_prod.shape"]}, {"cell_type": "code", "execution_count": null, "id": "dc413249-7d23-43f8-b599-191a350abc31", "metadata": {}, "outputs": [], "source": ["dim_prod.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "b4b1d98f-67db-4c63-88ba-c3ae74de793e", "metadata": {}, "outputs": [], "source": ["# Null value imputation\n", "dim_prod.loc[dim_prod.product_type_final.isna(), \"product_type_final\"] = dim_prod.loc[\n", "    dim_prod.product_type_final.isna(), \"product_type\"\n", "]\n", "dim_prod.loc[dim_prod.product_type_id_final.isna(), \"product_type_id_final\"] = dim_prod.loc[\n", "    dim_prod.product_type_id_final.isna(), \"product_type_id\"\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "61e7b566-8de5-4e09-892f-cd39377e1007", "metadata": {}, "outputs": [], "source": ["dim_prod.rename(\n", "    columns={\n", "        \"product_type_final\": \"master_ptype\",\n", "        \"product_type_id_final\": \"master_ptype_id\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9246c8f5-bdd6-4bb9-8491-f97906d830ae", "metadata": {}, "outputs": [], "source": ["dim_prod[\"master_ptype_id\"] = dim_prod[\"master_ptype_id\"].astype(\"int\")"]}, {"cell_type": "code", "execution_count": null, "id": "f8b44217-dfaf-4de8-b508-c0ddd214a237", "metadata": {}, "outputs": [], "source": ["dim_prod.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "6d62ed2e-c448-4ae3-a928-a012effc1e87", "metadata": {}, "outputs": [], "source": ["dim_prod.drop_duplicates().shape, dim_prod.shape"]}, {"cell_type": "markdown", "id": "ce71f287-ece1-4d0d-ab48-145359622c9c", "metadata": {}, "source": ["# Push to trino table"]}, {"cell_type": "code", "execution_count": null, "id": "eae9a239-859e-472c-9ee2-a01e3f74517f", "metadata": {}, "outputs": [], "source": ["def get_redshift_table_kwargs(table_name):\n", "    return {\n", "        \"schema_name\": \"consumer_intelligence_etls\",\n", "        \"table_name\": table_name,\n", "        \"column_dtypes\": [\n", "            {\"name\": \"product_id\", \"type\": \"INTEGER\", \"description\": \"product id\"},\n", "            {\n", "                \"name\": \"product_name\",\n", "                \"type\": \"VARCHAR\",\n", "                \"description\": \"product name\",\n", "            },\n", "            {\n", "                \"name\": \"product_type\",\n", "                \"type\": \"VARCHAR\",\n", "                \"description\": \"product_type\",\n", "            },\n", "            {\n", "                \"name\": \"product_type_id\",\n", "                \"type\": \"INTEGER\",\n", "                \"description\": \"product_type_id\",\n", "            },\n", "            {\n", "                \"name\": \"l0_category\",\n", "                \"type\": \"VARCHAR\",\n", "                \"description\": \"l0_category\",\n", "            },\n", "            {\n", "                \"name\": \"l0_category_id\",\n", "                \"type\": \"INTEGER\",\n", "                \"description\": \"l0_category_id\",\n", "            },\n", "            {\n", "                \"name\": \"l1_category\",\n", "                \"type\": \"VARCHAR\",\n", "                \"description\": \"l1_category\",\n", "            },\n", "            {\n", "                \"name\": \"l1_category_id\",\n", "                \"type\": \"INTEGER\",\n", "                \"description\": \"l1_category_id\",\n", "            },\n", "            {\"name\": \"master_ptype\", \"type\": \"VARCHAR\", \"description\": \"master_ptype\"},\n", "            {\n", "                \"name\": \"master_ptype_id\",\n", "                \"type\": \"INTEGER\",\n", "                \"description\": \"master_ptype_id\",\n", "            },\n", "        ],\n", "        \"sortkey\": [\"product_id\"],\n", "        \"partition_key\": [\"master_ptype\"],\n", "        \"load_type\": \"truncate\",  # append, overwrite, truncate or upsert,\n", "        \"table_description\": \"Master ptype data\",\n", "    }"]}, {"cell_type": "code", "execution_count": null, "id": "a52fdd9a-5293-4b67-a8bd-73fb1906fb99", "metadata": {}, "outputs": [], "source": ["kwargs = get_redshift_table_kwargs(\"personalisation_master_ptype\")\n", "pb.to_trino(dim_prod, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "6a95e425-0902-4a20-bc18-189525af7bd6", "metadata": {}, "outputs": [], "source": ["dim_prod = []\n", "del dim_prod\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "deff1af3-12a8-4b14-a1e1-97eec6dad30f", "metadata": {}, "source": ["# Reading Festive sheet"]}, {"cell_type": "code", "execution_count": null, "id": "c167370c-240f-463a-926c-71e0e755da04", "metadata": {}, "outputs": [], "source": ["# festive and gifting support\n", "gifting_df, festival_df, fnv_df = helper.get_festival_df(from_sheet=use_sheet)"]}, {"cell_type": "code", "execution_count": null, "id": "3dcad72f-1b17-48c8-a17f-199dc1f25aeb", "metadata": {}, "outputs": [], "source": ["festival_df"]}, {"cell_type": "code", "execution_count": null, "id": "cfbea8b6-b9bc-4cbe-90d9-ed0d7934d104", "metadata": {}, "outputs": [], "source": ["gifting_df"]}, {"cell_type": "code", "execution_count": null, "id": "f6ec3eff-b335-41af-a0aa-7884bfdf588d", "metadata": {}, "outputs": [], "source": ["fnv_df"]}, {"cell_type": "markdown", "id": "e366a0b2-0bd8-4855-bfdd-c60a6a49c433", "metadata": {}, "source": ["# Reading complimentary sheet for all ptypes"]}, {"cell_type": "code", "execution_count": null, "id": "673aa7c9-3f58-4a58-a40f-ee70e0627731", "metadata": {}, "outputs": [], "source": ["# reading complimentary sheet data\n", "ptype_data = helper.get_complimentary_data(\n", "    festival_df, gifting_df, fnv_df, festive=False, gifting=False, fnv=False, from_sheet=use_sheet\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "30d3e0f4-e072-4082-a866-f5e9ba7bcae7", "metadata": {}, "outputs": [], "source": ["ptype_data.ptype.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "002cd1ac-091c-442a-8474-0110c7dc5286", "metadata": {}, "outputs": [], "source": ["festive_ptype_data = helper.get_complimentary_data(\n", "    festival_df, gifting_df, fnv_df, festive=True, gifting=False, fnv=False, from_sheet=use_sheet\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c0001967-a0bf-4694-87b0-a69cddafef1a", "metadata": {}, "outputs": [], "source": ["gifting_ptype_data = helper.get_complimentary_data(\n", "    festival_df, gifting_df, fnv_df, festive=False, gifting=True, fnv=False, from_sheet=use_sheet\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c31f1d63-41f1-49b1-a865-494620faaed7", "metadata": {}, "outputs": [], "source": ["fnv_ptype_data = helper.get_complimentary_data(\n", "    festival_df, gifting_df, fnv_df, festive=False, gifting=False, fnv=True, from_sheet=use_sheet\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "010ed57b-f560-4323-9562-13bdbd7cfe5f", "metadata": {}, "outputs": [], "source": ["if len(fnv_ptype_data) > 0:\n", "    print(fnv_ptype_data.ptype.nunique())\n", "    ptype_data = ptype_data.loc[\n", "        ~ptype_data[\"ptype\"].isin(fnv_ptype_data.ptype.unique())\n", "    ].reset_index(drop=True)\n", "    ptype_data = pd.concat([ptype_data, fnv_ptype_data]).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "e477f12b-f1be-4d99-895a-b19dc8f853d7", "metadata": {}, "outputs": [], "source": ["if len(gifting_ptype_data) > 0:\n", "    print(gifting_ptype_data.ptype.nunique())\n", "    ptype_data = ptype_data.loc[\n", "        ~ptype_data[\"ptype\"].isin(gifting_ptype_data.ptype.unique())\n", "    ].reset_index(drop=True)\n", "    ptype_data = pd.concat([ptype_data, gifting_ptype_data]).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "54d63afd-bb0d-4557-a22b-e3ad136f5fc4", "metadata": {}, "outputs": [], "source": ["if len(festive_ptype_data) > 0:\n", "    print(festive_ptype_data.ptype.nunique())\n", "    ptype_data = ptype_data.loc[\n", "        ~ptype_data[\"ptype\"].isin(festive_ptype_data.ptype.unique())\n", "    ].reset_index(drop=True)\n", "    ptype_data = pd.concat([ptype_data, festive_ptype_data]).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "eefbcc9c-e981-45f9-9766-bcb4519a4e57", "metadata": {}, "outputs": [], "source": ["ptype_data.ptype.nunique()"]}, {"cell_type": "markdown", "id": "7ae303e0-0bf8-4a1c-9d72-cdb60374b4a3", "metadata": {}, "source": ["# Creating final complimentary sheet"]}, {"cell_type": "code", "execution_count": null, "id": "0725e3f6-78dd-4c3b-8b85-16bd5e86e342", "metadata": {}, "outputs": [], "source": ["# getting ptypeid for antecedent\n", "ptype_data = pd.merge(\n", "    ptype_data,\n", "    prod_info[[\"product_type\", \"product_type_id\"]]\n", "    .drop_duplicates()\n", "    .rename(\n", "        columns={\n", "            \"product_type\": \"ptype\",\n", "            \"product_type_id\": \"product_type_id_ant\",\n", "        }\n", "    ),\n", "    on=[\"ptype\"],\n", "    how=\"left\",\n", ")\n", "\n", "# getting master ptype for consequent\n", "ptype_data = pd.merge(\n", "    ptype_data,\n", "    prod_info[[\"product_type\", \"product_type_final\", \"product_type_id\"]]\n", "    .drop_duplicates()\n", "    .rename(\n", "        columns={\n", "            \"product_type\": \"value\",\n", "            \"product_type_final\": \"final_product_type_cons\",\n", "            \"product_type_id\": \"product_type_id_cons\",\n", "        }\n", "    ),\n", "    on=[\"value\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b5aad185-cd0a-4be0-9b11-27993fc5afca", "metadata": {}, "outputs": [], "source": ["ptype_data.rename(columns={\"ptype\": \"final_product_type_ant\"}, inplace=True)\n", "ptype_data.dropna(subset=[\"final_product_type_cons\"], inplace=True)\n", "ptype_data.drop(columns=[\"value\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "22758ba0-1eb8-465f-8236-567b0fb0303d", "metadata": {}, "outputs": [], "source": ["ptype_data = ptype_data.drop_duplicates(\n", "    subset=[\"final_product_type_ant\", \"final_product_type_cons\"]\n", ")\n", "\n", "ptype_data.sort_values([\"final_product_type_ant\", \"cons_ptype_rank\"], inplace=True)\n", "ptype_data.reset_index(drop=True, inplace=True)\n", "\n", "ptype_data.dropna(subset=[\"product_type_id_ant\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "e3dc8cd0-fec0-4348-a46f-80b0c902ce46", "metadata": {}, "outputs": [], "source": ["ptype_data[\"product_type_id_ant\"] = ptype_data[\"product_type_id_ant\"].astype(int)\n", "ptype_data[\"product_type_id_cons\"] = ptype_data[\"product_type_id_cons\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "2a7c78ba-0235-4d3d-ae05-8e2c5c800b60", "metadata": {}, "outputs": [], "source": ["ptype_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "48347e36-bc53-46ef-9632-d8c21365ea0a", "metadata": {}, "outputs": [], "source": ["# removing blacklist data - antecedent and consequent specific\n", "ptype_data = ptype_data[\n", "    ~ptype_data[\"product_type_id_cons\"].isin(exclusion_all_ptype + exclusion_consequent_ptype)\n", "]\n", "ptype_data = ptype_data[\n", "    ~ptype_data[\"product_type_id_ant\"].isin(exclusion_all_ptype + exclusion_antecedent_ptype)\n", "]\n", "ptype_data.reset_index(drop=True, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "ad78077a-83b2-4bd2-b264-006341af11a7", "metadata": {}, "outputs": [], "source": ["ptype_data.sort_values([\"final_product_type_ant\", \"cons_ptype_rank\"], inplace=True)\n", "ptype_data.reset_index(drop=True, inplace=True)\n", "\n", "ptype_data[\"rank\"] = 1\n", "ptype_data[\"rank\"] = ptype_data.groupby(\"final_product_type_ant\")[\"rank\"].cumsum()"]}, {"cell_type": "code", "execution_count": null, "id": "189e1d46-2327-40b4-a7a5-13efeab9f4d2", "metadata": {}, "outputs": [], "source": ["ptype_data.rename(\n", "    columns={\n", "        \"final_product_type_ant\": \"antecedent_product_type\",\n", "        \"final_product_type_cons\": \"consequent_product_type\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e025d8a1-ff9a-4350-b92a-8b2cdf5c1402", "metadata": {}, "outputs": [], "source": ["ptype_data.dropna(subset=[\"antecedent_product_type\"], inplace=True)\n", "ptype_data.reset_index(drop=True, inplace=True)"]}, {"cell_type": "markdown", "id": "0145086d-87a6-4523-a162-32652001f470", "metadata": {}, "source": ["# Sanity Check"]}, {"cell_type": "code", "execution_count": null, "id": "2c4d023a-d99f-4d74-b5ee-269959d04b75", "metadata": {}, "outputs": [], "source": ["ptype_data.query(\"antecedent_product_type == 'Book'\")"]}, {"cell_type": "code", "execution_count": null, "id": "4549e6ea-4039-4cf2-a7ef-63a47c0d5ebb", "metadata": {}, "outputs": [], "source": ["ptype_data.query(\"antecedent_product_type == 'Magazine'\")"]}, {"cell_type": "code", "execution_count": null, "id": "2ecc8560-44df-4fb7-b0ff-7cb2a5ffb984", "metadata": {}, "outputs": [], "source": ["ptype_data.query(\"antecedent_product_type == 'Combo'\")"]}, {"cell_type": "code", "execution_count": null, "id": "4569a585-c635-408b-9be3-feadee562a9d", "metadata": {}, "outputs": [], "source": ["ptype_data.query(\"antecedent_product_type == 'TWS Earbuds'\")"]}, {"cell_type": "code", "execution_count": null, "id": "ee566b70-47f6-4d8c-a826-258181d6c858", "metadata": {}, "outputs": [], "source": ["ptype_data.query(\"antecedent_product_type == 'Lipstick'\")"]}, {"cell_type": "code", "execution_count": null, "id": "ca8bd8e1-16af-47d6-9aef-34ebb66671d9", "metadata": {}, "outputs": [], "source": ["ptype_data.query(\"antecedent_product_type == 'Aloo Chips'\")"]}, {"cell_type": "code", "execution_count": null, "id": "481ba443-34e4-42a1-bc70-0ef7fad2d4f3", "metadata": {}, "outputs": [], "source": ["ptype_data.query(\"antecedent_product_type == 'Art Kit'\")"]}, {"cell_type": "code", "execution_count": null, "id": "1add78c0-ad72-4de9-a906-0560c92c257d", "metadata": {}, "outputs": [], "source": ["ptype_data.query(\"antecedent_product_type == 'Artificial Rose'\")"]}, {"cell_type": "markdown", "id": "c6dc5968-c662-470f-9aa7-0db2a6b24cb1", "metadata": {}, "source": ["# Push ptype ptype ranking data to s3"]}, {"cell_type": "code", "execution_count": null, "id": "51cb4580-a98f-4d47-b54b-deba718573cf", "metadata": {}, "outputs": [], "source": ["# pushing to s3\n", "ptype_data.to_csv(\"/tmp/ptype_ptype_rankings.csv\", index=False)\n", "pb.to_s3(\n", "    \"/tmp/ptype_ptype_rankings.csv\",\n", "    \"prod-dse-projects\",\n", "    \"recommendations/NPR/exp_a/supporting_files/ptype_ptype_rankings.csv\",\n", ")"]}, {"cell_type": "markdown", "id": "3680beea-9838-4cd9-b3d9-48eb6987e9f9", "metadata": {}, "source": ["# Add low repeat ptype flags"]}, {"cell_type": "code", "execution_count": null, "id": "04cd73af-1fe3-417d-8891-d8ba0ab0f227", "metadata": {}, "outputs": [], "source": ["df_low_repeat_final = helper.run_query_with_retries(\n", "    f\"\"\"\n", "select product_type,\n", "       master_product_type as product_type_final,\n", "       is_low_repeat_product_type as is_low_repeat_ptype,\n", "       is_low_repeat_master_product_type as is_low_repeat_master_ptype\n", "from consumer_intelligence_etls.personalisation_prod_meta_table_v2\n", "group by 1,2,3,4\n", "\"\"\"\n", ")\n", "df_low_repeat_final.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c0f708bb-2a61-41fa-9163-af838e202f18", "metadata": {}, "outputs": [], "source": ["df_low_repeat_final.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "ff1296ef-6d05-48f1-9e52-2640719228c9", "metadata": {}, "outputs": [], "source": ["df_low_repeat_final.query(\"is_low_repeat_ptype == 1\").sample(10)"]}, {"cell_type": "code", "execution_count": null, "id": "0b493d8b-5aff-49ea-b734-ffd800b55aac", "metadata": {}, "outputs": [], "source": ["prod_info = prod_info.merge(\n", "    df_low_repeat_final[[\"product_type\", \"is_low_repeat_ptype\"]].drop_duplicates(),\n", "    on=[\"product_type\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a9be4497-4178-49ae-8206-e63cf107229d", "metadata": {}, "outputs": [], "source": ["prod_info = prod_info.merge(\n", "    df_low_repeat_final[[\"product_type_final\", \"is_low_repeat_master_ptype\"]].drop_duplicates(),\n", "    on=[\"product_type_final\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ad95ef07-d724-42ff-8138-72399a3b2015", "metadata": {}, "outputs": [], "source": ["prod_info[\"is_low_repeat_ptype\"] = prod_info[\"is_low_repeat_ptype\"].fillna(0)\n", "prod_info[\"is_low_repeat_master_ptype\"] = prod_info[\"is_low_repeat_master_ptype\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "7806d89d-2555-421c-8b31-a3ccc53ee812", "metadata": {}, "outputs": [], "source": ["prod_info.query(\"product_type_final == 'Milk Cake'\")"]}, {"cell_type": "markdown", "id": "0e1d7fb3-821d-4d4f-9876-05d742a8e19e", "metadata": {"tags": []}, "source": ["## Espina db data push"]}, {"cell_type": "code", "execution_count": null, "id": "2ff08020-4c98-441b-afb4-0ee8d7fe32fb", "metadata": {}, "outputs": [], "source": ["import asyncio\n", "import concurrent.futures\n", "import datetime as dt\n", "import os\n", "import threading\n", "import time\n", "from contextlib import contextmanager\n", "from datetime import datetime\n", "\n", "import boto3\n", "import numpy as np\n", "import pandas as pd\n", "\n", "# import pencilbox as pb\n", "import pytz\n", "import sqlalchemy as sqla\n", "from sqlalchemy.dialects import postgresql\n", "from sqlalchemy.ext.automap import automap_base\n", "from sqlalchemy.orm import sessionmaker\n", "import db_utils"]}, {"cell_type": "code", "execution_count": null, "id": "94abb2b6-f061-412d-aeb6-0740c341d30a", "metadata": {}, "outputs": [], "source": ["def get_espina_db_connection():\n", "    dse_db_conn_params = pb.get_secret(\"dse/postgres/dse_db/airflow_etl_user\")\n", "    engine_url = dse_db_conn_params[\"uri\"]\n", "    # engine_url = dse_db_conn_params[\"uri_new\"]\n", "    engine = sqla.create_engine(engine_url, pool_size=1, echo=False)\n", "    return engine"]}, {"cell_type": "code", "execution_count": null, "id": "732b8254-9906-4034-aa81-7776849d6d8d", "metadata": {}, "outputs": [], "source": ["espina_db_connection = get_espina_db_connection()"]}, {"cell_type": "code", "execution_count": null, "id": "d0e79b07-ab7a-48f8-990e-4363a7fe58f7", "metadata": {}, "outputs": [], "source": ["sqla.__version__"]}, {"cell_type": "code", "execution_count": null, "id": "52e9f486-b6c4-40b5-97e0-aeceeb74caf2", "metadata": {}, "outputs": [], "source": ["table_name_edb = \"prod_group_data_v3\"\n", "staging_table_name_edb = \"prod_group_data_v3_staging\"\n", "index_name = \"idx\"\n", "index_columns = [\"product_type\"]"]}, {"cell_type": "code", "execution_count": null, "id": "6c9c4279-af7a-4716-96e5-098259d72a58", "metadata": {}, "outputs": [], "source": ["prod_info.head()"]}, {"cell_type": "code", "execution_count": null, "id": "22dc8abc-896e-413b-bcd5-cf3e7ca6068b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "21059478-befd-445c-928d-57bb472d8d31", "metadata": {}, "outputs": [], "source": ["bistro_prod_info = bistro_prod_info.reset_index(drop=True)\n", "bistro_prod_info[\"is_low_repeat_ptype\"] = 0.0\n", "bistro_prod_info[\"is_low_repeat_master_ptype\"] = 0.0\n", "bistro_prod_info[\"product_type_final\"] = bistro_prod_info[\"product_type\"]\n", "bistro_prod_info[\"product_type_id_final\"] = bistro_prod_info[\"product_type_id\"]\n", "bistro_prod_info = bistro_prod_info[prod_info.columns].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "1a2c84ed-c5df-414c-8fb6-4cbd3433ab6a", "metadata": {}, "outputs": [], "source": ["bistro_prod_info.shape"]}, {"cell_type": "code", "execution_count": null, "id": "83638cc9-e159-4b81-8e88-ccb405e5be9e", "metadata": {}, "outputs": [], "source": ["bistro_prod_info.head()"]}, {"cell_type": "code", "execution_count": null, "id": "de39f0d4-622c-4ac1-b294-9b851d1c1246", "metadata": {}, "outputs": [], "source": ["prod_info.shape, bistro_prod_info.shape, prod_info.shape[0] + bistro_prod_info.shape[0]"]}, {"cell_type": "code", "execution_count": null, "id": "a12d8d43-288e-431d-803c-bc5362b6de6b", "metadata": {}, "outputs": [], "source": ["pd.concat([prod_info, bistro_prod_info]).drop_duplicates(\"product_id\").shape"]}, {"cell_type": "code", "execution_count": null, "id": "1a97f216-f781-4918-85a4-6114d4f790a8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d95e00b3-0c44-461c-85ce-f511c519b8aa", "metadata": {}, "outputs": [], "source": ["prod_info = pd.concat([prod_info, bistro_prod_info[prod_info.columns]]).reset_index(\n", "    drop=True\n", ")  # including bistro data for meta table"]}, {"cell_type": "code", "execution_count": null, "id": "43862f4e-0bb3-42fd-8e70-2cecabafb1d2", "metadata": {}, "outputs": [], "source": ["prod_info.shape"]}, {"cell_type": "code", "execution_count": null, "id": "8ca325c7-deb0-4d60-8702-8a0d9f52bfc9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9c860559-cc12-440a-925b-a34e660f2101", "metadata": {}, "outputs": [], "source": ["data_to_write = (\n", "    prod_info[\n", "        [\n", "            \"product_id\",\n", "            \"product_type_id_final\",\n", "            \"product_type_final\",\n", "            \"is_low_repeat_master_ptype\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .rename(\n", "        columns={\n", "            \"product_type_id_final\": \"product_type_id\",\n", "            \"product_type_final\": \"product_type\",\n", "            \"is_low_repeat_master_ptype\": \"is_low_repeat_ptype\",\n", "        }\n", "    )\n", ")\n", "data_to_write[\"product_type\"] = data_to_write[\"product_type\"].str.replace(\n", "    \"'\", \"''\"\n", ")  # cases like Men's deodrant : escape single quote"]}, {"cell_type": "code", "execution_count": null, "id": "171df273-9740-4649-8a65-02403737d68d", "metadata": {}, "outputs": [], "source": ["data_to_write.shape, data_to_write.product_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "47a5dd9a-3e44-4e85-aff2-c4e1bead8d57", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "68e99303-dd28-4368-a10d-4d49bd76f11e", "metadata": {"tags": []}, "outputs": [], "source": ["data_to_write[\"product_id\"] = data_to_write[\"product_id\"].astype(\"int\")\n", "data_to_write[\"product_type_id\"] = data_to_write[\"product_type_id\"].astype(\"int\")\n", "data_to_write[\"is_low_repeat_ptype\"] = data_to_write[\"is_low_repeat_ptype\"].astype(\"int\")"]}, {"cell_type": "code", "execution_count": null, "id": "ec4698bd-ebcb-4a7b-9696-1b52b9ca6d8e", "metadata": {}, "outputs": [], "source": ["data_to_write.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "1ac9349a-f7b6-429a-b6e9-37bfb66e534b", "metadata": {}, "outputs": [], "source": ["data_to_write"]}, {"cell_type": "code", "execution_count": null, "id": "f88668c1-f207-4ec9-b02c-88b9caef8300", "metadata": {}, "outputs": [], "source": ["# data_to_write[\"is_low_repeat_ptype\"] = 0  # for NPR Ab testing"]}, {"cell_type": "code", "execution_count": null, "id": "0b3aef5b-cc2f-4706-bdc5-ebfef90ea115", "metadata": {}, "outputs": [], "source": ["data_to_write.is_low_repeat_ptype.unique()"]}, {"cell_type": "code", "execution_count": null, "id": "2827608f-aa1b-4662-bb18-fc615e3058c9", "metadata": {}, "outputs": [], "source": ["data_to_write.query(\"product_id in (105,106)\")"]}, {"cell_type": "code", "execution_count": null, "id": "da2dbc24-bb6f-4dfe-af19-e138a995d2ec", "metadata": {}, "outputs": [], "source": ["data_to_write.merge(bistro_prod_info, on=\"product_id\").product_type_id_y.max()"]}, {"cell_type": "code", "execution_count": null, "id": "ea7ddcbb-2d05-49ed-ac3e-a6ee6a304833", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5e8c731f-2ea8-48ba-9329-a7b4d6c40d4a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "bd703e3a-997b-4b13-9d62-c474757b260a", "metadata": {}, "outputs": [], "source": ["TABLE_CREATION_QUERY = f\"\"\"\n", "CREATE TABLE IF NOT EXISTS {staging_table_name_edb}\n", "(\n", "    product_id bigint,\n", "    product_type_id bigint,\n", "    product_type varchar,\n", "    is_low_repeat_ptype int,\n", "    CONSTRAINT {staging_table_name_edb}_pk PRIMARY KEY (product_id)\n", ");\n", "\n", "CREATE TABLE IF NOT EXISTS {table_name_edb}\n", "(\n", "    product_id bigint,\n", "    product_type_id bigint,\n", "    product_type varchar,\n", "    is_low_repeat_ptype int,\n", "    CONSTRAINT {table_name_edb}_pk PRIMARY KEY (product_id)\n", "    \n", ");\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "da59b861-04ba-4dde-9bbe-9c0446b931ac", "metadata": {}, "outputs": [], "source": ["print(TABLE_CREATION_QUERY)\n", "with espina_db_connection.begin() as connection:\n", "    connection.execute(TABLE_CREATION_QUERY)"]}, {"cell_type": "code", "execution_count": null, "id": "697f6b0a-88df-473f-9631-cd3e8c2a34b5", "metadata": {}, "outputs": [], "source": ["data_to_write.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "a78686af-14b4-4f6b-852c-972cfd709292", "metadata": {}, "outputs": [], "source": ["data_to_write.shape"]}, {"cell_type": "code", "execution_count": null, "id": "e7158d07-7b4c-45e7-ab25-c0108dc654c7", "metadata": {}, "outputs": [], "source": ["data_to_write.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "0b3aa4bd-6f5b-4819-abbf-d6ca189264c1", "metadata": {}, "outputs": [], "source": ["data_to_write.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "33fde8a2-292c-472d-b25b-535461c145fa", "metadata": {}, "outputs": [], "source": ["data_to_write.head()"]}, {"cell_type": "code", "execution_count": null, "id": "83491267-db78-4b85-bf7f-c8e4c9b383a1", "metadata": {}, "outputs": [], "source": ["staging_table_name_edb, table_name_edb"]}, {"cell_type": "code", "execution_count": null, "id": "24145507-f253-4fc3-82d5-e3362f179d31", "metadata": {}, "outputs": [], "source": ["db_utils.push_to_database(\n", "    table_name_edb=table_name_edb,\n", "    staging_table_name_edb=staging_table_name_edb,\n", "    dataframe=data_to_write,\n", "    index_name=index_name,\n", "    index_columns=index_columns,\n", "    dry_run=False,\n", ")\n", "# alerting TODO"]}, {"cell_type": "markdown", "id": "d10fe136-98e5-4f7f-ac23-bb0ab4fec72a", "metadata": {}, "source": ["### Datadog "]}, {"cell_type": "code", "execution_count": null, "id": "530fb0d8-9280-4a98-8508-904066ed451a", "metadata": {}, "outputs": [], "source": ["dag_id = task_instance.split(\"__\")[0]\n", "task_id = task_instance.split(\"__\")[1]"]}, {"cell_type": "code", "execution_count": null, "id": "e3ef8bbf-9ed7-4216-a1ca-d851e2026786", "metadata": {}, "outputs": [], "source": ["!pip install -q datadog\n", "import enum\n", "import threading\n", "import pencilbox as pb\n", "from datadog import initialize, api\n", "\n", "\n", "class EventAlertType(enum.Enum):\n", "    Info = \"info\"\n", "    Warning = \"warning\"\n", "    Error = \"error\"\n", "    Success = \"success\"\n", "\n", "\n", "class EventPriority(enum.Enum):\n", "    Low = \"low\"\n", "    Normal = \"normal\"\n", "\n", "\n", "class DatadogPublishEvent:\n", "    _instance = None\n", "    _lock = threading.Lock()  # Class-level lock for thread safety\n", "\n", "    def __new__(cls, *args, **kwargs):\n", "        \"\"\"Ensure only one instance of the class is created (<PERSON><PERSON>).\"\"\"\n", "        if cls._instance is None:\n", "            with cls._lock:  # Ensure only one thread can create the instance\n", "                if cls._instance is None:  # Double-checked locking pattern\n", "                    cls._instance = super(DatadogPublishEvent, cls).__new__(cls)\n", "        return cls._instance\n", "\n", "    def __init__(self):\n", "        \"\"\"Initialize Datadog only once.\"\"\"\n", "        if not hasattr(self, \"initialized\"):\n", "            try:\n", "                self.options = {\n", "                    \"api_key\": pb.get_secret(\"dse/personalisation/datadog\")[\"api_key\"],\n", "                }\n", "                initialize(**self.options)\n", "                self.initialized = True  # Mark as initialized to avoid re-initialization\n", "            except Exception as e:\n", "                print(f\"Failed to initialize DatadogPublishEvent, error: {str(e)}\")\n", "\n", "    @staticmethod\n", "    def publish_event(\n", "        dag_id: str,\n", "        task_id: str,\n", "        run_id: str,\n", "        task_status,\n", "        message: str,\n", "        tags: list = None,\n", "        priority: EventPriority = EventPriority.Normal,\n", "        alert_type: EventAlertType = EventAlertType.Info,\n", "    ):\n", "        try:\n", "            title = f\"AIRFLOW_{dag_id}_{task_id}_{task_status}\"\n", "            aggregation_key = f\"{dag_id}_{run_id}\"\n", "            if not tags or len(tags) == 0:\n", "                tags = [\"environment:production\", \"source:airflow\", f\"dag_id: {dag_id}\"]\n", "\n", "            res = api.Event.create(\n", "                title=title,\n", "                text=message,\n", "                aggregation_key=aggregation_key,\n", "                priority=priority.value,\n", "                alert_type=alert_type.value,\n", "                service=dag_id,\n", "                tags=tags,\n", "            )\n", "            print(res)\n", "        except Exception as e:\n", "            print(\n", "                f\"Failed to publish datadog event, dag_id: {dag_id}, task_id: {task_id},run_id: {run_id}, task_status: {task_status} \"\n", "                f\"error: {str(e)}\"\n", "            )"]}, {"cell_type": "code", "execution_count": null, "id": "cc5c9570-3a76-4b18-8944-16fb7d29bd82", "metadata": {}, "outputs": [], "source": ["DatadogPublishEvent().publish_event(\n", "    dag_id=dag_id,\n", "    task_id=task_id,\n", "    run_id=run_id,\n", "    task_status=\"SUCCESS\",\n", "    message=\"..... dag ran successfully\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "45615278-51a5-419c-90ee-89345340161f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}