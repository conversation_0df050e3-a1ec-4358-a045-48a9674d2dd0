import pandas as pd
import pencilbox as pb
import numpy as np
import ast
import queries
import re
import gc
import time
from datetime import datetime, timedelta
from typing import List, Tuple

s3_bucket = "prod-dse-projects"


def run_query_with_retries(query, con=pb.get_connection("[Warehouse] Trino"), retries=3):
    count = 1
    while count <= retries:
        try:
            df = pd.read_sql_query(query, con)
            break
        except Exception as e:
            print(e)
            count += 1
    return df


def read_sheets_with_retries(sheet_id, sheet_name, max_tries=3):
    for attempt in range(max_tries):
        print(f"Trying to read attempt: {attempt + 1}...")
        try:
            return pb.from_sheets(sheet_id, sheet_name)
        except Exception as e:
            print(f"Error occurred: {str(e)}")
            if hasattr(e, "response") and hasattr(e.response, "json"):
                try:
                    exception_code = (
                        e.response.json().get("error", {}).get("code", "Unknown error code")
                    )
                    print(f"API Error Code: {exception_code}")
                except Exception as inner_e:
                    print(f"Failed to extract error code: {str(inner_e)}")
            time.sleep(60)
            

def get_city_id_name_data():
    return run_query_with_retries(queries.get_active_city_query())


def get_active_pids():
    active_pids_df = run_query_with_retries(queries.get_active_pids_query())
    return active_pids_df


def get_city_pid_cart_count():
    return run_query_with_retries(queries.get_city_pid_cart_count_query())


def get_pid_cart_count():
    return run_query_with_retries(queries.get_pid_cart_count_query())


def get_affluent_pids():
    return run_query_with_retries(queries.get_affluent_pid_query())


def get_imported_pids():
    return run_query_with_retries(queries.get_imported_pid_query())


def get_product_group_id_data():
    data = run_query_with_retries(queries.get_product_group_id_query())
    data = data.groupby(["product_id"]).head(1).reset_index(drop=True)
    return data


def get_city_books_l2_popularity_data():
    data = run_query_with_retries(queries.get_city_books_l2_popularity_query())
    return data


def get_books_l2_popularity_data():
    data = run_query_with_retries(queries.get_books_l2_popularity_query())
    return data


def get_combo_data():
    data = run_query_with_retries(queries.get_combo_query())
    return data


def get_group_city_mapping(from_sheet=True): # region wise city mapping
    if from_sheet:
        group_city_mapping = read_sheets_with_retries("1YDoHJrzM9QJgfRcJkUuNOKO0gyEjVj52YsOp2KOcPhA", "Sheet1")
    else:
        group_city_mapping = pd.read_csv("city_region_mapping - Sheet1.csv")
        
    group_city_mapping["city_id"] = group_city_mapping["city_id"].astype("int")
    
    return group_city_mapping


def get_festival_df(from_sheet=True):
    if from_sheet:
        festival_df = read_sheets_with_retries("1sxAcHxNyTdNozzl8wFC4PtdO56xFce0Nkpc0blATFpo", "Festival Dates")
    else:
        festival_df = pd.read_csv("Festive_prod_v4 - Festival Dates.csv")
    
    date = pd.to_datetime(datetime.now() + timedelta(days=1)).date()
    print("current_date: ", date)
        
    festival_df["start"] = pd.to_datetime(festival_df["start"])
    festival_df["end"] = pd.to_datetime(festival_df["end"])
    festival_df["curr_festival"] = festival_df.apply(lambda row: 1 if date >= row["start"] and date <= row["end"] else 0, axis=1)
    
    gifting_df = festival_df.query("festival == 'Gifting'").reset_index(drop=True)
    fnv_df = festival_df.query("festival == 'FnV'").reset_index(drop=True)
    festival_df = festival_df.query("festival != 'Gifting' and festival != 'FnV'").reset_index(drop=True)
    
    return gifting_df, festival_df, fnv_df


def get_raw_arm_model_results(group):
    try:
        group_rules_local_path = "/tmp/rules_incremental_group_id_" + str(group) + ".csv"
        pb.from_s3(
            s3_bucket,
            "Abhinav_Reddy/byc_arm/incremental_rules/city_predictions/rules_incremental_group_id_"
            + str(group)
            + ".csv",
            group_rules_local_path,
        )
        group_results = pd.read_csv(group_rules_local_path)
        # print(group_results.shape)
        if group_results.shape[0] == 0:
            print(
                f"No predictions found for group id:: {group}. Skipping post-processing process"
            )
            return pd.DataFrame(columns=["antecedents", "consequents", "score"])
    except:
        print(
            f"No predictions found for group id:: {group}. Skipping post-processing process"
        )
        null_city.append(city)
        return pd.DataFrame(columns=["antecedents", "consequents", "score"])
    # group_results = pd.read_csv('sample_part1_output_new_v3.csv')
    group_results = group_results[group_results["group"] == group]
    group_results = group_results[
        [
            "antecedents",
            "consequents",
            "support",
            "confidence",
            "lift",
            "conviction",
        ]
    ]
    return group_results


def get_blacklist_data(widget, from_sheet=True):
    
    if from_sheet:
        blacklist_df = read_sheets_with_retries("17Q4P7HGhYLMnW4AqRWL4Jh_a5nT5DCeuK176CM2jgoE", "Blacklist_v2")
    else:
        blacklist_df = pd.read_csv("BLACKLIST - Blacklist_v2.csv")
    
    blacklist_df = blacklist_df.query("widget in ('Global', @widget)")
    
    exclusion_all_l0 = list(set(ast.literal_eval(",".join(list(blacklist_df.query("level == 'all'").l0.values)) + ",")))
    exclusion_all_l1 = list(set(ast.literal_eval(",".join(list(blacklist_df.query("level == 'all'").l1.values)) + ",")))
    exclusion_all_ptype = list(set(ast.literal_eval(",".join(list(blacklist_df.query("level == 'all'").ptype.values)) + ",")))
    exclusion_all_pid = list(set(ast.literal_eval(",".join(list(blacklist_df.query("level == 'all'").pid.values)) + ",")))

    exclusion_antecedent_l0 = list(set(ast.literal_eval(",".join(list(blacklist_df.query("level == 'antecedent'").l0.values)) + ",")))
    exclusion_antecedent_l1 = list(set(ast.literal_eval(",".join(list(blacklist_df.query("level == 'antecedent'").l1.values)) + ",")))
    exclusion_antecedent_ptype = list(set(ast.literal_eval(",".join(list(blacklist_df.query("level == 'antecedent'").ptype.values))+ ",")))
    exclusion_antecedent_pid = list(set(ast.literal_eval(",".join(list(blacklist_df.query("level == 'antecedent'").pid.values)) + ",")))

    exclusion_consequent_l0 = list(set(ast.literal_eval(",".join(list(blacklist_df.query("level == 'consequent'").l0.values)) + ",")))
    exclusion_consequent_l1 = list(set(ast.literal_eval(",".join(list(blacklist_df.query("level == 'consequent'").l1.values)) + ",")))
    exclusion_consequent_ptype = list(set(ast.literal_eval(",".join(list(blacklist_df.query("level == 'consequent'").ptype.values))+ ",")))
    exclusion_consequent_pid = list(set(ast.literal_eval(",".join(list(blacklist_df.query("level == 'consequent'").pid.values)) + ",")))
    
    return exclusion_all_l0, exclusion_all_l1, exclusion_all_ptype, exclusion_all_pid, exclusion_antecedent_l0, exclusion_antecedent_l1, exclusion_antecedent_ptype, exclusion_antecedent_pid, exclusion_consequent_l0,  exclusion_consequent_l1, exclusion_consequent_ptype, exclusion_consequent_pid


def get_master_ptype_data(from_sheet=True):
    if from_sheet:
        ptype_mappings = read_sheets_with_retries("16b-AbubMta4MjxsrzFkDK_JyIrdRQznobTwrKNwGjck", "Master")
    else:
        ptype_mappings = pd.read_csv("Master Ptype_ Prod - Master.csv")
        
    ptype_mappings = ptype_mappings.melt(id_vars=["Group Name","master_ptype_id"], value_vars=ptype_mappings.drop(columns=["Group Name","master_ptype_id"]).columns.tolist(),).reset_index(drop=True)
    ptype_mappings.drop(columns=["variable"], inplace=True)
    
    ptype_mappings = ptype_mappings.drop_duplicates()
    ptype_mappings = ptype_mappings.dropna(subset=["value"])
    ptype_mappings = ptype_mappings[ptype_mappings["value"] != ""]
    
    ptype_mappings["product_type"] = ptype_mappings["value"].apply(lambda x: x.split("||")[0].strip())
    ptype_mappings["product_type_id"] = ptype_mappings["value"].apply(lambda x: int(x.split("||")[1].strip()))
    
    ptype_mappings.drop(columns=["value"], inplace=True)
    
    # ptype_mappings["master_ptype_id"] = 1
    # ptype_mappings["master_ptype_id"] = ptype_mappings["master_ptype_id"].cumsum()
    # ptype_mappings["master_ptype_id"] = 170400 + ptype_mappings["master_ptype_id"]
    
    return ptype_mappings


def get_complimentary_data(festival_df, gifting_df, fnv_df, festive=True, gifting=True, fnv=True, from_sheet=True):
    
    if festive:
        try:
            festival = festival_df.query("curr_festival == 1")['festival'].values[0]
        except:
            print('No festival')
            return pd.DataFrame()
        if from_sheet:
            ptype_data = read_sheets_with_retries("1sxAcHxNyTdNozzl8wFC4PtdO56xFce0Nkpc0blATFpo", festival)
        else:
            ptype_data = pd.read_csv("Festive_prod_v4 - "+festival+".csv")
            
    elif gifting:
        try:
            gift = gifting_df.query("curr_festival == 1")['festival'].values[0]
        except:
            print('Gifting not active')
            return pd.DataFrame()
        if from_sheet:
            ptype_data = read_sheets_with_retries("1sxAcHxNyTdNozzl8wFC4PtdO56xFce0Nkpc0blATFpo", gift)
        else:
            ptype_data = pd.read_csv("Festive_prod_v4 - "+gift+".csv")
            
    elif fnv:
        try:
            fn = fnv_df.query("curr_festival == 1")['festival'].values[0]
        except:
            print('Fnv not active')
            return pd.DataFrame()
        if from_sheet:
            ptype_data = read_sheets_with_retries("1sxAcHxNyTdNozzl8wFC4PtdO56xFce0Nkpc0blATFpo", fn)
        else:
            ptype_data = pd.read_csv("Festive_prod_v4 - "+fn+".csv")
            
    else:
        if from_sheet:
            ptype_data = read_sheets_with_retries("1-TAGan8KKBxBuR_lkDXZ6xxAzRqAoPpj2uCNnc_5Efw", "prod_v5")
        else:
            ptype_data = pd.read_csv("Complementary Ptypes_ Prod - prod_v5.csv")
        
    ptype_data["product_type"] = ptype_data["product_type"].apply(lambda x: x.split("||")[0])
    
    ptype_data = ptype_data.melt(id_vars="product_type",value_vars=ptype_data.drop(columns=["product_type"]).columns.tolist(),).reset_index(drop=True)
    
    ptype_data.dropna(subset=["value"], inplace=True)
    ptype_data = ptype_data[ptype_data["value"] != ""]
    
    ptype_data["cons_ptype_rank"] = ptype_data.groupby(["product_type"]).cumcount()
    
    ptype_data.sort_values(["product_type", "cons_ptype_rank"], inplace=True)
    ptype_data.reset_index(drop=True, inplace=True)
    ptype_data.rename(columns={"product_type": "ptype"}, inplace=True)
    ptype_data = ptype_data.drop_duplicates(subset=["ptype", "value"])

    ptype_data.sort_values(["ptype", "cons_ptype_rank"], inplace=True)
    ptype_data.reset_index(drop=True, inplace=True)
    ptype_data["value"] = ptype_data["value"].apply(lambda x: x.split("||")[0])
    
    return ptype_data


def get_low_repeat_ptype_data(prod_info, from_sheet=True):
    if from_sheet:
        df_low_repeat_sheet = read_sheets_with_retries("1-TAGan8KKBxBuR_lkDXZ6xxAzRqAoPpj2uCNnc_5Efw", "ptype_intelligence")
    else:
        df_low_repeat_sheet = pd.read_csv("Complementary Ptypes_ Prod - ptype_intelligence.csv")
    
    df_low_repeat = run_query_with_retries(queries.get_low_repeatable_ptype_query())
    df_low_repeat["is_low_repeat_ptype_algo"] = 1
    
    df_low_repeat_sheet["is_low_repeat_ptype"] = df_low_repeat_sheet.is_low_repeat_ptype.astype("int")
    df_low_repeat_sheet.rename(columns={"is_low_repeat_ptype": "is_low_repeat_ptype_sheet"}, inplace=True)
    
    df_low_repeat = prod_info[["product_type"]].drop_duplicates().merge(df_low_repeat, on="product_type", how="left")
    
    df_low_repeat.loc[df_low_repeat.is_low_repeat_ptype_algo.isna(), "is_low_repeat_ptype_algo"] = 0
    df_low_repeat_final = df_low_repeat.merge(df_low_repeat_sheet, on="product_type", how="left")
    
    df_low_repeat_final["is_low_repeat_ptype"] = df_low_repeat_final["is_low_repeat_ptype_sheet"]
    df_low_repeat_final.loc[df_low_repeat_final.is_low_repeat_ptype.isna(), "is_low_repeat_ptype"] = df_low_repeat_final.loc[df_low_repeat_final.is_low_repeat_ptype.isna(), "is_low_repeat_ptype_algo"]
    df_low_repeat_final = df_low_repeat_final[["product_type", "is_low_repeat_ptype"]]
    
    return df_low_repeat_final


def gender_flag(cat,prod_info_data):
    df_1 = prod_info_data[[cat]].drop_duplicates()
    # Keywords
    men_keywords = r"\b(men's|male|mens|men)\b"
    women_keywords = r"\b(women's|female|womens|women)\b"
    kids_keywords = r"\b(kid's|kids|kid|children's|children|childrens)\b"
    
    # Vectorized flag assignment using regex matching
    df_1['gender_cat_flag'] = np.select(
        [
            df_1[cat].str.contains(men_keywords, case=False, na=False, regex=True),
            df_1[cat].str.contains(women_keywords, case=False, na=False, regex=True),
            df_1[cat].str.contains(kids_keywords, case=False, na=False, regex=True),
        ],
        ['M', 'W', 'K'],
        default='others'
    )
    return df_1[[cat,'gender_cat_flag']]


def embedding_to_reco_list_function (df_cleora_emb, req_cleora_pids_df):
    '''
    This function takes in cleora embeddings 2d array as input and returns dataframe with antecedent and conseuqent pIds.
    '''
    
    df_cleora_emb_exp = pd.DataFrame(df_cleora_emb['list_Embedding_array'].apply(pd.Series))
    item_embeddings_all = df_cleora_emb_exp.values
    
    df_cleora_emb_req = pd.DataFrame(df_cleora_emb.merge(req_cleora_pids_df, on=['product_id'])['list_Embedding_array'].apply(pd.Series))
    item_embeddings_req = df_cleora_emb_req.values
    
    cleora_embedding_ant_reco = item_embeddings_req.dot(item_embeddings_all.T)
    
    cleora_embedding_ant_reco = np.argsort(-cleora_embedding_ant_reco, axis=1)
    cleora_embedding_ant_reco_final = np.array(df_cleora_emb['product_id'].unique())[cleora_embedding_ant_reco]
    
    df_cleora_emb_exp = []
    df_cleora_emb_req = []
    item_embeddings = []
    cleora_embedding_ant_reco = []
    del df_cleora_emb_exp, item_embeddings, cleora_embedding_ant_reco, df_cleora_emb_req
    
    df_dataframe = pd.DataFrame({
        'antecedents': [sublist[0] for sublist in cleora_embedding_ant_reco_final],  # Extract the first element
        'consequents': [sublist[1:200] for sublist in cleora_embedding_ant_reco_final]  # Extract the remaining elements
    })
    
    return df_dataframe


def string_to_list(column):
    '''
    This function converts list of string items to usable list format.
    '''
    return list(map(float, column.split(',')))


def get_cleora_embeddings(prod_info, req_cleora_pids_df, region='North'):  
            
    cleora_embedding = run_query_with_retries(f"""select * from consumer_intelligence_etls.daily_cleora_embedding_product_level where region_type = '{region}' """)

    # Apply the function to the column and create a new column with lists
    cleora_embedding["list_Embedding_array"] = cleora_embedding["embedding_array"].apply(
        string_to_list
    )

    ## Merging with prod meta table to get the L0_category and l0_category_id of the Antecedent Pids

    cleora_embedding = pd.merge(
        cleora_embedding,
        prod_info[["product_id", "l0_category", "l0_category_id"]],
        left_on=["product_id"],
        right_on=["product_id"],
        how="inner",
    )
    
    cleora_embedding = (
        cleora_embedding.drop_duplicates(
            subset=["product_id", "region_type"], keep="first"
        )
    )
    
    cleora_embedding = (
        cleora_embedding.reset_index(drop=True)
    )
    
    ## Converting the embedding to raw recommendation list
    df_dataframe_reco_set= embedding_to_reco_list_function(cleora_embedding, req_cleora_pids_df)
    
    cleora_embedding=[]
    del cleora_embedding
    gc.collect()
    
    return df_dataframe_reco_set


def get_redshift_table_kwargs(table_name, table="main"):
    if table == "main":
        date_col_dtype = "DATE"
    else:
        date_col_dtype = "VARCHAR"
    return {
        "schema_name": "consumer_intelligence_etls",
        "table_name": table_name,
        "column_dtypes": [
            {
                "name": "antecedent_product_type",
                "type": "VARCHAR",
                "description": "antecedent product type",
            },
            {
                "name": "consequent_product_type",
                "type": "VARCHAR",
                "description": "consequent product type",
            },
            {
                "name": "rank",
                "type": "INTEGER",
                "description": "consequent product type rank",
            },
            {"name": "product_id", "type": "INTEGER", "description": "product id"},
            {
                "name": "consequent_pid",
                "type": "INTEGER",
                "description": "recommended product id",
            },
            {
                "name": "scores",
                "type": "REAL",
                "description": "recommended product scores",
            },
            {
                "name": "source",
                "type": "INTEGER",
                "description": "source for scores - cleora,ARM,popularity",
            },
            {
                "name": "antecedent_product_name",
                "type": "VARCHAR",
                "description": "antecedent product name",
            },
            {
                "name": "antecedent_product_type_id",
                "type": "INTEGER",
                "description": "antecedent product type id",
            },
            {
                "name": "consequent_product_name",
                "type": "VARCHAR",
                "description": "consequent product name",
            },
            {
                "name": "consequent_product_type_id",
                "type": "INTEGER",
                "description": "consequent product type id",
            },
            {"name": "city_id", "type": "INTEGER", "description": "city id"},
            {"name": "city_name", "type": "VARCHAR", "description": "city name"},
            {
                "name": "final_rank",
                "type": "INTEGER",
                "description": "rank of products",
            },
            {
                "name": "consequent_product_rank",
                "type": "INTEGER",
                "description": "consequent product rank",
            },
            # {"name": "cig_flag", "type": "int", "description": "antecedent tobacco pid flag"},
            {
                "name": "scores_rank",
                "type": "INTEGER",
                "description": "rank for scores column",
            },
            {"name": "ptype_rank", "type": "INTEGER", "description": "ptype spacing"},
            {
                "name": "final_scores_rank",
                "type": "INTEGER",
                "description": "final rank of products",
            },
            {
                "name": "updated_at",
                "type": date_col_dtype,
                "description": "update date of table rebuild",
            },
        ],
        "sortkey": ["city_id", "product_id"],
        "partition_key": ["city_id"],
        "load_type": "partition_overwrite",  # append, overwrite, truncate or upsert,
        "table_description": "Next product recommendations",
    }


def assign_gender_categories(data, prod_info_gender_l2, prod_info_gender_l1, prod_info_gender_pid):
    data = data.merge(
        prod_info_gender_l2.rename(
            columns={
                "l2_category": "antecedent_l2_category",
                "gender_l2_flag": "gender_ant_l2_flag",
            }
        ),
        how="left",
    )
    data = data.merge(
        prod_info_gender_l2.rename(
            columns={
                "l2_category": "consequent_l2_category",
                "gender_l2_flag": "gender_cons_l2_flag",
            }
        ),
        how="left",
    )

    data = data.merge(
        prod_info_gender_l1.rename(
            columns={
                "l1_category": "antecedent_l1_category",
                "gender_l1_flag": "gender_ant_l1_flag",
            }
        ),
        how="left",
    )
    data = data.merge(
        prod_info_gender_l1.rename(
            columns={
                "l1_category": "consequent_l1_category",
                "gender_l1_flag": "gender_cons_l1_flag",
            }
        ),
        how="left",
    )

    data = data.merge(
        prod_info_gender_pid.rename(columns={"gender_pid_flag": "gender_ant_pid_flag"}),
        how="left",
        on=["product_id"],
    )
    data = data.merge(
        prod_info_gender_pid.rename(
            columns={"product_id": "consequent_pid", "gender_pid_flag": "gender_cons_pid_flag"}
        ),
        how="left",
        on=["consequent_pid"],
    )

    data["gender_ant_cat"] = np.where(
        (data["gender_ant_l2_flag"] == "M")
        | (data["gender_ant_l1_flag"] == "M")
        | (data["gender_ant_pid_flag"] == "M"),
        "M",
        "Others",
    )
    data["gender_ant_cat"] = np.where(
        (data["gender_ant_l2_flag"] == "W")
        | (data["gender_ant_l1_flag"] == "W")
        | (data["gender_ant_pid_flag"] == "W"),
        "W",
        data["gender_ant_cat"],
    )
    data["gender_ant_cat"] = np.where(
        (data["gender_ant_l2_flag"] == "K")
        | (data["gender_ant_l1_flag"] == "K")
        | (data["gender_ant_pid_flag"] == "K"),
        "K",
        data["gender_ant_cat"],
    )

    data["gender_cons_cat"] = np.where(
        (data["gender_cons_l2_flag"] == "M")
        | (data["gender_cons_l1_flag"] == "M")
        | (data["gender_cons_pid_flag"] == "M"),
        "M",
        "Others",
    )
    data["gender_cons_cat"] = np.where(
        (data["gender_cons_l2_flag"] == "W")
        | (data["gender_cons_l1_flag"] == "W")
        | (data["gender_cons_pid_flag"] == "W"),
        "W",
        data["gender_cons_cat"],
    )
    data["gender_cons_cat"] = np.where(
        (data["gender_cons_l2_flag"] == "K")
        | (data["gender_cons_l1_flag"] == "K")
        | (data["gender_cons_pid_flag"] == "K"),
        "K",
        data["gender_cons_cat"],
    )

    # Convert columns to NumPy arrays for vectorized operations
    ant_flags = data["gender_ant_cat"].values
    cons_flags = data["gender_cons_cat"].values

    # Vectorized condition checks
    gender_neg_flag = np.where(
        (ant_flags == "M") & (np.isin(cons_flags, ["W", "K"])),
        1,
        np.where(
            (ant_flags == "W") & (np.isin(cons_flags, ["M", "K"])),
            1,
            np.where((ant_flags == "K") & (np.isin(cons_flags, ["M", "W"])), 1, 0),
        ),
    )

    # Add final_flag to DataFrame
    data["gender_neg_flag"] = gender_neg_flag
    
    data = data[data["gender_neg_flag"] == 0]

    data = data.drop(
        columns=[
            "gender_ant_cat",
            "gender_cons_cat",
            "gender_neg_flag",
            "gender_cons_cat",
            "gender_ant_cat",
            "gender_ant_l2_flag",
            "antecedent_l2_category",
            "consequent_l2_category",
            "gender_cons_l2_flag",
            "gender_ant_l1_flag",
            "antecedent_l1_category",
            "consequent_l1_category",
            "gender_cons_l1_flag",
            "gender_ant_pid_flag",
            "gender_cons_pid_flag",
        ]
    )
    
    return data


def handle_holi_gift_box(data):
    # Handling giftbox for holi
    try:
        holi_gift_box_pids = data.query(
            "antecedent_product_type=='Gift Box' and antecedent_l2_category=='Holi Essentials' "
        )["product_id"].unique()
    except:
        holi_gift_box_pids = []

    print(holi_gift_box_pids)
    if len(holi_gift_box_pids) > 0:
        holi_gift_box_pids_df = (
            data.query("product_id in @holi_gift_box_pids")[
                [
                    "antecedent_product_type",
                    "antecedent_product_type_id",
                    "product_id",
                    "antecedent_product_name",
                    "antecedent_l2_category",
                    "antecedent_l1_category",
                ]
            ]
            .drop_duplicates()
            .reset_index(drop=True)
        )

        data = data.query("product_id not in @holi_gift_box_pids").reset_index(drop=True)

        try:
            gulal_reco_df = data.query("product_id == 445472").reset_index(drop=True)
        except:
            try:
                gulal_reco_df = data.query("antecedent_product_type=='Gulal'")[
                    "product_id"
                ].unique()[0]
            except:
                gulal_reco_df = pd.DataFrame()

        if len(gulal_reco_df) > 0:
            final_holi_gift_box_df = pd.DataFrame()
            for i in holi_gift_box_pids:
                tmp_df = (
                    holi_gift_box_pids_df.query("product_id == @i").head(1).reset_index(drop=True)
                )
                # print(sample_df)
                gulal_reco_df_1 = gulal_reco_df.copy()
                gulal_reco_df_1["antecedent_product_type"] = tmp_df[
                    "antecedent_product_type"
                ].values[0]
                gulal_reco_df_1["antecedent_product_type_id"] = tmp_df[
                    "antecedent_product_type_id"
                ].values[0]
                gulal_reco_df_1["product_id"] = tmp_df["product_id"].values[0]
                gulal_reco_df_1["antecedent_product_name"] = tmp_df[
                    "antecedent_product_name"
                ].values[0]
                gulal_reco_df_1["antecedent_l2_category"] = tmp_df["antecedent_l2_category"].values[
                    0
                ]
                gulal_reco_df_1["antecedent_l1_category"] = tmp_df["antecedent_l1_category"].values[
                    0
                ]
                final_holi_gift_box_df = pd.concat(
                    [gulal_reco_df_1, final_holi_gift_box_df]
                ).reset_index(drop=True)
                # break
            data = pd.concat([final_holi_gift_box_df, data]).reset_index(drop=True)
    return data


def ffd_city_clustering_with_volume(
    df,
    city_col= "city_name",
    count_col= "pid_count",
    high_volume_threshold= 100000,
    max_cluster_volume=100000
):
    city_orders = df.copy()
    
    # Step 2: Split into high-volume and low-volume
    high_volume = city_orders[city_orders[count_col] > high_volume_threshold]
    low_volume = city_orders[city_orders[count_col] <= high_volume_threshold]
    
    high_volume_cities = list(zip(high_volume[city_col], high_volume[count_col]))
    
    # Step 3: Sort low-volume cities by order count (desc)
    sorted_low = low_volume.sort_values(count_col, ascending=False).reset_index(drop=True)

    clusters: List[List[str]] = []         # list of city lists
    cluster_totals: List[int] = []         # total order_count per cluster

    for _, row in sorted_low.iterrows():
        city = row[city_col]
        count = row[count_col]

        placed = False
        for i in range(len(clusters)):
            if cluster_totals[i] + count <= max_cluster_volume:
                clusters[i].append(city)
                cluster_totals[i] += count
                placed = True
                break

        if not placed:
            # Start a new cluster
            clusters.append([city])
            cluster_totals.append(count)

    # Combine cluster info as list of (cities, total order_count)
    low_volume_clusters = list(zip(clusters, cluster_totals))

    return high_volume_cities, low_volume_clusters
