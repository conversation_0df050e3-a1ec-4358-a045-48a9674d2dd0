{"cells": [{"cell_type": "markdown", "id": "a207891a-6e2d-49c5-af3f-228b1bcf9356", "metadata": {}, "source": ["# Install Libraries"]}, {"cell_type": "code", "execution_count": null, "id": "c478afde-8f53-4df9-ac5c-12b626e1b554", "metadata": {"tags": []}, "outputs": [], "source": ["!pip install -q scikit-learn==1.0.2\n", "!pip install -q numpy==1.21.5"]}, {"cell_type": "markdown", "id": "b7d1b729-b117-4eef-bd8b-14d526aa8604", "metadata": {}, "source": ["# Append current working directory and Imports"]}, {"cell_type": "code", "execution_count": null, "id": "5f867a6d-9f8f-4eff-8b14-c6a370c70738", "metadata": {}, "outputs": [], "source": ["import os, sys"]}, {"cell_type": "code", "execution_count": null, "id": "b71626a4-8dc8-4af0-b47a-5b1b5a3f226c", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "a2057e4d-8c7e-4386-a7de-ad572a41f468", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "14cb40a7-f734-40cd-9439-3062293098aa", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import gc\n", "import ast\n", "import warnings\n", "from sklearn.preprocessing import MinMaxScaler\n", "import helper\n", "import queries\n", "import json\n", "import ast\n", "import pickle\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "s3_bucket = \"prod-dse-projects\"\n", "use_sheet = True"]}, {"cell_type": "markdown", "id": "c5dd724c-c1f6-4a8c-9e17-66b7c74a5739", "metadata": {}, "source": ["# Products metadata"]}, {"cell_type": "code", "execution_count": null, "id": "c12d43b7-da6a-4af0-805f-5d9b3bc3487a", "metadata": {}, "outputs": [], "source": ["product_univ_query = f\"\"\"\n", "select distinct product_id,\n", "                product_name,\n", "                product_type_id,\n", "                product_type,\n", "                l0_category_id,\n", "                l1_category_id,\n", "                l2_category_id,\n", "                l0_category,\n", "                l1_category,\n", "                l2_category\n", "from dwh.dim_product\n", "where is_current and is_product_enabled\"\"\"\n", "product_univ = helper.run_query_with_retries(product_univ_query)"]}, {"cell_type": "markdown", "id": "6e5a2403-51b0-4533-86e7-632270dc6197", "metadata": {}, "source": ["# City id to city name mapping"]}, {"cell_type": "code", "execution_count": null, "id": "d4db291b-f922-4fd9-baba-8ebd324958fb", "metadata": {"tags": []}, "outputs": [], "source": ["city_id_name_mapping = helper.get_city_id_name_data()\n", "city_dict = dict(city_id_name_mapping.values)\n", "city_dict"]}, {"cell_type": "code", "execution_count": null, "id": "531274c0-c65b-477e-ba29-5c0d68bf692f", "metadata": {}, "outputs": [], "source": ["len(city_dict)"]}, {"cell_type": "markdown", "id": "e3a787d0-416c-4e05-81cb-7090d43bd4f7", "metadata": {}, "source": ["# Blacklist data"]}, {"cell_type": "code", "execution_count": null, "id": "d0fe0b06-87bb-4438-b9f4-c66794224b2c", "metadata": {}, "outputs": [], "source": ["# reading blacklist data\n", "(\n", "    exclusion_all_l0,\n", "    exclusion_all_l1,\n", "    exclusion_all_ptype,\n", "    exclusion_all_pid,\n", "    exclusion_antecedent_l0,\n", "    exclusion_antecedent_l1,\n", "    exclusion_antecedent_ptype,\n", "    exclusion_antecedent_pid,\n", "    exclusion_consequent_l0,\n", "    exclusion_consequent_l1,\n", "    exclusion_consequent_ptype,\n", "    exclusion_consequent_pid,\n", ") = helper.get_blacklist_data(widget=\"NPR\", from_sheet=use_sheet)"]}, {"cell_type": "code", "execution_count": null, "id": "f4493bc2-2c7c-439b-bf03-3a5b38e2d1b8", "metadata": {}, "outputs": [], "source": ["# removing blacklist data\n", "product_univ = product_univ.loc[\n", "    (~product_univ.product_id.isin(exclusion_all_pid))\n", "    & (~product_univ.product_type_id.isin(exclusion_all_ptype))\n", "    & (\n", "        ~product_univ.l1_category_id.isin(exclusion_all_l1)\n", "        & (~product_univ.l0_category_id.isin(exclusion_all_l0))\n", "    )\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "833cc31a-dbb8-41ec-a5a9-7bdfb84d7bde", "metadata": {}, "outputs": [], "source": ["product_univ.shape"]}, {"cell_type": "markdown", "id": "f83fa412-c46f-488c-97e8-4df194e41329", "metadata": {}, "source": ["# Product - group id mapping"]}, {"cell_type": "code", "execution_count": null, "id": "6527cded-89f5-4a23-9a9d-ec8764ee70ea", "metadata": {}, "outputs": [], "source": ["# product - group id mapping\n", "group_info = helper.get_product_group_id_data()\n", "group_info.shape"]}, {"cell_type": "markdown", "id": "96e4887c-85e7-4627-8aa4-23abbe3ee86e", "metadata": {}, "source": ["# Group, region, city level mapping"]}, {"cell_type": "code", "execution_count": null, "id": "4e7fc84f-e40f-4b8f-aed2-c108e926a170", "metadata": {}, "outputs": [], "source": ["# region level data\n", "group_city_mapping = helper.get_group_city_mapping(from_sheet=use_sheet)\n", "city_group_dict = dict(zip(group_city_mapping[\"city_id\"], group_city_mapping[\"group\"]))\n", "city_region_dict = dict(zip(group_city_mapping[\"city_id\"], group_city_mapping[\"city_group\"]))"]}, {"cell_type": "code", "execution_count": null, "id": "4a048f14-716c-4e4a-a81b-00df84e5971a", "metadata": {}, "outputs": [], "source": ["group_city_mapping.head()"]}, {"cell_type": "markdown", "id": "d7b5fcce-2c1f-405d-a4e0-0756ea1ab973", "metadata": {}, "source": ["# Imported and affluent pids"]}, {"cell_type": "code", "execution_count": null, "id": "3c83a3ec-95d7-4c47-bb5f-bf168ae27968", "metadata": {}, "outputs": [], "source": ["imported_pids_df = helper.get_imported_pids()\n", "affluent_pids_df = helper.get_affluent_pids()"]}, {"cell_type": "markdown", "id": "233ed0b4-1101-419b-b0ed-62cd999bb248", "metadata": {}, "source": ["# Pids for cleora embedding: Organic, Premium, imported and affluent"]}, {"cell_type": "code", "execution_count": null, "id": "0f78d789-debe-4e9c-b8c4-fe051833f815", "metadata": {}, "outputs": [], "source": ["req_cleora_pids_df = (\n", "    pd.concat(\n", "        [\n", "            product_univ.query(\"l0_category == 'Organic & Premium'\")[[\"product_id\"]],\n", "            imported_pids_df,\n", "            affluent_pids_df,\n", "        ]\n", "    )\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "req_cleora_pids_df.shape"]}, {"cell_type": "markdown", "id": "64d4a7a6-3c6a-42b5-ac4a-3aa95578f90b", "metadata": {}, "source": ["# Reading Region-wise cleora results and push to s3"]}, {"cell_type": "code", "execution_count": null, "id": "38667378-42a1-4d6d-aa93-b68ba60c2e24", "metadata": {}, "outputs": [], "source": ["# region wise cleora results\n", "for region in group_city_mapping.city_group.unique():\n", "    print(region)\n", "    try:\n", "        cleora_region = helper.get_cleora_embeddings(\n", "            product_univ, req_cleora_pids_df, region=region\n", "        )\n", "        cleora_region.to_parquet(\"/tmp/cleora_region.pq\", index=False)\n", "        pb.to_s3(\n", "            \"/tmp/cleora_region.pq\",\n", "            \"prod-dse-projects\",\n", "            \"recommendations/NPR/exp_a/recommendations/cleora_\" + region + \".pq\",\n", "        )\n", "        cleora_region = []\n", "        del cleora_region\n", "        gc.collect()\n", "    except:\n", "        print(\"data not updated\")\n", "        continue"]}, {"cell_type": "markdown", "id": "e4123890-05e7-4f40-9e06-7974c6f0f40d", "metadata": {}, "source": ["# Reading cities name for which data prep is completed"]}, {"cell_type": "code", "execution_count": null, "id": "e258a717-e9bb-4a28-8d4f-371e6c550c87", "metadata": {}, "outputs": [], "source": ["pb.from_s3(\n", "    \"prod-dse-projects\",\n", "    \"recommendations/NPR/exp_a/recommendations/completed_cities_model_inf.pkl\",\n", "    \"/tmp/completed_cities.pkl\",\n", ")\n", "\n", "with open(\"/tmp/completed_cities.pkl\", \"rb\") as file:\n", "    completed_cities = pickle.load(file)"]}, {"cell_type": "code", "execution_count": null, "id": "40817938-208b-44bc-b6ea-a229b0e8d955", "metadata": {}, "outputs": [], "source": ["len(completed_cities), len(city_dict)"]}, {"cell_type": "code", "execution_count": null, "id": "dc99279e-44a3-46b8-a8d0-391dff0b3da0", "metadata": {}, "outputs": [], "source": ["# Remove keys from city_dict that are present in completed_cities\n", "city_dict = {key: value for key, value in city_dict.items() if key not in completed_cities}"]}, {"cell_type": "code", "execution_count": null, "id": "0238802f-88ff-42cc-a65e-c56ecbf1d29c", "metadata": {}, "outputs": [], "source": ["len(city_dict)"]}, {"cell_type": "markdown", "id": "656f0ef7-49e6-4957-9506-b7f1cd37cddc", "metadata": {}, "source": ["# Reading pan india cart counts for pids"]}, {"cell_type": "code", "execution_count": null, "id": "7f79cf3e-902b-4da4-99cf-ee304e25dc29", "metadata": {}, "outputs": [], "source": ["pan_city_pid_cart_count = (\n", "    helper.get_pid_cart_count()\n", ")  # for cities which have active pids but still no sales"]}, {"cell_type": "code", "execution_count": null, "id": "b1f5fece-5ad2-408f-92ef-0a21983d9f00", "metadata": {}, "outputs": [], "source": ["pan_city_pid_cart_count.shape"]}, {"cell_type": "markdown", "id": "d149e9f4-2bd6-46a6-a9dd-773a1730dc2a", "metadata": {}, "source": ["# Citywise data prep"]}, {"cell_type": "code", "execution_count": null, "id": "be2f065d-8c32-4ce1-aaf0-fc305800a413", "metadata": {}, "outputs": [], "source": ["# # Father's day - Festive\n", "# gifting_df, festival_df, fnv_df = helper.get_festival_df(from_sheet=use_sheet)\n", "# festive_ptype_data = helper.get_complimentary_data(\n", "#     festival_df, gifting_df, fnv_df, festive=True, gifting=False, fnv=False, from_sheet=use_sheet\n", "# )\n", "\n", "# ptype_pids_df = pd.read_sql(\n", "#     \"\"\"\n", "#     SELECT DISTINCT product_id, product_type\n", "#     FROM dwh.dim_product\n", "#     WHERE is_current\n", "#       AND is_product_enabled\n", "#     \"\"\",\n", "#     con=pb.get_connection(\"[Warehouse] Trino\"),\n", "# )\n", "\n", "# fathers_day_pids_df = ptype_pids_df[\n", "#     ptype_pids_df[\"product_type\"].isin(list(festive_ptype_data[\"ptype\"].unique()))\n", "# ]\n", "# fathers_day_pids_df = fathers_day_pids_df.sort_values(by=[\"product_type\", \"product_id\"])\n", "# fathers_day_pids_df = fathers_day_pids_df.rename(columns={\"product_type\": \"ant_ptype\"})\n", "# fathers_day_pids_df[\"source\"] = 0\n", "\n", "# pb.from_s3(\n", "#     \"prod-dse-projects\",\n", "#     \"recommendations/NPR/exp_a/recommendations/festive_pid.csv\",\n", "#     \"/tmp/result_df.csv\",\n", "# )\n", "\n", "# fathers_day_results_df = pd.read_csv(\"/tmp/result_df.csv\")[[\"product_id\", \"product_name\", \"sim\"]]\n", "# fathers_day_results_df = pd.merge(fathers_day_results_df, ptype_pids_df, on=\"product_id\")\n", "# fathers_day_results_df = fathers_day_results_df.sort_values(\n", "#     by=[\"product_type\", \"sim\"], ascending=[False, False]\n", "# )\n", "# fathers_day_results_df = fathers_day_results_df.rename(columns={\"product_type\": \"value\"})\n", "# fathers_day_results_df = pd.merge(\n", "#     festive_ptype_data, fathers_day_results_df, on=\"value\", how=\"left\"\n", "# )\n", "# fathers_day_results_df = fathers_day_results_df.dropna()[[\"ptype\", \"value\", \"product_id\", \"sim\"]]\n", "# fathers_day_results_df = fathers_day_results_df.rename(\n", "#     columns={\"ptype\": \"ant_ptype\", \"product_id\": \"consequent_pid\", \"sim\": \"scores\"}\n", "# )\n", "# fathers_day_results_df[\"consequent_pid\"] = fathers_day_results_df[\"consequent_pid\"].astype(int)\n", "\n", "# fathers_day_arm_df = pd.merge(\n", "#     fathers_day_pids_df, fathers_day_results_df, on=\"ant_ptype\", how=\"left\"\n", "# )\n", "# fathers_day_arm_df = fathers_day_arm_df[[\"product_id\", \"consequent_pid\", \"scores\", \"source\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "ee4e4006-9825-4934-b461-7ec851bde893", "metadata": {}, "outputs": [], "source": ["null_city_region = []\n", "null_city_active_pids = []\n", "null_city_cleora = []"]}, {"cell_type": "code", "execution_count": null, "id": "8efceee2-1f30-4c43-ba0a-4f2ddd0f6e1b", "metadata": {}, "outputs": [], "source": ["# === Base DF loads (outside loop) ===\n", "active_pids_base_df = helper.get_active_pids()\n", "prev_buys_base_df = helper.get_city_pid_cart_count()"]}, {"cell_type": "code", "execution_count": null, "id": "0a82278d-80c8-4b8a-9095-f20421c690f8", "metadata": {}, "outputs": [], "source": ["active_pids_base_df.shape, prev_buys_base_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "8fa5130e-cdf8-4396-8886-137f798c26f7", "metadata": {}, "outputs": [], "source": ["# Default fallback values\n", "DEFAULT_REGION = \"North\"\n", "DEFAULT_GROUP = \"group1\"\n", "\n", "# Build a flat list: (region, group, city_id, city_name)\n", "flat_list = []\n", "\n", "for city_name, city_id in city_dict.items():\n", "    region = city_region_dict.get(city_id, DEFAULT_REGION)\n", "    group = city_group_dict.get(city_id, DEFAULT_GROUP)\n", "    flat_list.append((region, group, city_id, city_name))\n", "\n", "# Sort by region, then group, then city_id\n", "flat_list.sort(key=lambda x: (x[0], x[1], x[2]))"]}, {"cell_type": "code", "execution_count": null, "id": "0d8890dc-14d2-4d80-a148-eec9f8c494ba", "metadata": {}, "outputs": [], "source": ["len(flat_list)"]}, {"cell_type": "code", "execution_count": null, "id": "f10e94aa-53f1-4817-be53-448efc3df66f", "metadata": {}, "outputs": [], "source": ["# === Load static data once (outside loop) ===\n", "\n", "pb.from_s3(\n", "    \"prod-dse-projects\",\n", "    \"recommendations/NPR/exp_a/supporting_files/prod_info_data.csv\",\n", "    \"/tmp/prod_info_data.csv\",\n", ")\n", "ptype_mapping = pd.read_csv(\"/tmp/prod_info_data.csv\")\n", "\n", "pb.from_s3(\n", "    \"prod-dse-projects\",\n", "    \"recommendations/NPR/exp_a/supporting_files/ptype_ptype_rankings.csv\",\n", "    \"/tmp/ptype_ptype_rankings.csv\",\n", ")\n", "ptype_scores_master = pd.read_csv(\"/tmp/ptype_ptype_rankings.csv\")\n", "\n", "prod_info = pd.merge(\n", "    product_univ,\n", "    ptype_mapping[\n", "        [\"product_type_id\", \"product_type_id_final\", \"product_type_final\"]\n", "    ].drop_duplicates(),\n", "    on=\"product_type_id\",\n", "    how=\"left\",\n", ")\n", "\n", "result_df_base = product_univ[[\"product_id\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "19488b3c-5127-471e-91ed-091c31072c04", "metadata": {}, "outputs": [], "source": ["# Initialize caches\n", "cleora_cache = {}\n", "arm_cache = {}\n", "\n", "prev_region = None\n", "prev_group = None"]}, {"cell_type": "code", "execution_count": null, "id": "adba5c5e-4b07-4f93-8a66-b4fe033f8c1d", "metadata": {"tags": []}, "outputs": [], "source": ["counter = 0\n", "\n", "for region, city_group, city_id, city in flat_list:\n", "\n", "    print(\"City started:\", city)\n", "\n", "    # Clear old region cache if we've moved to a new region\n", "    if prev_region and region != prev_region and prev_region in cleora_cache:\n", "        del cleora_cache[prev_region]\n", "        print(f\"[Memory Cleanup] Cleared cleora cache for region: {prev_region}\")\n", "\n", "    # Clear old group cache if we've moved to a new group\n", "    if prev_group and city_group != prev_group and prev_group in arm_cache:\n", "        del arm_cache[prev_group]\n", "        print(f\"[Memory Cleanup] Cleared ARM cache for group: {prev_group}\")\n", "\n", "    prev_region, prev_group = region, city_group\n", "\n", "    active_pids = (\n", "        active_pids_base_df[active_pids_base_df[\"city_name\"] == city][[\"product_id\"]]\n", "        .drop_duplicates()\n", "        .reset_index(drop=True)\n", "    )\n", "    if len(active_pids) < 50:\n", "        print(\"no active pids found\")\n", "        null_city_active_pids.append(city)\n", "        continue\n", "\n", "    # <PERSON><PERSON> results with caching\n", "    if region in cleora_cache:\n", "        cleora_result_df = cleora_cache[region].copy()\n", "\n", "    else:\n", "        try:\n", "            pb.from_s3(\n", "                \"prod-dse-projects\",\n", "                f\"recommendations/NPR/exp_a/recommendations/cleora_{region}.pq\",\n", "                \"/tmp/cleora_region_results.pq\",\n", "            )\n", "            cleora_result_df = pd.read_parquet(\"/tmp/cleora_region_results.pq\")\n", "            cleora_result_df = cleora_result_df.merge(\n", "                req_cleora_pids_df, left_on=\"antecedents\", right_on=\"product_id\"\n", "            )[[\"antecedents\", \"consequents\"]]\n", "            cleora_result_df = cleora_result_df.explode(\"consequents\")\n", "            cleora_result_df[\"score\"] = 1 / (cleora_result_df.groupby(\"antecedents\").cumcount() + 1)\n", "            cleora_cache[region] = cleora_result_df.copy()\n", "\n", "        except:\n", "            null_city_cleora.append(city)\n", "            cleora_result_df = pd.DataFrame(columns=[\"antecedents\", \"consequents\", \"score\"])\n", "\n", "    # Load ARM results with caching\n", "    if city_group in arm_cache:\n", "        ARM_result_df = arm_cache[city_group].copy()\n", "\n", "    else:\n", "        ARM_result_df = helper.get_raw_arm_model_results(city_group)\n", "        arm_cache[city_group] = ARM_result_df.copy()\n", "\n", "    if len(ARM_result_df) < 10:\n", "        null_city_region.append(city)\n", "\n", "    ARM_result_df = ARM_result_df.merge(\n", "        active_pids, left_on=[\"antecedents\"], right_on=[\"product_id\"]\n", "    ).merge(active_pids, left_on=[\"consequents\"], right_on=[\"product_id\"])\n", "\n", "    cleora_result_df = cleora_result_df.merge(\n", "        active_pids, left_on=[\"antecedents\"], right_on=[\"product_id\"]\n", "    ).merge(active_pids, left_on=[\"consequents\"], right_on=[\"product_id\"])\n", "\n", "    ARM_result_df[\"city_id\"] = city_id\n", "    ARM_result_df[\"city\"] = city\n", "\n", "    # for cases where confidence=1, then conviction would be infinity - cases include just 1 or 2 transaction for those ant-cons pair\n", "    ARM_result_df[\"conviction\"] = np.where(\n", "        np.isinf(ARM_result_df[\"conviction\"]),\n", "        ARM_result_df[\"conviction\"].median(),\n", "        ARM_result_df[\"conviction\"],\n", "    )\n", "\n", "    # calculating combined score to rerank pids\n", "    scaler = MinMaxScaler(feature_range=(1, 2))\n", "    ARM_result_df[[\"scaled_lift\", \"scaled_conviction\"]] = scaler.fit_transform(\n", "        ARM_result_df[[\"lift\", \"conviction\"]]\n", "    )\n", "    ARM_result_df[\"score\"] = ARM_result_df[[\"scaled_lift\", \"scaled_conviction\"]].mean(axis=1)\n", "\n", "    ARM_result_df = ARM_result_df.rename(\n", "        columns={\n", "            \"antecedents\": \"product_id\",\n", "            \"consequents\": \"consequent_pid\",\n", "            \"score\": \"scores\",\n", "        }\n", "    )[[\"product_id\", \"consequent_pid\", \"scores\"]].drop_duplicates()\n", "\n", "    cleora_result_df = cleora_result_df.rename(\n", "        columns={\n", "            \"antecedents\": \"product_id\",\n", "            \"consequents\": \"consequent_pid\",\n", "            \"score\": \"scores\",\n", "        }\n", "    )[[\"product_id\", \"consequent_pid\", \"scores\"]].drop_duplicates()\n", "\n", "    ARM_result_df = result_df_base.merge(ARM_result_df)\n", "    cleora_result_df = result_df_base.merge(cleora_result_df)\n", "\n", "    cleora_result_df[\"source\"] = 1  #'cleora'\n", "    ARM_result_df[\"source\"] = 2  #'arm'\n", "\n", "    cleora_result_df[\"consequent_pid\"] = cleora_result_df[\"consequent_pid\"].astype(int)\n", "    # result_df = pd.concat([fathers_day_arm_df, cleora_result_df, ARM_result_df])\n", "    result_df = pd.concat([cleora_result_df, ARM_result_df])\n", "    result_df.sort_values(\"source\", ascending=True, inplace=True)\n", "    result_df.drop_duplicates(subset=[\"product_id\", \"consequent_pid\"], keep=\"first\", inplace=True)\n", "\n", "    del cleora_result_df, ARM_result_df\n", "    gc.collect()\n", "\n", "    # calculating popular pids on the basis of cart count on master ptype level\n", "    prev_buys = (\n", "        prev_buys_base_df[prev_buys_base_df[\"city_name\"] == city][\n", "            [\"product_type\", \"product_id\", \"cart_count\"]\n", "        ]\n", "        .drop_duplicates()\n", "        .reset_index(drop=True)\n", "    )\n", "\n", "    if len(prev_buys) == 0:\n", "        prev_buys = pan_city_pid_cart_count.copy()\n", "    prev_buys = (\n", "        prev_buys.merge(\n", "            ptype_mapping[[\"product_type\", \"product_type_final\"]].drop_duplicates(),\n", "            on=[\"product_type\"],\n", "        )\n", "        .drop(columns=[\"product_type\"])\n", "        .rename(columns={\"product_type_final\": \"product_type\"})\n", "    )\n", "\n", "    prev_buys.sort_values(\n", "        [\"product_type\", \"cart_count\", \"product_id\"], ascending=[False, False, False], inplace=True\n", "    )\n", "\n", "    prev_buys_top_5 = prev_buys.groupby([\"product_type\"]).head(5)\n", "\n", "    del prev_buys\n", "\n", "    # score_add_factor = (\n", "    #     prev_buys_top_5[\"cart_count\"].max() + 100\n", "    # )  # this factor would be used to sort consequents; using cart_count would indicate consequents which came in due to popularity and not ARM\n", "\n", "    # getting complimentary sheet data\n", "    ptype_scores = ptype_scores_master.copy()\n", "\n", "    # getting product info for antecedent pids\n", "    result_df = pd.merge(\n", "        result_df,\n", "        prod_info[\n", "            [\n", "                \"product_id\",\n", "                \"product_name\",\n", "                \"product_type_id\",\n", "                \"product_type\",\n", "            ]\n", "        ].rename(\n", "            columns={\n", "                \"product_name\": \"antecedent_product_name\",\n", "                \"product_type_id\": \"antecedent_product_type_id\",\n", "                \"product_type\": \"antecedent_product_type\",\n", "            }\n", "        ),\n", "        on=[\"product_id\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    # getting product info for consequent pids\n", "    result_df = pd.merge(\n", "        result_df,\n", "        prod_info[\n", "            [\n", "                \"product_id\",\n", "                \"product_name\",\n", "                \"product_type_id_final\",\n", "                \"product_type_final\",\n", "            ]\n", "        ].rename(\n", "            columns={\n", "                \"product_id\": \"consequent_pid\",\n", "                \"product_name\": \"consequent_product_name\",\n", "                \"product_type_id_final\": \"consequent_product_type_id\",\n", "                \"product_type_final\": \"consequent_product_type\",\n", "            }\n", "        ),\n", "        on=[\"consequent_pid\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    # combining ARM result with complementary sheet\n", "    temp_pid_df = (\n", "        product_univ[[\"product_id\", \"product_type\"]]\n", "        .merge(active_pids)\n", "        .drop_duplicates()\n", "        .rename(columns={\"product_type\": \"antecedent_product_type\"})\n", "    )\n", "    temp_pid_df[\"dummy\"] = 1\n", "    ptype_scores[\"dummy\"] = 1\n", "    ptype_scores = ptype_scores.merge(temp_pid_df).drop(columns=[\"dummy\"])\n", "    final_df = pd.merge(\n", "        ptype_scores,\n", "        result_df,\n", "        on=[\"antecedent_product_type\", \"consequent_product_type\", \"product_id\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    del temp_pid_df, result_df\n", "\n", "    # appending popularity data to results\n", "    backfill = ptype_scores.merge(\n", "        prev_buys_top_5.rename(\n", "            columns={\n", "                \"product_type\": \"consequent_product_type\",\n", "                \"product_id\": \"consequent_pid\",\n", "            }\n", "        )\n", "    )\n", "\n", "    del ptype_scores, prev_buys_top_5\n", "\n", "    backfill[\"source\"] = 3  # popularity\n", "\n", "    concat_df = pd.concat([final_df.query(\"consequent_pid==consequent_pid\"), backfill])\n", "\n", "    del backfill, final_df\n", "\n", "    # concat_df[\"scores\"] = concat_df[\"scores\"] + score_add_factor\n", "    concat_df.loc[concat_df.scores.isna(), \"scores\"] = concat_df.loc[\n", "        concat_df.scores.isna(), \"cart_count\"\n", "    ]\n", "    concat_df[\"consequent_pid\"] = concat_df.consequent_pid.astype(\"int\")\n", "\n", "    concat_df = concat_df.drop(\n", "        columns=[\n", "            \"antecedent_product_name\",\n", "            \"antecedent_product_type_id\",\n", "            \"consequent_product_name\",\n", "            \"consequent_product_type_id\",\n", "            \"cart_count\",\n", "        ]\n", "    )\n", "\n", "    concat_df.drop_duplicates(subset=[\"product_id\", \"consequent_pid\"], inplace=True)\n", "    concat_df.rename(\n", "        columns={\n", "            \"product_type_id_ant\": \"antecedent_product_type_id\",\n", "            \"product_type_id_cons\": \"consequent_product_type_id\",\n", "        },\n", "        inplace=True,\n", "    )\n", "    # concat_df.query(\"product_id == 498983\").sort_values(['rank','scores'], ascending=[True,False])\n", "\n", "    final_data = pd.merge(\n", "        concat_df,\n", "        prod_info[[\"product_id\", \"product_name\"]].rename(\n", "            columns={\n", "                \"product_id\": \"consequent_pid\",\n", "                \"product_name\": \"consequent_product_name\",\n", "            }\n", "        ),\n", "        on=[\"consequent_pid\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    del concat_df\n", "\n", "    final_data = pd.merge(\n", "        final_data,\n", "        prod_info[[\"product_id\", \"product_name\"]].rename(\n", "            columns={\"product_name\": \"antecedent_product_name\"}\n", "        ),\n", "        on=[\"product_id\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    # sort on basis complementary ptype rank and scores(model scores and also cart_counts for missing ptypes as scores)\n", "    final_data.sort_values(\n", "        [\"product_id\", \"rank\", \"source\", \"scores\"],\n", "        ascending=[True, True, True, False],\n", "        inplace=True,\n", "    )\n", "\n", "    final_data[\"product_id\"] = final_data[\"product_id\"].astype(int)\n", "    final_data[\"consequent_pid\"] = final_data[\"consequent_pid\"].astype(int)\n", "    final_data[\"antecedent_product_type_id\"] = final_data[\"antecedent_product_type_id\"].astype(int)\n", "    final_data.dropna(subset=[\"consequent_product_type_id\"], inplace=True)\n", "    final_data[\"consequent_product_type_id\"] = final_data[\"consequent_product_type_id\"].astype(int)\n", "\n", "    final_data[\"city_id\"] = city_dict[city]\n", "\n", "    # remove same group pids from antecedent and consequent\n", "    final_data = pd.merge(\n", "        final_data,\n", "        group_info.rename(columns={\"group_id\": \"antecedent_group_id\", \"product_id\": \"product_id\"}),\n", "        on=[\"product_id\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    final_data = pd.merge(\n", "        final_data,\n", "        group_info.rename(\n", "            columns={\"group_id\": \"consequent_group_id\", \"product_id\": \"consequent_pid\"}\n", "        ),\n", "        on=[\"consequent_pid\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    final_data = final_data[final_data[\"product_id\"] != final_data[\"consequent_pid\"]]\n", "    final_data = final_data[final_data[\"antecedent_group_id\"] != final_data[\"consequent_group_id\"]]\n", "    final_data.reset_index(drop=True, inplace=True)\n", "\n", "    final_data = final_data.drop(columns=[\"antecedent_group_id\", \"consequent_group_id\"])\n", "\n", "    print(\"No of unique product id: \", final_data.product_id.nunique())\n", "    print(\"Final Dataframe shape: \", final_data.shape)\n", "\n", "    # data write to local\n", "    final_data.to_csv(\"/tmp/\" + city + \".csv\", index=False)\n", "    pb.to_s3(\n", "        \"/tmp/\" + city + \".csv\",\n", "        \"prod-dse-projects\",\n", "        \"recommendations/NPR/exp_a/recommendations/\" + city + \".csv\",\n", "    )\n", "\n", "    del final_data\n", "    gc.collect()\n", "\n", "    completed_cities[city] = city_id\n", "    # send to s3\n", "    with open(\"/tmp/completed_cities.pkl\", \"wb\") as file:\n", "        pickle.dump(completed_cities, file)\n", "    pb.to_s3(\n", "        \"/tmp/completed_cities.pkl\",\n", "        \"prod-dse-projects\",\n", "        \"recommendations/NPR/exp_a/recommendations/completed_cities_model_inf.pkl\",\n", "    )\n", "    print(\"City ended:\", city)\n", "    counter = counter + 1\n", "    print(\"Count of cities completed: \", counter)\n", "    print()"]}, {"cell_type": "markdown", "id": "3d886365-d7d6-41d5-a919-19d9c486a86d", "metadata": {}, "source": ["# Null region, cities"]}, {"cell_type": "code", "execution_count": null, "id": "a860bc01-5c09-42d4-a095-bae79c8c73a6", "metadata": {}, "outputs": [], "source": ["null_city_region"]}, {"cell_type": "code", "execution_count": null, "id": "49ecadf8-21f3-405f-bccc-daac7c9c83e9", "metadata": {}, "outputs": [], "source": ["null_city_active_pids"]}, {"cell_type": "code", "execution_count": null, "id": "b927113c-5bc7-4d48-9cd0-d650d8a160c3", "metadata": {}, "outputs": [], "source": ["null_city_cleora"]}, {"cell_type": "markdown", "id": "23cb9ae3-d666-42c2-b2e0-f9f09f7bb08c", "metadata": {}, "source": ["# Make the completed cities dictionary as empty"]}, {"cell_type": "code", "execution_count": null, "id": "38deb9be-6a4a-4636-9327-98694f106fee", "metadata": {}, "outputs": [], "source": ["completed_cities = dict()\n", "with open(\"/tmp/completed_cities.pkl\", \"wb\") as file:\n", "    pickle.dump(completed_cities, file)\n", "pb.to_s3(\n", "    \"/tmp/completed_cities.pkl\",\n", "    \"prod-dse-projects\",\n", "    \"recommendations/NPR/exp_a/recommendations/completed_cities_model_inf.pkl\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1b15e419-eb31-48aa-b8fa-d3a8bf68ea14", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}