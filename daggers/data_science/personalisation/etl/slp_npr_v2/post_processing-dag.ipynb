{"cells": [{"cell_type": "markdown", "id": "c765e980-6c22-4003-9a5b-8fb3b703b2c5", "metadata": {}, "source": ["# Append current working directory and Imports"]}, {"cell_type": "code", "execution_count": null, "id": "21d0451a-b1c8-4ed1-b6a6-5d874c56ea81", "metadata": {}, "outputs": [], "source": ["import os, sys"]}, {"cell_type": "code", "execution_count": null, "id": "d02e69b1-6adc-44c6-807c-2319303c3055", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "4b61d22f-bb3d-486f-b5f7-4422c9b189b3", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "32ed39b3-143e-4c4f-8945-03652621c799", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "import pencilbox as pb\n", "import pytz\n", "from datetime import datetime\n", "import ast\n", "import time\n", "import gc\n", "import helper\n", "import queries\n", "import pickle\n", "\n", "use_sheet = True"]}, {"cell_type": "code", "execution_count": null, "id": "10dc0de8-55fa-4e44-a8dd-6e20d96d16de", "metadata": {}, "outputs": [], "source": ["import numpy as np"]}, {"cell_type": "code", "execution_count": null, "id": "68968166-1942-4271-b38c-ef745ff760dc", "metadata": {}, "outputs": [], "source": ["date = pd.to_datetime(datetime.now()).date().day\n", "print(date)"]}, {"cell_type": "markdown", "id": "e20682ce-325d-4549-98b4-ebad5f3e7789", "metadata": {}, "source": ["# Books and Magazine metadata"]}, {"cell_type": "code", "execution_count": null, "id": "c1400bd2-ddbf-45b3-a949-5f327d150e8b", "metadata": {}, "outputs": [], "source": ["books_prod_info = helper.run_query_with_retries(\n", "    \"\"\"\n", "    select \n", "        product_id,\n", "        product_name,\n", "        l1_category_id,\n", "        l1_category,\n", "        l2_category_id,\n", "        l2_category,\n", "        product_type,\n", "        product_type_id \n", "    from dwh.dim_product \n", "    where is_product_enabled \n", "    and is_current \n", "    and product_type in ('Book','Magazine') \n", "    group by 1,2,3,4,5,6,7,8\n", "    \"\"\"\n", ")"]}, {"cell_type": "markdown", "id": "354aeb54-0c12-4c82-aae5-16f583a63350", "metadata": {}, "source": ["# Product metadata saved in previous notebook"]}, {"cell_type": "code", "execution_count": null, "id": "2dff732a-8b30-4569-9cf2-728b283fb333", "metadata": {}, "outputs": [], "source": ["# product meta data\n", "pb.from_s3(\n", "    \"prod-dse-projects\",\n", "    \"recommendations/NPR/exp_a/supporting_files/prod_info_data.csv\",\n", "    \"/tmp/prod_info_data.csv\",\n", ")\n", "prod_info_all = pd.read_csv(\"/tmp/prod_info_data.csv\")\n", "\n", "# product - group id mapping\n", "group_info = helper.get_product_group_id_data()\n", "\n", "prod_info_all = pd.merge(prod_info_all, group_info, on=[\"product_id\"], how=\"left\")"]}, {"cell_type": "markdown", "id": "dc11ba39-be96-40a3-a2f9-6020dce39236", "metadata": {}, "source": ["# City id to city name mapping"]}, {"cell_type": "code", "execution_count": null, "id": "b897189b-52e4-44d1-8447-592cd40c4a1f", "metadata": {"tags": []}, "outputs": [], "source": ["city_id_name_mapping = helper.get_city_id_name_data()\n", "city_dict = dict(city_id_name_mapping.values)\n", "city_dict"]}, {"cell_type": "code", "execution_count": null, "id": "4c787afa-8bf6-4405-835b-0978a974b3f8", "metadata": {}, "outputs": [], "source": ["# Invert the dictionary\n", "reverse_dict = {v: k for k, v in city_dict.items()}"]}, {"cell_type": "markdown", "id": "68f5dea7-869c-4005-bcaf-d7fe2ed86d6e", "metadata": {}, "source": ["# Popularity based on 60 days cart counts for books and magazine"]}, {"cell_type": "code", "execution_count": null, "id": "61ef9b79-94ed-4076-b620-6e8de37e9a44", "metadata": {}, "outputs": [], "source": ["pan_india_l2_pop_books_df = (\n", "    helper.get_books_l2_popularity_data()\n", ")  # for cities where books are live but no sales"]}, {"cell_type": "code", "execution_count": null, "id": "2774d7af-fa02-4d4b-a8c4-414a6039febe", "metadata": {}, "outputs": [], "source": ["pan_india_l2_pop_books_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "5f6c7830-10ce-4047-9fc3-3c67e463121e", "metadata": {}, "outputs": [], "source": ["product_univ_query = f\"\"\"\n", "select distinct product_id,\n", "                product_name,\n", "                product_type_id,\n", "                product_type,\n", "                l0_category_id,\n", "                l1_category_id,\n", "                l2_category_id,\n", "                l0_category,\n", "                l1_category,\n", "                l2_category\n", "from dwh.dim_product\n", "where is_current and is_product_enabled\"\"\"\n", "product_univ = helper.run_query_with_retries(product_univ_query)"]}, {"cell_type": "code", "execution_count": null, "id": "fa840cc4-f82a-4dbd-a8b6-e51b0121bc73", "metadata": {}, "outputs": [], "source": ["# reading blacklist data\n", "(\n", "    exclusion_all_l0,\n", "    exclusion_all_l1,\n", "    exclusion_all_ptype,\n", "    exclusion_all_pid,\n", "    exclusion_antecedent_l0,\n", "    exclusion_antecedent_l1,\n", "    exclusion_antecedent_ptype,\n", "    exclusion_antecedent_pid,\n", "    exclusion_consequent_l0,\n", "    exclusion_consequent_l1,\n", "    exclusion_consequent_ptype,\n", "    exclusion_consequent_pid,\n", ") = helper.get_blacklist_data(widget=\"NPR\", from_sheet=use_sheet)"]}, {"cell_type": "code", "execution_count": null, "id": "210b2c50-19b8-416b-918a-96bccc2718a8", "metadata": {}, "outputs": [], "source": ["total_exclusion_all_pid = list(\n", "    set(\n", "        exclusion_all_pid\n", "        + list(\n", "            product_univ.loc[\n", "                product_univ.l1_category_id.isin(exclusion_all_l1), \"product_id\"\n", "            ].values\n", "        )\n", "        + list(\n", "            product_univ.loc[\n", "                product_univ.l0_category_id.isin(exclusion_all_l0), \"product_id\"\n", "            ].values\n", "        )\n", "        + list(\n", "            product_univ.loc[\n", "                product_univ.product_type_id.isin(exclusion_all_ptype), \"product_id\"\n", "            ].values\n", "        )\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b378281f-ef47-4ab6-b988-c18543e9d877", "metadata": {}, "outputs": [], "source": ["total_exclusion_antecedent_pid = list(\n", "    set(\n", "        exclusion_antecedent_pid\n", "        + list(\n", "            product_univ.loc[\n", "                product_univ.l1_category_id.isin(exclusion_antecedent_l1), \"product_id\"\n", "            ].values\n", "        )\n", "        + list(\n", "            product_univ.loc[\n", "                product_univ.l0_category_id.isin(exclusion_antecedent_l0), \"product_id\"\n", "            ].values\n", "        )\n", "        + list(\n", "            product_univ.loc[\n", "                product_univ.product_type_id.isin(exclusion_antecedent_ptype),\n", "                \"product_id\",\n", "            ].values\n", "        )\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a16f280e-adb9-49d7-ba8f-a59fab884872", "metadata": {}, "outputs": [], "source": ["total_exclusion_consequent_pid = list(\n", "    set(\n", "        exclusion_consequent_pid\n", "        + list(\n", "            product_univ.loc[\n", "                product_univ.l1_category_id.isin(exclusion_consequent_l1), \"product_id\"\n", "            ].values\n", "        )\n", "        + list(\n", "            product_univ.loc[\n", "                product_univ.l0_category_id.isin(exclusion_consequent_l0), \"product_id\"\n", "            ].values\n", "        )\n", "        + list(\n", "            product_univ.loc[\n", "                product_univ.product_type_id.isin(exclusion_consequent_ptype),\n", "                \"product_id\",\n", "            ].values\n", "        )\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8f081367-174e-410e-966c-7db9d2e831a8", "metadata": {}, "outputs": [], "source": ["# removing blacklist data\n", "product_univ = product_univ.loc[\n", "    (~product_univ.product_id.isin(exclusion_all_pid))\n", "    & (~product_univ.product_type_id.isin(exclusion_all_ptype))\n", "    & (\n", "        ~product_univ.l1_category_id.isin(exclusion_all_l1)\n", "        & (~product_univ.l0_category_id.isin(exclusion_all_l0))\n", "    )\n", "]"]}, {"cell_type": "markdown", "id": "f00ad436-91ae-4c94-b906-bf11acc5ddef", "metadata": {}, "source": ["# Generate Gender Flag"]}, {"cell_type": "code", "execution_count": null, "id": "e5d8bb94-9db4-4702-9785-fe027208eed2", "metadata": {}, "outputs": [], "source": ["prod_info_gender_l2 = helper.gender_flag(\"l2_category\", product_univ)\n", "print(prod_info_gender_l2.shape)\n", "prod_info_gender_l2.columns = [\"l2_category\", \"gender_l2_flag\"]\n", "prod_info_gender_l2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f8ea3fb0-7e96-41f0-841c-b72455480728", "metadata": {}, "outputs": [], "source": ["prod_info_gender_l1 = helper.gender_flag(\"l1_category\", product_univ)\n", "print(prod_info_gender_l1.shape)\n", "prod_info_gender_l1.columns = [\"l1_category\", \"gender_l1_flag\"]\n", "prod_info_gender_l1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8c29619c-9f08-4add-b5b0-21479e1b7675", "metadata": {}, "outputs": [], "source": ["prod_info_gender_pid = helper.gender_flag(\"product_name\", product_univ)\n", "print(prod_info_gender_pid.shape)\n", "prod_info_gender_pid.columns = [\"product_name\", \"gender_pid_flag\"]\n", "prod_info_gender_pid = prod_info_gender_pid.merge(\n", "    product_univ[[\"product_id\", \"product_name\"]].drop_duplicates(), how=\"left\"\n", ")\n", "prod_info_gender_pid = prod_info_gender_pid.drop([\"product_name\"], axis=1)\n", "print(prod_info_gender_pid.shape)\n", "prod_info_gender_pid.head()"]}, {"cell_type": "markdown", "id": "b0ac66e3-6869-4329-a9b3-a3d2b67b95fe", "metadata": {}, "source": ["# Checking completed cities"]}, {"cell_type": "code", "execution_count": null, "id": "e258a717-e9bb-4a28-8d4f-371e6c550c87", "metadata": {}, "outputs": [], "source": ["pb.from_s3(\n", "    \"prod-dse-projects\",\n", "    \"recommendations/NPR/exp_a/recommendations/completed_cities_final_data.pkl\",\n", "    \"/tmp/completed_cities.pkl\",\n", ")\n", "with open(\"/tmp/completed_cities.pkl\", \"rb\") as file:\n", "    completed_cities = pickle.load(file)"]}, {"cell_type": "code", "execution_count": null, "id": "40817938-208b-44bc-b6ea-a229b0e8d955", "metadata": {}, "outputs": [], "source": ["len(completed_cities), len(city_dict)"]}, {"cell_type": "code", "execution_count": null, "id": "dc99279e-44a3-46b8-a8d0-391dff0b3da0", "metadata": {}, "outputs": [], "source": ["# Remove keys from city_dict that are present in completed_cities\n", "city_dict = {key: value for key, value in city_dict.items() if key not in completed_cities}"]}, {"cell_type": "code", "execution_count": null, "id": "0238802f-88ff-42cc-a65e-c56ecbf1d29c", "metadata": {}, "outputs": [], "source": ["len(city_dict)"]}, {"cell_type": "markdown", "id": "5845530a-9037-411e-a22e-92c61b9ebfa2", "metadata": {"tags": []}, "source": ["# Create clusters"]}, {"cell_type": "code", "execution_count": null, "id": "6f6f6c47-175b-40b8-944d-e4a78727d46d", "metadata": {}, "outputs": [], "source": ["active_pids_base_df = helper.get_active_pids()"]}, {"cell_type": "code", "execution_count": null, "id": "b52ba9d6-8c3a-4cfe-bd23-000868768acb", "metadata": {}, "outputs": [], "source": ["city_count_df = (\n", "    active_pids_base_df.groupby(\"city_name\").agg(pid_count=(\"product_id\", \"count\")).reset_index()\n", ")\n", "city_df = pd.DataFrame({\"city_name\": city_dict.keys(), \"city_id\": city_dict.values()})\n", "city_df = city_df.merge(city_count_df, how=\"left\")\n", "city_df[\"pid_count\"] = city_df[\"pid_count\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "62908dfc-0c69-4009-829a-a47c38437b56", "metadata": {}, "outputs": [], "source": ["high_volume_cities, low_volume_clusters = helper.ffd_city_clustering_with_volume(\n", "    city_df,\n", "    city_col=\"city_name\",\n", "    count_col=\"pid_count\",\n", "    high_volume_threshold=400000,\n", "    max_cluster_volume=400000,\n", ")\n", "\n", "# High-volume cities as individual lists\n", "high_city_groups = [[city] for city, _ in high_volume_cities]\n", "\n", "# Low-volume clusters are already in list form\n", "low_city_groups = [cluster for cluster, _ in low_volume_clusters]\n", "\n", "# Final merged list\n", "all_city_groups = high_city_groups + low_city_groups"]}, {"cell_type": "code", "execution_count": null, "id": "840b02ea-bb74-4a9c-9951-ea5f8711f327", "metadata": {}, "outputs": [], "source": ["print(f\"# of clusters formed: {len(all_city_groups)}\")"]}, {"cell_type": "code", "execution_count": null, "id": "e1ada395-eee1-4c05-9ebd-a60e39e551a3", "metadata": {"tags": []}, "outputs": [], "source": ["all_city_groups"]}, {"cell_type": "markdown", "id": "27de6536-641d-45dc-90cc-733ed1d162d6", "metadata": {"tags": []}, "source": ["# Cluster-wise data prep"]}, {"cell_type": "code", "execution_count": null, "id": "10fdf7bf-9940-4a65-8427-e838ce80a765", "metadata": {}, "outputs": [], "source": ["req_cols = [\n", "    \"city_id\",\n", "    \"city_name\",\n", "    \"product_id\",\n", "    \"consequent_pid\",\n", "    \"ptype_rank\",\n", "    \"scores\",\n", "    \"final_scores_rank\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "7f67113a-14e4-4431-9575-2e6cebe25c1f", "metadata": {}, "outputs": [], "source": ["pb.from_s3(\n", "    \"prod-dse-projects\",\n", "    \"recommendations/NPR/exp_a/supporting_files/ptype_ptype_rankings.csv\",\n", "    \"/tmp/ptype_ptype_rankings.csv\",\n", ")\n", "\n", "ptype_scores = pd.read_csv(\"/tmp/ptype_ptype_rankings.csv\")\n", "\n", "base_combo_df = helper.get_combo_data()"]}, {"cell_type": "code", "execution_count": null, "id": "6705262d-fcc3-42f5-aa9e-c4f2afb80f72", "metadata": {}, "outputs": [], "source": ["if date in (10, 20):\n", "    l2_pop_books_base_df = helper.get_city_books_l2_popularity_data()\n", "    l2_pop_books_base_df.to_parquet(\"/tmp/\" + \"pan_india\" + \"_l2_pop_books_df.pq\")\n", "    pb.to_s3(\n", "        \"/tmp/\" + \"pan_india\" + \"_l2_pop_books_df.pq\",\n", "        \"prod-dse-projects\",\n", "        \"recommendations/NPR/exp_a/supporting_files/\" + \"pan_india\" + \"_l2_pop_books_df.pq\",\n", "    )\n", "else:\n", "    try:\n", "        pb.from_s3(\n", "            \"prod-dse-projects\",\n", "            \"recommendations/NPR/exp_a/supporting_files/\" + \"pan_india\" + \"_l2_pop_books_df.pq\",\n", "            \"/tmp/\" + \"pan_india\" + \"_l2_pop_books_df.pq\",\n", "        )\n", "        l2_pop_books_base_df = pd.read_parquet(\"/tmp/\" + \"pan_india\" + \"_l2_pop_books_df.pq\")\n", "    except:\n", "        l2_pop_books_base_df = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "id": "a43eb12b-c47a-46db-980c-b3758655efda", "metadata": {"tags": []}, "outputs": [], "source": ["for city_group in all_city_groups:\n", "    print(f\"Processing city group: {city_group}\")\n", "    city_ls = []\n", "    for city in city_group:\n", "        try:\n", "            pb.from_s3(\n", "                \"prod-dse-projects\",\n", "                \"recommendations/NPR/exp_a/recommendations/\" + city + \".csv\",\n", "                \"/tmp/\" + city + \".csv\",\n", "            )\n", "            df = pd.read_csv(\"/tmp/\" + city + \".csv\")\n", "        except:\n", "            print(f\"no data from prev NB for city: {city}\")\n", "            continue\n", "        if df.shape[0] == 0:\n", "            print(\"empty df from prev NB\")\n", "            continue\n", "        city_ls.append(df)\n", "\n", "    data = pd.concat(city_ls, ignore_index=True)\n", "    active_pids = (\n", "        active_pids_base_df[active_pids_base_df[\"city_name\"].isin(city_group)][\n", "            [\"city_id\", \"product_id\"]\n", "        ]\n", "        .drop_duplicates()\n", "        .reset_index(drop=True)\n", "    )\n", "    if len(active_pids) == 0:\n", "        print(\"no active pids found\")\n", "        continue\n", "\n", "    ## considering only city active pids\n", "    data = data.merge(\n", "        active_pids[[\"city_id\", \"product_id\"]].drop_duplicates(),\n", "        on=[\"city_id\", \"product_id\"],\n", "        how=\"inner\",\n", "    )\n", "\n", "    ## is a pid has no reco, then use the reco of another pid of same group_id\n", "    aval_reco_prods = data[\n", "        [\"city_id\", \"product_id\", \"antecedent_product_type_id\"]\n", "    ].drop_duplicates()\n", "    aval_reco_prods.reset_index(drop=True, inplace=True)\n", "\n", "    aval_reco_prods = pd.merge(aval_reco_prods, group_info, on=[\"product_id\"], how=\"left\")\n", "    aval_reco_prods[\"dummy\"] = 1\n", "\n", "    # group level product_id list for which recoms are available\n", "    proxy_prod = (\n", "        aval_reco_prods.groupby([\"city_id\", \"antecedent_product_type_id\", \"group_id\"])[\"product_id\"]\n", "        .apply(list)\n", "        .reset_index(name=\"proxy_product_id\")\n", "    )\n", "    proxy_prod[\"proxy_product_id\"] = proxy_prod[\"proxy_product_id\"].apply(lambda x: x[0])\n", "\n", "    intersect_df = pd.merge(\n", "        prod_info_all[[\"group_id\", \"product_id\", \"product_type_id\"]].merge(active_pids),\n", "        aval_reco_prods.rename(columns={\"antecedent_product_type_id\": \"product_type_id\"}),\n", "        on=[\"city_id\", \"product_type_id\", \"group_id\", \"product_id\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    del aval_reco_prods\n", "\n", "    intersect_df = intersect_df[intersect_df[\"dummy\"].isna()]\n", "    intersect_df = intersect_df[\n", "        intersect_df[\"product_type_id\"].isin(data[\"antecedent_product_type_id\"].unique())\n", "    ]\n", "\n", "    intersect_df.reset_index(drop=True, inplace=True)\n", "    intersect_df.drop(columns=[\"dummy\"], inplace=True)\n", "    intersect_df = pd.merge(\n", "        intersect_df,\n", "        proxy_prod.rename(columns={\"antecedent_product_type_id\": \"product_type_id\"}),\n", "        on=[\"city_id\", \"product_type_id\", \"group_id\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    del proxy_prod\n", "\n", "    intersect_df = intersect_df[~intersect_df[\"proxy_product_id\"].isna()]\n", "    intersect_df.reset_index(drop=True, inplace=True)\n", "\n", "    intersect_df[\"proxy_product_id\"] = intersect_df[\"proxy_product_id\"].astype(int)\n", "    intersect_df = pd.merge(\n", "        intersect_df,\n", "        data.rename(columns={\"product_id\": \"proxy_product_id\"}),\n", "        on=[\"city_id\", \"proxy_product_id\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    intersect_df.drop(columns=[\"group_id\", \"proxy_product_id\"], inplace=True)\n", "    intersect_df = intersect_df[data.columns]\n", "\n", "    data = pd.concat([data, intersect_df], axis=0, ignore_index=True)\n", "\n", "    del intersect_df\n", "\n", "    prod_info = prod_info_all.merge(active_pids, on=\"product_id\", how=\"inner\")\n", "\n", "    print(\"before transformed data\")\n", "    print(data.shape)\n", "\n", "    data = data.merge(\n", "        product_univ[[\"product_id\", \"l2_category\", \"l1_category\"]]\n", "        .drop_duplicates()\n", "        .rename(\n", "            columns={\n", "                \"l2_category\": \"antecedent_l2_category\",\n", "                \"l1_category\": \"antecedent_l1_category\",\n", "            }\n", "        ),\n", "        how=\"left\",\n", "    )\n", "    data = data.merge(\n", "        product_univ[[\"product_id\", \"l2_category\", \"l1_category\"]]\n", "        .drop_duplicates()\n", "        .rename(\n", "            columns={\n", "                \"product_id\": \"consequent_pid\",\n", "                \"l2_category\": \"consequent_l2_category\",\n", "                \"l1_category\": \"consequent_l1_category\",\n", "            }\n", "        ),\n", "        how=\"left\",\n", "    )\n", "\n", "    # # Handling giftbox for holi(NOTE: CITY LEVEL CHANGES NOT DONE INSIDE THIS FUNCTION, PLEASE CHECK ONCE WHEN UNCOMMENT)\n", "    # data = helper.handle_holi_gift_box(data)\n", "\n", "    # Assign gender categories\n", "    data = helper.assign_gender_categories(\n", "        data, prod_info_gender_l2, prod_info_gender_l1, prod_info_gender_pid\n", "    )\n", "\n", "    print(\"After transformed data\")\n", "    print(data.shape)\n", "\n", "    columns_to_feed = [\n", "        \"antecedent_product_type\",\n", "        \"consequent_product_type\",\n", "        \"rank\",\n", "        \"product_id\",\n", "        \"consequent_pid\",\n", "        \"scores\",\n", "        \"source\",\n", "        \"antecedent_product_name\",\n", "        \"antecedent_product_type_id\",\n", "        \"consequent_product_name\",\n", "        \"consequent_product_type_id\",\n", "        \"city_id\",\n", "        \"is_l2\",\n", "    ]\n", "\n", "    ## handling Book and Magazine - on the basis of l2 (popularity data)\n", "    all_books = prod_info.query(\"product_type_final in ('Book','Magazine')\")\n", "    if int(all_books.product_type_final.nunique()) == 2:\n", "        all_books[\"dummy\"] = 1\n", "\n", "        l2_pop_books_df = l2_pop_books_base_df[\n", "            l2_pop_books_base_df[\"city_name\"].isin(city_group)\n", "        ].reset_index()\n", "        remaining_cities_ls = list(set(city_group) - set(l2_pop_books_df[\"city_name\"].unique()))\n", "        if len(remaining_cities_ls) > 0:\n", "            l2_pop_books_df_remaining_cities = pan_india_l2_pop_books_df.copy()\n", "            l2_pop_books_df_remaining_cities[\"dummy\"] = 1\n", "            temp_city_df = pd.DataFrame({\"city_name\": remaining_cities_ls, \"dummy\": 1})\n", "            l2_pop_books_df_remaining_cities_final = l2_pop_books_df_remaining_cities.merge(\n", "                temp_city_df\n", "            )\n", "            l2_pop_books_df_remaining_cities_final = l2_pop_books_df_remaining_cities_final.drop(\n", "                columns=[\"dummy\"]\n", "            )\n", "\n", "            l2_pop_books_df = pd.concat([l2_pop_books_df, l2_pop_books_df_remaining_cities_final])\n", "            del (\n", "                l2_pop_books_df_remaining_cities,\n", "                temp_city_df,\n", "                l2_pop_books_df_remaining_cities_final,\n", "            )\n", "\n", "        l2_pop_books_df[\"dummy\"] = 1\n", "\n", "        all_books = all_books.merge(\n", "            books_prod_info[[\"product_id\", \"l2_category_id\", \"l2_category\"]]\n", "        )\n", "        all_books_pred = all_books.merge(l2_pop_books_df, on=[\"l2_category\", \"dummy\"])\n", "        all_books_pred = all_books_pred.query(\"product_id != consequent_pid\")\n", "\n", "        del all_books, l2_pop_books_df\n", "        gc.collect()\n", "\n", "        if len(all_books_pred) > 0:\n", "\n", "            all_books_pred[\"consequent_product_type\"] = all_books_pred[\"product_type_final\"]\n", "            all_books_pred[\"consequent_product_type_id\"] = all_books_pred[\"product_type_id_final\"]\n", "\n", "            book_rank = int(\n", "                ptype_scores.query(\n", "                    \"antecedent_product_type=='Book' and consequent_product_type=='Book'\"\n", "                )[\"rank\"].unique()[0]\n", "            )\n", "            mag_rank = int(\n", "                ptype_scores.query(\n", "                    \"antecedent_product_type=='Magazine' and consequent_product_type=='Magazine'\"\n", "                )[\"rank\"].unique()[0]\n", "            )\n", "\n", "            all_books_pred.loc[\n", "                (all_books_pred.product_type_final == \"Book\")\n", "                & (all_books_pred.consequent_product_type == \"Book\"),\n", "                \"rank\",\n", "            ] = book_rank\n", "            all_books_pred.loc[\n", "                (all_books_pred.product_type_final == \"Magazine\")\n", "                & (all_books_pred.consequent_product_type == \"Magazine\"),\n", "                \"rank\",\n", "            ] = mag_rank\n", "            # all_books_pred[\"city_id\"] = city_dict[city]\n", "\n", "            all_books_pred.rename(\n", "                columns={\n", "                    \"product_type_final\": \"antecedent_product_type\",\n", "                    \"cart_count\": \"scores\",\n", "                    \"product_name\": \"antecedent_product_name\",\n", "                    \"product_type_id_final\": \"antecedent_product_type_id\",\n", "                },\n", "                inplace=True,\n", "            )\n", "            all_books_pred[\"is_l2\"] = 1\n", "            all_books_pred[\"source\"] = 3.0\n", "            all_books_pred = all_books_pred[columns_to_feed]\n", "    else:\n", "        all_books_pred = pd.DataFrame()\n", "\n", "    data[\"is_l2\"] = 0\n", "\n", "    data = data[columns_to_feed]\n", "\n", "    data = pd.concat([data, all_books_pred], axis=0, ignore_index=True)\n", "\n", "    del all_books_pred\n", "\n", "    data[\"city_name\"] = data[\"city_id\"].map(reverse_dict)\n", "    data = data.loc[\n", "        ~(\n", "            (data.antecedent_product_type == \"Book\")\n", "            & (data.consequent_product_type == \"Book\")\n", "            & (data.is_l2 == 0)\n", "        )\n", "    ]\n", "    data = data.loc[\n", "        ~(\n", "            (data.antecedent_product_type == \"Magazine\")\n", "            & (data.consequent_product_type == \"Magazine\")\n", "            & (data.is_l2 == 0)\n", "        )\n", "    ]\n", "    data.drop(columns=[\"is_l2\"], inplace=True)\n", "\n", "    data.sort_values(\n", "        [\"city_id\", \"product_id\", \"rank\", \"source\", \"scores\"],\n", "        ascending=[True, False, True, True, False],\n", "        inplace=True,\n", "    )\n", "\n", "    data[\"final_rank\"] = 1\n", "    data[\"final_rank\"] = data.groupby([\"city_id\", \"product_id\", \"consequent_product_type\"])[\n", "        \"final_rank\"\n", "    ].cumsum()\n", "\n", "    data.sort_values([\"city_id\", \"product_id\", \"final_rank\"], inplace=True)\n", "    data.reset_index(drop=True, inplace=True)\n", "\n", "    data[\"consequent_product_rank\"] = 1\n", "    data[\"consequent_product_rank\"] = data.groupby([\"city_id\", \"product_id\"])[\n", "        \"consequent_product_rank\"\n", "    ].cumsum()\n", "\n", "    # handling Combos - using consequent pids recos\n", "    combo_df = base_combo_df.copy()\n", "    combo_df = combo_df.loc[~combo_df.combo_pid.isin(data.product_id.unique())]\n", "    combo_df_list = (\n", "        combo_df.groupby([\"combo_pid\"])[\"const_pid\"].unique().reset_index(name=\"const_pid_list\")\n", "    )\n", "    combo_df = combo_df.merge(combo_df_list, on=[\"combo_pid\"], how=\"left\")\n", "\n", "    combo_result_df = combo_df.merge(data, left_on=[\"const_pid\"], right_on=[\"product_id\"])\n", "    if len(combo_result_df) > 0:\n", "        combo_result_df[\"to_remove\"] = combo_result_df.apply(\n", "            lambda row: 1 if row.consequent_pid in row.const_pid_list else 0, axis=1\n", "        )\n", "\n", "        combo_result_df[\"source\"] = 3.0\n", "        combo_result_df = combo_result_df.query(\"to_remove == 0\").sort_values(\n", "            [\"city_id\", \"source\", \"scores\"], ascending=[True, True, False]\n", "        )  # combo's constituent pids should not come in recos\n", "        combo_result_df = combo_result_df.drop_duplicates(\n", "            [\"city_id\", \"combo_pid\", \"consequent_pid\"]\n", "        ).reset_index(drop=True)\n", "\n", "        combo_result_df[\"product_id\"] = combo_result_df[\"combo_pid\"]\n", "        combo_result_df.drop(\n", "            columns=[\n", "                \"combo_pid\",\n", "                \"antecedent_product_type\",\n", "                \"antecedent_product_name\",\n", "                \"antecedent_product_type_id\",\n", "                \"const_pid\",\n", "            ],\n", "            inplace=True,\n", "        )\n", "        combo_result_df = pd.merge(\n", "            combo_result_df,\n", "            prod_info_all[[\"product_id\", \"product_name\", \"product_type\", \"product_type_id\"]]\n", "            .drop_duplicates()\n", "            .rename(\n", "                columns={\n", "                    \"product_name\": \"antecedent_product_name\",\n", "                    \"product_type_id\": \"antecedent_product_type_id\",\n", "                    \"product_type\": \"antecedent_product_type\",\n", "                }\n", "            ),\n", "            on=[\"product_id\"],\n", "        ).reset_index(drop=True)\n", "\n", "        data = pd.concat([data, combo_result_df])\n", "\n", "        del combo_result_df\n", "\n", "    data = data.groupby([\"city_id\", \"product_id\"]).head(30)\n", "\n", "    data = data[~data[\"product_id\"].isin(total_exclusion_all_pid + total_exclusion_antecedent_pid)]\n", "    data = data[\n", "        ~data[\"consequent_pid\"].isin(total_exclusion_all_pid + total_exclusion_consequent_pid)\n", "    ]\n", "    data.reset_index(drop=True, inplace=True)\n", "\n", "    data = data.sort_values(\n", "        [\"city_id\", \"rank\", \"source\", \"scores\"], ascending=[True, True, True, False]\n", "    )\n", "    data[\"scores_rank\"] = data.groupby([\"city_id\", \"city_name\", \"product_id\"]).cumcount() + 1\n", "\n", "    data[\"ptype_rank\"] = data.groupby(\n", "        [\"city_id\", \"city_name\", \"product_id\", \"consequent_product_type\"]\n", "    )[\"scores_rank\"].rank(\"min\")\n", "\n", "    data = data.sort_values([\"city_id\", \"ptype_rank\", \"scores_rank\"], ascending=[True, True, True])\n", "    data[\"final_scores_rank\"] = data.groupby([\"city_id\", \"city_name\", \"product_id\"]).cumcount() + 1\n", "\n", "    data = data[req_cols]\n", "\n", "    for city in city_group:\n", "        data_final = data[data[\"city_name\"] == city].reset_index(drop=True)\n", "        print(\n", "            f\"city: {city}, data shape: {data_final.shape}, # unique pids: {data_final.product_id.nunique()}\"\n", "        )\n", "        data_final.to_csv(\"/tmp/final_data.csv\", index=False)\n", "        pb.to_s3(\n", "            \"/tmp/final_data.csv\",\n", "            \"prod-dse-projects\",\n", "            \"recommendations/NPR/exp_a/recommendations/temp_final_data_\" + city + \".csv\",\n", "        )\n", "\n", "        completed_cities[city] = city_dict[city]\n", "        # send to s3\n", "        with open(\"/tmp/completed_cities.pkl\", \"wb\") as file:\n", "            pickle.dump(completed_cities, file)\n", "\n", "        pb.to_s3(\n", "            \"/tmp/completed_cities.pkl\",\n", "            \"prod-dse-projects\",\n", "            \"recommendations/NPR/exp_a/recommendations/completed_cities_final_data.pkl\",\n", "        )\n", "        del data_final\n", "    print(f\"# cities completed: {len(completed_cities)}\")\n", "    print()\n", "    del data\n", "    gc.collect()"]}, {"cell_type": "markdown", "id": "3c63200e-993a-46af-aa48-3099567db54b", "metadata": {}, "source": ["# Make the completed cities dictionary as empty"]}, {"cell_type": "code", "execution_count": null, "id": "882fcc2a-7a56-4069-9181-35bda7ae07fd", "metadata": {}, "outputs": [], "source": ["completed_cities = dict()\n", "with open(\"/tmp/completed_cities.pkl\", \"wb\") as file:\n", "    pickle.dump(completed_cities, file)\n", "pb.to_s3(\n", "    \"/tmp/completed_cities.pkl\",\n", "    \"prod-dse-projects\",\n", "    \"recommendations/NPR/exp_a/recommendations/completed_cities_final_data.pkl\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a7a0ffd2-fb6e-4860-af42-4b4ce58a7cf5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}