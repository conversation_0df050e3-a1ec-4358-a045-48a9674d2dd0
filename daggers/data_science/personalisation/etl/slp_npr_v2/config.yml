alert_configs:
  slack:
  - channel: bl-personalisation-dag-failures
  - channel: bl-personalization-notifications
concurrency: 3
dag_name: slp_npr_v2
dag_type: etl
escalation_priority: low
execution_timeout: 837
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_science
notebooks:
- executor_config:
    load_type: tiny
    node_type: spot
  name: ptype_scores-group-updated-dag
  parameters:
    run_id: '{{ run_id }}'
    task_instance: '{{ task_instance_key_str }}'
  retries: 3
  retry_delay_in_seconds: 15
  tag: level1
- executor_config:
    load_type: very-high-mem
    node_type: spot
  name: model_inference-dag
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level2
- executor_config:
    load_type: very-high-mem
    node_type: spot
  name: post_processing-dag
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  tag: level3
- executor_config:
    load_type: ultra-high-mem
    node_type: spot
  name: data_write-dag
  parameters:
    run_id: '{{ run_id }}'
    task_instance: '{{ task_instance_key_str }}'
  retries: 3
  retry_delay_in_seconds: 15
  tag: level4
owner:
  email: <EMAIL>
  slack_id: U03SV2AN82C
path: data_science/personalisation/etl/slp_npr_v2
paused: false
pool: priority_pool
project_name: personalisation
schedule:
  end_date: '2025-07-13T00:00:00'
  interval: 30 11 * * *
  start_date: '2025-06-23T00:00:00'
schedule_type: fixed
sla: 128 minutes
support_files:
- helper.py
- queries.py
- db_utils.py
tags: []
template_name: multi_notebook
version: 24
