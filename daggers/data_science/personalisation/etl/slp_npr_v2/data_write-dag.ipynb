{"cells": [{"cell_type": "code", "execution_count": null, "id": "21d0451a-b1c8-4ed1-b6a6-5d874c56ea81", "metadata": {}, "outputs": [], "source": ["import os, sys"]}, {"cell_type": "code", "execution_count": null, "id": "d02e69b1-6adc-44c6-807c-2319303c3055", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "4b61d22f-bb3d-486f-b5f7-4422c9b189b3", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "32ed39b3-143e-4c4f-8945-03652621c799", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "import pencilbox as pb\n", "import pytz\n", "from datetime import datetime\n", "import ast\n", "import time\n", "import gc\n", "import helper\n", "import queries\n", "\n", "use_sheet = True"]}, {"cell_type": "code", "execution_count": null, "id": "10dc0de8-55fa-4e44-a8dd-6e20d96d16de", "metadata": {}, "outputs": [], "source": ["import numpy as np"]}, {"cell_type": "code", "execution_count": null, "id": "68968166-1942-4271-b38c-ef745ff760dc", "metadata": {}, "outputs": [], "source": ["date = pd.to_datetime(datetime.now()).date().day\n", "print(date)"]}, {"cell_type": "code", "execution_count": null, "id": "32e4c5df-e6ea-4668-b6ec-ec15b61d45c0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "62243587-a064-4691-b05a-41bb51ba0160", "metadata": {}, "outputs": [], "source": ["# columns = [\n", "#     \"antecedent_product_type\",\n", "#     \"consequent_product_type\",\n", "#     \"rank\",\n", "#     \"product_id\",\n", "#     \"consequent_pid\",\n", "#     \"scores\",\n", "#     \"source\",\n", "#     \"antecedent_product_name\",\n", "#     \"antecedent_product_type_id\",\n", "#     \"consequent_product_name\",\n", "#     \"consequent_product_type_id\",\n", "#     \"city_id\",\n", "#     \"city_name\",\n", "#     \"final_rank\",\n", "#     \"consequent_product_rank\",\n", "# ]\n", "req_cols = [\n", "    \"city_id\",\n", "    \"city_name\",\n", "    \"product_id\",\n", "    \"consequent_pid\",\n", "    \"consequent_product_type\",\n", "    \"scores\",\n", "    \"rank\",\n", "    \"source\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "b897189b-52e4-44d1-8447-592cd40c4a1f", "metadata": {}, "outputs": [], "source": ["city_id_name_mapping = helper.get_city_id_name_data()\n", "city_dict = dict(city_id_name_mapping.values)\n", "city_dict"]}, {"cell_type": "code", "execution_count": null, "id": "0f0b9da3-c88d-489b-87ea-6f2bd1f9b692", "metadata": {}, "outputs": [], "source": ["final_data = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "id": "44a392ba-329d-418c-9f06-57b285a41822", "metadata": {"tags": []}, "outputs": [], "source": ["final_data_list = []\n", "for city in city_dict.keys():\n", "    print(city)\n", "    try:\n", "        pb.from_s3(\n", "            \"prod-dse-projects\",\n", "            \"recommendations/NPR/exp_a/recommendations/temp_final_data_\" + city + \".csv\",\n", "            \"/tmp/final_data_\" + city + \".csv\",\n", "        )\n", "        data = pd.read_csv(\"/tmp/final_data_\" + city + \".csv\")\n", "        if data.shape[0] == 0:\n", "            print(\"empty df from prev NB\")\n", "            continue\n", "\n", "        print(data.shape, data.product_id.nunique())\n", "        final_data_list.append(data)\n", "    except:\n", "        print(\"no data from prev NB\")\n", "        continue\n", "\n", "    data = []\n", "    del data"]}, {"cell_type": "code", "execution_count": null, "id": "f38f5f88-b09c-4402-810c-dbffbce33e2a", "metadata": {}, "outputs": [], "source": ["if final_data_list:\n", "    final_data = pd.concat(final_data_list, ignore_index=True)\n", "else:\n", "    final_data = pd.DataFrame()\n", "\n", "# Clean up\n", "del final_data_list\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "53da6c10-c88d-4ecf-8490-7c9917d23e9f", "metadata": {}, "outputs": [], "source": ["print(final_data.shape, final_data.city_id.nunique())"]}, {"cell_type": "code", "execution_count": null, "id": "9c3e8a7d-0f97-4112-b34a-166a0f66edeb", "metadata": {}, "outputs": [], "source": ["final_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6a9dea99-6d48-4aae-86d9-3dbbddfb170d", "metadata": {}, "outputs": [], "source": ["final_data.query(\"city_id == 3 and product_id == 1\")"]}, {"cell_type": "code", "execution_count": null, "id": "bb4971f1-995f-4d44-a4b2-e2108b670389", "metadata": {}, "outputs": [], "source": ["# pan india fallback\n", "fallback_data = final_data.query(\"city_id == 1\")\n", "fallback_data[\"city_id\"] = 0\n", "fallback_data[\"city_name\"] = \"India\""]}, {"cell_type": "code", "execution_count": null, "id": "c78c2bd0-17ef-41d8-b166-34a70386320b", "metadata": {}, "outputs": [], "source": ["final_data = pd.concat([final_data, fallback_data]).reset_index(drop=True)\n", "\n", "fallback_data = []\n", "del fallback_data\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "f931ee20-906f-4375-983c-0b64068b4be6", "metadata": {}, "outputs": [], "source": ["final_data.shape, final_data.product_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "81108f18-2dbe-4344-81bc-cd9b9d9d8558", "metadata": {}, "outputs": [], "source": ["final_data.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "3d82209b-016e-453f-834d-23870ad56c9d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a6db800c-5886-4237-95ec-97b05aaa9600", "metadata": {}, "outputs": [], "source": ["final_data.city_id.unique()"]}, {"cell_type": "code", "execution_count": null, "id": "546edade-64fc-45d5-952e-e4d76b1fcd34", "metadata": {}, "outputs": [], "source": ["final_data.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "d6fdc0eb-d0ff-4848-b45a-77b8a2428509", "metadata": {}, "outputs": [], "source": ["final_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "90d09737-e434-4797-ae41-8a54a9479c99", "metadata": {}, "outputs": [], "source": ["final_data.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "262ceedb-7156-4737-9cc0-1a9100f53d76", "metadata": {}, "outputs": [], "source": ["final_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "74fc883b-9716-4681-88a8-1dbf3a25c6c7", "metadata": {}, "outputs": [], "source": ["# final_data[\"rank\"] = final_data[\"rank\"].astype(\"int\")\n", "final_data[\"ptype_rank\"] = final_data.ptype_rank.astype(\"int\")\n", "final_data[\"consequent_pid\"] = final_data.consequent_pid.astype(\"int\")\n", "final_data[\"product_id\"] = final_data.product_id.astype(\"int\")\n", "# final_data[\"antecedent_product_type_id\"] = final_data.antecedent_product_type_id.astype(\"int\")\n", "# final_data[\"consequent_product_type_id\"] = final_data.consequent_product_type_id.astype(\"int\")\n", "final_data[\"city_id\"] = final_data.city_id.astype(\"int\")\n", "# final_data[\"final_rank\"] = final_data.final_rank.astype(\"int\")\n", "# final_data[\"consequent_product_rank\"] = final_data.consequent_product_rank.astype(\"int\")\n", "# final_data[\"source\"] = final_data.source.astype(\"int\")"]}, {"cell_type": "code", "execution_count": null, "id": "e394e85c-08bf-4dae-a372-0548ea1d3768", "metadata": {}, "outputs": [], "source": ["s3_bucket = \"prod-dse-projects\"\n", "group_rules_local_path = \"df_bistro_temp_final_city_ant_reco_final.csv\"\n", "pb.from_s3(\n", "    s3_bucket,\n", "    \"A<PERSON><PERSON><PERSON>_<PERSON>/bistro/npr/complemntary_reco/df_bistro_temp_final_city_ant_reco_final.csv\",\n", "    group_rules_local_path,\n", ")\n", "npr_bistro_data = pd.read_csv(group_rules_local_path)\n", "npr_bistro_data[\"source\"] = 3\n", "npr_bistro_data = npr_bistro_data[final_data.columns]"]}, {"cell_type": "code", "execution_count": null, "id": "99e3a8ba-4d2b-4820-9aad-8cf0d8163845", "metadata": {}, "outputs": [], "source": ["# Check if columns and data types are the same\n", "def check_columns_and_dtypes(df1, df2):\n", "    if list(df1.columns) == list(df2.columns) and all(df1.dtypes == df2.dtypes):\n", "        return True\n", "    else:\n", "        return False\n", "\n", "\n", "# Assuming df1 and df2 are your two DataFrames\n", "if check_columns_and_dtypes(final_data, npr_bistro_data):\n", "    print(\"NPR_Bistro data got appended\")\n", "    # Append the DataFrames\n", "    final_data = pd.concat([final_data, npr_bistro_data], ignore_index=True)\n", "\n", "npr_bistro_data = []\n", "del npr_bistro_data\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "10510504-f387-48b3-bebe-cff2f3521ff9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "56963213-9122-4d34-bad9-df10b4b370f1", "metadata": {}, "outputs": [], "source": ["s3_bucket = \"prod-dse-projects\"\n", "group_rules_local_path = \"df_digital_goods_temp_final_city_ant_reco_final.csv\"\n", "pb.from_s3(\n", "    s3_bucket,\n", "    \"Ab<PERSON><PERSON>_<PERSON>/digital_goods/npr/complemntary_reco/df_digital_goods_temp_final_city_ant_reco_final.csv\",\n", "    group_rules_local_path,\n", ")\n", "npr_digital_goods = pd.read_csv(group_rules_local_path)\n", "npr_digital_goods[\"source\"] = 3\n", "npr_digital_goods = npr_digital_goods[final_data.columns]"]}, {"cell_type": "code", "execution_count": null, "id": "dc7229dd-9627-4742-b7c8-ff4d2fac6b04", "metadata": {}, "outputs": [], "source": ["# Check if columns and data types are the same\n", "def check_columns_and_dtypes(df1, df2):\n", "    if list(df1.columns) == list(df2.columns) and all(df1.dtypes == df2.dtypes):\n", "        return True\n", "    else:\n", "        return False\n", "\n", "\n", "# Assuming df1 and df2 are your two DataFrames\n", "if check_columns_and_dtypes(final_data, npr_digital_goods):\n", "    print(\"NPR_digital_goods data got appended\")\n", "    # Append the DataFrames\n", "    final_data = pd.concat([final_data, npr_digital_goods], ignore_index=True)\n", "\n", "npr_digital_goods = []\n", "del npr_digital_goods\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "8b8674f2-6167-485d-9584-76e47aeee822", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "07f43a68-c900-481c-aae2-cf11ab50ba03", "metadata": {}, "outputs": [], "source": ["final_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "fd3f2518-efb9-4000-bc45-ad6bb261556f", "metadata": {}, "outputs": [], "source": ["final_data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "952fc6fd-f9a1-46f5-8b49-bf88d42ae0cf", "metadata": {}, "outputs": [], "source": ["final_data.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "2b9e7bba-f8a1-4b20-b679-069f28a0cad1", "metadata": {}, "outputs": [], "source": ["IST_TZ = pytz.timezone(\"Asia/Kolkata\")\n", "final_data[\"updated_at\"] = datetime.now(IST_TZ).date()"]}, {"cell_type": "code", "execution_count": null, "id": "d381b95f-8fd5-48bd-8606-13054ff021ef", "metadata": {}, "outputs": [], "source": ["type(final_data[\"updated_at\"][0])"]}, {"cell_type": "code", "execution_count": null, "id": "0eec76f4-3067-4af1-9269-a3f52be6551d", "metadata": {}, "outputs": [], "source": ["final_data.query(\"product_id ==539100  and city_id==3\").shape"]}, {"cell_type": "code", "execution_count": null, "id": "fc7f8165-c09f-46fc-815b-e0be54a9d007", "metadata": {}, "outputs": [], "source": ["# kwargs = helper.get_redshift_table_kwargs(\n", "#     \"complimentary_product_recommendations_exp_a\", table=\"main\"\n", "# )\n", "# kwargs"]}, {"cell_type": "code", "execution_count": null, "id": "942f6226-af28-4f84-8b45-95867dbdd986", "metadata": {}, "outputs": [], "source": ["final_data.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "256e5a92-d6a1-4583-9a8f-64f59497aaf9", "metadata": {}, "outputs": [], "source": ["def get_redshift_table_kwargs_1(table_name, table=\"main\"):\n", "    if table == \"main\":\n", "        date_col_dtype = \"DATE\"\n", "    else:\n", "        date_col_dtype = \"VARCHAR\"\n", "    return {\n", "        \"schema_name\": \"consumer_intelligence_etls\",\n", "        \"table_name\": table_name,\n", "        \"column_dtypes\": [\n", "            {\"name\": \"city_id\", \"type\": \"INTEGER\", \"description\": \"city id\"},\n", "            {\"name\": \"city_name\", \"type\": \"VARCHAR\", \"description\": \"city name\"},\n", "            {\"name\": \"product_id\", \"type\": \"INTEGER\", \"description\": \"product id\"},\n", "            {\n", "                \"name\": \"consequent_pid\",\n", "                \"type\": \"INTEGER\",\n", "                \"description\": \"recommended product id\",\n", "            },\n", "            {\"name\": \"ptype_rank\", \"type\": \"INTEGER\", \"description\": \"ptype spacing\"},\n", "            {\n", "                \"name\": \"scores\",\n", "                \"type\": \"REAL\",\n", "                \"description\": \"recommended product scores\",\n", "            },\n", "            {\n", "                \"name\": \"final_scores_rank\",\n", "                \"type\": \"INTEGER\",\n", "                \"description\": \"final rank of products\",\n", "            },\n", "            {\n", "                \"name\": \"updated_at\",\n", "                \"type\": date_col_dtype,\n", "                \"description\": \"update date of table rebuild\",\n", "            },\n", "        ],\n", "        \"sortkey\": [\"city_id\", \"product_id\"],\n", "        \"partition_key\": [\"city_id\"],\n", "        \"load_type\": \"partition_overwrite\",  # append, overwrite, truncate or upsert,\n", "        \"table_description\": \"Next product recommendations\",\n", "    }"]}, {"cell_type": "code", "execution_count": null, "id": "c802a04e-44af-4069-82ea-240c83207552", "metadata": {}, "outputs": [], "source": ["# kwargs = helper.get_redshift_table_kwargs(\n", "#     \"complimentary_product_recommendations_exp_a\", table=\"main\"\n", "# )\n", "# kwargs"]}, {"cell_type": "code", "execution_count": null, "id": "371b4040-7372-495a-9a59-99295b720603", "metadata": {}, "outputs": [], "source": ["kwargs = get_redshift_table_kwargs_1(\"complimentary_product_recommendations_exp_a_1\", table=\"main\")\n", "kwargs"]}, {"cell_type": "code", "execution_count": null, "id": "fe9035fa-785e-4189-9c8b-c9de28d66e34", "metadata": {}, "outputs": [], "source": ["final_data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "3715fe2f-6b2d-4023-96ca-574ca1a6446b", "metadata": {}, "outputs": [], "source": ["pb.to_trino(final_data, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "ac8830f8-e0fe-45e6-84e4-f3e7f56d4b68", "metadata": {}, "outputs": [], "source": ["final_data.head()"]}, {"cell_type": "markdown", "id": "64ec3e93-edbe-4654-af6b-891c0c52d1cb", "metadata": {}, "source": ["## Espina db data push"]}, {"cell_type": "code", "execution_count": null, "id": "65f9f4cc-2cc9-43db-9853-acd24a6c6982", "metadata": {}, "outputs": [], "source": ["import asyncio\n", "import concurrent.futures\n", "import datetime as dt\n", "import os\n", "import threading\n", "import time\n", "from contextlib import contextmanager\n", "from datetime import datetime\n", "\n", "import boto3\n", "import numpy as np\n", "import pandas\n", "import pandas as pd\n", "\n", "import pencilbox as pb\n", "import pytz\n", "import sqlalchemy as sqla\n", "from sqlalchemy.dialects import postgresql\n", "from sqlalchemy.ext.automap import automap_base\n", "from sqlalchemy.orm import sessionmaker"]}, {"cell_type": "code", "execution_count": null, "id": "ed3c85f0-ea50-468c-b290-0b3a1ae62e8a", "metadata": {}, "outputs": [], "source": ["import db_utils"]}, {"cell_type": "code", "execution_count": null, "id": "9ca6cb88-ccb6-4557-9e40-718e44ca26cc", "metadata": {}, "outputs": [], "source": ["def get_espina_db_connection():\n", "    dse_db_conn_params = pb.get_secret(\"dse/postgres/dse_db/airflow_etl_user\")\n", "    engine_url = dse_db_conn_params[\"uri\"]\n", "    # engine_url = dse_db_conn_params[\"uri_new\"]\n", "    engine = sqla.create_engine(engine_url, pool_size=1, echo=False)\n", "    return engine"]}, {"cell_type": "code", "execution_count": null, "id": "0142ef05-3c3e-4cfd-8293-a4bfc30d3513", "metadata": {}, "outputs": [], "source": ["espina_db_connection = get_espina_db_connection()"]}, {"cell_type": "code", "execution_count": null, "id": "3975c407-49bb-40c2-ad56-7d0676856d27", "metadata": {}, "outputs": [], "source": ["table_name_edb = \"complimentary_product_recommendations_v8\"\n", "staging_table_name_edb = \"complimentary_product_recommendations_v8_staging\"\n", "index_name = \"idx\"\n", "index_columns = [\"city_id\", \"product_id\", \"product_scores_rank\"]"]}, {"cell_type": "code", "execution_count": null, "id": "36e41705-bea4-4878-8ac2-29e2f7e6a1fd", "metadata": {}, "outputs": [], "source": ["final_data = final_data.drop_duplicates(subset=[\"city_id\", \"product_id\", \"consequent_pid\"])\n", "final_data.reset_index(drop=True, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "f66281cb-4672-4576-8c74-da76201e32ef", "metadata": {}, "outputs": [], "source": ["final_data.rename(\n", "    columns={\n", "        # \"rank\": \"ptype_rank\",\n", "        \"scores\": \"product_scores\",\n", "        \"final_scores_rank\": \"product_scores_rank\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "df2ff8e5-4441-42f2-a58f-81aab38ef<PERSON>e", "metadata": {}, "outputs": [], "source": ["final_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "1a3edaa9-1e3c-4ba4-b3dc-37390a29c93a", "metadata": {}, "outputs": [], "source": ["final_data = final_data[\n", "    [\n", "        \"city_id\",\n", "        \"city_name\",\n", "        \"product_id\",\n", "        \"consequent_pid\",\n", "        \"ptype_rank\",\n", "        \"product_scores\",\n", "        \"product_scores_rank\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "3940a7dd-0ccb-409a-8a2d-a2f6d8de1873", "metadata": {}, "outputs": [], "source": ["final_data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "dbeca679-c5ec-4439-8f4f-f516ea138e19", "metadata": {}, "outputs": [], "source": ["final_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "290eae7e-e233-421f-b0f3-9290a7626ff4", "metadata": {}, "outputs": [], "source": ["final_data.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "8ffc0f6c-e8db-400a-b444-e7851d8cccbf", "metadata": {"tags": []}, "outputs": [], "source": ["final_data[\"city_id\"] = final_data.city_id.astype(\"int\")\n", "# final_data['city_name'] = final_data.city_name.astype('string')\n", "final_data[\"product_id\"] = final_data.product_id.astype(\"int\")\n", "final_data[\"consequent_pid\"] = final_data.consequent_pid.astype(\"int\")\n", "final_data[\"ptype_rank\"] = final_data.ptype_rank.astype(\"int\")\n", "final_data[\"product_scores\"] = final_data.product_scores.astype(\"float\")\n", "final_data[\"product_scores_rank\"] = final_data.product_scores_rank.astype(\"int\")"]}, {"cell_type": "code", "execution_count": null, "id": "b4d9c929-0f24-42f3-84f5-2d94c5247b3a", "metadata": {}, "outputs": [], "source": ["final_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "4481537d-dae9-4ab9-9fb8-1484b92cf153", "metadata": {}, "outputs": [], "source": ["final_data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "7e3d1402-1be3-41fd-8a1c-6f09baf2fcfd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cd8810e8-425c-47d8-959b-b0212df85cf5", "metadata": {}, "outputs": [], "source": ["final_data.groupby([\"city_id\", \"product_id\"])[\n", "    \"consequent_pid\"\n", "].nunique().reset_index().consequent_pid.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "6eb6b09c-8eae-482e-8522-25f463657205", "metadata": {}, "outputs": [], "source": ["final_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bfb9ec36-3aad-4e0e-9103-95cf9cb1a253", "metadata": {}, "outputs": [], "source": ["staging_table_name_edb, table_name_edb"]}, {"cell_type": "code", "execution_count": null, "id": "be9456e3-fec6-45b2-898f-c34db5b22065", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ab0ce704-347a-48c2-929c-2999440cda7b", "metadata": {}, "outputs": [], "source": ["TABLE_CREATION_QUERY = f\"\"\"\n", "CREATE TABLE IF NOT EXISTS {staging_table_name_edb}\n", "(\n", "    city_id int,\n", "    city_name varchar,\n", "    product_id bigint,\n", "    consequent_pid bigint,\n", "    ptype_rank bigint,\n", "    product_scores float,\n", "    product_scores_rank int,\n", "    CONSTRAINT {staging_table_name_edb}_pk PRIMARY KEY (city_id, product_id, consequent_pid)\n", ");\n", "\n", "CREATE TABLE IF NOT EXISTS {table_name_edb}\n", "(\n", "    city_id int,\n", "    city_name varchar,\n", "    product_id bigint,\n", "    consequent_pid bigint,\n", "    ptype_rank bigint,\n", "    product_scores float,\n", "    product_scores_rank int,\n", "    CONSTRAINT {table_name_edb}_pk PRIMARY KEY (city_id, product_id, consequent_pid)\n", "    \n", ");\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "c109309f-2412-47aa-8278-8892a60335ef", "metadata": {}, "outputs": [], "source": ["# TABLE_CREATION_QUERY = f\"\"\"ALTER TABLE {table_name_edb} ADD product_scores_rank int \"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "e00e94d0-51dd-4f59-937e-4a04c18c8518", "metadata": {}, "outputs": [], "source": ["# print(TABLE_CREATION_QUERY)\n", "with espina_db_connection.begin() as connection:\n", "    connection.execute(TABLE_CREATION_QUERY)"]}, {"cell_type": "code", "execution_count": null, "id": "849783c4-54fa-40ce-bbe4-1b774f428b5c", "metadata": {}, "outputs": [], "source": ["db_utils.push_to_database(\n", "    table_name_edb=table_name_edb,\n", "    staging_table_name_edb=staging_table_name_edb,\n", "    dataframe=final_data,\n", "    index_name=index_name,\n", "    index_columns=index_columns,\n", "    dry_run=False,\n", ")\n", "# alerting TODO"]}, {"cell_type": "markdown", "id": "48c46f71-0ae3-4e74-887f-df645d8f9b13", "metadata": {}, "source": ["**NEW TABLE**"]}, {"cell_type": "code", "execution_count": null, "id": "29618bad-f319-41f4-bb7d-eac8147f34fe", "metadata": {}, "outputs": [], "source": ["# Step 1: Sort before grouping\n", "sorted_df = final_data.sort_values(by=[\"city_id\", \"product_id\", \"product_scores_rank\"])\n", "\n", "# Step 2: Group and aggregate\n", "transformed_df = (\n", "    sorted_df.groupby([\"city_id\", \"product_id\"])[\"consequent_pid\"].apply(list).reset_index()\n", ")\n", "\n", "# Step 3: Add timestamps and flags\n", "IST_TZ = pytz.timezone(\"Asia/Kolkata\")\n", "current_ts = int(datetime.now(IST_TZ).timestamp())\n", "transformed_df[\"created_at\"] = current_ts\n", "transformed_df[\"updated_at\"] = current_ts\n", "transformed_df[\"is_enabled\"] = True\n", "\n", "# Step 4: <PERSON>ame column for schema\n", "transformed_df.rename(columns={\"consequent_pid\": \"consequent_pids\"}, inplace=True)\n", "transformed_df[\"consequent_pids\"] = transformed_df[\"consequent_pids\"].apply(\n", "    lambda x: \"{\" + \",\".join(map(str, x)) + \"}\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0e5d9525-abb7-4abf-8df1-d5b9b71a269e", "metadata": {}, "outputs": [], "source": ["table_name_edb = \"next_product_recommendations\"\n", "staging_table_name_edb = \"next_product_recommendations_staging\"\n", "index_name = \"idx\"\n", "index_columns = []"]}, {"cell_type": "code", "execution_count": null, "id": "26358e01-b895-48a7-8657-9f8bf78943ee", "metadata": {}, "outputs": [], "source": ["db_utils.push_to_database(\n", "    table_name_edb=table_name_edb,\n", "    staging_table_name_edb=staging_table_name_edb,\n", "    dataframe=transformed_df,\n", "    index_name=index_name,\n", "    index_columns=index_columns,\n", "    dry_run=False,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "111c9ee8-f885-4263-99d5-96a8cff6e10e", "metadata": {}, "outputs": [], "source": ["# ANALYZE_RENAME_SWAP_QUERY = f\"\"\"\n", "#     ANALYZE {staging_table_name_edb};\n", "#     ALTER TABLE {table_name_edb} RENAME TO _tmp_{table_name_edb};\n", "#     ALTER TABLE {staging_table_name_edb} RENAME TO {table_name_edb};\n", "#     ALTER TABLE _tmp_{table_name_edb} RENAME TO {staging_table_name_edb};\n", "#     DROP INDEX IF EXISTS {index_name}_{table_name_edb};\n", "#     ALTER INDEX {index_name}_{staging_table_name_edb} RENAME TO {index_name}_{table_name_edb};\n", "#     \"\"\"\n", "# with espina_db_connection.begin() as connection:\n", "#     connection.execute(ANALYZE_RENAME_SWAP_QUERY)"]}, {"cell_type": "code", "execution_count": null, "id": "a18f089e-e88f-465c-8ca8-c51a9b6b14c4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "806636f0-0bf0-4fa5-ab56-873f83b49923", "metadata": {}, "source": ["### Datadog "]}, {"cell_type": "code", "execution_count": null, "id": "cdf38be1-0596-4361-a2d4-59d9ef0a9855", "metadata": {}, "outputs": [], "source": ["dag_id = task_instance.split(\"__\")[0]\n", "task_id = task_instance.split(\"__\")[1]"]}, {"cell_type": "code", "execution_count": null, "id": "bf2312c1-f3c5-48ce-a045-3457357f3aaf", "metadata": {}, "outputs": [], "source": ["!pip install -q datadog\n", "import enum\n", "import threading\n", "import pencilbox as pb\n", "from datadog import initialize, api\n", "\n", "\n", "class EventAlertType(enum.Enum):\n", "    Info = \"info\"\n", "    Warning = \"warning\"\n", "    Error = \"error\"\n", "    Success = \"success\"\n", "\n", "\n", "class EventPriority(enum.Enum):\n", "    Low = \"low\"\n", "    Normal = \"normal\"\n", "\n", "\n", "class DatadogPublishEvent:\n", "    _instance = None\n", "    _lock = threading.Lock()  # Class-level lock for thread safety\n", "\n", "    def __new__(cls, *args, **kwargs):\n", "        \"\"\"Ensure only one instance of the class is created (<PERSON><PERSON>).\"\"\"\n", "        if cls._instance is None:\n", "            with cls._lock:  # Ensure only one thread can create the instance\n", "                if cls._instance is None:  # Double-checked locking pattern\n", "                    cls._instance = super(DatadogPublishEvent, cls).__new__(cls)\n", "        return cls._instance\n", "\n", "    def __init__(self):\n", "        \"\"\"Initialize Datadog only once.\"\"\"\n", "        if not hasattr(self, \"initialized\"):\n", "            try:\n", "                self.options = {\n", "                    \"api_key\": pb.get_secret(\"dse/personalisation/datadog\")[\"api_key\"],\n", "                }\n", "                initialize(**self.options)\n", "                self.initialized = True  # Mark as initialized to avoid re-initialization\n", "            except Exception as e:\n", "                print(f\"Failed to initialize DatadogPublishEvent, error: {str(e)}\")\n", "\n", "    @staticmethod\n", "    def publish_event(\n", "        dag_id: str,\n", "        task_id: str,\n", "        run_id: str,\n", "        task_status,\n", "        message: str,\n", "        tags: list = None,\n", "        priority: EventPriority = EventPriority.Normal,\n", "        alert_type: EventAlertType = EventAlertType.Info,\n", "    ):\n", "        try:\n", "            title = f\"AIRFLOW_{dag_id}_{task_id}_{task_status}\"\n", "            aggregation_key = f\"{dag_id}_{run_id}\"\n", "            if not tags or len(tags) == 0:\n", "                tags = [\"environment:production\", \"source:airflow\", f\"dag_id: {dag_id}\"]\n", "\n", "            res = api.Event.create(\n", "                title=title,\n", "                text=message,\n", "                aggregation_key=aggregation_key,\n", "                priority=priority.value,\n", "                alert_type=alert_type.value,\n", "                service=dag_id,\n", "                tags=tags,\n", "            )\n", "            print(res)\n", "        except Exception as e:\n", "            print(\n", "                f\"Failed to publish datadog event, dag_id: {dag_id}, task_id: {task_id},run_id: {run_id}, task_status: {task_status} \"\n", "                f\"error: {str(e)}\"\n", "            )"]}, {"cell_type": "code", "execution_count": null, "id": "7f10bb88-5b3e-42df-a6c0-c8c64a434873", "metadata": {}, "outputs": [], "source": ["DatadogPublishEvent().publish_event(\n", "    dag_id=dag_id,\n", "    task_id=task_id,\n", "    run_id=run_id,\n", "    task_status=\"SUCCESS\",\n", "    message=\"..... dag ran successfully\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d99e4ee0-629e-4645-81d2-74b2aa56212e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}