import pandas as pd
import pencilbox as pb
import numpy as np

def get_active_city_query():
    query = f"""select city_name, city_id from dwh.dim_merchant where is_current=True and chain_id = 1383 and city_name not in ('Not in service area') group by 1,2"""
    return query

def get_product_group_id_query():
    query = f"""select gpm.product_id, gpm.group_id 
    from lake_cms.gr_group_product_mapping gpm
    join lake_cms.gr_group gr on gpm.group_id = gr.id and gr.is_active
    where gpm.enabled_flag=True and gpm.lake_active_record=true group by 1,2"""
    return query

def get_imported_pid_query():
    query = f"""
    select gpam.product_id
    from cms.gr_product_attribute_mapping as gpam
    where gpam.attribute_id = 890
    and gpam.value in ('Imported','Premium')
    and gpam.lake_active_record
    union
    select product_id from cms.gr_product_tag_mapping where tag_id=14960"""
    return query

def get_affluent_pid_query():
    query = f"""
    WITH ipm AS (
  SELECT 
    DISTINCT ipr.product_id, 
    CASE WHEN ipr.item_id IS NULL THEN ipom_0.item_id ELSE ipr.item_id END AS item_id, 
    CASE WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier, 1) ELSE COALESCE(ipom_0.multiplier, 1) END AS multiplier 
  FROM 
    lake_rpc.item_product_mapping ipr 
    LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipr.product_id = ipom.product_id 
    AND ipr.item_id = ipom.item_id 
    LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipr.product_id = ipom_0.product_id
)
select product_id 
from consumer_intelligence_etls.personalisation_item_affluence_v2 pid_aff
join ipm on ipm.item_id = pid_aff.item_id
where item_affluence_bucket = 5
group by 1
    """
    return query

def get_city_pid_cart_count_query():
    query = f"""
    select
    product_type,
    fsoid.product_id,
    fsoid.city_name,
    count(distinct cart_id) as cart_count
    from dwh.fact_sales_order_item_details fsoid
    join dwh.dim_product dp on dp.product_id = fsoid.product_id
    where order_create_dt_ist > current_date - INTERVAL '30' DAY
    and dp.is_current and dp.is_product_enabled
    group by 1,2,3
    """
    return query

def get_pid_cart_count_query():
    query = f"""
    select
    product_type,
    fsoid.product_id,
    count(distinct cart_id) as cart_count
    from dwh.fact_sales_order_item_details fsoid
    join dwh.dim_product dp on dp.product_id = fsoid.product_id
    where order_create_dt_ist > current_date - INTERVAL '7' DAY
    and dp.is_current and dp.is_product_enabled
    group by 1,2
    """
    return query

def get_city_books_l2_popularity_query():
    return f"""select
    city_name,
    l2_category,
    product_type,
    dp.product_id as consequent_pid,
    dp.product_name as consequent_product_name,
    count(distinct cart_id) as cart_count
    from dwh.fact_sales_order_item_details fsoid
    join dwh.dim_product dp on dp.product_id = fsoid.product_id
    where order_create_dt_ist > current_date - INTERVAL '120' DAY
    and dp.is_current and dp.is_product_enabled
    and product_type in ('Book','Magazine')
    group by 1,2,3,4,5
    order by 6 desc
    """

def get_books_l2_popularity_query():
    return f"""select
    l2_category,
    product_type,
    dp.product_id as consequent_pid,
    dp.product_name as consequent_product_name,
    count(distinct cart_id) as cart_count
    from dwh.fact_sales_order_item_details fsoid
    join dwh.dim_product dp on dp.product_id = fsoid.product_id
    where order_create_dt_ist > current_date - INTERVAL '60' DAY
    and dp.is_current and dp.is_product_enabled
    and product_type in ('Book','Magazine')
    group by 1,2,3,4
    order by 5 desc
    """


def get_active_pids_query():
    query = """
    with weekly_inv as (
        select city_id, try_cast("mp#product_id" as bigint) product_id, true had_weekly_inv
        from dynamodb.blinkit_projection_inventory_service_v2 il
        join dwh.dim_merchant dm on dm.merchant_id = try_cast(il."mp#merchant_id" as bigint)
        and dm.is_current
        where at_date between cast(current_date - interval '7' day as varchar) and cast(current_date as varchar)
        group by 1,2
    ),
    base_table as  (
        select
            dm.city_id as city_id,
            dm.city_name as city_name,
            fmpm.product_id,
            sum(inv.quantity) quantity
        from
            lake_cms.gr_merchant_product_mapping fmpm
            join dwh.dim_merchant dm on (
                fmpm.merchant_id = dm.merchant_id
                and is_current
            )
            join zomato.dynamodb.blinkit_source_pricing_v2 pdp on (
                pdp.merchant_id = fmpm.merchant_id
                AND fmpm.product_id = pdp.product_id
            )
            left join dynamodb.blinkit_projection_inventory_service_snapshot inv on try_cast(inv.merchant_id as bigint) = fmpm.merchant_id
            and try_cast(inv.product_id as bigint) = fmpm.product_id
        where
            (
                fmpm.enabled_flag = True
                and pdp.price IS not null
            )
        group by
            1,
            2,
            3
        )
    select b.city_id, b.city_name, b.product_id from base_table b
    left join weekly_inv wi on wi.city_id = b.city_id and wi.product_id = b.product_id
    where (had_weekly_inv or quantity > 0)
    """
    return query


def get_combo_query():
    query = f"""
    with dp as
              (select *
               from dwh.dim_product
               where is_current and is_product_enabled),

    combo as
              (select dp.product_id,
                      coalesce(base_id, dp.product_id) pid,
                      case when free_count = 1 then coalesce(obpm.count, 0) else coalesce(obpm.count, 1) end units
               from dp
               left join cms.offer_base_product_mappings obpm on obpm.offer_id = dp.product_id
               and insert_ds_ist is not null)
    select combo.pid as const_pid,
           combo.product_id as combo_pid
               from combo
               join dp on dp.product_id = combo.product_id and dp.product_type = 'Combo'
               group by 1,2
    """
    return query


