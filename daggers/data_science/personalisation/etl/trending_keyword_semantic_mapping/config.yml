alert_configs:
  slack:
  - channel: bl-personalisation-dag-failures
dag_name: trending_keyword_semantic_mapping
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: high-cpu
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
owner:
  email: <EMAIL>
  slack_id: U07AD773UJZ
path: data_science/personalisation/etl/trending_keyword_semantic_mapping
paused: false
pool: data_science_pool
project_name: personalisation
schedule:
  end_date: '2025-09-14T00:00:00'
  interval: 30 0 * * *
  start_date: '2025-04-22T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- Queries/*
- src/models/autocomplete.py
- src/models/custom_pecos.py
- src/models/custom_pecos_v2.py
- src/models/optim_sentence_transformers.py
- src/models/query_preprocessor.py
- src/models/spell_autocomplete.py
- src/models/tokenization.py
- NER_Model_importer.py
- utils.py
tags: []
template_name: notebook
version: 30
