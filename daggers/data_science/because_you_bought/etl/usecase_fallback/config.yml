alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
  - channel: bl-personalization-notifications
catchup: false
dag_name: usecase_fallback
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
  retries: 3
  retry_delay_in_seconds: 5
owner:
  email: <EMAIL>
  slack_id: U03RQJY8HAT
path: data_science/because_you_bought/etl/usecase_fallback
paused: false
pool: data_science_pool
project_name: because_you_bought
schedule:
  end_date: '2025-09-01T00:00:00'
  interval: 30 1 * * *
  start_date: '2024-11-20T00:00:00'
schedule_type: fixed
sla: 60 minutes
support_files: []
tags: []
template_name: notebook
version: 4
