{"cells": [{"cell_type": "code", "execution_count": null, "id": "1f7f7014-b1a9-4561-bd9b-8f671f9998f7", "metadata": {"tags": []}, "outputs": [], "source": ["!pip install -q scipy==1.8.0\n", "!pip install -q pandas==1.5.1\n", "!pip install -q lightfm==1.16\n", "!pip install -q kafka-python==2.0.2\n", "!pip install -q scipy==1.8.0\n", "!pip install -q aiohttp==3.8.1\n", "!pip install -q fsspec==2023.1.0\n", "!pip install -q aiobotocore==2.4.2\n", "!pip install -q pymysql==1.0.2\n", "!pip install -q gremlinpython==3.6.2\n", "!pip install -q botocore==1.27.59\n", "!pip install -q progressbar2==4.2.0\n", "!pip install -q backoff==2.2.1\n", "!pip install -q pandas==1.5.1\n", "!pip install -q pg8000==1.29.4\n", "!pip install -q opensearch-py==2.1.1\n", "!pip install -q boto3==1.24.59\n", "!pip install -q requests-aws4auth==1.2.2\n", "!pip install -q s3transfer==0.6.0\n", "!pip install -q aenum==3.1.11\n", "!pip install -q scramp==1.4.4\n", "!pip install -q python-utils==3.5.2\n", "!pip install -q awswrangler==2.19.0\n", "!pip install -q s3fs==2023.1.0\n", "!pip install -q gzip\n", "!pip install -q scikit-learn\n", "from sklearn.preprocessing import QuantileTransformer"]}, {"cell_type": "code", "execution_count": null, "id": "80e7203a-cb15-46dc-9031-b99baf7cb5b9", "metadata": {}, "outputs": [], "source": ["import time\n", "import pytz\n", "import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import os\n", "import math\n", "import sys\n", "from datetime import date, datetime, timedelta\n", "import gc\n", "import importlib\n", "from tqdm import tqdm\n", "from itertools import zip_longest\n", "\n", "tqdm.pandas()\n", "import gzip\n", "import json\n", "import requests\n", "import multiprocessing as mp\n", "\n", "pd.options.display.max_colwidth = 1000\n", "pd.set_option(\"display.max_rows\", 500)\n", "pd.set_option(\"display.max_columns\", 500)"]}, {"cell_type": "code", "execution_count": null, "id": "277218c8-5cbb-42f9-8487-8a13663cb55f", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "c5156655-0a10-41d8-9d4c-4094d2d77f1b", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "b2ada589-30e1-4481-94e4-f2cc88a7b903", "metadata": {}, "outputs": [], "source": ["from kafka_utils import *"]}, {"cell_type": "code", "execution_count": null, "id": "8153a621-0fcb-43ee-b77f-daf09ba77cf3", "metadata": {}, "outputs": [], "source": ["from AffinityScore import *\n", "\n", "affinity_score = AffinityScore()"]}, {"cell_type": "code", "execution_count": null, "id": "c4cf0207-d344-44c7-9f1f-f545b760e066", "metadata": {}, "outputs": [], "source": ["def query_with_retries(query, con=pb.get_connection(\"[Warehouse] Trino\"), retries=3):\n", "    count = 1\n", "    while count <= retries:\n", "        try:\n", "            temp_df = pd.read_sql_query(query, con)\n", "            break\n", "        except Exception as e:\n", "            print(e)\n", "            count += 1\n", "    return temp_df"]}, {"cell_type": "code", "execution_count": null, "id": "33a51ef0-3fba-467a-a865-67e88eb80f8d", "metadata": {}, "outputs": [], "source": ["def from_sheets(sheet_id, sheet_name, max_tries=3):\n", "    for attempt in range(max_tries):\n", "        print(f\"Trying to read attempt: {attempt + 1}...\")\n", "        try:\n", "            return pb.from_sheets(sheet_id, sheet_name)\n", "        except Exception as e:\n", "            print(f\"Error occurred: {str(e)}\")\n", "            if hasattr(e, \"response\") and hasattr(e.response, \"json\"):\n", "                try:\n", "                    exception_code = (\n", "                        e.response.json().get(\"error\", {}).get(\"code\", \"Unknown error code\")\n", "                    )\n", "                    print(f\"API Error Code: {exception_code}\")\n", "                except Exception as inner_e:\n", "                    print(f\"Failed to extract error code: {str(inner_e)}\")\n", "            time.sleep(60)\n", "\n", "\n", "def to_sheets(df, sheet_id, sheet_name, max_tries=3):\n", "    for attempt in range(max_tries):\n", "        print(f\"Trying to read attempt: {attempt + 1}...\")\n", "        try:\n", "            return pb.to_sheets(df, sheet_id, sheet_name)\n", "        except Exception as e:\n", "            print(f\"Error occurred: {str(e)}\")\n", "            if hasattr(e, \"response\") and hasattr(e.response, \"json\"):\n", "                try:\n", "                    exception_code = (\n", "                        e.response.json().get(\"error\", {}).get(\"code\", \"Unknown error code\")\n", "                    )\n", "                    print(f\"API Error Code: {exception_code}\")\n", "                except Exception as inner_e:\n", "                    print(f\"Failed to extract error code: {str(inner_e)}\")\n", "            time.sleep(60)"]}, {"cell_type": "code", "execution_count": null, "id": "cc1bd0fe-cc61-407e-ab36-60beba6303a9", "metadata": {}, "outputs": [], "source": ["user_l1s = query_with_retries(\n", "    f\"\"\"with \n", "    prev_day_transacting_users as(\n", "    select\n", "        dim_customer_key\n", "        from dwh.fact_sales_order_details\n", "        where ORDER_CREATE_DT_IST = current_date - INTERVAL '1' DAY\n", "        group by 1\n", "    ),\n", "    cust_max_date as (\n", "        select fsod.dim_customer_key,\n", "               max(cart_rank) as cust_max_cr\n", "        from dwh.fact_sales_order_details fsod\n", "        join prev_day_transacting_users pd on pd.dim_customer_key = fsod.dim_customer_key\n", "        where order_create_dt_ist = current_date - interval '1' day \n", "        and is_internal_order = False\n", "        group by 1)\n", "    select \n", "        fs.dim_customer_key as user_id,\n", "        l1_category_id,\n", "        l1_category,\n", "        -- sum(power(0.80,(c.cust_max_cr - f.cart_rank)*100.0/c.cust_max_cr)) as score\n", "        round(sum(pow(0.95,(cust_max_cr - f.cart_rank)*100/(cust_max_cr))),5) as score\n", "        from dwh.fact_sales_order_item_details fs \n", "        join dwh.fact_sales_order_details f on f.order_id = fs.order_id\n", "        join cust_max_date c on c.dim_customer_key = f.dim_customer_key\n", "        join dwh.dim_product dp on dp.product_id = fs.product_id and dp.is_current and dp.is_product_enabled\n", "        where fs.is_internal_order = False \n", "        and fs.order_create_dt_ist between current_date - interval '180' day and current_date - interval '1' day\n", "        and f.order_create_dt_ist between current_date - interval '180' day and current_date - interval '1' day\n", "        and l0_category_id not in (343,2367,229,1487,16,1557,4265)\n", "        and product_type_id not in (5862,11778,11934,11789,11981,12115,12132,11939,459,11280,12184,11780,1704,\n", "        11791,11927,11929,11930,11932,11936,13800) \n", "        and l1_category_id not in (723,1474,629,75,298,326,741,290,155,199,1920,1674,922,1200,799,800,801,1048,7275,7342)\n", "        group by 1,2,3\n", "        order by 4 desc\"\"\"\n", ")\n", "\n", "user_l1s = user_l1s.drop_duplicates(subset=[\"user_id\", \"l1_category\"]).reset_index(drop=True)\n", "user_l1s[\"collection\"] = user_l1s.apply(\n", "    lambda row: \"empty_search_l1_category \" + str(row[\"l1_category_id\"]), axis=1\n", ")\n", "print(user_l1s.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "d52575ec-053c-49de-9fc7-7791b2ffc86d", "metadata": {}, "outputs": [], "source": ["user_pbs = query_with_retries(\n", "    f\"\"\"with cust_max_date as (\n", "        select dim_customer_key,\n", "               max(cart_rank) as cust_max_cr\n", "        from dwh.fact_sales_order_details\n", "        where order_create_dt_ist = current_date - interval '1' day \n", "        and is_internal_order = False\n", "        group by 1)\n", "    select \n", "        fs.dim_customer_key,\n", "        product_type_id,\n", "        product_type,\n", "        l1_category_id,\n", "        l1_category,\n", "        l0_category_id,\n", "        l0_category,\n", "        sum(power(0.80,(c.cust_max_cr - f.cart_rank)*100.0/c.cust_max_cr)) as decay_score\n", "        from dwh.fact_sales_order_item_details fs \n", "        join dwh.fact_sales_order_details f on f.order_id = fs.order_id\n", "        join cust_max_date c on c.dim_customer_key = f.dim_customer_key\n", "        join dwh.dim_product dp on dp.product_id = fs.product_id and dp.is_current and dp.is_product_enabled\n", "        where fs.is_internal_order = False \n", "        and fs.order_create_dt_ist between current_date - interval '180' day and current_date - interval '1' day\n", "        and f.order_create_dt_ist between current_date - interval '180' day and current_date - interval '1' day\n", "        and l0_category_id not in (343,2367,229,1487,16,1557)\n", "        and product_type_id not in (5862,11778,11934,11789,11981,12115,12132,11939,459,11280,12184,11780,\n", "        11791,11927,11929,11930,11932,11936,13800) \n", "        and l1_category_id not in (723,1474,629,75,298,326,741,290,155,199,1920,1674,922,1200,799,800,801,1048,7275,7342)\n", "        group by 1,2,3,4,5,6,7\n", "        order by 8 desc\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a4402009-c41c-48dc-8d1e-a3774479a582", "metadata": {}, "outputs": [], "source": ["df_anomalies = query_with_retries(\n", "    f\"\"\"with cust as (\n", "        select dim_customer_key\n", "        from dwh.fact_sales_order_details\n", "        where order_create_dt_ist = current_date - interval '1' day \n", "        and is_internal_order = False\n", "        group by 1)\n", "select a.* from consumer_intelligence_etls.anomaly_purchases a \n", "join cust c on c.dim_customer_key = a.dim_customer_key\n", "where user_mod is not null\"\"\"\n", ")\n", "\n", "\n", "def flatten_and_expand(row):\n", "    # Merge all list columns into one, maintaining order\n", "    group_ids = []\n", "    for col in [\"new_in_category\", \"new_in_tailend_ptype\", \"new_tailend_groupid\"]:\n", "        vals = row[col]\n", "        if isinstance(vals, list):\n", "            group_ids.extend(vals)\n", "    return pd.DataFrame(\n", "        {\n", "            \"dim_customer_key\": row[\"dim_customer_key\"],\n", "            \"rfm_segment\": row[\"rfm_segment\"],\n", "            \"group_id\": group_ids,\n", "        }\n", "    )\n", "\n", "\n", "# --- Step 1: Flatten the data as before ---\n", "df_anomalies1 = pd.concat(\n", "    df_anomalies.apply(flatten_and_expand, axis=1).to_list(), ignore_index=True\n", ")\n", "\n", "df_anomalies1[\"priority_rank\"] = df_anomalies1.groupby(\"dim_customer_key\").cumcount() + 1\n", "\n", "pid_gid_map = query_with_retries(\n", "    \"\"\"select distinct gpm.group_id, p.product_type, p.product_type_id\n", "       from lake_cms.gr_group_product_mapping gpm\n", "       join dwh.dim_product p on p.product_id = gpm.product_id and p.is_current and p.is_product_enabled\n", "       join cms.gr_group gg on gg.id = gpm.group_id and gg.is_active\n", "       where gpm.enabled_flag=True and gpm.lake_active_record=true\"\"\"\n", ")\n", "\n", "l1_gid_map = query_with_retries(\n", "    \"\"\"select distinct gpm.group_id, p.l1_category, p.l1_category_id\n", "       from lake_cms.gr_group_product_mapping gpm\n", "       join dwh.dim_product p on p.product_id = gpm.product_id and p.is_current and p.is_product_enabled\n", "       join cms.gr_group gg on gg.id = gpm.group_id and gg.is_active\n", "       where gpm.enabled_flag=True and gpm.lake_active_record=true\"\"\"\n", ")\n", "\n", "\n", "df_anomalies2 = df_anomalies1.merge(pid_gid_map, on=[\"group_id\"])\n", "df_anomalies2 = df_anomalies2.sort_values(\"priority_rank\")\n", "df_anomalies2 = df_anomalies2.drop_duplicates(\n", "    subset=[\"dim_customer_key\", \"product_type_id\"], keep=\"first\"\n", ")\n", "df_anomalies2 = df_anomalies2.rename(columns={\"priority_rank\": \"is_anomaly\"})\n", "df_anomalies2 = df_anomalies2[[\"dim_customer_key\", \"product_type_id\", \"is_anomaly\"]]\n", "df_anomalies2 = df_anomalies2[df_anomalies2[\"is_anomaly\"] > 0]\n", "\n", "\n", "df_anomalies3 = df_anomalies1.merge(l1_gid_map, on=[\"group_id\"])\n", "df_anomalies3 = df_anomalies3.sort_values(\"priority_rank\")\n", "df_anomalies3 = df_anomalies3.drop_duplicates(\n", "    subset=[\"dim_customer_key\", \"l1_category_id\"], keep=\"first\"\n", ")\n", "df_anomalies3 = df_anomalies3.rename(columns={\"priority_rank\": \"is_anomaly\"})\n", "df_anomalies3 = df_anomalies3[[\"dim_customer_key\", \"l1_category_id\", \"is_anomaly\"]]\n", "df_anomalies3 = df_anomalies3[df_anomalies3[\"is_anomaly\"] > 0]"]}, {"cell_type": "code", "execution_count": null, "id": "c8dbf4c7-754f-442b-9941-590f2d05f26f", "metadata": {}, "outputs": [], "source": ["user_l1s = user_l1s.merge(\n", "    df_anomalies3.rename(columns={\"dim_customer_key\": \"user_id\"}),\n", "    on=[\"user_id\", \"l1_category_id\"],\n", "    how=\"left\",\n", ").<PERSON>na(0)\n", "user_l1s = (\n", "    user_l1s.sort_values(by=[\"user_id\", \"is_anomaly\", \"score\"], ascending=[True, True, False])\n", "    .groupby([\"user_id\"])\n", "    .head(10)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "dfae4a28-0f09-4362-8ddd-e37824d9b1cb", "metadata": {}, "outputs": [], "source": ["user_l1s.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f70000e4-60af-4d5e-852d-8eec209a646c", "metadata": {}, "outputs": [], "source": ["user_pbs.dim_customer_key.unique()"]}, {"cell_type": "code", "execution_count": null, "id": "fe693528-42ff-40da-bf7e-1eb55c2db823", "metadata": {}, "outputs": [], "source": ["all_users = sorted(user_pbs.dim_customer_key.unique())"]}, {"cell_type": "code", "execution_count": null, "id": "29902d54-4a21-4991-a7c0-7b243794daf1", "metadata": {}, "outputs": [], "source": ["user_pbs = user_pbs.groupby([\"dim_customer_key\", \"product_type_id\"]).head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "08dc5f70-531b-4756-9170-b0babd856043", "metadata": {}, "outputs": [], "source": ["prod_data = query_with_retries(\n", "    f\"\"\"\n", "            select \n", "                product_id, product_name\n", "                product_type, product_type_id,\n", "                l1_category, l1_category_id,\n", "                l0_category, l0_category_id\n", "            from dwh.dim_product dp\n", "            where dp.is_current and dp.is_product_enabled\"\"\"\n", ")\n", "collection_pids = from_sheets(\"1k8pNkI7GjEGOul5aVeOcOb06fYBeLT43wkwPKfACFLI\", \"byb_pids\")\n", "# collection_pids = pd.read_csv(\"Order Again & BYB - Rules - byb_pids.csv\")\n", "collection_pids[\"product_id\"] = collection_pids[\"product_id\"].astype(int)\n", "collection_pids[\"product_type_id\"] = collection_pids[\"product_type_id\"].astype(int)\n", "collection_pids = collection_pids.merge(prod_data)\n", "print(collection_pids.shape)\n", "collection_pids.head(2)"]}, {"cell_type": "markdown", "id": "d5280cbc-d8b2-49d5-917a-6156989e1972", "metadata": {}, "source": ["## All scope"]}, {"cell_type": "code", "execution_count": null, "id": "17b0ec8e-a2ed-4aaa-8efb-cfce75f6155f", "metadata": {}, "outputs": [], "source": ["temp = collection_pids[(collection_pids[\"type\"] == \"byb\") & (collection_pids[\"scope\"] == \"all\")][\n", "    [\"collection_uuid\", \"usecase\", \"product_type_id\", \"l1_category_id\"]\n", "].drop_duplicates()\n", "byb_all = (\n", "    user_pbs.merge(temp)\n", "    .merge(df_anomalies2, how=\"left\")\n", "    .fillna(0)\n", "    .sort_values(by=[\"is_anomaly\", \"decay_score\"], ascending=[True, False])\n", ")\n", "byb_all = (\n", "    byb_all.groupby([\"dim_customer_key\", \"product_type\", \"l1_category\"])\n", "    .head(1)\n", "    .groupby([\"dim_customer_key\", \"usecase\"])\n", "    .head(1)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "87043aab-73d8-4102-bc43-5622aa729000", "metadata": {"tags": []}, "outputs": [], "source": ["user_pbs_agg = byb_all.groupby([\"dim_customer_key\"]).agg({\"decay_score\": [min, max]}).reset_index()\n", "user_pbs_agg.columns = [\"dim_customer_key\", \"decay_score_min\", \"decay_score_max\"]\n", "byb_all = byb_all.merge(user_pbs_agg)\n", "byb_all[\"scaled_score\"] = (byb_all[\"decay_score\"] - byb_all[\"decay_score_min\"]) / (\n", "    byb_all[\"decay_score_max\"] - byb_all[\"decay_score_min\"]\n", ")\n", "byb_all[\"scaled_score\"] = byb_all[\"scaled_score\"].fillna(1)\n", "byb_all = byb_all[[\"dim_customer_key\", \"usecase\", \"product_type\", \"scaled_score\", \"is_anomaly\"]]\n", "byb_all[\"product_type\"] = np.where(byb_all[\"product_type\"].isna(), \"\", byb_all[\"product_type\"])\n", "byb_all[\"scaled_score\"] = np.where(\n", "    byb_all[\"is_anomaly\"] >= 1, byb_all[\"scaled_score\"] + 2.5, byb_all[\"scaled_score\"]\n", ")\n", "byb_all = byb_all.sort_values(by=[\"scaled_score\"], ascending=False)[\n", "    [\"dim_customer_key\", \"usecase\", \"product_type\", \"scaled_score\"]\n", "].rename(\n", "    columns={\n", "        \"dim_customer_key\": \"user_id\",\n", "        \"scaled_score\": \"score\",\n", "        \"product_type\": \"subtitle\",\n", "    }\n", ")\n", "byb_all[\"score\"] = byb_all[\"score\"].fillna(0)\n", "byb_all[\"subtitle\"] = np.where(\n", "    byb_all[\"subtitle\"] != \"\",\n", "    \"Because you bought \" + byb_all[\"subtitle\"].str.lower(),\n", "    \"\",\n", ")\n", "\n", "byb_all.columns = [\"user_id\", \"collection\", \"subtitle\", \"score\"]"]}, {"cell_type": "markdown", "id": "9a1049f6-785f-4d03-86fb-32de70e508cf", "metadata": {}, "source": ["## kids scope"]}, {"cell_type": "code", "execution_count": null, "id": "cbc884b1-a305-42b2-b392-29dce1b1ac9a", "metadata": {}, "outputs": [], "source": ["kids_collections = from_sheets(\"1YR1ZnZapGi-_2yWSa4u9zp9mze_2w0Uk2_9XlI638D8\", \"kids\")[\n", "    [\"product_type_id\", \"usecase_collection_name\", \"collection_subtitle\"]\n", "].drop_duplicates()\n", "\n", "# kids_collections = pd.read_csv(\"BYB-UsecaseHook-Usecase Collection Master - kids.csv\")[\n", "#     [\"product_type_id\", \"usecase_collection_name\", \"collection_subtitle\"]\n", "# ].drop_duplicates()\n", "kids_collections[\"product_type_id\"] = kids_collections[\"product_type_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "9a316394-bb1c-4390-83f6-0d0e25b7a707", "metadata": {}, "outputs": [], "source": ["byb_kids = (\n", "    user_pbs[user_pbs[\"l0_category_id\"] == 7][\n", "        [\"dim_customer_key\", \"product_type_id\", \"product_type\", \"decay_score\"]\n", "    ]\n", "    .merge(kids_collections, on=[\"product_type_id\"])\n", "    .merge(df_anomalies2, how=\"left\")\n", "    .fillna(0)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "86bdbd3e-9852-4f1a-bb8e-8d0ad76d13d4", "metadata": {}, "outputs": [], "source": ["user_pbs_agg = byb_kids.groupby([\"dim_customer_key\"]).agg({\"decay_score\": [min, max]}).reset_index()\n", "user_pbs_agg.columns = [\"dim_customer_key\", \"decay_score_min\", \"decay_score_max\"]\n", "byb_kids = byb_kids.merge(user_pbs_agg)\n", "byb_kids[\"scaled_score\"] = (byb_kids[\"decay_score\"] - byb_kids[\"decay_score_min\"]) / (\n", "    byb_kids[\"decay_score_max\"] - byb_kids[\"decay_score_min\"]\n", ")\n", "byb_kids[\"scaled_score\"] = byb_kids[\"scaled_score\"].fillna(1)\n", "byb_kids[\"scaled_score\"] = np.where(\n", "    byb_kids[\"is_anomaly\"] >= 1, byb_kids[\"scaled_score\"] + 2.5, byb_kids[\"scaled_score\"]\n", ")\n", "byb_kids = byb_kids.sort_values(by=[\"scaled_score\"], ascending=False)[\n", "    [\n", "        \"dim_customer_key\",\n", "        \"usecase_collection_name\",\n", "        \"collection_subtitle\",\n", "        \"scaled_score\",\n", "    ]\n", "]\n", "byb_kids.columns = [\"user_id\", \"collection\", \"subtitle\", \"score\"]"]}, {"cell_type": "markdown", "id": "4c655003-5293-4dfe-a14a-f5d64b5cafd1", "metadata": {}, "source": ["## electronics scope"]}, {"cell_type": "code", "execution_count": null, "id": "d2d74526-69b4-48a4-b7fd-7ca37f2391a8", "metadata": {}, "outputs": [], "source": ["elec_collections = from_sheets(\"1YR1ZnZapGi-_2yWSa4u9zp9mze_2w0Uk2_9XlI638D8\", \"electronics\")[\n", "    [\"product_type_id\", \"usecase_collection_name\", \"collection_subtitle\"]\n", "].drop_duplicates()\n", "#\n", "# elec_collections = pd.read_csv(\"BYB-UsecaseHook-Usecase Collection Master - electronics.csv\")[\n", "#     [\"product_type_id\", \"usecase_collection_name\", \"collection_subtitle\"]\n", "# ].drop_duplicates()\n", "elec_collections[\"product_type_id\"] = elec_collections[\"product_type_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "b472575f-b46e-4d21-bb70-956c4964b8a0", "metadata": {}, "outputs": [], "source": ["byb_elecs = (\n", "    user_pbs[\n", "        (user_pbs[\"l0_category_id\"] == 1379)\n", "        & (user_pbs[\"l1_category_id\"].isin([1886, 1840, 1445, 1187, 1387, 2513]))\n", "    ][[\"dim_customer_key\", \"product_type_id\", \"product_type\", \"decay_score\"]]\n", "    .merge(elec_collections, on=[\"product_type_id\"])\n", "    .merge(df_anomalies2, how=\"left\")\n", "    .fillna(0)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "196731cc-43d6-479d-a011-903104d1b1e8", "metadata": {}, "outputs": [], "source": ["user_pbs_agg = (\n", "    byb_elecs.groupby([\"dim_customer_key\"]).agg({\"decay_score\": [min, max]}).reset_index()\n", ")\n", "user_pbs_agg.columns = [\"dim_customer_key\", \"decay_score_min\", \"decay_score_max\"]\n", "byb_elecs = byb_elecs.merge(user_pbs_agg)\n", "byb_elecs[\"scaled_score\"] = (byb_elecs[\"decay_score\"] - byb_elecs[\"decay_score_min\"]) / (\n", "    byb_elecs[\"decay_score_max\"] - byb_elecs[\"decay_score_min\"]\n", ")\n", "byb_elecs[\"scaled_score\"] = byb_elecs[\"scaled_score\"].fillna(1)\n", "byb_elecs[\"scaled_score\"] = np.where(\n", "    byb_elecs[\"is_anomaly\"] >= 1, byb_elecs[\"scaled_score\"] + 2.5, byb_elecs[\"scaled_score\"]\n", ")\n", "byb_elecs = byb_elecs.sort_values(by=[\"scaled_score\"], ascending=[False])[\n", "    [\n", "        \"dim_customer_key\",\n", "        \"usecase_collection_name\",\n", "        \"collection_subtitle\",\n", "        \"scaled_score\",\n", "    ]\n", "]\n", "byb_elecs.columns = [\"user_id\", \"collection\", \"subtitle\", \"score\"]"]}, {"cell_type": "code", "execution_count": null, "id": "a2226607-6933-4cbe-a668-f0ac135bdaf4", "metadata": {}, "outputs": [], "source": ["# byb_elecs[\"product_id\"] = byb_elecs.apply(\n", "#     lambda row: {\n", "#         \"collection\": row[\"usecase_collection_name\"],\n", "#         \"subtitle\": row[\"collection_subtitle\"],\n", "#         \"score\": row[\"scaled_score\"],\n", "#     },\n", "#     axis=1,\n", "# )\n", "# byb_elecs = byb_elecs.drop(\n", "#     columns=[\"usecase_collection_name\", \"collection_subtitle\", \"scaled_score\"]\n", "# ).rename(columns={\"dim_customer_key\": \"user_id\"})\n", "# df_elecs = byb_elecs.groupby(\"user_id\").product_id.apply(list).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "7bf1e625-bb20-463f-b0d5-42bc6a5127e9", "metadata": {}, "outputs": [], "source": ["byb_elecs.head(2)"]}, {"cell_type": "markdown", "id": "70a3a555-38f4-4204-a8e5-20b91e19cddf", "metadata": {}, "source": ["## beauty scope"]}, {"cell_type": "code", "execution_count": null, "id": "a3fa7f40-14b0-4b4c-8fa5-92b9dbf7eb34", "metadata": {}, "outputs": [], "source": ["beauty_collections = from_sheets(\"1YR1ZnZapGi-_2yWSa4u9zp9mze_2w0Uk2_9XlI638D8\", \"beauty\")[\n", "    [\"product_type_id\", \"usecase_collection_name\", \"collection_subtitle\"]\n", "].drop_duplicates()\n", "\n", "# beauty_collections = pd.read_csv(\"BYB-UsecaseHook-Usecase Collection Master - beauty.csv\")[\n", "#     [\"product_type_id\", \"usecase_collection_name\", \"collection_subtitle\"]\n", "# ].drop_duplicates()\n", "beauty_collections[\"product_type_id\"] = beauty_collections[\"product_type_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "6e2950b0-492c-44af-bf38-a8ce31fa7957", "metadata": {}, "outputs": [], "source": ["byb_beauty = (\n", "    user_pbs[(user_pbs[\"l0_category_id\"] == 13)][\n", "        [\"dim_customer_key\", \"product_type_id\", \"product_type\", \"decay_score\"]\n", "    ]\n", "    .merge(beauty_collections, on=[\"product_type_id\"])\n", "    .merge(df_anomalies2, how=\"left\")\n", "    .fillna(0)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fb2cb775-4b0d-4032-add6-ceb5e61bdbf6", "metadata": {}, "outputs": [], "source": ["user_pbs_agg = (\n", "    byb_beauty.groupby([\"dim_customer_key\"]).agg({\"decay_score\": [min, max]}).reset_index()\n", ")\n", "user_pbs_agg.columns = [\"dim_customer_key\", \"decay_score_min\", \"decay_score_max\"]\n", "byb_beauty = byb_beauty.merge(user_pbs_agg)\n", "byb_beauty[\"scaled_score\"] = (byb_beauty[\"decay_score\"] - byb_beauty[\"decay_score_min\"]) / (\n", "    byb_beauty[\"decay_score_max\"] - byb_beauty[\"decay_score_min\"]\n", ")\n", "byb_beauty[\"scaled_score\"] = byb_beauty[\"scaled_score\"].fillna(1)\n", "byb_beauty[\"scaled_score\"] = np.where(\n", "    byb_beauty[\"is_anomaly\"] >= 1, byb_beauty[\"scaled_score\"] + 2.5, byb_beauty[\"scaled_score\"]\n", ")\n", "byb_beauty = byb_beauty.sort_values(by=[\"scaled_score\"], ascending=False)[\n", "    [\n", "        \"dim_customer_key\",\n", "        \"usecase_collection_name\",\n", "        \"collection_subtitle\",\n", "        \"scaled_score\",\n", "    ]\n", "]\n", "byb_beauty.columns = [\"user_id\", \"collection\", \"subtitle\", \"score\"]"]}, {"cell_type": "code", "execution_count": null, "id": "9030bcad-c74c-4373-8ebc-74dd277c9de2", "metadata": {}, "outputs": [], "source": ["# byb_beauty[\"product_id\"] = byb_beauty.apply(\n", "#     lambda row: {\n", "#         \"collection\": row[\"usecase_collection_name\"],\n", "#         \"subtitle\": row[\"collection_subtitle\"],\n", "#         \"score\": row[\"scaled_score\"],\n", "#     },\n", "#     axis=1,\n", "# )\n", "# byb_beauty = byb_beauty.drop(\n", "#     columns=[\"usecase_collection_name\", \"collection_subtitle\", \"scaled_score\"]\n", "# ).rename(columns={\"dim_customer_key\": \"user_id\"})\n", "# df_beauty = byb_beauty.groupby(\"user_id\").product_id.apply(list).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "358fadf2-677f-42a5-9719-3eaa7e7098eb", "metadata": {}, "outputs": [], "source": ["byb_beauty.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "2bf20a60-3a21-48da-961a-35b57c6b501f", "metadata": {}, "outputs": [], "source": ["user_pbs = []\n", "user_pbs_agg = []\n", "del user_pbs, user_pbs_agg\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "083e4b8a-b358-48d8-9bd8-749c65392814", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "63e9b476-34d4-4473-b27f-af5cede1e9c8", "metadata": {}, "source": ["## Preliminary data for ranking collections"]}, {"cell_type": "code", "execution_count": null, "id": "ab847f31-9324-428f-9979-14de829ae388", "metadata": {}, "outputs": [], "source": ["df_L0_L1 = query_with_retries(\n", "    f\"\"\"\n", "    with dp as\n", "    (select l0_category_id,l1_category_id,product_type_id,l0_category,l1_category,product_type,cast(product_id as int) product_id\n", "    from dwh.dim_product\n", "    where is_current),\n", "\n", "    l0_l1_cart_penetration as \n", "    (select dp.l0_category,dp.l0_category_id,\n", "            dp.l1_category,dp.l1_category_id,\n", "            count(distinct(sales.cart_id)) as l0_l1_cart_count,\n", "            count(distinct(sales.dim_customer_key)) as l0_l1_user_count \n", "    from dwh.fact_sales_order_item_details sales left join dp \n", "    on sales.product_id = dp.product_id where sales.order_create_dt_ist >= current_date - INTERVAL '90' DAY group by 1,2,3,4)\n", "\n", "\n", "    select l0_category,l0_category_id,l1_category,l1_category_id,l0_l1_cart_count,cast((l0_l1_cart_count*1.000000000)/(select count(distinct(cart_id)) from dwh.fact_sales_order_item_details where order_create_dt_ist>=current_date - INTERVAL '90' DAY) as double) as l0_l1_cart_pnt,\n", "    \n", "    l0_l1_user_count,cast((l0_l1_user_count*1.000000000)/(select count(distinct(dim_customer_key)) from dwh.fact_sales_order_item_details where order_create_dt_ist>=current_date - INTERVAL '90' DAY) as double) as l0_l1_user_pnt from l0_l1_cart_penetration\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c616c7b2-6580-4041-950c-d17b054e9bd0", "metadata": {}, "outputs": [], "source": ["def quantile_transform(df, max_impression, min_impression):\n", "    qt = QuantileTransformer(output_distribution=\"uniform\")\n", "    df[\"transformed_penetration\"] = qt.fit_transform(df[[\"l0_l1_cart_pnt\"]])\n", "\n", "    def scale_1(value):\n", "        return max_impression + (min_impression - max_impression) * value\n", "\n", "    df[\"Impression_allowed\"] = df[\"transformed_penetration\"].apply(scale_1)\n", "    return df\n", "\n", "\n", "# Define Max and <PERSON> impressions\n", "max_impression = 5\n", "min_impression = 2\n", "\n", "# Apply Quantile Transformation\n", "df_quantile = pd.DataFrame()\n", "df_quantile = quantile_transform(df_L0_L1, max_impression, min_impression)"]}, {"cell_type": "code", "execution_count": null, "id": "9e32e8dc-0d1f-4b03-a924-9a409e8622a2", "metadata": {}, "outputs": [], "source": ["df_user_collection_actual_impression = query_with_retries(\n", "    f\"\"\"\n", "    with impr as \n", "    (\n", "    select at_date_ist, platform, user_id ,properties__widget_tracking_id widget_title, \n", "    properties__widget_name,\n", "    properties__page_visit_id,properties__page_title,properties__collection_id as collection_uuid\n", "    from lake_events.mobile_impression_data\n", "    where name in ('Product Shown')\n", "    and at_date_ist between current_date - interval '10' day and current_date - interval '1' day \n", "    and properties__page_name in ('feed')\n", "    and platform in ('android','ios')\n", "    and (properties__sub_page_name is null or properties__sub_page_name='' or properties__sub_page_name ='#-NA')\n", "    and properties__widget_name <> 'Product'\n", "    and (app__version like '16%%' or app__version like '17%%') and properties__widget_tracking_id  like 'byb_%%'\n", "    group by 1,2,3,4,5,6,7,8\n", "    ),\n", "\n", "    user_candidate_set as\n", "    (select cast(dim_customer_key as varchar) as user_id \n", "    from dwh.fact_sales_order_details o where \n", "    order_create_dt_ist >= current_date - interval '1' day\n", "    and o.order_current_status='DELIVERED' and o.is_internal_order = False\n", "    group by 1)\n", "    \n", "    \n", "    select \n", "        cast(a.user_id as int) as user_id,\n", "        a.collection_uuid,\n", "    count(distinct a.user_id) as unique_impr, \n", "    count(DISTINCT a.user_id||a.properties__page_visit_id) as total_impr\n", "    from impr a\n", "    inner join user_candidate_set b\n", "    on a.user_id = b.user_id\n", "    where a.properties__page_title not in ('Homepage V3.0')\n", "    group by 1,2\"\"\"\n", ")\n", "df_user_collection_actual_impression_final = df_user_collection_actual_impression[\n", "    (df_user_collection_actual_impression[\"collection_uuid\"].notna())\n", "    & (df_user_collection_actual_impression[\"collection_uuid\"] != \"\")\n", "][[\"user_id\", \"collection_uuid\", \"total_impr\"]]\n", "df_user_collection_actual_impression_final = df_user_collection_actual_impression_final.sort_values(\n", "    [\"user_id\"], ascending=[True]\n", ")\n", "df_user_collection_actual_impression_final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "964f1a74-189f-4f85-b24c-092a34f274ed", "metadata": {}, "outputs": [], "source": ["final = from_sheets(\"1k8pNkI7GjEGOul5aVeOcOb06fYBeLT43wkwPKfACFLI\", \"usecase_pids\")\n", "# final = pd.read_csv(\"Order Again & BYB - Rules - usecase_pids.csv\")\n", "final[\"product_type_id\"] = final[\"product_type_id\"].astype(int)\n", "final[\"product_id\"] = final[\"product_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "6e9fc70b-ef77-4ab0-8dd0-3741bbeb5d55", "metadata": {}, "outputs": [], "source": ["!pip install re\n", "import re\n", "\n", "\n", "def clean_string(input_string):\n", "    cleaned_string = re.sub(r\"[^a-zA-Z0-9]+\", \"_\", input_string)\n", "    cleaned_string = re.sub(r\"^_+|_+$\", \"\", cleaned_string)\n", "    return cleaned_string.lower()"]}, {"cell_type": "code", "execution_count": null, "id": "6fd2862d-b1a7-4dbf-a41d-c1228c3bb2b9", "metadata": {}, "outputs": [], "source": ["hooks_collection_df = final.drop(columns=[\"product_type\", \"product_type_id\"]).copy()\n", "hooks_collection_df[\"type\"] = \"hook\"\n", "hooks_collection_df[\"scope\"] = \"all\"\n", "hooks_collection_df = hooks_collection_df.merge(\n", "    prod_data[[\"product_id\", \"l1_category_id\", \"l0_category_id\"]]\n", ")\n", "hooks_collection_df.rename(columns={\"Usecase\": \"usecase\"}, inplace=True)\n", "hooks_collection_df[\"usecase\"] = hooks_collection_df.apply(\n", "    lambda row: clean_string(row[\"usecase\"]), axis=1\n", ")\n", "byb_collection_df = collection_pids[\n", "    [\n", "        \"collection_uuid\",\n", "        \"usecase\",\n", "        \"type\",\n", "        \"scope\",\n", "        \"product_id\",\n", "        \"l1_category_id\",\n", "        \"l0_category_id\",\n", "    ]\n", "].copy()\n", "\n", "total_collection_df = pd.concat([byb_collection_df, hooks_collection_df])\n", "\n", "collection_imp_allowed_df = total_collection_df.merge(\n", "    df_quantile[[\"l0_category_id\", \"l1_category_id\", \"Impression_allowed\"]].drop_duplicates()\n", ")\n", "\n", "collection_imp_allowed_df.shape\n", "\n", "collection_imp_allowed_df = (\n", "    collection_imp_allowed_df.groupby([\"usecase\", \"type\", \"scope\"])[\"Impression_allowed\"]\n", "    .mean()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "361b89e9-e737-45ac-add7-8366c2d957e1", "metadata": {}, "outputs": [], "source": ["collection_imp_allowed_df.columns = [\n", "    \"collection\",\n", "    \"type\",\n", "    \"scope\",\n", "    \"impression_allowed\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "28950f43-bd5d-4b8b-b982-b2af41f812f1", "metadata": {}, "outputs": [], "source": ["df_user_collection_actual_impression_final = df_user_collection_actual_impression_final.merge(\n", "    total_collection_df[[\"collection_uuid\", \"usecase\"]]\n", "    .drop_duplicates()\n", "    .rename(columns={\"usecase\": \"collection\"})\n", ")\n", "df_user_collection_actual_impression_final[\"user_id\"] = df_user_collection_actual_impression_final[\n", "    \"user_id\"\n", "].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "6d240419-59e8-4708-9b11-515a368519c6", "metadata": {}, "outputs": [], "source": ["def get_ranked_collections(user_id, collection_ptype_definition):\n", "    u_p_aff = ptypes_scores.loc[user_id, \"ptype_affinity\"]\n", "    u_p_aff = {str(k): v for k, v in u_p_aff.items()}\n", "    c_copy = collection_ptype_definition.copy()\n", "    c_copy[\"score\"] = c_copy[0].apply(lambda x: sum(x[k] * u_p_aff[k] for k in x if k in u_p_aff))\n", "    c_copy = c_copy.sort_values(\"score\", ascending=False)\n", "    c_copy[\"user_id\"] = user_id\n", "    return c_copy[[\"user_id\", \"collection_uuid\", \"collection\", \"score\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "b167d43e-084f-4274-922f-33219d2b12fe", "metadata": {}, "outputs": [], "source": ["def post_process(user_group, final):\n", "    collection_pid = (\n", "        final.groupby([\"collection_uuid\", \"product_type\", \"product_type_id\", \"Usecase\"])\n", "        .product_id.nunique()\n", "        .reset_index()\n", "    )\n", "    temp = (\n", "        collection_pid.groupby([\"collection_uuid\"])\n", "        .product_id.sum()\n", "        .reset_index()\n", "        .rename(columns={\"product_id\": \"total_pids\"})\n", "    )\n", "    collection_pid = collection_pid.merge(temp, on=[\"collection_uuid\"])\n", "\n", "    temp = collection_pid.drop_duplicates([\"collection_uuid\", \"Usecase\"])\n", "    ctousecase = dict(zip(temp.collection_uuid, temp.Usecase))\n", "\n", "    collection_pid[\"ptype_ratio\"] = collection_pid.product_id / collection_pid.total_pids\n", "    collection_ptype_definition = (\n", "        collection_pid.groupby(\"collection_uuid\")[[\"product_type_id\", \"ptype_ratio\"]]\n", "        .apply(lambda x: dict(zip(x.product_type_id.astype(str), x.ptype_ratio)))\n", "        .reset_index()\n", "    )\n", "    collection_ptype_definition[\"collection\"] = collection_ptype_definition[\"collection_uuid\"].map(\n", "        ctousecase\n", "    )\n", "    item_group = list(collection_pid.product_type_id.unique())\n", "    ptypes_scores = affinity_score.get_affinity(user_group, item_group)\n", "    ptypes_scores[\"ptype_affinity\"] = ptypes_scores.apply(\n", "        lambda row: dict(zip(row[\"item_id\"], row[\"scores\"])), axis=1\n", "    )\n", "    ptypes_scores = ptypes_scores[[\"user_id\", \"ptype_affinity\"]].set_index(\"user_id\")\n", "    df_hooks = pd.DataFrame(ptypes_scores.index)\n", "    return collection_ptype_definition, df_hooks, ptypes_scores"]}, {"cell_type": "code", "execution_count": null, "id": "5fa7158b-2e66-400d-bad7-6658d08fcd71", "metadata": {}, "outputs": [], "source": ["def parallel_get_collections_scores(user_id):\n", "    return get_ranked_collections(user_id, collection_ptype_definition)"]}, {"cell_type": "code", "execution_count": null, "id": "c9dbc978-9089-47c4-bd83-00a726b32672", "metadata": {}, "outputs": [], "source": ["def ranking(temp, type_, scope):\n", "    temp = temp.merge(\n", "        collection_imp_allowed_df[\n", "            (collection_imp_allowed_df[\"type\"] == type_)\n", "            & (collection_imp_allowed_df[\"scope\"] == scope)\n", "        ],\n", "        how=\"left\",\n", "        on=[\"collection\"],\n", "    )\n", "    temp = temp.merge(df_user_collection_actual_impression_final, how=\"left\")\n", "    temp[\"total_impr\"] = temp[\"total_impr\"].fillna(0)\n", "    temp[\"impression_allowed\"] = temp[\"impression_allowed\"].fillna(3)\n", "    temp[\"score\"] = np.where(\n", "        temp[\"total_impr\"] >= temp[\"impression_allowed\"],\n", "        temp[\"score\"],\n", "        temp[\"score\"] + 1.5,\n", "    )\n", "    return temp"]}, {"cell_type": "code", "execution_count": null, "id": "1ad36a4a-32bc-46a2-9759-43c382b17f28", "metadata": {}, "outputs": [], "source": ["from itertools import islice"]}, {"cell_type": "code", "execution_count": null, "id": "a79b33d0-795b-4165-9d00-809ad50efc10", "metadata": {}, "outputs": [], "source": ["def chunker(seq, size):\n", "    return (seq[pos : pos + size] for pos in range(0, len(seq), size))\n", "\n", "\n", "def get_nth_chunk(seq, size, k):\n", "    return next(islice(chunker(seq, size), k, k + 1))"]}, {"cell_type": "code", "execution_count": null, "id": "1665bb26-4792-4d1f-8adb-b4c222825e47", "metadata": {}, "outputs": [], "source": ["n = 75000\n", "k_prev = int(from_sheets(\"1k8pNkI7GjEGOul5aVeOcOb06fYBeLT43wkwPKfACFLI\", \"byb_chunk\").iloc[0, 0])\n", "print(k_prev)\n", "chunks = int(len(all_users) / n) + 1\n", "print(chunks)\n", "for k in range(0, chunks):\n", "    if k < k_prev:\n", "        print(k, k_prev)\n", "    else:\n", "        print(k, k_prev)\n", "        user_group = get_nth_chunk(all_users, n, k)\n", "        print(\"Running for users: \", len(user_group))\n", "        collection_ptype_definition, temp, ptypes_scores = post_process(user_group, final)\n", "        num_cpus = 8\n", "        with mp.Pool(num_cpus) as pool:\n", "            result_list = pool.map(parallel_get_collections_scores, temp[\"user_id\"])\n", "        df_hooks = pd.concat(result_list, ignore_index=True)\n", "        del ptypes_scores, result_list\n", "        gc.collect()\n", "        print(\"df_hooks shape: \", df_hooks.shape)\n", "\n", "        ## byb all\n", "\n", "        df_byb = ranking(byb_all[byb_all[\"user_id\"].isin(user_group)], \"byb\", \"all\")\n", "        df_byb = df_byb[[\"user_id\", \"collection\", \"subtitle\", \"score\"]].sort_values(\n", "            by=[\"user_id\", \"score\"], ascending=[True, False]\n", "        )\n", "        df_byb[\"product_id\"] = np.where(\n", "            df_byb[\"subtitle\"] != \"\",\n", "            df_byb.apply(\n", "                lambda row: {\n", "                    \"collection\": row[\"collection\"],\n", "                    \"subtitle\": row[\"subtitle\"],\n", "                    \"score\": row[\"score\"],\n", "                },\n", "                axis=1,\n", "            ),\n", "            df_byb.apply(\n", "                lambda row: {\"collection\": row[\"collection\"], \"score\": row[\"score\"]}, axis=1\n", "            ),\n", "        )\n", "        df_byb = df_byb.drop(columns=[\"collection\", \"subtitle\", \"score\"])\n", "        df_byb = df_byb.groupby(\"user_id\").product_id.apply(list).reset_index()\n", "        print(\"BYB All: \", df_byb.shape)\n", "\n", "        ## byb kids\n", "\n", "        df_kids = ranking(byb_kids[byb_kids[\"user_id\"].isin(user_group)], \"byb\", \"kids\")\n", "        df_kids = df_kids[[\"user_id\", \"collection\", \"subtitle\", \"score\"]].sort_values(\n", "            by=[\"user_id\", \"score\"], ascending=[True, False]\n", "        )\n", "        df_kids[\"product_id1\"] = df_kids.apply(\n", "            lambda row: {\n", "                \"collection\": row[\"collection\"],\n", "                \"subtitle\": row[\"subtitle\"],\n", "                \"score\": row[\"score\"],\n", "            },\n", "            axis=1,\n", "        )\n", "        df_kids = df_kids.drop(columns=[\"collection\", \"subtitle\", \"score\"])\n", "        df_kids = df_kids.groupby(\"user_id\").product_id1.apply(list).reset_index()\n", "        print(\"BYB Kids: \", df_kids.shape)\n", "\n", "        ## byb elecs\n", "\n", "        df_elecs = ranking(byb_elecs[byb_elecs[\"user_id\"].isin(user_group)], \"byb\", \"electronics\")\n", "        df_elecs = df_elecs[[\"user_id\", \"collection\", \"subtitle\", \"score\"]].sort_values(\n", "            by=[\"user_id\", \"score\"], ascending=[True, False]\n", "        )\n", "        df_elecs[\"product_id2\"] = df_elecs.apply(\n", "            lambda row: {\n", "                \"collection\": row[\"collection\"],\n", "                \"subtitle\": row[\"subtitle\"],\n", "                \"score\": row[\"score\"],\n", "            },\n", "            axis=1,\n", "        )\n", "        df_elecs = df_elecs.drop(columns=[\"collection\", \"subtitle\", \"score\"])\n", "        df_elecs = df_elecs.groupby(\"user_id\").product_id2.apply(list).reset_index()\n", "        print(\"BYB Elecs: \", df_elecs.shape)\n", "\n", "        ## byb beauty\n", "\n", "        df_beauty = ranking(byb_beauty[byb_beauty[\"user_id\"].isin(user_group)], \"byb\", \"beauty\")\n", "        df_beauty = df_beauty[[\"user_id\", \"collection\", \"subtitle\", \"score\"]].sort_values(\n", "            by=[\"user_id\", \"score\"], ascending=[True, False]\n", "        )\n", "        df_beauty[\"product_id3\"] = df_beauty.apply(\n", "            lambda row: {\n", "                \"collection\": row[\"collection\"],\n", "                \"subtitle\": row[\"subtitle\"],\n", "                \"score\": row[\"score\"],\n", "            },\n", "            axis=1,\n", "        )\n", "        df_beauty = df_beauty.drop(columns=[\"collection\", \"subtitle\", \"score\"])\n", "        df_beauty = df_beauty.groupby(\"user_id\").product_id3.apply(list).reset_index()\n", "        print(\"BYB Beauty: \", df_beauty.shape)\n", "\n", "        ### hooks\n", "\n", "        df_hooks[\"collection\"] = df_hooks[\"collection\"].apply(clean_string)\n", "        df_hooks = ranking(df_hooks, \"hook\", \"all\")\n", "        df_hooks = df_hooks[[\"user_id\", \"collection\", \"score\"]].sort_values(\n", "            by=[\"user_id\", \"score\"], ascending=[True, False]\n", "        )\n", "        df_hooks[\"product_id4\"] = df_hooks.apply(\n", "            lambda row: {\"collection\": row[\"collection\"], \"score\": row[\"score\"]}, axis=1\n", "        )\n", "        df_hooks = df_hooks.groupby(\"user_id\").product_id4.apply(list).reset_index()\n", "\n", "        ### ESP L1\n", "        # user_l1s['collection'] = user_l1s.apply(lambda row:  \"empty_search_l1_category \" + str(row['l1_category_id']), axis=1)\n", "        df_user_l1s = user_l1s[user_l1s[\"user_id\"].isin(user_group)]\n", "        df_user_l1s[\"product_id5\"] = df_user_l1s.apply(\n", "            lambda row: {\"collection\": row[\"collection\"], \"score\": row[\"score\"]}, axis=1\n", "        )\n", "        df_user_l1s = df_user_l1s.groupby(\"user_id\").product_id5.apply(list).reset_index()\n", "        print(\"ESP L1: \", df_user_l1s.shape)\n", "\n", "        tot = (\n", "            df_byb.merge(df_kids, how=\"outer\")\n", "            .merge(df_elecs, how=\"outer\")\n", "            .merge(df_beauty, how=\"outer\")\n", "            .merge(df_hooks, how=\"outer\")\n", "            .merge(df_user_l1s, how=\"outer\")\n", "        )\n", "\n", "        del df_kids, df_elecs, df_hooks, df_beauty, df_byb, df_user_l1s\n", "        gc.collect()\n", "\n", "        tot[\"product_id\"] = tot[\"product_id\"].fillna(\"\").apply(list)\n", "        tot[\"product_id1\"] = tot[\"product_id1\"].fillna(\"\").apply(list)\n", "        tot[\"product_id2\"] = tot[\"product_id2\"].fillna(\"\").apply(list)\n", "        tot[\"product_id3\"] = tot[\"product_id3\"].fillna(\"\").apply(list)\n", "        tot[\"product_id4\"] = tot[\"product_id4\"].fillna(\"\").apply(list)\n", "        tot[\"product_id5\"] = tot[\"product_id5\"].fillna(\"\").apply(list)\n", "\n", "        tot[\"product_id\"] = tot.apply(\n", "            lambda row: {\n", "                \"because_you_bought\": row[\"product_id\"],\n", "                \"usecase_hook\": row[\"product_id4\"],\n", "                \"usecase\": [],\n", "                \"because_you_bought_beauty\": row[\"product_id3\"],\n", "                \"usecase_hook_beauty\": [],\n", "                \"usecase_beauty\": [],\n", "                \"because_you_bought_kids\": row[\"product_id1\"],\n", "                \"usecase_hook_kids\": [],\n", "                \"usecase_kids\": [],\n", "                \"because_you_bought_electronics\": row[\"product_id2\"],\n", "                \"usecase_hook_electronics\": [],\n", "                \"usecase_electronics\": [],\n", "                \"because_you_bought_premium\": [],\n", "                \"usecase_hook_premium\": [],\n", "                \"usecase_premium\": [],\n", "                \"because_you_bought_gifting\": [],\n", "                \"usecase_hook_gifting\": [],\n", "                \"usecase_gifting\": [],\n", "                \"usecase_hook_empty_search\": row[\"product_id5\"],\n", "            },\n", "            axis=1,\n", "        )\n", "\n", "        tot = tot[[\"user_id\", \"product_id\"]]\n", "        tot[\"user_id\"] = tot[\"user_id\"].astype(int)\n", "        kafka_dict = tot.to_dict(orient=\"records\")\n", "\n", "        tot = []\n", "        del tot\n", "        gc.collect()\n", "\n", "        print(len(kafka_dict))\n", "\n", "        def np_encoder(object):\n", "            if isinstance(object, np.generic):\n", "                return object.item()\n", "\n", "        entity_column = \"user_id\"\n", "        entity_name = \"user\"\n", "        context = \"byb_group_ranking\"\n", "        ctx_value_col = \"product_id\"\n", "\n", "        push_to_kafka(\n", "            entities=[f\"{entity_name}:{i[entity_column]}\" for i in kafka_dict],\n", "            context=context,\n", "            ctx_properties=[\n", "                {\"ctx_value\": json.dumps(i[ctx_value_col], default=np_encoder)} for i in kafka_dict\n", "            ],\n", "            dry_run=False,\n", "        )\n", "        temp = pd.DataFrame({\"chunk\": [k]})\n", "        to_sheets(temp, \"1k8pNkI7GjEGOul5aVeOcOb06fYBeLT43wkwPKfACFLI\", \"byb_chunk\")\n", "temp = pd.DataFrame({\"chunk\": [-1]})\n", "to_sheets(temp, \"1k8pNkI7GjEGOul5aVeOcOb06fYBeLT43wkwPKfACFLI\", \"byb_chunk\")"]}, {"cell_type": "code", "execution_count": null, "id": "c0596ec9-b080-43dc-b512-da8b899e0202", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b3a93c95-0cef-46a8-bece-4b3e46faaec5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}