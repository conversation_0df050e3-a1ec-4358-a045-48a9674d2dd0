alert_configs:
  slack:
  - channel: bl-personalisation-dag-failures
catchup: false
dag_name: because_you_bought
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: ultra-high-mem
    node_type: od
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
  retries: 2
  retry_delay_in_seconds: 15
owner:
  email: <EMAIL>
  slack_id: U03RQJY8HAT
path: data_science/because_you_bought/etl/because_you_bought
paused: false
pool: data_science_pool
project_name: because_you_bought
schedule:
  end_date: '2025-07-31T00:00:00'
  interval: 30 21 * * *
  start_date: '2025-06-24T00:00:00'
schedule_type: fixed
sla: 35 minutes
support_files:
- kafka_utils.py
- AffinityScore.py
tags: []
template_name: notebook
version: 15