{"cells": [{"cell_type": "code", "execution_count": null, "id": "6ad1051a-d8b6-4e1a-bcfe-fcfbbbe6d38c", "metadata": {}, "outputs": [], "source": ["!pip install openpyxl"]}, {"cell_type": "code", "execution_count": null, "id": "4fbf20bf-1804-4dd7-9676-bb73cecc3e30", "metadata": {}, "outputs": [], "source": ["!pip install -q catboost\n", "!pip install -q joblib\n", "!pip install -q pytz\n", "!pip install h3==3.7.6\n", "!optuna==3.6.1\n", "!pip install haversine"]}, {"cell_type": "code", "execution_count": null, "id": "b5594702-56b6-46a0-aaee-4ce078ac9abc", "metadata": {}, "outputs": [], "source": ["! pip install shap"]}, {"cell_type": "code", "execution_count": null, "id": "ec5aea4c-b4a0-4c72-b82e-41c0e7b56248", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "\n", "con_trino = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "ef38cb8b-e9c6-4100-bc2f-46a8b13e1001", "metadata": {}, "outputs": [], "source": ["import asyncio\n", "import concurrent.futures\n", "import datetime as dt\n", "import os\n", "import threading\n", "import time\n", "from contextlib import contextmanager\n", "from datetime import datetime\n", "\n", "import boto3\n", "import pytz\n", "import sqlalchemy as sqla\n", "from sqlalchemy.dialects import postgresql\n", "from sqlalchemy.ext.automap import automap_base\n", "from sqlalchemy.orm import sessionmaker\n", "import gc\n", "\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "from catboost import CatBoostRegressor\n", "from sklearn.metrics import mean_absolute_error\n", "\n", "import statistics\n", "\n", "from collections import OrderedDict"]}, {"cell_type": "code", "execution_count": null, "id": "a9fcb0d3-2324-44a8-8d11-d1e5f070a46a", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)\n", "pd.set_option(\"display.max_rows\", None)\n", "pd.set_option(\"display.float_format\", lambda x: \"%.5f\" % x)\n", "pd.set_option(\"display.max_colwidth\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "41bfbcc0-956e-4a3a-ab1d-02dba2e8797c", "metadata": {}, "outputs": [], "source": ["trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "5bf1454f-ebda-4ccf-9de1-39715c2da350", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import json"]}, {"cell_type": "code", "execution_count": null, "id": "02d9c28c-f91e-4fd4-8a9f-ebb10d4268bb", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "c66ee34a-a8fe-4e01-9104-0936f1a53f69", "metadata": {}, "outputs": [], "source": ["cwd"]}, {"cell_type": "code", "execution_count": null, "id": "5d870fac-d9a7-49d5-9374-8c3399744703", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "d688a897-d65d-4557-8c74-222ce5f3c731", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "a6f1aeb9-719d-465a-b4fc-b3cbe610bd75", "metadata": {}, "source": ["## Reading Training,validation data for model training"]}, {"cell_type": "code", "execution_count": null, "id": "1507807c-edd0-471f-b37a-a9ed4c17027c", "metadata": {}, "outputs": [], "source": ["s3_bucket = \"prod-dse-projects\"\n", "training_data_local_path = \"training_data_picking_model_blinkit.parquet\"\n", "pb.from_s3(\n", "    s3_bucket,\n", "    \"A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Blinkit/picking_model/training_data/training_data_picking_model_blinkit.parquet\",\n", "    training_data_local_path,\n", ")\n", "\n", "training_data_blinkit = pd.read_parquet(training_data_local_path, engine=\"pyarrow\")\n", "\n", "\n", "s3_bucket = \"prod-dse-projects\"\n", "validation_data_local_path = \"validation_data_picking_model_blinkit.parquet\"\n", "pb.from_s3(\n", "    s3_bucket,\n", "    \"A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Blinkit/picking_model/validation_data/validation_data_picking_model_blinkit.parquet\",\n", "    validation_data_local_path,\n", ")\n", "\n", "validation_data_blinkit = pd.read_parquet(validation_data_local_path, engine=\"pyarrow\")\n", "\n", "\n", "s3_bucket = \"prod-dse-projects\"\n", "test_data_local_path = \"test_data_picking_model_blinkit.parquet\"\n", "pb.from_s3(\n", "    s3_bucket,\n", "    \"A<PERSON><PERSON><PERSON>_<PERSON>/ETA_models/Blinkit/picking_model/test_data/test_data_picking_model_blinkit.parquet\",\n", "    test_data_local_path,\n", ")\n", "\n", "test_data_blinkit = pd.read_parquet(test_data_local_path, engine=\"pyarrow\")"]}, {"cell_type": "code", "execution_count": null, "id": "f1fbcf35-ef6c-44fe-9410-df775704a5e5", "metadata": {}, "outputs": [], "source": ["training_data_blinkit.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "a51eb286-1810-4d5b-b100-5d5bd945dc97", "metadata": {}, "outputs": [], "source": ["validation_data_blinkit.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "2b9a9166-8efd-4c8c-beba-443d8eb9d4b3", "metadata": {}, "outputs": [], "source": ["print(training_data_blinkit.shape)\n", "print(validation_data_blinkit.shape)\n", "print(test_data_blinkit.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "b5a50861-d2ef-4f04-8872-7d1792d911a0", "metadata": {}, "outputs": [], "source": ["training_data_blinkit.head()"]}, {"cell_type": "code", "execution_count": null, "id": "17cb172f-34c5-4618-9bc7-05559ea5fa38", "metadata": {}, "outputs": [], "source": ["print(training_data_blinkit[\"date\"].min())\n", "print(training_data_blinkit[\"date\"].max())\n", "\n", "print(validation_data_blinkit[\"date\"].min())\n", "print(validation_data_blinkit[\"date\"].max())\n", "\n", "print(test_data_blinkit[\"date\"].min())\n", "print(test_data_blinkit[\"date\"].max())"]}, {"cell_type": "code", "execution_count": null, "id": "e131a743-4c9f-4d55-8253-98c07a7ed66f", "metadata": {}, "outputs": [], "source": ["categorical_feature = [\n", "    \"frontend_merchant_id\",\n", "    \"city_name\",\n", "    \"order_weight_bucket\",\n", "    \"hour_of_day_bucket\",\n", "]\n", "continous_feature = [\n", "    \"distinct_items_ordered\",\n", "    \"total_cold_items_ordered\",\n", "    \"total_items_quantity_ordered\",\n", "    \"total_distinct_l0_category\",\n", "    \"total_distinct_l1_category\",\n", "    \"total_distinct_ptype\",\n", "    \"mean_last_1_day\",\n", "    \"mean_last_3_days\",\n", "    \"mean_last_7_days\",\n", "]\n", "features = continous_feature + categorical_feature\n", "prediction_feature = [\"picking_time\"]\n", "full_features = features + prediction_feature"]}, {"cell_type": "code", "execution_count": null, "id": "ea4f3e6c-03fb-4e17-a832-79ee2f3a5cbd", "metadata": {}, "outputs": [], "source": ["training_data_blinkit = training_data_blinkit.dropna(\n", "    subset=[\n", "        \"distinct_items_ordered\",\n", "        \"total_cold_items_ordered\",\n", "        \"total_items_quantity_ordered\",\n", "        \"total_distinct_l0_category\",\n", "        \"total_distinct_l1_category\",\n", "        \"total_distinct_ptype\",\n", "        \"mean_last_1_day\",\n", "        \"mean_last_3_days\",\n", "        \"mean_last_7_days\",\n", "        \"frontend_merchant_id\",\n", "        \"city_name\",\n", "        \"order_weight_bucket\",\n", "        \"hour_of_day_bucket\",\n", "        \"picking_time\",\n", "    ]\n", ")\n", "\n", "\n", "validation_data_blinkit = validation_data_blinkit.dropna(\n", "    subset=[\n", "        \"distinct_items_ordered\",\n", "        \"total_cold_items_ordered\",\n", "        \"total_items_quantity_ordered\",\n", "        \"total_distinct_l0_category\",\n", "        \"total_distinct_l1_category\",\n", "        \"total_distinct_ptype\",\n", "        \"mean_last_1_day\",\n", "        \"mean_last_3_days\",\n", "        \"mean_last_7_days\",\n", "        \"frontend_merchant_id\",\n", "        \"city_name\",\n", "        \"order_weight_bucket\",\n", "        \"hour_of_day_bucket\",\n", "        \"picking_time\",\n", "    ]\n", ")\n", "\n", "\n", "test_data_blinkit = test_data_blinkit.dropna(\n", "    subset=[\n", "        \"distinct_items_ordered\",\n", "        \"total_cold_items_ordered\",\n", "        \"total_items_quantity_ordered\",\n", "        \"total_distinct_l0_category\",\n", "        \"total_distinct_l1_category\",\n", "        \"total_distinct_ptype\",\n", "        \"mean_last_1_day\",\n", "        \"mean_last_3_days\",\n", "        \"mean_last_7_days\",\n", "        \"frontend_merchant_id\",\n", "        \"city_name\",\n", "        \"order_weight_bucket\",\n", "        \"hour_of_day_bucket\",\n", "        \"picking_time\",\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "754f0c6b-627e-4f41-97d8-205bf7558ad4", "metadata": {}, "outputs": [], "source": ["print(training_data_blinkit.shape)\n", "print(validation_data_blinkit.shape)\n", "print(test_data_blinkit.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "46475edc-7d66-4d85-a267-42c6048d4d85", "metadata": {}, "outputs": [], "source": ["for col in categorical_feature:\n", "    training_data_blinkit[col] = training_data_blinkit[col].astype(\"category\")\n", "    validation_data_blinkit[col] = validation_data_blinkit[col].astype(\"category\")\n", "    test_data_blinkit[col] = test_data_blinkit[col].astype(\"category\")"]}, {"cell_type": "code", "execution_count": null, "id": "8d7131d7-ed9e-42ee-8023-09e6b7a28cfb", "metadata": {}, "outputs": [], "source": ["model_params = {\n", "    \"n_estimators\": 1500,\n", "    \"learning_rate\": 0.0110775870446307,\n", "    \"l2_leaf_reg\": 1,\n", "    \"depth\": 7,\n", "    \"loss_function\": \"MAE\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "ed80553b-30c4-44c2-8760-0188e3453574", "metadata": {}, "outputs": [], "source": ["def train_catboost(\n", "    tr_data,\n", "    val_data,\n", "    categorical_features,\n", "    continous_features,\n", "    params,\n", "    dependent_variable=\"picking_time\",\n", "):\n", "\n", "    features = continous_features + categorical_features\n", "\n", "    model = CatBoostRegressor(\n", "        approx_on_full_history=False,\n", "        allow_writing_files=True,\n", "        train_dir=\"/tmp/catboost_logs\",\n", "        **params,\n", "        cat_features=categorical_features,\n", "        custom_metric=[\"MAE\", \"R2\", \"RMSE\"],\n", "        save_snapshot=True,\n", "        verbose=200,\n", "    )\n", "\n", "    model.fit(\n", "        tr_data[features],\n", "        tr_data[dependent_variable],\n", "        eval_set=(\n", "            val_data[features],\n", "            val_data[dependent_variable],\n", "        ),\n", "        use_best_model=True,\n", "    )\n", "\n", "    return model"]}, {"cell_type": "code", "execution_count": null, "id": "fa5aad62-0aa5-4270-9b36-1191a02f0f56", "metadata": {}, "outputs": [], "source": ["model = train_catboost(\n", "    training_data_blinkit,\n", "    validation_data_blinkit,\n", "    categorical_feature,\n", "    continous_feature,\n", "    model_params,\n", "    dependent_variable=prediction_feature,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a8455ab2-f65e-4494-9456-a9803e7cf75b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "212d2c23-424e-414d-b658-c62db22094a7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "eab38f92-01b9-401a-867d-201dd15af77f", "metadata": {}, "source": ["## Shap values"]}, {"cell_type": "code", "execution_count": null, "id": "dba2cff5-8811-4f4c-905d-194bc7ab8036", "metadata": {}, "outputs": [], "source": ["import shap\n", "import joblib\n", "from joblib import load\n", "import pandas as pd\n", "\n", "\n", "explainer = shap.Explainer(model)\n", "shap_values = explainer(training_data_blinkit[features])\n", "\n", "# Summary plot\n", "shap.summary_plot(shap_values, training_data_blinkit[features])"]}, {"cell_type": "code", "execution_count": null, "id": "8d015fe0-6325-4d3b-a57d-534f236b366c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "46002dca-cfd7-432d-a9cc-7a13dec06cdc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "a9b37d00-12b0-4695-94a1-bfc536c4dd08", "metadata": {}, "source": ["## Evaluation"]}, {"cell_type": "code", "execution_count": null, "id": "21750715-ef9e-453f-9235-69b189be55a7", "metadata": {}, "outputs": [], "source": ["train_prediction = model.predict(training_data_blinkit[features])\n", "validation_prediction = model.predict(validation_data_blinkit[features])\n", "test_prediction = model.predict(test_data_blinkit[features])\n", "\n", "training_data_blinkit[\"predicted_picking_time\"] = train_prediction\n", "validation_data_blinkit[\"predicted_picking_time\"] = validation_prediction\n", "test_data_blinkit[\"predicted_picking_time\"] = test_prediction"]}, {"cell_type": "code", "execution_count": null, "id": "12a70f37-9fdc-4cd5-b5d5-83a3f5340aa8", "metadata": {}, "outputs": [], "source": ["model_train_mae = mean_absolute_error(training_data_blinkit.picking_time, train_prediction)\n", "model_validation_mae = mean_absolute_error(\n", "    validation_data_blinkit.picking_time, validation_prediction\n", ")\n", "model_test_mae = mean_absolute_error(test_data_blinkit.picking_time, test_prediction)\n", "\n", "\n", "model_train_me = np.mean(training_data_blinkit.picking_time - train_prediction)\n", "model_validation_me = np.mean(validation_data_blinkit.picking_time - validation_prediction)\n", "model_test_me = np.mean(test_data_blinkit.picking_time - test_prediction)\n", "\n", "\n", "print(\n", "    \"Model MAE:\",\n", "    model_train_mae,\n", "    model_validation_mae,\n", "    model_test_mae,\n", ")\n", "\n", "print(\n", "    \"Model ME:\",\n", "    model_train_me,\n", "    model_validation_me,\n", "    model_test_me,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c13b3669-d03f-4076-b3d2-fb436a280ad5", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    \"bl-personalization-notifications\",\n", "    f\"Picking Time Model:\\n\\tTrain MAE: {model_train_mae}\\n\\tValidation MAE: {model_validation_mae}\\n\\tTest MAE: {model_test_mae}\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9591cde4-e1d0-42fa-8fc3-7e233158cae6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f4b1954a-11c2-449b-b62e-4cc7517f32b5", "metadata": {}, "outputs": [], "source": ["# WITH BUSINESS METRIC\n", "import numpy as np\n", "from sklearn.metrics import mean_absolute_error, mean_squared_error\n", "\n", "\n", "def calculate_business_metrics(y_true, y_pred, threshold=0.5):\n", "    \"\"\"Calculate business compliance and accuracy metrics\"\"\"\n", "    # Business compliance +2 (actual must be at most 2 mins more than predicted)\n", "    compliance_mask = (y_true - y_pred) <= threshold\n", "\n", "    business_compliance = np.mean(compliance_mask) * 100\n", "\n", "    # Business accuracy ±2 (actual must at most have 2 mins deviation from predicted)\n", "    accuracy_mask = np.abs(y_true - y_pred) <= threshold\n", "    business_accuracy = np.mean(accuracy_mask) * 100\n", "\n", "    return business_compliance, business_accuracy"]}, {"cell_type": "code", "execution_count": null, "id": "2ca609c8-fffa-45b9-a05d-a7d02aafbead", "metadata": {}, "outputs": [], "source": ["calculate_business_metrics(validation_data_blinkit.picking_time, validation_prediction)"]}, {"cell_type": "code", "execution_count": null, "id": "7380d976-4b73-4def-a9b1-322322e2065a", "metadata": {}, "outputs": [], "source": ["calculate_business_metrics(test_data_blinkit.picking_time, test_prediction)"]}, {"cell_type": "code", "execution_count": null, "id": "ad8a6eda-1f12-4741-81db-5621bdcc129b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "a0cecbe1-9e78-4483-8dd8-5a02e8de6b30", "metadata": {}, "source": ["## Pushing <PERSON> Metada<PERSON>"]}, {"cell_type": "code", "execution_count": null, "id": "580019d7-2825-4a7a-a17e-97fff9caea21", "metadata": {}, "outputs": [], "source": ["from catboost import CatBoostRegressor\n", "\n", "# model.save_model(\"model.cbm\")\n", "model.save_model(\"model.cbm\", format=\"cbm\")"]}, {"cell_type": "code", "execution_count": null, "id": "b27681e9-fc83-4e47-92a3-402efa94d482", "metadata": {}, "outputs": [], "source": ["model_version = 2.0\n", "model_subid = datetime.now().strftime(\"%Y%m%d%H%M\")\n", "\n", "model_name = \"se_picking_time_estimation\"\n", "model_id = \"{}_v{}_{}\".format(model_name, model_version, model_subid)\n", "model_type = \"catboost_regressor\""]}, {"cell_type": "code", "execution_count": null, "id": "d09001ec-045d-455e-9d58-db606c82fc85", "metadata": {}, "outputs": [], "source": ["model_id"]}, {"cell_type": "code", "execution_count": null, "id": "3823ddde-f25f-41d8-b4f0-8cdcbbae1568", "metadata": {}, "outputs": [], "source": ["model_logs = {\n", "    \"training_data_start_date\": training_data_blinkit.date.min(),\n", "    \"training_data_end_date\": training_data_blinkit.date.max(),\n", "    \"validation_data_start_date\": validation_data_blinkit.date.min(),\n", "    \"validation_data_end_date\": validation_data_blinkit.date.max(),\n", "    \"test_data_start_date\": test_data_blinkit.date.min(),\n", "    \"test_data_end_date\": test_data_blinkit.date.max(),\n", "    \"model_id\": model_id,\n", "    \"model_name\": model_name,\n", "    \"model_version\": model_version,\n", "    \"model_subid\": model_subid,\n", "    \"model_type\": model_type,\n", "    \"model_train_mae\": round(model_train_mae, 2),\n", "    \"model_validation_mae\": round(model_validation_mae, 2),\n", "    \"model_test_mae\": round(model_test_mae, 2),\n", "    \"baseline_train_mae\": round(model_train_mae, 2),\n", "    \"baseline_validation_mae\": round(model_validation_mae, 2),\n", "    \"baseline_test_mae\": round(model_test_mae, 2),\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "d2aa9820-db78-4bba-a2ed-a87996cc27fc", "metadata": {}, "outputs": [], "source": ["model_logs"]}, {"cell_type": "code", "execution_count": null, "id": "d4b617a8-8369-4213-8208-8a8b8f25d39d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "863bce1a-d882-4468-b134-0e73e0726a92", "metadata": {}, "outputs": [], "source": ["BUCKET_NAME = \"prod-dse-projects\"\n", "MODEL_CLOUDPATH = \"/eta_prediction/picking_time_estimation/models/{}/{}/model.cbm\".format(\n", "    f\"v{model_version}\", model_subid\n", ")\n", "MODEL_METADATA_CLOUDPATH = (\n", "    \"/eta_prediction/picking_time_estimation/models/{}/{}/metadata.json\".format(\n", "        f\"v{model_version}\", model_subid\n", "    )\n", ")\n", "MODEL_POINTER_CLOUDPATH = \"/eta_prediction/picking_time_estimation/models/current.json\"\n", "\n", "MODEL_LOCALPATH = \"model.cbm\"\n", "MODEL_POINTER_LOCALPATH = \"current.json\"\n", "MODEL_METADATA_LOCALPATH = \"metadata.json\"\n", "\n", "\n", "AUTHOR = \"<EMAIL>\""]}, {"cell_type": "code", "execution_count": null, "id": "a6ce972b-b9d4-455e-8de1-f1c0445e644a", "metadata": {}, "outputs": [], "source": ["print(\"model cloud path:\", MODEL_CLOUDPATH)\n", "print(\"model metadata path:\", MODEL_METADATA_CLOUDPATH)\n", "print(\"model pointer:\", MODEL_POINTER_CLOUDPATH)"]}, {"cell_type": "code", "execution_count": null, "id": "ff226729-59c8-475a-b8c3-4c6d2cffd5ad", "metadata": {}, "outputs": [], "source": ["model_pointer = {\n", "    \"created_at\": datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "    \"created_by\": AUTH<PERSON>,\n", "    \"bucket_name\": BUCKET_NAME,\n", "    \"model_path\": MODEL_CLOUDPATH,\n", "    \"metadata_path\": MODEL_METADATA_CLOUDPATH,\n", "    \"model_id\": model_id,\n", "    \"model_name\": model_name,\n", "    \"model_version\": model_version,\n", "    \"model_type\": model_type,\n", "}\n", "\n", "model_metadata = model_pointer.copy()\n", "model_metadata[\"darkstore_ids\"] = training_data_blinkit.frontend_merchant_id.unique().tolist()\n", "model_metadata[\"model_params\"] = model_params\n", "model_metadata[\"model_test_mae\"] = model_logs[\"model_test_mae\"]\n", "model_metadata[\"baseline_test_mae\"] = model_logs[\"model_test_mae\"]\n", "\n", "\n", "model_logs[\"s3_bucket\"] = BUCKET_NAME\n", "model_logs[\"s3_path\"] = MODEL_CLOUDPATH"]}, {"cell_type": "code", "execution_count": null, "id": "11b0e875-6370-48c5-945c-32a2472a7080", "metadata": {}, "outputs": [], "source": ["# # dump model locally\n", "# joblib.dump(clf, MODEL_LOCALPATH)\n", "\n", "# model metadata\n", "with open(MODEL_METADATA_LOCALPATH, \"w\") as fp:\n", "    json.dump(model_metadata, fp)\n", "\n", "# model pointer\n", "with open(MODEL_POINTER_LOCALPATH, \"w\") as fp:\n", "    json.dump(model_pointer, fp)"]}, {"cell_type": "code", "execution_count": null, "id": "6434f7e4-4fde-4fc7-9fe8-2fc9da00a1df", "metadata": {}, "outputs": [], "source": ["# push model to s3\n", "pb.to_s3(MODEL_LOCALPATH, BUCKET_NAME, MODEL_CLOUDPATH)\n", "\n", "# push metadata to s3 (darkstore ids trained on)\n", "pb.to_s3(MODEL_METADATA_LOCALPATH, BUCKET_NAME, MODEL_METADATA_CLOUDPATH)\n", "\n", "# push model pointer to s3 (current model filepath)\n", "pb.to_s3(MODEL_POINTER_LOCALPATH, BUCKET_NAME, MODEL_POINTER_CLOUDPATH)"]}, {"cell_type": "code", "execution_count": null, "id": "5f3a6d6b-5496-441e-9326-6977dadf09b8", "metadata": {}, "outputs": [], "source": ["model_logs_df = pd.DataFrame([OrderedDict(model_logs)])"]}, {"cell_type": "code", "execution_count": null, "id": "e2404e73-4dd6-46b4-bb21-3d78460a7a8e", "metadata": {}, "outputs": [], "source": ["def push_to_db(logs):\n", "\n", "    frame = logs.copy()\n", "    frame[\"updated_at\"] = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    frame = frame.astype({\"updated_at\": \"datetime64[ns]\"})\n", "    frame[\"status\"] = \"trained\"\n", "    column_dtypes = [\n", "        {\n", "            \"name\": \"training_data_start_date\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"training data start date\",\n", "        },\n", "        {\n", "            \"name\": \"training_data_end_date\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"training data end date\",\n", "        },\n", "        {\n", "            \"name\": \"validation_data_start_date\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"Validation data start date\",\n", "        },\n", "        {\n", "            \"name\": \"validation_data_end_date\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"validation data end date\",\n", "        },\n", "        {\n", "            \"name\": \"test_data_start_date\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"test data start date\",\n", "        },\n", "        {\n", "            \"name\": \"test_data_end_date\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"test data end date\",\n", "        },\n", "        {\n", "            \"name\": \"model_id\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"id of the generated model\",\n", "        },\n", "        {\n", "            \"name\": \"model_name\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"name of the model used for predicting the eta component\",\n", "        },\n", "        {\n", "            \"name\": \"model_version\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"version of the model\",\n", "        },\n", "        {\n", "            \"name\": \"model_subid\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"model subid is the date on which the model was generated\",\n", "        },\n", "        {\n", "            \"name\": \"model_type\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"type of the ml model\",\n", "        },\n", "        {\n", "            \"name\": \"model_train_mae\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"mean absolute error of model on training data\",\n", "        },\n", "        {\n", "            \"name\": \"model_validation_mae\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"mean absolute error of model on validation data\",\n", "        },\n", "        {\n", "            \"name\": \"model_test_mae\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"mean absolute error of model on test data\",\n", "        },\n", "        {\n", "            \"name\": \"baseline_train_mae\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"mean absolute error of baseline model on training data\",\n", "        },\n", "        {\n", "            \"name\": \"baseline_validation_mae\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"mean absolute error of baseline model on validation data\",\n", "        },\n", "        {\n", "            \"name\": \"baseline_test_mae\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"mean absolute error of baseline model on test data\",\n", "        },\n", "        {\n", "            \"name\": \"s3_bucket\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"bucket name where artifacts are stores\",\n", "        },\n", "        {\n", "            \"name\": \"s3_path\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"path inside the bucket where the model is stored\",\n", "        },\n", "        {\n", "            \"name\": \"updated_at\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"table updation timestamp\",\n", "        },\n", "        {\n", "            \"name\": \"status\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"save status for prod deployement\",\n", "        },\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"consumer_etls\",\n", "        \"table_name\": \"serviceability_picking_time_model_retraining_logs\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"model_id\"],\n", "        \"sortkey\": [\"model_id\", \"updated_at\"],\n", "        \"incremental_key\": \"updated_at\",\n", "        \"load_type\": \"upsert\",\n", "        \"table_description\": \"Picking time estimation retraining pipeline logs\",\n", "    }\n", "    pb.to_trino(frame[[i[\"name\"] for i in column_dtypes]], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "78306eac-b24b-473c-8b94-fe84bd3c9d7e", "metadata": {}, "outputs": [], "source": ["if len(model_logs_df) != 0:\n", "    push_to_db(model_logs_df)"]}, {"cell_type": "code", "execution_count": null, "id": "3e13e91e-1e53-4fbc-8032-5b2c9d64e6d2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "85c33f4a-1b12-49a7-a069-e9e53198cddb", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    \"bl-personalization-notifications\",\n", "    f\"Picking Time Model DAG run completed. Model meta data pushed into Trino\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d450ae44-c6e0-49cb-a958-4c488b6065ea", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}