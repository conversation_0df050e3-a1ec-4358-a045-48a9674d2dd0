alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: charge_back_real_time_device_payments_data
dag_type: etl
escalation_priority: low
execution_timeout: 2000
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: high-mem
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U087G237876
path: data_science/fraud_prevention_service/etl/charge_back_real_time_device_payments_data
paused: false
pool: data_science_pool
project_name: fraud_prevention_service
schedule:
  end_date: '2025-09-01T00:00:00'
  interval: 0 */6 * * *
  start_date: '2025-06-04T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 3
