{"cells": [{"cell_type": "code", "execution_count": null, "id": "ab584b19-f08f-4409-a4fa-e4f5cc6e1f42", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import pencilbox as pb\n", "import json\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "id": "5d40ae5d-85ed-4c5e-a9c9-275c33b5c5bf", "metadata": {}, "outputs": [], "source": ["CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "\n", "# constants\n", "\n", "PAYMENTS_TABLE_SCHEMA = \"interim\"\n", "PAYMENTS_TABLE_NAME = \"temp_payment_info_v5\"\n", "\n", "\n", "SEGMENT_TABLE_SCHEMA = \"segment_computed_traits\"\n", "SEGMENT_TABLE_NAME = \"chargeback_device_payments_net_v2\"\n", "\n", "INTERIM_TEMP_TABLE_SCHEMA = \"interim\"\n", "INTERIM_TEMP_TABLE_NAME = \"chargeback_device_temp_details_v2\"\n", "\n", "INTERIM_PAYMENT_DETAIL_SCHEMA = \"interim\"\n", "INTERIM_PAYMENT_DETAIL_TABLE_NAME = \"chargeback_device_payment_details_v2\""]}, {"cell_type": "code", "execution_count": null, "id": "3bffe72a-c4d3-4e0c-9d7b-a202fe49dfc3", "metadata": {}, "outputs": [], "source": ["INTERIM_TEMP_TABLE_DTYPE = [{\"name\": \"device_id\", \"type\": \"VARCHAR\", \"description\": \"customer_id\"}]\n", "PAYMENT_DETAIL_DTYPE = [\n", "    {\"name\": \"device_id\", \"type\": \"VARCHAR\", \"description\": \"customer_id\"},\n", "    {\"name\": \"payload\", \"type\": \"VARCHAR\", \"description\": \"VARCHAR\"},\n", "]\n", "\n", "SEGMENT_DTYPES = [\n", "    {\"name\": \"device_id\", \"type\": \"VARCHAR\", \"description\": \"customer_id\"},\n", "    {\"name\": \"payload\", \"type\": \"VARCHAR\", \"description\": \"VARCHAR\"},\n", "]\n", "PAYMENTS_DTYPES = [\n", "    {\"name\": \"id\", \"type\": \"BIGINT\", \"description\": \"id\"},\n", "    {\"name\": \"cart_id\", \"type\": \"BIGINT\", \"description\": \"cart_id\"},\n", "    {\"name\": \"payment\", \"type\": \"VARCHAR\", \"description\": \"payment\"},\n", "    {\"name\": \"status\", \"type\": \"VARCHAR\", \"description\": \"status\"},\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"DATE\", \"description\": \"insert_ds_ist\"},\n", "    {\"name\": \"device_id\", \"type\": \"VARCHAR\", \"description\": \"insert_ds_ist\"},\n", "    {\n", "        \"name\": \"company_user_id\",\n", "        \"type\": \"BIGINT\",\n", "        \"description\": \"company_user_id extracted from event_data\",\n", "    },\n", "    {\n", "        \"name\": \"payment_method_type\",\n", "        \"type\": \"VARCHAR\",\n", "        \"description\": \"payment_method_type extracted from event_data.txns[0]\",\n", "    },\n", "    {\n", "        \"name\": \"card_token\",\n", "        \"type\": \"VARCHAR\",\n", "        \"description\": \"card_token extracted from payment_source.fingerprint\",\n", "    },\n", "    {\n", "        \"name\": \"track_id\",\n", "        \"type\": \"VARCHAR\",\n", "        \"description\": \"track_id extracted from event_data.txns[0]\",\n", "    },\n", "    {\n", "        \"name\": \"payment_method_id\",\n", "        \"type\": \"BIGINT\",\n", "        \"description\": \"payment_method_id extracted from event_data.txns[0]\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "45e05bd0-61d6-4481-bca4-bdf5171d5a20", "metadata": {}, "outputs": [], "source": ["INTERIM_PAYMENTS_KWARGS = {\n", "    \"schema_name\": PAYMENTS_TABLE_SCHEMA,\n", "    \"table_name\": PAYMENTS_TABLE_NAME,\n", "    \"column_dtypes\": PAYMENTS_DTYPES,\n", "    \"primary_key\": [\"id\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"Chargeback Data for Customers\",\n", "}\n", "\n", "INTERIM_PAYMENTS_DETAILS_KWARGS = {\n", "    \"schema_name\": INTERIM_TEMP_TABLE_SCHEMA,\n", "    \"table_name\": INTERIM_TEMP_TABLE_NAME,\n", "    \"column_dtypes\": PAYMENT_DETAIL_DTYPE,\n", "    \"primary_key\": [\"device_id\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"Chargeback Data for Customers\",\n", "}\n", "\n", "SEGMENT_KWARGS = {\n", "    \"schema_name\": SEGMENT_TABLE_SCHEMA,\n", "    \"table_name\": SEGMENT_TABLE_NAME,\n", "    \"column_dtypes\": SEGMENT_DTYPES,\n", "    \"primary_key\": [\"device_id\"],\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"Complaints Data for Customers\",\n", "}\n", "\n", "INTERIM_TEMP_PAYMENTS_DETAILS_KWARGS = {\n", "    \"schema_name\": INTERIM_PAYMENT_DETAIL_SCHEMA,\n", "    \"table_name\": INTERIM_PAYMENT_DETAIL_TABLE_NAME,\n", "    \"column_dtypes\": INTERIM_TEMP_TABLE_DTYPE,\n", "    \"primary_key\": [\"device_id\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"Chargeback Data for Customers\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "f8d86105-bd9a-4266-97be-b712f3f4144c", "metadata": {}, "outputs": [], "source": ["QUERY_interim = f\"\"\"\n", "With order_base as (\n", "\n", "SELECT \n", "    device_id\n", "FROM\n", "    dwh.fact_sales_order_details\n", "WHERE \n", "    order_create_dt_ist\n", "BETWEEN\n", "    current_date-INTERVAL '60' DAY\n", "AND\n", "    current_date\n", "GROUP BY 1\n", ")\n", "Select * FROM order_base\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "eb12e9ed-f04e-454c-ae79-254fc1b7c735", "metadata": {}, "outputs": [], "source": ["pb.to_trino(QUERY_interim, **INTERIM_TEMP_PAYMENTS_DETAILS_KWARGS)"]}, {"cell_type": "code", "execution_count": null, "id": "685bf3e5-d063-49e7-bb07-1684a9d10773", "metadata": {}, "outputs": [], "source": ["QUERY_PAYMENT = f\"\"\"\n", "\n", "    \n", "SELECT\n", "\n", "    GR.id,\n", "    bc.device_id,\n", "    GR.cart_id,\n", "    GR.payment,\n", "    GR.status,\n", "    GR.insert_ds_ist,\n", "    TRY_CAST(json_extract_scalar(other_details,'$.event_data.company_user_id') AS BIGINT) AS company_user_id,\n", "    TRY_CAST(json_extract_scalar(other_details, '$.event_data.txns[0].payment_method_type') AS VARCHAR) AS payment_method_type,\n", "    TRY_CAST(JSON_EXTRACT(other_details,'$.payment_source.fingerprint') AS VARCHAR) AS card_token,\n", "    TRY_CAST(json_extract_scalar(other_details, '$.event_data.txns[0].payment_method_id') AS BIGINT) AS payment_method_id,\n", "    json_extract_scalar(other_details, '$.event_data.txns[0].track_id') AS track_id\n", "\n", "    \n", "FROM\n", "    payments_db.gr_payment GR\n", "JOIN \n", "    zomato.dynamodb.blinkit_cart bc\n", "ON \n", "    bc.cart_id=GR.cart_id\n", "WHERE   \n", "    insert_ds_ist >= CAST(current_date - INTERVAL'60'DAY AS VARCHAR)\n", "AND insert_ds_ist <= CAST(current_date AS varchar)\n", "AND\n", "    dt >= REPLACE(cast(current_date - INTERVAL'60'DAY AS VARCHAR),'-','')\n", "AND \n", "    bc.cart_id IS NOT NULL\n", "AND\n", "device_id\n", "IN \n", "(SELECT device_id FROM {INTERIM_PAYMENT_DETAIL_SCHEMA}.{INTERIM_PAYMENT_DETAIL_TABLE_NAME})\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "25265675-8f0b-4513-8625-a20ed67ce02b", "metadata": {}, "outputs": [], "source": ["pb.to_trino(QUERY_PAYMENT, **INTERIM_PAYMENTS_KWARGS)"]}, {"cell_type": "code", "execution_count": null, "id": "9d59ded7-48b8-4d9c-80c4-d8a505d5e5e5", "metadata": {}, "outputs": [], "source": ["Query_device = f\"\"\"\n", "WITH vpa_links AS\n", "(\n", "SELECT \n", "    TRY_CAST(id AS BIGINT) AS id,\n", "    company_user_id,\n", "    vpa\n", "FROM\n", "    zomato.btransactions_upi_vpas\n", "),\n", "\n", "\n", "payment_fails_last_1m AS\n", "(\n", "SELECT\n", "    p.device_id,\n", "\n", "    -- Card failures\n", "    COUNT(DISTINCT CASE WHEN payment_method_type = 'card' AND status = 'FAILED' THEN track_id END) AS device_card_txn_fails_last_1m,\n", "    array_agg(DISTINCT track_id) FILTER (\n", "        WHERE payment_method_type = 'card' AND status = 'FAILED' AND track_id IS NOT NULL\n", "    ) AS device_card_txn_fail_ids_last_1m_array_agg,\n", "    \n", "    COUNT(DISTINCT CASE WHEN payment_method_type = 'card' AND status = 'FAILED' THEN p.card_token END) AS device_card_token_fails_last_1m,\n", "    array_agg(DISTINCT p.card_token) FILTER (\n", "        WHERE payment_method_type = 'card' AND status = 'FAILED' AND p.card_token IS NOT NULL\n", "    ) AS device_card_token_fail_tokens_last_1m_array_agg,\n", "    \n", "    -- Generic cart-level failures\n", "    COUNT(DISTINCT CASE WHEN status = 'FAILED' THEN p.cart_id END) AS device_cart_fails_last_1m,\n", "    array_agg(DISTINCT track_id) FILTER (\n", "        WHERE status = 'FAILED' AND track_id IS NOT NULL\n", "    ) AS device_cart_fail_ids_last_1m_array_agg,\n", "    \n", "    -- UPI Collect failures\n", "    COUNT(DISTINCT CASE WHEN payment_method_type = 'upi_collect' AND status = 'FAILED' THEN track_id END) AS device_upic_txn_fails_last_1m,\n", "    array_agg(DISTINCT track_id) FILTER (\n", "        WHERE payment_method_type = 'upi_collect' AND status = 'FAILED' AND track_id IS NOT NULL\n", "    ) AS device_upic_txn_fail_ids_last_1m_array_agg,\n", "    \n", "    COUNT(DISTINCT CASE WHEN payment_method_type = 'upi_collect' AND status = 'FAILED' THEN p.cart_id END) AS device_upic_fails_last_1m,\n", "    array_agg(DISTINCT track_id) FILTER (\n", "        WHERE payment_method_type = 'upi_collect' AND status = 'FAILED' AND track_id IS NOT NULL\n", "    ) AS device_upic_fails_ids_last_1m_array_agg,\n", "    \n", "    COUNT(DISTINCT CASE WHEN payment_method_type = 'upi_collect' AND status = 'FAILED' THEN v.vpa END) AS device_upic_dist_last_1m,\n", "    array_agg(DISTINCT v.vpa) FILTER (\n", "        WHERE payment_method_type = 'upi_collect' AND status = 'FAILED' AND v.vpa IS NOT NULL\n", "    ) AS device_upic_fail_vpas_last_1m_array_agg,\n", "    \n", "    -- UPI Intent (non-collect) failures\n", "    COUNT(DISTINCT CASE WHEN payment_method_type = 'upi' AND status = 'FAILED' THEN cart_id END) AS device_upi_dist_last_1m,\n", "    array_agg(DISTINCT track_id) FILTER (\n", "        WHERE payment_method_type = 'upi' AND status = 'FAILED' AND track_id IS NOT NULL\n", "    ) AS device_upi_fail_cart_ids_last_1m_array_agg\n", "\n", "\n", "FROM\n", "    {PAYMENTS_TABLE_SCHEMA}.{PAYMENTS_TABLE_NAME} p\n", "    \n", "LEFT JOIN\n", "    vpa_links v ON p.payment_method_id = v.id \n", "WHERE\n", "    insert_ds_ist >= TRY_CAST(current_date - INTERVAL'30'DAY AS VARCHAR)\n", "AND \n", "    insert_ds_ist <= TRY_CAST(current_date AS varchar)\n", "    \n", "GROUP BY 1\n", "),\n", "\n", "\n", "payment_fails_last_2m AS\n", "(\n", "SELECT\n", "   p.device_id,\n", "\n", "    -- Card failures over 2 months\n", "    COUNT(DISTINCT CASE WHEN payment_method_type = 'card' AND status = 'FAILED' THEN track_id END) AS device_card_txn_fails_last_2m,\n", "    array_agg(DISTINCT track_id) FILTER (\n", "        WHERE payment_method_type = 'card' AND status = 'FAILED' AND track_id IS NOT NULL\n", "    ) AS device_card_txn_fail_ids_last_2m_array_agg,\n", "    \n", "    COUNT(DISTINCT CASE WHEN payment_method_type = 'card' AND status = 'FAILED' THEN p.card_token END) AS device_card_token_fails_last_2m,\n", "    array_agg(DISTINCT p.card_token) FILTER (\n", "        WHERE payment_method_type = 'card' AND status = 'FAILED' AND p.card_token IS NOT NULL\n", "    ) AS device_card_token_fail_tokens_last_2m_array_agg,\n", "    \n", "    -- Generic cart-level failures over 2 months\n", "    COUNT(DISTINCT CASE WHEN status = 'FAILED' THEN p.cart_id END) AS device_cart_fails_last_2m,\n", "    array_agg(DISTINCT track_id) FILTER (\n", "        WHERE status = 'FAILED' AND track_id IS NOT NULL\n", "    ) AS device_cart_fail_ids_last_2m_array_agg,\n", "    \n", "    -- UPI Collect failures over 2 months\n", "    COUNT(DISTINCT CASE WHEN payment_method_type = 'upi_collect' AND status = 'FAILED' THEN track_id END) AS device_upic_txn_fails_last_2m,\n", "    array_agg(DISTINCT track_id) FILTER (\n", "        WHERE payment_method_type = 'upi_collect' AND status = 'FAILED' AND track_id IS NOT NULL\n", "    ) AS device_upic_txn_fail_ids_last_2m_array_agg,\n", "    \n", "    COUNT(DISTINCT CASE WHEN payment_method_type = 'upi_collect' AND status = 'FAILED' THEN p.cart_id END) AS device_upic_fails_last_2m,\n", "    array_agg(DISTINCT track_id) FILTER (\n", "        WHERE payment_method_type = 'upi_collect' AND status = 'FAILED' AND track_id IS NOT NULL\n", "    ) AS device_upic_fails_ids_last_2m_array_agg,\n", "    \n", "    COUNT(DISTINCT CASE WHEN payment_method_type = 'upi_collect' AND status = 'FAILED' THEN v.vpa END) AS device_upic_dist_last_2m,\n", "    array_agg(DISTINCT v.vpa) FILTER (\n", "        WHERE payment_method_type = 'upi_collect' AND status = 'FAILED' AND v.vpa IS NOT NULL\n", "    ) AS device_upic_fail_vpas_last_2m_array_agg,\n", "    \n", "    -- UPI Intent failures over 2 months\n", "    COUNT(DISTINCT CASE WHEN payment_method_type = 'upi' AND status = 'FAILED' THEN cart_id END) AS device_upi_dist_last_2m,\n", "    array_agg(DISTINCT track_id) FILTER (\n", "        WHERE payment_method_type = 'upi' AND status = 'FAILED' AND track_id IS NOT NULL\n", "    ) AS device_upi_fail_cart_ids_last_2m_array_agg\n", "\n", "FROM\n", "    {PAYMENTS_TABLE_SCHEMA}.{PAYMENTS_TABLE_NAME} p\n", "LEFT JOIN\n", "    vpa_links v ON p.payment_method_id = v.id \n", "WHERE\n", "    insert_ds_ist >= TRY_CAST(current_date - INTERVAL'60'DAY AS VARCHAR)\n", "AND \n", "    insert_ds_ist <= TRY_CAST(current_date AS varchar)\n", "    \n", "GROUP BY 1\n", ")\n", "\n", "SELECT \n", "    ob.device_id,\n", "\n", "  JSON_OBJECT(\n", "    -- Scala<PERSON>: last 1 month\n", "    KEY 'device_card_txn_fails_last_1m' VALUE COALESCE(MAX(device_card_txn_fails_last_1m), 0),\n", "    KEY 'device_cart_fails_last_1m' VALUE COALESCE(MAX(device_cart_fails_last_1m), 0),\n", "    KEY 'device_card_token_fails_last_1m' VALUE COALESCE(MAX(device_card_token_fails_last_1m), 0),\n", "    KEY 'device_upic_txn_fails_last_1m' VALUE COALESCE(MAX(device_upic_txn_fails_last_1m), 0),\n", "    KEY 'device_upic_fails_last_1m' VALUE COALESCE(MAX(device_upic_fails_last_1m), 0),\n", "    KEY 'device_upic_dist_last_1m' VALUE COALESCE(MAX(device_upic_dist_last_1m), 0),\n", "    KEY 'device_upi_dist_fails_last_1m' VALUE COALESCE(MAX(device_upi_dist_last_1m), 0),\n", "\n", "    -- Arrays: last 1 month\n", "    KEY 'device_card_txn_fail_ids_last_1m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(device_card_txn_fail_ids_last_1m_array_agg) AS JSON), JSON '[]')),\n", "    KEY 'device_cart_fail_ids_last_1m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(device_cart_fail_ids_last_1m_array_agg) AS JSON), JSON '[]')),\n", "    KEY 'device_card_token_fail_tokens_last_1m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(device_card_token_fail_tokens_last_1m_array_agg) AS JSON), JSON '[]')),\n", "    KEY 'device_upic_txn_fail_ids_last_1m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(device_upic_txn_fail_ids_last_1m_array_agg) AS JSON), JSON '[]')),\n", "    KEY 'device_upic_fails_ids_last_1m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(device_upic_fails_ids_last_1m_array_agg) AS JSON), JSON '[]')),\n", "    KEY 'device_upic_fail_vpas_last_1m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(device_upic_fail_vpas_last_1m_array_agg) AS JSON), JSON '[]')),\n", "    KEY 'device_upi_fail_cart_ids_last_1m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(device_upi_fail_cart_ids_last_1m_array_agg) AS JSON), JSON '[]')),\n", "\n", "    -- <PERSON><PERSON><PERSON>: last 2 months\n", "    KEY 'device_card_txn_fails_last_2m' VALUE COALESCE(MAX(device_card_txn_fails_last_2m), 0),\n", "    KEY 'device_cart_fails_last_2m' VALUE COALESCE(MAX(device_cart_fails_last_2m), 0),\n", "    KEY 'device_card_token_fails_last_2m' VALUE COALESCE(MAX(device_card_token_fails_last_2m), 0),\n", "    KEY 'device_upic_txn_fails_last_2m' VALUE COALESCE(MAX(device_upic_txn_fails_last_2m), 0),\n", "    KEY 'device_upic_fails_last_2m' VALUE COALESCE(MAX(device_upic_fails_last_2m), 0),\n", "    KEY 'device_upic_dist_last_2m' VALUE COALESCE(MAX(device_upic_dist_last_2m), 0),\n", "    KEY 'device_upi_dist_fails_last_2m' VALUE COALESCE(MAX(device_upi_dist_last_2m), 0),\n", "\n", "    -- Arrays: last 2 months\n", "    KEY 'device_card_txn_fail_ids_last_2m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(device_card_txn_fail_ids_last_2m_array_agg) AS JSON), JSON '[]')),\n", "    KEY 'device_cart_fail_ids_last_2m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(device_cart_fail_ids_last_2m_array_agg) AS JSON), JSON '[]')),\n", "    KEY 'device_card_token_fail_tokens_last_2m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(device_card_token_fail_tokens_last_2m_array_agg) AS JSON), JSON '[]')),\n", "    KEY 'device_upic_txn_fail_ids_last_2m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(device_upic_txn_fail_ids_last_2m_array_agg) AS JSON), JSON '[]')),\n", "    KEY 'device_upic_fails_ids_last_2m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(device_upic_fails_ids_last_2m_array_agg) AS JSON), JSON '[]')),\n", "    KEY 'device_upic_fail_vpas_last_2m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(device_upic_fail_vpas_last_2m_array_agg) AS JSON), JSON '[]')),\n", "    KEY 'device_upi_fail_cart_ids_last_2m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(device_upi_fail_cart_ids_last_2m_array_agg) AS JSON), JSON '[]'))\n", "  ) AS payload\n", "\n", "FROM \n", "    {INTERIM_PAYMENT_DETAIL_SCHEMA}.{INTERIM_PAYMENT_DETAIL_TABLE_NAME} ob\n", "\n", "LEFT JOIN\n", "\n", "    payment_fails_last_1m p_1 \n", "ON\n", "    ob.device_id = p_1.device_id\n", "\n", "LEFT JOIN payment_fails_last_2m p_2 \n", "  ON ob.device_id = p_2.device_id\n", "GROUP BY 1\n", "\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "4a68db08-6b62-4299-bbcd-36b04fe09555", "metadata": {}, "outputs": [], "source": ["pb.to_trino(Query_device, **INTERIM_PAYMENTS_DETAILS_KWARGS)"]}, {"cell_type": "code", "execution_count": null, "id": "6a68b415-c397-4ae6-8a24-a863f4f12b13", "metadata": {}, "outputs": [], "source": ["query_2 = f\"\"\"\n", "\n", "SELECT\n", "    device_id\n", "FROM\n", "    {SEGMENT_TABLE_SCHEMA}.{SEGMENT_TABLE_NAME}\n", "WHERE\n", "    device_id NOT IN (SELECT customer_id FROM {INTERIM_TEMP_TABLE_SCHEMA}.{INTERIM_TEMP_TABLE_NAME} )\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "b246b294-3235-4e03-8149-fac90cbde13d", "metadata": {}, "outputs": [], "source": ["data_json = str(\n", "    json.dumps(\n", "        {\n", "            \"device_upi_dist_fails_last_2m\": 0,\n", "            \"device_upic_txn_fail_ids_last_1m_array_agg\": \"[]\",\n", "            \"device_upic_dist_last_2m\": 0,\n", "            \"device_upic_fails_last_1m\": 0,\n", "            \"device_upic_txn_fails_last_2m\": 0,\n", "            \"device_card_txn_fails_last_1m\": 0,\n", "            \"device_cart_fails_last_1m\": 0,\n", "            \"device_card_token_fails_last_1m\": 0,\n", "            \"device_card_token_fail_tokens_last_2m_array_agg\": \"[]\",\n", "            \"device_upic_txn_fail_ids_last_2m_array_agg\": \"[]\",\n", "            \"device_card_txn_fails_last_2m\": 0,\n", "            \"device_upi_fail_cart_ids_last_1m_array_agg\": \"[]\",\n", "            \"device_upic_fails_ids_last_1m_array_agg\": \"[]\",\n", "            \"device_card_txn_fail_ids_last_2m_array_agg\": \"[]\",\n", "            \"device_cart_fail_ids_last_1m_array_agg\": \"[]\",\n", "            \"device_card_txn_fail_ids_last_1m_array_agg\": \"[]\",\n", "            \"device_upic_dist_last_1m\": 0,\n", "            \"device_upi_fail_cart_ids_last_2m_array_agg\": \"[]\",\n", "            \"device_upi_dist_fails_last_1m\": 0,\n", "            \"device_upic_txn_fails_last_1m\": 0,\n", "            \"device_upic_fails_ids_last_2m_array_agg\": \"[]\",\n", "            \"device_cart_fail_ids_last_2m_array_agg\": \"[]\",\n", "            \"device_cart_fails_last_2m\": 0,\n", "            \"device_card_token_fails_last_2m\": 0,\n", "            \"device_upic_fails_last_2m\": 0,\n", "            \"device_upic_fail_vpas_last_1m_array_agg\": \"[]\",\n", "            \"device_upic_fail_vpas_last_2m_array_agg\": \"[]\",\n", "            \"device_card_token_fail_tokens_last_1m_array_agg\": \"[]\",\n", "        }\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2bcb2719-1a2b-43a2-91c1-a3b10d321bf6", "metadata": {}, "outputs": [], "source": ["try:\n", "    df = pd.read_sql(query_2, CON_TRINO)\n", "\n", "    if len(df) > 0:\n", "        df[\"payload\"] = data_json\n", "        pb.to_trino(df, **SEGMENT_KWARGS)\n", "    else:\n", "        print(\"No data to be updated.\")\n", "\n", "except Exception as e:\n", "    print(\"Sync Failed.\")\n", "    print(e)"]}, {"cell_type": "code", "execution_count": null, "id": "95eb5952-fac9-4474-8e7e-59bab2c94864", "metadata": {}, "outputs": [], "source": ["pb.to_trino(Query_device, **SEGMENT_KWARGS)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}