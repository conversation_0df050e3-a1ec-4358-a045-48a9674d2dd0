{"cells": [{"cell_type": "code", "execution_count": null, "id": "618f8f88-bddd-4242-a3ba-340a9c21ddd1", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import pencilbox as pb\n", "import json\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "id": "c2741fe1-d194-4346-a415-05dda47b35eb", "metadata": {}, "outputs": [], "source": ["CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "# constants\n", "\n", "PAYMENTS_TABLE_SCHEMA = \"interim\"\n", "PAYMENTS_TABLE_NAME = \"temp_payment_info\"\n", "\n", "\n", "SEGMENT_TABLE_SCHEMA = \"segment_computed_traits\"\n", "SEGMENT_TABLE_NAME = \"chargeback_customer_payments_net_v1\"\n", "\n", "INTERIM_TEMP_TABLE_SCHEMA = \"interim\"\n", "INTERIM_TEMP_TABLE_NAME = \"chargeback_customer_temp_details\"\n", "\n", "INTERIM_PAYMENT_DETAIL_SCHEMA = \"interim\"\n", "INTERIM_PAYMENT_DETAIL_TABLE_NAME = \"chargeback_payment_details\""]}, {"cell_type": "code", "execution_count": null, "id": "236e676f-7f95-408b-b324-d885a31cef56", "metadata": {}, "outputs": [], "source": ["PAYMENTS_DTYPES = [\n", "    {\"name\": \"id\", \"type\": \"BIGINT\", \"description\": \"id\"},\n", "    {\"name\": \"cart_id\", \"type\": \"BIGINT\", \"description\": \"cart_id\"},\n", "    {\"name\": \"payment\", \"type\": \"VARCHAR\", \"description\": \"payment\"},\n", "    {\"name\": \"status\", \"type\": \"VARCHAR\", \"description\": \"status\"},\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"DATE\", \"description\": \"insert_ds_ist\"},\n", "    {\n", "        \"name\": \"install_ts\",\n", "        \"type\": \"DATE\",\n", "        \"description\": \"Date derived from install_ts with +330 minutes offset\",\n", "    },\n", "    {\n", "        \"name\": \"company_user_id\",\n", "        \"type\": \"BIGINT\",\n", "        \"description\": \"company_user_id extracted from event_data\",\n", "    },\n", "    {\n", "        \"name\": \"payment_method_type\",\n", "        \"type\": \"VARCHAR\",\n", "        \"description\": \"payment_method_type extracted from event_data.txns[0]\",\n", "    },\n", "    {\n", "        \"name\": \"card_token\",\n", "        \"type\": \"VARCHAR\",\n", "        \"description\": \"card_token extracted from payment_source.fingerprint\",\n", "    },\n", "    {\n", "        \"name\": \"track_id\",\n", "        \"type\": \"VARCHAR\",\n", "        \"description\": \"track_id extracted from event_data.txns[0]\",\n", "    },\n", "    {\n", "        \"name\": \"payment_method_id\",\n", "        \"type\": \"BIGINT\",\n", "        \"description\": \"payment_method_id extracted from event_data.txns[0]\",\n", "    },\n", "]\n", "\n", "\n", "PAYMENT_DETAIL_DTYPE = [\n", "    {\"name\": \"customer_id\", \"type\": \"BIGINT\", \"description\": \"customer_id\"},\n", "    {\"name\": \"payload\", \"type\": \"VARCHAR\", \"description\": \"VARCHAR\"},\n", "]\n", "\n", "SEGMENT_DTYPES = [\n", "    {\"name\": \"customer_id\", \"type\": \"BIGINT\", \"description\": \"customer_id\"},\n", "    {\"name\": \"payload\", \"type\": \"VARCHAR\", \"description\": \"VARCHAR\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "fa08d712-5954-4796-b557-134370ebe507", "metadata": {}, "outputs": [], "source": ["INTERIM_PAYMENTS_KWARGS = {\n", "    \"schema_name\": PAYMENTS_TABLE_SCHEMA,\n", "    \"table_name\": PAYMENTS_TABLE_NAME,\n", "    \"column_dtypes\": PAYMENTS_DTYPES,\n", "    \"primary_key\": [\"id\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"Chargeback Data for Customers\",\n", "}\n", "\n", "INTERIM_PAYMENTS_DETAILS_KWARGS = {\n", "    \"schema_name\": INTERIM_PAYMENT_DETAIL_SCHEMA,\n", "    \"table_name\": INTERIM_PAYMENT_DETAIL_TABLE_NAME,\n", "    \"column_dtypes\": PAYMENT_DETAIL_DTYPE,\n", "    \"primary_key\": [\"customer_id\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"Chargeback Data for Customers\",\n", "}\n", "\n", "SEGMENT_KWARGS = {\n", "    \"schema_name\": SEGMENT_TABLE_SCHEMA,\n", "    \"table_name\": SEGMENT_TABLE_NAME,\n", "    \"column_dtypes\": SEGMENT_DTYPES,\n", "    \"primary_key\": [\"customer_id\"],\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"Complaints Data for Customers\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "baa2c5eb-3d4c-4d11-a17f-1998bd31a523", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "27d7a6e5-2230-4659-b802-f4725f9c38b1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "896dee47-aa6c-461c-bf36-8e9993a55522", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "badc76bf-e50a-4213-94b4-00296c66f2bd", "metadata": {}, "outputs": [], "source": ["QUERY_PAYMENT = f\"\"\"\n", "\n", "SELECT\n", "    id,\n", "    cart_id,\n", "    payment,\n", "    status,\n", "    insert_ds_ist,\n", "    DATE(TRY_CAST(install_ts AS TIMESTAMP) + INTERVAL '330' MINUTE) AS install_ts,\n", "    TRY_CAST(json_extract_scalar(other_details,'$.event_data.company_user_id') AS BIGINT) AS company_user_id,\n", "    TRY_CAST(json_extract_scalar(other_details, '$.event_data.txns[0].payment_method_type') AS VARCHAR) AS payment_method_type,\n", "    TRY_CAST(JSON_EXTRACT(other_details,'$.payment_source.fingerprint') AS VARCHAR) AS card_token,\n", "    json_extract_scalar(other_details, '$.event_data.txns[0].track_id') AS track_id,\n", "    TRY_CAST(json_extract_scalar(other_details, '$.event_data.txns[0].payment_method_id') AS BIGINT) AS payment_method_id\n", "FROM \n", "    payments_db.gr_payment GR\n", "WHERE \n", "    insert_ds_ist >= TRY_CAST(current_date - INTERVAL '60' DAY AS VARCHAR)\n", "AND \n", "    insert_ds_ist <= TRY_CAST(current_date AS VARCHAR)\n", "AND TRY_CAST(json_extract_scalar(other_details,'$.event_data.company_user_id') AS BIGINT) IN \n", "    (SELECT customer_id FROM {INTERIM_TEMP_TABLE_SCHEMA}.{INTERIM_TEMP_TABLE_NAME} )\n", "\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "ba7d0ee2-70c0-4415-849d-b3c42403d1fc", "metadata": {}, "outputs": [], "source": ["pb.to_trino(QUERY_PAYMENT, **INTERIM_PAYMENTS_KWARGS)"]}, {"cell_type": "code", "execution_count": null, "id": "6d6ab9b6-1fae-43d0-8e33-83ce5f3d2343", "metadata": {}, "outputs": [], "source": ["QUERY_CUSTOMER = f\"\"\"\n", "\n", "\n", "\n", "WITH vpa_links AS (\n", "  SELECT \n", "    TRY_CAST(id AS BIGINT) AS id,\n", "    company_user_id,\n", "    vpa\n", "  FROM zomato.btransactions_upi_vpas\n", "  WHERE TRY_CAST(company_user_id AS BIGINT) IN \n", "        (SELECT customer_id FROM {INTERIM_TEMP_TABLE_SCHEMA}.{INTERIM_TEMP_TABLE_NAME})\n", "),\n", "\n", "pay_attempts_card AS (\n", "  SELECT \n", "    company_user_id,\n", "    card_token,\n", "    COUNT(DISTINCT id) AS pay_attempts\n", "  FROM {PAYMENTS_TABLE_SCHEMA}.{PAYMENTS_TABLE_NAME}\n", "  GROUP BY 1,2\n", "),\n", "\n", "typical_card AS (\n", "  SELECT \n", "    company_user_id,\n", "    MAX_BY(card_token, pay_attempts) AS typical_card\n", "  FROM pay_attempts_card\n", "  GROUP BY 1\n", "),\n", "\n", "pay_attempts_mode AS (\n", "  SELECT\n", "    company_user_id AS customer_id,\n", "    payment_method_type,\n", "    COUNT(DISTINCT id) AS pay_attempts\n", "  FROM {PAYMENTS_TABLE_SCHEMA}.{PAYMENTS_TABLE_NAME}\n", "  GROUP BY 1,2\n", "),\n", "\n", "typical_payments_method AS (\n", "  SELECT \n", "    customer_id,\n", "    MAX_BY(payment_method_type, pay_attempts) AS typical_pm\n", "  FROM pay_attempts_mode\n", "  GROUP BY 1\n", "),\n", "\n", "payment_fails_last_1m AS (\n", "  SELECT\n", "    p.company_user_id AS customer_id,\n", "    COUNT(DISTINCT CASE WHEN payment_method_type = 'card' AND status = 'FAILED' THEN track_id END) AS customer_card_txn_fails_last_1m,\n", "    array_agg(DISTINCT track_id) FILTER (WHERE payment_method_type = 'card' AND status = 'FAILED' AND track_id IS NOT NULL) AS customer_card_txn_fail_ids_last_1m_array_agg,\n", "    COUNT(DISTINCT CASE WHEN status = 'FAILED' THEN p.cart_id END) AS customer_cart_fails_last_1m,\n", "    array_agg(DISTINCT track_id) FILTER (WHERE status = 'FAILED' AND track_id IS NOT NULL) AS customer_cart_fail_ids_last_1m_array_agg,\n", "    COUNT(DISTINCT CASE WHEN payment_method_type = 'card' AND status = 'FAILED' THEN p.card_token END) AS customer_card_token_fails_last_1m,\n", "    array_agg(DISTINCT p.card_token) FILTER (WHERE payment_method_type = 'card' AND status = 'FAILED' AND p.card_token IS NOT NULL) AS customer_card_token_fail_tokens_last_1m_array_agg,\n", "    COUNT(DISTINCT CASE WHEN payment_method_type = 'upi_collect' AND status = 'FAILED' THEN track_id END) AS customer_upic_txn_fails_last_1m,\n", "    array_agg(DISTINCT track_id) FILTER (WHERE payment_method_type = 'upi_collect' AND status = 'FAILED' AND track_id IS NOT NULL) AS customer_upic_txn_fail_ids_last_1m_array_agg,\n", "    COUNT(DISTINCT CASE WHEN payment_method_type = 'upi_collect' AND status = 'FAILED' THEN p.cart_id END) AS customer_upic_fails_last_1m,\n", "    array_agg(DISTINCT track_id) FILTER (WHERE payment_method_type = 'upi_collect' AND status = 'FAILED' AND track_id IS NOT NULL) AS customer_upic_fails_ids_last_1m_array_agg,\n", "    COUNT(DISTINCT CASE WHEN payment_method_type = 'upi_collect' AND status = 'FAILED' THEN v.vpa END) AS customer_upic_dist_last_1m,\n", "    array_agg(DISTINCT v.vpa) FILTER (WHERE payment_method_type = 'upi_collect' AND status = 'FAILED' AND v.vpa IS NOT NULL) AS customer_upic_fail_vpas_last_1m_array_agg,\n", "    COUNT(DISTINCT CASE WHEN payment_method_type = 'upi' AND status = 'FAILED' THEN cart_id END) AS customer_upi_dist_last_1m,\n", "    array_agg(DISTINCT track_id) FILTER (WHERE payment_method_type = 'upi' AND status = 'FAILED' AND track_id IS NOT NULL) AS customer_upi_fail_cart_ids_last_1m_array_agg\n", "  FROM {PAYMENTS_TABLE_SCHEMA}.{PAYMENTS_TABLE_NAME} P\n", "  LEFT JOIN vpa_links v ON p.payment_method_id = v.id \n", "  WHERE insert_ds_ist >= TRY_CAST(current_date - INTERVAL '30' DAY AS VARCHAR)\n", "    AND insert_ds_ist <= TRY_CAST(current_date AS VARCHAR)\n", "  GROUP BY 1\n", "),\n", "\n", "payment_fails_last_2m AS (\n", "  SELECT\n", "    p.company_user_id AS customer_id,\n", "    COUNT(DISTINCT CASE WHEN payment_method_type = 'card' AND status = 'FAILED' THEN track_id END) AS customer_card_txn_fails_last_2m,\n", "    array_agg(DISTINCT track_id) FILTER (WHERE payment_method_type = 'card' AND status = 'FAILED' AND track_id IS NOT NULL) AS customer_card_txn_fail_ids_last_2m_array_agg,\n", "    COUNT(DISTINCT CASE WHEN status = 'FAILED' THEN p.cart_id END) AS customer_cart_fails_last_2m,\n", "    array_agg(DISTINCT track_id) FILTER (WHERE status = 'FAILED' AND track_id IS NOT NULL) AS customer_cart_fail_ids_last_2m_array_agg,\n", "    COUNT(DISTINCT CASE WHEN payment_method_type = 'card' AND status = 'FAILED' THEN p.card_token END) AS customer_card_token_fails_last_2m,\n", "    array_agg(DISTINCT p.card_token) FILTER (WHERE payment_method_type = 'card' AND status = 'FAILED' AND p.card_token IS NOT NULL) AS customer_card_token_fail_tokens_last_2m_array_agg,\n", "    COUNT(DISTINCT CASE WHEN payment_method_type = 'upi_collect' AND status = 'FAILED' THEN track_id END) AS customer_upic_txn_fails_last_2m,\n", "    array_agg(DISTINCT track_id) FILTER (WHERE payment_method_type = 'upi_collect' AND status = 'FAILED' AND track_id IS NOT NULL) AS customer_upic_txn_fail_ids_last_2m_array_agg,\n", "    COUNT(DISTINCT CASE WHEN payment_method_type = 'upi_collect' AND status = 'FAILED' THEN p.cart_id END) AS customer_upic_fails_last_2m,\n", "    array_agg(DISTINCT track_id) FILTER (WHERE payment_method_type = 'upi_collect' AND status = 'FAILED' AND track_id IS NOT NULL) AS customer_upic_fails_ids_last_2m_array_agg,\n", "    COUNT(DISTINCT CASE WHEN payment_method_type = 'upi_collect' AND status = 'FAILED' THEN v.vpa END) AS customer_upic_dist_last_2m,\n", "    array_agg(DISTINCT v.vpa) FILTER (WHERE payment_method_type = 'upi_collect' AND status = 'FAILED' AND v.vpa IS NOT NULL) AS customer_upic_fail_vpas_last_2m_array_agg,\n", "    COUNT(DISTINCT CASE WHEN payment_method_type = 'upi' AND status = 'FAILED' THEN cart_id END) AS customer_upi_dist_last_2m,\n", "    array_agg(DISTINCT track_id) FILTER (WHERE payment_method_type = 'upi' AND status = 'FAILED' AND track_id IS NOT NULL) AS customer_upi_fail_cart_ids_last_2m_array_agg\n", "  FROM {PAYMENTS_TABLE_SCHEMA}.{PAYMENTS_TABLE_NAME} P\n", "  LEFT JOIN vpa_links v ON p.payment_method_id = v.id \n", "  WHERE insert_ds_ist >= TRY_CAST(current_date - INTERVAL '60' DAY AS VARCHAR)\n", "    AND insert_ds_ist <= TRY_CAST(current_date AS VARCHAR)\n", "  GROUP BY 1\n", ")\n", "\n", "SELECT \n", "  ob.customer_id,\n", "\n", "  JSON_OBJECT(\n", "    -- Scala<PERSON>: last 1 month\n", "    KEY 'customer_card_txn_fails_last_1m' VALUE COALESCE(MAX(customer_card_txn_fails_last_1m), 0),\n", "    KEY 'customer_cart_fails_last_1m' VALUE COALESCE(MAX(customer_cart_fails_last_1m), 0),\n", "    KEY 'customer_card_token_fails_last_1m' VALUE COALESCE(MAX(customer_card_token_fails_last_1m), 0),\n", "    KEY 'customer_upic_txn_fails_last_1m' VALUE COALESCE(MAX(customer_upic_txn_fails_last_1m), 0),\n", "    KEY 'customer_upic_fails_last_1m' VALUE COALESCE(MAX(customer_upic_fails_last_1m), 0),\n", "    KEY 'customer_upic_dist_last_1m' VALUE COALESCE(MAX(customer_upic_dist_last_1m), 0),\n", "    KEY 'customer_upi_dist_fails_last_1m' VALUE COALESCE(MAX(customer_upi_dist_last_1m), 0),\n", "\n", "    -- Arrays: last 1 month\n", "    KEY 'customer_card_txn_fail_ids_last_1m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(customer_card_txn_fail_ids_last_1m_array_agg) AS JSON), JSON '[]')),\n", "    KEY 'customer_cart_fail_ids_last_1m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(customer_cart_fail_ids_last_1m_array_agg) AS JSON), JSON '[]')),\n", "    KEY 'customer_card_token_fail_tokens_last_1m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(customer_card_token_fail_tokens_last_1m_array_agg) AS JSON), JSON '[]')),\n", "    KEY 'customer_upic_txn_fail_ids_last_1m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(customer_upic_txn_fail_ids_last_1m_array_agg) AS JSON), JSON '[]')),\n", "    KEY 'customer_upic_fails_ids_last_1m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(customer_upic_fails_ids_last_1m_array_agg) AS JSON), JSON '[]')),\n", "    KEY 'customer_upic_fail_vpas_last_1m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(customer_upic_fail_vpas_last_1m_array_agg) AS JSON), JSON '[]')),\n", "    KEY 'customer_upi_fail_cart_ids_last_1m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(customer_upi_fail_cart_ids_last_1m_array_agg) AS JSON), JSON '[]')),\n", "\n", "    -- <PERSON><PERSON><PERSON>: last 2 months\n", "    KEY 'customer_card_txn_fails_last_2m' VALUE COALESCE(MAX(customer_card_txn_fails_last_2m), 0),\n", "    KEY 'customer_cart_fails_last_2m' VALUE COALESCE(MAX(customer_cart_fails_last_2m), 0),\n", "    KEY 'customer_card_token_fails_last_2m' VALUE COALESCE(MAX(customer_card_token_fails_last_2m), 0),\n", "    KEY 'customer_upic_txn_fails_last_2m' VALUE COALESCE(MAX(customer_upic_txn_fails_last_2m), 0),\n", "    KEY 'customer_upic_fails_last_2m' VALUE COALESCE(MAX(customer_upic_fails_last_2m), 0),\n", "    KEY 'customer_upic_dist_last_2m' VALUE COALESCE(MAX(customer_upic_dist_last_2m), 0),\n", "    KEY 'customer_upi_dist_fails_last_2m' VALUE COALESCE(MAX(customer_upi_dist_last_2m), 0),\n", "\n", "    -- Arrays: last 2 months\n", "    KEY 'customer_card_txn_fail_ids_last_2m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(customer_card_txn_fail_ids_last_2m_array_agg) AS JSON), JSON '[]')),\n", "    KEY 'customer_cart_fail_ids_last_2m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(customer_cart_fail_ids_last_2m_array_agg) AS JSON), JSON '[]')),\n", "    KEY 'customer_card_token_fail_tokens_last_2m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(customer_card_token_fail_tokens_last_2m_array_agg) AS JSON), JSON '[]')),\n", "    KEY 'customer_upic_txn_fail_ids_last_2m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(customer_upic_txn_fail_ids_last_2m_array_agg) AS JSON), JSON '[]')),\n", "    KEY 'customer_upic_fails_ids_last_2m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(customer_upic_fails_ids_last_2m_array_agg) AS JSON), JSON '[]')),\n", "    KEY 'customer_upic_fail_vpas_last_2m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(customer_upic_fail_vpas_last_2m_array_agg) AS JSON), JSON '[]')),\n", "    KEY 'customer_upi_fail_cart_ids_last_2m_array_agg' VALUE JSON_FORMAT(COALESCE(CAST(MAX(customer_upi_fail_cart_ids_last_2m_array_agg) AS JSON), JSON '[]')),\n", "\n", "    -- Statistical values with fallback to empty string\n", "    KEY 'statistical_payment_mode' VALUE COALESCE(MAX(typical_pm), ''),\n", "    KEY 'statistical_payment_card' VALUE COALESCE(MAX(typical_card), '')\n", "  ) AS payload\n", "\n", "FROM interim.chargeback_customer_temp_details ob\n", "\n", "LEFT JOIN payment_fails_last_1m p_1 \n", "  ON ob.customer_id = p_1.customer_id\n", "\n", "LEFT JOIN payment_fails_last_2m p_2 \n", "  ON ob.customer_id = p_2.customer_id\n", "\n", "LEFT JOIN typical_payments_method tpm \n", "  ON tpm.customer_id = ob.customer_id\n", "\n", "LEFT JOIN typical_card tc \n", "  ON tc.company_user_id = ob.customer_id\n", "\n", "GROUP BY 1\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "60aa52a8-0886-4217-b294-c2c75460e9aa", "metadata": {}, "outputs": [], "source": ["pb.to_trino(QUERY_CUSTOMER, **INTERIM_PAYMENTS_DETAILS_KWARGS)"]}, {"cell_type": "code", "execution_count": null, "id": "5eb895a7-49f3-479b-9d66-f7c28dce80ee", "metadata": {}, "outputs": [], "source": ["query_2 = f\"\"\"\n", "\n", "SELECT\n", "    customer_id\n", "FROM\n", "    {SEGMENT_TABLE_SCHEMA}.{SEGMENT_TABLE_NAME}\n", "WHERE\n", "    customer_id NOT IN (SELECT customer_id FROM {INTERIM_PAYMENT_DETAIL_SCHEMA}.{INTERIM_PAYMENT_DETAIL_TABLE_NAME} )\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "8248e78b-3545-4772-8281-93ad0d051dc2", "metadata": {}, "outputs": [], "source": ["data_json = str(\n", "    json.dumps(\n", "        {\n", "            \"customer_upic_fails_last_1m\": 0,\n", "            \"statistical_payment_card\": \"\",\n", "            \"customer_card_token_fails_last_1m\": 0,\n", "            \"customer_upi_dist_fails_last_2m\": 0,\n", "            \"customer_upic_txn_fail_ids_last_2m_array_agg\": \"[]\",\n", "            \"customer_upic_txn_fails_last_1m\": 0,\n", "            \"customer_cart_fails_last_2m\": 0,\n", "            \"customer_card_txn_fail_ids_last_2m_array_agg\": \"[]\",\n", "            \"customer_card_token_fail_tokens_last_1m_array_agg\": \"[]\",\n", "            \"customer_upic_fails_ids_last_1m_array_agg\": \"[]\",\n", "            \"customer_upic_dist_last_2m\": 0,\n", "            \"customer_upic_txn_fail_ids_last_1m_array_agg\": \"[]\",\n", "            \"customer_upic_fail_vpas_last_1m_array_agg\": \"[]\",\n", "            \"customer_upi_fail_cart_ids_last_1m_array_agg\": \"[]\",\n", "            \"customer_card_txn_fails_last_2m\": 0,\n", "            \"customer_upi_fail_cart_ids_last_2m_array_agg\": \"[]\",\n", "            \"customer_upic_fails_last_2m\": 0,\n", "            \"customer_card_token_fails_last_2m\": 0,\n", "            \"customer_cart_fail_ids_last_2m_array_agg\": \"[]\",\n", "            \"customer_card_txn_fail_ids_last_1m_array_agg\": \"[]\",\n", "            \"customer_upic_fails_ids_last_2m_array_agg\": \"[]\",\n", "            \"customer_upic_fail_vpas_last_2m_array_agg\": \"[]\",\n", "            \"customer_upi_dist_fails_last_1m\": 0,\n", "            \"customer_cart_fails_last_1m\": 0,\n", "            \"customer_upic_txn_fails_last_2m\": 0,\n", "            \"customer_card_txn_fails_last_1m\": 0,\n", "            \"statistical_payment_mode\": \"\",\n", "            \"customer_cart_fail_ids_last_1m_array_agg\": \"[]\",\n", "            \"customer_upic_dist_last_1m\": 0,\n", "            \"customer_card_token_fail_tokens_last_2m_array_agg\": \"[]\",\n", "        }\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c4d9abf5-d299-4d90-8e9f-5d41b8ba7c8e", "metadata": {}, "outputs": [], "source": ["try:\n", "    df = pd.read_sql(query_2, CON_TRINO)\n", "\n", "    if len(df) > 0:\n", "        df[\"payload\"] = data_json\n", "        pb.to_trino(df, **SEGMENT_KWARGS)\n", "    else:\n", "        print(\"No data to be updated.\")\n", "\n", "except Exception as e:\n", "    print(\"Sync Failed.\")\n", "    print(e)"]}, {"cell_type": "code", "execution_count": null, "id": "d95939c9-9847-413d-9dee-d32ee5207e03", "metadata": {}, "outputs": [], "source": ["pb.to_trino(QUERY_CUSTOMER, **SEGMENT_KWARGS)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}