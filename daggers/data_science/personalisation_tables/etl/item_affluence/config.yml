alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
  - channel: bl-personalization-notifications
dag_name: item_affluence
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: high-mem
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03SJ8DPFND
path: data_science/personalisation_tables/etl/item_affluence
paused: false
pool: data_science_pool
project_name: personalisation_tables
schedule:
  end_date: '2025-08-31T00:00:00'
  interval: 30 20 * * *
  start_date: '2024-08-08T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- queries/*
tags: []
template_name: notebook
version: 1
