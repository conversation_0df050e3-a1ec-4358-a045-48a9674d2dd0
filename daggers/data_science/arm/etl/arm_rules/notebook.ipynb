{"cells": [{"cell_type": "code", "execution_count": null, "id": "7392b302-7f67-4123-9443-71debc725098", "metadata": {}, "outputs": [], "source": ["import mlxtend\n", "\n", "mlxtend.__version__"]}, {"cell_type": "code", "execution_count": null, "id": "d421c49a-a180-400a-8717-e64d8a2dbbda", "metadata": {}, "outputs": [], "source": ["import sys\n", "import psutil\n", "import os"]}, {"cell_type": "code", "execution_count": null, "id": "8b355d1a-5421-4596-9e92-a75cbe4713a2", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "85039654-0505-467b-a7b1-a99d2bad76e9", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "f6e9bc4a-95b5-4ddd-a014-1fae03dcfa31", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import gc"]}, {"cell_type": "code", "execution_count": null, "id": "4865ed84-e451-40f2-96fd-b50b5a7b0415", "metadata": {}, "outputs": [], "source": ["from mlxtend.preprocessing import TransactionEncoder\n", "from mlxtend.frequent_patterns import apriori, association_rules, fpgrowth"]}, {"cell_type": "code", "execution_count": null, "id": "9dbb3268-f303-4421-90ba-7f830cace223", "metadata": {}, "outputs": [], "source": ["import requests"]}, {"cell_type": "code", "execution_count": null, "id": "7987b349-0b37-451d-b723-572e6cfc41e0", "metadata": {}, "outputs": [], "source": ["trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "fa622369-94ce-4b76-8f54-3b06cc0bd2d6", "metadata": {}, "outputs": [], "source": ["pid = os.getpid()\n", "process = psutil.Process(pid)"]}, {"cell_type": "markdown", "id": "b8a9aefa-0d59-4110-a0de-8cddfafddd4e", "metadata": {"tags": []}, "source": ["## ptype ARM 1 year"]}, {"cell_type": "code", "execution_count": null, "id": "adc1cedc-f455-4b2e-aad9-208c13fee2f5", "metadata": {}, "outputs": [], "source": ["def get_ptype_cart_data(start, end, retries=3):\n", "    count = 1\n", "    while count <= retries:\n", "        try:\n", "            carts_data = pd.read_sql(\n", "                f\"\"\"\n", "    \n", "            with trans_data as\n", "            (\n", "            select city_id,\n", "                   cart_id,\n", "                   cart_checkout_ts_ist as timestamp,\n", "                   product_type_id,\n", "                   cast(product_type_id as varchar) as concat_key\n", "            from dwh.fact_sales_order_item_details fs\n", "            join \n", "            (\n", "                select product_id, product_type_id from consumer_intelligence_etls.personalisation_byc_prod_meta_table where l0_category not in ('Specials','HP')\n", "            ) dp on dp.product_id=fs.product_id\n", "            join \n", "            (\n", "                select merchant_id,city_id from dwh.dim_merchant group by 1,2\n", "            ) dm on dm.merchant_id=fs.frontend_merchant_id\n", "            where order_create_dt_ist>=current_date - INTERVAL '{start}' DAY\n", "                  and order_create_dt_ist<current_date - INTERVAL '{end}' DAY\n", "                  and fs.order_current_status='DELIVERED'\n", "                  and city_id in (1,3,1849)\n", "                  group by 1,2,3,4,5\n", "            \n", "            ),\n", "\n", "            trans_data_2 as\n", "            (\n", "            select city_id,\n", "                   cart_id,\n", "                   timestamp,\n", "                   product_type_id,\n", "                   concat_key,\n", "                   row_number() over (partition by cart_id) as rank\n", "            from trans_data\n", "            ),\n", "            \n", "            cart_prod_count as \n", "            (\n", "            select cart_id, count(distinct product_type_id) as ptype_count from trans_data_2 group by 1\n", "            )\n", "\n", "            select cp.cart_id, listagg(concat_key,' ') within group (order by rank) as list_agg, timestamp from\n", "            trans_data_2 cp\n", "            left join cart_prod_count cpc on cp.cart_id=cpc.cart_id\n", "            where ptype_count>1\n", "            group by 1,3\n", "            \n", "            \"\"\",\n", "                con=pb.get_connection(\"[Warehouse] Trino\"),\n", "            )\n", "            break\n", "        except requests.ConnectionError:\n", "            count += 1\n", "    return carts_data"]}, {"cell_type": "code", "execution_count": null, "id": "635cca39-9827-429f-8894-960f416da280", "metadata": {}, "outputs": [], "source": ["def preprocess_ptype_cart_data(carts_data):\n", "    # carts_data[\"timestamp\"] = pd.to_datetime(carts_data[\"timestamp\"]).apply(\n", "    #     lambda x: int(x.strftime(\"%s\"))\n", "    # )\n", "    carts_data[\"list_agg\"] = carts_data[\"list_agg\"].apply(lambda x: x.split(\" \"))\n", "    carts_data[\"len\"] = carts_data[\"list_agg\"].apply(lambda x: len(x))\n", "    carts_data = carts_data.query(\"len<=10\")  # taking carts only with <=10 pids\n", "    cart_items_list = list(carts_data[\"list_agg\"].values)  # converting to list of carts\n", "    return cart_items_list"]}, {"cell_type": "code", "execution_count": null, "id": "b7ea6271-67f1-4a11-bf3c-8f2d1d540e39", "metadata": {}, "outputs": [], "source": ["def run_ptype_arm(te_ary_df):\n", "    #     te = TransactionEncoder()\n", "\n", "    #     oht_ary = te.fit(cart_items_list).transform(cart_items_list, sparse=True)\n", "    #     te_ary_df = pd.DataFrame.sparse.from_spmatrix(oht_ary, columns=te.columns_)\n", "\n", "    frequent_itemsets = fpgrowth(\n", "        te_ary_df, min_support=5 / te_ary_df.shape[0], max_len=2, use_colnames=True\n", "    )\n", "    rules = association_rules(frequent_itemsets, metric=\"lift\", min_threshold=1)\n", "    rules[\"antecedents\"] = rules[\"antecedents\"].apply(\n", "        lambda x: int(list(x)[0])\n", "    )  ## processing ARM output\n", "    rules[\"consequents\"] = rules[\"consequents\"].apply(\n", "        lambda x: int(list(x)[0])\n", "    )  ## processing ARM output\n", "    return rules"]}, {"cell_type": "raw", "id": "a2689b1b-9849-4faa-ab45-756b1582c91b", "metadata": {}, "source": ["def run_ptype_arm_1(cart_items_list):\n", "    itemsets, rules = eff_apriori(\n", "        cart_items_list, min_support=5 / len(cart_items_list), max_length=2, min_confidence=0\n", "    )\n", "    print(1)\n", "\n", "    columns = [\"antecedents\", \"consequents\", \"confidence\", \"support\", \"lift\", \"conviction\"]\n", "    data = []\n", "    rules_filter = filter(lambda rule: rule.lift > 1, rules)\n", "\n", "    for i in rules_filter:\n", "        new_row = {\n", "            \"antecedents\": i.lhs[0],\n", "            \"consequents\": i.rhs[0],\n", "            \"confidence\": i.confidence,\n", "            \"support\": i.support,\n", "            \"lift\": i.lift,\n", "            \"conviction\": i.conviction,\n", "        }\n", "        data.append(new_row)\n", "    print(2)\n", "\n", "    rules_df = pd.DataFrame(data, columns=columns)\n", "    print(3)\n", "    rules_df[\"antecedents\"] = rules_df[\"antecedents\"].astype(\"int\")\n", "    rules_df[\"consequents\"] = rules_df[\"consequents\"].astype(\"int\")\n", "    print(4)\n", "    return rules_df"]}, {"cell_type": "code", "execution_count": null, "id": "2d56a2ac-4d4d-4a3b-9f42-c9bbb7ec265b", "metadata": {}, "outputs": [], "source": ["def get_enc_trns(ptype_cart_items_list):\n", "    te = TransactionEncoder()\n", "    oht_ary = te.fit(ptype_cart_items_list).transform(ptype_cart_items_list, sparse=True)\n", "    te_ary_df = pd.DataFrame.sparse.from_spmatrix(oht_ary, columns=te.columns_)\n", "    return te_ary_df"]}, {"cell_type": "raw", "id": "fbb4438a-3597-430e-b9eb-3054e0994dcb", "metadata": {}, "source": ["temp_data_1 = get_ptype_cart_data(start=365, end=180)\n", "temp_data_2 = get_ptype_cart_data(start=180, end=1)\n", "\n", "print(f\"Memory usage: {process.memory_info().rss / 1024 / 1024} MB\")\n", "ptype_carts_data = (\n", "    pd.concat([temp_data_1, temp_data_2]).drop_duplicates(subset=\"cart_id\").reset_index(drop=True)\n", ")\n", "print(f\"Memory usage: {process.memory_info().rss / 1024 / 1024} MB\")"]}, {"cell_type": "raw", "id": "ad846b56-f846-4ff3-b076-ccac93647f74", "metadata": {}, "source": ["ptype_carts_data.shape"]}, {"cell_type": "raw", "id": "929f1ec3-d472-42e0-8c63-92548ba66175", "metadata": {}, "source": ["ptype_carts_data.head()"]}, {"cell_type": "raw", "id": "c105d242-5464-4e8f-bb24-21debf569020", "metadata": {}, "source": ["ptype_cart_items_list = preprocess_ptype_cart_data(ptype_carts_data)\n", "te_ary_df = get_enc_trns(ptype_cart_items_list)\n", "\n", "# ptype_carts_data = []\n", "# del ptype_carts_data\n", "# gc.collect()"]}, {"cell_type": "raw", "id": "3339fb07-3e70-484a-8661-d630686996e3", "metadata": {}, "source": ["print(f\"Memory usage: {process.memory_info().rss / 1024 / 1024} MB\")"]}, {"cell_type": "raw", "id": "0473ae04-3d79-4f19-87f3-fe8a7bbb1637", "metadata": {}, "source": ["ptype_cart_items_list[0:5]"]}, {"cell_type": "raw", "id": "5b35c2f9-25c3-41d6-b3dd-fe1ffbb70a0a", "metadata": {}, "source": ["del ptype_cart_items_list\n", "del ptype_carts_data\n", "del temp_data_1, temp_data_2\n", "gc.collect()"]}, {"cell_type": "raw", "id": "43fc9e30-60d2-4d2e-a851-c9b13ab465f3", "metadata": {}, "source": ["print(f\"Memory usage: {process.memory_info().rss / 1024 / 1024} MB\")"]}, {"cell_type": "raw", "id": "306b6046-fd9f-4b68-b31f-92a03d5577b5", "metadata": {}, "source": ["# ptype_rules = run_ptype_arm(ptype_cart_items_list)\n", "ptype_rules = run_ptype_arm(te_ary_df)\n", "gc.collect()"]}, {"cell_type": "raw", "id": "8455d279-8092-4b11-9d4e-af6303482734", "metadata": {}, "source": ["print(f\"Memory usage: {process.memory_info().rss / 1024 / 1024} MB\")"]}, {"cell_type": "raw", "id": "685b07c0-a7c8-4f35-bb06-07b61c6dde24", "metadata": {}, "source": ["ptype_rules.columns = [\n", "    \"antecedents\",\n", "    \"consequents\",\n", "    \"antecedent_support\",\n", "    \"consequent_support\",\n", "    \"support\",\n", "    \"confidence\",\n", "    \"lift\",\n", "    \"leverage\",\n", "    \"conviction\",\n", "    \"zhangs_metric\",\n", "]"]}, {"cell_type": "raw", "id": "b3154d04-b7ec-40c4-9f35-be8b6bf8c918", "metadata": {}, "source": ["ptype_rules.shape"]}, {"cell_type": "raw", "id": "60eef112-cf82-4966-bf7d-7ae30199035f", "metadata": {}, "source": ["ptype_rules.head()"]}, {"cell_type": "raw", "id": "c2cfbaf5-41cb-45ac-90dd-7a31ba2de110", "metadata": {}, "source": ["ptype_rules.to_parquet(\"/tmp/eff_arm_1_year_opt.pq\")\n", "pb.to_s3(\"/tmp/eff_arm_1_year_opt.pq\", \"prod-dse-projects\", \"ARM/ptype/eff_arm_1_year_opt.pq\")"]}, {"cell_type": "raw", "id": "a974625d-73bb-4873-9b26-ed51913251b7", "metadata": {}, "source": ["## pushing prod_df_master\n", "kwargs = {\n", "    \"schema_name\": \"consumer_intelligence_etls\",\n", "    \"table_name\": \"arm_ptype_ptype_raw_results\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"antecedents\", \"type\": \"bigint\", \"description\": \"antecedents\"},\n", "        {\"name\": \"consequents\", \"type\": \"bigint\", \"description\": \"consequents\"},\n", "        {\n", "            \"name\": \"antecedent_support\",\n", "            \"type\": \"double\",\n", "            \"description\": \"antecedent support\",\n", "        },\n", "        {\n", "            \"name\": \"consequent_support\",\n", "            \"type\": \"double\",\n", "            \"description\": \"consequent support\",\n", "        },\n", "        {\"name\": \"support\", \"type\": \"double\", \"description\": \"support\"},\n", "        {\"name\": \"confidence\", \"type\": \"double\", \"description\": \"confidence\"},\n", "        {\"name\": \"lift\", \"type\": \"double\", \"description\": \"lift\"},\n", "        {\"name\": \"leverage\", \"type\": \"double\", \"description\": \"leverage\"},\n", "        {\"name\": \"conviction\", \"type\": \"double\", \"description\": \"conviction\"},\n", "        {\"name\": \"zhangs_metric\", \"type\": \"double\", \"description\": \"zhangs_metric\"},\n", "    ],\n", "    \"primary_key\": [\"antecedents\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"Ptype to ptype ARM model results\",\n", "}\n", "\n", "pb.to_trino(ptype_rules, **kwargs)"]}, {"cell_type": "raw", "id": "e4f8dfe1-e9b6-452e-81d0-c0297222e97b", "metadata": {}, "source": ["channel = \"bl-personalization-notifications\"\n", "text = \"p-type to p-type ARM raw results table update\"\n", "pb.send_slack_message(channel=channel, text=text)"]}, {"cell_type": "raw", "id": "701234d2-7c00-4375-bdc6-5659d2dbd4c8", "metadata": {}, "source": ["ptype_rules = []\n", "del ptype_rules\n", "del te_ary_df\n", "gc.collect()"]}, {"cell_type": "raw", "id": "672299c8-3117-4036-a7be-6e4f7f32d6b2", "metadata": {}, "source": ["print(f\"Memory usage: {process.memory_info().rss / 1024 / 1024} MB\")"]}, {"cell_type": "code", "execution_count": null, "id": "9c8892ca-13b1-48cd-92cf-1428d6921c40", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "3239ff97-ed36-41c9-84ef-89a950b27700", "metadata": {}, "source": ["## PTYPE ARM 14days"]}, {"cell_type": "code", "execution_count": null, "id": "24b5803f-1990-4fe4-ba3a-0bdcc33a36d3", "metadata": {}, "outputs": [], "source": ["ptype_carts_data = get_ptype_cart_data(start=15, end=1)"]}, {"cell_type": "code", "execution_count": null, "id": "4cd5d240-8aa8-4c70-9c88-9c75a9450a24", "metadata": {}, "outputs": [], "source": ["ptype_carts_data.shape, ptype_carts_data.cart_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "183b0270-f4c0-4d7b-8baa-e8b3d77c4eb4", "metadata": {}, "outputs": [], "source": ["ptype_carts_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "989fada7-5aba-4b82-ae9b-68d6b577ec31", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "20144d05-ce08-4b4d-9a0e-d58e8c8a0beb", "metadata": {}, "outputs": [], "source": ["ptype_cart_items_list = preprocess_ptype_cart_data(ptype_carts_data)\n", "te_ary_df = get_enc_trns(ptype_cart_items_list)\n", "\n", "# ptype_carts_data = []\n", "# del ptype_carts_data\n", "# gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "9581a176-aed7-49cd-94c3-69ccb9c1cf22", "metadata": {}, "outputs": [], "source": ["te_ary_df"]}, {"cell_type": "code", "execution_count": null, "id": "af412c4c-6fbb-442b-9ace-c0eeefdb2483", "metadata": {}, "outputs": [], "source": ["ptype_cart_items_list[0:5]"]}, {"cell_type": "code", "execution_count": null, "id": "d70edd55-baf5-484f-a561-5e847f80de12", "metadata": {}, "outputs": [], "source": ["del ptype_cart_items_list\n", "del ptype_carts_data\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "1ddb8fb5-9de3-4662-8380-40ea23d19441", "metadata": {"tags": []}, "outputs": [], "source": ["ptype_rules = run_ptype_arm(te_ary_df)\n", "\n", "ptype_cart_items_list = []\n", "del ptype_cart_items_list\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "86573fa6-5dea-44da-b858-447faabe7704", "metadata": {}, "outputs": [], "source": ["print(f\"Memory usage: {process.memory_info().rss / 1024 / 1024} MB\")"]}, {"cell_type": "code", "execution_count": null, "id": "851a8307-0c24-4f70-907d-120fcd9f12c9", "metadata": {}, "outputs": [], "source": ["ptype_rules.columns = [\n", "    \"antecedents\",\n", "    \"consequents\",\n", "    \"antecedent_support\",\n", "    \"consequent_support\",\n", "    \"support\",\n", "    \"confidence\",\n", "    \"lift\",\n", "    \"leverage\",\n", "    \"conviction\",\n", "    \"zhangs_metric\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "2911d4d5-038d-4654-a071-7f41b76c7a14", "metadata": {}, "outputs": [], "source": ["ptype_rules.shape"]}, {"cell_type": "code", "execution_count": null, "id": "caa5ebe7-76da-49b3-bfae-45d8f1d624c9", "metadata": {}, "outputs": [], "source": ["ptype_rules.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9764e6ab-3407-4504-b398-b84075061f04", "metadata": {}, "outputs": [], "source": ["## pushing prod_df_master\n", "kwargs = {\n", "    \"schema_name\": \"consumer_intelligence_etls\",\n", "    \"table_name\": \"arm_ptype_ptype_raw_results_14days\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"antecedents\", \"type\": \"bigint\", \"description\": \"antecedents\"},\n", "        {\"name\": \"consequents\", \"type\": \"bigint\", \"description\": \"consequents\"},\n", "        {\n", "            \"name\": \"antecedent_support\",\n", "            \"type\": \"double\",\n", "            \"description\": \"antecedent support\",\n", "        },\n", "        {\n", "            \"name\": \"consequent_support\",\n", "            \"type\": \"double\",\n", "            \"description\": \"consequent support\",\n", "        },\n", "        {\"name\": \"support\", \"type\": \"double\", \"description\": \"support\"},\n", "        {\"name\": \"confidence\", \"type\": \"double\", \"description\": \"confidence\"},\n", "        {\"name\": \"lift\", \"type\": \"double\", \"description\": \"lift\"},\n", "        {\"name\": \"leverage\", \"type\": \"double\", \"description\": \"leverage\"},\n", "        {\"name\": \"conviction\", \"type\": \"double\", \"description\": \"conviction\"},\n", "        {\"name\": \"zhangs_metric\", \"type\": \"double\", \"description\": \"zhangs_metric\"},\n", "    ],\n", "    \"primary_key\": [\"antecedents\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"Ptype to ptype ARM model results\",\n", "}\n", "\n", "pb.to_trino(ptype_rules, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "6369d68c-8ac6-4773-be09-96f9d54506a8", "metadata": {}, "outputs": [], "source": ["channel = \"bl-personalization-notifications\"\n", "text = \"p-type to p-type ARM raw results 14days table update\"\n", "pb.send_slack_message(channel=channel, text=text)"]}, {"cell_type": "code", "execution_count": null, "id": "fbfe2c95-0ce1-42c3-a58a-0b2dfcc01f89", "metadata": {}, "outputs": [], "source": ["ptype_rules = []\n", "del ptype_rules\n", "del te_ary_df\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "81333983-90d5-4da8-83b5-d56abb96c1ee", "metadata": {}, "source": ["## L1 ARM"]}, {"cell_type": "code", "execution_count": null, "id": "72ca7bf4-1567-4719-aba1-d04d74555d34", "metadata": {}, "outputs": [], "source": ["def get_l1_cart_data(start, end, retries=3):\n", "    count = 1\n", "    while count <= retries:\n", "        try:\n", "            carts_data = pd.read_sql(\n", "                f\"\"\"\n", "    \n", "            with trans_data as\n", "            (\n", "            select city_id,\n", "                   cart_id,\n", "                   cart_checkout_ts_ist as timestamp,\n", "                   l1_category_id,\n", "                   cast(l1_category_id as varchar) as concat_key\n", "            from dwh.fact_sales_order_item_details fs\n", "            join \n", "            (\n", "                select product_id, l1_category_id from consumer_intelligence_etls.personalisation_byc_prod_meta_table where l0_category not in ('Specials','HP')\n", "            ) dp on dp.product_id=fs.product_id\n", "            join \n", "            (\n", "                select merchant_id,city_id from dwh.dim_merchant group by 1,2\n", "            ) dm on dm.merchant_id=fs.frontend_merchant_id\n", "            where order_create_dt_ist>=current_date - INTERVAL '{start}' DAY\n", "                  and order_create_dt_ist<current_date - INTERVAL '{end}' DAY\n", "                  and fs.order_current_status='DELIVERED'\n", "                  and city_id in (1,3,1849)\n", "            group by 1,2,3,4,5\n", "            ),\n", "\n", "            trans_data_2 as\n", "            (\n", "            select city_id,\n", "                   cart_id,timestamp,\n", "                   l1_category_id,\n", "                   concat_key,\n", "                   row_number() over (partition by cart_id) as rank\n", "            from trans_data\n", "            ),\n", "            \n", "            cart_prod_count as \n", "            (\n", "            select cart_id, count(distinct l1_category_id) as l1_count from trans_data_2 group by 1\n", "            )\n", "\n", "            select cp.cart_id, listagg(concat_key,' ') within group (order by rank) as list_agg, timestamp from\n", "            trans_data_2 cp\n", "            left join cart_prod_count cpc on cp.cart_id=cpc.cart_id\n", "            where l1_count>1\n", "            group by 1,3\n", "            \n", "            \"\"\",\n", "                con=pb.get_connection(\"[Warehouse] Trino\"),\n", "            )\n", "            break\n", "        except requests.ConnectionError:\n", "            count += 1\n", "    return carts_data"]}, {"cell_type": "code", "execution_count": null, "id": "ea44b087-8d18-4657-927c-d9a9b9e12cbd", "metadata": {}, "outputs": [], "source": ["def preprocess_l1_cart_data(carts_data):\n", "    # carts_data[\"timestamp\"] = pd.to_datetime(carts_data[\"timestamp\"]).apply(\n", "    #     lambda x: int(x.strftime(\"%s\"))\n", "    # )\n", "    carts_data[\"list_agg\"] = carts_data[\"list_agg\"].apply(lambda x: x.split(\" \"))\n", "    carts_data[\"len\"] = carts_data[\"list_agg\"].apply(lambda x: len(x))\n", "    carts_data = carts_data.query(\"len<=10\")  # taking carts only with <=10 pids\n", "    cart_items_list = list(carts_data[\"list_agg\"].values)  # converting to list of carts\n", "    return cart_items_list"]}, {"cell_type": "code", "execution_count": null, "id": "07c45144-4f69-42bc-9a6c-68ca27eb5bfb", "metadata": {}, "outputs": [], "source": ["def run_l1_arm(cart_items_list):\n", "    frequent_itemsets = fpgrowth(\n", "        te_ary_df, min_support=50000 / te_ary_df.shape[0], max_len=2, use_colnames=True\n", "    )\n", "    rules = association_rules(frequent_itemsets, metric=\"lift\", min_threshold=1)\n", "    rules[\"antecedents\"] = rules[\"antecedents\"].apply(\n", "        lambda x: int(list(x)[0])\n", "    )  ## processing ARM output\n", "    rules[\"consequents\"] = rules[\"consequents\"].apply(\n", "        lambda x: int(list(x)[0])\n", "    )  ## processing ARM output\n", "    return rules"]}, {"cell_type": "code", "execution_count": null, "id": "e0a15f05-b745-459b-a39b-a79a50bdf79c", "metadata": {"tags": []}, "outputs": [], "source": ["temp_data_1 = get_l1_cart_data(start=365, end=180)\n", "temp_data_2 = get_l1_cart_data(start=180, end=1)\n", "\n", "l1_carts_data = (\n", "    pd.concat([temp_data_1, temp_data_2]).drop_duplicates(subset=\"cart_id\").reset_index(drop=True)\n", ")\n", "\n", "temp_data_1 = []\n", "temp_data_2 = []\n", "del temp_data_1, temp_data_2\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "226d2e74-47d2-4b59-aba0-b12820b04c3d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b0b3da53-b040-47e8-b2ce-89166bac014b", "metadata": {}, "outputs": [], "source": ["# l1_carts_data = get_l1_cart_data()"]}, {"cell_type": "code", "execution_count": null, "id": "58de8577-cb12-4c56-bfa4-69c915b3cb46", "metadata": {}, "outputs": [], "source": ["l1_carts_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2afc13e6-7582-46e7-8905-4ee08c4c8665", "metadata": {}, "outputs": [], "source": ["l1_cart_items_list = preprocess_l1_cart_data(l1_carts_data)\n", "te_ary_df = get_enc_trns(l1_cart_items_list)\n", "\n", "# l1_carts_data = []\n", "# del l1_carts_data\n", "# gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "e8d122b8-88ca-4080-aa78-21e8ec9a7679", "metadata": {}, "outputs": [], "source": ["l1_cart_items_list[0:5]"]}, {"cell_type": "code", "execution_count": null, "id": "9fd61fc9-ecb6-4595-8592-55f4962a539c", "metadata": {}, "outputs": [], "source": ["del l1_cart_items_list\n", "del l1_carts_data\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "2add6401-95cf-42f1-a8b7-0f9ee2019f28", "metadata": {}, "outputs": [], "source": ["l1_rules = run_l1_arm(te_ary_df)\n", "\n", "l1_cart_items_list = []\n", "del l1_cart_items_list\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "95d5677d-1431-437e-a3b1-02579fc47bd4", "metadata": {}, "outputs": [], "source": ["l1_rules.columns = [\n", "    \"antecedents\",\n", "    \"consequents\",\n", "    \"antecedent_support\",\n", "    \"consequent_support\",\n", "    \"support\",\n", "    \"confidence\",\n", "    \"lift\",\n", "    \"leverage\",\n", "    \"conviction\",\n", "    \"zhangs_metric\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "0cd81933-0856-443b-8159-3e05aa7178a2", "metadata": {}, "outputs": [], "source": ["l1_rules.shape"]}, {"cell_type": "code", "execution_count": null, "id": "7ea80916-fa16-4261-a9dc-5ec03ac8a82d", "metadata": {}, "outputs": [], "source": ["l1_rules.head()"]}, {"cell_type": "code", "execution_count": null, "id": "21842cae-aaf3-4386-8685-0a070b046f1b", "metadata": {}, "outputs": [], "source": ["## pushing prod_df_master\n", "l1_kwargs = {\n", "    \"schema_name\": \"consumer_intelligence_etls\",\n", "    \"table_name\": \"arm_l1_l1_raw_results\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"antecedents\", \"type\": \"bigint\", \"description\": \"antecedents\"},\n", "        {\"name\": \"consequents\", \"type\": \"bigint\", \"description\": \"consequents\"},\n", "        {\n", "            \"name\": \"antecedent_support\",\n", "            \"type\": \"double\",\n", "            \"description\": \"antecedent support\",\n", "        },\n", "        {\n", "            \"name\": \"consequent_support\",\n", "            \"type\": \"double\",\n", "            \"description\": \"consequent support\",\n", "        },\n", "        {\"name\": \"support\", \"type\": \"double\", \"description\": \"support\"},\n", "        {\"name\": \"confidence\", \"type\": \"double\", \"description\": \"confidence\"},\n", "        {\"name\": \"lift\", \"type\": \"double\", \"description\": \"lift\"},\n", "        {\"name\": \"leverage\", \"type\": \"double\", \"description\": \"leverage\"},\n", "        {\"name\": \"conviction\", \"type\": \"double\", \"description\": \"conviction\"},\n", "        {\"name\": \"zhangs_metric\", \"type\": \"double\", \"description\": \"zhangs_metric\"},\n", "    ],\n", "    \"primary_key\": [\"antecedents\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"L1 to L1 ARM model results\",\n", "}\n", "\n", "pb.to_trino(l1_rules, **l1_kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "61cacf4c-9968-47e8-a11f-1aa04e86964d", "metadata": {}, "outputs": [], "source": ["channel = \"bl-personalization-notifications\"\n", "text = \"L1 to L1 ARM raw results table update\"\n", "# pb.send_slack_message(channel=channel, text=text)"]}, {"cell_type": "code", "execution_count": null, "id": "74f89462-4a48-4ccb-bdde-026e23be24e4", "metadata": {}, "outputs": [], "source": ["ptype_rules = []\n", "del l1_rules\n", "del te_ary_df\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "b8454dd8-dc37-4ce5-b237-e977fcbfb3e4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}