{"cells": [{"cell_type": "code", "execution_count": null, "id": "362013d9-7949-4a00-aa62-9de70e209c86", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e207f002-f70f-45b6-ba91-f86a2d542b0c", "metadata": {}, "outputs": [], "source": ["!pip install -q jupyter\n", "!pip install -q nbconvert"]}, {"cell_type": "code", "execution_count": null, "id": "b54068f8-f496-4556-9db6-9229dabfb010", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import os"]}, {"cell_type": "code", "execution_count": null, "id": "83910ab0-7775-48a2-a5cd-cbc4915dd7fe", "metadata": {}, "outputs": [], "source": ["dag_id = task_instance_key_str.split(\"__\")[0]\n", "task_id = task_instance_key_str.split(\"__\")[1]\n", "print(dag_id, task_id)"]}, {"cell_type": "code", "execution_count": null, "id": "240f076f-9a63-45d5-94ae-a382b809a9a9", "metadata": {}, "outputs": [], "source": ["dag_link = f\"https://dse-commuter.grofer.io/view/{dag_id}/{run_id}/{task_id}.ipynb\"\n", "print(dag_link)\n", "pb.send_slack_message(\n", "    channel=\"alerts-11\",\n", "    text=f\"<PERSON>hoc runner got started, \\n dag_link:- {dag_link} \\n cc:<@U07668YK86R> <@U071XRARZAS>\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "86fa8dc4-dd5c-4651-a6ac-eb25b3985848", "metadata": {}, "outputs": [], "source": ["if \"manual\" in run_id:\n", "    SHEET_ID = \"1uIHIDYoa5lsLNc6zrQAmrVVxCKDKUcfHHaBDg9EQ1Ok\"\n", "    SHEET_NAME = \"adhoc-runner\"\n", "    df = pb.from_sheets(sheetid=SHEET_ID, sheetname=SHEET_NAME)\n", "    for i, row in df.iterrows():\n", "        param = row[\"param\"]\n", "        value = row[\"value\"]\n", "        print(f\"{param} = {value}\")\n", "        exec(f\"{param} = value\")\n", "    df[\"value\"] = df.apply(lambda x: \"no\" if x.param == \"run_the_dag\" else x.value, axis=1)\n", "    pb.to_sheets(df, sheetid=SHEET_ID, sheetname=SHEET_NAME)\n", "    sheet_link = f\"https://docs.google.com/spreadsheets/d/{SHEET_ID}/\"\n", "    print(sheet_link)"]}, {"cell_type": "code", "execution_count": null, "id": "2a6ca1e8-5310-46d3-8dc0-c5f25198e0e9", "metadata": {}, "outputs": [], "source": ["assert run_the_dag == \"yes\""]}, {"cell_type": "code", "execution_count": null, "id": "bc15c69c-db31-4b58-82cd-e9d65e94e40f", "metadata": {}, "outputs": [], "source": ["pb.clone_repo(adhoc_repo, \"/home/<USER>/repo_folder/\", branch_to_fetch=branch_to_clone)"]}, {"cell_type": "code", "execution_count": null, "id": "e6341fdd-d41c-4b34-b3cf-95712fea7f49", "metadata": {}, "outputs": [], "source": ["os.chdir(f\"/home/<USER>/repo_folder/{adhoc_repo}/{ch_dir}\")"]}, {"cell_type": "code", "execution_count": null, "id": "2930e776-c58d-4dcb-bd6f-286af51b7b97", "metadata": {}, "outputs": [], "source": ["! pip install -qr {adhoc_requirements_loc}"]}, {"cell_type": "code", "execution_count": null, "id": "677e4dad-bdc8-4cce-a51d-952da9b28ef5", "metadata": {}, "outputs": [], "source": ["! jupyter nbconvert --to script {adhoc_dag_notebook_path}"]}, {"cell_type": "code", "execution_count": null, "id": "0b8c2416-b3f0-4e3a-8b64-cb8c8a044ba2", "metadata": {}, "outputs": [], "source": ["! python3 {adhoc_dag_notebook_path.replace('ipynb','py')}"]}, {"cell_type": "code", "execution_count": null, "id": "0b901a39-cda9-4707-b6a1-694af5bf668e", "metadata": {}, "outputs": [], "source": ["dag_link = f\"https://dse-commuter.grofer.io/view/{dag_id}/{run_id}/{task_id}.ipynb\"\n", "print(dag_link)\n", "pb.send_slack_message(\n", "    channel=\"alerts-11\",\n", "    text=f\"Adhoc runner ended,  \\n dag_link:- {dag_link} \\n cc:<@U07668YK86R> <@U071XRARZAS>\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "aa71c6ef-ab9e-4031-850f-d5ada5e5f3b1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}