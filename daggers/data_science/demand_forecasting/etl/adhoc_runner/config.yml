alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: adhoc_runner
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: very-high-cpu
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_science
notebook:
  parameters:
    run_id: '{{ run_id }}'
    task_instance_key_str: '{{ task_instance_key_str }}'
  priority_weight: 2
owner:
  email: <EMAIL>
  slack_id: S07DN04EBC0
path: data_science/demand_forecasting/etl/adhoc_runner
paused: false
pool: priority_pool
project_name: demand_forecasting
schedule:
  end_date: '2025-08-25T00:00:00'
  interval: 0 0 24 4 *
  start_date: '2025-06-04T00:00:00'
schedule_type: fixed
sla: 122 minutes
support_files: []
tags: []
template_name: notebook
version: 8
