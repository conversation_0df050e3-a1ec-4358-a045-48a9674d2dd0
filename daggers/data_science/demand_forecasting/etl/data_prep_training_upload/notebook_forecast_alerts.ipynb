{"cells": [{"cell_type": "code", "execution_count": null, "id": "b8987906-8a8b-4d79-aa19-0dba08ac51a5", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "73e4b370-e751-4009-a8aa-8dfc5884d87f", "metadata": {"tags": []}, "outputs": [], "source": ["!pip install awswrangler==3.9.1\n", "!pip install plotly\n", "!pip install kaleido==0.4.2"]}, {"cell_type": "code", "execution_count": null, "id": "1f28257c-3c98-42d3-a3dc-dac785bba34b", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import awswrangler as wr\n", "from datetime import timedelta, datetime\n", "\n", "pd.set_option(\"display.float_format\", \"{:.2f}\".format)\n", "\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "\n", "def read_parquet_data_from_s3(s3_path: str, suffix_list: list[str] = None) -> pd.DataFrame:\n", "    \"\"\"\n", "    --> Reads the data stored as a parquet file at the input s3 path and returns it as a pandas dataframe\n", "    Args:\n", "        s3_path: the s3 key where the data is present\n", "        suffix_list: list of suffix if list of data needs to be read\n", "    Returns: loaded data as a pandas dataframe\n", "    \"\"\"\n", "    if suffix_list is None:\n", "        data = wr.s3.read_parquet(\n", "            s3_path,\n", "            # dataset=True\n", "        )\n", "    else:\n", "        suffix_list = list(suffix_list)\n", "        data_list = []\n", "        if s3_path[-1] != \"/\":\n", "            s3_path += \"/\"\n", "        for suffix in suffix_list:\n", "            data_list.append(wr.s3.read_parquet(s3_path + str(suffix) + \"/\", dataset=True))\n", "        data = pd.concat(data_list)\n", "\n", "    return data\n", "\n", "\n", "def get_results_from_query(sql: str, con: str = \"[Warehouse] Trino\"):\n", "    data = pd.read_sql(sql, pb.get_connection(con))\n", "    return data"]}, {"cell_type": "code", "execution_count": null, "id": "7a927cfd-eae3-4f65-a0bb-b38547edc49d", "metadata": {}, "outputs": [], "source": ["def extract_rd_date(model_name: str) -> str:\n", "    \"\"\"Extracts run date from model name as YYYY/MM/DD\"\"\"\n", "    try:\n", "        rd_str = model_name.split(\"_\")[1].replace(\"rd-\", \"\")\n", "        return datetime.strptime(rd_str, \"%Y-%m-%d\").strftime(\"%Y/%m/%d\")\n", "    except Exception:\n", "        return None"]}, {"cell_type": "code", "execution_count": null, "id": "421e14c5-ffce-4726-9fda-5f9cb10693d1", "metadata": {}, "outputs": [], "source": ["def get_time_from_model(model_name):\n", "    # Extracts the timestamp part from model name\n", "    return model_name.split(\"_\")[2].replace(\"lt-\", \"\")\n", "\n", "\n", "def find_latest_full_train_model_paths(base_path, days_to_scan=15):\n", "    today = datetime.today()\n", "    tft_models = []\n", "    lgbm_models = []\n", "\n", "    for i in range(days_to_scan):\n", "        check_date = today - <PERSON><PERSON><PERSON>(days=i)\n", "        date_str = check_date.strftime(\"%Y/%m/%d\")\n", "        s3_prefix = f\"{base_path}{date_str}/fnv/production/data/processed/inference_prediction/\"\n", "\n", "        try:\n", "            folders = wr.s3.list_directories(s3_prefix)\n", "            for f in folders:\n", "                model_name = f.replace(s3_prefix, \"\").strip(\"/\")\n", "                if \"full-train\" in model_name:\n", "                    model_time = get_time_from_model(model_name)\n", "                    if \"tft\" in model_name:\n", "                        tft_models.append((model_time, f))\n", "                    elif \"lgbm\" in model_name:\n", "                        lgbm_models.append((model_time, f))\n", "        except Exception:\n", "            continue  # skip days where folder doesn't exist or can't be listed\n", "\n", "    # Sort models by timestamp descending\n", "    tft_models.sort(reverse=True)\n", "    lgbm_models.sort(reverse=True)\n", "\n", "    result = {\n", "        \"tft\": tft_models[0][1] if tft_models else None,\n", "        \"lgbm\": lgbm_models[0][1] if lgbm_models else None,\n", "    }\n", "\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "id": "d176eb22-c0b6-4417-8964-833bdb1089a2", "metadata": {}, "outputs": [], "source": ["base_path = \"s3://prod-dse-projects/demand/frontend_demand_forecasting/\"\n", "latest_ft_models = find_latest_full_train_model_paths(base_path)\n", "\n", "print(\"Latest TFT model path:\", latest_ft_models[\"tft\"])\n", "print(\"Latest LGBM model path:\", latest_ft_models[\"lgbm\"])"]}, {"cell_type": "code", "execution_count": null, "id": "238f1e68-9103-439d-9a50-a4364ec0f9c0", "metadata": {}, "outputs": [], "source": ["def find_latest_tl_prod_model_paths(base_path, days_to_scan=7):\n", "    today = datetime.today()\n", "    models_by_date = {}\n", "\n", "    for i in range(days_to_scan):\n", "        check_date = today - <PERSON><PERSON><PERSON>(days=i)\n", "        date_str = check_date.strftime(\"%Y/%m/%d\")\n", "        s3_prefix = f\"{base_path}{date_str}/fnv/production/data/processed/inference_prediction/\"\n", "\n", "        try:\n", "            folders = wr.s3.list_directories(s3_prefix)\n", "            for f in folders:\n", "                model_name = f.replace(s3_prefix, \"\").strip(\"/\")\n", "                if \"tl-prod\" in model_name:\n", "                    if date_str not in models_by_date:\n", "                        models_by_date[date_str] = {\"tft\": [], \"lgbm\": []}\n", "                    model_time = get_time_from_model(model_name)\n", "                    if \"tft\" in model_name:\n", "                        models_by_date[date_str][\"tft\"].append((model_time, f))\n", "                    elif \"lgbm\" in model_name:\n", "                        models_by_date[date_str][\"lgbm\"].append((model_time, f))\n", "        except Exception:\n", "            continue\n", "\n", "    # Check latest date with both TFT and LGBM\n", "    for date_str in sorted(models_by_date.keys(), reverse=True):\n", "        day_models = models_by_date[date_str]\n", "        if day_models[\"tft\"] and day_models[\"lgbm\"]:\n", "            day_models[\"tft\"].sort(reverse=True)\n", "            day_models[\"lgbm\"].sort(reverse=True)\n", "            return {\"tft\": day_models[\"tft\"][0][1], \"lgbm\": day_models[\"lgbm\"][0][1]}\n", "\n", "    print(\"⚠️ No date found with both TFT and LGBM tl-prod models. TL not trained completely.\")\n", "    return {\"tft\": None, \"lgbm\": None}"]}, {"cell_type": "code", "execution_count": null, "id": "b30db766-9c2b-4f88-b343-c155e2defab3", "metadata": {}, "outputs": [], "source": ["latest_tl_prod_models = find_latest_tl_prod_model_paths(base_path)\n", "print(\"Latest TL-prod TFT model:\", latest_tl_prod_models[\"tft\"])\n", "print(\"Latest TL-prod LGBM model:\", latest_tl_prod_models[\"lgbm\"])"]}, {"cell_type": "code", "execution_count": null, "id": "8e89ac0c-84b0-4bb2-84f2-4f9a866034fe", "metadata": {}, "outputs": [], "source": ["def get_prod_model_paths(base_path: str) -> dict:\n", "    query = \"\"\"\n", "    SELECT \n", "        distinct model_name,\n", "        MAX(updated_at_ist) AS applied_at_ist\n", "    FROM \n", "        ds_etls.demand_forecasting_base_feedback_post_processing_log\n", "    WHERE \n", "        current_replenishment_ts_ist = CURRENT_DATE + INTERVAL '2' DAY\n", "        AND model_name <> 'nan'\n", "    GROUP BY \n", "        model_name\n", "    \"\"\"\n", "\n", "    df = get_results_from_query(query)\n", "\n", "    model_paths = {}\n", "    for model_name in df[\"model_name\"]:\n", "        if \"tft\" in model_name:\n", "            model_type = \"tft\"\n", "        elif \"lgbm\" in model_name:\n", "            model_type = \"lgbm\"\n", "        else:\n", "            continue\n", "\n", "        rd_date = extract_rd_date(model_name)\n", "        if rd_date:\n", "            path = f\"{base_path}{rd_date}/fnv/production/data/processed/inference_prediction/{model_name}/\"\n", "            model_paths[model_type] = path\n", "\n", "    return model_paths"]}, {"cell_type": "code", "execution_count": null, "id": "2befd1e1-0638-4966-ba51-8e97c5018c09", "metadata": {}, "outputs": [], "source": ["prod_models = get_prod_model_paths(base_path)\n", "\n", "print(\"TFT model path:\", prod_models.get(\"tft\"))\n", "print(\"LGBM model path:\", prod_models.get(\"lgbm\"))"]}, {"cell_type": "code", "execution_count": null, "id": "35a68e57-064e-4b82-beda-fd8a1c589009", "metadata": {}, "outputs": [], "source": ["ft = pd.concat(\n", "    [\n", "        read_parquet_data_from_s3(latest_ft_models[\"tft\"]),\n", "        read_parquet_data_from_s3(latest_ft_models[\"lgbm\"]),\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8850dd02-5a83-4c7a-aaff-dc6601579979", "metadata": {}, "outputs": [], "source": ["tl = pd.concat(\n", "    [\n", "        read_parquet_data_from_s3(latest_tl_prod_models[\"tft\"]),\n", "        read_parquet_data_from_s3(latest_tl_prod_models[\"lgbm\"]),\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c7199e29-abbe-4bb3-a2e4-74c9b904280d", "metadata": {}, "outputs": [], "source": ["prod = pd.concat(\n", "    [read_parquet_data_from_s3(prod_models[\"tft\"]), read_parquet_data_from_s3(prod_models[\"lgbm\"])]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b58b645f-b9f8-4c39-96e4-6a955fbff0d7", "metadata": {}, "outputs": [], "source": ["active = get_results_from_query(\n", "    \"\"\"select\n", "    co.id outlet_id,\n", "    ic.item_id\n", "from\n", "    rpc.product_facility_master_assortment pfma\n", "join\n", "    retail.console_outlet co\n", "    on pfma.facility_id = co.facility_id\n", "    and co.active = 1\n", "join\n", "    rpc.item_category_details ic\n", "    on pfma.item_id = ic.item_id\n", "    and ic.l0_id = 1487\n", "where\n", "    master_assortment_substate_id = 1\n", "group by 1,2\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "aafc0126-1f0e-4d89-9124-74a4c20fa8f6", "metadata": {}, "outputs": [], "source": ["pot = get_results_from_query(\n", "    \"\"\"select distinct item_id from supply_etls.fresh_warehouse_store_details where product_type in ('Potato','Onion')\n", "                                and date_ = current_date - interval '2' day \"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6005b106-a8ab-4e29-8eb0-88c84daf6397", "metadata": {}, "outputs": [], "source": ["pot_items = pot.item_id.unique().tolist()"]}, {"cell_type": "code", "execution_count": null, "id": "bf559df7-cf69-402e-b943-ceacd2dee6f1", "metadata": {}, "outputs": [], "source": ["sales_overall = get_results_from_query(\n", "    \"\"\"select sum(qty_sold) as sales_overall, date_ as checkout_date from supply_etls.fresh_warehouse_store_details \n", "where date_ >= current_date - interval '14' day\n", "group by 2 order by 2 asc\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "848c19df-0c2d-4320-bcfc-a767273e3d2e", "metadata": {}, "outputs": [], "source": ["selected_quantiles = get_results_from_query(\n", "    \"\"\"select outlet_id, item_id, quantile from ds_etls.demand_forecast_item_ordering_with_feedback\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d49b47ed-334b-475b-a2f2-13c5c9d4b73a", "metadata": {}, "outputs": [], "source": ["tl[\"outlet_id\"] = tl[\"outlet_id\"].astype(int)\n", "tl[\"item_id\"] = tl[\"item_id\"].astype(int)\n", "ft[\"outlet_id\"] = ft[\"outlet_id\"].astype(int)\n", "ft[\"item_id\"] = ft[\"item_id\"].astype(int)\n", "prod[\"outlet_id\"] = prod[\"outlet_id\"].astype(int)\n", "prod[\"item_id\"] = prod[\"item_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "0725b1f4-72df-40b4-8d9e-33d9505a3523", "metadata": {}, "outputs": [], "source": ["ft = ft.merge(active, how=\"inner\", on=[\"outlet_id\", \"item_id\"])\n", "tl = tl.merge(active, how=\"inner\", on=[\"outlet_id\", \"item_id\"])\n", "prod = prod.merge(active, how=\"inner\", on=[\"outlet_id\", \"item_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "3c64977f-85bb-4297-9dca-01c5eaad2315", "metadata": {}, "outputs": [], "source": ["ft[\"quantile\"] = ft[\"quantile\"].astype(float)\n", "tl[\"quantile\"] = tl[\"quantile\"].astype(float)\n", "prod[\"quantile\"] = prod[\"quantile\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "561b682c-264d-4662-86c1-e6e584cb7d4c", "metadata": {}, "outputs": [], "source": ["ft = ft.merge(selected_quantiles, how=\"inner\", on=[\"outlet_id\", \"item_id\", \"quantile\"])\n", "tl = tl.merge(selected_quantiles, how=\"inner\", on=[\"outlet_id\", \"item_id\", \"quantile\"])\n", "prod = prod.merge(selected_quantiles, how=\"inner\", on=[\"outlet_id\", \"item_id\", \"quantile\"])"]}, {"cell_type": "code", "execution_count": null, "id": "bd337fc1-fd5a-40fc-a6fa-4ece293406b9", "metadata": {}, "outputs": [], "source": ["ft.drop(\n", "    columns=[c for c in [\"install_ts_ist\", \"model_name\", \"quantile\"] if c in ft.columns],\n", "    inplace=True,\n", ")\n", "tl.drop(\n", "    columns=[c for c in [\"install_ts_ist\", \"model_name\", \"quantile\"] if c in tl.columns],\n", "    inplace=True,\n", ")\n", "prod.drop(\n", "    columns=[c for c in [\"install_ts_ist\", \"model_name\", \"quantile\"] if c in prod.columns],\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d8bf4f99-97d1-4850-aa67-eaed35093e3f", "metadata": {}, "outputs": [], "source": ["for df in [ft, tl, prod]:\n", "    df[\"prediction\"] = np.where(df[\"prediction\"] < 1, 1, np.round(df[\"prediction\"]))"]}, {"cell_type": "code", "execution_count": null, "id": "a67b1250-30bf-4e3e-95fd-049990d39a6b", "metadata": {}, "outputs": [], "source": ["ft.rename(columns={\"prediction\": \"prediction_ft\"}, inplace=True)\n", "tl.rename(columns={\"prediction\": \"prediction_tl\"}, inplace=True)\n", "prod.rename(columns={\"prediction\": \"prediction_prod\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "55b4aa58-4e8e-478d-b6e8-478bd730fd2a", "metadata": {}, "outputs": [], "source": ["item_l2 = get_results_from_query(\n", "    \"\"\"select\n", "    item_id,\n", "    l2\n", "from\n", "    rpc.item_category_details\n", "where   \n", "    l0_id = 1487\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "09c48221-3608-4d10-aa2b-6bf1a41edcdf", "metadata": {}, "outputs": [], "source": ["ft = ft.merge(item_l2, how=\"left\", on=\"item_id\")\n", "tl = tl.merge(item_l2, how=\"left\", on=\"item_id\")\n", "prod = prod.merge(item_l2, how=\"left\", on=\"item_id\")"]}, {"cell_type": "code", "execution_count": null, "id": "4a92f8b5-2b9e-4f10-a3fc-634ae112e44a", "metadata": {}, "outputs": [], "source": ["for df in [ft, tl, prod]:\n", "    df.drop_duplicates(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "46c8442f-2ab4-48ba-b718-662f546a06d9", "metadata": {}, "outputs": [], "source": ["ft_overall = ft.groupby(\"checkout_date\", as_index=False).agg({\"prediction_ft\": \"sum\"})\n", "tl_overall = tl.groupby(\"checkout_date\", as_index=False).agg({\"prediction_tl\": \"sum\"})\n", "prod_overall = prod.groupby(\"checkout_date\", as_index=False).agg({\"prediction_prod\": \"sum\"})\n", "\n", "ft_l2 = ft.groupby([\"checkout_date\", \"l2\"], as_index=False).agg({\"prediction_ft\": \"sum\"})\n", "tl_l2 = tl.groupby([\"checkout_date\", \"l2\"], as_index=False).agg({\"prediction_tl\": \"sum\"})\n", "prod_l2 = prod.groupby([\"checkout_date\", \"l2\"], as_index=False).agg({\"prediction_prod\": \"sum\"})"]}, {"cell_type": "code", "execution_count": null, "id": "b1f2c08d-b765-414a-8411-685b3a9233d2", "metadata": {}, "outputs": [], "source": ["ft_overall_pot = (\n", "    ft[~ft.item_id.isin(pot_items)]\n", "    .groupby(\"checkout_date\", as_index=False)\n", "    .agg({\"prediction_ft\": \"sum\"})\n", ")\n", "tl_overall_pot = (\n", "    tl[~tl.item_id.isin(pot_items)]\n", "    .groupby(\"checkout_date\", as_index=False)\n", "    .agg({\"prediction_tl\": \"sum\"})\n", ")\n", "prod_overall_pot = (\n", "    prod[~prod.item_id.isin(pot_items)]\n", "    .groupby(\"checkout_date\", as_index=False)\n", "    .agg({\"prediction_prod\": \"sum\"})\n", ")\n", "\n", "ft_l2_pot = (\n", "    ft[~ft.item_id.isin(pot_items)]\n", "    .groupby([\"checkout_date\", \"l2\"], as_index=False)\n", "    .agg({\"prediction_ft\": \"sum\"})\n", ")\n", "tl_l2_pot = (\n", "    tl[~tl.item_id.isin(pot_items)]\n", "    .groupby([\"checkout_date\", \"l2\"], as_index=False)\n", "    .agg({\"prediction_tl\": \"sum\"})\n", ")\n", "prod_l2_pot = (\n", "    prod[~prod.item_id.isin(pot_items)]\n", "    .groupby([\"checkout_date\", \"l2\"], as_index=False)\n", "    .agg({\"prediction_prod\": \"sum\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "78b3d633-1939-4693-80ac-a48d3111b5d2", "metadata": {}, "outputs": [], "source": ["sales_overall_pot = get_results_from_query(\n", "    \"\"\"select sum(qty_sold) as sales_overall, date_ as checkout_date from supply_etls.fresh_warehouse_store_details \n", "where date_ >= current_date - interval '14' day and product_type not in ('<PERSON><PERSON><PERSON>','Onion')\n", "group by 2 order by 2 asc\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "72936c3c-5661-4ab7-a538-5b007c4b656b", "metadata": {}, "outputs": [], "source": ["import plotly.graph_objects as go\n", "import pandas as pd\n", "from datetime import datetime, timedelta\n", "\n", "# Rename columns and assign model labels\n", "ft_df = ft_overall.rename(columns={\"prediction_ft\": \"Forecast\", \"checkout_date\": \"Date\"})\n", "ft_df[\"Model\"] = \"FT\"\n", "\n", "tl_df = tl_overall.rename(columns={\"prediction_tl\": \"Forecast\", \"checkout_date\": \"Date\"})\n", "tl_df[\"Model\"] = \"TL\"\n", "\n", "prod_df = prod_overall.rename(columns={\"prediction_prod\": \"Forecast\", \"checkout_date\": \"Date\"})\n", "prod_df[\"Model\"] = \"Prod\"\n", "\n", "sales_df = sales_overall.rename(columns={\"sales_overall\": \"Forecast\", \"checkout_date\": \"Date\"})\n", "sales_df[\"Model\"] = \"Sales\"\n", "\n", "# Combine all into one dataframe\n", "combined_df = pd.concat([ft_df, tl_df, prod_df, sales_df], ignore_index=True)\n", "combined_df[\"Date\"] = pd.to_datetime(combined_df[\"Date\"])  # Ensure datetime\n", "combined_df = combined_df.sort_values(by=\"Date\")\n", "\n", "# Create display label\n", "combined_df[\"DateLabel\"] = combined_df[\"Date\"].dt.strftime(\"%a - %d/%m/%Y\")\n", "\n", "# Create figure\n", "fig = go.Figure()\n", "\n", "# Add forecast models (x = actual Date, use custom tick labels later)\n", "for model in [\"FT\", \"TL\", \"Prod\"]:\n", "    model_df = combined_df[combined_df[\"Model\"] == model]\n", "    fig.add_trace(\n", "        <PERSON><PERSON>(\n", "            x=model_df[\"Date\"],\n", "            y=model_df[\"Forecast\"] / 1e5,\n", "            mode=\"lines+markers\",\n", "            name=model,\n", "            line=dict(width=2),\n", "        )\n", "    )\n", "\n", "# Add sales\n", "sales_df_plot = combined_df[combined_df[\"Model\"] == \"Sales\"]\n", "fig.add_trace(\n", "    <PERSON><PERSON>(\n", "        x=sales_df_plot[\"Date\"],\n", "        y=sales_df_plot[\"Forecast\"] / 1e5,\n", "        mode=\"lines+markers\",\n", "        name=\"Sales\",\n", "        line=dict(width=3, dash=\"dash\", color=\"black\"),\n", "        marker=dict(symbol=\"circle\", size=7),\n", "    )\n", ")\n", "\n", "# === Add vertical line and label for T+2 ===\n", "t_plus_2 = datetime.today() + <PERSON><PERSON><PERSON>(days=2)\n", "t_plus_2_date = t_plus_2.date()\n", "\n", "fig.add_shape(\n", "    type=\"line\",\n", "    x0=t_plus_2_date,\n", "    x1=t_plus_2_date,\n", "    y0=0,\n", "    y1=1,\n", "    xref=\"x\",\n", "    yref=\"paper\",\n", "    line=dict(color=\"red\", width=2, dash=\"dot\"),\n", ")\n", "\n", "fig.add_annotation(\n", "    x=t_plus_2_date,\n", "    y=1.02,\n", "    xref=\"x\",\n", "    yref=\"paper\",\n", "    text=\"T+2\",\n", "    showarrow=False,\n", "    font=dict(color=\"red\", size=12),\n", ")\n", "\n", "# === Create tickvals and ticktext for x-axis ===\n", "unique_dates = combined_df[[\"Date\", \"DateLabel\"]].drop_duplicates().sort_values(\"Date\")\n", "tickvals = unique_dates[\"Date\"]\n", "ticktext = unique_dates[\"DateLabel\"]\n", "\n", "# Layout\n", "fig.update_layout(\n", "    title=\"Forecasts and Sales Over Time\",\n", "    xaxis_title=\"Date\",\n", "    yaxis_title=\"Forecast (in Lakhs)\",\n", "    xaxis=dict(tickmode=\"array\", tickvals=tickvals, ticktext=ticktext, tickangle=45, showgrid=True),\n", "    yaxis=dict(\n", "        showgrid=True,\n", "        ticksuffix=\"L\",\n", "        tickformat=\".1f\",\n", "    ),\n", "    legend_title=\"Model\",\n", "    height=500,\n", "    width=900,\n", "    template=\"plotly_white\",\n", "    hovermode=\"x unified\",\n", ")\n", "\n", "# Show and save\n", "fig.show()\n", "# fig.write_image(\"forecast_plot.png\", format=\"png\", scale=2)"]}, {"cell_type": "code", "execution_count": null, "id": "73bfa777-6cb6-4fe8-9233-bafdc29e4910", "metadata": {}, "outputs": [], "source": ["import plotly.graph_objects as go\n", "import pandas as pd\n", "from datetime import datetime, timedelta\n", "\n", "# Rename columns and assign model labels\n", "ft_df = ft_overall_pot.rename(columns={\"prediction_ft\": \"Forecast\", \"checkout_date\": \"Date\"})\n", "ft_df[\"Model\"] = \"FT\"\n", "\n", "tl_df = tl_overall_pot.rename(columns={\"prediction_tl\": \"Forecast\", \"checkout_date\": \"Date\"})\n", "tl_df[\"Model\"] = \"TL\"\n", "\n", "prod_df = prod_overall_pot.rename(columns={\"prediction_prod\": \"Forecast\", \"checkout_date\": \"Date\"})\n", "prod_df[\"Model\"] = \"Prod Model\"\n", "\n", "sales_df = sales_overall_pot.rename(columns={\"sales_overall\": \"Forecast\", \"checkout_date\": \"Date\"})\n", "sales_df[\"Model\"] = \"Sales\"\n", "\n", "# Combine all into one dataframe\n", "combined_df = pd.concat([ft_df, tl_df, prod_df, sales_df], ignore_index=True)\n", "combined_df[\"Date\"] = pd.to_datetime(combined_df[\"Date\"])  # Ensure datetime\n", "combined_df = combined_df.sort_values(by=\"Date\")\n", "\n", "# Create display label\n", "combined_df[\"DateLabel\"] = combined_df[\"Date\"].dt.strftime(\"%a - %d/%m/%Y\")\n", "\n", "# Create figure\n", "fig = go.Figure()\n", "\n", "# Add forecast models (x = actual Date, use custom tick labels later)\n", "for model in [\"FT\", \"TL\", \"Prod Model\"]:\n", "    model_df = combined_df[combined_df[\"Model\"] == model]\n", "    fig.add_trace(\n", "        <PERSON><PERSON>(\n", "            x=model_df[\"Date\"],\n", "            y=model_df[\"Forecast\"] / 1e5,\n", "            mode=\"lines+markers\",\n", "            name=model,\n", "            line=dict(width=2),\n", "        )\n", "    )\n", "\n", "# Add sales\n", "sales_df_plot = combined_df[combined_df[\"Model\"] == \"Sales\"]\n", "fig.add_trace(\n", "    <PERSON><PERSON>(\n", "        x=sales_df_plot[\"Date\"],\n", "        y=sales_df_plot[\"Forecast\"] / 1e5,\n", "        mode=\"lines+markers\",\n", "        name=\"Sales\",\n", "        line=dict(width=3, dash=\"dash\", color=\"black\"),\n", "        marker=dict(symbol=\"circle\", size=7),\n", "    )\n", ")\n", "\n", "# === Add vertical line and label for T+2 ===\n", "t_plus_2 = datetime.today() + <PERSON><PERSON><PERSON>(days=2)\n", "t_plus_2_date = t_plus_2.date()\n", "\n", "fig.add_shape(\n", "    type=\"line\",\n", "    x0=t_plus_2_date,\n", "    x1=t_plus_2_date,\n", "    y0=0,\n", "    y1=1,\n", "    xref=\"x\",\n", "    yref=\"paper\",\n", "    line=dict(color=\"red\", width=2, dash=\"dot\"),\n", ")\n", "\n", "fig.add_annotation(\n", "    x=t_plus_2_date,\n", "    y=1.02,\n", "    xref=\"x\",\n", "    yref=\"paper\",\n", "    text=\"T+2\",\n", "    showarrow=False,\n", "    font=dict(color=\"red\", size=12),\n", ")\n", "\n", "# === Create tickvals and ticktext for x-axis ===\n", "unique_dates = combined_df[[\"Date\", \"DateLabel\"]].drop_duplicates().sort_values(\"Date\")\n", "tickvals = unique_dates[\"Date\"]\n", "ticktext = unique_dates[\"DateLabel\"]\n", "\n", "# Layout\n", "fig.update_layout(\n", "    title=\"Forecasts and Sales Over Time - Without Po<PERSON><PERSON>, On<PERSON>\",\n", "    xaxis_title=\"Date\",\n", "    yaxis_title=\"Forecast (in Lakhs)\",\n", "    xaxis=dict(tickmode=\"array\", tickvals=tickvals, ticktext=ticktext, tickangle=45, showgrid=True),\n", "    yaxis=dict(\n", "        showgrid=True,\n", "        ticksuffix=\"L\",\n", "        tickformat=\".1f\",\n", "    ),\n", "    legend_title=\"Model\",\n", "    height=500,\n", "    width=900,\n", "    template=\"plotly_white\",\n", "    hovermode=\"x unified\",\n", ")\n", "\n", "# Show and save\n", "fig.show()\n", "fig.write_image(\"forecast_plot_pot.png\", format=\"png\", scale=2)"]}, {"cell_type": "code", "execution_count": null, "id": "a165938b-d779-4b03-a860-11ed5097a506", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"ds-fnv-critical-alerts\",\n", "    # text=f\"<!subteam^S07DN04EBC0>, Forecast chart generated as on {datetime.today().strftime('%d/%m/%Y')}\",\n", "    text=f\"<!channel>, Forecast chart generated as on {datetime.today().strftime('%d/%m/%Y')}\",\n", "    files=[\"forecast_plot_pot.png\"],\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}