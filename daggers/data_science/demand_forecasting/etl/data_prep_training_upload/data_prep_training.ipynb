{"cells": [{"cell_type": "code", "execution_count": null, "id": "73669d63-b310-4d1c-971c-c368b384ed1b", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "98b63a0b-67bb-493c-896c-1baf432abf6e", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import os"]}, {"cell_type": "code", "execution_count": null, "id": "d997e628-1da0-48db-a318-d835653fa312", "metadata": {}, "outputs": [], "source": ["dag_id = task_instance_key_str.split(\"__\")[0]\n", "task_id = task_instance_key_str.split(\"__\")[1]\n", "print(dag_id, task_id)"]}, {"cell_type": "code", "execution_count": null, "id": "60de7f10-1427-494a-96b2-1299e682f8be", "metadata": {}, "outputs": [], "source": ["dag_link = f\"https://dse-commuter.grofer.io/view/{dag_id}/{run_id}/{task_id}.ipynb\"\n", "print(dag_link)\n", "try:\n", "    pb.send_slack_message(\n", "        channel=\"bl-data-science-alerts\",\n", "        text=f\"Hi all, Daily model training dag for category:- {category} got started,  link with interpretation  \\n dag_link:- {dag_link} \\n cc:<@U056ML7E99R>\",\n", "    )\n", "except Exception as e:\n", "    print(f\"Failed to send Slack message: {e}\")"]}, {"cell_type": "code", "execution_count": null, "id": "f1d274e4-a4c6-4f8c-b6f7-7897e1119314", "metadata": {}, "outputs": [], "source": ["import datetime\n", "import pytz\n", "\n", "run_date = str(datetime.datetime.now(pytz.timezone(\"Asia/Kolkata\")).date()).replace(\"-\", \"/\")"]}, {"cell_type": "code", "execution_count": null, "id": "1f166ab7-f1bc-441f-944a-f1a87e386fc5", "metadata": {}, "outputs": [], "source": ["SHEET_ID = \"1Tixd-_xs0zHABO865ESNwRvmMhNa-GlEpnmsPuh9Fa4\"\n", "SHEET_NAME = f\"{category}\""]}, {"cell_type": "markdown", "id": "f1c3e583-590e-4d62-9dda-460e2cf41894", "metadata": {}, "source": ["## setting configs"]}, {"cell_type": "code", "execution_count": null, "id": "68ed6c46-30a2-48e3-88f1-0ad612fd9da3", "metadata": {}, "outputs": [], "source": ["if \"manual\" in run_id:\n", "    environment = \"test\"\n", "else:\n", "    environment = \"prod\"\n", "model_name_extention = environment\n", "print(environment)"]}, {"cell_type": "code", "execution_count": null, "id": "329a75de-d025-4e2a-98cf-10d28ecb176b", "metadata": {}, "outputs": [], "source": ["df = pb.from_sheets(sheetid=SHEET_ID, sheetname=f\"{SHEET_NAME}_static\")\n", "for i, row in df.iterrows():\n", "    param = row[\"param\"]\n", "    value = row[\"value\"]\n", "    print(f\"{param} = {value}\")\n", "    exec(f\"{param} = value\")\n", "sheet_link = f\"https://docs.google.com/spreadsheets/d/{SHEET_ID}/\"\n", "print(sheet_link)"]}, {"cell_type": "code", "execution_count": null, "id": "bc77900b-91b1-4719-8857-3c6e87998132", "metadata": {}, "outputs": [], "source": ["### this is only in case of categories using transfer learning\n", "try:\n", "    trino_conn = pb.get_connection(\"[Warehouse] Trino\")\n", "except:\n", "    trino_conn = pb.get_connection(\"[Warehouse] Trino\")\n", "try:\n", "    if category in (\"fnv\", \"milk\"):\n", "        base_model_query = f\"select distinct model_name from ds_etls.demand_forecasting_for_n_plus_two_days_{category} where model_name like '{base_model_regex}'\"\n", "        print(base_model_query)\n", "        print(\"------\")\n", "        base_df = pd.read_sql(\n", "            base_model_query,\n", "            trino_conn,\n", "        )\n", "        print(base_df)\n", "        base_model_name = base_df[\"model_name\"][0]\n", "        base_run_date = [i.replace(\"rd-\", \"\") for i in base_model_name.split(\"_\") if \"rd\" in i][\n", "            0\n", "        ].replace(\"-\", \"/\")\n", "        model_name_extention = f\"tl-{environment}\"\n", "    print(base_model_name, base_run_date)\n", "except Exception as e:\n", "    print(\"unable to fetch the base_model_info due to error :\")\n", "    print(e)"]}, {"cell_type": "code", "execution_count": null, "id": "c9663902-28ef-4610-83fd-eaf164bcfddf", "metadata": {}, "outputs": [], "source": ["base_model_query = f\"select * from ds_etls.demand_forecasting_for_n_plus_two_days_{category}\"\n", "print(base_model_query)\n", "print(\"------\")\n", "base_df = pd.read_sql(\n", "    base_model_query,\n", "    trino_conn,\n", ")\n", "print(base_df.shape)\n", "print(base_df)"]}, {"cell_type": "code", "execution_count": null, "id": "4d3a9c6b-e159-4c0d-bdbb-d4fa64929020", "metadata": {}, "outputs": [], "source": ["base_model_query = (\n", "    f\"select distinct model_name from ds_etls.demand_forecasting_for_n_plus_two_days_{category}\"\n", ")\n", "print(base_model_query)\n", "print(\"------\")\n", "base_df = pd.read_sql(\n", "    base_model_query,\n", "    trino_conn,\n", ")\n", "print(base_df.shape)\n", "print(base_df)"]}, {"cell_type": "code", "execution_count": null, "id": "8d865d7f-3fe4-4bce-9702-7836be7a20eb", "metadata": {}, "outputs": [], "source": ["if \"manual\" in run_id:\n", "    df = pb.from_sheets(sheetid=SHEET_ID, sheetname=f\"{SHEET_NAME}_dynamic\")\n", "    for i, row in df.iterrows():\n", "        print(\"------------------\")\n", "        param = row[\"param\"]\n", "        value = row[\"value\"]\n", "        print(f\"{param}_dynamic = {value}\")\n", "        exec(f\"{param}_dynamic = value\")\n", "        if value != \"default\":\n", "            print(f\"{param} = {param}_dynamic\")\n", "            exec(f\"{param} = {param}_dynamic\")\n", "    df[\"value\"] = \"default\"\n", "    pb.to_sheets(df, sheetid=SHEET_ID, sheetname=f\"{SHEET_NAME}_dynamic\")\n", "    sheet_link = f\"https://docs.google.com/spreadsheets/d/{SHEET_ID}/\"\n", "    print(sheet_link)"]}, {"cell_type": "code", "execution_count": null, "id": "2042d9b2-f763-47ec-96a1-b134b0d018df", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "604a3da6-a2a3-4146-b780-130baf1145c1", "metadata": {"tags": []}, "source": ["## Data Prep"]}, {"cell_type": "code", "execution_count": null, "id": "796a21df-1c58-463e-96a6-d4b71658cd44", "metadata": {"tags": []}, "outputs": [], "source": ["pb.clone_repo(\"ds-fnv-demand-forecasting\", \"/home/<USER>/repo_folder/\")"]}, {"cell_type": "code", "execution_count": null, "id": "fa40b108-335f-4ce0-82dc-f92efef51134", "metadata": {}, "outputs": [], "source": ["os.chdir(\"/home/<USER>/repo_folder/ds-fnv-demand-forecasting/script\")"]}, {"cell_type": "code", "execution_count": null, "id": "45cc3fed-1b3b-4b35-a4a6-983bd5e4c672", "metadata": {}, "outputs": [], "source": ["!pip install --default-timeout=100 -qr ../requirements.txt"]}, {"cell_type": "code", "execution_count": null, "id": "d2000971-7013-4582-9beb-028bcd9efd1d", "metadata": {}, "outputs": [], "source": ["# os.system(\"python3 feature_store.py\")"]}, {"cell_type": "code", "execution_count": null, "id": "b8792c49-165f-4a03-a980-04f8fcfa809e", "metadata": {"tags": []}, "outputs": [], "source": ["shell_list = []\n", "\n", "shell_list.append(f\"\"\"\\nif [ \"{run_data_prep}\" = \"run\" ]\"\"\")\n", "shell_list.append(\"\\nthen \\n\\techo 'run condition satisfied running the task'\\n\\t\")\n", "\n", "shell_list.append(\n", "    f\"python3 main.py --env={environment} --run_date={run_date} --update_base_tables False  --look_back_period={look_back_period} --category={category}\"\n", ")\n", "\n", "shell_list.append(\"\\nelse \\n\\techo 'run condition unsatisfied skipping the task'\\t\")\n", "shell_list.append(\"\\nfi\\n\\n\\n\")\n", "with open(\"shell_script.sh\", \"w\") as f:\n", "    f.writelines(shell_list)\n", "! cat shell_script.sh\n", "! chmod +x shell_script.sh\n", "! ./shell_script.sh"]}, {"cell_type": "code", "execution_count": null, "id": "40d065e8-585e-4cf5-9121-4088ed6ed3a6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "f9eb7c1e-5f28-4397-91db-62ad470f7bae", "metadata": {}, "source": ["## Model Training"]}, {"cell_type": "code", "execution_count": null, "id": "5c01c5cd-04c0-4d46-a780-664b8cfa68a2", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["os.chdir(\"/home/<USER>/repo_folder/ds-fnv-demand-forecasting/model_training\")"]}, {"cell_type": "code", "execution_count": null, "id": "5da8cbab-dada-4717-82d7-41f076406520", "metadata": {"tags": []}, "outputs": [], "source": ["!pip install --default-timeout=100 -qr requirements.txt\n", "!pip install -q awscli"]}, {"cell_type": "code", "execution_count": null, "id": "c0342ecc-3130-4a3f-a57e-2fbf57d06610", "metadata": {}, "outputs": [], "source": ["## transfering model  data to the required env\n", "\n", "to_env = {\"prod\": \"production\", \"dev\": \"development\", \"test\": \"test\"}[environment]\n", "copy_from_list = [\"production\", \"development\", \"test\"]\n", "copy_from_list.remove(to_env)\n", "\n", "transferring_ssh_list = []\n", "for env in copy_from_list:\n", "    transferring_ssh_list.append(\n", "        f\"aws s3 cp s3://prod-dse-projects/demand/frontend_demand_forecasting/{base_run_date}/{category}/{env}/data/base/models/ s3://prod-dse-projects/demand/frontend_demand_forecasting/{base_run_date}/{category}/{to_env}/data/base/models/ --recursive\\n\"\n", "    )\n", "with open(\"transfer_data_run.sh\", \"w\") as f:\n", "    f.writelines(transferring_ssh_list)\n", "! cat transfer_data_run.sh\n", "! chmod +x transfer_data_run.sh\n", "! ./transfer_data_run.sh"]}, {"cell_type": "code", "execution_count": null, "id": "026e0ea9-523b-4791-b309-b058efea821f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e2f22d84-87ba-467b-9baf-401d0a6194cc", "metadata": {"tags": []}, "outputs": [], "source": ["shell_list = []\n", "\n", "shell_list.append(f\"\"\"\\nif [ \"{run_training}\" = \"run\" ]\"\"\")\n", "shell_list.append(\"\\nthen \\n\\techo 'run condition satisfied running the task'\\n\\t\")\n", "\n", "shell_list.append(\n", "    f\"\"\"python3 main.py --run_date {run_date} --step 2 --models tft --seg True --gpu 1 --env {environment} --handle_anomaly True --tft_train_plus_val True --seg_fit_on train+validation --tft_epochs {tl_epochs} --transfer_learning_from_run_date '{base_run_date}' --transfer_learning_from_model '{base_model_name}' --train_on_how_many_days {train_on_how_many_days} --model_name_extention {model_name_extention}  --category {category}\\n\\t\"\"\"\n", ")\n", "shell_list.append(\n", "    f\"\"\"\n", "\\tpython3 main.py --run_date {run_date} --step 2 --models lgbm --seg False --gpu 0 --env {environment} --seg_fit_on train+validation --handle_anomaly True --transfer_learning_from_run_date '{base_run_date}' --transfer_learning_from_model '{base_model_name}' --train_on_how_many_days {train_on_how_many_days} --model_name_extention {model_name_extention} --category {category}\n", "\\n\"\"\"\n", ")\n", "\n", "shell_list.append(\"\\nelse \\n\\techo 'run condition unsatisfied skipping the task'\\t\")\n", "shell_list.append(\"\\nfi\\n\\n\\n\")\n", "with open(\"shell_script.sh\", \"w\") as f:\n", "    f.writelines(shell_list)\n", "! cat shell_script.sh\n", "! chmod +x shell_script.sh\n", "! ./shell_script.sh"]}, {"cell_type": "code", "execution_count": null, "id": "6cf28a48-c3c0-4c7b-a3a3-79674e00c555", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "0b16ac69-ce32-4a7f-9d63-d137e0e00fe8", "metadata": {}, "source": ["## upload predictions"]}, {"cell_type": "code", "execution_count": null, "id": "e94858e6-033f-4847-a230-9e08f524f524", "metadata": {}, "outputs": [], "source": ["ls = os.listdir(\"_TEMP/\")\n", "ls = list(\n", "    set(\n", "        [\n", "            x.split(model_name_extention)[0] + model_name_extention\n", "            for x in ls\n", "            if model_name_extention in x\n", "        ]\n", "    )\n", "    - set([base_model_name])\n", ")\n", "\n", "model_names = \" , \".join(ls)\n", "model_list = ls\n", "if not (\"tft\" in model_names and \"lgbm\" in model_names):\n", "    model_names = \"deliberately failing\"\n", "! python3 -m utils.upload_predictions --run_date '{run_date}' --env '{environment}' --model_names '{model_names}' --category '{category}' --post_process_predictions False --run_assertions False"]}, {"cell_type": "code", "execution_count": null, "id": "f8680108-e834-4248-8f28-32b77e607bd3", "metadata": {}, "outputs": [], "source": ["# ! python3 utils/alerts.py"]}, {"cell_type": "markdown", "id": "b3fb8e72-937c-4e5f-b660-f96ad4266c59", "metadata": {}, "source": ["## Model interpretation"]}, {"cell_type": "code", "execution_count": null, "id": "9b8d65b2-5894-4d1e-96bd-d533bee2d02c", "metadata": {}, "outputs": [], "source": ["tft_model_name = [i for i in model_list if \"tft\" in i][0]\n", "print(tft_model_name)"]}, {"cell_type": "code", "execution_count": null, "id": "76d88ba7-5498-4cc9-be47-5a935d4fa406", "metadata": {}, "outputs": [], "source": ["shell_list = []\n", "\n", "shell_list.append(\n", "    f\"python3 -m  utils.Interpret_tft_model --current_date '{run_date}' --environment '{environment}' --model_name '{tft_model_name}' --category '{category}'\"\n", ")\n", "\n", "with open(\"interpritaion_script.sh\", \"w\") as f:\n", "    f.writelines(shell_list)\n", "! cat interpritaion_script.sh\n", "! chmod +x interpritaion_script.sh\n", "! ./interpritaion_script.sh"]}, {"cell_type": "code", "execution_count": null, "id": "4e032165-666e-4b73-a61c-b39b54ed6713", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b2c35daf-5de2-4e34-b85f-e52050a75742", "metadata": {}, "outputs": [], "source": ["dag_link = f\"https://dse-commuter.grofer.io/view/{dag_id}/{run_id}/{task_id}.ipynb\"\n", "try:\n", "    pb.send_slack_message(\n", "        channel=\"bl-data-science-alerts\",\n", "        text=f\"Hi all, {category} DAG run completed link:- {dag_link} \\n cc:<@U056ML7E99R> , <@U05CCUDSWRX>\",\n", "    )\n", "except Exception as e:\n", "    print(f\"Failed to send Slack message: {e}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}