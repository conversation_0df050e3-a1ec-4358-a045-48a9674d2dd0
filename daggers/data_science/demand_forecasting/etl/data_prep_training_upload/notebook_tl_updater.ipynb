{"cells": [{"cell_type": "code", "execution_count": null, "id": "32626315-7183-4f9f-bc71-f44ef1c69350", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "44901eec-5370-454f-9909-3bb2893df919", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import os"]}, {"cell_type": "code", "execution_count": null, "id": "f5d2f010-b527-427d-a925-497e4ccfdbbb", "metadata": {}, "outputs": [], "source": ["dag_id = task_instance_key_str.split(\"__\")[0]\n", "task_id = task_instance_key_str.split(\"__\")[1]\n", "print(dag_id, task_id)"]}, {"cell_type": "code", "execution_count": null, "id": "ed7d8f79-998b-4a4f-ab71-6b21e0846775", "metadata": {}, "outputs": [], "source": ["dag_link = f\"https://dse-commuter.grofer.io/view/{dag_id}/{run_id}/{task_id}/try_1.ipynb\"\n", "print(dag_link)\n", "pb.send_slack_message(\n", "    channel=\"alerts-11\",\n", "    text=f\"TL Upload DAG started, \\n dag_link:- {dag_link} \\n cc:<@U071XRARZAS>\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c83bb720-8df6-409b-9f31-69bf606f06cc", "metadata": {}, "outputs": [], "source": ["!pip install -q jupyter\n", "!pip install -q nbconvert"]}, {"cell_type": "code", "execution_count": null, "id": "56a8a267-e970-48e2-b6e3-e1e13474dcae", "metadata": {}, "outputs": [], "source": ["pb.clone_repo(\"ds-fnv-demand-forecasting\", \"/home/<USER>/repo_folder/\", branch_to_fetch=\"master\")"]}, {"cell_type": "code", "execution_count": null, "id": "4f3a5f8c-00c3-4a99-b74f-e9a199070cdf", "metadata": {}, "outputs": [], "source": ["os.chdir(f\"/home/<USER>/repo_folder/ds-fnv-demand-forecasting/model_training\")"]}, {"cell_type": "code", "execution_count": null, "id": "b042fd0b-437f-4087-91ce-fa13d519bf2e", "metadata": {}, "outputs": [], "source": ["!pip install -qr requirements.txt"]}, {"cell_type": "code", "execution_count": null, "id": "7cace6ce-40c0-4bf4-8b1e-710794342c8b", "metadata": {}, "outputs": [], "source": ["notebook_to_run = \"adhoc_tl_updater.ipynb\""]}, {"cell_type": "code", "execution_count": null, "id": "f33f4d68-84a0-473a-8617-8b25c1a9bb7d", "metadata": {}, "outputs": [], "source": ["!jupyter nbconvert --to script {notebook_to_run}"]}, {"cell_type": "code", "execution_count": null, "id": "8a6e7bc7-6fed-490e-ae27-f82c60e55559", "metadata": {}, "outputs": [], "source": ["script_path = notebook_to_run.replace(\"ipynb\", \"py\")\n", "!python3 {script_path}"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}