alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 3
dag_name: data_prep_training_upload
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_science
notebooks:
- alias: mview_creation_all
  executor_config:
    load_type: low
    node_type: od
  name: mview_creation
  parameters:
    run_id: '{{ run_id }}'
    task_instance_key_str: '{{ task_instance_key_str }}'
  retries: 2
  retry_delay_in_seconds: 300
  tag: first
- alias: data_prep_training_fnv
  executor_config:
    load_type: ultra-high-cpu
    node_type: od
  name: data_prep_training
  parameters:
    category: fnv
    run_id: '{{ run_id }}'
    task_instance_key_str: '{{ task_instance_key_str }}'
  retries: 2
  retry_delay_in_seconds: 300
  tag: second
- alias: tl_updater
  executor_config:
    load_type: ultra-high-cpu
    node_type: od
  name: notebook_tl_updater
  parameters:
    category: fnv
    run_id: '{{ run_id }}'
    task_instance_key_str: '{{ task_instance_key_str }}'
  retries: 2
  retry_delay_in_seconds: 300
  tag: third
- alias: forecast_alerts
  executor_config:
    load_type: very-high-cpu
    node_type: spot
  name: notebook_forecast_alerts
  parameters:
    category: fnv
    run_id: '{{ run_id }}'
    task_instance_key_str: '{{ task_instance_key_str }}'
  retries: 2
  retry_delay_in_seconds: 300
  tag: fourth
owner:
  email: <EMAIL>
  slack_id: S07DN04EBC0
path: data_science/demand_forecasting/etl/data_prep_training_upload
paused: false
pool: priority_pool
project_name: demand_forecasting
schedule:
  end_date: '2025-08-15T00:00:00'
  interval: 35 19 * * *
  start_date: '2025-06-23T00:00:00'
schedule_type: fixed
sla: 132 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 17
