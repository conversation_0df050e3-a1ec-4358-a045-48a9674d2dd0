{"cells": [{"cell_type": "code", "execution_count": null, "id": "ed8ea47e-c67f-450e-9ee9-ff87d4d81541", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1d9655ee-1970-43a0-bede-26cc9aa5842e", "metadata": {}, "outputs": [], "source": ["import time\n", "from datetime import datetime\n", "\n", "notebook_start_time = time.time()\n", "start_time_str = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "print(f\"Notebook started at {start_time_str}\")"]}, {"cell_type": "code", "execution_count": null, "id": "18130095-fdf4-46b0-ac28-b3124f84fadc", "metadata": {}, "outputs": [], "source": ["last_checkpoint_time = notebook_start_time  # initialize globally\n", "\n", "\n", "def format_time(seconds):\n", "    hrs = int(seconds // 3600)\n", "    mins = int((seconds % 3600) // 60)\n", "    secs = seconds % 60\n", "    return f\"{hrs}h {mins}m {secs:.2f}s\"\n", "\n", "\n", "def checkpoint(label=\"Checkpoint\"):\n", "    global last_checkpoint_time\n", "    current_time = time.time()\n", "    elapsed_total = current_time - notebook_start_time\n", "    elapsed_since_last = current_time - last_checkpoint_time\n", "    print(\n", "        f\"[{label}] Total elapsed: {format_time(elapsed_total)} | Since last: {format_time(elapsed_since_last)}\"\n", "    )\n", "    last_checkpoint_time = current_time"]}, {"cell_type": "code", "execution_count": null, "id": "9c9a09b1-1d7c-4006-a294-239cefacb436", "metadata": {"tags": []}, "outputs": [], "source": ["!pip install awswrangler==3.9.1\n", "!pip install openpyxl\n", "!pip install sktime==0.19.1\n", "!pip install awscli==1.33.27\n", "!pip install catboost"]}, {"cell_type": "code", "execution_count": null, "id": "e25596ff-1fde-4ea4-acef-bb583461389f", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import awswrangler as wr\n", "from datetime import timedelta, datetime\n", "import matplotlib.pyplot as plt\n", "import time\n", "from catboost import CatBoostRegressor\n", "from sklearn.model_selection import TimeSeriesSplit"]}, {"cell_type": "code", "execution_count": null, "id": "1dc882f4-640e-42a8-9298-5cf8ab1140ea", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.float_format\", \"{:.2f}\".format)\n", "plt.style.use(\"ggplot\")\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "\n", "def read_parquet_data_from_s3(s3_path: str, suffix_list: list[str] = None) -> pd.DataFrame:\n", "    \"\"\"\n", "    --> Reads the data stored as a parquet file at the input s3 path and returns it as a pandas dataframe\n", "    Args:\n", "        s3_path: the s3 key where the data is present\n", "        suffix_list: list of suffix if list of data needs to be read\n", "    Returns: loaded data as a pandas dataframe\n", "    \"\"\"\n", "    if suffix_list is None:\n", "        data = wr.s3.read_parquet(\n", "            s3_path,\n", "            # dataset=True\n", "        )\n", "    else:\n", "        suffix_list = list(suffix_list)\n", "        data_list = []\n", "        if s3_path[-1] != \"/\":\n", "            s3_path += \"/\"\n", "        for suffix in suffix_list:\n", "            data_list.append(wr.s3.read_parquet(s3_path + str(suffix) + \"/\", dataset=True))\n", "        data = pd.concat(data_list)\n", "\n", "    return data\n", "\n", "\n", "def write_df_as_parquet_to_s3(data, s3_path, partition_cols=None, suffix=None):\n", "    mode = \"overwrite_partitions\" if partition_cols else \"overwrite\"\n", "\n", "    if suffix is not None:\n", "        s3_path = f\"{s3_path.strip('/')}/{suffix}\"\n", "\n", "    wr.s3.to_parquet(data, s3_path, dataset=True, mode=mode, partition_cols=partition_cols)"]}, {"cell_type": "code", "execution_count": null, "id": "c2453426-3f13-49b3-8298-3dfeca425475", "metadata": {}, "outputs": [], "source": ["def get_results_from_query(sql: str, con: str = \"[Warehouse] Trino\"):\n", "    # LOGGER.info(f\"Fetching data for the query \\n{sql[:50] + ' ...'}\")\n", "    data = pd.read_sql(sql, pb.get_connection(con))\n", "    return data"]}, {"cell_type": "code", "execution_count": null, "id": "0c722e9c-91e2-4c0e-adaf-eceb9470a4d2", "metadata": {}, "outputs": [], "source": ["conf = pb.from_sheets(\n", "    sheetid=\"14esxMsnZWKMkfkKKb0E76AEAFokVMfEPJsspEz6MV4Y\", sheetname=\"DTS_config\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a56e60c0-c5e6-49a7-9163-9da5f849738a", "metadata": {}, "outputs": [], "source": ["# conf = pd.read_csv(\"6pm_forecasting_config - DTS_config.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "9e29b818-0e70-4652-8720-606bce81cdaa", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "\n", "# Ensure forecast_date is parsed as datetime, errors='coerce' will turn invalid entries (like NaN) into NaT\n", "conf[\"forecast_date\"] = pd.to_datetime(conf[\"forecast_date\"], errors=\"coerce\")\n", "\n", "# Picking forecasting date\n", "if pd.isna(conf[\"forecast_date\"].iloc[0]):\n", "    fdate = (datetime.now() + <PERSON><PERSON>ta(days=2)).strftime(\"%Y-%m-%d\")\n", "else:\n", "    fdate = conf[\"forecast_date\"].iloc[0].strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "0f4b4db0-d3b3-46fc-b641-9c038daf90f1", "metadata": {}, "outputs": [], "source": ["f_datetime = datetime.strptime(fdate, \"%Y-%m-%d\")\n", "previous_day = (f_datetime - timedelta(days=2)).strftime(\"%Y/%m/%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "44431c2a-8d88-4754-a84d-949531329513", "metadata": {}, "outputs": [], "source": ["print(\"Picking Data Prep for:\", previous_day)"]}, {"cell_type": "markdown", "id": "a764bb5b-b16f-47b3-a61d-3de1bdcb8cae", "metadata": {"tags": []}, "source": ["### Training models for each quantile"]}, {"cell_type": "code", "execution_count": null, "id": "9d6550dc-bae3-4c5e-8e95-d1306c442e0f", "metadata": {}, "outputs": [], "source": ["base = read_parquet_data_from_s3(\n", "    f\"s3://prod-dse-projects/demand/frontend_demand_forecasting/dts_forecast/{previous_day}/base_exp\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "aaf7caa7-a946-4a79-ad37-f957a133671d", "metadata": {}, "outputs": [], "source": ["checkpoint(\"Data Fetch\")"]}, {"cell_type": "code", "execution_count": null, "id": "a71c5862-e0dc-4707-833a-119f0c90132b", "metadata": {}, "outputs": [], "source": ["from catboost import CatBoostRegressor\n", "from sklearn.model_selection import TimeSeriesSplit\n", "from sklearn.metrics import mean_absolute_error\n", "\n", "# Load and sort data by time\n", "base.sort_values(by=[\"outlet_id\", \"item_id\", \"checkout_date\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "886954fd-bc44-4d1d-8de4-cc8961b7f1fe", "metadata": {}, "outputs": [], "source": ["FEATURES = [\n", "    \"outlet_id\",\n", "    \"item_id\",\n", "    \"l2_id\",\n", "    \"ts_id\",\n", "    \"product_type\",\n", "    \"temp_min_growth_rolling_temp_max_7d\",\n", "    \"temp_max_growth_rolling_temp_min_7d\",\n", "    \"humidity_growth_rolling_humidity_mean_7d\",\n", "    \"precipitation_growth_rolling_precipitation_mean_7d\",\n", "    \"slot_0\",\n", "    \"slot_1\",\n", "    \"slot_2\",\n", "    \"slot_3\",\n", "    \"slot_4\",\n", "    \"slot_5\",\n", "    \"day_index_feature\",\n", "    \"dow\",\n", "    \"dom\",\n", "    \"month\",\n", "    \"wom\",\n", "]\n", "TARGET = \"qty_sold\"\n", "CATEGORICAL = [\n", "    \"outlet_id\",\n", "    \"item_id\",\n", "    \"l2_id\",\n", "    \"product_type\",\n", "    \"dow\",\n", "    \"dom\",\n", "    \"month\",\n", "    \"wom\",\n", "    \"ts_id\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "ff8d268b-4ea2-4014-9388-8fd302d9a50f", "metadata": {}, "outputs": [], "source": ["# Add new rolling mean, std, and velocity features dynamically\n", "for days in [7, 14, 30]:\n", "    FEATURES.append(f\"sales_mean_{days}d\")\n", "    FEATURES.append(f\"sales_std_{days}d\")\n", "\n", "# FEATURES.extend(['velocity_3_7', 'velocity_3_14'])\n", "FEATURES.extend([\"velocity_7_14\", \"velocity_7_30\"])\n", "\n", "print(\"Final Features List:\", FEATURES)"]}, {"cell_type": "code", "execution_count": null, "id": "acad239a-4080-4131-bb31-346254baf0fa", "metadata": {}, "outputs": [], "source": ["def get_train_inf_data(base, day_t):\n", "    \"\"\"Generate train and inference datasets with exactly 30 unique days before day_t - 3.\"\"\"\n", "    selected_days = sorted(\n", "        base.loc[base[\"day_index_feature\"] < (day_t - 3), \"day_index_feature\"].unique()\n", "    )[-30:]\n", "\n", "    train_mask = base[\"day_index_feature\"].isin(selected_days)\n", "    inf_mask = base[\"day_index_feature\"] == day_t\n", "\n", "    return (\n", "        base.loc[train_mask, FEATURES],\n", "        base.loc[train_mask, TARGET],\n", "        base.loc[inf_mask, FEATURES],\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "c4ba7fc8-b4bf-43cb-93cc-48d42049190d", "metadata": {}, "outputs": [], "source": ["import multiprocessing\n", "import time\n", "\n", "\n", "def fake_task(i):\n", "    for _ in range(120):\n", "        time.sleep(1)\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    pool = multiprocessing.Pool(16)\n", "    pool.map(fake_task, range(16))\n", "    pool.close()\n", "    pool.join()"]}, {"cell_type": "code", "execution_count": null, "id": "4d4d4779-b2d4-4d75-85a3-818bf5a406cd", "metadata": {}, "outputs": [], "source": ["checkpoint(\"vCPUs fetched using fake task\")"]}, {"cell_type": "code", "execution_count": null, "id": "023f9961-a28b-410c-af76-8e8f9dd9ea19", "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "from catboost import CatBoostRegressor\n", "\n", "\n", "def train_and_predict(base, quantiles):\n", "    results = []\n", "    max_day = int(base.day_index_feature.max())\n", "    print(f\"MAX DAY: {max_day}\")\n", "\n", "    for alpha in quantiles:\n", "        alpha_str = f\"{alpha:.2f}\".replace(\".\", \"\")\n", "        print(f\"Quantile: {alpha}\")\n", "\n", "        for day_t in range(max_day, max_day + 1):\n", "            cdate = base[base.day_index_feature == day_t][\"checkout_date\"].unique()[0]\n", "            print(f\"Processing quantile {alpha}, day {day_t} ({cdate})\")\n", "\n", "            X_train, y_train, X_inf = get_train_inf_data(base, day_t)\n", "\n", "            if y_train.empty or X_inf.empty:\n", "                print(f\"Skipping day {day_t} due to insufficient data.\")\n", "                continue  # Skip if no data\n", "\n", "            # Convert nullable columns to proper types\n", "            X_train.update(X_train.select_dtypes(\"Int64\").astype(\"int64\"))\n", "            X_inf.update(X_inf.select_dtypes(\"Int64\").astype(\"int64\"))\n", "            X_train.update(X_train.select_dtypes(\"Float64\").astype(\"float64\"))\n", "            X_inf.update(X_inf.select_dtypes(\"Float64\").astype(\"float64\"))\n", "\n", "            # Convert categorical columns to string\n", "            X_train[CATEGORICAL] = X_train[CATEGORICAL].astype(str)\n", "            X_inf[CATEGORICAL] = X_inf[CATEGORICAL].astype(str)\n", "\n", "            # Train the model\n", "            model = CatBoostRegressor(\n", "                iterations=2000,\n", "                learning_rate=0.03,\n", "                depth=10,\n", "                l2_leaf_reg=3,\n", "                border_count=100,\n", "                loss_function=f\"Quantile:alpha={alpha}\",\n", "                cat_features=CATEGORICAL,\n", "                random_seed=42,\n", "                verbose=100,\n", "                thread_count=16,  # Request 16 CPUs\n", "            )\n", "\n", "            print(f\"Training model for quantile {alpha} on day {day_t}\")\n", "            model.fit(X_train, y_train)\n", "\n", "            # Filter X_inf to match X_train's outlet-item combinations\n", "            X_inf = X_inf.merge(\n", "                X_train[[\"outlet_id\", \"item_id\"]].drop_duplicates(),\n", "                on=[\"outlet_id\", \"item_id\"],\n", "                how=\"inner\",\n", "            )\n", "\n", "            # Make predictions and store results\n", "            X_inf[\"quantile\"] = alpha\n", "            X_inf[\"prediction\"] = model.predict(X_inf)\n", "            X_inf[\"checkout_date\"] = cdate\n", "            results.append(X_inf)\n", "\n", "            print(f\"Processed {alpha_str}_{cdate} | Inference shape: {X_inf.shape}\")\n", "\n", "    return pd.concat(results, axis=0) if results else pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "id": "9b62ed41-871b-423d-82d0-c8a116017f14", "metadata": {"tags": []}, "outputs": [], "source": ["predictions = train_and_predict(base, quantiles=[0.9])"]}, {"cell_type": "code", "execution_count": null, "id": "c61e49e1-9517-42a0-9d31-0272ae8bc78d", "metadata": {}, "outputs": [], "source": ["predictions[\"outlet_id\"] = predictions[\"outlet_id\"].astype(int)\n", "predictions[\"item_id\"] = predictions[\"item_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "1aa7be19-2180-4440-b976-0fa6d12ab3ef", "metadata": {}, "outputs": [], "source": ["predictions[\"prediction_tr\"] = predictions[\"prediction\"].apply(lambda x: int(max(1, np.round(x))))"]}, {"cell_type": "code", "execution_count": null, "id": "09cade9d-6c54-4997-a0c8-aafbc9fbfcaf", "metadata": {}, "outputs": [], "source": ["checkpoint(\"predictions generated\")"]}, {"cell_type": "markdown", "id": "27c0c818-f38e-4c42-a527-80684cdcc821", "metadata": {}, "source": ["Pushing final predictions to table"]}, {"cell_type": "code", "execution_count": null, "id": "8602f602-165f-4abb-be91-585d4efbf3f6", "metadata": {}, "outputs": [], "source": ["final = predictions[[\"outlet_id\", \"item_id\", \"quantile\", \"prediction_tr\", \"checkout_date\"]].copy()"]}, {"cell_type": "markdown", "id": "f1c8fdad-9497-4aef-ad87-499e0272aae6", "metadata": {}, "source": ["Getting facility IDs"]}, {"cell_type": "code", "execution_count": null, "id": "ab0b2ccd-7f50-4e6e-acdf-82c75c463e54", "metadata": {}, "outputs": [], "source": ["fac = get_results_from_query(\n", "    \"\"\"select distinct outlet_id, facility_id from supply_etls.fresh_warehouse_store_details where date_ >= current_date - interval '30' day\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "dc08a47c-c8cc-4c38-a40f-37085f4fac41", "metadata": {}, "outputs": [], "source": ["final = final.merge(fac, how=\"left\", on=\"outlet_id\")"]}, {"cell_type": "code", "execution_count": null, "id": "65b33c16-e670-4e73-83a4-680979a178a1", "metadata": {}, "outputs": [], "source": ["final[\"min_qty\"] = final[\"prediction_tr\"]\n", "final[\"max_qty\"] = final[\"prediction_tr\"]"]}, {"cell_type": "code", "execution_count": null, "id": "46be82da-d778-4fdd-8e9f-d1c5073896bb", "metadata": {}, "outputs": [], "source": ["names = fac = get_results_from_query(\n", "    \"\"\"select\n", "   item_id,\n", "   name\n", "from\n", "   rpc.item_category_details\n", "where\n", "   l0_id = 1487\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "abe844f0-d6c2-498b-9538-c71672d02404", "metadata": {}, "outputs": [], "source": ["final = final.merge(names, how=\"left\", on=\"item_id\")"]}, {"cell_type": "code", "execution_count": null, "id": "75c194c8-5aca-42b6-9044-0d87e291fc9d", "metadata": {}, "outputs": [], "source": ["final[\"item_type\"] = \"fnv\""]}, {"cell_type": "code", "execution_count": null, "id": "9b2fafde-cdf1-47f5-b517-600719d91b6a", "metadata": {}, "outputs": [], "source": ["final[\"current_replenishment_ts_ist\"] = final[\"checkout_date\"]"]}, {"cell_type": "code", "execution_count": null, "id": "f80eb6c6-3f4d-4303-875a-ffff951a8480", "metadata": {}, "outputs": [], "source": ["import pytz\n", "\n", "final[\"updated_at_ist\"] = pd.to_datetime(\n", "    datetime.now(pytz.timezone(\"Asia/Calcutta\")).replace(tzinfo=None)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "89a81346-83f3-4a88-ba79-5eb2869a0b4c", "metadata": {}, "outputs": [], "source": ["final = final[\n", "    [\n", "        \"facility_id\",\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"name\",\n", "        \"item_type\",\n", "        \"quantile\",\n", "        \"min_qty\",\n", "        \"max_qty\",\n", "        \"current_replenishment_ts_ist\",\n", "        \"updated_at_ist\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "5564e164-ab82-4c57-9494-96c987e1443e", "metadata": {}, "outputs": [], "source": ["send_to_trino = True"]}, {"cell_type": "code", "execution_count": null, "id": "5e298da4-5820-46a4-aed5-db9fa70d4d89", "metadata": {}, "outputs": [], "source": ["print(final.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "4b67330c-f272-4622-b290-7a29cc9d9b50", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4907d3bd-0432-450d-b200-8c85d3ef6766", "metadata": {}, "outputs": [], "source": ["if send_to_trino:\n", "    # Define the kwargs for the Trino table update\n", "    kwargs = {\n", "        \"schema_name\": \"ds_etls\",\n", "        \"table_name\": \"exp_dts_forecast\",\n", "        \"column_dtypes\": [\n", "            {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"facility_id\"},\n", "            {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"outlet_id\"},\n", "            {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"item_id\"},\n", "            {\"name\": \"name\", \"type\": \"VARCHAR\", \"description\": \"item name\"},\n", "            {\"name\": \"item_type\", \"type\": \"VARCHAR\", \"description\": \"item name\"},\n", "            {\"name\": \"quantile\", \"type\": \"REAL\", \"description\": \"forecast quantile\"},\n", "            {\"name\": \"min_qty\", \"type\": \"REAL\", \"description\": \"forecast\"},\n", "            {\"name\": \"max_qty\", \"type\": \"REAL\", \"description\": \"forecast\"},\n", "            {\n", "                \"name\": \"current_replenishment_ts_ist\",\n", "                \"type\": \"TIMESTAMP(6)\",\n", "                \"description\": \"T+2 forecast date\",\n", "            },\n", "            {\n", "                \"name\": \"updated_at_ist\",\n", "                \"type\": \"TIMESTAMP(6)\",\n", "                \"description\": \"updated_at timestamp\",\n", "            },\n", "        ],\n", "        \"primary_key\": [\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"current_replenishment_ts_ist\",\n", "            \"quantile\",\n", "        ],  # Update primary_key\n", "        \"partition_key\": [\"current_replenishment_ts_ist\"],\n", "        \"incremental_key\": \"updated_at_ist\",\n", "        \"load_type\": \"upsert\",  # Ensure upsert operation\n", "        \"table_description\": \"catboost transfer forecasts T+2 DTS\",\n", "        \"force_upsert_without_increment_check\": True,\n", "    }\n", "\n", "    try:\n", "        # Perform the table update operation\n", "        pb.to_trino(data_obj=final, **kwargs)\n", "\n", "        # Send Slack message indicating success with the summary\n", "        pb.send_slack_message(\n", "            channel=\"alerts_aman\",\n", "            text=f\"<@U071XRARZAS> - The table 'exp_dts_forecast' has been successfully updated with the latest data for {fdate} and quantile 0.9\",\n", "        )\n", "\n", "    except Exception as e:\n", "        # Handle any exceptions and print the error message\n", "        print(f\"An error occurred: {e}\")\n", "\n", "        # Optionally, send a Slack message indicating failure (comment this out if not needed)\n", "        pb.send_slack_message(\n", "            channel=\"alerts_aman\",\n", "            text=\"<@U071XRARZAS> - An error occurred while updating the table 'exp_dts_forecast' for quantile 0.9. Error: {}\".format(\n", "                e\n", "            ),\n", "        )"]}, {"cell_type": "code", "execution_count": null, "id": "f29f958b-1e55-4769-b99a-778587c82dc5", "metadata": {}, "outputs": [], "source": ["checkpoint(\"pushed to table\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}