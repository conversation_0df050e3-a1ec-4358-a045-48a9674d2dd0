{"cells": [{"cell_type": "code", "execution_count": null, "id": "18b8218c-4afa-4904-a7c4-60742bd238a2", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "18965d11-106b-4490-b51f-90b60b575345", "metadata": {}, "outputs": [], "source": ["import time\n", "from datetime import datetime\n", "\n", "notebook_start_time = time.time()\n", "start_time_str = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "print(f\"Notebook started at {start_time_str}\")"]}, {"cell_type": "code", "execution_count": null, "id": "02210e9f-44ed-477a-8e6f-756b6dda6a32", "metadata": {}, "outputs": [], "source": ["last_checkpoint_time = notebook_start_time  # initialize globally\n", "\n", "\n", "def format_time(seconds):\n", "    hrs = int(seconds // 3600)\n", "    mins = int((seconds % 3600) // 60)\n", "    secs = seconds % 60\n", "    return f\"{hrs}h {mins}m {secs:.2f}s\"\n", "\n", "\n", "def checkpoint(label=\"Checkpoint\"):\n", "    global last_checkpoint_time\n", "    current_time = time.time()\n", "    elapsed_total = current_time - notebook_start_time\n", "    elapsed_since_last = current_time - last_checkpoint_time\n", "    print(\n", "        f\"[{label}] Total elapsed: {format_time(elapsed_total)} | Since last: {format_time(elapsed_since_last)}\"\n", "    )\n", "    last_checkpoint_time = current_time"]}, {"cell_type": "code", "execution_count": null, "id": "1c42a165-6394-4d99-9eef-c15c18e43bf4", "metadata": {"tags": []}, "outputs": [], "source": ["!pip install awswrangler==3.9.1\n", "!pip install openpyxl\n", "!pip install sktime==0.19.1\n", "!pip install awscli==1.33.27\n", "!pip install catboost"]}, {"cell_type": "code", "execution_count": null, "id": "3ce25bf6-5141-47a9-a4d7-ed906f78f2e7", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import awswrangler as wr\n", "from datetime import timedelta, datetime\n", "import matplotlib.pyplot as plt\n", "from catboost import CatBoostRegressor\n", "from sklearn.model_selection import TimeSeriesSplit"]}, {"cell_type": "code", "execution_count": null, "id": "b79ce271-fe9f-4d29-bc3e-5910f8021aed", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.float_format\", \"{:.2f}\".format)\n", "plt.style.use(\"ggplot\")\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "\n", "def read_parquet_data_from_s3(s3_path: str, suffix_list: list[str] = None) -> pd.DataFrame:\n", "    \"\"\"\n", "    --> Reads the data stored as a parquet file at the input s3 path and returns it as a pandas dataframe\n", "    Args:\n", "        s3_path: the s3 key where the data is present\n", "        suffix_list: list of suffix if list of data needs to be read\n", "    Returns: loaded data as a pandas dataframe\n", "    \"\"\"\n", "    if suffix_list is None:\n", "        data = wr.s3.read_parquet(\n", "            s3_path,\n", "            # dataset=True\n", "        )\n", "    else:\n", "        suffix_list = list(suffix_list)\n", "        data_list = []\n", "        if s3_path[-1] != \"/\":\n", "            s3_path += \"/\"\n", "        for suffix in suffix_list:\n", "            data_list.append(wr.s3.read_parquet(s3_path + str(suffix) + \"/\", dataset=True))\n", "        data = pd.concat(data_list)\n", "\n", "    return data\n", "\n", "\n", "def write_df_as_parquet_to_s3(data, s3_path, partition_cols=None, suffix=None):\n", "    mode = \"overwrite_partitions\" if partition_cols else \"overwrite\"\n", "\n", "    if suffix is not None:\n", "        s3_path = f\"{s3_path.strip('/')}/{suffix}\"\n", "\n", "    wr.s3.to_parquet(data, s3_path, dataset=True, mode=mode, partition_cols=partition_cols)"]}, {"cell_type": "code", "execution_count": null, "id": "f1b24d20-fbc3-4674-9492-9d2372a02892", "metadata": {}, "outputs": [], "source": ["def get_results_from_query(sql: str, con: str = \"[Warehouse] Trino\"):\n", "    # LOGGER.info(f\"Fetching data for the query \\n{sql[:50] + ' ...'}\")\n", "    data = pd.read_sql(sql, pb.get_connection(con))\n", "    return data"]}, {"cell_type": "markdown", "id": "7573abbf-9958-463d-8034-7d597aaf3dfa", "metadata": {}, "source": ["### Data Preparation"]}, {"cell_type": "code", "execution_count": null, "id": "27468af6-0754-417f-b67b-1d2063497e59", "metadata": {}, "outputs": [], "source": ["conf = pb.from_sheets(\n", "    sheetid=\"14esxMsnZWKMkfkKKb0E76AEAFokVMfEPJsspEz6MV4Y\", sheetname=\"DTS_config\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "044c7f99-a508-4e88-ab5b-16ce2e0a9835", "metadata": {}, "outputs": [], "source": ["# conf = pd.read_csv(\"6pm_forecasting_config - DTS_config.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "d0208571-a365-433e-8aa3-4abb172ac760", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "\n", "# Ensure forecast_date is parsed as datetime, errors='coerce' will turn invalid entries (like NaN) into NaT\n", "conf[\"forecast_date\"] = pd.to_datetime(conf[\"forecast_date\"], errors=\"coerce\")\n", "\n", "# Picking forecasting date\n", "if pd.isna(conf[\"forecast_date\"].iloc[0]):\n", "    fdate = (datetime.now() + <PERSON><PERSON>ta(days=2)).strftime(\"%Y-%m-%d\")\n", "else:\n", "    fdate = conf[\"forecast_date\"].iloc[0].strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "ea2618ed-df23-4133-a2c0-b7845b50a620", "metadata": {}, "outputs": [], "source": ["f_datetime = datetime.strptime(fdate, \"%Y-%m-%d\")\n", "previous_day = (f_datetime - timedelta(days=2)).strftime(\"%Y/%m/%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "bc794589-2e38-4496-acf7-a483353ec36e", "metadata": {}, "outputs": [], "source": ["print(\"Picking Data Prep for:\", previous_day)"]}, {"cell_type": "code", "execution_count": null, "id": "c50cba9a-4bf8-4482-a296-9dc6dd669770", "metadata": {}, "outputs": [], "source": ["# base=read_parquet_data_from_s3(f\"s3://prod-dse-projects/demand/frontend_demand_forecasting/{previous_day}/fnv/production/new_pipelines/processed/full_processed_data/\")"]}, {"cell_type": "code", "execution_count": null, "id": "098f758d-9128-46af-92f8-5a70921c35f4", "metadata": {}, "outputs": [], "source": ["MAX_RETRIES = 15\n", "WAIT_TIME = 60  # seconds\n", "\n", "\n", "def read_and_flag_data_with_retry(path, model_flag):\n", "    for attempt in range(MAX_RETRIES):\n", "        try:\n", "            print(f\"Attempt {attempt + 1}: Reading from {path}\")\n", "            df = read_parquet_data_from_s3(path)\n", "            df[\"model_flag\"] = model_flag\n", "            return df\n", "        except Exception as e:\n", "            if \"No files Found\" in str(e) or isinstance(e, FileNotFoundError):\n", "                print(f\"Retry {attempt + 1}/{MAX_RETRIES} for {path} - {e}\")\n", "                time.sleep(WAIT_TIME)\n", "            else:\n", "                raise\n", "    raise TimeoutError(f\"Failed to load data from {path} after {MAX_RETRIES} retries.\")\n", "\n", "\n", "train_path_tft = f\"s3://prod-dse-projects/demand/frontend_demand_forecasting/{previous_day}/fnv/production/new_pipelines/processed/train/tft/\"\n", "validation_path_tft = f\"s3://prod-dse-projects/demand/frontend_demand_forecasting/{previous_day}/fnv/production/new_pipelines/processed/validation/tft/\"\n", "train_path_lgbm = f\"s3://prod-dse-projects/demand/frontend_demand_forecasting/{previous_day}/fnv/production/new_pipelines/processed/train/lgbm/\"\n", "validation_path_lgbm = f\"s3://prod-dse-projects/demand/frontend_demand_forecasting/{previous_day}/fnv/production/new_pipelines/processed/validation/lgbm/\"\n", "inference_path_tft = f\"s3://prod-dse-projects/demand/frontend_demand_forecasting/{previous_day}/fnv/production/new_pipelines/processed/inference/tft/\"\n", "inference_path_lgbm = f\"s3://prod-dse-projects/demand/frontend_demand_forecasting/{previous_day}/fnv/production/new_pipelines/processed/inference/lgbm/\"\n", "\n", "# Define paths and corresponding flags\n", "path_flag_pairs = [\n", "    (train_path_tft, \"tft\"),\n", "    (train_path_lgbm, \"lgbm\"),\n", "    (validation_path_tft, \"tft\"),\n", "    (validation_path_lgbm, \"lgbm\"),\n", "    (inference_path_tft, \"tft\"),\n", "    (inference_path_lgbm, \"lgbm\"),\n", "]\n", "\n", "# Initialize an empty dataframe and incrementally concat\n", "base = pd.DataFrame()\n", "for path, flag in path_flag_pairs:\n", "    df = read_and_flag_data_with_retry(path, flag)\n", "    base = pd.concat([base, df], ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "98e60bed-8117-49b2-9a67-efaa88d26ac7", "metadata": {}, "outputs": [], "source": ["print(base.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "b28bf97e-ceca-4b5a-af89-66eefcf909a6", "metadata": {}, "outputs": [], "source": ["checkpoint(\"After data prep\")"]}, {"cell_type": "markdown", "id": "7ef28c43-5bb8-4906-a248-625865eafdd0", "metadata": {}, "source": ["Picking DTS outlets"]}, {"cell_type": "code", "execution_count": null, "id": "0b47ee06-0ca6-48b8-9126-ced2f69f39f3", "metadata": {}, "outputs": [], "source": ["dts_stores = get_results_from_query(\n", "    \"\"\"with pfom as (\n", "select \n", "    outlet_id\n", "from \n", "    po.physical_facility_outlet_mapping\n", "where \n", "    lake_active_record \n", "    and active = 1 \n", "    and ars_active = 1\n", "group by 1),\n", "\n", "bfom as (\n", "select \n", "    outlet_id, \n", "    facility_id\n", "from \n", "    po.bulk_facility_outlet_mapping\n", "where \n", "    lake_active_record \n", "    and active = true\n", "group by 1,2\n", "),\n", "\n", "co as (\n", "select \n", "    id as outlet_id, \n", "    facility_id, \n", "    business_type_id, \n", "    name\n", "from \n", "    retail.console_outlet\n", "where \n", "    lake_active_record \n", "    and  active = 1\n", "),\n", "\n", "icd as (\n", "select \n", "    item_id, \n", "    l0_id\n", "from \n", "    rpc.item_category_details\n", "where \n", "    lake_active_record \n", "    and l0_id = 1487\n", "),\n", "\n", "iotm as (\n", "select \n", "    tag_value as backend_outlet_id, \n", "    outlet_id, \n", "    item_id\n", "from \n", "    rpc.item_outlet_tag_mapping \n", "where \n", "    lake_active_record \n", "    and active = 1 \n", "    and tag_type_id = 8\n", "),\n", "\n", "pfma as (\n", "select \n", "    facility_id, \n", "    item_id\n", "from \n", "    rpc.product_facility_master_assortment\n", "where \n", "    lake_active_record \n", "    and active = 1 \n", "    and master_assortment_substate_id in (1)\n", "),\n", "\n", "\n", "dt as (\n", "select \n", "    iotm.backend_outlet_id, \n", "    co1.facility_id as backend_facility_id, \n", "    bfom.facility_id as bfom_backend_facility_id, \n", "    iotm.outlet_id, \n", "    co.facility_id, \n", "    iotm.item_id\n", "from \n", "    iotm\n", "inner join \n", "    co \n", "    on co.outlet_id = iotm.outlet_id \n", "    and business_type_id = 7 \n", "    and lower(name) not like ('%%dummy%%')\n", "inner join \n", "    pfma \n", "    on pfma.facility_id = co.facility_id \n", "    and pfma.item_id = iotm.item_id\n", "inner join \n", "    pfom \n", "    on pfom.outlet_id = iotm.outlet_id\n", "inner join \n", "    co as co1 \n", "    on co1.outlet_id = cast(iotm.backend_outlet_id as int)\n", "    and co1.business_type_id = 19\n", "left join \n", "    bfom \n", "    on bfom.outlet_id = iotm.outlet_id \n", "    and co1.facility_id = bfom.facility_id\n", "inner join \n", "    icd \n", "    on icd.item_id = iotm.item_id\n", "\n", ")\n", "\n", "select \n", "    backend_outlet_id,\n", "    outlet_id\n", "from \n", "    dt\n", "join\n", "    rpc.item_category_details ic\n", "    on dt.item_id = ic.item_id\n", "where\n", "    bfom_backend_facility_id = backend_facility_id\n", "    and cast(backend_outlet_id as int) not in (2666,2665,5094)\n", "group by 1,2\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "66b45119-f28b-428a-aa55-85c32b1e1f42", "metadata": {}, "outputs": [], "source": ["base = base[base[\"outlet_id\"].isin(dts_stores[\"outlet_id\"].astype(int).unique().tolist())].copy()"]}, {"cell_type": "code", "execution_count": null, "id": "22971a21-ebf2-46a5-973f-03dba0b3405e", "metadata": {}, "outputs": [], "source": ["print(base.checkout_date.min(), base.checkout_date.max(), base.checkout_date.nunique())"]}, {"cell_type": "code", "execution_count": null, "id": "ef7e07d2-8396-48f1-a940-0d3c2110e233", "metadata": {}, "outputs": [], "source": ["base[\"checkout_date\"] = pd.to_datetime(base[\"checkout_date\"])"]}, {"cell_type": "code", "execution_count": null, "id": "8da358f1-7d4f-4d7e-b3a3-ab3b141383e7", "metadata": {}, "outputs": [], "source": ["print(base.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "2d0e8c4b-d3e0-4c01-bf5c-d683247e979a", "metadata": {}, "outputs": [], "source": ["print(base.checkout_date.min(), base.checkout_date.max(), base.checkout_date.nunique())"]}, {"cell_type": "code", "execution_count": null, "id": "9496fcc6-415e-4a91-a173-9006b00bf7fa", "metadata": {}, "outputs": [], "source": ["base = base[(base[\"checkout_date\"] <= base[\"checkout_date\"].max() - pd.Timedel<PERSON>(days=8))]"]}, {"cell_type": "code", "execution_count": null, "id": "8e128a14-2629-4669-a964-5218cf95f8b2", "metadata": {}, "outputs": [], "source": ["print(base.checkout_date.min(), base.checkout_date.max(), base.checkout_date.nunique())"]}, {"cell_type": "code", "execution_count": null, "id": "a876d891-3e78-4524-a79e-add3438ab310", "metadata": {}, "outputs": [], "source": ["base = base[\n", "    base.groupby(\"city_name\")[\"checkout_date\"].transform(lambda x: x.isin(sorted(x.unique())[-90:]))\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "720ecf04-a890-4614-9aa7-d6c58274e254", "metadata": {}, "outputs": [], "source": ["print(base.groupby([\"city_name\"]).agg({\"checkout_date\": \"nunique\"}))"]}, {"cell_type": "code", "execution_count": null, "id": "b0ca3f02-2906-482b-9c7b-3d32586c0443", "metadata": {}, "outputs": [], "source": ["print(base.shape)"]}, {"cell_type": "markdown", "id": "dec00ceb-df79-4733-8eb6-709188b29e56", "metadata": {}, "source": ["Marking Anomaly Days"]}, {"cell_type": "code", "execution_count": null, "id": "03478e06-4a96-4453-9f21-05c7e7303cd3", "metadata": {}, "outputs": [], "source": ["anomalies = pb.from_sheets(\n", "    sheetid=\"1_61PjTYeJSE_PmcVwmL4PiZMLRAXB4oij5Qyh-mlpXs\", sheetname=\"anomaly-city\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2ea81764-9108-43c2-8184-2658de9c46fb", "metadata": {}, "outputs": [], "source": ["# anomalies = pd.read_csv(\"Assortment Category Input - FnV - anomaly-city (8).csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "4dda6f32-0165-4f6d-aac5-0ae549ab57d9", "metadata": {}, "outputs": [], "source": ["anomalies_all = anomalies.loc[anomalies.city_name == \"ALL\"]"]}, {"cell_type": "code", "execution_count": null, "id": "bfb0a479-f64c-4819-9e7d-7f179d320081", "metadata": {}, "outputs": [], "source": ["# Ensure datetime format\n", "anomalies[\"start_date\"] = pd.to_datetime(anomalies[\"start_date\"])\n", "anomalies[\"end_date\"] = pd.to_datetime(anomalies[\"end_date\"])\n", "\n", "# Get all anomalous dates across all ranges\n", "anomaly_dates = set()\n", "for start, end in zip(anomalies[\"start_date\"], anomalies[\"end_date\"]):\n", "    anomaly_dates.update(pd.date_range(start, end))\n", "\n", "# Optional: convert to sorted list of just dates (not timestamps)\n", "anomaly_dates = sorted([d.date() for d in anomaly_dates])"]}, {"cell_type": "code", "execution_count": null, "id": "f80e0af8-8db2-4436-8578-2596fd7cecf2", "metadata": {}, "outputs": [], "source": ["checkpoint(\"base prep 1\")"]}, {"cell_type": "markdown", "id": "a873c439-7083-49cc-85be-04f7b4d88114", "metadata": {"tags": []}, "source": ["Base Created"]}, {"cell_type": "markdown", "id": "5a2945df-4975-44f4-b7f6-491559360654", "metadata": {}, "source": ["### Features Preparation/Engg"]}, {"cell_type": "code", "execution_count": null, "id": "bca1dba4-731d-4f01-b777-7f0969f8a33e", "metadata": {}, "outputs": [], "source": ["base.sort_values([\"model_flag\", \"outlet_id\", \"item_id\", \"checkout_date\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "c7e60b23-3b12-4563-8d69-aa750b278213", "metadata": {}, "outputs": [], "source": ["outlet_id_list = tuple(base.outlet_id.dropna().unique().astype(int).tolist())\n", "item_id_list = tuple(base.item_id.dropna().unique().astype(int).tolist())"]}, {"cell_type": "code", "execution_count": null, "id": "f964742b-d93a-4410-a07d-3d75537b2079", "metadata": {}, "outputs": [], "source": ["# Get the min and max checkout_date from base dataframe\n", "min_date = base[\"checkout_date\"].min().strftime(\"%Y-%m-%d\")\n", "max_date = (base[\"checkout_date\"].max() - pd.<PERSON><PERSON><PERSON>(days=3)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "500c37e2-a4e5-434c-a543-4da57da018e6", "metadata": {}, "outputs": [], "source": ["min_date"]}, {"cell_type": "code", "execution_count": null, "id": "07745b7f-2726-42aa-9c41-ee2f3474ccb8", "metadata": {}, "outputs": [], "source": ["max_date"]}, {"cell_type": "code", "execution_count": null, "id": "ccd26c26-29c2-4f94-b77f-43470cabf52b", "metadata": {}, "outputs": [], "source": ["sales = get_results_from_query(\n", "    f\"\"\"\n", "    SELECT\n", "        DATE(o.order_create_ts_ist) AS date_,\n", "        fm.pos_outlet_id AS outlet_id,\n", "        ip.item_id,\n", "        SUM(o.procured_quantity * ip.multiplier) AS qty_sold\n", "    FROM\n", "        dwh.fact_sales_order_item_details o\n", "    INNER JOIN\n", "        dwh.dim_product dp \n", "        ON dp.product_id = o.product_id\n", "        AND dp.is_current\n", "    INNER JOIN\n", "        dwh.dim_merchant_outlet_facility_mapping fm\n", "        ON o.frontend_merchant_id = fm.frontend_merchant_id\n", "        AND fm.is_current = TRUE \n", "        AND fm.is_mapping_enabled = TRUE \n", "        AND fm.is_frontend_merchant_active = TRUE \n", "        AND fm.is_backend_merchant_active = TRUE \n", "        AND fm.is_pos_outlet_active = 1\n", "    INNER JOIN\n", "        dwh.dim_item_product_offer_mapping ip\n", "        ON ip.product_id = o.product_id\n", "        AND ip.is_current\n", "    INNER JOIN\n", "        rpc.item_category_details rp\n", "        ON rp.item_id = ip.item_id\n", "        AND rp.l0_id = 1487 \n", "    WHERE\n", "        o.order_current_status = 'DELIVERED'\n", "        AND o.is_internal_order = FALSE\n", "        AND o.order_create_dt_ist BETWEEN DATE('{min_date}') AND DATE('{max_date}')\n", "        AND fm.pos_outlet_id IN {outlet_id_list}\n", "        AND ip.item_id IN {item_id_list}\n", "    GROUP BY 1,2,3\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "27180750-5038-4ffb-ac9c-5fd1e9c1483c", "metadata": {}, "outputs": [], "source": ["sales.rename(columns={\"date_\": \"checkout_date\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "d78df03a-0c44-4230-aea8-f99a78845ef7", "metadata": {}, "outputs": [], "source": ["sales[\"checkout_date\"] = pd.to_datetime(sales[\"checkout_date\"])"]}, {"cell_type": "code", "execution_count": null, "id": "99c4e3ed-4e93-4f30-9716-2f4d7165cee2", "metadata": {}, "outputs": [], "source": ["base = base.merge(sales, how=\"left\", on=[\"outlet_id\", \"item_id\", \"checkout_date\"])"]}, {"cell_type": "code", "execution_count": null, "id": "46e214bb-d89e-4d0d-b2d2-4b530fb136ec", "metadata": {}, "outputs": [], "source": ["base[[\"outlet_id\", \"item_id\"]].drop_duplicates().shape[0]"]}, {"cell_type": "code", "execution_count": null, "id": "c7c342e4-b761-4629-997b-36f650d634e6", "metadata": {}, "outputs": [], "source": ["base.outlet_id.nunique()"]}, {"cell_type": "markdown", "id": "bf1f742d-1960-4818-a1e3-a6b1ff2d8a9b", "metadata": {}, "source": ["Setting today and tomorrow as 0 sales"]}, {"cell_type": "code", "execution_count": null, "id": "ead3adfa-5092-464a-a4c7-2c7273f51073", "metadata": {}, "outputs": [], "source": ["base.loc[base.checkout_date >= pd.to_datetime(fdate) - timedelta(days=2), \"item_sales\"] = 0\n", "base.loc[base.checkout_date >= pd.to_datetime(fdate) - timedelta(days=2), \"qty_sold\"] = 0"]}, {"cell_type": "code", "execution_count": null, "id": "0a4c9206-53c7-4642-8df1-6283a20bf5e8", "metadata": {}, "outputs": [], "source": ["base[\"qty_sold\"].isna().sum() / base.shape[0]"]}, {"cell_type": "markdown", "id": "0e955a2c-da31-4fb8-9bd5-deee400adfc6", "metadata": {}, "source": ["Drop rows if ffilled and sales is null"]}, {"cell_type": "code", "execution_count": null, "id": "eca79d14-1c6b-4441-ad81-9d626fbce03f", "metadata": {}, "outputs": [], "source": ["base.shape"]}, {"cell_type": "code", "execution_count": null, "id": "28caee72-c277-40df-82ed-9c78389fe1f2", "metadata": {}, "outputs": [], "source": ["base = base[\n", "    ~((base.qty_sold.isna()) & (base.item_sales_ffill == True))\n", "    & (base.item_change_imputed_flag == False)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "543d1033-c5da-42ee-9ec0-8a0d886cda9b", "metadata": {}, "outputs": [], "source": ["base[\"qty_sold\"].isna().sum() / base.shape[0]"]}, {"cell_type": "code", "execution_count": null, "id": "396a8ff9-e80b-4225-87c8-ada454b28b58", "metadata": {}, "outputs": [], "source": ["fw_data = get_results_from_query(\n", "    f\"\"\"select outlet_id,item_id,date_,qty_sold as sales_fw from supply_etls.fresh_warehouse_store_details where date_ >= date('{base.checkout_date.min().strftime('%Y-%m-%d')}')\n", "and outlet_id in {outlet_id_list} and item_id in {item_id_list}\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1c74bf23-304e-4f90-9f62-24eaadc4879a", "metadata": {}, "outputs": [], "source": ["fw_data[\"date_\"] = pd.to_datetime(fw_data[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "baeb6bc4-12ca-47c6-b32d-eac805741037", "metadata": {}, "outputs": [], "source": ["base = base.merge(\n", "    fw_data.rename(columns={\"date_\": \"checkout_date\"}),\n", "    how=\"left\",\n", "    on=[\"outlet_id\", \"item_id\", \"checkout_date\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e5b471be-3412-4af4-91d7-4fd31ffc2401", "metadata": {}, "outputs": [], "source": ["base.loc[(base.qty_sold.isna()) & (base.sales_fw == 0), \"qty_sold\"] = 0"]}, {"cell_type": "code", "execution_count": null, "id": "ffa8e79d-3a26-485c-8b99-7bbf42bd0f20", "metadata": {}, "outputs": [], "source": ["base.drop(\"sales_fw\", axis=1, inplace=True)"]}, {"cell_type": "markdown", "id": "5759e4bf-dda9-4d69-bd3d-cb3f47a8792a", "metadata": {}, "source": ["Dropping Nulls"]}, {"cell_type": "code", "execution_count": null, "id": "00a1323c-aa64-45e6-81bb-8452e265d1b6", "metadata": {}, "outputs": [], "source": ["print(base[\"qty_sold\"].isna().sum() / base.shape[0])"]}, {"cell_type": "code", "execution_count": null, "id": "c7548187-c5e1-42f9-b0d6-94d9c890c25d", "metadata": {}, "outputs": [], "source": ["base.dropna(subset=[\"qty_sold\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "1b7e7a3a-71ea-4edf-b41e-85b7d6e738dc", "metadata": {}, "outputs": [], "source": ["base.checkout_date.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "419a7247-84ed-4410-8cc7-5461cf46a92f", "metadata": {}, "outputs": [], "source": ["checkpoint(\"base with sales nulls fixed\")"]}, {"cell_type": "markdown", "id": "b98a701f-07d8-4b1b-80f4-501cce650ba4", "metadata": {}, "source": ["### Forward filling sales & using dow factor for outlier/missing dates and other features as well"]}, {"cell_type": "code", "execution_count": null, "id": "e7b3090c-fcfc-4999-a392-8da392277bff", "metadata": {}, "outputs": [], "source": ["base.sort_values([\"ts_id\", \"checkout_date\"], inplace=True)"]}, {"cell_type": "markdown", "id": "0503e5c0-f6be-4d25-a3b8-3bb4a1be9de8", "metadata": {}, "source": ["Creating PAN India DoW Factor Matrix"]}, {"cell_type": "code", "execution_count": null, "id": "dbece55a-1ace-43a0-b8dc-629a94a3ecb7", "metadata": {}, "outputs": [], "source": ["# --- Step 1: Get recent DOW median sales ---\n", "\n", "all_sales = get_results_from_query(\n", "    \"\"\"\n", "    SELECT date_, SUM(qty_sold) AS fnv_sales\n", "    FROM supply_etls.fresh_warehouse_store_details\n", "    WHERE date_ >= current_date - interval '85' day\n", "      AND date_ < current_date - interval '1' day\n", "    GROUP BY 1\n", "    ORDER BY 1 DESC\n", "\"\"\"\n", ")\n", "\n", "all_sales[\"date_\"] = pd.to_datetime(all_sales[\"date_\"])\n", "all_sales[\"dow\"] = all_sales[\"date_\"].dt.dayofweek  # Monday=0, Sunday=6\n", "\n", "# Median sales per day of week\n", "median_sale = all_sales.groupby(\"dow\")[\"fnv_sales\"].median()\n", "\n", "# DOW adjustment matrix: to adjust from one dow to another\n", "dow_matrix = np.outer(median_sale, 1 / median_sale)\n", "dow_matrix = pd.DataFrame(dow_matrix, index=range(7), columns=range(7))\n", "dow_matrix = dow_matrix.clip(lower=0.7, upper=1.3)  # avoid extreme adjustments\n", "print(dow_matrix)"]}, {"cell_type": "code", "execution_count": null, "id": "f53e5542-3932-484a-a7df-48f62d99b5da", "metadata": {}, "outputs": [], "source": ["anomaly_dates = set(pd.to_datetime(anomaly_dates))"]}, {"cell_type": "code", "execution_count": null, "id": "769bf6c3-3f78-4ea5-ad36-e74310a82881", "metadata": {}, "outputs": [], "source": ["# Step 1: Expand full date range\n", "min_max = base.groupby(\"ts_id\")[\"checkout_date\"].agg([\"min\", \"max\"])\n", "\n", "full_dates = (\n", "    min_max.apply(lambda row: pd.date_range(row[\"min\"], row[\"max\"]), axis=1)\n", "    .explode()\n", "    .reset_index()\n", "    .rename(columns={0: \"checkout_date\"})\n", ")\n", "full_dates[\"target_dow\"] = full_dates[\"checkout_date\"].dt.dayofweek"]}, {"cell_type": "code", "execution_count": null, "id": "9213b492-c537-498e-938f-714e8f185b29", "metadata": {}, "outputs": [], "source": ["# Step 2: Merge with base and tag original presence\n", "base[\"was_original\"] = True  # flag original data\n", "base_full = pd.merge(full_dates, base, on=[\"ts_id\", \"checkout_date\"], how=\"left\")\n", "base_full.sort_values([\"ts_id\", \"checkout_date\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "3ab182cd-ed6e-4a58-b1c9-39190b9303e9", "metadata": {}, "outputs": [], "source": ["# Step 3: Ffill qty and dow\n", "base_full[\"ffill_qty\"] = base_full.groupby(\"ts_id\")[\"qty_sold\"].ffill()\n", "base_full[\"ffill_dow\"] = base_full.groupby(\"ts_id\")[\"dow\"].ffill()"]}, {"cell_type": "code", "execution_count": null, "id": "cdeeb723-4990-46e5-92d3-b93da15d32f3", "metadata": {}, "outputs": [], "source": ["# Step 4: Add flags\n", "base_full[\"is_anomaly\"] = base_full[\"checkout_date\"].isin(anomaly_dates)\n", "base_full[\"was_ffilled\"] = base_full[\"was_original\"].isna() & base_full[\"ffill_qty\"].notna()"]}, {"cell_type": "code", "execution_count": null, "id": "94e16cd5-4614-44c8-a398-2190797f5356", "metadata": {}, "outputs": [], "source": ["# Step 5: Apply DOW adjustment only where ffilled and anomaly\n", "needs_adjustment = base_full[\"was_ffilled\"] & base_full[\"is_anomaly\"]\n", "\n", "base_full.loc[needs_adjustment, \"dow_adj\"] = base_full.loc[needs_adjustment].apply(\n", "    lambda row: dow_matrix.loc[row[\"target_dow\"], row[\"ffill_dow\"]], axis=1\n", ")\n", "base_full.loc[needs_adjustment, \"qty_sold\"] = (\n", "    base_full.loc[needs_adjustment][\"ffill_qty\"] * base_full.loc[needs_adjustment][\"dow_adj\"]\n", ")\n", "\n", "# Step 6: Final cleanup\n", "base_full[\"qty_sold\"] = base_full[\"qty_sold\"].apply(\n", "    lambda x: 1.0 if 0 < x < 1 else float(round(x)) if pd.notna(x) else x\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "eaf79ce5-2438-476f-b50b-e398360e0e06", "metadata": {}, "outputs": [], "source": ["# base_full[base_full.ts_id=='5132_10125442'].tail(30)"]}, {"cell_type": "markdown", "id": "cc40c263-7a0a-4b53-8e2d-ef7583ff1f97", "metadata": {}, "source": ["Setting today and tomorrow as 0 sales"]}, {"cell_type": "code", "execution_count": null, "id": "d48df9f9-898f-4956-ac70-2a9b619c377b", "metadata": {}, "outputs": [], "source": ["base_full.loc[\n", "    base_full.checkout_date >= pd.to_datetime(fdate) - timed<PERSON><PERSON>(days=2), \"item_sales\"\n", "] = 0\n", "base_full.loc[base_full.checkout_date >= pd.to_datetime(fdate) - timedelta(days=2), \"qty_sold\"] = 0"]}, {"cell_type": "code", "execution_count": null, "id": "6e29c54c-e071-4109-a5b5-db03738577df", "metadata": {}, "outputs": [], "source": ["FEATURES = [\n", "    \"outlet_id\",\n", "    \"item_id\",\n", "    \"l2_id\",\n", "    \"ts_id\",\n", "    \"product_type\",\n", "    \"temp_min_growth_rolling_temp_max_7d\",\n", "    \"temp_max_growth_rolling_temp_min_7d\",\n", "    \"humidity_growth_rolling_humidity_mean_7d\",\n", "    \"precipitation_growth_rolling_precipitation_mean_7d\",\n", "    \"slot_0\",\n", "    \"slot_1\",\n", "    \"slot_2\",\n", "    \"slot_3\",\n", "    \"slot_4\",\n", "    \"slot_5\",\n", "    \"day_index_feature\",\n", "    \"dow\",\n", "    \"dom\",\n", "    \"month\",\n", "    \"wom\",\n", "]\n", "TARGET = \"qty_sold\"\n", "CATEGORICAL = [\n", "    \"outlet_id\",\n", "    \"item_id\",\n", "    \"l2_id\",\n", "    \"product_type\",\n", "    \"dow\",\n", "    \"dom\",\n", "    \"month\",\n", "    \"wom\",\n", "    \"ts_id\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "bf0d041e-477e-44ab-a4bb-8d3b0765f877", "metadata": {}, "outputs": [], "source": ["len(FEATURES)"]}, {"cell_type": "code", "execution_count": null, "id": "deedbb38-06e4-4ae8-908e-ffa1f31eeb8c", "metadata": {}, "outputs": [], "source": ["# base_full[base_full.ts_id=='5132_10125442'].tail(30)"]}, {"cell_type": "code", "execution_count": null, "id": "c85a0bbd-0278-4a2b-baba-16e79bb416c4", "metadata": {}, "outputs": [], "source": ["# Add all desired columns to keep\n", "keep_cols = FEATURES + [\n", "    TARGET,\n", "    \"checkout_date\",\n", "    \"ffill_qty\",\n", "    \"ffill_dow\",\n", "    \"dow_adj\",\n", "    \"target_dow\",\n", "    \"was_original\",\n", "    \"is_anomaly\",\n", "    \"was_ffilled\",\n", "]\n", "\n", "# Subset base_full\n", "base_full = base_full[keep_cols].copy()"]}, {"cell_type": "code", "execution_count": null, "id": "eee15651-c37f-45ef-8e92-5b6d4f12ee9a", "metadata": {}, "outputs": [], "source": ["known = (\n", "    base_full[base_full.day_index_feature.notna()][[\"checkout_date\", \"day_index_feature\"]]\n", "    .drop_duplicates()\n", "    .sort_values(\"checkout_date\")\n", ")\n", "\n", "start_date = known[\"checkout_date\"].iloc[0]\n", "start_index = int(known[\"day_index_feature\"].iloc[0])\n", "end_date = known[\"checkout_date\"].iloc[-1]\n", "\n", "day_index_mapping = pd.DataFrame(\n", "    {\n", "        \"checkout_date\": pd.date_range(start=start_date, end=end_date),\n", "    }\n", ")\n", "day_index_mapping[\"day_index_feature\"] = range(start_index, start_index + len(day_index_mapping))"]}, {"cell_type": "markdown", "id": "5a1c6a1a-d8a8-4175-8706-2795147ac7ca", "metadata": {}, "source": ["Day Index Feature"]}, {"cell_type": "code", "execution_count": null, "id": "8c606b32-c2ef-4c67-8118-400878dcdecb", "metadata": {}, "outputs": [], "source": ["base_full = base_full.drop(\"day_index_feature\", axis=1).merge(\n", "    day_index_mapping, how=\"left\", on=\"checkout_date\"\n", ")"]}, {"cell_type": "markdown", "id": "18cde296-0722-412b-9c1b-67507b46615c", "metadata": {}, "source": ["TS_ID Property Features"]}, {"cell_type": "code", "execution_count": null, "id": "d3e42ff3-c481-43b0-97cf-c04746693651", "metadata": {}, "outputs": [], "source": ["# Step 1: Build static map keeping last occurrence per ts_id\n", "static_map = base_full[[\"ts_id\", \"outlet_id\", \"item_id\", \"l2_id\", \"product_type\"]].drop_duplicates(\n", "    subset=[\"ts_id\"], keep=\"last\"\n", ")\n", "\n", "# Step 2: Drop old static columns from base_full\n", "base_full.drop(columns=[\"outlet_id\", \"item_id\", \"l2_id\", \"product_type\"], inplace=True)\n", "\n", "# Step 3: Merge clean static map back\n", "base_full = base_full.merge(static_map, on=\"ts_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "0fe6eee3-2e76-458c-8160-2d5de8db8284", "metadata": {}, "outputs": [], "source": ["base_full.sort_values([\"ts_id\", \"checkout_date\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "3fe9ea92-db01-43a6-be2c-82266baed66a", "metadata": {}, "outputs": [], "source": ["# 2. Weather + slot features: just ffill\n", "weather_cols = [\n", "    \"temp_min_growth_rolling_temp_max_7d\",\n", "    \"temp_max_growth_rolling_temp_min_7d\",\n", "    \"humidity_growth_rolling_humidity_mean_7d\",\n", "    \"precipitation_growth_rolling_precipitation_mean_7d\",\n", "]\n", "slot_cols = [f\"slot_{i}\" for i in range(6)]\n", "base_full[weather_cols + slot_cols] = base_full[weather_cols + slot_cols].ffill()"]}, {"cell_type": "code", "execution_count": null, "id": "47c21bbe-a519-420c-be7d-3b9a867bf259", "metadata": {}, "outputs": [], "source": ["base_full[\"dow\"] = base_full[\"checkout_date\"].dt.dayofweek  # 0 = Monday\n", "base_full[\"dom\"] = base_full[\"checkout_date\"].dt.day  # Day of month\n", "base_full[\"month\"] = base_full[\"checkout_date\"].dt.month  # Month\n", "base_full[\"wom\"] = (base_full[\"checkout_date\"].dt.day - 1) // 7 + 1  # Week of month (1–5)"]}, {"cell_type": "code", "execution_count": null, "id": "729538c1-4ed9-483d-a317-448a32b49a3e", "metadata": {}, "outputs": [], "source": ["# Keep only original rows + ffilled-anomaly rows\n", "base_full = base_full.loc[~(base_full[\"was_ffilled\"] & ~base_full[\"is_anomaly\"])].reset_index(\n", "    drop=True\n", ")\n", "# Drop helper columns\n", "base_full.drop(\n", "    columns=[\n", "        \"ffill_qty\",\n", "        \"ffill_dow\",\n", "        \"dow_adj\",\n", "        \"target_dow\",\n", "        \"was_original\",\n", "        \"was_ffilled\",\n", "        \"is_anomaly\",\n", "    ],\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "43be0dc6-c55a-42e0-b11f-86422bf86af4", "metadata": {}, "outputs": [], "source": ["# base_full[base_full.ts_id=='5132_10125442'].tail(30)"]}, {"cell_type": "code", "execution_count": null, "id": "db2a2996-5ef1-428c-bdf3-595a836eebe9", "metadata": {}, "outputs": [], "source": ["print(base_full.isna().sum())"]}, {"cell_type": "code", "execution_count": null, "id": "594e6855-857c-4899-8768-57ad95d02c4d", "metadata": {}, "outputs": [], "source": ["checkpoint(\"base with ffill\")"]}, {"cell_type": "markdown", "id": "042ec239-795b-490b-9fea-43e45c5b45d5", "metadata": {}, "source": ["### Flooring and Capping"]}, {"cell_type": "code", "execution_count": null, "id": "40bd2eca-3f84-4bb1-b07b-53d44e790b8e", "metadata": {}, "outputs": [], "source": ["# Define date thresholds\n", "max_date = base_full[\"checkout_date\"].max()\n", "start_date = max_date - pd.<PERSON><PERSON><PERSON>(days=33)\n", "end_date = max_date - pd.Timedel<PERSON>(days=3)\n", "\n", "# Filter recent 30d (excluding last 3 days)\n", "recent_30d = base_full[\n", "    (base_full[\"checkout_date\"] > start_date) & (base_full[\"checkout_date\"] <= end_date)\n", "]\n", "\n", "# Compute 5th and 95th percentiles per ts_id\n", "q_limits = recent_30d.groupby(\"ts_id\")[\"qty_sold\"].quantile([0.05, 0.95]).unstack()\n", "q_limits.columns = [\"q05\", \"q95\"]\n", "q_limits = q_limits.reset_index()\n", "\n", "# Merge back to base\n", "base_full = base_full.merge(q_limits, on=\"ts_id\", how=\"left\")\n", "\n", "# Apply clipping only if qty_sold > 0\n", "base_full[\"qty_sold_new\"] = base_full[\"qty_sold\"]\n", "mask = base_full[\"qty_sold\"] > 0\n", "base_full.loc[mask, \"qty_sold_new\"] = base_full.loc[mask, \"qty_sold\"].clip(\n", "    lower=base_full.loc[mask, \"q05\"], upper=base_full.loc[mask, \"q95\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "455e484e-7f79-4471-97e6-ed7c711b61ad", "metadata": {}, "outputs": [], "source": ["# Drop extra columns if not needed\n", "base_full.drop(columns=[\"q05\", \"q95\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "a8fc46eb-5822-4600-b58c-8fbc70571ec9", "metadata": {}, "outputs": [], "source": ["base_full.qty_sold.sum()"]}, {"cell_type": "code", "execution_count": null, "id": "6f3a7c5b-8dec-4673-a09f-3c8ce95095c2", "metadata": {}, "outputs": [], "source": ["base_full[\"qty_sold_new\"] = base_full[\"qty_sold_new\"].apply(\n", "    lambda x: 1.0 if (x < 1 and x != 0) else float(round(x))\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "be14bb82-311f-4faf-a1ec-6b8fa61c3c26", "metadata": {}, "outputs": [], "source": ["base_full.qty_sold.describe()"]}, {"cell_type": "code", "execution_count": null, "id": "c7b138da-dd8e-4cb3-9ca0-320169977ebb", "metadata": {}, "outputs": [], "source": ["base_full.qty_sold_new.sum() / base_full.qty_sold.sum()"]}, {"cell_type": "code", "execution_count": null, "id": "756fa4c8-006f-4562-be49-ee9062b529ea", "metadata": {}, "outputs": [], "source": ["base_full.qty_sold_new.describe()"]}, {"cell_type": "code", "execution_count": null, "id": "64553251-cd5a-4e08-85fc-da539bcb4feb", "metadata": {}, "outputs": [], "source": ["affected = (base_full[\"qty_sold\"] != base_full[\"qty_sold_new\"]).sum()\n", "print(f\"fraction of capped rows: {affected/base_full.shape[0]}\")"]}, {"cell_type": "code", "execution_count": null, "id": "b013949c-1193-4a09-8654-ed905ddfb640", "metadata": {}, "outputs": [], "source": ["# import matplotlib.pyplot as plt\n", "\n", "# plt.figure(figsize=(10, 4))\n", "# plt.boxplot([base_full['qty_sold'], base_full['qty_sold_new']], labels=['Original', 'Capped'])\n", "# plt.title(\"Boxplot: Original vs Capped Sales\")\n", "# plt.grid(True)\n", "# plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "3340a734-9b64-4a98-8825-53a30edb32aa", "metadata": {}, "outputs": [], "source": ["base_full[\"qty_sold_original\"] = base_full[\"qty_sold\"]\n", "base_full[\"qty_sold\"] = base_full[\"qty_sold_new\"]"]}, {"cell_type": "markdown", "id": "dc66ea67-3983-4342-b2eb-586cccac1094", "metadata": {}, "source": ["More features of mean, std, velocity"]}, {"cell_type": "code", "execution_count": null, "id": "e9a7f37a-0454-4236-9881-0ec07fe2d4dd", "metadata": {}, "outputs": [], "source": ["base_full.sort_values([\"outlet_id\", \"item_id\", \"checkout_date\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "c8644091-ac03-4343-b607-e0588ce8db43", "metadata": {}, "outputs": [], "source": ["# Ensure checkout_date is in datetime format\n", "base_full[\"checkout_date\"] = pd.to_datetime(base_full[\"checkout_date\"])\n", "\n", "# Extract relevant columns\n", "sales_features = base_full[[\"outlet_id\", \"item_id\", \"checkout_date\", \"qty_sold\"]].copy()\n", "\n", "# Sort to ensure rolling works correctly\n", "sales_features = sales_features.sort_values([\"outlet_id\", \"item_id\", \"checkout_date\"])"]}, {"cell_type": "code", "execution_count": null, "id": "510ae496-622a-485d-a6de-325e0eae7639", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c52e064b-b264-4abd-a660-e63221f1910b", "metadata": {}, "outputs": [], "source": ["# sales_features[(sales_features.outlet_id==5132)&(sales_features.item_id==10125442)]\n", "# base_full[base_full.ts_id=='5132_10125442'].tail(30))].tail(30)"]}, {"cell_type": "code", "execution_count": null, "id": "0944c230-91fc-43a6-a873-8dafd64cbbea", "metadata": {}, "outputs": [], "source": ["# Shift sales data by 2 days\n", "sales_features[\"sales_shifted\"] = sales_features.groupby([\"outlet_id\", \"item_id\"])[\n", "    \"qty_sold\"\n", "].shift(3)"]}, {"cell_type": "code", "execution_count": null, "id": "f9e3dd85-3a81-4867-a50d-8555111cf0c8", "metadata": {}, "outputs": [], "source": ["# Initialize window sizes\n", "window_sizes = [7, 14, 30]\n", "\n", "# Loop through window sizes and calculate rolling features\n", "for days in window_sizes:\n", "    # Calculate rolling mean and standard deviation\n", "    sales_features[f\"sales_mean_{days}d\"] = (\n", "        sales_features.groupby([\"outlet_id\", \"item_id\"])[\"sales_shifted\"]\n", "        .rolling(window=days, min_periods=1)\n", "        .mean()\n", "        .reset_index(level=[0, 1], drop=True)  # Reset grouped index properly\n", "    )\n", "    sales_features[f\"sales_std_{days}d\"] = (\n", "        sales_features.groupby([\"outlet_id\", \"item_id\"])[\"sales_shifted\"]\n", "        .rolling(window=days, min_periods=1)\n", "        .std()\n", "        .reset_index(level=[0, 1], drop=True)\n", "    )\n", "\n", "# Compute velocity features\n", "sales_features[\"velocity_7_14\"] = sales_features[\"sales_mean_7d\"] / sales_features[\"sales_mean_14d\"]\n", "sales_features[\"velocity_7_30\"] = sales_features[\"sales_mean_7d\"] / sales_features[\"sales_mean_30d\"]\n", "\n", "# Replace infinities and NaNs\n", "sales_features.replace([np.inf, -np.inf], np.nan, inplace=True)\n", "sales_features.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "1d9a6495-6c95-4034-a594-c297b491b482", "metadata": {}, "outputs": [], "source": ["# Drop to avoid duplication\n", "sales_features.drop(columns=[\"qty_sold\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "1d1e8531-5106-4159-8698-0c4d445b9013", "metadata": {}, "outputs": [], "source": ["# Merge back with base_full\n", "base_full = base_full.merge(\n", "    sales_features, on=[\"outlet_id\", \"item_id\", \"checkout_date\"], how=\"left\"\n", ")\n", "base_full.head()"]}, {"cell_type": "code", "execution_count": null, "id": "78bec098-ebe4-4f01-83dc-aee0ef85c389", "metadata": {}, "outputs": [], "source": ["# Add new rolling mean, std, and velocity features dynamically\n", "for days in [7, 14, 30]:\n", "    FEATURES.append(f\"sales_mean_{days}d\")\n", "    FEATURES.append(f\"sales_std_{days}d\")\n", "\n", "# FEATURES.extend(['velocity_3_7', 'velocity_3_14'])\n", "FEATURES.extend([\"velocity_7_14\", \"velocity_7_30\"])\n", "\n", "print(\"Final Features List:\", FEATURES)"]}, {"cell_type": "code", "execution_count": null, "id": "c5ae9f59-b582-4bc6-b525-d5e4e4e0c54a", "metadata": {}, "outputs": [], "source": ["print(base_full.columns)"]}, {"cell_type": "code", "execution_count": null, "id": "99c04ac8-d9c5-4ff2-8ec9-923fb9e24c41", "metadata": {}, "outputs": [], "source": ["print(\"Sales before ffill: \", base.qty_sold.sum())\n", "print(\"Sales after ffill: \", base_full.qty_sold.sum())\n", "print(\"Change: \", np.round(base_full.qty_sold.sum() / base.qty_sold.sum(), 2), \"x\")"]}, {"cell_type": "code", "execution_count": null, "id": "7c3e3bad-aca7-43f3-bd79-b90706d9ef98", "metadata": {}, "outputs": [], "source": ["checkpoint(\"base with additional features\")"]}, {"cell_type": "code", "execution_count": null, "id": "38d1ce8f-7171-463f-87c9-92d54775e192", "metadata": {}, "outputs": [], "source": ["write_df_as_parquet_to_s3(\n", "    base_full,\n", "    f\"s3://prod-dse-projects/demand/frontend_demand_forecasting/dts_forecast/{previous_day}/base_exp\",\n", "    partition_cols=None,\n", "    suffix=None,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b452e813-b657-401d-b927-d1e8a215a660", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"alerts_aman\",\n", "    text=f\"<@U071XRARZAS> - The base data for DTS forecasts has been pushed to s3 for {fdate}\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "03e7a275-61d0-4241-a3ab-2743fe57965f", "metadata": {}, "outputs": [], "source": ["checkpoint(\"end\")"]}, {"cell_type": "code", "execution_count": null, "id": "92ebb771-31e0-4930-8d6e-fe3d2a1546ec", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}