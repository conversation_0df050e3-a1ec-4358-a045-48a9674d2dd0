alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: fnv_backend_item_details
dag_type: etl
escalation_priority: low
execution_timeout: 1080
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: very-high-cpu
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebook:
  parameters:
    run_id: '{{ run_id }}'
    task_instance_key_str: '{{ task_instance_key_str }}'
  retries: 2
  retry_delay_in_seconds: 300
owner:
  email: <EMAIL>
  slack_id: U071XRARZAS
path: data_science/demand_forecasting/etl/fnv_backend_item_details
paused: false
pool: data_science_pool
project_name: demand_forecasting
schedule:
  end_date: '2025-08-05T00:00:00'
  interval: 30 21 * * *
  start_date: '2025-05-10T00:00:00'
schedule_type: fixed
sla: 124 minutes
support_files: []
tags: []
template_name: notebook
version: 1
