{"cells": [{"cell_type": "code", "execution_count": null, "id": "042d1be4-cc36-4e1c-8b66-43a65d273d4f", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f5728400-787f-4da6-8f83-d90eb6ed2594", "metadata": {}, "outputs": [], "source": ["!pip install -q awswrangler==3.9.1"]}, {"cell_type": "code", "execution_count": null, "id": "dcf003a4-6f0e-432a-9a75-df42971e0b4b", "metadata": {}, "outputs": [], "source": ["!pip install -q openpyxl"]}, {"cell_type": "code", "execution_count": null, "id": "cf07e72c-3583-4e9c-bcd4-6caa3b45f7bd", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import awswrangler as wr\n", "from datetime import timedelta, datetime"]}, {"cell_type": "code", "execution_count": null, "id": "1e5bf5c5-4d3d-489d-acf6-e5a6ed925b1d", "metadata": {}, "outputs": [], "source": ["CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "\n", "def read_parquet_data_from_s3(s3_path: str, suffix_list: list[str] = None) -> pd.DataFrame:\n", "    \"\"\"\n", "    --> Reads the data stored as a parquet file at the input s3 path and returns it as a pandas dataframe\n", "    Args:\n", "        s3_path: the s3 key where the data is present\n", "        suffix_list: list of suffix if list of data needs to be read\n", "    Returns: loaded data as a pandas dataframe\n", "    \"\"\"\n", "    if suffix_list is None:\n", "        data = wr.s3.read_parquet(\n", "            s3_path,\n", "            # dataset=True\n", "        )\n", "    else:\n", "        suffix_list = list(suffix_list)\n", "        data_list = []\n", "        if s3_path[-1] != \"/\":\n", "            s3_path += \"/\"\n", "        for suffix in suffix_list:\n", "            data_list.append(wr.s3.read_parquet(s3_path + str(suffix) + \"/\", dataset=True))\n", "        data = pd.concat(data_list)\n", "\n", "    return data\n", "\n", "\n", "def write_df_as_parquet_to_s3(data, s3_path, partition_cols=None, suffix=None):\n", "    mode = \"overwrite_partitions\" if partition_cols else \"overwrite\"\n", "\n", "    if suffix is not None:\n", "        s3_path = f\"{s3_path.strip('/')}/{suffix}\"\n", "\n", "    wr.s3.to_parquet(data, s3_path, dataset=True, mode=mode, partition_cols=partition_cols)"]}, {"cell_type": "code", "execution_count": null, "id": "043782b0-1cc9-43d2-9f8e-53b7baea1648", "metadata": {}, "outputs": [], "source": ["def get_results_from_query(sql: str, con: str = \"[Warehouse] Trino\"):\n", "    # LOGGER.info(f\"Fetching data for the query \\n{sql[:50] + ' ...'}\")\n", "    data = pd.read_sql(sql, pb.get_connection(con))\n", "    return data"]}, {"cell_type": "code", "execution_count": null, "id": "46f022ff-8e85-4cd5-b0b3-27417b0c8a34", "metadata": {}, "outputs": [], "source": ["dag_id = task_instance_key_str.split(\"__\")[0]\n", "task_id = task_instance_key_str.split(\"__\")[1]\n", "dag_run_date_str = run_id.split(\"T\")[0].split(\"_\")[-1]\n", "dag_run_date = datetime.strptime(dag_run_date_str, \"%Y-%m-%d\")\n", "run_date_input = str((dag_run_date + timedelta(days=2)).date())\n", "print(dag_id, task_id, run_date_input)"]}, {"cell_type": "code", "execution_count": null, "id": "007d58be-3abf-4a08-89ae-aa60e4e2d576", "metadata": {}, "outputs": [], "source": ["dag_link = f\"https://dse-commuter.grofer.io/view/{dag_id}/{run_id}/{task_id}.ipynb\"\n", "print(dag_link)\n", "pb.send_slack_message(\n", "    channel=\"alerts-11\",\n", "    text=f\"fnv_backend_item_details table prep started for {run_date_input}, \\n dag_link:- {dag_link} \\n cc:<@U07KL3T5EH2>\",\n", ")"]}, {"cell_type": "markdown", "id": "efc3957a-9970-4c78-9746-ca7bf8be1332", "metadata": {}, "source": ["Weighted Availability Query"]}, {"cell_type": "code", "execution_count": null, "id": "d58b933e-a9ae-4b14-b45d-185ce0b3bb34", "metadata": {}, "outputs": [], "source": ["avl_query = f\"\"\"\n", "with \n", "assortment as \n", "(SELECT\n", "    item_id,\n", "    item_name,\n", "    l2,\n", "    l1,\n", "    product_type as ptype_,\n", "    outlet_id,\n", "    facility_id,\n", "    be_outlet_id,\n", "    be_outlet_name\n", "    from \n", "        supply_etls.fresh_warehouse_store_details a\n", "    where \n", "         date_ = date('{run_date_input}') - interval '2' day\n", "         and master_assortment_substate_id in (1,3)\n", "group by 1,2,3,4,5,6,7,8,9\n", "),\n", "base as (\n", "select \n", "        date(inv.snapshot_date_ist) as date_,\n", "        inv.outlet_id,\n", "        co.facility_id,\n", "        ic.item_id,\n", "        cast(snapshot_hr_mm/100 as int) as hour_,\n", "        sum(inv.current_inventory) current_inventory\n", "    from\n", "        dwh.agg_hourly_outlet_item_inventory inv\n", "    join\n", "        rpc.item_category_details ic\n", "        on inv.item_id = ic.item_id\n", "    join\n", "        retail.console_outlet co\n", "        on co.id = inv.outlet_id\n", "        and co.active = 1\n", "    where \n", "        snapshot_date_ist = date('{run_date_input}') - interval '2' day\n", "        and ic.item_id in (select distinct item_id from assortment)\n", "        and co.facility_id in (select distinct facility_id from assortment)\n", "        and snapshot_hr_mm%%100 = 0\n", "    group by 1,2,3,4,5),\n", "\n", "avail_pre_pre as (\n", "    SELECT date_, hour_, facility_id, outlet_id, item_id, \n", "    CASE \n", "        WHEN current_inventory > 0 THEN 1 \n", "        ELSE 0  \n", "    END is_available \n", "    FROM base a\n", "    WHERE item_id IN (select distinct item_id from assortment)\n", "),\n", "\n", "avail_pre as ( \n", "    SELECT date_, hour_, facility_id, outlet_id, item_id, MAX(is_available) AS is_available\n", "    from avail_pre_pre \n", "    GROUP BY 1,2,3,4,5\n", "),\n", "\n", "facility_item_avail AS (\n", "    SELECT a.* , cl.name AS city, be_outlet_id, be_outlet_name, b.l2, b.l1, b.ptype_\n", "    FROM avail_pre a\n", "    JOIN assortment b \n", "    ON a.item_id = b.item_id\n", "    and a.facility_id = b.facility_id\n", "    JOIN retail.console_outlet co ON co.id = a.outlet_id and co.active = 1 and co.business_type_id = 7 and co.lake_active_record\n", "    LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id\n", "    \n", "),\n", "\n", "city_item_weights AS (\n", "    SELECT city, a.item_id, CAST(weights AS DOUBLE) AS ciw\n", "    FROM supply_etls.city_item_weights a\n", "    WHERE a.updated_at = (select max(updated_at) from supply_etls.city_item_weights where updated_at >= date('{run_date_input}') -  interval '30' day and updated_at < date('{run_date_input}'))\n", "--    and updated_at >= date('{run_date_input}')\n", "--    and updated_at <= date('{run_date_input}')\n", "    AND item_id IN (select distinct item_id from assortment)\n", "),\n", "\n", "city_hour_weights AS (\n", "    SELECT DISTINCT city, order_hour AS hour_, CAST(weights AS DOUBLE) AS chw\n", "    FROM supply_etls.city_hour_weights a\n", "    WHERE a.updated_at IN (SELECT max(updated_at) FROM supply_etls.city_hour_weights where updated_at >= date('{run_date_input}') -  interval '30' day and updated_at < date('{run_date_input}'))\n", "--    and updated_at >= date('{run_date_input}')\n", "--    and updated_at <= date('{run_date_input}')\n", "    ),\n", "\n", "city_store_weights as (\n", "    select distinct city, facility_id, outlet_id, CAST(store_weight as double) as csw\n", "    from supply_etls.city_store_weights s\n", "    where s.updated_at IN (SELECT MAX(updated_at) FROM supply_etls.city_store_weights where updated_at >= date('{run_date_input}') -  interval '30' day and updated_at < date('{run_date_input}'))\n", "--    and updated_at >= date('{run_date_input}')\n", "--    and updated_at <= date('{run_date_input}')\n", "    ),\n", "\n", "facility_weights_merge as (\n", "    SELECT DISTINCT a.city, a.be_outlet_id, a.be_outlet_name, a.facility_id, date_, a.hour_, l1, l2, ptype_, a.item_id, is_available, iw.ciw, hw.chw, sw.csw\n", "    FROM facility_item_avail a \n", "    LEFT JOIN city_hour_weights hw ON a.city = hw.city AND a.hour_ = hw.hour_\n", "    LEFT JOIN city_item_weights iw ON a.city = iw.city AND a.item_id = iw.item_id\n", "    LEFT JOIN city_store_weights sw ON a.city = sw.city AND a.facility_id = sw.facility_id\n", "),\n", " \n", "pre_final as (\n", "    SELECT city, be_outlet_id, be_outlet_name, facility_id, date_ as checkout_date, hour_, l2 , ptype_ as product_type, item_id, is_available, (is_available*1.0000*ciw*chw*csw) AS fac_avail , (ciw*chw*csw) AS tot_weights\n", "    from facility_weights_merge\n", "),\n", "\n", "facility_availability as (\n", "    select checkout_date,\n", "    -- city,\n", "    be_outlet_name,\n", "    be_outlet_id,\n", "    l2,\n", "    -- product_type,\n", "    item_id,\n", "    sum(fac_avail)*1.000/sum(tot_weights) as weighted_availability \n", "    from pre_final\n", "    group by 1,2,3,4,5\n", "\n", ")\n", "\n", "select \n", "  checkout_date\n", ", be_outlet_name\n", ", be_outlet_id\n", ", l2 \n", ", item_id\n", ", weighted_availability\n", "from facility_availability a\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "190ac33f-803d-4807-bc1f-0ce5a81083bf", "metadata": {}, "outputs": [], "source": ["avl = get_results_from_query(avl_query)"]}, {"cell_type": "code", "execution_count": null, "id": "45a60971-b4c7-4c42-97f1-5096c627f14e", "metadata": {}, "outputs": [], "source": ["print(avl.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "845b6ecf-6c4d-41ff-af8d-23331b<PERSON>ce75", "metadata": {}, "outputs": [], "source": ["avl.sample(5)"]}, {"cell_type": "code", "execution_count": null, "id": "6e21ae4c-535a-4985-8bb8-01196381cdb0", "metadata": {}, "outputs": [], "source": ["rest_query = f\"\"\"\n", "select be_outlet_id, item_id, date_ as checkout_date, sum(qty_sold) as qty_sold, sum(dump) as dump from supply_etls.fresh_warehouse_store_details\n", "where date_ = date('{run_date_input}') - interval '2' day\n", "and master_assortment_substate_id in (1,3)\n", "group by 1,2,3\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "53ecc3a7-6683-4c4a-bbda-1d348eeb334a", "metadata": {}, "outputs": [], "source": ["rest = get_results_from_query(rest_query)"]}, {"cell_type": "code", "execution_count": null, "id": "6ee01a28-ef61-4ec2-aa2d-8df69b00d910", "metadata": {}, "outputs": [], "source": ["print(rest.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "38010da4-7a84-4a0c-afc2-36aea6991a0a", "metadata": {}, "outputs": [], "source": ["rest.sample(5)"]}, {"cell_type": "code", "execution_count": null, "id": "ce470123-872d-4dfa-b135-c7a512e90538", "metadata": {}, "outputs": [], "source": ["avl.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "d140917a-8b26-4234-b1f1-64914ea0260d", "metadata": {}, "outputs": [], "source": ["rest.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "827aa097-99d8-4bcb-a80a-63cf5b22fdc6", "metadata": {}, "outputs": [], "source": ["avl[\"checkout_date\"] = pd.to_datetime(avl[\"checkout_date\"])\n", "rest[\"checkout_date\"] = pd.to_datetime(rest[\"checkout_date\"])"]}, {"cell_type": "code", "execution_count": null, "id": "cbd16c47-9c18-4e16-8a68-2adcbee59142", "metadata": {}, "outputs": [], "source": ["final = rest.merge(avl, how=\"left\", on=[\"be_outlet_id\", \"item_id\", \"checkout_date\"])"]}, {"cell_type": "code", "execution_count": null, "id": "84cf9a5c-f8b2-4e86-ac05-ad4d10204055", "metadata": {}, "outputs": [], "source": ["final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ab318fd0-9f46-4487-a607-3a13ffff6609", "metadata": {}, "outputs": [], "source": ["final.loc[(final.weighted_availability.isna()) & (final.qty_sold > 0), \"weighted_availability\"] = 0"]}, {"cell_type": "code", "execution_count": null, "id": "5f98086b-87aa-40a6-bb18-c2ab7f9eb60e", "metadata": {}, "outputs": [], "source": ["final.loc[(final.weighted_availability.isna()) & (final.qty_sold == 0), \"weighted_availability\"] = 0"]}, {"cell_type": "code", "execution_count": null, "id": "114f1126-95af-4430-b601-6566bac79b89", "metadata": {}, "outputs": [], "source": ["final.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "d978f0ac-181c-4b0b-a38a-c6139642ef27", "metadata": {}, "outputs": [], "source": ["final = final[final.be_outlet_name.notna()]"]}, {"cell_type": "code", "execution_count": null, "id": "62739a54-1616-4ac7-9b06-36c09136e72f", "metadata": {}, "outputs": [], "source": ["send_to_trino = True"]}, {"cell_type": "code", "execution_count": null, "id": "cdcee5a2-2129-4450-86d6-2f916fcfa3da", "metadata": {}, "outputs": [], "source": ["print(final.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "f6efc3dc-8f78-46c5-8547-1da061e09220", "metadata": {}, "outputs": [], "source": ["import pytz\n", "\n", "final[\"updated_at_ist\"] = pd.to_datetime(\n", "    datetime.now(pytz.timezone(\"Asia/Calcutta\")).replace(tzinfo=None)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6d8c0094-b678-487f-897c-f82d3fc6bef7", "metadata": {}, "outputs": [], "source": ["final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "cb70ea67-e7f4-40cb-ac67-8976588de<PERSON>e", "metadata": {}, "outputs": [], "source": ["final[\"weighted_availability\"] = final[\"weighted_availability\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "b7e06c51-5ffd-42e3-b494-c0ee5af53c8c", "metadata": {}, "outputs": [], "source": ["if send_to_trino:\n", "    # Define roundkwargs for the Trino table update\n", "    kwargs = {\n", "        \"schema_name\": \"ds_etls\",\n", "        \"table_name\": \"fnv_backend_item_details_v1\",\n", "        \"column_dtypes\": [\n", "            {\"name\": \"be_outlet_id\", \"type\": \"INTEGER\", \"description\": \"be_outlet_id\"},\n", "            {\"name\": \"be_outlet_name\", \"type\": \"VARCHAR\", \"description\": \"be_outlet_name\"},\n", "            {\"name\": \"l2\", \"type\": \"VARCHAR\", \"description\": \"l2\"},\n", "            {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"item_id\"},\n", "            {\"name\": \"qty_sold\", \"type\": \"REAL\", \"description\": \"forecast\"},\n", "            {\"name\": \"dump\", \"type\": \"REAL\", \"description\": \"dump\"},\n", "            {\n", "                \"name\": \"weighted_availability\",\n", "                \"type\": \"REAL\",\n", "                \"description\": \"weighted_availability\",\n", "            },\n", "            {\n", "                \"name\": \"checkout_date\",\n", "                \"type\": \"TIMESTAMP(6)\",\n", "                \"description\": \"Date\",\n", "            },\n", "            {\n", "                \"name\": \"updated_at_ist\",\n", "                \"type\": \"TIMESTAMP(6)\",\n", "                \"description\": \"updated_at timestamp\",\n", "            },\n", "        ],\n", "        \"primary_key\": [\n", "            \"be_outlet_id\",\n", "            \"item_id\",\n", "            \"checkout_date\",\n", "        ],  # Update primary_key\n", "        \"partition_key\": [\"checkout_date\"],\n", "        \"incremental_key\": \"updated_at_ist\",\n", "        \"load_type\": \"upsert\",  # Ensure upsert operation\n", "        \"table_description\": \"BE x Item x Date level avl, dump and sales\",\n", "        \"force_upsert_without_increment_check\": True,\n", "    }\n", "\n", "    try:\n", "        # Perform the table update operation\n", "        pb.to_trino(data_obj=final, **kwargs)\n", "\n", "        # Send Slack message indicating success with the summary\n", "        pb.send_slack_message(\n", "            channel=\"alerts-11\",\n", "            text=f\"<@U07KL3T5EH2> - The table 'fnv_backend_item_details_v1' has been successfully updated with the data for {run_date_input}\",\n", "        )\n", "\n", "    except Exception as e:\n", "        # Handle any exceptions and print the error message\n", "        print(f\"An error occurred: {e}\")\n", "\n", "        # Optionally, send a Slack message indicating failure (comment this out if not needed)\n", "        pb.send_slack_message(\n", "            channel=\"alerts-11\",\n", "            text=\"<@U07KL3T5EH2> - An error occurred while updating the table 'fnv_backend_item_details_v1' Error: {}\".format(\n", "                e\n", "            ),\n", "        )"]}, {"cell_type": "code", "execution_count": null, "id": "06e4337f-06ae-4aee-94ff-2c8b3ed1f283", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}