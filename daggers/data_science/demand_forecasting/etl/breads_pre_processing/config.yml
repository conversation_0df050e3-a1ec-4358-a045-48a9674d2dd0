alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: breads_pre_processing
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: very-high-mem
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U07HMKDUS59
path: data_science/demand_forecasting/etl/breads_pre_processing
paused: true
pool: data_science_pool
project_name: demand_forecasting
schedule:
  end_date: '2025-07-01T00:00:00'
  interval: 30 6 * * *
  start_date: '2025-04-18T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
