{"cells": [{"cell_type": "code", "execution_count": null, "id": "5540b608-0c5b-4dd1-a1a5-2396f948f07d", "metadata": {}, "outputs": [], "source": ["!pip install awswrangler==3.9.1\n", "!pip install openpyxl\n", "!pip install sktime==0.19.1\n", "!pip install awscli==1.33.27\n", "!pip install catboost"]}, {"cell_type": "code", "execution_count": null, "id": "7a3bf521-75ca-468a-be6e-75056d4875db", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import awswrangler as wr\n", "from datetime import timedelta, datetime\n", "import matplotlib.pyplot as plt\n", "import time\n", "from catboost import CatBoostRegressor\n", "from sklearn.model_selection import TimeSeriesSplit\n", "\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.options.mode.chained_assignment = None  # default='warn'\n", "pd.set_option(\"display.max_columns\", 500)"]}, {"cell_type": "code", "execution_count": null, "id": "448c8ffe-fe8f-4e55-a426-d9abe888672a", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pytz\n", "import calendar\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")\n", "\n", "current_time = datetime.now(IST) - <PERSON><PERSON><PERSON>(days=0)\n", "\n", "today_date = (current_time - timedelta(days=0)).strftime(\"%Y-%m-%d\")\n", "t_minus_1 = (current_time - timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "t_minus_7 = (current_time - timedelta(days=7)).strftime(\"%Y-%m-%d\")\n", "t_minus_30 = (current_time - timedelta(days=30)).strftime(\"%Y-%m-%d\")\n", "t_minus_60 = (current_time - timed<PERSON>ta(days=60)).strftime(\"%Y-%m-%d\")\n", "t_minus_90 = (current_time - timed<PERSON>ta(days=90)).strftime(\"%Y-%m-%d\")\n", "\n", "\n", "### forecasting dates\n", "# f1 = (current_time + timedelta(days=0)).strftime(\"%Y-%m-%d\")\n", "f7 = (current_time + timedelta(days=7)).strftime(\"%Y-%m-%d\")\n", "f6 = (current_time + timedelta(days=6)).strftime(\"%Y-%m-%d\")\n", "f5 = (current_time + timedelta(days=5)).strftime(\"%Y-%m-%d\")\n", "f4 = (current_time + timedelta(days=4)).strftime(\"%Y-%m-%d\")\n", "f3 = (current_time + timedelta(days=3)).strftime(\"%Y-%m-%d\")\n", "f2 = (current_time + timedelta(days=2)).strftime(\"%Y-%m-%d\")\n", "\n", "dates = (today_date, t_minus_1, t_minus_7, t_minus_30, t_minus_60, f2, f3, f4, f5, f6, f7)\n", "dates"]}, {"cell_type": "code", "execution_count": null, "id": "fa6c1540-339f-4fc1-9221-4109b0d59ce7", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(\n", "            f\"Read attempt: {attempt}... \\nRun started at {datetime.now() + timed<PERSON>ta(hours = 5.5)}\"\n", "        )\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "002d8f03-fb70-4048-9c3a-cda68d343445", "metadata": {}, "outputs": [], "source": ["def try_upload_to_sheets(df, sheet_id, sheet_name, retries=3):\n", "    for attempt in range(retries):\n", "        try:\n", "            pb.to_sheets(df, sheet_id, sheet_name)\n", "            print(f\"Successfully uploaded {sheet_name} on attempt {attempt + 1}\")\n", "            return  # Exit the function once successful\n", "        except Exception as e:\n", "            print(f\"Attempt {attempt + 1} failed for {sheet_name}: {e}\")\n", "            if attempt == retries - 1:\n", "                print(f\"All attempts failed for {sheet_name}\")"]}, {"cell_type": "code", "execution_count": null, "id": "db7fd6a4-904a-4764-b5ae-ca21896306c6", "metadata": {}, "outputs": [], "source": ["def send_alert(df=None, duration_minutes=None, success=True, text=None):\n", "    import pencilbox as pb1\n", "\n", "    slack_channel = \"my_dump\"\n", "    channel = slack_channel\n", "\n", "    if text is None:\n", "        if success:\n", "            text = f\"Pre-processing breads completed, Duration: {duration_minutes} minutes.\"\n", "        else:\n", "            text = \"❌ Got an error during pre-processing.\"\n", "\n", "    pb1.send_slack_message(channel=channel, text=text)"]}, {"cell_type": "code", "execution_count": null, "id": "1628adf4-6fd2-4f8b-bb2a-0a6391bee697", "metadata": {}, "outputs": [], "source": ["from pytz import timezone\n", "\n", "ist = timezone(\"Asia/Kolkata\")\n", "start_time = datetime.now(ist)\n", "start_time"]}, {"cell_type": "markdown", "id": "64883d76-a62f-4f35-8879-56ac8586d248", "metadata": {}, "source": ["### City L2 Disruptions"]}, {"cell_type": "code", "execution_count": null, "id": "6d4ed7e9-a138-4474-a89b-593907307654", "metadata": {}, "outputs": [], "source": ["try:\n", "    city_l2_disruption_df = pb.from_sheets(\n", "        sheetid=\"1wFQBrKUB6LIt31VgDT6NoKBfva-DMLpPQtQxnmZRb3w\",\n", "        sheetname=\"config:l2-disruption-inputs\",\n", "    )\n", "except Exception as e:\n", "    print(f\"Error loading from Google Sheets: {e}\")\n", "    city_l2_disruption_df = pd.read_csv(\"city_l2_disruption.csv\")\n", "\n", "\n", "city_l2_disruption_df[\"start_date\"] = pd.to_datetime(city_l2_disruption_df[\"start_date\"])\n", "\n", "city_l2_disruption_df[\"end_date\"] = pd.to_datetime(city_l2_disruption_df[\"end_date\"])\n", "\n", "l2_mapping = pd.DataFrame(\n", "    {\n", "        \"Batter\",\n", "        \"Breads\",\n", "        \"Curd\",\n", "        \"Eggs\",\n", "        \"Paneer\",\n", "        \"Perishable Dairy\",\n", "        \"Yogurt\",\n", "        \"Other Perishable\",\n", "    },\n", "    columns=[\"category_bucket\"],\n", ")\n", "\n", "l2_mapping[\"category\"] = \"All\"\n", "\n", "city_l2_disruption_df = city_l2_disruption_df.merge(l2_mapping, on=[\"category\"], how=\"left\")\n", "\n", "city_l2_disruption_df[\"category_bucket\"] = np.where(\n", "    city_l2_disruption_df[\"category_bucket\"].isna(),\n", "    city_l2_disruption_df[\"category\"],\n", "    city_l2_disruption_df[\"category_bucket\"],\n", ")\n", "\n", "city_l2_disruption_df[\"date_\"] = list(\n", "    map(\n", "        lambda x, y: pd.date_range(start=x, end=y),\n", "        city_l2_disruption_df[\"start_date\"],\n", "        city_l2_disruption_df[\"end_date\"],\n", "    )\n", ")\n", "\n", "city_l2_disruption_df = city_l2_disruption_df.explode(\"date_\").drop(\n", "    [\"start_date\", \"end_date\"], axis=1\n", ")\n", "\n", "if city_l2_disruption_df.shape[0] > 0:\n", "    city_l2_disruption_df[\"date_\"] = pd.to_datetime(city_l2_disruption_df[\"date_\"])\n", "else:\n", "    city_l2_disruption_df[\"date_\"] = \"\"\n", "\n", "city_l2_disruption_df = city_l2_disruption_df.reset_index()\n", "\n", "city_disruption_max_df = city_l2_disruption_df.groupby([\"city\", \"category_bucket\", \"date_\"]).agg(\n", "    {\"index\": \"max\"}\n", ")\n", "\n", "city_l2_disruption_df = city_l2_disruption_df.merge(\n", "    city_disruption_max_df,\n", "    on=[\"city\", \"category_bucket\", \"date_\", \"index\"],\n", "    how=\"inner\",\n", ")\n", "\n", "city_l2_disruption_df = (\n", "    city_l2_disruption_df[[\"city\", \"category_bucket\", \"date_\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", "    .rename(columns={\"category_bucket\": \"l2\"})\n", ")\n", "\n", "city_l2_disruption_df[\"flag\"] = 1\n", "\n", "city_l2_disruption_df.shape"]}, {"cell_type": "markdown", "id": "853712e2-4dbe-4852-a424-f975cd4b52de", "metadata": {}, "source": ["### Breads L2s"]}, {"cell_type": "code", "execution_count": null, "id": "82ae3359-8331-4d4a-849a-f78c308bbe16", "metadata": {}, "outputs": [], "source": ["try:\n", "    l2_df = pb.from_sheets(\n", "        sheetid=\"1wFQBrKUB6LIt31VgDT6NoKBfva-DMLpPQtQxnmZRb3w\", sheetname=\"perishable_l2\"\n", "    )\n", "except Exception as e:\n", "    print(f\"Error loading from Google Sheets: {e}\")\n", "    l2_df = pd.read_csv(\"perishable_l2.csv\")\n", "\n", "l2_df[\"l2_id\"] = l2_df[\"l2_id\"].astype(int)\n", "\n", "\n", "### breads\n", "l2_df = l2_df[l2_df[\"l2\"].isin([\"Regular Bread\", \"Bun Pizza\", \"Speciality Breads\"])]\n", "\n", "l2_df"]}, {"cell_type": "markdown", "id": "eeb2a7bd-5035-4ff7-8273-7034da1c5392", "metadata": {}, "source": ["# 1. <PERSON>"]}, {"cell_type": "code", "execution_count": null, "id": "ac2e0ff0-a74c-4eba-8aea-4de8b40acb50", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "SELECT \n", "    city, facility_id, outlet_id,l2, item_id,\n", "    ptype, date_,full_day_avail,\n", "    slot_0, slot_1, slot_2, slot_3, slot_4, slot_5,\n", "    \n", "    sales_fd,\n", "    \n", "    ptype_city_searchers,     \n", "    \n", "    sales_fd_shifted, sales_fd_mean_3d,\n", "    sales_fd_std_3d, sales_fd_mean_7d, sales_fd_std_7d,\n", "    sales_fd_mean_14d, sales_fd_std_14d, velocity_3_7,\n", "    velocity_3_14,\n", "    dow, \n", "    ptype_city_searchers_shifted,\n", "    searchers_mean_3d, searchers_std_3d, searchers_mean_7d,\n", "    searchers_std_7d, \n", "    searchers_mean_14d,\n", "    searchers_std_14d,\n", "    temp_mean_3d, temp_std_3d, temp_mean_7d,\n", "    temp_std_7d, temp_mean_14d, temp_std_14d\n", "FROM supply_etls.breads_pre_processing\n", "WHERE date_ >= DATE('{today_date}') - INTERVAL '180' DAY\n", "\"\"\"\n", "\n", "universe_df = read_sql_query(query, trino)\n", "\n", "print(universe_df.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "f3671c39-0ca2-45d5-a7c3-5afe26db3dcb", "metadata": {}, "outputs": [], "source": ["universe_df[\"master_l2\"] = \"Breads\""]}, {"cell_type": "markdown", "id": "fa2cf5c4-b7d8-4418-ae3c-cacee2783e39", "metadata": {}, "source": ["### 1.1 <PERSON><PERSON><PERSON>"]}, {"cell_type": "markdown", "id": "3e6b80e9-0aff-48c8-9056-bd1866ca5f27", "metadata": {}, "source": ["####  Manual City X L2"]}, {"cell_type": "code", "execution_count": null, "id": "b87d880b-5fed-4175-a300-c1c02929914b", "metadata": {}, "outputs": [], "source": ["city_l2_disruption_df = city_l2_disruption_df.rename(columns={\"l2\": \"master_l2\"})"]}, {"cell_type": "code", "execution_count": null, "id": "93111809-13bf-4dcf-a4c6-da37fee46efc", "metadata": {}, "outputs": [], "source": ["rows_og = universe_df.shape[0]"]}, {"cell_type": "code", "execution_count": null, "id": "968948a6-5284-43cd-ac35-c7988ddded27", "metadata": {}, "outputs": [], "source": ["universe_df[\"date_\"] = pd.to_datetime(universe_df[\"date_\"])\n", "city_l2_disruption_df[\"date_\"] = pd.to_datetime(city_l2_disruption_df[\"date_\"])\n", "\n", "\n", "universe_df = pd.merge(\n", "    universe_df, city_l2_disruption_df, on=[\"city\", \"master_l2\", \"date_\"], how=\"left\"\n", ")\n", "universe_df[\"flag\"] = universe_df[\"flag\"].fillna(0)\n", "\n", "\n", "### remove anamoly days (all days before today where flag was 1-- to avoid cases where we have put future disruptions)\n", "universe_df = universe_df[~((universe_df[\"date_\"] < today_date) & (universe_df[\"flag\"] == 1))]\n", "universe_df = universe_df.drop(columns={\"master_l2\"})\n", "\n", "rows_new = universe_df.shape[0]"]}, {"cell_type": "code", "execution_count": null, "id": "95c49017-13a2-456f-9c11-02f0d6bfc5cd", "metadata": {}, "outputs": [], "source": ["rows_dropped = rows_og - rows_new"]}, {"cell_type": "markdown", "id": "cf62d545-486f-4b01-840f-02b3624bf6de", "metadata": {}, "source": ["### 2.Drop unavailable full day cases "]}, {"cell_type": "code", "execution_count": null, "id": "75e6f040-360f-423e-940f-dd3e776eb376", "metadata": {}, "outputs": [], "source": ["## keep full day avail >0 & >= today date cases\n", "universe_df = universe_df[\n", "    (universe_df[\"date_\"] >= today_date) | (universe_df[\"full_day_avail\"] > 0)\n", "]\n", "rows_new2 = universe_df.shape[0]\n", "rows_dropped2 = rows_new - rows_new2"]}, {"cell_type": "code", "execution_count": null, "id": "cdf03d04-17ab-4677-93f9-7b623d881e34", "metadata": {}, "outputs": [], "source": ["msg = (\n", "    f\"Universe data - OG: {rows_og}\\n\"\n", "    f\"Post Manual City L2 Removal: {rows_new}\\n\"\n", "    f\"Dropped: {rows_dropped}\\n\"\n", "    f\"Full day Unavailable Removal: {rows_new2}\\n\"\n", "    f\"Dropped2: {rows_dropped2}\"\n", ")\n", "\n", "send_alert(text=msg)"]}, {"cell_type": "markdown", "id": "9203de89-aa98-4253-9060-3d4d482ff14e", "metadata": {}, "source": ["# 2.  Count of entries against each outletXitem"]}, {"cell_type": "code", "execution_count": null, "id": "92294da3-f87f-49c5-8a73-83efd9d9b0bd", "metadata": {}, "outputs": [], "source": ["count_ = (\n", "    universe_df[universe_df[\"date_\"] < today_date]\n", "    .groupby([\"city\", \"outlet_id\", \"item_id\"])[\"date_\"]\n", "    .nunique()\n", "    .reset_index(name=\"unique_dates\")\n", ")\n", "\n", "count_[\"<46_flag\"] = np.where(count_[\"unique_dates\"] < 46, 1, 0)"]}, {"cell_type": "code", "execution_count": null, "id": "93f4285b-1dbc-4e17-81bd-587699bce7a9", "metadata": {}, "outputs": [], "source": ["count_.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "6eacd120-1894-41d5-bf2d-94fd762c4e7f", "metadata": {}, "outputs": [], "source": ["count_agg = count_.groupby([\"<46_flag\"]).agg({\"outlet_id\": \"count\"})\n", "count_agg[\"percentage\"] = (count_agg[\"outlet_id\"] / count_agg[\"outlet_id\"].sum()) * 100\n", "count_agg.head(5)"]}, {"cell_type": "markdown", "id": "263136d6-13ee-4b0e-96b0-3dbff604e553", "metadata": {"tags": []}, "source": ["### remove entries where we have < 45 days data"]}, {"cell_type": "markdown", "id": "6398f375-a214-4183-9a2e-da9763c3b326", "metadata": {}, "source": ["#### come back to this ^^"]}, {"cell_type": "code", "execution_count": null, "id": "fba12faf-d9e0-4ef9-9fe6-19f417b2d7a6", "metadata": {}, "outputs": [], "source": ["universe_df = universe_df.merge(count_, on=[\"city\", \"outlet_id\", \"item_id\"], how=\"left\")\n", "\n", "universe_df_less45 = universe_df[universe_df[\"<46_flag\"] == 1]\n", "universe_df_less45 = universe_df[universe_df[\"<46_flag\"] == 1]\n", "\n", "universe_df = universe_df[universe_df[\"<46_flag\"] == 0]\n", "\n", "# universe_df = universe_df[universe_df['<31_flag'] == 0]\n", "# universe_df = universe_df.drop(columns = {\"<31_flag\"})\n", "\n", "universe_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "21842d9d-ccbb-4550-a3fe-fb6a356f93d4", "metadata": {}, "outputs": [], "source": ["count_agg = count_agg.reset_index()\n", "\n", "row = count_agg[count_agg[\"<46_flag\"] == 1].iloc[0]\n", "item_outlet_combination = row[\"outlet_id\"]\n", "perc_of_uni = row[\"percentage\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "3c7302a6-c71d-4049-8210-7e09cf64f524", "metadata": {}, "outputs": [], "source": ["msg = (\n", "    f\"Item x Outlet with < 45 days data: {item_outlet_combination}\\n\"\n", "    f\"%ge of universe: {perc_of_uni}%\\n\"\n", ")\n", "\n", "send_alert(text=msg)"]}, {"cell_type": "markdown", "id": "ec10cd5d-4b0f-4d63-95cb-26963396c4c1", "metadata": {}, "source": ["### Fill sales & avail columns for >=today's data"]}, {"cell_type": "code", "execution_count": null, "id": "548d210c-b307-41c8-a76d-fffdbdf6b13d", "metadata": {}, "outputs": [], "source": ["today_date"]}, {"cell_type": "code", "execution_count": null, "id": "a11d2085-95f9-467d-b7fc-0f5c644d99e7", "metadata": {}, "outputs": [], "source": ["universe_df[\"sales_fd\"] = np.where(universe_df[\"date_\"] >= today_date, 0, universe_df[\"sales_fd\"])\n", "\n", "for column in [\"slot_0\", \"slot_1\", \"slot_2\", \"slot_3\", \"slot_4\", \"slot_5\"]:\n", "    universe_df[column] = np.where(\n", "        universe_df[\"date_\"] >= today_date, 0, universe_df[column]\n", "    )  ### here 0 = full availability\n", "\n", "\n", "# universe_df.drop(columns={\"flag\", \"full_day_avail\"}, inplace = True)\n", "# universe_df.drop(columns={ \"full_day_avail\"}, inplace = True)"]}, {"cell_type": "code", "execution_count": null, "id": "ff409182-26bc-49f9-bb77-6de374bbe923", "metadata": {}, "outputs": [], "source": ["universe_df.columns"]}, {"cell_type": "markdown", "id": "8a9e9d27-1874-4164-ba53-048e63170e22", "metadata": {}, "source": ["## freeze velocity features for T+2 to T+7"]}, {"cell_type": "code", "execution_count": null, "id": "b0ece5b3-851a-4851-996d-1fcfadb25b17", "metadata": {}, "outputs": [], "source": ["f2_snapshot = universe_df[universe_df[\"date_\"] == f2].copy()\n", "\n", "frozen_cols = [\n", "    \"ptype_city_searchers\",\n", "    \"sales_fd_shifted\",\n", "    \"sales_fd_mean_3d\",\n", "    \"sales_fd_std_3d\",\n", "    \"sales_fd_mean_7d\",\n", "    \"sales_fd_std_7d\",\n", "    \"sales_fd_mean_14d\",\n", "    \"sales_fd_std_14d\",\n", "    \"velocity_3_7\",\n", "    \"velocity_3_14\",\n", "    \"ptype_city_searchers_shifted\",\n", "    \"searchers_mean_3d\",\n", "    \"searchers_std_3d\",\n", "    \"searchers_mean_7d\",\n", "    \"searchers_std_7d\",\n", "    \"searchers_mean_14d\",\n", "    \"searchers_std_14d\",\n", "]\n", "\n", "# Also keep identifiers for merging\n", "id_cols = [\"outlet_id\", \"item_id\"]\n", "f2_snapshot = f2_snapshot[id_cols + frozen_cols]\n", "\n", "# Step 2: Merge these frozen values back into universe_df where date_ > f2\n", "mask = universe_df[\"date_\"] > f2\n", "universe_df.loc[mask, frozen_cols] = (\n", "    universe_df[mask]\n", "    .merge(f2_snapshot, on=id_cols, how=\"left\", suffixes=(\"\", \"_frozen\"))[\n", "        [col + \"_frozen\" for col in frozen_cols]\n", "    ]\n", "    .values\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "97739839-bd13-4430-aa22-b03281190f25", "metadata": {}, "outputs": [], "source": ["universe_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "d3ce6415-c05d-43a0-97cb-94b01df9cd0f", "metadata": {}, "outputs": [], "source": ["# filtered_df = universe_df[universe_df['date_'] >= today_date]\n", "\n", "# # Select only numeric columns\n", "# numeric_cols = filtered_df.select_dtypes(include='number').columns\n", "\n", "# # Group by date and aggregate min, max, mean\n", "# agg_df = filtered_df.groupby('date_')[numeric_cols].agg(['min', 'max', 'mean'])\n", "\n", "# # Flatten the multi-index columns (optional for readability)\n", "# agg_df.columns = ['_'.join(col).strip() for col in agg_df.columns.values]\n", "# agg_df = agg_df.reset_index()\n", "# agg_df.head(100)"]}, {"cell_type": "code", "execution_count": null, "id": "7d9821d6-eef4-4c5a-9ba3-17237b09bb60", "metadata": {}, "outputs": [], "source": ["universe_df.columns"]}, {"cell_type": "markdown", "id": "be087bf5-3424-48a7-a639-e168cd1ebe5f", "metadata": {}, "source": ["# 3. Model"]}, {"cell_type": "code", "execution_count": null, "id": "ecb30f8d-eacd-4b29-847d-c92c4d6b304f", "metadata": {}, "outputs": [], "source": ["FEATURES = [\n", "    \"outlet_id\",\n", "    \"l2\",\n", "    \"item_id\",\n", "    \"ptype\",\n", "    \"slot_0\",\n", "    \"slot_1\",\n", "    \"slot_2\",\n", "    \"slot_3\",\n", "    \"slot_4\",\n", "    \"slot_5\",\n", "    \"sales_fd_mean_3d\",\n", "    \"sales_fd_std_3d\",\n", "    \"sales_fd_mean_7d\",\n", "    \"sales_fd_std_7d\",\n", "    \"sales_fd_mean_14d\",\n", "    \"sales_fd_std_14d\",\n", "    \"velocity_3_7\",\n", "    \"velocity_3_14\",\n", "    \"dow\",\n", "    # \"ptype_city_searchers_shifted\",\n", "    # \"searchers_mean_3d\",\n", "    # \"searchers_std_3d\",\n", "    # \"searchers_mean_7d\",\n", "    # \"searchers_std_7d\",\n", "    # \"searchers_mean_14d\",\n", "    # \"searchers_std_14d\",\n", "    # \"temp_mean_3d\",\n", "    # \"temp_std_3d\",\n", "    # \"temp_mean_7d\",\n", "    # \"temp_std_7d\",\n", "    # \"temp_mean_14d\",\n", "    # \"temp_std_14d\",\n", "]\n", "\n", "TARGET = \"sales_fd\"\n", "\n", "CATEGORICAL = [\n", "    # 'city',\n", "    \"outlet_id\",\n", "    \"item_id\",\n", "    \"l2\",\n", "    \"ptype\",\n", "    \"dow\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "b89d9ddb-4d97-4c68-83a7-c343a91d1893", "metadata": {}, "outputs": [], "source": ["required_cols = FEATURES + [TARGET, \"date_\", \"city\"]\n", "base = universe_df[required_cols].copy()"]}, {"cell_type": "code", "execution_count": null, "id": "052c8096-4b68-47cf-85c4-ceac94189fef", "metadata": {}, "outputs": [], "source": ["required_cols"]}, {"cell_type": "code", "execution_count": null, "id": "0b6764a4-d61a-4259-978d-82415958424c", "metadata": {}, "outputs": [], "source": ["# ## final city list (with > 45 days data)\n", "# cities = tuple(set(base[\"city\"].to_list()))"]}, {"cell_type": "code", "execution_count": null, "id": "d57fce17-5f55-4d2e-a807-cfbe21819d11", "metadata": {}, "outputs": [], "source": ["from catboost import CatBoostRegressor\n", "from sklearn.model_selection import TimeSeriesSplit\n", "from sklearn.metrics import mean_absolute_error\n", "\n", "# Load and sort data by time\n", "base.sort_values(by=[\"outlet_id\", \"item_id\", \"date_\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "e965679e-20fd-4899-8e46-cfd69febc1ad", "metadata": {}, "outputs": [], "source": ["base.shape"]}, {"cell_type": "code", "execution_count": null, "id": "2234cee8-e318-483a-a831-88327e6b2505", "metadata": {}, "outputs": [], "source": ["y = base[(base[\"item_id\"] == 10167413) & (base[\"outlet_id\"] == 1881)]\n", "y.to_csv(\"y2.csv\")"]}, {"cell_type": "markdown", "id": "c4e2af18-19c3-402d-9381-f01934a9d016", "metadata": {}, "source": ["### Day index feature"]}, {"cell_type": "code", "execution_count": null, "id": "fda03112-5837-4a14-9bba-2ff076705c17", "metadata": {}, "outputs": [], "source": ["base[\"rank_\"] = base.groupby([\"outlet_id\", \"item_id\"])[\"date_\"].rank(\n", "    method=\"first\", ascending=False\n", ")\n", "base = base[base[\"rank_\"] < 53]"]}, {"cell_type": "code", "execution_count": null, "id": "d1953cbf-040d-4515-89cf-834fd49b2e78", "metadata": {}, "outputs": [], "source": ["base[\"day_index_feature\"] = base.groupby([\"outlet_id\", \"item_id\"])[\"date_\"].rank(\n", "    method=\"first\", ascending=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4ba4cbf9-8a55-46a5-a13a-441a7a2cd21d", "metadata": {}, "outputs": [], "source": ["base = base.sort_values(by=[\"outlet_id\", \"item_id\", \"date_\"])\n", "\n", "base[\"day_index\"] = (\n", "    base.groupby([\"outlet_id\", \"item_id\"])[\"date_\"].rank(method=\"first\", ascending=True).astype(int)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "59e5ccd7-89d9-4379-999e-e9cc1c502b94", "metadata": {}, "outputs": [], "source": ["base.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "9b0d827a-57b7-45f4-9192-ca45e313e472", "metadata": {}, "outputs": [], "source": ["# Get unique combinations\n", "date_di = base[[\"date_\", \"day_index_feature\"]].drop_duplicates()\n", "\n", "# Sort so that we can rank dates per day_index_feature\n", "date_di_sorted = date_di.sort_values(by=[\"day_index_feature\", \"date_\"], ascending=[True, False])\n", "\n", "# Get top 3 dates per day_index_feature and concatenate\n", "top_dates_per_day = (\n", "    date_di_sorted.groupby(\"day_index_feature\")[\"date_\"]\n", "    .apply(lambda x: \" | \".join(x.astype(str).head(3)))\n", "    .reset_index(name=\"top_3_dates\")\n", ")\n", "\n", "# View top 10 entries\n", "top_dates_per_day.sort_values(by=\"day_index_feature\", ascending=False).head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "7333dd21-bb22-4050-86ba-6c2778849d54", "metadata": {}, "outputs": [], "source": ["TRAIN_DAYS = list(range(15, 45))  # 15 to 44 inclusive\n", "PREDICT_DAYS = list(range(46, 53))  # 46 to 52 inclusive"]}, {"cell_type": "code", "execution_count": null, "id": "ebc6ca89-6dd1-416d-b73e-************", "metadata": {}, "outputs": [], "source": ["PREDICT_DAYS"]}, {"cell_type": "code", "execution_count": null, "id": "37f32ffd-a512-4e03-9430-782ed0485ce2", "metadata": {}, "outputs": [], "source": ["quantiles = [0.3, 0.4, 0.5, 0.6, 0.7, 0.8]\n", "cities = base[\"city\"].unique().tolist()"]}, {"cell_type": "code", "execution_count": null, "id": "4717b0b7-aa1c-4bf8-84ed-4f47a490671c", "metadata": {}, "outputs": [], "source": ["# cities = ['Kakinada', 'Barabanki', 'Kolkata']"]}, {"cell_type": "code", "execution_count": null, "id": "17e5beb9-6919-4c5b-801f-40628381a43a", "metadata": {}, "outputs": [], "source": ["def get_train_inf_data(base, day_t):\n", "    \"\"\"Generate fixed training and inference dataset.\"\"\"\n", "    # Fixed training from day_index 15 to 44\n", "    train_mask = base[\"day_index_feature\"].between(15, 44)\n", "    inf_mask = base[\"day_index_feature\"] == day_t\n", "\n", "    X_train, y_train = base.loc[train_mask, FEATURES], base.loc[train_mask, TARGET]\n", "    X_inf = base.loc[inf_mask, FEATURES]\n", "\n", "    return X_train, y_train, X_inf"]}, {"cell_type": "markdown", "id": "58782ae7-f67b-47a4-82d4-7ad80ca9b4ec", "metadata": {"jp-MarkdownHeadingCollapsed": true, "tags": []}, "source": ["## Train"]}, {"cell_type": "code", "execution_count": null, "id": "3d5ef0d6-db95-49b2-8517-de233d0ab8bb", "metadata": {}, "outputs": [], "source": ["# def train_models(base, quantiles, cities):\n", "#     import os\n", "#     import pickle\n", "#     import pandas as pd\n", "#     from catboost import CatBoostRegressor\n", "#     import matplotlib.pyplot as plt\n", "\n", "#     MODEL_DIR = f\"models_{today_date}\"\n", "\n", "#     os.makedirs(MODEL_DIR, exist_ok=True)\n", "\n", "#     errors_summary = []\n", "\n", "#     for city in cities:\n", "#         city_data = base[base[\"city\"] == city].copy()\n", "#         if city_data.empty:\n", "#             print(f\"⚠️ Skipping {city} — no data found.\")\n", "#             continue\n", "\n", "#         print(f\"\\n🏙️ Training city: {city}\")\n", "#         for alpha in quantiles:\n", "#             alpha_str = f\"{alpha:.2f}\".replace(\".\", \"\")\n", "#             print(f\"📈 Quantile: {alpha}\")\n", "\n", "#             # Dummy inference day (not used during training)\n", "#             dummy_day = 9999  # unlikely to exist, just to satisfy the function\n", "#             X_all, y_all, _ = get_train_inf_data(city_data, dummy_day)\n", "\n", "#             if y_all.empty or y_all.nunique() <= 1:\n", "#                 print(f\"⚠️ No usable training data for {city} - q={alpha}\")\n", "#                 continue\n", "\n", "#             X_all.update(X_all.select_dtypes(\"Int64\").astype(\"int64\"))\n", "#             X_all.update(X_all.select_dtypes(\"Float64\").astype(\"float64\"))\n", "#             X_all[CATEGORICAL] = X_all[CATEGORICAL].astype(str)\n", "\n", "#             # model = CatBoostRegressor(\n", "#             #     iterations=700,\n", "#             #     learning_rate=0.05,\n", "#             #     depth=8,\n", "#             #     l2_leaf_reg=3,\n", "#             #     border_count=100,\n", "#             #     loss_function=f\"Quantile:alpha={alpha}\",\n", "#             #     cat_features=CATEGORICAL,\n", "#             #     early_stopping_rounds=50,\n", "#             #     random_seed=42,\n", "#             #     verbose=False,\n", "#             # )\n", "\n", "#    ### post optuna tuning\n", "#             model = CatBoostRegressor(\n", "#                 iterations=900,\n", "#                 learning_rate=0.05,\n", "#                 depth=8,\n", "#                 l2_leaf_reg=6,\n", "#                 border_count=100,\n", "#                 loss_function=f\"Quantile:alpha={alpha}\",\n", "#                 cat_features=CATEGORICAL,\n", "#                 early_stopping_rounds=50,\n", "#                 random_seed=42,\n", "#                 verbose=False,\n", "#             )\n", "\n", "\n", "#             model.fit(X_all, y_all)\n", "\n", "#             preds = model.predict(X_all)\n", "#             err = y_all - preds\n", "#             errors_summary.append(pd.DataFrame({\n", "#                 \"error\": err,\n", "#                 \"city\": city,\n", "#                 \"quantile\": alpha\n", "#             }))\n", "\n", "#             model_path = f\"{MODEL_DIR}/catboost_{city}_q{alpha_str}.pkl\"\n", "#             with open(model_path, \"wb\") as f:\n", "#                 pickle.dump(model, f)\n", "\n", "#             print(f\"✅ Model saved to {model_path} | Train samples: {len(y_all)}\")"]}, {"cell_type": "markdown", "id": "ce605ea7-c8a1-446d-a205-fc4f7f2a72bf", "metadata": {}, "source": ["## Train with best Quantile"]}, {"cell_type": "code", "execution_count": null, "id": "86dea068-adb3-4c3f-b808-b8a13cce2c74", "metadata": {}, "outputs": [], "source": ["# def train_models(base, quantiles, cities):\n", "#     import os\n", "#     import pickle\n", "#     import pandas as pd\n", "#     from catboost import CatBoostRegressor\n", "#     from datetime import date\n", "\n", "#     today_date = date.today().strftime(\"%m%d\")\n", "#     MODEL_DIR = f\"models_{today_date}\"\n", "#     os.makedirs(MODEL_DIR, exist_ok=True)\n", "\n", "#     errors_summary = []\n", "#     best_quantiles = []\n", "\n", "#     for city in cities:\n", "#         city_data = base[base[\"city\"] == city].copy()\n", "#         if city_data.empty:\n", "#             print(f\"⚠️ Skipping {city} — no data found.\")\n", "#             continue\n", "\n", "#         print(f\"\\n🏙️ Training city: {city}\")\n", "#         for alpha in quantiles:\n", "#             alpha_str = f\"{alpha:.2f}\".replace(\".\", \"\")\n", "#             print(f\"📈 Quantile: {alpha}\")\n", "\n", "#             dummy_day = 9999\n", "#             X_all, y_all, _ = get_train_inf_data(city_data, dummy_day)\n", "\n", "#             if y_all.empty or y_all.nunique() <= 1:\n", "#                 print(f\"⚠️ No usable training data for {city} - q={alpha}\")\n", "#                 continue\n", "\n", "#             X_all.update(X_all.select_dtypes(\"Int64\").astype(\"int64\"))\n", "#             X_all.update(X_all.select_dtypes(\"Float64\").astype(\"float64\"))\n", "#             X_all[CATEGORICAL] = X_all[CATEGORICAL].astype(str)\n", "\n", "#             model = CatBoostRegressor(\n", "#                 iterations=900,\n", "#                 learning_rate=0.05,\n", "#                 depth=8,\n", "#                 l2_leaf_reg=10,\n", "#                 border_count=100,\n", "#                 loss_function=f\"Quantile:alpha={alpha}\",\n", "#                 cat_features=CATEGORICAL,\n", "#                 early_stopping_rounds=50,\n", "#                 random_seed=42,\n", "#                 verbose=False,\n", "#             )\n", "\n", "#             model.fit(X_all, y_all)\n", "\n", "#             preds = model.predict(X_all)\n", "#             err = y_all - preds\n", "#             errors_summary.append(pd.DataFrame({\"error\": err, \"city\": city, \"quantile\": alpha}))\n", "\n", "#             # Attach error info to X_all\n", "#             X_all[\"error\"] = err\n", "#             X_all[\"preds\"] = preds\n", "#             X_all[\"actuals\"] = y_all.values\n", "\n", "#             # Compute error % for each (outlet_id, item_id, dow)\n", "#             group_error = (\n", "#                 X_all.groupby([\"outlet_id\", \"item_id\", \"dow\"])\n", "#                 .apply(\n", "#                     lambda g: (g[\"error\"].abs().sum() / g[\"actuals\"].sum()) * 100\n", "#                     if g[\"actuals\"].sum() != 0\n", "#                     else 9999\n", "#                 )\n", "#                 .reset_index(name=\"error_percentage\")\n", "#             )\n", "\n", "#             for _, row in group_error.iterrows():\n", "#                 best_quantiles.append(\n", "#                     {\n", "#                         \"outlet_id\": row[\"outlet_id\"],\n", "#                         \"item_id\": row[\"item_id\"],\n", "#                         \"dow\": row[\"dow\"],\n", "#                         \"best_quantile\": alpha,\n", "#                         \"error_percentage\": row[\"error_percentage\"],\n", "#                     }\n", "#                 )\n", "\n", "#             model_path = f\"{MODEL_DIR}/catboost_{city}_q{alpha_str}.pkl\"\n", "#             with open(model_path, \"wb\") as f:\n", "#                 pickle.dump(model, f)\n", "\n", "#             print(f\"✅ Model saved to {model_path} | Train samples: {len(y_all)}\")\n", "\n", "#     # Consolidate best quantiles\n", "#     best_quantile_df = pd.DataFrame(best_quantiles)\n", "#     best_q_final = (\n", "#         best_quantile_df.sort_values(\"error_percentage\")\n", "#         .drop_duplicates(subset=[\"outlet_id\", \"item_id\", \"dow\"], keep=\"first\")\n", "#         .reset_index(drop=True)\n", "#     )\n", "\n", "#     # --- New: save best_q_final ---\n", "#     csv_path = os.path.join(MODEL_DIR, \"best_quantiles.csv\")\n", "#     pkl_path = os.path.join(MODEL_DIR, \"best_quantiles.pkl\")\n", "#     best_q_final.to_csv(csv_path, index=False)\n", "#     with open(pkl_path, \"wb\") as f:\n", "#         pickle.dump(best_q_final, f)\n", "#     print(f\"\\n💾 Saved best quantiles to:\\n  • {csv_path}\\n  • {pkl_path}\")\n", "\n", "#     print(\"\\n🎯 Best Quantiles per Outlet x Item x DOW:\")\n", "#     print(best_q_final)\n", "\n", "#     return best_q_final"]}, {"cell_type": "code", "execution_count": null, "id": "362cea9e-3946-4b4b-8e99-b05c75cb5147", "metadata": {}, "outputs": [], "source": ["def train_models(base, quantiles, cities):\n", "    import os\n", "    import pickle\n", "    import pandas as pd\n", "    from catboost import CatBoostRegressor\n", "    from datetime import date\n", "\n", "    today_date = date.today().strftime(\"%m%d\")\n", "    MODEL_DIR = f\"models_{today_date}\"\n", "    os.makedirs(MODEL_DIR, exist_ok=True)\n", "\n", "    errors_summary = []\n", "    best_quantiles = []\n", "\n", "    for city in cities:\n", "        city_data = base[base[\"city\"] == city].copy()\n", "        if city_data.empty:\n", "            print(f\"⚠️ Skipping {city} — no data found.\")\n", "            continue\n", "\n", "        print(f\"\\n🏙️ Training city: {city}\")\n", "        for alpha in quantiles:\n", "            alpha_str = f\"{alpha:.2f}\".replace(\".\", \"\")\n", "            print(f\"📈 Quantile: {alpha}\")\n", "\n", "            dummy_day = 9999\n", "            X_all, y_all, _ = get_train_inf_data(city_data, dummy_day)\n", "\n", "            if y_all.empty or y_all.nunique() <= 1:\n", "                print(f\"⚠️ No usable training data for {city} - q={alpha}\")\n", "                continue\n", "\n", "            X_all.update(X_all.select_dtypes(\"Int64\").astype(\"int64\"))\n", "            X_all.update(X_all.select_dtypes(\"Float64\").astype(\"float64\"))\n", "            X_all[CATEGORICAL] = X_all[CATEGORICAL].astype(str)\n", "\n", "            model = CatBoostRegressor(\n", "                iterations=900,\n", "                learning_rate=0.05,\n", "                depth=8,\n", "                l2_leaf_reg=10,\n", "                border_count=100,\n", "                loss_function=f\"Quantile:alpha={alpha}\",\n", "                cat_features=CATEGORICAL,\n", "                early_stopping_rounds=50,\n", "                random_seed=42,\n", "                verbose=False,\n", "            )\n", "\n", "            model.fit(X_all, y_all)\n", "\n", "            preds = model.predict(X_all)\n", "            err = y_all - preds\n", "            errors_summary.append(pd.DataFrame({\"error\": err, \"city\": city, \"quantile\": alpha}))\n", "\n", "            # Attach error info to X_all\n", "            X_all[\"error\"] = err\n", "            X_all[\"preds\"] = preds\n", "            X_all[\"actuals\"] = y_all.values\n", "\n", "            # Keep only overpredictions (positive errors)\n", "            X_over = X_all[X_all[\"error\"] > 0].copy()\n", "\n", "            # Compute error % for overpredictions only\n", "            group_error = (\n", "                X_over.groupby([\"outlet_id\", \"item_id\", \"dow\"])\n", "                .apply(\n", "                    lambda g: (\n", "                        (g[\"error\"].sum() / g[\"actuals\"].sum()) * 100\n", "                        if g[\"actuals\"].sum() != 0\n", "                        else 9999\n", "                    )\n", "                )\n", "                .reset_index(name=\"error_percentage\")\n", "            )\n", "\n", "            for _, row in group_error.iterrows():\n", "                best_quantiles.append(\n", "                    {\n", "                        \"outlet_id\": row[\"outlet_id\"],\n", "                        \"item_id\": row[\"item_id\"],\n", "                        \"dow\": row[\"dow\"],\n", "                        \"best_quantile\": alpha,\n", "                        \"error_percentage\": row[\"error_percentage\"],\n", "                    }\n", "                )\n", "\n", "            model_path = f\"{MODEL_DIR}/catboost_{city}_q{alpha_str}.pkl\"\n", "            with open(model_path, \"wb\") as f:\n", "                pickle.dump(model, f)\n", "\n", "            print(f\"✅ Model saved to {model_path} | Train samples: {len(y_all)}\")\n", "\n", "    # Consolidate best quantiles\n", "    best_quantile_df = pd.DataFrame(best_quantiles)\n", "    best_q_final = (\n", "        best_quantile_df.sort_values(\"error_percentage\")\n", "        .drop_duplicates(subset=[\"outlet_id\", \"item_id\", \"dow\"], keep=\"first\")\n", "        .reset_index(drop=True)\n", "    )\n", "\n", "    # --- New: save best_q_final ---\n", "    csv_path = os.path.join(MODEL_DIR, \"best_quantiles.csv\")\n", "    pkl_path = os.path.join(MODEL_DIR, \"best_quantiles.pkl\")\n", "    best_q_final.to_csv(csv_path, index=False)\n", "    with open(pkl_path, \"wb\") as f:\n", "        pickle.dump(best_q_final, f)\n", "    print(f\"\\n💾 Saved best quantiles to:\\n  • {csv_path}\\n  • {pkl_path}\")\n", "\n", "    print(\"\\n🎯 Best Quantiles per Outlet x Item x DOW:\")\n", "    print(best_q_final)\n", "\n", "    return best_q_final"]}, {"cell_type": "code", "execution_count": null, "id": "471026a4-d17e-437e-9607-b7ee2ac9c4a4", "metadata": {"tags": []}, "outputs": [], "source": ["train_models(base, quantiles, cities)"]}, {"cell_type": "markdown", "id": "f7fc0130-7099-4a84-8152-52c6f821bd89", "metadata": {}, "source": ["## Forecasting"]}, {"cell_type": "code", "execution_count": null, "id": "f066ad95-ce85-4fa8-8af1-2eb402bdfe6d", "metadata": {}, "outputs": [], "source": ["def predict_selected_days(base, quantiles, cities):\n", "    \"\"\"Use saved models to predict for days 47–52 and tag best quantile (loaded from disk).\"\"\"\n", "    import os\n", "    import pickle\n", "    import pandas as pd\n", "    from datetime import datetime, date\n", "\n", "    # --- derive today's date and model directory ---\n", "    today_date = date.today().strftime(\"%m%d\")\n", "    model_dir = f\"models_{today_date}\"\n", "\n", "    # --- load best_quantile_df from pickle (fallback to CSV if needed) ---\n", "    pkl_path = os.path.join(model_dir, \"best_quantiles.pkl\")\n", "    csv_path = os.path.join(model_dir, \"best_quantiles.csv\")\n", "    if os.path.exists(pkl_path):\n", "        best_quantile_df = pd.read_pickle(pkl_path)\n", "    else:\n", "        best_quantile_df = pd.read_csv(csv_path)\n", "\n", "    PREDICT_DAYS = list(range(47, 53))\n", "    overall_start_time = datetime.now()\n", "    output_file = f\"forecast_47to52_mcity_{today_date}.csv\"\n", "    print(f\"Saving predictions to: {output_file}\")\n", "\n", "    header_written = False\n", "    all_predictions = []\n", "\n", "    for city in cities:\n", "        city_data = base[base[\"city\"] == city].copy()\n", "        if city_data.empty:\n", "            print(f\"⚠️ Skipping {city} — no data found.\")\n", "            continue\n", "\n", "        print(f\"\\n🏙️ Predicting for city: {city}\")\n", "        for alpha in quantiles:\n", "            alpha_str = f\"{alpha:.2f}\".replace(\".\", \"\")\n", "            print(f\"📈 Quantile: {alpha}\")\n", "\n", "            model_path = os.path.join(model_dir, f\"catboost_{city}_q{alpha_str}.pkl\")\n", "            if not os.path.exists(model_path):\n", "                print(f\"⚠️ Model not found: {model_path}\")\n", "                continue\n", "\n", "            with open(model_path, \"rb\") as f:\n", "                model = pickle.load(f)\n", "\n", "            for day_t in PREDICT_DAYS:\n", "                print(f\"→ Predicting Day {day_t}\")\n", "                _, _, X_inf = get_train_inf_data(city_data, day_t)\n", "                if X_inf.empty:\n", "                    print(f\"⚠️ No inference data for day {day_t}\")\n", "                    continue\n", "\n", "                # ensure correct dtypes\n", "                X_inf.update(X_inf.select_dtypes(\"Int64\").astype(\"int64\"))\n", "                X_inf.update(X_inf.select_dtypes(\"Float64\").astype(\"float64\"))\n", "                X_inf[CATEGORICAL] = X_inf[CATEGORICAL].astype(str)\n", "\n", "                X_inf[\"quantile\"] = alpha\n", "                X_inf[\"prediction\"] = model.predict(X_inf)\n", "                X_inf[\"day\"] = day_t\n", "                X_inf[\"day_index_feature\"] = day_t\n", "                X_inf[\"city\"] = city\n", "\n", "                # merge best-quantile info\n", "                merge_keys = [\"outlet_id\", \"item_id\", \"dow\"]\n", "                merge_df = best_quantile_df[merge_keys + [\"best_quantile\"]].copy()\n", "                merge_df[\"best_quantile\"] = merge_df[\"best_quantile\"].astype(float)\n", "                merged = pd.merge(X_inf, merge_df, on=merge_keys, how=\"left\")\n", "                merged[\"is_best_quantile\"] = (merged[\"quantile\"] == merged[\"best_quantile\"]).astype(\n", "                    int\n", "                )\n", "\n", "                # append & write out\n", "                merged.to_csv(output_file, mode=\"a\", header=not header_written, index=False)\n", "                all_predictions.append(merged)\n", "                header_written = True\n", "\n", "    final_df = pd.concat(all_predictions, ignore_index=True)\n", "    print(f\"\\n🎉 Prediction complete. Results saved to: {output_file}\")\n", "\n", "    return final_df"]}, {"cell_type": "code", "execution_count": null, "id": "676c0ebd-15db-4ebc-aac7-a1efd58ce201", "metadata": {}, "outputs": [], "source": ["cities"]}, {"cell_type": "code", "execution_count": null, "id": "a79a0774-1fdd-4615-9c73-21c15af95fd4", "metadata": {"tags": []}, "outputs": [], "source": ["# Predict\n", "forecast_df = predict_selected_days(base, quantiles, cities)"]}, {"cell_type": "code", "execution_count": null, "id": "35de9449-fb58-4df0-b3a4-4f7cd45c8619", "metadata": {}, "outputs": [], "source": ["forecast_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "43800a0e-ec36-4a88-91ca-e63e6e36bde4", "metadata": {}, "outputs": [], "source": ["forecast_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "2a1f04bf-5ecd-47a0-8924-4e8c5837b31e", "metadata": {}, "outputs": [], "source": ["forecast_df = forecast_df.merge(date_di, on=[\"day_index_feature\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "64fdb2df-dac6-451e-a701-07beb9d63409", "metadata": {}, "outputs": [], "source": ["forecast_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "9295adaf-e24d-42ca-9e4b-4827cd30620e", "metadata": {}, "outputs": [], "source": ["for col in [\"outlet_id\", \"item_id\", \"dow\"]:\n", "    forecast_df[col] = forecast_df[col].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "89e4288f-77ea-47c2-8868-441184ab4598", "metadata": {}, "outputs": [], "source": ["upload_df = forecast_df.copy()\n", "\n", "upload_df = upload_df[\n", "    [\n", "        \"city\",\n", "        \"outlet_id\",\n", "        \"l2\",\n", "        \"item_id\",\n", "        \"ptype\",\n", "        \"date_\",\n", "        \"dow\",\n", "        \"quantile\",\n", "        \"prediction\",\n", "        \"is_best_quantile\",\n", "    ]\n", "].drop_duplicates()\n", "\n", "upload_df[\"prediction\"] = np.round(upload_df[\"prediction\"], 2)\n", "\n", "upload_df = upload_df.rename(columns={\"prediction\": \"final_ex_qty\"})\n", "\n", "upload_df[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))\n", "\n", "upload_df[\"date_ist\"] = pd.to_datetime(upload_df[\"updated_at\"]).dt.strftime(\"%Y-%m-%d\")\n", "\n", "upload_df[\"date_ist\"] = pd.to_datetime(upload_df[\"date_ist\"])"]}, {"cell_type": "code", "execution_count": null, "id": "8f1c4091-d8d9-4db7-98d2-0584f0e80b33", "metadata": {}, "outputs": [], "source": ["upload_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "4c70fde2-714d-4288-bbff-cb1e16cfc1b4", "metadata": {}, "outputs": [], "source": ["upload_df = upload_df[\n", "    [\n", "        \"city\",\n", "        \"outlet_id\",\n", "        \"l2\",\n", "        \"item_id\",\n", "        \"ptype\",\n", "        \"date_\",\n", "        \"dow\",\n", "        \"quantile\",\n", "        \"final_ex_qty\",\n", "        \"is_best_quantile\",\n", "        \"updated_at\",\n", "        \"date_ist\",\n", "    ]\n", "]\n", "\n", "upload_df[\"final_ex_qty\"] = np.where(upload_df[\"final_ex_qty\"] < 1, 1, upload_df[\"final_ex_qty\"])"]}, {"cell_type": "code", "execution_count": null, "id": "0e14b15b-21f3-4784-aa7b-3a0b093bf0c8", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"city\", \"type\": \"VARCHAR\", \"description\": \"city name\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"outlet identifier\"},\n", "    {\"name\": \"l2\", \"type\": \"VARCHAR\", \"description\": \"L2 category\"},\n", "    {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"item identifier\"},\n", "    {\"name\": \"ptype\", \"type\": \"VARCHAR\", \"description\": \"product type\"},\n", "    {\"name\": \"date_\", \"type\": \"DATE\", \"description\": \"forecast date\"},\n", "    {\"name\": \"dow\", \"type\": \"INTEGER\", \"description\": \"day of week (0=Monday)\"},\n", "    {\"name\": \"quantile\", \"type\": \"DOUBLE\", \"description\": \"quantile level\"},\n", "    {\"name\": \"final_ex_qty\", \"type\": \"DOUBLE\", \"description\": \"final forecast quantity\"},\n", "    {\n", "        \"name\": \"is_best_quantile\",\n", "        \"type\": \"INTEGER\",\n", "        \"description\": \"1 if this is the best quantile, else 0\",\n", "    },\n", "    {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(6)\", \"description\": \"timestamp of run\"},\n", "    {\"name\": \"date_ist\", \"type\": \"DATE\", \"description\": \"run date in IST\"},\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"ds_breads_forecast\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"outlet_id\", \"item_id\", \"date_\"],\n", "    \"partition_key\": [\"date_ist\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"append\",  # options: append, rebuild, truncate, upsert\n", "    \"table_description\": \"Perishable forecast outputs with best-quantile flag\",\n", "}\n", "\n", "pb.to_trino(upload_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "acf39c38-5dd4-4286-829b-b428270cfa0b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}