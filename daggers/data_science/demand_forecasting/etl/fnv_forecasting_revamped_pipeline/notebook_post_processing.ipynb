{"cells": [{"cell_type": "code", "execution_count": null, "id": "ab2785af-5ef0-4946-9ac7-00a3f4e13d30", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e7e40283-4081-48fe-802f-af647e483532", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import os\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "b27cf80e-60db-4987-9b5f-5ba8fdd417e6", "metadata": {}, "outputs": [], "source": ["dag_id = task_instance_key_str.split(\"__\")[0]\n", "task_id = task_instance_key_str.split(\"__\")[1]\n", "dag_run_date_str = run_id.split(\"T\")[0].split(\"_\")[-1]\n", "dag_run_date = datetime.strptime(dag_run_date_str, \"%Y-%m-%d\")\n", "run_date_input = str((dag_run_date + timedelta(days=2)).date())\n", "print(dag_id, task_id, run_date_input)"]}, {"cell_type": "code", "execution_count": null, "id": "1fd373ea-4822-421c-a0f4-8bce6025b763", "metadata": {}, "outputs": [], "source": ["dag_link = f\"https://dse-commuter.grofer.io/view/{dag_id}/{run_id}/{task_id}.ipynb\"\n", "print(dag_link)\n", "pb.send_slack_message(\n", "    channel=\"alerts-11\",\n", "    text=f\"fnv_demand_forecasting_revamped for Quantile selection and post processing got started, \\n dag_link:- {dag_link} \\n cc:<@U056ML7E99R>\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "569d5a2d-b7b1-4952-aca3-fc78b210f30e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "52b423b3-a128-4d3c-89c2-85714cca0fce", "metadata": {"tags": []}, "source": ["## Data Prep"]}, {"cell_type": "code", "execution_count": null, "id": "e26e33e2-5098-4314-be1c-7e69d04b0a57", "metadata": {"tags": []}, "outputs": [], "source": ["pb.clone_repo(\"ds-fnv-demand-forecasting\", \"/home/<USER>/repo_folder/\", branch_to_fetch=\"master\")"]}, {"cell_type": "code", "execution_count": null, "id": "8fde34d7-0d53-43ff-b845-80a679401c30", "metadata": {}, "outputs": [], "source": ["os.chdir(\"/home/<USER>/repo_folder/ds-fnv-demand-forecasting/post_processing\")"]}, {"cell_type": "code", "execution_count": null, "id": "0812d635-0258-4571-9e90-b03b8bfe244f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d40b4319-cfed-4439-ad3c-4fb016c6dd3e", "metadata": {}, "outputs": [], "source": ["!pip install --default-timeout=100 -qr requirements.txt"]}, {"cell_type": "code", "execution_count": null, "id": "ed77efad-3bfb-4c03-a4c5-3eef1a0b74fa", "metadata": {}, "outputs": [], "source": ["! python3 post_processing.py --run_date {run_date_input}"]}, {"cell_type": "code", "execution_count": null, "id": "7fd4695a-f991-4836-9ae8-bccf44747b08", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}