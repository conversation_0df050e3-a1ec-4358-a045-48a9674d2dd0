alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 3
dag_name: fnv_forecasting_revamped_pipeline
dag_type: etl
escalation_priority: low
execution_timeout: 4320
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebooks:
- alias: data_prep
  executor_config:
    load_type: ultra-high-cpu
    node_type: od
  name: notebook_data_prep
  parameters:
    run_id: '{{ run_id }}'
    task_instance_key_str: '{{ task_instance_key_str }}'
  retries: 2
  retry_delay_in_seconds: 300
  tag: group_1
- alias: lgbm_inf
  executor_config:
    load_type: ultra-high-cpu
    node_type: od
  name: notebook_lgbm_inf
  parameters:
    run_id: '{{ run_id }}'
    task_instance_key_str: '{{ task_instance_key_str }}'
  retries: 2
  retry_delay_in_seconds: 300
  tag: group_2
- alias: tft_inf
  executor_config:
    load_type: ultra-high-cpu
    node_type: od
  name: notebook_tft_inf
  parameters:
    run_id: '{{ run_id }}'
    task_instance_key_str: '{{ task_instance_key_str }}'
  retries: 2
  retry_delay_in_seconds: 300
  tag: group_2
- alias: push_preds_to_table
  executor_config:
    load_type: high-mem
    node_type: od
  name: notebook_push_preds_to_table
  parameters:
    run_id: '{{ run_id }}'
    task_instance_key_str: '{{ task_instance_key_str }}'
  retries: 2
  retry_delay_in_seconds: 300
  tag: group_3
- alias: post_processing
  executor_config:
    load_type: high-mem
    node_type: od
  name: notebook_post_processing
  parameters:
    run_id: '{{ run_id }}'
    task_instance_key_str: '{{ task_instance_key_str }}'
  retries: 2
  retry_delay_in_seconds: 300
  tag: group_4
owner:
  email: <EMAIL>
  slack_id: U07KL3T5EH2
path: data_science/demand_forecasting/etl/fnv_forecasting_revamped_pipeline
paused: false
pool: data_science_pool
project_name: demand_forecasting
schedule:
  end_date: '2025-09-15T00:00:00'
  interval: 35 21 * * *
  start_date: '2025-06-21T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 8
