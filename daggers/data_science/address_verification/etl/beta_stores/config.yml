alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: beta_stores
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U05K16ULBPH
path: data_science/address_verification/etl/beta_stores
paused: false
pool: data_science_pool
project_name: address_verification
schedule:
  end_date: '2025-09-01T00:00:00'
  interval: 0 4 * * *
  start_date: '2024-11-06T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
