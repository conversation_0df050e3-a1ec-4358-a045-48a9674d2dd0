{"cells": [{"cell_type": "code", "execution_count": null, "id": "8d76b55c-86e9-4987-9562-ac967964967d", "metadata": {}, "outputs": [], "source": ["!pip install papermill"]}, {"cell_type": "code", "execution_count": null, "id": "6d2601ba-b62a-4638-9118-4f22cde3a1d6", "metadata": {}, "outputs": [], "source": ["import os\n", "import pencilbox as pb\n", "import papermill as pm\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "7be49455-6498-4f39-a2c5-17eeb725e8c9", "metadata": {}, "outputs": [], "source": ["pb.clone_repo(\"ds-address-verification\", \"/tmp\")"]}, {"cell_type": "code", "execution_count": null, "id": "710903b4-9977-4cb4-86e4-42fc9c7eded7", "metadata": {}, "outputs": [], "source": ["os.chdir(\"/tmp/ds-address-verification\")"]}, {"cell_type": "code", "execution_count": null, "id": "a7f9ee4f-05d6-44b7-904f-722622b97d77", "metadata": {"tags": []}, "outputs": [], "source": ["!pip install -r requirements.txt"]}, {"cell_type": "code", "execution_count": null, "id": "5c8e81a7-0ffe-4171-aefa-375666016a5f", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["## parameters\n", "PROD = True\n", "IGNORE_POINTER = False\n", "PIPELINE = \"custom_verification_first_order\""]}, {"cell_type": "code", "execution_count": null, "id": "678c32e4-dba5-4a59-8baf-c80890899f0f", "metadata": {}, "outputs": [], "source": ["pm.execute_notebook(\n", "    input_path=\"./verification/custom_verification_first_order.ipynb\",\n", "    output_path=\"./output.ipynb\",\n", "    parameters=dict(PROD=PROD, IGNORE_POINTER=IGNORE_POINTER),\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}