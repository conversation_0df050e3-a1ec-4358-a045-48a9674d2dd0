alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: custom_verification
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: high-mem
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RZ0R1WDU
path: data_science/address_verification/etl/custom_verification
paused: false
project_name: address_verification
schedule:
  end_date: '2025-08-15T00:00:00'
  interval: 0 0 1 1 *
  start_date: '2024-05-15T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 15
pool: data_science_pool
