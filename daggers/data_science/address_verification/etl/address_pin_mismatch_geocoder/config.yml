alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: address_pin_mismatch_geocoder
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: high-cpu
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U083VSKK7KJ
path: data_science/address_verification/etl/address_pin_mismatch_geocoder
paused: false
pool: data_science_pool
project_name: address_verification
schedule:
  end_date: '2025-09-17T00:00:00'
  interval: 0 0 * * *
  start_date: '2025-06-19T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
