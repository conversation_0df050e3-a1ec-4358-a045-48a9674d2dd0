{"cells": [{"cell_type": "code", "execution_count": null, "id": "555724f4-32c4-4de1-b1f3-94acf67eda6d", "metadata": {}, "outputs": [], "source": ["!pip install papermill"]}, {"cell_type": "code", "execution_count": null, "id": "b08facfa-4b5a-434a-a567-1b0185b5918e", "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "import pencilbox as pb\n", "import papermill as pm\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "ab4a44e2-597b-4174-b977-65ee5d56a1ea", "metadata": {}, "outputs": [], "source": ["pb.clone_repo(\"data-science-models\", \"/tmp\")"]}, {"cell_type": "code", "execution_count": null, "id": "dc2cbc26-6624-4ecb-9ca0-c9461e3cc491", "metadata": {}, "outputs": [], "source": ["os.chdir(\"/tmp/data-science-models\")"]}, {"cell_type": "code", "execution_count": null, "id": "18e6514d-1bd5-4bcd-8786-6cf3eb02e253", "metadata": {}, "outputs": [], "source": ["!pip install -r projects/geocoder/requirements"]}, {"cell_type": "code", "execution_count": null, "id": "76395f8c-328f-4b5d-8f10-329a760817ef", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["params = 0"]}, {"cell_type": "code", "execution_count": null, "id": "2d27bb31-6f98-46ea-a188-d499ad172dbc", "metadata": {}, "outputs": [], "source": ["pm.execute_notebook(\n", "    input_path=\"./projects/geocoder/dags/geocoder_daily_inference.ipynb\",\n", "    output_path=\"./output_geocoder_daily_inference.ipynb\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8b332135-6304-4334-b02c-9d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    \"bl_forecasting_process_alerts\",\n", "    \"Daily Inference on orders & addresses of geocoder is complete!\",\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}