alert_configs:
  slack:
  - channel: bl-personalisation-dag-failures
concurrency: 3
dag_name: order_again_v3
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: high-mem
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: data_science
notebooks:
- executor_config:
    load_type: low
    node_type: od
  name: notebook_table
  parameters: null
  retries: 2
  retry_delay_in_seconds: 60
  tag: level1
- executor_config:
    load_type: ultra-high-mem
    node_type: od
  name: notebook_oa
  parameters: null
  retries: 2
  retry_delay_in_seconds: 15
  tag: level2
owner:
  email: <EMAIL>
  slack_id: U03RQJY8HAT
path: data_science/order_again/etl/order_again_v3
paused: false
pool: data_science_pool
project_name: order_again
schedule:
  end_date: '2025-07-31T00:00:00'
  interval: 30 21 * * *
  start_date: '2025-06-05T00:00:00'
schedule_type: fixed
sla: 30 minutes
support_files:
- kafka_utils.py
tags: []
template_name: multi_notebook
version: 1
