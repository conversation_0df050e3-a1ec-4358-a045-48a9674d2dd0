{"cells": [{"cell_type": "code", "execution_count": null, "id": "584ff9f7-b972-4c10-bd5b-c8daba285091", "metadata": {"tags": []}, "outputs": [], "source": ["!pip install -q scipy==1.8.0\n", "!pip install -q pandas==1.5.1"]}, {"cell_type": "code", "execution_count": null, "id": "c593b824-42cd-4ca6-944a-4ee98696d5cc", "metadata": {}, "outputs": [], "source": ["import time\n", "import pytz\n", "import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import os\n", "import math\n", "import sys\n", "from datetime import date, datetime, timedelta\n", "import gc\n", "from tqdm import tqdm\n", "\n", "tqdm.pandas()\n", "pd.options.display.max_colwidth = 1000"]}, {"cell_type": "code", "execution_count": null, "id": "dacb5cfb-48f8-4f85-8ce7-b0f1fd740092", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "9f24c8a0-1ed6-4bf6-9c55-d1b5b2c262b9", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "0860b2e4-c72c-4580-a9a5-e21aa4a02a4d", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_rows\", 500)\n", "pd.set_option(\"display.max_columns\", 500)"]}, {"cell_type": "code", "execution_count": null, "id": "4dba9fb4-b035-4bc8-8552-9bdff6dfc865", "metadata": {}, "outputs": [], "source": ["from kafka_utils import *"]}, {"cell_type": "code", "execution_count": null, "id": "c2a205d9-5bda-4634-937b-d29b53f5da39", "metadata": {}, "outputs": [], "source": ["def from_sheets(sheet_id, sheet_name, max_tries=3):\n", "    for attempt in range(max_tries):\n", "        print(f\"Trying to read attempt: {attempt + 1}...\")\n", "        try:\n", "            return pb.from_sheets(sheet_id, sheet_name)\n", "        except Exception as e:\n", "            print(f\"Error occurred: {str(e)}\")\n", "            if hasattr(e, \"response\") and hasattr(e.response, \"json\"):\n", "                try:\n", "                    exception_code = (\n", "                        e.response.json().get(\"error\", {}).get(\"code\", \"Unknown error code\")\n", "                    )\n", "                    print(f\"API Error Code: {exception_code}\")\n", "                except Exception as inner_e:\n", "                    print(f\"Failed to extract error code: {str(inner_e)}\")\n", "            time.sleep(60)\n", "\n", "\n", "def to_sheets(df, sheet_id, sheet_name, max_tries=3):\n", "    for attempt in range(max_tries):\n", "        print(f\"Trying to read attempt: {attempt + 1}...\")\n", "        try:\n", "            return pb.to_sheets(df, sheet_id, sheet_name)\n", "        except Exception as e:\n", "            print(f\"Error occurred: {str(e)}\")\n", "            if hasattr(e, \"response\") and hasattr(e.response, \"json\"):\n", "                try:\n", "                    exception_code = (\n", "                        e.response.json().get(\"error\", {}).get(\"code\", \"Unknown error code\")\n", "                    )\n", "                    print(f\"API Error Code: {exception_code}\")\n", "                except Exception as inner_e:\n", "                    print(f\"Failed to extract error code: {str(inner_e)}\")\n", "            time.sleep(60)"]}, {"cell_type": "markdown", "id": "a53118d9-0773-4170-beb8-4e2d0dad2108", "metadata": {}, "source": ["## Reading sheets & preprocessing"]}, {"cell_type": "code", "execution_count": null, "id": "62e28858-3763-4bdb-ac96-bec6d96274f9", "metadata": {}, "outputs": [], "source": ["ptype_mappings = from_sheets(\"16b-AbubMta4MjxsrzFkDK_JyIrdRQznobTwrKNwGjck\", \"Master\")\n", "ptype_mappings = ptype_mappings.melt(\n", "    id_vars=[\"Group Name\"],\n", "    value_vars=ptype_mappings.drop(columns=[\"Group Name\", \"master_ptype_id\"]).columns.tolist(),\n", ").reset_index(drop=True)\n", "ptype_mappings.drop(columns=[\"variable\"], inplace=True)\n", "ptype_mappings = ptype_mappings.drop_duplicates()\n", "ptype_mappings = ptype_mappings.dropna(subset=[\"value\"])\n", "ptype_mappings = ptype_mappings[ptype_mappings[\"value\"] != \"\"]\n", "ptype_mappings[\"product_type\"] = ptype_mappings[\"value\"].apply(lambda x: x.split(\"||\")[0].strip())\n", "ptype_mappings[\"product_type_id\"] = (\n", "    ptype_mappings[\"value\"].apply(lambda x: int(x.split(\"||\")[1].strip())).astype(int)\n", ")\n", "ptype_mappings.drop(columns=[\"value\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "bbaa9b0d-f583-43f9-9720-1ea0845846d9", "metadata": {}, "outputs": [], "source": ["## Order again Grouping\n", "df_sheet_mapping = from_sheets(\"1p0N6KkgAQDn8GEi-FzOUBIKgSuMEz9iAWic4rXmVMWg\", \"Updated Prod Sheet\")\n", "df_group_combinations = from_sheets(\n", "    \"1p0N6KkgAQDn8GEi-FzOUBIKgSuMEz9iAWic4rXmVMWg\", \"Updated group_combinations\"\n", ")\n", "sheet_columns = df_sheet_mapping.columns.tolist()\n", "columns_to_consider = [x for x in df_sheet_mapping.columns if \"id_\" in x]\n", "df_sheet_mapping = df_sheet_mapping[df_sheet_mapping[\"discard\"] != \"1\"]\n", "melted_groups = pd.melt(\n", "    df_sheet_mapping, id_vars=[\"group_name\", \"group_id\"], value_vars=columns_to_consider\n", ")\n", "melted_groups = melted_groups[melted_groups[\"value\"] != \"\"]\n", "melted_groups = melted_groups[~melted_groups[\"value\"].isna()]\n", "melted_groups[\"extracted_ptype\"] = melted_groups[\"value\"].apply(lambda x: x.split(\"||\")[1])\n", "melted_groups[\"l1_flag\"] = melted_groups[\"value\"].apply(\n", "    lambda x: 1 if \"(L1)\" in x.split(\"||\")[0] else 0\n", ")\n", "melted_groups = melted_groups[[\"group_name\", \"group_id\", \"extracted_ptype\", \"l1_flag\"]]\n", "melted_groups = melted_groups.merge(df_group_combinations, how=\"left\")\n", "\n", "melted_groups = melted_groups[\n", "    [\"merge_group\", \"merge_group_id\", \"extracted_ptype\", \"l1_flag\"]\n", "].rename(columns={\"merge_group\": \"group_name\", \"merge_group_id\": \"group_id\"})\n", "melted_groups = melted_groups[melted_groups[\"group_id\"] != \"\"]\n", "groups_to_consider = list(\n", "    pd.read_sql_query(\n", "        f\"\"\"select distinct cast(group_id as int) as group_id\n", "                     from dse_db.frequently_bought_groups_meta where is_enabled = 1\"\"\",\n", "        pb.get_connection(\"[Warehouse] Trino\"),\n", "    ).group_id.unique()\n", ")\n", "melted_groups = melted_groups[melted_groups.group_id.astype(int).isin(groups_to_consider)]"]}, {"cell_type": "code", "execution_count": null, "id": "4c8c4fcb-0f27-4b8d-83da-d915c5b79ee6", "metadata": {}, "outputs": [], "source": ["blacklist = from_sheets(\"17Q4P7HGhYLMnW4AqRWL4Jh_a5nT5DCeuK176CM2jgoE\", \"Blacklist_v2\")\n", "blacklist = blacklist[blacklist[\"widget\"].isin([\"Global\", \"Order Again\"])]\n", "\n", "\n", "def checknull(st):\n", "    if len(st) == 0:\n", "        st = st + \"0\"\n", "    return st\n", "\n", "\n", "b_l0 = checknull(\",\".join([x for x in list(blacklist[\"l0\"].values) if x != \"\"]))\n", "b_l1 = checknull(\",\".join([x for x in list(blacklist[\"l1\"].values) if x != \"\"]))\n", "b_pids = checknull(\",\".join([x for x in list(blacklist[\"pid\"].values) if x != \"\"]))\n", "b_ptype_ids = checknull(\",\".join([x for x in list(blacklist[\"ptype\"].values) if x != \"\"]))\n", "print(b_l0, b_l1, b_pids, b_ptype_ids)"]}, {"cell_type": "code", "execution_count": null, "id": "6ec675d2-8833-4df4-b39a-170c8c4a1d8d", "metadata": {}, "outputs": [], "source": ["# ## favourities group\n", "# group_intelligence = from_sheets(\n", "#     \"1-TAGan8KKBxBuR_lkDXZ6xxAzRqAoPpj2uCNnc_5Efw\", \"ptype_intelligence\"\n", "# )\n", "# fav_ptypes = group_intelligence[group_intelligence[\"favourite_flag\"] == \"1\"].product_type.unique()\n", "\n", "# len(fav_ptypes)\n", "\n", "# non_staple_groc_flag = from_sheets(\"1eizAWo3PyvgiMdfnCA_BYEXkxgPWy7Rn4W70E_IYDzA\", \"Sheet1\")\n", "# non_staple_groc_flag = non_staple_groc_flag[\n", "#     [\"l1_category_id\", \"l1_category\", \"non_staple_grocery\"]\n", "# ].drop_duplicates()\n", "# non_staple_groc_flag = non_staple_groc_flag[~non_staple_groc_flag[\"non_staple_grocery\"].isna()]\n", "\n", "# non_staple_groc_flag[\"l1_category_id\"] = non_staple_groc_flag[\"l1_category_id\"].astype(int)\n", "# non_staple_groc_flag[\"non_staple_grocery\"] = non_staple_groc_flag[\"non_staple_grocery\"].astype(int)\n", "# non_staple_groc_flag = non_staple_groc_flag[non_staple_groc_flag[\"non_staple_grocery\"] == 1]\n", "# non_staple_groc_flag = list(non_staple_groc_flag[\"l1_category_id\"].unique())\n", "\n", "# exclusion_l1s = []\n", "# inclusion_l1s = [1503]\n", "# non_staple_groc_flag = list(set(non_staple_groc_flag + inclusion_l1s) - set(exclusion_l1s))"]}, {"cell_type": "code", "execution_count": null, "id": "7eefcecf-7280-4b74-a559-9e8801b56091", "metadata": {}, "outputs": [], "source": ["df_product_info = pd.read_sql_query(\n", "    f\"\"\"\n", "    select product_id,\n", "           product_name,\n", "           product_type,\n", "           product_type_id,\n", "           l0_category,\n", "           l0_category_id,\n", "           l1_category,\n", "           l1_category_id\n", "    from dwh.dim_product \n", "    where is_current and is_product_enabled\"\"\",\n", "    pb.get_connection(\"[Warehouse] Trino\"),\n", ")\n", "print(df_product_info.shape)\n", "df_product_info = df_product_info.merge(\n", "    ptype_mappings[[\"product_type_id\", \"Group Name\"]],\n", "    on=[\"product_type_id\"],\n", "    how=\"left\",\n", ")\n", "df_product_info[\"Group Name\"] = np.where(\n", "    df_product_info[\"Group Name\"].isna(),\n", "    df_product_info[\"product_type\"],\n", "    df_product_info[\"Group Name\"],\n", ")\n", "ptypes = melted_groups.query(\"l1_flag==0\")\n", "l1 = melted_groups.query(\"l1_flag==1\")\n", "l1[\"extracted_ptype\"] = l1[\"extracted_ptype\"].astype(int)\n", "ptypes[\"extracted_ptype\"] = ptypes[\"extracted_ptype\"].astype(int)\n", "ptypes = df_product_info.merge(ptypes, right_on=\"extracted_ptype\", left_on=\"product_type_id\")\n", "l1 = df_product_info.merge(l1, right_on=\"extracted_ptype\", left_on=\"l1_category_id\")\n", "final_check_df = pd.DataFrame()\n", "final_check_df = final_check_df.append(ptypes)\n", "final_check_df = final_check_df.append(l1)\n", "final_check_df = final_check_df.drop_duplicates(subset=[\"product_id\", \"group_id\"], keep=\"first\")\n", "groups = final_check_df[[\"product_id\", \"group_name\", \"group_id\"]].drop_duplicates()\n", "groups = groups.drop_duplicates(subset=[\"product_id\"], keep=\"first\")\n", "\n", "assort_freq = pd.read_sql_query(\n", "    f\"\"\"select product_id, frequently_bought_pid, \n", "                                    frequently_bought_ptype, cycle_days\n", "                                    from consumer_intelligence_etls.purchase_cycle_days\n", "                                    where base_city_id = 0 and city_id = 0\"\"\",\n", "    pb.get_connection(\"[Warehouse] Trino\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7e0295a1-1eb5-4feb-ac5a-32203065a754", "metadata": {}, "outputs": [], "source": ["def query_with_retries(query, con=pb.get_connection(\"[Warehouse] Trino\"), retries=3):\n", "    count = 1\n", "    while count <= retries:\n", "        try:\n", "            df = pd.read_sql_query(query, con)\n", "            break\n", "        except Exception as e:\n", "            print(e)\n", "            count += 1\n", "    return df"]}, {"cell_type": "markdown", "id": "a3be7d60-c231-451b-aa6f-7ab833216587", "metadata": {}, "source": ["## Querying past orders data"]}, {"cell_type": "code", "execution_count": null, "id": "7e092cfc-6f0a-4efa-92f3-07b7dd6ccf67", "metadata": {}, "outputs": [], "source": ["def get_user_data(\n", "    day,\n", "    mods,\n", "    assort_freq=assort_freq,\n", "    groups=groups,\n", "    df_product_info=df_product_info,\n", "    b_l0=b_l0,\n", "    b_l1=b_l1,\n", "    b_pids=b_pids,\n", "    b_ptype_ids=b_ptype_ids,\n", "):\n", "    st = datetime.now()\n", "    user_data = query_with_retries(\n", "        f\"\"\"\n", "    with user_list as (select distinct dim_customer_key\n", "        from dwh.fact_sales_order_details\n", "        where ((order_create_dt_ist = current_date - interval '{day}' day)\n", "            or (order_archived_dt_ist = current_date - interval '{day}' day))\n", "        and is_internal_order = False and dim_customer_key %% 10 in {*mods,}),\n", "        \n", "    transacting_users as (\n", "    select dim_customer_key,\n", "           max(cart_rank_bucket) as cust_max_cr\n", "    from ds_etls.user_order_history\n", "    where last_bought_dt between current_date - interval '400' day and current_date - interval '1' day \n", "    and dim_customer_key is not null and dim_customer_key in (select * from user_list)\n", "    group by 1)\n", "    \n", "    select\n", "        fs.dim_customer_key,\n", "        dp.product_type,\n", "        fs.product_id,\n", "        max(fs.last_bought_dt) as last_bought_date,\n", "        sum(order_count) as order_count,\n", "        sum(case when cust_max_cr = 0 then 0.1 else 0.1 + fs.order_count * power(0.95,(t.cust_max_cr - fs.cart_rank_bucket)*100.0/t.cust_max_cr) end) as decay_score\n", "    from ds_etls.user_order_history fs\n", "    join transacting_users t on t.dim_customer_key = fs.dim_customer_key\n", "    join dwh.dim_product dp on dp.product_id = fs.product_id and dp.is_current and dp.is_product_enabled\n", "    and dp.l1_category_id not in ({b_l1}) and dp.l0_category_id not in ({b_l0})\n", "    and dp.product_type_id not in ({b_ptype_ids}) and dp.product_id not in ({b_pids})\n", "    and fs.dim_customer_key is not null\n", "    group by 1,2,3\"\"\"\n", "    )\n", "    print(user_data.dim_customer_key.nunique())\n", "\n", "    user_data = user_data.merge(\n", "        df_product_info[\n", "            [\n", "                \"product_id\",\n", "                \"product_type_id\",\n", "                \"l1_category\",\n", "                \"l1_category_id\",\n", "                \"l0_category\",\n", "                \"l0_category_id\",\n", "                \"Group Name\",\n", "            ]\n", "        ],\n", "        on=[\"product_id\"],\n", "    )\n", "    user_data = user_data.merge(groups, on=[\"product_id\"], how=\"left\")\n", "    user_data = user_data.merge(\n", "        assort_freq[\n", "            [\n", "                \"product_id\",\n", "                \"frequently_bought_pid\",\n", "                \"frequently_bought_ptype\",\n", "                \"cycle_days\",\n", "            ]\n", "        ],\n", "        on=[\"product_id\"],\n", "        how=\"left\",\n", "    )\n", "    user_groups = (\n", "        user_data.groupby([\"dim_customer_key\", \"group_name\"])\n", "        .agg({\"product_id\": \"nunique\", \"decay_score\": sum})\n", "        .reset_index()\n", "        .sort_values(by=[\"dim_customer_key\", \"decay_score\"], ascending=[True, False])\n", "    )\n", "    user_groups = user_groups[user_groups[\"product_id\"] >= 2]\n", "    user_groups = user_groups.rename(columns={\"decay_score\": \"group_score\"})\n", "    user_groups[\"group_rank\"] = user_groups.groupby([\"dim_customer_key\"]).group_score.rank(\n", "        method=\"first\", ascending=False\n", "    )\n", "    user_groups[\"group_rank\"] = user_groups[\"group_rank\"].astype(int)\n", "    user_data = user_data.merge(\n", "        user_groups[[\"dim_customer_key\", \"group_name\", \"group_score\", \"group_rank\"]],\n", "        how=\"left\",\n", "    )\n", "    del user_groups\n", "    gc.collect()\n", "    print(\"Runtime: \", (datetime.now() - st).seconds / 60)\n", "    return user_data"]}, {"cell_type": "markdown", "id": "12ea59f3-bff6-4d44-a168-277564f56cfa", "metadata": {}, "source": ["## defining pid level flags"]}, {"cell_type": "code", "execution_count": null, "id": "03eeda50-74a7-4186-863e-5cc058fb2de7", "metadata": {}, "outputs": [], "source": ["def defining_flags(user_data, min_group_count=3, group_count=12):\n", "    st = datetime.now()\n", "    ## frequently bought\n", "    user_data[\"frequently_bought\"] = np.where(user_data[\"order_count\"] >= 3, 1, 0)\n", "    ud = user_data[user_data[\"frequently_bought\"] == 1]\n", "    ud = ud[[\"dim_customer_key\", \"product_type\", \"product_type_id\"]].drop_duplicates()\n", "    ud[\"fb_ptype_flag\"] = 1\n", "    user_data = user_data.merge(ud, how=\"left\")\n", "    user_data[\"fb_ptype_flag\"] = user_data[\"fb_ptype_flag\"].fillna(0)\n", "    user_data[\"frequently_bought\"] = (\n", "        user_data[\"fb_ptype_flag\"].astype(int) | user_data[\"frequently_bought\"]\n", "    )\n", "\n", "    ## recently bought\n", "    user_data[\"filter_date\"] = date.today() - timed<PERSON>ta(days=90)\n", "    user_data[\"recently_bought\"] = np.where(\n", "        (user_data[\"filter_date\"] <= pd.to_datetime(user_data[\"last_bought_date\"]))\n", "        & (user_data[\"order_count\"] < 3)\n", "        & (\n", "            (user_data[\"frequently_bought_ptype\"] == \"yes\")\n", "            | (user_data[\"frequently_bought_pid\"] == \"yes\")\n", "        ),\n", "        1,\n", "        0,\n", "    )\n", "\n", "    ## favourites group\n", "    user_data[\"fav_pid\"] = np.where(\n", "        (user_data[\"order_count\"] >= 3)\n", "        & (user_data[\"filter_date\"] <= pd.to_datetime(user_data[\"last_bought_date\"])),\n", "        1,\n", "        0,\n", "    )\n", "\n", "    u_fav = user_data.groupby([\"dim_customer_key\"]).fav_pid.sum().reset_index()\n", "    u_fav = u_fav.rename(columns={\"fav_pid\": \"favourite_pids_count\"})\n", "    u_fav[\"show_favourties\"] = np.where(u_fav[\"favourite_pids_count\"] >= 2, 1, 0)\n", "    user_data = user_data.merge(\n", "        u_fav[[\"dim_customer_key\", \"show_favourties\"]], on=[\"dim_customer_key\"]\n", "    )\n", "    del u_fav\n", "    gc.collect()\n", "    user_data[\"fav_pid\"] = np.where(user_data[\"show_favourties\"] == 1, user_data[\"fav_pid\"], 0)\n", "    ## more that you ordered\n", "    user_data[\"more_that_you_ordered\"] = np.where(\n", "        ~((user_data[\"frequently_bought\"] == 1) | (user_data[\"recently_bought\"] == 1)),\n", "        1,\n", "        0,\n", "    )\n", "\n", "    user_data[\"max_group_count\"] = np.where(\n", "        user_data[\"show_favourties\"] == 1, group_count - 1, group_count\n", "    )\n", "\n", "    ugc = user_data.groupby([\"dim_customer_key\"]).group_rank.nunique().reset_index()\n", "    ugc = ugc.rename(columns={\"group_rank\": \"group_count\"})\n", "\n", "    user_data = user_data.merge(\n", "        ugc[[\"dim_customer_key\", \"group_count\"]], on=[\"dim_customer_key\"], how=\"left\"\n", "    )\n", "    user_data[\"group_count\"] = user_data[\"group_count\"].fillna(0)\n", "\n", "    user_data[\"group_pid\"] = np.where(\n", "        (~user_data.group_score.isna())\n", "        & (user_data[\"group_rank\"] <= user_data[\"max_group_count\"])\n", "        & (user_data[\"group_count\"] >= min_group_count),\n", "        1,\n", "        0,\n", "    )\n", "\n", "    user_data[\"non_grouped_fb\"] = np.where(\n", "        (user_data[\"frequently_bought\"] - user_data[\"group_pid\"]) < 0,\n", "        0,\n", "        (user_data[\"frequently_bought\"] - user_data[\"group_pid\"]),\n", "    )\n", "    user_data[\"non_grouped_rb\"] = np.where(\n", "        (user_data[\"recently_bought\"] - user_data[\"group_pid\"]) < 0,\n", "        0,\n", "        (user_data[\"recently_bought\"] - user_data[\"group_pid\"]),\n", "    )\n", "    user_data[\"non_grouped_mtyo\"] = np.where(\n", "        (user_data[\"more_that_you_ordered\"] - user_data[\"group_pid\"]) < 0,\n", "        0,\n", "        (user_data[\"more_that_you_ordered\"] - user_data[\"group_pid\"]),\n", "    )\n", "    print(\"Runtime for querying order data: \", (datetime.now() - st).seconds / 60)\n", "    return user_data"]}, {"cell_type": "markdown", "id": "6989b2a8-9efb-4d95-ba20-781ebd7d7a22", "metadata": {}, "source": ["## Assigntment logic & Conditions"]}, {"cell_type": "code", "execution_count": null, "id": "d5f47fb0-3493-4fae-8a6d-60a5a12c59a8", "metadata": {}, "outputs": [], "source": ["def get_user_stats(user_data):\n", "    st = datetime.now()\n", "    user_stats = (\n", "        user_data.groupby([\"dim_customer_key\"])\n", "        .agg(\n", "            {\n", "                \"product_id\": \"nunique\",\n", "                \"frequently_bought\": sum,\n", "                \"group_rank\": \"nunique\",\n", "                \"recently_bought\": sum,\n", "                \"more_that_you_ordered\": sum,\n", "                \"group_pid\": sum,\n", "                \"non_grouped_fb\": sum,\n", "                \"non_grouped_rb\": sum,\n", "                \"non_grouped_mtyo\": sum,\n", "                \"fav_pid\": sum,\n", "            }\n", "        )\n", "        .reset_index()\n", "    )\n", "\n", "    user_stats[\"group_rank\"] = np.where(\n", "        user_stats[\"fav_pid\"] >= 2,\n", "        np.where(user_stats[\"group_rank\"] > 11, 12, user_stats[\"group_rank\"] + 1),\n", "        np.where(user_stats[\"group_rank\"] > 12, 12, user_stats[\"group_rank\"]),\n", "    )\n", "\n", "    user_stats[\"show_groups\"] = np.where(\n", "        ((user_stats[\"group_pid\"] >= 2) | (user_stats[\"fav_pid\"] >= 2)), 1, 0\n", "    )\n", "\n", "    user_stats[\"frequently_bought\"] = np.where(\n", "        user_stats[\"show_groups\"] > 0,\n", "        user_stats[\"non_grouped_fb\"],\n", "        user_stats[\"frequently_bought\"],\n", "    )\n", "    user_stats[\"recently_bought\"] = np.where(\n", "        user_stats[\"show_groups\"] > 0,\n", "        user_stats[\"non_grouped_rb\"],\n", "        user_stats[\"recently_bought\"],\n", "    )\n", "    user_stats[\"more_that_you_ordered\"] = np.where(\n", "        user_stats[\"show_groups\"] > 0,\n", "        user_stats[\"non_grouped_mtyo\"],\n", "        user_stats[\"more_that_you_ordered\"],\n", "    )\n", "    user_stats[\"your_collections\"] = user_stats[\"group_pid\"]\n", "    user_stats[\"oa_collection_count\"] = np.where(\n", "        user_stats[\"show_groups\"] == 1, user_stats[\"group_rank\"], 0\n", "    )\n", "\n", "    user_stats[\"show_frequently_bought\"] = np.where(user_stats[\"frequently_bought\"] >= 0, 1, 0)\n", "    # user_stats[\"frequently_bought\"] = np.where(user_stats[\"frequently_bought\"] >= fb_limit,user_stats[\"frequently_bought\"],0)\n", "    user_stats[\"show_recently_bought\"] = np.where(user_stats[\"recently_bought\"] >= 0, 1, 0)\n", "    # user_stats[\"recently_bought\"] = np.where(user_stats[\"recently_bought\"] >= rb_limit,user_stats[\"recently_bought\"],0)\n", "\n", "    user_stats[\"show_more_that_you_ordered\"] = np.where(\n", "        user_stats[\"show_recently_bought\"] + user_stats[\"show_frequently_bought\"] == 2,\n", "        1,\n", "        0,\n", "    )\n", "    user_stats[\"more_that_you_ordered\"] = np.where(\n", "        user_stats[\"show_more_that_you_ordered\"] == 1,\n", "        user_stats[\"more_that_you_ordered\"],\n", "        0,\n", "    )\n", "    user_stats[\"bestsellers\"] = np.where(\n", "        user_stats[\"more_that_you_ordered\"]\n", "        + user_stats[\"recently_bought\"]\n", "        + user_stats[\"frequently_bought\"]\n", "        <= 27,\n", "        200,\n", "        0,\n", "    )\n", "\n", "    user_stats = user_stats[\n", "        [\n", "            \"dim_customer_key\",\n", "            \"product_id\",\n", "            \"your_collections\",\n", "            \"oa_collection_count\",\n", "            \"frequently_bought\",\n", "            \"recently_bought\",\n", "            \"more_that_you_ordered\",\n", "            \"bestsellers\",\n", "        ]\n", "    ].rename(columns={\"dim_customer_key\": \"user_id\"})\n", "\n", "    user_data = user_data.rename(\n", "        columns={\n", "            \"dim_customer_key\": \"user_id\",\n", "            \"frequently_bought\": \"frequently_bought_flag\",\n", "            \"recently_bought\": \"recently_bought_flag\",\n", "            \"more_that_you_ordered\": \"more_that_you_ordered_flag\",\n", "        }\n", "    ).merge(\n", "        user_stats[\n", "            [\n", "                \"user_id\",\n", "                \"your_collections\",\n", "                \"oa_collection_count\",\n", "                \"frequently_bought\",\n", "                \"recently_bought\",\n", "                \"more_that_you_ordered\",\n", "                \"bestsellers\",\n", "            ]\n", "        ],\n", "        on=[\"user_id\"],\n", "    )\n", "\n", "    user_stats[\"monthly_purchases\"] = 0\n", "\n", "    cols = [\n", "        \"your_collections\",\n", "        \"oa_collection_count\",\n", "        \"frequently_bought\",\n", "        \"recently_bought\",\n", "        \"monthly_purchases\",\n", "        \"more_that_you_ordered\",\n", "        \"bestsellers\",\n", "        \"prev_bought_item_count\",\n", "        \"ordered_in_the_last_month\",\n", "        \"you_might_be_running_out_of\",\n", "    ]\n", "\n", "    user_stats[\"prev_bought_item_count\"] = (\n", "        user_stats[\"frequently_bought\"]\n", "        + user_stats[\"recently_bought\"]\n", "        + user_stats[\"more_that_you_ordered\"]\n", "    )\n", "    user_stats[\"ordered_in_the_last_month\"] = 200\n", "    user_stats[\"you_might_be_running_out_of\"] = 200\n", "\n", "    user_stats[\"product_id\"] = user_stats.apply(\n", "        lambda row: [\n", "            {\n", "                \"asset\": collection,\n", "                \"number_of_pids\": row[collection],\n", "            }\n", "            for collection in cols\n", "        ],\n", "        axis=1,\n", "    )\n", "\n", "    user_stats = user_stats[[\"user_id\", \"product_id\"]]\n", "    user_stats[\"section\"] = \"statistics\"\n", "    print(\"Runtime for getting user stats: \", (datetime.now() - st).seconds / 60)\n", "    return user_stats, user_data"]}, {"cell_type": "markdown", "id": "4ec97086-7d5b-40b4-9916-4a9536ec98b5", "metadata": {}, "source": ["## Your Collections"]}, {"cell_type": "code", "execution_count": null, "id": "2ca8ad9f-9d86-433f-beb7-7825d357305c", "metadata": {}, "outputs": [], "source": ["def get_your_collections(user_data, group_count=12):\n", "    st = datetime.now()\n", "    ## favourites\n", "    favs = user_data[user_data[\"fav_pid\"] == 1][\n", "        [\"user_id\", \"decay_score\", \"l0_category\", \"product_id\"]\n", "    ]\n", "    favs = favs.sort_values(by=[\"user_id\", \"decay_score\"], ascending=[True, False])\n", "    favs[\"rank\"] = (\n", "        favs.groupby([\"user_id\", \"l0_category\"])[\"decay_score\"]\n", "        .rank(ascending=False, method=\"first\")\n", "        .astype(int)\n", "    )\n", "    favs = favs.sort_values(by=[\"user_id\", \"l0_category\", \"rank\"])\n", "    favs = favs.sort_values(by=[\"rank\", \"decay_score\"], ascending=[True, False])\n", "    favs[\"collection\"] = \"Favourites\"\n", "    favs[\"group_id\"] = \"99999\"\n", "    favs = (\n", "        favs.groupby([\"user_id\", \"collection\", \"group_id\"])\n", "        .agg({\"product_id\": list})\n", "        .reset_index()\n", "        .rename(columns={\"product_id\": \"product_ids\"})\n", "    )\n", "    favs = (\n", "        favs.groupby(\"user_id\")\n", "        .agg({\"collection\": list, \"group_id\": list, \"product_ids\": list})\n", "        .reset_index()\n", "    )\n", "    favs[\"product_id\"] = favs.apply(\n", "        lambda row: [\n", "            {\"collection\": collection, \"group_id\": group_id, \"product_ids\": product_ids}\n", "            for collection, group_id, product_ids in zip(\n", "                row[\"collection\"], row[\"group_id\"], row[\"product_ids\"]\n", "            )\n", "        ],\n", "        axis=1,\n", "    )\n", "    favs = favs[[\"user_id\", \"product_id\"]]\n", "\n", "    ## collections\n", "    collections = user_data[(~user_data[\"group_score\"].isna()) & (user_data[\"group_pid\"] == 1)][\n", "        [\n", "            \"user_id\",\n", "            \"product_id\",\n", "            \"group_name\",\n", "            \"group_id\",\n", "            \"decay_score\",\n", "            \"Group Name\",\n", "            \"group_score\",\n", "            \"max_group_count\",\n", "        ]\n", "    ]\n", "    collections = collections.rename(columns={\"group_name\": \"collection\"}).sort_values(\n", "        by=[\"user_id\", \"group_score\", \"decay_score\"],\n", "        ascending=[True, False, False],\n", "    )\n", "    col = (\n", "        collections.groupby([\"user_id\", \"collection\", \"group_id\", \"group_score\", \"max_group_count\"])\n", "        .agg({\"product_id\": list})\n", "        .reset_index()\n", "        .rename(columns={\"product_id\": \"product_ids\"})\n", "    )\n", "    col = col.sort_values(by=[\"user_id\", \"group_score\"], ascending=[True, False])\n", "    col[\"group_id\"] = col[\"group_id\"].astype(int).astype(str)\n", "\n", "    col1 = (\n", "        col.groupby(\"user_id\")\n", "        .apply(\n", "            lambda x: x.head(5) if x[\"max_group_count\"].iloc[0] == group_count - 1 else x.head(6)\n", "        )\n", "        .reset_index(drop=True)\n", "    )\n", "    col2 = (\n", "        col.groupby(\"user_id\")\n", "        .apply(\n", "            lambda x: x.iloc[5:] if x[\"max_group_count\"].iloc[0] == group_count - 1 else x.iloc[6:]\n", "        )\n", "        .reset_index(drop=True)\n", "    )\n", "\n", "    col1 = (\n", "        col1.groupby(\"user_id\")\n", "        .agg({\"collection\": list, \"group_id\": list, \"product_ids\": list})\n", "        .reset_index()\n", "    )\n", "    col1[\"product_id\"] = col1.apply(\n", "        lambda row: [\n", "            {\"collection\": collection, \"group_id\": group_id, \"product_ids\": product_ids}\n", "            for collection, group_id, product_ids in zip(\n", "                row[\"collection\"], row[\"group_id\"], row[\"product_ids\"]\n", "            )\n", "        ],\n", "        axis=1,\n", "    )\n", "    col1.drop(columns=[\"collection\", \"product_ids\", \"group_id\"], inplace=True)\n", "\n", "    col2 = (\n", "        col2.groupby(\"user_id\")\n", "        .agg({\"collection\": list, \"group_id\": list, \"product_ids\": list})\n", "        .reset_index()\n", "    )\n", "    col2[\"product_id\"] = col2.apply(\n", "        lambda row: [\n", "            {\"collection\": collection, \"group_id\": group_id, \"product_ids\": product_ids}\n", "            for collection, group_id, product_ids in zip(\n", "                row[\"collection\"], row[\"group_id\"], row[\"product_ids\"]\n", "            )\n", "        ],\n", "        axis=1,\n", "    )\n", "    col2.drop(columns=[\"collection\", \"product_ids\", \"group_id\"], inplace=True)\n", "\n", "    collections = col1[[\"user_id\", \"product_id\"]]\n", "    collections = pd.concat([favs, collections], axis=0)\n", "    collections[\"section\"] = \"your_collections\"\n", "    collections = collections.groupby([\"user_id\", \"section\"], as_index=False).agg(\n", "        {\"product_id\": \"sum\"}\n", "    )\n", "\n", "    col2 = col2[[\"user_id\", \"product_id\"]]\n", "    col2[\"section\"] = \"your_collections_v2\"\n", "    col2 = col2.groupby([\"user_id\", \"section\"], as_index=False).agg({\"product_id\": \"sum\"})\n", "    print(\"Runtime for getting collections: \", (datetime.now() - st).seconds / 60)\n", "    return collections, col2"]}, {"cell_type": "markdown", "id": "3a812a64-0928-4554-9d9b-71121602a07a", "metadata": {"tags": []}, "source": ["## frequently bought"]}, {"cell_type": "code", "execution_count": null, "id": "d72a5207-f2fa-4b09-9351-d0b73c956c92", "metadata": {}, "outputs": [], "source": ["def get_frequently_bought(user_data):\n", "    st = datetime.now()\n", "    frequently_bought = user_data[\n", "        (user_data[\"frequently_bought_flag\"] == 1)\n", "        & (user_data[\"group_pid\"] != 1)\n", "        & (user_data[\"frequently_bought\"] > 0)\n", "    ]\n", "\n", "    frequently_bought = frequently_bought.merge(df_product_info, on=[\"product_id\", \"product_type\"])\n", "    frequently_bought = frequently_bought[\n", "        [\n", "            \"user_id\",\n", "            \"product_id\",\n", "            \"product_type\",\n", "            \"last_bought_date\",\n", "            \"decay_score\",\n", "            \"cycle_days\",\n", "            \"order_count\",\n", "        ]\n", "    ]\n", "\n", "    fb1 = frequently_bought.groupby([\"user_id\", \"product_type\"]).decay_score.max().reset_index()\n", "\n", "    frequently_bought = frequently_bought[\n", "        [\n", "            \"user_id\",\n", "            \"product_id\",\n", "            \"product_type\",\n", "            \"cycle_days\",\n", "            \"order_count\",\n", "            \"last_bought_date\",\n", "        ]\n", "    ].merge(fb1)\n", "\n", "    frequently_bought = frequently_bought.sort_values(\n", "        by=[\"decay_score\", \"last_bought_date\"], ascending=[False, False]\n", "    )\n", "\n", "    frequently_bought[\"product_id\"] = frequently_bought.apply(\n", "        lambda row: {\n", "            \"product_id\": row[\"product_id\"],\n", "            \"last_bought\": row[\"last_bought_date\"],\n", "            \"decay_score\": row[\"decay_score\"],\n", "        },\n", "        axis=1,\n", "    )\n", "    frequently_bought.drop(columns=[\"last_bought_date\", \"decay_score\"], inplace=True)\n", "    frequently_bought = (\n", "        frequently_bought.groupby([\"user_id\"])[\"product_id\"]\n", "        .apply(list)\n", "        .reset_index()\n", "        .rename(columns={\"dim_customer_key\": \"user_id\"})\n", "    )\n", "    frequently_bought[\"section\"] = \"frequently_bought\"\n", "    del fb1\n", "    gc.collect()\n", "    print(\"Runtime for frequently bought: \", (datetime.now() - st).seconds / 60)\n", "    return frequently_bought"]}, {"cell_type": "markdown", "id": "b9e17239-6563-46c5-8590-effa411382b4", "metadata": {}, "source": ["## recently bought"]}, {"cell_type": "code", "execution_count": null, "id": "c10b3dfa-85da-45ec-8930-db9c3d731ea9", "metadata": {}, "outputs": [], "source": ["def get_recently_bought(user_data):\n", "    st = datetime.now()\n", "    recently_bought = user_data[\n", "        (user_data[\"recently_bought_flag\"] == 1)\n", "        & (user_data[\"group_pid\"] != 1)\n", "        & (user_data[\"recently_bought\"] > 0)\n", "    ]\n", "\n", "    rb1 = recently_bought.groupby([\"user_id\", \"product_type\"]).decay_score.max().reset_index()\n", "\n", "    recently_bought = recently_bought.merge(rb1, how=\"left\")\n", "\n", "    recently_bought = recently_bought.merge(df_product_info, on=[\"product_id\", \"product_type\"])\n", "    recently_bought = recently_bought.sort_values(\n", "        by=[\"user_id\", \"decay_score\", \"last_bought_date\"],\n", "        ascending=[True, False, False],\n", "    )[[\"user_id\", \"product_id\", \"decay_score\", \"last_bought_date\"]]\n", "\n", "    recently_bought[\"product_id\"] = recently_bought.apply(\n", "        lambda row: {\n", "            \"product_id\": row[\"product_id\"],\n", "            \"last_bought\": row[\"last_bought_date\"],\n", "            \"decay_score\": row[\"decay_score\"],\n", "        },\n", "        axis=1,\n", "    )\n", "    recently_bought.drop(columns=[\"last_bought_date\", \"decay_score\"], inplace=True)\n", "    recently_bought = (\n", "        recently_bought.groupby([\"user_id\"])[\"product_id\"]\n", "        .apply(list)\n", "        .reset_index()\n", "        .rename(columns={\"dim_customer_key\": \"user_id\"})\n", "    )\n", "    recently_bought[\"section\"] = \"recently_bought\"\n", "    del rb1\n", "    gc.collect()\n", "    print(\"Runtime for recently bought: \", (datetime.now() - st).seconds / 60)\n", "    return recently_bought"]}, {"cell_type": "markdown", "id": "0aab5f07-5421-4367-9806-0be3acb1df82", "metadata": {}, "source": ["## Previous Buys HFS"]}, {"cell_type": "code", "execution_count": null, "id": "2fdc15e2-3cd2-4848-b283-32024e8019e4", "metadata": {}, "outputs": [], "source": ["def get_hfs_data(day, mods, b_l0=b_l0, b_l1=b_l1, b_ptype_ids=b_ptype_ids, b_pids=b_pids):\n", "    st = datetime.now()\n", "    hfs_cat = from_sheets(\"1AExg8mqg9PaqpPOSl8h7WfuGjbPRA88SWDh6AY6nQ5w\", \"Sheet1\")\n", "    hfs_cat[[\"l0_id\", \"rank\"]] = hfs_cat[[\"l0_id\", \"rank\"]].astype(int)\n", "    pb_hfs = query_with_retries(\n", "        f\"\"\"with user_list as (select distinct dim_customer_key\n", "        from dwh.fact_sales_order_details\n", "        where ((order_create_dt_ist = current_date - interval '{day}' day)\n", "            or (order_archived_dt_ist = current_date - interval '{day}' day))\n", "        and is_internal_order = False and dim_customer_key %% 10 in {*mods,})\n", "        \n", "    select dim_customer_key user_id,\n", "           dp.l0_category_id l0_id,\n", "           dp.l1_category,\n", "           fs.product_id,\n", "           max(last_bought_dt) last_bought_date,\n", "           sum(order_count) cart_count\n", "    from ds_etls.user_order_history fs\n", "    join dwh.dim_product dp on dp.product_id = fs.product_id and dp.is_current and dp.is_product_enabled\n", "    and dp.l0_category_id in {*hfs_cat['l0_id'],}\n", "    and fs.dim_customer_key in (select dim_customer_key from user_list)\n", "    and fs.dim_customer_key is not null\n", "    and fs.last_bought_dt between current_date - interval '180' day and current_date - interval '1' day \n", "    and dp.l0_category_id not in ({b_l0})\n", "    and dp.l1_category_id not in ({b_l1})\n", "    and dp.product_type_id not in ({b_ptype_ids})\n", "    and dp.product_id not in ({b_pids})\n", "    group by 1,2,3,4\"\"\"\n", "    )\n", "    pb_hfs = pb_hfs.merge(hfs_cat, on=[\"l0_id\"])\n", "    pb_hfs[\"last_bought_date1\"] = pd.to_datetime(pb_hfs[\"last_bought_date\"])\n", "    pb_hfs = pb_hfs.sort_values(\n", "        [\"user_id\", \"rank\", \"cart_count\", \"last_bought_date1\"],\n", "        ascending=[True, True, False, False],\n", "    )\n", "    pb_hfs[\"l1_rank\"] = pb_hfs.groupby([\"user_id\", \"rank\", \"l1_category\"]).cumcount() + 1\n", "\n", "    pb_hfs = pb_hfs.sort_values(\n", "        [\"user_id\", \"rank\", \"l1_rank\", \"cart_count\", \"last_bought_date1\"],\n", "        ascending=[True, True, True, False, False],\n", "    )\n", "\n", "    pb_hfs[\"pid_rank\"] = pb_hfs.groupby([\"user_id\"]).cumcount() + 1\n", "    pb_hfs = pb_hfs[pb_hfs[\"pid_rank\"] <= 100]\n", "    pb_hfs[\"product_id\"] = pb_hfs.apply(\n", "        lambda row: {\n", "            \"product_id\": row[\"product_id\"],\n", "            \"last_bought\": row[\"last_bought_date\"],\n", "        },\n", "        axis=1,\n", "    )\n", "    pb_hfs = (\n", "        pb_hfs.groupby([\"user_id\"])[\"product_id\"]\n", "        .apply(list)\n", "        .reset_index()\n", "        .rename(columns={\"dim_customer_key\": \"user_id\"})\n", "    )\n", "\n", "    pb_hfs[\"section\"] = \"previously_bought_hfs\"\n", "    print(\"Runtime for HFS data: \", (datetime.now() - st).seconds / 60)\n", "    return pb_hfs"]}, {"cell_type": "markdown", "id": "dc405036-7983-4f2f-8d19-a210fc8deba7", "metadata": {}, "source": ["## More that you Ordered"]}, {"cell_type": "code", "execution_count": null, "id": "a7049f01-e4d7-4ba7-a4bc-a624d463d040", "metadata": {}, "outputs": [], "source": ["def get_more_that_you_ordered(user_data):\n", "    st = datetime.now()\n", "    more_that_you_ordered = user_data[\n", "        (user_data[\"more_that_you_ordered_flag\"] == 1)\n", "        & (user_data[\"group_pid\"] != 1)\n", "        & (user_data[\"more_that_you_ordered\"] > 0)\n", "    ]\n", "\n", "    m1 = (\n", "        more_that_you_ordered.groupby([\"user_id\", \"Group Name\"])\n", "        .cycle_days.min()\n", "        .reset_index()\n", "        .rename(columns={\"cycle_days\": \"gds\"})\n", "    )\n", "\n", "    more_that_you_ordered = more_that_you_ordered.merge(m1)\n", "\n", "    more_that_you_ordered[\"product_id\"] = more_that_you_ordered.apply(\n", "        lambda row: {\n", "            \"product_id\": row[\"product_id\"],\n", "            \"last_bought\": row[\"last_bought_date\"],\n", "            \"decay_score\": row[\"decay_score\"],\n", "        },\n", "        axis=1,\n", "    )\n", "\n", "    more_that_you_ordered = more_that_you_ordered.sort_values(\n", "        by=[\"user_id\", \"gds\", \"decay_score\"], ascending=[True, False, False]\n", "    )[\n", "        [\n", "            \"user_id\",\n", "            \"product_id\",\n", "            \"decay_score\",\n", "            \"order_count\",\n", "            \"last_bought_date\",\n", "        ]\n", "    ]\n", "\n", "    more_that_you_ordered = (\n", "        more_that_you_ordered.groupby([\"user_id\"])[\"product_id\"].apply(list).reset_index()\n", "    )\n", "    more_that_you_ordered[\"section\"] = \"more_that_you_ordered\"\n", "    del m1\n", "    gc.collect()\n", "    print(\"Runtime for more that you ordered: \", (datetime.now() - st).seconds / 60)\n", "    return more_that_you_ordered"]}, {"cell_type": "markdown", "id": "2198631b-4422-48a2-8fb2-31f5956c869c", "metadata": {}, "source": ["## YMBROO"]}, {"cell_type": "code", "execution_count": null, "id": "582599e2-53de-4521-83e5-6f639154aa01", "metadata": {}, "outputs": [], "source": ["def get_ymbroo(day, mods, b_l0=b_l0, b_l1=b_l1, b_ptype_ids=b_ptype_ids, b_pids=b_pids):\n", "    st = datetime.now()\n", "    df_ymbroo = query_with_retries(\n", "        f\"\"\"with user_list as (select distinct dim_customer_key\n", "        from dwh.fact_sales_order_details\n", "        where ((order_create_dt_ist = current_date - interval '{day}' day)\n", "            or (order_archived_dt_ist = current_date - interval '{day}' day))\n", "        and is_internal_order = False and dim_customer_key %% 10 in {*mods,}),\n", "        \n", "        cust_max_date as (\n", "        select dim_customer_key,\n", "               max(cart_rank_bucket) as cust_max_cr\n", "        from ds_etls.user_order_history\n", "        where dim_customer_key in (select dim_customer_key from user_list)\n", "        and last_bought_dt between current_date - interval '400' day and current_date - interval '1' day \n", "        group by 1)\n", "        \n", "        select \n", "            fs.dim_customer_key,\n", "            dp.product_type,\n", "            fs.product_id,\n", "            (case when dp.l0_category_id = 7 then 'kids' when dp.l0_category_id = 13 then 'beauty' end) as tab,\n", "            dp.product_name,\n", "            ptype_cycle_days,\n", "            frequently_bought_ptype,\n", "            max(fs.last_bought_dt) as last_bought_date,\n", "            sum(fs.order_count) as order_count,\n", "            sum(case when cust_max_cr = 0 then 0.1 else 0.1 + fs.order_count * power(0.95,(c.cust_max_cr - fs.cart_rank_bucket)*100.0/c.cust_max_cr) end) as decay_score\n", "        from ds_etls.user_order_history fs\n", "        join cust_max_date c on c.dim_customer_key = fs.dim_customer_key\n", "        join dwh.dim_product dp on dp.product_id = fs.product_id and dp.is_current and dp.is_product_enabled\n", "        left join consumer_intelligence_etls.purchase_cycle_days cy on cy.product_id = dp.product_id and cy.city_id = 0\n", "        where dp.l0_category_id in (7,13)\n", "        and dp.l0_category_id not in ({b_l0})\n", "        and dp.l1_category_id not in ({b_l1})\n", "        and dp.product_type_id not in ({b_ptype_ids})\n", "        and dp.product_id not in ({b_pids})\n", "        and fs.dim_customer_key is not null\n", "        group by 1,2,3,4,5,6,7\"\"\"\n", "    )\n", "    df_ymbroo[\"product_id\"] = df_ymbroo.apply(\n", "        lambda row: {\n", "            \"product_id\": row[\"product_id\"],\n", "            \"last_bought\": row[\"last_bought_date\"],\n", "            \"decay_score\": row[\"decay_score\"],\n", "        },\n", "        axis=1,\n", "    )\n", "\n", "    df_pharma = query_with_retries(\n", "        f\"\"\"\n", "        with user_list as (select distinct dim_customer_key\n", "            from dwh.fact_sales_order_details\n", "            where ((order_create_dt_ist = current_date - interval '{day}' day)\n", "                or (order_archived_dt_ist = current_date - interval '{day}' day))\n", "            and is_internal_order = False and dim_customer_key %% 10 in {*mods,}),\n", "\n", "        cust_max_date as (\n", "            select dim_customer_key,\n", "                   max(cart_rank_bucket) as cust_max_cr\n", "            from ds_etls.user_order_history\n", "            where dim_customer_key in (select dim_customer_key from user_list)\n", "            and last_bought_dt between current_date - interval '400' day and current_date - interval '1' day \n", "            group by 1)\n", "\n", "        select distinct\n", "            fs.dim_customer_key,\n", "            dp.product_type,\n", "            fs.product_id,\n", "            'pharmaceuticals' as tab,\n", "            dp.product_name,\n", "            max(fs.last_bought_dt) as last_bought_date,\n", "            sum(fs.order_count) as order_count,\n", "            sum(case when cust_max_cr = 0 then 0.1 else 0.1 + fs.order_count * power(0.95,(c.cust_max_cr - fs.cart_rank_bucket)*100.0/c.cust_max_cr) end) as decay_score\n", "        from ds_etls.user_order_history fs\n", "        join cust_max_date c on c.dim_customer_key = fs.dim_customer_key\n", "        join dwh.dim_product dp on dp.product_id = fs.product_id and dp.is_current and dp.is_product_enabled\n", "        and dp.l1_category_id in (7275,7342) and fs.dim_customer_key is not null\n", "        group by 1,2,3,4,5\"\"\"\n", "    )\n", "    df_ymbroo = df_ymbroo.sort_values(\n", "        by=[\"frequently_bought_ptype\", \"decay_score\"], ascending=[False, False]\n", "    )\n", "    df_ymbroo = df_ymbroo[df_ymbroo[\"frequently_bought_ptype\"] == \"yes\"]\n", "    df_ymbroo = df_ymbroo[df_ymbroo[\"order_count\"] > 1]\n", "    df_ymbroo = (\n", "        df_ymbroo.groupby([\"dim_customer_key\", \"tab\"])[\"product_id\"]\n", "        .apply(list)\n", "        .reset_index()\n", "        .rename(columns={\"dim_customer_key\": \"user_id\"})\n", "    )\n", "\n", "    if df_pharma.shape[0] > 0:\n", "        df_pharma[\"product_id\"] = df_pharma.apply(\n", "            lambda row: {\n", "                \"product_id\": row[\"product_id\"],\n", "                \"last_bought\": row[\"last_bought_date\"],\n", "                \"decay_score\": row[\"decay_score\"],\n", "            },\n", "            axis=1,\n", "        )\n", "        df_pharma = df_pharma.sort_values(by=[\"decay_score\"], ascending=[False])\n", "        df_pharma = (\n", "            df_pharma.groupby([\"dim_customer_key\", \"tab\"])[\"product_id\"]\n", "            .apply(list)\n", "            .reset_index()\n", "            .rename(columns={\"dim_customer_key\": \"user_id\"})\n", "        )\n", "        df_ymbroo = pd.concat([df_ymbroo, df_pharma], axis=0)\n", "\n", "    df_ymbroo[\"product_id\"] = df_ymbroo.apply(\n", "        lambda row: {\n", "            row[\"tab\"]: row[\"product_id\"],\n", "        },\n", "        axis=1,\n", "    )\n", "\n", "    def combine_dict(x):\n", "        combined_dict = {}\n", "        for a in x:\n", "            combined_dict.update(a)\n", "        return combined_dict\n", "\n", "    df_ymbroo = (\n", "        df_ymbroo[[\"user_id\", \"product_id\"]]\n", "        .groupby(\"user_id\")[\"product_id\"]\n", "        .apply(list)\n", "        .reset_index()\n", "    )\n", "    df_ymbroo[\"product_id\"] = df_ymbroo[\"product_id\"].apply(combine_dict)\n", "    df_ymbroo[\"section\"] = \"you_might_be_running_out_of\"\n", "    print(\"Runtime for you_might_be_running_out_of: \", (datetime.now() - st).seconds / 60)\n", "    return df_ymbroo"]}, {"cell_type": "markdown", "id": "facee732-fae2-4f98-bd28-0d580c3918fd", "metadata": {}, "source": ["## creating kafka dictionary for FS Push"]}, {"cell_type": "code", "execution_count": null, "id": "badc2ff2-85db-4394-ade5-da27b1cddbc2", "metadata": {}, "outputs": [], "source": ["def create_kafka_dict(overall):\n", "    st = datetime.now()\n", "    overall[\"product_id\"] = overall[\"product_id\"]\n", "    overall[\"section\"] = overall[\"section\"].astype(str)\n", "\n", "    def create_dict(row):\n", "        return {row[\"section\"]: row[\"product_id\"]}\n", "\n", "    overall[\"data_str\"] = overall.apply(lambda row: create_dict(row), axis=1)\n", "\n", "    def combine_dict(x):\n", "        combined_dict = {}\n", "        for a in x:\n", "            combined_dict.update(a)\n", "        return combined_dict\n", "\n", "    result = (\n", "        overall[[\"user_id\", \"data_str\"]].groupby(\"user_id\")[\"data_str\"].apply(list).reset_index()\n", "    )\n", "    result[\"data_str\"] = result[\"data_str\"].apply(combine_dict)\n", "    result = result.rename(columns={\"data_str\": \"product_id\"})\n", "    kafka_dict = result.to_dict(\"records\")\n", "    print(\"Runtime for Creating Kafka dictionary: \", (datetime.now() - st).seconds / 60)\n", "    return kafka_dict"]}, {"cell_type": "code", "execution_count": null, "id": "7ba042e7-4e67-42eb-a2ad-e4d2e551eed6", "metadata": {}, "outputs": [], "source": ["try:\n", "    day = (\n", "        from_sheets(\"1k8pNkI7GjEGOul5aVeOcOb06fYBeLT43wkwPKfACFLI\", \"adhoc_run\")\n", "        .run_day.astype(int)\n", "        .unique()[0]\n", "    )\n", "except:\n", "    day = 1\n", "print(day)"]}, {"cell_type": "code", "execution_count": null, "id": "2889f9ee-f38d-4413-b9ae-ecee87fb72ea", "metadata": {}, "outputs": [], "source": ["for mods in [[0, 1, 2], [3, 4, 5], [6, 7], [8, 9]]:\n", "    print(mods)\n", "    user_data = get_user_data(day, mods)\n", "    user_data = defining_flags(user_data)\n", "    user_stats, user_data = get_user_stats(user_data)\n", "    collections, collections_2 = get_your_collections(user_data)\n", "    frequently_bought = get_frequently_bought(user_data)\n", "    recently_bought = get_recently_bought(user_data)\n", "    more_that_you_ordered = get_more_that_you_ordered(user_data)\n", "    pb_hfs = get_hfs_data(day, mods)\n", "    df_ymbroo = get_ymbroo(day, mods)\n", "\n", "    del user_data\n", "    gc.collect()\n", "    overall = pd.concat(\n", "        [\n", "            collections,\n", "            collections_2,\n", "            user_stats,\n", "            frequently_bought,\n", "            recently_bought,\n", "            pb_hfs,\n", "            more_that_you_ordered,\n", "            df_ymbroo,\n", "        ],\n", "        axis=0,\n", "    )\n", "    del (\n", "        collections,\n", "        collections_2,\n", "        user_stats,\n", "        frequently_bought,\n", "        recently_bought,\n", "        pb_hfs,\n", "        more_that_you_ordered,\n", "        df_ymbroo,\n", "    )\n", "    gc.collect()\n", "    kafka_dict = create_kafka_dict(overall)\n", "    print(len(kafka_dict))\n", "\n", "    del overall\n", "    gc.collect()\n", "\n", "    entity_column = \"user_id\"\n", "    entity_name = \"user\"\n", "    context = \"order_again_3_0_0\"\n", "    ctx_value_col = \"product_id\"\n", "\n", "    def np_encoder(object):\n", "        if isinstance(object, np.generic):\n", "            return object.item()\n", "\n", "    push_to_kafka(\n", "        entities=[f\"{entity_name}:{i[entity_column]}\" for i in kafka_dict],\n", "        context=context,\n", "        ctx_properties=[\n", "            {\"ctx_value\": json.dumps(i[ctx_value_col], default=np_encoder)} for i in kafka_dict\n", "        ],\n", "        dry_run=False,\n", "    )\n", "\n", "    del kafka_dict\n", "    gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "b1e5008a-0746-453b-8edc-92d8c563adc6", "metadata": {}, "outputs": [], "source": ["print(\"Done\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}