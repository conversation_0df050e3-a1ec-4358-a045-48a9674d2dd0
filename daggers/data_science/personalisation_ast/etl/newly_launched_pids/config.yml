alert_configs:
  slack:
  - channel: bl-personalisation-dag-failures
concurrency: 3
dag_name: newly_launched_pids
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebooks:
- executor_config:
    load_type: high-mem
    node_type: spot
  name: notebook
  parameters: null
  tag: level1
- executor_config:
    load_type: very-high-mem
    node_type: spot
  name: new_at_your_store_v2
  parameters: null
  retries: 2
  retry_delay_in_seconds: 60
  tag: level2
owner:
  email: <EMAIL>
  slack_id: U03SV2AN82C
path: data_science/personalisation_ast/etl/newly_launched_pids
paused: false
pool: data_science_pool
project_name: personalisation_ast
schedule:
  end_date: '2025-08-08T00:00:00'
  interval: 30 1 * * *
  start_date: '2025-05-15T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- db_utils.py
- polygon_utils.py
tags: []
template_name: multi_notebook
version: 26
