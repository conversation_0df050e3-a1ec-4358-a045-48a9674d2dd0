alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
  - channel: bl-personalization-notifications
concurrency: 3
dag_name: personalisation_ast
dag_type: etl
escalation_priority: low
execution_timeout: 839
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebooks:
- executor_config:
    load_type: high-mem
    node_type: spot
  name: data_fetch_pipeline
  parameters: null
  retries: 1
  retry_delay_in_seconds: 15
  tag: level1
- executor_config:
    load_type: very-high-mem
    node_type: spot
  name: post_processing_pipeline_v2
  parameters: null
  retries: 1
  retry_delay_in_seconds: 15
  tag: level2
owner:
  email: <EMAIL>
  slack_id: U03SV2AN82C
path: data_science/personalisation_ast/etl/personalisation_ast
paused: false
pool: data_science_pool
project_name: personalisation_ast
schedule:
  end_date: '2025-08-08T00:00:00'
  interval: 0 20 * * *
  start_date: '2025-05-06T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- kafka_utils.py
tags: []
template_name: multi_notebook
version: 23
