alert_configs:
  slack:
  - channel: bl-personalisation-dag-failures
dag_name: ptype_expansion
dag_type: etl
escalation_priority: low
execution_timeout: 839
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: low
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: data_science
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03SV2AN82C
path: data_science/personalisation_ast/etl/ptype_expansion
paused: false
pool: data_science_pool
project_name: personalisation_ast
schedule:
  end_date: '2025-08-03T00:00:00'
  interval: 30 1 * * *
  start_date: '2025-05-15T00:00:00'
schedule_type: fixed
sla: 121 minutes
support_files: []
tags: []
template_name: notebook
version: 3
