{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import pytz\n", "import jinja2\n", "import requests\n", "import pandas as pd\n", "import pencilbox as pb\n", "from gspread.exceptions import APIError\n", "\n", "pd.set_option(\"display.max_columns\", 50)\n", "import pytz\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "from requests.auth import HTTPBasicAuth\n", "from datetime import date\n", "from dateutil.relativedelta import relativedelta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_details = pd.read_sql_query(\n", "    \"\"\"select id as outlet_id, name as outlet_name, tax_location_id from lake_retail.console_outlet c\n", "                                        inner join (select distinct outlet_id as outlet_id from lake_reports.reports_inventory_snapshot \n", "                                                     where date(snapshot_datetime) between current_date-2 and current_date-1) x on x.outlet_id = c.id\n", "                                        where active = 1\"\"\",\n", "    con=pb.get_connection(\"redshift\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["console_locations = pd.read_sql_query(\n", "    \"\"\"select id as tax_location_id, name as city_name from retail.console_location;\"\"\",\n", "    con=pb.get_connection(\"retail\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_details.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlets_final = outlet_details.merge(\n", "    console_locations, on=[\"tax_location_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlets_final.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order = (\n", "    outlets_final.city_name.value_counts()\n", "    .reset_index()\n", "    .rename(columns={\"city_name\": \"outlets\", \"index\": \"city_name\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = outlets_final.merge(order, on=[\"city_name\"], how=\"inner\").sort_values(\n", "    by=[\"outlets\", \"outlet_id\"], ascending=[False, True]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final.reset_index(drop=True).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["JENKINS_URL = \"https://jenkins-retail.grofer.io/job/inventory_recon/buildWithParameters\"\n", "\n", "JENKINS_TOKEN = \"11f2e1bd97633964d492339b8de0f031cf\"\n", "\n", "creds = HTTPBasicAuth(\"<EMAIL>\", JENKINS_TOKEN)\n", "\n", "today = date.today()\n", "tz = pytz.timezone(\"Asia/Kolkata\")\n", "end_date = (date.today().replace(day=1) - <PERSON><PERSON><PERSON>(days=1)).strftime(\"%d-%m-%Y\")\n", "start_date = (\n", "    (date.today().replace(day=1) - <PERSON><PERSON><PERSON>(days=1))\n", "    .replace(day=1)\n", "    .strftime(\"%d-%m-%Y\")\n", ")\n", "\n", "start_date, end_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["a = [i for i in range(0, final.shape[0], 40)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if date.today().day == 1:\n", "    for i in range(0, len(a)):\n", "        if i < len(a) - 1:\n", "            xi = final[(final[\"index\"] >= a[i]) & (final[\"index\"] < a[i + 1])]\n", "            input_str = \",\".join([str(x) for x in xi[\"outlet_id\"].unique()])\n", "        else:\n", "            xi = final[(final[\"index\"] >= a[i]) & (final[\"index\"] <= final.shape[0])]\n", "            input_str = \",\".join([str(x) for x in xi[\"outlet_id\"].unique()])\n", "        payload = {\n", "            \"BRANCH_NAME\": \"master\",\n", "            \"ENV\": \"prod\",\n", "            \"OUTLET_IDS\": input_str,\n", "            \"START_DATE\": start_date,\n", "            \"END_DATE\": end_date,\n", "        }\n", "        print(i, input_str)\n", "        import requests\n", "        from requests.auth import HTTPBasicAuth\n", "        import pencilbox as pb\n", "\n", "        requests.post(url=JENKINS_URL, auth=creds, params=payload)\n", "        time.sleep(60)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# [prod]\n", "# read_host = reportsmysql.retail.prod.grofer.io\n", "\n", "# [stage]\n", "# read_host = retail-mysql.stage.grofer.io\n", "\n", "# [test]\n", "# read_host = retail-mysql.test.grofer.io\n", "\n", "# [inventory_buckets]\n", "# grn = [1, 28, 76 , 90]\n", "# sale = [2, 22, 50, 75]\n", "# sale_reverse = [54]\n", "# customer_return = [3,4,5,6,23,29,30,31,32,35,81,82]\n", "# bad_stock = [11,12,13,64]\n", "# stock_update_positive = [14,26,44,45,46,47,48,74,118,121,122]\n", "# stock_update_negative = [15,27,39,40,41,42,43,73,117,129]\n", "# liquidation = [16,127]\n", "# esto = [17,92,91,124,125]\n", "# repackaged = [18]\n", "# vendor_return = [19,126]\n", "\n", "\n", "# [queries]\n", "# fetch_starting_inventory_log = select * from ims.ims_inventory_log where variant_id=\"{variant_id}\" and outlet_id={outlet_id} and pos_timestamp < \"{start_date_time}\" order by id desc limit 1;\n", "# fetch_ending_inventory_log = select * from ims.ims_inventory_log where variant_id=\"{variant_id}\" and outlet_id={outlet_id} and pos_timestamp <= \"{end_date_time}\" order by id desc limit 1;\n", "# fetch_bucket_variant_quantity = select COALESCE(sum(delta), 0) as quantity from ims.ims_inventory_log where variant_id in ({variant_ids}) and outlet_id={outlet_id} and inventory_update_type_id in ({inventory_update_types}) and pos_timestamp between \"{start_date_time}\" and \"{end_date_time}\";\n", "\n", "# variants = pd.read_sql_query(\"\"\"select item_id, upc, name, variant_uom_text, variant_mrp, variant_id, active, (CASE WHEN outlet_type = 1 THEN \"F&V Type\" ELSE \"Grocery type\" END) AS \"item_type\" from rpc.product_product\"\"\", con = pb.get_connection('retail'))\n", "\n", "\n", "# inventory_snapshot_date_time = pd.read_sql_query(\"\"\"select distinct(snapshot_datetime) as snapshot_datetime from reports.reports_inventory_snapshot\n", "#                                                     where snapshot_datetime between %(start_date_time)s and %(end_date_time)s\"\"\",con = pb.get_connection('retail'),\n", "#                                                  params = {\"start_date_time\":start_date, \"end_date_time\":end_date})\n", "\n", "# good_inventory_snapshot_date_time = pd.read_sql_query(\"\"\"select distinct(snapshot_datetime) as snapshot_datetime from reports.reports_good_inventory_snapshot\n", "#                                                     where snapshot_datetime between %(start_date_time)s and %(end_date_time)s\"\"\",con = pb.get_connection('retail'),\n", "#                                                  params = {\"start_date_time\":start_date, \"end_date_time\":end_date})\n", "\n", "# start_inv_snap_date_time = inventory_snapshot_date_time.iloc[0,0]\n", "# end_inv_snap_date_time = inventory_snapshot_date_time.iloc[-1,0]\n", "# start_good_inv_snap_date_time = good_inventory_snapshot_date_time.iloc[0,0]\n", "# end_good_inv_snap_date_time = good_inventory_snapshot_date_time.iloc[-1,0]\n", "\n", "# start_inv_snap_date_time,end_inv_snap_date_time,start_good_inv_snap_date_time,end_good_inv_snap_date_time\n", "\n", "# outlet_ids = [714]\n", "\n", "# for outlet in outlet_ids:\n", "#     # fetching inventory snapshot details for start & end dates\n", "#     start_outlet_variant_eod_inventory = pd.read_sql_query(\"\"\"select variant_id, quantity from reports.reports_inventory_snapshot\n", "#                                             where snapshot_datetime=%(snapshot)s and outlet_id=%(outlet_id)s and active = 1\"\"\"\n", "#                                                     ,con = pb.get_connection('retail'),params = {\"snapshot\":start_inv_snap_date_time, \"outlet_id\":outlet})\n", "\n", "#     start_outlet_variant_eod_good_inventory = pd.read_sql_query(\"\"\"select sum(quantity) as quantity, variant_id from reports.reports_good_inventory_snapshot\n", "#                                             where snapshot_datetime=%(snapshot)s and outlet_id=%(outlet_id)s and active = 1\n", "#                                             group by variant_id;\"\"\"\n", "#                                                     ,con = pb.get_connection('retail'),params = {\"snapshot\":start_good_inv_snap_date_time, \"outlet_id\":outlet})\n", "#     end_outlet_variant_eod_inventory = pd.read_sql_query(\"\"\"select variant_id, quantity from reports.reports_inventory_snapshot\n", "#                                             where snapshot_datetime=%(snapshot)s and outlet_id=%(outlet_id)s and active = 1\"\"\"\n", "#                                                     ,con = pb.get_connection('retail'),params = {\"snapshot\":end_inv_snap_date_time, \"outlet_id\":outlet})\n", "\n", "#     end_outlet_variant_eod_good_inventory = pd.read_sql_query(\"\"\"select sum(quantity) as quantity, variant_id from reports.reports_good_inventory_snapshot\n", "#                                             where snapshot_datetime=%(snapshot)s and outlet_id=%(outlet_id)s and active = 1\n", "#                                             group by variant_id;\"\"\"\n", "#                                                     ,con = pb.get_connection('retail'),params = {\"snapshot\":end_good_inv_snap_date_time, \"outlet_id\":outlet})\n", "#     print(start_outlet_variant_eod_inventory.shape,start_outlet_variant_eod_good_inventory.shape,\n", "#          end_outlet_variant_eod_inventory.shape,end_outlet_variant_eod_good_inventory.shape)\n", "\n", "#     # fetching starting & ending inventory logs\n", "\n", "\n", "#     starting_inventory_log = pd.read_sql_query(\"\"\"select * from ims.ims_inventory_log where variant_id=%(variant_id)s and outlet_id=%(outlet_id)s and pos_timestamp < %(start_date_time)s order by id desc limit 1;\"\"\"\n", "#                                                , con = pb.get_connection('retail')),params = {\"start_date_time\":start_inv_snap_date_time, \"outlet_id\":outlet})\n", "\n", "\n", "#     fetch_ending_inventory_log = select * from ims.ims_inventory_log where variant_id=\"{variant_id}\" and outlet_id={outlet_id} and pos_timestamp <= \"{end_date_time}\" order by id desc limit 1;\n", "\n", "#     latest_variant_id_for_item_outlet = pd.read_sql_query(\"\"\"select variant_id from ims.ims_inventory_log where variant_id in ({variant_ids})\n", "#                                                             and outlet_id = %(outlet_id)s order by id desc limit 1\"\"\".format(\n", "#                                                                         variant_ids = ','.join([str(x) for x in df.variant_id.unique()])),\n", "#                                                           con = pb.get_connection('retail')), params = {\"outlet_id\":outlet}\n", "\n", "# def eod_inventory_for_item_outlet(self, outlet_id, inventory_snap_shot_date_time, good_inventory_snap_shot_date_time):\n", "#         item_inventory_map = {}\n", "#         if inventory_snap_shot_date_time is not None:\n", "#             inventories = self.fetch_eod_inventory_snapshot_for_outlet(inventory_snap_shot_date_time, outlet_id)\n", "#             for inventory in inventories:\n", "#                 if inventory.get(\"variant_id\") not in self.variant_id_variant_map:\n", "#                     continue\n", "#                 variant = self.variant_id_variant_map[inventory.get(\"variant_id\")]\n", "#                 variant_item_id = variant.get(\"item_id\")\n", "#                 if variant_item_id not in item_inventory_map:\n", "#                     item_inventory_map[variant_item_id] = 0\n", "#                 item_inventory_map[variant_item_id] += inventory.get(\"quantity\")\n", "\n", "#         if good_inventory_snap_shot_date_time is not None:\n", "#             good_inventories = self.fetch_eod_good_inventory_snapshot_for_outlet(good_inventory_snap_shot_date_time,\n", "#                                                                                  outlet_id)\n", "#             for inventory in good_inventories:\n", "#                 if inventory.get(\"variant_id\") not in self.variant_id_variant_map:\n", "#                     continue\n", "#                 variant = self.variant_id_variant_map[inventory.get(\"variant_id\")]\n", "#                 variant_item_id = variant.get(\"item_id\")\n", "#                 if variant_item_id not in item_inventory_map:\n", "#                     item_inventory_map[variant_item_id] = 0\n", "#                 item_inventory_map[variant_item_id] += inventory.get(\"quantity\")\n", "\n", "#         return item_inventory_map\n", "\n", "# variants.head(2)\n", "\n", "# for variant in variants.variant_id.unique():\n", "#     variants[variants[\"variant_id\"]==variant].item_id.unique()\n", "\n", "# item_id_variant_map = {}\n", "# variant_id_variant_map = {}\n", "# for variant in variants.variant_id.unique():\n", "#     variant_id = variant.get(\"variant_id\")\n", "#     item_id = variant.get(\"item_id\")\n", "#     variant_id_variant_map[variant_id] = variant\n", "\n", "#     if item_id not in item_id_variant_map:\n", "#         item_id_variant_map[item_id] = []\n", "#     item_id_variant_map[item_id].append(variant_id)\n", "# self.item_id_variant_map = item_id_variant_map\n", "# self.variant_id_variant_map = variant_id_variant_map\n", "\n", "# import ConfigParser\n", "# import logging\n", "# import sys\n", "# from csv import writer\n", "# from datetime import datetime, timedelta\n", "# from io import BytesIO\n", "\n", "# from MySQLdb import connect, cursors\n", "\n", "\n", "# # Usage: python inventory_aging.py <env> <outlet_ids> <start_date> <end_date> <db_user> <db_pass>\n", "# # Eg : python inventory_recon.py prod 116,99 1-1-2018 31-1-2018\n", "\n", "# # This job calculates live inventory recon for given outlets between the passed time interval\n", "# class InventoryRecon:\n", "#     def __init__(self):\n", "#         try:\n", "#             self.config = ConfigParser.ConfigParser()\n", "#             self.config.read('inventory_recon.cnf')\n", "\n", "#             # logging\n", "#             self.info_log = logging.getLogger('inventory_recon_info')\n", "#             self.error_log = logging.getLogger('inventory_recon_error')\n", "#             self.set_logger_settings()\n", "\n", "#             if len(sys.argv) < 7:\n", "#                 self.info_log.info(\"python inventory_aging.py <env> <outlet_ids> <start_date> <end_date> <db_user> \"\n", "#                                    \"<db_pass>\")\n", "#                 exit(1)\n", "\n", "#             environment = str(sys.argv[1])\n", "#             self.outlet_ids = list(set([int(outlet) for outlet in sys.argv[2].split(\",\")]))\n", "#             self.start_date_time = datetime.strptime(sys.argv[3], \"%d-%m-%Y\") - timed<PERSON>ta(hours=5, minutes=30)\n", "#             self.end_date_time = datetime.strptime(sys.argv[4], \"%d-%m-%Y\") - timed<PERSON>ta(hours=5, minutes=30) + \\\n", "#                                  <PERSON><PERSON><PERSON>(days=1)\n", "#             self.start_date_str = sys.argv[3]\n", "#             self.end_date_str = sys.argv[4]\n", "\n", "#             if self.start_date_time >= self.end_date_time:\n", "#                 self.error_log.error(\"Start date should be less than End date\")\n", "\n", "#             self.report_file_name = \"inventory_recon\" + sys.argv[3] + \"_\" + sys.argv[4] + \".csv\"\n", "\n", "#             self.info_log.info(\"Environment set to- \" + str(environment))\n", "#             self.info_log.info(\"Outlet ids set to- \" + str(self.outlet_ids))\n", "\n", "#             db_user = str(sys.argv[5])\n", "#             db_pass = str(sys.argv[6])\n", "\n", "#             self.setup_retail_database(environment, db_user, db_pass)\n", "\n", "#             self.generate_variant_maps()\n", "\n", "#             self.generate_report_for_outlets()\n", "\n", "#         except Exception as e:\n", "#             self.error_log.error(e.message, exc_info=True)\n", "#             exit(1)\n", "\n", "#     def generate_report_for_outlets(self):\n", "#         self.start_inv_snap_date_time = self.fetch_inventory_snapshot_date_time(self.start_date_time)\n", "#         self.end_inv_snap_date_time = self.fetch_inventory_snapshot_date_time(self.end_date_time)\n", "\n", "#         self.start_good_inv_snap_date_time = self.fetch_good_inventory_snapshot_date_time(\n", "#             self.start_date_time)\n", "#         self.end_good_inv_snap_date_time = self.fetch_good_inventory_snapshot_date_time(self.end_date_time)\n", "\n", "#         inventory_buckets = self.get_inventory_buckets()\n", "#         outlet_details_map = self.fetch_active_outlet_details()\n", "#         locations_map = self.fetch_console_locations()\n", "#         self.write_column_in_file()\n", "#         for outlet_id in self.outlet_ids:\n", "#             if outlet_id not in outlet_details_map:\n", "#                 self.error_log.error(\"Outlet id {outlet_id} is not present in console\".format(outlet_id=outlet_id))\n", "#                 continue\n", "#             self.generate_report_for_outlet(outlet_details_map.get(outlet_id), locations_map, inventory_buckets)\n", "\n", "#     def write_column_in_file(self):\n", "#         columns = [\"item_id\", \"name\", \"grammage\", \"MRP\", \"Item Type\", \"start date\", \"end date\",\n", "#                    \"outlet_id\", \"outlet_name\", \"city_name\", \"opening_quantity\",\n", "#                    \"grn\", \"sale\", \"customer_return\", \"bad_stock\", \"stock_update_positive\",\n", "#                    \"stock_update_negative\", \"liquidation\", \"esto\", \"repackaged\", \"vendor_return\",\n", "#                    \"closing_quantity\", \"closing_calculated_quantity\", \"closing diff\"]\n", "\n", "#         csv_data = self.get_csv_from_list([columns])\n", "#         with open(\"reports/\" + self.report_file_name, 'w+') as f:\n", "#             f.write(str(csv_data))\n", "\n", "#     def get_inventory_buckets(self):\n", "#         return {\n", "#             \"grn\": self.config.get('inventory_buckets', 'grn'),\n", "#             \"sale\": self.config.get('inventory_buckets', 'sale'),\n", "#             \"sale_reverse\": self.config.get('inventory_buckets', 'sale_reverse'),\n", "#             \"customer_return\": self.config.get('inventory_buckets', 'customer_return'),\n", "#             \"bad_stock\": self.config.get('inventory_buckets', 'bad_stock'),\n", "#             \"stock_update_positive\": self.config.get('inventory_buckets', 'stock_update_positive'),\n", "#             \"stock_update_negative\": self.config.get('inventory_buckets', 'stock_update_negative'),\n", "#             \"liquidation\": self.config.get('inventory_buckets', 'liquidation'),\n", "#             \"esto\": self.config.get('inventory_buckets', 'esto'),\n", "#             \"repackaged\": self.config.get('inventory_buckets', 'repackaged'),\n", "#             \"vendor_return\": self.config.get('inventory_buckets', 'vendor_return'),\n", "#         }\n", "\n", "#     def generate_variant_maps(self):\n", "#         variants = self.fetch_variants()\n", "#         item_id_variant_map = {}\n", "#         variant_id_variant_map = {}\n", "#         for variant in variants:\n", "#             variant_id = variant.get(\"variant_id\")\n", "#             item_id = variant.get(\"item_id\")\n", "#             variant_id_variant_map[variant_id] = variant\n", "\n", "#             if item_id not in item_id_variant_map:\n", "#                 item_id_variant_map[item_id] = []\n", "#             item_id_variant_map[item_id].append(variant_id)\n", "#         self.item_id_variant_map = item_id_variant_map\n", "#         self.variant_id_variant_map = variant_id_variant_map\n", "\n", "#     def generate_report_for_outlet(self, outlet_details, locations_map, inventory_buckets):\n", "#         outlet_id = outlet_details.get(\"id\")\n", "#         outlet_name = outlet_details.get(\"name\")\n", "#         city = locations_map.get(outlet_details.get(\"tax_location_id\"))\n", "#         opening_item_inventory_map = self.get_eod_inventory_for_item_outlet(outlet_id,\n", "#                                                                             self.start_inv_snap_date_time,\n", "#                                                                             self.start_good_inv_snap_date_time)\n", "\n", "#         closing_item_inventory_map = self.get_eod_inventory_for_item_outlet(outlet_id,\n", "#                                                                             self.end_inv_snap_date_time,\n", "#                                                                             self.end_good_inv_snap_date_time)\n", "\n", "#         item_ids = [item_id for item_id in closing_item_inventory_map]\n", "#         csv_rows = []\n", "#         for item_id in item_ids:\n", "#             if item_id not in self.item_id_variant_map:\n", "#                 continue\n", "#             opening_quantity = opening_item_inventory_map.get(item_id, 0)\n", "#             closing_quantity = closing_item_inventory_map.get(item_id)\n", "#             grn = 0\n", "#             sale = 0\n", "#             sale_reverse = 0\n", "#             customer_return = 0\n", "#             bad_stock = 0\n", "#             stock_update_positive = 0\n", "#             stock_update_negative = 0\n", "#             liquidation = 0\n", "#             esto = 0\n", "#             repackaged = 0\n", "#             vendor_return = 0\n", "#             item_variant_ids = self.item_id_variant_map[item_id]\n", "#             query_variants = self.get_in_query_variants(item_variant_ids)\n", "\n", "#             grn += self.get_variant_bucket_quantity(outlet_id, query_variants, self.start_date_time,\n", "#                                                     self.end_date_time,\n", "#                                                     inventory_buckets.get(\"grn\"))\n", "#             sale += self.get_variant_bucket_quantity(outlet_id, query_variants, self.start_date_time,\n", "#                                                      self.end_date_time,\n", "#                                                      inventory_buckets.get(\"sale\"))\n", "#             sale_reverse += self.get_variant_bucket_quantity(outlet_id, query_variants, self.start_date_time,\n", "#                                                              self.end_date_time,\n", "#                                                              inventory_buckets.get(\"sale_reverse\"))\n", "\n", "#             customer_return += self.get_variant_bucket_quantity(outlet_id, query_variants, self.start_date_time,\n", "#                                                                 self.end_date_time,\n", "#                                                                 inventory_buckets.get(\"customer_return\"))\n", "#             bad_stock += self.get_variant_bucket_quantity(outlet_id, query_variants, self.start_date_time,\n", "#                                                           self.end_date_time,\n", "#                                                           inventory_buckets.get(\"bad_stock\"))\n", "#             stock_update_positive += self.get_variant_bucket_quantity(outlet_id, query_variants,\n", "#                                                                       self.start_date_time,\n", "#                                                                       self.end_date_time,\n", "#                                                                       inventory_buckets.get(\n", "#                                                                           \"stock_update_positive\"))\n", "#             stock_update_negative += self.get_variant_bucket_quantity(outlet_id, query_variants,\n", "#                                                                       self.start_date_time,\n", "#                                                                       self.end_date_time,\n", "#                                                                       inventory_buckets.get(\n", "#                                                                           \"stock_update_negative\"))\n", "#             liquidation += self.get_variant_bucket_quantity(outlet_id, query_variants, self.start_date_time,\n", "#                                                             self.end_date_time,\n", "#                                                             inventory_buckets.get(\"liquidation\"))\n", "#             esto += self.get_variant_bucket_quantity(outlet_id, query_variants, self.start_date_time,\n", "#                                                      self.end_date_time,\n", "#                                                      inventory_buckets.get(\"esto\"))\n", "#             repackaged += self.get_variant_bucket_quantity(outlet_id, query_variants, self.start_date_time,\n", "#                                                            self.end_date_time,\n", "#                                                            inventory_buckets.get(\"repackaged\"))\n", "#             vendor_return += self.get_variant_bucket_quantity(outlet_id, query_variants, self.start_date_time,\n", "#                                                               self.end_date_time,\n", "#                                                               inventory_buckets.get(\"vendor_return\"))\n", "\n", "#             closing_calculated_quantity = (opening_quantity + grn - sale + sale_reverse + customer_return - bad_stock\n", "#                                            + stock_update_positive - stock_update_negative - liquidation - esto +\n", "#                                            repackaged - vendor_return)\n", "#             # latest_inventory_log = self.fetch_latest_variant_id_for_item_outlet(self.item_id_variant_map[item_id],\n", "#             #                                                                   outlet_id)\n", "#             latest_variant = self.variant_id_variant_map[self.item_id_variant_map[item_id][0]]\n", "#             # if latest_inventory_log is None:\n", "#             #     latest_variant = self.variant_id_variant_map[self.item_id_variant_map[item_id][0]]\n", "#             # else:\n", "#             #     latest_variant_id = latest_inventory_log.get(\"variant_id\")\n", "#             #     latest_variant = self.variant_id_variant_map[latest_variant_id]\n", "#             csv_row = [\n", "#                 item_id,\n", "#                 latest_variant.get(\"name\"),\n", "#                 latest_variant.get(\"variant_uom_text\"),\n", "#                 latest_variant.get(\"variant_mrp\"),\n", "#                 latest_variant.get(\"item_type\"),\n", "#                 self.start_date_str,\n", "#                 self.end_date_str,\n", "#                 outlet_id,\n", "#                 outlet_name,\n", "#                 city,\n", "#                 opening_quantity,\n", "#                 grn,\n", "#                 sale - sale_reverse,\n", "#                 customer_return,\n", "#                 bad_stock,\n", "#                 stock_update_positive,\n", "#                 stock_update_negative,\n", "#                 liquidation,\n", "#                 esto,\n", "#                 repackaged,\n", "#                 vendor_return,\n", "#                 closing_quantity,\n", "#                 closing_calculated_quantity,\n", "#                 closing_quantity - closing_calculated_quantity\n", "#             ]\n", "#             csv_rows.append(csv_row)\n", "\n", "#         csv_data = self.get_csv_from_list(csv_rows)\n", "#         with open(\"reports/\" + self.report_file_name, 'a+') as f:\n", "#             f.write(str(csv_data))\n", "\n", "#     def get_variant_bucket_quantity(self, outlet_id, variant_ids, start_date_time, end_date_time,\n", "#                                     inventory_update_types):\n", "#         query = self.config.get('queries', 'fetch_bucket_variant_quantity')\n", "#         query = query.format(outlet_id=outlet_id, variant_ids=variant_ids, start_date_time=start_date_time,\n", "#                              end_date_time=end_date_time, inventory_update_types=inventory_update_types)\n", "#         query = query.format()\n", "#         # self.info_log.info(\"Executing query : \" + str(query))\n", "#         self.read_cursor.execute(query)\n", "#         rows = self.read_cursor.fetchall()\n", "#         if len(rows) > 0:\n", "#             return rows[0].get(\"quantity\")\n", "#         return 0\n", "\n", "#     def get_csv_from_list(self, data):\n", "#         output = BytesIO()\n", "#         csv_writer = writer(output)\n", "#         for each_row in data:\n", "#             csv_writer.writerow(each_row)\n", "#         return output.getvalue()\n", "\n", "#     def fetch_active_outlet_details(self):\n", "#         outlet_details_map = {}\n", "#         query = self.config.get('queries', 'fetch_outlet_details')\n", "#         self.info_log.info(\"Executing query : \" + str(query))\n", "#         self.read_cursor.execute(query)\n", "#         rows = self.read_cursor.fetchall()\n", "#         if len(rows) == 0:\n", "#             self.error_log.info(\"No active outlets found\")\n", "#             exit(1)\n", "#         for row in rows:\n", "#             outlet_details_map[row.get(\"id\")] = row\n", "#         return outlet_details_map\n", "\n", "#     def fetch_console_locations(self):\n", "#         locations_map = {}\n", "#         query = self.config.get('queries', 'fetch_console_locations')\n", "#         self.info_log.info(\"Executing query : \" + str(query))\n", "#         self.read_cursor.execute(query)\n", "#         rows = self.read_cursor.fetchall()\n", "#         if len(rows) == 0:\n", "#             self.error_log.info(\"No active outlets found\")\n", "#             exit(1)\n", "#         for row in rows:\n", "#             locations_map[row.get(\"id\")] = row.get(\"name\")\n", "#         return locations_map\n", "\n", "#     def fetch_isd_quantity_for_variant(self, outlet_id, variant_id, start_date_time, end_date_time):\n", "#         query = self.config.get('queries', 'fetch_isd_for_variant_date_range')\n", "#         query = query.format(outlet_id=outlet_id, variant_id=variant_id, start_date_time=start_date_time,\n", "#                              end_date_time=end_date_time)\n", "\n", "#         # self.info_log.info(\"Executing query : \" + str(query))\n", "#         self.read_cursor.execute(query)\n", "#         rows = self.read_cursor.fetchall()\n", "#         return int(rows[0].get(\"quantity\", 0))\n", "\n", "#     def fetch_starting_inventory_log(self, outlet_id, variant_id, start_date_time):\n", "#         query = self.config.get('queries', 'fetch_starting_inventory_log')\n", "#         query = query.format(outlet_id=outlet_id, variant_id=variant_id, start_date_time=start_date_time)\n", "\n", "#         # self.info_log.info(\"Executing query : \" + str(query))\n", "#         self.read_cursor.execute(query)\n", "#         rows = self.read_cursor.fetchall()\n", "#         if len(rows) > 0:\n", "#             return rows[0]\n", "#         return None\n", "\n", "#     def fetch_ending_inventory_log(self, outlet_id, variant_id, end_date_time):\n", "#         query = self.config.get('queries', 'fetch_ending_inventory_log')\n", "#         query = query.format(outlet_id=outlet_id, variant_id=variant_id, end_date_time=end_date_time)\n", "\n", "#         # self.info_log.info(\"Executing query : \" + str(query))\n", "#         self.read_cursor.execute(query)\n", "#         rows = self.read_cursor.fetchall()\n", "#         if len(rows) > 0:\n", "#             return rows[0]\n", "#         return None\n", "\n", "#     def set_logger_settings(self):\n", "#         formatter = logging.Formatter('%(levelname)s: %(asctime)s %(funcName)s(%(lineno)d) -- %(message)s',\n", "#                                       datefmt='%Y-%m-%d %H:%M:%S')\n", "#         console_handler = logging.StreamHandler()\n", "#         console_handler.setFormatter(formatter)\n", "\n", "#         self.info_log.setLevel(logging.INFO)\n", "#         file_handler = logging.FileHandler('logs/aging_inventory_info.log')\n", "#         file_handler.setFormatter(formatter)\n", "#         self.info_log.addHandler(file_handler)\n", "#         self.info_log.addHandler(console_handler)\n", "\n", "#         self.error_log.setLevel(logging.ERROR)\n", "#         file_handler = logging.FileHandler('logs/aging_inventory_error.log')\n", "#         file_handler.setFormatter(formatter)\n", "#         self.error_log.addHandler(file_handler)\n", "#         self.error_log.addHandler(console_handler)\n", "\n", "#     def connect_to_mysql(self, host, user, password, db):\n", "#         try:\n", "#             # Connect to MYSQL\n", "#             conn = connect(\n", "#                 host=host,\n", "#                 user=user,\n", "#                 passwd=password,\n", "#             )\n", "\n", "#             return conn\n", "#         except Exception as e:\n", "#             self.error_log.error(e)\n", "#             raise e\n", "\n", "#     def setup_retail_database(self, environment, db_user, db_pass):\n", "#         # Retail read settings\n", "#         self.read_host = self.config.get(environment, 'read_host')\n", "#         self.read_connection = self.connect_to_mysql(\n", "#             host=self.read_host, user=db_user, password=db_pass, db=None\n", "#         )\n", "#         self.read_cursor = self.read_connection.cursor(cursors.DictCursor)\n", "\n", "#     def fetch_variants(self):\n", "#         query = self.config.get('queries', 'fetch_variants')\n", "#         self.read_cursor.execute(query)\n", "#         self.info_log.info(\"Executing query : \" + str(query))\n", "#         variants = self.read_cursor.fetchall()\n", "#         return variants\n", "\n", "#     def fetch_inventory_snapshot_date_time(self, date_time):\n", "#         date_time_str = date_time.strftime(\"%Y-%m-%d\")\n", "#         start_date_time = date_time_str + \" 18:00:00\"\n", "#         end_date_time = date_time_str + \" 19:00:00\"\n", "#         query = self.config.get('queries', 'fetch_inventory_snapshot_date_time')\n", "#         query = query.format(start_date_time=start_date_time, end_date_time=end_date_time)\n", "#         self.info_log.info(\"Executing query : \" + str(query))\n", "#         self.read_cursor.execute(query)\n", "#         rows = self.read_cursor.fetchall()\n", "#         if len(rows) > 0:\n", "#             return rows[0].get(\"snapshot_datetime\")\n", "#         return None\n", "\n", "#     def fetch_good_inventory_snapshot_date_time(self, date_time):\n", "#         date_time_str = date_time.strftime(\"%Y-%m-%d\")\n", "#         start_date_time = date_time_str + \" 18:00:00\"\n", "#         end_date_time = date_time_str + \" 19:00:00\"\n", "#         query = self.config.get('queries', 'fetch_good_inventory_snapshot_date_time')\n", "#         query = query.format(start_date_time=start_date_time, end_date_time=end_date_time)\n", "#         self.info_log.info(\"Executing query : \" + str(query))\n", "#         self.read_cursor.execute(query)\n", "#         rows = self.read_cursor.fetchall()\n", "#         if len(rows) > 0:\n", "#             return rows[0].get(\"snapshot_datetime\")\n", "#         return None\n", "\n", "#     def fetch_eod_inventory_snapshot_for_outlet(self, snapshot_datetime, outlet_id):\n", "#         query = self.config.get('queries', 'fetch_outlet_variant_eod_inventory')\n", "#         query = query.format(snapshot_datetime=snapshot_datetime, outlet_id=outlet_id)\n", "#         self.info_log.info(\"Executing query : \" + str(query))\n", "#         self.read_cursor.execute(query)\n", "#         rows = self.read_cursor.fetchall()\n", "#         return rows\n", "\n", "#     def fetch_eod_good_inventory_snapshot_for_outlet(self, snapshot_datetime, outlet_id):\n", "#         query = self.config.get('queries', 'fetch_outlet_variant_eod_good_inventory')\n", "#         query = query.format(snapshot_datetime=snapshot_datetime, outlet_id=outlet_id)\n", "#         self.info_log.info(\"Executing query : \" + str(query))\n", "#         self.read_cursor.execute(query)\n", "#         rows = self.read_cursor.fetchall()\n", "#         return rows\n", "\n", "#     def get_in_query_variants(self, variant_ids):\n", "#         query_variants = [\"'\" + variant_id + \"'\" for variant_id in variant_ids]\n", "#         return \",\".join(query_variants)\n", "\n", "#     def fetch_latest_variant_id_for_item_outlet(self, variant_ids, outlet_id):\n", "#         query = self.config.get('queries', 'fetch_latest_variant_id_for_item_outlet')\n", "#         query_variants = self.get_in_query_variants(variant_ids)\n", "#         query = query.format(variant_ids=query_variants, outlet_id=outlet_id)\n", "#         # self.info_log.info(\"Executing query : \" + str(query))\n", "#         self.read_cursor.execute(query)\n", "#         rows = self.read_cursor.fetchall()\n", "#         if len(rows) > 0:\n", "#             return rows[0]\n", "#         return None\n", "\n", "#     def get_eod_inventory_for_item_outlet(self, outlet_id, inventory_snap_shot_date_time,\n", "#                                           good_inventory_snap_shot_date_time):\n", "#         item_inventory_map = {}\n", "#         if inventory_snap_shot_date_time is not None:\n", "#             inventories = self.fetch_eod_inventory_snapshot_for_outlet(inventory_snap_shot_date_time, outlet_id)\n", "#             for inventory in inventories:\n", "#                 if inventory.get(\"variant_id\") not in self.variant_id_variant_map:\n", "#                     continue\n", "#                 variant = self.variant_id_variant_map[inventory.get(\"variant_id\")]\n", "#                 variant_item_id = variant.get(\"item_id\")\n", "#                 if variant_item_id not in item_inventory_map:\n", "#                     item_inventory_map[variant_item_id] = 0\n", "#                 item_inventory_map[variant_item_id] += inventory.get(\"quantity\")\n", "\n", "#         if good_inventory_snap_shot_date_time is not None:\n", "#             good_inventories = self.fetch_eod_good_inventory_snapshot_for_outlet(good_inventory_snap_shot_date_time,\n", "#                                                                                  outlet_id)\n", "#             for inventory in good_inventories:\n", "#                 if inventory.get(\"variant_id\") not in self.variant_id_variant_map:\n", "#                     continue\n", "#                 variant = self.variant_id_variant_map[inventory.get(\"variant_id\")]\n", "#                 variant_item_id = variant.get(\"item_id\")\n", "#                 if variant_item_id not in item_inventory_map:\n", "#                     item_inventory_map[variant_item_id] = 0\n", "#                 item_inventory_map[variant_item_id] += inventory.get(\"quantity\")\n", "\n", "#         return item_inventory_map\n", "\n", "# InventoryRecon()\n", "\n", "\n", "# !pip install python-jenkins\n", "# import jenkins\n", "\n", "# !python --version"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}