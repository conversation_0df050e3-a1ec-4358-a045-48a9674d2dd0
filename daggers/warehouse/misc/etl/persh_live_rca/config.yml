dag_name: persh_live_rca
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 16G
      request: 10G
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: warehouse
notebooks:
- name: outlet_v2
  parameters: null
  tag: level1
owner:
  email: <EMAIL>
  slack_id: U03SCGRB152
path: warehouse/misc/etl/persh_live_rca
paused: true
project_name: misc
schedule:
  interval: 0 1,4,7,10,13,16 * * *
  start_date: '2022-08-08T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 21
