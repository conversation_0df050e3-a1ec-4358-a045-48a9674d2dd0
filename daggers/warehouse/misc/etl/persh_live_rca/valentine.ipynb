{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "\n", "pd.options.display.max_columns = 100\n", "import math\n", "\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, time\n", "import pytz\n", "\n", "tz = pytz.timezone(\"Asia/Calcutta\")\n", "now = datetime.now(tz)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# valentine days tracker"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = pb.from_sheets(\"1nwVIvCAExtU-SAvikOuM6jW8A5lNqvUZjzan60bfJ6I\", \"Abridged Master\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["p_ids = data.PID.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["p_ids = list(filter(None, p_ids))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["p_ids = list([int(i) for i in p_ids])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(p_ids)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT vm.id as frontend\n", "FROM lake_cms.gr_virtual_to_real_merchant_mapping vrm\n", "JOIN lake_cms.view_gr_merchant vm ON vrm.virtual_merchant_id = vm.id\n", "JOIN lake_cms.view_gr_merchant rm ON vrm.real_merchant_id = rm.id\n", "WHERE vrm.enabled_flag = TRUE\n", "  AND vm.enabled_flag = TRUE\n", "  AND vm.virtual_type='superstore_merchant'\n", "  AND vm.current_state!='content'\n", "  AND rm.virtual_type = 'not_applicable'\"\"\"\n", "fronts = pd.read_sql_query(query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f_ids = fronts.frontend.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excepts = pb.from_sheets(\"1-v_AMKltNY447GWneaM_x_3cNkmNKF5IEIUAiZvK9lc\", \"exceptions\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(f_ids)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excepts = list(excepts[\"frontend_ids\"].astype(int))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f_ids = list(set(f_ids) - set(excepts))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(f_ids)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from itertools import product\n", "\n", "df1 = pd.DataFrame(list(product(f_ids, p_ids)), columns=[\"f_ids\", \"pids\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["persh_query = f\"\"\"\n", "SELECT merchant_id as frontend_id, m.name as frontend_name, m.gr_id as frontend_gr_id,\n", " product_id, p.name as product_name,p.enabled_flag as product_enabled_flag,\n", "fmpm.inventory_limit,fmpm.master_inventory_limit, fmpm.enabled_flag, priority_order,\n", "pp.price, pp.mrp from lake_cms.gr_merchant_product_mapping fmpm \n", "left join lake_cms.view_gr_merchant m on m.id = fmpm.merchant_id \n", "LEFT JOIN lake_cms.gr_product p on p.id = fmpm.product_id\n", "LEFT JOIN lake_pricing_v3.pricing_domain_prices pp ON pp.frontend_id = fmpm.merchant_id\n", "                                        AND pp.cms_product_id = fmpm.product_id\n", "WHERE fmpm.merchant_id IN {*f_ids,}\n", "  AND fmpm.product_id IN {*p_ids,}\"\"\"\n", "persh_pids = pd.read_sql_query(persh_query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cats = pd.read_sql_query(\n", "    \"\"\"\n", "   select distinct P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "          C.NAME AS L0,\n", "          C.ID as L0_id, \n", "          (case when C1.NAME = C.name then C2.ID else C1.ID end) AS L1_id,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name as product_type\n", "   from lake_cms.gr_product P\n", "   INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   INNER JOIN lake_cms.gr_product_type pt on p.type_id=pt.id\"\"\",\n", "    redshift,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["persh_pids = persh_pids.merge(\n", "    cats[[\"pid\", \"l0\", \"l1\", \"l2\", \"product_type\"]],\n", "    left_on=[\"product_id\"],\n", "    right_on=[\"pid\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["persh_pids.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# express_query = \"\"\"express_frontends AS\n", "#         (SELECT DISTINCT vrm.virtual_merchant_id AS \"merchant_id\"\n", "#    FROM lake_cms.gr_virtual_to_real_merchant_mapping vrm\n", "#    JOIN lake_cms.view_gr_merchant rm ON vrm.real_merchant_id = rm.id\n", "#    JOIN lake_cms.view_gr_merchant vm ON vrm.virtual_merchant_id = vm.id\n", "#    WHERE vrm.enabled_flag = TRUE\n", "#      AND vm.enabled_flag = TRUE\n", "#      AND vm.virtual_type='superstore_merchant'\n", "#      AND vm.current_state!='content'\n", "#      AND rm.virtual_type = 'not_applicable'\n", "#      AND json_extract_path_text(rm.meta,'merchant_type')='Express'\n", "#      AND vm.id in {*f_ids,})\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for i in range(0, len(f_ids), int(math.sqrt(len(f_ids)))):\n", "#     print(f_ids[i : i + int(math.sqrt(len(f_ids)))])\n", "#     fmp_temp = pd.read_sql_query(\n", "#         f\"\"\"select merchant_id, product_id, inventory_limit,master_inventory_limit, enabled_flag, priority_order, pp.price, pp.mrp\n", "#                                      from lake_cms.gr_merchant_product_mapping mp\n", "#                                      LEFT JOIN lake_pricing_v3.pricing_domain_prices pp ON pp.frontend_id = mp.merchant_id\n", "#                                         AND pp.cms_product_id = mp.product_id\n", "#                                      where merchant_id in {*f_ids[i:i+int(math.sqrt(len(f_ids)))],}\"\"\",\n", "#         redshift,\n", "#     )\n", "#     f_merchant_product = pd.concat([f_merchant_product, fmp_temp], axis=0)\n", "# print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "st = time.time()\n", "prodct_cat = pd.read_sql_query(\n", "    \"\"\"select product_id, category_id from\n", "                                  lake_cms.gr_product_category_mapping \n", "                                  where is_primary = 1\"\"\",\n", "    redshift,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st = time.time()\n", "merchant_cat = pd.read_sql_query(\n", "    \"\"\"select merchant_id, category_id from\n", "                                  lake_cms.gr_merchant_category_mapping\"\"\",\n", "    redshift,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["persh_pids.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d1 = df1.merge(\n", "    persh_pids,\n", "    left_on=[\"f_ids\", \"pids\"],\n", "    right_on=[\"frontend_id\", \"product_id\"],\n", "    how=\"left\",\n", ")\n", "# d1 = persh_pids"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d2 = d1.merge(prodct_cat, on=[\"product_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_cat.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d3 = d2.merge(\n", "    merchant_cat,\n", "    left_on=[\"frontend_id\", \"category_id\"],\n", "    right_on=[\"merchant_id\", \"category_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d3.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# outlet_be_merchant_missing\n", "# item_product_mapping_missing\n", "# be_merchant_product_missing (store mapping)\n", "# be_fe_merchant_mappping_missing\n", "# fe_merchant_product_missing (dashboarding)\n", "# fe_enabled_flag_issue\n", "# inventory_limit_be_issue\n", "# inventory_limit_fe_issue\n", "# inventory_limit_be_fe_mismatch_issue\n", "# price_missing\n", "# mrp_missing\n", "# product_category_missing\n", "# product_category_missing_in_merchant"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d3[\"product_category_missing_in_merchant\"] = np.where(d3[\"merchant_id\"].isna(), 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d3[\"product_category_missing\"] = np.where(d3[\"category_id\"].isna(), 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d3[\"mrp_missing\"] = np.where(d3[\"mrp\"].isna(), 1, 0)\n", "d3[\"price_missing\"] = np.where(d3[\"price\"].isna(), 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# d3['fe_merchant_product_missing (dashboarding)'] = np.where(d3['product_id'].isna(),1,0)\n", "d3[\"fe_enabled_flag_issue\"] = np.where(d3[\"enabled_flag\"] == 0, 1, 0)\n", "d3[\"inventory_limit_fe_issue\"] = np.where(d3[\"inventory_limit\"] == 0, 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d3.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st = time.time()\n", "merchant_mapping = pd.read_sql_query(\n", "    f\"\"\"select vrmm.virtual_merchant_id as f_merchant_id, vrmm.real_merchant_id as b_merchant_id, m.gr_id as backend_gr_id,\n", "        dm.chain_id, dm.chain_name\n", "        from lake_cms.gr_virtual_to_real_merchant_mapping vrmm\n", "        join dwh.dim_merchant dm on dm.merchant_id = vrmm.virtual_merchant_id and is_current = 1\n", "        join lake_cms.view_gr_merchant m on m.id = vrmm.real_merchant_id\n", "        where vrmm.enabled_flag = true and vrmm.virtual_merchant_id in {*f_ids,}\"\"\",\n", "    redshift,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_mapping.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d3.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d4 = d3.merge(\n", "    merchant_mapping, left_on=[\"frontend_id\"], right_on=[\"f_merchant_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d4[\"be_fe_merchant_mappping_missing\"] = np.where(d4[\"b_merchant_id\"].isna(), 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d4.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b_merchants = d4.b_merchant_id.fillna(0).unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st = time.time()\n", "b_merchant_product = pd.DataFrame()\n", "for i in range(0, len(b_merchants), int(math.sqrt(len(b_merchants)))):\n", "    # print({*b_merchants[i:i+int(math.sqrt(len(b_merchants)))],})\n", "    b_temp = pd.read_sql_query(\n", "        f\"\"\"select merchant_id, product_id, inventory_limit, master_inventory_limit, \n", "                                        enabled_flag, priority_order, pp.price, pp.mrp\n", "                                        from lake_cms.gr_merchant_product_mapping mp\n", "                                        LEFT JOIN lake_pricing_v3.pricing_domain_prices pp ON pp.frontend_id = mp.merchant_id\n", "                                        AND pp.cms_product_id = mp.product_id\n", "                                        where merchant_id in {*b_merchants[i:i+int(math.sqrt(len(b_merchants)))],}\"\"\",\n", "        redshift,\n", "    )\n", "    b_merchant_product = pd.concat([b_merchant_product, b_temp], axis=0)\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b_merchant_product.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d5 = d4.merge(\n", "    b_merchant_product,\n", "    left_on=[\"b_merchant_id\", \"product_id\"],\n", "    right_on=[\"merchant_id\", \"product_id\"],\n", "    how=\"left\",\n", "    suffixes=(\"_fe\", \"_be\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d5[\"be_merchant_product_missing (store mapping)\"] = np.where(\n", "    d5[\"merchant_id_be\"].isna(), 1, 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d5[\"inventory_limit_be_issue\"] = np.where(d5[\"inventory_limit_be\"] == 0, 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d5[\"inventory_limit_be_fe_mismatch_issue\"] = np.where(\n", "    d5[\"inventory_limit_be\"] != d5[\"inventory_limit_fe\"], 1, 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st = time.time()\n", "prod_map = pd.read_sql_query(\n", "    \"\"\"select item_id, product_id, offer_id\n", "                                from lake_view_rpc.item_product_mapping\n", "                                where active = 1\"\"\",\n", "    presto,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d6 = d5.merge(prod_map, left_on=[\"product_id\"], right_on=[\"product_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d6[\"item_product_mapping_missing\"] = np.where(d6[\"item_id\"].isna(), 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st = time.time()\n", "backend_outlet = pd.read_sql_query(\n", "    \"\"\"select  c.outlet_id, c.cms_store, m.name as be_name\n", "       from lake_retail.console_outlet_cms_store c\n", "       join lake_cms.view_gr_merchant m on m.id = c.cms_store \n", "       join dwh.dim_merchant dm on dm.merchant_id = m.id and is_current = 1 and dm.chain_id = 177\n", "       where c.active = 1 and c.cms_update_active = 1 and c.outlet_id in (select id \n", "       from lake_retail.console_outlet where business_type_id = 7)\"\"\",\n", "    redshift,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d7 = d6.merge(\n", "    backend_outlet, left_on=[\"b_merchant_id\"], right_on=[\"cms_store\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d7[\"outlet_be_merchant_missing\"] = np.where(d7[\"outlet_id\"].isna(), 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "st = time.time()\n", "item_inv = pd.read_sql_query(\n", "    \"\"\"select outlet_id, co.name as outlet_name, i.item_id, id.name as item_name, quantity, threshold_quantity,\n", "    (case when co.active = 1 and co.device_id <> 47 then 1 else 0 end) as outlet_flag\n", "    from lake_view_ims.ims_item_inventory i\n", "    join lake_rpc.item_details id on id.item_id = i.item_id and perishable = 1\n", "    join lake_retail.console_outlet co on co.id =  i.outlet_id and business_type_id = 7\n", "    where i.active = 1 and quantity > 0\"\"\",\n", "    presto,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d8 = d7.merge(\n", "    item_inv,\n", "    left_on=[\"outlet_id\", \"item_id\"],\n", "    right_on=[\"outlet_id\", \"item_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d8.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d8[\"positive_inventory\"] = np.where(d8[\"quantity\"].isna(), 0, 1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d8.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d8[\"inventory_limit_be_issue\"] = np.where(\n", "    d8[\"positive_inventory\"] == 0, 0, d8[\"inventory_limit_be_issue\"]\n", ")\n", "d8[\"inventory_limit_fe_issue\"] = np.where(\n", "    d8[\"positive_inventory\"] == 0, 0, d8[\"inventory_limit_fe_issue\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d9 = d8[\n", "    [\n", "        \"frontend_id\",\n", "        \"frontend_name\",\n", "        \"frontend_gr_id\",\n", "        \"b_merchant_id\",\n", "        \"backend_gr_id\",\n", "        \"product_id\",\n", "        \"product_name\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"product_type\",\n", "        \"product_enabled_flag\",\n", "        \"product_category_missing_in_merchant\",\n", "        \"product_category_missing\",\n", "        \"mrp_missing\",\n", "        \"price_missing\",\n", "        \"fe_enabled_flag_issue\",\n", "        \"inventory_limit_fe_issue\",\n", "        \"be_fe_merchant_mappping_missing\",\n", "        \"be_merchant_product_missing (store mapping)\",\n", "        \"inventory_limit_be_issue\",\n", "        \"inventory_limit_be_fe_mismatch_issue\",\n", "        \"item_product_mapping_missing\",\n", "        \"outlet_be_merchant_missing\",\n", "        \"positive_inventory\",\n", "    ]\n", "].rename(columns={\"f_ids\": \"frontend_merchant_id\", \"pids\": \"product_id\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d9[\"total_flags\"] = d9[\n", "    [\n", "        \"product_category_missing_in_merchant\",\n", "        \"product_category_missing\",\n", "        \"mrp_missing\",\n", "        \"price_missing\",\n", "        \"fe_enabled_flag_issue\",\n", "        \"inventory_limit_fe_issue\",\n", "        \"be_fe_merchant_mappping_missing\",\n", "        \"be_merchant_product_missing (store mapping)\",\n", "        \"inventory_limit_be_issue\",\n", "        \"inventory_limit_be_fe_mismatch_issue\",\n", "        \"item_product_mapping_missing\",\n", "        \"outlet_be_merchant_missing\",\n", "    ]\n", "].sum(axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d9.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d9.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d9 = d9[d9[\"total_flags\"] > 0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d9.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d9.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# search indexing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "import time"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# url = f\"http://172.31.94.54:5001/product-indexing/all-merchants/{productID}\"\n", "# response = requests.request(\"GET\", url)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1 = pd.DataFrame()\n", "for productID in d9.product_id.fillna(0).unique():\n", "    if productID != 0:\n", "        url = (\n", "            f\"http://172.31.94.54:5001/product-indexing/all-merchants/{int(productID)}\"\n", "        )\n", "        response = requests.request(\"GET\", url)\n", "        # time.sleep(1)\n", "        print(productID, response.json()[\"Missing\"])\n", "        if not (\n", "            response.json()[\"Missing\"] == \"No mapping found for product ID\"\n", "            or len(response.json()[\"Missing\"]) == 0\n", "        ):\n", "            frontends = pd.read_sql_query(\n", "                \"\"\"select merchant_id from lake_cms.gr_merchant_product_mapping \n", "                                      where id in ({missing})\"\"\".format(\n", "                    missing=\",\".join([str(x) for x in response.json()[\"Missing\"]])\n", "                ),\n", "                redshift,\n", "            ).merchant_id.unique()\n", "            for i in frontends:\n", "                df = pd.DataFrame({\"product_id\": [productID], \"frontend_id\": [i]})\n", "                df1 = pd.concat([df1, df], axis=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1[\"indexed\"] = 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d9.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d10 = d9.merge(df1, on=[\"product_id\", \"frontend_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d10[\"not_indexed_flag\"] = np.where(d10[\"indexed\"] == 0, 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d10 = d10.drop(columns=[\"indexed\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d10 = d10[(~d10[\"frontend_id\"].isna()) & (~d10[\"product_id\"].isna())]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d11 = d10[\n", "    (d10[\"total_flags\"] == 0)\n", "    & (d10[\"positive_inventory\"] == 1)\n", "    & (d10[\"not_indexed_flag\"] == 1)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d11.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# alerts"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d11.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["a = d11.frontend_id.nunique()\n", "b = d11.product_id.nunique()\n", "c = d11[[\"frontend_id\", \"product_id\"]].drop_duplicates().shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d11[[\"frontend_id\", \"product_id\"]].to_csv(\"indexing_issues.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "from slack_sdk import WebClient\n", "from slack_sdk.errors import SlackApiError\n", "\n", "text = f\"Search Indexing Report for Valentine's Day Products (Report generated at {now}) with positive inventory and NO CMS issues:\"\n", "text1 = f\"Unique Frontend Merchants not indexed: {a}, Unique Product ID's not indexed: {b}, Unique Mappings not indexed: {c}\"\n", "text2 = \"To check the raw data, use this sheet: https://docs.google.com/spreadsheets/d/1-v_AMKltNY447GWneaM_x_3cNkmNKF5IEIUAiZvK9lc\"\n", "# fig, ax = render_mpl_table(fc_level_df, header_columns=0)\n", "# fig.savefig(\"STO_delayed_report.png\")\n", "token = \"******************************************************\"\n", "client = WebClient(token=token)\n", "file_check = \"./indexing_issues.csv\"\n", "channel = \"indexing-misses\"\n", "try:\n", "    response = client.chat_postMessage(channel=channel, text=text)\n", "    response = client.chat_postMessage(channel=channel, text=text1)\n", "    response = client.chat_postMessage(channel=channel, text=text2)\n", "    filepath = file_check\n", "    response = client.files_upload(channels=channel, file=filepath)\n", "except SlackApiError as e:\n", "    assert e.response[\"ok\"] is False\n", "    assert e.response[\"error\"]  # str like 'invalid_auth', 'channel_not_found'\n", "    print(f\"Got an error: {e.response['error']}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(d10, \"1-v_AMKltNY447GWneaM_x_3cNkmNKF5IEIUAiZvK9lc\", \"Data (with flags)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}