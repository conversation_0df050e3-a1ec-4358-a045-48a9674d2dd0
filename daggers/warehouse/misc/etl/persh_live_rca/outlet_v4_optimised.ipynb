{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime, time\n", "import pytz\n", "\n", "pd.options.display.max_columns = 100\n", "import math\n", "\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")\n", "red_con = pb.get_connection(\"redpen\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Same query from aurora https://redash-queries.grofers.com/queries/209312?p_Product%20Id=475166&p_Frontend%20Merchant%20Id=29629,%2029566,%2029735,%2029950"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Forward (outlet item tracker)"]}, {"cell_type": "raw", "metadata": {}, "source": ["Note: one FE and product id -> can have multiple BE but priority order = 1 will be live. \n", "    \n", "Reasons for not live: (start with outlet and item as starting point (with positive inventory))\n", "    Phase 1:\n", "        1. Backend merchant is not mapped to outlet \n", "        2. Backend merchant product mapping is missing \n", "        3. BE mapped to FE (merchant mapping issues) \n", "        4. FE to product mapping is missing\n", "        5. Item product mapping issue\n", "        6. check if inventory limit = 0 (either at BE product or FE product)\n", "        7. price or mrp missing at BE or FE\n", "        8. product doesnt have primary category (is_primary in product_category_mapping table)\n", "        9. category of product not mapped to category of merchant\n", "    Phase 2:\n", "        1. search: identify if indexing is done or not for positive inventory at outlet and item level (connect with search team)\n", "           (cover reasons from the search team)\n", "\n", "1. Backend merchant is not mapped to outlet -  Done\n", "2. Item product mapping issue - Done\n", "3. Backend merchant product mapping is missing - Done\n", "4. BE mapped to FE (merchant mapping issues) - Done\n", "5. FE to product mapping is missing - Done\n", "5.1 Add enabled flag as reason (enabled_flag in f_merchant_product) - Done\n", "6. check if inventory limit = 0 (either at BE product or FE product) - Done\n", "7. price or mrp missing at BE or FE - Done\n", "8. product doesnt have primary category (is_primary in product_category_mapping table) - Done\n", "9. category of product not mapped to category of merchant - Done\n", "10. This is exactly same as outlet_v2 except that this notebook has been optimised by replacing the loops in v2 by using temp tables created (in this notebook). Rest everything remains same."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "st = time.time()\n", "item_inv = pd.read_sql_query(\n", "    \"\"\"SELECT i.outlet_id,\n", "              co.name as outlet_name,\n", "              pam.product_id,\n", "              ipm.item_id,\n", "              id.name as item_name,\n", "              quantity,\n", "              threshold_quantity,\n", "              (case when co.active = 1 and co.device_id <> 47 then 1 else 0 end) as outlet_flag,\n", "              id.perishable AS item_perishable,\n", "              p.enabled_flag as product_enabled_flag\n", "          \n", "FROM lake_cms.gr_product_attribute_mapping AS pam\n", "LEFT JOIN lake_cms.gr_product AS p ON p.id = pam.product_id\n", "LEFT JOIN lake_rpc.item_product_mapping AS ipm ON ipm.product_id = p.id\n", "LEFT JOIN lake_rpc.item_details AS id ON id.item_id = ipm.item_id\n", "LEFT JOIN lake_ims.ims_item_inventory i ON id.item_id=i.item_id\n", "LEFT JOIN lake_retail.console_outlet co on co.id =  i.outlet_id and business_type_id = 7\n", "WHERE pam.attribute_id = 777\n", "  AND pam.value = 'Perishables'\n", "  AND ipm.active = 1\n", "  AND ipm.offer_id is null\n", "  AND i.quantity>0\n", "  AND i.active=1\"\"\",\n", "    redshift,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_inv.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st = time.time()\n", "backend_outlet = pd.read_sql_query(\n", "    \"\"\"select  c.outlet_id, c.cms_store, m.name as be_name, m.gr_id as backend_gr_id\n", "        from lake_retail.console_outlet_cms_store c\n", "        join lake_cms.view_gr_merchant m on m.id = c.cms_store \n", "        join dwh.dim_merchant dm on dm.merchant_id = m.id and is_current = 1 and dm.chain_id = 177\n", "        where c.active = 1 and c.cms_update_active = 1 and c.outlet_id in (select id \n", "        from lake_retail.console_outlet where business_type_id = 7)\"\"\",\n", "    redshift,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b_merchants = tuple(backend_outlet.cms_store.unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len_b = len(b_merchants)\n", "sqrt_b = int(math.sqrt(len(b_merchants)))\n", "diff_b = len_b % sqrt_b\n", "if diff_b == 1:\n", "    b_merchants.append(0)\n", "print(len(b_merchants))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(b_merchants)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["red_con.execute(\"\"\"DROP TABLE IF EXISTS playground.backend_merchant_ids\"\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st = time.time()\n", "red_con.execute(\n", "    f\"\"\"CREATE TABLE playground.backend_merchant_ids as select DISTINCT merchant_id, product_id, inventory_limit, master_inventory_limit, \n", "        enabled_flag, priority_order\n", "        from lake_cms.gr_merchant_product_mapping\n", "        where merchant_id in {b_merchants}\"\"\"\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st = time.time()\n", "b_merchant_product = pd.read_sql_query(\n", "    \"\"\"select merchant_id, product_id, inventory_limit, master_inventory_limit, \n", "        enabled_flag, priority_order, pp.price, pp.mrp\n", "        from playground.backend_merchant_ids mp\n", "        LEFT JOIN lake_pricing_v3.pricing_domain_prices pp\n", "        ON  mp.merchant_id = pp.frontend_id\n", "        AND  mp.product_id = pp.cms_product_id\n", "        \"\"\",\n", "    redshift,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# st = time.time()\n", "# b_merchant_product = pd.DataFrame()\n", "# for i in range(0, len(b_merchants), int(math.sqrt(len(b_merchants)))):\n", "#     # print(b_merchants[i : i + int(math.sqrt(len(b_merchants)))])\n", "#     b_temp = pd.read_sql_query(\n", "#         f\"\"\"select merchant_id, product_id, inventory_limit, master_inventory_limit,\n", "#             enabled_flag, priority_order, pp.price, pp.mrp\n", "#             from lake_cms.gr_merchant_product_mapping mp\n", "#             LEFT JOIN lake_pricing_v3.pricing_domain_prices pp ON pp.frontend_id = mp.merchant_id\n", "#             AND pp.cms_product_id = mp.product_id\n", "#             where merchant_id in {*b_merchants[i:i+int(math.sqrt(len(b_merchants)))],}\"\"\",\n", "#         redshift,\n", "#     )\n", "#     b_merchant_product = pd.concat([b_merchant_product, b_temp], axis=0)\n", "# print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b_merchant_product.priority_order.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st = time.time()\n", "merchant_mapping = pd.read_sql_query(\n", "    f\"\"\"select vrmm.virtual_merchant_id as f_merchant_id, vm.gr_id  as frontend_gr_id,\n", "    vrmm.real_merchant_id as b_merchant_id, dm.chain_id, dm.chain_name\n", "    from lake_cms.gr_virtual_to_real_merchant_mapping vrmm\n", "    join dwh.dim_merchant dm on dm.merchant_id = vrmm.virtual_merchant_id and is_current = 1\n", "    join lake_cms.view_gr_merchant vm on vm.id = dm.merchant_id\n", "    where vrmm.enabled_flag = true and \n", "     vrmm.real_merchant_id in {*b_merchants,}\"\"\",\n", "    redshift,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c1 = item_inv.merge(backend_outlet, on=[\"outlet_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c1[\"outlet_be_merchant_missing\"] = np.where(c1[\"cms_store\"].isna(), 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c1.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c1[\"item_product_mapping_missing\"] = np.where(c1[\"item_id\"].isna(), 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c1[\"item_perishable\"] = np.where(c1[\"item_perishable\"] == 1, True, False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c2 = c1.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c2.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c3 = c2.merge(\n", "    b_merchant_product,\n", "    left_on=[\"cms_store\", \"product_id\"],\n", "    right_on=[\"merchant_id\", \"product_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c3[\"be_merchant_product_missing (store mapping)\"] = np.where(\n", "    c3[\"merchant_id\"].isna(), 1, 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c3.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c4 = c3.merge(\n", "    merchant_mapping, left_on=[\"cms_store\"], right_on=[\"b_merchant_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c4[\"be_fe_merchant_mappping_missing\"] = np.where(\n", "    c4[\"f_merchant_id\"].isna() & (~c4[\"cms_store\"].isna()), 1, 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c4.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excepts = [\n", "    31151,\n", "    31141,\n", "    31142,\n", "    31143,\n", "    31144,\n", "    31145,\n", "    31146,\n", "    31147,\n", "    31148,\n", "    31150,\n", "    31180,\n", "    31161,\n", "    31162,\n", "    31163,\n", "    31164,\n", "    31165,\n", "    31168,\n", "    31169,\n", "    31170,\n", "    31171,\n", "    28663,\n", "    28800,\n", "    28614,\n", "    26511,\n", "    26782,\n", "    26255,\n", "    26942,\n", "    30978,\n", "    29701,\n", "    26943,\n", "    26253,\n", "    29981,\n", "    26389,\n", "    27364,\n", "    26528,\n", "    29704,\n", "    28864,\n", "    28696,\n", "    28755,\n", "    28798,\n", "    26780,\n", "    26171,\n", "    26049,\n", "    30963,\n", "    26383,\n", "    25568,\n", "    26379,\n", "    29749,\n", "    28822,\n", "    29664,\n", "    28809,\n", "    28734,\n", "    28707,\n", "    29705,\n", "    28683,\n", "    28706,\n", "    30634,\n", "    31594,\n", "    29832,\n", "    26197,\n", "    29877,\n", "    30021,\n", "    28472,\n", "    29846,\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c4 = c4[~c4[\"f_merchant_id\"].isin(excepts)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f_merchants = tuple(c4[\"f_merchant_id\"].fillna(0).astype(int).unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len_f = len(f_merchants)\n", "sqrt_f = int(math.sqrt(len(f_merchants)))\n", "diff_f = len_f % sqrt_f\n", "if diff_f == 1:\n", "    f_merchants.append(0)\n", "print(len(f_merchants))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(f_merchants)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c4.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cats = pd.read_sql_query(\n", "    \"\"\"\n", "   select distinct P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "          C.NAME AS L0,\n", "          C.ID as L0_id, \n", "          (case when C1.NAME = C.name then C2.ID else C1.ID end) AS L1_id,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name as product_type\n", "   from lake_cms.gr_product P\n", "   INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   INNER JOIN lake_cms.gr_product_type pt on p.type_id=pt.id\"\"\",\n", "    redshift,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cats[[\"pid\", \"l0\", \"l1\", \"l2\", \"product_type\"]].drop_duplicates().shape, cats[\n", "    [\"pid\", \"l0\", \"l1\", \"l2\", \"product_type\"]\n", "].shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c4 = c4.merge(\n", "    cats[[\"pid\", \"l0\", \"l1\", \"l2\", \"product_type\"]],\n", "    left_on=\"product_id\",\n", "    right_on=\"pid\",\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c4.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["red_con.execute(\"\"\"DROP TABLE IF EXISTS playground.frontend_merchant_ids\"\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st = time.time()\n", "red_con.execute(\n", "    f\"\"\"CREATE TABLE playground.frontend_merchant_ids as select DISTINCT merchant_id, product_id, inventory_limit, master_inventory_limit, \n", "        enabled_flag, priority_order\n", "        from lake_cms.gr_merchant_product_mapping\n", "        where merchant_id in {f_merchants}\"\"\"\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st = time.time()\n", "f_merchant_product = pd.read_sql_query(\n", "    \"\"\"select merchant_id, product_id, inventory_limit,master_inventory_limit, enabled_flag, priority_order,pp.price, pp.mrp\n", "         from playground.frontend_merchant_ids mp\n", "         LEFT JOIN lake_pricing_v3.pricing_domain_prices pp\n", "              ON  mp.merchant_id = pp.frontend_id\n", "             AND   mp.product_id = pp.cms_product_id\n", "        \"\"\",\n", "    redshift,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# st = time.time()\n", "# f_merchant_product = pd.DataFrame()\n", "# for i in range(0, len(f_merchants), int(math.sqrt(len(f_merchants)))):\n", "#     # print(f_merchants[i : i + int(math.sqrt(len(f_merchants)))])\n", "#     fmp_temp = pd.read_sql_query(\n", "#         f\"\"\"select merchant_id, product_id, inventory_limit,master_inventory_limit, enabled_flag, priority_order, pp.price, pp.mrp\n", "#          from lake_cms.gr_merchant_product_mapping mp\n", "#          LEFT JOIN (select frontend_id,cms_product_id,mrp,price from lake_pricing_v3.pricing_domain_prices) pp\n", "#               ON  mp.merchant_id = pp.frontend_id\n", "#             AND   mp.product_id = pp.cms_product_id\n", "#          where merchant_id in {*f_merchants[i:i+int(math.sqrt(len(f_merchants)))],}\"\"\",\n", "#         redshift,\n", "#     )\n", "#     f_merchant_product = pd.concat([f_merchant_product, fmp_temp], axis=0)\n", "# print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f_merchant_product.priority_order.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c4.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c5 = c4.merge(\n", "    f_merchant_product,\n", "    left_on=[\"f_merchant_id\", \"product_id\"],\n", "    right_on=[\"merchant_id\", \"product_id\"],\n", "    how=\"left\",\n", "    suffixes=(\"_be\", \"_fe\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c5.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c5[\"fe_merchant_product_missing (dashboarding)\"] = np.where(\n", "    c5[\"merchant_id_fe\"].isna(), 1, 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c5[\"fe_enabled_flag_issue\"] = np.where(c5[\"enabled_flag_fe\"] == 0, 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c5[\"inventory_limit_be_issue\"] = np.where((c5[\"inventory_limit_be\"] == 0), 1, 0)\n", "c5[\"inventory_limit_fe_issue\"] = np.where((c5[\"inventory_limit_fe\"] == 0), 1, 0)\n", "c5[\"inventory_limit_be_fe_mismatch_issue\"] = np.where(\n", "    c5[\"inventory_limit_be\"] != c5[\"inventory_limit_fe\"], 1, 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c5[\"price_missing\"] = np.where((c5[\"price_be\"].isna()) & (c5[\"price_fe\"].isna()), 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c5[\"mrp_missing\"] = np.where((c5[\"mrp_be\"].isna()) & (c5[\"mrp_fe\"].isna()), 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st = time.time()\n", "prodct_cat = pd.read_sql_query(\n", "    \"\"\"select product_id, category_id from\n", "                                  lake_cms.gr_product_category_mapping \n", "                                  where is_primary = 1\"\"\",\n", "    redshift,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c6 = c5.merge(prodct_cat, on=[\"product_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c6.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### mutliple categories could be present, but check if there is ateast one with is_primary = 1\n", "c6[\"product_category_missing\"] = np.where(c6[\"category_id\"].isna(), 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st = time.time()\n", "merchant_cat = pd.read_sql_query(\n", "    \"\"\"select merchant_id, category_id from\n", "                                  lake_cms.gr_merchant_category_mapping\"\"\",\n", "    redshift,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c7 = c6.merge(\n", "    merchant_cat,\n", "    left_on=[\"f_merchant_id\", \"category_id\"],\n", "    right_on=[\"merchant_id\", \"category_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c7[\"product_category_missing_in_merchant\"] = np.where(c7[\"merchant_id\"].isna(), 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c7.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c7[\n", "    [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"f_merchant_id\",\n", "        \"frontend_gr_id\",\n", "        \"backend_gr_id\",\n", "        \"chain_id\",\n", "        \"chain_name\",\n", "        \"product_id\",\n", "        \"item_perishable\",\n", "        \"product_enabled_flag\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"product_type\",\n", "        \"quantity\",\n", "        \"inventory_limit_fe\",\n", "        \"outlet_be_merchant_missing\",\n", "        \"item_product_mapping_missing\",\n", "        \"be_merchant_product_missing (store mapping)\",\n", "        \"be_fe_merchant_mappping_missing\",\n", "        \"fe_merchant_product_missing (dashboarding)\",\n", "        \"fe_enabled_flag_issue\",\n", "        \"inventory_limit_be_issue\",\n", "        \"inventory_limit_fe_issue\",\n", "        \"inventory_limit_be_fe_mismatch_issue\",\n", "        \"price_missing\",\n", "        \"mrp_missing\",\n", "        \"product_category_missing\",\n", "        \"product_category_missing_in_merchant\",\n", "    ]\n", "].head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c7[(c7[\"product_category_missing_in_merchant\"] == 1) & ~c7[\"f_merchant_id\"].isna()][\n", "    [\"outlet_id\", \"item_id\", \"f_merchant_id\", \"product_id\", \"quantity\"]\n", "].head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Indexing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "import time"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1 = pd.DataFrame()\n", "for productID in c7.product_id.fillna(0).unique():\n", "    if productID != 0:\n", "        url = (\n", "            f\"http://172.31.94.54:5001/product-indexing/all-merchants/{int(productID)}\"\n", "        )\n", "\n", "        try:\n", "            response = requests.request(\"GET\", url)\n", "            # time.sleep(1)\n", "            # print(productID, response.json()[\"Missing\"])\n", "        except:\n", "            continue\n", "        if not (\n", "            response.json()[\"Missing\"] == \"No mapping found for product ID\"\n", "            or len(response.json()[\"Missing\"]) == 0\n", "        ):\n", "            frontends = pd.read_sql_query(\n", "                \"\"\"select merchant_id from lake_cms.gr_merchant_product_mapping \n", "                                      where id in ({missing})\"\"\".format(\n", "                    missing=\",\".join([str(x) for x in response.json()[\"Missing\"]])\n", "                ),\n", "                redshift,\n", "            ).merchant_id.unique()\n", "            for i in frontends:\n", "                df = pd.DataFrame({\"product_id\": [productID], \"f_merchant_id\": [i]})\n", "                df1 = pd.concat([df1, df], axis=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if \"product_id\" in df1:\n", "    df1[\"indexed\"] = 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if \"product_id\" in df1:\n", "    c8 = c7.merge(df1, on=[\"product_id\", \"f_merchant_id\"], how=\"left\")\n", "    c8[\"not_indexed_flag\"] = np.where(c8[\"indexed\"] == 0, 1, 0)\n", "    c8 = c8.drop(columns=[\"indexed\"])\n", "    c8 = c8[(~c8[\"f_merchant_id\"].isna()) & (~c8[\"product_id\"].isna())]\n", "else:\n", "    c8 = c7.copy()\n", "    c8 = c8[(~c8[\"f_merchant_id\"].isna()) & (~c8[\"product_id\"].isna())]\n", "    c8[\"not_indexed_flag\"] = \"N/A\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "    c8[\"total_flags\"] = np.sum(\n", "        c8[\n", "            [\n", "                \"outlet_be_merchant_missing\",\n", "                \"item_product_mapping_missing\",\n", "                \"be_merchant_product_missing (store mapping)\",\n", "                \"be_fe_merchant_mappping_missing\",\n", "                \"fe_merchant_product_missing (dashboarding)\",\n", "                \"fe_enabled_flag_issue\",\n", "                \"inventory_limit_be_issue\",\n", "                \"inventory_limit_fe_issue\",\n", "                \"inventory_limit_be_fe_mismatch_issue\",\n", "                \"price_missing\",\n", "                \"mrp_missing\",\n", "                \"product_category_missing\",\n", "                \"product_category_missing_in_merchant\",\n", "                \"not_indexed_flag\",\n", "            ]\n", "        ],\n", "        axis=1,\n", "    )\n", "except:\n", "    c8[\"total_flags\"] = np.sum(\n", "        c8[\n", "            [\n", "                \"outlet_be_merchant_missing\",\n", "                \"item_product_mapping_missing\",\n", "                \"be_merchant_product_missing (store mapping)\",\n", "                \"be_fe_merchant_mappping_missing\",\n", "                \"fe_merchant_product_missing (dashboarding)\",\n", "                \"fe_enabled_flag_issue\",\n", "                \"inventory_limit_be_issue\",\n", "                \"inventory_limit_fe_issue\",\n", "                \"inventory_limit_be_fe_mismatch_issue\",\n", "                \"price_missing\",\n", "                \"mrp_missing\",\n", "                \"product_category_missing\",\n", "                \"product_category_missing_in_merchant\",\n", "            ]\n", "        ],\n", "        axis=1,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c8.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c8[\"light_house_link\"] = (\n", "    \"https://lighthouse.grofers.com/dashboard/merchants/\"\n", "    + c8[\"f_merchant_id\"].fillna(0).astype(int).astype(str)\n", "    + \"/products/\"\n", "    + c8[\"product_id\"].fillna(0).astype(int).astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c9 = c8[\n", "    [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"outlet_flag\",\n", "        \"b_merchant_id\",\n", "        \"f_merchant_id\",\n", "        \"frontend_gr_id\",\n", "        \"backend_gr_id\",\n", "        \"chain_id\",\n", "        \"chain_name\",\n", "        \"product_id\",\n", "        \"item_perishable\",\n", "        \"product_enabled_flag\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"product_type\",\n", "        \"quantity\",\n", "        \"inventory_limit_fe\",\n", "        \"outlet_be_merchant_missing\",\n", "        \"item_product_mapping_missing\",\n", "        \"be_merchant_product_missing (store mapping)\",\n", "        \"be_fe_merchant_mappping_missing\",\n", "        \"fe_merchant_product_missing (dashboarding)\",\n", "        \"fe_enabled_flag_issue\",\n", "        \"inventory_limit_be_issue\",\n", "        \"inventory_limit_fe_issue\",\n", "        \"inventory_limit_be_fe_mismatch_issue\",\n", "        \"price_missing\",\n", "        \"mrp_missing\",\n", "        \"product_category_missing\",\n", "        \"product_category_missing_in_merchant\",\n", "        \"not_indexed_flag\",\n", "        \"total_flags\",\n", "        \"light_house_link\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c9[c9[\"total_flags\"] == 0].product_id.sample()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c10 = c9[c9[\"total_flags\"] > 0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c10 = c10.sort_values([\"item_product_mapping_missing\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c10.drop(c10[c10.chain_name == \"Long Slot Store\"].index, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c10 = c10[~c10[\"f_merchant_id\"].isin(excepts)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# renaming certain columns as per <PERSON><PERSON>'s request\n", "c10 = c10.rename(\n", "    columns={\n", "        \"b_merchant_id\": \"b_store_id\",\n", "        \"f_merchant_id\": \"f_store_id\",\n", "        \"frontend_gr_id\": \"frontend_merchant_id\",\n", "        \"backend_gr_id\": \"backend_merchant_id\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(c10, \"1Xprf8jcGGm2cSiBH8yKvssIFbboM8NIi30p_6admAww\", \"cms basis\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os._exit(00)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON>s"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c11 = c10[\n", "    (c10[\"total_flags\"] == 0) & (c10[\"quantity\"] > 0) & (c10[\"not_indexed_flag\"] == 1)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["a = c11.f_store_id.nunique()\n", "b = c11.product_id.nunique()\n", "c = c11[[\"f_store_id\", \"product_id\"]].drop_duplicates().shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c11[[\"f_store_id\", \"product_id\"]].to_csv(\"indexing_issues.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, time\n", "import pytz\n", "\n", "tz = pytz.timezone(\"Asia/Calcutta\")\n", "now = datetime.now(tz)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "from slack_sdk import WebClient\n", "from slack_sdk.errors import SlackApiError\n", "\n", "text = f\"Search Indexing Report for Perishable Products (Report generated at {now}) with positive inventory and NO CMS issues:\"\n", "text1 = f\"Unique Frontend Merchants not indexed: {a}, Unique Product ID's not indexed: {b}, Unique Mappings not indexed: {c}\"\n", "text2 = \"To check the raw data, use this sheet: https://docs.google.com/spreadsheets/d/1Xprf8jcGGm2cSiBH8yKvssIFbboM8NIi30p_6admAww/edit#gid=1324557957\"\n", "# fig, ax = render_mpl_table(fc_level_df, header_columns=0)\n", "# fig.savefig(\"STO_delayed_report.png\")\n", "token = \"******************************************************\"\n", "client = WebClient(token=token)\n", "file_check = \"./indexing_issues.csv\"\n", "channel = \"indexing-misses\"\n", "try:\n", "    response = client.chat_postMessage(channel=channel, text=text)\n", "    response = client.chat_postMessage(channel=channel, text=text1)\n", "    response = client.chat_postMessage(channel=channel, text=text2)\n", "    filepath = file_check\n", "    response = client.files_upload(channels=channel, file=filepath)\n", "except SlackApiError as e:\n", "    assert e.response[\"ok\"] is False\n", "    assert e.response[\"error\"]  # str like 'invalid_auth', 'channel_not_found'\n", "    print(f\"Got an error: {e.response['error']}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# include item_perishable and non_indexed in total_flags count? It wasn't done before\n", "# indexing based alerts to be activated or not/"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}