{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "\n", "pd.options.display.max_columns = 100\n", "import math\n", "\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Same query from aurora https://redash-queries.grofers.com/queries/209312?p_Product%20Id=475166&p_Frontend%20Merchant%20Id=29629,%2029566,%2029735,%2029950"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Forward (outlet item tracker)"]}, {"cell_type": "raw", "metadata": {}, "source": ["Note: one FE and product id -> can have multiple BE but priority order = 1 will be live. \n", "    \n", "Reasons for not live: (start with outlet and item as starting point (with positive inventory))\n", "    Phase 1:\n", "        1. Backend merchant is not mapped to outlet \n", "        2. Backend merchant product mapping is missing \n", "        3. BE mapped to FE (merchant mapping issues) \n", "        4. FE to product mapping is missing\n", "        5. Item product mapping issue\n", "        6. check if inventory limit = 0 (either at BE product or FE product)\n", "        7. price or mrp missing at BE or FE\n", "        8. product doesnt have primary category (is_primary in product_category_mapping table)\n", "        9. category of product not mapped to category of merchant\n", "    Phase 2:\n", "        1. search: identify if indexing is done or not for positive inventory at outlet and item level (connect with search team)\n", "           (cover reasons from the search team)\n", "\n", "1. Backend merchant is not mapped to outlet -  Done\n", "2. Item product mapping issue - Done\n", "3. Backend merchant product mapping is missing - Done\n", "4. BE mapped to FE (merchant mapping issues) - Done\n", "5. FE to product mapping is missing - Done\n", "5.1 Add enabled flag as reason (enabled_flag in f_merchant_product) - Done\n", "6. check if inventory limit = 0 (either at BE product or FE product) - Done\n", "7. price or mrp missing at BE or FE - Done\n", "8. product doesnt have primary category (is_primary in product_category_mapping table) - Done\n", "9. category of product not mapped to category of merchant - Done"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "st = time.time()\n", "item_inv = pd.read_sql_query(\n", "    \"\"\"select outlet_id, co.name as outlet_name, i.item_id, id.name as item_name, quantity, threshold_quantity,\n", "    (case when co.active = 1 and co.device_id <> 47 then 1 else 0 end) as outlet_flag\n", "    from lake_view_ims.ims_item_inventory i\n", "    join lake_rpc.item_details id on id.item_id = i.item_id and perishable = 1\n", "    join lake_retail.console_outlet co on co.id =  i.outlet_id and business_type_id = 7\n", "    where i.active = 1 and quantity > 0\"\"\",\n", "    presto,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st = time.time()\n", "prod_map = pd.read_sql_query(\n", "    \"\"\"select item_id, product_id, offer_id, p.enabled_flag as product_enabled_flag\n", "       from lake_rpc.item_product_mapping pm\n", "       left join lake_cms.gr_product p on pm.product_id = p.id\n", "       where active = 1\"\"\",\n", "    redshift,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st = time.time()\n", "backend_outlet = pd.read_sql_query(\n", "    \"\"\"select  c.outlet_id, c.cms_store, m.name as be_name, m.gr_id as backend_gr_id\n", "        from lake_retail.console_outlet_cms_store c\n", "        join lake_cms.view_gr_merchant m on m.id = c.cms_store \n", "        join dwh.dim_merchant dm on dm.merchant_id = m.id and is_current = 1 and dm.chain_id = 177\n", "        where c.active = 1 and c.cms_update_active = 1 and c.outlet_id in (select id \n", "        from lake_retail.console_outlet where business_type_id = 7)\"\"\",\n", "    redshift,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b_merchants = list(backend_outlet.cms_store.unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(b_merchants)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st = time.time()\n", "b_merchant_product = pd.DataFrame()\n", "for i in range(0, len(b_merchants), int(math.sqrt(len(b_merchants)))):\n", "    print(b_merchants[i : i + int(math.sqrt(len(b_merchants)))])\n", "    b_temp = pd.read_sql_query(\n", "        f\"\"\"select merchant_id, product_id, inventory_limit, master_inventory_limit, \n", "            enabled_flag, priority_order, pp.price, pp.mrp\n", "            from lake_cms.gr_merchant_product_mapping mp\n", "            LEFT JOIN lake_pricing_v3.pricing_domain_prices pp ON pp.frontend_id = mp.merchant_id\n", "            AND pp.cms_product_id = mp.product_id\n", "            where merchant_id in {*b_merchants[i:i+int(math.sqrt(len(b_merchants)))],}\"\"\",\n", "        redshift,\n", "    )\n", "    b_merchant_product = pd.concat([b_merchant_product, b_temp], axis=0)\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b_merchant_product.priority_order.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st = time.time()\n", "merchant_mapping = pd.read_sql_query(\n", "    f\"\"\"select vrmm.virtual_merchant_id as f_merchant_id, vm.gr_id  as frontend_gr_id,\n", "    vrmm.real_merchant_id as b_merchant_id, dm.chain_id, dm.chain_name\n", "    from lake_cms.gr_virtual_to_real_merchant_mapping vrmm\n", "    join dwh.dim_merchant dm on dm.merchant_id = vrmm.virtual_merchant_id and is_current = 1\n", "    join lake_cms.view_gr_merchant vm on vm.id = dm.merchant_id\n", "    where vrmm.enabled_flag = true and vrmm.real_merchant_id in {*b_merchants,}\"\"\",\n", "    redshift,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c1 = item_inv.merge(backend_outlet, on=[\"outlet_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c1[\"outlet_be_merchant_missing\"] = np.where(c1[\"cms_store\"].isna(), 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c1.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c2 = c1.merge(prod_map, on=[\"item_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c2[\"item_product_mapping_missing\"] = np.where(\n", "    c2[\"product_id\"].isna() & c2[\"product_id\"].isna(), 1, 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c2.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c3 = c2.merge(\n", "    b_merchant_product,\n", "    left_on=[\"cms_store\", \"product_id\"],\n", "    right_on=[\"merchant_id\", \"product_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c3[\"be_merchant_product_missing (store mapping)\"] = np.where(\n", "    c3[\"merchant_id\"].isna(), 1, 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c3.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c4 = c3.merge(\n", "    merchant_mapping, left_on=[\"cms_store\"], right_on=[\"b_merchant_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c4[\"be_fe_merchant_mappping_missing\"] = np.where(\n", "    c4[\"f_merchant_id\"].isna() & (~c4[\"cms_store\"].isna()), 1, 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f_merchants = list(c4[\"f_merchant_id\"].fillna(0).astype(int).unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(f_merchants)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c4.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cats = pd.read_sql_query(\n", "    \"\"\"\n", "   select distinct P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "          C.NAME AS L0,\n", "          C.ID as L0_id, \n", "          (case when C1.NAME = C.name then C2.ID else C1.ID end) AS L1_id,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name as product_type\n", "   from lake_cms.gr_product P\n", "   INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   INNER JOIN lake_cms.gr_product_type pt on p.type_id=pt.id\"\"\",\n", "    redshift,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cats[[\"pid\", \"l0\", \"l1\", \"l2\", \"product_type\"]].drop_duplicates().shape, cats[\n", "    [\"pid\", \"l0\", \"l1\", \"l2\", \"product_type\"]\n", "].shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c4 = c4.merge(\n", "    cats[[\"pid\", \"l0\", \"l1\", \"l2\", \"product_type\"]],\n", "    left_on=\"product_id\",\n", "    right_on=\"pid\",\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c4.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st = time.time()\n", "f_merchant_product = pd.DataFrame()\n", "for i in range(0, len(f_merchants), int(math.sqrt(len(f_merchants)))):\n", "    print(f_merchants[i : i + int(math.sqrt(len(f_merchants)))])\n", "    fmp_temp = pd.read_sql_query(\n", "        f\"\"\"select merchant_id, product_id, inventory_limit,master_inventory_limit, enabled_flag, priority_order, pp.price, pp.mrp\n", "         from lake_cms.gr_merchant_product_mapping mp\n", "         LEFT JOIN lake_pricing_v3.pricing_domain_prices pp ON pp.frontend_id = mp.merchant_id\n", "            AND pp.cms_product_id = mp.product_id\n", "         where merchant_id in {*f_merchants[i:i+int(math.sqrt(len(f_merchants)))],}\"\"\",\n", "        redshift,\n", "    )\n", "    f_merchant_product = pd.concat([f_merchant_product, fmp_temp], axis=0)\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f_merchant_product.priority_order.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c4.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c5 = c4.merge(\n", "    f_merchant_product,\n", "    left_on=[\"f_merchant_id\", \"product_id\"],\n", "    right_on=[\"merchant_id\", \"product_id\"],\n", "    how=\"left\",\n", "    suffixes=(\"_be\", \"_fe\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c5[\"fe_merchant_product_missing (dashboarding)\"] = np.where(\n", "    c5[\"merchant_id_fe\"].isna(), 1, 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c5[\"fe_enabled_flag_issue\"] = np.where(c5[\"enabled_flag_fe\"] == 0, 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c5[\"inventory_limit_be_issue\"] = np.where((c5[\"inventory_limit_be\"] == 0), 1, 0)\n", "c5[\"inventory_limit_fe_issue\"] = np.where((c5[\"inventory_limit_fe\"] == 0), 1, 0)\n", "c5[\"inventory_limit_be_fe_mismatch_issue\"] = np.where(\n", "    c5[\"inventory_limit_be\"] != c5[\"inventory_limit_fe\"], 1, 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c5[\"price_missing\"] = np.where((c5[\"price_be\"].isna()) & (c5[\"price_fe\"].isna()), 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c5[\"mrp_missing\"] = np.where((c5[\"mrp_be\"].isna()) & (c5[\"mrp_fe\"].isna()), 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st = time.time()\n", "prodct_cat = pd.read_sql_query(\n", "    \"\"\"select product_id, category_id from\n", "                                  lake_cms.gr_product_category_mapping \n", "                                  where is_primary = 1\"\"\",\n", "    redshift,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c6 = c5.merge(prodct_cat, on=[\"product_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c6.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### mutliple categories could be present, but check if there is ateast one with is_primary = 1\n", "c6[\"product_category_missing\"] = np.where(c6[\"category_id\"].isna(), 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st = time.time()\n", "merchant_cat = pd.read_sql_query(\n", "    \"\"\"select merchant_id, category_id from\n", "                                  lake_cms.gr_merchant_category_mapping\"\"\",\n", "    redshift,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c7 = c6.merge(\n", "    merchant_cat,\n", "    left_on=[\"f_merchant_id\", \"category_id\"],\n", "    right_on=[\"merchant_id\", \"category_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c7[\"product_category_missing_in_merchant\"] = np.where(c7[\"merchant_id\"].isna(), 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c7.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c7[\n", "    [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"f_merchant_id\",\n", "        \"frontend_gr_id\",\n", "        \"backend_gr_id\",\n", "        \"chain_id\",\n", "        \"chain_name\",\n", "        \"product_id\",\n", "        \"product_enabled_flag\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"product_type\",\n", "        \"quantity\",\n", "        \"inventory_limit_fe\",\n", "        \"outlet_be_merchant_missing\",\n", "        \"item_product_mapping_missing\",\n", "        \"be_merchant_product_missing (store mapping)\",\n", "        \"be_fe_merchant_mappping_missing\",\n", "        \"fe_merchant_product_missing (dashboarding)\",\n", "        \"fe_enabled_flag_issue\",\n", "        \"inventory_limit_be_issue\",\n", "        \"inventory_limit_fe_issue\",\n", "        \"inventory_limit_be_fe_mismatch_issue\",\n", "        \"price_missing\",\n", "        \"mrp_missing\",\n", "        \"product_category_missing\",\n", "        \"product_category_missing_in_merchant\",\n", "    ]\n", "].head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c7[\"total_flags\"] = np.sum(\n", "    c7[\n", "        [\n", "            \"outlet_be_merchant_missing\",\n", "            \"item_product_mapping_missing\",\n", "            \"be_merchant_product_missing (store mapping)\",\n", "            \"be_fe_merchant_mappping_missing\",\n", "            \"fe_merchant_product_missing (dashboarding)\",\n", "            \"fe_enabled_flag_issue\",\n", "            \"inventory_limit_be_issue\",\n", "            \"inventory_limit_fe_issue\",\n", "            \"inventory_limit_be_fe_mismatch_issue\",\n", "            \"price_missing\",\n", "            \"mrp_missing\",\n", "            \"product_category_missing\",\n", "            \"product_category_missing_in_merchant\",\n", "        ]\n", "    ],\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c7[(c7[\"product_category_missing_in_merchant\"] == 1) & ~c7[\"f_merchant_id\"].isna()][\n", "    [\"outlet_id\", \"item_id\", \"f_merchant_id\", \"product_id\", \"quantity\"]\n", "].head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c7[\"light_house_link\"] = (\n", "    \"https://lighthouse.grofers.com/dashboard/merchants/\"\n", "    + c7[\"f_merchant_id\"].fillna(0).astype(int).astype(str)\n", "    + \"/products/\"\n", "    + c7[\"product_id\"].fillna(0).astype(int).astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c8 = c7[\n", "    [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"outlet_flag\",\n", "        \"b_merchant_id\",\n", "        \"f_merchant_id\",\n", "        \"frontend_gr_id\",\n", "        \"backend_gr_id\",\n", "        \"chain_id\",\n", "        \"chain_name\",\n", "        \"product_id\",\n", "        \"product_enabled_flag\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"product_type\",\n", "        \"quantity\",\n", "        \"inventory_limit_fe\",\n", "        \"outlet_be_merchant_missing\",\n", "        \"item_product_mapping_missing\",\n", "        \"be_merchant_product_missing (store mapping)\",\n", "        \"be_fe_merchant_mappping_missing\",\n", "        \"fe_merchant_product_missing (dashboarding)\",\n", "        \"fe_enabled_flag_issue\",\n", "        \"inventory_limit_be_issue\",\n", "        \"inventory_limit_fe_issue\",\n", "        \"inventory_limit_be_fe_mismatch_issue\",\n", "        \"price_missing\",\n", "        \"mrp_missing\",\n", "        \"product_category_missing\",\n", "        \"product_category_missing_in_merchant\",\n", "        \"total_flags\",\n", "        \"light_house_link\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c8.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# c10 = c8[c8[\"total_flags\"]==0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c8[c8[\"total_flags\"] == 0].product_id.sample()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c9 = c8[c8[\"total_flags\"] > 0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c9 = c9.sort_values([\"item_product_mapping_missing\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(c9, \"1Xprf8jcGGm2cSiBH8yKvssIFbboM8NIi30p_6admAww\", \"Data (with flags)\")\n", "# pb.to_sheets(c10, \"1Xprf8jcGGm2cSiBH8yKvssIFbboM8NIi30p_6admAww\", \"Data (without flags)\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}