alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: ib1
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: warehouse
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U0413ECJBL4
path: warehouse/misc/etl/ib1
paused: true
project_name: misc
schedule:
  interval: 30 5 * * *
  start_date: '2022-11-09T00:00:00'
schedule_type: fixed
sla: 121 minutes
support_files: []
tags: []
template_name: notebook
version: 1
