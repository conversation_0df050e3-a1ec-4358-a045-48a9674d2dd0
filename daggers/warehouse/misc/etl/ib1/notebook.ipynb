{"cells": [{"cell_type": "code", "execution_count": null, "id": "1aef4f5b-d1db-445d-b4ec-429dc23f6d8a", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import time\n", "import math\n", "import warnings"]}, {"cell_type": "code", "execution_count": null, "id": "2a3ed26b-5736-4aec-918a-f0b5e61a3095", "metadata": {}, "outputs": [], "source": ["presto = pb.get_connection(\"[Warehouse] Presto\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "08bf7ade-3c22-420c-b51c-518df287674d", "metadata": {}, "outputs": [], "source": ["def outlets():\n", "    outlets = \"\"\"\n", "    \n", "    select z.zone,\n", "        rcl.name as city_name,\n", "        om.outlet_id as hot_outlet_id, om.outlet_name, \n", "        om.facility_id, pf.internal_facility_identifier as facility_name,\n", "        case when wom.cloud_store_id is null then om.outlet_id else wom.cloud_store_id end as inv_outlet_id,\n", "        rco.business_type_id,\n", "        case when rco.business_type_id !=7 then 'be' else 'fe' end as taggings\n", "        \n", "            from lake_po.physical_facility_outlet_mapping om\n", "            \n", "            left join (select id, tax_location_id,\n", "                case when id = 581 then 12 else business_type_id end as business_type_id\n", "                    from lake_retail.console_outlet\n", "                        ) rco on rco.id = om.outlet_id\n", "            \n", "            left join (select pf.* from lake_po.physical_facility pf\n", "                join (select facility_id, max(updated_at) as updated_at from lake_po.physical_facility group by 1\n", "                        ) pf2 on pf2.facility_id = pf.facility_id and pf2.updated_at = pf.updated_at\n", "                        ) pf on pf.facility_id = om.facility_id\n", "            \n", "            left join (select distinct warehouse_id, cloud_store_id from lake_retail.warehouse_outlet_mapping \n", "                where active = 1\n", "                        ) wom on wom.warehouse_id = om.outlet_id\n", "            \n", "            left join lake_retail.console_location rcl on rcl.id = rco.tax_location_id\n", "            \n", "            left join (select distinct facility_id, zone from metrics.outlet_zone_mapping where business_type_id in (1,12,7,19,20,21)\n", "                        ) z on z.facility_id = om.facility_id\n", "            \n", "                where rco.business_type_id in (1,12,7,19,20,21) and om.outlet_id not in (0,1739)\n", "                and om.active = 1 and ars_active = 1 and is_primary = 1\n", "                and om.outlet_name not like '%%SSC%%'\n", "                and om.outlet_name not like '%%MODI%%'\n", "                and om.outlet_name not like '%%hot ff%%'\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(outlets, redshift)\n", "\n", "\n", "start = time.time()\n", "outlets = outlets()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "694ab48f-aee1-480b-8a54-a635c9d0d9c2", "metadata": {}, "outputs": [], "source": ["outlets.head()"]}, {"cell_type": "code", "execution_count": null, "id": "75860bd9-7b83-45e2-bc03-7e15e9427af5", "metadata": {}, "outputs": [], "source": ["frontend_outlets = outlets[outlets[\"taggings\"] == \"fe\"].rename(\n", "    columns={\"inv_outlet_id\": \"fe_outlet_id\"}\n", ")\n", "fe_outlet_id_list = list(frontend_outlets[\"fe_outlet_id\"].unique())\n", "fe_outlet_id_list = tuple(fe_outlet_id_list)\n", "len(fe_outlet_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "d59cbdc7-52d7-4edf-9a93-8609067778ae", "metadata": {}, "outputs": [], "source": ["backend_inv_outlets = outlets[outlets[\"taggings\"] == \"be\"].rename(\n", "    columns={\"inv_outlet_id\": \"outlet_id\"}\n", ")\n", "be_inv_outlet_id_list = list(backend_inv_outlets[\"outlet_id\"].unique())\n", "be_inv_outlet_id_list = tuple(be_inv_outlet_id_list)\n", "len(be_inv_outlet_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "d2089632-7799-4b81-b992-b3f4feb8429a", "metadata": {}, "outputs": [], "source": ["backend_hot_outlets = outlets[outlets[\"taggings\"] == \"be\"].rename(\n", "    columns={\"hot_outlet_id\": \"outlet_id\"}\n", ")\n", "be_hot_outlet_id_list = list(backend_hot_outlets[\"outlet_id\"].unique())\n", "be_hot_outlet_id_list = tuple(be_hot_outlet_id_list)\n", "len(be_hot_outlet_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "176417fb-3145-4120-8243-bc3b178541c5", "metadata": {}, "outputs": [], "source": ["def item_details():\n", "    item_details = \"\"\"\n", "    \n", "    with\n", "    fe_details as\n", "    (select ma.facility_id as fe_facility_id, ma.item_id, category\n", "        from metrics.perishable_assortment ma\n", "        \n", "            join (select max(updated_at) as updated_at, item_id, facility_id from metrics.perishable_assortment\n", "                where date(updated_at) between current_date - 18 and current_date\n", "                    group by 2,3\n", "                    ) pa on pa.item_id = ma.item_id and pa.facility_id = ma.facility_id and pa.updated_at = ma.updated_at\n", "    ),\n", "\n", "    item_details as\n", "        (select distinct rpc.item_id,\n", "            case when l0_id = 1487 then 'fnv'\n", "                when l2_id in (1185) then 'milk'\n", "                when l2_id in (1961,1367,63,1732,1733,1734,1369) then 'perishable'\n", "                when perishable = 1 then 'perishable'\n", "                when fd.item_id is not null then 'perishable' else 'packaged goods' end as assortment_type\n", "\n", "                    from lake_rpc.product_product rpc\n", "\n", "                    left join (select item_id, l0_id, l2_id from lake_rpc.item_category_details) cd on cd.item_id = rpc.item_id\n", "                    left join (select item_id from fe_details) fd on fd.item_id = rpc.item_id\n", "\n", "                    left join (select distinct item_id from lake_rpc.item_tag_mapping where active = true\n", "                        and cast(tag_value as int) = 3 and tag_type_id = 3\n", "                            ) i on i.item_id = rpc.item_id\n", "\n", "                        where id in (select max(id) as id from lake_rpc.product_product pp where pp.active = 1 and pp.approved = 1 group by item_id)\n", "                             and i.item_id is null\n", "        )\n", "        \n", "            select * from item_details\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(item_details, redshift)\n", "\n", "\n", "start = time.time()\n", "item_details = item_details()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "11880ed2-dcc6-45f5-a3ef-65d14794de7c", "metadata": {}, "outputs": [], "source": ["item_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b1d076c2-1f92-43f8-a578-937ce8d81b48", "metadata": {}, "outputs": [], "source": ["start = time.time()\n", "\n", "item_remove = pb.from_sheets(\n", "    \"1unpM2R3gWWm9JNvOFoC1JF55x11wR-BaApJlyMeFc8o\",\n", "    \"remove_item\",\n", "    clear_cache=True,\n", ")\n", "\n", "item_remove = (\n", "    item_remove[[\"item_id\"]].drop_duplicates().reset_index().drop(columns={\"index\"})\n", ")\n", "\n", "item_remove = item_remove[~(item_remove[\"item_id\"] == \"\")].astype(int)\n", "\n", "item_remove[\"key\"] = 1\n", "\n", "item_remove.dropna(inplace=True)\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "7d3f9d66-7591-42e4-a36d-c116081799fb", "metadata": {}, "outputs": [], "source": ["final_item_base = pd.merge(item_details, item_remove, on=[\"item_id\"], how=\"left\")\n", "\n", "final_item_base = (\n", "    final_item_base[final_item_base[\"key\"].isnull()]\n", "    .reset_index()\n", "    .drop(columns={\"index\", \"key\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a7e9a0ba-99e0-4b88-83ce-2dc6f24356b0", "metadata": {}, "outputs": [], "source": ["item_details.shape, final_item_base.shape"]}, {"cell_type": "code", "execution_count": null, "id": "aeec42af-dc91-4cb1-a677-f2dcfb4cf587", "metadata": {}, "outputs": [], "source": ["item_id_list = list(final_item_base[\"item_id\"].unique())\n", "item_id_list = tuple(item_id_list)\n", "len(item_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "4a7f024f-3bcd-42e1-9d79-038ba24e1a6d", "metadata": {}, "outputs": [], "source": ["def capacity():\n", "    capacity = f\"\"\"\n", "    \n", "    with\n", "    capacity as\n", "        (select a.outlet_id, a.assortment_type,\n", "        lower(a.type) as type, a.total_capacity, date(a.date) as date_, a.capacity from consumer.inbound_and_outbound_capacity a\n", "\n", "        join (select max(updated_at) as updated_at, outlet_id, assortment_type, type, date from consumer.inbound_and_outbound_capacity\n", "            where total_capacity not in (0) and date >= current_date - 18 group by 2,3,4,5\n", "            ) b on b.updated_at = a.updated_at and b.outlet_id = a.outlet_id and b.assortment_type = a.assortment_type\n", "                and b.type = a.type and b.date = a.date\n", "\n", "                    order by outlet_id, assortment_type, type, date_\n", "        )\n", "        \n", "            select * from capacity\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(capacity, redshift)\n", "\n", "\n", "capacity = capacity()"]}, {"cell_type": "code", "execution_count": null, "id": "36a6fef4-2337-42ac-bc2b-44054a68baaa", "metadata": {}, "outputs": [], "source": ["capacity.head()"]}, {"cell_type": "code", "execution_count": null, "id": "df2a1bd6-a973-4dd3-bf5e-8d76259c94df", "metadata": {}, "outputs": [], "source": ["capacity_details = pd.merge(\n", "    capacity, backend_hot_outlets, on=[\"outlet_id\"], how=\"inner\"\n", ")\n", "capacity_details = capacity_details[\n", "    [\n", "        \"date_\",\n", "        \"assortment_type\",\n", "        \"type\",\n", "        \"zone\",\n", "        \"city_name\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"outlet_id\",\n", "        \"inv_outlet_id\",\n", "        \"outlet_name\",\n", "        \"business_type_id\",\n", "        \"taggings\",\n", "        \"total_capacity\",\n", "        \"capacity\",\n", "    ]\n", "]\n", "capacity_details"]}, {"cell_type": "code", "execution_count": null, "id": "b1c4f3c5-3439-45ac-ab11-31451940707e", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    capacity_details,\n", "    sheetid=\"1tVJ-98zji37_6WI0jM_sphajFOKFoXAV0w2Kn32Spc8\",\n", "    sheetname=\"capacity_details\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "961a2694-5f5a-4e8e-ad07-bd1d888fa8e6", "metadata": {}, "outputs": [], "source": ["po_inbound_capacity = capacity[capacity[\"type\"] == \"po_inbound\"]\n", "\n", "sto_inbound_capacity = capacity[capacity[\"type\"] == \"sto_inbound\"]\n", "\n", "sto_outbound_capacity = capacity[capacity[\"type\"] == \"ds_transfer\"]\n", "\n", "skus_inbound_capacity = capacity[capacity[\"type\"] == \"skus_inbound\"]"]}, {"cell_type": "code", "execution_count": null, "id": "2d45c402-5ea0-4cfc-8722-e71b6730f053", "metadata": {}, "outputs": [], "source": ["def po_inward():\n", "    po_inward = f\"\"\"\n", "    \n", "    with\n", "    po_grn as\n", "        (select date(created_at + interval '5.5 Hours') as date_,  outlet_id, item_id, sum(quantity) as po_inward\n", "            from lake_po.po_grn\n", "\n", "                where created_at between (current_date-18 || ' 00:00:00')::timestamp - interval '5.5 Hours'\n", "                    and (current_date || ' 23:59:59')::timestamp - interval '5.5 Hours'\n", "                    and grn_id is not null and grn_id != '' and po_id is not null\n", "\n", "                        group by 1,2,3\n", "        )\n", "        \n", "            select * from po_grn\n", "            \n", "                where outlet_id in {be_hot_outlet_id_list}\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(po_inward, redshift)\n", "\n", "\n", "po_inward = po_inward()"]}, {"cell_type": "code", "execution_count": null, "id": "be00732f-ee2f-487d-b536-4d499652ddc0", "metadata": {}, "outputs": [], "source": ["po_inward.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bfadb6c8-0252-4a9b-ae20-adc859db0cfd", "metadata": {}, "outputs": [], "source": ["po_inward[po_inward[\"item_id\"] == 10117546]"]}, {"cell_type": "code", "execution_count": null, "id": "9c2ea43f-3e18-4c84-a678-d36e57c3ab52", "metadata": {}, "outputs": [], "source": ["po_item_assortment = pd.merge(po_inward, final_item_base, on=[\"item_id\"], how=\"inner\")\n", "\n", "po_inward_day_wise_summary = (\n", "    po_item_assortment.groupby([\"date_\", \"outlet_id\", \"assortment_type\"])\n", "    .agg({\"po_inward\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "po_inbound_details = pd.merge(\n", "    po_inbound_capacity,\n", "    po_inward_day_wise_summary,\n", "    on=[\"date_\", \"outlet_id\", \"assortment_type\"],\n", "    how=\"left\",\n", ").rename(\n", "    columns={\n", "        \"capacity\": \"po_capacity\",\n", "        \"total_capacity\": \"monthly_po_capacity\",\n", "        \"type\": \"po_type\",\n", "    }\n", ")\n", "\n", "po_inbound_details[\"po_inward\"] = po_inbound_details[\"po_inward\"].fillna(0)\n", "po_inbound_details"]}, {"cell_type": "code", "execution_count": null, "id": "6e670959-48e1-4e16-8cd1-5dc323f37fe1", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    po_inbound_details,\n", "    sheetid=\"1tVJ-98zji37_6WI0jM_sphajFOKFoXAV0w2Kn32Spc8\",\n", "    sheetname=\"Sheet1\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "92588d64-b01c-4113-8789-1f5806c138ce", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "toc-showcode": true}, "nbformat": 4, "nbformat_minor": 5}