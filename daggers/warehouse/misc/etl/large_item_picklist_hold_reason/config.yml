dag_name: large_item_picklist_hold_reason
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
    - mountPath: /usr/local/airflow/tmp
      name: airflow-dags
      subPath: tmp
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: warehouse
owner:
  name: shubham
  slack_id: USUR0RW84
path: warehouse/misc/etl/large_item_picklist_hold_reason
paused: true
project_name: misc
schedule:
  interval: 0 0 * * *
  start_date: '2019-10-01T00:00:00'
schedule_type: fixed
sink:
  column_dtypes: []
  conn_id: redshift_consumer
  conn_type: postgres
  incremental_key: DATE_TIME
  load_type: upsert
  primary_key:
  - DATE_TIME
  - FACILITY_NAME
  - PICK_LIST_ID
  - PICKLIST_RESUME_TIME
  - ITEM_ID
  - ITEM_SCANNED_AT
  schema: metrics
  sortkey:
  - DATE_TIME
  - FACILITY_NAME
  - PICK_LIST_ID
  - PICKLIST_RESUME_TIME
  table: large_item_picklist_hold_reason
sla: 120 minutes
source:
  bulk:
    parameters:
      where_condition: ' >= ''2019-04-01'' '
  conn_id: redshift_consumer
  conn_type: postgres
  incremental:
    parameters:
      where_condition: ' BETWEEN ''{{ macros.ds_add(ds, -1) }}'' AND ''{{ ds }}'' '
  infer:
    parameters:
      where_condition: ' >= ''2019-08-28'' '
support_files: []
tags: []
template_name: sql
version: 1
