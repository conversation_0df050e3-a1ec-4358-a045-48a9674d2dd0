{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "import sqlalchemy as sqla\n", "import pandas\n", "from collections import defaultdict\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "import numpy as np\n", "import pencilbox as pb\n", "\n", "redshift = pb.get_connection(\"redshift\")\n", "redshift.schema = \"consumer\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_recieved_and_synced_q = \"\"\"Select \n", "\n", "o.id as outlet_id, \n", "o.name as outlet_name, \n", "o.facility_id,\n", "o.tax_location_id as city_id, \n", "l.name as city, \n", "f.name as facility, \n", "m.name as backend_merchant_name,\n", "base.*,\n", "current_timestamp +interval '5.5 hour' as etl_timestamp\n", "\n", "from \n", "(\n", "\n", "Select base1.*,\n", "max(soe.install_ts + interval '5.5 hours') as order_sync_at_pos_ts\n", "\n", "From\n", "\n", "(\n", "Select  \n", "so.backend_merchant_id, \n", "so.id ,\n", "so.current_status, \n", "so.type,\n", "so.procurement_amount, \n", "timestamp 'epoch' + so.slot_start * interval '1 second' + interval '5.5 hour' as scheduled_slot_start,\n", "timestamp 'epoch' + so.slot_end * interval '1 second' + interval '5.5 hour' as scheduled_slot_end,\n", "Date(timestamp 'epoch' + so.slot_end * interval '1 second' + interval '5.5 hour') as scheduled_date,\n", "m.city_name as cms_city_name\n", "\n", "FROM consumer.oms_order o\n", "INNER JOIN consumer.oms_suborder so on o.id = so.order_id\n", "INNER JOIN oms_merchant m ON o.merchant_id = m.id\n", "WHERE o.type IN ('RetailForwardOrder','InternalForwardOrder')\n", "AND so.type ='RetailSuborder' and date(timestamp 'epoch' + so.slot_start * interval '1 second' + interval '5.5 hour' ) >= date(current_date - interval '2 day') \n", "AND m.city_name not in ('Not in service area') AND m.VIRTUAL_MERCHANT_TYPE = 'superstore_merchant'  \n", ")base1\n", "\n", "left join consumer.oms_suborder_event soe on base1.id = soe.suborder_id and soe.event_type_key = 'retail_received_at_pos'\n", "\n", "group by 1,2,3,4,5,6,7,8,9\n", ") base\n", "left join consumer.oms_merchant m on base.backend_merchant_id = m.id \n", "left join consumer.rt_console_outlet_cms_store cms on m.external_id = cms.cms_store and active = 1 and cms_update_active = 1\n", "left join consumer.pos_console_outlet o on cms.outlet_id= o.id\n", "left join consumer.pos_console_location l on o.tax_location_id = l.id\n", "left join consumer.crates_facility f on o.facility_id = f.id\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_recieved_and_synced = pandas.read_sql_query(\n", "    sql=orders_recieved_and_synced_q, con=redshift\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_recieved_and_synced.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# convert_dict = {\n", "#     'outlet_id' : int,\n", "#     'outlet_name' : str,\n", "#     'facility_id' : int,\n", "#     'city_id' : int,\n", "#     'city' : str,\n", "#     'facility' : str,\n", "#     'backend_merchant_name' : str,\n", "#     'backend_merchant_id' : int,\n", "#     'id' : int,\n", "#     'current_status' : str,\n", "#     'type' : str,\n", "#     'scheduled_slot_start' : 'datetime64[ns]',\n", "#     'scheduled_slot_end' : 'datetime64[ns]',\n", "#     'cms_city_name' : str,\n", "#     'order_sync_at_pos_ts' : 'datetime64[ns]',\n", "#     'etl_timestamp' : 'datetime64[ns]'\n", "# }"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"outputs_hidden": true, "source_hidden": true}}, "outputs": [], "source": ["# orders_recieved_and_synced = orders_recieved_and_synced.astype(convert_dict)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"outlet_id\", \"type\": \"float\"},\n", "    {\"name\": \"outlet_name\", \"type\": \"varchar\"},\n", "    {\"name\": \"facility_id\", \"type\": \"float\"},\n", "    {\"name\": \"city_id\", \"type\": \"float\"},\n", "    {\"name\": \"city\", \"type\": \"varchar\"},\n", "    {\"name\": \"facility\", \"type\": \"varchar\"},\n", "    {\"name\": \"backend_merchant_name\", \"type\": \"varchar\"},\n", "    {\"name\": \"backend_merchant_id\", \"type\": \"int\"},\n", "    {\"name\": \"id\", \"type\": \"int\"},\n", "    {\"name\": \"current_status\", \"type\": \"varchar\"},\n", "    {\"name\": \"type\", \"type\": \"varchar\"},\n", "    {\"name\": \"procurement_amount\", \"type\": \"float\"},\n", "    {\"name\": \"scheduled_slot_start\", \"type\": \"datetime\"},\n", "    {\"name\": \"scheduled_slot_end\", \"type\": \"datetime\"},\n", "    {\"name\": \"scheduled_date\", \"type\": \"date\"},\n", "    {\"name\": \"cms_city_name\", \"type\": \"varchar\"},\n", "    {\"name\": \"order_sync_at_pos_ts\", \"type\": \"datetime\"},\n", "    {\"name\": \"etl_timestamp\", \"type\": \"datetime\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"received_synced_orders_test1\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"id\"],\n", "    \"sortkey\": [\"facility\", \"id\", \"city\", \"outlet_name\"],\n", "    \"incremental_key\": \"etl_timestamp\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(orders_recieved_and_synced, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}