dag_name: order_synced
dag_type: etl
escalation_priority: low
executor:
  config:
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: warehouse
notebook:
  parameters: null
owner:
  name: sonal
  slack_id: UABB9AK9V
path: warehouse/misc/etl/order_synced
paused: true
project_name: misc
schedule:
  interval: 15 0 * * *
  start_date: '2020-05-20T00:15:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 4
