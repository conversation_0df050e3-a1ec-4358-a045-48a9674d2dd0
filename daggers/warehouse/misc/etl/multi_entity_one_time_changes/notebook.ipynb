{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["presto = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# sku = pd.read_csv('Item id level details for DS Movement.xlsx - Sheet2.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1XSLJcW4ODxFaz_cXq5X7fAm2n_5fW5fCDsjQELvoGaM\"\n", "sheet_name = \"Item Id details\"\n", "sku = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sku.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# exl = pd.read_excel('SOS Items (1).xlsx')\n", "# sku = sku[~sku['item_id'].isin(exl['ITEM_ID'].unique())]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sku[\"item_id\"] = sku[\"item_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FKN_sku = set(sku[sku[\"Seller - FKN Store\"] == \"TAMS\"].item_id)\n", "kemex_sku = set(sku[sku[\"Seller - G6 Store Store\"] == \"Kemexel\"].item_id)\n", "TAM_sku = set(sku[sku[\"Seller - G6 Store Store\"] == \"TAMS\"].item_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(FKN_sku), len(TAM_sku), len(kemex_sku)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FKN_ds = [\n", "    1072,\n", "    1190,\n", "    1473,\n", "    1477,\n", "    1495,\n", "    2307,\n", "    2337,\n", "    2339,\n", "    2344,\n", "    2345,\n", "    2447,\n", "    2448,\n", "    2449,\n", "    2451,\n", "    2693,\n", "    2696,\n", "    2697,\n", "    2698,\n", "    2871,\n", "    2872,\n", "    2910,\n", "    2914,\n", "    2936,\n", "    2938,\n", "    2939,\n", "    2941,\n", "    2944,\n", "    2946,\n", "    2948,\n", "    3231,\n", "    3233,\n", "    3234,\n", "    3235,\n", "    3238,\n", "    3257,\n", "    3260,\n", "    3446,\n", "    3453,\n", "    3521,\n", "    3523,\n", "    3530,\n", "    3659,\n", "    3662,\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kemex_ds = [\n", "    995,\n", "    1010,\n", "    1056,\n", "    1122,\n", "    1431,\n", "    1433,\n", "    1439,\n", "    1469,\n", "    1485,\n", "    1499,\n", "    1509,\n", "    1701,\n", "    1851,\n", "    1853,\n", "    1970,\n", "    1972,\n", "    2306,\n", "    2311,\n", "    2312,\n", "    2313,\n", "    2314,\n", "    2338,\n", "    2340,\n", "    2341,\n", "    2342,\n", "    2343,\n", "    2346,\n", "    2443,\n", "    2444,\n", "    2545,\n", "    2546,\n", "    2548,\n", "    2551,\n", "    2553,\n", "    2702,\n", "    2703,\n", "    2709,\n", "    2821,\n", "    2823,\n", "    2912,\n", "    3445,\n", "    3448,\n", "    3515,\n", "    3658,\n", "    3661,\n", "    3672,\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw = pd.read_sql_query(\n", "    f\"\"\"\n", "with oevi as\n", "  (select distinct outlet_id,\n", "                   entity_vendor_id\n", "   from lake_rpc.outlet_entity_vendor_item\n", "   where active = 1),\n", "   \n", "     lp as \n", "  (select entity_vendor_id,\n", "          variant_id,\n", "          landing_price\n", "   from lake_ims.ims_entity_vendor_inventory_landing_price)\n", "\n", "select o.id as outlet_id,\n", "       o.name as outlet_name,\n", "       -- location_id,\n", "       -- location_name,\n", "       -- wil.variant_id,\n", "       wil.item_id\n", "       -- sum(wil.quantity) as total_quantity,\n", "       -- sum(wil.quantity * lp.landing_price) as total_value\n", "from lake_warehouse_location.warehouse_item_location wil\n", "join lake_warehouse_location.warehouse_storage_location s on s.id = wil.location_id\n", "join (select distinct outlet_id, entity_vendor_id\n", "      from lake_retail.outlet_entity_vendor_mapping) oevm on oevm.entity_vendor_id = wil.entity_vendor_id\n", "join lp on lp.entity_vendor_id = wil.entity_vendor_id and lp.variant_id = wil.variant_id\n", "join lake_vms.vms_vendor v on v.id = wil.entity_vendor_id\n", "join lake_retail.console_outlet o on o.id = oevm.outlet_id \n", "and o.business_type_id = 7\n", "where o.id in (select outlet_id from oevi)\n", "group by 1,2,3\n", "having sum(wil.quantity) > 0\n", "order by 1,3\n", "\"\"\",\n", "    presto,\n", ")\n", "\n", "# raw['total_value'] = raw['total_value'].astype(float)\n", "raw.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Seller FKN - TAMS"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FKN = raw[(raw[\"outlet_id\"].isin(FKN_ds)) & (raw[\"item_id\"].isin(FKN_sku))]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FKN.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(FKN)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1FA72pSKS1lKkdOHAg7p8a7TmxNBYYwe0_hphH2gv5eU\"\n", "sheet_name = \"Item Id for Seller FKN - TAMS\"\n", "\n", "pb.to_sheets(FKN, sheet_id, sheet_name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Seller G6 - TAMS"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TAM = raw[(raw[\"outlet_id\"].isin(kemex_ds)) & (raw[\"item_id\"].isin(TAM_sku))]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TAM.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(TAM)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1FA72pSKS1lKkdOHAg7p8a7TmxNBYYwe0_hphH2gv5eU\"\n", "sheet_name = \"Item Id for Seller G6 - TAMS\"\n", "\n", "pb.to_sheets(TAM, sheet_id, sheet_name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Seller G6 - Kemexel"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kemex = raw[(raw[\"outlet_id\"].isin(kemex_ds)) & (raw[\"item_id\"].isin(kemex_sku))]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kemex.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(kemex)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1FA72pSKS1lKkdOHAg7p8a7TmxNBYYwe0_hphH2gv5eU\"\n", "sheet_name = \"Item Id for Seller G6 - Kemexel\"\n", "\n", "pb.to_sheets(kemex, sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}