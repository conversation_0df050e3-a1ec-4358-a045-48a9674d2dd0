template_name: notebook
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
path: warehouse/misc/etl/picking_effort_estimation
namespace: warehouse
project_name: misc
dag_name: picking_effort_estimation
version: 8
owner:
  name: aditya
  slack_id: U03S2LG8ADB

schedule:
  start_date: '2020-07-03T11:00:00'
  interval: '*/30 * * * *'

notebook:
  # https://airflow.apache.org/macros.html
  # you can define your own parameters
  parameters:
    start_date: "{{ execution_date.format('%Y-%m-%dT%H:%M:%S') }}"
    end_date: "{{ next_execution_date.format('%Y-%m-%dT%H:%M:%S') }}"
    dag_id: "{{ ti.dag_id }}"

dag_type: etl
escalation_priority: low
schedule_type: fixed
sla: 120 minutes
tags: []
support_files: []
paused: true
executor:
  type: kubernetes
  config:
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    node_selectors:
      nodetype: spot
    tolerations:
    - key: service
      operator: Equal
      value: airflow
      effect: NoSchedule
    cpu:
      request: 0.5
      limit: 1
    memory:
      request: 500M
      limit: 4G
    volume_mounts:
    - name: airflow-dags
      subPath: airflow/plugins
      mountPath: /usr/local/airflow/plugins
