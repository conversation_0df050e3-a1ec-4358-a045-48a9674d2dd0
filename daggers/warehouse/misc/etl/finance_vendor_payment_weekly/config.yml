dag_name: finance_vendor_payment_weekly
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 4G
      request: 2G
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: warehouse
notebook:
  parameters: null
owner: 
  email: <EMAIL>
  slack_id: U03SW62SWTS
path: warehouse/misc/etl/finance_vendor_payment_weekly
paused: true
project_name: misc
schedule: 
  interval: 30 3 * * 1

  start_date: '2022-09-08T12:30:00'

schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook

version: 6


