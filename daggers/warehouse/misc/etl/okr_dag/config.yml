template_name: notebook
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
dag_name: okr_dag
version: 1
owner:
  email: <EMAIL>
  slack_id: U03RQJY8HAT

schedule:
  start_date: '2021-06-12T01:30:00'
  interval: '30 1 * * 1'

notebook:
  parameters:
dag_type: etl
escalation_priority: low
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
namespace: warehouse
project_name: misc

paused: true
path: warehouse/misc/etl/okr_dag
executor:
  type: kubernetes
  config:
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    node_selectors:
      nodetype: spot
    tolerations:
    - key: service
      operator: Equal
      value: airflow
      effect: NoSchedule
    cpu:
      request: 0.5
      limit: 1
    memory:
      request: 500M
      limit: 4G
    volume_mounts:
    - name: airflow-dags
      subPath: airflow/plugins
      mountPath: /usr/local/airflow/plugins
