template_name: notebook
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
dag_name: ims_ils_diff
version: 4
owner:
  email: <EMAIL>
  slack_id: U03RQJY8HAT
schedule:
  start_date: '2021-10-19T01:30:00'
  interval: 30 2,14 * * *
notebook:
  parameters:
dag_type: etl
escalation_priority: low
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
namespace: warehouse
project_name: misc
paused: true
path: warehouse/misc/etl/ims_ils_diff
executor:
  type: kubernetes
  config:
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    node_selectors:
      nodetype: spot
    tolerations:
    - key: service
      operator: Equal
      value: airflow
      effect: NoSchedule
    cpu:
      request: 0.5
      limit: 1
    memory:
      request: 500M
      limit: 4G
    volume_mounts:
    - name: airflow-dags
      subPath: airflow/plugins
      mountPath: /usr/local/airflow/plugins
