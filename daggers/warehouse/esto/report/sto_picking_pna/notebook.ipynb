{"cells": [{"cell_type": "code", "execution_count": null, "id": "6d54bfee-e799-4003-a47a-acab6fb75d0e", "metadata": {}, "outputs": [], "source": ["import time\n", "import pytz\n", "import numpy\n", "import jinja2\n", "import pandas as pd\n", "from datetime import datetime, date, timedelta\n", "import pencilbox as pb\n", "import pytz\n", "from gspread.exceptions import APIError\n", "\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")\n", "pd.set_option(\"display.max_columns\", 50)"]}, {"cell_type": "code", "execution_count": null, "id": "9f5a493e-5d75-48d3-a77f-5d56e62b89ea", "metadata": {}, "outputs": [], "source": ["tz = pytz.timezone(\"Asia/calcutta\")\n", "# now = datetime.now(tz).hour"]}, {"cell_type": "code", "execution_count": null, "id": "360392ae-2048-4e81-bac5-92b10b5af697", "metadata": {}, "outputs": [], "source": ["yesterday = (datetime.now(tz) - <PERSON><PERSON><PERSON>(days=1)).date()\n", "today = datetime.now(tz).date()"]}, {"cell_type": "code", "execution_count": null, "id": "ad598a89-8003-44d6-aef8-e9e9f1a3fbe0", "metadata": {}, "outputs": [], "source": ["start = pd.to_datetime(str(yesterday) + \" 02:00:00\")\n", "end = pd.to_datetime(str(today) + \" 02:00:00\")\n", "text = str(yesterday)"]}, {"cell_type": "code", "execution_count": null, "id": "0aa4db68-4137-41d0-8721-039d23b1e061", "metadata": {}, "outputs": [], "source": ["# now"]}, {"cell_type": "code", "execution_count": null, "id": "f5067e07-6e2d-4fdf-8202-acfec92f775e", "metadata": {}, "outputs": [], "source": ["# if now <= 15:\n", "#     end = pd.to_datetime(str(today) + \" 07:30:00\")\n", "#     start = pd.to_datetime(str(yesterday) + \" 19:30:00\")  # change\n", "#     text = str(start.date()) + \"-Shift2\"\n", "# elif now >= 15:\n", "#     start = pd.to_datetime(str(today) + \" 07:30:00\")\n", "#     end = pd.to_datetime(str(today) + \" 19:30:00\")\n", "#     text = str(start.date()) + \"-Shift1\""]}, {"cell_type": "code", "execution_count": null, "id": "87403a21-fb12-4733-a5a3-086388436843", "metadata": {}, "outputs": [], "source": ["start, end, text"]}, {"cell_type": "markdown", "id": "57f20c3c-1e24-497d-b3b9-74fcb0a531d4", "metadata": {}, "source": ["# PNA Cases"]}, {"cell_type": "code", "execution_count": null, "id": "e298086d-418f-4478-8156-7858c8e12db1", "metadata": {}, "outputs": [], "source": ["total_outbound = pd.read_sql_query(\n", "    f\"\"\"\n", "SELECT i.outlet_id,\n", "       i.name AS outlet_name,\n", "       i.sto_id,\n", "       j.source_entity_vendor_id,\n", "       v1.vendor_name as source_entity_vendor_name,\n", "       j.destination_entity_vendor_id,\n", "       v2.vendor_name as destination_entity_vendor_name,\n", "       (i.created_at + interval '330' minute) as sto_created_at,\n", "       ii.item_id,\n", "       ii.reserved_quantity\n", "FROM (select sto_id, k.created_at, c.name, outlet_id from lake_view_ims.ims_sto_details k\n", "JOIN lake_retail.console_outlet c ON c.id = outlet_id AND c.business_type_id in (1,12) AND c.active = 1 \n", "AND c.device_id <> 47 AND c.id not in (451)\n", "WHERE k.created_at between cast('{start}'as timestamp) - interval '330' minute and cast('{end}'as timestamp) - interval '330' minute)i \n", "JOIN lake_ims.ims_sto_item ii on ii.sto_id = i.sto_id\n", "LEFT JOIN lake_po.sto j on j.id = i.sto_id\n", "LEFT JOIN lake_vms.vms_vendor v1 on v1.id = j.source_entity_vendor_id\n", "LEFT JOIN lake_vms.vms_vendor v2 on v2.id = j.destination_entity_vendor_id\n", "LEFT JOIN lake_vms.vms_vendor_city_mapping cm on cm.vendor_id = j.source_entity_vendor_id and cm.active = 1\n", "LEFT JOIN lake_vms.vms_vendor_city_tax_info cti on cti.vendor_city_id = cm.id and cti.active = 1\n", "WHERE lower(cti.legal_name) NOT LIKE '%%blink%%'\n", "\"\"\",\n", "    presto,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0d824433-5652-41fd-ad24-b6fe9b67c34f", "metadata": {}, "outputs": [], "source": ["total_outbound.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "9cbd53d6-da89-46e0-895f-4ebdc149382b", "metadata": {}, "outputs": [], "source": ["bpl = pd.read_sql_query(\n", "    f\"\"\"\n", "SELECT w.entity_id sto_id,\n", "       p.assigned_to_name as picker_id,\n", "       (p.picking_started_at + interval '330' minute) as picking_started_at,\n", "       pi.item_id,\n", "       pi.item_name,\n", "       array_join(array_agg(sil.location_name), ', ') location_names,\n", "       pi.picked_quantity,\n", "       (case when pi.state in (3,8,10) then (pi.required_quantity - pi.picked_quantity) else 0 end) as pna_qty \n", "FROM lake_warehouse_location.warehouse_wave w\n", "JOIN lake_view_warehouse_location.warehouse_pick_list p on w.id = p.wave_id and w.wave_type = 4\n", "and p.insert_ds_ist >= cast(cast(cast('{start}'as timestamp) as date) as varchar)\n", "JOIN lake_warehouse_location.warehouse_pick_list_item pi on pi.pick_list_id = p.id\n", "and pi.insert_ds_ist >= cast(cast(cast('{start}'as timestamp) as date) as varchar)\n", "LEFT JOIN lake_warehouse_location.warehouse_pick_list_item_location pil on pil.pick_list_item_id = pi.id \n", "and pil.insert_ds_ist >= cast(cast(cast('{start}'as timestamp) as date) as varchar)\n", "LEFT JOIN lake_warehouse_location.warehouse_storage_location sil on sil.id = pil.location_id  \n", "WHERE cast(w.entity_id as int) in {*total_outbound['sto_id'].unique(),}\n", "group by 1,2,3,4,5,7,8\n", "\"\"\",\n", "    presto,\n", ")\n", "bpl.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "5a667646-1b93-4e2f-a39e-1b683037f189", "metadata": {}, "outputs": [], "source": ["ipl = pd.read_sql_query(\n", "    f\"\"\"\n", "select cast(ipli.entity_id as int) sto_id,\n", "       assigned_to_name picker_id,\n", "       ipl.in_progress_timestamp + interval '330' minute picking_started_at,\n", "       cast(ipl.item_id as int) item_id,\n", "       ipl.item_name,\n", "       '' location_names,\n", "       ipli.picked_quantity,\n", "       (case when ipli.state in (3,4,5) THEN (ipli.required_quantity - ipli.picked_quantity) else 0 end) pna_qty\n", "from lake_view_warehouse_location.warehouse_item_pick_list ipl\n", "join lake_warehouse_location.warehouse_item_pick_list_line_item ipli on ipli.item_pick_list_id = ipl.id\n", "and ipli.insert_ds_ist >= cast(cast(cast('{start}' as TIMESTAMP) as DATE) as varchar)\n", "join lake_retail.console_outlet co on co.id = ipl.outlet_id \n", "and co.business_type_id in (1,12) \n", "and co.device_id != 47\n", "and co.active = 1\n", "join lake_ims.ims_sto_item si on si.sto_id = cast(ipli.entity_id as int) and si.item_id = cast(ipl.item_id as int)\n", "join lake_view_po.sto s on s.id = cast(ipli.entity_id as int)\n", "join lake_vms.vms_vendor v on v.id = s.source_entity_vendor_id\n", "join lake_vms.vms_vendor d on d.id = s.destination_entity_vendor_id\n", "where cast(ipli.entity_id as int) in {*total_outbound['sto_id'].unique(),}\n", "\"\"\",\n", "    presto,\n", ")\n", "ipl.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "7d9e12af-a988-4b88-823b-dc55b950865c", "metadata": {}, "outputs": [], "source": ["ipl[\"Picklist Type\"] = \"IPL\"\n", "bpl[\"Picklist Type\"] = \"BPL\""]}, {"cell_type": "code", "execution_count": null, "id": "70d7434f-13f8-4af7-ad31-cd941fb740f5", "metadata": {}, "outputs": [], "source": ["pl = pd.concat([bpl, ipl], axis=0)\n", "total_outbound = total_outbound.merge(pl, on=[\"sto_id\", \"item_id\"], how=\"left\")\n", "total_outbound.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "2aefdf74-8ffb-40bf-8b30-dae8ca930e2c", "metadata": {}, "outputs": [], "source": ["total_outbound.reserved_quantity.sum(), total_outbound.picked_quantity.sum()"]}, {"cell_type": "code", "execution_count": null, "id": "dbc34159-d319-42f2-88a6-64bc27284b1a", "metadata": {}, "outputs": [], "source": ["# total_outbound = total_outbound.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "276af88a-2537-4e91-9375-7ce9d5e68e30", "metadata": {}, "outputs": [], "source": ["total_outbound[\"sto_created_at\"] = pd.to_datetime(total_outbound[\"sto_created_at\"])"]}, {"cell_type": "code", "execution_count": null, "id": "c6d4fb50-9ce5-4a97-bc26-0b9ec5d37341", "metadata": {}, "outputs": [], "source": ["total_outbound.sto_created_at.min(), total_outbound.sto_created_at.max()"]}, {"cell_type": "code", "execution_count": null, "id": "64c844d3-aa8f-457f-b6ee-c22bcdbeee54", "metadata": {}, "outputs": [], "source": ["shift = total_outbound[\n", "    (total_outbound.sto_created_at <= end) & (total_outbound.sto_created_at >= start)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "19e59852-70fc-4b2f-ba08-b0a90b52189d", "metadata": {}, "outputs": [], "source": ["shift[\"reserved_quantity\"].sum(), shift[\"picked_quantity\"].sum(), shift[\"pna_qty\"].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "95752061-78fc-4790-bf3a-bab39f18f73b", "metadata": {}, "outputs": [], "source": ["shift[shift[\"pna_qty\"] > 0].to_csv(\"PNA_\" + str(text) + \".csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "f4bc34ea-3d2d-4de4-9dd0-0310d9082fff", "metadata": {}, "outputs": [], "source": ["shift[shift[\"pna_qty\"] > 0].head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "63362fcf-eddd-45a5-9233-74ad4ec5f8ca", "metadata": {}, "outputs": [], "source": ["summary = (\n", "    shift.groupby([\"Picklist Type\", \"outlet_id\", \"outlet_name\"])[\n", "        [\"reserved_quantity\", \"picked_quantity\", \"pna_qty\"]\n", "    ]\n", "    .sum()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "04f5be83-2862-4b75-9161-4a0fcf424474", "metadata": {}, "outputs": [], "source": ["summary[\"pna_percentage\"] = round(\n", "    summary[\"pna_qty\"] * 100 / summary[\"reserved_quantity\"], 2\n", ")\n", "summary[\"picking_fillrate\"] = round(\n", "    summary[\"picked_quantity\"] * 100 / summary[\"reserved_quantity\"], 2\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7a37a3f0-dec9-40d6-be60-c920176b8c90", "metadata": {}, "outputs": [], "source": ["summary.rename(\n", "    columns={\n", "        \"outlet_id\": \"Outlet ID\",\n", "        \"outlet_name\": \"Outlet Name\",\n", "        \"reserved_quantity\": \"Total Reserved Qty\",\n", "        \"picked_quantity\": \"Picked Qty\",\n", "        \"pna_qty\": \"PNA Marked Qty\",\n", "        \"pna_percentage\": \"PNA Percentage\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c1e4cd4e-485e-4c95-b70b-b82c00f88d15", "metadata": {}, "outputs": [], "source": ["summary.head()"]}, {"cell_type": "code", "execution_count": null, "id": "87d56d2b-772b-49bc-8492-c20d25c4c9cf", "metadata": {}, "outputs": [], "source": ["emails = pb.from_sheets(\"1vF3kmEfI_8WNqAgjQmK48tlCKaQ7shHJhzROSAwj-8I\", \"emails\")"]}, {"cell_type": "code", "execution_count": null, "id": "65937af3-f9a6-4364-9030-f1ba9828c45c", "metadata": {}, "outputs": [], "source": ["emails = emails.emails.unique()"]}, {"cell_type": "code", "execution_count": null, "id": "af969014-be44-4bfe-ad1e-096082cbf649", "metadata": {}, "outputs": [], "source": ["emails"]}, {"cell_type": "markdown", "id": "41e5b5c9-b09a-44aa-a127-fc297f873c66", "metadata": {}, "source": ["# FIFO Location Adherence"]}, {"cell_type": "code", "execution_count": null, "id": "920dee95-54d0-4471-a475-df91e22b7966", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "SELECT outlet_id,\n", "       outlet_name,\n", "       round(percent_location_adh,2) AS percent_location_adh\n", "FROM\n", "  (SELECT co.id AS outlet_id,\n", "          co.name AS outlet_name,\n", "          100-(sum((CASE\n", "                        WHEN (plir.location_suggested != plir.location_picked\n", "                              AND plir.location_suggested IS NOT NULL) THEN 1\n", "                        ELSE 0\n", "                    END))*100.0/count(pli.item_id)) percent_location_adh\n", "   FROM\n", "     (SELECT *\n", "      FROM lake_warehouse_location.warehouse_pick_list\n", "      WHERE (created_at + interval '5.5 hrs') >= '{start}' \n", "      AND (created_at + interval '5.5 hrs') < '{end}'\n", "        AND \"type\" IN (2,\n", "                       3,\n", "                       4,\n", "                       5,\n", "                       6,\n", "                       7)) pl\n", "   LEFT JOIN lake_warehouse_location.pick_list_log pll ON pll.pick_list_id = pl.id\n", "   AND pll.picklist_state = 4\n", "   LEFT JOIN lake_retail.console_outlet co ON co.id = pl.outlet_id\n", "   LEFT JOIN lake_retail.warehouse_facility f ON f.id = co.facility_id\n", "   LEFT JOIN lake_warehouse_location.warehouse_pick_list_item PLI ON pl.id = pli.pick_list_id\n", "   LEFT JOIN\n", "     (SELECT *\n", "      FROM lake_warehouse_location.warehouse_pick_list_item_location_error_log\n", "      WHERE error_type = 1) plir ON plir.item_id = pli.item_id\n", "   AND plir.pick_list_id = pli.pick_list_id\n", "   LEFT join lake_warehouse_location.warehouse_storage_location sl ON sl.id = plir.location_picked\n", "   WHERE f.name NOT LIKE '%%DarkS%%'\n", "     AND f.name NOT LIKE '%%Darks%%'\n", "     AND f.name NOT LIKE '%%ES%%'\n", "     AND f.name NOT LIKE '%%PC%%'\n", "     AND coalesce(sl.zone_identifier, 'NULL') NOT ilike '%%pack%%'\n", "   GROUP BY 1,\n", "            2\n", "   ORDER BY 3 DESC)\n", "   \"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "29af6f60-9553-471a-af57-2212cc966d27", "metadata": {}, "outputs": [], "source": ["data = pd.read_sql_query(query, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "0906c32e-8edb-4d1f-a99f-ac86923d3e1e", "metadata": {}, "outputs": [], "source": ["df = data.copy(deep=True)"]}, {"cell_type": "code", "execution_count": null, "id": "fd6c441e-5215-4946-8567-c34d1a66080a", "metadata": {}, "outputs": [], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "935231d1-f261-4be2-b227-bb619144e3a6", "metadata": {}, "outputs": [], "source": ["df = df.rename(\n", "    columns={\n", "        \"outlet_id\": \"Outlet ID\",\n", "        \"outlet_name\": \"Outlet Name\",\n", "        \"percent_location_adh\": \"Location Adherence Percentage\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d74f6fbf-ec3b-4c32-8f9e-8c02c4ab4867", "metadata": {}, "outputs": [], "source": ["query2 = f\"\"\"\n", "SELECT co.id AS outlet_id,\n", "       co.name AS outlet_name,\n", "       pl.assigned_to_name,\n", "       wu.name,\n", "       w.entity_id AS sto_id,\n", "       pl.pick_list_id,\n", "       pli.item_id,\n", "       sl1.location_name AS name1,\n", "       sl2.location_name AS name2\n", "FROM\n", "  (SELECT *\n", "   FROM lake_warehouse_location.warehouse_pick_list\n", "   WHERE (created_at + interval '5.5 hrs' ) BETWEEN '{start}' AND '{end}'\n", "     AND \"type\" IN (2,\n", "                    3,\n", "                    4,\n", "                    5,\n", "                    6,\n", "                    7)) pl\n", "LEFT JOIN lake_warehouse_location.pick_list_log pll ON pll.pick_list_id = pl.id\n", "AND pll.picklist_state = 4\n", "LEFT JOIN lake_retail.console_outlet co ON co.id = pl.outlet_id\n", "LEFT JOIN lake_retail.warehouse_facility f ON f.id = co.facility_id\n", "LEFT JOIN lake_warehouse_location.warehouse_pick_list_item PLI ON pl.id = pli.pick_list_id\n", "LEFT JOIN\n", "  (SELECT *\n", "   FROM lake_warehouse_location.warehouse_pick_list_item_location_error_log\n", "   WHERE error_type = 1 ) plir ON plir.item_id = pli.item_id\n", "AND plir.pick_list_id = pli.pick_list_id\n", "LEFT JOIN lake_retail.warehouse_user wu ON pl.assigned_to_name = wu.emp_id\n", "LEFT JOIN lake_warehouse_location.warehouse_wave w ON w.id = pl.wave_id\n", "LEFT JOIN lake_warehouse_location.warehouse_storage_location sl1 ON sl1.id = plir.location_suggested\n", "LEFT JOIN lake_warehouse_location.warehouse_storage_location sl2 ON sl2.id = plir.location_picked\n", "WHERE f.name NOT LIKE '%%DarkS%%'\n", "  AND f.name NOT LIKE '%%Darks%%'\n", "  AND f.name NOT LIKE '%%ES%%'\n", "  AND f.name NOT LIKE '%%PC%%'\n", "  AND plir.location_suggested != plir.location_picked\n", "  AND plir.location_suggested IS NOT NULL\n", "  AND coalesce(sl2.zone_identifier, 'NULL') NOT ilike '%%pack%%'\n", "ORDER BY 1,\n", "         4,\n", "         5,\n", "         6,\n", "         7\n", "  \"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "fcab181e-5605-4bca-93d4-c4574a573900", "metadata": {}, "outputs": [], "source": ["data2 = pd.read_sql_query(query2, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "a2958d3d-9e6b-4e21-9093-89c3d60fc259", "metadata": {}, "outputs": [], "source": ["df2 = data2.copy(deep=True)"]}, {"cell_type": "code", "execution_count": null, "id": "69f19e2d-9f97-465e-9819-c1e3a6f1071f", "metadata": {}, "outputs": [], "source": ["df2.columns"]}, {"cell_type": "code", "execution_count": null, "id": "4b3a9fc2-6ffb-4efc-ae3e-b31e6d8ce7b9", "metadata": {}, "outputs": [], "source": ["df2 = df2.rename(\n", "    columns={\n", "        \"outlet_id\": \"Outlet ID\",\n", "        \"outlet_name\": \"Outlet Name\",\n", "        \"assigned_to_name\": \"Employee ID\",\n", "        \"name\": \"Employee name\",\n", "        \"sto_id\": \"STO ID\",\n", "        \"pick_list_id\": \"Pick List ID\",\n", "        \"item_id\": \"Item ID\",\n", "        \"name1\": \"Location Suggested\",\n", "        \"name2\": \"Location Picked\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3f9190c3-0f43-48d4-97b1-dfb243da8689", "metadata": {}, "outputs": [], "source": ["df2.to_csv(\"Non_adherence_cases_\" + str(text) + \".csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "a5525286-a8d5-4259-9302-e3f0b724ba1a", "metadata": {}, "outputs": [], "source": ["summary.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9c5c1c86-433f-4c7b-aecd-e29c699a1117", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "markdown", "id": "4a4e6896-ea5f-406a-911a-eb08f361e4a6", "metadata": {}, "source": ["# Sending the email "]}, {"cell_type": "code", "execution_count": null, "id": "940ef057-c1cf-44fe-81c2-8c5dbc73c574", "metadata": {}, "outputs": [], "source": ["text"]}, {"cell_type": "code", "execution_count": null, "id": "0ea693df-9604-4062-8464-7c78cfb5f6c4", "metadata": {}, "outputs": [], "source": ["emails"]}, {"cell_type": "code", "execution_count": null, "id": "acdedf11-a863-4dc1-b967-af5e620d584c", "metadata": {}, "outputs": [], "source": ["str(start), str(end)"]}, {"cell_type": "code", "execution_count": null, "id": "41c6d883-f1fb-4650-a3c4-207f330868ca", "metadata": {}, "outputs": [], "source": ["pb.send_email(\n", "    from_email=\"<EMAIL>\",\n", "    # to_email = \"<EMAIL>\",\n", "    to_email=emails,\n", "    subject=\"Warehouse Picking PNA & FIFO Adherence Report for \" + str(text),\n", "    html_content=\"\"\"Hi,<br> <br> Warehouse Picking PNAs summary at an Outlet level for \"\"\"\n", "    + str(text)\n", "    + \"\"\" from \"\"\"\n", "    + str(start)\n", "    + \"\"\" to \"\"\"\n", "    + str(end)\n", "    + \"\"\"<br> <br>\"\"\"\n", "    + summary.to_html(index=False)\n", "    .replace(\"<td>\", '<td align=\"center\">')\n", "    .replace('<tr style=\"text-align: right;\">', '<tr style=\"text-align: center;\">')\n", "    + \"\"\"<br> PNA cases for a date range & at Outlet level in the following query: \"\"\"\n", "    + \"\"\"https://redash-queries.grofers.com/queries/223275\"\"\"\n", "    # + \"\"\"<br> You can find last one week's e-way bill adoption numbers here : https://docs.google.com/spreadsheets/d/1vF3kmEfI_8WNqAgjQmK48tlCKaQ7shHJhzROSAwj-8I/edit?usp=sharing <br>\"\"\"\n", "    \"\"\"<br> <br> Picking FIFO Location Adherence for \"\"\"\n", "    + str(text)\n", "    + \"\"\"<br> <br>\"\"\"\n", "    + df.to_html(index=False)\n", "    .replace(\"<td>\", '<td align=\"center\">')\n", "    .replace('<tr style=\"text-align: right;\">', '<tr style=\"text-align: center;\">')\n", "    + \"\"\"<br> <br> FIFO Adherence percentage in the following query: \"\"\"\n", "    + \"\"\"https://redash-queries.grofers.com/queries/226973\"\"\"\n", "    + \"\"\"<br> <br> PFA RawData for Warehouse Picking PNA cases at item level & FIFO Non Adhered cases for \"\"\"\n", "    + str(text)\n", "    + \"\"\"<br> <br> Thanks,<br>Warehouse data team.\"\"\",\n", "    files=[\"PNA_\" + str(text) + \".csv\", \"Non_adherence_cases_\" + str(text) + \".csv\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "54455fbf-1c53-4cb1-8d4a-f07d7c0940b4", "metadata": {}, "outputs": [], "source": ["# pb.send_email(\n", "#     from_email=\"<EMAIL>\",\n", "#     to_email=emails,\n", "#     subject=\"Warehouse Picking PNA for \" + str(yesterday),\n", "#     html_content=\"\"\"Hi,<br> <br> Please find the picking PNAs summary at an Outlet level for \"\"\"\n", "#     + str(yesterday)\n", "#     + \"\"\"<br> <br>\"\"\"\n", "#     + summary.to_html(index=False)\n", "#     .replace(\"<td>\", '<td align=\"center\">')\n", "#     .replace('<tr style=\"text-align: right;\">', '<tr style=\"text-align: center;\">')\n", "#     + \"\"\"<br> Please find individual PNA cases for a date range & outlet in the following query: \"\"\"\n", "#     + \"\"\"https://redash-queries.grofers.com/queries/223275\"\"\"\n", "#     + \"\"\"<br> <br> PFA RawData for Warehouse Picking PNA cases at item level for yesterday.\"\"\"\n", "#     # + \"\"\"<br> You can find last one week's e-way bill adoption numbers here : https://docs.google.com/spreadsheets/d/1vF3kmEfI_8WNqAgjQmK48tlCKaQ7shHJhzROSAwj-8I/edit?usp=sharing <br>\"\"\"\n", "#     + \"\"\"<br> <br> Thanks,<br>Warehouse data team.\"\"\",\n", "#     files=[\n", "#         \"PNA_\" + str(yesterday) + \".csv\",\n", "#     ],\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "8e613452-5d6d-4200-9a79-03632b9c51e4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}