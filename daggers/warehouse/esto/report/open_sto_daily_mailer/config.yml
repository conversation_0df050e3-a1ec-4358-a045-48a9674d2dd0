template_name: notebook
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
path: warehouse/esto/report/open_sto_daily_mailer
namespace: warehouse
project_name: esto
dag_name: open_sto_daily_mailer
version: 5
owner:
  email: <EMAIL>
  slack_id: UABB9AK9V
schedule:
  start_date: '2021-10-12T00:00:00'
  interval: 30 2 * * *
notebook:
  # https://airflow.apache.org/macros.html
  # you can define your own parameters
  parameters:
dag_type: report
escalation_priority: low
schedule_type: fixed
sla: 120 minutes
tags: []
support_files: []
paused: true
executor:
  type: kubernetes
  config:
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    node_selectors:
      nodetype: spot
    tolerations:
    - key: service
      operator: Equal
      value: airflow
      effect: NoSchedule
    cpu:
      request: 0.5
      limit: 1
    memory:
      request: 2G
      limit: 16G
    volume_mounts:
    - name: airflow-dags
      subPath: airflow/plugins
      mountPath: /usr/local/airflow/plugins
slack_alert_configs:
- channel: gobd-data-alerts
