{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 50)\n", "pd.set_option(\"display.max_rows\", 150)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_query = \"\"\"with\n", "\n", "zone_tbl_treatment as\n", "(\n", "select\n", "facility_id,\n", "max(zone) as zone\n", "from\n", "metrics.outlet_zone_mapping\n", "group by 1\n", "),\n", "\n", "item_level_info as\n", "(\n", "select\n", "item_id,\n", "name as item_name,\n", "brand,\n", "brand_id,\n", "manufacturer,\n", "variant_description,\n", "is_pl,\n", "variant_mrp,\n", "row_number() over (partition by item_id order by updated_at desc) as row_rank\n", "from \n", "lake_rpc.product_product\n", "),\n", "\n", "categories as\n", "(\n", "SELECT \n", "P.ID AS PID,\n", "P.NAME AS PRODUCT,\n", "C2.NAME AS L2,\n", "C.NAME AS L0,\n", "pt.name as product_type\n", "from lake_cms.gr_product P\n", "INNER join lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "INNER join lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID AND PCM.IS_PRIMARY=TRUE\n", "INNER join lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "INNER join lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "inner join lake_cms.gr_product_type pt on p.type_id=pt.id\n", "),\n", "\n", "category_pre as\n", "(\n", "SELECT \n", "item_id,\n", "product_id,\n", "cat.l2,\n", "cat.l0,\n", "cat.product_type\n", "from lake_rpc.item_product_mapping  rpc\n", "INNER JOIN categories cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL\n", "),\n", "\n", "ptype_tbl as\n", "(\n", "select \n", "item_id,\n", "max(l2) as l2,\n", "max(l0) as l0,\n", "max(product_type) as ptype\n", "from category_pre\n", "group by 1\n", "),\n", "\n", "sto_data as\n", "(\n", "select\n", "a.sto_id,\n", "a.item_id,\n", "a.sender_outlet_id,\n", "a.sender_outlet_name,\n", "a.receiving_outlet_id,\n", "a.receiver_outlet_name,\n", "a.item_name as item_name,\n", "a.actual_sales as value_ordered,\n", "a.actual_sales*1.000/expected_quantity as landing_price,\n", "a.expected_quantity as units_ordered,\n", "a.billed_quantity as billed_quantity,\n", "a.inwarded_quantity as grn_quantity,\n", "sto_type,\n", "sto_state,\n", "date(a.sto_created_at) as sto_creation_date,\n", "date(a.grn_created_at) as sto_grn_date,\n", "date(a.sto_expired_at) as sto_expiry_date,\n", "date(a.sto_partial_billed_at) as sto_partial_bill_date,\n", "date(a.sto_billed_at) as sto_bill_date,\n", "extract(month from date(sto_expiry_date)) as expiry_month,\n", "extract(month from date(sto_grn_date)) as grn_month,\n", "coalesce(date(a.sto_partial_billed_at), date(a.sto_billed_at)) as sto_bill_date1,\n", "case when inwarded_quantity > 0 then 1 else 0 end as grn_flag\n", "from metrics.esto_details a\n", "left join ptype_tbl d on a.item_id = d.item_id\n", "where \n", "--AND (date(sto_expired_at) >= current_date - 90 or date(grn_created_at) >= current_date - 90 or date(sto_billed_at) >= current_date - 90)\n", "date(sto_created_at) between current_date - 180 and current_date - 1\n", "and d.l0 not ilike '%%fruits%%'\n", "and sto_state <> 'Created'\n", "--and b.outlet_id not in (635,287) -- Outlets used for transfer in Kolkata\n", "),\n", "\n", "sto_expiry as\n", "(\n", "select\n", "a.*,\n", "c.facility_id as receiving_facility_id,\n", "d.facility_id as sender_facility_id,\n", "f.name as receiving_city_name,\n", "g.name as receiver_facility_name,\n", "h.name as sender_facility_name,\n", "sto_expiry_date - sto_creation_date as tat_days,\n", "case when grn_flag = 1 then sto_grn_date - sto_creation_date end as delivery_days\n", "from\n", "sto_data a\n", "left join (select * from lake_retail.console_outlet where active = true) c on a.receiving_outlet_id = c.id\n", "left join lake_retail.console_location f on c.tax_location_id = f.id\n", "left join lake_crates.facility g on c.facility_id = g.id\n", "left join (select * from lake_retail.console_outlet where active = true) d on a.sender_outlet_id = d.id\n", "left join lake_retail.console_location e on d.tax_location_id = e.id\n", "left join lake_crates.facility h on d.facility_id = h.id\n", "),\n", "\n", "sto_received_info as\n", "(\n", "select\n", "e.zone,\n", "sender_facility_id,\n", "sender_facility_name,\n", "receiving_facility_id,\n", "receiver_facility_name,\n", "\n", "count(distinct case when sto_creation_date = current_date - 1 then concat(sto_id, item_id) end) as sto_billing_expected_t_minus_1,\n", "count(distinct case when sto_creation_date = current_date - 1 and billed_quantity > 0 then concat(sto_id, item_id) end) as sto_billing_executed_t_minus_1,\n", "\n", "count(distinct case when sto_creation_date = current_date - 2 then concat(sto_id, item_id) end) as sto_billing_expected_t_minus_2,\n", "count(distinct case when sto_creation_date = current_date - 2 and billed_quantity > 0 then concat(sto_id, item_id) end) as sto_billing_executed_t_minus_2,\n", "\n", "count(distinct case when sto_creation_date = current_date - 3 then concat(sto_id, item_id) end) as sto_billing_expected_t_minus_3,\n", "count(distinct case when sto_creation_date = current_date - 3 and billed_quantity > 0 then concat(sto_id, item_id) end) as sto_billing_executed_t_minus_3,\n", "\n", "count(distinct case when sto_creation_date between current_date - 7 and current_date - 1 then concat(sto_id, item_id) end) as sto_billing_expected_7d,\n", "count(distinct case when sto_creation_date between current_date - 7 and current_date - 1 and billed_quantity > 0 then concat(sto_id, item_id) end) as sto_billing_executed_7d,\n", "\n", "count(distinct case when sto_creation_date between current_date - 30 and current_date - 1 then concat(sto_id, item_id) end) as sto_billing_expected_30d,\n", "count(distinct case when sto_creation_date between current_date - 30 and current_date - 1 and billed_quantity > 0 then concat(sto_id, item_id) end) as sto_billing_executed_30d,\n", "\n", "\n", "\n", "sum(case when sto_creation_date = current_date - 1 then units_ordered end) as sto_quantity_billing_expected_t_minus_1,\n", "sum(case when sto_creation_date = current_date - 1 and billed_quantity > 0 then billed_quantity end) as sto_quantity_billing_executed_t_minus_1,\n", "\n", "sum(case when sto_creation_date = current_date - 2 then units_ordered end) as sto_quantity_billing_expected_t_minus_2,\n", "sum(case when sto_creation_date = current_date - 2 and billed_quantity > 0 then billed_quantity end) as sto_quantity_billing_executed_t_minus_2,\n", "\n", "sum(case when sto_creation_date = current_date - 3 then units_ordered end) as sto_quantity_billing_expected_t_minus_3,\n", "sum(case when sto_creation_date = current_date - 3 and billed_quantity > 0 then billed_quantity end) as sto_quantity_billing_executed_t_minus_3,\n", "\n", "sum(case when sto_creation_date between current_date - 7 and current_date - 1 then units_ordered end) as sto_quantity_billing_expected_7d,\n", "sum(case when sto_creation_date between current_date - 7 and current_date - 1 and billed_quantity > 0 then billed_quantity end) as sto_quantity_billing_executed_7d,\n", "\n", "sum(case when sto_creation_date between current_date - 30 and current_date - 1 then units_ordered end) as sto_quantity_billing_expected_30d,\n", "sum(case when sto_creation_date between current_date - 30 and current_date - 1 and billed_quantity > 0 then billed_quantity end) as sto_quantity_billing_executed_30d\n", "\n", "from\n", "sto_expiry a\n", "left join zone_tbl_treatment e on a.receiving_facility_id = e.facility_id\n", "group by\n", "1,2,3,4,5\n", "),\n", "\n", "adding_pan_india as\n", "(\n", "select\n", "*\n", "from\n", "sto_received_info\n", "where sto_billing_expected_7d > 0\n", "\n", "union all\n", "\n", "select\n", "'00 - Pan India' as zone,\n", "-999 as sender_facility_id,\n", "'00 - Pan India' as sender_facility_name,\n", "-999 as receiving_facility_id,\n", "'00 - Pan India' as receiver_facility_name,\n", "sum(sto_billing_expected_t_minus_1) as sto_billing_expected_t_minus_1,\n", "sum(sto_billing_executed_t_minus_1) as sto_billing_executed_t_minus_1,\n", "sum(sto_billing_expected_t_minus_2) as sto_billing_expected_t_minus_2,\n", "sum(sto_billing_executed_t_minus_2) as sto_billing_executed_t_minus_2,\n", "sum(sto_billing_expected_t_minus_3) as sto_billing_expected_t_minus_3,\n", "sum(sto_billing_executed_t_minus_3) as sto_billing_executed_t_minus_3,\n", "sum(sto_billing_expected_7d) as sto_billing_expected_7d,\n", "sum(sto_billing_executed_7d) as sto_billing_executed_7d,\n", "sum(sto_billing_expected_30d) as sto_billing_expected_30d,\n", "sum(sto_billing_executed_30d) as sto_billing_executed_30d,\n", "\n", "\n", "sum(sto_quantity_billing_expected_t_minus_1) as sto_quantity_billing_expected_t_minus_1,\n", "sum(sto_quantity_billing_executed_t_minus_1) as sto_quantity_billing_executed_t_minus_1,\n", "sum(sto_quantity_billing_expected_t_minus_2) as sto_quantity_billing_expected_t_minus_2,\n", "sum(sto_quantity_billing_executed_t_minus_2) as sto_quantity_billing_executed_t_minus_2,\n", "sum(sto_quantity_billing_expected_t_minus_3) as sto_quantity_billing_expected_t_minus_3,\n", "sum(sto_quantity_billing_executed_t_minus_3) as sto_quantity_billing_executed_t_minus_3,\n", "sum(sto_quantity_billing_expected_7d) as sto_quantity_billing_expected_7d,\n", "sum(sto_quantity_billing_executed_7d) as sto_quantity_billing_executed_7d,\n", "sum(sto_quantity_billing_expected_30d) as sto_quantity_billing_expected_30d,\n", "sum(sto_quantity_billing_executed_30d) as sto_quantity_billing_executed_30d\n", "from\n", "sto_received_info\n", "where sto_billing_expected_7d > 0\n", "group by 1,2,3,4,5\n", "\n", "union all\n", "\n", "select\n", "zone as zone,\n", "-999 as sender_facility_id,\n", "zone as sender_facility_name,\n", "-999 as receiving_facility_id,\n", "zone as receiver_facility_name,\n", "sum(sto_billing_expected_t_minus_1) as sto_billing_expected_t_minus_1,\n", "sum(sto_billing_executed_t_minus_1) as sto_billing_executed_t_minus_1,\n", "sum(sto_billing_expected_t_minus_2) as sto_billing_expected_t_minus_2,\n", "sum(sto_billing_executed_t_minus_2) as sto_billing_executed_t_minus_2,\n", "sum(sto_billing_expected_t_minus_3) as sto_billing_expected_t_minus_3,\n", "sum(sto_billing_executed_t_minus_3) as sto_billing_executed_t_minus_3,\n", "sum(sto_billing_expected_7d) as sto_billing_expected_7d,\n", "sum(sto_billing_executed_7d) as sto_billing_executed_7d,\n", "sum(sto_billing_expected_30d) as sto_billing_expected_30d,\n", "sum(sto_billing_executed_30d) as sto_billing_executed_30d,\n", "\n", "\n", "sum(sto_quantity_billing_expected_t_minus_1) as sto_quantity_billing_expected_t_minus_1,\n", "sum(sto_quantity_billing_executed_t_minus_1) as sto_quantity_billing_executed_t_minus_1,\n", "sum(sto_quantity_billing_expected_t_minus_2) as sto_quantity_billing_expected_t_minus_2,\n", "sum(sto_quantity_billing_executed_t_minus_2) as sto_quantity_billing_executed_t_minus_2,\n", "sum(sto_quantity_billing_expected_t_minus_3) as sto_quantity_billing_expected_t_minus_3,\n", "sum(sto_quantity_billing_executed_t_minus_3) as sto_quantity_billing_executed_t_minus_3,\n", "sum(sto_quantity_billing_expected_7d) as sto_quantity_billing_expected_7d,\n", "sum(sto_quantity_billing_executed_7d) as sto_quantity_billing_executed_7d,\n", "sum(sto_quantity_billing_expected_30d) as sto_quantity_billing_expected_30d,\n", "sum(sto_quantity_billing_executed_30d) as sto_quantity_billing_executed_30d\n", "from\n", "sto_received_info\n", "where sto_billing_expected_7d > 0\n", "group by 1,2,3,4,5\n", "),\n", " \n", "fillrates_calculations as\n", "(\n", "select\n", "zone,\n", "sender_facility_id,\n", "replace(replace(sender_facility_name,'Super Store',''), '- Warehouse','') as sender_facility_name,\n", "receiving_facility_id,\n", "replace(replace(receiver_facility_name,'Super Store',''), '- Warehouse','') as receiver_facility_name,\n", "coalesce(sto_billing_expected_t_minus_1,0) as sto_billing_expected_t_minus_1,\n", "coalesce(sto_billing_executed_t_minus_1,0) as sto_billing_executed_t_minus_1,\n", "coalesce(sto_billing_expected_7d,0) as sto_billing_expected_7d,\n", "coalesce(sto_billing_executed_7d,0) as sto_billing_executed_7d,\n", "coalesce(sto_billing_expected_30d,0) as sto_billing_expected_30d,\n", "coalesce(sto_billing_executed_30d,0) as sto_billing_executed_30d,\n", "\n", "\n", "coalesce(sto_quantity_billing_expected_t_minus_1,0) as sto_quantity_billing_expected_t_minus_1,\n", "coalesce(sto_quantity_billing_executed_t_minus_1,0) as sto_quantity_billing_executed_t_minus_1,\n", "coalesce(sto_quantity_billing_expected_7d,0) as sto_quantity_billing_expected_7d,\n", "coalesce(sto_quantity_billing_executed_7d,0) as sto_quantity_billing_executed_7d,\n", "coalesce(sto_quantity_billing_expected_30d,0) as sto_quantity_billing_expected_30d,\n", "coalesce(sto_quantity_billing_executed_30d,0) as sto_quantity_billing_executed_30d,\n", "\n", "\n", "concat(round((coalesce(sto_billing_executed_t_minus_1,0)*1.000/(coalesce(sto_billing_expected_t_minus_1,0) + 0.0001))*100), '%%') as billing_article_fr_t_minus_1,\n", "concat(round((coalesce(sto_billing_executed_7d,0)*1.000/(coalesce(sto_billing_expected_7d,0) + 0.0001))*100,0), '%%') as billing_article_fr_7d,\n", "concat(round((coalesce(sto_billing_executed_30d,0)*1.000/(coalesce(sto_billing_expected_30d,0) + 0.0001))*100,0), '%%') as billing_article_fr_30d,\n", "\n", "concat(round((coalesce(sto_quantity_billing_executed_t_minus_1,0)*1.000/(coalesce(sto_quantity_billing_expected_t_minus_1,0) + 0.0001))*100,0), '%%') as billing_quantity_fr_t_minus_1,\n", "concat(round((coalesce(sto_quantity_billing_executed_7d,0)*1.000/(coalesce(sto_quantity_billing_expected_7d,0) + 0.0001))*100,0), '%%') as billing_quantity_fr_7d,\n", "concat(round((coalesce(sto_quantity_billing_executed_30d,0)*1.000/(coalesce(sto_quantity_billing_expected_30d,0) + 0.0001))*100,0), '%%') as billing_quantity_fr_30\n", "from\n", "adding_pan_india\n", "--where facility_id not in (31,40,64)\n", ")\n", "\n", "select \n", "*\n", "from fillrates_calculations\n", "order by \n", "case when sender_facility_id = -999 then 'A' end,\n", "case\n", "when zone = '00 - Pan India' then 'A1'\n", "when zone = 'North' then 'A2'\n", "when zone = 'South' then 'A3'\n", "when zone = 'West' then 'A4'\n", "when zone = 'East' then 'A5' end,\n", "sender_facility_id,\n", "sto_billing_expected_7d desc,\n", "receiving_facility_id,\n", "receiver_facility_name\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_query = \"\"\"with\n", "\n", "zone_tbl_treatment as\n", "(\n", "select\n", "facility_id,\n", "max(zone) as zone\n", "from\n", "metrics.outlet_zone_mapping\n", "group by 1\n", "),\n", "\n", "item_level_info as\n", "(\n", "select\n", "item_id,\n", "name as item_name,\n", "brand,\n", "brand_id,\n", "manufacturer,\n", "variant_description,\n", "is_pl,\n", "variant_mrp,\n", "row_number() over (partition by item_id order by updated_at desc) as row_rank\n", "from \n", "lake_rpc.product_product\n", "),\n", "\n", "categories as\n", "(\n", "SELECT \n", "P.ID AS PID,\n", "P.NAME AS PRODUCT,\n", "C2.NAME AS L2,\n", "C.NAME AS L0,\n", "pt.name as product_type\n", "from lake_cms.gr_product P\n", "INNER join lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "INNER join lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID AND PCM.IS_PRIMARY=TRUE\n", "INNER join lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "INNER join lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "inner join lake_cms.gr_product_type pt on p.type_id=pt.id\n", "),\n", "\n", "category_pre as\n", "(\n", "SELECT \n", "item_id,\n", "product_id,\n", "cat.l2,\n", "cat.l0,\n", "cat.product_type\n", "from lake_rpc.item_product_mapping  rpc\n", "INNER JOIN categories cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL\n", "),\n", "\n", "ptype_tbl as\n", "(\n", "select \n", "item_id,\n", "max(l2) as l2,\n", "max(l0) as l0,\n", "max(product_type) as ptype\n", "from category_pre\n", "group by 1\n", "),\n", "\n", "sto_data as\n", "(\n", "select\n", "a.sto_id,\n", "a.item_id,\n", "a.sender_outlet_id,\n", "a.sender_outlet_name,\n", "a.receiving_outlet_id,\n", "a.receiver_outlet_name,\n", "a.item_name as item_name,\n", "a.actual_sales as value_ordered,\n", "a.actual_sales*1.000/expected_quantity as landing_price,\n", "a.expected_quantity as units_ordered,\n", "a.billed_quantity as billed_quantity,\n", "a.inwarded_quantity as grn_quantity,\n", "sto_type,\n", "sto_state,\n", "date(a.sto_created_at) as sto_creation_date,\n", "date(a.grn_created_at) as sto_grn_date,\n", "date(a.sto_expired_at) as sto_expiry_date,\n", "date(a.sto_partial_billed_at) as sto_partial_bill_date,\n", "date(a.sto_billed_at) as sto_bill_date,\n", "extract(month from date(sto_expiry_date)) as expiry_month,\n", "extract(month from date(sto_grn_date)) as grn_month,\n", "coalesce(date(a.sto_partial_billed_at), date(a.sto_billed_at)) as sto_bill_date1,\n", "case when inwarded_quantity > 0 then 1 else 0 end as grn_flag\n", "from metrics.esto_details a\n", "left join ptype_tbl d on a.item_id = d.item_id\n", "where \n", "--AND (date(sto_expired_at) >= current_date - 90 or date(grn_created_at) >= current_date - 90 or date(sto_billed_at) >= current_date - 90)\n", "date(sto_created_at) between current_date - 180 and current_date - 1\n", "and d.l0 not ilike '%%fruits%%'\n", "and sto_state <> 'Created'\n", "--and b.outlet_id not in (635,287) -- Outlets used for transfer in Kolkata\n", "),\n", "\n", "sto_expiry as\n", "(\n", "select\n", "a.*,\n", "c.facility_id as receiving_facility_id,\n", "d.facility_id as sender_facility_id,\n", "f.name as receiving_city_name,\n", "g.name as receiver_facility_name,\n", "h.name as sender_facility_name,\n", "sto_expiry_date - sto_creation_date as tat_days,\n", "case when grn_flag = 1 then sto_grn_date - sto_creation_date end as delivery_days\n", "from\n", "sto_data a\n", "left join (select * from lake_retail.console_outlet where active = true) c on a.receiving_outlet_id = c.id\n", "left join lake_retail.console_location f on c.tax_location_id = f.id\n", "left join lake_crates.facility g on c.facility_id = g.id\n", "left join (select * from lake_retail.console_outlet where active = true) d on a.sender_outlet_id = d.id\n", "left join lake_retail.console_location e on d.tax_location_id = e.id\n", "left join lake_crates.facility h on d.facility_id = h.id\n", "),\n", "\n", "sto_received_info as\n", "(\n", "select\n", "e.zone,\n", "sender_facility_id,\n", "sender_facility_name,\n", "receiving_facility_id,\n", "receiver_facility_name,\n", "\n", "count(distinct case when sto_creation_date = current_date - 1 then concat(sto_id, item_id) end) as sto_billing_expected_t_minus_1,\n", "count(distinct case when sto_creation_date = current_date - 1 and billed_quantity > 0 then concat(sto_id, item_id) end) as sto_billing_executed_t_minus_1,\n", "\n", "count(distinct case when sto_creation_date between current_date - 7 and current_date - 1 then concat(sto_id, item_id) end) as sto_billing_expected_7d,\n", "count(distinct case when sto_creation_date between current_date - 7 and current_date - 1 and billed_quantity > 0 then concat(sto_id, item_id) end) as sto_billing_executed_7d,\n", "\n", "count(distinct case when sto_creation_date between current_date - 30 and current_date - 1 then concat(sto_id, item_id) end) as sto_billing_expected_30d,\n", "count(distinct case when sto_creation_date between current_date - 30 and current_date - 1 and billed_quantity > 0 then concat(sto_id, item_id) end) as sto_billing_executed_30d,\n", "\n", "\n", "\n", "sum(case when sto_creation_date = current_date - 1 then units_ordered end) as sto_quantity_billing_expected_t_minus_1,\n", "sum(case when sto_creation_date = current_date - 1 and billed_quantity > 0 then billed_quantity end) as sto_quantity_billing_executed_t_minus_1,\n", "\n", "sum(case when sto_creation_date between current_date - 7 and current_date - 1 then units_ordered end) as sto_quantity_billing_expected_7d,\n", "sum(case when sto_creation_date between current_date - 7 and current_date - 1 and billed_quantity > 0 then billed_quantity end) as sto_quantity_billing_executed_7d,\n", "\n", "sum(case when sto_creation_date between current_date - 30 and current_date - 1 then units_ordered end) as sto_quantity_billing_expected_30d,\n", "sum(case when sto_creation_date between current_date - 30 and current_date - 1 and billed_quantity > 0 then billed_quantity end) as sto_quantity_billing_executed_30d\n", "\n", "from\n", "sto_expiry a\n", "left join zone_tbl_treatment e on a.receiving_facility_id = e.facility_id\n", "group by\n", "1,2,3,4,5\n", "),\n", "\n", "adding_pan_india as\n", "(\n", "select\n", "*\n", "from\n", "sto_received_info\n", "where sto_billing_expected_7d > 0\n", "\n", "union all\n", "\n", "select\n", "'00 - Pan India' as zone,\n", "-999 as sender_facility_id,\n", "'00 - Pan India' as sender_facility_name,\n", "-999 as receiving_facility_id,\n", "'00 - Pan India' as receiver_facility_name,\n", "sum(sto_billing_expected_t_minus_1) as sto_billing_expected_t_minus_1,\n", "sum(sto_billing_executed_t_minus_1) as sto_billing_executed_t_minus_1,\n", "sum(sto_billing_expected_7d) as sto_billing_expected_7d,\n", "sum(sto_billing_executed_7d) as sto_billing_executed_7d,\n", "sum(sto_billing_expected_30d) as sto_billing_expected_30d,\n", "sum(sto_billing_executed_30d) as sto_billing_executed_30d,\n", "\n", "\n", "sum(sto_quantity_billing_expected_t_minus_1) as sto_quantity_billing_expected_t_minus_1,\n", "sum(sto_quantity_billing_executed_t_minus_1) as sto_quantity_billing_executed_t_minus_1,\n", "sum(sto_quantity_billing_expected_7d) as sto_quantity_billing_expected_7d,\n", "sum(sto_quantity_billing_executed_7d) as sto_quantity_billing_executed_7d,\n", "sum(sto_quantity_billing_expected_30d) as sto_quantity_billing_expected_30d,\n", "sum(sto_quantity_billing_executed_30d) as sto_quantity_billing_executed_30d\n", "from\n", "sto_received_info\n", "where sto_billing_expected_7d > 0\n", "group by 1,2,3,4,5\n", "\n", "union all\n", "\n", "select\n", "zone as zone,\n", "-999 as sender_facility_id,\n", "zone as sender_facility_name,\n", "-999 as receiving_facility_id,\n", "zone as receiver_facility_name,\n", "sum(sto_billing_expected_t_minus_1) as sto_billing_expected_t_minus_1,\n", "sum(sto_billing_executed_t_minus_1) as sto_billing_executed_t_minus_1,\n", "sum(sto_billing_expected_7d) as sto_billing_expected_7d,\n", "sum(sto_billing_executed_7d) as sto_billing_executed_7d,\n", "sum(sto_billing_expected_30d) as sto_billing_expected_30d,\n", "sum(sto_billing_executed_30d) as sto_billing_executed_30d,\n", "\n", "\n", "sum(sto_quantity_billing_expected_t_minus_1) as sto_quantity_billing_expected_t_minus_1,\n", "sum(sto_quantity_billing_executed_t_minus_1) as sto_quantity_billing_executed_t_minus_1,\n", "sum(sto_quantity_billing_expected_7d) as sto_quantity_billing_expected_7d,\n", "sum(sto_quantity_billing_executed_7d) as sto_quantity_billing_executed_7d,\n", "sum(sto_quantity_billing_expected_30d) as sto_quantity_billing_expected_30d,\n", "sum(sto_quantity_billing_executed_30d) as sto_quantity_billing_executed_30d\n", "from\n", "sto_received_info\n", "where sto_billing_expected_7d > 0\n", "group by 1,2,3,4,5\n", "),\n", " \n", "fillrates_calculations as\n", "(\n", "select\n", "zone,\n", "sender_facility_id,\n", "sender_facility_name,\n", "receiving_facility_id,\n", "receiver_facility_name,\n", "coalesce(sto_billing_expected_t_minus_1,0) as sto_billing_expected_t_minus_1,\n", "coalesce(sto_billing_executed_t_minus_1,0) as sto_billing_executed_t_minus_1,\n", "\n", "\n", "coalesce(sto_quantity_billing_expected_t_minus_1,0) as sto_quantity_billing_expected_t_minus_1,\n", "coalesce(sto_quantity_billing_executed_t_minus_1,0) as sto_quantity_billing_executed_t_minus_1,\n", "\n", "\n", "concat(round((coalesce(sto_billing_executed_t_minus_1,0)*1.000/(coalesce(sto_billing_expected_t_minus_1,0) + 0.0001))*100), '%%') as billing_article_fr_t_minus_1,\n", "concat(round((coalesce(sto_billing_executed_7d,0)*1.000/(coalesce(sto_billing_expected_7d,0) + 0.0001))*100), '%%') as billing_article_fr_7d,\n", "concat(round((coalesce(sto_billing_executed_30d,0)*1.000/(coalesce(sto_billing_expected_30d,0) + 0.0001))*100), '%%') as billing_article_fr_30d,\n", "\n", "concat(round((coalesce(sto_quantity_billing_executed_t_minus_1,0)*1.000/(coalesce(sto_quantity_billing_expected_t_minus_1,0) + 0.0001))*100), '%%') as billing_quantity_fr_t_minus_1,\n", "concat(round((coalesce(sto_quantity_billing_executed_7d,0)*1.000/(coalesce(sto_quantity_billing_expected_7d,0) + 0.0001))*100), '%%') as billing_quantity_fr_7d,\n", "concat(round((coalesce(sto_quantity_billing_executed_30d,0)*1.000/(coalesce(sto_quantity_billing_expected_30d,0) + 0.0001))*100), '%%') as billing_quantity_fr_30\n", "from\n", "adding_pan_india\n", "--where facility_id not in (31,40,64)\n", ")\n", "\n", "select \n", "zone,\n", "replace(replace(sender_facility_name,'Super Store',''), '- Warehouse','') as sender_facility_name, \n", "replace(replace(receiver_facility_name,'Super Store',''), '- Warehouse','') as receiver_facility_name,\n", "sto_billing_expected_t_minus_1,\n", "sto_billing_executed_t_minus_1,\n", "sto_quantity_billing_expected_t_minus_1,\n", "sto_quantity_billing_executed_t_minus_1,\n", "billing_article_fr_t_minus_1,\n", "billing_article_fr_7d,\n", "billing_article_fr_30d,\n", "\n", "billing_quantity_fr_t_minus_1,\n", "billing_quantity_fr_7d,\n", "billing_quantity_fr_30\n", "from fillrates_calculations\n", "where sto_billing_expected_t_minus_1 > 0\n", "order by \n", "case when sender_facility_id = -999 then 'A' end,\n", "case\n", "when zone = '00 - Pan India' then 'A1'\n", "when zone = 'North' then 'A2'\n", "when zone = 'South' then 'A3'\n", "when zone = 'West' then 'A4'\n", "when zone = 'East' then 'A5' end,\n", "sender_facility_id,\n", "sto_billing_expected_t_minus_1 desc,\n", "receiving_facility_id,\n", "receiver_facility_name\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_query = \"\"\"with\n", "\n", "zone_tbl_treatment as\n", "(\n", "select\n", "facility_id,\n", "max(zone) as zone\n", "from\n", "metrics.outlet_zone_mapping\n", "group by 1\n", "),\n", "\n", "item_level_info as\n", "(\n", "select\n", "item_id,\n", "name as item_name,\n", "brand,\n", "brand_id,\n", "manufacturer,\n", "variant_description,\n", "is_pl,\n", "variant_mrp,\n", "row_number() over (partition by item_id order by updated_at desc) as row_rank\n", "from \n", "lake_rpc.product_product\n", "),\n", "\n", "categories as\n", "(\n", "SELECT \n", "P.ID AS PID,\n", "P.NAME AS PRODUCT,\n", "C2.NAME AS L2,\n", "C.NAME AS L0,\n", "pt.name as product_type\n", "from lake_cms.gr_product P\n", "INNER join lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "INNER join lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID AND PCM.IS_PRIMARY=TRUE\n", "INNER join lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "INNER join lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "inner join lake_cms.gr_product_type pt on p.type_id=pt.id\n", "),\n", "\n", "category_pre as\n", "(\n", "SELECT \n", "item_id,\n", "product_id,\n", "cat.l2,\n", "cat.l0,\n", "cat.product_type\n", "from lake_rpc.item_product_mapping  rpc\n", "INNER JOIN categories cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL\n", "),\n", "\n", "ptype_tbl as\n", "(\n", "select \n", "item_id,\n", "max(l2) as l2,\n", "max(l0) as l0,\n", "max(product_type) as ptype\n", "from category_pre\n", "group by 1\n", "),\n", "\n", "sto_data as\n", "(\n", "select\n", "a.sto_id,\n", "a.item_id,\n", "a.sender_outlet_id,\n", "a.sender_outlet_name,\n", "a.receiving_outlet_id,\n", "a.receiver_outlet_name,\n", "a.item_name as item_name,\n", "a.actual_sales as value_ordered,\n", "a.actual_sales*1.000/expected_quantity as landing_price,\n", "a.expected_quantity as units_ordered,\n", "a.billed_quantity as billed_quantity,\n", "a.inwarded_quantity as grn_quantity,\n", "sto_type,\n", "sto_state,\n", "date(a.sto_created_at) as sto_creation_date,\n", "date(a.grn_created_at) as sto_grn_date,\n", "date(a.sto_expired_at) as sto_expiry_date,\n", "date(a.sto_partial_billed_at) as sto_partial_bill_date,\n", "date(a.sto_billed_at) as sto_bill_date,\n", "extract(month from date(sto_expiry_date)) as expiry_month,\n", "extract(month from date(sto_grn_date)) as grn_month,\n", "coalesce(date(a.sto_partial_billed_at), date(a.sto_billed_at)) as sto_bill_date1,\n", "case when inwarded_quantity > 0 then 1 else 0 end as grn_flag\n", "from metrics.esto_details a\n", "left join ptype_tbl d on a.item_id = d.item_id\n", "where \n", "--AND (date(sto_expired_at) >= current_date - 90 or date(grn_created_at) >= current_date - 90 or date(sto_billed_at) >= current_date - 90)\n", "date(sto_created_at) between current_date - 180 and current_date - 1\n", "and d.l0 not ilike '%%fruits%%'\n", "and sto_state <> 'Created'\n", "--and b.outlet_id not in (635,287) -- Outlets used for transfer in Kolkata\n", "),\n", "\n", "sto_expiry as\n", "(\n", "select\n", "a.*,\n", "c.facility_id as receiving_facility_id,\n", "d.facility_id as sender_facility_id,\n", "f.name as receiving_city_name,\n", "g.name as receiver_facility_name,\n", "h.name as sender_facility_name,\n", "sto_expiry_date - sto_creation_date as tat_days,\n", "case when grn_flag = 1 then sto_grn_date - sto_creation_date end as delivery_days\n", "from\n", "sto_data a\n", "left join (select * from lake_retail.console_outlet where active = true) c on a.receiving_outlet_id = c.id\n", "left join lake_retail.console_location f on c.tax_location_id = f.id\n", "left join lake_crates.facility g on c.facility_id = g.id\n", "left join (select * from lake_retail.console_outlet where active = true) d on a.sender_outlet_id = d.id\n", "left join lake_retail.console_location e on d.tax_location_id = e.id\n", "left join lake_crates.facility h on d.facility_id = h.id\n", "),\n", "\n", "sto_received_info as\n", "(\n", "select\n", "e.zone,\n", "sender_facility_id,\n", "sender_facility_name,\n", "receiving_facility_id,\n", "receiver_facility_name,\n", "\n", "count(distinct case when sto_creation_date = current_date - 1 then concat(sto_id, item_id) end) as sto_billing_expected_t_minus_1,\n", "count(distinct case when sto_creation_date = current_date - 1 and billed_quantity > 0 then concat(sto_id, item_id) end) as sto_billing_executed_t_minus_1,\n", "\n", "count(distinct case when sto_creation_date between current_date - 7 and current_date - 1 then concat(sto_id, item_id) end) as sto_billing_expected_7d,\n", "count(distinct case when sto_creation_date between current_date - 7 and current_date - 1 and billed_quantity > 0 then concat(sto_id, item_id) end) as sto_billing_executed_7d,\n", "\n", "count(distinct case when sto_creation_date between current_date - 30 and current_date - 1 then concat(sto_id, item_id) end) as sto_billing_expected_30d,\n", "count(distinct case when sto_creation_date between current_date - 30 and current_date - 1 and billed_quantity > 0 then concat(sto_id, item_id) end) as sto_billing_executed_30d,\n", "\n", "\n", "\n", "sum(case when sto_creation_date = current_date - 1 then units_ordered end) as sto_quantity_billing_expected_t_minus_1,\n", "sum(case when sto_creation_date = current_date - 1 and billed_quantity > 0 then billed_quantity end) as sto_quantity_billing_executed_t_minus_1,\n", "\n", "sum(case when sto_creation_date between current_date - 7 and current_date - 1 then units_ordered end) as sto_quantity_billing_expected_7d,\n", "sum(case when sto_creation_date between current_date - 7 and current_date - 1 and billed_quantity > 0 then billed_quantity end) as sto_quantity_billing_executed_7d,\n", "\n", "sum(case when sto_creation_date between current_date - 30 and current_date - 1 then units_ordered end) as sto_quantity_billing_expected_30d,\n", "sum(case when sto_creation_date between current_date - 30 and current_date - 1 and billed_quantity > 0 then billed_quantity end) as sto_quantity_billing_executed_30d\n", "\n", "from\n", "sto_expiry a\n", "left join zone_tbl_treatment e on a.receiving_facility_id = e.facility_id\n", "group by\n", "1,2,3,4,5\n", "),\n", "\n", "adding_pan_india as\n", "(\n", "select\n", "*\n", "from\n", "sto_received_info\n", "where sto_billing_expected_7d > 0\n", "\n", "union all\n", "\n", "select\n", "'00 - Pan India' as zone,\n", "-999 as sender_facility_id,\n", "'00 - Pan India' as sender_facility_name,\n", "-999 as receiving_facility_id,\n", "'00 - Pan India' as receiver_facility_name,\n", "sum(sto_billing_expected_t_minus_1) as sto_billing_expected_t_minus_1,\n", "sum(sto_billing_executed_t_minus_1) as sto_billing_executed_t_minus_1,\n", "sum(sto_billing_expected_7d) as sto_billing_expected_7d,\n", "sum(sto_billing_executed_7d) as sto_billing_executed_7d,\n", "sum(sto_billing_expected_30d) as sto_billing_expected_30d,\n", "sum(sto_billing_executed_30d) as sto_billing_executed_30d,\n", "\n", "\n", "sum(sto_quantity_billing_expected_t_minus_1) as sto_quantity_billing_expected_t_minus_1,\n", "sum(sto_quantity_billing_executed_t_minus_1) as sto_quantity_billing_executed_t_minus_1,\n", "sum(sto_quantity_billing_expected_7d) as sto_quantity_billing_expected_7d,\n", "sum(sto_quantity_billing_executed_7d) as sto_quantity_billing_executed_7d,\n", "sum(sto_quantity_billing_expected_30d) as sto_quantity_billing_expected_30d,\n", "sum(sto_quantity_billing_executed_30d) as sto_quantity_billing_executed_30d\n", "from\n", "sto_received_info\n", "where sto_billing_expected_7d > 0\n", "group by 1,2,3,4,5\n", "\n", "union all\n", "\n", "select\n", "zone as zone,\n", "-999 as sender_facility_id,\n", "zone as sender_facility_name,\n", "-999 as receiving_facility_id,\n", "zone as receiver_facility_name,\n", "sum(sto_billing_expected_t_minus_1) as sto_billing_expected_t_minus_1,\n", "sum(sto_billing_executed_t_minus_1) as sto_billing_executed_t_minus_1,\n", "sum(sto_billing_expected_7d) as sto_billing_expected_7d,\n", "sum(sto_billing_executed_7d) as sto_billing_executed_7d,\n", "sum(sto_billing_expected_30d) as sto_billing_expected_30d,\n", "sum(sto_billing_executed_30d) as sto_billing_executed_30d,\n", "\n", "\n", "sum(sto_quantity_billing_expected_t_minus_1) as sto_quantity_billing_expected_t_minus_1,\n", "sum(sto_quantity_billing_executed_t_minus_1) as sto_quantity_billing_executed_t_minus_1,\n", "sum(sto_quantity_billing_expected_7d) as sto_quantity_billing_expected_7d,\n", "sum(sto_quantity_billing_executed_7d) as sto_quantity_billing_executed_7d,\n", "sum(sto_quantity_billing_expected_30d) as sto_quantity_billing_expected_30d,\n", "sum(sto_quantity_billing_executed_30d) as sto_quantity_billing_executed_30d\n", "from\n", "sto_received_info\n", "where sto_billing_expected_7d > 0\n", "group by 1,2,3,4,5\n", "\n", "union all\n", "\n", "select\n", "zone as zone,\n", "sender_facility_id,\n", "sender_facility_name,\n", "-999 as receiving_facility_id,\n", "zone as receiver_facility_name,\n", "sum(sto_billing_expected_t_minus_1) as sto_billing_expected_t_minus_1,\n", "sum(sto_billing_executed_t_minus_1) as sto_billing_executed_t_minus_1,\n", "sum(sto_billing_expected_7d) as sto_billing_expected_7d,\n", "sum(sto_billing_executed_7d) as sto_billing_executed_7d,\n", "sum(sto_billing_expected_30d) as sto_billing_expected_30d,\n", "sum(sto_billing_executed_30d) as sto_billing_executed_30d,\n", "\n", "\n", "sum(sto_quantity_billing_expected_t_minus_1) as sto_quantity_billing_expected_t_minus_1,\n", "sum(sto_quantity_billing_executed_t_minus_1) as sto_quantity_billing_executed_t_minus_1,\n", "sum(sto_quantity_billing_expected_7d) as sto_quantity_billing_expected_7d,\n", "sum(sto_quantity_billing_executed_7d) as sto_quantity_billing_executed_7d,\n", "sum(sto_quantity_billing_expected_30d) as sto_quantity_billing_expected_30d,\n", "sum(sto_quantity_billing_executed_30d) as sto_quantity_billing_executed_30d\n", "from\n", "sto_received_info\n", "where sto_billing_expected_7d > 0\n", "group by 1,2,3,4,5\n", "),\n", " \n", "fillrates_calculations as\n", "(\n", "select\n", "zone,\n", "sender_facility_id,\n", "sender_facility_name,\n", "receiving_facility_id,\n", "receiver_facility_name,\n", "coalesce(sto_billing_expected_t_minus_1,0) as sto_billing_expected_t_minus_1,\n", "coalesce(sto_billing_executed_t_minus_1,0) as sto_billing_executed_t_minus_1,\n", "\n", "\n", "coalesce(sto_quantity_billing_expected_t_minus_1,0) as sto_quantity_billing_expected_t_minus_1,\n", "coalesce(sto_quantity_billing_executed_t_minus_1,0) as sto_quantity_billing_executed_t_minus_1,\n", "\n", "\n", "concat(round((coalesce(sto_billing_executed_t_minus_1,0)*1.000/(coalesce(sto_billing_expected_t_minus_1,0) + 0.0001))*100), '%%') as billing_article_fr_t_minus_1,\n", "concat(round((coalesce(sto_billing_executed_7d,0)*1.000/(coalesce(sto_billing_expected_7d,0) + 0.0001))*100), '%%') as billing_article_fr_7d,\n", "concat(round((coalesce(sto_billing_executed_30d,0)*1.000/(coalesce(sto_billing_expected_30d,0) + 0.0001))*100), '%%') as billing_article_fr_30d,\n", "\n", "concat(round((coalesce(sto_quantity_billing_executed_t_minus_1,0)*1.000/(coalesce(sto_quantity_billing_expected_t_minus_1,0) + 0.0001))*100), '%%') as billing_quantity_fr_t_minus_1,\n", "concat(round((coalesce(sto_quantity_billing_executed_7d,0)*1.000/(coalesce(sto_quantity_billing_expected_7d,0) + 0.0001))*100), '%%') as billing_quantity_fr_7d,\n", "concat(round((coalesce(sto_quantity_billing_executed_30d,0)*1.000/(coalesce(sto_quantity_billing_expected_30d,0) + 0.0001))*100), '%%') as billing_quantity_fr_30\n", "from\n", "adding_pan_india\n", "--where facility_id not in (31,40,64)\n", ")\n", "\n", "select \n", "zone,\n", "replace(replace(sender_facility_name,'Super Store',''), '- Warehouse','') as sender_facility_name, \n", "replace(replace(receiver_facility_name,'Super Store',''), '- Warehouse','') as receiver_facility_name,\n", "sto_billing_expected_t_minus_1,\n", "sto_billing_executed_t_minus_1,\n", "sto_quantity_billing_expected_t_minus_1,\n", "sto_quantity_billing_executed_t_minus_1,\n", "billing_article_fr_t_minus_1,\n", "billing_article_fr_7d,\n", "billing_article_fr_30d,\n", "\n", "billing_quantity_fr_t_minus_1,\n", "billing_quantity_fr_7d,\n", "billing_quantity_fr_30\n", "from fillrates_calculations\n", "where sto_billing_expected_t_minus_1 > 0\n", "order by \n", "case when sender_facility_id = -999 then 'A' end,\n", "case when receiving_facility_id = -999 then 'A0' end,\n", "case\n", "when zone = '00 - Pan India' then 'A1'\n", "when zone = 'North' then 'A2'\n", "when zone = 'South' then 'A3'\n", "when zone = 'West' then 'A4'\n", "when zone = 'East' then 'A5' end,\n", "\n", "case when (zone = 'North') and (receiver_facility_name = 'North') then 'A7'\n", "when (zone = 'South') and (receiver_facility_name = 'South') then 'A8'\n", "when (zone = 'West') and (receiver_facility_name = 'West') then 'A9'\n", "when(zone = 'East') and (receiver_facility_name = 'East') then 'A10' end,\n", "\n", "sender_facility_id,\n", "sto_billing_expected_t_minus_1 desc,\n", "-- receiving_facility_id,\n", "receiver_facility_name\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["o = pd.read_sql_query(sql=sto_query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["o[\"sto_billing_expected_t_minus_1\"] = o[\"sto_billing_expected_t_minus_1\"].apply(\n", "    lambda x: format(x, \",d\")\n", ")\n", "o[\"sto_billing_executed_t_minus_1\"] = o[\"sto_billing_executed_t_minus_1\"].apply(\n", "    lambda x: format(x, \",d\")\n", ")\n", "o[\"sto_quantity_billing_expected_t_minus_1\"] = o[\n", "    \"sto_quantity_billing_expected_t_minus_1\"\n", "].apply(lambda x: format(x, \",d\"))\n", "o[\"sto_quantity_billing_executed_t_minus_1\"] = o[\n", "    \"sto_quantity_billing_executed_t_minus_1\"\n", "].apply(lambda x: format(x, \",d\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# o.rename(columns={\"zone\": \"Zone\",\n", "#                   \"sender_facility_id\": \"Sender ID\",\n", "#                   \"sender_facility_name\": \"Sender Facility\",\n", "#                   \"receiving_facility_id\": \"Receiving ID\",\n", "#                   \"receiver_facility_name\": \"Receiving Facility\",\n", "#                   \"sto_billing_expected_t_minus_1\": \"Yesterday's Expected STO Articles\",\n", "#                   \"sto_billing_executed_t_minus_1\": \"Yesterday's Billed STO Articles\",\n", "#                   \"sto_billing_expected_7d\": \"Last 7 days Expected STO Articles\",\n", "#                   \"sto_billing_executed_7d\": \"Last 7 days Billed STO Articles\",\n", "#                   \"sto_billing_expected_30d\": \"Last 30 days Expected STO Articles\",\n", "#                   \"sto_billing_executed_30d\": \"Last 30 days Billed STO Articles\",\n", "#                   \"sto_quantity_billing_expected_t_minus_1\": \"Yesterday's Expected STO Quantity\",\n", "#                   \"sto_quantity_billing_executed_t_minus_1\": \"Yesterday's Billed STO Quantity\",\n", "#                   \"sto_quantity_billing_expected_7d\": \"Last 7 days Expected STO Quantity\",\n", "#                   \"sto_quantity_billing_executed_7d\": \"Last 7 days Billed STO Quantity\",\n", "#                   \"sto_quantity_billing_expected_30d\": \"Last 30 days Expected STO Quantity\",\n", "#                   \"sto_quantity_billing_executed_30d\": \"Last 30 days Billed STO Quantity\",\n", "#                   \"billing_article_fr_t_minus_1\": \"Yesterday's Article Billing Fill Rate\",\n", "#                   \"billing_article_fr_7d\": \"Last 7 days Article Billing Fill Rate\",\n", "#                   \"billing_article_fr_30d\": \"Last 30 days Article Billing Fill Rate\",\n", "#                   \"billing_quantity_fr_t_minus_1\": \"Yesterday's Billing Qty Fill Rate\",\n", "#                   \"billing_quantity_fr_7d\": \"Last 7 days Billing Qty Fill Rate\",\n", "#                   \"billing_quantity_fr_30\": \"Last 30 days Billing Qty Fill Rate\",\n", "\n", "\n", "#                  },inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["o.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1LUYNNZOqvAtxzjaKO1zAwNKukFi2ocpw2Kg9FPWfDAM\"\n", "time_period_considered = (datetime.now()).strftime(\"%Y-%m-%d\")\n", "\n", "# transfer_fill_rates = pb.from_sheets(sheet_id, \"Transfer Fill Rates\")\n", "\n", "# transfer_fill_rates=transfer_fill_rates.drop([2])\n", "\n", "# transfer_fill_rates.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tfl = o.to_dict(orient=\"rows\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tfl[1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from jinja2 import Template\n", "\n", "from_email = \"<EMAIL>\"\n", "\n", "email_list = pb.from_sheets(sheet_id, \"Email\")\n", "to_email = list(email_list[\"Email\"])\n", "\n", "# to_email =['<EMAIL>']\n", "subject = \"Transfer Fill Rate Report for \" + time_period_considered\n", "\n", "# cwd = os.getcwd()\n", "with open(\n", "    os.path.join(\n", "        cwd,\n", "        \"esto_mailer.html\",\n", "    ),\n", "    \"r\",\n", ") as f:\n", "    t = Template(f.read())\n", "\n", "\n", "rendered = t.render(\n", "    tfl=tfl,\n", "    time_period_considered=time_period_considered,\n", ")\n", "\n", "\n", "pb.send_email(from_email, to_email, subject, html_content=rendered)\n", "\n", "print(\"mail sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}