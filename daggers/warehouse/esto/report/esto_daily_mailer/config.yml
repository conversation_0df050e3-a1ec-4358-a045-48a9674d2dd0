template_name: notebook
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
path: warehouse/esto/report/esto_daily_mailer
namespace: warehouse
project_name: esto
dag_name: esto_daily_mailer
version: 4
owner:
  email: <EMAIL>
  slack_id: UABB9AK9V
schedule:
  start_date: '2021-04-07T03:30:00'
  interval: 30 3 * * *
notebook:
  # https://airflow.apache.org/macros.html
  # you can define your own parameters
  parameters:
dag_type: report
escalation_priority: low
schedule_type: fixed
sla: 120 minutes
tags: []
support_files:
- esto_mailer.html

paused: true
executor:
  type: kubernetes
  config:
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    node_selectors:
      nodetype: spot
    tolerations:
    - key: service
      operator: Equal
      value: airflow
      effect: NoSchedule
    cpu:
      request: 0.5
      limit: 1
    memory:
      request: 2G
      limit: 5G
    volume_mounts:
    - name: airflow-dags
      subPath: airflow/plugins
      mountPath: /usr/local/airflow/plugins
slack_alert_configs:
- channel: gobd-data-alerts
