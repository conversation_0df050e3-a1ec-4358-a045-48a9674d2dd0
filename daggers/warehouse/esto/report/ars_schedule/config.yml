dag_name: ars_schedule
dag_type: report
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: warehouse
notebooks:
- name: notebook
  parameters:
  tag: level1
- name: assortment_hyd
  parameters:
  tag: level2
owner:
  email: <EMAIL>
  slack_id: U03RQJY8HAT
path: warehouse/esto/report/ars_schedule
paused: true
project_name: esto
schedule:
  interval: 0 */2 * * *
  start_date: '2022-01-17T11:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 7
