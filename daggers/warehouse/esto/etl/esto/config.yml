dag_name: esto
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 16G
      request: 12G
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: warehouse
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RQJY8HAT
path: warehouse/esto/etl/esto
paused: true
project_name: esto
schedule:
  interval: 30 * * * *
  start_date: '2022-10-21T12:00:00'
schedule_type: fixed
sla: 30 minutes
slack_alert_configs:
- channel: bl-data-airflow-alerts
support_files: []
tags: []
template_name: notebook
version: 70
