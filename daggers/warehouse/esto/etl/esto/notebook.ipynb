{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "from collections import defaultdict\n", "from datetime import date, datetime, timedelta\n", "import pytz\n", "import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import sqlalchemy as sqla\n", "\n", "pd.set_option(\"display.max_columns\", 100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail_db = pb.get_connection(\"retail\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tz = pytz.timezone(\"Asia/Calcutta\")\n", "end = datetime.now(tz).date() - <PERSON><PERSON><PERSON>(days=0)\n", "start = datetime.now(tz).date() - <PERSON><PERSON><PERSON>(days=14)\n", "print(start, end)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Basic STO Information"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### For Forward STOs (from warehouses to darkstores), the table will update for last 5 days\n", "#### For Reverse STOs (from darkstores to warehouses), the table will update for last 14 days"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "st = time.time()\n", "qq1 = f\"\"\"SELECT ps.bulk_sto_id,\n", "       sto.sto_id,\n", "       sto.outlet_id AS sender_outlet_id,\n", "       o.name AS sender_outlet_name,\n", "       sto.merchant_outlet_id AS receiving_outlet_id,\n", "       o1.name AS receiver_outlet_name,\n", "       sto.delivery_date + interval '5' HOUR + interval '30' MINUTE as delivery_date,\n", "       sto.created_at + interval '5' HOUR + interval '30' MINUTE as sto_created_at,\n", "       CASE\n", "           WHEN sto.sto_type = 1 THEN 'Manual'\n", "           WHEN sto.sto_type = 2 THEN 'Automated'\n", "           WHEN sto.sto_type = 3 THEN 'No INV Check STO'\n", "           WHEN sto.sto_type = 4 THEN 'B2B STO'\n", "           WHEN sto.sto_type = 5 THEN 'B2B STO NO INVENTORY CHECK'\n", "       END AS sto_type,\n", "       CASE\n", "           WHEN sto.sto_state = 1 THEN 'Created'\n", "           WHEN sto.sto_state = 2 THEN 'Billed'\n", "           WHEN sto.sto_state = 5 THEN 'Partial-Billed'\n", "           WHEN sto.sto_state = 3 THEN 'Expired'\n", "           WHEN sto.sto_state = 4 THEN 'Inward'\n", "           ELSE 'New State'\n", "       END AS sto_state,\n", "       case when ps.created_by in (14,3511) then 0 else 1 end as Manual_sto,\n", "       (ps.dispatch_time + interval '330' MINUTE) as dispatch_time,\n", "       items.item_id,\n", "       items.item_name,\n", "       items.mrp,\n", "       items.ars_run_id,\n", "       items.expected_quantity,\n", "       items.reserved_quantity,\n", "       ars_run_flag,\n", "       manual_run,\n", "       ars_mode,\n", "       ps.source_entity_vendor_id, \n", "       vv2.vendor_name as source_entity_vendor_name, \n", "       ps.destination_entity_vendor_id, \n", "       vv1.vendor_name as destination_entity_vendor_name \n", "FROM (select s.* from lake_view_ims.ims_sto_details s\n", "     INNER JOIN lake_view_retail.console_outlet od ON s.outlet_id = od.id \n", "      WHERE s.created_at between \n", "      (case when od.business_type_id = 7 then \n", "      (cast('{start}' as timestamp) - interval '5' HOUR - interval '30' MINUTE)\n", "      else (cast('{start}' as timestamp) + interval '9' DAY - interval '5' HOUR - interval '30' MINUTE) \n", "      end)\n", "      AND (cast('{end}' as timestamp) + interval '19' HOUR - interval '30' MINUTE)\n", "      )sto\n", "INNER JOIN lake_view_retail.console_outlet o ON sto.outlet_id = o.id\n", "INNER JOIN lake_view_retail.console_outlet o1 ON sto.merchant_outlet_id = o1.id\n", "INNER JOIN lake_view_po.sto ps ON sto.sto_id = ps.id\n", "INNER JOIN (select id, vendor_name from lake_view_vms.vms_vendor) vv1 ON vv1.id = ps.destination_entity_vendor_id\n", "INNER JOIN (select id, vendor_name from lake_view_vms.vms_vendor) vv2 ON vv2.id = ps.source_entity_vendor_id\n", "INNER JOIN (\n", "select i.sto_id,\n", "       i.item_id,\n", "       p.name as item_name,\n", "       p.mrp,\n", "       p.run_id as ars_run_id,\n", "       i.expected_quantity,\n", "       i.reserved_quantity\n", "from lake_view_ims.ims_sto_item i\n", "join lake_view_po.sto_items p on p.item_id = i.item_id and p.sto_id = i.sto_id\n", "where p.created_at between (cast('{start}' as timestamp) - interval '5' HOUR - interval '30' MINUTE) AND \n", "(cast('{end}' as timestamp) + interval '19' HOUR - interval '30' MINUTE)\n", "and i.created_at between (cast('{start}' as timestamp) - interval '5' HOUR - interval '30' MINUTE) AND \n", "(cast('{end}' as timestamp) + interval '19' HOUR - interval '30' MINUTE)) items on items.sto_id = sto.sto_id\n", "LEFT JOIN (select run_id as ars_run_id, \n", "                replace (JSON_EXTRACT_SCALAR(simulation_params,'$.run'), '\"', '') as ars_run_flag, \n", "                JSON_EXTRACT_SCALAR(simulation_params, '$.manual') as manual_run, \n", "                coalesce(replace(JSON_EXTRACT_SCALAR(simulation_params, '$.mode'),'\"',''),'normal') as ars_mode\n", "from lake_view_ars.job_run\n", "where started_at \n", "between (cast('{start}' as timestamp) - interval '10' HOUR - interval '30' MINUTE)\n", "AND \n", "(cast('{end}' as timestamp) + interval '19' HOUR - interval '30' MINUTE)) ars on ars.ars_run_id = items.ars_run_id\n", "\"\"\"\n", "sto_base = pd.read_sql_query(sql=qq1, con=presto)\n", "sto_base[\"sto_id\"] = sto_base[\"sto_id\"].astype(int)\n", "sto_base[\"sto_created_at\"] = pd.to_datetime(sto_base.sto_created_at)\n", "sto_base[\"ars_run_id\"] = np.where(\n", "    sto_base[\"ars_run_id\"].str.startswith(\"202\"), sto_base[\"ars_run_id\"], str(0)\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_base.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Adding Invoice, GRN & Putaway Information"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "st = time.time()\n", "invoice_query = f\"\"\"\n", "    SELECT \n", "          cast(p.grofers_order_id as int) as sto_id,\n", "          p.invoice_id,\n", "          p.pos_timestamp + interval '5' HOUR + interval '30' MINUTE  as sto_invoice_created_at,\n", "          CASE\n", "             WHEN cast(transfer_state as integer) = 1 THEN 'Raised'\n", "             WHEN cast(transfer_state as integer) = 2 THEN 'GRN Complete'\n", "             WHEN cast(transfer_state as integer) = 3 THEN 'GRN Partial'\n", "             WHEN cast(transfer_state as integer) = 4 THEN 'GRN Force Complete'\n", "             WHEN cast(transfer_state as integer) = 5 THEN 'DISCREPANCY_NOTE_GENERATED'\n", "             ELSE 'NA'\n", "          END AS invoice_state,\n", "          pp.item_id,\n", "          sum(coalesce(pi.quantity,0)) as billed_quantity,\n", "          sum(pi.selling_price*coalesce(pi.quantity,0)) as invoice_value,\n", "          grn_started_at,\n", "          inwarded_quantity,\n", "          inward_value,\n", "          putlist_creation_time,\n", "          putaway_time,\n", "          putaway_quantity\n", "   FROM (select * from lake_view_pos.pos_invoice where insert_ds_ist between '{start}' AND '{end}'\n", "     AND invoice_type_id IN (5,14,16)\n", "     AND grofers_order_id != ''\n", "     AND grofers_order_id IS NOT NULL\n", "        ) p\n", "   JOIN (select * from lake_view_pos.pos_invoice_product_details where insert_ds_ist between '{start}' AND '{end}') pi on pi.invoice_id = p.id\n", "   JOIN lake_view_rpc.product_product pp on pp.variant_id = pi.variant_id and pp.active = 1\n", "   LEFT JOIN (SELECT \n", "               x.vendor_invoice_id as invoice_id,\n", "               x.item_id,\n", "               max(x.created_at + interval '5' hour + interval '30' minute) as grn_started_at,\n", "               sum(grn1) as inwarded_quantity,\n", "               sum(value1) as inward_value,\n", "               min(putlist_creation_time1) as putlist_creation_time,\n", "               max(putaway_time1) as putaway_time,\n", "               sum(putaway_quantity1) as putaway_quantity\n", "    FROM (select inward.grn_id, \n", "                 vendor_invoice_id, \n", "                 item_id,\n", "                 max(sd.created_at) as created_at,\n", "                 sum(coalesce(sd.delta,0)) as grn1,\n", "                 sum(coalesce(sd.delta,0)*sd.landing_price) as value1\n", "                 from\n", "    (select * from lake_view_ims.ims_inward_invoice where insert_ds_ist between '{start}' and '{end}' and source_type = 2) inward\n", "    INNER JOIN (select * from lake_view_ims.ims_inventory_stock_details where insert_ds_ist between '{start}' and '{end}') sd on inward.grn_id = sd.grn_id\n", "    INNER JOIN lake_view_rpc.product_product p on sd.variant_id = p.variant_id and p.active = 1\n", "    group by 1,2,3\n", "    ) x\n", "    LEFT JOIN (SELECT\n", "          wpl.grn_id,\n", "          wpli.item_id,\n", "          min(wpli.created_at + interval '5' HOUR + interval '30' MINUTE) AS putlist_creation_time1,\n", "          max(wpli.updated_at + interval '5' HOUR + interval '30' MINUTE) AS putaway_time1,\n", "          sum(placed_quantity) as putaway_quantity1\n", "   FROM (select * from lake_view_warehouse_location.warehouse_put_list\n", "         where insert_ds_ist between '{start}' AND '{end}'\n", "         and grn_id is not null) wpl\n", "   JOIN lake_view_warehouse_location.warehouse_put_list_item wpli on wpl.id = wpli.putlist_id\n", "   JOIN lake_view_retail.console_outlet co ON co.id = wpli.outlet_id AND co.business_type_id = 7\n", "   group by 1,2) put on put.grn_id = x.grn_id and x.item_id = put.item_id\n", "    group by 1,2) grn on grn.invoice_id = p.invoice_id and grn.item_id = pp.item_id \n", "   group by 1,2,3,4,5,8,9,10,11,12,13\n", "\"\"\"\n", "temp = pd.read_sql_query(sql=invoice_query, con=presto)\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_base = sto_base.merge(temp, how=\"left\", on=[\"sto_id\", \"item_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_base[\"etl_timestamp_ist\"] = datetime.now(tz)"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## Adding Picking Information (both bulk & item picking)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "st = time.time()\n", "picking_time_query = f\"\"\"\n", "with pl as (\n", "select id, wave_id, 'bulk' as picking_type, picking_started_at \n", "from lake_view_warehouse_location.warehouse_pick_list \n", "where cast(insert_ds_ist as timestamp) between (cast('{start}' as timestamp))\n", "AND (cast('{end}' as timestamp))\n", "),\n", "\n", "w as (select id, entity_id\n", "from lake_view_warehouse_location.warehouse_wave where wave_type = 4\n", "and entity_id is not null and id in (select wave_id from pl)\n", "),\n", "\n", "pll as (\n", "select pick_list_id , max(pos_timestamp) as pos_timestamp\n", "from lake_view_warehouse_location.pick_list_log\n", "where pick_list_id in (select id from pl) and picklist_state = 4\n", "and cast(insert_ds_ist as timestamp) between (cast('{start}' as timestamp))\n", "AND (cast('{end}' as timestamp))\n", "group by 1\n", "),\n", "\n", "pli as (\n", "select item_id, pick_list_id, picked_quantity\n", "from lake_view_warehouse_location.warehouse_pick_list_item\n", "where pick_list_id in (select id from pl) and \n", "cast(insert_ds_ist as timestamp) between (cast('{start}' as timestamp))\n", "AND (cast('{end}' as timestamp))\n", ")\n", "\n", "select distinct \n", "    cast(w.entity_id as int) as sto_id,\n", "     cast(pli.item_id as int) as item_id,\n", "    pl.picking_type,\n", "    min(pl.picking_started_at + INTERVAL '5' HOUR + INTERVAL '30' MINUTE) AS picking_started_at_ist,\n", "    max(pll.pos_timestamp + INTERVAL '5' HOUR + INTERVAL '30' MINUTE) AS picking_completed_at_ist,\n", "    sum(pli.picked_quantity) as picked_quantity\n", "from pl \n", "join w on w.id = pl.wave_id \n", "join pll on pl.id = pll.pick_list_id\n", "join pli on pl.id = pli.pick_list_id\n", "group by 1,2,3\n", "\"\"\"\n", "temp = pd.read_sql_query(sql=picking_time_query, con=presto)\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "st = time.time()\n", "picking_time_query = f\"\"\" Select\n", "    cast(pi.entity_id as int) as sto_id, \n", "    cast(p.item_id as int) as item_id,\n", "    'item_picking' as picking_type,\n", "    min(pl.created_at + INTERVAL '330' MINUTE) AS picking_started_at_ist,\n", "    max(pl1.created_at + INTERVAL '330' MINUTE) AS picking_completed_at_ist,\n", "    sum(pi.picked_quantity) AS picked_quantity    \n", "FROM lake_view_warehouse_location.warehouse_item_pick_list p\n", "JOIN lake_view_warehouse_location.warehouse_item_pick_list_line_item pi on pi.item_pick_list_id = p.id\n", "LEFT JOIN lake_view_warehouse_location.warehouse_item_pick_list_log pl on pl.item_pick_list_id = p.id and pl.state = 3\n", "LEFT JOIN lake_view_warehouse_location.warehouse_item_pick_list_log pl1 on pl1.item_pick_list_id = p.id and pl1.state = 4\n", "WHERE pi.insert_ds_ist between '{start}' AND '{end}'\n", "group by 1,2,3\n", "\"\"\"\n", "temp1 = pd.read_sql_query(sql=picking_time_query, con=presto)\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# temp1[(temp1[\"sto_id\"] == 5073459) & (temp1[\"item_id\"] == \"10000780\")]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp_pick = pd.concat([temp, temp1], axis=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp_pick[\"PickingTimeMins\"] = (\n", "    (\n", "        pd.to_datetime(temp_pick[\"picking_completed_at_ist\"])\n", "        - pd.to_datetime(temp_pick[\"picking_started_at_ist\"])\n", "    )\n", "    / np.timedelta64(1, \"D\")\n", "    * 24\n", "    * 60\n", ")\n", "sto_base = sto_base.merge(\n", "    temp_pick,\n", "    left_on=[\"sto_id\", \"item_id\"],\n", "    right_on=[\"sto_id\", \"item_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Adding dispatch Infomration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "st = time.time()\n", "dispatch_query = f\"\"\"\n", "select \n", "    di.sto_id,\n", "    di.id as invoice_id,\n", "    max(case when dib.state = 2 then dib.created_at + interval '5' HOUR + interval '30' minute end) as sto_dispatched_at\n", "from lake_view_warehouse_location.dispatch_indent di\n", "join lake_view_warehouse_location.dispatch_indent_box dib on di.id = dib.dispatch_indent_id\n", "where dib.created_at between\n", "   (cast('{start}' as timestamp) - interval '5' HOUR - interval '30' MINUTE) AND \n", "   (cast('{end}' as timestamp) + interval '19' HOUR - interval '30' MINUTE)\n", "group by 1,2\n", "\"\"\"\n", "temp = pd.read_sql_query(sql=dispatch_query, con=presto)\n", "print(\"Run time: \", time.time() - st)\n", "sto_base = sto_base.merge(\n", "    temp,\n", "    left_on=[\"sto_id\", \"invoice_id\"],\n", "    right_on=[\"sto_id\", \"invoice_id\"],\n", "    how=\"left\",\n", ")\n", "sto_base.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_base.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Adding Discrepancy & B2B Return Information"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b2b_return_query = f\"\"\"\n", "select tab1.original_invoice_id as invoice_id,\n", "       tab1.item_id,\n", "       sum(tab1.b2b_return_qty) as b2b_return_qty,\n", "       sum(tab1.b2b_return_value) as b2b_return_value,\n", "    sum(tab2.b2b_customer_return) b2breturn_good,\n", "    sum(tab2.b2b_customer_return_value) b2breturn_good_value,\n", "    sum(tab2.b2b_customer_return_expired) b2breturn_expired,\n", "    sum(tab2.b2b_customer_return_expired_value) b2breturn_expired_value,\n", "    sum(tab2.b2b_customer_return_damaged) b2breturn_damaged,\n", "    sum(tab2.b2b_customer_return_damaged_value) b2breturn_damaged_value,\n", "    sum(tab2.b2b_customer_return_near_expiry) b2breturn_near_expiry,\n", "    sum(tab2.b2b_customer_return_near_expiry_value) b2breturn_near_expiry_value\n", "from\n", "(\n", "    select pi.invoice_id ret_invoice_id, pi.original_invoice_id,pp.item_id,pipd.variant_id,sum(pipd.quantity) as  b2b_return_qty, sum(pipd.quantity * pipd.selling_price) b2b_return_value from \n", "    lake_view_pos.pos_invoice  pi \n", "    inner join lake_view_pos.pos_invoice pi2  on pi2.invoice_id=pi.original_invoice_id\n", "    inner join lake_view_pos.pos_invoice_product_details pipd on pi.id=pipd.invoice_id\n", "    inner join lake_view_rpc.product_product pp ON pp.variant_id = pipd.variant_id\n", "    where pi.invoice_type_id in (8,15)\n", "    and   pi2.invoice_type_id in (5,14,16)\n", "    and pi.insert_ds_ist between '{start}' AND '{end}'\n", "    and pi2.insert_ds_ist between cast(date('{start}') - interval '3' DAY as varchar) AND '{end}'\n", "    and pipd.insert_ds_ist between '{start}' AND '{end}'\n", "    group by 1,2,3,4\n", ")tab1\n", "left join \n", "(\n", "    select invoice_id as ret_invoice_id, item_id, variant_id,\n", "    sum(case  when update_type_id in (23,35) then ret_qty end) as b2b_customer_return,\n", "    sum(case  when update_type_id in (24,36) then ret_qty end) as b2b_customer_return_expired,\n", "    sum(case  when update_type_id in (25,37) then ret_qty end) as b2b_customer_return_damaged,\n", "    sum(case  when update_type_id in (66,68) then ret_qty end) as b2b_customer_return_near_expiry,\n", "    sum(case  when update_type_id in (23,35) then ret_qty * transaction_lp end) as b2b_customer_return_value,\n", "    sum(case  when update_type_id in (24,36) then ret_qty * transaction_lp end) as b2b_customer_return_expired_value,\n", "    sum(case  when update_type_id in (25,37) then ret_qty * transaction_lp end) as b2b_customer_return_damaged_value,\n", "    sum(case  when update_type_id in (66,68) then ret_qty * transaction_lp end) as b2b_customer_return_near_expiry_value,\n", "    sum(case  when update_type_id=119 then ret_qty end) as lost_in_transit_positive,\n", "    sum(case  when update_type_id=120 then ret_qty end) as lost_in_transit_negative,\n", "    sum(ret_qty) as tot_ret_qty \n", "    from\n", "        (\n", "        select iil.invoice_id,iil.merchant_invoice_id, iil.\"delta\" as ret_qty,iut.id as update_type_id,iut.name as update_type,pp.item_id , iil.variant_id, iil.transaction_lp\n", "        from lake_view_ims.ims_inventory_log iil \n", "        left join lake_view_ims.ims_inventory_update_type iut on iil.inventory_update_type_id=iut.id\n", "        left join lake_view_rpc.product_product pp ON iil.variant_id = pp.variant_id\n", "        where iil.insert_ds_ist between cast(date('{start}') - interval '3' DAY as varchar) AND '{end}'\n", "        ) temp\n", "    group by 1,2,3\n", ")tab2\n", "on tab1.ret_invoice_id=tab2.ret_invoice_id and tab1.item_id=tab2.item_id and tab1.variant_id = tab2.variant_id\n", "group by 1,2\n", "order by 1,2\n", "\"\"\"\n", "temp = pd.read_sql_query(sql=b2b_return_query, con=presto)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_base = sto_base.merge(\n", "    temp,\n", "    left_on=[\"invoice_id\", \"item_id\"],\n", "    right_on=[\"invoice_id\", \"item_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["discrepancy_query = f\"\"\"\n", "SELECT    vendor_invoice_id as invoice_id,\n", "          item_id,\n", "          max(created_at) as discrepancy_created_at,\n", "          sum(disc_qty) as disc_qty,\n", "          sum(landing_price * disc_qty) disc_value,\n", "          sum(case when disc_remark in ('UPC is invalid','Wrong item') then disc_qty else 0 end) as wrong_item_qty,\n", "          sum(case when disc_remark in ('MRP Mismatch') then disc_qty else 0 end) as variant_mismatch_qty,\n", "          sum(case when disc_remark in ('UPC not scannable','UPC not printed on SKU') then disc_qty else 0 end) as UPC_not_scannable_qty,\n", "          sum(case when disc_remark in ('Rcpt Qty < Invoice Qty','Item not in ESTO','STO Missing (Transport)','STO Missing (Source)',\n", "          'STO Short Quantity (Transport)','STO Short Quantity (Source)') then disc_qty else 0 end) as short_qty,\n", "          sum(case when disc_remark in ('Stale items- Mould/Fungus/Rotten','F&V Foul smell') then disc_qty else 0 end) as quality_issue_qty,\n", "          sum(case when disc_remark in ('Shelf Life guidelines breached') then disc_qty else 0 end) as expiry_nte_qty,\n", "          sum(case when disc_remark in ('Freebies missing') then disc_qty else 0 end) as freebies_missing_qty,\n", "          sum(case when disc_remark in ('Rcpt Qty > Invoice Qty') then disc_qty else 0 end) as excess_qty,\n", "          sum(case when disc_remark in ('Damaged','Packaging issue','Labelling issue') then disc_qty else 0 end) as damage_qty,\n", "          sum(case when disc_remark in ('UPC is invalid','Wrong item') then landing_price * disc_qty else 0 end) as wrong_item_value,\n", "          sum(case when disc_remark in ('MRP Mismatch') then landing_price * disc_qty else 0 end) as variant_mismatch_value,\n", "          sum(case when disc_remark in ('UPC not scannable','UPC not printed on SKU') then landing_price * disc_qty else 0 end) as UPC_not_scannable_value,\n", "          sum(case when disc_remark in ('Rcpt Qty < Invoice Qty','Item not in ESTO','STO Missing (Transport)','STO Missing (Source)',\n", "          'STO Short Quantity (Transport)','STO Short Quantity (Source)') then landing_price * disc_qty else 0 end) as short_value,\n", "          sum(case when disc_remark in ('Stale items- Mould/Fungus/Rotten','F&V Foul smell') then landing_price * disc_qty else 0 end) as quality_issue_value,\n", "          sum(case when disc_remark in ('Shelf Life guidelines breached') then landing_price * disc_qty else 0 end) as expiry_nte_value,\n", "          sum(case when disc_remark in ('Freebies missing') then landing_price * disc_qty else 0 end) as freebies_missing_value,\n", "          sum(case when disc_remark in ('Rcpt Qty > Invoice Qty') then landing_price * disc_qty else 0 end) as excess_value,\n", "          sum(case when disc_remark in ('Damaged','Packaging issue','Labelling issue') then landing_price * disc_qty else 0 end) as damage_value\n", "from(\n", "select dn.id,(dn.created_at + interval '5.5 hour') created_at,dn.vendor_name,dn.vendor_invoice_id,dn.net_amount,dn.outlet_id,\n", "dnpd.upc_id,dnpd.name,dnpd.quantity as disc_qty,dnpd.cost_price,dnpd.landing_price,dnr.name as disc_remark,\n", "pp.item_id\n", "from (select d.* from\n", "lake_pos.discrepancy_note d\n", "inner join (select vendor_invoice_id,max(created_at) as created_at \n", "from lake_pos.discrepancy_note \n", "where date(created_at) between date('{start}') and date('{end}')\n", "group by 1) d1 on d.vendor_invoice_id = d1.vendor_invoice_id and d.created_at = d1.created_at\n", ") dn\n", "left join lake_pos.discrepancy_note_product_detail dnpd on dn.id=dnpd.dn_id_id\n", "left join lake_pos.discrepancy_reason dnr on dnr.id = dnpd.reason_code\n", "left join (select distinct item_id,upc from  lake_rpc.product_product ) pp on  dnpd.upc_id=pp.upc\n", ") temp\n", "group by 1,2\"\"\"\n", "temp = pd.read_sql_query(sql=discrepancy_query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_base = sto_base.merge(\n", "    temp,\n", "    left_on=[\"invoice_id\", \"item_id\"],\n", "    right_on=[\"invoice_id\", \"item_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# fleet details"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "SELECT invoice_id,\n", "       min(fc.created_at) + interval '5' HOUR + interval '30' MINUTE as trip_created_at,\n", "       min(m.created_at) + interval '5' HOUR + interval '30' MINUTE as trip_started_at,\n", "       min(l.created_at) + interval '5' HOUR + interval '30' MINUTE as consignment_received_at\n", "FROM lake_view_vendor_console.fleet_consignment_invoice_mapping icn \n", "JOIN lake_view_vendor_console.fleet_consignment rec ON rec.id = icn.consignment_id AND rec.active = True\n", "JOIN lake_view_vendor_console.fleet_trip_details fc ON fc.id = rec.trip_id AND fc.active =  True\n", "LEFT JOIN lake_view_vendor_console.fleet_consignment_state_transition_log l on l.consignment_id = icn.consignment_id and l.state = 'DELIVERED'\n", "LEFT JOIN lake_view_vendor_console.fleet_trip_state_transition_log m on m.trip_id = rec.trip_id and m.state = 'IN_TRANSIT'\n", "where icn.active =  True and date(fc.created_at) between date('{start}') and date('{end}')\n", "group by 1\"\"\"\n", "data = pd.read_sql_query(query, presto)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.shape, data.invoice_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_base = sto_base.merge(data, on=[\"invoice_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_base = sto_base[\n", "    [\n", "        \"bulk_sto_id\",\n", "        \"sto_id\",\n", "        \"sender_outlet_id\",\n", "        \"sender_outlet_name\",\n", "        \"receiving_outlet_id\",\n", "        \"receiver_outlet_name\",\n", "        \"source_entity_vendor_id\",\n", "        \"source_entity_vendor_name\",\n", "        \"destination_entity_vendor_id\",\n", "        \"destination_entity_vendor_name\",\n", "        \"delivery_date\",\n", "        \"sto_created_at\",\n", "        \"sto_type\",\n", "        \"sto_state\",\n", "        \"Manual_sto\",\n", "        \"dispatch_time\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"mrp\",\n", "        \"ars_run_id\",\n", "        \"expected_quantity\",\n", "        \"reserved_quantity\",\n", "        \"ars_run_flag\",\n", "        \"manual_run\",\n", "        \"ars_mode\",\n", "        \"invoice_id\",\n", "        \"sto_invoice_created_at\",\n", "        \"invoice_state\",\n", "        \"billed_quantity\",\n", "        \"invoice_value\",\n", "        \"inwarded_quantity\",\n", "        \"inward_value\",\n", "        \"grn_started_at\",\n", "        \"etl_timestamp_ist\",\n", "        \"picking_type\",\n", "        \"picking_started_at_ist\",\n", "        \"picking_completed_at_ist\",\n", "        \"picked_quantity\",\n", "        \"PickingTimeMins\",\n", "        \"sto_dispatched_at\",\n", "        \"b2b_return_qty\",\n", "        \"b2breturn_good\",\n", "        \"b2breturn_expired\",\n", "        \"b2breturn_damaged\",\n", "        \"b2breturn_near_expiry\",\n", "        \"discrepancy_created_at\",\n", "        \"disc_qty\",\n", "        \"wrong_item_qty\",\n", "        \"variant_mismatch_qty\",\n", "        \"upc_not_scannable_qty\",\n", "        \"short_qty\",\n", "        \"quality_issue_qty\",\n", "        \"expiry_nte_qty\",\n", "        \"freebies_missing_qty\",\n", "        \"excess_qty\",\n", "        \"damage_qty\",\n", "        \"trip_created_at\",\n", "        \"trip_started_at\",\n", "        \"consignment_received_at\",\n", "        \"putlist_creation_time\",\n", "        \"putaway_time\",\n", "        \"putaway_quantity\",\n", "        \"b2b_return_value\",\n", "        \"b2breturn_good_value\",\n", "        \"b2breturn_expired_value\",\n", "        \"b2breturn_damaged_value\",\n", "        \"b2breturn_near_expiry_value\",\n", "        \"disc_value\",\n", "        \"wrong_item_value\",\n", "        \"variant_mismatch_value\",\n", "        \"upc_not_scannable_value\",\n", "        \"short_value\",\n", "        \"quality_issue_value\",\n", "        \"expiry_nte_value\",\n", "        \"freebies_missing_value\",\n", "        \"excess_value\",\n", "        \"damage_value\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_base[\"grn_started_at\"] = np.where(\n", "    sto_base[\"putlist_creation_time\"].notnull(),\n", "    sto_base[\"putlist_creation_time\"],\n", "    sto_base[\"grn_started_at\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_base[\"sto_created_at\"] = pd.to_datetime(sto_base[\"sto_created_at\"])\n", "sto_base[\"dispatch_time\"] = pd.to_datetime(sto_base[\"dispatch_time\"])\n", "sto_base[\"sto_invoice_created_at\"] = pd.to_datetime(sto_base[\"sto_invoice_created_at\"])\n", "sto_base[\"grn_started_at\"] = pd.to_datetime(sto_base[\"grn_started_at\"])\n", "sto_base[\"sto_dispatched_at\"] = pd.to_datetime(sto_base[\"sto_dispatched_at\"])\n", "sto_base[\"putlist_creation_time\"] = pd.to_datetime(sto_base[\"putlist_creation_time\"])\n", "sto_base[\"putaway_time\"] = pd.to_datetime(sto_base[\"putaway_time\"])\n", "sto_base[\"discrepancy_created_at\"] = pd.to_datetime(sto_base[\"discrepancy_created_at\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_base[\"trip_created_at\"] = pd.to_datetime(sto_base[\"trip_created_at\"])\n", "sto_base[\"trip_started_at\"] = pd.to_datetime(sto_base[\"trip_started_at\"])\n", "sto_base[\"consignment_received_at\"] = pd.to_datetime(\n", "    sto_base[\"consignment_received_at\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_base.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_base[\"b2breturn_good\"] = sto_base[\"b2breturn_good\"].fillna(0)\n", "sto_base[\"b2breturn_expired\"] = sto_base[\"b2breturn_expired\"].fillna(0)\n", "sto_base[\"b2breturn_damaged\"] = sto_base[\"b2breturn_damaged\"].fillna(0)\n", "sto_base[\"b2breturn_near_expiry\"] = sto_base[\"b2breturn_near_expiry\"].fillna(0)\n", "sto_base[\"b2b_return_value\"] = sto_base[\"b2b_return_value\"].fillna(0)\n", "sto_base[\"b2breturn_good_value\"] = sto_base[\"b2breturn_good_value\"].fillna(0)\n", "sto_base[\"b2breturn_expired_value\"] = sto_base[\"b2breturn_expired_value\"].fillna(0)\n", "sto_base[\"b2breturn_damaged_value\"] = sto_base[\"b2breturn_damaged_value\"].fillna(0)\n", "sto_base[\"b2breturn_near_expiry_value\"] = sto_base[\n", "    \"b2breturn_near_expiry_value\"\n", "].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_base[\"b2breturn_good\"] = sto_base[\"b2breturn_good\"].astype(\"Int64\")\n", "sto_base[\"b2breturn_expired\"] = sto_base[\"b2breturn_expired\"].astype(\"Int64\")\n", "sto_base[\"b2breturn_damaged\"] = sto_base[\"b2breturn_damaged\"].astype(\"Int64\")\n", "sto_base[\"b2breturn_near_expiry\"] = sto_base[\"b2breturn_near_expiry\"].astype(\"Int64\")\n", "sto_base[\"picked_quantity\"] = sto_base[\"picked_quantity\"].fillna(0).astype(\"Int64\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_base.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["# for i,j in zip(sto_base.columns, sto_base.dtypes):\n", "#     if j=='object':\n", "#         j = 'varchar'\n", "#     if j=='int64':\n", "#         j='int'\n", "#     if j=='float64':\n", "#         j='float'\n", "#     print(f\"\"\"{{\n", "#       \\\"name\\\":\\\"{i}\\\",\n", "#       \\\"type\\\":\\\"{j}\\\",\n", "#       \\\"description\\\":\\\"{i}\\\"\n", "#     }},\"\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"bulk_sto_id\", \"type\": \"int\", \"description\": \"bulk_sto_id\"},\n", "    {\"name\": \"sto_id\", \"type\": \"int\", \"description\": \"sto_id\"},\n", "    {\"name\": \"sender_outlet_id\", \"type\": \"int\", \"description\": \"sender_outlet_id\"},\n", "    {\n", "        \"name\": \"sender_outlet_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"sender_outlet_name\",\n", "    },\n", "    {\n", "        \"name\": \"receiving_outlet_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"receiving_outlet_id\",\n", "    },\n", "    {\n", "        \"name\": \"receiver_outlet_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"receiver_outlet_name\",\n", "    },\n", "    {\n", "        \"name\": \"source_entity_vendor_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"source_entity_vendor_id\",\n", "    },\n", "    {\n", "        \"name\": \"source_entity_vendor_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"source_entity_vendor_name\",\n", "    },\n", "    {\n", "        \"name\": \"destination_entity_vendor_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"destination_entity_vendor_id\",\n", "    },\n", "    {\n", "        \"name\": \"destination_entity_vendor_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"destination_entity_vendor_name\",\n", "    },\n", "    {\"name\": \"delivery_date\", \"type\": \"varchar\", \"description\": \"delivery_date\"},\n", "    {\"name\": \"sto_created_at\", \"type\": \"datetime\", \"description\": \"sto_created_at\"},\n", "    {\"name\": \"sto_type\", \"type\": \"varchar\", \"description\": \"sto_type\"},\n", "    {\"name\": \"sto_state\", \"type\": \"varchar\", \"description\": \"sto_state\"},\n", "    {\"name\": \"Manual_sto\", \"type\": \"int\", \"description\": \"Manual_sto\"},\n", "    {\"name\": \"dispatch_time\", \"type\": \"datetime\", \"description\": \"dispatch_time\"},\n", "    {\"name\": \"item_id\", \"type\": \"int\", \"description\": \"item_id\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"item_name\"},\n", "    {\"name\": \"mrp\", \"type\": \"float\", \"description\": \"mrp\"},\n", "    {\"name\": \"ars_run_id\", \"type\": \"varchar\", \"description\": \"ars_run_id\"},\n", "    {\"name\": \"expected_quantity\", \"type\": \"int\", \"description\": \"expected_quantity\"},\n", "    {\"name\": \"reserved_quantity\", \"type\": \"int\", \"description\": \"reserved_quantity\"},\n", "    {\"name\": \"ars_run_flag\", \"type\": \"varchar\", \"description\": \"ars_run_flag\"},\n", "    {\"name\": \"manual_run\", \"type\": \"varchar\", \"description\": \"manual_run\"},\n", "    {\"name\": \"ars_mode\", \"type\": \"varchar\", \"description\": \"ars_mode\"},\n", "    {\"name\": \"invoice_id\", \"type\": \"varchar\", \"description\": \"invoice_id\"},\n", "    {\n", "        \"name\": \"sto_invoice_created_at\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"sto_invoice_created_at\",\n", "    },\n", "    {\"name\": \"invoice_state\", \"type\": \"varchar\", \"description\": \"invoice_state\"},\n", "    {\"name\": \"billed_quantity\", \"type\": \"float\", \"description\": \"billed_quantity\"},\n", "    {\"name\": \"invoice_value\", \"type\": \"float\", \"description\": \"invoice_value\"},\n", "    {\"name\": \"inwarded_quantity\", \"type\": \"float\", \"description\": \"inwarded_quantity\"},\n", "    {\"name\": \"inward_value\", \"type\": \"float\", \"description\": \"inward_value\"},\n", "    {\"name\": \"grn_started_at\", \"type\": \"datetime\", \"description\": \"grn_started_at\"},\n", "    {\n", "        \"name\": \"etl_timestamp_ist\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"etl_timestamp_ist\",\n", "    },\n", "    {\"name\": \"picking_type\", \"type\": \"varchar\", \"description\": \"picking_type\"},\n", "    {\n", "        \"name\": \"picking_started_at_ist\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"picking_started_at_ist\",\n", "    },\n", "    {\n", "        \"name\": \"picking_completed_at_ist\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"picking_completed_at_ist\",\n", "    },\n", "    {\"name\": \"picked_quantity\", \"type\": \"int\", \"description\": \"picked_quantity\"},\n", "    {\"name\": \"PickingTimeMins\", \"type\": \"float\", \"description\": \"PickingTimeMins\"},\n", "    {\n", "        \"name\": \"sto_dispatched_at\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"sto_dispatched_at\",\n", "    },\n", "    {\"name\": \"b2b_return_qty\", \"type\": \"float\", \"description\": \"b2b_return_qty\"},\n", "    {\"name\": \"b2breturn_good\", \"type\": \"int\", \"description\": \"b2breturn_good\"},\n", "    {\"name\": \"b2breturn_expired\", \"type\": \"int\", \"description\": \"b2breturn_expired\"},\n", "    {\"name\": \"b2breturn_damaged\", \"type\": \"int\", \"description\": \"b2breturn_damaged\"},\n", "    {\n", "        \"name\": \"b2breturn_near_expiry\",\n", "        \"type\": \"int\",\n", "        \"description\": \"b2breturn_near_expiry\",\n", "    },\n", "    {\n", "        \"name\": \"discrepancy_created_at\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"discrepancy_created_at\",\n", "    },\n", "    {\"name\": \"disc_qty\", \"type\": \"float\", \"description\": \"disc_qty\"},\n", "    {\"name\": \"wrong_item_qty\", \"type\": \"float\", \"description\": \"wrong_item_qty\"},\n", "    {\n", "        \"name\": \"variant_mismatch_qty\",\n", "        \"type\": \"float\",\n", "        \"description\": \"variant_mismatch_qty\",\n", "    },\n", "    {\n", "        \"name\": \"upc_not_scannable_qty\",\n", "        \"type\": \"float\",\n", "        \"description\": \"upc_not_scannable_qty\",\n", "    },\n", "    {\"name\": \"short_qty\", \"type\": \"float\", \"description\": \"short_qty\"},\n", "    {\"name\": \"quality_issue_qty\", \"type\": \"float\", \"description\": \"quality_issue_qty\"},\n", "    {\"name\": \"expiry_nte_qty\", \"type\": \"float\", \"description\": \"expiry_nte_qty\"},\n", "    {\n", "        \"name\": \"freebies_missing_qty\",\n", "        \"type\": \"float\",\n", "        \"description\": \"freebies_missing_qty\",\n", "    },\n", "    {\"name\": \"excess_qty\", \"type\": \"float\", \"description\": \"excess_qty\"},\n", "    {\"name\": \"damage_qty\", \"type\": \"float\", \"description\": \"damage_qty\"},\n", "    {\"name\": \"trip_created_at\", \"type\": \"datetime\", \"description\": \"trip_created_at\"},\n", "    {\"name\": \"trip_started_at\", \"type\": \"datetime\", \"description\": \"trip_started_at\"},\n", "    {\n", "        \"name\": \"consignment_received_at\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"consignment_received_at\",\n", "    },\n", "    {\n", "        \"name\": \"putlist_creation_time\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"putlist_creation_time\",\n", "    },\n", "    {\"name\": \"putaway_time\", \"type\": \"datetime\", \"description\": \"putaway_time\"},\n", "    {\"name\": \"putaway_quantity\", \"type\": \"float\", \"description\": \"putaway_quantity\"},\n", "    {\"name\": \"b2b_return_value\", \"type\": \"float\", \"description\": \"b2b_return_value\"},\n", "    {\n", "        \"name\": \"b2breturn_good_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"b2breturn_good_value\",\n", "    },\n", "    {\n", "        \"name\": \"b2breturn_expired_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"b2breturn_expired_value\",\n", "    },\n", "    {\n", "        \"name\": \"b2breturn_damaged_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"b2breturn_damaged_value\",\n", "    },\n", "    {\n", "        \"name\": \"b2breturn_near_expiry_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"b2breturn_near_expiry_value\",\n", "    },\n", "    {\"name\": \"disc_value\", \"type\": \"float\", \"description\": \"disc_value\"},\n", "    {\"name\": \"wrong_item_value\", \"type\": \"float\", \"description\": \"wrong_item_value\"},\n", "    {\n", "        \"name\": \"variant_mismatch_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"variant_mismatch_value\",\n", "    },\n", "    {\n", "        \"name\": \"upc_not_scannable_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"upc_not_scannable_value\",\n", "    },\n", "    {\"name\": \"short_value\", \"type\": \"float\", \"description\": \"short_value\"},\n", "    {\n", "        \"name\": \"quality_issue_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"quality_issue_value\",\n", "    },\n", "    {\"name\": \"expiry_nte_value\", \"type\": \"float\", \"description\": \"expiry_nte_value\"},\n", "    {\n", "        \"name\": \"freebies_missing_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"freebies_missing_value\",\n", "    },\n", "    {\"name\": \"excess_value\", \"type\": \"float\", \"description\": \"excess_value\"},\n", "    {\"name\": \"damage_value\", \"type\": \"float\", \"description\": \"damage_value\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp = sto_base[\"sto_id\"].astype(str) + sto_base[\"item_id\"].astype(str)\n", "print(len(temp.drop_duplicates()), len(temp), sto_base.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\n", "    sto_base.sto_id.max(), sto_base.sto_created_at.max(), sto_base.sto_created_at.min()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"esto_details\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"sto_id\", \"item_id\"],\n", "    \"sortkey\": [\"sto_id\", \"item_id\"],\n", "    \"incremental_key\": \"etl_timestamp_ist\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"captures the life cyle of all sto\",  # Description of the table being sent to redshift\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(sto_base, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"End of Code\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}