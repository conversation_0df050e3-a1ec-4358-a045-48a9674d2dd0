alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: bistro_karma_v0
dag_type: etl
escalation_priority: low
execution_timeout: 1000
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: fps
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03S5ACSW3C
path: fps/bistro/etl/bistro_karma_v0
paused: false
pool: fps_pool
project_name: bistro
schedule:
  end_date: '2025-09-15T00:00:00'
  interval: 5 */8 * * *
  start_date: '2025-06-18T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
