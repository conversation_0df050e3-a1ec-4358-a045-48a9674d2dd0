{"cells": [{"cell_type": "code", "execution_count": null, "id": "cb35df0e-b24e-4f18-ad59-6c1de5bd883b", "metadata": {}, "outputs": [], "source": ["# !pip install openpyxl"]}, {"cell_type": "code", "execution_count": null, "id": "d199d8e0-f98e-4356-8604-14e43421b564", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "id": "2cc0e901-6db2-4c82-9bb0-d35d36238f46", "metadata": {}, "outputs": [], "source": ["CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "MIN_ELIGIBLE_ORDER_COUNT = 3\n", "INTERVAL_TIME = 90\n", "NTILE = 5\n", "ERROR_CNT = 0\n", "\n", "IRON_MARKING_RESOLUTION_PERC = 0.85\n", "IRON_MARKING_COMPLAINT_PERC = 0.85\n", "IRON_MARKING_MIN_RESO_CARTS = 1\n", "IRON_MIN_COMPLAINT_CARTS = 3\n", "\n", "\n", "# Table constants\n", "INTERIM_TABLE_SCHEMA = \"interim\"\n", "INTERIM_TABLE_NAME = \"bistro_karma_prod\"\n", "\n", "SEGMENT_TABLE_SCHEMA = \"segment_computed_traits\"\n", "SEGMENT_TABLE_NAME = \"bistro_karma_prod\"\n", "\n", "FORCED_UPDATE_SCHEMA = \"crm\"\n", "FORCED_UPDATE_TABLE = \"crm_force_update_karma\""]}, {"cell_type": "code", "execution_count": null, "id": "dd95d41a-db44-4289-afbc-85dd9eecd091", "metadata": {}, "outputs": [], "source": ["def fetch_data(query):\n", "    return pd.read_sql(query, CON_TRINO).copy()"]}, {"cell_type": "code", "execution_count": null, "id": "a2469a7b-8b5f-4373-8e77-fc88b39d8b0e", "metadata": {}, "outputs": [], "source": ["try:\n", "    LAST_RUN_VALUE = fetch_data(\n", "        f\"SELECT SUM(1) AS prev_count FROM {SEGMENT_TABLE_SCHEMA}.{SEGMENT_TABLE_NAME}\"\n", "    )[\"prev_count\"][0]\n", "except:\n", "    LAST_RUN_VALUE = 0"]}, {"cell_type": "markdown", "id": "fff8cdce-4eeb-4126-8e1d-3ef8ed533cac", "metadata": {}, "source": ["## Fetching Preliminary Data"]}, {"cell_type": "code", "execution_count": null, "id": "89347536-edb7-4402-b7b5-0d17334a4849", "metadata": {}, "outputs": [], "source": ["QUERY = f\"\"\"\n", "\n", "WITH orders_info AS\n", "(\n", "SELECT\n", "    order_id,\n", "    cart_id,\n", "    dim_customer_key AS customer_id,\n", "    total_selling_price AS order_gmv,\n", "    org_channel_id,\n", "    order_deliver_ts_ist\n", "FROM\n", "    bistro_etls.fact_sales_order_details_bistro\n", "WHERE\n", "    order_create_dt_ist >= CURRENT_DATE - INTERVAL'{INTERVAL_TIME}'DAY\n", "AND order_current_status = 'DELIVERED'\n", "AND is_internal_order = FALSE\n", "),\n", "\n", "agg_order_metrics AS\n", "(\n", "SELECT \n", "    customer_id,\n", "    COUNT(DISTINCT cart_id) AS cart_count,\n", "    COUNT(DISTINCT order_id) AS order_count,\n", "    SUM(order_gmv) AS gmv\n", "FROM\n", "    orders_info\n", "GROUP BY 1\n", "),\n", "\n", "-- Complaints (All complaints post-delivery are consiered) ---\n", "\n", "order_sess AS (\n", "\n", "    SELECT\n", "        session_id,\n", "        created_at,\n", "        TRY_CAST(\n", "            (\n", "            CASE\n", "                WHEN meta_value = '' THEN NULL\n", "                ELSE meta_value \n", "            END\n", "            ) AS BIGINT) AS session_order_id\n", "    FROM\n", "        zomato.blinkitchatdb.support_session_meta \n", "    WHERE\n", "        meta_key='order_id' \n", "    AND created_at >= NOW() - INTERVAL'{INTERVAL_TIME}'DAY\n", "),\n", "\n", "complaint_metrics AS\n", "(\n", "SELECT\n", "    customer_id,\n", "    CASE\n", "        WHEN created_at + INTERVAL'330'MINUTE >= order_deliver_ts_ist THEN TRUE\n", "        WHEN (order_id IS NULL OR created_at + INTERVAL'330'MINUTE < order_deliver_ts_ist) THEN FALSE\n", "    END AS is_complaint_eligible_order,\n", "    COALESCE(session_order_id,0) AS session_order_id\n", "FROM\n", "    order_sess os\n", "LEFT JOIN\n", "    orders_info oi ON os.session_order_id = oi.order_id\n", "GROUP BY 1,2,3\n", "),\n", "\n", "agg_complaint_metrics AS\n", "(\n", "SELECT\n", "    customer_id,\n", "    COUNT(DISTINCT CASE WHEN is_complaint_eligible_order = TRUE THEN session_order_id END) AS complaint_order_count\n", "FROM\n", "    complaint_metrics\n", "GROUP BY 1\n", "),\n", "\n", "-- Resolutions (All Refund + Replace Amounts) ---\n", "\n", "agg_resolution_metrics AS\n", "(\n", "SELECT\n", "    cx_id AS customer_id,\n", "    COUNT(DISTINCT requested_order_id) AS resolution_order_count,\n", "    SUM(COALESCE(refunded_amt,0) + COALESCE(replaced_amt,0)) AS resolution_amt\n", "FROM\n", "    zomato.dynamodb.blinkit_crm_complaints\n", "WHERE\n", "    dt >= REPLACE(CAST(CURRENT_DATE - INTERVAL'{INTERVAL_TIME}'DAY AS VARCHAR),'-','')\n", "    AND complaint_type NOT IN \n", "    (\n", "    'BISTR<PERSON>_SYSTEM_PAY_BEFORE_DELIVERY',\n", "    'BISTRO_SYSTEM_CANCEL',\n", "    'BISTR<PERSON>_SYSTEM_PROCUREMENT_UPDATE',\n", "    'BISTR<PERSON>_SYSTEM_NOT_PROCURED',\n", "    'BISTRO_SYSTEM_PRICE_CHANGED'\n", "    )\n", "    AND LOWER(status) = 'resolved'\n", "    AND org_id = '4'\n", "    \n", "GROUP BY 1\n", "),\n", "\n", "-- Putting it all together ---\n", "\n", "output AS\n", "(\n", "SELECT\n", "    o.customer_id,\n", "    cart_count,\n", "    order_count,\n", "    CASE \n", "        WHEN gmv = 0 THEN 1\n", "    ELSE\n", "        gmv \n", "    END AS gmv,\n", "    COALESCE(complaint_order_count,0) AS complaint_order_count,\n", "    COALESCE(resolution_order_count,0) AS resolution_order_count,\n", "    COALESCE(resolution_amt,0) AS resolution_amt\n", "FROM\n", "    agg_order_metrics o \n", "LEFT JOIN\n", "    agg_complaint_metrics c ON o.customer_id = c.customer_id\n", "LEFT JOIN\n", "    agg_resolution_metrics r ON o.customer_id = r.customer_id\n", "    \n", ")\n", "\n", "SELECT\n", "    *\n", "FROM\n", "    output\n", "WHERE\n", "   cart_count >= {MIN_ELIGIBLE_ORDER_COUNT}\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "cbcc0503-f4bd-4149-af30-6b58855fc38e", "metadata": {}, "outputs": [], "source": ["eligible_df = fetch_data(QUERY)"]}, {"cell_type": "code", "execution_count": null, "id": "8a249680-b7fa-404c-998a-17bb7006d6b8", "metadata": {}, "outputs": [], "source": ["eligible_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9cf482ab-f8dd-40b7-9c92-7f5c3fc36261", "metadata": {}, "outputs": [], "source": ["eligible_df[\"frequency\"] = eligible_df[\"cart_count\"]\n", "eligible_df[\"monetary\"] = eligible_df[\"gmv\"] - eligible_df[\"resolution_amt\"]\n", "eligible_df[\"amount_factor\"] = round(100 * (eligible_df[\"resolution_amt\"] / eligible_df[\"gmv\"]), 3)\n", "eligible_df[\"complaint_factor\"] = round(\n", "    100 * (eligible_df[\"complaint_order_count\"] / eligible_df[\"cart_count\"]), 3\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9864cbf0-8a8d-4221-8875-490bf2dfd627", "metadata": {}, "outputs": [], "source": ["if not np.isinf(eligible_df).any().sum() and not eligible_df.isna().any().sum():\n", "    print(\"All good\")\n", "else:\n", "    print(\"Something is wrong!\")\n", "    ERROR_CNT += 1"]}, {"cell_type": "code", "execution_count": null, "id": "3486002e-306c-4555-aa53-202175930305", "metadata": {}, "outputs": [], "source": ["eligible_df[\"complaint_perc_percentile\"] = np.where(\n", "    eligible_df[\"amount_factor\"] == 0,  # Condition\n", "    0,  # Value if condition is True\n", "    100\n", "    * (\n", "        eligible_df[\"complaint_factor\"].rank(pct=True, ascending=True)\n", "    ),  # Value if condition is False\n", ")\n", "\n", "eligible_df[\"amount_perc_percentile\"] = np.where(\n", "    eligible_df[\"amount_factor\"] == 0,  # Condition\n", "    0,  # Value if condition is True\n", "    100\n", "    * (eligible_df[\"amount_factor\"].rank(pct=True, ascending=True)),  # Value if condition is False\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8c968478-4439-4d8d-b59e-748feba225db", "metadata": {}, "outputs": [], "source": ["eligible_df.head()"]}, {"cell_type": "markdown", "id": "b212fe9d-9e32-4133-86e9-867d1b898e2d", "metadata": {}, "source": ["## NTiling Customers"]}, {"cell_type": "code", "execution_count": null, "id": "b9a0bccf-7f52-4735-a426-89b8100b3ea7", "metadata": {}, "outputs": [], "source": ["f_labels = range(1, NTILE + 1)\n", "frequency = eligible_df[\"frequency\"].rank(method=\"first\")  # rank to deal with duplicate values\n", "f_quartiles, bins = pd.qcut(frequency, NTILE, labels=f_labels, retbins=True)\n", "eligible_df = eligible_df.assign(F=f_quartiles.values)\n", "\n", "\n", "# monetary value quartile segmentation\n", "\n", "m_labels = range(1, NTILE + 1)\n", "monetary = eligible_df[\"monetary\"]\n", "m_quartiles, bins = pd.qcut(monetary, NTILE, labels=m_labels, retbins=True)\n", "eligible_df = eligible_df.assign(M=m_quartiles.values)"]}, {"cell_type": "code", "execution_count": null, "id": "f86067ef-60e5-4067-b3ab-fed1b9a7029f", "metadata": {}, "outputs": [], "source": ["eligible_df"]}, {"cell_type": "code", "execution_count": null, "id": "96a4798b-e503-465d-af3f-ff5be69137c5", "metadata": {}, "outputs": [], "source": ["c_df = eligible_df.query(\"complaint_factor > 0\").copy()\n", "c_df[\"complaint_factor\"] = c_df[\"complaint_factor\"] * (-1.0)\n", "\n", "c_labels = range(1, NTILE + 1)\n", "complaint_factor = c_df[\"complaint_factor\"]\n", "c_quartiles, bins = pd.qcut(complaint_factor, NTILE, labels=c_labels, retbins=True)\n", "c_df = c_df.assign(C=c_quartiles.values)\n", "\n", "c_df[\"complaint_factor\"] = c_df[\"complaint_factor\"] * (-1.0)\n", "eligible_df = eligible_df.merge(c_df[[\"customer_id\", \"C\"]], on=\"customer_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "cde64564-6326-4f39-8d12-7d66f2326041", "metadata": {}, "outputs": [], "source": ["eligible_df[\"C\"] = pd.to_numeric(eligible_df[\"C\"], errors=\"coerce\")\n", "eligible_df[\"C\"].fillna(NTILE, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "9a1a8dff-9c3d-4579-bedb-16b129319123", "metadata": {}, "outputs": [], "source": ["eligible_df.groupby(\"C\")[\"complaint_factor\"].min()"]}, {"cell_type": "code", "execution_count": null, "id": "46af61f0-d661-4a0b-8164-f4fb69542cad", "metadata": {}, "outputs": [], "source": ["eligible_df.groupby(\"C\")[\"complaint_factor\"].max()"]}, {"cell_type": "code", "execution_count": null, "id": "2f06f64f-5200-4066-bbe0-84de97a17854", "metadata": {}, "outputs": [], "source": ["print(eligible_df[\"C\"].value_counts())\n", "print(100 * (eligible_df[\"C\"].value_counts(1)))"]}, {"cell_type": "code", "execution_count": null, "id": "92c17170-5dfd-4132-b0e9-20d2db2b35a5", "metadata": {}, "outputs": [], "source": ["eligible_df.head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "824249b1-e43c-442a-88a4-5971c4994a0e", "metadata": {"tags": []}, "outputs": [], "source": ["NTILE = 5\n", "default_score = NTILE\n", "a_labels = list(range(NTILE - 1, 0, -1))  # [4, 3, 2, 1] — if lower is better\n", "\n", "# Subset with amount_factor > 0\n", "mask = eligible_df[\"amount_factor\"] > 0\n", "df_nonzero = eligible_df[mask].copy()\n", "\n", "# Rank and bin only non-zero refund customers\n", "amount_factor_ranked = df_nonzero[\"amount_factor\"].rank(method=\"first\", ascending=True)\n", "a_quartiles, bins = pd.qcut(\n", "    amount_factor_ranked, NTILE - 1, labels=a_labels, retbins=True, duplicates=\"drop\"\n", ")\n", "df_nonzero[\"A\"] = a_quartiles.astype(int)  # convert categorical to int\n", "\n", "# Merge and fill\n", "eligible_df = eligible_df.merge(df_nonzero[[\"customer_id\", \"A\"]], on=\"customer_id\", how=\"left\")\n", "eligible_df[\"A\"] = eligible_df[\"A\"].fillna(default_score).astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "dc24e858-0837-45a4-9578-0e22d853fd3e", "metadata": {}, "outputs": [], "source": ["eligible_df.groupby(\"A\")[\"amount_factor\"].min()"]}, {"cell_type": "code", "execution_count": null, "id": "ccda9ff2-776b-41e1-80ec-c4e0dd35376f", "metadata": {}, "outputs": [], "source": ["eligible_df.groupby(\"A\")[\"amount_factor\"].max()"]}, {"cell_type": "code", "execution_count": null, "id": "54e41694-c53f-4f84-8d1f-1c982a307cb5", "metadata": {}, "outputs": [], "source": ["print(eligible_df[\"A\"].value_counts())\n", "print(100 * (eligible_df[\"A\"].value_counts(1)))"]}, {"cell_type": "markdown", "id": "86c31dc4-a017-4b0b-990f-f9573131b6f0", "metadata": {"tags": []}, "source": ["### Setting Boundary Conditions"]}, {"cell_type": "code", "execution_count": null, "id": "a9dc465b-5378-47f0-a05c-f24aebd2e497", "metadata": {}, "outputs": [], "source": ["def set_boundary_conditions(df, factor, transformer_variable):\n", "\n", "    groups = df.groupby(factor)\n", "    min_value = groups[transformer_variable].transform(\"max\")\n", "    adjusted_column = df[transformer_variable].where(\n", "        df[transformer_variable] != min_value, min_value\n", "    )\n", "    return adjusted_column"]}, {"cell_type": "code", "execution_count": null, "id": "065472e4-5810-40b0-842b-15fec999925b", "metadata": {}, "outputs": [], "source": ["eligible_df[\"F\"] = set_boundary_conditions(eligible_df, \"frequency\", \"F\")\n", "eligible_df[\"M\"] = set_boundary_conditions(eligible_df, \"monetary\", \"M\")\n", "eligible_df[\"A\"] = set_boundary_conditions(eligible_df, \"amount_factor\", \"A\")\n", "eligible_df[\"C\"] = set_boundary_conditions(eligible_df, \"complaint_factor\", \"C\")"]}, {"cell_type": "code", "execution_count": null, "id": "c5634cbb-0283-4b10-bc3e-ac40241a37ba", "metadata": {}, "outputs": [], "source": ["if not eligible_df[[\"F\", \"M\", \"A\", \"C\"]].isna().any().sum():\n", "    print(\"All good\")\n", "else:\n", "    print(\"Something is wrong!\")\n", "    ERROR_CNT += 1"]}, {"cell_type": "code", "execution_count": null, "id": "7e2ea5ce-0f81-4ff8-90da-38fc93ad0996", "metadata": {}, "outputs": [], "source": ["eligible_df.sort_values(by=\"cart_count\", ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "1e8d4d0a-3542-451e-99b0-4d2290eaefc1", "metadata": {}, "outputs": [], "source": ["eligible_df.groupby([\"F\"])[\"frequency\"].min(), eligible_df.groupby([\"F\"])[\"frequency\"].max()"]}, {"cell_type": "markdown", "id": "ef918546-dbd9-4c4e-8c32-ef27ec54b689", "metadata": {}, "source": ["## Setting Karma Score"]}, {"cell_type": "code", "execution_count": null, "id": "0bec905d-dafb-48d8-8445-870a7349ee73", "metadata": {}, "outputs": [], "source": ["eligible_df[\"segment\"] = (\n", "    eligible_df[\"F\"].astype(str)\n", "    + eligible_df[\"M\"].astype(str)\n", "    + eligible_df[\"A\"].astype(str)\n", "    + eligible_df[\"C\"].astype(str)\n", ")\n", "\n", "eligible_df[\"segment_score\"] = eligible_df[[\"F\", \"M\", \"A\", \"C\"]].sum(axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "52d5108a-1198-482d-8bba-05523d94a2ca", "metadata": {}, "outputs": [], "source": ["print(eligible_df.shape)"]}, {"cell_type": "markdown", "id": "7f0c9e95-8fee-4e2f-8ce8-1b0420b150b3", "metadata": {}, "source": ["## <PERSON> Marking"]}, {"cell_type": "code", "execution_count": null, "id": "1f5eb873-c884-40f3-b682-e0d21c556ad5", "metadata": {}, "outputs": [], "source": ["amount_factor_iron_perc = eligible_df.query(\n", "    \"amount_factor > 0 and complaint_factor > 0\"\n", ").amount_factor.quantile(IRON_MARKING_RESOLUTION_PERC)\n", "\n", "complaint_factor_iron_perc = eligible_df.query(\n", "    \"amount_factor > 0 and complaint_factor > 0\"\n", ").complaint_factor.quantile(IRON_MARKING_COMPLAINT_PERC)"]}, {"cell_type": "code", "execution_count": null, "id": "44038685-1551-44ba-bf39-62a0809bd70c", "metadata": {}, "outputs": [], "source": ["iron_mask = (\n", "    (eligible_df[\"resolution_order_count\"] > IRON_MARKING_MIN_RESO_CARTS)\n", "    & (eligible_df[\"complaint_factor\"] >= complaint_factor_iron_perc)\n", "    & (eligible_df[\"amount_factor\"] >= amount_factor_iron_perc)\n", "    & (eligible_df[\"amount_factor\"] >= IRON_MIN_COMPLAINT_CARTS)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0ddba112-fd72-44b3-927d-71bd21b822c1", "metadata": {}, "outputs": [], "source": ["eligible_df[\"score\"] = np.where(iron_mask, 3, eligible_df[\"segment_score\"])"]}, {"cell_type": "code", "execution_count": null, "id": "6eea09ac-af0e-4533-a3f1-7d82b4d168bd", "metadata": {}, "outputs": [], "source": ["eligible_df[\"karma_score\"] = eligible_df[\"score\"]\n", "eligible_df[\"updated_at\"] = pd.Timestamp(\"today\").strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "9800645d-fe7d-472f-9270-b1886b6db272", "metadata": {}, "outputs": [], "source": ["score_grade_map = {3: \"Iron\", 4: \"Bronze\", 13: \"Silver\", 17: \"Gold\"}\n", "\n", "eligible_df[\"karma_label\"] = eligible_df.karma_score.apply(\n", "    lambda x: score_grade_map[max(filter(lambda k: k <= x, score_grade_map.keys()))]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "81acb531-bd97-48bc-9812-8fcca9e739b0", "metadata": {}, "outputs": [], "source": ["eligible_df[\"karma_source\"] = \"bistro_karma_v0\""]}, {"cell_type": "code", "execution_count": null, "id": "9a8be31b-0d63-4331-b7ae-2468fb0d9961", "metadata": {}, "outputs": [], "source": ["eligible_df[\"karma_label\"].value_counts(1)"]}, {"cell_type": "markdown", "id": "35e6ab66-03be-4038-84d8-3f8f80126d9f", "metadata": {"tags": []}, "source": ["## Detour - Analysis of Premium and Iron Cx"]}, {"cell_type": "code", "execution_count": null, "id": "4b552260-9713-432b-afad-e86436c1455f", "metadata": {}, "outputs": [], "source": ["iron_df = eligible_df.query(\"karma_score == 3\")\n", "premium_df = eligible_df.query(\"F in [4,5] and M == 5 and A in [4,5]\")\n", "iron_df.head()\n", "premium_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "be97a6d7-219d-47a3-b8d4-be04c74cc251", "metadata": {}, "outputs": [], "source": ["# iron_df.to_excel(\"Iron Cx.xlsx\")\n", "# premium_df.to_excel(\"Premium Cx.xlsx\")"]}, {"cell_type": "markdown", "id": "deaa67d0-3b35-47a9-a497-492546d14d40", "metadata": {}, "source": ["## Updating Force Karma"]}, {"cell_type": "code", "execution_count": null, "id": "7a3de5f8-2f33-445d-aad7-5e5512fb2d7d", "metadata": {}, "outputs": [], "source": ["FORCE_KARMA_UPDATE_QUERY = f\"\"\"\n", "\n", "WITH forced_karma AS\n", "(\n", "SELECT \n", "    customer_id,\n", "    updated_karma_label AS forced_karma_label,\n", "    TRY_CAST(updated_karma_score AS BIGINT) AS forced_karma_score\n", "FROM\n", "    {FORCED_UPDATE_SCHEMA}.{FORCED_UPDATE_TABLE}\n", "\n", "WHERE \n", "LOWER(reason_code) != 'new user signup with linked iron account'\n", "AND expire_ts > CURRENT_TIMESTAMP - INTERVAL'330'MINUTE \n", "\n", "GROUP BY 1,2,3\n", "),\n", "\n", "\n", "final AS\n", "(\n", "SELECT\n", "    mx.customer_id,\n", "    mx.forced_karma_label,\n", "    mx.forced_karma_score,\n", "    'crm_force_karma' AS forced_karma_source\n", "FROM\n", "    forced_karma mx\n", "GROUP BY 1,2,3,4\n", ")\n", "\n", "SELECT * FROM final\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "9f7c576b-63a3-4a7b-aa6e-b384a394b0db", "metadata": {}, "outputs": [], "source": ["force_u_df = fetch_data(FORCE_KARMA_UPDATE_QUERY)"]}, {"cell_type": "code", "execution_count": null, "id": "13707e2f-8a63-4355-bb25-dc9b9330948d", "metadata": {}, "outputs": [], "source": ["force_u_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8be20c4f-56f7-4415-8f6a-51ed9fac78c3", "metadata": {}, "outputs": [], "source": ["df_merged = pd.merge(eligible_df, force_u_df, on=\"customer_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "0fd12312-a57f-4880-a94e-e330ebf82d94", "metadata": {}, "outputs": [], "source": ["df_merged[\"karma_label\"].update(df_merged[\"forced_karma_label\"])\n", "df_merged[\"karma_score\"].update(df_merged[\"forced_karma_score\"])\n", "df_merged[\"karma_source\"].update(df_merged[\"forced_karma_source\"])\n", "\n", "df_merged.drop(\n", "    columns=[\"forced_karma_label\", \"forced_karma_score\", \"forced_karma_source\"],\n", "    inplace=True,\n", ")\n", "\n", "del force_u_df"]}, {"cell_type": "code", "execution_count": null, "id": "2e6246ab-df9c-4d5e-b5ec-5711c76952c8", "metadata": {}, "outputs": [], "source": ["df_merged.head()"]}, {"cell_type": "code", "execution_count": null, "id": "fee73e98-d6f0-46e3-8349-f9ccebc4e483", "metadata": {}, "outputs": [], "source": ["100 * (df_merged[\"karma_label\"].value_counts(1))"]}, {"cell_type": "code", "execution_count": null, "id": "1f128eb8-7a07-4686-8382-335abb9b2d05", "metadata": {}, "outputs": [], "source": ["df_merged[\"karma_label\"].value_counts()"]}, {"cell_type": "markdown", "id": "487dcc55-5370-4830-8661-4a83798c4aa8", "metadata": {"tags": []}, "source": ["# Pushing Values to Data Tables"]}, {"cell_type": "code", "execution_count": null, "id": "7f004d43-b39d-4ec6-8e81-0fecf6c0f289", "metadata": {}, "outputs": [], "source": ["# df_merged.to_excel('All cx >= 1 bistro.xlsx')"]}, {"cell_type": "code", "execution_count": null, "id": "8662cb3a-3e58-4585-949b-c9d476225448", "metadata": {}, "outputs": [], "source": ["df_merged = df_merged[\n", "    [\n", "        \"customer_id\",\n", "        \"cart_count\",\n", "        \"gmv\",\n", "        \"frequency\",\n", "        \"monetary\",\n", "        \"complaint_order_count\",\n", "        \"resolution_order_count\",\n", "        \"resolution_amt\",\n", "        \"F\",\n", "        \"M\",\n", "        \"A\",\n", "        \"C\",\n", "        \"segment\",\n", "        \"segment_score\",\n", "        \"karma_score\",\n", "        \"updated_at\",\n", "        \"karma_label\",\n", "        \"karma_source\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "0617d1ff-0069-43fb-b70c-7eb14180bf10", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"customer_id\", \"type\": \"BIGINT\", \"description\": \"Customer ID\"},\n", "    {\"name\": \"cart_count\", \"type\": \"BIGINT\", \"description\": \"Cart Count\"},\n", "    {\"name\": \"gmv\", \"type\": \"REAL\", \"description\": \"GMV\"},\n", "    {\"name\": \"frequency\", \"type\": \"REAL\", \"description\": \"frequency\"},\n", "    {\"name\": \"monetary\", \"type\": \"REAL\", \"description\": \"monetary\"},\n", "    {\"name\": \"complaint_order_count\", \"type\": \"REAL\", \"description\": \"complaint_order_count\"},\n", "    {\"name\": \"resolution_order_count\", \"type\": \"REAL\", \"description\": \"resolution_order_count\"},\n", "    {\"name\": \"resolution_amt\", \"type\": \"REAL\", \"description\": \"resolution amount (ref+rep)\"},\n", "    {\"name\": \"F\", \"type\": \"BIGINT\", \"description\": \"F\"},\n", "    {\"name\": \"M\", \"type\": \"BIGINT\", \"description\": \"M\"},\n", "    {\"name\": \"A\", \"type\": \"BIGINT\", \"description\": \"A\"},\n", "    {\"name\": \"C\", \"type\": \"BIGINT\", \"description\": \"C\"},\n", "    {\"name\": \"segment\", \"type\": \"varchar\", \"description\": \"segment\"},\n", "    {\"name\": \"segment_score\", \"type\": \"BIGINT\", \"description\": \"Segement Score\"},\n", "    {\"name\": \"updated_at\", \"type\": \"date\", \"description\": \"Updated at\"},\n", "    {\"name\": \"karma_score\", \"type\": \"REAL\", \"description\": \"Final Score\"},\n", "    {\"name\": \"karma_label\", \"type\": \"varchar\", \"description\": \"karma_label\"},\n", "    {\"name\": \"karma_source\", \"type\": \"varchar\", \"description\": \"karma_label\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "9838780b-da0d-45b0-a861-89bec73df7de", "metadata": {}, "outputs": [], "source": ["df_merged.segment_score = df_merged.segment_score.astype(\"int\")\n", "df_merged.updated_at = df_merged.updated_at.astype(\"datetime64[ns]\")"]}, {"cell_type": "code", "execution_count": null, "id": "fe9685c7-2237-4235-a3f1-c2b55dc284db", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": INTERIM_TABLE_SCHEMA,\n", "    \"table_name\": INTERIM_TABLE_NAME,\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"customer_id\"],\n", "    \"sortkey\": [\"updated_at\"],\n", "    \"incremental_key\": \"customer_id\",\n", "    \"load_type\": \"truncate\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This table stores final scores and karma label for customers for slot prioritisation; based on Recency , Frequency , Monetary , Complaint rate and refund amount rate. Only customers who transacted in previous 3 months are considered.\",\n", "}\n", "\n", "pb.to_trino(df_merged, **kwargs)\n", "\n", "# del df_merged"]}, {"cell_type": "code", "execution_count": null, "id": "96660161-99cb-4064-9ae3-16f7f5134739", "metadata": {}, "outputs": [], "source": ["# df_merged.to_excel(\"All Cx Karma.xlsx\")"]}, {"cell_type": "code", "execution_count": null, "id": "66bfcda9-7991-49df-8cb2-eeb5a9e58d5a", "metadata": {}, "outputs": [], "source": ["FINAL_UPSERT_QUERY = f\"\"\"\n", "\n", "SELECT \n", "    customer_id, \n", "    karma_score AS score, \n", "    karma_label AS label, \n", "    CAST(updated_at AS DATE) AS updated_at, \n", "    karma_source \n", "FROM  \n", "    {INTERIM_TABLE_SCHEMA}.{INTERIM_TABLE_NAME}\n", "GROUP BY 1,2,3,4,5\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "0dc159e3-f781-407f-b2d7-ab21cc98fac6", "metadata": {}, "outputs": [], "source": ["# INSERT INTO SEGMENT TABLE\n", "\n", "column_dtypes = [\n", "    {\"name\": \"customer_id\", \"type\": \"bigint\", \"description\": \"Customer ID \"},\n", "    {\"name\": \"score\", \"type\": \"double\", \"description\": \"Karma score.range: 4-20\"},\n", "    {\n", "        \"name\": \"label\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Category label : Iron : 4 , Bronze: 5-12 , Silver: 13-16 , Gold: 17-20\",\n", "    },\n", "    {\n", "        \"name\": \"karma_source\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"The source from which the karma is derived\",\n", "    },\n", "    {\"name\": \"updated_at\", \"type\": \"date\", \"description\": \"Updated at\"},\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": SEGMENT_TABLE_SCHEMA,\n", "    \"table_name\": SEGMENT_TABLE_NAME,\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"customer_id\"],\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This table stores final scores and karma label for customers for slot prioritisation; based on Recency , Frequency , Monetary , Complaint rate and refund amount rate. Only customers who transacted in previous 3 months are considered.\",\n", "}\n", "\n", "# pb.to_trino(FINAL_UPSERT_QUERY, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "2d8e1f2e-bbac-46e4-8034-e951500026e7", "metadata": {}, "outputs": [], "source": ["# final_shape_df = pd.read_sql_query(\n", "#     f\"\"\"SELECT SUM(1) FROM {SEGMENT_TABLE_SCHEMA}.{SEGMENT_TABLE_NAME}\"\"\", CON_TRINO\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "366b9850-9916-43ab-80e5-0be9d81d6808", "metadata": {}, "outputs": [], "source": ["# pb.send_slack_message(\n", "#     channel=\"bkfp-alerts\",\n", "#     text=f\"{SEGMENT_TABLE_NAME} Updated with {int(final_shape_df.values[0])} rows. {int(final_shape_df.values[0]) - LAST_RUN_VALUE} new customers onboarded.\",\n", "# )"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}