alert_configs:
  slack:
  - channel: bl-fps-airflow-alerts
dag_name: otp_skip_eligibility
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: fps
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03S5ACSW3C
path: fps/otp/etl/otp_skip_eligibility
paused: false
pool: fps_pool
project_name: otp
schedule:
  end_date: '2025-07-28T00:00:00'
  interval: 30 0,12 * * *
  start_date: '2025-04-16T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 5
