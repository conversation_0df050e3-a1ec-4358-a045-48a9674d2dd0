{"cells": [{"cell_type": "code", "execution_count": null, "id": "a6b22d8b-7b3b-4f3f-be11-027e21f5bd8b", "metadata": {"papermill": {"duration": 10.392966, "end_time": "2025-06-19T11:40:47.001931", "exception": false, "start_time": "2025-06-19T11:40:36.608965", "status": "completed"}, "tags": []}, "outputs": [], "source": ["!pip install numpy==1.26.4\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import time\n", "from datetime import datetime, timedelta, date\n", "import gc\n", "\n", "# !pip install pandasql\n", "# import pandasql as ps"]}, {"cell_type": "code", "execution_count": null, "id": "9baf8cdd-b1e4-4feb-b120-d6a6f59ae3da", "metadata": {"papermill": {"duration": 0.169138, "end_time": "2025-06-19T11:40:47.253728", "exception": false, "start_time": "2025-06-19T11:40:47.084590", "status": "completed"}, "tags": []}, "outputs": [], "source": ["trino = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "3494cf16-a377-4a4d-b2e4-060516c32771", "metadata": {"papermill": {"duration": 0.044169, "end_time": "2025-06-19T11:40:47.421646", "exception": false, "start_time": "2025-06-19T11:40:47.377477", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "260cb160-7a33-4340-92ac-c6dcda3ce810", "metadata": {"papermill": {"duration": 0.083036, "end_time": "2025-06-19T11:40:47.569156", "exception": false, "start_time": "2025-06-19T11:40:47.486120", "status": "completed"}, "tags": []}, "outputs": [], "source": ["pd.options.mode.chained_assignment = None  # default='warn'"]}, {"cell_type": "code", "execution_count": null, "id": "e368e024-a705-437f-8701-7fca30d8a162", "metadata": {"papermill": {"duration": 10.564017, "end_time": "2025-06-19T11:40:58.178313", "exception": false, "start_time": "2025-06-19T11:40:47.614296", "status": "completed"}, "tags": []}, "outputs": [], "source": ["!pip install matplotlib"]}, {"cell_type": "code", "execution_count": null, "id": "0140f923-7009-451d-a63e-1fdde4b0a6b9", "metadata": {"papermill": {"duration": 0.056152, "end_time": "2025-06-19T11:40:58.315778", "exception": false, "start_time": "2025-06-19T11:40:58.259626", "status": "completed"}, "tags": []}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pytz\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")\n", "\n", "current_time = datetime.now(IST)\n", "\n", "current_hour = pd.to_datetime(current_time).hour\n", "\n", "today_date = current_time.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "markdown", "id": "3c1013cd-139b-4523-a6e8-963c9a9943e0", "metadata": {"papermill": {"duration": 0.05234, "end_time": "2025-06-19T11:40:58.428161", "exception": false, "start_time": "2025-06-19T11:40:58.375821", "status": "completed"}, "tags": []}, "source": ["# Active Assortment "]}, {"cell_type": "code", "execution_count": null, "id": "13da5275-2bc0-4160-85ca-6917a8ea3b13", "metadata": {"papermill": {"duration": 0.04511, "end_time": "2025-06-19T11:40:58.512793", "exception": false, "start_time": "2025-06-19T11:40:58.467683", "status": "completed"}, "tags": []}, "outputs": [], "source": ["l2_id_list = (\n", "    1185,\n", "    1425,\n", "    1956,\n", "    130,\n", "    949,\n", "    1389,\n", "    1778,\n", "    1959,\n", "    477,\n", "    943,\n", "    1182,\n", "    1827,\n", "    33,\n", "    89,\n", "    108,\n", "    3542,\n", "    952,\n", "    950,\n", "    138,\n", "    1091,\n", "    1093,\n", "    31,\n", "    198,\n", "    116,\n", "    1097,\n", "    2633,\n", "    1094,\n", "    36,\n", "    2466,\n", "    342,\n", "    176,\n", "    1429,\n", "    63,\n", "    1367,\n", "    1369,\n", "    1201,\n", "    3113,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0eebba13-6936-4095-b0fa-fd19d1db9a2e", "metadata": {"papermill": {"duration": 67.668042, "end_time": "2025-06-19T11:42:06.227423", "exception": false, "start_time": "2025-06-19T11:40:58.559381", "status": "completed"}, "tags": []}, "outputs": [], "source": ["base_query = f\"\"\"\n", "    WITH active_stores AS (\n", "        SELECT DISTINCT pfom.facility_id, pfom.outlet_id, bfom.facility_id AS be_facility_id\n", "        FROM po.physical_facility_outlet_mapping pfom \n", "        JOIN retail.console_outlet co ON co.id = pfom.outlet_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "        JOIN po.bulk_facility_outlet_mapping bfom ON bfom.outlet_id = pfom.outlet_id AND bfom.active = True AND bfom.lake_active_record\n", "        WHERE ars_active = 1 AND pfom.active = 1 AND pfom.is_primary = 1 AND pfom.lake_active_record\n", "    ),\n", "\n", "    assortment AS (\n", "        SELECT DISTINCT cl.name AS city, a.facility_id, outlet_id, a.item_id, be_facility_id\n", "        FROM rpc.product_facility_master_assortment a\n", "        JOIN active_stores b ON a.facility_id = b.facility_id\n", "        JOIN (\n", "            SELECT DISTINCT a.item_id\n", "            FROM rpc.product_product a\n", "            JOIN (\n", "                SELECT item_id, MAX(id) AS id\n", "                FROM rpc.product_product\n", "                WHERE approved = 1 AND active = 1\n", "                GROUP BY 1\n", "            ) b ON a.id = b.id AND a.item_id = b.item_id\n", "            WHERE a.active = 1 AND a.lake_active_record AND a.approved = 1 AND a.perishable = 1\n", "        ) ma ON ma.item_id = a.item_id\n", "        JOIN retail.console_outlet co ON co.id = b.outlet_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "        LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id AND cl.lake_active_record\n", "        WHERE a.item_id IN (SELECT DISTINCT item_id FROM rpc.item_category_details WHERE l2_id IN {l2_id_list})\n", "        AND a.active = 1 AND a.master_assortment_substate_id = 1 AND a.lake_active_record\n", "    ),\n", "\n", "    be_mapping AS (\n", "        SELECT DISTINCT item_id, outlet_id, cb.facility_id AS be_facility_id , cb.name AS be_outlet_name, cb.id AS be_outlet_id\n", "        FROM rpc.item_outlet_tag_mapping tm\n", "        JOIN retail.console_outlet cb ON cb.id = CAST(tag_value AS int) AND cb.active = 1 AND cb.lake_active_record\n", "        WHERE tm.active = 1 AND tm.tag_type_id = 8 AND tm.lake_active_record\n", "    ),\n", "\n", "    final AS (\n", "        SELECT city, facility_id, a.outlet_id, \n", "        CASE \n", "           when l2_id in (1185) THEN 'Fresh Milk'\n", "            WHEN l2_id IN (1425) THEN 'Batter'\n", "            WHEN l2_id IN (31,198) THEN 'Regular Breads'\n", "            WHEN l2_id IN (1956) THEN 'Buns, Pavs & Pizza Base'\n", "            WHEN l2_id IN (116,1097,2633) THEN 'Speciality Breads'\n", "            WHEN l2_id IN (63,1367,1369, 1201, 1730) THEN 'Meats'\n", "            WHEN l2_id IN (949) THEN 'Curd'\n", "            WHEN l2_id IN (1389,1778) THEN 'Eggs'\n", "            WHEN l2_id IN (950) THEN 'Paneer'\n", "            WHEN l2_id IN (138) THEN 'Tofu'\n", "            WHEN l2_id IN (1091) THEN 'Speciality Milk'\n", "            WHEN l2_id IN (1093) THEN 'Lassi & Chaach'\n", "            WHEN l2_id IN (1094) THEN 'Yogurt'\n", "            else 'others'\n", "            END AS l2, \n", "        a.item_id, icd.name AS item_name, icd.product_type AS ptype, a.be_facility_id , b.be_outlet_id, b.be_outlet_name\n", "        FROM assortment a\n", "        JOIN be_mapping b ON a.item_id = b.item_id AND a.outlet_id = b.outlet_id AND a.be_facility_id = b.be_facility_id\n", "        JOIN rpc.item_category_details icd ON icd.item_id = a.item_id AND icd.lake_active_record\n", "    )\n", "    \n", "    SELECT city, facility_id, outlet_id, ptype, item_id, item_name, be_facility_id, be_outlet_id, be_outlet_name, l2\n", "    FROM final\n", "\"\"\"\n", "base_df = read_sql_query(base_query, trino)\n", "\n", "base_df[\"be_facility_id\"] = base_df[\"be_facility_id\"].fillna(0).astype(int)\n", "\n", "base_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "f5bacebc-2ac9-4745-8b57-74c03424d4de", "metadata": {"papermill": {"duration": 0.261667, "end_time": "2025-06-19T11:42:06.530836", "exception": false, "start_time": "2025-06-19T11:42:06.269169", "status": "completed"}, "tags": []}, "outputs": [], "source": ["mapping_df = base_df[\n", "    [\n", "        \"city\",\n", "        \"facility_id\",\n", "        \"outlet_id\",\n", "        \"ptype\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"l2\",\n", "        \"be_facility_id\",\n", "        \"be_outlet_id\",\n", "        \"be_outlet_name\",\n", "    ]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "c85fc0ed-3ad7-4f87-b1eb-03e40cf2d1e1", "metadata": {"papermill": {"duration": 0.04674, "end_time": "2025-06-19T11:42:06.633550", "exception": false, "start_time": "2025-06-19T11:42:06.586810", "status": "completed"}, "tags": []}, "outputs": [], "source": ["item_list = tuple(base_df[\"item_id\"].unique())"]}, {"cell_type": "code", "execution_count": null, "id": "106911ea-41a6-493c-a96d-991ba0d2390d", "metadata": {"papermill": {"duration": 47.137758, "end_time": "2025-06-19T11:42:53.810997", "exception": false, "start_time": "2025-06-19T11:42:06.673239", "status": "completed"}, "tags": []}, "outputs": [], "source": ["base_query = f\"\"\"\n", "\n", "with be_mapping AS\n", "  ( SELECT DISTINCT item_id,\n", "                    outlet_id,\n", "                    cb.facility_id AS be_facility_id,\n", "                    cb.name AS be_outlet_name,\n", "                    cb.id AS be_outlet_id\n", "   FROM rpc.item_outlet_tag_mapping tm\n", "   JOIN retail.console_outlet cb ON cb.id = CAST(tag_value AS int)\n", "   AND cb.active = 1\n", "   AND cb.lake_active_record\n", "   WHERE tm.active = 1\n", "     AND tm.tag_type_id = 8\n", "     AND tm.lake_active_record \n", "     and tm.item_id IN {item_list}),\n", "     \n", "     \n", "stores_mapped as\n", "(select be_facility_id, be_outlet_id,be_outlet_name, count(distinct outlet_id) as fe_outlets_mapped\n", "from be_mapping\n", "group by 1,2,3\n", ")\n", "\n", "\n", "select \n", "be_facility_id, \n", "case  when fe_outlets_mapped > 3 then 'CPC'\n", "     else 'DTS'\n", "     end as dts_cpc_flag  \n", "\n", "from stores_mapped\n", "\n", "group by 1,2\n", "\"\"\"\n", "\n", "dts_cpc = read_sql_query(base_query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "7a428371-75b8-4b58-9eba-82ef3bc0f8bc", "metadata": {"papermill": {"duration": 0.04097, "end_time": "2025-06-19T11:42:53.890432", "exception": false, "start_time": "2025-06-19T11:42:53.849462", "status": "completed"}, "tags": []}, "outputs": [], "source": ["dts_cpc.shape"]}, {"cell_type": "markdown", "id": "39d96073-302c-4647-ad45-9129bfba07a3", "metadata": {"papermill": {"duration": 0.039032, "end_time": "2025-06-19T11:42:53.975070", "exception": false, "start_time": "2025-06-19T11:42:53.936038", "status": "completed"}, "tags": []}, "source": ["## GMV"]}, {"cell_type": "code", "execution_count": null, "id": "964dd33c-e6ae-4817-a54f-f506e3273c72", "metadata": {"papermill": {"duration": 116.175518, "end_time": "2025-06-19T11:44:50.199811", "exception": false, "start_time": "2025-06-19T11:42:54.024293", "status": "completed"}, "tags": []}, "outputs": [], "source": ["base_query = f\"\"\"\n", "\n", "with gmv as (\n", "select \n", "    date(oid.cart_checkout_ts_ist) as date_,\n", "    case    \n", "    when date(oid.cart_checkout_ts_ist) between current_date- interval '7' day and current_date - interval '1' day then 'L7'\n", "    when date(oid.cart_checkout_ts_ist)  between current_date- interval '14' day and current_date- interval '8' day then 'L14'\n", "    when date(oid.cart_checkout_ts_ist)  between current_date- interval '21' day and current_date - interval '15' day then 'L21'\n", "    else 'Nil'end as flag,\n", "    oid.outlet_id,\n", "    item_id,\n", "    sum(oid.total_selling_price) as gmv\n", "from \n", "    (select * from dwh.fact_sales_order_item_details oid where (oid.order_create_dt_ist between current_date - interval '21' day \n", "    and current_date - interval '1' day) and (oid.cart_checkout_ts_ist >= current_date - interval '21' day)\n", "    AND (oid.is_internal_order = false) AND (oid.order_type NOT LIKE '%%internal%%' OR oid.order_type IS NULL)\n", "        AND (oid.procured_quantity > 0) AND (oid.order_current_status = 'DELIVERED')\n", "    )oid\n", "    JOIN (select * from dwh.fact_sales_order_details od where (od.order_create_dt_ist between current_date - interval '21' day \n", "    and current_date - interval '1' day) )od on od.order_id = oid.order_id\n", "    JOIN (SELECT DISTINCT ipr.product_id, \n", "            case when ipr.item_id is null then ipom_0.item_id else ipr.item_id end as item_id,\n", "            case when ipr.item_id is not null then COALESCE(ipom.multiplier,1) else\n", "                COALESCE(ipom_0.multiplier,1) end AS multiplier, \n", "                                COALESCE(ipom_0.avg_selling_price_ratio,1) as avg_selling_price_ratio\n", "                    FROM lake_rpc.item_product_mapping ipr\n", "            left join dwh.dim_item_product_offer_mapping ipom\n", "                    on  ipr.product_id = ipom.product_id \n", "                    and ipr.item_id = ipom.item_id and ipom.is_current\n", "            left join dwh.dim_item_product_offer_mapping ipom_0\n", "                    on ipr.product_id = ipom_0.product_id and ipom_0.is_current) pl ON pl.product_id = oid.product_id\n", "                    \n", "where item_id IN {item_list}\n", "-- and oid.order_create_dt_ist between current_date - interval '21' day and current_date - interval '1' day\n", "-- and od.order_create_dt_ist between current_date - interval '21' day and current_date - interval '1' day\n", "--and oid.cart_checkout_ts_ist >= current_date - interval '21' day\n", "AND oid.is_internal_order = false AND (oid.order_type NOT LIKE '%%internal%%' OR oid.order_type IS NULL)\n", "        AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED'\n", "\n", "group by 1,2,3,4\n", "), \n", "\n", "store_age as\n", "(select \n", "    fsoid.outlet_id,\n", "    date_diff('day', min(cast(fsoid.cart_checkout_ts_ist as date)), CURRENT_DATE) as store_age_days\n", " from \n", "    dwh.fact_sales_order_item_details fsoid\n", "where \n", "    order_create_dt_ist >= current_date - interval '90' day\n", "group by\n", "    1),\n", "\n", "\n", "opd as(\n", "select\n", "    date(cart_checkout_ts_ist) as date_,\n", "    outlet_id,\n", "    count(distinct order_id) as opd\n", "from\n", "    dwh.fact_sales_order_details sod\n", "where\n", "    order_create_dt_ist >= current_date - interval '21' day\n", "group by\n", "    1,2\n", ")\n", "\n", "select g.*, opd, store_age_days\n", "from gmv  as g \n", "left join store_age as a on a.outlet_id = g.outlet_id\n", "left join opd as b on b.outlet_id = g.outlet_id and b.date_ = g.date_\n", "\"\"\"\n", "\n", "gmv_df = read_sql_query(base_query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "9eb12ee1-5907-4d75-9144-f34837056d3f", "metadata": {"papermill": {"duration": 0.049625, "end_time": "2025-06-19T11:44:50.286433", "exception": false, "start_time": "2025-06-19T11:44:50.236808", "status": "completed"}, "tags": []}, "outputs": [], "source": ["gmv_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "52dfc2d1-32ce-4933-b4fc-eb37d3a4d321", "metadata": {"papermill": {"duration": 0.044208, "end_time": "2025-06-19T11:44:50.371416", "exception": false, "start_time": "2025-06-19T11:44:50.327208", "status": "completed"}, "tags": []}, "outputs": [], "source": ["gmv_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "6fc01ba2-fb5f-411b-b9f5-e6766a9258fb", "metadata": {"papermill": {"duration": 2.066555, "end_time": "2025-06-19T11:44:52.477498", "exception": false, "start_time": "2025-06-19T11:44:50.410943", "status": "completed"}, "tags": []}, "outputs": [], "source": ["gmv_df[[\"outlet_id\", \"item_id\"]] = gmv_df[[\"outlet_id\", \"item_id\"]].fillna(0).astype(int)\n", "gmv_df = gmv_df.merge(mapping_df, on=[\"outlet_id\", \"item_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "ca7f1685-0c93-475c-af8c-cbfb70f90e39", "metadata": {"papermill": {"duration": 4.309448, "end_time": "2025-06-19T11:44:56.825382", "exception": false, "start_time": "2025-06-19T11:44:52.515934", "status": "completed"}, "tags": []}, "outputs": [], "source": ["gmv_df[[\"be_facility_id\"]] = gmv_df[[\"be_facility_id\"]].fillna(0).astype(int)\n", "gmv_df = gmv_df.merge(dts_cpc, on=[\"be_facility_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "ce72ece1-1682-4cba-9158-b25f8a477165", "metadata": {"papermill": {"duration": 0.04421, "end_time": "2025-06-19T11:44:56.909240", "exception": false, "start_time": "2025-06-19T11:44:56.865030", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# age_bins = [0, 15, 30, 60, float(\"inf\")]\n", "# age_labels = [\"0-15 days\", \"15-30 days\", \"30-60 days\", \"60 days +\"]\n", "\n", "\n", "# gmv_df[\"store_age_category\"] = pd.cut(\n", "#     gmv_df[\"store_age_days\"], bins=age_bins, labels=age_labels, right=False\n", "# )\n", "\n", "\n", "# gmv_df_all = gmv_df.copy()\n", "# gmv_df_all[\"store_age_category\"] = \"All\"\n", "\n", "# gmv_df_1 = pd.concat([gmv_df, gmv_df_all])"]}, {"cell_type": "code", "execution_count": null, "id": "2150e09a-08f4-4643-b215-0f4c66827f33", "metadata": {"papermill": {"duration": 0.045096, "end_time": "2025-06-19T11:44:57.005757", "exception": false, "start_time": "2025-06-19T11:44:56.960661", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# del [gmv_df, gmv_df_all]\n", "# gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "5912e5f2-4dee-43e0-9ff6-0a4c94639c7e", "metadata": {"papermill": {"duration": 0.061994, "end_time": "2025-06-19T11:44:57.120860", "exception": false, "start_time": "2025-06-19T11:44:57.058866", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# opd_bins = [0, 800, float(\"inf\")]  # Bins representing 0-800, 800+\n", "# opd_labels = [\"0-800\", \"800+\"]\n", "\n", "\n", "# gmv_df_1[\"opd_category\"] = pd.cut(gmv_df_1[\"opd\"], bins=opd_bins, labels=opd_labels, right=False)\n", "\n", "# gmv_df_1_all = gmv_df_1.copy()\n", "# gmv_df_1_all[\"opd_category\"] = \"All\"\n", "# gmv_df_2 = pd.concat([gmv_df_1, gmv_df_1_all])\n", "\n", "# gmv_df_2 = gmv_df_2.reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "825f838a-d815-4ee5-9ab7-6d678ec0e84b", "metadata": {"papermill": {"duration": 0.04221, "end_time": "2025-06-19T11:44:57.200492", "exception": false, "start_time": "2025-06-19T11:44:57.158282", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# del [gmv_df_1, gmv_df_1_all]\n", "# gc.collect()\n", "\n", "\n", "# gmv_df_2_all = gmv_df_2.copy()\n", "# gmv_df_2_all[\"l2\"] = \"All Perishables\"\n", "# gmv_df_2_all[\"ptype\"] = \"All Perishables\"\n", "\n", "# gmv_df_3 = pd.concat([gmv_df_2, gmv_df_2_all])\n", "\n", "\n", "# del [gmv_df_2, gmv_df_2_all]\n", "# gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "9b4574be-2f26-4b25-bc3d-ce73d25a3b29", "metadata": {"papermill": {"duration": 0.04408, "end_time": "2025-06-19T11:44:57.282117", "exception": false, "start_time": "2025-06-19T11:44:57.238037", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# gmv_df_3 = gmv_df_3.reset_index(drop=True)\n", "# gmv_df_3.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "3bacbd98-5bdd-4544-b88a-2e9e8ff6c110", "metadata": {"papermill": {"duration": 0.042742, "end_time": "2025-06-19T11:44:57.387834", "exception": false, "start_time": "2025-06-19T11:44:57.345092", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# gmv_df_agg = (\n", "#     gmv_df_3.groupby(\n", "#         [\n", "#             \"date_\",\n", "#             \"flag\",\n", "#             \"city\",\n", "#             \"l2\",\n", "#             \"ptype\",\n", "#             \"store_age_category\",\n", "#             \"opd_category\",\n", "#             \"dts_cpc_flag\",\n", "#         ]\n", "#     )\n", "#     .agg({\"gmv\": \"sum\"})\n", "#     .reset_index()\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "9ade1aeb-baa8-4288-9c3d-c05c80391ae6", "metadata": {"papermill": {"duration": 0.041545, "end_time": "2025-06-19T11:44:57.470372", "exception": false, "start_time": "2025-06-19T11:44:57.428827", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# gmv_df_agg.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "5416e37a-13c3-4ebb-a299-b443890ae892", "metadata": {"papermill": {"duration": 0.059889, "end_time": "2025-06-19T11:44:57.570100", "exception": false, "start_time": "2025-06-19T11:44:57.510211", "status": "completed"}, "tags": []}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "91c453cd-d037-4603-b8d5-174cff59c624", "metadata": {"papermill": {"duration": 0.072807, "end_time": "2025-06-19T11:44:57.683757", "exception": false, "start_time": "2025-06-19T11:44:57.610950", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def prepare_gmv_data(df):\n", "    df = df.copy()  # only one copy\n", "\n", "    # Create bins\n", "    age_bins = [0, 15, 30, 60, float(\"inf\")]\n", "    age_labels = [\"0-15 days\", \"15-30 days\", \"30-60 days\", \"60 days +\"]\n", "    df[\"store_age_category\"] = pd.cut(\n", "        df[\"store_age_days\"], bins=age_bins, labels=age_labels, right=False\n", "    )\n", "\n", "    opd_bins = [0, 800, float(\"inf\")]\n", "    opd_labels = [\"0-800\", \"800+\"]\n", "    df[\"opd_category\"] = pd.cut(df[\"opd\"], bins=opd_bins, labels=opd_labels, right=False)\n", "\n", "    # Duplicate with \"All\" labels using assign\n", "    df_all_age = df.assign(store_age_category=\"All\")\n", "    df_all_opd = df.assign(opd_category=\"All\")\n", "    df_all_both = df.assign(store_age_category=\"All\", opd_category=\"All\")\n", "\n", "    # Concatenate in layers\n", "    df = pd.concat([df, df_all_age, df_all_opd, df_all_both], ignore_index=True)\n", "\n", "    # Clean up intermediates to save memory\n", "    del [df_all_age, df_all_opd, df_all_both]\n", "    gc.collect()\n", "\n", "    # Add \"All Perishables\"\n", "    df_all_ptype = df.assign(l2=\"All Perishables\", ptype=\"All Perishables\")\n", "    df = pd.concat([df, df_all_ptype], ignore_index=True)\n", "\n", "    # Final cleanup\n", "    del df_all_ptype\n", "    gc.collect()\n", "\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "04e0597a-e0a6-4090-a194-507b3a3c410f", "metadata": {"papermill": {"duration": 39.470018, "end_time": "2025-06-19T11:45:37.192211", "exception": false, "start_time": "2025-06-19T11:44:57.722193", "status": "completed"}, "tags": []}, "outputs": [], "source": ["gmv_df_optimized = prepare_gmv_data(gmv_df)\n", "\n", "gmv_df_agg = (\n", "    gmv_df_optimized.groupby(\n", "        [\n", "            \"date_\",\n", "            \"flag\",\n", "            \"city\",\n", "            \"l2\",\n", "            \"ptype\",\n", "            \"store_age_category\",\n", "            \"opd_category\",\n", "            \"dts_cpc_flag\",\n", "        ]\n", "    )[\"gmv\"]\n", "    .sum()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fb3d4915-e55b-4c96-b573-c7f3623b4816", "metadata": {"papermill": {"duration": 0.065846, "end_time": "2025-06-19T11:45:37.348711", "exception": false, "start_time": "2025-06-19T11:45:37.282865", "status": "completed"}, "tags": []}, "outputs": [], "source": ["gmv_df_agg.head()"]}, {"cell_type": "code", "execution_count": null, "id": "dd0b1585-6f2f-4dcc-8c21-e809d3a7fd5a", "metadata": {"papermill": {"duration": 0.050373, "end_time": "2025-06-19T11:45:37.440476", "exception": false, "start_time": "2025-06-19T11:45:37.390103", "status": "completed"}, "tags": []}, "outputs": [], "source": ["data = {\n", "    \"l2\": [\n", "        \"Paneer\",\n", "        \"Lassi & Chaach\",\n", "        \"Curd\",\n", "        \"Batter\",\n", "        \"Regular Breads\",\n", "        \"Eggs\",\n", "        \"Speciality Milk\",\n", "        \"Yogurt\",\n", "        \"Tofu\",\n", "        \"Buns, Pavs & Pizza Base\",\n", "        \"Speciality Breads\",\n", "        \"Meats\",\n", "        \"Fresh Milk\",\n", "        \"All Perishables\",\n", "    ],\n", "    \"Block\": [\"K\", \"K\", \"K\", \"K\", \"S\", \"S2\", \"K\", \"S\", \"K\", \"S\", \"S\", \"S2\", \"K\", \"all\"],\n", "}\n", "\n", "cat_div = pd.DataFrame(data)\n", "cat_div"]}, {"cell_type": "code", "execution_count": null, "id": "7ce3628f-ec46-4191-92dd-4be221938ef8", "metadata": {"papermill": {"duration": 0.084693, "end_time": "2025-06-19T11:45:37.563658", "exception": false, "start_time": "2025-06-19T11:45:37.478965", "status": "completed"}, "tags": []}, "outputs": [], "source": ["gmv_df_agg.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "17891ecc-f669-4da2-8b98-0b2bdcbfe947", "metadata": {"papermill": {"duration": 0.150297, "end_time": "2025-06-19T11:45:37.783936", "exception": false, "start_time": "2025-06-19T11:45:37.633639", "status": "completed"}, "tags": []}, "outputs": [], "source": ["gmv_df_agg = gmv_df_agg.merge(cat_div, on=[\"l2\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "62a5ae8a-574c-44bf-85e0-d42a1715fd8c", "metadata": {"papermill": {"duration": 0.05089, "end_time": "2025-06-19T11:45:37.875296", "exception": false, "start_time": "2025-06-19T11:45:37.824406", "status": "completed"}, "tags": []}, "outputs": [], "source": ["gmv_df_agg.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "edc795f6-6836-4192-ba7b-e87dc932d82f", "metadata": {"papermill": {"duration": 0.045795, "end_time": "2025-06-19T11:45:37.965436", "exception": false, "start_time": "2025-06-19T11:45:37.919641", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# x = gmv_df_agg[gmv_df_agg['l2']== \"All Perishables\"]\n", "# x.to_csv('ALL.CSV')"]}, {"cell_type": "code", "execution_count": null, "id": "76433dd0-db37-4b88-b234-556576a0b2d1", "metadata": {"papermill": {"duration": 0.224647, "end_time": "2025-06-19T11:45:38.229042", "exception": false, "start_time": "2025-06-19T11:45:38.004395", "status": "completed"}, "tags": []}, "outputs": [], "source": ["gmv_df_agg_K = gmv_df_agg[gmv_df_agg[\"Block\"].isin([\"K\", \"all\"])].drop(columns=\"Block\")\n", "gmv_df_agg_S = gmv_df_agg[gmv_df_agg[\"Block\"].isin([\"S\", \"all\"])].drop(columns=\"Block\")\n", "gmv_df_agg_S2 = gmv_df_agg[gmv_df_agg[\"Block\"].isin([\"S2\", \"all\"])].drop(columns=\"Block\")"]}, {"cell_type": "markdown", "id": "95131034-4bb3-40d1-901f-7ec3aeb54bab", "metadata": {"papermill": {"duration": 0.04016, "end_time": "2025-06-19T11:45:38.324294", "exception": false, "start_time": "2025-06-19T11:45:38.284134", "status": "completed"}, "tags": []}, "source": ["### GMV Data Upload"]}, {"cell_type": "code", "execution_count": null, "id": "472c04d0-8eec-4719-b8a5-b41a63b7e5cf", "metadata": {"papermill": {"duration": 202.648513, "end_time": "2025-06-19T11:49:01.027798", "exception": false, "start_time": "2025-06-19T11:45:38.379285", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def try_upload_to_sheets(df, sheet_id, sheet_name, retries=3):\n", "    for attempt in range(retries):\n", "        try:\n", "            pb.to_sheets(df, sheet_id, sheet_name)\n", "            print(f\"Successfully uploaded {sheet_name} on attempt {attempt + 1}\")\n", "            return  # Exit the function once successful\n", "        except Exception as e:\n", "            print(f\"Attempt {attempt + 1} failed for {sheet_name}: {e}\")\n", "            if attempt == retries - 1:\n", "                print(f\"All attempts failed for {sheet_name}\")\n", "\n", "\n", "try_upload_to_sheets(gmv_df_agg_K, \"1pnVcG2BOLs4K_Q54HthBYUi985nC9akYbBowgN61N0g\", \"gmv\")\n", "try_upload_to_sheets(gmv_df_agg_S, \"1ADK1tZNlIHL1pCmK6QdCigWsAg8DG3EnilYYWI3XFfQ\", \"gmv\")\n", "try_upload_to_sheets(gmv_df_agg_S2, \"1LJ6hI2yWUphvEMBVKCAqGqCzqI1wLkPQ6vp3xcxi-oU\", \"gmv\")"]}, {"cell_type": "code", "execution_count": null, "id": "c4eeb629-c5d9-4a95-87f8-f0711cce08da", "metadata": {"papermill": {"duration": 0.051656, "end_time": "2025-06-19T11:49:01.169351", "exception": false, "start_time": "2025-06-19T11:49:01.117695", "status": "completed"}, "tags": []}, "outputs": [], "source": ["gmv_df_agg_S.head()"]}, {"cell_type": "code", "execution_count": null, "id": "613df540-53c3-4a6d-bef5-141fe50fb5d1", "metadata": {"papermill": {"duration": 0.047893, "end_time": "2025-06-19T11:49:01.276459", "exception": false, "start_time": "2025-06-19T11:49:01.228566", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# gmv_df_agg_K.to_csv('gmv_df_agg_K.csv',index=False)\n", "# gmv_df_agg_S.to_csv('gmv_df_agg_S.csv',index=False)\n", "# gmv_df_agg_S2.to_csv('gmv_df_agg_S2.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "b59266de-dc6f-4c86-8ac3-c14e7eefa71a", "metadata": {"papermill": {"duration": 0.117663, "end_time": "2025-06-19T11:49:01.435537", "exception": false, "start_time": "2025-06-19T11:49:01.317874", "status": "completed"}, "tags": []}, "outputs": [], "source": ["del [gmv_df_agg, gmv_df_agg_K, gmv_df_agg_S, gmv_df_agg_S2]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "f65ba200-e41a-43df-bfea-d13203c72309", "metadata": {"papermill": {"duration": 0.059832, "end_time": "2025-06-19T11:49:01.554653", "exception": false, "start_time": "2025-06-19T11:49:01.494821", "status": "completed"}, "tags": []}, "source": ["## Dump , OPD & Store age"]}, {"cell_type": "code", "execution_count": null, "id": "257f1a52-4369-4c04-af20-b48f64d5b110", "metadata": {"papermill": {"duration": 57.977356, "end_time": "2025-06-19T11:49:59.573892", "exception": false, "start_time": "2025-06-19T11:49:01.596536", "status": "completed"}, "tags": []}, "outputs": [], "source": ["query = f\"\"\"\n", "\n", "with store_age as\n", "(select \n", "    fsoid.outlet_id,\n", "    date_diff('day', min(cast(fsoid.cart_checkout_ts_ist as date)), CURRENT_DATE) as store_age_days\n", " from \n", "    dwh.fact_sales_order_item_details fsoid\n", "where \n", "    order_create_dt_ist >= current_date - interval '600' day\n", "group by\n", "    1),\n", "\n", "\n", "opd as(\n", "select\n", "    date(cart_checkout_ts_ist) as date_,\n", "    outlet_id,\n", "    count(distinct order_id) as opd\n", "from\n", "    dwh.fact_sales_order_details sod\n", "where\n", "    order_create_dt_ist >= current_date - interval '21' day\n", "group by\n", "    1,2\n", "),    \n", "\n", "dump_ as (\n", "select\n", "    date(dump_date_ist) as date_,\n", "    0 as placeholder,\n", "    dr.outlet_id,\n", "    dr.outlet_name,\n", "    store_age_days,\n", "    opd.opd,\n", "    case when outlet_name in ('Super Store Jaipur F&V CPC') then 'backend' else facility_type end as facility_type,\n", "    dr.item_id,\n", "    CASE    \n", "        WHEN dump_date_ist between current_date- interval '7' day and current_date- interval '1' day then 'L7'\n", "        WHEN dump_date_ist between current_date- interval '14' day and current_date- interval '8' day then 'L14'\n", "        WHEN dump_date_ist between current_date- interval '21' day and current_date - interval '15' day then 'L21'\n", "    else 'Nil'\n", "    END AS flag,\n", "    sum(damage_dump) as damage_dump,\n", "    sum(damage_dump_quan) as damage_dump_quan,\n", "    sum(rtv) as rtv,\n", "    sum(rtv_quan) as rtv_quan,\n", "    sum(bad_stock_expired) as bad_stock_expired,\n", "    sum(bad_stock_expired_quan) as bad_stock_expired_quan,\n", "    sum(near_expiry_dump) as near_expiry_dump,\n", "    sum(near_expiry_dump_quan) as near_expiry_dump_quan,\n", "    sum(weight_short_dump) as weight_short_dump,\n", "    sum(weight_short_dump_quan) as weight_short_dump_quan,\n", "    sum(not_received_dump) as not_received_dump,\n", "    sum(not_received_dump_quan) as not_received_dump_quan,\n", "    sum(customer_dump) as customer_dump,\n", "    sum(customer_dump_quan) as customer_dump_quan,\n", "    sum(pilferage) as pilferage,\n", "    sum(pilferage_quan) as pilferage_quan,\n", "    sum(lm_pilferage) as lm_pilferage,\n", "    sum(lm_pilferage_quan) as lm_pilferage_quan,\n", "    sum(cn_not_usable) as cn_not_usable,\n", "    sum(cn_not_usable_quan) as cn_not_usable_quan,\n", "    SUM(tot_dump_quan) as dump_quantity, \n", "    SUM(tot_dump) as dump_value\n", "\n", "from \n", "    supply_etls.fnv_milk_perishables_dump_raw dr\n", "inner join \n", "    rpc.item_category_details icd \n", "    on icd.item_id = dr.item_id \n", "    and icd.l2_id IN {l2_id_list}\n", "    and icd.item_id IN {item_list}\n", "\n", "left join\n", "    opd\n", "    on opd.outlet_id = dr.outlet_id\n", "    and opd.date_ = date(dr.dump_date_ist)\n", "left join\n", "    store_age ss\n", "    on ss.outlet_id = dr.outlet_id\n", "where \n", "    dump_date_ist >= current_date - interval '21' day\n", "group by\n", "    1,2,3,4,5,6,7,8,9)\n", "\n", "select \n", "    * \n", "from \n", "    dump_\n", "\"\"\"\n", "query = read_sql_query(query, trino)\n", "query.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b9314990-b68f-4ffc-98f6-0bf9463dbdd4", "metadata": {"papermill": {"duration": 0.071618, "end_time": "2025-06-19T11:49:59.703703", "exception": false, "start_time": "2025-06-19T11:49:59.632085", "status": "completed"}, "tags": []}, "outputs": [], "source": ["query.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c98f23a4-182a-490a-bd54-1ff56d812774", "metadata": {"papermill": {"duration": 0.113165, "end_time": "2025-06-19T11:49:59.858905", "exception": false, "start_time": "2025-06-19T11:49:59.745740", "status": "completed"}, "tags": []}, "outputs": [], "source": ["frontend_df = query[query[\"facility_type\"] == \"frontend\"]"]}, {"cell_type": "code", "execution_count": null, "id": "3c2b0bf3-9752-4322-aca2-578e6b9910d9", "metadata": {"papermill": {"duration": 0.049722, "end_time": "2025-06-19T11:49:59.956756", "exception": false, "start_time": "2025-06-19T11:49:59.907034", "status": "completed"}, "tags": []}, "outputs": [], "source": ["frontend_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "00db4597-ef5b-433a-8e18-59ee2542f2ef", "metadata": {"papermill": {"duration": 0.078971, "end_time": "2025-06-19T11:50:00.078320", "exception": false, "start_time": "2025-06-19T11:49:59.999349", "status": "completed"}, "tags": []}, "outputs": [], "source": ["df = frontend_df.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "a5c1ba38-d920-44be-a308-fb14e14522d6", "metadata": {"papermill": {"duration": 0.563157, "end_time": "2025-06-19T11:50:00.706891", "exception": false, "start_time": "2025-06-19T11:50:00.143734", "status": "completed"}, "tags": []}, "outputs": [], "source": ["df = df.merge(mapping_df, on=[\"outlet_id\", \"item_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "68921486-6391-4139-8473-ecbb7dd7ca02", "metadata": {"papermill": {"duration": 4.999786, "end_time": "2025-06-19T11:50:05.781022", "exception": false, "start_time": "2025-06-19T11:50:00.781236", "status": "completed"}, "tags": []}, "outputs": [], "source": ["age_bins = [0, 15, 30, 60, float(\"inf\")]\n", "age_labels = [\"0-15 days\", \"15-30 days\", \"30-60 days\", \"60 days +\"]\n", "\n", "\n", "df[\"store_age_category\"] = pd.cut(\n", "    df[\"store_age_days\"], bins=age_bins, labels=age_labels, right=False\n", ")\n", "\n", "\n", "frontend_df_all = df.copy()\n", "frontend_df_all[\"store_age_category\"] = \"All\"\n", "\n", "final_df_1 = pd.concat([df, frontend_df_all])\n", "\n", "\n", "opd_bins = [0, 800, float(\"inf\")]  # Bins representing 0-800, 800+\n", "opd_labels = [\"0-800\", \"800+\"]\n", "\n", "\n", "final_df_1[\"opd_category\"] = pd.cut(\n", "    final_df_1[\"opd\"], bins=opd_bins, labels=opd_labels, right=False\n", ")\n", "final_df_1_all = final_df_1.copy()\n", "final_df_1_all[\"opd_category\"] = \"All\"\n", "final_df_2 = pd.concat([final_df_1, final_df_1_all])\n", "\n", "\n", "final_df_2_all = final_df_2.copy()\n", "final_df_2_all[\"l2\"] = \"All Perishables\"\n", "final_df_2_all[\"ptype\"] = \"All Perishables\"\n", "\n", "final_df_3 = pd.concat([final_df_2, final_df_2_all])\n", "\n", "\n", "final_df_3 = final_df_3.reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "b19534a9-e884-4c53-948e-df3a7548edae", "metadata": {"papermill": {"duration": 0.068061, "end_time": "2025-06-19T11:50:05.938379", "exception": false, "start_time": "2025-06-19T11:50:05.870318", "status": "completed"}, "tags": []}, "outputs": [], "source": ["final_df_3.shape"]}, {"cell_type": "code", "execution_count": null, "id": "5321f0c2-39c7-4764-94d4-9acce03400dc", "metadata": {"papermill": {"duration": 0.490278, "end_time": "2025-06-19T11:50:06.469785", "exception": false, "start_time": "2025-06-19T11:50:05.979507", "status": "completed"}, "tags": []}, "outputs": [], "source": ["del [frontend_df, df, frontend_df_all, final_df_1, final_df_1_all, final_df_2, final_df_2_all]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "5bf0a55b-5d16-48f5-a777-0a6149f1f25d", "metadata": {"papermill": {"duration": 0.104996, "end_time": "2025-06-19T11:50:06.621234", "exception": false, "start_time": "2025-06-19T11:50:06.516238", "status": "completed"}, "tags": []}, "outputs": [], "source": ["final_df_3.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "746f576e-7a8a-40f6-94dd-c5195dac1d6e", "metadata": {"papermill": {"duration": 1.641951, "end_time": "2025-06-19T11:50:08.317180", "exception": false, "start_time": "2025-06-19T11:50:06.675229", "status": "completed"}, "tags": []}, "outputs": [], "source": ["result_df = (\n", "    final_df_3.groupby(\n", "        [\n", "            \"date_\",\n", "            \"city\",\n", "            \"placeholder\",\n", "            \"outlet_id\",\n", "            \"outlet_name\",\n", "            \"l2\",\n", "            \"ptype\",\n", "            \"opd_category\",\n", "            \"store_age_category\",\n", "        ]\n", "    )\n", "    .size()\n", "    .reset_index(name=\"count\")\n", ")\n", "result_df"]}, {"cell_type": "code", "execution_count": null, "id": "5ca5a2a4-3944-4a09-81ee-ae86340d5203", "metadata": {"papermill": {"duration": 0.054784, "end_time": "2025-06-19T11:50:08.454423", "exception": false, "start_time": "2025-06-19T11:50:08.399639", "status": "completed"}, "tags": []}, "outputs": [], "source": ["result_df[\"count\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "6111fd5b-aa13-4c91-9b63-73a093c7f38e", "metadata": {"papermill": {"duration": 0.423391, "end_time": "2025-06-19T11:50:08.958636", "exception": false, "start_time": "2025-06-19T11:50:08.535245", "status": "completed"}, "tags": []}, "outputs": [], "source": ["final_df_3[[\"be_facility_id\", \"be_outlet_id\"]] = final_df_3[\n", "    [\"be_facility_id\", \"be_outlet_id\"]\n", "].fillna(0)\n", "final_df_3[[\"be_facility_id\", \"be_outlet_id\"]] = final_df_3[\n", "    [\"be_facility_id\", \"be_outlet_id\"]\n", "].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "3d5e7084-5d38-4347-8833-90b4f652d078", "metadata": {"papermill": {"duration": 5.102973, "end_time": "2025-06-19T11:50:14.149358", "exception": false, "start_time": "2025-06-19T11:50:09.046385", "status": "completed"}, "tags": []}, "outputs": [], "source": ["final_df_3 = final_df_3.merge(dts_cpc, on=[\"be_facility_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "142e9e93-2d3d-4393-9057-2a411857e4d9", "metadata": {"papermill": {"duration": 3.483308, "end_time": "2025-06-19T11:50:17.675551", "exception": false, "start_time": "2025-06-19T11:50:14.192243", "status": "completed"}, "tags": []}, "outputs": [], "source": ["columns_to_sum = [\n", "    \"damage_dump\",\n", "    \"damage_dump_quan\",\n", "    \"rtv\",\n", "    \"rtv_quan\",\n", "    \"bad_stock_expired\",\n", "    \"bad_stock_expired_quan\",\n", "    \"near_expiry_dump\",\n", "    \"near_expiry_dump_quan\",\n", "    \"weight_short_dump\",\n", "    \"weight_short_dump_quan\",\n", "    \"not_received_dump\",\n", "    \"not_received_dump_quan\",\n", "    \"customer_dump\",\n", "    \"customer_dump_quan\",\n", "    \"pilferage\",\n", "    \"pilferage_quan\",\n", "    \"lm_pilferage\",\n", "    \"lm_pilferage_quan\",\n", "    \"cn_not_usable\",\n", "    \"cn_not_usable_quan\",\n", "    \"dump_quantity\",\n", "    \"dump_value\",\n", "]\n", "\n", "aggregated_df = (\n", "    final_df_3.groupby(\n", "        [\n", "            \"date_\",\n", "            \"city\",\n", "            \"placeholder\",\n", "            \"facility_type\",\n", "            \"l2\",\n", "            \"ptype\",\n", "            \"store_age_category\",\n", "            \"opd_category\",\n", "            \"dts_cpc_flag\",\n", "            \"flag\",\n", "        ]\n", "    )[columns_to_sum]\n", "    .sum()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "11b9bd1b-9896-46aa-9858-d5df79b02cac", "metadata": {"papermill": {"duration": 0.079384, "end_time": "2025-06-19T11:50:17.823001", "exception": false, "start_time": "2025-06-19T11:50:17.743617", "status": "completed"}, "tags": []}, "outputs": [], "source": ["aggregated_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e8012fe2-8f1f-43e5-9410-051e9da19440", "metadata": {"papermill": {"duration": 0.14257, "end_time": "2025-06-19T11:50:18.039657", "exception": false, "start_time": "2025-06-19T11:50:17.897087", "status": "completed"}, "tags": []}, "outputs": [], "source": ["aggregated_df = aggregated_df[\n", "    [\n", "        \"date_\",\n", "        \"city\",\n", "        \"placeholder\",\n", "        \"facility_type\",\n", "        \"l2\",\n", "        \"ptype\",\n", "        \"store_age_category\",\n", "        \"opd_category\",\n", "        \"dts_cpc_flag\",\n", "        \"flag\",\n", "        \"damage_dump\",\n", "        \"damage_dump_quan\",\n", "        \"rtv\",\n", "        \"rtv_quan\",\n", "        \"bad_stock_expired\",\n", "        \"bad_stock_expired_quan\",\n", "        \"near_expiry_dump\",\n", "        \"near_expiry_dump_quan\",\n", "        \"weight_short_dump\",\n", "        \"weight_short_dump_quan\",\n", "        \"not_received_dump\",\n", "        \"not_received_dump_quan\",\n", "        \"customer_dump\",\n", "        \"customer_dump_quan\",\n", "        \"pilferage\",\n", "        \"pilferage_quan\",\n", "        \"lm_pilferage\",\n", "        \"lm_pilferage_quan\",\n", "        \"cn_not_usable\",\n", "        \"cn_not_usable_quan\",\n", "        \"dump_quantity\",\n", "        \"dump_value\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "4fc81878-75be-431a-9ed9-f598fedbaf25", "metadata": {"papermill": {"duration": 0.104235, "end_time": "2025-06-19T11:50:18.223541", "exception": false, "start_time": "2025-06-19T11:50:18.119306", "status": "completed"}, "tags": []}, "outputs": [], "source": ["aggregated_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a5adc0a7-d302-49d5-af18-ba513bea2697", "metadata": {"papermill": {"duration": 0.138209, "end_time": "2025-06-19T11:50:18.426823", "exception": false, "start_time": "2025-06-19T11:50:18.288614", "status": "completed"}, "tags": []}, "outputs": [], "source": ["aggregated_df = aggregated_df.merge(cat_div, on=[\"l2\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "2c63cb97-7dc5-4637-a33b-501f1ebe207e", "metadata": {"papermill": {"duration": 0.083341, "end_time": "2025-06-19T11:50:18.554858", "exception": false, "start_time": "2025-06-19T11:50:18.471517", "status": "completed"}, "tags": []}, "outputs": [], "source": ["aggregated_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2e5c9d2d-91f5-47c8-bfcd-edd50d8fbb18", "metadata": {"papermill": {"duration": 0.214526, "end_time": "2025-06-19T11:50:18.816809", "exception": false, "start_time": "2025-06-19T11:50:18.602283", "status": "completed"}, "tags": []}, "outputs": [], "source": ["aggregated_df_K = aggregated_df[aggregated_df[\"Block\"].isin([\"K\", \"all\"])].drop(columns=\"Block\")\n", "aggregated_df_S = aggregated_df[aggregated_df[\"Block\"].isin([\"S\", \"all\"])].drop(columns=\"Block\")\n", "aggregated_df_S2 = aggregated_df[aggregated_df[\"Block\"].isin([\"S2\", \"all\"])].drop(columns=\"Block\")"]}, {"cell_type": "code", "execution_count": null, "id": "49a8a3bf-5b3b-4266-b5f4-55ede3036652", "metadata": {"papermill": {"duration": 0.058821, "end_time": "2025-06-19T11:50:18.955333", "exception": false, "start_time": "2025-06-19T11:50:18.896512", "status": "completed"}, "tags": []}, "outputs": [], "source": ["aggregated_df_K.head()"]}, {"cell_type": "code", "execution_count": null, "id": "1fb7113f-0850-434f-a8bf-d35c2afe5828", "metadata": {"papermill": {"duration": 199.121356, "end_time": "2025-06-19T11:53:38.169015", "exception": false, "start_time": "2025-06-19T11:50:19.047659", "status": "completed"}, "tags": []}, "outputs": [], "source": ["try_upload_to_sheets(aggregated_df_K, \"1pnVcG2BOLs4K_Q54HthBYUi985nC9akYbBowgN61N0g\", \"fe\")\n", "try_upload_to_sheets(aggregated_df_S, \"1ADK1tZNlIHL1pCmK6QdCigWsAg8DG3EnilYYWI3XFfQ\", \"fe\")\n", "try_upload_to_sheets(aggregated_df_S2, \"1LJ6hI2yWUphvEMBVKCAqGqCzqI1wLkPQ6vp3xcxi-oU\", \"fe\")"]}, {"cell_type": "code", "execution_count": null, "id": "9978ecf1-0ff0-4f08-93da-6ece4007d50b", "metadata": {"papermill": {"duration": 0.072661, "end_time": "2025-06-19T11:53:38.371539", "exception": false, "start_time": "2025-06-19T11:53:38.298878", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# aggregated_df_K.to_csv('aggregated_df_K.csv')\n", "# aggregated_df_S.to_csv('aggregated_df_S.csv')\n", "# aggregated_df_S2.to_csv('aggregated_df_S2.csv')"]}, {"cell_type": "markdown", "id": "4e8d0d13-8d7d-47f7-b5f0-d701202a77e7", "metadata": {"papermill": {"duration": 0.078829, "end_time": "2025-06-19T11:53:38.540186", "exception": false, "start_time": "2025-06-19T11:53:38.461357", "status": "completed"}, "tags": []}, "source": ["#### Transfer Loss df"]}, {"cell_type": "code", "execution_count": null, "id": "bf774455-9175-455a-8453-db525d0660dd", "metadata": {"papermill": {"duration": 202.29732, "end_time": "2025-06-19T11:57:00.896154", "exception": false, "start_time": "2025-06-19T11:53:38.598834", "status": "completed"}, "tags": []}, "outputs": [], "source": ["query = f\"\"\"\n", "with perishable_skus as (SELECT DISTINCT rpc.item_id\n", "        from  lake_rpc.product_product rpc \n", "        JOIN (\n", "                SELECT item_id, MAX(id) AS id\n", "                FROM lake_rpc.product_product\n", "                WHERE active = 1 and approved = 1\n", "                GROUP BY 1\n", "            ) b ON rpc.item_id = b.item_id\n", "     JOIN rpc.item_category_details icd on icd.item_id = rpc.item_id\n", "     where icd.l2_id in (31,116,198,1097,1956,2633,1425, 1185, 949, 950, 1389, 1778, 138, 1091, 1093, 1094, 63,1367,1369)),\n", "     \n", " hp_wh as (SELECT\n", "    CAST(target_delivery_date + interval '330' minute AS timestamp)  as targetdeliverydate,\n", "    warehouse_code as warehousecode ,\n", "    buyer_order_request_id as id,\n", "    cast(coalesce(order_reference.hp_order_references[1].hp_order_id,0) as bigint) AS orderid,\n", "    status_details[1] AS status\n", "FROM zomato.dynamodb.prod_hp_order_service\n", "where type = 'BLINKIT'),\n", "\n", "po_base as \n", "(select distinct date(o.target_delivery_date+interval'330'minute) as date_,\n", "    ipom.outlet_id as warehouse_id,\n", "    warehouse_code,\n", "    po.po_number as po_number,\n", "    poid.outlet_id as outlet_id,\n", "    pi.item_id as item_id,\n", "    o.id,\n", "    SUM(oi.ordered_quantity*oi.weight_per_packet) ordered_tonnage,\n", "    SUM(oi.picked_quantity*oi.weight_per_packet) picked_tonnage,\n", "    sum(oi.picked_quantity *  (asno.cost_price * (1 + ((asno.cgst_value + asno.sgst_value+ asno.igst_value + asno.cess_value + asno.additional_cess_value)/10))) ) as picked_value,\n", "    MAX(oi.ordered_quantity) ordered_quantity,\n", "   MAX(oi.picked_quantity) picked_quantity,\n", "   MAX(units_ordered) as units_ordered,\n", "   MAX(grn.quan) as grn_quantity,\n", "      SUM(units_ordered* (asno.cost_price * (1 + ((asno.cgst_value + asno.sgst_value+ asno.igst_value + asno.cess_value + asno.additional_cess_value)/10))) ) as po_value,\n", "   SUM(grn.quan* (asno.cost_price * (1 + ((asno.cgst_value + asno.sgst_value+ asno.igst_value + asno.cess_value + asno.additional_cess_value)/10))) ) as grn_valuee\n", "from zomato.hp_wms.orders o\n", "    left join zomato.hp_wms.order_item oi\n", "    on o.id=oi.order_id\n", "left join hp_wh bor on bor.orderid = o.id\n", "left join po.edi_integration_partner_purchase_order_details poid on bor.id = poid.partner_order_id\n", "LEFT JOIN po.edi_integration_partner_item_mapping pi ON CAST(pi.partner_item_id as int) = oi.product_number and pi.active = true\n", "left join po.purchase_order_items poi on poid.po_id= poi.po_id and poi.item_id = pi.item_id\n", "left join po.purchase_order po on po.id=poi.po_id\n", "left join (select po_id, item_id,landing_price, sum(quantity) quan, max(created_at+interval '330' minute) as max_grn_time from po.po_grn grn\n", "            where insert_ds_ist >= cast(current_date - interval '30' day as varchar)\n", "            group by 1,2,3) grn\n", "            on  grn.item_id = poi.item_id and grn.po_id = poi.po_id\n", "INNER JOIN po.edi_integration_partner_outlet_mapping ipom ON lower(ipom.partner_outlet_id) = lower(o.warehouse_code) AND ipom.active\n", "Inner join perishable_skus psku on psku.item_id = poi.item_id\n", " left join (select po_number,asn_id, max(updated_at) from po.edi_integration_partner_po_invoice_mapping group by 1,2) invo  on invo.po_number = po.po_number\n", "left join po.edi_integration_partner_invoice_item_details asno on asno.asn_id = invo.asn_id and asno.item_id =poi.item_id and asno.insert_ds_ist >= cast(current_date - interval '30' day as varchar)\n", "where o.dt>'2022111'\n", "    and oi.dt>'2022111'\n", "and date(o.target_delivery_date+interval'330'minute) >= current_date - interval '30' day\n", "    and lower(o.order_status)!='cancelled'\n", "    and o.order_tag!='DIRECT_DELIVERY'\n", " group by 1,2,3,4,5,6,7),\n", " \n", "dn_base as (\n", "select \n", "    d1.po_number, \n", "    ppp.item_id, \n", "    d1.created_at,\n", "    Case when c.name in ('Damaged') then 'Damage' \n", "    when c.name in ( 'STO Short Quantity (Source)','STO Missing (Transport)',\n", "    'Item not in ESTO','Rcpt Qty < Invoice Qty','STO Short Quantity (Transport)','STO Missing (Source)') then 'Short'\n", "    when c.name in ('Shelf Life guidelines breached') then 'NTE'\n", "    else 'Others' end as reason, \n", "    MAX(b.quantity) as dn_quantity,\n", "    sum(b.quantity* (asno.cost_price * (1 + ((asno.cgst_value + asno.sgst_value+ asno.igst_value + asno.cess_value + asno.additional_cess_value)/10))) ) as dn_value\n", "from\n", "    pos.discrepancy_note d1 \n", "left join \n", "    pos.discrepancy_note_product_detail b on d1.id=b.dn_id_id \n", "inner join \n", "    (select distinct item_id, variant_id from rpc.product_product \n", "where item_id in (Select distinct item_id from perishable_skus)) ppp on b.variant_id  = ppp.variant_id\n", "left join \n", "    pos.discrepancy_reason c on b.reason_code=c.id\n", "left join \n", "    po.purchase_order po on po.po_number= d1.po_number\n", "left join \n", "    po.purchase_order_items poi on po.id= poi.po_id and poi.item_id = ppp.item_id\n", "left join \n", "    (select po_number,asn_id,  max(updated_at) from po.edi_integration_partner_po_invoice_mapping group by 1,2) invo  on invo.po_number = po.po_number\n", "left join \n", "    po.edi_integration_partner_invoice_item_details asno on asno.asn_id = invo.asn_id and asno.item_id =poi.item_id \n", "    and asno.insert_ds_ist >= cast(current_date - interval '30' day as varchar)\n", "\n", "where d1.po_number in (select distinct po_number from po_base) group by 1,2,3,4),\n", "\n", "\n", "\n", "reinventorisation_base as \n", "(with base as (\n", "                select outlet_id, warehouse_code,po_number,item_id,tdd,\n", "                sum(dispatched_value) dispatched_value,\n", "                sum(issue_value) as issue_value,\n", "                sum(quantity_dispatched) as quantity_dispatched,\n", "                sum(dispatched_value) as dispatched_value,\n", "                sum(warehouse_recieved_value) as warehouse_recieved_value,\n", "                sum(warehouse_reusable_value) as warehouse_reusable_value,\n", "                sum(issue_units) as issue_units,\n", "                sum(warehouse_recieved_qty) as warehouse_recieved_qty, \n", "                sum(warehouse_usable_qty) as warehouse_usable_qty\n", "                from\n", "                (select outlet_id, warehouse_code, po_number,item_id,tdd, quantity_dispatched, issue_units,dispatched_value, issue_value\n", "                , case when warehouse_recieved_qty>quantity_dispatched then quantity_dispatched else warehouse_recieved_qty end as warehouse_recieved_qty\n", "                , case when warehouse_usable_qty>quantity_dispatched then quantity_dispatched else warehouse_usable_qty end as warehouse_usable_qty\n", "                , case when warehouse_recieved_value>dispatched_value then dispatched_value else warehouse_recieved_value end as warehouse_recieved_value\n", "                , case when warehouse_reusable_value>dispatched_value then dispatched_value else warehouse_reusable_value end as warehouse_reusable_value\n", "                from\n", "                (\n", "                select ipom.outlet_id, oo.warehouse_code,po.po_number as po_number,pim.item_id as item_id,\n", "                date(o.target_delivery_date+interval'330'minute) as tdd, \n", "                max(op.quantity_dispatched) as quantity_dispatched,\n", "                sum(op.quantity_dispatched * (asno.cost_price * (1 + ((asno.cgst_value + asno.sgst_value+ asno.igst_value + asno.cess_value + asno.additional_cess_value)/10))) ) as dispatched_value,\n", "                MAX(pi.issue_in_quantity/op.sub_uom_count) as issue_units,\n", "                sum(((pi.issue_in_quantity/op.sub_uom_count)*asno.cost_price * (1 + ((asno.cgst_value + asno.sgst_value+ asno.igst_value + asno.cess_value + asno.additional_cess_value)/10))) ) as issue_value,\n", "                MAX(pi.warehouse_received_qty) as warehouse_recieved_qty, \n", "                MAX(pi.warehouse_reusable_qty) as warehouse_usable_qty,\n", "                sum(pi.warehouse_received_qty* (asno.cost_price * (1 + ((asno.cgst_value + asno.sgst_value+ asno.igst_value + asno.cess_value + asno.additional_cess_value)/10))) ) as warehouse_recieved_value,\n", "                sum(pi.warehouse_reusable_qty* (asno.cost_price * (1 + ((asno.cgst_value + asno.sgst_value+ asno.igst_value + asno.cess_value + asno.additional_cess_value)/10))) ) as warehouse_reusable_value\n", "                from zomato.hp_pod.orders o\n", "                left join zomato.hp_pod.order_product op on o.id=op.order_id\n", "                LEFT JOIN zomato.hp_wms.orders oo on o.number = oo.order_number\n", "                left join zomato.hp_pod.product_issue pi on op.id=pi.order_product_id\n", "                --left join zomato.hp_wms.order_item oi on oo.id=oi.order_id\n", "                left join zomato.hp_consumer.buyer_order_requests bor on bor.orderid = oo.id\n", "                inner join po.edi_integration_partner_purchase_order_details poid on bor.id = poid.partner_order_id\n", "                inner JOIN po.edi_integration_partner_item_mapping pim ON CAST(pim.partner_item_id as int) = op.product_number and pim.active = true\n", "                LEFT JOIN po.edi_integration_partner_outlet_mapping ipom ON lower(ipom.partner_outlet_id) = lower(oo.warehouse_code) AND ipom.active\n", "                left join po.purchase_order_items poi on poid.po_id= poi.po_id and poi.item_id = pim.item_id\n", "                left join po.purchase_order po on po.id=poi.po_id\n", "                 left join (select po_number,asn_id, max(updated_at) from po.edi_integration_partner_po_invoice_mapping group by 1,2) invo  on invo.po_number = po.po_number\n", "                left join po.edi_integration_partner_invoice_item_details asno on asno.asn_id = invo.asn_id and asno.item_id =poi.item_id and asno.insert_ds_ist >= cast(current_date - interval '30' day as varchar)\n", "                \n", "                where o.dt >= date_format(date_add('day',- 1,current_timestamp),'%%Y%%m%%d')\n", "                and pi.dt >= date_format(date_add('day',- 1,current_timestamp),'%%Y%%m%%d') \n", "                and o.target_delivery_date > current_date - interval '30' day\n", "                and oo.warehouse_code like 'CPC%%'\n", "                and oo.dt>'2022011'\n", "                and pi.status in ('approved','refunded')\n", "                group by 1,2,3,4,5\n", "                ))\n", "                group by 1,2,3,4,5)\n", "\n", "\n", "                select tdd as date_, \n", "                outlet_id, \n", "                warehouse_code,\n", "                po_number,\n", "                item_id, \n", "                warehouse_usable_qty as warehouse_usable_qty, \n", "                (issue_units-warehouse_usable_qty) as warehouse_non_usable_qty, \n", "                warehouse_reusable_value as warehouse_reusable_value,\n", "                (issue_value-warehouse_reusable_value) as warehouse_non_usable_value \n", "                from base b\n", "        ),\n", "        \n", "        \n", "\n", "final_join_pre as \n", "(select \n", "    p2.*,\n", "    coalesce(dn_quantity,0) as discrepancy_quantity,\n", "    coalesce(dn_value,0) as dn_value,\n", "    reason, \n", "    warehouse_reusable_value, \n", "    warehouse_non_usable_value\n", "from \n", "    po_base p2\n", "left join \n", "    dn_base dn \n", "    on dn.item_id = p2.item_id \n", "    and p2.po_number = dn.po_number\n", "left join \n", "    reinventorisation_base rb \n", "    on rb.item_id = p2.item_id \n", "    and rb.po_number = p2.po_number\n", "    and rb.outlet_id = p2.outlet_id\n", "    )\n", "\n", "\n", "\n", "select \n", "    date_ as date_, \n", "    warehouse_id,\n", "    -- warehouse_code, \n", "    outlet_id,\n", "    item_id,\n", "    case when date_ between current_date - interval '7' day and current_date - interval '1' day then 'L7'\n", "         when date_ between current_date - interval '14' day and current_date - interval '8' day then 'L14'\n", "         when date_ between current_date - interval '21' day and current_date - interval '15' day then 'L21'\n", "         else null end as flag, \n", "    coalesce((sum(picked_value) - (sum(grn_valuee)+ sum(dn_value))),0) as grn_gap,\n", "    Case when ((sum(picked_value) - (sum(grn_valuee)+sum(dn_value)))) <= 0  or  (sum(picked_value) - (sum(grn_valuee) + sum(warehouse_reusable_value) + sum(warehouse_non_usable_value))) <= 0 then 0\n", "    else (coalesce((sum(picked_value) - (sum(grn_valuee)+ sum(dn_value)) + sum(-1*warehouse_reusable_value) + sum(-1*warehouse_non_usable_value)),0))  end as grn_gap_after_b2b,\n", "    sum(dn_value) as total_dn,\n", "    sum(case when reason = 'NTE' then dn_value end) as dn_nte,\n", "    sum(case when reason = 'Damage' then dn_value end) as dn_damage,\n", "    sum(case when reason = 'Others' then dn_value end) as dn_misc,\n", "    sum(case when reason = 'Short' then dn_value end) as dn_short,\n", "    sum(case when reason = 'NTE' then discrepancy_quantity end) as dn_nte_qty,\n", "    sum(case when reason = 'Damage' then discrepancy_quantity end) as dn_damage_qty,\n", "    sum(case when reason = 'Others' then discrepancy_quantity end) as dn_misc_qty,\n", "    sum(case when reason = 'Short' then discrepancy_quantity end) as dn_short_qty,\n", "    -- sum(warehouse_non_usable_qty) as warehouse_non_usable_qty,\n", "    -- sum(warehouse_usable_qty) as warehouse_usable_qty,\n", "    sum(warehouse_reusable_value)  as warehouse_usable_value,\n", "    sum(warehouse_non_usable_value) as warehouse_non_usable_value\n", "from final_join_pre as a\n", "where date_ >=current_date - interval '30' day\n", "\n", "group by 1,2,3,4, 5\"\"\"\n", "\n", "transfer_df = read_sql_query(query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "ed1b4c30-c39a-47ee-ad5c-9ac6cfb60d19", "metadata": {"papermill": {"duration": 0.080654, "end_time": "2025-06-19T11:57:01.061504", "exception": false, "start_time": "2025-06-19T11:57:00.980850", "status": "completed"}, "tags": []}, "outputs": [], "source": ["transfer_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "403b1cb5-875e-4832-ba47-df87fa482a38", "metadata": {"papermill": {"duration": 0.097163, "end_time": "2025-06-19T11:57:01.241339", "exception": false, "start_time": "2025-06-19T11:57:01.144176", "status": "completed"}, "tags": []}, "outputs": [], "source": ["transfer_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "85e7ceb7-e57e-4727-89f8-c3132bc3cf06", "metadata": {"papermill": {"duration": 5.580284, "end_time": "2025-06-19T11:57:06.885387", "exception": false, "start_time": "2025-06-19T11:57:01.305103", "status": "completed"}, "tags": []}, "outputs": [], "source": ["transfer_df = transfer_df.merge(mapping_df, on=[\"outlet_id\", \"item_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "b9f94964-448e-46d2-9936-528c1cbd85b6", "metadata": {"papermill": {"duration": 0.09442, "end_time": "2025-06-19T11:57:07.023998", "exception": false, "start_time": "2025-06-19T11:57:06.929578", "status": "completed"}, "tags": []}, "outputs": [], "source": ["transfer_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "638df171-c77c-49fc-a843-53289bc8725b", "metadata": {"papermill": {"duration": 14.496491, "end_time": "2025-06-19T11:57:21.582067", "exception": false, "start_time": "2025-06-19T11:57:07.085576", "status": "completed"}, "tags": []}, "outputs": [], "source": ["transfer_df = transfer_df.merge(dts_cpc, on=[\"be_facility_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "db6ad503-f234-4925-9aeb-b6fe7a69063c", "metadata": {"papermill": {"duration": 0.119521, "end_time": "2025-06-19T11:57:21.807048", "exception": false, "start_time": "2025-06-19T11:57:21.687527", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# transfer_df[\"cpc_dts_flag\"] = \"All\"\n", "transfer_df[\"store_age\"] = \"All\"\n", "transfer_df[\"opd_category\"] = \"All\""]}, {"cell_type": "code", "execution_count": null, "id": "fa228be3-c3de-4e3e-b761-8d88f3384656", "metadata": {"papermill": {"duration": 0.073904, "end_time": "2025-06-19T11:57:21.928213", "exception": false, "start_time": "2025-06-19T11:57:21.854309", "status": "completed"}, "tags": []}, "outputs": [], "source": ["transfer_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "2e7894e0-a692-4701-9802-7dec9fdbd71b", "metadata": {"papermill": {"duration": 3.219155, "end_time": "2025-06-19T11:57:25.236557", "exception": false, "start_time": "2025-06-19T11:57:22.017402", "status": "completed"}, "tags": []}, "outputs": [], "source": ["transfer_df_agg = (\n", "    transfer_df.groupby(\n", "        [\"date_\", \"city\", \"l2\", \"ptype\", \"flag\", \"dts_cpc_flag\", \"store_age\", \"opd_category\"]\n", "    )\n", "    .agg(\n", "        {\n", "            \"dn_damage\": \"sum\",\n", "            \"dn_damage_qty\": \"sum\",\n", "            \"dn_short\": \"sum\",\n", "            \"dn_short_qty\": \"sum\",\n", "            \"total_dn\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3c8d5861-e135-4175-9c2d-3c7392897303", "metadata": {"papermill": {"duration": 0.457486, "end_time": "2025-06-19T11:57:25.764721", "exception": false, "start_time": "2025-06-19T11:57:25.307235", "status": "completed"}, "tags": []}, "outputs": [], "source": ["del transfer_df\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "6617a1b6-b855-462c-9f53-edf41cc68e42", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["transfer_df_agg_all = transfer_df_agg.copy()\n", "transfer_df_agg_all[\"l2\"] = \"All Perishables\"\n", "transfer_df_agg_all[\"ptype\"] = \"All Perishables\"\n", "\n", "transfer_df_agg_all = (\n", "    transfer_df_agg_all.groupby(\n", "        [\"date_\", \"city\", \"l2\", \"ptype\", \"flag\", \"dts_cpc_flag\", \"store_age\", \"opd_category\"]\n", "    )\n", "    .agg(\n", "        {\n", "            \"dn_damage\": \"sum\",\n", "            \"dn_damage_qty\": \"sum\",\n", "            \"dn_short\": \"sum\",\n", "            \"dn_short_qty\": \"sum\",\n", "            \"total_dn\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a004bc1e-bf54-4c92-98bb-f8ad386f34ed", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["transfer_df_final = pd.concat([transfer_df_agg, transfer_df_agg_all])\n", "transfer_df_final = transfer_df_final.reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "2636d868-fe90-45f4-8248-89192372e3a3", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["transfer_df_final.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "6a082660-b3de-43cd-87a7-6723b4dac6c9", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["cat_div.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "a37a73b1-41ac-41b4-b551-8f198a4f5530", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["transfer_df_final = transfer_df_final.merge(cat_div, on=[\"l2\"])\n", "\n", "transfer_df_agg_K = transfer_df_final[transfer_df_final[\"Block\"].isin([\"K\", \"all\"])].drop(\n", "    columns=\"Block\"\n", ")\n", "transfer_df_agg_S = transfer_df_final[transfer_df_final[\"Block\"].isin([\"S\", \"all\"])].drop(\n", "    columns=\"Block\"\n", ")\n", "transfer_df_agg_S2 = transfer_df_final[transfer_df_final[\"Block\"].isin([\"S2\", \"all\"])].drop(\n", "    columns=\"Block\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c915c6bc-96cf-4648-9b73-420bc67a4fb9", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["try_upload_to_sheets(transfer_df_agg_K, \"1pnVcG2BOLs4K_Q54HthBYUi985nC9akYbBowgN61N0g\", \"dn\")\n", "try_upload_to_sheets(transfer_df_agg_S, \"1ADK1tZNlIHL1pCmK6QdCigWsAg8DG3EnilYYWI3XFfQ\", \"dn\")\n", "try_upload_to_sheets(transfer_df_agg_S2, \"1LJ6hI2yWUphvEMBVKCAqGqCzqI1wLkPQ6vp3xcxi-oU\", \"dn\")"]}, {"cell_type": "code", "execution_count": null, "id": "81829719-7116-46e3-8706-d3859f9527a1", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# transfer_df_agg_K.to_csv('transfer_df_agg_K.csv')\n", "# transfer_df_agg_S.to_csv('transfer_df_agg_S.csv')\n", "\n", "# transfer_df_agg_S2.to_csv('transfer_df_agg_S2.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "bbfd0634-d6bf-415d-8233-fc6fac2ec8ee", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "papermill": {"duration": 1013.413229, "end_time": "2025-06-19T11:57:28.927454", "environment_variables": {}, "exception": true, "input_path": "/usr/local/airflow/dags/b15ad46e11d3e6b6745c6183be864775d6ec3820/dags/fresh/perishable_analytics/report/dump_gmv/notebook.ipynb", "output_path": "s3://grofers-prod-dse-sgp/airflow/dag_runs/fresh_perishable_analytics_report_dump_gmv_v4/manual__2025-06-19T11:32:39.061979+00:00/run_notebook/try_1.ipynb", "parameters": {"cwd": "/usr/local/airflow/dags/repo/dags/fresh/perishable_analytics/report/dump_gmv"}, "start_time": "2025-06-19T11:40:35.514225", "version": "2.0.0"}}, "nbformat": 4, "nbformat_minor": 5}