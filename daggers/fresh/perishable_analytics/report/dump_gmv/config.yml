alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: dump_gmv
dag_type: report
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: fresh
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U08M45PL61G
path: fresh/perishable_analytics/report/dump_gmv
paused: false
pool: fresh_pool
project_name: perishable_analytics
schedule:
  end_date: '2025-08-18T00:00:00'
  interval: 30 3,5,7 * * *
  start_date: '2025-06-19T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 4
