{"cells": [{"cell_type": "code", "execution_count": null, "id": "36874417-d99b-4108-acc5-2f0dc33c00a6", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import os\n", "import time\n", "import gc\n", "import requests\n", "\n", "import calendar\n", "from datetime import datetime as dt\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.options.mode.chained_assignment = None  # default='warn'\n", "pd.set_option(\"display.max_columns\", 500)"]}, {"cell_type": "code", "execution_count": null, "id": "320a68ce-65a4-43a3-acde-9b79d47476fb", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pytz\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")\n", "\n", "current_time = datetime.now(IST)\n", "\n", "current_hour = pd.to_datetime(current_time).hour\n", "\n", "today_date = pd.to_datetime(current_time).strftime(\"%Y-%m-%d\")\n", "\n", "today_date"]}, {"cell_type": "code", "execution_count": null, "id": "9d811e5b-027d-41df-b6c6-c694eb9d8eb8", "metadata": {}, "outputs": [], "source": ["current_hour"]}, {"cell_type": "code", "execution_count": null, "id": "fc55eb6a-3334-44da-9811-8a97f7c46760", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(\n", "            f\"Read attempt: {attempt}... \\nRun started at {datetime.now() + timed<PERSON>ta(hours = 5.5)}\"\n", "        )\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "markdown", "id": "623707c4-2f02-41e8-8aeb-a7b68a050742", "metadata": {}, "source": ["## ptypes & hour input"]}, {"cell_type": "code", "execution_count": null, "id": "b80749d6-c387-41cb-92a4-de6b38ad57ae", "metadata": {}, "outputs": [], "source": ["hour_list = tuple([2, 8, 9, 13, 14, 19, 21])"]}, {"cell_type": "code", "execution_count": null, "id": "08c046a0-4ba0-459c-a183-d5469d321e81", "metadata": {}, "outputs": [], "source": ["# ptype_list = (\n", "#     \"Chicken Breast\",\n", "#     \"Chicken Curry Cut\",\n", "#     \"Pav\",\n", "#     \"Batter\",\n", "#     \"White Bread\",\n", "#     \"Brown Bread\",\n", "#     \"Wheat Bread\",\n", "#     \"White Eggs\",\n", "#     \"Greek Yogurt\",\n", "#     \"<PERSON><PERSON><PERSON>\",\n", "#     \"Curd\",\n", "#     \"Paneer\",\n", "#     \"<PERSON><PERSON>\",\n", "#     \"Unsalted Buttermilk\",\n", "#     \"Salted Buttermilk\",\n", "# )\n", "\n", "\n", "oos_input_df = pb.from_sheets(\n", "    sheetid=\"1pIEInYj2XyvbwjLIdbnZOZ1w7n8HQwUFm8csGuw0Ivg\",\n", "    sheetname=\"oos_input\",\n", ")\n", "\n", "# oos_input_df = pd.read_csv('oos_input.csv')\n", "\n", "\n", "if oos_input_df.shape[0] > 0:\n", "    ptype_list = list(oos_input_df[\"ptype\"].unique())\n", "    if len(ptype_list) < 2:\n", "        ptype_list.append(-1)\n", "        ptype_list.append(-2)\n", "    ptype_list = tuple(ptype_list)\n", "\n", "ptype_list"]}, {"cell_type": "markdown", "id": "e55a8265-c966-452d-9348-a475c36a2434", "metadata": {"tags": []}, "source": ["## Meats Sales"]}, {"cell_type": "code", "execution_count": null, "id": "99609b94-da78-4171-89dd-98eb14754774", "metadata": {}, "outputs": [], "source": ["# sale_query = f\"\"\"\n", "#     WITH  meats_skus as (\n", "#     select distinct item_id, name as item_name, product_type as ptype\n", "#     from rpc.item_category_details\n", "#     where l2_id IN (63,1367,1369)\n", "#     and product_type in {ptype_list}\n", "#     ),\n", "\n", "\n", "#     pre_item_mapping as (\n", "#         SELECT DISTINCT ipr.product_id,\n", "#         CASE\n", "#             WHEN ipr.item_id IS NULL THEN ipom_0.item_id\n", "#             ELSE ipr.item_id\n", "#         END AS item_id,\n", "#         CASE\n", "#             WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1)\n", "#             ELSE COALESCE(ipom_0.multiplier,1)\n", "#         END AS multiplier\n", "#         FROM rpc.item_product_mapping ipr\n", "#         LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id\n", "#         LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", "#         WHERE ipr.lake_active_record\n", "#     ),\n", "\n", "#     item_mapping as (\n", "#     select product_id, x.item_id , item_name, ptype, multiplier\n", "#     from pre_item_mapping as x\n", "#     inner join meats_skus as ms on ms.item_id = x.item_id),\n", "\n", "\n", "#     sales AS (\n", "#         SELECT (oid.cart_checkout_ts_ist) AS order_date, cl.name AS city, rco.facility_id, oid.product_id, im.item_id, oid.order_id, im.multiplier, im.item_name, im.ptype,\n", "#         ((oid.procured_quantity - oid.total_doorstep_return_quantity) * im.multiplier) AS sales_quantity\n", "#         FROM dwh.fact_sales_order_item_details oid\n", "#         JOIN item_mapping im on im.product_id = oid.product_id\n", "#         JOIN retail.console_outlet rco ON rco.id = oid.outlet_id AND business_type_id = 7  AND rco.lake_active_record\n", "#         LEFT JOIN retail.console_location cl ON cl.id = rco.tax_location_id AND cl.lake_active_record\n", "#         WHERE oid.order_create_dt_ist >= DATE('{today_date}') - interval '31' day AND oid.order_create_dt_ist < DATE('{today_date}') - interval '0' day\n", "#         AND oid.is_internal_order = false\n", "#         AND (oid.order_type NOT LIKE '%%internal%%' OR oid.order_type IS NULL)\n", "#         AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED'\n", "#     ),\n", "\n", "#     final_sales AS (\n", "#         SELECT DATE(order_date) AS date_, city, ptype, s.item_id,item_name,  CAST(SUM(sales_quantity) AS int) AS quantity\n", "#         FROM sales s\n", "#         GROUP BY 1,2,3, 4,5\n", "#     )\n", "\n", "#     SELECT DISTINCT city, ptype, item_id, item_name, date_, quantity\n", "#     FROM final_sales\n", "\n", "#     \"\"\"\n", "# meats_sales_pre_df = read_sql_query(sale_query, trino)\n", "# meats_sales_pre_df[\"date_\"] = pd.to_datetime(meats_sales_pre_df[\"date_\"])\n", "\n", "# meats_sales_pre_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c584ac1b-058d-4769-ae24-24970acdbd44", "metadata": {}, "outputs": [], "source": ["# meats_sales_pre_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "2afd6f21-3c41-4ffd-b894-d8fab372a139", "metadata": {}, "outputs": [], "source": ["# meats_sales_pre_df = meats_sales_pre_df.rename(columns={\"quantity\": \"qty_sold\"})"]}, {"cell_type": "code", "execution_count": null, "id": "eca17d70-e598-4a30-9164-ea0d0b41a0c4", "metadata": {}, "outputs": [], "source": ["# sku_rank_df = (\n", "#     meats_sales_pre_df.groupby([\"city\", \"ptype\", \"item_id\", \"item_name\"])\n", "#     .agg({\"qty_sold\": \"mean\"})\n", "#     .reset_index()\n", "# )\n", "\n", "\n", "# sku_rank_df[\"qty_sold\"] = sku_rank_df[\"qty_sold\"].astype(int)\n", "\n", "# sku_rank_df = sku_rank_df.sort_values(by=\"qty_sold\", ascending=True)\n", "\n", "# sku_rank_df[\"cum_quantity\"] = sku_rank_df.groupby([\"city\", \"ptype\"])[\"qty_sold\"].cumsum()\n", "\n", "# sku_agg_df = (\n", "#     sku_rank_df.groupby([\"city\", \"ptype\"])\n", "#     .agg({\"qty_sold\": \"sum\"})\n", "#     .reset_index()\n", "#     .rename(columns={\"qty_sold\": \"tot_quantity\"})\n", "# )\n", "\n", "# sku_rank_df = sku_rank_df.merge(sku_agg_df, on=[\"city\", \"ptype\"], how=\"left\")\n", "\n", "# sku_rank_df[\"perc_contri\"] = sku_rank_df[\"cum_quantity\"] / sku_rank_df[\"tot_quantity\"]\n", "\n", "# ## 70% sales contributing..\n", "# sku_rank_df[\"stype_\"] = np.where(sku_rank_df[\"perc_contri\"] > 0.3, \"top\", \"bottom\")\n", "\n", "# sku_rank_df.head(1)\n", "\n", "# top_skus_meats = sku_rank_df[sku_rank_df[\"stype_\"] == \"top\"]\n", "# top_skus_meats = top_skus_meats[[\"city\", \"ptype\", \"item_id\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "7b72239f-6ab5-4f84-80fa-b8a61832b3da", "metadata": {}, "outputs": [], "source": ["# top_skus_meats.head(5)"]}, {"cell_type": "markdown", "id": "1b087a35-03f7-40f1-a804-c8686d4e897e", "metadata": {}, "source": ["### Top skus within a ptype in a city"]}, {"cell_type": "code", "execution_count": null, "id": "a1f4bfd6-22d3-4e99-9e9b-5feec08bdda8", "metadata": {}, "outputs": [], "source": ["# sales_q = f\"\"\"\n", "# with pre_sales as\n", "# (SELECT\n", "#        a.date_,\n", "#        a.city,\n", "#        a.outlet_id,\n", "#        a.item_id,\n", "#        a.item_name,\n", "#        a.quantity\n", "# FROM supply_etls.perishables_daily_item_outlet_sales as a\n", "# JOIN (select date_, facility_id,item_id, max(updated_at) as updated_at\n", "#           from supply_etls.perishables_daily_item_outlet_sales\n", "#        where date_ist >= date('{today_date}') - interval '30' DAY\n", "#         group by 1,2,3) as b ON a.facility_id = b.facility_id AND a.item_id = b.item_id AND a.date_ = b.date_ AND a.updated_at = b.updated_at\n", "# WHERE a.date_ist >= date('{today_date}') - interval '30' DAY\n", "# )\n", "\n", "# , sales_agg as\n", "# (select city,\n", "#         date_,\n", "#         item_id,\n", "#         item_name,\n", "#         sum(quantity) as qty_sold\n", "#     from pre_sales\n", "#     group by 1,2,3,4)\n", "\n", "\n", "# ,ptype_mapping as\n", "# (select distinct item_id, product_type  from rpc.item_category_details\n", "#     where product_type in {ptype_list})\n", "\n", "\n", "# select s.city, s.date_, p.product_type as ptype, s.item_id, s.item_name, qty_sold\n", "# from sales_agg as s\n", "# join ptype_mapping as p ON s.item_id = p.item_id\n", "# \"\"\"\n", "\n", "# sales_df = read_sql_query(sales_q, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "c14c1a26-efd1-437e-a600-5678d15ca276", "metadata": {}, "outputs": [], "source": ["# sales_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "939075e2-3254-409a-b727-aea1e5357a3c", "metadata": {}, "outputs": [], "source": ["# sku_rank_df = (\n", "#     sales_df.groupby([\"city\", \"ptype\", \"item_id\", \"item_name\"])\n", "#     .agg({\"qty_sold\": \"mean\"})\n", "#     .reset_index()\n", "# )\n", "\n", "\n", "# sku_rank_df[\"qty_sold\"] = sku_rank_df[\"qty_sold\"].astype(int)\n", "\n", "# sku_rank_df = sku_rank_df.sort_values(by=\"qty_sold\", ascending=True)\n", "\n", "# sku_rank_df[\"cum_quantity\"] = sku_rank_df.groupby([\"city\", \"ptype\"])[\"qty_sold\"].cumsum()\n", "\n", "# sku_agg_df = (\n", "#     sku_rank_df.groupby([\"city\", \"ptype\"])\n", "#     .agg({\"qty_sold\": \"sum\"})\n", "#     .reset_index()\n", "#     .rename(columns={\"qty_sold\": \"tot_quantity\"})\n", "# )\n", "\n", "# sku_rank_df = sku_rank_df.merge(sku_agg_df, on=[\"city\", \"ptype\"], how=\"left\")\n", "\n", "# sku_rank_df[\"perc_contri\"] = sku_rank_df[\"cum_quantity\"] / sku_rank_df[\"tot_quantity\"]\n", "\n", "# ## 70% sales contributing..\n", "# sku_rank_df[\"stype_\"] = np.where(sku_rank_df[\"perc_contri\"] > 0.3, \"top\", \"bottom\")\n", "\n", "# sku_rank_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "17b51041-3878-4f6b-bfc7-7073faa549bb", "metadata": {}, "outputs": [], "source": ["# top_skus = sku_rank_df[sku_rank_df[\"stype_\"] == \"top\"]\n", "# top_skus = top_skus[[\"city\", \"ptype\", \"item_id\"]].drop_duplicates()"]}, {"cell_type": "markdown", "id": "47aa1e46-8bdc-491d-af87-9b7c1c008225", "metadata": {}, "source": ["### concat meats & other l2 top skus"]}, {"cell_type": "code", "execution_count": null, "id": "dab55dc9-7406-47de-8c73-f68613493ae2", "metadata": {}, "outputs": [], "source": ["# top_skus = pd.concat([top_skus, top_skus_meats], ignore_index=True)"]}, {"cell_type": "markdown", "id": "d85e651f-b4eb-4ca4-ac74-3ccbc1a1cde2", "metadata": {}, "source": ["# Inventory"]}, {"cell_type": "code", "execution_count": null, "id": "70099c41-71e4-4072-a78b-70a594aa5864", "metadata": {}, "outputs": [], "source": ["pre_base_query = f\"\"\"\n", "\n", "with active_ds as (\n", "    select cl.name as city, ds.outlet_id\n", "    from dwh.fact_sales_order_item_details ds\n", "    inner join retail.console_outlet co on co.id = ds.outlet_id and co.active = 1\n", "    inner join retail.console_location cl on cl.id = co.tax_location_id\n", "    inner join po.physical_facility_outlet_mapping pfom on pfom.outlet_id = ds.outlet_id and pfom.ars_active = 1 and pfom.active = 1\n", "    inner join dwh.dim_product dp\n", "    on ds.product_id = dp.product_id\n", "    and dp.is_current \n", "    and dp.l0_category_id = 1487 \n", "    where order_create_dt_ist >= date('{today_date}') - interval '3' DAY\n", "    group by 1,2\n", "    having count(distinct cart_id) > 10\n", ")\n", "\n", ",perishable_skus AS (SELECT DISTINCT ma.item_id as item_id, \n", "                    icd.product_type AS ptype, icd.name as item_name\n", "                    FROM rpc.product_product ma\n", "                    JOIN (\n", "                        SELECT item_id, MAX(id) AS id\n", "                        FROM rpc.product_product\n", "                        WHERE active = 1 and approved = 1 and lake_active_record\n", "                        GROUP BY 1\n", "                    ) b ON ma.item_id = b.item_id and ma.id = b.id\n", "                    JOIN rpc.item_category_details icd ON icd.item_id = ma.item_id\n", "                    WHERE l2_id in (116, 63, 1425, 198, 31, 1097, 1956, 1367, 1369, 1389, 1778, 1094, 1093, 1091, 138, 949, 950, 2633) \n", "                    AND ma.perishable = 1 AND ma.lake_active_record\n", "                    AND icd.product_type IN {ptype_list}\n", "\n", ")\n", "\n", "\n", ",eggs_uom AS \n", "(select item_id, \n", "case when eg.uom_value between 1 and 20 then 'small packs'\n", "             when eg.uom_value >= 21 then 'large packs'\n", "             else null end as uom_bucket\n", "    from \n", "            (SELECT \n", "                    DISTINCT ma.item_id AS item_id,\n", "                    icd.name AS item_name,\n", "                    l2,\n", "                    l1,\n", "                    icd.product_type,\n", "                    ma.brand,\n", "                    case when ma.variant_uom_value is NULL or ma.variant_uom_value = 0 \n", "                         then CAST(regexp_extract(icd.name, '(\\d+)(?=\\s*(pieces|units))') AS INT)\n", "                    else CAST(ma.variant_uom_value AS INT) end AS uom_value\n", "                    \n", "                FROM \n", "                    rpc.product_product ma\n", "                JOIN (\n", "                    SELECT item_id, MAX(id) AS id\n", "                    FROM rpc.product_product\n", "                    WHERE active = 1 and approved = 1\n", "                    GROUP BY 1\n", "                ) b ON ma.item_id = b.item_id and ma.id = b.id\n", "                JOIN rpc.item_category_details icd ON icd.item_id = ma.item_id\n", "                WHERE l2_id in (1389,1778)\n", "            ) as eg\n", "group by 1,2\n", ")\n", "\n", ", base as \n", "(select city,item_id, item_name, ptype, outlet_id, outlet_name, date_ist as date_, hour_, sum(inventory) as inventory   \n", "\n", "      FROM\n", "       (SELECT iii.outlet_id,\n", "                          iii.item_id,\n", "                          iii.snapshot_date_ist as date_ist,\n", "                          ad.city, \n", "                           ps.item_name, ps.ptype,\n", "                          o.name as outlet_name,\n", "                          extract(hour from etl_snapshot_ts_ist) AS hour_,\n", "                          max(current_inventory) AS inventory\n", "                   FROM dwh.agg_hourly_outlet_item_inventory as iii\n", "                   LEFT JOIN retail.console_outlet o ON iii.outlet_id = o.id\n", "                   INNER JOIN perishable_skus as ps on ps.item_id = iii.item_id\n", "                   INNER JOIN rpc.product_facility_master_assortment pfma on pfma.item_id = iii.item_id and o.facility_id = pfma.facility_id and master_assortment_substate_id = 1\n", "                   inner join active_ds ad on iii.outlet_id  = ad.outlet_id\n", "                   \n", "                   where snapshot_date_ist = date('{today_date}') \n", "                   and ptype in {ptype_list}\n", "                   group by 1,2,3,4,5,6,7,8)\n", "                   \n", "      \n", "group by 1,2,3,4,5,6,7,8\n", ")\n", "\n", "\n", "select \n", "    city,\n", "    ptype, \n", "    outlet_id, \n", "    outlet_name, \n", "    x.item_id, \n", "    item_name,\n", "    date_,\n", "    hour_, \n", "    inventory\n", "    \n", "from base  as x\n", "left join eggs_uom as y on x.item_id = y.item_id\n", "where hour_ in {hour_list}\n", "\n", "\"\"\"\n", "\n", "pre_base_df = read_sql_query(pre_base_query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "707167ab-f8fa-4981-88b8-e8d619a57282", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "select item_id, \n", "case when eg.uom_value between 1 and 20 then 'small packs'\n", "             when eg.uom_value >= 21 then 'large packs'\n", "             else null end as uom_bucket\n", "    from \n", "            (SELECT \n", "                    DISTINCT ma.item_id AS item_id,\n", "                    icd.name AS item_name,\n", "                    l2,\n", "                    l1,\n", "                    icd.product_type,\n", "                    ma.brand,\n", "                    case when ma.variant_uom_value is NULL or ma.variant_uom_value = 0 \n", "                         then CAST(regexp_extract(icd.name, '(\\d+)(?=\\s*(pieces|units))') AS INT)\n", "                    else CAST(ma.variant_uom_value AS INT) end AS uom_value\n", "                    \n", "                FROM \n", "                    rpc.product_product ma\n", "                JOIN (\n", "                    SELECT item_id, MAX(id) AS id\n", "                    FROM rpc.product_product\n", "                    WHERE active = 1 and approved = 1\n", "                    GROUP BY 1\n", "                ) b ON ma.item_id = b.item_id and ma.id = b.id\n", "                JOIN rpc.item_category_details icd ON icd.item_id = ma.item_id\n", "                WHERE l2_id in (1389,1778)\n", "            ) as eg\n", "group by 1,2\"\"\"\n", "\n", "eggs_uom = read_sql_query(query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "7cca5bd7-d0e3-4d13-afcd-23b59280c943", "metadata": {}, "outputs": [], "source": ["pre_base_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "8f7d2014-6323-4a0b-8eeb-5e504935be24", "metadata": {}, "outputs": [], "source": ["eggs_uom.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "d8c6dfa6-7b8c-48e5-b33e-2cd91a992cda", "metadata": {}, "outputs": [], "source": ["# pre_base_df[\"ptype\"] = np.where(\n", "#     pre_base_df[\"ptype\"] == \"White Eggs\",\n", "#     pre_base_df[\"ptype\"] + \" - \" + pre_base_df[\"uom_bucket\"].astype(str),  # Corrected concatenation\n", "#     pre_base_df[\"ptype\"],  # Keep 'ptype' unchanged if the condition is not met\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "e929fd70-8577-4dfd-8cc8-bbc01ba39c86", "metadata": {}, "outputs": [], "source": ["pre_base_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "e6e2cfb4-aba7-4421-8eee-05027ae265a4", "metadata": {}, "outputs": [], "source": ["pre_base_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "72ce5b0f-213f-4c17-a943-154134d77ee7", "metadata": {}, "outputs": [], "source": ["pre_base_df = pre_base_df.merge(eggs_uom, on=[\"item_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "8c517c5e-c78f-4955-bb68-798e6398a197", "metadata": {}, "outputs": [], "source": ["pre_base_df[\"uom_bucket\"] = pre_base_df[\"uom_bucket\"].fillna(\"\")"]}, {"cell_type": "code", "execution_count": null, "id": "a6a31efa-6503-48d6-a401-4f4e1a6ff3b6", "metadata": {}, "outputs": [], "source": ["pre_base_df[\"ptype_uom\"] = np.where(\n", "    pre_base_df[\"ptype\"] == \"White Eggs\",\n", "    pre_base_df[\"ptype\"] + \" - \" + pre_base_df[\"uom_bucket\"].astype(str),  # Corrected concatenation\n", "    pre_base_df[\"ptype\"],  # Keep 'ptype' unchanged if the condition is not met\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b836584e-dc07-4263-99c0-ed924b671c57", "metadata": {}, "outputs": [], "source": ["base_df_agg = (\n", "    pre_base_df.groupby([\"city\", \"ptype_uom\", \"outlet_id\", \"outlet_name\", \"date_\", \"hour_\"])\n", "    .agg({\"inventory\": \"sum\"})\n", "    .reset_index()\n", ")\n", "base_df_agg[\"inv_flag\"] = np.where(base_df_agg[\"inventory\"] > 0, 1, 0)"]}, {"cell_type": "code", "execution_count": null, "id": "045e3efb-584c-49fc-8cc7-7a3737b805e3", "metadata": {}, "outputs": [], "source": ["base_df_agg.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "4d08a3ab-1c57-413d-9451-fa970be63645", "metadata": {}, "outputs": [], "source": ["base_df_agg.shape"]}, {"cell_type": "markdown", "id": "1d191646-e248-4c3c-a6d9-60686cb5f491", "metadata": {}, "source": ["### OOS df"]}, {"cell_type": "code", "execution_count": null, "id": "f014c883-06d5-45c7-b720-dd4eaa4040a7", "metadata": {}, "outputs": [], "source": ["oos_base_df = base_df_agg[base_df_agg[\"inv_flag\"] == 0]\n", "oos_base_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "3f2caa33-fa85-49a1-8294-43ccc3bbe19e", "metadata": {}, "outputs": [], "source": ["outlet_list = tuple(list(oos_base_df[\"outlet_id\"].unique()))"]}, {"cell_type": "markdown", "id": "75150fd5-b933-4493-9629-a7afb0fc256a", "metadata": {}, "source": ["### Storeage"]}, {"cell_type": "code", "execution_count": null, "id": "15613a13-7555-4fd6-8164-9ea2b28a5637", "metadata": {}, "outputs": [], "source": ["store_ages_query = f\"\"\"\n", "    with fs as (\n", "                select outlet_id, min(date(cart_checkout_ts_ist)) as min_cart_date, current_date as today_date\n", "                from dwh.fact_sales_order_details\n", "                where order_create_dt_ist is not null\n", "                and order_current_status <> 'CANCELLED'\n", "                group by 1,3\n", "            ),\n", "            \n", "        base as (select outlet_id, date_diff('day', min_cart_date, today_date) as store_age\n", "            from fs\n", "            WHERE outlet_id IN {outlet_list} )\n", "            \n", "        Select outlet_id, store_age from base \n", "            \n", "\"\"\"\n", "store_age_df = read_sql_query(store_ages_query, trino)"]}, {"cell_type": "markdown", "id": "dfd0c95c-5d32-4815-8bbe-eb3fddf64273", "metadata": {}, "source": ["### Historical forecast & Sales "]}, {"cell_type": "code", "execution_count": null, "id": "f47d1ce6-1ba8-4d34-a99a-7d13c9abff8f", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "\n", "select\n", "         date_, \n", "         outlet_id, \n", "         ptype, \n", "         item_id,\n", "         count(distinct item_id) as live_items, \n", "         sum(forecast) forecast, \n", "         sum(max_qty) max_qty, \n", "         sum(max_qty) - sum(fe_inv_2am) as initial_demand_qty,\n", "         sum(sto_quantity) as sto_quantity, \n", "         sum(grn) as grn,\n", "         sum(fe_inv_2am) as sod_inv, \n", "         sum(fe_inv_11pm) as eod_inv,\n", "         sum(qty_sold) as qty_sold,\n", "         sum(tot_dump_quan) as dump_qty\n", "         \n", "         from supply_etls.perishables_daily_universe\n", "         \n", "         where date_ist >= current_date - interval '2' day\n", "         and ptype IN {ptype_list}\n", "         and outlet_id IN {outlet_list}\n", "\n", "         group by 1,2,3,4\n", "\"\"\"\n", "\n", "h_df = read_sql_query(query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "3f32b023-1985-4f66-9ad7-043a4f54c5af", "metadata": {}, "outputs": [], "source": ["h_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "7b6753b4-f44c-4f8e-970e-da1de3cfc406", "metadata": {}, "outputs": [], "source": ["h_df = h_df.merge(eggs_uom, on=[\"item_id\"], how=\"left\")\n", "h_df[\"uom_bucket\"] = h_df[\"uom_bucket\"].fillna(\"\")"]}, {"cell_type": "code", "execution_count": null, "id": "09a0163f-a4ef-46d3-9bea-f362d1ca4de3", "metadata": {}, "outputs": [], "source": ["h_df[\"ptype_uom\"] = np.where(\n", "    h_df[\"ptype\"] == \"White Eggs\",\n", "    h_df[\"ptype\"] + \" - \" + h_df[\"uom_bucket\"].astype(str),  # Corrected concatenation\n", "    h_df[\"ptype\"],  # Keep 'ptype' unchanged if the condition is not met\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "66f20b90-055c-4ceb-ae23-303b74f5d7f1", "metadata": {}, "outputs": [], "source": ["h_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "060bf50b-de6f-4e9e-b027-c4f5d6d8b52e", "metadata": {}, "outputs": [], "source": ["cols_ = [\n", "    \"live_items\",\n", "    \"forecast\",\n", "    \"max_qty\",\n", "    \"initial_demand_qty\",\n", "    \"sto_quantity\",\n", "    \"grn\",\n", "    \"sod_inv\",\n", "    \"eod_inv\",\n", "    \"qty_sold\",\n", "    \"dump_qty\",\n", "]\n", "\n", "h_df = (\n", "    h_df.groupby([\"ptype_uom\", \"outlet_id\", \"date_\"])\n", "    .agg({col: \"sum\" for col in cols_})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2da11be3-2d5c-4f41-8fa6-7b643359d533", "metadata": {}, "outputs": [], "source": ["h_df[\"date_\"] = pd.to_datetime(h_df[\"date_\"])\n", "\n", "current_date = pd.to_datetime(today_date)\n", "\n", "# Calculate the difference in days and format it as T-x\n", "h_df[\"T_label\"] = h_df[\"date_\"].apply(lambda x: f\"T-{(current_date - pd.to_datetime(x)).days}\")"]}, {"cell_type": "code", "execution_count": null, "id": "a59e8757-af82-4508-a5fb-1f35a62774a7", "metadata": {}, "outputs": [], "source": ["df_pivoted = h_df.pivot_table(\n", "    index=[\"outlet_id\", \"ptype_uom\"],\n", "    columns=\"T_label\",\n", "    values=[\n", "        \"live_items\",\n", "        \"qty_sold\",\n", "        \"forecast\",\n", "        \"max_qty\",\n", "        \"initial_demand_qty\",\n", "        \"sto_quantity\",\n", "        \"grn\",\n", "        \"sod_inv\",\n", "        \"eod_inv\",\n", "        \"dump_qty\",\n", "    ],\n", "    aggfunc=\"first\",\n", ")\n", "\n", "# Flatten the MultiIndex columns and rename them\n", "df_pivoted.columns = [f\"{col[1]}_{col[0]}\" for col in df_pivoted.columns]\n", "\n", "# Reset index to make outlet_id and ptype columns again\n", "df_pivoted.reset_index(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "c737ab8a-4daa-4750-b65a-2b880a8171d5", "metadata": {}, "outputs": [], "source": ["x = df_pivoted[df_pivoted[\"outlet_id\"] == 1024]\n", "x.head(5)"]}, {"cell_type": "markdown", "id": "e94d6ec0-9e3e-4d4e-8100-076dcec0b832", "metadata": {}, "source": ["### Today's forecast & sales"]}, {"cell_type": "code", "execution_count": null, "id": "bd67c1ce-7497-47c8-bcda-88684d0b693f", "metadata": {}, "outputs": [], "source": ["sales_query = f\"\"\"\n", "    WITH item_mapping as (\n", "        SELECT DISTINCT ipr.product_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NULL THEN ipom_0.item_id \n", "            ELSE ipr.item_id \n", "        END AS item_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1) \n", "            ELSE COALESCE(ipom_0.multiplier,1) \n", "        END AS multiplier\n", "        FROM rpc.item_product_mapping ipr \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", "        WHERE ipr.lake_active_record\n", "\n", "    ),\n", "\n", "    sales AS (\n", "        SELECT (oid.cart_checkout_ts_ist) AS order_date, \n", "        oid.outlet_id, oid.product_id, im.item_id, oid.order_id, oid.cart_id, oid.city_name, im.multiplier, ((oid.procured_quantity - oid.total_doorstep_return_quantity) * im.multiplier) AS sales_quantity \n", "        FROM dwh.fact_sales_order_item_details oid \n", "        JOIN item_mapping im on im.product_id = oid.product_id  \n", "        JOIN retail.console_outlet rco ON rco.id = oid.outlet_id AND business_type_id = 7 AND rco.lake_active_record\n", "        WHERE oid.order_create_dt_ist = DATE(current_date) \n", "        AND oid.is_internal_order = false  \n", "        AND (oid.order_type NOT LIKE '%%internal%%' OR oid.order_type IS NULL)  \n", "        AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED'\n", "        AND oid.outlet_id IN {outlet_list}\n", "    ),\n", "    \n", "    final_sales AS (\n", "        SELECT DATE(s.order_date) AS date_, \n", "        EXTRACT(hour FROM s.order_date) AS hour_, \n", "        s.outlet_id,\n", "        s.item_id,\n", "        icd.product_type as ptype,\n", "        CAST(SUM(s.sales_quantity) AS int) AS quantity\n", "        FROM sales s\n", "        JOIN rpc.item_category_details icd ON s.item_id = icd.item_id AND icd.lake_active_record\n", "        WHERE product_type IN {ptype_list}\n", "        GROUP BY 1,2,3,4,5\n", "    )\n", "    \n", "    SELECT a.outlet_id, a.ptype, a.item_id, max(a.hour_) as till_hour, SUM(quantity) AS sales_today\n", "    FROM final_sales a\n", "    where date_ = current_date\n", "    GROUP BY 1,2,3\n", "\"\"\"\n", "sales_df = read_sql_query(sales_query, trino)\n", "\n", "sales_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "ea266a4a-bcb4-4aa5-ae29-4b3f1166e77a", "metadata": {}, "outputs": [], "source": ["sales_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "8822a84e-bf63-4e5a-b1df-d6741be01723", "metadata": {}, "outputs": [], "source": ["sales_df = sales_df.merge(eggs_uom, on=[\"item_id\"], how=\"left\")\n", "sales_df[\"uom_bucket\"] = sales_df[\"uom_bucket\"].fillna(\"\")\n", "\n", "\n", "sales_df[\"ptype_uom\"] = np.where(\n", "    sales_df[\"ptype\"] == \"White Eggs\",\n", "    sales_df[\"ptype\"] + \" - \" + sales_df[\"uom_bucket\"].astype(str),  # Corrected concatenation\n", "    sales_df[\"ptype\"],  # Keep 'ptype' unchanged if the condition is not met\n", ")\n", "\n", "\n", "sales_df = (\n", "    sales_df.groupby([\"ptype_uom\", \"outlet_id\"])\n", "    .agg({\"sales_today\": \"sum\", \"till_hour\": \"max\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "eeb6640d-83d6-4e84-beac-c654ff343c8f", "metadata": {}, "outputs": [], "source": ["sales_df.head(2)"]}, {"cell_type": "markdown", "id": "d3252873-91dd-402b-9085-3e99533c4179", "metadata": {}, "source": ["#### forecast"]}, {"cell_type": "code", "execution_count": null, "id": "759e7dd6-40e5-4ee2-bedc-42efc0553f33", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "\n", "with forecast as \n", "(SELECT DATE(a.fdate) AS date_, a.facility_id, outlet_id, a.item_id, product_type as ptype, \n", "SUM(a.final_ex_qty) as forecast_today\n", "    FROM supply_etls.perishable_forecast_cpd_logs a\n", "    JOIN (\n", "        SELECT fdate, facility_id, item_id, MAX(updated_at) AS updated_at\n", "        FROM supply_etls.perishable_forecast_cpd_logs b\n", "        WHERE date_ist >= current_date - interval '7' day\n", "        GROUP BY 1,2,3\n", "    ) b ON a.item_id = b.item_id AND a.facility_id = b.facility_id AND a.updated_at = b.updated_at AND a.fdate = b.fdate\n", "    JOIN retail.console_outlet co ON co.facility_id = a.facility_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "    join rpc.item_category_details as icd on icd.item_id = a.item_id\n", "    WHERE a.date_ist >= current_date - interval '7' day\n", "    and a.fdate = current_date\n", "    and product_type in {ptype_list}\n", "    group by 1,2,3,4,5)\n", "    \n", ", min_max as \n", "    (SELECT \n", "           a.facility_id,\n", "           icd.product_type as ptype,\n", "           a.item_id,\n", "           a.date_ist as date_,\n", "           sum(max_qty) as max_qty_today\n", "    FROM supply_etls.facility_item_min_max_quantity_log a\n", "    join rpc.item_category_details as icd on icd.item_id = a.item_id\n", "    JOIN\n", "      (SELECT a.item_id,\n", "              a.date_ist,\n", "              facility_id,\n", "              max(a.updated_at) AS updated_at\n", "       FROM supply_etls.facility_item_min_max_quantity_log a\n", "       WHERE date_ist >= current_date - interval '7' day\n", "       GROUP BY 1,\n", "                2,\n", "                3) b ON a.item_id = b.item_id\n", "    AND a.date_ist = b.date_ist\n", "    AND a.updated_at = b.updated_at\n", "    AND a.facility_id = b.facility_id\n", "    group by 1,2,3,4)\n", "    \n", "    \n", "select f.outlet_id, f.item_id, f.ptype,  forecast_today, max_qty_today\n", "from \n", "forecast as f\n", "left join min_max as m ON f.facility_id = m.facility_id and m.ptype = f.ptype and m.date_ = f.date_ and m.item_id = f.item_id\n", "\"\"\"\n", "\n", "current_forecast = read_sql_query(query, trino)\n", "current_forecast.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "585e7d55-868e-453f-82a3-1c73af0bdf98", "metadata": {}, "outputs": [], "source": ["current_forecast = current_forecast.merge(eggs_uom, on=[\"item_id\"], how=\"left\")\n", "current_forecast[\"uom_bucket\"] = current_forecast[\"uom_bucket\"].fillna(\"\")\n", "\n", "\n", "current_forecast[\"ptype_uom\"] = np.where(\n", "    current_forecast[\"ptype\"] == \"White Eggs\",\n", "    current_forecast[\"ptype\"]\n", "    + \" - \"\n", "    + current_forecast[\"uom_bucket\"].astype(str),  # Corrected concatenation\n", "    current_forecast[\"ptype\"],  # Keep 'ptype' unchanged if the condition is not met\n", ")\n", "\n", "\n", "current_forecast = (\n", "    current_forecast.groupby([\"ptype_uom\", \"outlet_id\"])\n", "    .agg({\"forecast_today\": \"sum\", \"max_qty_today\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e65c0624-8b61-47fe-bbee-085a718cd7cb", "metadata": {}, "outputs": [], "source": ["current_forecast.head(2)"]}, {"cell_type": "markdown", "id": "5b8eb91e-71ea-46d0-a798-be522247128f", "metadata": {}, "source": ["### grn timing"]}, {"cell_type": "code", "execution_count": null, "id": "025cc8b4-f0e9-4be3-8480-7077ce6e536f", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "\n", "with item_base as (\n", "    select distinct ma.item_id, icd.name as item_name, \n", "    icd.product_type as ptype\n", "        from   rpc.product_product ma \n", "        JOIN (\n", "                SELECT item_id, MAX(id) AS id\n", "                FROM  rpc.product_product\n", "                WHERE active = 1 AND approved = 1 and perishable = 1\n", "                GROUP BY 1\n", "            ) b ON ma.item_id = b.item_id\n", "            join  rpc.item_category_details icd on icd.item_id = ma.item_id\n", "        where l2_id in (1425,31,116,198,1097,1956,949,1389,1778,63,1367,1369,1185,950,138,1091,1093,138,1091,1093,1094,33,97,197,1127)\n", "        ),\n", "        \n", "wh_mapping as (SELECT DISTINCT item_id, tm.outlet_id, tag_value AS be_outlet_id, cb.facility_id AS be_facility_id, cb.name as backend_name\n", "    FROM  rpc.item_outlet_tag_mapping tm\n", "    Join  po.physical_facility_outlet_mapping pfom ON tm.outlet_id=pfom.outlet_id and pfom.active=1 and pfom.ars_active = 1 AND pfom.is_primary =1\n", "    JOIN  retail.console_outlet co ON co.id = tm.outlet_id AND co.active = 1 AND co.business_type_id = 7\n", "    JOIN  retail.console_outlet cb ON cb.id = cast(tag_value as int) AND cb.active = 1\n", "    LEFT JOIN  crates.facility cf on cf.id = cb.facility_id\n", "    WHERE item_id IN (SELECT DISTINCT item_id FROM  rpc.item_category_details \n", "    WHERE l2_id IN (1185,1425,31,116,198,1097,1956,949,1389,1778,63,1367,1369,950,138,1091,1093,138,1091,1093,1094,33,97,197,1127)\n", "    )\n", "    AND tm.active = 1 AND tm.tag_type_id = 8),\n", "\n", "po_base as \n", "            (select \n", "                wh.be_outlet_id as backend_outlet_id,\n", "                coalesce(wh.backend_name,'DTS') as backend_name,\n", "                p.outlet_id,\n", "                o.name,\n", "                poi.item_id,\n", "                it.item_name as item_name,\n", "                it.ptype,\n", "                (p.created_at + interval '330' minute) as issue_date,\n", "   CASE \n", "    WHEN EXTRACT(HOUR FROM p.created_at + interval '330' minute)  BETWEEN 20 AND 23\n", "     OR EXTRACT(HOUR FROM p.created_at + interval '330' minute) BETWEEN 0 AND 4\n", "    THEN 'SLOT A'\n", "-- 4 pm to 8 pm \n", "WHEN EXTRACT(HOUR FROM p.created_at + interval '330' minute) BETWEEN 16 AND 19\n", "  THEN 'SLOT C'\n", "  ELSE 'SLOT B'\n", "  END AS slot,\n", "                max_grn_time as grn_time, \n", "            sum(grn.quan) as total_grn,\n", "            sum(poi.units_ordered) as po_quant\n", "        from \n", "             po.purchase_order p\n", "        left join  po.po_schedule ps on p.id=ps.po_id_id\n", "        inner join  po.purchase_order_items poi on p.id=poi.po_id\n", "        inner join\n", "             retail.console_outlet o on o.id=p.outlet_id and o.business_type_id = 7 and o.active = 1 \n", "        inner join\n", "             po.purchase_order_status posa on posa.po_id = p.id\n", "        inner join\n", "             po.purchase_order_state posta on posta.id = posa.po_state_id\n", "        LEFT JOIN wh_mapping wh on wh.outlet_id = p.outlet_id and wh.item_id = poi.item_id\n", "        inner join item_base it on it.item_id = poi.item_id \n", "        left join\n", "                (select po_id, item_id, sum(quantity) quan, max(created_at + interval '330' minute) as max_grn_time from  po.po_grn grn \n", "                where insert_ds_ist >= cast(current_date - interval '5' day as varchar) group by 1,2) grn\n", "            on \n", "                grn.item_id = poi.item_id \n", "                and grn.po_id = p.id\n", "        where \n", "            (posta.name not in ('Cancelled', 'Rejected', 'Cancelled post Creation') or (posta.name in ('Expired'))) \n", "            and issue_date >= current_date - interval '5' day\n", "            and po_type_id <> 11\n", "            and p.vendor_id in (13280)\n", "            and cast(p.outlet_id as varchar) not in (select distinct be_outlet_id from wh_mapping)\n", "            group  by 1,2,3,4,5,6,7,8,9,10\n", "            ),\n", "            \n", "final_to_store as (select \n", "Case when date(grn_time) is not null then date(grn_time) \n", "when extract(hour from issue_Date) between 18 and 23 then date(issue_Date + interval '1' day) \n", "else date(issue_Date) end as scheduled_grn_date, \n", "backend_outlet_id as warehouse_id, \n", "backend_name as warehouse_name,\n", "outlet_id as store_id, \n", "name as store_name,\n", "slot,\n", "item_id,\n", "ptype,\n", " max(Cast(grn_time as time)) as grn_time_, \n", "sum(po_quant)  as sto_quantity,  \n", "sum(total_grn) as grn_quantity\n", "from po_base\n", "group by 1,2,3,4,5,6,7,8),\n", "    \n", "base as \n", "(with\n", "sto_details as\n", "    (select (created_at + interval '330' minute) as created_time,\n", "        sto_id, outlet_id as sender_outlet_id, merchant_outlet_id as receiver_outlet_id,\n", "            case when sto_state = 1 then 'created'\n", "                when sto_state = 2 then 'billed' when sto_state = 3 then 'expired'\n", "                when sto_state = 4 then 'inward' when sto_state = 5 then 'partial-billed'\n", "                    else null end as sto_state\n", "\n", "            from ims.ims_sto_details sd\n", "\n", "                where\n", "                     created_at+ interval '330' minute >= current_date - interval '5' day\n", "    ),\n", "\n", "sto_item_details as\n", "    (select created_time,\n", "        si.sto_id, sender_outlet_id, receiver_outlet_id, si.item_id, reserved_quantity as sto_quantity, sto_state\n", "\n", "            from ims.ims_sto_item si\n", "\n", "                join\n", "                    sto_details sd on sd.sto_id = si.sto_id\n", "                Join item_base ib on ib.item_id = si.item_id\n", "                    where created_at+ interval '330' minute >= current_date - interval '5' day\n", "    ) ,\n", "    \n", "invoice_details as\n", "    (select cast(grofers_order_id as bigint) as sto_id,\n", "        invoice_id, pi.id, pi.outlet_id,\n", "        (pos_timestamp + interval '330' minute) as last_billed_time\n", "\n", "            from pos.pos_invoice pi\n", "\n", "                where insert_ds_ist >= cast((current_date - interval '5' day) as varchar)\n", "                    and invoice_type_id in (5,14,16)\n", "                    and grofers_order_id != ''\n", "                    and grofers_order_id is not null\n", "    ),\n", "\n", "invoice_item_details as\n", "    (select item_id, outlet_id, sto_id, sum(quantity) as billed_quantity\n", "        from pos.pos_invoice_product_details pd\n", "\n", "            join (select distinct item_id, upc from rpc.product_product\n", "                where id in (select max(id) as id from rpc.product_product where active = 1 and approved = 1 group by upc)\n", "                    ) rpp on rpp.upc = pd.upc_id\n", "\n", "            join \n", "                invoice_details id on id.id = pd.invoice_id\n", "\n", "                where insert_ds_ist >= cast((current_date - interval '5' day) as varchar)\n", "\n", "                    group by 1,2,3\n", "    ),\n", "\n", "grn_details as\n", "    (select ii.grn_id, ii.vendor_invoice_id, id.sto_id\n", "        from ims.ims_inward_invoice ii\n", "\n", "            join\n", "                invoice_details id on id.invoice_id = ii.vendor_invoice_id\n", "\n", "                where insert_ds_ist >= cast((current_date - interval '5' day) as varchar)\n", "                    and source_type = 2\n", "    ),\n", "\n", "grn_item_details as\n", "    (select item_id, outlet_id, sto_id, max(created_at + interval '330' minute) as last_inward_time,\n", "        sum(\"delta\") as grn_quantity\n", "        from ims.ims_inventory_stock_details sd\n", "\n", "            join (select distinct item_id, upc from rpc.product_product\n", "                where id in (select max(id) as id from rpc.product_product where active = 1 and approved = 1 group by upc)\n", "                    ) rpp on rpp.upc = sd.upc_id\n", "\n", "            join \n", "                grn_details gd on gd.grn_id = sd.grn_id\n", "\n", "                where insert_ds_ist >= cast((current_date - interval '5' day) as varchar)\n", "\n", "                    group by 1,2,3\n", "    ),\n", "    \n", "final as\n", "    (select   \n", "    sid.created_time,\n", "    CASE \n", "    WHEN EXTRACT(HOUR FROM CAST(sid.created_time AS timestamp)) BETWEEN 18 AND 23 \n", "    THEN DATE(CAST(sid.created_time AS timestamp) + INTERVAL '1' DAY ) \n", "    ELSE DATE(CAST(sid.created_time AS timestamp)) \n", "  END AS scheduled_grn_date,\n", "  CASE \n", "    WHEN EXTRACT(HOUR FROM CAST(sid.created_time AS timestamp)) BETWEEN 16 AND 19 THEN 'SLOT C'\n", "    WHEN EXTRACT(HOUR FROM CAST(sid.created_time AS timestamp)) BETWEEN 20 AND 23\n", "         OR EXTRACT(HOUR FROM CAST(sid.created_time AS timestamp)) BETWEEN 0 AND 4\n", "    THEN 'SLOT A'\n", "    ELSE 'SLOT B'\n", "  END AS slot,\n", "        sid.sto_id, \n", "        sid.sender_outlet_id as warehouse_id,\n", "        co.name as warehouse_name,\n", "        sid.receiver_outlet_id as store_id,\n", "        cb.name as store_name,\n", "        ptype,\n", "        sid.item_id, \n", "        ib.item_name as item_name,\n", "        sid.sto_quantity as po_quant, \n", "        last_inward_time as grn_time,\n", "        case when grn_quantity is null then 0 else grn_quantity end as grn_qty\n", "        from sto_item_details sid\n", "                left join\n", "                    invoice_item_details iid on iid.sto_id = sid.sto_id \n", "                        and iid.item_id = sid.item_id\n", "                left join\n", "                    grn_item_details gid on gid.sto_id = sid.sto_id \n", "                        and gid.item_id = sid.item_id\n", "        left join retail.console_outlet co on co.id = sid.sender_outlet_id and co.business_type_id in (19,20,21,1,12)\n", "        left join retail.console_outlet cb on cb.id = sid.receiver_outlet_id and cb.business_type_id in (7)\n", "        inner join item_base ib on ib.item_id = sid.item_id\n", "    )\n", "select * from final\n", "    ),\n", "\n", "    final_sto as (select \n", "    date(scheduled_grn_date) as scheduled_grn_date,\n", "    Cast(warehouse_id as varchar),\n", "    warehouse_name as warehouse_name,\n", "        store_id,\n", "    store_name, \n", "    slot,\n", "     item_id,\n", "     --item_name,\n", "    ptype,\n", "     max(Cast(grn_time as time)) as grn_time_, \n", "    sum(po_quant) as sto_quantity,\n", "    sum(grn_qty) as grn_quantity\n", "    from base as b\n", "    group by 1,2,3,4,5,6,7, 8),\n", "\n", "    base_final as (select * from final_to_store union select * from final_sto)\n", "    \n", "    select\n", "           store_id,\n", "           item_id,\n", "           slot,\n", "           ptype,\n", "           grn_time_,\n", "           sto_quantity,\n", "           grn_quantity \n", "         \n", "        from base_final\n", "    \n", "     where warehouse_name IS NOT NULL\n", "     and date(scheduled_grn_date) = current_date\n", "\"\"\"\n", "\n", "grn_df = read_sql_query(query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "637020b5-2c6c-454c-97fd-4821b1edcfcc", "metadata": {}, "outputs": [], "source": ["grn_df = grn_df.merge(eggs_uom, on=[\"item_id\"], how=\"left\")\n", "grn_df[\"uom_bucket\"] = grn_df[\"uom_bucket\"].fillna(\"\")\n", "\n", "\n", "grn_df[\"ptype_uom\"] = np.where(\n", "    grn_df[\"ptype\"] == \"White Eggs\",\n", "    grn_df[\"ptype\"] + \" - \" + grn_df[\"uom_bucket\"].astype(str),  # Corrected concatenation\n", "    grn_df[\"ptype\"],  # Keep 'ptype' unchanged if the condition is not met\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "abc0db3d-2181-4f3e-8088-6065f6699ebf", "metadata": {}, "outputs": [], "source": ["grn_df = (\n", "    grn_df.groupby([\"ptype_uom\", \"slot\", \"store_id\", \"grn_time_\"])\n", "    .agg({\"sto_quantity\": \"sum\", \"grn_quantity\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fce4399d-cd49-4800-a44c-db55e95766f0", "metadata": {}, "outputs": [], "source": ["grn_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d6258b34-af17-4ce1-b8c2-3cff307aa60c", "metadata": {}, "outputs": [], "source": ["x = grn_df[(grn_df[\"store_id\"] == 1024) & (grn_df[\"ptype_uom\"] == \"Bread\")]\n", "x.head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "af505ae6-7416-45d0-815a-3f04b0a00d8a", "metadata": {}, "outputs": [], "source": ["grn_df2 = grn_df.pivot_table(\n", "    index=[\"store_id\", \"ptype_uom\"],\n", "    columns=\"slot\",\n", "    values=[\"grn_time_\", \"sto_quantity\", \"grn_quantity\"],\n", "    aggfunc=\"first\",\n", ")\n", "grn_df2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "1708bef8-31f2-42e4-bfe7-4cfef3bf159d", "metadata": {}, "outputs": [], "source": ["# Flatten the MultiIndex columns\n", "grn_df2.columns = [f\"{col[1]}_{col[0]}\" for col in grn_df2.columns]"]}, {"cell_type": "code", "execution_count": null, "id": "4908adca-8470-4d59-b09e-fd1024b821fa", "metadata": {}, "outputs": [], "source": ["grn_df2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b5e003f1-492b-4fe0-a895-db8d528b5493", "metadata": {}, "outputs": [], "source": ["# Reset the index to make 'date_', 'store_id', and 'ptype' columns\n", "grn_df2.reset_index(inplace=True)\n", "grn_df2[\n", "    [\"SLOT A_grn_quantity\", \"SLOT B_grn_quantity\", \"SLOT A_sto_quantity\", \"SLOT B_sto_quantity\"]\n", "] = (\n", "    grn_df2[\n", "        [\"SLOT A_grn_quantity\", \"SLOT B_grn_quantity\", \"SLOT A_sto_quantity\", \"SLOT B_sto_quantity\"]\n", "    ]\n", "    .fillna(0)\n", "    .astype(int)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8317bc8d-d67c-40c4-99b6-e8e3357ca50f", "metadata": {}, "outputs": [], "source": ["grn_df2 = grn_df2.rename(columns={\"store_id\": \"outlet_id\"})"]}, {"cell_type": "markdown", "id": "b0a7f92f-6e4d-4396-b400-4388ae74fc33", "metadata": {}, "source": ["# JOINS"]}, {"cell_type": "code", "execution_count": null, "id": "aca37c02-2356-467f-9a63-acf3167ab6e1", "metadata": {}, "outputs": [], "source": ["oos_base_df = oos_base_df.merge(store_age_df, on=[\"outlet_id\"], how=\"left\")\n", "oos_base_df = oos_base_df.merge(sales_df, on=[\"outlet_id\", \"ptype_uom\"], how=\"left\")\n", "oos_base_df = oos_base_df.merge(current_forecast, on=[\"outlet_id\", \"ptype_uom\"], how=\"left\")\n", "oos_base_df = oos_base_df.merge(grn_df2, on=[\"outlet_id\", \"ptype_uom\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "87a13adb-1301-4810-a226-a279c8f5bf49", "metadata": {}, "outputs": [], "source": ["oos_base_df = oos_base_df.merge(df_pivoted, on=[\"outlet_id\", \"ptype_uom\"], how=\"left\")\n", "oos_base_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "59c8efc7-6a7b-405c-bd19-cfe7c6a76d33", "metadata": {}, "outputs": [], "source": ["oos_base_df[[\"SLOT C_grn_quantity\", \"SLOT C_sto_quantity\"]] = (\n", "    oos_base_df[[\"SLOT C_grn_quantity\", \"SLOT C_sto_quantity\"]].fillna(0).astype(int)\n", ")\n", "oos_base_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d95d9296-34e6-4fc4-babf-0ffc48dc7bee", "metadata": {}, "outputs": [], "source": ["oos_base_df[~oos_base_df[\"SLOT C_grn_time_\"].isna()].head()"]}, {"cell_type": "code", "execution_count": null, "id": "8f2d7e6e-3ac6-4074-a048-4f94a983f30a", "metadata": {}, "outputs": [], "source": ["cols_to_move = [\"SLOT C_grn_quantity\", \"SLOT C_grn_time_\", \"SLOT C_sto_quantity\"]\n", "\n", "# Get the rest of the columns in the original order, excluding the ones to move\n", "other_cols = [col for col in oos_base_df.columns if col not in cols_to_move]\n", "\n", "# Create new column order\n", "new_column_order = other_cols + cols_to_move\n", "\n", "# Reorder the DataFrame\n", "oos_base_df = oos_base_df[new_column_order]\n", "oos_base_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8e0d8f47-fd47-4f29-9ef1-c4000b4f86a1", "metadata": {}, "outputs": [], "source": ["grn_time_cols = [\"SLOT A_grn_time_\", \"SLOT B_grn_time_\", \"SLOT C_grn_time_\"]\n", "oos_base_df[grn_time_cols] = oos_base_df[grn_time_cols].fillna(\"-\")\n", "oos_base_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b85fd8c9-3ac9-45b2-9d0d-ec3732742457", "metadata": {}, "outputs": [], "source": ["attempts = 2\n", "\n", "for attempt in range(attempts):\n", "    try:\n", "        pb.to_sheets(oos_base_df, \"1pIEInYj2XyvbwjLIdbnZOZ1w7n8HQwUFm8csGuw0Ivg\", \"oos_rca_today\")\n", "        print(\"Data successfully written to sheets.\")\n", "        break  # If successful, exit the loop\n", "    except Exception as e:\n", "        # If an error occurs, print the error and try again if there are remaining attempts\n", "        print(f\"Attempt {attempt + 1} failed: {e}\")\n", "        if attempt == attempts - 1:\n", "            print(\"Skipping the operation after 2 failed attempts.\")\n", "        else:\n", "            print(\"Retrying...\")"]}, {"cell_type": "code", "execution_count": null, "id": "2b8196ed-ad0a-444c-8a9d-cafb7bcc3952", "metadata": {}, "outputs": [], "source": ["# oos_base_df.to_csv(\"oos_base_df.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "ba176429-c477-484d-88bf-29cb94f825cb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "abee76d3-8ce1-428b-8c25-9846d0b2ae53", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}