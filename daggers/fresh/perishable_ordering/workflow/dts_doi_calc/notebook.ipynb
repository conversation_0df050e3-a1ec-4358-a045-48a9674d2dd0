{"cells": [{"cell_type": "code", "execution_count": null, "id": "bf632b1a", "metadata": {"papermill": {"duration": 0.079588, "end_time": "2025-05-28T00:06:12.667192", "exception": false, "start_time": "2025-05-28T00:06:12.587604", "status": "completed"}, "tags": ["injected-parameters"]}, "outputs": [], "source": ["# Parameters\n", "cwd = \"/usr/local/airflow/dags/repo/dags/fresh/perishable_forecasting/workflow/backend_doi\""]}, {"cell_type": "markdown", "id": "144b6c26-73d8-44e2-a372-bdd91e608d15", "metadata": {"papermill": {"duration": 0.047524, "end_time": "2025-05-28T00:06:12.783633", "exception": false, "start_time": "2025-05-28T00:06:12.736109", "status": "completed"}, "tags": []}, "source": ["### Import Libraries"]}, {"cell_type": "code", "execution_count": null, "id": "a0bab353-3fe4-410a-bae0-bea6774869f0", "metadata": {"papermill": {"duration": 2.014253, "end_time": "2025-05-28T00:06:14.858024", "exception": false, "start_time": "2025-05-28T00:06:12.843771", "status": "completed"}, "tags": []}, "outputs": [], "source": ["import pencilbox as pb\n", "import requests\n", "import pandas as pd\n", "from datetime import datetime, date, timedelta\n", "import time\n", "from pytz import timezone\n", "import boto3\n", "import numpy as np\n", "\n", "# try:\n", "#     from noto.settings import settings\n", "# except Exception as e:\n", "#     print(\"Executing on jhub\")"]}, {"cell_type": "markdown", "id": "1eda932f-255b-4775-9d0d-fe90100ed0e5", "metadata": {"papermill": {"duration": 0.04353, "end_time": "2025-05-28T00:06:14.947421", "exception": false, "start_time": "2025-05-28T00:06:14.903891", "status": "completed"}, "tags": []}, "source": ["### Setup Trino Connection"]}, {"cell_type": "code", "execution_count": null, "id": "048f0a47-cdb6-4c99-b7aa-68ebff4b2c8b", "metadata": {"papermill": {"duration": 0.298676, "end_time": "2025-05-28T00:06:15.316456", "exception": false, "start_time": "2025-05-28T00:06:15.017780", "status": "completed"}, "tags": []}, "outputs": [], "source": ["CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "pd.options.mode.chained_assignment = None  # default='warn'\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "64037554-e0ae-4e1e-9b4f-a2f80214ca11", "metadata": {"papermill": {"duration": 0.087269, "end_time": "2025-05-28T00:06:15.464694", "exception": false, "start_time": "2025-05-28T00:06:15.377425", "status": "completed"}, "tags": []}, "outputs": [], "source": ["ist = timezone(\"Asia/Kolkata\")\n", "start_time = datetime.now(ist).strftime(\"%H:%M\")\n", "start_time"]}, {"cell_type": "code", "execution_count": null, "id": "4332295b-7d06-46bf-afb7-8e8fc2020fe8", "metadata": {"papermill": {"duration": 0.068802, "end_time": "2025-05-28T00:06:15.600699", "exception": false, "start_time": "2025-05-28T00:06:15.531897", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(\n", "            f\"Read attempt: {attempt}... \\nRun started at {datetime.now() + timed<PERSON>ta(hours = 5.5)}\"\n", "        )\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "5174d0eb-eee4-4e2f-afb0-7576549a08d2", "metadata": {"papermill": {"duration": 446.216154, "end_time": "2025-05-28T00:13:41.876495", "exception": false, "start_time": "2025-05-28T00:06:15.660341", "status": "completed"}, "tags": []}, "outputs": [], "source": ["update_backend_doi_df = read_sql_query(\n", "    f\"\"\"\n", "    \n", "       with outlets as (\n", "    select * from supply_etls.outlet_details\n", "    where ars_check = 1\n", "),\n", "\n", "co as (\n", "    select id as outlet_id, facility_id from retail.console_outlet where lake_active_record and active = 1\n", "),\n", "\n", "\n", "id AS (select \n", "        item_id,item_name,parent_item_id,new_parent_item_id,variant_mrp,manufacturer_id,manufacturer_name,brand_id,brand_name,\n", "        l0_id,l0_category,l1_id,l1_category,l2_id,l2_category,p_type,weight_in_kg,shelf_life,inward_guidelines,outward_guidelines,\n", "        inner_case_size,outer_case_size,handling_type_id,handling_type,storage_type_id,storage_type,mrp,\n", "        assortment_type2 as assortment_type\n", "    \n", "    FROM \n", "            \n", "        (SELECT a.*,\n", "               CASE\n", "                   WHEN assortment_type IN ('Perishable') THEN 'Perishable'\n", "                   WHEN assortment_type IN ('Meat') THEN 'Perishable'\n", "               END AS assortment_type2\n", "        FROM\n", "          (SELECT *,\n", "                  variant_mrp AS mrp\n", "           FROM supply_etls.item_details\n", "           WHERE l2_id IN (130,1959,477,943,1182,1827,33,89,108,3542,952,36,2466,342,176,1933,80,964,1425,1956,31,198,949,1389,1778,63,1367,1369,1201,3113,35,950,138,1091,1093,116,1097,2633,1094)\n", "           ) a)\n", "),\n", "\n", "\n", "iotm as (\n", "    select tag_value, outlet_id, item_id from rpc.item_outlet_tag_mapping\n", "    where lake_active_record and active = 1 and tag_type_id = 8\n", "    \n", "    union\n", "    \n", "    select tag_value, outlet_id, item_id from rpc.item_outlet_tag_mapping\n", "    where lake_active_record and active = 1 and tag_type_id = 9\n", "),\n", "\n", "pfma as (\n", "     select pfma.facility_id, pfma.item_id from rpc.product_facility_master_assortment pfma\n", " inner join retail.console_outlet c on c.facility_id = pfma.facility_id\n", " inner join retail.console_location ca on ca.id = c.tax_location_id\n", "where pfma.lake_active_record and pfma.active = 1 \n", "and pfma.master_assortment_substate_id = 1 \n", "),\n", "\n", "cpd as (\n", "    select cpd.item_id, cpd.facility_id, avg(cpd.cpd) as cpd\n", "    from supply_etls.perishable_datewise_cpd cpd\n", "    inner join rpc.item_category_details icd on icd.item_id = cpd.item_id and l2_id IN (130,1959,477,943,1182,1827,33,89,108,3542,952,36,2466,342,176,1933,80,964,1425,1956,31,198,949,1389,1778,63,1367,1369,1201,3113,35,950,138,1091,1093,116,1097,2633,1094)\n", "    where for_date >= current_date - interval '5' day\n", "    group by 1,2\n", "),\n", "\n", "ds_details as (\n", "    select pfma.item_id, pfma.facility_id, iotm.outlet_id, iotm.tag_value, co.facility_id as be_facility_id, cpd\n", "    from pfma\n", "    inner join outlets o on o.facility_id = pfma.facility_id\n", "    inner join iotm on iotm.item_id = pfma.item_id and iotm.outlet_id = o.hot_outlet_id\n", "    inner join co on iotm.tag_value = cast(co.outlet_id as varchar)\n", "    left join cpd on cpd.item_id = pfma.item_id and cpd.facility_id = pfma.facility_id \n", "    inner join rpc.item_category_details icd on icd.item_id = pfma.item_id and icd.l2_id in (130,1959,477,943,1182,1827,33,89,108,3542,952,36,2466,342,176,1933,80,964,1425,1956,31,198,949,1389,1778,63,1367,1369,1201,3113,35,950,138,1091,1093,116,1097,2633,1094)\n", "),\n", "\n", "be_details as (\n", "    select facility_id as facility_id, item_id, sum(cpd) as avg_cpd\n", "    from ds_details\n", "    group by 1,2\n", "),\n", "\n", "vfa as (\n", "    select vfa.* from vms.vms_vendor_facility_alignment vfa\n", "    where lake_active_record and active = 1 \n", "),\n", "\n", "vpi as (\n", "    select * from vms.vms_vendor_new_pi_logic\n", "    where lake_active_record and active\n", "),\n", "\n", "vm as (\n", "    select * from vms.vms_vendor\n", "    where lake_active_record and active = 1\n", "),\n", "\n", " rtm as (\n", "    select * from rpc.tot_margin\n", "    where lake_active_record and active = 1\n", "),\n", "\n", " poi as (\n", "    select * from po.purchase_order_items\n", "    where lake_active_record\n", "),\n", "\n", " po as (\n", "    select * from po.purchase_order\n", "    where lake_active_record\n", "),\n", "\n", "lp_core as (\n", "    select rtm.item_id, rtm.facility_id, \n", "           case when (rtm.landing_price is null) or (rtm.landing_price = 0) then lola.landing_rate\n", "                else rtm.landing_price\n", "           end as lp\n", "    from rtm\n", "    inner join (select item_id, facility_id, max(updated_at) as updated_at from rtm group by 1,2\n", "               ) rm on rm.item_id = rtm.item_id and rm.facility_id = rtm.facility_id and rm.updated_at = rtm.updated_at\n", "    left join (\n", "                select poi.item_id, o.facility_id, poi.landing_rate\n", "                from po\n", "                inner join poi on po.id = poi.po_id\n", "                inner join outlets o on o.hot_outlet_id = po.outlet_id\n", "                inner join (\n", "                                select item_id, facility_id, max(poi.created_at) as created_at\n", "                                from po\n", "                                inner join poi on poi.po_id = po.id\n", "                                inner join outlets o on o.hot_outlet_id = po.outlet_id\n", "                                group by 1,2\n", "                           ) popo on popo.item_id = poi.item_id and popo.facility_id = o.facility_id and popo.created_at = poi.created_at\n", "\n", "              ) lola on lola.item_id = rtm.item_id and lola.facility_id = rtm.facility_id\n", "),\n", "\n", "\n", "backend_view as (\n", "    select be.facility_id, be.item_id, id.item_name, id.assortment_type, id.manufacturer_id, id.manufacturer_name, \n", "           vfa.vendor_id, vm.vendor_name, vpi.po_days, vpi.po_cycle, \n", "           vpi.load_size, vpi.load_type, vpi.group_id, \n", "           case when lc.lp is null or lc.lp = 0 then id.mrp * 0.75 else lc.lp end as lp,\n", "           id.weight_in_kg as uom, id.shelf_life, \n", "           case when vfa.case_sensitivity_type is null then 0 else vfa.case_sensitivity_type end as case_sensitivity,\n", "           case when (vfa.case_sensitivity_type = 0) or (vfa.case_sensitivity_type is null) then 1\n", "                when vfa.case_sensitivity_type = 1 then id.inner_case_size\n", "                else id.outer_case_size\n", "           end as case_size,\n", "           case when (vpi.tat_day is null) or (vpi.tat_day = 0) then 2 else vpi.tat_day end as tat,\n", "           vpi.tat_day as vendor_tat,\n", "           avg_cpd\n", "    from be_details be\n", "    inner join id on id.item_id = be.item_id\n", "    left join vfa on vfa.item_id = id.item_id and vfa.facility_id = be.facility_id\n", "    left join vm on vm.id = vfa.vendor_id\n", "    left join vpi on vpi.vendor_id = vfa.vendor_id and vpi.group_id = vfa.group_id and vpi.facility_id = vfa.facility_id\n", "    left join lp_core lc on lc.item_id = be.item_id and lc.facility_id = be.facility_id\n", "),\n", "\n", "final_view as (\n", "    select facility_id, item_id, item_name, assortment_type, manufacturer_id, manufacturer_name, vendor_id, vendor_name, group_id, load_size, load_type, \n", "           case when po_days is null then 'Monday,Tuesday,Wednesday,Thursday,Friday' else po_days end as po_days,\n", "           po_cycle, case_sensitivity, case_size, vendor_tat, lp, uom, shelf_life, avg_cpd, \n", "           case when load_type = 1 then lp\n", "                when load_type = 2 then uom\n", "                when load_type = 3 then case_size\n", "                else null\n", "           end as item_load_type_value,\n", "           case_size * 1.00 / nullif(avg_cpd, 0) as single_case_doi, avg_cpd * 30.00 as monthly_forecast,\n", "           case when load_type = 1 then avg_cpd * 30.00 * lp\n", "                when load_type = 2 then avg_cpd * 30.00 * uom\n", "                when load_type = 3 then (avg_cpd * 30.00) / case_size\n", "                else 0\n", "           end as vendor_value,\n", "           sum(case when load_type = 1 then avg_cpd * 30.00 * lp\n", "                when load_type = 2 then avg_cpd * 30.00 * uom\n", "                when load_type = 3 then (avg_cpd * 30.00) / case_size\n", "                else 0\n", "               end) over (partition by vendor_id, facility_id, group_id) as vendor_load_meet,\n", "           sum(case when load_type = 1 then avg_cpd * 30.00 * lp\n", "                when load_type = 2 then avg_cpd * 30.00 * uom\n", "                when load_type = 3 then (avg_cpd * 30.00) / case_size\n", "                else 0\n", "               end) over (partition by vendor_id, facility_id, group_id) * 1.00 / load_size as vendor_ratio\n", "    from backend_view\n", ")\n", "\n", "\n", "select facility_id, item_id, item_name, assortment_type, manufacturer_id, manufacturer_name, vendor_id, vendor_name, group_id, load_size, load_type, \n", "       po_days, po_cycle, vendor_tat, shelf_life, item_load_type_value, sum(monthly_forecast) as monthly_forecast,\n", "       sum(vendor_value) as vendor_value, vendor_ratio, \n", "       case when (load_size is null) or (load_size = 0) then 1\n", "            when (vendor_ratio is null) or (vendor_ratio = 0) then 1\n", "            when vendor_ratio >= 30 then 1\n", "            when vendor_ratio >= 15 and vendor_ratio < 30 then 2\n", "            when vendor_ratio >= 9 and vendor_ratio < 15 then 3\n", "            else 5\n", "       end as po_cycle_to_be_updated\n", "from final_view\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,19,20\n", "\n", "\n", "    \n", "    \n", "                    \"\"\",\n", "    CON_TRINO,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e0cf3fec-51f8-4267-b827-e03b1bdfcef5", "metadata": {"papermill": {"duration": 0.09021, "end_time": "2025-05-28T00:13:42.022500", "exception": false, "start_time": "2025-05-28T00:13:41.932290", "status": "completed"}, "tags": []}, "outputs": [], "source": ["update_backend_doi_df.groupby([\"assortment_type\"]).agg({\"item_id\": \"count\"}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "02ba85f6-4a93-4804-83fe-570fcac9e4ee", "metadata": {"papermill": {"duration": 0.075884, "end_time": "2025-05-28T00:13:42.141794", "exception": false, "start_time": "2025-05-28T00:13:42.065910", "status": "completed"}, "tags": []}, "outputs": [], "source": ["len(update_backend_doi_df), update_backend_doi_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "93da6db0-a492-4d25-9458-9c88e0608055", "metadata": {"papermill": {"duration": 0.105972, "end_time": "2025-05-28T00:13:42.319995", "exception": false, "start_time": "2025-05-28T00:13:42.214023", "status": "completed"}, "tags": []}, "outputs": [], "source": ["len(update_backend_doi_df.drop_duplicates(subset=[\"facility_id\", \"item_id\"]))"]}, {"cell_type": "code", "execution_count": null, "id": "bc015d91-e9b9-442d-b3fa-0692a01ebd38", "metadata": {"papermill": {"duration": 0.079831, "end_time": "2025-05-28T00:13:42.462538", "exception": false, "start_time": "2025-05-28T00:13:42.382707", "status": "completed"}, "tags": []}, "outputs": [], "source": ["time_ist = datetime.now() - timedelta(days=0) + timedelta(hours=10.5)\n", "today = time_ist.date()\n", "\n", "DAYS_OF_WEEK = {\n", "    \"MONDAY\": 0,\n", "    \"TUESDAY\": 1,\n", "    \"WEDNESDAY\": 2,\n", "    \"THURSDAY\": 3,\n", "    \"FRIDAY\": 4,\n", "    \"SATURDAY\": 5,\n", "    \"SUNDAY\": 6,\n", "}\n", "\n", "today"]}, {"cell_type": "markdown", "id": "54e4ac10-3516-4291-86a9-93a67be994e2", "metadata": {"papermill": {"duration": 0.069094, "end_time": "2025-05-28T00:13:42.577737", "exception": false, "start_time": "2025-05-28T00:13:42.508643", "status": "completed"}, "tags": []}, "source": ["### Backend DOI calculation"]}, {"cell_type": "code", "execution_count": null, "id": "63ccf02f-be7c-4584-9b14-e60fa8d6bfff", "metadata": {"papermill": {"duration": 0.057632, "end_time": "2025-05-28T00:13:42.711227", "exception": false, "start_time": "2025-05-28T00:13:42.653595", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def doi_basis_po_days(po_days_str):\n", "    doi = None\n", "    po_days_str = po_days_str.strip()\n", "    po_days = [each_day.strip().upper() for each_day in po_days_str.split(\",\")]\n", "    today_dow = today.strftime(\"%A\").upper()\n", "    po_days_sorted = sorted(po_days, key=DAYS_OF_WEEK.get)\n", "\n", "    # If there's only one day in the list, the interval is always 7 days (a full week).\n", "    if len(po_days) == 1:\n", "        doi = 7\n", "        return doi\n", "\n", "    # Calculate the interval to the next closest day, even if today_dow does not match a day in po_days_sorted\n", "    next_day_found = False\n", "    for idx, po_day in enumerate(po_days_sorted):\n", "        if DAYS_OF_WEEK[po_day] > DAYS_OF_WEEK[today_dow]:\n", "            doi = (DAYS_OF_WEEK[po_day] - DAYS_OF_WEEK[today_dow]) % 7\n", "            next_day_found = True\n", "            break\n", "\n", "    # If no next day is found in the current week, calculate the interval for the next week\n", "    if not next_day_found:\n", "        next_po_day = po_days_sorted[0]\n", "        doi = (DAYS_OF_WEEK[next_po_day] - DAYS_OF_WEEK[today_dow]) % 7\n", "\n", "    return doi"]}, {"cell_type": "code", "execution_count": null, "id": "20b8457d-cf6a-4cb0-8697-b8505be88aeb", "metadata": {"papermill": {"duration": 0.542861, "end_time": "2025-05-28T00:13:43.321679", "exception": false, "start_time": "2025-05-28T00:13:42.778818", "status": "completed"}, "tags": []}, "outputs": [], "source": ["update_backend_doi_df[\"doi_po_days_basis\"] = update_backend_doi_df[\"po_days\"].apply(\n", "    lambda x: doi_basis_po_days(x)\n", ")\n", "update_backend_doi_df[\"doi_po_days_basis\"] = update_backend_doi_df[\"doi_po_days_basis\"].fillna(0)\n", "# update_backend_doi_df[\"final_backend_doi\"] = np.maximum(update_backend_doi_df[\"doi_po_days_basis\"], update_backend_doi_df[\"po_cycle_to_be_updated\"])\n", "update_backend_doi_df[\"final_backend_doi\"] = np.where(\n", "    update_backend_doi_df[\"assortment_type\"] == \"Perishable\",\n", "    update_backend_doi_df[\"doi_po_days_basis\"],\n", "    np.maximum(\n", "        update_backend_doi_df[\"doi_po_days_basis\"],\n", "        update_backend_doi_df[\"po_cycle_to_be_updated\"],\n", "    ),\n", ")\n", "\n", "update_backend_doi_df[\"final_backend_doi\"] = np.where(\n", "    update_backend_doi_df[\"final_backend_doi\"] < 1,\n", "    1,\n", "    update_backend_doi_df[\"final_backend_doi\"],\n", ")"]}, {"cell_type": "markdown", "id": "72067193-acb7-4ce5-ae0f-2c8ccd4e0799", "metadata": {"papermill": {"duration": 0.053636, "end_time": "2025-05-28T00:13:43.434703", "exception": false, "start_time": "2025-05-28T00:13:43.381067", "status": "completed"}, "tags": []}, "source": ["### Capping of <PERSON><PERSON> life"]}, {"cell_type": "code", "execution_count": null, "id": "a984e209-4126-4793-acd9-544325c39359", "metadata": {"papermill": {"duration": 1.307456, "end_time": "2025-05-28T00:13:44.808764", "exception": false, "start_time": "2025-05-28T00:13:43.501308", "status": "completed"}, "tags": []}, "outputs": [], "source": ["import math\n", "\n", "\n", "def cap_by_shelf_life(row):\n", "    return min(row[\"final_backend_doi\"], math.floor(0.5 * row[\"shelf_life\"]))\n", "\n", "\n", "update_backend_doi_df[\"final_backend_doi\"] = update_backend_doi_df.apply(\n", "    lambda x: cap_by_shelf_life(x), axis=1\n", ")"]}, {"cell_type": "markdown", "id": "f7abd12a-62df-4271-85ca-9e364a09832c", "metadata": {"papermill": {"duration": 0.048837, "end_time": "2025-05-28T00:13:44.920488", "exception": false, "start_time": "2025-05-28T00:13:44.871651", "status": "completed"}, "tags": []}, "source": ["### Set default Columns and rename"]}, {"cell_type": "code", "execution_count": null, "id": "d315f8d1-6f3f-4c31-b61f-49b5d0b6e5d3", "metadata": {"papermill": {"duration": 0.079744, "end_time": "2025-05-28T00:13:45.046202", "exception": false, "start_time": "2025-05-28T00:13:44.966458", "status": "completed"}, "tags": []}, "outputs": [], "source": ["update_backend_doi_df = update_backend_doi_df[[\"item_id\", \"facility_id\", \"final_backend_doi\"]]\n", "update_backend_doi_df.rename(columns={\"final_backend_doi\": \"trigger_doi\"}, inplace=True)\n", "update_backend_doi_df[\"active\"] = 1"]}, {"cell_type": "code", "execution_count": null, "id": "0a393a6d-586f-46ac-a637-a65b999e1281", "metadata": {"papermill": {"duration": 0.06942, "end_time": "2025-05-28T00:13:45.168608", "exception": false, "start_time": "2025-05-28T00:13:45.099188", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# Adjustment because we add demand on PO receiving day as well\n", "\n", "update_backend_doi_df[\"trigger_doi\"] = update_backend_doi_df[\"trigger_doi\"] - 1"]}, {"cell_type": "code", "execution_count": null, "id": "c2029c91-139a-4200-a5e1-1f9d09b70b80", "metadata": {"papermill": {"duration": 0.088166, "end_time": "2025-05-28T00:13:45.311306", "exception": false, "start_time": "2025-05-28T00:13:45.223140", "status": "completed"}, "tags": []}, "outputs": [], "source": ["update_backend_doi_df[\"updated_at\"] = pd.to_datetime(datetime.now() + timedelta(hours=5.5))\n", "update_backend_doi_df.rename(columns={\"trigger_doi\": \"backend_doi\"}, inplace=True)"]}, {"cell_type": "markdown", "id": "f02ae566-30a9-4fef-81b1-8fd4b06171d4", "metadata": {"papermill": {"duration": 0.04588, "end_time": "2025-05-28T00:13:45.406092", "exception": false, "start_time": "2025-05-28T00:13:45.360212", "status": "completed"}, "tags": []}, "source": ["### no supply days handling"]}, {"cell_type": "code", "execution_count": null, "id": "450c6de3-a084-4791-9a4b-3cfc0e5e5b33", "metadata": {"papermill": {"duration": 144.401811, "end_time": "2025-05-28T00:16:09.865836", "exception": false, "start_time": "2025-05-28T00:13:45.464025", "status": "completed"}, "tags": []}, "outputs": [], "source": ["no_supply_df = read_sql_query(\n", "    f\"\"\"\n", "                 SELECT facility_id AS backend_facility_id,\n", "                   vendor_id,\n", "                   no_supply_days as no_supply_day,\n", "           group_id\n", "    FROM vms.vms_vendor_new_pi_logic\n", "    WHERE active = TRUE\n", "      AND lake_active_record = TRUE\n", "      AND (\n", "          lower(no_supply_days) LIKE '%%sun%%'\n", "          OR lower(no_supply_days) LIKE '%%mon%%'\n", "          OR lower(no_supply_days) LIKE '%%tue%%'\n", "          OR lower(no_supply_days) LIKE '%%wed%%'\n", "          OR lower(no_supply_days) LIKE '%%thu%%'\n", "          OR lower(no_supply_days) LIKE '%%fri%%'\n", "          OR lower(no_supply_days) LIKE '%%sat%%'\n", "      )\n", "      \n", "                \"\"\",\n", "    CON_TRINO,\n", ")\n", "\n", "\n", "vendor_list = tuple(list(no_supply_df[\"vendor_id\"].unique().astype(int)))\n", "facility_list = tuple(list(no_supply_df[\"backend_facility_id\"].unique().astype(int)))\n", "\n", "vfa_df = read_sql_query(\n", "    f\"\"\" \n", "                select cast(vfa.facility_id as varchar) || cast(vfa.vendor_id as varchar) || cast(vfa.item_id as varchar) || cast(vpl.group_id as varchar) as union_, vfa.facility_id, vfa.vendor_id, vfa.item_id, vpl.tat_day as vendor_tat, \n", "                vpl.po_days, vpl.po_cycle, vpl.group_id\n", "                from vms.vms_vendor_facility_alignment vfa\n", "                inner join vms.vms_vendor_new_pi_logic vpl on vpl.vendor_id = vfa.vendor_id and vpl.facility_id = vfa.facility_id and vpl.group_id = vfa.group_id and vpl.active = true\n", "                inner join (select distinct item_id from rpc.product_product where id in (select max(id) from rpc.product_product where active = 1 and approved = 1 and perishable = 1 and lake_active_record group by item_id)) pp on pp.item_id = vfa.item_id\n", "                inner join rpc.item_category_details icd on icd.item_id = vfa.item_id and icd.l0_id != 1487\n", "                where vfa.active = 1 and vfa.lake_active_record and vpl.lake_active_record and icd.lake_active_record and vfa.vendor_id in {vendor_list} and vfa.facility_id in {facility_list}\n", "            \"\"\",\n", "    CON_TRINO,\n", ")\n", "\n", "vfa_df = vfa_df.rename(columns={\"union_\": \"union\"})\n", "vfa_df = pd.merge(\n", "    vfa_df,\n", "    no_supply_df.rename(columns={\"backend_facility_id\": \"facility_id\"}),\n", "    on=[\"vendor_id\", \"facility_id\", \"group_id\"],\n", "    how=\"inner\",\n", ")\n", "vfa_df[\"po_days\"] = (vfa_df[\"po_days\"].str.lower()).str.strip()\n", "vfa_df[\"no_supply_day\"] = (vfa_df[\"no_supply_day\"].str.lower()).str.strip()\n", "\n", "vfa_df[\"po_days\"] = vfa_df[\"po_days\"].str.replace(\" \", \"\").apply(lambda x: x.split(\",\"))\n", "m = [\"monday\", \"tuesday\", \"wednesday\", \"thursday\", \"friday\", \"saturday\", \"sunday\"]\n", "vfa_df[\"po_days\"] = vfa_df[\"po_days\"].apply(lambda x: sorted(x, key=m.index))\n", "vfa_df[\"po_days\"] = vfa_df[\"po_days\"].apply(\n", "    lambda x: str(x).replace(\"[\", \"\").replace(\"]\", \"\").replace(\"'\", \"\").replace(\" \", \"\")\n", ")\n", "\n", "vfa_df"]}, {"cell_type": "code", "execution_count": null, "id": "a03c43a4-c07d-4b4a-a030-125516b70fd0", "metadata": {"papermill": {"duration": 0.06884, "end_time": "2025-05-28T00:16:10.012141", "exception": false, "start_time": "2025-05-28T00:16:09.943301", "status": "completed"}, "tags": []}, "outputs": [], "source": ["vfa_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a7b6d7d9-9117-4a47-ad7c-cae1272830b1", "metadata": {"papermill": {"duration": 0.554953, "end_time": "2025-05-28T00:16:10.616855", "exception": false, "start_time": "2025-05-28T00:16:10.061902", "status": "completed"}, "tags": []}, "outputs": [], "source": ["n_days = 100\n", "df_po_days = pd.DataFrame(\n", "    [(i + 1) % 7 for i in range(n_days)] for i in range(vfa_df[\"union\"].nunique())\n", ")\n", "df_po_days = df_po_days.rename(\n", "    columns=dict(zip(np.arange(0, n_days), [f\"day_{i}\" for i in np.arange(0, n_days)]))\n", ")\n", "df_po_days = df_po_days.replace(0, \"sunday\")\n", "df_po_days = df_po_days.replace(1, \"monday\")\n", "df_po_days = df_po_days.replace(2, \"tuesday\")\n", "df_po_days = df_po_days.replace(3, \"wednesday\")\n", "df_po_days = df_po_days.replace(4, \"thursday\")\n", "df_po_days = df_po_days.replace(5, \"friday\")\n", "df_po_days = df_po_days.replace(6, \"saturday\")\n", "union_df = vfa_df[\"union\"]\n", "item_df = vfa_df[\"item_id\"]\n", "vendor_df = vfa_df[\"vendor_id\"]\n", "facility_df = vfa_df[\"facility_id\"]\n", "df_po_days.insert(0, \"union\", union_df)\n", "df_po_days.insert(1, \"item_id\", item_df)\n", "df_po_days.insert(2, \"vendor_id\", vendor_df)\n", "df_po_days.insert(3, \"facility_id\", facility_df)\n", "\n", "\n", "df_supply_days = pd.DataFrame([0 for i in range(n_days)] for i in range(vfa_df[\"union\"].nunique()))\n", "df_supply_days = df_supply_days.rename(\n", "    columns=dict(zip(np.arange(0, n_days), [f\"supply_{i}\" for i in np.arange(0, n_days)]))\n", ")\n", "union_df = vfa_df[\"union\"]\n", "item_df = vfa_df[\"item_id\"]\n", "vendor_df = vfa_df[\"vendor_id\"]\n", "facility_df = vfa_df[\"facility_id\"]\n", "df_supply_days.insert(0, \"union\", union_df)\n", "df_supply_days.insert(1, \"item_id\", item_df)\n", "df_supply_days.insert(2, \"vendor_id\", vendor_df)\n", "df_supply_days.insert(3, \"facility_id\", facility_df)\n", "\n", "\n", "core_pro_df = (vfa_df.merge(df_po_days)).merge(df_supply_days)"]}, {"cell_type": "code", "execution_count": null, "id": "c839a393-df80-4a98-929a-03f894ce4091", "metadata": {"papermill": {"duration": 0.124856, "end_time": "2025-05-28T00:16:10.791288", "exception": false, "start_time": "2025-05-28T00:16:10.666432", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def parallel_proc(core_pro_df):\n", "    core_pro_df = core_pro_df.iloc[0].to_dict()\n", "    for i in range(n_days):\n", "        x = 0\n", "        # print(type(core_pro_df[f'po_days']))\n", "        if core_pro_df[f\"day_{i}\"] in core_pro_df[f\"po_days\"]:\n", "            x = int(core_pro_df[f\"vendor_tat\"])\n", "            # print('first entry point', i)\n", "            ps_counter = 0\n", "\n", "            for j in range(i, n_days - i - 1):\n", "                if core_pro_df[f\"day_{j+x}\"] in core_pro_df[f\"no_supply_day\"]:\n", "                    ps_counter = ps_counter + 1\n", "                else:\n", "                    break\n", "            core_pro_df[f\"supply_{i+x+ps_counter}\"] = 1\n", "\n", "    for i in range(0, n_days):\n", "        sd_value = core_pro_df[f\"supply_{i}\"]\n", "        if sd_value >= 1:\n", "\n", "            counter = 0\n", "            for j in range(i + 1, n_days):\n", "                if core_pro_df[f\"supply_{j}\"] == 0:\n", "                    counter = counter + 1\n", "                else:\n", "                    break\n", "            core_pro_df[f\"supply_{i}\"] = counter + core_pro_df[f\"supply_{i}\"]\n", "\n", "    pd_list = core_pro_df[f\"po_days\"]\n", "    pd_list = pd_list.split(\",\")\n", "    po_cycle = []\n", "\n", "    for i in range(n_days):\n", "        if len(po_cycle) < len(pd_list):\n", "            if core_pro_df[f\"supply_{i}\"] > 0:\n", "                po_cycle.append(core_pro_df[f\"supply_{i}\"])\n", "        else:\n", "            break\n", "\n", "    core_pro_df[\"anuvrat_po_cycle\"] = po_cycle\n", "\n", "    return pd.DataFrame({k: [v] for k, v in core_pro_df.items()})"]}, {"cell_type": "code", "execution_count": null, "id": "59515a9d-4973-4f5f-89e3-1ba68728581d", "metadata": {"papermill": {"duration": 49.108764, "end_time": "2025-05-28T00:16:59.973683", "exception": false, "start_time": "2025-05-28T00:16:10.864919", "status": "completed"}, "tags": []}, "outputs": [], "source": ["ans = core_pro_df.groupby([\"union\"]).apply(parallel_proc)  # 10010618, 10010692\n", "ans = ans.drop(columns={\"union\"}).reset_index().drop(columns={\"level_1\"})\n", "\n", "check_df = ans[\n", "    [\n", "        \"union\",\n", "        \"facility_id\",\n", "        \"vendor_id\",\n", "        \"item_id\",\n", "        \"po_days\",\n", "        \"vendor_tat\",\n", "        \"po_cycle\",\n", "        \"no_supply_day\",\n", "        \"anuvrat_po_cycle\",\n", "    ]\n", "]\n", "check_df[\"po_days\"] = check_df[\"po_days\"].str.split(\",\")\n", "check_df = check_df.reset_index()\n", "\n", "po_day = pd.DataFrame(check_df[\"po_days\"].tolist()).fillna(\"\").add_prefix(\"model_\").reset_index()\n", "po_day = (\n", "    pd.melt(po_day, id_vars=[\"index\"])\n", "    .drop(columns={\"variable\"})\n", "    .rename(columns={\"value\": \"po_day\"})\n", ")\n", "\n", "po_cycle = (\n", "    pd.DataFrame(check_df[\"anuvrat_po_cycle\"].tolist())\n", "    .fillna(\"\")\n", "    .add_prefix(\"model_\")\n", "    .reset_index()\n", ")\n", "po_cycle = (\n", "    pd.melt(po_cycle, id_vars=[\"index\"])\n", "    .drop(columns={\"variable\"})\n", "    .rename(columns={\"value\": \"anuvrat_po_cycle\", \"index\": \"index2\"})\n", ")\n", "\n", "check_df = check_df[[\"index\", \"facility_id\", \"item_id\"]]\n", "print(check_df.shape)\n", "po_cycle_days = pd.concat([po_day, po_cycle], axis=1)\n", "check_df = pd.merge(check_df, po_cycle_days, on=[\"index\"], how=\"inner\").drop(\n", "    columns={\"index\", \"index2\"}\n", ")\n", "print(check_df.shape)\n", "check_df = check_df.replace(\"\", np.nan).dropna().reset_index().drop(columns={\"index\"})\n", "check_df = (\n", "    check_df[\n", "        check_df[\"po_day\"]\n", "        == (\n", "            (\n", "                pd.to_datetime(datetime.today() + timedelta(hours=10.5) + timedelta(days=0))\n", "            ).day_name()\n", "        ).lower()\n", "    ]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "check_df[\"anuvrat_po_cycle\"] = check_df[\"anuvrat_po_cycle\"].astype(int)\n", "check_df[\"anuvrat_po_cycle\"] = np.where(\n", "    check_df[\"anuvrat_po_cycle\"] - 1 < 0, 0, check_df[\"anuvrat_po_cycle\"] - 1\n", ")\n", "# # check_df"]}, {"cell_type": "code", "execution_count": null, "id": "23ee2d32-be9e-44a6-9225-3193b5661a1c", "metadata": {"papermill": {"duration": 0.097914, "end_time": "2025-05-28T00:17:00.143061", "exception": false, "start_time": "2025-05-28T00:17:00.045147", "status": "completed"}, "tags": []}, "outputs": [], "source": ["print(\"Before\", update_backend_doi_df.shape)\n", "update_backend_doi_df_new = pd.merge(\n", "    update_backend_doi_df, check_df, on=[\"facility_id\", \"item_id\"], how=\"left\"\n", ").drop(columns={\"po_day\"})\n", "update_backend_doi_df_new[\"backend_doi\"] = np.where(\n", "    update_backend_doi_df_new[\"anuvrat_po_cycle\"].isna() == False,\n", "    update_backend_doi_df_new[\"anuvrat_po_cycle\"],\n", "    update_backend_doi_df_new[\"backend_doi\"],\n", ")\n", "update_backend_doi_df_new = update_backend_doi_df_new.drop(columns={\"anuvrat_po_cycle\"})\n", "print(\"After\", update_backend_doi_df_new.shape)\n", "update_backend_doi_df_new"]}, {"cell_type": "code", "execution_count": null, "id": "46c43b1b-10ad-4a2c-84d5-74ddc9258fd6", "metadata": {"papermill": {"duration": 0.059913, "end_time": "2025-05-28T00:17:00.260036", "exception": false, "start_time": "2025-05-28T00:17:00.200123", "status": "completed"}, "tags": []}, "outputs": [], "source": ["update_backend_doi_df_new.backend_doi.min(), update_backend_doi_df_new.backend_doi.max(), check_df.anuvrat_po_cycle.min(), check_df.anuvrat_po_cycle.max()"]}, {"cell_type": "code", "execution_count": null, "id": "91840971-2737-4e38-ac15-f7e0eced9ae9", "metadata": {"papermill": {"duration": 0.078745, "end_time": "2025-05-28T00:17:00.386030", "exception": false, "start_time": "2025-05-28T00:17:00.307285", "status": "completed"}, "tags": []}, "outputs": [], "source": ["len(update_backend_doi_df_new)"]}, {"cell_type": "code", "execution_count": null, "id": "56821b22-0b91-4ba2-b60f-14543b19619b", "metadata": {"papermill": {"duration": 0.068528, "end_time": "2025-05-28T00:17:00.503975", "exception": false, "start_time": "2025-05-28T00:17:00.435447", "status": "completed"}, "tags": []}, "outputs": [], "source": ["update_backend_doi_df_new.query(\"facility_id==1395\")[\"backend_doi\"].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "d1786999-c8ef-47ac-b195-aec5e4085fae", "metadata": {"papermill": {"duration": 0.068197, "end_time": "2025-05-28T00:17:00.625051", "exception": false, "start_time": "2025-05-28T00:17:00.556854", "status": "completed"}, "tags": []}, "outputs": [], "source": ["update_backend_doi_df_new.query(\"backend_doi < 0\")"]}, {"cell_type": "code", "execution_count": null, "id": "867cefcb-43e4-445f-a0d2-bd02df977589", "metadata": {"papermill": {"duration": 0.066894, "end_time": "2025-05-28T00:17:00.741268", "exception": false, "start_time": "2025-05-28T00:17:00.674374", "status": "completed"}, "tags": []}, "outputs": [], "source": ["update_backend_doi_df_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "48c31ef4-b9fa-491a-a7dd-4271426c675e", "metadata": {}, "outputs": [], "source": ["# update_backend_doi_df_new.to_csv('perishable_doi.csv', index = False)"]}, {"cell_type": "code", "execution_count": null, "id": "0073ea1e-09b4-4056-852d-3d8f922ad47e", "metadata": {"papermill": {"duration": 0.107239, "end_time": "2025-05-28T00:17:00.921111", "exception": false, "start_time": "2025-05-28T00:17:00.813872", "status": "completed"}, "tags": []}, "outputs": [], "source": ["x = update_backend_doi_df_new[(update_backend_doi_df_new[\"item_id\"] == 10108991)]\n", "x.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "8116865c-c26e-4cd4-abc9-e69b5918729e", "metadata": {"papermill": {"duration": 0.09252, "end_time": "2025-05-28T00:17:01.081743", "exception": false, "start_time": "2025-05-28T00:17:00.989223", "status": "completed"}, "tags": []}, "outputs": [], "source": ["x = update_backend_doi_df_new[(update_backend_doi_df_new[\"item_id\"] == 10132066)]\n", "x.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "7cda7796-d489-4314-8de0-044684a8435f", "metadata": {}, "outputs": [], "source": ["filtered_df = update_backend_doi_df_new\n", "filtered_df"]}, {"cell_type": "markdown", "id": "e4de9065-bcb2-4dda-aee1-ddfba2a9d02a", "metadata": {"papermill": {"duration": 0.092718, "end_time": "2025-05-28T00:17:01.227771", "exception": false, "start_time": "2025-05-28T00:17:01.135053", "status": "completed"}, "tags": []}, "source": ["### Save Data to Trino table"]}, {"cell_type": "code", "execution_count": null, "id": "dd1afb8f-33a2-4ab3-bd99-92eaa7cd81f5", "metadata": {"execution": {"iopub.execute_input": "2025-05-28T00:17:01.341252Z", "iopub.status.busy": "2025-05-28T00:17:01.340862Z", "iopub.status.idle": "2025-05-28T00:18:12.608561Z", "shell.execute_reply": "2025-05-28T00:18:12.607404Z"}, "papermill": {"duration": 71.333459, "end_time": "2025-05-28T00:18:12.611697", "exception": false, "start_time": "2025-05-28T00:17:01.278238", "status": "completed"}, "tags": []}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"ars_ordering_backend_doi_temp_dts\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"item id\"},\n", "        {\n", "            \"name\": \"facility_id\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"backend facility id\",\n", "        },\n", "        {\"name\": \"backend_doi\", \"type\": \"DOUBLE\", \"description\": \"backend doi\"},\n", "        {\"name\": \"active\", \"type\": \"BOOLEAN\", \"description\": \"backend doi\"},\n", "        {\n", "            \"name\": \"updated_at\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"Updated timestamp\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"item_id\", \"facility_id\"],\n", "    \"sortkey\": [\"item_id\", \"facility_id\"],\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"Stores backend doi for new ARS Ordering items\",\n", "    \"force_upsert_without_increment_check\": True,\n", "}\n", "\n", "pb.to_trino(filtered_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "2f3f9a3f-e5a2-43fc-a1af-3573e98a0971", "metadata": {"execution": {"iopub.execute_input": "2025-05-28T00:18:12.760602Z", "iopub.status.busy": "2025-05-28T00:18:12.760203Z", "iopub.status.idle": "2025-05-28T00:18:55.087624Z", "shell.execute_reply": "2025-05-28T00:18:55.086400Z"}, "papermill": {"duration": 42.387042, "end_time": "2025-05-28T00:18:55.090352", "exception": false, "start_time": "2025-05-28T00:18:12.703310", "status": "completed"}, "tags": []}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"ars_ordering_backend_doi_temp_dts_log\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"item id\"},\n", "        {\n", "            \"name\": \"facility_id\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"backend facility id\",\n", "        },\n", "        {\"name\": \"backend_doi\", \"type\": \"DOUBLE\", \"description\": \"backend doi\"},\n", "        {\"name\": \"active\", \"type\": \"BOOLEAN\", \"description\": \"backend doi\"},\n", "        {\n", "            \"name\": \"updated_at\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"Updated timestamp\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"item_id\", \"facility_id\", \"updated_at\"],\n", "    \"sortkey\": [\"item_id\", \"facility_id\"],\n", "    \"load_type\": \"append\",\n", "    \"table_description\": \"Stores backend doi for new ARS Ordering items\",\n", "    \"force_upsert_without_increment_check\": True,\n", "}\n", "\n", "pb.to_trino(filtered_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "13cf2669-d64a-4b84-99db-572b4404ef9e", "metadata": {"execution": {"iopub.execute_input": "2025-05-28T00:18:55.280991Z", "iopub.status.busy": "2025-05-28T00:18:55.280445Z", "iopub.status.idle": "2025-05-28T00:18:55.292646Z", "shell.execute_reply": "2025-05-28T00:18:55.291095Z"}, "papermill": {"duration": 0.089551, "end_time": "2025-05-28T00:18:55.295528", "exception": false, "start_time": "2025-05-28T00:18:55.205977", "status": "completed"}, "tags": []}, "outputs": [], "source": ["ist = timezone(\"Asia/Kolkata\")\n", "end_time = datetime.now(ist).strftime(\"%H:%M\")\n", "end_time"]}, {"cell_type": "code", "execution_count": null, "id": "cdb6ceaf-d892-4818-aaca-61ffe6b893a7", "metadata": {"execution": {"iopub.execute_input": "2025-05-28T00:18:55.424696Z", "iopub.status.busy": "2025-05-28T00:18:55.424284Z", "iopub.status.idle": "2025-05-28T00:18:55.429775Z", "shell.execute_reply": "2025-05-28T00:18:55.428667Z"}, "papermill": {"duration": 0.062178, "end_time": "2025-05-28T00:18:55.432145", "exception": false, "start_time": "2025-05-28T00:18:55.369967", "status": "completed"}, "tags": []}, "outputs": [], "source": ["df = pd.DataFrame({\"Start Time\": [start_time], \"End Time\": [end_time]})"]}, {"cell_type": "code", "execution_count": null, "id": "178ec099-ab73-4aee-99bf-3dede705efde", "metadata": {"execution": {"iopub.execute_input": "2025-05-28T00:18:55.533942Z", "iopub.status.busy": "2025-05-28T00:18:55.533549Z", "iopub.status.idle": "2025-05-28T00:18:55.543798Z", "shell.execute_reply": "2025-05-28T00:18:55.542646Z"}, "papermill": {"duration": 0.063216, "end_time": "2025-05-28T00:18:55.546081", "exception": false, "start_time": "2025-05-28T00:18:55.482865", "status": "completed"}, "tags": []}, "outputs": [], "source": ["from datetime import time\n", "\n", "df[\"Start Time\"] = pd.to_datetime(df[\"Start Time\"], format=\"%H:%M\").dt.time\n", "df[\"End Time\"] = pd.to_datetime(df[\"End Time\"], format=\"%H:%M\").dt.time\n", "\n", "cut_off_time = time(5, 55)\n", "\n", "df[\"Flag\"] = np.where(df[\"End Time\"] > cut_off_time, \"Run Delay\", \"Run on time\")"]}, {"cell_type": "code", "execution_count": null, "id": "022c1d90-527a-4b5e-8aad-a845ee558093", "metadata": {"execution": {"iopub.execute_input": "2025-05-28T00:18:55.663732Z", "iopub.status.busy": "2025-05-28T00:18:55.663278Z", "iopub.status.idle": "2025-05-28T00:18:55.674768Z", "shell.execute_reply": "2025-05-28T00:18:55.673377Z"}, "papermill": {"duration": 0.080207, "end_time": "2025-05-28T00:18:55.677880", "exception": false, "start_time": "2025-05-28T00:18:55.597673", "status": "completed"}, "tags": []}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "id": "2e0ad059-0a65-45dd-9731-b7f78aae4787", "metadata": {"execution": {"iopub.execute_input": "2025-05-28T00:18:55.892534Z", "iopub.status.busy": "2025-05-28T00:18:55.892119Z", "iopub.status.idle": "2025-05-28T00:18:55.899315Z", "shell.execute_reply": "2025-05-28T00:18:55.898166Z"}, "papermill": {"duration": 0.063659, "end_time": "2025-05-28T00:18:55.902420", "exception": false, "start_time": "2025-05-28T00:18:55.838761", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def send_alert():\n", "    import pencilbox as pb1\n", "\n", "    slack_channel = \"perishable-dag-alerts\"\n", "\n", "    if df.shape[0] > 0:\n", "        # fig, ax = render_mpl_table(df, header_columns=0)\n", "        # fig.savefig(\"perishable_forecast_push.png\")\n", "        # filepath_fig = \"./perishable_forecast_push.png\"\n", "        channel = slack_channel\n", "        # files = [filepath_fig]\n", "\n", "        end_time = df[\"End Time\"].iloc[0]\n", "\n", "        if (df[\"Flag\"] == \"Run Delay\").any():\n", "            text = f\"<@U0832943QP2>, <@U05CCTXLBU1> Delay in Perishable Backend DOI for DTS. End Time: {end_time}\"\n", "        elif (df[\"Flag\"] == \"Run on time\").any():\n", "            text = f\"<@U0832943QP2>, <@U05CCTXLBU1> Perishable Backend DOI on time for DTS. End Time: {end_time}\"\n", "        else:\n", "            text = \"Got an error\"\n", "\n", "        pb1.send_slack_message(channel=channel, text=text)"]}, {"cell_type": "code", "execution_count": null, "id": "69859df4-f706-4bf3-8983-7a43fe00bac4", "metadata": {"execution": {"iopub.execute_input": "2025-05-28T00:18:56.018990Z", "iopub.status.busy": "2025-05-28T00:18:56.018509Z", "iopub.status.idle": "2025-05-28T00:18:56.718141Z", "shell.execute_reply": "2025-05-28T00:18:56.717042Z"}, "papermill": {"duration": 0.755298, "end_time": "2025-05-28T00:18:56.720874", "exception": false, "start_time": "2025-05-28T00:18:55.965576", "status": "completed"}, "tags": []}, "outputs": [], "source": ["send_alert()"]}, {"cell_type": "code", "execution_count": null, "id": "4b60fbfa-b328-417f-88ef-de16494e19f5", "metadata": {"papermill": {"duration": 0.056778, "end_time": "2025-05-28T00:18:56.829172", "exception": false, "start_time": "2025-05-28T00:18:56.772394", "status": "completed"}, "tags": []}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "papermill": {"duration": 766.895355, "end_time": "2025-05-28T00:18:57.602446", "environment_variables": {}, "exception": null, "input_path": "/usr/local/airflow/dags/325914dd56b624d4ab02902ac629a2f6cee875b2/dags/fresh/perishable_forecasting/workflow/backend_doi/notebook.ipynb", "output_path": "s3://grofers-prod-dse-sgp/airflow/dag_runs/fresh_perishable_forecasting_workflow_backend_doi_v1/scheduled__2025-05-27T20:05:00+00:00/run_notebook/try_1.ipynb", "parameters": {"cwd": "/usr/local/airflow/dags/repo/dags/fresh/perishable_forecasting/workflow/backend_doi"}, "start_time": "2025-05-28T00:06:10.707091", "version": "2.0.0"}}, "nbformat": 4, "nbformat_minor": 5}