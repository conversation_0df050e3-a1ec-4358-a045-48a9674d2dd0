alert_configs:
  slack:
  - channel: perishable-dag-alerts
dag_name: dts_doi_calc
dag_type: workflow
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: fresh
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U041Q7LJVFD
path: fresh/perishable_ordering/workflow/dts_doi_calc
paused: false
pool: fresh_pool
project_name: perishable_ordering
schedule:
  end_date: '2025-09-10T00:00:00'
  interval: 30 00 * * *
  start_date: '2025-06-13T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
