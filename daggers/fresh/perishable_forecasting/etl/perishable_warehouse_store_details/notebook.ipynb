{"cells": [{"cell_type": "code", "execution_count": null, "id": "e3e6144a-e4c6-4e55-bc23-90ce6471cf0b", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib\n", "!pip install pandas openpyxl\n", "!pip install numpy==1.26.4\n", "import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "import datetime\n", "\n", "\n", "from datetime import datetime\n", "from matplotlib import pyplot as plt\n", "\n", "# CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "\n", "# SQL Connection\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "7557586f-affe-446a-a883-edc9f3c8e40f", "metadata": {}, "outputs": [], "source": ["l2_id = [\n", "    1425,\n", "    31,\n", "    116,\n", "    198,\n", "    1097,\n", "    1956,\n", "    949,\n", "    1389,\n", "    1778,\n", "    950,\n", "    138,\n", "    1091,\n", "    1093,\n", "    1094,\n", "    2633,\n", "    63,\n", "    1367,\n", "    1369,\n", "    1429,\n", "    1185,\n", "]"]}, {"cell_type": "markdown", "id": "b8805089-4a95-409d-9f49-37d95c2cbd6d", "metadata": {}, "source": ["## Universe"]}, {"cell_type": "code", "execution_count": null, "id": "9de424bf-681b-4372-bb94-cb5ad1d9d517", "metadata": {}, "outputs": [], "source": ["sql = f\"\"\"\n", "\n", "with live_stores as(\n", "select\n", "    o.outlet_id \n", "from\n", "    dwh.fact_sales_order_details o\n", "where\n", "    order_current_status = 'DELIVERED'\n", "    and is_internal_order = False \n", "    and order_create_dt_ist between current_date - interval '7' day and current_date\n", "group by 1\n", "),\n", "\n", "dates as(\n", "select date as date_ from dwh.dim_date where date >= current_date - interval '1' day and date < current_date group by 1\n", "),\n", "\n", "universe as (\n", "select\n", "    d.date_,\n", "    cl.name as city_name,\n", "    pfma.facility_id,\n", "    po.outlet_id,\n", "    po.outlet_name as store_name,\n", "    ic.l0,\n", "    ic.l1,\n", "    ic.l2,\n", "    ic.l2_id,\n", "    ic.product_type,\n", "    pfma.item_id,\n", "    pfma.master_assortment_substate_id,\n", "    ic.name,\n", "    tm.tag_value be_outlet_id,\n", "    case when tm.tag_value = '0' then 'Direct to Store - No Backend' else om.outlet_name end be_outlet_name\n", "from\n", "    rpc.product_facility_master_assortment pfma\n", "cross join\n", "    dates d\n", "inner join\n", "    rpc.item_category_details ic\n", "    on ic.item_id = pfma.item_id\n", "    and ic.l2_id in {tuple(l2_id)}\n", "inner join\n", "    (\n", "    SELECT DISTINCT a.item_id\n", "        FROM rpc.product_product a\n", "        JOIN (\n", "            SELECT item_id, MAX(id) AS id\n", "            FROM rpc.product_product\n", "            WHERE approved = 1 AND active = 1\n", "            GROUP BY 1\n", "        ) b ON a.id = b.id AND a.item_id = b.item_id\n", "    WHERE a.active = 1 AND a.lake_active_record AND a.approved = 1 AND a.perishable = 1\n", "    ) ma ON ma.item_id = ic.item_id\n", "inner join\n", "    po.physical_facility_outlet_mapping po\n", "    on po.facility_id = pfma.facility_id\n", "    and po.active = 1\n", "    and po.ars_active = 1\n", "    and po.lake_active_record = true\n", "inner join\n", "    retail.console_outlet co\n", "    on co.id = po.outlet_id \n", "    and co.active = 1\n", "    and co.lake_active_record = true\n", "    and co.business_type_id = 7\n", "    and co.id in (select distinct outlet_id from live_stores)\n", "inner join\n", "    retail.console_location cl\n", "    on cl.id = co.tax_location_id\n", "    and cl.lake_active_record = true\n", "inner join   \n", "    rpc.item_outlet_tag_mapping tm\n", "    on pfma.item_id = tm.item_id \n", "    and po.outlet_id = tm.outlet_id\n", "    and tm.tag_type_id = 8 \n", "    and tm.active = 1\n", "left join\n", "    po.physical_facility_outlet_mapping om\n", "    on cast(tm.tag_value as int) =  om.outlet_id    \n", "    and om.active = 1\n", "    and om.ars_active = 1\n", "    and om.lake_active_record = true\n", "where\n", "    pfma.active = 1\n", "    and pfma.master_assortment_substate_id in (1,3)\n", ")\n", "\n", "select * from universe\n", "\n", "\"\"\"\n", "df1 = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "725a32a8-acf9-4efa-8ea7-8d12385eeeb6", "metadata": {}, "outputs": [], "source": ["df1[\"be_outlet_name\"] = df1[\"be_outlet_name\"].fillna(\"NA\")\n", "df1[\"be_outlet_id\"] = df1[\"be_outlet_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "1ac549ef-a29e-4520-9a10-3f4a83450feb", "metadata": {}, "outputs": [], "source": ["temp_universe = df1.copy()\n", "df1 = df1.drop(columns=[\"l2_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "2edbe4a6-80f0-490d-8d23-74856a6df26b", "metadata": {}, "outputs": [], "source": ["universe = df1.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "e462abc7-4be4-4e84-a266-99750d74be75", "metadata": {}, "outputs": [], "source": ["a = universe.shape"]}, {"cell_type": "code", "execution_count": null, "id": "2e7b4dbe-0d11-434d-9245-ff471e393ceb", "metadata": {}, "outputs": [], "source": ["universe.facility_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "51d5e061-437c-40f8-b85e-03015adb8c53", "metadata": {}, "outputs": [], "source": ["universe.shape"]}, {"cell_type": "markdown", "id": "b21358ee-4b8a-48d8-9821-7358d27d6aa0", "metadata": {}, "source": ["## Sales"]}, {"cell_type": "code", "execution_count": null, "id": "c0479768-89ed-4729-8145-b85b51a8f9ed", "metadata": {}, "outputs": [], "source": ["sql = f\"\"\"\n", "with sales as (\n", "SELECT\n", "    date(o.order_create_dt_ist) as date_,\n", "    o.outlet_id,\n", "    ip.item_id,\n", "    sum(o.procured_quantity*ip.multiplier) as qty_sold\n", "FROM\n", "    dwh.fact_sales_order_item_details o\n", "inner join\n", "    dwh.dim_item_product_offer_mapping ip\n", "    on ip.product_id = o.product_id\n", "    and ip.is_current\n", "inner join\n", "    rpc.item_category_details rp\n", "    on rp.item_id = ip.item_id\n", "    and rp.l2_id in {tuple(l2_id)}\n", " WHERE\n", "    o.order_current_status = 'DELIVERED'\n", "    and o.order_create_dt_ist >= current_date - interval '1' day\n", "    and o.order_create_dt_ist < current_date\n", "    and o.is_internal_order = False\n", "GROUP BY 1,2,3\n", ")\n", "\n", "select * from sales\n", "\n", "\"\"\"\n", "sales_data = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "508908f4-cd9e-457e-9a02-2471f3d2aa2d", "metadata": {}, "outputs": [], "source": ["sales_data.head()"]}, {"cell_type": "markdown", "id": "9cf12d70-46dc-449c-a1dc-27350b5233bc", "metadata": {}, "source": ["## Pan India Level Hourly L2 Weights Calculation"]}, {"cell_type": "code", "execution_count": null, "id": "9d53197b-fb1b-4b0f-8d83-540eddf3b7b1", "metadata": {}, "outputs": [], "source": ["searches_df = f\"\"\"\n", "with keyword_searches as (\n", "select \n", "    date(at_date_ist) as date_,\n", "    at_hour_ist as hour_,\n", "    keyword,\n", "    city_name city,\n", "    sum(search_dau) searches_\n", "from \n", "    dwh.agg_hourly_search_keyword_conversion_metrics\n", "where \n", "    at_date_ist between current_date-interval'30'day and current_date-interval'1'day\n", "    and city_name  <> 'Overall'\n", "    and platform in ('android','ios')\n", "    and merchant_name <> 'Overall'\n", "    and keyword <> 'Overall'\n", "group by 1,2,3,4\n", "),\n", "\n", "l2_mapping as (\n", "select \n", "    keyword, l2_category, l2_category_id\n", "from \n", "    dwh.dim_keywords_l2_mapping \n", "where \n", "    is_current\n", "    and l2_category_id in {tuple(l2_id)}\n", "group by 1,2,3\n", ")\n", "\n", "select\n", "    date_,\n", "    hour_,\n", "    city,\n", "    l2_category as l2,\n", "    l2_category_id as l2_id,\n", "    sum(searches_) as searches\n", "from\n", "    keyword_searches ks\n", "join \n", "    l2_mapping m\n", "    on m.keyword=ks.keyword\n", "group by 1,2,3,4,5\n", "\n", "\"\"\"\n", "searches_df = read_sql_query(searches_df, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "d8b5fbc5-f9b5-4d9f-b915-1c75fc93d230", "metadata": {}, "outputs": [], "source": ["pan_india_searches_df = searches_df.groupby([\"date_\", \"hour_\", \"l2_id\"], as_index=False)[\n", "    \"searches\"\n", "].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "105eae67-5943-482b-aa93-d6aa741fbcf6", "metadata": {}, "outputs": [], "source": ["pan_india_l2_searches_df = pan_india_searches_df.groupby([\"hour_\", \"l2_id\"], as_index=False)[\n", "    \"searches\"\n", "].mean()"]}, {"cell_type": "code", "execution_count": null, "id": "71d7e138-285b-452a-b6e6-b190b2dea1f9", "metadata": {}, "outputs": [], "source": ["pan_india_l2_searches_df[\"weights\"] = pan_india_l2_searches_df.groupby([\"l2_id\"])[\n", "    \"searches\"\n", "].transform(lambda x: x / x.sum())"]}, {"cell_type": "code", "execution_count": null, "id": "dffd0e05-2edd-4db8-98ca-10091bbccc50", "metadata": {}, "outputs": [], "source": ["pan_india_l2_searches_df.drop(columns=[\"searches\"], inplace=True)\n", "pan_india_l2_searches_df[\"weights\"].fillna(1, inplace=True)"]}, {"cell_type": "markdown", "id": "3cdfd06a-e33e-428b-8aa5-db34cd60f48d", "metadata": {}, "source": ["## Wtd Avail Calculation"]}, {"cell_type": "code", "execution_count": null, "id": "7309ec7c-b2da-4488-af97-82461106902c", "metadata": {}, "outputs": [], "source": ["inv_df = f\"\"\"\n", "select\n", "    snapshot_date_ist as date_,\n", "    snapshot_hr_mm/100 as hour_,\n", "    outlet_id,\n", "    icd.l2_id,\n", "    i.item_id,\n", "    Max(current_inventory) as current_inventory\n", "from \n", "    dwh.agg_hourly_outlet_item_inventory i\n", "join \n", "    rpc.item_category_details icd\n", "    on icd.item_id = i.item_id\n", "    and icd.l2_id in {tuple(l2_id)}\n", "where \n", "    snapshot_date_ist = current_date - interval '1' day\n", "group by 1,2,3,4,5\n", "\"\"\"\n", "inv_df = read_sql_query(inv_df, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "eef88127-6e83-4ba4-b9d5-f4df053a5cde", "metadata": {}, "outputs": [], "source": ["inv_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "1bd41a23-bf92-46f1-9b61-ba562a29485b", "metadata": {}, "outputs": [], "source": ["backend_availability_inv_df = inv_df.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "740fffbf-4e20-4964-8983-44819b30ad0b", "metadata": {}, "outputs": [], "source": ["eod_inv_df = inv_df.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "1fab2b76-b595-4ab1-9eef-b1737af8e7ff", "metadata": {}, "outputs": [], "source": ["avail_df = (\n", "    inv_df[[\"date_\", \"outlet_id\", \"l2_id\", \"item_id\"]]\n", "    .drop_duplicates()  ## created date x outlet x item universe\n", "    .merge(pan_india_l2_searches_df, on=[\"l2_id\"], how=\"left\")\n", "    ## Incorpated 24 hours into the above universe to handle missing inv hours from inv table\n", "    .merge(inv_df, on=[\"date_\", \"hour_\", \"outlet_id\", \"l2_id\", \"item_id\"], how=\"left\")\n", ")\n", "avail_df[\"current_inventory\"].fillna(0, inplace=True)\n", "## filled inventory with zero when not find in inv table"]}, {"cell_type": "code", "execution_count": null, "id": "4f0a9a2b-8961-4b23-a563-fa4951123856", "metadata": {}, "outputs": [], "source": ["avail_df[\"is_avail\"] = np.where(avail_df[\"current_inventory\"] > 0, 1, 0)\n", "avail_df[\"avail\"] = avail_df[\"weights\"] * avail_df[\"is_avail\"]"]}, {"cell_type": "code", "execution_count": null, "id": "cfa03fd7-b2fb-4313-be3c-435d965f20d7", "metadata": {}, "outputs": [], "source": ["final_avail_df = avail_df.groupby([\"date_\", \"outlet_id\", \"l2_id\", \"item_id\"], as_index=False)[\n", "    \"avail\"\n", "].sum()\n", "final_avail_df.rename(columns={\"avail\": \"wtd_avail\"}, inplace=True)\n", "final_avail_df.drop(columns=[\"l2_id\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "119f1aca-391f-42dd-9fa5-46989f20003f", "metadata": {}, "outputs": [], "source": ["final_avail_df.head()"]}, {"cell_type": "markdown", "id": "a3faf544-b66a-4449-bb63-8a53e554752b", "metadata": {}, "source": ["## EOD Inventory"]}, {"cell_type": "code", "execution_count": null, "id": "ed01c563-d0da-4e07-aac8-2da0f703048d", "metadata": {}, "outputs": [], "source": ["eod_inv_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "0b037040-01a9-42c9-bc64-ac0ace579cf0", "metadata": {}, "outputs": [], "source": ["final_eod_df = eod_inv_df[eod_inv_df[\"hour_\"] == 23].reset_index(drop=True)\n", "final_eod_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "a8c8413b-892d-43f9-889f-354af60d4b68", "metadata": {}, "outputs": [], "source": ["grouped_df_eod_df = (\n", "    final_eod_df.groupby([\"date_\", \"outlet_id\", \"item_id\"])[\"current_inventory\"].sum().reset_index()\n", ")\n", "grouped_df_eod_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "816f46dc-c2ce-4046-b4fe-03f71b560e4e", "metadata": {}, "outputs": [], "source": ["grouped_df_eod_df.head()"]}, {"cell_type": "markdown", "id": "9da4aee3-9b9b-4eca-811f-3d9658ee57c0", "metadata": {}, "source": ["## Transfers Data"]}, {"cell_type": "code", "execution_count": null, "id": "8c4f3069-33ef-4a88-be0c-c5ad24d7766d", "metadata": {}, "outputs": [], "source": ["transfers_df = f\"\"\"\n", "with item_base as (\n", "select\n", "    item_id\n", "from\n", "    rpc.item_category_details ic\n", "where\n", "    l2_id in {tuple(l2_id)}\n", "group by 1\n", "),\n", "\n", "hyperpure_sto_grn_details as (\n", "select\n", "    p.created_at + interval '330' minute as po_creation_time,\n", "    p.id,\n", "    date(case \n", "        when extract(hour from (p.created_at + interval '330' minute)) between 0 and 15 then (p.created_at + interval '330' minute)\n", "        when extract(hour from (p.created_at + interval '330' minute)) between 16 and 23 then (p.created_at + interval '330' minute + interval '1' day)\n", "    end) as date_of_consumption,\n", "    case \n", "        when extract(hour from (p.created_at + interval '330' minute)) in (16,17,18,19,20) then 'SLOT_C'\n", "        when extract(hour from (p.created_at + interval '330' minute)) in (21,22,23,0,1,2,3,4,5,6) then 'SLOT_A'\n", "        else 'SLOT_B'\n", "    end as slots,\n", "    p.outlet_id,\n", "    poi.item_id,\n", "    poi.units_ordered as sto_qty,\n", "    grn.grn_quantity as grn_qty\n", "from\n", "    po.purchase_order p\n", "inner join  \n", "    po.purchase_order_items poi \n", "    on p.id=poi.po_id\n", "inner join\n", "    po.purchase_order_status posa \n", "    on posa.po_id = p.id\n", "inner join\n", "    po.purchase_order_state posta \n", "    on posta.id = posa.po_state_id\n", "inner join\n", "    item_base it\n", "    on it.item_id = poi.item_id \n", "left join\n", "    (\n", "    select \n", "        po_id, \n", "        item_id, \n", "        sum(quantity) grn_quantity\n", "        from  \n", "            po.po_grn grn \n", "        where \n", "            insert_ds_ist >= cast(current_date - interval '3' day as varchar) \n", "        group by 1,2\n", "    ) grn on \n", "    grn.item_id = poi.item_id \n", "    and grn.po_id = p.id\n", "where\n", "    (posta.name not in ('Cancelled', 'Rejected', 'Cancelled post Creation') or (posta.name in ('Expired'))) \n", "    and p.created_at + interval '330' minute >= current_date - interval '3' day\n", "    and po_type_id <> 11\n", "    and p.vendor_id in (13280)\n", "),\n", "\n", "\n", "final_warehouse as (\n", "select\n", "    date_of_consumption,\n", "    slots,\n", "    outlet_id,\n", "    item_id,\n", "    sum(sto_qty) sto_qty, \n", "    sum(grn_qty) grn_qty\n", "from\n", "    hyperpure_sto_grn_details\n", "group by 1,2,3,4\n", "),\n", "\n", "\n", "final as(\n", "select\n", "    date_of_consumption as date_,\n", "    outlet_id,\n", "    item_id,\n", "    coalesce(sum(case when slots = 'SLOT_C' then sto_qty end),0) as slot_c_sto,\n", "    coalesce(sum(case when slots = 'SLOT_C' then grn_qty end),0) as slot_c_grn,\n", "    coalesce(sum(case when slots = 'SLOT_A' then sto_qty end),0) as slot_a_sto,\n", "    coalesce(sum(case when slots = 'SLOT_A' then grn_qty end),0) as slot_a_grn,\n", "    coalesce(sum(case when slots = 'SLOT_B' then sto_qty end),0) as slot_b_sto,\n", "    coalesce(sum(case when slots = 'SLOT_B' then grn_qty end),0) as slot_b_grn\n", "from \n", "    final_warehouse \n", "where \n", "    date_of_consumption = current_date - interval '1' day\n", "group by 1,2,3\n", ")\n", "\n", "select * from final\n", "\"\"\"\n", "transfers_df = read_sql_query(transfers_df, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "ec1171ce-8490-4737-a7fe-68ac9b9eff7f", "metadata": {}, "outputs": [], "source": ["transfers_df.shape"]}, {"cell_type": "markdown", "id": "51acd0aa-53aa-4d6d-8cf2-dbf9f0094272", "metadata": {}, "source": ["## Forecast Data"]}, {"cell_type": "code", "execution_count": null, "id": "4e803112-de80-4cee-ba5f-5b77cc16f0ad", "metadata": {}, "outputs": [], "source": ["forecast_df = f\"\"\"\n", "with base as(\n", "select\n", "    fdate,\n", "    facility_id,\n", "    item_id, \n", "    max(updated_at) max_updated_at\n", "from\n", "    supply_etls.perishable_forecast_cpd_logs s\n", "where\n", "    fdate = current_date - interval '1' day\n", "    and date_ist <= current_date - interval '3' day\n", "    and date_ist >= current_date - interval '4' day\n", "group by 1,2,3\n", "),\n", "\n", "final as (\n", "select \n", "    f.fdate as date_,\n", "    f.facility_id,\n", "    f.item_id,\n", "    f.final_ex_qty as forecast_qty\n", "from\n", "    supply_etls.perishable_forecast_cpd_logs f\n", "inner join\n", "    base b\n", "    on f.facility_id = b.facility_id\n", "    and f.item_id = b.item_id\n", "    and f.fdate = b.fdate\n", "    and f.updated_at = b.max_updated_at\n", "where\n", "    f.fdate = current_date - interval '1' day    \n", "    and f.date_ist <= current_date - interval '3' day\n", "    and f.date_ist >= current_date - interval '4' day\n", ")\n", "\n", "select date_, facility_id, item_id, sum(forecast_qty) forecast_qty\n", "from final \n", "group by 1,2,3 \n", "\"\"\"\n", "forecast_df = read_sql_query(forecast_df, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "95c4fbab-6c7a-4606-a62b-7fff2ec09821", "metadata": {}, "outputs": [], "source": ["milk_forecast_df = f\"\"\"\n", "with fin as (\n", "select \n", "    facility_id,\n", "    date_ist,\n", "    forecast_date,\n", "    updated_at,\n", "    item_id,\n", "    sum(cpd) as cpd,\n", "    sum(final_cpd) as cpd,\n", "    sum(final_cpd_case) as final_cpd_case,\n", "    row_number() over (partition by forecast_date, facility_id,item_id order by updated_at desc) as rn\n", "from \n", "    supply_etls.milk_forecast_logs\n", "where date_ist>= current_date - interval '4' day\n", "group by 1,2,3,4,5\n", ")\n", "select \n", "    forecast_date as date_,\n", "    facility_id,\n", "    item_id,\n", "    sum(final_cpd_case) forecast_qty\n", "from fin\n", "where rn = 1\n", "and forecast_date = current_Date-interval '1' day\n", "group by 1,2,3\n", "\"\"\"\n", "milk_forecast_df = read_sql_query(milk_forecast_df, CON_TRINO)\n", "milk_forecast_df[\"date_\"] = pd.to_datetime(milk_forecast_df[\"date_\"])\n", "forecast_df = pd.concat([forecast_df, milk_forecast_df], axis=0)"]}, {"cell_type": "code", "execution_count": null, "id": "e9afcf67-d1d6-4788-8a7f-da12d59c7a8b", "metadata": {}, "outputs": [], "source": ["forecast_df.drop_duplicates(subset=[\"date_\", \"facility_id\", \"item_id\"], keep=\"last\", inplace=True)"]}, {"cell_type": "markdown", "id": "0f51f2f5-5717-460d-92fb-9282e096c4df", "metadata": {}, "source": ["## Dump Data"]}, {"cell_type": "code", "execution_count": null, "id": "eb93c74d-ca55-4adb-a402-025a9db1aa74", "metadata": {}, "outputs": [], "source": ["dump_df = f\"\"\"\n", "with dump as (\n", "SELECT \n", "    DATE(il.pos_timestamp + interval '330' minute) AS date_, \n", "    il.outlet_id,\n", "    ma.item_id,\n", "    sum(il.\"delta\")as dump_qty\n", "FROM \n", "    ims.ims_inventory_log il\n", "INNER JOIN \n", "    (\n", "    SELECT \n", "        pp.item_id, pp.variant_id\n", "    FROM \n", "        rpc.product_product pp\n", "    INNER JOIN\n", "        rpc.item_category_details ic\n", "        on pp.item_id = ic.item_id\n", "        and ic.l2_id in {tuple(l2_id)}\n", "    WHERE \n", "        pp.approved = 1 \n", "        and pp.active = 1\n", "        and pp.lake_active_record\n", "        and pp.perishable = 1\n", "    GROUP BY 1,2\n", "    ) ma\n", "    ON ma.variant_id = il.variant_id\n", "LEFT JOIN\n", "    ims.ims_bad_inventory_update_log ib\n", "    on ib.inventory_update_id = il.inventory_update_id\n", "WHERE  \n", "    il.insert_ds_ist >= cast(current_date - interval '1' day as varchar)\n", "    and il.insert_ds_ist < cast(current_date as varchar) \n", "    AND il.inventory_update_type_id IN (11,64,7,9,33,34,63,67,12)\n", "GROUP BY 1,2,3\n", ")\n", "\n", "select * from dump\n", "\"\"\"\n", "dump_df = read_sql_query(dump_df, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "5e1f1f46-f95f-4ad9-927c-ef1120860d62", "metadata": {}, "outputs": [], "source": ["dump_df.shape"]}, {"cell_type": "markdown", "id": "ebc4d6d9-a2f4-4e67-a45d-ccfd94dd4e2f", "metadata": {}, "source": ["## Backend x Item x Availability"]}, {"cell_type": "code", "execution_count": null, "id": "d17fcbcc-c799-4294-a083-40dbad5524c0", "metadata": {}, "outputs": [], "source": ["sql = f\"\"\"\n", "with base_opd as(\n", "select\n", "    date(order_create_dt_ist) date_,\n", "    o.outlet_id,\n", "    count(distinct order_id) opd\n", "from\n", "    dwh.fact_sales_order_item_details o\n", "where\n", "    o.order_current_status = 'DELIVERED'\n", "    and o.is_internal_order = False \n", "    and o.order_create_dt_ist between current_date - interval '15' day and current_date  - interval '1' day\n", "group by 1,2\n", "having count(distinct order_id) > 10\n", ")\n", "select\n", "    outlet_id,\n", "    avg(opd) opd\n", "from\n", "    base_opd\n", "group by 1\n", "\n", "\"\"\"\n", "opd_data = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "329e8684-bc01-4414-b8bb-4b500d36fef7", "metadata": {}, "outputs": [], "source": ["opd_data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "37857fe6-0f10-4567-ad0b-bd07d04c25e7", "metadata": {}, "outputs": [], "source": ["temp_universe = temp_universe.query(\"be_outlet_id != 0\").reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "edc7d690-7864-44ab-8400-a63be0b23827", "metadata": {}, "outputs": [], "source": ["# temp_universe = temp_universe.query(\"be_outlet_id == 4353 and item_id in (10032715,10138644)\").reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "5788f59c-7f8a-48b3-917c-32360a8d3820", "metadata": {}, "outputs": [], "source": ["backend_item_availability = temp_universe.merge(\n", "    opd_data,\n", "    left_on=[\"outlet_id\"],\n", "    right_on=[\"outlet_id\"],\n", "    how=\"left\",\n", ")\n", "backend_item_availability[\"opd\"].fillna(0, inplace=True)\n", "\n", "backend_item_availability[\"store_weight\"] = backend_item_availability.groupby(\n", "    [\"date_\", \"be_outlet_id\", \"item_id\"]\n", ")[\"opd\"].transform(lambda x: x / x.sum())"]}, {"cell_type": "code", "execution_count": null, "id": "db1596f0-ccdc-4394-99e4-22193ae6a2b2", "metadata": {}, "outputs": [], "source": ["# backend_item_availability.to_csv(\"a.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "0fbec4a6-87b1-4a37-acea-bb930782ccc7", "metadata": {}, "outputs": [], "source": ["hours_df = pd.DataFrame({\"hour_\": range(0, 24)})\n", "\n", "backend_item_availability[\"key\"] = 1\n", "hours_df[\"key\"] = 1\n", "\n", "final_backend_item_avl = pd.merge(backend_item_availability, hours_df, on=\"key\").drop(\"key\", axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "2884577e-d444-44a6-93ef-1c492273ae31", "metadata": {}, "outputs": [], "source": ["final_backend_item_avl.shape"]}, {"cell_type": "code", "execution_count": null, "id": "159b5a95-facb-4b39-94f6-f9d67e65fa9b", "metadata": {}, "outputs": [], "source": ["backend_availability_inv_df = backend_availability_inv_df.drop(columns=[\"l2_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "b6c64a45-2fbe-4d83-8045-3186461e6fd5", "metadata": {}, "outputs": [], "source": ["final_backend_item_avl_1 = final_backend_item_avl.merge(\n", "    backend_availability_inv_df,\n", "    left_on=[\"date_\", \"outlet_id\", \"item_id\", \"hour_\"],\n", "    right_on=[\"date_\", \"outlet_id\", \"item_id\", \"hour_\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8d41f755-0a2c-4588-a0f2-62349408f993", "metadata": {}, "outputs": [], "source": ["final_backend_item_avl_1[\"is_avail\"] = np.where(\n", "    final_backend_item_avl_1[\"current_inventory\"] > 0, 1, 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "546449d5-c78b-437f-89f3-3d397bb8932f", "metadata": {}, "outputs": [], "source": ["final_backend_item_avl_1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "404d9d3e-5040-4c92-81c0-954c6aab0068", "metadata": {}, "outputs": [], "source": ["final_backend_item_avl_2 = final_backend_item_avl_1.merge(\n", "    pan_india_l2_searches_df,\n", "    left_on=[\"l2_id\", \"hour_\"],\n", "    right_on=[\"l2_id\", \"hour_\"],\n", "    how=\"left\",\n", ")\n", "final_backend_item_avl_2[\"weights\"].fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "320ddf1f-8491-4eee-badf-812003757f17", "metadata": {}, "outputs": [], "source": ["final_backend_item_avl_2.shape"]}, {"cell_type": "code", "execution_count": null, "id": "19029961-0176-4d44-a437-53a0b800dd00", "metadata": {}, "outputs": [], "source": ["final_backend_item_avl_2[\"is_avail\"].fillna(0, inplace=True)\n", "final_backend_item_avl_2[\"weights\"].fillna(0, inplace=True)\n", "final_backend_item_avl_2[\"store_weight\"].fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "490975d6-c6f6-475b-bdfb-a333472c5e5d", "metadata": {}, "outputs": [], "source": ["final_backend_item_avl_2[\"weighted_avail\"] = (\n", "    final_backend_item_avl_2[\"is_avail\"]\n", "    * final_backend_item_avl_2[\"weights\"]\n", "    * final_backend_item_avl_2[\"store_weight\"]\n", ")\n", "\n", "result_df_be_item_avl = (\n", "    final_backend_item_avl_2.groupby([\"date_\", \"be_outlet_id\", \"item_id\"])[\"weighted_avail\"]\n", "    .sum()\n", "    .reset_index(name=\"be_item_avl\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4bfd41a7-8496-4157-8e84-2f827d5ef83e", "metadata": {}, "outputs": [], "source": ["result_df_be_item_avl.head()"]}, {"cell_type": "code", "execution_count": null, "id": "096b4b0e-3c78-4232-853d-ea5ee7babc0c", "metadata": {}, "outputs": [], "source": ["result_df_be_item_avl.shape"]}, {"cell_type": "code", "execution_count": null, "id": "46c7b92a-44e2-4b8d-8900-ed0045211766", "metadata": {}, "outputs": [], "source": ["result_df_be_item_avl[\"be_item_avl\"] = result_df_be_item_avl[\"be_item_avl\"].round(2)"]}, {"cell_type": "markdown", "id": "577bb816-7bf1-454e-a57e-1101c5a64053", "metadata": {}, "source": ["## Changing dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "bd87d2ab-3da9-4599-8c41-de1e89aa3b27", "metadata": {}, "outputs": [], "source": ["universe[\"facility_id\"] = universe[\"facility_id\"].astype(int)\n", "universe[\"outlet_id\"] = universe[\"outlet_id\"].astype(int)\n", "universe[\"item_id\"] = universe[\"item_id\"].astype(int)\n", "universe[\"date_\"] = pd.to_datetime(universe[\"date_\"])\n", "universe[\"be_outlet_id\"] = universe[\"be_outlet_id\"].astype(int)\n", "\n", "\n", "sales_data[\"outlet_id\"] = sales_data[\"outlet_id\"].astype(int)\n", "sales_data[\"item_id\"] = sales_data[\"item_id\"].astype(int)\n", "sales_data[\"date_\"] = pd.to_datetime(sales_data[\"date_\"])\n", "\n", "final_avail_df[\"outlet_id\"] = final_avail_df[\"outlet_id\"].astype(int)\n", "final_avail_df[\"item_id\"] = final_avail_df[\"item_id\"].astype(int)\n", "final_avail_df[\"date_\"] = pd.to_datetime(final_avail_df[\"date_\"])\n", "\n", "transfers_df[\"outlet_id\"] = transfers_df[\"outlet_id\"].astype(int)\n", "transfers_df[\"item_id\"] = transfers_df[\"item_id\"].astype(int)\n", "transfers_df[\"date_\"] = pd.to_datetime(transfers_df[\"date_\"])\n", "\n", "forecast_df[\"facility_id\"] = forecast_df[\"facility_id\"].astype(int)\n", "forecast_df[\"item_id\"] = forecast_df[\"item_id\"].astype(int)\n", "forecast_df[\"date_\"] = pd.to_datetime(forecast_df[\"date_\"])\n", "\n", "grouped_df_eod_df[\"outlet_id\"] = grouped_df_eod_df[\"outlet_id\"].astype(int)\n", "grouped_df_eod_df[\"item_id\"] = grouped_df_eod_df[\"item_id\"].astype(int)\n", "grouped_df_eod_df[\"date_\"] = pd.to_datetime(grouped_df_eod_df[\"date_\"])\n", "\n", "dump_df[\"outlet_id\"] = dump_df[\"outlet_id\"].astype(int)\n", "dump_df[\"item_id\"] = dump_df[\"item_id\"].astype(int)\n", "dump_df[\"date_\"] = pd.to_datetime(dump_df[\"date_\"])\n", "\n", "result_df_be_item_avl[\"be_outlet_id\"] = result_df_be_item_avl[\"be_outlet_id\"].astype(int)\n", "result_df_be_item_avl[\"item_id\"] = result_df_be_item_avl[\"item_id\"].astype(int)\n", "result_df_be_item_avl[\"date_\"] = pd.to_datetime(result_df_be_item_avl[\"date_\"])"]}, {"cell_type": "markdown", "id": "8d672bff-e473-4f07-87b0-5e1b67d4dfcc", "metadata": {}, "source": ["## Merging Dataframes"]}, {"cell_type": "code", "execution_count": null, "id": "3580da76-d09e-49b5-a743-1bd076484058", "metadata": {}, "outputs": [], "source": ["final_df = (\n", "    universe.merge(\n", "        sales_data,\n", "        left_on=[\"outlet_id\", \"item_id\", \"date_\"],\n", "        right_on=[\"outlet_id\", \"item_id\", \"date_\"],\n", "        how=\"left\",\n", "    )\n", "    .merge(\n", "        final_avail_df,\n", "        left_on=[\"outlet_id\", \"item_id\", \"date_\"],\n", "        right_on=[\"outlet_id\", \"item_id\", \"date_\"],\n", "        how=\"left\",\n", "    )\n", "    .merge(\n", "        transfers_df,\n", "        left_on=[\"outlet_id\", \"item_id\", \"date_\"],\n", "        right_on=[\"outlet_id\", \"item_id\", \"date_\"],\n", "        how=\"left\",\n", "    )\n", "    .merge(\n", "        forecast_df,\n", "        left_on=[\"facility_id\", \"item_id\", \"date_\"],\n", "        right_on=[\"facility_id\", \"item_id\", \"date_\"],\n", "        how=\"left\",\n", "    )\n", "    .merge(\n", "        grouped_df_eod_df,\n", "        left_on=[\"outlet_id\", \"item_id\", \"date_\"],\n", "        right_on=[\"outlet_id\", \"item_id\", \"date_\"],\n", "        how=\"left\",\n", "    )\n", "    .merge(\n", "        dump_df,\n", "        left_on=[\"outlet_id\", \"item_id\", \"date_\"],\n", "        right_on=[\"outlet_id\", \"item_id\", \"date_\"],\n", "        how=\"left\",\n", "    )\n", "    .merge(\n", "        result_df_be_item_avl,\n", "        left_on=[\"be_outlet_id\", \"item_id\", \"date_\"],\n", "        right_on=[\"be_outlet_id\", \"item_id\", \"date_\"],\n", "        how=\"left\",\n", "    )\n", ")\n", "\n", "final_df[\"qty_sold\"].fillna(0, inplace=True)\n", "final_df[\"wtd_avail\"].fillna(0, inplace=True)\n", "final_df[\"be_item_avl\"].fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "d682b142-5fcf-450b-a25e-7ff01153d6a2", "metadata": {}, "outputs": [], "source": ["final_df.shape, universe.shape"]}, {"cell_type": "markdown", "id": "66f0f8b0-bdba-4607-a247-41db7fc8d253", "metadata": {}, "source": ["## Pushing into Main Table"]}, {"cell_type": "code", "execution_count": null, "id": "7f4b7a90-8e8d-4207-a9c6-4b19b931df63", "metadata": {}, "outputs": [], "source": ["final_df.rename(\n", "    columns={\n", "        \"name\": \"item_name\",\n", "        \"current_inventory\": \"eod_inventory\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6c6fe628-0a0e-4ccc-9397-f0e0bfc87261", "metadata": {}, "outputs": [], "source": ["new_column_order = [\n", "    \"date_\",\n", "    \"facility_id\",\n", "    \"outlet_id\",\n", "    \"store_name\",\n", "    \"l0\",\n", "    \"l1\",\n", "    \"l2\",\n", "    \"product_type\",\n", "    \"item_id\",\n", "    \"master_assortment_substate_id\",\n", "    \"item_name\",\n", "    \"be_outlet_id\",\n", "    \"be_outlet_name\",\n", "    \"qty_sold\",\n", "    \"wtd_avail\",\n", "    \"slot_c_sto\",\n", "    \"slot_c_grn\",\n", "    \"slot_a_sto\",\n", "    \"slot_a_grn\",\n", "    \"slot_b_sto\",\n", "    \"slot_b_grn\",\n", "    \"forecast_qty\",\n", "    \"eod_inventory\",\n", "    \"dump_qty\",\n", "    \"city_name\",\n", "    \"be_item_avl\",\n", "]\n", "\n", "# Reassign the DataFrame with the new column order\n", "final_df = final_df[new_column_order]"]}, {"cell_type": "code", "execution_count": null, "id": "d015a8aa-4508-488d-bc02-7f480355ccec", "metadata": {}, "outputs": [], "source": ["final_df = final_df.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "98c06a9f-a791-4690-a113-a80eed16bd6d", "metadata": {}, "outputs": [], "source": ["final_result = final_df.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "0b8effc6-9903-42b0-b63a-1db9a3dcf711", "metadata": {}, "outputs": [], "source": ["final_result.shape"]}, {"cell_type": "code", "execution_count": null, "id": "a7553296-b40f-4ab4-b17a-8a260961aafc", "metadata": {}, "outputs": [], "source": ["b = final_result.shape"]}, {"cell_type": "code", "execution_count": null, "id": "9da4cbaa-f8cd-4f95-914e-b5f68427cedc", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"timestamp(6)\", \"description\": \"date\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility_id\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"outlet_id\"},\n", "    {\"name\": \"store_name\", \"type\": \"varchar\", \"description\": \"store_name\"},\n", "    {\"name\": \"l0\", \"type\": \"varchar\", \"description\": \"l0\"},\n", "    {\"name\": \"l1\", \"type\": \"varchar\", \"description\": \"l1\"},\n", "    {\"name\": \"l2\", \"type\": \"varchar\", \"description\": \"l2\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar\", \"description\": \"product_type\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "    {\n", "        \"name\": \"master_assortment_substate_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"master_assortment_substate_id\",\n", "    },\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"item_name\"},\n", "    {\"name\": \"be_outlet_id\", \"type\": \"integer\", \"description\": \"be_outlet_id\"},\n", "    {\"name\": \"be_outlet_name\", \"type\": \"varchar\", \"description\": \"be_outlet_name\"},\n", "    {\"name\": \"qty_sold\", \"type\": \"real\", \"description\": \"qty_sold\"},\n", "    {\"name\": \"wtd_avail\", \"type\": \"real\", \"description\": \"wtd_avail\"},\n", "    {\"name\": \"slot_c_sto\", \"type\": \"real\", \"description\": \"slot_c_sto\"},\n", "    {\"name\": \"slot_c_grn\", \"type\": \"real\", \"description\": \"slot_c_grn\"},\n", "    {\"name\": \"slot_a_sto\", \"type\": \"real\", \"description\": \"slot_a_sto\"},\n", "    {\"name\": \"slot_a_grn\", \"type\": \"real\", \"description\": \"slot_a_grn\"},\n", "    {\"name\": \"slot_b_sto\", \"type\": \"real\", \"description\": \"slot_b_sto\"},\n", "    {\"name\": \"slot_b_grn\", \"type\": \"real\", \"description\": \"slot_b_grn\"},\n", "    {\"name\": \"forecast_qty\", \"type\": \"real\", \"description\": \"forecast_qty\"},\n", "    {\"name\": \"eod_inventory\", \"type\": \"real\", \"description\": \"eod_inventory\"},\n", "    {\"name\": \"dump_qty\", \"type\": \"real\", \"description\": \"dump_qty\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"city_name\"},\n", "    {\"name\": \"be_item_avl\", \"type\": \"real\", \"description\": \"be_item_avl\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "cf11a2f5-10f7-4826-a241-a713297924aa", "metadata": {}, "outputs": [], "source": ["if a[0] == b[0]:\n", "    kwargs = {\n", "        \"schema_name\": \"supply_etls\",\n", "        \"table_name\": \"perishable_warehouse_store_details\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"date_\", \"facility_id\", \"outlet_id\", \"item_id\"],\n", "        \"partition_key\": [\"date_\"],\n", "        \"incremental_key\": \"date_\",\n", "        \"force_upsert_without_increment_check\": False,\n", "        \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "        \"table_description\": \"Details of Perishable items at store level\",\n", "    }\n", "\n", "    pb.to_trino(final_result, **kwargs)\n", "\n", "    channel = \"table-updates-tanishq\"\n", "    text_req = \"\\n <@U05CCTXLBU1> \\n Perishable ETL Fresh Details is updated Today \"\n", "    pb.send_slack_message(\n", "        channel=channel,\n", "        text=text_req,\n", "    )\n", "else:\n", "    channel = \"table-updates-tanishq\"\n", "    text_req = (\n", "        \"\\n <@U05CCTXLBU1> \\n Duplication Row in Perishable ETL Fresh Details Table is not updated Today \"\n", "        + current_date_str\n", "    )\n", "    pb.send_slack_message(\n", "        channel=channel,\n", "        text=text_req,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "0452c593-5f06-4fa9-b534-e6454a00d842", "metadata": {}, "outputs": [], "source": ["# select count(*) from blinkit_staging.supply_etls.testing_fnv_table_v1"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}