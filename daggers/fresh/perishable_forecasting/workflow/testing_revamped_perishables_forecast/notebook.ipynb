{"cells": [{"cell_type": "markdown", "id": "490b56f1-d50d-4696-b037-3269115b93b2", "metadata": {}, "source": ["### Imports & Utils"]}, {"cell_type": "code", "execution_count": null, "id": "3c6d8017-5dee-4775-a379-aa88b6119d59", "metadata": {}, "outputs": [], "source": ["!pip install numpy==1.26.4"]}, {"cell_type": "code", "execution_count": null, "id": "ed716440-16a4-46a5-81e9-054d9f9e550c", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import time\n", "from datetime import datetime, timedelta, date\n", "import math\n", "import calendar\n", "import gc\n", "from pytz import timezone\n", "from datetime import datetime as dt\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.options.mode.chained_assignment = None  # default='warn'\n", "pd.set_option(\"display.max_columns\", 500)"]}, {"cell_type": "code", "execution_count": null, "id": "c1a297c6-0adf-40ab-b5cd-50b85bb78a5a", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(\n", "            f\"Read attempt: {attempt}... \\nRun started at {datetime.now() + timed<PERSON>ta(hours = 5.5)}\"\n", "        )\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "b0fc4f62-ba80-45b3-a389-460591356fb3", "metadata": {}, "outputs": [], "source": ["def detect_iqr_outliers(data, iqr_coefficient=1.5):\n", "    q1 = data.quantile(0.25)\n", "    q3 = data.quantile(0.75)\n", "    iqr = q3 - q1\n", "\n", "    lower_bound = q1 - (iqr_coefficient * iqr)\n", "    upper_bound = q3 + (iqr_coefficient * iqr)\n", "\n", "    is_outlier = (data < lower_bound) | (data > upper_bound)\n", "    return is_outlier"]}, {"cell_type": "code", "execution_count": null, "id": "3a34fe5f-da9b-422a-9b83-660bcd0d63ca", "metadata": {}, "outputs": [], "source": ["l2_id = [\n", "    1425,\n", "    31,\n", "    116,\n", "    198,\n", "    1097,\n", "    1956,\n", "    949,\n", "    1389,\n", "    1778,\n", "    950,\n", "    138,\n", "    1091,\n", "    1093,\n", "    1094,\n", "    2633,\n", "    63,\n", "    1367,\n", "    1369,\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "a50f406f-0325-4959-b7c8-25c5c7502814", "metadata": {}, "outputs": [], "source": ["# l2_id=[949, 1093,  950,  198, 1389,   31, 1094, 1956, 1425, 1778]"]}, {"cell_type": "code", "execution_count": null, "id": "65fa9050-10ee-4916-9a2c-5b4093e2b566", "metadata": {}, "outputs": [], "source": ["current_date = datetime.now(timezone(\"Asia/Kolkata\")).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "markdown", "id": "0684bb80-b250-4367-8b77-dde30731b4fc", "metadata": {}, "source": ["## Queries"]}, {"cell_type": "code", "execution_count": null, "id": "90e2486c-d33f-4b58-bbc7-fa31ab55702a", "metadata": {}, "outputs": [], "source": ["universe_query = f\"\"\"\n", "with dates as(\n", "select date as date_ from dwh.dim_date where date >= current_date - interval '30' day and date < current_date group by 1\n", "union\n", "select date as date_ from dwh.dim_date where date > current_date and date <= current_date + interval '7' day group by 1\n", "),\n", "\n", "universe as (\n", "select\n", "    d.date_,\n", "    pfma.facility_id,\n", "    po.outlet_id,\n", "    po.outlet_name as store_name,\n", "    cl.name as city_name,\n", "    ic.l0,\n", "    ic.l1,\n", "    ic.l2,\n", "    ic.l2_id,\n", "    ic.product_type,\n", "    pfma.item_id,\n", "    pfma.master_assortment_substate_id,\n", "    ic.name,\n", "    tm.tag_value,\n", "    case when tm.tag_value = '0' then 'Direct to Store - No Backend' else om.outlet_name end outlet_name\n", "from\n", "    rpc.product_facility_master_assortment pfma\n", "cross join\n", "    dates d\n", "inner join\n", "    rpc.item_category_details ic\n", "    on ic.item_id = pfma.item_id\n", "    and ic.l2_id in {tuple(l2_id)}\n", "inner join\n", "    (\n", "    SELECT DISTINCT a.item_id\n", "        FROM rpc.product_product a\n", "        JOIN (\n", "            SELECT item_id, MAX(id) AS id\n", "            FROM rpc.product_product\n", "            WHERE approved = 1 AND active = 1\n", "            GROUP BY 1\n", "        ) b ON a.id = b.id AND a.item_id = b.item_id\n", "    WHERE a.active = 1 AND a.lake_active_record AND a.approved = 1 AND a.perishable = 1\n", "    ) ma ON ma.item_id = ic.item_id\n", "inner join\n", "    po.physical_facility_outlet_mapping po\n", "    on po.facility_id = pfma.facility_id\n", "    and po.active = 1\n", "    and po.ars_active = 1\n", "    and po.lake_active_record = true\n", "inner join\n", "    retail.console_outlet co\n", "    on co.id = po.outlet_id \n", "    and co.active = 1\n", "    and co.lake_active_record = true\n", "    and co.business_type_id = 7\n", "inner join\n", "    retail.console_location cl\n", "    on cl.id = co.tax_location_id\n", "    and cl.lake_active_record = true\n", "inner join   \n", "    rpc.item_outlet_tag_mapping tm\n", "    on pfma.item_id = tm.item_id \n", "    and po.outlet_id = tm.outlet_id\n", "    and tm.tag_type_id = 8 \n", "    and tm.active = 1\n", "left join\n", "    po.physical_facility_outlet_mapping om\n", "    on cast(tm.tag_value as int) =  om.outlet_id    \n", "    and om.active = 1\n", "    and om.ars_active = 1\n", "    and om.lake_active_record = true\n", "where\n", "    pfma.active = 1\n", "    and pfma.master_assortment_substate_id in (1)\n", ")\n", "\n", "select * from universe \n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "fe4400a7-c052-45bd-b7df-4287bec5b61e", "metadata": {}, "outputs": [], "source": ["outlet_item_query = f\"\"\"\n", "select \n", "    date(date_) as date_,\n", "    outlet_id,\n", "    item_id,\n", "    qty_sold,\n", "    wtd_avail\n", "from \n", "    supply_etls.perishable_warehouse_store_details \n", "where \n", "    date_ between current_date - interval '30' day and current_date - interval '1' day\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "9e11dda0-7917-42c5-9384-6d6ad1271dee", "metadata": {}, "outputs": [], "source": ["outlet_opd_query = f\"\"\"\n", "with store_orders as(\n", "select \n", "    date(order_create_dt_ist) date_,\n", "    outlet_id,\n", "    count(distinct order_id) orders_count\n", "from\n", "    dwh.fact_sales_order_details o\n", "where\n", "    order_create_dt_ist > current_date - interval '100' day\n", "    and order_create_dt_ist < current_date\n", "    and o.order_current_status = 'DELIVERED'\n", "group by 1,2\n", "having \n", "    count(distinct order_id) > 10\n", "),\n", "\n", "old_stores as(\n", "select \n", "    outlet_id,\n", "    min(date_) as starting_date\n", "from\n", "    store_orders\n", "group by 1\n", "),\n", "\n", "final_base as (\n", "select \n", "    outlet_id, starting_date, extract(day from current_date - starting_date) store_age\n", "from \n", "    old_stores \n", "group by \n", "    1,2\n", "),\n", "\n", "current_stores as (\n", "select\n", "    outlet_id\n", "from\n", "    store_orders\n", "where\n", "    date_ >= current_date - interval '40' day\n", "group by 1\n", "),\n", "\n", "dates as(\n", "select date as date_ from dwh.dim_date where date >= current_date - interval '40' day and date < current_date group by 1\n", "),\n", "\n", "final_universe as(\n", "select\n", "    d.date_,\n", "    c.outlet_id\n", "from\n", "    current_stores c\n", "cross join\n", "    dates d\n", ")\n", "\n", "select\n", "    u.date_,\n", "    u.outlet_id,\n", "    so.orders_count as opd,\n", "    fb.starting_date,\n", "    fb.store_age\n", "from\n", "    final_universe u\n", "left join\n", "    store_orders so\n", "    on u.outlet_id = so.outlet_id\n", "    and u.date_ = so.date_\n", "left join\n", "    final_base fb\n", "    on u.outlet_id = fb.outlet_id\n", "where\n", "    u.date_ >= fb.starting_date\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "138c8362-7b11-474a-8bd7-9b993852a1ce", "metadata": {}, "outputs": [], "source": ["normalization_query = f\"\"\"\n", "select \n", "    city_name,\n", "    l2_category_id as l2_id,\n", "    dow_to,\n", "    dow_from,\n", "    qty_sold_factor,\n", "    searches_factor\n", "from\n", "    supply_etls.perishable_dow_normalization\n", "\"\"\""]}, {"cell_type": "markdown", "id": "92f33b40-833f-476a-b6e2-b0698ccc23e1", "metadata": {}, "source": ["## Data Import and Processing"]}, {"cell_type": "code", "execution_count": null, "id": "f0e30bf0-15e3-4ff5-8bf4-058f008e0e57", "metadata": {"tags": []}, "outputs": [], "source": ["universe_df = read_sql_query(universe_query, trino)\n", "outlet_item_df = read_sql_query(outlet_item_query, trino)\n", "outlet_opd_df = read_sql_query(outlet_opd_query, trino)\n", "normalization_df = read_sql_query(normalization_query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "9d59127e-bdea-4b02-b4bc-3d8a0a0161dd", "metadata": {}, "outputs": [], "source": ["universe_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "a640311e-9529-4acf-9d2f-cf7276b21dc9", "metadata": {}, "outputs": [], "source": ["past_universe_df = universe_df.query(f\"date_<'{current_date}'\").copy()\n", "future_universe_df = universe_df.query(f\"date_>'{current_date}'\").copy()"]}, {"cell_type": "code", "execution_count": null, "id": "8826fd26-e285-4e90-a2e8-ed8d340740ff", "metadata": {}, "outputs": [], "source": ["del universe_df\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "33e8326f-899d-4ac1-a636-fd00fed5e696", "metadata": {}, "outputs": [], "source": ["## Sales and Avail data Merging with universe\n", "past_universe_df = past_universe_df.merge(\n", "    outlet_item_df, on=[\"date_\", \"outlet_id\", \"item_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8314238c-0584-403d-992a-caa67668e01a", "metadata": {}, "outputs": [], "source": ["del outlet_item_df\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "b5bbb592-2754-4a45-9bff-049bcb45dd23", "metadata": {}, "outputs": [], "source": ["## Sales & Availability Handling\n", "\n", "past_universe_df[\"qty_sold\"].fillna(0, inplace=True)\n", "past_universe_df[\"wtd_avail\"].fillna(\n", "    0, inplace=True\n", ")  ## backfill and forward fill with non disruption dates\n", "past_universe_df[\"wtd_avail\"] = np.where(\n", "    past_universe_df[\"wtd_avail\"] <= 0.1, np.NaN, past_universe_df[\"wtd_avail\"]\n", ")\n", "past_universe_df[\"exp_qty_sold\"] = (\n", "    past_universe_df[\"qty_sold\"]\n", "    * 1.000\n", "    / np.where(past_universe_df[\"wtd_avail\"] < 0.4, 0.4, past_universe_df[\"wtd_avail\"])\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9396bf28-5edf-47f3-ad32-9811641c0161", "metadata": {}, "outputs": [], "source": ["## OPD Outlier Detection\n", "outlet_opd_df = outlet_opd_df.query(f\"date_>=starting_date\").copy()\n", "outlet_opd_df[\"opd\"].fillna(0, inplace=True)\n", "outlet_opd_df.sort_values(by=[\"outlet_id\", \"date_\"], inplace=True)\n", "outlet_opd_df[\"l7_rolling_mean\"] = (\n", "    outlet_opd_df.groupby(\"outlet_id\")[\"opd\"].shift(1).transform(lambda x: x.rolling(7).mean())\n", ")\n", "outlet_opd_df[\"diff\"] = np.abs(outlet_opd_df[\"opd\"] - outlet_opd_df[\"l7_rolling_mean\"])\n", "outlet_opd_df[\"is_outlier\"] = outlet_opd_df.groupby([\"outlet_id\"], as_index=False)[\n", "    \"diff\"\n", "].transform(lambda x: detect_iqr_outliers(x, 1.5))\n", "# outlet_opd_df.dropna(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "2f952538-23db-4f05-983f-2659d95a56d7", "metadata": {}, "outputs": [], "source": ["# Handling New Stores with age less than 30 days as no outlier\n", "outlet_opd_df[\"is_outlier\"] = np.where(\n", "    outlet_opd_df[\"store_age\"] <= 30, False, outlet_opd_df[\"is_outlier\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "eafb1aff-c0dc-4f2b-9238-e283b4166d68", "metadata": {}, "outputs": [], "source": ["## Merging Outlier Dates with sales data\n", "past_universe_df = past_universe_df.merge(\n", "    outlet_opd_df[[\"date_\", \"outlet_id\", \"is_outlier\"]].drop_duplicates(),\n", "    on=[\"date_\", \"outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "## Filling unhandled stores with False\n", "past_universe_df[\"is_outlier\"].fillna(False, inplace=True)\n", "\n", "## Filling Disruption Days Sales with NaN Values\n", "past_universe_df[\"exp_qty_sold\"] = np.where(\n", "    past_universe_df[\"is_outlier\"] == True, np.NaN, past_universe_df[\"exp_qty_sold\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "45247ce9-6401-4362-9342-e4a72acfa06a", "metadata": {}, "outputs": [], "source": ["past_universe_df[\"dow\"] = pd.to_datetime(past_universe_df[\"date_\"]).dt.day_of_week\n", "past_universe_df[\"week_number\"] = pd.to_datetime(past_universe_df[\"date_\"]).dt.isocalendar().week"]}, {"cell_type": "code", "execution_count": null, "id": "e8047818-a7a9-401a-b602-e909e6eff493", "metadata": {}, "outputs": [], "source": ["## Filling NaN values with same dow sales or previous or forward day sales\n", "\n", "past_universe_df.sort_values(by=[\"outlet_id\", \"item_id\", \"date_\"], inplace=True)\n", "past_universe_df[\"exp_qty_sold\"] = past_universe_df.groupby([\"outlet_id\", \"item_id\", \"dow\"])[\n", "    \"exp_qty_sold\"\n", "].transform(\"ffill\")"]}, {"cell_type": "code", "execution_count": null, "id": "2a74b6a7-e371-422c-9ff9-a10cf8d23d0c", "metadata": {}, "outputs": [], "source": ["past_universe_df[\"exp_qty_sold\"].fillna(-1, inplace=True)\n", "past_universe_df[\"date_rank\"] = past_universe_df.groupby([\"outlet_id\", \"item_id\"])[\"date_\"].rank(\n", "    method=\"dense\", ascending=False\n", ")\n", "past_universe_df[\"dow_from\"] = pd.to_datetime(past_universe_df[\"date_\"]).dt.day_of_week"]}, {"cell_type": "code", "execution_count": null, "id": "258ae041-273b-4b26-bc42-aec18b75b670", "metadata": {}, "outputs": [], "source": ["## Creating Future Universe from main Universe\n", "\n", "future_universe_df[\"dow_to\"] = pd.to_datetime(future_universe_df[\"date_\"]).dt.day_of_week"]}, {"cell_type": "code", "execution_count": null, "id": "57419d8d-c441-4646-bd77-292e17412afc", "metadata": {}, "outputs": [], "source": ["## Creating Final Base from Future Universe with left joining Past Universe df\n", "\n", "past_universe_df_copy = past_universe_df.query(f\"date_rank<=7\").copy()\n", "master_df = future_universe_df.merge(\n", "    past_universe_df_copy[[\"date_\", \"outlet_id\", \"item_id\", \"exp_qty_sold\", \"dow_from\"]],\n", "    on=[\"outlet_id\", \"item_id\"],\n", "    how=\"left\",\n", "    suffixes=(\"\", \"_past\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "32c97294-95f5-45eb-a071-189877a86a8d", "metadata": {}, "outputs": [], "source": ["del future_universe_df, past_universe_df\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "a8d98995-b056-48e2-9f31-89559ff1532d", "metadata": {}, "outputs": [], "source": ["## Defining Infinte Date when Past DOW matches with Future DOW to get max rank\n", "## Conisdering only rows with exp sales qty as >= 0 for weekdays standardization\n", "## Calculating Weighted Avg Exp Sales Qty at date row wise\n", "master_df[\"date__past\"] = np.where(\n", "    master_df[\"dow_to\"] == master_df[\"dow_from\"], \"2200-01-01\", master_df[\"date__past\"]\n", ")\n", "master_df[\"rank_date_from\"] = (\n", "    master_df.query(f\"exp_qty_sold>=0\")\n", "    .groupby([\"date_\", \"outlet_id\", \"item_id\"])[\"date__past\"]\n", "    .rank(method=\"dense\", ascending=True)\n", ")\n", "master_df[\"rank_date_from\"].fillna(0, inplace=True)\n", "master_df[\"sum_rank\"] = master_df.groupby([\"date_\", \"outlet_id\", \"item_id\"])[\n", "    \"rank_date_from\"\n", "].transform(\"sum\")\n", "master_df[\"dow_weight\"] = master_df[\"rank_date_from\"] / master_df[\"sum_rank\"]\n", "master_df[\"wtd_exp_qty_sold\"] = np.abs(master_df[\"dow_weight\"] * master_df[\"exp_qty_sold\"])"]}, {"cell_type": "code", "execution_count": null, "id": "86553c84-555e-49ec-9bbc-0c4088e81af4", "metadata": {}, "outputs": [], "source": ["## Left Join Normalization factors\n", "master_df = master_df.merge(\n", "    normalization_df, on=[\"city_name\", \"l2_id\", \"dow_to\", \"dow_from\"], how=\"left\"\n", ")\n", "\n", "## Also Joining with Normalization factore calculated for Pan India\n", "master_df = master_df.merge(\n", "    normalization_df.query(f\"city_name=='Pan_India'\"),\n", "    on=[\"l2_id\", \"dow_to\", \"dow_from\"],\n", "    how=\"left\",\n", "    suffixes=(\"\", \"_pan_india\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "10fb9561-96d2-4953-b3fe-6bf8646d5ec1", "metadata": {}, "outputs": [], "source": ["## Final Forecast Number Calculations\n", "\n", "master_df[\"forecast\"] = master_df[\"wtd_exp_qty_sold\"] * (\n", "    0.5\n", "    * np.where(\n", "        master_df[\"qty_sold_factor\"].isna(),\n", "        master_df[\"qty_sold_factor_pan_india\"],\n", "        master_df[\"qty_sold_factor\"],\n", "    )\n", "    + 0.5\n", "    * np.where(\n", "        master_df[\"searches_factor\"].isna(),\n", "        master_df[\"searches_factor_pan_india\"],\n", "        master_df[\"searches_factor\"],\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a5315979-7588-4a24-9057-04f341d5a4bc", "metadata": {}, "outputs": [], "source": ["## Aggregating Forecast Numbers for Future Dates\n", "\n", "final_df = master_df.groupby(\n", "    [\n", "        \"date_\",\n", "        \"facility_id\",\n", "        \"outlet_id\",\n", "        \"store_name\",\n", "        \"city_name\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"l2_id\",\n", "        \"product_type\",\n", "        \"item_id\",\n", "        \"master_assortment_substate_id\",\n", "        \"name\",\n", "        \"tag_value\",\n", "        \"outlet_name\",\n", "    ],\n", "    as_index=False,\n", ")[\"forecast\"].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "d983ce02-1d62-4db6-a66e-4728fe5685b7", "metadata": {}, "outputs": [], "source": ["del master_df\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "f69f1924-1701-4fb3-a07f-361590a762a2", "metadata": {}, "source": ["## Missing, Upcoming and New Stores <=15 Days Forecast Handling"]}, {"cell_type": "code", "execution_count": null, "id": "2f616760-c48a-4a9f-a57a-21686e9079cc", "metadata": {}, "outputs": [], "source": ["missing_stores = f\"\"\"\n", "with new_stores as (\n", "select\n", "    s.facility_name,\n", "    s.facility_id,\n", "    s.outlet_id,\n", "    s.store_type,\n", "    s.final_ob_date,\n", "    s.sister_store_name,\n", "    s.sister_store_facility_id,\n", "    coalesce(a.ars_active,0) ars_active,\n", "    case when co.device_id = 143 then 1 else 0 end outlet_id_active\n", "from\n", "    supply_etls.e3_new_darkstores s\n", "left join\n", "    po.physical_facility_outlet_mapping a\n", "    on s.facility_id = a.facility_id\n", "    and a.active = 1\n", "left join\n", "    retail.console_outlet co\n", "    on s.outlet_id = co.id\n", "    and co.active = 1\n", "where\n", "    s.final_ob_date >= current_date - interval '3' day\n", "    and s.final_ob_date <= current_date + interval '2' day\n", "group by 1,2,3,4,5,6,7,8,9\n", "having \n", "    coalesce(a.ars_active,0) = 1 and (case when co.device_id = 143 then 1 else 0 end) = 1\n", ")\n", "\n", "select \n", "    outlet_id\n", "from\n", "    new_stores\n", "group by 1\n", "\"\"\"\n", "missing_stores = read_sql_query(missing_stores, trino)\n", "missing_stores"]}, {"cell_type": "code", "execution_count": null, "id": "fc3123a1-ce8b-4188-855b-756feee9d450", "metadata": {}, "outputs": [], "source": ["new_outlet_ids = list(\n", "    set(outlet_opd_df.query(\"store_age<=15\")[\"outlet_id\"].unique())\n", "    | set(missing_stores[\"outlet_id\"].unique())\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "25a3e169-e835-4b8c-a60d-195dde817b4a", "metadata": {}, "outputs": [], "source": ["new_store_forecasts = f\"\"\"\n", "with new_store_base_universe as(\n", "SELECT \n", "    facility_id, item_id, date_, MAX(updated_at) AS updated_at\n", "FROM \n", "    supply_etls.new_store_forecast_logs\n", "where\n", "    outlet_id in {tuple(new_outlet_ids)}\n", "    and date_ > current_date \n", "    AND date_ <= current_date + interval '7' day\n", "GROUP BY 1,2,3\n", "),\n", "\n", "new_store_final_universe as(\n", "select\n", "    a.*\n", "from\n", "    supply_etls.new_store_forecast_logs a\n", "inner join\n", "    new_store_base_universe b\n", "    on a.facility_id = b.facility_id AND a.item_id = b.item_id AND a.date_ = b.date_ AND a.updated_at = b.updated_at\n", "),\n", "\n", "new_store_forecast AS (\n", "SELECT \n", "    a.date_, a.facility_id, a.item_id, a.outlet_id, cl.name as city,\n", "    max(CASE WHEN a.item_nearby_fps IS NULL THEN 0 ELSE item_nearby_fps END) AS item_nearby_fps,\n", "    max(CASE WHEN a.item_recent_fps IS NULL THEN 0 ELSE item_recent_fps END) AS item_recent_fps, \n", "    max(CASE WHEN a.item_avail_fps IS NULL THEN 0 ELSE item_avail_fps END) AS item_avail_fps\n", "FROM \n", "    new_store_final_universe a\n", "INNER JOIN \n", "    retail.console_outlet co \n", "    ON co.id = a.outlet_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "LEFT JOIN \n", "    retail.console_location cl \n", "    ON cl.id = co.tax_location_id AND cl.lake_active_record\n", "inner join \n", "    (SELECT DISTINCT a.item_id\n", "            FROM rpc.product_product a\n", "            JOIN (\n", "                SELECT item_id, MAX(id) AS id\n", "                FROM rpc.product_product\n", "                WHERE approved = 1 AND active = 1\n", "                GROUP BY 1\n", "            ) b ON a.id = b.id AND a.item_id = b.item_id\n", "            WHERE a.active = 1 AND a.lake_active_record AND a.approved = 1 AND a.perishable = 1\n", "    ) ma \n", "    ON ma.item_id = a.item_id\n", "inner join\n", "    rpc.item_category_details ic\n", "    on ic.item_id = a.item_id\n", "    and ic.l2_id in {tuple(l2_id)}\n", "group by 1,2,3,4,5\n", ")\n", "\n", "select * from new_store_forecast \n", "\"\"\"\n", "new_store_df = read_sql_query(new_store_forecasts, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "61952e82-1d69-4849-b621-544f7ebcc7ed", "metadata": {}, "outputs": [], "source": ["new_store_df[\"item_min_fps\"] = (0.75 * new_store_df[\"item_nearby_fps\"]) + (\n", "    0.25 * new_store_df[\"item_recent_fps\"]\n", ")\n", "\n", "new_store_df[\"forecast\"] = new_store_df[[\"item_min_fps\", \"item_avail_fps\"]].max(axis=1)\n", "\n", "new_store_df[\"forecast\"] = new_store_df[\"forecast\"].fillna(0)\n", "\n", "new_store_df[\"forecast\"] = np.where(\n", "    new_store_df[\"forecast\"] > 1, new_store_df[\"forecast\"], 1\n", ").astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "1ae507d9-69a4-42fa-95e7-e35a42b377ea", "metadata": {}, "outputs": [], "source": ["## Adding New Stores and overall forecast together\n", "\n", "df_concat = pd.concat(\n", "    [\n", "        final_df[[\"date_\", \"facility_id\", \"outlet_id\", \"item_id\", \"forecast\"]],\n", "        new_store_df[[\"date_\", \"facility_id\", \"outlet_id\", \"item_id\", \"forecast\"]],\n", "    ],\n", "    ignore_index=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0f2edbd7-d593-4906-8c02-616d15d5defd", "metadata": {}, "outputs": [], "source": ["final_forecast_df = df_concat.drop_duplicates(\n", "    subset=[\"date_\", \"facility_id\", \"outlet_id\", \"item_id\"], keep=\"last\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "14c29a7e-7ad8-4a07-a10b-45f9f811a879", "metadata": {}, "outputs": [], "source": ["del final_df, df_concat, new_store_df, outlet_opd_df\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "ab615677-7452-4ce3-a807-e0862e82f0dd", "metadata": {}, "outputs": [], "source": ["ist_timezone = timezone(\"Asia/Kolkata\")\n", "current_time_ist = datetime.now(ist_timezone)\n", "final_forecast_df[\"updated_at_ist\"] = pd.to_datetime(current_time_ist)"]}, {"cell_type": "code", "execution_count": null, "id": "ae0f6a9b-6015-4275-ab68-7588755da298", "metadata": {}, "outputs": [], "source": ["final_forecast_df[\"date_\"] = pd.to_datetime(final_forecast_df[\"date_\"])\n", "final_forecast_df[\"facility_id\"] = final_forecast_df[\"facility_id\"].astype(\"int\")\n", "final_forecast_df[\"outlet_id\"] = final_forecast_df[\"outlet_id\"].astype(\"int\")\n", "final_forecast_df[\"item_id\"] = final_forecast_df[\"item_id\"].astype(\"int\")\n", "final_forecast_df[\"forecast\"] = round(final_forecast_df[\"forecast\"].astype(\"float\"), 2)"]}, {"cell_type": "code", "execution_count": null, "id": "ac421a43-23b6-45b4-86dc-0fb8ec6a875a", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"timestamp(6)\", \"description\": \"date\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility_id\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"outlet_id\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "    {\"name\": \"forecast\", \"type\": \"real\", \"description\": \"forecast qty\"},\n", "    {\"name\": \"updated_at_ist\", \"type\": \"timestamp(6)\", \"description\": \"updated timestamp\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "dc0ddba8-2101-4e01-a95d-b471b560230e", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"testing_revamped_perishable_forecast\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"date_\", \"outlet_id\", \"item_id\"],\n", "    \"incremental_key\": \"date_\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"append\",  # append, truncate or upsert,\n", "    \"table_description\": \"Details of Perishable forecast at items store level\",\n", "}\n", "\n", "pb.to_trino(final_forecast_df, **kwargs)\n", "\n", "channel = \"table-updates-tanishq\"\n", "text_req = \"\\n <@U07668YK86R> \\n Perishable Testing Forecast updated Today \"\n", "pb.send_slack_message(\n", "    channel=channel,\n", "    text=text_req,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "aaf03700-feb2-47a5-b4d3-851650a6f9b9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}