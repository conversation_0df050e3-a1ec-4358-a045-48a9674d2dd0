alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 3
dag_name: perishable_ordering_logic
dag_type: workflow
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: fresh
notebooks:
- executor_config:
    load_type: medium
    node_type: od
  name: batter_ordering_notebook
  parameters: null
  retries: 2
  tag: parallel
- executor_config:
    load_type: medium
    node_type: od
  name: paneer_ordering_notebook
  parameters: null
  retries: 2
  tag: parallel
- executor_config:
    load_type: medium
    node_type: od
  name: egg_ordering_notebook
  parameters: null
  retries: 2
  tag: parallel
- executor_config:
    load_type: medium
    node_type: od
  name: curd_ordering_notebook
  parameters: null
  retries: 2
  tag: parallel
- executor_config:
    load_type: medium
    node_type: od
  name: yogurt_ordering_notebook
  parameters: null
  retries: 2
  tag: parallel
- executor_config:
    load_type: high-mem
    node_type: od
  name: breads_ordering_notebook
  parameters: null
  retries: 2
  tag: parallel
- executor_config:
    load_type: medium
    node_type: od
  name: bread_bun_pizza_notebook
  parameters: null
  retries: 2
  tag: parallel
- executor_config:
    load_type: medium
    node_type: od
  name: speciality_breads_ordering_notebook
  parameters: null
  retries: 2
  tag: parallel
- executor_config:
    load_type: medium
    node_type: od
  name: pd_ordering_notebook
  parameters: null
  retries: 2
  tag: parallel
- executor_config:
    load_type: medium
    node_type: od
  name: meats_ordering_notebook
  parameters: null
  retries: 2
  tag: parallel
- executor_config:
    load_type: medium
    node_type: od
  name: final_ars_ordering_notebook
  parameters: null
  retries: 2
  tag: parallel_2
owner:
  email: <EMAIL>
  slack_id: U05CCTXLBU1
path: fresh/perishable_forecasting/workflow/perishable_ordering_logic
paused: false
pool: fresh_pool
project_name: perishable_forecasting
schedule:
  end_date: '2025-08-21T00:00:00'
  interval: 30 15 * * *
  start_date: '2025-06-04T00:00:00'
schedule_type: fixed
sla: 121 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 1
