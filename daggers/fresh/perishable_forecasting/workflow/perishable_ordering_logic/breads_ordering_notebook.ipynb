{"cells": [{"cell_type": "code", "execution_count": null, "id": "09c38fe8-3e8a-4208-b5d4-6485711c999a", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import os\n", "import time\n", "import gc\n", "import requests\n", "\n", "import calendar\n", "from datetime import datetime as dt\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.options.mode.chained_assignment = None  # default='warn'\n", "pd.set_option(\"display.max_columns\", 500)"]}, {"cell_type": "code", "execution_count": null, "id": "f0148818-820e-43c0-904b-e29949953a73", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pytz\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")\n", "\n", "current_time = datetime.now(IST) - <PERSON><PERSON><PERSON>(days=0)\n", "\n", "today_date = current_time.strftime(\"%Y-%m-%d\")\n", "\n", "today_date"]}, {"cell_type": "code", "execution_count": null, "id": "8fd3c3bc-1d39-4a0f-93a6-5cab9f9d86de", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(\n", "            f\"Read attempt: {attempt}... \\nRun started at {datetime.now() + timed<PERSON>ta(hours = 5.5)}\"\n", "        )\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "markdown", "id": "e0465249-feac-4e4a-bd74-780e34205fbb", "metadata": {}, "source": ["## Remove Manual Disruption Input"]}, {"cell_type": "code", "execution_count": null, "id": "97813c52-c1b9-439e-9285-dcff731d04a9", "metadata": {}, "outputs": [], "source": ["city_l2_disruption_df = pb.from_sheets(\n", "    \"1wFQBrKUB6LIt31VgDT6NoKBfva-DMLpPQtQxnmZRb3w\", \"config:l2-disruption-inputs\"\n", ")\n", "\n", "# city_l2_disruption_df = pd.read_csv(\"Perishable-Ordering Params - config_l2-disruption-inputs.csv\")\n", "\n", "city_l2_disruption_df[\"start_date\"] = pd.to_datetime(city_l2_disruption_df[\"start_date\"])\n", "\n", "city_l2_disruption_df[\"end_date\"] = pd.to_datetime(city_l2_disruption_df[\"end_date\"])\n", "\n", "l2_mapping = pd.DataFrame(\n", "    {\n", "        \"Batter\",\n", "        \"Breads\",\n", "        \"Curd\",\n", "        \"Eggs\",\n", "        \"Paneer\",\n", "        \"Perishable Dairy\",\n", "        \"Yogurt\",\n", "        \"Other Perishable\",\n", "    },\n", "    columns=[\"category_bucket\"],\n", ")\n", "\n", "l2_mapping[\"category\"] = \"All\"\n", "\n", "city_l2_disruption_df = city_l2_disruption_df.merge(l2_mapping, on=[\"category\"], how=\"left\")\n", "\n", "city_l2_disruption_df[\"category_bucket\"] = np.where(\n", "    city_l2_disruption_df[\"category_bucket\"].isna(),\n", "    city_l2_disruption_df[\"category\"],\n", "    city_l2_disruption_df[\"category_bucket\"],\n", ")\n", "\n", "city_l2_disruption_df[\"date_\"] = list(\n", "    map(\n", "        lambda x, y: pd.date_range(start=x, end=y),\n", "        city_l2_disruption_df[\"start_date\"],\n", "        city_l2_disruption_df[\"end_date\"],\n", "    )\n", ")\n", "\n", "city_l2_disruption_df = city_l2_disruption_df.explode(\"date_\").drop(\n", "    [\"start_date\", \"end_date\"], axis=1\n", ")\n", "\n", "if city_l2_disruption_df.shape[0] > 0:\n", "    city_l2_disruption_df[\"date_\"] = pd.to_datetime(city_l2_disruption_df[\"date_\"])\n", "else:\n", "    city_l2_disruption_df[\"date_\"] = \"\"\n", "\n", "city_l2_disruption_df = city_l2_disruption_df.reset_index()\n", "\n", "city_disruption_max_df = city_l2_disruption_df.groupby([\"city\", \"category_bucket\", \"date_\"]).agg(\n", "    {\"index\": \"max\"}\n", ")\n", "\n", "city_l2_disruption_df = city_l2_disruption_df.merge(\n", "    city_disruption_max_df,\n", "    on=[\"city\", \"category_bucket\", \"date_\", \"index\"],\n", "    how=\"inner\",\n", ")\n", "\n", "city_l2_disruption_df = (\n", "    city_l2_disruption_df[[\"city\", \"category_bucket\", \"date_\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", "    .rename(columns={\"category_bucket\": \"l2\"})\n", ")\n", "\n", "city_l2_disruption_df[\"flag\"] = 1\n", "\n", "city_l2_disruption_df.shape"]}, {"cell_type": "markdown", "id": "d666f63a-8711-49fb-b915-d0a1017c23b3", "metadata": {}, "source": ["# Bread Forecast Base"]}, {"cell_type": "code", "execution_count": null, "id": "577462ee-e9b0-42d4-b03c-40487574bcc6", "metadata": {}, "outputs": [], "source": ["l2_df = pb.from_sheets(\n", "    sheetid=\"1wFQBrKUB6LIt31VgDT6NoKBfva-DMLpPQtQxnmZRb3w\",\n", "    sheetname=\"perishable_l2\",\n", ")\n", "\n", "# l2_df = pd.read_csv(\"Perishable-Ordering Params - perishable_l2.csv\")\n", "l2_df[\"l2_id\"] = l2_df[\"l2_id\"].astype(int)\n", "l2_df = l2_df[l2_df[\"l2\"] == \"Regular Bread\"]\n", "l2_df"]}, {"cell_type": "code", "execution_count": null, "id": "d2ed9f91-65b8-4c6e-8de0-568904d337d4", "metadata": {}, "outputs": [], "source": ["if l2_df.shape[0] > 0:\n", "    l2_id_list = list(l2_df[\"l2_id\"].unique())\n", "    if len(l2_id_list) < 2:\n", "        l2_id_list.append(-1)\n", "        l2_id_list.append(-2)\n", "    l2_id_list = tuple(l2_id_list)\n", "    len(l2_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "c07b1a5d-a52c-484a-ad26-8deadf05a627", "metadata": {}, "outputs": [], "source": ["l2_id_list"]}, {"cell_type": "markdown", "id": "ed37a12e-deea-4dbb-a07c-6205af036103", "metadata": {}, "source": ["## Base Assortment Fetch"]}, {"cell_type": "code", "execution_count": null, "id": "301dd495-8cb4-41b0-8660-3e6e872f88ec", "metadata": {}, "outputs": [], "source": ["base_query = f\"\"\"\n", "    WITH active_stores AS (\n", "        SELECT DISTINCT pfom.facility_id, pfom.outlet_id, bfom.facility_id AS be_facility_id\n", "        FROM po.physical_facility_outlet_mapping pfom \n", "        JOIN retail.console_outlet co ON co.id = pfom.outlet_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "        JOIN po.bulk_facility_outlet_mapping bfom ON bfom.outlet_id = pfom.outlet_id AND bfom.active = True AND bfom.lake_active_record\n", "        WHERE ars_active = 1 AND pfom.active = 1 AND pfom.is_primary = 1 AND pfom.lake_active_record\n", "    ),\n", "\n", "    assortment AS (\n", "        SELECT DISTINCT cl.name AS city, a.facility_id, outlet_id, a.item_id, be_facility_id\n", "        FROM rpc.product_facility_master_assortment a\n", "        JOIN active_stores b ON a.facility_id = b.facility_id\n", "        JOIN (\n", "            SELECT DISTINCT a.item_id\n", "            FROM rpc.product_product a\n", "            JOIN (\n", "                SELECT item_id, MAX(id) AS id\n", "                FROM rpc.product_product\n", "                WHERE approved = 1 AND active = 1\n", "                GROUP BY 1\n", "            ) b ON a.id = b.id AND a.item_id = b.item_id\n", "            WHERE a.active = 1 AND a.lake_active_record AND a.approved = 1 AND a.perishable = 1\n", "        ) ma ON ma.item_id = a.item_id\n", "        JOIN retail.console_outlet co ON co.id = b.outlet_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "        LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id AND cl.lake_active_record\n", "        WHERE a.item_id IN (SELECT DISTINCT item_id FROM rpc.item_category_details WHERE l2_id IN {l2_id_list})\n", "        AND a.active = 1 AND a.master_assortment_substate_id = 1 AND a.lake_active_record\n", "    ),\n", "\n", "    be_mapping AS (\n", "        SELECT DISTINCT item_id, outlet_id, cb.facility_id AS be_facility_id , cb.name AS be_outlet_name, cb.id AS be_outlet_id\n", "        FROM rpc.item_outlet_tag_mapping tm\n", "        JOIN retail.console_outlet cb ON cb.id = CAST(tag_value AS int) AND cb.active = 1 AND cb.lake_active_record\n", "        WHERE tm.active = 1 AND tm.tag_type_id = 8 AND tm.lake_active_record\n", "    ),\n", "\n", "    final AS (\n", "        SELECT city, facility_id, a.outlet_id, icd.l2 , a.item_id, icd.name AS item_name, icd.product_type AS ptype, a.be_facility_id , b.be_outlet_id, b.be_outlet_name\n", "        FROM assortment a\n", "        JOIN be_mapping b ON a.item_id = b.item_id AND a.outlet_id = b.outlet_id AND a.be_facility_id = b.be_facility_id\n", "        JOIN rpc.item_category_details icd ON icd.item_id = a.item_id AND icd.lake_active_record\n", "    )\n", "    \n", "    SELECT city, facility_id, outlet_id, ptype, item_id, item_name, be_facility_id, be_outlet_id, be_outlet_name, l2\n", "    FROM final\n", "\"\"\"\n", "base_df = read_sql_query(base_query, trino)\n", "\n", "base_df[\"be_facility_id\"] = base_df[\"be_facility_id\"].fillna(0).astype(int)\n", "\n", "base_df[\"l2\"] = \"Breads\"\n", "\n", "base_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "705190f6-4734-4bf8-9dde-75661bb92e71", "metadata": {}, "outputs": [], "source": ["## New Store"]}, {"cell_type": "code", "execution_count": null, "id": "88480737-c594-421d-9dc4-8ab55eec5c50", "metadata": {}, "outputs": [], "source": ["new_store = f\"\"\"\n", "with new_stores as (\n", "select\n", "    s.facility_name,\n", "    s.facility_id,\n", "    s.store_type,\n", "    s.final_ob_date,\n", "    s.sister_store_name,\n", "    s.sister_store_facility_id,\n", "    coalesce(a.ars_active,0) ars_active,\n", "    case when co.device_id = 143 then 1 else 0 end outlet_id_active\n", "from\n", "    supply_etls.e3_new_darkstores s\n", "left join\n", "    po.physical_facility_outlet_mapping a\n", "    on s.facility_id = a.facility_id\n", "    and a.active = 1\n", "left join\n", "    retail.console_outlet co\n", "    on s.outlet_id = co.id\n", "    and co.active = 1\n", "where\n", "    -- lower(s.store_type) not in ('new store')\n", "    s.final_ob_date > date'2024-03-15'\n", "    and s.final_ob_date < current_date + interval '15' day\n", "group by 1,2,3,4,5,6,7,8\n", "having \n", "    coalesce(a.ars_active,0) = 1 and (case when co.device_id = 143 then 1 else 0 end) = 1\n", ")\n", "\n", "select \n", "    facility_id,\n", "    abs(date_diff('day',DATE('{today_date}'),max(final_ob_date))) as age\n", "from\n", "    new_stores\n", "    where final_ob_date between DATE('{today_date}') - interval '15' day and DATE('{today_date}')\n", "    group by 1\n", "\"\"\"\n", "new_store_mapping = read_sql_query(new_store, trino)\n", "new_store_facility_list = tuple(\n", "    set(new_store_mapping[new_store_mapping[\"age\"] <= 15][\"facility_id\"].unique())\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "aa728072-bb49-4a54-9c9b-6fae0f731102", "metadata": {}, "outputs": [], "source": ["new_store_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e9b50e4f-b183-45eb-b439-a60196a941da", "metadata": {}, "outputs": [], "source": ["## Polygon Change Store"]}, {"cell_type": "code", "execution_count": null, "id": "f9bf6fb3-9e71-49fa-9ae9-95070b8dba3d", "metadata": {}, "outputs": [], "source": ["buffer_store = f\"\"\"\n", "with\n", "frontend_merchant_mapping as\n", "        (select * from dwh.dim_merchant_outlet_facility_mapping\n", "            where \n", "                is_frontend_merchant_active = true\n", "                and\n", "                    is_backend_merchant_active = true\n", "                and \n", "                    is_pos_outlet_active = 1\n", "                and \n", "                    is_mapping_enabled = true\n", "                and \n", "                    is_express_store = true\n", "                and \n", "                    is_current_mapping_active = true\n", "                and \n", "                    is_current = true\n", "                and \n", "                    pos_outlet_name <> 'SS Gurgaon Test Store'\n", "),\n", "\n", "store_polygon_updates as (\n", "select \n", "    cr.updated_at + interval '5' hour + interval '30' minute as updated_time, \n", "    f.external_id as merchant_id, \n", "    json_query(cr.meta, 'strict $.business_impact.old_order_count') as old_order_count,\n", "    json_query(cr.meta, 'strict $.business_impact.new_order_count') as new_orders_count,\n", "    json_query(cr.diff, 'strict $.polygon.info') as change_in_area\n", "from \n", "    sauron.change_requests cr \n", "join \n", "    sauron.feature f \n", "    on f.id=cr.feature_id\n", "where \n", "    f.layer_id = 6 \n", "    AND cr.updated_at + interval '5' hour + interval '30' minute > CURRENT_DATE - interval '30' day \n", "    and cr.updated_at + interval '5' hour + interval '30' minute < CURRENT_DATE \n", "    AND cr.state = 'MERGED'\n", "    -- and f.external_id in (33207)\n", ")\n", "\n", "select \n", "    fm.facility_id,\n", "    fm.pos_outlet_id as outlet_id,\n", "    date(max(updated_time)) as change_date,\n", "    abs(date_diff('day',DATE('{today_date}'),date(max(updated_time)))) as change_age\n", "    \n", "from \n", "    store_polygon_updates s\n", "left join frontend_merchant_mapping  fm on fm.frontend_merchant_id = s.merchant_id\n", "where s.updated_time >  DATE('{today_date}') - interval '10' day \n", "-- and DATE('{today_date}') - interval '1' day\n", "and fm.facility_id not in {new_store_facility_list}\n", "group by 1,2\n", "    \"\"\"\n", "\n", "buffer_store_mapping = read_sql_query(buffer_store, trino)\n", "buffer_store_mapping[\"facility_id\"] = buffer_store_mapping[\"facility_id\"].astype(int)\n", "buffer_store_mapping[\"change_date\"] = pd.to_datetime(buffer_store_mapping[\"change_date\"])\n", "# buffer_store_facility_list = list(set(buffer_store_mapping[\"facility_id\"].unique()))"]}, {"cell_type": "code", "execution_count": null, "id": "c70a0d1d-f590-487f-8b87-a7b682d096a3", "metadata": {}, "outputs": [], "source": ["buffer_store_mapping.head()"]}, {"cell_type": "markdown", "id": "54d826ca-2183-47e7-af44-cef8cc914c70", "metadata": {}, "source": ["## Item, Facility, Outlet list"]}, {"cell_type": "code", "execution_count": null, "id": "875e7bfc-ea70-40e1-b703-d1ea999768f4", "metadata": {}, "outputs": [], "source": ["facility_id_list = tuple(set(base_df[\"facility_id\"].to_list()))\n", "outlet_id_list = tuple(set(base_df[\"outlet_id\"].to_list()))\n", "item_id_list = tuple(set(base_df[\"item_id\"].to_list()))\n", "buffer_store_facility_list = tuple(set(buffer_store_mapping[\"facility_id\"].to_list()))\n", "buffer_store_outlet_list = tuple(set(buffer_store_mapping[\"outlet_id\"].to_list()))\n", "\n", "len(item_id_list), len(facility_id_list), len(outlet_id_list), len(buffer_store_facility_list), len(\n", "    buffer_store_outlet_list\n", ")"]}, {"cell_type": "markdown", "id": "bcb625f5-b336-4485-9c30-5973555f5a5a", "metadata": {}, "source": ["## Hourly Availability"]}, {"cell_type": "code", "execution_count": null, "id": "a3427c5c-8920-4916-8178-059e3bca10c8", "metadata": {}, "outputs": [], "source": ["base_query = f\"\"\"\n", "with base as (\n", "    select \n", "        a.snapshot_date_ist as date_,\n", "        a.outlet_id,\n", "        co.facility_id,\n", "        a.item_id,\n", "        cast(snapshot_hr_mm/100 as int) as hour_,\n", "        MAX(current_inventory) as current_inventory\n", "        from\n", "        dwh.agg_hourly_outlet_item_inventory as a\n", "        JOIN retail.console_outlet co ON co.id = a.outlet_id AND business_type_id = 7 and lake_active_record\n", "        LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id\n", "        where snapshot_date_ist >= DATE('{today_date}') - interval '30' day\n", "        AND  snapshot_date_ist < DATE('{today_date}')\n", "        and co.facility_id in {facility_id_list}\n", "        and item_id in {item_id_list}\n", "        and snapshot_hr_mm%%100=0\n", "        group by 1,2,3,4,5), \n", "    \n", "    avail_pre AS (\n", "        SELECT date_, hour_, facility_id,outlet_id, item_id, MAX(is_available) AS is_available\n", "        FROM (\n", "            SELECT date_, hour_, facility_id, outlet_id, item_id, \n", "            CASE \n", "                WHEN current_inventory > 0 THEN 1 \n", "                ELSE 0  \n", "            END is_available\n", "            FROM base\n", "        )\n", "        WHERE hour_ BETWEEN 6 AND 23\n", "        GROUP BY 1,2,3,4,5\n", "    )\n", "    \n", "    SELECT * FROM avail_pre  \n", "    \n", "    \"\"\"\n", "assortment_base_pre_df = read_sql_query(base_query, trino)\n", "\n", "assortment_base_pre_df[\"dow\"] = pd.to_datetime(assortment_base_pre_df[\"date_\"]).dt.dayofweek\n", "\n", "assortment_base_pre_df.shape"]}, {"cell_type": "markdown", "id": "990a52c3-c03b-445f-b76d-9a17cccbebd8", "metadata": {}, "source": ["### Assortment correction"]}, {"cell_type": "code", "execution_count": null, "id": "c4710be7-307b-4e89-a3ea-35c3635c24c9", "metadata": {}, "outputs": [], "source": ["assortment_df = pd.merge(\n", "    base_df,\n", "    assortment_base_pre_df,\n", "    on=[\"item_id\", \"facility_id\", \"outlet_id\"],\n", "    how=\"inner\",\n", ")\n", "\n", "assortment_df[\"date_\"] = pd.to_datetime(assortment_df[\"date_\"])\n", "\n", "assortment_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "2cf6e551-95fc-4d1f-ac35-69c66c98ea36", "metadata": {}, "outputs": [], "source": ["del [base_df]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "e988aaeb-8ad7-44af-8812-4490f4453345", "metadata": {}, "source": ["## Hourly Sales"]}, {"cell_type": "code", "execution_count": null, "id": "8b5fdb16-789d-4edd-a202-070f12595e39", "metadata": {}, "outputs": [], "source": ["sale_query = f\"\"\"\n", "    WITH item_mapping as (\n", "        SELECT DISTINCT ipr.product_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NULL THEN ipom_0.item_id \n", "            ELSE ipr.item_id \n", "        END AS item_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1) \n", "            ELSE COALESCE(ipom_0.multiplier,1) \n", "        END AS multiplier\n", "        FROM rpc.item_product_mapping ipr \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", "        WHERE ipr.lake_active_record\n", "    ),\n", "\n", "    sales AS (\n", "        SELECT (oid.cart_checkout_ts_ist) AS order_date, cl.name AS city, rco.facility_id, oid.product_id, im.item_id, oid.order_id, im.multiplier, \n", "        ((oid.procured_quantity - oid.total_doorstep_return_quantity) * im.multiplier) AS sales_quantity \n", "        FROM dwh.fact_sales_order_item_details oid \n", "        JOIN item_mapping im on im.product_id = oid.product_id  \n", "        JOIN retail.console_outlet rco ON rco.id = oid.outlet_id AND business_type_id = 7  AND rco.lake_active_record\n", "        LEFT JOIN retail.console_location cl ON cl.id = rco.tax_location_id AND cl.lake_active_record\n", "        WHERE oid.order_create_dt_ist >= DATE('{today_date}') - interval '30' day AND oid.order_create_dt_ist < DATE('{today_date}') - interval '0' day\n", "        AND oid.is_internal_order = false  \n", "        AND (oid.order_type NOT LIKE '%%internal%%' OR oid.order_type IS NULL)  \n", "        AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED'\n", "        AND oid.outlet_id IN {outlet_id_list}\n", "    ),\n", "    \n", "    final_sales AS (\n", "        SELECT DATE(order_date) AS date_, EXTRACT(hour FROM order_date) AS hour_, city, facility_id, s.item_id, CAST(SUM(sales_quantity) AS int) AS quantity\n", "        FROM sales s\n", "        WHERE item_id IN {item_id_list}\n", "        GROUP BY 1,2,3,4,5\n", "    )\n", "    \n", "    SELECT DISTINCT city, facility_id, item_id, hour_, date_, quantity \n", "    FROM final_sales\n", "    WHERE hour_ BETWEEN 6 AND 23\n", "    \"\"\"\n", "sales_pre_df = read_sql_query(sale_query, trino)\n", "sales_pre_df[\"date_\"] = pd.to_datetime(sales_pre_df[\"date_\"])\n", "\n", "sales_pre_df.shape"]}, {"cell_type": "markdown", "id": "ab802d27-da5e-4acc-8e9b-0c4f5fc86a37", "metadata": {}, "source": ["### Daily Carts & Cart Penetration - Store Definition & Disruption"]}, {"cell_type": "code", "execution_count": null, "id": "2058ed8f-7cda-44b6-90c9-954363ee6b60", "metadata": {}, "outputs": [], "source": ["carts_query = f\"\"\"\n", "    WITH item_mapping AS (\n", "        SELECT DISTINCT ipr.product_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NULL THEN ipom_0.item_id \n", "            ELSE ipr.item_id \n", "        END AS item_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1) \n", "            ELSE COALESCE(ipom_0.multiplier,1) \n", "        END AS multiplier\n", "        FROM rpc.item_product_mapping ipr \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", "        WHERE ipr.lake_active_record\n", "    ),\n", "\n", "    sales AS (\n", "        SELECT (oid.cart_checkout_ts_ist) AS order_date, oid.outlet_id, oid.product_id, im.item_id, oid.order_id, im.multiplier\n", "        FROM dwh.fact_sales_order_item_details oid \n", "        JOIN item_mapping im on im.product_id = oid.product_id  \n", "        WHERE oid.order_create_dt_ist >= DATE('{today_date}') - interval '30' day AND oid.order_create_dt_ist < DATE('{today_date}') - interval '0' day\n", "        AND oid.is_internal_order = false  \n", "        AND (oid.order_type NOT LIKE '%%internal%%' OR oid.order_type IS NULL)  \n", "        AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED'\n", "    ),\n", "    \n", "    all_carts AS (\n", "        SELECT DATE(order_date) AS date_, EXTRACT(hour FROM order_date) AS hour_, outlet_id, COUNT(DISTINCT order_id) AS fac_carts\n", "        FROM sales\n", "        GROUP BY 1,2,3\n", "    ),\n", "    \n", "    category_carts AS (\n", "        SELECT DATE(order_date) AS date_, EXTRACT(hour FROM order_date) AS hour_, outlet_id, COUNT(DISTINCT order_id) AS category_carts\n", "        FROM sales\n", "        WHERE item_id IN {item_id_list}\n", "        GROUP BY 1,2,3\n", "    )\n", "    \n", "    SELECT a.outlet_id, a.date_, a.hour_, fac_carts, CASE WHEN category_carts IS NULL THEN 0 ELSE category_carts END AS category_carts\n", "    FROM all_carts a\n", "    LEFT JOIN category_carts b ON a.outlet_id = b.outlet_id AND a.date_ = b.date_ AND a.hour_ = b.hour_\n", "    WHERE a.hour_ BETWEEN 6 AND 23\n", "    \"\"\"\n", "carts_df = read_sql_query(carts_query, trino)\n", "\n", "carts_df[\"date_\"] = pd.to_datetime(carts_df[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "8dffb540-30c6-4128-8b10-dbfbe80a521c", "metadata": {}, "outputs": [], "source": ["carts_agg_df = (\n", "    carts_df.groupby([\"date_\", \"outlet_id\"])\n", "    .agg({\"fac_carts\": \"sum\", \"category_carts\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "l15_date = (current_time - timedelta(days=15)).strftime(\"%Y-%m-%d\")\n", "\n", "x = pd.date_range(\n", "    start=(current_time - timed<PERSON>ta(days=15)).strftime(\"%Y-%m-%d\"),\n", "    end=(pd.to_datetime(today_date)).strftime(\"%Y-%m-%d\"),\n", ")\n", "\n", "date_df = pd.DataFrame({\"date_\": x, \"flag\": 1})\n", "\n", "store_base_df = pd.DataFrame(outlet_id_list, columns=[\"outlet_id\"])\n", "store_base_df[\"flag\"] = 1\n", "\n", "carts_base_df = pd.merge(store_base_df, date_df, on=[\"flag\"], how=\"left\").drop(columns={\"flag\"})\n", "\n", "carts_base_df = pd.merge(carts_base_df, carts_agg_df, on=[\"outlet_id\", \"date_\"], how=\"left\")\n", "\n", "carts_base_df[\"cp\"] = carts_base_df[\"category_carts\"] / carts_base_df[\"fac_carts\"]\n", "\n", "carts_base_df[[\"fac_carts\", \"category_carts\", \"cp\"]] = carts_base_df[\n", "    [\"fac_carts\", \"category_carts\", \"cp\"]\n", "].fillna(0)\n", "\n", "carts_base_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "a993603d-6476-48cc-85af-ba682479920a", "metadata": {}, "outputs": [], "source": ["def percentile(x):\n", "    return np.ceil(np.percentile(x, 75)).astype(int)\n", "\n", "\n", "def cp_percentile(x):\n", "    return np.percentile(x, 75)"]}, {"cell_type": "markdown", "id": "d12d04bd-5e83-4ab5-a90a-4ae83005aded", "metadata": {}, "source": ["### Disruption Days"]}, {"cell_type": "markdown", "id": "b7dd1f3a-666c-4c24-9c18-179b08a817c0", "metadata": {}, "source": ["#### Store Disruption"]}, {"cell_type": "code", "execution_count": null, "id": "1465dfb6-f295-424c-8140-5f9af6aa34d5", "metadata": {}, "outputs": [], "source": ["opd_df = (\n", "    carts_base_df[(carts_base_df[\"fac_carts\"] != 0)]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "opd_df = opd_df.groupby([\"outlet_id\"]).agg({\"fac_carts\": percentile}).reset_index()\n", "\n", "opd_df = opd_df.rename(columns={\"fac_carts\": \"opd\"})\n", "\n", "opd_df[\"ftype_\"] = np.where(\n", "    opd_df[\"opd\"] < 800, \"low\", np.where(opd_df[\"opd\"] >= 1200, \"high\", \"medium\")\n", ")\n", "\n", "opd_df.shape"]}, {"cell_type": "markdown", "id": "037f7c01-418a-4c32-a8db-b439e5f205f4", "metadata": {}, "source": ["#### Category Carts Disruption"]}, {"cell_type": "code", "execution_count": null, "id": "a5dcece9-9e8d-4320-a2f9-dd8bfc784284", "metadata": {}, "outputs": [], "source": ["category_cp_df = (\n", "    carts_base_df[carts_base_df[\"cp\"] != 0].drop_duplicates().reset_index().drop(columns={\"index\"})\n", ")\n", "\n", "category_cp_df = category_cp_df.groupby([\"outlet_id\"]).agg({\"cp\": cp_percentile}).reset_index()\n", "\n", "category_cp_df = category_cp_df.rename(columns={\"cp\": \"category_cp\"})\n", "\n", "category_cp_df[\"cp_bucket\"] = np.where(\n", "    category_cp_df[\"category_cp\"] < np.quantile(category_cp_df[\"category_cp\"], 0.25),\n", "    \"low\",\n", "    np.where(\n", "        category_cp_df[\"category_cp\"] > np.quantile(category_cp_df[\"category_cp\"], 0.75),\n", "        \"high\",\n", "        \"medium\",\n", "    ),\n", ")\n", "\n", "category_cp_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "1b3658b4-d8e6-498e-9bce-bd4e411355be", "metadata": {}, "outputs": [], "source": ["disruption_base_df = pd.merge(carts_base_df, opd_df, on=[\"outlet_id\"], how=\"left\")\n", "\n", "disruption_base_df = pd.merge(disruption_base_df, category_cp_df, on=[\"outlet_id\"], how=\"left\")\n", "\n", "disruption_base_df[[\"cp_bucket\", \"ftype_\"]] = disruption_base_df[[\"cp_bucket\", \"ftype_\"]].fillna(\n", "    \"low\"\n", ")\n", "\n", "disruption_base_df[\"opd_deviation\"] = (\n", "    disruption_base_df[\"fac_carts\"] / disruption_base_df[\"opd\"] - 1\n", ")\n", "\n", "disruption_base_df[\"cp_deviation\"] = (\n", "    disruption_base_df[\"cp\"] / disruption_base_df[\"category_cp\"] - 1\n", ")\n", "\n", "disruption_base_df[\"opd_flag\"] = np.where(\n", "    (disruption_base_df[\"ftype_\"].isin({\"high\", \"medium\"}))\n", "    & (disruption_base_df[\"opd_deviation\"] <= -0.25),\n", "    1,\n", "    0,\n", ")\n", "\n", "disruption_base_df[\"opd_flag\"] = np.where(\n", "    (disruption_base_df[\"ftype_\"].isin({\"high\", \"medium\"}))\n", "    & (disruption_base_df[\"opd_deviation\"] >= 0.3),\n", "    1,\n", "    disruption_base_df[\"opd_flag\"],\n", ")\n", "\n", "disruption_base_df[\"cp_flag\"] = np.where(\n", "    (disruption_base_df[\"ftype_\"].isin({\"high\", \"medium\"}))\n", "    & (disruption_base_df[\"cp_deviation\"] <= -0.4),\n", "    1,\n", "    0,\n", ")\n", "\n", "disruption_base_df[\"d_flag\"] = disruption_base_df[\"opd_flag\"] + disruption_base_df[\"cp_flag\"]\n", "\n", "disruption_base_df[\"d_flag\"] = np.where(\n", "    disruption_base_df[\"outlet_id\"].isin(buffer_store_outlet_list), 0, disruption_base_df[\"d_flag\"]\n", ")\n", "\n", "\n", "disruption_df = (\n", "    disruption_base_df[disruption_base_df[\"d_flag\"] > 0][[\"outlet_id\", \"date_\", \"d_flag\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")"]}, {"cell_type": "markdown", "id": "38737b52-eb70-4323-9640-dc02bc49d7b1", "metadata": {}, "source": ["#### Store Type Definition"]}, {"cell_type": "code", "execution_count": null, "id": "4bb8905e-0e07-466b-8648-a0d97fbbeb9e", "metadata": {}, "outputs": [], "source": ["store_type_df = (\n", "    disruption_base_df[[\"outlet_id\", \"cp_bucket\", \"ftype_\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "store_type_df[\"ftype_\"] = np.where(\n", "    (store_type_df[\"ftype_\"] == \"medium\") & (store_type_df[\"cp_bucket\"].isin({\"low\"})),\n", "    \"low\",\n", "    store_type_df[\"ftype_\"],\n", ")\n", "\n", "store_type_df[\"ftype_\"] = np.where(\n", "    (store_type_df[\"ftype_\"] == \"high\") & (store_type_df[\"cp_bucket\"].isin({\"medium\", \"low\"})),\n", "    \"medium\",\n", "    store_type_df[\"ftype_\"],\n", ")\n", "\n", "store_type_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "144dfd01-b155-4c6e-ae8e-0b297c3d6ed1", "metadata": {}, "outputs": [], "source": ["del [opd_df, category_cp_df]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "61acb383-6448-4d4e-8369-d54167101896", "metadata": {}, "source": ["### Bucketing - Item Ranking"]}, {"cell_type": "code", "execution_count": null, "id": "e4a51b48-f0e1-4ecf-8b50-50667789a17a", "metadata": {}, "outputs": [], "source": ["sku_rank_df = pd.merge(\n", "    assortment_df,\n", "    sales_pre_df,\n", "    on=[\"city\", \"facility_id\", \"item_id\", \"date_\", \"hour_\"],\n", "    how=\"left\",\n", ")\n", "\n", "sku_rank_df = (\n", "    sku_rank_df.groupby([\"be_facility_id\", \"item_id\", \"date_\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "sku_rank_df = (\n", "    sku_rank_df.groupby([\"be_facility_id\", \"item_id\"]).agg({\"quantity\": \"mean\"}).reset_index()\n", ")\n", "\n", "sku_rank_df[\"quantity\"] = sku_rank_df[\"quantity\"].astype(int)\n", "\n", "sku_rank_df = sku_rank_df.sort_values(by=\"quantity\", ascending=True)\n", "\n", "sku_rank_df[\"cum_quantity\"] = sku_rank_df.groupby([\"be_facility_id\"])[\"quantity\"].cumsum()\n", "\n", "sku_agg_df = (\n", "    sku_rank_df.groupby([\"be_facility_id\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"quantity\": \"tot_quantity\"})\n", ")\n", "\n", "sku_rank_df = sku_rank_df.merge(sku_agg_df, on=[\"be_facility_id\"], how=\"left\")\n", "\n", "sku_rank_df[\"perc_contri\"] = sku_rank_df[\"cum_quantity\"] / sku_rank_df[\"tot_quantity\"]\n", "\n", "## 70% sales contributing.. (If consistantly lower fills then distribution changes)\n", "sku_rank_df[\"stype_\"] = np.where(sku_rank_df[\"perc_contri\"] > 0.4, \"top\", \"bottom\")\n", "\n", "sku_rank_df = sku_rank_df[[\"be_facility_id\", \"item_id\", \"stype_\"]].drop_duplicates()\n", "\n", "sku_rank_df.head(1)"]}, {"cell_type": "markdown", "id": "628723e3-b6b2-4256-9dc2-6a29d3194ec9", "metadata": {}, "source": ["### Merge with Assortment "]}, {"cell_type": "code", "execution_count": null, "id": "4f06016a-fa92-484a-8419-008e18f1677f", "metadata": {}, "outputs": [], "source": ["assortment_filter_df = pd.merge(\n", "    assortment_df, store_type_df[[\"outlet_id\", \"ftype_\"]], on=[\"outlet_id\"], how=\"left\"\n", ")\n", "\n", "assortment_filter_df = assortment_filter_df.merge(\n", "    sku_rank_df, on=[\"be_facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "\n", "assortment_filter_df[\"date_\"] = pd.to_datetime(assortment_filter_df[\"date_\"])\n", "\n", "assortment_filter_df[\"ftype_\"] = assortment_filter_df[\"ftype_\"].fillna(\"low\")\n", "assortment_filter_df[\"stype_\"] = assortment_filter_df[\"stype_\"].fillna(\"bottom\")\n", "\n", "assortment_filter_df.shape"]}, {"cell_type": "markdown", "id": "491b0b4b-9f7b-490f-9090-8f76ca344f29", "metadata": {}, "source": ["### L2 Disruption Treatment"]}, {"cell_type": "code", "execution_count": null, "id": "78c442dd-9e3c-4754-ab94-bb9a46b0e9ea", "metadata": {}, "outputs": [], "source": ["assortment_filter_df = pd.merge(\n", "    assortment_filter_df, city_l2_disruption_df, on=[\"city\", \"l2\", \"date_\"], how=\"left\"\n", ")\n", "\n", "assortment_filter_df[\"flag\"] = assortment_filter_df[\"flag\"].fillna(0)\n", "\n", "assortment_filter_df = assortment_filter_df[assortment_filter_df[\"flag\"] == 0]\n", "\n", "assortment_filter_df = assortment_filter_df.drop(columns={\"l2\"})\n", "\n", "assortment_filter_df.shape"]}, {"cell_type": "markdown", "id": "dce1fc81-e0d3-4442-8ffb-fc9402cd4da8", "metadata": {}, "source": ["### Hour Weights - Search + Carts Logic"]}, {"cell_type": "markdown", "id": "e941850b-7b02-4646-aa8e-b110f73e72ca", "metadata": {}, "source": ["#### Carts based Weights"]}, {"cell_type": "code", "execution_count": null, "id": "d4b41369-8cfe-4ab4-a925-ef3b7db4d162", "metadata": {}, "outputs": [], "source": ["hourly_carts_query = f\"\"\"\n", "    WITH item_mapping AS (\n", "        SELECT DISTINCT ipr.product_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NULL THEN ipom_0.item_id \n", "            ELSE ipr.item_id \n", "        END AS item_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1) \n", "            ELSE COALESCE(ipom_0.multiplier,1) \n", "        END AS multiplier\n", "        FROM rpc.item_product_mapping ipr \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", "        WHERE ipr.lake_active_record\n", "    ),\n", "\n", "    sales AS (\n", "        SELECT (oid.cart_checkout_ts_ist) AS order_date, oid.outlet_id, oid.product_id, im.item_id, oid.order_id, im.multiplier, \n", "        oid.total_doorstep_return_quantity, ((oid.procured_quantity - oid.total_doorstep_return_quantity) * im.multiplier) AS sales_quantity\n", "        FROM dwh.fact_sales_order_item_details oid\n", "        JOIN item_mapping im on im.product_id = oid.product_id \n", "        WHERE oid.order_create_dt_ist >= DATE('{today_date}') - interval '15' day AND oid.order_create_dt_ist < DATE('{today_date}') - interval '0' day\n", "        AND oid.is_internal_order = false \n", "        AND (oid.order_type NOT LIKE '%%internal%%' OR oid.order_type IS NULL) \n", "        AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED' AND outlet_id IN {outlet_id_list}\n", "    ),\n", "\n", "    pre_summary AS (\n", "        SELECT outlet_id, hour_, item_id, AVG(cat_carts) AS cat_carts\n", "        FROM (\n", "            SELECT DATE(order_date) AS date_, EXTRACT(hour FROM order_date) AS hour_, outlet_id, item_id, COUNT(DISTINCT order_id) AS cat_carts\n", "            FROM sales\n", "            WHERE item_id IN {item_id_list}\n", "            GROUP BY 1,2,3,4\n", "        )\n", "        GROUP BY 1,2,3\n", "    ),\n", "    \n", "    summary AS (\n", "        SELECT a.*, cl.name AS city \n", "        FROM pre_summary a\n", "        JOIN retail.console_outlet co ON co.id = a.outlet_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "        JOIN retail.console_location cl ON cl.id = co.tax_location_id AND cl.lake_active_record\n", "        WHERE hour_ BETWEEN 6 AND 23\n", "    )\n", "    \n", "    SELECT city, item_id, hour_, SUM(cat_carts) AS cat_carts\n", "    FROM summary\n", "    GROUP BY 1,2,3\n", "    \"\"\"\n", "carts_hourly_df = read_sql_query(hourly_carts_query, trino)\n", "\n", "carts_agg_df = (\n", "    carts_hourly_df.groupby([\"city\", \"item_id\"])\n", "    .agg({\"cat_carts\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"cat_carts\": \"tot_cat_carts\"})\n", ")\n", "\n", "carts_hourly_df = carts_hourly_df.merge(carts_agg_df, on=[\"city\", \"item_id\"], how=\"left\")\n", "\n", "carts_hourly_df[\"chw\"] = carts_hourly_df[\"cat_carts\"] / carts_hourly_df[\"tot_cat_carts\"]\n", "\n", "carts_hourly_df = carts_hourly_df[[\"city\", \"item_id\", \"hour_\", \"chw\"]]\n", "\n", "carts_hourly_df.shape"]}, {"cell_type": "markdown", "id": "3327df54-e3be-482e-a239-be2d5ef44d88", "metadata": {}, "source": ["#### Search based Weights"]}, {"cell_type": "code", "execution_count": null, "id": "c7cacee9-d1b3-4fd3-81f2-7c76da13a85e", "metadata": {}, "outputs": [], "source": ["search_hourly_query = f\"\"\"\n", "    WITH city_item_hour_search_wt AS (\n", "        WITH psuedo_base AS (\n", "            SELECT * FROM supply_etls.merchant_item_search_weights\n", "            WHERE at_date_ist BETWEEN DATE('{today_date}') - interval '7' day AND DATE('{today_date}') - interval '1' day\n", "        ),\n", "\n", "        search_base AS (\n", "            SELECT b.* from psuedo_base b\n", "            JOIN rpc.item_category_details icd ON icd.item_id = b.item_id AND icd.l2_id IN {l2_id_list} AND icd.lake_active_record\n", "        ),\n", "\n", "        hour_item_city_search AS (\n", "            SELECT hour_of_the_day AS hour_, city_name AS city, item_id, SUM(searches_total) AS hour_searches\n", "            FROM search_base \n", "            GROUP BY 1,2,3\n", "        ),\n", "\n", "        item_city_search AS (\n", "            SELECT item_id, city_name AS city, SUM(searches_total) AS item_searches\n", "            FROM search_base\n", "            GROUP BY 1,2\n", "        )\n", "\n", "        SELECT hic.city, hic.item_id, hic.hour_, hour_searches * 1.00000 / NULLIF(item_searches,0) AS cshw\n", "        FROM hour_item_city_search hic\n", "        LEFT JOIN item_city_search ic ON hic.item_id = ic.item_id AND hic.city = ic.city\n", "    )\n", "\n", "    SELECT * FROM city_item_hour_search_wt\n", "    ORDER BY hour_\n", "    \n", "    \"\"\"\n", "search_hourly_df = read_sql_query(search_hourly_query, trino)\n", "\n", "search_hourly_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "3eb84903-bb29-41f7-b7d7-03203035a084", "metadata": {}, "outputs": [], "source": ["hour_weights_df = (\n", "    assortment_df[[\"city\", \"item_id\", \"hour_\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "hour_weights_df = hour_weights_df.merge(\n", "    search_hourly_df, on=[\"city\", \"item_id\", \"hour_\"], how=\"left\"\n", ")\n", "\n", "hour_weights_df = hour_weights_df.merge(\n", "    carts_hourly_df, on=[\"city\", \"item_id\", \"hour_\"], how=\"left\"\n", ")\n", "\n", "hour_weights_df[[\"cshw\", \"chw\"]] = hour_weights_df[[\"cshw\", \"chw\"]].fillna(0)\n", "\n", "hour_weights_df[\"hw\"] = (hour_weights_df[\"cshw\"] * 0.75) + (hour_weights_df[\"chw\"] * 0.25)\n", "\n", "hour_weights_agg_df = (\n", "    hour_weights_df.groupby([\"city\", \"item_id\"])\n", "    .agg({\"hw\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"hw\": \"tot_hw\"})\n", ")\n", "\n", "hour_weights_df = hour_weights_df.merge(hour_weights_agg_df, on=[\"city\", \"item_id\"], how=\"left\")\n", "\n", "hour_weights_df[\"new_hw\"] = hour_weights_df[\"hw\"] / hour_weights_df[\"tot_hw\"]\n", "\n", "hour_weights_df = (\n", "    hour_weights_df[[\"city\", \"item_id\", \"hour_\", \"new_hw\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "del [search_hourly_df, carts_hourly_df, carts_agg_df, hour_weights_agg_df]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "6dcc3db4-5a2e-46a7-96ab-c1df2ea18896", "metadata": {}, "source": ["# Daywise Extrapolation"]}, {"cell_type": "markdown", "id": "c3db4605-ec0f-4a99-b048-77ce60df3fb2", "metadata": {}, "source": ["### Merge with DataFrame"]}, {"cell_type": "code", "execution_count": null, "id": "d32e4246-f6c9-4781-8c4d-b34e9b95c7c6", "metadata": {}, "outputs": [], "source": ["assortment_final_df = pd.merge(\n", "    assortment_filter_df, disruption_df, on=[\"outlet_id\", \"date_\"], how=\"left\"\n", ")\n", "\n", "assortment_final_df[\"d_flag\"] = np.where(\n", "    assortment_final_df[\"d_flag\"].isna(), 0, assortment_final_df[\"d_flag\"]\n", ")\n", "\n", "assortment_final_df = (\n", "    assortment_final_df[assortment_final_df[\"d_flag\"] == 0]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\", \"d_flag\"})\n", ")\n", "\n", "assortment_final_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "044dc237-947a-4160-828d-e15a508a0c5e", "metadata": {}, "outputs": [], "source": ["del assortment_filter_df\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "680cf7ec-1176-4a82-b40b-56ba457737ed", "metadata": {}, "source": ["### Ranking & Sequencing"]}, {"cell_type": "code", "execution_count": null, "id": "945a4375-0878-4dcb-8a14-460675ac86a5", "metadata": {}, "outputs": [], "source": ["ranking_df = assortment_final_df[assortment_final_df[\"date_\"] < today_date][\n", "    [\"item_id\", \"facility_id\", \"date_\"]\n", "].drop_duplicates()\n", "\n", "ranking_df[\"rank\"] = ranking_df.groupby([\"item_id\", \"facility_id\"])[\"date_\"].rank(\n", "    method=\"dense\", ascending=False\n", ")\n", "\n", "ranking_df = ranking_df[ranking_df[\"rank\"] < 31]\n", "\n", "ranking_df = ranking_df[[\"facility_id\", \"item_id\", \"date_\", \"rank\"]].drop_duplicates()"]}, {"cell_type": "markdown", "id": "c6505026-c197-4b80-8f41-b3a4a7b38e67", "metadata": {}, "source": ["### Final Assortment"]}, {"cell_type": "code", "execution_count": null, "id": "328c2234-0998-4569-ba7b-1d3b8fc5dd1e", "metadata": {}, "outputs": [], "source": ["assortment_base_df = pd.merge(\n", "    assortment_final_df, ranking_df, on=[\"item_id\", \"facility_id\", \"date_\"], how=\"inner\"\n", ")\n", "\n", "assortment_base_df[\"flag\"] = np.where(assortment_base_df[\"hour_\"] < 23, 1, 0)\n", "\n", "assortment_base_df = assortment_base_df[assortment_base_df[\"flag\"] == 1].drop(columns={\"flag\"})\n", "\n", "assortment_base_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "b330c10c-cbaf-4f92-be80-610a12a73af6", "metadata": {}, "outputs": [], "source": ["del assortment_final_df\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "9ecf65b6-6308-4ae1-b89c-d7809c47edd1", "metadata": {}, "source": ["### Assortment & Sales Merge"]}, {"cell_type": "code", "execution_count": null, "id": "9919c030-e3d3-446d-af6e-464c5da953e1", "metadata": {}, "outputs": [], "source": ["assortment_sales_df = pd.merge(\n", "    assortment_base_df,\n", "    sales_pre_df,\n", "    on=[\"item_id\", \"facility_id\", \"hour_\", \"date_\", \"city\"],\n", "    how=\"left\",\n", ")\n", "\n", "assortment_sales_df[\"is_available\"] = np.where(\n", "    assortment_sales_df[\"quantity\"] > 0, 1, assortment_sales_df[\"is_available\"]\n", ")\n", "\n", "assortment_sales_df[\"quantity\"] = assortment_sales_df[\"quantity\"].fillna(0)\n", "\n", "assortment_sales_df = assortment_sales_df.merge(\n", "    hour_weights_df, on=[\"city\", \"item_id\", \"hour_\"], how=\"left\"\n", ")\n", "\n", "assortment_sales_df[\"hw\"] = np.where(\n", "    assortment_sales_df[\"new_hw\"].isna(), 0, assortment_sales_df[\"new_hw\"]\n", ")\n", "\n", "assortment_sales_df[\"wt_score\"] = assortment_sales_df[\"is_available\"] * assortment_sales_df[\"hw\"]"]}, {"cell_type": "code", "execution_count": null, "id": "79621a66-3ced-44fa-8f68-14aed2b8105e", "metadata": {}, "outputs": [], "source": ["del assortment_base_df\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "7157c2ff-c191-47c9-b065-ed6f2deb2d64", "metadata": {}, "source": ["### Daywise Extrapolation Aggregation"]}, {"cell_type": "code", "execution_count": null, "id": "adb57bed-c219-47dc-b824-7850de6eeac3", "metadata": {}, "outputs": [], "source": ["daywise_df = (\n", "    assortment_sales_df.groupby(\n", "        [\n", "            \"city\",\n", "            \"outlet_id\",\n", "            \"facility_id\",\n", "            \"ptype\",\n", "            \"item_id\",\n", "            \"be_facility_id\",\n", "            \"be_outlet_id\",\n", "            \"be_outlet_name\",\n", "            \"date_\",\n", "            \"rank\",\n", "            \"stype_\",\n", "            \"ftype_\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"is_available\": \"sum\",\n", "            \"hour_\": \"nunique\",\n", "            \"hw\": \"sum\",\n", "            \"wt_score\": \"sum\",\n", "            \"quantity\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "daywise_df[\"avail\"] = daywise_df[\"is_available\"] / daywise_df[\"hour_\"]\n", "daywise_df[\"wt_avail\"] = daywise_df[\"wt_score\"] / daywise_df[\"hw\"]\n", "\n", "daywise_df[\"wt_avail\"] = daywise_df[\"wt_avail\"].fillna(0)\n", "\n", "daywise_df[\"wt_avail\"] = np.where(\n", "    (daywise_df[\"avail\"] > 0) & (daywise_df[\"wt_avail\"] == 0), 1, daywise_df[\"wt_avail\"]\n", ")\n", "\n", "daywise_df[\"ext_qty_li\"] = daywise_df[\"quantity\"] * (1 + 0.35 * (1 - daywise_df[\"wt_avail\"]))\n", "\n", "daywise_df[\"ext_qty_exp\"] = daywise_df[\"quantity\"] * (5 ** (0.50 * (1 - daywise_df[\"wt_avail\"])))\n", "\n", "daywise_df[\"ext_qty_para\"] = daywise_df[\"quantity\"] * (\n", "    1 + ((1 - daywise_df[\"wt_avail\"]) ** 2) / (4 * 0.08)\n", ")\n", "\n", "daywise_df[\"high_ext_qty\"] = np.where(\n", "    (daywise_df[\"ftype_\"] == \"high\"),\n", "    np.where(\n", "        (daywise_df[\"stype_\"] == \"bottom\"),\n", "        daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "        daywise_df[[\"ext_qty_para\", \"ext_qty_exp\"]].mean(axis=1),\n", "    ),\n", "    0,\n", ")\n", "\n", "daywise_df[\"medium_ext_qty\"] = np.where(\n", "    (daywise_df[\"ftype_\"] == \"medium\"),\n", "    np.where(\n", "        (daywise_df[\"stype_\"] == \"bottom\"),\n", "        daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "        np.where(\n", "            daywise_df[\"wt_avail\"] >= 0.7,\n", "            daywise_df[[\"ext_qty_li\", \"ext_qty_para\", \"ext_qty_exp\"]].mean(axis=1),\n", "            daywise_df[[\"ext_qty_exp\", \"ext_qty_para\"]].mean(axis=1),\n", "        ),\n", "    ),\n", "    0,\n", ")\n", "\n", "daywise_df[\"low_ext_qty\"] = np.where(\n", "    (daywise_df[\"ftype_\"] == \"low\"),\n", "    np.where(\n", "        (daywise_df[\"stype_\"] == \"bottom\"),\n", "        daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "        np.where(\n", "            daywise_df[\"wt_avail\"] >= 0.7,\n", "            daywise_df[[\"ext_qty_li\", \"ext_qty_para\", \"ext_qty_exp\"]].mean(axis=1),\n", "            daywise_df[[\"ext_qty_exp\", \"ext_qty_para\"]].mean(axis=1),\n", "        ),\n", "    ),\n", "    0,\n", ")\n", "\n", "daywise_df[\"ext_qty_hybrid\"] = (\n", "    daywise_df[\"high_ext_qty\"] + daywise_df[\"medium_ext_qty\"] + daywise_df[\"low_ext_qty\"]\n", ")\n", "\n", "daywise_df = daywise_df.drop(columns={\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"})\n", "\n", "daywise_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "7c08d4f6-19c9-4b7a-a127-9e0dd246177b", "metadata": {}, "outputs": [], "source": ["daywise_df.head(1)"]}, {"cell_type": "markdown", "id": "035c6ac0-7ecb-4d3d-8c2d-1474e1ce0eae", "metadata": {}, "source": ["### Weekday Weekend Normalisation"]}, {"cell_type": "code", "execution_count": null, "id": "d8e7881f-d65b-4af4-9d74-3fe42f0413f7", "metadata": {}, "outputs": [], "source": ["increment_df = (\n", "    daywise_df.groupby(\n", "        [\n", "            \"city\",\n", "            \"date_\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"ext_qty_hybrid\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    .rename(columns={\"ext_qty_hybrid\": \"carts\"})\n", ")\n", "\n", "\n", "increment_df[\"date_\"] = pd.to_datetime(increment_df[\"date_\"])\n", "\n", "increment_df[\"dow\"] = pd.to_datetime(increment_df[\"date_\"]).dt.dayofweek"]}, {"cell_type": "code", "execution_count": null, "id": "72f2fedf-57d6-4363-a810-7d193b15514f", "metadata": {}, "outputs": [], "source": ["carts_increment_agg_df = increment_df.groupby([\"city\", \"dow\"]).agg({\"carts\": \"mean\"}).reset_index()\n", "\n", "carts_increment_agg_df[\"carts\"] = np.round(carts_increment_agg_df[\"carts\"], 0)\n", "carts_increment_agg_df[\"carts\"] = (carts_increment_agg_df[\"carts\"]).astype(int)\n", "\n", "new_carts_df = carts_increment_agg_df.copy()\n", "new_carts_df = new_carts_df.rename(columns={\"carts\": \"new_carts\"})\n", "new_carts_df = new_carts_df[[\"city\", \"new_carts\"]].reset_index().drop(columns={\"index\"})\n", "\n", "final_carts_increment_df = pd.merge(carts_increment_agg_df, new_carts_df, on=[\"city\"], how=\"left\")\n", "\n", "carts_increment_agg_df = carts_increment_agg_df.rename(\n", "    columns={\"dow\": \"fdow\", \"carts\": \"new_carts\"}\n", ")\n", "\n", "final_carts_increment_df = pd.merge(\n", "    final_carts_increment_df,\n", "    carts_increment_agg_df,\n", "    on=[\"city\", \"new_carts\"],\n", "    how=\"left\",\n", ")\n", "\n", "final_carts_increment_df[\"bf\"] = 1 + (\n", "    (final_carts_increment_df[\"new_carts\"] / final_carts_increment_df[\"carts\"]) - 1\n", ")\n", "\n", "final_increment_df = (\n", "    final_carts_increment_df[[\"city\", \"dow\", \"fdow\", \"bf\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "final_increment_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "69073137-150d-4e6f-a8d8-7171dddbdd8e", "metadata": {}, "outputs": [], "source": ["del (increment_df, carts_increment_agg_df, final_carts_increment_df)\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "82d405d8-5687-474c-92fd-96e8a3e9e5db", "metadata": {}, "source": ["# Looking Forward Forecast"]}, {"cell_type": "code", "execution_count": null, "id": "09686638-7190-4e9c-a5ab-e2ec37815e83", "metadata": {}, "outputs": [], "source": ["forecast_calc_df = daywise_df.copy()\n", "\n", "forecast_calc_df[\"flag\"] = 1"]}, {"cell_type": "markdown", "id": "b75b772f-67c5-4c79-873d-9428f750c725", "metadata": {}, "source": ["### Looking Forward Dates"]}, {"cell_type": "code", "execution_count": null, "id": "93698948-973d-43ad-9a9d-8adb974c6c85", "metadata": {}, "outputs": [], "source": ["x = pd.date_range(\n", "    start=(pd.to_datetime(today_date) + timedelta(days=1)).strftime(\"%Y-%m-%d\"),\n", "    end=(pd.to_datetime(today_date) + timedelta(days=7)).strftime(\"%Y-%m-%d\"),\n", ")\n", "\n", "looking_forward_df = pd.DataFrame({\"fdate\": x, \"flag\": 1})\n", "\n", "forecast_calc_df = forecast_calc_df.merge(looking_forward_df, on=[\"flag\"], how=\"left\")\n", "\n", "forecast_calc_df = forecast_calc_df.drop(columns={\"flag\"})\n", "\n", "forecast_calc_df[\"dow\"] = pd.to_datetime(forecast_calc_df[\"date_\"]).dt.dayofweek\n", "forecast_calc_df[\"fdow\"] = pd.to_datetime(forecast_calc_df[\"fdate\"]).dt.dayofweek\n", "\n", "forecast_calc_df = forecast_calc_df[(forecast_calc_df[\"wt_avail\"] > 0)]\n", "\n", "forecast_calc_df.shape"]}, {"cell_type": "markdown", "id": "d487deb9-9749-42de-9280-7519f9cd7ba1", "metadata": {}, "source": ["### Ranking for Weighted Mean Method"]}, {"cell_type": "code", "execution_count": null, "id": "af333e61-adba-4f70-b136-81878dc5fb5c", "metadata": {}, "outputs": [], "source": ["forecast_calc_df = forecast_calc_df.drop(columns={\"rank\"})\n", "\n", "forecast_calc_df[\"r1\"] = forecast_calc_df.groupby([\"item_id\", \"facility_id\"])[\"date_\"].rank(\n", "    method=\"dense\", ascending=False\n", ")\n", "\n", "\n", "forecast_calc_df = forecast_calc_df.merge(\n", "    buffer_store_mapping, on=[\"facility_id\", \"outlet_id\"], how=\"left\"\n", ")\n", "\n", "forecast_calc_df[\"change_date\"] = forecast_calc_df[\"change_date\"].fillna(today_date)\n", "forecast_calc_df[\"change_age\"] = forecast_calc_df[\"change_age\"].fillna(30)\n", "forecast_calc_df[\"change_age\"] = np.where(\n", "    forecast_calc_df[\"change_age\"] == 0,\n", "    30,\n", "    np.where(forecast_calc_df[\"change_age\"] > 30, 30, forecast_calc_df[\"change_age\"]),\n", ")\n", "\n", "# forecast_calc_df = forecast_calc_df[forecast_calc_df[\"rank\"] <= 7]"]}, {"cell_type": "code", "execution_count": null, "id": "29b21b89-f8f7-44f0-be4b-a01d8d9aa720", "metadata": {}, "outputs": [], "source": ["forecast_calc_df = forecast_calc_df[forecast_calc_df[\"r1\"] <= forecast_calc_df[\"change_age\"]]\n", "\n", "forecast_calc_df = forecast_calc_df.drop(columns={\"change_age\", \"change_date\"})\n", "\n", "ranking_df = (\n", "    forecast_calc_df[[\"item_id\", \"facility_id\", \"date_\", \"dow\", \"fdate\", \"fdow\", \"r1\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "ranking_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d22b68b7-13a2-45f9-aab4-342f52f9b706", "metadata": {}, "outputs": [], "source": ["same_dow = (\n", "    ranking_df[ranking_df[\"dow\"] == ranking_df[\"fdow\"]]\n", "    .groupby([\"item_id\", \"facility_id\", \"fdate\"])\n", "    .apply(lambda x: x.sort_values(\"date_\", ascending=False))\n", "    .reset_index(drop=True)\n", ")\n", "\n", "\n", "same_dow[\"latest_dow\"] = same_dow.groupby([\"item_id\", \"facility_id\", \"fdate\"])[\"date_\"].rank(\n", "    method=\"dense\", ascending=False\n", ")\n", "\n", "same_dow = same_dow[same_dow.latest_dow > 1]\n", "same_dow[\"nd_flag\"] = 1\n", "ranking_df = ranking_df.merge(\n", "    same_dow[[\"facility_id\", \"item_id\", \"date_\", \"fdate\", \"nd_flag\"]],\n", "    on=[\"facility_id\", \"item_id\", \"date_\", \"fdate\"],\n", "    how=\"left\",\n", ")\n", "\n", "ranking_df[\"nd_flag\"] = ranking_df[\"nd_flag\"].fillna(0)\n", "ranking_df = ranking_df[ranking_df[\"nd_flag\"] == 0]\n", "# #ranking_df = ranking_df[ranking_df[\"flag\"] == 0]\n", "ranking_df[\"r2\"] = np.where(\n", "    (ranking_df[\"dow\"] == ranking_df[\"fdow\"])\n", "    & ~(ranking_df[\"facility_id\"].isin(buffer_store_facility_list)),\n", "    ranking_df[\"r1\"],\n", "    ranking_df[\"r1\"] + 30,\n", ")\n", "\n", "ranking_df[\"rank\"] = ranking_df.groupby([\"item_id\", \"facility_id\", \"fdate\"])[\"r2\"].rank(\n", "    method=\"dense\", ascending=True\n", ")\n", "\n", "ranking_df = ranking_df[ranking_df[\"rank\"] < 8]\n", "\n", "ranking_df = ranking_df[[\"facility_id\", \"item_id\", \"date_\", \"fdate\", \"rank\"]].drop_duplicates()\n", "\n", "forecast_calc_df = forecast_calc_df.drop(columns={\"r1\"}).merge(\n", "    ranking_df, on=[\"facility_id\", \"item_id\", \"date_\", \"fdate\"], how=\"inner\"\n", ")\n", "\n", "forecast_calc_df.head(1)"]}, {"cell_type": "markdown", "id": "5a729339-7d3b-4310-a52d-894c43ca7349", "metadata": {}, "source": ["### Weights Calculation"]}, {"cell_type": "code", "execution_count": null, "id": "71107b17-8d91-4a13-b147-74cdb53f44ee", "metadata": {}, "outputs": [], "source": ["date_weights_df = forecast_calc_df[[\"item_id\", \"facility_id\", \"date_\", \"fdate\", \"rank\"]]\n", "\n", "date_weights_df[\"wr\"] = date_weights_df.groupby([\"item_id\", \"facility_id\"])[\"rank\"].rank(\n", "    method=\"dense\", ascending=True\n", ")\n", "\n", "wr_agg = (\n", "    date_weights_df.groupby([\"item_id\", \"facility_id\", \"fdate\"])\n", "    .agg({\"wr\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"total_rank\"})\n", ")\n", "\n", "wr_max = (\n", "    date_weights_df.groupby([\"item_id\", \"facility_id\", \"fdate\"])\n", "    .agg({\"wr\": \"max\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"max_rank\"})\n", ")\n", "\n", "date_weights_df = date_weights_df.merge(wr_agg, on=[\"item_id\", \"facility_id\", \"fdate\"], how=\"left\")\n", "\n", "date_weights_df = date_weights_df.merge(wr_max, on=[\"item_id\", \"facility_id\", \"fdate\"], how=\"left\")\n", "\n", "date_weights_df[\"weights\"] = (\n", "    date_weights_df[\"max_rank\"] - date_weights_df[\"wr\"] + 1\n", ") / date_weights_df[\"total_rank\"]\n", "\n", "date_weights_df = date_weights_df.drop(columns={\"max_rank\", \"wr\", \"total_rank\", \"rank\"})\n", "\n", "date_weights_df.shape"]}, {"cell_type": "markdown", "id": "49f752a8-4145-45a9-b76d-9be32cef2d01", "metadata": {}, "source": ["### Weights Merge with DataFrame"]}, {"cell_type": "code", "execution_count": null, "id": "92e73913-5a41-4676-8079-452cfec62350", "metadata": {}, "outputs": [], "source": ["forecast_calc_df = (\n", "    forecast_calc_df.merge(\n", "        date_weights_df, on=[\"item_id\", \"facility_id\", \"date_\", \"fdate\"], how=\"inner\"\n", "    )\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "forecast_calc_df[\"ext_qty\"] = forecast_calc_df[\"ext_qty_hybrid\"] * forecast_calc_df[\"weights\"]\n", "\n", "forecast_calc_df[\"category\"] = \"Breads\"\n", "\n", "forecast_calc_df.head(1)"]}, {"cell_type": "markdown", "id": "b3b88308-48ae-45c6-9ac1-859308520d6a", "metadata": {}, "source": ["### Apply Weekday - Weekend Lift & Drop"]}, {"cell_type": "code", "execution_count": null, "id": "e988260e-0e0a-4b7d-a270-de5bfe8372de", "metadata": {}, "outputs": [], "source": ["forecast_calc_df = pd.merge(\n", "    forecast_calc_df, final_increment_df, on=[\"city\", \"dow\", \"fdow\"], how=\"left\"\n", ")\n", "\n", "forecast_calc_df[\"bf\"] = forecast_calc_df[\"bf\"].fillna(1)\n", "\n", "forecast_calc_df[\"bf\"] = np.where(forecast_calc_df[\"bf\"] > 1.35, 1.35, forecast_calc_df[\"bf\"])\n", "\n", "forecast_calc_df[\"bf\"] = np.where(forecast_calc_df[\"bf\"] < 0.75, 0.75, forecast_calc_df[\"bf\"])\n", "\n", "forecast_calc_df[\"final_ex_qty\"] = forecast_calc_df[\"ext_qty\"] * forecast_calc_df[\"bf\"]\n", "\n", "forecast_calc_df = (\n", "    forecast_calc_df.groupby(\n", "        [\n", "            \"city\",\n", "            \"be_facility_id\",\n", "            \"be_outlet_id\",\n", "            \"be_outlet_name\",\n", "            \"category\",\n", "            \"ptype\",\n", "            \"facility_id\",\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"stype_\",\n", "            \"ftype_\",\n", "            \"fdate\",\n", "            \"fdow\",\n", "        ]\n", "    )\n", "    .agg({\"final_ex_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "forecast_calc_df.head(1)"]}, {"cell_type": "markdown", "id": "bac34453-723b-46a7-96d9-a7c908615fc3", "metadata": {}, "source": ["## edit- new store"]}, {"cell_type": "code", "execution_count": null, "id": "67772c18-575b-4e8d-b4e9-8c2795e018d3", "metadata": {}, "outputs": [], "source": ["max_date = forecast_calc_df[\"fdate\"].max()\n", "max_date = max_date.strftime(\"%Y-%m-%d\")\n", "max_date"]}, {"cell_type": "markdown", "id": "43576ff9-596d-4b89-a00f-22b61f8f3463", "metadata": {}, "source": ["## edit- New Store Min Max"]}, {"cell_type": "code", "execution_count": null, "id": "fc0d0550-89c4-47bd-9349-767c8271ada6", "metadata": {}, "outputs": [], "source": ["new_store_forecast_query = f\"\"\"\n", "    WITH base AS (\n", "                SELECT a.date_, a.facility_id, a.item_id, a.outlet_id, a.category, cl.name as city,\n", "                CASE WHEN item_nearby_fps IS NULL THEN 0 ELSE item_nearby_fps END AS item_nearby_fps,\n", "                CASE WHEN item_recent_fps IS NULL THEN 0 ELSE item_recent_fps END AS item_recent_fps, \n", "                CASE WHEN item_avail_fps IS NULL THEN 0 ELSE item_avail_fps END AS item_avail_fps\n", "                FROM (\n", "                    SELECT a.* FROM supply_etls.new_store_forecast_logs a\n", "                    JOIN (\n", "                        SELECT facility_id, item_id, date_, MAX(updated_at) AS updated_at\n", "                        FROM supply_etls.new_store_forecast_logs\n", "                        GROUP BY 1,2,3\n", "                    ) b ON a.facility_id = b.facility_id AND a.item_id = b.item_id AND a.date_ = b.date_ AND a.updated_at = b.updated_at\n", "                    WHERE a.category IN ('Breads') \n", "                    AND DATE(a.date_) > (DATE('{today_date}')) \n", "                    AND DATE(a.date_) <= (DATE('{max_date}'))\n", "                ) as a \n", "\n", "                JOIN retail.console_outlet co ON co.id = a.outlet_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "                LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id AND cl.lake_active_record\n", "\n", "                inner join \n", "                    (SELECT DISTINCT a.item_id\n", "                            FROM rpc.product_product a\n", "                            JOIN (\n", "                                SELECT item_id, MAX(id) AS id\n", "                                FROM rpc.product_product\n", "                                WHERE approved = 1 AND active = 1\n", "                                GROUP BY 1\n", "                            ) b ON a.id = b.id AND a.item_id = b.item_id\n", "                            WHERE a.active = 1 AND a.lake_active_record AND a.approved = 1 AND a.perishable = 1\n", "                        ) ma ON ma.item_id = a.item_id\n", "            ),\n", "            \n", "\n", "    min_max_base AS (\n", "        SELECT date_, facility_id, outlet_id, item_id, city, category, item_nearby_fps, item_recent_fps, item_avail_fps\n", "        FROM base\n", "    ),\n", "    \n", "    store_age_base AS (\n", "       WITH first_outbound AS (\n", "            SELECT outlet_id, DATE(od.cart_checkout_ts_ist) AS f_date\n", "            FROM dwh.fact_sales_order_details od \n", "            WHERE od.order_create_dt_ist > current_date - interval '60' day\n", "            AND od.is_internal_order = false \n", "            AND od.outlet_id IN (SELECT DISTINCT outlet_id FROM min_max_base)\n", "            AND (od.order_type NOT LIKE '%%internal%%' OR od.order_type IS NULL) AND od.order_current_status = 'DELIVERED'\n", "            GROUP BY 1,2\n", "            having  count(distinct order_id)>10\n", "        )\n", "        \n", "        SELECT outlet_id, DATE_DIFF('day',  min(f_date), DATE('{today_date}')) AS age\n", "        FROM first_outbound\n", "        group by 1\n", "    ),\n", "    \n", "    be_mapping AS (\n", "        SELECT DISTINCT item_id, outlet_id, cb.facility_id AS be_facility_id , cb.name AS be_outlet_name, cb.id AS be_outlet_id\n", "        FROM rpc.item_outlet_tag_mapping tm\n", "        JOIN retail.console_outlet cb ON cb.id = CAST(tag_value AS int) AND cb.active = 1 AND cb.lake_active_record\n", "        WHERE tm.active = 1 AND tm.tag_type_id = 8 AND tm.lake_active_record\n", "    )\n", "    \n", "     SELECT a.*, icd.product_type as ptype,\n", "    CASE WHEN b.age IS NULL THEN 0 ELSE age END AS age, \n", "    CASE WHEN be_outlet_id IS NULL THEN 0 ELSE be_outlet_id END AS be_outlet_id,\n", "    CASE WHEN c.be_facility_id IS NULL THEN 0 ELSE c.be_facility_id END AS be_facility_id, c.be_outlet_name\n", "\n", "    FROM min_max_base a\n", "    LEFT JOIN store_age_base b ON a.outlet_id = b.outlet_id\n", "    LEFT JOIN be_mapping c ON a.outlet_id = c.outlet_id AND a.item_id = c.item_id\n", "    left join rpc.item_category_details icd on a.item_id = icd.item_id\n", "    \n", "\"\"\"\n", "new_store_df = read_sql_query(new_store_forecast_query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "db4a1f56-cc6a-43f6-8f68-70ad0e64f86e", "metadata": {}, "outputs": [], "source": ["new_store_df[\"fdow\"] = pd.to_datetime(new_store_df[\"date_\"]).dt.dayofweek\n", "\n", "\n", "new_store_df = (\n", "    new_store_df[new_store_df[\"age\"] < 15][\n", "        [\n", "            \"category\",\n", "            \"date_\",\n", "            \"ptype\",\n", "            \"item_id\",\n", "            \"facility_id\",\n", "            \"outlet_id\",\n", "            \"be_outlet_id\",\n", "            \"be_facility_id\",\n", "            \"be_outlet_name\",\n", "            \"item_nearby_fps\",\n", "            \"item_recent_fps\",\n", "            \"item_avail_fps\",\n", "            \"city\",\n", "            \"fdow\",\n", "        ]\n", "    ]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "new_store_df[\"item_min_fps\"] = (0.75 * new_store_df[\"item_nearby_fps\"]) + (\n", "    0.25 * new_store_df[\"item_recent_fps\"]\n", ")\n", "\n", "new_store_df[\"new_min_qty\"] = new_store_df[[\"item_min_fps\", \"item_avail_fps\"]].max(axis=1)\n", "\n", "new_store_df[\"new_min_qty\"] = new_store_df[\"new_min_qty\"].fillna(0)\n", "\n", "new_store_df[\"new_min_qty\"] = np.where(\n", "    new_store_df[\"new_min_qty\"] > 1, new_store_df[\"new_min_qty\"], 1\n", ").astype(int)\n", "\n", "new_store_df = (\n", "    new_store_df[\n", "        [\n", "            \"city\",\n", "            \"be_facility_id\",\n", "            \"be_outlet_id\",\n", "            \"be_outlet_name\",\n", "            \"category\",\n", "            \"ptype\",\n", "            \"facility_id\",\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"date_\",\n", "            \"fdow\",\n", "            \"new_min_qty\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "new_store_df.new_min_qty.sum()"]}, {"cell_type": "code", "execution_count": null, "id": "35f2b8c7-aa11-4d5a-a6ba-0dd0b9076ec5", "metadata": {}, "outputs": [], "source": ["new_store_df.rename(columns={\"date_\": \"fdate\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "eae976ef-cce7-4fba-86f8-7bce2ef5a4ba", "metadata": {}, "outputs": [], "source": ["forecast_calc_df[\"fdate\"] = pd.to_datetime(forecast_calc_df[\"fdate\"])\n", "new_store_df[\"fdate\"] = pd.to_datetime(new_store_df[\"fdate\"])"]}, {"cell_type": "markdown", "id": "202d8423-5958-41e3-90dd-d45b5690e406", "metadata": {}, "source": ["## Concat w new store df"]}, {"cell_type": "code", "execution_count": null, "id": "516d47a6-7525-40e8-81f1-134e938f55cc", "metadata": {}, "outputs": [], "source": ["forecast_new_store = (\n", "    forecast_calc_df.merge(\n", "        new_store_df,\n", "        on=[\n", "            \"city\",\n", "            \"be_facility_id\",\n", "            \"be_outlet_id\",\n", "            \"be_outlet_name\",\n", "            \"category\",\n", "            \"ptype\",\n", "            \"facility_id\",\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"fdate\",\n", "            \"fdow\",\n", "        ],\n", "        how=\"left\",\n", "    )\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a78c1c40-3c5b-4bd3-b0a4-4b88c792692c", "metadata": {}, "outputs": [], "source": ["forecast_new_store = (\n", "    forecast_new_store[forecast_new_store[\"new_min_qty\"].isna()]\n", "    .reset_index()\n", "    .drop(columns={\"index\", \"new_min_qty\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b4eb5663-8ebc-410a-90ac-bf6cc5eeb736", "metadata": {}, "outputs": [], "source": ["## get ftype from old df\n", "ftype_df = forecast_calc_df[[\"facility_id\", \"ftype_\"]].drop_duplicates()\n", "new_store_df = pd.merge(new_store_df, ftype_df, on=[\"facility_id\"], how=\"left\")\n", "new_store_df[\"ftype_\"] = new_store_df[\"ftype_\"].fillna(\"low\")\n", "\n", "\n", "## get stype from old df\n", "stype_df = forecast_calc_df[[\"be_facility_id\", \"item_id\", \"stype_\"]].drop_duplicates()\n", "new_store_df = pd.merge(new_store_df, stype_df, on=[\"item_id\", \"be_facility_id\"], how=\"left\")\n", "new_store_df[\"stype_\"] = new_store_df[\"stype_\"].fillna(\"bottom\")"]}, {"cell_type": "markdown", "id": "07a91704-0af1-4889-a1ab-c0c5e87e3a95", "metadata": {}, "source": ["### recent exp sales of new stores"]}, {"cell_type": "code", "execution_count": null, "id": "df29f014-4eff-4c80-9f42-10491ee15880", "metadata": {}, "outputs": [], "source": ["new_facility_df = new_store_df[[\"facility_id\", \"item_id\"]].drop_duplicates()\n", "\n", "new_store_sales = sales_pre_df.merge(new_facility_df, on=[\"facility_id\", \"item_id\"], how=\"inner\")\n", "new_store_sales_hw = new_store_sales.merge(\n", "    hour_weights_df, on=[\"city\", \"item_id\", \"hour_\"], how=\"left\"\n", ")\n", "new_store_sales_hw = new_store_sales_hw.merge(\n", "    assortment_df, on=[\"city\", \"item_id\", \"facility_id\", \"hour_\", \"date_\"], how=\"left\"\n", ")\n", "\n", "new_store_sales_hw[\"wt_score\"] = new_store_sales_hw[\"is_available\"] * new_store_sales_hw[\"new_hw\"]"]}, {"cell_type": "code", "execution_count": null, "id": "87bc885a-e305-4168-8cb7-4c49550724c3", "metadata": {}, "outputs": [], "source": ["new_store_daily = (\n", "    new_store_sales_hw.groupby([\"facility_id\", \"item_id\", \"date_\"])\n", "    .agg(\n", "        {\n", "            \"is_available\": \"sum\",\n", "            \"hour_\": \"nunique\",\n", "            \"new_hw\": \"sum\",\n", "            \"wt_score\": \"sum\",\n", "            \"quantity\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "new_store_daily[\"avail\"] = new_store_daily[\"is_available\"] / new_store_daily[\"hour_\"]\n", "\n", "new_store_daily[\"wt_avail\"] = new_store_daily[\"wt_score\"] / new_store_daily[\"new_hw\"]\n", "\n", "new_store_daily[\"wt_avail\"] = new_store_daily[\"wt_avail\"].fillna(0)\n", "\n", "new_store_daily[\"wt_avail\"] = np.where(\n", "    (new_store_daily[\"avail\"] > 0) & (new_store_daily[\"wt_avail\"] == 0),\n", "    1,\n", "    new_store_daily[\"wt_avail\"],\n", ")\n", "\n", "new_store_daily[\"ext_qty_li\"] = new_store_daily[\"quantity\"] * (\n", "    1 + 0.35 * (1 - new_store_daily[\"wt_avail\"])\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3ec1f136-1f3a-46b6-ada7-9428d357bee1", "metadata": {}, "outputs": [], "source": ["new_store_daily_agg = (\n", "    new_store_daily.groupby([\"facility_id\", \"item_id\", \"date_\"])[\"ext_qty_li\"].sum().reset_index()\n", ")\n", "\n", "new_store_daily_max = (\n", "    new_store_daily_agg.groupby([\"facility_id\", \"item_id\"])[\"ext_qty_li\"].max().reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1120f5b2-b4e4-416a-bf14-59c460bd8861", "metadata": {}, "outputs": [], "source": ["new_store_df = new_store_df.merge(new_store_daily_max, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "new_store_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "91aa67d7-6a69-49cb-a92b-d030aedfd344", "metadata": {}, "outputs": [], "source": ["new_store_df[\"ext_qty_li\"] = new_store_df[\"ext_qty_li\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "00893b34-6202-4847-b573-2073cd87267d", "metadata": {}, "outputs": [], "source": ["new_store_df[\"new_min_qty\"].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "d9909bbc-b5c1-428e-ba89-258e1a0a301a", "metadata": {}, "outputs": [], "source": ["new_store_df[\"ext_qty_li\"].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "706b749a-4b74-49cb-9c44-c6dda2f24177", "metadata": {}, "outputs": [], "source": ["new_store_df[\"new_forecast\"] = new_store_df[[\"ext_qty_li\", \"new_min_qty\"]].max(axis=1)\n", "new_store_df[\"new_forecast\"].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "ac5e1ad2-7834-4207-84ff-4ad8cad8df80", "metadata": {}, "outputs": [], "source": ["new_store_df[[\"be_facility_id\", \"be_outlet_id\", \"facility_id\", \"outlet_id\", \"item_id\"]] = (\n", "    new_store_df[[\"be_facility_id\", \"be_outlet_id\", \"facility_id\", \"outlet_id\", \"item_id\"]].astype(\n", "        int\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1f95cd80-2a60-49be-a8c5-b6f727c67ba9", "metadata": {}, "outputs": [], "source": ["## concat w original df\n", "new_store_df = new_store_df.drop(columns=[\"new_min_qty\", \"ext_qty_li\"])\n", "forecast_new_store = pd.concat(\n", "    [forecast_new_store, new_store_df.rename(columns={\"new_forecast\": \"final_ex_qty\"})]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "abfa5722-a450-4c1a-ac04-d871e361a84a", "metadata": {}, "outputs": [], "source": ["forecast_new_store.groupby([\"fdate\"]).agg({\"final_ex_qty\": \"sum\"}).head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "5038f91e-1fd5-401b-a512-58bf1ff97a8a", "metadata": {}, "outputs": [], "source": ["forecast_calc_df.groupby([\"fdate\"]).agg({\"final_ex_qty\": \"sum\"}).head(10)"]}, {"cell_type": "markdown", "id": "16d0ee28-b21f-4f03-bedd-018d22fcbe30", "metadata": {}, "source": ["### edit - main df"]}, {"cell_type": "code", "execution_count": null, "id": "3d7908f9-a4ad-433b-af71-bb625919c519", "metadata": {}, "outputs": [], "source": ["forecast_calc_df = forecast_new_store"]}, {"cell_type": "markdown", "id": "c8b59e27-b7f2-46aa-b9b5-6d2ae11d1aba", "metadata": {}, "source": ["# Man<PERSON> Bump Factors"]}, {"cell_type": "markdown", "id": "c55e1a51-568a-4794-a4a7-b5b8078787f5", "metadata": {}, "source": ["## Backend <PERSON><PERSON> Bumps"]}, {"cell_type": "code", "execution_count": null, "id": "9ad1c374-e3db-49a2-b230-1f2d789ef1ad", "metadata": {}, "outputs": [], "source": ["backend_item_input = pb.from_sheets(\n", "    sheetid=\"1wFQBrKUB6LIt31VgDT6NoKBfva-DMLpPQtQxnmZRb3w\",\n", "    sheetname=\"config:backend-item-input\",\n", ")\n", "\n", "# backend_item_input = pd.read_csv(\"Perishable-Ordering Params - config_backend-item-input.csv\")\n", "\n", "backend_item_input = backend_item_input[~backend_item_input[\"start_date\"].isna()]\n", "backend_item_input = backend_item_input[~backend_item_input[\"end_date\"].isna()]\n", "backend_item_input = backend_item_input[~backend_item_input[\"backend_outlet_id\"].isna()]\n", "backend_item_input = backend_item_input[~backend_item_input[\"item_id\"].isna()]\n", "backend_item_input = backend_item_input[~backend_item_input[\"quantity\"].isna()]\n", "\n", "backend_item_input[\"backend_outlet_id\"] = backend_item_input[\"backend_outlet_id\"].astype(int)\n", "backend_item_input[\"item_id\"] = backend_item_input[\"item_id\"].astype(int)\n", "backend_item_input[\"quantity\"] = backend_item_input[\"quantity\"].astype(int)\n", "\n", "backend_item_input = backend_item_input.rename(\n", "    columns={\"backend_outlet_id\": \"be_outlet_id\"}\n", ").reset_index()\n", "\n", "backend_item_input[\"date\"] = list(\n", "    map(\n", "        lambda x, y: pd.date_range(start=x, end=y),\n", "        backend_item_input[\"start_date\"],\n", "        backend_item_input[\"end_date\"],\n", "    )\n", ")\n", "\n", "backend_item_input = backend_item_input.explode(\"date\").drop([\"start_date\", \"end_date\"], axis=1)\n", "\n", "backend_item_input = backend_item_input.rename(columns={\"date\": \"fdate\"})\n", "\n", "backend_item_input[\"fdate\"] = pd.to_datetime(backend_item_input[\"fdate\"])\n", "\n", "backend_item_input_max = (\n", "    backend_item_input.groupby([\"be_outlet_id\", \"item_id\", \"fdate\"])\n", "    .agg({\"index\": \"max\"})\n", "    .reset_index()\n", ")\n", "\n", "backend_item_input = backend_item_input.merge(\n", "    backend_item_input_max,\n", "    on=[\"be_outlet_id\", \"item_id\", \"fdate\", \"index\"],\n", "    how=\"inner\",\n", ")\n", "\n", "backend_item_input = backend_item_input.drop(columns={\"index\"})\n", "\n", "factor_calc_df = (\n", "    forecast_calc_df.groupby([\"be_outlet_id\", \"item_id\", \"fdate\"])\n", "    .agg({\"final_ex_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "backend_item_input = backend_item_input.merge(\n", "    factor_calc_df, on=[\"be_outlet_id\", \"item_id\", \"fdate\"], how=\"inner\"\n", ")\n", "\n", "backend_item_input[\"be_factor\"] = (\n", "    backend_item_input[\"quantity\"] / backend_item_input[\"final_ex_qty\"]\n", ")\n", "\n", "backend_item_input = (\n", "    backend_item_input[[\"be_outlet_id\", \"item_id\", \"fdate\", \"be_factor\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "backend_item_input.head(1)"]}, {"cell_type": "markdown", "id": "0b95e5c3-dadd-4814-b284-95f0ee586842", "metadata": {}, "source": ["### Apply Factors"]}, {"cell_type": "code", "execution_count": null, "id": "4840afcb-b522-4d44-94f4-a383c4e4d221", "metadata": {}, "outputs": [], "source": ["forecast_calc_df = pd.merge(\n", "    forecast_calc_df,\n", "    backend_item_input,\n", "    on=[\"be_outlet_id\", \"item_id\", \"fdate\"],\n", "    how=\"left\",\n", ")\n", "\n", "forecast_calc_df[\"be_factor\"] = forecast_calc_df[\"be_factor\"].fillna(1)\n", "\n", "forecast_calc_df[\"final_ex_qty\"] = forecast_calc_df[\"final_ex_qty\"] * forecast_calc_df[\"be_factor\"]\n", "\n", "forecast_calc_df[\"final_ex_qty\"] = forecast_calc_df[\"final_ex_qty\"].fillna(0).astype(float)\n", "\n", "forecast_calc_df.head(1)"]}, {"cell_type": "markdown", "id": "798fb0d5-6b8d-4900-9e1a-e964fe9245eb", "metadata": {}, "source": ["## City L2 Bumps"]}, {"cell_type": "code", "execution_count": null, "id": "45931426-07f6-4399-8a32-ab72dccbdaee", "metadata": {}, "outputs": [], "source": ["sku_df = pd.DataFrame({\"top\", \"bottom\"}, columns=[\"stype_\"])\n", "\n", "sku_df[\"sku_type\"] = \"all\""]}, {"cell_type": "code", "execution_count": null, "id": "e05c272c-fc19-4d4b-9f20-02c9a9f225c7", "metadata": {}, "outputs": [], "source": ["ptype_df = forecast_calc_df[[\"ptype\"]].drop_duplicates().reset_index().drop(columns={\"index\"})\n", "\n", "ptype_df[\"ptype_\"] = \"all\""]}, {"cell_type": "code", "execution_count": null, "id": "95cc277f-f5f5-4feb-87fd-8663d9c32184", "metadata": {}, "outputs": [], "source": ["city_l2_bump_df = pb.from_sheets(\n", "    sheetid=\"1wFQBrKUB6LIt31VgDT6NoKBfva-DMLpPQtQxnmZRb3w\",\n", "    sheetname=\"config:new-logic-l2-bumps\",\n", ").rename(columns={\"l2\": \"category\"})\n", "\n", "# city_l2_bump_df = pd.read_csv(\"Perishable-Ordering Params - config_new-logic-l2-bumps.csv\").rename(columns={\"l2\": \"category\"})\n", "\n", "city_l2_bump_df[\"factor\"] = city_l2_bump_df[\"factor\"].astype(float)\n", "\n", "city_l2_bump_df = city_l2_bump_df.merge(ptype_df, on=[\"ptype_\"], how=\"left\")\n", "\n", "city_l2_bump_df[\"ptype\"] = np.where(\n", "    city_l2_bump_df[\"ptype\"].isna(), city_l2_bump_df[\"ptype_\"], city_l2_bump_df[\"ptype\"]\n", ")\n", "\n", "city_l2_bump_df = city_l2_bump_df.merge(sku_df, on=[\"sku_type\"], how=\"left\")\n", "\n", "city_l2_bump_df[\"stype_\"] = np.where(\n", "    city_l2_bump_df[\"stype_\"].isna(),\n", "    city_l2_bump_df[\"sku_type\"],\n", "    city_l2_bump_df[\"stype_\"],\n", ")\n", "\n", "city_l2_bump_df = city_l2_bump_df.drop(columns={\"sku_type\", \"ptype_\"})\n", "\n", "city_l2_bump_df[\"fdate\"] = list(\n", "    map(\n", "        lambda x, y: pd.date_range(start=x, end=y),\n", "        city_l2_bump_df[\"start_date\"],\n", "        city_l2_bump_df[\"end_date\"],\n", "    )\n", ")\n", "\n", "city_l2_bump_df = city_l2_bump_df.explode(\"fdate\").drop([\"start_date\", \"end_date\"], axis=1)\n", "\n", "city_l2_bump_df[\"fdate\"] = pd.to_datetime(city_l2_bump_df[\"fdate\"])\n", "\n", "city_l2_bump_df = city_l2_bump_df.reset_index()\n", "\n", "l2_max_df = (\n", "    city_l2_bump_df.groupby([\"city\", \"category\", \"ptype\", \"stype_\", \"fdate\"])\n", "    .agg({\"index\": \"max\"})\n", "    .reset_index()\n", ")\n", "\n", "city_l2_bump_df = city_l2_bump_df.merge(\n", "    l2_max_df, on=[\"city\", \"category\", \"ptype\", \"stype_\", \"fdate\", \"index\"], how=\"inner\"\n", ").drop(columns={\"index\"})\n", "\n", "city_l2_bump_df.head(1)"]}, {"cell_type": "markdown", "id": "72c3dde1-c9d1-4531-9946-2cd9bc708aa5", "metadata": {}, "source": ["### Apply Growth Degrowth"]}, {"cell_type": "code", "execution_count": null, "id": "a3d6354c-99ec-43dc-99e0-cda8836e7d01", "metadata": {}, "outputs": [], "source": ["forecast_calc_df = forecast_calc_df.merge(\n", "    city_l2_bump_df, on=[\"city\", \"category\", \"ptype\", \"stype_\", \"fdate\"], how=\"left\"\n", ")\n", "forecast_calc_df[\"factor\"] = forecast_calc_df[\"factor\"].fillna(1)\n", "\n", "forecast_calc_df[\"final_ex_qty\"] = forecast_calc_df[\"final_ex_qty\"] * forecast_calc_df[\"factor\"]\n", "\n", "forecast_calc_df = forecast_calc_df.rename(columns={\"factor\": \"city_l2_factor\"})\n", "\n", "forecast_calc_df.head(1)"]}, {"cell_type": "markdown", "id": "26140589-1477-4574-9458-6a7eff8d8de6", "metadata": {"tags": []}, "source": ["## Store L2 Bumps"]}, {"cell_type": "code", "execution_count": null, "id": "d590c936-704e-4897-86f8-3d82e426cb57", "metadata": {}, "outputs": [], "source": ["sku_df = pd.DataFrame({\"top\", \"bottom\"}, columns=[\"stype_\"])\n", "sku_df[\"sku_type\"] = \"all\"\n", "\n", "ptype_df = forecast_calc_df[[\"ptype\"]].drop_duplicates().reset_index().drop(columns={\"index\"})\n", "ptype_df[\"ptype_\"] = \"all\"\n", "\n", "\n", "store_l2_bump_df = pb.from_sheets(\n", "    sheetid=\"1wFQBrKUB6LIt31VgDT6NoKBfva-DMLpPQtQxnmZRb3w\",\n", "    sheetname=\"config:new-logic-store-l2-bumps\",\n", ").rename(columns={\"l2\": \"category\"})\n", "\n", "# store_l2_bump_df = pd.read_csv(\"Perishable-Ordering Params - config_new-logic-store-l2-bumps.csv\").rename(columns={\"l2\": \"category\"})\n", "\n", "store_l2_bump_df[\"facility_id\"] = store_l2_bump_df[\"facility_id\"].astype(int)\n", "store_l2_bump_df[\"factor\"] = store_l2_bump_df[\"factor\"].astype(float)\n", "\n", "store_l2_bump_df = store_l2_bump_df.merge(ptype_df, on=[\"ptype_\"], how=\"left\")\n", "\n", "store_l2_bump_df[\"ptype\"] = np.where(\n", "    store_l2_bump_df[\"ptype\"].isna(), store_l2_bump_df[\"ptype_\"], store_l2_bump_df[\"ptype\"]\n", ")\n", "\n", "store_l2_bump_df = store_l2_bump_df.merge(sku_df, on=[\"sku_type\"], how=\"left\")\n", "\n", "store_l2_bump_df[\"stype_\"] = np.where(\n", "    store_l2_bump_df[\"stype_\"].isna(),\n", "    store_l2_bump_df[\"sku_type\"],\n", "    store_l2_bump_df[\"stype_\"],\n", ")\n", "\n", "store_l2_bump_df = store_l2_bump_df.drop(columns={\"sku_type\", \"ptype_\"})\n", "\n", "store_l2_bump_df[\"fdate\"] = list(\n", "    map(\n", "        lambda x, y: pd.date_range(start=x, end=y),\n", "        store_l2_bump_df[\"start_date\"],\n", "        store_l2_bump_df[\"end_date\"],\n", "    )\n", ")\n", "\n", "store_l2_bump_df = store_l2_bump_df.explode(\"fdate\").drop([\"start_date\", \"end_date\"], axis=1)\n", "\n", "store_l2_bump_df[\"fdate\"] = pd.to_datetime(store_l2_bump_df[\"fdate\"])\n", "\n", "store_l2_bump_df = store_l2_bump_df.reset_index()\n", "\n", "store_l2_max_df = (\n", "    store_l2_bump_df.groupby([\"facility_id\", \"category\", \"ptype\", \"stype_\", \"fdate\"])\n", "    .agg({\"index\": \"max\"})\n", "    .reset_index()\n", ")\n", "\n", "store_l2_bump_df = store_l2_bump_df.merge(\n", "    store_l2_max_df,\n", "    on=[\"facility_id\", \"category\", \"ptype\", \"stype_\", \"fdate\", \"index\"],\n", "    how=\"inner\",\n", ").drop(columns={\"index\"})\n", "\n", "store_l2_bump_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "a848d23e-ffb8-482d-a10f-42c05cfaa573", "metadata": {}, "outputs": [], "source": ["forecast_calc_df = forecast_calc_df.merge(\n", "    store_l2_bump_df, on=[\"facility_id\", \"category\", \"ptype\", \"stype_\", \"fdate\"], how=\"left\"\n", ")\n", "forecast_calc_df[\"factor\"] = forecast_calc_df[\"factor\"].fillna(1)\n", "\n", "forecast_calc_df[\"final_ex_qty\"] = forecast_calc_df[\"final_ex_qty\"] * forecast_calc_df[\"factor\"]\n", "\n", "forecast_calc_df = forecast_calc_df.rename(columns={\"factor\": \"store_l2_factor\"})\n", "\n", "forecast_calc_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "502c7b6a-11af-4333-9e50-42c7377d36b0", "metadata": {}, "outputs": [], "source": ["del (sku_df, city_l2_bump_df, l2_max_df, ptype_df)\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "9610bb53-696e-4ced-82a5-9faf5492a4f1", "metadata": {}, "source": ["## City Item Bumps"]}, {"cell_type": "code", "execution_count": null, "id": "e0b4b3ab-4b98-471e-9d2a-f35ba2e78b2e", "metadata": {}, "outputs": [], "source": ["city_item_bump_df = pb.from_sheets(\n", "    sheetid=\"1wFQBrKUB6LIt31VgDT6NoKBfva-DMLpPQtQxnmZRb3w\",\n", "    sheetname=\"config:city-item-growth\",\n", ")\n", "\n", "# city_item_bump_df = pd.read_csv(\"Perishable-Ordering Params - config_city-item-growth.csv\")\n", "\n", "city_item_bump_df[\"item_id\"] = city_item_bump_df[\"item_id\"].astype(int)\n", "\n", "city_item_bump_df[\"factor\"] = city_item_bump_df[\"factor\"].astype(float)\n", "\n", "city_item_bump_df[\"fdate\"] = list(\n", "    map(\n", "        lambda x, y: pd.date_range(start=x, end=y),\n", "        city_item_bump_df[\"start_date\"],\n", "        city_item_bump_df[\"end_date\"],\n", "    )\n", ")\n", "\n", "city_item_bump_df = city_item_bump_df.explode(\"fdate\").drop([\"start_date\", \"end_date\"], axis=1)\n", "\n", "city_item_bump_df[\"fdate\"] = pd.to_datetime(city_item_bump_df[\"fdate\"])\n", "\n", "city_item_bump_df = city_item_bump_df.reset_index()\n", "\n", "max_df = city_item_bump_df.groupby([\"city\", \"item_id\", \"fdate\"]).agg({\"index\": \"max\"}).reset_index()\n", "\n", "city_item_bump_df = city_item_bump_df.merge(\n", "    max_df, on=[\"city\", \"item_id\", \"fdate\", \"index\"], how=\"inner\"\n", ").drop(columns={\"index\"})\n", "\n", "city_item_bump_df.head(1)"]}, {"cell_type": "markdown", "id": "87e8bc75-f25b-497e-b8b0-37a95698672a", "metadata": {}, "source": ["### Apply Growth Factors"]}, {"cell_type": "code", "execution_count": null, "id": "486d38f3-9436-4c26-9c86-bbbe9c7d96c9", "metadata": {}, "outputs": [], "source": ["forecast_calc_df = forecast_calc_df.merge(\n", "    city_item_bump_df, on=[\"city\", \"item_id\", \"fdate\"], how=\"left\"\n", ")\n", "\n", "forecast_calc_df[\"factor\"] = forecast_calc_df[\"factor\"].fillna(1)\n", "\n", "forecast_calc_df[\"final_ex_qty\"] = forecast_calc_df[\"final_ex_qty\"] * forecast_calc_df[\"factor\"]\n", "\n", "forecast_calc_df = forecast_calc_df.rename(columns={\"factor\": \"city_item_factor\"})\n", "\n", "forecast_calc_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "9df85b17-e389-44dd-a60d-8258bffa1c6b", "metadata": {}, "outputs": [], "source": ["del (city_item_bump_df, max_df)\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "a4d831b3-2bd7-4da1-8a1d-ae9ea414d90c", "metadata": {}, "source": ["## Forecast Logs"]}, {"cell_type": "code", "execution_count": null, "id": "98950d36-1a8c-40c1-ab94-0f0e75ee45f3", "metadata": {}, "outputs": [], "source": ["plugin_df = pb.from_sheets(\n", "    sheetid=\"1wFQBrKUB6LIt31VgDT6NoKBfva-DMLpPQtQxnmZRb3w\",\n", "    sheetname=\"config:new-logic-plugin\",\n", ")\n", "\n", "# plugin_df = pd.read_csv(\"Perishable-Ordering Params - config_new-logic-plugin.csv\")\n", "\n", "plugin_df = plugin_df[plugin_df[\"category\"] == \"Breads\"]\n", "\n", "plugin_df[\"flag\"] = plugin_df[\"flag\"].astype(int)\n", "\n", "split_df = plugin_df[\"backend_to_test\"].str.split(\",\", expand=True)\n", "backend_df = plugin_df[[\"category\", \"flag\"]]\n", "backend_df = pd.concat([backend_df, split_df], axis=1)\n", "\n", "backend_df = pd.melt(backend_df, id_vars=[\"category\", \"flag\"])\n", "\n", "plugin_flag = plugin_df.iloc[0, 1]\n", "\n", "backend_list = plugin_df.iloc[0, 2]\n", "\n", "if backend_list == \"all\":\n", "    be_list = list(forecast_calc_df[\"be_facility_id\"].unique())\n", "else:\n", "    be_list = list(backend_df[\"value\"].astype(int).unique())\n", "\n", "len(be_list)"]}, {"cell_type": "code", "execution_count": null, "id": "4d135b5a-5f07-46f4-9cb3-5c4fe29dc30f", "metadata": {}, "outputs": [], "source": ["forecast_logs_df = forecast_calc_df.copy()\n", "\n", "forecast_logs_df[\"temp_flag\"] = plugin_flag\n", "\n", "forecast_logs_df[\"flag\"] = np.where(\n", "    (forecast_logs_df[\"be_facility_id\"].isin(be_list)) & (forecast_logs_df[\"temp_flag\"] == 1),\n", "    1,\n", "    0,\n", ")\n", "\n", "forecast_logs_df = forecast_logs_df[\n", "    [\n", "        \"city\",\n", "        \"be_facility_id\",\n", "        \"category\",\n", "        \"ptype\",\n", "        \"facility_id\",\n", "        \"outlet_id\",\n", "        \"ftype_\",\n", "        \"item_id\",\n", "        \"stype_\",\n", "        \"fdate\",\n", "        \"flag\",\n", "        \"be_factor\",\n", "        \"city_l2_factor\",\n", "        \"final_ex_qty\",\n", "    ]\n", "]\n", "\n", "forecast_logs_df.shape"]}, {"cell_type": "markdown", "id": "ae0717ee-f067-46cc-90f7-bdf28e14619d", "metadata": {}, "source": ["## New SKU Launch Input"]}, {"cell_type": "code", "execution_count": null, "id": "f464c719-f8d4-4803-a916-b13e43083be5", "metadata": {}, "outputs": [], "source": ["new_sku_input_df = pb.from_sheets(\n", "    sheetid=\"1wFQBrKUB6LIt31VgDT6NoKBfva-DMLpPQtQxnmZRb3w\",\n", "    sheetname=\"config:new-sku-launch\",\n", ")\n", "\n", "# new_sku_input_df = pd.read_csv(\"Perishable-Ordering Params - config_new-sku-launch.csv\")\n", "\n", "new_sku_input_df[[\"be_outlet_id\", \"new_item_id\", \"replicate_item_id\"]] = new_sku_input_df[\n", "    [\"be_outlet_id\", \"new_item_id\", \"replicate_item_id\"]\n", "].astype(int)\n", "\n", "new_sku_input_df.head(1)"]}, {"cell_type": "markdown", "id": "7fda1791-1b6c-48fe-b22b-14d48676da1f", "metadata": {}, "source": ["### New SKU"]}, {"cell_type": "code", "execution_count": null, "id": "f4c56bde-8876-4516-9e41-06b082a6a3bc", "metadata": {}, "outputs": [], "source": ["new_launch_df = new_sku_input_df[\n", "    new_sku_input_df[\"new_item_id\"] == new_sku_input_df[\"replicate_item_id\"]\n", "].reset_index()\n", "\n", "new_launch_df = new_launch_df.drop(columns={\"index\", \"replicate_item_id\", \"factor\"}).rename(\n", "    columns={\"new_item_id\": \"item_id\"}\n", ")\n", "\n", "new_launch_df[\"item_id\"] = new_launch_df[\"item_id\"].fillna(0).astype(int)\n", "\n", "new_launch_df[\"quantity\"] = new_launch_df[\"quantity\"].fillna(0).astype(int)\n", "\n", "new_launch_df[\"fdate\"] = list(\n", "    map(\n", "        lambda x, y: pd.date_range(start=x, end=y),\n", "        new_launch_df[\"start_date\"],\n", "        new_launch_df[\"end_date\"],\n", "    )\n", ")\n", "\n", "new_launch_df = new_launch_df.explode(\"fdate\").drop([\"start_date\", \"end_date\"], axis=1)\n", "\n", "new_launch_df[\"fdate\"] = pd.to_datetime(new_launch_df[\"fdate\"])\n", "\n", "new_launch_df = new_launch_df.reset_index()\n", "\n", "max_df = (\n", "    new_launch_df.groupby([\"be_outlet_id\", \"item_id\", \"fdate\"]).agg({\"index\": \"max\"}).reset_index()\n", ")\n", "\n", "new_launch_df = new_launch_df.merge(\n", "    max_df, on=[\"be_outlet_id\", \"item_id\", \"fdate\", \"index\"], how=\"inner\"\n", ").drop(columns={\"index\"})\n", "\n", "new_launch_df = new_launch_df[\n", "    (\n", "        new_launch_df[\"fdate\"]\n", "        >= (pd.to_datetime(today_date) + timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "    )\n", "    & (\n", "        new_launch_df[\"fdate\"]\n", "        <= (pd.to_datetime(today_date) + timedelta(days=7)).strftime(\"%Y-%m-%d\")\n", "    )\n", "]\n", "\n", "new_launch_df = new_launch_df.merge(\n", "    assortment_df[\n", "        [\n", "            \"city\",\n", "            \"be_facility_id\",\n", "            \"be_outlet_id\",\n", "            \"item_id\",\n", "            \"outlet_id\",\n", "            \"facility_id\",\n", "            \"ptype\",\n", "        ]\n", "    ].drop_duplicates(),\n", "    on=[\"be_outlet_id\", \"item_id\"],\n", "    how=\"inner\",\n", ")\n", "\n", "fac_carts_df = carts_df.groupby([\"outlet_id\", \"date_\"]).agg({\"fac_carts\": \"sum\"}).reset_index()\n", "\n", "fac_carts_df = fac_carts_df.groupby([\"outlet_id\"]).agg({\"fac_carts\": percentile}).reset_index()\n", "\n", "fac_carts_df[\"fac_carts\"] = fac_carts_df[\"fac_carts\"].astype(int)\n", "\n", "fac_carts_df = pd.merge(\n", "    fac_carts_df,\n", "    assortment_df[[\"outlet_id\", \"be_facility_id\"]].drop_duplicates(),\n", "    on=[\"outlet_id\"],\n", "    how=\"inner\",\n", ")\n", "\n", "agg_fac_carts_df = (\n", "    fac_carts_df.groupby([\"be_facility_id\"])\n", "    .agg({\"fac_carts\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"fac_carts\": \"tot_fac_carts\"})\n", ")\n", "\n", "fac_carts_df = pd.merge(fac_carts_df, agg_fac_carts_df, on=[\"be_facility_id\"], how=\"left\")\n", "\n", "fac_carts_df[\"carts_contri\"] = fac_carts_df[\"fac_carts\"] / fac_carts_df[\"tot_fac_carts\"]\n", "\n", "fac_carts_df = (\n", "    fac_carts_df[[\"be_facility_id\", \"outlet_id\", \"carts_contri\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "new_launch_df = pd.merge(\n", "    new_launch_df, fac_carts_df, on=[\"be_facility_id\", \"outlet_id\"], how=\"left\"\n", ")\n", "\n", "new_launch_df[\"new_sku_fps\"] = new_launch_df[\"quantity\"] * new_launch_df[\"carts_contri\"]\n", "\n", "new_launch_df[\"new_sku_fps\"] = np.where(\n", "    new_launch_df[\"new_sku_fps\"] < 1, 1, np.round(new_launch_df[\"new_sku_fps\"])\n", ")\n", "\n", "new_launch_df[\"category\"] = \"Breads\"\n", "\n", "new_launch_df[\"stype_\"] = \"bottom\"\n", "\n", "new_launch_df[\"flag\"] = 1\n", "\n", "new_launch_df = pd.merge(\n", "    new_launch_df,\n", "    forecast_logs_df[[\"facility_id\", \"ftype_\", \"fdate\"]].drop_duplicates(),\n", "    on=[\"facility_id\", \"fdate\"],\n", "    how=\"left\",\n", ")\n", "\n", "new_launch_df = pd.merge(\n", "    new_launch_df,\n", "    forecast_logs_df[[\"city\", \"category\", \"ptype\", \"fdate\", \"city_l2_factor\"]].drop_duplicates(),\n", "    on=[\"city\", \"category\", \"ptype\", \"fdate\"],\n", "    how=\"left\",\n", ")\n", "\n", "new_launch_df = pd.merge(\n", "    new_launch_df,\n", "    forecast_logs_df[[\"be_facility_id\", \"fdate\", \"be_factor\"]].drop_duplicates(),\n", "    on=[\"be_facility_id\", \"fdate\"],\n", "    how=\"left\",\n", ")\n", "\n", "new_launch_df = (\n", "    new_launch_df[\n", "        [\n", "            \"city\",\n", "            \"be_facility_id\",\n", "            \"category\",\n", "            \"ptype\",\n", "            \"facility_id\",\n", "            \"outlet_id\",\n", "            \"ftype_\",\n", "            \"item_id\",\n", "            \"stype_\",\n", "            \"fdate\",\n", "            \"flag\",\n", "            \"be_factor\",\n", "            \"city_l2_factor\",\n", "            \"new_sku_fps\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "print(new_launch_df.shape)\n", "new_launch_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "5328b457-9857-49c8-bf2f-f04ae8820863", "metadata": {}, "outputs": [], "source": ["forecast_logs_df = pd.merge(\n", "    forecast_logs_df,\n", "    new_launch_df[[\"outlet_id\", \"item_id\", \"fdate\", \"new_sku_fps\"]].drop_duplicates(),\n", "    on=[\"outlet_id\", \"item_id\", \"fdate\"],\n", "    how=\"left\",\n", ")\n", "\n", "forecast_logs_df = forecast_logs_df[forecast_logs_df[\"new_sku_fps\"].isna()].drop(\n", "    columns={\"new_sku_fps\"}\n", ")\n", "\n", "forecast_logs_df = pd.concat(\n", "    [forecast_logs_df, new_launch_df.rename(columns={\"new_sku_fps\": \"final_ex_qty\"})]\n", ")\n", "\n", "forecast_logs_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "0dba1ad8-fd2e-463e-8468-c41b1c59e75a", "metadata": {}, "outputs": [], "source": ["del (new_launch_df, max_df, fac_carts_df, agg_fac_carts_df)\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "3bd3e770-2f32-4d24-9893-5ef0a1fd4137", "metadata": {}, "source": ["### Replicate SKU"]}, {"cell_type": "code", "execution_count": null, "id": "ff32625d-2a9a-47a0-963b-8e0c002fa641", "metadata": {}, "outputs": [], "source": ["replicate_df = new_sku_input_df[\n", "    new_sku_input_df[\"new_item_id\"] != new_sku_input_df[\"replicate_item_id\"]\n", "].reset_index()\n", "replicate_df = replicate_df.drop(columns={\"index\", \"quantity\"}).rename(\n", "    columns={\"new_item_id\": \"item_id\"}\n", ")\n", "\n", "replicate_df[[\"item_id\", \"replicate_item_id\"]] = replicate_df[\n", "    [\"item_id\", \"replicate_item_id\"]\n", "].astype(int)\n", "\n", "replicate_df[\"factor\"] = replicate_df[\"factor\"].fillna(0).astype(float)\n", "\n", "replicate_df[\"fdate\"] = list(\n", "    map(\n", "        lambda x, y: pd.date_range(start=x, end=y),\n", "        replicate_df[\"start_date\"],\n", "        replicate_df[\"end_date\"],\n", "    )\n", ")\n", "\n", "replicate_df = replicate_df.explode(\"fdate\").drop([\"start_date\", \"end_date\"], axis=1)\n", "\n", "replicate_df[\"fdate\"] = pd.to_datetime(replicate_df[\"fdate\"])\n", "\n", "replicate_df = replicate_df.reset_index()\n", "\n", "max_df = (\n", "    replicate_df.groupby([\"be_outlet_id\", \"item_id\", \"fdate\"]).agg({\"index\": \"max\"}).reset_index()\n", ")\n", "\n", "replicate_df = replicate_df.merge(\n", "    max_df, on=[\"be_outlet_id\", \"item_id\", \"fdate\", \"index\"], how=\"inner\"\n", ").drop(columns={\"index\"})\n", "\n", "replicate_df = replicate_df[\n", "    (replicate_df[\"fdate\"] >= (pd.to_datetime(today_date) + timedelta(days=1)).strftime(\"%Y-%m-%d\"))\n", "    & (\n", "        replicate_df[\"fdate\"]\n", "        <= (pd.to_datetime(today_date) + timedelta(days=7)).strftime(\"%Y-%m-%d\")\n", "    )\n", "]\n", "\n", "replicate_df = replicate_df.merge(\n", "    assortment_df[\n", "        [\n", "            \"city\",\n", "            \"be_facility_id\",\n", "            \"be_outlet_id\",\n", "            \"item_id\",\n", "            \"outlet_id\",\n", "            \"facility_id\",\n", "        ]\n", "    ].drop_duplicates(),\n", "    on=[\"be_outlet_id\", \"item_id\"],\n", "    how=\"inner\",\n", ")\n", "\n", "replicate_df = replicate_df.merge(\n", "    forecast_logs_df[[\"outlet_id\", \"item_id\", \"ptype\", \"fdate\", \"final_ex_qty\", \"ftype_\"]]\n", "    .drop_duplicates()\n", "    .rename(columns={\"item_id\": \"replicate_item_id\"}),\n", "    on=[\"outlet_id\", \"replicate_item_id\", \"fdate\"],\n", "    how=\"inner\",\n", ")\n", "\n", "replicate_df[\"replicate_sku_fps\"] = replicate_df[\"final_ex_qty\"] * replicate_df[\"factor\"]\n", "\n", "replicate_df[\"category\"] = \"Breads\"\n", "\n", "replicate_df[\"stype_\"] = \"bottom\"\n", "\n", "replicate_df[\"flag\"] = 1\n", "\n", "replicate_df = pd.merge(\n", "    replicate_df,\n", "    forecast_logs_df[\n", "        [\"city\", \"category\", \"ptype\", \"stype_\", \"fdate\", \"city_l2_factor\"]\n", "    ].drop_duplicates(),\n", "    on=[\"city\", \"category\", \"ptype\", \"stype_\", \"fdate\"],\n", "    how=\"left\",\n", ")\n", "\n", "replicate_df = pd.merge(\n", "    replicate_df,\n", "    forecast_logs_df[[\"be_facility_id\", \"fdate\", \"be_factor\"]].drop_duplicates(),\n", "    on=[\"be_facility_id\", \"fdate\"],\n", "    how=\"left\",\n", ")\n", "\n", "replicate_df = (\n", "    replicate_df[\n", "        [\n", "            \"city\",\n", "            \"be_facility_id\",\n", "            \"category\",\n", "            \"ptype\",\n", "            \"facility_id\",\n", "            \"outlet_id\",\n", "            \"ftype_\",\n", "            \"item_id\",\n", "            \"stype_\",\n", "            \"fdate\",\n", "            \"flag\",\n", "            \"be_factor\",\n", "            \"city_l2_factor\",\n", "            \"replicate_sku_fps\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "replicate_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "5a2f0f78-0cae-4e41-a662-f86aea9678c9", "metadata": {}, "outputs": [], "source": ["forecast_logs_df = pd.merge(\n", "    forecast_logs_df,\n", "    replicate_df[[\"outlet_id\", \"item_id\", \"fdate\", \"replicate_sku_fps\"]].drop_duplicates(),\n", "    on=[\"outlet_id\", \"item_id\", \"fdate\"],\n", "    how=\"left\",\n", ")\n", "\n", "forecast_logs_df = forecast_logs_df[forecast_logs_df[\"replicate_sku_fps\"].isna()].drop(\n", "    columns={\"replicate_sku_fps\"}\n", ")\n", "\n", "forecast_logs_df = pd.concat(\n", "    [\n", "        forecast_logs_df,\n", "        replicate_df.rename(columns={\"replicate_sku_fps\": \"final_ex_qty\"}),\n", "    ]\n", ")\n", "\n", "forecast_logs_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "7b6db3f7-33ee-46a2-a88d-75ebc109a818", "metadata": {}, "outputs": [], "source": ["del (replicate_df, max_df)\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "59d44826-6ad7-4f34-97aa-8dc0f86d202d", "metadata": {}, "source": ["## Upload Frame"]}, {"cell_type": "code", "execution_count": null, "id": "d65f1f30-4929-4f70-a194-f87f17fc3425", "metadata": {}, "outputs": [], "source": ["upload_df = forecast_logs_df.copy()\n", "\n", "upload_df[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))\n", "\n", "upload_df[\"date_ist\"] = pd.to_datetime(upload_df[\"updated_at\"]).dt.strftime(\"%Y-%m-%d\")\n", "\n", "upload_df[\"date_ist\"] = pd.to_datetime(upload_df[\"date_ist\"])\n", "\n", "upload_df = upload_df[\n", "    [\n", "        \"city\",\n", "        \"be_facility_id\",\n", "        \"category\",\n", "        \"facility_id\",\n", "        \"outlet_id\",\n", "        \"ftype_\",\n", "        \"item_id\",\n", "        \"stype_\",\n", "        \"fdate\",\n", "        \"flag\",\n", "        \"be_factor\",\n", "        \"city_l2_factor\",\n", "        \"final_ex_qty\",\n", "        \"date_ist\",\n", "        \"updated_at\",\n", "    ]\n", "]\n", "\n", "upload_df[\"final_ex_qty\"] = np.where(upload_df[\"final_ex_qty\"] < 1, 1, upload_df[\"final_ex_qty\"])\n", "\n", "upload_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "d526b9e3-8a37-484e-bb0d-f18dfd3f47b6", "metadata": {}, "outputs": [], "source": ["upload_df[\"final_ex_qty\"].sum()"]}, {"cell_type": "markdown", "id": "98ec48a8-23e2-4557-b6c7-1b4f9c384dfb", "metadata": {}, "source": ["## New Forecast upload"]}, {"cell_type": "code", "execution_count": null, "id": "706688f0-0763-4665-bb7c-91b1b006d791", "metadata": {}, "outputs": [], "source": ["new_forecat_query = f\"\"\"\n", "with be_mapping AS (\n", "SELECT\n", "    cb.id AS be_outlet_id,\n", "    cb.facility_id AS be_facility_id,\n", "    cb.name AS be_outlet_name,\n", "    outlet_id,\n", "    item_id\n", "FROM rpc.item_outlet_tag_mapping tm\n", "JOIN retail.console_outlet cb \n", "    ON cb.id = CAST(tag_value AS int) \n", "    AND cb.active = 1 \n", "    AND cb.lake_active_record\n", "WHERE tm.active = 1\n", "    AND tm.tag_type_id = 8\n", "    AND tm.lake_active_record\n", "group by 1,2,3,4,5\n", "),\n", "final as (\n", "select\n", "    date(date_) as fdate,\n", "    be_facility_id,\n", "    f.outlet_id,\n", "    f.item_id,\n", "    forecast as new_forecast_qty\n", "from supply_etls.testing_revamped_perishable_forecast f\n", "join be_mapping m\n", "    on m.outlet_id=f.outlet_id\n", "    and m.item_id=f.item_id\n", "join rpc.item_category_details icd\n", "on icd.item_id=f.item_id\n", "and icd.l2_id in (198)\n", "where f.updated_at_ist in (select max(updated_at_ist) from supply_etls.testing_revamped_perishable_forecast)\n", "and be_facility_id=2079\n", ")\n", "select\n", "    *\n", "from\n", "    final\n", "\"\"\"\n", "new_forecat_df = read_sql_query(new_forecat_query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "53e583fd-7fa3-444b-b909-626c8ab6f010", "metadata": {}, "outputs": [], "source": ["new_forecat_df[\"fdate\"] = pd.to_datetime(new_forecat_df[\"fdate\"])"]}, {"cell_type": "code", "execution_count": null, "id": "d5dfd33d-1da7-406d-9ae5-35e13f097330", "metadata": {}, "outputs": [], "source": ["check_df = upload_df.merge(\n", "    new_forecat_df, on=[\"fdate\", \"be_facility_id\", \"outlet_id\", \"item_id\"], how=\"inner\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "185cafd4-bf9c-490c-a7bb-7cf40e1e7e10", "metadata": {}, "outputs": [], "source": ["upload_df = upload_df.merge(\n", "    new_forecat_df, on=[\"fdate\", \"be_facility_id\", \"outlet_id\", \"item_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "92234f97-f039-47e7-9288-9137db3e6a85", "metadata": {}, "outputs": [], "source": ["upload_df[\"final_ex_qty\"] = np.where(\n", "    upload_df[\"new_forecast_qty\"].isna(), upload_df[\"final_ex_qty\"], upload_df[\"new_forecast_qty\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f02382b7-f13e-478f-bfb1-2d0f82742278", "metadata": {}, "outputs": [], "source": ["## checks\n", "print(\n", "    upload_df[~upload_df[\"new_forecast_qty\"].isna()][[\"final_ex_qty\", \"new_forecast_qty\"]]\n", "    .sum()\n", "    .astype(int)\n", ")\n", "print(\n", "    check_df[~check_df[\"new_forecast_qty\"].isna()][[\"final_ex_qty\", \"new_forecast_qty\"]]\n", "    .sum()\n", "    .astype(int)\n", ")\n", "print(upload_df[\"final_ex_qty\"].sum())"]}, {"cell_type": "code", "execution_count": null, "id": "d2a053ba-2938-4c4b-a192-8385b4dc0d31", "metadata": {}, "outputs": [], "source": ["upload_df.drop(columns=[\"new_forecast_qty\"], inplace=True)"]}, {"cell_type": "markdown", "id": "725721fb-3610-44c3-aa57-ebb5a83ddc6b", "metadata": {}, "source": ["# Trino"]}, {"cell_type": "code", "execution_count": null, "id": "855e4f12-dd06-4217-a206-fd4ac2d505c0", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"city\", \"type\": \"VARCHAR\", \"description\": \"city\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"INTEGER\", \"description\": \"backend facility id\"},\n", "    {\"name\": \"category\", \"type\": \"VARCHAR\", \"description\": \"l2 category\"},\n", "    {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"facility id\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"outlet id\"},\n", "    {\"name\": \"ftype_\", \"type\": \"VARCHAR\", \"description\": \"facility categorization\"},\n", "    {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"item id\"},\n", "    {\"name\": \"stype_\", \"type\": \"VARCHAR\", \"description\": \"SKU categorization\"},\n", "    {\"name\": \"fdate\", \"type\": \"DATE\", \"description\": \"forecast date\"},\n", "    {\"name\": \"flag\", \"type\": \"INTEGER\", \"description\": \"new logic flag\"},\n", "    {\"name\": \"be_factor\", \"type\": \"DOUBLE\", \"description\": \"new logic flag\"},\n", "    {\"name\": \"city_l2_factor\", \"type\": \"DOUBLE\", \"description\": \"new logic flag\"},\n", "    {\"name\": \"final_ex_qty\", \"type\": \"DOUBLE\", \"description\": \"forecast cpd\"},\n", "    {\"name\": \"date_ist\", \"type\": \"DATE\", \"description\": \"date of run\"},\n", "    {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(6)\", \"description\": \"datetime of run\"},\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"breads_forecast_cpd_logs\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"facility_id\", \"item_id\", \"fdate\"],\n", "    \"partition_key\": [\"date_ist\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"truncate\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This table contains perishable forecast working steps\",\n", "}\n", "pb.to_trino(upload_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "7ca7c302-ac94-47ce-8816-b3557dfa27a5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}