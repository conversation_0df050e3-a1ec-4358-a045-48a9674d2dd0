{"cells": [{"cell_type": "code", "execution_count": null, "id": "845dd954-ba69-47c4-bbd8-105d691d6ff4", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib\n", "!pip install pandas openpyxl\n", "!pip install numpy==1.26.4\n", "import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "import datetime\n", "from datetime import date, datetime, timedelta\n", "\n", "from datetime import datetime\n", "from matplotlib import pyplot as plt\n", "\n", "# CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "\n", "# SQL Connection\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "395147c3-ef58-45ab-a03f-3b25aa04e00e", "metadata": {}, "outputs": [], "source": ["l2_id = [\n", "    1425,\n", "    31,\n", "    116,\n", "    198,\n", "    1097,\n", "    1956,\n", "    949,\n", "    1389,\n", "    1778,\n", "    950,\n", "    138,\n", "    1091,\n", "    1093,\n", "    1094,\n", "    2633,\n", "    63,\n", "    1367,\n", "    1369,\n", "    1429,\n", "    1185,\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "ef17c5bb-8c46-4695-baa2-5be751b53e33", "metadata": {}, "outputs": [], "source": ["sql = f\"\"\"\n", "with conversion as (\n", "select \n", "    date(at_date_ist) as date_,\n", "    keyword,\n", "    cl.name as city,\n", "    sum(search_dau) searches_\n", "from \n", "    dwh.agg_hourly_search_keyword_conversion_metrics a\n", "inner join  \n", "    dwh.dim_merchant_outlet_facility_mapping fm\n", "    on a.merchant_id = fm.frontend_merchant_id\n", "    and fm.is_current = true \n", "    and fm.is_mapping_enabled = true \n", "    and fm.is_frontend_merchant_active = true \n", "    and fm.is_backend_merchant_active = true \n", "    and fm.is_pos_outlet_active = 1\n", "    and fm.is_current_mapping_active = true\n", "inner join\n", "    retail.console_outlet co\n", "    on co.id = fm.pos_outlet_id \n", "    and co.active = 1\n", "    and co.lake_active_record = true\n", "    and co.business_type_id = 7\n", "inner join\n", "    retail.console_location cl\n", "    on cl.id = co.tax_location_id\n", "    and cl.lake_active_record = true\n", "where \n", "    at_date_ist between current_date - interval '70' day and current_date - interval '1' day\n", "    and city_name  <> 'Overall'\n", "    and platform in ('android','ios')\n", "    and merchant_name <> 'Overall'\n", "    and keyword <> 'Overall'\n", "group by 1,2,3\n", "),\n", "\n", "l2_mapping as (\n", "select \n", "    keyword , l2_category, l2_category_id\n", "from \n", "    dwh.dim_keywords_l2_mapping \n", "where \n", "    is_current\n", "    and l2_category_id in {tuple(l2_id)}\n", "group by 1,2,3\n", "),\n", "\n", "searches as (\n", "select\n", "    date_,\n", "    city,\n", "    l2_category as l2,\n", "    l2_category_id,\n", "    sum(searches_) as searches\n", "from\n", "    conversion c\n", "join \n", "    l2_mapping m\n", "    on m.keyword=c.keyword\n", "group by 1,2,3,4\n", "),\n", "\n", "searches_final as (\n", "select\n", "    sb.date_,\n", "    sb.city city_name,\n", "    sb.l2_category_id,\n", "    sum(searches) searches\n", "from\n", "    searches sb\n", "group by 1,2,3\n", "),\n", "\n", "sales as (\n", "SELECT\n", "    date(o.order_create_dt_ist) as date_,\n", "    cl.name as city_name,\n", "    dp.l2_category,\n", "    dp.l2_category_id,\n", "    sum(o.procured_quantity) as qty_sold\n", "FROM\n", "    dwh.fact_sales_order_item_details o\n", "inner join\n", "    retail.console_outlet co\n", "    on co.id = o.outlet_id \n", "    and co.active = 1\n", "    and co.lake_active_record = true\n", "    and co.business_type_id = 7\n", "inner join\n", "    retail.console_location cl\n", "    on cl.id = co.tax_location_id\n", "    and cl.lake_active_record = true\n", "inner join\n", "    dwh.dim_product dp\n", "    on o.product_id = dp.product_id\n", "    and dp.is_current\n", "    and dp.l2_category_id in {tuple(l2_id)}\n", " WHERE\n", "    o.order_current_status = 'DELIVERED'\n", "    and o.order_create_dt_ist >= current_date - interval '70' day\n", "    and o.order_create_dt_ist < current_date\n", "    and o.is_internal_order = False\n", "    and extract(hour from o.order_create_ts_ist) <= 15\n", "GROUP BY 1,2,3,4\n", "),\n", "\n", "sales_final as (\n", "select \n", "    date_,\n", "    city_name,\n", "    l2_category_id,\n", "    sum(qty_sold) qty_sold\n", "from \n", "    sales\n", "group by 1,2,3\n", ")\n", "\n", "select \n", "    s.date_,\n", "    s.city_name,\n", "    s.l2_category_id,\n", "    s.qty_sold,\n", "    f.searches\n", "from \n", "    sales_final s\n", "left join\n", "    searches_final f\n", "    on s.date_ = f.date_\n", "    and s.city_name = f.city_name\n", "    and s.l2_category_id = f.l2_category_id\n", "where \n", "    qty_sold > 0\n", "    and searches > 0\n", "\"\"\"\n", "df = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "markdown", "id": "d0662618-f42a-4bb0-9faf-d9a8dfff9f0e", "metadata": {}, "source": ["## Appending Pan-India Data"]}, {"cell_type": "code", "execution_count": null, "id": "a22ed65d-bc6c-411d-876d-5f797ba905ad", "metadata": {}, "outputs": [], "source": ["pan_india_df = df.copy()\n", "pan_india_df[\"city_name\"] = \"Pan_India\"\n", "\n", "df_grouped_pan_india = pan_india_df.groupby(\n", "    [\"date_\", \"city_name\", \"l2_category_id\"], as_index=False\n", ")[[\"qty_sold\", \"searches\"]].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "a61d0a3a-82aa-4478-8339-19ea8ddd3981", "metadata": {}, "outputs": [], "source": ["df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "531f0de7-7b92-43df-acab-97e0a4c8cbc2", "metadata": {}, "outputs": [], "source": ["df_grouped_pan_india.shape"]}, {"cell_type": "code", "execution_count": null, "id": "546b403b-f890-4006-9ec6-c35430ece35d", "metadata": {}, "outputs": [], "source": ["base_df = pd.concat([df, df_grouped_pan_india], axis=0, ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "54709e90-615c-453c-bd7f-3d4f55001afb", "metadata": {}, "outputs": [], "source": ["base_df.shape"]}, {"cell_type": "markdown", "id": "ca19fbeb-41eb-4419-8fe3-7896dc3a6c87", "metadata": {}, "source": ["## Data Processing"]}, {"cell_type": "code", "execution_count": null, "id": "ffa41f56-0352-4452-88a8-f32a24f59ada", "metadata": {}, "outputs": [], "source": ["# Ensure date column is datetime\n", "base_df[\"date_\"] = pd.to_datetime(base_df[\"date_\"])\n", "\n", "# Day of Week (full name like Monday, Tuesday)\n", "base_df[\"dow\"] = base_df[\"date_\"].dt.day_name()\n", "\n", "# Week of Year (ISO week number)\n", "base_df[\"woy\"] = base_df[\"date_\"].dt.isocalendar().week"]}, {"cell_type": "code", "execution_count": null, "id": "c671e0c0-d074-4f93-8142-cba908912f86", "metadata": {}, "outputs": [], "source": ["base_df"]}, {"cell_type": "markdown", "id": "b13c7ce5-7ff1-4ae7-af98-6dcbc5137a2a", "metadata": {}, "source": ["### Filtering the Weeks which has all 7 days of data"]}, {"cell_type": "code", "execution_count": null, "id": "09142329-9a68-40d3-8131-89ab19c437d1", "metadata": {}, "outputs": [], "source": ["# Step 1: Identify valid (city, category, week) groups that have all 7 DOWs\n", "valid_weeks = (\n", "    base_df.groupby([\"city_name\", \"l2_category_id\", \"woy\"])[\"dow\"]\n", "    .nunique()\n", "    .reset_index()\n", "    .query(\"dow == 7\")  # Only groups with all 7 days\n", "    .rename(columns={\"dow\": \"unique_dow_count\"})\n", ")\n", "\n", "# Step 2: Do an inner merge on all three keys to ensure exact match\n", "df_filtered = base_df.merge(\n", "    valid_weeks[[\"city_name\", \"l2_category_id\", \"woy\"]],\n", "    on=[\"city_name\", \"l2_category_id\", \"woy\"],\n", "    how=\"inner\",\n", ")"]}, {"cell_type": "markdown", "id": "8a7ff756-cd0a-4c33-9866-0815ad1e495f", "metadata": {}, "source": ["### Filtering Cities x l2 which has minimum 8 weeks of data"]}, {"cell_type": "code", "execution_count": null, "id": "9d0499e2-da1f-4ca5-b6be-64f0ecbf2021", "metadata": {}, "outputs": [], "source": ["# Step 3: Count distinct weeks per (city, l2_category_id)\n", "valid_city_l2 = (\n", "    df_filtered.groupby([\"city_name\", \"l2_category_id\"])[\"woy\"]\n", "    .nunique()\n", "    .reset_index()\n", "    .query(\"woy >= 8\")  # This now means: \"at least 8 unique weeks of data\"\n", "    .rename(columns={\"woy\": \"unique_week_count\"})\n", ")\n", "\n", "# Step 4: Filter original df to keep only these valid combinations\n", "df_final = df_filtered.merge(\n", "    valid_city_l2[[\"city_name\", \"l2_category_id\"]], on=[\"city_name\", \"l2_category_id\"], how=\"inner\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d0422d66-d35a-4989-aa5a-1c417cf2ae6c", "metadata": {}, "outputs": [], "source": ["df_final"]}, {"cell_type": "markdown", "id": "9cd349df-9961-42a4-9611-0b9c39fabc28", "metadata": {}, "source": ["### Taking median value of all the Day of the week."]}, {"cell_type": "code", "execution_count": null, "id": "da31b4eb-abe5-409f-8e19-a42e76949669", "metadata": {}, "outputs": [], "source": ["median_df = (\n", "    df_final.groupby([\"city_name\", \"l2_category_id\", \"dow\"])[[\"qty_sold\", \"searches\"]]\n", "    .median()\n", "    .reset_index()\n", ")"]}, {"cell_type": "markdown", "id": "dfe15445-e7fa-40dc-a8c5-514fee7e12e8", "metadata": {}, "source": ["### Taking cross join to calculate normalization factor"]}, {"cell_type": "code", "execution_count": null, "id": "beb1d700-861e-452a-9a85-34d6a20359c0", "metadata": {}, "outputs": [], "source": ["final_cross_join_df = median_df.merge(\n", "    median_df, on=[\"city_name\", \"l2_category_id\"], how=\"left\", suffixes=(\"_to\", \"_from\")\n", ")\n", "\n", "final_cross_join_df[\"qty_sold_factor\"] = (\n", "    final_cross_join_df[\"qty_sold_to\"] / final_cross_join_df[\"qty_sold_from\"]\n", ").round(2)\n", "\n", "final_cross_join_df[\"searches_factor\"] = (\n", "    final_cross_join_df[\"searches_to\"] / final_cross_join_df[\"searches_from\"]\n", ").round(2)"]}, {"cell_type": "code", "execution_count": null, "id": "2ca9185e-8110-4b27-ad28-2bd7f5486cab", "metadata": {}, "outputs": [], "source": ["final_cross_join_df"]}, {"cell_type": "markdown", "id": "51c42278-663e-4afe-8850-de3613819042", "metadata": {}, "source": ["### Replacing Monday, Tuesday with values"]}, {"cell_type": "code", "execution_count": null, "id": "cbb2c045-0248-4d55-9922-bd2e36ec4dd0", "metadata": {}, "outputs": [], "source": ["# Step 1: Define mapping from day names to integers\n", "dow_map = {\n", "    \"Monday\": 0,\n", "    \"Tuesday\": 1,\n", "    \"Wednesday\": 2,\n", "    \"Thursday\": 3,\n", "    \"Friday\": 4,\n", "    \"Saturday\": 5,\n", "    \"Sunday\": 6,\n", "}\n", "\n", "# Step 2: Apply the mapping to both columns\n", "final_cross_join_df[\"dow_from\"] = final_cross_join_df[\"dow_from\"].map(dow_map)\n", "final_cross_join_df[\"dow_to\"] = final_cross_join_df[\"dow_to\"].map(dow_map)"]}, {"cell_type": "code", "execution_count": null, "id": "491eca5a-ebb5-41da-9353-5db0e64d6b5a", "metadata": {}, "outputs": [], "source": ["final_cross_join_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "11541780-009e-4070-a8af-393af354e594", "metadata": {}, "outputs": [], "source": ["final_cross_join_df[\"updated_at\"] = datetime.today() + timed<PERSON>ta(hours=5.5)\n", "final_cross_join_df[\"updated_at\"] = pd.to_datetime(final_cross_join_df[\"updated_at\"])"]}, {"cell_type": "code", "execution_count": null, "id": "e77a6317-30ac-4730-adbb-3115fb735ad4", "metadata": {}, "outputs": [], "source": ["final_cross_join_df = final_cross_join_df.astype(\n", "    {\n", "        \"city_name\": \"object\",\n", "        \"l2_category_id\": \"int64\",\n", "        \"dow_to\": \"int64\",\n", "        \"dow_from\": \"int64\",\n", "        # Convert float columns to int as requested\n", "        \"qty_sold_to\": \"int64\",\n", "        \"searches_to\": \"int64\",\n", "        \"qty_sold_from\": \"int64\",\n", "        \"searches_from\": \"int64\",\n", "        # Keep factors as float\n", "        \"qty_sold_factor\": \"float64\",\n", "        \"searches_factor\": \"float64\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a99cd337-7aa8-452b-8656-20123fb10a1f", "metadata": {}, "outputs": [], "source": ["final_cross_join_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "8714ab60-8729-428b-a5a2-8a32cf316341", "metadata": {}, "outputs": [], "source": ["final_cross_join_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "797a4248-8a21-4e08-b3f2-b21304a9cf2d", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"city_name\"},\n", "    {\"name\": \"l2_category_id\", \"type\": \"integer\", \"description\": \"l2_category_id\"},\n", "    {\"name\": \"dow_to\", \"type\": \"integer\", \"description\": \"dow_to\"},\n", "    {\"name\": \"qty_sold_to\", \"type\": \"integer\", \"description\": \"qty_sold_to\"},\n", "    {\"name\": \"searches_to\", \"type\": \"integer\", \"description\": \"searches_to\"},\n", "    {\"name\": \"dow_from\", \"type\": \"integer\", \"description\": \"dow_from\"},\n", "    {\"name\": \"qty_sold_from\", \"type\": \"integer\", \"description\": \"qty_sold_from\"},\n", "    {\"name\": \"searches_from\", \"type\": \"integer\", \"description\": \"searches_from\"},\n", "    {\"name\": \"qty_sold_factor\", \"type\": \"real\", \"description\": \"qty_sold_factor\"},\n", "    {\"name\": \"searches_factor\", \"type\": \"real\", \"description\": \"searches_factor\"},\n", "    {\"name\": \"updated_at\", \"type\": \"timestamp(6)\", \"description\": \"updated_at\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "16bf9215-753a-4676-8d40-cd6f594c9f01", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"perishable_dow_normalization\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"city_name\", \"l2_category_id\", \"dow_to\", \"dow_from\"],\n", "    # \"partition_key\": [\"city_name\"],\n", "    # \"incremental_key\": \"date_\",\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"Perishable Normalization Factor\",\n", "}\n", "\n", "pb.to_trino(final_cross_join_df, **kwargs)\n", "\n", "channel = \"table-updates-tanishq\"\n", "text_req = \"\\n <@U05CCTXLBU1> \\n Perishable Normalization Factors are Updated\"\n", "pb.send_slack_message(\n", "    channel=channel,\n", "    text=text_req,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "99dc2430-2e01-49bc-9455-0666d1c8c3bd", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}