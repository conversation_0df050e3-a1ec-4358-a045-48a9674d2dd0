{"cells": [{"cell_type": "code", "execution_count": null, "id": "e3e6144a-e4c6-4e55-bc23-90ce6471cf0b", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib\n", "!pip install pandas openpyxl\n", "!pip install numpy==1.26.4\n", "import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "import datetime\n", "\n", "\n", "from datetime import datetime\n", "from matplotlib import pyplot as plt\n", "\n", "# CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "# SQL Connection\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "7557586f-affe-446a-a883-edc9f3c8e40f", "metadata": {}, "outputs": [], "source": ["l2_id = [\n", "    1425,\n", "    31,\n", "    116,\n", "    198,\n", "    1097,\n", "    1956,\n", "    949,\n", "    1389,\n", "    1778,\n", "    950,\n", "    138,\n", "    1091,\n", "    1093,\n", "    1094,\n", "    2633,\n", "    63,\n", "    1367,\n", "    1369,\n", "]"]}, {"cell_type": "markdown", "id": "b8805089-4a95-409d-9f49-37d95c2cbd6d", "metadata": {}, "source": ["## Universe"]}, {"cell_type": "code", "execution_count": null, "id": "9de424bf-681b-4372-bb94-cb5ad1d9d517", "metadata": {}, "outputs": [], "source": ["sql = f\"\"\"\n", "\n", "with live_stores as(\n", "select\n", "    o.outlet_id \n", "from\n", "    dwh.fact_sales_order_details o\n", "where\n", "    order_current_status = 'DELIVERED'\n", "    and is_internal_order = False \n", "    and order_create_dt_ist between current_date - interval '7' day and current_date\n", "group by 1\n", "),\n", "\n", "dates as(\n", "select date as date_ from dwh.dim_date where date >= current_date - interval '1' day and date < current_date group by 1\n", "),\n", "\n", "universe as (\n", "select\n", "    d.date_,\n", "    pfma.facility_id,\n", "    po.outlet_id,\n", "    po.outlet_name as store_name,\n", "    ic.l0,\n", "    ic.l1,\n", "    ic.l2,\n", "    ic.product_type,\n", "    pfma.item_id,\n", "    pfma.master_assortment_substate_id,\n", "    ic.name,\n", "    tm.tag_value,\n", "    case when tm.tag_value = '0' then 'Direct to Store - No Backend' else om.outlet_name end outlet_name\n", "from\n", "    rpc.product_facility_master_assortment pfma\n", "cross join\n", "    dates d\n", "inner join\n", "    rpc.item_category_details ic\n", "    on ic.item_id = pfma.item_id\n", "    and ic.l2_id in {tuple(l2_id)}\n", "inner join\n", "    (\n", "    SELECT DISTINCT a.item_id\n", "        FROM rpc.product_product a\n", "        JOIN (\n", "            SELECT item_id, MAX(id) AS id\n", "            FROM rpc.product_product\n", "            WHERE approved = 1 AND active = 1\n", "            GROUP BY 1\n", "        ) b ON a.id = b.id AND a.item_id = b.item_id\n", "    WHERE a.active = 1 AND a.lake_active_record AND a.approved = 1 AND a.perishable = 1\n", "    ) ma ON ma.item_id = ic.item_id\n", "inner join\n", "    po.physical_facility_outlet_mapping po\n", "    on po.facility_id = pfma.facility_id\n", "    and po.active = 1\n", "    and po.ars_active = 1\n", "    and po.lake_active_record = true\n", "inner join\n", "    retail.console_outlet co\n", "    on co.id = po.outlet_id \n", "    and co.active = 1\n", "    and co.lake_active_record = true\n", "    and co.business_type_id = 7\n", "    and co.id in (select distinct outlet_id from live_stores)\n", "inner join   \n", "    rpc.item_outlet_tag_mapping tm\n", "    on pfma.item_id = tm.item_id \n", "    and po.outlet_id = tm.outlet_id\n", "    and tm.tag_type_id = 8 \n", "    and tm.active = 1\n", "left join\n", "    po.physical_facility_outlet_mapping om\n", "    on cast(tm.tag_value as int) =  om.outlet_id    \n", "    and om.active = 1\n", "    and om.ars_active = 1\n", "    and om.lake_active_record = true\n", "where\n", "    pfma.active = 1\n", "    and pfma.master_assortment_substate_id in (1,3)\n", ")\n", "\n", "select * from universe\n", "\n", "\"\"\"\n", "df1 = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "725a32a8-acf9-4efa-8ea7-8d12385eeeb6", "metadata": {}, "outputs": [], "source": ["df1[\"outlet_name\"] = df1[\"outlet_name\"].fillna(\"NA\")"]}, {"cell_type": "code", "execution_count": null, "id": "2edbe4a6-80f0-490d-8d23-74856a6df26b", "metadata": {}, "outputs": [], "source": ["universe = df1.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "e462abc7-4be4-4e84-a266-99750d74be75", "metadata": {}, "outputs": [], "source": ["a = universe.shape"]}, {"cell_type": "code", "execution_count": null, "id": "2e7b4dbe-0d11-434d-9245-ff471e393ceb", "metadata": {}, "outputs": [], "source": ["universe.facility_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "51d5e061-437c-40f8-b85e-03015adb8c53", "metadata": {}, "outputs": [], "source": ["universe.shape"]}, {"cell_type": "markdown", "id": "b21358ee-4b8a-48d8-9821-7358d27d6aa0", "metadata": {}, "source": ["## Sales"]}, {"cell_type": "code", "execution_count": null, "id": "c0479768-89ed-4729-8145-b85b51a8f9ed", "metadata": {}, "outputs": [], "source": ["sql = f\"\"\"\n", "with sales as (\n", "SELECT\n", "    date(o.order_create_dt_ist) as date_,\n", "    o.outlet_id,\n", "    ip.item_id,\n", "    sum(o.procured_quantity*ip.multiplier) as qty_sold\n", "FROM\n", "    dwh.fact_sales_order_item_details o\n", "inner join\n", "    dwh.dim_item_product_offer_mapping ip\n", "    on ip.product_id = o.product_id\n", "    and ip.is_current\n", "inner join\n", "    rpc.item_category_details rp\n", "    on rp.item_id = ip.item_id\n", "    and rp.l2_id in {tuple(l2_id)}\n", " WHERE\n", "    o.order_current_status = 'DELIVERED'\n", "    and o.order_create_dt_ist >= current_date - interval '1' day\n", "    and o.order_create_dt_ist < current_date\n", "    and o.is_internal_order = False\n", "GROUP BY 1,2,3\n", ")\n", "\n", "select * from sales\n", "\n", "\"\"\"\n", "sales_data = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "508908f4-cd9e-457e-9a02-2471f3d2aa2d", "metadata": {}, "outputs": [], "source": ["sales_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "96c7b89a-0443-499a-9205-c1ee4de54e8d", "metadata": {}, "outputs": [], "source": ["sales_data.shape"]}, {"cell_type": "markdown", "id": "9cf12d70-46dc-449c-a1dc-27350b5233bc", "metadata": {}, "source": ["## Pan India Level Hourly L2 Weights Calculation"]}, {"cell_type": "code", "execution_count": null, "id": "9d53197b-fb1b-4b0f-8d83-540eddf3b7b1", "metadata": {}, "outputs": [], "source": ["searches_df = f\"\"\"\n", "with keyword_searches as (\n", "select \n", "    date(at_date_ist) as date_,\n", "    at_hour_ist as hour_,\n", "    keyword,\n", "    city_name city,\n", "    sum(search_dau) searches_\n", "from \n", "    dwh.agg_hourly_search_keyword_conversion_metrics\n", "where \n", "    at_date_ist between current_date-interval'30'day and current_date-interval'1'day\n", "    and city_name  <> 'Overall'\n", "    and platform in ('android','ios')\n", "    and merchant_name <> 'Overall'\n", "    and keyword <> 'Overall'\n", "group by 1,2,3,4\n", "),\n", "\n", "l2_mapping as (\n", "select \n", "    keyword, l2_category, l2_category_id\n", "from \n", "    dwh.dim_keywords_l2_mapping \n", "where \n", "    is_current\n", "    and l2_category_id in {tuple(l2_id)}\n", "group by 1,2,3\n", ")\n", "\n", "select\n", "    date_,\n", "    hour_,\n", "    city,\n", "    l2_category as l2,\n", "    l2_category_id as l2_id,\n", "    sum(searches_) as searches\n", "from\n", "    keyword_searches ks\n", "join \n", "    l2_mapping m\n", "    on m.keyword=ks.keyword\n", "group by 1,2,3,4,5\n", "\n", "\"\"\"\n", "searches_df = read_sql_query(searches_df, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "d8b5fbc5-f9b5-4d9f-b915-1c75fc93d230", "metadata": {}, "outputs": [], "source": ["pan_india_searches_df = searches_df.groupby([\"date_\", \"hour_\", \"l2_id\"], as_index=False)[\n", "    \"searches\"\n", "].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "105eae67-5943-482b-aa93-d6aa741fbcf6", "metadata": {}, "outputs": [], "source": ["pan_india_l2_searches_df = pan_india_searches_df.groupby([\"hour_\", \"l2_id\"], as_index=False)[\n", "    \"searches\"\n", "].mean()"]}, {"cell_type": "code", "execution_count": null, "id": "71d7e138-285b-452a-b6e6-b190b2dea1f9", "metadata": {}, "outputs": [], "source": ["pan_india_l2_searches_df[\"weights\"] = pan_india_l2_searches_df.groupby([\"l2_id\"])[\n", "    \"searches\"\n", "].transform(lambda x: x / x.sum())"]}, {"cell_type": "code", "execution_count": null, "id": "dffd0e05-2edd-4db8-98ca-10091bbccc50", "metadata": {}, "outputs": [], "source": ["pan_india_l2_searches_df.drop(columns=[\"searches\"], inplace=True)\n", "pan_india_l2_searches_df[\"weights\"].fillna(1, inplace=True)"]}, {"cell_type": "markdown", "id": "3cdfd06a-e33e-428b-8aa5-db34cd60f48d", "metadata": {}, "source": ["## Wtd Avail Calculation"]}, {"cell_type": "code", "execution_count": null, "id": "7309ec7c-b2da-4488-af97-82461106902c", "metadata": {}, "outputs": [], "source": ["inv_df = f\"\"\"\n", "select\n", "    snapshot_date_ist as date_,\n", "    snapshot_hr_mm/100 as hour_,\n", "    outlet_id,\n", "    icd.l2_id,\n", "    i.item_id,\n", "    Max(current_inventory) as current_inventory\n", "from \n", "    dwh.agg_hourly_outlet_item_inventory i\n", "join \n", "    rpc.item_category_details icd\n", "    on icd.item_id = i.item_id\n", "    and icd.l2_id in {tuple(l2_id)}\n", "where \n", "    snapshot_date_ist = current_date - interval '1' day\n", "group by 1,2,3,4,5\n", "\"\"\"\n", "inv_df = read_sql_query(inv_df, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "1fab2b76-b595-4ab1-9eef-b1737af8e7ff", "metadata": {}, "outputs": [], "source": ["avail_df = (\n", "    inv_df[[\"date_\", \"outlet_id\", \"l2_id\", \"item_id\"]]\n", "    .drop_duplicates()  ## created date x outlet x item universe\n", "    .merge(pan_india_l2_searches_df, on=[\"l2_id\"], how=\"left\")\n", "    ## Incorpated 24 hours into the above universe to handle missing inv hours from inv table\n", "    .merge(inv_df, on=[\"date_\", \"hour_\", \"outlet_id\", \"l2_id\", \"item_id\"], how=\"left\")\n", ")\n", "avail_df[\"current_inventory\"].fillna(0, inplace=True)\n", "## filled inventory with zero when not find in inv table"]}, {"cell_type": "code", "execution_count": null, "id": "4f0a9a2b-8961-4b23-a563-fa4951123856", "metadata": {}, "outputs": [], "source": ["avail_df[\"is_avail\"] = np.where(avail_df[\"current_inventory\"] > 0, 1, 0)\n", "avail_df[\"avail\"] = avail_df[\"weights\"] * avail_df[\"is_avail\"]"]}, {"cell_type": "code", "execution_count": null, "id": "cfa03fd7-b2fb-4313-be3c-435d965f20d7", "metadata": {}, "outputs": [], "source": ["final_avail_df = avail_df.groupby([\"date_\", \"outlet_id\", \"l2_id\", \"item_id\"], as_index=False)[\n", "    \"avail\"\n", "].sum()\n", "final_avail_df.rename(columns={\"avail\": \"wtd_avail\"}, inplace=True)\n", "final_avail_df.drop(columns=[\"l2_id\"], inplace=True)"]}, {"cell_type": "markdown", "id": "577bb816-7bf1-454e-a57e-1101c5a64053", "metadata": {}, "source": ["## Changing dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "bd87d2ab-3da9-4599-8c41-de1e89aa3b27", "metadata": {}, "outputs": [], "source": ["universe[\"facility_id\"] = universe[\"facility_id\"].astype(int)\n", "universe[\"outlet_id\"] = universe[\"outlet_id\"].astype(int)\n", "universe[\"item_id\"] = universe[\"item_id\"].astype(int)\n", "universe[\"date_\"] = pd.to_datetime(universe[\"date_\"])\n", "universe[\"tag_value\"] = universe[\"tag_value\"].astype(int)\n", "\n", "\n", "sales_data[\"outlet_id\"] = sales_data[\"outlet_id\"].astype(int)\n", "sales_data[\"item_id\"] = sales_data[\"item_id\"].astype(int)\n", "sales_data[\"date_\"] = pd.to_datetime(sales_data[\"date_\"])\n", "\n", "final_avail_df[\"outlet_id\"] = final_avail_df[\"outlet_id\"].astype(int)\n", "final_avail_df[\"item_id\"] = final_avail_df[\"item_id\"].astype(int)\n", "final_avail_df[\"date_\"] = pd.to_datetime(final_avail_df[\"date_\"])"]}, {"cell_type": "markdown", "id": "8d672bff-e473-4f07-87b0-5e1b67d4dfcc", "metadata": {}, "source": ["## Merging Dataframes"]}, {"cell_type": "code", "execution_count": null, "id": "3580da76-d09e-49b5-a743-1bd076484058", "metadata": {}, "outputs": [], "source": ["final_df = universe.merge(\n", "    sales_data,\n", "    left_on=[\"outlet_id\", \"item_id\", \"date_\"],\n", "    right_on=[\"outlet_id\", \"item_id\", \"date_\"],\n", "    how=\"left\",\n", ").merge(\n", "    final_avail_df,\n", "    left_on=[\"outlet_id\", \"item_id\", \"date_\"],\n", "    right_on=[\"outlet_id\", \"item_id\", \"date_\"],\n", "    how=\"left\",\n", ")\n", "final_df[\"qty_sold\"].fillna(0, inplace=True)\n", "final_df[\"wtd_avail\"].fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "d682b142-5fcf-450b-a25e-7ff01153d6a2", "metadata": {}, "outputs": [], "source": ["final_df.shape, universe.shape"]}, {"cell_type": "markdown", "id": "66f0f8b0-bdba-4607-a247-41db7fc8d253", "metadata": {}, "source": ["## Pushing into Main Table"]}, {"cell_type": "code", "execution_count": null, "id": "7f4b7a90-8e8d-4207-a9c6-4b19b931df63", "metadata": {}, "outputs": [], "source": ["final_df.rename(\n", "    columns={\"name\": \"item_name\", \"tag_value\": \"be_outlet_id\", \"outlet_name\": \"be_outlet_name\"},\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b9ee481c-fba3-4f10-b8b2-f510d63ee368", "metadata": {}, "outputs": [], "source": ["final_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "6c6fe628-0a0e-4ccc-9397-f0e0bfc87261", "metadata": {}, "outputs": [], "source": ["new_column_order = [\n", "    \"date_\",\n", "    \"facility_id\",\n", "    \"outlet_id\",\n", "    \"store_name\",\n", "    \"l0\",\n", "    \"l1\",\n", "    \"l2\",\n", "    \"product_type\",\n", "    \"item_id\",\n", "    \"master_assortment_substate_id\",\n", "    \"item_name\",\n", "    \"be_outlet_id\",\n", "    \"be_outlet_name\",\n", "    \"qty_sold\",\n", "    \"wtd_avail\",\n", "]\n", "\n", "# Reassign the DataFrame with the new column order\n", "final_df = final_df[new_column_order]"]}, {"cell_type": "code", "execution_count": null, "id": "d015a8aa-4508-488d-bc02-7f480355ccec", "metadata": {}, "outputs": [], "source": ["final_df = final_df.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "98c06a9f-a791-4690-a113-a80eed16bd6d", "metadata": {}, "outputs": [], "source": ["final_result = final_df.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "0b8effc6-9903-42b0-b63a-1db9a3dcf711", "metadata": {}, "outputs": [], "source": ["final_result.shape"]}, {"cell_type": "code", "execution_count": null, "id": "a7553296-b40f-4ab4-b17a-8a260961aafc", "metadata": {}, "outputs": [], "source": ["b = final_result.shape"]}, {"cell_type": "code", "execution_count": null, "id": "9da4cbaa-f8cd-4f95-914e-b5f68427cedc", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"timestamp(6)\", \"description\": \"date\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility_id\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"outlet_id\"},\n", "    {\"name\": \"store_name\", \"type\": \"varchar\", \"description\": \"store_name\"},\n", "    {\"name\": \"l0\", \"type\": \"varchar\", \"description\": \"l0\"},\n", "    {\"name\": \"l1\", \"type\": \"varchar\", \"description\": \"l1\"},\n", "    {\"name\": \"l2\", \"type\": \"varchar\", \"description\": \"l2\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar\", \"description\": \"product_type\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "    {\n", "        \"name\": \"master_assortment_substate_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"master_assortment_substate_id\",\n", "    },\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"item_name\"},\n", "    {\"name\": \"be_outlet_id\", \"type\": \"integer\", \"description\": \"be_outlet_id\"},\n", "    {\"name\": \"be_outlet_name\", \"type\": \"varchar\", \"description\": \"be_outlet_name\"},\n", "    {\"name\": \"qty_sold\", \"type\": \"real\", \"description\": \"qty_sold\"},\n", "    {\"name\": \"wtd_avail\", \"type\": \"real\", \"description\": \"qty_sold\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "417d82d6-92fb-46cb-87bf-ace75c73d327", "metadata": {}, "outputs": [], "source": ["import datetime\n", "\n", "current_date_str = datetime.datetime.now().strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "cf11a2f5-10f7-4826-a241-a713297924aa", "metadata": {}, "outputs": [], "source": ["if a[0] == b[0]:\n", "    kwargs = {\n", "        \"schema_name\": \"supply_etls\",\n", "        \"table_name\": \"perishable_warehouse_store_details\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"date_\", \"facility_id\", \"outlet_id\", \"item_id\"],\n", "        \"partition_key\": [\"date_\"],\n", "        \"incremental_key\": \"date_\",\n", "        \"force_upsert_without_increment_check\": False,\n", "        \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "        \"table_description\": \"Details of Perishable items at store level\",\n", "    }\n", "\n", "    pb.to_trino(final_result, **kwargs)\n", "\n", "    channel = \"table-updates-tanishq\"\n", "    text_req = (\n", "        \"\\n <@U05CCTXLBU1> \\n Perishable ETL Fresh Details is updated Today \" + current_date_str\n", "    )\n", "    pb.send_slack_message(\n", "        channel=channel,\n", "        text=text_req,\n", "    )\n", "else:\n", "    channel = \"table-updates-tanishq\"\n", "    text_req = (\n", "        \"\\n <@U05CCTXLBU1> \\n Duplication Row in Perishable ETL Fresh Details Table is not updated Today \"\n", "        + current_date_str\n", "    )\n", "    pb.send_slack_message(\n", "        channel=channel,\n", "        text=text_req,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "0452c593-5f06-4fa9-b534-e6454a00d842", "metadata": {}, "outputs": [], "source": ["# select count(*) from blinkit_staging.supply_etls.testing_fnv_table_v1"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}