alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: new_ordering_logic
dag_type: workflow
escalation_priority: low
execution_timeout: 120
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: fresh
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U07668YK86R
path: fresh/perishable_forecasting/workflow/new_ordering_logic
paused: true
pool: fresh_pool
project_name: perishable_forecasting
schedule:
  end_date: '2025-09-03T00:00:00'
  interval: 30 22 * * *
  start_date: '2025-06-06T00:00:00'
schedule_type: fixed
sla: 122 minutes
support_files: []
tags: []
template_name: notebook
version: 1
