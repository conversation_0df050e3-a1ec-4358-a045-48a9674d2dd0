{"cells": [{"cell_type": "code", "execution_count": null, "id": "570d2b75-ccf4-4aa2-b5e8-a8f46da7051c", "metadata": {"papermill": {"duration": 15.931455, "end_time": "2025-06-16T03:50:55.110060", "exception": false, "start_time": "2025-06-16T03:50:39.178605", "status": "completed"}, "tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import os\n", "import time\n", "import gc\n", "import requests\n", "\n", "import calendar\n", "from datetime import datetime as dt\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.options.mode.chained_assignment = None  # default='warn'\n", "pd.set_option(\"display.max_columns\", 500)"]}, {"cell_type": "code", "execution_count": null, "id": "0ced419f-5515-4017-b6e9-2b337fa8d931", "metadata": {"papermill": {"duration": 0.055172, "end_time": "2025-06-16T03:50:55.257465", "exception": false, "start_time": "2025-06-16T03:50:55.202293", "status": "completed"}, "tags": []}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pytz\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")\n", "\n", "current_time = datetime.now(IST)\n", "\n", "current_hour = pd.to_datetime(current_time).hour\n", "\n", "today_date = pd.to_datetime(current_time).strftime(\"%Y-%m-%d\")\n", "\n", "today_date"]}, {"cell_type": "code", "execution_count": null, "id": "2b01ec6f-0754-4486-8658-69ebb4d0575c", "metadata": {"papermill": {"duration": 0.043908, "end_time": "2025-06-16T03:50:55.337983", "exception": false, "start_time": "2025-06-16T03:50:55.294075", "status": "completed"}, "tags": []}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pytz\n", "import calendar\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")\n", "\n", "current_time = datetime.now(IST)\n", "\n", "today_date = (current_time - timedelta(days=0)).strftime(\"%Y-%m-%d\")\n", "t1 = (current_time - timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "t3 = (current_time - timedelta(days=3)).strftime(\"%Y-%m-%d\")\n", "t7 = (current_time - timedelta(days=7)).strftime(\"%Y-%m-%d\")\n", "\n", "\n", "today_date, t1, t7\n", "\n", "dates = (today_date, t1, t7, t3)"]}, {"cell_type": "code", "execution_count": null, "id": "1923587a-110b-4682-8eee-e7ff010d54f8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b40989e0-bc1b-44c2-a216-ae23324fd406", "metadata": {"papermill": {"duration": 0.065551, "end_time": "2025-06-16T03:50:55.540170", "exception": false, "start_time": "2025-06-16T03:50:55.474619", "status": "completed"}, "tags": []}, "outputs": [], "source": ["current_hour"]}, {"cell_type": "code", "execution_count": null, "id": "85c1f3f8-9cfa-4c23-ace2-22c748561dde", "metadata": {"papermill": {"duration": 0.062919, "end_time": "2025-06-16T03:50:55.639164", "exception": false, "start_time": "2025-06-16T03:50:55.576245", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(\n", "            f\"Read attempt: {attempt}... \\nRun started at {datetime.now() + timed<PERSON>ta(hours = 5.5)}\"\n", "        )\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "006e4e3a-31b3-4c69-a883-853bf7792e75", "metadata": {"papermill": {"duration": 0.067209, "end_time": "2025-06-16T03:50:55.748212", "exception": false, "start_time": "2025-06-16T03:50:55.681003", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def try_upload_to_sheets(df, sheet_id, sheet_name, retries=3):\n", "    for attempt in range(retries):\n", "        try:\n", "            pb.to_sheets(df, sheet_id, sheet_name)\n", "            print(f\"Successfully uploaded {sheet_name} on attempt {attempt + 1}\")\n", "            return  # Exit the function once successful\n", "        except Exception as e:\n", "            print(f\"Attempt {attempt + 1} failed for {sheet_name}: {e}\")\n", "            if attempt == retries - 1:\n", "                print(f\"All attempts failed for {sheet_name}\")"]}, {"cell_type": "code", "execution_count": null, "id": "1c8229e9-6126-48f9-8551-de16bd38c3be", "metadata": {"papermill": {"duration": 0.036937, "end_time": "2025-06-16T03:50:55.819073", "exception": false, "start_time": "2025-06-16T03:50:55.782136", "status": "completed"}, "tags": []}, "outputs": [], "source": ["backfill = \"no\""]}, {"cell_type": "markdown", "id": "d02e0658-9120-4bd4-9bb9-9e3e1cf31850", "metadata": {"papermill": {"duration": 0.074435, "end_time": "2025-06-16T03:50:55.957812", "exception": false, "start_time": "2025-06-16T03:50:55.883377", "status": "completed"}, "tags": []}, "source": ["### Active Assortment"]}, {"cell_type": "code", "execution_count": null, "id": "1adc8264-e17e-4b93-a744-82bccf47535a", "metadata": {"papermill": {"duration": 64.617637, "end_time": "2025-06-16T03:52:00.610886", "exception": false, "start_time": "2025-06-16T03:50:55.993249", "status": "completed"}, "tags": []}, "outputs": [], "source": ["base_query = f\"\"\"\n", "    WITH active_stores AS (\n", "        SELECT DISTINCT facility_id, outlet_id \n", "        FROM po.physical_facility_outlet_mapping pfom  \n", "        WHERE ars_active = 1 AND active = 1 AND is_primary = 1 \n", "        AND outlet_id IN (SELECT DISTINCT id FROM retail.console_outlet WHERE business_type_id = 7 AND active = 1 AND lake_active_record) \n", "        AND outlet_id IN (SELECT DISTINCT outlet_id FROM po.bulk_facility_outlet_mapping WHERE active = True AND lake_active_record) \n", "    ), \n", "    \n", "    milk_assortment AS (\n", "        SELECT DISTINCT cl.name AS city, a.facility_id, outlet_id, a.item_id,rpc.name as item_name\n", "        FROM rpc.product_facility_master_assortment a\n", "        JOIN active_stores b ON a.facility_id = b.facility_id\n", "        JOIN retail.console_outlet co ON co.id = b.outlet_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "        LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id AND cl.lake_active_record\n", "        inner join rpc.item_category_details rpc on rpc.item_id = a.item_id and l2_id = 1185 and rpc.lake_active_record\n", "        AND a.active = 1 AND a.master_assortment_substate_id = 1\n", "    ),\n", "    \n", "    be_mapping AS (\n", "        SELECT item_id, facility_id, be_facility_id, be_outlet_id, \n", "        CASE \n", "            WHEN cloud_store_id IS NULL THEN be_outlet_id \n", "            ELSE cloud_store_id \n", "        END AS be_inv_outlet_id\n", "        FROM (\n", "            SELECT DISTINCT tm.item_id, cf.facility_id, cb.facility_id AS be_facility_id, CAST(tm.tag_value AS int) AS be_outlet_id\n", "            FROM rpc.item_outlet_tag_mapping tm\n", "            LEFT JOIN retail.console_outlet cb ON cb.id = CAST(tm.tag_value AS int) AND cb.active = 1 AND cb.lake_active_record\n", "            JOIN retail.console_outlet cf ON cf.id = outlet_id AND cf.active = 1 AND cf.lake_active_record\n", "            WHERE tag_type_id IN (8) AND tm.active = 1\n", "        ) a\n", "        LEFT JOIN (\n", "            SELECT DISTINCT warehouse_id, cloud_store_id \n", "            FROM retail.warehouse_outlet_mapping\n", "            WHERE lake_active_record\n", "        ) wom on wom.warehouse_id = a.be_outlet_id\n", "    ),\n", "    \n", "    final AS (\n", "        SELECT city, a.facility_id, a.outlet_id, a.item_id, be_facility_id,item_name\n", "        FROM milk_assortment a\n", "        LEFT JOIN be_mapping b ON a.item_id = b.item_id AND a.facility_id = b.facility_id\n", "    )\n", "    \n", "    SELECT distinct city, outlet_id,item_id, be_facility_id\n", "    FROM final\n", "\"\"\"\n", "base_df = read_sql_query(base_query, trino)"]}, {"cell_type": "markdown", "id": "4cb3c847-fe81-48b8-bd73-d9ebfc9b811c", "metadata": {"papermill": {"duration": 0.038251, "end_time": "2025-06-16T03:52:00.786883", "exception": false, "start_time": "2025-06-16T03:52:00.748632", "status": "completed"}, "tags": []}, "source": ["### DTS CPC tag hack"]}, {"cell_type": "code", "execution_count": null, "id": "c1ea7caf-ae5d-49b9-8300-596611f4569f", "metadata": {"papermill": {"duration": 6.984654, "end_time": "2025-06-16T03:52:07.814025", "exception": false, "start_time": "2025-06-16T03:52:00.829371", "status": "completed"}, "tags": []}, "outputs": [], "source": ["try:\n", "    be_list = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"Backend Ordering w Case Size\",\n", "    )\n", "except:\n", "    be_list = pd.read_csv(\"Backend Ordering w Case Size.csv\")\n", "be_list = be_list.replace(\"\", 0)\n", "be_list[\"Be_Facility_Id\"] = be_list[\"Be_Facility_Id\"].fillna(0).astype(int)\n", "be_list = be_list.rename(\n", "    columns={\"Be_Facility_Id\": \"be_facility_id\", \"Be_Outlet_Id\": \"outlet_id\", \"type\": \"cpc_flag\"}\n", ")\n", "base_df = base_df.merge(\n", "    be_list[[\"be_facility_id\", \"cpc_flag\"]].drop_duplicates(), on=[\"be_facility_id\"], how=\"left\"\n", ")\n", "base_df[\"cpc_flag\"] = base_df[\"cpc_flag\"].fillna(\"CPC\")\n", "\n", "base_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "07daecb4-f745-443f-96ab-bfa3439ab19d", "metadata": {"papermill": {"duration": 0.050703, "end_time": "2025-06-16T03:52:07.958506", "exception": false, "start_time": "2025-06-16T03:52:07.907803", "status": "completed"}, "tags": []}, "outputs": [], "source": ["base_df[(base_df.city == \"Gurgaon\") & (base_df.cpc_flag == \"DTS\")]"]}, {"cell_type": "code", "execution_count": null, "id": "389e6327-9a4f-493b-b5b6-de7f26d91455", "metadata": {"papermill": {"duration": 0.062857, "end_time": "2025-06-16T03:52:08.060328", "exception": false, "start_time": "2025-06-16T03:52:07.997471", "status": "completed"}, "tags": []}, "outputs": [], "source": ["cpc_cities = set(base_df[base_df.cpc_flag == \"CPC\"][\"city\"].to_list())\n", "dts_cities = set(base_df[base_df.cpc_flag != \"CPC\"][\"city\"].to_list())\n", "len(dts_cities), len(cpc_cities)"]}, {"cell_type": "code", "execution_count": null, "id": "85e91a60-c702-481a-af14-3589e6ef4d42", "metadata": {"papermill": {"duration": 140.151312, "end_time": "2025-06-16T03:54:28.533823", "exception": false, "start_time": "2025-06-16T03:52:08.382511", "status": "completed"}, "tags": []}, "outputs": [], "source": ["if backfill == \"yes\":\n", "    milk_oos = f\"\"\"\n", "    with item_mapping as (\n", "        SELECT DISTINCT ipr.product_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NULL THEN ipom_0.item_id \n", "            ELSE ipr.item_id \n", "        END AS item_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1) \n", "            ELSE COALESCE(ipom_0.multiplier,1) \n", "        END AS multiplier\n", "        FROM rpc.item_product_mapping ipr \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", "    ),\n", "\n", "    sales AS (\n", "        SELECT (oid.order_create_dt_ist) AS order_date, oid.outlet_id, rco.facility_id, oid.product_id, im.item_id, oid.order_id, im.multiplier, ((oid.procured_quantity - oid.total_doorstep_return_quantity) * im.multiplier) AS sales_quantity \n", "        FROM dwh.fact_sales_order_item_details oid \n", "        JOIN item_mapping im on im.product_id = oid.product_id  \n", "        JOIN retail.console_outlet rco ON rco.id = oid.outlet_id AND business_type_id = 7 AND lake_active_record\n", "        WHERE oid.order_create_dt_ist >= current_date - interval '1' day\n", "        AND oid.is_internal_order = false  \n", "        AND (oid.order_type NOT LIKE '%%internal%%' OR oid.order_type IS NULL)  \n", "        AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED'\n", "    ),\n", "\n", "    final_sales AS (\n", "        SELECT cl.name as city, s.outlet_id, CAST(SUM(sales_quantity) AS int) AS sales\n", "        FROM sales s\n", "        JOIN retail.console_outlet co ON co.id = s.outlet_id and co.active = 1 and co.business_type_id = 7 and co.lake_active_record\n", "        LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id\n", "        WHERE item_id IN (SELECT DISTINCT item_id FROM rpc.item_category_details WHERE l2_id = 1185) \n", "        GROUP BY 1,2\n", "        having \n", "        CAST(SUM(case when order_date = current_date then sales_quantity end) AS int) > 10\n", "        or \n", "        CAST(SUM(case when order_date = current_date - interval '1' day then sales_quantity end) AS int) >0\n", "    ),\n", "\n", "    city_item_weights AS (\n", "        select \n", "            city,\n", "            item_id,\n", "            cum_sum_wt,\n", "            case \n", "            when cum_sum_wt/total_weight>0.3 then 'top'\n", "            else 'bottom' end as stype\n", "        from (\n", "         select\n", "            city,\n", "            item_id,\n", "            sum(CAST(weights AS DOUBLE)) over (partition by city order by weights ) as cum_sum_wt,\n", "            sum(CAST(weights AS DOUBLE)) over (partition by city) as total_weight\n", "        FROM supply_etls.city_item_weights a\n", "        WHERE a.updated_at = (select max(updated_at) from supply_etls.city_item_weights where updated_at >= current_date - interval '30' day) \n", "        and updated_at >= current_date - interval '30' day\n", "        AND item_id IN (select distinct item_id from rpc.item_category_details where l2_id = 1185 and lake_active_record)\n", "        )\n", "\n", "    ),\n", "\n", "    final as (\n", "                SELECT\n", "            a.date_,\n", "            cl.name as city,\n", "            b.outlet_name,\n", "            b.outlet_id,\n", "            a.item_id,\n", "            a.hour_,\n", "            max(ci) as net_inv\n", "        from\n", "            supply_etls.milk_sales_avail_searches_dump as a\n", "        inner join po.physical_facility_outlet_mapping b on b.facility_id = a.facility_id and ars_active = 1 and lake_active_record\n", "        LEFT JOIN retail.console_location cl ON cl.id = b.city_id\n", "        inner join (select date_,facility_id,item_id,max(updated_at) as updated_at from supply_etls.milk_sales_avail_searches_dump group by 1,2,3) as mx\n", "            on mx.date_ = a.date_ and a.updated_at=mx.updated_at\n", "            and a.facility_id=mx.facility_id and a.item_id=mx.item_id\n", "        where a.date_ >= current_date - interval '15' day \n", "            and hour_ in (21,22)\n", "        group by 1,2,3,4,5,6\n", "            )\n", "\n", "    select \n", "        date_,\n", "        f.city,\n", "        outlet_id,\n", "        outlet_name,\n", "        f.item_id,\n", "        hour_,\n", "        coalesce(stype,'bottom') as stype,\n", "        net_inv\n", "    from final f\n", "    left join (select item_id, product_id from lake_rpc.item_product_mapping) ipm on ipm.item_id = f.item_id \n", "                    left join (select id, type_id from lake_cms.gr_product) gp on gp.id = cast(ipm.product_id as int)\n", "                    left join (select id, name from lake_cms.gr_product_type) pt on pt.id = gp.type_id\n", "    inner join rpc.item_category_details icd on icd.item_id = f.item_id\n", "    left join city_item_weights ci on ci.city = f.city and ci.item_id = f.item_id\n", "        group by 1,2,3,4,5,6,7,8\n", "    \"\"\"\n", "\n", "else:\n", "    milk_oos = f\"\"\"\n", "    with item_mapping as (\n", "        SELECT DISTINCT ipr.product_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NULL THEN ipom_0.item_id \n", "            ELSE ipr.item_id \n", "        END AS item_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1) \n", "            ELSE COALESCE(ipom_0.multiplier,1) \n", "        END AS multiplier\n", "        FROM rpc.item_product_mapping ipr \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", "    ),\n", "\n", "    sales AS (\n", "        SELECT (oid.order_create_dt_ist) AS order_date, oid.outlet_id, rco.facility_id, oid.product_id, im.item_id, oid.order_id, im.multiplier, ((oid.procured_quantity - oid.total_doorstep_return_quantity) * im.multiplier) AS sales_quantity \n", "        FROM dwh.fact_sales_order_item_details oid \n", "        JOIN item_mapping im on im.product_id = oid.product_id  \n", "        JOIN retail.console_outlet rco ON rco.id = oid.outlet_id AND business_type_id = 7 AND lake_active_record\n", "        WHERE oid.order_create_dt_ist >= current_date - interval '1' day\n", "        AND oid.is_internal_order = false  \n", "        AND (oid.order_type NOT LIKE '%%internal%%' OR oid.order_type IS NULL)  \n", "        AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED'\n", "    ),\n", "\n", "    final_sales AS (\n", "        SELECT cl.name as city, s.outlet_id, CAST(SUM(sales_quantity) AS int) AS sales\n", "        FROM sales s\n", "        JOIN retail.console_outlet co ON co.id = s.outlet_id and co.active = 1 and co.business_type_id = 7 and co.lake_active_record\n", "        LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id\n", "        WHERE item_id IN (SELECT DISTINCT item_id FROM rpc.item_category_details WHERE l2_id = 1185) \n", "        GROUP BY 1,2\n", "        having \n", "        CAST(SUM(case when order_date = current_date then sales_quantity end) AS int) > 10\n", "        or \n", "        CAST(SUM(case when order_date = current_date - interval '1' day then sales_quantity end) AS int) >0\n", "    ),\n", "\n", "    city_item_weights AS (\n", "        select \n", "            city,\n", "            item_id,\n", "            cum_sum_wt,\n", "            case \n", "            when cum_sum_wt/total_weight>0.3 then 'top'\n", "            else 'bottom' end as stype\n", "        from (\n", "         select\n", "            city,\n", "            item_id,\n", "            sum(CAST(weights AS DOUBLE)) over (partition by city order by weights ) as cum_sum_wt,\n", "            sum(CAST(weights AS DOUBLE)) over (partition by city) as total_weight\n", "        FROM supply_etls.city_item_weights a\n", "        WHERE a.updated_at = (select max(updated_at) from supply_etls.city_item_weights where updated_at >= current_date - interval '30' day) \n", "        and updated_at >= current_date - interval '30' day\n", "        AND item_id IN (select distinct item_id from rpc.item_category_details where l2_id = 1185 and lake_active_record)\n", "        )\n", "\n", "    ),\n", "\n", "    item_inv_base as (\n", "                select \n", "                si.item_id as item_id,\n", "                si.outlet_id as outlet_id, \n", "                facility_id,\n", "                po.outlet_name,\n", "                max(si.updated_at + interval '330' minute) as updated_at_ist,\n", "                sum(case when state = 'GOOD' then quantity end) as net_inv,\n", "                sum(case when state = 'BLOCKED' then quantity end) as blocked_inv\n", "                from dynamodb.blinkit_store_inventory_service_oi_rt_view_v2 si\n", "                inner join po.physical_facility_outlet_mapping po on po.outlet_id = cast(si.outlet_id as int) and ars_active = 1 and lake_active_record\n", "                where \n", "                item_id in (select distinct cast(item_id as varchar) from rpc.item_category_details where l2_id = 1185 and lake_active_record)\n", "                group by 1,2,3,4\n", "            ),\n", "\n", "    final as (\n", "            select \n", "            cl.name as city,\n", "            po.facility_id,\n", "            cast(iib.outlet_id as int) outlet_id,\n", "            po.outlet_name as outlet_name,\n", "            cast(iib.item_id as int) as item_id, \n", "            (case when net_inv - coalesce(blocked_inv,0) > 0 then net_inv - coalesce(blocked_inv,0) else 0 end) as net_inv\n", "            from item_inv_base iib\n", "            inner join final_sales ad on ad.outlet_id  = cast(iib.outlet_id as int)\n", "            inner join po.physical_facility_outlet_mapping po on po.outlet_id = cast(iib.outlet_id as int) and ars_active = 1 and lake_active_record\n", "            left join lake_retail.console_location cl on cl.id = po.city_id \n", "            )\n", "\n", "    select \n", "        f.city,\n", "        outlet_id,\n", "        outlet_name,\n", "        f.item_id,\n", "        coalesce(stype,'bottom') as stype,\n", "        net_inv\n", "    from final f\n", "    left join (select item_id, product_id from lake_rpc.item_product_mapping) ipm on ipm.item_id = f.item_id \n", "                    left join (select id, type_id from lake_cms.gr_product) gp on gp.id = cast(ipm.product_id as int)\n", "                    left join (select id, name from lake_cms.gr_product_type) pt on pt.id = gp.type_id\n", "    inner join rpc.item_category_details icd on icd.item_id = f.item_id\n", "    left join city_item_weights ci on ci.city = f.city and ci.item_id = f.item_id\n", "        group by 1,2,3,4,5,6\n", "    \"\"\"\n", "milk_oos = read_sql_query(milk_oos, trino)\n", "milk_oos"]}, {"cell_type": "code", "execution_count": null, "id": "6215ffc7-ff12-440a-bad0-ab905e2bda9b", "metadata": {"papermill": {"duration": 0.059829, "end_time": "2025-06-16T03:54:28.630549", "exception": false, "start_time": "2025-06-16T03:54:28.570720", "status": "completed"}, "tags": []}, "outputs": [], "source": ["## 50% key SKU OOS\n", "top_sku = milk_oos[milk_oos.stype == \"top\"]\n", "top_sku[\"total_top_sku\"] = top_sku.groupby([\"outlet_id\"])[\"item_id\"].transform(\"nunique\")\n", "top_sku[\"avail_top_sku\"] = (\n", "    top_sku[top_sku.net_inv > 0].groupby([\"outlet_id\"])[\"item_id\"].transform(\"nunique\")\n", ")\n", "top_sku = (\n", "    top_sku.groupby([\"city\", \"outlet_id\", \"outlet_name\"])\n", "    .agg({\"total_top_sku\": \"max\", \"avail_top_sku\": \"max\"})\n", "    .reset_index()\n", ")\n", "\n", "top_sku[\"avail_top_sku\"] = top_sku[\"avail_top_sku\"].fillna(0)\n", "top_sku = top_sku[(top_sku[\"total_top_sku\"] // 2 > top_sku[\"avail_top_sku\"])]\n", "top_sku.head()"]}, {"cell_type": "code", "execution_count": null, "id": "91b3f967-9ea7-41ce-b901-d009e55c96bd", "metadata": {"papermill": {"duration": 0.048251, "end_time": "2025-06-16T03:54:28.719655", "exception": false, "start_time": "2025-06-16T03:54:28.671404", "status": "completed"}, "tags": []}, "outputs": [], "source": ["top_sku = top_sku[[\"city\", \"outlet_id\", \"outlet_name\"]].drop_duplicates()\n", "top_sku[\"date_\"] = today_date\n", "top_sku[\"hour_\"] = current_hour\n", "top_sku.head()"]}, {"cell_type": "code", "execution_count": null, "id": "732716d4-3ea2-43fe-8d3d-025e740999f7", "metadata": {"papermill": {"duration": 0.070009, "end_time": "2025-06-16T03:54:28.826688", "exception": false, "start_time": "2025-06-16T03:54:28.756679", "status": "completed"}, "tags": []}, "outputs": [], "source": ["top_sku[top_sku.city == \"Bengaluru\"]"]}, {"cell_type": "code", "execution_count": null, "id": "4c951229-bf39-4616-ae19-494086eb0690", "metadata": {"papermill": {"duration": 0.078388, "end_time": "2025-06-16T03:54:28.959732", "exception": false, "start_time": "2025-06-16T03:54:28.881344", "status": "completed"}, "tags": []}, "outputs": [], "source": ["## all key SKU OOS\n", "at_1_key_sku = (\n", "    milk_oos[milk_oos.stype == \"top\"]\n", "    .groupby([\"city\", \"outlet_id\", \"outlet_name\"])\n", "    .agg({\"net_inv\": \"max\"})\n", "    .reset_index()\n", ")\n", "at_1_key_sku = at_1_key_sku[at_1_key_sku.net_inv == 0]\n", "at_1_key_sku[\"date_\"] = today_date\n", "at_1_key_sku[\"hour_\"] = current_hour\n", "at_1_key_sku"]}, {"cell_type": "code", "execution_count": null, "id": "06d8205d-0b64-4503-b952-8f7c40bbbdec", "metadata": {"papermill": {"duration": 0.096785, "end_time": "2025-06-16T03:54:29.094805", "exception": false, "start_time": "2025-06-16T03:54:28.998020", "status": "completed"}, "tags": []}, "outputs": [], "source": ["## All SKU OOS\n", "all_sku = (\n", "    milk_oos.groupby([\"city\", \"outlet_id\", \"outlet_name\"]).agg({\"net_inv\": \"max\"}).reset_index()\n", ")\n", "all_sku = all_sku[all_sku.net_inv == 0]\n", "all_sku[\"date_\"] = today_date\n", "all_sku[\"hour_\"] = current_hour\n", "all_sku"]}, {"cell_type": "code", "execution_count": null, "id": "5172c22d-a2c2-4fa0-8a6e-4e1ecabacb68", "metadata": {"papermill": {"duration": 0.063677, "end_time": "2025-06-16T03:54:29.196656", "exception": false, "start_time": "2025-06-16T03:54:29.132979", "status": "completed"}, "tags": []}, "outputs": [], "source": ["all_sku.sort_values(\"city\")"]}, {"cell_type": "code", "execution_count": null, "id": "3efa9180-5064-4fe1-9d7d-c1131fcaa7e3", "metadata": {"papermill": {"duration": 0.102063, "end_time": "2025-06-16T03:54:29.338128", "exception": false, "start_time": "2025-06-16T03:54:29.236065", "status": "completed"}, "tags": []}, "outputs": [], "source": ["all_outlets = tuple(all_sku[\"outlet_id\"].to_list())\n", "\n", "top_outlets = tuple(top_sku[\"outlet_id\"].to_list())\n", "\n", "all_top_outlets = tuple(at_1_key_sku[\"outlet_id\"].to_list())\n", "\n", "outlet_id_list = tuple(\n", "    all_sku[\"outlet_id\"].to_list()\n", "    + top_sku[\"outlet_id\"].to_list()\n", "    + at_1_key_sku[\"outlet_id\"].to_list()\n", ")\n", "len(outlet_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "6356557f-5ec9-4934-ba19-44d14fadbc1d", "metadata": {"papermill": {"duration": 4.501091, "end_time": "2025-06-16T03:54:33.881208", "exception": false, "start_time": "2025-06-16T03:54:29.380117", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# try_upload_to_sheets(all_sku, \"1NWgK2bEiFlCCFi5YFFz7XrkgcgDc_pK7D0VdZCSxQTk\", \"All OOS\")\n", "# try_upload_to_sheets(at_1_key_sku, \"1NWgK2bEiFlCCFi5YFFz7XrkgcgDc_pK7D0VdZCSxQTk\", \"All key OOS\")\n", "# try_upload_to_sheets(top_sku, \"1NWgK2bEiFlCCFi5YFFz7XrkgcgDc_pK7D0VdZCSxQTk\", \"50% key OOS\")"]}, {"cell_type": "code", "execution_count": null, "id": "a9bf6dfe-5b0d-4b07-b26c-153290ccaff3", "metadata": {"papermill": {"duration": 0.064188, "end_time": "2025-06-16T03:54:33.988631", "exception": false, "start_time": "2025-06-16T03:54:33.924443", "status": "completed"}, "tags": []}, "outputs": [], "source": ["top_sku_agg = (\n", "    top_sku.groupby([\"city\"])\n", "    .agg({\"outlet_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"outlet_id\": \"50% Key OOS\"})\n", ")\n", "at_1_key_sku_agg = (\n", "    at_1_key_sku.groupby([\"city\"])\n", "    .agg({\"outlet_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"outlet_id\": \"All key OOS\"})\n", ")\n", "all_sku_agg = (\n", "    all_sku.groupby([\"city\"])\n", "    .agg({\"outlet_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"outlet_id\": \"All OOS\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "076b5712-2cd9-46cf-9b7f-dc393c8862c5", "metadata": {"papermill": {"duration": 284.308918, "end_time": "2025-06-16T03:59:18.337545", "exception": false, "start_time": "2025-06-16T03:54:34.028627", "status": "completed"}, "tags": []}, "outputs": [], "source": ["pre_base_query = f\"\"\"\n", "with base as \n", "(select \n", "    item_id,\n", "    outlet_id,\n", "    outlet_name,\n", "    date_ist as date_,\n", "    hour_,\n", "    sum(inventory) as inventory   \n", "\n", "      FROM\n", "       (SELECT iii.outlet_id,\n", "                          iii.item_id,\n", "                          iii.snapshot_date_ist as date_ist,\n", "                          o.name as outlet_name,\n", "                          cast(snapshot_hr_mm/100 as int) AS hour_,\n", "                          max(current_inventory) AS inventory\n", "                   FROM dwh.agg_hourly_outlet_item_inventory as iii\n", "                   LEFT JOIN retail.console_outlet o ON iii.outlet_id = o.id\n", "                   INNER JOIN rpc.product_facility_master_assortment pfma on pfma.item_id = iii.item_id and o.facility_id = pfma.facility_id and master_assortment_substate_id = 1\n", "                   where snapshot_date_ist between  current_date - interval '14' day and current_date - interval '1' day\n", "                   and cast(snapshot_hr_mm/100 as int) = {current_hour}\n", "                   and snapshot_hr_mm%%100 = 0\n", "                   and iii.outlet_id in {outlet_id_list}\n", "                   and iii.item_id in (select item_id from rpc.item_category_details where l2_id = 1185 and lake_active_record)\n", "                   group by 1,2,3,4,5)\n", "                   \n", "      \n", "group by 1,2,3,4,5\n", ")\n", "\n", "\n", "select \n", "    date_, \n", "    outlet_id, \n", "    outlet_name, \n", "    item_id,\n", "    inventory\n", "    \n", "from base \n", "\n", "\"\"\"\n", "\n", "pre_df = read_sql_query(pre_base_query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "68df4e16-c183-417a-ae5b-7b9b929de227", "metadata": {"papermill": {"duration": 0.043904, "end_time": "2025-06-16T03:59:18.433740", "exception": false, "start_time": "2025-06-16T03:59:18.389836", "status": "completed"}, "tags": []}, "outputs": [], "source": ["pre_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "4019507e-51a1-4d67-bfb1-67f0c4005bb4", "metadata": {"papermill": {"duration": 0.103356, "end_time": "2025-06-16T03:59:18.634063", "exception": false, "start_time": "2025-06-16T03:59:18.530707", "status": "completed"}, "tags": []}, "outputs": [], "source": ["## 50% of top SKU\n", "top_base_df = pre_df[pre_df[\"outlet_id\"].isin(top_outlets)].merge(\n", "    milk_oos[(milk_oos.stype == \"top\")][[\"city\", \"outlet_id\", \"item_id\"]],\n", "    on=[\"outlet_id\", \"item_id\"],\n", "    how=\"inner\",\n", ")\n", "top_base_df[\"total_top_sku\"] = top_base_df.groupby([\"date_\", \"outlet_id\"])[\"item_id\"].transform(\n", "    \"nunique\"\n", ")\n", "top_base_df[\"avail_top_sku\"] = (\n", "    top_base_df[top_base_df.inventory > 0]\n", "    .groupby([\"date_\", \"outlet_id\"])[\"item_id\"]\n", "    .transform(\"nunique\")\n", ")\n", "\n", "top_base_df = (\n", "    top_base_df.groupby([\"city\", \"date_\", \"outlet_id\", \"outlet_name\"])\n", "    .agg({\"total_top_sku\": \"max\", \"avail_top_sku\": \"max\"})\n", "    .reset_index()\n", ")\n", "\n", "top_base_df[\"avail_top_sku\"] = top_base_df[\"avail_top_sku\"].fillna(0)\n", "top_base_df = top_base_df[(top_base_df[\"total_top_sku\"] // 2 > top_base_df[\"avail_top_sku\"])]\n", "top_base_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0fbfa05d-22d6-45e5-a08f-334317048773", "metadata": {"papermill": {"duration": 0.102549, "end_time": "2025-06-16T03:59:18.796706", "exception": false, "start_time": "2025-06-16T03:59:18.694157", "status": "completed"}, "tags": []}, "outputs": [], "source": ["## All Key SKU\n", "all_key_df = pre_df[pre_df[\"outlet_id\"].isin(all_top_outlets)].merge(\n", "    milk_oos[[\"city\", \"outlet_id\", \"item_id\", \"stype\"]][milk_oos.stype == \"top\"],\n", "    on=[\"outlet_id\", \"item_id\"],\n", "    how=\"inner\",\n", ")\n", "all_key_df[\"max_inv\"] = all_key_df.groupby([\"date_\", \"outlet_id\"])[\"inventory\"].transform(\"max\")\n", "all_key_df = all_key_df[all_key_df[\"max_inv\"] == 0]\n", "all_key_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "de79f0a7-9502-4b99-81c8-47cc5061e02e", "metadata": {"papermill": {"duration": 0.10041, "end_time": "2025-06-16T03:59:18.982547", "exception": false, "start_time": "2025-06-16T03:59:18.882137", "status": "completed"}, "tags": []}, "outputs": [], "source": ["## 50% of top SKU\n", "all_base_df = pre_df[pre_df[\"outlet_id\"].isin(all_outlets)].merge(\n", "    milk_oos[[\"city\", \"outlet_id\", \"item_id\", \"stype\"]], on=[\"outlet_id\", \"item_id\"], how=\"inner\"\n", ")\n", "all_base_df[\"max_inv\"] = all_base_df.groupby([\"date_\", \"outlet_id\"])[\"inventory\"].transform(\"max\")\n", "all_base_df = all_base_df[all_base_df[\"max_inv\"] == 0]\n", "all_base_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a7cc458a-5638-4fd8-80df-360308531189", "metadata": {"papermill": {"duration": 0.06803, "end_time": "2025-06-16T03:59:19.104983", "exception": false, "start_time": "2025-06-16T03:59:19.036953", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# oos_df = oos_df[oos_df[\"ptype_\"].isin(ptype_list)]"]}, {"cell_type": "markdown", "id": "2e751960-107a-4bb1-b15d-794bfeb065f9", "metadata": {"papermill": {"duration": 0.040651, "end_time": "2025-06-16T03:59:19.208305", "exception": false, "start_time": "2025-06-16T03:59:19.167654", "status": "completed"}, "tags": []}, "source": ["## No of days OOS in L7D"]}, {"cell_type": "code", "execution_count": null, "id": "507fd0df-7931-407a-b865-134266ae89ca", "metadata": {"papermill": {"duration": 4.549711, "end_time": "2025-06-16T03:59:23.870567", "exception": false, "start_time": "2025-06-16T03:59:19.320856", "status": "completed"}, "tags": []}, "outputs": [], "source": ["## 50% of top SKU\n", "\n", "top_oos_days = (\n", "    top_base_df.groupby([\"city\", \"outlet_id\", \"outlet_name\"])\n", "    .agg({\"date_\": \"nunique\"})\n", "    .rename(columns={\"date_\": \"days_oos\"})\n", "    .reset_index()\n", ")\n", "top_oos_days = top_oos_days[top_oos_days.days_oos > 3]\n", "print(top_oos_days.shape)\n", "\n", "top_oos_days[\"date_\"] = today_date\n", "top_oos_days[\"hour_\"] = current_hour\n", "\n", "## All key SKU\n", "\n", "\n", "all_top_oos_days = (\n", "    all_key_df.groupby([\"city\", \"outlet_id\", \"outlet_name\"])\n", "    .agg({\"date_\": \"nunique\"})\n", "    .rename(columns={\"date_\": \"days_oos\"})\n", "    .reset_index()\n", ")\n", "all_top_oos_days = all_top_oos_days[all_top_oos_days.days_oos > 3]\n", "print(top_oos_days.shape)\n", "\n", "all_top_oos_days[\"date_\"] = today_date\n", "all_top_oos_days[\"hour_\"] = current_hour\n", "\n", "\n", "## All SKU\n", "\n", "all_oos_days = (\n", "    all_base_df.groupby([\"city\", \"outlet_id\", \"outlet_name\"])\n", "    .agg({\"date_\": \"nunique\"})\n", "    .rename(columns={\"date_\": \"days_oos\"})\n", "    .reset_index()\n", ")\n", "all_oos_days = all_oos_days[all_oos_days.days_oos > 3]\n", "print(all_oos_days.shape)\n", "\n", "\n", "all_oos_days[\"date_\"] = today_date\n", "all_oos_days[\"hour_\"] = current_hour\n", "\n", "# try_upload_to_sheets(\n", "#     top_oos_days, \"1NWgK2bEiFlCCFi5YFFz7XrkgcgDc_pK7D0VdZCSxQTk\", \"50% key L7 stores\"\n", "# )\n", "\n", "# try_upload_to_sheets(\n", "#     all_top_oos_days, \"1NWgK2bEiFlCCFi5YFFz7XrkgcgDc_pK7D0VdZCSxQTk\", \"All key L7 stores\"\n", "# )\n", "\n", "# try_upload_to_sheets(all_oos_days, \"1NWgK2bEiFlCCFi5YFFz7XrkgcgDc_pK7D0VdZCSxQTk\", \"All L7 stores\")"]}, {"cell_type": "code", "execution_count": null, "id": "0a649680-8dc7-4c19-81f0-ee499a38faa0", "metadata": {"papermill": {"duration": 0.097732, "end_time": "2025-06-16T03:59:24.029441", "exception": false, "start_time": "2025-06-16T03:59:23.931709", "status": "completed"}, "tags": []}, "outputs": [], "source": ["top_l7_agg = (\n", "    top_oos_days.groupby([\"city\"])\n", "    .agg({\"outlet_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"outlet_id\": \"L7 50% Key OOS\"})\n", ")\n", "\n", "all_top_l7_agg = (\n", "    all_top_oos_days.groupby([\"city\"])\n", "    .agg({\"outlet_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"outlet_id\": \"L7 All Key OOS\"})\n", ")\n", "\n", "all_l7_agg = (\n", "    all_oos_days.groupby([\"city\"])\n", "    .agg({\"outlet_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"outlet_id\": \"L7 All OOS\"})\n", ")"]}, {"cell_type": "markdown", "id": "0260a3c5-f654-4e7f-b620-8b53d490a0ae", "metadata": {"papermill": {"duration": 0.03949, "end_time": "2025-06-16T03:59:24.143720", "exception": false, "start_time": "2025-06-16T03:59:24.104230", "status": "completed"}, "tags": []}, "source": ["## Consecutive OOS in L3D"]}, {"cell_type": "code", "execution_count": null, "id": "5ece84ef-bbb4-455d-bef4-df4bdb42a05a", "metadata": {"papermill": {"duration": 4.109228, "end_time": "2025-06-16T03:59:28.348053", "exception": false, "start_time": "2025-06-16T03:59:24.238825", "status": "completed"}, "tags": []}, "outputs": [], "source": ["## 50% of top SKU\n", "\n", "top_cons_days = (\n", "    top_base_df[top_base_df[\"date_\"].between(t3, t1)]\n", "    .groupby([\"city\", \"outlet_id\", \"outlet_name\"])\n", "    .agg({\"date_\": \"nunique\"})\n", "    .rename(columns={\"date_\": \"days_oos\"})\n", "    .reset_index()\n", ")\n", "\n", "top_cons_days = top_cons_days[top_cons_days.days_oos == 3]\n", "\n", "\n", "print(top_cons_days.shape)\n", "\n", "top_cons_days[\"date_\"] = today_date\n", "top_cons_days[\"hour_\"] = current_hour\n", "\n", "## Alltop SKU\n", "\n", "all_key_cons_days = (\n", "    all_key_df[all_key_df[\"date_\"].between(t3, t1)]\n", "    .groupby([\"city\", \"outlet_id\", \"outlet_name\"])\n", "    .agg({\"date_\": \"nunique\"})\n", "    .rename(columns={\"date_\": \"days_oos\"})\n", "    .reset_index()\n", ")\n", "all_key_cons_days = all_key_cons_days[all_key_cons_days.days_oos == 3]\n", "print(all_key_cons_days.shape)\n", "\n", "all_key_cons_days[\"date_\"] = today_date\n", "all_key_cons_days[\"hour_\"] = current_hour\n", "\n", "\n", "## All SKU\n", "all_cons_days = (\n", "    all_base_df[all_base_df[\"date_\"].between(t3, t1)]\n", "    .groupby([\"city\", \"outlet_id\", \"outlet_name\"])\n", "    .agg({\"date_\": \"nunique\"})\n", "    .rename(columns={\"date_\": \"days_oos\"})\n", "    .reset_index()\n", ")\n", "all_cons_days = all_cons_days[all_cons_days.days_oos == 3]\n", "print(all_cons_days.shape)\n", "\n", "all_cons_days[\"date_\"] = today_date\n", "all_cons_days[\"hour_\"] = current_hour\n", "\n", "# try_upload_to_sheets(\n", "#     top_cons_days, \"1NWgK2bEiFlCCFi5YFFz7XrkgcgDc_pK7D0VdZCSxQTk\", \"50% key L3 stores\"\n", "# )\n", "\n", "# try_upload_to_sheets(\n", "#     all_key_cons_days, \"1NWgK2bEiFlCCFi5YFFz7XrkgcgDc_pK7D0VdZCSxQTk\", \"All key L3 stores\"\n", "# )\n", "\n", "# try_upload_to_sheets(all_cons_days, \"1NWgK2bEiFlCCFi5YFFz7XrkgcgDc_pK7D0VdZCSxQTk\", \"All L3 stores\")"]}, {"cell_type": "code", "execution_count": null, "id": "c74ed273-6a9e-499c-a9c5-2e7fbc3d518b", "metadata": {}, "outputs": [], "source": ["all_oos = all_sku\n", "all_key_oos = at_1_key_sku\n", "fifty_perc_key_OOS = top_sku\n", "\n", "fifty_perc_key_l7_stores = top_oos_days\n", "all_key_l7_stores = all_top_oos_days\n", "all_l7_stores = all_oos_days\n", "\n", "fifty_key_l3_stores = top_cons_days\n", "all_key_l3_stores = all_key_cons_days\n", "all_l3_stores = all_cons_days"]}, {"cell_type": "code", "execution_count": null, "id": "323d3ddf-109e-47b7-a3cf-2e382f916fd3", "metadata": {}, "outputs": [], "source": ["all_oos[\"report\"] = \"all_oos\"\n", "all_key_oos[\"report\"] = \"all_key_oos\"\n", "fifty_perc_key_OOS[\"report\"] = \"fifty_perc_key_OOS\"\n", "fifty_perc_key_l7_stores[\"report\"] = \"fifty_perc_key_l7_stores\"\n", "all_key_l7_stores[\"report\"] = \"all_key_l7_stores\"\n", "all_l7_stores[\"report\"] = \"all_l7_stores\"\n", "fifty_key_l3_stores[\"report\"] = \"fifty_key_l3_stores\"\n", "all_key_l3_stores[\"report\"] = \"all_key_l3_stores\"\n", "all_l3_stores[\"report\"] = \"all_l3_stores\"\n", "\n", "columns = [\"city\", \"outlet_id\", \"outlet_name\", \"days_oos\", \"net_inv\", \"date_\", \"hour_\", \"report\"]\n", "\n", "all_dfs = [\n", "    all_oos,\n", "    all_key_oos,\n", "    fifty_perc_key_OOS,\n", "    fifty_perc_key_l7_stores,\n", "    all_key_l7_stores,\n", "    all_l7_stores,\n", "    fifty_key_l3_stores,\n", "    all_key_l3_stores,\n", "    all_l3_stores,\n", "]\n", "\n", "for df in all_dfs:\n", "    for col in columns:\n", "        if col not in df.columns:\n", "            df[col] = None\n", "\n", "milk_oos_alert = pd.concat(all_dfs, ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "83be9310-6063-4eaf-8520-d3cb286ba76a", "metadata": {}, "outputs": [], "source": ["milk_oos_alert[\"date_\"] = pd.to_datetime(milk_oos_alert[\"date_\"]).dt.date\n", "milk_oos_alert[\"net_inv\"] = milk_oos_alert[\"net_inv\"].astype(float)\n", "milk_oos_alert[\"days_oos\"] = milk_oos_alert[\"days_oos\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "9797c44a-3bf6-4f6f-a72b-4f9d868182c1", "metadata": {}, "outputs": [], "source": ["kwargs_milk = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"milk_oos_alert_data\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"city\"},\n", "        {\"name\": \"outlet_id\", \"type\": \"bigint\", \"description\": \"outlet_id\"},\n", "        {\"name\": \"outlet_name\", \"type\": \"varchar\", \"description\": \"outlet_name\"},\n", "        {\"name\": \"net_inv\", \"type\": \"double\", \"description\": \"net_inv\"},\n", "        {\"name\": \"date_\", \"type\": \"date\", \"description\": \"date_\"},\n", "        {\"name\": \"hour_\", \"type\": \"bigint\", \"description\": \"hour_\"},\n", "        {\"name\": \"report\", \"type\": \"varchar\", \"description\": \"report\"},\n", "        {\"name\": \"days_oos\", \"type\": \"double\", \"description\": \"days_oos\"},\n", "    ],\n", "    \"primary_key\": [\"date_\", \"city\", \"outlet_id\", \"hour_\", \"report\"],\n", "    \"partition_key\": [\"date_\"],\n", "    \"incremental_key\": \"date_\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"Out of Stock alerts for Milk products by city and outlet\",\n", "}\n", "\n", "pb.to_trino(milk_oos_alert, **kwargs_milk)"]}, {"cell_type": "code", "execution_count": null, "id": "ddb90642-861b-4c98-94ad-2400234ad556", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "papermill": {"duration": 564.331405, "end_time": "2025-06-16T04:00:02.227238", "environment_variables": {}, "exception": null, "input_path": "/usr/local/airflow/dags/4ae96f36753da25453e5911aa13373bbe46ee6bb/dags/povms/perishables/alert/milk_oos_alert/notebook.ipynb", "output_path": "s3://grofers-prod-dse-sgp/airflow/dag_runs/povms_perishables_alert_milk_oos_alert_v4/scheduled__2025-06-15T16:30:00+00:00/run_notebook/try_1.ipynb", "parameters": {"cwd": "/usr/local/airflow/dags/repo/dags/povms/perishables/alert/milk_oos_alert"}, "start_time": "2025-06-16T03:50:37.895833", "version": "2.0.0"}}, "nbformat": 4, "nbformat_minor": 5}