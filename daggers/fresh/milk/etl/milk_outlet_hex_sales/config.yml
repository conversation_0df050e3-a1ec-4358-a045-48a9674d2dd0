alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: milk_outlet_hex_sales
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: very-high-mem
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: fresh
notebook:
  parameters: null
  retries: 1
owner:
  email: <EMAIL>
  slack_id: U06T927GCSW
path: fresh/milk/etl/milk_outlet_hex_sales
paused: false
pool: fresh_pool
project_name: milk
schedule:
  end_date: '2025-09-05T00:00:00'
  interval: 46 1,4 * * *
  start_date: '2025-06-11T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
