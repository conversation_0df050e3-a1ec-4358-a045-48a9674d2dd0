{"cells": [{"cell_type": "code", "execution_count": null, "id": "b89c7bb5-ec1f-487e-b00e-82ac8f3c8fef", "metadata": {"tags": ["parameter"]}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import os\n", "import time\n", "import gc\n", "import requests\n", "import sys\n", "import calendar\n", "from datetime import datetime as dt\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "!pip install duckdb\n", "import duckdb\n", "\n", "# redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "# rds = pb.get_connection(\"[Replica] RDS IMS\")\n", "\n", "pd.options.mode.chained_assignment = None  # default='warn'\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_rows\", 500)"]}, {"cell_type": "code", "execution_count": null, "id": "6ba58bd9-de41-4708-ae11-d7a7501f7f0b", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(\n", "            f\"Read attempt: {attempt}... \\nRun started at {datetime.now() + timed<PERSON>ta(hours = 5.5)}\"\n", "        )\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "835a1be9-549d-4d82-89eb-d6c948a6fb90", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pytz\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")\n", "\n", "current_time = datetime.now(IST)\n", "\n", "current_hour = pd.to_datetime(current_time).hour\n", "\n", "today_date = current_time.strftime(\"%Y-%m-%d\")\n", "\n", "today_date, current_hour"]}, {"cell_type": "code", "execution_count": null, "id": "eb5dfe44-187a-46aa-b94b-2245104cca44", "metadata": {}, "outputs": [], "source": ["if current_hour >= 10:\n", "    transfer_date = (pd.to_datetime(today_date) + timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "else:\n", "    transfer_date = today_date\n", "transfer_date"]}, {"cell_type": "code", "execution_count": null, "id": "976c6b7f-b6ea-4fc8-92c9-f258b1dbc8e9", "metadata": {}, "outputs": [], "source": ["l21_date = (pd.to_datetime(transfer_date) - <PERSON><PERSON><PERSON>(days=22)).strftime(\"%Y-%m-%d\")\n", "yday = (pd.to_datetime(transfer_date) - timed<PERSON>ta(days=2)).strftime(\"%Y-%m-%d\")\n", "l7d = (pd.to_datetime(transfer_date) - <PERSON><PERSON><PERSON>(days=9)).strftime(\"%Y-%m-%d\")\n", "tday = (pd.to_datetime(transfer_date) - timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "l21_date, yday, tday, current_hour"]}, {"cell_type": "code", "execution_count": null, "id": "fdd362d6-8651-434f-a56a-827c486766d3", "metadata": {"tags": []}, "outputs": [], "source": ["try:\n", "    be_list = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"Backend Ordering w Case Size\",\n", "    )\n", "\n", "except:\n", "    be_list = pd.read_csv(\"Backend Ordering w Case Size.csv\")\n", "be_list = be_list.replace(\"\", 0)\n", "be_list[\"Be_Facility_Id\"] = be_list[\"Be_Facility_Id\"].fillna(0).astype(int)\n", "be_list = be_list[be_list[\"type\"] == \"CPC\"]\n", "be_facility_id = tuple(set(be_list[\"Be_Facility_Id\"].to_list()))"]}, {"cell_type": "code", "execution_count": null, "id": "cdfc2868-701d-4126-aa5b-cf0321383e38", "metadata": {}, "outputs": [], "source": ["if current_hour >= 10:\n", "    h1 = 6\n", "    h2 = 23\n", "    h3 = 17\n", "    slot = \"Slot A and full day\"\n", "else:\n", "    h1 = 17\n", "    h2 = 23\n", "    slot = \"Slot B\""]}, {"cell_type": "code", "execution_count": null, "id": "6015f8cd-9078-4806-a98b-e436da562eeb", "metadata": {}, "outputs": [], "source": ["l21_date"]}, {"cell_type": "code", "execution_count": null, "id": "83a409f7-8714-4de0-9f51-d549255927e0", "metadata": {"tags": []}, "outputs": [], "source": ["base_query = f\"\"\"\n", "    WITH active_stores AS (\n", "        SELECT DISTINCT facility_id, outlet_id \n", "        FROM po.physical_facility_outlet_mapping pfom  \n", "        WHERE ars_active = 1 AND active = 1 AND is_primary = 1 \n", "        AND outlet_id IN (SELECT DISTINCT id FROM retail.console_outlet WHERE business_type_id = 7 AND active = 1 AND lake_active_record) \n", "        AND outlet_id IN (SELECT DISTINCT outlet_id FROM po.bulk_facility_outlet_mapping WHERE active = True AND lake_active_record) \n", "    ), \n", "    \n", "    milk_assortment AS (\n", "        SELECT DISTINCT cl.name AS city, a.facility_id, outlet_id, item_id\n", "        FROM rpc.product_facility_master_assortment a\n", "        JOIN active_stores b ON a.facility_id = b.facility_id\n", "        JOIN retail.console_outlet co ON co.id = b.outlet_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "        LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id AND cl.lake_active_record\n", "        WHERE item_id IN (SELECT DISTINCT item_id FROM rpc.item_category_details WHERE l2_id = 1185) \n", "        AND a.active = 1 AND a.master_assortment_substate_id = 1\n", "    ),\n", "    \n", "    be_mapping AS (\n", "        SELECT item_id, facility_id, be_facility_id, be_outlet_id, \n", "        CASE \n", "            WHEN cloud_store_id IS NULL THEN be_outlet_id \n", "            ELSE cloud_store_id \n", "        END AS be_inv_outlet_id\n", "        FROM (\n", "            SELECT DISTINCT tm.item_id, cf.facility_id, cb.facility_id AS be_facility_id, CAST(tm.tag_value AS int) AS be_outlet_id\n", "            FROM rpc.item_outlet_tag_mapping tm\n", "            LEFT JOIN retail.console_outlet cb ON cb.id = CAST(tm.tag_value AS int) AND cb.active = 1 AND cb.lake_active_record\n", "            JOIN retail.console_outlet cf ON cf.id = outlet_id AND cf.active = 1 AND cf.lake_active_record\n", "            WHERE tag_type_id IN (8) AND tm.active = 1\n", "        ) a\n", "        LEFT JOIN (\n", "            SELECT DISTINCT warehouse_id, cloud_store_id \n", "            FROM retail.warehouse_outlet_mapping\n", "            WHERE lake_active_record\n", "        ) wom on wom.warehouse_id = a.be_outlet_id\n", "    ),\n", "    \n", "    final AS (\n", "        SELECT \n", "        city, \n", "        a.facility_id, \n", "        a.outlet_id, \n", "        a.item_id, \n", "        be_facility_id, \n", "        be_outlet_id, \n", "        be_inv_outlet_id\n", "        FROM milk_assortment a\n", "        LEFT JOIN be_mapping b ON a.item_id = b.item_id AND a.facility_id = b.facility_id\n", "        where \n", "        be_facility_id in {be_facility_id}\n", "        \n", "    ),\n", "    date_hour as (\n", "    select date date_ from unnest(sequence(date('{l21_date}') ,date('{tday}') )) as t(date)\n", "    ),\n", "fin_ as (\n", "SELECT \n", "        city, \n", "        facility_id, \n", "        outlet_id, \n", "        item_id, \n", "        be_facility_id, \n", "        be_outlet_id, \n", "        be_inv_outlet_id,\n", "        date(date_) date_\n", "    from \n", "        final\n", "    cross join date_hour\n", "),\n", "\n", "\n", "hex_mapping as (\n", "    with frontend_merchant_mapping as\n", "        (select * from dwh.dim_merchant_outlet_facility_mapping\n", "            where \n", "                is_frontend_merchant_active = true\n", "                and\n", "                    is_backend_merchant_active = true\n", "                and \n", "                    is_pos_outlet_active = 1\n", "                and \n", "                    is_mapping_enabled = true\n", "                and \n", "                    is_express_store = true\n", "                and \n", "                    is_current_mapping_active = true\n", "                and \n", "                    is_current = true\n", "                and \n", "                    pos_outlet_name <> 'SS Gurgaon Test Store'\n", "        ),\n", "\n", "    fin_l15_l as (\n", "        select \n", "            date('{l21_date}') as etl_create_ts_ist,\n", "            hex_id,\n", "            outlet_id,\n", "            outlet_name\n", "        from \n", "        \n", "        (\n", "                select\n", "                    merchant_id,\n", "                    a.action,\n", "                    hex_id,\n", "                    etl_create_ts_ist,\n", "                    rank() over (partition by merchant_id order by date(etl_create_ts_ist) desc) as rk,\n", "                    case \n", "                    when pos_outlet_city_name = 'HR-NCR' then 'Gurgaon'\n", "                    when pos_outlet_city_name = 'UP-NCR' then 'Ghaziabad'\n", "                    else pos_outlet_city_name end as city,\n", "                    pos_outlet_name as outlet_name,\n", "                    fm.pos_outlet_id as outlet_id\n", "                from dwh.dim_merchant_polygon_hex_enriched a\n", "                left join frontend_merchant_mapping fm on fm.frontend_merchant_id = a.merchant_id\n", "                where action in ('POLYGON_UPDATED','STORE_ADDED')\n", "                 and \n", "                 etl_create_ts_ist < date('{l21_date}') \n", "                 and type = 'STORE_POLYGON'\n", "        )\n", "        WHERE rk = 1\n", "        ),\n", "        \n", "    fin_l15_g as \n", "    \n", "        (    select\n", "                etl_create_ts_ist,\n", "                hex_id,\n", "                fm.pos_outlet_id as outlet_id,\n", "                pos_outlet_name as outlet_name\n", "            from dwh.dim_merchant_polygon_hex_enriched a\n", "            left join frontend_merchant_mapping fm on fm.frontend_merchant_id = a.merchant_id\n", "            where action in ('POLYGON_UPDATED','STORE_ADDED')\n", "             and \n", "            etl_create_ts_ist >= date('{l21_date}')\n", "            and type = 'STORE_POLYGON'\n", "        )\n", "        \n", "\n", "        select \n", "        distinct \n", "            date(etl_create_ts_ist) as date_,\n", "            outlet_id,\n", "            outlet_name,\n", "            hex_id\n", "        from\n", "            fin_l15_l\n", "            \n", "        UNION \n", "        \n", "        select \n", "        distinct \n", "            date(etl_create_ts_ist)  as date_,\n", "            outlet_id,\n", "            outlet_name,\n", "            hex_id\n", "        from\n", "            fin_l15_g\n", "),\n", "\n", "base as (\n", "        select \n", "            he.date_ as last_poly_updated,\n", "            b.date_ as date_,\n", "            b.city,\n", "            b.be_facility_id,\n", "            b.outlet_id,\n", "            b.facility_id,\n", "            he.hex_id,\n", "            item_id\n", "            --hour_\n", "        from \n", "            fin_ b\n", "        left join hex_mapping he on b.outlet_id = he.outlet_id  and he.date_ <= b.date_\n", ")\n", "    select\n", "    distinct\n", "        b.last_poly_updated,\n", "        b.date_,\n", "        b.city,\n", "        b.facility_id,\n", "        b.be_facility_id,\n", "        b.outlet_id,\n", "        b.hex_id,\n", "        b.item_id\n", "        --hour_\n", "from\n", "    base b\n", "inner join (select outlet_id,date_, max(last_poly_updated) last_poly_updated from base group by 1,2) mx\n", "        on mx.outlet_id = b.outlet_id and mx.last_poly_updated = b.last_poly_updated and mx.date_ = b.date_\n", "inner join po.physical_facility_outlet_mapping p on p.outlet_id = b.outlet_id and ars_active = 1 and lake_active_record\n", "\n", "\"\"\"\n", "base_pre_df = read_sql_query(base_query, trino)\n", "\n", "base_pre_df[\"be_facility_id\"] = base_pre_df[\"be_facility_id\"].fillna(0).astype(int)\n", "base_pre_df[\"date_\"] = pd.to_datetime(base_pre_df[\"date_\"])\n", "base_pre_df[\"overlap\"] = base_pre_df.groupby([\"date_\", \"hex_id\"])[\"outlet_id\"].transform(\"nunique\")\n", "base_pre_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "ff449daa-9bc9-47fa-998e-286b53f222c2", "metadata": {}, "outputs": [], "source": ["base_pre_df[\"overlap_max\"] = base_pre_df.groupby([\"date_\", \"outlet_id\"])[\"overlap\"].transform(\"max\")\n", "base_pre_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ad41bc37-34ef-429e-bf65-10c8c6d68b0d", "metadata": {}, "outputs": [], "source": ["facility_id_list = tuple(set(base_pre_df[\"facility_id\"].to_list()))\n", "outlet_id_list = tuple(set(base_pre_df[\"outlet_id\"].to_list()))\n", "item_id_list = tuple(set(base_pre_df[\"item_id\"].to_list()))\n", "ovlp_hex = base_pre_df[base_pre_df.overlap > 1].copy()\n", "non_onvl_hex = base_pre_df[base_pre_df.overlap == 1].copy()\n", "ovlp_facility_ids = tuple(set(ovlp_hex[\"facility_id\"].to_list()))\n", "non_ovlp_facility_ids = tuple(set(non_onvl_hex[\"facility_id\"].to_list()))\n", "len(facility_id_list), len(outlet_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "fa26fd66-8f1e-420d-989f-dfc656e2feb9", "metadata": {"tags": []}, "outputs": [], "source": ["hex_level_sales = f\"\"\"\n", "WITH item_mapping as (\n", "    SELECT DISTINCT ipr.product_id,\n", "    CASE\n", "        WHEN ipr.item_id IS NULL THEN ipom_0.item_id\n", "        ELSE ipr.item_id\n", "    END AS item_id,\n", "    CASE\n", "        WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1)\n", "        ELSE COALESCE(ipom_0.multiplier,1)\n", "    END AS multiplier\n", "    FROM rpc.item_product_mapping ipr\n", "    LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id\n", "    LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", ")\n", ",\n", "\n", "sales AS (\n", "     SELECT \n", "     (oid.cart_checkout_ts_ist) AS date_,\n", "     extract(hour from oid.cart_checkout_ts_ist) hour_,\n", "     cl.name AS city,\n", "     rco.facility_id, \n", "     rco.name AS outlet_name,\n", "     oid.outlet_id,\n", "     frontend_merchant_id,\n", "     oid.product_id, \n", "     im.item_id, \n", "     oid.cart_id,\n", "     oid.order_id,\n", "     im.multiplier,\n", "    ((oid.procured_quantity - oid.total_doorstep_return_quantity) * im.multiplier) AS sales_quantity\n", "    FROM dwh.fact_sales_order_item_details oid\n", "    left JOIN item_mapping im on im.product_id = oid.product_id\n", "    left JOIN retail.console_outlet rco ON rco.id = oid.outlet_id AND business_type_id = 7\n", "    LEFT JOIN retail.console_location cl ON cl.id = rco.tax_location_id\n", "    WHERE oid.order_create_dt_ist between date('{l21_date}') and date('{tday}') - interval '1' day\n", "    AND oid.is_internal_order = false AND (oid.order_type NOT LIKE '%%internal%%' OR oid.order_type IS NULL)\n", "    AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED'\n", "    and im.item_id IN {item_id_list}\n", "    and extract(hour from cart_checkout_ts_ist) between {h1} and {h2}\n", "    and oid.outlet_id in {outlet_id_list}\n", ")\n", "        \n", "        \n", "        \n", "    \n", "select \n", "    date(s.date_) as date_,\n", "    s.city,\n", "    hex9 as hex_id,\n", "    s.outlet_id as outlet_id,\n", "    s.item_id,\n", "    s.hour_,\n", "    sum(sales_quantity) as sales \n", "from dwh.fact_sales_supply_merchant_hex_details a\n", "inner join sales s on s.order_id = a.order_id\n", "where\n", "    order_create_dt_ist  between date('{l21_date}') and date('{tday}') - interval '1' day\n", "group by 1,2,3,4,5,6\n", "\"\"\"\n", "sales = read_sql_query(hex_level_sales, trino)\n", "sales[\"date_\"] = pd.to_datetime(sales[\"date_\"])\n", "\n", "sales.shape, sales.date_.min(), sales.date_.max()"]}, {"cell_type": "code", "execution_count": null, "id": "85ba41a9-d83a-40de-bd77-658d91541cc8", "metadata": {}, "outputs": [], "source": ["sales_df = (\n", "    sales.groupby([\"date_\", \"city\", \"hex_id\", \"outlet_id\", \"item_id\"])\n", "    .agg({\"sales\": \"sum\"})\n", "    .reset_index()\n", ")\n", "sales_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bab6a599-bf9d-4b4a-a423-c62f50d79b3f", "metadata": {}, "outputs": [], "source": ["avail = f\"\"\"\n", "        SELECT\n", "           a.date_,\n", "            cl.name as city,\n", "            a.facility_id,\n", "            b.outlet_id,\n", "            a.item_id,\n", "            a.hour_,\n", "            max(is_available) as is_available\n", "\n", "        from\n", "            supply_etls.milk_sales_avail_searches_dump as a\n", "        left join po.physical_facility_outlet_mapping b on b.facility_id = a.facility_id and ars_active = 1 and lake_active_record\n", "        LEFT JOIN retail.console_location cl ON cl.id = b.city_id\n", "        inner join (select date_,facility_id,item_id,max(updated_at) as updated_at from supply_etls.milk_sales_avail_searches_dump group by 1,2,3) as mx\n", "            on mx.date_ = a.date_ and a.updated_at=mx.updated_at\n", "            and a.facility_id=mx.facility_id and a.item_id=mx.item_id\n", "        where a.date_ BETWEEN DATE('{l21_date}') AND DATE('{tday}') - interval '1' day\n", "            and a.facility_id in {facility_id_list}\n", "            and a.item_id in {item_id_list}\n", "            and hour_ between {h1} and {h2}\n", "        group by 1,2,3,4,5,6\n", "\"\"\"\n", "\n", "avail_df = read_sql_query(avail, trino)\n", "avail_df[\"date_\"] = pd.to_datetime(avail_df[\"date_\"])\n", "avail_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "5bc93712-17b2-4890-b715-4e57104f36fe", "metadata": {}, "outputs": [], "source": ["hr_wts = read_sql_query(\n", "    \"\"\"SELECT DISTINCT city, order_hour AS hour_, CAST(weights AS DOUBLE) AS chw\n", "                        FROM supply_etls.city_hour_weights a\n", "                        WHERE a.updated_at IN (SELECT MAX(updated_at) FROM supply_etls.city_hour_weights where updated_at >= current_date - interval '32' day)\n", "                         and a.updated_at >= current_date - interval '32' day\"\"\",\n", "    trino,\n", ")\n", "hr_wts.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "4d085186-dc47-4870-87bf-520ff5408060", "metadata": {}, "outputs": [], "source": ["non_ovlp = f\"\"\"\n", "with base as (\n", "select \n", "    b.date_ as date_,\n", "    b.city,\n", "    b.be_facility_id,\n", "    b.outlet_id,\n", "    b.facility_id,\n", "    b.hex_id,\n", "    b.item_id,\n", "    coalesce(sales,0) as sales\n", "from \n", "    non_onvl_hex b\n", "    left join sales_df s on \n", "        cast(b.outlet_id as int) = s.outlet_id  \n", "        and date(b.date_) = s.date_ \n", "        and b.hex_id = s.hex_id \n", "        and cast(b.item_id as int) = s.item_id \n", "),\n", "\n", "avail as (\n", "    select\n", "        a.date_,\n", "        a.city,\n", "        a.facility_id,\n", "        a.item_id,\n", "        sum(is_available*chw)/sum(chw) as avail\n", "    from avail_df a\n", "    left join hr_wts h on h.city = a.city and h.hour_ = a.hour_\n", "group by 1,2,3,4\n", ")\n", "\n", "select\n", "    b.date_,\n", "    b.outlet_id,\n", "    b.hex_id,\n", "    b.item_id,\n", "    sales,\n", "    avail,\n", "    sales * (1 + 0.35 * (1-coalesce(avail,1))) as lin_extp,\n", "    sales * (5 ** (0.50 * (1 - coalesce(avail,1)))) as exp_extp,\n", "    sales * (1 + ((1 - coalesce(avail,1)) ** 2) / (4 * 0.08)) para_extp\n", "from\n", "    base b\n", "left join avail a on \n", "        b.facility_id = a.facility_id\n", "        and b.item_id = a.item_id \n", "        and b.date_ = a.date_\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "d0fc4a32-da40-4060-839d-f4193166e302", "metadata": {}, "outputs": [], "source": ["non_ovlp_df = duckdb.query(non_ovlp).to_df()\n", "non_ovlp_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6041b701-a9fd-4f3f-959a-7848d176edf6", "metadata": {}, "outputs": [], "source": ["ovlp = f\"\"\"\n", "with base as (\n", "    select \n", "        date(b.date_) as date_,\n", "        b.city,\n", "        b.be_facility_id,\n", "        cast(b.outlet_id as int) outlet_id,\n", "        cast(b.facility_id as int) facility_id,\n", "        b.hex_id,\n", "        cast(b.item_id as int) item_id,\n", "        sales,\n", "        sum(sales) over (partition by b.hex_id,b.item_id,b.date_) as net_sales\n", "    from \n", "        ovlp_hex b\n", "        left join sales_df s on \n", "            cast(b.outlet_id as int) = s.outlet_id  \n", "            and date(b.date_) = s.date_ \n", "            and b.hex_id = s.hex_id \n", "            and cast(b.item_id as int) = s.item_id \n", ")\n", ",\n", "\n", "avail as (\n", "    select \n", "        date_,\n", "        hex_id,\n", "        item_id,\n", "        coalesce(sales,0) as sales,\n", "        sum(is_available*chw)/sum(chw) as avail\n", "    from \n", "        (select\n", "            b.date_,\n", "            b.city,\n", "            b.item_id,\n", "            hex_id,\n", "            hour_,\n", "            net_sales sales,\n", "            max(is_available) as is_available\n", "        from \n", "        base b\n", "        left join avail_df a on date(a.date_) = b.date_ and cast(a.item_id as int) = b.item_id and cast(a.facility_id as int) = b.facility_id\n", "        group by 1,2,3,4,5,6\n", "        ) a\n", "    left join hr_wts h on h.city = a.city and h.hour_ = a.hour_\n", "    GROUP BY 1,2,3,4\n", ")\n", "\n", "select\n", "    date_,\n", "    hex_id,\n", "    item_id,\n", "    sales,\n", "    avail,\n", "    sales*(1+0.35*(1-coalesce(avail,1))) as lin_extp,\n", "    sales * (5 ** (0.50 * (1 - coalesce(avail,1)))) as exp_extp,\n", "    sales * (1 + ((1 - coalesce(avail,1)) ** 2) / (4 * 0.08)) para_extp\n", "from\n", "    avail\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "f2956339-edab-4465-acfb-f3d9d08115be", "metadata": {}, "outputs": [], "source": ["ovlp_df = duckdb.query(ovlp).to_df()\n", "ovlp_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c9f71a81-2dbb-4efd-a8ba-874134252325", "metadata": {}, "outputs": [], "source": ["boundary = f\"\"\"\n", "with base as (\n", "    select \n", "        date(s.date_) as date_,\n", "        cast(s.outlet_id as int) outlet_id,\n", "        s.city,\n", "        s.hex_id as hex_id,\n", "        cast(s.item_id as int) item_id,\n", "        s.sales\n", "    from \n", "        sales_df s\n", "    left join\n", "        base_pre_df b\n", "        on cast(s.outlet_id as int) = cast(b.outlet_id as int)\n", "        and date(s.date_) = date(b.date_)\n", "        and s.hex_id = b.hex_id\n", "    where\n", "        b.hex_id is null\n", "),\n", "\n", "avb as (\n", "    select \n", "        date_,\n", "        hex_id,\n", "        outlet_id,\n", "        item_id,\n", "        sales,\n", "        sum(is_available*chw)/sum(chw) as avail\n", "    from \n", "    (  select\n", "            b.date_,\n", "            b.city,\n", "            b.outlet_id,\n", "            b.item_id,\n", "            hex_id,\n", "            hour_,\n", "            sales,\n", "            max(is_available) as is_available\n", "        from \n", "            base b\n", "        left join avail_df a on date(a.date_) = b.date_ and cast(a.item_id as int) = b.item_id and cast(a.outlet_id as int) = b.outlet_id\n", "            group by 1,2,3,4,5,6,7\n", "    ) as a\n", "    left join hr_wts h on h.city = a.city and h.hour_ = a.hour_\n", "    GROUP BY 1,2,3,4,5\n", ")\n", "\n", "select\n", "    b.date_,\n", "    b.outlet_id,\n", "    b.hex_id,\n", "    b.item_id,\n", "    sales,\n", "    avail,\n", "    sales * (1+0.35*(1-coalesce(avail,1))) as lin_extp,\n", "    sales * (5 ** (0.50 * (1 - coalesce(avail,1)))) as exp_extp,\n", "    sales * (1 + ((1 - coalesce(avail,1)) ** 2) / (4 * 0.08)) para_extp\n", "from\n", "    avb b\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "843121b8-638b-44a4-87a8-12a31a170b75", "metadata": {}, "outputs": [], "source": ["bound_df = duckdb.query(boundary).to_df()\n", "bound_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8a84bcf2-1d3b-42e1-9e6e-e9cffff374f2", "metadata": {}, "outputs": [], "source": ["bound_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "74bd3d72-56cb-46f1-8207-674d120d139b", "metadata": {"tags": []}, "outputs": [], "source": ["del [non_onvl_hex]\n", "gc.collect"]}, {"cell_type": "code", "execution_count": null, "id": "feb64747-b366-4ef6-9aca-60dce1f936d8", "metadata": {}, "outputs": [], "source": ["ovlp_hex.head()"]}, {"cell_type": "code", "execution_count": null, "id": "472cf1b4-07ef-48a3-b339-41c5a773f95c", "metadata": {"tags": []}, "outputs": [], "source": ["live_hex = f\"\"\"\n", "with frontend_merchant_mapping as\n", "        (select * from dwh.dim_merchant_outlet_facility_mapping\n", "            where \n", "                is_frontend_merchant_active = true\n", "                and\n", "                    is_backend_merchant_active = true\n", "                and \n", "                    is_pos_outlet_active = 1\n", "                and \n", "                    is_mapping_enabled = true\n", "                and \n", "                    is_express_store = true\n", "                and \n", "                    is_current_mapping_active = true\n", "                and \n", "                    is_current = true\n", "                and \n", "                    pos_outlet_name <> 'SS Gurgaon Test Store'\n", "        ),\n", "\n", "    fin_l25_l as (\n", "        select \n", "            date(current_date - interval '25' day) as etl_create_ts_ist,\n", "            hex_id,\n", "            outlet_id,\n", "            outlet_name\n", "        from \n", "        \n", "        (\n", "                select\n", "                    merchant_id,\n", "                    a.action,\n", "                    hex_id,\n", "                    etl_create_ts_ist,\n", "                    rank() over (partition by merchant_id order by date(etl_create_ts_ist) desc) as rk,\n", "                    case \n", "                    when pos_outlet_city_name = 'HR-NCR' then 'Gurgaon'\n", "                    when pos_outlet_city_name = 'UP-NCR' then 'Ghaziabad'\n", "                    else pos_outlet_city_name end as city,\n", "                    pos_outlet_name as outlet_name,\n", "                    fm.pos_outlet_id as outlet_id\n", "                from dwh.dim_merchant_polygon_hex_enriched a\n", "                left join frontend_merchant_mapping fm on fm.frontend_merchant_id = a.merchant_id\n", "                where action in ('POLYGON_UPDATED', 'STORE_ADDED')\n", "                 and \n", "                 etl_create_ts_ist < date(current_date - interval '25' day) \n", "                 and type = 'STORE_POLYGON'\n", "                 and pos_outlet_id in {outlet_id_list}\n", "        )\n", "        WHERE rk = 1\n", "        ),\n", "        \n", "    fin_l25_g as \n", "        (    select\n", "                etl_create_ts_ist,\n", "                hex_id,\n", "                fm.pos_outlet_id as outlet_id,\n", "                pos_outlet_name as outlet_name\n", "            from dwh.dim_merchant_polygon_hex_enriched a\n", "            left join frontend_merchant_mapping fm on fm.frontend_merchant_id = a.merchant_id\n", "            where action in ('POLYGON_UPDATED', 'STORE_ADDED')\n", "             and \n", "            etl_create_ts_ist >= date(current_date - interval '25' day)\n", "            and type = 'STORE_POLYGON'\n", "            and pos_outlet_id in {outlet_id_list}\n", "        ),\n", "            \n", "    pre_fin as ( select \n", "                etl_create_ts_ist as date_,\n", "                outlet_id,\n", "                outlet_name,\n", "                hex_id\n", "            from\n", "                fin_l25_l\n", "            \n", "            UNION \n", "            \n", "            select\n", "                etl_create_ts_ist  as date_,\n", "                outlet_id,\n", "                outlet_name,\n", "                hex_id\n", "            from\n", "                fin_l25_g\n", "          )\n", "    select distinct\n", "        a.outlet_id,\n", "        hex_id\n", "    from \n", "        pre_fin a\n", "    inner join (select outlet_id,max(date_) date_ from pre_fin group by 1) b on a.outlet_id = b.outlet_id and a.date_ = b.date_     \n", "\"\"\"\n", "live_hex_df = read_sql_query(live_hex, trino)\n", "\n", "live_hex_df[\"outlet_id\"] = live_hex_df[\"outlet_id\"].astype(int)\n", "live_hex_df[\"live_overlap\"] = live_hex_df.groupby([\"hex_id\"])[\"outlet_id\"].transform(\"nunique\")\n", "live_hex_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "540120a2-0478-4bfb-a8ae-c0ece37e8423", "metadata": {}, "outputs": [], "source": ["if transfer_date >= today_date:\n", "    live_hex_df = live_hex_df.merge(\n", "        base_pre_df[\n", "            [\"outlet_id\", \"item_id\", \"city\", \"facility_id\", \"be_facility_id\"]\n", "        ].drop_duplicates(),\n", "        on=[\"outlet_id\"],\n", "        how=\"left\",\n", "    )\n", "else:\n", "    live_hex_df = base_pre_df[(base_pre_df.date_ == tday)].drop_duplicates()\n", "    live_hex_df[\"live_overlap\"] = live_hex_df[\"overlap\"]\n", "    live_hex_df.drop(columns={\"date_\"}, inplace=True)\n", "\n", "live_hex_df = live_hex_df.drop_duplicates()\n", "current_ovlp = live_hex_df[live_hex_df.live_overlap > 1].copy()\n", "current_ovlp = current_ovlp[[\"hex_id\", \"outlet_id\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "675e428e-540a-4a60-9bba-776e1d059343", "metadata": {}, "outputs": [], "source": ["current_ovlp.head()"]}, {"cell_type": "markdown", "id": "cf57a20b-ecca-4cc8-b7c9-7cc0d683ea06", "metadata": {}, "source": ["## overlapping and boundary hex share"]}, {"cell_type": "code", "execution_count": null, "id": "bf8bdc86-c0a1-411e-9522-77a8bca78bdb", "metadata": {"tags": []}, "outputs": [], "source": ["ovlp_ss = ovlp_hex.merge(\n", "    sales_df[[\"date_\", \"outlet_id\", \"item_id\", \"hex_id\", \"sales\"]],\n", "    on=[\"date_\", \"outlet_id\", \"item_id\", \"hex_id\"],\n", "    how=\"left\",\n", ")\n", "ovlp_ss[\"sales\"] = ovlp_ss[\"sales\"].fillna(0)\n", "ovlp_ss = ovlp_ss[[\"date_\", \"outlet_id\", \"item_id\", \"hex_id\", \"sales\"]]\n", "ovlp_ss = ovlp_ss.merge(current_ovlp, on=[\"hex_id\", \"outlet_id\"], how=\"inner\")\n", "ovlp_ss.head()"]}, {"cell_type": "code", "execution_count": null, "id": "152ba227-f7bb-4160-ad2c-5227ace5eac0", "metadata": {}, "outputs": [], "source": ["del [ovlp_hex]\n", "gc.collect"]}, {"cell_type": "code", "execution_count": null, "id": "5b38002c-6a8f-4c37-ad2b-9bec3fd8658e", "metadata": {}, "outputs": [], "source": ["bound_ovlp_sales_share_df = ovlp_ss.copy()\n", "bound_ovlp_sales_share_df[\"hex_item_sales\"] = bound_ovlp_sales_share_df.groupby(\n", "    [\"hex_id\", \"date_\", \"item_id\"]\n", ")[\"sales\"].transform(\"sum\")\n", "bound_ovlp_sales_share_df[\"sales\"] = bound_ovlp_sales_share_df[\"sales\"].fillna(0)\n", "bound_ovlp_sales_share_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "935d084c-2a1a-4d8c-a20d-823573196b98", "metadata": {"tags": []}, "outputs": [], "source": ["hex_store_share = (\n", "    bound_ovlp_sales_share_df[bound_ovlp_sales_share_df.date_ >= l7d]\n", "    .groupby([\"outlet_id\", \"item_id\", \"hex_id\"])\n", "    .agg({\"hex_item_sales\": \"mean\", \"sales\": \"mean\"})\n", "    .reset_index()\n", ")\n", "hex_store_share[\"store_share\"] = (\n", "    hex_store_share[\"sales\"] / hex_store_share[\"hex_item_sales\"]\n", ").<PERSON>na(0)\n", "hex_store_share.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0d1fd021-9b85-4d85-9120-7abd3fd92019", "metadata": {}, "outputs": [], "source": ["hex_store_share[[\"outlet_id\", \"item_id\", \"hex_id\"]].drop_duplicates().shape"]}, {"cell_type": "code", "execution_count": null, "id": "c0bb520b-7a9f-4952-aa1f-fa7cd2ef4a79", "metadata": {}, "outputs": [], "source": ["del [bound_ovlp_sales_share_df]\n", "gc.collect"]}, {"cell_type": "code", "execution_count": null, "id": "6407dabd-b265-47ae-9786-d77182ef9705", "metadata": {}, "outputs": [], "source": ["boundary_hex_sales = (\n", "    bound_df.groupby([\"date_\", \"hex_id\", \"outlet_id\", \"item_id\"])\n", "    .agg(\n", "        {\"sales\": \"sum\", \"avail\": \"mean\", \"lin_extp\": \"sum\", \"exp_extp\": \"sum\", \"para_extp\": \"sum\"}\n", "    )\n", "    .reset_index()\n", ")\n", "boundary_hex_sales.head()"]}, {"cell_type": "markdown", "id": "0fb01dc7-5cb4-4574-9a91-4f8fa41797fd", "metadata": {}, "source": ["## Handling boundary unmapped hexes"]}, {"cell_type": "code", "execution_count": null, "id": "70eeb472-e7c1-4142-b535-6e13659705e1", "metadata": {}, "outputs": [], "source": ["live_hex_df[\"flag\"] = 1\n", "unmapped_boundary_sales = boundary_hex_sales.merge(\n", "    live_hex_df[[\"hex_id\", \"flag\"]],\n", "    on=[\"hex_id\"],\n", "    how=\"left\",\n", ")\n", "unmapped_boundary_sales = unmapped_boundary_sales[unmapped_boundary_sales.flag != 1]\n", "unmapped_boundary_sales.drop(columns={\"flag\", \"avail\"}, inplace=True)\n", "live_hex_df.drop(columns={\"flag\"}, inplace=True)\n", "\n", "unmapped_boundary_sales.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6f3037d9-72bf-48a1-a3ce-3b4248c01147", "metadata": {}, "outputs": [], "source": ["last_outlet_sale = (\n", "    unmapped_boundary_sales[unmapped_boundary_sales.sales > 0][[\"hex_id\", \"date_\", \"outlet_id\"]]\n", "    .copy()\n", "    .drop_duplicates()\n", ")\n", "last_outlet_sale[\"rank\"] = last_outlet_sale.groupby([\"outlet_id\", \"hex_id\"])[\"date_\"].rank(\n", "    method=\"dense\", ascending=False\n", ")\n", "last_outlet_sale = last_outlet_sale[last_outlet_sale[\"rank\"] == 1]\n", "last_outlet_sale.drop(columns={\"date_\", \"rank\"}, inplace=True)\n", "last_outlet_sale = last_outlet_sale.merge(\n", "    unmapped_boundary_sales.drop(columns={\"outlet_id\"}), on=[\"hex_id\"]\n", ")\n", "\n", "last_outlet_sale.head()"]}, {"cell_type": "code", "execution_count": null, "id": "5266588d-0c15-4df6-b24b-9f299e3ca878", "metadata": {}, "outputs": [], "source": ["del [unmapped_boundary_sales]\n", "gc.collect"]}, {"cell_type": "code", "execution_count": null, "id": "b7e00012-13d8-49e9-99ab-ed22d6d90f2b", "metadata": {}, "outputs": [], "source": ["unmapped_boundary_sales = last_outlet_sale.merge(\n", "    base_pre_df[\n", "        [\"outlet_id\", \"facility_id\", \"date_\", \"be_facility_id\", \"city\", \"item_id\"]\n", "    ].drop_duplicates(),\n", "    on=[\"outlet_id\", \"date_\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "unmapped_boundary_sales.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c54f6487-4a73-4998-b618-dc1059f005bc", "metadata": {}, "outputs": [], "source": ["non_ovlp_df.drop(columns={\"outlet_id\"}, inplace=True)\n", "boundary_hex_sales.drop(columns={\"outlet_id\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "0fc9bb60-b68a-44ac-9320-2bb9fc0ef194", "metadata": {}, "outputs": [], "source": ["final_hex_sales = pd.concat([boundary_hex_sales, non_ovlp_df, ovlp_df])\n", "final_hex_sales = (\n", "    final_hex_sales.groupby([\"date_\", \"hex_id\", \"item_id\"])\n", "    .agg({\"sales\": \"sum\", \"lin_extp\": \"sum\", \"exp_extp\": \"sum\", \"para_extp\": \"sum\"})\n", "    .reset_index()\n", ")\n", "print(final_hex_sales.shape)\n", "final_hex_sales.head()"]}, {"cell_type": "code", "execution_count": null, "id": "eb729826-2355-47d3-8de3-be7e118d1f02", "metadata": {"tags": []}, "outputs": [], "source": ["final_hex_sales[\n", "    (final_hex_sales[\"hex_id\"] == \"896189276a7ffff\") & (final_hex_sales[\"item_id\"] == 10000834)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "2818ae2e-b429-4371-9e8f-c08278fc4f54", "metadata": {}, "outputs": [], "source": ["del [boundary_hex_sales, non_ovlp_df, ovlp_df]\n", "gc.collect"]}, {"cell_type": "code", "execution_count": null, "id": "b38f01ed-7d1b-443c-9f5b-a21b0c8f0c53", "metadata": {"tags": []}, "outputs": [], "source": ["live_sales_hex_mapping = live_hex_df.merge(final_hex_sales, on=[\"hex_id\", \"item_id\"], how=\"left\")\n", "live_sales_hex_mapping = live_sales_hex_mapping.dropna()\n", "live_sales_hex_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7ee03440-ea72-452d-9b2d-ef297e1f6d88", "metadata": {}, "outputs": [], "source": ["live_sales_hex_mapping[\n", "    (live_sales_hex_mapping[\"outlet_id\"] == 5072) & (live_sales_hex_mapping[\"item_id\"] == 10000834)\n", "].sort_values(\"sales\", ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "7ca4930b-5619-4f40-96a1-96c439e6e39e", "metadata": {}, "outputs": [], "source": ["live_sales_hex_mapping = live_sales_hex_mapping.merge(\n", "    hex_store_share[[\"outlet_id\", \"item_id\", \"hex_id\", \"store_share\"]],\n", "    on=[\"outlet_id\", \"item_id\", \"hex_id\"],\n", "    how=\"left\",\n", ")\n", "live_sales_hex_mapping[\"store_share\"] = live_sales_hex_mapping[\"store_share\"].fillna(1)"]}, {"cell_type": "code", "execution_count": null, "id": "aaaa70d4-1e41-4523-807e-645109fa57d4", "metadata": {}, "outputs": [], "source": ["del [hex_store_share]\n", "gc.collect"]}, {"cell_type": "code", "execution_count": null, "id": "eba080b1-ca2d-4866-bb2a-65d7f204d111", "metadata": {}, "outputs": [], "source": ["# live_hex_df['live_overlap'] = live_hex_df['overlap']"]}, {"cell_type": "code", "execution_count": null, "id": "1496213c-4da3-413b-ad34-ae039103bc4d", "metadata": {}, "outputs": [], "source": ["live_sales_hex_mapping[\"sales\"] = np.where(\n", "    live_sales_hex_mapping[\"live_overlap\"] > 1,\n", "    live_sales_hex_mapping[\"sales\"] * live_sales_hex_mapping[\"store_share\"],\n", "    live_sales_hex_mapping[\"sales\"],\n", ")\n", "live_sales_hex_mapping[\"lin_extp\"] = np.where(\n", "    live_sales_hex_mapping[\"live_overlap\"] > 1,\n", "    live_sales_hex_mapping[\"lin_extp\"] * live_sales_hex_mapping[\"store_share\"],\n", "    live_sales_hex_mapping[\"lin_extp\"],\n", ")\n", "live_sales_hex_mapping[\"exp_extp\"] = np.where(\n", "    live_sales_hex_mapping[\"live_overlap\"] > 1,\n", "    live_sales_hex_mapping[\"exp_extp\"] * live_sales_hex_mapping[\"store_share\"],\n", "    live_sales_hex_mapping[\"exp_extp\"],\n", ")\n", "live_sales_hex_mapping[\"para_extp\"] = np.where(\n", "    live_sales_hex_mapping[\"live_overlap\"] > 1,\n", "    live_sales_hex_mapping[\"para_extp\"] * live_sales_hex_mapping[\"store_share\"],\n", "    live_sales_hex_mapping[\"para_extp\"],\n", ")\n", "\n", "live_sales_hex_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8fc4a94b-3107-47f9-8bdf-49605d1b3764", "metadata": {}, "outputs": [], "source": ["live_sales_hex_mapping[live_sales_hex_mapping.facility_id == 2273].groupby([\"date_\"]).sum(\"sales\")"]}, {"cell_type": "code", "execution_count": null, "id": "45960dc2-2ce8-4d87-ac16-77d39e08332c", "metadata": {}, "outputs": [], "source": ["store_item_sales = (\n", "    live_sales_hex_mapping.groupby(\n", "        [\"date_\", \"city\", \"facility_id\", \"be_facility_id\", \"outlet_id\", \"item_id\"]\n", "    )\n", "    .agg({\"sales\": \"sum\", \"lin_extp\": \"sum\", \"exp_extp\": \"sum\", \"para_extp\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "unmapped_boundary_sales = (\n", "    unmapped_boundary_sales.groupby(\n", "        [\"date_\", \"city\", \"facility_id\", \"be_facility_id\", \"outlet_id\", \"item_id\"]\n", "    )\n", "    .agg({\"sales\": \"sum\", \"lin_extp\": \"sum\", \"exp_extp\": \"sum\", \"para_extp\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "store_item_level_sales = pd.concat([store_item_sales, unmapped_boundary_sales]).drop_duplicates()\n", "\n", "store_item_level_sales = (\n", "    store_item_level_sales.groupby(\n", "        [\"date_\", \"city\", \"facility_id\", \"be_facility_id\", \"outlet_id\", \"item_id\"]\n", "    )\n", "    .agg({\"sales\": \"sum\", \"lin_extp\": \"sum\", \"exp_extp\": \"sum\", \"para_extp\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "71ef7798-eaef-452c-b8dc-5ce16be73514", "metadata": {}, "outputs": [], "source": ["store_item_level_sales[\"tdate\"] = transfer_date\n", "store_item_level_sales[\"tdow\"] = pd.to_datetime(store_item_level_sales[\"tdate\"]).dt.dayofweek\n", "store_item_level_sales[\"dow\"] = pd.to_datetime(store_item_level_sales[\"date_\"]).dt.dayofweek\n", "store_item_level_sales.head()"]}, {"cell_type": "code", "execution_count": null, "id": "31fc8b99-194b-4ae5-a24f-1c4a2ff8d2f3", "metadata": {}, "outputs": [], "source": ["store_item_level_sales[\"slot\"] = np.where(current_hour >= 10, \"6 to 23\", \"17 to 23\")"]}, {"cell_type": "code", "execution_count": null, "id": "d52fdb45-cdc7-4799-a74d-f414b25b41b0", "metadata": {}, "outputs": [], "source": ["store_item_level_sales.head()"]}, {"cell_type": "code", "execution_count": null, "id": "cb7fee64-1b62-49e6-bfa1-9708418a3122", "metadata": {}, "outputs": [], "source": ["data = store_item_level_sales.copy()\n", "data[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))\n", "data[\"tdate\"] = pd.to_datetime(data[\"tdate\"])\n", "data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2762ae60-a168-43f4-9c07-95f781cfc572", "metadata": {}, "outputs": [], "source": ["columns = [\n", "    \"date_\",\n", "    \"city\",\n", "    \"be_facility_id\",\n", "    \"facility_id\",\n", "    \"outlet_id\",\n", "    \"item_id\",\n", "    \"tdate\",\n", "    \"sales\",\n", "    \"lin_extp\",\n", "    \"exp_extp\",\n", "    \"para_extp\",\n", "    \"tdow\",\n", "    \"dow\",\n", "    \"slot\",\n", "    \"updated_at\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "18b50864-e497-4596-a88d-60ca3d554fba", "metadata": {}, "outputs": [], "source": ["data = data[columns]"]}, {"cell_type": "code", "execution_count": null, "id": "a9805974-7ba1-4de5-aa70-e9f0e19ec15b", "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "37842d4d-c9e1-4291-a4cb-d1b921889d2a", "metadata": {}, "outputs": [], "source": ["data.dtypes"]}, {"cell_type": "markdown", "id": "c9cf5f89-9f85-4d37-a098-4ec03a30bff1", "metadata": {}, "source": ["## data push to table"]}, {"cell_type": "code", "execution_count": null, "id": "92b42f91-34ce-4f05-8514-5cafe3864655", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"TIMESTAMP(6)\", \"description\": \"sales date\"},\n", "    {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"city\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"be_facility_id\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility_id\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"outlet_id\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "    {\"name\": \"tdate\", \"type\": \"TIMESTAMP(6)\", \"description\": \"transfer_date\"},\n", "    {\"name\": \"sales\", \"type\": \"double\", \"description\": \"actual sales\"},\n", "    {\"name\": \"lin_extp\", \"type\": \"double\", \"description\": \"linear extrapolated sales\"},\n", "    {\n", "        \"name\": \"exp_extp\",\n", "        \"type\": \"double\",\n", "        \"description\": \"exponential extrapolated sales\",\n", "    },\n", "    {\"name\": \"para_extp\", \"type\": \"double\", \"description\": \"parabolic extrapolated sales\"},\n", "    {\"name\": \"tdow\", \"type\": \"integer\", \"description\": \"transfer day of week\"},\n", "    {\"name\": \"dow\", \"type\": \"integer\", \"description\": \"sales day of week\"},\n", "    {\"name\": \"slot\", \"type\": \"varchar\", \"description\": \"data prep for slot A/B\"},\n", "    {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(6)\", \"description\": \"updated_at\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "ad6e8710-f08a-4535-8681-75b53bd8809b", "metadata": {"tags": []}, "outputs": [], "source": ["if current_hour >= 10:\n", "    kwargs = {\n", "        \"schema_name\": \"supply_etls\",\n", "        \"table_name\": \"milk_outlet_hex_sales\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"facility_id\", \"item_id\", \"tdate\"],\n", "        \"partition_key\": [\"updated_at\"],\n", "        \"incremental_key\": \"updated_at\",\n", "        \"load_type\": \"append\",  # append, truncate or upsert,\n", "        \"table_description\": \"this table contains outlet_item level sales and extrapolated sales for last 17 days based on their current hex_mapping\",\n", "    }\n", "else:\n", "    kwargs = {\n", "        \"schema_name\": \"supply_etls\",\n", "        \"table_name\": \"milk_outlet_hex_sales\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"facility_id\", \"item_id\", \"tdate\"],\n", "        \"partition_key\": [\"updated_at\"],\n", "        \"incremental_key\": \"updated_at\",\n", "        \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "        \"table_description\": \"this table contains outlet_item level sales and extrapolated sales for last 17 days based on their current hex_mapping\",\n", "    }\n", "\n", "pb.to_trino(data, **kwargs)"]}, {"cell_type": "markdown", "id": "d1e10f1f-0593-4d71-b737-4b039d5aa55e", "metadata": {}, "source": ["## data push to table logs"]}, {"cell_type": "code", "execution_count": null, "id": "cb4651fa-8924-4ca0-94bc-987e35785087", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"TIMESTAMP(6)\", \"description\": \"sales date\"},\n", "    {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"city\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"be_facility_id\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility_id\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"outlet_id\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "    {\"name\": \"tdate\", \"type\": \"TIMESTAMP(6)\", \"description\": \"transfer_date\"},\n", "    {\"name\": \"sales\", \"type\": \"double\", \"description\": \"actual sales\"},\n", "    {\"name\": \"lin_extp\", \"type\": \"double\", \"description\": \"linear extrapolated sales\"},\n", "    {\n", "        \"name\": \"exp_extp\",\n", "        \"type\": \"double\",\n", "        \"description\": \"exponential extrapolated sales\",\n", "    },\n", "    {\"name\": \"para_extp\", \"type\": \"double\", \"description\": \"parabolic extrapolated sales\"},\n", "    {\"name\": \"tdow\", \"type\": \"integer\", \"description\": \"transfer day of week\"},\n", "    {\"name\": \"dow\", \"type\": \"integer\", \"description\": \"sales day of week\"},\n", "    {\"name\": \"slot\", \"type\": \"varchar\", \"description\": \"data prep for slot A/B\"},\n", "    {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(6)\", \"description\": \"updated_at\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "6b10be77-39d9-43c0-82fd-57d96ba49802", "metadata": {"tags": []}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"milk_outlet_hex_sales_logs\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"facility_id\", \"item_id\", \"date_\", \"tdate\"],\n", "    \"partition_key\": [\"updated_at\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"append\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains outlet_item level sales and extrapolated sales logs for last 21 days based on their current hex_mapping\",\n", "}\n", "pb.to_trino(data, **kwargs)"]}, {"cell_type": "markdown", "id": "00e5a427-2a9a-4a91-bed9-cc655876a9d9", "metadata": {}, "source": ["## hex mapping push to table"]}, {"cell_type": "code", "execution_count": null, "id": "6d9a18d5-7376-429c-b4f6-51adeb8b3843", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"last_poly_updated\", \"type\": \"TIMESTAMP(6)\", \"description\": \"last polygon picked\"},\n", "    {\"name\": \"date_\", \"type\": \"TIMESTAMP(6)\", \"description\": \"date\"},\n", "    {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"city\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"be_facility_id\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility_id\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"outlet_id\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "    {\"name\": \"hex_id\", \"type\": \"varchar\", \"description\": \"hex_id\"},\n", "    {\"name\": \"overlap\", \"type\": \"integer\", \"description\": \"store mapped to this hex\"},\n", "    {\n", "        \"name\": \"overlap_max\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"hex_shared by max how many stores, >1 means poly overlap hai\",\n", "    },\n", "    {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(6)\", \"description\": \"updated_at\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "0fdb64a9-2827-401a-9abc-ae003e659017", "metadata": {}, "outputs": [], "source": ["base_pre_df[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))\n", "base_pre_df[\"last_poly_updated\"] = pd.to_datetime(base_pre_df[\"last_poly_updated\"])"]}, {"cell_type": "code", "execution_count": null, "id": "90868c30-116e-40f6-9b99-14e727b1c217", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"outlet_hex_mapping\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"facility_id\", \"item_id\", \"date_\"],\n", "    \"partition_key\": [\"updated_at\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains outlet_item level sales and extrapolated sales for last 17 days based on their current hex_mapping\",\n", "}\n", "pb.to_trino(base_pre_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "a1360999-3d87-495a-97b8-27576b0a08f8", "metadata": {}, "outputs": [], "source": ["del [data]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "aaff630a-8369-46a6-90c5-542aa967f258", "metadata": {}, "outputs": [], "source": ["def send_alert(date, slot):\n", "    channel = \"perishable-dag-alerts\"\n", "    text = f\"milk hex level sales has been updated for {slot} for {date} \\n cc <@U06T927GCSW> <@U078DPW05T4>\"\n", "    pb.send_slack_message(channel=channel, text=text)"]}, {"cell_type": "code", "execution_count": null, "id": "2544fd5a-144b-4f0a-938a-2f927fc104df", "metadata": {"tags": []}, "outputs": [], "source": ["if current_hour < 10:\n", "    send_alert(tday, slot)\n", "    print(f\"Current hour is {current_hour}. Running till here for slot B data only.\")\n", "    sys.exit()"]}, {"cell_type": "markdown", "id": "dfa46038-3120-4017-9f73-ae057cf455d7", "metadata": {}, "source": ["## For SLot A (6am - 5:59pm)"]}, {"cell_type": "code", "execution_count": null, "id": "caa3949b-ff81-45e3-9fdb-6bfaa7584005", "metadata": {"tags": []}, "outputs": [], "source": ["sales_df = (\n", "    sales[sales[\"hour_\"].between(h1, h3)]\n", "    .groupby([\"date_\", \"city\", \"hex_id\", \"outlet_id\", \"item_id\"])\n", "    .agg({\"sales\": \"sum\"})\n", "    .reset_index()\n", ")\n", "sales_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4cd5c951-18fd-458a-b9ae-8339994e767d", "metadata": {}, "outputs": [], "source": ["avail_df = avail_df[avail_df[\"hour_\"].between(h1, h3)]\n", "avail_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "1f9677d1-161f-499c-971d-74314ea22755", "metadata": {}, "outputs": [], "source": ["hr_wts = read_sql_query(\n", "    \"\"\"SELECT DISTINCT city, order_hour AS hour_, CAST(weights AS DOUBLE) AS chw\n", "                        FROM supply_etls.city_hour_weights a\n", "                        WHERE a.updated_at IN (SELECT MAX(updated_at) FROM supply_etls.city_hour_weights where updated_at >= current_date - interval '32' day)\n", "                         and a.updated_at >= current_date - interval '32' day\"\"\",\n", "    trino,\n", ")\n", "hr_wts.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "384f7d7e-54f8-464c-a4e4-6ad6e26fc848", "metadata": {}, "outputs": [], "source": ["ovlp_hex = base_pre_df[base_pre_df.overlap > 1].copy()\n", "non_onvl_hex = base_pre_df[base_pre_df.overlap == 1].copy()"]}, {"cell_type": "code", "execution_count": null, "id": "ae8cc718-5cd9-4f74-8c10-8b6884521396", "metadata": {}, "outputs": [], "source": ["non_ovlp = f\"\"\"\n", "with base as (\n", "select \n", "    b.date_ as date_,\n", "    b.city,\n", "    b.be_facility_id,\n", "    b.outlet_id,\n", "    b.facility_id,\n", "    b.hex_id,\n", "    b.item_id,\n", "    coalesce(sales,0) as sales\n", "from \n", "    non_onvl_hex b\n", "    left join sales_df s on \n", "        cast(b.outlet_id as int) = s.outlet_id  \n", "        and date(b.date_) = s.date_ \n", "        and b.hex_id = s.hex_id \n", "        and cast(b.item_id as int) = s.item_id \n", "),\n", "\n", "avail as (\n", "    select\n", "        a.date_,\n", "        a.city,\n", "        a.facility_id,\n", "        a.item_id,\n", "        sum(is_available*chw)/sum(chw) as avail\n", "    from avail_df a\n", "    left join hr_wts h on h.city = a.city and h.hour_ = a.hour_\n", "group by 1,2,3,4\n", ")\n", "\n", "select\n", "    b.date_,\n", "    b.outlet_id,\n", "    b.hex_id,\n", "    b.item_id,\n", "    sales,\n", "    avail,\n", "    sales * (1 + 0.35 * (1-coalesce(avail,1))) as lin_extp,\n", "    sales * (5 ** (0.50 * (1 - coalesce(avail,1)))) as exp_extp,\n", "    sales * (1 + ((1 - coalesce(avail,1)) ** 2) / (4 * 0.08)) para_extp\n", "from\n", "    base b\n", "left join avail a on \n", "        b.facility_id = a.facility_id\n", "        and b.item_id = a.item_id \n", "        and b.date_ = a.date_\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "c8b5f0aa-b3e7-47e4-9311-b7e0c635afac", "metadata": {}, "outputs": [], "source": ["non_ovlp_df = duckdb.query(non_ovlp).to_df()\n", "non_ovlp_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b6336dff-bd83-4d32-b1e9-ce28ab755bd4", "metadata": {}, "outputs": [], "source": ["ovlp = f\"\"\"\n", "with base as (\n", "    select \n", "        date(b.date_) as date_,\n", "        b.city,\n", "        b.be_facility_id,\n", "        cast(b.outlet_id as int) outlet_id,\n", "        cast(b.facility_id as int) facility_id,\n", "        b.hex_id,\n", "        cast(b.item_id as int) item_id,\n", "        sales,\n", "        sum(sales) over (partition by b.hex_id,b.item_id,b.date_) as net_sales\n", "    from \n", "        ovlp_hex b\n", "        left join sales_df s on \n", "            cast(b.outlet_id as int) = s.outlet_id  \n", "            and date(b.date_) = s.date_ \n", "            and b.hex_id = s.hex_id \n", "            and cast(b.item_id as int) = s.item_id \n", ")\n", ",\n", "\n", "avail as (\n", "    select \n", "        date_,\n", "        hex_id,\n", "        item_id,\n", "        coalesce(sales,0) as sales,\n", "        sum(is_available*chw)/sum(chw) as avail\n", "    from \n", "        (select\n", "            b.date_,\n", "            b.city,\n", "            b.item_id,\n", "            hex_id,\n", "            hour_,\n", "            net_sales sales,\n", "            max(is_available) as is_available\n", "        from \n", "        base b\n", "        left join avail_df a on date(a.date_) = b.date_ and cast(a.item_id as int) = b.item_id and cast(a.facility_id as int) = b.facility_id\n", "        group by 1,2,3,4,5,6\n", "        ) a\n", "    left join hr_wts h on h.city = a.city and h.hour_ = a.hour_\n", "    GROUP BY 1,2,3,4\n", ")\n", "\n", "select\n", "    date_,\n", "    hex_id,\n", "    item_id,\n", "    sales,\n", "    avail,\n", "    sales*(1+0.35*(1-coalesce(avail,1))) as lin_extp,\n", "    sales * (5 ** (0.50 * (1 - coalesce(avail,1)))) as exp_extp,\n", "    sales * (1 + ((1 - coalesce(avail,1)) ** 2) / (4 * 0.08)) para_extp\n", "from\n", "    avail\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "34f829fa-9c66-4181-8f51-8f8c80a3f405", "metadata": {}, "outputs": [], "source": ["ovlp_df = duckdb.query(ovlp).to_df()\n", "ovlp_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c4f59270-b257-4ece-b7df-bb42fe0a5bf4", "metadata": {}, "outputs": [], "source": ["boundary = f\"\"\"\n", "with base as (\n", "    select \n", "        date(s.date_) as date_,\n", "        cast(s.outlet_id as int) outlet_id,\n", "        s.city,\n", "        s.hex_id as hex_id,\n", "        cast(s.item_id as int) item_id,\n", "        s.sales\n", "    from \n", "        sales_df s\n", "    left join\n", "        base_pre_df b\n", "        on cast(s.outlet_id as int) = cast(b.outlet_id as int)\n", "        and date(s.date_) = date(b.date_)\n", "        and s.hex_id = b.hex_id\n", "    where\n", "        b.hex_id is null\n", "),\n", "\n", "avb as (\n", "    select \n", "        date_,\n", "        hex_id,\n", "        outlet_id,\n", "        item_id,\n", "        sales,\n", "        sum(is_available*chw)/sum(chw) as avail\n", "    from \n", "    (  select\n", "            b.date_,\n", "            b.city,\n", "            b.outlet_id,\n", "            b.item_id,\n", "            hex_id,\n", "            hour_,\n", "            sales,\n", "            max(is_available) as is_available\n", "        from \n", "            base b\n", "        left join avail_df a on date(a.date_) = b.date_ and cast(a.item_id as int) = b.item_id and cast(a.outlet_id as int) = b.outlet_id\n", "            group by 1,2,3,4,5,6,7\n", "    ) as a\n", "    left join hr_wts h on h.city = a.city and h.hour_ = a.hour_\n", "    GROUP BY 1,2,3,4,5\n", ")\n", "\n", "select\n", "    b.date_,\n", "    b.outlet_id,\n", "    b.hex_id,\n", "    b.item_id,\n", "    sales,\n", "    avail,\n", "    sales * (1+0.35*(1-coalesce(avail,1))) as lin_extp,\n", "    sales * (5 ** (0.50 * (1 - coalesce(avail,1)))) as exp_extp,\n", "    sales * (1 + ((1 - coalesce(avail,1)) ** 2) / (4 * 0.08)) para_extp\n", "from\n", "    avb b\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "1f15804e-f7ec-409e-b315-66352f8f818c", "metadata": {}, "outputs": [], "source": ["bound_df = duckdb.query(boundary).to_df()\n", "bound_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ebc28cf6-48fa-4da7-a0ad-f2ac8feffc60", "metadata": {}, "outputs": [], "source": ["bound_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "f753c743-5591-407a-9dd4-c777e94f45f4", "metadata": {"tags": []}, "outputs": [], "source": ["del [avail_df, non_onvl_hex]\n", "gc.collect"]}, {"cell_type": "code", "execution_count": null, "id": "ac1856bb-1000-46bf-a15e-53a366b86c0f", "metadata": {}, "outputs": [], "source": ["ovlp_hex.head()"]}, {"cell_type": "code", "execution_count": null, "id": "294aea7f-13c1-4ccd-830f-3a96f6c7fcaa", "metadata": {"tags": []}, "outputs": [], "source": ["live_hex = f\"\"\"\n", "with frontend_merchant_mapping as\n", "        (select * from dwh.dim_merchant_outlet_facility_mapping\n", "            where \n", "                is_frontend_merchant_active = true\n", "                and\n", "                    is_backend_merchant_active = true\n", "                and \n", "                    is_pos_outlet_active = 1\n", "                and \n", "                    is_mapping_enabled = true\n", "                and \n", "                    is_express_store = true\n", "                and \n", "                    is_current_mapping_active = true\n", "                and \n", "                    is_current = true\n", "                and \n", "                    pos_outlet_name <> 'SS Gurgaon Test Store'\n", "        ),\n", "\n", "    fin_l25_l as (\n", "        select \n", "            date(current_date - interval '25' day) as etl_create_ts_ist,\n", "            hex_id,\n", "            outlet_id,\n", "            outlet_name\n", "        from \n", "        \n", "        (\n", "                select\n", "                    merchant_id,\n", "                    a.action,\n", "                    hex_id,\n", "                    etl_create_ts_ist,\n", "                    rank() over (partition by merchant_id order by date(etl_create_ts_ist) desc) as rk,\n", "                    case \n", "                    when pos_outlet_city_name = 'HR-NCR' then 'Gurgaon'\n", "                    when pos_outlet_city_name = 'UP-NCR' then 'Ghaziabad'\n", "                    else pos_outlet_city_name end as city,\n", "                    pos_outlet_name as outlet_name,\n", "                    fm.pos_outlet_id as outlet_id\n", "                from dwh.dim_merchant_polygon_hex_enriched a\n", "                left join frontend_merchant_mapping fm on fm.frontend_merchant_id = a.merchant_id\n", "                where action in ('POLYGON_UPDATED', 'STORE_ADDED')\n", "                 and \n", "                 etl_create_ts_ist < date(current_date - interval '25' day) \n", "                 and type = 'STORE_POLYGON'\n", "                 and pos_outlet_id in {outlet_id_list}\n", "        )\n", "        WHERE rk = 1\n", "        ),\n", "        \n", "    fin_l25_g as \n", "        (    select\n", "                etl_create_ts_ist,\n", "                hex_id,\n", "                fm.pos_outlet_id as outlet_id,\n", "                pos_outlet_name as outlet_name\n", "            from dwh.dim_merchant_polygon_hex_enriched a\n", "            left join frontend_merchant_mapping fm on fm.frontend_merchant_id = a.merchant_id\n", "            where action in ('POLYGON_UPDATED', 'STORE_ADDED')\n", "             and \n", "            etl_create_ts_ist >= date(current_date - interval '25' day)\n", "            and type = 'STORE_POLYGON'\n", "            and pos_outlet_id in {outlet_id_list}\n", "        ),\n", "            \n", "    pre_fin as ( select \n", "                etl_create_ts_ist as date_,\n", "                outlet_id,\n", "                outlet_name,\n", "                hex_id\n", "            from\n", "                fin_l25_l\n", "            \n", "            UNION \n", "            \n", "            select\n", "                etl_create_ts_ist  as date_,\n", "                outlet_id,\n", "                outlet_name,\n", "                hex_id\n", "            from\n", "                fin_l25_g\n", "          )\n", "    select distinct\n", "        a.outlet_id,\n", "        hex_id\n", "    from \n", "        pre_fin a\n", "    inner join (select outlet_id,max(date_) date_ from pre_fin group by 1) b on a.outlet_id = b.outlet_id and a.date_ = b.date_     \n", "\"\"\"\n", "live_hex_df = read_sql_query(live_hex, trino)\n", "\n", "live_hex_df[\"outlet_id\"] = live_hex_df[\"outlet_id\"].astype(int)\n", "live_hex_df[\"live_overlap\"] = live_hex_df.groupby([\"hex_id\"])[\"outlet_id\"].transform(\"nunique\")\n", "live_hex_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "eef4fa7f-db10-4426-a4f4-ee39ffc1e251", "metadata": {}, "outputs": [], "source": ["if transfer_date >= today_date:\n", "    live_hex_df = live_hex_df.merge(\n", "        base_pre_df[\n", "            [\"outlet_id\", \"item_id\", \"city\", \"facility_id\", \"be_facility_id\"]\n", "        ].drop_duplicates(),\n", "        on=[\"outlet_id\"],\n", "        how=\"left\",\n", "    )\n", "else:\n", "    live_hex_df = base_pre_df[(base_pre_df.date_ == tday)].drop_duplicates()\n", "    live_hex_df[\"live_overlap\"] = live_hex_df[\"overlap\"]\n", "    live_hex_df.drop(columns={\"date_\"}, inplace=True)\n", "\n", "live_hex_df = live_hex_df.drop_duplicates()\n", "current_ovlp = live_hex_df[live_hex_df.live_overlap > 1].copy()\n", "current_ovlp = current_ovlp[[\"hex_id\", \"outlet_id\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "a43dcebd-6860-4aa6-86b6-c2ab923ce16d", "metadata": {}, "outputs": [], "source": ["current_ovlp.head()"]}, {"cell_type": "markdown", "id": "879e3150-bece-48a0-910f-5c28ed0f9afe", "metadata": {}, "source": ["## overlapping and boundary hex share"]}, {"cell_type": "code", "execution_count": null, "id": "d2ce5e3d-5642-41f7-a9e8-98976c7772b8", "metadata": {"tags": []}, "outputs": [], "source": ["ovlp_ss = ovlp_hex.merge(\n", "    sales_df[[\"date_\", \"outlet_id\", \"item_id\", \"hex_id\", \"sales\"]],\n", "    on=[\"date_\", \"outlet_id\", \"item_id\", \"hex_id\"],\n", "    how=\"left\",\n", ")\n", "ovlp_ss[\"sales\"] = ovlp_ss[\"sales\"].fillna(0)\n", "ovlp_ss = ovlp_ss[[\"date_\", \"outlet_id\", \"item_id\", \"hex_id\", \"sales\"]]\n", "ovlp_ss = ovlp_ss.merge(current_ovlp, on=[\"hex_id\", \"outlet_id\"], how=\"inner\")\n", "ovlp_ss.head()"]}, {"cell_type": "code", "execution_count": null, "id": "dd7ef82b-b5e6-4dd7-9db9-9a7b6719a519", "metadata": {}, "outputs": [], "source": ["del [ovlp_hex]\n", "gc.collect"]}, {"cell_type": "code", "execution_count": null, "id": "e1fb021c-b923-4e36-9127-b7780550a836", "metadata": {}, "outputs": [], "source": ["bound_ovlp_sales_share_df = ovlp_ss.copy()\n", "bound_ovlp_sales_share_df[\"hex_item_sales\"] = bound_ovlp_sales_share_df.groupby(\n", "    [\"hex_id\", \"date_\", \"item_id\"]\n", ")[\"sales\"].transform(\"sum\")\n", "bound_ovlp_sales_share_df[\"sales\"] = bound_ovlp_sales_share_df[\"sales\"].fillna(0)\n", "bound_ovlp_sales_share_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b506db75-cdd8-44bb-8f8b-d80b6beb8443", "metadata": {"tags": []}, "outputs": [], "source": ["hex_store_share = (\n", "    bound_ovlp_sales_share_df[bound_ovlp_sales_share_df.date_ >= l7d]\n", "    .groupby([\"outlet_id\", \"item_id\", \"hex_id\"])\n", "    .agg({\"hex_item_sales\": \"mean\", \"sales\": \"mean\"})\n", "    .reset_index()\n", ")\n", "hex_store_share[\"store_share\"] = (\n", "    hex_store_share[\"sales\"] / hex_store_share[\"hex_item_sales\"]\n", ").<PERSON>na(0)\n", "hex_store_share.head()"]}, {"cell_type": "code", "execution_count": null, "id": "97031efc-b469-43e5-9e28-519925eae478", "metadata": {}, "outputs": [], "source": ["hex_store_share[[\"outlet_id\", \"item_id\", \"hex_id\"]].drop_duplicates().shape"]}, {"cell_type": "code", "execution_count": null, "id": "1c0f99ff-fb82-4512-af55-68719e94cf92", "metadata": {}, "outputs": [], "source": ["del [bound_ovlp_sales_share_df]\n", "gc.collect"]}, {"cell_type": "code", "execution_count": null, "id": "019ff4c1-cd37-4c79-89e0-97e21aee109a", "metadata": {}, "outputs": [], "source": ["boundary_hex_sales = (\n", "    bound_df.groupby([\"date_\", \"hex_id\", \"outlet_id\", \"item_id\"])\n", "    .agg(\n", "        {\"sales\": \"sum\", \"avail\": \"mean\", \"lin_extp\": \"sum\", \"exp_extp\": \"sum\", \"para_extp\": \"sum\"}\n", "    )\n", "    .reset_index()\n", ")\n", "boundary_hex_sales.head()"]}, {"cell_type": "markdown", "id": "a2fea275-f067-4192-99d6-bee38e8f128a", "metadata": {}, "source": ["## Handling boundary unmapped hexes"]}, {"cell_type": "code", "execution_count": null, "id": "4ffa4ad4-8666-4042-a2b2-9972ba504558", "metadata": {}, "outputs": [], "source": ["live_hex_df[\"flag\"] = 1\n", "unmapped_boundary_sales = boundary_hex_sales.merge(\n", "    live_hex_df[[\"hex_id\", \"flag\"]],\n", "    on=[\"hex_id\"],\n", "    how=\"left\",\n", ")\n", "unmapped_boundary_sales = unmapped_boundary_sales[unmapped_boundary_sales.flag != 1]\n", "unmapped_boundary_sales.drop(columns={\"flag\", \"avail\"}, inplace=True)\n", "live_hex_df.drop(columns={\"flag\"}, inplace=True)\n", "\n", "unmapped_boundary_sales.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d0c3c355-4132-43d2-bf63-0fdd60a711ca", "metadata": {}, "outputs": [], "source": ["last_outlet_sale = (\n", "    unmapped_boundary_sales[unmapped_boundary_sales.sales > 0][[\"hex_id\", \"date_\", \"outlet_id\"]]\n", "    .copy()\n", "    .drop_duplicates()\n", ")\n", "last_outlet_sale[\"rank\"] = last_outlet_sale.groupby([\"outlet_id\", \"hex_id\"])[\"date_\"].rank(\n", "    method=\"dense\", ascending=False\n", ")\n", "last_outlet_sale = last_outlet_sale[last_outlet_sale[\"rank\"] == 1]\n", "last_outlet_sale.drop(columns={\"date_\", \"rank\"}, inplace=True)\n", "last_outlet_sale = last_outlet_sale.merge(\n", "    unmapped_boundary_sales.drop(columns={\"outlet_id\"}), on=[\"hex_id\"]\n", ")\n", "\n", "last_outlet_sale.head()"]}, {"cell_type": "code", "execution_count": null, "id": "556d6eba-03ae-4b5e-a4e2-f4669c45468a", "metadata": {}, "outputs": [], "source": ["del [unmapped_boundary_sales]\n", "gc.collect"]}, {"cell_type": "code", "execution_count": null, "id": "04d66c8f-c991-4306-8bf5-a6ffcaed8f16", "metadata": {}, "outputs": [], "source": ["unmapped_boundary_sales = last_outlet_sale.merge(\n", "    base_pre_df[\n", "        [\"outlet_id\", \"facility_id\", \"date_\", \"be_facility_id\", \"city\", \"item_id\"]\n", "    ].drop_duplicates(),\n", "    on=[\"outlet_id\", \"date_\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "unmapped_boundary_sales.head()"]}, {"cell_type": "code", "execution_count": null, "id": "212f9a81-ef83-485b-9e45-c28e9720e646", "metadata": {}, "outputs": [], "source": ["non_ovlp_df.drop(columns={\"outlet_id\"}, inplace=True)\n", "boundary_hex_sales.drop(columns={\"outlet_id\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "9adbe478-4667-4a47-a030-922cf85a416a", "metadata": {}, "outputs": [], "source": ["final_hex_sales = pd.concat([boundary_hex_sales, non_ovlp_df, ovlp_df])\n", "final_hex_sales = (\n", "    final_hex_sales.groupby([\"date_\", \"hex_id\", \"item_id\"])\n", "    .agg({\"sales\": \"sum\", \"lin_extp\": \"sum\", \"exp_extp\": \"sum\", \"para_extp\": \"sum\"})\n", "    .reset_index()\n", ")\n", "print(final_hex_sales.shape)\n", "final_hex_sales.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9a21d6cf-f2d8-422b-a2b8-e6edf8cea303", "metadata": {"tags": []}, "outputs": [], "source": ["final_hex_sales[\n", "    (final_hex_sales[\"hex_id\"] == \"896189276a7ffff\") & (final_hex_sales[\"item_id\"] == 10000834)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "d4ba2e17-57d1-4d87-a289-168c4318554c", "metadata": {}, "outputs": [], "source": ["del [boundary_hex_sales, non_ovlp_df, ovlp_df]\n", "gc.collect"]}, {"cell_type": "code", "execution_count": null, "id": "ee4eb9ad-9be5-4866-8936-4a1d570c9fbb", "metadata": {"tags": []}, "outputs": [], "source": ["live_sales_hex_mapping = live_hex_df.merge(final_hex_sales, on=[\"hex_id\", \"item_id\"], how=\"left\")\n", "live_sales_hex_mapping = live_sales_hex_mapping.dropna()\n", "live_sales_hex_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d4dcb714-7e03-4f35-98aa-8db196bdb166", "metadata": {}, "outputs": [], "source": ["live_sales_hex_mapping[\n", "    (live_sales_hex_mapping[\"outlet_id\"] == 5072) & (live_sales_hex_mapping[\"item_id\"] == 10000834)\n", "].sort_values(\"sales\", ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "14db4241-32b7-4050-b4df-f3f9451e9799", "metadata": {}, "outputs": [], "source": ["live_sales_hex_mapping = live_sales_hex_mapping.merge(\n", "    hex_store_share[[\"outlet_id\", \"item_id\", \"hex_id\", \"store_share\"]],\n", "    on=[\"outlet_id\", \"item_id\", \"hex_id\"],\n", "    how=\"left\",\n", ")\n", "live_sales_hex_mapping[\"store_share\"] = live_sales_hex_mapping[\"store_share\"].fillna(1)"]}, {"cell_type": "code", "execution_count": null, "id": "cf60569a-0b64-4da1-9e93-4937d8499abf", "metadata": {}, "outputs": [], "source": ["del [hex_store_share, base_pre_df]\n", "gc.collect"]}, {"cell_type": "code", "execution_count": null, "id": "12672823-fd2f-458a-bbbb-2984cc3c0d5c", "metadata": {}, "outputs": [], "source": ["# live_hex_df['live_overlap'] = live_hex_df['overlap']"]}, {"cell_type": "code", "execution_count": null, "id": "c695fb44-9008-4132-bb14-f7b97ca7e4ee", "metadata": {}, "outputs": [], "source": ["live_sales_hex_mapping[\"sales\"] = np.where(\n", "    live_sales_hex_mapping[\"live_overlap\"] > 1,\n", "    live_sales_hex_mapping[\"sales\"] * live_sales_hex_mapping[\"store_share\"],\n", "    live_sales_hex_mapping[\"sales\"],\n", ")\n", "live_sales_hex_mapping[\"lin_extp\"] = np.where(\n", "    live_sales_hex_mapping[\"live_overlap\"] > 1,\n", "    live_sales_hex_mapping[\"lin_extp\"] * live_sales_hex_mapping[\"store_share\"],\n", "    live_sales_hex_mapping[\"lin_extp\"],\n", ")\n", "live_sales_hex_mapping[\"exp_extp\"] = np.where(\n", "    live_sales_hex_mapping[\"live_overlap\"] > 1,\n", "    live_sales_hex_mapping[\"exp_extp\"] * live_sales_hex_mapping[\"store_share\"],\n", "    live_sales_hex_mapping[\"exp_extp\"],\n", ")\n", "live_sales_hex_mapping[\"para_extp\"] = np.where(\n", "    live_sales_hex_mapping[\"live_overlap\"] > 1,\n", "    live_sales_hex_mapping[\"para_extp\"] * live_sales_hex_mapping[\"store_share\"],\n", "    live_sales_hex_mapping[\"para_extp\"],\n", ")\n", "\n", "live_sales_hex_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "11bd66a9-05d4-4a3a-9407-c1b04ba43507", "metadata": {}, "outputs": [], "source": ["live_sales_hex_mapping[live_sales_hex_mapping.facility_id == 1098].groupby([\"date_\"]).sum(\"sales\")"]}, {"cell_type": "code", "execution_count": null, "id": "7402f90d-1779-4462-b721-0197c006dbb8", "metadata": {}, "outputs": [], "source": ["store_item_sales = (\n", "    live_sales_hex_mapping.groupby(\n", "        [\"date_\", \"city\", \"facility_id\", \"be_facility_id\", \"outlet_id\", \"item_id\"]\n", "    )\n", "    .agg({\"sales\": \"sum\", \"lin_extp\": \"sum\", \"exp_extp\": \"sum\", \"para_extp\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "unmapped_boundary_sales = (\n", "    unmapped_boundary_sales.groupby(\n", "        [\"date_\", \"city\", \"facility_id\", \"be_facility_id\", \"outlet_id\", \"item_id\"]\n", "    )\n", "    .agg({\"sales\": \"sum\", \"lin_extp\": \"sum\", \"exp_extp\": \"sum\", \"para_extp\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "store_item_level_sales = pd.concat([store_item_sales, unmapped_boundary_sales]).drop_duplicates()\n", "\n", "store_item_level_sales = (\n", "    store_item_level_sales.groupby(\n", "        [\"date_\", \"city\", \"facility_id\", \"be_facility_id\", \"outlet_id\", \"item_id\"]\n", "    )\n", "    .agg({\"sales\": \"sum\", \"lin_extp\": \"sum\", \"exp_extp\": \"sum\", \"para_extp\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "276fd2e3-2c38-4e6b-ab2c-033c20635197", "metadata": {}, "outputs": [], "source": ["store_item_level_sales[\"tdate\"] = transfer_date\n", "store_item_level_sales[\"tdow\"] = pd.to_datetime(store_item_level_sales[\"tdate\"]).dt.dayofweek\n", "store_item_level_sales[\"dow\"] = pd.to_datetime(store_item_level_sales[\"date_\"]).dt.dayofweek\n", "store_item_level_sales.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f264473e-f29d-4c55-9585-6e39a992597a", "metadata": {}, "outputs": [], "source": ["store_item_level_sales[\"slot\"] = np.where(current_hour >= 10, \"6 to 17\", \"17 to 23\")"]}, {"cell_type": "code", "execution_count": null, "id": "898ba39d-92a3-4524-86d5-7a6553356f80", "metadata": {}, "outputs": [], "source": ["store_item_level_sales.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c3b78806-f4b1-4d23-b3ee-7e4abd0271e9", "metadata": {}, "outputs": [], "source": ["data = store_item_level_sales.copy()\n", "data[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))\n", "data[\"tdate\"] = pd.to_datetime(data[\"tdate\"])\n", "data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "19cca392-41a3-4199-8d9c-9476eddedd48", "metadata": {}, "outputs": [], "source": ["columns = [\n", "    \"date_\",\n", "    \"city\",\n", "    \"be_facility_id\",\n", "    \"facility_id\",\n", "    \"outlet_id\",\n", "    \"item_id\",\n", "    \"tdate\",\n", "    \"sales\",\n", "    \"lin_extp\",\n", "    \"exp_extp\",\n", "    \"para_extp\",\n", "    \"tdow\",\n", "    \"dow\",\n", "    \"slot\",\n", "    \"updated_at\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "9ae25b51-e92b-4417-bbd4-8f1d2cf7fc36", "metadata": {}, "outputs": [], "source": ["data = data[columns]"]}, {"cell_type": "code", "execution_count": null, "id": "b08f083e-792d-4eb3-8065-a4fc8cc88966", "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e4188f50-3b58-4844-91f0-0940966fbdda", "metadata": {}, "outputs": [], "source": ["data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "180452af-0c44-45d4-aab8-1f09fdd3e9ac", "metadata": {}, "outputs": [], "source": ["del [store_item_level_sales, live_sales_hex_mapping]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "d1aa964b-753f-4736-9caf-157724fe00b2", "metadata": {}, "source": ["## data push to table"]}, {"cell_type": "code", "execution_count": null, "id": "7d7c49e1-294f-4206-92ab-3459e51c912a", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"TIMESTAMP(6)\", \"description\": \"sales date\"},\n", "    {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"city\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"be_facility_id\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility_id\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"outlet_id\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "    {\"name\": \"tdate\", \"type\": \"TIMESTAMP(6)\", \"description\": \"transfer_date\"},\n", "    {\"name\": \"sales\", \"type\": \"double\", \"description\": \"actual sales\"},\n", "    {\"name\": \"lin_extp\", \"type\": \"double\", \"description\": \"linear extrapolated sales\"},\n", "    {\n", "        \"name\": \"exp_extp\",\n", "        \"type\": \"double\",\n", "        \"description\": \"exponential extrapolated sales\",\n", "    },\n", "    {\"name\": \"para_extp\", \"type\": \"double\", \"description\": \"parabolic extrapolated sales\"},\n", "    {\"name\": \"tdow\", \"type\": \"integer\", \"description\": \"transfer day of week\"},\n", "    {\"name\": \"dow\", \"type\": \"integer\", \"description\": \"sales day of week\"},\n", "    {\"name\": \"slot\", \"type\": \"varchar\", \"description\": \"data prep for slot A/B\"},\n", "    {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(6)\", \"description\": \"updated_at\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "e5640158-d689-43c3-9735-3bd7ef58c1ca", "metadata": {}, "outputs": [], "source": ["if current_hour >= 10:\n", "    kwargs = {\n", "        \"schema_name\": \"supply_etls\",\n", "        \"table_name\": \"milk_outlet_hex_sales\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"facility_id\", \"item_id\", \"tdate\", \"slot\"],\n", "        \"partition_key\": [\"updated_at\"],\n", "        \"incremental_key\": \"updated_at\",\n", "        \"load_type\": \"append\",  # append, truncate or upsert,\n", "        \"table_description\": \"this table contains outlet_item level sales and extrapolated sales for last 17 days based on their current hex_mapping\",\n", "    }\n", "else:\n", "    kwargs = {\n", "        \"schema_name\": \"supply_etls\",\n", "        \"table_name\": \"milk_outlet_hex_sales\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"facility_id\", \"item_id\", \"tdate\", \"slot\"],\n", "        \"partition_key\": [\"updated_at\"],\n", "        \"incremental_key\": \"updated_at\",\n", "        \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "        \"table_description\": \"this table contains outlet_item level sales and extrapolated sales for last 17 days based on their current hex_mapping\",\n", "    }\n", "\n", "pb.to_trino(data, **kwargs)"]}, {"cell_type": "markdown", "id": "f67afef2-8579-4764-81dc-afb5d940412d", "metadata": {}, "source": ["## data push to table logs"]}, {"cell_type": "code", "execution_count": null, "id": "ce2de819-6ff2-4a7d-aa16-4a8e7eb7d5a1", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"TIMESTAMP(6)\", \"description\": \"sales date\"},\n", "    {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"city\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"be_facility_id\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility_id\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"outlet_id\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "    {\"name\": \"tdate\", \"type\": \"TIMESTAMP(6)\", \"description\": \"transfer_date\"},\n", "    {\"name\": \"sales\", \"type\": \"double\", \"description\": \"actual sales\"},\n", "    {\"name\": \"lin_extp\", \"type\": \"double\", \"description\": \"linear extrapolated sales\"},\n", "    {\n", "        \"name\": \"exp_extp\",\n", "        \"type\": \"double\",\n", "        \"description\": \"exponential extrapolated sales\",\n", "    },\n", "    {\"name\": \"para_extp\", \"type\": \"double\", \"description\": \"parabolic extrapolated sales\"},\n", "    {\"name\": \"tdow\", \"type\": \"integer\", \"description\": \"transfer day of week\"},\n", "    {\"name\": \"dow\", \"type\": \"integer\", \"description\": \"sales day of week\"},\n", "    {\"name\": \"slot\", \"type\": \"varchar\", \"description\": \"data prep for slot A/B\"},\n", "    {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(6)\", \"description\": \"updated_at\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "efff25aa-e7e5-4288-b33d-3620097c1f75", "metadata": {"tags": []}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"milk_outlet_hex_sales_logs\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"facility_id\", \"item_id\", \"date_\", \"tdate\"],\n", "    \"partition_key\": [\"updated_at\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"append\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains outlet_item level sales and extrapolated sales logs for last 21 days based on their current hex_mapping\",\n", "}\n", "pb.to_trino(data, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "977655fb-9e66-4508-a46d-06134822f74e", "metadata": {}, "outputs": [], "source": ["send_alert(tday, slot)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}