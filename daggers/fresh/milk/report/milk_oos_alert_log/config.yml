alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: milk_oos_alert_log
dag_type: report
escalation_priority: low
execution_timeout: 120
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: fresh
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U0899MAT7PT
path: fresh/milk/report/milk_oos_alert_log
paused: false
pool: fresh_pool
project_name: milk
schedule:
  end_date: '2025-09-14T00:00:00'
  interval: 5 16,17,18 * * *
  start_date: '2025-06-17T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
