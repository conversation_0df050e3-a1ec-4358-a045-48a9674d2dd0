{"cells": [{"cell_type": "markdown", "id": "5b8690c9-91d9-4573-953c-bfbf4271de71", "metadata": {}, "source": ["## Importing Libraries and functions"]}, {"cell_type": "code", "execution_count": null, "id": "33465ef1-509b-4360-8570-dc55f43f0159", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import os\n", "import time\n", "import gc\n", "import requests\n", "\n", "import calendar\n", "from datetime import datetime, timedelta\n", "\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.options.mode.chained_assignment = None  # default='warn'\n", "pd.set_option(\"display.max_columns\", 500)"]}, {"cell_type": "code", "execution_count": null, "id": "42b59145-00ad-4a28-846c-31d8cd245b0e", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(\n", "            f\"Read attempt: {attempt}... \\nRun started at {datetime.now() + timed<PERSON>ta(hours = 5.5)}\"\n", "        )\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "07035fed-4405-42bf-804d-2e7659610b38", "metadata": {}, "outputs": [], "source": ["def try_upload_to_sheets(df, sheet_id, sheet_name, retries=3):\n", "    for attempt in range(retries):\n", "        try:\n", "            pb.to_sheets(df, sheet_id, sheet_name)\n", "            print(f\"Successfully uploaded {sheet_name} on attempt {attempt + 1}\")\n", "            return  # Exit the function once successful\n", "        except Exception as e:\n", "            print(f\"Attempt {attempt + 1} failed for {sheet_name}: {e}\")\n", "            if attempt == retries - 1:\n", "                print(f\"All attempts failed for {sheet_name}\")"]}, {"cell_type": "markdown", "id": "51016a6f-da82-4666-a0f7-4777bdd1edf9", "metadata": {}, "source": ["## Reading Backlog Data from Sheets"]}, {"cell_type": "code", "execution_count": null, "id": "e77815a8-0efe-44a1-8934-3cdc0f65a807", "metadata": {}, "outputs": [], "source": ["try:\n", "    all_key_oos_1 = pb.from_sheets(\n", "        sheetid=\"1qDSkBlf_DJMwM_pm68nUq8d8ZWvh2giOfnvJHlMj3_o\",\n", "        sheetname=\"All key OOS\",\n", "    )\n", "except:\n", "    all_key_oos_1 = pd.read_csv(\"milk_oos_log_backup - All key OOS.csv\")\n", "\n", "\n", "exc_col = [\"net_inv\"]\n", "fil_col = [col for col in all_key_oos_1.columns if col not in exc_col]\n", "all_key_oos_1 = all_key_oos_1[fil_col]\n", "all_key_oos_1[[\"outlet_id\", \"hour_\"]] = all_key_oos_1[[\"outlet_id\", \"hour_\"]].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "2197681f-15d6-4c24-8ebf-b9b4c40b7bc9", "metadata": {}, "outputs": [], "source": ["try:\n", "    fifty_perc_key_OOS_1 = pb.from_sheets(\n", "        sheetid=\"1qDSkBlf_DJMwM_pm68nUq8d8ZWvh2giOfnvJHlMj3_o\",\n", "        sheetname=\"50% key OOS\",\n", "    )\n", "except:\n", "    fifty_perc_key_OOS_1 = pd.read_csv(\"milk_oos_log_backup - 50% key OOS.csv\")\n", "\n", "fifty_perc_key_OOS_1[[\"outlet_id\", \"hour_\"]] = fifty_perc_key_OOS_1[[\"outlet_id\", \"hour_\"]].astype(\n", "    float\n", ")"]}, {"cell_type": "markdown", "id": "d512c77a-b82c-4505-8ead-ea38db28134d", "metadata": {}, "source": ["## Reading data from ETL"]}, {"cell_type": "code", "execution_count": null, "id": "1c82d831-3cb7-4b31-bd3d-d09ccef1236e", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT city, outlet_id, outlet_name, date_, hour_ \n", "FROM supply_etls.milk_oos_alert_data where date_ >= date'2025-06-17' and hour_ in (21,22) and report in ('all_key_oos')\n", "\"\"\"\n", "all_key_oos = read_sql_query(query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "5bd29717-0767-44e0-92b8-53cddb4b210d", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT city, outlet_id, outlet_name, date_, hour_ \n", "FROM supply_etls.milk_oos_alert_data where date_ >= date'2025-06-17' and hour_ in (21,22) and report in ('fifty_perc_key_OOS')\n", "\"\"\"\n", "fifty_perc_key_OOS = read_sql_query(query, trino)"]}, {"cell_type": "markdown", "id": "ee0958a2-b5f7-45ad-b2cf-71b1b02a0dee", "metadata": {}, "source": ["## Union the data"]}, {"cell_type": "code", "execution_count": null, "id": "38431686-7b26-42ea-a2b9-c240b05e20a7", "metadata": {}, "outputs": [], "source": ["all_key_oos_final = pd.concat([all_key_oos_1, all_key_oos], ignore_index=True)\n", "all_key_oos_final.sort_values(\"date_\", ascending=False, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "70580493-4b70-4d87-b3b4-1eb4b05c8cec", "metadata": {}, "outputs": [], "source": ["fifty_perc_key_OOS_final = pd.concat([fifty_perc_key_OOS_1, fifty_perc_key_OOS], ignore_index=True)\n", "fifty_perc_key_OOS_final.sort_values(\"date_\", ascending=False, inplace=True)"]}, {"cell_type": "markdown", "id": "fbf65d4b-9725-44f1-9582-2c841cc01efe", "metadata": {}, "source": ["## Push Data in Gsheet"]}, {"cell_type": "code", "execution_count": null, "id": "05d122b5-70d9-4ef8-958d-c08a81bfae70", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    all_key_oos_final,\n", "    \"1xhehSjsA-QtPTyvApzz8cvKC6Mn1nKnoy46n27VO3Cs\",\n", "    \"All key OOS\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f61e2fa0-c111-4b1a-8655-33bfc76b6f3c", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    fifty_perc_key_OOS_final,\n", "    \"1xhehSjsA-QtPTyvApzz8cvKC6Mn1nKnoy46n27VO3Cs\",\n", "    \"50% key OOS\",\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}