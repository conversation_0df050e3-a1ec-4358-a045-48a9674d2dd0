{"cells": [{"cell_type": "code", "execution_count": null, "id": "b1796c41-f499-4aeb-8616-5501cf430e6e", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import os\n", "import time\n", "import gc\n", "import requests\n", "\n", "import calendar\n", "from datetime import datetime as dt\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.options.mode.chained_assignment = None  # default='warn'\n", "pd.set_option(\"display.max_columns\", 500)"]}, {"cell_type": "code", "execution_count": null, "id": "70a43e7c-4a4a-4229-8b42-52c63d708eab", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(\n", "            f\"Read attempt: {attempt}... \\nRun started at {datetime.now() + timed<PERSON>ta(hours = 5.5)}\"\n", "        )\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "4eefc49d-168c-4a16-aee5-9e106af5f3cb", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pytz\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")\n", "\n", "current_time = datetime.now(IST)\n", "\n", "current_hour = pd.to_datetime(current_time).hour\n", "\n", "today_date = current_time.strftime(\"%Y-%m-%d\")\n", "\n", "l10_date = (pd.to_datetime(today_date) - <PERSON><PERSON><PERSON>(days=10)).strftime(\"%Y-%m-%d\")\n", "\n", "l45_date = (pd.to_datetime(today_date) - <PERSON><PERSON><PERSON>(days=45)).strftime(\"%Y-%m-%d\")\n", "\n", "today_date, l10_date, l45_date, current_hour"]}, {"cell_type": "code", "execution_count": null, "id": "51380165-422c-4c1a-befb-7a25ee0891a0", "metadata": {}, "outputs": [], "source": ["if current_hour > 12:\n", "    transfer_date = (pd.to_datetime(today_date) + timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "else:\n", "    transfer_date = today_date\n", "transfer_date"]}, {"cell_type": "code", "execution_count": null, "id": "66549ad4-a3d0-4b48-816c-772b11547530", "metadata": {}, "outputs": [], "source": ["##transfer_date = \"2024-12-11\""]}, {"cell_type": "markdown", "id": "4b1b283d-3bc6-4c12-b31f-bcafa795b7aa", "metadata": {"tags": []}, "source": ["## 1 liter items and backend\n"]}, {"cell_type": "code", "execution_count": null, "id": "f9008c10-5484-403a-bfc6-47a731292a32", "metadata": {}, "outputs": [], "source": ["try:\n", "    one_l = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"1 litre:: Backends\",\n", "    )\n", "\n", "except:\n", "    one_l = pd.read_csv(\"1_litre_Backends.csv\")\n", "\n", "one_l[\"be_facility_id\"] = one_l[\"be_facility_id\"].fillna(0).astype(int)\n", "one_l[\"500ml_item_id\"] = one_l[\"500ml_item_id\"].fillna(0).astype(int)\n", "one_l[\"1L_item_id\"] = one_l[\"1L_item_id\"].fillna(0).astype(int)\n", "one_l[\"start_date\"] = pd.to_datetime(one_l[\"start_date\"])\n", "one_l[\"end_date\"] = pd.to_datetime(one_l[\"end_date\"])\n", "one_l[\"conversion_factor\"] = one_l[\"conversion_factor\"].fillna(0).astype(float)\n", "one_l_item_id = tuple(set(one_l[\"1L_item_id\"].to_list()))\n", "half_l_item_id = tuple(set(one_l[\"500ml_item_id\"].to_list()))\n", "# one_l_be_item = tuple(set(one_l[\"be_facility_id\",\"item_id\"].to_list()))\n", "one_l_item_id"]}, {"cell_type": "code", "execution_count": null, "id": "dba5e729-ee4a-483c-80dd-db87e8f4ed5d", "metadata": {}, "outputs": [], "source": ["def ignore_wrong_manual_input(df, file_name=\"\"):\n", "    cols = list(df.columns.values)\n", "    error_df = pd.DataFrame()\n", "    df[\"filter_tag\"] = 0\n", "    for col in cols:\n", "        error_df = error_df.append(\n", "            df[(df[col].isna() == True) | (df[col].isin([\"#N/A\", \"\", \"#DIV/0!\"]))][cols]\n", "        )\n", "        df[\"filter_tag\"] = df[\"filter_tag\"] + np.where(\n", "            (df[col].isna() == True) | (df[col].isin([\"#N/A\", \"\", \"#DIV/0!\"])), 1, 0\n", "        )\n", "    error_df = error_df.drop_duplicates()\n", "    df = df.drop_duplicates()\n", "    df = df[cols][df[\"filter_tag\"] == 0]\n", "    if error_df.shape[0] > 0:\n", "        # update.\n", "        error_df.to_csv(f\"\"\"{file_name}_wrong_entries_ignored.csv\"\"\")\n", "        pb.send_slack_message(\n", "            channel=\"bl-milk-indent-check\",\n", "            text=f\"\"\"The below inputs for transfer bumps are ignored for {file_name}: \\ncc <@U06NUDF672P> <@U06T927GCSW> <@U03ABAH15H9> <@U07E0MTBS2X>\"\"\",\n", "            files=[f\"\"\"{file_name}_wrong_entries_ignored.csv\"\"\"],\n", "        )\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "a9a7f597-cb28-4259-8570-6aca3aee992f", "metadata": {"tags": []}, "outputs": [], "source": ["try:\n", "    mother_dairy_forecast_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"mother_dairy_forecast\",\n", "    )\n", "\n", "except:\n", "    mother_dairy_forecast_df = pd.read_csv(\"mother_dairy_forecast.csv\")\n", "\n", "\n", "mother_dairy_forecast_df = mother_dairy_forecast_df[\n", "    [\"facility_id\", \"be_facility_id\", \"item_id\", \"tot_cpd_v2\"]\n", "].astype(int)\n", "md_item_id_list = tuple(set(mother_dairy_forecast_df[\"item_id\"].to_list()))\n", "md_facility_id_list = tuple(set(mother_dairy_forecast_df[\"facility_id\"].to_list()))\n", "md_be_facility_id_list = tuple(set(mother_dairy_forecast_df[\"be_facility_id\"].to_list()))\n", "if len(md_item_id_list) == 0:\n", "    md_item_id_list = (1000000, 2000000)\n", "    md_facility_id_list = (8888, 9999)\n", "    md_be_facility_id_list = (9999, 8888)\n", "# mother_dairy_forecast_df.head()\n", "md_item_id_list, md_facility_id_list, md_be_facility_id_list"]}, {"cell_type": "code", "execution_count": null, "id": "eef269cb-3bbc-4e6f-bc94-110ba2b09221", "metadata": {}, "outputs": [], "source": ["def percentile(x):\n", "    return np.percentile(x, 75)"]}, {"cell_type": "markdown", "id": "42c13b33-bae0-4c58-8f4f-0097368a363c", "metadata": {}, "source": ["# Milk Base"]}, {"cell_type": "code", "execution_count": null, "id": "5dc7b22f-7292-434a-9bd9-e8808814a95e", "metadata": {"tags": []}, "outputs": [], "source": ["base_query = \"\"\"\n", "    WITH active_stores AS (\n", "        SELECT DISTINCT facility_id, outlet_id \n", "        FROM po.physical_facility_outlet_mapping pfom  \n", "        WHERE ars_active = 1 AND active = 1 AND is_primary = 1 \n", "        AND outlet_id IN (SELECT DISTINCT id FROM retail.console_outlet WHERE business_type_id = 7 AND active = 1 AND lake_active_record) \n", "        AND outlet_id IN (SELECT DISTINCT outlet_id FROM po.bulk_facility_outlet_mapping WHERE active = True AND lake_active_record) \n", "    ), \n", "    \n", "    milk_assortment AS (\n", "        SELECT DISTINCT cl.name AS city, a.facility_id, outlet_id, item_id\n", "        FROM rpc.product_facility_master_assortment a\n", "        JOIN active_stores b ON a.facility_id = b.facility_id\n", "        JOIN retail.console_outlet co ON co.id = b.outlet_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "        LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id AND cl.lake_active_record\n", "        WHERE item_id IN (SELECT DISTINCT item_id FROM rpc.item_category_details WHERE l2_id = 1185) \n", "        AND a.active = 1 AND a.master_assortment_substate_id = 1\n", "    ),\n", "    \n", "    be_mapping AS (\n", "        SELECT item_id, facility_id, be_facility_id, be_outlet_id, \n", "        CASE \n", "            WHEN cloud_store_id IS NULL THEN be_outlet_id \n", "            ELSE cloud_store_id \n", "        END AS be_inv_outlet_id\n", "        FROM (\n", "            SELECT DISTINCT tm.item_id, cf.facility_id, cb.facility_id AS be_facility_id, CAST(tm.tag_value AS int) AS be_outlet_id\n", "            FROM rpc.item_outlet_tag_mapping tm\n", "            LEFT JOIN retail.console_outlet cb ON cb.id = CAST(tm.tag_value AS int) AND cb.active = 1 AND cb.lake_active_record\n", "            JOIN retail.console_outlet cf ON cf.id = outlet_id AND cf.active = 1 AND cf.lake_active_record\n", "            WHERE tag_type_id IN (8) AND tm.active = 1\n", "        ) a\n", "        LEFT JOIN (\n", "            SELECT DISTINCT warehouse_id, cloud_store_id \n", "            FROM retail.warehouse_outlet_mapping\n", "            WHERE lake_active_record\n", "        ) wom on wom.warehouse_id = a.be_outlet_id\n", "    ),\n", "    \n", "    final AS (\n", "        SELECT city, a.facility_id, a.outlet_id, a.item_id, be_facility_id, be_outlet_id, be_inv_outlet_id\n", "        FROM milk_assortment a\n", "        LEFT JOIN be_mapping b ON a.item_id = b.item_id AND a.facility_id = b.facility_id\n", "    )\n", "    \n", "    SELECT * FROM final\n", "\"\"\"\n", "base_pre_df = read_sql_query(base_query, trino)\n", "\n", "base_pre_df[\"be_facility_id\"] = base_pre_df[\"be_facility_id\"].fillna(0).astype(int)\n", "base_pre_df[\"be_inv_outlet_id\"] = base_pre_df[\"be_inv_outlet_id\"].fillna(0).astype(int)\n", "base_pre_df[\"be_outlet_id\"] = base_pre_df[\"be_outlet_id\"].fillna(0).astype(int)\n", "\n", "base_pre_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "5eb93fb0-320a-410f-81a0-352f7dbd4e7c", "metadata": {}, "outputs": [], "source": ["x = pd.date_range(start=l45_date, end=pd.to_datetime(transfer_date) - timedelta(days=2))\n", "date_df = pd.DataFrame({\"date_\": x, \"date_flag\": 1})\n", "date_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "0285a565-f5ff-41cc-90d2-2403280791ad", "metadata": {}, "outputs": [], "source": ["item_id_list = tuple(set(base_pre_df[\"item_id\"].to_list()))\n", "facility_id_list = tuple(set(base_pre_df[\"facility_id\"].to_list()))\n", "outlet_id_list = tuple(set(base_pre_df[\"outlet_id\"].to_list()))\n", "\n", "len(item_id_list), len(facility_id_list), len(outlet_id_list)"]}, {"cell_type": "markdown", "id": "60b4e735-558d-4681-b97e-d9cf97383556", "metadata": {}, "source": ["## ARS Slot Data"]}, {"cell_type": "code", "execution_count": null, "id": "d89ea3d7-abba-402f-b8fd-240beeb37440", "metadata": {}, "outputs": [], "source": ["def slot(id):\n", "    url = \"https://retail-internal.grofer.io/ars/v1/sto-slot-config/\" + str(id) + \"/?mode=milk\"\n", "\n", "    response = requests.get(url)\n", "    response.json()[\"data\"]\n", "\n", "    slot_timings = response.json()[\"data\"][\"slot_config\"]\n", "\n", "    slot_timings_df = pd.DataFrame(\n", "        {\"time\": list(slot_timings.keys()), \"outlet_id\": list(slot_timings.values())}\n", "    )\n", "\n", "    slot_timings_df = slot_timings_df.explode(\"outlet_id\")\n", "    slot_timings_df[\"be_facility_id\"] = id\n", "\n", "    return slot_timings_df"]}, {"cell_type": "code", "execution_count": null, "id": "ab46b487-c91e-4ee8-b801-8e6e6acd9536", "metadata": {}, "outputs": [], "source": ["be_facility_list = list(base_pre_df[\"be_facility_id\"].unique())\n", "\n", "slot_df = pd.DataFrame()\n", "for i in be_facility_list:\n", "    temp = slot(i)\n", "    slot_df = pd.concat([slot_df, temp])\n", "\n", "slot_df[\"is_slota\"] = np.where(slot_df[\"time\"] >= \"15:00\", 1, 0)\n", "slot_df[\"is_slotb\"] = np.where(slot_df[\"time\"] < \"15:00\", 1, 0)\n", "\n", "slot_df = (\n", "    slot_df.groupby([\"be_facility_id\", \"outlet_id\"])\n", "    .agg({\"is_slota\": \"sum\", \"is_slotb\": \"sum\", \"time\": \"count\"})\n", "    .reset_index()\n", ")\n", "\n", "slot_df.head(1)"]}, {"cell_type": "markdown", "id": "0e9bfc7a-056c-4038-ae5c-28d2b838a053", "metadata": {}, "source": ["### New stores / buffer / polygon changed stores list"]}, {"cell_type": "code", "execution_count": null, "id": "b36d14ce-4156-4e65-b271-df309e8833ee", "metadata": {}, "outputs": [], "source": ["new_store = f\"\"\"\n", "with new_stores as (\n", "select\n", "    s.facility_name,\n", "    s.facility_id,\n", "    s.store_type,\n", "    s.final_ob_date,\n", "    s.sister_store_name,\n", "    s.sister_store_facility_id,\n", "    coalesce(a.ars_active,0) ars_active,\n", "    case when co.device_id = 143 then 1 else 0 end outlet_id_active\n", "from\n", "    supply_etls.e3_new_darkstores s\n", "left join\n", "    po.physical_facility_outlet_mapping a\n", "    on s.facility_id = a.facility_id\n", "    and a.active = 1\n", "left join\n", "    retail.console_outlet co\n", "    on s.outlet_id = co.id\n", "    and co.active = 1\n", "where\n", "    -- lower(s.store_type) not in ('new store')\n", "    s.final_ob_date > date'2024-03-15'\n", "    and s.final_ob_date < current_date + interval '15' day\n", "group by 1,2,3,4,5,6,7,8\n", "having \n", "    coalesce(a.ars_active,0) = 1 and (case when co.device_id = 143 then 1 else 0 end) = 1\n", "),\n", "\n", "store_age_base AS (\n", "        WITH first_outbound AS (\n", "            SELECT facility_id, DATE(od.cart_checkout_ts_ist) AS f_date\n", "            FROM dwh.fact_sales_order_details od \n", "            inner join  po.physical_facility_outlet_mapping mp on mp.outlet_id = od.outlet_id and lake_active_record and ars_active = 1\n", "            WHERE od.order_create_dt_ist > DATE('{transfer_date}') - interval '30' day\n", "            AND od.is_internal_order = false \n", "            AND mp.facility_id IN (select distinct facility_id from new_stores)\n", "            AND (od.order_type NOT LIKE '%%internal%%' OR od.order_type IS NULL) AND od.order_current_status = 'DELIVERED'\n", "            GROUP BY 1,2\n", "            having  count(distinct order_id)>10\n", "        )\n", "        \n", "        SELECT \n", "            facility_id, \n", "            min(f_date) as start_date \n", "        FROM first_outbound fb \n", "        group by 1\n", "    )\n", "\n", "select \n", "    ns.facility_id,\n", "    abs(date_diff('day',date'{transfer_date}',coalesce(max(start_date),max(final_ob_date)))) as age\n", "from\n", "    new_stores ns\n", "    left join store_age_base sb on sb.facility_id = ns.facility_id\n", "    where final_ob_date between date'{transfer_date}' - interval '45' day and date'{transfer_date}'\n", "    group by 1\n", "\n", "\"\"\"\n", "new_store_mapping = read_sql_query(new_store, trino)\n", "new_store_facility_list = tuple(\n", "    list(new_store_mapping[new_store_mapping[\"age\"] <= 5][\"facility_id\"].unique()) + ([-1, -2])\n", ")\n", "\n", "l21_facility_list = tuple(\n", "    list(new_store_mapping[new_store_mapping[\"age\"] <= 21][\"facility_id\"].unique()) + ([-1, -2])\n", ")\n", "medium_age_facility_list = tuple(\n", "    list(new_store_mapping[new_store_mapping[\"age\"] <= 45][\"facility_id\"].unique()) + ([-1, -2])\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "43c3d4db-d1a8-45cd-bc56-2d4302d36a9b", "metadata": {}, "outputs": [], "source": ["new_store_mapping[new_store_mapping.facility_id == 2551]"]}, {"cell_type": "code", "execution_count": null, "id": "17dbf198-9257-44c3-bb90-2b05ef62484e", "metadata": {}, "outputs": [], "source": ["buffer_store = f\"\"\"\n", "with\n", "frontend_merchant_mapping as\n", "        (select * from dwh.dim_merchant_outlet_facility_mapping\n", "            where \n", "                is_frontend_merchant_active = true\n", "                and\n", "                    is_backend_merchant_active = true\n", "                and \n", "                    is_pos_outlet_active = 1\n", "                and \n", "                    is_mapping_enabled = true\n", "                and \n", "                    is_express_store = true\n", "                and \n", "                    is_current_mapping_active = true\n", "                and \n", "                    is_current = true\n", "                and \n", "                    pos_outlet_name <> 'SS Gurgaon Test Store'\n", "),\n", "\n", "store_polygon_updates as (\n", "select \n", "    cr.updated_at + interval '5' hour + interval '30' minute as updated_time, \n", "    f.external_id as merchant_id, \n", "    json_query(cr.meta, 'strict $.business_impact.old_order_count') as old_order_count,\n", "    json_query(cr.meta, 'strict $.business_impact.new_order_count') as new_orders_count,\n", "    json_query(cr.diff, 'strict $.polygon.info') as change_in_area\n", "from \n", "    sauron.change_requests cr \n", "join \n", "    sauron.feature f \n", "    on f.id=cr.feature_id\n", "where \n", "    f.layer_id = 6 \n", "    AND cr.updated_at + interval '5' hour + interval '30' minute > CURRENT_DATE - interval '30' day \n", "    and cr.updated_at + interval '5' hour + interval '30' minute < CURRENT_DATE \n", "    AND cr.state = 'MERGED'\n", "    -- and f.external_id in (33207)\n", ")\n", "\n", "select \n", "    fm.facility_id,\n", "    date(max(updated_time)) as change_date\n", "    \n", "from \n", "    store_polygon_updates s\n", "left join frontend_merchant_mapping  fm on fm.frontend_merchant_id = s.merchant_id\n", "where s.updated_time between  date'{transfer_date}' - interval '8' day and date'{transfer_date}' - interval '2' day\n", "and fm.facility_id not in {new_store_facility_list}\n", "\n", "\n", "\n", "group by 1\n", "    \"\"\"\n", "\n", "buffer_store_mapping = read_sql_query(buffer_store, trino)\n", "buffer_store_mapping[\"facility_id\"] = buffer_store_mapping[\"facility_id\"].astype(int)\n", "buffer_store_mapping[\"change_date\"] = pd.to_datetime(buffer_store_mapping[\"change_date\"])\n", "buffer_store_facility_list = tuple(list(buffer_store_mapping[\"facility_id\"].unique()) + ([-1, -2]))"]}, {"cell_type": "code", "execution_count": null, "id": "13806474-13e6-46b9-a0d0-4a959cc38946", "metadata": {"tags": []}, "outputs": [], "source": ["wl_stores_list = tuple(list(buffer_store_mapping[\"facility_id\"].unique()) + [l21_facility_list])\n", "len(wl_stores_list)"]}, {"cell_type": "code", "execution_count": null, "id": "75733b5f-5fef-4d22-93a5-3396cfa8f226", "metadata": {}, "outputs": [], "source": ["base_pre_df = base_pre_df.merge(slot_df, on=[\"be_facility_id\", \"outlet_id\"], how=\"left\")\n", "\n", "base_pre_df[\"time\"] = np.where(base_pre_df[\"time\"].isna(), 0, base_pre_df[\"time\"]).astype(int)\n", "\n", "base_pre_df[\"is_slota\"] = np.where(\n", "    base_pre_df[\"is_slota\"].isna(), 0, base_pre_df[\"is_slota\"]\n", ").astype(int)\n", "\n", "base_pre_df[\"is_slotb\"] = np.where(\n", "    base_pre_df[\"is_slotb\"].isna(), 0, base_pre_df[\"is_slotb\"]\n", ").astype(int)\n", "\n", "base_pre_df.shape"]}, {"cell_type": "markdown", "id": "08ece8f5-8705-4b32-8704-ffdc339c7597", "metadata": {}, "source": ["## Fetching all data from ETL\n"]}, {"cell_type": "code", "execution_count": null, "id": "7cac6ea5-910c-4122-88da-27ee76fc68a0", "metadata": {}, "outputs": [], "source": ["base_query = f\"\"\"\n", "       SELECT\n", "           a.date_,\n", "            cl.name as city,\n", "            b.outlet_id as outlet_id,\n", "            a.facility_id,\n", "            a.item_id,\n", "            a.hour_,\n", "            max(is_available) as is_available,\n", "            max(carts) carts,\n", "            max(total_search) as searches,\n", "            max(sales) as quantity\n", "        from\n", "            supply_etls.milk_sales_avail_searches_dump as a\n", "        left join po.physical_facility_outlet_mapping b on b.facility_id = a.facility_id and ars_active = 1 and lake_active_record\n", "        LEFT JOIN retail.console_location cl ON cl.id = b.city_id\n", "        inner join (select date_,facility_id,item_id,max(updated_at) as updated_at from supply_etls.milk_sales_avail_searches_dump group by 1,2,3) as mx\n", "            on mx.date_ = a.date_ and a.updated_at=mx.updated_at\n", "            and a.facility_id=mx.facility_id and a.item_id=mx.item_id\n", "        where a.date_ BETWEEN DATE('{transfer_date}') - interval '45' day AND DATE('{transfer_date}') - interval '1' day\n", "            and a.facility_id in {facility_id_list}\n", "            and a.item_id in {item_id_list}\n", "        group by 1,2,3,4,5,6\n", "    \"\"\"\n", "data_df = read_sql_query(base_query, trino)\n", "data_df[\"date_\"] = pd.to_datetime(data_df[\"date_\"])\n", "data_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "9f77f47c-c934-45b7-b59f-606ac7b4aa33", "metadata": {}, "outputs": [], "source": ["(\n", "    data_df[\n", "        (data_df.facility_id == 1240)\n", "        & (data_df.item_id == 10005109)\n", "        & (data_df.date_ == \"2025-01-31\")\n", "    ].sort_values(\"hour_\", ascending=False)\n", ")"]}, {"cell_type": "markdown", "id": "1d8a8f28-ab10-49de-8967-c36f2fccc229", "metadata": {}, "source": ["## Hourly Availability Data"]}, {"cell_type": "code", "execution_count": null, "id": "0bbb97c2-d06b-4a18-bc84-0adcd8880905", "metadata": {}, "outputs": [], "source": ["hr_wts = read_sql_query(\n", "    \"\"\"SELECT DISTINCT city, order_hour AS hour_, CAST(weights AS DOUBLE) AS chw\n", "                        FROM supply_etls.city_hour_weights a\n", "                        WHERE a.updated_at IN (SELECT MAX(updated_at) FROM supply_etls.city_hour_weights where updated_at >= current_date - interval '32' day)\n", "                         and a.updated_at >= current_date - interval '32' day\"\"\",\n", "    trino,\n", ")\n", "hr_wts.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "d316b6b8-a941-411e-a305-171719acf539", "metadata": {}, "outputs": [], "source": ["pre_avail_df = (\n", "    data_df[[\"city\", \"date_\", \"facility_id\", \"item_id\", \"hour_\", \"is_available\"]]\n", "    .copy()\n", "    .drop_duplicates()\n", ")\n", "pre_avail_df = pre_avail_df.merge(hr_wts, on=[\"city\", \"hour_\"], how=\"left\")\n", "pre_avail_df[\"chw\"] = pre_avail_df[\"chw\"].fillna(0)\n", "pre_avail_df[\"wts\"] = pre_avail_df[\"chw\"] * pre_avail_df[\"is_available\"]\n", "pre_avail_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "cdcceda9-cdfd-4a60-8425-fa1c64e8fb5b", "metadata": {}, "outputs": [], "source": ["day_wt = (\n", "    pre_avail_df[pre_avail_df[\"hour_\"].between(6, 23)]\n", "    .groupby([\"date_\", \"facility_id\", \"item_id\"])\n", "    .agg({\"wts\": \"sum\", \"chw\": \"sum\"})\n", "    .reset_index()\n", ")\n", "day_wt[\"day_wt_avail\"] = (day_wt[\"wts\"] / day_wt[\"chw\"]).fillna(0)\n", "\n", "\n", "mor_wt = (\n", "    pre_avail_df[pre_avail_df[\"hour_\"].between(6, 17)]\n", "    .groupby([\"date_\", \"facility_id\", \"item_id\"])\n", "    .agg({\"wts\": \"sum\", \"chw\": \"sum\"})\n", "    .reset_index()\n", ")\n", "mor_wt[\"mor_wt_avail\"] = (mor_wt[\"wts\"] / mor_wt[\"chw\"]).fillna(0)\n", "\n", "eve_wt = (\n", "    pre_avail_df[pre_avail_df[\"hour_\"].between(current_hour, 23)]\n", "    .groupby([\"date_\", \"facility_id\", \"item_id\"])\n", "    .agg({\"wts\": \"sum\", \"chw\": \"sum\"})\n", "    .reset_index()\n", ")\n", "eve_wt[\"eve_wt_avail\"] = (eve_wt[\"wts\"] / eve_wt[\"chw\"]).fillna(0)\n", "\n", "noon_wt = (\n", "    pre_avail_df[pre_avail_df[\"hour_\"].between(11, 23)]\n", "    .groupby([\"date_\", \"facility_id\", \"item_id\"])\n", "    .agg({\"wts\": \"sum\", \"chw\": \"sum\"})\n", "    .reset_index()\n", ")\n", "noon_wt[\"noon_wt_avail\"] = (noon_wt[\"wts\"] / noon_wt[\"chw\"]).fillna(0)\n", "\n", "\n", "day_wt.drop(columns={\"wts\", \"chw\"}, inplace=True)\n", "noon_wt.drop(columns={\"wts\", \"chw\"}, inplace=True)\n", "mor_wt.drop(columns={\"wts\", \"chw\"}, inplace=True)\n", "eve_wt.drop(columns={\"wts\", \"chw\"}, inplace=True)\n", "\n", "availability_df = day_wt.merge(mor_wt, on=[\"date_\", \"facility_id\", \"item_id\"], how=\"left\")\n", "availability_df = availability_df.merge(noon_wt, on=[\"date_\", \"facility_id\", \"item_id\"], how=\"left\")\n", "availability_df = availability_df.merge(eve_wt, on=[\"date_\", \"facility_id\", \"item_id\"], how=\"left\")\n", "\n", "availability_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "e9dad158-a114-43eb-82b2-e946056302ab", "metadata": {}, "outputs": [], "source": ["del [pre_avail_df, day_wt, mor_wt, noon_wt, eve_wt, hr_wts]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "0071a976-60c4-450d-a95e-bd31144b328b", "metadata": {}, "source": ["## Sales Data"]}, {"cell_type": "code", "execution_count": null, "id": "36b76e7d-916a-45d2-8d41-c6ec0965c977", "metadata": {}, "outputs": [], "source": ["day_sales = (\n", "    data_df[data_df[\"hour_\"].between(6, 23)]\n", "    .groupby([\"date_\", \"facility_id\", \"item_id\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"quantity\": \"day_sales_qty\"})\n", ")\n", "\n", "mor_sales = (\n", "    data_df[data_df[\"hour_\"].between(6, 17)]\n", "    .groupby([\"date_\", \"facility_id\", \"item_id\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"quantity\": \"mor_sales_qty\"})\n", ")\n", "\n", "early_mor_sales = (\n", "    data_df[data_df[\"hour_\"].between(6, 8)]\n", "    .groupby([\"date_\", \"facility_id\", \"item_id\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"quantity\": \"early_mor_sales_qty\"})\n", ")\n", "\n", "noon_sales = (\n", "    data_df[data_df[\"hour_\"].between(11, 17)]\n", "    .groupby([\"date_\", \"facility_id\", \"item_id\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"quantity\": \"noon_sales_qty\"})\n", ")\n", "\n", "\n", "eve_sales = (\n", "    data_df[data_df[\"hour_\"].between(current_hour, 23)]\n", "    .groupby([\"date_\", \"facility_id\", \"item_id\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"quantity\": \"eve_sales_qty\"})\n", ")\n", "\n", "night_sales = (\n", "    data_df[data_df[\"hour_\"].between(0, 5)]\n", "    .groupby([\"date_\", \"facility_id\", \"item_id\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"quantity\": \"night_sales_qty\"})\n", ")\n", "\n", "\n", "sales_df = day_sales.merge(mor_sales, on=[\"date_\", \"facility_id\", \"item_id\"], how=\"left\")\n", "sales_df = sales_df.merge(early_mor_sales, on=[\"date_\", \"facility_id\", \"item_id\"], how=\"left\")\n", "sales_df = sales_df.merge(noon_sales, on=[\"date_\", \"facility_id\", \"item_id\"], how=\"left\")\n", "sales_df = sales_df.merge(eve_sales, on=[\"date_\", \"facility_id\", \"item_id\"], how=\"left\")\n", "sales_df = sales_df.merge(night_sales, on=[\"date_\", \"facility_id\", \"item_id\"], how=\"left\")\n", "\n", "sales_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "567da5a4-9cbd-4fe0-bf4c-c2ea9477f326", "metadata": {}, "outputs": [], "source": ["del [day_sales, mor_sales, eve_sales, noon_sales, early_mor_sales, night_sales]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "a45dd191-7b5a-4644-b37e-973f7a5ad45f", "metadata": {}, "source": ["## Store Disruption Data"]}, {"cell_type": "code", "execution_count": null, "id": "eb312d93-6bb0-4b52-a48b-5e28a38993a1", "metadata": {}, "outputs": [], "source": ["disruption_query = f\"\"\"\n", "    WITH base AS(\n", "        SELECT * FROM supply_etls.milk_outlet_disruptions_logs\n", "    )\n", "    \n", "    SELECT DISTINCT outlet_id , date_ FROM base \n", "    WHERE date_ BETWEEN DATE('{today_date}') - interval '45' day AND DATE('{today_date}') - interval '0' day\n", "    \n", "    \"\"\"\n", "disruption_df = pd.read_sql(disruption_query, trino)\n", "disruption_df[\"date_\"] = pd.to_datetime(disruption_df[\"date_\"])\n", "\n", "print(disruption_df.shape)\n", "disruption_df = disruption_df.drop_duplicates()\n", "print(disruption_df.shape)\n", "\n", "disruption_df[\"d_flag\"] = 1"]}, {"cell_type": "code", "execution_count": null, "id": "b6067a54-7471-469b-bcee-4a2e3cde3269", "metadata": {}, "outputs": [], "source": ["try:\n", "    city_disruption_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::city-disruption\",\n", "    )\n", "\n", "except:\n", "    city_disruption_df = pd.read_csv(\"city_disruption.csv\")\n", "\n", "city_disruption_df = ignore_wrong_manual_input(city_disruption_df)\n", "city_disruption_df = city_disruption_df.drop_duplicates().reset_index().drop(columns={\"index\"})\n", "\n", "city_disruption_df[\"date_\"] = pd.to_datetime(city_disruption_df[\"date_\"])\n", "\n", "city_disruption_df[\"city_flag\"] = 1\n", "\n", "city_disruption_df.head(10)"]}, {"cell_type": "markdown", "id": "90944858-3539-4aa0-86e0-3fe990461c42", "metadata": {}, "source": ["## Base Merge"]}, {"cell_type": "code", "execution_count": null, "id": "b97a4c22-7ff3-4307-9ea6-67c892d124e9", "metadata": {}, "outputs": [], "source": ["base_df = base_pre_df.copy()\n", "base_df[\"date_flag\"] = 1\n", "base_df = pd.merge(base_df, date_df, on=[\"date_flag\"], how=\"inner\").drop(columns={\"date_flag\"})\n", "\n", "## Sales and Availability data merge\n", "\n", "base_df = pd.merge(base_df, sales_df, on=[\"date_\", \"facility_id\", \"item_id\"], how=\"left\")\n", "base_df = pd.merge(base_df, availability_df, on=[\"date_\", \"facility_id\", \"item_id\"], how=\"left\")\n", "base_df[\n", "    [\n", "        \"early_mor_sales_qty\",\n", "        \"mor_sales_qty\",\n", "        \"noon_sales_qty\",\n", "        \"eve_sales_qty\",\n", "        \"night_sales_qty\",\n", "        \"day_sales_qty\",\n", "        \"day_wt_avail\",\n", "        \"mor_wt_avail\",\n", "        \"eve_wt_avail\",\n", "        \"noon_wt_avail\",\n", "    ]\n", "] = base_df[\n", "    [\n", "        \"early_mor_sales_qty\",\n", "        \"mor_sales_qty\",\n", "        \"noon_sales_qty\",\n", "        \"eve_sales_qty\",\n", "        \"night_sales_qty\",\n", "        \"day_sales_qty\",\n", "        \"day_wt_avail\",\n", "        \"mor_wt_avail\",\n", "        \"eve_wt_avail\",\n", "        \"noon_wt_avail\",\n", "    ]\n", "].fillna(\n", "    0\n", ")\n", "base_df[\n", "    [\n", "        \"early_mor_sales_qty\",\n", "        \"mor_sales_qty\",\n", "        \"noon_sales_qty\",\n", "        \"eve_sales_qty\",\n", "        \"night_sales_qty\",\n", "        \"day_sales_qty\",\n", "    ]\n", "] = base_df[\n", "    [\n", "        \"early_mor_sales_qty\",\n", "        \"mor_sales_qty\",\n", "        \"noon_sales_qty\",\n", "        \"eve_sales_qty\",\n", "        \"night_sales_qty\",\n", "        \"day_sales_qty\",\n", "    ]\n", "].astype(\n", "    int\n", ")\n", "\n", "## Final sales and availability calculation\n", "base_df = base_df[~base_df[\"date_\"].isin({transfer_date})]\n", "base_df = base_df.fillna(0)\n", "base_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "dc1e20e5-5467-45ed-915b-714c8beff3e4", "metadata": {}, "outputs": [], "source": ["fac_df = base_df[[\"facility_id\", \"date_\"]].drop_duplicates().reset_index().drop(columns={\"index\"})\n", "fac_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "30f16b47-09a9-4942-8273-99d33dfd5593", "metadata": {}, "outputs": [], "source": ["all_disruption = disruption_df[disruption_df[\"outlet_id\"] == -666]\n", "all_disruption = pd.merge(all_disruption, fac_df, on=[\"date_\"], how=\"inner\")\n", "all_disruption = all_disruption.drop(columns={\"outlet_id\"}).rename(columns={\"d_flag\": \"all_flag\"})\n", "all_disruption.shape"]}, {"cell_type": "code", "execution_count": null, "id": "f5e69212-5fd9-406a-bc61-87fb85ce82a9", "metadata": {}, "outputs": [], "source": ["disruption_base_df = pd.merge(base_df, disruption_df, on=[\"outlet_id\", \"date_\"], how=\"left\")\n", "\n", "disruption_base_df = pd.merge(\n", "    disruption_base_df, all_disruption, on=[\"facility_id\", \"date_\"], how=\"left\"\n", ")\n", "\n", "disruption_base_df = pd.merge(\n", "    disruption_base_df, city_disruption_df, on=[\"city\", \"date_\"], how=\"left\"\n", ")\n", "\n", "disruption_base_df[[\"d_flag\", \"all_flag\", \"city_flag\"]] = disruption_base_df[\n", "    [\"d_flag\", \"all_flag\", \"city_flag\"]\n", "].fillna(0)\n", "\n", "disruption_base_df = disruption_base_df.merge(buffer_store_mapping, on=[\"facility_id\"], how=\"left\")\n", "disruption_base_df[\"d_flag\"] = np.where(\n", "    disruption_base_df[\"facility_id\"].isin(wl_stores_list), 0, disruption_base_df[\"d_flag\"]\n", ")\n", "disruption_base_df[\"change_date\"] = disruption_base_df[\"change_date\"].fillna(l45_date)\n", "disruption_base_df[\"city_flag\"] = np.where(\n", "    disruption_base_df[\"facility_id\"].isin(wl_stores_list)\n", "    & disruption_base_df[\"date_\"].between(\n", "        disruption_base_df[\"change_date\"],\n", "        (pd.to_datetime(disruption_base_df[\"change_date\"]) + timedelta(days=2)),\n", "    ),\n", "    0,\n", "    disruption_base_df[\"city_flag\"],\n", ")\n", "disruption_base_df[\"change_date\"] = disruption_base_df[\"change_date\"].fillna(l45_date)\n", "disruption_base_df[\"flag\"] = (\n", "    disruption_base_df[\"d_flag\"] + disruption_base_df[\"all_flag\"] + disruption_base_df[\"city_flag\"]\n", ")\n", "disruption_base_df = disruption_base_df[disruption_base_df[\"flag\"] == 0]\n", "wow_bumps = disruption_base_df.copy()"]}, {"cell_type": "markdown", "id": "399417cc-769d-42cd-90bc-aba3e1ac8711", "metadata": {}, "source": ["## Adding hex sales for CPC stores"]}, {"cell_type": "code", "execution_count": null, "id": "c6dee0b8-a458-41e7-be38-10dd46d4a38f", "metadata": {}, "outputs": [], "source": ["hex_ = f\"\"\"\n", "select\n", "    date_,\n", "    facility_id,\n", "    item_id as item_id,\n", "    sales as sales,\n", "    lin_extp lin_extp,\n", "    exp_extp exp_extp,\n", "    para_extp para_extp\n", "from\n", "    supply_etls.milk_outlet_hex_sales a\n", "inner join (select slot,max(updated_at) updated_at\n", "                        from supply_etls.milk_outlet_hex_sales\n", "                        where \n", "                        updated_at>=current_date group by 1) b\n", "                on a.updated_at = b.updated_at and a.slot = b.slot\n", "where a.slot = '6 to 23'\n", "and a.updated_at>=current_date\n", "\"\"\"\n", "hex_sales = read_sql_query(hex_, trino)\n", "hex_sales[\"date_\"] = pd.to_datetime(hex_sales[\"date_\"])\n", "hex_sales[[\"facility_id\", \"item_id\", \"sales\", \"lin_extp\", \"exp_extp\", \"para_extp\"]] = hex_sales[\n", "    [\"facility_id\", \"item_id\", \"sales\", \"lin_extp\", \"exp_extp\", \"para_extp\"]\n", "].astype(int)\n", "hex_sales[\"flag\"] = 1\n", "hex_sales.shape"]}, {"cell_type": "code", "execution_count": null, "id": "7d375d3a-5c9f-4f2d-9b24-2d9c159811dd", "metadata": {}, "outputs": [], "source": ["hex_sales.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ee219785-8a59-4d18-b875-2278073314c6", "metadata": {}, "outputs": [], "source": ["disruption_base_df = pd.merge(base_df, disruption_df, on=[\"outlet_id\", \"date_\"], how=\"left\")\n", "\n", "disruption_base_df = pd.merge(\n", "    disruption_base_df, all_disruption, on=[\"facility_id\", \"date_\"], how=\"left\"\n", ")\n", "\n", "disruption_base_df = pd.merge(\n", "    disruption_base_df, city_disruption_df, on=[\"city\", \"date_\"], how=\"left\"\n", ")\n", "\n", "disruption_base_df[[\"d_flag\", \"all_flag\", \"city_flag\"]] = disruption_base_df[\n", "    [\"d_flag\", \"all_flag\", \"city_flag\"]\n", "].fillna(0)\n", "disruption_base_df[\"dis_flag\"] = (\n", "    disruption_base_df[\"d_flag\"] + disruption_base_df[\"all_flag\"] + disruption_base_df[\"city_flag\"]\n", ")\n", "\n", "\n", "disruption_base_df[\"rank\"] = disruption_base_df.groupby([\"facility_id\", \"item_id\"])[\"date_\"].rank(\n", "    method=\"dense\", ascending=False\n", ")\n", "\n", "disruption_base_df = disruption_base_df.merge(\n", "    hex_sales, on=[\"date_\", \"facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "disruption_base_df[\"day_sales_qty\"] = np.where(\n", "    disruption_base_df[\"flag\"] == 1,\n", "    disruption_base_df[\"sales\"],\n", "    disruption_base_df[\"day_sales_qty\"],\n", ")\n", "disruption_base_df[\"day_wt_avail\"] = 1\n", "disruption_base_df = disruption_base_df[disruption_base_df[\"dis_flag\"] == 0]\n", "disruption_base_df[\"rank\"] = disruption_base_df.groupby([\"facility_id\", \"item_id\"])[\"date_\"].rank(\n", "    method=\"dense\", ascending=False\n", ")\n", "disruption_base_df = disruption_base_df[(disruption_base_df[\"rank\"] <= 7)]\n", "disruption_base_df[\"dow\"] = pd.to_datetime(disruption_base_df[\"date_\"]).dt.dayofweek\n", "disruption_base_df[\"tdate\"] = pd.to_datetime(transfer_date)\n", "disruption_base_df[\"tdow\"] = pd.to_datetime(disruption_base_df[\"tdate\"]).dt.dayofweek"]}, {"cell_type": "code", "execution_count": null, "id": "b7701341-5f27-4cbe-9c55-b83438c15b8a", "metadata": {}, "outputs": [], "source": ["disruption_base_df[\n", "    (disruption_base_df.facility_id == 1098) & (disruption_base_df.item_id == 10005109)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "ab194701-281b-4cf0-81b4-aafc9ff7ab79", "metadata": {}, "outputs": [], "source": ["del [base_df, disruption_df]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "c7939e09-ae23-4d06-adab-f3c0c71b433c", "metadata": {}, "source": ["## Weekend Weekday Growth"]}, {"cell_type": "code", "execution_count": null, "id": "8b9bc90e-3317-4695-a64d-2f2e1ea06d8d", "metadata": {}, "outputs": [], "source": ["search = data_df[[\"city\", \"outlet_id\", \"hour_\", \"date_\", \"searches\"]].copy().drop_duplicates()\n", "day_search = (\n", "    search[search[\"hour_\"].between(6, 23)]\n", "    .groupby([\"date_\", \"city\"])\n", "    .agg({\"searches\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"searches\": \"day_search\"})\n", ")\n", "\n", "mor_search = (\n", "    search[search[\"hour_\"].between(6, 17)]\n", "    .groupby([\"date_\", \"city\"])\n", "    .agg({\"searches\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"searches\": \"mor_search\"})\n", ")\n", "\n", "early_mor_search = (\n", "    search[search[\"hour_\"].between(6, 8)]\n", "    .groupby([\"date_\", \"city\"])\n", "    .agg({\"searches\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"searches\": \"early_mor_search\"})\n", ")\n", "\n", "noon_search = (\n", "    search[search[\"hour_\"].between(11, 17)]\n", "    .groupby([\"date_\", \"city\"])\n", "    .agg({\"searches\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"searches\": \"noon_search\"})\n", ")\n", "\n", "\n", "eve_search = (\n", "    search[search[\"hour_\"].between(current_hour, 23)]\n", "    .groupby([\"date_\", \"city\"])\n", "    .agg({\"searches\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"searches\": \"eve_search\"})\n", ")\n", "\n", "night_search = (\n", "    search[search[\"hour_\"].between(0, 5)]\n", "    .groupby([\"date_\", \"city\"])\n", "    .agg({\"searches\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"searches\": \"night_search\"})\n", ")\n", "\n", "\n", "search_df = day_search.merge(mor_search, on=[\"date_\", \"city\"], how=\"left\")\n", "search_df = search_df.merge(early_mor_search, on=[\"date_\", \"city\"], how=\"left\")\n", "search_df = search_df.merge(noon_search, on=[\"date_\", \"city\"], how=\"left\")\n", "search_df = search_df.merge(eve_search, on=[\"date_\", \"city\"], how=\"left\")\n", "search_df = search_df.merge(night_search, on=[\"date_\", \"city\"], how=\"left\")\n", "\n", "search_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "8780eba3-b5d6-4380-a0a3-138f8a3e55ee", "metadata": {}, "outputs": [], "source": ["del [search, day_search, mor_search, early_mor_search, noon_search, eve_search, night_search]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "1911e6f2-5b8b-410a-b320-50cc00480caa", "metadata": {}, "outputs": [], "source": ["search_df[\"dow\"] = pd.to_datetime(search_df[\"date_\"]).dt.dayofweek\n", "search_df[\"city\"] = search_df[\"city\"].replace(\"HR-NCR\", \"Gurgaon\")\n", "search_df[\"city\"] = search_df[\"city\"].replace(\"UP-NCR\", \"Ghaziabad\")\n", "search_df = search_df[~search_df[\"city\"].isin({\"Not in service area\"})]\n", "search_df[\n", "    [\n", "        \"early_mor_search\",\n", "        \"mor_search\",\n", "        \"eve_search\",\n", "        \"noon_search\",\n", "        \"night_search\",\n", "        \"day_search\",\n", "    ]\n", "] = search_df[\n", "    [\n", "        \"early_mor_search\",\n", "        \"mor_search\",\n", "        \"eve_search\",\n", "        \"noon_search\",\n", "        \"night_search\",\n", "        \"day_search\",\n", "    ]\n", "].fillna(\n", "    0\n", ")\n", "search_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "168a4618-919d-47d9-8fa2-45b4624d022d", "metadata": {}, "outputs": [], "source": ["search_increment_agg_df = (\n", "    search_df.groupby([\"city\", \"dow\"])\n", "    .agg(\n", "        {\n", "            \"mor_search\": \"mean\",\n", "            \"eve_search\": \"mean\",\n", "            \"noon_search\": \"mean\",\n", "            \"night_search\": \"mean\",\n", "            \"day_search\": \"mean\",\n", "            \"early_mor_search\": \"mean\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "search_increment_agg_df[\n", "    [\n", "        \"mor_search\",\n", "        \"eve_search\",\n", "        \"noon_search\",\n", "        \"night_search\",\n", "        \"day_search\",\n", "        \"early_mor_search\",\n", "    ]\n", "] = np.round(\n", "    search_increment_agg_df[\n", "        [\n", "            \"mor_search\",\n", "            \"eve_search\",\n", "            \"noon_search\",\n", "            \"night_search\",\n", "            \"day_search\",\n", "            \"early_mor_search\",\n", "        ]\n", "    ],\n", "    0,\n", ")\n", "search_increment_agg_df[\n", "    [\n", "        \"mor_search\",\n", "        \"eve_search\",\n", "        \"noon_search\",\n", "        \"night_search\",\n", "        \"day_search\",\n", "        \"early_mor_search\",\n", "    ]\n", "] = (\n", "    search_increment_agg_df[\n", "        [\n", "            \"mor_search\",\n", "            \"eve_search\",\n", "            \"noon_search\",\n", "            \"night_search\",\n", "            \"day_search\",\n", "            \"early_mor_search\",\n", "        ]\n", "    ]\n", ").astype(\n", "    int\n", ")\n", "\n", "pre_search_df = search_increment_agg_df.copy()\n", "pre_search_df = pre_search_df.rename(\n", "    columns={\n", "        \"mor_search\": \"new_mor_search\",\n", "        \"eve_search\": \"new_eve_search\",\n", "        \"noon_search\": \"new_noon_search\",\n", "        \"night_search\": \"new_night_search\",\n", "        \"day_search\": \"new_day_search\",\n", "        \"early_mor_search\": \"new_early_mor_search\",\n", "    }\n", ")\n", "pre_search_df = (\n", "    pre_search_df[\n", "        [\n", "            \"city\",\n", "            \"new_mor_search\",\n", "            \"new_eve_search\",\n", "            \"new_noon_search\",\n", "            \"new_night_search\",\n", "            \"new_day_search\",\n", "            \"new_early_mor_search\",\n", "        ]\n", "    ]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "final_search_increment_df = pd.merge(\n", "    search_increment_agg_df, pre_search_df, on=[\"city\"], how=\"left\"\n", ")\n", "\n", "search_increment_agg_df = search_increment_agg_df.rename(\n", "    columns={\n", "        \"dow\": \"fdow\",\n", "        \"mor_search\": \"new_mor_search\",\n", "        \"eve_search\": \"new_eve_search\",\n", "        \"noon_search\": \"new_noon_search\",\n", "        \"night_search\": \"new_night_search\",\n", "        \"day_search\": \"new_day_search\",\n", "        \"early_mor_search\": \"new_early_mor_search\",\n", "    }\n", ")\n", "\n", "final_search_increment_df = pd.merge(\n", "    final_search_increment_df,\n", "    search_increment_agg_df,\n", "    on=[\n", "        \"city\",\n", "        \"new_mor_search\",\n", "        \"new_eve_search\",\n", "        \"new_noon_search\",\n", "        \"new_night_search\",\n", "        \"new_day_search\",\n", "        \"new_early_mor_search\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "final_search_increment_df[\"emsbf\"] = 1 + (\n", "    (\n", "        final_search_increment_df[\"new_early_mor_search\"]\n", "        / final_search_increment_df[\"early_mor_search\"]\n", "    )\n", "    - 1\n", ")\n", "\n", "final_search_increment_df[\"msbf\"] = 1 + (\n", "    (final_search_increment_df[\"new_mor_search\"] / final_search_increment_df[\"mor_search\"]) - 1\n", ")\n", "\n", "final_search_increment_df[\"esbf\"] = 1 + (\n", "    (final_search_increment_df[\"new_eve_search\"] / final_search_increment_df[\"eve_search\"]) - 1\n", ")\n", "\n", "final_search_increment_df[\"ansbf\"] = 1 + (\n", "    (final_search_increment_df[\"new_noon_search\"] / final_search_increment_df[\"noon_search\"]) - 1\n", ")\n", "\n", "final_search_increment_df[\"nsbf\"] = 1 + (\n", "    (final_search_increment_df[\"new_night_search\"] / final_search_increment_df[\"night_search\"]) - 1\n", ")\n", "\n", "final_search_increment_df[\"dsbf\"] = 1 + (\n", "    (final_search_increment_df[\"new_day_search\"] / final_search_increment_df[\"day_search\"]) - 1\n", ")\n", "\n", "\n", "final_search_increment_df = (\n", "    final_search_increment_df[\n", "        [\"city\", \"dow\", \"fdow\", \"emsbf\", \"msbf\", \"esbf\", \"ansbf\", \"nsbf\", \"dsbf\"]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "final_search_increment_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "e3aec337-f39f-4a19-b53a-3e6dfb804a4a", "metadata": {}, "outputs": [], "source": ["del [search_df, search_increment_agg_df, pre_search_df]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "e419f4d4-3c6f-4c48-a38f-3e8f19b90485", "metadata": {}, "source": ["### Taking carts "]}, {"cell_type": "code", "execution_count": null, "id": "21d5e0ee-a1e7-4f14-888e-0770b2597360", "metadata": {}, "outputs": [], "source": ["carts_df = data_df[[\"city\", \"facility_id\", \"hour_\", \"date_\", \"carts\"]].copy().drop_duplicates()\n", "day_carts = (\n", "    carts_df[carts_df[\"hour_\"].between(6, 23)]\n", "    .groupby([\"date_\", \"facility_id\", \"city\"])\n", "    .agg({\"carts\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"carts\": \"day_carts\"})\n", ")\n", "\n", "mor_carts = (\n", "    carts_df[carts_df[\"hour_\"].between(6, 17)]\n", "    .groupby([\"date_\", \"facility_id\", \"city\"])\n", "    .agg({\"carts\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"carts\": \"mor_carts\"})\n", ")\n", "\n", "early_mor_carts = (\n", "    carts_df[carts_df[\"hour_\"].between(6, 8)]\n", "    .groupby([\"date_\", \"facility_id\", \"city\"])\n", "    .agg({\"carts\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"carts\": \"early_mor_carts\"})\n", ")\n", "\n", "noon_carts = (\n", "    carts_df[carts_df[\"hour_\"].between(11, 17)]\n", "    .groupby([\"date_\", \"facility_id\", \"city\"])\n", "    .agg({\"carts\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"carts\": \"noon_carts\"})\n", ")\n", "\n", "\n", "eve_carts = (\n", "    carts_df[carts_df[\"hour_\"].between(current_hour, 23)]\n", "    .groupby([\"date_\", \"facility_id\", \"city\"])\n", "    .agg({\"carts\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"carts\": \"eve_carts\"})\n", ")\n", "\n", "night_carts = (\n", "    carts_df[carts_df[\"hour_\"].between(0, 5)]\n", "    .groupby([\"date_\", \"facility_id\", \"city\"])\n", "    .agg({\"carts\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"carts\": \"night_carts\"})\n", ")\n", "\n", "\n", "carts = day_carts.merge(mor_carts, on=[\"date_\", \"facility_id\", \"city\"], how=\"left\")\n", "carts = carts.merge(early_mor_carts, on=[\"date_\", \"facility_id\", \"city\"], how=\"left\")\n", "carts = carts.merge(noon_carts, on=[\"date_\", \"facility_id\", \"city\"], how=\"left\")\n", "carts = carts.merge(eve_carts, on=[\"date_\", \"facility_id\", \"city\"], how=\"left\")\n", "carts = carts.merge(night_carts, on=[\"date_\", \"facility_id\", \"city\"], how=\"left\")\n", "\n", "carts.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "70672d6c-943e-4ab0-a183-5c9ad1a5e131", "metadata": {}, "outputs": [], "source": ["del [carts_df, day_carts, mor_carts, early_mor_carts, noon_carts, eve_carts, night_carts, data_df]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "17520c32-e74c-4291-a28e-5ac0192b624b", "metadata": {}, "outputs": [], "source": ["carts[\"dow\"] = pd.to_datetime(carts[\"date_\"]).dt.dayofweek\n", "carts_increment_df = (\n", "    carts.groupby([\"city\", \"date_\", \"dow\"])\n", "    .agg(\n", "        {\n", "            \"early_mor_carts\": \"sum\",\n", "            \"mor_carts\": \"sum\",\n", "            \"eve_carts\": \"sum\",\n", "            \"noon_carts\": \"sum\",\n", "            \"night_carts\": \"sum\",\n", "            \"day_carts\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "\n", "carts_increment_df[\n", "    [\n", "        \"early_mor_carts\",\n", "        \"mor_carts\",\n", "        \"eve_carts\",\n", "        \"noon_carts\",\n", "        \"night_carts\",\n", "        \"day_carts\",\n", "    ]\n", "] = carts_increment_df[\n", "    [\n", "        \"early_mor_carts\",\n", "        \"mor_carts\",\n", "        \"eve_carts\",\n", "        \"noon_carts\",\n", "        \"night_carts\",\n", "        \"day_carts\",\n", "    ]\n", "].fillna(\n", "    0\n", ")\n", "\n", "carts_increment_agg_df = (\n", "    carts_increment_df.groupby([\"city\", \"dow\"])\n", "    .agg(\n", "        {\n", "            \"mor_carts\": percentile,\n", "            \"eve_carts\": percentile,\n", "            \"noon_carts\": percentile,\n", "            \"night_carts\": percentile,\n", "            \"day_carts\": percentile,\n", "            \"early_mor_carts\": percentile,\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "carts_increment_agg_df[\n", "    [\n", "        \"mor_carts\",\n", "        \"eve_carts\",\n", "        \"noon_carts\",\n", "        \"night_carts\",\n", "        \"day_carts\",\n", "        \"early_mor_carts\",\n", "    ]\n", "] = np.round(\n", "    carts_increment_agg_df[\n", "        [\n", "            \"mor_carts\",\n", "            \"eve_carts\",\n", "            \"noon_carts\",\n", "            \"night_carts\",\n", "            \"day_carts\",\n", "            \"early_mor_carts\",\n", "        ]\n", "    ],\n", "    0,\n", ")\n", "carts_increment_agg_df[\n", "    [\n", "        \"mor_carts\",\n", "        \"eve_carts\",\n", "        \"noon_carts\",\n", "        \"night_carts\",\n", "        \"day_carts\",\n", "        \"early_mor_carts\",\n", "    ]\n", "] = carts_increment_agg_df[\n", "    [\n", "        \"mor_carts\",\n", "        \"eve_carts\",\n", "        \"noon_carts\",\n", "        \"night_carts\",\n", "        \"day_carts\",\n", "        \"early_mor_carts\",\n", "    ]\n", "].astype(\n", "    int\n", ")\n", "\n", "carts_df = carts_increment_agg_df.copy()\n", "carts_df = carts_df.rename(\n", "    columns={\n", "        \"mor_carts\": \"new_mor_carts\",\n", "        \"eve_carts\": \"new_eve_carts\",\n", "        \"noon_carts\": \"new_noon_carts\",\n", "        \"night_carts\": \"new_night_carts\",\n", "        \"day_carts\": \"new_day_carts\",\n", "        \"early_mor_carts\": \"new_early_mor_carts\",\n", "    }\n", ")\n", "carts_df = (\n", "    carts_df[\n", "        [\n", "            \"city\",\n", "            \"new_mor_carts\",\n", "            \"new_eve_carts\",\n", "            \"new_noon_carts\",\n", "            \"new_night_carts\",\n", "            \"new_day_carts\",\n", "            \"new_early_mor_carts\",\n", "        ]\n", "    ]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "final_carts_increment_df = pd.merge(carts_increment_agg_df, carts_df, on=[\"city\"], how=\"left\")\n", "\n", "carts_increment_agg_df = carts_increment_agg_df.rename(\n", "    columns={\n", "        \"dow\": \"fdow\",\n", "        \"mor_carts\": \"new_mor_carts\",\n", "        \"eve_carts\": \"new_eve_carts\",\n", "        \"noon_carts\": \"new_noon_carts\",\n", "        \"night_carts\": \"new_night_carts\",\n", "        \"day_carts\": \"new_day_carts\",\n", "        \"early_mor_carts\": \"new_early_mor_carts\",\n", "    }\n", ")\n", "\n", "final_carts_increment_df = pd.merge(\n", "    final_carts_increment_df,\n", "    carts_increment_agg_df,\n", "    on=[\n", "        \"city\",\n", "        \"new_mor_carts\",\n", "        \"new_eve_carts\",\n", "        \"new_noon_carts\",\n", "        \"new_night_carts\",\n", "        \"new_day_carts\",\n", "        \"new_early_mor_carts\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "final_carts_increment_df[\"cembf\"] = 1 + (\n", "    (final_carts_increment_df[\"new_early_mor_carts\"] / final_carts_increment_df[\"early_mor_carts\"])\n", "    - 1\n", ")\n", "\n", "final_carts_increment_df[\"cmbf\"] = 1 + (\n", "    (final_carts_increment_df[\"new_mor_carts\"] / final_carts_increment_df[\"mor_carts\"]) - 1\n", ")\n", "\n", "final_carts_increment_df[\"cebf\"] = 1 + (\n", "    (final_carts_increment_df[\"new_eve_carts\"] / final_carts_increment_df[\"eve_carts\"]) - 1\n", ")\n", "\n", "final_carts_increment_df[\"canbf\"] = 1 + (\n", "    (final_carts_increment_df[\"new_noon_carts\"] / final_carts_increment_df[\"noon_carts\"]) - 1\n", ")\n", "\n", "final_carts_increment_df[\"cnbf\"] = 1 + (\n", "    (final_carts_increment_df[\"new_night_carts\"] / final_carts_increment_df[\"night_carts\"]) - 1\n", ")\n", "\n", "final_carts_increment_df[\"cdbf\"] = 1 + (\n", "    (final_carts_increment_df[\"new_day_carts\"] / final_carts_increment_df[\"day_carts\"]) - 1\n", ")\n", "\n", "\n", "final_carts_increment_df = (\n", "    final_carts_increment_df[\n", "        [\"city\", \"dow\", \"fdow\", \"cembf\", \"cmbf\", \"cebf\", \"canbf\", \"cnbf\", \"cdbf\"]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "\n", "final_carts_increment_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "b4eacae7-fb4f-4921-a10b-b4d4f7de2517", "metadata": {}, "outputs": [], "source": ["del [carts_increment_agg_df, carts_increment_df]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "396dcb42-9202-4fad-8cee-b08a7011289e", "metadata": {}, "outputs": [], "source": ["final_increment_df = pd.merge(\n", "    final_search_increment_df,\n", "    final_carts_increment_df,\n", "    on=[\"city\", \"dow\", \"fdow\"],\n", "    how=\"left\",\n", ")\n", "\n", "final_increment_df[\"embf\"] = np.where(\n", "    final_increment_df[\"cembf\"].isna(),\n", "    final_increment_df[\"emsbf\"],\n", "    (0.5 * final_increment_df[\"cembf\"]) + (0.5 * final_increment_df[\"emsbf\"]),\n", ")\n", "\n", "final_increment_df[\"mbf\"] = np.where(\n", "    final_increment_df[\"cmbf\"].isna(),\n", "    final_increment_df[\"msbf\"],\n", "    (0.5 * final_increment_df[\"cmbf\"]) + (0.5 * final_increment_df[\"msbf\"]),\n", ")\n", "\n", "final_increment_df[\"ebf\"] = np.where(\n", "    final_increment_df[\"cebf\"].isna(),\n", "    final_increment_df[\"esbf\"],\n", "    (0.5 * final_increment_df[\"cebf\"]) + (0.5 * final_increment_df[\"esbf\"]),\n", ")\n", "\n", "final_increment_df[\"anbf\"] = np.where(\n", "    final_increment_df[\"canbf\"].isna(),\n", "    final_increment_df[\"ansbf\"],\n", "    (0.5 * final_increment_df[\"canbf\"]) + (0.5 * final_increment_df[\"ansbf\"]),\n", ")\n", "\n", "final_increment_df[\"nbf\"] = np.where(\n", "    final_increment_df[\"cnbf\"].isna(),\n", "    final_increment_df[\"nsbf\"],\n", "    (0.5 * final_increment_df[\"cnbf\"]) + (0.5 * final_increment_df[\"nsbf\"]),\n", ")\n", "\n", "final_increment_df[\"dbf\"] = np.where(\n", "    final_increment_df[\"cdbf\"].isna(),\n", "    final_increment_df[\"dsbf\"],\n", "    (0.5 * final_increment_df[\"cdbf\"]) + (0.5 * final_increment_df[\"dsbf\"]),\n", ")\n", "\n", "final_increment_df = (\n", "    final_increment_df[[\"city\", \"dow\", \"fdow\", \"embf\", \"mbf\", \"ebf\", \"anbf\", \"nbf\", \"dbf\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", "    .rename(columns={\"fdow\": \"tdow\"})\n", ")\n", "final_increment_df.shape"]}, {"cell_type": "markdown", "id": "9c105e4f-c2c8-46be-9fe2-10f6f004d3e8", "metadata": {}, "source": ["### Weekend weekday bumps based on extrapolated sales of 6am-6pm (prev sales of 6am-6pm - non exrapolated)"]}, {"cell_type": "code", "execution_count": null, "id": "cc1f3848-595a-4d68-80db-6eabc0ad1b2b", "metadata": {}, "outputs": [], "source": ["new_bumps = wow_bumps[wow_bumps[\"day_sales_qty\"] > 0][\n", "    [\n", "        \"city\",\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"date_\",\n", "        \"day_sales_qty\",\n", "        \"day_wt_avail\",\n", "    ]\n", "]\n", "new_bumps[\"tdate\"] = pd.to_datetime(transfer_date)\n", "new_bumps[\"dow\"] = pd.to_datetime(new_bumps[\"date_\"]).dt.dayofweek\n", "new_bumps[\"tdow\"] = pd.to_datetime(transfer_date).dayofweek\n", "new_bumps[\"exp_sales\"] = new_bumps[\"day_sales_qty\"] * (1 + 0.35 * (1 - new_bumps[\"day_wt_avail\"]))\n", "\n", "new_bumps_agg = (\n", "    new_bumps.groupby([\"facility_id\", \"city\", \"date_\", \"tdow\", \"dow\"])\n", "    .agg({\"exp_sales\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "new_increment_agg_df = (\n", "    new_bumps_agg.groupby([\"facility_id\", \"city\", \"tdow\", \"dow\"])\n", "    .agg({\"exp_sales\": percentile})\n", "    .reset_index()\n", "    .rename(columns={\"exp_sales\": \"dow_sales\"})\n", ")\n", "new_fdow_sales = (\n", "    new_increment_agg_df[(new_increment_agg_df[\"tdow\"] == new_increment_agg_df[\"dow\"])][\n", "        [\"facility_id\", \"city\", \"dow_sales\"]\n", "    ]\n", "    .drop_duplicates()\n", "    .rename(columns={\"dow_sales\": \"tdow_sales\"})\n", ")\n", "# increment_fdow =\n", "new_increment_agg_df = new_increment_agg_df.merge(\n", "    new_fdow_sales,\n", "    on=[\"city\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "\n", "new_increment_agg_df[\"bf\"] = new_increment_agg_df[\"tdow_sales\"] / new_increment_agg_df[\"dow_sales\"]\n", "new_increment_agg_df[\"bf\"] = new_increment_agg_df[\"bf\"].fillna(1).astype(float)\n", "new_increment_agg_df[\"bf\"] = new_increment_agg_df[\"bf\"].replace(np.inf, 1).astype(float)\n", "# new_increment_agg_df = new_increment_agg_df[[\"facility_id\", \"city\", \"tdow\", \"dow\", \"bf\"]]\n", "new_increment_agg_df.head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "e8e77e86-af92-48be-877f-781011a0c4d7", "metadata": {}, "outputs": [], "source": ["final_increment_df_2 = new_increment_agg_df.merge(\n", "    final_increment_df, on=[\"city\", \"dow\", \"tdow\"], how=\"left\"\n", ")\n", "final_increment_df_2[\"mbf\"] = final_increment_df_2[\"bf\"]\n", "final_increment_df_2[\"ebf\"] = final_increment_df_2[\"bf\"]\n", "final_increment_df_2[\"dbf\"] = final_increment_df_2[\"bf\"]\n", "final_increment_df_2 = (\n", "    final_increment_df_2[\n", "        [\n", "            \"facility_id\",\n", "            \"city\",\n", "            \"dow\",\n", "            \"tdow\",\n", "            \"embf\",\n", "            \"mbf\",\n", "            \"ebf\",\n", "            \"anbf\",\n", "            \"nbf\",\n", "            \"dbf\",\n", "        ]\n", "    ]\n", "    .fillna(1)\n", "    .replace(np.inf, 1)\n", ")\n", "final_increment_df_2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7807e166-477e-4fa1-8d88-9aecccba3ea9", "metadata": {"tags": []}, "outputs": [], "source": ["sis_store = f\"\"\"\n", "\n", "            select\n", "                facility_id as fac_id,\n", "                sister_store_facility_id facility_id\n", "            from\n", "            supply_etls.e3_new_darkstores\n", "            where final_ob_date >= current_date - interval '45' day\n", "                and store_type in ('Replacement Store','Buffer Store')\n", "                and sister_store_facility_id in {facility_id_list}\n", "            \n", "            \"\"\"\n", "sis_store_df = read_sql_query(sis_store, trino)\n", "sis_fac_list = tuple(list(sis_store_df[\"fac_id\"].unique()) + ([-1, -2]))\n", "len(sis_fac_list)"]}, {"cell_type": "code", "execution_count": null, "id": "0fa6c8de-bd94-476e-b45c-9908a4275fa2", "metadata": {}, "outputs": [], "source": ["sis_bump = sis_store_df.merge(final_increment_df_2, on=[\"facility_id\"], how=\"left\")\n", "sis_bump.head()"]}, {"cell_type": "code", "execution_count": null, "id": "dbbc9032-d6c3-4634-ad76-f95b392ddc32", "metadata": {}, "outputs": [], "source": ["old_store = final_increment_df_2[~(final_increment_df_2[\"facility_id\"].isin(l21_facility_list))]\n", "new_store_bumps = final_increment_df_2[\n", "    (final_increment_df_2[\"facility_id\"].isin(l21_facility_list))\n", "    & ~(final_increment_df_2[\"facility_id\"].isin(sis_fac_list))\n", "]\n", "\n", "sis_store_bumps = (\n", "    sis_bump[sis_bump[\"fac_id\"].isin(l21_facility_list)].copy().drop(columns={\"facility_id\"})\n", ")\n", "sis_store_bumps.rename(columns={\"fac_id\": \"facility_id\"}, inplace=True)\n", "sis_store_bumps.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f7eef08c-4cc1-4ab5-9224-8b79ae0418a4", "metadata": {}, "outputs": [], "source": ["final_increment_df_2 = pd.concat([old_store, new_store_bumps, sis_store_bumps])\n", "final_increment_df_2 = final_increment_df_2.fillna(1).replace(np.inf, 1)\n", "final_increment_df_2.head()"]}, {"cell_type": "markdown", "id": "5f204cd4-2a68-43b4-9884-c44f81e47671", "metadata": {}, "source": ["## Forecast Data For Transfer"]}, {"cell_type": "code", "execution_count": null, "id": "bc65d57a-215d-4237-8a51-e0ac29e9fbf6", "metadata": {}, "outputs": [], "source": ["forecast_query = f\"\"\"\n", "    WITH base AS(\n", "        SELECT a.tdate, a.facility_id, a.item_id, a.outlet_id, ftype_, stype_, slota_transfer_qty, slotb_transfer_qty\n", "        FROM supply_etls.milk_indent_correction_v2 a\n", "        JOIN (\n", "            SELECT DATE(tdate) as tdate, facility_id, item_id, MAX(updated_at) AS updated_at\n", "            FROM supply_etls.milk_indent_correction_v2\n", "            WHERE date_ist >= DATE('{transfer_date}') - interval '3' day\n", "            GROUP BY 1,2,3\n", "        ) b ON a.item_id = b.item_id AND a.facility_id = b.facility_id AND a.updated_at = b.updated_at AND a.tdate = b.tdate\n", "        WHERE a.tdate IN (DATE('{transfer_date}'))\n", "        AND a.date_ist >= DATE('{transfer_date}') - interval '3' day\n", ")\n", "    SELECT tdate , cl.name as city , a.facility_id, item_id , ftype_, stype_, slota_transfer_qty, slotb_transfer_qty\n", "    FROM base a\n", "    JOIN retail.console_outlet co ON co.id = a.outlet_id\n", "    LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id\n", "    \n", "    \"\"\"\n", "forecast_df = read_sql_query(forecast_query, trino)\n", "\n", "forecast_df[\"tdate\"] = pd.to_datetime(forecast_df[\"tdate\"])\n", "\n", "forecast_df[\"tot_transfer\"] = forecast_df[\"slota_transfer_qty\"] + forecast_df[\"slotb_transfer_qty\"]\n", "\n", "forecast_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "461f6004-1c59-4354-bd5b-081c86306219", "metadata": {}, "outputs": [], "source": ["forecast_df.head(1)"]}, {"cell_type": "markdown", "id": "dfb31e32-d906-4fb5-b164-bfabc6abf2ec", "metadata": {}, "source": ["## FE current Inventory"]}, {"cell_type": "code", "execution_count": null, "id": "91424374-b527-4bb6-85f8-7633e9266392", "metadata": {}, "outputs": [], "source": ["fe_inv_query = f\"\"\"\n", "    with item_inv_base as (\n", "            select \n", "            si.item_id as item_id, \n", "            outlet_id, \n", "            max(si.updated_at + interval '330' minute) as updated_at_ist,\n", "            sum(case when state = 'GOOD' then quantity end) as net_inv,\n", "            sum(case when state = 'BLOCKED' then quantity end) as blocked_inv\n", "            from dynamodb.blinkit_store_inventory_service_oi_rt_view_v2 si\n", "            where outlet_id in ({','.join([f\"'{i}'\" for i in outlet_id_list])})\n", "            and item_id in ({','.join([f\"'{i}'\" for i in item_id_list])})  \n", "            group by 1,2\n", "        )\n", "\n", "        select \n", "        iib.outlet_id, \n", "        iib.item_id, \n", "        (case when net_inv - coalesce(blocked_inv,0) > 0 then net_inv - coalesce(blocked_inv,0) else 0 end) as net_inv\n", "        from item_inv_base iib\n", "    \"\"\"\n", "inv_df = read_sql_query(fe_inv_query, trino)\n", "inv_df[[\"outlet_id\", \"item_id\", \"net_inv\"]] = inv_df[[\"outlet_id\", \"item_id\", \"net_inv\"]].astype(\n", "    int\n", ")\n", "fe_inv_df = (\n", "    inv_df.groupby([\"item_id\", \"outlet_id\"])\n", "    .agg({\"net_inv\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"net_inv\": \"current_inv\"})\n", ")\n", "\n", "fe_inv_df.head(1)"]}, {"cell_type": "raw", "id": "0edc2694-2085-42cf-98b9-3a7facc98779", "metadata": {}, "source": ["# for simulation\n", "fe_inv = f\"\"\"\n", " with inv_base as (\n", "    select\n", "\n", "        a.outlet_id,\n", "        a.item_id,\n", "        min(total_inventory_count) as ci\n", "        from\n", "        dwh.absolute_hourly_inventory_availability_perishables a\n", "        JOIN retail.console_outlet co ON co.id = a.outlet_id\n", "        LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id\n", "        where snapshot_date_ist = date'{transfer_date}' - interval '1' day\n", "        and lower(l2_category) = 'fresh milk'\n", "        and time_flag = 'Hourly Bucket'\n", "        and extract(hour from snapshot_hour_ist) in ({current_hour})\n", "        and a.item_id in {item_id_list}\n", "        group by 1,2\n", ")\n", "\n", "    select\n", "        outlet_id,\n", "        item_id,\n", "        ci as current_inv\n", "    from\n", "        inv_base\n", "    group by 1,2,3\n", "\n", " \"\"\"\n", "fe_inv_df = read_sql_query(fe_inv, trino)\n", "fe_inv_df.head(2)"]}, {"cell_type": "markdown", "id": "4d78c10c-8b8f-4376-b57e-c989c05df5cb", "metadata": {}, "source": ["### Hyperpure Backend & Item List"]}, {"cell_type": "code", "execution_count": null, "id": "290d64cd-8dc7-471b-9039-3c536d79daf8", "metadata": {}, "outputs": [], "source": ["backend_query = \"\"\"\n", "    SELECT om.outlet_id as hot_outlet_id, om.facility_id, \n", "    CASE \n", "        WHEN wom.cloud_store_id IS NULL THEN om.outlet_id \n", "        ELSE wom.cloud_store_id \n", "    END AS inv_outlet_id, rco.business_type_id  \n", "    FROM po.physical_facility_outlet_mapping om\n", "    LEFT JOIN (\n", "        SELECT id, tax_location_id, \n", "        CASE \n", "            WHEN id = 581 THEN 12 \n", "            ELSE business_type_id \n", "        END AS business_type_id\n", "        FROM retail.console_outlet\n", "        WHERE lake_active_record\n", "    ) rco ON rco.id = om.outlet_id \n", "    LEFT JOIN (\n", "        SELECT DISTINCT warehouse_id, cloud_store_id \n", "        FROM retail.warehouse_outlet_mapping \n", "        WHERE active = 1 AND lake_active_record\n", "    ) wom ON wom.warehouse_id = om.outlet_id\n", "    WHERE rco.business_type_id in (21) AND om.active = 1 AND ars_active = 1 AND is_primary = 1\n", "\"\"\"\n", "hp_outlet_mapping = read_sql_query(backend_query, trino)\n", "hp_backend_facility_list = tuple(set(hp_outlet_mapping[\"facility_id\"].unique()))\n", "\n", "item_query = f\"\"\"\n", "    SELECT  DISTINCT outlet_id, tag_value AS be_outlet_id, cb.facility_id AS be_facility_id,co.facility_id as fe_facility_id, tm.item_id as item_id\n", "    FROM rpc.item_outlet_tag_mapping tm\n", "    JOIN retail.console_outlet co ON co.id = outlet_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "    JOIN retail.console_outlet cb ON cb.id = CAST(tag_value AS int) AND cb.active = 1\n", "    WHERE cb.facility_id IN {hp_backend_facility_list} AND tm.item_id IN (SELECT DISTINCT item_id FROM rpc.item_category_details WHERE l2_id = 1185) \n", "    AND tm.active = 1 AND tm.tag_type_id = 8\n", "    \"\"\"\n", "facility_mapping = read_sql_query(item_query, trino)\n", "\n", "facility_mapping[\"be_outlet_id\"] = facility_mapping[\"be_outlet_id\"].astype(int)\n", "\n", "\n", "hp_backend_list = list(facility_mapping[\"be_outlet_id\"].unique())\n", "hp_item_list = list(map(int, list(facility_mapping[\"item_id\"].unique())))\n", "\n", "len(hp_backend_list), len(hp_item_list)"]}, {"cell_type": "markdown", "id": "7286ed25-ff84-468d-afe8-294533923db1", "metadata": {}, "source": ["### Hpyerpure Backend Inventory"]}, {"cell_type": "code", "execution_count": null, "id": "70a234fd-b2ca-4408-aa98-15dc9e1c07ba", "metadata": {}, "outputs": [], "source": ["def get_inventory(be_id):\n", "    json_req = {\"item_ids\": hp_item_list, \"get_projected_inventory\": False}\n", "    url = \"http://po.retail.prod.grofer.io/v1/hyperpure/\" + be_id + \"/item-available-quantity/\"\n", "    response = requests.post(url, json=json_req)\n", "    inv_df = response.json()\n", "    if response.status_code == 200:\n", "        inventory_df = inv_df[\"data\"][\"inventories\"]\n", "        inventory_df = pd.DataFrame.from_dict(inventory_df)\n", "        return inventory_df\n", "    else:\n", "        return pd.DataFrame()\n", "\n", "\n", "df = pd.DataFrame()\n", "final_df = pd.DataFrame()\n", "\n", "for i in hp_backend_list:\n", "    df = get_inventory(str(i))\n", "    if df.shape[0] > 0:\n", "        df[\"be_inv_outlet_id\"] = i\n", "        final_df = pd.concat([final_df, df])\n", "\n", "hp_be_inv_df = final_df[[\"item_id\", \"be_inv_outlet_id\", \"quantity\"]].rename(\n", "    columns={\"quantity\": \"be_inv\"}\n", ")\n", "hp_be_inv_df.shape"]}, {"cell_type": "markdown", "id": "ede54c6f-705d-4881-8e0d-90a262a66362", "metadata": {}, "source": ["### Blinkit Backend Inventory"]}, {"cell_type": "code", "execution_count": null, "id": "d4c20e11-8a10-4bec-b49f-8ab84579bade", "metadata": {}, "outputs": [], "source": ["be_id = base_pre_df[(~base_pre_df[\"be_inv_outlet_id\"].isna())][[\"be_inv_outlet_id\"]]\n", "\n", "be_id_list = tuple(set(base_pre_df[\"be_inv_outlet_id\"].to_list()))\n", "\n", "be_inv_query = f\"\"\"\n", "    SELECT item_id, outlet_id AS be_inv_outlet_id, actual_quantity, blocked_quantity, \n", "    CASE \n", "        WHEN (actual_quantity - blocked_quantity) < 0 THEN 0 \n", "        ELSE (actual_quantity - blocked_quantity)\n", "    END AS net_inv\n", "    FROM (\n", "        SELECT iii.item_id, iii.outlet_id, iii.quantity AS actual_quantity,\n", "        (COALESCE(SUM(CASE WHEN iibi.blocked_type IN (1,2,5) THEN iibi.quantity ELSE 0 END),0)) AS blocked_quantity\n", "        FROM ims.ims_item_inventory iii\n", "        LEFT JOIN (\n", "            SELECT item_id, outlet_id, blocked_type, CASE WHEN quantity < 0 THEN 0 ELSE quantity END AS quantity\n", "            FROM ims.ims_item_blocked_inventory\n", "            WHERE active = 1\n", "        ) iibi ON iii.item_id = iibi.item_id AND iii.outlet_id = iibi.outlet_id\n", "        WHERE iii.item_id IN {item_id_list} AND iii.outlet_id IN {be_id_list} AND iii.active = 1\n", "        GROUP BY 1, 2, 3\n", "    ) ims\n", "    \"\"\"\n", "inv_df = read_sql_query(be_inv_query, trino)\n", "\n", "bl_be_inv_df = (\n", "    inv_df.groupby([\"item_id\", \"be_inv_outlet_id\"])\n", "    .agg({\"net_inv\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"net_inv\": \"be_inv\"})\n", ")\n", "\n", "bl_be_inv_df = bl_be_inv_df[~(bl_be_inv_df[\"be_inv_outlet_id\"].isin(hp_backend_list))]\n", "\n", "bl_be_inv_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "fc3af904-9ee7-4306-ac69-17dd0023a486", "metadata": {}, "outputs": [], "source": ["be_inv_df = pd.concat([bl_be_inv_df, hp_be_inv_df]).reset_index().drop(columns={\"index\"})\n", "\n", "check_df = base_pre_df[(~base_pre_df[\"be_inv_outlet_id\"].isna())][\n", "    [\"be_inv_outlet_id\", \"item_id\"]\n", "].drop_duplicates()\n", "\n", "be_inv_df = pd.merge(be_inv_df, check_df, on=[\"be_inv_outlet_id\", \"item_id\"], how=\"inner\")\n", "\n", "be_inv_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "af7ffb27-ce26-41d3-b311-2f7ed6903f9e", "metadata": {}, "outputs": [], "source": ["be_inv_df[be_inv_df[\"be_inv_outlet_id\"] == 4287].sort_values(\"be_inv\")"]}, {"cell_type": "code", "execution_count": null, "id": "c739974a-b633-4b8c-996c-4d1b1e8f3396", "metadata": {}, "outputs": [], "source": ["disruption_base_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9c4bfe19-551a-4193-88ef-3b791ffeb845", "metadata": {}, "outputs": [], "source": ["def percentile(x):\n", "    return np.percentile(x, 80)"]}, {"cell_type": "markdown", "id": "7707664e-454b-4eda-8776-df320d20d082", "metadata": {}, "source": ["## Slot A Transfer Query"]}, {"cell_type": "code", "execution_count": null, "id": "d386ed50-d3db-4393-913e-67f2f01e0064", "metadata": {}, "outputs": [], "source": ["slot_a_df = (\n", "    disruption_base_df[\n", "        [\n", "            \"city\",\n", "            \"be_facility_id\",\n", "            \"be_inv_outlet_id\",\n", "            \"facility_id\",\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"is_slotb\",\n", "            \"date_\",\n", "            \"dow\",\n", "            \"tdate\",\n", "            \"tdow\",\n", "            \"early_mor_sales_qty\",\n", "            \"day_sales_qty\",\n", "            \"eve_sales_qty\",\n", "            \"day_wt_avail\",\n", "            \"eve_wt_avail\",\n", "            \"night_sales_qty\",\n", "            \"lin_extp\",\n", "            \"exp_extp\",\n", "            \"para_extp\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "slot_a_df = pd.merge(\n", "    slot_a_df,\n", "    final_increment_df_2[[\"city\", \"facility_id\", \"dow\", \"tdow\", \"embf\", \"dbf\", \"ebf\", \"nbf\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"}),\n", "    on=[\"city\", \"facility_id\", \"dow\", \"tdow\"],\n", "    how=\"left\",\n", ")\n", "\n", "slot_a_df = slot_a_df.merge(\n", "    forecast_df,\n", "    on=[\"tdate\", \"city\", \"facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "slot_a_df[[\"embf\", \"dbf\", \"ebf\", \"nbf\"]] = slot_a_df[[\"embf\", \"dbf\", \"ebf\", \"nbf\"]].fillna(1)\n", "slot_a_df[[\"embf\", \"dbf\", \"ebf\", \"nbf\"]] = slot_a_df[[\"embf\", \"dbf\", \"ebf\", \"nbf\"]].replace(\n", "    np.inf, 1\n", ")\n", "# slot_a_df[[\"embf\", \"dbf\", \"ebf\", \"nbf\"]] = np.where(slot_a_df[\"facility_id\"].isin(new_store_facility_list),\n", "#                                                                 slot_a_df[[\"embf\", \"dbf\", \"ebf\", \"nbf\"]].clip(0.8,1.2, axis = 1),\n", "#                                                                     slot_a_df[[\"embf\", \"dbf\", \"ebf\", \"nbf\"]].clip(0.5,1.5, axis = 1)\n", "#                                                             )\n", "\n", "# slot_a_df[~slot_a_df[\"facility_id\"].isin(medium_age_facility_list)][[\"embf\", \"dbf\", \"ebf\", \"nbf\"]] = slot_a_df[~slot_a_df[\"facility_id\"].isin(medium_age_facility_list)][[\"embf\", \"dbf\", \"ebf\", \"nbf\"]].clip(0.5,1.5)\n", "# slot_a_df[slot_a_df[\"facility_id\"].isin(medium_age_facility_list)][[\"embf\", \"dbf\", \"ebf\", \"nbf\"]] = slot_a_df[slot_a_df[\"facility_id\"].isin(medium_age_facility_list)][[\"embf\", \"dbf\", \"ebf\", \"nbf\"]].clip(0.8,1.2)\n", "\n", "medium_age_df = slot_a_df[slot_a_df[\"facility_id\"].isin(medium_age_facility_list)]\n", "normal_age_df = slot_a_df[~slot_a_df[\"facility_id\"].isin(medium_age_facility_list)]\n", "\n", "mask = medium_age_df[\"facility_id\"].isin(sis_fac_list)\n", "# Create a bounded version of the 4 columns\n", "bounded = medium_age_df[[\"embf\", \"dbf\", \"ebf\", \"nbf\"]].clip(lower=0.8, upper=1.2)\n", "# Where the facility_id is not in sis_fac_list, apply the bounds\n", "medium_age_df[[\"embf\", \"dbf\", \"ebf\", \"nbf\"]] = medium_age_df[[\"embf\", \"dbf\", \"ebf\", \"nbf\"]].where(\n", "    mask, bounded\n", ")\n", "## final cliping between 0.5 to 1.5\n", "medium_age_df[[\"embf\", \"dbf\", \"ebf\", \"nbf\"]] = medium_age_df[[\"embf\", \"dbf\", \"ebf\", \"nbf\"]].clip(\n", "    0.5, 1.5\n", ")\n", "\n", "normal_age_df[[\"embf\", \"dbf\", \"ebf\", \"nbf\"]] = normal_age_df[[\"embf\", \"dbf\", \"ebf\", \"nbf\"]].clip(\n", "    0.5, 1.5\n", ")\n", "# normal_age_df[[\"embf\", \"dbf\", \"ebf\", \"nbf\"]] = np.where(\n", "#     normal_age_df[[\"embf\", \"dbf\", \"ebf\", \"nbf\"]] < 0.5,\n", "#     0.5,\n", "#     np.where(\n", "#         normal_age_df[[\"embf\", \"dbf\", \"ebf\", \"nbf\"]] > 1.5,\n", "#         1.5,\n", "#         normal_age_df[[\"embf\", \"dbf\", \"ebf\", \"nbf\"]],\n", "#     ),\n", "# )\n", "slot_a_df = pd.concat([medium_age_df, normal_age_df]).drop_duplicates()\n", "\n", "slot_a_df[\n", "    [\n", "        \"eve_wt_avail\",\n", "        \"day_wt_avail\",\n", "    ]\n", "] = slot_a_df[\n", "    [\"eve_wt_avail\", \"day_wt_avail\"]\n", "].astype(float)\n", "\n", "\n", "# slot_a_df[\"ext_qty_li\"] = np.ceil(\n", "#     slot_a_df[\"day_sales_qty\"] * (1 + 0.35 * (1 - slot_a_df[\"day_wt_avail\"]))\n", "# )\n", "\n", "slot_a_df[\"ext_qty_li\"] = np.ceil(slot_a_df[\"lin_extp\"])\n", "\n", "\n", "# slot_a_df[\"ext_qty_exp\"] = np.ceil(\n", "#     slot_a_df[\"day_sales_qty\"] * (5 ** (0.50 * (1 - slot_a_df[\"day_wt_avail\"])))\n", "# )\n", "\n", "slot_a_df[\"ext_qty_exp\"] = np.ceil(slot_a_df[\"exp_extp\"])\n", "\n", "\n", "# slot_a_df[\"ext_qty_para\"] = np.ceil(\n", "#     slot_a_df[\"day_sales_qty\"] * (1 + ((1 - slot_a_df[\"day_wt_avail\"]) ** 2) / (4 * 0.08))\n", "# )\n", "\n", "slot_a_df[\"ext_qty_para\"] = np.ceil(slot_a_df[\"para_extp\"])\n", "\n", "slot_a_df[\"eve_ext_qty\"] = np.ceil(\n", "    slot_a_df[\"eve_sales_qty\"] * (1 + 0.35 * (1 - slot_a_df[\"eve_wt_avail\"]))\n", ")\n", "\n", "slot_a_df[\"lower_bound\"] = np.where(\n", "    ~slot_a_df[\"facility_id\"].isin(l21_facility_list),\n", "    slot_a_df[[\"ext_qty_li\", \"ext_qty_para\", \"ext_qty_exp\"]].mean(axis=1),\n", "    slot_a_df[\"ext_qty_exp\"],\n", ")\n", "\n", "slot_a_df[\"upper_bound\"] = np.where(\n", "    slot_a_df[\"facility_id\"].isin(l21_facility_list),\n", "    slot_a_df[[\"ext_qty_para\", \"ext_qty_exp\"]].mean(axis=1),\n", "    np.where(\n", "        slot_a_df[\"ftype_\"] == \"high\",\n", "        slot_a_df[[\"ext_qty_exp\", \"ext_qty_para\"]].mean(axis=1),\n", "        np.where(\n", "            (slot_a_df[\"ftype_\"] == \"medium\") & (slot_a_df[\"stype_\"] == \"top\"),\n", "            slot_a_df[[\"ext_qty_exp\", \"ext_qty_para\"]].mean(axis=1),\n", "            np.where(\n", "                (slot_a_df[\"ftype_\"] == \"low\") & (slot_a_df[\"stype_\"] == \"top\"),\n", "                slot_a_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].mean(axis=1),\n", "                slot_a_df[[\"ext_qty_li\", \"ext_qty_exp\"]].mean(axis=1),\n", "            ),\n", "        ),\n", "    ),\n", ")\n", "\n", "slot_a_df[\"norm_early_mor_sales\"] = slot_a_df[\"early_mor_sales_qty\"] * slot_a_df[\"embf\"]\n", "slot_a_df[\"lower_bound\"] = slot_a_df[\"lower_bound\"] * slot_a_df[\"dbf\"]\n", "slot_a_df[\"upper_bound\"] = slot_a_df[\"upper_bound\"] * slot_a_df[\"dbf\"]\n", "slot_a_df[\"norm_eve_sales\"] = slot_a_df[\"eve_sales_qty\"] * slot_a_df[\"ebf\"]\n", "slot_a_df[\"norm_night_sales\"] = slot_a_df[\"night_sales_qty\"] * slot_a_df[\"nbf\"]\n", "slot_a_df[\"stype_\"] = slot_a_df[\"stype_\"].fillna(\"top\")\n", "slot_a_df[\"ftype_\"] = slot_a_df[\"ftype_\"].fillna(\"medium\")\n", "slot_a_df[[\"slota_transfer_qty\", \"tot_transfer\"]] = slot_a_df[\n", "    [\"slota_transfer_qty\", \"tot_transfer\"]\n", "].fillna(0)\n", "print(slot_a_df.shape)\n", "final_slot_a_df = (\n", "    slot_a_df.groupby(\n", "        [\n", "            \"city\",\n", "            \"be_facility_id\",\n", "            \"be_inv_outlet_id\",\n", "            \"facility_id\",\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"is_slotb\",\n", "            \"tdate\",\n", "            \"stype_\",\n", "            \"ftype_\",\n", "            \"slota_transfer_qty\",\n", "            \"tot_transfer\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"lower_bound\": percentile,\n", "            \"upper_bound\": percentile,\n", "            \"eve_ext_qty\": percentile,\n", "            \"norm_night_sales\": percentile,\n", "            \"norm_early_mor_sales\": percentile,\n", "            \"day_wt_avail\": \"mean\",\n", "            \"eve_wt_avail\": \"mean\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "\n", "final_slot_a_df[\"slot_a_lower_lim\"] = np.where(\n", "    (final_slot_a_df[\"is_slotb\"] == 1) & (final_slot_a_df[\"ftype_\"] == \"low\"),\n", "    final_slot_a_df[\"lower_bound\"] * 0.85,\n", "    np.where(\n", "        (final_slot_a_df[\"is_slotb\"] == 1) & (final_slot_a_df[\"ftype_\"] == \"medium\"),\n", "        final_slot_a_df[\"lower_bound\"] * 0.9,\n", "        final_slot_a_df[\"lower_bound\"] * 0.8,\n", "    ),\n", ")\n", "\n", "final_slot_a_df[\"slot_a_upper_lim\"] = np.where(\n", "    (final_slot_a_df[\"is_slotb\"] == 1) & (final_slot_a_df[\"ftype_\"] == \"low\"),\n", "    final_slot_a_df[\"upper_bound\"] * 0.85,\n", "    np.where(\n", "        (final_slot_a_df[\"is_slotb\"] == 1) & (final_slot_a_df[\"ftype_\"] == \"medium\"),\n", "        final_slot_a_df[\"upper_bound\"] * 0.9,\n", "        final_slot_a_df[\"upper_bound\"] * 0.8,\n", "    ),\n", ")\n", "## applying lower limit\n", "final_slot_a_df[\"final_slot_a_qty\"] = np.where(\n", "    final_slot_a_df[\"slota_transfer_qty\"] < final_slot_a_df[\"slot_a_lower_lim\"],\n", "    final_slot_a_df[\"slot_a_lower_lim\"],\n", "    final_slot_a_df[\"slota_transfer_qty\"],\n", ")\n", "\n", "## applying upper limit\n", "final_slot_a_df[\"final_slot_a_qty\"] = np.where(\n", "    final_slot_a_df[\"final_slot_a_qty\"] > final_slot_a_df[\"slot_a_upper_lim\"],\n", "    final_slot_a_df[\"slot_a_upper_lim\"],\n", "    final_slot_a_df[\"final_slot_a_qty\"],\n", ")\n", "\n", "## taking max of indent_correction numbers for store_age <=5 and upper limit for age between (6,15)\n", "final_slot_a_df[\"final_slot_a_qty\"] = np.where(\n", "    final_slot_a_df[\"facility_id\"].isin(new_store_facility_list),\n", "    final_slot_a_df[[\"slota_transfer_qty\", \"slot_a_upper_lim\"]].max(axis=1),\n", "    np.where(\n", "        final_slot_a_df[\"facility_id\"].isin(l21_facility_list),\n", "        final_slot_a_df[[\"final_slot_a_qty\", \"slot_a_upper_lim\"]].max(axis=1),\n", "        final_slot_a_df[\"final_slot_a_qty\"],\n", "    ),\n", ")\n", "\n", "## taking full day numbers of indent correction where slot B = 0\n", "final_slot_a_df[\"final_slot_a_qty\"] = np.where(\n", "    final_slot_a_df[\"is_slotb\"] == 0,\n", "    final_slot_a_df[\"tot_transfer\"],\n", "    final_slot_a_df[\"final_slot_a_qty\"],\n", ")\n", "\n", "final_slot_a_df[\"eve_ext_qty\"] = np.round(final_slot_a_df[\"eve_ext_qty\"], 0)\n", "\n", "final_slot_a_df[\"norm_night_sales\"] = np.round(final_slot_a_df[\"norm_night_sales\"], 0)\n", "\n", "final_slot_a_df[\"final_slot_a_qty\"] = np.round(final_slot_a_df[\"final_slot_a_qty\"], 0)\n", "\n", "final_slot_a_df[\"norm_early_mor_sales\"] = np.round(final_slot_a_df[\"norm_early_mor_sales\"], 0)\n", "\n", "final_slot_a_df = pd.merge(final_slot_a_df, fe_inv_df, on=[\"outlet_id\", \"item_id\"], how=\"left\")\n", "final_slot_a_df[\"current_inv\"] = final_slot_a_df[\"current_inv\"].fillna(0)\n", "\n", "final_slot_a_df[\"evening_potential_sales\"] = (\n", "    final_slot_a_df[\"eve_ext_qty\"] + final_slot_a_df[\"norm_night_sales\"]\n", ")\n", "\n", "\n", "final_slot_a_df[\"potential_lo\"] = (\n", "    final_slot_a_df[\"current_inv\"] - final_slot_a_df[\"evening_potential_sales\"]\n", ")\n", "final_slot_a_df[\"potential_lo\"] = np.where(\n", "    final_slot_a_df[\"potential_lo\"] < 0, 0, final_slot_a_df[\"potential_lo\"]\n", ")\n", "\n", "final_slot_a_df[\"2nd_lo\"] = (\n", "    final_slot_a_df[\"potential_lo\"] - final_slot_a_df[\"norm_early_mor_sales\"]\n", ")\n", "\n", "final_slot_a_df[\"new_final_slot_a_qty\"] = np.where(\n", "    final_slot_a_df[\"2nd_lo\"] < 0,\n", "    final_slot_a_df[\"final_slot_a_qty\"],\n", "    final_slot_a_df[\"final_slot_a_qty\"] - final_slot_a_df[\"norm_early_mor_sales\"],\n", ")\n", "# final_slot_a_df[\"new_final_slot_a_qty\"] = np.where(\n", "#     final_slot_a_df[\"is_slotb\"] == 0,\n", "#     final_slot_a_df[\"tot_transfer\"],\n", "#     final_slot_a_df[\"new_final_slot_a_qty\"],\n", "# )\n", "\n", "final_slot_a_df[\"new_final_slot_a_qty\"] = np.where(\n", "    (final_slot_a_df[\"new_final_slot_a_qty\"] == 0) & (final_slot_a_df[\"tot_transfer\"] > 0),\n", "    final_slot_a_df[\"tot_transfer\"],\n", "    final_slot_a_df[\"new_final_slot_a_qty\"],\n", ")\n", "print(\"run_success...\")\n", "final_slot_a_df.shape"]}, {"cell_type": "markdown", "id": "50f83670-cb6f-43c4-bf96-1aa205bbf151", "metadata": {}, "source": ["### 1 Liter addition for transistion phase\n"]}, {"cell_type": "code", "execution_count": null, "id": "09af5d7f-7e89-4e7f-ab86-1738e42e15a4", "metadata": {}, "outputs": [], "source": ["pre_agg = (\n", "    final_slot_a_df.groupby([\"be_facility_id\", \"item_id\", \"tdate\"])\n", "    .agg({\"new_final_slot_a_qty\": \"sum\"})\n", "    .rename(columns={\"new_final_slot_a_qty\": \"fin_total\"})\n", "    .reset_index()\n", ")\n", "\n", "transfers_df = final_slot_a_df.merge(pre_agg, on=[\"be_facility_id\", \"item_id\", \"tdate\"], how=\"left\")\n", "transfers_df[\"1_L_factor\"] = (\n", "    transfers_df[\"new_final_slot_a_qty\"] * 1.000 / transfers_df[\"fin_total\"]\n", ")\n", "transfers_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2e33d76d-d091-4761-a7b1-fd6c8f0f2bca", "metadata": {}, "outputs": [], "source": ["fact = transfers_df.merge(\n", "    one_l,\n", "    right_on=[\"be_facility_id\", \"500ml_item_id\"],\n", "    left_on=[\"be_facility_id\", \"item_id\"],\n", "    how=\"inner\",\n", ")\n", "fact = fact[(fact[\"tdate\"] >= fact[\"start_date\"]) & (fact[\"tdate\"] <= fact[\"end_date\"])]\n", "fact = (\n", "    fact[[\"facility_id\", \"1L_item_id\", \"1_L_factor\"]]\n", "    .drop_duplicates()\n", "    .rename(columns={\"1L_item_id\": \"item_id\", \"1_L_factor\": \"1_L_factor_2\"})\n", ")\n", "fact[fact[\"facility_id\"] == 727]"]}, {"cell_type": "code", "execution_count": null, "id": "7eb8ac6a-538c-49b5-b2e8-435e5dcf7248", "metadata": {}, "outputs": [], "source": ["new_f = transfers_df.merge(fact, on=[\"facility_id\", \"item_id\"], how=\"left\").fillna(0)\n", "new_f[\"factor\"] = np.where(\n", "    new_f[\"item_id\"].isin(one_l_item_id), new_f[\"1_L_factor_2\"], new_f[\"1_L_factor\"]\n", ")\n", "new_f.drop(columns={\"1_L_factor_2\", \"1_L_factor_2\"}, inplace=True)\n", "new_f[new_f[\"facility_id\"] == 228]"]}, {"cell_type": "code", "execution_count": null, "id": "65d26732-fa86-433a-ac89-a0829aa815f4", "metadata": {}, "outputs": [], "source": ["transfers_df = new_f.copy()\n", "transfers_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d7681fab-4753-45dd-8780-32f124051b09", "metadata": {}, "outputs": [], "source": ["total_conversions = one_l.merge(\n", "    pre_agg,\n", "    left_on=[\"be_facility_id\", \"500ml_item_id\"],\n", "    right_on=[\"be_facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "total_conversions = (\n", "    total_conversions[\n", "        (total_conversions[\"tdate\"] >= total_conversions[\"start_date\"])\n", "        & (total_conversions[\"tdate\"] <= total_conversions[\"end_date\"])\n", "    ]\n", "    .rename(columns={\"fin_total\": \"500ml_qty\"})\n", "    .drop(columns={\"item_id\"})\n", ")\n", "total_conversions = (\n", "    total_conversions.groupby(\n", "        [\"be_facility_id\", \"500ml_item_id\", \"1L_item_id\", \"tdate\", \"500ml_qty\"]\n", "    )\n", "    .agg({\"conversion_factor\": \"max\"})\n", "    .reset_index()\n", ")\n", "total_conversions = (\n", "    total_conversions.merge(\n", "        pre_agg,\n", "        left_on=[\"be_facility_id\", \"1L_item_id\", \"tdate\"],\n", "        right_on=[\"be_facility_id\", \"item_id\", \"tdate\"],\n", "        how=\"left\",\n", "    )\n", "    .rename(columns={\"fin_total\": \"1l_qty\"})\n", "    .drop(columns={\"item_id\"})\n", ")\n", "total_conversions[\"1l_qty\"] = total_conversions[\"1l_qty\"].fillna(0)\n", "total_conversions[\"final_500_qty\"] = np.ceil(\n", "    (total_conversions[\"1l_qty\"] * 2 + total_conversions[\"500ml_qty\"])\n", "    * (1 - total_conversions[\"conversion_factor\"])\n", ")\n", "total_conversions[\"final_1L_qty\"] = np.ceil(\n", "    (total_conversions[\"1l_qty\"] * 2 + total_conversions[\"500ml_qty\"])\n", "    * (total_conversions[\"conversion_factor\"])\n", "    / 2\n", ")\n", "\n", "one_l_df = total_conversions[[\"be_facility_id\", \"1L_item_id\", \"final_1L_qty\"]].rename(\n", "    columns={\"1L_item_id\": \"item_id\"}\n", ")\n", "half_l_df = total_conversions[[\"be_facility_id\", \"500ml_item_id\", \"final_500_qty\"]].rename(\n", "    columns={\"500ml_item_id\": \"item_id\"}\n", ")\n", "\n", "total_conversions.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4a39689a-aacf-4d14-96f7-a843d1648a43", "metadata": {}, "outputs": [], "source": ["new_transfer_df = transfers_df.merge(one_l_df, on=[\"be_facility_id\", \"item_id\"], how=\"left\")\n", "new_transfer_df = new_transfer_df.merge(half_l_df, on=[\"be_facility_id\", \"item_id\"], how=\"left\")\n", "new_transfer_df[[\"final_1L_qty\", \"final_500_qty\"]] = new_transfer_df[\n", "    [\"final_1L_qty\", \"final_500_qty\"]\n", "].fillna(0)\n", "\n", "new_transfer_df[\"new_final_slot_a_qty\"] = np.where(\n", "    (new_transfer_df[\"final_500_qty\"] + new_transfer_df[\"final_1L_qty\"]) > 0,\n", "    (new_transfer_df[\"final_500_qty\"] + new_transfer_df[\"final_1L_qty\"])\n", "    * new_transfer_df[\"factor\"],\n", "    new_transfer_df[\"new_final_slot_a_qty\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "36614878-c6ab-42a0-81e4-1291c7cb2840", "metadata": {}, "outputs": [], "source": ["final_slot_a_df = new_transfer_df.drop(columns={\"1_L_factor\", \"factor\"})\n", "final_slot_a_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b93d7d1e-a6f9-493c-a8e7-0ae17f837a3b", "metadata": {}, "outputs": [], "source": ["new_transfer_df[new_transfer_df.facility_id == 424]"]}, {"cell_type": "markdown", "id": "6383e532-c647-4d2b-a1a7-dbcd9ac0976d", "metadata": {}, "source": ["### Bumps"]}, {"cell_type": "code", "execution_count": null, "id": "be445a4f-20b3-408a-9ac9-43f3179a934f", "metadata": {}, "outputs": [], "source": ["bump_query = f\"\"\"with base as ( \n", "        select \n", "        date_ist,\n", "        updated_at,\n", "        city,\n", "        be_facility_id,\n", "        facility_id,\n", "        item_id ,\n", "        be_f,\n", "        city_f,\n", "        store_f,\n", "        event,\n", "        rank() over (partition by date_ist order by updated_at desc) as rk\n", "        from \n", "        supply_etls.milk_ordering_bump_factors\n", "        where date_ist>= date'{transfer_date}' - interval '3' day\n", "\n", "    )\n", "    \n", "        select\n", "        date_ist + interval '2' day as tdate,\n", "        be_facility_id,\n", "        item_id ,\n", "        facility_id,\n", "        be_f,\n", "        city_f,\n", "        store_f,\n", "        event\n", "        from base\n", "        where rk = 1\n", "        \"\"\"\n", "\n", "bump_df = read_sql_query(bump_query, trino)\n", "bump_df[\"tdate\"] = pd.to_datetime(bump_df[\"tdate\"])\n", "bump_df = bump_df[bump_df[\"event\"] != \"BAU\"]\n", "bump_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "26a20517-099f-40f2-82bb-e638e14f6ebb", "metadata": {}, "outputs": [], "source": ["bump_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bc11c42e-d8fd-4cc0-ae6d-2dc86c4d3cbd", "metadata": {}, "outputs": [], "source": ["final_slot_a_df = final_slot_a_df.merge(\n", "    bump_df[[\"tdate\", \"item_id\", \"facility_id\", \"be_f\", \"city_f\", \"store_f\"]],\n", "    on=[\"item_id\", \"facility_id\", \"tdate\"],\n", "    how=\"left\",\n", ")\n", "final_slot_a_df[[\"be_f\", \"city_f\", \"store_f\"]] = final_slot_a_df[\n", "    [\"be_f\", \"city_f\", \"store_f\"]\n", "].fillna(1)\n", "final_slot_a_df[\"bump_fac\"] = np.where(\n", "    final_slot_a_df[\"store_f\"] != 1,\n", "    final_slot_a_df[\"store_f\"],\n", "    np.where(\n", "        final_slot_a_df[\"city_f\"] != 1,\n", "        final_slot_a_df[\"city_f\"],\n", "        np.where(final_slot_a_df[\"be_f\"] != 1, final_slot_a_df[\"be_f\"], 1),\n", "    ),\n", ")\n", "\n", "# final_slot_a_df['new_final_slot_a_qty'] = final_slot_a_df['new_final_slot_a_qty']*final_slot_a_df['bump_fac']\n", "final_slot_a_df.drop(columns={\"be_f\", \"city_f\", \"store_f\"}, inplace=True)\n", "final_slot_a_df.head()"]}, {"cell_type": "markdown", "id": "64480836-e14f-4d0d-9a92-dd856f847558", "metadata": {}, "source": ["### Store level inputs"]}, {"cell_type": "code", "execution_count": null, "id": "142c2b2d-e916-426f-9429-01553f9a597b", "metadata": {}, "outputs": [], "source": ["try:\n", "    store_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::store-transfer-bumps\",\n", "    )\n", "\n", "except:\n", "    store_df = pd.read_csv(\"store_input.csv\")\n", "\n", "store_df = ignore_wrong_manual_input(store_df)\n", "store_df = store_df[store_df[\"slot\"] != \"B\"].drop(columns={\"slot\"})\n", "store_df[\"facility_id\"] = store_df[\"facility_id\"].fillna(0).astype(int)\n", "store_df[\"start_date\"] = pd.to_datetime(store_df[\"start_date\"])\n", "store_df[\"end_date\"] = pd.to_datetime(store_df[\"end_date\"])\n", "\n", "store_df[\"factor\"] = store_df[\"factor\"].astype(float)\n", "\n", "store_df[\"tdate\"] = list(\n", "    map(\n", "        lambda x, y: pd.date_range(start=x, end=y),\n", "        store_df[\"start_date\"],\n", "        store_df[\"end_date\"],\n", "    )\n", ")\n", "\n", "store_df = store_df.explode(\"tdate\").drop([\"start_date\", \"end_date\"], axis=1)\n", "\n", "store_df[\"tdate\"] = pd.to_datetime(store_df[\"tdate\"])\n", "\n", "store_df = store_df.reset_index()\n", "\n", "store_max = store_df.groupby([\"facility_id\", \"tdate\"]).agg({\"index\": \"max\"}).reset_index()\n", "\n", "store_df = store_df.merge(store_max, on=[\"facility_id\", \"tdate\", \"index\"], how=\"inner\").drop(\n", "    columns={\"index\"}\n", ")\n", "\n", "store_df = store_df[store_df[\"tdate\"] >= today_date].reset_index().drop(columns={\"index\"})\n", "\n", "store_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "7bf5ea69-caf7-43f6-b27f-e49e71e0f2d8", "metadata": {}, "outputs": [], "source": ["final_slot_a_df = final_slot_a_df.merge(store_df, on=[\"facility_id\", \"tdate\"], how=\"left\")\n", "\n", "final_slot_a_df[\"factor\"] = final_slot_a_df[\"factor\"].fillna(1)\n", "final_slot_a_df.rename(columns={\"factor\": \"store_f\"}, inplace=True)\n", "final_slot_a_df.head(1)"]}, {"cell_type": "markdown", "id": "41eaeffa-0ead-4407-b60b-b4c6ed793395", "metadata": {}, "source": ["### City level inputs"]}, {"cell_type": "code", "execution_count": null, "id": "a3b10742-3791-4747-9b6e-bcbfcc6bccd7", "metadata": {}, "outputs": [], "source": ["try:\n", "    city_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::city-transfer-bumps\",\n", "    ).rename(columns={\"factor\": \"city_f\"})\n", "\n", "except:\n", "    city_df = pd.read_csv(\"city_input.csv\").rename(columns={\"factor\": \"city_f\"})\n", "city_df = ignore_wrong_manual_input(city_df)\n", "city_df = city_df[city_df[\"slot\"] != \"B\"].drop(columns={\"slot\"})\n", "city_df = city_df[city_df[\"city_f\"] != \"\"]\n", "city_df[\"start_date\"] = pd.to_datetime(city_df[\"start_date\"])\n", "city_df[\"end_date\"] = pd.to_datetime(city_df[\"end_date\"])\n", "\n", "city_df[\"city_f\"] = city_df[\"city_f\"].astype(float)\n", "\n", "# sku_mapping_df = pd.DataFrame(\n", "#     {\"sku_type\": [\"all\", \"all\", \"all\"], \"stype_\": [\"top\", \"bottom\", \"premium\"]}\n", "# )\n", "\n", "# city_df = city_df.merge(sku_mapping_df, on=[\"sku_type\"], how=\"left\")\n", "\n", "# city_df[\"stype_\"] = np.where(\n", "#     city_df[\"stype_\"].isna(), city_df[\"sku_type\"], city_df[\"stype_\"]\n", "# )\n", "\n", "# city_df = city_df.drop(columns={\"sku_type\"})\n", "city_df[\"tdate\"] = list(\n", "    map(\n", "        lambda x, y: pd.date_range(start=x, end=y),\n", "        city_df[\"start_date\"],\n", "        city_df[\"end_date\"],\n", "    )\n", ")\n", "\n", "city_df = city_df.explode(\"tdate\").drop([\"start_date\", \"end_date\"], axis=1)\n", "\n", "city_df[\"tdate\"] = pd.to_datetime(city_df[\"tdate\"])\n", "\n", "city_df = city_df.reset_index()\n", "\n", "city_df_max = city_df.groupby([\"city\", \"tdate\"]).agg({\"index\": \"max\"}).reset_index()\n", "\n", "city_df = city_df.merge(city_df_max, on=[\"city\", \"tdate\", \"index\"], how=\"inner\").drop(\n", "    columns={\"index\"}\n", ")\n", "\n", "city_df = city_df[city_df[\"tdate\"] >= today_date].reset_index().drop(columns={\"index\"})\n", "city_df.drop(columns={\"sku_type\"}, inplace=True)\n", "city_df = city_df.drop_duplicates()\n", "city_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "96611930-41a0-4db0-b58a-d6e34f067576", "metadata": {}, "outputs": [], "source": ["final_slot_a_df = (\n", "    final_slot_a_df.merge(city_df, on=[\"city\", \"tdate\"], how=\"left\")\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "final_slot_a_df[\"city_f\"] = final_slot_a_df[\"city_f\"].fillna(1)\n", "\n", "\n", "# final_slot_a_df.drop(columns={\"city_f\"}, inplace=True)\n", "final_slot_a_df.head(1)"]}, {"cell_type": "markdown", "id": "8fd1748b-1271-438c-89df-7f1f52bdfce2", "metadata": {}, "source": ["## Backend level factor"]}, {"cell_type": "code", "execution_count": null, "id": "33b17bba-6ca8-44e4-80cf-3c758bc6ea1e", "metadata": {"tags": []}, "outputs": [], "source": ["try:\n", "    store_item_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::backend-item-transfer-bumps\",\n", "    ).rename(columns={\"factor\": \"si_factor\"})\n", "\n", "except:\n", "    store_item_df = pd.read_csv(\"be_input.csv\").rename(columns={\"factor\": \"si_factor\"})\n", "store_item_df = ignore_wrong_manual_input(store_item_df)\n", "store_item_df = store_item_df[store_item_df[\"slot\"] != \"B\"].drop(columns={\"slot\"})\n", "store_item_df = store_item_df[store_item_df[\"si_factor\"] != \"\"]\n", "store_item_df[\"be_facility_id\"] = store_item_df[\"be_facility_id\"].fillna(0).astype(int)\n", "store_item_df[\"item_id\"] = store_item_df[\"item_id\"].fillna(0).astype(int)\n", "\n", "store_item_df[\"start_date\"] = pd.to_datetime(store_item_df[\"start_date\"])\n", "store_item_df[\"end_date\"] = pd.to_datetime(store_item_df[\"end_date\"])\n", "\n", "store_item_df[\"si_factor\"] = store_item_df[\"si_factor\"].astype(float)\n", "\n", "store_item_df[\"tdate\"] = list(\n", "    map(\n", "        lambda x, y: pd.date_range(start=x, end=y),\n", "        store_item_df[\"start_date\"],\n", "        store_item_df[\"end_date\"],\n", "    )\n", ")\n", "\n", "store_item_df = store_item_df.explode(\"tdate\").drop([\"start_date\", \"end_date\"], axis=1)\n", "\n", "store_item_df[\"tdate\"] = pd.to_datetime(store_item_df[\"tdate\"])\n", "\n", "store_item_df = store_item_df.reset_index()\n", "\n", "store_item_max = (\n", "    store_item_df.groupby([\"be_facility_id\", \"item_id\", \"tdate\"])\n", "    .agg({\"index\": \"max\"})\n", "    .reset_index()\n", ")\n", "\n", "store_item_df = store_item_df.merge(\n", "    store_item_max, on=[\"be_facility_id\", \"item_id\", \"tdate\", \"index\"], how=\"inner\"\n", ").drop(columns={\"index\"})\n", "\n", "store_item_df = (\n", "    store_item_df[store_item_df[\"tdate\"] >= transfer_date].reset_index().drop(columns={\"index\"})\n", ")\n", "\n", "store_item_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "387f4715-0263-4e89-9ec3-865e5bbfb693", "metadata": {"tags": []}, "outputs": [], "source": ["final_slot_a_df = (\n", "    final_slot_a_df.merge(store_item_df, on=[\"be_facility_id\", \"item_id\", \"tdate\"], how=\"left\")\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "final_slot_a_df[\"si_factor\"] = final_slot_a_df[\"si_factor\"].fillna(1)\n", "\n", "# final_slot_a_df.drop(columns={\"si_factor\"}, inplace=True)\n", "final_slot_a_df.rename(columns={\"si_factor\": \"be_f\"}, inplace=True)\n", "final_slot_a_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "da5ba0ad-2931-4d53-8f5b-5bcc4449b2b2", "metadata": {"tags": []}, "outputs": [], "source": ["final_slot_a_df[\"new_bump_fac\"] = np.where(\n", "    final_slot_a_df[\"store_f\"] != 1,\n", "    final_slot_a_df[\"store_f\"],\n", "    np.where(\n", "        final_slot_a_df[\"city_f\"] != 1,\n", "        final_slot_a_df[\"city_f\"],\n", "        np.where(final_slot_a_df[\"be_f\"] != 1, final_slot_a_df[\"be_f\"], 1),\n", "    ),\n", ")\n", "final_slot_a_df[\"final_bump\"] = np.where(\n", "    final_slot_a_df[\"new_bump_fac\"] != 1,\n", "    final_slot_a_df[\"new_bump_fac\"],\n", "    final_slot_a_df[\"bump_fac\"],\n", ")\n", "final_slot_a_df[\"new_final_slot_a_qty\"] = (\n", "    np.ceil(final_slot_a_df[\"new_final_slot_a_qty\"] * final_slot_a_df[\"final_bump\"])\n", "    .fillna(0)\n", "    .astype(int)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "aa79b842-208d-4fd3-be8a-372d6982887d", "metadata": {"tags": []}, "outputs": [], "source": ["final_slot_a_df.head()"]}, {"cell_type": "markdown", "id": "044e7402-2811-4efc-b13b-23c3b678de29", "metadata": {}, "source": ["## Manual CPD Overwrite"]}, {"cell_type": "code", "execution_count": null, "id": "709fd392-9f70-4e4c-9206-1f7e3e61e2da", "metadata": {"tags": []}, "outputs": [], "source": ["try:\n", "    manual_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::manual-forecast-input\",\n", "    )\n", "\n", "except:\n", "    manual_df = pd.read_csv(\"manual_input.csv\")\n", "manual_df = ignore_wrong_manual_input(manual_df)\n", "manual_df = manual_df[manual_df[\"slot\"] != \"B\"].drop(columns={\"slot\"})\n", "manual_df[\"facility_id\"] = manual_df[\"facility_id\"].fillna(0).astype(int)\n", "manual_df[\"item_id\"] = manual_df[\"item_id\"].fillna(0).astype(int)\n", "manual_df[\"manual_cpd\"] = manual_df[\"manual_cpd\"].fillna(0).astype(int)\n", "\n", "manual_df[\"start_date\"] = pd.to_datetime(manual_df[\"start_date\"])\n", "manual_df[\"end_date\"] = pd.to_datetime(manual_df[\"end_date\"])\n", "\n", "manual_df = manual_df[\n", "    (manual_df[\"start_date\"] <= transfer_date) & (manual_df[\"end_date\"] >= transfer_date)\n", "]\n", "manual_df = (\n", "    manual_df.groupby([\"attribute\", \"facility_id\", \"item_id\"])\n", "    .agg({\"manual_cpd\": \"max\"})\n", "    .reset_index()\n", ")\n", "\n", "be_manual = (\n", "    manual_df[manual_df.attribute == \"backend\"]\n", "    .copy()\n", "    .rename(columns={\"facility_id\": \"be_facility_id\", \"manual_cpd\": \"be_manual_cpd\"})\n", ")\n", "fe_manual = manual_df[manual_df.attribute == \"frontend\"].copy()\n", "be_manual.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "f1f8ae97-d6ad-4b33-85a0-8b95812e608a", "metadata": {}, "outputs": [], "source": ["fe_manual.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "764bb8ad-0b5a-4de2-9cf9-582679f64da1", "metadata": {}, "outputs": [], "source": ["## applying backend item level manual cpd\n", "\n", "final_slot_a_df = final_slot_a_df.merge(be_manual, on=[\"be_facility_id\", \"item_id\"], how=\"left\")\n", "\n", "final_slot_a_df[\"manual_flag\"] = np.where(\n", "    final_slot_a_df[\"be_manual_cpd\"].isna(),\n", "    0,\n", "    1,\n", ")\n", "\n", "final_slot_a_df[\"new_final_slot_a_qty\"] = np.where(\n", "    final_slot_a_df[\"manual_flag\"] == 0,\n", "    final_slot_a_df[\"new_final_slot_a_qty\"],\n", "    final_slot_a_df[\"be_manual_cpd\"],\n", ").astype(int)\n", "\n", "\n", "final_slot_a_df = final_slot_a_df.drop(columns={\"manual_flag\", \"attribute\"})\n", "\n", "final_slot_a_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "cea705fd-e12b-426b-9e57-615ab86e80f2", "metadata": {}, "outputs": [], "source": ["## applying frontend item level manual cpd\n", "\n", "final_slot_a_df = final_slot_a_df.merge(manual_df, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "\n", "final_slot_a_df[\"manual_flag\"] = np.where(\n", "    final_slot_a_df[\"manual_cpd\"].isna(),\n", "    0,\n", "    1,\n", ")\n", "\n", "final_slot_a_df[\"new_final_slot_a_qty\"] = np.where(\n", "    final_slot_a_df[\"manual_flag\"] == 0,\n", "    final_slot_a_df[\"new_final_slot_a_qty\"],\n", "    final_slot_a_df[\"manual_cpd\"],\n", ").astype(int)\n", "\n", "\n", "final_slot_a_df = final_slot_a_df.drop(columns={\"manual_flag\", \"attribute\"})\n", "\n", "final_slot_a_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "c75738dd-388e-4819-857c-34db474f581b", "metadata": {}, "outputs": [], "source": ["# final_slot_a_df.to_csv('new_logic _with_new_queries.csv',index=False)"]}, {"cell_type": "markdown", "id": "6cf13e03-f9d7-4dba-9e75-2c3b86cfcb55", "metadata": {}, "source": ["### Flushing toggle city level"]}, {"cell_type": "code", "execution_count": null, "id": "18fd4104-5e9c-48f3-a2d8-1782ca51d755", "metadata": {}, "outputs": [], "source": ["try:\n", "    flush_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::flushing_toggle\",\n", "    )\n", "\n", "except:\n", "    flush_df = pd.read_csv(\"config__flushing_toggle.csv\")\n", "flush_df = ignore_wrong_manual_input(flush_df)\n", "flush_df[\"be_facility_id\"] = flush_df[\"be_facility_id\"].fillna(0).astype(int)\n", "flush_df[\"item_id\"] = flush_df[\"item_id\"].fillna(0).astype(int)\n", "flush_df[\"factor\"] = flush_df[\"factor\"].fillna(0).astype(float)\n", "\n", "flush_df[\"start_date\"] = pd.to_datetime(flush_df[\"start_date\"])\n", "flush_df[\"end_date\"] = pd.to_datetime(flush_df[\"end_date\"])\n", "\n", "flush_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "31c42241-36b0-4fcc-9c88-f1334f4dff02", "metadata": {}, "outputs": [], "source": ["final_slot_a_df = final_slot_a_df.merge(flush_df, on=[\"be_facility_id\", \"item_id\"], how=\"left\")\n", "\n", "final_slot_a_df[\"flush_flag\"] = np.where(\n", "    (final_slot_a_df[\"tdate\"] >= final_slot_a_df[\"start_date\"])\n", "    & (final_slot_a_df[\"tdate\"] <= final_slot_a_df[\"end_date\"]),\n", "    1,\n", "    0,\n", ")\n", "final_slot_a_df.drop(columns={\"start_date\", \"end_date\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "b6bf20c2-0d82-4a3c-a5cd-048bed71969e", "metadata": {}, "outputs": [], "source": ["flush_df[flush_df.be_facility_id == 1209]"]}, {"cell_type": "code", "execution_count": null, "id": "98f10155-6a94-493a-8ac7-62faf97a26d5", "metadata": {}, "outputs": [], "source": ["final_slot_a_df[final_slot_a_df.be_facility_id == 2171]"]}, {"cell_type": "code", "execution_count": null, "id": "864cecde-da4d-4567-9bcb-012b17be8758", "metadata": {}, "outputs": [], "source": ["indent = f\"\"\"\n", "        with f as (select \n", "            date_of_run + interval '2' day as date_,\n", "            be_outlet_id,\n", "            be_facility_id,\n", "            item_id,\n", "            final_indent,\n", "            rank() over (partition by date_of_run,be_outlet_id order by l.updated_at desc) as rk\n", "            from \n", "            supply_etls.daily_milk_indent_details l\n", "            -- left join dwh.dim_merchant_outlet_facility_mapping mp on mp.facility_id = l.facility_id\n", "            -- JOIN retail.console_outlet co ON l.facility_id = co.facility_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "            -- LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id AND cl.lake_active_record\n", "            where date_of_run >= DATE('{transfer_date}') - interval '3' day\n", "            )\n", "\n", "            select \n", "            -- city,\n", "            be_facility_id,\n", "            be_outlet_id as be_inv_outlet_id,\n", "            item_id,\n", "            sum(final_indent) as indent\n", "            from f\n", "            where rk =1\n", "            and date_ = DATE('{transfer_date}')\n", "            group by 1,2,3\n", "        \"\"\"\n", "indent_df = read_sql_query(indent, trino)\n", "# indent_df['date_'] = pd.to_datetime(indent_df['date_'])\n", "indent_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "15676e13-3bcf-45ed-81ac-a29b1298f2d8", "metadata": {"tags": []}, "outputs": [], "source": ["## Picking max of indent and backend inventory\n", "\n", "be_inv_df = indent_df.merge(be_inv_df, on=[\"be_inv_outlet_id\", \"item_id\"], how=\"left\")\n", "be_inv_df[\"indent\"] = be_inv_df[\"indent\"].fillna(0)\n", "be_inv_df[\"be_inv\"] = be_inv_df[\"be_inv\"].fillna(0)\n", "be_inv_df[\"be_inv\"] = np.where(\n", "    (be_inv_df[\"be_facility_id\"].isin(md_be_facility_id_list))\n", "    & (be_inv_df[\"item_id\"].isin(md_item_id_list)),\n", "    be_inv_df[\"be_inv\"],\n", "    be_inv_df[[\"be_inv\", \"indent\"]].max(axis=1),\n", ")\n", "be_inv_df[\"be_inv_outlet_id\"] = np.where(\n", "    be_inv_df[\"be_inv_outlet_id\"] == 2665, 2666, be_inv_df[\"be_inv_outlet_id\"]\n", ")\n", "be_inv_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d899c890-ed72-4d16-9122-2061587d53c3", "metadata": {}, "outputs": [], "source": ["be_inv_df[be_inv_df[\"be_facility_id\"].isin(md_be_facility_id_list)]"]}, {"cell_type": "code", "execution_count": null, "id": "cb222489-f065-4a8c-8546-4861987f2a2a", "metadata": {}, "outputs": [], "source": ["current_flushing = final_slot_a_df[final_slot_a_df[\"flush_flag\"] == 1][\n", "    [\n", "        \"be_inv_outlet_id\",\n", "        \"outlet_id\",\n", "        \"factor\",\n", "        \"item_id\",\n", "        \"is_slotb\",\n", "        \"new_final_slot_a_qty\",\n", "        \"slota_transfer_qty\",\n", "    ]\n", "]\n", "current_flushing[\"new_final_slot_a_qty\"] = np.where(\n", "    current_flushing[\"is_slotb\"] == 0,\n", "    np.round(current_flushing[\"new_final_slot_a_qty\"] * 0.85, 0),\n", "    current_flushing[\"new_final_slot_a_qty\"],\n", ")\n", "\n", "flush_agg = (\n", "    current_flushing.groupby([\"be_inv_outlet_id\", \"item_id\"])\n", "    .agg({\"new_final_slot_a_qty\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"new_final_slot_a_qty\": \"total\"})\n", ")\n", "current_flushing = current_flushing.merge(flush_agg, on=[\"be_inv_outlet_id\", \"item_id\"], how=\"left\")\n", "current_flushing = current_flushing.merge(be_inv_df, on=[\"be_inv_outlet_id\", \"item_id\"], how=\"left\")\n", "current_flushing[\"flush_factor\"] = (\n", "    current_flushing[\"new_final_slot_a_qty\"] / current_flushing[\"total\"]\n", ")\n", "# non_slb = current_flushing[current_flushing[\"is_slotb\"] == 0][\n", "#     [\"be_inv_outlet_id\", \"item_id\", \"total\"]\n", "# ].rename(columns={\"total\": \"non_slb\"})\n", "# current_flushing = current_flushing.merge(non_slb, on=[\"be_inv_outlet_id\", \"item_id\"], how=\"left\")\n", "# current_flushing[\"non_slb\"] = current_flushing[\"non_slb\"].fillna(0)\n", "current_flushing[\"final_slot_a\"] = np.ceil(\n", "    current_flushing[\"flush_factor\"] * current_flushing[\"be_inv\"] * current_flushing[\"factor\"]\n", ")\n", "\n", "current_flushing = current_flushing[[\"outlet_id\", \"item_id\", \"final_slot_a\"]].drop_duplicates()\n", "current_flushing.head()"]}, {"cell_type": "code", "execution_count": null, "id": "20aea869-d7c5-42c4-aa04-1ff4aaf52da3", "metadata": {}, "outputs": [], "source": ["final_slot_a_df[final_slot_a_df.outlet_id == 1393]"]}, {"cell_type": "code", "execution_count": null, "id": "3d997646-3396-4e9e-91c0-2d9cc8667742", "metadata": {}, "outputs": [], "source": ["final_slot_a_df = final_slot_a_df.merge(current_flushing, on=[\"outlet_id\", \"item_id\"], how=\"left\")\n", "final_slot_a_df[\"final_slot_a\"] = final_slot_a_df[\"final_slot_a\"].fillna(0)\n", "final_slot_a_df[\"new_final_slot_a_qty\"] = np.where(\n", "    final_slot_a_df[\"flush_flag\"] == 1,\n", "    final_slot_a_df[\"final_slot_a\"],\n", "    final_slot_a_df[\"new_final_slot_a_qty\"],\n", ")"]}, {"cell_type": "markdown", "id": "51974614-3e5f-4281-be59-c59bba503175", "metadata": {}, "source": ["### Leftover reductions"]}, {"cell_type": "code", "execution_count": null, "id": "2be53e55-cee9-464e-b911-551e7fc70e58", "metadata": {}, "outputs": [], "source": ["try:\n", "    store_item_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::store-input::quantity\",\n", "    )\n", "\n", "except:\n", "    store_item_df = pd.read_csv(\"lo_reduction.csv\").rename(columns={\"quantity\": \"reduction_qty\"})\n", "store_item_df = ignore_wrong_manual_input(store_item_df)\n", "store_item_df = store_item_df[store_item_df[\"reduction_qty\"] != \"\"]\n", "store_item_df[\"facility_id\"] = store_item_df[\"facility_id\"].fillna(0).astype(int)\n", "store_item_df[\"item_id\"] = store_item_df[\"item_id\"].fillna(0).astype(int)\n", "\n", "store_item_df[\"start_date\"] = pd.to_datetime(store_item_df[\"start_date\"])\n", "store_item_df[\"end_date\"] = pd.to_datetime(store_item_df[\"end_date\"])\n", "\n", "store_item_df[\"reduction_qty\"] = store_item_df[\"reduction_qty\"].astype(float)\n", "\n", "store_item_df[\"tdate\"] = list(\n", "    map(\n", "        lambda x, y: pd.date_range(start=x, end=y),\n", "        store_item_df[\"start_date\"],\n", "        store_item_df[\"end_date\"],\n", "    )\n", ")\n", "\n", "store_item_df = store_item_df.explode(\"tdate\").drop([\"start_date\", \"end_date\"], axis=1)\n", "\n", "store_item_df[\"tdate\"] = pd.to_datetime(store_item_df[\"tdate\"])\n", "\n", "store_item_df = store_item_df.reset_index()\n", "\n", "store_item_max = (\n", "    store_item_df.groupby([\"facility_id\", \"item_id\", \"tdate\"]).agg({\"index\": \"max\"}).reset_index()\n", ")\n", "\n", "store_item_df = store_item_df.merge(\n", "    store_item_max, on=[\"facility_id\", \"item_id\", \"tdate\", \"index\"], how=\"inner\"\n", ").drop(columns={\"index\"})\n", "\n", "store_item_df = (\n", "    store_item_df[store_item_df[\"tdate\"] >= today_date].reset_index().drop(columns={\"index\"})\n", ")\n", "\n", "store_item_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "32fa9573-d702-49b0-b8ff-2f63487842b5", "metadata": {}, "outputs": [], "source": ["final_slot_a_df = (\n", "    final_slot_a_df.merge(store_item_df, on=[\"facility_id\", \"item_id\", \"tdate\"], how=\"left\")\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "final_slot_a_df[\"reduction_qty\"] = final_slot_a_df[\"reduction_qty\"].fillna(0)\n", "final_slot_a_df[\"new_final_slot_a_qty\"] = (\n", "    np.ceil(final_slot_a_df[\"new_final_slot_a_qty\"] + final_slot_a_df[\"reduction_qty\"])\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "# final_slot_a_df.drop(columns={\"reduction_qty\"}, inplace=True)\n", "final_slot_a_df = final_slot_a_df.drop_duplicates()\n", "final_slot_a_df.head()"]}, {"cell_type": "markdown", "id": "27d83175-b4ff-4885-8ecd-14eedb70cb96", "metadata": {}, "source": ["### applying case size "]}, {"cell_type": "code", "execution_count": null, "id": "c88428e8-66f8-4951-9bf2-d99af6dbe403", "metadata": {}, "outputs": [], "source": ["try:\n", "    case_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::transfer_case_size\",\n", "    )\n", "\n", "except:\n", "    case_df = pd.read_csv(\"case_size.csv\")\n", "\n", "case_df = ignore_wrong_manual_input(case_df)\n", "case_df = case_df[case_df[\"slot\"] != \"B\"].drop(columns={\"slot\"})\n", "case_df[\"be_facility_id\"] = case_df[\"be_facility_id\"].fillna(0).astype(int)\n", "case_df[\"item_id\"] = case_df[\"item_id\"].fillna(0).astype(int)\n", "\n", "case_df[\"start_date\"] = pd.to_datetime(case_df[\"start_date\"])\n", "case_df[\"end_date\"] = pd.to_datetime(case_df[\"end_date\"])\n", "\n", "case_df[\"case_size\"] = case_df[\"case_size\"].astype(int)\n", "case_df = case_df[(case_df.start_date <= transfer_date) & (case_df.end_date >= transfer_date)]\n", "case_df = case_df[[\"be_facility_id\", \"item_id\", \"case_size\"]]\n", "case_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "cba8243f-8abb-40f1-bc1f-0a354cb75d2f", "metadata": {}, "outputs": [], "source": ["final_slot_a_df = final_slot_a_df.merge(case_df, on=[\"be_facility_id\", \"item_id\"], how=\"left\")\n", "final_slot_a_df[\"case_size\"] = final_slot_a_df[\"case_size\"].fillna(1)\n", "final_slot_a_df[\"new_final_slot_a_qty\"] = np.where(\n", "    final_slot_a_df[\"new_final_slot_a_qty\"].between(1, final_slot_a_df[\"case_size\"]),\n", "    final_slot_a_df[\"case_size\"],\n", "    final_slot_a_df[\"new_final_slot_a_qty\"],\n", ")\n", "final_slot_a_df.head(2)"]}, {"cell_type": "markdown", "id": "eeb2411f-538f-4b56-a38b-348c182067ff", "metadata": {}, "source": ["### creating leftover reduction logs\n"]}, {"cell_type": "code", "execution_count": null, "id": "c68f1fe3-ad8f-4ba3-90dc-41b5a68351cb", "metadata": {}, "outputs": [], "source": ["leftover_reduction_df = final_slot_a_df[final_slot_a_df[\"reduction_qty\"] != 0][\n", "    [\"city\", \"facility_id\", \"outlet_id\", \"item_id\", \"tdate\", \"reduction_qty\"]\n", "]\n", "leftover_reduction_df[\"reduction_qty\"] = leftover_reduction_df[\"reduction_qty\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "6cae0a2a-21f9-4db1-bd62-70f423d2f748", "metadata": {}, "outputs": [], "source": ["leftover_reduction_df[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))\n", "leftover_reduction_df[\"date_ist\"] = pd.to_datetime(\n", "    pd.to_datetime(leftover_reduction_df[\"updated_at\"]).dt.strftime(\"%Y-%m-%d\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a5053e21-874d-4402-88c9-56dfbd8ac16a", "metadata": {}, "outputs": [], "source": ["leftover_reduction_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c8a6ee87-d452-48ff-b57f-013e22aa7204", "metadata": {}, "outputs": [], "source": ["data = final_slot_a_df[[\"facility_id\", \"item_id\", \"new_final_slot_a_qty\"]]\n", "data[[\"facility_id\", \"item_id\", \"new_final_slot_a_qty\"]] = (\n", "    data[[\"facility_id\", \"item_id\", \"new_final_slot_a_qty\"]].round(0).astype(int)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9df355ab-9d37-4a81-bcc8-74c8778aceab", "metadata": {}, "outputs": [], "source": ["data[\"max_qty\"] = data[\"new_final_slot_a_qty\"]\n", "data[\"min_qty\"] = data[\"max_qty\"]\n", "data[\"is_bundle\"] = 0\n", "data[\"old_bucket\"] = 1\n", "data[\"case_type\"] = \"CUSTOM\"\n", "data[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))\n", "data[\"updated_by\"] = 14\n", "data[[\"facility_id\", \"item_id\", \"min_qty\", \"max_qty\"]] = data[\n", "    [\"facility_id\", \"item_id\", \"min_qty\", \"max_qty\"]\n", "].astype(int)\n", "data.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "3f865ab8-59f1-48d2-9e62-d23a8c0d73e6", "metadata": {}, "outputs": [], "source": ["data = data[\n", "    [\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"min_qty\",\n", "        \"max_qty\",\n", "        \"is_bundle\",\n", "        \"old_bucket\",\n", "        \"case_type\",\n", "        \"updated_at\",\n", "        \"updated_by\",\n", "    ]\n", "]\n", "data.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "9073a32d-7a66-4002-ba45-dac1f446d1d7", "metadata": {}, "outputs": [], "source": ["data = data.drop_duplicates(subset=[\"facility_id\", \"item_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "8f6b2b3f-efe3-451e-9eeb-d6d6cc5ae846", "metadata": {}, "outputs": [], "source": ["def upload_to_table(data, maintenance_flag):\n", "\n", "    MIN_MAX_SCHEMA_NAME = \"supply_etls\"\n", "    MIN_MAX_TABLE_NAME = \"facility_item_min_max_quantity\"\n", "\n", "    kwargs = {\n", "        \"schema_name\": MIN_MAX_SCHEMA_NAME,\n", "        \"table_name\": MIN_MAX_TABLE_NAME,\n", "        \"column_dtypes\": [\n", "            {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"Facility ID\"},\n", "            {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"Item ID\"},\n", "            {\"name\": \"min_qty\", \"type\": \"INTEGER\", \"description\": \"Minimum quantity\"},\n", "            {\"name\": \"max_qty\", \"type\": \"INTEGER\", \"description\": \"Maximum quantity\"},\n", "            {\"name\": \"is_bundle\", \"type\": \"INTEGER\", \"description\": \"is bundle\"},\n", "            {\n", "                \"name\": \"old_bucket\",\n", "                \"type\": \"INTEGER\",\n", "                \"description\": \"previous day bucket\",\n", "            },\n", "            {\"name\": \"case_type\", \"type\": \"VARCHAR\", \"description\": \"case type\"},\n", "            {\n", "                \"name\": \"updated_at\",\n", "                \"type\": \"TIMESTAMP(6)\",\n", "                \"description\": \"Updated at ts\",\n", "            },\n", "            {\"name\": \"updated_by\", \"type\": \"INTEGER\", \"description\": \"Updated by\"},\n", "        ],\n", "        \"primary_key\": [\"facility_id\", \"item_id\"],\n", "        \"partition_key\": [\"facility_id\"],\n", "        \"incremental_key\": \"updated_at\",\n", "        \"force_upsert_without_increment_check\": False,\n", "        \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "        \"table_description\": \"this table contains the min max quantity for transfers against each facility item\",\n", "        \"run_maintenance\": maintenance_flag,\n", "    }\n", "    pb.to_trino(data, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "80662f79-bb5d-476f-a0a2-d6e5136e361c", "metadata": {}, "outputs": [], "source": ["if (current_hour > 12) & (final_slot_a_df.shape[0] > 0):\n", "    upload_to_table(data, maintenance_flag=False)"]}, {"cell_type": "code", "execution_count": null, "id": "81eb88fd-827a-46fd-8ea0-c0ecc19ac91e", "metadata": {}, "outputs": [], "source": ["if (current_hour > 12) & (final_slot_a_df.shape[0] > 0):\n", "    try:\n", "        channel = \"bl-milk-indent-check\"\n", "        text = \" min max is updated for slot A (with guardrails) \\n <@U06T927GCSW> <@U03ABAH15H9> <@U078DPW05T4> <@U07HMKDUS59>  \"\n", "        pb.send_slack_message(channel=channel, text=text)\n", "        pb.to_sheets(\n", "            final_slot_a_df,\n", "            sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "            sheetname=\"new_transfer_logic\",\n", "        )\n", "    except:\n", "        text = \"min max is updated for slot A (with guardrails), data not pushed to milk req 2.0 sheet \\n <@U06T927GCSW> <@U03ABAH15H9> <@U078DPW05T4> <@U07HMKDUS59> \"\n", "        pb.send_slack_message(channel=channel, text=text)"]}, {"cell_type": "code", "execution_count": null, "id": "bd36d39e-1669-4fce-8ef6-58dd1ae8acc8", "metadata": {}, "outputs": [], "source": ["data_log = data.copy()\n", "\n", "data_log[\"date_ist\"] = pd.to_datetime(\n", "    pd.to_datetime(data_log[\"updated_at\"]).dt.strftime(\"%Y-%m-%d\")\n", ")\n", "\n", "data_log = data_log[\n", "    [\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"min_qty\",\n", "        \"max_qty\",\n", "        \"is_bundle\",\n", "        \"old_bucket\",\n", "        \"case_type\",\n", "        \"date_ist\",\n", "        \"updated_at\",\n", "        \"updated_by\",\n", "    ]\n", "]\n", "\n", "data_log.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "b0ed0b88-8d59-411a-8ab7-5e4455793bc2", "metadata": {}, "outputs": [], "source": ["if (current_hour > 12) & (final_slot_a_df.shape[0] > 0):\n", "    MIN_MAX_SCHEMA_NAME = \"supply_etls\"\n", "    MIN_MAX_TABLE_NAME = \"facility_item_min_max_quantity_log\"\n", "\n", "    kwargs = {\n", "        \"schema_name\": MIN_MAX_SCHEMA_NAME,\n", "        \"table_name\": MIN_MAX_TABLE_NAME,\n", "        \"column_dtypes\": [\n", "            {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"Facility ID\"},\n", "            {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"Item ID\"},\n", "            {\"name\": \"min_qty\", \"type\": \"INTEGER\", \"description\": \"Minimum quantity\"},\n", "            {\"name\": \"max_qty\", \"type\": \"INTEGER\", \"description\": \"Maximum quantity\"},\n", "            {\"name\": \"is_bundle\", \"type\": \"INTEGER\", \"description\": \"is bundle\"},\n", "            {\n", "                \"name\": \"old_bucket\",\n", "                \"type\": \"INTEGER\",\n", "                \"description\": \"previous day bucket\",\n", "            },\n", "            {\"name\": \"case_type\", \"type\": \"VARCHAR\", \"description\": \"case type\"},\n", "            {\n", "                \"name\": \"date_ist\",\n", "                \"type\": \"DATE\",\n", "                \"description\": \"updated_at filter date\",\n", "            },\n", "            {\n", "                \"name\": \"updated_at\",\n", "                \"type\": \"TIMESTAMP(6)\",\n", "                \"description\": \"Updated at ts\",\n", "            },\n", "            {\"name\": \"updated_by\", \"type\": \"INTEGER\", \"description\": \"Updated by\"},\n", "        ],\n", "        \"primary_key\": [\"facility_id\", \"item_id\"],\n", "        \"partition_key\": [\"date_ist\"],\n", "        \"incremental_key\": \"updated_at\",\n", "        \"force_upsert_without_increment_check\": False,\n", "        \"load_type\": \"append\",  # append, truncate or upsert,\n", "        \"table_description\": \"this table contains the min max quantity for transfers against each facility item\",\n", "    }\n", "    pb.to_trino(data_log, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "fa240be2-5721-49ed-8826-6dfadd08ef32", "metadata": {}, "outputs": [], "source": ["### Pushing leftover reduction logs to table"]}, {"cell_type": "code", "execution_count": null, "id": "4b25709a-5725-4706-842e-4e62883f1b57", "metadata": {}, "outputs": [], "source": ["leftover_reduction_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "07f053c3-ae35-46c6-8a37-a9ffedc629f6", "metadata": {}, "outputs": [], "source": ["if (current_hour > 12) & (final_slot_a_df.shape[0] > 0):\n", "    SCHEMA_NAME = \"supply_etls\"\n", "    TABLE_NAME = \"milk_leftover_reduction_logs\"\n", "\n", "    kwargs = {\n", "        \"schema_name\": SCHEMA_NAME,\n", "        \"table_name\": TABLE_NAME,\n", "        \"column_dtypes\": [\n", "            {\"name\": \"city\", \"type\": \"VARCHAR\", \"description\": \"city\"},\n", "            {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"Facility ID\"},\n", "            {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"outlet ID\"},\n", "            {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"Item_id \"},\n", "            {\"name\": \"tdate\", \"type\": \"DATE\", \"description\": \"transfer date\"},\n", "            {\n", "                \"name\": \"reduction_qty\",\n", "                \"type\": \"INTEGER\",\n", "                \"description\": \"quantity reduced or increased\",\n", "            },\n", "            {\n", "                \"name\": \"updated_at\",\n", "                \"type\": \"TIMESTAMP(6)\",\n", "                \"description\": \"Updated at ts\",\n", "            },\n", "            {\n", "                \"name\": \"date_ist\",\n", "                \"type\": \"DATE\",\n", "                \"description\": \"updated_at filter date\",\n", "            },\n", "        ],\n", "        \"primary_key\": [\"facility_id\", \"item_id\"],\n", "        \"partition_key\": [\"date_ist\"],\n", "        \"incremental_key\": \"updated_at\",\n", "        \"force_upsert_without_increment_check\": False,\n", "        \"load_type\": \"append\",  # append, truncate or upsert,\n", "        \"table_description\": \"this table contains the milk quantity increased or decreased\",\n", "    }\n", "    pb.to_trino(leftover_reduction_df, **kwargs)"]}, {"cell_type": "markdown", "id": "2e79ec54-9c4a-41fc-9443-3cef5d197b56", "metadata": {}, "source": ["### Slot A flushing amount alert"]}, {"cell_type": "code", "execution_count": null, "id": "be49faa1-7af6-4fff-a0bd-acf50055b481", "metadata": {}, "outputs": [], "source": ["facility_id_list = tuple(set(base_pre_df[\"facility_id\"].to_list()))\n", "outlet_id_list = tuple(set(base_pre_df[\"outlet_id\"].to_list()))\n", "item_id_list = tuple(set(base_pre_df[\"item_id\"].to_list()))\n", "\n", "len(item_id_list), len(facility_id_list), len(outlet_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "dc812e7d-b122-4d2e-8606-000c1a0f0630", "metadata": {}, "outputs": [], "source": ["try:\n", "    be_list = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"Backend Ordering w Case Size\",\n", "    )\n", "except:\n", "    be_list = pd.read_csv(\"Backend Ordering w Case Size.csv\")\n", "be_list = be_list.replace(\"\", 0)\n", "be_list[\"Be_Facility_Id\"] = be_list[\"Be_Facility_Id\"].fillna(0).astype(int)\n", "be_list = be_list[be_list[\"type\"] == \"CPC\"]\n", "be_facility_id = tuple(set(be_list[\"Be_Facility_Id\"].to_list()))"]}, {"cell_type": "code", "execution_count": null, "id": "ed36aaa3-cf67-4784-a6a5-8affe6241461", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "    SELECT item_id, name AS item_name\n", "    FROM rpc.item_category_details\n", "    WHERE item_id IN {item_id_list}\n", "    GROUP BY 1,2\n", "\"\"\"\n", "item_df = read_sql_query(query, trino)\n", "\n", "query = f\"\"\"\n", "    SELECT facility_id AS be_facility_id, outlet_name AS be_facility_name\n", "    FROM po.physical_facility_outlet_mapping\n", "    WHERE lake_active_record\n", "    and ars_active = 1\n", "    GROUP BY 1,2\n", "\"\"\"\n", "backend_df = read_sql_query(query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "04c57db5-21fb-4dfe-90dd-5f88d721833c", "metadata": {}, "outputs": [], "source": ["po_df = be_inv_df.copy()\n", "po_df[\"date_\"] = pd.to_datetime(transfer_date)\n", "# po_df[\"be_facility_id\"] = base_pre_df[\"be_facility_id\"].fillna(0).astype(int)\n", "\n", "po_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "5d2e27c6-adb5-4ea1-aa8b-cfadfae2aed1", "metadata": {}, "outputs": [], "source": ["min_max_df = data_log[[\"facility_id\", \"item_id\", \"min_qty\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "d2077d2a-30a7-43ec-8b71-b0dcfb272ce8", "metadata": {}, "outputs": [], "source": ["base = (\n", "    base_pre_df.merge(backend_df, on=[\"be_facility_id\"], how=\"left\")\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "base = base.merge(item_df, on=[\"item_id\"], how=\"left\").reset_index().drop(columns={\"index\"})\n", "base = base.merge(po_df, on=[\"be_facility_id\", \"be_inv_outlet_id\", \"item_id\"], how=\"left\")\n", "base.head()\n", "base = base.merge(min_max_df, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "base.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f71b24e2-37e6-490c-a9a2-67a20b48010d", "metadata": {}, "outputs": [], "source": ["fin_df = (\n", "    base.groupby(\n", "        [\n", "            \"date_\",\n", "            \"be_facility_id\",\n", "            \"be_facility_name\",\n", "            \"item_id\",\n", "            \"item_name\",\n", "            \"be_inv\",\n", "        ]\n", "    )\n", "    .agg({\"min_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "fin_df[\"perc_flushed\"] = np.round(fin_df[\"min_qty\"] * 100 // fin_df[\"be_inv\"])\n", "fin_df[\"perc_flushed\"] = fin_df[\"perc_flushed\"].apply(lambda x: str(x) + \"%\")\n", "fin_df.rename(columns={\"date_\": \"transfer_date\"})\n", "fin_df = fin_df[fin_df[\"be_facility_id\"].isin(be_facility_id)]\n", "fin_df.sort_values([\"be_facility_name\", \"be_inv\"], ascending=False).head(20)"]}, {"cell_type": "code", "execution_count": null, "id": "30bc8ee4-f2bd-41dd-bf70-14343f7a28f5", "metadata": {}, "outputs": [], "source": ["def send_alert():\n", "\n", "    import pencilbox as pb1\n", "\n", "    slack_channel = \"bl-milk-indent-check\"\n", "\n", "    # alert_df = pb1.from_sheets(\n", "    #     sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "    #     sheetname=\"Excess Inventory Summary\",\n", "    # )\n", "    # alert_df = alert_df\n", "\n", "    if fin_df.shape[0] > 0:\n", "        fig, ax = render_mpl_table(fin_df, header_columns=0)\n", "        fig.savefig(\"Slot_A_Quantum.png\")\n", "        file_check_1 = \"./Slot_A_Quantum.png\"\n", "        filepath_fig = file_check_1\n", "        channel = slack_channel\n", "        files = [filepath_fig]\n", "        text = f\"Slot A Quantum flushed on backend level\"\n", "    pb1.send_slack_message(channel=channel, text=text, files=files)"]}, {"cell_type": "code", "execution_count": null, "id": "f4d68396-8dec-4bcb-9b73-56ff4908ccf5", "metadata": {}, "outputs": [], "source": ["def render_mpl_table(\n", "    data,\n", "    col_width=3.05,\n", "    row_height=0.8,\n", "    font_size=10,\n", "    header_color=\"#741b47\",\n", "    footer_color=\"#434343\",\n", "    row_colors=[\n", "        \"#EFEFEF\",  # 0\n", "        \"#FFFFFF\",  # 1\n", "    ],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, cellLoc=\"center\", **kwargs\n", "    )\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"w\", fontsize=15)\n", "            cell.set_facecolor(header_color)\n", "        # elif k[0] == 10:\n", "        #     cell.set_text_props(weight=\"bold\", color=\"w\", fontsize=15)\n", "        #     cell.set_facecolor(footer_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "            cell.set_text_props(weight=\"normal\", color=\"black\", fontsize=14)\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "2807638f-6a46-4e8e-b90d-fcd626731e88", "metadata": {}, "outputs": [], "source": ["send_alert()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}