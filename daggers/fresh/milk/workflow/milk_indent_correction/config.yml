alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
  - channel: bl-invt-alerts
dag_name: milk_indent_correction
dag_type: workflow
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: fresh
notebook:
  parameters: null
  priority_weight: 2
  retries: 1
owner:
  email: <EMAIL>
  slack_id: U06T927GCSW
path: fresh/milk/workflow/milk_indent_correction
paused: false
pool: fresh_pool
project_name: milk
schedule:
  end_date: '2025-08-21T00:00:00'
  interval: 30 7,13 * * *
  start_date: '2025-05-11T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
