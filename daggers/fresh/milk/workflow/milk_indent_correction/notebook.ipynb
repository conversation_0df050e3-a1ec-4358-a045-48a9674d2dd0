{"cells": [{"cell_type": "code", "execution_count": null, "id": "45584acb-aaa9-4d83-9277-23618d7af676", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import os\n", "import time\n", "import gc\n", "import requests\n", "\n", "import calendar\n", "from datetime import datetime as dt\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.options.mode.chained_assignment = None  # default='warn'\n", "pd.set_option(\"display.max_columns\", 500)"]}, {"cell_type": "code", "execution_count": null, "id": "cda140df-574c-43dc-9584-1affe1e2e742", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pytz\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")\n", "\n", "current_time = datetime.now(IST)\n", "\n", "current_hour = pd.to_datetime(current_time).hour\n", "\n", "today_date = current_time.strftime(\"%Y-%m-%d\")\n", "\n", "today_date"]}, {"cell_type": "code", "execution_count": null, "id": "13b77f30-19a9-4725-ad32-ee22711a71b1", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 1\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "markdown", "id": "b3c0baa7-8e52-492d-b5cd-d1d9bca1c90d", "metadata": {}, "source": ["## Winter Forecast Flag - Aggressive Extrapolation"]}, {"cell_type": "code", "execution_count": null, "id": "1538a832-bf32-41bc-879a-2b7275891278", "metadata": {}, "outputs": [], "source": ["try:\n", "    winter_input_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::city-winter-input\",\n", "    )\n", "\n", "except:\n", "    winter_input_df = pd.read_csv(\"winter_input.csv\")\n", "\n", "winter_input_df[\"winter_flag\"] = np.where(winter_input_df[\"switch\"] == \"no\", 0, 1)\n", "\n", "winter_input_df = winter_input_df.drop(columns={\"state\", \"switch\"})"]}, {"cell_type": "markdown", "id": "99c177eb-6d38-4891-924c-c8bf9fb2cfd0", "metadata": {}, "source": ["## Total Disruption - City & L2"]}, {"cell_type": "code", "execution_count": null, "id": "0b0fdf2d-a8c2-4739-af2b-b04051678270", "metadata": {}, "outputs": [], "source": ["try:\n", "    tot_disruption_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::city-disruption\",\n", "    )\n", "\n", "except:\n", "    tot_disruption_df = pd.read_csv(\"city_disruption.csv\")\n", "\n", "tot_disruption_df[\"date_\"] = pd.to_datetime(tot_disruption_df[\"date_\"])\n", "\n", "tot_disruption_df[\"tot_flag\"] = 1"]}, {"cell_type": "code", "execution_count": null, "id": "acf34935-eae1-45c6-82cf-5f0f61c91dd1", "metadata": {}, "outputs": [], "source": ["x = pd.date_range(\n", "    start=(pd.to_datetime(current_time) - timed<PERSON>ta(days=30)).strftime(\"%Y-%m-%d\"),\n", "    end=(pd.to_datetime(current_time) - timedelta(days=1)).strftime(\"%Y-%m-%d\"),\n", ")\n", "\n", "date_df = pd.DataFrame({\"date_\": x, \"date_flag\": 1})\n", "\n", "hour_list = list({6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23})\n", "hour_df = pd.DataFrame({\"hour_\": hour_list, \"date_flag\": 1})\n", "\n", "date_df = date_df.merge(hour_df, on=[\"date_flag\"], how=\"left\")\n", "\n", "date_df.shape"]}, {"cell_type": "markdown", "id": "9248213a-61f3-48ea-bdd5-cacfbc32b792", "metadata": {"tags": []}, "source": ["# Data Extraction"]}, {"cell_type": "markdown", "id": "a40b1693-c03a-4345-94ec-4ed4ec4a4679", "metadata": {"tags": []}, "source": ["## Milk Forecast Base"]}, {"cell_type": "markdown", "id": "e8d78b22-b720-4d47-aa33-08979493604d", "metadata": {"tags": []}, "source": ["### Base Assortment - Fetch"]}, {"cell_type": "code", "execution_count": null, "id": "c37f81a7-def9-45a5-89ee-ef76395dbe76", "metadata": {}, "outputs": [], "source": ["base_query = \"\"\"\n", "    WITH active_stores AS (\n", "        SELECT DISTINCT pfom.facility_id, pfom.outlet_id, bfom.facility_id AS be_facility_id\n", "        FROM po.physical_facility_outlet_mapping pfom \n", "        JOIN retail.console_outlet co ON co.id = pfom.outlet_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "        JOIN po.bulk_facility_outlet_mapping bfom ON bfom.outlet_id = pfom.outlet_id AND bfom.active = True AND bfom.lake_active_record\n", "        WHERE ars_active = 1 AND pfom.active = 1 AND pfom.is_primary = 1 AND pfom.lake_active_record\n", "    ),\n", "\n", "    milk_assortment AS (\n", "        SELECT DISTINCT cl.name AS city, a.facility_id, outlet_id, item_id, be_facility_id\n", "        FROM rpc.product_facility_master_assortment a\n", "        JOIN active_stores b ON a.facility_id = b.facility_id\n", "        JOIN retail.console_outlet co ON co.id = b.outlet_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "        LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id AND cl.lake_active_record\n", "        WHERE item_id IN (SELECT DISTINCT item_id FROM rpc.item_category_details WHERE l2_id = 1185 AND lake_active_record) \n", "        AND a.active = 1 AND a.master_assortment_substate_id = 1 AND a.lake_active_record\n", "    ),\n", "\n", "    be_mapping AS (\n", "        SELECT DISTINCT item_id, outlet_id, cb.facility_id AS be_facility_id\n", "        FROM rpc.item_outlet_tag_mapping tm\n", "        JOIN retail.console_outlet cb ON cb.id = CAST(tag_value AS int) AND cb.active = 1 AND cb.lake_active_record\n", "        WHERE tm.active = 1 AND tm.tag_type_id = 8 AND tm.lake_active_record\n", "    ),\n", "\n", "    final AS (\n", "        SELECT city, facility_id, a.outlet_id, a.item_id, a.be_facility_id\n", "        FROM milk_assortment a\n", "        JOIN be_mapping b ON a.item_id = b.item_id AND a.outlet_id = b.outlet_id AND a.be_facility_id = b.be_facility_id\n", "    )\n", "\n", "    SELECT * FROM final\n", "\"\"\"\n", "base_pre_df = read_sql_query(base_query, trino)\n", "\n", "base_pre_df[\"be_facility_id\"] = base_pre_df[\"be_facility_id\"].fillna(0).astype(int)\n", "\n", "base_pre_df.shape"]}, {"cell_type": "markdown", "id": "d2d70ff7-f1be-4cc2-951b-c0d5390634c4", "metadata": {"tags": []}, "source": ["### TAT Calculation"]}, {"cell_type": "code", "execution_count": null, "id": "f9880145-2c73-4439-83c8-4fe688ee42b8", "metadata": {}, "outputs": [], "source": ["base_df = base_pre_df.copy()\n", "\n", "base_df[\"date_flag\"] = 1\n", "\n", "base_df = base_df.merge(date_df, on=[\"date_flag\"], how=\"left\").drop(columns={\"date_flag\"})\n", "\n", "base_df = base_df.merge(winter_input_df, on=[\"city\"], how=\"left\")\n", "\n", "base_df[\"winter_flag\"] = base_df[\"winter_flag\"].fillna(0).astype(int)\n", "\n", "base_df[\"tat\"] = 1\n", "\n", "base_df[\"tdate\"] = pd.to_datetime(today_date) + pd.to_timedelta(base_df[\"tat\"], unit=\"D\")\n", "\n", "base_df[\"tdow\"] = pd.to_datetime(base_df[\"tdate\"]).dt.dayofweek\n", "\n", "base_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "936e0053-3c63-4c69-9695-7631128e6650", "metadata": {}, "outputs": [], "source": ["del [base_pre_df]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "1231aced-1398-493c-bb50-bc5d11fd9ddb", "metadata": {"tags": []}, "source": ["### Item, Facility, Outlet List"]}, {"cell_type": "code", "execution_count": null, "id": "4a940b37-9d27-42b6-be70-d2feefa615b6", "metadata": {}, "outputs": [], "source": ["facility_id_list = tuple(set(base_df[\"facility_id\"].to_list()))\n", "outlet_id_list = tuple(set(base_df[\"outlet_id\"].to_list()))\n", "item_id_list = tuple(set(base_df[\"item_id\"].to_list()))\n", "\n", "len(item_id_list), len(facility_id_list), len(outlet_id_list)"]}, {"cell_type": "markdown", "id": "464288b2-0942-4dba-9302-3ab6dcd8ee26", "metadata": {"tags": []}, "source": ["## Hourly Availability"]}, {"cell_type": "code", "execution_count": null, "id": "fb655e14-3aa9-49b1-a19f-cd9d0189b978", "metadata": {}, "outputs": [], "source": ["base_query = f\"\"\"\n", "    WITH base AS (\n", "        SELECT * FROM supply_etls.hourly_inventory_snapshots\n", "        WHERE date_ist >= current_date - interval '31' day\n", "    )\n", "    \n", "    SELECT date_, hour_, facility_id, item_id, MAX(is_available) AS is_available\n", "    FROM (\n", "        SELECT DATE(date_ist) AS date_, EXTRACT(hour FROM updated_at_ist) AS hour_, facility_id, item_id, \n", "        CASE \n", "            WHEN current_inventory > 0 THEN 1 \n", "            ELSE 0  \n", "        END is_available\n", "        FROM base\n", "        WHERE item_id IN {item_id_list} AND outlet_id IN {outlet_id_list}\n", "    )\n", "    GROUP BY 1,2,3,4\n", "\"\"\"\n", "availability_df = read_sql_query(base_query, trino)\n", "\n", "availability_df[\"date_\"] = pd.to_datetime(availability_df[\"date_\"])\n", "\n", "availability_df.shape"]}, {"cell_type": "markdown", "id": "a513dc33-f0b6-43ef-8457-d5b2327ebc69", "metadata": {"tags": []}, "source": ["#### Assortment Correction"]}, {"cell_type": "code", "execution_count": null, "id": "63b3808b-40ec-424f-a815-ea5914ae3471", "metadata": {}, "outputs": [], "source": ["assortment_df = pd.merge(\n", "    base_df,\n", "    availability_df,\n", "    on=[\"item_id\", \"facility_id\", \"date_\", \"hour_\"],\n", "    how=\"left\",\n", ")\n", "\n", "assortment_df[\"is_available\"] = np.where(\n", "    assortment_df[\"is_available\"].isna(), 0, assortment_df[\"is_available\"]\n", ")\n", "\n", "assortment_df[\"dow\"] = pd.to_datetime(assortment_df[\"date_\"]).dt.dayofweek\n", "\n", "assortment_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "9821425f-1351-4add-a4b7-e9fdf4a1a643", "metadata": {}, "outputs": [], "source": ["del [availability_df, base_df]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "fa71582f-e4c4-496a-907b-604854f14a5c", "metadata": {"tags": []}, "source": ["### Hourly Sales"]}, {"cell_type": "code", "execution_count": null, "id": "42b4078f-d507-4972-87d5-f7997e65e82e", "metadata": {"tags": []}, "outputs": [], "source": ["sale_query = f\"\"\"\n", "    WITH item_mapping as (\n", "        SELECT DISTINCT ipr.product_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NULL THEN ipom_0.item_id \n", "            ELSE ipr.item_id \n", "        END AS item_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1) \n", "            ELSE COALESCE(ipom_0.multiplier,1) \n", "        END AS multiplier\n", "        FROM rpc.item_product_mapping ipr \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", "        WHERE ipr.lake_active_record\n", "    ),\n", "\n", "    sales AS (\n", "        SELECT (oid.cart_checkout_ts_ist) AS order_date, cl.name AS city, rco.facility_id, oid.product_id, im.item_id, oid.order_id, im.multiplier, ((oid.procured_quantity - oid.total_doorstep_return_quantity) * im.multiplier) AS sales_quantity \n", "        FROM dwh.fact_sales_order_item_details oid \n", "        JOIN item_mapping im on im.product_id = oid.product_id  \n", "        JOIN retail.console_outlet rco ON rco.id = oid.outlet_id AND business_type_id = 7 AND rco.lake_active_record\n", "        LEFT JOIN retail.console_location cl ON cl.id = rco.tax_location_id AND cl.lake_active_record\n", "        WHERE oid.order_create_dt_ist >= DATE('{today_date}') - interval '31' day\n", "        AND oid.is_internal_order = false  \n", "        AND (oid.order_type NOT LIKE '%%internal%%' OR oid.order_type IS NULL)  \n", "        AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED'\n", "        AND oid.outlet_id IN {outlet_id_list}\n", "    ),\n", "    \n", "    final_sales AS (\n", "        SELECT DATE(order_date) AS date_, EXTRACT(hour FROM order_date) AS hour_, city, facility_id, s.item_id, CAST(SUM(sales_quantity) AS int) AS quantity\n", "        FROM sales s\n", "        WHERE item_id IN {item_id_list}\n", "        GROUP BY 1,2,3,4,5\n", "    )\n", "    \n", "    SELECT DISTINCT city, facility_id, item_id, hour_, date_, quantity \n", "    FROM final_sales\n", "    WHERE hour_ BETWEEN 6 AND 23\n", "    \"\"\"\n", "sales_pre_df = read_sql_query(sale_query, trino)\n", "sales_pre_df[\"date_\"] = pd.to_datetime(sales_pre_df[\"date_\"])\n", "\n", "sales_pre_df.shape"]}, {"cell_type": "markdown", "id": "26cd380f-21e9-4e0c-bf97-25ab7ece73b7", "metadata": {"tags": []}, "source": ["## Daily Carts & Cart Penetration - Store Definition & Disruption"]}, {"cell_type": "code", "execution_count": null, "id": "6a5fcb8f-589a-4921-8505-0ffd4ea0b4fa", "metadata": {}, "outputs": [], "source": ["carts_query = f\"\"\"\n", "    WITH item_mapping AS (\n", "        SELECT DISTINCT ipr.product_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NULL THEN ipom_0.item_id \n", "            ELSE ipr.item_id \n", "        END AS item_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1) \n", "            ELSE COALESCE(ipom_0.multiplier,1) \n", "        END AS multiplier\n", "        FROM rpc.item_product_mapping ipr \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", "        WHERE ipr.lake_active_record\n", "    ),\n", "\n", "    sales AS (\n", "        SELECT (oid.cart_checkout_ts_ist) AS order_date, oid.outlet_id, oid.product_id, im.item_id, oid.order_id, im.multiplier, oid.total_doorstep_return_quantity, ((oid.procured_quantity - oid.total_doorstep_return_quantity) * im.multiplier) AS sales_quantity \n", "        FROM dwh.fact_sales_order_item_details oid \n", "        JOIN item_mapping im on im.product_id = oid.product_id  \n", "        WHERE oid.order_create_dt_ist >= DATE('{today_date}') - interval '60' day\n", "        AND oid.is_internal_order = false  \n", "        AND (oid.order_type NOT LIKE '%%internal%%' OR oid.order_type IS NULL)  \n", "        AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED'\n", "        AND oid.outlet_id IN {outlet_id_list}\n", "    ),\n", "    \n", "    all_carts AS (\n", "        SELECT DATE(order_date) AS date_, EXTRACT(hour FROM order_date) AS hour_, outlet_id, COUNT(DISTINCT order_id) AS fac_carts\n", "        FROM sales\n", "        GROUP BY 1,2,3\n", "    ),\n", "    \n", "    milk_carts AS (\n", "        SELECT DATE(order_date) AS date_, EXTRACT(hour FROM order_date) AS hour_, outlet_id, COUNT(DISTINCT order_id) AS milk_carts\n", "        FROM sales\n", "        WHERE item_id IN {item_id_list}\n", "        GROUP BY 1,2,3\n", "    )\n", "    \n", "    SELECT a.outlet_id, a.date_, a.hour_, fac_carts, \n", "    CASE \n", "        WHEN milk_carts IS NULL THEN 0\n", "        ELSE milk_carts \n", "    END AS milk_carts\n", "    FROM all_carts a\n", "    LEFT JOIN milk_carts b ON a.outlet_id = b.outlet_id AND a.date_ = b.date_ AND a.hour_ = b.hour_\n", "    WHERE a.hour_ BETWEEN 6 AND 23\n", "    \"\"\"\n", "carts_df = read_sql_query(carts_query, trino)\n", "\n", "carts_df[\"date_\"] = pd.to_datetime(carts_df[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "659ca067-18fe-4653-8486-e90f8c6108f5", "metadata": {}, "outputs": [], "source": ["morning_carts_df = (\n", "    carts_df[carts_df[\"hour_\"] < 18]\n", "    .groupby([\"date_\", \"outlet_id\"])\n", "    .agg({\"fac_carts\": \"sum\", \"milk_carts\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "morning_carts_df = morning_carts_df.rename(\n", "    columns={\"fac_carts\": \"morning_fac_carts\", \"milk_carts\": \"morning_milk_carts\"}\n", ")\n", "\n", "carts_agg_df = (\n", "    carts_df.groupby([\"date_\", \"outlet_id\"])\n", "    .agg({\"fac_carts\": \"sum\", \"milk_carts\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "l15_date = (current_time - timedelta(days=15)).strftime(\"%Y-%m-%d\")\n", "\n", "x = pd.date_range(\n", "    start=pd.to_datetime(l15_date).strftime(\"%Y-%m-%d\"),\n", "    end=(pd.to_datetime(today_date)).strftime(\"%Y-%m-%d\"),\n", ")\n", "\n", "date_df = pd.DataFrame({\"date_\": x, \"flag\": 1})\n", "\n", "store_base_df = pd.DataFrame(outlet_id_list, columns=[\"outlet_id\"])\n", "store_base_df[\"flag\"] = 1\n", "\n", "carts_base_df = pd.merge(store_base_df, date_df, on=[\"flag\"], how=\"left\").drop(columns={\"flag\"})\n", "\n", "carts_base_df = pd.merge(carts_base_df, morning_carts_df, on=[\"outlet_id\", \"date_\"], how=\"left\")\n", "\n", "carts_base_df = pd.merge(carts_base_df, carts_agg_df, on=[\"outlet_id\", \"date_\"], how=\"left\")\n", "\n", "carts_base_df[\"morning_cp\"] = (\n", "    carts_base_df[\"morning_milk_carts\"] / carts_base_df[\"morning_fac_carts\"]\n", ")\n", "\n", "carts_base_df[\"cp\"] = carts_base_df[\"milk_carts\"] / carts_base_df[\"fac_carts\"]\n", "\n", "carts_base_df[[\"fac_carts\", \"milk_carts\", \"cp\"]] = carts_base_df[\n", "    [\"fac_carts\", \"milk_carts\", \"cp\"]\n", "].fillna(0)\n", "\n", "carts_base_df[[\"morning_fac_carts\", \"morning_milk_carts\", \"morning_cp\"]] = carts_base_df[\n", "    [\"morning_fac_carts\", \"morning_milk_carts\", \"morning_cp\"]\n", "].fillna(0)\n", "\n", "carts_base_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "dfdcdd2d-f8bd-4a8f-9f28-f3874a6b5ed3", "metadata": {}, "outputs": [], "source": ["def percentile(x):\n", "    return np.ceil(np.percentile(x, 75)).astype(int)\n", "\n", "\n", "def cp_percentile(x):\n", "    return np.percentile(x, 75)"]}, {"cell_type": "markdown", "id": "9ac79292-a9f3-4aaf-9438-c47abc3088ee", "metadata": {"tags": []}, "source": ["### Disruption Days"]}, {"cell_type": "markdown", "id": "4ca04f61-8a4b-4e01-ae5e-3621da473e86", "metadata": {"tags": []}, "source": ["#### Store Disruption"]}, {"cell_type": "code", "execution_count": null, "id": "bab9dd08-5194-4541-b73e-036640713d67", "metadata": {}, "outputs": [], "source": ["opd_df = (\n", "    carts_base_df[(carts_base_df[\"fac_carts\"] != 0)]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "opd_df = (\n", "    opd_df.groupby([\"outlet_id\"])\n", "    .agg({\"fac_carts\": percentile, \"morning_fac_carts\": percentile})\n", "    .reset_index()\n", ")\n", "\n", "opd_df = opd_df.rename(columns={\"fac_carts\": \"opd\", \"morning_fac_carts\": \"morning_opd\"})\n", "\n", "opd_df[\"ftype_\"] = np.where(\n", "    opd_df[\"opd\"] < 800, \"low\", np.where(opd_df[\"opd\"] >= 1200, \"high\", \"medium\")\n", ")\n", "\n", "opd_df.shape"]}, {"cell_type": "markdown", "id": "1ab98296-3745-421f-bb0c-032cd71370fc", "metadata": {"tags": []}, "source": ["#### Milk Carts Disruption"]}, {"cell_type": "code", "execution_count": null, "id": "ffd0ce9b-c0da-4fb1-a30e-1f74db6c6852", "metadata": {}, "outputs": [], "source": ["milk_cp_df = (\n", "    carts_base_df[carts_base_df[\"cp\"] != 0].drop_duplicates().reset_index().drop(columns={\"index\"})\n", ")\n", "\n", "milk_cp_df = (\n", "    milk_cp_df.groupby([\"outlet_id\"])\n", "    .agg({\"cp\": cp_percentile, \"morning_cp\": cp_percentile})\n", "    .reset_index()\n", ")\n", "\n", "milk_cp_df = milk_cp_df.rename(columns={\"cp\": \"milk_cp\", \"morning_cp\": \"morning_milk_cp\"})\n", "\n", "milk_cp_df[\"cp_bucket\"] = np.where(\n", "    milk_cp_df[\"milk_cp\"] < 0.1,\n", "    \"low\",\n", "    np.where(milk_cp_df[\"milk_cp\"] >= 0.18, \"high\", \"medium\"),\n", ")\n", "\n", "milk_cp_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "46f6a06e-0d3e-4998-9dec-71424163988a", "metadata": {}, "outputs": [], "source": ["disruption_base_df = pd.merge(carts_base_df, opd_df, on=[\"outlet_id\"], how=\"left\")\n", "\n", "disruption_base_df = pd.merge(disruption_base_df, milk_cp_df, on=[\"outlet_id\"], how=\"left\")\n", "\n", "disruption_base_df[[\"cp_bucket\", \"ftype_\"]] = disruption_base_df[[\"cp_bucket\", \"ftype_\"]].fillna(\n", "    \"low\"\n", ")\n", "\n", "disruption_base_df = pd.merge(carts_base_df, opd_df, on=[\"outlet_id\"], how=\"left\")\n", "\n", "disruption_base_df = pd.merge(disruption_base_df, milk_cp_df, on=[\"outlet_id\"], how=\"left\")\n", "\n", "disruption_base_df[[\"cp_bucket\", \"ftype_\"]] = disruption_base_df[[\"cp_bucket\", \"ftype_\"]].fillna(\n", "    \"low\"\n", ")\n", "\n", "disruption_base_df[\"past_opd_deviation\"] = (\n", "    disruption_base_df[\"fac_carts\"] / disruption_base_df[\"opd\"] - 1\n", ")\n", "\n", "disruption_base_df[\"current_opd_deviation\"] = (\n", "    disruption_base_df[\"morning_fac_carts\"] / disruption_base_df[\"morning_opd\"] - 1\n", ")\n", "\n", "disruption_base_df[\"past_cp_deviation\"] = (\n", "    disruption_base_df[\"cp\"] / disruption_base_df[\"milk_cp\"] - 1\n", ")\n", "\n", "disruption_base_df[\"current_cp_deviation\"] = (\n", "    disruption_base_df[\"morning_cp\"] / disruption_base_df[\"morning_milk_cp\"] - 1\n", ")\n", "\n", "disruption_base_df.head(1)"]}, {"cell_type": "markdown", "id": "287f5a78-f5c8-4926-afaa-ee45b97b1296", "metadata": {}, "source": ["#### Morning Disruption Check"]}, {"cell_type": "code", "execution_count": null, "id": "d32695d8-4573-4873-b19c-2c929abe8a6a", "metadata": {}, "outputs": [], "source": ["morning_disruption_base_df = disruption_base_df.copy()\n", "\n", "morning_disruption_base_df[\"opd_deviation\"] = morning_disruption_base_df[\"current_opd_deviation\"]\n", "\n", "morning_disruption_base_df[\"cp_deviation\"] = morning_disruption_base_df[\"current_cp_deviation\"]\n", "\n", "morning_disruption_base_df[\"opd_flag\"] = np.where(\n", "    (morning_disruption_base_df[\"ftype_\"].isin({\"high\", \"medium\"}))\n", "    & (morning_disruption_base_df[\"opd_deviation\"] <= -0.25),\n", "    1,\n", "    0,\n", ")\n", "\n", "morning_disruption_base_df[\"cp_flag\"] = np.where(\n", "    (morning_disruption_base_df[\"ftype_\"].isin({\"high\", \"medium\"}))\n", "    & (morning_disruption_base_df[\"cp_deviation\"] <= -0.4),\n", "    1,\n", "    0,\n", ")\n", "\n", "morning_disruption_base_df[\"d_flag\"] = (\n", "    morning_disruption_base_df[\"opd_flag\"] + morning_disruption_base_df[\"cp_flag\"]\n", ")\n", "\n", "morning_disruption_df = (\n", "    morning_disruption_base_df[morning_disruption_base_df[\"d_flag\"] > 0][\n", "        [\"outlet_id\", \"date_\", \"d_flag\"]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "morning_disruption_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "c83a092b-00cf-455d-8e2f-055bf39988ed", "metadata": {}, "outputs": [], "source": ["buffer_store = f\"\"\"with\n", "    frontend_merchant_mapping as\n", "            (select * from dwh.dim_merchant_outlet_facility_mapping\n", "                where \n", "                    is_frontend_merchant_active = true\n", "                    and\n", "                        is_backend_merchant_active = true\n", "                    and \n", "                        is_pos_outlet_active = 1\n", "                    and \n", "                        is_mapping_enabled = true\n", "                    and \n", "                        is_express_store = true\n", "                    and \n", "                        is_current_mapping_active = true\n", "                    and \n", "                        is_current = true\n", "                    and \n", "                        pos_outlet_name <> 'SS Gurgaon Test Store'\n", "    ),\n", "\n", "    store_polygon_updates as (\n", "    select \n", "        cr.updated_at + interval '5' hour + interval '30' minute as updated_time, \n", "        f.external_id as merchant_id, \n", "        json_query(cr.meta, 'strict $.business_impact.old_order_count') as old_order_count,\n", "        json_query(cr.meta, 'strict $.business_impact.new_order_count') as new_orders_count,\n", "        json_query(cr.diff, 'strict $.polygon.info') as change_in_area\n", "    from \n", "        sauron.change_requests cr \n", "    join \n", "        sauron.feature f \n", "        on f.id=cr.feature_id\n", "    where \n", "        f.layer_id = 6 \n", "        AND cr.updated_at + interval '5' hour + interval '30' minute > CURRENT_DATE - interval '30' day \n", "        and cr.updated_at + interval '5' hour + interval '30' minute < CURRENT_DATE \n", "        AND cr.state = 'MERGED'\n", "        -- and f.external_id in (33207)\n", "    )\n", "\n", "    select \n", "        fm.pos_outlet_id as outlet_id,\n", "        fm.facility_id as facility_id,\n", "        date(max(updated_time)) as date_n\n", "\n", "    from \n", "        store_polygon_updates s\n", "    left join frontend_merchant_mapping  fm on fm.frontend_merchant_id = s.merchant_id\n", "    where s.updated_time between  date'{today_date}' - interval '10' day and date'{today_date}' \n", "\n", "group by 1,2\n", "    \"\"\"\n", "\n", "buffer_store_mapping = pd.read_sql_query(buffer_store, trino)\n", "buffer_store_mapping[\"date_n\"] = pd.to_datetime(buffer_store_mapping[\"date_n\"])\n", "\n", "buffer_store_facility_list = list(set(buffer_store_mapping[\"facility_id\"].unique()))"]}, {"cell_type": "code", "execution_count": null, "id": "c92ed55c-695d-41f9-92da-d0981c607a9f", "metadata": {}, "outputs": [], "source": ["# wl_store_df = pd.concat([buffer_store_mapping,new_store_mapping])\n", "morning_disruption_df = morning_disruption_df.merge(\n", "    buffer_store_mapping[[\"outlet_id\", \"date_n\"]], on=[\"outlet_id\"], how=\"left\"\n", ")\n", "morning_disruption_df[\"d_flag\"] = np.where(\n", "    morning_disruption_df[\"date_\"] >= morning_disruption_df[\"date_n\"],\n", "    0,\n", "    morning_disruption_df[\"d_flag\"],\n", ")\n", "morning_disruption_df.drop(columns={\"date_n\"}, inplace=True)"]}, {"cell_type": "markdown", "id": "54eb9ac9-3bb0-43a3-a297-c7c692b3b307", "metadata": {}, "source": ["#### Overall Disruption Check"]}, {"cell_type": "code", "execution_count": null, "id": "4d0d3f46-b3de-4d2c-8949-e95ac9bf6df1", "metadata": {}, "outputs": [], "source": ["overall_disruption_base_df = disruption_base_df.copy()\n", "\n", "overall_disruption_base_df[\"opd_deviation\"] = overall_disruption_base_df[\"past_opd_deviation\"]\n", "\n", "overall_disruption_base_df[\"cp_deviation\"] = overall_disruption_base_df[\"past_cp_deviation\"]\n", "\n", "overall_disruption_base_df[\"opd_flag\"] = np.where(\n", "    (overall_disruption_base_df[\"ftype_\"].isin({\"high\", \"medium\"}))\n", "    & (overall_disruption_base_df[\"opd_deviation\"] <= -0.25),\n", "    1,\n", "    0,\n", ")\n", "\n", "overall_disruption_base_df[\"cp_flag\"] = np.where(\n", "    (overall_disruption_base_df[\"ftype_\"].isin({\"high\", \"medium\"}))\n", "    & (overall_disruption_base_df[\"cp_deviation\"] <= -0.4),\n", "    1,\n", "    0,\n", ")\n", "\n", "overall_disruption_base_df[\"d_flag\"] = (\n", "    overall_disruption_base_df[\"opd_flag\"] + overall_disruption_base_df[\"cp_flag\"]\n", ")\n", "\n", "overall_disruption_df = (\n", "    overall_disruption_base_df[overall_disruption_base_df[\"d_flag\"] > 0][\n", "        [\"outlet_id\", \"date_\", \"d_flag\"]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "overall_disruption_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "ebf99f10-68ad-44fc-bfc6-ab86efed5f34", "metadata": {}, "outputs": [], "source": ["overall_disruption_df = overall_disruption_df.merge(\n", "    buffer_store_mapping[[\"outlet_id\", \"date_n\"]], on=[\"outlet_id\"], how=\"left\"\n", ")\n", "overall_disruption_df[\"d_flag\"] = np.where(\n", "    overall_disruption_df[\"date_\"] >= overall_disruption_df[\"date_n\"],\n", "    0,\n", "    overall_disruption_df[\"d_flag\"],\n", ")\n", "overall_disruption_df.drop(columns={\"date_n\"}, inplace=True)\n", "overall_disruption_df.head()"]}, {"cell_type": "markdown", "id": "5b9b941f-addf-4b92-9dc2-9ac0cd2bb823", "metadata": {}, "source": ["#### Store Type Definition"]}, {"cell_type": "code", "execution_count": null, "id": "5f627b67-fbe3-43ab-aaa2-6683c71be2d0", "metadata": {}, "outputs": [], "source": ["store_type_df = (\n", "    disruption_base_df[[\"outlet_id\", \"cp_bucket\", \"ftype_\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "store_type_df[\"ftype_\"] = np.where(\n", "    (store_type_df[\"ftype_\"] == \"medium\") & (store_type_df[\"cp_bucket\"].isin({\"low\"})),\n", "    \"low\",\n", "    store_type_df[\"ftype_\"],\n", ")\n", "\n", "store_type_df[\"ftype_\"] = np.where(\n", "    (store_type_df[\"ftype_\"] == \"high\") & (store_type_df[\"cp_bucket\"].isin({\"medium\", \"low\"})),\n", "    \"medium\",\n", "    store_type_df[\"ftype_\"],\n", ")\n", "\n", "store_type_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "da0fc3f2-0d3a-442d-94ec-088f76b2f2b1", "metadata": {}, "outputs": [], "source": ["store_type_df.groupby([\"ftype_\"]).agg({\"outlet_id\": \"count\"}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "64dc8499-9766-49d3-8fba-06715a460799", "metadata": {}, "outputs": [], "source": ["del [opd_df, milk_cp_df]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "11a473d7-8ae7-4913-bf5a-c78e052c24de", "metadata": {"tags": []}, "source": ["### Bucketing - Item Ranking"]}, {"cell_type": "code", "execution_count": null, "id": "0585c277-5341-4ed6-96c1-e5fa28587010", "metadata": {}, "outputs": [], "source": ["sku_rank_df = (\n", "    sales_pre_df.groupby([\"date_\", \"facility_id\", \"item_id\"]).agg({\"quantity\": \"sum\"}).reset_index()\n", ")\n", "\n", "sku_rank_df = (\n", "    sku_rank_df.groupby([\"facility_id\", \"item_id\"]).agg({\"quantity\": \"mean\"}).reset_index()\n", ")\n", "\n", "sku_rank_df[\"quantity\"] = sku_rank_df[\"quantity\"].astype(int)\n", "\n", "sku_rank_df = sku_rank_df.sort_values(by=\"quantity\", ascending=True)\n", "\n", "sku_rank_df[\"cum_quantity\"] = sku_rank_df.groupby([\"facility_id\"])[\"quantity\"].cumsum()\n", "\n", "sku_agg_df = (\n", "    sku_rank_df.groupby([\"facility_id\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"quantity\": \"tot_quantity\"})\n", ")\n", "\n", "sku_rank_df = sku_rank_df.merge(sku_agg_df, on=[\"facility_id\"], how=\"left\")\n", "\n", "sku_rank_df[\"perc_contri\"] = sku_rank_df[\"cum_quantity\"] / sku_rank_df[\"tot_quantity\"]\n", "\n", "## 70% sales contributing.. (If consistantly lower fills then distribution changes)\n", "sku_rank_df[\"stype_\"] = np.where(sku_rank_df[\"perc_contri\"] > 0.3, \"top\", \"bottom\")\n", "\n", "sku_rank_df = sku_rank_df[[\"facility_id\", \"item_id\", \"stype_\"]].drop_duplicates()\n", "\n", "sku_rank_df.head(1)"]}, {"cell_type": "markdown", "id": "7567cec4-3505-46ef-952d-a24a22c2ca7d", "metadata": {}, "source": ["### Premium Milk"]}, {"cell_type": "code", "execution_count": null, "id": "1961a31e-c52b-4892-a94f-0ff4b028709a", "metadata": {}, "outputs": [], "source": ["premium_df = (\n", "    sales_pre_df.groupby([\"city\", \"date_\", \"item_id\"])\n", "    .agg({\"quantity\": \"sum\", \"facility_id\": \"nunique\"})\n", "    .reset_index()\n", ")\n", "\n", "premium_df[\"qps\"] = premium_df[\"quantity\"] / premium_df[\"facility_id\"]\n", "\n", "premium_df = premium_df.groupby([\"city\", \"item_id\"]).agg({\"qps\": \"mean\"}).reset_index()\n", "\n", "premium_df[\"premium_flag\"] = np.where(premium_df[\"qps\"] <= 5, 1, 0)\n", "\n", "premium_df.head(1)"]}, {"cell_type": "markdown", "id": "e25ba609-45f6-4c49-8356-beeec3e37016", "metadata": {"tags": []}, "source": ["### Merge with Assortment "]}, {"cell_type": "code", "execution_count": null, "id": "a0be1ef3-a764-44d2-8da1-332a61d93f54", "metadata": {}, "outputs": [], "source": ["assortment_filter_df = pd.merge(\n", "    assortment_df, store_type_df[[\"outlet_id\", \"ftype_\"]], on=[\"outlet_id\"], how=\"left\"\n", ")\n", "\n", "assortment_filter_df = assortment_filter_df.merge(\n", "    sku_rank_df, on=[\"facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "\n", "assortment_filter_df = assortment_filter_df.merge(\n", "    premium_df.drop(columns={\"qps\"}), on=[\"city\", \"item_id\"], how=\"left\"\n", ")\n", "\n", "assortment_filter_df[\"stype_\"] = np.where(\n", "    assortment_filter_df[\"premium_flag\"] == 1, \"premium\", assortment_filter_df[\"stype_\"]\n", ")\n", "\n", "assortment_filter_df = assortment_filter_df.drop(columns={\"premium_flag\"})\n", "\n", "assortment_filter_df[\"date_\"] = pd.to_datetime(assortment_filter_df[\"date_\"])\n", "\n", "assortment_filter_df[\"ftype_\"] = assortment_filter_df[\"ftype_\"].fillna(\"low\")\n", "assortment_filter_df[\"stype_\"] = assortment_filter_df[\"stype_\"].fillna(\"bottom\")\n", "\n", "assortment_filter_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "face5197-e0a7-427c-aa16-81881952252b", "metadata": {}, "outputs": [], "source": ["assortment_filter_df = assortment_filter_df.merge(\n", "    tot_disruption_df, on=[\"city\", \"date_\"], how=\"left\"\n", ")\n", "\n", "assortment_filter_df[\"tot_flag\"] = np.where(\n", "    assortment_filter_df[\"tot_flag\"].isna(), 0, assortment_filter_df[\"tot_flag\"]\n", ")\n", "\n", "assortment_filter_df = (\n", "    assortment_filter_df[assortment_filter_df[\"tot_flag\"] == 0]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\", \"tot_flag\"})\n", ")\n", "\n", "assortment_filter_df.shape"]}, {"cell_type": "markdown", "id": "63fcdaca-d269-4870-a5ed-04fd62526ec2", "metadata": {}, "source": ["## Hour Weights - Search + Carts Logic"]}, {"cell_type": "markdown", "id": "adacecee-8867-41ea-ae23-55bee49c448d", "metadata": {}, "source": ["### Carts based Weights"]}, {"cell_type": "code", "execution_count": null, "id": "2c4d7ee9-187c-490f-bc9a-a2eb41ec38fd", "metadata": {}, "outputs": [], "source": ["hourly_carts_query = f\"\"\"\n", "    WITH item_mapping AS (\n", "        SELECT DISTINCT ipr.product_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NULL THEN ipom_0.item_id \n", "            ELSE ipr.item_id \n", "        END AS item_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1) \n", "            ELSE COALESCE(ipom_0.multiplier,1) \n", "        END AS multiplier\n", "        FROM rpc.item_product_mapping ipr \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", "        WHERE ipr.lake_active_record\n", "    ),\n", "\n", "    sales AS (\n", "        SELECT (oid.cart_checkout_ts_ist) AS order_date, oid.outlet_id, oid.product_id, im.item_id, oid.order_id, im.multiplier, \n", "        oid.total_doorstep_return_quantity, ((oid.procured_quantity - oid.total_doorstep_return_quantity) * im.multiplier) AS sales_quantity\n", "        FROM dwh.fact_sales_order_item_details oid\n", "        JOIN item_mapping im on im.product_id = oid.product_id \n", "        WHERE oid.order_create_dt_ist >= DATE('{today_date}') - interval '15' day\n", "        AND oid.is_internal_order = false \n", "        AND (oid.order_type NOT LIKE '%%internal%%' OR oid.order_type IS NULL) \n", "        AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED' AND outlet_id IN {outlet_id_list}\n", "    ),\n", "\n", "    pre_summary AS (\n", "        SELECT outlet_id, hour_, item_id, AVG(milk_carts) AS milk_carts\n", "        FROM (\n", "            SELECT DATE(order_date) AS date_, EXTRACT(hour FROM order_date) AS hour_, outlet_id, item_id, COUNT(DISTINCT order_id) AS milk_carts\n", "            FROM sales\n", "            WHERE item_id IN {item_id_list}\n", "            GROUP BY 1,2,3,4\n", "        )\n", "        WHERE date_ < current_date\n", "        GROUP BY 1,2,3\n", "    ),\n", "    \n", "    summary AS (\n", "        SELECT a.*, cl.name AS city \n", "        FROM pre_summary a\n", "        JOIN retail.console_outlet co ON co.id = a.outlet_id AND co.active = 1 AND co.business_type_id = 7 AND lake_active_record\n", "        JOIN retail.console_location cl ON cl.id = co.tax_location_id\n", "        WHERE hour_ BETWEEN 6 AND 23\n", "    )\n", "    \n", "    SELECT city, item_id, hour_, SUM(milk_carts) AS milk_carts\n", "    FROM summary\n", "    GROUP BY 1,2,3\n", "    \"\"\"\n", "carts_hourly_df = read_sql_query(hourly_carts_query, trino)\n", "\n", "carts_agg_df = (\n", "    carts_hourly_df.groupby([\"city\", \"item_id\"])\n", "    .agg({\"milk_carts\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"milk_carts\": \"tot_milk_carts\"})\n", ")\n", "\n", "carts_hourly_df = carts_hourly_df.merge(carts_agg_df, on=[\"city\", \"item_id\"], how=\"left\")\n", "\n", "carts_hourly_df[\"chw\"] = carts_hourly_df[\"milk_carts\"] / carts_hourly_df[\"tot_milk_carts\"]\n", "\n", "carts_hourly_df = carts_hourly_df[[\"city\", \"item_id\", \"hour_\", \"chw\"]]"]}, {"cell_type": "markdown", "id": "b55d3895-6a5b-47bb-965c-f89822ee84df", "metadata": {}, "source": ["### Search based Weights"]}, {"cell_type": "code", "execution_count": null, "id": "90511865-2ace-4b7e-a40b-f0dd10b2115b", "metadata": {}, "outputs": [], "source": ["search_hourly_query = \"\"\"\n", "    WITH city_item_hour_search_wt AS (\n", "        WITH psuedo_base AS (\n", "            SELECT * FROM supply_etls.merchant_item_search_weights\n", "            WHERE at_date_ist BETWEEN current_date - interval '7' day AND current_date - interval '1' day\n", "        ),\n", "\n", "        search_base AS (\n", "            SELECT b.* from psuedo_base b\n", "            JOIN rpc.item_category_details icd ON icd.item_id = b.item_id AND icd.l2_id = 1185 AND icd.lake_active_record\n", "        ),\n", "\n", "        hour_item_city_search AS (\n", "            SELECT hour_of_the_day AS hour_, city_name AS city, item_id, SUM(searches_total) AS hour_searches\n", "            FROM search_base \n", "            GROUP BY 1,2,3\n", "        ),\n", "\n", "        item_city_search AS (\n", "            SELECT item_id, city_name AS city, SUM(searches_total) AS item_searches\n", "            FROM search_base\n", "            GROUP BY 1,2\n", "        )\n", "\n", "        SELECT hic.city, hic.item_id, hic.hour_, hour_searches * 1.00000 / NULLIF(item_searches,0) AS cshw\n", "        FROM hour_item_city_search hic\n", "        LEFT JOIN item_city_search ic ON hic.item_id = ic.item_id AND hic.city = ic.city\n", "    )\n", "\n", "    SELECT * FROM city_item_hour_search_wt\n", "    ORDER BY hour_\n", "    \"\"\"\n", "search_hourly_df = read_sql_query(search_hourly_query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "f1f87a0b-c0ae-45da-8fec-8ea14ca9ae5f", "metadata": {}, "outputs": [], "source": ["hour_weights_df = (\n", "    assortment_df[[\"city\", \"item_id\", \"hour_\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "hour_weights_df = hour_weights_df.merge(\n", "    search_hourly_df, on=[\"city\", \"item_id\", \"hour_\"], how=\"left\"\n", ")\n", "\n", "hour_weights_df = hour_weights_df.merge(\n", "    carts_hourly_df, on=[\"city\", \"item_id\", \"hour_\"], how=\"left\"\n", ")\n", "\n", "hour_weights_df[[\"cshw\", \"chw\"]] = hour_weights_df[[\"cshw\", \"chw\"]].fillna(0)\n", "\n", "hour_weights_df[\"hw\"] = (hour_weights_df[\"cshw\"] * 0.75) + (hour_weights_df[\"chw\"] * 0.25)\n", "\n", "hour_weights_agg_df = (\n", "    hour_weights_df.groupby([\"city\", \"item_id\"])\n", "    .agg({\"hw\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"hw\": \"tot_hw\"})\n", ")\n", "\n", "hour_weights_df = hour_weights_df.merge(hour_weights_agg_df, on=[\"city\", \"item_id\"], how=\"left\")\n", "\n", "hour_weights_df[\"new_hw\"] = hour_weights_df[\"hw\"] / hour_weights_df[\"tot_hw\"]\n", "\n", "hour_weights_df = (\n", "    hour_weights_df[[\"city\", \"item_id\", \"hour_\", \"new_hw\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "del [search_hourly_df, carts_hourly_df, carts_agg_df, hour_weights_agg_df]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "692e4335-a063-4485-b241-c29eabef954e", "metadata": {}, "outputs": [], "source": ["overall_disruption_df.head()"]}, {"cell_type": "markdown", "id": "e42db196-1f66-4c39-a04f-b811d41343dd", "metadata": {"tags": []}, "source": ["# Morning Extrapolation - including Current Date"]}, {"cell_type": "markdown", "id": "a8c9cca6-856e-4b52-a02d-f6d55ab7af3c", "metadata": {}, "source": ["### Merge with DataFrame"]}, {"cell_type": "code", "execution_count": null, "id": "743e1d14-40c0-40c4-b8d4-dd5e6e5da13c", "metadata": {}, "outputs": [], "source": ["assortment_final_df = pd.merge(\n", "    assortment_filter_df, morning_disruption_df, on=[\"outlet_id\", \"date_\"], how=\"left\"\n", ")\n", "\n", "assortment_final_df[\"d_flag\"] = np.where(\n", "    assortment_final_df[\"d_flag\"].isna(), 0, assortment_final_df[\"d_flag\"]\n", ")\n", "\n", "assortment_final_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "27c2abc5-c3bd-41d3-b13f-e50b40adb2c4", "metadata": {}, "outputs": [], "source": ["assortment_final_df = (\n", "    assortment_final_df[assortment_final_df[\"d_flag\"] == 0]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\", \"d_flag\"})\n", ")\n", "\n", "assortment_final_df.shape"]}, {"cell_type": "markdown", "id": "07ee108e-d8c2-4df4-8d33-eb41c6a3a1a9", "metadata": {"tags": []}, "source": ["### Ranking & Sequencing"]}, {"cell_type": "code", "execution_count": null, "id": "44862518-b9a0-449c-bb2d-5ed5eddeb7e2", "metadata": {}, "outputs": [], "source": ["ranking_df = assortment_final_df[\n", "    [\"item_id\", \"facility_id\", \"date_\", \"dow\", \"tdate\", \"tdow\"]\n", "].drop_duplicates()\n", "\n", "ranking_df[\"r1\"] = ranking_df.groupby([\"item_id\", \"facility_id\"])[\"date_\"].rank(\n", "    method=\"dense\", ascending=False\n", ")\n", "\n", "ranking_df[\"flag\"] = np.where(\n", "    (ranking_df[\"facility_id\"].isin(buffer_store_facility_list)) & (ranking_df[\"r1\"] > 3),\n", "    1,\n", "    0,\n", ")\n", "same_dow = (\n", "    ranking_df[ranking_df[\"dow\"] == ranking_df[\"tdow\"]].copy().sort_values(\"date_\", ascending=False)\n", ")\n", "same_dow[\"latest_dow\"] = same_dow.groupby([\"item_id\", \"facility_id\", \"tdate\"])[\"date_\"].rank(\n", "    method=\"dense\", ascending=False\n", ")\n", "same_dow = same_dow[same_dow.latest_dow > 1]\n", "same_dow[\"nd_flag\"] = 1\n", "ranking_df = ranking_df.merge(\n", "    same_dow[[\"facility_id\", \"item_id\", \"date_\", \"nd_flag\"]],\n", "    on=[\"facility_id\", \"item_id\", \"date_\"],\n", "    how=\"left\",\n", ")\n", "ranking_df[\"nd_flag\"] = ranking_df[\"nd_flag\"].fillna(0)\n", "ranking_df = ranking_df[ranking_df[\"nd_flag\"] == 0]\n", "ranking_df = ranking_df[ranking_df[\"flag\"] == 0]\n", "ranking_df[\"r2\"] = np.where(\n", "    (ranking_df[\"dow\"] == ranking_df[\"tdow\"])\n", "    & ~(ranking_df[\"facility_id\"].isin(buffer_store_facility_list)),\n", "    ranking_df[\"r1\"],\n", "    ranking_df[\"r1\"] + 30,\n", ")\n", "\n", "\n", "#\n", "ranking_df[\"rank\"] = ranking_df.groupby([\"item_id\", \"facility_id\"])[\"r2\"].rank(\n", "    method=\"dense\", ascending=True\n", ")\n", "ranking_df = ranking_df[ranking_df[\"rank\"] < 8]\n", "\n", "ranking_df = ranking_df[[\"facility_id\", \"item_id\", \"date_\", \"rank\"]].drop_duplicates()\n", "\n", "print(ranking_df.shape)\n", "ranking_df.head(1)"]}, {"cell_type": "markdown", "id": "7df6bf5e-7363-4d8a-8414-568e1ea86853", "metadata": {}, "source": ["### Final Assortment"]}, {"cell_type": "code", "execution_count": null, "id": "6df35487-4fc5-449e-9d75-ca75000c193e", "metadata": {}, "outputs": [], "source": ["assortment_base_df = pd.merge(\n", "    assortment_filter_df,\n", "    ranking_df,\n", "    on=[\"item_id\", \"facility_id\", \"date_\"],\n", "    how=\"left\",\n", ")\n", "assortment_base_df[\"rank\"] = assortment_base_df[\"rank\"].fillna(666)\n", "assortment_base_df[\"flag\"] = np.where(\n", "    (assortment_base_df[\"stype_\"].isin({\"top\", \"bottom\"})) & (assortment_base_df[\"hour_\"] <= 23),\n", "    1,\n", "    np.where(\n", "        (assortment_base_df[\"stype_\"].isin({\"premium\"})) & (assortment_base_df[\"hour_\"] <= 21),\n", "        1,\n", "        0,\n", "    ),\n", ")\n", "\n", "assortment_base_df[\"flag\"] = np.where(\n", "    (assortment_base_df[\"stype_\"].isin({\"top\", \"bottom\"}))\n", "    & (assortment_base_df[\"hour_\"] <= 23)\n", "    & (assortment_base_df[\"winter_flag\"] == 1),\n", "    1,\n", "    assortment_base_df[\"flag\"],\n", ")\n", "\n", "assortment_base_df = assortment_base_df[assortment_base_df[\"flag\"] == 1].drop(columns={\"flag\"})\n", "\n", "assortment_base_df.shape"]}, {"cell_type": "markdown", "id": "951bca56-85e1-4969-93b1-f39124ce35dc", "metadata": {}, "source": ["### Assortment & Sales Merge"]}, {"cell_type": "code", "execution_count": null, "id": "82ded3c2-e11d-4cac-99d1-9933e45533e2", "metadata": {}, "outputs": [], "source": ["assortment_sales_df = pd.merge(\n", "    assortment_base_df,\n", "    sales_pre_df,\n", "    on=[\"item_id\", \"facility_id\", \"hour_\", \"date_\", \"city\"],\n", "    how=\"left\",\n", ")\n", "\n", "assortment_sales_df[\"is_available\"] = np.where(\n", "    assortment_sales_df[\"quantity\"] > 0, 1, assortment_sales_df[\"is_available\"]\n", ")\n", "\n", "assortment_sales_df[\"quantity\"] = np.where(\n", "    (assortment_sales_df[\"is_available\"] == 1) & (assortment_sales_df[\"quantity\"].isna()),\n", "    0,\n", "    assortment_sales_df[\"quantity\"],\n", ")\n", "\n", "assortment_sales_df = assortment_sales_df.merge(\n", "    hour_weights_df, on=[\"city\", \"item_id\", \"hour_\"], how=\"left\"\n", ")\n", "\n", "assortment_sales_df[\"hw\"] = np.where(\n", "    assortment_sales_df[\"new_hw\"].isna(), 0, assortment_sales_df[\"new_hw\"]\n", ")\n", "\n", "assortment_sales_df[\"wt_score\"] = assortment_sales_df[\"is_available\"] * assortment_sales_df[\"hw\"]\n", "\n", "assortment_sales_df.head(1)"]}, {"cell_type": "markdown", "id": "603c8697-9a7e-4dbd-8c5a-864ad13db60d", "metadata": {}, "source": ["### Daywise Aggregation"]}, {"cell_type": "code", "execution_count": null, "id": "7020be9a-545c-4ca4-b46c-12ad39b64188", "metadata": {}, "outputs": [], "source": ["daywise_df = (\n", "    assortment_sales_df[assortment_sales_df[\"hour_\"] < 18]\n", "    .groupby(\n", "        [\n", "            \"city\",\n", "            \"outlet_id\",\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"be_facility_id\",\n", "            \"tat\",\n", "            \"tdate\",\n", "            \"tdow\",\n", "            \"date_\",\n", "            \"dow\",\n", "            \"rank\",\n", "            \"stype_\",\n", "            \"ftype_\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"is_available\": \"sum\",\n", "            \"hour_\": \"nunique\",\n", "            \"hw\": \"sum\",\n", "            \"wt_score\": \"sum\",\n", "            \"quantity\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "daywise_df[\"avail\"] = daywise_df[\"is_available\"] / daywise_df[\"hour_\"]\n", "daywise_df[\"wt_avail\"] = daywise_df[\"wt_score\"] / daywise_df[\"hw\"]\n", "\n", "daywise_df[\"wt_avail\"] = np.where(\n", "    (daywise_df[\"avail\"] > 0) & (daywise_df[\"wt_avail\"] == 0), 1, daywise_df[\"wt_avail\"]\n", ")\n", "\n", "daywise_df[\"ext_qty_li\"] = daywise_df[\"quantity\"] * (1 + 0.35 * (1 - daywise_df[\"wt_avail\"]))\n", "\n", "daywise_df[\"ext_qty_exp\"] = daywise_df[\"quantity\"] * (5 ** (0.50 * (1 - daywise_df[\"wt_avail\"])))\n", "\n", "daywise_df[\"ext_qty_para\"] = daywise_df[\"quantity\"] * (\n", "    1 + ((1 - daywise_df[\"wt_avail\"]) ** 2) / (4 * 0.08)\n", ")\n", "\n", "daywise_df[\"ext_qty_hybrid\"] = np.where(\n", "    daywise_df[\"stype_\"] == \"premium\",\n", "    daywise_df[\"ext_qty_li\"],\n", "    daywise_df[[\"ext_qty_para\", \"ext_qty_exp\", \"ext_qty_li\"]].max(axis=1),\n", ")\n", "\n", "wow_bumps = daywise_df.copy()\n", "daywise_df = daywise_df[daywise_df[\"rank\"] <= 7]\n", "daywise_df = daywise_df.drop(columns={\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"})\n", "daywise_df = daywise_df[daywise_df[\"ext_qty_hybrid\"] > 0]\n", "daywise_df.head(1)"]}, {"cell_type": "markdown", "id": "4d7436cc-2f91-4464-a132-7dd689b860d3", "metadata": {"tags": []}, "source": ["### Weights Calculation for Dates"]}, {"cell_type": "markdown", "id": "7f445343-99b3-44b9-83be-828c50766136", "metadata": {}, "source": ["#### High & Medium OPD Stores"]}, {"cell_type": "code", "execution_count": null, "id": "3b6b89e3-94d1-425a-bb4a-64f04d8e5b94", "metadata": {}, "outputs": [], "source": ["date_weights_df_1 = daywise_df[\n", "    daywise_df[\"ftype_\"].isin({\"high\", \"medium\"}) & (daywise_df[\"rank\"] <= 5)\n", "][[\"item_id\", \"facility_id\", \"date_\", \"rank\"]]\n", "\n", "date_weights_df_1[\"wr\"] = date_weights_df_1.groupby([\"item_id\", \"facility_id\"])[\"rank\"].rank(\n", "    method=\"dense\", ascending=True\n", ")\n", "\n", "wr_agg = (\n", "    date_weights_df_1.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"total_rank\"})\n", ")\n", "\n", "wr_max = (\n", "    date_weights_df_1.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"max\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"max_rank\"})\n", ")\n", "\n", "date_weights_df_1 = date_weights_df_1.merge(wr_agg, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "date_weights_df_1 = date_weights_df_1.merge(wr_max, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "date_weights_df_1[\"weights\"] = (\n", "    date_weights_df_1[\"max_rank\"] - date_weights_df_1[\"wr\"] + 1\n", ") / date_weights_df_1[\"total_rank\"]\n", "\n", "date_weights_df_1 = date_weights_df_1.drop(columns={\"max_rank\", \"wr\", \"total_rank\", \"rank\"})"]}, {"cell_type": "markdown", "id": "1c446689-5d87-48f7-be88-25e3b6834677", "metadata": {}, "source": ["#### Low OPD Stores"]}, {"cell_type": "code", "execution_count": null, "id": "cdc5dd48-5070-41c0-9b05-8f268815fcca", "metadata": {}, "outputs": [], "source": ["date_weights_df_2 = daywise_df[(daywise_df[\"ftype_\"] == \"low\") & (daywise_df[\"rank\"] <= 5)][\n", "    [\"item_id\", \"facility_id\", \"date_\", \"ext_qty_hybrid\"]\n", "]\n", "\n", "date_weights_df_2[\"wr\"] = date_weights_df_2.groupby([\"item_id\", \"facility_id\"])[\n", "    \"ext_qty_hybrid\"\n", "].rank(method=\"dense\", ascending=True)\n", "\n", "wr_agg = (\n", "    date_weights_df_2.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"total_rank\"})\n", ")\n", "\n", "wr_max = (\n", "    date_weights_df_2.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"max\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"max_rank\"})\n", ")\n", "\n", "date_weights_df_2 = date_weights_df_2.merge(wr_agg, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "date_weights_df_2 = date_weights_df_2.merge(wr_max, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "date_weights_df_2[\"weights\"] = date_weights_df_2[\"wr\"] / date_weights_df_2[\"total_rank\"]\n", "\n", "date_weights_df_2 = date_weights_df_2.drop(\n", "    columns={\"max_rank\", \"wr\", \"total_rank\", \"ext_qty_hybrid\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "07d18a67-2589-488b-9c08-f884066f0b4a", "metadata": {}, "outputs": [], "source": ["date_weights_df = pd.concat([date_weights_df_1, date_weights_df_2])\n", "date_weights_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "45dede44-b96d-4e03-9bce-60d087121e99", "metadata": {}, "outputs": [], "source": ["del [date_weights_df_1, date_weights_df_2, wr_agg, wr_max]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "a175f8f9-7455-4b4c-a6eb-020861472ac6", "metadata": {}, "source": ["### Weights Merge with DataFrame"]}, {"cell_type": "code", "execution_count": null, "id": "b784f975-937f-46ee-9110-ee82a796094d", "metadata": {}, "outputs": [], "source": ["daywise_df = (\n", "    daywise_df[daywise_df[\"rank\"] <= 5]\n", "    .merge(date_weights_df, on=[\"item_id\", \"facility_id\", \"date_\"], how=\"left\")\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "daywise_df[\"transfer_ext_qty\"] = daywise_df[\"ext_qty_hybrid\"] * daywise_df[\"weights\"]\n", "\n", "# daywise_df[\"date_dow\"] = np.where(\n", "#     daywise_df[\"dow\"].isin({0, 1, 2, 3, 4}),\n", "#     \"weekday\",\n", "#     np.where(daywise_df[\"dow\"] == 5, \"saturday\", \"sunday\"),\n", "# )\n", "\n", "# daywise_df[\"transfer_dow\"] = np.where(\n", "#     daywise_df[\"tdow\"].isin({0, 1, 2, 3, 4}),\n", "#     \"weekday\",\n", "#     np.where(daywise_df[\"tdow\"] == 5, \"saturday\", \"sunday\"),\n", "# )\n", "\n", "daywise_df.head(1)"]}, {"cell_type": "markdown", "id": "e76b14cd-90a5-4aa9-b075-c111d11a2a3a", "metadata": {}, "source": ["## Weekday Weekend Normalisation"]}, {"cell_type": "code", "execution_count": null, "id": "fe84c953-bdd3-4930-bab3-49d4d87289aa", "metadata": {}, "outputs": [], "source": ["increment_df = (\n", "    wow_bumps[wow_bumps[\"hour_\"].between(6, 17)]\n", "    .groupby([\"date_\", \"facility_id\"])\n", "    .agg({\"ext_qty_li\": \"sum\"})\n", "    .rename(columns={\"ext_qty_li\": \"quantity\"})\n", "    .reset_index()\n", ")\n", "\n", "increment_df[\"date_\"] = pd.to_datetime(increment_df[\"date_\"])\n", "increment_df[\"dow\"] = pd.to_datetime(increment_df[\"date_\"]).dt.dayofweek\n", "\n", "increment_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "0e7d79c4-97dd-4e9a-b05c-1e0e3e2d83f6", "metadata": {}, "outputs": [], "source": ["increment_df.head()\n", "increment_agg_df = (\n", "    increment_df.groupby([\"facility_id\", \"dow\"]).agg({\"quantity\": percentile}).reset_index()\n", ")\n", "increment_df_fdow = increment_agg_df.copy()\n", "increment_df_fdow.rename(columns={\"dow\": \"tdow\", \"quantity\": \"tdow_quantity\"}, inplace=True)\n", "increment_df_calc = increment_agg_df.merge(increment_df_fdow, on=[\"facility_id\"], how=\"left\")\n", "# fdow_df = increment_agg_df[(increment_agg_df[\"fdow\"] == increment_agg_df[\"dow\"])][\n", "#                         [\"facility_id\", \"fdow_carts\"]\n", "#                     ].drop_duplicates().rename(columns={'carts':'fdow_carts'})\n", "# fin_increment = pd.merge(increment_df_calc,fdow_df, on=['facility_id'], how= 'left')\n", "increment_df_calc[\"bf\"] = increment_df_calc[\"tdow_quantity\"] / increment_df_calc[\"quantity\"]\n", "increment_df_calc[\"bf\"] = increment_df_calc[\"bf\"].fillna(1)\n", "increment_df_calc[\"bf\"] = increment_df_calc[\"bf\"].clip(0.5, 1.5)\n", "increment_df_calc = increment_df_calc[[\"facility_id\", \"dow\", \"tdow\", \"bf\"]]\n", "increment_df_calc[increment_df_calc[\"facility_id\"] == 271].head(20)"]}, {"cell_type": "markdown", "id": "e08a6cc3-52a4-4447-b58c-cef278fcf4b4", "metadata": {}, "source": ["### Apply Weekday - Weekend Normalization"]}, {"cell_type": "code", "execution_count": null, "id": "521a2b9d-65fd-43e2-a6d6-e3ea01fa2b79", "metadata": {}, "outputs": [], "source": ["transfer_df = (\n", "    daywise_df.groupby(\n", "        [\n", "            \"city\",\n", "            \"be_facility_id\",\n", "            \"facility_id\",\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"stype_\",\n", "            \"ftype_\",\n", "            \"dow\",\n", "            \"tdate\",\n", "            \"tdow\",\n", "        ]\n", "    )\n", "    .agg({\"transfer_ext_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "# transfer_df = transfer_df.merge(increment_agg_df, on=[\"facility_id\"], how=\"left\")\n", "\n", "# transfer_df[[\"w_st\", \"w_su\", \"st_su\"]] = transfer_df[[\"w_st\", \"w_su\", \"st_su\"]].fillna(\n", "#     1\n", "# )\n", "\n", "transfer_df = transfer_df.merge(increment_df_calc, on=[\"facility_id\", \"dow\", \"tdow\"], how=\"left\")\n", "\n", "\n", "transfer_df[\"bf\"] = np.where(transfer_df[\"bf\"] > 1.5, 1.5, transfer_df[\"bf\"])\n", "transfer_df[\"bf\"] = np.where(transfer_df[\"bf\"] < 0.5, 0.5, transfer_df[\"bf\"])\n", "transfer_df[\"bf\"] = transfer_df[\"bf\"].fillna(1)\n", "transfer_df[\"slota_transfer_qty\"] = np.round(transfer_df[\"transfer_ext_qty\"] * transfer_df[\"bf\"], 0)\n", "\n", "transfer_df = (\n", "    transfer_df.groupby(\n", "        [\n", "            \"city\",\n", "            \"be_facility_id\",\n", "            \"facility_id\",\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"stype_\",\n", "            \"ftype_\",\n", "            \"tdate\",\n", "        ]\n", "    )\n", "    .agg({\"slota_transfer_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "transfer_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "b447b8af-3df2-4dbc-b5ab-ae7558fae3e7", "metadata": {}, "outputs": [], "source": ["del [\n", "    ranking_df,\n", "    assortment_base_df,\n", "    assortment_sales_df,\n", "    date_weights_df,\n", "    assortment_final_df,\n", "]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "b8c0234f-b06e-47b2-90fe-5180af85241a", "metadata": {"tags": []}, "source": ["# Overall Day Extrapolation - excluding Current Date"]}, {"cell_type": "markdown", "id": "334167b7-d562-4c59-9f4d-cd394f3ddd2c", "metadata": {}, "source": ["### Merge with DataFrame"]}, {"cell_type": "code", "execution_count": null, "id": "417c4946-bb79-4117-9176-4ff1fc2ce6d7", "metadata": {}, "outputs": [], "source": ["assortment_final_df = pd.merge(\n", "    assortment_filter_df, overall_disruption_df, on=[\"outlet_id\", \"date_\"], how=\"left\"\n", ")\n", "\n", "assortment_final_df[\"d_flag\"] = np.where(\n", "    assortment_final_df[\"d_flag\"].isna(), 0, assortment_final_df[\"d_flag\"]\n", ")\n", "\n", "assortment_final_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "4b84ee07-e474-48b9-9e7c-4d423527d7dd", "metadata": {}, "outputs": [], "source": ["assortment_final_df = (\n", "    assortment_final_df[assortment_final_df[\"d_flag\"] == 0]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\", \"d_flag\"})\n", ")\n", "\n", "assortment_final_df.shape"]}, {"cell_type": "markdown", "id": "ae431900-a26a-4dd4-86e9-58d87e43c556", "metadata": {}, "source": ["### Ranking & Sequencing"]}, {"cell_type": "code", "execution_count": null, "id": "a0ffc037-c427-49db-991a-9fa25b964907", "metadata": {}, "outputs": [], "source": ["ranking_df = assortment_final_df[assortment_final_df[\"date_\"] < today_date][\n", "    [\"item_id\", \"facility_id\", \"date_\", \"dow\", \"tdate\", \"tdow\"]\n", "].drop_duplicates()\n", "\n", "ranking_df[\"r1\"] = ranking_df.groupby([\"item_id\", \"facility_id\"])[\"date_\"].rank(\n", "    method=\"dense\", ascending=False\n", ")\n", "\n", "ranking_df = ranking_df[ranking_df[\"r1\"] < 8]\n", "ranking_df[\"flag\"] = np.where(\n", "    (ranking_df[\"facility_id\"].isin(buffer_store_facility_list)) & (ranking_df[\"r1\"] > 3),\n", "    1,\n", "    0,\n", ")\n", "ranking_df = ranking_df[ranking_df[\"flag\"] == 0]\n", "ranking_df[\"r2\"] = np.where(\n", "    (ranking_df[\"dow\"] == ranking_df[\"tdow\"])\n", "    & ~(ranking_df[\"facility_id\"].isin(buffer_store_facility_list)),\n", "    ranking_df[\"r1\"],\n", "    ranking_df[\"r1\"] + 15,\n", ")\n", "\n", "ranking_df[\"rank\"] = ranking_df.groupby([\"item_id\", \"facility_id\"])[\"r2\"].rank(\n", "    method=\"dense\", ascending=True\n", ")\n", "\n", "ranking_df = ranking_df[[\"facility_id\", \"item_id\", \"date_\", \"rank\"]].drop_duplicates()"]}, {"cell_type": "markdown", "id": "abc44ce4-c57d-4907-851d-20c9cbe3b5b6", "metadata": {}, "source": ["### Final Assortment"]}, {"cell_type": "code", "execution_count": null, "id": "9959ce8b-2f96-4609-9170-3b9c8a8ecd13", "metadata": {}, "outputs": [], "source": ["assortment_base_df = pd.merge(\n", "    assortment_final_df, ranking_df, on=[\"item_id\", \"facility_id\", \"date_\"], how=\"inner\"\n", ")\n", "\n", "assortment_base_df[\"flag\"] = np.where(\n", "    (assortment_base_df[\"stype_\"].isin({\"top\", \"bottom\"})) & (assortment_base_df[\"hour_\"] <= 23),\n", "    1,\n", "    np.where(\n", "        (assortment_base_df[\"stype_\"].isin({\"premium\"})) & (assortment_base_df[\"hour_\"] < 21),\n", "        1,\n", "        0,\n", "    ),\n", ")\n", "\n", "assortment_base_df[\"flag\"] = np.where(\n", "    (assortment_base_df[\"stype_\"].isin({\"top\", \"bottom\"}))\n", "    & (assortment_base_df[\"hour_\"] <= 23)\n", "    & (assortment_base_df[\"winter_flag\"] == 1),\n", "    1,\n", "    assortment_base_df[\"flag\"],\n", ")\n", "\n", "assortment_base_df = assortment_base_df[assortment_base_df[\"flag\"] == 1].drop(columns={\"flag\"})\n", "\n", "assortment_base_df.shape"]}, {"cell_type": "markdown", "id": "5d15652a-7fa1-43ec-a4a5-e9333882051c", "metadata": {}, "source": ["### Assortment & Sales Merge"]}, {"cell_type": "code", "execution_count": null, "id": "6c3942a0-0828-49f6-892a-179564ccab1d", "metadata": {}, "outputs": [], "source": ["assortment_sales_df = pd.merge(\n", "    assortment_base_df,\n", "    sales_pre_df,\n", "    on=[\"item_id\", \"facility_id\", \"hour_\", \"date_\", \"city\"],\n", "    how=\"left\",\n", ")\n", "\n", "assortment_sales_df[\"is_available\"] = np.where(\n", "    assortment_sales_df[\"quantity\"] > 0, 1, assortment_sales_df[\"is_available\"]\n", ")\n", "\n", "assortment_sales_df[\"quantity\"] = np.where(\n", "    (assortment_sales_df[\"is_available\"] == 1) & (assortment_sales_df[\"quantity\"].isna()),\n", "    0,\n", "    assortment_sales_df[\"quantity\"],\n", ")\n", "\n", "assortment_sales_df = assortment_sales_df.merge(\n", "    hour_weights_df, on=[\"city\", \"item_id\", \"hour_\"], how=\"left\"\n", ")\n", "\n", "assortment_sales_df[\"hw\"] = np.where(\n", "    assortment_sales_df[\"new_hw\"].isna(), 0, assortment_sales_df[\"new_hw\"]\n", ")\n", "\n", "assortment_sales_df[\"wt_score\"] = assortment_sales_df[\"is_available\"] * assortment_sales_df[\"hw\"]\n", "\n", "assortment_sales_df.head(1)"]}, {"cell_type": "markdown", "id": "93a51da8-4f59-4b4c-a1f2-6efed06a7995", "metadata": {}, "source": ["### Daywise Aggregation"]}, {"cell_type": "code", "execution_count": null, "id": "5ed00df8-5ecf-4153-9f10-5e3b697aa91b", "metadata": {}, "outputs": [], "source": ["overall_daywise_df = (\n", "    assortment_sales_df.groupby(\n", "        [\n", "            \"city\",\n", "            \"outlet_id\",\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"be_facility_id\",\n", "            \"tat\",\n", "            \"tdate\",\n", "            \"tdow\",\n", "            \"date_\",\n", "            \"winter_flag\",\n", "            \"dow\",\n", "            \"rank\",\n", "            \"stype_\",\n", "            \"ftype_\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"is_available\": \"sum\",\n", "            \"hour_\": \"nunique\",\n", "            \"hw\": \"sum\",\n", "            \"wt_score\": \"sum\",\n", "            \"quantity\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "overall_daywise_df[\"avail\"] = overall_daywise_df[\"is_available\"] / overall_daywise_df[\"hour_\"]\n", "\n", "overall_daywise_df[\"wt_avail\"] = overall_daywise_df[\"wt_score\"] / overall_daywise_df[\"hw\"]\n", "\n", "overall_daywise_df[\"wt_avail\"] = np.where(\n", "    (overall_daywise_df[\"avail\"] > 0) & (overall_daywise_df[\"wt_avail\"] == 0),\n", "    1,\n", "    overall_daywise_df[\"wt_avail\"],\n", ")\n", "\n", "overall_daywise_df[\"ext_qty_li\"] = np.ceil(\n", "    overall_daywise_df[\"quantity\"] * (1 + 0.35 * (1 - overall_daywise_df[\"wt_avail\"]))\n", ")\n", "\n", "overall_daywise_df[\"ext_qty_exp\"] = np.ceil(\n", "    overall_daywise_df[\"quantity\"] * (5 ** (0.50 * (1 - overall_daywise_df[\"wt_avail\"])))\n", ")\n", "\n", "overall_daywise_df[\"ext_qty_para\"] = np.ceil(\n", "    overall_daywise_df[\"quantity\"] * (1 + ((1 - overall_daywise_df[\"wt_avail\"]) ** 2) / (4 * 0.08))\n", ")\n", "\n", "overall_daywise_df[\"high_ext_qty\"] = np.where(\n", "    overall_daywise_df[\"ftype_\"] == \"high\",\n", "    np.where(\n", "        (overall_daywise_df[\"stype_\"] == \"top\") & (overall_daywise_df[\"winter_flag\"] == 1),\n", "        overall_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].mean(axis=1),\n", "        overall_daywise_df[[\"ext_qty_exp\", \"ext_qty_para\"]].mean(axis=1),\n", "    ),\n", "    0,\n", ")\n", "\n", "overall_daywise_df[\"medium_ext_qty\"] = np.where(\n", "    overall_daywise_df[\"ftype_\"] == \"medium\",\n", "    np.where(\n", "        overall_daywise_df[\"stype_\"] == \"bottom\",\n", "        np.where(\n", "            overall_daywise_df[\"winter_flag\"] == 1,\n", "            overall_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].mean(axis=1),\n", "            overall_daywise_df[[\"ext_qty_li\", \"ext_qty_para\", \"ext_qty_exp\"]].median(axis=1),\n", "        ),\n", "        np.where(\n", "            overall_daywise_df[\"stype_\"] == \"premium\",\n", "            overall_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "            np.where(\n", "                overall_daywise_df[\"wt_avail\"] >= 0.7,\n", "                overall_daywise_df[[\"ext_qty_para\", \"ext_qty_exp\"]].mean(axis=1),\n", "                overall_daywise_df[[\"ext_qty_exp\", \"ext_qty_li\", \"ext_qty_para\"]].mean(axis=1),\n", "            ),\n", "        ),\n", "    ),\n", "    0,\n", ")\n", "\n", "overall_daywise_df[\"low_ext_qty\"] = np.where(\n", "    overall_daywise_df[\"ftype_\"] == \"low\",\n", "    np.where(\n", "        overall_daywise_df[\"stype_\"] == \"bottom\",\n", "        overall_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\"]].mean(axis=1),\n", "        np.where(\n", "            overall_daywise_df[\"stype_\"] == \"premium\",\n", "            overall_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "            overall_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].max(axis=1),\n", "        ),\n", "    ),\n", "    0,\n", ")\n", "\n", "overall_daywise_df[\"ext_qty_hybrid\"] = (\n", "    overall_daywise_df[\"high_ext_qty\"]\n", "    + overall_daywise_df[\"medium_ext_qty\"]\n", "    + overall_daywise_df[\"low_ext_qty\"]\n", ")\n", "\n", "overall_daywise_df = overall_daywise_df.drop(columns={\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"})\n", "overall_daywise_df = overall_daywise_df[overall_daywise_df[\"ext_qty_hybrid\"] > 0]\n", "overall_daywise_df.head(1)"]}, {"cell_type": "markdown", "id": "87a63c58-5d0d-446f-bee5-c5adb127ba90", "metadata": {}, "source": ["### Weights Calculation for Dates"]}, {"cell_type": "markdown", "id": "8097f479-d255-4ff1-ba47-7d11b2f208d5", "metadata": {}, "source": ["#### High & Low OPD Stores"]}, {"cell_type": "code", "execution_count": null, "id": "3c2c80e2-6b3c-44aa-a99a-d61e334885b6", "metadata": {}, "outputs": [], "source": ["date_weights_df_1 = overall_daywise_df[\n", "    (overall_daywise_df[\"ftype_\"].isin({\"high\", \"medium\"}))\n", "    & (overall_daywise_df[\"ext_qty_hybrid\"] != 0)\n", "][[\"item_id\", \"facility_id\", \"date_\", \"rank\"]]\n", "\n", "date_weights_df_1[\"wr\"] = date_weights_df_1.groupby([\"item_id\", \"facility_id\"])[\"rank\"].rank(\n", "    method=\"dense\", ascending=True\n", ")\n", "\n", "wr_agg = (\n", "    date_weights_df_1.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"total_rank\"})\n", ")\n", "\n", "wr_max = (\n", "    date_weights_df_1.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"max\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"max_rank\"})\n", ")\n", "\n", "date_weights_df_1 = date_weights_df_1.merge(wr_agg, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "date_weights_df_1 = date_weights_df_1.merge(wr_max, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "date_weights_df_1[\"weights\"] = (\n", "    date_weights_df_1[\"max_rank\"] - date_weights_df_1[\"wr\"] + 1\n", ") / date_weights_df_1[\"total_rank\"]\n", "\n", "date_weights_df_1 = date_weights_df_1.drop(columns={\"max_rank\", \"wr\", \"total_rank\", \"rank\"})"]}, {"cell_type": "markdown", "id": "a64cafea-d026-455d-9fe7-5c36ea3e6eb3", "metadata": {}, "source": ["#### Low OPD Stores"]}, {"cell_type": "code", "execution_count": null, "id": "7c595b0f-40eb-4ba0-a9b4-035cc7f4f471", "metadata": {}, "outputs": [], "source": ["date_weights_df_2 = overall_daywise_df[\n", "    (overall_daywise_df[\"ftype_\"] == \"low\") & ((overall_daywise_df[\"ext_qty_hybrid\"] != 0))\n", "][[\"item_id\", \"facility_id\", \"date_\", \"ext_qty_hybrid\"]]\n", "\n", "date_weights_df_2[\"wr\"] = date_weights_df_2.groupby([\"item_id\", \"facility_id\"])[\n", "    \"ext_qty_hybrid\"\n", "].rank(method=\"dense\", ascending=True)\n", "\n", "wr_agg = (\n", "    date_weights_df_2.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"total_rank\"})\n", ")\n", "\n", "wr_max = (\n", "    date_weights_df_2.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"max\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"max_rank\"})\n", ")\n", "\n", "date_weights_df_2 = date_weights_df_2.merge(wr_agg, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "date_weights_df_2 = date_weights_df_2.merge(wr_max, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "date_weights_df_2[\"weights\"] = date_weights_df_2[\"wr\"] / date_weights_df_2[\"total_rank\"]\n", "\n", "date_weights_df_2 = date_weights_df_2.drop(\n", "    columns={\"max_rank\", \"wr\", \"total_rank\", \"ext_qty_hybrid\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a974870e-e449-41fe-8722-4c5f35491d62", "metadata": {}, "outputs": [], "source": ["date_weights_df = pd.concat([date_weights_df_1, date_weights_df_2])\n", "date_weights_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "556da37f-7455-4a1b-ae2d-716d5f29fcde", "metadata": {}, "outputs": [], "source": ["del [date_weights_df_1, date_weights_df_2, wr_agg, wr_max]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "3f99473c-dbe9-4058-8350-5e61fe532437", "metadata": {}, "source": ["### Weights Merge with DataFrame"]}, {"cell_type": "code", "execution_count": null, "id": "9bcdc922-74ac-4555-84ae-855be36d6cce", "metadata": {}, "outputs": [], "source": ["overall_daywise_df = (\n", "    overall_daywise_df.merge(date_weights_df, on=[\"item_id\", \"facility_id\", \"date_\"], how=\"inner\")\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "overall_daywise_df[\"transfer_ext_qty\"] = (\n", "    overall_daywise_df[\"ext_qty_hybrid\"] * overall_daywise_df[\"weights\"]\n", ")\n", "\n", "# overall_daywise_df[\"date_dow\"] = np.where(\n", "#     overall_daywise_df[\"dow\"].isin({0, 1, 2, 3, 4}),\n", "#     \"weekday\",\n", "#     np.where(overall_daywise_df[\"dow\"] == 5, \"saturday\", \"sunday\"),\n", "# )\n", "\n", "# overall_daywise_df[\"transfer_dow\"] = np.where(\n", "#     overall_daywise_df[\"tdow\"].isin({0, 1, 2, 3, 4}),\n", "#     \"weekday\",\n", "#     np.where(overall_daywise_df[\"tdow\"] == 5, \"saturday\", \"sunday\"),\n", "# )\n", "\n", "overall_daywise_df.head(1)"]}, {"cell_type": "markdown", "id": "4686ee22-02cd-466c-b0c8-53ae01c82fe8", "metadata": {}, "source": ["### Apply Weekday - Weekend Normalization"]}, {"cell_type": "code", "execution_count": null, "id": "daf7c769-3d69-4e80-a62f-0d6c306415cb", "metadata": {}, "outputs": [], "source": ["tot_transfer_df = (\n", "    overall_daywise_df.groupby(\n", "        [\n", "            \"city\",\n", "            \"be_facility_id\",\n", "            \"facility_id\",\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"stype_\",\n", "            \"ftype_\",\n", "            \"dow\",\n", "            \"tdate\",\n", "            \"tdow\",\n", "        ]\n", "    )\n", "    .agg({\"transfer_ext_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "tot_transfer_df = tot_transfer_df.merge(\n", "    increment_df_calc, on=[\"facility_id\", \"dow\", \"tdow\"], how=\"left\"\n", ")\n", "\n", "\n", "tot_transfer_df[\"bf\"] = np.where(tot_transfer_df[\"bf\"] > 1.2, 1.2, tot_transfer_df[\"bf\"])\n", "\n", "tot_transfer_df[\"bf\"] = np.where(tot_transfer_df[\"bf\"] < 0.8, 0.8, tot_transfer_df[\"bf\"])\n", "\n", "tot_transfer_df[\"tot_transfer_qty\"] = np.round(\n", "    tot_transfer_df[\"transfer_ext_qty\"] * tot_transfer_df[\"bf\"], 0\n", ")\n", "\n", "tot_transfer_df = (\n", "    tot_transfer_df.groupby(\n", "        [\n", "            \"city\",\n", "            \"be_facility_id\",\n", "            \"facility_id\",\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"stype_\",\n", "            \"ftype_\",\n", "            \"tdate\",\n", "        ]\n", "    )\n", "    .agg({\"tot_transfer_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "tot_transfer_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "e77fbae7-e1b1-43ab-b612-b9392c369f1c", "metadata": {}, "outputs": [], "source": ["del [ranking_df, assortment_base_df, date_weights_df, assortment_final_df]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "882d3277-2318-4f57-8bb7-ee5d3c7dd4ec", "metadata": {}, "source": ["## Top SKU Availability Feedback for Winter Stores"]}, {"cell_type": "code", "execution_count": null, "id": "36410fa0-dac0-4488-a5f1-12070023ca3a", "metadata": {"tags": []}, "outputs": [], "source": ["try:\n", "    feedback_input_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::city-feedback-input\",\n", "    )\n", "\n", "except:\n", "    feedback_input_df = pd.read_csv(\"config__city-feedback-input.csv\")\n", "\n", "feedback_input_df[\"feedback_flag\"] = np.where(feedback_input_df[\"feedback\"] == \"no\", 0, 1)\n", "\n", "feedback_input_df = feedback_input_df.drop(columns={\"feedback\"})"]}, {"cell_type": "code", "execution_count": null, "id": "517a7d51-66b1-411c-84c7-5b2865ee86dc", "metadata": {}, "outputs": [], "source": ["l2_date = pd.to_datetime(today_date) - <PERSON><PERSON><PERSON>(days=2)\n", "\n", "top_sku_feedback_df = assortment_sales_df[\n", "    (assortment_sales_df[\"stype_\"] == \"top\") & (assortment_sales_df[\"hour_\"] > 19)\n", "]\n", "\n", "top_sku_feedback_df = (\n", "    top_sku_feedback_df[\n", "        (top_sku_feedback_df[\"date_\"] >= l2_date) & (top_sku_feedback_df[\"date_\"] < today_date)\n", "    ]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "top_sku_feedback_df = top_sku_feedback_df.merge(feedback_input_df, on=[\"city\"], how=\"left\")\n", "\n", "top_sku_feedback_df[\"feedback_flag\"] = top_sku_feedback_df[\"feedback_flag\"].fillna(0)\n", "\n", "top_sku_feedback_df = (\n", "    top_sku_feedback_df[top_sku_feedback_df[\"feedback_flag\"] == 1]\n", "    .groupby([\"facility_id\", \"item_id\"])\n", "    .agg({\"hour_\": \"count\", \"is_available\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "top_sku_feedback_df[\"avail\"] = top_sku_feedback_df[\"is_available\"] / top_sku_feedback_df[\"hour_\"]\n", "\n", "top_sku_feedback_df[\"flag\"] = np.where(top_sku_feedback_df[\"avail\"] < 0.25, 1.05, 1)\n", "\n", "top_sku_feedback_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "12626faa-1ce2-4f06-ad31-9bffc11f48ef", "metadata": {}, "outputs": [], "source": ["tot_transfer_df = tot_transfer_df.merge(\n", "    top_sku_feedback_df.drop(columns={\"hour_\", \"is_available\", \"avail\"}),\n", "    on=[\"facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "tot_transfer_df[\"flag\"] = tot_transfer_df[\"flag\"].fillna(1)\n", "\n", "tot_transfer_df[\"tot_transfer_qty\"] = np.ceil(\n", "    tot_transfer_df[\"tot_transfer_qty\"] * tot_transfer_df[\"flag\"]\n", ").astype(int)\n", "\n", "tot_transfer_df = tot_transfer_df.drop(columns={\"flag\"})\n", "\n", "tot_transfer_df.head(1)"]}, {"cell_type": "markdown", "id": "34918d13-be66-4ec0-a712-f27c476e8cec", "metadata": {"tags": []}, "source": ["# Merge All DataFrames with Assortment Base"]}, {"cell_type": "code", "execution_count": null, "id": "9d72c3b5-6e85-4be3-85de-e71f5b5f2c94", "metadata": {}, "outputs": [], "source": ["final_df = (\n", "    assortment_df[[\"city\", \"facility_id\", \"outlet_id\", \"item_id\", \"be_facility_id\", \"tdate\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "final_df = final_df.merge(\n", "    transfer_df[[\"facility_id\", \"ftype_\", \"item_id\", \"stype_\", \"tdate\", \"slota_transfer_qty\"]],\n", "    on=[\"facility_id\", \"item_id\", \"tdate\"],\n", "    how=\"left\",\n", ")\n", "\n", "final_df = final_df.merge(\n", "    tot_transfer_df[[\"facility_id\", \"item_id\", \"tdate\", \"tot_transfer_qty\"]],\n", "    on=[\"facility_id\", \"item_id\", \"tdate\"],\n", "    how=\"left\",\n", ")\n", "\n", "final_df[\"tot_transfer_qty\"] = np.where(\n", "    final_df[\"tot_transfer_qty\"].isna(),\n", "    final_df[\"slota_transfer_qty\"],\n", "    final_df[\"tot_transfer_qty\"],\n", ")\n", "\n", "final_df[\"tot_transfer_qty\"] = final_df[[\"tot_transfer_qty\", \"slota_transfer_qty\"]].max(axis=1)\n", "\n", "final_df[\"slotb_transfer_qty\"] = np.abs(\n", "    final_df[\"tot_transfer_qty\"] - final_df[\"slota_transfer_qty\"]\n", ")\n", "\n", "final_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "acccf53e-c7cc-4546-a0ea-71d75b9b1822", "metadata": {}, "outputs": [], "source": ["final_df.slota_transfer_qty.sum(), final_df.tot_transfer_qty.sum()"]}, {"cell_type": "markdown", "id": "c154da65-2bf8-4e5c-bb41-e65565e6b9f0", "metadata": {}, "source": ["# DS Model Plugin"]}, {"cell_type": "code", "execution_count": null, "id": "241f2f81-c145-49b1-ae04-d5846d042ed3", "metadata": {}, "outputs": [], "source": ["ds_query = f\"\"\"\n", "    SELECT  DATE(current_replenishment_ts_ist) AS tdate, facility_id, item_id, AVG(max_qty) AS ds_qty\n", "    FROM ds_etls.demand_forecast_item_min_max_quantity_milk\n", "    WHERE updated_at_ist >= current_date - interval '10' day\n", "    GROUP BY 1,2,3\n", "\"\"\"\n", "\n", "ds_input_df = read_sql_query(ds_query, trino)\n", "\n", "ds_input_df[\"tdate\"] = pd.to_datetime(ds_input_df[\"tdate\"])\n", "\n", "ds_input_df[\"ds_qty\"] = ds_input_df[\"ds_qty\"].fillna(0).astype(int)\n", "\n", "ds_input_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "a4cedd9e-b126-4411-9222-0c1a92894612", "metadata": {"tags": []}, "outputs": [], "source": ["ds_be_df = (\n", "    final_df[[\"be_facility_id\", \"facility_id\", \"item_id\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "ds_input_df = pd.merge(ds_be_df, ds_input_df, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "\n", "try:\n", "    ds_model_be = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::ds-model-input-transfer\",\n", "    )\n", "\n", "except:\n", "    ds_model_be = pd.read_csv(\"config__ds-model-input.csv\")\n", "\n", "ds_model_be = ds_model_be.rename(columns={\"backend_facility_id\": \"be_facility_id\"}).drop(\n", "    columns={\"backend_name\"}\n", ")\n", "\n", "ds_model_be[\"be_facility_id\"] = ds_model_be[\"be_facility_id\"].astype(int)\n", "\n", "ds_input_df = pd.merge(ds_input_df, ds_model_be, on=[\"be_facility_id\"], how=\"inner\")\n", "\n", "ds_input_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "93133206-67e8-4852-a9b9-4313dea4a3af", "metadata": {}, "outputs": [], "source": ["final_df = pd.merge(\n", "    final_df,\n", "    ds_input_df[[\"tdate\", \"facility_id\", \"item_id\", \"ds_qty\"]],\n", "    on=[\"tdate\", \"facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "final_df[\"tot_transfer_qty\"] = np.where(\n", "    final_df[\"ds_qty\"].isna(), final_df[\"tot_transfer_qty\"], final_df[\"ds_qty\"]\n", ")\n", "\n", "final_df[[\"tot_transfer_qty\"]] = final_df[[\"tot_transfer_qty\"]].fillna(0).astype(int)\n", "\n", "final_df[\"ds_f\"] = np.where(final_df[\"ds_qty\"].isna(), 0, 1)\n", "\n", "final_df = final_df.drop(columns={\"ds_qty\"})\n", "\n", "final_df[\"slota_transfer_qty\"] = np.where(\n", "    final_df[\"ds_f\"] == 0,\n", "    final_df[\"slota_transfer_qty\"],\n", "    np.round(final_df[\"tot_transfer_qty\"] * 0.85),\n", ").astype(int)\n", "\n", "final_df[\"slotb_transfer_qty\"] = final_df[\"tot_transfer_qty\"] - final_df[\"slota_transfer_qty\"]\n", "final_df = final_df[final_df[\"slota_transfer_qty\"] > 0]\n", "final_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "a0c53253-f47d-4627-982e-06ffa1ee8821", "metadata": {}, "outputs": [], "source": ["final_df.slota_transfer_qty.sum(), final_df.tot_transfer_qty.sum()"]}, {"cell_type": "code", "execution_count": null, "id": "f63571d7-c55a-4c1c-810f-6bc776a05293", "metadata": {"tags": []}, "outputs": [], "source": ["del [ds_be_df, ds_model_be]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "7c103a53-2d01-4266-bcc2-f17b078dbd20", "metadata": {}, "source": ["# New Store Plugin"]}, {"cell_type": "code", "execution_count": null, "id": "e79142ba-9572-4725-8098-5afe8e52d2f2", "metadata": {}, "outputs": [], "source": ["new_store_forecast_query = f\"\"\"\n", "   WITH base AS (\n", "        SELECT facility_id, item_id, outlet_id, date_, \n", "        CASE WHEN item_nearby_fps IS NULL THEN 0 ELSE item_nearby_fps END AS item_nearby_fps,\n", "        CASE WHEN item_recent_fps IS NULL THEN 0 ELSE item_recent_fps END AS item_recent_fps, \n", "        CASE WHEN item_avail_fps IS NULL THEN 0 ELSE item_avail_fps END AS item_avail_fps\n", "        FROM (\n", "            SELECT a.* FROM supply_etls.new_store_forecast_logs a\n", "            JOIN (\n", "                SELECT facility_id, item_id, date_, MAX(updated_at) AS updated_at\n", "                FROM supply_etls.new_store_forecast_logs\n", "                GROUP BY 1,2,3\n", "            ) b ON a.facility_id = b.facility_id AND a.item_id = b.item_id AND a.date_ = b.date_ AND a.updated_at = b.updated_at\n", "            WHERE a.category IN ('Milk') AND DATE(a.date_) IN (DATE('{today_date}') + interval '1' day)\n", "        )\n", "    ),\n", "\n", "    min_max_base AS (\n", "        SELECT facility_id, outlet_id, item_id, DATE(date_) AS tdate, item_nearby_fps, item_recent_fps, item_avail_fps\n", "        FROM base\n", "    ),\n", "    \n", "    store_age_base AS (\n", "        WITH first_outbound AS (\n", "            SELECT outlet_id, DATE(od.cart_checkout_ts_ist) AS f_date\n", "            FROM dwh.fact_sales_order_details od \n", "            WHERE od.order_create_dt_ist > DATE('{today_date}') - interval '30' day\n", "            AND od.is_internal_order = false \n", "            AND od.outlet_id IN (SELECT DISTINCT outlet_id FROM min_max_base)\n", "            AND (od.order_type NOT LIKE '%%internal%%' OR od.order_type IS NULL) AND od.order_current_status = 'DELIVERED'\n", "            GROUP BY 1,2\n", "            having  count(distinct order_id)>10\n", "        )\n", "        \n", "        SELECT outlet_id, min(f_date) as start_date, DATE_DIFF('day', min(f_date), (DATE('{today_date}') + interval '1' day)) AS age\n", "        FROM first_outbound\n", "        group by 1\n", "    ),\n", "    \n", "    be_mapping AS (\n", "        SELECT DISTINCT item_id, outlet_id, cb.facility_id AS be_facility_id\n", "        FROM rpc.item_outlet_tag_mapping tm\n", "        JOIN retail.console_outlet cb ON cb.id = CAST(tag_value AS int) AND cb.active = 1 AND cb.lake_active_record\n", "        WHERE tm.active = 1 AND tm.tag_type_id = 8 AND tm.lake_active_record\n", "    )\n", "\n", "    SELECT a.*, start_date, be_facility_id, cl.name AS city,\n", "    CASE WHEN b.age IS NULL THEN 0 ELSE age END AS age\n", "    FROM min_max_base a\n", "    LEFT JOIN store_age_base b ON a.outlet_id = b.outlet_id\n", "    JOIN be_mapping bm ON bm.item_id = a.item_id AND bm.outlet_id = a.outlet_id\n", "    JOIN retail.console_outlet co ON co.id = a.outlet_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "    LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id AND cl.lake_active_record\n", "    \n", "\"\"\"\n", "new_store_df = pd.read_sql_query(new_store_forecast_query, trino)\n", "new_store_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d5c3290d-0ba1-43ec-b25e-c7281dd1c3e3", "metadata": {}, "outputs": [], "source": ["new_store_df_tot = (\n", "    new_store_df.groupby([\"facility_id\"])\n", "    .agg({\"item_nearby_fps\": \"sum\", \"item_recent_fps\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_nearby_fps\": \"near\", \"item_recent_fps\": \"recent\"})\n", ")\n", "new_store_df = new_store_df.merge(new_store_df_tot, on=[\"facility_id\"], how=\"left\")\n", "new_store_df[\"final_cpd\"] = np.round(new_store_df[\"near\"] * 0.75 + new_store_df[\"recent\"] * 0.25, 0)\n", "new_store_df = new_store_df[\n", "    [\"city\", \"be_facility_id\", \"facility_id\", \"outlet_id\", \"item_id\", \"tdate\", \"age\", \"final_cpd\"]\n", "]\n", "new_store_df.tail()"]}, {"cell_type": "code", "execution_count": null, "id": "84537b9d-f386-4076-a39a-4e90701aae5c", "metadata": {}, "outputs": [], "source": ["## sister_store\n", "\n", "sis = f\"\"\"\n", "            select\n", "            distinct\n", "            zone as city,\n", "            facility_id,\n", "            sister_store_facility_id as s_fac\n", "            \n", "            from\n", "            \n", "            supply_etls.e3_new_darkstores\n", "            where final_ob_date >= current_date - interval '30' day\n", "            and sister_store_facility_id <>0\n", "\"\"\"\n", "sis_df = pd.read_sql_query(sis, trino)\n", "\n", "sis_store_list = tuple(list(sis_df[\"facility_id\"].unique()) + ([-1, -2]))\n", "len(sis_store_list)"]}, {"cell_type": "markdown", "id": "804c4406-68da-4b8d-a294-36797a425f3b", "metadata": {"tags": []}, "source": ["### BE item conrti"]}, {"cell_type": "code", "execution_count": null, "id": "c28f56b1-84f4-49fb-9c21-167056a50ac9", "metadata": {}, "outputs": [], "source": ["contri_base = overall_daywise_df[\n", "    [\"city\", \"be_facility_id\", \"facility_id\", \"item_id\", \"quantity\"]\n", "].copy()\n", "\n", "be_contri = (\n", "    overall_daywise_df.groupby([\"be_facility_id\", \"item_id\"]).agg({\"quantity\": \"sum\"}).reset_index()\n", ")\n", "\n", "be_agri = (\n", "    be_contri.groupby([\"be_facility_id\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"quantity\": \"tot_quan\"})\n", ")\n", "\n", "be_contri = be_contri.merge(be_agri, on=[\"be_facility_id\"], how=\"left\")\n", "be_contri[\"be_share\"] = be_contri[\"quantity\"] / be_contri[\"tot_quan\"]\n", "be_contri.drop(columns={\"quantity\", \"tot_quan\"}, inplace=True)\n", "be_contri.head(10)"]}, {"cell_type": "markdown", "id": "d987f6fe-d3eb-4ade-91c2-429ddce52d7d", "metadata": {"tags": []}, "source": ["### City item conrti"]}, {"cell_type": "code", "execution_count": null, "id": "f6545c04-634b-4c8b-ba22-f5ab915728fc", "metadata": {}, "outputs": [], "source": ["city_contri = overall_daywise_df.groupby([\"city\", \"item_id\"]).agg({\"quantity\": \"sum\"}).reset_index()\n", "\n", "city_agri = (\n", "    city_contri.groupby([\"city\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"quantity\": \"tot_quan\"})\n", ")\n", "\n", "city_contri = city_contri.merge(city_agri, on=[\"city\"], how=\"left\")\n", "city_contri[\"city_share\"] = city_contri[\"quantity\"] / city_contri[\"tot_quan\"]\n", "city_contri.drop(columns={\"quantity\", \"tot_quan\"}, inplace=True)\n", "city_contri.head(7)"]}, {"cell_type": "markdown", "id": "3387994a-2381-4fa0-84ac-e945c7864588", "metadata": {"tags": []}, "source": ["### Sister item conrti"]}, {"cell_type": "code", "execution_count": null, "id": "e2030cfd-a729-41d9-889c-0bd9f47b7db5", "metadata": {}, "outputs": [], "source": ["store_contri = (\n", "    overall_daywise_df[overall_daywise_df[\"facility_id\"].isin(sis_store_list)]\n", "    .groupby([\"facility_id\", \"item_id\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"facility_id\": \"s_fac\"})\n", ")\n", "\n", "store_agri = (\n", "    store_contri.groupby([\"s_fac\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"quantity\": \"tot_quan\"})\n", ")\n", "\n", "store_contri = store_contri.merge(store_agri, on=[\"s_fac\"], how=\"left\")\n", "store_contri[\"fac_share\"] = store_contri[\"quantity\"] / store_contri[\"tot_quan\"]\n", "store_contri = store_contri.merge(sis_df[[\"s_fac\", \"facility_id\"]], on=[\"s_fac\"], how=\"left\")\n", "store_contri = store_contri.dropna()\n", "store_contri.drop(columns={\"s_fac\", \"quantity\", \"tot_quan\"}, inplace=True)\n", "store_contri.head(7)"]}, {"cell_type": "code", "execution_count": null, "id": "17164836-82ef-471e-b518-70c85c3b6e79", "metadata": {}, "outputs": [], "source": ["new_df = new_store_df.merge(be_contri, on=[\"be_facility_id\", \"item_id\"], how=\"left\")\n", "new_df = new_df.merge(city_contri, on=[\"city\", \"item_id\"], how=\"left\")\n", "new_df = new_df.merge(store_contri, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "new_df = new_df.fillna(0)\n", "new_df[\"final_share\"] = np.where(\n", "    new_df[\"fac_share\"] != 0,\n", "    new_df[\"fac_share\"],\n", "    np.where(new_df[\"city_share\"] != 0, new_df[\"city_share\"], new_df[\"be_share\"]),\n", ")\n", "new_df[\"tot_transfer_qty\"] = np.ceil(new_df[\"final_cpd\"] * new_df[\"final_share\"]).clip(1, 1000)\n", "new_df.tail()"]}, {"cell_type": "code", "execution_count": null, "id": "b58d4a39-58a0-475f-811e-d385c4003d92", "metadata": {"tags": []}, "outputs": [], "source": ["new_store_df = (\n", "    new_df[new_df[\"age\"] <= 10][\n", "        [\n", "            \"city\",\n", "            \"outlet_id\",\n", "            \"be_facility_id\",\n", "            \"tdate\",\n", "            \"item_id\",\n", "            \"facility_id\",\n", "            \"tot_transfer_qty\",\n", "        ]\n", "    ]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "new_store_df[\"slota_transfer_qty\"] = np.ceil(0.85 * new_store_df[\"tot_transfer_qty\"]).astype(int)\n", "\n", "new_store_df[\"slotb_transfer_qty\"] = np.where(\n", "    new_store_df[\"tot_transfer_qty\"] - new_store_df[\"slota_transfer_qty\"] < 0,\n", "    0,\n", "    new_store_df[\"tot_transfer_qty\"] - new_store_df[\"slota_transfer_qty\"],\n", ")\n", "\n", "new_store_df[\"stype_\"] = \"high\"\n", "\n", "new_store_df[\"ftype_\"] = \"medium\"\n", "\n", "new_store_df[\"ds_f\"] = 0\n", "\n", "new_store_df = (\n", "    new_store_df[\n", "        [\n", "            \"city\",\n", "            \"facility_id\",\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"be_facility_id\",\n", "            \"tdate\",\n", "            \"ftype_\",\n", "            \"stype_\",\n", "            \"slota_transfer_qty\",\n", "            \"tot_transfer_qty\",\n", "            \"slotb_transfer_qty\",\n", "            \"ds_f\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "new_store_df[\"tdate\"] = pd.to_datetime(new_store_df[\"tdate\"])"]}, {"cell_type": "code", "execution_count": null, "id": "06c7f506-48e0-46e3-800c-a7f092e358c6", "metadata": {}, "outputs": [], "source": ["# new_store_df[new_store_df.facility_id == 3086].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "28b5a7e4-1233-4a3f-a590-78d89d9af615", "metadata": {}, "outputs": [], "source": ["data = final_df[[\"facility_id\", \"item_id\"]].drop_duplicates().reset_index().drop(columns={\"index\"})\n", "\n", "data = pd.merge(\n", "    data,\n", "    new_store_df[[\"facility_id\", \"item_id\", \"tot_transfer_qty\"]].drop_duplicates(),\n", "    on=[\"facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "data = (\n", "    data[data[\"tot_transfer_qty\"].isna()].reset_index().drop(columns={\"index\", \"tot_transfer_qty\"})\n", ")\n", "\n", "final_df = pd.merge(final_df, data, on=[\"item_id\", \"facility_id\"], how=\"inner\")\n", "\n", "final_df = pd.concat([final_df, new_store_df])\n", "\n", "final_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "8d23cccb-1521-4baf-850b-7425e033c6b2", "metadata": {}, "outputs": [], "source": ["final_df.slota_transfer_qty.sum(), final_df.tot_transfer_qty.sum()"]}, {"cell_type": "code", "execution_count": null, "id": "f455f790-0792-4c01-8ea4-0da4def66c51", "metadata": {}, "outputs": [], "source": ["final_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b7d05320-0ac5-469b-b80f-3ed6b078692b", "metadata": {}, "outputs": [], "source": ["final_df[\"si_factor\"] = 1\n", "final_df[\"factor\"] = 1"]}, {"cell_type": "raw", "id": "ce4e9221-be19-4611-8877-fd69d20b5c08", "metadata": {}, "source": ["bump_query = f\"\"\"with base as ( \n", "        select \n", "        date_ist,\n", "        updated_at,\n", "        city,\n", "        be_facility_id,\n", "        facility_id,\n", "        item_id ,\n", "        be_f,\n", "        city_f,\n", "        store_f,\n", "        rank() over (partition by date_ist order by updated_at desc) as rk\n", "        from \n", "        supply_etls.milk_ordering_bump_factors\n", "        where date_ist>= current_date - interval '3' day\n", "\n", "    )\n", "    \n", "        select\n", "        date_ist + interval '2' day as tdate,\n", "        be_facility_id,\n", "        item_id ,\n", "        facility_id,\n", "        be_f,\n", "        city_f,\n", "        store_f\n", "        from base\n", "        where rk = 1\"\"\"\n", "\n", "bump_df = pd.read_sql_query(bump_query, trino)\n", "\n", "bump_df[\"tdate\"] = pd.to_datetime(bump_df[\"tdate\"])\n", "bump_df.shape"]}, {"cell_type": "raw", "id": "539e3cd7-30b0-4f50-bd60-0c270514c21e", "metadata": {}, "source": ["final_df.head()"]}, {"cell_type": "raw", "id": "0c24cc35-4a2d-446d-9dd8-656c3ce2e9d4", "metadata": {}, "source": ["final_df = final_df.merge(\n", "    bump_df[[\"tdate\", \"item_id\", \"facility_id\", \"be_f\", \"city_f\", \"store_f\"]],\n", "    on=[\"item_id\", \"facility_id\", \"tdate\"],\n", "    how=\"left\",\n", ")\n", "final_df[[\"be_f\", \"city_f\", \"store_f\"]] = final_df[\n", "    [\"be_f\", \"city_f\", \"store_f\"]\n", "].fillna(1)\n", "final_df[\"slota_transfer_qty\"] = (\n", "    final_df[\"slota_transfer_qty\"]\n", "    * final_df[\"be_f\"]\n", "    * final_df[\"city_f\"]\n", "    * final_df[\"store_f\"]\n", ")\n", "final_df[\"tot_transfer_qty\"] = (\n", "    final_df[\"tot_transfer_qty\"]\n", "    * final_df[\"be_f\"]\n", "    * final_df[\"city_f\"]\n", "    * final_df[\"store_f\"]\n", ")\n", "final_df[\"slotb_transfer_qty\"] = (\n", "    final_df[\"slotb_transfer_qty\"]\n", "    * final_df[\"be_f\"]\n", "    * final_df[\"city_f\"]\n", "    * final_df[\"store_f\"]\n", ")\n", "\n", "\n", "final_df.drop(columns={\"be_f\", \"city_f\", \"store_f\"}, inplace=True)\n", "final_df.head()"]}, {"cell_type": "markdown", "id": "f7eadd8b-b69f-406c-8f90-8fc82bf5e742", "metadata": {}, "source": ["# Manual Inputs"]}, {"cell_type": "markdown", "id": "75f973d3-db5a-4084-ac05-044cf4358d84", "metadata": {}, "source": ["## Demand Replication"]}, {"cell_type": "raw", "id": "36492645-242b-4476-82ca-d285ff839d12", "metadata": {"tags": []}, "source": ["try:\n", "    demand_rep_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::demand-replication\",\n", "    ).rename(columns={\"factor\": \"d_factor\"})\n", "\n", "except:\n", "    demand_rep_df = pd.read_csv(\"config__demand-replication.csv\").rename(\n", "        columns={\"factor\": \"d_factor\"}\n", "    )\n", "\n", "demand_rep_df[\"new_facility_id\"] = demand_rep_df[\"new_facility_id\"].fillna(0).astype(int)\n", "\n", "demand_rep_df[\"nearby_facility_id\"] = demand_rep_df[\"nearby_facility_id\"].fillna(0).astype(int)\n", "\n", "demand_rep_df[\"start_date\"] = pd.to_datetime(demand_rep_df[\"start_date\"])\n", "demand_rep_df[\"end_date\"] = pd.to_datetime(demand_rep_df[\"end_date\"])\n", "\n", "demand_rep_df[\"tdate\"] = list(\n", "    map(\n", "        lambda x, y: pd.date_range(start=x, end=y),\n", "        demand_rep_df[\"start_date\"],\n", "        demand_rep_df[\"end_date\"],\n", "    )\n", ")\n", "\n", "demand_rep_df = demand_rep_df.explode(\"tdate\").drop([\"start_date\", \"end_date\"], axis=1)\n", "\n", "demand_rep_df[\"tdate\"] = pd.to_datetime(demand_rep_df[\"tdate\"])\n", "\n", "demand_rep_df = demand_rep_df.reset_index()\n", "\n", "demand_rep_max = (\n", "    demand_rep_df.groupby([\"new_facility_id\", \"nearby_facility_id\", \"tdate\"])\n", "    .agg({\"index\": \"max\"})\n", "    .reset_index()\n", ")\n", "\n", "demand_rep_df = demand_rep_df.merge(\n", "    demand_rep_max,\n", "    on=[\"new_facility_id\", \"nearby_facility_id\", \"tdate\", \"index\"],\n", "    how=\"inner\",\n", ").drop(columns={\"index\"})\n", "\n", "nearby_facility_list = list(demand_rep_df[\"nearby_facility_id\"].unique())\n", "\n", "demand_rep_df = (\n", "    demand_rep_df[demand_rep_df[\"tdate\"] >= today_date].reset_index().drop(columns={\"index\"})\n", ")\n", "\n", "demand_rep_df.head(1)"]}, {"cell_type": "raw", "id": "db82984a-390e-4c2b-98d1-94d08caadb40", "metadata": {}, "source": ["nearby_df = final_df[final_df[\"facility_id\"].isin(nearby_facility_list)][\n", "    [\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"tdate\",\n", "        \"slota_transfer_qty\",\n", "        \"tot_transfer_qty\",\n", "        \"slotb_transfer_qty\",\n", "    ]\n", "]\n", "\n", "nearby_df = nearby_df.rename(columns={\"facility_id\": \"nearby_facility_id\"})\n", "\n", "demand_rep_df = demand_rep_df.merge(nearby_df, on=[\"nearby_facility_id\", \"tdate\"], how=\"inner\")\n", "\n", "demand_rep_df[\"flag\"] = 1\n", "\n", "demand_rep_df[\"slota\"] = demand_rep_df[\"slota_transfer_qty\"] * demand_rep_df[\"d_factor\"]\n", "\n", "demand_rep_df[\"tot\"] = demand_rep_df[\"tot_transfer_qty\"] * demand_rep_df[\"d_factor\"]\n", "\n", "demand_rep_df[\"slotb\"] = demand_rep_df[\"slotb_transfer_qty\"] * demand_rep_df[\"d_factor\"]\n", "\n", "demand_rep_df = demand_rep_df[[\"new_facility_id\", \"item_id\", \"slota\", \"tot\", \"slotb\"]]\n", "\n", "demand_rep_df = demand_rep_df.rename(columns={\"new_facility_id\": \"facility_id\"})\n", "\n", "demand_rep_df.head(1)"]}, {"cell_type": "raw", "id": "aa814aaa-1f68-41d6-96ca-ab29aa2d64c8", "metadata": {}, "source": ["final_df = final_df.merge(demand_rep_df, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "final_df[\"slota_transfer_qty\"] = np.where(\n", "    final_df[\"slota\"].isna(), final_df[\"slota_transfer_qty\"], final_df[\"slota\"]\n", ")\n", "\n", "final_df[\"tot_transfer_qty\"] = np.where(\n", "    final_df[\"tot\"].isna(), final_df[\"tot_transfer_qty\"], final_df[\"tot\"]\n", ")\n", "\n", "final_df[\"slotb_transfer_qty\"] = np.where(\n", "    final_df[\"slotb\"].isna(), final_df[\"slotb_transfer_qty\"], final_df[\"slotb\"]\n", ")\n", "\n", "final_df = final_df.drop(columns={\"slota\", \"slotb\", \"tot\"})\n", "\n", "final_df.head(1)"]}, {"cell_type": "markdown", "id": "dd553f9d-563d-4f78-8c3f-f1037389ff0c", "metadata": {}, "source": ["## Store Correction"]}, {"cell_type": "raw", "id": "64704b9f-e020-4b5b-bf2c-44b085dbc29b", "metadata": {}, "source": ["try:\n", "    store_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::store-input\",\n", "    )\n", "\n", "except:\n", "    store_df = pd.read_csv(\"store_input.csv\")\n", "\n", "store_df[\"facility_id\"] = store_df[\"facility_id\"].fillna(0).astype(int)\n", "\n", "store_df[\"start_date\"] = pd.to_datetime(store_df[\"start_date\"])\n", "store_df[\"end_date\"] = pd.to_datetime(store_df[\"end_date\"])\n", "\n", "store_df[\"factor\"] = store_df[\"factor\"].astype(float)\n", "\n", "store_df[\"tdate\"] = list(\n", "    map(\n", "        lambda x, y: pd.date_range(start=x, end=y),\n", "        store_df[\"start_date\"],\n", "        store_df[\"end_date\"],\n", "    )\n", ")\n", "\n", "store_df = store_df.explode(\"tdate\").drop([\"start_date\", \"end_date\"], axis=1)\n", "\n", "store_df[\"tdate\"] = pd.to_datetime(store_df[\"tdate\"])\n", "\n", "store_df = store_df.reset_index()\n", "\n", "store_max = store_df.groupby([\"facility_id\", \"tdate\"]).agg({\"index\": \"max\"}).reset_index()\n", "\n", "store_df = store_df.merge(store_max, on=[\"facility_id\", \"tdate\", \"index\"], how=\"inner\").drop(\n", "    columns={\"index\"}\n", ")\n", "\n", "store_df = store_df[store_df[\"tdate\"] >= today_date].reset_index().drop(columns={\"index\"})\n", "\n", "store_df.head(1)"]}, {"cell_type": "raw", "id": "0d6bdadc-e1c5-4f01-a48f-3c041acb1832", "metadata": {}, "source": ["final_df = final_df.merge(store_df, on=[\"facility_id\", \"tdate\"], how=\"left\")\n", "\n", "final_df[\"factor\"] = final_df[\"factor\"].fillna(1)\n", "\n", "# final_df[\"slota_transfer_qty\"] = (\n", "#     np.ceil(final_df[\"slota_transfer_qty\"] * final_df[\"factor\"]).fillna(0).astype(int)\n", "# )\n", "\n", "# final_df[\"slotb_transfer_qty\"] = (\n", "#     np.ceil(final_df[\"slotb_transfer_qty\"] * final_df[\"factor\"]).fillna(0).astype(int)\n", "# )\n", "\n", "final_df.head(1)"]}, {"cell_type": "raw", "id": "f41212c2-43b4-499c-8a92-a25357418029", "metadata": {}, "source": ["## Backend Item Correction"]}, {"cell_type": "raw", "id": "f9250206-13cd-49b0-831e-82e10542c4bf", "metadata": {}, "source": ["try:\n", "    store_item_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::backend-item-input\",\n", "    ).rename(columns={\"factor\": \"si_factor\"})\n", "\n", "except:\n", "    store_item_df = pd.read_csv(\"be_input.csv\").rename(columns={\"factor\": \"si_factor\"})\n", "\n", "store_item_df[\"be_facility_id\"] = store_item_df[\"be_facility_id\"].fillna(0).astype(int)\n", "store_item_df[\"item_id\"] = store_item_df[\"item_id\"].fillna(0).astype(int)\n", "\n", "store_item_df[\"start_date\"] = pd.to_datetime(store_item_df[\"start_date\"])\n", "store_item_df[\"end_date\"] = pd.to_datetime(store_item_df[\"end_date\"])\n", "\n", "store_item_df[\"si_factor\"] = store_item_df[\"si_factor\"].astype(float)\n", "\n", "store_item_df[\"tdate\"] = list(\n", "    map(\n", "        lambda x, y: pd.date_range(start=x, end=y),\n", "        store_item_df[\"start_date\"],\n", "        store_item_df[\"end_date\"],\n", "    )\n", ")\n", "\n", "store_item_df = store_item_df.explode(\"tdate\").drop([\"start_date\", \"end_date\"], axis=1)\n", "\n", "store_item_df[\"tdate\"] = pd.to_datetime(store_item_df[\"tdate\"])\n", "\n", "store_item_df = store_item_df.reset_index()\n", "\n", "store_item_max = (\n", "    store_item_df.groupby([\"be_facility_id\", \"item_id\", \"tdate\"])\n", "    .agg({\"index\": \"max\"})\n", "    .reset_index()\n", ")\n", "\n", "store_item_df = store_item_df.merge(\n", "    store_item_max, on=[\"be_facility_id\", \"item_id\", \"tdate\", \"index\"], how=\"inner\"\n", ").drop(columns={\"index\"})\n", "\n", "store_item_df = (\n", "    store_item_df[store_item_df[\"tdate\"] >= today_date].reset_index().drop(columns={\"index\"})\n", ")\n", "\n", "store_item_df.head(1)"]}, {"cell_type": "raw", "id": "5aa4f547-e8e2-4e6a-b5d5-e7b1231f023c", "metadata": {}, "source": ["final_df = final_df.merge(store_item_df, on=[\"be_facility_id\", \"item_id\", \"tdate\"], how=\"left\")\n", "\n", "final_df[\"si_factor\"] = final_df[\"si_factor\"].fillna(1)\n", "\n", "# final_df[\"slota_transfer_qty\"] = (\n", "#     np.ceil(final_df[\"slota_transfer_qty\"] * final_df[\"si_factor\"])\n", "#     .<PERSON>na(0)\n", "#     .astype(int)\n", "# )\n", "\n", "# final_df[\"slotb_transfer_qty\"] = (\n", "#     np.ceil(final_df[\"slotb_transfer_qty\"] * final_df[\"si_factor\"])\n", "#     .<PERSON>na(0)\n", "#     .astype(int)\n", "# )\n", "\n", "final_df.head(1)"]}, {"cell_type": "markdown", "id": "63cb4d26-d7b6-4cae-8583-0b066e5bdd3c", "metadata": {"tags": []}, "source": ["# Logs"]}, {"cell_type": "markdown", "id": "b966cddc-5ad8-4629-8c42-7d0620619260", "metadata": {}, "source": ["### Corrected Forecast Logs"]}, {"cell_type": "code", "execution_count": null, "id": "d7239ff6-eb08-402d-8c7c-0ac2aad54319", "metadata": {}, "outputs": [], "source": ["working_steps_df = final_df.copy()\n", "\n", "working_steps_df[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))\n", "\n", "working_steps_df = working_steps_df[\n", "    [\n", "        \"facility_id\",\n", "        \"outlet_id\",\n", "        \"ftype_\",\n", "        \"item_id\",\n", "        \"stype_\",\n", "        \"tdate\",\n", "        \"factor\",\n", "        \"si_factor\",\n", "        \"ds_f\",\n", "        \"slota_transfer_qty\",\n", "        \"slotb_transfer_qty\",\n", "        \"updated_at\",\n", "    ]\n", "]\n", "\n", "working_steps_df[[\"slota_transfer_qty\", \"slotb_transfer_qty\"]] = (\n", "    working_steps_df[[\"slota_transfer_qty\", \"slotb_transfer_qty\"]].fillna(0).astype(int)\n", ")\n", "\n", "working_steps_df.head(1)"]}, {"cell_type": "markdown", "id": "2298c7dd-6051-4dff-a5bb-e6c8fb8bae5c", "metadata": {}, "source": ["## <PERSON> <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "id": "ec1d7ead-a237-4327-a8a2-3a6d8ca5cebf", "metadata": {}, "outputs": [], "source": ["trino_data_df = working_steps_df.copy()\n", "\n", "trino_data_df[\"date_ist\"] = pd.to_datetime(\n", "    pd.to_datetime(trino_data_df[\"updated_at\"]).dt.strftime(\"%Y-%m-%d\")\n", ")\n", "\n", "trino_data_df = trino_data_df[\n", "    [\n", "        \"facility_id\",\n", "        \"outlet_id\",\n", "        \"ftype_\",\n", "        \"item_id\",\n", "        \"stype_\",\n", "        \"tdate\",\n", "        \"factor\",\n", "        \"si_factor\",\n", "        \"ds_f\",\n", "        \"slota_transfer_qty\",\n", "        \"slotb_transfer_qty\",\n", "        \"date_ist\",\n", "        \"updated_at\",\n", "    ]\n", "]\n", "\n", "trino_data_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "ae5a3651-505e-4a6c-93b2-aa0ed6b76bf8", "metadata": {}, "outputs": [], "source": ["trino_data_df.slota_transfer_qty.sum(), trino_data_df.slotb_transfer_qty.sum()"]}, {"cell_type": "code", "execution_count": null, "id": "736211ac-78fa-4f76-ba6a-c7f897bfe802", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"facility id\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"outlet id\"},\n", "    {\"name\": \"ftype_\", \"type\": \"VARCHAR\", \"description\": \"facility categorization\"},\n", "    {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"item id\"},\n", "    {\"name\": \"stype_\", \"type\": \"VARCHAR\", \"description\": \"SKU categorization\"},\n", "    {\"name\": \"tdate\", \"type\": \"DATE\", \"description\": \"transfer date\"},\n", "    {\"name\": \"factor\", \"type\": \"DOUBLE\", \"description\": \"manual store input\"},\n", "    {\"name\": \"si_factor\", \"type\": \"DOUBLE\", \"description\": \"manual store input\"},\n", "    {\"name\": \"ds_f\", \"type\": \"DOUBLE\", \"description\": \"ds model flag\"},\n", "    {\n", "        \"name\": \"slota_transfer_qty\",\n", "        \"type\": \"INTEGER\",\n", "        \"description\": \"Morning Transfer Qty\",\n", "    },\n", "    {\n", "        \"name\": \"slotb_transfer_qty\",\n", "        \"type\": \"INTEGER\",\n", "        \"description\": \"Evening Transfer Qty\",\n", "    },\n", "    {\"name\": \"date_ist\", \"type\": \"DATE\", \"description\": \"updated_at filter\"},\n", "    {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(6)\", \"description\": \"date/time of run\"},\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"milk_indent_correction_v2\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"updated_at\", \"facility_id\", \"item_id\"],\n", "    \"partition_key\": [\"date_ist\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"append\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains milk indent correction used for transfers\",\n", "}\n", "\n", "pb.to_trino(trino_data_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "daae5491-a223-4ba3-9c4f-9c0a9460a43b", "metadata": {}, "outputs": [], "source": ["channel = \"bl-milk-indent-check\"\n", "text = \"milk indent correction table has been updated \\n cc: <@U06NUDF672P> <@U06T927GCSW> <@U05CCTXLBU1>\"\n", "pb.send_slack_message(channel=channel, text=text)"]}, {"cell_type": "code", "execution_count": null, "id": "b33d6edd-814f-4c75-84ab-eb236745a918", "metadata": {}, "outputs": [], "source": ["data = trino_data_df[[\"facility_id\", \"item_id\", \"slota_transfer_qty\"]]\n", "data[[\"facility_id\", \"item_id\", \"slota_transfer_qty\"]] = (\n", "    data[[\"facility_id\", \"item_id\", \"slota_transfer_qty\"]].round(0).astype(int)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a8fdb178-4c14-438c-b457-a5264a0cd759", "metadata": {}, "outputs": [], "source": ["data[\"max_qty\"] = data[\"slota_transfer_qty\"]\n", "data[\"min_qty\"] = data[\"max_qty\"]\n", "data[\"is_bundle\"] = 0\n", "data[\"old_bucket\"] = 1\n", "data[\"case_type\"] = \"CUSTOM\"\n", "data[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))\n", "data[\"updated_by\"] = 14\n", "data[[\"facility_id\", \"item_id\", \"min_qty\", \"max_qty\"]] = data[\n", "    [\"facility_id\", \"item_id\", \"min_qty\", \"max_qty\"]\n", "].astype(int)\n", "data.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "62c9e06e-9f9c-4ec5-8276-27cd49113915", "metadata": {}, "outputs": [], "source": ["data = data[\n", "    [\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"min_qty\",\n", "        \"max_qty\",\n", "        \"is_bundle\",\n", "        \"old_bucket\",\n", "        \"case_type\",\n", "        \"updated_at\",\n", "        \"updated_by\",\n", "    ]\n", "]\n", "data.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "34d8c678-6de3-46b5-a987-52f6a1518eec", "metadata": {}, "outputs": [], "source": ["def upload_to_table(data, maintenance_flag):\n", "\n", "    MIN_MAX_SCHEMA_NAME = \"supply_etls\"\n", "    MIN_MAX_TABLE_NAME = \"facility_item_min_max_quantity\"\n", "\n", "    kwargs = {\n", "        \"schema_name\": MIN_MAX_SCHEMA_NAME,\n", "        \"table_name\": MIN_MAX_TABLE_NAME,\n", "        \"column_dtypes\": [\n", "            {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"Facility ID\"},\n", "            {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"Item ID\"},\n", "            {\"name\": \"min_qty\", \"type\": \"INTEGER\", \"description\": \"Minimum quantity\"},\n", "            {\"name\": \"max_qty\", \"type\": \"INTEGER\", \"description\": \"Maximum quantity\"},\n", "            {\"name\": \"is_bundle\", \"type\": \"INTEGER\", \"description\": \"is bundle\"},\n", "            {\n", "                \"name\": \"old_bucket\",\n", "                \"type\": \"INTEGER\",\n", "                \"description\": \"previous day bucket\",\n", "            },\n", "            {\"name\": \"case_type\", \"type\": \"VARCHAR\", \"description\": \"case type\"},\n", "            {\n", "                \"name\": \"updated_at\",\n", "                \"type\": \"TIMESTAMP(6)\",\n", "                \"description\": \"Updated at ts\",\n", "            },\n", "            {\"name\": \"updated_by\", \"type\": \"INTEGER\", \"description\": \"Updated by\"},\n", "        ],\n", "        \"primary_key\": [\"facility_id\", \"item_id\"],\n", "        \"partition_key\": [\"facility_id\"],\n", "        \"incremental_key\": \"updated_at\",\n", "        \"force_upsert_without_increment_check\": False,\n", "        \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "        \"table_description\": \"this table contains the min max quantity for transfers against each facility item\",\n", "        \"run_maintenance\": maintenance_flag,\n", "    }\n", "    pb.to_trino(data, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "05fee4b1-73d9-4aa3-9b44-029f0de9de38", "metadata": {}, "outputs": [], "source": ["if current_hour <= 14:\n", "    upload_to_table(data, maintenance_flag=True)"]}, {"cell_type": "code", "execution_count": null, "id": "0de1c3e9-3ab7-47bc-9746-d784a3cc00ee", "metadata": {}, "outputs": [], "source": ["data_log = data.copy()\n", "\n", "data_log[\"date_ist\"] = pd.to_datetime(\n", "    pd.to_datetime(data_log[\"updated_at\"]).dt.strftime(\"%Y-%m-%d\")\n", ")\n", "\n", "data_log = data_log[\n", "    [\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"min_qty\",\n", "        \"max_qty\",\n", "        \"is_bundle\",\n", "        \"old_bucket\",\n", "        \"case_type\",\n", "        \"date_ist\",\n", "        \"updated_at\",\n", "        \"updated_by\",\n", "    ]\n", "]\n", "\n", "data_log.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "b35851ca-a39a-4773-880f-bee586ca384f", "metadata": {}, "outputs": [], "source": ["MIN_MAX_SCHEMA_NAME = \"supply_etls\"\n", "MIN_MAX_TABLE_NAME = \"facility_item_min_max_quantity_log\"\n", "\n", "kwargs = {\n", "    \"schema_name\": MIN_MAX_SCHEMA_NAME,\n", "    \"table_name\": MIN_MAX_TABLE_NAME,\n", "    \"column_dtypes\": [\n", "        {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"Facility ID\"},\n", "        {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"Item ID\"},\n", "        {\"name\": \"min_qty\", \"type\": \"INTEGER\", \"description\": \"Minimum quantity\"},\n", "        {\"name\": \"max_qty\", \"type\": \"INTEGER\", \"description\": \"Maximum quantity\"},\n", "        {\"name\": \"is_bundle\", \"type\": \"INTEGER\", \"description\": \"is bundle\"},\n", "        {\n", "            \"name\": \"old_bucket\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"previous day bucket\",\n", "        },\n", "        {\"name\": \"case_type\", \"type\": \"VARCHAR\", \"description\": \"case type\"},\n", "        {\n", "            \"name\": \"date_ist\",\n", "            \"type\": \"DATE\",\n", "            \"description\": \"updated_at filter date\",\n", "        },\n", "        {\n", "            \"name\": \"updated_at\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"Updated at ts\",\n", "        },\n", "        {\"name\": \"updated_by\", \"type\": \"INTEGER\", \"description\": \"Updated by\"},\n", "    ],\n", "    \"primary_key\": [\"facility_id\", \"item_id\"],\n", "    \"partition_key\": [\"date_ist\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"append\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains the min max quantity for transfers against each facility item\",\n", "}\n", "\n", "if current_hour <= 14:\n", "    pb.to_trino(data_log, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "8c6d53a8-90a5-4309-b193-3651a329273e", "metadata": {"tags": []}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}