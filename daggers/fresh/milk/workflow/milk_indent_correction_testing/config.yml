alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: milk_indent_correction_testing
dag_type: workflow
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: fresh
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U06T927GCSW
path: fresh/milk/workflow/milk_indent_correction_testing
paused: false
pool: fresh_pool
project_name: milk
schedule:
  end_date: '2025-09-09T00:00:00'
  interval: 33 7,13 * * *
  start_date: '2025-06-10T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
