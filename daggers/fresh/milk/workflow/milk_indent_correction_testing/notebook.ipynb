{"cells": [{"cell_type": "code", "execution_count": null, "id": "ca9092b1-758c-4322-9255-b2e902758143", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import os\n", "import time\n", "import gc\n", "import requests\n", "\n", "import calendar\n", "from datetime import datetime as dt\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.options.mode.chained_assignment = None  # default='warn'\n", "pd.set_option(\"display.max_columns\", 500)"]}, {"cell_type": "code", "execution_count": null, "id": "44e98399-917a-4b34-a8d0-8e36430da1cc", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pytz\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")\n", "\n", "current_time = datetime.now(IST)\n", "\n", "current_hour = pd.to_datetime(current_time).hour\n", "\n", "today_date = current_time.strftime(\"%Y-%m-%d\")\n", "\n", "# today_date"]}, {"cell_type": "code", "execution_count": null, "id": "80e66564-df3d-4e15-b34c-270ac7f22982", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 1\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "markdown", "id": "20f6f1d4-7254-422d-88d7-f4ee767c860a", "metadata": {}, "source": ["## Winter Forecast Flag - Aggressive Extrapolation"]}, {"cell_type": "code", "execution_count": null, "id": "fe9d9214-2e6f-43a5-b640-4e76dff160fd", "metadata": {}, "outputs": [], "source": ["try:\n", "    winter_input_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::city-winter-input\",\n", "    )\n", "\n", "except:\n", "    winter_input_df = pd.read_csv(\"winter_input.csv\")\n", "\n", "winter_input_df[\"winter_flag\"] = np.where(winter_input_df[\"switch\"] == \"no\", 0, 1)\n", "\n", "winter_input_df = winter_input_df.drop(columns={\"state\", \"switch\"})"]}, {"cell_type": "markdown", "id": "39fa6029-2106-4394-96db-460a24a58565", "metadata": {}, "source": ["## Total Disruption - City & L2"]}, {"cell_type": "code", "execution_count": null, "id": "3d7f3178-4644-4d8e-be04-68bb3edd3a5e", "metadata": {}, "outputs": [], "source": ["try:\n", "    tot_disruption_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::city-disruption\",\n", "    )\n", "\n", "except:\n", "    tot_disruption_df = pd.read_csv(\"city_disruption.csv\")\n", "\n", "tot_disruption_df[\"date_\"] = pd.to_datetime(tot_disruption_df[\"date_\"])\n", "\n", "tot_disruption_df[\"tot_flag\"] = 1"]}, {"cell_type": "code", "execution_count": null, "id": "a98c5f35-454b-44c5-8499-718691941edd", "metadata": {}, "outputs": [], "source": ["x = pd.date_range(\n", "    start=(pd.to_datetime(current_time) - timed<PERSON>ta(days=30)).strftime(\"%Y-%m-%d\"),\n", "    end=(pd.to_datetime(current_time) - timedelta(days=1)).strftime(\"%Y-%m-%d\"),\n", ")\n", "\n", "date_df = pd.DataFrame({\"date_\": x, \"date_flag\": 1})\n", "\n", "hour_list = list({6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23})\n", "hour_df = pd.DataFrame({\"hour_\": hour_list, \"date_flag\": 1})\n", "\n", "date_df = date_df.merge(hour_df, on=[\"date_flag\"], how=\"left\")\n", "\n", "date_df.shape"]}, {"cell_type": "markdown", "id": "9cd45e83-591c-4bdc-b72a-992089de30e8", "metadata": {"tags": []}, "source": ["# Data Extraction"]}, {"cell_type": "markdown", "id": "99b6d335-4032-49f0-a3cd-ce366a5ff8c8", "metadata": {"tags": []}, "source": ["## Milk Forecast Base"]}, {"cell_type": "markdown", "id": "de60aed2-8a89-461b-b3b6-b07011a840ed", "metadata": {"tags": []}, "source": ["### Base Assortment - Fetch"]}, {"cell_type": "code", "execution_count": null, "id": "70901eec-e7b9-457a-b4a6-7b428bc69174", "metadata": {}, "outputs": [], "source": ["base_query = \"\"\"\n", "    WITH active_stores AS (\n", "        SELECT DISTINCT pfom.facility_id, pfom.outlet_id, bfom.facility_id AS be_facility_id\n", "        FROM po.physical_facility_outlet_mapping pfom \n", "        JOIN retail.console_outlet co ON co.id = pfom.outlet_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "        JOIN po.bulk_facility_outlet_mapping bfom ON bfom.outlet_id = pfom.outlet_id AND bfom.active = True AND bfom.lake_active_record\n", "        WHERE ars_active = 1 AND pfom.active = 1 AND pfom.is_primary = 1 AND pfom.lake_active_record\n", "    ),\n", "\n", "    milk_assortment AS (\n", "        SELECT DISTINCT cl.name AS city, a.facility_id, outlet_id, item_id, be_facility_id\n", "        FROM rpc.product_facility_master_assortment a\n", "        JOIN active_stores b ON a.facility_id = b.facility_id\n", "        JOIN retail.console_outlet co ON co.id = b.outlet_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "        LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id AND cl.lake_active_record\n", "        WHERE item_id IN (SELECT DISTINCT item_id FROM rpc.item_category_details WHERE l2_id = 1185 AND lake_active_record) \n", "        AND a.active = 1 AND a.master_assortment_substate_id = 1 AND a.lake_active_record\n", "    ),\n", "\n", "    be_mapping AS (\n", "        SELECT DISTINCT item_id, outlet_id, cb.facility_id AS be_facility_id\n", "        FROM rpc.item_outlet_tag_mapping tm\n", "        JOIN retail.console_outlet cb ON cb.id = CAST(tag_value AS int) AND cb.active = 1 AND cb.lake_active_record\n", "        WHERE tm.active = 1 AND tm.tag_type_id = 8 AND tm.lake_active_record\n", "    ),\n", "\n", "    final AS (\n", "        SELECT city, facility_id, a.outlet_id, a.item_id, a.be_facility_id\n", "        FROM milk_assortment a\n", "        JOIN be_mapping b ON a.item_id = b.item_id AND a.outlet_id = b.outlet_id AND a.be_facility_id = b.be_facility_id\n", "    )\n", "\n", "    SELECT * FROM final\n", "\"\"\"\n", "base_pre_df = read_sql_query(base_query, trino)\n", "\n", "base_pre_df[\"be_facility_id\"] = base_pre_df[\"be_facility_id\"].fillna(0).astype(int)\n", "\n", "base_pre_df.shape"]}, {"cell_type": "markdown", "id": "fa6d5e5c-80ba-4d1f-828d-80fc3baacf67", "metadata": {"tags": []}, "source": ["### TAT Calculation"]}, {"cell_type": "code", "execution_count": null, "id": "a8572279-2ec6-4092-b673-c3d028dfede6", "metadata": {}, "outputs": [], "source": ["base_df = base_pre_df.copy()\n", "\n", "base_df[\"date_flag\"] = 1\n", "\n", "base_df = base_df.merge(date_df, on=[\"date_flag\"], how=\"left\").drop(columns={\"date_flag\"})\n", "\n", "base_df = base_df.merge(winter_input_df, on=[\"city\"], how=\"left\")\n", "\n", "base_df[\"winter_flag\"] = base_df[\"winter_flag\"].fillna(0).astype(int)\n", "\n", "base_df[\"tat\"] = 1\n", "\n", "base_df[\"tdate\"] = pd.to_datetime(today_date) + pd.to_timedelta(base_df[\"tat\"], unit=\"D\")\n", "\n", "base_df[\"tdow\"] = pd.to_datetime(base_df[\"tdate\"]).dt.dayofweek\n", "\n", "base_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "a85cf78d-7a24-4aaf-bebe-b9c8bb71339a", "metadata": {}, "outputs": [], "source": ["del [base_pre_df]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "93745c2b-a5a0-4a84-aeb3-a871ec5fdc46", "metadata": {"tags": []}, "source": ["### Item, Facility, Outlet List"]}, {"cell_type": "code", "execution_count": null, "id": "e98f2d61-7fc5-4f96-9aff-7d9287ed9436", "metadata": {}, "outputs": [], "source": ["facility_id_list = tuple(set(base_df[\"facility_id\"].to_list()))\n", "outlet_id_list = tuple(set(base_df[\"outlet_id\"].to_list()))\n", "item_id_list = tuple(set(base_df[\"item_id\"].to_list()))\n", "\n", "len(item_id_list), len(facility_id_list), len(outlet_id_list)"]}, {"cell_type": "markdown", "id": "f9d503c1-017f-44c5-ae12-8be35625acd9", "metadata": {"tags": []}, "source": ["## Fetching all data from ETL\n"]}, {"cell_type": "code", "execution_count": null, "id": "db95b646-996c-414b-8e1c-8e7593f24166", "metadata": {}, "outputs": [], "source": ["base_query = f\"\"\"\n", "       SELECT\n", "           a.date_,\n", "            cl.name as city,\n", "            b.outlet_id as outlet_id,\n", "            a.facility_id,\n", "            a.item_id,\n", "            hour_,\n", "            max(is_available) as is_available,\n", "            max(carts) carts,\n", "            max(total_search) as searches,\n", "            max(sales) as quantity\n", "        from\n", "            supply_etls.milk_sales_avail_searches_dump as a\n", "        left join po.physical_facility_outlet_mapping b on b.facility_id = a.facility_id and ars_active = 1 and lake_active_record\n", "        LEFT JOIN retail.console_location cl ON cl.id = b.city_id\n", "        inner join (select date_,facility_id,item_id,max(updated_at) as updated_at from supply_etls.milk_sales_avail_searches_dump group by 1,2,3) as mx\n", "            on mx.date_ = a.date_ and a.updated_at=mx.updated_at\n", "            and a.facility_id=mx.facility_id and a.item_id=mx.item_id\n", "        where a.date_ BETWEEN DATE('{today_date}') - interval '45' day AND DATE('{today_date}') - interval '1' day\n", "            and a.facility_id in {facility_id_list}\n", "            and a.item_id in {item_id_list}\n", "            and hour_ between 6 and 23\n", "        group by 1,2,3,4,5,6\n", "    \"\"\"\n", "data_df = read_sql_query(base_query, trino)\n", "data_df[\"date_\"] = pd.to_datetime(data_df[\"date_\"])\n", "data_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "95ad71e8-e3ab-4caf-85c4-46275a1ee77b", "metadata": {}, "outputs": [], "source": ["data_df.to_csv(\"data_df.csv\", index=False)"]}, {"cell_type": "markdown", "id": "278fd917-41ec-444b-8ad2-8f19bd2acf42", "metadata": {"tags": []}, "source": ["#### Assortment Correction"]}, {"cell_type": "code", "execution_count": null, "id": "9786a85d-1205-42ab-a0a4-3308c3f9e57e", "metadata": {}, "outputs": [], "source": ["assortment_df = pd.merge(\n", "    base_df,\n", "    data_df[[\"date_\", \"hour_\", \"facility_id\", \"item_id\", \"is_available\"]].copy().drop_duplicates(),\n", "    on=[\"item_id\", \"facility_id\", \"date_\", \"hour_\"],\n", "    how=\"left\",\n", ")\n", "\n", "assortment_df[\"is_available\"] = np.where(\n", "    assortment_df[\"is_available\"].isna(), 0, assortment_df[\"is_available\"]\n", ")\n", "\n", "assortment_df[\"dow\"] = pd.to_datetime(assortment_df[\"date_\"]).dt.dayofweek\n", "\n", "assortment_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "db59d79e-08cc-425b-a117-0e3252878b97", "metadata": {}, "outputs": [], "source": ["del [base_df]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "3b1ff759-7bdd-4441-a483-637a06b7315f", "metadata": {"tags": []}, "source": ["### Hourly Sales"]}, {"cell_type": "code", "execution_count": null, "id": "eb345ac3-ad4a-4233-8d76-e62b47daf201", "metadata": {}, "outputs": [], "source": ["# sales_pre_df = data_df[[\"city\",\"date_\", \"hour_\", \"facility_id\", \"item_id\", \"quantity\"]].copy().drop_duplicates()"]}, {"cell_type": "markdown", "id": "73e9c507-d6c4-4dec-814c-301d06a1933c", "metadata": {"tags": []}, "source": ["## Daily Carts & Cart Penetration - Store Definition & Disruption"]}, {"cell_type": "code", "execution_count": null, "id": "b1328bbd-de5a-4d00-b325-c193803b96c9", "metadata": {}, "outputs": [], "source": ["carts_query = f\"\"\"\n", "    WITH item_mapping AS (\n", "        SELECT DISTINCT ipr.product_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NULL THEN ipom_0.item_id \n", "            ELSE ipr.item_id \n", "        END AS item_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1) \n", "            ELSE COALESCE(ipom_0.multiplier,1) \n", "        END AS multiplier\n", "        FROM rpc.item_product_mapping ipr \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", "        WHERE ipr.lake_active_record\n", "    ),\n", "\n", "    sales AS (\n", "        SELECT (oid.cart_checkout_ts_ist) AS order_date, oid.outlet_id, oid.product_id, im.item_id, oid.order_id, im.multiplier, oid.total_doorstep_return_quantity, ((oid.procured_quantity - oid.total_doorstep_return_quantity) * im.multiplier) AS sales_quantity \n", "        FROM dwh.fact_sales_order_item_details oid \n", "        JOIN item_mapping im on im.product_id = oid.product_id  \n", "        WHERE oid.order_create_dt_ist >= DATE('{today_date}') - interval '15' day\n", "        AND oid.is_internal_order = false  \n", "        AND (oid.order_type NOT LIKE '%%internal%%' OR oid.order_type IS NULL)  \n", "        AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED'\n", "        AND oid.outlet_id IN {outlet_id_list}\n", "    ),\n", "    \n", "    all_carts AS (\n", "        SELECT DATE(order_date) AS date_, EXTRACT(hour FROM order_date) AS hour_, outlet_id, COUNT(DISTINCT order_id) AS fac_carts\n", "        FROM sales\n", "        GROUP BY 1,2,3\n", "    ),\n", "    \n", "    milk_carts AS (\n", "        SELECT DATE(order_date) AS date_, EXTRACT(hour FROM order_date) AS hour_, outlet_id, COUNT(DISTINCT order_id) AS milk_carts\n", "        FROM sales\n", "        WHERE item_id IN {item_id_list}\n", "        GROUP BY 1,2,3\n", "    )\n", "    \n", "    SELECT a.outlet_id, a.date_, a.hour_, fac_carts, \n", "    CASE \n", "        WHEN milk_carts IS NULL THEN 0\n", "        ELSE milk_carts \n", "    END AS milk_carts\n", "    FROM all_carts a\n", "    LEFT JOIN milk_carts b ON a.outlet_id = b.outlet_id AND a.date_ = b.date_ AND a.hour_ = b.hour_\n", "    WHERE a.hour_ BETWEEN 6 AND 23\n", "    \"\"\"\n", "carts_df = read_sql_query(carts_query, trino)\n", "\n", "carts_df[\"date_\"] = pd.to_datetime(carts_df[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "5ea93526-17b2-443f-8f62-a40bde3eaf48", "metadata": {}, "outputs": [], "source": ["morning_carts_df = (\n", "    carts_df[carts_df[\"hour_\"] < 18]\n", "    .groupby([\"date_\", \"outlet_id\"])\n", "    .agg({\"fac_carts\": \"sum\", \"milk_carts\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "morning_carts_df = morning_carts_df.rename(\n", "    columns={\"fac_carts\": \"morning_fac_carts\", \"milk_carts\": \"morning_milk_carts\"}\n", ")\n", "\n", "carts_agg_df = (\n", "    carts_df.groupby([\"date_\", \"outlet_id\"])\n", "    .agg({\"fac_carts\": \"sum\", \"milk_carts\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "l15_date = (current_time - timedelta(days=15)).strftime(\"%Y-%m-%d\")\n", "\n", "x = pd.date_range(\n", "    start=pd.to_datetime(l15_date).strftime(\"%Y-%m-%d\"),\n", "    end=(pd.to_datetime(today_date)).strftime(\"%Y-%m-%d\"),\n", ")\n", "\n", "date_df = pd.DataFrame({\"date_\": x, \"flag\": 1})\n", "\n", "store_base_df = pd.DataFrame(outlet_id_list, columns=[\"outlet_id\"])\n", "store_base_df[\"flag\"] = 1\n", "\n", "carts_base_df = pd.merge(store_base_df, date_df, on=[\"flag\"], how=\"left\").drop(columns={\"flag\"})\n", "\n", "carts_base_df = pd.merge(carts_base_df, morning_carts_df, on=[\"outlet_id\", \"date_\"], how=\"left\")\n", "\n", "carts_base_df = pd.merge(carts_base_df, carts_agg_df, on=[\"outlet_id\", \"date_\"], how=\"left\")\n", "\n", "carts_base_df[\"morning_cp\"] = (\n", "    carts_base_df[\"morning_milk_carts\"] / carts_base_df[\"morning_fac_carts\"]\n", ")\n", "\n", "carts_base_df[\"cp\"] = carts_base_df[\"milk_carts\"] / carts_base_df[\"fac_carts\"]\n", "\n", "carts_base_df[[\"fac_carts\", \"milk_carts\", \"cp\"]] = carts_base_df[\n", "    [\"fac_carts\", \"milk_carts\", \"cp\"]\n", "].fillna(0)\n", "\n", "carts_base_df[[\"morning_fac_carts\", \"morning_milk_carts\", \"morning_cp\"]] = carts_base_df[\n", "    [\"morning_fac_carts\", \"morning_milk_carts\", \"morning_cp\"]\n", "].fillna(0)\n", "\n", "carts_base_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "f2f4a878-dac5-47a5-8e6c-8143e071e9a6", "metadata": {}, "outputs": [], "source": ["def percentile(x):\n", "    return np.ceil(np.percentile(x, 75)).astype(int)\n", "\n", "\n", "def cp_percentile(x):\n", "    return np.percentile(x, 75)"]}, {"cell_type": "markdown", "id": "03888468-34ad-4b39-b6fd-354c6582849e", "metadata": {"tags": []}, "source": ["### Disruption Days"]}, {"cell_type": "markdown", "id": "8d609fd8-b0eb-44a5-a45e-2978e94a5bf4", "metadata": {"tags": []}, "source": ["#### Store Disruption"]}, {"cell_type": "code", "execution_count": null, "id": "a7b2a9c9-1865-4fc0-bf82-1c9fd1fb1d25", "metadata": {}, "outputs": [], "source": ["opd_df = (\n", "    carts_base_df[(carts_base_df[\"fac_carts\"] != 0)]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "opd_df = (\n", "    opd_df.groupby([\"outlet_id\"])\n", "    .agg({\"fac_carts\": percentile, \"morning_fac_carts\": percentile})\n", "    .reset_index()\n", ")\n", "\n", "opd_df = opd_df.rename(columns={\"fac_carts\": \"opd\", \"morning_fac_carts\": \"morning_opd\"})\n", "\n", "opd_df[\"ftype_\"] = np.where(\n", "    opd_df[\"opd\"] < 800, \"low\", np.where(opd_df[\"opd\"] >= 1200, \"high\", \"medium\")\n", ")\n", "\n", "opd_df.shape"]}, {"cell_type": "markdown", "id": "3a562492-4f0f-4fde-b79f-e35e431d371f", "metadata": {"tags": []}, "source": ["#### Milk Carts Disruption"]}, {"cell_type": "code", "execution_count": null, "id": "292f0b9d-9001-4ed2-91f1-310b616d6de9", "metadata": {}, "outputs": [], "source": ["milk_cp_df = (\n", "    carts_base_df[carts_base_df[\"cp\"] != 0].drop_duplicates().reset_index().drop(columns={\"index\"})\n", ")\n", "\n", "milk_cp_df = (\n", "    milk_cp_df.groupby([\"outlet_id\"])\n", "    .agg({\"cp\": cp_percentile, \"morning_cp\": cp_percentile})\n", "    .reset_index()\n", ")\n", "\n", "milk_cp_df = milk_cp_df.rename(columns={\"cp\": \"milk_cp\", \"morning_cp\": \"morning_milk_cp\"})\n", "\n", "milk_cp_df[\"cp_bucket\"] = np.where(\n", "    milk_cp_df[\"milk_cp\"] < 0.1,\n", "    \"low\",\n", "    np.where(milk_cp_df[\"milk_cp\"] >= 0.16, \"high\", \"medium\"),\n", ")\n", "\n", "milk_cp_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "13ef6fcb-10af-4ed5-a86c-e1dd42b77aec", "metadata": {}, "outputs": [], "source": ["disruption_base_df = pd.merge(carts_base_df, opd_df, on=[\"outlet_id\"], how=\"left\")\n", "\n", "disruption_base_df = pd.merge(disruption_base_df, milk_cp_df, on=[\"outlet_id\"], how=\"left\")\n", "\n", "disruption_base_df[[\"cp_bucket\", \"ftype_\"]] = disruption_base_df[[\"cp_bucket\", \"ftype_\"]].fillna(\n", "    \"low\"\n", ")\n", "\n", "disruption_base_df = pd.merge(carts_base_df, opd_df, on=[\"outlet_id\"], how=\"left\")\n", "\n", "disruption_base_df = pd.merge(disruption_base_df, milk_cp_df, on=[\"outlet_id\"], how=\"left\")\n", "\n", "disruption_base_df[[\"cp_bucket\", \"ftype_\"]] = disruption_base_df[[\"cp_bucket\", \"ftype_\"]].fillna(\n", "    \"low\"\n", ")\n", "\n", "disruption_base_df[\"past_opd_deviation\"] = (\n", "    disruption_base_df[\"fac_carts\"] / disruption_base_df[\"opd\"] - 1\n", ")\n", "\n", "disruption_base_df[\"current_opd_deviation\"] = (\n", "    disruption_base_df[\"morning_fac_carts\"] / disruption_base_df[\"morning_opd\"] - 1\n", ")\n", "\n", "disruption_base_df[\"past_cp_deviation\"] = (\n", "    disruption_base_df[\"cp\"] / disruption_base_df[\"milk_cp\"] - 1\n", ")\n", "\n", "disruption_base_df[\"current_cp_deviation\"] = (\n", "    disruption_base_df[\"morning_cp\"] / disruption_base_df[\"morning_milk_cp\"] - 1\n", ")\n", "\n", "disruption_base_df.head(1)"]}, {"cell_type": "markdown", "id": "ba5bf11b-4483-421d-83fa-09b27c19aa71", "metadata": {}, "source": ["#### Morning Disruption Check"]}, {"cell_type": "code", "execution_count": null, "id": "70fa2980-47e1-4866-9c97-dc76f46c6b87", "metadata": {}, "outputs": [], "source": ["morning_disruption_base_df = disruption_base_df.copy()\n", "\n", "morning_disruption_base_df[\"opd_deviation\"] = morning_disruption_base_df[\"current_opd_deviation\"]\n", "\n", "morning_disruption_base_df[\"cp_deviation\"] = morning_disruption_base_df[\"current_cp_deviation\"]\n", "\n", "morning_disruption_base_df[\"opd_flag\"] = np.where(\n", "    (morning_disruption_base_df[\"ftype_\"].isin({\"high\", \"medium\"}))\n", "    & (morning_disruption_base_df[\"opd_deviation\"] <= -0.25),\n", "    1,\n", "    0,\n", ")\n", "\n", "morning_disruption_base_df[\"cp_flag\"] = np.where(\n", "    (morning_disruption_base_df[\"ftype_\"].isin({\"high\", \"medium\"}))\n", "    & (morning_disruption_base_df[\"cp_deviation\"] <= -0.4),\n", "    1,\n", "    0,\n", ")\n", "\n", "morning_disruption_base_df[\"d_flag\"] = (\n", "    morning_disruption_base_df[\"opd_flag\"] + morning_disruption_base_df[\"cp_flag\"]\n", ")\n", "\n", "morning_disruption_df = (\n", "    morning_disruption_base_df[morning_disruption_base_df[\"d_flag\"] > 0][\n", "        [\"outlet_id\", \"date_\", \"d_flag\"]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "morning_disruption_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "4aa99e93-1406-4c9c-895b-61bbc12bceda", "metadata": {}, "outputs": [], "source": ["buffer_store = f\"\"\"with\n", "    frontend_merchant_mapping as\n", "            (select * from dwh.dim_merchant_outlet_facility_mapping\n", "                where \n", "                    is_frontend_merchant_active = true\n", "                    and\n", "                        is_backend_merchant_active = true\n", "                    and \n", "                        is_pos_outlet_active = 1\n", "                    and \n", "                        is_mapping_enabled = true\n", "                    and \n", "                        is_express_store = true\n", "                    and \n", "                        is_current_mapping_active = true\n", "                    and \n", "                        is_current = true\n", "                    and \n", "                        pos_outlet_name <> 'SS Gurgaon Test Store'\n", "    ),\n", "\n", "    store_polygon_updates as (\n", "    select \n", "        cr.updated_at + interval '5' hour + interval '30' minute as updated_time, \n", "        f.external_id as merchant_id, \n", "        json_query(cr.meta, 'strict $.business_impact.old_order_count') as old_order_count,\n", "        json_query(cr.meta, 'strict $.business_impact.new_order_count') as new_orders_count,\n", "        json_query(cr.diff, 'strict $.polygon.info') as change_in_area\n", "    from \n", "        sauron.change_requests cr \n", "    join \n", "        sauron.feature f \n", "        on f.id=cr.feature_id\n", "    where \n", "        f.layer_id = 6 \n", "        AND cr.updated_at + interval '5' hour + interval '30' minute > CURRENT_DATE - interval '30' day \n", "        and cr.updated_at + interval '5' hour + interval '30' minute < CURRENT_DATE \n", "        AND cr.state = 'MERGED'\n", "        -- and f.external_id in (33207)\n", "    )\n", "\n", "    select \n", "        fm.pos_outlet_id as outlet_id,\n", "        fm.facility_id as facility_id,\n", "        date(max(updated_time)) as date_n\n", "\n", "    from \n", "        store_polygon_updates s\n", "    left join frontend_merchant_mapping  fm on fm.frontend_merchant_id = s.merchant_id\n", "    where s.updated_time between  date'{today_date}' - interval '10' day and date'{today_date}' \n", "\n", "group by 1,2\n", "    \"\"\"\n", "\n", "buffer_store_mapping = pd.read_sql_query(buffer_store, trino)\n", "buffer_store_mapping[\"date_n\"] = pd.to_datetime(buffer_store_mapping[\"date_n\"])\n", "\n", "buffer_store_facility_list = list(set(buffer_store_mapping[\"facility_id\"].unique()))"]}, {"cell_type": "code", "execution_count": null, "id": "cf8dfa1f-aa18-4e8b-8202-45518daf0097", "metadata": {}, "outputs": [], "source": ["# wl_store_df = pd.concat([buffer_store_mapping,new_store_mapping])\n", "morning_disruption_df = morning_disruption_df.merge(\n", "    buffer_store_mapping[[\"outlet_id\", \"date_n\"]], on=[\"outlet_id\"], how=\"left\"\n", ")\n", "morning_disruption_df[\"d_flag\"] = np.where(\n", "    morning_disruption_df[\"date_\"] >= morning_disruption_df[\"date_n\"],\n", "    0,\n", "    morning_disruption_df[\"d_flag\"],\n", ")\n", "morning_disruption_df.drop(columns={\"date_n\"}, inplace=True)"]}, {"cell_type": "markdown", "id": "5c946a6d-fd72-4f64-a05b-c6737f6685d1", "metadata": {}, "source": ["#### Overall Disruption Check"]}, {"cell_type": "code", "execution_count": null, "id": "0619addb-2f8a-4ee1-9ce7-d3103a8bb4ae", "metadata": {}, "outputs": [], "source": ["overall_disruption_base_df = disruption_base_df.copy()\n", "\n", "overall_disruption_base_df[\"opd_deviation\"] = overall_disruption_base_df[\"past_opd_deviation\"]\n", "\n", "overall_disruption_base_df[\"cp_deviation\"] = overall_disruption_base_df[\"past_cp_deviation\"]\n", "\n", "overall_disruption_base_df[\"opd_flag\"] = np.where(\n", "    (overall_disruption_base_df[\"ftype_\"].isin({\"high\", \"medium\"}))\n", "    & (overall_disruption_base_df[\"opd_deviation\"] <= -0.25),\n", "    1,\n", "    0,\n", ")\n", "\n", "overall_disruption_base_df[\"cp_flag\"] = np.where(\n", "    (overall_disruption_base_df[\"ftype_\"].isin({\"high\", \"medium\"}))\n", "    & (overall_disruption_base_df[\"cp_deviation\"] <= -0.4),\n", "    1,\n", "    0,\n", ")\n", "\n", "overall_disruption_base_df[\"d_flag\"] = (\n", "    overall_disruption_base_df[\"opd_flag\"] + overall_disruption_base_df[\"cp_flag\"]\n", ")\n", "\n", "overall_disruption_df = (\n", "    overall_disruption_base_df[overall_disruption_base_df[\"d_flag\"] > 0][\n", "        [\"outlet_id\", \"date_\", \"d_flag\"]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "overall_disruption_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "30ab4a2c-7b8b-423e-9e55-dfee380ce08f", "metadata": {}, "outputs": [], "source": ["overall_disruption_df = overall_disruption_df.merge(\n", "    buffer_store_mapping[[\"outlet_id\", \"date_n\"]], on=[\"outlet_id\"], how=\"left\"\n", ")\n", "overall_disruption_df[\"d_flag\"] = np.where(\n", "    overall_disruption_df[\"date_\"] >= overall_disruption_df[\"date_n\"],\n", "    0,\n", "    overall_disruption_df[\"d_flag\"],\n", ")\n", "overall_disruption_df.drop(columns={\"date_n\"}, inplace=True)\n", "overall_disruption_df.head()"]}, {"cell_type": "markdown", "id": "db54c0b0-9f29-4b12-bb0c-7fc335493fc5", "metadata": {}, "source": ["#### Store Type Definition"]}, {"cell_type": "code", "execution_count": null, "id": "2a1d725d-2df1-43b6-81c5-6d99345b2127", "metadata": {}, "outputs": [], "source": ["store_type_df = (\n", "    disruption_base_df[[\"outlet_id\", \"cp_bucket\", \"ftype_\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "store_type_df[\"ftype_\"] = np.where(\n", "    (store_type_df[\"ftype_\"] == \"medium\") & (store_type_df[\"cp_bucket\"].isin({\"low\"})),\n", "    \"low\",\n", "    store_type_df[\"ftype_\"],\n", ")\n", "\n", "store_type_df[\"ftype_\"] = np.where(\n", "    (store_type_df[\"ftype_\"] == \"high\") & (store_type_df[\"cp_bucket\"].isin({\"medium\", \"low\"})),\n", "    \"medium\",\n", "    store_type_df[\"ftype_\"],\n", ")\n", "\n", "store_type_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "ba6c91b6-a29e-4205-8fe7-e80ef1a620ad", "metadata": {}, "outputs": [], "source": ["store_type_df.groupby([\"ftype_\"]).agg({\"outlet_id\": \"count\"}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "865442fe-e4c9-421f-9b11-d093f5d36854", "metadata": {}, "outputs": [], "source": ["del [opd_df, milk_cp_df, carts_df]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "d60db951-ed84-4921-9af4-54c1e4be0276", "metadata": {"tags": []}, "source": ["### Bucketing - Item Ranking"]}, {"cell_type": "code", "execution_count": null, "id": "51755f1a-0f69-4a0f-8eba-cfb23822ff41", "metadata": {}, "outputs": [], "source": ["sku_rank_df = read_sql_query(\n", "    f\"\"\"\n", "    select \n", "            city,\n", "            item_id,\n", "            wt/total_weight wt,\n", "            case \n", "            when cum_sum_wt/total_weight>0.3 then 'top'\n", "            else 'bottom' end as stype_\n", "        from (\n", "         select\n", "            city,\n", "            item_id,\n", "            CAST(weights AS DOUBLE) as wt,\n", "            sum(CAST(weights AS DOUBLE)) over (partition by city order by weights ) as cum_sum_wt,\n", "            sum(CAST(weights AS DOUBLE)) over (partition by city) as total_weight\n", "        FROM supply_etls.city_item_weights a\n", "        WHERE a.updated_at = (select max(updated_at) from supply_etls.city_item_weights where updated_at >= current_date - interval '32' day) \n", "        and updated_at >= current_date - interval '32' day\n", "        AND item_id IN (select distinct item_id from rpc.item_category_details where l2_id = 1185 and lake_active_record)\n", "        )\n", "\"\"\",\n", "    trino,\n", ")\n", "\n", "sku_rank_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "27b2e9e3-4395-413b-87a5-b0d6e8f3567d", "metadata": {}, "outputs": [], "source": ["sku_rank_df[sku_rank_df.city == \"Delhi\"].sort_values(\"wt\", ascending=False)"]}, {"cell_type": "markdown", "id": "eb41524e-ab6e-4000-93a2-1b82e6978183", "metadata": {}, "source": ["### Premium Milk"]}, {"cell_type": "code", "execution_count": null, "id": "65f1a5e4-5da7-492c-8ab7-1a219819e517", "metadata": {}, "outputs": [], "source": ["premium_df = (\n", "    data_df[[\"city\", \"date_\", \"hour_\", \"facility_id\", \"item_id\", \"quantity\"]]\n", "    .copy()\n", "    .drop_duplicates()\n", "    .groupby([\"city\", \"date_\", \"item_id\"])\n", "    .agg({\"quantity\": \"sum\", \"facility_id\": \"nunique\"})\n", "    .reset_index()\n", ")\n", "\n", "premium_df[\"qps\"] = premium_df[\"quantity\"] / premium_df[\"facility_id\"]\n", "\n", "premium_df = premium_df.groupby([\"city\", \"item_id\"]).agg({\"qps\": \"mean\"}).reset_index()\n", "\n", "premium_df[\"premium_flag\"] = np.where(premium_df[\"qps\"] <= 5, 1, 0)\n", "\n", "premium_df.head(1)"]}, {"cell_type": "markdown", "id": "2a10721f-b5cb-41a0-9714-1d1f170e48a8", "metadata": {"tags": []}, "source": ["### Merge with Assortment "]}, {"cell_type": "code", "execution_count": null, "id": "2036a1a4-768a-481f-9096-1f4ddf5c30f4", "metadata": {}, "outputs": [], "source": ["assortment_filter_df = pd.merge(\n", "    assortment_df, store_type_df[[\"outlet_id\", \"ftype_\"]], on=[\"outlet_id\"], how=\"left\"\n", ")\n", "\n", "assortment_filter_df = assortment_filter_df.merge(sku_rank_df, on=[\"city\", \"item_id\"], how=\"left\")\n", "\n", "assortment_filter_df = assortment_filter_df.merge(\n", "    premium_df.drop(columns={\"qps\"}), on=[\"city\", \"item_id\"], how=\"left\"\n", ")\n", "\n", "assortment_filter_df[\"stype_\"] = np.where(\n", "    assortment_filter_df[\"premium_flag\"] == 1, \"premium\", assortment_filter_df[\"stype_\"]\n", ")\n", "\n", "assortment_filter_df = assortment_filter_df.drop(columns={\"premium_flag\"})\n", "\n", "assortment_filter_df[\"date_\"] = pd.to_datetime(assortment_filter_df[\"date_\"])\n", "\n", "assortment_filter_df[\"ftype_\"] = assortment_filter_df[\"ftype_\"].fillna(\"low\")\n", "assortment_filter_df[\"stype_\"] = assortment_filter_df[\"stype_\"].fillna(\"bottom\")\n", "\n", "assortment_filter_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "3ec08cee-7e71-48cb-b023-e3b04d9566ac", "metadata": {}, "outputs": [], "source": ["assortment_filter_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3f181412-32d8-4101-b833-2a69dcec5d8e", "metadata": {}, "outputs": [], "source": ["assortment_filter_df = assortment_filter_df.merge(\n", "    tot_disruption_df, on=[\"city\", \"date_\"], how=\"left\"\n", ")\n", "\n", "assortment_filter_df[\"tot_flag\"] = np.where(\n", "    assortment_filter_df[\"tot_flag\"].isna(), 0, assortment_filter_df[\"tot_flag\"]\n", ")\n", "\n", "assortment_filter_df = (\n", "    assortment_filter_df[assortment_filter_df[\"tot_flag\"] == 0]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\", \"tot_flag\"})\n", ")\n", "\n", "assortment_filter_df.shape"]}, {"cell_type": "markdown", "id": "b1288e98-45f2-4368-85da-77ae1a72973d", "metadata": {}, "source": ["## Hour Weights - Search + Carts Logic"]}, {"cell_type": "markdown", "id": "a826f286-a9c8-421d-975d-6413c665e70c", "metadata": {}, "source": ["### Carts based Weights"]}, {"cell_type": "code", "execution_count": null, "id": "754c22ee-09ee-42e7-a027-f26dbeed6c81", "metadata": {}, "outputs": [], "source": ["carts_hourly_df = data_df[[\"date_\", \"city\", \"hour_\", \"outlet_id\", \"carts\"]].copy().drop_duplicates()\n", "\n", "carts_hourly_df = (\n", "    carts_hourly_df.groupby([\"city\", \"outlet_id\", \"hour_\"]).agg({\"carts\": percentile}).reset_index()\n", ")\n", "\n", "carts_hourly_df[\"tot_milk_carts\"] = carts_hourly_df.groupby([\"city\", \"outlet_id\"])[\n", "    \"carts\"\n", "].transform(\"sum\")\n", "\n", "carts_hourly_df[\"chw\"] = carts_hourly_df[\"carts\"] / carts_hourly_df[\"tot_milk_carts\"]\n", "\n", "carts_hourly_df = carts_hourly_df[[\"city\", \"outlet_id\", \"hour_\", \"chw\"]]\n", "carts_hourly_df.head(2)"]}, {"cell_type": "markdown", "id": "153ea43f-a6c9-4133-83e7-6c9e5755cb90", "metadata": {}, "source": ["### Search based Weights"]}, {"cell_type": "code", "execution_count": null, "id": "be64ea5e-ae3b-41e5-8cd9-af2647bd12a9", "metadata": {}, "outputs": [], "source": ["search_hourly_df = (\n", "    data_df[[\"city\", \"date_\", \"hour_\", \"outlet_id\", \"searches\"]].copy().drop_duplicates()\n", ")\n", "search_hourly_df = (\n", "    search_hourly_df.groupby([\"city\", \"outlet_id\", \"hour_\"])\n", "    .agg({\"searches\": percentile})\n", "    .reset_index()\n", ")\n", "search_hourly_df[\"tot_milk_searches\"] = search_hourly_df.groupby([\"city\", \"outlet_id\",])[\n", "    \"searches\"\n", "].transform(\"sum\")\n", "\n", "search_hourly_df[\"cshw\"] = search_hourly_df[\"searches\"] / search_hourly_df[\"tot_milk_searches\"]\n", "\n", "search_hourly_df = search_hourly_df[[\"city\", \"outlet_id\", \"hour_\", \"cshw\"]]\n", "\n", "search_hourly_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "ae613d2d-c155-4382-af72-2ac959d2c086", "metadata": {}, "outputs": [], "source": ["hour_weights_df = (\n", "    assortment_df[[\"city\", \"outlet_id\", \"hour_\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "hour_weights_df = hour_weights_df.merge(\n", "    search_hourly_df, on=[\"city\", \"outlet_id\", \"hour_\"], how=\"left\"\n", ")\n", "\n", "hour_weights_df = hour_weights_df.merge(\n", "    carts_hourly_df, on=[\"city\", \"outlet_id\", \"hour_\"], how=\"left\"\n", ")\n", "\n", "hour_weights_df[[\"cshw\", \"chw\"]] = hour_weights_df[[\"cshw\", \"chw\"]].fillna(0)\n", "\n", "hour_weights_df[\"hw\"] = (hour_weights_df[\"cshw\"] * 0.75) + (hour_weights_df[\"chw\"] * 0.25)\n", "\n", "hour_weights_df[\"tot_hw\"] = hour_weights_df.groupby([\"city\", \"outlet_id\"])[\"hw\"].transform(\"sum\")\n", "\n", "hour_weights_df[\"new_hw\"] = hour_weights_df[\"hw\"] / hour_weights_df[\"tot_hw\"]\n", "\n", "hour_weights_df = (\n", "    hour_weights_df[[\"city\", \"outlet_id\", \"hour_\", \"new_hw\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "del [search_hourly_df, carts_hourly_df]\n", "gc.collect()\n", "hour_weights_df.head()"]}, {"cell_type": "markdown", "id": "4c072eb8-8a8e-43c9-81c4-9834457d7f7f", "metadata": {"tags": []}, "source": ["# Morning Extrapolation - excluding Current Date"]}, {"cell_type": "markdown", "id": "132ddbe8-1714-4af0-9f25-926cab2cbb13", "metadata": {}, "source": ["### Merge with DataFrame"]}, {"cell_type": "code", "execution_count": null, "id": "4a7d211e-a331-4dbf-852e-aba4be3c4788", "metadata": {}, "outputs": [], "source": ["assortment_final_df = pd.merge(\n", "    assortment_filter_df, morning_disruption_df, on=[\"outlet_id\", \"date_\"], how=\"left\"\n", ")\n", "\n", "assortment_final_df[\"d_flag\"] = np.where(\n", "    assortment_final_df[\"d_flag\"].isna(), 0, assortment_final_df[\"d_flag\"]\n", ")\n", "\n", "assortment_final_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "01e6787a-6f14-4bab-a01c-93467750570d", "metadata": {}, "outputs": [], "source": ["assortment_final_df = (\n", "    assortment_final_df[assortment_final_df[\"d_flag\"] == 0]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\", \"d_flag\"})\n", ")\n", "\n", "assortment_final_df.shape"]}, {"cell_type": "markdown", "id": "bac7265b-8bc8-4771-94bf-f60437721e87", "metadata": {"tags": []}, "source": ["### Ranking & Sequencing"]}, {"cell_type": "code", "execution_count": null, "id": "ec81551e-283e-4c5e-bd9b-a5d131a62040", "metadata": {}, "outputs": [], "source": ["ranking_df = assortment_final_df[\n", "    [\"item_id\", \"facility_id\", \"date_\", \"dow\", \"tdate\", \"tdow\"]\n", "].drop_duplicates()\n", "\n", "ranking_df[\"r1\"] = ranking_df.groupby([\"item_id\", \"facility_id\"])[\"date_\"].rank(\n", "    method=\"dense\", ascending=False\n", ")\n", "\n", "# ranking_df[\"flag\"] = np.where(\n", "#     (ranking_df[\"facility_id\"].isin(buffer_store_facility_list)) & (ranking_df[\"r1\"] > 3),\n", "#     1,\n", "#     0,\n", "# )\n", "same_dow = (\n", "    ranking_df[ranking_df[\"dow\"] == ranking_df[\"tdow\"]].copy().sort_values(\"date_\", ascending=False)\n", ")\n", "same_dow[\"latest_dow\"] = same_dow.groupby([\"item_id\", \"facility_id\", \"tdate\"])[\"date_\"].rank(\n", "    method=\"dense\", ascending=False\n", ")\n", "same_dow = same_dow[same_dow.latest_dow > 1]\n", "same_dow[\"nd_flag\"] = 1\n", "ranking_df = ranking_df.merge(\n", "    same_dow[[\"facility_id\", \"item_id\", \"date_\", \"nd_flag\"]],\n", "    on=[\"facility_id\", \"item_id\", \"date_\"],\n", "    how=\"left\",\n", ")\n", "ranking_df[\"nd_flag\"] = ranking_df[\"nd_flag\"].fillna(0)\n", "ranking_df = ranking_df[ranking_df[\"nd_flag\"] == 0]\n", "# ranking_df = ranking_df[ranking_df[\"flag\"] == 0]\n", "ranking_df[\"r2\"] = np.where(\n", "    (ranking_df[\"dow\"] == ranking_df[\"tdow\"]),\n", "    ranking_df[\"r1\"],\n", "    ranking_df[\"r1\"] + 30,\n", ")\n", "\n", "\n", "ranking_df[\"rank\"] = ranking_df.groupby([\"item_id\", \"facility_id\"])[\"r2\"].rank(\n", "    method=\"dense\", ascending=True\n", ")\n", "ranking_df = ranking_df[ranking_df[\"rank\"] < 8]\n", "\n", "ranking_df = ranking_df[[\"facility_id\", \"item_id\", \"date_\", \"rank\"]].drop_duplicates()\n", "\n", "print(ranking_df.shape)\n", "ranking_df.head(1)"]}, {"cell_type": "markdown", "id": "22eefbfa-e0b5-424c-83bd-8e760f19e96b", "metadata": {}, "source": ["### Final Assortment"]}, {"cell_type": "code", "execution_count": null, "id": "19c163dd-1511-4b75-b946-421a5fe93dca", "metadata": {}, "outputs": [], "source": ["assortment_base_df = pd.merge(\n", "    assortment_filter_df,\n", "    ranking_df,\n", "    on=[\"item_id\", \"facility_id\", \"date_\"],\n", "    how=\"left\",\n", ")\n", "assortment_base_df[\"rank\"] = assortment_base_df[\"rank\"].fillna(666)\n", "assortment_base_df[\"flag\"] = np.where(\n", "    (assortment_base_df[\"stype_\"].isin({\"top\", \"bottom\"})) & (assortment_base_df[\"hour_\"] <= 23),\n", "    1,\n", "    np.where(\n", "        (assortment_base_df[\"stype_\"].isin({\"premium\"})) & (assortment_base_df[\"hour_\"] <= 21),\n", "        1,\n", "        0,\n", "    ),\n", ")\n", "\n", "assortment_base_df[\"flag\"] = np.where(\n", "    (assortment_base_df[\"stype_\"].isin({\"top\", \"bottom\"}))\n", "    & (assortment_base_df[\"hour_\"] <= 23)\n", "    & (assortment_base_df[\"winter_flag\"] == 1),\n", "    1,\n", "    assortment_base_df[\"flag\"],\n", ")\n", "\n", "assortment_base_df = assortment_base_df[assortment_base_df[\"flag\"] == 1].drop(columns={\"flag\"})\n", "\n", "assortment_base_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "8b12e123-ade1-4219-b6c8-b3c5d69e59c2", "metadata": {}, "outputs": [], "source": ["# del [data_df]\n", "# gc.collect()"]}, {"cell_type": "markdown", "id": "dd443710-5049-4e63-a59c-7489aee61e19", "metadata": {}, "source": ["### Assortment & Sales Merge"]}, {"cell_type": "code", "execution_count": null, "id": "e7c50c84-552f-4889-a9cb-c640acc1b42f", "metadata": {}, "outputs": [], "source": ["assortment_sales_df = pd.merge(\n", "    assortment_base_df,\n", "    data_df[[\"city\", \"date_\", \"hour_\", \"facility_id\", \"item_id\", \"quantity\"]]\n", "    .copy()\n", "    .drop_duplicates(),\n", "    # sales_pre_df,\n", "    on=[\"item_id\", \"facility_id\", \"hour_\", \"date_\", \"city\"],\n", "    how=\"left\",\n", ")\n", "\n", "assortment_sales_df[\"is_available\"] = np.where(\n", "    assortment_sales_df[\"quantity\"] > 0, 1, assortment_sales_df[\"is_available\"]\n", ")\n", "\n", "assortment_sales_df[\"quantity\"] = np.where(\n", "    (assortment_sales_df[\"is_available\"] == 1) & (assortment_sales_df[\"quantity\"].isna()),\n", "    0,\n", "    assortment_sales_df[\"quantity\"],\n", ")\n", "assortment_sales_df = assortment_sales_df.merge(\n", "    hour_weights_df, on=[\"city\", \"outlet_id\", \"hour_\"], how=\"left\"\n", ")\n", "\n", "assortment_sales_df[\"hw\"] = np.where(\n", "    assortment_sales_df[\"new_hw\"].isna(), 0, assortment_sales_df[\"new_hw\"]\n", ")\n", "\n", "assortment_sales_df[\"wt_score\"] = assortment_sales_df[\"is_available\"] * assortment_sales_df[\"hw\"]\n", "\n", "assortment_sales_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "44afdbc4-eccf-4c90-a437-544ff54209f0", "metadata": {}, "outputs": [], "source": ["del [data_df]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "9e6dc78e-0001-4ef8-a194-36d8cee4811f", "metadata": {}, "source": ["## Adding hex sales for CPC stores - 6am to 5:69pm"]}, {"cell_type": "code", "execution_count": null, "id": "f52fb127-f22f-4ab9-93b7-fc5dd2387eb6", "metadata": {}, "outputs": [], "source": ["hex_A = f\"\"\"\n", "select\n", "    date_,\n", "    facility_id,\n", "    item_id as item_id,\n", "    sales as sales,\n", "    lin_extp lin_extp,\n", "    exp_extp exp_extp,\n", "    para_extp para_extp\n", "from\n", "    supply_etls.milk_outlet_hex_sales a\n", "inner join (select slot,max(updated_at) updated_at\n", "                        from supply_etls.milk_outlet_hex_sales\n", "                        where \n", "                        updated_at>=current_date group by 1) b\n", "                on a.updated_at = b.updated_at and a.slot = b.slot\n", "where a.slot = '6 to 17'\n", "and a.updated_at>=current_date\n", "\"\"\"\n", "hex_sales = read_sql_query(hex_A, trino)\n", "hex_sales[\"date_\"] = pd.to_datetime(hex_sales[\"date_\"])\n", "hex_sales[[\"facility_id\", \"item_id\", \"sales\", \"lin_extp\", \"exp_extp\", \"para_extp\"]] = hex_sales[\n", "    [\"facility_id\", \"item_id\", \"sales\", \"lin_extp\", \"exp_extp\", \"para_extp\"]\n", "].astype(int)\n", "hex_sales[\"flag\"] = 1\n", "hex_sales.shape"]}, {"cell_type": "code", "execution_count": null, "id": "e8276986-1886-40bf-ba43-54a944a8e714", "metadata": {}, "outputs": [], "source": ["hex_sales.head()"]}, {"cell_type": "markdown", "id": "5cecd09c-a7ff-49a2-ba83-a12f697aca1d", "metadata": {}, "source": ["### Daywise Aggregation"]}, {"cell_type": "code", "execution_count": null, "id": "4aa517b2-dafb-45c4-9291-bbee511b44d1", "metadata": {}, "outputs": [], "source": ["daywise_df = (\n", "    assortment_sales_df[assortment_sales_df[\"hour_\"] < 18]\n", "    .groupby(\n", "        [\n", "            \"city\",\n", "            \"outlet_id\",\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"be_facility_id\",\n", "            \"tat\",\n", "            \"tdate\",\n", "            \"tdow\",\n", "            \"date_\",\n", "            \"dow\",\n", "            \"rank\",\n", "            \"stype_\",\n", "            \"ftype_\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"is_available\": \"sum\",\n", "            \"hour_\": \"nunique\",\n", "            \"hw\": \"sum\",\n", "            \"wt_score\": \"sum\",\n", "            \"quantity\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "assortment_sales_df = assortment_sales_df[assortment_sales_df[\"rank\"] <= 7]\n", "daywise_df[\"avail\"] = daywise_df[\"is_available\"] / daywise_df[\"hour_\"]\n", "daywise_df[\"wt_avail\"] = daywise_df[\"wt_score\"] / daywise_df[\"hw\"]\n", "\n", "daywise_df[\"wt_avail\"] = np.where(\n", "    (daywise_df[\"avail\"] > 0) & (daywise_df[\"wt_avail\"] == 0), 1, daywise_df[\"wt_avail\"]\n", ")\n", "\n", "daywise_df[\"ext_qty_li\"] = daywise_df[\"quantity\"] * (1 + 0.35 * (1 - daywise_df[\"wt_avail\"]))\n", "\n", "daywise_df[\"ext_qty_exp\"] = daywise_df[\"quantity\"] * (5 ** (0.50 * (1 - daywise_df[\"wt_avail\"])))\n", "\n", "daywise_df[\"ext_qty_para\"] = daywise_df[\"quantity\"] * (\n", "    1 + ((1 - daywise_df[\"wt_avail\"]) ** 2) / (4 * 0.08)\n", ")\n", "\n", "daywise_df = daywise_df.merge(hex_sales, on=[\"facility_id\", \"item_id\", \"date_\"], how=\"left\")\n", "daywise_df[\"flag\"] = daywise_df[\"flag\"].fillna(0)\n", "\n", "daywise_df[\"quantity\"] = np.where(\n", "    daywise_df[\"flag\"] == 1, daywise_df[\"sales\"], daywise_df[\"quantity\"]\n", ")\n", "daywise_df[\"ext_qty_li\"] = np.where(\n", "    daywise_df[\"flag\"] == 1, daywise_df[\"lin_extp\"], daywise_df[\"ext_qty_li\"]\n", ")\n", "daywise_df[\"ext_qty_exp\"] = np.where(\n", "    daywise_df[\"flag\"] == 1, daywise_df[\"exp_extp\"], daywise_df[\"ext_qty_exp\"]\n", ")\n", "daywise_df[\"ext_qty_para\"] = np.where(\n", "    daywise_df[\"flag\"] == 1, daywise_df[\"para_extp\"], daywise_df[\"ext_qty_para\"]\n", ")\n", "\n", "daywise_df[\"ext_qty_hybrid\"] = np.where(\n", "    daywise_df[\"stype_\"] == \"premium\",\n", "    daywise_df[\"ext_qty_li\"],\n", "    daywise_df[[\"ext_qty_para\", \"ext_qty_exp\", \"ext_qty_li\"]].max(axis=1),\n", ")\n", "\n", "\n", "wow_bumps = daywise_df.copy()\n", "daywise_df = daywise_df[daywise_df[\"rank\"] <= 5]\n", "daywise_df = daywise_df.drop(\n", "    columns={\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\", \"lin_extp\", \"exp_extp\", \"para_extp\"}\n", ")\n", "daywise_df = daywise_df[daywise_df[\"ext_qty_hybrid\"] > 0]\n", "daywise_df.head(1)"]}, {"cell_type": "markdown", "id": "0ee793e6-d4af-4fcf-8a60-f826c01d90b5", "metadata": {"tags": []}, "source": ["### Weights Calculation for Dates"]}, {"cell_type": "markdown", "id": "e73179e2-b0a0-4517-b979-6e7c4125de52", "metadata": {}, "source": ["#### High & Medium OPD Stores"]}, {"cell_type": "code", "execution_count": null, "id": "c3ba698d-0719-4301-9d28-1d3610933237", "metadata": {}, "outputs": [], "source": ["date_weights_df_1 = daywise_df[\n", "    daywise_df[\"ftype_\"].isin({\"high\", \"medium\"}) & (daywise_df[\"rank\"] <= 5)\n", "][[\"item_id\", \"facility_id\", \"date_\", \"rank\"]]\n", "\n", "date_weights_df_1[\"wr\"] = date_weights_df_1.groupby([\"item_id\", \"facility_id\"])[\"rank\"].rank(\n", "    method=\"dense\", ascending=True\n", ")\n", "\n", "wr_agg = (\n", "    date_weights_df_1.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"total_rank\"})\n", ")\n", "\n", "wr_max = (\n", "    date_weights_df_1.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"max\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"max_rank\"})\n", ")\n", "\n", "date_weights_df_1 = date_weights_df_1.merge(wr_agg, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "date_weights_df_1 = date_weights_df_1.merge(wr_max, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "date_weights_df_1[\"weights\"] = (\n", "    date_weights_df_1[\"max_rank\"] - date_weights_df_1[\"wr\"] + 1\n", ") / date_weights_df_1[\"total_rank\"]\n", "\n", "date_weights_df_1 = date_weights_df_1.drop(columns={\"max_rank\", \"wr\", \"total_rank\", \"rank\"})"]}, {"cell_type": "markdown", "id": "636ac3f0-44d5-47be-912b-3c11dc307766", "metadata": {}, "source": ["#### Low OPD Stores"]}, {"cell_type": "code", "execution_count": null, "id": "eced0e99-2b92-488b-ab27-5385d9ef4d28", "metadata": {}, "outputs": [], "source": ["date_weights_df_2 = daywise_df[(daywise_df[\"ftype_\"] == \"low\") & (daywise_df[\"rank\"] <= 5)][\n", "    [\"item_id\", \"facility_id\", \"date_\", \"ext_qty_hybrid\"]\n", "]\n", "\n", "date_weights_df_2[\"wr\"] = date_weights_df_2.groupby([\"item_id\", \"facility_id\"])[\n", "    \"ext_qty_hybrid\"\n", "].rank(method=\"dense\", ascending=True)\n", "\n", "wr_agg = (\n", "    date_weights_df_2.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"total_rank\"})\n", ")\n", "\n", "wr_max = (\n", "    date_weights_df_2.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"max\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"max_rank\"})\n", ")\n", "\n", "date_weights_df_2 = date_weights_df_2.merge(wr_agg, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "date_weights_df_2 = date_weights_df_2.merge(wr_max, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "date_weights_df_2[\"weights\"] = date_weights_df_2[\"wr\"] / date_weights_df_2[\"total_rank\"]\n", "\n", "date_weights_df_2 = date_weights_df_2.drop(\n", "    columns={\"max_rank\", \"wr\", \"total_rank\", \"ext_qty_hybrid\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "34334c40-d900-43e8-b368-0355f08c81d7", "metadata": {}, "outputs": [], "source": ["date_weights_df = pd.concat([date_weights_df_1, date_weights_df_2])\n", "date_weights_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "e0195fd1-0bae-4cc3-9e43-a3b17ac484a9", "metadata": {}, "outputs": [], "source": ["del [date_weights_df_1, date_weights_df_2, wr_agg, wr_max, hex_sales]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "c2eb8d39-c06e-4fcc-9429-bf0f0b5f402b", "metadata": {}, "source": ["### Weights Merge with DataFrame"]}, {"cell_type": "code", "execution_count": null, "id": "32ba45b3-d4cc-4b0a-8267-5b07b679d804", "metadata": {}, "outputs": [], "source": ["daywise_df = (\n", "    daywise_df[daywise_df[\"rank\"] <= 5]\n", "    .merge(date_weights_df, on=[\"item_id\", \"facility_id\", \"date_\"], how=\"left\")\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "daywise_df[\"transfer_ext_qty\"] = daywise_df[\"ext_qty_hybrid\"] * daywise_df[\"weights\"]\n", "\n", "# daywise_df[\"date_dow\"] = np.where(\n", "#     daywise_df[\"dow\"].isin({0, 1, 2, 3, 4}),\n", "#     \"weekday\",\n", "#     np.where(daywise_df[\"dow\"] == 5, \"saturday\", \"sunday\"),\n", "# )\n", "\n", "# daywise_df[\"transfer_dow\"] = np.where(\n", "#     daywise_df[\"tdow\"].isin({0, 1, 2, 3, 4}),\n", "#     \"weekday\",\n", "#     np.where(daywise_df[\"tdow\"] == 5, \"saturday\", \"sunday\"),\n", "# )\n", "\n", "daywise_df.head(1)"]}, {"cell_type": "markdown", "id": "f503b8f8-1e4b-4b29-a164-5a313aeaf9d0", "metadata": {}, "source": ["## Weekday Weekend Normalisation"]}, {"cell_type": "code", "execution_count": null, "id": "082f7010-7c8a-4424-a3af-0c54ed564812", "metadata": {}, "outputs": [], "source": ["increment_df = (\n", "    wow_bumps.groupby([\"date_\", \"facility_id\"])\n", "    .agg({\"ext_qty_li\": \"sum\"})\n", "    .rename(columns={\"ext_qty_li\": \"quantity\"})\n", "    .reset_index()\n", ")\n", "\n", "increment_df[\"date_\"] = pd.to_datetime(increment_df[\"date_\"])\n", "increment_df[\"dow\"] = pd.to_datetime(increment_df[\"date_\"]).dt.dayofweek\n", "\n", "increment_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "31adc476-958e-4e2d-a51b-c7047e946496", "metadata": {}, "outputs": [], "source": ["increment_df.head()\n", "increment_agg_df = (\n", "    increment_df.groupby([\"facility_id\", \"dow\"]).agg({\"quantity\": percentile}).reset_index()\n", ")\n", "increment_df_fdow = increment_agg_df.copy()\n", "increment_df_fdow.rename(columns={\"dow\": \"tdow\", \"quantity\": \"tdow_quantity\"}, inplace=True)\n", "increment_df_calc = increment_agg_df.merge(increment_df_fdow, on=[\"facility_id\"], how=\"left\")\n", "# fdow_df = increment_agg_df[(increment_agg_df[\"fdow\"] == increment_agg_df[\"dow\"])][\n", "#                         [\"facility_id\", \"fdow_carts\"]\n", "#                     ].drop_duplicates().rename(columns={'carts':'fdow_carts'})\n", "# fin_increment = pd.merge(increment_df_calc,fdow_df, on=['facility_id'], how= 'left')\n", "increment_df_calc[\"bf\"] = increment_df_calc[\"tdow_quantity\"] / increment_df_calc[\"quantity\"]\n", "increment_df_calc[\"bf\"] = increment_df_calc[\"bf\"].fillna(1)\n", "increment_df_calc[\"bf\"] = increment_df_calc[\"bf\"].clip(0.5, 1.5)\n", "increment_df_calc[increment_df_calc[\"facility_id\"] == 271].head(20)"]}, {"cell_type": "markdown", "id": "79abeb8c-dc63-4f19-8c6a-7f8da31a21bc", "metadata": {}, "source": ["### Apply Weekday - Weekend Normalization"]}, {"cell_type": "code", "execution_count": null, "id": "acf4ef75-4808-4518-9d7e-19e34a49f56e", "metadata": {}, "outputs": [], "source": ["transfer_df = (\n", "    daywise_df.groupby(\n", "        [\n", "            \"city\",\n", "            \"be_facility_id\",\n", "            \"facility_id\",\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"stype_\",\n", "            \"ftype_\",\n", "            \"dow\",\n", "            \"tdate\",\n", "            \"tdow\",\n", "        ]\n", "    )\n", "    .agg({\"transfer_ext_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "# transfer_df = transfer_df.merge(increment_agg_df, on=[\"facility_id\"], how=\"left\")\n", "\n", "# transfer_df[[\"w_st\", \"w_su\", \"st_su\"]] = transfer_df[[\"w_st\", \"w_su\", \"st_su\"]].fillna(\n", "#     1\n", "# )\n", "\n", "transfer_df = transfer_df.merge(\n", "    increment_df_calc[[\"facility_id\", \"dow\", \"tdow\", \"bf\"]],\n", "    on=[\"facility_id\", \"dow\", \"tdow\"],\n", "    how=\"left\",\n", ")\n", "\n", "\n", "transfer_df[\"bf\"] = np.where(transfer_df[\"bf\"] > 1.5, 1.5, transfer_df[\"bf\"])\n", "transfer_df[\"bf\"] = np.where(transfer_df[\"bf\"] < 0.5, 0.5, transfer_df[\"bf\"])\n", "transfer_df[\"bf\"] = transfer_df[\"bf\"].fillna(1)\n", "transfer_df[\"slota_transfer_qty\"] = np.round(transfer_df[\"transfer_ext_qty\"] * transfer_df[\"bf\"], 0)\n", "\n", "transfer_df = (\n", "    transfer_df.groupby(\n", "        [\n", "            \"city\",\n", "            \"be_facility_id\",\n", "            \"facility_id\",\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"stype_\",\n", "            \"ftype_\",\n", "            \"tdate\",\n", "        ]\n", "    )\n", "    .agg({\"slota_transfer_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "transfer_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "da1e0f6d-48c1-4e3e-bab6-7f2bcb023ad5", "metadata": {}, "outputs": [], "source": ["del [wow_bumps, assortment_base_df, date_weights_df, assortment_final_df, daywise_df]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "487b8bc7-91b4-4c56-9774-3ace60a07e0d", "metadata": {"tags": []}, "source": ["# Overall Day Extrapolation - excluding Current Date"]}, {"cell_type": "markdown", "id": "63622d94-ab0e-478a-ad47-25f65662ceac", "metadata": {}, "source": ["## Adding hex sales for CPC stores - 6 to 23:59"]}, {"cell_type": "code", "execution_count": null, "id": "59763d0c-c795-4f67-b7b7-249c68f486ff", "metadata": {}, "outputs": [], "source": ["hex_full = f\"\"\"\n", "select\n", "    date_,\n", "    facility_id,\n", "    item_id as item_id,\n", "    sales as sales,\n", "    lin_extp lin_extp,\n", "    exp_extp exp_extp,\n", "    para_extp para_extp\n", "from\n", "    supply_etls.milk_outlet_hex_sales a\n", "inner join (select slot,max(updated_at) updated_at\n", "                        from supply_etls.milk_outlet_hex_sales\n", "                        where \n", "                        updated_at>=current_date group by 1) b\n", "                on a.updated_at = b.updated_at and a.slot = b.slot\n", "where a.slot = '6 to 23'\n", "and a.updated_at>=current_date\n", "\"\"\"\n", "hex_sales = read_sql_query(hex_full, trino)\n", "hex_sales[\"date_\"] = pd.to_datetime(hex_sales[\"date_\"])\n", "hex_sales[[\"facility_id\", \"item_id\", \"sales\", \"lin_extp\", \"exp_extp\", \"para_extp\"]] = hex_sales[\n", "    [\"facility_id\", \"item_id\", \"sales\", \"lin_extp\", \"exp_extp\", \"para_extp\"]\n", "].astype(int)\n", "hex_sales[\"flag\"] = 1\n", "hex_sales.shape"]}, {"cell_type": "code", "execution_count": null, "id": "7d5b55e2-b284-45e3-b999-1a833b7288b3", "metadata": {}, "outputs": [], "source": ["hex_sales.head()"]}, {"cell_type": "markdown", "id": "bc1135d0-4a7c-42fa-bda2-dca8f122d1c0", "metadata": {}, "source": ["### Daywise Aggregation"]}, {"cell_type": "code", "execution_count": null, "id": "4583cfd4-60ed-4183-aa31-1bf4d577a1b2", "metadata": {}, "outputs": [], "source": ["overall_daywise_df = (\n", "    assortment_sales_df[assortment_sales_df[\"rank\"] <= 5]\n", "    .groupby(\n", "        [\n", "            \"city\",\n", "            \"outlet_id\",\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"be_facility_id\",\n", "            \"tat\",\n", "            \"tdate\",\n", "            \"tdow\",\n", "            \"date_\",\n", "            \"winter_flag\",\n", "            \"dow\",\n", "            \"rank\",\n", "            \"stype_\",\n", "            \"ftype_\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"is_available\": \"sum\",\n", "            \"hour_\": \"nunique\",\n", "            \"hw\": \"sum\",\n", "            \"wt_score\": \"sum\",\n", "            \"quantity\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "overall_daywise_df[\"avail\"] = overall_daywise_df[\"is_available\"] / overall_daywise_df[\"hour_\"]\n", "\n", "overall_daywise_df[\"wt_avail\"] = overall_daywise_df[\"wt_score\"] / overall_daywise_df[\"hw\"]\n", "\n", "overall_daywise_df[\"wt_avail\"] = np.where(\n", "    (overall_daywise_df[\"avail\"] > 0) & (overall_daywise_df[\"wt_avail\"] == 0),\n", "    1,\n", "    overall_daywise_df[\"wt_avail\"],\n", ")\n", "\n", "overall_daywise_df[\"ext_qty_li\"] = np.ceil(\n", "    overall_daywise_df[\"quantity\"] * (1 + 0.35 * (1 - overall_daywise_df[\"wt_avail\"]))\n", ")\n", "\n", "overall_daywise_df[\"ext_qty_exp\"] = np.ceil(\n", "    overall_daywise_df[\"quantity\"] * (5 ** (0.50 * (1 - overall_daywise_df[\"wt_avail\"])))\n", ")\n", "\n", "overall_daywise_df[\"ext_qty_para\"] = np.ceil(\n", "    overall_daywise_df[\"quantity\"] * (1 + ((1 - overall_daywise_df[\"wt_avail\"]) ** 2) / (4 * 0.08))\n", ")\n", "\n", "\n", "overall_daywise_df = overall_daywise_df.merge(\n", "    hex_sales, on=[\"facility_id\", \"item_id\", \"date_\"], how=\"left\"\n", ")\n", "overall_daywise_df[\"flag\"] = overall_daywise_df[\"flag\"].fillna(0)\n", "\n", "overall_daywise_df[\"quantity\"] = np.where(\n", "    overall_daywise_df[\"flag\"] == 1, overall_daywise_df[\"sales\"], overall_daywise_df[\"quantity\"]\n", ")\n", "overall_daywise_df[\"ext_qty_li\"] = np.where(\n", "    overall_daywise_df[\"flag\"] == 1,\n", "    overall_daywise_df[\"lin_extp\"],\n", "    overall_daywise_df[\"ext_qty_li\"],\n", ")\n", "overall_daywise_df[\"ext_qty_exp\"] = np.where(\n", "    overall_daywise_df[\"flag\"] == 1,\n", "    overall_daywise_df[\"exp_extp\"],\n", "    overall_daywise_df[\"ext_qty_exp\"],\n", ")\n", "overall_daywise_df[\"ext_qty_para\"] = np.where(\n", "    overall_daywise_df[\"flag\"] == 1,\n", "    overall_daywise_df[\"para_extp\"],\n", "    overall_daywise_df[\"ext_qty_para\"],\n", ")\n", "\n", "\n", "overall_daywise_df[\"high_ext_qty\"] = np.where(\n", "    overall_daywise_df[\"ftype_\"] == \"high\",\n", "    np.where(\n", "        (overall_daywise_df[\"stype_\"] == \"top\") & (overall_daywise_df[\"winter_flag\"] == 1),\n", "        overall_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].mean(axis=1),\n", "        overall_daywise_df[[\"ext_qty_exp\", \"ext_qty_para\"]].mean(axis=1),\n", "    ),\n", "    0,\n", ")\n", "\n", "overall_daywise_df[\"medium_ext_qty\"] = np.where(\n", "    overall_daywise_df[\"ftype_\"] == \"medium\",\n", "    np.where(\n", "        overall_daywise_df[\"stype_\"] == \"bottom\",\n", "        np.where(\n", "            overall_daywise_df[\"winter_flag\"] == 1,\n", "            overall_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].mean(axis=1),\n", "            overall_daywise_df[[\"ext_qty_li\", \"ext_qty_para\", \"ext_qty_exp\"]].median(axis=1),\n", "        ),\n", "        np.where(\n", "            overall_daywise_df[\"stype_\"] == \"premium\",\n", "            overall_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "            np.where(\n", "                overall_daywise_df[\"wt_avail\"] >= 0.7,\n", "                overall_daywise_df[[\"ext_qty_para\", \"ext_qty_exp\"]].mean(axis=1),\n", "                overall_daywise_df[[\"ext_qty_exp\", \"ext_qty_li\", \"ext_qty_para\"]].mean(axis=1),\n", "            ),\n", "        ),\n", "    ),\n", "    0,\n", ")\n", "\n", "overall_daywise_df[\"low_ext_qty\"] = np.where(\n", "    overall_daywise_df[\"ftype_\"] == \"low\",\n", "    np.where(\n", "        overall_daywise_df[\"stype_\"] == \"bottom\",\n", "        overall_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\"]].mean(axis=1),\n", "        np.where(\n", "            overall_daywise_df[\"stype_\"] == \"premium\",\n", "            overall_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "            overall_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].max(axis=1),\n", "        ),\n", "    ),\n", "    0,\n", ")\n", "\n", "overall_daywise_df[\"ext_qty_hybrid\"] = (\n", "    overall_daywise_df[\"high_ext_qty\"]\n", "    + overall_daywise_df[\"medium_ext_qty\"]\n", "    + overall_daywise_df[\"low_ext_qty\"]\n", ")\n", "\n", "overall_daywise_df = overall_daywise_df.drop(columns={\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"})\n", "overall_daywise_df = overall_daywise_df[overall_daywise_df[\"ext_qty_hybrid\"] > 0]\n", "overall_daywise_df.head(1)"]}, {"cell_type": "markdown", "id": "b9548b3e-e245-41d9-872e-ded751b20961", "metadata": {}, "source": ["### Weights Calculation for Dates"]}, {"cell_type": "markdown", "id": "a0b03e17-067f-40ad-b223-8ee662962d3b", "metadata": {}, "source": ["#### High & Low OPD Stores"]}, {"cell_type": "code", "execution_count": null, "id": "54c20581-0bdc-4780-b6ef-7cf4eeaa471b", "metadata": {}, "outputs": [], "source": ["date_weights_df_1 = overall_daywise_df[\n", "    (overall_daywise_df[\"ftype_\"].isin({\"high\", \"medium\"}))\n", "    & (overall_daywise_df[\"ext_qty_hybrid\"] != 0)\n", "][[\"item_id\", \"facility_id\", \"date_\", \"rank\"]]\n", "\n", "date_weights_df_1[\"wr\"] = date_weights_df_1.groupby([\"item_id\", \"facility_id\"])[\"rank\"].rank(\n", "    method=\"dense\", ascending=True\n", ")\n", "\n", "wr_agg = (\n", "    date_weights_df_1.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"total_rank\"})\n", ")\n", "\n", "wr_max = (\n", "    date_weights_df_1.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"max\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"max_rank\"})\n", ")\n", "\n", "date_weights_df_1 = date_weights_df_1.merge(wr_agg, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "date_weights_df_1 = date_weights_df_1.merge(wr_max, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "date_weights_df_1[\"weights\"] = (\n", "    date_weights_df_1[\"max_rank\"] - date_weights_df_1[\"wr\"] + 1\n", ") / date_weights_df_1[\"total_rank\"]\n", "\n", "date_weights_df_1 = date_weights_df_1.drop(columns={\"max_rank\", \"wr\", \"total_rank\", \"rank\"})"]}, {"cell_type": "markdown", "id": "0980405b-c5a5-4ea9-8a66-244cfe245dae", "metadata": {}, "source": ["#### Low OPD Stores"]}, {"cell_type": "code", "execution_count": null, "id": "2c3e0f82-4804-49c2-bccf-71ec5bcd2fd6", "metadata": {}, "outputs": [], "source": ["date_weights_df_2 = overall_daywise_df[\n", "    (overall_daywise_df[\"ftype_\"] == \"low\") & ((overall_daywise_df[\"ext_qty_hybrid\"] != 0))\n", "][[\"item_id\", \"facility_id\", \"date_\", \"ext_qty_hybrid\"]]\n", "\n", "date_weights_df_2[\"wr\"] = date_weights_df_2.groupby([\"item_id\", \"facility_id\"])[\n", "    \"ext_qty_hybrid\"\n", "].rank(method=\"dense\", ascending=True)\n", "\n", "wr_agg = (\n", "    date_weights_df_2.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"total_rank\"})\n", ")\n", "\n", "wr_max = (\n", "    date_weights_df_2.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"max\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"max_rank\"})\n", ")\n", "\n", "date_weights_df_2 = date_weights_df_2.merge(wr_agg, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "date_weights_df_2 = date_weights_df_2.merge(wr_max, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "date_weights_df_2[\"weights\"] = date_weights_df_2[\"wr\"] / date_weights_df_2[\"total_rank\"]\n", "\n", "date_weights_df_2 = date_weights_df_2.drop(\n", "    columns={\"max_rank\", \"wr\", \"total_rank\", \"ext_qty_hybrid\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d133377b-4405-41bd-8ef1-e2cc3975e655", "metadata": {}, "outputs": [], "source": ["date_weights_df = pd.concat([date_weights_df_1, date_weights_df_2])\n", "date_weights_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "5ad17452-fa45-4cdb-a849-cc9977c82b93", "metadata": {}, "outputs": [], "source": ["del [date_weights_df_1, date_weights_df_2, wr_agg, wr_max]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "6a49c445-d217-45c4-a3b4-15df46e773a9", "metadata": {}, "source": ["### Weights Merge with DataFrame"]}, {"cell_type": "code", "execution_count": null, "id": "9bceb7be-e704-42a4-8919-14f96fd1cdff", "metadata": {}, "outputs": [], "source": ["overall_daywise_df = (\n", "    overall_daywise_df.merge(date_weights_df, on=[\"item_id\", \"facility_id\", \"date_\"], how=\"inner\")\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "overall_daywise_df[\"transfer_ext_qty\"] = (\n", "    overall_daywise_df[\"ext_qty_hybrid\"] * overall_daywise_df[\"weights\"]\n", ")\n", "\n", "overall_daywise_df.head(1)"]}, {"cell_type": "markdown", "id": "691f1cfd-44cc-4d5e-966f-a92fd4e7206c", "metadata": {}, "source": ["### Apply Weekday - Weekend Normalization"]}, {"cell_type": "code", "execution_count": null, "id": "5081aba7-3f8c-48c9-a8ab-8dd12236a196", "metadata": {}, "outputs": [], "source": ["tot_transfer_df = (\n", "    overall_daywise_df.groupby(\n", "        [\n", "            \"city\",\n", "            \"be_facility_id\",\n", "            \"facility_id\",\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"stype_\",\n", "            \"ftype_\",\n", "            \"dow\",\n", "            \"tdate\",\n", "            \"tdow\",\n", "        ]\n", "    )\n", "    .agg({\"transfer_ext_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "tot_transfer_df = tot_transfer_df.merge(\n", "    increment_df_calc[[\"facility_id\", \"dow\", \"tdow\", \"bf\"]],\n", "    on=[\"facility_id\", \"dow\", \"tdow\"],\n", "    how=\"left\",\n", ")\n", "\n", "\n", "tot_transfer_df[\"bf\"] = np.where(tot_transfer_df[\"bf\"] > 1.2, 1.2, tot_transfer_df[\"bf\"])\n", "\n", "tot_transfer_df[\"bf\"] = np.where(tot_transfer_df[\"bf\"] < 0.8, 0.8, tot_transfer_df[\"bf\"])\n", "\n", "tot_transfer_df[\"tot_transfer_qty\"] = np.round(\n", "    tot_transfer_df[\"transfer_ext_qty\"] * tot_transfer_df[\"bf\"], 0\n", ")\n", "\n", "tot_transfer_df = (\n", "    tot_transfer_df.groupby(\n", "        [\n", "            \"city\",\n", "            \"be_facility_id\",\n", "            \"facility_id\",\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"stype_\",\n", "            \"ftype_\",\n", "            \"tdate\",\n", "        ]\n", "    )\n", "    .agg({\"tot_transfer_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "tot_transfer_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "3b454810-1884-41a5-92d5-948f45a9ba93", "metadata": {}, "outputs": [], "source": ["del [ranking_df, date_weights_df]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "b41c163b-74ba-4b73-901a-380a64897182", "metadata": {}, "source": ["## Top SKU Availability Feedback for Winter Stores"]}, {"cell_type": "code", "execution_count": null, "id": "276a3590-8451-49ab-90bc-b1ad113b36c9", "metadata": {"tags": []}, "outputs": [], "source": ["try:\n", "    feedback_input_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::city-feedback-input\",\n", "    )\n", "\n", "except:\n", "    feedback_input_df = pd.read_csv(\"city_feedback_input.csv\")\n", "\n", "feedback_input_df[\"feedback_flag\"] = np.where(feedback_input_df[\"feedback\"] == \"no\", 0, 1)\n", "\n", "feedback_input_df = feedback_input_df.drop(columns={\"feedback\"})"]}, {"cell_type": "code", "execution_count": null, "id": "14f87296-6fdc-419b-8d70-dce9b9874b2f", "metadata": {}, "outputs": [], "source": ["l2_date = pd.to_datetime(today_date) - <PERSON><PERSON><PERSON>(days=2)\n", "\n", "top_sku_feedback_df = assortment_sales_df[\n", "    (assortment_sales_df[\"stype_\"] == \"top\") & (assortment_sales_df[\"hour_\"] > 19)\n", "]\n", "\n", "top_sku_feedback_df = (\n", "    top_sku_feedback_df[\n", "        (top_sku_feedback_df[\"date_\"] >= l2_date) & (top_sku_feedback_df[\"date_\"] < today_date)\n", "    ]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "top_sku_feedback_df = top_sku_feedback_df.merge(feedback_input_df, on=[\"city\"], how=\"left\")\n", "\n", "top_sku_feedback_df[\"feedback_flag\"] = top_sku_feedback_df[\"feedback_flag\"].fillna(0)\n", "\n", "top_sku_feedback_df = (\n", "    top_sku_feedback_df[top_sku_feedback_df[\"feedback_flag\"] == 1]\n", "    .groupby([\"facility_id\", \"item_id\"])\n", "    .agg({\"hour_\": \"count\", \"is_available\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "top_sku_feedback_df[\"avail\"] = top_sku_feedback_df[\"is_available\"] / top_sku_feedback_df[\"hour_\"]\n", "\n", "top_sku_feedback_df[\"flag\"] = np.where(top_sku_feedback_df[\"avail\"] < 0.25, 1.1, 1)\n", "\n", "top_sku_feedback_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "fd4c3598-5586-433c-b5c8-5b35b347fe05", "metadata": {}, "outputs": [], "source": ["tot_transfer_df = tot_transfer_df.merge(\n", "    top_sku_feedback_df.drop(columns={\"hour_\", \"is_available\", \"avail\"}),\n", "    on=[\"facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "tot_transfer_df[\"flag\"] = tot_transfer_df[\"flag\"].fillna(1)\n", "\n", "tot_transfer_df[\"tot_transfer_qty\"] = np.ceil(\n", "    tot_transfer_df[\"tot_transfer_qty\"] * tot_transfer_df[\"flag\"]\n", ").astype(int)\n", "\n", "tot_transfer_df = tot_transfer_df.drop(columns={\"flag\"})\n", "\n", "tot_transfer_df.head(1)"]}, {"cell_type": "markdown", "id": "320782ef-03ed-423c-b343-d8bada7d9a62", "metadata": {"tags": []}, "source": ["# Merge All DataFrames with Assortment Base"]}, {"cell_type": "code", "execution_count": null, "id": "02cb900e-6856-4e99-b862-e3657eebcd1f", "metadata": {}, "outputs": [], "source": ["final_df = (\n", "    assortment_df[[\"city\", \"facility_id\", \"outlet_id\", \"item_id\", \"be_facility_id\", \"tdate\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "final_df = final_df.merge(\n", "    transfer_df[[\"facility_id\", \"ftype_\", \"item_id\", \"stype_\", \"tdate\", \"slota_transfer_qty\"]],\n", "    on=[\"facility_id\", \"item_id\", \"tdate\"],\n", "    how=\"left\",\n", ")\n", "\n", "final_df = final_df.merge(\n", "    tot_transfer_df[[\"facility_id\", \"item_id\", \"tdate\", \"tot_transfer_qty\"]],\n", "    on=[\"facility_id\", \"item_id\", \"tdate\"],\n", "    how=\"left\",\n", ")\n", "\n", "final_df[\"tot_transfer_qty\"] = np.where(\n", "    final_df[\"tot_transfer_qty\"].isna(),\n", "    final_df[\"slota_transfer_qty\"],\n", "    final_df[\"tot_transfer_qty\"],\n", ")\n", "\n", "final_df[\"tot_transfer_qty\"] = final_df[[\"tot_transfer_qty\", \"slota_transfer_qty\"]].max(axis=1)\n", "\n", "final_df[\"slotb_transfer_qty\"] = np.abs(\n", "    final_df[\"tot_transfer_qty\"] - final_df[\"slota_transfer_qty\"]\n", ")\n", "\n", "final_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c7250959-e51c-481b-a95f-73f832b6c112", "metadata": {}, "outputs": [], "source": ["final_df.slota_transfer_qty.sum(), final_df.tot_transfer_qty.sum()"]}, {"cell_type": "markdown", "id": "74384d8e-015c-4e35-a7b6-d4767629c5c8", "metadata": {}, "source": ["# DS Model Plugin"]}, {"cell_type": "code", "execution_count": null, "id": "f30917d9-bd30-445d-aeef-9df8fa6d7648", "metadata": {}, "outputs": [], "source": ["ds_query = f\"\"\"\n", "    SELECT  DATE(current_replenishment_ts_ist) AS tdate, facility_id, item_id, AVG(max_qty) AS ds_qty\n", "    FROM ds_etls.demand_forecast_item_min_max_quantity_milk\n", "    WHERE updated_at_ist >= current_date - interval '10' day\n", "    GROUP BY 1,2,3\n", "\"\"\"\n", "\n", "ds_input_df = read_sql_query(ds_query, trino)\n", "\n", "ds_input_df[\"tdate\"] = pd.to_datetime(ds_input_df[\"tdate\"])\n", "\n", "ds_input_df[\"ds_qty\"] = ds_input_df[\"ds_qty\"].fillna(0).astype(int)\n", "\n", "ds_input_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "e8b060ac-b5a0-47b7-963f-249bc75fba70", "metadata": {"tags": []}, "outputs": [], "source": ["ds_be_df = (\n", "    final_df[[\"be_facility_id\", \"facility_id\", \"item_id\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "ds_input_df = pd.merge(ds_be_df, ds_input_df, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "\n", "try:\n", "    ds_model_be = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::ds-model-input-transfer\",\n", "    )\n", "\n", "except:\n", "    ds_model_be = pd.read_csv(\"ds_model_input.csv\")\n", "\n", "ds_model_be = ds_model_be.rename(columns={\"backend_facility_id\": \"be_facility_id\"}).drop(\n", "    columns={\"backend_name\"}\n", ")\n", "\n", "ds_model_be[\"be_facility_id\"] = ds_model_be[\"be_facility_id\"].astype(int)\n", "\n", "ds_input_df = pd.merge(ds_input_df, ds_model_be, on=[\"be_facility_id\"], how=\"inner\")\n", "\n", "ds_input_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "840e5da1-2b06-4477-b858-efe4740b1c3b", "metadata": {}, "outputs": [], "source": ["final_df = pd.merge(\n", "    final_df,\n", "    ds_input_df[[\"tdate\", \"facility_id\", \"item_id\", \"ds_qty\"]],\n", "    on=[\"tdate\", \"facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "final_df[\"tot_transfer_qty\"] = np.where(\n", "    final_df[\"ds_qty\"].isna(), final_df[\"tot_transfer_qty\"], final_df[\"ds_qty\"]\n", ")\n", "\n", "final_df[[\"tot_transfer_qty\"]] = final_df[[\"tot_transfer_qty\"]].fillna(0).astype(int)\n", "\n", "final_df[\"ds_f\"] = np.where(final_df[\"ds_qty\"].isna(), 0, 1)\n", "\n", "final_df = final_df.drop(columns={\"ds_qty\"})\n", "\n", "final_df[\"slota_transfer_qty\"] = np.where(\n", "    final_df[\"ds_f\"] == 0,\n", "    final_df[\"slota_transfer_qty\"],\n", "    np.round(final_df[\"tot_transfer_qty\"] * 0.85),\n", ").astype(int)\n", "\n", "final_df[\"slotb_transfer_qty\"] = final_df[\"tot_transfer_qty\"] - final_df[\"slota_transfer_qty\"]\n", "final_df = final_df[final_df[\"slota_transfer_qty\"] > 0]\n", "final_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "b100033d-d3ed-487d-93fd-b1a6b71b2332", "metadata": {}, "outputs": [], "source": ["final_df.slota_transfer_qty.sum(), final_df.tot_transfer_qty.sum()"]}, {"cell_type": "code", "execution_count": null, "id": "922b1b54-94b2-48d5-8c09-2e3020adf065", "metadata": {"tags": []}, "outputs": [], "source": ["del [ds_be_df, ds_model_be]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "15270423-baa4-413a-ba63-0632daafdd94", "metadata": {}, "source": ["# New Store Plugin"]}, {"cell_type": "code", "execution_count": null, "id": "268591a0-9480-4b9d-89b9-408eb892827d", "metadata": {}, "outputs": [], "source": ["new_store_forecast_query = f\"\"\"\n", "   WITH base AS (\n", "        SELECT facility_id, item_id, outlet_id, date_, \n", "        CASE WHEN item_nearby_fps IS NULL THEN 0 ELSE item_nearby_fps END AS item_nearby_fps,\n", "        CASE WHEN item_recent_fps IS NULL THEN 0 ELSE item_recent_fps END AS item_recent_fps, \n", "        CASE WHEN item_avail_fps IS NULL THEN 0 ELSE item_avail_fps END AS item_avail_fps\n", "        FROM (\n", "            SELECT a.* FROM supply_etls.new_store_forecast_logs a\n", "            JOIN (\n", "                SELECT facility_id, item_id, date_, MAX(updated_at) AS updated_at\n", "                FROM supply_etls.new_store_forecast_logs\n", "                GROUP BY 1,2,3\n", "            ) b ON a.facility_id = b.facility_id AND a.item_id = b.item_id AND a.date_ = b.date_ AND a.updated_at = b.updated_at\n", "            WHERE a.category IN ('Milk') AND DATE(a.date_) IN (DATE('{today_date}') + interval '1' day)\n", "        )\n", "    ),\n", "\n", "    min_max_base AS (\n", "        SELECT facility_id, outlet_id, item_id, DATE(date_) AS tdate, item_nearby_fps, item_recent_fps, item_avail_fps\n", "        FROM base\n", "    ),\n", "    \n", "    store_age_base AS (\n", "        WITH first_outbound AS (\n", "            SELECT outlet_id, DATE(od.cart_checkout_ts_ist) AS f_date\n", "            FROM dwh.fact_sales_order_details od \n", "            WHERE od.order_create_dt_ist > DATE('{today_date}') - interval '30' day\n", "            AND od.is_internal_order = false \n", "            AND od.outlet_id IN (SELECT DISTINCT outlet_id FROM min_max_base)\n", "            AND (od.order_type NOT LIKE '%%internal%%' OR od.order_type IS NULL) AND od.order_current_status = 'DELIVERED'\n", "            GROUP BY 1,2\n", "            having  count(distinct order_id)>10\n", "        )\n", "        \n", "        SELECT outlet_id, min(f_date) as start_date, DATE_DIFF('day', min(f_date), (DATE('{today_date}') + interval '1' day)) AS age\n", "        FROM first_outbound\n", "        group by 1\n", "    ),\n", "    \n", "    be_mapping AS (\n", "        SELECT DISTINCT item_id, outlet_id, cb.facility_id AS be_facility_id\n", "        FROM rpc.item_outlet_tag_mapping tm\n", "        JOIN retail.console_outlet cb ON cb.id = CAST(tag_value AS int) AND cb.active = 1 AND cb.lake_active_record\n", "        WHERE tm.active = 1 AND tm.tag_type_id = 8 AND tm.lake_active_record\n", "    )\n", "\n", "    SELECT a.*, start_date, be_facility_id, cl.name AS city,\n", "    CASE WHEN b.age IS NULL THEN 0 ELSE age END AS age\n", "    FROM min_max_base a\n", "    LEFT JOIN store_age_base b ON a.outlet_id = b.outlet_id\n", "    JOIN be_mapping bm ON bm.item_id = a.item_id AND bm.outlet_id = a.outlet_id\n", "    JOIN retail.console_outlet co ON co.id = a.outlet_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "    LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id AND cl.lake_active_record\n", "    \n", "\"\"\"\n", "new_store_df = pd.read_sql_query(new_store_forecast_query, trino)\n", "new_store_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "50d183d9-18c6-46d3-adef-99cc112e3762", "metadata": {}, "outputs": [], "source": ["new_store_df_tot = (\n", "    new_store_df.groupby([\"facility_id\"])\n", "    .agg({\"item_nearby_fps\": \"sum\", \"item_recent_fps\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_nearby_fps\": \"near\", \"item_recent_fps\": \"recent\"})\n", ")\n", "new_store_df = new_store_df.merge(new_store_df_tot, on=[\"facility_id\"], how=\"left\")\n", "new_store_df[\"final_cpd\"] = np.round(new_store_df[\"near\"] * 0.75 + new_store_df[\"recent\"] * 0.25, 0)\n", "new_store_df = new_store_df[\n", "    [\"city\", \"be_facility_id\", \"facility_id\", \"outlet_id\", \"item_id\", \"tdate\", \"age\", \"final_cpd\"]\n", "]\n", "new_store_df.tail()"]}, {"cell_type": "code", "execution_count": null, "id": "cda1f80d-d0e3-4cb4-a38b-07dde10d6736", "metadata": {}, "outputs": [], "source": ["## sister_store\n", "\n", "sis = f\"\"\"\n", "            select\n", "            distinct\n", "            zone as city,\n", "            facility_id,\n", "            sister_store_facility_id as s_fac\n", "            \n", "            from\n", "            \n", "            supply_etls.e3_new_darkstores\n", "            where final_ob_date >= current_date - interval '30' day\n", "            and sister_store_facility_id <>0\n", "\"\"\"\n", "sis_df = pd.read_sql_query(sis, trino)\n", "\n", "sis_store_list = tuple(list(sis_df[\"facility_id\"].unique()) + ([-1, -2]))\n", "len(sis_store_list)"]}, {"cell_type": "markdown", "id": "2a575582-5796-4e23-8339-963a2b28b07c", "metadata": {"tags": []}, "source": ["### BE item conrti"]}, {"cell_type": "code", "execution_count": null, "id": "ccfaefe9-2567-4bbc-a885-0e5c904609c9", "metadata": {}, "outputs": [], "source": ["contri_base = overall_daywise_df[\n", "    [\"city\", \"be_facility_id\", \"facility_id\", \"item_id\", \"quantity\"]\n", "].copy()\n", "\n", "be_contri = (\n", "    overall_daywise_df.groupby([\"be_facility_id\", \"item_id\"]).agg({\"quantity\": \"sum\"}).reset_index()\n", ")\n", "\n", "be_agri = (\n", "    be_contri.groupby([\"be_facility_id\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"quantity\": \"tot_quan\"})\n", ")\n", "\n", "be_contri = be_contri.merge(be_agri, on=[\"be_facility_id\"], how=\"left\")\n", "be_contri[\"be_share\"] = be_contri[\"quantity\"] / be_contri[\"tot_quan\"]\n", "be_contri.drop(columns={\"quantity\", \"tot_quan\"}, inplace=True)\n", "be_contri.head(10)"]}, {"cell_type": "markdown", "id": "8908b54b-1dcf-49ab-8690-df8d9321063d", "metadata": {"tags": []}, "source": ["### City item conrti"]}, {"cell_type": "code", "execution_count": null, "id": "94e66e9c-81aa-41b2-8024-66a0b929f139", "metadata": {}, "outputs": [], "source": ["city_contri = overall_daywise_df.groupby([\"city\", \"item_id\"]).agg({\"quantity\": \"sum\"}).reset_index()\n", "\n", "city_agri = (\n", "    city_contri.groupby([\"city\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"quantity\": \"tot_quan\"})\n", ")\n", "\n", "city_contri = city_contri.merge(city_agri, on=[\"city\"], how=\"left\")\n", "city_contri[\"city_share\"] = city_contri[\"quantity\"] / city_contri[\"tot_quan\"]\n", "city_contri.drop(columns={\"quantity\", \"tot_quan\"}, inplace=True)\n", "city_contri.head(7)"]}, {"cell_type": "markdown", "id": "3b11acbe-093e-4a61-90dd-a33c68cc2ce5", "metadata": {"tags": []}, "source": ["### Sister item conrti"]}, {"cell_type": "code", "execution_count": null, "id": "0ce63751-0fed-4164-9091-cb926c90d88a", "metadata": {}, "outputs": [], "source": ["store_contri = (\n", "    overall_daywise_df[overall_daywise_df[\"facility_id\"].isin(sis_store_list)]\n", "    .groupby([\"facility_id\", \"item_id\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"facility_id\": \"s_fac\"})\n", ")\n", "\n", "store_agri = (\n", "    store_contri.groupby([\"s_fac\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"quantity\": \"tot_quan\"})\n", ")\n", "\n", "store_contri = store_contri.merge(store_agri, on=[\"s_fac\"], how=\"left\")\n", "store_contri[\"fac_share\"] = store_contri[\"quantity\"] / store_contri[\"tot_quan\"]\n", "store_contri = store_contri.merge(sis_df[[\"s_fac\", \"facility_id\"]], on=[\"s_fac\"], how=\"left\")\n", "store_contri = store_contri.dropna()\n", "store_contri.drop(columns={\"s_fac\", \"quantity\", \"tot_quan\"}, inplace=True)\n", "store_contri.head(7)"]}, {"cell_type": "code", "execution_count": null, "id": "3d759516-fa93-4f4e-bf5f-50e4519bb0b1", "metadata": {}, "outputs": [], "source": ["new_df = new_store_df.merge(be_contri, on=[\"be_facility_id\", \"item_id\"], how=\"left\")\n", "new_df = new_df.merge(city_contri, on=[\"city\", \"item_id\"], how=\"left\")\n", "new_df = new_df.merge(store_contri, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "new_df = new_df.fillna(0)\n", "new_df[\"final_share\"] = np.where(\n", "    new_df[\"fac_share\"] != 0,\n", "    new_df[\"fac_share\"],\n", "    np.where(new_df[\"city_share\"] != 0, new_df[\"city_share\"], new_df[\"be_share\"]),\n", ")\n", "new_df[\"tot_transfer_qty\"] = np.ceil(new_df[\"final_cpd\"] * new_df[\"final_share\"]).clip(1, 1000)\n", "new_df.tail()"]}, {"cell_type": "code", "execution_count": null, "id": "97950f78-b8fb-40ca-acc3-2dc2f23505e1", "metadata": {"tags": []}, "outputs": [], "source": ["new_store_df = (\n", "    new_df[new_df[\"age\"] <= 10][\n", "        [\n", "            \"city\",\n", "            \"outlet_id\",\n", "            \"be_facility_id\",\n", "            \"tdate\",\n", "            \"item_id\",\n", "            \"facility_id\",\n", "            \"tot_transfer_qty\",\n", "        ]\n", "    ]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "new_store_df[\"slota_transfer_qty\"] = np.ceil(0.85 * new_store_df[\"tot_transfer_qty\"]).astype(int)\n", "\n", "new_store_df[\"slotb_transfer_qty\"] = np.where(\n", "    new_store_df[\"tot_transfer_qty\"] - new_store_df[\"slota_transfer_qty\"] < 0,\n", "    0,\n", "    new_store_df[\"tot_transfer_qty\"] - new_store_df[\"slota_transfer_qty\"],\n", ")\n", "new_store_df[\"ftype_\"] = \"medium\"\n", "new_store_df = new_store_df.merge(\n", "    sku_rank_df[[\"city\", \"item_id\", \"stype_\"]], on=[\"city\", \"item_id\"], how=\"left\"\n", ")\n", "new_store_df[\"stype_\"] = new_store_df[\"stype_\"].fillna(\"low\")\n", "new_store_df[\"ds_f\"] = 0\n", "new_store_df = (\n", "    new_store_df[\n", "        [\n", "            \"city\",\n", "            \"facility_id\",\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"be_facility_id\",\n", "            \"tdate\",\n", "            \"ftype_\",\n", "            \"stype_\",\n", "            \"slota_transfer_qty\",\n", "            \"tot_transfer_qty\",\n", "            \"slotb_transfer_qty\",\n", "            \"ds_f\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "new_store_df[\"tdate\"] = pd.to_datetime(new_store_df[\"tdate\"])"]}, {"cell_type": "code", "execution_count": null, "id": "6bd7e9c9-a1e7-400f-af8f-7f7a63f137d4", "metadata": {}, "outputs": [], "source": ["# new_store_df[new_store_df.facility_id == 3086].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "7454f459-c36f-4f08-8524-d9e749d45fab", "metadata": {}, "outputs": [], "source": ["data = final_df[[\"facility_id\", \"item_id\"]].drop_duplicates().reset_index().drop(columns={\"index\"})\n", "\n", "data = pd.merge(\n", "    data,\n", "    new_store_df[[\"facility_id\", \"item_id\", \"tot_transfer_qty\"]].drop_duplicates(),\n", "    on=[\"facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "data = (\n", "    data[data[\"tot_transfer_qty\"].isna()].reset_index().drop(columns={\"index\", \"tot_transfer_qty\"})\n", ")\n", "\n", "final_df = pd.merge(final_df, data, on=[\"item_id\", \"facility_id\"], how=\"inner\")\n", "\n", "final_df = pd.concat([final_df, new_store_df])\n", "\n", "final_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "759867da-fe89-492f-81f6-bddcf1730aca", "metadata": {}, "outputs": [], "source": ["final_df.slota_transfer_qty.sum(), final_df.tot_transfer_qty.sum()"]}, {"cell_type": "code", "execution_count": null, "id": "302439f8-ebe9-4c61-9430-dade17f30663", "metadata": {}, "outputs": [], "source": ["final_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b237546a-66b8-40cf-9924-ef393f3aa330", "metadata": {}, "outputs": [], "source": ["final_df[\"si_factor\"] = 1\n", "final_df[\"factor\"] = 1"]}, {"cell_type": "raw", "id": "59cd479b-4e70-4350-b551-16cfe44316fb", "metadata": {}, "source": ["bump_query = f\"\"\"with base as ( \n", "        select \n", "        date_ist,\n", "        updated_at,\n", "        city,\n", "        be_facility_id,\n", "        facility_id,\n", "        item_id ,\n", "        be_f,\n", "        city_f,\n", "        store_f,\n", "        rank() over (partition by date_ist order by updated_at desc) as rk\n", "        from \n", "        supply_etls.milk_ordering_bump_factors\n", "        where date_ist>= current_date - interval '3' day\n", "\n", "    )\n", "    \n", "        select\n", "        date_ist + interval '2' day as tdate,\n", "        be_facility_id,\n", "        item_id ,\n", "        facility_id,\n", "        be_f,\n", "        city_f,\n", "        store_f\n", "        from base\n", "        where rk = 1\"\"\"\n", "\n", "bump_df = pd.read_sql_query(bump_query, trino)\n", "\n", "bump_df[\"tdate\"] = pd.to_datetime(bump_df[\"tdate\"])\n", "bump_df.shape"]}, {"cell_type": "raw", "id": "a49770ad-1688-42aa-899a-4084259805b9", "metadata": {}, "source": ["final_df.head()"]}, {"cell_type": "raw", "id": "4d2b6e0c-08b4-4cdd-9ccc-fef8f8d7005f", "metadata": {}, "source": ["final_df = final_df.merge(\n", "    bump_df[[\"tdate\", \"item_id\", \"facility_id\", \"be_f\", \"city_f\", \"store_f\"]],\n", "    on=[\"item_id\", \"facility_id\", \"tdate\"],\n", "    how=\"left\",\n", ")\n", "final_df[[\"be_f\", \"city_f\", \"store_f\"]] = final_df[\n", "    [\"be_f\", \"city_f\", \"store_f\"]\n", "].fillna(1)\n", "final_df[\"slota_transfer_qty\"] = (\n", "    final_df[\"slota_transfer_qty\"]\n", "    * final_df[\"be_f\"]\n", "    * final_df[\"city_f\"]\n", "    * final_df[\"store_f\"]\n", ")\n", "final_df[\"tot_transfer_qty\"] = (\n", "    final_df[\"tot_transfer_qty\"]\n", "    * final_df[\"be_f\"]\n", "    * final_df[\"city_f\"]\n", "    * final_df[\"store_f\"]\n", ")\n", "final_df[\"slotb_transfer_qty\"] = (\n", "    final_df[\"slotb_transfer_qty\"]\n", "    * final_df[\"be_f\"]\n", "    * final_df[\"city_f\"]\n", "    * final_df[\"store_f\"]\n", ")\n", "\n", "\n", "final_df.drop(columns={\"be_f\", \"city_f\", \"store_f\"}, inplace=True)\n", "final_df.head()"]}, {"cell_type": "markdown", "id": "e512e9d8-fe24-4ae8-aafc-d0b3d4ca4015", "metadata": {}, "source": ["# Manual Inputs"]}, {"cell_type": "markdown", "id": "0c0ce01f-caa6-4954-893b-361de7c827d6", "metadata": {}, "source": ["## Demand Replication"]}, {"cell_type": "raw", "id": "35bc84f8-21c9-433a-8489-6f3c3fd4042c", "metadata": {"tags": []}, "source": ["try:\n", "    demand_rep_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::demand-replication\",\n", "    ).rename(columns={\"factor\": \"d_factor\"})\n", "\n", "except:\n", "    demand_rep_df = pd.read_csv(\"config__demand-replication.csv\").rename(\n", "        columns={\"factor\": \"d_factor\"}\n", "    )\n", "\n", "demand_rep_df[\"new_facility_id\"] = demand_rep_df[\"new_facility_id\"].fillna(0).astype(int)\n", "\n", "demand_rep_df[\"nearby_facility_id\"] = demand_rep_df[\"nearby_facility_id\"].fillna(0).astype(int)\n", "\n", "demand_rep_df[\"start_date\"] = pd.to_datetime(demand_rep_df[\"start_date\"])\n", "demand_rep_df[\"end_date\"] = pd.to_datetime(demand_rep_df[\"end_date\"])\n", "\n", "demand_rep_df[\"tdate\"] = list(\n", "    map(\n", "        lambda x, y: pd.date_range(start=x, end=y),\n", "        demand_rep_df[\"start_date\"],\n", "        demand_rep_df[\"end_date\"],\n", "    )\n", ")\n", "\n", "demand_rep_df = demand_rep_df.explode(\"tdate\").drop([\"start_date\", \"end_date\"], axis=1)\n", "\n", "demand_rep_df[\"tdate\"] = pd.to_datetime(demand_rep_df[\"tdate\"])\n", "\n", "demand_rep_df = demand_rep_df.reset_index()\n", "\n", "demand_rep_max = (\n", "    demand_rep_df.groupby([\"new_facility_id\", \"nearby_facility_id\", \"tdate\"])\n", "    .agg({\"index\": \"max\"})\n", "    .reset_index()\n", ")\n", "\n", "demand_rep_df = demand_rep_df.merge(\n", "    demand_rep_max,\n", "    on=[\"new_facility_id\", \"nearby_facility_id\", \"tdate\", \"index\"],\n", "    how=\"inner\",\n", ").drop(columns={\"index\"})\n", "\n", "nearby_facility_list = list(demand_rep_df[\"nearby_facility_id\"].unique())\n", "\n", "demand_rep_df = (\n", "    demand_rep_df[demand_rep_df[\"tdate\"] >= today_date].reset_index().drop(columns={\"index\"})\n", ")\n", "\n", "demand_rep_df.head(1)"]}, {"cell_type": "raw", "id": "2cee7631-a516-49ee-ab06-6dbb3c5f347c", "metadata": {}, "source": ["nearby_df = final_df[final_df[\"facility_id\"].isin(nearby_facility_list)][\n", "    [\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"tdate\",\n", "        \"slota_transfer_qty\",\n", "        \"tot_transfer_qty\",\n", "        \"slotb_transfer_qty\",\n", "    ]\n", "]\n", "\n", "nearby_df = nearby_df.rename(columns={\"facility_id\": \"nearby_facility_id\"})\n", "\n", "demand_rep_df = demand_rep_df.merge(nearby_df, on=[\"nearby_facility_id\", \"tdate\"], how=\"inner\")\n", "\n", "demand_rep_df[\"flag\"] = 1\n", "\n", "demand_rep_df[\"slota\"] = demand_rep_df[\"slota_transfer_qty\"] * demand_rep_df[\"d_factor\"]\n", "\n", "demand_rep_df[\"tot\"] = demand_rep_df[\"tot_transfer_qty\"] * demand_rep_df[\"d_factor\"]\n", "\n", "demand_rep_df[\"slotb\"] = demand_rep_df[\"slotb_transfer_qty\"] * demand_rep_df[\"d_factor\"]\n", "\n", "demand_rep_df = demand_rep_df[[\"new_facility_id\", \"item_id\", \"slota\", \"tot\", \"slotb\"]]\n", "\n", "demand_rep_df = demand_rep_df.rename(columns={\"new_facility_id\": \"facility_id\"})\n", "\n", "demand_rep_df.head(1)"]}, {"cell_type": "raw", "id": "5ff26d9f-7532-4c22-a68c-9aa4b9a93a84", "metadata": {}, "source": ["final_df = final_df.merge(demand_rep_df, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "final_df[\"slota_transfer_qty\"] = np.where(\n", "    final_df[\"slota\"].isna(), final_df[\"slota_transfer_qty\"], final_df[\"slota\"]\n", ")\n", "\n", "final_df[\"tot_transfer_qty\"] = np.where(\n", "    final_df[\"tot\"].isna(), final_df[\"tot_transfer_qty\"], final_df[\"tot\"]\n", ")\n", "\n", "final_df[\"slotb_transfer_qty\"] = np.where(\n", "    final_df[\"slotb\"].isna(), final_df[\"slotb_transfer_qty\"], final_df[\"slotb\"]\n", ")\n", "\n", "final_df = final_df.drop(columns={\"slota\", \"slotb\", \"tot\"})\n", "\n", "final_df.head(1)"]}, {"cell_type": "markdown", "id": "9c10f540-79c3-408f-8c63-c02dead92774", "metadata": {}, "source": ["## Store Correction"]}, {"cell_type": "raw", "id": "e08a7684-6e4c-4853-a05c-47a5e9e0c963", "metadata": {}, "source": ["try:\n", "    store_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::store-input\",\n", "    )\n", "\n", "except:\n", "    store_df = pd.read_csv(\"store_input.csv\")\n", "\n", "store_df[\"facility_id\"] = store_df[\"facility_id\"].fillna(0).astype(int)\n", "\n", "store_df[\"start_date\"] = pd.to_datetime(store_df[\"start_date\"])\n", "store_df[\"end_date\"] = pd.to_datetime(store_df[\"end_date\"])\n", "\n", "store_df[\"factor\"] = store_df[\"factor\"].astype(float)\n", "\n", "store_df[\"tdate\"] = list(\n", "    map(\n", "        lambda x, y: pd.date_range(start=x, end=y),\n", "        store_df[\"start_date\"],\n", "        store_df[\"end_date\"],\n", "    )\n", ")\n", "\n", "store_df = store_df.explode(\"tdate\").drop([\"start_date\", \"end_date\"], axis=1)\n", "\n", "store_df[\"tdate\"] = pd.to_datetime(store_df[\"tdate\"])\n", "\n", "store_df = store_df.reset_index()\n", "\n", "store_max = store_df.groupby([\"facility_id\", \"tdate\"]).agg({\"index\": \"max\"}).reset_index()\n", "\n", "store_df = store_df.merge(store_max, on=[\"facility_id\", \"tdate\", \"index\"], how=\"inner\").drop(\n", "    columns={\"index\"}\n", ")\n", "\n", "store_df = store_df[store_df[\"tdate\"] >= today_date].reset_index().drop(columns={\"index\"})\n", "\n", "store_df.head(1)"]}, {"cell_type": "raw", "id": "7c824fec-3fa5-4a0d-b91a-497cd0b3a612", "metadata": {}, "source": ["final_df = final_df.merge(store_df, on=[\"facility_id\", \"tdate\"], how=\"left\")\n", "\n", "final_df[\"factor\"] = final_df[\"factor\"].fillna(1)\n", "\n", "# final_df[\"slota_transfer_qty\"] = (\n", "#     np.ceil(final_df[\"slota_transfer_qty\"] * final_df[\"factor\"]).fillna(0).astype(int)\n", "# )\n", "\n", "# final_df[\"slotb_transfer_qty\"] = (\n", "#     np.ceil(final_df[\"slotb_transfer_qty\"] * final_df[\"factor\"]).fillna(0).astype(int)\n", "# )\n", "\n", "final_df.head(1)"]}, {"cell_type": "raw", "id": "d19c6c14-d2d7-4139-a539-63ebbae8d103", "metadata": {}, "source": ["## Backend Item Correction"]}, {"cell_type": "raw", "id": "0735b615-1b92-4105-8285-19b7c879919a", "metadata": {}, "source": ["try:\n", "    store_item_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::backend-item-input\",\n", "    ).rename(columns={\"factor\": \"si_factor\"})\n", "\n", "except:\n", "    store_item_df = pd.read_csv(\"be_input.csv\").rename(columns={\"factor\": \"si_factor\"})\n", "\n", "store_item_df[\"be_facility_id\"] = store_item_df[\"be_facility_id\"].fillna(0).astype(int)\n", "store_item_df[\"item_id\"] = store_item_df[\"item_id\"].fillna(0).astype(int)\n", "\n", "store_item_df[\"start_date\"] = pd.to_datetime(store_item_df[\"start_date\"])\n", "store_item_df[\"end_date\"] = pd.to_datetime(store_item_df[\"end_date\"])\n", "\n", "store_item_df[\"si_factor\"] = store_item_df[\"si_factor\"].astype(float)\n", "\n", "store_item_df[\"tdate\"] = list(\n", "    map(\n", "        lambda x, y: pd.date_range(start=x, end=y),\n", "        store_item_df[\"start_date\"],\n", "        store_item_df[\"end_date\"],\n", "    )\n", ")\n", "\n", "store_item_df = store_item_df.explode(\"tdate\").drop([\"start_date\", \"end_date\"], axis=1)\n", "\n", "store_item_df[\"tdate\"] = pd.to_datetime(store_item_df[\"tdate\"])\n", "\n", "store_item_df = store_item_df.reset_index()\n", "\n", "store_item_max = (\n", "    store_item_df.groupby([\"be_facility_id\", \"item_id\", \"tdate\"])\n", "    .agg({\"index\": \"max\"})\n", "    .reset_index()\n", ")\n", "\n", "store_item_df = store_item_df.merge(\n", "    store_item_max, on=[\"be_facility_id\", \"item_id\", \"tdate\", \"index\"], how=\"inner\"\n", ").drop(columns={\"index\"})\n", "\n", "store_item_df = (\n", "    store_item_df[store_item_df[\"tdate\"] >= today_date].reset_index().drop(columns={\"index\"})\n", ")\n", "\n", "store_item_df.head(1)"]}, {"cell_type": "raw", "id": "753ea9d9-c093-457e-bbda-e1c90a0fc1de", "metadata": {}, "source": ["final_df = final_df.merge(store_item_df, on=[\"be_facility_id\", \"item_id\", \"tdate\"], how=\"left\")\n", "\n", "final_df[\"si_factor\"] = final_df[\"si_factor\"].fillna(1)\n", "\n", "# final_df[\"slota_transfer_qty\"] = (\n", "#     np.ceil(final_df[\"slota_transfer_qty\"] * final_df[\"si_factor\"])\n", "#     .<PERSON>na(0)\n", "#     .astype(int)\n", "# )\n", "\n", "# final_df[\"slotb_transfer_qty\"] = (\n", "#     np.ceil(final_df[\"slotb_transfer_qty\"] * final_df[\"si_factor\"])\n", "#     .<PERSON>na(0)\n", "#     .astype(int)\n", "# )\n", "\n", "final_df.head(1)"]}, {"cell_type": "markdown", "id": "ac9b635b-a739-476a-8780-bea66878ae79", "metadata": {"tags": []}, "source": ["# Logs"]}, {"cell_type": "markdown", "id": "57934b22-186b-4f5c-9b6c-cb7dc638a2dd", "metadata": {}, "source": ["### Corrected Forecast Logs"]}, {"cell_type": "code", "execution_count": null, "id": "95743857-e04b-466a-967f-fa6efa36716d", "metadata": {}, "outputs": [], "source": ["working_steps_df = final_df.copy()\n", "\n", "working_steps_df[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))\n", "\n", "working_steps_df = working_steps_df[\n", "    [\n", "        \"facility_id\",\n", "        \"outlet_id\",\n", "        \"ftype_\",\n", "        \"item_id\",\n", "        \"stype_\",\n", "        \"tdate\",\n", "        \"factor\",\n", "        \"si_factor\",\n", "        \"ds_f\",\n", "        \"slota_transfer_qty\",\n", "        \"slotb_transfer_qty\",\n", "        \"updated_at\",\n", "    ]\n", "]\n", "\n", "working_steps_df[[\"slota_transfer_qty\", \"slotb_transfer_qty\"]] = (\n", "    working_steps_df[[\"slota_transfer_qty\", \"slotb_transfer_qty\"]].fillna(0).astype(int)\n", ")\n", "\n", "working_steps_df.head(1)"]}, {"cell_type": "markdown", "id": "18c74df9-60cb-43d9-a920-f9e05bc678d5", "metadata": {}, "source": ["## <PERSON> <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "id": "bac1b777-df13-4711-ab3f-e6a3cdc43cd9", "metadata": {}, "outputs": [], "source": ["trino_data_df = working_steps_df.copy()\n", "\n", "trino_data_df[\"date_ist\"] = pd.to_datetime(\n", "    pd.to_datetime(trino_data_df[\"updated_at\"]).dt.strftime(\"%Y-%m-%d\")\n", ")\n", "\n", "trino_data_df = trino_data_df[\n", "    [\n", "        \"facility_id\",\n", "        \"outlet_id\",\n", "        \"ftype_\",\n", "        \"item_id\",\n", "        \"stype_\",\n", "        \"tdate\",\n", "        \"factor\",\n", "        \"si_factor\",\n", "        \"ds_f\",\n", "        \"slota_transfer_qty\",\n", "        \"slotb_transfer_qty\",\n", "        \"date_ist\",\n", "        \"updated_at\",\n", "    ]\n", "]\n", "\n", "trino_data_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "22d13c45-0fba-432e-86eb-e21679e3e1b4", "metadata": {}, "outputs": [], "source": ["trino_data_df.slota_transfer_qty.sum(), trino_data_df.slotb_transfer_qty.sum()"]}, {"cell_type": "code", "execution_count": null, "id": "f3c5de5b-8975-4444-9bcf-55940ecacccd", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"facility id\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"outlet id\"},\n", "    {\"name\": \"ftype_\", \"type\": \"VARCHAR\", \"description\": \"facility categorization\"},\n", "    {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"item id\"},\n", "    {\"name\": \"stype_\", \"type\": \"VARCHAR\", \"description\": \"SKU categorization\"},\n", "    {\"name\": \"tdate\", \"type\": \"DATE\", \"description\": \"transfer date\"},\n", "    {\"name\": \"factor\", \"type\": \"DOUBLE\", \"description\": \"manual store input\"},\n", "    {\"name\": \"si_factor\", \"type\": \"DOUBLE\", \"description\": \"manual store input\"},\n", "    {\"name\": \"ds_f\", \"type\": \"DOUBLE\", \"description\": \"ds model flag\"},\n", "    {\n", "        \"name\": \"slota_transfer_qty\",\n", "        \"type\": \"INTEGER\",\n", "        \"description\": \"Morning Transfer Qty\",\n", "    },\n", "    {\n", "        \"name\": \"slotb_transfer_qty\",\n", "        \"type\": \"INTEGER\",\n", "        \"description\": \"Evening Transfer Qty\",\n", "    },\n", "    {\"name\": \"date_ist\", \"type\": \"DATE\", \"description\": \"updated_at filter\"},\n", "    {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(6)\", \"description\": \"date/time of run\"},\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"milk_indent_correction_v2_testing\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"updated_at\", \"facility_id\", \"item_id\"],\n", "    \"partition_key\": [\"date_ist\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"append\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains milk indent correction used for transfers\",\n", "}\n", "\n", "pb.to_trino(trino_data_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "9e4969ea-5810-4e22-822e-50bf0f794860", "metadata": {}, "outputs": [], "source": ["channel = \"perishable-dag-alerts\"\n", "text = \"milk indent correction testing table has been updated \\n cc: <@U06NUDF672P> <@U06T927GCSW> <@U05CCTXLBU1>\"\n", "pb.send_slack_message(channel=channel, text=text)"]}, {"cell_type": "code", "execution_count": null, "id": "d09457c0-b530-4b9c-a230-adba94d08738", "metadata": {}, "outputs": [], "source": ["data = trino_data_df[[\"facility_id\", \"item_id\", \"slota_transfer_qty\"]]\n", "data[[\"facility_id\", \"item_id\", \"slota_transfer_qty\"]] = (\n", "    data[[\"facility_id\", \"item_id\", \"slota_transfer_qty\"]].round(0).astype(int)\n", ")"]}, {"cell_type": "markdown", "id": "50996848-d595-4266-8292-2915eff6c883", "metadata": {}, "source": ["### Data updated for outlet hour weights"]}, {"cell_type": "code", "execution_count": null, "id": "e8cee5e7-e4e9-45fa-8da4-72f1919c4602", "metadata": {}, "outputs": [], "source": ["hour_weights_df = hour_weights_df.drop_duplicates()\n", "hour_weights_df[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))\n", "hour_weights_df[[\"hour_\", \"outlet_id\"]] = hour_weights_df[[\"hour_\", \"outlet_id\"]].astype(int)\n", "hour_weights_df[\"new_hw\"] = hour_weights_df[\"new_hw\"].astype(float)\n", "hour_weights_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b981e56b-ea42-4a0f-b355-eb23da8fb399", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"city_outlet_milk_hour_weight\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"Item ID\"},\n", "        {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"Facility ID\"},\n", "        {\"name\": \"hour_\", \"type\": \"INTEGER\", \"description\": \"hour\"},\n", "        {\"name\": \"new_hw\", \"type\": \"double\", \"description\": \"hour weight\"},\n", "        {\n", "            \"name\": \"updated_at\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"Updated at ts\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"outlet_id\", \"hour_\"],\n", "    \"partition_key\": [\"updated_at\"],\n", "    # \"incremental_key\": \"updated_at\",\n", "    # \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains the hour weights of milk at store level ny giving 75% weights to search and 25% to milk carts of last 45 days\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "8975e696-323a-465e-a00b-bd74680ed44f", "metadata": {}, "outputs": [], "source": ["pb.to_trino(hour_weights_df, **kwargs)"]}, {"cell_type": "markdown", "id": "e537ba3f-444e-40da-80a3-333cebe81b57", "metadata": {"tags": []}, "source": ["### Data updated for outlet WoW bumps"]}, {"cell_type": "code", "execution_count": null, "id": "94745e69-8aea-4315-8394-4d984120e183", "metadata": {}, "outputs": [], "source": ["increment_df_calc = increment_df_calc.drop_duplicates()\n", "increment_df_calc[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))\n", "increment_df_calc[\"bf\"] = increment_df_calc[\"bf\"].astype(float)\n", "increment_df_calc.head()"]}, {"cell_type": "code", "execution_count": null, "id": "89e76048-4702-4aaf-a228-0055af581f3d", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"facility_milk_wow_bumps\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"facility_id\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"facility_id\",\n", "        },\n", "        {\"name\": \"dow\", \"type\": \"INTEGER\", \"description\": \"day of week\"},\n", "        {\"name\": \"tdow\", \"type\": \"INTEGER\", \"description\": \"transfer day of week\"},\n", "        {\n", "            \"name\": \"quantity\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"75th percentile 6-6 extrapolated lin sales\",\n", "        },\n", "        {\n", "            \"name\": \"tdow_quantity\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"75th percentile 6-6 extrapolated lin sales for transfer dow\",\n", "        },\n", "        {\"name\": \"bf\", \"type\": \"double\", \"description\": \"bump factor\"},\n", "        {\n", "            \"name\": \"updated_at\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"Updated at ts\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"facility_id\", \"dow\", \"tdow\"],\n", "    \"partition_key\": [\"updated_at\"],\n", "    # \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains store level day of week normalisation factor\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "5230cd74-2dd4-4925-8277-c2081ad08f80", "metadata": {}, "outputs": [], "source": ["pb.to_trino(increment_df_calc, **kwargs)"]}, {"cell_type": "markdown", "id": "18c87adb-8f23-4b19-97bf-23c589143394", "metadata": {}, "source": ["### data updated to min-max"]}, {"cell_type": "code", "execution_count": null, "id": "429d0d8c-c9c1-4874-b882-bb998b15f8d0", "metadata": {}, "outputs": [], "source": ["data[\"max_qty\"] = data[\"slota_transfer_qty\"]\n", "data[\"min_qty\"] = data[\"max_qty\"]\n", "data[\"is_bundle\"] = 0\n", "data[\"old_bucket\"] = 1\n", "data[\"case_type\"] = \"CUSTOM\"\n", "data[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))\n", "data[\"updated_by\"] = 14\n", "data[[\"facility_id\", \"item_id\", \"min_qty\", \"max_qty\"]] = data[\n", "    [\"facility_id\", \"item_id\", \"min_qty\", \"max_qty\"]\n", "].astype(int)\n", "data.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "8575e7b7-e302-4e0d-a1c7-a327e6a59fce", "metadata": {}, "outputs": [], "source": ["data = data[\n", "    [\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"min_qty\",\n", "        \"max_qty\",\n", "        \"is_bundle\",\n", "        \"old_bucket\",\n", "        \"case_type\",\n", "        \"updated_at\",\n", "        \"updated_by\",\n", "    ]\n", "]\n", "data.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "67405c5d-6efa-4959-9891-6972eee405c9", "metadata": {}, "outputs": [], "source": ["def upload_to_table(data, maintenance_flag):\n", "\n", "    MIN_MAX_SCHEMA_NAME = \"supply_etls\"\n", "    MIN_MAX_TABLE_NAME = \"facility_item_min_max_quantity\"\n", "\n", "    kwargs = {\n", "        \"schema_name\": MIN_MAX_SCHEMA_NAME,\n", "        \"table_name\": MIN_MAX_TABLE_NAME,\n", "        \"column_dtypes\": [\n", "            {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"Facility ID\"},\n", "            {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"Item ID\"},\n", "            {\"name\": \"min_qty\", \"type\": \"INTEGER\", \"description\": \"Minimum quantity\"},\n", "            {\"name\": \"max_qty\", \"type\": \"INTEGER\", \"description\": \"Maximum quantity\"},\n", "            {\"name\": \"is_bundle\", \"type\": \"INTEGER\", \"description\": \"is bundle\"},\n", "            {\n", "                \"name\": \"old_bucket\",\n", "                \"type\": \"INTEGER\",\n", "                \"description\": \"previous day bucket\",\n", "            },\n", "            {\"name\": \"case_type\", \"type\": \"VARCHAR\", \"description\": \"case type\"},\n", "            {\n", "                \"name\": \"updated_at\",\n", "                \"type\": \"TIMESTAMP(6)\",\n", "                \"description\": \"Updated at ts\",\n", "            },\n", "            {\"name\": \"updated_by\", \"type\": \"INTEGER\", \"description\": \"Updated by\"},\n", "        ],\n", "        \"primary_key\": [\"facility_id\", \"item_id\"],\n", "        \"partition_key\": [\"facility_id\"],\n", "        \"incremental_key\": \"updated_at\",\n", "        \"force_upsert_without_increment_check\": False,\n", "        \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "        \"table_description\": \"this table contains the min max quantity for transfers against each facility item\",\n", "        \"run_maintenance\": maintenance_flag,\n", "    }\n", "    pb.to_trino(data, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "f707b456-fca5-4f67-8f58-875e76a7234d", "metadata": {}, "outputs": [], "source": ["# if current_hour <= 14:\n", "#     upload_to_table(data, maintenance_flag=False)"]}, {"cell_type": "markdown", "id": "906ba595-d2ef-4fb4-8c84-1de701cc5f92", "metadata": {}, "source": ["### data updated to min-max logs"]}, {"cell_type": "code", "execution_count": null, "id": "4dd81a28-396f-4e43-9895-f32d74873735", "metadata": {}, "outputs": [], "source": ["data_log = data.copy()\n", "\n", "data_log[\"date_ist\"] = pd.to_datetime(\n", "    pd.to_datetime(data_log[\"updated_at\"]).dt.strftime(\"%Y-%m-%d\")\n", ")\n", "\n", "data_log = data_log[\n", "    [\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"min_qty\",\n", "        \"max_qty\",\n", "        \"is_bundle\",\n", "        \"old_bucket\",\n", "        \"case_type\",\n", "        \"date_ist\",\n", "        \"updated_at\",\n", "        \"updated_by\",\n", "    ]\n", "]\n", "\n", "data_log.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "7f7b866a-2e14-4ee0-a00e-e393f032d98a", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"facility_item_min_max_quantity_log\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"Facility ID\"},\n", "        {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"Item ID\"},\n", "        {\"name\": \"min_qty\", \"type\": \"INTEGER\", \"description\": \"Minimum quantity\"},\n", "        {\"name\": \"max_qty\", \"type\": \"INTEGER\", \"description\": \"Maximum quantity\"},\n", "        {\"name\": \"is_bundle\", \"type\": \"INTEGER\", \"description\": \"is bundle\"},\n", "        {\n", "            \"name\": \"old_bucket\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"previous day bucket\",\n", "        },\n", "        {\"name\": \"case_type\", \"type\": \"VARCHAR\", \"description\": \"case type\"},\n", "        {\n", "            \"name\": \"date_ist\",\n", "            \"type\": \"DATE\",\n", "            \"description\": \"updated_at filter date\",\n", "        },\n", "        {\n", "            \"name\": \"updated_at\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"Updated at ts\",\n", "        },\n", "        {\"name\": \"updated_by\", \"type\": \"INTEGER\", \"description\": \"Updated by\"},\n", "    ],\n", "    \"primary_key\": [\"facility_id\", \"item_id\"],\n", "    \"partition_key\": [\"date_ist\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"append\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains the min max quantity logs for transfers against each facility item\",\n", "}\n", "\n", "# if current_hour <= 14:\n", "#     pb.to_trino(data_log, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "b532a810-3df5-4e20-8cb4-5a44fa3e031a", "metadata": {"tags": []}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}