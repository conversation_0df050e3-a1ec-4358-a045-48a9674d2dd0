{"cells": [{"cell_type": "code", "execution_count": null, "id": "7f13d1a3-294c-4442-9f0b-875a64a8ef29", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import os\n", "import time\n", "import gc\n", "import requests\n", "\n", "import calendar\n", "from datetime import datetime as dt\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.options.mode.chained_assignment = None  # default='warn'\n", "pd.set_option(\"display.max_columns\", 500)"]}, {"cell_type": "code", "execution_count": null, "id": "2960c816-5d28-4b4f-97de-87d10e995b84", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pytz\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")\n", "\n", "current_time = datetime.now(IST)\n", "\n", "current_hour = pd.to_datetime(current_time).hour\n", "\n", "today_date = current_time.strftime(\"%Y-%m-%d\")\n", "\n", "today_date, current_hour"]}, {"cell_type": "code", "execution_count": null, "id": "3d9af3c7-9e1f-4d7c-8815-e1899dcc29d8", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(\n", "            f\"Read attempt: {attempt}... \\nRun started at {datetime.now() + timed<PERSON>ta(hours = 5.5)}\"\n", "        )\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "markdown", "id": "3d47ad1f-ea2e-43bd-97cd-000124d1054c", "metadata": {}, "source": ["## Forecast Run Day"]}, {"cell_type": "code", "execution_count": null, "id": "04753e3b-e47d-494d-9ccb-cfc6fd8232d8", "metadata": {"tags": []}, "outputs": [], "source": ["try:\n", "    run_day_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::forecast-day\",\n", "    )\n", "\n", "except:\n", "    run_day_df = pd.read_csv(\"forecast_day.csv\")\n", "\n", "run_day_df[\"today\"] = today_date\n", "\n", "run_day_df[\"tdow\"] = pd.to_datetime(run_day_df[\"today\"]).dt.dayofweek\n", "\n", "run_day_df[\"run_dow\"] = np.where(\n", "    run_day_df[\"run_day\"] == \"regular\",\n", "    20,\n", "    np.where(\n", "        run_day_df[\"run_day\"] == \"monday\",\n", "        0,\n", "        np.where(\n", "            run_day_df[\"run_day\"] == \"tuesday\",\n", "            1,\n", "            np.where(\n", "                run_day_df[\"run_day\"] == \"wednesday\",\n", "                2,\n", "                np.where(\n", "                    run_day_df[\"run_day\"] == \"thursday\",\n", "                    3,\n", "                    np.where(\n", "                        run_day_df[\"run_day\"] == \"friday\",\n", "                        4,\n", "                        np.where(run_day_df[\"run_day\"] == \"saturday\", 5, 6),\n", "                    ),\n", "                ),\n", "            ),\n", "        ),\n", "    ),\n", ")\n", "\n", "run_day_df[\"tat_check\"] = run_day_df[\"run_dow\"] - run_day_df[\"tdow\"]\n", "\n", "run_day_df[\"tat_check\"] = np.where(\n", "    run_day_df[\"tat_check\"] <= 0, 7 + run_day_df[\"tat_check\"], run_day_df[\"tat_check\"]\n", ")\n", "\n", "run_day_df[\"tat\"] = np.where(run_day_df[\"tat_check\"] > 7, 2, run_day_df[\"tat_check\"])\n", "\n", "tat_df = run_day_df[[\"city\", \"tat\"]].drop_duplicates().reset_index().drop(columns={\"index\"})\n", "\n", "run_day_df.head(1)"]}, {"cell_type": "markdown", "id": "0fe4532c-57d5-4ab1-ac5f-79100facf0d3", "metadata": {}, "source": ["## Winter Forecast Input"]}, {"cell_type": "code", "execution_count": null, "id": "637d0974-8f8f-4a0d-b8da-2d9d3f493c2b", "metadata": {}, "outputs": [], "source": ["try:\n", "    winter_input_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::city-winter-input\",\n", "    )\n", "\n", "except:\n", "    winter_input_df = pd.read_csv(\"winter_input.csv\")\n", "\n", "winter_input_df[\"winter_flag\"] = np.where(winter_input_df[\"switch\"] == \"no\", 0, 1)\n", "\n", "winter_input_df = winter_input_df.drop(columns={\"state\", \"switch\"})\n", "\n", "winter_input_df.head(1)"]}, {"cell_type": "markdown", "id": "d108fd8a-7a48-44bd-b865-d34463933604", "metadata": {"tags": []}, "source": ["## City Disruption Removal"]}, {"cell_type": "code", "execution_count": null, "id": "314fb627-e84c-499e-ae9e-bb5638a181aa", "metadata": {}, "outputs": [], "source": ["try:\n", "    tot_disruption_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::city-disruption\",\n", "    )\n", "\n", "except:\n", "    tot_disruption_df = pd.read_csv(\"city_disruption.csv\")\n", "\n", "tot_disruption_df = tot_disruption_df.drop_duplicates().reset_index().drop(columns={\"index\"})\n", "\n", "tot_disruption_df[\"date_\"] = pd.to_datetime(tot_disruption_df[\"date_\"])\n", "\n", "tot_disruption_df[\"tot_flag\"] = 1\n", "\n", "tot_disruption_df.head(1)"]}, {"cell_type": "markdown", "id": "b40e8f5d-bb61-4b30-b06a-9b2fdcdebba6", "metadata": {}, "source": ["## Date Hour DataFrame "]}, {"cell_type": "code", "execution_count": null, "id": "902b2d01-2911-4a6a-a7b5-c91890db2351", "metadata": {}, "outputs": [], "source": ["x = pd.date_range(\n", "    start=(pd.to_datetime(today_date) - timed<PERSON>ta(days=30)).strftime(\"%Y-%m-%d\"),\n", "    end=(pd.to_datetime(today_date) - timed<PERSON>ta(days=1)).strftime(\"%Y-%m-%d\"),\n", ")\n", "\n", "date_df = pd.DataFrame({\"date_\": x, \"date_flag\": 1})\n", "\n", "hour_list = list({6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23})\n", "hour_df = pd.DataFrame({\"hour_\": hour_list, \"date_flag\": 1})\n", "\n", "date_df = date_df.merge(hour_df, on=[\"date_flag\"], how=\"left\")\n", "\n", "date_df.shape"]}, {"cell_type": "markdown", "id": "a36f61ef-5769-43c5-98b4-dc5eb902fe72", "metadata": {}, "source": ["## 1 liter items and backend\n"]}, {"cell_type": "code", "execution_count": null, "id": "be901afe-a25a-4358-a7be-45b95824a616", "metadata": {}, "outputs": [], "source": ["try:\n", "    one_l = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"1 litre:: Backends\",\n", "    )\n", "\n", "except:\n", "    one_l = pd.read_csv(\"1_litre_Backends.csv\")\n", "\n", "one_l[\"be_facility_id\"] = one_l[\"be_facility_id\"].fillna(0).astype(int)\n", "one_l[\"500ml_item_id\"] = one_l[\"500ml_item_id\"].fillna(0).astype(int)\n", "one_l[\"1L_item_id\"] = one_l[\"1L_item_id\"].fillna(0).astype(int)\n", "one_l[\"start_date\"] = pd.to_datetime(one_l[\"start_date\"])\n", "one_l[\"end_date\"] = pd.to_datetime(one_l[\"end_date\"])\n", "one_l[\"conversion_factor\"] = one_l[\"conversion_factor\"].fillna(0).astype(float)\n", "one_l_item_id = tuple(set(one_l[\"1L_item_id\"].to_list()))\n", "half_l_item_id = tuple(set(one_l[\"500ml_item_id\"].to_list()))\n", "one_l_item_id"]}, {"cell_type": "markdown", "id": "d794c18d-c415-4376-a9b4-f2a489e1875f", "metadata": {"tags": []}, "source": ["## Milk Forecast Base"]}, {"cell_type": "markdown", "id": "65deafe8-fa9e-431f-8282-8c5d005f3899", "metadata": {"tags": []}, "source": ["### Base Assortment - Fetch"]}, {"cell_type": "code", "execution_count": null, "id": "341f1341-52ba-4014-8f85-6aeda9f57fbe", "metadata": {}, "outputs": [], "source": ["base_query = \"\"\"\n", "     WITH active_stores AS (\n", "        SELECT DISTINCT facility_id, outlet_id \n", "        FROM po.physical_facility_outlet_mapping pfom  \n", "        WHERE ars_active = 1 AND active = 1 AND is_primary = 1 \n", "        AND outlet_id IN (SELECT DISTINCT id FROM retail.console_outlet WHERE business_type_id = 7 AND active = 1 AND lake_active_record) \n", "        AND outlet_id IN (SELECT DISTINCT outlet_id FROM po.bulk_facility_outlet_mapping WHERE active = True AND lake_active_record) \n", "    ), \n", "    \n", "    milk_assortment AS (\n", "        SELECT DISTINCT cl.name AS city, a.facility_id, outlet_id, a.item_id,rpc.name as item_name\n", "        FROM rpc.product_facility_master_assortment a\n", "        JOIN active_stores b ON a.facility_id = b.facility_id\n", "        JOIN retail.console_outlet co ON co.id = b.outlet_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "        LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id AND cl.lake_active_record\n", "        inner join rpc.item_category_details rpc on rpc.item_id = a.item_id and l2_id = 1185 and rpc.lake_active_record\n", "        AND a.active = 1 AND a.master_assortment_substate_id = 1\n", "    ),\n", "    \n", "    be_mapping AS (\n", "        SELECT item_id, facility_id, be_facility_id, be_outlet_id, \n", "        CASE \n", "            WHEN cloud_store_id IS NULL THEN be_outlet_id \n", "            ELSE cloud_store_id \n", "        END AS be_inv_outlet_id\n", "        FROM (\n", "            SELECT DISTINCT tm.item_id, cf.facility_id, cb.facility_id AS be_facility_id, CAST(tm.tag_value AS int) AS be_outlet_id\n", "            FROM rpc.item_outlet_tag_mapping tm\n", "            LEFT JOIN retail.console_outlet cb ON cb.id = CAST(tm.tag_value AS int) AND cb.active = 1 AND cb.lake_active_record\n", "            JOIN retail.console_outlet cf ON cf.id = outlet_id AND cf.active = 1 AND cf.lake_active_record\n", "            WHERE tag_type_id IN (8) AND tm.active = 1\n", "        ) a\n", "        LEFT JOIN (\n", "            SELECT DISTINCT warehouse_id, cloud_store_id \n", "            FROM retail.warehouse_outlet_mapping\n", "            WHERE lake_active_record\n", "        ) wom on wom.warehouse_id = a.be_outlet_id\n", "    ),\n", "    \n", "    final AS (\n", "        SELECT city, a.facility_id, a.outlet_id, a.item_id, be_facility_id,item_name\n", "        FROM milk_assortment a\n", "        LEFT JOIN be_mapping b ON a.item_id = b.item_id AND a.facility_id = b.facility_id\n", "    )\n", "    \n", "    SELECT * FROM final\n", "\"\"\"\n", "base_pre_df = read_sql_query(base_query, trino)\n", "\n", "base_pre_df[\"be_facility_id\"] = base_pre_df[\"be_facility_id\"].fillna(0).astype(int)\n", "\n", "base_pre_df.shape"]}, {"cell_type": "markdown", "id": "1684c0b5-805e-4861-a28e-7553cfc6a2c4", "metadata": {"tags": []}, "source": ["### TAT Calculation"]}, {"cell_type": "code", "execution_count": null, "id": "cd2c194e-7df4-472d-bc20-f13cbca30e20", "metadata": {}, "outputs": [], "source": ["base_df = base_pre_df.copy()\n", "\n", "base_df[\"date_flag\"] = 1\n", "\n", "base_df = base_df.merge(date_df, on=[\"date_flag\"], how=\"left\").drop(columns={\"date_flag\"})\n", "\n", "base_df = base_df.merge(tat_df, on=[\"city\"], how=\"left\")\n", "\n", "base_df = base_df.merge(winter_input_df, on=[\"city\"], how=\"left\")\n", "\n", "base_df[\"winter_flag\"] = base_df[\"winter_flag\"].fillna(0).astype(int)\n", "\n", "base_df[\"tat\"] = base_df[\"tat\"].fillna(2).astype(int)\n", "\n", "base_df[\"fdate\"] = pd.to_datetime(today_date) + pd.to_timedelta(base_df[\"tat\"], unit=\"D\")\n", "\n", "base_df[\"fdow\"] = pd.to_datetime(base_df[\"fdate\"]).dt.dayofweek\n", "\n", "base_df.shape"]}, {"cell_type": "markdown", "id": "b2e578d2-73ab-4e8f-8d3a-1c2f48cd8148", "metadata": {"tags": []}, "source": ["### Item, Facility, Outlet List"]}, {"cell_type": "code", "execution_count": null, "id": "dc1f1949-153d-460c-bac7-7d6ee6ab7740", "metadata": {}, "outputs": [], "source": ["facility_id_list = tuple(set(base_pre_df[\"facility_id\"].to_list()))\n", "outlet_id_list = tuple(set(base_pre_df[\"outlet_id\"].to_list()))\n", "item_id_list = tuple(set(base_pre_df[\"item_id\"].to_list()))\n", "\n", "len(item_id_list), len(facility_id_list), len(outlet_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "76517d90-b671-481a-839a-a6d81a5fdd54", "metadata": {}, "outputs": [], "source": ["del [base_pre_df]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "3101105b-f546-4780-9451-3014c8f21916", "metadata": {"tags": []}, "source": ["## Data Fetch"]}, {"cell_type": "markdown", "id": "2bcea522-bc75-4e3b-8706-ed3b5784864d", "metadata": {"tags": []}, "source": ["## Fetching all data from ETL\n"]}, {"cell_type": "code", "execution_count": null, "id": "526bc480-b52b-495b-aaf8-1a791bbb8e09", "metadata": {}, "outputs": [], "source": ["base_query = f\"\"\"\n", "       SELECT\n", "           a.date_,\n", "            cl.name as city,\n", "            b.outlet_id as outlet_id,\n", "            a.facility_id,\n", "            a.item_id,\n", "            hour_,\n", "            max(is_available) as is_available,\n", "            max(carts) carts,\n", "            max(total_search) as searches,\n", "            max(sales) as quantity\n", "        from\n", "            supply_etls.milk_sales_avail_searches_dump as a\n", "        left join po.physical_facility_outlet_mapping b on b.facility_id = a.facility_id and ars_active = 1 and lake_active_record\n", "        LEFT JOIN retail.console_location cl ON cl.id = b.city_id\n", "        where a.date_ BETWEEN DATE('{today_date}') - interval '45' day AND DATE('{today_date}') - interval '1' day\n", "            and a.facility_id in {facility_id_list}\n", "            and item_id in {item_id_list}\n", "            and hour_ between 6 and 23\n", "        group by 1,2,3,4,5,6\n", "    \"\"\"\n", "data_df = read_sql_query(base_query, trino)\n", "data_df[\"date_\"] = pd.to_datetime(data_df[\"date_\"])\n", "data_df.shape"]}, {"cell_type": "markdown", "id": "5f6d2176-495f-46c9-a79a-493eaf2b6038", "metadata": {"tags": []}, "source": ["### Hourly Availability"]}, {"cell_type": "raw", "id": "e5061a28-b787-4cd3-b784-980116611d47", "metadata": {}, "source": ["base_query = f\"\"\"\n", "    WITH base AS (\n", "        SELECT * FROM supply_etls.hourly_inventory_snapshots\n", "        WHERE date_ist BETWEEN DATE('{today_date}') - interval '31' day AND DATE('{today_date}') + interval '1' day \n", "        AND outlet_id IN {outlet_id_list}\n", "    )\n", "    \n", "    SELECT date_, hour_, facility_id, item_id, MAX(is_available) AS is_available\n", "    FROM (\n", "        SELECT DATE(date_ist) AS date_, EXTRACT(hour FROM updated_at_ist) AS hour_, facility_id, item_id, \n", "        CASE \n", "            WHEN current_inventory > 0 THEN 1 \n", "            ELSE 0  \n", "        END is_available\n", "        FROM base\n", "        WHERE item_id IN {item_id_list}\n", "    )\n", "    GROUP BY 1,2,3,4\n", "\"\"\"\n", "availability_df = read_sql_query(base_query, trino)\n", "\n", "availability_df[\"date_\"] = pd.to_datetime(availability_df[\"date_\"])\n", "\n", "availability_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "6074b52b-2471-4caf-888c-6ee3f1bbae06", "metadata": {}, "outputs": [], "source": ["availability_df = (\n", "    data_df[[\"date_\", \"hour_\", \"facility_id\", \"item_id\", \"is_available\"]].copy().drop_duplicates()\n", ")"]}, {"cell_type": "markdown", "id": "c9266030-62cf-4350-9611-809cab03c31f", "metadata": {"tags": []}, "source": ["#### Assortment Correction"]}, {"cell_type": "code", "execution_count": null, "id": "edbf7266-1aa9-42cc-a5eb-4d8c26cbeeb0", "metadata": {}, "outputs": [], "source": ["assortment_df = pd.merge(\n", "    base_df,\n", "    availability_df,\n", "    on=[\"item_id\", \"facility_id\", \"date_\", \"hour_\"],\n", "    how=\"left\",\n", ")\n", "\n", "assortment_df[\"is_available\"] = np.where(\n", "    assortment_df[\"is_available\"].isna(), 0, assortment_df[\"is_available\"]\n", ")\n", "\n", "assortment_df[\"dow\"] = pd.to_datetime(assortment_df[\"date_\"]).dt.dayofweek\n", "\n", "assortment_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "d7dbf12e-a58c-4f10-b8c5-bbb4c60ba894", "metadata": {}, "outputs": [], "source": ["del [base_df, availability_df]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "c37c88c0-4cd4-4aa8-b84f-b1721617ad81", "metadata": {"tags": []}, "source": ["### Hourly Sales"]}, {"cell_type": "code", "execution_count": null, "id": "67ba94f2-5012-4a95-89f1-8bcd875458ab", "metadata": {}, "outputs": [], "source": ["sales_pre_df = (\n", "    data_df[[\"city\", \"date_\", \"hour_\", \"facility_id\", \"item_id\", \"quantity\"]]\n", "    .copy()\n", "    .drop_duplicates()\n", ")"]}, {"cell_type": "markdown", "id": "0e160af8-fa36-40e3-ab4e-3cf2ece20c0a", "metadata": {"tags": []}, "source": ["## Daily Carts & Cart Penetration "]}, {"cell_type": "code", "execution_count": null, "id": "734c4724-6dc0-4766-b294-f5ca6722ddf5", "metadata": {}, "outputs": [], "source": ["carts_query = f\"\"\"\n", "    WITH item_mapping as (\n", "        SELECT DISTINCT ipr.product_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NULL THEN ipom_0.item_id \n", "            ELSE ipr.item_id \n", "        END AS item_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1) \n", "            ELSE COALESCE(ipom_0.multiplier,1) \n", "        END AS multiplier\n", "        FROM rpc.item_product_mapping ipr \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", "        WHERE ipr.lake_active_record\n", "    ),\n", "\n", "    sales AS (\n", "        SELECT (oid.cart_checkout_ts_ist) AS order_date, oid.outlet_id, oid.product_id, im.item_id, oid.order_id\n", "        FROM dwh.fact_sales_order_item_details oid \n", "        JOIN item_mapping im on im.product_id = oid.product_id  \n", "        WHERE oid.order_create_dt_ist >= DATE('{today_date}') - interval '15' day\n", "        AND oid.is_internal_order = false  \n", "        AND (oid.order_type NOT LIKE '%%internal%%' OR oid.order_type IS NULL)  \n", "        AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED'\n", "        AND oid.outlet_id IN {outlet_id_list}\n", "    ),\n", "    \n", "    all_carts AS (\n", "        SELECT DATE(order_date) AS date_, outlet_id, COUNT(DISTINCT order_id) AS fac_carts\n", "        FROM sales\n", "        GROUP BY 1,2\n", "    ),\n", "    \n", "    milk_carts AS (\n", "        SELECT DATE(order_date) AS date_, outlet_id, COUNT(DISTINCT order_id) AS milk_carts\n", "        FROM sales\n", "        WHERE item_id IN {item_id_list}\n", "        GROUP BY 1,2\n", "    )\n", "    \n", "    SELECT a.outlet_id, a.date_, fac_carts, \n", "    CASE \n", "        WHEN milk_carts IS NULL THEN 0 \n", "        ELSE milk_carts \n", "    END AS milk_carts\n", "    FROM all_carts a\n", "    LEFT JOIN milk_carts b ON a.outlet_id = b.outlet_id AND a.date_ = b.date_\n", "    WHERE a.date_ < date'{today_date}'\n", "    \"\"\"\n", "carts_df = read_sql_query(carts_query, trino)\n", "\n", "carts_df[\"date_\"] = pd.to_datetime(carts_df[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "f9b48bd9-ab12-4432-80ba-6cf4eb1c1f5e", "metadata": {}, "outputs": [], "source": ["store_base_df = pd.DataFrame(outlet_id_list, columns=[\"outlet_id\"])\n", "store_base_df[\"flag\"] = 1\n", "\n", "x = pd.date_range(\n", "    start=(pd.to_datetime(today_date) - timed<PERSON>ta(days=15)).strftime(\"%Y-%m-%d\"),\n", "    end=(pd.to_datetime(today_date) - timed<PERSON>ta(days=1)).strftime(\"%Y-%m-%d\"),\n", ")\n", "\n", "date_df = pd.DataFrame({\"date_\": x, \"flag\": 1})\n", "\n", "carts_base_df = pd.merge(store_base_df, date_df, on=[\"flag\"], how=\"left\").drop(columns={\"flag\"})\n", "\n", "carts_base_df = pd.merge(carts_base_df, carts_df, on=[\"outlet_id\", \"date_\"], how=\"left\")\n", "\n", "carts_base_df[\"cp\"] = carts_base_df[\"milk_carts\"] / carts_base_df[\"fac_carts\"]\n", "\n", "carts_base_df[[\"fac_carts\", \"milk_carts\", \"cp\"]] = carts_base_df[\n", "    [\"fac_carts\", \"milk_carts\", \"cp\"]\n", "].fillna(0)\n", "\n", "carts_base_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c6148eaf-9f6c-4978-b335-34f38105e4a7", "metadata": {}, "outputs": [], "source": ["def percentile(x):\n", "    return np.ceil(np.percentile(x, 75)).astype(int)\n", "\n", "\n", "def cp_percentile(x):\n", "    return np.percentile(x, 75)"]}, {"cell_type": "markdown", "id": "80e18fd1-a14e-4ca2-8789-7772377684e6", "metadata": {"tags": []}, "source": ["### Disruption Days"]}, {"cell_type": "code", "execution_count": null, "id": "7d0c18af-941e-43b9-922d-62637169f07a", "metadata": {"tags": []}, "outputs": [], "source": ["#### Store Disruption"]}, {"cell_type": "code", "execution_count": null, "id": "660e8594-4ccc-46a2-9f57-917b24013e62", "metadata": {}, "outputs": [], "source": ["opd_df = (\n", "    carts_base_df[carts_base_df[\"fac_carts\"] != 0]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "opd_df = (\n", "    opd_df.groupby([\"outlet_id\"])\n", "    .agg({\"fac_carts\": percentile})\n", "    .reset_index()\n", "    .rename(columns={\"fac_carts\": \"opd\"})\n", ")\n", "\n", "opd_df[\"ftype_\"] = np.where(\n", "    opd_df[\"opd\"] < 800, \"low\", np.where(opd_df[\"opd\"] >= 1200, \"high\", \"medium\")\n", ")\n", "\n", "opd_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "a0be5b69-f632-482d-91bf-5f8d174da95c", "metadata": {"tags": []}, "outputs": [], "source": ["#### Milk Carts Disruption"]}, {"cell_type": "code", "execution_count": null, "id": "63562206-2e93-4781-90e4-99f8d3bc048a", "metadata": {}, "outputs": [], "source": ["milk_cp_df = (\n", "    carts_base_df[carts_base_df[\"cp\"] != 0].drop_duplicates().reset_index().drop(columns={\"index\"})\n", ")\n", "\n", "milk_cp_df = (\n", "    milk_cp_df.groupby([\"outlet_id\"])\n", "    .agg({\"cp\": cp_percentile})\n", "    .reset_index()\n", "    .rename(columns={\"cp\": \"milk_cp\"})\n", ")\n", "\n", "milk_cp_df[\"cp_bucket\"] = np.where(\n", "    milk_cp_df[\"milk_cp\"] < 0.10,\n", "    \"low\",\n", "    np.where(milk_cp_df[\"milk_cp\"] >= 0.18, \"high\", \"medium\"),\n", ")\n", "\n", "milk_cp_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "99a8a345-f443-4de7-8f3f-10c82feadaab", "metadata": {}, "outputs": [], "source": ["disruption_base_df = pd.merge(carts_base_df, opd_df, on=[\"outlet_id\"], how=\"left\")\n", "\n", "disruption_base_df = pd.merge(disruption_base_df, milk_cp_df, on=[\"outlet_id\"], how=\"left\")\n", "\n", "disruption_base_df[[\"cp_bucket\", \"ftype_\"]] = disruption_base_df[[\"cp_bucket\", \"ftype_\"]].fillna(\n", "    \"low\"\n", ")\n", "\n", "disruption_base_df[\"opd_deviation\"] = (\n", "    disruption_base_df[\"fac_carts\"] / disruption_base_df[\"opd\"] - 1\n", ")\n", "\n", "disruption_base_df[\"cp_deviation\"] = disruption_base_df[\"cp\"] / disruption_base_df[\"milk_cp\"] - 1\n", "\n", "disruption_base_df[\"opd_flag\"] = np.where(\n", "    (disruption_base_df[\"ftype_\"].isin({\"high\", \"medium\"}))\n", "    & (disruption_base_df[\"opd_deviation\"] <= -0.25),\n", "    1,\n", "    0,\n", ")\n", "\n", "disruption_base_df[\"opd_flag\"] = np.where(\n", "    (disruption_base_df[\"ftype_\"].isin({\"high\", \"medium\"}))\n", "    & (disruption_base_df[\"opd_deviation\"] >= 0.3),\n", "    1,\n", "    disruption_base_df[\"opd_flag\"],\n", ")\n", "\n", "disruption_base_df[\"cp_flag\"] = np.where(\n", "    (disruption_base_df[\"ftype_\"].isin({\"high\", \"medium\"}))\n", "    & (disruption_base_df[\"cp_deviation\"] <= -0.4),\n", "    1,\n", "    0,\n", ")\n", "\n", "disruption_base_df[\"d_flag\"] = disruption_base_df[\"opd_flag\"] + disruption_base_df[\"cp_flag\"]\n", "\n", "disruption_df = (\n", "    disruption_base_df[disruption_base_df[\"d_flag\"] > 0][[\"outlet_id\", \"date_\", \"d_flag\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "print(disruption_df.shape)\n", "\n", "print(disruption_base_df.shape)"]}, {"cell_type": "markdown", "id": "b4f3f99c-9d4f-41c9-9a56-83c5d4824448", "metadata": {}, "source": ["#### Removing disruption flag after polgon changed date"]}, {"cell_type": "code", "execution_count": null, "id": "f1d90b8c-8ff9-4567-a663-4335<PERSON>de27a", "metadata": {}, "outputs": [], "source": ["buffer_store = f\"\"\"\n", "    with\n", "    frontend_merchant_mapping as\n", "            (select * from dwh.dim_merchant_outlet_facility_mapping\n", "                where \n", "                    is_frontend_merchant_active = true\n", "                    and\n", "                        is_backend_merchant_active = true\n", "                    and \n", "                        is_pos_outlet_active = 1\n", "                    and \n", "                        is_mapping_enabled = true\n", "                    and \n", "                        is_express_store = true\n", "                    and \n", "                        is_current_mapping_active = true\n", "                    and \n", "                        is_current = true\n", "                    and \n", "                        pos_outlet_name <> 'SS Gurgaon Test Store'\n", "    ),\n", "\n", "    store_polygon_updates as (\n", "    select \n", "        cr.updated_at + interval '5' hour + interval '30' minute as updated_time, \n", "        f.external_id as merchant_id, \n", "        json_query(cr.meta, 'strict $.business_impact.old_order_count') as old_order_count,\n", "        json_query(cr.meta, 'strict $.business_impact.new_order_count') as new_orders_count,\n", "        json_query(cr.diff, 'strict $.polygon.info') as change_in_area\n", "    from \n", "        sauron.change_requests cr \n", "    join \n", "        sauron.feature f \n", "        on f.id=cr.feature_id\n", "    where \n", "        f.layer_id = 6 \n", "        AND cr.updated_at + interval '5' hour + interval '30' minute > CURRENT_DATE - interval '30' day \n", "        and cr.updated_at + interval '5' hour + interval '30' minute < CURRENT_DATE \n", "        AND cr.state = 'MERGED'\n", "        -- and f.external_id in (33207)\n", "    )\n", "\n", "    select \n", "        fm.pos_outlet_id as outlet_id,\n", "        fm.facility_id as facility_id,\n", "        date(max(updated_time)) as date_n\n", "\n", "    from \n", "        store_polygon_updates s\n", "    left join frontend_merchant_mapping  fm on fm.frontend_merchant_id = s.merchant_id\n", "    where s.updated_time between  date'{today_date}' - interval '10' day and date'{today_date}' - interval '1' day\n", "\n", "group by 1,2\n", "    \"\"\"\n", "\n", "buffer_store_mapping = pd.read_sql_query(buffer_store, trino)\n", "buffer_store_mapping[\"date_n\"] = pd.to_datetime(buffer_store_mapping[\"date_n\"])\n", "\n", "buffer_store_outlet_list = list(set(buffer_store_mapping[\"outlet_id\"].unique()))\n", "buffer_store_facility_list = list(set(buffer_store_mapping[\"facility_id\"].unique()))"]}, {"cell_type": "code", "execution_count": null, "id": "c4dbae1c-7888-4831-928c-f7501d58ce42", "metadata": {}, "outputs": [], "source": ["# wl_store_df = pd.concat([buffer_store_mapping,new_store_mapping])\n", "disruption_base_df = disruption_base_df.merge(\n", "    buffer_store_mapping[[\"outlet_id\", \"date_n\"]], on=[\"outlet_id\"], how=\"left\"\n", ")\n", "disruption_base_df[\"d_flag\"] = np.where(\n", "    disruption_base_df[\"date_\"] >= disruption_base_df[\"date_n\"],\n", "    0,\n", "    disruption_base_df[\"d_flag\"],\n", ")\n", "disruption_base_df.drop(columns={\"date_n\"}, inplace=True)"]}, {"cell_type": "markdown", "id": "12879315-b666-4cd8-abac-174113619d33", "metadata": {}, "source": ["#### Store Type Defination"]}, {"cell_type": "code", "execution_count": null, "id": "ea7bf5c1-9366-4639-bab9-8a0d2904a597", "metadata": {}, "outputs": [], "source": ["store_type_df = (\n", "    disruption_base_df[[\"outlet_id\", \"cp_bucket\", \"ftype_\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "store_type_df[\"ftype_\"] = np.where(\n", "    (store_type_df[\"ftype_\"] == \"medium\") & (store_type_df[\"cp_bucket\"].isin({\"low\"})),\n", "    \"low\",\n", "    store_type_df[\"ftype_\"],\n", ")\n", "\n", "store_type_df[\"ftype_\"] = np.where(\n", "    (store_type_df[\"ftype_\"] == \"high\") & (store_type_df[\"cp_bucket\"].isin({\"medium\", \"low\"})),\n", "    \"medium\",\n", "    store_type_df[\"ftype_\"],\n", ")\n", "\n", "store_type_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "9d4d19b2-625f-4e3f-a2f1-2a696c888b03", "metadata": {}, "outputs": [], "source": ["store_type_df.groupby([\"ftype_\"]).agg({\"outlet_id\": \"count\"}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "813b6402-9419-4c18-b620-fdd5e0ed4b58", "metadata": {}, "outputs": [], "source": ["del [opd_df, milk_cp_df]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "02ce19f3-06de-4279-965b-64b4a47e91fc", "metadata": {"tags": []}, "source": ["### Bucketing - Item Ranking"]}, {"cell_type": "code", "execution_count": null, "id": "bd85405a-80e7-4d99-91e9-e5e426e63e56", "metadata": {}, "outputs": [], "source": ["sku_rank_df = (\n", "    sales_pre_df.groupby([\"date_\", \"facility_id\", \"item_id\"]).agg({\"quantity\": \"sum\"}).reset_index()\n", ")\n", "\n", "sku_rank_df[\"quantity\"] = np.where(\n", "    sku_rank_df.item_id.isin(one_l_item_id), sku_rank_df[\"quantity\"] * 2, sku_rank_df[\"quantity\"]\n", ")\n", "\n", "sku_rank_df = (\n", "    sku_rank_df.groupby([\"facility_id\", \"item_id\"]).agg({\"quantity\": \"mean\"}).reset_index()\n", ")\n", "\n", "sku_rank_df[\"quantity\"] = sku_rank_df[\"quantity\"].astype(int)\n", "\n", "sku_rank_df = sku_rank_df.sort_values(by=\"quantity\", ascending=True)\n", "\n", "sku_rank_df[\"cum_quantity\"] = sku_rank_df.groupby([\"facility_id\"])[\"quantity\"].cumsum()\n", "\n", "sku_agg_df = (\n", "    sku_rank_df.groupby([\"facility_id\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"quantity\": \"tot_quantity\"})\n", ")\n", "\n", "sku_rank_df = sku_rank_df.merge(sku_agg_df, on=[\"facility_id\"], how=\"left\")\n", "\n", "sku_rank_df[\"perc_contri\"] = sku_rank_df[\"cum_quantity\"] / sku_rank_df[\"tot_quantity\"]\n", "\n", "## 70% sales contributing.. (If consistantly lower fills then distribution changes)\n", "sku_rank_df[\"stype_\"] = np.where(sku_rank_df[\"perc_contri\"] > 0.3, \"top\", \"bottom\")\n", "\n", "sku_rank_df = sku_rank_df[[\"facility_id\", \"item_id\", \"stype_\"]].drop_duplicates()\n", "\n", "sku_rank_df.head(1)"]}, {"cell_type": "markdown", "id": "9548698b-0483-4933-b9ed-443042d4815f", "metadata": {}, "source": ["### Premium Milk Calculation"]}, {"cell_type": "code", "execution_count": null, "id": "50bea53d-6233-48e0-bce1-ed20f71a54ec", "metadata": {}, "outputs": [], "source": ["premium_df = (\n", "    sales_pre_df.groupby([\"city\", \"date_\", \"item_id\"])\n", "    .agg({\"quantity\": \"sum\", \"facility_id\": \"nunique\"})\n", "    .reset_index()\n", ")\n", "\n", "premium_df[\"qps\"] = premium_df[\"quantity\"] / premium_df[\"facility_id\"]\n", "\n", "premium_df = premium_df.groupby([\"city\", \"item_id\"]).agg({\"qps\": \"mean\"}).reset_index()\n", "\n", "premium_df[\"premium_flag\"] = np.where(premium_df[\"qps\"] <= 5, 1, 0)\n", "\n", "premium_df.shape"]}, {"cell_type": "markdown", "id": "c489d996-0cc1-4433-9a3e-2178f2acd1b9", "metadata": {"tags": []}, "source": ["### Merge with Assortment "]}, {"cell_type": "code", "execution_count": null, "id": "54c7360d-377a-4410-9beb-f07ae2cd01c5", "metadata": {"tags": []}, "outputs": [], "source": ["assortment_filter_df = pd.merge(\n", "    assortment_df, store_type_df[[\"outlet_id\", \"ftype_\"]], on=[\"outlet_id\"], how=\"left\"\n", ")\n", "\n", "assortment_filter_df = assortment_filter_df.merge(\n", "    sku_rank_df, on=[\"facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "\n", "assortment_filter_df = assortment_filter_df.merge(\n", "    premium_df.drop(columns={\"qps\"}), on=[\"city\", \"item_id\"], how=\"left\"\n", ")\n", "\n", "assortment_filter_df[\"stype_\"] = np.where(\n", "    assortment_filter_df[\"premium_flag\"] == 1, \"premium\", assortment_filter_df[\"stype_\"]\n", ")\n", "\n", "assortment_filter_df = assortment_filter_df.drop(columns={\"premium_flag\"})\n", "\n", "assortment_filter_df = assortment_filter_df.merge(\n", "    disruption_df, on=[\"outlet_id\", \"date_\"], how=\"left\"\n", ")\n", "\n", "assortment_filter_df[\"ftype_\"] = assortment_filter_df[\"ftype_\"].fillna(\"low\")\n", "assortment_filter_df[\"stype_\"] = assortment_filter_df[\"stype_\"].fillna(\"bottom\")\n", "\n", "assortment_filter_df[\"d_flag\"] = np.where(\n", "    assortment_filter_df[\"d_flag\"].isna(), 0, assortment_filter_df[\"d_flag\"]\n", ")\n", "\n", "## Ordering Assortment DataFrame\n", "assortment_ordering_df = assortment_filter_df[\n", "    [\"city\", \"item_id\", \"facility_id\", \"be_facility_id\", \"fdate\", \"ftype_\", \"stype_\"]\n", "].drop_duplicates()\n", "\n", "print(\"intial dataframe size..\", assortment_filter_df.shape)\n", "\n", "## Removing Store Disruption\n", "assortment_filter_df = (\n", "    assortment_filter_df[assortment_filter_df[\"d_flag\"] == 0]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\", \"d_flag\"})\n", ")\n", "\n", "print(\"store disruption removed..\", assortment_filter_df.shape)\n", "\n", "## Removing City Disruption\n", "assortment_filter_df = assortment_filter_df.merge(\n", "    tot_disruption_df, on=[\"city\", \"date_\"], how=\"left\"\n", ")\n", "\n", "assortment_filter_df[\"tot_flag\"] = np.where(assortment_filter_df[\"tot_flag\"].isna(), 0, 1)\n", "\n", "assortment_filter_df = (\n", "    assortment_filter_df[assortment_filter_df[\"tot_flag\"] == 0]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\", \"tot_flag\"})\n", ")\n", "\n", "\n", "print(\"city disruption removed..\", assortment_filter_df.shape)"]}, {"cell_type": "markdown", "id": "aea74063-bba6-4cb4-80fb-3eedaf004053", "metadata": {}, "source": ["## Mother Dairy Base"]}, {"cell_type": "code", "execution_count": null, "id": "10a1443a-98cc-4bf6-9727-9afa9d228c2d", "metadata": {}, "outputs": [], "source": ["try:\n", "    md_facility_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"mother_dairy_facility\",\n", "    )\n", "\n", "except:\n", "    md_facility_df = pd.read_csv(\"mother_dairy_facility.csv\")\n", "md_facility_df[[\"outlet_id\", \"item_id\"]] = md_facility_df[[\"outlet_id\", \"item_id\"]].astype(int)\n", "md_facility_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "4bf664b1-c353-4746-84e2-3f45c588a320", "metadata": {}, "outputs": [], "source": ["md_base_df = pd.merge(\n", "    assortment_filter_df, md_facility_df, on=[\"outlet_id\", \"item_id\"], how=\"inner\"\n", ")\n", "print(md_base_df.shape)\n", "md_base_df[\"flag\"] = 1\n", "md_base_df.head(1)"]}, {"cell_type": "markdown", "id": "a0293674-a466-433d-ab56-7dfa8db00516", "metadata": {}, "source": ["## Filtered Base Assortment"]}, {"cell_type": "code", "execution_count": null, "id": "714c7cd4-6361-4c64-adef-18def3f374bd", "metadata": {}, "outputs": [], "source": ["assortment_filter_df = pd.merge(\n", "    assortment_filter_df,\n", "    md_base_df,\n", "    on=[\n", "        \"city\",\n", "        \"facility_id\",\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        # \"item_name\",\n", "        \"be_facility_id\",\n", "        \"date_\",\n", "        \"hour_\",\n", "        \"tat\",\n", "        \"winter_flag\",\n", "        \"fdate\",\n", "        \"fdow\",\n", "        \"is_available\",\n", "        \"dow\",\n", "        \"ftype_\",\n", "        \"stype_\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "assortment_filter_df[\"flag\"] = assortment_filter_df[\"flag\"].fillna(0)\n", "assortment_filter_df = assortment_filter_df[assortment_filter_df[\"flag\"] == 0].drop(\n", "    columns={\"flag\"}\n", ")\n", "print(assortment_filter_df.shape)\n", "assortment_filter_df.head(1)"]}, {"cell_type": "markdown", "id": "4922bb2b-2857-473c-8f64-f55d2755eca7", "metadata": {}, "source": ["### <PERSON> Dairy Slot Wise Base "]}, {"cell_type": "markdown", "id": "e49ce56b-512f-45fb-829f-9a232399575b", "metadata": {}, "source": ["#### For Tat = 1"]}, {"cell_type": "code", "execution_count": null, "id": "4e361a72-0de6-4df2-aa58-d8c32bddd811", "metadata": {}, "outputs": [], "source": ["pre_md_base_df = md_base_df[md_base_df[\"hour_\"].between(17, 23)].drop(columns={\"flag\"})\n", "pre_md_base_df[\"tat\"] = np.where(\n", "    pre_md_base_df[\"tat\"] == 2,\n", "    1,\n", "    np.where(pre_md_base_df[\"tat\"] > 2, pre_md_base_df[\"tat\"], 0),\n", ")\n", "pre_md_base_df[\"fdate\"] = pd.to_datetime(today_date) + pd.to_timedelta(\n", "    pre_md_base_df[\"tat\"], unit=\"D\"\n", ")\n", "\n", "pre_md_base_df[\"fdow\"] = pd.to_datetime(pre_md_base_df[\"fdate\"]).dt.dayofweek\n", "\n", "print(pre_md_base_df.shape)\n", "pre_md_base_df.head(1)"]}, {"cell_type": "markdown", "id": "e265e3d7-c4e8-4b49-ab7f-3ea18c269564", "metadata": {}, "source": ["#### For Tat = 2"]}, {"cell_type": "code", "execution_count": null, "id": "c706b23f-0164-4d4d-ba48-34f337dd8c0f", "metadata": {}, "outputs": [], "source": ["post_md_base_df = md_base_df[md_base_df[\"hour_\"].between(6, 23)].drop(columns={\"flag\"})\n", "\n", "print(post_md_base_df.shape)\n", "post_md_base_df.head(1)"]}, {"cell_type": "markdown", "id": "0e2130c5-21fb-4ae6-8359-96120ffbf291", "metadata": {"tags": []}, "source": ["## Ranking & Sequencing"]}, {"cell_type": "markdown", "id": "db3bafd7-66a8-4c65-8a42-32409c90535d", "metadata": {}, "source": ["#### For Filtered Base Assortment"]}, {"cell_type": "code", "execution_count": null, "id": "1c28f8a9-d067-4e63-8449-687ebcf56f32", "metadata": {}, "outputs": [], "source": ["ranking_df = assortment_filter_df[\n", "    [\"item_id\", \"facility_id\", \"date_\", \"dow\", \"fdate\", \"fdow\"]\n", "].drop_duplicates()\n", "\n", "ranking_df[\"r1\"] = ranking_df.groupby([\"item_id\", \"facility_id\"])[\"date_\"].rank(\n", "    method=\"dense\", ascending=False\n", ")\n", "\n", "ranking_df[\"flag\"] = np.where(\n", "    (ranking_df[\"facility_id\"].isin(buffer_store_facility_list)) & (ranking_df[\"r1\"] > 3),\n", "    1,\n", "    0,\n", ")\n", "same_dow = (\n", "    ranking_df[ranking_df[\"dow\"] == ranking_df[\"fdow\"]].copy().sort_values(\"date_\", ascending=False)\n", ")\n", "same_dow[\"latest_dow\"] = same_dow.groupby([\"item_id\", \"facility_id\", \"fdate\"])[\"date_\"].rank(\n", "    method=\"dense\", ascending=False\n", ")\n", "same_dow = same_dow[same_dow.latest_dow > 1]\n", "same_dow[\"nd_flag\"] = 1\n", "ranking_df = ranking_df.merge(\n", "    same_dow[[\"facility_id\", \"item_id\", \"date_\", \"nd_flag\"]],\n", "    on=[\"facility_id\", \"item_id\", \"date_\"],\n", "    how=\"left\",\n", ")\n", "ranking_df[\"nd_flag\"] = ranking_df[\"nd_flag\"].fillna(0)\n", "ranking_df = ranking_df[ranking_df[\"nd_flag\"] == 0]\n", "ranking_df = ranking_df[ranking_df[\"flag\"] == 0]\n", "ranking_df[\"r2\"] = np.where(\n", "    (ranking_df[\"dow\"] == ranking_df[\"fdow\"])\n", "    & ~(ranking_df[\"facility_id\"].isin(buffer_store_facility_list)),\n", "    ranking_df[\"r1\"],\n", "    ranking_df[\"r1\"] + 30,\n", ")\n", "\n", "\n", "#\n", "ranking_df[\"rank\"] = ranking_df.groupby([\"item_id\", \"facility_id\"])[\"r2\"].rank(\n", "    method=\"dense\", ascending=True\n", ")\n", "ranking_df = ranking_df[ranking_df[\"rank\"] < 8]\n", "\n", "ranking_df = ranking_df[[\"facility_id\", \"item_id\", \"date_\", \"rank\"]].drop_duplicates()\n", "\n", "print(ranking_df.shape)\n", "ranking_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "370d061c-d4f9-42d2-ad2f-0f7fcafc21bc", "metadata": {}, "outputs": [], "source": ["ranking_df[(ranking_df.facility_id == 1564) & (ranking_df.item_id == 10005109)]"]}, {"cell_type": "code", "execution_count": null, "id": "88b981c1-803d-4130-b3b3-6d6f1e484de0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "9ecb5a9a-8b06-4f44-a66d-e3831765f58c", "metadata": {}, "source": ["#### For Mother dairy tat = 1"]}, {"cell_type": "code", "execution_count": null, "id": "bdaef820-3858-4257-9ccc-26baafae8452", "metadata": {}, "outputs": [], "source": ["pre_ranking_df = pre_md_base_df[\n", "    [\"item_id\", \"facility_id\", \"date_\", \"dow\", \"fdate\", \"fdow\"]\n", "].drop_duplicates()\n", "\n", "pre_ranking_df[\"r1\"] = pre_ranking_df.groupby([\"item_id\", \"facility_id\"])[\"date_\"].rank(\n", "    method=\"dense\", ascending=False\n", ")\n", "\n", "\n", "pre_ranking_df[\"flag\"] = np.where(\n", "    (pre_ranking_df[\"facility_id\"].isin(buffer_store_facility_list)) & (pre_ranking_df[\"r1\"] > 3),\n", "    1,\n", "    0,\n", ")\n", "same_dow = (\n", "    pre_ranking_df[pre_ranking_df[\"dow\"] == pre_ranking_df[\"fdow\"]]\n", "    .copy()\n", "    .sort_values(\"date_\", ascending=False)\n", ")\n", "same_dow[\"latest_dow\"] = same_dow.groupby([\"item_id\", \"facility_id\", \"fdate\"])[\"date_\"].rank(\n", "    method=\"dense\", ascending=False\n", ")\n", "same_dow = same_dow[same_dow.latest_dow > 1]\n", "same_dow[\"nd_flag\"] = 1\n", "pre_ranking_df = pre_ranking_df.merge(\n", "    same_dow[[\"facility_id\", \"item_id\", \"date_\", \"nd_flag\"]],\n", "    on=[\"facility_id\", \"item_id\", \"date_\"],\n", "    how=\"left\",\n", ")\n", "pre_ranking_df[\"nd_flag\"] = pre_ranking_df[\"nd_flag\"].fillna(0)\n", "pre_ranking_df = pre_ranking_df[pre_ranking_df[\"nd_flag\"] == 0]\n", "pre_ranking_df = pre_ranking_df[pre_ranking_df[\"flag\"] == 0]\n", "pre_ranking_df[\"r2\"] = np.where(\n", "    (pre_ranking_df[\"dow\"] == pre_ranking_df[\"fdow\"])\n", "    & ~(pre_ranking_df[\"facility_id\"].isin(buffer_store_facility_list)),\n", "    pre_ranking_df[\"r1\"],\n", "    pre_ranking_df[\"r1\"] + 30,\n", ")\n", "\n", "\n", "pre_ranking_df[\"rank\"] = pre_ranking_df.groupby([\"item_id\", \"facility_id\"])[\"r2\"].rank(\n", "    method=\"dense\", ascending=True\n", ")\n", "\n", "pre_ranking_df = pre_ranking_df[pre_ranking_df[\"rank\"] < 8]\n", "pre_ranking_df = pre_ranking_df[[\"facility_id\", \"item_id\", \"date_\", \"rank\"]].drop_duplicates()\n", "\n", "print(pre_ranking_df.shape)\n", "pre_ranking_df.head(1)"]}, {"cell_type": "markdown", "id": "585957fb-107a-42ff-8f18-2b664c2c5f78", "metadata": {}, "source": ["#### For Mother dairy tat = 2"]}, {"cell_type": "code", "execution_count": null, "id": "5d1ce347-a0b1-4f23-aee3-436ede655eed", "metadata": {}, "outputs": [], "source": ["post_ranking_df = post_md_base_df[\n", "    [\"item_id\", \"facility_id\", \"date_\", \"dow\", \"fdate\", \"fdow\"]\n", "].drop_duplicates()\n", "\n", "post_ranking_df[\"r1\"] = post_ranking_df.groupby([\"item_id\", \"facility_id\"])[\"date_\"].rank(\n", "    method=\"dense\", ascending=False\n", ")\n", "\n", "post_ranking_df[\"flag\"] = np.where(\n", "    (post_ranking_df[\"facility_id\"].isin(buffer_store_facility_list)) & (post_ranking_df[\"r1\"] > 3),\n", "    1,\n", "    0,\n", ")\n", "same_dow = (\n", "    post_ranking_df[post_ranking_df[\"dow\"] == post_ranking_df[\"fdow\"]]\n", "    .copy()\n", "    .sort_values(\"date_\", ascending=False)\n", ")\n", "same_dow[\"latest_dow\"] = same_dow.groupby([\"item_id\", \"facility_id\", \"fdate\"])[\"date_\"].rank(\n", "    method=\"dense\", ascending=False\n", ")\n", "same_dow = same_dow[same_dow.latest_dow > 1]\n", "same_dow[\"nd_flag\"] = 1\n", "post_ranking_df = post_ranking_df.merge(\n", "    same_dow[[\"facility_id\", \"item_id\", \"date_\", \"nd_flag\"]],\n", "    on=[\"facility_id\", \"item_id\", \"date_\"],\n", "    how=\"left\",\n", ")\n", "post_ranking_df[\"nd_flag\"] = post_ranking_df[\"nd_flag\"].fillna(0)\n", "post_ranking_df = post_ranking_df[post_ranking_df[\"nd_flag\"] == 0]\n", "post_ranking_df = post_ranking_df[post_ranking_df[\"flag\"] == 0]\n", "post_ranking_df[\"r2\"] = np.where(\n", "    (post_ranking_df[\"dow\"] == post_ranking_df[\"fdow\"])\n", "    & ~(post_ranking_df[\"facility_id\"].isin(buffer_store_facility_list)),\n", "    post_ranking_df[\"r1\"],\n", "    post_ranking_df[\"r1\"] + 30,\n", ")\n", "\n", "\n", "post_ranking_df[\"rank\"] = post_ranking_df.groupby([\"item_id\", \"facility_id\"])[\"r2\"].rank(\n", "    method=\"dense\", ascending=True\n", ")\n", "\n", "post_ranking_df = post_ranking_df[post_ranking_df[\"rank\"] < 8]\n", "post_ranking_df = post_ranking_df[[\"facility_id\", \"item_id\", \"date_\", \"rank\"]].drop_duplicates()\n", "\n", "print(post_ranking_df.shape)\n", "pre_ranking_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "062d8736-0306-4a1f-9ac1-dc9bb732342e", "metadata": {}, "outputs": [], "source": ["post_ranking_df[post_ranking_df[\"facility_id\"] == 271].head(1)"]}, {"cell_type": "markdown", "id": "6233efe0-2d88-47da-a59d-3c5c4b9ccf4f", "metadata": {"tags": []}, "source": ["## Final Assortment (<PERSON><PERSON> Base With Date Ranks)"]}, {"cell_type": "markdown", "id": "9ce9d76c-11d4-4ac8-a786-8bc546f99f4a", "metadata": {}, "source": ["### For Filtered Base Assortment"]}, {"cell_type": "code", "execution_count": null, "id": "42851bcb-4137-4b40-bd16-d1bd27f365d6", "metadata": {}, "outputs": [], "source": ["assortment_base_df = pd.merge(\n", "    assortment_filter_df,\n", "    ranking_df,\n", "    on=[\"item_id\", \"facility_id\", \"date_\"],\n", "    how=\"left\",\n", ")\n", "assortment_base_df[\"rank\"] = assortment_base_df[\"rank\"].fillna(666)\n", "assortment_base_df[\"flag\"] = np.where(\n", "    (assortment_base_df[\"stype_\"].isin({\"top\", \"bottom\"})) & (assortment_base_df[\"hour_\"] <= 23),\n", "    1,\n", "    np.where(\n", "        (assortment_base_df[\"stype_\"].isin({\"premium\"})) & (assortment_base_df[\"hour_\"] < 21),\n", "        1,\n", "        0,\n", "    ),\n", ")\n", "\n", "assortment_base_df[\"flag\"] = np.where(\n", "    (assortment_base_df[\"stype_\"].isin({\"top\", \"bottom\"}))\n", "    & (assortment_base_df[\"hour_\"] <= 23)\n", "    & (assortment_base_df[\"winter_flag\"] == 1),\n", "    1,\n", "    assortment_base_df[\"flag\"],\n", ")\n", "\n", "## aavin for chennai\n", "# assortment_base_df[\"flag\"] = np.where(\n", "#     (assortment_base_df[\"city\"] == \"Chennai\")\n", "#     & (assortment_base_df[\"item_id\"].isin({10110668, 10110669, 10114626}))\n", "#     & (assortment_base_df[\"hour_\"] > 20),\n", "#     0,\n", "#     assortment_base_df[\"flag\"],\n", "# )\n", "# assortment_base_df[\"flag\"] = np.where(\n", "#     (assortment_base_df[\"item_id\"].isin(one_l_item_id)) & (assortment_base_df[\"hour_\"] > 19),\n", "#     0,\n", "#     assortment_base_df[\"flag\"],\n", "# )\n", "\n", "assortment_base_df = assortment_base_df[assortment_base_df[\"flag\"] == 1].drop(columns={\"flag\"})\n", "\n", "print(assortment_base_df.shape)\n", "\n", "assortment_base_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "794217b6-2d03-41bb-8dfa-2817b250ff0e", "metadata": {}, "outputs": [], "source": ["assortment_base_df[(assortment_base_df[\"item_id\"].isin(one_l_item_id))][\n", "    [\"item_id\", \"hour_\"]\n", "].groupby(\"item_id\").max(\"hour\")"]}, {"cell_type": "markdown", "id": "4c6f2606-edf6-4013-a40b-5059b8ab7d7b", "metadata": {}, "source": ["### For Mother Dairy Tat = 1"]}, {"cell_type": "code", "execution_count": null, "id": "e05ecc9d-d0f3-4027-ab92-695616707428", "metadata": {}, "outputs": [], "source": ["pre_md_base_df = pd.merge(\n", "    pre_md_base_df, pre_ranking_df, on=[\"item_id\", \"facility_id\", \"date_\"], how=\"inner\"\n", ")\n", "\n", "pre_md_base_df[\"flag\"] = np.where(\n", "    (pre_md_base_df[\"stype_\"].isin({\"top\", \"bottom\"})) & (pre_md_base_df[\"hour_\"] <= 23),\n", "    1,\n", "    np.where(\n", "        (pre_md_base_df[\"stype_\"].isin({\"premium\"})) & (pre_md_base_df[\"hour_\"] < 21),\n", "        1,\n", "        0,\n", "    ),\n", ")\n", "\n", "pre_md_base_df[\"flag\"] = np.where(\n", "    (pre_md_base_df[\"stype_\"].isin({\"top\", \"bottom\"}))\n", "    & (pre_md_base_df[\"hour_\"] <= 23)\n", "    & (pre_md_base_df[\"winter_flag\"] == 1),\n", "    1,\n", "    pre_md_base_df[\"flag\"],\n", ")\n", "\n", "pre_md_base_df[\"flag\"] = np.where(\n", "    (pre_md_base_df[\"item_id\"].isin(one_l_item_id)) & (pre_md_base_df[\"hour_\"] > 19),\n", "    0,\n", "    1,\n", ")\n", "\n", "pre_md_base_df = pre_md_base_df[pre_md_base_df[\"flag\"] == 1].drop(columns={\"flag\"})\n", "\n", "print(pre_md_base_df.shape)\n", "\n", "pre_md_base_df.head(1)"]}, {"cell_type": "markdown", "id": "5619efc3-3dd1-4761-9214-fdcc0953f635", "metadata": {}, "source": ["### For Mother Dairy <PERSON>t = 2"]}, {"cell_type": "code", "execution_count": null, "id": "5bc191bf-e294-44e8-91e0-b732878737be", "metadata": {}, "outputs": [], "source": ["post_md_base_df = pd.merge(\n", "    post_md_base_df,\n", "    post_ranking_df,\n", "    on=[\"item_id\", \"facility_id\", \"date_\"],\n", "    how=\"inner\",\n", ")\n", "\n", "post_md_base_df[\"flag\"] = np.where(\n", "    (post_md_base_df[\"stype_\"].isin({\"top\", \"bottom\"})) & (post_md_base_df[\"hour_\"] <= 23),\n", "    1,\n", "    np.where(\n", "        (post_md_base_df[\"stype_\"].isin({\"premium\"})) & (post_md_base_df[\"hour_\"] < 21),\n", "        1,\n", "        0,\n", "    ),\n", ")\n", "\n", "post_md_base_df[\"flag\"] = np.where(\n", "    (post_md_base_df[\"stype_\"].isin({\"top\", \"bottom\"}))\n", "    & (post_md_base_df[\"hour_\"] <= 23)\n", "    & (post_md_base_df[\"winter_flag\"] == 1),\n", "    1,\n", "    post_md_base_df[\"flag\"],\n", ")\n", "\n", "post_md_base_df[\"flag\"] = np.where(\n", "    (post_md_base_df[\"item_id\"].isin(one_l_item_id)) & (post_md_base_df[\"hour_\"] > 19),\n", "    0,\n", "    1,\n", ")\n", "\n", "post_md_base_df = post_md_base_df[post_md_base_df[\"flag\"] == 1].drop(columns={\"flag\"})\n", "\n", "print(post_md_base_df.shape)\n", "\n", "post_md_base_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "92cc9ee2-a8e5-4e2d-84a0-1ca9ba9d1d38", "metadata": {}, "outputs": [], "source": ["del [\n", "    assortment_filter_df,\n", "    ranking_df,\n", "    sku_rank_df,\n", "    premium_df,\n", "    pre_ranking_df,\n", "    post_ranking_df,\n", "]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "0ca7f976-06b7-4f4b-b7d3-cbf75239303e", "metadata": {"tags": []}, "source": ["## Extrapolated CPD Calculations"]}, {"cell_type": "markdown", "id": "b27acc0c-4b90-4fee-9e44-f9b303e2762b", "metadata": {"tags": []}, "source": ["#### Filtered Assortment & Sales Data Merge"]}, {"cell_type": "code", "execution_count": null, "id": "139f875c-3225-4b27-947b-a2a5f11a5457", "metadata": {}, "outputs": [], "source": ["assortment_sales_df = pd.merge(\n", "    assortment_base_df,\n", "    sales_pre_df,\n", "    on=[\"item_id\", \"facility_id\", \"hour_\", \"date_\", \"city\"],\n", "    how=\"left\",\n", ")\n", "\n", "assortment_sales_df[\"is_available\"] = np.where(\n", "    assortment_sales_df[\"quantity\"] > 0, 1, assortment_sales_df[\"is_available\"]\n", ")\n", "\n", "assortment_sales_df[\"quantity\"] = assortment_sales_df[\"quantity\"].fillna(0)\n", "\n", "print(assortment_sales_df.shape)\n", "assortment_sales_df.head(1)"]}, {"cell_type": "markdown", "id": "d77bc83b-0f55-4bde-a954-462fa260bee3", "metadata": {}, "source": ["#### Mother dairy with tat = 1 & Sales Data Merge"]}, {"cell_type": "code", "execution_count": null, "id": "91aef057-95df-4a8b-bfb2-78b9811a8976", "metadata": {}, "outputs": [], "source": ["pre_md_sales_df = pd.merge(\n", "    pre_md_base_df,\n", "    sales_pre_df,\n", "    on=[\"item_id\", \"facility_id\", \"hour_\", \"date_\", \"city\"],\n", "    how=\"left\",\n", ")\n", "\n", "pre_md_sales_df[\"is_available\"] = np.where(\n", "    pre_md_sales_df[\"quantity\"] > 0, 1, pre_md_sales_df[\"is_available\"]\n", ")\n", "\n", "pre_md_sales_df[\"quantity\"] = pre_md_sales_df[\"quantity\"].fillna(0)\n", "\n", "print(pre_md_sales_df.shape)\n", "pre_md_sales_df.head(1)"]}, {"cell_type": "markdown", "id": "24f93172-682f-4655-84b5-bad2836ce5d6", "metadata": {}, "source": ["#### Mother dairy with tat = 2 & Sales Data Merge"]}, {"cell_type": "code", "execution_count": null, "id": "969f2d50-ce41-4971-9261-e91ddd0b0d07", "metadata": {}, "outputs": [], "source": ["post_md_sales_df = pd.merge(\n", "    post_md_base_df,\n", "    sales_pre_df,\n", "    on=[\"item_id\", \"facility_id\", \"hour_\", \"date_\", \"city\"],\n", "    how=\"left\",\n", ")\n", "\n", "post_md_sales_df[\"is_available\"] = np.where(\n", "    post_md_sales_df[\"quantity\"] > 0, 1, post_md_sales_df[\"is_available\"]\n", ")\n", "\n", "post_md_sales_df[\"quantity\"] = post_md_sales_df[\"quantity\"].fillna(0)\n", "\n", "print(post_md_sales_df.shape)\n", "post_md_sales_df.head(1)"]}, {"cell_type": "markdown", "id": "e154ae3f-9c01-4f70-ad2e-b406b8b6df7c", "metadata": {"tags": []}, "source": ["## Hour Weights - Search + Carts Logic"]}, {"cell_type": "markdown", "id": "caa25263-19d0-4b83-a1da-1ea68e92c620", "metadata": {"tags": []}, "source": ["#### Carts Hour Weights"]}, {"cell_type": "code", "execution_count": null, "id": "4c331d7b-a3ed-4144-beac-ff3e9dc1ba1b", "metadata": {}, "outputs": [], "source": ["# milk_hourly_carts_query = f\"\"\"\n", "#     WITH item_mapping AS (\n", "#         SELECT DISTINCT ipr.product_id,\n", "#         CASE\n", "#             WHEN ipr.item_id IS NULL THEN ipom_0.item_id\n", "#             ELSE ipr.item_id\n", "#         END AS item_id,\n", "#         CASE\n", "#             WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1)\n", "#             ELSE COALESCE(ipom_0.multiplier,1)\n", "#         END AS multiplier\n", "#         FROM rpc.item_product_mapping ipr\n", "#         LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id\n", "#         LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", "#         WHERE ipr.lake_active_record\n", "#     ),\n", "\n", "#     sales AS (\n", "#         SELECT (oid.cart_checkout_ts_ist) AS order_date, oid.outlet_id, oid.product_id, im.item_id, oid.order_id\n", "#         FROM dwh.fact_sales_order_item_details oid\n", "#         JOIN item_mapping im on im.product_id = oid.product_id\n", "#         WHERE oid.order_create_dt_ist >= DATE('{today_date}') - interval '15' day\n", "#         AND oid.is_internal_order = false\n", "#         AND (oid.order_type NOT LIKE '%%internal%%' OR oid.order_type IS NULL)\n", "#         AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED' AND outlet_id IN {outlet_id_list}\n", "#     ),\n", "\n", "#     pre_summary AS (\n", "#         SELECT outlet_id, hour_, AVG(milk_carts) AS milk_carts\n", "#         FROM (\n", "#             SELECT DATE(order_date) AS date_, EXTRACT(hour FROM order_date) AS hour_, outlet_id, item_id, COUNT(DISTINCT order_id) AS milk_carts\n", "#             FROM sales\n", "#             WHERE item_id IN {item_id_list}\n", "#             GROUP BY 1,2,3,4\n", "#         )\n", "#         WHERE date_ < date'{today_date}'\n", "#         GROUP BY 1,2\n", "#     ),\n", "\n", "#     summary AS (\n", "#         SELECT a.*, cl.name AS city\n", "#         FROM pre_summary a\n", "#         JOIN retail.console_outlet co ON co.id = a.outlet_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "#         JOIN retail.console_location cl ON cl.id = co.tax_location_id AND cl.lake_active_record\n", "#         WHERE hour_ BETWEEN 6 AND 23\n", "#     )\n", "\n", "#     SELECT city, outlet_id, hour_, SUM(milk_carts) AS milk_carts\n", "#     FROM summary\n", "#     GROUP BY 1,2,3\n", "#     \"\"\"\n", "milk_hourly_carts_df = (\n", "    data_df[[\"city\", \"date_\", \"hour_\", \"outlet_id\", \"carts\"]].copy().drop_duplicates()\n", ")\n", "milk_hourly_carts_df = (\n", "    milk_hourly_carts_df.groupby([\"city\", \"outlet_id\", \"hour_\"])\n", "    .agg({\"carts\": percentile})\n", "    .reset_index()\n", ")\n", "milk_agg_df = (\n", "    milk_hourly_carts_df.groupby([\"city\", \"outlet_id\"])\n", "    .agg({\"carts\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"carts\": \"tot_milk_carts\"})\n", ")\n", "\n", "milk_hourly_carts_df = milk_hourly_carts_df.merge(milk_agg_df, on=[\"city\", \"outlet_id\"], how=\"left\")\n", "\n", "milk_hourly_carts_df[\"chw\"] = milk_hourly_carts_df[\"carts\"] / milk_hourly_carts_df[\"tot_milk_carts\"]\n", "\n", "milk_hourly_carts_df = milk_hourly_carts_df[[\"city\", \"outlet_id\", \"hour_\", \"chw\"]]\n", "\n", "milk_hourly_carts_df.shape"]}, {"cell_type": "markdown", "id": "d82cc098-5abf-43a3-8bba-90dce4a7c888", "metadata": {"tags": []}, "source": ["#### Search Hourly Weights"]}, {"cell_type": "code", "execution_count": null, "id": "09275b92-a46a-430c-b83e-48c38f19d544", "metadata": {}, "outputs": [], "source": ["# search_hourly_query = f\"\"\"\n", "#     WITH city_item_hour_search_wt AS (\n", "#         WITH psuedo_base AS (\n", "#             SELECT * FROM supply_etls.merchant_item_search_weights\n", "#             WHERE at_date_ist BETWEEN DATE('{today_date}') - interval '30' day AND DATE('{today_date}') - interval '1' day\n", "#             and item_id in {item_id_list}\n", "#         ),\n", "\n", "#         search_base AS (\n", "#             SELECT b.* from psuedo_base b\n", "#             JOIN rpc.item_category_details icd ON icd.item_id = b.item_id AND icd.l2_id = 1185 AND icd.lake_active_record\n", "#         ),\n", "\n", "#         hour_item_city_search AS (\n", "#             SELECT hour_of_the_day AS hour_, city_name AS city, outlet_id, SUM(searches_total) AS hour_searches\n", "#             FROM search_base\n", "#             GROUP BY 1,2,3\n", "#         ),\n", "\n", "#         item_city_search AS (\n", "#             SELECT outlet_id, city_name AS city, SUM(searches_total) AS outlet_searches\n", "#             FROM search_base\n", "#             GROUP BY 1,2\n", "#         )\n", "\n", "#         SELECT hic.city, hic.outlet_id, hic.hour_, hour_searches * 1.00000 / NULLIF(outlet_searches,0) AS cshw\n", "#         FROM hour_item_city_search hic\n", "#         LEFT JOIN item_city_search ic ON hic.outlet_id = ic.outlet_id AND hic.city = ic.city\n", "#     )\n", "\n", "#     SELECT * FROM city_item_hour_search_wt\n", "#     ORDER BY hour_\n", "#     \"\"\"\n", "# search_hourly_df = read_sql_query(search_hourly_query, trino)\n", "search_hourly_df = (\n", "    data_df[[\"city\", \"date_\", \"hour_\", \"outlet_id\", \"searches\"]].copy().drop_duplicates()\n", ")\n", "search_hourly_df = (\n", "    search_hourly_df.groupby([\"city\", \"outlet_id\", \"hour_\"])\n", "    .agg({\"searches\": percentile})\n", "    .reset_index()\n", ")\n", "search_agg_df = (\n", "    search_hourly_df.groupby(\n", "        [\n", "            \"city\",\n", "            \"outlet_id\",\n", "        ]\n", "    )\n", "    .agg({\"searches\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"searches\": \"tot_milk_searches\"})\n", ")\n", "\n", "search_hourly_df = search_hourly_df.merge(search_agg_df, on=[\"city\", \"outlet_id\"], how=\"left\")\n", "\n", "search_hourly_df[\"cshw\"] = search_hourly_df[\"searches\"] / search_hourly_df[\"tot_milk_searches\"]\n", "\n", "search_hourly_df = search_hourly_df[[\"city\", \"outlet_id\", \"hour_\", \"cshw\"]]\n", "\n", "search_hourly_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "0543b251-6825-4d91-9e6a-655ebc3a7c71", "metadata": {}, "outputs": [], "source": ["hour_weights_df = (\n", "    assortment_df[[\"city\", \"outlet_id\", \"hour_\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "hour_weights_df = hour_weights_df.merge(\n", "    search_hourly_df, on=[\"city\", \"outlet_id\", \"hour_\"], how=\"left\"\n", ")\n", "\n", "hour_weights_df = hour_weights_df.merge(\n", "    milk_hourly_carts_df, on=[\"city\", \"outlet_id\", \"hour_\"], how=\"left\"\n", ")\n", "\n", "hour_weights_df[[\"cshw\", \"chw\"]] = hour_weights_df[[\"cshw\", \"chw\"]].fillna(0)\n", "\n", "hour_weights_df[\"hw\"] = (hour_weights_df[\"cshw\"] * 0.75) + (hour_weights_df[\"chw\"] * 0.25)\n", "\n", "hour_weights_agg_df = (\n", "    hour_weights_df.groupby([\"city\", \"outlet_id\"])\n", "    .agg({\"hw\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"hw\": \"tot_hw\"})\n", ")\n", "\n", "hour_weights_df = hour_weights_df.merge(hour_weights_agg_df, on=[\"city\", \"outlet_id\"], how=\"left\")\n", "\n", "hour_weights_df[\"new_hw\"] = hour_weights_df[\"hw\"] / hour_weights_df[\"tot_hw\"]\n", "\n", "hour_weights_df = (\n", "    hour_weights_df[[\"city\", \"outlet_id\", \"hour_\", \"new_hw\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "print(hour_weights_df.shape)\n", "\n", "hour_weights_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "cfe362b6-853e-462e-ba51-314704a1cca0", "metadata": {}, "outputs": [], "source": ["del [search_hourly_df, milk_hourly_carts_df, hour_weights_agg_df]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "5a79c7c8-93df-4077-8c8e-7be3fac2ba06", "metadata": {}, "source": ["## Weights Merge to Base"]}, {"cell_type": "markdown", "id": "005daa69-3dc8-45ec-a899-f282027d7515", "metadata": {}, "source": ["### Weights merge to filtered base"]}, {"cell_type": "code", "execution_count": null, "id": "9108ba52-7277-407b-9d34-7b74f7f485fe", "metadata": {}, "outputs": [], "source": ["assortment_sales_df = assortment_sales_df.merge(\n", "    hour_weights_df, on=[\"city\", \"outlet_id\", \"hour_\"], how=\"left\"\n", ")\n", "\n", "assortment_sales_df[\"hw\"] = np.where(\n", "    assortment_sales_df[\"new_hw\"].isna(), 0, assortment_sales_df[\"new_hw\"]\n", ")\n", "\n", "assortment_sales_df[\"wt_score\"] = assortment_sales_df[\"is_available\"] * assortment_sales_df[\"hw\"]\n", "\n", "print(assortment_sales_df.shape)\n", "assortment_sales_df.head(1)"]}, {"cell_type": "markdown", "id": "1bc7f214-3cca-4ee8-ba53-8a6399881af9", "metadata": {"tags": []}, "source": ["### Weights merge to Mother dairy base with tat = 1"]}, {"cell_type": "code", "execution_count": null, "id": "af2fc72c-8f46-4b70-8dc2-7810a2b8b71a", "metadata": {}, "outputs": [], "source": ["pre_md_sales_df = pre_md_sales_df.merge(\n", "    hour_weights_df, on=[\"city\", \"outlet_id\", \"hour_\"], how=\"left\"\n", ")\n", "\n", "pre_md_sales_df[\"hw\"] = np.where(pre_md_sales_df[\"new_hw\"].isna(), 0, pre_md_sales_df[\"new_hw\"])\n", "\n", "pre_md_sales_df[\"wt_score\"] = pre_md_sales_df[\"is_available\"] * pre_md_sales_df[\"hw\"]\n", "\n", "print(pre_md_sales_df.shape)\n", "pre_md_sales_df.head(1)"]}, {"cell_type": "markdown", "id": "b6996eae-a54b-4a89-a8bd-68cbe72c812e", "metadata": {}, "source": ["### Weights merge to Mother dairy base with tat = 2"]}, {"cell_type": "code", "execution_count": null, "id": "87b8b02c-7914-4ccf-a1cd-925c4504255e", "metadata": {}, "outputs": [], "source": ["post_md_sales_df = post_md_sales_df.merge(\n", "    hour_weights_df, on=[\"city\", \"outlet_id\", \"hour_\"], how=\"left\"\n", ")\n", "\n", "post_md_sales_df[\"hw\"] = np.where(post_md_sales_df[\"new_hw\"].isna(), 0, post_md_sales_df[\"new_hw\"])\n", "\n", "post_md_sales_df[\"wt_score\"] = post_md_sales_df[\"is_available\"] * post_md_sales_df[\"hw\"]\n", "\n", "print(post_md_sales_df.shape)\n", "post_md_sales_df.head(1)"]}, {"cell_type": "markdown", "id": "822afeb6-7e25-47c8-bce9-4b779cf0aa36", "metadata": {}, "source": ["## Daywise Aggregation"]}, {"cell_type": "markdown", "id": "8d599269-d433-42af-9e37-a2bf596e96ff", "metadata": {}, "source": ["#### For Filtered Base"]}, {"cell_type": "code", "execution_count": null, "id": "82dfeb8f-b32c-456b-b37e-625b19fe6925", "metadata": {}, "outputs": [], "source": ["daywise_df = (\n", "    assortment_sales_df.groupby(\n", "        [\n", "            \"city\",\n", "            \"outlet_id\",\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"be_facility_id\",\n", "            \"tat\",\n", "            \"fdate\",\n", "            \"fdow\",\n", "            \"date_\",\n", "            \"winter_flag\",\n", "            \"dow\",\n", "            \"rank\",\n", "            \"stype_\",\n", "            \"ftype_\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"is_available\": \"sum\",\n", "            \"hour_\": \"nunique\",\n", "            \"hw\": \"sum\",\n", "            \"wt_score\": \"sum\",\n", "            \"quantity\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "daywise_df[\"avail\"] = daywise_df[\"is_available\"] / daywise_df[\"hour_\"]\n", "daywise_df[\"wt_avail\"] = daywise_df[\"wt_score\"] / daywise_df[\"hw\"]\n", "\n", "daywise_df[\"wt_avail\"] = np.where(\n", "    (daywise_df[\"avail\"] > 0) & (daywise_df[\"wt_avail\"] == 0),\n", "    daywise_df[\"avail\"],\n", "    daywise_df[\"wt_avail\"],\n", ")\n", "\n", "daywise_df[\"ext_qty_li\"] = daywise_df[\"quantity\"] * (1 + 0.35 * (1 - daywise_df[\"wt_avail\"]))\n", "\n", "daywise_df[\"ext_qty_exp\"] = daywise_df[\"quantity\"] * (5 ** (0.50 * (1 - daywise_df[\"wt_avail\"])))\n", "\n", "daywise_df[\"ext_qty_para\"] = daywise_df[\"quantity\"] * (\n", "    1 + ((1 - daywise_df[\"wt_avail\"]) ** 2) / (4 * 0.08)\n", ")\n", "\n", "wow_bumps = daywise_df.copy()\n", "daywise_df = daywise_df[daywise_df[\"rank\"] <= 7]\n", "print(daywise_df.shape)\n", "daywise_df.head(1)"]}, {"cell_type": "markdown", "id": "5134bdbf-7ab9-4605-9dea-d0798351189a", "metadata": {}, "source": ["#### For Mother dairy tat = 1"]}, {"cell_type": "code", "execution_count": null, "id": "09fd90f0-38cb-416c-9ad0-3e71bc44713a", "metadata": {}, "outputs": [], "source": ["pre_md_daywise_df = (\n", "    pre_md_sales_df.groupby(\n", "        [\n", "            \"city\",\n", "            \"outlet_id\",\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"be_facility_id\",\n", "            \"tat\",\n", "            \"fdate\",\n", "            \"fdow\",\n", "            \"date_\",\n", "            \"winter_flag\",\n", "            \"dow\",\n", "            \"rank\",\n", "            \"stype_\",\n", "            \"ftype_\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"is_available\": \"sum\",\n", "            \"hour_\": \"nunique\",\n", "            \"hw\": \"sum\",\n", "            \"wt_score\": \"sum\",\n", "            \"quantity\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "pre_md_daywise_df[\"avail\"] = pre_md_daywise_df[\"is_available\"] / pre_md_daywise_df[\"hour_\"]\n", "pre_md_daywise_df[\"wt_avail\"] = pre_md_daywise_df[\"wt_score\"] / pre_md_daywise_df[\"hw\"]\n", "\n", "pre_md_daywise_df[\"wt_avail\"] = np.where(\n", "    (pre_md_daywise_df[\"avail\"] > 0) & (pre_md_daywise_df[\"wt_avail\"] == 0),\n", "    pre_md_daywise_df[\"avail\"],\n", "    pre_md_daywise_df[\"wt_avail\"],\n", ")\n", "\n", "pre_md_daywise_df[\"ext_qty_li\"] = pre_md_daywise_df[\"quantity\"] * (\n", "    1 + 0.35 * (1 - pre_md_daywise_df[\"wt_avail\"])\n", ")\n", "\n", "pre_md_daywise_df[\"ext_qty_exp\"] = pre_md_daywise_df[\"quantity\"] * (\n", "    5 ** (0.50 * (1 - pre_md_daywise_df[\"wt_avail\"]))\n", ")\n", "\n", "pre_md_daywise_df[\"ext_qty_para\"] = pre_md_daywise_df[\"quantity\"] * (\n", "    1 + ((1 - pre_md_daywise_df[\"wt_avail\"]) ** 2) / (4 * 0.08)\n", ")\n", "\n", "print(pre_md_daywise_df.shape)\n", "pre_md_daywise_df.head(1)"]}, {"cell_type": "markdown", "id": "b7169962-f485-4678-8624-df185144b155", "metadata": {}, "source": ["#### For Mother dairy tat = 2"]}, {"cell_type": "code", "execution_count": null, "id": "eae68aea-ac5a-41ce-8d7d-55367111bd14", "metadata": {}, "outputs": [], "source": ["post_md_daywise_df = (\n", "    post_md_sales_df.groupby(\n", "        [\n", "            \"city\",\n", "            \"outlet_id\",\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"be_facility_id\",\n", "            \"tat\",\n", "            \"fdate\",\n", "            \"fdow\",\n", "            \"date_\",\n", "            \"winter_flag\",\n", "            \"dow\",\n", "            \"rank\",\n", "            \"stype_\",\n", "            \"ftype_\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"is_available\": \"sum\",\n", "            \"hour_\": \"nunique\",\n", "            \"hw\": \"sum\",\n", "            \"wt_score\": \"sum\",\n", "            \"quantity\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "post_md_daywise_df[\"avail\"] = post_md_daywise_df[\"is_available\"] / post_md_daywise_df[\"hour_\"]\n", "post_md_daywise_df[\"wt_avail\"] = post_md_daywise_df[\"wt_score\"] / post_md_daywise_df[\"hw\"]\n", "\n", "post_md_daywise_df[\"wt_avail\"] = np.where(\n", "    (post_md_daywise_df[\"avail\"] > 0) & (post_md_daywise_df[\"wt_avail\"] == 0),\n", "    post_md_daywise_df[\"avail\"],\n", "    post_md_daywise_df[\"wt_avail\"],\n", ")\n", "\n", "post_md_daywise_df[\"ext_qty_li\"] = post_md_daywise_df[\"quantity\"] * (\n", "    1 + 0.35 * (1 - post_md_daywise_df[\"wt_avail\"])\n", ")\n", "\n", "post_md_daywise_df[\"ext_qty_exp\"] = post_md_daywise_df[\"quantity\"] * (\n", "    5 ** (0.50 * (1 - post_md_daywise_df[\"wt_avail\"]))\n", ")\n", "\n", "post_md_daywise_df[\"ext_qty_para\"] = post_md_daywise_df[\"quantity\"] * (\n", "    1 + ((1 - post_md_daywise_df[\"wt_avail\"]) ** 2) / (4 * 0.08)\n", ")\n", "\n", "print(post_md_daywise_df.shape)\n", "post_md_daywise_df.head(1)"]}, {"cell_type": "markdown", "id": "23c52418-76fa-4f14-841c-a38398d39e59", "metadata": {}, "source": ["## Taking post mother dairy days where avail >25%"]}, {"cell_type": "code", "execution_count": null, "id": "d4f20f7a-616e-4904-a532-d95f1a32d95a", "metadata": {}, "outputs": [], "source": ["post_ranking_df_2 = post_md_daywise_df[post_md_daywise_df[\"wt_avail\"] >= 0.25][\n", "    [\"item_id\", \"facility_id\", \"date_\", \"dow\", \"fdate\", \"fdow\"]\n", "].drop_duplicates()\n", "\n", "post_ranking_df_2[\"r1\"] = post_ranking_df_2.groupby([\"item_id\", \"facility_id\"])[\"date_\"].rank(\n", "    method=\"dense\", ascending=False\n", ")\n", "\n", "# post_ranking_df_2 = post_ranking_df_2[post_ranking_df_2[\"r1\"] < 8]\n", "\n", "# post_ranking_df_2[\"r2\"] = np.where(\n", "#     post_ranking_df_2[\"dow\"] == post_ranking_df_2[\"fdow\"],\n", "#     post_ranking_df_2[\"r1\"],\n", "#     post_ranking_df_2[\"r1\"] + 16,\n", "# )\n", "## taking l3d for polygon changed stores\n", "post_ranking_df_2 = post_ranking_df_2[post_ranking_df_2[\"r1\"] < 8]\n", "post_ranking_df_2[\"flag\"] = np.where(\n", "    (post_ranking_df_2[\"facility_id\"].isin(buffer_store_facility_list))\n", "    & (post_ranking_df_2[\"r1\"] > 3),\n", "    1,\n", "    0,\n", ")\n", "post_ranking_df_2 = post_ranking_df_2[post_ranking_df_2[\"flag\"] == 0]\n", "post_ranking_df_2[\"r2\"] = np.where(\n", "    (post_ranking_df_2[\"dow\"] == post_ranking_df_2[\"fdow\"])\n", "    & ~(post_ranking_df_2[\"facility_id\"].isin(buffer_store_facility_list)),\n", "    post_ranking_df_2[\"r1\"],\n", "    post_ranking_df_2[\"r1\"] + 15,\n", ")\n", "####\n", "\n", "post_ranking_df_2[\"rank\"] = post_ranking_df_2.groupby([\"item_id\", \"facility_id\"])[\"r2\"].rank(\n", "    method=\"dense\", ascending=True\n", ")\n", "\n", "post_ranking_df_2 = post_ranking_df_2[[\"facility_id\", \"item_id\", \"date_\", \"rank\"]].drop_duplicates()\n", "\n", "print(post_ranking_df_2.shape)\n", "post_ranking_df_2.head(7)"]}, {"cell_type": "code", "execution_count": null, "id": "5183020d-eaf4-4fef-a4b6-70fdeea0932f", "metadata": {}, "outputs": [], "source": ["post_ranking_df_2[post_ranking_df_2[\"facility_id\"] == 271]"]}, {"cell_type": "code", "execution_count": null, "id": "01722ef4-90d5-4fb1-a79b-14deae0af239", "metadata": {}, "outputs": [], "source": ["post_md_daywise_df.drop(columns={\"rank\"}, inplace=True)\n", "post_md_daywise_df = post_md_daywise_df.merge(\n", "    post_ranking_df_2, on=[\"facility_id\", \"item_id\", \"date_\"], how=\"inner\"\n", ")\n", "post_md_daywise_df.head()"]}, {"cell_type": "markdown", "id": "161a1062-91fb-49b6-af27-7999392d8bfa", "metadata": {"tags": []}, "source": ["## Extrapolated Quantity - Conservative Approach - V1"]}, {"cell_type": "markdown", "id": "075a3d83-fff9-444b-b5ce-e4531e819ef7", "metadata": {}, "source": ["#### For Filtered Assortment Base"]}, {"cell_type": "code", "execution_count": null, "id": "90b7e144-0d45-4e01-b6e2-ccf07155169e", "metadata": {}, "outputs": [], "source": ["daywise_df[\"high_ext_qty\"] = np.where(\n", "    daywise_df[\"ftype_\"] == \"high\",\n", "    np.where(\n", "        (daywise_df[\"winter_flag\"] == 1) & (daywise_df[\"stype_\"] == \"top\"),\n", "        daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].mean(axis=1),\n", "        daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "    ),\n", "    0,\n", ")\n", "\n", "daywise_df[\"medium_ext_qty\"] = np.where(\n", "    daywise_df[\"ftype_\"] == \"medium\",\n", "    np.where(\n", "        (daywise_df[\"stype_\"].isin({\"bottom\", \"premium\"})),\n", "        daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "        np.where(\n", "            daywise_df[\"wt_avail\"] >= 0.7,\n", "            np.where(\n", "                daywise_df[\"winter_flag\"] == 1,\n", "                daywise_df[[\"ext_qty_para\", \"ext_qty_exp\"]].mean(axis=1),\n", "                daywise_df[\"ext_qty_exp\"],\n", "            ),\n", "            daywise_df[[\"ext_qty_para\", \"ext_qty_exp\", \"ext_qty_li\"]].mean(axis=1),\n", "        ),\n", "    ),\n", "    0,\n", ")\n", "\n", "daywise_df[\"low_ext_qty\"] = np.where(\n", "    (daywise_df[\"ftype_\"] == \"low\"),\n", "    np.where(\n", "        (daywise_df[\"stype_\"].isin({\"bottom\", \"premium\"})),\n", "        daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "        daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].max(axis=1),\n", "    ),\n", "    0,\n", ")\n", "\n", "daywise_df[\"ext_qty_hybrid\"] = (\n", "    daywise_df[\"high_ext_qty\"] + daywise_df[\"medium_ext_qty\"] + daywise_df[\"low_ext_qty\"]\n", ")\n", "\n", "print(daywise_df.shape)\n", "daywise_df.head(1)"]}, {"cell_type": "markdown", "id": "f44aca4f-53a1-4ade-aea3-561955978633", "metadata": {}, "source": ["#### For Mother dairy tat = 1"]}, {"cell_type": "code", "execution_count": null, "id": "ae0aef4d-f5a4-43c6-8312-d3124c79e1ed", "metadata": {}, "outputs": [], "source": ["pre_md_daywise_df[\"high_ext_qty\"] = np.where(\n", "    pre_md_daywise_df[\"ftype_\"] == \"high\",\n", "    np.where(\n", "        (pre_md_daywise_df[\"winter_flag\"] == 1) & (pre_md_daywise_df[\"stype_\"] == \"top\"),\n", "        pre_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].mean(axis=1),\n", "        pre_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "    ),\n", "    0,\n", ")\n", "\n", "pre_md_daywise_df[\"medium_ext_qty\"] = np.where(\n", "    pre_md_daywise_df[\"ftype_\"] == \"medium\",\n", "    np.where(\n", "        (pre_md_daywise_df[\"stype_\"].isin({\"bottom\", \"premium\"})),\n", "        pre_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "        np.where(\n", "            pre_md_daywise_df[\"wt_avail\"] >= 0.7,\n", "            np.where(\n", "                pre_md_daywise_df[\"winter_flag\"] == 1,\n", "                pre_md_daywise_df[[\"ext_qty_para\", \"ext_qty_exp\"]].mean(axis=1),\n", "                pre_md_daywise_df[\"ext_qty_exp\"],\n", "            ),\n", "            pre_md_daywise_df[[\"ext_qty_para\", \"ext_qty_exp\", \"ext_qty_li\"]].mean(axis=1),\n", "        ),\n", "    ),\n", "    0,\n", ")\n", "\n", "pre_md_daywise_df[\"low_ext_qty\"] = np.where(\n", "    (pre_md_daywise_df[\"ftype_\"] == \"low\"),\n", "    np.where(\n", "        (pre_md_daywise_df[\"stype_\"].isin({\"bottom\", \"premium\"})),\n", "        pre_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "        pre_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].max(axis=1),\n", "    ),\n", "    0,\n", ")\n", "\n", "pre_md_daywise_df[\"ext_qty_hybrid\"] = (\n", "    pre_md_daywise_df[\"high_ext_qty\"]\n", "    + pre_md_daywise_df[\"medium_ext_qty\"]\n", "    + pre_md_daywise_df[\"low_ext_qty\"]\n", ")\n", "\n", "print(pre_md_daywise_df.shape)\n", "pre_md_daywise_df.head(1)"]}, {"cell_type": "markdown", "id": "45ac828c-a82f-408c-b4f2-f05a211bd748", "metadata": {}, "source": ["#### For Mother dairy tat = 2"]}, {"cell_type": "code", "execution_count": null, "id": "d377bac0-ed04-4c9d-baef-4efbcb0107d9", "metadata": {}, "outputs": [], "source": ["post_md_daywise_df[\"high_ext_qty\"] = np.where(\n", "    post_md_daywise_df[\"ftype_\"] == \"high\",\n", "    np.where(\n", "        (post_md_daywise_df[\"winter_flag\"] == 1) & (post_md_daywise_df[\"stype_\"] == \"top\"),\n", "        post_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].mean(axis=1),\n", "        post_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "    ),\n", "    0,\n", ")\n", "\n", "post_md_daywise_df[\"medium_ext_qty\"] = np.where(\n", "    post_md_daywise_df[\"ftype_\"] == \"medium\",\n", "    np.where(\n", "        (post_md_daywise_df[\"stype_\"].isin({\"bottom\", \"premium\"})),\n", "        post_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "        np.where(\n", "            post_md_daywise_df[\"wt_avail\"] >= 0.7,\n", "            np.where(\n", "                post_md_daywise_df[\"winter_flag\"] == 1,\n", "                post_md_daywise_df[[\"ext_qty_para\", \"ext_qty_exp\"]].mean(axis=1),\n", "                post_md_daywise_df[\"ext_qty_exp\"],\n", "            ),\n", "            post_md_daywise_df[[\"ext_qty_para\", \"ext_qty_exp\", \"ext_qty_li\"]].mean(axis=1),\n", "        ),\n", "    ),\n", "    0,\n", ")\n", "\n", "post_md_daywise_df[\"low_ext_qty\"] = np.where(\n", "    (post_md_daywise_df[\"ftype_\"] == \"low\"),\n", "    np.where(\n", "        (post_md_daywise_df[\"stype_\"].isin({\"bottom\", \"premium\"})),\n", "        post_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "        post_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].max(axis=1),\n", "    ),\n", "    0,\n", ")\n", "\n", "post_md_daywise_df[\"ext_qty_hybrid\"] = (\n", "    post_md_daywise_df[\"high_ext_qty\"]\n", "    + post_md_daywise_df[\"medium_ext_qty\"]\n", "    + post_md_daywise_df[\"low_ext_qty\"]\n", ")\n", "\n", "print(post_md_daywise_df.shape)\n", "post_md_daywise_df.head(1)"]}, {"cell_type": "markdown", "id": "1b792557-6cb9-4869-9174-8fc2afa04f6c", "metadata": {"tags": []}, "source": ["## Extrapolated Quantity - Aggressive Approach - V2"]}, {"cell_type": "markdown", "id": "174f1728-fc53-4e4e-8111-285745eb8efc", "metadata": {"tags": []}, "source": ["#### For Filtered Assortment Base"]}, {"cell_type": "code", "execution_count": null, "id": "1aab48f9-1e90-4a9b-ae07-28221e65a429", "metadata": {}, "outputs": [], "source": ["daywise_df[\"high_ext_qty_v2\"] = np.where(\n", "    daywise_df[\"ftype_\"] == \"high\",\n", "    np.where(\n", "        (daywise_df[\"winter_flag\"] == 1) & (daywise_df[\"stype_\"] == \"top\"),\n", "        daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].mean(axis=1),\n", "        daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].median(axis=1),\n", "    ),\n", "    0,\n", ")\n", "\n", "daywise_df[\"medium_ext_qty_v2\"] = np.where(\n", "    daywise_df[\"ftype_\"] == \"medium\",\n", "    np.where(\n", "        daywise_df[\"stype_\"] == \"bottom\",\n", "        np.where(\n", "            daywise_df[\"winter_flag\"] == 1,\n", "            daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].mean(axis=1),\n", "            daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].median(axis=1),\n", "        ),\n", "        np.where(\n", "            daywise_df[\"stype_\"] == \"premium\",\n", "            daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "            np.where(\n", "                daywise_df[\"wt_avail\"] >= 0.7,\n", "                daywise_df[[\"ext_qty_para\", \"ext_qty_exp\"]].max(axis=1),\n", "                daywise_df[[\"ext_qty_exp\", \"ext_qty_li\", \"ext_qty_para\"]].mean(axis=1),\n", "            ),\n", "        ),\n", "    ),\n", "    0,\n", ")\n", "\n", "daywise_df[\"low_ext_qty_v2\"] = np.where(\n", "    daywise_df[\"ftype_\"] == \"low\",\n", "    np.where(\n", "        daywise_df[\"stype_\"] == \"bottom\",\n", "        daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].median(axis=1),\n", "        np.where(\n", "            daywise_df[\"stype_\"] == \"premium\",\n", "            daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "            daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].max(axis=1),\n", "        ),\n", "    ),\n", "    0,\n", ")\n", "\n", "daywise_df[\"ext_qty_hybrid_v2\"] = (\n", "    daywise_df[\"high_ext_qty_v2\"] + daywise_df[\"medium_ext_qty_v2\"] + daywise_df[\"low_ext_qty_v2\"]\n", ")\n", "\n", "print(daywise_df.shape)\n", "daywise_df.head(1)"]}, {"cell_type": "markdown", "id": "6b4b908b-01a2-48a8-a7f8-d13785b21e8d", "metadata": {}, "source": ["#### For Mother Dairy Tat = 1\n"]}, {"cell_type": "code", "execution_count": null, "id": "8358d75d-ab9c-4412-aa7f-aac27a2c4f48", "metadata": {}, "outputs": [], "source": ["pre_md_daywise_df[\"high_ext_qty_v2\"] = np.where(\n", "    pre_md_daywise_df[\"ftype_\"] == \"high\",\n", "    np.where(\n", "        (pre_md_daywise_df[\"winter_flag\"] == 1) & (pre_md_daywise_df[\"stype_\"] == \"top\"),\n", "        pre_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].mean(axis=1),\n", "        pre_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].median(axis=1),\n", "    ),\n", "    0,\n", ")\n", "\n", "pre_md_daywise_df[\"medium_ext_qty_v2\"] = np.where(\n", "    pre_md_daywise_df[\"ftype_\"] == \"medium\",\n", "    np.where(\n", "        pre_md_daywise_df[\"stype_\"] == \"bottom\",\n", "        np.where(\n", "            pre_md_daywise_df[\"winter_flag\"] == 1,\n", "            pre_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].mean(axis=1),\n", "            pre_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].median(axis=1),\n", "        ),\n", "        np.where(\n", "            pre_md_daywise_df[\"stype_\"] == \"premium\",\n", "            pre_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "            np.where(\n", "                pre_md_daywise_df[\"wt_avail\"] >= 0.7,\n", "                pre_md_daywise_df[[\"ext_qty_para\", \"ext_qty_exp\"]].max(axis=1),\n", "                pre_md_daywise_df[[\"ext_qty_exp\", \"ext_qty_li\", \"ext_qty_para\"]].mean(axis=1),\n", "            ),\n", "        ),\n", "    ),\n", "    0,\n", ")\n", "\n", "pre_md_daywise_df[\"low_ext_qty_v2\"] = np.where(\n", "    pre_md_daywise_df[\"ftype_\"] == \"low\",\n", "    np.where(\n", "        pre_md_daywise_df[\"stype_\"] == \"bottom\",\n", "        pre_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].median(axis=1),\n", "        np.where(\n", "            pre_md_daywise_df[\"stype_\"] == \"premium\",\n", "            pre_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "            pre_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].max(axis=1),\n", "        ),\n", "    ),\n", "    0,\n", ")\n", "\n", "pre_md_daywise_df[\"ext_qty_hybrid_v2\"] = (\n", "    pre_md_daywise_df[\"high_ext_qty_v2\"]\n", "    + pre_md_daywise_df[\"medium_ext_qty_v2\"]\n", "    + pre_md_daywise_df[\"low_ext_qty_v2\"]\n", ")\n", "\n", "print(pre_md_daywise_df.shape)\n", "pre_md_daywise_df.head(1)"]}, {"cell_type": "markdown", "id": "fbb7f7df-9dbb-4b5c-9dae-f79f890ab2db", "metadata": {}, "source": ["#### For Mother Dairy Tat = 2\n"]}, {"cell_type": "code", "execution_count": null, "id": "9083392d-0164-4126-971c-3d7f92427d83", "metadata": {}, "outputs": [], "source": ["post_md_daywise_df[\"high_ext_qty_v2\"] = np.where(\n", "    post_md_daywise_df[\"ftype_\"] == \"high\",\n", "    np.where(\n", "        (post_md_daywise_df[\"winter_flag\"] == 1) & (post_md_daywise_df[\"stype_\"] == \"top\"),\n", "        np.where(\n", "            post_md_daywise_df[\"wt_avail\"] < 0.25,\n", "            post_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].max(axis=1),\n", "            post_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].mean(axis=1),\n", "        ),\n", "        np.where(\n", "            post_md_daywise_df[\"wt_avail\"] < 0.25,\n", "            post_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].max(axis=1),\n", "            post_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].median(axis=1),\n", "        ),\n", "    ),\n", "    0,\n", ")\n", "\n", "post_md_daywise_df[\"medium_ext_qty_v2\"] = np.where(\n", "    post_md_daywise_df[\"ftype_\"] == \"medium\",\n", "    np.where(\n", "        post_md_daywise_df[\"stype_\"] == \"bottom\",\n", "        np.where(\n", "            post_md_daywise_df[\"winter_flag\"] == 1,\n", "            np.where(\n", "                post_md_daywise_df[\"wt_avail\"] < 0.25,\n", "                post_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].max(axis=1),\n", "                post_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].mean(axis=1),\n", "            ),\n", "            np.where(\n", "                post_md_daywise_df[\"wt_avail\"] < 0.25,\n", "                post_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].max(axis=1),\n", "                post_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].median(axis=1),\n", "            ),\n", "        ),\n", "        np.where(\n", "            post_md_daywise_df[\"stype_\"] == \"premium\",\n", "            post_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "            np.where(\n", "                post_md_daywise_df[\"wt_avail\"] >= 0.7,\n", "                post_md_daywise_df[[\"ext_qty_para\", \"ext_qty_exp\"]].max(axis=1),\n", "                post_md_daywise_df[[\"ext_qty_exp\", \"ext_qty_li\", \"ext_qty_para\"]].mean(axis=1),\n", "            ),\n", "        ),\n", "    ),\n", "    0,\n", ")\n", "\n", "post_md_daywise_df[\"low_ext_qty_v2\"] = np.where(\n", "    post_md_daywise_df[\"ftype_\"] == \"low\",\n", "    np.where(\n", "        post_md_daywise_df[\"stype_\"] == \"bottom\",\n", "        np.where(\n", "            post_md_daywise_df[\"wt_avail\"] < 0.25,\n", "            post_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].max(axis=1),\n", "            post_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].median(axis=1),\n", "        ),\n", "        np.where(\n", "            post_md_daywise_df[\"stype_\"] == \"premium\",\n", "            post_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "            post_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].max(axis=1),\n", "        ),\n", "    ),\n", "    0,\n", ")\n", "\n", "post_md_daywise_df[\"ext_qty_hybrid_v2\"] = (\n", "    post_md_daywise_df[\"high_ext_qty_v2\"]\n", "    + post_md_daywise_df[\"medium_ext_qty_v2\"]\n", "    + post_md_daywise_df[\"low_ext_qty_v2\"]\n", ")\n", "\n", "print(post_md_daywise_df.shape)\n", "post_md_daywise_df.head(1)"]}, {"cell_type": "markdown", "id": "0c3e097a-d486-4e1f-b0c1-e0da6d9cc2de", "metadata": {"tags": []}, "source": ["## Extrapolated Quantity - Hybrid Approach - V3"]}, {"cell_type": "markdown", "id": "4b9aed62-841e-4738-a729-37d9edca6bdd", "metadata": {}, "source": ["#### For Filtered Assortment Base"]}, {"cell_type": "code", "execution_count": null, "id": "94b47a62-9347-429d-99ca-9d74d1cc1fe4", "metadata": {}, "outputs": [], "source": ["daywise_df[\"high_ext_qty_v3\"] = np.where(\n", "    daywise_df[\"ftype_\"] == \"high\",\n", "    np.where(\n", "        (daywise_df[\"winter_flag\"] == 1) & (daywise_df[\"stype_\"] == \"top\"),\n", "        daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].mean(axis=1),\n", "        daywise_df[[\"ext_qty_li\", \"ext_qty_exp\"]].mean(axis=1),\n", "    ),\n", "    0,\n", ")\n", "\n", "daywise_df[\"medium_ext_qty_v3\"] = np.where(\n", "    daywise_df[\"ftype_\"] == \"medium\",\n", "    np.where(\n", "        daywise_df[\"stype_\"] == \"bottom\",\n", "        np.where(\n", "            daywise_df[\"winter_flag\"] == 1,\n", "            daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].mean(axis=1),\n", "            daywise_df[[\"ext_qty_li\", \"ext_qty_exp\"]].mean(axis=1),\n", "        ),\n", "        np.where(\n", "            daywise_df[\"stype_\"] == \"premium\",\n", "            daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "            np.where(\n", "                daywise_df[\"wt_avail\"] >= 0.7,\n", "                daywise_df[[\"ext_qty_para\", \"ext_qty_exp\"]].max(axis=1),\n", "                daywise_df[[\"ext_qty_exp\", \"ext_qty_li\", \"ext_qty_para\"]].mean(axis=1),\n", "            ),\n", "        ),\n", "    ),\n", "    0,\n", ")\n", "\n", "daywise_df[\"low_ext_qty_v3\"] = np.where(\n", "    daywise_df[\"ftype_\"] == \"low\",\n", "    np.where(\n", "        daywise_df[\"stype_\"] == \"bottom\",\n", "        daywise_df[[\"ext_qty_li\", \"ext_qty_exp\"]].mean(axis=1),\n", "        np.where(\n", "            daywise_df[\"stype_\"] == \"premium\",\n", "            daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "            daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].max(axis=1),\n", "        ),\n", "    ),\n", "    0,\n", ")\n", "\n", "daywise_df[\"ext_qty_hybrid_v3\"] = (\n", "    daywise_df[\"high_ext_qty_v3\"] + daywise_df[\"medium_ext_qty_v3\"] + daywise_df[\"low_ext_qty_v3\"]\n", ")\n", "\n", "print(daywise_df.shape)\n", "daywise_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "6937057b-7988-47d6-928e-bc901be29a81", "metadata": {}, "outputs": [], "source": ["daywise_df[(daywise_df.facility_id == 681) & (daywise_df.item_id == 10005493)]"]}, {"cell_type": "markdown", "id": "8c381db2-bee7-4b23-bfee-77ff75ec1936", "metadata": {}, "source": ["#### For Mother Dairy Tat = 1"]}, {"cell_type": "code", "execution_count": null, "id": "29356ee1-1639-46c6-8ccf-98b06f127f58", "metadata": {}, "outputs": [], "source": ["pre_md_daywise_df[\"high_ext_qty_v3\"] = np.where(\n", "    pre_md_daywise_df[\"ftype_\"] == \"high\",\n", "    np.where(\n", "        (pre_md_daywise_df[\"winter_flag\"] == 1) & (pre_md_daywise_df[\"stype_\"] == \"top\"),\n", "        pre_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].mean(axis=1),\n", "        pre_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\"]].mean(axis=1),\n", "    ),\n", "    0,\n", ")\n", "\n", "pre_md_daywise_df[\"medium_ext_qty_v3\"] = np.where(\n", "    pre_md_daywise_df[\"ftype_\"] == \"medium\",\n", "    np.where(\n", "        pre_md_daywise_df[\"stype_\"] == \"bottom\",\n", "        np.where(\n", "            pre_md_daywise_df[\"winter_flag\"] == 1,\n", "            pre_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].mean(axis=1),\n", "            pre_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\"]].mean(axis=1),\n", "        ),\n", "        np.where(\n", "            pre_md_daywise_df[\"stype_\"] == \"premium\",\n", "            pre_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "            np.where(\n", "                pre_md_daywise_df[\"wt_avail\"] >= 0.7,\n", "                pre_md_daywise_df[[\"ext_qty_para\", \"ext_qty_exp\"]].max(axis=1),\n", "                pre_md_daywise_df[[\"ext_qty_exp\", \"ext_qty_li\", \"ext_qty_para\"]].mean(axis=1),\n", "            ),\n", "        ),\n", "    ),\n", "    0,\n", ")\n", "\n", "pre_md_daywise_df[\"low_ext_qty_v3\"] = np.where(\n", "    pre_md_daywise_df[\"ftype_\"] == \"low\",\n", "    np.where(\n", "        pre_md_daywise_df[\"stype_\"] == \"bottom\",\n", "        pre_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\"]].mean(axis=1),\n", "        np.where(\n", "            pre_md_daywise_df[\"stype_\"] == \"premium\",\n", "            pre_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "            pre_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].max(axis=1),\n", "        ),\n", "    ),\n", "    0,\n", ")\n", "\n", "pre_md_daywise_df[\"ext_qty_hybrid_v3\"] = (\n", "    pre_md_daywise_df[\"high_ext_qty_v3\"]\n", "    + pre_md_daywise_df[\"medium_ext_qty_v3\"]\n", "    + pre_md_daywise_df[\"low_ext_qty_v3\"]\n", ")\n", "\n", "print(pre_md_daywise_df.shape)\n", "pre_md_daywise_df.head(1)"]}, {"cell_type": "markdown", "id": "6819bfe1-0d49-4de9-aeb2-38bf552d0564", "metadata": {}, "source": ["#### For Mother Dairy Tat = 2"]}, {"cell_type": "code", "execution_count": null, "id": "0593eafa-a73f-4f33-9ea8-0110717eebf2", "metadata": {}, "outputs": [], "source": ["post_md_daywise_df[\"high_ext_qty_v3\"] = np.where(\n", "    post_md_daywise_df[\"ftype_\"] == \"high\",\n", "    np.where(\n", "        (post_md_daywise_df[\"winter_flag\"] == 1) & (post_md_daywise_df[\"stype_\"] == \"top\"),\n", "        post_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].mean(axis=1),\n", "        post_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\"]].mean(axis=1),\n", "    ),\n", "    0,\n", ")\n", "\n", "post_md_daywise_df[\"medium_ext_qty_v3\"] = np.where(\n", "    post_md_daywise_df[\"ftype_\"] == \"medium\",\n", "    np.where(\n", "        post_md_daywise_df[\"stype_\"] == \"bottom\",\n", "        np.where(\n", "            post_md_daywise_df[\"winter_flag\"] == 1,\n", "            post_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].mean(axis=1),\n", "            post_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\"]].mean(axis=1),\n", "        ),\n", "        np.where(\n", "            post_md_daywise_df[\"stype_\"] == \"premium\",\n", "            post_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "            np.where(\n", "                post_md_daywise_df[\"wt_avail\"] >= 0.7,\n", "                post_md_daywise_df[[\"ext_qty_para\", \"ext_qty_exp\"]].max(axis=1),\n", "                post_md_daywise_df[[\"ext_qty_exp\", \"ext_qty_li\", \"ext_qty_para\"]].mean(axis=1),\n", "            ),\n", "        ),\n", "    ),\n", "    0,\n", ")\n", "\n", "post_md_daywise_df[\"low_ext_qty_v3\"] = np.where(\n", "    post_md_daywise_df[\"ftype_\"] == \"low\",\n", "    np.where(\n", "        post_md_daywise_df[\"stype_\"] == \"bottom\",\n", "        post_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\"]].mean(axis=1),\n", "        np.where(\n", "            post_md_daywise_df[\"stype_\"] == \"premium\",\n", "            post_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].min(axis=1),\n", "            post_md_daywise_df[[\"ext_qty_li\", \"ext_qty_exp\", \"ext_qty_para\"]].max(axis=1),\n", "        ),\n", "    ),\n", "    0,\n", ")\n", "\n", "post_md_daywise_df[\"ext_qty_hybrid_v3\"] = (\n", "    post_md_daywise_df[\"high_ext_qty_v3\"]\n", "    + post_md_daywise_df[\"medium_ext_qty_v3\"]\n", "    + post_md_daywise_df[\"low_ext_qty_v3\"]\n", ")\n", "\n", "print(post_md_daywise_df.shape)\n", "post_md_daywise_df.head(1)"]}, {"cell_type": "markdown", "id": "0ef07bb2-aa02-4104-99ab-9c3c565eb527", "metadata": {}, "source": ["## Daywise Weights Calculations"]}, {"cell_type": "markdown", "id": "3c5bfb98-bb2c-427c-8b0b-6f00a0a419ed", "metadata": {}, "source": ["#### High and Medium OPD Stores (For Filtered Assortment Base)"]}, {"cell_type": "code", "execution_count": null, "id": "f78601fc-bf3f-438c-92d3-8d2ce6e5917f", "metadata": {}, "outputs": [], "source": ["date_weights_df_1 = daywise_df[daywise_df[\"ftype_\"].isin({\"high\", \"medium\"})][\n", "    [\"item_id\", \"facility_id\", \"date_\", \"rank\"]\n", "]\n", "\n", "date_weights_df_1[\"wr\"] = date_weights_df_1.groupby([\"item_id\", \"facility_id\"])[\"rank\"].rank(\n", "    method=\"dense\", ascending=True\n", ")\n", "\n", "wr_agg = (\n", "    date_weights_df_1.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"total_rank\"})\n", ")\n", "\n", "wr_max = (\n", "    date_weights_df_1.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"max\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"max_rank\"})\n", ")\n", "\n", "date_weights_df_1 = date_weights_df_1.merge(wr_agg, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "date_weights_df_1 = date_weights_df_1.merge(wr_max, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "date_weights_df_1[\"weights\"] = (\n", "    date_weights_df_1[\"max_rank\"] - date_weights_df_1[\"wr\"] + 1\n", ") / date_weights_df_1[\"total_rank\"]\n", "\n", "date_weights_df_1 = date_weights_df_1.drop(columns={\"max_rank\", \"wr\", \"total_rank\", \"rank\"})\n", "\n", "date_weights_df_1.head(1)"]}, {"cell_type": "markdown", "id": "052d925b-ce83-4b2e-a656-b2c2a7e5451b", "metadata": {}, "source": ["#### High and Medium OPD Stores (For Mother Dairy tat = 1)"]}, {"cell_type": "code", "execution_count": null, "id": "f7cf8996-f568-4bf3-b631-6bd25c96b92a", "metadata": {}, "outputs": [], "source": ["pre_md_date_weights_df_1 = pre_md_daywise_df[pre_md_daywise_df[\"ftype_\"].isin({\"high\", \"medium\"})][\n", "    [\"item_id\", \"facility_id\", \"date_\", \"rank\"]\n", "]\n", "\n", "pre_md_date_weights_df_1[\"wr\"] = pre_md_date_weights_df_1.groupby([\"item_id\", \"facility_id\"])[\n", "    \"rank\"\n", "].rank(method=\"dense\", ascending=True)\n", "\n", "pre_wr_agg = (\n", "    pre_md_date_weights_df_1.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"total_rank\"})\n", ")\n", "\n", "pre_wr_max = (\n", "    pre_md_date_weights_df_1.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"max\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"max_rank\"})\n", ")\n", "\n", "pre_md_date_weights_df_1 = pre_md_date_weights_df_1.merge(\n", "    pre_wr_agg, on=[\"item_id\", \"facility_id\"], how=\"left\"\n", ")\n", "\n", "pre_md_date_weights_df_1 = pre_md_date_weights_df_1.merge(\n", "    pre_wr_max, on=[\"item_id\", \"facility_id\"], how=\"left\"\n", ")\n", "\n", "pre_md_date_weights_df_1[\"weights\"] = (\n", "    pre_md_date_weights_df_1[\"max_rank\"] - pre_md_date_weights_df_1[\"wr\"] + 1\n", ") / pre_md_date_weights_df_1[\"total_rank\"]\n", "\n", "pre_md_date_weights_df_1 = pre_md_date_weights_df_1.drop(\n", "    columns={\"max_rank\", \"wr\", \"total_rank\", \"rank\"}\n", ")\n", "\n", "pre_md_date_weights_df_1.head(1)"]}, {"cell_type": "markdown", "id": "4d34a588-2f8c-4e70-9b91-a98e3ba9f460", "metadata": {}, "source": ["#### High and Medium OPD Stores (For Mother Dairy tat = 2)"]}, {"cell_type": "code", "execution_count": null, "id": "76b504e3-69af-41d2-99d2-6e8501f00c0c", "metadata": {}, "outputs": [], "source": ["post_md_date_weights_df_1 = post_md_daywise_df[\n", "    post_md_daywise_df[\"ftype_\"].isin({\"high\", \"medium\"})\n", "][[\"item_id\", \"facility_id\", \"date_\", \"rank\"]]\n", "\n", "post_md_date_weights_df_1[\"wr\"] = post_md_date_weights_df_1.groupby([\"item_id\", \"facility_id\"])[\n", "    \"rank\"\n", "].rank(method=\"dense\", ascending=True)\n", "\n", "post_wr_agg = (\n", "    post_md_date_weights_df_1.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"total_rank\"})\n", ")\n", "\n", "post_wr_max = (\n", "    post_md_date_weights_df_1.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"max\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"max_rank\"})\n", ")\n", "\n", "post_md_date_weights_df_1 = post_md_date_weights_df_1.merge(\n", "    post_wr_agg, on=[\"item_id\", \"facility_id\"], how=\"left\"\n", ")\n", "\n", "post_md_date_weights_df_1 = post_md_date_weights_df_1.merge(\n", "    post_wr_max, on=[\"item_id\", \"facility_id\"], how=\"left\"\n", ")\n", "\n", "post_md_date_weights_df_1[\"weights\"] = (\n", "    post_md_date_weights_df_1[\"max_rank\"] - post_md_date_weights_df_1[\"wr\"] + 1\n", ") / post_md_date_weights_df_1[\"total_rank\"]\n", "\n", "post_md_date_weights_df_1 = post_md_date_weights_df_1.drop(\n", "    columns={\"max_rank\", \"wr\", \"total_rank\", \"rank\"}\n", ")\n", "\n", "post_md_date_weights_df_1.head(1)"]}, {"cell_type": "markdown", "id": "44df2762-7b28-4af2-ae12-4d702bc24c91", "metadata": {"tags": []}, "source": ["#### Low OPD Stores (For Filtered Assortment Base)"]}, {"cell_type": "code", "execution_count": null, "id": "43e528d3-d053-48cf-b3d1-1294e6404cea", "metadata": {}, "outputs": [], "source": ["date_weights_df_2 = daywise_df[daywise_df[\"ftype_\"] == \"low\"][\n", "    [\"item_id\", \"facility_id\", \"date_\", \"ext_qty_hybrid\"]\n", "]\n", "\n", "date_weights_df_2[\"wr\"] = date_weights_df_2.groupby([\"item_id\", \"facility_id\"])[\n", "    \"ext_qty_hybrid\"\n", "].rank(method=\"dense\", ascending=True)\n", "\n", "wr_agg = (\n", "    date_weights_df_2.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"total_rank\"})\n", ")\n", "\n", "wr_max = (\n", "    date_weights_df_2.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"max\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"max_rank\"})\n", ")\n", "\n", "date_weights_df_2 = date_weights_df_2.merge(wr_agg, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "date_weights_df_2 = date_weights_df_2.merge(wr_max, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "date_weights_df_2[\"weights\"] = date_weights_df_2[\"wr\"] / date_weights_df_2[\"total_rank\"]\n", "\n", "date_weights_df_2 = date_weights_df_2.drop(\n", "    columns={\"max_rank\", \"wr\", \"total_rank\", \"ext_qty_hybrid\"}\n", ")\n", "\n", "date_weights_df_2.head(2)"]}, {"cell_type": "markdown", "id": "a0a9ca59-0bbf-4f8f-b594-be199933a306", "metadata": {}, "source": ["#### Low OPD Stores (For Mother Dairy tat =1)"]}, {"cell_type": "code", "execution_count": null, "id": "1ce5f2ca-e19a-42ba-b40a-a54217bf190e", "metadata": {}, "outputs": [], "source": ["pre_md_date_weights_df_2 = pre_md_daywise_df[pre_md_daywise_df[\"ftype_\"] == \"low\"][\n", "    [\"item_id\", \"facility_id\", \"date_\", \"ext_qty_hybrid\"]\n", "]\n", "\n", "pre_md_date_weights_df_2[\"wr\"] = pre_md_date_weights_df_2.groupby([\"item_id\", \"facility_id\"])[\n", "    \"ext_qty_hybrid\"\n", "].rank(method=\"dense\", ascending=True)\n", "\n", "wr_agg = (\n", "    pre_md_date_weights_df_2.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"total_rank\"})\n", ")\n", "\n", "wr_max = (\n", "    pre_md_date_weights_df_2.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"max\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"max_rank\"})\n", ")\n", "\n", "pre_md_date_weights_df_2 = pre_md_date_weights_df_2.merge(\n", "    wr_agg, on=[\"item_id\", \"facility_id\"], how=\"left\"\n", ")\n", "\n", "pre_md_date_weights_df_2 = pre_md_date_weights_df_2.merge(\n", "    wr_max, on=[\"item_id\", \"facility_id\"], how=\"left\"\n", ")\n", "\n", "pre_md_date_weights_df_2[\"weights\"] = (\n", "    pre_md_date_weights_df_2[\"wr\"] / pre_md_date_weights_df_2[\"total_rank\"]\n", ")\n", "\n", "pre_md_date_weights_df_2 = pre_md_date_weights_df_2.drop(\n", "    columns={\"max_rank\", \"wr\", \"total_rank\", \"ext_qty_hybrid\"}\n", ")\n", "\n", "pre_md_date_weights_df_2.head(1)"]}, {"cell_type": "markdown", "id": "31beeaab-1ad8-49de-9c1d-fb5aec0cc23a", "metadata": {}, "source": ["#### Low OPD Stores (For Mother Dairy tat =2)"]}, {"cell_type": "code", "execution_count": null, "id": "58894694-aa21-4971-9765-460bc76dd5ce", "metadata": {}, "outputs": [], "source": ["post_md_date_weights_df_2 = post_md_daywise_df[post_md_daywise_df[\"ftype_\"] == \"low\"][\n", "    [\"item_id\", \"facility_id\", \"date_\", \"ext_qty_hybrid\"]\n", "]\n", "\n", "post_md_date_weights_df_2[\"wr\"] = post_md_date_weights_df_2.groupby([\"item_id\", \"facility_id\"])[\n", "    \"ext_qty_hybrid\"\n", "].rank(method=\"dense\", ascending=True)\n", "\n", "wr_agg = (\n", "    post_md_date_weights_df_2.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"total_rank\"})\n", ")\n", "\n", "wr_max = (\n", "    post_md_date_weights_df_2.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"wr\": \"max\"})\n", "    .reset_index()\n", "    .rename(columns={\"wr\": \"max_rank\"})\n", ")\n", "\n", "post_md_date_weights_df_2 = post_md_date_weights_df_2.merge(\n", "    wr_agg, on=[\"item_id\", \"facility_id\"], how=\"left\"\n", ")\n", "\n", "post_md_date_weights_df_2 = post_md_date_weights_df_2.merge(\n", "    wr_max, on=[\"item_id\", \"facility_id\"], how=\"left\"\n", ")\n", "\n", "post_md_date_weights_df_2[\"weights\"] = (\n", "    post_md_date_weights_df_2[\"wr\"] / post_md_date_weights_df_2[\"total_rank\"]\n", ")\n", "\n", "post_md_date_weights_df_2 = post_md_date_weights_df_2.drop(\n", "    columns={\"max_rank\", \"wr\", \"total_rank\", \"ext_qty_hybrid\"}\n", ")\n", "\n", "post_md_date_weights_df_2.head(1)"]}, {"cell_type": "markdown", "id": "506b354c-360d-462c-bb4a-0c080ebac947", "metadata": {}, "source": ["## Total weights for High, Medium and Low OPD stores"]}, {"cell_type": "markdown", "id": "e7459f98-d3b7-4128-bd92-62b8c0019c75", "metadata": {}, "source": ["#### For filtered Assortment Base"]}, {"cell_type": "code", "execution_count": null, "id": "8ad9f910-838f-4823-bc17-c6f30aee7db1", "metadata": {}, "outputs": [], "source": ["date_weights_df = pd.concat([date_weights_df_1, date_weights_df_2])\n", "print(date_weights_df.shape)\n", "date_weights_df.head(1)"]}, {"cell_type": "markdown", "id": "9a63c693-d620-499e-836e-d50acb0a3160", "metadata": {}, "source": ["#### For Mother Dairy Tat = 1"]}, {"cell_type": "code", "execution_count": null, "id": "22beddc4-994d-4c2d-8f4f-7b5d8b7e8567", "metadata": {}, "outputs": [], "source": ["pre_date_weights_df = pd.concat([pre_md_date_weights_df_1, pre_md_date_weights_df_2])\n", "print(pre_date_weights_df.shape)\n", "pre_date_weights_df.head(1)"]}, {"cell_type": "markdown", "id": "41e56de6-7dce-4bea-a219-b8be6afe8119", "metadata": {}, "source": ["#### For Mother Dairy Tat = 2"]}, {"cell_type": "code", "execution_count": null, "id": "b26e1c7e-2557-49a2-a2e6-48f162894650", "metadata": {}, "outputs": [], "source": ["post_date_weights_df = pd.concat([post_md_date_weights_df_1, post_md_date_weights_df_2])\n", "print(post_date_weights_df.shape)\n", "post_date_weights_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "8c30bee5-dea0-433e-af1b-25131b1a92fc", "metadata": {}, "outputs": [], "source": ["del [\n", "    date_weights_df_1,\n", "    date_weights_df_2,\n", "    wr_agg,\n", "    wr_max,\n", "    post_md_date_weights_df_1,\n", "    post_md_date_weights_df_2,\n", "    pre_md_date_weights_df_1,\n", "    pre_md_date_weights_df_2,\n", "    pre_wr_agg,\n", "    post_wr_agg,\n", "    pre_wr_max,\n", "    post_wr_max,\n", "]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "efcdc58c-0dce-40c3-8d61-e0949e0f38de", "metadata": {}, "source": ["## Weights Merge to Daywise DataFrame"]}, {"cell_type": "markdown", "id": "a06f716a-b1ca-45bf-8e21-f6434c2bcc23", "metadata": {}, "source": ["### For Filtered Assortment Base"]}, {"cell_type": "code", "execution_count": null, "id": "05af2033-3692-4617-b40c-6c65b6ce77f7", "metadata": {}, "outputs": [], "source": ["daywise_df = daywise_df.merge(date_weights_df, on=[\"item_id\", \"facility_id\", \"date_\"], how=\"left\")\n", "\n", "daywise_df[\"ext_cpd_hybrid\"] = daywise_df[\"ext_qty_hybrid\"] * daywise_df[\"weights\"]\n", "\n", "daywise_df[\"ext_cpd_hybrid_v2\"] = daywise_df[\"ext_qty_hybrid_v2\"] * daywise_df[\"weights\"]\n", "\n", "daywise_df[\"ext_cpd_hybrid_v3\"] = daywise_df[\"ext_qty_hybrid_v3\"] * daywise_df[\"weights\"]\n", "\n", "print(daywise_df.shape)\n", "daywise_df.head(1)"]}, {"cell_type": "markdown", "id": "c29cfed9-fb91-489f-8fd7-7e3945bd57fe", "metadata": {}, "source": ["### For Mother Dairy Tat = 1"]}, {"cell_type": "code", "execution_count": null, "id": "cbb8cabf-65aa-48f9-881d-0d5c1c518e42", "metadata": {}, "outputs": [], "source": ["pre_md_daywise_df = pre_md_daywise_df.merge(\n", "    pre_date_weights_df, on=[\"item_id\", \"facility_id\", \"date_\"], how=\"left\"\n", ")\n", "\n", "pre_md_daywise_df[\"ext_cpd_hybrid\"] = (\n", "    pre_md_daywise_df[\"ext_qty_hybrid\"] * pre_md_daywise_df[\"weights\"]\n", ")\n", "\n", "pre_md_daywise_df[\"ext_cpd_hybrid_v2\"] = (\n", "    pre_md_daywise_df[\"ext_qty_hybrid_v2\"] * pre_md_daywise_df[\"weights\"]\n", ")\n", "\n", "pre_md_daywise_df[\"ext_cpd_hybrid_v3\"] = (\n", "    pre_md_daywise_df[\"ext_qty_hybrid_v3\"] * pre_md_daywise_df[\"weights\"]\n", ")\n", "\n", "print(pre_md_daywise_df.shape)\n", "pre_md_daywise_df.head(1)"]}, {"cell_type": "markdown", "id": "96ab699c-a64e-431f-8211-ea727f144a51", "metadata": {}, "source": ["### For Mother Dairy <PERSON>t = 2"]}, {"cell_type": "code", "execution_count": null, "id": "1bf9f0c4-f171-4c2b-bcee-07fae264b69e", "metadata": {}, "outputs": [], "source": ["post_md_daywise_df = post_md_daywise_df.merge(\n", "    post_date_weights_df, on=[\"item_id\", \"facility_id\", \"date_\"], how=\"left\"\n", ")\n", "\n", "post_md_daywise_df[\"ext_cpd_hybrid\"] = (\n", "    post_md_daywise_df[\"ext_qty_hybrid\"] * post_md_daywise_df[\"weights\"]\n", ")\n", "\n", "post_md_daywise_df[\"ext_cpd_hybrid_v2\"] = (\n", "    post_md_daywise_df[\"ext_qty_hybrid_v2\"] * post_md_daywise_df[\"weights\"]\n", ")\n", "\n", "post_md_daywise_df[\"ext_cpd_hybrid_v3\"] = (\n", "    post_md_daywise_df[\"ext_qty_hybrid_v3\"] * post_md_daywise_df[\"weights\"]\n", ")\n", "\n", "print(post_md_daywise_df.shape)\n", "post_md_daywise_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "53933e8d-c5d2-47c2-b59b-653db1d2511f", "metadata": {}, "outputs": [], "source": ["del [date_weights_df, pre_date_weights_df, post_date_weights_df]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "6719465f-2f25-47a5-8bcf-3aec866294e5", "metadata": {}, "source": ["## Extrapolated CPD DataFrame"]}, {"cell_type": "markdown", "id": "db487406-1ce7-4772-8fc0-690f60430bcf", "metadata": {}, "source": ["### For Filtered Assortment Base"]}, {"cell_type": "code", "execution_count": null, "id": "1ccc2d35-ef6f-48b9-a4ca-7935d97fc2b6", "metadata": {}, "outputs": [], "source": ["pre_cpd_df = daywise_df.copy()\n", "\n", "# pre_cpd_df[\"date_dow\"] = np.where(\n", "#     pre_cpd_df[\"dow\"].isin({0, 1, 2, 3, 4}),\n", "#     \"weekday\",\n", "#     np.where(pre_cpd_df[\"dow\"] == 5, \"saturday\", \"sunday\"),\n", "# )\n", "\n", "# pre_cpd_df[\"forecast_dow\"] = np.where(\n", "#     pre_cpd_df[\"fdow\"].isin({0, 1, 2, 3, 4}),\n", "#     \"weekday\",\n", "#     np.where(pre_cpd_df[\"fdow\"] == 5, \"saturday\", \"sunday\"),\n", "# )\n", "\n", "pre_cpd_df = pre_cpd_df[\n", "    [\n", "        \"city\",\n", "        \"be_facility_id\",\n", "        \"facility_id\",\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"stype_\",\n", "        \"ftype_\",\n", "        \"date_\",\n", "        \"winter_flag\",\n", "        \"dow\",\n", "        \"fdate\",\n", "        \"fdow\",\n", "        \"ext_cpd_hybrid\",\n", "        \"ext_cpd_hybrid_v2\",\n", "        \"ext_cpd_hybrid_v3\",\n", "        \"weights\",\n", "    ]\n", "]\n", "\n", "print(pre_cpd_df.shape)\n", "pre_cpd_df.head(1)"]}, {"cell_type": "markdown", "id": "1601e86e-a646-4e23-9e4b-b276a701198b", "metadata": {}, "source": ["### For Mother Dairy tat = 1"]}, {"cell_type": "code", "execution_count": null, "id": "d1a43009-33a1-40e7-804c-37bbca4600fc", "metadata": {}, "outputs": [], "source": ["pre_md_cpd_df = pre_md_daywise_df.copy()\n", "\n", "# pre_md_cpd_df[\"date_dow\"] = np.where(\n", "#     pre_md_cpd_df[\"dow\"].isin({0, 1, 2, 3, 4}),\n", "#     \"weekday\",\n", "#     np.where(pre_md_cpd_df[\"dow\"] == 5, \"saturday\", \"sunday\"),\n", "# )\n", "\n", "# pre_md_cpd_df[\"forecast_dow\"] = np.where(\n", "#     pre_md_cpd_df[\"fdow\"].isin({0, 1, 2, 3, 4}),\n", "#     \"weekday\",\n", "#     np.where(pre_md_cpd_df[\"fdow\"] == 5, \"saturday\", \"sunday\"),\n", "# )\n", "\n", "pre_md_cpd_df = pre_md_cpd_df[\n", "    [\n", "        \"city\",\n", "        \"be_facility_id\",\n", "        \"facility_id\",\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"stype_\",\n", "        \"ftype_\",\n", "        \"date_\",\n", "        \"winter_flag\",\n", "        \"dow\",\n", "        \"fdate\",\n", "        \"fdow\",\n", "        \"ext_cpd_hybrid\",\n", "        \"ext_cpd_hybrid_v2\",\n", "        \"ext_cpd_hybrid_v3\",\n", "        \"weights\",\n", "    ]\n", "]\n", "\n", "print(pre_md_cpd_df.shape)\n", "pre_md_cpd_df.head(1)"]}, {"cell_type": "markdown", "id": "32b5809f-11b5-42d7-b03f-37d76e3e6c26", "metadata": {}, "source": ["### For Mother Dairy <PERSON>t = 2"]}, {"cell_type": "code", "execution_count": null, "id": "7cd253df-3a28-449f-9309-b0c323a46e4c", "metadata": {}, "outputs": [], "source": ["post_md_cpd_df = post_md_daywise_df.copy()\n", "\n", "# post_md_cpd_df[\"date_dow\"] = np.where(\n", "#     post_md_cpd_df[\"dow\"].isin({0, 1, 2, 3, 4}),\n", "#     \"weekday\",\n", "#     np.where(post_md_cpd_df[\"dow\"] == 5, \"saturday\", \"sunday\"),\n", "# )\n", "\n", "# post_md_cpd_df[\"forecast_dow\"] = np.where(\n", "#     post_md_cpd_df[\"fdow\"].isin({0, 1, 2, 3, 4}),\n", "#     \"weekday\",\n", "#     np.where(post_md_cpd_df[\"fdow\"] == 5, \"saturday\", \"sunday\"),\n", "# )\n", "\n", "post_md_cpd_df = post_md_cpd_df[\n", "    [\n", "        \"city\",\n", "        \"be_facility_id\",\n", "        \"facility_id\",\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"stype_\",\n", "        \"ftype_\",\n", "        \"date_\",\n", "        \"winter_flag\",\n", "        \"dow\",\n", "        \"fdate\",\n", "        \"fdow\",\n", "        \"ext_cpd_hybrid\",\n", "        \"ext_cpd_hybrid_v2\",\n", "        \"ext_cpd_hybrid_v3\",\n", "        \"weights\",\n", "    ]\n", "]\n", "\n", "print(post_md_cpd_df.shape)\n", "post_md_cpd_df.head(1)"]}, {"cell_type": "markdown", "id": "7e9f82c1-d4b0-4dbf-9bb2-e638560c9e80", "metadata": {"tags": []}, "source": ["## Weekday Weekend Growth - Degrowth - Actual sales 6am-6pm"]}, {"cell_type": "code", "execution_count": null, "id": "e8b1ffcd-5044-4a28-b918-d6705b141045", "metadata": {}, "outputs": [], "source": ["increment_df = (\n", "    wow_bumps.groupby([\"date_\", \"facility_id\"])\n", "    .agg({\"ext_qty_li\": \"sum\"})\n", "    .rename(columns={\"ext_qty_li\": \"quantity\"})\n", "    .reset_index()\n", ")\n", "\n", "increment_df[\"date_\"] = pd.to_datetime(increment_df[\"date_\"])\n", "increment_df[\"dow\"] = pd.to_datetime(increment_df[\"date_\"]).dt.dayofweek\n", "\n", "increment_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "c3766e3c-8d9f-41ed-9759-f0e9467c97db", "metadata": {}, "outputs": [], "source": ["increment_df.head()\n", "increment_agg_df = (\n", "    increment_df.groupby([\"facility_id\", \"dow\"]).agg({\"quantity\": percentile}).reset_index()\n", ")\n", "increment_df_fdow = increment_agg_df.copy()\n", "increment_df_fdow.rename(columns={\"dow\": \"fdow\", \"quantity\": \"fdow_quantity\"}, inplace=True)\n", "increment_df_calc = increment_agg_df.merge(increment_df_fdow, on=[\"facility_id\"], how=\"left\")\n", "# fdow_df = increment_agg_df[(increment_agg_df[\"fdow\"] == increment_agg_df[\"dow\"])][\n", "#                         [\"facility_id\", \"fdow_carts\"]\n", "#                     ].drop_duplicates().rename(columns={'carts':'fdow_carts'})\n", "# fin_increment = pd.merge(increment_df_calc,fdow_df, on=['facility_id'], how= 'left')\n", "increment_df_calc[\"bf\"] = increment_df_calc[\"fdow_quantity\"] / increment_df_calc[\"quantity\"]\n", "increment_df_calc[\"bf\"] = increment_df_calc[\"bf\"].fillna(1)\n", "increment_df_calc[\"bf\"] = increment_df_calc[\"bf\"].clip(0.6, 1.4)\n", "increment_df_calc = increment_df_calc[[\"facility_id\", \"dow\", \"fdow\", \"bf\"]]\n", "increment_df_calc[increment_df_calc[\"facility_id\"] == 271].head(20)"]}, {"cell_type": "markdown", "id": "eb3d6ec2-d6c3-4c30-ad06-700f214b2400", "metadata": {}, "source": ["## Apply Lift & Drop"]}, {"cell_type": "markdown", "id": "0b41902b-262e-4759-ac7f-c6d1feb9678d", "metadata": {}, "source": ["### For Filtered Assortment Base"]}, {"cell_type": "code", "execution_count": null, "id": "f45d04bf-c330-4df9-a933-403bce1ad886", "metadata": {}, "outputs": [], "source": ["pre_calc_df = (\n", "    pre_cpd_df.groupby(\n", "        [\n", "            \"city\",\n", "            \"be_facility_id\",\n", "            \"facility_id\",\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"stype_\",\n", "            \"ftype_\",\n", "            \"dow\",\n", "            \"fdate\",\n", "            \"fdow\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"ext_cpd_hybrid\": \"sum\",\n", "            \"ext_cpd_hybrid_v2\": \"sum\",\n", "            \"ext_cpd_hybrid_v3\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "pre_calc_df = pre_calc_df.merge(increment_df_calc, on=[\"facility_id\", \"dow\", \"fdow\"], how=\"left\")\n", "\n", "pre_calc_df[\"bf\"] = pre_calc_df[\"bf\"].fillna(1)\n", "pre_calc_df[\"bf\"] = np.where(pre_calc_df[\"bf\"] > 1.5, 1.5, pre_calc_df[\"bf\"])\n", "pre_calc_df[\"bf\"] = np.where(pre_calc_df[\"bf\"] < 0.5, 0.5, pre_calc_df[\"bf\"])\n", "\n", "pre_calc_df[\"extrapolated_cpd_hybrid\"] = pre_calc_df[\"ext_cpd_hybrid\"] * pre_calc_df[\"bf\"]\n", "\n", "pre_calc_df[\"extrapolated_cpd_hybrid_v2\"] = pre_calc_df[\"ext_cpd_hybrid_v2\"] * pre_calc_df[\"bf\"]\n", "\n", "pre_calc_df[\"extrapolated_cpd_hybrid_v3\"] = pre_calc_df[\"ext_cpd_hybrid_v3\"] * pre_calc_df[\"bf\"]\n", "\n", "pre_calc_df.head(1)"]}, {"cell_type": "markdown", "id": "a8ef452a-a316-4fb2-a762-93dcbd34fc1f", "metadata": {}, "source": ["### For Mother Dairy Tat = 1"]}, {"cell_type": "code", "execution_count": null, "id": "9b429a73-2628-437e-aee2-0e8c50a44e94", "metadata": {}, "outputs": [], "source": ["pre_md_calc_df = (\n", "    pre_md_cpd_df.groupby(\n", "        [\n", "            \"city\",\n", "            \"be_facility_id\",\n", "            \"facility_id\",\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"stype_\",\n", "            \"ftype_\",\n", "            \"dow\",\n", "            \"fdate\",\n", "            \"fdow\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"ext_cpd_hybrid\": \"sum\",\n", "            \"ext_cpd_hybrid_v2\": \"sum\",\n", "            \"ext_cpd_hybrid_v3\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "pre_md_calc_df = pre_md_calc_df.merge(\n", "    increment_df_calc, on=[\"facility_id\", \"dow\", \"fdow\"], how=\"left\"\n", ")\n", "\n", "pre_md_calc_df[\"bf\"] = pre_md_calc_df[\"bf\"].fillna(1)\n", "pre_md_calc_df[\"bf\"] = np.where(pre_md_calc_df[\"bf\"] > 1.5, 1.5, pre_md_calc_df[\"bf\"])\n", "pre_md_calc_df[\"bf\"] = np.where(pre_md_calc_df[\"bf\"] < 0.5, 0.5, pre_md_calc_df[\"bf\"])\n", "\n", "pre_md_calc_df[\"extrapolated_cpd_hybrid\"] = pre_md_calc_df[\"ext_cpd_hybrid\"] * pre_md_calc_df[\"bf\"]\n", "\n", "pre_md_calc_df[\"extrapolated_cpd_hybrid_v2\"] = (\n", "    pre_md_calc_df[\"ext_cpd_hybrid_v2\"] * pre_md_calc_df[\"bf\"]\n", ")\n", "\n", "pre_md_calc_df[\"extrapolated_cpd_hybrid_v3\"] = (\n", "    pre_md_calc_df[\"ext_cpd_hybrid_v3\"] * pre_md_calc_df[\"bf\"]\n", ")\n", "\n", "pre_md_calc_df.head(1)"]}, {"cell_type": "markdown", "id": "adc49531-2f98-42d6-95db-57a9995fea34", "metadata": {}, "source": ["### For Mother Dairy Tat = 1"]}, {"cell_type": "code", "execution_count": null, "id": "33b3c019-4721-4878-a6d0-4348986302aa", "metadata": {}, "outputs": [], "source": ["post_md_calc_df = (\n", "    post_md_cpd_df.groupby(\n", "        [\n", "            \"city\",\n", "            \"be_facility_id\",\n", "            \"facility_id\",\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"stype_\",\n", "            \"ftype_\",\n", "            \"dow\",\n", "            \"fdate\",\n", "            \"fdow\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"ext_cpd_hybrid\": \"sum\",\n", "            \"ext_cpd_hybrid_v2\": \"sum\",\n", "            \"ext_cpd_hybrid_v3\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "# post_md_calc_df = post_md_calc_df.merge(\n", "#     increment_agg_df, on=[\"facility_id\"], how=\"left\"\n", "# )\n", "post_md_calc_df = post_md_calc_df.merge(\n", "    increment_df_calc, on=[\"facility_id\", \"dow\", \"fdow\"], how=\"left\"\n", ")\n", "post_md_calc_df[\"bf\"] = post_md_calc_df[\"bf\"].fillna(1)\n", "post_md_calc_df[\"bf\"] = np.where(post_md_calc_df[\"bf\"] > 1.5, 1.5, post_md_calc_df[\"bf\"])\n", "post_md_calc_df[\"bf\"] = np.where(post_md_calc_df[\"bf\"] < 0.5, 0.5, post_md_calc_df[\"bf\"])\n", "\n", "post_md_calc_df[\"extrapolated_cpd_hybrid\"] = (\n", "    post_md_calc_df[\"ext_cpd_hybrid\"] * post_md_calc_df[\"bf\"]\n", ")\n", "\n", "post_md_calc_df[\"extrapolated_cpd_hybrid_v2\"] = (\n", "    post_md_calc_df[\"ext_cpd_hybrid_v2\"] * post_md_calc_df[\"bf\"]\n", ")\n", "\n", "post_md_calc_df[\"extrapolated_cpd_hybrid_v3\"] = (\n", "    post_md_calc_df[\"ext_cpd_hybrid_v3\"] * post_md_calc_df[\"bf\"]\n", ")\n", "\n", "post_md_calc_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "793c5df0-b52f-40e9-9dec-6b0257b00522", "metadata": {}, "outputs": [], "source": ["del [data_df, increment_agg_df, increment_df]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "65c93c9f-c8f5-4cc7-b911-b5bd30e597aa", "metadata": {}, "source": ["## Top SKU Availability Feedback for Winter Stores"]}, {"cell_type": "raw", "id": "e82d946b-3dba-424c-919a-3e14dea26c6a", "metadata": {}, "source": ["try:\n", "    feedback_input_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::city-feedback-input\",\n", "    )\n", "\n", "except:\n", "    feedback_input_df = pd.read_csv(\"city_feedback_input.csv\")\n", "\n", "feedback_input_df[\"feedback_flag\"] = np.where(feedback_input_df[\"feedback\"] == \"no\", 0, 1)\n", "\n", "feedback_input_df = feedback_input_df.drop(columns={\"feedback\"})"]}, {"cell_type": "raw", "id": "8ec6f98e-7761-434a-b8af-2d1e179fafff", "metadata": {"tags": []}, "source": ["l2_date = pd.to_datetime(today_date) - <PERSON><PERSON><PERSON>(days=2)\n", "\n", "top_sku_feedback_df = assortment_sales_df[\n", "    (assortment_sales_df[\"stype_\"] == \"top\") & (assortment_sales_df[\"hour_\"] > 19)\n", "]\n", "\n", "top_sku_feedback_df = (\n", "    top_sku_feedback_df[\n", "        (top_sku_feedback_df[\"date_\"] >= l2_date) & (top_sku_feedback_df[\"date_\"] < today_date)\n", "    ]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "top_sku_feedback_df = top_sku_feedback_df.merge(feedback_input_df, on=[\"city\"], how=\"left\")\n", "\n", "top_sku_feedback_df[\"feedback_flag\"] = top_sku_feedback_df[\"feedback_flag\"].fillna(0)\n", "\n", "top_sku_feedback_df = (\n", "    top_sku_feedback_df[top_sku_feedback_df[\"feedback_flag\"] == 1]\n", "    .groupby([\"facility_id\", \"item_id\"])\n", "    .agg({\"hour_\": \"count\", \"is_available\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "top_sku_feedback_df[\"avail\"] = top_sku_feedback_df[\"is_available\"] / top_sku_feedback_df[\"hour_\"]\n", "\n", "top_sku_feedback_df[\"flag\"] = np.where(top_sku_feedback_df[\"avail\"] < 0.25, 1.05, 1)"]}, {"cell_type": "markdown", "id": "13a33439-90c9-4362-a348-b7098186d468", "metadata": {}, "source": ["## Final Forecast DataFrame"]}, {"cell_type": "markdown", "id": "03989a24-24d1-4287-8648-2f030ee0738f", "metadata": {}, "source": ["### For Filtered Assortment Base"]}, {"cell_type": "code", "execution_count": null, "id": "2da4199c-82e6-4f50-8990-dc474ed5d2d2", "metadata": {"tags": []}, "outputs": [], "source": ["pre_forecast_df = (\n", "    pre_calc_df.groupby([\"city\", \"be_facility_id\", \"facility_id\", \"item_id\", \"stype_\", \"fdate\"])\n", "    .agg(\n", "        {\n", "            \"extrapolated_cpd_hybrid\": \"sum\",\n", "            \"extrapolated_cpd_hybrid_v2\": \"sum\",\n", "            \"extrapolated_cpd_hybrid_v3\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "pre_forecast_df = pre_forecast_df.rename(\n", "    columns={\n", "        \"extrapolated_cpd_hybrid\": \"extrapolated_cpd\",\n", "        \"extrapolated_cpd_hybrid_v2\": \"extrapolated_cpd_v2\",\n", "        \"extrapolated_cpd_hybrid_v3\": \"extrapolated_cpd_v3\",\n", "    }\n", ")\n", "\n", "# pre_forecast_df = pre_forecast_df.merge(\n", "#     top_sku_feedback_df.drop(columns={\"hour_\", \"is_available\", \"avail\"}),\n", "#     on=[\"facility_id\", \"item_id\"],\n", "#     how=\"left\",\n", "# )\n", "\n", "# pre_forecast_df[\"flag\"] = pre_forecast_df[\"flag\"].fillna(1)\n", "\n", "# pre_forecast_df[\"extrapolated_cpd\"] = np.round(\n", "#     pre_forecast_df[\"extrapolated_cpd\"] * pre_forecast_df[\"flag\"], 0\n", "# ).astype(int)\n", "\n", "# pre_forecast_df[\"extrapolated_cpd_v2\"] = np.round(\n", "#     pre_forecast_df[\"extrapolated_cpd_v2\"] * pre_forecast_df[\"flag\"], 0\n", "# ).astype(int)\n", "\n", "# pre_forecast_df[\"extrapolated_cpd_v3\"] = np.round(\n", "#     pre_forecast_df[\"extrapolated_cpd_v3\"] * pre_forecast_df[\"flag\"], 0\n", "# ).astype(int)\n", "\n", "# pre_forecast_df = pre_forecast_df.drop(columns={\"flag\"})\n", "\n", "pre_forecast_df.head(1)"]}, {"cell_type": "markdown", "id": "3981c478-7c43-403d-b64e-1e3081ffaf97", "metadata": {}, "source": ["### For Mother Dairy tat = 1"]}, {"cell_type": "code", "execution_count": null, "id": "29af4f67-9567-4fa4-b24e-446fc3035077", "metadata": {}, "outputs": [], "source": ["pre_md_forecast_df = (\n", "    pre_md_calc_df.groupby([\"city\", \"be_facility_id\", \"facility_id\", \"item_id\", \"stype_\", \"fdate\"])\n", "    .agg(\n", "        {\n", "            \"extrapolated_cpd_hybrid\": \"sum\",\n", "            \"extrapolated_cpd_hybrid_v2\": \"sum\",\n", "            \"extrapolated_cpd_hybrid_v3\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "pre_md_forecast_df = pre_md_forecast_df.rename(\n", "    columns={\n", "        \"extrapolated_cpd_hybrid\": \"pre_extrapolated_cpd\",\n", "        \"extrapolated_cpd_hybrid_v2\": \"pre_extrapolated_cpd_v2\",\n", "        \"extrapolated_cpd_hybrid_v3\": \"pre_extrapolated_cpd_v3\",\n", "    }\n", ")\n", "\n", "pre_md_forecast_df[\"pre_extrapolated_cpd\"] = np.round(\n", "    pre_md_forecast_df[\"pre_extrapolated_cpd\"], 0\n", ").astype(int)\n", "\n", "pre_md_forecast_df[\"pre_extrapolated_cpd_v2\"] = np.round(\n", "    pre_md_forecast_df[\"pre_extrapolated_cpd_v2\"], 0\n", ").astype(int)\n", "\n", "pre_md_forecast_df[\"pre_extrapolated_cpd_v3\"] = np.round(\n", "    pre_md_forecast_df[\"pre_extrapolated_cpd_v3\"], 0\n", ").astype(int)\n", "\n", "pre_md_forecast_df.head(1)"]}, {"cell_type": "markdown", "id": "4fd4400a-d101-49a6-9e9c-35df9d688d9f", "metadata": {}, "source": ["### For Mother Dairy tat = 2"]}, {"cell_type": "code", "execution_count": null, "id": "751b4d9c-a3ca-464d-8937-cc7cb92e4e8d", "metadata": {}, "outputs": [], "source": ["post_md_forecast_df = (\n", "    post_md_calc_df.groupby(\n", "        [\n", "            \"city\",\n", "            \"be_facility_id\",\n", "            \"facility_id\",\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"stype_\",\n", "            \"fdate\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"extrapolated_cpd_hybrid\": \"sum\",\n", "            \"extrapolated_cpd_hybrid_v2\": \"sum\",\n", "            \"extrapolated_cpd_hybrid_v3\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "post_md_forecast_df = post_md_forecast_df.rename(\n", "    columns={\n", "        \"extrapolated_cpd_hybrid\": \"post_extrapolated_cpd\",\n", "        \"extrapolated_cpd_hybrid_v2\": \"post_extrapolated_cpd_v2\",\n", "        \"extrapolated_cpd_hybrid_v3\": \"post_extrapolated_cpd_v3\",\n", "    }\n", ")\n", "\n", "post_md_forecast_df[\"post_extrapolated_cpd\"] = np.round(\n", "    post_md_forecast_df[\"post_extrapolated_cpd\"], 0\n", ").astype(int)\n", "\n", "post_md_forecast_df[\"post_extrapolated_cpd_v2\"] = np.round(\n", "    post_md_forecast_df[\"post_extrapolated_cpd_v2\"], 0\n", ").astype(int)\n", "\n", "post_md_forecast_df[\"post_extrapolated_cpd_v3\"] = np.round(\n", "    post_md_forecast_df[\"post_extrapolated_cpd_v3\"], 0\n", ").astype(int)\n", "\n", "post_md_forecast_df.head(1)"]}, {"cell_type": "markdown", "id": "f345b58d-0272-40c0-ab6c-6b900d3a16b2", "metadata": {"tags": []}, "source": ["### hour weights for MD tat=2 (for 6 am to 6pm)"]}, {"cell_type": "code", "execution_count": null, "id": "93fd2e10-5d01-45fc-be58-a25b41cda7e4", "metadata": {}, "outputs": [], "source": ["slot_a_hw = (\n", "    hour_weights_df[hour_weights_df[\"hour_\"].between(6, 17)]\n", "    .groupby([\"outlet_id\"])\n", "    .agg({\"new_hw\": \"sum\"})\n", "    .reset_index()\n", ")\n", "post_md_forecast_df = post_md_forecast_df.merge(slot_a_hw, on=[\"outlet_id\"], how=\"left\")\n", "post_md_forecast_df[\"post_extrapolated_cpd\"] = np.round(\n", "    post_md_forecast_df[\"post_extrapolated_cpd\"] * post_md_forecast_df[\"new_hw\"], 0\n", ")\n", "post_md_forecast_df[\"post_extrapolated_cpd_v2\"] = np.round(\n", "    post_md_forecast_df[\"post_extrapolated_cpd_v2\"] * post_md_forecast_df[\"new_hw\"], 0\n", ")\n", "post_md_forecast_df[\"post_extrapolated_cpd_v3\"] = np.round(\n", "    post_md_forecast_df[\"post_extrapolated_cpd_v3\"] * post_md_forecast_df[\"new_hw\"], 0\n", ")\n", "post_md_forecast_df.drop(columns={\"new_hw\"}, inplace=True)\n", "post_md_forecast_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "005f7929-2b24-4975-8ecd-ec1b93098e29", "metadata": {}, "outputs": [], "source": ["final_md_df = pd.merge(\n", "    post_md_forecast_df,\n", "    pre_md_forecast_df[\n", "        [\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"pre_extrapolated_cpd\",\n", "            \"pre_extrapolated_cpd_v2\",\n", "            \"pre_extrapolated_cpd_v3\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"}),\n", "    on=[\"facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "final_md_df[\"tot_cpd_v1\"] = (\n", "    final_md_df[\"pre_extrapolated_cpd\"] + final_md_df[\"post_extrapolated_cpd\"]\n", ")\n", "final_md_df[\"tot_cpd_v2\"] = (\n", "    final_md_df[\"pre_extrapolated_cpd_v2\"] + final_md_df[\"post_extrapolated_cpd_v2\"]\n", ")\n", "final_md_df[\"tot_cpd_v3\"] = (\n", "    final_md_df[\"pre_extrapolated_cpd_v3\"] + final_md_df[\"post_extrapolated_cpd_v3\"]\n", ")\n", "\n", "final_md_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "3e51dd31-938b-48bd-9da0-369d07fd57ad", "metadata": {"tags": []}, "outputs": [], "source": ["pre_forecast_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4ba0d9e1-25c7-4127-ab31-ae07fc8dfbca", "metadata": {}, "outputs": [], "source": ["one_l.head(10)"]}, {"cell_type": "markdown", "id": "c4ea4367-34c8-4181-a230-ed5b695eb3fb", "metadata": {}, "source": ["## adding 1 litre for transition phase"]}, {"cell_type": "code", "execution_count": null, "id": "01700e89-5c96-43d2-a3e0-87d7b50bbbf3", "metadata": {}, "outputs": [], "source": ["pre_agg = (\n", "    pre_forecast_df.groupby([\"be_facility_id\", \"item_id\", \"fdate\"])\n", "    .agg({\"extrapolated_cpd\": \"sum\"})\n", "    .rename(columns={\"extrapolated_cpd\": \"v1_total\"})\n", "    .reset_index()\n", ")\n", "\n", "forecast_df = pre_forecast_df.merge(pre_agg, on=[\"be_facility_id\", \"item_id\", \"fdate\"], how=\"left\")\n", "forecast_df[\"factor\"] = forecast_df[\"extrapolated_cpd\"] * 1.000 / forecast_df[\"v1_total\"]\n", "forecast_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b1880a0c-f7cc-40a0-8911-85f996b064d2", "metadata": {}, "outputs": [], "source": ["forecast_df[forecast_df[\"facility_id\"] == 435]"]}, {"cell_type": "code", "execution_count": null, "id": "4bfd0276-7938-4f3c-bf2e-bb27ac84216f", "metadata": {}, "outputs": [], "source": ["fact = forecast_df.merge(\n", "    one_l,\n", "    right_on=[\"be_facility_id\", \"500ml_item_id\"],\n", "    left_on=[\"be_facility_id\", \"item_id\"],\n", "    how=\"inner\",\n", ")\n", "fact = fact[(fact[\"fdate\"] >= fact[\"start_date\"]) & (fact[\"fdate\"] <= fact[\"end_date\"])]\n", "fact = (\n", "    fact[[\"facility_id\", \"1L_item_id\", \"factor\"]]\n", "    .drop_duplicates()\n", "    .rename(columns={\"1L_item_id\": \"item_id\", \"factor\": \"factor_2\"})\n", ")\n", "fact[fact[\"facility_id\"] == 435]"]}, {"cell_type": "code", "execution_count": null, "id": "63fb45d0-e939-41ff-a976-cb930199422c", "metadata": {}, "outputs": [], "source": ["new_f = forecast_df.merge(fact, on=[\"facility_id\", \"item_id\"], how=\"left\").fillna(0)\n", "new_f[\"factor\"] = np.where(new_f[\"item_id\"].isin(one_l_item_id), new_f[\"factor_2\"], new_f[\"factor\"])\n", "new_f.drop(columns={\"factor_2\"}, inplace=True)\n", "new_f[new_f[\"facility_id\"] == 435]"]}, {"cell_type": "code", "execution_count": null, "id": "bc7c2596-1f40-493d-b3af-c02136e3930a", "metadata": {}, "outputs": [], "source": ["forecast_df = new_f.copy()\n", "forecast_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c6817058-e23c-48ed-98fb-33465340bd30", "metadata": {"tags": []}, "outputs": [], "source": ["total_conversions = one_l.merge(\n", "    pre_agg,\n", "    left_on=[\"be_facility_id\", \"500ml_item_id\"],\n", "    right_on=[\"be_facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "total_conversions = (\n", "    total_conversions[\n", "        (total_conversions[\"fdate\"] >= total_conversions[\"start_date\"])\n", "        & (total_conversions[\"fdate\"] <= total_conversions[\"end_date\"])\n", "    ]\n", "    .rename(columns={\"v1_total\": \"500ml_qty\"})\n", "    .drop(columns={\"item_id\"})\n", ")\n", "total_conversions = (\n", "    total_conversions.groupby(\n", "        [\"be_facility_id\", \"500ml_item_id\", \"1L_item_id\", \"fdate\", \"500ml_qty\"]\n", "    )\n", "    .agg({\"conversion_factor\": \"max\"})\n", "    .reset_index()\n", ")\n", "total_conversions = (\n", "    total_conversions.merge(\n", "        pre_agg,\n", "        left_on=[\"be_facility_id\", \"1L_item_id\", \"fdate\"],\n", "        right_on=[\"be_facility_id\", \"item_id\", \"fdate\"],\n", "        how=\"left\",\n", "    )\n", "    .rename(columns={\"v1_total\": \"1l_qty\"})\n", "    .drop(columns={\"item_id\"})\n", ")\n", "total_conversions[\"1l_qty\"] = total_conversions[\"1l_qty\"].fillna(0)\n", "total_conversions[\"final_500_qty\"] = np.ceil(\n", "    (total_conversions[\"1l_qty\"] * 2 + total_conversions[\"500ml_qty\"])\n", "    * (1 - total_conversions[\"conversion_factor\"])\n", ")\n", "total_conversions[\"final_1L_qty\"] = np.ceil(\n", "    (total_conversions[\"1l_qty\"] * 2 + total_conversions[\"500ml_qty\"])\n", "    * (total_conversions[\"conversion_factor\"])\n", "    / 2\n", ")\n", "\n", "one_l_df = total_conversions[[\"be_facility_id\", \"1L_item_id\", \"final_1L_qty\"]].rename(\n", "    columns={\"1L_item_id\": \"item_id\"}\n", ")\n", "half_l_df = total_conversions[[\"be_facility_id\", \"500ml_item_id\", \"final_500_qty\"]].rename(\n", "    columns={\"500ml_item_id\": \"item_id\"}\n", ")\n", "\n", "total_conversions.head()"]}, {"cell_type": "code", "execution_count": null, "id": "54215440-90a6-4aac-8d52-565e97069379", "metadata": {}, "outputs": [], "source": ["one_l_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "748ef101-0568-44d3-8e92-334002d6fd7a", "metadata": {}, "outputs": [], "source": ["half_l_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "d07ca34b-1a26-4ca9-9389-c2e456a494c6", "metadata": {}, "outputs": [], "source": ["forecast_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "10de54a3-b60d-47d4-b4de-8b21d7737cff", "metadata": {}, "outputs": [], "source": ["new_forecast_df = forecast_df.merge(one_l_df, on=[\"be_facility_id\", \"item_id\"], how=\"left\")\n", "new_forecast_df = new_forecast_df.merge(half_l_df, on=[\"be_facility_id\", \"item_id\"], how=\"left\")\n", "new_forecast_df[[\"final_1L_qty\", \"final_500_qty\"]] = new_forecast_df[\n", "    [\"final_1L_qty\", \"final_500_qty\"]\n", "].fillna(0)\n", "\n", "new_forecast_df[\"extrapolated_cpd\"] = np.where(\n", "    (new_forecast_df[\"final_500_qty\"] + new_forecast_df[\"final_1L_qty\"]) > 0,\n", "    (new_forecast_df[\"final_500_qty\"] + new_forecast_df[\"final_1L_qty\"])\n", "    * new_forecast_df[\"factor\"],\n", "    new_forecast_df[\"extrapolated_cpd\"],\n", ")\n", "new_forecast_df[\"extrapolated_cpd_v2\"] = np.where(\n", "    (new_forecast_df[\"final_500_qty\"] + new_forecast_df[\"final_1L_qty\"]) > 0,\n", "    (new_forecast_df[\"final_500_qty\"] + new_forecast_df[\"final_1L_qty\"])\n", "    * new_forecast_df[\"factor\"],\n", "    new_forecast_df[\"extrapolated_cpd_v2\"],\n", ")\n", "new_forecast_df[\"extrapolated_cpd_v3\"] = np.where(\n", "    (new_forecast_df[\"final_500_qty\"] + new_forecast_df[\"final_1L_qty\"]) > 0,\n", "    (new_forecast_df[\"final_500_qty\"] + new_forecast_df[\"final_1L_qty\"])\n", "    * new_forecast_df[\"factor\"],\n", "    new_forecast_df[\"extrapolated_cpd_v3\"],\n", ")\n", "new_forecast_df[new_forecast_df[\"be_facility_id\"] == 1923].head()\n", "\n", "\n", "# new_forecast_df.drop(columns={'v1_total','factor','final_1L_qty','final_500_qty'})"]}, {"cell_type": "code", "execution_count": null, "id": "d591aa39-7a59-4e4d-88ba-2dacc0415bbf", "metadata": {}, "outputs": [], "source": ["forecast_df = new_forecast_df[\n", "    [\n", "        \"city\",\n", "        \"be_facility_id\",\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"stype_\",\n", "        \"fdate\",\n", "        \"extrapolated_cpd\",\n", "        \"extrapolated_cpd_v2\",\n", "        \"extrapolated_cpd_v3\",\n", "    ]\n", "]\n", "forecast_df[[\"extrapolated_cpd\", \"extrapolated_cpd_v2\", \"extrapolated_cpd_v3\"]] = forecast_df[\n", "    [\"extrapolated_cpd\", \"extrapolated_cpd_v2\", \"extrapolated_cpd_v3\"]\n", "].astype(int)\n", "forecast_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "1de15800-dadc-4982-9aa8-affb821b30b6", "metadata": {}, "outputs": [], "source": ["forecast_df[forecast_df[\"facility_id\"] == 435]"]}, {"cell_type": "code", "execution_count": null, "id": "be65f0db-7333-4b79-93d8-bc18ada9bb30", "metadata": {"tags": []}, "outputs": [], "source": ["# final_md_df.to_csv('new_md_logic.csv', index = False)"]}, {"cell_type": "code", "execution_count": null, "id": "ffec5ce7-a85a-4f9b-8f5d-6342eecbaa41", "metadata": {}, "outputs": [], "source": ["final_md_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "96241f33-e803-4316-b48d-d39755b70a21", "metadata": {"tags": []}, "outputs": [], "source": ["md_pre_agg = (\n", "    final_md_df.groupby([\"be_facility_id\", \"item_id\", \"fdate\"])\n", "    .agg({\"tot_cpd_v1\": \"sum\"})\n", "    .rename(columns={\"tot_cpd_v1\": \"v1_total\"})\n", "    .reset_index()\n", ")\n", "\n", "final_md_df = final_md_df.merge(md_pre_agg, on=[\"be_facility_id\", \"item_id\", \"fdate\"], how=\"left\")\n", "final_md_df[\"factor\"] = final_md_df[\"tot_cpd_v1\"] * 1.000 / final_md_df[\"v1_total\"]\n", "final_md_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9b9f8932-83ea-4fe5-85f9-d176806e2310", "metadata": {}, "outputs": [], "source": ["md_fact = final_md_df.merge(\n", "    one_l,\n", "    right_on=[\"be_facility_id\", \"500ml_item_id\"],\n", "    left_on=[\"be_facility_id\", \"item_id\"],\n", "    how=\"inner\",\n", ")\n", "md_fact = md_fact[\n", "    (md_fact[\"fdate\"] >= md_fact[\"start_date\"]) & (md_fact[\"fdate\"] <= md_fact[\"end_date\"])\n", "]\n", "md_fact = (\n", "    md_fact[[\"facility_id\", \"1L_item_id\", \"factor\"]]\n", "    .drop_duplicates()\n", "    .rename(columns={\"1L_item_id\": \"item_id\", \"factor\": \"factor_2\"})\n", ")\n", "md_fact[md_fact[\"facility_id\"] == 435]"]}, {"cell_type": "code", "execution_count": null, "id": "0d4c7009-6484-4105-b40b-f642c834c2e6", "metadata": {}, "outputs": [], "source": ["md_new_f = final_md_df.merge(md_fact, on=[\"facility_id\", \"item_id\"], how=\"left\").fillna(0)\n", "md_new_f[\"factor\"] = np.where(\n", "    md_new_f[\"item_id\"].isin(one_l_item_id), md_new_f[\"factor_2\"], md_new_f[\"factor\"]\n", ")\n", "md_new_f.drop(columns={\"factor_2\"}, inplace=True)\n", "md_new_f[md_new_f[\"facility_id\"] == 1654]"]}, {"cell_type": "code", "execution_count": null, "id": "f1dee665-6b4f-4100-901d-c82d1b7da4b1", "metadata": {}, "outputs": [], "source": ["final_md_df = md_new_f.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "8093c774-b01b-48b1-9534-d7866f46307b", "metadata": {}, "outputs": [], "source": ["total_conversions = one_l.merge(\n", "    md_pre_agg,\n", "    left_on=[\"be_facility_id\", \"500ml_item_id\"],\n", "    right_on=[\"be_facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "total_conversions = (\n", "    total_conversions[\n", "        (total_conversions[\"fdate\"] >= total_conversions[\"start_date\"])\n", "        & (total_conversions[\"fdate\"] <= total_conversions[\"end_date\"])\n", "    ]\n", "    .rename(columns={\"v1_total\": \"500ml_qty\"})\n", "    .drop(columns={\"item_id\"})\n", ")\n", "total_conversions = (\n", "    total_conversions.groupby(\n", "        [\"be_facility_id\", \"500ml_item_id\", \"1L_item_id\", \"fdate\", \"500ml_qty\"]\n", "    )\n", "    .agg({\"conversion_factor\": \"max\"})\n", "    .reset_index()\n", ")\n", "total_conversions = (\n", "    total_conversions.merge(\n", "        md_pre_agg,\n", "        left_on=[\"be_facility_id\", \"1L_item_id\", \"fdate\"],\n", "        right_on=[\"be_facility_id\", \"item_id\", \"fdate\"],\n", "        how=\"left\",\n", "    )\n", "    .rename(columns={\"v1_total\": \"1l_qty\"})\n", "    .drop(columns={\"item_id\"})\n", ")\n", "total_conversions[\"1l_qty\"] = total_conversions[\"1l_qty\"].fillna(0)\n", "total_conversions[\"final_500_qty\"] = np.ceil(\n", "    (total_conversions[\"1l_qty\"] * 2 + total_conversions[\"500ml_qty\"])\n", "    * (1 - total_conversions[\"conversion_factor\"])\n", ")\n", "total_conversions[\"final_1L_qty\"] = np.ceil(\n", "    (total_conversions[\"1l_qty\"] * 2 + total_conversions[\"500ml_qty\"])\n", "    * (total_conversions[\"conversion_factor\"])\n", "    / 2\n", ")\n", "\n", "one_l_df = total_conversions[[\"be_facility_id\", \"1L_item_id\", \"final_1L_qty\"]].rename(\n", "    columns={\"1L_item_id\": \"item_id\"}\n", ")\n", "half_l_df = total_conversions[[\"be_facility_id\", \"500ml_item_id\", \"final_500_qty\"]].rename(\n", "    columns={\"500ml_item_id\": \"item_id\"}\n", ")\n", "\n", "total_conversions.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b0405c2d-d11a-4e28-b5ee-bac69d10687c", "metadata": {}, "outputs": [], "source": ["new_md_forecast_df = final_md_df.merge(one_l_df, on=[\"be_facility_id\", \"item_id\"], how=\"left\")\n", "new_md_forecast_df = new_md_forecast_df.merge(\n", "    half_l_df, on=[\"be_facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "new_md_forecast_df[[\"final_1L_qty\", \"final_500_qty\"]] = new_md_forecast_df[\n", "    [\"final_1L_qty\", \"final_500_qty\"]\n", "].fillna(0)\n", "\n", "new_md_forecast_df[\"tot_cpd_v1\"] = np.where(\n", "    (new_md_forecast_df[\"final_500_qty\"] + new_md_forecast_df[\"final_1L_qty\"]) > 0,\n", "    (new_md_forecast_df[\"final_500_qty\"] + new_md_forecast_df[\"final_1L_qty\"])\n", "    * new_md_forecast_df[\"factor\"],\n", "    new_md_forecast_df[\"tot_cpd_v1\"],\n", ")\n", "new_md_forecast_df[\"tot_cpd_v2\"] = np.where(\n", "    (new_md_forecast_df[\"final_500_qty\"] + new_md_forecast_df[\"final_1L_qty\"]) > 0,\n", "    (new_md_forecast_df[\"final_500_qty\"] + new_md_forecast_df[\"final_1L_qty\"])\n", "    * new_md_forecast_df[\"factor\"],\n", "    new_md_forecast_df[\"tot_cpd_v2\"],\n", ")\n", "new_md_forecast_df[\"tot_cpd_v3\"] = np.where(\n", "    (new_md_forecast_df[\"final_500_qty\"] + new_md_forecast_df[\"final_1L_qty\"]) > 0,\n", "    (new_md_forecast_df[\"final_500_qty\"] + new_md_forecast_df[\"final_1L_qty\"])\n", "    * new_md_forecast_df[\"factor\"],\n", "    new_md_forecast_df[\"tot_cpd_v3\"],\n", ")\n", "new_md_forecast_df[new_md_forecast_df[\"facility_id\"] == 1564].head()"]}, {"cell_type": "code", "execution_count": null, "id": "f9e90a18-0d30-4f79-b803-411659117371", "metadata": {}, "outputs": [], "source": ["final_md_df = new_md_forecast_df.copy()\n", "final_md_df.drop(columns={\"v1_total\", \"factor\", \"final_1L_qty\", \"final_500_qty\"}, inplace=True)\n", "final_md_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "02fe05e5-4ed5-476c-8994-4c2b612f0a29", "metadata": {"tags": []}, "outputs": [], "source": ["new_md_forecast_df = new_md_forecast_df[\n", "    (new_md_forecast_df[\"final_1L_qty\"] + new_md_forecast_df[\"final_500_qty\"]) > 0\n", "]\n", "new_md_forecast_df = new_md_forecast_df[\n", "    [\n", "        \"city\",\n", "        \"be_facility_id\",\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"stype_\",\n", "        \"fdate\",\n", "        \"tot_cpd_v1\",\n", "        \"tot_cpd_v2\",\n", "        \"tot_cpd_v3\",\n", "        \"factor\",\n", "        \"v1_total\",\n", "        \"final_1L_qty\",\n", "        \"final_500_qty\",\n", "    ]\n", "].rename(\n", "    columns={\n", "        \"tot_cpd_v1\": \"extrapolated_cpd\",\n", "        \"tot_cpd_v2\": \"extrapolated_cpd_v2\",\n", "        \"tot_cpd_v3\": \"extrapolated_cpd_v3\",\n", "    }\n", ")\n", "\n", "new_forecast_df = new_forecast_df[\n", "    (new_forecast_df[\"final_1L_qty\"] + new_forecast_df[\"final_500_qty\"]) > 0\n", "]\n", "final_1_l_df = pd.concat([new_md_forecast_df, new_forecast_df]).reset_index()\n", "final_1_l_df.drop(columns={\"index\"}, inplace=True)\n", "final_1_l_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9040555d-dbd4-475a-9fc8-98a735d7b768", "metadata": {"tags": []}, "outputs": [], "source": ["## item name mapping query\n", "query = f\"\"\"\n", "    SELECT item_id, name AS item_name\n", "    FROM rpc.item_category_details\n", "    WHERE item_id IN {item_id_list} AND lake_active_record\n", "    GROUP BY 1,2\n", "\"\"\"\n", "item_df = read_sql_query(query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "cc374cef-8b38-4cab-a209-dda38fb02dad", "metadata": {}, "outputs": [], "source": ["final_1_l_df = final_1_l_df.merge(item_df, on=[\"item_id\"], how=\"left\")\n", "final_1_l_df = final_1_l_df[\n", "    [\n", "        \"city\",\n", "        \"be_facility_id\",\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"stype_\",\n", "        \"fdate\",\n", "        \"extrapolated_cpd\",\n", "        \"extrapolated_cpd_v2\",\n", "        \"extrapolated_cpd_v3\",\n", "        \"factor\",\n", "        \"v1_total\",\n", "        \"final_1L_qty\",\n", "        \"final_500_qty\",\n", "    ]\n", "]\n", "final_1_l_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8b1199ad-c821-4c46-acc6-dc3b0f360a8b", "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.to_sheets(\n", "        final_1_l_df,\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"1_Liter_forecasts\",\n", "    )\n", "except:\n", "    final_1_l_df.to_csv(\"1_Liter_forecasts.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "6aed94fa-bfaa-4929-a825-4729dccbf95c", "metadata": {}, "outputs": [], "source": ["final_md_df = final_md_df.merge(item_df, on=[\"item_id\"], how=\"left\")\n", "final_md_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8be21ff8-5d29-4f10-9221-d905bec0ea1e", "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.to_sheets(\n", "        final_md_df,\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"mother_dairy_forecast\",\n", "    )\n", "except:\n", "    final_md_df.to_csv(\"mother_dairy_forecast.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "f78f2cbd-7552-4949-ba85-50e6c74cbd09", "metadata": {}, "outputs": [], "source": ["mother_dairy_df = (\n", "    final_md_df[\n", "        [\n", "            \"city\",\n", "            \"be_facility_id\",\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"stype_\",\n", "            \"fdate\",\n", "            \"tot_cpd_v1\",\n", "            \"tot_cpd_v2\",\n", "            \"tot_cpd_v3\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "mother_dairy_df = mother_dairy_df.rename(\n", "    columns={\n", "        \"tot_cpd_v1\": \"extrapolated_cpd\",\n", "        \"tot_cpd_v2\": \"extrapolated_cpd_v2\",\n", "        \"tot_cpd_v3\": \"extrapolated_cpd_v3\",\n", "    }\n", ")\n", "\n", "print(mother_dairy_df.shape)\n", "mother_dairy_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "6596b3dc-99f4-466a-a1df-e9e899a495c5", "metadata": {}, "outputs": [], "source": ["del [\n", "    final_md_df,\n", "    pre_md_base_df,\n", "    pre_md_calc_df,\n", "    pre_md_cpd_df,\n", "    post_md_base_df,\n", "    post_md_calc_df,\n", "    post_md_cpd_df,\n", "]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "d2bd6839-4514-4b19-b4d1-869d34aa6398", "metadata": {}, "source": ["## Leftover calculation for Mother Dairy"]}, {"cell_type": "code", "execution_count": null, "id": "917cb733-0eb1-4d79-89d6-ce86705d1864", "metadata": {}, "outputs": [], "source": ["fe_inv_query = f\"\"\"\n", "    SELECT item_id, outlet_id, actual_quantity, blocked_quantity,\n", "    CASE \n", "        WHEN (actual_quantity - blocked_quantity) < 0 THEN 0 \n", "        ELSE (actual_quantity - blocked_quantity) \n", "    END AS net_inv\n", "    FROM (\n", "        SELECT iii.item_id, iii.outlet_id, iii.quantity AS actual_quantity,\n", "        (COALESCE(SUM(CASE WHEN iibi.blocked_type IN (1,2,5) THEN iibi.quantity ELSE 0 END),0)) AS blocked_quantity\n", "        FROM ims.ims_item_inventory iii\n", "        LEFT JOIN ims.ims_item_blocked_inventory iibi ON iii.item_id = iibi.item_id\n", "        AND iii.outlet_id = iibi.outlet_id\n", "        WHERE iii.item_id IN {item_id_list} AND iii.outlet_id IN {outlet_id_list} AND iii.active = 1\n", "        GROUP BY 1, 2, 3\n", "    ) ims\n", "    \"\"\"\n", "inv_df = read_sql_query(fe_inv_query, trino)\n", "\n", "fe_inv_df = (\n", "    inv_df.groupby([\"item_id\", \"outlet_id\"])\n", "    .agg({\"net_inv\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"net_inv\": \"current_inv\"})\n", ")\n", "\n", "fe_inv_df.head(1)"]}, {"cell_type": "markdown", "id": "3c38b4cd-a6eb-4dae-b3e4-6a6fb6f24225", "metadata": {}, "source": ["## Potential Sales till 6 PM"]}, {"cell_type": "code", "execution_count": null, "id": "15825803-3b9c-411d-8039-3efbb78c78bf", "metadata": {}, "outputs": [], "source": ["def sales_percentile(x):\n", "    return np.percentile(x, 75)"]}, {"cell_type": "code", "execution_count": null, "id": "76ee893d-7a1c-4796-b802-4733d262c7f7", "metadata": {}, "outputs": [], "source": ["lo_sales_hw = (\n", "    hour_weights_df[hour_weights_df[\"hour_\"].between(current_hour - 1, 17)]\n", "    .groupby([\"outlet_id\"])\n", "    .agg({\"new_hw\": \"sum\"})\n", "    .reset_index()\n", ")\n", "pot_sales_df = post_md_forecast_df.merge(lo_sales_hw, on=[\"outlet_id\"], how=\"left\")\n", "pot_sales_df[\"post_extrapolated_cpd\"] = np.round(\n", "    pot_sales_df[\"post_extrapolated_cpd\"] * pot_sales_df[\"new_hw\"], 0\n", ")\n", "pot_sales_df[\"post_extrapolated_cpd_v2\"] = np.round(\n", "    pot_sales_df[\"post_extrapolated_cpd_v2\"] * pot_sales_df[\"new_hw\"], 0\n", ")\n", "pot_sales_df[\"post_extrapolated_cpd_v3\"] = np.round(\n", "    pot_sales_df[\"post_extrapolated_cpd_v3\"] * pot_sales_df[\"new_hw\"], 0\n", ")\n", "pot_sales_df.drop(columns={\"new_hw\"}, inplace=True)\n", "pot_sales_df.head()"]}, {"cell_type": "markdown", "id": "cd004dfe-abdf-48e0-9a34-58c5d7195799", "metadata": {}, "source": ["### Potential LO"]}, {"cell_type": "code", "execution_count": null, "id": "607070a4-6750-463a-8b93-4ad4f3e2871b", "metadata": {}, "outputs": [], "source": ["lo_df = pd.merge(\n", "    pot_sales_df[\n", "        [\"be_facility_id\", \"facility_id\", \"outlet_id\", \"item_id\", \"post_extrapolated_cpd_v2\"]\n", "    ],\n", "    fe_inv_df,\n", "    on=[\"outlet_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "lo_df[\"lo\"] = lo_df[\"current_inv\"] - lo_df[\"post_extrapolated_cpd_v2\"]\n", "lo_df[\"lo\"] = np.where(lo_df[\"lo\"] < 0, 0, lo_df[\"lo\"])\n", "lo_df = lo_df.drop(columns={\"post_extrapolated_cpd_v2\", \"outlet_id\"})\n", "lo_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "56c3f1cf-05e4-4543-a3fc-a247e84eba11", "metadata": {}, "outputs": [], "source": ["lo_df.groupby([\"be_facility_id\", \"item_id\"]).agg({\"current_inv\": \"sum\", \"lo\": \"sum\"}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "a192e170-8e1d-41fc-ac9d-32fc16aed816", "metadata": {}, "outputs": [], "source": ["del [fe_inv_df, pot_sales_df]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "8597273c-6828-4216-8b6e-30225a5b9725", "metadata": {}, "source": ["## Final Mother dairy forecast"]}, {"cell_type": "code", "execution_count": null, "id": "f2bc3263-1372-4615-b54c-b6af042f4c9d", "metadata": {}, "outputs": [], "source": ["mother_dairy_df = pd.merge(mother_dairy_df, lo_df, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "mother_dairy_df[\"extrapolated_cpd\"] = mother_dairy_df[\"extrapolated_cpd\"] - mother_dairy_df[\"lo\"]\n", "mother_dairy_df[\"extrapolated_cpd_v2\"] = (\n", "    mother_dairy_df[\"extrapolated_cpd_v2\"] - mother_dairy_df[\"lo\"]\n", ")\n", "mother_dairy_df[\"extrapolated_cpd_v3\"] = (\n", "    mother_dairy_df[\"extrapolated_cpd_v3\"] - mother_dairy_df[\"lo\"]\n", ")\n", "mother_dairy_df = mother_dairy_df.drop(columns={\"lo\"})\n", "mother_dairy_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "f12cc5cc-a406-4861-bd90-8e899dc0b762", "metadata": {}, "outputs": [], "source": ["del [lo_df]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "cbd6f1ba-1913-43eb-893b-4ddca80721ae", "metadata": {}, "outputs": [], "source": ["forecast_df = pd.concat([pre_forecast_df, mother_dairy_df])\n", "print(forecast_df.shape)\n", "forecast_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "8a5bad74-dd5b-4142-8e97-1f3e36589199", "metadata": {}, "outputs": [], "source": ["del [pre_forecast_df, mother_dairy_df]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "7bc28469-ce0c-4088-be16-78fd083a2c8a", "metadata": {}, "source": ["## DS Model Plugin"]}, {"cell_type": "code", "execution_count": null, "id": "9c382bad-2f26-4654-b9ea-0d7ccaa7b65a", "metadata": {}, "outputs": [], "source": ["ds_query = f\"\"\"\n", "    SELECT DATE(current_replenishment_ts_ist) AS fdate, facility_id, item_id, AVG(max_qty) AS ds_qty\n", "    FROM ds_etls.demand_forecast_item_ordering_min_max_quantity_milk  \n", "    WHERE updated_at_ist >= current_date - interval '10' day\n", "    GROUP BY 1,2,3\n", "\"\"\"\n", "\n", "ds_input_df = read_sql_query(ds_query, trino)\n", "\n", "ds_input_df[\"fdate\"] = pd.to_datetime(ds_input_df[\"fdate\"])\n", "\n", "ds_input_df[\"ds_qty\"] = ds_input_df[\"ds_qty\"].fillna(0).astype(int)\n", "\n", "ds_input_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "3e6a89f5-8cfc-41ab-bcc8-72f476da1bb8", "metadata": {}, "outputs": [], "source": ["ds_be_df = (\n", "    forecast_df[[\"be_facility_id\", \"facility_id\", \"item_id\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "ds_input_df = pd.merge(ds_be_df, ds_input_df, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "\n", "try:\n", "    ds_model_be = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::ds-model-input\",\n", "    )\n", "\n", "except:\n", "    ds_model_be = pd.read_csv(\"ds_model_input.csv\")\n", "\n", "ds_model_be = ds_model_be.rename(columns={\"backend_facility_id\": \"be_facility_id\"}).drop(\n", "    columns={\"backend_name\"}\n", ")\n", "\n", "ds_model_be[\"be_facility_id\"] = ds_model_be[\"be_facility_id\"].astype(int)\n", "\n", "ds_input_df = pd.merge(ds_input_df, ds_model_be, on=[\"be_facility_id\"], how=\"inner\")\n", "\n", "ds_input_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "e26e700a-3cdc-405b-bf0d-d15f7d3956dc", "metadata": {}, "outputs": [], "source": ["forecast_df = pd.merge(\n", "    forecast_df,\n", "    ds_input_df[[\"facility_id\", \"item_id\", \"fdate\", \"ds_qty\"]].drop_duplicates(),\n", "    on=[\"fdate\", \"facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "forecast_df[\"extrapolated_cpd\"] = np.where(\n", "    forecast_df[\"ds_qty\"].isna(), forecast_df[\"extrapolated_cpd\"], forecast_df[\"ds_qty\"]\n", ")\n", "\n", "forecast_df[\"extrapolated_cpd_v2\"] = np.where(\n", "    forecast_df[\"ds_qty\"].isna(),\n", "    forecast_df[\"extrapolated_cpd_v2\"],\n", "    forecast_df[\"ds_qty\"],\n", ")\n", "\n", "forecast_df[\"extrapolated_cpd_v3\"] = np.where(\n", "    forecast_df[\"ds_qty\"].isna(),\n", "    forecast_df[\"extrapolated_cpd_v3\"],\n", "    forecast_df[\"ds_qty\"],\n", ")\n", "\n", "forecast_df[[\"extrapolated_cpd\", \"extrapolated_cpd_v2\", \"extrapolated_cpd_v3\"]] = (\n", "    forecast_df[[\"extrapolated_cpd\", \"extrapolated_cpd_v2\", \"extrapolated_cpd_v3\"]]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "forecast_df[\"ds_f\"] = np.where(forecast_df[\"ds_qty\"].isna(), 0, 1)\n", "\n", "forecast_df = forecast_df.drop(columns={\"ds_qty\"})\n", "\n", "forecast_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "bbfacaba-7d1c-454b-8ae6-65bd19544292", "metadata": {}, "outputs": [], "source": ["del [ds_be_df, ds_model_be]\n", "gc.collect()"]}, {"cell_type": "raw", "id": "c8e14b31-316e-4cbb-bc84-07135bb3377e", "metadata": {"tags": []}, "source": ["## City Growth Degrowth Inputs"]}, {"cell_type": "raw", "id": "0559ea2b-877a-47a9-a347-b6a1684c9f30", "metadata": {}, "source": ["try:\n", "    city_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::city-input\",\n", "    ).rename(columns={\"factor\": \"city_f\"})\n", "\n", "except:\n", "    city_df = pd.read_csv(\"city_input.csv\").rename(columns={\"factor\": \"city_f\"})\n", "\n", "city_df[\"start_date\"] = pd.to_datetime(city_df[\"start_date\"])\n", "city_df[\"end_date\"] = pd.to_datetime(city_df[\"end_date\"])\n", "\n", "city_df[\"city_f\"] = city_df[\"city_f\"].astype(float)\n", "\n", "sku_mapping_df = pd.DataFrame(\n", "    {\"sku_type\": [\"all\", \"all\", \"all\"], \"stype_\": [\"top\", \"bottom\", \"premium\"]}\n", ")\n", "\n", "city_df = city_df.merge(sku_mapping_df, on=[\"sku_type\"], how=\"left\")\n", "\n", "city_df[\"stype_\"] = np.where(city_df[\"stype_\"].isna(), city_df[\"sku_type\"], city_df[\"stype_\"])\n", "\n", "city_df = city_df.drop(columns={\"sku_type\"})\n", "\n", "city_df.head(50)"]}, {"cell_type": "raw", "id": "9b4ca2d6-2a9c-4820-b7e7-24f345a7983f", "metadata": {}, "source": ["forecast_df = forecast_df.merge(city_df, on=[\"city\", \"stype_\"], how=\"left\")\n", "\n", "forecast_df[\"city_f\"] = np.where(\n", "    (forecast_df[\"fdate\"] >= forecast_df[\"start_date\"])\n", "    & (forecast_df[\"fdate\"] <= forecast_df[\"end_date\"]),\n", "    forecast_df[\"city_f\"],\n", "    1,\n", ")\n", "\n", "forecast_df[\"extrapolated_cpd\"] = np.ceil(\n", "    forecast_df[\"extrapolated_cpd\"] * forecast_df[\"city_f\"]\n", ").astype(int)\n", "\n", "forecast_df[\"extrapolated_cpd_v2\"] = np.ceil(\n", "    forecast_df[\"extrapolated_cpd_v2\"] * forecast_df[\"city_f\"]\n", ").astype(int)\n", "\n", "forecast_df[\"extrapolated_cpd_v3\"] = np.ceil(\n", "    forecast_df[\"extrapolated_cpd_v3\"] * forecast_df[\"city_f\"]\n", ").astype(int)\n", "\n", "forecast_df = forecast_df.drop(columns={\"start_date\", \"end_date\"})\n", "\n", "forecast_df.head(1)"]}, {"cell_type": "raw", "id": "72613011-d9bc-4f02-b385-87dca95bce7d", "metadata": {"tags": []}, "source": ["## Facility Growth Degrowth Inputs"]}, {"cell_type": "raw", "id": "b2dbe7a9-3522-4af1-be85-bc7b06ab0b40", "metadata": {}, "source": ["try:\n", "    store_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::store-input\",\n", "    )\n", "\n", "except:\n", "    store_df = pd.read_csv(\"store_input.csv\")\n", "\n", "store_df[\"facility_id\"] = store_df[\"facility_id\"].fillna(0).astype(int)\n", "\n", "store_df[\"start_date\"] = pd.to_datetime(store_df[\"start_date\"])\n", "store_df[\"end_date\"] = pd.to_datetime(store_df[\"end_date\"])\n", "\n", "store_df[\"factor\"] = store_df[\"factor\"].astype(float)\n", "store_fac_df = store_df.copy()\n", "store_df.head()"]}, {"cell_type": "raw", "id": "1bf80e77-2a8c-489e-8706-137e16380465", "metadata": {}, "source": ["forecast_df = forecast_df.merge(store_df, on=[\"facility_id\"], how=\"left\")\n", "\n", "forecast_df[\"factor\"] = np.where(\n", "    (forecast_df[\"fdate\"] >= forecast_df[\"start_date\"])\n", "    & (forecast_df[\"fdate\"] <= forecast_df[\"end_date\"]),\n", "    forecast_df[\"factor\"],\n", "    1,\n", ")\n", "\n", "forecast_df[\"extrapolated_cpd\"] = np.ceil(\n", "    forecast_df[\"extrapolated_cpd\"] * forecast_df[\"factor\"]\n", ").astype(int)\n", "\n", "forecast_df[\"extrapolated_cpd_v2\"] = np.ceil(\n", "    forecast_df[\"extrapolated_cpd_v2\"] * forecast_df[\"factor\"]\n", ").astype(int)\n", "\n", "forecast_df[\"extrapolated_cpd_v3\"] = np.ceil(\n", "    forecast_df[\"extrapolated_cpd_v3\"] * forecast_df[\"factor\"]\n", ").astype(int)\n", "\n", "forecast_df = forecast_df.drop(columns={\"start_date\", \"end_date\"})\n", "\n", "forecast_df.head(1)"]}, {"cell_type": "raw", "id": "f250f246-9d94-4703-9526-10610a9f45e8", "metadata": {}, "source": ["## Backend Item Growth Degrowth"]}, {"cell_type": "raw", "id": "4d74d54e-6e2e-47c4-b587-35dd097495ae", "metadata": {}, "source": ["try:\n", "    store_item_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::backend-item-input\",\n", "    ).rename(columns={\"factor\": \"si_factor\"})\n", "\n", "except:\n", "    store_item_df = pd.read_csv(\"be_input.csv\").rename(columns={\"factor\": \"si_factor\"})\n", "\n", "store_item_df[\"be_facility_id\"] = store_item_df[\"be_facility_id\"].fillna(0).astype(int)\n", "store_item_df[\"item_id\"] = store_item_df[\"item_id\"].fillna(0).astype(int)\n", "\n", "store_item_df[\"start_date\"] = pd.to_datetime(store_item_df[\"start_date\"])\n", "store_item_df[\"end_date\"] = pd.to_datetime(store_item_df[\"end_date\"])\n", "\n", "store_item_df[\"si_factor\"] = store_item_df[\"si_factor\"].astype(float)\n", "\n", "store_item_df.head(1)"]}, {"cell_type": "raw", "id": "35eec6e6-9cd5-4ec4-8a14-f4fe92b89726", "metadata": {}, "source": ["forecast_df = forecast_df.merge(store_item_df, on=[\"be_facility_id\", \"item_id\"], how=\"left\")\n", "\n", "forecast_df[\"si_factor\"] = np.where(\n", "    (forecast_df[\"fdate\"] >= forecast_df[\"start_date\"])\n", "    & (forecast_df[\"fdate\"] <= forecast_df[\"end_date\"]),\n", "    forecast_df[\"si_factor\"],\n", "    1,\n", ")\n", "\n", "forecast_df[\"extrapolated_cpd\"] = (\n", "    np.round(forecast_df[\"extrapolated_cpd\"] * forecast_df[\"si_factor\"], 0).fillna(0).astype(int)\n", ")\n", "\n", "forecast_df[\"extrapolated_cpd_v2\"] = (\n", "    np.round(forecast_df[\"extrapolated_cpd_v2\"] * forecast_df[\"si_factor\"], 0).fillna(0).astype(int)\n", ")\n", "\n", "forecast_df[\"extrapolated_cpd_v3\"] = (\n", "    np.round(forecast_df[\"extrapolated_cpd_v3\"] * forecast_df[\"si_factor\"], 0).fillna(0).astype(int)\n", ")\n", "\n", "forecast_df = forecast_df.drop(columns={\"start_date\", \"end_date\"})\n", "\n", "forecast_df.head(1)"]}, {"cell_type": "raw", "id": "2cfe662b-ad4d-462f-a6c2-42a81cdb737c", "metadata": {}, "source": ["## Manual CPD Overwrite"]}, {"cell_type": "raw", "id": "1f4a44f9-6a79-4cc9-beb5-dbcc3f9945e1", "metadata": {}, "source": ["try:\n", "    manual_df = pb.from_sheets(\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"config::manual-forecast-input\",\n", "    )\n", "\n", "except:\n", "    manual_df = pd.read_csv(\"manual_input.csv\")\n", "\n", "manual_df[\"facility_id\"] = manual_df[\"facility_id\"].fillna(0).astype(int)\n", "manual_df[\"item_id\"] = manual_df[\"item_id\"].fillna(0).astype(int)\n", "manual_df[\"manual_cpd\"] = manual_df[\"manual_cpd\"].fillna(0).astype(int)\n", "\n", "manual_df[\"start_date\"] = pd.to_datetime(manual_df[\"start_date\"])\n", "manual_df[\"end_date\"] = pd.to_datetime(manual_df[\"end_date\"])\n", "\n", "manual_df.head(1)"]}, {"cell_type": "raw", "id": "b8a0fd79-2ade-4753-bc98-a1265ae70af1", "metadata": {}, "source": ["forecast_df = forecast_df.merge(manual_df, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "\n", "forecast_df[\"manual_flag\"] = np.where(\n", "    (forecast_df[\"fdate\"] >= forecast_df[\"start_date\"])\n", "    & (forecast_df[\"fdate\"] <= forecast_df[\"end_date\"]),\n", "    1,\n", "    0,\n", ")\n", "\n", "forecast_df[\"extrapolated_cpd\"] = np.where(\n", "    forecast_df[\"manual_flag\"] == 0,\n", "    forecast_df[\"extrapolated_cpd\"],\n", "    forecast_df[\"manual_cpd\"],\n", ").astype(int)\n", "\n", "forecast_df[\"extrapolated_cpd_v2\"] = np.where(\n", "    forecast_df[\"manual_flag\"] == 0,\n", "    forecast_df[\"extrapolated_cpd_v2\"],\n", "    forecast_df[\"manual_cpd\"],\n", ").astype(int)\n", "\n", "forecast_df[\"extrapolated_cpd_v3\"] = np.where(\n", "    forecast_df[\"manual_flag\"] == 0,\n", "    forecast_df[\"extrapolated_cpd_v3\"],\n", "    forecast_df[\"manual_cpd\"],\n", ").astype(int)\n", "\n", "forecast_df = forecast_df.drop(columns={\"start_date\", \"end_date\", \"manual_flag\"})\n", "\n", "forecast_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "9ec254f5-de0d-4321-b3a7-5081d63d2721", "metadata": {}, "outputs": [], "source": ["del [pre_calc_df, pre_cpd_df]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "6a0b1182-47df-468d-9c08-6d4d20a9f812", "metadata": {}, "source": ["# New Store Plugin"]}, {"cell_type": "code", "execution_count": null, "id": "034bb0bd-1c15-46b4-9d4b-d6461cb47c75", "metadata": {}, "outputs": [], "source": ["new_store_forecast_query = f\"\"\"\n", "   WITH base AS (\n", "        SELECT facility_id, item_id, outlet_id, date_, \n", "        CASE WHEN item_nearby_fps IS NULL THEN 0 ELSE item_nearby_fps END AS item_nearby_fps,\n", "        CASE WHEN item_recent_fps IS NULL THEN 0 ELSE item_recent_fps END AS item_recent_fps, \n", "        CASE WHEN item_avail_fps IS NULL THEN 0 ELSE item_avail_fps END AS item_avail_fps\n", "        FROM (\n", "            SELECT a.* FROM supply_etls.new_store_forecast_logs a\n", "            JOIN (\n", "                SELECT facility_id, item_id, date_, MAX(updated_at) AS updated_at\n", "                FROM supply_etls.new_store_forecast_logs\n", "                GROUP BY 1,2,3\n", "            ) b ON a.facility_id = b.facility_id AND a.item_id = b.item_id AND a.date_ = b.date_ AND a.updated_at = b.updated_at\n", "            WHERE a.category IN ('Milk') AND DATE(a.date_) IN (DATE('{today_date}') + interval '2' day)\n", "        )\n", "    ),\n", "\n", "    min_max_base AS (\n", "        SELECT facility_id, outlet_id, item_id, DATE(date_) AS fdate, item_nearby_fps, item_recent_fps, item_avail_fps\n", "        FROM base\n", "    ),\n", "    \n", "    store_age_base AS (\n", "        WITH first_outbound AS (\n", "            SELECT outlet_id, DATE(od.cart_checkout_ts_ist) AS f_date\n", "            FROM dwh.fact_sales_order_details od \n", "            WHERE od.order_create_dt_ist > DATE('{today_date}') - interval '30' day\n", "            AND od.is_internal_order = false \n", "            AND od.outlet_id IN (SELECT DISTINCT outlet_id FROM min_max_base)\n", "            AND (od.order_type NOT LIKE '%%internal%%' OR od.order_type IS NULL) AND od.order_current_status = 'DELIVERED'\n", "            GROUP BY 1,2\n", "            having  count(distinct order_id)>10\n", "        )\n", "        \n", "        SELECT outlet_id, min(f_date) as start_date, DATE_DIFF('day', min(f_date), (DATE('{today_date}') + interval '2' day)) AS age\n", "        FROM first_outbound\n", "        group by 1\n", "    ),\n", "    \n", "    be_mapping AS (\n", "        SELECT DISTINCT item_id, outlet_id, cb.facility_id AS be_facility_id\n", "        FROM rpc.item_outlet_tag_mapping tm\n", "        JOIN retail.console_outlet cb ON cb.id = CAST(tag_value AS int) AND cb.active = 1 AND cb.lake_active_record\n", "        WHERE tm.active = 1 AND tm.tag_type_id = 8 AND tm.lake_active_record\n", "    )\n", "\n", "    SELECT a.*, start_date, be_facility_id, cl.name AS city,\n", "    CASE WHEN b.age IS NULL THEN 0 ELSE age END AS age\n", "    FROM min_max_base a\n", "    LEFT JOIN store_age_base b ON a.outlet_id = b.outlet_id\n", "    JOIN be_mapping bm ON bm.item_id = a.item_id AND bm.outlet_id = a.outlet_id\n", "    JOIN retail.console_outlet co ON co.id = a.outlet_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "    LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id AND cl.lake_active_record\n", "    \n", "\"\"\"\n", "new_store_df = pd.read_sql_query(new_store_forecast_query, trino)\n", "new_store_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "869e74a4-c7d6-4e5a-8c4c-364bb81cddaf", "metadata": {}, "outputs": [], "source": ["new_store_df_tot = (\n", "    new_store_df.groupby([\"facility_id\"])\n", "    .agg({\"item_nearby_fps\": \"sum\", \"item_recent_fps\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_nearby_fps\": \"near\", \"item_recent_fps\": \"recent\"})\n", ")\n", "new_store_df = new_store_df.merge(new_store_df_tot, on=[\"facility_id\"], how=\"left\")\n", "new_store_df[\"final_cpd\"] = np.round(new_store_df[\"near\"] * 0.75 + new_store_df[\"recent\"] * 0.25, 0)\n", "new_store_df = new_store_df[\n", "    [\"city\", \"be_facility_id\", \"facility_id\", \"outlet_id\", \"item_id\", \"fdate\", \"age\", \"final_cpd\"]\n", "]\n", "new_store_df.tail()"]}, {"cell_type": "code", "execution_count": null, "id": "a4a52dfb-40bd-4f59-96b7-2a87835ebf08", "metadata": {}, "outputs": [], "source": ["## sister_store\n", "\n", "sis = f\"\"\"\n", "            select\n", "            distinct\n", "            zone as city,\n", "            facility_id,\n", "            sister_store_facility_id as s_fac\n", "            \n", "            from\n", "            \n", "            supply_etls.e3_new_darkstores\n", "            where final_ob_date >= current_date - interval '30' day\n", "            and sister_store_facility_id <>0\n", "\"\"\"\n", "sis_df = pd.read_sql_query(sis, trino)\n", "\n", "sis_store_list = tuple(list(sis_df[\"facility_id\"].unique()) + ([-1, -2]))\n", "len(sis_store_list)"]}, {"cell_type": "markdown", "id": "71f460b8-b6be-4675-9015-0f53bebb33a0", "metadata": {"tags": []}, "source": ["### BE item conrti"]}, {"cell_type": "code", "execution_count": null, "id": "4210ea06-9a7d-4f8a-a90e-d295f70fb392", "metadata": {}, "outputs": [], "source": ["contri_base = daywise_df[[\"city\", \"be_facility_id\", \"facility_id\", \"item_id\", \"quantity\"]].copy()\n", "\n", "be_contri = daywise_df.groupby([\"be_facility_id\", \"item_id\"]).agg({\"quantity\": \"sum\"}).reset_index()\n", "\n", "be_agri = (\n", "    be_contri.groupby([\"be_facility_id\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"quantity\": \"tot_quan\"})\n", ")\n", "\n", "be_contri = be_contri.merge(be_agri, on=[\"be_facility_id\"], how=\"left\")\n", "be_contri[\"be_share\"] = be_contri[\"quantity\"] / be_contri[\"tot_quan\"]\n", "be_contri.drop(columns={\"quantity\", \"tot_quan\"}, inplace=True)\n", "be_contri.head(10)"]}, {"cell_type": "markdown", "id": "88c81556-5b3c-46ef-92c5-2a576f6eba46", "metadata": {"tags": []}, "source": ["### City item conrti"]}, {"cell_type": "code", "execution_count": null, "id": "22898f3f-72df-4393-abe4-20488fd30b63", "metadata": {"tags": []}, "outputs": [], "source": ["city_contri = daywise_df.groupby([\"city\", \"item_id\"]).agg({\"quantity\": \"sum\"}).reset_index()\n", "\n", "city_agri = (\n", "    city_contri.groupby([\"city\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"quantity\": \"tot_quan\"})\n", ")\n", "\n", "city_contri = city_contri.merge(city_agri, on=[\"city\"], how=\"left\")\n", "city_contri[\"city_share\"] = city_contri[\"quantity\"] / city_contri[\"tot_quan\"]\n", "city_contri.drop(columns={\"quantity\", \"tot_quan\"}, inplace=True)\n", "city_contri.head(7)"]}, {"cell_type": "markdown", "id": "e8b99ce8-dbcb-4d20-905e-a1b250418b53", "metadata": {"tags": []}, "source": ["### Sister item conrti"]}, {"cell_type": "code", "execution_count": null, "id": "a67e55b1-ee94-468c-9b5c-c97e0fd71610", "metadata": {}, "outputs": [], "source": ["store_contri = (\n", "    daywise_df[daywise_df[\"facility_id\"].isin(sis_store_list)]\n", "    .groupby([\"facility_id\", \"item_id\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"facility_id\": \"s_fac\"})\n", ")\n", "\n", "store_agri = (\n", "    store_contri.groupby([\"s_fac\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"quantity\": \"tot_quan\"})\n", ")\n", "\n", "store_contri = store_contri.merge(store_agri, on=[\"s_fac\"], how=\"left\")\n", "store_contri[\"fac_share\"] = store_contri[\"quantity\"] / store_contri[\"tot_quan\"]\n", "store_contri = store_contri.merge(sis_df[[\"s_fac\", \"facility_id\"]], on=[\"s_fac\"], how=\"left\")\n", "store_contri = store_contri.dropna()\n", "store_contri.drop(columns={\"s_fac\", \"quantity\", \"tot_quan\"}, inplace=True)\n", "store_contri.head(7)"]}, {"cell_type": "code", "execution_count": null, "id": "ddea8ffd-7e3b-421c-9008-6037bf9b7dae", "metadata": {}, "outputs": [], "source": ["new_df = new_store_df.merge(be_contri, on=[\"be_facility_id\", \"item_id\"], how=\"left\")\n", "new_df = new_df.merge(city_contri, on=[\"city\", \"item_id\"], how=\"left\")\n", "new_df = new_df.merge(store_contri, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "new_df = new_df.fillna(0)\n", "new_df[\"final_share\"] = np.where(\n", "    new_df[\"fac_share\"] != 0,\n", "    new_df[\"fac_share\"],\n", "    np.where(new_df[\"city_share\"] != 0, new_df[\"city_share\"], new_df[\"be_share\"]),\n", ")\n", "new_df[\"extrapolated_cpd\"] = np.ceil(new_df[\"final_cpd\"] * new_df[\"final_share\"]).clip(1, 1000)\n", "new_df[\"extrapolated_cpd_v2\"] = np.ceil(new_df[\"final_cpd\"] * new_df[\"final_share\"]).clip(1, 1000)\n", "new_df[\"extrapolated_cpd_v3\"] = np.ceil(new_df[\"final_cpd\"] * new_df[\"final_share\"]).clip(1, 1000)\n", "\n", "new_df.tail()"]}, {"cell_type": "code", "execution_count": null, "id": "9683c8cb-85d1-446d-98f2-95c0d44f6c03", "metadata": {"tags": []}, "outputs": [], "source": ["new_store_df = (\n", "    new_df[new_df[\"age\"] <= 9][\n", "        [\n", "            \"city\",\n", "            \"outlet_id\",\n", "            \"age\",\n", "            \"be_facility_id\",\n", "            \"fdate\",\n", "            \"item_id\",\n", "            \"facility_id\",\n", "            \"extrapolated_cpd\",\n", "            \"extrapolated_cpd_v2\",\n", "            \"extrapolated_cpd_v3\",\n", "        ]\n", "    ]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "new_store_df[[\"extrapolated_cpd\", \"extrapolated_cpd_v2\", \"extrapolated_cpd_v3\"]] = np.ceil(\n", "    new_store_df[[\"extrapolated_cpd\", \"extrapolated_cpd_v2\", \"extrapolated_cpd_v3\"]]\n", ").astype(int)\n", "\n", "# new_store_df[\"slotb_transfer_qty\"] = np.where(\n", "#     new_store_df[\"tot_transfer_qty\"] - new_store_df[\"slota_transfer_qty\"] < 0,\n", "#     0,\n", "#     new_store_df[\"tot_transfer_qty\"] - new_store_df[\"slota_transfer_qty\"],\n", "# )\n", "\n", "new_store_df[\"stype_\"] = \"top\"\n", "\n", "new_store_df[\"ftype_\"] = \"medium\"\n", "\n", "new_store_df[\"ds_f\"] = 0\n", "\n", "new_store_df = (\n", "    new_store_df[\n", "        [\n", "            \"city\",\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"be_facility_id\",\n", "            \"fdate\",\n", "            \"ftype_\",\n", "            \"stype_\",\n", "            \"extrapolated_cpd\",\n", "            \"extrapolated_cpd_v2\",\n", "            \"extrapolated_cpd_v3\",\n", "            \"age\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "new_store_df[\"fdate\"] = pd.to_datetime(new_store_df[\"fdate\"])"]}, {"cell_type": "code", "execution_count": null, "id": "074dc35b-e4b1-4c02-b054-07158ad21a48", "metadata": {}, "outputs": [], "source": ["new_store_df.sort_values(\"age\", ascending=False)"]}, {"cell_type": "markdown", "id": "5ee8684e-7b08-45b1-aa2a-e80f4837f1f8", "metadata": {}, "source": ["## Final DataFrame"]}, {"cell_type": "code", "execution_count": null, "id": "4df558eb-66cb-44a8-a50c-df239f1d248d", "metadata": {}, "outputs": [], "source": ["final_df = pd.merge(\n", "    assortment_ordering_df,\n", "    forecast_df.drop(columns={\"city\", \"stype_\", \"be_facility_id\"}),\n", "    on=[\"facility_id\", \"item_id\", \"fdate\"],\n", "    how=\"left\",\n", ")\n", "final_df = final_df.merge(new_store_df[[\"facility_id\", \"age\"]], on=[\"facility_id\"], how=\"left\")\n", "final_df[\"age\"] = final_df[\"age\"].fillna(10)\n", "final_df = final_df[final_df[\"age\"] > 9]\n", "final_df = pd.concat([final_df, new_store_df])\n", "final_df[\"extrapolated_cpd\"] = final_df[\"extrapolated_cpd\"].fillna(5).astype(int)\n", "\n", "final_df[\"extrapolated_cpd_v2\"] = final_df[\"extrapolated_cpd_v2\"].fillna(5).astype(int)\n", "\n", "final_df[\"extrapolated_cpd_v3\"] = final_df[\"extrapolated_cpd_v3\"].fillna(5).astype(int)\n", "\n", "# final_df[\"factor\"] = final_df[\"factor\"].fillna(1)\n", "\n", "# final_df[\"city_f\"] = final_df[\"city_f\"].fillna(1)\n", "\n", "final_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "958ce3aa-f49c-4a40-a0dd-3584519c1306", "metadata": {}, "outputs": [], "source": ["final_df = final_df[\n", "    [\n", "        \"city\",\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"be_facility_id\",\n", "        \"fdate\",\n", "        \"ftype_\",\n", "        \"stype_\",\n", "        \"extrapolated_cpd\",\n", "        \"extrapolated_cpd_v2\",\n", "        \"extrapolated_cpd_v3\",\n", "        \"age\",\n", "    ]\n", "]\n", "final_df = pd.concat([final_df, new_store_df]).drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "74a66d50-18ae-4ad3-91f5-a8d044f920f0", "metadata": {}, "outputs": [], "source": ["final_df.head()"]}, {"cell_type": "markdown", "id": "9852ed79-0932-4bbf-abf5-feee64d3c59d", "metadata": {}, "source": ["## Summary for Indent Check & Correction"]}, {"cell_type": "code", "execution_count": null, "id": "d5657bf3-5ba8-4aa5-b05c-783821e623f7", "metadata": {}, "outputs": [], "source": ["summary_df = final_df.copy()"]}, {"cell_type": "markdown", "id": "5ccc4954-cc82-42b2-b6e3-577660500b6e", "metadata": {}, "source": ["#### Item Store Backend Name Data"]}, {"cell_type": "code", "execution_count": null, "id": "39e6db4b-695b-44aa-a277-fe9c957945fc", "metadata": {}, "outputs": [], "source": ["trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "\n", "query = f\"\"\"\n", "    SELECT facility_id AS facility_id, outlet_name AS facility_name\n", "    FROM po.physical_facility_outlet_mapping\n", "    WHERE lake_active_record\n", "    and ars_active = 1\n", "    GROUP BY 1,2\n", "\"\"\"\n", "store_df = read_sql_query(query, trino)\n", "\n", "query = f\"\"\"\n", "    SELECT facility_id AS be_facility_id, outlet_name AS be_facility_name\n", "    FROM po.physical_facility_outlet_mapping\n", "    WHERE lake_active_record\n", "    and ars_active = 1\n", "    GROUP BY 1,2\n", "\"\"\"\n", "backend_df = read_sql_query(query, trino)"]}, {"cell_type": "markdown", "id": "0d3c4dd4-51fe-4015-964e-08f55df62713", "metadata": {}, "source": ["#### Past Sales Data"]}, {"cell_type": "code", "execution_count": null, "id": "a30db349-cc39-44ec-82ea-b7953586345b", "metadata": {}, "outputs": [], "source": ["l1_date = (current_time - timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "l7_date = (current_time - timedelta(days=5)).strftime(\"%Y-%m-%d\")\n", "\n", "l7_item_fac_sales = (\n", "    sales_pre_df[sales_pre_df[\"date_\"] == l7_date]\n", "    .groupby([\"facility_id\", \"item_id\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "l7_item_fac_df = (\n", "    assortment_df[\n", "        (pd.to_datetime(assortment_df[\"date_\"]) == l7_date) & (assortment_df[\"hour_\"] < 23)\n", "    ]\n", "    .groupby([\"facility_id\", \"item_id\"])\n", "    .agg({\"is_available\": \"sum\", \"hour_\": \"count\"})\n", "    .reset_index()\n", ")\n", "\n", "l7_item_fac_df[\"l7_avail\"] = l7_item_fac_df[\"is_available\"] / l7_item_fac_df[\"hour_\"]\n", "\n", "l7_item_fac_df = l7_item_fac_df.merge(l7_item_fac_sales, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "\n", "l7_item_fac_df = l7_item_fac_df.rename(columns={\"quantity\": \"l7_sales\"}).drop(\n", "    columns={\"is_available\", \"hour_\"}\n", ")\n", "\n", "l1_item_fac_sales = (\n", "    sales_pre_df[sales_pre_df[\"date_\"] == l1_date]\n", "    .groupby([\"facility_id\", \"item_id\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "l1_item_fac_df = (\n", "    assortment_df[\n", "        (pd.to_datetime(assortment_df[\"date_\"]) == l1_date) & (assortment_df[\"hour_\"] < 23)\n", "    ]\n", "    .groupby([\"facility_id\", \"item_id\"])\n", "    .agg({\"is_available\": \"sum\", \"hour_\": \"count\"})\n", "    .reset_index()\n", ")\n", "\n", "l1_item_fac_df[\"l1_avail\"] = l1_item_fac_df[\"is_available\"] / l1_item_fac_df[\"hour_\"]\n", "\n", "l1_item_fac_df = l1_item_fac_df.merge(l1_item_fac_sales, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "\n", "l1_item_fac_df = l1_item_fac_df.rename(columns={\"quantity\": \"l1_sales\"}).drop(\n", "    columns={\"is_available\", \"hour_\"}\n", ")\n", "\n", "summary_df = summary_df.merge(l7_item_fac_df, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "\n", "summary_df = summary_df.merge(l1_item_fac_df, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "\n", "summary_df = summary_df.merge(store_df, on=[\"facility_id\"], how=\"left\")\n", "\n", "summary_df = summary_df.merge(backend_df, on=[\"be_facility_id\"], how=\"left\")\n", "\n", "summary_df = summary_df.merge(item_df, on=[\"item_id\"], how=\"left\")\n", "\n", "summary_df = summary_df[\n", "    [\n", "        \"city\",\n", "        \"be_facility_id\",\n", "        \"be_facility_name\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"ftype_\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"stype_\",\n", "        \"fdate\",\n", "        \"l7_sales\",\n", "        \"l7_avail\",\n", "        \"l1_sales\",\n", "        \"l1_avail\",\n", "        # \"factor\",\n", "        # \"manual_cpd\",\n", "        \"extrapolated_cpd\",\n", "        \"extrapolated_cpd_v2\",\n", "        \"extrapolated_cpd_v3\",\n", "        \"age\",\n", "    ]\n", "].fillna(0)\n", "\n", "summary_df[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))\n", "\n", "summary_df[\"fdate\"] = pd.to_datetime(today_date) + timedelta(days=2)\n", "summary_df = summary_df[summary_df.be_facility_id > 0]\n", "summary_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8b73827f-00f8-4aa7-b049-14472454bfda", "metadata": {"tags": []}, "outputs": [], "source": ["try:\n", "    pb.to_sheets(\n", "        summary_df,\n", "        sheetid=\"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\",\n", "        sheetname=\"summary_raw\",\n", "    )\n", "except:\n", "    summary_df.to_csv(f\"{summary_df.fdate.max()} ordering.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "43f83de3-ba9b-4301-a827-f5d9cfa86813", "metadata": {}, "outputs": [], "source": ["del [l7_item_fac_df, l7_item_fac_sales, l1_item_fac_df, l1_item_fac_sales]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "df5fffad-4dc0-4ce6-91eb-ef3cacf35ac0", "metadata": {}, "source": ["## Logs for summary"]}, {"cell_type": "code", "execution_count": null, "id": "06d6d703-327d-4237-86ba-4a6fe94eccef", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"milk_extrapolated_cpd_data\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"City name\"},\n", "        {\"name\": \"be_facility_id\", \"type\": \"bigint\", \"description\": \"Backend facility ID\"},\n", "        {\"name\": \"be_facility_name\", \"type\": \"varchar\", \"description\": \"Backend facility name\"},\n", "        {\"name\": \"facility_id\", \"type\": \"bigint\", \"description\": \"Storefront facility ID\"},\n", "        {\"name\": \"facility_name\", \"type\": \"varchar\", \"description\": \"Storefront facility name\"},\n", "        {\"name\": \"ftype_\", \"type\": \"varchar\", \"description\": \"Facility type\"},\n", "        {\"name\": \"item_id\", \"type\": \"bigint\", \"description\": \"Item ID\"},\n", "        {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"Item name\"},\n", "        {\"name\": \"stype_\", \"type\": \"varchar\", \"description\": \"SKU type\"},\n", "        {\"name\": \"fdate\", \"type\": \"TIMESTAMP(6)\", \"description\": \"Forecast date\"},\n", "        {\"name\": \"l7_sales\", \"type\": \"double\", \"description\": \"Last 7 days sales\"},\n", "        {\"name\": \"l7_avail\", \"type\": \"double\", \"description\": \"Last 7 days availability\"},\n", "        {\"name\": \"l1_sales\", \"type\": \"double\", \"description\": \"Last 1 day sales\"},\n", "        {\"name\": \"l1_avail\", \"type\": \"double\", \"description\": \"Last 1 day availability\"},\n", "        {\"name\": \"extrapolated_cpd\", \"type\": \"double\", \"description\": \"Extrapolated CPD\"},\n", "        {\n", "            \"name\": \"extrapolated_cpd_v2\",\n", "            \"type\": \"double\",\n", "            \"description\": \"Extrapolated CPD version 2\",\n", "        },\n", "        {\n", "            \"name\": \"extrapolated_cpd_v3\",\n", "            \"type\": \"double\",\n", "            \"description\": \"Extrapolated CPD version 3\",\n", "        },\n", "        {\"name\": \"age\", \"type\": \"double\", \"description\": \"Age of the SKU in days (10 means >=10)\"},\n", "        {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(6)\", \"description\": \"Last update timestamp\"},\n", "    ],\n", "    \"primary_key\": [\"city\", \"facility_id\", \"item_id\", \"fdate\"],\n", "    \"partition_key\": [\"fdate\"],\n", "    \"incremental_key\": \"fdate\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"Extrapolated CPD data for milk SKUs at store level\",\n", "}\n", "\n", "pb.to_trino(summary_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "ec12b090-b409-4445-bfbc-0632af1967f7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "e8e8b171-9419-47f9-8aff-2ba123dfe254", "metadata": {}, "source": ["## Working Steps Logs"]}, {"cell_type": "code", "execution_count": null, "id": "01061e9f-88f2-4e4c-9836-5b2bcb777061", "metadata": {}, "outputs": [], "source": ["working_steps_df = daywise_df.copy()\n", "working_steps_df = working_steps_df.merge(\n", "    increment_df_calc, on=[\"facility_id\", \"fdow\", \"dow\"], how=\"left\"\n", ")\n", "working_steps_df[\"bf\"] = working_steps_df[\"bf\"].fillna(1)\n", "working_steps_df = working_steps_df[\n", "    [\n", "        \"facility_id\",\n", "        \"ftype_\",\n", "        \"item_id\",\n", "        \"stype_\",\n", "        \"fdate\",\n", "        \"date_\",\n", "        \"wt_avail\",\n", "        \"bf\",\n", "        \"weights\",\n", "        \"ext_qty_li\",\n", "        \"ext_qty_exp\",\n", "        \"ext_qty_para\",\n", "        \"ext_qty_hybrid\",\n", "        \"ext_qty_hybrid_v2\",\n", "        \"ext_qty_hybrid_v3\",\n", "    ]\n", "]\n", "\n", "working_steps_df = working_steps_df.rename(\n", "    columns={\n", "        \"ext_qty_hybrid\": \"ext_qty\",\n", "        \"ext_qty_hybrid_v2\": \"ext_qty_v2\",\n", "        \"ext_qty_hybrid_v3\": \"ext_qty_v3\",\n", "    }\n", ")\n", "\n", "working_steps_df[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))\n", "\n", "working_steps_df.head(1)"]}, {"cell_type": "markdown", "id": "67d1513c-563e-4694-90d5-7e45315f8326", "metadata": {}, "source": ["## <PERSON> <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "id": "b2b430fb-0e21-4284-8830-e8766a8df2c0", "metadata": {}, "outputs": [], "source": ["trino_data_df = working_steps_df.copy()\n", "\n", "trino_data_df[\"date_ist\"] = pd.to_datetime(\n", "    pd.to_datetime(trino_data_df[\"updated_at\"]).dt.strftime(\"%Y-%m-%d\")\n", ")\n", "\n", "trino_data_df = trino_data_df[\n", "    [\n", "        \"facility_id\",\n", "        \"ftype_\",\n", "        \"item_id\",\n", "        \"stype_\",\n", "        \"fdate\",\n", "        \"date_\",\n", "        \"wt_avail\",\n", "        \"weights\",\n", "        \"bf\",\n", "        \"ext_qty_li\",\n", "        \"ext_qty_exp\",\n", "        \"ext_qty_para\",\n", "        \"ext_qty\",\n", "        \"ext_qty_v2\",\n", "        \"ext_qty_v3\",\n", "        \"date_ist\",\n", "        \"updated_at\",\n", "    ]\n", "]\n", "\n", "trino_data_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "d73ab67e-00d2-4781-abe8-b22ac0e9af70", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"facility id\"},\n", "    {\"name\": \"ftype_\", \"type\": \"VARCHAR\", \"description\": \"facility categorization\"},\n", "    {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"item id\"},\n", "    {\"name\": \"stype_\", \"type\": \"VARCHAR\", \"description\": \"SKU categorization\"},\n", "    {\"name\": \"fdate\", \"type\": \"DATE\", \"description\": \"forecast date\"},\n", "    {\"name\": \"date_\", \"type\": \"DATE\", \"description\": \"dates for forecast\"},\n", "    {\"name\": \"wt_avail\", \"type\": \"DOUBLE\", \"description\": \"weighted availability\"},\n", "    {\"name\": \"weights\", \"type\": \"DOUBLE\", \"description\": \"day weights\"},\n", "    {\"name\": \"bf\", \"type\": \"DOUBLE\", \"description\": \"bump factor for fdow/dow normalisation\"},\n", "    {\"name\": \"ext_qty_li\", \"type\": \"DOUBLE\", \"description\": \"Extrapolated CPD\"},\n", "    {\"name\": \"ext_qty_exp\", \"type\": \"DOUBLE\", \"description\": \"Extrapolated CPD\"},\n", "    {\"name\": \"ext_qty_para\", \"type\": \"DOUBLE\", \"description\": \"Extrapolated CPD\"},\n", "    {\"name\": \"ext_qty\", \"type\": \"DOUBLE\", \"description\": \"Extrapolated CPD\"},\n", "    {\"name\": \"ext_qty_v2\", \"type\": \"DOUBLE\", \"description\": \"Extrapolated CPD\"},\n", "    {\"name\": \"ext_qty_v3\", \"type\": \"DOUBLE\", \"description\": \"Extrapolated CPD\"},\n", "    {\"name\": \"date_ist\", \"type\": \"DATE\", \"description\": \"updated_at filter date\"},\n", "    {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(6)\", \"description\": \"date/time of run\"},\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"milk_ordering_working_steps_logs\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"updated_at\", \"facility_id\", \"item_id\"],\n", "    \"partition_key\": [\"date_ist\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"append\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains milk forecasting working steps logs\",\n", "}\n", "\n", "pb.to_trino(trino_data_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "5468e8b5-aa40-42b7-8b4b-b8856e9ddab4", "metadata": {}, "outputs": [], "source": ["del [trino_data_df]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "d7535fd7-c2f7-46ab-9dcc-83b799843e53", "metadata": {}, "source": ["### For Tat = 1"]}, {"cell_type": "code", "execution_count": null, "id": "ea90e1fa-0625-43bf-82fd-323dcb37f655", "metadata": {}, "outputs": [], "source": ["pre_working_steps_df = pre_md_daywise_df.copy()\n", "\n", "pre_working_steps_df = pre_working_steps_df[\n", "    [\n", "        \"facility_id\",\n", "        \"ftype_\",\n", "        \"item_id\",\n", "        \"stype_\",\n", "        \"fdate\",\n", "        \"date_\",\n", "        \"quantity\",\n", "        \"wt_avail\",\n", "        \"weights\",\n", "        \"ext_qty_li\",\n", "        \"ext_qty_exp\",\n", "        \"ext_qty_para\",\n", "        \"ext_qty_hybrid\",\n", "        \"ext_qty_hybrid_v2\",\n", "        \"ext_qty_hybrid_v3\",\n", "    ]\n", "]\n", "\n", "pre_working_steps_df = pre_working_steps_df.rename(\n", "    columns={\n", "        \"ext_qty_hybrid\": \"ext_qty\",\n", "        \"ext_qty_hybrid_v2\": \"ext_qty_v2\",\n", "        \"ext_qty_hybrid_v3\": \"ext_qty_v3\",\n", "    }\n", ")\n", "\n", "pre_working_steps_df[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))\n", "\n", "pre_working_steps_df.head(1)"]}, {"cell_type": "markdown", "id": "5104aa84-de4f-4ac9-876b-9f086b482721", "metadata": {}, "source": ["### To trino"]}, {"cell_type": "code", "execution_count": null, "id": "0c2ed5b4-c1dd-4bec-8c92-4653a54c44c7", "metadata": {}, "outputs": [], "source": ["pre_trino_data_df = pre_working_steps_df.copy()\n", "\n", "pre_trino_data_df[\"date_ist\"] = pd.to_datetime(\n", "    pd.to_datetime(pre_trino_data_df[\"updated_at\"]).dt.strftime(\"%Y-%m-%d\")\n", ")\n", "\n", "pre_trino_data_df = pre_trino_data_df[\n", "    [\n", "        \"facility_id\",\n", "        \"ftype_\",\n", "        \"item_id\",\n", "        \"stype_\",\n", "        \"fdate\",\n", "        \"date_\",\n", "        \"quantity\",\n", "        \"wt_avail\",\n", "        \"weights\",\n", "        \"ext_qty_li\",\n", "        \"ext_qty_exp\",\n", "        \"ext_qty_para\",\n", "        \"ext_qty\",\n", "        \"ext_qty_v2\",\n", "        \"ext_qty_v3\",\n", "        \"date_ist\",\n", "        \"updated_at\",\n", "    ]\n", "]\n", "\n", "pre_trino_data_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "3938b776-1e31-4afd-b377-abfd2703486d", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"facility id\"},\n", "    {\"name\": \"ftype_\", \"type\": \"VARCHAR\", \"description\": \"facility categorization\"},\n", "    {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"item id\"},\n", "    {\"name\": \"stype_\", \"type\": \"VARCHAR\", \"description\": \"SKU categorization\"},\n", "    {\"name\": \"fdate\", \"type\": \"DATE\", \"description\": \"forecast date\"},\n", "    {\"name\": \"date_\", \"type\": \"DATE\", \"description\": \"dates for forecast\"},\n", "    {\"name\": \"quantity\", \"type\": \"DOUBLE\", \"description\": \"sales quantity\"},\n", "    {\"name\": \"wt_avail\", \"type\": \"DOUBLE\", \"description\": \"weighted availability\"},\n", "    {\"name\": \"weights\", \"type\": \"DOUBLE\", \"description\": \"day weights\"},\n", "    {\"name\": \"ext_qty_li\", \"type\": \"DOUBLE\", \"description\": \"Extrapolated CPD\"},\n", "    {\"name\": \"ext_qty_exp\", \"type\": \"DOUBLE\", \"description\": \"Extrapolated CPD\"},\n", "    {\"name\": \"ext_qty_para\", \"type\": \"DOUBLE\", \"description\": \"Extrapolated CPD\"},\n", "    {\"name\": \"ext_qty\", \"type\": \"DOUBLE\", \"description\": \"Extrapolated CPD\"},\n", "    {\"name\": \"ext_qty_v2\", \"type\": \"DOUBLE\", \"description\": \"Extrapolated CPD\"},\n", "    {\"name\": \"ext_qty_v3\", \"type\": \"DOUBLE\", \"description\": \"Extrapolated CPD\"},\n", "    {\"name\": \"date_ist\", \"type\": \"DATE\", \"description\": \"updated_at filter date\"},\n", "    {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(6)\", \"description\": \"date/time of run\"},\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"pre_mother_dairy_ordering_working_steps_logs\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"updated_at\", \"facility_id\", \"item_id\"],\n", "    \"partition_key\": [\"date_ist\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"append\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains milk forecasting working steps logs\",\n", "}\n", "\n", "pb.to_trino(pre_trino_data_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "e7ff1050-cbf4-4044-bbb2-16aa2592fa6d", "metadata": {}, "outputs": [], "source": ["del [pre_trino_data_df]\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "0cbab927-4774-40f2-8475-d08efce3593b", "metadata": {}, "source": ["## For Mother Dairy tat = 2"]}, {"cell_type": "code", "execution_count": null, "id": "f4eeb3eb-ee8d-4d57-addc-f7a7c1004622", "metadata": {}, "outputs": [], "source": ["post_working_steps_df = post_md_daywise_df.copy()\n", "\n", "post_working_steps_df = post_working_steps_df[\n", "    [\n", "        \"facility_id\",\n", "        \"ftype_\",\n", "        \"item_id\",\n", "        \"stype_\",\n", "        \"fdate\",\n", "        \"date_\",\n", "        \"quantity\",\n", "        \"wt_avail\",\n", "        \"weights\",\n", "        \"ext_qty_li\",\n", "        \"ext_qty_exp\",\n", "        \"ext_qty_para\",\n", "        \"ext_qty_hybrid\",\n", "        \"ext_qty_hybrid_v2\",\n", "        \"ext_qty_hybrid_v3\",\n", "    ]\n", "]\n", "\n", "post_working_steps_df = post_working_steps_df.rename(\n", "    columns={\n", "        \"ext_qty_hybrid\": \"ext_qty\",\n", "        \"ext_qty_hybrid_v2\": \"ext_qty_v2\",\n", "        \"ext_qty_hybrid_v3\": \"ext_qty_v3\",\n", "    }\n", ")\n", "\n", "post_working_steps_df[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))\n", "\n", "post_working_steps_df.head(1)"]}, {"cell_type": "markdown", "id": "28da765f-4f2d-451e-8825-4f86f12f4ff2", "metadata": {}, "source": ["### To trino"]}, {"cell_type": "code", "execution_count": null, "id": "01b52418-d9d3-4c37-a1a7-14eecf6d9e1e", "metadata": {}, "outputs": [], "source": ["post_trino_data_df = post_working_steps_df.copy()\n", "\n", "post_trino_data_df[\"date_ist\"] = pd.to_datetime(\n", "    pd.to_datetime(post_trino_data_df[\"updated_at\"]).dt.strftime(\"%Y-%m-%d\")\n", ")\n", "\n", "post_trino_data_df = post_trino_data_df[\n", "    [\n", "        \"facility_id\",\n", "        \"ftype_\",\n", "        \"item_id\",\n", "        \"stype_\",\n", "        \"fdate\",\n", "        \"date_\",\n", "        \"quantity\",\n", "        \"wt_avail\",\n", "        \"weights\",\n", "        \"ext_qty_li\",\n", "        \"ext_qty_exp\",\n", "        \"ext_qty_para\",\n", "        \"ext_qty\",\n", "        \"ext_qty_v2\",\n", "        \"ext_qty_v3\",\n", "        \"date_ist\",\n", "        \"updated_at\",\n", "    ]\n", "]\n", "\n", "post_trino_data_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "f210b9a8-1a2f-4ec3-aa7e-74df5be25cef", "metadata": {"tags": []}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"facility id\"},\n", "    {\"name\": \"ftype_\", \"type\": \"VARCHAR\", \"description\": \"facility categorization\"},\n", "    {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"item id\"},\n", "    {\"name\": \"stype_\", \"type\": \"VARCHAR\", \"description\": \"SKU categorization\"},\n", "    {\"name\": \"fdate\", \"type\": \"DATE\", \"description\": \"forecast date\"},\n", "    {\"name\": \"date_\", \"type\": \"DATE\", \"description\": \"dates for forecast\"},\n", "    {\"name\": \"quantity\", \"type\": \"DOUBLE\", \"description\": \"sales quantity\"},\n", "    {\"name\": \"wt_avail\", \"type\": \"DOUBLE\", \"description\": \"weighted availability\"},\n", "    {\"name\": \"weights\", \"type\": \"DOUBLE\", \"description\": \"day weights\"},\n", "    {\"name\": \"ext_qty_li\", \"type\": \"DOUBLE\", \"description\": \"Extrapolated CPD\"},\n", "    {\"name\": \"ext_qty_exp\", \"type\": \"DOUBLE\", \"description\": \"Extrapolated CPD\"},\n", "    {\"name\": \"ext_qty_para\", \"type\": \"DOUBLE\", \"description\": \"Extrapolated CPD\"},\n", "    {\"name\": \"ext_qty\", \"type\": \"DOUBLE\", \"description\": \"Extrapolated CPD\"},\n", "    {\"name\": \"ext_qty_v2\", \"type\": \"DOUBLE\", \"description\": \"Extrapolated CPD\"},\n", "    {\"name\": \"ext_qty_v3\", \"type\": \"DOUBLE\", \"description\": \"Extrapolated CPD\"},\n", "    {\"name\": \"date_ist\", \"type\": \"DATE\", \"description\": \"updated_at filter date\"},\n", "    {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(6)\", \"description\": \"date/time of run\"},\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"post_mother_dairy_ordering_working_steps_logs\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"updated_at\", \"facility_id\", \"item_id\"],\n", "    \"partition_key\": [\"date_ist\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"append\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains milk forecasting working steps logs\",\n", "}\n", "\n", "pb.to_trino(post_trino_data_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "0b327139-28ee-4f48-8fa7-822c108a614e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}