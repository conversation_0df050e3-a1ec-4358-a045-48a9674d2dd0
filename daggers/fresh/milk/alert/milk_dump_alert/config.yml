alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: milk_dump_alert
dag_type: alert
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: fresh
notebook:
  parameters: null
  retries: 1
owner:
  email: <EMAIL>
  slack_id: U06T927GCSW
path: fresh/milk/alert/milk_dump_alert
paused: false
pool: fresh_pool
project_name: milk
schedule:
  end_date: '2025-08-22T00:00:00'
  interval: 37 2,5 * * *
  start_date: '2025-05-17T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
