{"cells": [{"cell_type": "code", "execution_count": null, "id": "a390cff5-0f04-4200-9e12-e17e2bd2e9c3", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import os\n", "import time\n", "import gc\n", "import requests\n", "\n", "import calendar\n", "from datetime import datetime as dt\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.options.mode.chained_assignment = None  # default='warn'\n", "pd.set_option(\"display.max_columns\", 500)"]}, {"cell_type": "code", "execution_count": null, "id": "dd6d38a3-57a9-41cb-aec5-369491d04b92", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)\n", "\n", "pd.options.mode.chained_assignment = None  # default='warn'"]}, {"cell_type": "code", "execution_count": null, "id": "a7aef57c-570e-48a2-a0b4-65edfaaccb17", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(\n", "            f\"Read attempt: {attempt}... \\nRun started at {datetime.now() + timed<PERSON>ta(hours = 5.5)}\"\n", "        )\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "175ae7af-185f-4f9d-9a20-e773c70ef019", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pytz\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")\n", "\n", "current_time = datetime.now(IST)\n", "current_hour = pd.to_datetime(current_time).hour\n", "\n", "start_date = pd.to_datetime(current_time - timedelta(days=14)).strftime(\"%Y-%m-%d\")\n", "l1_date = pd.to_datetime(current_time - timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "l8_date = pd.to_datetime(current_time - timedelta(days=8)).strftime(\"%Y-%m-%d\")\n", "l10_date = pd.to_datetime(current_time - timedelta(days=10)).strftime(\"%Y-%m-%d\")\n", "l45_date = pd.to_datetime(current_time - timedelta(days=45)).strftime(\"%Y-%m-%d\")\n", "today_date = pd.to_datetime(current_time).strftime(\"%Y-%m-%d\")\n", "\n", "current_week = pd.to_datetime(today_date).isocalendar().week\n", "\n", "start_date, today_date, l45_date, current_hour, current_week"]}, {"cell_type": "code", "execution_count": null, "id": "ccbc8016-5813-402a-a702-f05b3d095fef", "metadata": {}, "outputs": [], "source": ["x = pd.date_range(start=start_date, end=today_date)\n", "date_df = pd.DataFrame({\"date_\": x, \"date_flag\": 1})"]}, {"cell_type": "markdown", "id": "ae33904e-dc70-4f41-96a4-dfb8b57a6e49", "metadata": {}, "source": ["## Milk Item Base Query ##"]}, {"cell_type": "code", "execution_count": null, "id": "c28b04e4-ddfb-4eb3-b0c0-c3d2e6c62416", "metadata": {}, "outputs": [], "source": ["base_query = \"\"\"\n", "    WITH active_stores AS (\n", "        SELECT DISTINCT facility_id, outlet_id \n", "        FROM po.physical_facility_outlet_mapping pfom  \n", "        WHERE ars_active = 1 AND active = 1 AND is_primary = 1 \n", "        AND outlet_id IN (SELECT DISTINCT id FROM retail.console_outlet WHERE business_type_id = 7 AND active = 1 AND lake_active_record) \n", "        AND outlet_id IN (SELECT DISTINCT outlet_id FROM po.bulk_facility_outlet_mapping WHERE active = True AND lake_active_record) \n", "    ), \n", "    \n", "    milk_assortment AS (\n", "        SELECT DISTINCT cl.name AS city, a.facility_id, outlet_id, item_id\n", "        FROM rpc.product_facility_master_assortment a\n", "        JOIN active_stores b ON a.facility_id = b.facility_id\n", "        JOIN retail.console_outlet co ON co.facility_id = a.facility_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "        LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id AND cl.lake_active_record\n", "        WHERE item_id IN (SELECT DISTINCT item_id FROM rpc.item_category_details WHERE l2_id = 1185) \n", "        AND a.active = 1 AND a.master_assortment_substate_id = 1\n", "    ),\n", "    \n", "    be_mapping AS (\n", "        SELECT item_id, facility_id, be_facility_id, be_outlet_id, \n", "        CASE \n", "            WHEN cloud_store_id IS NULL THEN be_outlet_id \n", "            ELSE cloud_store_id \n", "        END AS be_inv_outlet_id\n", "        FROM (\n", "            SELECT DISTINCT tm.item_id, cf.facility_id, cb.facility_id AS be_facility_id, CAST(tm.tag_value AS int) AS be_outlet_id\n", "            FROM rpc.item_outlet_tag_mapping tm\n", "            LEFT JOIN retail.console_outlet cb ON cb.id = CAST(tm.tag_value AS int) AND cb.active = 1 AND cb.lake_active_record\n", "            JOIN retail.console_outlet cf ON cf.id = outlet_id AND cf.active = 1 AND cf.lake_active_record\n", "            WHERE tag_type_id IN (8) AND tm.active = 1\n", "        ) a\n", "        LEFT JOIN (\n", "            SELECT DISTINCT warehouse_id, cloud_store_id \n", "            FROM retail.warehouse_outlet_mapping\n", "            WHERE lake_active_record\n", "        ) wom on wom.warehouse_id = a.be_outlet_id\n", "    ),\n", "    \n", "    final AS (\n", "        SELECT city, a.facility_id, a.outlet_id, a.item_id, be_facility_id, be_outlet_id, be_inv_outlet_id\n", "        FROM milk_assortment a\n", "        LEFT JOIN be_mapping b ON a.item_id = b.item_id AND a.facility_id = b.facility_id\n", "    )\n", "    \n", "    SELECT * FROM final\n", "\"\"\"\n", "base_pre_df = pd.read_sql_query(base_query, trino)\n", "\n", "base_pre_df[\"be_facility_id\"] = base_pre_df[\"be_facility_id\"].fillna(0).astype(int)\n", "base_pre_df[\"be_inv_outlet_id\"] = base_pre_df[\"be_inv_outlet_id\"].fillna(0).astype(int)\n", "base_pre_df[\"be_outlet_id\"] = base_pre_df[\"be_outlet_id\"].fillna(0).astype(int)\n", "\n", "base_pre_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "ca76b955-7a41-40c7-b2f1-f3861863a42f", "metadata": {}, "outputs": [], "source": ["item_id_list = tuple(set(base_pre_df[\"item_id\"].to_list()))\n", "\n", "facility_id_list = tuple(set(base_pre_df[\"facility_id\"].to_list()))\n", "outlet_id_list = tuple(set(base_pre_df[\"outlet_id\"].to_list()))\n", "\n", "len(item_id_list), len(facility_id_list), len(outlet_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "8a80d7dd-e127-4119-9ae4-6b117ef978c4", "metadata": {}, "outputs": [], "source": ["base_df = base_pre_df.copy()\n", "base_df[\"date_flag\"] = 1\n", "base_df = pd.merge(base_df, date_df, on=[\"date_flag\"], how=\"inner\").drop(columns={\"date_flag\"})"]}, {"cell_type": "code", "execution_count": null, "id": "6757f7bb-8da5-4d79-9ea8-c37ffb922509", "metadata": {}, "outputs": [], "source": ["# chk_base = base_df.groupby(['facility_id','item_id']).agg({'city':'nunique' ,\n", "#                                                          'outlet_id':'nunique' ,\n", "#                                                          'be_facility_id':'nunique' ,\n", "#                                                          'be_outlet_id':'nunique' ,\n", "#                                                          'be_inv_outlet_id':'nunique' }).reset_index()"]}, {"cell_type": "markdown", "id": "a5a2ecbd-ac25-459a-81a1-fcca51498193", "metadata": {}, "source": ["## Debit Note Data"]}, {"cell_type": "code", "execution_count": null, "id": "5678074f-0f2f-4d40-bddd-2b0d85b29e07", "metadata": {}, "outputs": [], "source": ["input_query__ = f\"\"\"\n", "    SELECT *\n", "    FROM supply_etls.perishable_debit_note_eligible_items \n", "    \"\"\"\n", "input_df = pd.read_sql_query(input_query__, trino)\n", "input_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "8800a27e-58ef-4d04-a739-290c50be007b", "metadata": {"tags": []}, "outputs": [], "source": ["try:\n", "    md_stores_df = pb.from_sheets(\n", "        \"1Xi_uRVasNFQZWHGhwUWMoXpRuWdchvajyu2F5EVFROc\", \"mother_dairy_facility\"\n", "    )\n", "    md_stores_df[\"outlet_id\"] = md_stores_df[\"outlet_id\"].astype(int)\n", "\n", "    md_outlet_id_list = tuple(set(md_stores_df[\"outlet_id\"].to_list()))\n", "except:\n", "    md_stores_df = pd.read_csv(\"mother_dairy_outlets.csv\")\n", "    md_stores_df[\"outlet_id\"] = md_stores_df[\"outlet_id\"].astype(int)\n", "\n", "    md_outlet_id_list = tuple(set(md_stores_df[\"outlet_id\"].to_list()))\n", "\n", "\n", "# outlet_id_list\n", "# md_outlet_id_list = (10001,2001)"]}, {"cell_type": "code", "execution_count": null, "id": "bea5807a-a10c-432d-9f96-97ca0a1d7885", "metadata": {"tags": []}, "outputs": [], "source": ["dump_query_new = f\"\"\"\n", "    WITH assortment_base AS (\n", "                        SELECT DISTINCT ma.item_id as item_id, icd.name AS item_name, \n", "                        CASE \n", "                             WHEN l2_id IN (1425) THEN 'Batter'\n", "                             WHEN l2_id IN (31,116,198,1097,1956,2633) THEN 'Breads'\n", "                             WHEN l2_id IN (949) THEN 'Curd'\n", "                             WHEN l2_id IN (1389,1778) THEN 'Eggs'\n", "                             WHEN l2_id IN (63,1367,1369) THEN 'Meats'\n", "                             WHEN l2_id IN (1185) THEN 'Milk'\n", "                             WHEN l2_id IN (950) THEN 'Paneer'\n", "                             WHEN l2_id IN (138,1091,1093) THEN 'Perishable Dairy'\n", "                             WHEN l2_id IN (1094) THEN 'Yogurt'\n", "                        ELSE 'Others'\n", "                        END AS category \n", "                        FROM rpc.product_product ma\n", "                        JOIN (\n", "                            SELECT item_id, MAX(id) AS id\n", "                            FROM rpc.product_product\n", "                            WHERE active = 1 and approved = 1 and lake_active_record and perishable = 1\n", "                            GROUP BY 1\n", "                        ) b ON ma.item_id = b.item_id and ma.id = b.id\n", "                        JOIN rpc.item_category_details icd ON icd.item_id = ma.item_id and icd.lake_active_record\n", "                        WHERE l2_id = 1185\n", "            ),\n", "\n", "    pre_sales AS (\n", "                SELECT (oid.cart_checkout_ts_ist) AS order_date, rco.facility_id, oid.product_id, im.item_id, oid.order_id, oid.outlet_id,\n", "                ((oid.procured_quantity - oid.total_doorstep_return_quantity) * im.multiplier) AS sales_quantity \n", "                FROM dwh.fact_sales_order_item_details oid \n", "                JOIN dwh.dim_item_product_offer_mapping im on im.product_id = oid.product_id and is_current\n", "                JOIN retail.console_outlet rco ON rco.id = oid.outlet_id AND business_type_id = 7 AND rco.lake_active_record\n", "                join rpc.item_category_details icd ON icd.item_id = im.item_id and icd.lake_active_record and l2_id = 1185\n", "                WHERE oid.order_create_dt_ist >= current_date - interval '10' day\n", "                and oid.order_create_dt_ist <= current_date\n", "                AND oid.is_internal_order = false  \n", "                AND (oid.order_type NOT LIKE '%%internal%%' OR oid.order_type IS NULL)  \n", "                AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED'\n", "            ),\n", "\n", "    pre_sales_2 as (\n", "            select \n", "            date_, \n", "            outlet_id, \n", "            item_id, \n", "            lead(\"13 to 23 sales\") over(partition by outlet_id, item_id order by date_ desc) as \"13 to 23 sales\", \n", "            \"0 to 8 sales\",\n", "            lead(\"18 to 21 sales\") over(partition by outlet_id, item_id order by date_ desc) as \"18 to 21 sales\",\n", "            lead(\"21 to 23 sales\") over(partition by outlet_id, item_id order by date_ desc) as \"21 to 23 sales\",\n", "            lead(date_) over(partition by outlet_id, item_id order by date_ desc) as pre_date\n", "            from (\n", "                SELECT DATE(order_date) AS date_, s.outlet_id, s.item_id,\n", "                coalesce(SUM(case when EXTRACT(hour FROM order_date)  between 0 and 8 then sales_quantity end),0) AS \"0 to 8 sales\",\n", "                coalesce(SUM(case when EXTRACT(hour FROM order_date)  between 13 and 23 then sales_quantity end),0) AS \"13 to 23 sales\",\n", "                coalesce(SUM(case when EXTRACT(hour FROM order_date)  between 18 and 21 then sales_quantity end),0) AS \"18 to 21 sales\",\n", "                coalesce(SUM(case when EXTRACT(hour FROM order_date)  between 21 and 23 then sales_quantity end),0) AS \"21 to 23 sales\"\n", "                FROM pre_sales s\n", "                GROUP BY 1,2,3\n", "                )\n", "            ),\n", "    sales as (\n", "        select \n", "            date_, \n", "            outlet_id, \n", "            item_id, \n", "            case \n", "            when pre_date = date_ - interval '1' day then \"13 to 23 sales\" else 0 end as \"13 to 23 sales\",\n", "            \"0 to 8 sales\",\n", "            case \n", "            when pre_date = date_ - interval '1' day then \"18 to 21 sales\" else 0 end as \"18 to 21 sales\",\n", "            case \n", "            when pre_date = date_ - interval '1' day then \"21 to 23 sales\" else 0 end as \"21 to 23 sales\"\n", "            from \n", "            pre_sales_2\n", "\n", "        ),\n", "\n", "    dump as (\n", "                select \n", "                order_date as date_ist,\n", "                order_hour as  hour_,\n", "                outlet_id,\n", "                item_id,\n", "                reason,\n", "                last_dump_ts,\n", "                first_dump_ts,\n", "                avg(weighted_lp) lp,\n", "                sum(quan) tot_dump\n", "                from\n", "                    (\n", "                    SELECT \n", "                        case \n", "                            when \n", "                                lower(cl.name) = 'chennai' \n", "                                and r.item_id in (10140682,10110668,10114626,10110669)\n", "                                and extract(hour from i.pos_timestamp + interval '330' minute) >19\n", "                            then date(i.pos_timestamp + interval '330' minute) + interval '1' day \n", "                            else date(i.pos_timestamp + interval '330' minute) end  AS order_date,\n", "                        extract(hour from i.pos_timestamp + interval '330' minute) as order_hour,\n", "                        i.outlet_id,\n", "                        r.item_id,\n", "                        ab.item_name,\n", "                        Case when i.inventory_update_type_id in (11) then 'Damaged' \n", "                             when i.inventory_update_type_id in (64) then 'Near Expiry'\n", "                             when i.inventory_update_type_id in (7,9,33,34,63,67) then 'Customer Dump'\n", "                             when i.inventory_update_type_id in (12) then 'Badstock Expiry'\n", "                        else 'Others' end as reason,\n", "                        i.\"delta\" quan,\n", "                        i.weighted_lp,\n", "                        (i.\"delta\" * i.weighted_lp) AS dump_value,\n", "                        max(i.pos_timestamp + interval '330' minute) over (partition by date(i.pos_timestamp + interval '330' minute),i.outlet_id,r.item_id) as last_dump_ts,\n", "                        min(i.pos_timestamp + interval '330' minute) over (partition by date(i.pos_timestamp + interval '330' minute),i.outlet_id,r.item_id) as first_dump_ts\n", "                    FROM ims.ims_inventory_log i \n", "                    inner join rpc.product_product r on i.variant_id=r.variant_id AND r.lake_active_record\n", "                    JOIN assortment_base ab on ab.item_id = r.item_id\n", "                    LEFT JOIN ims.ims_inventory_update_type i2 ON i.inventory_update_type_id = i2.id AND i2.lake_active_record\n", "                    LEFT JOIN ims.ims_bad_inventory_update_log ibil ON  i.inventory_update_id=ibil.inventory_update_id AND ibil.lake_active_record\n", "                    LEFT join ims.ims_bad_update_reason ibur on ibil.reason_id=ibur.id AND ibur.lake_active_record\n", "                    left JOIN retail.console_outlet co ON co.id = i.outlet_id AND co.active = 1 AND co.business_type_id = 7\n", "                    LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id\n", "                    WHERE \n", "                    i.inventory_update_type_id in (11,12,13,64,87,88,89,7,33,9,34,63,67) \n", "                    -- i.inventory_update_type_id in (64, 12)\n", "                and i.insert_ds_ist >= cast(current_date - interval '10' day as varchar)\n", "                and i.insert_ds_ist <= cast(current_date as varchar)\n", "                ) a\n", "                GROUP By 1,2,3,4,5,6,7\n", "            ),\n", "\n", "    final_dump as (\n", "                select \n", "                    date_ist as date_,\n", "                    hour_,\n", "                    last_dump_ts,\n", "                    first_dump_ts,\n", "                    outlet_id, \n", "                    item_id, \n", "                    -- reason,\n", "                    avg(lp) as lp,\n", "                    sum(case when lower(reason) like '%%expir%%' then tot_dump end) tot_dump,\n", "                    sum(case when lower(reason) like '%%damage%%' then tot_dump end) Dam_dump\n", "                from \n", "                    dump \n", "                    -- where lower(reason) like '%%expir%%' \n", "                group by 1,2,3,4,5,6\n", "            ),\n", "\n", "\n", "    inv_base as (\n", "                select date_, \n", "                    city, \n", "                    outlet_id, item_id, \n", "                    lead(ci_1pm) over(partition by outlet_id, item_id order by date_ desc) as ci_1pm, \n", "                    lead(ci_9pm) over(partition by outlet_id, item_id order by date_ desc) as ci_9pm,\n", "                    lead(ci_6pm) over(partition by outlet_id, item_id order by date_ desc) as ci_6pm,\n", "                    ci_12am\n", "                    from (select \n", "                    a.snapshot_date_ist as date_,\n", "                    -- extract(hour from a.updated_at_ist) as hour_,\n", "                    cl.name as city,\n", "                    a.outlet_id,\n", "                    a.item_id,\n", "                    max(case when snapshot_hr_mm/100 = 21 then a.current_inventory end) as ci_9pm,\n", "                    max(case when snapshot_hr_mm/100 = 13 then a.current_inventory end) as ci_1pm,\n", "                    max(case when snapshot_hr_mm/100 = 18 then a.current_inventory end) as ci_6pm,\n", "                    max(case when snapshot_hr_mm/100 = 0 then a.current_inventory end) as ci_12am\n", "                    from dwh.agg_hourly_outlet_item_inventory a\n", "                    JOIN retail.console_outlet co ON co.id = a.outlet_id AND co.active = 1 AND co.business_type_id = 7\n", "                    join rpc.item_category_details icd on icd.item_id = a.item_id and l2_id  = 1185\n", "                    LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id\n", "                    where snapshot_date_ist >= current_date - interval '10' day\n", "                    and snapshot_hr_mm%%100 = 0 \n", "                    group by 1,2,3,4)\n", "            ),\n", "            \n", "    pre as (\n", "    \n", "    SELECT\n", "        cast(isi.created_at as timestamp(6)) + INTERVAL '330' minute  as sto_creation_time,\n", "        isi.sto_id,\n", "        case when s.sto_state=1 then 'CREATED' when s.sto_state=2 then 'BILLED' when s.sto_state=3 then 'EXPIRED' when s.sto_state=4 then 'INWARD' when s.sto_state=5 then 'FORCE_BILLED' end as sto_state,\n", "        s.outlet_id as warehouse_outlet_id,\n", "        s.merchant_outlet_id as store_outlet_id,\n", "        bo.name as store_name,\n", "        isi.item_id,\n", "        au.username,\n", "        cast(s.updated_at as timestamp(6)) + INTERVAL '330' minute as grn_time,\n", "        sum(isi.reserved_quantity) as sto_quantity,\n", "        sum(isi.billed_quantity) as billed_quanity,\n", "        sum(isi.inward_quantity) as inward\n", "    FROM\n", "        ims.ims_sto_item isi\n", "    inner join\n", "        rpc.item_category_details ic\n", "        on ic.item_id = isi.item_id\n", "        and ic.l2_id = 1185\n", "    INNER JOIN\n", "        ims.ims_sto_details s\n", "        ON isi.sto_id=s.sto_id\n", "    INNER JOIN\n", "        lake_retail.console_outlet bo\n", "        on bo.id=s.merchant_outlet_id\n", "        and bo.active = 1\n", "    INNER JOIN\n", "        po.sto_items ps\n", "        on ps.sto_id = isi.sto_id \n", "        and ps.item_id = isi.item_id\n", "    INNER JOIN\n", "        lake_retail.auth_user au\n", "        on au.id = ps.created_by\n", "        \n", "    WHERE\n", "        isi.created_at >= current_date - interval '8' day\n", "        \n", "    GROUP BY 1,2,3,4,5,6,7,8,9\n", "),\n", "\n", "    interstore as (\n", "    select \n", "        date(p.sto_creation_time) + interval '1' day as date_,\n", "        isl.destination_outlet_id outlet_id,\n", "        isl.item_id,\n", "        grn_time,\n", "        p.inward as is_grn_aftr_9pm\n", "    from\n", "        interim.milk_inter_store_logs_v2 isl\n", "    left join\n", "        pre p\n", "        on date(isl.date_) = date(p.sto_creation_time)\n", "        and isl.source_outlet_id = p.warehouse_outlet_id\n", "        and isl.destination_outlet_id = p.store_outlet_id\n", "        and isl.item_id = p.item_id\n", "    where \n", "        date_ >= current_date - interval '8' day\n", "        and grn_time is not null\n", "        and extract(hour from grn_time) >=21\n", "),   \n", "          \n", "          \n", "          \n", "            \n", "            \n", "    pre_dump_df as (\n", "            select \n", "            lc.date_, \n", "            cl.name as city, \n", "            lc.outlet_id, \n", "            co.name as outlet_name, \n", "            lc.item_id, \n", "            lp,\n", "            Case when lc.item_id in (9999) then 'All' else item_name end as item_name,\n", "            case \n", "                when lc.outlet_id in {outlet_id_list} and lc.item_id in (10014172, 10012985, 10012984, 10018467) then ci_1pm\n", "                when lower(cl.name) = 'chennai' and lc.item_id in (10140682,10110668,10114626,10110669) then ci_6pm\n", "                else  ci_9pm + coalesce(is_grn_aftr_9pm,0) end as leftover_quantity,\n", "            case \n", "                when lc.outlet_id in {outlet_id_list} and lc.item_id in (10014172, 10012985, 10012984, 10018467) then (\"13 to 23 sales\" + \"0 to 8 sales\") \n", "                when lower(city) = 'chennai' and lc.item_id in (10140682,10110668,10114626,10110669) then \"18 to 21 sales\"\n", "                else (\"21 to 23 sales\" + \"0 to 8 sales\") end as sales_quantity,\n", "            substr(cast(first_dump_ts as varchar),11,6) first_dump_ts,\n", "            substr(cast(last_dump_ts as varchar),11,6) last_dump_ts,\n", "            -- first_dump_ts,\n", "            count(distinct hour_) as dist_dump_hours,\n", "            sum(tot_dump) as expiry_dump,\n", "            sum(Dam_dump) as damage_dump\n", "            from final_dump lc\n", "            left join inv_base inv on inv.item_id = lc.item_id and inv.outlet_id = lc.outlet_id and lc.date_ = inv.date_\n", "            left join retail.console_outlet co on co.id = lc.outlet_id and co.business_type_id = 7 and co.active = 1 and co.lake_active_record\n", "            left join retail.console_location cl on cl.id = co.tax_location_id\n", "            left join sales s on s.item_id = lc.item_id and s.date_= lc.date_ and lc.outlet_id = s.outlet_id\n", "            inner join assortment_base ab on ab.item_id = lc.item_id \n", "            left join interstore ist on ist.date_ = inv.date_ and ist.item_id = inv.item_id and ist.outlet_id = inv.outlet_id\n", "            group by 1,2,3,3,4,5,6,7,8,9,10,11\n", "            -- having sum(case when hour_ between 8 and 9 and lower(reason) like '%%expir%%' then tot_dump)>0\n", "            ),\n", "    \n", "    dump_pre as (\n", "                select \n", "                date_,\n", "                city,\n", "                outlet_id,\n", "                item_id,\n", "                lp,\n", "                damage_dump,\n", "                expiry_dump,\n", "                case \n", "                    when coalesce(leftover_quantity,0) > coalesce(sales_quantity,0) and expiry_dump > (coalesce(leftover_quantity,0) - coalesce(sales_quantity,0)) then coalesce(leftover_quantity,0) - coalesce(sales_quantity,0)\n", "                    when coalesce(leftover_quantity,0) > coalesce(sales_quantity,0) and expiry_dump <= (coalesce(leftover_quantity,0) - coalesce(sales_quantity,0)) then expiry_dump\n", "                    else 0 end as correct_dump\n", "                from \n", "                    pre_dump_df\n", "                    ),\n", "\n", "dump_fin AS (\n", "        SELECT distinct\n", "        pd.date_ AS date_, \n", "        pd.outlet_id, \n", "        pd.item_id, \n", "        damage_dump,\n", "        lp,\n", "        CAST(damage_dump * lp AS int) AS damage_dump_value,\n", "        expiry_dump,\n", "        CAST(expiry_dump * lp AS int) AS expiry_dump_value,\n", "        correct_dump,\n", "        CAST(correct_dump * lp AS int) AS corr_dump_value\n", "        FROM \n", "            dump_pre as pd\n", "    )  \n", "    \n", "    SELECT  \n", "    date_, \n", "    co.facility_id,\n", "    a.outlet_id,\n", "    a.item_id,\n", "    avg(lp) as lp,\n", "    sum(damage_dump) damage_dump,\n", "    sum(expiry_dump) expiry_dump,\n", "    sum(correct_dump) correct_dump,\n", "    sum(case when expiry_dump>=correct_dump then expiry_dump - correct_dump else 0 end) as wrong_dump,\n", "    sum(damage_dump_value) damage_dump_value,\n", "    sum(expiry_dump_value) as expiry_dump_value,\n", "    sum(corr_dump_value) as correct_dump_value,\n", "    sum(case when expiry_dump_value >= corr_dump_value then expiry_dump_value - corr_dump_value else 0 end) as wrong_dump_value\n", "    FROM dump_fin a\n", "    JOIN retail.console_outlet co ON co.id = outlet_id AND co.active = 1 AND co.lake_active_record\n", "    group by 1,2,3,4\n", "    \"\"\"\n", "dump_query = read_sql_query(dump_query_new, trino)\n", "\n", "dump_query[\"date_\"] = pd.to_datetime(dump_query[\"date_\"])\n", "dump_query = dump_query.fillna(0)\n", "dump_query.shape"]}, {"cell_type": "code", "execution_count": null, "id": "f76018d4-f807-4790-964b-1b58bc60a353", "metadata": {}, "outputs": [], "source": ["final_input_df = pd.merge(\n", "    base_df, dump_query, on=[\"item_id\", \"outlet_id\", \"facility_id\", \"date_\"], how=\"left\"\n", ")\n", "final_input_df = final_input_df[final_input_df[\"date_\"].notnull()]\n", "final_input_df = final_input_df.fillna(0)\n", "final_input_df.head()"]}, {"cell_type": "markdown", "id": "964104fb-fbb2-44af-8c83-9c647adfaf81", "metadata": {}, "source": ["## PRN Data"]}, {"cell_type": "code", "execution_count": null, "id": "2b5510c2-cd0d-483a-a690-39bbe761546b", "metadata": {}, "outputs": [], "source": ["prn_query = f\"\"\"\n", "   WITH base AS (\n", "        SELECT DATE(pos_timestamp + interval '330' minute) AS date_, EXTRACT(hour from pos_timestamp + interval '330' minute) AS hour_, outlet_id, \n", "        item_id, il.weighted_lp, SUM(il.\"delta\") AS prn_qty\n", "        FROM ims.ims_inventory_log il\n", "        JOIN (\n", "            SELECT DISTINCT item_id, variant_id \n", "            FROM rpc.product_product WHERE lake_active_record\n", "        ) rpp ON rpp.variant_id = il.variant_id\n", "        WHERE insert_ds_ist > CAST(current_date - interval '10' day AS varchar) \n", "        AND inventory_update_type_id IN (19,20,21,65) \n", "        AND item_id IN {item_id_list}\n", "        AND il.lake_active_record\n", "        GROUP BY 1,2,3,4,5\n", "    )\n", "\n", "    SELECT date_, cf.facility_id, item_id, hour_ , CAST((weighted_lp * prn_qty) AS int) AS prn_value\n", "    FROM base a\n", "    JOIN retail.console_outlet cf ON cf.active = 1 AND cf.business_type_id IN (1,12,7,19,20,21) AND cf.id = outlet_id AND cf.lake_active_record\n", "    WHERE item_id IN {item_id_list}\n", "  \n", "    \"\"\"\n", "prn_df = pd.read_sql_query(prn_query, trino)\n", "prn_df[\"date_\"] = pd.to_datetime(prn_df[\"date_\"])\n", "\n", "prn_df.shape"]}, {"cell_type": "markdown", "id": "540729ed-b132-430b-8a78-b17d6143e828", "metadata": {}, "source": ["## Sales Data ##"]}, {"cell_type": "code", "execution_count": null, "id": "e0bb3590-2cc2-46e2-bcc9-767aefa113b2", "metadata": {}, "outputs": [], "source": ["sale_query = f\"\"\"\n", "    WITH item_mapping as (\n", "        SELECT DISTINCT ipr.product_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NULL THEN ipom_0.item_id \n", "            ELSE ipr.item_id \n", "        END AS item_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1) \n", "            ELSE COALESCE(ipom_0.multiplier,1) \n", "        END AS multiplier\n", "        FROM rpc.item_product_mapping ipr \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", "        WHERE ipr.lake_active_record\n", "    ),\n", "\n", "    sales AS (\n", "        SELECT (oid.cart_checkout_ts_ist) AS order_date, \n", "        rco.facility_id,\n", "        oid.product_id, \n", "        oid.outlet_id,\n", "        im.item_id,\n", "        oid.order_id, \n", "        im.multiplier,\n", "        frontend_merchant_id,\n", "        ((oid.procured_quantity - oid.total_doorstep_return_quantity) * im.multiplier) AS sales_quantity,\n", "        ((oid.procured_quantity - oid.total_doorstep_return_quantity) * im.multiplier) * oid.unit_selling_price * 1.000/ im.multiplier AS GMV\n", "        FROM dwh.fact_sales_order_item_details oid \n", "        JOIN item_mapping im on im.product_id = oid.product_id  \n", "        JOIN retail.console_outlet rco ON rco.id = oid.outlet_id AND business_type_id = 7 AND rco.lake_active_record\n", "        WHERE oid.cart_checkout_ts_ist >= current_date - interval '10' day  \n", "        and oid.order_create_dt_ist >= current_date - interval '10' day\n", "        AND oid.is_internal_order = false  \n", "        AND (oid.order_type NOT LIKE '%%internal%%' OR oid.order_type IS NULL)  \n", "        AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED'\n", "        AND oid.outlet_id IN {outlet_id_list}\n", "    ),\n", "\n", "     milk_carts AS (\n", "            SELECT \n", "            DATE(order_date) AS date_,\n", "            extract(hour from order_date) as hour_,\n", "            frontend_merchant_id,\n", "            cl.name as city,\n", "            s.facility_id,\n", "            s.item_id,\n", "            sum(gmv) as gmv,\n", "            sum(sales_quantity) AS qty,\n", "            count(distinct order_id) AS milk_carts\n", "            FROM sales s\n", "            JOIN retail.console_outlet co ON co.id = s.outlet_id and co.active = 1 and co.business_type_id = 7 and co.lake_active_record\n", "            LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id\n", "            WHERE item_id IN {item_id_list}\n", "            GROUP BY 1,2,3,4,5,6\n", "        ),\n", "\n", "    fac_carts AS (\n", "        SELECT DATE(order_date) AS date_,extract(hour from order_date) as hour_,frontend_merchant_id,cl.name as city,s.facility_id, count(distinct order_id) AS fac_carts\n", "        FROM sales s\n", "        JOIN retail.console_outlet co ON co.id = s.outlet_id and co.active = 1 and co.business_type_id = 7 and co.lake_active_record\n", "        LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id\n", "        GROUP BY 1,2,3,4,5\n", "    ),\n", "\n", "    final_sales AS (\n", "        SELECT DATE(order_date) AS date_,extract(hour from order_date) as hour_,frontend_merchant_id, cl.name as city,s.facility_id,s.item_id, CAST(SUM(sales_quantity) AS int) AS sales\n", "        FROM sales s\n", "        JOIN retail.console_outlet co ON co.id = s.outlet_id and co.active = 1 and co.business_type_id = 7 and co.lake_active_record\n", "        LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id\n", "        WHERE item_id IN {item_id_list}\n", "        GROUP BY 1,2,3,4,5,6\n", "    )\n", "       \n", "select\n", "        mc.date_,\n", "        mc.facility_id,\n", "        mc.item_id,\n", "        mc.frontend_merchant_id as merchant_id,\n", "        mc.hour_,\n", "        sum(qty) as qty,\n", "        sum(mc.gmv) as gmv,\n", "        (sum(milk_carts)*1.000)/sum(fac_carts) as cart_pen,\n", "        (sum(sales)*1.000)/sum(milk_carts) as ipc\n", "    from\n", "        milk_carts mc\n", "    left join\n", "        fac_carts fc\n", "    on mc.date_ = fc.date_ and mc.facility_id = fc.facility_id and mc.hour_ = fc.hour_ \n", "    left join\n", "        final_sales fs\n", "    on mc.date_ = fs.date_ and mc.facility_id = fs.facility_id and mc.hour_ = fs.hour_ and mc.item_id = fs.item_id\n", "    group by 1,2,3,4,5\n", "\n", "    \"\"\"\n", "\n", "sales_pre_df = read_sql_query(sale_query, trino)\n", "sales_pre_df[\"date_\"] = pd.to_datetime(sales_pre_df[\"date_\"])\n", "sales_pre_df[[\"cart_pen\", \"ipc\"]] = sales_pre_df[[\"cart_pen\", \"ipc\"]].astype(float)\n", "sales_pre_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d32caa85-10eb-45eb-ac48-005edb0990b5", "metadata": {}, "outputs": [], "source": ["sales_pre_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "45530044-3a82-491d-9b62-e398381b2783", "metadata": {}, "outputs": [], "source": ["sales_agg = sales_pre_df.groupby([\"date_\"]).agg({\"gmv\": \"sum\", \"qty\": \"sum\"}).reset_index()\n", "sales_agg.tail()"]}, {"cell_type": "code", "execution_count": null, "id": "ec4ed5c4-0180-49ca-b415-ad16b842eb7f", "metadata": {}, "outputs": [], "source": ["sales_df = sales_pre_df[\n", "    [\"facility_id\", \"item_id\", \"date_\", \"hour_\", \"qty\", \"gmv\"]\n", "].drop_duplicates()\n", "sales_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "368999dd-5c35-4f64-9c9a-dde6f03f437a", "metadata": {}, "outputs": [], "source": ["sec_sale_query = f\"\"\"\n", "with perishable_skus_base as (\n", "    select distinct ma.item_id, icd.name as item_name, ma.upc, ma.variant_id,\n", "    Case WHEN l2_id IN (1425) THEN 'Batter'\n", "            WHEN l2_id IN (31,116,198,1097,1956,2633) THEN 'Breads'\n", "            WHEN l2_id IN (949) THEN 'Curd'\n", "            WHEN l2_id IN (1389,1778) THEN 'Eggs'\n", "            WHEN l2_id IN (63,1367,1369) THEN 'Meats'\n", "            WHEN l2_id IN (1185) THEN 'Milk'\n", "            WHEN l2_id IN (950) THEN 'Paneer'\n", "            WHEN l2_id IN (138,1091,1093) THEN 'Perishable Dairy'\n", "            WHEN l2_id IN (1094) THEN 'Yogurt'\n", "            WHEN l2_id IN (33,97,197,1127) THEN 'Other Perishables' \n", "            else 'Others' end as l2\n", "        from  rpc.product_product ma \n", "        JOIN (\n", "                SELECT item_id, MAX(id) AS id\n", "                FROM rpc.product_product\n", "                WHERE active = 1 and approved = 1 and lake_active_record\n", "                GROUP BY 1\n", "            ) b ON ma.item_id = b.item_id and ma.id = b.id\n", "            join rpc.item_category_details icd on icd.item_id = ma.item_id and icd.lake_active_record\n", "            where ma.lake_active_record\n", "),\n", "            \n", "it1 as (\n", "    Select * from perishable_skus_base\n", "),\n", "    \n", " \n", "it as (\n", "    select item_id, upc, l2 as category\n", "    from it1\n", "    group by 1,2,3\n", "),\n", "    \n", "o as (\n", "    select co.id as out_id\n", "    from retail.console_outlet co \n", "    where  co.active = 1 and co.business_type_id in (1,12,7,8,19,20) and co.lake_active_record\n", "),\n", "\n", "ims_invoice as (\n", "            select \n", "                invoice_id, variant_id, sum(\"delta\" * transaction_lp) as weighted_lp\n", "            from \n", "                ims.ims_inventory_log\n", "            where \n", "                insert_ds_ist  >= CAST(current_date - interval '10' day AS varchar)\n", "                and inventory_update_type_id in (52, 56, 69)\n", "                and lake_active_record\n", "                group by 1,2\n", "        ),\n", "    \n", "ss as (\n", "    select date(pinv.pos_timestamp + interval '330' minute) as date_, r.id out_id, pinv.item_id,\n", "        sum(secondary_sale_quantity) as secondary_sale_quantity,\n", "        sum(secondary_sale_value) as sec_sale\n", "    from \n", "        (select \n", "            pi.pos_timestamp,\n", "            pi.invoice_id, \n", "            pipd.variant_id, \n", "            pi.outlet_id, \n", "            it.category,\n", "            it.item_id,\n", "            pipd.invoice_id as pipd_invoice_id,\n", "            sum(pipd.quantity) as secondary_sale_quantity,\n", "            sum(pipd.quantity * selling_price) as secondary_sale_value\n", "         from \n", "            pos.pos_invoice pi\n", "         join \n", "            pos.pos_invoice_product_details pipd on pipd.invoice_id = pi.id and pipd.lake_active_record and pipd.insert_ds_ist  >= CAST(current_date - interval '10' day AS varchar)\n", "        inner join \n", "            it \n", "        on \n", "            it.upc = pipd.upc_id\n", "         where \n", "            pi.insert_ds_ist  >= CAST(current_date - interval '10' day AS varchar)\n", "            and invoice_type_id = 12\n", "            and pipd.variant_id not in ('0409053a-a916-48c5-2e5d-42095f234f61',\n", "                               '0fb4b95c-51a3-e850-c26d-47ac10c891f2',\n", "                               '357cb303-73ac-3a7f-20bf-82932e55981b',\n", "                               '578fa4ae-b2a8-4f1b-ad31-8093b7b71617',\n", "                               '98c7bfe7-16b2-4e37-ad5e-1fa114173fd8',\n", "                               'a9c0c724-6d54-18e3-1e0a-300601af6260',\n", "                               'aeafd3c2-2d79-737e-379b-f9af831e9a6a',\n", "                               'c49abd3a-31b5-3751-9e23-2cf031c46a27',\n", "                               'c5284533-cd5f-4cd4-be88-d1570bbad332',\n", "                               'e1d6eb70-125f-ab61-0888-711eda04b0c7',\n", "                               'efac5dca-1dfd-ba70-ef55-1b5c48df6224',\n", "                               'f18677ca-8a89-4342-8320-3ba218517244')\n", "            and pi.lake_active_record\n", "         group by 1,2,3,4,5,6,7) pinv\n", "    inner join \n", "        o \n", "    on \n", "        o.out_id = pinv.outlet_id\n", "        and pinv.outlet_id not in (1976)\n", "   join \n", "        retail.console_outlet r on r.id = pinv.outlet_id and r.active = 1 and r.device_id != 47 and r.lake_active_record\n", "   join \n", "        rpc.product_product prod on prod.variant_id = pinv.variant_id and prod.lake_active_record\n", "   join \n", "        pos.pos_customer_tax_details p on p.invoice_id = pinv.pipd_invoice_id and p.lake_active_record and p.insert_ds_ist>= CAST(current_date - interval '10' day AS varchar)\n", "   join \n", "        vms.vms_vendor v on v.id = p.vms_merchant_id and v.type_id = 3 and lower(vendor_name) not like ('%%zomato%%') and v.lake_active_record\n", "   left join ims_invoice ims \n", "            on \n", "                ims.invoice_id = pinv.invoice_id \n", "                and ims.variant_id = pinv.variant_id\n", "   where\n", "        r.name not like '%%Infra%%'\n", "        and r.business_type_id != 8\n", "   group by 1,2,3\n", "        )\n", "    \n", " select  \n", "     date_ as date_,\n", "     out_id AS outlet_id,\n", "     ss.item_id, \n", "     sum(sec_sale) as sec_sale  \n", "     from \n", "     ss group  by 1,2,3\n", "    \"\"\"\n", "\n", "sec_sales_df = pd.read_sql(sec_sale_query, trino)\n", "sec_sales_df[\"date_\"] = pd.to_datetime(sec_sales_df[\"date_\"])\n", "\n", "sec_sales_df.head()"]}, {"cell_type": "markdown", "id": "b3952145-f53c-4e71-9eb0-3a5630fe2f93", "metadata": {}, "source": ["## Order loss due to surge"]}, {"cell_type": "code", "execution_count": null, "id": "e1ad3a0a-182a-41cd-a6a6-c4aca5aa7d07", "metadata": {}, "outputs": [], "source": ["surgre_query = \"\"\"\n", "       with order_loss as (\n", "            select \n", "                at_date_ist as date_,\n", "                city,\n", "                merchant_id,\n", "                hour,\n", "                sum(lost_orders) as order_loss\n", "            from \n", "                dwh.agg_daily_lost_order_metrics\n", "            where \n", "                at_date_ist >= current_date - interval '10' day  \n", "                and \n", "                loss_type = 'High ETA and Surge'\n", "            group by 1,2,3,4\n", "        )\n", "\n", "        select\n", "            mc.date_,\n", "            mc.merchant_id as merchant_id,\n", "            mc.hour as hour_,\n", "            coalesce(mc.order_loss,0) as order_loss\n", "        from\n", "            order_loss mc\n", "        group by 1,2,3,4\n", "\"\"\"\n", "\n", "order_loss = read_sql_query(surgre_query, trino)\n", "order_loss[\"date_\"] = pd.to_datetime(order_loss[\"date_\"])\n", "order_loss = order_loss.fillna(0)\n", "order_loss.head()"]}, {"cell_type": "code", "execution_count": null, "id": "fccc2bff-1d84-4014-bdc3-562efda08337", "metadata": {}, "outputs": [], "source": ["inv = f\"\"\"  \n", "        SELECT\n", "            a.date_,\n", "            a.facility_id,\n", "            a.item_id,\n", "            hour_,\n", "            max(ci) as ci\n", "\n", "        from\n", "            supply_etls.milk_sales_avail_searches_dump as a\n", "        inner join (select date_,max(updated_at) as updated_at from supply_etls.milk_sales_avail_searches_dump group by 1) as mx\n", "           on mx.date_ = a.date_ and a.updated_at=mx.updated_at\n", "        where a.date_ >= current_date - interval '12' day\n", "            and item_id in {item_id_list}\n", "        group by 1,2,3,4\n", "        \n", "    \"\"\"\n", "inv_df = read_sql_query(inv, trino)\n", "inv_df[\"date_\"] = pd.to_datetime(inv_df[\"date_\"])\n", "inv_df = inv_df.fillna(0)\n", "inv_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "01e1a27b-954c-48df-bdbc-1abf9ab99dde", "metadata": {}, "outputs": [], "source": ["def percentile(x):\n", "    return np.percentile(x, 75)"]}, {"cell_type": "code", "execution_count": null, "id": "2642366a-8c86-4b24-ae85-1b8df90b463c", "metadata": {"tags": []}, "outputs": [], "source": ["cp_ipc = (\n", "    sales_pre_df.groupby([\"facility_id\", \"merchant_id\", \"item_id\", \"hour_\"])\n", "    .agg({\"cart_pen\": percentile, \"ipc\": percentile})\n", "    .reset_index()\n", ")\n", "surge_pre = order_loss.merge(cp_ipc, on=[\"merchant_id\", \"hour_\"], how=\"left\")\n", "surge_pre = surge_pre.merge(inv_df, on=[\"date_\", \"facility_id\", \"item_id\", \"hour_\"], how=\"left\")\n", "surge_pre[\"qty_loss\"] = surge_pre[\"order_loss\"] * surge_pre[\"cart_pen\"] * surge_pre[\"ipc\"]\n", "surge_pre[\"final_sales_loss\"] = np.where(\n", "    surge_pre[\"ci\"] >= surge_pre[\"qty_loss\"], surge_pre[\"qty_loss\"], surge_pre[\"ci\"]\n", ")\n", "surge_pre[\"dump_date\"] = np.where(\n", "    surge_pre[\"hour_\"] < 9, surge_pre[\"date_\"], surge_pre[\"date_\"] + timedelta(days=1)\n", ")\n", "# surge_pre['final_sales_loss'] = surge_pre['final_sales_loss'].fillna(0)\n", "surge_pre[\"final_sales_loss\"] = np.round(surge_pre[\"final_sales_loss\"])\n", "surge_pre.sort_values(\"order_loss\", ascending=False).head()"]}, {"cell_type": "markdown", "id": "4a15142e-a873-486a-8d7b-8bb22c319499", "metadata": {}, "source": ["## Base Preparation ##"]}, {"cell_type": "code", "execution_count": null, "id": "4fcc1c2f-b5d2-4844-a2b4-0873b904d846", "metadata": {}, "outputs": [], "source": ["assortment_base_df = base_df.copy().drop_duplicates()\n", "\n", "prn_agg_df = (\n", "    prn_df.groupby([\"date_\", \"facility_id\", \"item_id\"]).agg({\"prn_value\": \"sum\"}).reset_index()\n", ")\n", "\n", "sales_agg_df = (\n", "    sales_df.groupby([\"date_\", \"facility_id\", \"item_id\"])\n", "    .agg({\"gmv\": \"sum\", \"qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "assortment_base_df = pd.merge(\n", "    assortment_base_df, dump_query, on=[\"facility_id\", \"outlet_id\", \"item_id\", \"date_\"], how=\"left\"\n", ")\n", "\n", "\n", "assortment_base_df = pd.merge(\n", "    assortment_base_df, prn_agg_df, on=[\"date_\", \"facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "\n", "assortment_base_df = pd.merge(\n", "    assortment_base_df, sec_sales_df, on=[\"date_\", \"outlet_id\", \"item_id\"], how=\"left\"\n", ")\n", "\n", "assortment_base_df[\"expiry_dump_value\"] = assortment_base_df[\"expiry_dump_value\"].fillna(0)\n", "# assortment_base_df[\"debit_note\"] = assortment_base_df[\"debit_note\"].fillna(0)\n", "assortment_base_df[\"prn_value\"] = assortment_base_df[\"prn_value\"].fillna(0)\n", "assortment_base_df[\"sec_sale\"] = assortment_base_df[\"sec_sale\"].fillna(0)\n", "\n", "assortment_base_df[\"net_dump\"] = (\n", "    assortment_base_df[\"expiry_dump_value\"]\n", "    - assortment_base_df[\"prn_value\"]\n", "    - assortment_base_df[\"sec_sale\"]\n", ")\n", "\n", "\n", "assortment_base_df = assortment_base_df.drop([\"expiry_dump_value\", \"prn_value\", \"sec_sale\"], axis=1)\n", "\n", "assortment_base_df = pd.merge(\n", "    assortment_base_df, sales_agg_df, on=[\"facility_id\", \"item_id\", \"date_\"], how=\"left\"\n", ")\n", "\n", "assortment_base_df[\"gmv\"] = assortment_base_df[\"gmv\"].fillna(0)\n", "\n", "assortment_base_df[\"week_no\"] = assortment_base_df[\"date_\"].dt.isocalendar().week\n", "\n", "assortment_base_df = assortment_base_df.rename(columns={\"net_dump\": \"expiry_dump_value\"})\n", "assortment_base_df = assortment_base_df.fillna(0)\n", "assortment_base_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "33915f94-cdc8-4ecc-b9ba-1e337428281a", "metadata": {}, "outputs": [], "source": ["new_store = f\"\"\"\n", "with new_stores as (\n", "select\n", "    s.facility_name,\n", "    s.facility_id,\n", "    s.store_type,\n", "    s.final_ob_date,\n", "    s.sister_store_name,\n", "    s.sister_store_facility_id,\n", "    coalesce(a.ars_active,0) ars_active,\n", "    case when co.device_id = 143 then 1 else 0 end outlet_id_active\n", "from\n", "    supply_etls.e3_new_darkstores s\n", "left join\n", "    po.physical_facility_outlet_mapping a\n", "    on s.facility_id = a.facility_id\n", "    and a.active = 1\n", "left join\n", "    retail.console_outlet co\n", "    on s.outlet_id = co.id\n", "    and co.active = 1\n", "where\n", "    -- lower(s.store_type) not in ('new store')\n", "    s.final_ob_date > date'2024-03-15'\n", "    and s.final_ob_date < current_date + interval '15' day\n", "group by 1,2,3,4,5,6,7,8\n", "having \n", "    coalesce(a.ars_active,0) = 1 and (case when co.device_id = 143 then 1 else 0 end) = 1\n", ")\n", "\n", "select \n", "    facility_id,\n", "    max(final_ob_date) as start_date\n", "from\n", "    new_stores\n", "    where final_ob_date between date'{today_date}' - interval '12' day and date'{today_date}'\n", "    group by 1\n", "\"\"\"\n", "new_store_mapping = read_sql_query(new_store, trino)\n", "new_store_mapping[\"facility_id\"] = new_store_mapping[\"facility_id\"].astype(int)\n", "new_store_mapping[\"start_date\"] = pd.to_datetime(new_store_mapping[\"start_date\"])"]}, {"cell_type": "code", "execution_count": null, "id": "5f03ff0e-6cd2-4efe-88bb-5d83b7ad6f80", "metadata": {}, "outputs": [], "source": ["buffer_store = f\"\"\"\n", "with\n", "frontend_merchant_mapping as\n", "        (select * from dwh.dim_merchant_outlet_facility_mapping\n", "            where \n", "                is_frontend_merchant_active = true\n", "                and\n", "                    is_backend_merchant_active = true\n", "                and \n", "                    is_pos_outlet_active = 1\n", "                and \n", "                    is_mapping_enabled = true\n", "                and \n", "                    is_express_store = true\n", "                and \n", "                    is_current_mapping_active = true\n", "                and \n", "                    is_current = true\n", "                and \n", "                    pos_outlet_name <> 'SS Gurgaon Test Store'\n", "),\n", "\n", "store_polygon_updates as (\n", "select \n", "    cr.updated_at + interval '5' hour + interval '30' minute as updated_time, \n", "    f.external_id as merchant_id, \n", "    json_query(cr.meta, 'strict $.business_impact.old_order_count') as old_order_count,\n", "    json_query(cr.meta, 'strict $.business_impact.new_order_count') as new_orders_count,\n", "    json_query(cr.diff, 'strict $.polygon.info') as change_in_area\n", "from \n", "    sauron.change_requests cr \n", "join \n", "    sauron.feature f \n", "    on f.id=cr.feature_id\n", "where \n", "    f.layer_id = 6 \n", "    AND cr.updated_at + interval '5' hour + interval '30' minute > CURRENT_DATE - interval '30' day \n", "    and cr.updated_at + interval '5' hour + interval '30' minute < CURRENT_DATE \n", "    AND cr.state = 'MERGED'\n", "    -- and f.external_id in (33207)\n", ")\n", "\n", "select distinct \n", "    coalesce(fm.facility_id,0) as facility_id,\n", "    date(updated_time) as date_,\n", "    date(updated_time + interval '1' day) as date_1,\n", "    date(updated_time + interval '2' day) as date_2\n", "    \n", "from \n", "    store_polygon_updates s\n", "left join frontend_merchant_mapping  fm on fm.frontend_merchant_id = s.merchant_id\n", "where s.updated_time between  date'{today_date}' - interval '12' day and date'{today_date}' - interval '1' day\n", "    \"\"\"\n", "\n", "buffer_store_mapping = read_sql_query(buffer_store, trino)\n", "buffer_store_mapping[\"facility_id\"] = buffer_store_mapping[\"facility_id\"].astype(int)\n", "buffer_store_mapping[\"date_\"] = pd.to_datetime(buffer_store_mapping[\"date_\"])\n", "buffer_store_mapping[\"date_1\"] = pd.to_datetime(buffer_store_mapping[\"date_1\"])\n", "buffer_store_mapping[\"date_2\"] = pd.to_datetime(buffer_store_mapping[\"date_2\"])\n", "buffer_store_facility_list = list(set(buffer_store_mapping[\"facility_id\"].unique()))"]}, {"cell_type": "code", "execution_count": null, "id": "9a6e04a1-17fa-4cae-ac81-9b2c4df7772f", "metadata": {}, "outputs": [], "source": ["chng_date_1 = (\n", "    buffer_store_mapping[[\"facility_id\", \"date_1\"]].copy().rename(columns={\"date_1\": \"date_\"})\n", ")\n", "chng_date_2 = (\n", "    buffer_store_mapping[[\"facility_id\", \"date_2\"]].copy().rename(columns={\"date_2\": \"date_\"})\n", ")\n", "change_date_df = pd.concat([chng_date_1, chng_date_2]).drop_duplicates()\n", "change_date_df[\"polygon_change_flag\"] = 1\n", "change_date_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "81d75ff0-46ed-42ee-b8cb-0e66d64d31da", "metadata": {}, "outputs": [], "source": ["disruption_query = f\"\"\"\n", "    WITH base AS(\n", "        SELECT * FROM supply_etls.milk_outlet_disruptions_logs\n", "    )\n", "    \n", "    SELECT DISTINCT outlet_id , date_ + interval '1' day as date_ FROM base \n", "    WHERE date_ BETWEEN DATE('{today_date}') - interval '45' day AND DATE('{today_date}') - interval '0' day\n", "    \n", "    \"\"\"\n", "disruption_df = pd.read_sql(disruption_query, trino)\n", "disruption_df[\"date_\"] = pd.to_datetime(disruption_df[\"date_\"])\n", "\n", "print(disruption_df.shape)\n", "disruption_df = disruption_df.drop_duplicates()\n", "print(disruption_df.shape)\n", "\n", "disruption_df[\"d_flag\"] = 1"]}, {"cell_type": "code", "execution_count": null, "id": "6ffa10e2-350e-4ad6-a6b6-606302e8c73a", "metadata": {}, "outputs": [], "source": ["final_ssortment = assortment_base_df.merge(new_store_mapping, on=[\"facility_id\"], how=\"left\")\n", "final_ssortment[\"start_date\"] = final_ssortment[\"start_date\"].fillna(l45_date)\n", "final_ssortment = final_ssortment.merge(change_date_df, on=[\"facility_id\", \"date_\"], how=\"left\")\n", "final_ssortment[\"polygon_change_flag\"] = final_ssortment[\"polygon_change_flag\"].fillna(0)\n", "final_ssortment[\"new_store_flag\"] = np.where(\n", "    (final_ssortment[\"date_\"] <= pd.to_datetime(final_ssortment[\"start_date\"] + timedelta(days=3)))\n", "    & (final_ssortment[\"date_\"] > final_ssortment[\"start_date\"]),\n", "    1,\n", "    0,\n", ")\n", "final_ssortment = final_ssortment.merge(disruption_df, on=[\"outlet_id\", \"date_\"], how=\"left\")\n", "final_ssortment[\"d_flag\"] = final_ssortment[\"d_flag\"].fillna(0)\n", "final_ssortment.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "aedb21db-f1b9-4a0e-b82c-83cf4835b9b7", "metadata": {"tags": []}, "outputs": [], "source": ["final_ssortment = final_ssortment[\n", "    (final_ssortment[\"date_\"] >= final_ssortment[\"start_date\"])\n", "    & (final_ssortment[\"date_\"] >= l10_date)\n", "].drop(columns={\"be_outlet_id\", \"start_date\", \"be_inv_outlet_id\"})\n", "surge_dump = (\n", "    surge_pre.groupby([\"facility_id\", \"item_id\", \"dump_date\"])\n", "    .agg({\"final_sales_loss\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"dump_date\": \"date_\"})\n", ")\n", "final_ssortment = final_ssortment.merge(\n", "    surge_dump, on=[\"facility_id\", \"item_id\", \"date_\"], how=\"left\"\n", ")\n", "final_ssortment[\"final_sales_loss\"] = final_ssortment[\"final_sales_loss\"].fillna(0)\n", "final_ssortment[\"lp\"] = final_ssortment[\"lp\"].fillna(0).astype(float)\n", "# final_ssortment['lp']  = (final_ssortment['expiry_dump_value']/final_ssortment['expiry_dump']).fillna(0).astype(float)\n", "final_ssortment[\"surge_dump\"] = (\n", "    final_ssortment[\"final_sales_loss\"].clip(0, final_ssortment[\"correct_dump\"]).astype(float)\n", ")\n", "final_ssortment[\"surge_dump_value\"] = np.round(\n", "    final_ssortment[\"surge_dump\"] * final_ssortment[\"lp\"]\n", ")\n", "final_ssortment[\"correct_dump_value\"] = (\n", "    final_ssortment[\"correct_dump_value\"] - final_ssortment[\"surge_dump_value\"]\n", ")\n", "final_ssortment[\"correct_dump\"] = final_ssortment[\"correct_dump\"] - final_ssortment[\"surge_dump\"]\n", "final_ssortment.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "81dafbf1-2464-4d06-bb26-89aa45a5cb6a", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "    SELECT item_id, name AS item_name\n", "    FROM rpc.item_category_details\n", "    WHERE item_id IN {item_id_list} AND lake_active_record\n", "    GROUP BY 1,2\n", "\"\"\"\n", "item_df = read_sql_query(query, trino)\n", "\n", "query = f\"\"\"\n", "    SELECT facility_id AS facility_id, outlet_name AS facility_name\n", "    FROM po.physical_facility_outlet_mapping\n", "    WHERE lake_active_record\n", "    and ars_active = 1\n", "    GROUP BY 1,2\n", "\"\"\"\n", "store_df = read_sql_query(query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "87819eb5-2e89-4f1a-a16b-b666bdfe8cd1", "metadata": {}, "outputs": [], "source": ["columns = [\n", "    \"city\",\n", "    \"facility_id\",\n", "    \"outlet_id\",\n", "    \"facility_name\",\n", "    \"item_id\",\n", "    \"item_name\",\n", "    \"be_facility_id\",\n", "    \"date_\",\n", "    \"lp\",\n", "    \"damage_dump\",\n", "    \"expiry_dump\",\n", "    \"correct_dump\",\n", "    \"wrong_dump\",\n", "    \"damage_dump_value\",\n", "    \"correct_dump_value\",\n", "    \"wrong_dump_value\",\n", "    \"expiry_dump_value\",\n", "    \"gmv\",\n", "    \"qty\",\n", "    \"week_no\",\n", "    \"polygon_change_flag\",\n", "    \"new_store_flag\",\n", "    \"d_flag\",\n", "    \"final_sales_loss\",\n", "    \"surge_dump\",\n", "    \"surge_dump_value\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "92b13810-3797-4c2e-9948-e1f711180825", "metadata": {"tags": []}, "outputs": [], "source": ["final_ssortment = final_ssortment.merge(item_df, on=[\"item_id\"], how=\"left\")\n", "final_ssortment = final_ssortment.merge(store_df, on=[\"facility_id\"], how=\"left\")\n", "final_ssortment = final_ssortment[columns]\n", "final_ssortment.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "543dc963-c842-4616-aeb2-3a8c01c74364", "metadata": {}, "outputs": [], "source": ["final_ssortment.groupby([\"date_\"]).agg({\"gmv\": \"sum\", \"qty\": \"sum\"})"]}, {"cell_type": "code", "execution_count": null, "id": "3133d348-6b3c-4410-bd37-64c6ff68b10d", "metadata": {}, "outputs": [], "source": ["alert_df = final_ssortment.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "e0ba3a71-0075-48e5-8580-71259965a449", "metadata": {}, "outputs": [], "source": ["city_summary = (\n", "    alert_df[(alert_df.date_ >= l8_date) & (alert_df.date_ <= l1_date)]\n", "    .groupby([\"date_\", \"city\", \"polygon_change_flag\", \"new_store_flag\", \"d_flag\"])\n", "    .agg(\n", "        {\n", "            \"correct_dump_value\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "agg_dumps = (\n", "    alert_df.groupby([\"date_\", \"city\"])\n", "    .agg(\n", "        {\n", "            \"gmv\": \"sum\",\n", "            \"qty\": \"sum\",\n", "            \"surge_dump_value\": \"sum\",\n", "            \"expiry_dump_value\": \"sum\",\n", "            \"wrong_dump_value\": \"sum\",\n", "            \"damage_dump_value\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "city_summary = city_summary.merge(agg_dumps, on=[\"date_\", \"city\"], how=\"left\")\n", "city_summary[\"dump_type\"] = np.where(\n", "    city_summary[\"new_store_flag\"] == 1,\n", "    \"new_store\",\n", "    np.where(\n", "        city_summary[\"d_flag\"] == 1,\n", "        \"Disruption\",\n", "        np.where(city_summary[\"polygon_change_flag\"] == 1, \"Polygon_change\", \"Normal\"),\n", "    ),\n", ")\n", "city_summary = city_summary.drop(columns={\"polygon_change_flag\", \"new_store_flag\", \"d_flag\"})\n", "city_pivot = city_summary.pivot_table(\n", "    values=[\"correct_dump_value\"],\n", "    index=[\n", "        \"date_\",\n", "        \"city\",\n", "        \"gmv\",\n", "        \"qty\",\n", "        \"wrong_dump_value\",\n", "        \"damage_dump_value\",\n", "        \"expiry_dump_value\",\n", "        \"surge_dump_value\",\n", "    ],\n", "    columns=[\"dump_type\"],\n", "    aggfunc=\"sum\",\n", ").reset_index()\n", "\n", "city_pivot.columns = [\"_\".join(map(str, col)) for col in city_pivot.columns]\n", "\n", "pan_india = (\n", "    city_pivot.groupby([\"date__\"])\n", "    .agg(\n", "        {\n", "            \"gmv_\": \"sum\",\n", "            \"qty_\": \"sum\",\n", "            \"surge_dump_value_\": \"sum\",\n", "            \"wrong_dump_value_\": \"sum\",\n", "            \"damage_dump_value_\": \"sum\",\n", "            \"expiry_dump_value_\": \"sum\",\n", "            \"correct_dump_value_Disruption\": \"sum\",\n", "            \"correct_dump_value_Normal\": \"sum\",\n", "            \"correct_dump_value_Polygon_change\": \"sum\",\n", "            \"correct_dump_value_new_store\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "\n", "pan_india[\"city_\"] = \"Pan_india\"\n", "final_pivot = pd.concat([city_pivot, pan_india]).fillna(0)\n", "# final_pivot['date__'] = final_pivot['date__'].astype(str)\n", "final_pivot.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a4eb1d45-a6ef-407c-9b1b-ecb10df5e4e8", "metadata": {}, "outputs": [], "source": ["l7d_agg = (\n", "    final_pivot.groupby([\"city_\"])\n", "    .agg(\n", "        {\n", "            \"gmv_\": \"sum\",\n", "            \"qty_\": \"sum\",\n", "            \"wrong_dump_value_\": \"sum\",\n", "            \"damage_dump_value_\": \"sum\",\n", "            \"expiry_dump_value_\": \"sum\",\n", "            \"surge_dump_value_\": \"sum\",\n", "            \"correct_dump_value_Disruption\": \"sum\",\n", "            \"correct_dump_value_Normal\": \"sum\",\n", "            \"correct_dump_value_Polygon_change\": \"sum\",\n", "            \"correct_dump_value_new_store\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "l7d_agg[\"date__\"] = \"Total\"\n", "final_pivot = pd.concat([final_pivot, l7d_agg])\n", "final_pivot.head()"]}, {"cell_type": "code", "execution_count": null, "id": "369f6a26-a2df-466f-8013-d5a3e1e901f6", "metadata": {}, "outputs": [], "source": ["final_alert_df = final_pivot.copy().fillna(0)\n", "\n", "final_alert_df[\"Net_dump %\"] = (\n", "    final_alert_df[\"damage_dump_value_\"]\n", "    + final_alert_df[\"correct_dump_value_Disruption\"]\n", "    + final_alert_df[\"correct_dump_value_Normal\"]\n", "    + final_alert_df[\"correct_dump_value_Polygon_change\"]\n", "    + final_alert_df[\"correct_dump_value_new_store\"]\n", "    + final_alert_df[\"wrong_dump_value_\"]\n", "    + final_alert_df[\"surge_dump_value_\"]\n", ") / final_alert_df[\"gmv_\"]\n", "final_alert_df[\"Net_dump ₹\"] = (\n", "    final_alert_df[\"damage_dump_value_\"]\n", "    + final_alert_df[\"correct_dump_value_Disruption\"]\n", "    + final_alert_df[\"correct_dump_value_Normal\"]\n", "    + final_alert_df[\"correct_dump_value_Polygon_change\"]\n", "    + final_alert_df[\"correct_dump_value_new_store\"]\n", "    + final_alert_df[\"wrong_dump_value_\"]\n", "    + final_alert_df[\"surge_dump_value_\"]\n", ")\n", "# final_alert_df['% Correct_dump'] = ((final_alert_df['correct_dump_value_Disruption']\n", "#                                      +final_alert_df['correct_dump_value_Normal']\n", "#                                      +final_alert_df['correct_dump_value_Polygon_change']\n", "#                                      +final_alert_df['correct_dump_value_new_store'])*1.00\n", "#                                     /final_alert_df['Net_dump']\n", "#                                    )\n", "final_alert_df[\"% Damage_dump\"] = (final_alert_df[\"damage_dump_value_\"]) / final_alert_df[\n", "    \"Net_dump ₹\"\n", "]\n", "final_alert_df[\"% incorrect_dump @ 9am\"] = (final_alert_df[\"wrong_dump_value_\"]) / final_alert_df[\n", "    \"Net_dump ₹\"\n", "]\n", "\n", "\n", "# final_alert_df['Correct_dump'] = (final_alert_df['correct_dump_value_Disruption']\n", "#                                  +final_alert_df['correct_dump_value_Normal']\n", "#                                  +final_alert_df['correct_dump_value_Polygon_change']\n", "#                                  +final_alert_df['correct_dump_value_new_store']\n", "#                                  +final_alert_df['surge_dump_value_'])\n", "\n", "final_alert_df[\"% New store\"] = np.clip(\n", "    (final_alert_df[\"correct_dump_value_new_store\"]) * 1.00 / (final_alert_df[\"Net_dump ₹\"]), 0, 1\n", ")\n", "final_alert_df[\"% Poly change\"] = np.clip(\n", "    (final_alert_df[\"correct_dump_value_Polygon_change\"]) * 1.00 / (final_alert_df[\"Net_dump ₹\"]),\n", "    0,\n", "    1,\n", ")\n", "final_alert_df[\"% Surge sales loss\"] = np.clip(\n", "    (final_alert_df[\"surge_dump_value_\"]) * 1.00 / (final_alert_df[\"Net_dump ₹\"]), 0, 1\n", ")\n", "final_alert_df[\"% disruption\"] = np.clip(\n", "    (final_alert_df[\"correct_dump_value_Disruption\"]) * 1.00 / (final_alert_df[\"Net_dump ₹\"]), 0, 1\n", ")\n", "final_alert_df[\"% Forecasting\"] = np.clip(\n", "    (\n", "        1.0\n", "        - final_alert_df[\"% New store\"]\n", "        - final_alert_df[\"% Poly change\"]\n", "        - final_alert_df[\"% disruption\"]\n", "        - final_alert_df[\"% Surge sales loss\"]\n", "        - final_alert_df[\"% incorrect_dump @ 9am\"]\n", "        - final_alert_df[\"% Damage_dump\"]\n", "    ),\n", "    0,\n", "    1,\n", ")\n", "final_alert_df = final_alert_df.fillna(0)\n", "\n", "tot_dump = (\n", "    final_alert_df.groupby([\"city_\"])\n", "    .agg({\"Net_dump ₹\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"Net_dump ₹\": \"10d_dump\"})\n", ")\n", "final_alert_df = final_alert_df.merge(tot_dump, on=[\"city_\"], how=\"left\")\n", "final_alert_df[\"% dump share\"] = (\n", "    final_alert_df[\"10d_dump\"] * 1.00 / final_alert_df[\"Net_dump ₹\"].sum()\n", ")\n", "final_alert_df.head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "32938cd7-efed-4f0d-b7f5-55af3dd8145c", "metadata": {"tags": []}, "outputs": [], "source": ["final_alert_df = (\n", "    final_alert_df[\n", "        [\n", "            \"city_\",\n", "            \"date__\",\n", "            \"Net_dump %\",\n", "            \"Net_dump ₹\",\n", "            \"% dump share\",\n", "            # '% Correct_dump',\n", "            \"% Forecasting\",\n", "            \"% incorrect_dump @ 9am\",\n", "            \"% Damage_dump\",\n", "            \"% Surge sales loss\",\n", "            \"% New store\",\n", "            \"% Poly change\",\n", "            \"% disruption\",\n", "        ]\n", "    ]\n", "    .copy()\n", "    .sort_values([\"% dump share\", \"date__\"], ascending=False)\n", ")\n", "final_alert_df = final_alert_df.drop(columns={\"% dump share\"})\n", "\n", "final_alert_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0f40387d-991e-4897-bb1e-0d57e182d284", "metadata": {}, "outputs": [], "source": ["final_alert_df[final_alert_df.city_ == \"Pan_india\"]"]}, {"cell_type": "code", "execution_count": null, "id": "dfa54c26-ecf4-4f89-a520-bd803a51d2f7", "metadata": {"tags": []}, "outputs": [], "source": ["final_alert_df[\"date__\"] = np.where(\n", "    final_alert_df[\"date__\"] == \"Total\",\n", "    final_alert_df[\"date__\"],\n", "    final_alert_df[\"date__\"].apply(lambda x: str(x)[0:10]),\n", ")\n", "\n", "final_alert_df[\"Net_dump %\"] = final_alert_df[\"Net_dump %\"].apply(\n", "    lambda x: str(np.round(x * 100, 2)) + \"%\"\n", ")\n", "\n", "final_alert_df[\"Net_dump ₹\"] = np.where(\n", "    final_alert_df[\"Net_dump ₹\"].between(0, 99999),\n", "    final_alert_df[\"Net_dump ₹\"].apply(lambda x: \"₹\" + str(np.round(x / 1000.0, 2)) + \" K\"),\n", "    np.where(\n", "        final_alert_df[\"Net_dump ₹\"].between(100000, 9999999),\n", "        final_alert_df[\"Net_dump ₹\"].apply(lambda x: \"₹\" + str(np.round(x / 100000.0, 2)) + \" L\"),\n", "        final_alert_df[\"Net_dump ₹\"].apply(\n", "            lambda x: \"₹\" + str(np.round(x / 10000000.0, 2)) + \" Cr\"\n", "        ),\n", "    ),\n", ")\n", "\n", "# final_alert_df['% Correct_dump'] = final_alert_df['% Correct_dump'].apply(\n", "#     lambda x: str(np.round(x * 100, 0)) + \"%\"\n", "# )\n", "\n", "final_alert_df[\"% Damage_dump\"] = final_alert_df[\"% Damage_dump\"].apply(\n", "    lambda x: str(np.round(x * 100, 0)) + \"%\"\n", ")\n", "\n", "final_alert_df[\"% incorrect_dump @ 9am\"] = final_alert_df[\"% incorrect_dump @ 9am\"].apply(\n", "    lambda x: str(np.round(x * 100, 0)) + \"%\"\n", ")\n", "\n", "# final_alert_df['Correct_dump'] = final_alert_df['Correct_dump'].apply(\n", "#     lambda x: str(np.round(x * 100, 2)) + \"%\"\n", "# )\n", "\n", "final_alert_df[\"% Forecasting\"] = final_alert_df[\"% Forecasting\"].apply(\n", "    lambda x: str(np.round(x * 100, 0)) + \"%\"\n", ")\n", "\n", "final_alert_df[\"% New store\"] = final_alert_df[\"% New store\"].apply(\n", "    lambda x: str(np.round(x * 100, 0)) + \"%\"\n", ")\n", "\n", "final_alert_df[\"% Poly change\"] = final_alert_df[\"% Poly change\"].apply(\n", "    lambda x: str(np.round(x * 100, 0)) + \"%\"\n", ")\n", "\n", "final_alert_df[\"% Surge sales loss\"] = final_alert_df[\"% Surge sales loss\"].apply(\n", "    lambda x: str(np.round(x * 100, 0)) + \"%\"\n", ")\n", "\n", "final_alert_df[\"% disruption\"] = final_alert_df[\"% disruption\"].apply(\n", "    lambda x: str(np.round(x * 100, 0)) + \"%\"\n", ")\n", "\n", "final_alert_df.head(10)"]}, {"cell_type": "markdown", "id": "317ecbf0-9390-4d9a-a1b0-4962c9cc81f4", "metadata": {"tags": []}, "source": ["## Sending City level alert"]}, {"cell_type": "code", "execution_count": null, "id": "57b2f7ff-1781-4bea-bc6a-96eff5bf213f", "metadata": {}, "outputs": [], "source": ["def send_alert(chnl):\n", "\n", "    import pencilbox as pb1\n", "\n", "    slack_channel = chnl\n", "\n", "    alert_df = final_alert_df.copy().iloc[0:72]\n", "\n", "    if alert_df.shape[0] > 0:\n", "        fig, ax = render_mpl_table(alert_df, header_columns=0)\n", "        fig.savefig(\"city dump summary.png\")\n", "        file_check_1 = \"./city dump summary.png\"\n", "        filepath_fig = file_check_1\n", "        channel = slack_channel\n", "        gsheet_link = \"https://docs.google.com/spreadsheets/d/17QbLjrFOfAVAtj6rTnT_Oe52Ghj1j9-gqRddn08QB0Y/edit?gid=2046915039#gid=2046915039\"\n", "        text = f\"\"\"Milk frontend dump report on city level (top 7) for L7D. \\n Please refer the sheet for cities \\n <{gsheet_link}|Link> \\n cc <@U06T927GCSW> <@U07185MUX32> <@U07E0MTBS2X> <@U05H3HFBT9C> \\n\n", "        \"\"\"\n", "    else:\n", "        print(\"Got an error\")\n", "    pb1.send_slack_message(channel=channel, text=text, files=[filepath_fig])"]}, {"cell_type": "code", "execution_count": null, "id": "9dbe6524-e278-4919-a2da-052365e2819b", "metadata": {"tags": []}, "outputs": [], "source": ["def render_mpl_table(\n", "    data,\n", "    col_width=3.25,\n", "    row_height=0.9,\n", "    font_size=14,\n", "    header_color=\"#00008B\",\n", "    medium_blue=\"#0c0cff\",\n", "    footer_color=\"#434343\",\n", "    grey=\"#a6a6a6\",\n", "    light_grey=\"#cccccc\",\n", "    light_yellow=\"#ffffe0\",\n", "    paly_yellow=\"#fffffa\",\n", "    dark_red=\"#ff9a9a\",\n", "    light_red=\"#ffb4b4\",\n", "    row_colors=[\n", "        \"#EFEFEF\",  # 0\n", "        \"#FFFFFF\",  # 1\n", "    ],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, cellLoc=\"center\", **kwargs\n", "    )\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        # print(k)\n", "        cell.set_edgecolor(edge_color)\n", "        cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"w\", fontsize=18)\n", "            cell.set_facecolor(header_color)\n", "        elif k[0] % 9 == 1:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\", fontsize=18)\n", "            cell.set_facecolor(grey)\n", "        # elif k[1] in (0,1):\n", "        #     cell.set_facecolor(light_grey)\n", "        #     cell.set_text_props(weight=\"bold\", fontsize=18)\n", "        elif k[1] == 5:\n", "            if float(data.iloc[k[0] - 1, k[1]].replace(\"%\", \"\")) <= 1:\n", "                cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "            elif float(data.iloc[k[0] - 1, k[1]].replace(\"%\", \"\")) <= 5:\n", "                cell.set_facecolor(light_red)\n", "            else:\n", "                cell.set_facecolor(dark_red)\n", "        elif k[1] == 6:\n", "            if float(data.iloc[k[0] - 1, k[1]].replace(\"%\", \"\")) <= 5:\n", "                cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "            elif float(data.iloc[k[0] - 1, k[1]].replace(\"%\", \"\")) <= 15:\n", "                cell.set_facecolor(light_red)\n", "            else:\n", "                cell.set_facecolor(dark_red)\n", "        # elif k[1] in (7,8,9,10,11):\n", "        #     cell.set_facecolor(light_yellow)\n", "        else:\n", "            cell.set_text_props(weight=\"normal\", color=\"black\", fontsize=15)\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "2b42d88e-01b2-43d8-8628-2b9d3d9eaca0", "metadata": {}, "outputs": [], "source": ["channel_list = [\"blinkit-perishables-core\", \"hyblink-stores-fresh\"]\n", "if current_hour <= 10:\n", "    for i in channel_list:\n", "        send_alert(i)"]}, {"cell_type": "code", "execution_count": null, "id": "09d633b7-c6c8-4470-803d-a96c5554f9ba", "metadata": {}, "outputs": [], "source": ["columns = [\n", "    \"city\",\n", "    \"facility_id\",\n", "    \"outlet_id\",\n", "    \"item_id\",\n", "    \"be_facility_id\",\n", "    \"date_\",\n", "    \"lp\",\n", "    \"damage_dump_value\",\n", "    \"correct_dump_value\",\n", "    \"wrong_dump_value\",\n", "    \"expiry_dump_value\",\n", "    \"gmv\",\n", "    \"week_no\",\n", "    \"polygon_change_flag\",\n", "    \"new_store_flag\",\n", "    \"d_flag\",\n", "    \"surge_dump_value\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "934a11a6-451c-463b-bca7-cf529db0b10f", "metadata": {}, "outputs": [], "source": ["dump_df = final_ssortment[\n", "    (final_ssortment[\"damage_dump_value\"] > 0) | (final_ssortment[\"expiry_dump_value\"] > 0)\n", "].copy()\n", "dump_df = dump_df[columns]"]}, {"cell_type": "code", "execution_count": null, "id": "83190f28-f0aa-4611-9b98-37e68ada07be", "metadata": {}, "outputs": [], "source": ["agg_data = (\n", "    final_ssortment[\n", "        (final_ssortment[\"damage_dump_value\"] == 0) | (final_ssortment[\"expiry_dump_value\"] == 0)\n", "    ]\n", "    .groupby(\n", "        [\n", "            \"city\",\n", "            \"item_id\",\n", "            \"date_\",\n", "        ]\n", "    )\n", "    .agg({\"gmv\": \"sum\"})\n", "    .reset_index()\n", ")\n", "agg_cols = [\n", "    \"facility_id\",\n", "    \"outlet_id\",\n", "    \"be_facility_id\",\n", "    \"lp\",\n", "    \"damage_dump_value\",\n", "    \"correct_dump_value\",\n", "    \"wrong_dump_value\",\n", "    \"expiry_dump_value\",\n", "    \"week_no\",\n", "    \"polygon_change_flag\",\n", "    \"new_store_flag\",\n", "    \"d_flag\",\n", "    \"surge_dump_value\",\n", "]\n", "\n", "for i in agg_cols:\n", "    agg_data[i] = 0"]}, {"cell_type": "code", "execution_count": null, "id": "fe20d8f4-59d1-4c9e-8912-d243f4f37a52", "metadata": {}, "outputs": [], "source": ["finl_data = pd.concat([dump_df, agg_data])"]}, {"cell_type": "code", "execution_count": null, "id": "99f1c605-84f3-477e-a107-9f322f8ff7fd", "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.to_sheets(\n", "        finl_data[finl_data.date_ >= l8_date][columns],\n", "        sheetid=\"17QbLjrFOfAVAtj6rTnT_Oe52Ghj1j9-gqRddn08QB0Y\",\n", "        sheetname=\"Raw Data\",\n", "    )\n", "except:\n", "    time.sleep(30)\n", "    pb.to_sheets(\n", "        finl_data[finl_data.date_ >= l8_date][columns],\n", "        sheetid=\"17QbLjrFOfAVAtj6rTnT_Oe52Ghj1j9-gqRddn08QB0Y\",\n", "        sheetname=\"Raw Data\",\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "31798455-2b26-4542-b1d1-dce90b18f037", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}