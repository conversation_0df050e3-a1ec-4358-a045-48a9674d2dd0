{"cells": [{"cell_type": "code", "execution_count": null, "id": "87390b30-37f2-48fb-839c-5070ccdc87c8", "metadata": {}, "outputs": [], "source": ["!pip install catboost\n", "!pip install awswrangler==3.9.0\n", "!pip install numpy==1.24.0\n", "!pip install matplotlib==3.8.4\n", "!pip install tabulate\n", "!pip install pymysql\n", "!pip install pandasql\n", "\n", "import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import awswrangler as wr\n", "from catboost import CatBoostRegressor\n", "\n", "from datetime import date, datetime, timedelta\n", "\n", "\n", "from tabulate import tabulate\n", "\n", "from pathlib import Path\n", "\n", "import pymysql\n", "\n", "import boto3\n", "import io\n", "\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "import requests\n", "from requests.exceptions import HTTPError\n", "\n", "import pandasql as ps\n", "\n", "from tqdm.notebook import tqdm\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "# CON_IMS = pb.get_connection(\"[Replica] RDS IMS\")\n", "\n", "# secrets = pb.get_secret(\"retail/po/db/po.db.credential\")\n", "# host_name = secrets.get(\"host\")\n", "# user_name = secrets.get(\"db_user\")\n", "# password = secrets.get(\"db_password\")\n", "# CON_PO = pymysql.connect(\n", "#     host=host_name, user=user_name, password=password, autocommit=True, local_infile=1\n", "# )\n", "\n", "# rpc_secret = pb.get_secret(\"retail/noto-reports/mysql/rpc-rds-read\")\n", "# host = rpc_secret.get(\"host\")\n", "# username = rpc_secret.get(\"username\")\n", "# password = rpc_secret.get(\"password\")\n", "# CON_RPC = pymysql.connect(host=host, user=username, password=password)\n", "\n", "\n", "# start_time = time.time()"]}, {"cell_type": "code", "execution_count": null, "id": "3d3e956a-14f3-4ed0-8d13-1bbe686256f6", "metadata": {}, "outputs": [], "source": ["s3_path = \"s3://prod-dse-projects/demand/frontend_demand_forecasting/frontend_leftover_model\""]}, {"cell_type": "code", "execution_count": null, "id": "98a326fa-18f8-406d-8d7d-3017f27a49a7", "metadata": {}, "outputs": [], "source": ["features = [\n", "    \"outlet_id\",\n", "    \"item_id\",\n", "    \"product_type_id\",\n", "    \"l2_id\",\n", "    \"shelf_life\",\n", "    \"month\",\n", "    \"day\",\n", "    \"day_of_week\",\n", "    \"is_weekend\",\n", "    \"current_inventory\",\n", "    \"morning_sales\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "9fbea6b7-0198-431f-86dc-93947fef0522", "metadata": {}, "outputs": [], "source": ["# date parameters set for indent calculation.\n", "date_selected = date.today() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "# date_selected_model = date(2025, 3, 6)\n", "previous_date_selected = date.today() + timedelta(hours=5.5) - timedelta(days=1)\n", "current_hour = (datetime.today() + timedelta(hours=5.5)).hour\n", "day_selected = pd.to_datetime(date_selected).strftime(\"%A\")\n", "\n", "forecast_date = date.today() + <PERSON><PERSON><PERSON>(days=2)\n", "forecast_day_selected = pd.to_datetime(forecast_date).strftime(\"%A\")"]}, {"cell_type": "code", "execution_count": null, "id": "78ed36bb-c35f-4726-8cf4-e3f63e51bc61", "metadata": {}, "outputs": [], "source": ["def read_model_from_s3(model_name, s3_path, local_models_root_path, run_date=\"no_change\"):\n", "    local_file_path = local_models_root_path.joinpath(model_name).as_posix()\n", "    if run_date != \"no_change\":\n", "        # restructing the run date for transfer_learning\n", "        s3_path = restructure_s3_path(s3_path, run_date)\n", "    path = s3_path + model_name\n", "    wr.s3.download(path=path, local_file=local_file_path)\n", "    # LOGGER.info(f'downloaded s3 file {path} into local file {local_file_path}')"]}, {"cell_type": "code", "execution_count": null, "id": "017947ad-82eb-4a3c-bb59-0c957e0a62fa", "metadata": {}, "outputs": [], "source": ["def write_file_to_s3(file_name, s3_path, local_file_root_path):\n", "    local_file_path = local_file_root_path.joinpath(file_name).as_posix()\n", "    path = s3_path + file_name\n", "    wr.s3.upload(local_file=local_file_path, path=path)"]}, {"cell_type": "code", "execution_count": null, "id": "85f57208-02b2-452b-b0e8-41adcde7c696", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        print(\n", "            f\"Read attempt: {attempt}... \\nRun started at {datetime.now() + timed<PERSON>ta(hours = 5.5)}\"\n", "        )\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "78b5b49e-7c47-4b66-8475-2fa99f24f7af", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, **kwargs):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except Exception as e:\n", "            print(e)\n", "            send_slack_alert(f\"`Forecast/Indent Upload failed due to some error \\n{e}\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "8d1396b9-c3a6-4f18-be8c-517a783ff591", "metadata": {}, "outputs": [], "source": ["def check_duplicates(df, cols, identifier):\n", "    if df.shape[0] != df[cols].drop_duplicates().shape[0]:\n", "        error = f\"\"\"*Alert*: Duplication found while extracting `{identifier}`\"\"\"\n", "        send_slack_alert(\n", "            f\":red_circle:*Alert*:red_circle:\\nDuplicates found in this df - {identifier}\"\n", "        )\n", "        df[\"key\"] = 1\n", "        df_duplicate = df.groupby(cols).agg({\"key\": \"sum\"}).reset_index()\n", "        tabulate_print(\n", "            pd.merge(\n", "                df,\n", "                df_duplicate[df_duplicate[\"key\"] > 1].drop(columns={\"key\"}),\n", "                on=cols,\n", "                how=\"inner\",\n", "            )\n", "        )\n", "    else:\n", "        pass"]}, {"cell_type": "code", "execution_count": null, "id": "796ea89c-be99-4ca1-a2be-b24d798bc9bf", "metadata": {}, "outputs": [], "source": ["def from_sheets(sheet_id, sheet_name):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            df = pb.from_sheets(sheet_id, sheet_name)\n", "\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pulled from sheet in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pulled from sheet in: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "2698a4ab-96cc-49b6-9bd1-7ffec60ad78a", "metadata": {}, "outputs": [], "source": ["def send_slack_alert(error_message):\n", "    slack_channel = \"dag-alerts-anchit\"\n", "    error_message = error_message + \"\\ncc <@U078DPW05T4>\"\n", "    try:\n", "        pb.send_slack_message(channel=slack_channel, text=error_message)\n", "    except:\n", "        print(error_message)"]}, {"cell_type": "code", "execution_count": null, "id": "c61b134f-5796-4029-9ded-6eb20ee1b4bf", "metadata": {}, "outputs": [], "source": ["def check_df_shape(df, df_name):\n", "    if df.shape[0] == 0:\n", "        send_slack_alert(\n", "            f\":red_circle:*Alert*:red_circle:\\nNull data returned from system while fetching {df_name}\"\n", "        )\n", "        print(f\"Null data returned from system while fetching {df_name}\")\n", "    else:\n", "        pass"]}, {"cell_type": "code", "execution_count": null, "id": "6e6cb6ed-08c8-49bb-bcb0-05794bfde4b7", "metadata": {}, "outputs": [], "source": ["def ignore_wrong_manual_input(df, file_name=\"\"):\n", "    cols = list(df.columns.values)\n", "    error_df = pd.DataFrame()\n", "    df[\"filter_tag\"] = 0\n", "    for col in cols:\n", "        error_df = error_df.append(df[(df[col].isna() == True) | (df[col] == \"\")][cols])\n", "        df[\"filter_tag\"] = df[\"filter_tag\"] + np.where(\n", "            (df[col].isna() == True) | (df[col] == \"\"), 1, 0\n", "        )\n", "    error_df = error_df.drop_duplicates()\n", "    df = df.drop_duplicates()\n", "    df = df[cols][df[\"filter_tag\"] == 0]\n", "    if error_df.shape[0] > 0:\n", "        # update.\n", "        error_df.to_csv(f\"\"\"{file_name}_wrong_entries_ignored.csv\"\"\")\n", "        pb.send_slack_message(\n", "            channel=\"table-updates-tanishq\",\n", "            text=f\"\"\"The below inputs for FnV indent are ignored for {file_name}: \\ncc <@U078DPW05T4>,<@U05CCTXLBU1>\"\"\",\n", "            files=[f\"\"\"{file_name}_wrong_entries_ignored.csv\"\"\"],\n", "        )\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "5f8b27c6-c04a-430f-b5bb-079614a6161f", "metadata": {}, "outputs": [], "source": ["def tabulate_print(df):\n", "    print(tabulate(df, headers=\"keys\", tablefmt=\"psql\"))"]}, {"cell_type": "code", "execution_count": null, "id": "18f0e3c2-6c02-45f3-b0af-26d38c5a804b", "metadata": {}, "outputs": [], "source": ["pd.options.mode.chained_assignment = None  # default='warn'\n", "pd.set_option(\"display.max_rows\", 50)\n", "pd.set_option(\"display.max_columns\", 50)"]}, {"cell_type": "code", "execution_count": null, "id": "0e282d76-4dfd-496c-b9cc-fe52808a33d9", "metadata": {}, "outputs": [], "source": ["model = CatBoostRegressor()\n", "i = 1\n", "while i != 0 and i < 100:\n", "    try:\n", "        model_date = date_selected - <PERSON><PERSON><PERSON>(days=i - 1)\n", "        read_model_from_s3(f\"\"\"{model_date}_feature1_leftover_catboost.cbm\"\"\", s3_path, Path(\"./\"))\n", "        model.load_model(f\"\"\"./{model_date}_feature1_leftover_catboost.cbm\"\"\")\n", "        print(f\"\"\"Using model trained on {model_date}\"\"\")\n", "        i = 0\n", "    except:\n", "        i = i + 1"]}, {"cell_type": "code", "execution_count": null, "id": "9a839a2a-d169-43a8-94e9-387bbdd91427", "metadata": {}, "outputs": [], "source": ["warehouse_base_sheet = from_sheets(\n", "    \"1_61PjTYeJSE_PmcVwmL4PiZMLRAXB4oij5Qyh-mlpXs\", \"Cluster Details\"\n", ")  # List of all clusters\n", "# warehouse_base_sheet = pd.read_csv(\"cluster_details.csv\")\n", "run_check_sheet = from_sheets(\n", "    \"1_61PjTYeJSE_PmcVwmL4PiZMLRAXB4oij5Qyh-mlpXs\", \"Run Check\"\n", ")  # List of clusters to run dag\n", "# run_check_sheet = pd.read_csv(\"run_check.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "25095761-3639-4b6d-894b-f70cac0265a6", "metadata": {}, "outputs": [], "source": ["# Base backend cluster list creation.\n", "warehouse_base_sheet[\"dag_type\"] = warehouse_base_sheet[\"dag_type\"].replace(\"\", np.nan).fillna(\"1\")\n", "\n", "warehouse_base_sheet[[\"is_dts\", \"is_trino\", \"dag_type\"]] = warehouse_base_sheet[\n", "    [\"is_dts\", \"is_trino\", \"dag_type\"]\n", "].astype(int)\n", "warehouse_base_sheet[\"meta\"] = warehouse_base_sheet[\"meta\"].astype(str)\n", "\n", "warehouse_base_sheet = (\n", "    warehouse_base_sheet[warehouse_base_sheet[\"dag_type\"].isin({1, 2})]\n", "    .reset_index()\n", "    .drop(columns={\"index\", \"dag_type\"})\n", ")\n", "\n", "warehouse_base_sheet[\"meta\"] = (\n", "    (warehouse_base_sheet[\"meta\"].str.replace(\"[\", \"\", regex=False)).str.replace(\n", "        \"]\", \"\", regex=True\n", "    )\n", ").str.replace(\"'\", \"\", regex=True)\n", "\n", "warehouse_base_sheet = pd.concat(\n", "    [\n", "        (warehouse_base_sheet[\"meta\"].str.split(\",\", expand=True)),\n", "        warehouse_base_sheet[[\"is_dts\"]],\n", "    ],\n", "    axis=1,\n", ")\n", "\n", "warehouse_base_sheet.columns = [\n", "    \"cluster\",\n", "    \"backend_facility_id\",\n", "    \"backend_hot_outlet_id\",\n", "    \"backend_outlet_id\",\n", "    \"is_dts\",\n", "]\n", "\n", "warehouse_base_sheet[[\"backend_facility_id\", \"backend_hot_outlet_id\", \"backend_outlet_id\"]] = (\n", "    warehouse_base_sheet[\n", "        [\"backend_facility_id\", \"backend_hot_outlet_id\", \"backend_outlet_id\"]\n", "    ].astype(int)\n", ")\n", "\n", "run_check_sheet = (\n", "    run_check_sheet[run_check_sheet[\"run_check\"].isin({\"Yes\"})]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "warehouse_base_sheet = pd.merge(\n", "    warehouse_base_sheet, run_check_sheet, on=\"cluster\", how=\"inner\"\n", ").drop(columns={\"run_check\"})\n", "\n", "warehouse_base_sheet"]}, {"cell_type": "code", "execution_count": null, "id": "ce225566-2a7c-4d20-9843-ae6e3c0123f3", "metadata": {}, "outputs": [], "source": ["warehouse_base = warehouse_base_sheet"]}, {"cell_type": "code", "execution_count": null, "id": "f89d16c7-fc3d-45ae-aba1-4bf266f3e977", "metadata": {}, "outputs": [], "source": ["def get_change_item_input(df):\n", "    try:\n", "        item_change = from_sheets(\"1_61PjTYeJSE_PmcVwmL4PiZMLRAXB4oij5Qyh-mlpXs\", \"change_item_id\")\n", "        # item_change = pd.read_csv(\"change_item_id.csv\")\n", "        item_change[\"start_date\"] = pd.to_datetime(item_change[\"start_date\"])\n", "        item_change[\"end_date\"] = pd.to_datetime(item_change[\"end_date\"])\n", "        item_change = item_change[\n", "            (item_change.start_date <= pd.to_datetime(date_selected))\n", "            & (item_change.end_date >= pd.to_datetime(date_selected))\n", "        ]\n", "\n", "        item_change[[\"backend_outlet_id_hot\", \"old_item_id\", \"new_item_id\"]] = item_change[\n", "            [\"backend_outlet_id_hot\", \"old_item_id\", \"new_item_id\"]\n", "        ].astype(int)\n", "\n", "        item_change = item_change[item_change.backend_outlet_id_hot.isin(df.backend_hot_outlet_id)]\n", "\n", "        check_duplicates(\n", "            item_change,\n", "            [\"backend_outlet_id_hot\", \"old_item_id\"],\n", "            \"Item change check\",\n", "        )\n", "\n", "        item_change = (\n", "            item_change[[\"backend_outlet_id_hot\", \"old_item_id\", \"new_item_id\"]]\n", "            .drop_duplicates()\n", "            .astype(int)\n", "        )\n", "\n", "        return item_change\n", "\n", "    except Exception as e:\n", "        send_slack_alert(\n", "            f\":red_circle:*Alert*:red_circle:\\nError while fetching item-change inputs \\n\\n{e}\"\n", "        )\n", "        print(e)\n", "        # print(kuch_to_fatta)\n", "\n", "\n", "item_change = get_change_item_input(warehouse_base)\n", "\n", "\n", "def initiate_change_item_id(df):\n", "    df = (\n", "        pd.merge(\n", "            df,\n", "            item_change,\n", "            left_on=[\"backend_hot_outlet_id\", \"item_id\"],\n", "            right_on=[\"backend_outlet_id_hot\", \"old_item_id\"],\n", "            how=\"left\",\n", "        )\n", "        .reset_index()\n", "        .drop(columns={\"backend_outlet_id_hot\", \"index\", \"old_item_id\"})\n", "    )\n", "    df[\"item_id\"] = np.where(df[\"new_item_id\"].isna(), df[\"item_id\"], df[\"new_item_id\"])\n", "    df = df.drop(columns={\"new_item_id\"})\n", "    df = df.drop_duplicates().reset_index().drop(columns={\"index\"})\n", "    df[\"item_id\"] = df[\"item_id\"].astype(int)\n", "    return df\n", "\n", "\n", "def get_cpc_outlet_transition_input(df):\n", "    try:\n", "        cpc_outlet_transition = from_sheets(\n", "            \"1_61PjTYeJSE_PmcVwmL4PiZMLRAXB4oij5Qyh-mlpXs\", \"cpc_transition_outlet\"\n", "        )\n", "        # cpc_outlet_transition = pd.read_csv(\"cpc_transition_outlet.csv\")\n", "        cpc_outlet_transition[\"start_date\"] = pd.to_datetime(cpc_outlet_transition[\"start_date\"])\n", "        cpc_outlet_transition[\"end_date\"] = pd.to_datetime(cpc_outlet_transition[\"end_date\"])\n", "        cpc_outlet_transition = cpc_outlet_transition[\n", "            (cpc_outlet_transition.start_date <= pd.to_datetime(date_selected))\n", "            & (cpc_outlet_transition.end_date >= pd.to_datetime(date_selected))\n", "        ]\n", "\n", "        cpc_outlet_transition[[\"old_backend_outlet_hot_id\", \"new_backend_outlet_hot_id\"]] = (\n", "            cpc_outlet_transition[\n", "                [\"old_backend_outlet_hot_id\", \"new_backend_outlet_hot_id\"]\n", "            ].astype(int)\n", "        )\n", "\n", "        cpc_outlet_transition = cpc_outlet_transition[\n", "            (cpc_outlet_transition.old_backend_outlet_hot_id.isin(df.backend_hot_outlet_id))\n", "            | (cpc_outlet_transition.new_backend_outlet_hot_id.isin(df.backend_hot_outlet_id))\n", "        ]\n", "\n", "        check_duplicates(\n", "            cpc_outlet_transition,\n", "            [\"old_backend_outlet_hot_id\", \"outlet_id\"],\n", "            \"Exiting DAG....Multiple new cpc received for same outlet. One outlet can be mapped to single cpc\",\n", "        )\n", "\n", "        cpc_outlet_transition = (\n", "            cpc_outlet_transition[\n", "                [\n", "                    \"outlet_id\",\n", "                    \"facility_id\",\n", "                    \"old_backend_outlet_hot_id\",\n", "                    \"new_backend_outlet_hot_id\",\n", "                ]\n", "            ]\n", "            .drop_duplicates()\n", "            .astype(int)\n", "        )\n", "        return cpc_outlet_transition\n", "\n", "    except Exception as e:\n", "        send_slack_alert(\n", "            f\":red_circle:*Alert*:red_circle:\\nError while fetching cpc transition inputs \\n\\n{e}\"\n", "        )\n", "        print(e)\n", "        # print(kuch_to_fatta)\n", "\n", "\n", "cpc_transition = get_cpc_outlet_transition_input(warehouse_base)\n", "\n", "add_transition_outlet = (\n", "    cpc_transition[\"outlet_id\"].drop_duplicates().reset_index().drop(columns={\"index\"})\n", ")\n", "add_transition_outlet_list = tuple(list(add_transition_outlet[\"outlet_id\"].unique()) + [-1] + [-2])\n", "\n", "\n", "def initiate_change_cpc_transition(df):\n", "    df = (\n", "        pd.merge(\n", "            df,\n", "            cpc_transition[\n", "                [\n", "                    \"old_backend_outlet_hot_id\",\n", "                    \"new_backend_outlet_hot_id\",\n", "                    \"outlet_id\",\n", "                ]\n", "            ],\n", "            left_on=[\"backend_hot_outlet_id\", \"outlet_id\"],\n", "            right_on=[\"old_backend_outlet_hot_id\", \"outlet_id\"],\n", "            how=\"left\",\n", "        )\n", "        .reset_index()\n", "        .drop(columns={\"index\", \"old_backend_outlet_hot_id\"})\n", "    )\n", "\n", "    df[\"backend_hot_outlet_id\"] = np.where(\n", "        df[\"new_backend_outlet_hot_id\"].isna(),\n", "        df[\"backend_hot_outlet_id\"],\n", "        df[\"new_backend_outlet_hot_id\"],\n", "    )\n", "\n", "    df = df.drop(columns={\"new_backend_outlet_hot_id\"})\n", "    df = df.drop_duplicates().reset_index().drop(columns={\"index\"})\n", "    df[\"backend_hot_outlet_id\"] = df[\"backend_hot_outlet_id\"].astype(int)\n", "    return df\n", "\n", "\n", "# CON_RPC.ping()\n", "# CON_PO.ping()\n", "\n", "\n", "def get_active_tea_tagged_assortment():\n", "    try:\n", "        assortment_df = read_sql_query(\n", "            f\"\"\"\n", "                    with pfom as (\n", "                select outlet_id\n", "                from po.physical_facility_outlet_mapping\n", "                where lake_active_record and active = 1 and ars_active = 1\n", "                group by 1\n", "            ),\n", "\n", "            bfom as (\n", "                select outlet_id, facility_id\n", "                from po.bulk_facility_outlet_mapping\n", "                where lake_active_record and active = true\n", "                group by 1,2\n", "            ),\n", "\n", "            co as (\n", "                select id as outlet_id, facility_id, business_type_id, name\n", "                from retail.console_outlet\n", "                where lake_active_record and  active = 1\n", "            ),\n", "\n", "            icd as (\n", "                select item_id, l0_id\n", "                from rpc.item_category_details\n", "                where lake_active_record and l0_id = 1487\n", "            ),\n", "\n", "            iotm as (\n", "                select tag_value as backend_outlet_id, outlet_id, item_id\n", "                from rpc.item_outlet_tag_mapping \n", "                where lake_active_record and active = 1 and tag_type_id = 8\n", "            ),\n", "\n", "            pfma as (\n", "                select facility_id, item_id\n", "                from rpc.product_facility_master_assortment\n", "                where lake_active_record and active = 1 and master_assortment_substate_id in (1)\n", "            ),\n", "\n", "\n", "            dt as (\n", "                select iotm.backend_outlet_id, co1.facility_id as backend_facility_id, bfom.facility_id as bfom_backend_facility_id, iotm.outlet_id, co.facility_id, iotm.item_id, 'derived_table_yes' as derived_table\n", "                from iotm\n", "                inner join co on co.outlet_id = iotm.outlet_id and business_type_id = 7 and lower(name) not like ('%%dummy%%')\n", "                inner join pfma on pfma.facility_id = co.facility_id and pfma.item_id = iotm.item_id\n", "                inner join pfom on pfom.outlet_id = iotm.outlet_id\n", "                inner join co as co1 on co1.outlet_id = cast(iotm.backend_outlet_id as int)\n", "                left join bfom on bfom.outlet_id = iotm.outlet_id and co1.facility_id = bfom.facility_id\n", "                inner join icd on icd.item_id = iotm.item_id\n", "                where co1.facility_id in {tuple(list(warehouse_base.backend_facility_id)+[-1]+[-2])}\n", "                or iotm.outlet_id in {add_transition_outlet_list}\n", "\n", "            )\n", "\n", "            select * from dt\n", "\n", "\n", "                \"\"\",\n", "            CON_TRINO,\n", "        )\n", "\n", "        assortment_df = (\n", "            assortment_df[\n", "                assortment_df[\"backend_facility_id\"] == assortment_df[\"bfom_backend_facility_id\"]\n", "            ]\n", "            .reset_index()\n", "            .drop(columns={\"index\"})\n", "        )\n", "        assortment_df = assortment_df[\n", "            [\n", "                \"item_id\",\n", "                \"facility_id\",\n", "                \"outlet_id\",\n", "                \"backend_facility_id\",\n", "                \"backend_outlet_id\",\n", "            ]\n", "        ].astype(int)\n", "\n", "        assortment_df = assortment_df.rename(columns={\"backend_outlet_id\": \"backend_hot_outlet_id\"})\n", "\n", "        base_assortment = initiate_change_cpc_transition(assortment_df)\n", "\n", "        base_assortment = base_assortment[\n", "            base_assortment.backend_hot_outlet_id.isin(warehouse_base.backend_hot_outlet_id)\n", "        ]\n", "\n", "        print(\n", "            (\n", "                pd.merge(\n", "                    base_assortment.groupby([\"backend_hot_outlet_id\"])\n", "                    .agg({\"item_id\": pd.Series.nunique})\n", "                    .rename(columns={\"item_id\": \"Assortment Count\"}),\n", "                    warehouse_base[[\"backend_hot_outlet_id\", \"cluster\"]],\n", "                    on=\"backend_hot_outlet_id\",\n", "                    how=\"inner\",\n", "                ).drop(columns={\"backend_hot_outlet_id\"})\n", "            )[[\"cluster\", \"Assortment Count\"]]\n", "        )\n", "\n", "        return base_assortment\n", "\n", "    except Exception as e:\n", "        send_slack_alert(\n", "            f\":red_circle:*Alert*:red_circle:\\nError while fetching the assortment \\n\\n{e}\"\n", "        )\n", "        print(e)\n", "\n", "\n", "active_tea_tagged_assortment = get_active_tea_tagged_assortment()\n", "active_tea_tagged_assortment = (\n", "    active_tea_tagged_assortment[\n", "        [\n", "            \"item_id\",\n", "            \"facility_id\",\n", "            \"outlet_id\",\n", "            \"backend_facility_id\",\n", "            \"backend_hot_outlet_id\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "active_tea_tagged_assortment = initiate_change_item_id(active_tea_tagged_assortment)\n", "# active_tea_tagged_assortment = initiate_change_outlet_id(active_tea_tagged_assortment)\n", "\n", "check_duplicates(\n", "    active_tea_tagged_assortment,\n", "    [\"outlet_id\", \"item_id\"],\n", "    \"active_tea_tagged_assortment\",\n", ")\n", "\n", "active_tea_tagged_assortment = active_tea_tagged_assortment[\n", "    [\n", "        \"item_id\",\n", "        \"facility_id\",\n", "        \"outlet_id\",\n", "        \"backend_facility_id\",\n", "        \"backend_hot_outlet_id\",\n", "    ]\n", "].astype(int)\n", "\n", "dts_map = pd.merge(\n", "    active_tea_tagged_assortment,\n", "    warehouse_base,\n", "    on=[\"backend_hot_outlet_id\"],\n", "    how=\"inner\",\n", ")[[\"backend_hot_outlet_id\", \"backend_outlet_id\", \"outlet_id\", \"item_id\", \"is_dts\"]]\n", "\n", "item_id_list = tuple(\n", "    list(active_tea_tagged_assortment[\"item_id\"].unique().astype(int))\n", "    + list(item_change[\"old_item_id\"].unique().astype(int))\n", "    + list(item_change[\"new_item_id\"].unique().astype(int))\n", "    + [-1]\n", "    + [-2]\n", ")\n", "outlet_id_list = tuple(\n", "    list(active_tea_tagged_assortment[\"outlet_id\"].unique().astype(int))\n", "    # + list(outlet_change[\"old_outlet_id\"].unique().astype(int))\n", "    # + list(outlet_change[\"new_outlet_id\"].unique().astype(int))\n", "    + [-1]\n", "    + [-2]\n", ")\n", "\n", "# CON_RPC.close()\n", "# CON_PO.close()\n", "\n", "\n", "def get_category_input(df):\n", "    try:\n", "        category_input = from_sheets(\n", "            \"1_61PjTYeJSE_PmcVwmL4PiZMLRAXB4oij5Qyh-mlpXs\", \"assortment_input\"\n", "        )\n", "        # category_input = pd.read_csv(\"assortment_input.csv\")\n", "        category_input = ignore_wrong_manual_input(category_input, \"Fixed Indent\")\n", "        category_input[\"start_date\"] = pd.to_datetime(category_input[\"start_date\"])\n", "        category_input[\"end_date\"] = pd.to_datetime(category_input[\"end_date\"])\n", "        # category_input = category_input[\n", "        #     category_input.end_date >= pd.to_datetime(date_selected)\n", "        # ]\n", "\n", "        category_input[\"backend_outlet_id_hot\"] = category_input[\"backend_outlet_id_hot\"].astype(\n", "            int\n", "        )\n", "\n", "        category_input = category_input[\n", "            category_input.backend_outlet_id_hot.isin(df.backend_outlet_id)\n", "        ]\n", "\n", "        category_input[\"date\"] = list(\n", "            map(\n", "                lambda x, y: pd.date_range(start=x, end=y),\n", "                category_input[\"start_date\"],\n", "                category_input[\"end_date\"],\n", "            )\n", "        )\n", "        category_input = (\n", "            category_input.explode(\"date\")\n", "            .drop([\"start_date\", \"end_date\"], axis=1)\n", "            .reset_index()\n", "            .drop(columns={\"index\"})\n", "        )\n", "\n", "        category_input[\"date\"] = pd.to_datetime(category_input[\"date\"])\n", "        category_input[\"date_of_consumption\"] = category_input[\"date\"] + <PERSON><PERSON><PERSON>(days=2)\n", "\n", "        category_input[\"date_of_consumption\"] = pd.to_datetime(\n", "            category_input[\"date_of_consumption\"]\n", "        )\n", "\n", "        category_input = category_input[\n", "            category_input[\"date_of_consumption\"] > pd.to_datetime(date_selected)\n", "        ]\n", "\n", "        category_input = category_input[\n", "            [\"date_of_consumption\", \"backend_outlet_id_hot\", \"item_id\", \"indent_quantity\"]\n", "        ].drop_duplicates()\n", "\n", "        category_input[[\"backend_outlet_id_hot\", \"item_id\", \"indent_quantity\"]] = category_input[\n", "            [\"backend_outlet_id_hot\", \"item_id\", \"indent_quantity\"]\n", "        ].astype(int)\n", "        category_input = (\n", "            category_input.groupby([\"date_of_consumption\", \"backend_outlet_id_hot\", \"item_id\"])\n", "            .agg({\"indent_quantity\": \"max\"})\n", "            .reset_index()\n", "            .rename(columns={\"indent_quantity\": \"fixed_indent_quantity\"})\n", "        )\n", "\n", "        category_input = category_input.rename(\n", "            columns={\"backend_outlet_id_hot\": \"backend_hot_outlet\"}\n", "        )\n", "\n", "        category_input[\"manual_indent_flag\"] = 1\n", "        return category_input\n", "\n", "    except Exception as e:\n", "        send_slack_alert(\n", "            f\":red_circle:*Alert*:red_circle:\\nError while fetching category inputs (cluster-item fixed quantity) \\n\\n{e}\"\n", "        )\n", "        print(e)\n", "        # print(kuch_to_fatta)\n", "\n", "\n", "fixed_indent_category_input = get_category_input(warehouse_base)\n", "\n", "# def get_data_science_demand_forecast():\n", "#     try:\n", "#         data_science_demand_forecast_sql = f\"\"\"\n", "#             with ff as (\n", "#                 select\n", "#                 *\n", "#                 from ds_etls.demand_forecast_item_ordering_min_max_quantity\n", "#                 where current_replenishment_ts_ist = current_date + interval '2' day\n", "\n", "#             )\n", "\n", "#             select facility_id, outlet_id, item_id, max(cast(max_qty as int)) as t2_ds_forecast\n", "#             from ff\n", "#             where item_id in {item_id_list}\n", "#             and outlet_id in {outlet_id_list}\n", "#             group by 1,2,3\n", "#             \"\"\"\n", "#         data_science_demand_forecast = read_sql_query(\n", "#             data_science_demand_forecast_sql, CON_TRINO\n", "#         )\n", "#         check_df_shape(data_science_demand_forecast, \"data_science_demand_forecast\")\n", "#         check_duplicates(\n", "#             data_science_demand_forecast,\n", "#             [\"outlet_id\", \"item_id\"],\n", "#             \"data_science_demand_forecast\",\n", "#         )\n", "\n", "#         return data_science_demand_forecast\n", "\n", "#     except Exception as e:\n", "#         send_slack_alert(\n", "#             f\":red_circle:*Alert*:red_circle:\\nError while fetching the forecast (from ds table) \\n\\n{e}\"\n", "#         )\n", "#         print(e)\n", "\n", "\n", "def get_data_science_demand_forecast_t3():\n", "    try:\n", "        data_science_demand_forecast_sql = f\"\"\"\n", "            with ff as (\n", "                select \n", "                    *\n", "                from ds_etls.demand_forecast_item_ordering_min_max_quantity_t_plus_3\n", "                where current_replenishment_ts_ist = current_date + interval '3' day\n", "\n", "            )\n", "\n", "            select facility_id, outlet_id, item_id, max(cast(max_qty as int)) as t3_ds_forecast\n", "            from ff\n", "            where item_id in {item_id_list}\n", "            and outlet_id in {outlet_id_list}\n", "            group by 1,2,3\n", "            \"\"\"\n", "        data_science_demand_forecast = read_sql_query(data_science_demand_forecast_sql, CON_TRINO)\n", "        check_df_shape(data_science_demand_forecast, \"data_science_demand_forecast\")\n", "        check_duplicates(\n", "            data_science_demand_forecast,\n", "            [\"outlet_id\", \"item_id\"],\n", "            \"data_science_demand_forecast\",\n", "        )\n", "\n", "        return data_science_demand_forecast\n", "\n", "    except Exception as e:\n", "        send_slack_alert(\n", "            f\":red_circle:*Alert*:red_circle:\\nError while fetching the forecast (from t3 ds table) \\n\\n{e}\"\n", "        )\n", "        print(e)\n", "\n", "\n", "def get_previous_day_forecast():\n", "    try:\n", "        previous_day_forecast_sql = f\"\"\"\n", "            with max_update as (\n", "                select\n", "                    date_of_consumption,\n", "                    backend_hot_outlet,\n", "                    max(updated_at_ist) max_upd\n", "                from\n", "                    supply_etls.fnv_indent_forecasts_t_plus_n\n", "                where\n", "                    date_ist >= current_date - interval '2' day    \n", "                    and date_ist < current_date\n", "                group by 1,2\n", "                )\n", "                select\n", "                    forec.date_of_consumption,\n", "                    cluster,\n", "                    forec.backend_hot_outlet backend_hot_outlet_id,\n", "                    outlet_id,\n", "                    item_id,\n", "                    sum(t3_ds_forecast) forecast\n", "                from\n", "                    supply_etls.fnv_indent_forecasts_t_plus_n forec\n", "                join\n", "                    max_update mu\n", "                    on forec.date_of_consumption = mu.date_of_consumption\n", "                    and forec.backend_hot_outlet = mu.backend_hot_outlet\n", "                    and forec.updated_at_ist = mu.max_upd\n", "\n", "                where\n", "                    date_ist >= current_date - interval '2' day    \n", "                    and date_ist < current_date\n", "                    and forec.item_id in {item_id_list}\n", "                    and forec.outlet_id in {outlet_id_list}\n", "                group by 1,2,3,4,5\n", "\n", "            \"\"\"\n", "        previous_day_forecast = read_sql_query(previous_day_forecast_sql, CON_TRINO)\n", "        check_df_shape(previous_day_forecast, \"previous_day_forecast\")\n", "\n", "        #             previous_day_forecast_sql = f\"\"\"\n", "        #                 with ff as (\n", "        #                     select * from ds_etls.demand_forecast_item_ordering_min_max_quantity_log\n", "        #                     where current_replenishment_ts_ist = current_date + interval '1' day\n", "        #                 )\n", "\n", "        #                 select ff.outlet_id, ff.item_id, ff.max_qty as t1_forecast\n", "        #                 from ff\n", "        #                 inner join (\n", "        #                     select outlet_id, item_id, max(updated_at_ist) as updated_at_ist from ff\n", "        #                     where item_id in {item_id_list}\n", "        #                     and outlet_id in {outlet_id_list}\n", "        #                     group by 1,2\n", "        #                 ) f on ff.outlet_id = f.outlet_id and ff.item_id = f.item_id and ff.updated_at_ist = f.updated_at_ist\n", "\n", "        #                 \"\"\"\n", "        #             previous_day_forecast = read_sql_query(previous_day_forecast_sql, CON_TRINO)\n", "        #             check_df_shape(previous_day_forecast, \"previous_day_forecast\")\n", "        check_duplicates(\n", "            previous_day_forecast,\n", "            [\"date_of_consumption\", \"outlet_id\", \"item_id\"],\n", "            \"previous_day_forecast\",\n", "        )\n", "\n", "        previous_day_forecast = initiate_change_item_id(previous_day_forecast)\n", "        # previous_day_forecast = initiate_change_outlet_id(previous_day_forecast)\n", "        previous_day_forecast[\"date_of_consumption\"] = pd.to_datetime(\n", "            previous_day_forecast[\"date_of_consumption\"]\n", "        )\n", "        previous_day_forecast_t1 = previous_day_forecast[\n", "            previous_day_forecast[\"date_of_consumption\"]\n", "            == pd.to_datetime(date_selected + timed<PERSON>ta(days=1))\n", "        ].reset_index(drop=True)\n", "        previous_day_forecast_t2 = previous_day_forecast[\n", "            previous_day_forecast[\"date_of_consumption\"]\n", "            == pd.to_datetime(date_selected + timed<PERSON>ta(days=2))\n", "        ].reset_index(drop=True)\n", "        previous_day_forecast_t1 = (\n", "            previous_day_forecast_t1.groupby([\"backend_hot_outlet_id\", \"item_id\", \"outlet_id\"])\n", "            .agg({\"forecast\": \"sum\"})\n", "            .reset_index()\n", "            .rename(columns={\"forecast\": \"t1_forecast\"})\n", "        )\n", "        previous_day_forecast_t2 = (\n", "            previous_day_forecast_t2.groupby([\"backend_hot_outlet_id\", \"item_id\", \"outlet_id\"])\n", "            .agg({\"forecast\": \"sum\"})\n", "            .reset_index()\n", "            .rename(columns={\"forecast\": \"t2_forecast\"})\n", "        )\n", "        return previous_day_forecast_t1, previous_day_forecast_t2\n", "\n", "    except Exception as e:\n", "        send_slack_alert(\n", "            f\":red_circle:*Alert*:red_circle:\\nError while fetching the previous day forecast (item-outlet level) \\n\\n{e}\"\n", "        )\n", "        print(e)\n", "\n", "\n", "def get_previous_day_forecast_overall():\n", "    try:\n", "        previous_day_forecast_overall_sql = f\"\"\"\n", "        with max_update as (\n", "            select\n", "                date_of_consumption,\n", "                backend_hot_outlet,\n", "                max(updated_at_ist) max_upd\n", "            from\n", "                supply_etls.fnv_indent_ordering_t_plus_n\n", "            where\n", "                date_ist >= current_date - interval '2' day    \n", "                and date_ist < current_date\n", "            group by 1,2\n", "            )\n", "            select\n", "                indent.date_of_consumption,\n", "                cluster,\n", "                indent.backend_hot_outlet backend_hot_outlet_id,\n", "                item_id,\n", "                sum(final_indent_qty) indent,\n", "                sum(t3_ds_forecast) forecast\n", "            from\n", "                supply_etls.fnv_indent_ordering_t_plus_n indent\n", "            join\n", "                max_update mu\n", "                on indent.date_of_consumption = mu.date_of_consumption\n", "                and indent.backend_hot_outlet = mu.backend_hot_outlet\n", "                and indent.updated_at_ist = mu.max_upd\n", "            where\n", "                date_ist >= current_date - interval '2' day    \n", "                and date_ist < current_date\n", "                and indent.backend_hot_outlet in {tuple(list(warehouse_base.backend_hot_outlet_id)+[-1]+[-2])}\n", "            group by 1,2,3,4\n", "        \"\"\"\n", "\n", "        previous_day_forecast_overall = read_sql_query(previous_day_forecast_overall_sql, CON_TRINO)\n", "        check_df_shape(previous_day_forecast_overall, \"previous_day_forecast_overall\")\n", "        check_duplicates(\n", "            previous_day_forecast_overall,\n", "            [\"backend_hot_outlet_id\", \"item_id\"],\n", "            \"previous_day_forecast_overall\",\n", "        )\n", "\n", "        previous_day_forecast_overall = initiate_change_item_id(previous_day_forecast_overall)\n", "        previous_day_forecast_overall[\"date_of_consumption\"] = pd.to_datetime(\n", "            previous_day_forecast_overall[\"date_of_consumption\"]\n", "        )\n", "        previous_day_forecast_overall_t1 = previous_day_forecast_overall[\n", "            previous_day_forecast_overall[\"date_of_consumption\"]\n", "            == pd.to_datetime(date_selected + timed<PERSON>ta(days=1))\n", "        ].reset_index(drop=True)\n", "        previous_day_forecast_overall_t2 = previous_day_forecast_overall[\n", "            previous_day_forecast_overall[\"date_of_consumption\"]\n", "            == pd.to_datetime(date_selected + timed<PERSON>ta(days=2))\n", "        ].reset_index(drop=True)\n", "        previous_day_forecast_overall_t1 = (\n", "            previous_day_forecast_overall_t1.groupby([\"backend_hot_outlet_id\", \"item_id\"])\n", "            .agg({\"forecast\": \"sum\", \"indent\": \"sum\"})\n", "            .reset_index()\n", "            .rename(columns={\"forecast\": \"t1_forecast\", \"indent\": \"t1_indent\"})\n", "        )\n", "        previous_day_forecast_overall_t2 = (\n", "            previous_day_forecast_overall_t2.groupby([\"backend_hot_outlet_id\", \"item_id\"])\n", "            .agg({\"forecast\": \"sum\", \"indent\": \"sum\"})\n", "            .reset_index()\n", "            .rename(columns={\"forecast\": \"t2_forecast\", \"indent\": \"t2_indent\"})\n", "        )\n", "        return previous_day_forecast_overall_t1, previous_day_forecast_overall_t2\n", "\n", "    except Exception as e:\n", "        send_slack_alert(\n", "            f\":red_circle:*Alert*:red_circle:\\nError while fetching the previous day forecast and indent (item-cluster level) \\n\\n{e}\"\n", "        )\n", "        print(e)\n", "        # print(kuch_to_fatta)\n", "\n", "\n", "def get_item_details():\n", "    try:\n", "        item_details = read_sql_query(\n", "            f\"\"\"\n", "                            with pp as (\n", "                                select * from rpc.product_product\n", "                                where active = 1 and approved = 1\n", "                            )\n", "\n", "                            select pp.item_id, pp.shelf_life, pp.name as item_name\n", "                            from pp\n", "                            inner join (select item_id, max(id) as id from pp group by 1) p on pp.item_id = p.item_id and pp.id = p.id\n", "                            where pp.item_id in {item_id_list}\n", "                        \"\"\",\n", "            CON_TRINO,\n", "        )\n", "\n", "        check_df_shape(item_details, \"item_details\")\n", "\n", "        return item_details\n", "\n", "    except Exception as e:\n", "        send_slack_alert(\n", "            f\":red_circle:*Alert*:red_circle:\\nError while fetching item details \\n\\n{e}\"\n", "        )\n", "        print(e)\n", "        # print(kuch_to_fatta)\n", "\n", "\n", "item_details = get_item_details()\n", "\n", "\n", "def get_outlet_details():\n", "    try:\n", "        outlet_details = read_sql_query(\n", "            f\"\"\"\n", "                            with co as (\n", "                                select * from retail.console_outlet\n", "                                where active = 1\n", "                            )\n", "\n", "                            select co.id as outlet_id, co.business_type_id, co.name as outlet_name\n", "                            from co\n", "                            where co.id in {tuple(list(outlet_id_list) + list(warehouse_base.backend_outlet_id.astype(int)) + list(warehouse_base.backend_hot_outlet_id.astype(int)))}\n", "                        \"\"\",\n", "            CON_TRINO,\n", "        )\n", "\n", "        check_df_shape(outlet_details, \"outlet_details\")\n", "\n", "        return outlet_details\n", "\n", "    except Exception as e:\n", "        send_slack_alert(\n", "            f\":red_circle:*Alert*:red_circle:\\nError while fetching outlet details \\n\\n{e}\"\n", "        )\n", "        print(e)\n", "        # print(kuch_to_fatta)\n", "\n", "\n", "outlet_details = get_outlet_details()\n", "\n", "\n", "def get_evening_sales():\n", "    try:\n", "        max_ims_inv = read_sql_query(\n", "            \"\"\"select max(updated_at) as inventory_upd from ims.ims_item_inventory where active = 1\n", "           \"\"\",\n", "            CON_TRINO,\n", "        )\n", "        max_ims_inv[\"inventory_upd\"] = pd.to_datetime(max_ims_inv[\"inventory_upd\"])\n", "        inventory_hour = (max_ims_inv[\"inventory_upd\"][0] + timedelta(hours=5.5)).strftime(\"%H\")\n", "\n", "        sales_avail_df = read_sql_query(\n", "            f\"\"\"\n", "\n", "            with his as (\n", "                                select * from supply_etls.hourly_inventory_snapshots\n", "                                where date_ist >= current_date - interval '14' day and date_ist < current_date\n", "                                and outlet_id in {outlet_id_list} and item_id in {item_id_list}\n", "                                and extract(hour from updated_at_ist) between {int(inventory_hour)} and 22\n", "                            ),\n", "\n", "                            his_core as (\n", "                                select date_ist as date_, extract(hour from updated_at_ist) as hour_, outlet_id, item_id, current_inventory,\n", "                                        case when current_inventory > 0 then 1 else 0 end as inv_flag\n", "                                from his                                \n", "                            ),\n", "\n", "                            ass_base as (\n", "                                select outlet_id, item_id from his_core group by 1,2\n", "                            ),\n", "\n", "                            ip as (\n", "                                select item_id, product_id, multiplier\n", "                                from dwh.dim_item_product_offer_mapping\n", "                                where is_current\n", "                                and item_id in {item_id_list}\n", "                            ),\n", "\n", "                            fs as (\n", "                                select cart_checkout_ts_ist, date(cart_checkout_ts_ist) as date_, extract(hour from cart_checkout_ts_ist) as hour_, outlet_id, product_id, cart_id, procured_quantity\n", "                                from dwh.fact_sales_order_item_details\n", "                                where order_create_dt_ist >= current_date - interval '14' day and order_create_dt_ist < current_date\n", "                                and cart_checkout_ts_ist >= current_date - interval '14' day and cart_checkout_ts_ist < current_date\n", "                                and order_current_status = 'DELIVERED'\n", "                                and outlet_id in {outlet_id_list}\n", "                                and extract(hour from order_create_ts_ist) between {int(inventory_hour)} and 22\n", "                            ),\n", "\n", "                            sb as (\n", "                                select date_, hour_, outlet_id, item_id, sum(procured_quantity * multiplier) as qty_sold\n", "                                from fs\n", "                                inner join ip on ip.product_id = fs.product_id\n", "                                group by 1,2,3,4\n", "                            ),\n", "\n", "\n", "                            date_hour_base as (\n", "                                select d.date as date_, hour_, outlet_id, item_id\n", "                                from dwh.dim_date d\n", "                                cross join (select number hour_ from unnest(sequence({int(inventory_hour)},22)) as t(number))\n", "                                cross join ass_base\n", "                                where d.date between current_date - interval '14' day and current_date - interval '1' day\n", "                            ),\n", "\n", "                            agg_sales as (\n", "                                select dh.date_, dh.hour_, dh.outlet_id, dh.item_id, \n", "                                        case when (inv_flag is null and qty_sold > 0) or (inv_flag = 1) then 1 else 0 end as inv_flag, qty_sold\n", "                                from date_hour_base dh\n", "                                left join his_core on his_core.outlet_id = dh.outlet_id and his_core.item_id = dh.item_id and his_core.date_ = dh.date_ and his_core.hour_ = dh.hour_\n", "                                left join sb on sb.outlet_id = dh.outlet_id and sb.item_id = dh.item_id and sb.date_ = dh.date_ and sb.hour_ = dh.hour_\n", "                            ),\n", "\n", "                            avail_sales as (\n", "                                select date_, outlet_id, item_id, sum(case when inv_flag > 0 then 1 end) * 1.00 / count(hour_) as avail, coalesce(sum(qty_sold),0) as qty_sold\n", "                                from agg_sales\n", "                                group by 1,2,3\n", "                            )\n", "\n", "                            select outlet_id, item_id, avg(qty_sold) as avg_sales\n", "                            from avail_sales\n", "                            where avail > 0.5\n", "                            group by 1,2\n", "\n", "                        \"\"\",\n", "            CON_TRINO,\n", "        )\n", "\n", "        check_df_shape(sales_avail_df, \"sales_avail_df\")\n", "\n", "        return sales_avail_df\n", "\n", "    except Exception as e:\n", "        send_slack_alert(\n", "            f\":red_circle:*Alert*:red_circle:\\nError while fetching evening sales and availability \\n\\n{e}\"\n", "        )\n", "        print(e)\n", "        # print(kuch_to_fatta)\n", "\n", "\n", "sales_avail_df = get_evening_sales()\n", "\n", "\n", "def get_blinkit_open_po():\n", "    try:\n", "        open_po_df = read_sql_query(\n", "            f\"\"\"\n", "                            SELECT\n", "            p.outlet_id,\n", "            (p.issue_date + INTERVAL '330' MINUTE) AS issue_datetime,\n", "            (ps.schedule_date_time + INTERVAL '330' MINUTE) AS schedule_datetime,\n", "            ps.schedule_date_time + INTERVAL '330' MINUTE AS schedule_date,\n", "            poi.item_id,\n", "            poi.units_ordered AS open_po_qty\n", "        FROM\n", "            (SELECT * FROM po.purchase_order WHERE created_at >= current_date - INTERVAL '1' DAY) p\n", "        INNER JOIN\n", "            (SELECT * FROM po.purchase_order_items WHERE created_at >= current_date - INTERVAL '1' DAY) poi\n", "        ON\n", "            poi.po_id = p.id\n", "        LEFT JOIN\n", "            po.po_schedule ps\n", "        ON\n", "            ps.po_id_id = p.id\n", "        INNER JOIN\n", "            po.purchase_order_status posa\n", "        ON\n", "            posa.po_id = p.id\n", "        INNER JOIN\n", "            po.purchase_order_state posta\n", "        ON\n", "            posta.id = posa.po_state_id\n", "        LEFT JOIN\n", "            (SELECT * FROM po.po_grn WHERE insert_ds_ist >= cast(current_date - INTERVAL '1' DAY as varchar)) pg\n", "        ON\n", "            pg.item_id = poi.item_id AND pg.po_id = p.id\n", "        WHERE\n", "            (posta.id IN (2, 3, 13, 14, 15) OR (posta.id = 9 AND is_multiple_grn = 1))\n", "            AND pg.grn_id IS NULL\n", "            AND ps.schedule_date_time >= current_date\n", "            AND ps.schedule_date_time < current_date + INTERVAL '2' DAY\n", "            AND poi.item_id IN {item_id_list}\n", "\n", "                        \"\"\",\n", "            CON_TRINO,\n", "        )\n", "\n", "        check_df_shape(open_po_df, \"open_po_df\")\n", "\n", "        return open_po_df\n", "\n", "    except Exception as e:\n", "        send_slack_alert(f\":red_circle:*Alert*:red_circle:\\nError while fetching open PO \\n\\n{e}\")\n", "        print(e)\n", "        # print(kuch_to_fatta)\n", "\n", "\n", "# CON_PO.ping()\n", "\n", "blinkit_open_po_df = get_blinkit_open_po()\n", "blinkit_open_po_df1 = pd.merge(\n", "    blinkit_open_po_df,\n", "    dts_map[[\"backend_hot_outlet_id\", \"outlet_id\", \"is_dts\"]].drop_duplicates(),\n", "    on=[\"outlet_id\"],\n", "    how=\"inner\",\n", ")[\n", "    [\n", "        \"backend_hot_outlet_id\",\n", "        \"outlet_id\",\n", "        \"issue_datetime\",\n", "        \"schedule_datetime\",\n", "        \"schedule_date\",\n", "        \"item_id\",\n", "        \"open_po_qty\",\n", "        \"is_dts\",\n", "    ]\n", "]\n", "blinkit_open_po_df1 = blinkit_open_po_df1.append(\n", "    pd.merge(\n", "        blinkit_open_po_df,\n", "        dts_map[[\"backend_hot_outlet_id\", \"is_dts\"]].drop_duplicates(),\n", "        left_on=[\"outlet_id\"],\n", "        right_on=[\"backend_hot_outlet_id\"],\n", "        how=\"inner\",\n", "    )[\n", "        [\n", "            \"backend_hot_outlet_id\",\n", "            \"outlet_id\",\n", "            \"issue_datetime\",\n", "            \"schedule_datetime\",\n", "            \"schedule_date\",\n", "            \"item_id\",\n", "            \"open_po_qty\",\n", "            \"is_dts\",\n", "        ]\n", "    ]\n", ")\n", "blinkit_open_po_df = blinkit_open_po_df1.append(\n", "    pd.merge(\n", "        blinkit_open_po_df,\n", "        dts_map[[\"backend_hot_outlet_id\", \"backend_outlet_id\", \"is_dts\"]].drop_duplicates(),\n", "        left_on=[\"outlet_id\"],\n", "        right_on=[\"backend_outlet_id\"],\n", "        how=\"inner\",\n", "    )[\n", "        [\n", "            \"backend_hot_outlet_id\",\n", "            \"outlet_id\",\n", "            \"issue_datetime\",\n", "            \"schedule_datetime\",\n", "            \"schedule_date\",\n", "            \"item_id\",\n", "            \"open_po_qty\",\n", "            \"is_dts\",\n", "        ]\n", "    ]\n", ")\n", "\n", "blinkit_open_po_df = blinkit_open_po_df.drop_duplicates().reset_index().drop(columns={\"index\"})\n", "\n", "blinkit_open_po_df[\"pick\"] = np.where(\n", "    (blinkit_open_po_df[\"is_dts\"] == 1)\n", "    & (\n", "        blinkit_open_po_df[\"outlet_id\"].isin(\n", "            tuple(\n", "                list(outlet_id_list)\n", "                + list(warehouse_base.backend_outlet_id.astype(int))\n", "                + list(warehouse_base.backend_hot_outlet_id.astype(int))\n", "            )\n", "        )\n", "    )\n", "    & (\n", "        pd.to_datetime(blinkit_open_po_df[\"schedule_datetime\"])\n", "        == pd.to_datetime((date.today() + timedelta(days=1)).strftime(\"%Y-%m-%d\") + \" 23:30:00\")\n", "    ),\n", "    1,\n", "    np.where(\n", "        (blinkit_open_po_df[\"is_dts\"] != 1)\n", "        & (\n", "            blinkit_open_po_df[\"outlet_id\"].isin(\n", "                tuple(\n", "                    list(warehouse_base.backend_outlet_id.astype(int))\n", "                    + list(warehouse_base.backend_hot_outlet_id.astype(int))\n", "                )\n", "            )\n", "        )\n", "        & (\n", "            pd.to_datetime(blinkit_open_po_df[\"schedule_datetime\"])\n", "            == pd.to_datetime((date.today() + timedelta(days=0)).strftime(\"%Y-%m-%d\") + \" 23:30:00\")\n", "        ),\n", "        1,\n", "        0,\n", "    ),\n", ")\n", "\n", "blinkit_open_po_df = (\n", "    blinkit_open_po_df[blinkit_open_po_df[\"pick\"] == 1]\n", "    .groupby([\"backend_hot_outlet_id\", \"item_id\"])\n", "    .agg({\"open_po_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "\n", "def get_hp_open_po(backend_outlet_hot):\n", "    print(\" -- Backend Open PO through API -- \")\n", "    url = (\n", "        \"https://purchaseorder-retail.prod-sgp-k8s.grofer.io/v1/hyperpure/\"\n", "        + str(backend_outlet_hot)\n", "        + \"/open-pos/\"\n", "    )\n", "    print(url)\n", "    response = requests.get(url)\n", "    open_po_hp = pd.DataFrame(response.json()[\"data\"][\"open_pos\"])\n", "    open_po_hp[\"schedule_date_time\"] = pd.to_datetime(open_po_hp[\"schedule_date_time\"]).dt.strftime(\n", "        \"%Y-%m-%d %H:%M:%S\"\n", "    )\n", "    open_po_hp = open_po_hp[\n", "        pd.to_datetime(open_po_hp[\"schedule_date_time\"])\n", "        == pd.to_datetime((date.today() + timedelta(days=0)).strftime(\"%Y-%m-%d\") + \" 18:30:00\")\n", "    ]\n", "    open_po_hp = (\n", "        open_po_hp[open_po_hp[\"item_id\"].isin(item_id_list)].reset_index().drop(columns={\"index\"})\n", "    )\n", "    open_po_hp = open_po_hp.groupby([\"item_id\"]).agg({\"open_po_quantity\": \"sum\"}).reset_index()\n", "\n", "    return open_po_hp\n", "\n", "\n", "def get_hp_open_po_all(df):\n", "    resp = pd.DataFrame({\"backend_hot_outlet_id\": [], \"item_id\": [], \"open_po_quantity\": []})\n", "    for r in df[\"backend_hot_outlet_id\"]:\n", "        retries = 1\n", "        success = False\n", "        while not success:\n", "            try:\n", "                resp_temp = get_hp_open_po(r)\n", "                success = True\n", "            except Exception as e:\n", "                wait = 5\n", "                print(f\"Error! Waiting {wait} secs and re-trying...\")\n", "                sys.stdout.flush()\n", "                time.sleep(wait)\n", "                retries += 1\n", "\n", "                if retries > 5:\n", "                    send_slack_alert(\n", "                        f\":red_circle:*Alert*:red_circle:\\nError while fetching api open po \\n\\n{e}\"\n", "                    )\n", "                    resp_temp = pd.DataFrame(\n", "                        {\n", "                            \"backend_hot_outlet_id\": [],\n", "                            \"item_id\": [],\n", "                            \"open_po_quantity\": [],\n", "                        }\n", "                    )\n", "                    break\n", "        resp_temp[\"backend_hot_outlet_id\"] = r\n", "        resp = resp.append(resp_temp)\n", "    resp = resp.reset_index().drop(columns={\"index\"})\n", "    return resp\n", "\n", "\n", "import sys\n", "\n", "backend_outlet_detail = pd.merge(dts_map, outlet_details, on=[\"outlet_id\"], how=\"inner\")[\n", "    [\"backend_hot_outlet_id\", \"business_type_id\"]\n", "]\n", "backend_outlet_detail = backend_outlet_detail.append(\n", "    pd.merge(\n", "        dts_map,\n", "        outlet_details,\n", "        left_on=[\"backend_hot_outlet_id\"],\n", "        right_on=[\"outlet_id\"],\n", "        how=\"inner\",\n", "    )[[\"backend_hot_outlet_id\", \"business_type_id\"]]\n", ")\n", "backend_outlet_detail = backend_outlet_detail.append(\n", "    pd.merge(\n", "        warehouse_base,\n", "        outlet_details,\n", "        left_on=[\"backend_outlet_id\"],\n", "        right_on=[\"outlet_id\"],\n", "        how=\"inner\",\n", "    )[[\"backend_hot_outlet_id\", \"business_type_id\"]]\n", ")\n", "\n", "hyperpure_backend = (\n", "    backend_outlet_detail[backend_outlet_detail[\"business_type_id\"] == 21][\"backend_hot_outlet_id\"]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "hp_open_po_df = pd.DataFrame({\"backend_hot_outlet_id\": [], \"item_id\": [], \"open_po_qty\": []})\n", "\n", "if hyperpure_backend.shape[0] > 0:\n", "    try:\n", "        hp_open_po_df = get_hp_open_po_all(hyperpure_backend)\n", "    except Exception as e:\n", "        sys.stdout.flush()\n", "        print(e)\n", "    hp_open_po_df = (\n", "        hp_open_po_df.groupby([\"backend_hot_outlet_id\", \"item_id\"])\n", "        .agg({\"open_po_quantity\": \"sum\"})\n", "        .reset_index()\n", "        .rename(columns={\"open_po_quantity\": \"open_po_qty\"})\n", "    )\n", "\n", "open_po_df = hp_open_po_df.copy()\n", "open_po_df = open_po_df.append(\n", "    blinkit_open_po_df[\n", "        blinkit_open_po_df[\"backend_hot_outlet_id\"].isin(hyperpure_backend[\"backend_hot_outlet_id\"])\n", "        == False\n", "    ]\n", ")\n", "open_po_df = open_po_df.drop_duplicates().reset_index().drop(columns={\"index\"})\n", "open_po_df[[\"backend_hot_outlet_id\", \"item_id\"]] = open_po_df[\n", "    [\"backend_hot_outlet_id\", \"item_id\"]\n", "].astype(int)\n", "\n", "\n", "def get_blinkit_inventory():\n", "    try:\n", "        inventory_details_sql = f\"\"\"\n", "            select * from \n", "\n", "            (select a.item_id, a.outlet_id, (a.quantity - coalesce(b.blocked_quantity,0)) as current_inventory\n", "\n", "            from (select * from ims.ims_item_inventory where outlet_id in {tuple(list(outlet_id_list) + list(warehouse_base.backend_outlet_id.astype(int)) + list(warehouse_base.backend_hot_outlet_id.astype(int)))}) a\n", "\n", "            left join\n", "                (select item_id, outlet_id, sum(quantity) as blocked_quantity\n", "                    from (select * from ims.ims_item_blocked_inventory where outlet_id in {tuple(list(outlet_id_list) + list(warehouse_base.backend_outlet_id.astype(int)) + list(warehouse_base.backend_hot_outlet_id.astype(int)))}) c\n", "                        where active = 1\n", "                        and quantity > 0\n", "                        and blocked_type in (1,2,5)\n", "\n", "                            group by 1,2\n", "                ) b on b.item_id = a.item_id and b.outlet_id = a.outlet_id\n", "\n", "                where a.active = 1\n", "            ) x\n", "            where item_id in {item_id_list}\n", "            \"\"\"\n", "\n", "        blinkit_inventory_df = read_sql_query(inventory_details_sql, CON_TRINO)\n", "        check_df_shape(blinkit_inventory_df, \"blinkit_inventory_df\")\n", "\n", "        return blinkit_inventory_df\n", "\n", "    except Exception as e:\n", "        send_slack_alert(\n", "            f\":red_circle:*Alert*:red_circle:\\nError while fetching ims inventory \\n\\n{e}\"\n", "        )\n", "        print(e)\n", "        # print(kuch_to_fatta)\n", "\n", "\n", "nagpur_outlet_list = tuple(\n", "    list(\n", "        active_tea_tagged_assortment[\n", "            active_tea_tagged_assortment[\"backend_hot_outlet_id\"].isin(\n", "                [5528, 5914, 5915, 6198, 6329, 5932, 5984]\n", "            )  # adding surat and varanasi with nagpur to list. adding Vishakhapatnam and Guntur.\n", "        ].outlet_id.unique()\n", "    )\n", "    + [-1]\n", "    + [-2]\n", ")\n", "\n", "\n", "def get_current_grn():\n", "    try:\n", "        today_grn_sql = f\"\"\"\n", "            with grn as (\n", "                select\n", "                    date(grn.insert_ds_ist) insert_ds_ist,\n", "                    (grn.created_at + interval '330' minute) created_at_ist,\n", "                    po.po_number,\n", "                    grn.outlet_id,\n", "                    grn.item_id,\n", "                    sum(grn.quantity) grn_quantity\n", "                from\n", "                    po.po_grn grn\n", "                join\n", "                    rpc.item_category_details ic\n", "                    on grn.item_id = ic.item_id\n", "                    and ic.l0_id = 1487\n", "                left join\n", "                    (\n", "                    SELECT \n", "                        id,\n", "                        outlet_id, \n", "                        po_number \n", "                    FROM \n", "                        po.purchase_order \n", "                    WHERE \n", "                        created_at >= current_date - INTERVAL '2' DAY\n", "                    group by 1,2,3\n", "                    ) po\n", "                    on grn.po_id = po.id\n", "                    and grn.outlet_id = po.outlet_id\n", "                where\n", "                    grn.outlet_id not in (\n", "                    2666,2665  --excluding Jaipur.\n", "                    )\n", "                    and date(grn.insert_ds_ist) >= current_date - interval '2' day\n", "                    and grn.lake_active_record\n", "                    and grn.outlet_id in {nagpur_outlet_list}\n", "                group by 1,2,3,4,5\n", "                ),\n", "                daily_grn as (\n", "                select\n", "                    insert_ds_ist day_of_consumption,\n", "                    extract(hour from created_at_ist) hour_,\n", "                    outlet_id,\n", "                    item_id,\n", "                    sum(grn_quantity) grn_quantity\n", "                from\n", "                    grn\n", "                group by 1,2,3,4)\n", "                select\n", "                    outlet_id,\n", "                    item_id,\n", "                    sum(grn_quantity) grn\n", "                from\n", "                    daily_grn\n", "                where \n", "                    day_of_consumption = current_date\n", "                    and hour_>14\n", "                group by 1,2\n", "            \"\"\"\n", "\n", "        today_grn_df = read_sql_query(today_grn_sql, CON_TRINO)\n", "        check_df_shape(today_grn_df, \"today_grn\")\n", "\n", "        return today_grn_df\n", "\n", "    except Exception as e:\n", "        send_slack_alert(\n", "            f\":red_circle:*Alert*:red_circle:\\nError while fetching today's grn \\n\\n{e}\"\n", "        )\n", "        print(e)\n", "        # print(kuch_to_fatta)\n", "\n", "\n", "blinkit_inventory_df = get_blinkit_inventory()\n", "today_grn = get_current_grn()\n", "\n", "blinkit_inventory_df = blinkit_inventory_df.merge(\n", "    today_grn, on=[\"outlet_id\", \"item_id\"], how=\"left\"\n", ")\n", "blinkit_inventory_df[\"grn\"] = blinkit_inventory_df[\"grn\"].fillna(0)\n", "blinkit_inventory_df[\"current_inventory\"] = (\n", "    blinkit_inventory_df[\"current_inventory\"] - blinkit_inventory_df[\"grn\"]\n", ")\n", "blinkit_inventory_df = blinkit_inventory_df[[\"item_id\", \"outlet_id\", \"current_inventory\"]]\n", "\n", "# update\n", "blinkit_inventory_df[\"current_inventory\"] = np.where(\n", "    blinkit_inventory_df[\"current_inventory\"] <= 0,\n", "    0,\n", "    blinkit_inventory_df[\"current_inventory\"],\n", ")\n", "\n", "blinkit_inventory_df1 = (\n", "    pd.merge(\n", "        blinkit_inventory_df,\n", "        dts_map[[\"backend_hot_outlet_id\", \"outlet_id\"]].drop_duplicates(),\n", "        on=[\"outlet_id\"],\n", "        how=\"inner\",\n", "    )[[\"backend_hot_outlet_id\", \"outlet_id\", \"item_id\", \"current_inventory\"]]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "blinkit_inventory_df1 = blinkit_inventory_df1.append(\n", "    pd.merge(\n", "        blinkit_inventory_df,\n", "        dts_map[[\"backend_hot_outlet_id\"]].drop_duplicates(),\n", "        left_on=[\"outlet_id\"],\n", "        right_on=[\"backend_hot_outlet_id\"],\n", "        how=\"inner\",\n", "    )[[\"backend_hot_outlet_id\", \"outlet_id\", \"item_id\", \"current_inventory\"]]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "blinkit_inventory_df = blinkit_inventory_df1.append(\n", "    pd.merge(\n", "        blinkit_inventory_df,\n", "        dts_map[[\"backend_hot_outlet_id\", \"backend_outlet_id\"]].drop_duplicates(),\n", "        left_on=[\"outlet_id\"],\n", "        right_on=[\"backend_outlet_id\"],\n", "        how=\"inner\",\n", "    )[[\"backend_hot_outlet_id\", \"outlet_id\", \"item_id\", \"current_inventory\"]]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "blinkit_inventory_df = blinkit_inventory_df.drop_duplicates().reset_index().drop(columns={\"index\"})\n", "\n", "blinkit_fe_inv_df = (\n", "    blinkit_inventory_df[\n", "        blinkit_inventory_df[\"outlet_id\"].isin(\n", "            outlet_details[outlet_details[\"business_type_id\"].isin({7})][\"outlet_id\"].unique()\n", "        )\n", "    ]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "blinkit_fe_inv_df = pd.merge(\n", "    blinkit_fe_inv_df, sales_avail_df, on=[\"outlet_id\", \"item_id\"], how=\"left\"\n", ")  ## avg_sales\n", "blinkit_fe_inv_df[\"avg_sales\"] = blinkit_fe_inv_df[\"avg_sales\"].fillna(0.25)\n", "blinkit_fe_inv_df = pd.merge(\n", "    blinkit_fe_inv_df, item_details[[\"item_id\", \"shelf_life\"]], how=\"left\"\n", ")  ## shelf_life\n", "\n", "blinkit_fe_inv_df[\"year\"] = date_selected.year\n", "blinkit_fe_inv_df[\"month\"] = date_selected.month\n", "blinkit_fe_inv_df[\"day\"] = date_selected.day\n", "blinkit_fe_inv_df[\"day_of_week\"] = date_selected.weekday()  # 0 = Monday, 6 = Sunday\n", "blinkit_fe_inv_df[\"is_weekend\"] = (blinkit_fe_inv_df[\"day_of_week\"] >= 5).astype(\n", "    int\n", ")  # 1 if weekend (Saturday/Sunday)\n", "\n", "item_level_sql = f\"\"\"\n", "        select\n", "            item_id,\n", "            product_type_id,\n", "            l2_id\n", "        from\n", "            rpc.item_category_details\n", "        where\n", "            l0_id = 1487\n", "            and item_id in {item_id_list}\n", "        group by 1,2,3\n", "\"\"\"\n", "\n", "item_level_df = read_sql_query(item_level_sql, CON_TRINO)\n", "\n", "morning_sales_sql = f\"\"\"\n", "SELECT\n", "    fm.pos_outlet_id outlet_id,\n", "    ip.item_id,\n", "    sum(o.procured_quantity*ip.multiplier) morning_sales\n", "FROM\n", "    dwh.fact_sales_order_item_details o\n", "INNER JOIN\n", "    dwh.dim_product dp \n", "    on dp.product_id = o.product_id\n", "    -- and dp.l0_category_id = 1487\n", "    and dp.is_current\n", "inner join\n", "    dwh.dim_merchant_outlet_facility_mapping as fm\n", "    on o.frontend_merchant_id = fm.frontend_merchant_id\n", "    and fm.is_current = true \n", "    and fm.is_mapping_enabled = true \n", "    and fm.is_frontend_merchant_active = true \n", "    and fm.is_backend_merchant_active = true \n", "    and fm.is_pos_outlet_active = 1\n", "    and fm.pos_outlet_id in {outlet_id_list}\n", "inner join\n", "    dwh.dim_item_product_offer_mapping ip\n", "    on ip.product_id = o.product_id\n", "    and ip.is_current\n", "inner join\n", "    rpc.item_category_details rp\n", "    on rp.item_id = ip.item_id\n", "    and rp.l0_id = 1487 \n", "    and rp.item_id in {item_id_list}\n", " WHERE\n", "    o.order_current_status = 'DELIVERED'\n", "    and o.order_create_dt_ist = current_date\n", "    and o.is_internal_order = False\n", "    and extract(hour from o.order_create_ts_ist) between 0 and 17\n", "GROUP BY 1,2\n", "\"\"\"\n", "morning_sales_df = read_sql_query(morning_sales_sql, CON_TRINO)\n", "\n", "blinkit_fe_inv_df = pd.merge(blinkit_fe_inv_df, item_level_df, on=\"item_id\", how=\"left\")\n", "\n", "blinkit_fe_inv_df = pd.merge(\n", "    blinkit_fe_inv_df, morning_sales_df, on=[\"outlet_id\", \"item_id\"], how=\"left\"\n", ")\n", "\n", "blinkit_fe_inv_df[\"morning_sales\"] = blinkit_fe_inv_df[\"morning_sales\"].fillna(0)\n", "\n", "leftover_prediction = np.maximum(model.predict(blinkit_fe_inv_df[features]), 0)\n", "leftover_prediction = np.minimum(leftover_prediction, 0.9)\n", "\n", "blinkit_fe_inv_df[\"leftover_ratio_prediction\"] = leftover_prediction\n", "\n", "blinkit_fe_inv_df[\"predicted_leftover\"] = (\n", "    blinkit_fe_inv_df[\"leftover_ratio_prediction\"] * blinkit_fe_inv_df[\"current_inventory\"]\n", ")\n", "\n", "blinkit_fe_inv_df[\"predicted_leftover\"] = blinkit_fe_inv_df[\"predicted_leftover\"].astype(int)\n", "\n", "blinkit_fe_inv_df.to_parquet(f\"\"\"{date_selected}_model_indent_run_predictions.parquet\"\"\")\n", "\n", "write_file_to_s3(f\"\"\"{date_selected}_model_indent_run_predictions.parquet\"\"\", s3_path, Path(\"./\"))\n", "\n", "# Sales availability is calculated only for cases where availability was for atleast half a day and sales not available is filled with 0.25 need to update.\n", "\n", "blinkit_fe_inv_df[\"current_inventory\"] = np.where(\n", "    blinkit_fe_inv_df[\"shelf_life\"] <= 1,\n", "    0,\n", "    np.where(\n", "        ((blinkit_fe_inv_df[\"current_inventory\"] - (1.1 * blinkit_fe_inv_df[\"avg_sales\"])) > 0)\n", "        & (blinkit_fe_inv_df[\"shelf_life\"] <= 2),\n", "        np.round(\n", "            (blinkit_fe_inv_df[\"current_inventory\"] - (1.1 * blinkit_fe_inv_df[\"avg_sales\"])) * 0.5\n", "        ),\n", "        np.where(\n", "            ((blinkit_fe_inv_df[\"current_inventory\"] - (1.1 * blinkit_fe_inv_df[\"avg_sales\"])) > 0)\n", "            & (blinkit_fe_inv_df[\"shelf_life\"] <= 5),\n", "            np.round(\n", "                (blinkit_fe_inv_df[\"current_inventory\"] - (1.1 * blinkit_fe_inv_df[\"avg_sales\"]))\n", "                * 0.7\n", "            ),\n", "            np.where(\n", "                (\n", "                    (\n", "                        blinkit_fe_inv_df[\"current_inventory\"]\n", "                        - (1.1 * blinkit_fe_inv_df[\"avg_sales\"])\n", "                    )\n", "                    > 0\n", "                )\n", "                & (blinkit_fe_inv_df[\"shelf_life\"] <= 7),\n", "                np.round(\n", "                    (\n", "                        blinkit_fe_inv_df[\"current_inventory\"]\n", "                        - (1.1 * blinkit_fe_inv_df[\"avg_sales\"])\n", "                    )\n", "                    * 0.9\n", "                ),\n", "                np.where(\n", "                    (\n", "                        (\n", "                            blinkit_fe_inv_df[\"current_inventory\"]\n", "                            - (1.1 * blinkit_fe_inv_df[\"avg_sales\"])\n", "                        )\n", "                        > 0\n", "                    )\n", "                    & (blinkit_fe_inv_df[\"shelf_life\"] > 7),\n", "                    np.round(\n", "                        (\n", "                            blinkit_fe_inv_df[\"current_inventory\"]\n", "                            - (1.1 * blinkit_fe_inv_df[\"avg_sales\"])\n", "                        )\n", "                        * 1\n", "                    ),\n", "                    blinkit_fe_inv_df[\n", "                        \"current_inventory\"\n", "                    ],  # should be considered 0 if avg sales exceed inventory,\n", "                ),\n", "            ),\n", "        ),\n", "    ),\n", ")\n", "\n", "blinkit_fe_inv_df_store = (\n", "    blinkit_fe_inv_df.groupby([\"backend_hot_outlet_id\", \"outlet_id\", \"item_id\"])\n", "    .agg({\"current_inventory\": \"sum\", \"predicted_leftover\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"current_inventory\": \"fe_inventory\"})\n", ")\n", "\n", "blinkit_fe_inv_df = (\n", "    blinkit_fe_inv_df.groupby([\"backend_hot_outlet_id\", \"item_id\"])\n", "    .agg({\"current_inventory\": \"sum\", \"predicted_leftover\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"current_inventory\": \"fe_inventory\"})\n", ")\n", "\n", "blinkit_be_inv_df = (\n", "    blinkit_inventory_df[\n", "        blinkit_inventory_df[\"outlet_id\"].isin(\n", "            outlet_details[outlet_details[\"business_type_id\"].isin({7, 21}) == False][\n", "                \"outlet_id\"\n", "            ].unique()\n", "        )\n", "    ]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "blinkit_be_inv_df = pd.merge(blinkit_be_inv_df, item_details[[\"item_id\", \"shelf_life\"]], how=\"left\")\n", "blinkit_be_inv_df[\"current_inventory\"] = np.where(\n", "    blinkit_be_inv_df[\"shelf_life\"] <= 1, 0, blinkit_be_inv_df[\"current_inventory\"]\n", ")\n", "blinkit_be_inv_df = (\n", "    blinkit_be_inv_df.groupby([\"backend_hot_outlet_id\", \"item_id\"])\n", "    .agg({\"current_inventory\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"current_inventory\": \"be_inventory\"})\n", ")\n", "\n", "\n", "def get_be_inv(backend_hot_outlet_id):\n", "    print(\" -- Backend Inventory through API -- \")\n", "    base_item_list = list(\n", "        map(\n", "            int,\n", "            list(\n", "                active_tea_tagged_assortment[\n", "                    active_tea_tagged_assortment[\"backend_hot_outlet_id\"] == backend_hot_outlet_id\n", "                ].item_id.unique()\n", "            ),\n", "        )\n", "    )\n", "    json_req = {\"item_ids\": base_item_list, \"get_projected_inventory\": False}\n", "    url = (\n", "        \"http://po.retail.prod.grofer.io/v1/hyperpure/\"\n", "        + str(backend_hot_outlet_id)\n", "        + \"/item-available-quantity/\"\n", "    )\n", "    print(url)\n", "    response = requests.post(url, json=json_req)\n", "    inventory_df = response.json()\n", "    inventory_df.keys()\n", "    inventory_df = inventory_df[\"data\"][\"inventories\"]\n", "    inventory_df = pd.DataFrame.from_dict(inventory_df)\n", "    inventory_df = inventory_df.rename(columns={\"quantity\": \"current_inventory\"})\n", "    inventory_df[\"current_inventory\"] = np.where(\n", "        (inventory_df[\"current_inventory\"] - inventory_df[\"inwarded_today\"]) < 0,\n", "        0,\n", "        inventory_df[\"current_inventory\"] - inventory_df[\"inwarded_today\"],\n", "    )\n", "\n", "    return inventory_df\n", "\n", "\n", "def get_be_inv_all(df):\n", "    resp = pd.DataFrame(\n", "        {\n", "            \"backend_hot_outlet_id\": [],\n", "            \"item_id\": [],\n", "            \"inwarded_today\": [],\n", "            \"priority_split\": [],\n", "            \"current_inventory\": [],\n", "        }\n", "    )\n", "    for r in df[\"backend_hot_outlet_id\"]:\n", "        retries = 1\n", "        success = False\n", "        while not success:\n", "            try:\n", "                resp_temp = get_be_inv(r)\n", "                success = True\n", "            except Exception as e:\n", "                wait = 5\n", "                print(f\"Error! Waiting {wait} secs and re-trying...\")\n", "                sys.stdout.flush()\n", "                time.sleep(wait)\n", "                retries += 1\n", "\n", "                if retries > 5:\n", "                    send_slack_alert(\n", "                        f\":red_circle:*Alert*:red_circle:\\nError while fetching api be inv \\n\\n{e}\"\n", "                    )\n", "                    resp_temp = pd.DataFrame(\n", "                        {\n", "                            \"backend_hot_outlet_id\": [],\n", "                            \"item_id\": [],\n", "                            \"inwarded_today\": [],\n", "                            \"priority_split\": [],\n", "                            \"current_inventory\": [],\n", "                        }\n", "                    )\n", "                    break\n", "        resp_temp[\"backend_hot_outlet_id\"] = r\n", "        resp = resp.append(resp_temp)\n", "    resp = resp.reset_index().drop(columns={\"index\"})\n", "    return resp\n", "\n", "\n", "hp_be_inv_df = pd.DataFrame({\"backend_hot_outlet_id\": [], \"item_id\": [], \"be_inventory\": []})\n", "\n", "if hyperpure_backend.shape[0] > 0:\n", "    try:\n", "        hp_be_inv_df = get_be_inv_all(hyperpure_backend)\n", "    except Exception as e:\n", "        sys.stdout.flush()\n", "        print(e)\n", "\n", "    hp_be_inv_df = pd.merge(hp_be_inv_df, item_details[[\"item_id\", \"shelf_life\"]], how=\"left\")\n", "    hp_be_inv_df[\"current_inventory\"] = np.where(\n", "        hp_be_inv_df[\"shelf_life\"] <= 1, 0, hp_be_inv_df[\"current_inventory\"]\n", "    )\n", "    hp_be_inv_df = (\n", "        hp_be_inv_df.groupby([\"backend_hot_outlet_id\", \"item_id\"])\n", "        .agg({\"current_inventory\": \"sum\"})\n", "        .reset_index()\n", "        .rename(columns={\"current_inventory\": \"be_inventory\"})\n", "    )\n", "\n", "# update\n", "\n", "hp_be_inv_df[[\"backend_hot_outlet_id\", \"item_id\"]] = hp_be_inv_df[\n", "    [\"backend_hot_outlet_id\", \"item_id\"]\n", "].astype(int)\n", "hp_be_outlet = tuple(list(hp_be_inv_df.backend_hot_outlet_id.unique()) + [-1] + [-2])\n", "inventory_transfer_sql = f\"\"\"\n", "with lg as (\n", "select \n", "    date(from_unixtime(ts_ms/1000)) final_date,\n", "    replace(cast(json_extract(after,'$.warehouse_code') as varchar),'\"','') as warehouse_code,\n", "    cast(json_extract(after,'$.product_number') as bigint) as destination_product_name,\n", "    cast(json_extract(after,'$.quantity') as bigint) as quantity, \n", "    cast(json_extract(after,'$.reason_code') as varchar) as reason_code,\n", "    cast(json_extract(after,'$.bin_subtype') as varchar) as bin_subtype,\n", "    cast(json_extract(after,'$.updated_by_user_type') as varchar) as updated_by_user_type,\n", "    cast(json_extract(after,'$.operation') as varchar) as operation,\n", "    cast(json_extract(after,'$.source_entity_id') as varchar) as source_entity_id\n", "from \n", "    zomato.hp_wms_cdc.bin_inventory_logs \n", "where \n", "    dt >= date_format(current_date - interval '1' day,'%%Y%%m%%d')\n", "    and extract(hour from from_unixtime(ts_ms/1000)) between 12 and 23\n", ")\n", "\n", "select \n", "    lg.final_date, \n", "    lg.warehouse_code, \n", "    pom.outlet_id backend_hot_outlet,\n", "    pi.item_id,\n", "    icd.name as product_name, \n", "    sum(lg.quantity) as quantity\n", "from \n", "    lg\n", "JOIN \n", "    blinkit.po.edi_integration_partner_item_mapping pi \n", "    ON CAST(pi.partner_item_id as int) = lg.destination_product_name \n", "    and pi.active = true\n", "INNER JOIN \n", "    blinkit.rpc.item_category_details icd \n", "    ON icd.item_id = pi.item_id \n", "    and icd.l0_id = 1487\n", "left join \n", "    zomato.hp_wms.bin b \n", "    on lg.source_entity_id = b.bin_number \n", "    and lg.warehouse_code = b.warehouse_code\n", "inner join\n", "    po.edi_integration_partner_outlet_mapping pom\n", "    on lg.warehouse_code = pom.partner_outlet_id\n", "    and pom.outlet_id in {hp_be_outlet}\n", "where \n", "    (\n", "    lg.reason_code in ('SKU_TRANSFER_ADD')\n", "    or \n", "    (\n", "    b.bin_subtype in ('QUALITY_HOLD') \n", "    and lg.updated_by_user_type in ('BIN_TRANSFER_USER','ADMIN') \n", "    and lg.operation = 'INTER_BIN_TRANSFER_ADD'\n", "    ) \n", "    )\n", "    and lg.warehouse_code like ('%%CPC%%')\n", "    and lg.final_date = current_date\n", "group by 1,2,3,4,5\n", "\"\"\"\n", "inventory_transfers = read_sql_query(inventory_transfer_sql, CON_TRINO)\n", "if inventory_transfers.shape[0] > 0:\n", "    inventory_transfers.to_csv(\"inventory_transfers.csv\", index=False)\n", "    pb.send_slack_message(\n", "        channel=\"dag-alerts-anchit\",\n", "        text=\"Inventory Transfer cases for today \\ncc<@U078DPW05T4>\",\n", "        files=[\"inventory_transfers.csv\"],\n", "    )\n", "\n", "inventory_transfers = inventory_transfers[[\"backend_hot_outlet\", \"item_id\", \"quantity\"]].astype(int)\n", "hp_be_inv_df = (\n", "    pd.merge(\n", "        hp_be_inv_df,\n", "        inventory_transfers,\n", "        left_on=[\"backend_hot_outlet_id\", \"item_id\"],\n", "        right_on=[\"backend_hot_outlet\", \"item_id\"],\n", "        how=\"left\",\n", "    )\n", "    .reset_index()\n", "    .drop(columns={\"index\", \"backend_hot_outlet\"})\n", ")\n", "hp_be_inv_df[\"quantity\"] = hp_be_inv_df[\"quantity\"].fillna(0)\n", "hp_be_inv_df[\"be_inventory\"] = np.where(\n", "    hp_be_inv_df[\"be_inventory\"] - hp_be_inv_df[\"quantity\"] > 0,\n", "    hp_be_inv_df[\"be_inventory\"] - hp_be_inv_df[\"quantity\"],\n", "    0,\n", ")\n", "hp_be_inv_df = hp_be_inv_df.drop(columns={\"quantity\"})\n", "\n", "be_inv_df = hp_be_inv_df.copy()\n", "be_inv_df = be_inv_df.append(blinkit_be_inv_df)\n", "be_inv_df = be_inv_df.drop_duplicates().reset_index().drop(columns={\"index\"})\n", "be_inv_df[[\"backend_hot_outlet_id\", \"item_id\"]] = be_inv_df[\n", "    [\"backend_hot_outlet_id\", \"item_id\"]\n", "].astype(int)\n", "\n", "\n", "data_science_demand_forecast_t3 = get_data_science_demand_forecast_t3()\n", "previous_day_forecast_t1, previous_day_forecast_t2 = get_previous_day_forecast()\n", "(\n", "    previous_day_forecast_overall_t1,\n", "    previous_day_forecast_overall_t2,\n", ") = get_previous_day_forecast_overall()\n", "\n", "\n", "forecast_asst = pd.merge(\n", "    active_tea_tagged_assortment,\n", "    data_science_demand_forecast_t3[[\"outlet_id\", \"item_id\", \"t3_ds_forecast\"]],\n", "    on=[\"outlet_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "forecast_asst = pd.merge(\n", "    forecast_asst,\n", "    warehouse_base[[\"backend_hot_outlet_id\", \"backend_outlet_id\", \"cluster\", \"is_dts\"]],\n", "    on=[\"backend_hot_outlet_id\"],\n", "    how=\"inner\",\n", ").rename(\n", "    columns={\n", "        \"backend_hot_outlet_id\": \"backend_hot_outlet\",\n", "        \"backend_outlet_id\": \"backend_ssc_outlet\",\n", "    }\n", ")\n", "forecast_asst = forecast_asst.astype(\n", "    {\n", "        \"outlet_id\": \"int\",\n", "        \"item_id\": \"int\",\n", "        \"backend_hot_outlet\": \"int\",\n", "        \"backend_ssc_outlet\": \"int\",\n", "    }\n", ")\n", "\n", "blank_forecast_asst = (\n", "    forecast_asst[forecast_asst[\"t3_ds_forecast\"].isna() == True]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")  #### forecast not provided for this assortment\n", "\n", "blank_forecast_asst = pd.merge(\n", "    blank_forecast_asst, item_details[[\"item_id\", \"item_name\"]], how=\"left\"\n", ")\n", "\n", "blank_forecast_asst = pd.merge(\n", "    blank_forecast_asst, outlet_details[[\"outlet_id\", \"outlet_name\"]], how=\"left\"\n", ")\n", "\n", "blank_forecast_asst = blank_forecast_asst[\n", "    [\n", "        \"cluster\",\n", "        \"backend_facility_id\",\n", "        \"facility_id\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"t3_ds_forecast\",\n", "    ]\n", "]\n", "\n", "if blank_forecast_asst.shape[0] == forecast_asst.shape[0]:\n", "    pb.send_slack_message(\n", "        channel=\"dag-alerts-anchit\",\n", "        text=\"Base forecasts not available for any assortment \\ncc@U078DPW05T4\",\n", "    )\n", "\n", "forecast_asst[\"t3_ds_forecast\"] = forecast_asst[\"t3_ds_forecast\"].fillna(0)\n", "\n", "forecast_asst = pd.merge(\n", "    forecast_asst,\n", "    previous_day_forecast_t1[[\"outlet_id\", \"item_id\", \"t1_forecast\"]],\n", "    on=[\"outlet_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "forecast_asst = pd.merge(\n", "    forecast_asst,\n", "    previous_day_forecast_t2[[\"outlet_id\", \"item_id\", \"t2_forecast\"]],\n", "    on=[\"outlet_id\", \"item_id\"],\n", "    how=\"left\",\n", ").rename(columns={\"t2_forecast\": \"t2_ds_forecast\"})\n", "\n", "forecast_asst[\"t1_forecast\"] = forecast_asst[\"t1_forecast\"].fillna(0).astype(int)\n", "forecast_asst[\"t2_ds_forecast\"] = forecast_asst[\"t2_ds_forecast\"].fillna(0).astype(int)\n", "\n", "\n", "forecast_asst = (\n", "    pd.merge(\n", "        forecast_asst,\n", "        blinkit_fe_inv_df_store,\n", "        left_on=[\"backend_hot_outlet\", \"outlet_id\", \"item_id\"],\n", "        right_on=[\"backend_hot_outlet_id\", \"outlet_id\", \"item_id\"],\n", "        how=\"left\",\n", "    )\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\", \"backend_hot_outlet_id\"})\n", ")\n", "\n", "forecast_asst[\"fe_inventory\"] = forecast_asst[\"fe_inventory\"].fillna(0)\n", "forecast_asst[\"predicted_leftover\"] = forecast_asst[\"predicted_leftover\"].fillna(0)\n", "\n", "forecast_asst[\"t1_dmd_exc_indent\"] = np.where(\n", "    (forecast_asst[\"t1_forecast\"] - forecast_asst[\"fe_inventory\"]) > 0,\n", "    (forecast_asst[\"t1_forecast\"] - forecast_asst[\"fe_inventory\"]),\n", "    0,\n", ")\n", "\n", "forecast_asst[\"t1_dmd_exc_indent\"] = forecast_asst[\"t1_dmd_exc_indent\"].astype(int)\n", "\n", "forecast_asst[\"t1_dmd_exc_indent_model\"] = np.where(\n", "    (forecast_asst[\"t1_forecast\"] - forecast_asst[\"predicted_leftover\"]) > 0,\n", "    (forecast_asst[\"t1_forecast\"] - forecast_asst[\"predicted_leftover\"]),\n", "    0,\n", ")\n", "\n", "forecast_asst[\"t1_dmd_exc_indent_model\"] = forecast_asst[\"t1_dmd_exc_indent_model\"].astype(int)\n", "\n", "forecast_asst[\"t2_adjusted_dmd\"] = np.where(\n", "    forecast_asst[\"t1_dmd_exc_indent\"] == 0,\n", "    np.where(\n", "        forecast_asst[\"t2_ds_forecast\"]\n", "        - (forecast_asst[\"fe_inventory\"] - forecast_asst[\"t1_forecast\"])\n", "        > 0,\n", "        forecast_asst[\"t2_ds_forecast\"]\n", "        - (forecast_asst[\"fe_inventory\"] - forecast_asst[\"t1_forecast\"]),\n", "        0,\n", "    ),\n", "    forecast_asst[\"t2_ds_forecast\"],\n", ")\n", "\n", "forecast_asst[\"t2_adjusted_dmd_model\"] = np.where(\n", "    forecast_asst[\"t1_dmd_exc_indent_model\"] == 0,\n", "    np.where(\n", "        forecast_asst[\"t2_ds_forecast\"]\n", "        - (forecast_asst[\"predicted_leftover\"] - forecast_asst[\"t1_forecast\"])\n", "        > 0,\n", "        forecast_asst[\"t2_ds_forecast\"]\n", "        - (forecast_asst[\"predicted_leftover\"] - forecast_asst[\"t1_forecast\"]),\n", "        0,\n", "    ),\n", "    forecast_asst[\"t2_ds_forecast\"],\n", ")\n", "\n", "forecast_asst[\"predicted_leftover_t3\"] = np.where(\n", "    forecast_asst[\"t2_adjusted_dmd_model\"] > 0,\n", "    0,\n", "    np.where(\n", "        forecast_asst[\"predicted_leftover\"]\n", "        - forecast_asst[\"t1_forecast\"]\n", "        - forecast_asst[\"t2_ds_forecast\"]\n", "        > 0,\n", "        forecast_asst[\"predicted_leftover\"]\n", "        - forecast_asst[\"t1_forecast\"]\n", "        - forecast_asst[\"t2_ds_forecast\"],\n", "        0,\n", "    ),\n", ")\n", "\n", "forecast_asst[\"fe_inventory_t3\"] = np.where(\n", "    forecast_asst[\"t2_adjusted_dmd\"] > 0,\n", "    0,\n", "    np.where(\n", "        forecast_asst[\"fe_inventory\"]\n", "        - forecast_asst[\"t1_forecast\"]\n", "        - forecast_asst[\"t2_ds_forecast\"]\n", "        > 0,\n", "        forecast_asst[\"fe_inventory\"]\n", "        - forecast_asst[\"t1_forecast\"]\n", "        - forecast_asst[\"t2_ds_forecast\"],\n", "        0,\n", "    ),\n", ")\n", "forecast_asst = pd.merge(\n", "    forecast_asst,\n", "    item_details[[\"item_id\", \"shelf_life\"]].drop_duplicates().reset_index(drop=True),\n", "    on=\"item_id\",\n", "    how=\"left\",\n", ")\n", "\n", "forecast_asst[\"predicted_leftover_t3\"] = np.where(\n", "    forecast_asst[\"shelf_life\"].isin([1, 2]), 0, forecast_asst[\"predicted_leftover_t3\"]\n", ")\n", "forecast_asst[\"fe_inventory_t3\"] = np.where(\n", "    forecast_asst[\"shelf_life\"].isin([1, 2]), 0, forecast_asst[\"fe_inventory_t3\"]\n", ")\n", "\n", "forecast_asst[\"t3_adjusted_dmd_model\"] = np.where(\n", "    forecast_asst[\"t3_ds_forecast\"] - forecast_asst[\"predicted_leftover_t3\"] > 0,\n", "    forecast_asst[\"t3_ds_forecast\"] - forecast_asst[\"predicted_leftover_t3\"],\n", "    0,\n", ")\n", "\n", "forecast_asst[\"t3_adjusted_dmd\"] = np.where(\n", "    forecast_asst[\"t3_ds_forecast\"] - forecast_asst[\"fe_inventory_t3\"] > 0,\n", "    forecast_asst[\"t3_ds_forecast\"] - forecast_asst[\"fe_inventory_t3\"],\n", "    0,\n", ")\n", "\n", "forecast_asst[\n", "    [\n", "        \"t1_forecast\",\n", "        \"fe_inventory\",\n", "        \"fe_inventory_t3\",\n", "        \"predicted_leftover_t3\",\n", "        \"t2_ds_forecast\",\n", "        \"t3_adjusted_dmd_model\",\n", "        \"t3_adjusted_dmd\",\n", "        \"t3_ds_forecast\",\n", "        \"t2_adjusted_dmd_model\",\n", "        \"predicted_leftover\",\n", "        \"t2_adjusted_dmd\",\n", "    ]\n", "] = (\n", "    forecast_asst[\n", "        [\n", "            \"t1_forecast\",\n", "            \"fe_inventory\",\n", "            \"fe_inventory_t3\",\n", "            \"predicted_leftover_t3\",\n", "            \"t2_ds_forecast\",\n", "            \"t3_adjusted_dmd_model\",\n", "            \"t3_adjusted_dmd\",\n", "            \"t3_ds_forecast\",\n", "            \"t2_adjusted_dmd_model\",\n", "            \"predicted_leftover\",\n", "            \"t2_adjusted_dmd\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "previous_day_demand = pd.merge(\n", "    previous_day_forecast_t1,\n", "    blinkit_fe_inv_df_store[[\"outlet_id\", \"item_id\", \"fe_inventory\", \"predicted_leftover\"]],\n", "    on=[\"outlet_id\", \"item_id\"],\n", "    how=\"left\",\n", ").rename(columns={\"backend_hot_outlet_id\": \"t1_backend\"})\n", "\n", "previous_day_demand = pd.merge(\n", "    previous_day_demand,\n", "    previous_day_forecast_t2,\n", "    on=[\"outlet_id\", \"item_id\"],\n", "    how=\"left\",\n", ").rename(columns={\"backend_hot_outlet_id\": \"t2_backend\"})\n", "\n", "previous_day_demand[[\"t1_forecast\", \"t2_forecast\", \"fe_inventory\", \"predicted_leftover\"]] = (\n", "    previous_day_demand[[\"t1_forecast\", \"t2_forecast\", \"fe_inventory\", \"predicted_leftover\"]]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "previous_day_demand[\"t1_tea_demand\"] = np.where(\n", "    (previous_day_demand[\"t1_forecast\"] - previous_day_demand[\"fe_inventory\"]) > 0,\n", "    (previous_day_demand[\"t1_forecast\"] - previous_day_demand[\"fe_inventory\"]),\n", "    0,\n", ")\n", "\n", "previous_day_demand[\"t1_tea_demand_model\"] = np.where(\n", "    (previous_day_demand[\"t1_forecast\"] - previous_day_demand[\"predicted_leftover\"]) > 0,\n", "    (previous_day_demand[\"t1_forecast\"] - previous_day_demand[\"predicted_leftover\"]),\n", "    0,\n", ")\n", "\n", "previous_day_demand[\"t2_tea_demand\"] = np.where(\n", "    previous_day_demand[\"t1_tea_demand\"] == 0,\n", "    np.where(\n", "        previous_day_demand[\"t2_forecast\"]\n", "        - (previous_day_demand[\"fe_inventory\"] - previous_day_demand[\"t1_forecast\"])\n", "        > 0,\n", "        previous_day_demand[\"t2_forecast\"]\n", "        - (previous_day_demand[\"fe_inventory\"] - previous_day_demand[\"t1_forecast\"]),\n", "        0,\n", "    ),\n", "    previous_day_demand[\"t2_forecast\"],\n", ")\n", "\n", "previous_day_demand[\"t2_tea_demand_model\"] = np.where(\n", "    previous_day_demand[\"t1_tea_demand_model\"] == 0,\n", "    np.where(\n", "        previous_day_demand[\"t2_forecast\"]\n", "        - (previous_day_demand[\"predicted_leftover\"] - previous_day_demand[\"t1_forecast\"])\n", "        > 0,\n", "        previous_day_demand[\"t2_forecast\"]\n", "        - (previous_day_demand[\"predicted_leftover\"] - previous_day_demand[\"t1_forecast\"]),\n", "        0,\n", "    ),\n", "    previous_day_demand[\"t2_forecast\"],\n", ")\n", "\n", "\n", "previous_day_demand_t1 = (\n", "    previous_day_demand.groupby([\"t1_backend\", \"item_id\"])\n", "    .agg({\"t1_tea_demand\": \"sum\", \"t1_tea_demand_model\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"t1_backend\": \"backend_hot_outlet_id\"})\n", ")\n", "\n", "previous_day_demand_t2 = (\n", "    previous_day_demand.groupby([\"t2_backend\", \"item_id\"])\n", "    .agg({\"t2_tea_demand\": \"sum\", \"t2_tea_demand_model\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"t2_backend\": \"backend_hot_outlet_id\"})\n", ")\n", "\n", "previous_day_demand_t1[\"t1_tea_demand\"] = previous_day_demand_t1[\"t1_tea_demand\"].astype(int)\n", "previous_day_demand_t1[\"t1_tea_demand_model\"] = previous_day_demand_t1[\n", "    \"t1_tea_demand_model\"\n", "].astype(int)\n", "\n", "previous_day_demand_t2[\"t2_tea_demand\"] = previous_day_demand_t2[\"t2_tea_demand\"].astype(int)\n", "previous_day_demand_t2[\"t2_tea_demand_model\"] = previous_day_demand_t2[\n", "    \"t2_tea_demand_model\"\n", "].astype(int)\n", "\n", "indent_asst = (\n", "    forecast_asst.groupby(\n", "        [\"cluster\", \"backend_hot_outlet\", \"backend_ssc_outlet\", \"item_id\", \"is_dts\", \"shelf_life\"]\n", "    )\n", "    .agg(\n", "        {\n", "            \"t2_ds_forecast\": \"sum\",\n", "            \"t2_adjusted_dmd\": \"sum\",\n", "            \"t2_adjusted_dmd_model\": \"sum\",\n", "            \"t3_ds_forecast\": \"sum\",\n", "            \"t3_adjusted_dmd_model\": \"sum\",\n", "            \"t3_adjusted_dmd\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "indent_asst = pd.merge(\n", "    indent_asst,\n", "    be_inv_df,\n", "    left_on=[\"backend_hot_outlet\", \"item_id\"],\n", "    right_on=[\"backend_hot_outlet_id\", \"item_id\"],\n", "    how=\"left\",\n", ").drop(columns={\"backend_hot_outlet_id\"})\n", "indent_asst = pd.merge(\n", "    indent_asst,\n", "    blinkit_fe_inv_df,\n", "    left_on=[\"backend_hot_outlet\", \"item_id\"],\n", "    right_on=[\"backend_hot_outlet_id\", \"item_id\"],\n", "    how=\"left\",\n", ").drop(columns={\"backend_hot_outlet_id\"})\n", "indent_asst = pd.merge(\n", "    indent_asst,\n", "    open_po_df,\n", "    left_on=[\"backend_hot_outlet\", \"item_id\"],\n", "    right_on=[\"backend_hot_outlet_id\", \"item_id\"],\n", "    how=\"left\",\n", ").drop(columns={\"backend_hot_outlet_id\"})\n", "\n", "indent_asst = pd.merge(\n", "    indent_asst,\n", "    previous_day_forecast_overall_t1[\n", "        [\n", "            \"backend_hot_outlet_id\",\n", "            \"item_id\",\n", "            \"t1_indent\",\n", "            \"t1_forecast\",\n", "        ]\n", "    ],\n", "    left_on=[\"backend_hot_outlet\", \"item_id\"],\n", "    right_on=[\"backend_hot_outlet_id\", \"item_id\"],\n", "    how=\"left\",\n", ").drop(columns={\"backend_hot_outlet_id\"})\n", "\n", "indent_asst = pd.merge(\n", "    indent_asst,\n", "    previous_day_forecast_overall_t2[\n", "        [\n", "            \"backend_hot_outlet_id\",\n", "            \"item_id\",\n", "            \"t2_indent\",\n", "            \"t2_forecast\",\n", "        ]\n", "    ],\n", "    left_on=[\"backend_hot_outlet\", \"item_id\"],\n", "    right_on=[\"backend_hot_outlet_id\", \"item_id\"],\n", "    how=\"left\",\n", ").drop(columns={\"backend_hot_outlet_id\"})\n", "\n", "indent_asst[\"is_hp\"] = np.where(\n", "    indent_asst[\"backend_hot_outlet\"].isin(hyperpure_backend[\"backend_hot_outlet_id\"]),\n", "    1,\n", "    0,\n", ")\n", "\n", "indent_asst = pd.merge(\n", "    indent_asst,\n", "    previous_day_demand_t1,\n", "    left_on=[\"backend_hot_outlet\", \"item_id\"],\n", "    right_on=[\"backend_hot_outlet_id\", \"item_id\"],\n", "    how=\"left\",\n", ").drop(columns=[\"backend_hot_outlet_id\"])\n", "\n", "indent_asst = pd.merge(\n", "    indent_asst,\n", "    previous_day_demand_t2,\n", "    left_on=[\"backend_hot_outlet\", \"item_id\"],\n", "    right_on=[\"backend_hot_outlet_id\", \"item_id\"],\n", "    how=\"left\",\n", ").drop(columns=[\"backend_hot_outlet_id\"])\n", "\n", "indent_asst[\n", "    [\n", "        \"t1_forecast\",\n", "        \"be_inventory\",\n", "        \"fe_inventory\",\n", "        \"open_po_qty\",\n", "        \"t2_ds_forecast\",\n", "        \"t1_tea_demand\",\n", "        \"t1_tea_demand_model\",\n", "        \"t2_tea_demand_model\",\n", "        \"t2_tea_demand\",\n", "        \"t3_adjusted_dmd_model\",\n", "        \"t3_adjusted_dmd\",\n", "        \"t3_ds_forecast\",\n", "        \"t2_adjusted_dmd_model\",\n", "        \"predicted_leftover\",\n", "        \"t2_adjusted_dmd\",\n", "        \"t2_indent\",\n", "        \"t2_forecast\",\n", "        \"t1_indent\",\n", "    ]\n", "] = (\n", "    indent_asst[\n", "        [\n", "            \"t1_forecast\",\n", "            \"be_inventory\",\n", "            \"fe_inventory\",\n", "            \"open_po_qty\",\n", "            \"t2_ds_forecast\",\n", "            \"t1_tea_demand\",\n", "            \"t1_tea_demand_model\",\n", "            \"t2_tea_demand_model\",\n", "            \"t2_tea_demand\",\n", "            \"t3_adjusted_dmd_model\",\n", "            \"t3_adjusted_dmd\",\n", "            \"t3_ds_forecast\",\n", "            \"t2_adjusted_dmd_model\",\n", "            \"predicted_leftover\",\n", "            \"t2_adjusted_dmd\",\n", "            \"t2_indent\",\n", "            \"t2_forecast\",\n", "            \"t1_indent\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "indent_asst[\"total_inv\"] = indent_asst[\"fe_inventory\"] + indent_asst[\"be_inventory\"]\n", "\n", "indent_asst[\"total_inv_model\"] = indent_asst[\"predicted_leftover\"] + indent_asst[\"be_inventory\"]\n", "\n", "indent_asst[\"total_open_po\"] = np.where(\n", "    indent_asst[\"is_hp\"] == 1,\n", "    indent_asst[\"t1_indent\"],\n", "    indent_asst[\"open_po_qty\"],\n", ")\n", "\n", "indent_asst[\"t1_eod_inv\"] = np.where(\n", "    (indent_asst[\"be_inventory\"] + indent_asst[\"t1_indent\"]) - indent_asst[\"t1_tea_demand\"] > 0,\n", "    (indent_asst[\"be_inventory\"] + indent_asst[\"t1_indent\"]) - indent_asst[\"t1_tea_demand\"],\n", "    0,\n", ")\n", "\n", "indent_asst[\"t1_eod_inv_model\"] = np.where(\n", "    (indent_asst[\"be_inventory\"] + indent_asst[\"t1_indent\"]) - indent_asst[\"t1_tea_demand_model\"]\n", "    > 0,\n", "    (indent_asst[\"be_inventory\"] + indent_asst[\"t1_indent\"]) - indent_asst[\"t1_tea_demand_model\"],\n", "    0,\n", ")\n", "\n", "indent_asst[\"t1_eod_inv\"] = indent_asst[\"t1_eod_inv\"].astype(int)\n", "indent_asst[\"t1_eod_inv_model\"] = indent_asst[\"t1_eod_inv_model\"].astype(int)\n", "\n", "\n", "indent_asst[\"t1_eod_inv\"] = np.where(indent_asst[\"shelf_life\"] <= 1, 0, indent_asst[\"t1_eod_inv\"])\n", "\n", "indent_asst[\"t2_eod_inv\"] = np.where(\n", "    (indent_asst[\"t1_eod_inv\"] + indent_asst[\"t2_indent\"]) - indent_asst[\"t2_tea_demand\"] > 0,\n", "    (indent_asst[\"t1_eod_inv\"] + indent_asst[\"t2_indent\"]) - indent_asst[\"t2_tea_demand\"],\n", "    0,\n", ")\n", "indent_asst[\"t2_eod_inv_model\"] = np.where(\n", "    (indent_asst[\"t1_eod_inv_model\"] + indent_asst[\"t2_indent\"])\n", "    - indent_asst[\"t2_tea_demand_model\"]\n", "    > 0,\n", "    (indent_asst[\"t1_eod_inv_model\"] + indent_asst[\"t2_indent\"])\n", "    - indent_asst[\"t2_tea_demand_model\"],\n", "    0,\n", ")\n", "\n", "indent_asst[\"t2_eod_inv\"] = np.where(\n", "    indent_asst[\"shelf_life\"].isin([1, 2]),\n", "    np.where(\n", "        indent_asst[\"t2_indent\"]\n", "        + (indent_asst[\"t1_eod_inv\"] - indent_asst[\"t2_tea_demand\"]).clip(upper=0)\n", "        > 0,\n", "        indent_asst[\"t2_indent\"]\n", "        + (indent_asst[\"t1_eod_inv\"] - indent_asst[\"t2_tea_demand\"]).clip(upper=0),\n", "        0,\n", "    ),\n", "    np.where(\n", "        (indent_asst[\"t1_eod_inv\"] + indent_asst[\"t2_indent\"]) - indent_asst[\"t2_tea_demand\"] > 0,\n", "        (indent_asst[\"t1_eod_inv\"] + indent_asst[\"t2_indent\"]) - indent_asst[\"t2_tea_demand\"],\n", "        0,\n", "    ),\n", ")\n", "\n", "indent_asst[\"t2_eod_inv_model\"] = np.where(\n", "    indent_asst[\"shelf_life\"].isin([1, 2]),\n", "    np.where(\n", "        indent_asst[\"t2_indent\"]\n", "        + (indent_asst[\"t1_eod_inv_model\"] - indent_asst[\"t2_tea_demand_model\"]).clip(upper=0)\n", "        > 0,\n", "        indent_asst[\"t2_indent\"]\n", "        + (indent_asst[\"t1_eod_inv_model\"] - indent_asst[\"t2_tea_demand_model\"]).clip(upper=0),\n", "        0,\n", "    ),\n", "    np.where(\n", "        (indent_asst[\"t1_eod_inv_model\"] + indent_asst[\"t2_indent\"])\n", "        - indent_asst[\"t2_tea_demand_model\"]\n", "        > 0,\n", "        (indent_asst[\"t1_eod_inv_model\"] + indent_asst[\"t2_indent\"])\n", "        - indent_asst[\"t2_tea_demand_model\"],\n", "        0,\n", "    ),\n", ")\n", "\n", "indent_asst[\"t2_eod_inv\"] = indent_asst[\"t2_eod_inv\"].astype(int)\n", "indent_asst[\"t2_eod_inv_model\"] = indent_asst[\"t2_eod_inv_model\"].astype(int)\n", "\n", "\n", "indent_asst[\"t2_eod_inv\"] = np.where(indent_asst[\"shelf_life\"] <= 1, 0, indent_asst[\"t2_eod_inv\"])\n", "\n", "indent_asst[\"final_indent_qty_old\"] = np.where(\n", "    indent_asst[\"t3_adjusted_dmd\"] - indent_asst[\"t2_eod_inv\"] > 0,\n", "    indent_asst[\"t3_adjusted_dmd\"] - indent_asst[\"t2_eod_inv\"],\n", "    0,\n", ")\n", "\n", "indent_asst[\"final_indent_qty\"] = np.where(\n", "    indent_asst[\"t3_adjusted_dmd_model\"] - indent_asst[\"t2_eod_inv_model\"] > 0,\n", "    indent_asst[\"t3_adjusted_dmd_model\"] - indent_asst[\"t2_eod_inv_model\"],\n", "    0,\n", ")\n", "\n", "indent_asst[\"final_indent_qty\"] = indent_asst[\"final_indent_qty\"].astype(int)\n", "\n", "updated_at_ist = pd.to_datetime(datetime.now() - timedelta(days=0) + timedelta(hours=5.5))\n", "date_ist = (datetime.now() - timedelta(days=0) + timedelta(hours=5.5)).date()\n", "date_of_consumption = (datetime.now() + timedelta(days=3) + timedelta(hours=5.5)).date()\n", "\n", "indent_asst[\"date_ist\"] = date_ist\n", "indent_asst[\"date_of_consumption\"] = date_of_consumption\n", "indent_asst[\"updated_at_ist\"] = updated_at_ist\n", "indent_asst[\"date_ist\"] = pd.to_datetime(indent_asst[\"date_ist\"], format=\"%Y-%m-%d\")\n", "indent_asst[\"date_of_consumption\"] = pd.to_datetime(\n", "    indent_asst[\"date_of_consumption\"], format=\"%Y-%m-%d\"\n", ")\n", "\n", "\n", "forecast_asst[\"date_ist\"] = date_ist\n", "forecast_asst[\"date_of_consumption\"] = date_of_consumption\n", "forecast_asst[\"updated_at_ist\"] = updated_at_ist\n", "forecast_asst[\"date_ist\"] = pd.to_datetime(forecast_asst[\"date_ist\"], format=\"%Y-%m-%d\")\n", "forecast_asst[\"date_of_consumption\"] = pd.to_datetime(\n", "    forecast_asst[\"date_of_consumption\"], format=\"%Y-%m-%d\"\n", ")\n", "\n", "indent_asst = indent_asst.merge(\n", "    fixed_indent_category_input,\n", "    on=[\"date_of_consumption\", \"backend_hot_outlet\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "indent_asst[\"manual_indent_flag\"] = indent_asst[\"manual_indent_flag\"].fillna(0)\n", "\n", "indent_asst[\"final_indent_qty\"] = np.where(\n", "    indent_asst[\"manual_indent_flag\"] == 1,\n", "    indent_asst[\"fixed_indent_quantity\"],\n", "    indent_asst[\"final_indent_qty\"],\n", ")\n", "\n", "\n", "indent_asst[\"final_indent_qty_old\"] = np.where(\n", "    indent_asst[\"manual_indent_flag\"] == 1,\n", "    indent_asst[\"fixed_indent_quantity\"],\n", "    indent_asst[\"final_indent_qty_old\"],\n", ")\n", "\n", "indent_asst[\"final_indent_qty\"] = indent_asst[\"final_indent_qty\"].fillna(0).astype(int)\n", "\n", "indent_asst[\"final_indent_qty_old\"] = indent_asst[\"final_indent_qty_old\"].fillna(0).astype(int)\n", "\n", "check_duplicates(forecast_asst, [\"outlet_id\", \"item_id\"], \"forecast_asst\")\n", "check_duplicates(indent_asst, [\"cluster\", \"item_id\"], \"indent_asst\")"]}, {"cell_type": "code", "execution_count": null, "id": "d341a478-cba9-4821-90f8-03281dc4d077", "metadata": {}, "outputs": [], "source": ["indent_asst = indent_asst[\n", "    [\n", "        \"date_ist\",\n", "        \"date_of_consumption\",\n", "        \"cluster\",\n", "        \"backend_hot_outlet\",\n", "        \"backend_ssc_outlet\",\n", "        \"is_dts\",\n", "        \"is_hp\",\n", "        \"item_id\",\n", "        \"shelf_life\",\n", "        \"final_indent_qty\",\n", "        \"final_indent_qty_old\",\n", "        \"be_inventory\",\n", "        \"fe_inventory\",\n", "        \"predicted_leftover\",\n", "        \"t1_eod_inv_model\",\n", "        \"t2_eod_inv_model\",\n", "        \"open_po_qty\",\n", "        \"t1_indent\",\n", "        \"t2_indent\",\n", "        \"t1_forecast\",\n", "        \"t1_tea_demand_model\",\n", "        \"t2_ds_forecast\",\n", "        \"t2_tea_demand_model\",\n", "        \"t3_ds_forecast\",\n", "        \"t3_adjusted_dmd_model\",\n", "        \"fixed_indent_quantity\",\n", "        \"manual_indent_flag\",\n", "        \"updated_at_ist\",\n", "    ]\n", "]\n", "indent_asst"]}, {"cell_type": "code", "execution_count": null, "id": "8670bdc7-2a0f-4856-b54b-626c51ed2874", "metadata": {}, "outputs": [], "source": ["forecast_asst = forecast_asst[\n", "    [\n", "        \"date_ist\",\n", "        \"date_of_consumption\",\n", "        \"cluster\",\n", "        \"backend_hot_outlet\",\n", "        \"backend_ssc_outlet\",\n", "        \"outlet_id\",\n", "        \"facility_id\",\n", "        \"is_dts\",\n", "        \"item_id\",\n", "        \"shelf_life\",\n", "        \"predicted_leftover_t3\",\n", "        \"fe_inventory_t3\",\n", "        \"fe_inventory\",\n", "        \"predicted_leftover\",\n", "        \"t1_forecast\",\n", "        \"t1_dmd_exc_indent_model\",\n", "        \"t2_ds_forecast\",\n", "        \"t2_adjusted_dmd_model\",\n", "        \"t3_ds_forecast\",\n", "        \"t3_adjusted_dmd_model\",\n", "        \"updated_at_ist\",\n", "    ]\n", "]\n", "forecast_asst"]}, {"cell_type": "code", "execution_count": null, "id": "cbafe3f7-2422-41e7-9657-9df37c9c1928", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"fnv_indent_ordering_t_plus_n\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date_ist\", \"type\": \"date\", \"description\": \"date of entry\"},\n", "        {\"name\": \"date_of_consumption\", \"type\": \"date\", \"description\": \"date of consumption\"},\n", "        {\"name\": \"cluster\", \"type\": \"varchar\", \"description\": \"backend cluster\"},\n", "        {\n", "            \"name\": \"backend_hot_outlet\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"backend hot outlet\",\n", "        },\n", "        {\n", "            \"name\": \"backend_ssc_outlet\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"backend ssc outlet\",\n", "        },\n", "        {\"name\": \"is_dts\", \"type\": \"integer\", \"description\": \"is dts flag\"},\n", "        {\"name\": \"is_hp\", \"type\": \"integer\", \"description\": \"is hp flag\"},\n", "        {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item id\"},\n", "        {\n", "            \"name\": \"shelf_life\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"shelf life of item id\",\n", "        },\n", "        {\n", "            \"name\": \"final_indent_qty\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"final indent quantity as per store level model logic.\",\n", "        },\n", "        {\n", "            \"name\": \"final_indent_qty_old\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"final indent quantity as per store level logic.\",\n", "        },\n", "        {\n", "            \"name\": \"be_inventory\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"overall backend inventory\",\n", "        },\n", "        {\n", "            \"name\": \"fe_inventory\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"adjusted frontend inventory\",\n", "        },\n", "        {\n", "            \"name\": \"predicted_leftover\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"model output frontend inventory\",\n", "        },\n", "        {\n", "            \"name\": \"t1_eod_inv_model\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"model output frontend inventory\",\n", "        },\n", "        {\n", "            \"name\": \"t2_eod_inv_model\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"model output frontend inventory\",\n", "        },\n", "        {\n", "            \"name\": \"open_po_qty\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"open po qty\",\n", "        },\n", "        {\n", "            \"name\": \"t1_indent\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"t1_indent\",\n", "        },\n", "        {\n", "            \"name\": \"t2_indent\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"t2_indent\",\n", "        },\n", "        {\n", "            \"name\": \"t1_forecast\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"t1 forecast final\",\n", "        },\n", "        {\n", "            \"name\": \"t1_tea_demand_model\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"T1 demand as per tea at the time of T1 ordering.\",\n", "        },\n", "        {\n", "            \"name\": \"t2_ds_forecast\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"t2 forecast final\",\n", "        },\n", "        {\n", "            \"name\": \"t2_tea_demand_model\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"T2 demand as per tea at the time of T2 ordering.\",\n", "        },\n", "        {\n", "            \"name\": \"t3_ds_forecast\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"t3 forecast final\",\n", "        },\n", "        {\n", "            \"name\": \"t3_adjusted_dmd_model\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"t3 adjusted demand using fe inventory from model\",\n", "        },\n", "        {\n", "            \"name\": \"fixed_indent_quantity\",\n", "            \"type\": \"real\",\n", "            \"description\": \"fixed indent quantity\",\n", "        },\n", "        {\n", "            \"name\": \"manual_indent_flag\",\n", "            \"type\": \"real\",\n", "            \"description\": \"manual indent flag\",\n", "        },\n", "        {\n", "            \"name\": \"updated_at_ist\",\n", "            \"type\": \"timestamp(6)\",\n", "            \"description\": \"timestamp of entry\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\n", "        \"date_ist\",\n", "        \"date_of_consumption\",\n", "        \"cluster\",\n", "        \"backend_hot_outlet\",\n", "        \"item_id\",\n", "    ],\n", "    \"partition_key\": [\"date_ist\"],\n", "    \"incremental_key\": \"updated_at_ist\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"This table contains t+n indents with inventory transfer handling\",\n", "}\n", "\n", "to_trino(indent_asst, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "b309f3aa-198e-4ad5-8ae5-8f185e062582", "metadata": {}, "outputs": [], "source": ["complete_indent = (\n", "    indent_asst[indent_asst[\"is_dts\"] == 0]\n", "    .groupby([\"date_of_consumption\", \"cluster\"])\n", "    .agg({\"final_indent_qty\": \"sum\", \"t3_ds_forecast\": \"sum\", \"t2_eod_inv_model\": \"sum\"})\n", "    .reset_index()\n", ")\n", "total_row = complete_indent.sum(numeric_only=True)\n", "total_row[\"cluster\"] = \"Total\"\n", "total_row[\"date_of_consumption\"] = \"\"\n", "complete_indent = complete_indent.append(total_row, ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "93a39b9d-de1b-4766-8ab6-cce7612b6317", "metadata": {}, "outputs": [], "source": ["def render_mpl_table(\n", "    data,\n", "    col_width=6.0,\n", "    row_height=0.725,\n", "    font_size=20,\n", "    header_color=\"#E96125\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs)\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        #         elif k[0] == data.shape[0]:\n", "        #             cell.set_text_props(weight=\"bold\", ma=\"center\")\n", "        # cell.set_facecolor('#f1f1f2')\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "try:\n", "    fig, ax = render_mpl_table(complete_indent, header_columns=0)\n", "    fig.savefig(\"complete_indent.png\")\n", "except:\n", "    print(\"No CPC in run check\")"]}, {"cell_type": "code", "execution_count": null, "id": "f5c7bfc8-02f3-4023-a8d8-4f0d517f934d", "metadata": {}, "outputs": [], "source": ["try:\n", "    channel = \"bl-fnv-indent-check\"\n", "    if complete_indent.shape[0] > 0:\n", "        text_req = \"FnV T+3 Indent for CPC \\n cc:<@U07AA88LZU6>\"\n", "        pb.send_slack_message(\n", "            channel=channel,\n", "            text=text_req,\n", "            files=[\"./complete_indent.png\"],\n", "        )\n", "    else:\n", "        print(\"No Indent Created\")\n", "except:\n", "    print(\"CPC not in run check\")"]}, {"cell_type": "code", "execution_count": null, "id": "635cb798-a10d-4823-8fae-7d8e0dab52b9", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"fnv_indent_forecasts_t_plus_n\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date_ist\", \"type\": \"date\", \"description\": \"date of entry\"},\n", "        {\"name\": \"date_of_consumption\", \"type\": \"date\", \"description\": \"date of consumption\"},\n", "        {\"name\": \"cluster\", \"type\": \"varchar\", \"description\": \"backend cluster\"},\n", "        {\n", "            \"name\": \"backend_hot_outlet\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"backend hot outlet\",\n", "        },\n", "        {\n", "            \"name\": \"backend_ssc_outlet\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"backend ssc outlet\",\n", "        },\n", "        {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"outlet_id\"},\n", "        {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility_id\"},\n", "        {\"name\": \"is_dts\", \"type\": \"integer\", \"description\": \"is dts flag\"},\n", "        {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item id\"},\n", "        {\n", "            \"name\": \"shelf_life\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"shelf life of item id\",\n", "        },\n", "        {\n", "            \"name\": \"predicted_leftover_t3\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"predicted_leftover_t3\",\n", "        },\n", "        {\n", "            \"name\": \"fe_inventory_t3\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"fe_inventory_t3\",\n", "        },\n", "        {\n", "            \"name\": \"fe_inventory\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"adjusted frontend inventory\",\n", "        },\n", "        {\n", "            \"name\": \"predicted_leftover\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"model output frontend inventory\",\n", "        },\n", "        {\n", "            \"name\": \"t1_forecast\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"t1_forecast\",\n", "        },\n", "        {\n", "            \"name\": \"t1_dmd_exc_indent_model\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"t1_dmd_exc_indent_model\",\n", "        },\n", "        {\n", "            \"name\": \"t2_ds_forecast\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"t2_ds_forecast\",\n", "        },\n", "        {\n", "            \"name\": \"t2_adjusted_dmd_model\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"t2_adjusted_dmd_model\",\n", "        },\n", "        {\n", "            \"name\": \"t3_ds_forecast\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"t3_ds_forecast\",\n", "        },\n", "        {\n", "            \"name\": \"t3_adjusted_dmd_model\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"t3_adjusted_dmd_model\",\n", "        },\n", "        {\n", "            \"name\": \"updated_at_ist\",\n", "            \"type\": \"timestamp(6)\",\n", "            \"description\": \"timestamp of entry\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\n", "        \"date_ist\",\n", "        \"date_of_consumption\",\n", "        \"cluster\",\n", "        \"backend_hot_outlet\",\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "    ],\n", "    \"partition_key\": [\"date_ist\"],\n", "    \"incremental_key\": \"updated_at_ist\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"This table contains t+n indents forecasts with inventory transfer handling\",\n", "}\n", "\n", "to_trino(forecast_asst, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}