alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: leftover_forecast_model
dag_type: workflow
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: fresh
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U078DPW05T4
path: fresh/fnv_ordering/workflow/leftover_forecast_model
paused: false
pool: fresh_pool
project_name: fnv_ordering
schedule:
  end_date: '2025-09-20T00:00:00'
  interval: 30 21 * * *
  start_date: '2025-06-25T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
