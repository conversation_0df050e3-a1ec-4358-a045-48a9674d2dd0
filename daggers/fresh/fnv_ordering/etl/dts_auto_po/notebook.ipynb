{"cells": [{"cell_type": "code", "execution_count": null, "id": "58804e65-efe0-4889-a02c-10e62b057460", "metadata": {}, "outputs": [], "source": ["!pip install numpy==1.26.4"]}, {"cell_type": "code", "execution_count": null, "id": "db8a3430-2c11-466b-9e5a-567e4ed1267c", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import date, datetime, timedelta\n", "import pytz\n", "\n", "from datetime import datetime\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "\n", "# SQL Connection\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "markdown", "id": "3884ac29-3f7d-458c-8d6b-ba9e3ed3b4dc", "metadata": {}, "source": ["#### Outlet x Vendor x city x run_id config"]}, {"cell_type": "code", "execution_count": null, "id": "6392151f-259c-4138-8041-817011b91e24", "metadata": {}, "outputs": [], "source": ["# Keep adding new entries here\n", "outlet_config = {\n", "    5965: {\"city_id\": 21, \"run_id\": 202506044248002, \"vendor_id\": 13280},\n", "    6243: {\"city_id\": 21, \"run_id\": 202506044248002, \"vendor_id\": 13280},\n", "    6377: {\"city_id\": 21, \"run_id\": 202506044248002, \"vendor_id\": 13280},\n", "    4862: {\"city_id\": 43, \"run_id\": 202504083869002, \"vendor_id\": 13280},\n", "    6134: {\"city_id\": 43, \"run_id\": 202504083869002, \"vendor_id\": 13280},\n", "    4880: {\"city_id\": 43, \"run_id\": 202504083869002, \"vendor_id\": 13280},\n", "    5338: {\"city_id\": 43, \"run_id\": 202504083869002, \"vendor_id\": 13280},\n", "    5084: {\"city_id\": 43, \"run_id\": 202504083869002, \"vendor_id\": 13280},\n", "    5865: {\"city_id\": 49, \"run_id\": 202505064078001, \"vendor_id\": 13280},\n", "    4825: {\"city_id\": 53, \"run_id\": 202505064076001, \"vendor_id\": 13280},\n", "    4777: {\"city_id\": 53, \"run_id\": 202505064076001, \"vendor_id\": 13280},\n", "    5226: {\"city_id\": 53, \"run_id\": 202505064076001, \"vendor_id\": 13280},\n", "    5680: {\"city_id\": 67, \"run_id\": 202506054291001, \"vendor_id\": 13280},\n", "    5056: {\"city_id\": 68, \"run_id\": 202504183940001, \"vendor_id\": 13280},\n", "    4955: {\"city_id\": 68, \"run_id\": 202504183940001, \"vendor_id\": 13280},\n", "    6167: {\"city_id\": 68, \"run_id\": 202504183940001, \"vendor_id\": 13280},\n", "    6098: {\"city_id\": 68, \"run_id\": 202504183940001, \"vendor_id\": 13280},\n", "    6070: {\"city_id\": 68, \"run_id\": 202504183940001, \"vendor_id\": 13280},\n", "    4945: {\"city_id\": 68, \"run_id\": 202504183940001, \"vendor_id\": 13280},\n", "    5712: {\"city_id\": 68, \"run_id\": 202504183940001, \"vendor_id\": 13280},\n", "    4958: {\"city_id\": 68, \"run_id\": 202504183940001, \"vendor_id\": 13280},\n", "    4946: {\"city_id\": 68, \"run_id\": 202504183940001, \"vendor_id\": 13280},\n", "    4941: {\"city_id\": 68, \"run_id\": 202504183940001, \"vendor_id\": 13280},\n", "    5881: {\"city_id\": 69, \"run_id\": 202506054288001, \"vendor_id\": 13280},\n", "    5141: {\"city_id\": 73, \"run_id\": 202506054279001, \"vendor_id\": 13280},\n", "    5858: {\"city_id\": 73, \"run_id\": 202506054279001, \"vendor_id\": 13280},\n", "    5021: {\"city_id\": 73, \"run_id\": 202506054279001, \"vendor_id\": 13280},\n", "    5945: {\"city_id\": 73, \"run_id\": 202506054279001, \"vendor_id\": 13280},\n", "    4980: {\"city_id\": 73, \"run_id\": 202506054279001, \"vendor_id\": 13280},\n", "    5583: {\"city_id\": 74, \"run_id\": 202505164166001, \"vendor_id\": 13280},\n", "    6537: {\"city_id\": 74, \"run_id\": 202505164166001, \"vendor_id\": 13280},\n", "    5644: {\"city_id\": 74, \"run_id\": 202505164166001, \"vendor_id\": 13280},\n", "    5993: {\"city_id\": 74, \"run_id\": 202505164166001, \"vendor_id\": 13280},\n", "    5648: {\"city_id\": 74, \"run_id\": 202505164166001, \"vendor_id\": 13280},\n", "    5582: {\"city_id\": 74, \"run_id\": 202505164166001, \"vendor_id\": 13280},\n", "    5055: {\"city_id\": 75, \"run_id\": 202504083868001, \"vendor_id\": 13280},\n", "    6287: {\"city_id\": 75, \"run_id\": 202504083868001, \"vendor_id\": 13280},\n", "    5132: {\"city_id\": 75, \"run_id\": 202504083868001, \"vendor_id\": 13280},\n", "    4906: {\"city_id\": 75, \"run_id\": 202504083868001, \"vendor_id\": 13280},\n", "    5806: {\"city_id\": 77, \"run_id\": 202504083870001, \"vendor_id\": 13280},\n", "    5302: {\"city_id\": 82, \"run_id\": 202504294027002, \"vendor_id\": 13280},\n", "    5476: {\"city_id\": 82, \"run_id\": 202504294027002, \"vendor_id\": 13280},\n", "    5961: {\"city_id\": 85, \"run_id\": 202505164177001, \"vendor_id\": 13280},\n", "    5847: {\"city_id\": 88, \"run_id\": 202505264239001, \"vendor_id\": 13280},\n", "    5564: {\"city_id\": 95, \"run_id\": 202504083867001, \"vendor_id\": 13280},\n", "    5697: {\"city_id\": 97, \"run_id\": 202506054285001, \"vendor_id\": 13280},\n", "    5375: {\"city_id\": 102, \"run_id\": 202505064083001, \"vendor_id\": 13280},\n", "    5493: {\"city_id\": 102, \"run_id\": 202505064083001, \"vendor_id\": 13280},\n", "    6522: {\"city_id\": 102, \"run_id\": 202505064083001, \"vendor_id\": 13280},\n", "    6006: {\"city_id\": 102, \"run_id\": 202505064083001, \"vendor_id\": 13280},\n", "    5492: {\"city_id\": 102, \"run_id\": 202505064083001, \"vendor_id\": 13280},\n", "    6386: {\"city_id\": 102, \"run_id\": 202505064083001, \"vendor_id\": 13280},\n", "    6332: {\"city_id\": 102, \"run_id\": 202505064083001, \"vendor_id\": 13280},\n", "    5861: {\"city_id\": 104, \"run_id\": 202504183944001, \"vendor_id\": 13280},\n", "    5403: {\"city_id\": 104, \"run_id\": 202504183944001, \"vendor_id\": 13280},\n", "    5758: {\"city_id\": 105, \"run_id\": 202504183939001, \"vendor_id\": 13280},\n", "    5784: {\"city_id\": 105, \"run_id\": 202504183939001, \"vendor_id\": 13280},\n", "    5869: {\"city_id\": 106, \"run_id\": 202504183937002, \"vendor_id\": 13280},\n", "    5507: {\"city_id\": 114, \"run_id\": 202505164172001, \"vendor_id\": 13280},\n", "    5460: {\"city_id\": 115, \"run_id\": 202504294021002, \"vendor_id\": 13280},\n", "    4875: {\"city_id\": 126, \"run_id\": 202503053601003, \"vendor_id\": 13280},\n", "    5668: {\"city_id\": 126, \"run_id\": 202503053601003, \"vendor_id\": 13280},\n", "    4876: {\"city_id\": 126, \"run_id\": 202503053601003, \"vendor_id\": 13280},\n", "    5231: {\"city_id\": 126, \"run_id\": 202503053601003, \"vendor_id\": 13280},\n", "    5234: {\"city_id\": 126, \"run_id\": 202503053601003, \"vendor_id\": 13280},\n", "    6415: {\"city_id\": 129, \"run_id\": 202505284246001, \"vendor_id\": 13280},\n", "    6009: {\"city_id\": 165, \"run_id\": 202504183942001, \"vendor_id\": 13280},\n", "    6403: {\"city_id\": 165, \"run_id\": 202504183942001, \"vendor_id\": 13280},\n", "    6099: {\"city_id\": 165, \"run_id\": 202504183942001, \"vendor_id\": 13280},\n", "    5337: {\"city_id\": 165, \"run_id\": 202504183942001, \"vendor_id\": 13280},\n", "    5473: {\"city_id\": 165, \"run_id\": 202504183942001, \"vendor_id\": 13280},\n", "    5675: {\"city_id\": 165, \"run_id\": 202504183942001, \"vendor_id\": 13280},\n", "    6253: {\"city_id\": 171, \"run_id\": 202504294029002, \"vendor_id\": 13280},\n", "    5859: {\"city_id\": 171, \"run_id\": 202504294029002, \"vendor_id\": 13280},\n", "    5863: {\"city_id\": 177, \"run_id\": 202505064077001, \"vendor_id\": 13280},\n", "    6089: {\"city_id\": 179, \"run_id\": 202506054278001, \"vendor_id\": 13280},\n", "    5728: {\"city_id\": 188, \"run_id\": 202505164171001, \"vendor_id\": 13280},\n", "    5679: {\"city_id\": 188, \"run_id\": 202505164171001, \"vendor_id\": 13280},\n", "    5833: {\"city_id\": 199, \"run_id\": 202501213247001, \"vendor_id\": 13280},\n", "    5151: {\"city_id\": 199, \"run_id\": 202501213247001, \"vendor_id\": 13280},\n", "    5803: {\"city_id\": 199, \"run_id\": 202501213247001, \"vendor_id\": 13280},\n", "    5339: {\"city_id\": 199, \"run_id\": 202501213247001, \"vendor_id\": 13280},\n", "    5083: {\"city_id\": 199, \"run_id\": 202501213247001, \"vendor_id\": 13280},\n", "    5153: {\"city_id\": 199, \"run_id\": 202501213247001, \"vendor_id\": 13280},\n", "    5156: {\"city_id\": 199, \"run_id\": 202501213247001, \"vendor_id\": 13280},\n", "    5682: {\"city_id\": 200, \"run_id\": 202506184338001, \"vendor_id\": 13280},\n", "    6057: {\"city_id\": 200, \"run_id\": 202506184338001, \"vendor_id\": 13280},\n", "    5300: {\"city_id\": 207, \"run_id\": 202504183941001, \"vendor_id\": 13280},\n", "    5060: {\"city_id\": 207, \"run_id\": 202504183941001, \"vendor_id\": 13280},\n", "    6069: {\"city_id\": 207, \"run_id\": 202504183941001, \"vendor_id\": 13280},\n", "    4735: {\"city_id\": 207, \"run_id\": 202504183941001, \"vendor_id\": 13280},\n", "    5062: {\"city_id\": 207, \"run_id\": 202504183941001, \"vendor_id\": 13280},\n", "    4956: {\"city_id\": 207, \"run_id\": 202504183941001, \"vendor_id\": 13280},\n", "    6533: {\"city_id\": 207, \"run_id\": 202504183941001, \"vendor_id\": 13280},\n", "    4873: {\"city_id\": 211, \"run_id\": 202503243745001, \"vendor_id\": 13280},\n", "    5160: {\"city_id\": 211, \"run_id\": 202503243745001, \"vendor_id\": 13280},\n", "    6106: {\"city_id\": 211, \"run_id\": 202503243745001, \"vendor_id\": 13280},\n", "    4470: {\"city_id\": 211, \"run_id\": 202503243745001, \"vendor_id\": 13280},\n", "    4882: {\"city_id\": 211, \"run_id\": 202503243745001, \"vendor_id\": 13280},\n", "    4074: {\"city_id\": 221, \"run_id\": 202506054296001, \"vendor_id\": 13280},\n", "    5040: {\"city_id\": 221, \"run_id\": 202506054296001, \"vendor_id\": 13280},\n", "    3577: {\"city_id\": 221, \"run_id\": 202506054296001, \"vendor_id\": 13280},\n", "    5755: {\"city_id\": 221, \"run_id\": 202506054296001, \"vendor_id\": 13280},\n", "    3474: {\"city_id\": 221, \"run_id\": 202506054296001, \"vendor_id\": 13280},\n", "    6018: {\"city_id\": 221, \"run_id\": 202506054296001, \"vendor_id\": 13280},\n", "    3171: {\"city_id\": 221, \"run_id\": 202506054296001, \"vendor_id\": 13280},\n", "    4073: {\"city_id\": 221, \"run_id\": 202506054296001, \"vendor_id\": 13280},\n", "    6016: {\"city_id\": 221, \"run_id\": 202506054296001, \"vendor_id\": 13280},\n", "    4933: {\"city_id\": 221, \"run_id\": 202506054296001, \"vendor_id\": 13280},\n", "    4071: {\"city_id\": 221, \"run_id\": 202506054296001, \"vendor_id\": 13280},\n", "    5401: {\"city_id\": 221, \"run_id\": 202506054296001, \"vendor_id\": 13280},\n", "    4935: {\"city_id\": 221, \"run_id\": 202506054296001, \"vendor_id\": 13280},\n", "    6474: {\"city_id\": 221, \"run_id\": 202506054296001, \"vendor_id\": 13280},\n", "    6342: {\"city_id\": 221, \"run_id\": 202506054296001, \"vendor_id\": 13280},\n", "    5711: {\"city_id\": 224, \"run_id\": 202505164169001, \"vendor_id\": 13280},\n", "    5649: {\"city_id\": 224, \"run_id\": 202505164169001, \"vendor_id\": 13280},\n", "    5855: {\"city_id\": 227, \"run_id\": 202506054295001, \"vendor_id\": 13280},\n", "    5944: {\"city_id\": 227, \"run_id\": 202506054295001, \"vendor_id\": 13280},\n", "    5008: {\"city_id\": 227, \"run_id\": 202506054295001, \"vendor_id\": 13280},\n", "    6369: {\"city_id\": 227, \"run_id\": 202506054295001, \"vendor_id\": 13280},\n", "    6431: {\"city_id\": 227, \"run_id\": 202506054295001, \"vendor_id\": 13280},\n", "    5774: {\"city_id\": 227, \"run_id\": 202506054295001, \"vendor_id\": 13280},\n", "    4954: {\"city_id\": 227, \"run_id\": 202506054295001, \"vendor_id\": 13280},\n", "    4959: {\"city_id\": 227, \"run_id\": 202506054295001, \"vendor_id\": 13280},\n", "    4953: {\"city_id\": 227, \"run_id\": 202506054295001, \"vendor_id\": 13280},\n", "    5474: {\"city_id\": 227, \"run_id\": 202506054295001, \"vendor_id\": 13280},\n", "    5475: {\"city_id\": 227, \"run_id\": 202506054295001, \"vendor_id\": 13280},\n", "    5823: {\"city_id\": 227, \"run_id\": 202506054295001, \"vendor_id\": 13280},\n", "    4960: {\"city_id\": 227, \"run_id\": 202506054295001, \"vendor_id\": 13280},\n", "    6001: {\"city_id\": 227, \"run_id\": 202506054295001, \"vendor_id\": 13280},\n", "    5214: {\"city_id\": 227, \"run_id\": 202506054295001, \"vendor_id\": 13280},\n", "    5943: {\"city_id\": 227, \"run_id\": 202506054295001, \"vendor_id\": 13280},\n", "    5989: {\"city_id\": 227, \"run_id\": 202506054295001, \"vendor_id\": 13280},\n", "    5988: {\"city_id\": 252, \"run_id\": 202505064082001, \"vendor_id\": 13280},\n", "    6187: {\"city_id\": 262, \"run_id\": 202505064081001, \"vendor_id\": 13280},\n", "    5558: {\"city_id\": 262, \"run_id\": 202505064081001, \"vendor_id\": 13280},\n", "    5777: {\"city_id\": 263, \"run_id\": 202504294024002, \"vendor_id\": 13280},\n", "    5652: {\"city_id\": 270, \"run_id\": 202505164170001, \"vendor_id\": 13280},\n", "    5556: {\"city_id\": 272, \"run_id\": 202506054290001, \"vendor_id\": 13280},\n", "    5489: {\"city_id\": 277, \"run_id\": 202504083866001, \"vendor_id\": 13280},\n", "    5490: {\"city_id\": 277, \"run_id\": 202504083866001, \"vendor_id\": 13280},\n", "    5756: {\"city_id\": 277, \"run_id\": 202504083866001, \"vendor_id\": 13280},\n", "    5360: {\"city_id\": 277, \"run_id\": 202504083866001, \"vendor_id\": 13280},\n", "    6002: {\"city_id\": 277, \"run_id\": 202504083866001, \"vendor_id\": 13280},\n", "    5702: {\"city_id\": 279, \"run_id\": 202504294020002, \"vendor_id\": 13280},\n", "    5922: {\"city_id\": 279, \"run_id\": 202504294020002, \"vendor_id\": 13280},\n", "    4845: {\"city_id\": 279, \"run_id\": 202504294020002, \"vendor_id\": 13280},\n", "    6406: {\"city_id\": 279, \"run_id\": 202504294020002, \"vendor_id\": 13280},\n", "    6052: {\"city_id\": 285, \"run_id\": 202505284245001, \"vendor_id\": 13280},\n", "    6079: {\"city_id\": 287, \"run_id\": 202506184339001, \"vendor_id\": 13280},\n", "    6259: {\"city_id\": 287, \"run_id\": 202506184339001, \"vendor_id\": 13280},\n", "    5886: {\"city_id\": 289, \"run_id\": 202506054275001, \"vendor_id\": 13280},\n", "    5298: {\"city_id\": 289, \"run_id\": 202506054275001, \"vendor_id\": 13280},\n", "    5336: {\"city_id\": 289, \"run_id\": 202506054275001, \"vendor_id\": 13280},\n", "    4745: {\"city_id\": 291, \"run_id\": 202505064080001, \"vendor_id\": 13280},\n", "    4781: {\"city_id\": 292, \"run_id\": 202504183945001, \"vendor_id\": 13280},\n", "    5467: {\"city_id\": 292, \"run_id\": 202504183945001, \"vendor_id\": 13280},\n", "    5463: {\"city_id\": 292, \"run_id\": 202504183945001, \"vendor_id\": 13280},\n", "    5791: {\"city_id\": 295, \"run_id\": 202506184336001, \"vendor_id\": 13280},\n", "    5793: {\"city_id\": 298, \"run_id\": 202506054283001, \"vendor_id\": 13280},\n", "    5142: {\"city_id\": 300, \"run_id\": 202506054292001, \"vendor_id\": 13280},\n", "    6096: {\"city_id\": 300, \"run_id\": 202506054292001, \"vendor_id\": 13280},\n", "    6097: {\"city_id\": 300, \"run_id\": 202506054292001, \"vendor_id\": 13280},\n", "    5180: {\"city_id\": 301, \"run_id\": 202505064079001, \"vendor_id\": 13280},\n", "    5979: {\"city_id\": 304, \"run_id\": 202505164175001, \"vendor_id\": 13280},\n", "    5283: {\"city_id\": 304, \"run_id\": 202505164175001, \"vendor_id\": 13280},\n", "    5479: {\"city_id\": 305, \"run_id\": 202505164167001, \"vendor_id\": 13280},\n", "    5499: {\"city_id\": 306, \"run_id\": 202504294022002, \"vendor_id\": 13280},\n", "    5585: {\"city_id\": 307, \"run_id\": 202505064084001, \"vendor_id\": 13280},\n", "    5568: {\"city_id\": 307, \"run_id\": 202505064084001, \"vendor_id\": 13280},\n", "    5406: {\"city_id\": 308, \"run_id\": 202504294028002, \"vendor_id\": 13280},\n", "    5303: {\"city_id\": 308, \"run_id\": 202504294028002, \"vendor_id\": 13280},\n", "    5565: {\"city_id\": 309, \"run_id\": 202505164168001, \"vendor_id\": 13280},\n", "    5580: {\"city_id\": 310, \"run_id\": 202504083865002, \"vendor_id\": 13280},\n", "    6339: {\"city_id\": 310, \"run_id\": 202504083865002, \"vendor_id\": 13280},\n", "    5574: {\"city_id\": 312, \"run_id\": 202505164174001, \"vendor_id\": 13280},\n", "    5588: {\"city_id\": 313, \"run_id\": 202506054276001, \"vendor_id\": 13280},\n", "    6165: {\"city_id\": 313, \"run_id\": 202506054276001, \"vendor_id\": 13280},\n", "    5589: {\"city_id\": 313, \"run_id\": 202506054276001, \"vendor_id\": 13280},\n", "    6233: {\"city_id\": 313, \"run_id\": 202506054276001, \"vendor_id\": 13280},\n", "    5667: {\"city_id\": 315, \"run_id\": 202504083864002, \"vendor_id\": 13280},\n", "    5695: {\"city_id\": 315, \"run_id\": 202504083864002, \"vendor_id\": 13280},\n", "    5730: {\"city_id\": 317, \"run_id\": 202504183943001, \"vendor_id\": 13280},\n", "    5714: {\"city_id\": 318, \"run_id\": 202504294025002, \"vendor_id\": 13280},\n", "    5686: {\"city_id\": 320, \"run_id\": 202504083863003, \"vendor_id\": 13280},\n", "    5759: {\"city_id\": 322, \"run_id\": 202504083862002, \"vendor_id\": 13280},\n", "    5820: {\"city_id\": 324, \"run_id\": 202504083861002, \"vendor_id\": 13280},\n", "    5683: {\"city_id\": 326, \"run_id\": 202506054280001, \"vendor_id\": 13280},\n", "    5651: {\"city_id\": 327, \"run_id\": 202504294023002, \"vendor_id\": 13280},\n", "    6266: {\"city_id\": 330, \"run_id\": 202505164173001, \"vendor_id\": 13280},\n", "    5723: {\"city_id\": 330, \"run_id\": 202505164173001, \"vendor_id\": 13280},\n", "    5848: {\"city_id\": 331, \"run_id\": 202504183935001, \"vendor_id\": 13280},\n", "    5844: {\"city_id\": 333, \"run_id\": 202506054286001, \"vendor_id\": 13280},\n", "    5923: {\"city_id\": 334, \"run_id\": 202504183938001, \"vendor_id\": 13280},\n", "    5958: {\"city_id\": 335, \"run_id\": 202506054281001, \"vendor_id\": 13280},\n", "    5760: {\"city_id\": 339, \"run_id\": 202505064085001, \"vendor_id\": 13280},\n", "    5975: {\"city_id\": 340, \"run_id\": 202506054277001, \"vendor_id\": 13280},\n", "    5995: {\"city_id\": 344, \"run_id\": 202505064086001, \"vendor_id\": 13280},\n", "    5840: {\"city_id\": 345, \"run_id\": 202504294026002, \"vendor_id\": 13280},\n", "    5672: {\"city_id\": 348, \"run_id\": 202506184337001, \"vendor_id\": 13280},\n", "    6090: {\"city_id\": 349, \"run_id\": 202506054289001, \"vendor_id\": 13280},\n", "    5963: {\"city_id\": 352, \"run_id\": 202506054282001, \"vendor_id\": 13280},\n", "    6255: {\"city_id\": 356, \"run_id\": 202506054287001, \"vendor_id\": 13280},\n", "    6183: {\"city_id\": 357, \"run_id\": 202505164176001, \"vendor_id\": 13280},\n", "    5974: {\"city_id\": 358, \"run_id\": 202505284244001, \"vendor_id\": 13280},\n", "    6338: {\"city_id\": 359, \"run_id\": 202505284251001, \"vendor_id\": 13280},\n", "    6105: {\"city_id\": 361, \"run_id\": 202505284250001, \"vendor_id\": 13280},\n", "    6303: {\"city_id\": 364, \"run_id\": 202505274241001, \"vendor_id\": 13280},\n", "    6328: {\"city_id\": 366, \"run_id\": 202505284247001, \"vendor_id\": 13280},\n", "    6462: {\"city_id\": 367, \"run_id\": 202505274243001, \"vendor_id\": 13280},\n", "    4283: {\"city_id\": 370, \"run_id\": 202505284249001, \"vendor_id\": 13280},\n", "    6283: {\"city_id\": 370, \"run_id\": 202505284249001, \"vendor_id\": 13280},\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "09a1651e-3df8-4c03-899d-16a0a0e9997d", "metadata": {}, "outputs": [], "source": ["outlet_config_df = pd.DataFrame.from_dict(outlet_config, orient=\"index\").reset_index()\n", "outlet_config_df.rename(columns={\"index\": \"outlet_id\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "370bfea8-537f-41f5-a06d-fb2a09ab1463", "metadata": {}, "outputs": [], "source": ["outlet_config_df"]}, {"cell_type": "code", "execution_count": null, "id": "f6d98320-053f-4204-bef0-1c11e6ed043c", "metadata": {}, "outputs": [], "source": ["indent_sql = f\"\"\"\n", "with max_created_at as (\n", "select\n", "    outlet_id,\n", "    max(created_at_ist) max_created_at_ist\n", "from\n", "    ars_etls.ars_fnv_ordering_dts_indent\n", "where\n", "    consumption_date = current_date + interval '2' day\n", "group by 1\n", ")\n", "select\n", "    consumption_date,\n", "    ind.outlet_id,\n", "    pfom.outlet_name,\n", "    item_id,\n", "    max(final_indent_qty) final_indent_qty\n", "from\n", "    ars_etls.ars_fnv_ordering_dts_indent ind\n", "join\n", "    max_created_at mca\n", "    on ind.outlet_id = mca.outlet_id\n", "    and ind.created_at_ist = mca.max_created_at_ist\n", "join\n", "    po.physical_facility_outlet_mapping pfom\n", "    on pfom.outlet_id = ind.outlet_id\n", "    and pfom.lake_active_record \n", "    and pfom.active = 1 \n", "    and pfom.ars_active = 1\n", "where\n", "    ind.consumption_date = current_date + interval '2' day\n", "group by 1,2,3,4\n", "\"\"\"\n", "indent_df = read_sql_query(indent_sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "a9b645fd-f1f2-414e-aa93-e55bcb841ec9", "metadata": {}, "outputs": [], "source": ["indent_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "6000da8d-8dd2-4ae8-a648-657b6a69cae4", "metadata": {}, "outputs": [], "source": ["indent_df[[\"outlet_id\", \"item_id\"]].duplicated().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "dd0a8232-508d-4214-a005-b2dba7034007", "metadata": {}, "outputs": [], "source": ["final_result = indent_df.merge(outlet_config_df, on=\"outlet_id\", how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "id": "c0f6cf45-53ec-4469-81eb-18ff5abe0393", "metadata": {}, "outputs": [], "source": ["len(final_result[\"outlet_id\"].unique())"]}, {"cell_type": "code", "execution_count": null, "id": "3cb27478-09f6-4953-a8cf-c232236adf77", "metadata": {}, "outputs": [], "source": ["final_result[[\"outlet_id\", \"item_id\"]].duplicated().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "f700669f-a9ba-4792-a1ce-a5a3c85e915b", "metadata": {}, "outputs": [], "source": ["final_result.shape"]}, {"cell_type": "code", "execution_count": null, "id": "9c9fb610-6616-4c65-8b20-af50154ce0e6", "metadata": {}, "outputs": [], "source": ["final_result.head()"]}, {"cell_type": "code", "execution_count": null, "id": "fbae77f5-1648-4f45-9f33-ab34159f3deb", "metadata": {}, "outputs": [], "source": ["final_result = final_result.rename(\n", "    columns={\"consumption_date\": \"fdate_\", \"outlet_name\": \"be_outlet_name\"}\n", ")\n", "final_result[\"fdate_\"] = pd.to_datetime(final_result[\"fdate_\"])\n", "final_result[\"be_outlet_id\"] = 0"]}, {"cell_type": "code", "execution_count": null, "id": "57350153-6acd-477c-a62e-1c4f7d38b6dc", "metadata": {}, "outputs": [], "source": ["final_result = final_result[\n", "    [\n", "        \"fdate_\",\n", "        \"be_outlet_id\",\n", "        \"be_outlet_name\",\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"final_indent_qty\",\n", "        \"run_id\",\n", "        \"vendor_id\",\n", "        \"city_id\",\n", "    ]\n", "]\n", "final_result"]}, {"cell_type": "code", "execution_count": null, "id": "c5e076de-7314-4801-bd44-62dd26a74941", "metadata": {}, "outputs": [], "source": ["dropped_df = final_result[final_result.isnull().any(axis=1)].copy()\n", "\n", "if dropped_df.shape[0] > 0:\n", "    dropped_df.to_csv(\"dropped.csv\")\n", "    channel = \"temp-dts-new-flow\"\n", "    text_req = \"\\n <@U05CCTXLBU1> \\n Missing Backend and Outlet Id Mapping \"\n", "    pb.send_slack_message(channel=channel, text=text_req, files=[\"dropped.csv\"])\n", "else:\n", "    pass"]}, {"cell_type": "code", "execution_count": null, "id": "7e1c7b44-fc76-4fa4-98f8-839fca657893", "metadata": {}, "outputs": [], "source": ["final_result = final_result.dropna().reset_index(drop=True)\n", "final_result.shape"]}, {"cell_type": "code", "execution_count": null, "id": "3248a359-e527-4054-9992-b22adfa5e20e", "metadata": {}, "outputs": [], "source": ["# final_result[\"updated_at_ist\"] = datetime.today() + timedelta(hours=5.5)\n", "# final_result.fdate_ = pd.to_datetime(final_result.fdate_)"]}, {"cell_type": "code", "execution_count": null, "id": "992ed375-6a26-4b4b-995b-e9203ad580ff", "metadata": {}, "outputs": [], "source": ["final_result.shape"]}, {"cell_type": "markdown", "id": "1bad6b92-94d3-4613-8d6f-7939d217510a", "metadata": {"tags": []}, "source": ["# Mandi Closure"]}, {"cell_type": "markdown", "id": "9b4ac391-fcc8-4542-a712-9425f93c4420", "metadata": {"tags": []}, "source": ["## Fields for Upload Data"]}, {"cell_type": "code", "execution_count": null, "id": "677a06ce-4ae0-45b9-a0a4-7d7b30f76f52", "metadata": {}, "outputs": [], "source": ["base_run_id = (\n", "    final_result[[\"outlet_id\", \"run_id\", \"vendor_id\", \"city_id\"]]\n", "    .groupby([\"outlet_id\", \"run_id\", \"vendor_id\", \"city_id\"])\n", "    .size()\n", "    .reset_index(name=\"count\")\n", ")\n", "base_run_id = base_run_id[[\"outlet_id\", \"run_id\", \"vendor_id\", \"city_id\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "a1aa0155-a152-4e96-b0b5-36f0510d6b4c", "metadata": {}, "outputs": [], "source": ["base_run_id"]}, {"cell_type": "markdown", "id": "72fa2407-ec42-466a-9de1-1f10d482b972", "metadata": {"tags": []}, "source": ["## Input Data"]}, {"cell_type": "code", "execution_count": null, "id": "bfebdd76-5992-46dd-8046-e1d8574d0228", "metadata": {}, "outputs": [], "source": ["# df_mandi_closure = pd.read_csv(\"mandi_closure_testing.csv\")\n", "df_mandi_closure = pb.from_sheets(\"1NMzNTgvSMajNuWnXM1zLxF0ju2DIhmX6U6OEOuSJ8OY\", \"Sheet1\")\n", "df_mandi_closure = df_mandi_closure[\n", "    [\"day_of_consumption\", \"store_outlet_id\", \"item_id\", \"indent_qty\", \"flag_\"]\n", "].copy()"]}, {"cell_type": "code", "execution_count": null, "id": "32f7accb-99fc-43d2-865e-55499d6a85cd", "metadata": {}, "outputs": [], "source": ["df_mandi_closure.shape"]}, {"cell_type": "code", "execution_count": null, "id": "a226672e-c7b1-4213-be65-e6161317def5", "metadata": {}, "outputs": [], "source": ["df_mandi_closure = df_mandi_closure.groupby(\n", "    [\"day_of_consumption\", \"store_outlet_id\", \"item_id\", \"flag_\"], as_index=False\n", ")[\"indent_qty\"].max()"]}, {"cell_type": "code", "execution_count": null, "id": "c44bdb56-ae9e-44fb-bd7b-7899b6276d89", "metadata": {}, "outputs": [], "source": ["df_mandi_closure[\"day_of_consumption\"] = pd.to_datetime(df_mandi_closure[\"day_of_consumption\"])\n", "df_mandi_closure[\"store_outlet_id\"] = df_mandi_closure[\"store_outlet_id\"].astype(int)\n", "df_mandi_closure[\"item_id\"] = df_mandi_closure[\"item_id\"].astype(int)\n", "df_mandi_closure[\"indent_qty\"] = df_mandi_closure[\"indent_qty\"].astype(int)\n", "df_mandi_closure[\"flag_\"] = df_mandi_closure[\"flag_\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "e5483d58-f533-418f-abd5-26d2418a7af5", "metadata": {}, "outputs": [], "source": ["ist = pytz.timezone(\"Asia/Kolkata\")\n", "today = datetime.now(ist).date()\n", "target_dates = [today + timedelta(days=2), today + timedelta(days=3)]"]}, {"cell_type": "code", "execution_count": null, "id": "569c13e3-5412-404c-b4b0-f54c838e25c2", "metadata": {}, "outputs": [], "source": ["df_mandi_closure = df_mandi_closure[\n", "    (df_mandi_closure[\"indent_qty\"] > 0)\n", "    & (df_mandi_closure[\"day_of_consumption\"].dt.date.isin(target_dates))\n", "].copy()"]}, {"cell_type": "code", "execution_count": null, "id": "46060a23-7f85-4b97-a488-79aef186bfbe", "metadata": {}, "outputs": [], "source": ["df_mandi_closure.shape\n", "# Final Mandi Closure Universe"]}, {"cell_type": "code", "execution_count": null, "id": "4446958a-8aa2-48b3-abc6-46de52d5fca5", "metadata": {}, "outputs": [], "source": ["# df_mandi_closure_fina"]}, {"cell_type": "code", "execution_count": null, "id": "a848d52d-3a4e-4f29-ab55-4d313d6eb600", "metadata": {}, "outputs": [], "source": ["df_mandi_closure_final = df_mandi_closure.merge(\n", "    base_run_id, how=\"left\", left_on=[\"store_outlet_id\"], right_on=[\"outlet_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "39f20c16-a9bc-49bc-a1d4-11ba4ebfdcbc", "metadata": {}, "outputs": [], "source": ["df_mandi_closure_final.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c5df03bd-fd3d-4cc6-bd80-045572e0dded", "metadata": {}, "outputs": [], "source": ["df_mandi_closure_final = df_mandi_closure_final.dropna().copy()"]}, {"cell_type": "code", "execution_count": null, "id": "32dd005f-b8f0-48e1-9f22-37e252efbdc0", "metadata": {}, "outputs": [], "source": ["df_mandi_closure_final.shape"]}, {"cell_type": "code", "execution_count": null, "id": "6fce60bb-8ad8-4c52-a301-f1278135e657", "metadata": {}, "outputs": [], "source": ["df_mandi_closure_final"]}, {"cell_type": "code", "execution_count": null, "id": "60a2f58f-4f67-4c36-8ead-40f563e8f40d", "metadata": {}, "outputs": [], "source": ["df_mandi_closure_final[\"be_outlet_id\"] = 0\n", "df_mandi_closure_final[\"be_outlet_name\"] = \"\""]}, {"cell_type": "code", "execution_count": null, "id": "d04ea0ce-0884-4468-823c-bab4c990b3f1", "metadata": {}, "outputs": [], "source": ["df_mandi_closure_final = df_mandi_closure_final[\n", "    [\n", "        \"day_of_consumption\",\n", "        \"be_outlet_id\",\n", "        \"be_outlet_name\",\n", "        \"store_outlet_id\",\n", "        \"item_id\",\n", "        \"indent_qty\",\n", "        \"run_id\",\n", "        \"vendor_id\",\n", "        \"city_id\",\n", "        \"flag_\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "ceb4ec50-beee-4625-a7f3-71514efc40ee", "metadata": {}, "outputs": [], "source": ["df_mandi_closure_final.rename(\n", "    columns={\n", "        \"day_of_consumption\": \"fdate_\",\n", "        \"store_outlet_id\": \"outlet_id\",\n", "        \"indent_qty\": \"final_indent_qty\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "02cf9ff2-b7d9-47e8-999a-50447a83ca5f", "metadata": {}, "outputs": [], "source": ["df_mandi_closure_final"]}, {"cell_type": "markdown", "id": "09866db3-6a21-48cb-b911-c15d9f1d37a9", "metadata": {}, "source": ["### Removing All the outlet ids for Mandi Closure and Forecasting for T+2"]}, {"cell_type": "code", "execution_count": null, "id": "7ed24bd1-343a-4a12-8b16-3ecfc9165032", "metadata": {}, "outputs": [], "source": ["df_mandi_closure_final_filtered = df_mandi_closure_final[\n", "    df_mandi_closure_final[\"fdate_\"].dt.date == today + timedelta(days=2)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "1bb43e3b-5bf9-434e-ac1c-e13c6180ee47", "metadata": {}, "outputs": [], "source": ["df_mandi_closure_final_filtered"]}, {"cell_type": "code", "execution_count": null, "id": "36410c1a-44df-49e8-bead-af1866e885f9", "metadata": {}, "outputs": [], "source": ["outlet_removed = list(df_mandi_closure_final_filtered.outlet_id.unique())"]}, {"cell_type": "code", "execution_count": null, "id": "33b6a7e7-d861-48ba-92de-c94099097190", "metadata": {}, "outputs": [], "source": ["outlet_removed"]}, {"cell_type": "code", "execution_count": null, "id": "a8322a1b-9a4c-4284-9cd0-4585ae578b92", "metadata": {}, "outputs": [], "source": ["if len(outlet_removed) > 0:\n", "    channel = \"bl-fnv-replenishment-dag-run-alerts\"\n", "    text_req = (\n", "        \"\\n <@U05CCTXLBU1> \\n Removing these outlet ids from Base Universe in DTS PO and reading it from the sheet \"\n", "        + str(outlet_removed)\n", "    )\n", "    pb.send_slack_message(\n", "        channel=channel,\n", "        text=text_req,\n", "    )\n", "else:\n", "    pass"]}, {"cell_type": "code", "execution_count": null, "id": "89e2b0af-5598-48c4-b0d7-313ce460438f", "metadata": {}, "outputs": [], "source": ["final_result.shape"]}, {"cell_type": "code", "execution_count": null, "id": "1e3e1619-4cde-4592-8753-fbb432b38414", "metadata": {}, "outputs": [], "source": ["final_result = final_result[~final_result[\"outlet_id\"].isin(outlet_removed)]"]}, {"cell_type": "code", "execution_count": null, "id": "23c7d15c-70f2-4279-834a-b55460a21d7f", "metadata": {}, "outputs": [], "source": ["final_result.shape"]}, {"cell_type": "markdown", "id": "bb00c6f3-1b3b-49fa-8e1e-82126a0444d6", "metadata": {"tags": []}, "source": ["## Putting in Main Dataframe for Normal Delivery at Store"]}, {"cell_type": "code", "execution_count": null, "id": "83362e00-216f-43d4-a8c1-8855e17d6631", "metadata": {}, "outputs": [], "source": ["df_mandi_closure_final_first = df_mandi_closure_final[\n", "    (df_mandi_closure_final[\"flag_\"] == 0)\n", "    & (df_mandi_closure_final[\"fdate_\"].dt.date == today + timedelta(days=2))\n", "].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "d152eac0-978c-4a92-88aa-b1853000008c", "metadata": {}, "outputs": [], "source": ["df_mandi_closure_final_first.head()"]}, {"cell_type": "code", "execution_count": null, "id": "799a0e4d-3c44-4020-8233-e31067c6b2b3", "metadata": {}, "outputs": [], "source": ["outlet_id_replaced_with_mandi_closure = list(df_mandi_closure_final_first.outlet_id.unique())"]}, {"cell_type": "code", "execution_count": null, "id": "aaa166ed-9a9d-449a-9a8a-3ccdbefabfc9", "metadata": {}, "outputs": [], "source": ["if len(outlet_id_replaced_with_mandi_closure) > 0:\n", "    channel = \"bl-fnv-replenishment-dag-run-alerts\"\n", "    text_req = (\n", "        \"\\n <@U05CCTXLBU1> \\n Replaced the indent of Mandi Closure from sheet for this outlet ids  \"\n", "        + str(outlet_id_replaced_with_mandi_closure)\n", "    )\n", "    pb.send_slack_message(\n", "        channel=channel,\n", "        text=text_req,\n", "    )\n", "else:\n", "    pass"]}, {"cell_type": "code", "execution_count": null, "id": "f811b0ee-97f8-4f01-bc8e-1e49c77eb075", "metadata": {}, "outputs": [], "source": ["df_mandi_closure_final_first[\"fdate_\"] = df_mandi_closure_final_first[\"fdate_\"].astype(object)"]}, {"cell_type": "code", "execution_count": null, "id": "01432e26-2f31-4d0c-b193-99f0dda77050", "metadata": {}, "outputs": [], "source": ["df_mandi_closure_final_first.shape"]}, {"cell_type": "code", "execution_count": null, "id": "47abeb25-7bb2-462e-8fd5-b390a4500a49", "metadata": {}, "outputs": [], "source": ["df_mandi_closure_final_first = df_mandi_closure_final_first[\n", "    [\n", "        \"fdate_\",\n", "        \"be_outlet_id\",\n", "        \"be_outlet_name\",\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"final_indent_qty\",\n", "        \"run_id\",\n", "        \"vendor_id\",\n", "        \"city_id\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "d22095c4-58b0-49a4-ac5a-ff1307cf2c87", "metadata": {}, "outputs": [], "source": ["df_mandi_closure_final_first.shape"]}, {"cell_type": "code", "execution_count": null, "id": "a0b6210c-3803-43f5-bcf5-10e9e8ef284f", "metadata": {}, "outputs": [], "source": ["final_result.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c4009ddf-ae6f-4234-aca9-aacdcaa96eaf", "metadata": {}, "outputs": [], "source": ["final_result1 = pd.concat([final_result, df_mandi_closure_final_first], ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "c7b695bb-baff-463f-97a9-c8cec5b0d382", "metadata": {}, "outputs": [], "source": ["final_result1.shape"]}, {"cell_type": "markdown", "id": "e9e07551-c0fe-4de8-973e-80bb4126aae4", "metadata": {}, "source": ["### Adding in the main indent qty for Mandi Closure with flag 1"]}, {"cell_type": "code", "execution_count": null, "id": "33376c21-87c0-450c-8296-714d5f749052", "metadata": {}, "outputs": [], "source": ["df_mandi_closure_final_second = df_mandi_closure_final[\n", "    (df_mandi_closure_final[\"flag_\"] == 1)\n", "    & (df_mandi_closure_final[\"fdate_\"].dt.date == today + timedelta(days=3))\n", "].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "e86abc1f-4915-4441-8c68-bc57eb13b094", "metadata": {}, "outputs": [], "source": ["df_mandi_closure_final_second.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2d274d56-0af1-4ee5-afda-b1a61cc4ce06", "metadata": {}, "outputs": [], "source": ["outlet_id_adding_in_normal_indent = list(df_mandi_closure_final_second.outlet_id.unique())"]}, {"cell_type": "code", "execution_count": null, "id": "ba7cdb71-79f6-4713-9b7c-b1cd2503d1aa", "metadata": {}, "outputs": [], "source": ["if len(outlet_id_adding_in_normal_indent) > 0:\n", "    channel = \"bl-fnv-replenishment-dag-run-alerts\"\n", "    text_req = (\n", "        \"\\n <@U05CCTXLBU1> \\n Added Indent of Mandi Closure from sheet in the Normal Indent \"\n", "        + str(outlet_id_adding_in_normal_indent)\n", "    )\n", "    pb.send_slack_message(\n", "        channel=channel,\n", "        text=text_req,\n", "    )\n", "else:\n", "    pass"]}, {"cell_type": "code", "execution_count": null, "id": "cc16d6a3-313a-49cb-8171-152d15ca3349", "metadata": {}, "outputs": [], "source": ["df_mandi_closure_final_second[\"fdate_\"] = df_mandi_closure_final_second[\"fdate_\"] - <PERSON><PERSON><PERSON>(\n", "    days=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0eabbbae-b624-40eb-b518-a48f438a6575", "metadata": {}, "outputs": [], "source": ["outlet_id_changes = list(df_mandi_closure_final_second.outlet_id.unique())"]}, {"cell_type": "code", "execution_count": null, "id": "913027aa-2c81-4eb0-b72a-4ca987675550", "metadata": {}, "outputs": [], "source": ["outlet_id_changes"]}, {"cell_type": "code", "execution_count": null, "id": "1b756858-1dae-48f8-a16c-fcbf1d81d9c0", "metadata": {}, "outputs": [], "source": ["final_result1.loc[\n", "    final_result1[\"outlet_id\"].isin(outlet_id_changes), [\"be_outlet_id\", \"be_outlet_name\"]\n", "] = [0, \"\"]"]}, {"cell_type": "code", "execution_count": null, "id": "1ba93361-e288-4f22-a10f-47daf1299c56", "metadata": {}, "outputs": [], "source": ["df_mandi_closure_final_second = df_mandi_closure_final_second[\n", "    [\n", "        \"fdate_\",\n", "        \"be_outlet_id\",\n", "        \"be_outlet_name\",\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"final_indent_qty\",\n", "        \"run_id\",\n", "        \"vendor_id\",\n", "        \"city_id\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "daf38f80-5433-4fd4-903e-250b4dbe057d", "metadata": {}, "outputs": [], "source": ["df_mandi_closure_final_second.shape"]}, {"cell_type": "code", "execution_count": null, "id": "1c1d97a3-f68f-479f-95ca-b7381794834d", "metadata": {}, "outputs": [], "source": ["final_result1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "7e4021e2-ef46-4cb3-a32c-00a63e7159b3", "metadata": {}, "outputs": [], "source": ["final_result2 = pd.concat([final_result1, df_mandi_closure_final_second], axis=0, ignore_index=True)\n", "final_result2.shape"]}, {"cell_type": "code", "execution_count": null, "id": "b3cab0a7-4658-4ba8-919d-2929b8b252d2", "metadata": {}, "outputs": [], "source": ["# final_result2[\"fdate_\"].unique()\n", "final_result2[\"fdate_\"] = pd.to_datetime(final_result2[\"fdate_\"]).dt.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "3af2085c-86b6-4bca-88a8-8fe1cb79bf67", "metadata": {}, "outputs": [], "source": ["final_result2"]}, {"cell_type": "code", "execution_count": null, "id": "6883441f-34cd-439e-bfe2-e2626a9843f2", "metadata": {}, "outputs": [], "source": ["final_result3 = final_result2.groupby(\n", "    [\n", "        \"fdate_\",\n", "        \"be_outlet_id\",\n", "        \"be_outlet_name\",\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"run_id\",\n", "        \"vendor_id\",\n", "        \"city_id\",\n", "    ],\n", "    as_index=False,\n", ")[\"final_indent_qty\"].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "0cbf2bc3-e7c7-47ba-ba7b-10be465da65a", "metadata": {}, "outputs": [], "source": ["final_result3"]}, {"cell_type": "markdown", "id": "9ebc2239-aa72-4e7a-942f-6ad0ccf63dd1", "metadata": {}, "source": ["## Checking HP Product Code Mapping"]}, {"cell_type": "code", "execution_count": null, "id": "d97c654d-7e29-4359-a35d-cc6929bb2877", "metadata": {}, "outputs": [], "source": ["final_result3.shape"]}, {"cell_type": "code", "execution_count": null, "id": "b8bfe8a9-b0a8-40b8-9c2e-4aacd5898405", "metadata": {}, "outputs": [], "source": ["sql = f\"\"\"\n", "\n", "with base as(\n", "select item_id, partner_item_id, integration_partner from po.edi_integration_partner_item_mapping\n", "where active = true\n", "and lake_active_record = true\n", "and integration_partner = 'HYPERPURE'\n", ")\n", "\n", "select distinct item_id from base\n", "\"\"\"\n", "check_item_id = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "7952a934-aad5-461f-bbd3-fbefe196b0df", "metadata": {}, "outputs": [], "source": ["check_item_id.shape"]}, {"cell_type": "code", "execution_count": null, "id": "bcf79158-9d52-4f8a-8e00-91db33279287", "metadata": {}, "outputs": [], "source": ["final_result3 = final_result3[final_result3[\"item_id\"].isin(check_item_id[\"item_id\"])]"]}, {"cell_type": "code", "execution_count": null, "id": "3c4fd4f3-03fb-4915-adac-84aa9972be45", "metadata": {}, "outputs": [], "source": ["final_result3.shape"]}, {"cell_type": "markdown", "id": "77fca1db-a1b0-4c47-91b0-38d9d7789a14", "metadata": {}, "source": ["# Final Table Push"]}, {"cell_type": "code", "execution_count": null, "id": "12e5417a-e8f7-4853-8051-042bbd27325d", "metadata": {}, "outputs": [], "source": ["check_duplicate = final_result3.duplicated().any()"]}, {"cell_type": "code", "execution_count": null, "id": "8853ebae-8ada-440c-9cca-96411a811e6f", "metadata": {}, "outputs": [], "source": ["check_duplicate"]}, {"cell_type": "code", "execution_count": null, "id": "84ac2242-6813-4840-98ca-7b1d094be400", "metadata": {}, "outputs": [], "source": ["final_result3[\"updated_at_ist\"] = datetime.today() + timedelta(hours=5.5)\n", "final_result3.fdate_ = pd.to_datetime(final_result3.fdate_)"]}, {"cell_type": "code", "execution_count": null, "id": "b5a67660-8c7f-4f5b-945a-1cc52eface3a", "metadata": {}, "outputs": [], "source": ["final_result3 = (\n", "    final_result3.groupby(\n", "        [\n", "            \"fdate_\",\n", "            \"be_outlet_id\",\n", "            \"be_outlet_name\",\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"run_id\",\n", "            \"vendor_id\",\n", "            \"city_id\",\n", "            \"updated_at_ist\",\n", "        ]\n", "    )\n", "    .agg({\"final_indent_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "final_result3 = final_result3[\n", "    [\n", "        \"fdate_\",\n", "        \"be_outlet_id\",\n", "        \"be_outlet_name\",\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"final_indent_qty\",\n", "        \"run_id\",\n", "        \"vendor_id\",\n", "        \"city_id\",\n", "        \"updated_at_ist\",\n", "    ]\n", "]\n", "final_result3.shape"]}, {"cell_type": "code", "execution_count": null, "id": "988dfd82-0a0d-40d8-925a-835ce7467d05", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"fdate_\", \"type\": \"timestamp(6)\", \"description\": \"date\"},\n", "    {\"name\": \"be_outlet_id\", \"type\": \"integer\", \"description\": \"be_outlet_id\"},\n", "    {\"name\": \"be_outlet_name\", \"type\": \"varchar\", \"description\": \"be_outlet_name\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"outlet_id\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "    {\n", "        \"name\": \"final_indent_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"final_indent_qty\",\n", "    },\n", "    {\n", "        \"name\": \"run_id\",\n", "        \"type\": \"BIGINT\",\n", "        \"description\": \"run_id\",\n", "    },\n", "    {\n", "        \"name\": \"vendor_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"vendor_id\",\n", "    },\n", "    {\n", "        \"name\": \"city_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"city_id\",\n", "    },\n", "    {\n", "        \"name\": \"updated_at_ist\",\n", "        \"type\": \"timestamp(6)\",\n", "        \"description\": \"updated timestamp\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "2897238f-ed4a-4bf1-b106-5651e0268d72", "metadata": {}, "outputs": [], "source": ["if check_duplicate == False:\n", "    kwargs = {\n", "        \"schema_name\": \"supply_etls\",\n", "        \"table_name\": \"fnv_dts_indent_auto_po\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"fdate_\", \"outlet_id\", \"item_id\"],\n", "        \"partition_key\": [\"fdate_\"],\n", "        \"incremental_key\": \"fdate_\",\n", "        \"force_upsert_without_increment_check\": False,\n", "        \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "        \"table_description\": \"Details of FnV DTS Stores Indent Qty\",\n", "    }\n", "\n", "    pb.to_trino(final_result3, **kwargs)\n", "\n", "    channel = \"bl-fnv-replenishment-dag-run-alerts\"\n", "    text_req = \"\\n <@U05CCTXLBU1> \\n FnV DTS Indent Auto PO is updated Today \"\n", "    pb.send_slack_message(\n", "        channel=channel,\n", "        text=text_req,\n", "    )\n", "else:\n", "    channel = \"bl-fnv-replenishment-dag-run-alerts\"\n", "    text_req = \"\\n <@U05CCTXLBU1> \\n There are duplicate rows in FnV DTS Indent Auto PO, Please correct then re-run\"\n", "    pb.send_slack_message(\n", "        channel=channel,\n", "        text=text_req,\n", "    )"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}