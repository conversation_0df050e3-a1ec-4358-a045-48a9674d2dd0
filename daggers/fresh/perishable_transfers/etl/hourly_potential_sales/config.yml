alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: hourly_potential_sales
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: fresh
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U05CCTXLBU1
path: fresh/perishable_transfers/etl/hourly_potential_sales
paused: false
pool: fresh_pool
project_name: perishable_transfers
schedule:
  end_date: '2025-09-20T00:00:00'
  interval: 30 0 * * *
  start_date: '2025-06-22T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
