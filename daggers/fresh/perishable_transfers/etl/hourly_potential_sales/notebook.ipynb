{"cells": [{"cell_type": "code", "execution_count": null, "id": "b713f854-72b4-4d33-b719-e99204c831c1", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib\n", "!pip install pandas openpyxl\n", "!pip install numpy==1.26.4\n", "import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "import datetime\n", "import requests\n", "\n", "from datetime import datetime\n", "from matplotlib import pyplot as plt\n", "\n", "from datetime import datetime\n", "\n", "# CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "\n", "# SQL Connection\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "80f125f8-787b-41fd-a88c-14f909663370", "metadata": {}, "outputs": [], "source": ["# item_ids_df = pb.from_sheets(\n", "#     sheetid=\"1ZWmOoK4rLFdUn7jVTiUG2RDX8ReA8ZzdlBdzecuZg6E\", sheetname=\"Sheet1\"\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "67d043cc-d551-4ce0-82f0-e97c20a729a1", "metadata": {}, "outputs": [], "source": ["# # item_ids_df = pd.read_csv('item_id_universe.csv')\n", "\n", "# item_ids_df[\"experiment_item_id\"] = item_ids_df[\"experiment_item_id\"].astype(int)\n", "# target = tuple(list(item_ids_df[\"experiment_item_id\"]))"]}, {"cell_type": "markdown", "id": "ab17025d-94e5-4a9e-9e8c-72fd2865d84a", "metadata": {}, "source": ["# Perishable"]}, {"cell_type": "markdown", "id": "1d99338f-8bb7-4fe5-ad51-e6ef019178c1", "metadata": {"tags": []}, "source": ["## Universe"]}, {"cell_type": "code", "execution_count": null, "id": "4fd19516-faf1-4cf4-b47a-a8a11b37a72a", "metadata": {}, "outputs": [], "source": ["sql = f\"\"\"\n", "\n", "with live_stores as (\n", "select\n", "    outlet_id,\n", "    count(distinct order_id) as orders\n", "from\n", "    dwh.fact_sales_order_details\n", "where\n", "    order_create_dt_ist >= current_date - interval '7' day\n", "group by 1\n", "having count(distinct order_id) > 10\n", "),\n", "\n", "\n", "-- Making the universe\n", "dates as(\n", "select date as date_ from dwh.dim_date where date >= current_date - interval '15' day and date < current_date group by 1\n", "),\n", "\n", "hours as (\n", "    -- select 0 as hour_ union all\n", "    -- select 1 as hour_ union all\n", "    -- select 2 as hour_ union all\n", "    -- select 3 as hour_ union all\n", "    -- select 4 as hour_ union all\n", "    -- select 5 as hour_ union all\n", "    -- select 6 as hour_ union all\n", "    -- select 7 as hour_ union all\n", "    -- select 8 as hour_ union all\n", "    -- select 9 as hour_ union all\n", "    -- select 10 as hour_ union all\n", "    -- select 11 as hour_ union all\n", "    -- select 12 as hour_ union all\n", "    -- select 13 as hour_ union all\n", "    -- select 14 as hour_ union all\n", "    -- select 15 as hour_ union all\n", "    -- select 16 as hour_ union all\n", "    select 17 as hour_ union all\n", "    select 18 as hour_ union all\n", "    select 19 as hour_ union all\n", "    select 20 as hour_ union all\n", "    select 21 as hour_ union all\n", "    select 22 as hour_ union all\n", "    select 23 as hour_\n", "),\n", "\n", "store_universe as (\n", "select\n", "    outlet_id,\n", "    store_name\n", "from\n", "    supply_etls.fresh_warehouse_store_details s\n", "where\n", "    date_ = current_date - interval '2' day\n", "    and be_outlet_id in (4353,4184,4240,5030,5376,4433,4553,4287,4552,4665,5537,4514,6153,4236,6056,6569)\n", "group by 1,2\n", "),\n", "\n", "universe as(\n", "select \n", "    d.date_,\n", "    h.hour_,\n", "    tm.tag_value as be_outlet_id,\n", "    om.outlet_name as be_outlet_name,\n", "    fm.frontend_merchant_id,\n", "    fm.frontend_merchant_name,\n", "    fm.pos_outlet_id,\n", "    pfma.facility_id,\n", "    pfma.item_id,\n", "    ic.name\n", "from\n", "    rpc.product_facility_master_assortment pfma\n", "inner join\n", "    rpc.item_category_details ic\n", "    on ic.item_id = pfma.item_id\n", " --   and l0_id = 1487\n", "    and ic.l2_id in (949,1094,1425,1093,138,950,63,1367,1369,1091)\n", "inner join\n", "        (\n", "        SELECT DISTINCT a.item_id\n", "            FROM rpc.product_product a\n", "            JOIN (\n", "                SELECT item_id, MAX(id) AS id\n", "                FROM rpc.product_product\n", "                WHERE approved = 1 AND active = 1\n", "                GROUP BY 1\n", "            ) b ON a.id = b.id AND a.item_id = b.item_id\n", "            WHERE a.active = 1 AND a.lake_active_record AND a.approved = 1 AND a.perishable = 1\n", "        ) ma ON ma.item_id = ic.item_id\n", "inner join\n", "    dwh.dim_merchant_outlet_facility_mapping fm\n", "    on fm.facility_id = pfma.facility_id\n", "    and fm.is_current = true \n", "    and fm.is_mapping_enabled = true \n", "    and fm.is_frontend_merchant_active = true \n", "    and fm.is_backend_merchant_active = true \n", "    and fm.is_pos_outlet_active = 1\n", "    and fm.pos_outlet_id in (select distinct outlet_id from live_stores)\n", "    and fm.pos_outlet_id in (select distinct outlet_id from store_universe)\n", "inner join   \n", "    rpc.item_outlet_tag_mapping tm\n", "    on pfma.item_id = tm.item_id \n", "    and fm.pos_outlet_id = tm.outlet_id\n", "    and tm.tag_type_id = 8 \n", "    and tm.active = 1\n", "inner join\n", "    lake_po.physical_facility_outlet_mapping om\n", "    on cast(tm.tag_value as int) =  om.outlet_id    \n", "cross join\n", "    dates d\n", "cross join\n", "    hours h\n", "where\n", "    pfma.master_assortment_substate_id =  1\n", ")\n", "\n", "select\n", "    *\n", "from\n", "    universe u\n", "\n", "\"\"\"\n", "base_universe = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "adcaf7d5-1b0f-4bd7-8644-1014b84363e7", "metadata": {}, "outputs": [], "source": ["base_universe.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f61db0cc-4db3-4b83-aaca-06303f28ce04", "metadata": {}, "outputs": [], "source": ["base_universe.shape"]}, {"cell_type": "code", "execution_count": null, "id": "853624f0-b63f-433a-96e7-8dd26b2b39e7", "metadata": {}, "outputs": [], "source": ["unique_pos_outlet_id_values = base_universe[\"pos_outlet_id\"].unique()\n", "outlet_id = tuple(unique_pos_outlet_id_values)\n", "\n", "unique_pos_item_id_values = base_universe[\"item_id\"].unique()\n", "item_id = tuple(unique_pos_item_id_values)"]}, {"cell_type": "markdown", "id": "64d73e6f-62b0-4416-800a-4108bd51ab2a", "metadata": {}, "source": ["## Sales"]}, {"cell_type": "code", "execution_count": null, "id": "c1b78df5-1133-4be6-841e-c770a148b924", "metadata": {}, "outputs": [], "source": ["sql = f\"\"\"\n", "\n", "with sales as (\n", "SELECT\n", "    date(o.order_create_ts_ist) as date_,\n", "    extract(hour from order_create_ts_ist) as hour_,\n", "    o.frontend_merchant_id,\n", "    fm.pos_outlet_id,\n", "    ip.item_id,\n", "    sum(o.procured_quantity*ip.multiplier) as qty_sold\n", "FROM\n", "    dwh.fact_sales_order_item_details o\n", "INNER JOIN\n", "    dwh.dim_product dp \n", "    on dp.product_id = o.product_id\n", "    --and dp.l0_category_id = 1487\n", "    and dp.is_current\n", "inner join\n", "    dwh.dim_merchant_outlet_facility_mapping as fm\n", "    on o.frontend_merchant_id = fm.frontend_merchant_id\n", "    and fm.is_current = true \n", "    and fm.is_mapping_enabled = true \n", "    and fm.is_frontend_merchant_active = true \n", "    and fm.is_backend_merchant_active = true \n", "    and fm.is_pos_outlet_active = 1\n", "inner join\n", "    dwh.dim_item_product_offer_mapping ip\n", "    on ip.product_id = o.product_id\n", "    and ip.is_current\n", "inner join\n", "    rpc.item_category_details rp\n", "    on rp.item_id = ip.item_id\n", "  --  and l0_category_id = 1487 \n", " WHERE\n", "    o.order_current_status = 'DELIVERED'\n", "    and o.order_create_dt_ist >= current_date - interval '15' day\n", "    and o.order_create_dt_ist < current_date\n", "    and o.is_internal_order = False\n", "    and rp.item_id in {item_id}\n", "    and fm.pos_outlet_id in {outlet_id}\n", "GROUP BY 1,2,3,4,5\n", ")\n", "\n", "select * from sales where hour_ between 17 and 23\n", "    \n", "\"\"\"\n", "sales_data = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "076b1606-1e1b-4528-83f2-ee510e8bc84f", "metadata": {}, "outputs": [], "source": ["sales_data.head()"]}, {"cell_type": "markdown", "id": "6741e59d-078c-4677-bd77-10f4c883f254", "metadata": {}, "source": ["## Hourly Inventory Snapshot"]}, {"cell_type": "code", "execution_count": null, "id": "376e364a-f6fc-450a-8724-bb0cef93c958", "metadata": {}, "outputs": [], "source": ["sql = f\"\"\"\n", "with perishable_skus AS (\n", "        SELECT DISTINCT ma.item_id AS item_id, icd.name AS item_name, l1\n", "        FROM rpc.product_product ma\n", "        JOIN (\n", "            SELECT item_id, MAX(id) AS id\n", "            FROM rpc.product_product\n", "            WHERE active = 1 and approved = 1\n", "            GROUP BY 1\n", "        ) b ON ma.item_id = b.item_id and ma.id = b.id\n", "        JOIN rpc.item_category_details icd ON icd.item_id = ma.item_id\n", "        WHERE icd.item_id in {item_id}\n", "),\n", "\n", "base_pre AS (\n", "    SELECT * FROM supply_etls.hourly_inventory_snapshots\n", "    WHERE date_ist BETWEEN current_date - interval '15' day AND current_date - interval '1' day --- change date here\n", "    and item_id IN (select distinct item_id from perishable_skus)\n", "    and substate_id = 1\n", "),\n", "\n", "base AS (\n", "    SELECT * FROM base_pre\n", "    WHERE outlet_id IN {outlet_id}\n", "),\n", "\n", "avail_pre_pre as (\n", "        SELECT DATE(date_ist) AS date_, EXTRACT(hour FROM updated_at_ist) AS hour_, facility_id, outlet_id, item_id, \n", "        CASE \n", "            WHEN current_inventory > 0 THEN 1 \n", "            ELSE 0  \n", "        END is_available,\n", "        current_inventory\n", "        FROM base\n", "        WHERE item_id IN (select distinct item_id from perishable_skus)\n", "),\n", "\n", "avail_pre as ( \n", "    SELECT date_, hour_, facility_id, outlet_id, item_id, MAX(is_available) AS is_available, MAX(current_inventory) as current_inventory\n", "    from avail_pre_pre \n", "    where hour_ BETWEEN 17 AND 23\n", "    GROUP BY 1,2,3,4,5\n", ")\n", "\n", "select * from avail_pre\n", "    \n", "\"\"\"\n", "availability_data = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "0e4d5e24-e8b4-4225-a16d-ff64d9df59f9", "metadata": {}, "outputs": [], "source": ["availability_data.head()"]}, {"cell_type": "markdown", "id": "9f18160d-5840-4952-a5fa-abb90cc15aff", "metadata": {}, "source": ["## Merging Universe x Sales x Availability "]}, {"cell_type": "code", "execution_count": null, "id": "5719ca5d-8d18-4cbe-89ed-95289388265d", "metadata": {}, "outputs": [], "source": ["merged_universe = base_universe.merge(\n", "    availability_data,\n", "    left_on=[\"date_\", \"hour_\", \"item_id\", \"pos_outlet_id\"],\n", "    right_on=[\"date_\", \"hour_\", \"item_id\", \"outlet_id\"],\n", "    how=\"left\",\n", ").merge(\n", "    sales_data,\n", "    left_on=[\"date_\", \"hour_\", \"item_id\", \"pos_outlet_id\"],\n", "    right_on=[\"date_\", \"hour_\", \"item_id\", \"pos_outlet_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fd62906a-8716-4056-b8e2-a71873b4f6e5", "metadata": {}, "outputs": [], "source": ["merged_universe.head()"]}, {"cell_type": "code", "execution_count": null, "id": "fdd689ce-b9ed-4e4a-b0d2-de7c91e2fc30", "metadata": {}, "outputs": [], "source": ["# Check for the joins\n", "len(base_universe) == len(merged_universe)"]}, {"cell_type": "code", "execution_count": null, "id": "9d9e0f21-2734-4113-86cb-ee7fe69a918a", "metadata": {}, "outputs": [], "source": ["# When there is no sales in particular hour it will be NaN value but it might be available in that case filling with 0 sales\n", "merged_universe.loc[\n", "    (merged_universe[\"is_available\"] == 1) & (merged_universe[\"qty_sold\"].isna()),\n", "    \"qty_sold\",\n", "] = 0"]}, {"cell_type": "code", "execution_count": null, "id": "7d5054c0-f5f0-4de4-a971-4afaf62f4056", "metadata": {}, "outputs": [], "source": ["# Current Date\n", "merged_universe[\"date_\"] = pd.to_datetime(merged_universe[\"date_\"])\n", "current_date = datetime.today()"]}, {"cell_type": "code", "execution_count": null, "id": "ebdc590f-7cc9-4cdb-a524-95271b7a0a6c", "metadata": {}, "outputs": [], "source": ["# Giving weights based on current date and day, if we are calculating for the current date then T-7 has the highest weight then subsquent decrement order\n", "\n", "\n", "def calculate_weight(date):\n", "    days_difference = (current_date - date).days\n", "    if days_difference == 7:\n", "        return 15\n", "    elif days_difference == 1:\n", "        return 14\n", "    elif days_difference == 2:\n", "        return 13\n", "    elif days_difference == 3:\n", "        return 12\n", "    elif days_difference == 4:\n", "        return 11\n", "    elif days_difference == 5:\n", "        return 10\n", "    elif days_difference == 6:\n", "        return 9\n", "    elif days_difference == 8:\n", "        return 8\n", "    elif days_difference == 9:\n", "        return 7\n", "    elif days_difference == 10:\n", "        return 6\n", "    elif days_difference == 11:\n", "        return 5\n", "    elif days_difference == 12:\n", "        return 4\n", "    elif days_difference == 13:\n", "        return 3\n", "    elif days_difference == 14:\n", "        return 2\n", "    elif days_difference == 15:\n", "        return 1\n", "    else:\n", "        return 0\n", "\n", "\n", "merged_universe[\"weight\"] = merged_universe[\"date_\"].apply(calculate_weight)"]}, {"cell_type": "code", "execution_count": null, "id": "7e97c57d-f5cb-411c-b237-f552006b6f7b", "metadata": {}, "outputs": [], "source": ["# Calculating potential sales in a Hour\n", "merged_universe[\"final_calculations\"] = merged_universe[\"weight\"] * merged_universe[\"qty_sold\"]\n", "\n", "grouped_df = (\n", "    merged_universe[merged_universe[\"qty_sold\"].notnull()]\n", "    .groupby([\"hour_\", \"frontend_merchant_name\", \"pos_outlet_id\", \"facility_id_x\", \"item_id\"])\n", "    .agg({\"final_calculations\": \"sum\", \"weight\": \"sum\", \"qty_sold\": \"size\"})\n", "    .reset_index()\n", ")\n", "grouped_df.rename(columns={\"qty_sold\": \"unique_data\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "e003ba5a-fe99-45e7-8a74-eb4797db4f27", "metadata": {}, "outputs": [], "source": ["# grouped_df.to_csv('b.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "d114add2-2315-41d7-80d5-fb35f4e81e41", "metadata": {}, "outputs": [], "source": ["# Lets keep at float values, it might be better\n", "grouped_df[\"hourly_cpd\"] = grouped_df[\"final_calculations\"] / grouped_df[\"weight\"]\n", "grouped_df[\"hourly_cpd\"] = grouped_df[\"hourly_cpd\"].map(\"{:.2f}\".format)\n", "\n", "# grouped_df['hourly_cpd'] = np.ceil(grouped_df['hourly_cpd'])\n", "# grouped_df['hourly_cpd'] = grouped_df['hourly_cpd'].astype(int)\n", "# grouped_df"]}, {"cell_type": "code", "execution_count": null, "id": "ae007d34-207e-4d5a-9f9f-d16678568965", "metadata": {}, "outputs": [], "source": ["# Taking those values where we have minimum three days of data on particular hour\n", "final_df = grouped_df.copy()\n", "# grouped_df[grouped_df[\"unique_data\"] >= 3]"]}, {"cell_type": "code", "execution_count": null, "id": "ac04064f-d2b3-4321-816e-db33cb63340f", "metadata": {}, "outputs": [], "source": ["target_universe = merged_universe[\n", "    [\"hour_\", \"frontend_merchant_name\", \"pos_outlet_id\", \"facility_id_x\", \"item_id\"]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "331dac96-cd6a-4f30-bb4a-97c4d7c0ee4a", "metadata": {}, "outputs": [], "source": ["target_universe"]}, {"cell_type": "code", "execution_count": null, "id": "56a851ba-d417-4ded-9407-cf60217149c2", "metadata": {}, "outputs": [], "source": ["#  In df1 there are multiple rows where we do not get data from the previous values\n", "\n", "df1 = target_universe.merge(\n", "    final_df,\n", "    left_on=[\n", "        \"hour_\",\n", "        \"frontend_merchant_name\",\n", "        \"pos_outlet_id\",\n", "        \"facility_id_x\",\n", "        \"item_id\",\n", "    ],\n", "    right_on=[\n", "        \"hour_\",\n", "        \"frontend_merchant_name\",\n", "        \"pos_outlet_id\",\n", "        \"facility_id_x\",\n", "        \"item_id\",\n", "    ],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6dd00abd-8403-4964-8215-e0a505abe4c8", "metadata": {}, "outputs": [], "source": ["df1"]}, {"cell_type": "code", "execution_count": null, "id": "f32c4510-44af-41a9-b4e4-0a7505530041", "metadata": {}, "outputs": [], "source": ["new_column_names = {\n", "    \"pos_outlet_id\": \"outlet_id\",\n", "    \"facility_id_x\": \"facility_id\",\n", "}\n", "\n", "df1.rename(columns=new_column_names, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "9cf334ee-507b-47de-a602-a4fe8ca88ad5", "metadata": {}, "outputs": [], "source": ["# Where we do not get the data from the last 15 days we are going ahead with different approach\n", "filtered_df = df1[df1[\"hourly_cpd\"].isna()]"]}, {"cell_type": "code", "execution_count": null, "id": "dd52c2cd-8c38-410f-9e2e-9a2e98a1e1b5", "metadata": {}, "outputs": [], "source": ["filtered_df"]}, {"cell_type": "code", "execution_count": null, "id": "9ec4a631-a631-45a5-9612-ecb7aba50b86", "metadata": {}, "outputs": [], "source": ["unique_no_data_facility_id_values = filtered_df[\"facility_id\"].unique()\n", "facility_id_no_data = tuple(unique_no_data_facility_id_values)\n", "\n", "unique_no_data_item_id_values = filtered_df[\"item_id\"].unique()\n", "item_id_no_data = tuple(unique_no_data_item_id_values)"]}, {"cell_type": "code", "execution_count": null, "id": "e1175547-ac64-405c-9f74-3961b2b1e234", "metadata": {}, "outputs": [], "source": ["sql = f\"\"\"\n", "\n", "with base_forecast as( \n", "SELECT\n", "    date(current_replenishment_ts_ist) date_,\n", "    facility_id,\n", "    item_id,\n", "    name,\n", "    max_qty,\n", "    updated_at_ist,\n", "    ROW_NUMBER() OVER (PARTITION BY facility_id, item_id, date(current_replenishment_ts_ist) ORDER BY updated_at_ist DESC) AS RowNum\n", "FROM\n", "    ds_etls.demand_forecast_item_ordering_min_max_quantity_log\n", "WHERE   \n", "    current_replenishment_ts_ist = current_date \n", "),\n", "\n", "final_forecast as (\n", "select\n", "    bf.date_,\n", "    bf.facility_id,\n", "    bf.item_id,\n", "    bf.name,\n", "    bf.max_qty as forecast_qty\n", "from\n", "    base_forecast bf\n", "where\n", "    RowNum = 1\n", "    and facility_id in {facility_id_no_data}\n", "    and item_id in (10002229)\n", "    -- Ideally item id should pass here but right now we are doing only for perishable and this table have data for fnv\n", "),\n", "\n", "hours as (\n", "    select 0 as hour_ union all\n", "    select 1 as hour_ union all\n", "    select 2 as hour_ union all\n", "    select 3 as hour_ union all\n", "    select 4 as hour_ union all\n", "    select 5 as hour_ union all\n", "    select 6 as hour_ union all\n", "    select 7 as hour_ union all\n", "    select 8 as hour_ union all\n", "    select 9 as hour_ union all\n", "    select 10 as hour_ union all\n", "    select 11 as hour_ union all\n", "    select 12 as hour_ union all\n", "    select 13 as hour_ union all\n", "    select 14 as hour_ union all\n", "    select 15 as hour_ union all\n", "    select 16 as hour_ union all\n", "    select 17 as hour_ union all\n", "    select 18 as hour_ union all\n", "    select 19 as hour_ union all\n", "    select 20 as hour_ union all\n", "    select 21 as hour_ union all\n", "    select 22 as hour_ union all\n", "    select 23 as hour_\n", "),\n", "\n", "hourly_sales as (\n", "SELECT\n", "    extract(hour from order_create_ts_ist) as hour_,\n", "    sum(o.procured_quantity) as qty_sold\n", "FROM\n", "    dwh.fact_sales_order_item_details o\n", "INNER JOIN\n", "    dwh.dim_product dp \n", "    on dp.product_id = o.product_id\n", "    and dp.l0_category_id = 1487\n", "    and dp.is_current\n", "    and dp.product_type in ('<PERSON>tat<PERSON>','Onion')\n", "WHERE\n", "    o.order_current_status = 'DELIVERED'\n", "    and o.order_create_dt_ist >= current_date - interval '15' day\n", "    and o.order_create_dt_ist < current_date\n", "    and o.is_internal_order = False\n", "GROUP BY 1\n", "),\n", "\n", "total_sales as (\n", "select\n", "    sum(qty_sold) total_qty_sold\n", "from\n", "    hourly_sales\n", "),\n", "\n", "hour_weights as (\n", "select\n", "    hs.hour_,\n", "    hs.qty_sold,\n", "    ts.total_qty_sold,\n", "    (hs.qty_sold*1.00) / ts.total_qty_sold as weights \n", "    \n", "from\n", "    hourly_sales hs\n", "cross join\n", "    total_sales ts\n", "),\n", "\n", "hourly_forecast as (\n", "select\n", "    ff.*,\n", "    h.hour_\n", "from\n", "    final_forecast ff\n", "cross join\n", "    hours h\n", "),\n", "\n", "final_hourly_forecast as (\n", "select\n", "    a.*,\n", "    b.weights\n", "from\n", "    hourly_forecast a\n", "left join\n", "    hour_weights b\n", "    on a.hour_ = b.hour_\n", ")\n", "\n", "select \n", "    f.*, \n", "    coalesce(forecast_qty*weights,0) as hourly_forecast \n", "from \n", "    final_hourly_forecast f\n", "\n", "    \n", "\"\"\"\n", "forecast_split_hr_data = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "7fb94baa-54ea-4691-8f5d-ef0879fd030a", "metadata": {}, "outputs": [], "source": ["forecast_split_hr_data"]}, {"cell_type": "code", "execution_count": null, "id": "8cb1635d-fba8-4a59-8376-b0baed1d7e69", "metadata": {}, "outputs": [], "source": ["# forecast_split_hr_data.to_csv('a.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "dd746f98-d08f-4075-8d16-1d9b07c739f8", "metadata": {}, "outputs": [], "source": ["df2 = df1.merge(\n", "    forecast_split_hr_data,\n", "    left_on=[\"hour_\", \"facility_id\", \"item_id\"],\n", "    right_on=[\"hour_\", \"facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d6bbb880-1f5c-4839-b44c-8a4e0b66c7c8", "metadata": {}, "outputs": [], "source": ["# df2[df2[\"hourly_cpd\"].isna()]\n", "df2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "976406d3-888b-4d87-8ab5-9a1eff1dea45", "metadata": {}, "outputs": [], "source": ["df2[\"hourly_cpd\"] = df2[\"hourly_cpd\"].fillna(df2[\"hourly_forecast\"])"]}, {"cell_type": "code", "execution_count": null, "id": "f803799a-bec4-46c0-b1ec-4bdb2d8ee9bd", "metadata": {}, "outputs": [], "source": ["# df2.to_csv('a.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "8dbaf063-8782-40f9-92bc-7911690678b6", "metadata": {}, "outputs": [], "source": ["today_ = datetime.today().date()\n", "df2[\"date_of_consumption\"] = today_"]}, {"cell_type": "code", "execution_count": null, "id": "b2da53b3-5252-4202-aa89-6f35563b9bc8", "metadata": {}, "outputs": [], "source": ["merge_df = df2[\n", "    [\n", "        \"date_of_consumption\",\n", "        \"hour_\",\n", "        \"frontend_merchant_name\",\n", "        \"outlet_id\",\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"unique_data\",\n", "        \"hourly_cpd\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "a9202240-99f6-4e00-9312-3bc383f87656", "metadata": {}, "outputs": [], "source": ["merge_df = merge_df.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "f529e1bd-207d-4e5d-b25c-3777f734124f", "metadata": {}, "outputs": [], "source": ["merge_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "42ba271b-773d-4ced-834d-95a1e966eb26", "metadata": {}, "outputs": [], "source": ["merge_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "227a12ed-f0bc-47b6-900e-9cd1e507005b", "metadata": {}, "outputs": [], "source": ["import datetime\n", "\n", "current_date_str = datetime.datetime.now().strftime(\"%Y-%m-%d\")\n", "\n", "\n", "# Changing dtypes\n", "merge_df[\"date_of_consumption\"] = pd.to_datetime(merge_df[\"date_of_consumption\"])\n", "merge_df[\"hour_\"] = merge_df[\"hour_\"].astype(int)\n", "merge_df[\"outlet_id\"] = merge_df[\"outlet_id\"].astype(int)\n", "merge_df[\"facility_id\"] = merge_df[\"facility_id\"].astype(int)\n", "merge_df[\"item_id\"] = merge_df[\"item_id\"].astype(int)\n", "merge_df[\"unique_data\"] = merge_df[\"unique_data\"].astype(int)\n", "merge_df[\"hourly_cpd\"] = merge_df[\"hourly_cpd\"].astype(float)\n", "\n", "column_dtypes = [\n", "    {\"name\": \"date_of_consumption\", \"type\": \"timestamp(6)\", \"description\": \"date\"},\n", "    {\"name\": \"hour_\", \"type\": \"integer\", \"description\": \"hour_\"},\n", "    {\n", "        \"name\": \"frontend_merchant_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"frontend_merchant_name\",\n", "    },\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"outlet_id\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility_id\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "    {\n", "        \"name\": \"unique_data\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"calculated_data_for_how_many_days\",\n", "    },\n", "    {\"name\": \"hourly_cpd\", \"type\": \"real\", \"description\": \"hourly_cpd\"},\n", "]\n", "\n", "# Pushing Data to Table\n", "if len(target_universe) == len(merge_df):\n", "    kwargs = {\n", "        \"schema_name\": \"supply_etls\",\n", "        \"table_name\": \"fnv_hourly_potential_sales\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\n", "            \"date_of_consumption\",\n", "            \"hour_\",\n", "            \"outlet_id\",\n", "            \"facility_id\",\n", "            \"item_id\",\n", "        ],\n", "        \"partition_key\": [\"date_of_consumption\"],\n", "        \"incremental_key\": \"date_of_consumption\",\n", "        \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "        \"table_description\": \"Expected Sales of FnV by EOD\",\n", "    }\n", "\n", "    pb.to_trino(merge_df, **kwargs)\n", "\n", "    channel = \"table-updates-tanishq\"\n", "    text_req = (\n", "        \"\\n <@U05CCTXLBU1> \\n Multiple ARS Run Hourly Potential Sales Updated for Perishable \"\n", "        + current_date_str\n", "    )\n", "    pb.send_slack_message(\n", "        channel=channel,\n", "        text=text_req,\n", "    )\n", "else:\n", "    channel = \"table-updates-tanishq\"\n", "    text_req = (\n", "        \"\\n <@U05CCTXLBU1> \\n Multiple ARS Run Repeated Rows Table is not Updated \"\n", "        + current_date_str\n", "    )\n", "    pb.send_slack_message(\n", "        channel=channel,\n", "        text=text_req,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "ba82ea88-afc4-4f55-9bdb-236a029e21dd", "metadata": {}, "outputs": [], "source": ["# Table Updated"]}, {"cell_type": "markdown", "id": "1b1f903b-3acf-4034-b215-86a69f743ab9", "metadata": {"tags": []}, "source": ["# FnV"]}, {"cell_type": "markdown", "id": "6452a3f3-bb4e-469c-ada0-1946bdf3ec9e", "metadata": {"tags": []}, "source": ["## Universe"]}, {"cell_type": "code", "execution_count": null, "id": "b3dbc6ce-b540-43a9-9217-c1a061c30db1", "metadata": {}, "outputs": [], "source": ["sql = f\"\"\"\n", "\n", "with live_stores as (\n", "select\n", "    outlet_id,\n", "    count(distinct order_id) as orders\n", "from\n", "    dwh.fact_sales_order_details\n", "where\n", "    order_create_dt_ist >= current_date - interval '7' day\n", "group by 1\n", "having count(distinct order_id) > 10\n", "),\n", "\n", "\n", "-- Making the universe\n", "dates as(\n", "select date as date_ from dwh.dim_date where date >= current_date - interval '15' day and date < current_date group by 1\n", "),\n", "\n", "hours as (\n", "    -- select 0 as hour_ union all\n", "    -- select 1 as hour_ union all\n", "    -- select 2 as hour_ union all\n", "    -- select 3 as hour_ union all\n", "    -- select 4 as hour_ union all\n", "    -- select 5 as hour_ union all\n", "    -- select 6 as hour_ union all\n", "    -- select 7 as hour_ union all\n", "    -- select 8 as hour_ union all\n", "    -- select 9 as hour_ union all\n", "    -- select 10 as hour_ union all\n", "    -- select 11 as hour_ union all\n", "    -- select 12 as hour_ union all\n", "    -- select 13 as hour_ union all\n", "    -- select 14 as hour_ union all\n", "    -- select 15 as hour_ union all\n", "    -- select 16 as hour_ union all\n", "    select 17 as hour_ union all\n", "    select 18 as hour_ union all\n", "    select 19 as hour_ union all\n", "    select 20 as hour_ union all\n", "    select 21 as hour_ union all\n", "    select 22 as hour_ union all\n", "    select 23 as hour_\n", "),\n", "\n", "store_universe as (\n", "select\n", "    outlet_id,\n", "    store_name\n", "from\n", "    supply_etls.fresh_warehouse_store_details s\n", "where\n", "    date_ = current_date - interval '2' day\n", "    and be_outlet_id in (4353,4184,4240,5030,5376,4433,4553,4287,4552,4665,5537,4514,6153,4236,6056,6569)\n", "group by 1,2\n", "),\n", "\n", "universe as(\n", "select \n", "    d.date_,\n", "    h.hour_,\n", "    tm.tag_value as be_outlet_id,\n", "    om.outlet_name as be_outlet_name,\n", "    fm.frontend_merchant_id,\n", "    fm.frontend_merchant_name,\n", "    fm.pos_outlet_id,\n", "    pfma.facility_id,\n", "    pfma.item_id,\n", "    ic.name\n", "from\n", "    rpc.product_facility_master_assortment pfma\n", "inner join\n", "    rpc.item_category_details ic\n", "    on ic.item_id = pfma.item_id\n", "    and l0_id = 1487\n", "    and (\n", "        ic.l2_id in (395)\n", "        or ic.product_type_id in (1759,8537,5589,10492,6866,7831,8729,7673,3419,11870,9865,6714,11165,2484,7390,3253,3865,5954,11095,7917,115,40,10468,5989,5835,2685,6867,3559,1066,10335,13893,2811,9137,11920,8798,1632,10233,1071,12176,9708,1578,4898,10963,5870,2762,3921)\n", "        )\n", "inner join\n", "    dwh.dim_merchant_outlet_facility_mapping fm\n", "    on fm.facility_id = pfma.facility_id\n", "    and fm.is_current = true \n", "    and fm.is_mapping_enabled = true \n", "    and fm.is_frontend_merchant_active = true \n", "    and fm.is_backend_merchant_active = true \n", "    and fm.is_pos_outlet_active = 1\n", "    and fm.pos_outlet_id in (select distinct outlet_id from live_stores)\n", "    and fm.pos_outlet_id in (select distinct outlet_id from store_universe)\n", "inner join   \n", "    rpc.item_outlet_tag_mapping tm\n", "    on pfma.item_id = tm.item_id \n", "    and fm.pos_outlet_id = tm.outlet_id\n", "    and tm.tag_type_id = 8 \n", "    and tm.active = 1\n", "inner join\n", "    lake_po.physical_facility_outlet_mapping om\n", "    on cast(tm.tag_value as int) =  om.outlet_id    \n", "cross join\n", "    dates d\n", "cross join\n", "    hours h\n", "where\n", "    pfma.master_assortment_substate_id =  1\n", ")\n", "\n", "select\n", "    *\n", "from\n", "    universe u\n", "\n", "\"\"\"\n", "base_universe = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "43c23294-56b5-4ad9-b27b-872c2dfc815b", "metadata": {}, "outputs": [], "source": ["base_universe.head()"]}, {"cell_type": "code", "execution_count": null, "id": "65488e2a-6871-4f02-9851-8f1a745542f9", "metadata": {}, "outputs": [], "source": ["base_universe.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c2bba8b0-4473-4a4f-a387-beb78693f9ba", "metadata": {}, "outputs": [], "source": ["unique_pos_outlet_id_values = base_universe[\"pos_outlet_id\"].unique()\n", "outlet_id = tuple(unique_pos_outlet_id_values)\n", "\n", "unique_pos_item_id_values = base_universe[\"item_id\"].unique()\n", "item_id = tuple(unique_pos_item_id_values)"]}, {"cell_type": "markdown", "id": "73b9b5c3-d3c3-4763-8e52-3e78dedf598f", "metadata": {}, "source": ["## Sales"]}, {"cell_type": "code", "execution_count": null, "id": "fd96ab74-3380-49d2-8f0e-41a530ca8dda", "metadata": {}, "outputs": [], "source": ["sql = f\"\"\"\n", "\n", "with sales as (\n", "SELECT\n", "    date(o.order_create_ts_ist) as date_,\n", "    extract(hour from order_create_ts_ist) as hour_,\n", "    o.frontend_merchant_id,\n", "    fm.pos_outlet_id,\n", "    ip.item_id,\n", "    sum(o.procured_quantity*ip.multiplier) as qty_sold\n", "FROM\n", "    dwh.fact_sales_order_item_details o\n", "INNER JOIN\n", "    dwh.dim_product dp \n", "    on dp.product_id = o.product_id\n", "    --and dp.l0_category_id = 1487\n", "    and dp.is_current\n", "inner join\n", "    dwh.dim_merchant_outlet_facility_mapping as fm\n", "    on o.frontend_merchant_id = fm.frontend_merchant_id\n", "    and fm.is_current = true \n", "    and fm.is_mapping_enabled = true \n", "    and fm.is_frontend_merchant_active = true \n", "    and fm.is_backend_merchant_active = true \n", "    and fm.is_pos_outlet_active = 1\n", "inner join\n", "    dwh.dim_item_product_offer_mapping ip\n", "    on ip.product_id = o.product_id\n", "    and ip.is_current\n", "inner join\n", "    rpc.item_category_details rp\n", "    on rp.item_id = ip.item_id\n", "  --  and l0_category_id = 1487 \n", " WHERE\n", "    o.order_current_status = 'DELIVERED'\n", "    and o.order_create_dt_ist >= current_date - interval '15' day\n", "    and o.order_create_dt_ist < current_date\n", "    and o.is_internal_order = False\n", "    and rp.item_id in {item_id}\n", "    and fm.pos_outlet_id in {outlet_id}\n", "GROUP BY 1,2,3,4,5\n", ")\n", "\n", "select * from sales where hour_ between 17 and 23\n", "    \n", "\"\"\"\n", "sales_data = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "39dcc30f-bfed-4ba3-ada9-0ae40747ac10", "metadata": {}, "outputs": [], "source": ["sales_data.head()"]}, {"cell_type": "markdown", "id": "b60d87ed-14ea-4ec1-8fb5-d2ff51e79b84", "metadata": {}, "source": ["## Hourly Inventory Snapshot"]}, {"cell_type": "code", "execution_count": null, "id": "281fdfdc-797f-43c4-8064-b569eee849b2", "metadata": {}, "outputs": [], "source": ["sql = f\"\"\"\n", "with perishable_skus AS (\n", "        SELECT DISTINCT ma.item_id AS item_id, icd.name AS item_name, l1\n", "        FROM rpc.product_product ma\n", "        JOIN (\n", "            SELECT item_id, MAX(id) AS id\n", "            FROM rpc.product_product\n", "            WHERE active = 1 and approved = 1\n", "            GROUP BY 1\n", "        ) b ON ma.item_id = b.item_id and ma.id = b.id\n", "        JOIN rpc.item_category_details icd ON icd.item_id = ma.item_id\n", "        WHERE icd.item_id in {item_id}\n", "),\n", "\n", "base_pre AS (\n", "    SELECT * FROM supply_etls.hourly_inventory_snapshots\n", "    WHERE date_ist BETWEEN current_date - interval '15' day AND current_date - interval '1' day --- change date here\n", "    and item_id IN (select distinct item_id from perishable_skus)\n", "    and substate_id = 1\n", "),\n", "\n", "base AS (\n", "    SELECT * FROM base_pre\n", "    WHERE outlet_id IN {outlet_id}\n", "),\n", "\n", "avail_pre_pre as (\n", "        SELECT DATE(date_ist) AS date_, EXTRACT(hour FROM updated_at_ist) AS hour_, facility_id, outlet_id, item_id, \n", "        CASE \n", "            WHEN current_inventory > 0 THEN 1 \n", "            ELSE 0  \n", "        END is_available,\n", "        current_inventory\n", "        FROM base\n", "        WHERE item_id IN (select distinct item_id from perishable_skus)\n", "),\n", "\n", "avail_pre as ( \n", "    SELECT date_, hour_, facility_id, outlet_id, item_id, MAX(is_available) AS is_available, MAX(current_inventory) as current_inventory\n", "    from avail_pre_pre \n", "    where hour_ BETWEEN 17 AND 23\n", "    GROUP BY 1,2,3,4,5\n", ")\n", "\n", "select * from avail_pre\n", "    \n", "\"\"\"\n", "availability_data = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "1281d067-1837-46e9-922b-89e6d386cefb", "metadata": {}, "outputs": [], "source": ["availability_data.head()"]}, {"cell_type": "markdown", "id": "cc46112b-c33c-4306-8830-9bfa44267647", "metadata": {}, "source": ["## Merging Universe x Sales x Availability "]}, {"cell_type": "code", "execution_count": null, "id": "41ea7da3-1a7b-439e-b54b-f00cc5af63de", "metadata": {}, "outputs": [], "source": ["merged_universe = base_universe.merge(\n", "    availability_data,\n", "    left_on=[\"date_\", \"hour_\", \"item_id\", \"pos_outlet_id\"],\n", "    right_on=[\"date_\", \"hour_\", \"item_id\", \"outlet_id\"],\n", "    how=\"left\",\n", ").merge(\n", "    sales_data,\n", "    left_on=[\"date_\", \"hour_\", \"item_id\", \"pos_outlet_id\"],\n", "    right_on=[\"date_\", \"hour_\", \"item_id\", \"pos_outlet_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d7b13a76-e0af-4532-b711-2da846597f40", "metadata": {}, "outputs": [], "source": ["merged_universe.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a88b258d-cc4b-467b-b2d7-b2bb73f23be6", "metadata": {}, "outputs": [], "source": ["# Check for the joins\n", "len(base_universe) == len(merged_universe)"]}, {"cell_type": "code", "execution_count": null, "id": "ce434f08-82bc-4bc0-bc16-e931aaaf0686", "metadata": {}, "outputs": [], "source": ["# When there is no sales in particular hour it will be NaN value but it might be available in that case filling with 0 sales\n", "merged_universe.loc[\n", "    (merged_universe[\"is_available\"] == 1) & (merged_universe[\"qty_sold\"].isna()),\n", "    \"qty_sold\",\n", "] = 0"]}, {"cell_type": "code", "execution_count": null, "id": "1d815d88-ae2b-4140-8f87-8e8c749956d6", "metadata": {}, "outputs": [], "source": ["# Current Date\n", "from datetime import datetime\n", "\n", "merged_universe[\"date_\"] = pd.to_datetime(merged_universe[\"date_\"])\n", "current_date = datetime.today()"]}, {"cell_type": "code", "execution_count": null, "id": "3759b7b6-7967-408f-b584-bd1e5d5abbd7", "metadata": {}, "outputs": [], "source": ["# Giving weights based on current date and day, if we are calculating for the current date then T-7 has the highest weight then subsquent decrement order\n", "\n", "\n", "def calculate_weight(date):\n", "    days_difference = (current_date - date).days\n", "    if days_difference == 7:\n", "        return 15\n", "    elif days_difference == 1:\n", "        return 14\n", "    elif days_difference == 2:\n", "        return 13\n", "    elif days_difference == 3:\n", "        return 12\n", "    elif days_difference == 4:\n", "        return 11\n", "    elif days_difference == 5:\n", "        return 10\n", "    elif days_difference == 6:\n", "        return 9\n", "    elif days_difference == 8:\n", "        return 8\n", "    elif days_difference == 9:\n", "        return 7\n", "    elif days_difference == 10:\n", "        return 6\n", "    elif days_difference == 11:\n", "        return 5\n", "    elif days_difference == 12:\n", "        return 4\n", "    elif days_difference == 13:\n", "        return 3\n", "    elif days_difference == 14:\n", "        return 2\n", "    elif days_difference == 15:\n", "        return 1\n", "    else:\n", "        return 0\n", "\n", "\n", "merged_universe[\"weight\"] = merged_universe[\"date_\"].apply(calculate_weight)"]}, {"cell_type": "code", "execution_count": null, "id": "0aa1e6fe-c6ae-40c6-b424-abef958affe1", "metadata": {}, "outputs": [], "source": ["# Calculating potential sales in a Hour\n", "merged_universe[\"final_calculations\"] = merged_universe[\"weight\"] * merged_universe[\"qty_sold\"]\n", "\n", "grouped_df = (\n", "    merged_universe[merged_universe[\"qty_sold\"].notnull()]\n", "    .groupby([\"hour_\", \"frontend_merchant_name\", \"pos_outlet_id\", \"facility_id_x\", \"item_id\"])\n", "    .agg({\"final_calculations\": \"sum\", \"weight\": \"sum\", \"qty_sold\": \"size\"})\n", "    .reset_index()\n", ")\n", "grouped_df.rename(columns={\"qty_sold\": \"unique_data\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "ba4a3caa-b26c-433f-9868-1ad0c273372f", "metadata": {}, "outputs": [], "source": ["# grouped_df.to_csv('b.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "e7c36bb8-d02d-48eb-ae3f-c88ec417b861", "metadata": {}, "outputs": [], "source": ["# Lets keep at float values, it might be better\n", "grouped_df[\"hourly_cpd\"] = grouped_df[\"final_calculations\"] / grouped_df[\"weight\"]\n", "grouped_df[\"hourly_cpd\"] = grouped_df[\"hourly_cpd\"].map(\"{:.2f}\".format)\n", "\n", "# grouped_df['hourly_cpd'] = np.ceil(grouped_df['hourly_cpd'])\n", "# grouped_df['hourly_cpd'] = grouped_df['hourly_cpd'].astype(int)\n", "# grouped_df"]}, {"cell_type": "code", "execution_count": null, "id": "fb5b48a4-d027-4074-b4c0-bfef806cdf58", "metadata": {}, "outputs": [], "source": ["# Taking those values where we have minimum three days of data on particular hour\n", "final_df = grouped_df.copy()\n", "# grouped_df[grouped_df[\"unique_data\"] >= 3]"]}, {"cell_type": "code", "execution_count": null, "id": "476ec694-2836-4090-abe6-0d9d124a2948", "metadata": {}, "outputs": [], "source": ["target_universe = merged_universe[\n", "    [\"hour_\", \"frontend_merchant_name\", \"pos_outlet_id\", \"facility_id_x\", \"item_id\"]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "97bd3cbf-0eb6-48f8-b51e-51bd48cc25ba", "metadata": {}, "outputs": [], "source": ["target_universe"]}, {"cell_type": "code", "execution_count": null, "id": "4e2e237f-29ab-47eb-8f43-c30a26222318", "metadata": {}, "outputs": [], "source": ["#  In df1 there are multiple rows where we do not get data from the previous values\n", "\n", "df1 = target_universe.merge(\n", "    final_df,\n", "    left_on=[\n", "        \"hour_\",\n", "        \"frontend_merchant_name\",\n", "        \"pos_outlet_id\",\n", "        \"facility_id_x\",\n", "        \"item_id\",\n", "    ],\n", "    right_on=[\n", "        \"hour_\",\n", "        \"frontend_merchant_name\",\n", "        \"pos_outlet_id\",\n", "        \"facility_id_x\",\n", "        \"item_id\",\n", "    ],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fbeaeed8-2290-41cb-a860-69d36f44127c", "metadata": {}, "outputs": [], "source": ["df1"]}, {"cell_type": "code", "execution_count": null, "id": "558bb732-6334-4407-8d46-952228ebf5c1", "metadata": {}, "outputs": [], "source": ["new_column_names = {\n", "    \"pos_outlet_id\": \"outlet_id\",\n", "    \"facility_id_x\": \"facility_id\",\n", "}\n", "\n", "df1.rename(columns=new_column_names, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "f3643f86-6075-4df8-bd3a-46010165e1c6", "metadata": {}, "outputs": [], "source": ["# Where we do not get the data from the last 15 days we are going ahead with different approach\n", "filtered_df = df1[df1[\"hourly_cpd\"].isna()]"]}, {"cell_type": "code", "execution_count": null, "id": "169ba4d4-65f2-4765-91d1-65ef1f76e800", "metadata": {}, "outputs": [], "source": ["filtered_df"]}, {"cell_type": "code", "execution_count": null, "id": "c44a7728-02f7-40e0-b0e1-d137088f31bd", "metadata": {}, "outputs": [], "source": ["unique_no_data_facility_id_values = filtered_df[\"facility_id\"].unique()\n", "facility_id_no_data = tuple(unique_no_data_facility_id_values)\n", "\n", "unique_no_data_item_id_values = filtered_df[\"item_id\"].unique()\n", "item_id_no_data = tuple(unique_no_data_item_id_values)"]}, {"cell_type": "code", "execution_count": null, "id": "c4e986a9-57c3-46b8-8c25-9247b1c17cef", "metadata": {}, "outputs": [], "source": ["# sql = f\"\"\"\n", "\n", "# with base_forecast as(\n", "# SELECT\n", "#     date(current_replenishment_ts_ist) date_,\n", "#     facility_id,\n", "#     item_id,\n", "#     name,\n", "#     max_qty,\n", "#     updated_at_ist,\n", "#     ROW_NUMBER() OVER (PARTITION BY facility_id, item_id, date(current_replenishment_ts_ist) ORDER BY updated_at_ist DESC) AS RowNum\n", "# FROM\n", "#     ds_etls.demand_forecast_item_ordering_min_max_quantity_log\n", "# WHERE\n", "#     current_replenishment_ts_ist = current_date\n", "# ),\n", "\n", "# final_forecast as (\n", "# select\n", "#     bf.date_,\n", "#     bf.facility_id,\n", "#     bf.item_id,\n", "#     bf.name,\n", "#     bf.max_qty as forecast_qty\n", "# from\n", "#     base_forecast bf\n", "# where\n", "#     RowNum = 1\n", "#     and facility_id in {facility_id_no_data}\n", "#     and item_id in (10002229)\n", "#     -- Ideally item id should pass here but right now we are doing only for perishable and this table have data for fnv\n", "# ),\n", "\n", "# hours as (\n", "#     select 0 as hour_ union all\n", "#     select 1 as hour_ union all\n", "#     select 2 as hour_ union all\n", "#     select 3 as hour_ union all\n", "#     select 4 as hour_ union all\n", "#     select 5 as hour_ union all\n", "#     select 6 as hour_ union all\n", "#     select 7 as hour_ union all\n", "#     select 8 as hour_ union all\n", "#     select 9 as hour_ union all\n", "#     select 10 as hour_ union all\n", "#     select 11 as hour_ union all\n", "#     select 12 as hour_ union all\n", "#     select 13 as hour_ union all\n", "#     select 14 as hour_ union all\n", "#     select 15 as hour_ union all\n", "#     select 16 as hour_ union all\n", "#     select 17 as hour_ union all\n", "#     select 18 as hour_ union all\n", "#     select 19 as hour_ union all\n", "#     select 20 as hour_ union all\n", "#     select 21 as hour_ union all\n", "#     select 22 as hour_ union all\n", "#     select 23 as hour_\n", "# ),\n", "\n", "# hourly_sales as (\n", "# SELECT\n", "#     extract(hour from order_create_ts_ist) as hour_,\n", "#     sum(o.procured_quantity) as qty_sold\n", "# FROM\n", "#     dwh.fact_sales_order_item_details o\n", "# INNER JOIN\n", "#     dwh.dim_product dp\n", "#     on dp.product_id = o.product_id\n", "#     and dp.l0_category_id = 1487\n", "#     and dp.is_current\n", "#     and dp.product_type in ('<PERSON><PERSON><PERSON>','Onion')\n", "# WHERE\n", "#     o.order_current_status = 'DELIVERED'\n", "#     and o.order_create_dt_ist >= current_date - interval '15' day\n", "#     and o.order_create_dt_ist < current_date\n", "#     and o.is_internal_order = False\n", "# GROUP BY 1\n", "# ),\n", "\n", "# total_sales as (\n", "# select\n", "#     sum(qty_sold) total_qty_sold\n", "# from\n", "#     hourly_sales\n", "# ),\n", "\n", "# hour_weights as (\n", "# select\n", "#     hs.hour_,\n", "#     hs.qty_sold,\n", "#     ts.total_qty_sold,\n", "#     (hs.qty_sold*1.00) / ts.total_qty_sold as weights\n", "\n", "# from\n", "#     hourly_sales hs\n", "# cross join\n", "#     total_sales ts\n", "# ),\n", "\n", "# hourly_forecast as (\n", "# select\n", "#     ff.*,\n", "#     h.hour_\n", "# from\n", "#     final_forecast ff\n", "# cross join\n", "#     hours h\n", "# ),\n", "\n", "# final_hourly_forecast as (\n", "# select\n", "#     a.*,\n", "#     b.weights\n", "# from\n", "#     hourly_forecast a\n", "# left join\n", "#     hour_weights b\n", "#     on a.hour_ = b.hour_\n", "# )\n", "\n", "# select\n", "#     f.*,\n", "#     coalesce(forecast_qty*weights,0) as hourly_forecast\n", "# from\n", "#     final_hourly_forecast f\n", "\n", "\n", "# \"\"\"\n", "# # forecast_split_hr_data = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "9b8478e2-55b9-4f16-aa49-0a4be94c7be3", "metadata": {}, "outputs": [], "source": ["# forecast_split_hr_data"]}, {"cell_type": "code", "execution_count": null, "id": "920af938-c968-440e-8e43-9f37b6819d79", "metadata": {}, "outputs": [], "source": ["# forecast_split_hr_data.to_csv('a.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "2e66b34b-d1e6-4fcc-a462-ab64d9b3d02d", "metadata": {}, "outputs": [], "source": ["# df2 = df1.merge(\n", "#     forecast_split_hr_data,\n", "#     left_on=[\"hour_\", \"facility_id\", \"item_id\"],\n", "#     right_on=[\"hour_\", \"facility_id\", \"item_id\"],\n", "#     how=\"left\",\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "bfc658d3-9401-4b3f-9313-0806c4ea1ca7", "metadata": {}, "outputs": [], "source": ["# # df2[df2[\"hourly_cpd\"].isna()]\n", "# df2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8d672f39-740b-4aa0-b55b-d364e82530f9", "metadata": {}, "outputs": [], "source": ["# df2[\"hourly_cpd\"] = df2[\"hourly_cpd\"].fillna(df2[\"hourly_forecast\"])"]}, {"cell_type": "code", "execution_count": null, "id": "05c20893-232a-4812-98ed-e3097de8a7d2", "metadata": {}, "outputs": [], "source": ["# df2.to_csv('a.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "996e3b81-76be-46a6-8953-5295317114a7", "metadata": {}, "outputs": [], "source": ["today_ = datetime.today().date()\n", "df1[\"date_of_consumption\"] = today_"]}, {"cell_type": "code", "execution_count": null, "id": "7fdb1187-ef1c-49eb-b069-3314fdacd2f0", "metadata": {}, "outputs": [], "source": ["merge_df = df1[\n", "    [\n", "        \"date_of_consumption\",\n", "        \"hour_\",\n", "        \"frontend_merchant_name\",\n", "        \"outlet_id\",\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"unique_data\",\n", "        \"hourly_cpd\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "1d1c9ca5-c73d-425a-8589-a196319fb839", "metadata": {}, "outputs": [], "source": ["merge_df = merge_df.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "fe69b2d9-a3ab-426b-b9c0-161debb7a564", "metadata": {}, "outputs": [], "source": ["merge_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "1ece5076-dc77-4741-a1e7-a5cb4f452122", "metadata": {}, "outputs": [], "source": ["merge_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c722eb82-d1d9-4c24-a920-cf38b0c31081", "metadata": {}, "outputs": [], "source": ["import datetime\n", "\n", "current_date_str = datetime.datetime.now().strftime(\"%Y-%m-%d\")\n", "\n", "\n", "# Changing dtypes\n", "merge_df[\"date_of_consumption\"] = pd.to_datetime(merge_df[\"date_of_consumption\"])\n", "merge_df[\"hour_\"] = merge_df[\"hour_\"].astype(int)\n", "merge_df[\"outlet_id\"] = merge_df[\"outlet_id\"].astype(int)\n", "merge_df[\"facility_id\"] = merge_df[\"facility_id\"].astype(int)\n", "merge_df[\"item_id\"] = merge_df[\"item_id\"].astype(int)\n", "merge_df[\"unique_data\"] = merge_df[\"unique_data\"].astype(int)\n", "merge_df[\"hourly_cpd\"] = merge_df[\"hourly_cpd\"].astype(float)\n", "\n", "column_dtypes = [\n", "    {\"name\": \"date_of_consumption\", \"type\": \"timestamp(6)\", \"description\": \"date\"},\n", "    {\"name\": \"hour_\", \"type\": \"integer\", \"description\": \"hour_\"},\n", "    {\n", "        \"name\": \"frontend_merchant_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"frontend_merchant_name\",\n", "    },\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"outlet_id\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility_id\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "    {\n", "        \"name\": \"unique_data\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"calculated_data_for_how_many_days\",\n", "    },\n", "    {\"name\": \"hourly_cpd\", \"type\": \"real\", \"description\": \"hourly_cpd\"},\n", "]\n", "\n", "# Pushing Data to Table\n", "if len(target_universe) == len(merge_df):\n", "    kwargs = {\n", "        \"schema_name\": \"supply_etls\",\n", "        \"table_name\": \"fnv_hourly_potential_sales\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\n", "            \"date_of_consumption\",\n", "            \"hour_\",\n", "            \"outlet_id\",\n", "            \"facility_id\",\n", "            \"item_id\",\n", "        ],\n", "        \"partition_key\": [\"date_of_consumption\"],\n", "        \"incremental_key\": \"date_of_consumption\",\n", "        \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "        \"table_description\": \"Expected Sales of FnV by EOD\",\n", "    }\n", "\n", "    pb.to_trino(merge_df, **kwargs)\n", "\n", "    channel = \"table-updates-tanishq\"\n", "    text_req = (\n", "        \"\\n <@U05CCTXLBU1> \\n Multiple ARS Run Hourly Potential Sales Updated for FnV \"\n", "        + current_date_str\n", "    )\n", "    pb.send_slack_message(\n", "        channel=channel,\n", "        text=text_req,\n", "    )\n", "else:\n", "    channel = \"table-updates-tanishq\"\n", "    text_req = (\n", "        \"\\n <@U05CCTXLBU1> \\n Multiple ARS Run Repeated Rows Table is not Updated \"\n", "        + current_date_str\n", "    )\n", "    pb.send_slack_message(\n", "        channel=channel,\n", "        text=text_req,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "383f416a-fa6e-4225-a72c-015c0b118851", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}