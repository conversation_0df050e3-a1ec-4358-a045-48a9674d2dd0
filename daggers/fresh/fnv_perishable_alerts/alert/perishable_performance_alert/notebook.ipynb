{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import os\n", "import time\n", "import json\n", "import gc\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(\n", "            f\"Read attempt: {attempt}... \\nRun started at {datetime.now() + timed<PERSON>ta(hours = 5.5)}\"\n", "        )\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pytz\n", "import calendar\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")\n", "\n", "current_time = datetime.now(IST)\n", "\n", "t1 = (current_time - timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "t2 = (current_time - timedelta(days=2)).strftime(\"%Y-%m-%d\")\n", "t7 = (current_time - timedelta(days=8)).strftime(\"%Y-%m-%d\")\n", "\n", "t1, t2, t7\n", "\n", "dates = (t1, t2, t7)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["slack_channel = pb.from_sheets(\"1AdjnAHCBHCCVX6RZ_xx5rbdsICeTwpJy1KFbee9AsJM\", \"alert_channel\")\n", "\n", "# slack_channel = pd.read_csv('alert_input.csv')\n", "\n", "slack_channel = (\n", "    slack_channel[slack_channel[\"alert\"] == \"perishbale_daily_view_alert\"]\n", "    .reset_index()\n", "    .iloc[:, 2][0]\n", ")\n", "\n", "slack_channel"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_list = tuple(list({t1, t2, t7}))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_list"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## Assortment Details"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Perishable SKUs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sku_query = \"\"\"\n", "    WITH perishable_assortment AS (\n", "        SELECT item_id, perishable \n", "        FROM (\n", "            SELECT a.item_id, a.perishable, c.l0, c.l1, c.l2\n", "            FROM rpc.product_product a \n", "            JOIN (\n", "                SELECT item_id, MAX(id) AS id\n", "                FROM rpc.product_product\n", "                WHERE active = 1 AND approved = 1\n", "                AND lake_active_record\n", "                GROUP BY 1\n", "            ) b ON a.item_id = b.item_id AND a.id = b.id\n", "            JOIN \n", "            (\n", "                SELECT item_id, l0_id, l0, l1, l2_id, l2\n", "                FROM rpc.item_category_details\n", "                WHERE l0_id NOT IN (1487)\n", "                AND lake_active_record\n", "                GROUP BY 1,2,3,4,5,6\n", "            ) c ON c.item_id = a.item_id\n", "            WHERE l2_id IN (116, 1425, 198, 31, 1097, 1956, 1389, 1778, 1094, 1093, 1091, 138, 949, 950, 2633, 63, 1367, 1369, 1185) \n", "            AND perishable = 1\n", "            AND a.lake_active_record\n", "        ) GROUP BY 1,2\n", "    )\n", "    SELECT * FROM perishable_assortment\n", "    \"\"\"\n", "sku_df = pd.read_sql_query(sku_query, trino)\n", "sku_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sku_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sku_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_list = tuple(set(sku_df[\"item_id\"].to_list()))\n", "len(item_list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["category_query = \"\"\"\n", "    SELECT item_id, \n", "    CASE \n", "        WHEN l0_id IN (1487) THEN 'FnV'\n", "        WHEN l2_id IN (1425) THEN 'Batter'\n", "        WHEN l2_id IN (31,116,198,1097,1956,2633) THEN 'Breads'\n", "        WHEN l2_id IN (949) THEN 'Curd'\n", "        WHEN l2_id IN (1389,1778) THEN 'Eggs'\n", "        WHEN l2_id IN (63,1367,1369) THEN 'Meats'\n", "        WHEN l2_id IN (1185) THEN 'Milk'\n", "        WHEN l2_id IN (950) THEN 'Paneer'\n", "        WHEN l2_id IN (138,1091,1093) THEN 'Perishable Dairy'\n", "        WHEN l2_id IN (1094) THEN 'Yogurt'\n", "        ELSE 'Others'\n", "    END AS category FROM (\n", "        SELECT item_id, l2_id, l0_id \n", "        FROM rpc.item_category_details\n", "        WHERE lake_active_record\n", "        GROUP BY 1,2,3\n", "    )\n", "    \"\"\"\n", "item_category_df = pd.read_sql_query(category_query, trino)\n", "item_category_df.shape"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## Sales Details"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["#### Item Sales, GMV & Carts"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_category_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### sales_0\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_query = f\"\"\"\n", "    WITH item_mapping as (\n", "        SELECT DISTINCT ipr.product_id,\n", "        CASE\n", "            WHEN ipr.item_id IS NULL THEN ipom_0.item_id\n", "            ELSE ipr.item_id\n", "        END AS item_id,\n", "        CASE\n", "            WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1)\n", "            ELSE COALESCE(ipom_0.multiplier,1)\n", "        END AS multiplier\n", "        FROM rpc.item_product_mapping ipr\n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id\n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", "        WHERE ipr.lake_active_record\n", "    ),\n", "    category_mapping as \n", "    (\n", "    SELECT item_id, \n", "    CASE \n", "        WHEN l0_id IN (1487) THEN 'FnV'\n", "        WHEN l2_id IN (1425) THEN 'Batter'\n", "        WHEN l2_id IN (31,116,198,1097,1956,2633) THEN 'Breads'\n", "        WHEN l2_id IN (949) THEN 'Curd'\n", "        WHEN l2_id IN (1389,1778) THEN 'Eggs'\n", "        WHEN l2_id IN (63,1367,1369) THEN 'Meats'\n", "        WHEN l2_id IN (1185) THEN 'Milk'\n", "        WHEN l2_id IN (950) THEN 'Paneer'\n", "        WHEN l2_id IN (138,1091,1093) THEN 'Perishable Dairy'\n", "        WHEN l2_id IN (1094) THEN 'Yogurt'\n", "        ELSE 'Others'\n", "    END AS category FROM (\n", "        SELECT item_id, l2_id, l0_id \n", "        FROM rpc.item_category_details\n", "        WHERE lake_active_record\n", "        GROUP BY 1,2,3\n", "    )\n", "    )\n", "    ,\n", "    \n", "    perishable_assortment AS (\n", "        SELECT item_id, perishable \n", "        FROM (\n", "            SELECT a.item_id, a.perishable, c.l0, c.l1, c.l2\n", "            FROM rpc.product_product a \n", "            JOIN (\n", "                SELECT item_id, MAX(id) AS id\n", "                FROM rpc.product_product\n", "                WHERE active = 1 AND approved = 1\n", "                AND lake_active_record\n", "                GROUP BY 1\n", "            ) b ON a.item_id = b.item_id AND a.id = b.id\n", "            JOIN \n", "            (\n", "                SELECT item_id, l0_id, l0, l1, l2_id, l2\n", "                FROM rpc.item_category_details\n", "                WHERE l0_id NOT IN (1487)\n", "                AND lake_active_record\n", "                GROUP BY 1,2,3,4,5,6\n", "            ) c ON c.item_id = a.item_id\n", "            WHERE l2_id IN (116, 1425, 198, 31, 1097, 1956, 1389, 1778, 1094, 1093, 1091, 138, 949, 950, 2633, 63, 1367, 1369, 1185) \n", "            AND perishable = 1\n", "            AND a.lake_active_record\n", "        ) GROUP BY 1,2\n", "    )\n", "  \n", ",\n", "    sales AS (\n", "        SELECT DATE(oid.order_create_dt_ist) AS date_, \n", "        --oid.outlet_id, oid.product_id, im.item_id, oid.order_id,\n", "       cm.category,\n", "        \n", "      CASE WHEN perishable = 1 THEN 'Perishable' ELSE 'Non-Perishable' END AS perishable_flag,\n", "\n", "        \n", "        sum((oid.procured_quantity - oid.total_doorstep_return_quantity) * im.multiplier) AS sales_quantity,\n", "        sum(oid.total_selling_price * 1.000 / im.multiplier) AS gmv, \n", "        sum(oid.total_retained_margin * 1.000 / im.multiplier) AS retained_margin,\n", "        count(distinct oid.order_id) carts\n", "        \n", "        FROM dwh.fact_sales_order_item_details oid\n", "        JOIN item_mapping im on im.product_id = oid.product_id\n", "        left join perishable_assortment pa on im.item_id=pa.item_id\n", "        left join category_mapping cm on im.item_id=cm.item_id\n", "        JOIN retail.console_outlet rco ON rco.id = oid.outlet_id AND business_type_id = 7 AND rco.lake_active_record\n", "        WHERE order_create_dt_ist IS NOT NULL\n", "        AND DATE(order_create_dt_ist) IN ({','.join([f\"DATE '{date}'\" for date in date_list])})\n", "\n", "        AND oid.is_internal_order = false AND (oid.order_type NOT LIKE '%%internal%%' OR oid.order_type IS NULL)\n", "        AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED'\n", "        group by 1,2,3\n", "    )\n", "  SELECT date_, \n", "       coalesce(category,'Other') category,\n", "       perishable_flag,\n", "       SUM(sales_quantity) AS total_sales, \n", "       SUM(gmv) AS total_gmv, \n", "       SUM(retained_margin) AS total_retained_margin\n", "FROM sales\n", "GROUP BY 1,2,3\n", "\n", "\"\"\"\n", "sales_pre_df_0 = pd.read_sql_query(sales_query, trino)\n", "\n", "sales_pre_df_0.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_pre_df_0[\"category\"].unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### sales_1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_query = f\"\"\"\n", "    WITH item_mapping as (\n", "        SELECT DISTINCT ipr.product_id,\n", "        CASE\n", "            WHEN ipr.item_id IS NULL THEN ipom_0.item_id\n", "            ELSE ipr.item_id\n", "        END AS item_id,\n", "        CASE\n", "            WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1)\n", "            ELSE COALESCE(ipom_0.multiplier,1)\n", "        END AS multiplier\n", "        FROM rpc.item_product_mapping ipr\n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id\n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", "        WHERE ipr.lake_active_record\n", "    ),\n", "    \n", "    perishable_assortment AS (\n", "        SELECT item_id, perishable \n", "        FROM (\n", "            SELECT a.item_id, a.perishable, c.l0, c.l1, c.l2\n", "            FROM rpc.product_product a \n", "            JOIN (\n", "                SELECT item_id, MAX(id) AS id\n", "                FROM rpc.product_product\n", "                WHERE active = 1 AND approved = 1\n", "                AND lake_active_record\n", "                GROUP BY 1\n", "            ) b ON a.item_id = b.item_id AND a.id = b.id\n", "            JOIN \n", "            (\n", "                SELECT item_id, l0_id, l0, l1, l2_id, l2\n", "                FROM rpc.item_category_details\n", "                WHERE l0_id NOT IN (1487)\n", "                AND lake_active_record\n", "                GROUP BY 1,2,3,4,5,6\n", "            ) c ON c.item_id = a.item_id\n", "            WHERE l2_id IN (116, 1425, 198, 31, 1097, 1956, 1389, 1778, 1094, 1093, 1091, 138, 949, 950, 2633, 63, 1367, 1369, 1185) \n", "            AND perishable = 1\n", "            AND a.lake_active_record\n", "        ) GROUP BY 1,2\n", "    )\n", "  \n", ",\n", "    sales AS (\n", "        SELECT DATE(oid.order_create_dt_ist) AS date_, \n", "        --oid.outlet_id, oid.product_id, im.item_id, oid.order_id,\n", "        \n", "      CASE WHEN perishable = 1 THEN 'Perishable' ELSE 'Non-Perishable' END AS perishable_flag,\n", "\n", "        \n", "        sum((oid.procured_quantity - oid.total_doorstep_return_quantity) * im.multiplier) AS sales_quantity,\n", "        sum(oid.total_selling_price * 1.000 / im.multiplier) AS gmv, \n", "        sum(oid.total_retained_margin * 1.000 / im.multiplier) AS retained_margin,\n", "        count(distinct oid.order_id) carts\n", "        \n", "        FROM dwh.fact_sales_order_item_details oid\n", "        JOIN item_mapping im on im.product_id = oid.product_id\n", "        left join perishable_assortment pa on im.item_id=pa.item_id\n", "        JOIN retail.console_outlet rco ON rco.id = oid.outlet_id AND business_type_id = 7 AND rco.lake_active_record\n", "        \n", "        \n", "        WHERE order_create_dt_ist IS NOT NULL\n", "        AND DATE(order_create_dt_ist) IN ({','.join([f\"DATE '{date}'\" for date in date_list])})\n", "        AND oid.is_internal_order = false AND (oid.order_type NOT LIKE '%%internal%%' OR oid.order_type IS NULL)\n", "        AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED'\n", "        group by 1,2\n", "    )\n", "  SELECT date_, \n", "       perishable_flag,\n", "       SUM(sales_quantity) AS total_sales, \n", "       SUM(gmv) AS total_gmv, \n", "       SUM(retained_margin) AS total_retained_margin,\n", "       sum(carts) as total_carts\n", "FROM sales\n", "GROUP BY 1,2\n", "\n", "\"\"\"\n", "sales_pre_df_1 = pd.read_sql_query(sales_query, trino)\n", "\n", "sales_pre_df_1.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### sales_1.1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_query = f\"\"\"\n", "    WITH item_mapping as (\n", "        SELECT DISTINCT ipr.product_id,\n", "        CASE\n", "            WHEN ipr.item_id IS NULL THEN ipom_0.item_id\n", "            ELSE ipr.item_id\n", "        END AS item_id,\n", "        CASE\n", "            WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1)\n", "            ELSE COALESCE(ipom_0.multiplier,1)\n", "        END AS multiplier\n", "        FROM rpc.item_product_mapping ipr\n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id\n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", "        WHERE ipr.lake_active_record\n", "    ),\n", "    \n", "    perishable_assortment AS (\n", "        SELECT item_id, perishable \n", "        FROM (\n", "            SELECT a.item_id, a.perishable, c.l0, c.l1, c.l2\n", "            FROM rpc.product_product a \n", "            JOIN (\n", "                SELECT item_id, MAX(id) AS id\n", "                FROM rpc.product_product\n", "                WHERE active = 1 AND approved = 1\n", "                AND lake_active_record\n", "                GROUP BY 1\n", "            ) b ON a.item_id = b.item_id AND a.id = b.id\n", "            JOIN \n", "            (\n", "                SELECT item_id, l0_id, l0, l1, l2_id, l2\n", "                FROM rpc.item_category_details\n", "                WHERE l0_id NOT IN (1487)\n", "                AND lake_active_record\n", "                GROUP BY 1,2,3,4,5,6\n", "            ) c ON c.item_id = a.item_id\n", "            WHERE l2_id IN (116, 1425, 198, 31, 1097, 1956, 1389, 1778, 1094, 1093, 1091, 138, 949, 950, 2633, 63, 1367, 1369, 1185) \n", "            AND perishable = 1\n", "            AND a.lake_active_record\n", "        ) GROUP BY 1,2\n", "    )\n", "  \n", ",\n", "    sales AS (\n", "        SELECT DATE(oid.order_create_dt_ist) AS date_, \n", "        --oid.outlet_id, oid.product_id, im.item_id, oid.order_id,\n", "      \n", "\n", "        \n", "        sum((oid.procured_quantity - oid.total_doorstep_return_quantity) * im.multiplier) AS sales_quantity,\n", "        sum(oid.total_selling_price * 1.000 / im.multiplier) AS gmv, \n", "        sum(oid.total_retained_margin * 1.000 / im.multiplier) AS retained_margin,\n", "        count(distinct oid.order_id) carts\n", "        \n", "        FROM dwh.fact_sales_order_item_details oid\n", "        JOIN item_mapping im on im.product_id = oid.product_id\n", "        left join perishable_assortment pa on im.item_id=pa.item_id\n", "        JOIN retail.console_outlet rco ON rco.id = oid.outlet_id AND business_type_id = 7 AND rco.lake_active_record\n", "        \n", "        \n", "        WHERE order_create_dt_ist IS NOT NULL\n", "        AND DATE(order_create_dt_ist) IN ({','.join([f\"DATE '{date}'\" for date in date_list])})\n", "        AND oid.is_internal_order = false AND (oid.order_type NOT LIKE '%%internal%%' OR oid.order_type IS NULL)\n", "        AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED'\n", "        group by 1\n", "    )\n", "  SELECT date_, \n", "       SUM(sales_quantity) AS total_sales, \n", "       SUM(gmv) AS total_gmv, \n", "       SUM(retained_margin) AS total_retained_margin,\n", "       sum(carts) as total_carts\n", "FROM sales\n", "GROUP BY 1\n", "\n", "\"\"\"\n", "sales_pre_df_1_1 = pd.read_sql_query(sales_query, trino)\n", "\n", "sales_pre_df_1_1.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_pre_df_1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_pre_df_1_1.head(6)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### sales_2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_query = f\"\"\"\n", "    WITH item_mapping as (\n", "        SELECT DISTINCT ipr.product_id,\n", "        CASE\n", "            WHEN ipr.item_id IS NULL THEN ipom_0.item_id\n", "            ELSE ipr.item_id\n", "        END AS item_id,\n", "        CASE\n", "            WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1)\n", "            ELSE COALESCE(ipom_0.multiplier,1)\n", "        END AS multiplier\n", "        FROM rpc.item_product_mapping ipr\n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id\n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", "        WHERE ipr.lake_active_record\n", "    ),\n", "    perishable_assortment AS (\n", "        SELECT item_id, perishable \n", "        FROM (\n", "            SELECT a.item_id, a.perishable, c.l0, c.l1, c.l2\n", "            FROM rpc.product_product a \n", "            JOIN (\n", "                SELECT item_id, MAX(id) AS id\n", "                FROM rpc.product_product\n", "                WHERE active = 1 AND approved = 1\n", "                AND lake_active_record\n", "                GROUP BY 1\n", "            ) b ON a.item_id = b.item_id AND a.id = b.id\n", "            JOIN \n", "            (\n", "                SELECT item_id, l0_id, l0, l1, l2_id, l2\n", "                FROM rpc.item_category_details\n", "                WHERE l0_id NOT IN (1487)\n", "                AND lake_active_record\n", "                GROUP BY 1,2,3,4,5,6\n", "            ) c ON c.item_id = a.item_id\n", "            WHERE l2_id IN (116, 1425, 198, 31, 1097, 1956, 1389, 1778, 1094, 1093, 1091, 138, 949, 950, 2633, 63, 1367, 1369, 1185) \n", "            AND perishable = 1\n", "            AND a.lake_active_record\n", "        ) GROUP BY 1,2\n", "    )\n", "  \n", ",\n", "    sales AS (\n", "        SELECT DATE(oid.order_create_dt_ist) AS date_, \n", "        --oid.outlet_id, oid.product_id, im.item_id, \n", "        oid.order_id,\n", "        \n", "      count(distinct CASE WHEN perishable = 1 then im.item_id end) as perishable_item_cnt,\n", "      count(distinct im.item_id) as total_item_cnt\n", "\n", "        \n", "        FROM dwh.fact_sales_order_item_details oid\n", "        JOIN item_mapping im on im.product_id = oid.product_id\n", "        left join perishable_assortment pa on im.item_id=pa.item_id\n", "        JOIN retail.console_outlet rco ON rco.id = oid.outlet_id AND business_type_id = 7 AND rco.lake_active_record\n", "        \n", "        \n", "        WHERE order_create_dt_ist IS NOT NULL\n", "        AND DATE(order_create_dt_ist) IN ({','.join([f\"DATE '{date}'\" for date in date_list])})\n", "        AND oid.is_internal_order = false AND (oid.order_type NOT LIKE '%%internal%%' OR oid.order_type IS NULL)\n", "        AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED'\n", "        group by 1,2\n", ")\n", "\n", "  SELECT date_, \n", "       COUNT(DISTINCT order_id) pure_carts\n", "      \n", "FROM sales\n", "where perishable_item_cnt= total_item_cnt\n", "GROUP BY 1\n", "\n", "\"\"\"\n", "sales_pre_df_2 = pd.read_sql_query(sales_query, trino)\n", "\n", "sales_pre_df_2.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_pre_df_2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_pre_df_0.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_pre_df_1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_pre_df_2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# sales_df = pd.merge(sales_pre_df, sku_df, on=[\"item_id\"], how=\"left\")\n", "# sales_df = pd.merge(sales_df, item_category_df, on=[\"item_id\"], how=\"left\")\n", "\n", "sales_pre_df_0[\"category\"] = sales_pre_df_0[\"category\"].fillna(\"Others\")\n", "sales_pre_df_1[\"perishable_flag\"] = sales_pre_df_1[\"perishable_flag\"].fillna(\"Non-Perishable\")\n", "\n", "sales_pre_df_0[\"date_\"] = pd.to_datetime(sales_pre_df_0[\"date_\"])\n", "sales_pre_df_1[\"date_\"] = pd.to_datetime(sales_pre_df_1[\"date_\"])\n", "sales_pre_df_2[\"date_\"] = pd.to_datetime(sales_pre_df_2[\"date_\"])"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## DAU Details"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dau_query = f\"\"\"\n", "    WITH base AS (\n", "        SELECT DATE(snapshot_date_ist) AS date_, city, SUM(daily_active_users) AS dau \n", "        FROM dwh.agg_daily_consumer_conversion_details\n", "        WHERE snapshot_date_ist IN ({','.join([f\"DATE '{date}'\" for date in dates])}) \n", "        AND city NOT IN ('Overall') AND merchant_id IN ('Overall') AND snapshot_hour_ist = 24\n", "        GROUP BY 1,2\n", "    )\n", "    \n", "    SELECT date_, SUM(dau) AS dau\n", "    FROM base \n", "    GROUP BY 1\n", "    \"\"\"\n", "dau_df = pd.read_sql_query(dau_query, trino)\n", "\n", "dau_df[\"date_\"] = pd.to_datetime(dau_df[\"date_\"])\n", "\n", "dau_df.shape"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## Weighted Availability"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dau_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_query = f\"\"\"\n", "     WITH base AS (\n", "        SELECT * FROM supply_etls.hourly_inventory_snapshots\n", "        WHERE date_ist >= current_date - interval '10' day\n", "    ),\n", "\n", "    avail_pre AS (\n", "        SELECT date_, hour_, facility_id, outlet_id, item_id, MAX(is_available) AS is_available\n", "        FROM (\n", "            SELECT DATE(date_ist) AS date_, EXTRACT(hour FROM updated_at_ist) AS hour_, facility_id, outlet_id, item_id, \n", "            CASE \n", "                WHEN current_inventory > 0 THEN 1 \n", "                ELSE 0  \n", "            END is_available\n", "            FROM base\n", "        )\n", "        WHERE hour_ BETWEEN 6 AND 23\n", "        GROUP BY 1,2,3,4,5\n", "    ),\n", "\n", "    item_details AS (\n", "        SELECT ma.item_id as item_id, icd.name AS item_name, \n", "        CASE \n", "            WHEN l2_id IN (1425) THEN 'Batter'\n", "            WHEN l2_id IN (31,116,198,1097,1956,2633) THEN 'Breads'\n", "            WHEN l2_id IN (949) THEN 'Curd'\n", "            WHEN l2_id IN (1389,1778) THEN 'Eggs'\n", "            WHEN l2_id IN (63,1367,1369) THEN 'Meats'\n", "            WHEN l2_id IN (1185) THEN 'Milk'\n", "            WHEN l2_id IN (950) THEN 'Paneer'\n", "            WHEN l2_id IN (138,1091,1093) THEN 'Perishable Dairy'\n", "            WHEN l2_id IN (1094) THEN 'Yogurt' \n", "            ELSE 'Others' \n", "        END AS l2\n", "        FROM rpc.product_product ma\n", "        JOIN (\n", "            SELECT item_id, MAX(id) AS id\n", "            FROM rpc.product_product\n", "            WHERE active = 1 AND approved = 1 AND perishable = 1\n", "            GROUP BY 1\n", "        ) b ON ma.item_id = b.item_id \n", "        JOIN rpc.item_category_details icd ON icd.item_id = ma.item_id\n", "        WHERE l2_id IN (116, 1425, 198, 31, 1097, 1956, 1389, 1778, 1094, 1093, 1091, 138, 949, 950, 2633, 63, 1367, 1369, 1185) \n", "        AND ma.perishable = 1\n", "        GROUP BY 1,2,3\n", "    )\n", "   \n", "   SELECT cl.name AS city, date_, hour_, a.facility_id, a.item_id, b.l2 AS category, is_available\n", "   FROM avail_pre a\n", "   JOIN item_details b ON a.item_id = b.item_id\n", "   JOIN retail.console_outlet co ON co.id = a.outlet_id AND co.active = 1 AND co.business_type_id = 7\n", "   LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id\n", "   WHERE date_ IN ({','.join([f\"DATE '{date}'\" for date in dates])})\n", "   GROUP BY 1,2,3,4,5,6,7 \n", "\"\"\"\n", "base_df = pd.read_sql_query(base_query, trino)\n", "\n", "base_df[\"date_\"] = pd.to_datetime(base_df[\"date_\"])\n", "\n", "base_df = base_df[\n", "    [\"city\", \"date_\", \"hour_\", \"facility_id\", \"item_id\", \"category\", \"is_available\"]\n", "].drop_duplicates()\n", "\n", "base_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Weights Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_hour_query = \"\"\"\n", "    SELECT city, order_hour AS hour_, CAST(weights AS DOUBLE) AS chw\n", "    FROM supply_etls.city_hour_weights \n", "    WHERE updated_at IN (SELECT MAX(updated_at) FROM supply_etls.city_hour_weights where updated_at >= current_date - interval '60' day)\n", "    AND order_hour BETWEEN 6 AND 23\n", "    and updated_at >= current_date - interval '60' day\n", "    \"\"\"\n", "city_hour_df = pd.read_sql_query(city_hour_query, trino)\n", "\n", "city_item_query = \"\"\"\n", "    SELECT city, a.item_id, CAST(weights AS DOUBLE) AS ciw\n", "    FROM supply_etls.city_item_weights a\n", "    WHERE a.updated_at IN (SELECT MAX(updated_at) FROM supply_etls.city_item_weights where updated_at >= current_date - interval '60' day)\n", "    and updated_at >= current_date - interval '60' day\n", "    \"\"\"\n", "city_item_df = pd.read_sql_query(city_item_query, trino)\n", "\n", "city_store_query = \"\"\"\n", "    SELECT city, facility_id, CAST(store_weight AS DOUBLE) AS csw\n", "    FROM supply_etls.city_store_weights\n", "    WHERE updated_at IN (SELECT MAX(updated_at) FROM supply_etls.city_store_weights where updated_at >= current_date - interval '60' day) \n", "    and updated_at >= current_date - interval '60' day\n", "    \"\"\"\n", "city_store_df = pd.read_sql_query(city_store_query, trino)\n", "\n", "city_pan_query = \"\"\"\n", "    SELECT city, CAST(weight AS DOUBLE) AS pw\n", "    FROM supply_etls.city_weights cw \n", "    WHERE updated_at = (SELECT MAX(updated_at) FROM supply_etls.city_weights where updated_at >= current_date - interval '60' day)\n", "    and updated_at >= current_date - interval '60' day\n", "    \"\"\"\n", "\n", "city_pan_df = pd.read_sql_query(city_pan_query, trino)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Calculations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# --- City Hourly Item --- #\n", "c_base_df = pd.merge(base_df, city_store_df, on=[\"city\", \"facility_id\"], how=\"left\")\n", "\n", "c_base_df[\"score\"] = c_base_df[\"is_available\"] * c_base_df[\"csw\"]\n", "\n", "facility_agg_df = (\n", "    c_base_df.groupby([\"city\", \"item_id\", \"category\", \"date_\", \"hour_\"])\n", "    .agg({\"csw\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"csw\": \"tot_csw\"})\n", ")\n", "\n", "c_base_df = c_base_df.merge(\n", "    facility_agg_df, on=[\"city\", \"item_id\", \"date_\", \"hour_\", \"category\"], how=\"left\"\n", ")\n", "\n", "c_base_df[\"score\"] = c_base_df[\"score\"] / c_base_df[\"tot_csw\"]\n", "\n", "city_hourly_df = (\n", "    c_base_df.groupby([\"city\", \"item_id\", \"category\", \"date_\", \"hour_\"])\n", "    .agg({\"score\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "# --- City Aggregate Item --- #\n", "city_overall_df = pd.merge(city_hourly_df, city_hour_df, on=[\"city\", \"hour_\"], how=\"left\")\n", "\n", "city_agg_df = (\n", "    city_overall_df.groupby([\"city\", \"date_\", \"item_id\", \"category\"])\n", "    .agg({\"chw\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"chw\": \"tot_chw\"})\n", ")\n", "\n", "city_overall_df = city_overall_df.merge(\n", "    city_agg_df, on=[\"city\", \"date_\", \"item_id\", \"category\"], how=\"left\"\n", ")\n", "\n", "city_overall_df[\"score\"] = (\n", "    city_overall_df[\"score\"] * city_overall_df[\"chw\"] / city_overall_df[\"tot_chw\"]\n", ")\n", "\n", "city_overall_df = (\n", "    city_overall_df.groupby([\"city\", \"date_\", \"item_id\", \"category\"])\n", "    .agg({\"score\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "# --- Category Item --- #\n", "category_df = pd.merge(city_overall_df, city_item_df, on=[\"city\", \"item_id\"], how=\"left\")\n", "\n", "item_agg_df = (\n", "    category_df.groupby([\"city\", \"date_\", \"category\"])\n", "    .agg({\"ciw\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"ciw\": \"tot_ciw\"})\n", ")\n", "\n", "category_df = category_df.merge(item_agg_df, on=[\"date_\", \"city\", \"category\"], how=\"left\")\n", "\n", "category_df[\"score\"] = category_df[\"score\"] * category_df[\"ciw\"] / category_df[\"tot_ciw\"]\n", "\n", "category_df = category_df.groupby([\"city\", \"date_\", \"category\"]).agg({\"score\": \"sum\"}).reset_index()\n", "\n", "# --- All Item --- #\n", "category_all_df = pd.merge(city_overall_df, city_item_df, on=[\"city\", \"item_id\"], how=\"left\")\n", "\n", "item_all_df = (\n", "    category_all_df.groupby([\"city\", \"date_\"])\n", "    .agg({\"ciw\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"ciw\": \"tot_ciw\"})\n", ")\n", "\n", "category_all_df = category_all_df.merge(item_all_df, on=[\"date_\", \"city\"], how=\"left\")\n", "\n", "category_all_df[\"score\"] = (\n", "    category_all_df[\"score\"] * category_all_df[\"ciw\"] / category_all_df[\"tot_ciw\"]\n", ")\n", "\n", "category_all_df = category_all_df.groupby([\"city\", \"date_\"]).agg({\"score\": \"sum\"}).reset_index()\n", "\n", "category_all_df[\"category\"] = \"All\"\n", "\n", "category_all_df = category_all_df[[\"city\", \"date_\", \"category\", \"score\"]]\n", "\n", "overall_df = pd.concat([category_df, category_all_df])\n", "\n", "# --- Pan India Item --- #\n", "pan_df = pd.merge(overall_df, city_pan_df, on=[\"city\"], how=\"left\")\n", "\n", "pan_agg_df = (\n", "    pan_df.groupby([\"date_\", \"category\"])\n", "    .agg({\"pw\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"pw\": \"tot_pw\"})\n", ")\n", "\n", "pan_df = pan_df.merge(pan_agg_df, on=[\"date_\", \"category\"], how=\"left\")\n", "pan_df[\"score\"] = pan_df[\"score\"] * pan_df[\"pw\"] / pan_df[\"tot_pw\"]\n", "pan_df = pan_df.groupby([\"date_\", \"category\"]).agg({\"score\": \"sum\"}).reset_index()\n", "\n", "del (\n", "    c_base_df,\n", "    facility_agg_df,\n", "    city_hourly_df,\n", "    city_overall_df,\n", "    city_agg_df,\n", "    category_df,\n", "    item_agg_df,\n", "    category_all_df,\n", "    item_all_df,\n", "    overall_df,\n", "    pan_agg_df,\n", ")\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_df = pan_df.copy().rename(columns={\"score\": \"wt_availability\"})\n", "overall_availability_df = availability_df[availability_df[\"category\"] == \"All\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_availability_df"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## QnG Details"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_query = f\"\"\"\n", "    WITH  assortment_base AS (\n", "        SELECT DISTINCT ma.item_id AS item_id, icd.name AS item_name, l2\n", "        FROM rpc.product_product ma\n", "        JOIN (\n", "            SELECT item_id, MAX(id) AS id\n", "            FROM rpc.product_product\n", "            WHERE active = 1 and approved = 1\n", "            GROUP BY 1\n", "        ) b ON ma.item_id = b.item_id and ma.id = b.id\n", "        JOIN rpc.item_category_details icd ON icd.item_id = ma.item_id\n", "        WHERE l2_id in (116, 1425, 198, 31, 1097, 1956, 1389, 1778, 1094, 1093, 1091, 138, 949, 950, 2633, 63, 1367, 1369, 1185)\n", "    ),\n", "    \n", "    base AS (\n", "        SELECT DATE(oid.cart_checkout_ts_ist) AS date_, oid.city_name, ab.l2, COUNT(DISTINCT session_id) AS qng_complaints\n", "        FROM cd_etls.item_wise_reso cd\n", "        JOIN dwh.fact_sales_order_item_details oid on oid.order_id = cd.order_id AND oid.order_type IN ('RetailForwardOrder', 'DropShippingForwardOrder') and oid.order_current_status = ('DELIVERED') and oid.product_id = cd.product_id\n", "        JOIN (\n", "            SELECT DISTINCT ipr.product_id, \n", "            CASE \n", "                WHEN ipr.item_id IS NULL THEN ipom_0.item_id \n", "                ELSE ipr.item_id \n", "            END AS item_id,\n", "            CASE \n", "                WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1) \n", "                ELSE COALESCE(ipom_0.multiplier,1) \n", "            END AS multiplier \n", "            FROM rpc.item_product_mapping ipr\n", "            LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipr.product_id = ipom.product_id AND ipr.item_id = ipom.item_id\n", "            LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipr.product_id = ipom_0.product_id\n", "        ) pl ON pl.product_id = oid.product_id\n", "        JOIN assortment_base ab on ab.item_id = pl.item_id \n", "        WHERE oid.order_create_dt_ist >= current_Date - interval '10' day\n", "        and reso_date >= current_date - interval '30' day\n", "        GROUP BY 1,2,3\n", "    )\n", "    \n", "    SELECT date_, SUM(qng_complaints) AS qng\n", "    FROM base\n", "    WHERE date_ IN ({','.join([f\"DATE '{date}'\" for date in dates])})\n", "    GROUP BY 1\n", "    \"\"\"\n", "qng_df = pd.read_sql_query(qng_query, trino)\n", "\n", "qng_df[\"date_\"] = pd.to_datetime(qng_df[\"date_\"])\n", "\n", "qng_df.shape"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## Dump Details"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### RTV dump"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select\n", "  distinct item_id,\n", "  city,\n", "  date_,\n", "  claim_percent\n", "from\n", "  supply_etls.perishable_rtv\n", "where\n", "  date_ >= current_date - interval '15' day\n", "  and date_ < current_date + interval '1' day\"\"\"\n", "\n", "rtv_df = pd.read_sql_query(query, trino)\n", "rtv_df[\"rtv_flag\"] = 1\n", "rtv_df[\"date_\"] = pd.to_datetime(rtv_df[\"date_\"])\n", "rtv_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dump_query = f\"\"\" \n", "with dump_ as (\n", "select\n", "    date(dump_date_ist) as date_, \n", "    cl.name as city, \n", "    icd.item_id,\n", "    SUM(tot_dump_quan) as dump_quantity, \n", "    SUM(tot_dump) as dump_value\n", "from \n", "    supply_etls.fnv_milk_perishables_dump_raw dr\n", "inner join \n", "    rpc.item_category_details icd \n", "    on icd.item_id = dr.item_id \n", "    and icd.l2_id in (116, 1425, 198, 31, 1097, 1956, 1389, 1778, 1094, 1093, 1091, 138, 949, 950, 2633, 63, 1367, 1369, 1185)\n", "left join \n", "    retail.console_outlet co \n", "    on co.id = dr.outlet_id \n", "    and co.active = 1 \n", "    and co.lake_active_record \n", "left join \n", "    retail.console_location cl \n", "    on cl.id = co.tax_location_id\n", "where \n", "    facility_type = 'frontend'\n", "    and dump_date_ist >= current_date - interval '10' day \n", "group by 1,2,3\n", ")\n", "\n", "select date_, city, item_id,  sum(dump_value) as dump_value  from dump_\n", "group by 1,2,3\n", "\"\"\"\n", "\n", "dump_df = pd.read_sql_query(dump_query, trino)\n", "\n", "dump_df[\"dump_value\"] = dump_df[\"dump_value\"].astype(float)\n", "\n", "dump_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dump_df = dump_df.merge(item_category_df, on=[\"item_id\"], how=\"left\")\n", "\n", "dump_df[\"category\"] = dump_df[\"category\"].fillna(\"Others\")\n", "\n", "dump_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dump_df[\"date_\"] = pd.to_datetime(dump_df[\"date_\"])\n", "\n", "dump_df = dump_df.merge(rtv_df, on=[\"item_id\", \"city\", \"date_\"], how=\"left\")\n", "dump_df[[\"claim_percent\", \"rtv_flag\"]] = dump_df[[\"claim_percent\", \"rtv_flag\"]].fillna(0)\n", "dump_df[\"dump_value_excl_rtv\"] = dump_df[\"dump_value\"] - (\n", "    dump_df[\"dump_value\"] * dump_df[\"claim_percent\"] / 100\n", ")\n", "dump_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["blinkit_dump_df = dump_df.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["blinkit_dump_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["agg = (\n", "    blinkit_dump_df.groupby([\"date_\", \"category\"])\n", "    .agg({\"dump_value\": \"sum\", \"dump_value_excl_rtv\": \"sum\"})\n", "    .reset_index()\n", ")\n", "agg.head(20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hp_dump_df = f\"\"\"\n", "    with perishable_skus_base as \n", "(SELECT item_id, \n", "       item_name, \n", "       l2_id\n", "FROM (SELECT DISTINCT \n", "           ma.item_id, \n", "           icd.name AS item_name,\n", "           l2_id\n", "    FROM \n", "        rpc.product_product ma\n", "    JOIN (\n", "        SELECT item_id, MAX(id) AS id\n", "        FROM rpc.product_product\n", "        WHERE active = 1 AND approved = 1\n", "        GROUP BY item_id\n", "        \n", "    ) b ON ma.item_id = b.item_id\n", "    JOIN rpc.item_category_details icd ON icd.item_id = ma.item_id\n", "    WHERE l2_id IN (116, 1425, 198, 31, 1097, 1956, 1389, 1778, 1094, 1093, 1091, 138, 949, 950, 2633, 63, 1367, 1369, 1185) \n", ") \n", "),\n", "union_ as (\n", "            \n", "select\n", "    item_id,\n", "    item_name,\n", "    l2_id\n", "from\n", "    perishable_skus_base),\n", "\n", "item_ptype as (\n", "select\n", "    item_id,\n", "    l2_id\n", "from\n", "    union_\n", "),\n", "\n", "base as (\n", "select w.* from zomato.hyperpure_etls.hp_cdc_wastage as w \n", "where lower(w.warehouse_code) like '%%cpc%%'\n", "\n", "and w.warehouse_code not like '%%TEST%%'\n", ")\n", "\n", ", final as (\n", "select k.dt as date_, \n", "ipom.outlet_id as outlet_id, \n", "cl.name as city_name, \n", "\n", "co.name as outlet_name,\n", "pi.item_id,\n", "sum(auto_expiry_rtv+auto_expiry_non_rtv) as expiry, \n", "sum(shrinkage) as weight_loss, \n", "sum(damage) as damage, \n", "sum(wh_pilferage+normal_bin_pilferage) as pilferage,\n", "sum(auto_expiry_rtv_units+auto_expiry_non_rtv_units) as expiry_units, \n", "sum(shrinkage_units) as weight_loss_units, \n", "sum(damage_units) as damage_units, \n", "sum(wh_pilferage_units+normal_bin_pilferage_units) as pilferage_units\n", "from base as k           \n", "INNER JOIN po.edi_integration_partner_outlet_mapping ipom ON lower(ipom.partner_outlet_id) = lower(k.warehouse_code) AND ipom.active\n", "INNER JOIN po.edi_integration_partner_item_mapping pi ON CAST(pi.partner_item_id as int) = k.product_number\n", "INNER JOIN  item_ptype icd on icd.item_id = pi.item_id\n", "INNER JOIN retail.console_outlet co ON co.id = ipom.outlet_id and co.lake_active_record and co.active = 1\n", "LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id and cl.lake_active_record\n", "group by 1,2,3,4,5\n", ")\n", "\n", "\n", "select \n", "date_, city_name as city,  item_id ,\n", "sum(expiry+weight_loss+damage+pilferage) as dump_value\n", "from final\n", "where date_ IN ({','.join([f\"DATE '{date}'\" for date in dates])}) \n", "group by 1,2, 3\n", "    \"\"\"\n", "dump_df_hp = pd.read_sql_query(hp_dump_df, trino)\n", "dump_df_hp.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dump_df_hp = dump_df_hp.merge(item_category_df, on=[\"item_id\"], how=\"left\")\n", "\n", "dump_df_hp[\"category\"] = dump_df_hp[\"category\"].fillna(\"Others\")\n", "\n", "dump_df_hp.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dump_df_hp[\"dump_value\"] = dump_df_hp[\"dump_value\"].astype(int)\n", "dump_df[\"dump_value\"] = dump_df[\"dump_value\"].astype(int)\n", "dump_df[\"date_\"] = pd.to_datetime(dump_df[\"date_\"])\n", "dump_df_hp[\"date_\"] = pd.to_datetime(dump_df_hp[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dump_df_hp[\"dump_value\"] = np.where(dump_df_hp[\"dump_value\"] < 0, abs(dump_df_hp[\"dump_value\"]), 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dump_df_hp.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dump_df_hp[\"date_\"] = pd.to_datetime(dump_df_hp[\"date_\"])\n", "\n", "\n", "dump_df_hp = dump_df_hp.merge(rtv_df, on=[\"item_id\", \"city\", \"date_\"], how=\"left\")\n", "dump_df_hp[[\"claim_percent\", \"rtv_flag\"]] = dump_df_hp[[\"claim_percent\", \"rtv_flag\"]].fillna(0)\n", "dump_df_hp[\"dump_value_excl_rtv\"] = dump_df_hp[\"dump_value\"] - (\n", "    dump_df_hp[\"dump_value\"] * dump_df_hp[\"claim_percent\"] / 100\n", ")\n", "dump_df_hp.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["agg = (\n", "    dump_df_hp.groupby([\"date_\", \"category\"])\n", "    .agg({\"dump_value\": \"sum\", \"dump_value_excl_rtv\": \"sum\"})\n", "    .reset_index()\n", ")\n", "agg.head(20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dump_total_df = pd.concat([dump_df_hp, dump_df])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dump_total_df = (\n", "    dump_total_df.groupby([\"date_\", \"city\", \"item_id\", \"category\"])\n", "    .agg({\"dump_value\": \"sum\", \"dump_value_excl_rtv\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "dump_total_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["agg = (\n", "    dump_total_df.groupby([\"date_\", \"category\"])\n", "    .agg({\"dump_value\": \"sum\", \"dump_value_excl_rtv\": \"sum\"})\n", "    .reset_index()\n", ")\n", "agg.head(20)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Category Wise Dump"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["blinkit_dump_category_df = (\n", "    blinkit_dump_df.groupby([\"date_\", \"category\"])\n", "    .agg({\"dump_value\": \"sum\", \"dump_value_excl_rtv\": \"sum\"})\n", "    .rename(\n", "        columns={\n", "            \"dump_value\": \"frontend_dump_value\",\n", "            \"dump_value_excl_rtv\": \"frontend_dump_value_excl_rtv\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "blinkit_dump_category_df[\"date_\"] = pd.to_datetime(blinkit_dump_category_df[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hp_dump_category_df = (\n", "    dump_df_hp.groupby([\"date_\", \"category\"])\n", "    .agg({\"dump_value\": \"sum\", \"dump_value_excl_rtv\": \"sum\"})\n", "    .rename(\n", "        columns={\n", "            \"dump_value\": \"backend_dump_value\",\n", "            \"dump_value_excl_rtv\": \"backend_dump_value_excl_rtv\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "hp_dump_category_df[\"date_\"] = pd.to_datetime(hp_dump_category_df[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_dump_category_df = (\n", "    dump_total_df.groupby([\"date_\", \"category\"])\n", "    .agg({\"dump_value\": \"sum\", \"dump_value_excl_rtv\": \"sum\"})\n", "    .rename(\n", "        columns={\n", "            \"dump_value\": \"total_dump_value\",\n", "            \"dump_value_excl_rtv\": \"total_dump_value_excl_rtv\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "total_dump_category_df.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Overall Dump"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["blinkit_dump_overall_df = (\n", "    blinkit_dump_df.groupby([\"date_\"])\n", "    .agg({\"dump_value\": \"sum\", \"dump_value_excl_rtv\": \"sum\"})\n", "    .rename(\n", "        columns={\n", "            \"dump_value\": \"frontend_dump_value\",\n", "            \"dump_value_excl_rtv\": \"frontend_dump_value_excl_rtv\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "blinkit_dump_overall_df[\"date_\"] = pd.to_datetime(blinkit_dump_overall_df[\"date_\"])\n", "\n", "blinkit_dump_overall_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["blinkit_dump_overall_df.head(15)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hp_dump_overall_df = (\n", "    dump_df_hp.groupby([\"date_\"])\n", "    .agg({\"dump_value\": \"sum\", \"dump_value_excl_rtv\": \"sum\"})\n", "    .rename(\n", "        columns={\n", "            \"dump_value\": \"backend_dump_value\",\n", "            \"dump_value_excl_rtv\": \"backend_dump_value_excl_rtv\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "hp_dump_overall_df[\"date_\"] = pd.to_datetime(hp_dump_overall_df[\"date_\"])\n", "\n", "hp_dump_overall_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_dump_overall_df = (\n", "    dump_total_df.groupby([\"date_\"])\n", "    .agg({\"dump_value\": \"sum\", \"dump_value_excl_rtv\": \"sum\"})\n", "    .rename(\n", "        columns={\n", "            \"dump_value\": \"total_dump_value\",\n", "            \"dump_value_excl_rtv\": \"total_dump_value_excl_rtv\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "total_dump_overall_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_dump_overall_df.head(10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Customer Dump"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["customer_query = f\"\"\"\n", "    WITH dump AS (\n", "        SELECT DATE(pos_timestamp + interval '330' minute) AS date_, outlet_id, item_id, SUM(il.\"delta\") AS dump_quantity, SUM(il.\"delta\" * il.weighted_lp) AS dump_value\n", "        FROM ims.ims_inventory_log il\n", "        JOIN (\n", "            SELECT DISTINCT item_id, variant_id\n", "            FROM rpc.product_product\n", "        ) rpp ON rpp.variant_id = il.variant_id\n", "        WHERE  insert_ds_ist > CAST(current_date - interval '10' day AS varchar)\n", "        AND inventory_update_type_id IN (7,9,33,34,63,67)\n", "        GROUP BY 1,2,3\n", "    ),\n", "\n", "    pre_summary AS (    \n", "        SELECT date_, facility_id, item_id, dump_quantity, dump_value, \n", "        CASE \n", "            WHEN co.business_type_id = 7 THEN 'fe' \n", "            ELSE 'be' \n", "        END AS flag\n", "        FROM dump a\n", "        JOIN retail.console_outlet co ON co.id = outlet_id AND co.active = 1\n", "        WHERE item_id IN {item_list} \n", "        AND date_ IN ({','.join([f\"DATE '{date}'\" for date in dates])})\n", "    )\n", "    \n", "    SELECT date_, flag, item_id, SUM(dump_value) AS customer_dump_value\n", "    FROM pre_summary\n", "    GROUP BY 1,2,3\n", "    \"\"\"\n", "customer_dump_df = pd.read_sql_query(customer_query, trino)\n", "\n", "customer_dump_df[\"customer_dump_value\"] = customer_dump_df[\"customer_dump_value\"].astype(float)\n", "\n", "customer_dump_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["customer_dump_df = customer_dump_df.merge(item_category_df, on=[\"item_id\"], how=\"left\")\n", "\n", "customer_dump_df[\"category\"] = customer_dump_df[\"category\"].fillna(\"Others\")\n", "\n", "customer_dump_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["customer_category_df = (\n", "    customer_dump_df.groupby([\"date_\", \"category\"])\n", "    .agg({\"customer_dump_value\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "customer_category_df[\"date_\"] = pd.to_datetime(customer_category_df[\"date_\"])\n", "\n", "customer_category_df.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Transfer Losses"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["### Non Hyperpure"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_query = f\"\"\"\n", "    WITH base AS (\n", "        SELECT DATE(invoice_billed_date_ist) AS date_, EXTRACT(week FROM invoice_billed_date_ist) AS week, sender_outlet_id AS be_outlet_id, receiver_outlet_id AS fe_outlet_id, \n", "        CASE \n", "            WHEN l2_id IN (1425) THEN 'Batter'\n", "            WHEN l2_id IN (31,116,198,1097,1956,2633) THEN 'Breads'\n", "            WHEN l2_id IN (949) THEN 'Curd'\n", "            WHEN l2_id IN (1389,1778) THEN 'Eggs'\n", "            WHEN l2_id IN (63,1367,1369) THEN 'Meats'\n", "            WHEN l2_id IN (1185) THEN 'Milk'\n", "            WHEN l2_id IN (950) THEN 'Paneer'\n", "            WHEN l2_id IN (138,1091,1093) THEN 'Perishable Dairy'\n", "            WHEN l2_id IN (1094) THEN 'Yogurt' \n", "            ELSE 'Others' \n", "        END AS category, SUM(dn_short_amt) AS dn_short_amt, \n", "        SUM(dn_other_damage_amt) as dn_damage_amt,\n", "        SUM(dn_other_amt)-SUM(dn_other_damage_amt) AS dn_other_amt\n", "        FROM ba_etls.transfer_loss_v3 ct\n", "        JOIN rpc.item_category_details icd ON icd.item_id = ct.item_id\n", "        JOIN (\n", "            SELECT item_id, MAX(id) AS id \n", "            FROM rpc.product_product \n", "            WHERE active = 1 AND approved = 1 and perishable = 1 \n", "            GROUP BY 1\n", "        ) b ON ct.item_id = b.item_id \n", "        WHERE invoice_billed_date_ist >= current_date - interval '30' day \n", "        GROUP BY 1,2,3,4,5\n", "    )\n", "\n", "    SELECT a.date_,category, SUM(dn_short_amt) AS dn_short_amt, \n", "    sum(dn_damage_amt) as dn_damage_amt, \n", "    SUM(dn_other_amt) AS dn_other_amt\n", "    FROM base a\n", "    WHERE date_ IN ({','.join([f\"DATE '{date}'\" for date in dates])}) AND category NOT IN ('Others')\n", "    GROUP BY 1,2\n", "\"\"\"\n", "nhp_dn_df = pd.read_sql_query(dn_query, trino)\n", "\n", "nhp_dn_df[[\"dn_short_amt\", \"dn_other_amt\", \"dn_damage_amt\"]] = nhp_dn_df[\n", "    [\"dn_short_amt\", \"dn_other_amt\", \"dn_damage_amt\"]\n", "].fillna(0)\n", "\n", "nhp_dn_df[\"transfer_loss\"] = (\n", "    nhp_dn_df[\"dn_short_amt\"] + nhp_dn_df[\"dn_damage_amt\"] + nhp_dn_df[\"dn_other_amt\"]\n", ").round(0)\n", "\n", "nhp_dn_df = nhp_dn_df[[\"date_\", \"category\", \"transfer_loss\", \"dn_short_amt\", \"dn_damage_amt\"]]\n", "\n", "agg_dn_df = (\n", "    nhp_dn_df.groupby([\"date_\"])\n", "    .agg({\"transfer_loss\": \"sum\", \"dn_short_amt\": \"sum\", \"dn_damage_amt\": \"sum\"})\n", "    .reset_index()\n", ")\n", "agg_dn_df[\"category\"] = \"All\"\n", "\n", "agg_dn_df = agg_dn_df[[\"date_\", \"category\", \"transfer_loss\", \"dn_short_amt\", \"dn_damage_amt\"]]\n", "\n", "nhp_transfer_df = pd.concat([nhp_dn_df, agg_dn_df])\n", "\n", "nhp_transfer_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trino = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nhp_transfer_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Hyperpure"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hp_dn_query = f\"\"\"\n", "      with item_base as (SELECT DISTINCT rpc.item_id, icd.name as item_name,     \n", "    Case \n", "      WHEN (l2_id IN (1425) and rpc.perishable = 1) THEN 'Batter'\n", "        WHEN (l2_id IN (31,116,198,1097,1956,2633) and rpc.perishable = 1) THEN 'Breads'\n", "        WHEN (l2_id IN (949) and rpc.perishable = 1) THEN 'Curd'\n", "        WHEN (l2_id IN (1389,1778)and rpc.perishable = 1) THEN 'Eggs'\n", "        WHEN (l2_id IN (63,1367,1369)and rpc.perishable = 1) THEN 'Meats'\n", "        WHEN (l2_id IN (1185)and rpc.perishable = 1) THEN 'Milk'\n", "        WHEN (l2_id IN (950)and rpc.perishable = 1) THEN 'Paneer'\n", "        WHEN (l2_id IN (138,1091,1093)and rpc.perishable = 1) THEN 'Perishable Dairy'\n", "        WHEN (l2_id IN (1094)and rpc.perishable = 1) THEN 'Yogurt'\n", "            else 'Other SKU' end as l2\n", "        from  lake_rpc.product_product rpc \n", "        JOIN (\n", "                SELECT item_id, MAX(id) AS id\n", "                FROM lake_rpc.product_product\n", "                WHERE active = 1 and approved = 1\n", "                GROUP BY 1\n", "            ) b ON rpc.item_id = b.item_id\n", "    --  INNER JOIN rpc.item_outlet_tag_mapping tm\n", "    --  INNER JOIN po.physical_facility_outlet_mapping pfom on pfom.outlet_id = tm.outlet_id and pfom.active = 1 and pfom.is_primary = 1 and pfom.ars_active = 1\n", "    --  JOIN retail.console_outlet co ON co.id = tm.outlet_id AND co.active = 1 AND co.business_type_id = 7 \n", "    --  JOIN retail.console_outlet cb ON cb.id = cast(tag_value as int) AND cb.active = 1 and cb.business_type_id in (19,20,21,1,12)\n", "    --  JOIN retail.console_location cl on cl.id = co.tax_location_id \n", "    --  LEFT JOIN retail.warehouse_outlet_mapping wom on wom.warehouse_id = cast(tm.tag_value as int)\n", "    --  JOIN po.bulk_facility_outlet_mapping bfom on bfom.outlet_id = tm.outlet_id and bfom.active = true\n", "     JOIN rpc.item_category_details icd on icd.item_id = rpc.item_id),\n", "    --  where\n", "    --   tm.active = 1 AND tm.tag_type_id = 8), \n", "      \n", "     perishable_skus as (Select * from item_base where l2 not in ('Other SKU')),\n", "     \n", " hp_wh as (SELECT\n", "    CAST(target_delivery_date + interval '330' minute AS timestamp)  as targetdeliverydate,\n", "    warehouse_code as warehousecode ,\n", "    buyer_order_request_id as id,\n", "    cast(coalesce(order_reference.hp_order_references[1].hp_order_id,0) as bigint) AS orderid,\n", "    status_details[1] AS status\n", "FROM zomato.dynamodb.prod_hp_order_service\n", "where type = 'BLINKIT'),\n", "\n", "po_base as \n", "(select distinct date(o.target_delivery_date+interval'330'minute) as date_,\n", "    ipom.outlet_id as warehouse_id,\n", "    warehouse_code,\n", "    po.po_number as po_number,\n", "    poid.outlet_id as outlet_id,\n", "    pi.item_id as item_id,\n", "    psku.l2,\n", "    psku.item_name,\n", "    o.id,\n", "    SUM(oi.ordered_quantity*oi.weight_per_packet) ordered_tonnage,\n", "    SUM(oi.picked_quantity*oi.weight_per_packet) picked_tonnage,\n", "    sum(oi.picked_quantity *  (asno.cost_price * (1 + ((asno.cgst_value + asno.sgst_value+ asno.igst_value + asno.cess_value + asno.additional_cess_value)/10))) ) as picked_value,\n", "    MAX(oi.ordered_quantity) ordered_quantity,\n", "   MAX(oi.picked_quantity) picked_quantity,\n", "   MAX(units_ordered) as units_ordered,\n", "   MAX(grn.quan) as grn_quantity,\n", "      SUM(units_ordered* (asno.cost_price * (1 + ((asno.cgst_value + asno.sgst_value+ asno.igst_value + asno.cess_value + asno.additional_cess_value)/10))) ) as po_value,\n", "   SUM(grn.quan* (asno.cost_price * (1 + ((asno.cgst_value + asno.sgst_value+ asno.igst_value + asno.cess_value + asno.additional_cess_value)/10))) ) as grn_valuee\n", "from zomato.hp_wms.orders o\n", "    left join zomato.hp_wms.order_item oi\n", "    on o.id=oi.order_id\n", "left join hp_wh bor on bor.orderid = o.id\n", "left join po.edi_integration_partner_purchase_order_details poid on bor.id = poid.partner_order_id\n", "LEFT JOIN po.edi_integration_partner_item_mapping pi ON CAST(pi.partner_item_id as int) = oi.product_number and pi.active = true\n", "left join po.purchase_order_items poi on poid.po_id= poi.po_id and poi.item_id = pi.item_id\n", "left join po.purchase_order po on po.id=poi.po_id\n", "left join (select po_id, item_id,landing_price, sum(quantity) quan, max(created_at+interval '330' minute) as max_grn_time from po.po_grn grn\n", "            where insert_ds_ist >= cast(current_date - interval '20' day as varchar)\n", "            group by 1,2,3) grn\n", "            on  grn.item_id = poi.item_id and grn.po_id = poi.po_id\n", "INNER JOIN po.edi_integration_partner_outlet_mapping ipom ON lower(ipom.partner_outlet_id) = lower(o.warehouse_code) AND ipom.active\n", "Inner join perishable_skus psku on psku.item_id = poi.item_id\n", " left join (select po_number,asn_id, max(updated_at) from po.edi_integration_partner_po_invoice_mapping group by 1,2) invo  on invo.po_number = po.po_number\n", "left join po.edi_integration_partner_invoice_item_details asno on asno.asn_id = invo.asn_id and asno.item_id =poi.item_id and asno.insert_ds_ist >= cast(current_date - interval '10' day as varchar)\n", "where o.dt>'2022111'\n", "    and oi.dt>'2022111'\n", "and date(o.target_delivery_date+interval'330'minute) >= current_date - interval '20' day\n", "    and lower(o.order_status)!='cancelled'\n", "    and o.order_tag!='DIRECT_DELIVERY'\n", " group by 1,2,3,4,5,6,7,8,9),\n", " \n", "dn_base as (\n", "select \n", "    d1.po_number, \n", "    ppp.item_id, \n", "    Case when c.name in ('Damaged') then 'Damage' \n", "    when c.name in ( 'STO Short Quantity (Source)','STO Missing (Transport)',\n", "    'Item not in ESTO','Rcpt Qty < Invoice Qty','STO Short Quantity (Transport)','STO Missing (Source)') then 'Short'\n", "    when c.name in ('Shelf Life guidelines breached') then 'NTE'\n", "    else 'Others' end as reason, \n", "    MAX(b.quantity) as dn_quantity,\n", "    sum(b.quantity* (asno.cost_price * (1 + ((asno.cgst_value + asno.sgst_value+ asno.igst_value + asno.cess_value + asno.additional_cess_value)/10))) ) as dn_value\n", "from\n", "    pos.discrepancy_note d1 \n", "left join \n", "    pos.discrepancy_note_product_detail b on d1.id=b.dn_id_id \n", "inner join \n", "    (select distinct item_id, variant_id from rpc.product_product \n", "where item_id in (Select distinct item_id from perishable_skus)) ppp on b.variant_id  = ppp.variant_id\n", "left join \n", "    pos.discrepancy_reason c on b.reason_code=c.id\n", "left join \n", "    po.purchase_order po on po.po_number= d1.po_number\n", "left join \n", "    po.purchase_order_items poi on po.id= poi.po_id and poi.item_id = ppp.item_id\n", "left join \n", "    (select po_number,asn_id,  max(updated_at) from po.edi_integration_partner_po_invoice_mapping group by 1,2) invo  on invo.po_number = po.po_number\n", "left join \n", "    po.edi_integration_partner_invoice_item_details asno on asno.asn_id = invo.asn_id and asno.item_id =poi.item_id \n", "    and asno.insert_ds_ist >= cast(current_date - interval '20' day as varchar)\n", "\n", "where d1.po_number in (select distinct po_number from po_base) group by 1,2,3),\n", "\n", "\n", "\n", "reinventorisation_base as \n", "(with base as (\n", "                select outlet_id, warehouse_code,po_number,item_id,tdd,\n", "                sum(dispatched_value) dispatched_value,\n", "                sum(issue_value) as issue_value,\n", "                sum(quantity_dispatched) as quantity_dispatched,\n", "                sum(dispatched_value) as dispatched_value,\n", "                sum(warehouse_recieved_value) as warehouse_recieved_value,\n", "                sum(warehouse_reusable_value) as warehouse_reusable_value,\n", "                sum(issue_units) as issue_units,\n", "                sum(warehouse_recieved_qty) as warehouse_recieved_qty, \n", "                sum(warehouse_usable_qty) as warehouse_usable_qty\n", "                from\n", "                (select outlet_id, warehouse_code, po_number,item_id,tdd, quantity_dispatched, issue_units,dispatched_value, issue_value\n", "                , case when warehouse_recieved_qty>quantity_dispatched then quantity_dispatched else warehouse_recieved_qty end as warehouse_recieved_qty\n", "                , case when warehouse_usable_qty>quantity_dispatched then quantity_dispatched else warehouse_usable_qty end as warehouse_usable_qty\n", "                , case when warehouse_recieved_value>dispatched_value then dispatched_value else warehouse_recieved_value end as warehouse_recieved_value\n", "                , case when warehouse_reusable_value>dispatched_value then dispatched_value else warehouse_reusable_value end as warehouse_reusable_value\n", "                from\n", "                (\n", "                select ipom.outlet_id, oo.warehouse_code,po.po_number as po_number,pim.item_id as item_id,\n", "                date(o.target_delivery_date+interval'330'minute) as tdd, \n", "                max(op.quantity_dispatched) as quantity_dispatched,\n", "                sum(op.quantity_dispatched * (asno.cost_price * (1 + ((asno.cgst_value + asno.sgst_value+ asno.igst_value + asno.cess_value + asno.additional_cess_value)/10))) ) as dispatched_value,\n", "                MAX(pi.issue_in_quantity/op.sub_uom_count) as issue_units,\n", "                sum(((pi.issue_in_quantity/op.sub_uom_count)*asno.cost_price * (1 + ((asno.cgst_value + asno.sgst_value+ asno.igst_value + asno.cess_value + asno.additional_cess_value)/10))) ) as issue_value,\n", "                MAX(pi.warehouse_received_qty) as warehouse_recieved_qty, \n", "                MAX(pi.warehouse_reusable_qty) as warehouse_usable_qty,\n", "                sum(pi.warehouse_received_qty* (asno.cost_price * (1 + ((asno.cgst_value + asno.sgst_value+ asno.igst_value + asno.cess_value + asno.additional_cess_value)/10))) ) as warehouse_recieved_value,\n", "                sum(pi.warehouse_reusable_qty* (asno.cost_price * (1 + ((asno.cgst_value + asno.sgst_value+ asno.igst_value + asno.cess_value + asno.additional_cess_value)/10))) ) as warehouse_reusable_value\n", "                from zomato.hp_pod.orders o\n", "                left join zomato.hp_pod.order_product op on o.id=op.order_id\n", "                LEFT JOIN zomato.hp_wms.orders oo on o.number = oo.order_number\n", "                left join zomato.hp_pod.product_issue pi on op.id=pi.order_product_id\n", "                --left join zomato.hp_wms.order_item oi on oo.id=oi.order_id\n", "                left join zomato.hp_consumer.buyer_order_requests bor on bor.orderid = oo.id\n", "                inner join po.edi_integration_partner_purchase_order_details poid on bor.id = poid.partner_order_id\n", "                inner JOIN po.edi_integration_partner_item_mapping pim ON CAST(pim.partner_item_id as int) = op.product_number and pim.active = true\n", "                LEFT JOIN po.edi_integration_partner_outlet_mapping ipom ON lower(ipom.partner_outlet_id) = lower(oo.warehouse_code) AND ipom.active\n", "                left join po.purchase_order_items poi on poid.po_id= poi.po_id and poi.item_id = pim.item_id\n", "                left join po.purchase_order po on po.id=poi.po_id\n", "                 left join (select po_number,asn_id, max(updated_at) from po.edi_integration_partner_po_invoice_mapping group by 1,2) invo  on invo.po_number = po.po_number\n", "                left join po.edi_integration_partner_invoice_item_details asno on asno.asn_id = invo.asn_id and asno.item_id =poi.item_id and asno.insert_ds_ist >= cast(current_date - interval '20' day as varchar)\n", "                \n", "                where o.dt >= date_format(date_add('day',- 1,current_timestamp),'%%Y%%m%%d')\n", "                and pi.dt >= date_format(date_add('day',- 1,current_timestamp),'%%Y%%m%%d') \n", "                and o.target_delivery_date > current_date - interval '20' day\n", "                and oo.warehouse_code like 'CPC%%'\n", "                and oo.dt>'2022011'\n", "                and pi.status in ('approved','refunded')\n", "                group by 1,2,3,4,5\n", "                ))\n", "                group by 1,2,3,4,5)\n", "\n", "\n", "                select tdd as date_, \n", "                outlet_id, \n", "                warehouse_code,\n", "                po_number,\n", "                item_id, \n", "                warehouse_usable_qty as warehouse_usable_qty, \n", "                (issue_units-warehouse_usable_qty) as warehouse_non_usable_qty, \n", "                warehouse_reusable_value as warehouse_reusable_value,\n", "                (issue_value-warehouse_reusable_value) as warehouse_non_usable_value \n", "                from base b\n", "        ),\n", "        \n", "        \n", "\n", "final_join_pre as \n", "(select \n", "    p2.*,\n", "    coalesce(dn_quantity,0) as discrepancy_quantity,\n", "    coalesce(dn_value,0) as dn_value,\n", "    reason, \n", "    warehouse_reusable_value, \n", "    warehouse_non_usable_value\n", "from \n", "    po_base p2\n", "left join \n", "    dn_base dn \n", "    on dn.item_id = p2.item_id \n", "    and p2.po_number = dn.po_number\n", "left join \n", "    reinventorisation_base rb \n", "    on rb.item_id = p2.item_id \n", "    and rb.po_number = p2.po_number\n", "    and rb.outlet_id = p2.outlet_id\n", "    ),\n", "\n", "\n", "final_last as \n", "(select \n", "    date_ as date_, \n", "    warehouse_id,\n", "    warehouse_code, \n", "    outlet_id,\n", "    item_id,\n", "    l2,\n", "    sum(dn_value) as total_dn,\n", "    sum(case when reason = 'NTE' then dn_value end) as dn_nte,\n", "    sum(case when reason = 'Damage' then dn_value end) as dn_damage,\n", "    sum(case when reason = 'Others' then dn_value end) as dn_misc,\n", "    sum(case when reason = 'Short' then dn_value end) as dn_short,\n", "    -- sum(warehouse_non_usable_qty) as warehouse_non_usable_qty,\n", "    -- sum(warehouse_usable_qty) as warehouse_usable_qty,\n", "    sum(warehouse_reusable_value)  as warehouse_usable_value,\n", "    sum(warehouse_non_usable_value) as warehouse_non_usable_value\n", "from final_join_pre\n", "where date_ >=current_date - interval '20' day\n", "group by 1,2,3,4,5,6\n", ")\n", "\n", "select \n", "date(a.date_) as date_,\n", "cl.name as city, \n", "a.item_id,\n", "a.l2 as category ,\n", "sum(a.total_dn) as transfer_loss,\n", "sum(a.dn_short) as dn_short,\n", "sum(a.dn_damage) as dn_damage\n", "from final_last as a \n", "LEFT JOIN retail.console_outlet co ON co.id = a.outlet_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id AND cl.lake_active_record\n", "where date_ in ({','.join([f\"DATE '{date}'\" for date in dates])})\n", "group by 1,2,3,4\n", "\"\"\"\n", "\n", "hp_dn_df = pd.read_sql_query(hp_dn_query, trino)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hp_dn_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hp_dn_df[[\"transfer_loss\", \"dn_short\", \"dn_damage\"]] = hp_dn_df[\n", "    [\"transfer_loss\", \"dn_short\", \"dn_damage\"]\n", "].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hp_dn_df[\"date_\"] = pd.to_datetime(hp_dn_df[\"date_\"])\n", "rtv_df[\"date_\"] = pd.to_datetime(rtv_df[\"date_\"])\n", "\n", "\n", "hp_dn_df = hp_dn_df.merge(rtv_df, on=[\"item_id\", \"city\", \"date_\"], how=\"left\")\n", "hp_dn_df[[\"claim_percent\", \"rtv_flag\"]] = hp_dn_df[[\"claim_percent\", \"rtv_flag\"]].fillna(0)\n", "\n", "hp_dn_df[\"transfer_loss_excl_rtv\"] = hp_dn_df[\"transfer_loss\"] - (\n", "    hp_dn_df[\"transfer_loss\"] * hp_dn_df[\"claim_percent\"] / 100\n", ")\n", "\n", "hp_dn_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hp_dn_df = (\n", "    hp_dn_df.groupby([\"date_\", \"category\"])\n", "    .agg(\n", "        {\n", "            \"transfer_loss\": \"sum\",\n", "            \"transfer_loss_excl_rtv\": \"sum\",\n", "            \"dn_short\": \"sum\",\n", "            \"dn_damage\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hp_dn_df = hp_dn_df[\n", "    [\"date_\", \"category\", \"transfer_loss\", \"transfer_loss_excl_rtv\", \"dn_short\", \"dn_damage\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["agg_dn_df = (\n", "    hp_dn_df.groupby([\"date_\"])\n", "    .agg(\n", "        {\n", "            \"transfer_loss\": \"sum\",\n", "            \"transfer_loss_excl_rtv\": \"sum\",\n", "            \"dn_short\": \"sum\",\n", "            \"dn_damage\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "agg_dn_df[\"category\"] = \"All\"\n", "\n", "agg_dn_df = agg_dn_df[\n", "    [\"date_\", \"category\", \"transfer_loss\", \"transfer_loss_excl_rtv\", \"dn_short\", \"dn_damage\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["agg_dn_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hp_dn_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hp_transfer_df = pd.concat([hp_dn_df, agg_dn_df])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hp_transfer_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hp_transfer_df[\n", "    [\"transfer_loss\", \"transfer_loss_excl_rtv\", \"dn_short\", \"dn_damage\"]\n", "] = hp_transfer_df[[\"transfer_loss\", \"transfer_loss_excl_rtv\", \"dn_short\", \"dn_damage\"]].round(0)\n", "hp_transfer_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hp_transfer_df.head(10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Categorywise Daily Transfer Losses"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# nhp_transfer_df[\"date_\"] = pd.to_datetime(nhp_transfer_df[\"date_\"])\n", "hp_transfer_df[\"date_\"] = pd.to_datetime(hp_transfer_df[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hp_transfer_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# transfer_loss_df = pd.merge(\n", "#     hp_transfer_df.rename(\n", "#         columns={\n", "#             \"transfer_loss\": \"hp_transfer_loss\",\n", "#             \"dn_short\": \"hp_dn_short\",\n", "#             \"dn_damage\": \"hp_dn_damage\",\n", "#         }\n", "#     ),\n", "#     nhp_transfer_df,\n", "#     on=[\"date_\", \"category\"],\n", "#     how=\"left\",\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["transfer_loss_df = hp_transfer_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["transfer_loss_df.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["transfer_loss_df = transfer_loss_df.rename(\n", "    columns={\n", "        \"transfer_loss\": \"transfer_loss_value\",\n", "        \"transfer_loss_excl_rtv\": \"transfer_loss_value_excl_rtv\",\n", "    }\n", ")\n", "\n", "\n", "transfer_loss_df[\n", "    [\"transfer_loss_value\", \"transfer_loss_value_excl_rtv\", \"dn_short\", \"dn_damage\"]\n", "] = transfer_loss_df[\n", "    [\"transfer_loss_value\", \"transfer_loss_value_excl_rtv\", \"dn_short\", \"dn_damage\"]\n", "].fillna(\n", "    0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# transfer_loss_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# transfer_loss_df[\"transfer_loss_value\"] = (\n", "#     transfer_loss_df[\"transfer_loss\"] + transfer_loss_df[\"hp_transfer_loss\"]\n", "# )\n", "\n", "\n", "# transfer_loss_df[\"dn_short\"] = transfer_loss_df[\"hp_dn_short\"] + transfer_loss_df[\"dn_short_amt\"]\n", "\n", "# transfer_loss_df[\"dn_short\"] = transfer_loss_df[\"hp_dn_damage\"] + transfer_loss_df[\"dn_damage_amt\"]\n", "\n", "# transfer_loss_df = (\n", "#     transfer_loss_df[[\"date_\", \"category\", \"transfer_loss_value\", \"dn_short\", \"dn_damage\"]]\n", "#     .drop_duplicates()\n", "#     .reset_index()\n", "#     .drop(columns={\"index\"})\n", "# )\n", "\n", "# transfer_loss_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# transfer_loss_df.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Calculations - Day Wise View"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Overall Total"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_df = (\n", "    sales_pre_df_1_1.groupby([\"date_\"])\n", "    .agg(\n", "        {\n", "            \"total_sales\": \"sum\",\n", "            \"total_gmv\": \"sum\",\n", "            \"total_carts\": \"sum\",\n", "            \"total_retained_margin\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "# overall_df = overall_df.rename(\n", "#     columns={\"sales\": \"total_sales\", \"gmv\": \"total_gmv\", \"ancestor\": \"total_carts\"}\n", "# )\n", "\n", "overall_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_df[\"date_\"] = pd.to_datetime(overall_df[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Perishable Total"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["perishable_df = (\n", "    sales_pre_df_1[sales_pre_df_1[\"perishable_flag\"] == \"Perishable\"]\n", "    .groupby([\"date_\"])\n", "    .agg(\n", "        {\n", "            \"total_sales\": \"sum\",\n", "            \"total_gmv\": \"sum\",\n", "            \"total_carts\": \"sum\",\n", "            \"total_retained_margin\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "perishable_df = perishable_df.rename(\n", "    columns={\n", "        \"total_sales\": \"sales\",\n", "        \"total_gmv\": \"gmv\",\n", "        \"total_carts\": \"carts\",\n", "        \"total_retained_margin\": \"retained_margin\",\n", "    }\n", ")\n", "\n", "perishable_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["perishable_df[\"date_\"] = pd.to_datetime(perishable_df[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["perishable_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "#### Carts Split"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# tot_carts_df = (\n", "#     sales_df.groupby([\"date_\", \"ancestor\"])\n", "#     .agg({\"item_id\": \"nunique\"})\n", "#     .reset_index()\n", "#     .rename(columns={\"item_id\": \"total_items\"})\n", "# )\n", "\n", "# per_carts_df = (\n", "#     sales_df[sales_df[\"perishable\"] == 1]\n", "#     .groupby([\"date_\", \"ancestor\"])\n", "#     .agg({\"item_id\": \"nunique\"})\n", "#     .reset_index()\n", "#     .rename(columns={\"item_id\": \"perishable_items\"})\n", "# )\n", "\n", "# carts_df = pd.merge(tot_carts_df, per_carts_df, on=[\"date_\", \"ancestor\"], how=\"left\")\n", "\n", "# carts_df[\"perishable_items\"] = carts_df[\"perishable_items\"].fillna(0)\n", "\n", "# carts_df[\"pure_flag\"] = np.where(carts_df[\"total_items\"] == carts_df[\"perishable_items\"], 1, 0)\n", "\n", "# pure_carts_df = (\n", "#     carts_df[carts_df[\"pure_flag\"] == 1]\n", "#     .groupby([\"date_\"])\n", "#     .agg({\"pure_flag\": \"sum\"})\n", "#     .reset_index()\n", "#     .rename(columns={\"pure_flag\": \"pure_carts\"})\n", "# )\n", "\n", "# pure_carts_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_pre_df_2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pure_carts_df = sales_pre_df_2.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pure_carts_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pure_carts_df.dtypes"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Merge Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_df = pd.merge(overall_df, perishable_df, on=[\"date_\"], how=\"left\")\n", "\n", "calc_df = pd.merge(calc_df, pure_carts_df, on=[\"date_\"], how=\"left\")\n", "\n", "calc_df = pd.merge(calc_df, dau_df, on=[\"date_\"], how=\"left\")\n", "\n", "calc_df = pd.merge(calc_df, qng_df, on=[\"date_\"], how=\"left\")\n", "\n", "calc_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_df[\"date_\"] = calc_df[\"date_\"].astype(str)\n", "\n", "total_dump_overall_df[\"date_\"] = total_dump_overall_df[\"date_\"].astype(str)\n", "\n", "blinkit_dump_overall_df[\"date_\"] = blinkit_dump_overall_df[\"date_\"].astype(str)\n", "\n", "hp_dump_overall_df[\"date_\"] = hp_dump_overall_df[\"date_\"].astype(str)\n", "\n", "transfer_loss_df[\"date_\"] = transfer_loss_df[\"date_\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_df = pd.merge(calc_df, blinkit_dump_overall_df, on=[\"date_\"], how=\"left\")\n", "\n", "calc_df = pd.merge(calc_df, hp_dump_overall_df, on=[\"date_\"], how=\"left\")\n", "\n", "calc_df = pd.merge(calc_df, total_dump_overall_df, on=[\"date_\"], how=\"left\")\n", "\n", "calc_df = pd.merge(\n", "    calc_df,\n", "    transfer_loss_df[transfer_loss_df[\"category\"] == \"All\"].drop(columns={\"category\"}),\n", "    on=[\"date_\"],\n", "    how=\"left\",\n", ")\n", "\n", "calc_df[\"total_dump_value\"] = (\n", "    calc_df[\"frontend_dump_value\"] + calc_df[\"backend_dump_value\"] + calc_df[\"transfer_loss_value\"]\n", ")\n", "\n", "calc_df[\"total_dump_value_excl_rtv\"] = (\n", "    calc_df[\"frontend_dump_value_excl_rtv\"]\n", "    + calc_df[\"backend_dump_value_excl_rtv\"]\n", "    + calc_df[\"transfer_loss_value_excl_rtv\"]\n", ")\n", "\n", "calc_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_availability_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_df[\"date_\"] = pd.to_datetime(calc_df[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_df = pd.merge(calc_df, overall_availability_df, on=[\"date_\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_df[\"mixed_carts\"] = calc_df[\"carts\"] - calc_df[\"pure_carts\"]\n", "calc_df.loc[:, \"perc_gmv\"] = calc_df[\"gmv\"] / calc_df[\"total_gmv\"]\n", "calc_df[\"cart_pen\"] = (calc_df[\"carts\"] / calc_df[\"total_carts\"]).round(4)\n", "calc_df[\"dau_pen\"] = (calc_df[\"carts\"] / calc_df[\"dau\"]).round(4)\n", "calc_df[\"gmv_per_dau\"] = (calc_df[\"gmv\"] / calc_df[\"dau\"]).round(4)\n", "calc_df[\"aov\"] = (calc_df[\"gmv\"] / calc_df[\"total_carts\"]).round(4)\n", "calc_df[\"pricing_margin\"] = (calc_df[\"retained_margin\"] / calc_df[\"gmv\"]).round(4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# calc_df[\"mixed_carts\"] = calc_df[\"carts\"] - calc_df[\"pure_carts\"]\n", "# calc_df[\"perc_gmv\"] = (calc_df[\"gmv\"] / calc_df[\"total_gmv\"]).round(4)\n", "# calc_df[\"cart_pen\"] = (calc_df[\"carts\"] / calc_df[\"total_carts\"]).round(4)\n", "# calc_df[\"dau_pen\"] = (calc_df[\"carts\"] / calc_df[\"dau\"]).round(4)\n", "# calc_df[\"gmv_per_dau\"] = (calc_df[\"gmv\"] / calc_df[\"dau\"]).round(4)\n", "# calc_df[\"aov\"] = (calc_df[\"gmv\"] / calc_df[\"total_carts\"]).round(4)\n", "# calc_df[\"pricing_margin\"] = (calc_df[\"retained_margin\"] / calc_df[\"gmv\"]).round(4)\n", "\n", "\n", "calc_df[\"blinkit_dump_perc\"] = (calc_df[\"frontend_dump_value\"] / calc_df[\"gmv\"]).round(4)\n", "calc_df[\"blinkit_dump_perc_excl_rtv\"] = (\n", "    calc_df[\"frontend_dump_value_excl_rtv\"] / calc_df[\"gmv\"]\n", ").round(4)\n", "\n", "calc_df[\"hp_dump_perc\"] = (calc_df[\"backend_dump_value\"] / calc_df[\"gmv\"]).round(4)\n", "calc_df[\"hp_dump_perc_excl_rtv\"] = (calc_df[\"backend_dump_value_excl_rtv\"] / calc_df[\"gmv\"]).round(\n", "    4\n", ")\n", "\n", "\n", "calc_df[\"total_dump_perc\"] = (calc_df[\"total_dump_value\"] / calc_df[\"gmv\"]).round(4)\n", "calc_df[\"total_dump_perc_excl_rtv\"] = (calc_df[\"total_dump_value_excl_rtv\"] / calc_df[\"gmv\"]).round(\n", "    4\n", ")\n", "\n", "\n", "calc_df[\"perc_qng\"] = (calc_df[\"qng\"] / calc_df[\"carts\"]).round(4)\n", "calc_df[\"wt_availability\"] = (calc_df[\"wt_availability\"]).round(4)\n", "\n", "calc_df[\"sales\"] = calc_df[\"sales\"].fillna(0).astype(int)\n", "\n", "calc_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_df.head(10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Calculations - Category Wise"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cat_sales = sales_pre_df_0[\n", "    (sales_pre_df_0[\"perishable_flag\"] == \"Perishable\")\n", "    & (~(sales_pre_df_0[\"category\"].isin({\"Others\", \"FnV\"})))\n", "]\n", "cat_sales.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_1_df = (\n", "    cat_sales[(pd.to_datetime(cat_sales[\"date_\"]) == t1)]\n", "    .groupby([\"date_\", \"category\"])\n", "    .agg({\"total_gmv\": \"sum\", \"total_sales\": \"sum\"})\n", "    .reset_index()\n", ")\n", "calc_1_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_1_today = calc_df[(pd.to_datetime(calc_df[\"date_\"]) == t1)]\n", "calc_1_today.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_1_today.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_1_today = calc_1_today[[\"date_\", \"total_carts\"]]\n", "calc_1_today[\"date_\"] = calc_1_today[\"date_\"].astype(str)\n", "calc_1_df[\"date_\"] = calc_1_df[\"date_\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_1_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_1_df = pd.merge(calc_1_df, calc_1_today, on=[\"date_\"], how=\"left\")\n", "calc_1_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_dump_category_df[\"date_\"] = total_dump_category_df[\"date_\"].astype(str)\n", "blinkit_dump_category_df[\"date_\"] = blinkit_dump_category_df[\"date_\"].astype(str)\n", "hp_dump_category_df[\"date_\"] = hp_dump_category_df[\"date_\"].astype(str)\n", "\n", "\n", "calc_1_df = pd.merge(calc_1_df, total_dump_category_df, on=[\"date_\", \"category\"], how=\"left\")\n", "\n", "calc_1_df = pd.merge(calc_1_df, blinkit_dump_category_df, on=[\"date_\", \"category\"], how=\"left\")\n", "\n", "calc_1_df = pd.merge(calc_1_df, hp_dump_category_df, on=[\"date_\", \"category\"], how=\"left\")\n", "\n", "calc_1_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_1_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["transfer_loss_df[\"date_\"] = transfer_loss_df[\"date_\"].astype(str)\n", "customer_category_df[\"date_\"] = customer_category_df[\"date_\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_1_df = pd.merge(\n", "    calc_1_df,\n", "    customer_category_df,\n", "    on=[\"date_\", \"category\"],\n", "    how=\"left\",\n", ")\n", "\n", "calc_1_df[\"customer_dump_value\"] = calc_1_df[\"customer_dump_value\"].fillna(0)\n", "\n", "calc_1_df = calc_1_df.merge(transfer_loss_df, on=[\"date_\", \"category\"], how=\"left\")\n", "\n", "calc_1_df[\"transfer_loss_value\"] = calc_1_df[\"transfer_loss_value\"].fillna(0)\n", "\n", "calc_1_df[\"total_dump_value\"] = (\n", "    calc_1_df[\"backend_dump_value\"]\n", "    + calc_1_df[\"frontend_dump_value\"]\n", "    + calc_1_df[\"transfer_loss_value\"]\n", ")\n", "\n", "calc_1_df[\"total_dump_value_excl_rtv\"] = (\n", "    calc_1_df[\"backend_dump_value_excl_rtv\"]\n", "    + calc_1_df[\"frontend_dump_value_excl_rtv\"]\n", "    + calc_1_df[\"transfer_loss_value_excl_rtv\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_1_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_agg_df = (\n", "    calc_1_df.groupby([\"date_\"])\n", "    .agg(\n", "        {\n", "            \"total_gmv\": \"sum\",\n", "            \"total_sales\": \"sum\",\n", "            \"total_carts\": \"max\",\n", "            \"total_dump_value\": \"sum\",\n", "            \"total_dump_value_excl_rtv\": \"sum\",\n", "            \"frontend_dump_value\": \"sum\",\n", "            \"frontend_dump_value_excl_rtv\": \"sum\",\n", "            \"backend_dump_value\": \"sum\",\n", "            \"backend_dump_value_excl_rtv\": \"sum\",\n", "            \"transfer_loss_value\": \"sum\",\n", "            \"transfer_loss_value_excl_rtv\": \"sum\",\n", "            \"dn_short\": \"sum\",\n", "            \"dn_damage\": \"sum\",\n", "            \"customer_dump_value\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "calc_agg_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_agg_df[\"category\"] = \"All\"\n", "\n", "calc_1_df = calc_1_df.append(calc_agg_df)\n", "calc_1_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_1_df[\"aov\"] = calc_1_df[\"total_gmv\"] / calc_1_df[\"total_carts\"]\n", "\n", "calc_1_df[\"date_\"] = pd.to_datetime(calc_1_df[\"date_\"])\n", "\n", "calc_1_df = pd.merge(calc_1_df, availability_df, on=[\"date_\", \"category\"], how=\"left\")\n", "\n", "calc_1_df[\"blinkit_dump_perc\"] = (calc_1_df[\"frontend_dump_value\"] / calc_1_df[\"total_gmv\"]).round(\n", "    4\n", ")\n", "calc_1_df[\"blinkit_dump_perc_excl_rtv\"] = (\n", "    calc_1_df[\"frontend_dump_value_excl_rtv\"] / calc_1_df[\"total_gmv\"]\n", ").round(4)\n", "\n", "\n", "calc_1_df[\"hp_dump_perc\"] = (calc_1_df[\"backend_dump_value\"] / calc_1_df[\"total_gmv\"]).round(4)\n", "calc_1_df[\"hp_dump_perc_excl_rtv\"] = (\n", "    calc_1_df[\"backend_dump_value_excl_rtv\"] / calc_1_df[\"total_gmv\"]\n", ").round(4)\n", "\n", "\n", "calc_1_df[\"total_dump_perc\"] = (calc_1_df[\"total_dump_value\"] / calc_1_df[\"total_gmv\"]).round(4)\n", "calc_1_df[\"total_dump_perc_excl_rtv\"] = (\n", "    calc_1_df[\"total_dump_value_excl_rtv\"] / calc_1_df[\"total_gmv\"]\n", ").round(4)\n", "\n", "\n", "calc_1_df[\"tl_perc\"] = (calc_1_df[\"transfer_loss_value\"] / calc_1_df[\"total_gmv\"]).round(4)\n", "calc_1_df[\"tl_perc_excl_rtv\"] = (\n", "    calc_1_df[\"transfer_loss_value_excl_rtv\"] / calc_1_df[\"total_gmv\"]\n", ").round(4)\n", "\n", "\n", "calc_1_df[\"dn_short_perc\"] = (calc_1_df[\"dn_short\"] / calc_1_df[\"total_gmv\"]).round(4)\n", "\n", "calc_1_df[\"dn_damage_perc\"] = (calc_1_df[\"dn_damage\"] / calc_1_df[\"total_gmv\"]).round(4)\n", "\n", "calc_1_df[\"customer_dump_perc\"] = (calc_1_df[\"customer_dump_value\"] / calc_1_df[\"total_gmv\"]).round(\n", "    4\n", ")\n", "\n", "calc_1_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_1_df[\n", "    [\"date_\", \"category\", \"aov\", \"total_sales\", \"total_dump_perc\", \"wt_availability\", \"total_carts\"]\n", "].head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### change"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cat_summary_df = calc_1_df.copy()\n", "\n", "cat_summary_df = cat_summary_df[\n", "    [\n", "        \"date_\",\n", "        \"category\",\n", "        \"wt_availability\",\n", "        \"total_sales\",\n", "        \"aov\",\n", "        \"total_dump_perc\",\n", "        \"blinkit_dump_perc\",\n", "        \"hp_dump_perc\",\n", "        \"tl_perc\",\n", "        \"dn_short_perc\",\n", "        \"dn_damage_perc\",\n", "        \"customer_dump_perc\",\n", "        \"total_dump_perc_excl_rtv\",\n", "        \"blinkit_dump_perc_excl_rtv\",\n", "        \"hp_dump_perc_excl_rtv\",\n", "        \"tl_perc_excl_rtv\",\n", "    ]\n", "]\n", "\n", "cat_summary_df[\"total_sales\"] = cat_summary_df[\"total_sales\"].fillna(0).astype(int)\n", "cat_summary_df.rename(columns={\"total_sales\": \"sales\"}, inplace=True)\n", "cat_summary_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    cat_summary_df,\n", "    sheetid=\"1Q-AF8BB-Br-Y6J4BcR5CfjZ7Cl9m8ROAg32jLpJG6u4\",\n", "    sheetname=\"category_view_raw\",\n", ")\n", "\n", "# cat_summary_df.to_csv(\"cat_summary_df\" + \"_master.csv\", index = False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["time.sleep(15)"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## <PERSON><PERSON> "]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["### Overall Day Wise Summary"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["#### Input from Sheet"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["### Catgeroy View"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cat_input_df = pb.from_sheets(\n", "    sheetid=\"1Q-AF8BB-Br-Y6J4BcR5CfjZ7Cl9m8ROAg32jLpJG6u4\",\n", "    sheetname=\"Category Daily View\",\n", ")\n", "\n", "cat_input_df\n", "\n", "# cat_input_df = pd.read_csv('Category Daily View.csv')"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["#### View Creation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def render_mpl_table_2(\n", "    data,\n", "    col_width=3.25,\n", "    row_height=0.8,\n", "    font_size=16,\n", "    header_color=\"#434343\",\n", "    footer_color=\"#434343\",\n", "    row_colors=[\n", "        \"#EFEFEF\",  # 0\n", "        \"#FFFFFF\",  # 1\n", "        \"#EFEFEF\",  # 2\n", "        \"#FFFFFF\",  # 3\n", "        \"#EFEFEF\",  # 4\n", "        \"#FFFFFF\",  # 5\n", "        \"#EFEFEF\",  # 6\n", "        \"#FFFFFF\",  # 7\n", "        \"#EFEFEF\",  # 8\n", "        \"#FFFFFF\",  # 9\n", "    ],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, cellLoc=\"center\", **kwargs\n", "    )\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"w\", fontsize=18)\n", "            cell.set_facecolor(header_color)\n", "        elif k[0] == 10:\n", "            cell.set_text_props(weight=\"bold\", color=\"w\", fontsize=15)\n", "            cell.set_facecolor(footer_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "            cell.set_text_props(weight=\"normal\", color=\"black\", fontsize=15)\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["#### <PERSON><PERSON> on Slack"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "\n", "if cat_input_df.shape[0] > 0:\n", "    fig, ax = render_mpl_table_2(cat_input_df, header_columns=0)\n", "    fig.savefig(\"perishable_category_daily.png\")\n", "    file_check = \"./perishable_category_daily.png\"\n", "    filepath = file_check\n", "    channel = \"blinkit-perishables-core\"\n", "    text = f\"Category Wise Sales & Dump Summary ({t1})\"\n", "else:\n", "    print(\"Got an error\")\n", "pb.send_slack_message(channel=channel, text=text, files=[filepath])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}