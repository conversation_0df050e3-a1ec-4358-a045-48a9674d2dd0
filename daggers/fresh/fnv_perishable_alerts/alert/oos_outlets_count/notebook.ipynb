{"cells": [{"cell_type": "code", "execution_count": null, "id": "f4eceb78-59e5-4ffd-b5af-beddda3554c8", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import os\n", "import time\n", "import gc\n", "import requests\n", "\n", "import calendar\n", "from datetime import datetime as dt\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.options.mode.chained_assignment = None  # default='warn'\n", "pd.set_option(\"display.max_columns\", 500)"]}, {"cell_type": "code", "execution_count": null, "id": "64189d81-19e2-4908-b276-d4b1d67b3295", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pytz\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")\n", "\n", "current_time = datetime.now(IST)\n", "\n", "current_hour = pd.to_datetime(current_time).hour\n", "\n", "today_date = pd.to_datetime(current_time).strftime(\"%Y-%m-%d\")\n", "\n", "today_date"]}, {"cell_type": "code", "execution_count": null, "id": "de306c47-0d5f-4a4e-a6fd-59fc097b977a", "metadata": {}, "outputs": [], "source": ["current_hour"]}, {"cell_type": "code", "execution_count": null, "id": "3952a72b-9f6b-4508-9ce0-ed98d158ce48", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(\n", "            f\"Read attempt: {attempt}... \\nRun started at {datetime.now() + timed<PERSON>ta(hours = 5.5)}\"\n", "        )\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "markdown", "id": "9dc7652d-3722-4193-bf14-823caa9571f0", "metadata": {}, "source": ["## ptypes & hour input"]}, {"cell_type": "code", "execution_count": null, "id": "069d2d51-e236-4570-8d80-4a65831f31df", "metadata": {}, "outputs": [], "source": ["oos_input_df = pb.from_sheets(\n", "    sheetid=\"1pIEInYj2XyvbwjLIdbnZOZ1w7n8HQwUFm8csGuw0Ivg\",\n", "    sheetname=\"oos_input\",\n", ")\n", "\n", "# oos_input_df = pd.read_csv(\"oos_input.csv\")\n", "\n", "oos_input_df[\"hour_\"] = oos_input_df[\"hour_\"].fillna(0)\n", "oos_input_df[\"hour_\"] = oos_input_df[\"hour_\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "57c5591d-2347-4a24-aa75-fb6d75c1d3fd", "metadata": {}, "outputs": [], "source": ["oos_input_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "e737d4d8-6b00-4ed0-a5cc-4946f4710e17", "metadata": {}, "outputs": [], "source": ["if oos_input_df.shape[0] > 0:\n", "    hour_list = list(oos_input_df[\"hour_\"].unique())\n", "    if len(hour_list) < 2:\n", "        hour_list.append(-1)\n", "        hour_list.append(-2)\n", "    hour_list = tuple(hour_list)\n", "    len(hour_list)"]}, {"cell_type": "code", "execution_count": null, "id": "ae340244-5785-4fef-aa0f-d5fd6ba43567", "metadata": {}, "outputs": [], "source": ["hour_list"]}, {"cell_type": "code", "execution_count": null, "id": "0e0c9bf4-ffb1-4454-a31b-82bb85366849", "metadata": {}, "outputs": [], "source": ["if oos_input_df.shape[0] > 0:\n", "    ptype_list = list(oos_input_df[\"ptype\"].unique())\n", "    if len(ptype_list) < 2:\n", "        ptype_list.append(-1)\n", "        ptype_list.append(-2)\n", "    ptype_list = tuple(ptype_list)\n", "len(ptype_list)"]}, {"cell_type": "code", "execution_count": null, "id": "18a07a9b-ae0b-4356-a98e-9ecb4d8610ba", "metadata": {}, "outputs": [], "source": ["ptype_list"]}, {"cell_type": "code", "execution_count": null, "id": "21bcd52b-7307-4103-bee8-14fcdae7b869", "metadata": {}, "outputs": [], "source": ["ptype_df = oos_input_df[[\"ptype\", \"group\", \"channel\", \"users\"]].drop_duplicates()"]}, {"cell_type": "markdown", "id": "1ec27345-a57d-4f1c-851b-0ae951224fce", "metadata": {"tags": []}, "source": ["## Meats Sales"]}, {"cell_type": "code", "execution_count": null, "id": "1564c460-e574-4059-83de-ae307e757d79", "metadata": {}, "outputs": [], "source": ["# sale_query = f\"\"\"\n", "#     WITH  meats_skus as (\n", "#     select distinct item_id, name as item_name, product_type as ptype\n", "#     from rpc.item_category_details\n", "#     where l2_id IN (63,1367,1369)\n", "#     and product_type in {ptype_list}\n", "#     ),\n", "\n", "\n", "#     pre_item_mapping as (\n", "#         SELECT DISTINCT ipr.product_id,\n", "#         CASE\n", "#             WHEN ipr.item_id IS NULL THEN ipom_0.item_id\n", "#             ELSE ipr.item_id\n", "#         END AS item_id,\n", "#         CASE\n", "#             WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1)\n", "#             ELSE COALESCE(ipom_0.multiplier,1)\n", "#         END AS multiplier\n", "#         FROM rpc.item_product_mapping ipr\n", "#         LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id\n", "#         LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", "#         WHERE ipr.lake_active_record\n", "#     ),\n", "\n", "#     item_mapping as (\n", "#     select product_id, x.item_id , item_name, ptype, multiplier\n", "#     from pre_item_mapping as x\n", "#     inner join meats_skus as ms on ms.item_id = x.item_id),\n", "\n", "\n", "#     sales AS (\n", "#         SELECT (oid.cart_checkout_ts_ist) AS order_date, cl.name AS city, rco.facility_id, oid.product_id, im.item_id, oid.order_id, im.multiplier, im.item_name, im.ptype,\n", "#         ((oid.procured_quantity - oid.total_doorstep_return_quantity) * im.multiplier) AS sales_quantity\n", "#         FROM dwh.fact_sales_order_item_details oid\n", "#         JOIN item_mapping im on im.product_id = oid.product_id\n", "#         JOIN retail.console_outlet rco ON rco.id = oid.outlet_id AND business_type_id = 7  AND rco.lake_active_record\n", "#         LEFT JOIN retail.console_location cl ON cl.id = rco.tax_location_id AND cl.lake_active_record\n", "#         WHERE oid.order_create_dt_ist >= DATE('{today_date}') - interval '31' day AND oid.order_create_dt_ist < DATE('{today_date}') - interval '0' day\n", "#         AND oid.is_internal_order = false\n", "#         AND (oid.order_type NOT LIKE '%%internal%%' OR oid.order_type IS NULL)\n", "#         AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED'\n", "#     ),\n", "\n", "#     final_sales AS (\n", "#         SELECT DATE(order_date) AS date_, city, ptype, s.item_id,item_name,  CAST(SUM(sales_quantity) AS int) AS quantity\n", "#         FROM sales s\n", "#         GROUP BY 1,2,3, 4,5\n", "#     )\n", "\n", "#     SELECT DISTINCT city, ptype, item_id, item_name, date_, quantity\n", "#     FROM final_sales\n", "\n", "#     \"\"\"\n", "# meats_sales_pre_df = read_sql_query(sale_query, trino)\n", "# meats_sales_pre_df[\"date_\"] = pd.to_datetime(meats_sales_pre_df[\"date_\"])\n", "\n", "# meats_sales_pre_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "7ee2c863-4219-4763-8720-dd296<PERSON>baaa7", "metadata": {}, "outputs": [], "source": ["# meats_sales_pre_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "2190ba6c-ce6d-438b-8193-f01007def668", "metadata": {}, "outputs": [], "source": ["# meats_sales_pre_df = meats_sales_pre_df.rename(columns={\"quantity\": \"qty_sold\"})"]}, {"cell_type": "code", "execution_count": null, "id": "c3d0d813-0427-4f04-a6e1-22e7a2874251", "metadata": {}, "outputs": [], "source": ["# sku_rank_df = (\n", "#     meats_sales_pre_df.groupby([\"city\", \"ptype\", \"item_id\", \"item_name\"])\n", "#     .agg({\"qty_sold\": \"mean\"})\n", "#     .reset_index()\n", "# )\n", "\n", "\n", "# sku_rank_df[\"qty_sold\"] = sku_rank_df[\"qty_sold\"].astype(int)\n", "\n", "# sku_rank_df = sku_rank_df.sort_values(by=\"qty_sold\", ascending=True)\n", "\n", "# sku_rank_df[\"cum_quantity\"] = sku_rank_df.groupby([\"city\", \"ptype\"])[\"qty_sold\"].cumsum()\n", "\n", "# sku_agg_df = (\n", "#     sku_rank_df.groupby([\"city\", \"ptype\"])\n", "#     .agg({\"qty_sold\": \"sum\"})\n", "#     .reset_index()\n", "#     .rename(columns={\"qty_sold\": \"tot_quantity\"})\n", "# )\n", "\n", "# sku_rank_df = sku_rank_df.merge(sku_agg_df, on=[\"city\", \"ptype\"], how=\"left\")\n", "\n", "# sku_rank_df[\"perc_contri\"] = sku_rank_df[\"cum_quantity\"] / sku_rank_df[\"tot_quantity\"]\n", "\n", "# ## 70% sales contributing..\n", "# sku_rank_df[\"stype_\"] = np.where(sku_rank_df[\"perc_contri\"] > 0.3, \"top\", \"bottom\")\n", "\n", "# sku_rank_df.head(1)\n", "\n", "# top_skus_meats = sku_rank_df[sku_rank_df[\"stype_\"] == \"top\"]\n", "# top_skus_meats = top_skus_meats[[\"city\", \"ptype\", \"item_id\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "b2be22fd-8722-4c25-8c3a-2a80aaf5159d", "metadata": {}, "outputs": [], "source": ["# top_skus_meats.head(5)"]}, {"cell_type": "markdown", "id": "30e8c30a-da15-4180-a520-40f61c077593", "metadata": {}, "source": ["### Top skus within a ptype in a city"]}, {"cell_type": "code", "execution_count": null, "id": "e0489415-b809-4e02-84c3-6e2a7ecdec02", "metadata": {}, "outputs": [], "source": ["# sales_q = f\"\"\"\n", "# with pre_sales as\n", "# (SELECT\n", "#        a.date_,\n", "#        a.city,\n", "#        a.outlet_id,\n", "#        a.item_id,\n", "#        a.item_name,\n", "#        a.quantity\n", "# FROM supply_etls.perishables_daily_item_outlet_sales as a\n", "# JOIN (select date_, facility_id,item_id, max(updated_at) as updated_at\n", "#           from supply_etls.perishables_daily_item_outlet_sales\n", "#        where date_ist >= date('{today_date}') - interval '30' DAY\n", "#         group by 1,2,3) as b ON a.facility_id = b.facility_id AND a.item_id = b.item_id AND a.date_ = b.date_ AND a.updated_at = b.updated_at\n", "# WHERE a.date_ist >= date('{today_date}') - interval '30' DAY\n", "# )\n", "\n", "# , sales_agg as\n", "# (select city,\n", "#         date_,\n", "#         item_id,\n", "#         item_name,\n", "#         sum(quantity) as qty_sold\n", "#     from pre_sales\n", "#     group by 1,2,3,4)\n", "\n", "\n", "# ,ptype_mapping as\n", "# (select distinct item_id, product_type  from rpc.item_category_details\n", "#     where product_type in {ptype_list})\n", "\n", "\n", "# select s.city, s.date_, p.product_type as ptype, s.item_id, s.item_name, qty_sold\n", "# from sales_agg as s\n", "# join ptype_mapping as p ON s.item_id = p.item_id\n", "# \"\"\"\n", "\n", "# sales_df = read_sql_query(sales_q, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "be5bb23b-2a3c-41ce-9447-5a5664fe85b7", "metadata": {}, "outputs": [], "source": ["# sales_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "73c4093f-0fcb-4fdf-af61-0d91c77c4042", "metadata": {}, "outputs": [], "source": ["# sku_rank_df = (\n", "#     sales_df.groupby([\"city\", \"ptype\", \"item_id\", \"item_name\"])\n", "#     .agg({\"qty_sold\": \"mean\"})\n", "#     .reset_index()\n", "# )\n", "\n", "\n", "# sku_rank_df[\"qty_sold\"] = sku_rank_df[\"qty_sold\"].astype(int)\n", "\n", "# sku_rank_df = sku_rank_df.sort_values(by=\"qty_sold\", ascending=True)\n", "\n", "# sku_rank_df[\"cum_quantity\"] = sku_rank_df.groupby([\"city\", \"ptype\"])[\"qty_sold\"].cumsum()\n", "\n", "# sku_agg_df = (\n", "#     sku_rank_df.groupby([\"city\", \"ptype\"])\n", "#     .agg({\"qty_sold\": \"sum\"})\n", "#     .reset_index()\n", "#     .rename(columns={\"qty_sold\": \"tot_quantity\"})\n", "# )\n", "\n", "# sku_rank_df = sku_rank_df.merge(sku_agg_df, on=[\"city\", \"ptype\"], how=\"left\")\n", "\n", "# sku_rank_df[\"perc_contri\"] = sku_rank_df[\"cum_quantity\"] / sku_rank_df[\"tot_quantity\"]\n", "\n", "# ## 70% sales contributing..\n", "# sku_rank_df[\"stype_\"] = np.where(sku_rank_df[\"perc_contri\"] > 0.3, \"top\", \"bottom\")\n", "\n", "# sku_rank_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "9ae8982d-8b2e-4e24-8ddd-32c7282badfe", "metadata": {}, "outputs": [], "source": ["# top_skus = sku_rank_df[sku_rank_df[\"stype_\"] == \"top\"]\n", "# top_skus = top_skus[[\"city\", \"ptype\", \"item_id\"]].drop_duplicates()"]}, {"cell_type": "markdown", "id": "db102b02-6fd4-4e90-95d8-0aa1d2bc8052", "metadata": {}, "source": ["### concat meats & other l2 top skus"]}, {"cell_type": "code", "execution_count": null, "id": "b03e0494-92df-4b45-b50f-c261321fc77b", "metadata": {}, "outputs": [], "source": ["# top_skus = pd.concat([top_skus, top_skus_meats], ignore_index=True)"]}, {"cell_type": "markdown", "id": "fed2a012-764c-4951-ad70-18038f1cdd07", "metadata": {}, "source": ["# Inventory"]}, {"cell_type": "code", "execution_count": null, "id": "bfaa139c-ba25-49cb-90a4-99eaee25912d", "metadata": {}, "outputs": [], "source": ["pre_base_query = f\"\"\"\n", "\n", "with active_ds as (\n", "    WITH active_stores AS (\n", "  SELECT\n", "    DISTINCT pfom.facility_id,\n", "    pfom.outlet_id,\n", "    bfom.facility_id AS be_facility_id\n", "  FROM\n", "    po.physical_facility_outlet_mapping pfom\n", "    JOIN retail.console_outlet co ON co.id = pfom.outlet_id\n", "    AND co.active = 1\n", "    AND co.business_type_id = 7\n", "    AND co.lake_active_record\n", "    JOIN po.bulk_facility_outlet_mapping bfom ON bfom.outlet_id = pfom.outlet_id\n", "    AND bfom.active = TRUE\n", "    AND bfom.lake_active_record\n", "  WHERE\n", "    ars_active = 1\n", "    AND pfom.active = 1\n", "    AND pfom.is_primary = 1\n", "    AND pfom.lake_active_record\n", "),\n", "\n", "assortment AS (\n", "  SELECT\n", "    DISTINCT cl.name AS city,\n", "    a.facility_id,\n", "    outlet_id,\n", "    a.item_id,\n", "    be_facility_id\n", "  FROM\n", "    rpc.product_facility_master_assortment a\n", "    JOIN active_stores b ON a.facility_id = b.facility_id\n", "    JOIN (\n", "      SELECT\n", "        DISTINCT a.item_id\n", "      FROM\n", "        rpc.product_product a\n", "        JOIN (\n", "          SELECT\n", "            item_id,\n", "            MAX(id) AS id\n", "          FROM\n", "            rpc.product_product\n", "          WHERE\n", "            approved = 1\n", "            AND active = 1\n", "          GROUP BY\n", "            1\n", "        ) b ON a.id = b.id\n", "        AND a.item_id = b.item_id\n", "      WHERE\n", "        a.active = 1\n", "        AND a.lake_active_record\n", "        AND a.approved = 1\n", "        AND a.perishable = 1\n", "    ) ma ON ma.item_id = a.item_id\n", "    JOIN retail.console_outlet co ON co.id = b.outlet_id\n", "    AND co.active = 1\n", "    AND co.business_type_id = 7\n", "    AND co.lake_active_record\n", "    LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id\n", "    AND cl.lake_active_record\n", "    JOIN rpc.item_category_details icd ON a.item_id = icd.item_id\n", "    AND icd.l2_id IN (\n", "      1425,\n", "      31,\n", "      116,\n", "      198,\n", "      1097,\n", "      1956,\n", "      949,\n", "      1389,\n", "      1778,\n", "      950,\n", "      138,\n", "      1091,\n", "      1093,\n", "      1094,\n", "      2633,\n", "      63,\n", "      1367,\n", "      1369\n", "    )\n", "  WHERE\n", "    a.active = 1\n", "    AND a.master_assortment_substate_id = 1\n", "    AND a.lake_active_record\n", "),\n", "be_mapping AS (\n", "  SELECT\n", "    DISTINCT item_id,\n", "    outlet_id,\n", "    cb.facility_id AS be_facility_id,\n", "    cb.name AS be_outlet_name,\n", "    cb.id AS be_outlet_id\n", "  FROM\n", "    rpc.item_outlet_tag_mapping tm\n", "    JOIN retail.console_outlet cb ON cb.id = CAST(tag_value AS int)\n", "    AND cb.active = 1\n", "    AND cb.lake_active_record\n", "  WHERE\n", "    tm.active = 1\n", "    AND tm.tag_type_id = 8\n", "    AND tm.lake_active_record\n", "),\n", "FINAL AS (\n", "  SELECT\n", "    city,\n", "    facility_id,\n", "    a.outlet_id,\n", "    icd.l2,\n", "    a.item_id,\n", "    icd.name AS item_name,\n", "    icd.product_type AS ptype,\n", "    a.be_facility_id,\n", "    b.be_outlet_id,\n", "    b.be_outlet_name\n", "  FROM\n", "    assortment a\n", "    JOIN be_mapping b ON a.item_id = b.item_id\n", "    AND a.outlet_id = b.outlet_id\n", "    AND a.be_facility_id = b.be_facility_id\n", "    JOIN rpc.item_category_details icd ON icd.item_id = a.item_id\n", "    AND icd.lake_active_record\n", "),\n", "\n", "check as (select cl.name as city, ds.outlet_id\n", "    from dwh.fact_sales_order_item_details ds\n", "    inner join retail.console_outlet co on co.id = ds.outlet_id and co.active = 1\n", "    inner join retail.console_location cl on cl.id = co.tax_location_id\n", "    inner join po.physical_facility_outlet_mapping pfom on pfom.outlet_id = ds.outlet_id and pfom.ars_active = 1 and pfom.active = 1\n", "    inner join dwh.dim_product dp\n", "    on ds.product_id = dp.product_id\n", "    and dp.is_current \n", "    and dp.l2_category_id in (1425,\n", "      31,\n", "      116,\n", "      198,\n", "      1097,\n", "      1956,\n", "      949,\n", "      1389,\n", "      1778,\n", "      950,\n", "      138,\n", "      1091,\n", "      1093,\n", "      1094,\n", "      2633,\n", "      63,\n", "      1367,\n", "      1369) \n", "    where order_create_dt_ist >= date('{today_date}')  - interval '0' DAY\n", "    group by 1,2\n", "    having count(distinct cart_id) > 10)\n", "\n", "SELECT\n", "city, \n", "  outlet_id\n", "FROM\n", "  FINAL\n", "  where outlet_id in (select distinct outlet_id from check)\n", "group by\n", "  1,\n", "  2\n", ")\n", "\n", ",perishable_skus AS (SELECT DISTINCT ma.item_id as item_id, \n", "                    icd.product_type AS ptype, icd.name as item_name\n", "                    FROM rpc.product_product ma\n", "                    JOIN (\n", "                        SELECT item_id, MAX(id) AS id\n", "                        FROM rpc.product_product\n", "                        WHERE active = 1 and approved = 1 and lake_active_record\n", "                        GROUP BY 1\n", "                    ) b ON ma.item_id = b.item_id and ma.id = b.id\n", "                    JOIN rpc.item_category_details icd ON icd.item_id = ma.item_id\n", "                    WHERE l2_id in (116, 63, 1425, 198, 31, 1097, 1956, 1367, 1369, 1389, 1778, 1094, 1093, 1091, 138, 949, 950, 2633) AND ma.perishable = 1 AND ma.lake_active_record\n", "\n", ")\n", "\n", "\n", ",eggs_uom AS \n", "(select item_id, \n", "case when eg.uom_value between 1 and 20 then 'small packs'\n", "             when eg.uom_value >= 21 then 'large packs'\n", "             else null end as uom_bucket\n", "    from \n", "            (SELECT \n", "                    DISTINCT ma.item_id AS item_id,\n", "                    icd.name AS item_name,\n", "                    l2,\n", "                    l1,\n", "                    icd.product_type,\n", "                    ma.brand,\n", "                    case when ma.variant_uom_value is NULL or ma.variant_uom_value = 0 \n", "                         then CAST(regexp_extract(icd.name, '(\\d+)(?=\\s*(pieces|units))') AS INT)\n", "                    else CAST(ma.variant_uom_value AS INT) end AS uom_value\n", "                    \n", "                FROM \n", "                    rpc.product_product ma\n", "                JOIN (\n", "                    SELECT item_id, MAX(id) AS id\n", "                    FROM rpc.product_product\n", "                    WHERE active = 1 and approved = 1\n", "                    GROUP BY 1\n", "                ) b ON ma.item_id = b.item_id and ma.id = b.id\n", "                JOIN rpc.item_category_details icd ON icd.item_id = ma.item_id\n", "                WHERE l2_id in (1389,1778)\n", "            ) as eg\n", "group by 1,2\n", ")\n", "\n", ", base as \n", "(select city,item_id, item_name, ptype, outlet_id, outlet_name, date_ist as date_, hour_, sum(inventory) as inventory   \n", "\n", "      FROM\n", "       (SELECT iii.outlet_id,\n", "                          iii.item_id,\n", "                          iii.snapshot_date_ist as date_ist,\n", "                          ad.city, \n", "                           ps.item_name, ps.ptype,\n", "                          o.name as outlet_name,\n", "                          extract(hour from etl_snapshot_ts_ist) AS hour_,\n", "                          max(current_inventory) AS inventory\n", "                   FROM dwh.agg_hourly_outlet_item_inventory as iii\n", "                   LEFT JOIN retail.console_outlet o ON iii.outlet_id = o.id\n", "                   INNER JOIN perishable_skus as ps on ps.item_id = iii.item_id\n", "                   INNER JOIN rpc.product_facility_master_assortment pfma on pfma.item_id = iii.item_id and o.facility_id = pfma.facility_id and master_assortment_substate_id = 1\n", "                   inner join active_ds ad on iii.outlet_id  = ad.outlet_id\n", "                   \n", "                   where snapshot_date_ist = date('{today_date}') \n", "                   and ptype in {ptype_list}\n", "                   group by 1,2,3,4,5,6,7,8)\n", "                   \n", "      \n", "group by 1,2,3,4,5,6,7,8\n", ")\n", "\n", "\n", "select \n", "    city,\n", "    ptype, \n", "    outlet_id, \n", "    outlet_name, \n", "    y.uom_bucket,\n", "    x.item_id, \n", "    item_name,\n", "    hour_, \n", "    inventory\n", "    \n", "from base  as x\n", "left join eggs_uom as y on x.item_id = y.item_id\n", "where hour_ in {hour_list}\n", "\n", "\"\"\"\n", "\n", "pre_base_df = read_sql_query(pre_base_query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "ddceda1c-33dd-4af2-84ef-d21b33a43397", "metadata": {}, "outputs": [], "source": ["pre_base_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "b03058d9-afe0-4bee-b2a3-e6a11c9c0565", "metadata": {}, "outputs": [], "source": ["pre_base_df[\"ptype\"] = np.where(\n", "    pre_base_df[\"ptype\"] == \"White Eggs\",\n", "    pre_base_df[\"ptype\"]\n", "    + \" - \"\n", "    + pre_base_df[\"uom_bucket\"].fillna(\"small packs\").astype(str),  # Corrected concatenation\n", "    pre_base_df[\"ptype\"],  # Keep 'ptype' unchanged if the condition is not met\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "bad2c642-a0b6-464f-bd87-e1c49fc0a468", "metadata": {}, "outputs": [], "source": ["pre_base_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "0c077751-6c76-4442-8bb5-6fe088d5b133", "metadata": {}, "outputs": [], "source": ["x = pre_base_df[pre_base_df[\"ptype\"].str.contains(\"Eggs\", case=False, na=False)]\n", "x.head(10)"]}, {"cell_type": "markdown", "id": "99f7193c-04ef-4599-9a19-7dd96abb7c9b", "metadata": {}, "source": ["#### considering inventory for only top skus"]}, {"cell_type": "code", "execution_count": null, "id": "7a1de7cf-0816-4b59-a0df-1e6c14437c69", "metadata": {}, "outputs": [], "source": ["# pre_base_df = pre_base_df.merge(top_skus, on=[\"city\", \"ptype\", \"item_id\"], how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "id": "b3b17579-df74-4665-9a51-3ff05be79c27", "metadata": {}, "outputs": [], "source": ["pre_base_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "700abcfd-73e7-4fd4-a496-2ba2dff1f7f8", "metadata": {}, "outputs": [], "source": ["pre_base_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "7f867663-f50a-4ec4-bc9a-39abb5dba84b", "metadata": {}, "outputs": [], "source": ["pre_base_df_max = pre_base_df[\"hour_\"].max()"]}, {"cell_type": "code", "execution_count": null, "id": "62d62811-32d3-4358-9355-d3ee1876de20", "metadata": {}, "outputs": [], "source": ["pre_base_df_max"]}, {"cell_type": "code", "execution_count": null, "id": "18b28b4f-c59f-4a6a-bac1-84575c91b38a", "metadata": {}, "outputs": [], "source": ["if current_hour < 11:\n", "    pre_base_df = pre_base_df[pre_base_df[\"hour_\"] == 9]\n", "elif 11 <= current_hour < 14:\n", "    pre_base_df = pre_base_df[\n", "        (pre_base_df[\"hour_\"] == pre_base_df_max)\n", "        & (pre_base_df[\"ptype\"].isin([\"Chicken Breast\", \"Chicken Curry Cut\"]))\n", "    ]\n", "elif 14 <= current_hour < 17:\n", "    pre_base_df = pre_base_df[\n", "        (pre_base_df[\"hour_\"] == pre_base_df_max)\n", "        & (~pre_base_df[\"ptype\"].isin([\"Chicken Breast\", \"Chicken Curry Cut\"]))\n", "    ]\n", "elif 17 <= current_hour < 20:\n", "    pre_base_df = pre_base_df[\n", "        (pre_base_df[\"hour_\"] == pre_base_df_max)\n", "        & (pre_base_df[\"ptype\"].isin([\"Chicken Breast\", \"Chicken Curry Cut\"]))\n", "    ]\n", "else:\n", "    pre_base_df = pre_base_df[\n", "        (pre_base_df[\"hour_\"] == pre_base_df_max)\n", "        & (~pre_base_df[\"ptype\"].isin([\"Chicken Breast\", \"Chicken Curry Cut\"]))\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "id": "4864e955-adb0-41bb-a94f-1c53573f1206", "metadata": {}, "outputs": [], "source": ["pre_base_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "607e364b-1ae2-45d5-a97a-31e607101799", "metadata": {}, "outputs": [], "source": ["### raw data for top skus in sheet\n", "\n", "attempts = 2  # Number of attempts\n", "\n", "for attempt in range(attempts):\n", "    try:\n", "        # Try to write to the sheet\n", "        pb.to_sheets(pre_base_df, \"1pIEInYj2XyvbwjLIdbnZOZ1w7n8HQwUFm8csGuw0Ivg\", \"oos_stores_raw\")\n", "        print(\"Data successfully written to raw_top_skus_oos sheet.\")\n", "        break  # If successful, exit the loop\n", "    except Exception as e:\n", "        # If an error occurs, print the error and try again if there are remaining attempts\n", "        print(f\"Attempt {attempt + 1} failed: {e}\")\n", "        if attempt == attempts - 1:\n", "            print(\"Skipping the operation after 2 failed attempts.\")\n", "        else:\n", "            print(\"Retrying...\")"]}, {"cell_type": "code", "execution_count": null, "id": "98558767-a0a5-48a0-a952-93169e8adb66", "metadata": {}, "outputs": [], "source": ["base_df_agg = (\n", "    pre_base_df.groupby([\"city\", \"ptype\", \"outlet_id\", \"outlet_name\", \"hour_\"])\n", "    .agg({\"inventory\": \"sum\"})\n", "    .reset_index()\n", ")\n", "base_df_agg[\"inv_flag\"] = np.where(base_df_agg[\"inventory\"] > 0, 1, 0)"]}, {"cell_type": "code", "execution_count": null, "id": "142eff8b-787e-4def-8f6a-e449a6363974", "metadata": {}, "outputs": [], "source": ["base_df_agg.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "7d57e666-7420-4283-b85a-974020938025", "metadata": {}, "outputs": [], "source": ["base_df = (\n", "    base_df_agg[base_df_agg[\"inv_flag\"] == 0]\n", "    .groupby([\"city\", \"ptype\", \"hour_\"])[\"outlet_id\"]\n", "    .nunique()\n", "    .reset_index(name=\"oos_outlets\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d21e74f4-18f7-4567-9273-71c740d3bc4c", "metadata": {}, "outputs": [], "source": ["base_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "b60a6c28-5c83-4165-97c2-144e9a2eb803", "metadata": {}, "outputs": [], "source": ["base_df[[\"ptype\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "b6e3a622-a188-4583-ac41-267b250544c1", "metadata": {}, "outputs": [], "source": ["### oos outlets\n", "\n", "attempts = 2\n", "\n", "for attempt in range(attempts):\n", "    try:\n", "        pb.to_sheets(base_df, \"1pIEInYj2XyvbwjLIdbnZOZ1w7n8HQwUFm8csGuw0Ivg\", \"oos_outlets\")\n", "        print(\"Data successfully written to sheets.\")\n", "        break  # If successful, exit the loop\n", "    except Exception as e:\n", "        # If an error occurs, print the error and try again if there are remaining attempts\n", "        print(f\"Attempt {attempt + 1} failed: {e}\")\n", "        if attempt == attempts - 1:\n", "            print(\"Skipping the operation after 2 failed attempts.\")\n", "        else:\n", "            print(\"Retrying...\")"]}, {"cell_type": "code", "execution_count": null, "id": "3575b247-d7d0-4eb4-a34d-72890856e34e", "metadata": {"tags": []}, "outputs": [], "source": ["# base_query = f\"\"\"\n", "\n", "# with active_ds as (\n", "#     select cl.name as city, ds.outlet_id\n", "#     from dwh.fact_sales_order_item_details ds\n", "#     inner join retail.console_outlet co on co.id = ds.outlet_id and co.active = 1\n", "#     inner join retail.console_location cl on cl.id = co.tax_location_id\n", "#     inner join po.physical_facility_outlet_mapping pfom on pfom.outlet_id = ds.outlet_id and pfom.ars_active = 1 and pfom.active = 1\n", "#     inner join dwh.dim_product dp\n", "#     on ds.product_id = dp.product_id\n", "#     and dp.is_current\n", "#     and dp.l0_category_id = 1487\n", "#     where order_create_dt_ist >= date('{today_date}') - interval '2' DAY\n", "#     group by 1,2\n", "#     having count(distinct cart_id) > 10\n", "# )\n", "\n", "# ,perishable_skus AS (SELECT DISTINCT ma.item_id as item_id, icd.product_type AS ptype, icd.name as item_name,\n", "#             CASE\n", "#             WHEN l2_id IN (1425) THEN 'Batter'\n", "#             WHEN l2_id IN (31,198) THEN 'Regular Breads'\n", "#             WHEN l2_id IN (1956) THEN 'Buns, Pavs & Pizza Base'\n", "#             WHEN l2_id IN (116,1097,2633) THEN 'Speciality Breads'\n", "#             WHEN l2_id IN (63,1367,1369) THEN 'Meats'\n", "#             WHEN l2_id IN (949) THEN 'Curd'\n", "#             WHEN l2_id IN (1389,1778) THEN 'Eggs'\n", "#             WHEN l2_id IN (950) THEN 'Paneer'\n", "#             WHEN l2_id IN (138) THEN 'Tofu'\n", "#             WHEN l2_id IN (1091) THEN 'Speciality Milk'\n", "#             WHEN l2_id IN (1093) THEN 'Lassi & Chaach'\n", "#             WHEN l2_id IN (1094) THEN 'Yogurt'\n", "#             END AS category\n", "#             FROM rpc.product_product ma\n", "#             JOIN (\n", "#                 SELECT item_id, MAX(id) AS id\n", "#                 FROM rpc.product_product\n", "#                 WHERE active = 1 and approved = 1 and lake_active_record\n", "#                 GROUP BY 1\n", "#             ) b ON ma.item_id = b.item_id and ma.id = b.id\n", "#             JOIN rpc.item_category_details icd ON icd.item_id = ma.item_id\n", "#             WHERE l2_id in (116, 63, 1425, 198, 31, 1097, 1956, 1367, 1369, 1389, 1778, 1094, 1093, 1091, 138, 949, 950, 2633) AND ma.perishable = 1 AND ma.lake_active_record\n", "# )\n", "\n", "# , base as\n", "# (select city,item_id, item_name,category,  ptype, outlet_id, outlet_name, date_ist as date_, hour_, sum(inventory) as inventory\n", "\n", "#       FROM\n", "#        (SELECT iii.outlet_id,\n", "#                           iii.item_id,\n", "#                           iii.snapshot_date_ist as date_ist,\n", "#                           ad.city,\n", "#                           ps.category, ps.item_name, ps.ptype,\n", "#                           o.name as outlet_name,\n", "#                           extract(hour from etl_snapshot_ts_ist) AS hour_,\n", "#                           max(current_inventory) AS inventory\n", "#                    FROM dwh.agg_hourly_outlet_item_inventory as iii\n", "#                    LEFT JOIN retail.console_outlet o ON iii.outlet_id = o.id\n", "#                    INNER JOIN perishable_skus as ps on ps.item_id = iii.item_id\n", "#                    INNER JOIN rpc.product_facility_master_assortment pfma on pfma.item_id = iii.item_id and o.facility_id = pfma.facility_id and master_assortment_substate_id = 1\n", "#                    inner join active_ds ad on iii.outlet_id  = ad.outlet_id\n", "\n", "#                    where snapshot_date_ist  = date('{today_date}')\n", "#                    group by 1,2,3,4,5,6,7,8,9)\n", "\n", "\n", "# group by 1,2,3,4,5,6,7,8,9\n", "# )\n", "\n", "\n", "# select\n", "# city,\n", "# category,\n", "# ptype,\n", "# hour_,\n", "# date_,\n", "# count(distinct outlet_id) as total_outlets,\n", "# count(distinct (case when inv_flag = 0 then outlet_id else null END)) as oos_outlets\n", "\n", "# from\n", "\n", "# (select city, category, ptype, outlet_id, outlet_name, hour_, date_, case when current_inv > 0 then 1 else 0 end as inv_flag\n", "#     from\n", "#      (select city, category, ptype, outlet_id, outlet_name,hour_, date_, sum(inventory) as current_inv  from base\n", "#         group by 1,2,3,4,5,6,7)\n", "# )\n", "\n", "# where ptype in {ptype_list}\n", "# and hour_ in {hour_list}\n", "# and date_ = date('{today_date}')\n", "\n", "# group by 1,2,3,4,5 \"\"\"\n", "\n", "# base_df = read_sql_query(base_query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "710de07c-1c78-48ba-ba06-ce65b0902673", "metadata": {}, "outputs": [], "source": ["base_df = base_df.merge(ptype_df, on=[\"ptype\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "cf4393b0-db21-4c41-84a9-8d338bb09c1a", "metadata": {}, "outputs": [], "source": ["base_df[\"group\"] = np.where(\n", "    base_df[\"ptype\"].isin([\"White Eggs - large packs\", \"White Eggs - small packs\"]),\n", "    2,\n", "    base_df[\"group\"],\n", ")\n", "\n", "base_df[\"channel\"] = np.where(\n", "    base_df[\"ptype\"].isin([\"White Eggs - large packs\", \"White Eggs - small packs\"]),\n", "    \"blinkit-perishables-indent-check\",\n", "    base_df[\"channel\"],\n", ")\n", "\n", "\n", "base_df[\"users\"] = np.where(\n", "    base_df[\"ptype\"].isin([\"White Eggs - large packs\", \"White Eggs - small packs\"]),\n", "    \"<@U07185MUX32> <@U03SMGMHZ4G>\",\n", "    base_df[\"users\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e81ca5dc-65ae-4a94-916c-7b10d625e716", "metadata": {}, "outputs": [], "source": ["base_df[\"group\"] = base_df[\"group\"].astype(int)\n", "base_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "dc7f4987-a903-4c9c-96e4-53395918ae2a", "metadata": {}, "outputs": [], "source": ["inventory_snapshot_hr = base_df[\"hour_\"].unique().tolist()\n", "print(*inventory_snapshot_hr)"]}, {"cell_type": "code", "execution_count": null, "id": "89c5f3d7-66fa-4b82-ab3c-60f567375679", "metadata": {}, "outputs": [], "source": ["base_df_meats = base_df[base_df[\"group\"] == 1]"]}, {"cell_type": "code", "execution_count": null, "id": "e8cb43a4-b075-4bd3-98ce-573b513e2a8a", "metadata": {}, "outputs": [], "source": ["base_df_meats = base_df_meats[base_df_meats[\"oos_outlets\"] > 2]"]}, {"cell_type": "code", "execution_count": null, "id": "e5732ce1-3681-4d9a-b870-77046266038e", "metadata": {}, "outputs": [], "source": ["# Create the pivot table\n", "pivot_table_1 = base_df_meats.pivot_table(\n", "    index=[\"city\"],  # Rows\n", "    columns=\"ptype\",  # Columns\n", "    values=\"oos_outlets\",  # Metric\n", "    aggfunc=\"sum\",  # Aggregation function\n", "    fill_value=0,  # Fill missing values with 0\n", ").reset_index()\n", "\n", "# Add the total_oos_outlets column\n", "pivot_table_1[\"total_oos_outlets\"] = pivot_table_1.sum(axis=1)\n", "\n", "# Sort by total_oos_outlets in descending order\n", "pivot_table_1 = pivot_table_1.sort_values(by=\"total_oos_outlets\", ascending=False).drop(\n", "    \"total_oos_outlets\", axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "bdfa0b1f-bffa-4182-b285-031be919ee2d", "metadata": {}, "outputs": [], "source": ["pivot_table_1.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "2f7ed11f-0490-467e-95a6-949b0d1f3a94", "metadata": {}, "outputs": [], "source": ["base_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "079b6ccb-de25-4da5-8369-5a8c9acb89da", "metadata": {}, "outputs": [], "source": ["base_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "d5ee7c36-538b-4e5e-ab14-f4aae7c111f0", "metadata": {}, "outputs": [], "source": ["base_write = base_df[[\"city\", \"ptype\", \"hour_\", \"oos_outlets\"]].drop_duplicates()\n", "pb.to_sheets(base_write, \"1pIEInYj2XyvbwjLIdbnZOZ1w7n8HQwUFm8csGuw0Ivg\", \"list_1\")"]}, {"cell_type": "code", "execution_count": null, "id": "4f3ebc3a-bdd9-4ecf-b9a7-ba1d7b16cd2c", "metadata": {}, "outputs": [], "source": ["# base_df.to_csv('base_df.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "5a4d6f0d-407c-461d-a232-be768bc24c0e", "metadata": {}, "outputs": [], "source": ["base_df.head(2)"]}, {"cell_type": "markdown", "id": "e1106c43-78f6-4b6c-a4d9-1b62e48460fc", "metadata": {}, "source": ["## Other L2s"]}, {"cell_type": "code", "execution_count": null, "id": "1c83a02e-5b43-426d-a09a-96c566f14a90", "metadata": {}, "outputs": [], "source": ["base_df_2 = base_df[(base_df[\"group\"] == 2) & (base_df[\"oos_outlets\"] > 4)]\n", "base_df_3 = base_df[(base_df[\"group\"] == 3) & (base_df[\"oos_outlets\"] > 4)]"]}, {"cell_type": "code", "execution_count": null, "id": "ec50ed4f-a699-4ab3-9aed-874e414d9ab3", "metadata": {}, "outputs": [], "source": ["base_df_2.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "ae1e6d71-b01a-4e9b-96fa-de602e657b90", "metadata": {}, "outputs": [], "source": ["base_df_3.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "4c0eb871-8dc4-4583-9f22-c54fe0d19e46", "metadata": {}, "outputs": [], "source": ["# Create the pivot table\n", "pivot_table_2 = base_df_2.pivot_table(\n", "    index=[\"city\"],  # Rows\n", "    columns=\"ptype\",  # Columns\n", "    values=\"oos_outlets\",  # Metric\n", "    aggfunc=\"sum\",  # Aggregation function\n", "    fill_value=0,  # Fill missing values with 0\n", ").reset_index()\n", "\n", "# Add the total_oos_outlets column\n", "pivot_table_2[\"total_oos_outlets\"] = pivot_table_2.sum(axis=1)\n", "\n", "# Sort by total_oos_outlets in descending order\n", "pivot_table_2 = pivot_table_2.sort_values(by=\"total_oos_outlets\", ascending=False).drop(\n", "    \"total_oos_outlets\", axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "61af7bb6-cb34-4bb4-bd11-d22174ea2b9a", "metadata": {}, "outputs": [], "source": ["# Create the pivot table\n", "pivot_table_3 = base_df_3.pivot_table(\n", "    index=[\"city\"],  # Rows\n", "    columns=\"ptype\",  # Columns\n", "    values=\"oos_outlets\",  # Metric\n", "    aggfunc=\"sum\",  # Aggregation function\n", "    fill_value=0,  # Fill missing values with 0\n", ").reset_index()\n", "\n", "# Add the total_oos_outlets column\n", "pivot_table_3[\"total_oos_outlets\"] = pivot_table_3.sum(axis=1)\n", "\n", "# Sort by total_oos_outlets in descending order\n", "pivot_table_3 = pivot_table_3.sort_values(by=\"total_oos_outlets\", ascending=False).drop(\n", "    \"total_oos_outlets\", axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0e39df25-5a07-4bfe-9490-8bbbceda3ec3", "metadata": {}, "outputs": [], "source": ["pivot_table_2.head(100)"]}, {"cell_type": "code", "execution_count": null, "id": "541fe30a-0f85-4910-a901-a80b40f228c6", "metadata": {}, "outputs": [], "source": ["pivot_table_3.head(100)"]}, {"cell_type": "code", "execution_count": null, "id": "8d0e975c-8689-41f5-8412-1b87fac61c0b", "metadata": {}, "outputs": [], "source": ["# data_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "c616c3b4-670d-4ef9-a768-3523014e3794", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5c982159-b1ff-493a-8e49-6e94f9297c31", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ddfc3cd7-9b07-4d32-8259-5a3ed438c50a", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "from datetime import datetime, date, timedelta\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "id": "fc56a2ed-f1cc-4522-939b-41e2d237db7e", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5,\n", "    row_height=0.8,\n", "    font_size=15,\n", "    header_color=\"#741b47\",\n", "    footer_color=\"#434343\",\n", "    row_colors=[\n", "        \"#EFEFEF\",  # 0\n", "        \"#FFFFFF\",  # 1\n", "    ],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "\n", "    # Create the table\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, cellLoc=\"center\", **kwargs\n", "    )\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    # Iterate over each cell to apply formatting\n", "    for k, cell in mpl_table._cells.items():\n", "        # Default settings for all cells\n", "        cell.set_edgecolor(edge_color)\n", "\n", "        # Header row or first few columns\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"w\", fontsize=18)\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            # Row-based color alternation\n", "            row_color = row_colors[k[0] % len(row_colors)]\n", "            cell.set_facecolor(row_color)\n", "            cell.set_text_props(weight=\"normal\", color=\"black\", fontsize=15)\n", "\n", "            # Conditional formatting for numeric columns\n", "            col_name = data.columns[k[1]]\n", "            if (\n", "                pd.api.types.is_numeric_dtype(data[col_name]) and k[0] > 0\n", "            ):  # Skip header row (k[0] > 0)\n", "                numeric_value = data.iloc[k[0] - 1][\n", "                    col_name\n", "                ]  # k[0] - 1 to get the correct row value\n", "                # Apply formatting based on the numeric value\n", "                if numeric_value == 0:\n", "                    cell.set_facecolor(\"#FFFFFF\")  # No color\n", "                elif 2 <= numeric_value <= 5:\n", "                    cell.set_facecolor(\"#FFCCCC\")  # Light red\n", "                elif numeric_value > 5:\n", "                    cell.set_facecolor(\"#e06666\")  # Dark red\n", "\n", "    # Auto adjust column width\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "e68c83fc-b3d5-45bb-ae59-fc013115310f", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb1\n", "\n", "\n", "def send_alert(alert_df, product_list, message_prefix, slack_channel=None, users=None, hour=None):\n", "    if alert_df.shape[0] > 0:\n", "        fig, ax = render_mpl_table(alert_df, header_columns=0)\n", "        fig.savefig(\"perishable_oos_outlets.png\")\n", "        file_check = \"./perishable_oos_outlets.png\"\n", "\n", "        google_sheets_link = \"https://docs.google.com/spreadsheets/d/1pIEInYj2XyvbwjLIdbnZOZ1w7n8HQwUFm8csGuw0Ivg/edit?gid=2106903063#gid=2106903063\"\n", "        text = f\"{users} {message_prefix} at {hour}th hour for {product_list}. Detailed sheet linked <{google_sheets_link}|here>\"\n", "\n", "        pb1.send_slack_message(channel=slack_channel, text=text, files=[file_check])\n", "    else:\n", "        print(f\"No data available for {slack_channel}.\")\n", "\n", "\n", "# Group data by 'group' column\n", "grouped_df = base_df.groupby(\"group\")\n", "\n", "# Iterate over each group and send an alert\n", "for group_name, group_data in grouped_df:\n", "    # Concatenate product types (ptypes) for each group\n", "    product_list = \", \".join(group_data[\"ptype\"].unique())\n", "    hour = \" \".join(map(str, inventory_snapshot_hr))\n", "\n", "    # Filter the alert_df for the specific group and handle slack_channel accordingly\n", "    if group_name == 1:\n", "        alert_df = pivot_table_1\n", "        slack_channel = group_data[\"channel\"].iloc[0]  # Set slack_channel only if alert_df is set\n", "    elif group_name == 2:\n", "        alert_df = pivot_table_2\n", "        slack_channel = group_data[\"channel\"].iloc[0]  # Set slack_channel only if alert_df is set\n", "    elif group_name == 3:\n", "        alert_df = pivot_table_3\n", "        slack_channel = group_data[\"channel\"].iloc[0]  # Set slack_channel only if alert_df is set\n", "    else:\n", "        # If group_name is not 1, 2, or 3, handle it with a default empty DataFrame\n", "        alert_df = pd.DataFrame()\n", "        slack_channel = None  # No slack channel if no valid group\n", "        print(f\"Warning: group_name {group_name} is not recognized.\")\n", "\n", "    # Only proceed if alert_df has data\n", "    if alert_df.shape[0] > 0:\n", "        # Set the user tags (using the first value of the group)\n", "        users = group_data[\"users\"].iloc[0]\n", "\n", "        # Define the message prefix\n", "        message_prefix = f\"OOS Outlets for a Ptype\"\n", "\n", "        # Call send_alert with the relevant parameters\n", "        send_alert(\n", "            alert_df=alert_df,\n", "            product_list=product_list,\n", "            message_prefix=message_prefix,\n", "            slack_channel=slack_channel,\n", "            users=users,\n", "            hour=hour,\n", "        )"]}, {"cell_type": "code", "execution_count": null, "id": "58e4c719-bd22-4aba-9e4f-e856afe8ccea", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}