alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: backend_dump
dag_type: alert
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: fresh
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U091TA1NZNC
path: fresh/fnv_perishable_alerts/alert/backend_dump
paused: false
pool: fresh_pool
project_name: fnv_perishable_alerts
schedule:
  end_date: '2025-09-16T00:00:00'
  interval: 1 4 * * *
  start_date: '2025-06-18T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
