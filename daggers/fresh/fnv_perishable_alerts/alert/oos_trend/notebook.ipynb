{"cells": [{"cell_type": "code", "execution_count": null, "id": "f2fe10d9-4666-4b81-b5be-8c90fc5ff160", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import os\n", "import time\n", "import gc\n", "import requests\n", "\n", "import calendar\n", "from datetime import datetime as dt\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.options.mode.chained_assignment = None  # default='warn'\n", "pd.set_option(\"display.max_columns\", 500)"]}, {"cell_type": "code", "execution_count": null, "id": "124b3ef1-d04b-428d-8ddf-180df4df0ae5", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pytz\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")\n", "\n", "current_time = datetime.now(IST)\n", "\n", "current_hour = pd.to_datetime(current_time).hour\n", "\n", "today_date = pd.to_datetime(current_time).strftime(\"%Y-%m-%d\")\n", "\n", "today_date"]}, {"cell_type": "code", "execution_count": null, "id": "839eab43-7980-488b-a755-71d263647205", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pytz\n", "import calendar\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")\n", "\n", "current_time = datetime.now(IST)\n", "\n", "today_date = (current_time - timedelta(days=0)).strftime(\"%Y-%m-%d\")\n", "t1 = (current_time - timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "t7 = (current_time - timedelta(days=7)).strftime(\"%Y-%m-%d\")\n", "\n", "today_date, t1, t7\n", "\n", "dates = (today_date, t1, t7)"]}, {"cell_type": "code", "execution_count": null, "id": "3a22ddc4-5118-4c08-a228-014708f770a3", "metadata": {}, "outputs": [], "source": ["dates"]}, {"cell_type": "code", "execution_count": null, "id": "87e27e50-6558-4323-9cc9-7797f677cb63", "metadata": {}, "outputs": [], "source": ["current_hour"]}, {"cell_type": "code", "execution_count": null, "id": "3e8d4320-b291-4e81-af20-41facb97a6ba", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(\n", "            f\"Read attempt: {attempt}... \\nRun started at {datetime.now() + timed<PERSON>ta(hours = 5.5)}\"\n", "        )\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "e72e0355-c8e2-4524-8362-42710e66a586", "metadata": {}, "outputs": [], "source": ["def try_upload_to_sheets(df, sheet_id, sheet_name, retries=3):\n", "    for attempt in range(retries):\n", "        try:\n", "            pb.to_sheets(df, sheet_id, sheet_name)\n", "            print(f\"Successfully uploaded {sheet_name} on attempt {attempt + 1}\")\n", "            return  # Exit the function once successful\n", "        except Exception as e:\n", "            print(f\"Attempt {attempt + 1} failed for {sheet_name}: {e}\")\n", "            if attempt == retries - 1:\n", "                print(f\"All attempts failed for {sheet_name}\")"]}, {"cell_type": "code", "execution_count": null, "id": "6208ecea-8434-4cf8-8447-75a0f3a6de1f", "metadata": {}, "outputs": [], "source": ["l2_id_list = (\n", "    116,\n", "    63,\n", "    1425,\n", "    198,\n", "    31,\n", "    1097,\n", "    1956,\n", "    1367,\n", "    1369,\n", "    1389,\n", "    1778,\n", "    1094,\n", "    1093,\n", "    1091,\n", "    138,\n", "    949,\n", "    950,\n", "    2633,\n", "    1201,\n", "    1730,\n", ")"]}, {"cell_type": "markdown", "id": "b603a382-b279-4929-a188-d75c3717cfa5", "metadata": {}, "source": ["### Active Assortment"]}, {"cell_type": "code", "execution_count": null, "id": "fe116129-2623-4969-8d08-759e08b60aa5", "metadata": {}, "outputs": [], "source": ["base_query = f\"\"\"\n", "    WITH active_stores AS (\n", "        SELECT DISTINCT pfom.facility_id, pfom.outlet_id, bfom.facility_id AS be_facility_id\n", "        FROM po.physical_facility_outlet_mapping pfom \n", "        JOIN retail.console_outlet co ON co.id = pfom.outlet_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "        JOIN po.bulk_facility_outlet_mapping bfom ON bfom.outlet_id = pfom.outlet_id AND bfom.active = True AND bfom.lake_active_record\n", "        WHERE ars_active = 1 AND pfom.active = 1 AND pfom.is_primary = 1 AND pfom.lake_active_record\n", "    ),\n", "\n", "    assortment AS (\n", "        SELECT DISTINCT cl.name AS city, a.facility_id, outlet_id, a.item_id, be_facility_id\n", "        FROM rpc.product_facility_master_assortment a\n", "        JOIN active_stores b ON a.facility_id = b.facility_id\n", "        JOIN (\n", "            SELECT DISTINCT a.item_id\n", "            FROM rpc.product_product a\n", "            JOIN (\n", "                SELECT item_id, MAX(id) AS id\n", "                FROM rpc.product_product\n", "                WHERE approved = 1 AND active = 1\n", "                GROUP BY 1\n", "            ) b ON a.id = b.id AND a.item_id = b.item_id\n", "            WHERE a.active = 1 AND a.lake_active_record AND a.approved = 1 AND a.perishable = 1\n", "        ) ma ON ma.item_id = a.item_id\n", "        JOIN retail.console_outlet co ON co.id = b.outlet_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "        LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id AND cl.lake_active_record\n", "        JOIN rpc.item_category_details icd ON a.item_id = icd.item_id AND icd.l2_id IN {l2_id_list}\n", "        WHERE a.active = 1 AND a.master_assortment_substate_id = 1 AND a.lake_active_record\n", "    ),\n", "\n", "    be_mapping AS (\n", "        SELECT DISTINCT item_id, outlet_id, cb.facility_id AS be_facility_id , cb.name AS be_outlet_name, cb.id AS be_outlet_id\n", "        FROM rpc.item_outlet_tag_mapping tm\n", "        JOIN retail.console_outlet cb ON cb.id = CAST(tag_value AS int) AND cb.active = 1 AND cb.lake_active_record\n", "        WHERE tm.active = 1 AND tm.tag_type_id = 8 AND tm.lake_active_record\n", "    ),\n", "\n", "    final AS (\n", "        SELECT city, facility_id, a.outlet_id, icd.l2 , a.item_id, icd.name AS item_name, icd.product_type AS ptype, a.be_facility_id , b.be_outlet_id, b.be_outlet_name\n", "        FROM assortment a\n", "        JOIN be_mapping b ON a.item_id = b.item_id AND a.outlet_id = b.outlet_id AND a.be_facility_id = b.be_facility_id\n", "        JOIN rpc.item_category_details icd ON icd.item_id = a.item_id AND icd.lake_active_record\n", "    )\n", "    \n", "    SELECT distinct outlet_id,item_id, be_facility_id\n", "    FROM final\n", "\"\"\"\n", "base_df = read_sql_query(base_query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "da9ebd84-6b16-4eed-b0b4-9a1f74e7a54b", "metadata": {}, "outputs": [], "source": ["base_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "ee897112-3748-4396-97ae-c77f2f971efc", "metadata": {}, "outputs": [], "source": ["base_df.head(2)"]}, {"cell_type": "markdown", "id": "54409341-59d2-4975-901f-c973ad14c162", "metadata": {"tags": []}, "source": ["### DTS CPC tag hack"]}, {"cell_type": "code", "execution_count": null, "id": "6d966754-744c-4904-9307-28ca96309772", "metadata": {"tags": []}, "outputs": [], "source": ["dts_cpc = base_df.groupby([\"be_facility_id\"]).agg({\"outlet_id\": \"nunique\"}).reset_index()\n", "dts_cpc[\"cpc_flag\"] = np.where(dts_cpc[\"outlet_id\"] > 3, \"CPC\", \"DTS\")\n", "\n", "dts_cpc = dts_cpc[[\"be_facility_id\", \"cpc_flag\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "a9de49ac-692e-42cd-b8c2-7d0a370de20c", "metadata": {}, "outputs": [], "source": ["base_df = base_df.merge(dts_cpc, on=[\"be_facility_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "51172dd0-7f2e-4af2-a380-3f74ebcb62b4", "metadata": {}, "outputs": [], "source": ["try:\n", "    oos_input_df = pb.from_sheets(\n", "        sheetid=\"1XCzIZ3Y1qGl7jNPcd68iMAUmHHSUCarOibUEhOWMBwk\",\n", "        sheetname=\"oos_input\",\n", "    )\n", "except Exception as e:\n", "    print(f\"Error loading from Google Sheets: {e}\")\n", "    oos_input_df = pd.read_csv(\"oos_input.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "cf188ece-f26a-4230-859c-d46cb1bd5f7b", "metadata": {}, "outputs": [], "source": ["if oos_input_df.shape[0] > 0:\n", "    ptype_list = list(oos_input_df[\"ptype\"].unique())\n", "    if len(ptype_list) < 2:\n", "        ptype_list.append(-1)\n", "        ptype_list.append(-2)\n", "    ptype_list = tuple(ptype_list)\n", "len(ptype_list)"]}, {"cell_type": "code", "execution_count": null, "id": "04b115c9-310a-408c-81ca-3eb6d33ebb14", "metadata": {}, "outputs": [], "source": ["pre_base_query = f\"\"\"\n", "with active_ds as (\n", "    select cl.name as city, ds.outlet_id\n", "    from dwh.fact_sales_order_item_details ds\n", "    inner join retail.console_outlet co on co.id = ds.outlet_id and co.active = 1\n", "    inner join retail.console_location cl on cl.id = co.tax_location_id\n", "    inner join po.physical_facility_outlet_mapping pfom on pfom.outlet_id = ds.outlet_id and pfom.ars_active = 1 and pfom.active = 1\n", "    inner join dwh.dim_product dp\n", "    on ds.product_id = dp.product_id\n", "    and dp.is_current \n", "    and dp.l0_category_id = 1487 \n", "    where order_create_dt_ist >= date('{today_date}') - interval '2' DAY\n", "    group by 1,2\n", "    having count(distinct cart_id) > 10\n", "),\n", "\n", "perishable_skus AS (SELECT DISTINCT ma.item_id as item_id, \n", "                    icd.product_type AS ptype, icd.name as item_name\n", "                    FROM rpc.product_product ma\n", "                    JOIN (\n", "                        SELECT item_id, MAX(id) AS id\n", "                        FROM rpc.product_product\n", "                        WHERE active = 1 and approved = 1 and lake_active_record\n", "                        GROUP BY 1\n", "                    ) b ON ma.item_id = b.item_id and ma.id = b.id\n", "                    JOIN rpc.item_category_details icd ON icd.item_id = ma.item_id\n", "                    WHERE l2_id in (116, 63, 1425, 198, 31, 1097, 1956, 1367, 1369, 1389, 1778, 1094, 1093, 1091, 138, 949, 950, 2633, 1185) AND ma.perishable = 1 AND ma.lake_active_record\n", "\n", ")\n", "\n", ", base as \n", "(select city,item_id, item_name, ptype, outlet_id, outlet_name, date_ist as date_, hour_, sum(inventory) as inventory   \n", "\n", "      FROM\n", "       (SELECT iii.outlet_id,\n", "                          iii.item_id,\n", "                          iii.snapshot_date_ist as date_ist,\n", "                          ad.city, \n", "                           ps.item_name, ps.ptype,\n", "                          o.name as outlet_name,\n", "                          extract(hour from etl_snapshot_ts_ist) AS hour_,\n", "                          max(current_inventory) AS inventory\n", "                   FROM dwh.agg_hourly_outlet_item_inventory as iii\n", "                   LEFT JOIN retail.console_outlet o ON iii.outlet_id = o.id\n", "                   INNER JOIN perishable_skus as ps on ps.item_id = iii.item_id\n", "                   INNER JOIN rpc.product_facility_master_assortment pfma on pfma.item_id = iii.item_id and o.facility_id = pfma.facility_id and master_assortment_substate_id = 1\n", "                   inner join active_ds ad on iii.outlet_id  = ad.outlet_id\n", "                   \n", "                   where snapshot_date_ist IN ({','.join([f\"DATE '{date}'\" for date in dates])})\n", "                   and extract(hour from etl_snapshot_ts_ist) = 20\n", "                   group by 1,2,3,4,5,6,7,8)\n", "                   \n", "      \n", "group by 1,2,3,4,5,6,7,8\n", ")\n", "\n", "\n", "select \n", "    city,\n", "    date_, \n", "    ptype, \n", "    outlet_id, \n", "    outlet_name, \n", "    item_id, \n", "    item_name,\n", "    hour_, \n", "    inventory\n", "    \n", "from base \n", "\n", "\"\"\"\n", "\n", "pre_base_df = read_sql_query(pre_base_query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "5c97e957-c8c4-4ded-8e7f-0e9e2159a8f4", "metadata": {}, "outputs": [], "source": ["pre_base_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "8e9d2626-2710-4000-8294-043d8855a11d", "metadata": {}, "outputs": [], "source": ["### Active assortment inner join\n", "\n", "pre_base_df = pre_base_df.merge(base_df, on=[\"outlet_id\", \"item_id\"], how=\"inner\")\n", "pre_base_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "55ef66c4-4cf6-41e0-869d-7fbfc228ba4d", "metadata": {}, "outputs": [], "source": ["### Total outlets count\n", "\n", "ptype_outlets = pre_base_df.groupby([\"ptype\"]).agg({\"outlet_id\": \"nunique\"}).reset_index()\n", "ptype_outlets = ptype_outlets.rename(columns={\"outlet_id\": \"total_live_outlets\"})"]}, {"cell_type": "markdown", "id": "69d08e65-fd2c-40f6-8b5b-dd1ba7ba664a", "metadata": {}, "source": ["### Ptype OOS"]}, {"cell_type": "code", "execution_count": null, "id": "d8363e9b-3e34-4a59-94c9-590065f4a4c8", "metadata": {}, "outputs": [], "source": ["base_df_agg = (\n", "    pre_base_df.groupby([\"cpc_flag\", \"ptype\", \"date_\", \"outlet_id\", \"outlet_name\", \"hour_\"])\n", "    .agg({\"inventory\": \"sum\"})\n", "    .reset_index()\n", ")\n", "base_df_agg[\"inv_flag\"] = np.where(base_df_agg[\"inventory\"] > 0, 1, 0)\n", "\n", "oos_df = base_df_agg[base_df_agg[\"inv_flag\"] == 0]\n", "oos_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "616fb6c5-db2f-48cd-b69e-4acd2a53c5ab", "metadata": {}, "outputs": [], "source": ["oos_df = oos_df[oos_df[\"ptype\"].isin(ptype_list)]"]}, {"cell_type": "markdown", "id": "740443b4-1b10-419d-ad47-547f221bd9aa", "metadata": {}, "source": ["## No of days OOS "]}, {"cell_type": "code", "execution_count": null, "id": "1df587a6-b62b-43c1-bb95-80abcfc0f522", "metadata": {}, "outputs": [], "source": ["no_of_oos_days = (\n", "    oos_df.groupby([\"cpc_flag\", \"outlet_id\", \"outlet_name\", \"ptype\"])\n", "    .agg({\"date_\": \"nunique\"})\n", "    .reset_index()\n", ")\n", "no_of_oos_days = no_of_oos_days.rename(columns={\"date_\": \"days_oos\"})\n", "\n", "try_upload_to_sheets(\n", "    no_of_oos_days, \"1XCzIZ3Y1qGl7jNPcd68iMAUmHHSUCarOibUEhOWMBwk\", \"outlets_oos_days\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a37acc96-9fbf-4ec3-8c0a-364cfcca5321", "metadata": {}, "outputs": [], "source": ["oos_all3d = no_of_oos_days[no_of_oos_days[\"days_oos\"] == 3]\n", "oos_all3d.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "65391dcc-462b-4c7a-8e2d-0791445a1b32", "metadata": {}, "outputs": [], "source": ["oos_all3d_agg = oos_all3d.groupby([\"cpc_flag\", \"ptype\"]).agg({\"outlet_id\": \"nunique\"}).reset_index()\n", "oos_all3d_agg = oos_all3d_agg.rename(columns={\"outlet_id\": \"repeat_oos\"})\n", "oos_all3d_agg[\"repeat_oos\"] = oos_all3d_agg[\"repeat_oos\"].fillna(0).astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "181849d2-2bc8-4048-82b5-561da712ee1f", "metadata": {}, "outputs": [], "source": ["oos_all3d_agg.head(2)"]}, {"cell_type": "markdown", "id": "525f4bfd-e8e0-4c14-a192-3539f123302d", "metadata": {}, "source": ["## OOS Count"]}, {"cell_type": "code", "execution_count": null, "id": "96d47ac7-9558-4201-8c81-2ac761d122bb", "metadata": {}, "outputs": [], "source": ["oos_agg = oos_df.groupby([\"cpc_flag\", \"ptype\", \"date_\"]).agg({\"outlet_id\": \"nunique\"}).reset_index()\n", "oos_agg = oos_agg.rename(columns={\"outlet_id\": \"oos_outlet\"})"]}, {"cell_type": "code", "execution_count": null, "id": "f959fe47-841e-472a-9a12-02e075371062", "metadata": {}, "outputs": [], "source": ["oos_agg[\"date_\"] = pd.to_datetime(oos_agg[\"date_\"])\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")\n", "current_time = datetime.now(IST)\n", "\n", "today_date = current_time.date()\n", "\n", "oos_agg[\"days_diff\"] = (today_date - oos_agg[\"date_\"].dt.date).dt.days\n", "\n", "\n", "def categorize_days_diff(days_diff):\n", "    if days_diff == 0:\n", "        return \"T\"\n", "    elif days_diff == 1:\n", "        return \"T-1\"\n", "    elif days_diff == 7:\n", "        return \"T-7\"\n", "    else:\n", "        return f\"T-{days_diff}\"\n", "\n", "\n", "# Apply the categorization\n", "oos_agg[\"date_diff\"] = oos_agg[\"days_diff\"].apply(categorize_days_diff)\n", "\n", "oos_agg.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "ba92f09e-7a6a-41a1-a9fe-a39d73bb1b9f", "metadata": {}, "outputs": [], "source": ["pivoted_df = oos_agg.pivot(\n", "    index=[\"ptype\", \"cpc_flag\"], columns=\"date_diff\", values=\"oos_outlet\"\n", ").reset_index()\n", "\n", "pivoted_df = pivoted_df[sorted(pivoted_df.columns, reverse=True)]\n", "pivoted_df = pivoted_df.sort_values(by=\"ptype\", ascending=True)"]}, {"cell_type": "code", "execution_count": null, "id": "92c11bfa-4391-4a11-b3b1-bec0ab024a6b", "metadata": {}, "outputs": [], "source": ["pivoted_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "3d8e0eb2-03ad-4a04-b5b9-86cd04aa4d6b", "metadata": {}, "outputs": [], "source": ["pivoted_df = pivoted_df.merge(oos_all3d_agg, on=[\"ptype\", \"cpc_flag\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "22b8b832-fe49-40fc-a650-5180e4cfca4d", "metadata": {}, "outputs": [], "source": ["pivoted_df[\"repeat_oos\"] = pivoted_df[\"repeat_oos\"].fillna(0).astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "9d9e2272-d3b1-4cf1-9737-7c1f2604f9fd", "metadata": {}, "outputs": [], "source": ["desired_columns = [\"ptype\", \"cpc_flag\", \"T\", \"T-1\", \"T-7\", \"repeat_oos\"]\n", "pivoted_df = pivoted_df[desired_columns]"]}, {"cell_type": "code", "execution_count": null, "id": "de178cf8-73c1-4229-ae71-7b387c8064b2", "metadata": {}, "outputs": [], "source": ["pivoted_df.head(20)"]}, {"cell_type": "code", "execution_count": null, "id": "61594b43-f129-4953-84a6-ff368380d606", "metadata": {}, "outputs": [], "source": ["pivoted_df_cpc = pivoted_df[pivoted_df[\"cpc_flag\"] == \"CPC\"].drop(columns={\"cpc_flag\"})\n", "pivoted_df_dts = pivoted_df[pivoted_df[\"cpc_flag\"] != \"CPC\"].drop(columns={\"cpc_flag\"})"]}, {"cell_type": "code", "execution_count": null, "id": "a9dbdf6a-dd93-406c-ae94-ae51b45e8204", "metadata": {}, "outputs": [], "source": ["pivoted_df_cpc.head(15)"]}, {"cell_type": "code", "execution_count": null, "id": "4b41e9de-d5bb-452d-bb30-81271ecbbb73", "metadata": {}, "outputs": [], "source": ["pivoted_df_dts.head(15)"]}, {"cell_type": "code", "execution_count": null, "id": "7b171f40-69a6-4b51-8738-9f5f8e71c3ba", "metadata": {}, "outputs": [], "source": ["def render_mpl_table(\n", "    data,\n", "    col_width=5,\n", "    row_height=0.8,\n", "    font_size=15,\n", "    header_color=\"#741b47\",\n", "    footer_color=\"#434343\",\n", "    row_colors=[\n", "        \"#EFEFEF\",  # 0\n", "        \"#FFFFFF\",  # 1\n", "    ],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    title=None,  # Add title as a parameter\n", "    **kwargs,\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "\n", "    # If a title is provided, add it above the table\n", "    if title:\n", "        ax.text(\n", "            0.5, 1.05, title, ha=\"center\", va=\"center\", fontsize=20, weight=\"bold\", color=\"black\"\n", "        )\n", "\n", "    # Create the table\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, cellLoc=\"center\", **kwargs\n", "    )\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    # Iterate over each cell to apply formatting\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "\n", "        # Header row or first few columns\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"w\", fontsize=18)\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            row_color = row_colors[k[0] % len(row_colors)]\n", "            cell.set_facecolor(row_color)\n", "            cell.set_text_props(weight=\"normal\", color=\"black\", fontsize=15)\n", "\n", "            if k[1] == 1:\n", "                try:\n", "                    value_2 = pd.to_numeric(data.iloc[k[0] - 1, 1], errors=\"coerce\")\n", "                    value_3 = pd.to_numeric(data.iloc[k[0] - 1, 2], errors=\"coerce\")\n", "\n", "                    if value_2 > value_3:\n", "                        mpl_table[(k[0], k[1])].set_facecolor(\"#FFCCCC\")  # Light red\n", "                    else:\n", "                        mpl_table[(k[0], k[1])].set_facecolor(\"#FFFFFF\")  # No color\n", "                except Exception as e:\n", "                    print(f\"Error processing row {k[0]}: {e}\")\n", "                    pass  # Skip if there's an error in retrieving data\n", "\n", "    # Auto adjust column width\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "4d0beada-0e6c-440e-8ff8-37aa58c88290", "metadata": {}, "outputs": [], "source": ["def send_alert1():\n", "    import pencilbox as pb1\n", "\n", "    slack_channel = \"blinkit-perishables-indent-check\"\n", "\n", "    alert_df = pivoted_df_cpc\n", "\n", "    if alert_df.shape[0] > 0:\n", "        # Render the table and save it as an image\n", "        fig, ax = render_mpl_table(alert_df, header_columns=0, title=\"CPC OOS Trend\")\n", "        fig.savefig(\"oos_trend_cpc.png\")\n", "        file_check_1 = \"./oos_trend_cpc.png\"\n", "        filepath_fig = file_check_1\n", "        channel = slack_channel\n", "\n", "        # Link to the Google Sheet\n", "        gsheet_link = \"https://docs.google.com/spreadsheets/d/1XCzIZ3Y1qGl7jNPcd68iMAUmHHSUCarOibUEhOWMBwk/edit?gid=1219998157#gid=1219998157\"\n", "\n", "        # Alert text with Slack-friendly markdown formatting for the Google Sheet link\n", "        text = f\"OOS Trend for CPCs at 8 PM; Repeat Outlets List: <{gsheet_link}|Link> \"\n", "\n", "    else:\n", "        print(\"No data available or error occurred.\")\n", "\n", "    # Send message with the image and the formatted text\n", "    pb1.send_slack_message(channel=channel, text=text, files=[filepath_fig])"]}, {"cell_type": "code", "execution_count": null, "id": "d06590a0-fdb1-4de4-9e65-ca4be352aca3", "metadata": {}, "outputs": [], "source": ["send_alert1()"]}, {"cell_type": "code", "execution_count": null, "id": "b236bb61-73e7-4d34-9c2d-78dfb7bb65f8", "metadata": {}, "outputs": [], "source": ["def send_alert2():\n", "    import pencilbox as pb1\n", "\n", "    slack_channel = \"blinkit-perishables-indent-check\"\n", "\n", "    alert_df = pivoted_df_dts\n", "\n", "    if alert_df.shape[0] > 0:\n", "        # Render the table and save it as an image\n", "        fig, ax = render_mpl_table(alert_df, header_columns=0, title=\"DTS OOS Trend\")\n", "        fig.savefig(\"oos_trend_dts.png\")\n", "        file_check_1 = \"./oos_trend_dts.png\"\n", "        filepath_fig = file_check_1\n", "        channel = slack_channel\n", "\n", "        # Link to the Google Sheet\n", "        gsheet_link = \"https://docs.google.com/spreadsheets/d/1XCzIZ3Y1qGl7jNPcd68iMAUmHHSUCarOibUEhOWMBwk/edit?gid=1219998157#gid=1219998157\"\n", "\n", "        # Alert text with Slack-friendly markdown formatting for the Google Sheet link\n", "        text = f\"OOS Trend for DTS at 8 PM; Repeat Outlets List: <{gsheet_link}|Link> \"\n", "\n", "    else:\n", "        print(\"No data available or error occurred.\")\n", "\n", "    # Send message with the image and the formatted text\n", "    pb1.send_slack_message(channel=channel, text=text, files=[filepath_fig])"]}, {"cell_type": "code", "execution_count": null, "id": "c6657ac3-cf0f-4f28-9196-2cc46ac7999d", "metadata": {}, "outputs": [], "source": ["send_alert2()"]}, {"cell_type": "code", "execution_count": null, "id": "53045b4c-0ade-444f-9d9c-2b5c491a05db", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "08414b19-c845-440c-a106-39e9252f7df5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}