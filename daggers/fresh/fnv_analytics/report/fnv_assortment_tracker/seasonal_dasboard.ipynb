{"cells": [{"cell_type": "code", "execution_count": null, "id": "2ce28f26-3dd0-4196-9a2f-f800fa44ff88", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import time\n", "from datetime import datetime, timedelta, date\n", "import gc\n", "\n", "# !pip install pandasql\n", "# import pandasql as ps"]}, {"cell_type": "code", "execution_count": null, "id": "0edc409a-8608-4c9e-b6a4-9bb809665993", "metadata": {"tags": []}, "outputs": [], "source": ["trino = pb.get_connection(\"[Warehouse] Trino\")\n", "# redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "# retail = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "id": "f22d2937-2c8b-4784-8ac6-1ae3ffc9bc07", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "96d4b00f-de39-4a9f-98ec-c732e9c9e47a", "metadata": {}, "outputs": [], "source": ["pd.options.mode.chained_assignment = None  # default='warn'"]}, {"cell_type": "code", "execution_count": null, "id": "25a5b471-ed30-4a49-a325-425af559b283", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib"]}, {"cell_type": "code", "execution_count": null, "id": "0a1f055d-a502-416f-ae17-a1b9c8790635", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pytz\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")\n", "\n", "current_time = datetime.now(IST)\n", "\n", "current_hour = pd.to_datetime(current_time).hour\n", "\n", "today_date = current_time.strftime(\"%Y-%m-%d\")\n", "yesterday_date = (current_time - timedelta(days=1)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "c02ffd1b-6a17-49f7-9db9-6ccc7693827e", "metadata": {}, "outputs": [], "source": ["def fetch_data_with_retry(sheet_id, sheet_name, retries=3, delay=2):\n", "    for attempt in range(retries):\n", "        try:\n", "            return pb.from_sheets(sheet_id, sheet_name)\n", "        except Exception as e:\n", "            print(f\"Attempt {attempt + 1} failed for {sheet_name}: {e}\")\n", "            if attempt < retries - 1:\n", "                time.sleep(delay)  # wait before retrying\n", "    raise Exception(f\"Failed to fetch {sheet_name} after {retries} attempts.\")\n", "\n", "\n", "other_ptypes = fetch_data_with_retry(\"1BZWXMd6cCnpZc9IukdNa3Fbd8Wuo2uSpaoK6rUuuTc4\", \"inputsheet\")"]}, {"cell_type": "code", "execution_count": null, "id": "5c83a346-f3c7-4a38-bba9-0eac8556c73a", "metadata": {}, "outputs": [], "source": ["other_ptypes_list = tuple(list(other_ptypes[\"ptype\"].unique()))"]}, {"cell_type": "code", "execution_count": null, "id": "63c2c94b-2834-43f7-bee0-b083aaedec88", "metadata": {}, "outputs": [], "source": ["other_ptypes_list"]}, {"cell_type": "code", "execution_count": null, "id": "661e7d5d-013c-4a1f-ac8b-331edb5d42b3", "metadata": {}, "outputs": [], "source": ["seasonal_tag = \"\"\"\n", "select distinct city as city_name, item_id\n", "from ( \n", "select cl.name as city,\n", "icd.item_id,\n", "icd.name as item_name,\n", "    cat.id as category_id,\n", "    cat.name as cat_name,\n", "    pt.name as product_type\n", "    from  cms.gr_product_category_mapping pcm\n", "    inner join  cms.gr_category cat on cat.id=pcm.category_id\n", "    inner join  cms.gr_product p on p.id=pcm.product_id\n", "    left join (select id, name from cms.gr_product_type) pt on pt.id = p.type_id\n", "    inner join  rpc.item_product_mapping ipm on ipm.product_id = pcm.product_id\n", "    INNER JOIN  rpc.item_category_details icd ON icd.item_id = ipm.item_id AND icd.l0_id in (1487)\n", "    INNER JOIN  rpc.product_facility_master_assortment pfma ON pfma.item_id = ipm.item_id \n", "                AND pfma.master_assortment_substate_id = 1 AND pfma.active = 1 and pfma.lake_active_record\n", "    inner JOIN  retail.console_outlet co on co.facility_id = pfma.facility_id and co.active = 1 and co.business_type_id = 7 and co.lake_active_record\n", "    left join  retail.console_location cl on cl.id = co.tax_location_id and cl.lake_active_record\n", "    where p.enabled_flag=true\n", "    and cat.enabled_flag=true\n", "        and pcm.lake_active_record\n", "    and cat.lake_active_record\n", "    and p.lake_active_record\n", "    and cat.id in (482)\n", "    and is_primary = false \n", "    union\n", "    select 'Pan India' as city,\n", "    icd.item_id,\n", "    icd.name as item_name,\n", "    cat.id as category_id,\n", "    cat.name as cat_name,\n", "    pt.name as product_type\n", "    from  cms.gr_product_category_mapping pcm\n", "    inner join  cms.gr_category cat on cat.id=pcm.category_id\n", "    inner join  cms.gr_product p on p.id=pcm.product_id\n", "    left join (select id, name from cms.gr_product_type) pt on pt.id = p.type_id\n", "    inner join  rpc.item_product_mapping ipm on ipm.product_id = pcm.product_id\n", "    INNER JOIN  rpc.item_category_details icd ON icd.item_id = ipm.item_id AND icd.l0_id in (1487)\n", "    INNER JOIN  rpc.product_facility_master_assortment pfma ON pfma.item_id = ipm.item_id \n", "                AND pfma.master_assortment_substate_id = 1 AND pfma.active = 1 and pfma.lake_active_record\n", "    inner JOIN  retail.console_outlet co on co.facility_id = pfma.facility_id and co.active = 1 and co.business_type_id = 7 and co.lake_active_record\n", "    left join  retail.console_location cl on cl.id = co.tax_location_id and cl.lake_active_record\n", "    where p.enabled_flag=true\n", "    and cat.enabled_flag=true\n", "        and pcm.lake_active_record\n", "    and cat.lake_active_record\n", "    and p.lake_active_record\n", "    and cat.id in (482)\n", "    and is_primary = false)\n", "\"\"\"\n", "seasonal_tag = read_sql_query(seasonal_tag, trino)\n", "seasonal_tag[\"seasonal_flag\"] = 1"]}, {"cell_type": "code", "execution_count": null, "id": "17a928fe-dd27-4b62-8910-498a6ea15994", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select * from supply_etls.fnv_city_item_data where date_ >= current_date - interval '21' day \n", "\"\"\"\n", "merge_df = read_sql_query(query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "c7339da4-746a-425c-85fc-6c5eaa793cec", "metadata": {}, "outputs": [], "source": ["merge_df[\"date_\"] = pd.to_datetime(merge_df[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "7c3fa874-2708-4e10-959e-da6d51a08bc5", "metadata": {}, "outputs": [], "source": ["merge_df = pd.merge(merge_df, seasonal_tag, on=[\"city_name\", \"item_id\"], how=\"left\")\n", "merge_df[\"seasonal_flag\"] = merge_df[\"seasonal_flag\"].fillna(0)\n", "merge_df[\"seasonal_flag\"] = merge_df[\"seasonal_flag\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "45cd1ad2-7ba3-49bd-b636-22bc92dd84bd", "metadata": {}, "outputs": [], "source": ["item_name = \"\"\"\n", "select product_id, icd.item_id, name as item_name, product_type, variant_uom_text as unit\n", "from rpc.item_category_details icd\n", "join dwh.dim_item_product_offer_mapping pl ON icd.item_id = pl.item_id and pl.is_current and pl.is_offer = false\n", "JOIN (SELECT item_id,variant_uom_text, MAX(id) AS id\n", "                FROM rpc.product_product\n", "                WHERE active = 1 and approved = 1 and lake_active_record\n", "                GROUP BY 1,2\n", "            ) b ON icd.item_id = b.item_id\n", "where l0_id in (1487)\"\"\"\n", "item_name = read_sql_query(item_name, trino)\n", "merge_df = pd.merge(merge_df, item_name, on=[\"item_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "e9ea40ab-c080-4a39-9068-5991aab24dc2", "metadata": {}, "outputs": [], "source": ["merge_df = merge_df[\n", "    (merge_df[\"seasonal_flag\"] == 1) | (merge_df[\"product_type\"].isin(other_ptypes_list))\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "91a88d82-3bd0-4144-8c71-f9d63db241c9", "metadata": {}, "outputs": [], "source": ["merge_df"]}, {"cell_type": "code", "execution_count": null, "id": "d7eb3cd3-3288-4526-94a6-08b34b6ad138", "metadata": {}, "outputs": [], "source": ["cart_breakers = f\"\"\"\n", "with treasure as (\n", "SELECT  fsoid.city_name as city, ipm.item_id, icd.l2 as l2, icd.name as item_name, COUNT(DISTINCT fsoid.cart_id) AS item_checkout_carts\n", "FROM  dwh.fact_sales_order_item_details  AS fsoid \n", "LEFT JOIN retail.console_outlet c on fsoid.outlet_id = c.id\n", "JOIN rpc.item_product_mapping ipm on ipm.product_id = fsoid.product_id and ipm.active = 1\n", "JOIN rpc.item_category_details icd on icd.item_id = ipm.item_id and icd.l0_id in (1487)\n", "WHERE \n", "    fsoid.order_create_dt_ist  >=  current_date - interval '16' day AND fsoid.order_create_dt_ist < current_date\n", "    AND fsoid.order_type in ('RetailForwardOrder', 'DropShippingForwardOrder')\n", "    and fsoid.order_current_status in ('DELIVERED')\n", "GROUP BY\n", "    1, 2, 3,4\n", "),\n", "\n", "cartbreakers_ as (select city, item_id, item_name, item_checkout_carts,\n", "  sum(item_checkout_carts) over (partition by city order by item_checkout_carts desc rows unbounded preceding) as cumulative_sum,\n", "  sum(item_checkout_carts) over (partition by city) as total_carts\n", "from treasure\n", "union\n", "select 'Pan India' as city, item_id, item_name, item_checkout_carts,\n", "  sum(item_checkout_carts) over (partition by 'Pan India' order by item_checkout_carts desc rows unbounded preceding) as cumulative_sum,\n", "  sum(item_checkout_carts) over (partition by 'Pan India') as total_carts\n", "from treasure),\n", "\n", "final as (select *, \n", "cumulative_sum*1.00/(total_carts+0.0000001) as cumsum_perc\n", "from cartbreakers_)\n", "\n", "select city as city_name,item_id from final where cumsum_perc <=0.7     \n", "\"\"\"\n", "cart_breakers_df = read_sql_query(cart_breakers, trino)\n", "cart_breakers_df[\"cb_flag\"] = 1\n", "cart_breakers_df[\"date_\"] = yesterday_date"]}, {"cell_type": "code", "execution_count": null, "id": "1922070e-a019-4ba3-b348-003f81c6d4e2", "metadata": {}, "outputs": [], "source": ["cart_breakers_df[\"date_\"] = pd.to_datetime(cart_breakers_df[\"date_\"])\n", "merge_df = pd.merge(merge_df, cart_breakers_df, on=[\"city_name\", \"item_id\", \"date_\"], how=\"left\")\n", "merge_df[\"cb_flag\"] = merge_df[\"cb_flag\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "ad4396df-fcdf-41ca-b925-f57d0f890966", "metadata": {}, "outputs": [], "source": ["active_stores = \"\"\"select   \n", "Case when cl.name in ('Gurgaon') then 'HR-NCR'\n", "    when cl.name in ('Noida','Ghaziabad') then 'UP-NCR' else cl.name end as city_name,\n", "    pfma.item_id,\n", "    count(distinct co.id) as active_outlets\n", "from  rpc.product_facility_master_assortment pfma\n", "join retail.console_outlet co on co.facility_id = pfma.facility_id and co.business_type_id in (7) and co.active =1 and co.lake_active_record\n", "join retail.console_location cl on cl.id = co.tax_location_id\n", "join rpc.item_category_details icd on icd.item_id = pfma.item_id and icd.l0_id in (1487)\n", "join supply_etls.outlet_details od on od.facility_id = pfma.facility_id AND ars_Active = 1 and ars_check = 1\n", "where master_assortment_substate_id = 1 and pfma.active = 1\n", "group by 1,2\n", "union\n", "select   \n", "'Pan India' city_name,\n", "    pfma.item_id,\n", "    count(distinct co.id) as active_outlets\n", "from  rpc.product_facility_master_assortment pfma\n", "join retail.console_outlet co on co.facility_id = pfma.facility_id and co.business_type_id in (7) and co.active =1 and co.lake_active_record\n", "join retail.console_location cl on cl.id = co.tax_location_id\n", "join rpc.item_category_details icd on icd.item_id = pfma.item_id and icd.l0_id in (1487)\n", "join supply_etls.outlet_details od on od.facility_id = pfma.facility_id AND ars_Active = 1 and ars_check = 1\n", "where master_assortment_substate_id = 1 and pfma.active = 1\n", "group by 1,2\n", "\"\"\"\n", "active_stores = read_sql_query(active_stores, trino)\n", "active_stores[\"date_\"] = yesterday_date\n", "active_stores[\"date_\"] = pd.to_datetime(active_stores[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "72c12d65-1d7c-4366-9aa5-48fc0ee04847", "metadata": {}, "outputs": [], "source": ["merge_df = pd.merge(merge_df, active_stores, on=[\"city_name\", \"item_id\", \"date_\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "d5fea33e-c37b-4c95-9192-dcc3fa8d85ce", "metadata": {}, "outputs": [], "source": ["current_inv = \"\"\"\n", "with base as\n", "(\n", "    SELECT \n", "        item_id, item_name,outlet_id, outlet_name,\n", "     sum(actual_quantity)-sum(Case when blocked_quantity < 0 then 0 else blocked_quantity end) as net_inv \n", "    from\n", "    (\n", "        SELECT \n", "                iii.item_id, \n", "                icd.name as item_name, iii.outlet_id as outlet_id, o.name as outlet_name,\n", "                iii.quantity as actual_quantity, \n", "                (COALESCE(sum(case when iibi.blocked_type in (1,2,5) then iibi.quantity else 0 end),0)) as blocked_quantity\n", "        FROM \n", "            ims.ims_item_inventory iii\n", "        LEFT JOIN \n", "            ims.ims_item_blocked_inventory iibi ON iii.item_id = iibi.item_id AND iii.outlet_id = iibi.outlet_id\n", "        INNER JOIN \n", "            retail.console_outlet o ON iii.outlet_id = o.id and o.active = 1 and business_type_id = 7\n", "        INNER JOIN \n", "            rpc.item_category_details icd on icd.item_id = iii.item_id and icd.l0_id in (1487)\n", "        where\n", "            iii.active = 1 \n", "        GROUP BY \n", "            1,2,3,4,5,iii.quantity\n", "        )\n", "    group by 1,2,3,4\n", ")\n", "\n", "select * from\n", "(\n", "    select \n", "    Case when cl.name in ('Gurgaon') then 'HR-NCR'\n", "    when cl.name in ('Noida','Ghaziabad') then 'UP-NCR' else cl.name end as city_name,\n", "    base.item_id,\n", "    sum(net_inv) as net_inv\n", "    from base\n", "    join retail.console_outlet co on co.id = base.outlet_id and co.business_type_id = 7 and active = 1 and co.lake_active_record\n", "    left join retail.console_location cl on cl.id = co.tax_location_id\n", "    join supply_etls.outlet_details od on od.hot_outlet_id = base.outlet_id AND ars_Active = 1\n", "    group by 1,2\n", "    union\n", "    select \n", "    'Pan India' city_name,\n", "    base.item_id,\n", "    sum(net_inv) as net_inv\n", "    from base\n", "    join retail.console_outlet co on co.id = base.outlet_id and co.business_type_id = 7 and active = 1 and co.lake_active_record\n", "    left join retail.console_location cl on cl.id = co.tax_location_id\n", "    join supply_etls.outlet_details od on od.hot_outlet_id = base.outlet_id AND ars_Active = 1\n", "    group by 1,2\n", "\n", ")\n", "\n", "\"\"\"\n", "current_inv = read_sql_query(current_inv, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "58bb8451-45f1-4db4-b628-08763ec6c4fd", "metadata": {}, "outputs": [], "source": ["merge_df = pd.merge(merge_df, current_inv, on=[\"city_name\", \"item_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "52426cb6-4c68-4f37-9b5a-1cc6d452290c", "metadata": {}, "outputs": [], "source": ["merge_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "8ea9219d-8c8c-45ce-90df-2208b561e06e", "metadata": {}, "outputs": [], "source": ["merge_df = merge_df.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "3013ad58-5606-452e-983e-9c5b793e787f", "metadata": {}, "outputs": [], "source": ["merge_df = merge_df[\n", "    [\n", "        \"date_\",\n", "        \"city_name\",\n", "        \"l2\",\n", "        \"product_id\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"unit\",\n", "        \"product_type\",\n", "        \"seasonal_flag\",\n", "        \"cb_flag\",\n", "        \"active_outlets\",\n", "        \"net_inv\",\n", "        \"category_gmv\",\n", "        \"category_retained_margin\",\n", "        \"category_customers\",\n", "        \"category_checkout_carts\",\n", "        \"category_quants\",\n", "        \"wlp\",\n", "        \"overall_gmv\",\n", "        \"overall_customers\",\n", "        \"overall_checkout_carts\",\n", "        \"dump_quantity\",\n", "        \"dump_value\",\n", "        \"wtd_avail\",\n", "        \"avail\",\n", "        \"qng_complaints\",\n", "        \"indent\",\n", "        \"grn_qty\",\n", "        \"impressions\",\n", "        \"atc\",\n", "        \"wtd_avail_9pm\",\n", "        \"no_stores\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "3f4cf193-d80f-4897-869e-3b91a7c2e917", "metadata": {}, "outputs": [], "source": ["merge_df[\"week_number\"] = merge_df[\"date_\"].dt.isocalendar().week\n", "merge_df[\"month\"] = merge_df[\"date_\"].dt.month"]}, {"cell_type": "code", "execution_count": null, "id": "1f71fc70-f262-4e08-bfff-aa4de8f40f4a", "metadata": {}, "outputs": [], "source": ["merge_df = merge_df.sort_values(by=[\"category_quants\", \"date_\"], ascending=[False, False])"]}, {"cell_type": "code", "execution_count": null, "id": "d85123eb-2c84-409a-9310-d1c6ce1f01a3", "metadata": {}, "outputs": [], "source": ["merge_df[\"updated_at\"] = current_time"]}, {"cell_type": "code", "execution_count": null, "id": "32a42558-cff0-492a-8a7b-c777efb823e5", "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.to_sheets(\n", "        merge_df,\n", "        \"1uZpJuKXpVPsjj7a3duioNUzVMHXZd0qldQgSGQ2wErs\",\n", "        \"raw\",\n", "    )\n", "except:\n", "    print(\"nai hua\")"]}, {"cell_type": "code", "execution_count": null, "id": "3f3504e1-0f06-412d-922c-fd6b41947ef1", "metadata": {}, "outputs": [], "source": ["time.sleep(60)"]}, {"cell_type": "code", "execution_count": null, "id": "94f553a5-1bd6-4a4f-b027-a9b9c3f26c3b", "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.to_sheets(\n", "        merge_df,\n", "        \"1uZpJuKXpVPsjj7a3duioNUzVMHXZd0qldQgSGQ2wErs\",\n", "        \"raw\",\n", "    )\n", "except:\n", "    print(\"nai hua\")"]}, {"cell_type": "code", "execution_count": null, "id": "2d809f71-89e1-408f-95c3-ad078058e0c4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "afa78f66-cee7-4c42-b1b9-96412147fe35", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "be69cb47-a40e-473c-aa59-636ef00e9c05", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7870a371-1962-4bbf-bc7b-dad713ef4971", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7c718017-88b5-4bec-a430-f816d27c2187", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}