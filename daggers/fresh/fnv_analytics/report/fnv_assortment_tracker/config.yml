alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 3
dag_name: fnv_assortment_tracker
dag_type: report
escalation_priority: low
execution_timeout: 4320
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: fresh
notebooks:
- executor_config:
    load_type: low
    node_type: spot
  name: assortment_tracker
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: low
    node_type: spot
  name: seasonal_dasboard
  parameters: null
  retries: 2
  tag: first
owner:
  email: <EMAIL>
  slack_id: U06TVCBNG0P
path: fresh/fnv_analytics/report/fnv_assortment_tracker
paused: false
pool: fresh_pool
project_name: fnv_analytics
schedule:
  end_date: '2025-09-12T00:00:00'
  interval: 30 3,5,8,10,12,15 * * *
  start_date: '2025-06-15T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 1
