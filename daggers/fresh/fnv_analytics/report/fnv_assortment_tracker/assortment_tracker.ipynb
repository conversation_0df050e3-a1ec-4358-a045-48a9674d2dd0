{"cells": [{"cell_type": "code", "execution_count": null, "id": "2ce28f26-3dd0-4196-9a2f-f800fa44ff88", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import time\n", "from datetime import datetime, timedelta, date\n", "import gc\n", "\n", "# !pip install pandasql\n", "# import pandasql as ps"]}, {"cell_type": "code", "execution_count": null, "id": "0edc409a-8608-4c9e-b6a4-9bb809665993", "metadata": {"tags": []}, "outputs": [], "source": ["trino = pb.get_connection(\"[Warehouse] Trino\")\n", "# redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "# retail = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "id": "f22d2937-2c8b-4784-8ac6-1ae3ffc9bc07", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "96d4b00f-de39-4a9f-98ec-c732e9c9e47a", "metadata": {}, "outputs": [], "source": ["pd.options.mode.chained_assignment = None  # default='warn'"]}, {"cell_type": "code", "execution_count": null, "id": "25a5b471-ed30-4a49-a325-425af559b283", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib"]}, {"cell_type": "code", "execution_count": null, "id": "0a1f055d-a502-416f-ae17-a1b9c8790635", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pytz\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")\n", "\n", "current_time = datetime.now(IST)\n", "\n", "current_hour = pd.to_datetime(current_time).hour\n", "\n", "today_date = current_time.strftime(\"%Y-%m-%d\")\n", "yesterday_date = (current_time - timedelta(days=1)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "661e7d5d-013c-4a1f-ac8b-331edb5d42b3", "metadata": {}, "outputs": [], "source": ["seasonal_tag = \"\"\"\n", "select distinct city as city_name, item_id\n", "from ( \n", "select cl.name as city,\n", "icd.item_id,\n", "icd.name as item_name,\n", "    cat.id as category_id,\n", "    cat.name as cat_name,\n", "    pt.name as product_type\n", "    from  cms.gr_product_category_mapping pcm\n", "    inner join  cms.gr_category cat on cat.id=pcm.category_id\n", "    inner join  cms.gr_product p on p.id=pcm.product_id\n", "    left join (select id, name from cms.gr_product_type) pt on pt.id = p.type_id\n", "    inner join  rpc.item_product_mapping ipm on ipm.product_id = pcm.product_id\n", "    INNER JOIN  rpc.item_category_details icd ON icd.item_id = ipm.item_id AND icd.l0_id in (1487)\n", "    INNER JOIN  rpc.product_facility_master_assortment pfma ON pfma.item_id = ipm.item_id \n", "                AND pfma.master_assortment_substate_id = 1 AND pfma.active = 1 and pfma.lake_active_record\n", "    inner JOIN  retail.console_outlet co on co.facility_id = pfma.facility_id and co.active = 1 and co.business_type_id = 7 and co.lake_active_record\n", "    left join  retail.console_location cl on cl.id = co.tax_location_id and cl.lake_active_record\n", "    where p.enabled_flag=true\n", "    and cat.enabled_flag=true\n", "        and pcm.lake_active_record\n", "    and cat.lake_active_record\n", "    and p.lake_active_record\n", "    and cat.id in (482)\n", "    and is_primary = false\n", "    union\n", "    select 'Pan India' as city,\n", "    icd.item_id,\n", "    icd.name as item_name,\n", "    cat.id as category_id,\n", "    cat.name as cat_name,\n", "    pt.name as product_type\n", "    from  cms.gr_product_category_mapping pcm\n", "    inner join  cms.gr_category cat on cat.id=pcm.category_id\n", "    inner join  cms.gr_product p on p.id=pcm.product_id\n", "    left join (select id, name from cms.gr_product_type) pt on pt.id = p.type_id\n", "    inner join  rpc.item_product_mapping ipm on ipm.product_id = pcm.product_id\n", "    INNER JOIN  rpc.item_category_details icd ON icd.item_id = ipm.item_id AND icd.l0_id in (1487)\n", "    INNER JOIN  rpc.product_facility_master_assortment pfma ON pfma.item_id = ipm.item_id \n", "                AND pfma.master_assortment_substate_id = 1 AND pfma.active = 1 and pfma.lake_active_record\n", "    inner JOIN  retail.console_outlet co on co.facility_id = pfma.facility_id and co.active = 1 and co.business_type_id = 7 and co.lake_active_record\n", "    left join  retail.console_location cl on cl.id = co.tax_location_id and cl.lake_active_record\n", "    where p.enabled_flag=true\n", "    and cat.enabled_flag=true\n", "        and pcm.lake_active_record\n", "    and cat.lake_active_record\n", "    and p.lake_active_record\n", "    and cat.id in (482)\n", "    and is_primary = false)\n", "\"\"\"\n", "seasonal_tag = read_sql_query(seasonal_tag, trino)\n", "seasonal_tag[\"seasonal_flag\"] = 1\n", "seasonal_tag[\"date_\"] = yesterday_date"]}, {"cell_type": "code", "execution_count": null, "id": "c015df91-e954-430f-9ea2-17743a8244c4", "metadata": {}, "outputs": [], "source": ["sales_query = f\"\"\"with perishable_skus_base as \n", "(\n", "    select distinct ma.item_id, icd.name as item_name,\n", "    pt.name as product_type,\n", "    l2\n", "        from  rpc.product_product ma \n", "        JOIN (\n", "                SELECT item_id, MAX(id) AS id\n", "                FROM lake_rpc.product_product\n", "                WHERE active = 1 and approved = 1\n", "                GROUP BY 1\n", "            ) b ON ma.item_id = b.item_id\n", "            join rpc.item_category_details icd on icd.item_id = ma.item_id and icd.l0_id in (1487)\n", "        left join (select item_id, product_id from rpc.item_product_mapping) ipm on ipm.item_id = icd.item_id\n", "        left join (select id, type_id from cms.gr_product) gp on gp.id = ipm.product_id\n", "        left join (select id, name from cms.gr_product_type) pt on pt.id = gp.type_id\n", "            -- where ma.storage_type in (3,7) and l1_id not in (183))\n", "        \n", "            ),\n", "            \n", "perishable_skus as (Select * from perishable_skus_base),\n", "\n", "base_overall as\n", "(SELECT \n", "  date(oid.order_create_dt_ist) as date_,\n", "  oid.city_name,\n", "sum(oid.unit_selling_price*oid.procured_quantity) as overall_gmv,\n", "SUM(oid.total_retained_margin*avg_selling_price_ratio) AS overall_retained_margin,\n", "    count(distinct oid.dim_customer_key) as overall_customers,\n", "    COUNT( distinct oid.cart_id) AS overall_checkout_carts,\n", "     SUM( oid.procured_quantity*pl.multiplier) AS overall_quant\n", "FROM dwh.fact_sales_order_item_details oid\n", "    JOIN (SELECT DISTINCT ipr.product_id, \n", "            case when ipr.item_id is null then ipom_0.item_id else ipr.item_id end as item_id,\n", "            case when ipr.item_id is not null then COALESCE(ipom.multiplier,1) else\n", "                COALESCE(ipom_0.multiplier,1) end AS multiplier, \n", "                                COALESCE(ipom_0.avg_selling_price_ratio,1) as avg_selling_price_ratio\n", "                    FROM lake_rpc.item_product_mapping ipr\n", "            left join dwh.dim_item_product_offer_mapping ipom\n", "                    on  ipr.product_id = ipom.product_id \n", "                    and ipr.item_id = ipom.item_id and ipom.is_current\n", "            left join dwh.dim_item_product_offer_mapping ipom_0\n", "                    on ipr.product_id = ipom_0.product_id and ipom_0.is_current) pl ON pl.product_id = oid.product_id\n", "Join rpc.item_category_details icd on icd.item_id = pl.item_id \n", "where oid.order_create_dt_ist >= current_date - interval '1' day\n", "    and oid.order_type in ('RetailForwardOrder', 'DropShippingForwardOrder')\n", "    and oid.order_current_status = ('DELIVERED')\n", "        and is_internal_order = false\n", " GROUP BY 1,2\n", "    order by 1 desc,2),\n", "\n", "base_category as\n", "(SELECT \n", "  date(oid.order_create_dt_ist) as date_, \n", "  oid.city_name,\n", "  ps.l2,\n", "  ps.item_id,\n", "    sum(oid.unit_selling_price*oid.procured_quantity) as category_gmv,\n", "       sum(oid.total_weighted_landing_price * avg_selling_price_ratio) as wlp,\n", "   SUM(oid.total_retained_margin*avg_selling_price_ratio) AS category_retained_margin,\n", "    count(distinct oid.dim_customer_key) as category_customers,\n", "    COUNT( distinct oid.cart_id) AS category_checkout_carts,\n", "    SUM( oid.procured_quantity*pl.multiplier) AS category_quants\n", "FROM dwh.fact_sales_order_item_details oid\n", "    JOIN (SELECT DISTINCT ipr.product_id, \n", "            case when ipr.item_id is null then ipom_0.item_id else ipr.item_id end as item_id,\n", "            case when ipr.item_id is not null then COALESCE(ipom.multiplier,1) else\n", "                COALESCE(ipom_0.multiplier,1) end AS multiplier, \n", "                                COALESCE(ipom_0.avg_selling_price_ratio,1) as avg_selling_price_ratio\n", "                    FROM lake_rpc.item_product_mapping ipr\n", "            left join dwh.dim_item_product_offer_mapping ipom\n", "                    on  ipr.product_id = ipom.product_id \n", "                    and ipr.item_id = ipom.item_id and ipom.is_current\n", "            left join dwh.dim_item_product_offer_mapping ipom_0\n", "                    on ipr.product_id = ipom_0.product_id and ipom_0.is_current) pl ON pl.product_id = oid.product_id\n", "JOIN perishable_skus ps on ps.item_id = pl.item_id\n", "where oid.order_create_dt_ist >= current_date - interval '1' day\n", "    and oid.order_type in ('RetailForwardOrder', 'DropShippingForwardOrder')\n", "    and oid.order_current_status = ('DELIVERED')\n", "    and is_internal_order = false\n", "GROUP BY 1,2,3,4),\n", "\n", "final_base as (select base.*, overall_gmv, overall_customers, overall_checkout_carts\n", "from base_category base \n", "left join base_overall overall on base.date_ = overall.date_ and base.city_name = overall.city_name)\n", "\n", "select * from final_base\n", "\"\"\"\n", "sales_query = read_sql_query(sales_query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "15d4d732-6bc5-4f25-b0cb-cd670d1a0eeb", "metadata": {}, "outputs": [], "source": ["sales_query"]}, {"cell_type": "code", "execution_count": null, "id": "109fd889-8e78-4e73-bc5a-2dd255954277", "metadata": {}, "outputs": [], "source": ["sales_query[\"date_\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "713d84aa-1a44-4489-a1cd-79f4218eb1fa", "metadata": {}, "outputs": [], "source": ["# sales_query.to_csv(\"sales_query.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "9fe3b0f0-7e9c-491d-9905-82986dfa49c8", "metadata": {}, "outputs": [], "source": ["bl_dump_ = f\"\"\"\n", "    with perishable_skus as (select distinct ma.item_id, icd.name as item_name, icd.l2\n", "        from  rpc.product_product ma \n", "        JOIN (\n", "                SELECT item_id, MAX(id) AS id\n", "                FROM rpc.product_product\n", "                WHERE active = 1 AND approved = 1 \n", "                GROUP BY 1\n", "            ) b ON ma.item_id = b.item_id and ma.id = b.id\n", "            join rpc.item_category_details icd on icd.item_id = ma.item_id and l0_id in (1487)),\n", "        \n", " dump AS (\n", "        SELECT DATE(pos_timestamp + interval '330' minute) AS date_, outlet_id, item_id, SUM(il.\"delta\") AS dump_quantity, SUM(il.\"delta\" * il.weighted_lp) AS dump_value\n", "        FROM ims.ims_inventory_log il\n", "        JOIN (\n", "            SELECT DISTINCT item_id, variant_id \n", "            FROM rpc.product_product\n", "        ) rpp ON rpp.variant_id = il.variant_id\n", "        WHERE pos_timestamp + interval '330' minute >= cast(current_date - interval '1' day as timestamp)\n", "        and insert_ds_ist >= cast(current_date - interval '1' day as varchar)\n", "        AND inventory_update_type_id IN (11,12,13,64)\n", "        GROUP BY 1,2,3\n", "    ),\n", "    \n", "    pre_summary AS (    \n", "        SELECT date_, Case when cl.name in ('Gurgaon') then 'HR-NCR' when cl.name in ('Ghaziabad') then 'UP-NCR' else cl.name end as city_name, l2, a.item_id, sum(dump_quantity) as dump_quantity, sum(dump_value) as dump_value\n", "        FROM dump a\n", "        JOIN retail.console_outlet co ON co.id = outlet_id AND co.active = 1 AND co.business_type_id = 7 and lake_active_record\n", "        JOIN retail.console_location  cl on cl.id = co.tax_location_id\n", "        inner join perishable_skus psku on psku.item_id = a.item_id\n", "        and date_ >= current_date - interval '1' day\n", "        group by 1,2,3,4\n", "    )\n", "    \n", "    select * from pre_summary\n", "\n", "\"\"\"\n", "bl_dump_ = read_sql_query(bl_dump_, trino)\n", "bl_dump_[\"dump_value\"] = bl_dump_[\"dump_value\"].astype(float)\n", "bl_dump_[\"dump_quantity\"] = bl_dump_[\"dump_quantity\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "505bc68f-6026-49a8-b93a-4fda73ab8595", "metadata": {}, "outputs": [], "source": ["bl_dump_[\"date_\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "16d23683-ee6e-42f8-8057-0bfa1e66a3d6", "metadata": {}, "outputs": [], "source": ["bl_dump_"]}, {"cell_type": "code", "execution_count": null, "id": "2bbbf158-f75f-4ee2-a801-e15753052663", "metadata": {}, "outputs": [], "source": ["wtd_avail = f\"\"\"\n", "WITH     perishable_skus AS (\n", "        SELECT DISTINCT ma.item_id AS item_id, icd.name AS item_name, l2\n", "        FROM rpc.product_product ma\n", "        JOIN (\n", "            SELECT item_id, MAX(id) AS id\n", "            FROM rpc.product_product\n", "            WHERE active = 1 and approved = 1\n", "            GROUP BY 1\n", "        ) b ON ma.item_id = b.item_id and ma.id = b.id\n", "        JOIN rpc.item_category_details icd ON icd.item_id = ma.item_id\n", "        WHERE l0_id in (1487)\n", "\n", "    ),\n", "\n", "active_outlets as (select distinct outlet_id,facility_id from\n", "    (select date(cart_checkout_ts_ist) as date_, outlet_id, co.facility_id, count(distinct cart_id) as carts\n", "    from dwh.fact_sales_order_item_details od\n", "    left join retail.console_outlet co on co.id = od.outlet_id and co.business_type_id = 7 and co.active = 1\n", "    where procured_quantity >0\n", "    and order_create_dt_ist >= current_date - interval '3' day\n", "    group by 1,2,3\n", "    )\n", "where carts > 10),\n", "\n", "base_pre AS (\n", "select \n", "            snapshot_date_ist as date_,\n", "            case when minute(updated_till) = 30 then hour(updated_till) else hour(updated_till) - 1 end as hour_,\n", "            city,\n", "            outlet_id,\n", "            co.facility_id,\n", "            item_id,\n", "            max(current_inventory) as current_inv,\n", "            max(updated_till) as updated_at,\n", "            case when max(current_inventory) > 0 then 1 else 0 end as is_available\n", "        from dwh.agg_hourly_outlet_item_inventory his\n", "        left join retail.console_outlet co on co.id = his.outlet_id and co.active = 1 and co.lake_active_record and business_type_id = 7\n", "        where snapshot_date_ist >= current_date - interval '1' day\n", "        and substate_reason = 'Active'\n", "        and outlet_id IN (select distinct outlet_id from active_outlets)\n", "        and item_id IN (select distinct item_id from perishable_skus)\n", "        group by 1,2,3,4,5,6\n", "    ),\n", "    \n", "    \n", "    avail_pre as ( \n", "        SELECT date_, hour_, facility_id, outlet_id, item_id, MAX(is_available) AS is_available\n", "    from base_pre \n", "    where hour_ between 6 and 23\n", "        GROUP BY 1,2,3,4,5),\n", "\n", "\n", "    facility_item_avail AS (\n", "        SELECT a.* , cl.name AS city , b.l2\n", "        FROM avail_pre a\n", "        JOIN perishable_skus b ON a.item_id = b.item_id\n", "        JOIN retail.console_outlet co ON co.id = a.outlet_id and co.active = 1 and co.business_type_id = 7 and co.lake_active_record\n", "        LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id\n", "    ),\n", "\n", "\n", "    city_item_weights AS (\n", "        SELECT DISTINCT city, a.item_id, CAST(weights AS DOUBLE) AS ciw\n", "        FROM supply_etls.city_item_weights a\n", "        WHERE a.updated_at IN (SELECT MAX(updated_at) FROM supply_etls.city_item_weights)\n", "        AND item_id IN (select distinct item_id from perishable_skus)\n", "    ),\n", "\n", "    city_facility_weights AS (\n", "        SELECT DISTINCT city, facility_id, CAST(store_weight AS DOUBLE) AS csw\n", "        FROM supply_etls.city_store_weights a\n", "        WHERE a.updated_at IN (SELECT MAX(updated_at) FROM supply_etls.city_store_weights)\n", "        AND facility_id IN (select distinct facility_id from active_outlets)\n", "    ),\n", "\n", "    city_hour_weights AS (\n", "        SELECT DISTINCT city, order_hour AS hour_, CAST(weights AS DOUBLE) AS chw\n", "        FROM supply_etls.city_hour_weights a\n", "        WHERE a.updated_at IN (SELECT MAX(updated_at) FROM supply_etls.city_hour_weights)\n", "    ),\n", "\n", "       facility_weights_merge as (SELECT DISTINCT a.city, a.facility_id, date_, a.hour_, l2, a.item_id, is_available, iw.ciw, fw.csw, hw.chw\n", "            FROM facility_item_avail a \n", "            LEFT JOIN city_facility_weights fw ON a.city = fw.city AND a.facility_id = fw.facility_id\n", "            LEFT JOIN city_hour_weights hw ON a.city = hw.city AND a.hour_ = hw.hour_\n", "            LEFT JOIN city_item_weights iw ON a.city = iw.city AND a.item_id = iw.item_id),\n", "            \n", "    pre_final as (\n", "        SELECT city, facility_id, date_, hour_, l2 , item_id,(is_available*1.0000*chw * csw) AS fac_avail , (chw * csw) AS tot_weights\n", "        from facility_weights_merge)\n", "\n", "    select date_ as date_, Case when city in ('Gurgaon') then 'HR-NCR'\n", "    when city in ('Noida','Ghaziabad') then 'UP-NCR' else city end as city_name,\n", "    l2, item_id, sum(fac_avail) *1.00000/ sum(tot_weights) as wtd_avail from pre_final\n", "    group by 1,2,3,4\n", "\"\"\"\n", "wtd_avail = read_sql_query(wtd_avail, trino)\n", "wtd_avail.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "dcd87cb4-772f-451b-a820-3cd807ccb99c", "metadata": {}, "outputs": [], "source": ["wtd_avail[\"date_\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "5227acbb-ed5d-4a45-8dc4-bead8c502467", "metadata": {}, "outputs": [], "source": ["unique_sale = sales_query[[\"date_\", \"city_name\", \"l2\", \"item_id\"]].drop_duplicates()\n", "unique_dump = bl_dump_[[\"date_\", \"city_name\", \"l2\", \"item_id\"]].drop_duplicates()\n", "unique_avai = wtd_avail[[\"date_\", \"city_name\", \"l2\", \"item_id\"]].drop_duplicates()\n", "\n", "union_base = pd.concat([unique_sale, unique_dump, unique_avai])\n", "union_base = union_base.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "29bf3819-01e7-4775-ac8f-639c5470d164", "metadata": {}, "outputs": [], "source": ["merge_base = pd.merge(\n", "    union_base, sales_query, on=[\"date_\", \"city_name\", \"l2\", \"item_id\"], how=\"left\"\n", ")\n", "merge_base = pd.merge(merge_base, bl_dump_, on=[\"date_\", \"city_name\", \"l2\", \"item_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "b90808b6-b9f7-48f2-b183-6983b5a35aa2", "metadata": {}, "outputs": [], "source": ["merge_df = pd.merge(merge_base, wtd_avail, on=[\"date_\", \"city_name\", \"l2\", \"item_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "67fe985f-a678-4afc-9b89-c618eca0b048", "metadata": {}, "outputs": [], "source": ["del [bl_dump_, sales_query, unique_sale, unique_dump, union_base, wtd_avail]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "f7f93346-adb7-46ce-b89a-db4bdae62d8f", "metadata": {}, "outputs": [], "source": ["avail = f\"\"\"\n", "WITH     perishable_skus AS (\n", "        SELECT DISTINCT ma.item_id AS item_id, icd.name AS item_name, l2\n", "        FROM rpc.product_product ma\n", "        JOIN (\n", "            SELECT item_id, MAX(id) AS id\n", "            FROM rpc.product_product\n", "            WHERE active = 1 and approved = 1\n", "            GROUP BY 1\n", "        ) b ON ma.item_id = b.item_id and ma.id = b.id\n", "        JOIN rpc.item_category_details icd ON icd.item_id = ma.item_id\n", "        WHERE l0_id in (1487)\n", "    ),\n", "\n", "active_outlets as (select distinct outlet_id,facility_id from\n", "    (select date(cart_checkout_ts_ist) as date_, outlet_id, co.facility_id, count(distinct cart_id) as carts\n", "    from dwh.fact_sales_order_item_details od\n", "    left join retail.console_outlet co on co.id = od.outlet_id and co.business_type_id = 7 and co.active = 1\n", "    where procured_quantity >0\n", "    and order_create_dt_ist >= current_date - interval '3' day\n", "    group by 1,2,3\n", "    )\n", "where carts > 10),\n", "\n", "base_pre AS (\n", "select \n", "            snapshot_date_ist as date_,\n", "            case when minute(updated_till) = 30 then hour(updated_till) else hour(updated_till) - 1 end as hour_,\n", "            city,\n", "            outlet_id,\n", "            co.facility_id,\n", "            item_id,\n", "            max(current_inventory) as current_inv,\n", "            max(updated_till) as updated_at,\n", "            case when max(current_inventory) > 0 then 1 else 0 end as is_available\n", "        from dwh.agg_hourly_outlet_item_inventory his\n", "        left join retail.console_outlet co on co.id = his.outlet_id and co.active = 1 and co.lake_active_record and business_type_id = 7\n", "        where snapshot_date_ist = current_date\n", "        and substate_reason = 'Active'\n", "        and outlet_id IN (select distinct outlet_id from active_outlets)\n", "        and item_id IN (select distinct item_id from perishable_skus)\n", "        group by 1,2,3,4,5,6\n", "    ),\n", "    \n", "    avail_pre as ( \n", "        SELECT date_, hour_, facility_id, outlet_id, item_id, MAX(is_available) AS is_available\n", "    from base_pre \n", "    where hour_ BETWEEN 6 AND 23\n", "        GROUP BY 1,2,3,4,5),\n", "\n", "\n", "    facility_item_avail AS (\n", "        SELECT a.date_,a.hour_,a.facility_id,a.outlet_id, a.item_id, cl.name AS city , b.l2, is_available, 1 as total\n", "        FROM avail_pre a\n", "        JOIN perishable_skus b ON a.item_id = b.item_id\n", "        JOIN retail.console_outlet co ON co.id = a.outlet_id and co.active = 1 and co.business_type_id = 7 and co.lake_active_record\n", "        LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id\n", "    )\n", "\n", "    select date_, \n", "    Case when city in ('Gurgaon') then 'HR-NCR'\n", "    when city in ('Noida','Ghaziabad') then 'UP-NCR' else city end as city_name,\n", "    l2, \n", "    item_id,\n", "    sum(is_available) *1.00000/ sum(total) as avail \n", "    from facility_item_avail\n", "    group by 1,2,3,4\n", "\"\"\"\n", "avail = read_sql_query(avail, trino)\n", "avail.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "c88e30a2-931c-4387-a193-da614bdf3190", "metadata": {}, "outputs": [], "source": ["avail[\"date_\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "4669f624-3725-464a-b2aa-ca430acb1305", "metadata": {}, "outputs": [], "source": ["merge_df = pd.merge(merge_df, avail, on=[\"date_\", \"city_name\", \"l2\", \"item_id\"], how=\"left\")\n", "del [avail]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "c7b077e9-e82e-48c6-af15-d60829353e34", "metadata": {}, "outputs": [], "source": ["avail_at_9pm = f\"\"\"\n", "WITH     perishable_skus AS (\n", "        SELECT DISTINCT ma.item_id AS item_id, icd.name AS item_name, l2\n", "        FROM rpc.product_product ma\n", "        JOIN (\n", "            SELECT item_id, MAX(id) AS id\n", "            FROM rpc.product_product\n", "            WHERE active = 1 and approved = 1\n", "            GROUP BY 1\n", "        ) b ON ma.item_id = b.item_id and ma.id = b.id\n", "        JOIN rpc.item_category_details icd ON icd.item_id = ma.item_id\n", "        WHERE l0_id in (1487)\n", "\n", "    ),\n", "\n", "active_outlets as (select distinct outlet_id,facility_id from\n", "    (select date(cart_checkout_ts_ist) as date_, outlet_id, co.facility_id, count(distinct cart_id) as carts\n", "    from dwh.fact_sales_order_item_details od\n", "    left join retail.console_outlet co on co.id = od.outlet_id and co.business_type_id = 7 and co.active = 1\n", "    where procured_quantity >0\n", "    and order_create_dt_ist >= current_date - interval '1' day\n", "    group by 1,2,3\n", "    )\n", "where carts > 10),\n", "\n", "base_pre AS (\n", "select \n", "            snapshot_date_ist as date_,\n", "            case when minute(updated_till) = 30 then hour(updated_till) else hour(updated_till) - 1 end as hour_,\n", "            city,\n", "            outlet_id,\n", "            co.facility_id,\n", "            item_id,\n", "            max(current_inventory) as current_inv,\n", "            max(updated_till) as updated_at,\n", "            case when max(current_inventory) > 0 then 1 else 0 end as is_available\n", "        from dwh.agg_hourly_outlet_item_inventory his\n", "        left join retail.console_outlet co on co.id = his.outlet_id and co.active = 1 and co.lake_active_record and business_type_id = 7\n", "        where snapshot_date_ist >= current_date - interval '1' day\n", "        and substate_reason = 'Active'\n", "        and outlet_id IN (select distinct outlet_id from active_outlets)\n", "        and item_id IN (select distinct item_id from perishable_skus)\n", "        group by 1,2,3,4,5,6\n", "    ),\n", "    \n", "    \n", "    avail_pre as ( \n", "        SELECT date_, hour_, facility_id, outlet_id, item_id, MAX(is_available) AS is_available\n", "    from base_pre \n", "    where hour_ = 21\n", "        GROUP BY 1,2,3,4,5),\n", "\n", "\n", "    facility_item_avail AS (\n", "        SELECT a.* , cl.name AS city , b.l2\n", "        FROM avail_pre a\n", "        JOIN perishable_skus b ON a.item_id = b.item_id\n", "        JOIN retail.console_outlet co ON co.id = a.outlet_id and co.active = 1 and co.business_type_id = 7 and co.lake_active_record\n", "        LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id\n", "    ),\n", "\n", "\n", "    city_item_weights AS (\n", "        SELECT DISTINCT city, a.item_id, CAST(weights AS DOUBLE) AS ciw\n", "        FROM supply_etls.city_item_weights a\n", "        WHERE a.updated_at IN (SELECT MAX(updated_at) FROM supply_etls.city_item_weights)\n", "        AND item_id IN (select distinct item_id from perishable_skus)\n", "    ),\n", "\n", "    city_facility_weights AS (\n", "        SELECT DISTINCT city, facility_id, CAST(store_weight AS DOUBLE) AS csw\n", "        FROM supply_etls.city_store_weights a\n", "        WHERE a.updated_at IN (SELECT MAX(updated_at) FROM supply_etls.city_store_weights)\n", "        AND facility_id IN (select distinct facility_id from active_outlets)\n", "    ),\n", "\n", "    city_hour_weights AS (\n", "        SELECT DISTINCT city, order_hour AS hour_, CAST(weights AS DOUBLE) AS chw\n", "        FROM supply_etls.city_hour_weights a\n", "        WHERE a.updated_at IN (SELECT MAX(updated_at) FROM supply_etls.city_hour_weights)\n", "    ),\n", "\n", "       facility_weights_merge as (SELECT DISTINCT a.city, a.facility_id, date_, a.hour_, l2, a.item_id, is_available, iw.ciw, fw.csw, hw.chw\n", "            FROM facility_item_avail a \n", "            LEFT JOIN city_facility_weights fw ON a.city = fw.city AND a.facility_id = fw.facility_id\n", "            LEFT JOIN city_hour_weights hw ON a.city = hw.city AND a.hour_ = hw.hour_\n", "            LEFT JOIN city_item_weights iw ON a.city = iw.city AND a.item_id = iw.item_id),\n", "            \n", "    pre_final as (\n", "        SELECT city, facility_id, date_, hour_, l2 , item_id,(is_available*1.0000*chw * csw) AS fac_avail , (chw * csw) AS tot_weights\n", "        from facility_weights_merge)\n", "\n", "    select date_ as date_, Case when city in ('Gurgaon') then 'HR-NCR'\n", "    when city in ('Noida','Ghaziabad') then 'UP-NCR' else city end as city_name,\n", "    l2, item_id, sum(fac_avail) *1.00000/ sum(tot_weights) as wtd_avail_9pm from pre_final\n", "    group by 1,2,3,4\n", "\"\"\"\n", "avail_at_9pm = read_sql_query(avail_at_9pm, trino)\n", "avail_at_9pm.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "d440349a-ec58-428f-837d-12a0ce0e5878", "metadata": {}, "outputs": [], "source": ["avail_at_9pm[\"date_\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "c8af614b-ad92-488b-9b1e-fee0f894ab07", "metadata": {}, "outputs": [], "source": ["merge_df = pd.merge(merge_df, avail_at_9pm, on=[\"date_\", \"city_name\", \"l2\", \"item_id\"], how=\"left\")\n", "del [avail_at_9pm]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "db59e129-19cc-41ca-9dee-61d484ccbf52", "metadata": {}, "outputs": [], "source": ["qng = f\"\"\"\n", "WITH  assortment_base AS (\n", "        SELECT DISTINCT ma.item_id AS item_id, icd.name AS item_name, l2\n", "        FROM rpc.product_product ma\n", "        JOIN (\n", "            SELECT item_id, MAX(id) AS id\n", "            FROM rpc.product_product\n", "            WHERE active = 1 and approved = 1\n", "            GROUP BY 1\n", "        ) b ON ma.item_id = b.item_id and ma.id = b.id\n", "        JOIN rpc.item_category_details icd ON icd.item_id = ma.item_id\n", "        WHERE l0_id in (1487)\n", "    )\n", "    \n", "select date(oid.order_create_dt_ist) as date_,\n", "oid.city_name,\n", "ab.l2,\n", "pl.item_id,\n", "count(distinct session_id) as qng_complaints\n", "from cd_etls.item_wise_reso cd\n", "join dwh.fact_sales_order_item_details oid on oid.order_id = cd.order_id and oid.order_type in ('RetailForwardOrder', 'DropShippingForwardOrder') and oid.order_current_status = ('DELIVERED') and oid.product_id = cd.product_id\n", "JOIN dwh.dim_item_product_offer_mapping pl ON pl.product_id = oid.product_id and pl.is_current\n", "JOIN assortment_base ab on ab.item_id = pl.item_id \n", "where reason IN ('Items','MDND')\n", "and complaint_type in ('COMPLAINT_SMELL_OR_TASTE_ISSUE','COMPLAINT_ROTTEN_ITEM','COMPLAINT_DAMAGED_ITEM','COMPLAINT_EXPIRED_ITEM','COMPLAINT_NEAR_EXPIRY','COMPLAINT_QUALITY_ISSUE','COMPLAINT_PACKAGING_ISSUE')\n", "and oid.order_create_dt_ist >= current_date - interval '1' day\n", "and cd.reso_date >= current_date - interval '1' day\n", "group by 1,2,3,4\n", "\n", "\"\"\"\n", "qng = read_sql_query(qng, trino)\n", "qng.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "11167d6f-5a3e-44e9-8f63-77784d9174f2", "metadata": {}, "outputs": [], "source": ["qng[\"date_\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "97ab177d-7e6f-4e14-8ed8-ffe6e0b051dd", "metadata": {}, "outputs": [], "source": ["qng[\"date_\"] = pd.to_datetime(qng[\"date_\"])\n", "merge_df[\"date_\"] = pd.to_datetime(merge_df[\"date_\"])\n", "merge_df = pd.merge(merge_df, qng, on=[\"date_\", \"city_name\", \"l2\", \"item_id\"], how=\"left\")\n", "del [qng]\n", "gc.collect()\n", "merge_df[\"qng_complaints\"] = merge_df[\"qng_complaints\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "da840a55-c0ee-49e4-b020-8245cdea9d7d", "metadata": {}, "outputs": [], "source": ["oos_store_ratio = \"\"\"\n", "with oos as\n", "    (\n", "        select\n", "            date(m.updated_at_ist) date_,\n", "            extract(hour from m.updated_at_ist) as hour_,\n", "    Case when cl.name in ('Gurgaon') then 'HR-NCR'\n", "    when cl.name  in ('Noida','Ghaziabad') then 'UP-NCR' else cl.name  end as city_name,\n", "            m.item_id,\n", "            icd.l2,\n", "            count(distinct case when current_inventory <= 0 then outlet_id end) as no_stores\n", "        from\n", "            supply_etls.hourly_inventory_snapshots m\n", "        inner join rpc.item_category_details icd on icd.item_id = m.item_id and icd.l0_id in (1487)\n", "        join retail.console_outlet co on co.id = m.outlet_id and co.business_type_id = 7\n", "        inner join  supply_etls.outlet_details od on od.hot_outlet_id = m.outlet_id and ars_Check = 1\n", "        join retail.console_location cl on cl.id = co.tax_location_id\n", "        where\n", "            m.updated_at_ist  >= current_date - interval '1' day\n", "            and substate_id = 1\n", "            and date_ist >= current_date - interval '1' day\n", "        group by 1,2,3,4,5\n", "    )\n", "\n", "select\n", "    date_,\n", "    city_name,\n", "    item_id,\n", "    l2,\n", "    no_stores\n", "from oos \n", "where \n", "    hour_ = 9\n", "\"\"\"\n", "oos_store_ratio = read_sql_query(oos_store_ratio, trino)\n", "oos_store_ratio[\"date_\"] = pd.to_datetime(oos_store_ratio[\"date_\"])\n", "oos_store_ratio.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "cf3cb124-092f-4865-83b9-4293b0415214", "metadata": {}, "outputs": [], "source": ["oos_store_ratio[\"date_\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "919805ad-8c88-497d-9e88-3d87df1ffaba", "metadata": {}, "outputs": [], "source": ["merge_df = pd.merge(\n", "    merge_df, oos_store_ratio, on=[\"date_\", \"city_name\", \"l2\", \"item_id\"], how=\"left\"\n", ")\n", "del [oos_store_ratio]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "a9fe7794-6fc3-4eb7-ad8e-d7b10a3928b3", "metadata": {}, "outputs": [], "source": ["impressions = \"\"\"\n", "with sku_base as (\n", "select\n", "icd.l2,\n", "ip.product_id,\n", "ip.item_id\n", "from dwh.dim_item_product_offer_mapping ip\n", "left join dwh.dim_product p on ip.product_id=p.product_id and p.is_current\n", "inner join rpc.item_category_details icd on icd.item_id = ip.item_id and icd.l0_id in (1487)\n", "and ip.is_current\n", "group by 1,2,3 \n", ")\n", ",\n", "imp as (\n", "select\n", "at_date_ist as date_,\n", "traits__city_name as city,\n", "item_id,\n", "s.l2,\n", "count(distinct ed.device_uuid) as impressions\n", "from lake_events.mobile_impression_data ed\n", "join sku_base s on coalesce(ed.properties__product_id,ed.properties__child_widget_id)=cast(s.product_id as varchar)\n", "where at_date_ist >= current_date - interval '1' day\n", "and name in ('Product Shown')\n", "and traits__city_name is not null\n", "and properties__page_name is not null\n", "and properties__page_name <> '#-NA'\n", "group by 1,2,3,4\n", ")\n", ",\n", "atc as (\n", "select\n", "at_date_ist as date_,\n", "traits__city_name as city,\n", "item_id,\n", "s.l2,\n", "-- properties__page_name as page_name,\n", "count(distinct ed.device_uuid) as atc\n", "from lake_events.mobile_event_data ed\n", "join sku_base s on ed.properties__product_id=cast(s.product_id as varchar)\n", "where at_date_ist >= current_date - interval '1' day\n", "and name in ('Product Added')\n", "and ed.device_uuid is not null\n", "and traits__city_name is not null\n", "and properties__page_name is not null\n", "and properties__page_name <> '#-NA'\n", "group by 1,2,3,4\n", "-- limit 1000\n", ")\n", "select\n", "i.date_,\n", "i.city as city_name,\n", "i.item_id,\n", "i.l2,\n", "impressions,\n", "case when atc is null then 0 else atc end as atc\n", "from imp i\n", "left join atc a on i.city=a.city and i.date_=a.date_ and i.item_id=a.item_id\n", "\"\"\"\n", "impressions = read_sql_query(impressions, trino)\n", "impressions[\"date_\"] = pd.to_datetime(impressions[\"date_\"])\n", "impressions.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "1e246136-a0ae-43e6-8a9b-40eebbc0ad0a", "metadata": {}, "outputs": [], "source": ["impressions[\"date_\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "44ebade9-4f1f-4eb2-844b-b81b8c830ebe", "metadata": {}, "outputs": [], "source": ["merge_df = pd.merge(merge_df, impressions, on=[\"date_\", \"city_name\", \"l2\", \"item_id\"], how=\"left\")\n", "del [impressions]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "d6c85f1c-3994-4f8a-b810-4367f1b9137e", "metadata": {}, "outputs": [], "source": ["fills = \"\"\"\n", "select date(date_of_consumption) as date_, city as city_name, \n", "fills.item_id, icd.l2, sum(grn_qty) as grn_qty, sum(indent) as indent \n", "from  supply_etls.fnv_backend_fillrates fills\n", "left join rpc.item_category_details icd on icd.item_id = fills.item_id\n", "where date_of_consumption >= current_date - interval '1' day\n", "group by 1,2,3,4\n", "\"\"\"\n", "fills = read_sql_query(fills, trino)\n", "fills[\"date_\"] = pd.to_datetime(fills[\"date_\"])\n", "fills.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "e1a1fd53-b51d-44e0-9af3-ac3776d6ac1f", "metadata": {}, "outputs": [], "source": ["fills[\"date_\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "8c8a068e-7994-491b-a7c7-625143f7ef73", "metadata": {}, "outputs": [], "source": ["merge_df = pd.merge(merge_df, fills, on=[\"date_\", \"city_name\", \"l2\", \"item_id\"], how=\"left\")\n", "del [fills]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "a515d812-7758-4983-ab33-3adc389b2fff", "metadata": {}, "outputs": [], "source": ["merge_df"]}, {"cell_type": "code", "execution_count": null, "id": "fa1aade5-60f5-4da6-88e0-66d39012d2a5", "metadata": {}, "outputs": [], "source": ["merge_df[\"category_gmv\"] = merge_df[\"category_gmv\"].fillna(0)\n", "merge_df[\"wlp\"] = merge_df[\"wlp\"].fillna(0)\n", "merge_df[\"category_retained_margin\"] = merge_df[\"category_retained_margin\"].fillna(0)\n", "merge_df[\"category_customers\"] = merge_df[\"category_customers\"].fillna(0)\n", "merge_df[\"category_checkout_carts\"] = merge_df[\"category_checkout_carts\"].fillna(0)\n", "merge_df[\"category_quants\"] = merge_df[\"category_quants\"].fillna(0)\n", "merge_df[\"overall_gmv\"] = merge_df[\"overall_gmv\"].fillna(0)\n", "merge_df[\"overall_customers\"] = merge_df[\"overall_customers\"].fillna(0)\n", "merge_df[\"overall_checkout_carts\"] = merge_df[\"overall_checkout_carts\"].fillna(0)\n", "merge_df[\"dump_quantity\"] = merge_df[\"dump_quantity\"].fillna(0)\n", "merge_df[\"dump_value\"] = merge_df[\"dump_value\"].fillna(0)\n", "merge_df[\"wtd_avail\"] = merge_df[\"wtd_avail\"].fillna(0)\n", "merge_df[\"wtd_avail_9pm\"] = merge_df[\"wtd_avail_9pm\"].fillna(0)\n", "merge_df[\"impressions\"] = merge_df[\"impressions\"].fillna(0)\n", "merge_df[\"atc\"] = merge_df[\"atc\"].fillna(0)\n", "merge_df[\"grn_qty\"] = merge_df[\"grn_qty\"].fillna(0)\n", "merge_df[\"indent\"] = merge_df[\"indent\"].fillna(0)\n", "merge_df[\"avail\"] = merge_df[\"avail\"].fillna(0)\n", "merge_df[\"no_stores\"] = merge_df[\"no_stores\"].fillna(0)\n", "merge_df[\"qng_complaints\"] = merge_df[\"qng_complaints\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "74b4d643-a037-48fa-b0ff-014e1d4e2adf", "metadata": {}, "outputs": [], "source": ["merge_df[\"category_gmv\"] = merge_df[\"category_gmv\"].astype(float)\n", "merge_df[\"wlp\"] = merge_df[\"wlp\"].astype(float)\n", "merge_df[\"category_retained_margin\"] = merge_df[\"category_retained_margin\"].astype(float)\n", "merge_df[\"category_customers\"] = merge_df[\"category_customers\"].astype(float)\n", "merge_df[\"category_checkout_carts\"] = merge_df[\"category_checkout_carts\"].astype(float)\n", "merge_df[\"category_quants\"] = merge_df[\"category_quants\"].astype(float)\n", "merge_df[\"overall_gmv\"] = merge_df[\"overall_gmv\"].astype(float)\n", "merge_df[\"overall_customers\"] = merge_df[\"overall_customers\"].astype(float)\n", "merge_df[\"overall_checkout_carts\"] = merge_df[\"overall_checkout_carts\"].astype(float)\n", "merge_df[\"dump_quantity\"] = merge_df[\"dump_quantity\"].astype(float)\n", "merge_df[\"dump_value\"] = merge_df[\"dump_value\"].astype(float)\n", "merge_df[\"wtd_avail\"] = merge_df[\"wtd_avail\"].astype(float)\n", "merge_df[\"wtd_avail_9pm\"] = merge_df[\"wtd_avail_9pm\"].astype(float)\n", "merge_df[\"avail\"] = merge_df[\"avail\"].astype(float)\n", "merge_df[\"no_stores\"] = merge_df[\"no_stores\"].astype(float)\n", "merge_df[\"qng_complaints\"] = merge_df[\"qng_complaints\"].astype(float)\n", "merge_df[\"impressions\"] = merge_df[\"impressions\"].astype(float)\n", "merge_df[\"atc\"] = merge_df[\"atc\"].astype(float)\n", "merge_df[\"grn_qty\"] = merge_df[\"grn_qty\"].astype(float)\n", "merge_df[\"indent\"] = merge_df[\"indent\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "0dc137d4-2066-4aee-ad80-b3a85e9d481c", "metadata": {}, "outputs": [], "source": ["pan_india_df = (\n", "    merge_df.groupby([\"date_\", \"l2\", \"item_id\"])\n", "    .agg(\n", "        {\n", "            \"category_gmv\": \"sum\",\n", "            \"category_retained_margin\": \"sum\",\n", "            \"category_customers\": \"sum\",\n", "            \"category_checkout_carts\": \"sum\",\n", "            \"category_quants\": \"sum\",\n", "            \"wlp\": \"sum\",\n", "            \"overall_gmv\": \"sum\",\n", "            \"overall_customers\": \"sum\",\n", "            \"overall_checkout_carts\": \"sum\",\n", "            \"dump_quantity\": \"sum\",\n", "            \"dump_value\": \"sum\",\n", "            \"wtd_avail\": \"mean\",\n", "            \"wtd_avail_9pm\": \"mean\",\n", "            \"avail\": \"mean\",\n", "            \"qng_complaints\": \"sum\",\n", "            \"no_stores\": \"sum\",\n", "            \"impressions\": \"sum\",\n", "            \"atc\": \"sum\",\n", "            \"grn_qty\": \"sum\",\n", "            \"indent\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "pan_india_df[\"city_name\"] = \"Pan India\""]}, {"cell_type": "code", "execution_count": null, "id": "ecba0c42-d471-4e1a-bc2f-409aaff54986", "metadata": {}, "outputs": [], "source": ["pan_india_df"]}, {"cell_type": "code", "execution_count": null, "id": "ae1de598-d9dd-4cb5-8d6d-56477da914ad", "metadata": {}, "outputs": [], "source": ["pan_india_df = pan_india_df[\n", "    [\n", "        \"date_\",\n", "        \"city_name\",\n", "        \"l2\",\n", "        \"item_id\",\n", "        \"category_gmv\",\n", "        \"category_retained_margin\",\n", "        \"category_customers\",\n", "        \"category_checkout_carts\",\n", "        \"category_quants\",\n", "        \"wlp\",\n", "        \"overall_gmv\",\n", "        \"overall_customers\",\n", "        \"overall_checkout_carts\",\n", "        \"dump_quantity\",\n", "        \"dump_value\",\n", "        \"wtd_avail\",\n", "        \"wtd_avail_9pm\",\n", "        \"avail\",\n", "        \"qng_complaints\",\n", "        \"no_stores\",\n", "        \"impressions\",\n", "        \"atc\",\n", "        \"grn_qty\",\n", "        \"indent\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "cf4b45a2-d060-47d6-8ebb-6567b462ef11", "metadata": {}, "outputs": [], "source": ["merge_df = merge_df[\n", "    [\n", "        \"date_\",\n", "        \"city_name\",\n", "        \"l2\",\n", "        \"item_id\",\n", "        \"category_gmv\",\n", "        \"category_retained_margin\",\n", "        \"category_customers\",\n", "        \"category_checkout_carts\",\n", "        \"category_quants\",\n", "        \"wlp\",\n", "        \"overall_gmv\",\n", "        \"overall_customers\",\n", "        \"overall_checkout_carts\",\n", "        \"dump_quantity\",\n", "        \"dump_value\",\n", "        \"wtd_avail\",\n", "        \"wtd_avail_9pm\",\n", "        \"avail\",\n", "        \"qng_complaints\",\n", "        \"no_stores\",\n", "        \"impressions\",\n", "        \"atc\",\n", "        \"grn_qty\",\n", "        \"indent\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "a49ff84b-3b72-4dcb-adac-cace623e80cb", "metadata": {}, "outputs": [], "source": ["merge_df = pd.concat([merge_df, pan_india_df])\n", "\n", "merge_df = merge_df.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "83ceb75e-e678-47bf-94ff-74152730073d", "metadata": {}, "outputs": [], "source": ["merge_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "b7721fb4-e6ce-4d66-825a-a3731f6a4720", "metadata": {}, "outputs": [], "source": ["# merge_df = merge_df[merge_df[\"date_\"] == today_date]"]}, {"cell_type": "code", "execution_count": null, "id": "4d4ab79d-2750-48d1-a39a-5b0b1bf34fd3", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"fnv_city_item_data\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"date_\",\n", "            \"type\": \"date\",\n", "            \"description\": \"date_\",\n", "        },\n", "        {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"city_name\"},\n", "        {\n", "            \"name\": \"l2\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"l2\",\n", "        },\n", "        {\n", "            \"name\": \"item_id\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"item_id\",\n", "        },\n", "        {\"name\": \"category_gmv\", \"type\": \"double\", \"description\": \"category_gmv\"},\n", "        {\n", "            \"name\": \"category_retained_margin\",\n", "            \"type\": \"double\",\n", "            \"description\": \"category_retained_margin\",\n", "        },\n", "        {\n", "            \"name\": \"category_customers\",\n", "            \"type\": \"double\",\n", "            \"description\": \"category_customers\",\n", "        },\n", "        {\n", "            \"name\": \"category_checkout_carts\",\n", "            \"type\": \"double\",\n", "            \"description\": \"category_checkout_carts\",\n", "        },\n", "        {\"name\": \"category_quants\", \"type\": \"double\", \"description\": \"category_quants\"},\n", "        {\n", "            \"name\": \"wlp\",\n", "            \"type\": \"double\",\n", "            \"description\": \"wlp\",\n", "        },\n", "        {\n", "            \"name\": \"overall_gmv\",\n", "            \"type\": \"double\",\n", "            \"description\": \"overall_gmv\",\n", "        },\n", "        {\n", "            \"name\": \"overall_customers\",\n", "            \"type\": \"double\",\n", "            \"description\": \"overall_customers\",\n", "        },\n", "        {\n", "            \"name\": \"overall_checkout_carts\",\n", "            \"type\": \"double\",\n", "            \"description\": \"overall_checkout_carts\",\n", "        },\n", "        {\n", "            \"name\": \"dump_quantity\",\n", "            \"type\": \"double\",\n", "            \"description\": \"dump_quantity\",\n", "        },\n", "        {\n", "            \"name\": \"dump_value\",\n", "            \"type\": \"double\",\n", "            \"description\": \"dump_value\",\n", "        },\n", "        {\n", "            \"name\": \"wtd_avail\",\n", "            \"type\": \"double\",\n", "            \"description\": \"wtd_avail\",\n", "        },\n", "        {\n", "            \"name\": \"wtd_avail_9pm\",\n", "            \"type\": \"double\",\n", "            \"description\": \"wtd_avail_9pm\",\n", "        },\n", "        {\n", "            \"name\": \"avail\",\n", "            \"type\": \"double\",\n", "            \"description\": \"avail\",\n", "        },\n", "        {\n", "            \"name\": \"qng_complaints\",\n", "            \"type\": \"double\",\n", "            \"description\": \"qng_complaints\",\n", "        },\n", "        {\n", "            \"name\": \"no_stores\",\n", "            \"type\": \"double\",\n", "            \"description\": \"no_stores\",\n", "        },\n", "        {\n", "            \"name\": \"impressions\",\n", "            \"type\": \"double\",\n", "            \"description\": \"impressions\",\n", "        },\n", "        {\n", "            \"name\": \"atc\",\n", "            \"type\": \"double\",\n", "            \"description\": \"atc\",\n", "        },\n", "        {\n", "            \"name\": \"grn_qty\",\n", "            \"type\": \"double\",\n", "            \"description\": \"grn_qty\",\n", "        },\n", "        {\n", "            \"name\": \"indent\",\n", "            \"type\": \"double\",\n", "            \"description\": \"indent\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\n", "        \"date_\",\n", "        \"city_name\",\n", "        \"item_id\",\n", "    ],\n", "    \"partition_key\": [\"date_\"],\n", "    \"incremental_key\": \"date_\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"table for internal use and visualisation\",\n", "}\n", "\n", "pb.to_trino(merge_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "17a928fe-dd27-4b62-8910-498a6ea15994", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select * from supply_etls.fnv_city_item_data where date_ >= current_date - interval '8' day \n", "\"\"\"\n", "merge_df = read_sql_query(query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "d51e6c1a-e45a-420a-85da-7e57b8c81504", "metadata": {}, "outputs": [], "source": ["merge_df[\"date_\"] = pd.to_datetime(merge_df[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "4c07e0d7-4455-4b9f-bd6f-da6bf1fe2ca3", "metadata": {}, "outputs": [], "source": ["merge_df"]}, {"cell_type": "code", "execution_count": null, "id": "7c3fa874-2708-4e10-959e-da6d51a08bc5", "metadata": {}, "outputs": [], "source": ["seasonal_tag[\"date_\"] = pd.to_datetime(seasonal_tag[\"date_\"])\n", "merge_df = pd.merge(merge_df, seasonal_tag, on=[\"city_name\", \"item_id\", \"date_\"], how=\"left\")\n", "merge_df[\"seasonal_flag\"] = merge_df[\"seasonal_flag\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "d7eb3cd3-3288-4526-94a6-08b34b6ad138", "metadata": {}, "outputs": [], "source": ["cart_breakers = f\"\"\"\n", "with treasure as (\n", "SELECT  fsoid.city_name as city, ipm.item_id, icd.l2 as l2, icd.name as item_name, COUNT(DISTINCT fsoid.cart_id) AS item_checkout_carts\n", "FROM  dwh.fact_sales_order_item_details  AS fsoid \n", "LEFT JOIN retail.console_outlet c on fsoid.outlet_id = c.id\n", "JOIN rpc.item_product_mapping ipm on ipm.product_id = fsoid.product_id and ipm.active = 1\n", "JOIN rpc.item_category_details icd on icd.item_id = ipm.item_id and icd.l0_id in (1487)\n", "WHERE \n", "    fsoid.order_create_dt_ist  >=  current_date - interval '16' day AND fsoid.order_create_dt_ist < current_date\n", "    AND fsoid.order_type in ('RetailForwardOrder', 'DropShippingForwardOrder')\n", "    and fsoid.order_current_status in ('DELIVERED')\n", "GROUP BY\n", "    1, 2, 3,4\n", "),\n", "\n", "cartbreakers_ as (select city, item_id, item_name, item_checkout_carts,\n", "  sum(item_checkout_carts) over (partition by city order by item_checkout_carts desc rows unbounded preceding) as cumulative_sum,\n", "  sum(item_checkout_carts) over (partition by city) as total_carts\n", "from treasure\n", "union\n", "select 'Pan India' as city, item_id, item_name, item_checkout_carts,\n", "  sum(item_checkout_carts) over (partition by 'Pan India' order by item_checkout_carts desc rows unbounded preceding) as cumulative_sum,\n", "  sum(item_checkout_carts) over (partition by 'Pan India') as total_carts\n", "from treasure),\n", "\n", "final as (select *, \n", "cumulative_sum*1.00/(total_carts+0.0000001) as cumsum_perc\n", "from cartbreakers_)\n", "\n", "select city as city_name,item_id from final where cumsum_perc <=0.7     \n", "\"\"\"\n", "cart_breakers_df = read_sql_query(cart_breakers, trino)\n", "cart_breakers_df[\"cb_flag\"] = 1\n", "cart_breakers_df[\"date_\"] = yesterday_date"]}, {"cell_type": "code", "execution_count": null, "id": "d0aa3809-abad-4870-b5f8-dbc1b0ecd5d8", "metadata": {}, "outputs": [], "source": ["cart_breakers_df[cart_breakers_df[\"city_name\"] == \"Pan India\"]"]}, {"cell_type": "code", "execution_count": null, "id": "1922070e-a019-4ba3-b348-003f81c6d4e2", "metadata": {}, "outputs": [], "source": ["cart_breakers_df[\"date_\"] = pd.to_datetime(cart_breakers_df[\"date_\"])\n", "merge_df = pd.merge(merge_df, cart_breakers_df, on=[\"city_name\", \"item_id\", \"date_\"], how=\"left\")\n", "merge_df[\"cb_flag\"] = merge_df[\"cb_flag\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "ad4396df-fcdf-41ca-b925-f57d0f890966", "metadata": {}, "outputs": [], "source": ["active_stores = \"\"\"select   \n", "Case when cl.name in ('Gurgaon') then 'HR-NCR'\n", "    when cl.name in ('Noida','Ghaziabad') then 'UP-NCR' else cl.name end as city_name,\n", "    pfma.item_id,\n", "    count(distinct co.id) as active_outlets\n", "from  rpc.product_facility_master_assortment pfma\n", "join retail.console_outlet co on co.facility_id = pfma.facility_id and co.business_type_id in (7) and co.active =1 and co.lake_active_record\n", "join retail.console_location cl on cl.id = co.tax_location_id\n", "join rpc.item_category_details icd on icd.item_id = pfma.item_id and icd.l0_id in (1487)\n", "join supply_etls.outlet_details od on od.facility_id = pfma.facility_id AND ars_Active = 1 and ars_check = 1\n", "where master_assortment_substate_id = 1 and pfma.active = 1\n", "group by 1,2\n", "union\n", "select   \n", "'Pan India' city_name,\n", "    pfma.item_id,\n", "    count(distinct co.id) as active_outlets\n", "from  rpc.product_facility_master_assortment pfma\n", "join retail.console_outlet co on co.facility_id = pfma.facility_id and co.business_type_id in (7) and co.active =1 and co.lake_active_record\n", "join retail.console_location cl on cl.id = co.tax_location_id\n", "join rpc.item_category_details icd on icd.item_id = pfma.item_id and icd.l0_id in (1487)\n", "join supply_etls.outlet_details od on od.facility_id = pfma.facility_id AND ars_Active = 1 and ars_check = 1\n", "where master_assortment_substate_id = 1 and pfma.active = 1\n", "group by 1,2\n", "\"\"\"\n", "active_stores = read_sql_query(active_stores, trino)\n", "active_stores[\"date_\"] = yesterday_date\n", "active_stores[\"date_\"] = pd.to_datetime(active_stores[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "72c12d65-1d7c-4366-9aa5-48fc0ee04847", "metadata": {}, "outputs": [], "source": ["merge_df = pd.merge(merge_df, active_stores, on=[\"city_name\", \"item_id\", \"date_\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "027b3610-9d7e-426b-aad8-a45c1037ae55", "metadata": {}, "outputs": [], "source": ["item_name = \"\"\"\n", "select product_id, icd.item_id, name as item_name, product_type, variant_uom_text as unit\n", "from rpc.item_category_details icd\n", "join dwh.dim_item_product_offer_mapping pl ON icd.item_id = pl.item_id and pl.is_current and pl.is_offer = false\n", "JOIN (SELECT item_id,variant_uom_text, MAX(id) AS id\n", "                FROM rpc.product_product\n", "                WHERE active = 1 and approved = 1 and lake_active_record\n", "                GROUP BY 1,2\n", "            ) b ON icd.item_id = b.item_id\n", "where l0_id in (1487)\"\"\"\n", "item_name = read_sql_query(item_name, trino)\n", "merge_df = pd.merge(merge_df, item_name, on=[\"item_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "d5fea33e-c37b-4c95-9192-dcc3fa8d85ce", "metadata": {}, "outputs": [], "source": ["current_inv = \"\"\"\n", "with base as\n", "(\n", "    SELECT \n", "        item_id, item_name,outlet_id, outlet_name,\n", "     sum(actual_quantity)-sum(Case when blocked_quantity < 0 then 0 else blocked_quantity end) as net_inv \n", "    from\n", "    (\n", "        SELECT \n", "                iii.item_id, \n", "                icd.name as item_name, iii.outlet_id as outlet_id, o.name as outlet_name,\n", "                iii.quantity as actual_quantity, \n", "                (COALESCE(sum(case when iibi.blocked_type in (1,2,5) then iibi.quantity else 0 end),0)) as blocked_quantity\n", "        FROM \n", "            ims.ims_item_inventory iii\n", "        LEFT JOIN \n", "            ims.ims_item_blocked_inventory iibi ON iii.item_id = iibi.item_id AND iii.outlet_id = iibi.outlet_id\n", "        INNER JOIN \n", "            retail.console_outlet o ON iii.outlet_id = o.id and o.active = 1 and business_type_id = 7\n", "        INNER JOIN \n", "            rpc.item_category_details icd on icd.item_id = iii.item_id and icd.l0_id in (1487)\n", "        where\n", "            iii.active = 1 \n", "        GROUP BY \n", "            1,2,3,4,5,iii.quantity\n", "        )\n", "    group by 1,2,3,4\n", ")\n", "\n", "select * from\n", "(\n", "    select \n", "    Case when cl.name in ('Gurgaon') then 'HR-NCR'\n", "    when cl.name in ('Noida','Ghaziabad') then 'UP-NCR' else cl.name end as city_name,\n", "    base.item_id,\n", "    sum(net_inv) as net_inv\n", "    from base\n", "    join retail.console_outlet co on co.id = base.outlet_id and co.business_type_id = 7 and active = 1 and co.lake_active_record\n", "    left join retail.console_location cl on cl.id = co.tax_location_id\n", "    join supply_etls.outlet_details od on od.hot_outlet_id = base.outlet_id AND ars_Active = 1\n", "    group by 1,2\n", "    union\n", "    select \n", "    'Pan India' city_name,\n", "    base.item_id,\n", "    sum(net_inv) as net_inv\n", "    from base\n", "    join retail.console_outlet co on co.id = base.outlet_id and co.business_type_id = 7 and active = 1 and co.lake_active_record\n", "    left join retail.console_location cl on cl.id = co.tax_location_id\n", "    join supply_etls.outlet_details od on od.hot_outlet_id = base.outlet_id AND ars_Active = 1\n", "    group by 1,2\n", "\n", ")\n", "\n", "\"\"\"\n", "current_inv = read_sql_query(current_inv, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "58bb8451-45f1-4db4-b628-08763ec6c4fd", "metadata": {}, "outputs": [], "source": ["merge_df = pd.merge(merge_df, current_inv, on=[\"city_name\", \"item_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "52426cb6-4c68-4f37-9b5a-1cc6d452290c", "metadata": {}, "outputs": [], "source": ["merge_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "8ea9219d-8c8c-45ce-90df-2208b561e06e", "metadata": {}, "outputs": [], "source": ["merge_df = merge_df.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "3013ad58-5606-452e-983e-9c5b793e787f", "metadata": {}, "outputs": [], "source": ["merge_df = merge_df[\n", "    [\n", "        \"date_\",\n", "        \"city_name\",\n", "        \"l2\",\n", "        \"product_id\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"unit\",\n", "        \"product_type\",\n", "        \"seasonal_flag\",\n", "        \"cb_flag\",\n", "        \"active_outlets\",\n", "        \"net_inv\",\n", "        \"category_gmv\",\n", "        \"category_retained_margin\",\n", "        \"category_customers\",\n", "        \"category_checkout_carts\",\n", "        \"category_quants\",\n", "        \"wlp\",\n", "        \"overall_gmv\",\n", "        \"overall_customers\",\n", "        \"overall_checkout_carts\",\n", "        \"dump_quantity\",\n", "        \"dump_value\",\n", "        \"wtd_avail\",\n", "        \"avail\",\n", "        \"qng_complaints\",\n", "        \"indent\",\n", "        \"grn_qty\",\n", "        \"impressions\",\n", "        \"atc\",\n", "        \"wtd_avail_9pm\",\n", "        \"no_stores\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "3f4cf193-d80f-4897-869e-3b91a7c2e917", "metadata": {}, "outputs": [], "source": ["merge_df[\"week_number\"] = merge_df[\"date_\"].dt.isocalendar().week\n", "merge_df[\"month\"] = merge_df[\"date_\"].dt.month"]}, {"cell_type": "code", "execution_count": null, "id": "1f71fc70-f262-4e08-bfff-aa4de8f40f4a", "metadata": {}, "outputs": [], "source": ["merge_df = merge_df.sort_values(by=[\"category_quants\", \"date_\"], ascending=[False, False])"]}, {"cell_type": "code", "execution_count": null, "id": "d85123eb-2c84-409a-9310-d1c6ce1f01a3", "metadata": {}, "outputs": [], "source": ["merge_df[\"updated_at\"] = current_time"]}, {"cell_type": "code", "execution_count": null, "id": "a65e339a-26d1-4309-8b41-226b9c383dbf", "metadata": {}, "outputs": [], "source": ["mango_data = merge_df[merge_df[\"product_type\"] == \"Mango\"]\n", "mango_data"]}, {"cell_type": "code", "execution_count": null, "id": "0430b0e0-336c-46fa-bdbc-5ceb2c588763", "metadata": {}, "outputs": [], "source": ["cut_and_sprouts = merge_df[merge_df[\"l2\"] == \"Freshly Cut & Sprouts\"]\n", "cut_and_sprouts"]}, {"cell_type": "code", "execution_count": null, "id": "f72b0067-18c7-454f-a16e-efe71eb7ee1c", "metadata": {}, "outputs": [], "source": ["# merge_df.to_csv('trial_file.csv', index = False)"]}, {"cell_type": "code", "execution_count": null, "id": "32a42558-cff0-492a-8a7b-c777efb823e5", "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.to_sheets(\n", "        merge_df,\n", "        \"1QfD_7KqHGx0_JaEJX5-PW8vlqaHduXl_t94uq9fJlFg\",\n", "        \"raw\",\n", "    )\n", "except:\n", "    print(\"nai hua\")"]}, {"cell_type": "code", "execution_count": null, "id": "3f3504e1-0f06-412d-922c-fd6b41947ef1", "metadata": {}, "outputs": [], "source": ["time.sleep(60)"]}, {"cell_type": "code", "execution_count": null, "id": "94f553a5-1bd6-4a4f-b027-a9b9c3f26c3b", "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.to_sheets(\n", "        merge_df,\n", "        \"1QfD_7KqHGx0_JaEJX5-PW8vlqaHduXl_t94uq9fJlFg\",\n", "        \"raw\",\n", "    )\n", "except:\n", "    print(\"nai hua\")"]}, {"cell_type": "code", "execution_count": null, "id": "d198d209-bfc0-424e-9cc1-2d5c68fca1a2", "metadata": {}, "outputs": [], "source": ["time.sleep(60)"]}, {"cell_type": "code", "execution_count": null, "id": "9637253b-6a1a-41c8-8983-ad98fb488eba", "metadata": {}, "outputs": [], "source": ["current_date = pd.to_datetime(\"today\")\n", "\n", "merge_df_1 = merge_df[merge_df[\"date_\"] >= (current_date - pd.Timedelta(days=14))]\n", "merge_df_1"]}, {"cell_type": "code", "execution_count": null, "id": "ae7e4131-6d55-4bd0-a15e-979bcc7e759b", "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.to_sheets(\n", "        merge_df_1,\n", "        \"14N2PYbe4ikFfu8ydcc9RIbk9cFCbRXVsYkg8GfjjHZc\",\n", "        \"raw\",\n", "    )\n", "except:\n", "    print(\"failed\")"]}, {"cell_type": "code", "execution_count": null, "id": "2d809f71-89e1-408f-95c3-ad078058e0c4", "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.to_sheets(\n", "        merge_df,\n", "        \"1LyYXobnBFJDimPtv1zaiF8-pxfuhgW8hPvkf4nPhaDo\",\n", "        \"raw\",\n", "    )\n", "except:\n", "    print(\"nai hua\")"]}, {"cell_type": "code", "execution_count": null, "id": "afa78f66-cee7-4c42-b1b9-96412147fe35", "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.to_sheets(\n", "        merge_df,\n", "        \"1LyYXobnBFJDimPtv1zaiF8-pxfuhgW8hPvkf4nPhaDo\",\n", "        \"raw\",\n", "    )\n", "except:\n", "    print(\"nai hua\")"]}, {"cell_type": "code", "execution_count": null, "id": "be69cb47-a40e-473c-aa59-636ef00e9c05", "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.to_sheets(\n", "        mango_data,\n", "        \"1T7enMro2CspMnaJ_yPZu7MqnU_jqm4CJ8Kai81rPrvI\",\n", "        \"RAW\",\n", "    )\n", "except:\n", "    print(\"error\")"]}, {"cell_type": "code", "execution_count": null, "id": "ced7bac0-0c05-4fa3-8ea8-a3451fdfde4f", "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.to_sheets(\n", "        cut_and_sprouts,\n", "        \"1k_WhxyKxWdlcyw48vb1cdtzkqAOSMBQjpbYA0hmjBsM\",\n", "        \"raw\",\n", "    )\n", "except:\n", "    print(\"error, please check\")"]}, {"cell_type": "code", "execution_count": null, "id": "7870a371-1962-4bbf-bc7b-dad713ef4971", "metadata": {}, "outputs": [], "source": ["mango_data"]}, {"cell_type": "code", "execution_count": null, "id": "92cf9e13-ff1b-43c0-8a8e-fa258e28f6e3", "metadata": {}, "outputs": [], "source": ["mango_2 = (\n", "    mango_data.groupby([\"city_name\", \"product_type\", \"date_\", \"l2\"])\n", "    .agg(\n", "        {\n", "            \"net_inv\": \"sum\",\n", "            \"category_gmv\": \"sum\",\n", "            \"category_retained_margin\": \"sum\",\n", "            \"category_customers\": \"sum\",\n", "            \"category_checkout_carts\": \"sum\",\n", "            \"category_quants\": \"sum\",\n", "            \"wlp\": \"sum\",\n", "            \"overall_gmv\": \"sum\",\n", "            \"overall_customers\": \"sum\",\n", "            \"overall_checkout_carts\": \"sum\",\n", "            \"dump_quantity\": \"sum\",\n", "            \"dump_value\": \"sum\",\n", "            \"wtd_avail\": \"mean\",\n", "            \"avail\": \"mean\",\n", "            \"qng_complaints\": \"sum\",\n", "            \"indent\": \"sum\",\n", "            \"grn_qty\": \"sum\",\n", "            \"impressions\": \"sum\",\n", "            \"atc\": \"sum\",\n", "            \"week_number\": \"first\",\n", "            \"month\": \"first\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "\n", "mango_2"]}, {"cell_type": "code", "execution_count": null, "id": "6610cbb0-15d5-428c-a482-2b2e9617a093", "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.to_sheets(\n", "        mango_data,\n", "        \"1T7enMro2CspMnaJ_yPZu7MqnU_jqm4CJ8Kai81rPrvI\",\n", "        \"raw_ptype\",\n", "    )\n", "except:\n", "    print(\"error\")"]}, {"cell_type": "code", "execution_count": null, "id": "a546955a-66af-4365-a10b-a7497f61c0b4", "metadata": {}, "outputs": [], "source": ["item_ids = pb.from_sheets(\"1CwxunbYOKWIbGS1aye5COJOecmR1beAXHzx73_-Hfms\", \"item_ids\")\n", "item_ids"]}, {"cell_type": "code", "execution_count": null, "id": "06ae2223-f0ec-48f4-9bc1-a29c6391164f", "metadata": {}, "outputs": [], "source": ["item_ids[\"item_id\"] = item_ids[\"item_id\"].astype(int)\n", "item_ids"]}, {"cell_type": "code", "execution_count": null, "id": "8d5d559e-d621-440f-8e63-a34e32a52e8d", "metadata": {}, "outputs": [], "source": ["items = merge_df[merge_df[\"item_id\"].isin(item_ids[\"item_id\"])]\n", "items"]}, {"cell_type": "code", "execution_count": null, "id": "33200240-3276-403e-ba4b-3a669b2aee5f", "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.to_sheets(\n", "        items,\n", "        \"1CwxunbYOKWIbGS1aye5COJOecmR1beAXHzx73_-Hfms\",\n", "        \"raw\",\n", "    )\n", "except:\n", "    print(\"error\")"]}, {"cell_type": "code", "execution_count": null, "id": "b5828e6a-5178-4633-be1d-e8773bc0b967", "metadata": {}, "outputs": [], "source": ["fruits_df = merge_df[merge_df[\"l2\"].isin([\"Fresh Fruits\", \"Exotics\"])]\n", "fruits_df"]}, {"cell_type": "code", "execution_count": null, "id": "a4e0c567-9093-489c-9772-859cb6f05e86", "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.to_sheets(\n", "        fruits_df,\n", "        \"1T7enMro2CspMnaJ_yPZu7MqnU_jqm4CJ8Kai81rPrvI\",\n", "        \"raw_all\",\n", "    )\n", "except:\n", "    print(\"error\")"]}, {"cell_type": "code", "execution_count": null, "id": "d0a3600d-e86a-4034-8a13-a901a359f1b6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9b189f2d-ec70-4cbb-854d-3a290e156a1b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "901859b3-710c-4dfa-b4bf-9459547504d7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0fdc8c96-28b1-4d3c-be51-ad40b885dd37", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "dfa9843c-fca4-48f7-b160-78464012d318", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a35b8997-6f61-4f7c-bfed-3864cf958fe2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c3edf1f5-380d-438f-8624-d65e079127d7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e0089299-460f-447f-ad88-67e7b33a136e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}