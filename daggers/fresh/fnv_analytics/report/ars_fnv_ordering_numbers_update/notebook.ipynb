{"cells": [{"cell_type": "code", "execution_count": null, "id": "36a0ec4e-01b4-47d2-9647-b3ad16f6aead", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8e5d2b93-faec-4b8d-85c2-00bb072a503b", "metadata": {"tags": []}, "outputs": [], "source": ["!pip install matplotlib==3.8.4\n", "!pip install seaborn==0.11.2\n", "!pip install tqdm==4.62.3\n", "!pip install six==1.16.0\n", "!pip install numpy==1.26.4"]}, {"cell_type": "code", "execution_count": null, "id": "bacfb234-b9a8-41ea-9869-71615d3d1f3f", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pandas as pd\n", "import pencilbox as pb\n", "from pytz import timezone\n", "import warnings\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import six"]}, {"cell_type": "code", "execution_count": null, "id": "7e6c0ce0-0ad2-40e3-af97-deeea9bea080", "metadata": {}, "outputs": [], "source": ["trino_conn = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "6f0f7ffb-47c2-4a86-b687-9b67548213b9", "metadata": {}, "outputs": [], "source": ["def render_mpl_table(\n", "    data,\n", "    col_width=5.0,\n", "    row_height=0.625,\n", "    font_size=16,\n", "    header_color=\"#40466e\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"w\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "\n", "    mpl_table = ax.table(cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs)\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in six.iteritems(mpl_table._cells):\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    return fig, ax"]}, {"cell_type": "code", "execution_count": null, "id": "31324aef-66e6-4044-b86c-39af2382e355", "metadata": {}, "outputs": [], "source": ["def get_results_from_query(sql: str, con: str = \"[Warehouse] Trino\"):\n", "    # LOGGER.info(f\"Fetching data for the query \\n{sql[:50] + ' ...'}\")\n", "    data = pd.read_sql(sql, pb.get_connection(con))\n", "    return data"]}, {"cell_type": "code", "execution_count": null, "id": "40f83999-381b-445e-8010-2e256eec2df4", "metadata": {}, "outputs": [], "source": ["def warner(text, channel=\"bl-data-science-alerts\", files=[], schema=\"consumer\"):\n", "    if schema == \"playground\":\n", "        channel = \"bl-data-science-alerts-test\"\n", "    pb.send_slack_message(channel=channel, text=text, files=files)\n", "\n", "\n", "def send_to_trino(\n", "    rep_df,\n", "    schema=\"ds_etls\",\n", "    normal_table_name=\"demand_forecast_item_min_max_quantity_io\",\n", "    send_to_table=False,\n", "    changed_log_table_name=\"demand_forecast_item_min_max_quantity_details_io\",\n", "    log_extra_columns_dict=dict(),\n", "    log_extra_dtypes_dict=dict(),\n", "    log_extra_primary_key_list=[\"updated_at_ist\"],\n", "    log_extra_sortkey_list=[\"updated_at_ist\"],\n", "    send_to_log_table=True,\n", "):\n", "\n", "    to_push_df = rep_df.copy()\n", "\n", "    normal_kwargs = {\n", "        \"schema_name\": f\"{schema}\",\n", "        \"table_name\": f\"{normal_table_name}\",\n", "        \"column_dtypes\": [\n", "            {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"facility_id\"},\n", "            {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"outlet id\"},\n", "            {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"item id\"},\n", "            {\"name\": \"name\", \"type\": \"VARCHAR\", \"description\": \"item name\"},\n", "            {\"name\": \"item_type\", \"type\": \"VARCHAR\", \"description\": \"item type\"},\n", "            {\"name\": \"quantile\", \"type\": \"REAL\", \"description\": \"quantile\"},\n", "            {\"name\": \"min_qty\", \"type\": \"INTEGER\", \"description\": \"Min Stock level\"},\n", "            {\"name\": \"max_qty\", \"type\": \"INTEGER\", \"description\": \"Max Stock level\"},\n", "            {\n", "                \"name\": \"current_replenishment_ts_ist\",\n", "                \"type\": \"TIMESTAMP(6)\",\n", "                \"description\": \"Current Replenishment Timestamp\",\n", "            },\n", "            {\n", "                \"name\": \"updated_at_ist\",\n", "                \"type\": \"TIMESTAMP(6)\",\n", "                \"description\": \"Latest update time in IST\",\n", "            },\n", "        ],\n", "        \"primary_key\": [\"facility_id\", \"item_id\"],\n", "        \"sort_key\": [\"facility_id\", \"item_id\"],\n", "        \"incremental_key\": \"updated_at_ist\",\n", "        \"load_type\": \"upsert\",\n", "        \"table_description\": \"demand_forecast_item_min_max_quantity\",\n", "        \"force_upsert_without_increment_check\": True,\n", "        \"run_maintenance\": True,\n", "    }\n", "\n", "    if send_to_table:\n", "\n", "        update_table_on_trino(\n", "            normal_df=to_push_df,\n", "            normal_kwargs=normal_kwargs,\n", "            changed_log_table_name=changed_log_table_name,\n", "            log_extra_columns_dict=log_extra_columns_dict,\n", "            log_extra_dtypes_dict=log_extra_dtypes_dict,\n", "            log_extra_primary_key_list=log_extra_primary_key_list,\n", "            log_extra_sortkey_list=log_extra_sortkey_list,\n", "            send_to_log_table=send_to_log_table,\n", "        )\n", "\n", "    return to_push_df\n", "\n", "\n", "def update_table_on_trino(\n", "    normal_df,\n", "    normal_kwargs,\n", "    changed_log_table_name=None,\n", "    log_extra_columns_dict=dict(),\n", "    log_extra_dtypes_dict=dict(),\n", "    log_extra_primary_key_list=list(),\n", "    log_extra_sortkey_list=list(),\n", "    send_to_log_table=True,\n", "):\n", "    if changed_log_table_name == normal_kwargs[\"table_name\"]:\n", "        raise Exception(\"Log and normal table names are same!\")\n", "    if not set(log_extra_columns_dict.keys()) == set(log_extra_dtypes_dict.keys()):\n", "        raise Exception(\"Log extra column and dtype do not match\")\n", "\n", "    try:\n", "        pb.to_trino(normal_df, **normal_kwargs)\n", "        print(f\"Updated {normal_kwargs['schema_name']}.{normal_kwargs['table_name']}\")\n", "    except Exception as e:\n", "        raise ValueError(\n", "            f\"Error in populating {normal_kwargs['schema_name']}.{normal_kwargs['table_name']}: {e}\"\n", "        )\n", "\n", "    if send_to_log_table:\n", "        log_kwargs = normal_kwargs\n", "\n", "        if changed_log_table_name:\n", "            log_kwargs[\"table_name\"] = changed_log_table_name\n", "\n", "        log_kwargs[\"table_name\"] = log_kwargs[\"table_name\"] + \"_log\"\n", "\n", "        for col, value in log_extra_columns_dict.items():\n", "            normal_df[col] = value\n", "            dtype_dict = {\n", "                \"name\": col,\n", "                \"type\": log_extra_dtypes_dict[col],\n", "                \"description\": col,\n", "            }\n", "            log_kwargs[\"column_dtypes\"].append(dtype_dict)\n", "\n", "        if \"frontend_id\" in [d[\"name\"] for d in normal_kwargs[\"column_dtypes\"]]:\n", "            ind = [\n", "                i\n", "                for i in range(len(normal_kwargs[\"column_dtypes\"]))\n", "                if log_kwargs[\"column_dtypes\"][i][\"name\"] == \"frontend_id\"\n", "            ][0]\n", "            log_kwargs[\"column_dtypes\"].insert(0, log_kwargs[\"column_dtypes\"].pop(ind))\n", "            normal_df.insert(0, \"frontend_id\", normal_df.pop(\"frontend_id\"))\n", "\n", "        log_kwargs[\"load_type\"] = \"append\"\n", "        log_kwargs[\"partition_key\"] = [\"current_replenishment_ts_ist\"]\n", "\n", "        try:\n", "            pb.to_trino(normal_df, **log_kwargs)\n", "            print(f\"Updated {log_kwargs['schema_name']}.{log_kwargs['table_name']}\")\n", "        except Exception as e:\n", "            raise ValueError(\n", "                f\"Error in populating {log_kwargs['schema_name']}.{log_kwargs['table_name']}\"\n", "            )\n", "\n", "    return normal_df"]}, {"cell_type": "code", "execution_count": null, "id": "4a86643d-3d31-4d73-8dae-b7d5c05a8080", "metadata": {}, "outputs": [], "source": ["def update_base_data(base_data, updated_data, run_bump_forecasts, run_assertions=True):\n", "    updated_df = pd.concat([base_data, updated_data], axis=0).drop_duplicates(\n", "        subset=[\"outlet_id\", \"item_id\", \"current_replenishment_ts_ist\"], keep=\"last\"\n", "    )\n", "\n", "    updated_df[\"min_qty\"] = np.where(\n", "        updated_df[\"min_qty\"] > updated_df[\"max_qty\"],\n", "        updated_df[\"max_qty\"],\n", "        updated_df[\"min_qty\"],\n", "    )\n", "\n", "    if run_bump_forecasts:\n", "        updated_df[\"min_qty\"] = updated_df[\"min_qty\"].apply(np.round).astype(int)\n", "        updated_df[\"max_qty\"] = updated_df[\"max_qty\"].apply(np.round).astype(int)\n", "    else:\n", "        updated_df[\"min_qty\"] = updated_df[\"min_qty\"].apply(np.ceil).astype(int)\n", "        updated_df[\"max_qty\"] = updated_df[\"max_qty\"].apply(np.ceil).astype(int)\n", "\n", "    updated_df[\"min_qty\"] = np.where(updated_df[\"min_qty\"] < 0, 0, updated_df[\"min_qty\"])\n", "    updated_df[\"max_qty\"] = np.where(updated_df[\"max_qty\"] < 0, 0, updated_df[\"max_qty\"])\n", "\n", "    print(\n", "        \"Total difference of max_qty between updated data and base data : \",\n", "        np.round(updated_df[\"max_qty\"].sum() - base_data[\"max_qty\"].sum(), 0),\n", "    )\n", "\n", "    updated_df[\"updated_at_ist\"] = datetime.now(timezone(\"Asia/Kolkata\")).replace(tzinfo=None)\n", "\n", "    if run_assertions:\n", "        assert (\n", "            updated_df.shape == base_data.shape\n", "        ), f\"Processed dataframe shape {updated_df.shape} is different than base data shape {base_data.shape}, please check\"\n", "    else:\n", "        print(\"Difference in rows :\", updated_df.shape[0] - base_data.shape[0])\n", "    return updated_df"]}, {"cell_type": "code", "execution_count": null, "id": "ba38e3b9-852f-4882-a00e-f92e3c8fb0ad", "metadata": {}, "outputs": [], "source": ["# dts_ars_outlets = [\n", "#     4935,\n", "#     5474,\n", "#     4959,\n", "#     4960,\n", "#     4074,\n", "#     5040,\n", "#     3474,\n", "#     3577,\n", "#     5008,\n", "#     5475,\n", "#     5401,\n", "#     4953,\n", "#     5499,\n", "#     4933,\n", "#     5682,\n", "#     4954,\n", "#     3171,\n", "#     6018,\n", "#     5774,\n", "#     5823,\n", "#     4073,\n", "#     4071,\n", "#     5943,\n", "#     6183,\n", "#     5214,\n", "#     5944,\n", "#     6016,\n", "#     6057,\n", "#     6001,\n", "#     5855,\n", "#     6369,\n", "#     5755,\n", "#     5989,\n", "#     6431,\n", "#     5473,\n", "#     5337,\n", "#     5084,\n", "#     5675,\n", "#     5588,\n", "#     6403,\n", "#     4880,\n", "#     4845,\n", "#     5859,\n", "#     4956,\n", "#     5489,\n", "#     5062,\n", "#     5231,\n", "#     5712,\n", "#     4876,\n", "#     5582,\n", "#     4941,\n", "#     5490,\n", "#     5055,\n", "#     4958,\n", "#     5648,\n", "#     5338,\n", "#     5644,\n", "#     4946,\n", "#     5083,\n", "#     6099,\n", "#     5234,\n", "#     5961,\n", "#     5226,\n", "#     4777,\n", "#     5558,\n", "#     5151,\n", "#     5056,\n", "#     5668,\n", "#     5583,\n", "#     6069,\n", "#     4875,\n", "#     5060,\n", "#     6253,\n", "#     5833,\n", "#     5589,\n", "#     4735,\n", "#     5153,\n", "#     5132,\n", "#     5695,\n", "#     5360,\n", "#     5702,\n", "#     4955,\n", "#     5302,\n", "#     6336,\n", "#     5760,\n", "#     4862,\n", "#     5995,\n", "#     5667,\n", "#     6070,\n", "#     5300,\n", "#     4906,\n", "#     5803,\n", "#     4945,\n", "#     4825,\n", "#     5476,\n", "#     6537,\n", "#     6009,\n", "#     5756,\n", "#     5974,\n", "#     6406,\n", "#     6283,\n", "#     5993,\n", "#     6165,\n", "#     5339,\n", "#     6167,\n", "#     5922,\n", "#     5156,\n", "#     6134,\n", "#     6187,\n", "#     6098,\n", "#     6002,\n", "#     6338,\n", "#     6287,\n", "#     6105,\n", "# ]"]}, {"cell_type": "markdown", "id": "22bd92e2-ee47-49a7-9f9d-4179c86b95c0", "metadata": {"tags": []}, "source": ["### parameters"]}, {"cell_type": "code", "execution_count": null, "id": "5c75fe90-b3ba-4589-8afb-43a6e903c37e", "metadata": {}, "outputs": [], "source": ["TRINO_SCHEMA = \"ds_etls\"\n", "min_max_normal_table_name = \"demand_forecast_item_min_max_quantity\"\n", "ordering_table_name = \"demand_forecast_item_ordering_min_max_quantity\"\n", "send_to_min_max_table = True"]}, {"cell_type": "markdown", "id": "b947a64f-8c36-49b2-8127-434d69798b6d", "metadata": {}, "source": ["### import data"]}, {"cell_type": "code", "execution_count": null, "id": "ec6a353f-31f2-4cec-904f-05aa9f0c0b86", "metadata": {}, "outputs": [], "source": ["upload_table = f\"\"\"\n", "with base as (\n", "select\n", "    co.facility_id,\n", "    a.outlet_id,\n", "    a.item_id,\n", "    a.t2_forecast,\n", "    a.created_at_ist,\n", "    ROW_NUMBER() OVER (PARTITION BY a.outlet_id, a.item_id ORDER BY a.created_at_ist DESC) AS rank_\n", "from\n", "    ars_etls.ars_fnv_ordering_store_data a\n", "join\n", "    retail.console_outlet co\n", "    on a.outlet_id = co.id\n", "    and co.active = 1\n", "where\n", "    a.backend_outlet_id in (4665, 4514, 4553,5377,5615,5537,4184,6153,4236,5030,4240,4433,4552,6056,4287,4353,5376,2665)\n", "    and a.consumption_date in (current_date + interval '2' day)\n", "union all\n", "\n", "select\n", "    co.facility_id,\n", "    a.outlet_id,\n", "    a.item_id,\n", "    a.t2_forecast,\n", "    a.created_at_ist,\n", "    ROW_NUMBER() OVER (PARTITION BY a.outlet_id, a.item_id ORDER BY a.created_at_ist DESC) AS rank_\n", "from\n", "    ars_etls.ars_fnv_ordering_dts_store_data a\n", "join\n", "    retail.console_outlet co\n", "    on a.outlet_id = co.id\n", "    and co.active = 1\n", "where\n", "    a.consumption_date in (current_date + interval '2' day)\n", "    \n", ")\n", "\n", "select \n", "    facility_id,\n", "    outlet_id,\n", "    item_id,\n", "    t2_forecast as max_qty\n", "from \n", "    base \n", "where \n", "    rank_ = 1 and t2_forecast is not null\n", "\"\"\"\n", "upload_table = pd.read_sql(upload_table, trino_conn)"]}, {"cell_type": "code", "execution_count": null, "id": "84df168b-8aac-49a6-9928-0f924021d216", "metadata": {}, "outputs": [], "source": ["l2_item_mapping_df = pd.read_sql(\n", "    \"select distinct item_id, name, l2, l2_id from lake_rpc.item_category_details where l0_id = 1487\",\n", "    con=trino_conn,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "79a41d18-192c-4e5b-96d4-f430742e17c6", "metadata": {}, "outputs": [], "source": ["upload_table.facility_id = upload_table.facility_id.astype(int)\n", "upload_table.outlet_id = upload_table.outlet_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "55ee9692-7b52-477f-aede-d3b8bf338d15", "metadata": {}, "outputs": [], "source": ["upload_table.sort_values(by=[\"max_qty\"], ascending=False, inplace=True)\n", "upload_table.drop_duplicates(\n", "    subset=[\"facility_id\", \"outlet_id\", \"item_id\"], keep=\"first\", inplace=True\n", ")"]}, {"cell_type": "markdown", "id": "1c8f8cdd-e119-41b9-aee2-0228ad1ef9aa", "metadata": {}, "source": ["### Facility Id x Outlet Id Checking"]}, {"cell_type": "code", "execution_count": null, "id": "4fcf5e23-7e4f-4246-a0fc-2d23cf881324", "metadata": {}, "outputs": [], "source": ["facility_outlet_df = pd.read_sql(\n", "    \"\"\"select\n", "            facility_id,\n", "            outlet_id\n", "        from\n", "            po.physical_facility_outlet_mapping\n", "        where active=1\n", "        and is_primary=1\n", "        and lake_active_record\n", "        and ars_active=1\n", "        group by 1,2 \"\"\",\n", "    con=trino_conn,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4a7bb3e0-c261-485d-b090-2dc132488327", "metadata": {}, "outputs": [], "source": ["facility_outlet_df.facility_id = facility_outlet_df.facility_id.astype(int)\n", "facility_outlet_df.outlet_id = facility_outlet_df.outlet_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "18dd33ef-cb68-4db6-b4db-7b5332f25a3f", "metadata": {}, "outputs": [], "source": ["facility_outlet_df.drop_duplicates(inplace=True)\n", "facility_outlet_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "9c6d7c9f-4d90-42b9-9de4-6d1d1ce6232d", "metadata": {}, "outputs": [], "source": ["facility_ids = (\n", "    facility_outlet_df.groupby([\"facility_id\"], as_index=False)[\"outlet_id\"]\n", "    .nunique()\n", "    .sort_values(by=[\"outlet_id\"], ascending=False)\n", "    .query(f\"outlet_id>1\")[\"facility_id\"]\n", "    .unique()\n", ")\n", "facility_outlet_df[facility_outlet_df[\"facility_id\"].isin(facility_ids)].to_csv(\n", "    \"./facility_id_check.csv\", index=False\n", ")\n", "\n", "if True in ((facility_outlet_df.groupby([\"facility_id\"])[\"outlet_id\"].count() > 1)).values:\n", "    pb.send_slack_message(\n", "        channel=\"#wrong-post-processing-inputs-fnv\",\n", "        text=\"There are multiple rows across facility_id in * po.physical_facility_outlet_mapping * so dropping the following facility_ids \\n  cc:<@U05CCTXLBU1> <@U07668YK86R>,<@U078DPW05T4>\",\n", "        files=[\"./facility_id_check.csv\"],\n", "    )\n", "upload_table = upload_table[~upload_table[\"facility_id\"].isin(facility_ids)]"]}, {"cell_type": "code", "execution_count": null, "id": "3030d012-6426-4d1a-b37e-011494f2c395", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9c03ab81-12e4-4922-bcfe-07ed89129df2", "metadata": {}, "outputs": [], "source": ["outlet_ids = (\n", "    facility_outlet_df.groupby([\"outlet_id\"], as_index=False)[\"facility_id\"]\n", "    .nunique()\n", "    .sort_values(by=[\"facility_id\"], ascending=False)\n", "    .query(f\"facility_id>1\")[\"outlet_id\"]\n", "    .unique()\n", ")\n", "facility_outlet_df[facility_outlet_df[\"outlet_id\"].isin(outlet_ids)].to_csv(\n", "    \"./outlet_id_check.csv\", index=False\n", ")\n", "\n", "if True in ((facility_outlet_df.groupby([\"outlet_id\"])[\"facility_id\"].count() > 1)).values:\n", "    pb.send_slack_message(\n", "        channel=\"#wrong-post-processing-inputs-fnv\",\n", "        text=\"There are multiple rows across outlet_id in * po.physical_facility_outlet_mapping * so dropping the following outlet_ids \\n  cc:<@U05CCTXLBU1> <@U07668YK86R>,<@U078DPW05T4>\",\n", "        files=[\"./outlet_id_check.csv\"],\n", "    )\n", "upload_table = upload_table[~upload_table[\"outlet_id\"].isin(outlet_ids)]"]}, {"cell_type": "code", "execution_count": null, "id": "b12b707d-e31f-47f8-843b-19c8cc49fcf2", "metadata": {}, "outputs": [], "source": ["facility_outlet_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "18e3a89f-514b-4013-96ff-e3b755a66758", "metadata": {}, "outputs": [], "source": ["main_df = facility_outlet_df.merge(upload_table, on=[\"facility_id\", \"outlet_id\"], how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "id": "550a0844-81e4-41b2-8e16-b54c615ecc80", "metadata": {}, "outputs": [], "source": ["main_df[\"flag\"] = 1"]}, {"cell_type": "code", "execution_count": null, "id": "4930c28d-f130-4677-8486-f2823184ed39", "metadata": {}, "outputs": [], "source": ["main_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "6c7e7b97-78d3-4443-a212-e561f94e5516", "metadata": {}, "outputs": [], "source": ["false_df = upload_table.merge(main_df, how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "ab360c45-f4f6-47b9-9f2d-7edd21272956", "metadata": {}, "outputs": [], "source": ["false_df[\"flag\"].fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "ccc82110-2b64-43bb-8b95-7bc23c9d8a1d", "metadata": {"tags": []}, "outputs": [], "source": ["false_df = false_df.query(f\"flag==0\").drop(columns=[\"flag\"])\n", "false_df.to_csv(\"./false_df.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "3fe4c013-5c02-442f-a351-2c58d6edd039", "metadata": {}, "outputs": [], "source": ["false_df"]}, {"cell_type": "code", "execution_count": null, "id": "3aaa74fc-91a9-4138-a0be-d833f6797fd5", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"wrong-post-processing-inputs-fnv\",\n", "    text=f\"Following data is ignored for min max update in adhoc_update_table for ordering numbers \\n  cc:<@U05CCTXLBU1> <@U07668YK86R>,<@U078DPW05T4>\",\n", "    files=[\"./false_df.csv\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "924991c8-ee7e-4695-898b-2d6d9a13431d", "metadata": {}, "outputs": [], "source": ["upload_table = main_df.drop(columns=[\"flag\"])\n", "upload_table"]}, {"cell_type": "code", "execution_count": null, "id": "f7bf8d63-e7ce-4927-a2fb-264b2c9346cf", "metadata": {}, "outputs": [], "source": ["col_dtypes = {\n", "    \"facility_id\": int,\n", "    \"outlet_id\": int,\n", "    \"item_id\": int,\n", "    \"name\": str,\n", "    \"item_type\": str,\n", "    \"quantile\": float,\n", "    \"min_qty\": int,\n", "    \"max_qty\": int,\n", "    \"current_replenishment_ts_ist\": \"datetime64[ns]\",\n", "    \"updated_at_ist\": \"datetime64[ns]\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "0fadfc4f-bda8-44f1-94ab-926aaac35dbd", "metadata": {}, "outputs": [], "source": ["upload_table[\"updated_at_ist\"] = datetime.now(timezone(\"Asia/Kolkata\")).replace(tzinfo=None)"]}, {"cell_type": "markdown", "id": "e727bd4f-32ea-4dce-87fe-a45c5ce5dc8d", "metadata": {}, "source": ["### upload data "]}, {"cell_type": "code", "execution_count": null, "id": "35d57cc6-875b-40de-aa65-be240ddb9059", "metadata": {}, "outputs": [], "source": ["update_table_name = ordering_table_name"]}, {"cell_type": "code", "execution_count": null, "id": "a0f295f3-c1bb-43b6-99b0-730bb056901a", "metadata": {}, "outputs": [], "source": ["base_df = pd.read_sql(f\"select * from ds_etls.{update_table_name}\", con=trino_conn)"]}, {"cell_type": "code", "execution_count": null, "id": "0fc4ea22-fb37-48cb-a8c4-4388a27a0778", "metadata": {}, "outputs": [], "source": ["final_updated_df = base_df.copy()\n", "base_data_table = update_table_name"]}, {"cell_type": "code", "execution_count": null, "id": "db61bdeb-1900-424e-a7d2-ec41a3f72b8a", "metadata": {}, "outputs": [], "source": ["base_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3d61a7b4-854c-4f8b-8cd7-fb456f33cce2", "metadata": {}, "outputs": [], "source": ["upload_table[\"min_qty\"] = upload_table[\"max_qty\"]"]}, {"cell_type": "code", "execution_count": null, "id": "2f28a70c-de33-45bd-adfc-44198860524a", "metadata": {}, "outputs": [], "source": ["upload_table[\"quantile\"] = 0.7\n", "upload_table[\"item_type\"] = \"fnv\"\n", "upload_table[\"current_replenishment_ts_ist\"] = base_df[\"current_replenishment_ts_ist\"].unique()[0]\n", "upload_table[\"updated_at_ist\"] = base_df[\"updated_at_ist\"].unique()[0]\n", "upload_table = upload_table.rename(\n", "    columns={\"min\": \"min_qty\", \"max\": \"max_qty\", \"item_name\": \"name\"}\n", ")\n", "\n", "upload_table[\"max_qty\"] = upload_table[\"max_qty\"].astype(float).apply(np.round).astype(int)\n", "upload_table[\"min_qty\"] = upload_table[\"min_qty\"].astype(float).apply(np.round).astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "c184cb02-ebf0-46de-b88b-e4af4f3a840a", "metadata": {}, "outputs": [], "source": ["upload_table = upload_table.astype({\"item_id\": int}).merge(\n", "    l2_item_mapping_df[[\"item_id\", \"name\"]], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b5c00645-8640-4d1d-a33a-a7a43576bed9", "metadata": {}, "outputs": [], "source": ["upload_table[\"name\"] = upload_table[\"name\"].fillna(\"fnv\")"]}, {"cell_type": "code", "execution_count": null, "id": "14c2c531-8192-4bae-bbe1-08221d7bca36", "metadata": {}, "outputs": [], "source": ["upload_table.head()"]}, {"cell_type": "code", "execution_count": null, "id": "86d62a95-521f-447d-b179-d34097fc2525", "metadata": {}, "outputs": [], "source": ["upload_table = upload_table[base_df.columns]\n", "upload_table = upload_table.astype(col_dtypes)"]}, {"cell_type": "code", "execution_count": null, "id": "20a08e44-aa88-4c47-b05b-ae6157cd2604", "metadata": {}, "outputs": [], "source": ["final_updated_df = final_updated_df.astype(col_dtypes)"]}, {"cell_type": "code", "execution_count": null, "id": "f956334a-fc30-4f49-b6c3-b001c40958ca", "metadata": {}, "outputs": [], "source": ["final_updated_df = update_base_data(\n", "    base_data=final_updated_df,\n", "    updated_data=upload_table,\n", "    run_bump_forecasts=False,\n", "    run_assertions=False,\n", ")"]}, {"cell_type": "markdown", "id": "f88a1550-d866-4fa9-87ba-2e81df0db7aa", "metadata": {}, "source": ["### send to trino "]}, {"cell_type": "code", "execution_count": null, "id": "fd42d33a-87d2-424d-a85b-61181da663ce", "metadata": {"tags": []}, "outputs": [], "source": ["final_updated_df_out = send_to_trino(\n", "    final_updated_df,\n", "    schema=\"ds_etls\",\n", "    normal_table_name=base_data_table,\n", "    send_to_table=True,\n", "    changed_log_table_name=None,\n", "    log_extra_columns_dict={\"frontend_id\": -1},\n", "    log_extra_dtypes_dict={\"frontend_id\": \"INTEGER\"},\n", ")"]}, {"cell_type": "markdown", "id": "f1e5bef3-8136-4735-8df3-77b819d042be", "metadata": {}, "source": ["#### alert "]}, {"cell_type": "code", "execution_count": null, "id": "f4dee3bf-b243-4642-a6d5-f0cec1a801ff", "metadata": {}, "outputs": [], "source": ["# date_today = datetime.today().date()"]}, {"cell_type": "code", "execution_count": null, "id": "29bc8f46-dc6a-4bc6-974f-a838132584bf", "metadata": {}, "outputs": [], "source": ["# if process == \"ordering\":\n", "#     offset = 5\n", "# else:\n", "#     offset = 6"]}, {"cell_type": "code", "execution_count": null, "id": "79c0f42c-f81e-414c-bee2-374df848bb01", "metadata": {}, "outputs": [], "source": ["# outlet_cluster_map_query = \"\"\"SELECT DISTINCT CASE\n", "#     WHEN is_hp = 1 THEN CLUSTER || '_HP'\n", "#     ELSE CLUSTER  -- Specify the desired result when is_hp is not 1\n", "#   END AS cluster,\n", "#                   city,\n", "#                   outlet_id,\n", "#                   item_id\n", "#   FROM\n", "#      (SELECT fa.cluster,\n", "#              fa.is_hp,\n", "#              fa.item_id,\n", "#              fa.ssc_id AS outlet_id,\n", "#              ofm.city,\n", "#              dense_rank() OVER (PARTITION BY fa.cluster,ofm.city,fa.is_hp\n", "#                                 ORDER BY fa.ssc_id) AS outlet_rank\n", "#       FROM supply_etls.dark_stores_fnv_assortment fa\n", "#       LEFT JOIN (select distinct pos_outlet_id as outlet_id, pos_outlet_city_name as city from dwh.dim_merchant_outlet_facility_mapping) ofm on ofm.outlet_id = fa.ssc_id\n", "#       LEFT JOIN lake_retail.console_outlet co ON co.id = fa.ssc_id\n", "#       WHERE co.active = 1\n", "#       AND fa.date_ist >= current_date - interval '15' day and fa.date_ist = (SELECT max(date_ist) as date_ist FROM supply_etls.dark_stores_fnv_assortment)\n", "#       GROUP BY 1,\n", "#               2,\n", "#               3,\n", "#               4,\n", "#               5)\"\"\"\n", "# outlet_cluster_mapping_df = get_results_from_query(outlet_cluster_map_query)"]}, {"cell_type": "code", "execution_count": null, "id": "9be66187-8ad7-4d3b-bf16-46215f26162a", "metadata": {}, "outputs": [], "source": ["# cluster_demand_query = f\"\"\"with outlet_cluster_mapping AS\n", "# (SELECT DISTINCT CASE\n", "#     WHEN is_hp = 1 THEN CLUSTER || '_HP'\n", "#     ELSE CLUSTER  -- Specify the desired result when is_hp is not 1\n", "#   END AS cluster,\n", "#                   city,\n", "#                   outlet_id,\n", "#                   item_id\n", "#   FROM\n", "#      (SELECT fa.cluster,\n", "#              fa.is_hp,\n", "#              fa.item_id,\n", "#              fa.ssc_id AS outlet_id,\n", "#              ofm.city,\n", "#              dense_rank() OVER (PARTITION BY fa.cluster,ofm.city,fa.is_hp\n", "#                                 ORDER BY fa.ssc_id) AS outlet_rank\n", "#       FROM supply_etls.dark_stores_fnv_assortment fa\n", "#       LEFT JOIN (select distinct pos_outlet_id as outlet_id, pos_outlet_city_name as city from dwh.dim_merchant_outlet_facility_mapping) ofm on ofm.outlet_id = fa.ssc_id\n", "#       LEFT JOIN lake_retail.console_outlet co ON co.id = fa.ssc_id\n", "#       WHERE co.active = 1\n", "#       AND fa.date_ist >= current_date - interval '15' day\n", "#       GROUP BY 1,\n", "#               2,\n", "#               3,\n", "#               4,\n", "#               5)),\n", "\n", "# fnv_assortment AS\n", "#       (SELECT DISTINCT fva.ssc_id AS outlet_id,\n", "#                    acs.facility_id,\n", "#                    fva.item_id,\n", "#                    'fnv' AS assortment_type\n", "#    FROM supply_etls.dark_stores_fnv_assortment fva\n", "#    INNER JOIN ds_etls.demand_forecasting_active_outlets acs ON fva.ssc_id = acs.outlet_id\n", "#    WHERE fva.date_ist >= current_date - interval '15' day)\n", "\n", "\n", "# select c.cluster, a.checkout_date, a.item_id,sum(a.item_demand) as item_demand from ds_etls.demand_forecasting_mview_frontend_outlet_item_daily_demand a join fnv_assortment b ON a.outlet_id = b.outlet_id\n", "# AND a.item_id = b.item_id\n", "# left join outlet_cluster_mapping c on a.outlet_id = c.outlet_id\n", "# where a.checkout_date in (current_date - interval '1' day, current_date - interval '{offset}' day)\n", "# group by 1,2,3\"\"\"\n", "\n", "# cluster_demand_df = get_results_from_query(cluster_demand_query)"]}, {"cell_type": "code", "execution_count": null, "id": "28b2b8ce-b3bb-4b50-9ae6-3d17ce01d473", "metadata": {}, "outputs": [], "source": ["# cluster_demand_df[\"checkout_date\"] = pd.to_datetime(cluster_demand_df[\"checkout_date\"]).astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "1d852dd8-a944-474d-9387-3e2ff080a8c4", "metadata": {}, "outputs": [], "source": ["# t_1_date = str(date_today - timed<PERSON>ta(days=1))\n", "# t_7_date = str(date_today - timedelta(days=offset))\n", "\n", "# cluster_demand_df[\"checkout_date\"] = (\n", "#     cluster_demand_df[\"checkout_date\"]\n", "#     .astype(str)\n", "#     .map({t_1_date: \"T-1 Sales\", t_7_date: \"T-7 Sales\"})\n", "# )\n", "\n", "# cluster_demand_df = cluster_demand_df.pivot_table(\n", "#     index=[\"cluster\"], columns=[\"checkout_date\"], values=[\"item_demand\"]\n", "# )\n", "# cluster_demand_df.columns = [\"_\".join(col).strip(\"_\") for col in cluster_demand_df.columns]\n", "# cluster_demand_df = cluster_demand_df.reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "b395c368-1108-4566-9d1f-ace91380bbbe", "metadata": {}, "outputs": [], "source": ["# cluster_demand_df[[\"item_demand_T-1 Sales\", \"item_demand_T-7 Sales\"]] = cluster_demand_df[\n", "#     [\"item_demand_T-1 Sales\", \"item_demand_T-7 Sales\"]\n", "# ].round()"]}, {"cell_type": "code", "execution_count": null, "id": "e933c3d3-db6f-4f6f-9deb-1e02cf1aa087", "metadata": {}, "outputs": [], "source": ["# cluster_demand_df = cluster_demand_df.merge(\n", "#     final_updated_df.merge(\n", "#         outlet_cluster_mapping_df[[\"cluster\", \"outlet_id\", \"item_id\"]].drop_duplicates(),\n", "#         on=[\"outlet_id\", \"item_id\"],\n", "#         how=\"inner\",\n", "#     )\n", "#     .groupby(\"cluster\")[\"max_qty\"]\n", "#     .sum()\n", "#     .reset_index()\n", "# )[[\"cluster\", \"item_demand_T-1 Sales\", \"max_qty\", \"item_demand_T-7 Sales\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "1f9f7e57-4061-4860-882f-dd583552dc70", "metadata": {}, "outputs": [], "source": ["# fig, ax = render_mpl_table(cluster_demand_df)\n", "# fname = f\"transfers_table.png\"\n", "# fig.savefig(fname, bbox_inches=\"tight\", pad_inches=0, dpi=200)\n", "\n", "pb.send_slack_message(\n", "    channel=\"bl-fnv-replenishment-dag-run-alerts\",\n", "    text=f\"*Replenishment Update Alert*: Ordering table updated through Adhoc Dag!\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2c70c33b-db9d-4cce-8e4c-d6c06484c67c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}