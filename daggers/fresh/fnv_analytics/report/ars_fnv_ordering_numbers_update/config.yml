alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: ars_fnv_ordering_numbers_update
dag_type: report
escalation_priority: low
execution_timeout: 120
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: fresh
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U07668YK86R
path: fresh/fnv_analytics/report/ars_fnv_ordering_numbers_update
paused: false
pool: fresh_pool
project_name: fnv_analytics
schedule:
  end_date: '2025-09-05T00:00:00'
  interval: 30 16 * * *
  start_date: '2025-06-09T00:00:00'
schedule_type: fixed
sla: 123 minutes
support_files: []
tags: []
template_name: notebook
version: 1
