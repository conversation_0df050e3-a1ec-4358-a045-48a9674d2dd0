alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: competition_pricing_dashboard_coverage
dag_type: report
escalation_priority: low
execution_timeout: 120
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: fresh
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U0899MAT7PT
path: fresh/fnv_analytics/report/competition_pricing_dashboard_coverage
paused: false
pool: fresh_pool
project_name: fnv_analytics
schedule:
  end_date: '2025-08-26T00:00:00'
  interval: 00 3 * * *
  start_date: '2025-05-27T00:00:00'
schedule_type: fixed
sla: 119 minutes
support_files: []
tags: []
template_name: notebook
version: 1
