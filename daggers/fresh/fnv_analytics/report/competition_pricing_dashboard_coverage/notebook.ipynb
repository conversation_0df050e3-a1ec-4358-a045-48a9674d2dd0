{"cells": [{"cell_type": "code", "execution_count": null, "id": "12e4fd99-3579-4dd3-a34a-93a45b14cfcc", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import os\n", "import time\n", "import gc\n", "import requests\n", "\n", "import calendar\n", "from datetime import datetime as dt\n", "\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.options.mode.chained_assignment = None  # default='warn'\n", "pd.set_option(\"display.max_columns\", 500)"]}, {"cell_type": "code", "execution_count": null, "id": "136c0bbc-512a-4318-a8b8-ae5127580b96", "metadata": {}, "outputs": [], "source": ["from functools import reduce\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "d428e9be-beaf-472d-a054-467da8925681", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 1\n", "    for attempt in range(max_tries):\n", "        print(\n", "            f\"Read attempt: {attempt}... \\nRun started at {datetime.now() + timed<PERSON>ta(hours = 5.5)}\"\n", "        )\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "markdown", "id": "b57b9f86-fb5e-4b23-81ed-073965d51527", "metadata": {}, "source": ["### Kvi_Qty"]}, {"cell_type": "code", "execution_count": null, "id": "47769be5-4bdb-4890-ad8c-929572095d0f", "metadata": {}, "outputs": [], "source": ["base_query = \"\"\"\n", "WITH item_product_mapping AS (\n", "    SELECT DISTINCT \n", "        COALESCE(ipr.item_id, ipom_0.item_id) AS item_id, \n", "        ipr.product_id, \n", "        COALESCE(ipom.multiplier, ipom_0.multiplier, 1) AS multiplier, \n", "        COALESCE(ipom_0.avg_selling_price_ratio, 1) AS avg_selling_price_ratio\n", "    FROM lake_rpc.item_product_mapping ipr\n", "    LEFT JOIN dwh.dim_item_product_offer_mapping ipom \n", "        ON ipr.product_id = ipom.product_id \n", "        AND ipr.item_id = ipom.item_id \n", "        AND ipom.is_current\n", "    LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 \n", "        ON ipr.product_id = ipom_0.product_id \n", "        AND ipom_0.is_current\n", "),\n", "perishable_skus_base AS (\n", "    SELECT DISTINCT \n", "        ma.item_id, \n", "        icd.name AS item_name, \n", "        pt.name AS product_type, \n", "        ipm.product_id, \n", "        icd.l2, \n", "        icd.l0\n", "    FROM rpc.product_product ma \n", "    JOIN (\n", "        SELECT item_id, MAX(id) AS id\n", "        FROM lake_rpc.product_product\n", "        WHERE approved = 1\n", "        GROUP BY item_id\n", "    ) b ON ma.item_id = b.item_id\n", "    JOIN rpc.item_category_details icd ON icd.item_id = ma.item_id\n", "    JOIN rpc.product_facility_master_assortment pfma ON pfma.item_id = ma.item_id\n", "    LEFT JOIN rpc.item_product_mapping ipm ON ipm.item_id = icd.item_id\n", "    LEFT JOIN cms.gr_product gp ON gp.id = ipm.product_id\n", "    LEFT JOIN cms.gr_product_type pt ON pt.id = gp.type_id\n", "    WHERE pfma.master_assortment_substate_id = 1\n", "    AND icd.l0_id IN (1487)\n", "),\n", "perishable_skus AS (\n", "    SELECT * \n", "    FROM perishable_skus_base \n", "    WHERE l2 NOT IN ('Other SKU')\n", "),\n", "\n", "product_id_mapping AS (\n", "    SELECT  \n", "        ipm.product_id,  \n", "        MAX(ipm.item_id) AS item_id\n", "    FROM rpc.item_product_mapping ipm\n", "    INNER JOIN rpc.item_category_details icd\n", "        ON icd.item_id = ipm.item_id\n", "        AND ipm.active = 1\n", "        AND ipm.lake_active_record = True\n", "        AND icd.l0_id = 1487\n", "    GROUP BY 1\n", "    HAVING COUNT(DISTINCT ipm.item_id) = 1\n", "),\n", "\n", "sales_data AS (\n", "    SELECT \n", "        DATE_TRUNC('day', o.order_create_dt_ist) AS date_,\n", "        o.city_name AS city,\n", "        ipm.item_id AS item_id,\n", "        ps.item_name AS item_name,\n", "        p.product_id as product_id,\n", "        ps.product_type,\n", "        ps.l2,\n", "        'null' AS rule_type,\n", "        SUM(o.procured_quantity * ipm.multiplier) AS item_sales,\n", "        SUM(o.procured_quantity * o.unit_selling_price * 1.000 * ipm.avg_selling_price_ratio) AS gmv,\n", "        (SUM(o.procured_quantity * o.unit_selling_price * ipm.avg_selling_price_ratio )/SUM(o.procured_quantity * ipm.multiplier)) AS asp,\n", "        SUM(o.procured_quantity * o.unit_weighted_landing_price * ipm.avg_selling_price_ratio)/SUM(o.procured_quantity * ipm.multiplier) AS wlp\n", "    FROM viz.category_etls_fact_sales_order_item_details o\n", "    LEFT JOIN item_product_mapping ipm ON o.product_id = ipm.product_id\n", "    LEFT JOIN rpc.item_category_details icd ON ipm.item_id = icd.item_id\n", "    LEFT JOIN perishable_skus ps ON ps.item_id = ipm.item_id\n", "    LEFT JOIN product_id_mapping p on p.item_id = ipm.item_id\n", "    WHERE icd.l0 IN ('Vegetables & Fruits') \n", "    AND o.order_create_dt_ist BETWEEN CURRENT_DATE - INTERVAL '1' day AND CURRENT_DATE - INTERVAL '0' day\n", "    AND o.order_current_status = 'DELIVERED'\n", "    AND (o.order_type NOT LIKE '%%internal%%' OR o.order_type IS NULL)\n", "    AND o.procured_quantity > 0\n", "    GROUP BY 1, 2, 3,4,5,6,7\n", "    ORDER BY 1 DESC\n", "),\n", "cumulative_sales AS (\n", "    SELECT \n", "        city,\n", "        item_id,\n", "        item_name,\n", "        l2,\n", "        product_id,\n", "        AVG(item_sales) AS avg_item_sales,\n", "        AVG(gmv) AS avg_gmv,\n", "        AVG(wlp) AS avg_wlp,\n", "        SUM(SUM(item_sales)) OVER (PARTITION BY city ORDER BY SUM(item_sales) DESC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) as running_sales,\n", "        SUM(SUM(item_sales)) OVER (PARTITION BY city) as total_sales\n", "    FROM sales_data\n", "    GROUP BY city, item_id, item_name, l2, product_id\n", "),\n", "final_selection AS (\n", "    SELECT \n", "        city,\n", "        item_id,\n", "        item_name,\n", "        l2,\n", "        product_id,\n", "        avg_wlp,\n", "        avg_item_sales,\n", "        avg_gmv,\n", "        running_sales,\n", "        total_sales,\n", "        100.0 * running_sales / total_sales AS cumulative_percentage\n", "    FROM cumulative_sales\n", "),\n", "ranked_selection AS (\n", "    SELECT \n", "        city,\n", "        item_id,\n", "        item_name,\n", "        l2,\n", "        product_id,\n", "        avg_wlp,\n", "        avg_item_sales,\n", "        avg_gmv,\n", "        cumulative_percentage,\n", "        RANK() OVER (PARTITION BY city ORDER BY avg_item_sales DESC) as rank\n", "    FROM final_selection\n", "    WHERE cumulative_percentage <= 75\n", ")\n", "SELECT \n", "    city,\n", "    item_id,\n", "    item_name,\n", "    l2,\n", "    product_id,\n", "    avg_item_sales,\n", "    avg_wlp,\n", "    avg_gmv,\n", "    cumulative_percentage,\n", "    rank as rank_qty\n", "FROM ranked_selection\n", "\"\"\"\n", "\n", "kvi_qty = read_sql_query(base_query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "6e76a084-b732-4e90-a39e-cad721830ffc", "metadata": {}, "outputs": [], "source": ["kvi_qty[\"city\"] = kvi_qty[\"city\"].str.lower()\n", "kvi_qty[\"city\"] = kvi_qty[\"city\"].replace([\"up-ncr\"], \"noida\")\n", "kvi_qty[\"city\"] = kvi_qty[\"city\"].replace([\"hr-ncr\"], \"gurgaon\")"]}, {"cell_type": "markdown", "id": "6e698862-b991-4706-9b49-6b7f85d27c4c", "metadata": {}, "source": ["### KVI Fruits"]}, {"cell_type": "code", "execution_count": null, "id": "96dbc26e-cd33-428a-a764-20d96d485438", "metadata": {}, "outputs": [], "source": ["base_query = \"\"\"\n", "WITH item_product_mapping AS (\n", "    SELECT DISTINCT \n", "        COALESCE(ipr.item_id, ipom_0.item_id) AS item_id, \n", "        ipr.product_id, \n", "        COALESCE(ipom.multiplier, ipom_0.multiplier, 1) AS multiplier, \n", "        COALESCE(ipom_0.avg_selling_price_ratio, 1) AS avg_selling_price_ratio\n", "    FROM lake_rpc.item_product_mapping ipr\n", "    LEFT JOIN dwh.dim_item_product_offer_mapping ipom \n", "        ON ipr.product_id = ipom.product_id \n", "        AND ipr.item_id = ipom.item_id \n", "        AND ipom.is_current\n", "    LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 \n", "        ON ipr.product_id = ipom_0.product_id \n", "        AND ipom_0.is_current\n", "),\n", "perishable_skus_base AS (\n", "    SELECT DISTINCT \n", "        ma.item_id, \n", "        icd.name AS item_name, \n", "        pt.name AS product_type, \n", "        ipm.product_id, \n", "        icd.l2, \n", "        icd.l0\n", "    FROM rpc.product_product ma \n", "    JOIN (\n", "        SELECT item_id, MAX(id) AS id\n", "        FROM lake_rpc.product_product\n", "        WHERE approved = 1\n", "        GROUP BY item_id\n", "    ) b ON ma.item_id = b.item_id\n", "    JOIN rpc.item_category_details icd ON icd.item_id = ma.item_id\n", "    JOIN rpc.product_facility_master_assortment pfma ON pfma.item_id = ma.item_id\n", "    LEFT JOIN rpc.item_product_mapping ipm ON ipm.item_id = icd.item_id\n", "    LEFT JOIN cms.gr_product gp ON gp.id = ipm.product_id\n", "    LEFT JOIN cms.gr_product_type pt ON pt.id = gp.type_id\n", "    WHERE pfma.master_assortment_substate_id = 1\n", "    AND icd.l0_id IN (1487)\n", "),\n", "perishable_skus AS (\n", "    SELECT * \n", "    FROM perishable_skus_base \n", "    WHERE l2 NOT IN ('Other SKU')\n", "),\n", "product_id_mapping AS (\n", "    SELECT  \n", "        ipm.product_id,  \n", "        MAX(ipm.item_id) AS item_id\n", "    FROM rpc.item_product_mapping ipm\n", "    INNER JOIN rpc.item_category_details icd\n", "        ON icd.item_id = ipm.item_id\n", "        AND ipm.active = 1\n", "        AND ipm.lake_active_record = True\n", "        AND icd.l0_id = 1487\n", "    GROUP BY 1\n", "    HAVING COUNT(DISTINCT ipm.item_id) = 1\n", "),\n", "cms as (\n", "select \n", "    product_id, \n", "    category_id\n", "\n", "from cms.gr_product_category_mapping\n", "where category_id in (1503)\n", "and lake_active_record\n", "group by 1,2\n", "),\n", "\n", "sales_data AS (\n", "    SELECT \n", "        DATE_TRUNC('day', o.order_create_dt_ist) AS date_,\n", "        o.city_name AS city,\n", "        ipm.item_id AS item_id,\n", "        ps.item_name AS item_name,\n", "        p.product_id as product_id,\n", "        ps.product_type,\n", "        ps.l2,\n", "        'null' AS rule_type,\n", "        SUM(o.procured_quantity * ipm.multiplier) AS item_sales,\n", "        SUM(o.procured_quantity * o.unit_selling_price * 1.000 * ipm.avg_selling_price_ratio) AS gmv,\n", "        (SUM(o.procured_quantity * o.unit_selling_price * ipm.avg_selling_price_ratio )/SUM(o.procured_quantity * ipm.multiplier)) AS asp,\n", "        SUM(o.procured_quantity * o.unit_weighted_landing_price * ipm.avg_selling_price_ratio)/SUM(o.procured_quantity * ipm.multiplier) AS wlp\n", "    FROM viz.category_etls_fact_sales_order_item_details o\n", "    LEFT JOIN item_product_mapping ipm ON o.product_id = ipm.product_id\n", "    LEFT JOIN rpc.item_category_details icd ON ipm.item_id = icd.item_id\n", "    LEFT JOIN perishable_skus ps ON ps.item_id = ipm.item_id\n", "    LEFT JOIN product_id_mapping p on p.item_id = ipm.item_id\n", "    JOIN cms ON cms.product_id = o.product_id \n", "    WHERE icd.l0 IN ('Vegetables & Fruits') \n", "    AND icd.l2 IN ('Fresh Fruits', 'Exotics', 'Certified Organic', 'Freshly Cut & Sprouts')\n", "    AND o.order_create_dt_ist BETWEEN CURRENT_DATE - INTERVAL '1' day AND CURRENT_DATE - INTERVAL '0' day\n", "    AND o.order_current_status = 'DELIVERED'\n", "    AND (o.order_type NOT LIKE '%%internal%%' OR o.order_type IS NULL)\n", "    AND o.procured_quantity > 0\n", "    GROUP BY 1, 2, 3,4,5,6,7\n", "    ORDER BY 1 DESC\n", "),\n", "cumulative_sales AS (\n", "    SELECT \n", "        city,\n", "        item_id,\n", "        item_name,\n", "        l2,\n", "        product_id,\n", "        AVG(item_sales) AS avg_item_sales,\n", "        AVG(gmv) AS avg_gmv,\n", "        AVG(wlp) AS avg_wlp,\n", "        SUM(SUM(item_sales)) OVER (PARTITION BY city ORDER BY SUM(item_sales) DESC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) as running_sales,\n", "        SUM(SUM(item_sales)) OVER (PARTITION BY city) as total_sales\n", "    FROM sales_data\n", "    GROUP BY city, item_id, item_name, l2, product_id\n", "),\n", "final_selection AS (\n", "    SELECT \n", "        city,\n", "        item_id,\n", "        item_name,\n", "        l2,\n", "        product_id,\n", "        avg_wlp,\n", "        avg_item_sales,\n", "        avg_gmv,\n", "        running_sales,\n", "        total_sales,\n", "        100.0 * running_sales / total_sales AS cumulative_percentage\n", "    FROM cumulative_sales\n", "),\n", "ranked_selection AS (\n", "    SELECT \n", "        city,\n", "        item_id,\n", "        item_name,\n", "        l2,\n", "        product_id,\n", "        avg_wlp,\n", "        avg_item_sales,\n", "        avg_gmv,\n", "        cumulative_percentage,\n", "        RANK() OVER (PARTITION BY city ORDER BY avg_item_sales DESC) as rank\n", "    FROM final_selection\n", "    WHERE cumulative_percentage <= 70\n", ")\n", "SELECT \n", "    city,\n", "    item_id,\n", "    item_name,\n", "    l2,\n", "    product_id,\n", "    avg_item_sales,\n", "    avg_wlp,\n", "    avg_gmv,\n", "    cumulative_percentage,\n", "    rank as rank_fruits\n", "FROM ranked_selection\n", "\"\"\"\n", "\n", "kvi_fruits = read_sql_query(base_query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "6a56a7ff-1140-482a-a7fe-348e200120f2", "metadata": {}, "outputs": [], "source": ["kvi_fruits[\"city\"] = kvi_fruits[\"city\"].str.lower()\n", "kvi_fruits[\"city\"] = kvi_fruits[\"city\"].replace([\"up-ncr\"], \"noida\")\n", "kvi_fruits[\"city\"] = kvi_fruits[\"city\"].replace([\"hr-ncr\"], \"gurgaon\")"]}, {"cell_type": "markdown", "id": "a71341cb-3c8e-4659-98e1-5da3c9aea197", "metadata": {}, "source": ["### KVI"]}, {"cell_type": "code", "execution_count": null, "id": "72f19e3c-3341-4fd8-9092-cdc1bbd6f20b", "metadata": {}, "outputs": [], "source": ["columns_to_fill = [\"avg_item_sales\", \"avg_wlp\", \"avg_gmv\", \"cumulative_percentage\"]"]}, {"cell_type": "code", "execution_count": null, "id": "cf00a978-7b1e-419e-8326-30761a10ac24", "metadata": {}, "outputs": [], "source": ["merged_df = pd.merge(\n", "    kvi_qty,\n", "    kvi_fruits[\n", "        [\"city\", \"item_id\", \"item_name\", \"l2\", \"product_id\", \"rank_fruits\"] + columns_to_fill\n", "    ],\n", "    on=[\"city\", \"item_id\", \"item_name\", \"l2\", \"product_id\"],\n", "    how=\"outer\",\n", "    suffixes=(\"_qty\", \"_fruits\"),\n", ")\n", "\n", "for column in columns_to_fill:\n", "    merged_df[column] = merged_df[f\"{column}_qty\"].fillna(merged_df[f\"{column}_fruits\"])\n", "\n", "merged_df.drop(\n", "    columns=[f\"{col}_qty\" for col in columns_to_fill]\n", "    + [f\"{col}_fruits\" for col in columns_to_fill],\n", "    errors=\"ignore\",\n", "    inplace=True,\n", ")\n", "\n", "final_columns = [\n", "    \"city\",\n", "    \"item_id\",\n", "    \"item_name\",\n", "    \"l2\",\n", "    \"product_id\",\n", "    \"avg_item_sales\",\n", "    \"avg_wlp\",\n", "    \"avg_gmv\",\n", "    \"cumulative_percentage\",\n", "    \"rank_qty\",\n", "    \"rank_fruits\",\n", "]\n", "\n", "merged_df = merged_df[final_columns]\n", "\n", "kvi = merged_df"]}, {"cell_type": "markdown", "id": "ba280454-3e46-4989-99c1-d9a44816c35d", "metadata": {}, "source": ["### Competition_data"]}, {"cell_type": "code", "execution_count": null, "id": "8ce9ee96-0c21-4151-a27b-64d8758b4d71", "metadata": {}, "outputs": [], "source": ["competition_base_query = \"\"\"\n", "WITH\n", "zeptosupersaver_ AS (\n", "    SELECT \n", "        DISTINCT\n", "        date(at_date) as date_,\n", "        pid as product_id,\n", "        title as product_name,\n", "        lower(city) as city,\n", "        zeptosupersaver_availability as comp_availability,\n", "        zeptosupersaver_productcode as comp_productcode,\n", "        zeptosupersaver_mrp as comp_mrp,\n", "        zeptosupersaver_selling_price as comp_selling_price,\n", "        multiplier,\n", "        'zss' as competitor_name\n", "    FROM category_etls.blinkit_competitormaster_price c \n", "    INNER JOIN dwh.dim_product dp ON c.pid = dp.product_id AND is_current\n", "    WHERE \n", "        at_date >= cast(current_date - interval '1' day as varchar) \n", "        AND zeptosupersaver_mrp > 0 AND zeptosupersaver_selling_price > 0 AND dp.l0_category_id =1487\n", "),\n", "\n", "zepto AS (\n", "    SELECT \n", "        cast(np.created_at_dt as date) as date_,\n", "        cast(np.pid as int) as product_id,\n", "        product_name,\n", "        lower(np.city) city,\n", "        cast(cast(np.zepto_availability as decimal(10,2)) as varchar) as comp_availability,\n", "        np.zepto_productcode as comp_productcode,\n", "        case when cast(np.zepto_mrp as decimal(10,2)) > 0 then cast(np.zepto_mrp as decimal(10,2)) else 0 end as comp_mrp ,\n", "        case when cast(np.zepto_selling_price as decimal(10,2)) > 0 then cast(zepto_selling_price as decimal(10,2)) else 0 end as comp_selling_price,\n", "        np.multiplier as multiplier,\n", "        'zepto' as competitor_name\n", "    FROM consumer_etls.intellolabs_blinkit_app_mapping_onetomany np\n", "    INNER JOIN dwh.dim_product dp ON cast(dp.product_id as int)=cast(np.pid as int) AND is_current = true\n", "    WHERE np.zepto_availability >=0 \n", "    AND cast(created_at_dt as date) >= cast(current_date - interval '1' day as date) \n", "    AND dp.l0_category_id =1487\n", "),\n", "\n", "swiggy AS (\n", "    SELECT \n", "        cast(np.created_at_dt as date) as date_,\n", "        cast(np.pid as int) as product_id,\n", "        product_name,\n", "        lower(np.city) city,\n", "        cast(cast(np.swiggy_availability as decimal(10,2)) as varchar) as comp_availability,\n", "        np.swiggy_productcode as comp_productcode,\n", "        case when cast(np.swiggy_mrp as decimal(10,2)) > 0 then cast(np.swiggy_mrp as decimal(10,2)) else 0 end as comp_mrp ,\n", "        case when cast(np.swiggy_selling_price as decimal(10,2)) > 0 then cast(swiggy_selling_price as decimal(10,2)) else 0 end as comp_selling_price,\n", "        np.multiplier as multiplier,\n", "        'swiggy' as competitor_name\n", "    FROM consumer_etls.intellolabs_blinkit_app_mapping_onetomany np\n", "    INNER JOIN dwh.dim_product dp ON cast(dp.product_id as int)=cast(np.pid as int) AND is_current = true\n", "    WHERE np.swiggy_availability >=0 \n", "    AND cast(created_at_dt as date) >= cast(current_date - interval '1' day as date) \n", "    AND dp.l0_category_id =1487\n", "),\n", "\n", "-- bbdaily AS (\n", "--     SELECT \n", "--         DISTINCT\n", "--         date(at_date) as date_,\n", "--         product_id,\n", "--         product_name,\n", "--         lower(city) as city,\n", "--         competitor_availability as comp_availability,\n", "--         competitor_productcode as comp_productcode,\n", "--         competitor_mrp as comp_mrp,\n", "--         competitor_sp as comp_selling_price,\n", "--         multiplier,\n", "--         'bbdaily' as competitor_name\n", "--     FROM category_etls.vendor_competitor_prices a \n", "--     INNER JOIN dwh.dim_product dp ON a.pid = dp.product_id AND is_current\n", "--     WHERE cast(at_date as date) >= cast(current_date - interval '1' day as date)\n", "--     AND competitor_name = 'bbdaily'\n", "--     AND competitor_sp > 0\n", "--     AND dp.l0_category_id =1487\n", "-- ),\n", "\n", "bbnow AS (\n", "    SELECT \n", "        DISTINCT\n", "        date(at_date) as date_,\n", "        product_id,\n", "        product_name,\n", "        lower(city) as city,\n", "        competitor_availability as comp_availability,\n", "        competitor_productcode as comp_productcode,\n", "        competitor_mrp as comp_mrp,\n", "        competitor_sp as comp_selling_price,\n", "        multiplier,\n", "        'bbnow' as competitor_name\n", "    FROM category_etls.vendor_competitor_prices a \n", "    INNER JOIN dwh.dim_product dp ON a.pid = dp.product_id AND is_current\n", "    WHERE cast(at_date as date) >= cast(current_date - interval '1' day as date)\n", "    AND competitor_name = 'bbnow'\n", "    AND competitor_sp > 0\n", "    AND dp.l0_category_id =1487\n", "),\n", "\n", "bigbasket AS (\n", "    SELECT \n", "        DISTINCT\n", "        date(at_date) as date_,\n", "        product_id,\n", "        product_name,\n", "        lower(city) as city,\n", "        competitor_availability as comp_availability,\n", "        competitor_productcode as comp_productcode,\n", "        competitor_mrp as comp_mrp,\n", "        competitor_sp as comp_selling_price,\n", "        multiplier,\n", "        'bigbasket' as competitor_name\n", "    FROM category_etls.vendor_competitor_prices a \n", "    INNER JOIN dwh.dim_product dp ON a.pid = dp.product_id AND is_current\n", "    WHERE cast(at_date as date) >= cast(current_date - interval '1' day as date)\n", "    AND competitor_name = 'bigbasket'\n", "    AND competitor_sp > 0\n", "    AND dp.l0_category_id =1487\n", ")\n", "\n", "SELECT * FROM zeptosupersaver_ \n", "UNION ALL\n", "SELECT * FROM zepto\n", "UNION ALL\n", "SELECT * FROM swiggy\n", "--UNION ALL\n", "--SELECT * FROM bbdaily\n", "UNION ALL\n", "SELECT * FROM bbnow\n", "UNION ALL\n", "SELECT * FROM bigbasket\n", "\"\"\"\n", "\n", "competition = read_sql_query(competition_base_query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "a9cbfba6-5a87-407e-a660-9051acd4c84f", "metadata": {}, "outputs": [], "source": ["competition = competition[competition[\"comp_selling_price\"] > 0]"]}, {"cell_type": "markdown", "id": "d4c9df1a-4c47-401c-815a-d70c67b765c1", "metadata": {}, "source": ["### Blinkit_Sales_Data"]}, {"cell_type": "code", "execution_count": null, "id": "bf06906f-08a3-4c49-9b86-3aead96cfde8", "metadata": {}, "outputs": [], "source": ["base_query = \"\"\"\n", "WITH item_product_mapping AS (\n", "    SELECT DISTINCT \n", "        COALESCE(ipr.item_id, ipom_0.item_id) AS item_id, \n", "        ipr.product_id, \n", "        COALESCE(ipom.multiplier, ipom_0.multiplier, 1) AS multiplier, \n", "        COALESCE(ipom_0.avg_selling_price_ratio, 1) AS avg_selling_price_ratio\n", "    FROM lake_rpc.item_product_mapping ipr\n", "    LEFT JOIN dwh.dim_item_product_offer_mapping ipom \n", "        ON ipr.product_id = ipom.product_id \n", "        AND ipr.item_id = ipom.item_id \n", "        AND ipom.is_current\n", "    LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 \n", "        ON ipr.product_id = ipom_0.product_id \n", "        AND ipom_0.is_current\n", "),\n", "perishable_skus AS (\n", "    SELECT DISTINCT \n", "        ma.item_id, \n", "        icd.name AS item_name, \n", "        pt.name AS product_type, \n", "        ipm.product_id, \n", "        icd.l2, \n", "        icd.l0\n", "    FROM rpc.product_product ma \n", "    JOIN (\n", "        SELECT item_id, MAX(id) AS id\n", "        FROM lake_rpc.product_product\n", "        WHERE approved = 1\n", "        GROUP BY item_id\n", "    ) b ON ma.item_id = b.item_id\n", "    JOIN rpc.item_category_details icd ON icd.item_id = ma.item_id\n", "    JOIN rpc.product_facility_master_assortment pfma ON pfma.item_id = ma.item_id\n", "    LEFT JOIN rpc.item_product_mapping ipm ON ipm.item_id = icd.item_id\n", "    LEFT JOIN cms.gr_product gp ON gp.id = ipm.product_id\n", "    LEFT JOIN cms.gr_product_type pt ON pt.id = gp.type_id\n", "    WHERE pfma.master_assortment_substate_id = 1\n", "    AND icd.l0_id IN (1487)\n", "    AND icd.l2 NOT IN ('Other SKU')\n", "),\n", "\n", "product_id_mapping AS (\n", "    SELECT  \n", "        ipm.product_id,  \n", "        MAX(ipm.item_id) AS item_id\n", "    FROM rpc.item_product_mapping ipm\n", "    INNER JOIN rpc.item_category_details icd\n", "        ON icd.item_id = ipm.item_id\n", "        AND ipm.active = 1\n", "        AND ipm.lake_active_record = True\n", "        AND icd.l0_id = 1487\n", "    GROUP BY 1\n", "    HAVING COUNT(DISTINCT ipm.item_id) = 1\n", ")\n", "\n", "SELECT \n", "    DATE_TRUNC('day', o.order_create_dt_ist) AS date_,\n", "    o.city_name AS city,\n", "    p.product_id as product_id,\n", "    ps.product_type,\n", "    ps.l2,\n", "    ipm.item_id AS item_id,\n", "    ps.item_name AS item_name,\n", "    SUM(o.procured_quantity * ipm.multiplier) AS item_sales,\n", "    SUM(o.procured_quantity * o.unit_selling_price * 1.000 * ipm.avg_selling_price_ratio) AS item_gmv,\n", "    (SUM(o.procured_quantity * o.unit_selling_price * ipm.avg_selling_price_ratio )/SUM(o.procured_quantity * ipm.multiplier)) AS asp,\n", "    SUM(o.procured_quantity * o.unit_weighted_landing_price * ipm.avg_selling_price_ratio)/SUM(o.procured_quantity * ipm.multiplier) AS wlp\n", "FROM viz.category_etls_fact_sales_order_item_details o\n", "LEFT JOIN item_product_mapping ipm ON o.product_id = ipm.product_id\n", "LEFT JOIN rpc.item_category_details icd ON ipm.item_id = icd.item_id\n", "LEFT JOIN perishable_skus ps ON ps.item_id = ipm.item_id\n", "LEFT JOIN product_id_mapping p on p.item_id = ipm.item_id\n", "WHERE icd.l0 IN ('Vegetables & Fruits') \n", "AND o.order_create_dt_ist BETWEEN CURRENT_DATE - INTERVAL '3' day AND CURRENT_DATE - INTERVAL '0' day\n", "AND o.order_current_status = 'DELIVERED'\n", "AND (o.order_type NOT LIKE '%%internal%%' OR o.order_type IS NULL)\n", "AND o.procured_quantity > 0\n", "GROUP BY 1, 2, 3,4,5,6,7\n", "ORDER BY 1 DESC\n", "\"\"\"\n", "blinkit_1 = read_sql_query(base_query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "eeb81044-b49b-41b1-9a48-a586e9f718fd", "metadata": {}, "outputs": [], "source": ["blinkit = blinkit_1"]}, {"cell_type": "code", "execution_count": null, "id": "a80633f1-83c4-4fab-944d-37fb8aff6451", "metadata": {}, "outputs": [], "source": ["blinkit[\"date_\"] = pd.to_datetime(blinkit[\"date_\"])\n", "blinkit[\"date_\"] = blinkit[\"date_\"].dt.strftime(\"%Y-%m-%d\")\n", "blinkit = blinkit.sort_values(\"date_\")\n", "blinkit[\"city\"] = blinkit[\"city\"].str.lower()\n", "blinkit[\"city\"] = blinkit[\"city\"].replace([\"up-ncr\"], \"noida\")\n", "blinkit[\"city\"] = blinkit[\"city\"].replace([\"hr-ncr\"], \"gurgaon\")"]}, {"cell_type": "markdown", "id": "1a0e8d5b-5562-4ab1-8703-b982c8c49241", "metadata": {}, "source": ["### Competition_completion"]}, {"cell_type": "code", "execution_count": null, "id": "b5227774-61c1-44dd-8faf-42866ba85c1a", "metadata": {}, "outputs": [], "source": ["competition_1 = competition\n", "competition_1[\"flag\"] = 1\n", "\n", "df_flags = competition_1.pivot_table(\n", "    index=[\"city\", \"product_id\"], columns=\"competitor_name\", values=\"flag\", fill_value=0\n", ").reset_index()\n", "\n", "df_flags.columns.name = None\n", "df_flags = df_flags.rename(columns=lambda x: f\"{x}_flag\" if x not in [\"city\", \"product_id\"] else x)\n", "\n", "merged_df = pd.merge(kvi, df_flags, how=\"left\", on=[\"city\", \"product_id\"])\n", "\n", "merged_df[\"any_flag\"] = (\n", "    merged_df[[\"bigbasket_flag\", \"bbnow_flag\", \"swiggy_flag\", \"zepto_flag\", \"zss_flag\"]]\n", "    .any(axis=1)\n", "    .astype(int)\n", ")\n", "\n", "merged_df.sort_values([\"city\", \"rank_qty\", \"rank_fruits\"], na_position=\"last\").fillna(0)\n", "\n", "merged_df_1 = merged_df.sort_values([\"city\", \"rank_qty\", \"rank_fruits\"], na_position=\"last\").fillna(\n", "    0\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fb6bef72-a6f1-4033-bc60-67cf1147f9e7", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    merged_df_1,\n", "    \"1oJzPfa25yG2x5gIpjAWWtqjXScuiPHmxCSjYzrnToeA\",\n", "    \"top_itemsXcity (75%) final\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "cece4b22-b5ee-4049-8613-0c5e50775d3c", "metadata": {}, "outputs": [], "source": ["top_cities = blinkit.groupby(\"city\")[\"item_sales\"].sum().nlargest(10).index\n", "top_items_by_city = {\n", "    city: blinkit_1[blinkit[\"city\"] == city]\n", "    .groupby(\"item_id\")[\"item_sales\"]\n", "    .sum()\n", "    .nlar<PERSON>t(10)\n", "    .index\n", "    for city in top_cities\n", "}\n", "top_items_by_city_df = pd.DataFrame(\n", "    [(city, item_id) for city, items in top_items_by_city.items() for item_id in items],\n", "    columns=[\"city\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "83b2d648-e557-4203-92b4-b67976502ffe", "metadata": {}, "outputs": [], "source": ["alerts = merged_df.merge(top_items_by_city_df, on=[\"city\", \"item_id\"], how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "id": "e7ada619-352f-47ae-b96c-8395b56a4523", "metadata": {}, "outputs": [], "source": ["bigbasket_alerts = alerts[alerts[\"bigbasket_flag\"] == 0][\n", "    [\"city\", \"product_id\", \"item_id\", \"item_name\", \"bigbasket_flag\"]\n", "]\n", "bbnow_alerts = alerts[alerts[\"bbnow_flag\"] == 0][\n", "    [\"city\", \"product_id\", \"item_id\", \"item_name\", \"bbnow_flag\"]\n", "]\n", "swiggy_alerts = alerts[alerts[\"swiggy_flag\"] == 0][\n", "    [\"city\", \"product_id\", \"item_id\", \"item_name\", \"swiggy_flag\"]\n", "]\n", "zepto_alerts = alerts[alerts[\"zepto_flag\"] == 0][\n", "    [\"city\", \"product_id\", \"item_id\", \"item_name\", \"zepto_flag\"]\n", "]\n", "zss_alerts = alerts[alerts[\"zss_flag\"] == 0][\n", "    [\"city\", \"product_id\", \"item_id\", \"item_name\", \"zss_flag\"]\n", "]\n", "\n", "\n", "bigbasket_alerts = bigbasket_alerts.drop(columns=[\"bigbasket_flag\"]).assign(\n", "    competition_name=\"bigbasket\"\n", ")\n", "bbnow_alerts = bbnow_alerts.drop(columns=[\"bbnow_flag\"]).assign(competition_name=\"bbnow\")\n", "swiggy_alerts = swiggy_alerts.drop(columns=[\"swiggy_flag\"]).assign(competition_name=\"swiggy\")\n", "zepto_alerts = zepto_alerts.drop(columns=[\"zepto_flag\"]).assign(competition_name=\"zepto\")\n", "zss_alerts = zss_alerts.drop(columns=[\"zss_flag\"]).assign(competition_name=\"zss\")"]}, {"cell_type": "code", "execution_count": null, "id": "d56eeb09-6da7-41c6-b449-f1430a1da7b5", "metadata": {}, "outputs": [], "source": ["all_alerts = pd.concat(\n", "    [bigbasket_alerts, bbnow_alerts, swiggy_alerts, zepto_alerts, zss_alerts], ignore_index=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f95a202c-3010-4ca7-a081-7d7e25426fc6", "metadata": {}, "outputs": [], "source": ["# all_alerts.to_csv('all_alerts.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "f5adeac7-fd48-4e69-b844-5d9754829144", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    all_alerts,\n", "    \"1oJzPfa25yG2x5gIpjAWWtqjXScuiPHmxCSjYzrnToeA\",\n", "    \"alerts_raw\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c4ef8bb7-767f-4052-ba22-f270f9b4a62b", "metadata": {}, "outputs": [], "source": ["top_cities = merged_df.groupby(\"city\")[\"avg_item_sales\"].sum().nlargest(20).index\n", "top_cities_df = merged_df[merged_df[\"city\"].isin(top_cities)]\n", "\n", "top_items_rank_qty = top_cities_df.sort_values(\"rank_qty\").groupby(\"city\").head(10)\n", "top_items_rank_fruits = top_cities_df.sort_values(\"rank_fruits\").groupby(\"city\").head(5)\n", "\n", "combined_top_items = (\n", "    pd.concat([top_items_rank_qty, top_items_rank_fruits]).drop_duplicates().fillna(0)\n", ")\n", "\n", "result_df = merged_df.merge(\n", "    combined_top_items.drop(columns=[\"avg_item_sales\", \"rank_qty\", \"rank_fruits\"]),\n", "    on=[\"city\", \"item_id\", \"item_name\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "154f34c2-94e5-435b-90b0-0224800e931f", "metadata": {}, "outputs": [], "source": ["df_old = merged_df_1.copy()\n", "\n", "item_sales = blinkit.groupby([\"city\", \"product_id\"])[\"item_sales\"].sum().reset_index()\n", "\n", "city_sales = blinkit.groupby([\"city\"])[\"item_sales\"].sum().reset_index()\n", "\n", "flag_cols = [col for col in df_old.columns if col.endswith(\"_flag\")]\n", "\n", "df_flags_old = df_old[[\"item_id\", \"product_id\", \"city\"] + flag_cols]\n", "\n", "df = df_flags.merge(item_sales, on=[\"city\", \"product_id\"], how=\"outer\").dropna(\n", "    subset=[\"item_sales\"]\n", ")\n", "\n", "df[\"bb_overall_flag\"] = df[[\"bigbasket_flag\", \"bbnow_flag\"]].max(axis=1)\n", "\n", "city_total_sales = df.groupby(\"city\")[\"item_sales\"].sum().reset_index(name=\"total_sales\")\n", "\n", "flag_cols = [col for col in df.columns if col.endswith(\"_flag\")]\n", "\n", "sales_ratios = {}\n", "for flag in flag_cols:\n", "    sales_ratios[flag] = (\n", "        df.groupby(\"city\")\n", "        .apply(lambda x: x.loc[x[flag] == 1, \"item_sales\"].sum() / x[\"item_sales\"].sum())\n", "        .reset_index(name=f\"{flag}_sales_ratio\")\n", "    )\n", "\n", "result = city_total_sales.copy()\n", "for flag in flag_cols:\n", "    result = result.merge(sales_ratios[flag], on=\"city\", how=\"left\")\n", "\n", "result = result.sort_values(by=\"total_sales\", ascending=False)\n", "\n", "result_1 = result[\n", "    [\n", "        \"city\",\n", "        \"swiggy_flag_sales_ratio\",\n", "        \"zepto_flag_sales_ratio\",\n", "        \"zss_flag_sales_ratio\",\n", "        \"bigbasket_flag_sales_ratio\",\n", "        \"bbnow_flag_sales_ratio\",\n", "    ]\n", "]\n", "\n", "# result_1.to_csv('outer_coverage_comp_data_new.csv')\n", "\n", "\n", "pb.to_sheets(\n", "    result_1,\n", "    \"1oJzPfa25yG2x5gIpjAWWtqjXScuiPHmxCSjYzrnToeA\",\n", "    \"city_completion_raw\",\n", ")"]}, {"cell_type": "markdown", "id": "87f8afa5-996b-4971-965c-afba4a588637", "metadata": {}, "source": ["### PID not Mapped Correctly"]}, {"cell_type": "code", "execution_count": null, "id": "5cbc8063-38e9-4265-875d-bc0c06635c69", "metadata": {}, "outputs": [], "source": ["df = competition[[\"city\", \"product_id\", \"competitor_name\"]].drop_duplicates()\n", "\n", "df_1 = blinkit[[\"city\", \"product_id\"]].drop_duplicates()\n", "\n", "diff_df = df.merge(df_1, on=[\"city\", \"product_id\"], how=\"left\", indicator=True)\n", "\n", "entries_in_df_not_in_df1 = diff_df[diff_df[\"_merge\"] == \"left_only\"].drop(columns=[\"_merge\"])"]}, {"cell_type": "code", "execution_count": null, "id": "a53ef950-d049-4055-be92-7fcdc730af0d", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT\n", "    lower(city) as city,\n", "    product_id,\n", "    competitor_name\n", "FROM category_etls.vendor_competitor_prices a \n", "INNER JOIN dwh.dim_product dp ON a.pid = dp.product_id AND is_current\n", "WHERE cast(at_date as date) >= cast(current_date - interval '1' day as date)\n", "AND competitor_name = 'bbdaily'\n", "AND competitor_sp > 0\n", "AND dp.l0_category_id =1487\n", "GROUP BY 1,2,3\n", "\"\"\"\n", "\n", "bbdaily = read_sql_query(query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "2c83bebb-ce9f-4255-98fe-0236643865bb", "metadata": {}, "outputs": [], "source": ["incorrectly_mapped_pid = pd.concat([bbdaily, entries_in_df_not_in_df1], ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "0850862b-6cff-4eae-be06-06ab18e0b6c5", "metadata": {}, "outputs": [], "source": ["# incorrectly_mapped_pid.to_csv('KVI_entries_mapped_inncorrectly.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "47831a99-e491-4f5e-8364-7446ec3802a6", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    incorrectly_mapped_pid,\n", "    \"1oJzPfa25yG2x5gIpjAWWtqjXScuiPHmxCSjYzrnToeA\",\n", "    \"PID incorrectly mapped (cleanup required)\",\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}