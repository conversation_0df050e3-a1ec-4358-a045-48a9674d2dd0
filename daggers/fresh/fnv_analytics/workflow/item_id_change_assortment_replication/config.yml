alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: item_id_change_assortment_replication
dag_type: workflow
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: fresh
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U05CCTXLBU1
path: fresh/fnv_analytics/workflow/item_id_change_assortment_replication
paused: false
pool: fresh_pool
project_name: fnv_analytics
schedule:
  end_date: '2025-08-29T00:00:00'
  interval: 30 16 * * *
  start_date: '2025-06-02T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
