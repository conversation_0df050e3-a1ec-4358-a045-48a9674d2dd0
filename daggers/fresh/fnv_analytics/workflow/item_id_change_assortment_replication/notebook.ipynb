{"cells": [{"cell_type": "code", "execution_count": null, "id": "cd38efbd-0874-4c8a-9742-3d000e3fca21", "metadata": {}, "outputs": [], "source": ["!pip uninstall -y openpyxl\n", "!pip install matplotlib\n", "!pip install openpyxl==3.1.5\n", "!pip install numpy==1.26.4\n", "\n", "import datetime\n", "import pencilbox as pb\n", "import requests\n", "import datetime\n", "from pytz import timezone\n", "import boto3\n", "import os\n", "import sys\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import json\n", "import openpyxl\n", "\n", "from matplotlib import pyplot as plt\n", "\n", "# CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "\n", "# SQL Connection\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "0987e7a6-8e8a-4cd9-9e12-f4eacb5456d2", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "with input as (\n", "select \n", "    a.facility_id as be_facility_id,\n", "    po.outlet_id as be_outlet_id,\n", "    po.outlet_name,\n", "    a.old_item_id,\n", "    a.new_item_id\n", "from \n", "    ars.item_cpd_replication a\n", "inner join\n", "    po.physical_facility_outlet_mapping po\n", "    on po.facility_id = a.facility_id\n", "    and po.active = 1\n", "    and po.lake_active_record = true\n", "where \n", "    a.mode = 'fnv'\n", "    and a.insert_ds_ist = cast(current_date as varchar)\n", "    and a.lake_active_record = true \n", "    and a.active = true\n", "    and a.facility_type = 'BACKEND'\n", "),\n", "\n", "universe_backend_store as (\n", "select\n", "    pfma.facility_id,\n", "    po.outlet_id,\n", "    po.outlet_name as store_name,\n", "    cast(tm.tag_value as int) be_outlet_id,\n", "    om.outlet_name be_outlet_name,\n", "    ic.item_id\n", "from\n", "    rpc.product_facility_master_assortment pfma\n", "inner join\n", "    rpc.item_category_details ic\n", "    on ic.item_id = pfma.item_id\n", "    and ic.l0_id = 1487\n", "    and pfma.master_assortment_substate_id in (1)\n", "    and ic.item_id in (select distinct old_item_id from input)\n", "inner join\n", "    po.physical_facility_outlet_mapping po\n", "    on po.facility_id = pfma.facility_id\n", "    and po.active = 1\n", "inner join   \n", "    rpc.item_outlet_tag_mapping tm\n", "    on pfma.item_id = tm.item_id \n", "    and po.outlet_id = tm.outlet_id\n", "    and tm.tag_type_id = 8 \n", "    and tm.active = 1\n", "inner join\n", "    po.physical_facility_outlet_mapping om\n", "    on cast(tm.tag_value as int) =  om.outlet_id\n", "    and om.active = 1\n", "    and cast(tm.tag_value as int) in (select distinct be_outlet_id from input)\n", "group by 1,2,3,4,5,6\n", ")\n", "\n", "select\n", "    u.facility_id as store_facility_id,\n", "    u.outlet_id as store_outlet_id,\n", "    u.store_name,\n", "    u.be_outlet_id,\n", "    u.be_outlet_name,\n", "    u.item_id,\n", "    i.be_outlet_id input_be_outlet_id,\n", "    i.old_item_id input_old_item_id,\n", "    i.new_item_id input_new_item_id\n", "from\n", "    universe_backend_store u\n", "inner join\n", "    po.physical_facility_outlet_mapping p\n", "    on u.facility_id = p.facility_id\n", "    and p.active = 1\n", "    and p.ars_active = 1\n", "inner join\n", "    retail.console_outlet co\n", "    on co.id = u.outlet_id\n", "    and co.active = 1\n", "    and co.lake_active_record = true\n", "    and co.business_type_id = 7\n", "left join\n", "    input i\n", "    on u.be_outlet_id = i.be_outlet_id\n", "    and u.item_id = i.old_item_id \n", "where\n", "    i.be_outlet_id is not null\n", "\n", "\"\"\"\n", "df1 = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "a3db0e97-5ec0-451b-a0ee-a592454d3fdd", "metadata": {}, "outputs": [], "source": ["df1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "bdee34b3-b89b-4f6e-9805-937f004f7ada", "metadata": {}, "outputs": [], "source": ["df1"]}, {"cell_type": "code", "execution_count": null, "id": "f062553f-3d6b-476b-afae-14da51e9940d", "metadata": {}, "outputs": [], "source": ["# https://redash-queries.grofer.io/queries/394818/source"]}, {"cell_type": "code", "execution_count": null, "id": "de8d34c0-4e2b-467d-b03b-1308f6116391", "metadata": {}, "outputs": [], "source": ["df1"]}, {"cell_type": "code", "execution_count": null, "id": "897922d8-9430-40ef-aac5-c97b128f93c3", "metadata": {}, "outputs": [], "source": ["list_facility_id = list(df1[\"store_facility_id\"])\n", "\n", "list_old_item_id = list(df1[\"input_old_item_id\"])\n", "list_new_item_id = list(df1[\"input_new_item_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "004af48c-71a7-4237-862d-945e3f9e81ce", "metadata": {}, "outputs": [], "source": ["temp_df_1 = pd.DataFrame(\n", "    {\"store_facility_id\": list_facility_id, \"input_old_item_id\": list_old_item_id}\n", ")\n", "\n", "# Create new DataFrame with required columns and values for temp inactivation\n", "input_df1 = pd.DataFrame(\n", "    {\n", "        \"item_id\": temp_df_1[\"input_old_item_id\"],\n", "        \"city_name\": np.nan,\n", "        \"backend_facility_id\": np.nan,\n", "        \"frontend_facility_id\": temp_df_1[\"store_facility_id\"],\n", "        \"master_assortment_substate\": \"Temporarily Inactive\",\n", "        \"assortment_type\": \"EXPRESS_ALL\",\n", "        \"substate_reason_type\": \"BAU\",\n", "        \"request_reason_type\": \"ITEM_ID_CHANGE_FNV_SCRIPT_TEMP_INACTIVATION\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "59f5dde8-d0cc-474c-8b40-a752d8e01a94", "metadata": {}, "outputs": [], "source": ["temp_df_2 = pd.DataFrame(\n", "    {\"store_facility_id\": list_facility_id, \"input_new_item_id\": list_new_item_id}\n", ")\n", "\n", "# Create new DataFrame with required columns and values for temp inactivation\n", "input_df2 = pd.DataFrame(\n", "    {\n", "        \"item_id\": temp_df_2[\"input_new_item_id\"],\n", "        \"city_name\": np.nan,\n", "        \"backend_facility_id\": np.nan,\n", "        \"frontend_facility_id\": temp_df_2[\"store_facility_id\"],\n", "        \"master_assortment_substate\": \"Active\",\n", "        \"assortment_type\": \"EXPRESS_ALL\",\n", "        \"substate_reason_type\": \"BAU\",\n", "        \"request_reason_type\": \"ITEM_ID_CHANGE_FNV_SCRIPT_ACTIVATION\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "408c4c93-952b-4713-b992-bba2b6d26d98", "metadata": {}, "outputs": [], "source": ["input_df2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3e4fb7f2-a369-4ac8-800d-0d700a09eee9", "metadata": {}, "outputs": [], "source": ["input_df = pd.concat([input_df1, input_df2], axis=0, ignore_index=True)\n", "input_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "4a051780-fbc9-4422-8bfc-25c599868104", "metadata": {}, "outputs": [], "source": ["req_columns = [\n", "    \"item_id\",\n", "    \"city_name\",\n", "    \"backend_facility_id\",\n", "    \"frontend_facility_id\",\n", "    \"master_assortment_substate\",\n", "    \"assortment_type\",\n", "    \"substate_reason_type\",\n", "    \"request_reason_type\",\n", "]\n", "column_types = {\n", "    \"item_id\": \"int\",\n", "    \"city_name\": \"str\",\n", "    \"backend_facility_id\": \"int\",\n", "    \"frontend_facility_id\": \"int\",\n", "    \"master_assortment_substate\": \"str\",\n", "    \"assortment_type\": \"str\",\n", "    \"substate_reason_type\": \"str\",\n", "    \"request_reason_type\": \"str\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "5292263c-9aaf-4791-b675-d5267e59ef24", "metadata": {}, "outputs": [], "source": ["def convert_columns(df, columnTypeJson=column_types):\n", "    # Iterate over each column in the dataframe\n", "    for col in df.columns:\n", "        if col in columnTypeJson and columnTypeJson[col] == \"str\":\n", "            df[col] = df[col].astype(str)\n", "        elif col in columnTypeJson and columnTypeJson[col] == \"int\":\n", "            df[col] = pd.to_numeric(df[col])\n", "            df[col] = df[col].apply(lambda x: int(x) if not np.isnan(x) else x)\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "cb6d846a-ebb7-4fc5-8210-01f74c825f65", "metadata": {}, "outputs": [], "source": ["def check_required_columns(df, req_columns):\n", "    # Get the current columns of the DataFrame\n", "    current_columns = set(df.columns)\n", "    # Convert required columns to a set\n", "    required = set(req_columns)\n", "    # Check if all required columns are present\n", "    return required.issubset(current_columns)"]}, {"cell_type": "code", "execution_count": null, "id": "927b35b9-e68c-4235-86ef-f21b2d52f265", "metadata": {}, "outputs": [], "source": ["def upload_to_bu(file_path, upload_type):\n", "    try:\n", "        url = \"https://retail-internal.grofer.io/bulk_upload/v1/upload_jobs\"\n", "        payload = {\n", "            \"file\": file_path,\n", "            \"created_by\": \"<PERSON><PERSON>\",\n", "            \"user_id\": 14,\n", "            \"is_auto_po\": True,\n", "            \"upload_type_id\": upload_type,\n", "            \"content_type\": \"text/csv\",\n", "        }\n", "        response = requests.post(url, json=payload)\n", "        return response.status_code, response.json()\n", "    except Exception as e:\n", "        print(\"error in bulk upload api\", e)\n", "        return 404, {}"]}, {"cell_type": "code", "execution_count": null, "id": "4c3ba8c8-4d11-45fb-97c2-e64079cd726c", "metadata": {}, "outputs": [], "source": ["def processFile(input_df, chunk_size=15000):\n", "    print(input_df)\n", "    if input_df.shape[0] == 0:\n", "        print(\"nothing to process, returning\")\n", "        return\n", "    elif input_df.shape[0] < chunk_size:\n", "        list_df = [input_df]\n", "    else:\n", "        print(\"Cannot process this much heavy data\")\n", "        channel = \"table-updates-tanishq\"\n", "        text_req = \"\\n <@U05CCTXLBU1> \\n Cannot process this much heavy data, Item id change assortment replication not done for FnV\"\n", "        pb.send_slack_message(\n", "            channel=channel,\n", "            text=text_req,\n", "        )\n", "\n", "    responses = []\n", "    df_logs = pd.DataFrame()\n", "\n", "    for df in list_df:\n", "        uid = int(time.time())\n", "        local_file_path = f\"product_team_assortment_request_upload_bot_{uid}.xlsx\"\n", "        df.to_excel(local_file_path, index=False, engine=\"openpyxl\")\n", "        file_path = f\"assortment/{local_file_path}\"\n", "        secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "        bucket_name = \"retail-bulk-upload\"\n", "        aws_key = secrets.get(\"aws_key\")\n", "        aws_secret = secrets.get(\"aws_secret\")\n", "        session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "        s3 = session.resource(\"s3\")\n", "        bucket_obj = s3.Bucket(bucket_name)\n", "        bucket_obj.upload_file(local_file_path, file_path)\n", "        status_code, responseJson = upload_to_bu(file_path, 100)\n", "        os.remove(local_file_path)\n", "        df[\"uid\"] = uid\n", "        responses.append(responseJson)\n", "        df[\"response\"] = json.dumps(responseJson)\n", "        df[\"status_code\"] = status_code\n", "        df_logs = pd.concat([df_logs, df])\n", "        time.sleep(2)\n", "    df_logs[\"source\"] = \"povms_assortment_rationalisation_etl_retail_bot_bulk_upload\"\n", "    return responses"]}, {"cell_type": "code", "execution_count": null, "id": "09affdf6-5c2b-4086-9392-8429d732934e", "metadata": {}, "outputs": [], "source": ["input_df"]}, {"cell_type": "code", "execution_count": null, "id": "63e57d25-45e9-410e-80c5-7ab9cac6ff24", "metadata": {}, "outputs": [], "source": ["input_df = convert_columns(input_df)\n", "print(\"final_shape\", input_df.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "8ef45b1d-2fef-4a44-a98a-f5fe4b81c3fb", "metadata": {}, "outputs": [], "source": ["input_df[\"city_name\"] = np.NaN\n", "input_df[\"backend_facility_id\"] = np.NaN\n", "input_df = input_df.fillna(\"\")"]}, {"cell_type": "code", "execution_count": null, "id": "25e51fab-533d-458f-8d7a-d643738544e3", "metadata": {}, "outputs": [], "source": ["input_df"]}, {"cell_type": "code", "execution_count": null, "id": "2b62af20-9a26-45db-8a9f-0b9d70667c0a", "metadata": {}, "outputs": [], "source": ["input_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "9484310a-ffc9-4647-8080-f3eb82033de4", "metadata": {}, "outputs": [], "source": ["input_df = input_df.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "bf314296-223f-487e-a60e-9636da544473", "metadata": {}, "outputs": [], "source": ["input_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "40fd21a8-6529-4f7f-9ec6-fe3732d338ff", "metadata": {}, "outputs": [], "source": ["flag = check_required_columns(input_df, req_columns)\n", "\n", "if not flag or input_df.shape[0] == 0:\n", "    channel = \"table-updates-tanishq\"\n", "    text_req = \"\\n <@U05CCTXLBU1> \\n Invalid columns in item id change assortment replication for FnV, doing nothing or No rows to update.\"\n", "    pb.send_slack_message(\n", "        channel=channel,\n", "        text=text_req,\n", "    )\n", "\n", "else:\n", "    responses = processFile(input_df)\n", "    json_string = json.dumps(responses)\n", "    channel = \"table-updates-tanishq\"\n", "    text_req = \"\\n <@U05CCTXLBU1> \\n Item id change assortment replication completed for FnV\" + str(\n", "        json_string\n", "    )\n", "    pb.send_slack_message(\n", "        channel=channel,\n", "        text=text_req,\n", "    )\n", "\n", "    channel = \"assortment-alerts-plus-discussions\"\n", "    text_req = \"Item id change assortment replication completed for FnV\" + str(json_string)\n", "    pb.send_slack_message(\n", "        channel=channel,\n", "        text=text_req,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "76fb5018-9026-463e-bb0f-b29f646e2269", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}