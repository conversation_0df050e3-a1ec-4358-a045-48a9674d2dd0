alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
catchup: true
dag_name: cc_suggestive_price_2
dag_type: etl
escalation_priority: low
execution_timeout: 120
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: fresh
notebook:
  parameters:
    date_filter: '{{ data_interval_end }}'
owner:
  email: <EMAIL>
  slack_id: U0899MAT7PT
path: fresh/fnv_analytics/etl/cc_suggestive_price_2
paused: False
pool: fresh_pool
project_name: fnv_analytics
schedule:
  end_date: '2025-07-25T00:00:00'
  interval: 00 11 * * *
  start_date: '2025-04-01T00:00:00'
schedule_type: fixed
sla: 125 minutes
support_files: []
tags: []
template_name: notebook
version: 5
