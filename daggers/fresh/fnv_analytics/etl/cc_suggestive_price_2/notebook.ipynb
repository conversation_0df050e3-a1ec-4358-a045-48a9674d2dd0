{"cells": [{"cell_type": "markdown", "id": "aa4c3a1f-2eca-46da-8b1a-da568bb98fd3", "metadata": {}, "source": ["### Import Libraries"]}, {"cell_type": "code", "execution_count": null, "id": "4056f666-4335-4a7a-9a2e-f1bfee275162", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import os\n", "import time\n", "import gc\n", "import requests\n", "\n", "import calendar\n", "from datetime import datetime as dt\n", "\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.options.mode.chained_assignment = None  # default='warn'\n", "pd.set_option(\"display.max_columns\", 500)"]}, {"cell_type": "code", "execution_count": null, "id": "8d004b6d-930f-40de-a482-4f48e6421570", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f3290a9e-9949-43fb-b039-33c7845d9407", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c03f7f24-9716-4672-88a8-1d767888e188", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b0fe2cda-689a-47a8-8bfc-36a2375eefa5", "metadata": {"tags": []}, "outputs": [], "source": ["date_filter_dt = dt.fromisoformat(date_filter)\n", "date_filter_str = date_filter_dt.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "95dbf9e0-be95-474b-b783-7593895da39d", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 1\n", "    for attempt in range(max_tries):\n", "        print(\n", "            f\"Read attempt: {attempt}... \\nRun started at {datetime.now() + timed<PERSON>ta(hours = 5.5)}\"\n", "        )\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "5c03eb87-cae6-4f70-a463-b67dfe0c14e1", "metadata": {}, "outputs": [], "source": ["def extract_details(item_name):\n", "    core_name = re.sub(\n", "        r\"\\s*\\(*\\d+(\\.\\d+)?[-–]?\\d*(\\.\\d+)?\\s*(?:kg|g|ml|pcs?|pieces?|pack|unit|pc|ft)?\\)*\",\n", "        \"\",\n", "        item_name,\n", "        flags=re.IGNORECASE,\n", "    ).strip()\n", "    core_name = re.sub(\n", "        r\"\\s+\\b(?:kg|g|ml|pcs?|pieces?|pack|unit|pc|ft)\\b$\", \"\", core_name, flags=re.IGNORECASE\n", "    )\n", "    core_name = re.sub(r\"[-\\s]+$\", \"\", core_name)  # Remove trailing hyphens and spaces\n", "\n", "    quantity, unit = \"\", \"\"  # Default to empty strings\n", "\n", "    piece_match = re.search(r\"(\\d+)\\s*(?:piece|pieces|pcs|unit|pack|pc)\", item_name, re.IGNORECASE)\n", "    if piece_match:\n", "        quantity, unit = piece_match.group(1), \"pieces\"\n", "\n", "    weight_match = re.search(\n", "        r\"\\(?(\\d+(\\.\\d+)?)(?:[-–](\\d+(\\.\\d+)?))?\\)?\\s*(kg|g|ml|ft)\", item_name, re.IGNORECASE\n", "    )\n", "    if weight_match:\n", "        quantity = (\n", "            weight_match.group(3) if weight_match.group(3) else weight_match.group(1)\n", "        )  # Take upper bound if available\n", "        unit = weight_match.group(5)\n", "\n", "    return pd.Series([core_name, quantity, unit])"]}, {"cell_type": "code", "execution_count": null, "id": "2f0b28b7-ddf9-4581-aa96-c9d6470ae133", "metadata": {}, "outputs": [], "source": ["from functools import reduce\n", "from datetime import datetime, timedelta\n", "import re"]}, {"cell_type": "markdown", "id": "fecd9404-4564-4faa-b438-cf474deafb23", "metadata": {}, "source": ["### CPC to WH Mapping"]}, {"cell_type": "code", "execution_count": null, "id": "5d4d998f-877c-4dc5-b4fe-95d96c797980", "metadata": {}, "outputs": [], "source": ["wh_to_cpc_mapping = {\n", "    \"WH-DEL3\": [\"CPC-DEL3\", \"CPC-AGRA1\", \"CPC-LDH1\", \"CPC-LKO1\", \"CPC-RPJ1\", \"CPC-VARANASI1\"],\n", "    \"WH-NOIDA1\": [\"CPC-NOIDA1\"],\n", "    \"WH-GGN2\": [\"CPC-GGN2\"],\n", "    \"WH-BLR5\": [\"CPC-BLR7\"],\n", "    \"WH-BLR7\": [\"CPC-BLR7\"],\n", "    \"WH-MUM3\": [\"CPC-MUM3\", \"CPC-MUM4\", \"CPC-NAGPUR1\"],\n", "    \"WH-HYD2\": [\"CPC-HYD3\"],\n", "    \"WH-CHN2\": [\"CPC-CHN2\"],\n", "    \"WH-PUNE2\": [\"CPC-PUNE1\", \"CPC-GOA1\"],\n", "    \"WH-AMD1\": [\"CPC-AMD2\", \"CPC-INDORE2\", \"CPC-SURAT2\"],\n", "    \"WH-KOL1\": [\"CPC-KOL1\", \"CPC-KOL2\"],\n", "}\n", "\n", "rows = [(wh, cpc) for wh, cpcs in wh_to_cpc_mapping.items() for cpc in cpcs]\n", "\n", "cpc_wh_mapping = pd.DataFrame(rows, columns=[\"warehouse_code\", \"CPC\"])"]}, {"cell_type": "markdown", "id": "160fb9b5-bddb-4b20-b0f5-dd99e70dca2e", "metadata": {}, "source": ["### Live CPC"]}, {"cell_type": "code", "execution_count": null, "id": "1ec546ab-cac7-4d6b-8e80-2d09f96f53aa", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT * FROM zomato.hp_wms.warehouse\n", "--WHERE warehouse_type = 'BLINKIT' and operation_mode != 'MANDI'\n", "\"\"\"\n", "\n", "warehouse = read_sql_query(query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "58e79301-929b-4109-bc99-d2d1e162fb37", "metadata": {}, "outputs": [], "source": ["live_cpc = (\n", "    warehouse[\n", "        (warehouse[\"warehouse_type\"] == \"BLINKIT\")\n", "        & (warehouse[\"operation_mode\"] != \"MANDI\")\n", "        & (~warehouse[\"warehouse_code\"].str.contains(\"-TEST-\"))\n", "    ][\"warehouse_code\"]\n", "    .unique()\n", "    .tolist()\n", ")"]}, {"cell_type": "markdown", "id": "c163e943-f8b9-4fcc-82da-5ad31d6021b4", "metadata": {}, "source": ["### Vendor to CPC"]}, {"cell_type": "code", "execution_count": null, "id": "a45d3d7c-05a9-4715-ac3e-8c38f5555b04", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "SELECT\n", "    poi.dt as date_,\n", "    po.is_multiple_grn_enabled, \n", "    po.po_status,\n", "    po.vendor_id, \n", "    ipom.outlet_id as warehouse_id, \n", "    po.warehouse_code, \n", "    icd.item_id,\n", "    icd.name as item_name, \n", "    icd.l2 as l2,\n", "    icd.product_type as ptype,\n", "    poi.price_per_unit AS Price_Per_Unit,\n", "    (case when po.is_multiple_grn_enabled = 0 then coalesce(poi.quantity_delivered,0) else coalesce(pg.delivered_quantity,0) end) as quantity_delivered,\n", "    CASE WHEN pg.id>0 AND (pg.delivered_quantity + pg.dock_rejected_quantity /* + pgi.grn_quantity*/) > 0 then 'GRN Completed' \n", "        WHEN is_multiple_grn_enabled = 0 then po.po_status \n", "        ELSE 'GRN Incomplete'  END AS grn_status\n", "\n", "FROM zomato.hp_wms.purchase_order po\n", "INNER JOIN zomato.hp_wms.purchase_order_items poi on po.id = poi.purchase_order_id\n", "INNER JOIN po.edi_integration_partner_item_mapping pi ON CAST(pi.partner_item_id as int) = poi.product_number and pi.active = true\n", "INNER JOIN rpc.item_category_details icd ON icd.item_id = pi.item_id and icd.l0_id = 1487 \n", "LEFT JOIN (Select pg.grn_quantity, purchase_order_item_id, invoiced_quantity, id, delivered_quantity,  dock_rejected_quantity, (pg.updated_at+interval'330'minute) as grn_updated_at,\n", "            (pg.created_at + interval '330' minute) as grn_created_at \n", "            from  zomato.hp_wms.po_grn_item pg\n", "            where dt >= replace(CAST(date('{date_filter_str}') - interval '4' day as varchar), '-','')\n", "            AND dt <= replace(CAST(date('{date_filter_str}') as varchar), '-','')\n", "            group by 1,2,3,4,5,6,7,8) pg on poi.id = pg.purchase_order_item_id\n", "INNER JOIN po.edi_integration_partner_outlet_mapping ipom ON lower(ipom.partner_outlet_id) = lower(po.warehouse_code) AND ipom.active\n", "WHERE po.dt >= replace(CAST(date('{date_filter_str}') - interval '4' day as varchar), '-','')\n", "AND po.dt <= replace(CAST(date('{date_filter_str}') as varchar), '-','')\n", "AND poi.dt >= replace(CAST(date('{date_filter_str}') - interval '4' day as varchar), '-','')\n", "AND poi.dt <= replace(CAST(date('{date_filter_str}') as varchar), '-','')\n", "and po.warehouse_code in {tuple(live_cpc)}\n", "AND po.po_status in ('COMPLETED','APPROVED')\n", "AND pg.id>0 \n", "AND (pg.delivered_quantity + pg.dock_rejected_quantity) > 0\n", "ORDER BY 1 DESC\n", "\"\"\"\n", "\n", "vendor_to_cpc = read_sql_query(query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "174be54e-7cb0-4e4d-8125-1476642b727f", "metadata": {}, "outputs": [], "source": ["vendor_to_cpc = vendor_to_cpc[vendor_to_cpc[\"quantity_delivered\"] > 0]"]}, {"cell_type": "code", "execution_count": null, "id": "5313ab33-8299-4292-9c7f-6332f2f82460", "metadata": {}, "outputs": [], "source": ["vendor_to_cpc[\"date_\"] = pd.to_datetime(vendor_to_cpc[\"date_\"], format=\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "62d1634e-6708-4232-856f-74b1df0f94ac", "metadata": {}, "outputs": [], "source": ["# vendor_to_cpc = vendor_to_cpc[vendor_to_cpc[\"date_\"] != today]"]}, {"cell_type": "code", "execution_count": null, "id": "01a87478-c73b-4261-850b-a76af4d61eea", "metadata": {}, "outputs": [], "source": ["group_cols = [\"warehouse_code\", \"item_id\", \"item_name\"]\n", "unique_combinations = vendor_to_cpc[group_cols].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "e6db2b4b-3550-4e7f-af66-101221fb2b59", "metadata": {}, "outputs": [], "source": ["group_cols = [\"warehouse_code\", \"item_id\", \"item_name\"]\n", "unique_combinations = vendor_to_cpc[group_cols].drop_duplicates()\n", "today = vendor_to_cpc[\"date_\"].max()\n", "results = []\n", "\n", "for _, row in unique_combinations.iterrows():\n", "    mask = (\n", "        (vendor_to_cpc[\"warehouse_code\"] == row[\"warehouse_code\"])\n", "        & (vendor_to_cpc[\"item_id\"] == row[\"item_id\"])\n", "        & (vendor_to_cpc[\"item_name\"] == row[\"item_name\"])\n", "    )\n", "\n", "    group_data = vendor_to_cpc[mask]\n", "\n", "    for window in range(0, 4):\n", "        window_start = today - pd.Timedelta(days=window)\n", "        recent_data = group_data[\n", "            (group_data[\"date_\"] >= window_start) & (group_data[\"date_\"] <= today)\n", "        ]\n", "\n", "        if not recent_data.empty:\n", "            latest_date = recent_data[\"date_\"].max()\n", "            latest_day_data = recent_data[recent_data[\"date_\"] == latest_date]\n", "\n", "            total_qty = latest_day_data[\"quantity_delivered\"].sum()\n", "            if total_qty > 0:\n", "                weighted_price = (\n", "                    latest_day_data[\"Price_Per_Unit\"] * latest_day_data[\"quantity_delivered\"]\n", "                ).sum() / total_qty\n", "            else:\n", "                weighted_price = latest_day_data[\n", "                    \"Price_Per_Unit\"\n", "                ].mean()  # fallback if all quantities are 0\n", "\n", "            results.append(\n", "                {\n", "                    \"warehouse_code\": row[\"warehouse_code\"],\n", "                    \"item_id\": row[\"item_id\"],\n", "                    \"item_name\": row[\"item_name\"],\n", "                    \"Price_Per_Unit\": weighted_price,\n", "                    \"quantity_delivered\": total_qty,\n", "                }\n", "            )\n", "            break\n", "\n", "vendor_to_cpc_grp_1 = pd.DataFrame(results)"]}, {"cell_type": "markdown", "id": "68a1c950-e275-4e87-a69e-0805fa1fae8a", "metadata": {"tags": []}, "source": ["### STO"]}, {"cell_type": "markdown", "id": "39810975-adb3-4df0-8f5b-cf59409cb75f", "metadata": {}, "source": ["#### cc to cpc independent of finance cost"]}, {"cell_type": "code", "execution_count": null, "id": "d556829c-cbd1-406c-b776-bfb858e5762a", "metadata": {}, "outputs": [], "source": ["cc_names = warehouse[\n", "    (warehouse[\"warehouse_type\"] == \"COLLECTION_CENTER\") & (warehouse[\"operation_mode\"] != \"MANDI\")\n", "][\"warehouse_code\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "f30cbb45-0505-4021-a29a-1d73e305327c", "metadata": {"tags": []}, "outputs": [], "source": ["query = f\"\"\"\n", "with base as (\n", "select \n", "date(sth.updated_at+interval'330'minute) as Date,\n", "sto.id as orderid,\n", "sto.order_number as sto_order_number,\n", "sti.product_number as ProductNumber,\n", "icd.name as ProductName,\n", "icd.product_type as ptype,\n", "pi.item_id as bl_item_id,\n", "icd.name as item_name,\n", "icd.l2_id as l2_id,\n", "sto.source_warehouse_code as sourcewh,\n", "sto.destination_warehouse_code as ToWarehouseCode,\n", "sti.stock_transferred_quantity as STOQuantity,\n", "sti.warehouse_usable_quantity as TransferedInventory,\n", "sti.warehouse_usable_value as Transferred_value, \n", "p.weight_per_packet,\n", "sti.warehouse_variable_price, \n", "sti.warehouse_variable_price_uom, \n", "sti.warehouse_fixed_price, \n", "sti.warehouse_fixed_price_uom\n", "\n", "\n", "FROM zomato.hp_wms.stock_transfer_order_item sti\n", "\n", "JOIN zomato.hp_wms.stock_transfer_order sto on sto.id = sti.order_id and sto.status = 'RECEIVED'\n", "\n", "JOIN po.edi_integration_partner_item_mapping pi ON CAST(pi.partner_item_id as int) = sti.product_number and pi.active = true\n", "\n", "JOIN rpc.item_category_details icd ON icd.item_id = pi.item_id and icd.l0_id = 1487\n", "\n", "JOIN zomato.hp_wms.stock_transfer_order_status_history sth on sth.order_id = sto.id and new_sto_status='RECEIVED'\n", "\n", "JOIN zomato.hp_wms.product p on sti.product_number = p.product_number\n", "\n", "WHERE 1=1 and date(sth.updated_at+interval'330'minute) >= date('{date_filter_str}') - interval '8' day\n", "AND date(sth.updated_at+interval'330'minute) <= date('{date_filter_str}')\n", "and sti.dt>'20180101'\n", "and sto.dt>'20180101'\n", "and sth.dt>'20180101'\n", "--and sto.source_warehouse_code not like ('%%WH%%')\n", "--AND sto.source_warehouse_code like ('%%CC%%')\n", "AND sto.source_warehouse_code in {tuple(cc_names.tolist())}\n", "--AND cast(sti.warehouse_variable_price as int)> 0\n", "--AND cast(sti.warehouse_fixed_price as int)> 0\n", ")\n", "\n", "select \n", "date(Date) as date_, \n", "sourcewh as source_cc,\n", "ToWarehouseCode as warehouse_code,\n", "bl_item_id as item_id,\n", "ProductName as item_name, \n", "ptype,\n", "l2_id,\n", "'CC' as tag, \n", "sum(TransferedInventory) as GRN, \n", "sum(Transferred_value) as COGS,\n", "max(weight_per_packet) weight_per_packet,\n", "avg(warehouse_fixed_price)*max(weight_per_packet) as fixed_overhead,\n", "avg(warehouse_fixed_price) as warehouse_fixed_price,\n", "avg(warehouse_variable_price)*max(weight_per_packet) as variable_overhead,\n", "avg(warehouse_variable_price) as warehouse_variable_price,\n", "(avg(warehouse_fixed_price) + avg(warehouse_variable_price))*max(weight_per_packet) as total_overheads,\n", "sum(Transferred_value)/sum(TransferedInventory) as price_per_unit_without_overhead\n", "from base\n", "group by 1,2,3,4,5,6,7\n", "\"\"\"\n", "\n", "\n", "cc_to_cpc_sto_wo_overheads = read_sql_query(query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "4b2a0a02-b526-45dd-b364-4719df893562", "metadata": {}, "outputs": [], "source": ["cc_to_cpc_sto_wo_overheads[\"fixed_overhead\"] = cc_to_cpc_sto_wo_overheads[\"fixed_overhead\"].astype(\n", "    \"double\"\n", ")\n", "cc_to_cpc_sto_wo_overheads[\"variable_overhead\"] = cc_to_cpc_sto_wo_overheads[\n", "    \"variable_overhead\"\n", "].astype(\"double\")\n", "cc_to_cpc_sto_wo_overheads[\"warehouse_fixed_price\"] = cc_to_cpc_sto_wo_overheads[\n", "    \"warehouse_fixed_price\"\n", "].astype(\"double\")\n", "cc_to_cpc_sto_wo_overheads[\"warehouse_variable_price\"] = cc_to_cpc_sto_wo_overheads[\n", "    \"warehouse_variable_price\"\n", "].astype(\"double\")\n", "cc_to_cpc_sto_wo_overheads[\"price_per_unit_without_overhead\"] = cc_to_cpc_sto_wo_overheads[\n", "    \"price_per_unit_without_overhead\"\n", "].astype(\"double\")\n", "\n", "\n", "cc_to_cpc_sto_wo_overheads[\"date_\"] = pd.to_datetime(cc_to_cpc_sto_wo_overheads[\"date_\"])\n", "\n", "group_cols = [\n", "    \"source_cc\",\n", "    \"warehouse_code\",\n", "    \"item_id\",\n", "    \"item_name\",\n", "    \"weight_per_packet\",\n", "    \"ptype\",\n", "    \"l2_id\",\n", "]\n", "\n", "unique_keys = cc_to_cpc_sto_wo_overheads[group_cols].drop_duplicates()\n", "today = cc_to_cpc_sto_wo_overheads[\"date_\"].max()\n", "result_rows = []\n", "\n", "for _, row in unique_keys.iterrows():\n", "    mask = np.logical_and.reduce(\n", "        [cc_to_cpc_sto_wo_overheads[col] == row[col] for col in group_cols]\n", "    )\n", "    group_data = cc_to_cpc_sto_wo_overheads[mask]\n", "\n", "    recent_data = pd.DataFrame()\n", "    for window in range(0, 8):\n", "        window_start = today - pd.Timedelta(days=window)\n", "        window_data = group_data[\n", "            (group_data[\"date_\"] >= window_start) & (group_data[\"date_\"] <= today)\n", "        ]\n", "        if not window_data.empty:\n", "            recent_data = window_data\n", "            break\n", "\n", "    if recent_data.empty:\n", "        continue\n", "\n", "    latest_date = recent_data[\"date_\"].max()\n", "    latest_day_data = recent_data[recent_data[\"date_\"] == latest_date]\n", "\n", "    total_grn = latest_day_data[\"GRN\"].sum()\n", "\n", "    if total_grn > 0:\n", "        fixed_overhead_per_item = (\n", "            latest_day_data[\"fixed_overhead\"] * latest_day_data[\"GRN\"]\n", "        ).sum() / total_grn\n", "        variable_overhead_per_item = (\n", "            latest_day_data[\"variable_overhead\"] * latest_day_data[\"GRN\"]\n", "        ).sum() / total_grn\n", "        warehouse_fixed_price = (\n", "            latest_day_data[\"warehouse_fixed_price\"] * latest_day_data[\"GRN\"]\n", "        ).sum() / total_grn\n", "        warehouse_variable_price = (\n", "            latest_day_data[\"warehouse_variable_price\"] * latest_day_data[\"GRN\"]\n", "        ).sum() / total_grn\n", "        cc_price_per_item = latest_day_data[\"COGS\"].sum() / total_grn\n", "    else:\n", "        fixed_overhead_per_item = np.nan\n", "        variable_overhead_per_item = np.nan\n", "        warehouse_fixed_price = np.nan\n", "        warehouse_variable_price = np.nan\n", "        cc_price_per_item = np.nan\n", "\n", "    result_rows.append(\n", "        {\n", "            **row.to_dict(),\n", "            \"fixed_overhead_per_item\": fixed_overhead_per_item,\n", "            \"variable_overhead_per_item\": variable_overhead_per_item,\n", "            \"warehouse_fixed_price\": warehouse_fixed_price,\n", "            \"warehouse_variable_price\": warehouse_variable_price,\n", "            \"cc_price_per_item\": cc_price_per_item,\n", "            \"grn\": total_grn,\n", "            \"latest_date_used\": latest_date,\n", "        }\n", "    )\n", "\n", "result_cc_to_cpc_sto_wo_overheads = pd.DataFrame(result_rows)\n", "\n", "\n", "result_cc_to_cpc_sto_wo_overheads[\"fixed_overhead_per_item\"] = result_cc_to_cpc_sto_wo_overheads[\n", "    \"fixed_overhead_per_item\"\n", "].round(2)\n", "result_cc_to_cpc_sto_wo_overheads[\"variable_overhead_per_item\"] = result_cc_to_cpc_sto_wo_overheads[\n", "    \"variable_overhead_per_item\"\n", "].round(2)\n", "result_cc_to_cpc_sto_wo_overheads[\"grn\"] = result_cc_to_cpc_sto_wo_overheads[\"grn\"].round(2)\n", "result_cc_to_cpc_sto_wo_overheads[\"cc_price_per_item\"] = result_cc_to_cpc_sto_wo_overheads[\n", "    \"cc_price_per_item\"\n", "].round(2)"]}, {"cell_type": "code", "execution_count": null, "id": "50c5d3b9-8676-430e-894e-f50793b3c926", "metadata": {}, "outputs": [], "source": ["df = result_cc_to_cpc_sto_wo_overheads.copy()\n", "\n", "grn_positive_df = df[df[\"grn\"] > 0]\n", "\n", "priority_groups = [\n", "    [\"source_cc\", \"warehouse_code\", \"ptype\"],\n", "    [\"source_cc\", \"warehouse_code\", \"l2_id\"],\n", "    [\"source_cc\", \"ptype\"],\n", "    [\"warehouse_code\", \"ptype\"],\n", "    [\"source_cc\", \"warehouse_code\"],\n", "    [\"source_cc\", \"l2_id\"],\n", "    [\"warehouse_code\", \"l2_id\"],\n", "    [\"ptype\"],\n", "    [\"l2_id\"],\n", "]\n", "\n", "for group_cols in priority_groups:\n", "    weighted_avg = (\n", "        grn_positive_df.groupby(group_cols)\n", "        .apply(\n", "            lambda g: pd.Series(\n", "                {\n", "                    \"weighted_variable_overhead\": np.average(\n", "                        g[\"warehouse_variable_price\"], weights=g[\"grn\"]\n", "                    ),\n", "                    \"weighted_fixed_overhead\": np.average(\n", "                        g[\"warehouse_fixed_price\"], weights=g[\"grn\"]\n", "                    ),\n", "                }\n", "            )\n", "        )\n", "        .reset_index()\n", "    )\n", "\n", "    df = df.merge(weighted_avg, on=group_cols, how=\"left\")\n", "\n", "    mask = (df[\"variable_overhead_per_item\"] == 0) & df[\"weighted_variable_overhead\"].notna()\n", "    df.loc[mask, \"variable_overhead_per_item\"] = (\n", "        df.loc[mask, \"weighted_variable_overhead\"] * df.loc[mask, \"weight_per_packet\"]\n", "    )\n", "\n", "    mask = (df[\"fixed_overhead_per_item\"] == 0) & df[\"weighted_fixed_overhead\"].notna()\n", "    df.loc[mask, \"fixed_overhead_per_item\"] = (\n", "        df.loc[mask, \"weighted_fixed_overhead\"] * df.loc[mask, \"weight_per_packet\"]\n", "    )\n", "\n", "    df.drop([\"weighted_variable_overhead\", \"weighted_fixed_overhead\"], axis=1, inplace=True)\n", "\n", "result_cc_to_cpc_sto_wo_overheads_1 = df"]}, {"cell_type": "markdown", "id": "f0a26b13-aa7c-41af-85ee-731fe1c86e71", "metadata": {}, "source": ["#### Outlier Removal"]}, {"cell_type": "code", "execution_count": null, "id": "210c0e14-744c-4a43-94b4-451dd7f6e8b1", "metadata": {}, "outputs": [], "source": ["data_zero_var_price = result_cc_to_cpc_sto_wo_overheads_1[\n", "    result_cc_to_cpc_sto_wo_overheads_1[\"warehouse_variable_price\"] == 0\n", "]\n", "Q1 = data_zero_var_price[\"variable_overhead_per_item\"].quantile(0.01)\n", "Q3 = data_zero_var_price[\"variable_overhead_per_item\"].quantile(0.98)\n", "IQR = Q3 - Q1\n", "threshold = Q3 + 1.5 * IQR\n", "outliers_right_tail = data_zero_var_price[\n", "    data_zero_var_price[\"variable_overhead_per_item\"] > threshold\n", "]\n", "result_cc_to_cpc_sto_wo_overheads_1.loc[outliers_right_tail.index, \"variable_overhead_per_item\"] = 0"]}, {"cell_type": "code", "execution_count": null, "id": "f495047f-c569-4c6e-85e8-f78a4ec7b81a", "metadata": {}, "outputs": [], "source": ["data_zero_var_price[\"fixed_overhead_per_item\"].max()"]}, {"cell_type": "code", "execution_count": null, "id": "73f5e603-6f49-476e-aaea-5a775f09d59d", "metadata": {}, "outputs": [], "source": ["data_zero_var_price = result_cc_to_cpc_sto_wo_overheads_1[\n", "    result_cc_to_cpc_sto_wo_overheads_1[\"warehouse_variable_price\"] == 0\n", "]\n", "Q1 = data_zero_var_price[\"fixed_overhead_per_item\"].quantile(0.01)\n", "Q3 = data_zero_var_price[\"fixed_overhead_per_item\"].quantile(0.98)\n", "IQR = Q3 - Q1\n", "threshold = Q3 + 1.5 * IQR\n", "outliers_right_tail = data_zero_var_price[\n", "    data_zero_var_price[\"fixed_overhead_per_item\"] > threshold\n", "]\n", "result_cc_to_cpc_sto_wo_overheads_1.loc[outliers_right_tail.index, \"fixed_overhead_per_item\"] = 0"]}, {"cell_type": "code", "execution_count": null, "id": "7ee3a85e-3b5d-4bc5-af16-671d43927804", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "\n", "def create_distribution_table(df, col_name):\n", "    \"\"\"Helper function to create distribution series for a column\"\"\"\n", "    return (\n", "        pd.cut(df[col_name].fillna(0), bins=range(-1, int(df[col_name].max()) + 2))\n", "        .value_counts()\n", "        .sort_index()\n", "    )\n", "\n", "\n", "distributions = {\n", "    \"DF1_variable\": create_distribution_table(\n", "        result_cc_to_cpc_sto_wo_overheads, \"variable_overhead_per_item\"\n", "    ),\n", "    \"DF1_fixed\": create_distribution_table(\n", "        result_cc_to_cpc_sto_wo_overheads, \"fixed_overhead_per_item\"\n", "    ),\n", "    \"DF2_variable\": create_distribution_table(\n", "        result_cc_to_cpc_sto_wo_overheads_1, \"variable_overhead_per_item\"\n", "    ),\n", "    \"DF2_fixed\": create_distribution_table(\n", "        result_cc_to_cpc_sto_wo_overheads_1, \"fixed_overhead_per_item\"\n", "    ),\n", "}\n", "\n", "comparison_df = pd.concat(distributions, axis=1)\n", "\n", "comparison_df.columns = [\n", "    \"og_variable_overhead\",\n", "    \"og_fixed_overhead\",\n", "    \"comp_variable_overhead\",\n", "    \"comp_fixed_overhead\",\n", "]\n", "\n", "# comparison_df[['og_variable_overhead', 'comp_variable_overhead', 'og_fixed_overhead', 'comp_fixed_overhead']]"]}, {"cell_type": "markdown", "id": "1c14d653-9f20-466b-b321-ea6c0e4a639e", "metadata": {}, "source": ["### Trans"]}, {"cell_type": "code", "execution_count": null, "id": "ade22f3a-fdd2-494f-ad70-dbe2acf8b40a", "metadata": {}, "outputs": [], "source": ["# from zomato.hp_wms.bin_inventory_logs lg  -batch number"]}, {"cell_type": "code", "execution_count": null, "id": "ef769285-fc67-467a-8d53-8c48cd292d33", "metadata": {}, "outputs": [], "source": ["# query = '''\n", "#  with final as (select dt,\n", "#  Case when extract(hour from lg.created_at) between 11 and 23 then lg.created_at + interval '1' day else lg.created_at end as final_Date,\n", "#  warehouse_code,\n", "#  lg.product_number as destination_product_name,\n", "#  icd.name as product_name,\n", "#  in_price,\n", "#  cast(SUBSTRING(source_entity_id, 1, POSITION('-' IN source_entity_id) - 1) as int) AS source_product_number,\n", "#  sum(quantity) as qty,\n", "#  sum(quantity)*in_price as COGS\n", "#  from zomato.hp_wms.bin_inventory_logs lg\n", "#  left join zomato.hp_wms.product p on p.product_number = lg.product_number\n", "#  left JOIN po.edi_integration_partner_item_mapping pi ON CAST(pi.partner_item_id as int) = lg.product_number and pi.active = true\n", "#  INNER JOIN rpc.item_category_details icd ON icd.item_id = pi.item_id and icd.l0_id = 1487\n", "#  left join zomato.hp_wms.category pc on p.parent_category_id = pc.id\n", "#  left join zomato.hp_wms.category c on p.category_id = c.id\n", "#  where lg.dt >= replace(CAST(date('{date_filter_str}') - interval '17' day as varchar), '-','') and reason_code in ('SKU_TRANSFER_ADD')\n", "#  group by 1,2,3,4,5,6,7)\n", "\n", "#  select date(final_Date) as dt,\n", "#  warehouse_code,\n", "#  pi.item_id as item_id,\n", "#  product_name as item_name,\n", "#  'CC' as tag,\n", "#  sum(qty) as QTY,\n", "#  sum(COGS) as COGS\n", "#  from final\n", "#  left join zomato.hp_wms.product p on p.product_number = final.source_product_number\n", "#  left join zomato.hp_wms.category pc on p.parent_category_id = pc.id\n", "#  left join zomato.hp_wms.category c on p.category_id = c.id\n", "#  left join po.edi_integration_partner_item_mapping pi ON CAST(pi.partner_item_id as int) = final.destination_product_name and pi.active = true\n", "#  where pc.name not in ('GT Vegetables & Fruits')\n", "#  and warehouse_code like ('%%CPC%%')\n", "#  group by 1,2,3,4,5 order by 1 desc\n", "# '''\n", "\n", "# cc_to_cpc_trans = read_sql_query(query, trino)"]}, {"cell_type": "markdown", "id": "aa31e459-031c-4278-bd1f-56589056ae2d", "metadata": {}, "source": ["### Blinkit Items multiplier"]}, {"cell_type": "code", "execution_count": null, "id": "187f6e01-3a62-417f-b67e-bb6ff6bc570d", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select icd.item_id, icd.name as item_name, weight_per_packet\n", "FROM rpc.item_category_details icd\n", "JOIN lake_po.edi_integration_partner_item_mapping pi on pi.item_id = icd.item_id\n", "JOIN zomato.hp_wms.product p on p.product_number = cast(pi.partner_item_id as int)\n", "\n", "WHERE l0_id = 1487 AND icd.lake_active_record\n", "GROUP BY 1,2,3\n", "\"\"\"\n", "\n", "item_name = read_sql_query(query, trino)\n", "\n", "item_name[[\"core_name\", \"quantity\", \"unit\"]] = item_name[\"item_name\"].apply(extract_details)\n", "\n", "item_name = item_name[item_name[\"quantity\"] != \"\"]\n", "item_name[\"quantity\"] = item_name[\"quantity\"].astype(\"double\")\n", "\n", "item_name[\"unit\"].unique()\n", "\n", "item_name[\"unit\"] = item_name[\"unit\"].replace([\"Kg\", \"kg\", \"KG\"], \"KG\")\n", "item_name[\"unit\"] = item_name[\"unit\"].replace([\"G\"], \"g\")\n", "item_name[\"multiplier_blinkit\"] = item_name[\"unit\"].apply(lambda x: 1000 if x == \"KG\" else 1)\n", "\n", "item_name_1 = item_name[item_name[\"unit\"].isin([\"g\", \"KG\"])]\n", "\n", "item_name_1[\"final_multiplier_blinkit\"] = (\n", "    item_name_1[\"quantity\"] * item_name_1[\"multiplier_blinkit\"]\n", ")"]}, {"cell_type": "markdown", "id": "c28377c5-f686-4764-93d1-815b8daa5b4a", "metadata": {}, "source": ["### Mandi price"]}, {"cell_type": "code", "execution_count": null, "id": "2da6b75f-95b4-463c-9782-44b5a7e23a95", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "with new as (select distinct(i.id), \n", "(i.created_at + interval '330' minute) as date_,  \n", "v.warehouse_code, \n", "w.external_platform_id as Mandi_id,\n", "ex.name as <PERSON><PERSON>_name, \n", "i.product_number as mandi_product_number, \n", "ep.hp_product_number as hp_product_number, \n", "icd.item_id as item_id, \n", "icd.name item_name ,\n", "p.name as product_name, \n", "p.weight_per_packet,\n", "i.vendor_id,\n", "i.price as mandi_price, i.created_by as created_by_id, u.name as price_entered_by, i.latitude, i.longitude, i.note as comments\n", "\n", "from zomato.hp_wms.external_price_input i\n", "left join zomato.hp_users.users u on u.id = i.created_by\n", "left join zomato.hp_wms.external_vendor_warehouse_mapping v on v.vendor_id = i.vendor_id\n", "left join zomato.hp_wms.platform_vendor_mapping w on w.vendor_id = i.vendor_id\n", "left join zomato.hp_wms.external_platform ex on ex.id = w.external_platform_id \n", "left join zomato.hp_wms.external_product_mapping ep on ep.external_product_number = i.product_number\n", "LEFT JOIN zomato.hp_wms.product p on p.product_number = ep.hp_product_number\n", "\n", "left join lake_po.edi_integration_partner_item_mapping pi ON cast(pi.partner_item_id as int) = ep.hp_product_number\n", "\n", "\n", "INNER JOIN rpc.item_category_details icd ON icd.item_id = pi.item_id and icd.l0_id = 1487 \n", "\n", "\n", "where i.price_type = ('AARHAT')\n", "and date_format(i.created_at + interval '330' minute,'%%Y/%%m/%%d') >= date_format(date('{date_filter_str}') - interval '7' day,'%%Y/%%m/%%d')\n", "and date_format(i.created_at + interval '330' minute,'%%Y/%%m/%%d') <= date_format(date('{date_filter_str}'),'%%Y/%%m/%%d')\n", "and v.warehouse_code like ('%%CPC%%')\n", ")\n", "\n", "select date_format(date_,'%%Y/%%m/%%d') as date_,\n", "warehouse_code,\n", "Mandi_name,\n", "mandi_product_number, \n", "new.hp_product_number, \n", "item_id, \n", "item_name,\n", "product_name,\n", "weight_per_packet,\n", "avg(mandi_price) as mandi_price,\n", "avg(weight_per_packet*mandi_price) as weighted_mandi_price\n", "from new \n", "group by 1,2,3,4,5,6,7,8,9\n", "\"\"\"\n", "\n", "mandi_lp_1 = read_sql_query(query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "99c7ba9e-c928-4465-9b5a-47c24977b649", "metadata": {}, "outputs": [], "source": ["mandi_lp = mandi_lp_1"]}, {"cell_type": "code", "execution_count": null, "id": "bf5a9634-8679-4fbe-b413-41d86d1f77bd", "metadata": {}, "outputs": [], "source": ["mandi_lp[\"date_\"] = pd.to_datetime(mandi_lp[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "cff27594-967e-416e-8357-e259c9ae4e27", "metadata": {}, "outputs": [], "source": ["today = mandi_lp[\"date_\"].max()"]}, {"cell_type": "code", "execution_count": null, "id": "c9e69760-c867-44ad-9b57-21f0b4286ab5", "metadata": {}, "outputs": [], "source": ["# mandi_lp = mandi_lp[mandi_lp[\"date_\"] != today]"]}, {"cell_type": "code", "execution_count": null, "id": "a28f0c6b-cf82-4a6a-9157-ec7402ce765f", "metadata": {}, "outputs": [], "source": ["# max_date = mandi_lp[\"date_\"].max()"]}, {"cell_type": "code", "execution_count": null, "id": "9852cd60-d867-4523-bd85-00a3e81cef99", "metadata": {}, "outputs": [], "source": ["group_cols = [\"warehouse_code\", \"item_id\", \"Mandi_name\"]\n", "unique_combinations = mandi_lp[group_cols].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "49097ad6-cb74-41a3-9b94-4d025f3ecf00", "metadata": {}, "outputs": [], "source": ["results = []\n", "\n", "for _, row in unique_combinations.iterrows():\n", "    mask = (\n", "        (mandi_lp[\"warehouse_code\"] == row[\"warehouse_code\"])\n", "        & (mandi_lp[\"item_id\"] == row[\"item_id\"])\n", "        & (mandi_lp[\"Mandi_name\"] == row[\"Mandi_name\"])\n", "    )\n", "\n", "    group_data = mandi_lp[mask]\n", "\n", "    for window in range(0, 8):\n", "        window_start = today - pd.Timedelta(days=window)\n", "        recent_data = group_data[\n", "            (group_data[\"date_\"] >= window_start) & (group_data[\"date_\"] <= today)\n", "        ]\n", "\n", "        if not recent_data.empty:\n", "            latest_data = recent_data.sort_values(\"date_\", ascending=False).iloc[0]\n", "            latest_price = latest_data[\"weighted_mandi_price\"]\n", "\n", "            results.append(\n", "                {\n", "                    \"warehouse_code\": row[\"warehouse_code\"],\n", "                    \"item_id\": row[\"item_id\"],\n", "                    \"Mandi_name\": row[\"Mandi_name\"],\n", "                    \"days_used\": window,\n", "                    \"mandi_price_per_item\": latest_price,  # Using latest price instead of average\n", "                }\n", "            )\n", "            break\n", "\n", "mandi_lp_join = pd.DataFrame(results)"]}, {"cell_type": "code", "execution_count": null, "id": "c686ca56-834e-44ad-a7ee-59ad8fea93f1", "metadata": {}, "outputs": [], "source": ["# data = [\n", "#     (\"CPC-AGRA1\", \"CPC-DEL3\"),\n", "#     (\"CPC-NAGPUR1\", \"CPC-MUM3\"),\n", "#     (\"CPC-RPJ1\", \"CPC-DEL3\"),\n", "#     (\"CPC-KOL2\", \"CPC-KOL1\"),\n", "#     (\"CPC-MUM4\", \"CPC-MUM3\"),\n", "#     (\"CPC-LDH1\", \"CPC-DEL3\"),\n", "#     (\"CPC-INDORE2\", \"CPC-AMD2\"),\n", "#     (\"CPC-GOA1\", \"CPC-PUNE1\"),\n", "#     (\"CPC-VARANASI1\", \"CPC-LKO1\"),\n", "# ]\n", "\n", "# missing_mandi_mapping = pd.DataFrame(data, columns=[\"baby_warehouse_code\", \"warehouse_code\"])\n", "\n", "# inner_merged = pd.merge(mandi_lp_join, missing_mandi_mapping, on=\"warehouse_code\", how=\"inner\")\n", "\n", "# inner_merged.drop(columns=\"warehouse_code\", inplace=True)\n", "# inner_merged.rename(columns={\"baby_warehouse_code\": \"warehouse_code\"}, inplace=True)\n", "\n", "# mandi_lp_join_1 = pd.concat([mandi_lp_join, inner_merged], ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "10769f97-ca4e-48df-b1dd-3845eee2185a", "metadata": {}, "outputs": [], "source": ["mandi_lp_join_1 = mandi_lp_join.copy()"]}, {"cell_type": "markdown", "id": "241cffb8-272b-4bc0-a36b-916c9c7b7f32", "metadata": {}, "source": ["### Hyper Pure B2B"]}, {"cell_type": "code", "execution_count": null, "id": "697fa9c8-d4e5-435f-a3f8-bc3f97a08d8b", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "SELECT\n", "    date(grn_created_at) as final_Date, \n", "    po.warehouse_code, \n", "    poi.product_number,\n", "    p.name as product_name,\n", "    poi.price_per_unit AS Price_Per_Unit,\n", "    p.weight_per_packet,\n", "    (case when po.is_multiple_grn_enabled = 0 then coalesce(poi.quantity_delivered,0) else coalesce(pg.delivered_quantity,0) end) as quantity_delivered\n", "FROM zomato.hp_wms.purchase_order po\n", "INNER JOIN zomato.hp_wms.purchase_order_items poi on po.id = poi.purchase_order_id\n", "left join zomato.hp_wms.product p on poi.product_number = p.product_number\n", "left join zomato.hp_wms.category pc on p.parent_category_id = pc.id\n", "left join zomato.hp_wms.category c on p.category_id = c.id\n", "left join lake_po.edi_integration_partner_item_mapping pi ON cast(pi.partner_item_id as int) = poi.product_number\n", "\n", "LEFT JOIN (Select pg.grn_quantity, purchase_order_item_id, invoiced_quantity, id, delivered_quantity,  dock_rejected_quantity, (pg.updated_at+interval'330'minute) as grn_updated_at,\n", "            (pg.created_at + interval '330' minute) as grn_created_at \n", "            from  zomato.hp_wms.po_grn_item pg\n", "            where dt >= replace(CAST(date('{date_filter_str}') - interval '4' day as varchar), '-','')\n", "            AND dt <= replace(CAST(date('{date_filter_str}') as varchar), '-','')\n", "            GROUP BY 1,2,3,4,5,6,7,8) pg on poi.id = pg.purchase_order_item_id\n", "LEFT JOIN po.edi_integration_partner_outlet_mapping ipom ON lower(ipom.partner_outlet_id) = lower(po.warehouse_code) AND ipom.active\n", "WHERE po.dt >= replace(CAST(date('{date_filter_str}') - interval '4' day as varchar), '-','')\n", "AND po.dt <= replace(CAST(date('{date_filter_str}') as varchar), '-','')\n", "AND poi.dt >= replace(CAST(date('{date_filter_str}') - interval '4' day as varchar), '-','')\n", "AND poi.dt <= replace(CAST(date('{date_filter_str}') as varchar), '-','')\n", "and po.warehouse_code like ('%%WH%%')\n", "AND po.po_status in ('COMPLETED','APPROVED')\n", "AND pg.id>0 \n", "AND (pg.delivered_quantity + pg.dock_rejected_quantity) > 0\n", "and pc.name = 'Fruits & Vegetables'\n", "AND po.order_tag != 'DIRECT_DELIVERY'\n", "ORDER BY 1 DESC\n", "\"\"\"\n", "\n", "hyp_b2b = read_sql_query(query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "58604616-4855-4c98-9123-8f94c2cb3b00", "metadata": {}, "outputs": [], "source": ["# hyp_b2b = hyp_b2b[hyp_b2b[\"final_Date\"] != today]"]}, {"cell_type": "code", "execution_count": null, "id": "172ea871-8595-4342-a441-40c32e60d2ed", "metadata": {}, "outputs": [], "source": ["hyp_b2b[\"core_name_hyp\"] = hyp_b2b[\"product_name\"].str.split(\",\").str[0].str.strip()"]}, {"cell_type": "code", "execution_count": null, "id": "c32a2752-ba4f-4190-8b4c-d0222a5048a1", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1aRW5Qvvr55sHVRLWeTJqp9aAihntKR5jeSY2r8RGNGQ\"\n", "sheet_name = \"mapping_final\"\n", "hyp_b2b_clean_2 = pb.from_sheets(sheet_id, sheet_name)\n", "\n", "# hyp_b2b_clean_2 = pd.read_csv('hyp_b2b_mapping_new - mapping_final.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "254e05f3-1312-4f51-a446-fd42c69d22c9", "metadata": {}, "outputs": [], "source": ["hyp_b2b_clean_2 = hyp_b2b_clean_2[~hyp_b2b_clean_2[\"hyp_name_1\"].isnull()]\n", "\n", "df_melted = hyp_b2b_clean_2.melt(\n", "    id_vars=[\"warehouse_code\", \"hyblink_name\"],\n", "    value_vars=[\"hyp_name_1\", \"hyp_name_2\", \"hyp_name_3\", \"hyp_name_4\", \"hyp_name_5\", \"hyp_name_6\"],\n", "    var_name=\"source_column\",\n", "    value_name=\"core_name_hyp\",\n", ")\n", "\n", "df_melted = df_melted.dropna(subset=[\"core_name_hyp\"]).reset_index(drop=True)\n", "\n", "df_melted = df_melted.drop(columns=[\"source_column\"])\n", "\n", "hyp_b2b_clean_2 = df_melted"]}, {"cell_type": "code", "execution_count": null, "id": "1346abd7-8939-48b5-8422-557cdb1e3978", "metadata": {}, "outputs": [], "source": ["hyp_b2b_clean_2 = hyp_b2b_clean_2.iloc[:, :3]\n", "\n", "item_name_1.rename(columns={\"weight_per_packet\": \"weight_per_packet_blinkit\"}, inplace=True)\n", "hyp_b2b.rename(columns={\"weight_per_packet\": \"weight_per_packet_hyperpure\"}, inplace=True)\n", "hyp_b2b_clean_2.rename(columns={\"hyblink_name\": \"core_name\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "93549b35-431c-4a1c-bedb-e52ac05836d5", "metadata": {}, "outputs": [], "source": ["hyp_b2b[\"final_Date\"] = pd.to_datetime(hyp_b2b[\"final_Date\"])\n", "today = hyp_b2b[\"final_Date\"].max()\n", "group_cols = [\n", "    \"warehouse_code\",\n", "    \"product_number\",\n", "    \"product_name\",\n", "    \"weight_per_packet_hyperpure\",\n", "    \"core_name_hyp\",\n", "]\n", "\n", "unique_combinations = hyp_b2b[group_cols].drop_duplicates()\n", "\n", "records = []\n", "\n", "for _, row in unique_combinations.iterrows():\n", "    mask = (\n", "        (hyp_b2b[\"warehouse_code\"] == row[\"warehouse_code\"])\n", "        & (hyp_b2b[\"product_number\"] == row[\"product_number\"])\n", "        & (hyp_b2b[\"product_name\"] == row[\"product_name\"])\n", "        & (hyp_b2b[\"weight_per_packet_hyperpure\"] == row[\"weight_per_packet_hyperpure\"])\n", "        & (hyp_b2b[\"core_name_hyp\"] == row[\"core_name_hyp\"])\n", "    )\n", "    filtered_data = hyp_b2b[mask]\n", "\n", "    for day_offset in range(4):\n", "        date_start = today - <PERSON><PERSON><PERSON>(days=day_offset)\n", "        recent_data = filtered_data[(filtered_data[\"final_Date\"] == date_start)]\n", "\n", "        if not recent_data.empty:\n", "            total_qty = recent_data[\"quantity_delivered\"].sum()\n", "            if total_qty > 0:\n", "                weighted_price = (\n", "                    recent_data[\"Price_Per_Unit\"] * recent_data[\"quantity_delivered\"]\n", "                ).sum() / total_qty\n", "            else:\n", "                weighted_price = recent_data[\"Price_Per_Unit\"].mean()\n", "\n", "            records.append(\n", "                {\n", "                    \"warehouse_code\": row[\"warehouse_code\"],\n", "                    \"product_number\": row[\"product_number\"],\n", "                    \"product_name\": row[\"product_name\"],\n", "                    \"weight_per_packet_hyperpure\": row[\"weight_per_packet_hyperpure\"],\n", "                    \"core_name_hyp\": row[\"core_name_hyp\"],\n", "                    \"weighted_Price_Per_Unit\": weighted_price,\n", "                    \"date_used\": date_start,\n", "                }\n", "            )\n", "            break\n", "\n", "hyp_b2b_2 = pd.DataFrame(records)"]}, {"cell_type": "code", "execution_count": null, "id": "c9239e12-4e7b-494f-9938-9e2b54160e95", "metadata": {}, "outputs": [], "source": ["hyp_b2b1 = (\n", "    hyp_b2b_2.merge(cpc_wh_mapping, on=\"warehouse_code\", how=\"inner\")\n", "    .drop(columns=\"warehouse_code\")\n", "    .rename(columns={\"CPC\": \"warehouse_code\"})\n", ")\n", "\n", "hyp_b2b_clean2 = hyp_b2b_clean_2.merge(item_name_1, on=\"core_name\", how=\"left\")\n", "\n", "hyp_b2b_2 = hyp_b2b1.merge(hyp_b2b_clean2, on=[\"warehouse_code\", \"core_name_hyp\"], how=\"inner\")\n", "\n", "hyp_b2b_2_deduped = hyp_b2b_2.drop_duplicates(\n", "    subset=[\"warehouse_code\", \"item_id\", \"item_name\"], keep=\"first\"\n", ")\n", "\n", "hyp_b2b_2_deduped[\"per_unit_price_hyp\"] = (\n", "    hyp_b2b_2_deduped[\"weighted_Price_Per_Unit\"]\n", "    * hyp_b2b_2_deduped[\"weight_per_packet_blinkit\"]\n", "    / hyp_b2b_2_deduped[\"weight_per_packet_hyperpure\"]\n", ")\n", "hyp_b2b_2_deduped[\"per_unit_price_hyp\"] = hyp_b2b_2_deduped[\"per_unit_price_hyp\"].round(1)\n", "\n", "set(hyp_b2b[\"warehouse_code\"]) - set(cpc_wh_mapping[\"warehouse_code\"])\n", "\n", "hyp_b2b_3_deduped = hyp_b2b_2_deduped"]}, {"cell_type": "code", "execution_count": null, "id": "039f1d5c-dd5d-4190-83a2-61c13b200d8b", "metadata": {}, "outputs": [], "source": ["hyp_b2b_3_deduped = hyp_b2b_3_deduped[~(hyp_b2b_3_deduped[\"item_id\"].isnull())]"]}, {"cell_type": "markdown", "id": "087ba3aa-fa5b-45fb-9ff8-9409ee2e3f79", "metadata": {}, "source": ["### Merge Data"]}, {"cell_type": "code", "execution_count": null, "id": "f7299916-be74-418e-be81-989f2e8ca06f", "metadata": {}, "outputs": [], "source": ["cc_mandi_comparison = result_cc_to_cpc_sto_wo_overheads_1.merge(\n", "    mandi_lp_join_1, on=[\"warehouse_code\", \"item_id\"], how=\"left\"\n", ")\n", "\n", "cc_mandi_comparison[\"cc_price_per_item\"] = cc_mandi_comparison[\"cc_price_per_item\"].round(1)"]}, {"cell_type": "code", "execution_count": null, "id": "d5e771e1-95e5-4f5d-bc26-75878ca65102", "metadata": {}, "outputs": [], "source": ["# total_grn_sum = cc_mandi_comparison['grn'].sum()\n", "# null_grn_sum = cc_mandi_comparison[cc_mandi_comparison['mandi_price_per_item'].isnull()]['grn'].sum()\n", "# populated_grn_sum = total_grn_sum - null_grn_sum\n", "# null_coverage_percent = (null_grn_sum / total_grn_sum) * 100\n", "# populated_coverage_percent = (populated_grn_sum / total_grn_sum) * 100\n", "# populated_coverage_percent"]}, {"cell_type": "code", "execution_count": null, "id": "35584fda-a4a8-4ef6-a734-040921842b76", "metadata": {}, "outputs": [], "source": ["cc_mandi_hyp_comparison = cc_mandi_comparison.merge(\n", "    hyp_b2b_3_deduped[[\"item_id\", \"warehouse_code\", \"per_unit_price_hyp\"]],\n", "    on=[\"item_id\", \"warehouse_code\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "64eb35ff-d52b-4e2a-bb20-41751e2579de", "metadata": {}, "outputs": [], "source": ["cc_mandi_hyp_comparison[\"total_overhead_per_item\"] = (\n", "    cc_mandi_hyp_comparison[\"fixed_overhead_per_item\"]\n", "    + cc_mandi_hyp_comparison[\"variable_overhead_per_item\"]\n", ")\n", "\n", "cols = list(cc_mandi_hyp_comparison.columns)\n", "\n", "var_idx = cols.index(\"variable_overhead_per_item\")\n", "\n", "cols.remove(\"total_overhead_per_item\")\n", "\n", "cols.insert(var_idx + 1, \"total_overhead_per_item\")\n", "\n", "cc_mandi_hyp_comparison = cc_mandi_hyp_comparison[cols]\n", "\n", "cc_mandi_hyp_comparison[\"total_overhead_per_item\"] = cc_mandi_hyp_comparison[\n", "    \"total_overhead_per_item\"\n", "].round(2)"]}, {"cell_type": "code", "execution_count": null, "id": "66bec38c-402f-451c-a759-901542def552", "metadata": {}, "outputs": [], "source": ["# cc_mandi_hyp_comparison_1 = cc_mandi_hyp_comparison[(cc_mandi_hyp_comparison['fixed_overhead_per_item']>0) & (cc_mandi_hyp_comparison['variable_overhead_per_item']>0)]\n", "cc_mandi_hyp_comparison_1 = cc_mandi_hyp_comparison"]}, {"cell_type": "code", "execution_count": null, "id": "49f47144-5935-4716-a3a1-e3c991d7fb27", "metadata": {}, "outputs": [], "source": ["vendor_to_cpc_grp_1.rename(\n", "    columns={\"Price_Per_Unit\": \"price_per_unit_vendor_including_overheads\"}, inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "71de91a5-747f-4133-8b2c-6ac6c1a5c92d", "metadata": {}, "outputs": [], "source": ["vendor_to_cpc_grp_2 = vendor_to_cpc_grp_1.merge(\n", "    cc_mandi_hyp_comparison[[\"warehouse_code\", \"item_id\", \"total_overhead_per_item\"]],\n", "    on=[\"warehouse_code\", \"item_id\"],\n", "    how=\"inner\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b1fe1ecc-f27a-46c5-b619-60ab1e282266", "metadata": {}, "outputs": [], "source": ["vendor_to_cpc_grp_2[\"price_per_unit_vendor_excluding_overheads\"] = (\n", "    vendor_to_cpc_grp_2[\"price_per_unit_vendor_including_overheads\"]\n", "    - vendor_to_cpc_grp_2[\"total_overhead_per_item\"]\n", ").round(1)"]}, {"cell_type": "code", "execution_count": null, "id": "298a31ee-f0bf-4c4e-8b8a-1ef70234606c", "metadata": {}, "outputs": [], "source": ["vendor_to_cpc_grp_1[\"price_per_unit_vendor_including_overheads\"] = vendor_to_cpc_grp_1[\n", "    \"price_per_unit_vendor_including_overheads\"\n", "].round(1)"]}, {"cell_type": "code", "execution_count": null, "id": "5bc55eec-cef6-4681-b99d-ee44f7d0923b", "metadata": {}, "outputs": [], "source": ["cc_mandi_hyp_vendor_comparison = cc_mandi_hyp_comparison.merge(\n", "    vendor_to_cpc_grp_1[[\"warehouse_code\", \"item_id\", \"price_per_unit_vendor_including_overheads\"]],\n", "    on=[\"warehouse_code\", \"item_id\"],\n", "    how=\"left\",\n", ").merge(\n", "    vendor_to_cpc_grp_2[[\"warehouse_code\", \"item_id\", \"price_per_unit_vendor_excluding_overheads\"]],\n", "    on=[\"warehouse_code\", \"item_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "be0265c7-e46c-4b21-924c-65de73d14d67", "metadata": {}, "outputs": [], "source": ["cc_mandi_hyp_vendor_comparison_grp = cc_mandi_hyp_vendor_comparison.groupby(\n", "    [\"source_cc\", \"warehouse_code\", \"item_id\", \"item_name\"], as_index=False\n", ").mean(numeric_only=True)"]}, {"cell_type": "code", "execution_count": null, "id": "1fd50b67-928e-43c4-a424-e0a73f6ea9fb", "metadata": {}, "outputs": [], "source": ["cc_mandi_hyp_vendor_comparison_grp_1 = cc_mandi_hyp_vendor_comparison_grp.merge(\n", "    cc_mandi_hyp_vendor_comparison[\n", "        [\"source_cc\", \"warehouse_code\", \"item_id\", \"item_name\", \"Mandi_name\"]\n", "    ].drop_duplicates(),\n", "    on=[\"source_cc\", \"warehouse_code\", \"item_id\", \"item_name\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ea9234ab-61cb-4b11-9798-f2783356ad54", "metadata": {}, "outputs": [], "source": ["max_date = mandi_lp[\"date_\"].max()\n", "cc_mandi_hyp_vendor_comparison_grp_1[\"max_date\"] = max_date"]}, {"cell_type": "code", "execution_count": null, "id": "e47f081c-11cb-441d-afdf-ae4ed8ae830d", "metadata": {}, "outputs": [], "source": ["cc_mandi_hyp_vendor_comparison_grp_1.rename(\n", "    columns={\n", "        \"per_unit_price_hyp\": \"hyp_price_per_item\",\n", "        \"price_per_unit_vendor_including_overheads\": \"vendor_price_per_unit_including_overheads\",\n", "        \"price_per_unit_vendor_excluding_overheads\": \"vendor_price_per_unit_excluding_overheads\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3f6da7be-99cc-418e-aa73-bce1f68c2f46", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT DISTINCT \n", "        ma.item_id,\n", "        pt.name AS p_type, \n", "        icd.l2\n", "    FROM rpc.product_product ma \n", "    JOIN (\n", "        SELECT item_id, MAX(id) AS id\n", "        FROM lake_rpc.product_product\n", "        WHERE approved = 1\n", "        GROUP BY item_id\n", "    ) b ON ma.item_id = b.item_id\n", "    JOIN rpc.item_category_details icd ON icd.item_id = ma.item_id\n", "    JOIN rpc.product_facility_master_assortment pfma ON pfma.item_id = ma.item_id\n", "    LEFT JOIN rpc.item_product_mapping ipm ON ipm.item_id = icd.item_id\n", "    LEFT JOIN cms.gr_product gp ON gp.id = ipm.product_id\n", "    LEFT JOIN cms.gr_product_type pt ON pt.id = gp.type_id\n", "    WHERE pfma.master_assortment_substate_id = 1\n", "    AND icd.l0_id IN (1487)\n", "    AND icd.l2 NOT IN ('Other SKU')\n", "\"\"\"\n", "item_details = read_sql_query(query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "d0316187-2771-4582-b3b4-02e0a8aa3870", "metadata": {}, "outputs": [], "source": ["cc_mandi_hyp_vendor_comparison_grp_2 = cc_mandi_hyp_vendor_comparison_grp_1[\n", "    ~cc_mandi_hyp_vendor_comparison_grp_1[\"cc_price_per_item\"].isnull()\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "486f0d8a-d7e7-4dc8-afad-a04af97e04c8", "metadata": {}, "outputs": [], "source": ["cc_mandi_hyp_vendor_comparison_grp_3 = cc_mandi_hyp_vendor_comparison_grp_2.merge(\n", "    item_details, on=\"item_id\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0ccbf78b-3e6c-4aff-8569-a09c21a7ec74", "metadata": {}, "outputs": [], "source": ["cc_mandi_hyp_vendor_comparison_grp_3 = cc_mandi_hyp_vendor_comparison_grp_3[\n", "    [\n", "        \"source_cc\",\n", "        \"warehouse_code\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"p_type\",\n", "        \"l2\",\n", "        \"Mandi_name\",\n", "        \"weight_per_packet\",\n", "        \"fixed_overhead_per_item\",\n", "        \"variable_overhead_per_item\",\n", "        \"total_overhead_per_item\",\n", "        \"cc_price_per_item\",\n", "        \"grn\",\n", "        \"mandi_price_per_item\",\n", "        \"hyp_price_per_item\",\n", "        \"vendor_price_per_unit_including_overheads\",\n", "        \"vendor_price_per_unit_excluding_overheads\",\n", "        \"max_date\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "3bcfdd72-76ba-4dbd-b800-711200b94777", "metadata": {}, "outputs": [], "source": ["for column in cc_mandi_hyp_vendor_comparison_grp_3.select_dtypes(include=[\"float64\"]).columns:\n", "    cc_mandi_hyp_vendor_comparison_grp_3[column] = cc_mandi_hyp_vendor_comparison_grp_3[\n", "        column\n", "    ].round(2)"]}, {"cell_type": "code", "execution_count": null, "id": "fb3c9b37-0031-44e7-b888-9391fb3adfca", "metadata": {}, "outputs": [], "source": ["def calculate_min_price(row):\n", "    prices = []\n", "    if pd.notnull(row[\"cc_price_per_item\"]):\n", "        prices.append(row[\"cc_price_per_item\"])\n", "    if pd.notnull(row[\"mandi_price_per_item\"]):\n", "        prices.append(row[\"mandi_price_per_item\"] * 0.9)\n", "    if pd.notnull(row[\"hyp_price_per_item\"]):\n", "        prices.append(row[\"hyp_price_per_item\"] * 0.95)\n", "    if pd.notnull(row[\"vendor_price_per_unit_including_overheads\"]):\n", "        adjusted_vendor_price = (row[\"vendor_price_per_unit_including_overheads\"] * 0.85) - row[\n", "            \"variable_overhead_per_item\"\n", "        ]\n", "        prices.append(adjusted_vendor_price)\n", "    return min(prices) if prices else np.nan\n", "\n", "\n", "cc_mandi_hyp_vendor_comparison_grp_3[\"Suggestive pricing\"] = (\n", "    cc_mandi_hyp_vendor_comparison_grp_3.apply(calculate_min_price, axis=1)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ab527bf6-2511-47ea-bedf-5a56fc5e383c", "metadata": {}, "outputs": [], "source": ["cc_mandi_hyp_vendor_comparison_grp_3[\"Influence\"] = (\n", "    cc_mandi_hyp_vendor_comparison_grp_3[\"cc_price_per_item\"]\n", "    - cc_mandi_hyp_vendor_comparison_grp_3[\"Suggestive pricing\"]\n", ") * cc_mandi_hyp_vendor_comparison_grp_3[\"grn\"]\n", "\n", "cc_mandi_hyp_vendor_comparison_grp_3[\"CC_price\"] = (\n", "    cc_mandi_hyp_vendor_comparison_grp_3[\"cc_price_per_item\"]\n", "    * cc_mandi_hyp_vendor_comparison_grp_3[\"grn\"]\n", ")\n", "cc_mandi_hyp_vendor_comparison_grp_3[\"Suggestive_Price_CC\"] = (\n", "    cc_mandi_hyp_vendor_comparison_grp_3[\"Suggestive pricing\"]\n", "    * cc_mandi_hyp_vendor_comparison_grp_3[\"grn\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e66433b5-c3a3-4782-bad1-ffe50cb78155", "metadata": {}, "outputs": [], "source": ["cc_mandi_hyp_vendor_comparison_grp_3[\"Suggestive pricing\"] = cc_mandi_hyp_vendor_comparison_grp_3[\n", "    \"Suggestive pricing\"\n", "].round(1)"]}, {"cell_type": "code", "execution_count": null, "id": "b7b3583b-4f9b-4276-b00e-0861f761667a", "metadata": {}, "outputs": [], "source": ["cc_mandi_hyp_vendor_comparison_grp_3[\"suggestive_pricing_1\"] = cc_mandi_hyp_vendor_comparison_grp_3[\n", "    \"Suggestive pricing\"\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "35d1145c-9b4e-4a37-86df-e6a8a6c746fc", "metadata": {"tags": []}, "outputs": [], "source": ["df = cc_mandi_hyp_vendor_comparison_grp_3.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "f367dfbd-3cd4-4ab0-bf09-e4aeeb16f26b", "metadata": {}, "outputs": [], "source": ["price_per_kg = df[\"cc_price_per_item\"] / df[\"weight_per_packet\"]\n", "\n", "mask1 = (price_per_kg <= 30) & (df[\"cc_price_per_item\"] == df[\"Suggestive pricing\"])\n", "df.loc[mask1, \"Suggestive pricing\"] *= 0.95\n", "\n", "mask2 = (\n", "    (price_per_kg > 30)\n", "    & (price_per_kg <= 50)\n", "    & (df[\"cc_price_per_item\"] == df[\"Suggestive pricing\"])\n", ")\n", "df.loc[mask2, \"Suggestive pricing\"] = df.loc[mask2, \"Suggestive pricing\"] - (\n", "    1.5 * df.loc[mask2, \"weight_per_packet\"]\n", ")\n", "\n", "mask3 = (price_per_kg > 50) & (df[\"cc_price_per_item\"] == df[\"Suggestive pricing\"])\n", "df.loc[mask3, \"Suggestive pricing\"] *= 0.97"]}, {"cell_type": "code", "execution_count": null, "id": "607b4f1d-d535-4a54-8856-70534018600a", "metadata": {}, "outputs": [], "source": ["df.columns = df.columns.str.lower()"]}, {"cell_type": "code", "execution_count": null, "id": "fb8292c4-2231-4cc9-83a4-d50cb205b6f6", "metadata": {}, "outputs": [], "source": ["df = df.rename(columns={\"max_date\": \"date_\", \"suggestive pricing\": \"suggestive_pricing\"})"]}, {"cell_type": "code", "execution_count": null, "id": "f2aa86ad-66e2-4dd3-b6e2-a13289c73956", "metadata": {}, "outputs": [], "source": ["df = df[[\"date_\"] + df.columns.difference([\"date_\"]).tolist()]"]}, {"cell_type": "code", "execution_count": null, "id": "8a7d3ca6-6a87-4b8f-bf8d-5075da786a32", "metadata": {}, "outputs": [], "source": ["df[\"price_diff\"] = (\n", "    (\n", "        df[\"cc_price_per_item\"] / df[\"weight_per_packet\"]\n", "        - df[\"suggestive_pricing\"] / df[\"weight_per_packet\"]\n", "    )\n", "    * 1.000\n", "    * 100\n", "    / (df[\"cc_price_per_item\"] / df[\"weight_per_packet\"])\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "475cc7be-94e3-4131-bbc1-2c18f5bf0950", "metadata": {}, "outputs": [], "source": ["def flag_outliers(df, iqr_multipliers):\n", "    df = df.copy()\n", "\n", "    df[\"l2\"] = df[\"l2\"].fillna(\"Unknown\")\n", "\n", "    def get_upper_bound(series, multiplier):\n", "        q1 = series.quantile(0.25)\n", "        q3 = series.quantile(0.75)\n", "        iqr = q3 - q1\n", "        return q3 + multiplier * iqr\n", "\n", "    def compute_upper_bound(row):\n", "        group = row[\"l2\"]\n", "        multiplier = iqr_multipliers.get(group, 2.5)  # default to 2.5 if not specified\n", "        group_data = df[df[\"l2\"] == group][\"price_diff\"]\n", "        return get_upper_bound(group_data, multiplier)\n", "\n", "    df[\"upper_bound\"] = df.apply(compute_upper_bound, axis=1)\n", "\n", "    df[\"outlier_flag\"] = (df[\"price_diff\"] > df[\"upper_bound\"]).astype(int)\n", "\n", "    return df.drop(columns=\"upper_bound\")\n", "\n", "\n", "iqr_multipliers = {\n", "    \"Fresh Vegetables\": 0.75,\n", "    \"Fresh Fruits\": 3,\n", "    \"Leafies & Herbs\": 0.5,\n", "    \"Exotics\": 2.5,\n", "    \"Flowers & Leaves\": 2.0,\n", "    \"Freshly Cut & Sprouts\": 3.0,\n", "    \"Unknown\": 2.5,\n", "}\n", "\n", "df1 = flag_outliers(df, iqr_multipliers)"]}, {"cell_type": "code", "execution_count": null, "id": "d98db3b2-da14-415e-9228-c3bfea48afe9", "metadata": {}, "outputs": [], "source": ["df1.drop(columns=\"price_diff\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "4ede8d1c-ac16-47a8-9b21-bfe3a4a40993", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"fnv_cc_suggestive_price\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date_\", \"type\": \"date\", \"description\": \"date_\"},\n", "        {\"name\": \"source_cc\", \"type\": \"varchar\", \"description\": \"source_cc\"},\n", "        {\"name\": \"warehouse_code\", \"type\": \"varchar\", \"description\": \"warehouse_code\"},\n", "        {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"item_id\"},\n", "        {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"item_name\"},\n", "        {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"p_type\"},\n", "        {\"name\": \"l2\", \"type\": \"varchar\", \"description\": \"l2\"},\n", "        {\"name\": \"Mandi_name\", \"type\": \"varchar\", \"description\": \"Mandi_name\"},\n", "        {\"name\": \"weight_per_packet\", \"type\": \"double\", \"description\": \"weight_per_packet\"},\n", "        {\n", "            \"name\": \"fixed_overhead_per_item\",\n", "            \"type\": \"double\",\n", "            \"description\": \"fixed_overhead_per_item\",\n", "        },\n", "        {\n", "            \"name\": \"variable_overhead_per_item\",\n", "            \"type\": \"double\",\n", "            \"description\": \"variable_overhead_per_item\",\n", "        },\n", "        {\n", "            \"name\": \"total_overhead_per_item\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_overhead_per_item\",\n", "        },\n", "        {\"name\": \"cc_price_per_item\", \"type\": \"double\", \"description\": \"cc_price_per_item\"},\n", "        {\"name\": \"grn\", \"type\": \"double\", \"description\": \"grn\"},\n", "        {\"name\": \"mandi_price_per_item\", \"type\": \"double\", \"description\": \"mandi_price_per_item\"},\n", "        {\"name\": \"hyp_price_per_item\", \"type\": \"double\", \"description\": \"hyp_price_per_item\"},\n", "        {\n", "            \"name\": \"vendor_price_per_unit_including_overheads\",\n", "            \"type\": \"double\",\n", "            \"description\": \"vendor_price_per_unit_including_overheads\",\n", "        },\n", "        {\n", "            \"name\": \"vendor_price_per_unit_excluding_overheads\",\n", "            \"type\": \"double\",\n", "            \"description\": \"vendor_price_per_unit_excluding_overheads\",\n", "        },\n", "        {\"name\": \"suggestive_pricing\", \"type\": \"double\", \"description\": \"Suggestive pricing\"},\n", "        {\"name\": \"influence\", \"type\": \"double\", \"description\": \"Influence\"},\n", "        {\"name\": \"cc_price\", \"type\": \"double\", \"description\": \"CC_price\"},\n", "        {\"name\": \"suggestive_price_cc\", \"type\": \"double\", \"description\": \"Suggestive_Price_CC\"},\n", "        {\"name\": \"suggestive_pricing_1\", \"type\": \"double\", \"description\": \"suggestive_pricing_1\"},\n", "        {\"name\": \"outlier_flag\", \"type\": \"INTEGER\", \"description\": \"outlier_flag\"},\n", "    ],\n", "    \"primary_key\": [\"item_id\", \"warehouse_code\", \"source_cc\", \"date_\"],  # Adjust if needed\n", "    \"partition_key\": [\"date_\"],\n", "    \"incremental_key\": \"date_\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert\n", "    \"table_description\": \"CC suggetive pricing\",\n", "}\n", "pb.to_trino(df1, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "f1dd5472-5223-4c43-9419-a781191e1822", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b9a24fe7-afaa-489e-a229-0495206693c3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "70aaf5cb-9cc8-465e-9526-f03f914a654c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9431ffba-e4db-431f-b4e3-67be25a6cc1a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}