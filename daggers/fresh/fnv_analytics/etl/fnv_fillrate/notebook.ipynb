{"cells": [{"cell_type": "code", "execution_count": null, "id": "2ce28f26-3dd0-4196-9a2f-f800fa44ff88", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import time\n", "from datetime import datetime, timedelta, date\n", "\n", "\n", "# !pip install pandasql\n", "# import pandasql as ps"]}, {"cell_type": "code", "execution_count": null, "id": "0edc409a-8608-4c9e-b6a4-9bb809665993", "metadata": {"tags": []}, "outputs": [], "source": ["trino = pb.get_connection(\"[Warehouse] Trino\")\n", "# redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "# retail = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "id": "f22d2937-2c8b-4784-8ac6-1ae3ffc9bc07", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 5\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "96d4b00f-de39-4a9f-98ec-c732e9c9e47a", "metadata": {}, "outputs": [], "source": ["pd.options.mode.chained_assignment = None  # default='warn'"]}, {"cell_type": "code", "execution_count": null, "id": "25a5b471-ed30-4a49-a325-425af559b283", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib==3.8"]}, {"cell_type": "code", "execution_count": null, "id": "cbcf37e1-a257-47fa-9522-33310996eb87", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pytz\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")\n", "\n", "current_time = datetime.now(IST)\n", "\n", "current_hour = pd.to_datetime(current_time).hour\n", "\n", "today_date = current_time.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "122797ca-b2d3-49b7-b22b-6d6cd2e32b5d", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=7.0,\n", "    row_height=0.825,\n", "    font_size=24,\n", "    header_color=\"#5A5A5A\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    #     cellLoc='center',\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, cellLoc=\"center\", **kwargs\n", "    )\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "a7457d8e-6e79-4e0c-9e77-c4b57021dd02", "metadata": {}, "outputs": [], "source": ["def to_sheets(df, sheet_id, sheet_name):\n", "    max_tries = 4\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_sheets(df, sheet_id, sheet_name)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed to sheet in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed to sheet in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "markdown", "id": "59691e6b-43dd-4e2f-9a2d-c279e77af5b4", "metadata": {}, "source": ["#### VFR "]}, {"cell_type": "code", "execution_count": null, "id": "335b00bf-a07c-4ac8-9c47-da055e7a92d7", "metadata": {"tags": []}, "outputs": [], "source": ["cpc_po_sql = f\"\"\"\n", "with base as\n", "(select distinct poi.item_id as item_id, icd.name as item_name, poi.upc, p.outlet_id as outlet_id, o.name as outlet_name, o.facility_id, p.id po_id, p.po_number,       \n", "    case when (ps.schedule_date_time + interval '330' minute) is null and extract(hour from issue_date + interval '330' minute) between 19 and 23\n", "                then date(issue_date + interval '330' minute + interval '1' day) \n", "        when (ps.schedule_date_time + interval '330' minute) is null and extract(hour from issue_date + interval '330' minute) between 0 and 18\n", "                then date(issue_date + interval '330' minute) \n", "        when extract(hour from ps.schedule_date_time + interval '330' minute) between 15 and 23 \n", "                then date(ps.schedule_date_time + interval '330' minute + interval '1' day)\n", "        else \n", "                date(ps.schedule_date_time + interval '330' minute) end as date_,\n", "        units_ordered as total_po,grn.quan as total_grn,\n", "        dnpd.quantity as rejection,\n", "        Case when extract(hour from max_grn_time) between 14 and 23 then 'Slot A'\n", "        when extract(hour from max_grn_time) between 00 and 01 then 'Slot A' \n", "            else 'Slot B' end as Slot,\n", "            poi.landing_rate as po_lp\n", "    from po.purchase_order p\n", "    left join po.po_schedule ps on p.id=ps.po_id_id\n", "    inner join po.purchase_order_items poi on p.id=poi.po_id\n", "          and date(p.issue_date + interval '330' minute) between current_date - interval '2' day and current_date\n", "    inner join retail.console_outlet o on o.id=p.outlet_id\n", "    inner join po.purchase_order_status posa on posa.po_id = p.id\n", "    inner join po.purchase_order_state posta on posta.id = posa.po_state_id\n", "    inner join rpc.item_category_details icd on icd.item_id = poi.item_id and icd.l0_id in (1487)\n", "    left join (select po_id, item_id, sum(quantity) quan, max(created_at + interval '330' minute) as max_grn_time from po.po_grn grn where grn.insert_ds_ist >= cast(current_date - interval '2' day as varchar) group by 1,2) grn\n", "        on grn.item_id = poi.item_id and grn.po_id = p.id\n", "    left join pos.discrepancy_note dn on dn.po_number = p.po_number\n", "    left join pos.discrepancy_note_product_detail dnpd on dnpd.dn_id_id= dn.id and dnpd.upc_id = poi.upc\n", "    left join pos.discrepancy_reason dr on dr.id = dnpd.reason_code and dr.name in ('Damaged','Near Expired','Expired','Stale items- Mould/Fungus/Rotten')\n", "    where (posta.name not in ('Cancelled', 'Rejected', 'Cancelled post Creation') or (posta.name in ('Expired') and grn.quan is not null)) \n", "  and p.outlet_id in (1673,2671,2665,2728,2923,3227)\n", "    ),\n", "    \n", "final_cpc_po as (select date(t1.date_) as date_, \n", "cloud_store_id as outlet_id, \n", "t1.item_id as item_id, \n", "avg(po_lp) as price_per_unit,\n", "sum(t1.po_qty) as po_qty, \n", "sum(t1.grn_qty) as grn_qty, \n", "sum(t1.slot_a_grn) as slot_a_grn,\n", "sum(t1.rejected_qty) as rejected_qty\n", "from(select b.date_, b.outlet_id, b.outlet_name, \n", "        coalesce(wom.cloud_store_id, b.outlet_id) as cloud_store_id, b.item_id as item_id, b.item_name as item_name, total_po as po_qty, total_grn as grn_qty, rejection as rejected_qty,po_lp,\n", "        case when slot = 'Slot A' then total_grn else 0 end as slot_a_grn\n", "from base b\n", "left join  retail.warehouse_outlet_mapping wom on wom.warehouse_id = b.outlet_id\n", "order by date_ desc) t1\n", "inner join  retail.console_outlet o on o.id=t1.cloud_store_id\n", "where t1.date_ is not null and t1.date_ < current_date + interval '2' day\n", "group by 1,2,3\n", "order by 1 desc,2),\n", "\n", "fills_base as (\n", "SELECT\n", "    (po.created_at + interval '330' minute) as po_created_at, po.purchase_order_number, po.is_multiple_grn_enabled, po.po_status,\n", "    po.vendor_id, ipom.outlet_id as warehouse_id, po.warehouse_code, (po.po_consideration_date + interval '330' minute) as po_consideration_date,\n", "    poi.product_number as hp_item_id, poi.product_name as hp_product_name, pi.item_id as bl_item_id, icd.name as bl_item_name,\n", "    poi.quantity_ordered as po_qty, (po.expected_delivery_date + interval '330' minute) as expected_delivery_date, \n", "    pg.invoiced_quantity, pg.dock_rejected_quantity, pg.grn_quantity, \n", "    -- pg.delivered_quantity, \n", "    poi.price_per_unit,\n", "    (case when po.is_multiple_grn_enabled = 0 then coalesce(poi.quantity_delivered,0) else coalesce(pg.delivered_quantity,0) end) as quantity_delivered,\n", "\n", "     grn_created_at,\n", "Case \n", "        when po.warehouse_code in ('CPC-CHN2') and extract(hour from grn_created_at) between 10 and 23 then grn_created_at + interval '1' day\n", "        when po.warehouse_code in ('CPC-NOIDA1','CPC-DEL3','CPC-GGN2') and extract(hour from grn_created_at) between 12 and 23 then grn_created_at + interval '1' day\n", "        when po.warehouse_code in ('CPC-MUM4', 'CPC-AMD2','CPC-HYD2','CPC-HYD3','CPC-MUM3','CPC-KOL1','CPC-BLR7','CPC-PUNE1','CPC-INDORE2','CPC-LKO1','CPC-RPJ1','CPC-LDH1', 'CPC-KOL2') and extract(hour from grn_created_at) between 11 and 23 then grn_created_at + interval '1' day\n", "        when po.warehouse_code in ('CPC-VARANASI1', 'CPC-AGRA1','CPC-GOA1','CPC-NAGPUR1', 'CPC-SURAT2','CPC-GUNTUR1', 'CPC-VISAKHAPATNAM1') then grn_created_at + interval '1' day\n", "        else grn_created_at end as final_Date,\n", "    Case\n", "        when extract(hour from grn_created_at) between 11 and 23 then 'Slot A' else 'SLOT B' end as Slot,\n", "    Case\n", "        when pg.id>0 AND (pg.delivered_quantity + pg.dock_rejected_quantity /* + pgi.grn_quantity*/) > 0 then 'GRN Completed' \n", "        when is_multiple_grn_enabled = 0 then po.po_status \n", "        else 'GRN Incomplete'  END AS grn_status\n", "FROM\n", "    zomato.hp_wms.purchase_order po\n", "INNER JOIN\n", "    zomato.hp_wms.purchase_order_items poi on po.id = poi.purchase_order_id\n", "INNER JOIN\n", "    po.edi_integration_partner_item_mapping pi ON cast(pi.partner_item_id as int) = poi.product_number and pi.active\n", "INNER JOIN\n", "    rpc.item_category_details icd ON icd.item_id = pi.item_id and icd.l0_id = 1487\n", "LEFT JOIN (Select pg.grn_quantity, purchase_order_item_id, invoiced_quantity, id, delivered_quantity,  dock_rejected_quantity, \n", "            (pg.created_at + interval '330' minute) as grn_created_at \n", "            from  zomato.hp_wms.po_grn_item pg\n", "            where dt >= replace(CAST(date(current_Date) - interval '2' day as varchar), '-','')) pg on poi.id = pg.purchase_order_item_id\n", "INNER JOIN\n", "    po.edi_integration_partner_outlet_mapping ipom ON lower(ipom.partner_outlet_id) = lower(po.warehouse_code) AND ipom.active\n", "\n", "WHERE\n", "    po.dt >= replace(CAST(date(current_Date) - interval '2' day as varchar), '-','')\n", "AND\n", "    poi.dt >= replace(CAST(date(current_Date) - interval '2' day as varchar), '-','')\n", "ORDER BY \n", "    1 DESC\n", "),\n", "\n", "hp_sto_details_base  as (select \n", "case when Date_ is null then date(Date1_ + interval '1' day) \n", "when hour(Date_) >12 then date(Date_ + interval '1' day) else date(Date_) end as date_,\n", "--date(Date + interval '1' day) as date_, \n", "--Date_,\n", "--Date1_,\n", "ToWarehouseCode,\n", "outlet_id,\n", "bl_item_id as item_id,\n", "sum(STOQuantity) as po,\n", "sum(TransferedInventory) as grn,\n", "sum(TransferedInventory) as slot_a_grn,\n", "avg(price_per_unit) as price_per_unit\n", "from\n", "(select \n", "sto.id as orderid,\n", "ipom.outlet_id,\n", "stoi.product_number as ProductNumber,\n", "stoi.product_name as ProductName,\n", "pi.item_id as bl_item_id,\n", "icd.name as item_name,\n", "sto.source_warehouse_code as sourcewh,\n", "sto.destination_warehouse_code as ToWarehouseCode,\n", "stoi.stock_transferred_quantity as STOQuantity,\n", "stoi.warehouse_usable_quantity as TransferedInventory,\n", "stoi.price_per_unit,\n", "case when stoh.new_sto_status = 'RECEIVED' then stoh.updated_at+interval'330'minute else null end as Date_,\n", "stoh1.updated_at+interval'330'minute as Date1_,\n", "sto.order_number as sto_order_number\n", "\n", "from zomato.hp_wms.stock_transfer_order_item stoi\n", "inner join zomato.hp_wms.stock_transfer_order sto on sto.id = stoi.order_id and sto.status in ('RECEIVED','DISPATCHED')\n", "INNER JOIN po.edi_integration_partner_item_mapping pi ON CAST(pi.partner_item_id as int) = stoi.product_number\n", "INNER JOIN rpc.item_category_details icd ON icd.item_id = pi.item_id and icd.l0_id = 1487\n", "left join zomato.hp_wms.stock_transfer_order_status_history stoh on stoh.order_id = sto.id and stoh.new_sto_status = 'RECEIVED' \n", "left join zomato.hp_wms.stock_transfer_order_status_history stoh1 on stoh1.order_id = sto.id and stoh1.new_sto_status='DISPATCHED' \n", "inner JOIN po.edi_integration_partner_outlet_mapping ipom ON lower(ipom.partner_outlet_id) = lower(sto.destination_warehouse_code) AND ipom.active\n", "\n", "where date(stoh1.updated_at+interval'330'minute) >= current_Date - interval '2' day\n", "and stoi.dt>'20230101'\n", "and sto.dt>'20230101'\n", "and sto.source_warehouse_code not in ('WH-GGN2')\n", ")\n", "group by 1,2,3,4),\n", "\n", "hp_sto_details_sto as (select date_, \n", "outlet_id,\n", "item_id, \n", "price_per_unit as price_per_unit,\n", "(po) as po_qty, \n", "(grn) as grn_qty,\n", "(slot_a_grn) as slot_a_grn,\n", "po-grn as rejected_qty\n", "from hp_sto_details_base group by 1,2,3,4,5,6,7),\n", "\n", "cc_base  as (select dt, \n", " Case when extract(hour from lg.created_at) between 11 and 23 then lg.created_at + interval '1' day else lg.created_at end as final_Date,\n", " outlet_id,\n", " warehouse_code, lg.product_number as destination_product_name, icd.name as name, in_price,\n", " cast(SUBSTRING(source_entity_id, 1, POSITION('-' IN source_entity_id) - 1) as int) AS source_product_number, sum(quantity) as qty \n", " from zomato.hp_wms.bin_inventory_logs lg\n", " left join zomato.hp_wms.product p on p.product_number = lg.product_number\n", " left JOIN po.edi_integration_partner_item_mapping pi ON CAST(pi.partner_item_id as int) = lg.product_number and pi.active = true\n", " INNER JOIN rpc.item_category_details icd ON icd.item_id = pi.item_id and icd.l0_id = 1487\n", " inner JOIN po.edi_integration_partner_outlet_mapping ipom ON lower(ipom.partner_outlet_id) = lower(lg.warehouse_code) AND ipom.active\n", " left join zomato.hp_wms.category pc on p.parent_category_id = pc.id\n", " left join zomato.hp_wms.category c on p.category_id = c.id\n", " where lg.dt >= replace(CAST(date(current_Date) - interval '15' day as varchar), '-','') and reason_code in ('SKU_TRANSFER_ADD') \n", " group by 1,2,3,4,5,6,7,8),\n", " \n", "\n", "hp_cc_deatils as (  \n", "select date(final_Date) as date_, outlet_id, pi.item_id as item_id, avg(in_price) as price_per_unit,sum(qty) as po, sum(qty) as grn, sum(qty) as slot_a_grn , 0 as rejected_qty\n", " from cc_base final \n", " left join zomato.hp_wms.product p on p.product_number = final.source_product_number\n", " left join zomato.hp_wms.category pc on p.parent_category_id = pc.id\n", " left join zomato.hp_wms.category c on p.category_id = c.id\n", " left join po.edi_integration_partner_item_mapping pi ON CAST(pi.partner_item_id as int) = final.destination_product_name and pi.active = true\n", " where pc.name not in ('GT Vegetables & Fruits')\n", " and warehouse_code like ('%%CPC%%')\n", " group by 1,2,3),\n", "\n", "hp_sto_details as (Select * from hp_cc_deatils union select * from hp_sto_details_sto),\n", "\n", "hp_base as (\n", "SELECT a.*, (delivered_qtyyy * 1.000 / po_quantity) as fills, (dock_rejected_qty * 1.000 / po_quantity) as rejection_fills\n", "FROM (\n", "    SELECT\n", "        date(final_Date) as date_, warehouse_id, warehouse_code, hp_item_id, bl_item_id as item_id, bl_item_name as item_name,Slot,\n", "            SUM(po_qty) as po_quantity, SUM(dock_rejected_quantity) as dock_rejected_qty, SUM(quantity_delivered) as delivered_qtyyy, avg(price_per_unit) as price_per_unit\n", "    FROM\n", "        fills_base fb\n", "    WHERE\n", "        po_status in ('COMPLETED','APPROVED')\n", "    AND \n", "        date(final_Date) <= current_date \n", "    GROUP BY \n", "        1,2,3,4,5,6,7\n", ") a\n", "\n", "WHERE\n", "     date_ between current_date - interval '2' day and current_date \n", "\n", "ORDER BY 1 DESC),\n", "\n", "\n", "pre_final_hp as (SELECT \n", "date_,\n", "warehouse_id as outlet_id,\n", "item_id, \n", "avg(price_per_unit) as price_per_unit,\n", "max(po_quantity) as po, \n", "max(delivered_qtyyy) as grn,\n", "max(case when slot = 'Slot A' then delivered_qtyyy else 0 end) as slot_a_grn,\n", "max(dock_rejected_qty) as rejected_qty\n", "FROM hp_base hp\n", "inner join  retail.console_outlet o on o.id=hp.warehouse_id\n", "WHERE date_ between current_Date - interval '2' day and current_Date\n", "group by 1,2,3),\n", " \n", "final_hp as (select * from pre_final_hp union select * from hp_sto_details),\n", "\n", "\n", "dts as (with base as\n", "    (\n", "        select distinct\n", "            poi.item_id as item_id, \n", "            icd.name as item_name,\n", "            poi.upc, \n", "            itom.tag_value as outlet_id, \n", "            o.name as outlet_name,\n", "            p.vendor_name,\n", "            o.facility_id,\n", "            p.id as po_id,\n", "            p.po_number,\n", "            dnpd.quantity as rejection,\n", "            grn_time,\n", "            case when (ps.schedule_date_time + interval '330' minute) is null then date(issue_date + interval '330' minute + interval '1' day) \n", "                else date(ps.schedule_date_time + interval '330' minute) end as target_date_,\n", "            units_ordered as total_po,\n", "            grn.quan as total_grn,\n", "            poi.landing_rate as price_per_unit\n", "        from \n", "            po.purchase_order p\n", "        left join\n", "            po.po_schedule ps on p.id=ps.po_id_id\n", "             inner join\n", "            po.purchase_order_items poi on p.id=poi.po_id\n", "            and date(p.issue_date + interval '330' minute) >current_date - interval '2' day\n", "        inner join\n", "            retail.console_outlet o on o.id=p.outlet_id\n", "        inner join\n", "            po.purchase_order_status posa on posa.po_id = p.id\n", "        inner join\n", "            po.purchase_order_state posta on posta.id = posa.po_state_id\n", "        inner join rpc.item_category_details icd on icd.item_id = poi.item_id and icd.l0_id in (1487)\n", "        left join\n", "                (select po_id, item_id, max(grn.created_at + interval '330' minute) as grn_time, \n", "                sum(quantity) quan from po.po_grn grn \n", "                where grn.insert_ds_ist >= cast(current_date - interval '2' day as varchar) group by 1,2) grn\n", "            on \n", "                grn.item_id = poi.item_id \n", "                and grn.po_id = p.id\n", "            left join pos.discrepancy_note dn on dn.po_number = p.po_number\n", "            left join pos.discrepancy_note_product_detail dnpd on dnpd.dn_id_id= dn.id and dnpd.upc_id = poi.upc\n", "            left join pos.discrepancy_reason dr on dr.id = dnpd.reason_code and dr.name in ('Damaged','Near Expired','Expired','Stale items- Mould/Fungus/Rotten')\n", "            left join  rpc.item_outlet_tag_mapping itom on itom.outlet_id = p.outlet_id and itom.item_id = poi.item_id and tag_type_id = 8 and itom.active = 1 and itom.lake_active_record\n", "        where \n", "            (posta.name not in ('Cancelled', 'Rejected', 'Cancelled post Creation') or (posta.name in ('Expired') and grn.quan is not null))\n", "             and p.po_type_id <> 11 and p.vendor_id <> 13280\n", "            and p.outlet_id in (select distinct hot_outlet_id from supply_etls.outlet_details where business_type_id = 7)\n", "    )\n", "    \n", "    select \n", "    Case WHEN extract(hour FROM grn_time) between 18 AND 23 THEN DATE(grn_time) + INTERVAL '1' DAY  \n", "    When extract(hour from grn_time) between 1 and 17 then date(grn_time) \n", "    else target_date_ end as date_,\n", "    cast(outlet_id as INT), \n", "    item_id,\n", "    avg(price_per_unit) as price_per_unit,\n", "    sum(total_po) as po_quantity,\n", "    sum(total_grn) as total_grn,\n", "    0 as slot_a_grn,\n", "    sum(rejection) as rejected_qty\n", "    from base\n", "    group by 1,2,3),\n", "\n", "\n", "final_po as (select * from final_cpc_po union select * from final_hp union select * from dts)\n", "\n", "select date_, outlet_id, item_id, avg(price_per_unit) as price_per_unit, sum(po_qty) as po_qty, sum(grn_qty) as grn_qty, sum(slot_a_grn) slot_a_grn, sum(rejected_qty) as rejected_qty\n", "from final_po\n", "group by 1,2,3\n", "\"\"\"\n", "\n", "cpc_po = read_sql_query(cpc_po_sql, trino)\n", "cpc_po = cpc_po.drop_duplicates()\n", "\n", "\n", "indent_sql = f\"\"\"\n", "with t2_indent as(\n", "select\n", "    i.consumption_date as date_,\n", "    CASE\n", "            WHEN r.name in ('Gurgaon','HRNCR','HR-NCR') then 'HR-NCR'  \n", "            WHEN r.name in ('UPNCR','Ghaziabad','UP-NCR') then 'UP-NCR' \n", "            else r.name\n", "            end as city,    \n", "    case when i.backend_outlet_id = 2665 then 2666 else i.backend_outlet_id end as outlet_id,\n", "    o.name as outlet_name,\n", "    i.item_id,\n", "    icd.name as item_name,\n", "    sum(final_indent_qty) indent\n", "from\n", "    ars_etls.ars_fnv_ordering_indent i\n", "inner join\n", "    (select consumption_date,backend_outlet_id, max(created_at_ist) dd from ars_etls.ars_fnv_ordering_indent where consumption_date = current_date group by 1,2) t\n", "    on t.dd = i.created_at_ist\n", "    and t.backend_outlet_id = i.backend_outlet_id\n", "    and t.consumption_date = i.consumption_date\n", "inner join\n", "    retail.console_outlet o \n", "    on o.id = i.backend_outlet_id\n", "left join\n", "    retail.console_location r\n", "    on r.id = o.tax_location_id\n", "join\n", "    rpc.item_category_details icd \n", "    on icd.item_id = i.item_id\n", "where\n", "    i.consumption_date = current_date \n", "    -- - interval '2' day\n", "group by 1,2,3,4,5,6\n", "),\n", "\n", "t3_indent as\n", "( \n", "select \n", "    n.date_of_consumption as date_,    \n", "    r.name as city,\n", "    n.backend_hot_outlet as outlet_id,\n", "    o.name as outlet_name,\n", "    n.item_id,\n", "    icd.name as item_name,\n", "    sum(final_indent_qty) indent\n", "from \n", "    supply_etls.fnv_indent_ordering_t_plus_n n\n", "inner join\n", "    (select date_of_consumption,  backend_hot_outlet, max(updated_at_ist) dd from supply_etls.fnv_indent_ordering_t_plus_n where date_ist = current_date - interval '3' day group by 1,2) r\n", "    on r.date_of_consumption = n.date_of_consumption\n", "    and r.backend_hot_outlet = n.backend_hot_outlet\n", "    and r.dd = n.updated_at_ist\n", "inner join  \n", "    retail.console_outlet o\n", "    on o.id = n.backend_hot_outlet\n", "left join\n", "    retail.console_location r\n", "    on r.id = o.tax_location_id\n", "join\n", "    rpc.item_category_details icd \n", "    on icd.item_id = n.item_id\n", "where\n", "    n.backend_hot_outlet in (5016, 5528, 5914, 5915, 6198, 6329)\n", "    and n.date_ist = current_date - interval '3' day\n", "group by\n", "    1,2,3,4,5,6\n", "    )\n", "    \n", "select \n", "    *\n", "from \n", "    t2_indent t2\n", "union\n", "select\n", "    *\n", "from\n", "    t3_indent\n", "\n", "\"\"\"\n", "indent = read_sql_query(indent_sql, trino)\n", "indent = indent.drop_duplicates()\n", "\n", "indent[\"date_\"] = pd.to_datetime(indent[\"date_\"])\n", "cpc_po[\"date_\"] = pd.to_datetime(cpc_po[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "b730181c-ba92-4d03-ba59-aae24701a40b", "metadata": {}, "outputs": [], "source": ["cpc_po[\"outlet_id\"].replace([np.inf, -np.inf], np.nan, inplace=True)\n", "cpc_po[\"outlet_id\"].fillna(0, inplace=True)\n", "cpc_po[\"outlet_id\"] = cpc_po[\"outlet_id\"].astype(\"int64\")"]}, {"cell_type": "code", "execution_count": null, "id": "b246a942-8269-4560-b3e7-ab61d304e86c", "metadata": {"tags": []}, "outputs": [], "source": ["fillrate_cpc = pd.merge(\n", "    indent,\n", "    cpc_po,\n", "    on=[\"date_\", \"outlet_id\", \"item_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "acee46ee-e628-429a-a104-594da29a1720", "metadata": {}, "outputs": [], "source": ["fillrate_cpc[\"slot_a_grn\"] = fillrate_cpc[\"slot_a_grn\"].fillna(0)\n", "\n", "fillrate_cpc[\"slot_a_grn\"] = np.where(\n", "    fillrate_cpc[\"slot_a_grn\"] > fillrate_cpc[\"indent\"],\n", "    fillrate_cpc[\"indent\"],\n", "    fillrate_cpc[\"slot_a_grn\"],\n", ")\n", "\n", "fillrate_cpc[\"grn_qty\"] = fillrate_cpc[\"grn_qty\"].fillna(0)\n", "\n", "fillrate_cpc[\"grn_calc\"] = np.where(\n", "    fillrate_cpc[\"grn_qty\"] > fillrate_cpc[\"indent\"],\n", "    fillrate_cpc[\"indent\"],\n", "    fillrate_cpc[\"grn_qty\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e45dab8c-25fb-4f7d-8441-5e0067bc258a", "metadata": {}, "outputs": [], "source": ["fillrate_table = fillrate_cpc.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "1446bcf3-ef92-4ea1-ae9d-dbe498ddf03b", "metadata": {}, "outputs": [], "source": ["current_hour = pd.to_datetime(datetime.today() + timedelta(hours=5.5)).hour\n", "current_hour"]}, {"cell_type": "code", "execution_count": null, "id": "27abaca0-dac4-4bfc-b822-c2a32811c871", "metadata": {}, "outputs": [], "source": ["fillrate_table = fillrate_table[fillrate_table[\"date_\"] == today_date]\n", "\n", "fillrate_table = fillrate_table.fillna(0)\n", "\n", "l2_query = f\"\"\"\n", "select DISTINCT item_id, l2 from   rpc.item_category_details where l0_id = 1487\n", "\"\"\"\n", "l2 = read_sql_query(l2_query, trino)\n", "\n", "fillrate_table = pd.merge(\n", "    fillrate_table,\n", "    l2,\n", "    on=[\"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "for_table = fillrate_table.rename(\n", "    columns={\n", "        \"outlet_id\": \"be_outlet_id\",\n", "        \"outlet_name\": \"be_outlet_name\",\n", "        \"date_\": \"date_of_consumption\",\n", "        \"l2\": \"l2_category\",\n", "    }\n", ")\n", "for_table = for_table[for_table[\"l2_category\"].notnull()]\n", "for_table[\"indent\"] = for_table[\"indent\"].astype(float)\n", "for_table = for_table[\n", "    [\n", "        \"date_of_consumption\",\n", "        \"city\",\n", "        \"be_outlet_id\",\n", "        \"be_outlet_name\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"indent\",\n", "        \"price_per_unit\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"slot_a_grn\",\n", "        \"grn_calc\",\n", "        \"l2_category\",\n", "        \"rejected_qty\",\n", "    ]\n", "]\n", "final_table = for_table[\n", "    [\n", "        \"date_of_consumption\",\n", "        \"city\",\n", "        \"be_outlet_id\",\n", "        \"be_outlet_name\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"indent\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"slot_a_grn\",\n", "        \"grn_calc\",\n", "        \"l2_category\",\n", "        \"rejected_qty\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "b2a2bfec-5d12-4597-b244-17b96561b912", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"fnv_backend_fillrates\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"date_of_consumption\",\n", "            \"type\": \"date\",\n", "            \"description\": \"date_of_consumption\",\n", "        },\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"city\"},\n", "        {\n", "            \"name\": \"be_outlet_id\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"be_outlet_id\",\n", "        },\n", "        {\n", "            \"name\": \"be_outlet_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"be_outlet_name\",\n", "        },\n", "        {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"item_id\"},\n", "        {\n", "            \"name\": \"item_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"item_name\",\n", "        },\n", "        {\"name\": \"indent\", \"type\": \"Double\", \"description\": \"indent_qty\"},\n", "        {\n", "            \"name\": \"po_qty\",\n", "            \"type\": \"double\",\n", "            \"description\": \"po_qty\",\n", "        },\n", "        {\"name\": \"grn_qty\", \"type\": \"double\", \"description\": \"grn_qty\"},\n", "        {\n", "            \"name\": \"slot_a_grn\",\n", "            \"type\": \"double\",\n", "            \"description\": \"slot_a_grn\",\n", "        },\n", "        {\n", "            \"name\": \"grn_calc\",\n", "            \"type\": \"double\",\n", "            \"description\": \"grn_calc\",\n", "        },\n", "        {\n", "            \"name\": \"l2_category\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"l2_category\",\n", "        },\n", "        {\n", "            \"name\": \"rejected_qty\",\n", "            \"type\": \"double\",\n", "            \"description\": \"rejected_qty\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\n", "        \"date_of_consumption\",\n", "        \"be_outlet_id\",\n", "        \"item_id\",\n", "    ],\n", "    \"partition_key\": [\"date_of_consumption\"],\n", "    \"incremental_key\": \"date_of_consumption\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"fnv_be_fillrates\",\n", "}\n", "\n", "pb.to_trino(final_table, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "58121129-f2d4-4b7a-b1d2-d983de397fd4", "metadata": {}, "outputs": [], "source": ["fillrate_cpc = \"\"\"\n", "select\n", "    *\n", "from\n", "    supply_etls.fnv_backend_fillrates f\n", "where\n", "    date_of_consumption >= current_date - interval '20' day\"\"\"\n", "fillrate_cpc = read_sql_query(fillrate_cpc, trino)\n", "fillrate_cpc = fillrate_cpc[fillrate_cpc[\"indent\"] > 0]"]}, {"cell_type": "code", "execution_count": null, "id": "15e74767-7257-46b2-8a1d-07e97d423873", "metadata": {}, "outputs": [], "source": ["fillrate_cpc"]}, {"cell_type": "code", "execution_count": null, "id": "7ede5c1c-6fff-421a-9e50-8f67929238b8", "metadata": {}, "outputs": [], "source": ["fillrate_cpc = fillrate_cpc.drop(columns={\"rejected_qty\"})"]}, {"cell_type": "code", "execution_count": null, "id": "f123f722-453d-4e43-8bfb-b715a0438421", "metadata": {}, "outputs": [], "source": ["fillrate_cpc = fillrate_cpc.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "3bc9addc-2429-4d5c-9022-5b04d6d93dec", "metadata": {}, "outputs": [], "source": ["final_sheet = fillrate_cpc.sort_values([\"indent\", \"be_outlet_name\"], ascending=[False, True])"]}, {"cell_type": "code", "execution_count": null, "id": "ce97879b-a1a2-4cd1-8797-2b08f434ae76", "metadata": {}, "outputs": [], "source": ["final_sheet = final_sheet[\n", "    [\n", "        \"date_of_consumption\",\n", "        \"city\",\n", "        \"be_outlet_id\",\n", "        \"be_outlet_name\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"indent\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"slot_a_grn\",\n", "        \"grn_calc\",\n", "        \"l2_category\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "6f6e4fcc-b8b0-4826-88f5-28d013dfbe57", "metadata": {}, "outputs": [], "source": ["final_sheet"]}, {"cell_type": "code", "execution_count": null, "id": "c72375a2-568f-4926-8e17-e2cd045dc6d3", "metadata": {}, "outputs": [], "source": ["flower_data = final_sheet[final_sheet[\"l2_category\"] == \"Flowers & Leaves\"]"]}, {"cell_type": "code", "execution_count": null, "id": "ee750845-7785-4bbd-a150-74aa40ed6f9b", "metadata": {}, "outputs": [], "source": ["to_sheets(flower_data, \"1qnrhduXFlpzN35OoTd-A0jitMGIkPomShzTfxxcZXWE\", \"fills\")"]}, {"cell_type": "code", "execution_count": null, "id": "690573a2-41fd-4565-bcc9-d4402c6e3eff", "metadata": {}, "outputs": [], "source": ["seasonal_skus = \"\"\"select distinct item_id,product_id, product_type as ptype_name\n", "from ( \n", "select cl.name as city,\n", "ipm.product_id,\n", "icd.item_id,\n", "icd.name as item_name,\n", "    cat.id as category_id,\n", "    cat.name as cat_name,\n", "    pt.name as product_type\n", "    from  cms.gr_product_category_mapping pcm\n", "    inner join  cms.gr_category cat on cat.id=pcm.category_id\n", "    inner join  cms.gr_product p on p.id=pcm.product_id\n", "    left join (select id, name from cms.gr_product_type) pt on pt.id = p.type_id\n", "    inner join  rpc.item_product_mapping ipm on ipm.product_id = pcm.product_id\n", "    INNER JOIN  rpc.item_category_details icd ON icd.item_id = ipm.item_id AND icd.l0_id in (1487)\n", "    INNER JOIN  rpc.product_facility_master_assortment pfma ON pfma.item_id = ipm.item_id \n", "                AND pfma.master_assortment_substate_id = 1 AND pfma.active = 1 and pfma.lake_active_record\n", "    inner JOIN  retail.console_outlet co on co.facility_id = pfma.facility_id and co.active = 1 and co.business_type_id = 7 and co.lake_active_record\n", "    left join  retail.console_location cl on cl.id = co.tax_location_id and cl.lake_active_record\n", "    where p.enabled_flag=true\n", "    and cat.enabled_flag=true\n", "        and pcm.lake_active_record\n", "    and cat.lake_active_record\n", "    and p.lake_active_record\n", "    and cat.id in (482)\n", "    and is_primary = false)\"\"\"\n", "seasonal_skus = read_sql_query(seasonal_skus, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "5c9b17da-4540-4a9c-bbfe-d357e73b74d5", "metadata": {}, "outputs": [], "source": ["seasonal_df = pd.merge(final_sheet, seasonal_skus, on=[\"item_id\"], how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "id": "7adcf69c-ee08-4a1f-8388-8db21de618a4", "metadata": {}, "outputs": [], "source": ["to_sheets(seasonal_df, \"1usI8ZgrJGr0XSJTxlcQDKMbwWtMd4Zi0WMQ8MsUEDEU\", \"fill_rate_data\")"]}, {"cell_type": "code", "execution_count": null, "id": "4f6ee600-126f-43cb-8177-1435ae76b6b0", "metadata": {}, "outputs": [], "source": ["seasonal_fillrate = (\n", "    seasonal_df.groupby([\"date_of_consumption\"])\n", "    .agg(\n", "        {\n", "            \"indent\": \"sum\",\n", "            \"po_qty\": \"sum\",\n", "            \"grn_qty\": \"sum\",\n", "            \"slot_a_grn\": \"sum\",\n", "            \"grn_calc\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2bd5a15c-617f-41cc-ba3a-2f2101d9831d", "metadata": {}, "outputs": [], "source": ["to_sheets(\n", "    seasonal_fillrate,\n", "    \"1_d_q9s3BQshd8j7KjD8YvjdFfI9mwBzpLGZeucEuj58\",\n", "    \"seasonal_fillrate\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "58842375-03f7-49d0-a542-f3aaadb9cb3b", "metadata": {}, "outputs": [], "source": ["if current_hour == 10:\n", "    for_table[\"EI\"] = np.where(for_table[\"grn_qty\"] > for_table[\"indent\"], \"EI\", \"I\")\n", "    for_table = for_table[for_table[\"EI\"] == \"EI\"]\n", "    for_table = for_table.drop(columns=[\"EI\"])\n", "\n", "    for_table = for_table.sort_values(by=\"grn_qty\", ascending=False)\n", "\n", "    ptype_map = \"\"\"\n", "    select distinct item_id, product_type from rpc.item_category_details where l0_id = 1487\n", "    \"\"\"\n", "    ptype_map = read_sql_query(ptype_map, trino)\n", "\n", "    for_table = pd.merge(for_table, ptype_map, on=[\"item_id\"], how=\"left\")\n", "\n", "    for_table = for_table[(for_table[\"product_type\"] != \"Onion\")]\n", "    for_table = for_table[(for_table[\"product_type\"] != \"Potato\")]\n", "\n", "    for_table[\"excess\"] = for_table[\"grn_qty\"] - for_table[\"indent\"]\n", "    for_table[\"excess_amount\"] = for_table[\"excess\"] * for_table[\"price_per_unit\"]\n", "\n", "    for_table = for_table.sort_values(by=\"excess_amount\", ascending=False)\n", "\n", "    for_table = for_table[\n", "        [\n", "            \"date_of_consumption\",\n", "            \"be_outlet_id\",\n", "            \"be_outlet_name\",\n", "            \"item_id\",\n", "            \"item_name\",\n", "            \"l2_category\",\n", "            \"product_type\",\n", "            \"indent\",\n", "            \"po_qty\",\n", "            \"grn_qty\",\n", "            \"rejected_qty\",\n", "            \"excess\",\n", "            \"excess_amount\",\n", "        ]\n", "    ]\n", "\n", "    to_sheets(for_table, \"15aFc_04HO9beyWjF_IvrF5_eG4JHc09ZmsqiB9qZgTQ\", \"GRN > INDENT\")\n", "\n", "    summary_df = (\n", "        for_table.groupby([\"be_outlet_id\", \"be_outlet_name\"])\n", "        .agg({\"indent\": \"sum\", \"grn_qty\": \"sum\", \"excess\": \"sum\", \"excess_amount\": \"sum\"})\n", "        .reset_index()\n", "    )\n", "\n", "    summary_df = summary_df.sort_values(by=\"excess_amount\", ascending=False)\n", "\n", "    summary_df[\"indent\"] = summary_df[\"indent\"].astype(int)\n", "    summary_df[\"grn_qty\"] = summary_df[\"grn_qty\"].astype(int)\n", "    summary_df[\"excess\"] = summary_df[\"excess\"].astype(int)\n", "    summary_df[\"excess_amount\"] = summary_df[\"excess_amount\"].astype(int)\n", "\n", "    summary_df = summary_df.rename(\n", "        columns={\n", "            \"be_outlet_id\": \"BE Outlet ID\",\n", "            \"be_outlet_name\": \"BE Outlet Name\",\n", "            \"indent\": \"Indent Qty\",\n", "            \"grn_qty\": \"GRN Qty\",\n", "            \"excess\": \"Excess Qty\",\n", "            \"excess_amount\": \"Excess Amt\",\n", "        }\n", "    )\n", "    summary_df[\"Excess Amt\"] = \"₹\" + summary_df[\"Excess Amt\"].astype(str)\n", "\n", "    slack_channel_sheet = pb.from_sheets(\n", "        \"1AdjnAHCBHCCVX6RZ_xx5rbdsICeTwpJy1KFbee9AsJM\", \"alert_channel\"\n", "    )\n", "    slack_channel = (\n", "        slack_channel_sheet[slack_channel_sheet[\"alert\"] == \"grn_more_indent\"]\n", "        .reset_index()\n", "        .iloc[:, 2][0]\n", "    )\n", "    slack_channel_cluster = list(slack_channel.split(\",\"))\n", "\n", "    today = pd.to_datetime(datetime.now() + timedelta(hours=5.5)).strftime(\"%Y-%m-%d\")\n", "\n", "    if summary_df.shape[0] > 0:\n", "        fig, ax = render_mpl_table(\n", "            summary_df,\n", "            header_columns=0,\n", "        )\n", "        fig.savefig(\"cluster_summary.png\")\n", "\n", "    import pencilbox as pb\n", "\n", "    for i in range(len(slack_channel_cluster)):\n", "        channel = slack_channel_cluster[i]\n", "        if summary_df.shape[0] > 0:\n", "            file_check = \"./cluster_summary.png\"\n", "            filepath = file_check\n", "            channel = channel\n", "            text = (\n", "                f\"Please find the summary for Extra Inwards for {today} \"\n", "                + \"\\n\"\n", "                + f\"<@U041Q7LJVFD>\"\n", "                + \"\\n\"\n", "                + f\"For list of SKUS please refer to this <https://docs.google.com/spreadsheets/d/15aFc_04HO9beyWjF_IvrF5_eG4JHc09ZmsqiB9qZgTQ/edit#gid=1820995921|sheet>\"\n", "            )\n", "        else:\n", "            print(\"<PERSON><PERSON> failed, will check and update\")\n", "    pb.send_slack_message(channel=channel, text=text, files=[filepath])\n", "else:\n", "    pass"]}, {"cell_type": "code", "execution_count": null, "id": "9a2ca0a8-d5e0-48c0-9d82-4d128c380ba7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8cb75da0-504f-4556-8b4c-ab1be73deff3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d6f31f15-f579-486f-bb5c-57f5543eb372", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 5}