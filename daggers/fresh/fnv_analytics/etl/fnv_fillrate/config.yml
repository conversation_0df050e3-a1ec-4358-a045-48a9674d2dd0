alert_configs:
  slack:
  - channel: bl-data-inventory-pvt
  - channel: bl-data-airflow-alerts
dag_name: fnv_fillrate
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: fresh
notebook:
  parameters: null
  retries: 3
owner:
  email: <EMAIL>
  slack_id: U06TVCBNG0P
path: fresh/fnv_analytics/etl/fnv_fillrate
paused: false
pool: fresh_pool
project_name: fnv_analytics
schedule:
  end_date: '2025-09-02T00:00:00'
  interval: 30 0-13,19 * * *
  start_date: '2025-06-15T00:00:00'
schedule_type: fixed
sla: 122 minutes
support_files: []
tags: []
template_name: notebook
version: 27
