alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
catchup: true
dag_name: fnv_item_city_impressions
dag_type: etl
escalation_priority: low
execution_timeout: 600
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: fresh
notebook:
  parameters:
      date_filter: '{{ data_interval_end }}'
owner:
  email: <EMAIL>
  slack_id: U0899MAT7PT
path: fresh/fnv_analytics/etl/fnv_item_city_impressions
paused: false
pool: fresh_pool
project_name: fnv_analytics
schedule:
  end_date: '2025-08-27T00:00:00'
  interval: 15 0 * * *
  start_date: '2025-05-01T00:00:00'
schedule_type: fixed
sla: 352 minutes
support_files: []
tags: []
template_name: notebook
version: 2
