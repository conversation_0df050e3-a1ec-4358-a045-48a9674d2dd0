{"cells": [{"cell_type": "code", "execution_count": null, "id": "8f96d1d7-2db4-4228-9270-5bc865b7ffa6", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import os\n", "import time\n", "import gc\n", "import requests\n", "\n", "import calendar\n", "from datetime import datetime as dt\n", "\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.options.mode.chained_assignment = None  # default='warn'\n", "pd.set_option(\"display.max_columns\", 500)"]}, {"cell_type": "code", "execution_count": null, "id": "8e804b35-1bfc-42bc-b62a-4d2076edfa8f", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "from functools import reduce\n", "import re"]}, {"cell_type": "code", "execution_count": null, "id": "3a8aaa3d-cced-46d4-bb11-0f3351d4ec17", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 1\n", "    for attempt in range(max_tries):\n", "        print(\n", "            f\"Read attempt: {attempt}... \\nRun started at {datetime.now() + timed<PERSON>ta(hours = 5.5)}\"\n", "        )\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "119bc056-8dcd-4299-8189-8c83820e791b", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "da3837ba-5f26-44ea-89b7-ea05ef86f8dc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e12af8ee-face-4cde-94ca-cedf9cd715e0", "metadata": {}, "outputs": [], "source": ["date_filter_dt = dt.fromisoformat(date_filter)\n", "date_filter_str = date_filter_dt.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "markdown", "id": "c1d39231-4478-4dc0-95fb-59d243102e52", "metadata": {}, "source": ["### Item Impressions Query"]}, {"cell_type": "code", "execution_count": null, "id": "5f328eee-d0bc-4846-a465-a10a9e96ac15", "metadata": {}, "outputs": [], "source": ["base_query = f\"\"\"\n", "with \n", "merchant_outlet_mapping as (\n", "SELECT frontend_merchant_id, outlet_id, city_name \n", "FROM dwh.fact_sales_order_item_details oid\n", "JOIN rpc.item_product_mapping ipm ON oid.product_id = ipm.product_id\n", "JOIN rpc.item_category_details icd ON icd.item_id = ipm.item_id AND icd.l0_id = 1487\n", "WHERE order_create_dt_ist >= date'{date_filter_str}' - interval '7' day\n", "GROUP BY 1,2,3\n", "),\n", "\n", "atc_checkout_data AS (\n", "    SELECT a.at_date_ist,\n", "           CASE \n", "                WHEN a.properties__page_name IN ('Homepage', 'feed') THEN 'Homepage'\n", "                WHEN a.properties__page_name = 'listing_widgets' AND a.properties__page_type = 'Category' THEN 'Category grid PLP'\n", "                WHEN a.properties__page_name IN ('Product List', 'Collection', 'listing_widgets') AND a.properties__page_type != 'Category' THEN 'Collections'\n", "                WHEN a.properties__page_name IN ('Landing Pages', 'layout_page', 'tab_layout_page', 'tabs') THEN 'Landing Pages'\n", "                WHEN a.properties__page_name = 'Cart' THEN 'Cart'\n", "                WHEN a.properties__page_name IN ('Search Page', 'search') THEN 'Search Page'\n", "                WHEN a.properties__page_name IN ('empty_search') THEN 'empty_search'\n", "                WHEN a.properties__page_name IN ('home_tabs', 'order_again') AND properties__page_title = 'Order Again' THEN 'Order Again'\n", "                ELSE 'Others'\n", "           END AS properties__page_name,\n", "        mom.city_name,\n", "           a.properties__product_id product_id,\n", "           COUNT(DISTINCT a.device_uuid) AS unique_atc,\n", "           COUNT(a.device_uuid) AS total_atc,\n", "           COUNT(DISTINCT CASE WHEN a.checkout_flag = 1 THEN a.device_uuid END) AS checkout_users, \n", "           SUM(CASE WHEN a.checkout_flag = 1 THEN total_checkout_quantity END) AS total_checkout_atcs, \n", "           SUM(CASE WHEN a.checkout_flag = 1 THEN a.properties__price END) AS checkout_gmv\n", "    FROM consumer_etls.fact_atc_checkout_detail AS a\n", "    JOIN dwh.dim_product AS dp ON CAST(dp.product_id AS VARCHAR) = a.properties__product_id AND is_current AND l0_category_id = 1487\n", "    JOIN merchant_outlet_mapping mom on mom.frontend_merchant_id = a.traits__merchant_id\n", "    WHERE a.at_date_ist = date'{date_filter_str}' - interval '1' day\n", "    GROUP BY 1, 2, 3, 4\n", "),\n", "\n", "impression_sources AS (\n", "    SELECT\n", "        a.at_date_ist AS date_,\n", "        CASE \n", "            WHEN a.properties__page_name IN ('Homepage', 'feed') THEN 'Homepage'\n", "            WHEN a.properties__page_name = 'listing_widgets' AND a.properties__page_type = 'Category' THEN 'Category grid PLP'\n", "            WHEN a.properties__page_name IN ('Product List', 'Collection', 'listing_widgets') AND a.properties__page_type != 'Category' THEN 'Collections'\n", "            WHEN a.properties__page_name IN ('Landing Pages', 'layout_page', 'tab_layout_page', 'tabs') THEN 'Landing Pages'\n", "            WHEN a.properties__page_name = 'Cart' THEN 'Cart'\n", "            WHEN a.properties__page_name IN ('Search Page', 'search') THEN 'Search Page'\n", "            WHEN a.properties__page_name IN ('empty_search') THEN 'empty_search'\n", "            WHEN a.properties__page_name IN ('home_tabs', 'order_again') AND properties__page_title = 'Order Again' THEN 'Order Again'\n", "            ELSE 'Others'\n", "        END AS properties__page_name,\n", "        mom.city_name,\n", "        a.properties__product_id product_id,\n", "        --ipom.item_id,\n", "        COUNT(DISTINCT a.device_uuid) AS unique_impr, \n", "        COUNT(DISTINCT a.device_uuid || a.properties__page_visit_id || a.properties__product_id) AS total_impr, \n", "        COUNT(DISTINCT CASE WHEN a.properties__inventory IS NULL OR properties__inventory = 0 THEN a.device_uuid END) AS unique_oos_impr,\n", "        COUNT(DISTINCT CASE WHEN a.properties__inventory IS NULL OR properties__inventory = 0 THEN a.device_uuid || a.properties__page_visit_id || a.properties__product_id END) AS total_oos_impr\n", "    FROM lake_events.mobile_impression_data a\n", "    JOIN dwh.dim_product as dp ON COALESCE(a.properties__product_id, a.properties__widget_id, a.properties__child_widget_id) = CAST(dp.product_id AS VARCHAR) AND dp.is_current AND dp.l0_category_id = 1487\n", "    --JOIN dwh.dim_item_product_offer_mapping ipom on ipom.product_id = dp.product_id\n", "    JOIN dwh.dim_merchant_outlet_facility_mapping fm ON fm.frontend_merchant_id = a.traits__merchant_id\n", "    JOIN merchant_outlet_mapping mom on mom.frontend_merchant_id = a.traits__merchant_id\n", "        AND fm.is_current = TRUE \n", "        AND fm.is_mapping_enabled = TRUE \n", "        AND fm.is_frontend_merchant_active = TRUE \n", "        AND fm.is_backend_merchant_active = TRUE \n", "        AND fm.is_pos_outlet_active = 1\n", "    WHERE a.at_date_ist  = date'{date_filter_str}' - interval '1' day\n", "    AND name IN ('Product Shown')\n", "    GROUP BY 1, 2, 3, 4\n", ")\n", "\n", "\n", "SELECT \n", "    atc.at_date_ist date_, \n", "    atc.properties__page_name AS atc_page_name, \n", "    atc.city_name,\n", "    atc.product_id,\n", "    im.unique_impr,\n", "    im.unique_oos_impr,\n", "    atc.unique_atc,\n", "    atc.checkout_users,\n", "    atc.checkout_gmv,\n", "    im.total_impr,\n", "    im.total_oos_impr,\n", "    atc.total_checkout_atcs\n", "FROM atc_checkout_data atc\n", "LEFT JOIN impression_sources im ON atc.at_date_ist = im.date_\n", "    AND atc.properties__page_name = im.properties__page_name\n", "    AND atc.city_name = im.city_name\n", "    AND atc.product_id = im.product_id\n", "\"\"\"\n", "\n", "\n", "impressions = read_sql_query(base_query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "12aa47fc-ddc0-4cff-945a-56710f836eb1", "metadata": {}, "outputs": [], "source": ["impressions[\"product_id\"] = impressions[\"product_id\"].astype(int)\n", "impressions[\"date_\"] = pd.to_datetime(impressions[\"date_\"]).dt.date\n", "\n", "kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"fnv_item_city_impressions_data\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date_\", \"type\": \"date\", \"description\": \"date_\"},\n", "        {\"name\": \"atc_page_name\", \"type\": \"varchar\", \"description\": \"atc_page_name\"},\n", "        {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"city_name\"},\n", "        {\"name\": \"product_id\", \"type\": \"bigint\", \"description\": \"product_id\"},\n", "        {\"name\": \"unique_impr\", \"type\": \"bigint\", \"description\": \"unique_impr\"},\n", "        {\"name\": \"unique_oos_impr\", \"type\": \"bigint\", \"description\": \"unique_oos_impr\"},\n", "        {\"name\": \"unique_atc\", \"type\": \"bigint\", \"description\": \"unique_atc\"},\n", "        {\"name\": \"checkout_users\", \"type\": \"bigint\", \"description\": \"checkout_users\"},\n", "        {\"name\": \"checkout_gmv\", \"type\": \"bigint\", \"description\": \"checkout_gmv\"},\n", "        {\"name\": \"total_impr\", \"type\": \"bigint\", \"description\": \"total_impr\"},\n", "        {\"name\": \"total_oos_impr\", \"type\": \"bigint\", \"description\": \"total_oos_impr\"},\n", "        {\"name\": \"total_checkout_atcs\", \"type\": \"bigint\", \"description\": \"total_checkout_atcs\"},\n", "    ],\n", "    \"primary_key\": [\n", "        \"date_\",\n", "        \"city_name\",\n", "        \"product_id\",\n", "        \"atc_page_name\",\n", "    ],  # adjust based on uniqueness logic\n", "    \"partition_key\": [\"date_\"],\n", "    \"incremental_key\": \"date_\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"Impression-level metrics for F&V product pages by city and outlet\",\n", "}\n", "\n", "pb.to_trino(impressions, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}