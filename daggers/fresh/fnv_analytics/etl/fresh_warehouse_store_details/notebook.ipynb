{"cells": [{"cell_type": "code", "execution_count": null, "id": "e3e6144a-e4c6-4e55-bc23-90ce6471cf0b", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib\n", "!pip install pandas openpyxl\n", "!pip install numpy==1.26.4\n", "import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "import datetime\n", "\n", "\n", "from datetime import datetime\n", "from matplotlib import pyplot as plt\n", "\n", "# CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "\n", "# SQL Connection\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "9de424bf-681b-4372-bb94-cb5ad1d9d517", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "\n", "with live_stores as(\n", "select\n", "    fs.frontend_merchant_id,\n", "    fm.pos_outlet_id,\n", "    fm.facility_id\n", "from\n", "    dwh.fact_sales_order_details fs\n", "inner join\n", "    dwh.dim_merchant_outlet_facility_mapping as fm\n", "    on fs.frontend_merchant_id=fm.frontend_merchant_id\n", "    and fm.is_current = true \n", "    and fm.is_mapping_enabled = true \n", "    and fm.is_frontend_merchant_active = true \n", "    and fm.is_backend_merchant_active = true \n", "    and fm.is_pos_outlet_active = 1\n", "    and pos_outlet_id not in (4381)\n", "where\n", "    order_current_status = 'DELIVERED'\n", "    and is_internal_order = False \n", "    and order_create_dt_ist between current_date - interval '7' day and current_date\n", "group by 1,2,3\n", "),\n", "\n", "dates as(\n", "select date as date_ from dwh.dim_date where date >= current_date - interval '1' day and date < current_date group by 1\n", "),\n", "\n", "universe as (\n", "select\n", "    d.date_,\n", "    pfma.facility_id,\n", "    po.outlet_id,\n", "    po.outlet_name as store_name,\n", "    ic.l0,\n", "    ic.l1,\n", "    ic.l2,\n", "    ic.product_type,\n", "    pfma.item_id,\n", "    pfma.master_assortment_substate_id,\n", "    ic.name,\n", "    tm.tag_value,\n", "    case when tm.tag_value = '0' then 'Direct to Store - No Backend' else om.outlet_name end outlet_name\n", "from\n", "    rpc.product_facility_master_assortment pfma\n", "inner join\n", "    rpc.item_category_details ic\n", "    on ic.item_id = pfma.item_id\n", "    and ic.l0_id = 1487\n", "inner join\n", "    po.physical_facility_outlet_mapping po\n", "    on po.facility_id = pfma.facility_id\n", "inner join   \n", "    rpc.item_outlet_tag_mapping tm\n", "    on pfma.item_id = tm.item_id \n", "    and po.outlet_id = tm.outlet_id\n", "    and tm.tag_type_id = 8 \n", "    and tm.active = 1\n", "inner join\n", "    lake_po.physical_facility_outlet_mapping om\n", "    on cast(tm.tag_value as int) =  om.outlet_id    \n", "cross join\n", "    dates d \n", "where\n", "    pfma.active = 1\n", "    and pfma.master_assortment_substate_id in (1,3)\n", "    and pfma.facility_id in (select facility_id from live_stores)\n", ")\n", "\n", "select * from universe \n", "\n", "\n", "\"\"\"\n", "df1 = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "28f223d7-7965-479c-9894-0cd2d5098a3d", "metadata": {}, "outputs": [], "source": ["tea_alert = df1.groupby(\"facility_id\")[\"tag_value\"].nunique()\n", "\n", "# Filter groups where count is greater than 1\n", "tea_alert = tea_alert[tea_alert > 1]\n", "\n", "len(tea_alert)\n", "\n", "if len(tea_alert) > 0:\n", "    channel = \"table-updates-tanishq\"\n", "    text_req = \"\\n <@U05CCTXLBU1> \\nTea Tag is Broken for FnV Backend Same Store is Connected with two Backend\"\n", "    pb.send_slack_message(\n", "        channel=channel,\n", "        text=text_req,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "2edbe4a6-80f0-490d-8d23-74856a6df26b", "metadata": {}, "outputs": [], "source": ["universe = df1.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "e462abc7-4be4-4e84-a266-99750d74be75", "metadata": {}, "outputs": [], "source": ["a = universe.shape"]}, {"cell_type": "code", "execution_count": null, "id": "2e7b4dbe-0d11-434d-9245-ff471e393ceb", "metadata": {}, "outputs": [], "source": ["universe"]}, {"cell_type": "raw", "id": "db8032da-effb-4602-a892-b6394fddf704", "metadata": {}, "source": ["# FR_NR "]}, {"cell_type": "code", "execution_count": null, "id": "7f0a4948-5d30-40e9-bc62-5919ed25d568", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "\n", "with fr_nr as (\n", "SELECT \n", "    DATE(il.pos_timestamp + interval '330' minute) AS date_, \n", "    il.outlet_id,\n", "    rpp.item_id,\n", "    sum(case when reason_id in (12,13) then il.\"delta\" end) as \"not_received_weight_short\",\n", "    sum(case when (reason_id = 4) or (reason_id in (5,17) and il.inventory_update_type_id=11) or (reason_id in (6,7,11)) then il.\"delta\" end) as \"forward_rejection\",\n", "    sum(il.\"delta\") - coalesce(sum(case when reason_id in (12,13) then il.\"delta\" end),0) - coalesce(sum(case when (reason_id = 4) or (reason_id in (5,17) and il.inventory_update_type_id=11) or (reason_id in (6,7,11)) then il.\"delta\" end),0) as dump\n", "FROM \n", "    ims.ims_inventory_log il\n", "INNER JOIN \n", "    (\n", "    SELECT \n", "        ipm.product_id,\n", "        dp.product_name,\n", "        pp.item_id, \n", "        dp.l0_category,\n", "        dp.l1_category,\n", "        dp.l2_category,\n", "        dp.product_type,\n", "        pp.variant_id\n", "    FROM \n", "        rpc.product_product pp \n", "    INNER JOIN\n", "        lake_rpc.item_product_mapping ipm\n", "        on ipm.item_id = pp.item_id \n", "        and ipm.active=1\n", "    inner join \n", "        dwh.dim_product dp\n", "        on ipm.product_id = dp.product_id \n", "        and dp.is_current \n", "        and l0_category_id in (1487) and lower(product_name) not like ('%%frozen%%') and lower(product_name) not like ('%%ferns%%') and lower(product_name) not like ('%%aura%%')\n", "    group by 1,2,3,4,5,6,7,8\n", "    ) rpp \n", "    ON rpp.variant_id = il.variant_id\n", "LEFT JOIN\n", "    ims.ims_bad_inventory_update_log ib\n", "    on ib.inventory_update_id = il.inventory_update_id\n", "WHERE  \n", "    il.insert_ds_ist >= cast(current_date - interval '1' day as varchar)\n", "    and il.insert_ds_ist < cast(current_date as varchar) \n", "    AND il.inventory_update_type_id IN (11,12,13,64,87,88,89,7,33,9,34,63,67)\n", "GROUP BY 1,2,3\n", ")\n", "select * from fr_nr \n", "\n", "\n", "\"\"\"\n", "fr_nr = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "eb2c66f6-fde1-4148-a23d-c777d81b2345", "metadata": {}, "outputs": [], "source": ["# Store Data"]}, {"cell_type": "code", "execution_count": null, "id": "72a6bdc3-9f37-4c53-a59f-4ee331540600", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "with live_stores as(\n", "select\n", "    fs.frontend_merchant_id,\n", "    fm.pos_outlet_id,\n", "    fm.facility_id\n", "from\n", "    dwh.fact_sales_order_details fs\n", "inner join\n", "    dwh.dim_merchant_outlet_facility_mapping as fm\n", "    on fs.frontend_merchant_id=fm.frontend_merchant_id\n", "    and fm.is_current = true \n", "    and fm.is_mapping_enabled = true \n", "    and fm.is_frontend_merchant_active = true \n", "    and fm.is_backend_merchant_active = true \n", "    and fm.is_pos_outlet_active = 1\n", "    and pos_outlet_id not in (4381)\n", "where\n", "    order_current_status = 'DELIVERED'\n", "    and is_internal_order = False \n", "    and order_create_dt_ist between current_date - interval '7' day and current_date\n", "group by 1,2,3\n", "),\n", "\n", "\n", "starting_date as (\n", "select\n", "    fs.frontend_merchant_id,\n", "    fm.pos_outlet_id,\n", "    case \n", "        when EXTRACT(DAY FROM current_date - date(min(order_create_dt_ist))) <= 15 then '< 15 days' \n", "        when EXTRACT(DAY FROM current_date - date(min(order_create_dt_ist))) > 15 and EXTRACT(DAY FROM current_date - date(min(order_create_dt_ist))) <= 45 then '16-45 days' \n", "        when EXTRACT(DAY FROM current_date - date(min(order_create_dt_ist))) > 45 and EXTRACT(DAY FROM current_date - date(min(order_create_dt_ist))) <= 365 then '45-365 days' \n", "        when EXTRACT(DAY FROM current_date - date(min(order_create_dt_ist))) > 365 then '>1 year' \n", "    end as store_age\n", "from\n", "    dwh.fact_sales_order_details fs\n", "inner join\n", "    dwh.dim_merchant_outlet_facility_mapping as fm\n", "    on fs.frontend_merchant_id=fm.frontend_merchant_id\n", "    and fm.is_current = true \n", "    and fm.is_mapping_enabled = true \n", "    and fm.is_frontend_merchant_active = true \n", "    and fm.is_backend_merchant_active = true \n", "    and fm.is_pos_outlet_active = 1\n", "    and pos_outlet_id not in (4381)\n", "where\n", "    order_current_status = 'DELIVERED'\n", "    and is_internal_order = False \n", "    and order_create_dt_ist between current_date - interval '400' day and current_date\n", "    and fs.frontend_merchant_id in (select frontend_merchant_id from live_stores)\n", "    and fs.total_selling_price > 500\n", "group by 1,2\n", "),\n", "\n", "orders as (\n", "select\n", "    date(fs.order_create_dt_ist) date_,\n", "    fs.frontend_merchant_id,\n", "    fm.pos_outlet_id,\n", "    count(distinct order_id) as orders\n", "from\n", "    dwh.fact_sales_order_details fs\n", "inner join\n", "    dwh.dim_merchant_outlet_facility_mapping as fm\n", "    on fs.frontend_merchant_id=fm.frontend_merchant_id\n", "    and fm.is_current = true \n", "    and fm.is_mapping_enabled = true \n", "    and fm.is_frontend_merchant_active = true \n", "    and fm.is_backend_merchant_active = true \n", "    and fm.is_pos_outlet_active = 1\n", "    and pos_outlet_id not in (4381)\n", "where\n", "    order_current_status = 'DELIVERED'\n", "    and is_internal_order = False \n", "    and order_create_dt_ist between current_date - interval '7' day and current_date\n", "    and fs.frontend_merchant_id in (select frontend_merchant_id from live_stores)\n", "group by 1,2,3\n", "),\n", "\n", "avg_orders as (\n", "select\n", "    frontend_merchant_id,\n", "    pos_outlet_id,\n", "    -- cast(avg(orders) as int) opd\n", "    case \n", "        when cast(avg(orders) as int) <= 800 then '<800' \n", "        when cast(avg(orders) as int) > 800 and cast(avg(orders) as int) <= 1200 then '800-1200' \n", "        when cast(avg(orders) as int) > 1200 then '>1200' \n", "    end as opd\n", "    \n", "from\n", "    orders\n", "group by 1,2\n", ")\n", "\n", "select\n", "    l.pos_outlet_id,\n", "    l.facility_id,\n", "    s.store_age,\n", "    ao.opd\n", "from\n", "    live_stores l\n", "left join\n", "    starting_date s\n", "    on l.pos_outlet_id =  s.pos_outlet_id\n", "left join\n", "    avg_orders ao\n", "    on l.pos_outlet_id = ao.pos_outlet_id\n", "\n", "\n", "\"\"\"\n", "store_data = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "c1b002c1-2d6a-49bb-b1a4-cbc4787a5520", "metadata": {}, "outputs": [], "source": ["store_data"]}, {"cell_type": "code", "execution_count": null, "id": "fae1d061-7c91-40ee-89c9-e0485a858d31", "metadata": {}, "outputs": [], "source": ["# Sales"]}, {"cell_type": "code", "execution_count": null, "id": "c0479768-89ed-4729-8145-b85b51a8f9ed", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "with sales as (\n", "SELECT\n", "    date(o.order_create_ts_ist) as date_,\n", "    o.frontend_merchant_id,\n", "    fm.pos_outlet_id,\n", "    ip.item_id,\n", "    sum(o.procured_quantity*ip.multiplier) as qty_sold\n", "FROM\n", "    dwh.fact_sales_order_item_details o\n", "INNER JOIN\n", "    dwh.dim_product dp \n", "    on dp.product_id = o.product_id\n", "--    and dp.l0_category_id = 1487\n", "    and dp.is_current\n", "inner join\n", "    dwh.dim_merchant_outlet_facility_mapping as fm\n", "    on o.frontend_merchant_id = fm.frontend_merchant_id\n", "    and fm.is_current = true \n", "    and fm.is_mapping_enabled = true \n", "    and fm.is_frontend_merchant_active = true \n", "    and fm.is_backend_merchant_active = true \n", "    and fm.is_pos_outlet_active = 1\n", "    and fm.pos_outlet_id not in (4381)\n", "inner join\n", "    dwh.dim_item_product_offer_mapping ip\n", "    on ip.product_id = o.product_id\n", "    and ip.is_current\n", "inner join\n", "    rpc.item_category_details rp\n", "    on rp.item_id = ip.item_id\n", "    and rp.l0_id = 1487 \n", " WHERE\n", "    o.order_current_status = 'DELIVERED'\n", "    and o.order_create_dt_ist >= current_date - interval '1' day\n", "    and o.order_create_dt_ist < current_date\n", "    and o.is_internal_order = False\n", "GROUP BY 1,2,3,4\n", ")\n", "\n", "select * from sales\n", "\n", "\"\"\"\n", "sales_data = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "489b9418-6bb0-4053-a4a6-74942eb1dc5d", "metadata": {}, "outputs": [], "source": ["# Impression Data"]}, {"cell_type": "code", "execution_count": null, "id": "cceaefbf-8889-44ec-aa31-615e79f5a5fd", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "WITH \n", "    sku_base AS (\n", "        SELECT DISTINCT ma.item_id AS item_id, icd.name AS item_name, l2, product_id\n", "        FROM rpc.product_product ma\n", "        JOIN (\n", "            SELECT item_id, MAX(id) AS id\n", "            FROM rpc.product_product\n", "            WHERE active = 1 and approved = 1\n", "            GROUP BY 1\n", "        ) b ON ma.item_id = b.item_id and ma.id = b.id\n", "        JOIN rpc.item_category_details icd ON icd.item_id = ma.item_id\n", "        Join rpc.item_product_mapping dpom on dpom.item_id = icd.item_id \n", "        WHERE l0_id in (1487)\n", "    )\n", "    -- 4822\n", "    \n", "    select\n", "        at_date_ist as date_,\n", "        traits__merchant_id as merchant_id,\n", "        fm.pos_outlet_id,\n", "        s.product_id,\n", "        s.item_id, \n", "        count(distinct ed.user_id) as impressions\n", "    from \n", "        lake_events.mobile_impression_data ed\n", "    join \n", "        sku_base s on coalesce(ed.properties__product_id,ed.properties__child_widget_id)=cast(s.product_id as varchar)\n", "    join\n", "        dwh.dim_merchant_outlet_facility_mapping fm\n", "        on fm.frontend_merchant_id = ed.traits__merchant_id\n", "        and fm.is_current = true \n", "        and fm.is_mapping_enabled = true \n", "        and fm.is_frontend_merchant_active = true \n", "        and fm.is_backend_merchant_active = true \n", "        and fm.is_pos_outlet_active = 1\n", "        and fm.pos_outlet_id not in (4381,6328)\n", "    where \n", "        at_date_ist >= current_date - interval '1' day\n", "        and at_date_ist < current_date \n", "        and name in ('Product Shown')\n", "        and traits__city_name is not null\n", "        and properties__page_name is not null\n", "        and properties__inventory > 0\n", "        and properties__page_name <> '#-NA'\n", "    group by 1,2,3,4,5\n", "\"\"\"\n", "impression_data = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "c064e72e-2e94-4140-87ff-d54602949461", "metadata": {}, "outputs": [], "source": ["# Forecast Data"]}, {"cell_type": "code", "execution_count": null, "id": "bc703100-a9a8-47f6-b966-37f3bf76d773", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "with base_forecast AS ( \n", "SELECT\n", "    date(current_replenishment_ts_ist) date_,\n", "    facility_id,\n", "    item_id,\n", "    name,\n", "    max_qty,\n", "    updated_at_ist,\n", "    ROW_NUMBER() OVER ( PARTITION BY facility_id, item_id, date(current_replenishment_ts_ist) ORDER BY updated_at_ist DESC) AS RowNum\n", "FROM\n", "    ds_etls.demand_forecast_item_ordering_min_max_quantity_log\n", "WHERE   \n", "    current_replenishment_ts_ist >= current_date - interval '2' day\n", "    and current_replenishment_ts_ist < current_date\n", "),\n", "\n", "final_forecast as (\n", "select\n", "    bf.date_,\n", "    bf.facility_id,\n", "    bf.item_id,\n", "    bf.name,\n", "    bf.max_qty as forecast_qty\n", "from\n", "    base_forecast bf\n", "where\n", "    RowNum = 1\n", ")\n", "\n", "select * from final_forecast\n", "\n", "\"\"\"\n", "forecast_data = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "57da8f07-dcff-42d0-a151-05bb645a8207", "metadata": {}, "outputs": [], "source": ["# Complaints Data"]}, {"cell_type": "code", "execution_count": null, "id": "140ec33a-9cfd-4a24-934b-339f3db0455e", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "with complaints as (\n", "SELECT \n", "    *\n", "FROM \n", "    cd_etls.item_wise_reso c\n", "WHERE \n", "    reso_date >= current_date - interval '1' day\n", "    and reso_date < current_date \n", "    AND reason IN ('Items') \n", "    AND l0_category_id = 1487\n", "    and complaint_type in ('COMPLAINT_SMELL_OR_TASTE_ISSUE','COMPLAINT_ROTTEN_ITEM','COMPLAINT_DAMAGED_ITEM','COMPLAINT_EXPIRED_ITEM','COMPLAINT_NEAR_EXPIRY','COMPLAINT_QUALITY_ISSUE','COMPLAINT_PACKAGING_ISSUE')\n", "),\n", "\n", "\n", "final as (\n", "select\n", "    c.*,\n", "    ip.item_id,\n", "    fm.pos_outlet_id as store_outlet_id\n", "from\n", "    complaints c\n", "inner join    \n", "    dwh.dim_merchant_outlet_facility_mapping as fm\n", "    on c.merchant_id = fm.frontend_merchant_id\n", "    and fm.is_current = true \n", "    and fm.is_mapping_enabled = true \n", "    and fm.is_frontend_merchant_active = true \n", "    and fm.is_backend_merchant_active = true \n", "    and fm.is_pos_outlet_active = 1\n", "    and fm.pos_outlet_id not in (4381)\n", "inner join\n", "    dwh.dim_item_product_offer_mapping ip\n", "    on ip.product_id = c.product_id\n", ")\n", "\n", "select\n", "    reso_date,\n", "    merchant_id,\n", "    store_outlet_id,\n", "    item_id,\n", "    count(distinct session_id) complaints_count\n", "from\n", "    final \n", "group by \n", "    1,2,3,4\n", "\"\"\"\n", "complaints_data = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "56cc1e8e-a636-43cf-91ae-3126517aa534", "metadata": {}, "outputs": [], "source": ["# Rating Data"]}, {"cell_type": "code", "execution_count": null, "id": "d79b94d9-dfda-45ee-a80b-3a8f89fe4e2c", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "with base as(\n", "select  \n", "    date_format(date_parse(ri.dt, '%%Y%%m%%d'), '%%Y-%%m-%%d') as date_,\n", "    ri.entity_id,\n", "    o.frontend_merchant_id,\n", "    fm.pos_outlet_id,\n", "    ri.res_id as product_id,\n", "    ri.rating/2 as ratingg\n", "from\n", "    zomato.blinkitreviewsdb.user_rating_inline as ri\n", "inner join\n", "    dwh.dim_product as dp\n", "    on dp.product_id = ri.res_id\n", "    and dp.is_current = True\n", "    and dp.l0_category_id in (1487)\n", "inner join  \n", "    dwh.fact_sales_order_details o\n", "    on o.order_id = ri.entity_id\n", "inner join\n", "    dwh.dim_merchant_outlet_facility_mapping as fm\n", "    on o.frontend_merchant_id = fm.frontend_merchant_id\n", "    and fm.is_current = true \n", "    and fm.is_mapping_enabled = true \n", "    and fm.is_frontend_merchant_active = true \n", "    and fm.is_backend_merchant_active = true \n", "    and fm.is_pos_outlet_active = 1\n", "    and fm.pos_outlet_id not in (4381)\n", "where\n", "    ri.dt >= date_format(current_date - interval '3' day, '%%Y%%m%%d')\n", "    and ri.subject_type='BLINKIT_ITEM'\n", "    and o.order_create_dt_ist > current_date - interval '5' day\n", "group by 1,2,3,4,5,6\n", ")\n", "\n", "select\n", "    date_,\n", "    pos_outlet_id,\n", "    ip.item_id,\n", "    count(case when ratingg > 3 then 1 end) as good_rating,\n", "    count(case when ratingg <= 3 then 1 end) as bad_rating\n", "from\n", "    base b\n", "inner join\n", "    dwh.dim_item_product_offer_mapping ip\n", "    on b.product_id = ip.product_id\n", "group by 1,2,3\n", "\n", "\"\"\"\n", "rating_data = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "cb8b709e-95b7-40c4-a278-2feffd5d4baf", "metadata": {}, "outputs": [], "source": ["# Store STO"]}, {"cell_type": "code", "execution_count": null, "id": "6216a4af-d97f-4e20-8de1-86feb3b454f1", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "with item_base as (\n", "    select distinct ma.item_id, icd.name as item_name, \n", "    Case when l0_id in (1487) then 'FnV'\n", "     WHEN l2_id IN (1425) THEN 'Batter'\n", "            WHEN l2_id IN (31,116,198,1097,1956) THEN 'Breads'\n", "            WHEN l2_id IN (949) THEN 'Curd'\n", "            WHEN l2_id IN (1389,1778) THEN 'Eggs'\n", "            WHEN l2_id IN (63,1367,1369) THEN 'Meats'\n", "            WHEN l2_id IN (1185) THEN 'Milk'\n", "            WHEN l2_id IN (950) THEN 'Paneer'\n", "            WHEN l2_id IN (138,1091,1093) THEN 'Perishable Dairy'\n", "            WHEN l2_id IN (1094) THEN 'Yogurt'\n", "            WHEN l2_id IN (33,97,197,1127) THEN 'Other Perishables' else icd.l2 end as l2 \n", "        from   rpc.product_product ma \n", "        JOIN (\n", "                SELECT item_id, MAX(id) AS id\n", "                FROM  rpc.product_product\n", "                WHERE active = 1 AND approved = 1 \n", "                GROUP BY 1\n", "            ) b ON ma.item_id = b.item_id\n", "            join  rpc.item_category_details icd on icd.item_id = ma.item_id\n", "        where l0_id in (1487)),\n", "        \n", "wh_mapping as (SELECT DISTINCT item_id, tm.outlet_id, tag_value AS be_outlet_id, cb.facility_id AS be_facility_id, cf.name as backend_name\n", "    FROM  rpc.item_outlet_tag_mapping tm\n", "    Join  po.physical_facility_outlet_mapping pfom ON tm.outlet_id=pfom.outlet_id and pfom.active=1 and pfom.ars_active = 1 AND pfom.is_primary =1\n", "    JOIN  retail.console_outlet co ON co.id = tm.outlet_id AND co.active = 1 AND co.business_type_id = 7\n", "    JOIN  retail.console_outlet cb ON cb.id = cast(tag_value as int) AND cb.active = 1\n", "    LEFT JOIN  crates.facility cf on cf.id = cb.facility_id\n", "    WHERE item_id IN (SELECT DISTINCT item_id FROM  rpc.item_category_details WHERE l0_id in (1487))\n", "    AND tm.active = 1 AND tm.tag_type_id = 8),\n", "\n", "po_base as \n", "            (select \n", "                wh.be_outlet_id as backend_outlet_id,\n", "                coalesce(wh.backend_name,'DTS') as backend_name,\n", "                p.outlet_id,\n", "                o.name,\n", "                poi.item_id,\n", "                it.item_name as item_name,\n", "                it.l2 as l2,\n", "                (p.created_at + interval '330' minute) as issue_date,\n", "                Case when extract(hour from max_grn_time) between 0 and 11 then 'SLOT A' else 'SLOT B' end as slot,\n", "                max_grn_time as grn_time, \n", "            sum(grn.quan) as total_grn,\n", "            sum(poi.units_ordered) as po_quant\n", "        from \n", "             po.purchase_order p\n", "        left join  po.po_schedule ps on p.id=ps.po_id_id\n", "        inner join  po.purchase_order_items poi on p.id=poi.po_id\n", "        inner join\n", "             retail.console_outlet o on o.id=p.outlet_id and o.business_type_id = 7 and o.active = 1 \n", "        inner join\n", "             po.purchase_order_status posa on posa.po_id = p.id\n", "        inner join\n", "             po.purchase_order_state posta on posta.id = posa.po_state_id\n", "        LEFT JOIN wh_mapping wh on wh.outlet_id = p.outlet_id and wh.item_id = poi.item_id\n", "        inner join item_base it on it.item_id = poi.item_id \n", "        left join\n", "                (select po_id, item_id, sum(quantity) quan, max(created_at + interval '330' minute) as max_grn_time from  po.po_grn grn where insert_ds_ist >= cast(current_date - interval '7' day as varchar) group by 1,2) grn\n", "            on \n", "                grn.item_id = poi.item_id \n", "                and grn.po_id = p.id\n", "        where \n", "            (posta.name not in ('Cancelled', 'Rejected', 'Cancelled post Creation') or (posta.name in ('Expired'))) \n", "            and issue_date >= current_date - interval '6' day\n", "            and po_type_id <> 11\n", "            and p.vendor_id in (13280)\n", "            and cast(p.outlet_id as varchar) not in (select distinct be_outlet_id from wh_mapping)\n", "            group  by 1,2,3,4,5,6,7,8,9,10),\n", "            \n", "final_to_store as (select \n", "Case when date(grn_time) is not null then date(grn_time) \n", "when extract(hour from issue_Date) between 18 and 23 then date(issue_Date + interval '1' day) \n", "else date(issue_Date) end as scheduled_grn_date, \n", "backend_outlet_id as warehouse_id, \n", "backend_name as warehouse_name,\n", "outlet_id as store_id, \n", "name as store_name,\n", "item_id,\n", "item_name,\n", "slot,\n", "l2,\n", " max(Cast(grn_time as time)) as grn_time_, \n", "sum(po_quant)  as sto_quantity,  \n", "sum(total_grn) as grn_quantity\n", "from po_base\n", "group by 1,2,3,4,5,6,7,8,9),\n", "    \n", "base as \n", "(with\n", "sto_details as\n", "    (select (created_at + interval '330' minute) as created_time,\n", "        sto_id, outlet_id as sender_outlet_id, merchant_outlet_id as receiver_outlet_id,\n", "            case when sto_state = 1 then 'created'\n", "                when sto_state = 2 then 'billed' when sto_state = 3 then 'expired'\n", "                when sto_state = 4 then 'inward' when sto_state = 5 then 'partial-billed'\n", "                    else null end as sto_state\n", "\n", "            from ims.ims_sto_details sd\n", "\n", "                where\n", "                     created_at+ interval '330' minute >= current_date - interval '7' day\n", "    ),\n", "\n", "sto_item_details as\n", "    (select created_time,\n", "        si.sto_id, sender_outlet_id, receiver_outlet_id, si.item_id, reserved_quantity as sto_quantity, sto_state\n", "\n", "            from ims.ims_sto_item si\n", "\n", "                join\n", "                    sto_details sd on sd.sto_id = si.sto_id\n", "                Join item_base ib on ib.item_id = si.item_id\n", "                    where created_at+ interval '330' minute >= current_date - interval '7' day\n", "    ) ,\n", "    \n", "invoice_details as\n", "    (select cast(grofers_order_id as bigint) as sto_id,\n", "        invoice_id, pi.id, pi.outlet_id,\n", "        (pos_timestamp + interval '330' minute) as last_billed_time\n", "\n", "            from pos.pos_invoice pi\n", "\n", "                where insert_ds_ist >= cast((current_date - interval '7' day) as varchar)\n", "                    and invoice_type_id in (5,14,16)\n", "                    and grofers_order_id != ''\n", "                    and grofers_order_id is not null\n", "    ),\n", "\n", "invoice_item_details as\n", "    (select item_id, outlet_id, sto_id, sum(quantity) as billed_quantity\n", "        from pos.pos_invoice_product_details pd\n", "\n", "            join (select distinct item_id, upc from rpc.product_product\n", "                where id in (select max(id) as id from rpc.product_product where active = 1 and approved = 1 group by upc)\n", "                    ) rpp on rpp.upc = pd.upc_id\n", "\n", "            join \n", "                invoice_details id on id.id = pd.invoice_id\n", "\n", "                where insert_ds_ist >= cast((current_date - interval '7' day) as varchar)\n", "\n", "                    group by 1,2,3\n", "    ),\n", "\n", "grn_details as\n", "    (select ii.grn_id, ii.vendor_invoice_id, id.sto_id\n", "        from ims.ims_inward_invoice ii\n", "\n", "            join\n", "                invoice_details id on id.invoice_id = ii.vendor_invoice_id\n", "\n", "                where insert_ds_ist >= cast((current_date - interval '7' day) as varchar)\n", "                    and source_type = 2\n", "    ),\n", "\n", "grn_item_details as\n", "    (select item_id, outlet_id, sto_id, max(created_at + interval '330' minute) as last_inward_time,\n", "        sum(\"delta\") as grn_quantity\n", "        from ims.ims_inventory_stock_details sd\n", "\n", "            join (select distinct item_id, upc from rpc.product_product\n", "                where id in (select max(id) as id from rpc.product_product where active = 1 and approved = 1 group by upc)\n", "                    ) rpp on rpp.upc = sd.upc_id\n", "\n", "            join \n", "                grn_details gd on gd.grn_id = sd.grn_id\n", "\n", "                where insert_ds_ist >= cast((current_date - interval '7' day) as varchar)\n", "\n", "                    group by 1,2,3\n", "    ),\n", "    \n", "final as\n", "    (select   \n", "    sid.created_time,\n", "    CASE \n", "    WHEN EXTRACT(HOUR FROM CAST(sid.created_time AS timestamp)) BETWEEN 18 AND 23 \n", "    THEN DATE(CAST(sid.created_time AS timestamp) + INTERVAL '1' DAY ) \n", "    ELSE DATE(CAST(sid.created_time AS timestamp)) \n", "  END AS scheduled_grn_date,\n", "  CASE \n", "    WHEN EXTRACT(HOUR FROM CAST(sid.created_time AS timestamp)) BETWEEN 18 AND 23\n", "         OR EXTRACT(HOUR FROM CAST(sid.created_time AS timestamp)) BETWEEN 0 AND 4\n", "    THEN 'SLOT A'\n", "    ELSE 'SLOT B'\n", "  END AS slot,\n", "        sid.sto_id, \n", "        sid.sender_outlet_id as warehouse_id,\n", "        co.name as warehouse_name,\n", "        sid.receiver_outlet_id as store_id,\n", "        cb.name as store_name,\n", "        l2,\n", "        sid.item_id, \n", "        ib.item_name as item_name,\n", "        sid.sto_quantity as po_quant, \n", "        last_inward_time as grn_time,\n", "        case when grn_quantity is null then 0 else grn_quantity end as grn_qty\n", "        from sto_item_details sid\n", "                left join\n", "                    invoice_item_details iid on iid.sto_id = sid.sto_id \n", "                        and iid.item_id = sid.item_id\n", "                left join\n", "                    grn_item_details gid on gid.sto_id = sid.sto_id \n", "                        and gid.item_id = sid.item_id\n", "        left join retail.console_outlet co on co.id = sid.sender_outlet_id and co.business_type_id in (19,20,21,1,12)\n", "        left join retail.console_outlet cb on cb.id = sid.receiver_outlet_id and cb.business_type_id in (7)\n", "        inner join item_base ib on ib.item_id = sid.item_id\n", "    )\n", "select * from final\n", "    ),\n", "\n", "    final_sto as (select \n", "    date(scheduled_grn_date) as scheduled_grn_date,\n", "    Cast(warehouse_id as varchar),\n", "    warehouse_name as warehouse_name,\n", "        store_id,\n", "    store_name, \n", "    item_id,\n", "    item_name,\n", "    slot,\n", "    l2,\n", "     max(Cast(grn_time as time)) as grn_time_, \n", "    sum(po_quant) as sto_quantity,\n", "    sum(grn_qty) as grn_quantity\n", "    from base as b\n", "    group by 1,2,3,4,5,6,7,8,9),\n", "\n", "    base_final as (select * from final_to_store union select * from final_sto),\n", "    \n", "    super_final as (select date(scheduled_grn_date) as date_, warehouse_id, warehouse_name, store_id, store_name,slot, item_id, item_name, l2, grn_time_, sto_quantity, grn_quantity from base_final\n", "    order by 1 desc)\n", "    \n", "    select\n", "        date_,\n", "        store_id,\n", "        item_id,\n", "        item_name,\n", "        sum(sto_quantity) as sto_quantity,\n", "        sum(grn_quantity) as grn_quantity\n", "    from\n", "        super_final\n", "    group by 1,2,3,4\n", "\n", "\"\"\"\n", "store_sto_data = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "4160c2b9-1ccf-4b72-b094-ea3ee466fbb9", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "\n", "with base as(select\n", "    cloud_store_id,\n", "    warehouse_id\n", "from\n", "    retail.warehouse_outlet_mapping\n", "where\n", "    active = 1\n", "    and lake_active_record\n", ")    \n", "    \n", "select\n", "    date_of_consumption date_,\n", "    be_outlet_id,\n", "    coalesce(r.warehouse_id,be_outlet_id) final_be_outlet_id,\n", "    be_outlet_name,\n", "    item_id,\n", "    indent,\n", "    po_qty,\n", "    grn_qty,\n", "    rejected_qty\n", "from\n", "    supply_etls.fnv_backend_fillrates f \n", "left join\n", "    base r\n", "    on f.be_outlet_id = r.cloud_store_id \n", "where\n", "    date_of_consumption >= current_date - interval '1' day\n", "\"\"\"\n", "warehouse_inward_data = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "64318bfe-eb5b-4166-a761-ec77a8a9aef3", "metadata": {}, "outputs": [], "source": ["warehouse_inward_data"]}, {"cell_type": "code", "execution_count": null, "id": "f5159589-9ce7-4680-887d-8a5199842a19", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"with sold_quantity as (\n", "SELECT \n", "    DATE(fsoid.cart_checkout_ts_ist) AS date_,\n", "    fsoid.outlet_id,\n", "    icd.item_id,\n", "    avg(fsoid.unit_selling_price * avg_selling_price_ratio) as sp,\n", "    avg(fsoid.unit_weighted_landing_price * avg_selling_price_ratio) as wlp\n", "FROM \n", "    dwh.fact_sales_order_item_details \n", "        AS fsoid \n", "JOIN\n", "    retail.console_outlet c\n", "on\n", "    fsoid.outlet_id = c.id and c.business_type_id in (7) and c.active = 1 and c.lake_active_record\n", "JOIN (SELECT DISTINCT ipr.product_id, \n", "            case when ipr.item_id is null then ipom_0.item_id else ipr.item_id end as item_id,\n", "            case when ipr.item_id is not null then COALESCE(ipom.multiplier,1) else\n", "                COALESCE(ipom_0.multiplier,1) end AS multiplier, \n", "                                COALESCE(ipom_0.avg_selling_price_ratio,1) as avg_selling_price_ratio\n", "                    FROM lake_rpc.item_product_mapping ipr\n", "            left join dwh.dim_item_product_offer_mapping ipom\n", "                    on  ipr.product_id = ipom.product_id \n", "                    and ipr.item_id = ipom.item_id and ipom.is_current\n", "            left join dwh.dim_item_product_offer_mapping ipom_0\n", "                    on ipr.product_id = ipom_0.product_id and ipom_0.is_current) pl ON pl.product_id = fsoid.product_id\n", " JOIN rpc.item_category_details icd ON icd.item_id = pl.item_id and l0_id in (1487)\n", "        where fsoid.order_create_dt_ist >= current_date - interval '8' day \n", "    and fsoid.order_current_status in ('DELIVERED')\n", "    and fsoid.order_type in ('RetailForwardOrder', 'DropShippingForwardOrder')\n", "group by 1,2,3\n", ")\n", "\n", "select * from sold_quantity\n", "\"\"\"\n", "wlp_sp = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "a0d0d61d-8632-4d27-a9e2-0b9d8e844e50", "metadata": {}, "outputs": [], "source": ["wlp_sp_average = (\n", "    wlp_sp.groupby([\"outlet_id\", \"item_id\"]).agg({\"sp\": \"mean\", \"wlp\": \"mean\"}).reset_index()\n", ")\n", "wlp_sp_average = wlp_sp_average.rename(columns={\"sp\": \"avg_sp\", \"wlp\": \"avg_wlp\"})"]}, {"cell_type": "code", "execution_count": null, "id": "7c1bfb3e-7dc0-42f8-a37c-8ca7b1358599", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "with live_stores as(\n", "select\n", "    outlet_id,\n", "    count(distinct order_id) orders\n", "from\n", "    dwh.fact_sales_order_item_details o\n", "where\n", "    o.order_current_status = 'DELIVERED'\n", "    and o.is_internal_order = False \n", "    and o.order_create_dt_ist >= current_date - interval '7' day \n", "group by 1\n", "having \n", "    count(distinct order_id) > 5\n", "),\n", "\n", "fnv_skus AS (\n", "        SELECT DISTINCT ma.item_id AS item_id, icd.name AS item_name, l1\n", "        FROM rpc.product_product ma\n", "        JOIN (\n", "            SELECT item_id, MAX(id) AS id\n", "            FROM rpc.product_product\n", "            WHERE active = 1 and approved = 1\n", "            GROUP BY 1\n", "        ) b ON ma.item_id = b.item_id and ma.id = b.id\n", "        JOIN rpc.item_category_details icd ON icd.item_id = ma.item_id\n", "        WHERE l0_id in (1487)\n", "),\n", "\n", "base_pre AS (\n", "    SELECT * FROM supply_etls.hourly_inventory_snapshots\n", "    WHERE date_ist = current_date - interval '1' day \n", "    and item_id IN (select distinct item_id from fnv_skus)\n", "    -- and substate_id = 1\n", "),\n", "\n", "base AS (\n", "    SELECT * FROM base_pre\n", "    WHERE outlet_id IN (select distinct outlet_id from live_stores)\n", "),\n", "\n", "avail_pre_pre as (\n", "        SELECT DATE(date_ist) AS date_, EXTRACT(hour FROM updated_at_ist) AS hour_, facility_id, outlet_id, item_id, current_inventory\n", "        FROM base\n", "        WHERE EXTRACT(hour FROM updated_at_ist) BETWEEN 1 AND 23\n", "),\n", "\n", "max_hr as (\n", "select\n", "    max(hour_) target_hour\n", "from\n", "    avail_pre_pre\n", "),\n", "\n", "avail_pre as ( \n", "    SELECT date_, hour_, facility_id, outlet_id, item_id, MAX(current_inventory) AS current_inventory\n", "    from avail_pre_pre a\n", "    where hour_ in (select target_hour from max_hr)\n", "    GROUP BY 1,2,3,4,5\n", ")\n", "\n", "select * from avail_pre\n", "\"\"\"\n", "eod_inv = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "af3de866-65b4-4292-9cb6-93286022abfd", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "WITH    perishable_skus AS (\n", "        select\n", "        distinct item_id \n", "        from\n", "        rpc.item_category_details icd \n", "        WHERE l0_id in (1487)\n", "    ),\n", "\n", "active_outlets as (\n", "select distinct outlet_id,facility_id from\n", "    (select date(cart_checkout_ts_ist) as date_, outlet_id, co.facility_id, count(distinct cart_id) as carts\n", "    from dwh.fact_sales_order_item_details od\n", "    left join retail.console_outlet co on co.id = od.outlet_id and co.business_type_id = 7 and co.active = 1\n", "    where procured_quantity >0\n", "    and order_create_dt_ist >= current_date - interval '7' day\n", "    group by 1,2,3\n", "    )\n", "where carts > 10),\n", "\n", "base_pre AS (\n", "        SELECT * FROM supply_etls.hourly_inventory_snapshots\n", "        WHERE date_ist = current_date - interval '1' day \n", "    ),\n", "    \n", "    base AS (\n", "        SELECT * FROM base_pre\n", "        WHERE outlet_id IN (select distinct outlet_id from active_outlets)\n", "    ),\n", "\n", "    avail_pre AS (\n", "        SELECT date_, hour_, facility_id, outlet_id, item_id, MAX(is_available) AS is_available\n", "        FROM (\n", "            SELECT DATE(date_ist) AS date_, EXTRACT(hour FROM updated_at_ist) AS hour_, facility_id, outlet_id, item_id, \n", "            CASE \n", "                WHEN current_inventory > 0 THEN 1 \n", "                ELSE 0  \n", "            END is_available\n", "            FROM base\n", "            WHERE item_id IN (select distinct item_id from perishable_skus)\n", "        )\n", "        WHERE hour_ BETWEEN 6 AND 23\n", "        GROUP BY 1,2,3,4,5\n", "    ),\n", "\n", "\n", "    facility_item_avail AS (\n", "        SELECT a.* , cl.name AS city \n", "        FROM avail_pre a\n", "        JOIN perishable_skus b ON a.item_id = b.item_id\n", "        JOIN retail.console_outlet co ON co.id = a.outlet_id\n", "        LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id\n", "    ),\n", "\n", "    city_hour_weights AS (\n", "        SELECT DISTINCT city, order_hour AS hour_, CAST(weights AS DOUBLE) AS chw\n", "        FROM supply_etls.city_hour_weights a\n", "        WHERE a.updated_at IN (SELECT MAX(updated_at) FROM supply_etls.city_hour_weights where updated_at >= current_date - interval '30' day)\n", "        and a.updated_at >= current_date - interval '30' day\n", "    ),\n", "\n", "    facility_weights_merge AS (\n", "        SELECT city, outlet_id, date_, hour_ , item_id, is_available, (is_available*1.0000*chw) AS fac_avail , (chw) AS tot_weights\n", "        FROM(\n", "            SELECT DISTINCT a.city, a.outlet_id, date_, a.hour_, a.item_id, is_available, hw.chw\n", "            FROM facility_item_avail a \n", "            LEFT JOIN city_hour_weights hw ON a.city = hw.city AND a.hour_ = hw.hour_\n", "        )\n", "    )\n", "\n", "    select \n", "    date_ as date_, item_id, outlet_id, sum(fac_avail) *1.00000/ sum(tot_weights) as weighted_availability, sum(is_available)*1.00/count(is_available) normal_availability\n", "    from facility_weights_merge fm\n", "    group by 1,2,3\n", "\"\"\"\n", "availability = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "73567b91-adae-4820-8a25-031bca438b53", "metadata": {}, "outputs": [], "source": ["# universe\n", "# fr_nr\n", "# store_data\n", "# forecast_data\n", "# sales_data\n", "# complaints_data\n", "# rating_data\n", "# store_sto_data"]}, {"cell_type": "code", "execution_count": null, "id": "953ba547-6bb7-42ef-9d20-a22ba9357c62", "metadata": {}, "outputs": [], "source": ["# warehouse_inward_data"]}, {"cell_type": "code", "execution_count": null, "id": "508908f4-cd9e-457e-9a02-2471f3d2aa2d", "metadata": {}, "outputs": [], "source": ["universe"]}, {"cell_type": "code", "execution_count": null, "id": "bd87d2ab-3da9-4599-8c41-de1e89aa3b27", "metadata": {}, "outputs": [], "source": ["universe[\"facility_id\"] = universe[\"facility_id\"].astype(int)\n", "universe[\"outlet_id\"] = universe[\"outlet_id\"].astype(int)\n", "universe[\"item_id\"] = universe[\"item_id\"].astype(int)\n", "universe[\"date_\"] = pd.to_datetime(universe[\"date_\"])\n", "universe[\"tag_value\"] = universe[\"tag_value\"].astype(int)\n", "\n", "\n", "fr_nr[\"outlet_id\"] = fr_nr[\"outlet_id\"].astype(int)\n", "fr_nr[\"item_id\"] = fr_nr[\"item_id\"].astype(int)\n", "fr_nr[\"date_\"] = pd.to_datetime(fr_nr[\"date_\"])\n", "\n", "impression_data[\"pos_outlet_id\"] = impression_data[\"pos_outlet_id\"].astype(int)\n", "impression_data[\"item_id\"] = impression_data[\"item_id\"].astype(int)\n", "impression_data[\"date_\"] = pd.to_datetime(impression_data[\"date_\"])\n", "\n", "forecast_data[\"facility_id\"] = forecast_data[\"facility_id\"].astype(int)\n", "forecast_data[\"item_id\"] = forecast_data[\"item_id\"].astype(int)\n", "forecast_data[\"date_\"] = pd.to_datetime(forecast_data[\"date_\"])\n", "\n", "sales_data[\"pos_outlet_id\"] = sales_data[\"pos_outlet_id\"].astype(int)\n", "sales_data[\"item_id\"] = sales_data[\"item_id\"].astype(int)\n", "sales_data[\"date_\"] = pd.to_datetime(sales_data[\"date_\"])\n", "\n", "store_data[\"pos_outlet_id\"] = store_data[\"pos_outlet_id\"].astype(int)\n", "\n", "complaints_data[\"store_outlet_id\"] = complaints_data[\"store_outlet_id\"].astype(int)\n", "complaints_data[\"item_id\"] = complaints_data[\"item_id\"].astype(int)\n", "complaints_data[\"reso_date\"] = pd.to_datetime(complaints_data[\"reso_date\"])\n", "\n", "rating_data[\"pos_outlet_id\"] = rating_data[\"pos_outlet_id\"].astype(int)\n", "rating_data[\"item_id\"] = rating_data[\"item_id\"].astype(int)\n", "rating_data[\"date_\"] = pd.to_datetime(rating_data[\"date_\"])\n", "\n", "store_sto_data[\"store_id\"] = store_sto_data[\"store_id\"].astype(int)\n", "store_sto_data[\"item_id\"] = store_sto_data[\"item_id\"].astype(int)\n", "store_sto_data[\"date_\"] = pd.to_datetime(store_sto_data[\"date_\"])\n", "\n", "wlp_sp[\"outlet_id\"] = wlp_sp[\"outlet_id\"].astype(int)\n", "wlp_sp[\"item_id\"] = wlp_sp[\"item_id\"].astype(int)\n", "wlp_sp[\"date_\"] = pd.to_datetime(wlp_sp[\"date_\"])\n", "wlp_sp_average[\"outlet_id\"] = wlp_sp_average[\"outlet_id\"].astype(int)\n", "wlp_sp_average[\"item_id\"] = wlp_sp_average[\"item_id\"].astype(int)\n", "# warehouse_qty_per_store_data['tag_value'] = warehouse_qty_per_store_data['tag_value'].astype(int)\n", "# warehouse_qty_per_store_data['item_id'] = warehouse_qty_per_store_data['item_id'].astype(int)\n", "# warehouse_qty_per_store_data['date_'] = pd.to_datetime(warehouse_qty_per_store_data['date_'])\n", "\n", "warehouse_inward_data[\"final_be_outlet_id\"] = warehouse_inward_data[\"final_be_outlet_id\"].astype(\n", "    int\n", ")\n", "warehouse_inward_data[\"item_id\"] = warehouse_inward_data[\"item_id\"].astype(int)\n", "warehouse_inward_data[\"date_\"] = pd.to_datetime(warehouse_inward_data[\"date_\"])\n", "\n", "# warehouse_sto_data['be_outlet_id'] = warehouse_sto_data['be_outlet_id'].astype(int)\n", "# warehouse_sto_data['item_id'] = warehouse_sto_data['item_id'].astype(int)\n", "# warehouse_sto_data['date_of_run'] = pd.to_datetime(warehouse_sto_data['date_of_run'])"]}, {"cell_type": "code", "execution_count": null, "id": "3580da76-d09e-49b5-a743-1bd076484058", "metadata": {}, "outputs": [], "source": ["final = (\n", "    universe.merge(\n", "        forecast_data,\n", "        left_on=[\"facility_id\", \"item_id\", \"date_\"],\n", "        right_on=[\"facility_id\", \"item_id\", \"date_\"],\n", "        how=\"left\",\n", "    )\n", "    .merge(store_data, left_on=[\"outlet_id\"], right_on=[\"pos_outlet_id\"], how=\"left\")\n", "    .merge(\n", "        fr_nr,\n", "        left_on=[\"outlet_id\", \"item_id\", \"date_\"],\n", "        right_on=[\"outlet_id\", \"item_id\", \"date_\"],\n", "        how=\"left\",\n", "    )\n", "    .merge(\n", "        sales_data,\n", "        left_on=[\"outlet_id\", \"item_id\", \"date_\"],\n", "        right_on=[\"pos_outlet_id\", \"item_id\", \"date_\"],\n", "        how=\"left\",\n", "    )\n", "    .merge(\n", "        complaints_data,\n", "        left_on=[\"outlet_id\", \"item_id\", \"date_\"],\n", "        right_on=[\"store_outlet_id\", \"item_id\", \"reso_date\"],\n", "        how=\"left\",\n", "    )\n", "    .merge(\n", "        rating_data,\n", "        left_on=[\"outlet_id\", \"item_id\", \"date_\"],\n", "        right_on=[\"pos_outlet_id\", \"item_id\", \"date_\"],\n", "        how=\"left\",\n", "    )\n", "    .merge(\n", "        store_sto_data,\n", "        left_on=[\"outlet_id\", \"item_id\", \"date_\"],\n", "        right_on=[\"store_id\", \"item_id\", \"date_\"],\n", "        how=\"left\",\n", "    )\n", "    .merge(\n", "        impression_data,\n", "        left_on=[\"outlet_id\", \"item_id\", \"date_\"],\n", "        right_on=[\"pos_outlet_id\", \"item_id\", \"date_\"],\n", "        how=\"left\",\n", "    )\n", "    .merge(\n", "        warehouse_inward_data,\n", "        left_on=[\"tag_value\", \"item_id\", \"date_\"],\n", "        right_on=[\"final_be_outlet_id\", \"item_id\", \"date_\"],\n", "        how=\"left\",\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5856d2b8-a140-49cb-8365-fc834f7b3def", "metadata": {}, "outputs": [], "source": ["final = pd.merge(final, wlp_sp, on=[\"date_\", \"outlet_id\", \"item_id\"], how=\"left\")\n", "final = pd.merge(final, wlp_sp_average, on=[\"outlet_id\", \"item_id\"], how=\"left\")\n", "final[\"sp\"] = np.where(final[\"sp\"].isna(), final[\"avg_sp\"], final[\"sp\"])\n", "final[\"wlp\"] = np.where(final[\"wlp\"].isna(), final[\"avg_wlp\"], final[\"wlp\"])\n", "final = final.drop(columns=[\"avg_sp\", \"avg_wlp\"])"]}, {"cell_type": "code", "execution_count": null, "id": "9accbbd0-d043-421e-b820-f4ee2b1053c3", "metadata": {}, "outputs": [], "source": ["final.shape"]}, {"cell_type": "code", "execution_count": null, "id": "0724faba-2e01-4fa2-9e9a-50be170dc1cf", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "0f93281c-8f8a-4fa5-a616-1383bbede776", "metadata": {}, "outputs": [], "source": ["final"]}, {"cell_type": "code", "execution_count": null, "id": "fc035cdc-96fa-4e5d-9af7-b6882b0a8985", "metadata": {}, "outputs": [], "source": ["final"]}, {"cell_type": "code", "execution_count": null, "id": "65aa9018-9dc4-47bd-a722-2296478f0a42", "metadata": {}, "outputs": [], "source": ["eod_inv[\"outlet_id\"] = eod_inv[\"outlet_id\"].astype(int)\n", "eod_inv[\"item_id\"] = eod_inv[\"item_id\"].astype(int)\n", "eod_inv[\"current_inventory\"] = eod_inv[\"current_inventory\"].astype(int)\n", "eod_inv[\"date_\"] = pd.to_datetime(eod_inv[\"date_\"])\n", "\n", "final = pd.merge(final, eod_inv, on=[\"date_\", \"outlet_id\", \"item_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "f374a0d3-ab82-4636-bd5d-b2e358dbda23", "metadata": {}, "outputs": [], "source": ["availability[\"outlet_id\"] = availability[\"outlet_id\"].astype(int)\n", "availability[\"item_id\"] = availability[\"item_id\"].astype(int)\n", "availability[\"weighted_availability\"] = availability[\"weighted_availability\"].astype(float)\n", "availability[\"normal_availability\"] = availability[\"normal_availability\"].astype(float)\n", "availability[\"date_\"] = pd.to_datetime(availability[\"date_\"])\n", "\n", "final = pd.merge(final, availability, on=[\"date_\", \"outlet_id\", \"item_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "4076d3df-e57b-40a5-a905-e5c00bfccccf", "metadata": {}, "outputs": [], "source": ["final_1 = final.drop(\n", "    columns=[\n", "        \"name_y\",\n", "        \"pos_outlet_id_x\",\n", "        \"facility_id_y\",\n", "        \"frontend_merchant_id\",\n", "        \"pos_outlet_id_y\",\n", "        \"reso_date\",\n", "        \"merchant_id_x\",\n", "        \"store_outlet_id\",\n", "        \"pos_outlet_id_x\",\n", "        \"store_id\",\n", "        \"item_name\",\n", "        \"merchant_id_y\",\n", "        \"pos_outlet_id_y\",\n", "        \"product_id\",\n", "        \"be_outlet_id\",\n", "        \"final_be_outlet_id\",\n", "        \"be_outlet_name\",\n", "        \"hour_\",\n", "        \"facility_id\",\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1de29545-cb8b-4163-b4ac-d6dff8c8ca7b", "metadata": {}, "outputs": [], "source": ["# final_1 = final_1[final_1['dump'] > 0]"]}, {"cell_type": "code", "execution_count": null, "id": "6fea6417-a2ba-4a59-b114-194d4e281dcd", "metadata": {}, "outputs": [], "source": ["final_1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "7f4b7a90-8e8d-4207-a9c6-4b19b931df63", "metadata": {}, "outputs": [], "source": ["final_1.rename(\n", "    columns={\n", "        \"facility_id_x\": \"facility_id\",\n", "        \"name_x\": \"item_name\",\n", "        \"tag_value\": \"be_outlet_id\",\n", "        \"outlet_name\": \"be_outlet_name\",\n", "        \"indent\": \"be_indent\",\n", "        \"po_qty\": \"be_po_qty\",\n", "        \"grn_qty\": \"be_grn_qty\",\n", "        \"rejected_qty\": \"be_rejected_qty\",\n", "        \"impressions\": \"platform_impression_instock\",\n", "        \"sp\": \"asp\",\n", "        \"current_inventory\": \"eod_inventory\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8f47007d-4837-43c1-a3ab-cd6392df9611", "metadata": {}, "outputs": [], "source": ["final_1"]}, {"cell_type": "code", "execution_count": null, "id": "6c6fe628-0a0e-4ccc-9397-f0e0bfc87261", "metadata": {}, "outputs": [], "source": ["new_column_order = [\n", "    \"date_\",\n", "    \"facility_id\",\n", "    \"outlet_id\",\n", "    \"store_name\",\n", "    \"store_age\",\n", "    \"opd\",\n", "    \"l0\",\n", "    \"l1\",\n", "    \"l2\",\n", "    \"product_type\",\n", "    \"item_id\",\n", "    \"master_assortment_substate_id\",\n", "    \"item_name\",\n", "    \"be_outlet_id\",\n", "    \"be_outlet_name\",\n", "    \"be_indent\",\n", "    \"be_po_qty\",\n", "    \"be_grn_qty\",\n", "    \"be_rejected_qty\",\n", "    \"forecast_qty\",\n", "    \"not_received_weight_short\",\n", "    \"forward_rejection\",\n", "    \"dump\",\n", "    \"qty_sold\",\n", "    \"complaints_count\",\n", "    \"good_rating\",\n", "    \"bad_rating\",\n", "    \"sto_quantity\",\n", "    \"grn_quantity\",\n", "    \"platform_impression_instock\",\n", "    \"asp\",\n", "    \"wlp\",\n", "    \"eod_inventory\",\n", "    \"weighted_availability\",\n", "    \"normal_availability\",\n", "]\n", "\n", "# Reassign the DataFrame with the new column order\n", "final_1 = final_1[new_column_order]"]}, {"cell_type": "code", "execution_count": null, "id": "d015a8aa-4508-488d-bc02-7f480355ccec", "metadata": {}, "outputs": [], "source": ["final_1 = final_1.<PERSON><PERSON>(0)"]}, {"cell_type": "code", "execution_count": null, "id": "95ad7e13-0737-4a02-8d43-9f02e44a3791", "metadata": {}, "outputs": [], "source": ["final_1"]}, {"cell_type": "code", "execution_count": null, "id": "98c06a9f-a791-4690-a113-a80eed16bd6d", "metadata": {}, "outputs": [], "source": ["final_result = final_1.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "b0044a30-9d6c-4049-8439-75ae31951b6d", "metadata": {}, "outputs": [], "source": ["# final_result['qty_by_stores'] = final_result['qty_by_stores'].astype(float)\n", "final_result[\"store_age\"] = final_result[\"store_age\"].astype(str)\n", "final_result[\"opd\"] = final_result[\"opd\"].astype(str)\n", "final_result[\"eod_inventory\"] = final_result[\"eod_inventory\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "48796345-e89d-4807-8508-0348c9b2b0ef", "metadata": {}, "outputs": [], "source": ["final_result.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "0b8effc6-9903-42b0-b63a-1db9a3dcf711", "metadata": {}, "outputs": [], "source": ["final_result.shape"]}, {"cell_type": "code", "execution_count": null, "id": "a7553296-b40f-4ab4-b17a-8a260961aafc", "metadata": {}, "outputs": [], "source": ["b = final_result.shape"]}, {"cell_type": "code", "execution_count": null, "id": "9da4cbaa-f8cd-4f95-914e-b5f68427cedc", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"timestamp(6)\", \"description\": \"date\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility_id\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"outlet_id\"},\n", "    {\"name\": \"store_name\", \"type\": \"varchar\", \"description\": \"store_name\"},\n", "    {\"name\": \"store_age\", \"type\": \"varchar\", \"description\": \"store_age\"},\n", "    {\"name\": \"opd\", \"type\": \"varchar\", \"description\": \"opd\"},\n", "    {\"name\": \"l0\", \"type\": \"varchar\", \"description\": \"l0\"},\n", "    {\"name\": \"l1\", \"type\": \"varchar\", \"description\": \"l1\"},\n", "    {\"name\": \"l2\", \"type\": \"varchar\", \"description\": \"l2\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar\", \"description\": \"product_type\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "    {\n", "        \"name\": \"master_assortment_substate_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"master_assortment_substate_id\",\n", "    },\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"item_name\"},\n", "    {\"name\": \"be_outlet_id\", \"type\": \"integer\", \"description\": \"be_outlet_id\"},\n", "    {\"name\": \"be_outlet_name\", \"type\": \"varchar\", \"description\": \"be_outlet_name\"},\n", "    {\"name\": \"be_indent\", \"type\": \"real\", \"description\": \"be_indent\"},\n", "    {\"name\": \"be_po_qty\", \"type\": \"real\", \"description\": \"be_po_qty\"},\n", "    {\"name\": \"be_grn_qty\", \"type\": \"real\", \"description\": \"be_grn_qty\"},\n", "    {\"name\": \"be_rejected_qty\", \"type\": \"real\", \"description\": \"be_rejected_qty\"},\n", "    {\"name\": \"forecast_qty\", \"type\": \"real\", \"description\": \"forecast_qty\"},\n", "    {\n", "        \"name\": \"not_received_weight_short\",\n", "        \"type\": \"real\",\n", "        \"description\": \"not_received_weight_short\",\n", "    },\n", "    {\"name\": \"forward_rejection\", \"type\": \"real\", \"description\": \"forward_rejection\"},\n", "    {\"name\": \"dump\", \"type\": \"real\", \"description\": \"dump\"},\n", "    {\"name\": \"qty_sold\", \"type\": \"real\", \"description\": \"qty_sold\"},\n", "    {\"name\": \"complaints_count\", \"type\": \"real\", \"description\": \"complaints_count\"},\n", "    {\"name\": \"good_rating\", \"type\": \"real\", \"description\": \"good_rating\"},\n", "    {\"name\": \"bad_rating\", \"type\": \"real\", \"description\": \"bad_rating\"},\n", "    {\"name\": \"sto_quantity\", \"type\": \"real\", \"description\": \"sto_quantity\"},\n", "    {\"name\": \"grn_quantity\", \"type\": \"real\", \"description\": \"grn_quantity\"},\n", "    {\n", "        \"name\": \"platform_impression_instock\",\n", "        \"type\": \"real\",\n", "        \"description\": \"platform_impression_data\",\n", "    },\n", "    {\n", "        \"name\": \"asp\",\n", "        \"type\": \"real\",\n", "        \"description\": \"selling_price\",\n", "    },\n", "    {\n", "        \"name\": \"wlp\",\n", "        \"type\": \"real\",\n", "        \"description\": \"weighted_landing_price\",\n", "    },\n", "    {\n", "        \"name\": \"eod_inventory\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"eod_inventory\",\n", "    },\n", "    {\n", "        \"name\": \"weighted_availability\",\n", "        \"type\": \"real\",\n", "        \"description\": \"weighted_availability\",\n", "    },\n", "    {\n", "        \"name\": \"normal_availability\",\n", "        \"type\": \"real\",\n", "        \"description\": \"normal_availability\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "7b067e05-0a12-4721-917f-6722d8eb4520", "metadata": {}, "outputs": [], "source": ["# # Change this needs to be deleted after this version\n", "# final_result = final_result.iloc[[0]]"]}, {"cell_type": "code", "execution_count": null, "id": "a37d951e-6019-41f8-8a17-93a84456e645", "metadata": {}, "outputs": [], "source": ["final_result"]}, {"cell_type": "code", "execution_count": null, "id": "417d82d6-92fb-46cb-87bf-ace75c73d327", "metadata": {}, "outputs": [], "source": ["import datetime\n", "\n", "current_date_str = datetime.datetime.now().strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "cf11a2f5-10f7-4826-a241-a713297924aa", "metadata": {}, "outputs": [], "source": ["if a[0] == b[0]:\n", "    kwargs = {\n", "        \"schema_name\": \"supply_etls\",\n", "        \"table_name\": \"fresh_warehouse_store_details\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"date_\", \"facility_id\", \"outlet_id\", \"item_id\"],\n", "        \"partition_key\": [\"date_\"],\n", "        \"incremental_key\": \"date_\",\n", "        \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "        \"table_description\": \"Details of FnV items for at store level\",\n", "    }\n", "\n", "    pb.to_trino(final_result, **kwargs)\n", "\n", "    channel = \"table-updates-tanishq\"\n", "    text_req = \"\\n <@U05CCTXLBU1> \\n FnV ETL Fresh Details is updated Today \" + current_date_str\n", "    pb.send_slack_message(\n", "        channel=channel,\n", "        text=text_req,\n", "    )\n", "else:\n", "    channel = \"table-updates-tanishq\"\n", "    text_req = (\n", "        \"\\n <@U05CCTXLBU1> \\n Duplication Row in FnV ETL Fresh Details Table is not updated Today \"\n", "        + current_date_str\n", "    )\n", "    pb.send_slack_message(\n", "        channel=channel,\n", "        text=text_req,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "0452c593-5f06-4fa9-b534-e6454a00d842", "metadata": {}, "outputs": [], "source": ["# select count(*) from blinkit_staging.interim.testing_fnv_table_v1"]}, {"cell_type": "code", "execution_count": null, "id": "90030d2b-5e3c-496f-aeac-16dfcea7815e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}