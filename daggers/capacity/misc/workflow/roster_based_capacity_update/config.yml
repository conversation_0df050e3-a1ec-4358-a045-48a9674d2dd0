alert_configs:
  slack:
  - channel: serviceability-alerts
dag_name: roster_based_capacity_update
dag_type: workflow
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: capacity
notebook:
  parameters:
    end_date: ''
    start_date: ''
owner:
  email: <EMAIL>
  slack_id: U03S57HQ0EP
path: capacity/misc/workflow/roster_based_capacity_update
paused: true
project_name: misc
schedule:
  interval: 32 * * * *
  start_date: '2021-11-17T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- sql_queries.py
- user_functions.py
tags: []
template_name: notebook
version: 16
