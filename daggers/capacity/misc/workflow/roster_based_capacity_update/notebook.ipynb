{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<H3> Import libs</H3>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "from datetime import timedelta, datetime\n", "\n", "\n", "import copy\n", "import requests\n", "import warnings\n", "import sys\n", "\n", "!pip install tabulate\n", "from tabulate import tabulate\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)\n", "from sql_queries import *\n", "from user_functions import *"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<H3> DateTime Variables </H3>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nowist = datetime.now() + <PERSON><PERSON><PERSON>(hours=5, minutes=30)\n", "date_today = (datetime.now() + timedelta(hours=5, minutes=30)).strftime(\"%Y-%m-%d\")\n", "now_hr = nowist.hour\n", "\n", "print(\"running for date\", date_today, \"and\", now_hr, \"th HR\", \"\\n\", \"timestamp\", nowist)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<H3> GSheet Connection & File paths</H3>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["input_sheet_id = \"1nj7G-SwoHtBVjlPH52nSdVrOA0XRsW7nN43YJxchl9A\"\n", "input_sheet_name = \"roster_based_config\"\n", "mail_sheet = \"MailSheet\"\n", "slot_type_distribution = \"slot_type_distribution\"\n", "\n", "\n", "MERCHANT_FILE_UPLOAD_PATH = \"/tmp/roster_based_capacity.csv\"\n", "\n", "MAIL_UPLOAD_FILE_PATH = \"/tmp/[{}]roster_based_capacity.csv\".format(\n", "    str(nowist.strftime(\"%Y-%m-%d %H:%M\"))\n", ")\n", "\n", "detailed_calculation_mail = \"/tmp/[{}]gsp_picker_information.csv\".format(\n", "    str(nowist.strftime(\"%Y-%m-%d %H:%M\"))\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<H3> Database Connection </H3>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["supply_con = pb.get_connection(\"[Replica] Supply Orchestrator\")\n", "redshift_con = pb.get_connection(\"[Warehouse] Redshift\")\n", "logistic_db = pb.get_connection(\"[Replica] Logistic DB\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def send_slack_alert(df, message):\n", "    ascii_table = tabulate(\n", "        df,\n", "        tablefmt=\"pretty\",\n", "        headers=\"keys\",\n", "    )\n", "    message = \"<@U03S57HQ0EP>\" + message + \"\\n\" + ascii_table\n", "    message1 = pb.send_slack_message(channel=\"planning-dag-alerts\", text=message)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<H3> Fetch GSheet Data </H3>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config_df = pb.from_sheets_patch(\n", "    input_sheet_id, input_sheet_name, service_account=\"service_account\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mail_df = pb.from_sheets_patch(\n", "    input_sheet_id, mail_sheet, service_account=\"service_account\"\n", ")\n", "mail_df = mail_df[\"mail_id\"].apply(lambda x: str(x)).to_list()\n", "mail_df.append(\"<EMAIL>\")\n", "mail_df.append(\"<EMAIL>\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<H3> Computing consumable Input </H3>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config_df[[\"merchant_id\", \"smoothening_factor\", \"g_oph\", \"p_oph\"]] = config_df[\n", "    [\"merchant_id\", \"smoothening_factor\", \"g_oph\", \"p_oph\"]\n", "].apply(pd.to_numeric)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_config_df = config_df[(config_df[\"is_active\"] == \"TRUE\")]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picker_df = read_sql(picker_manpower, logistic_db)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gsp_df = read_sql(gsp_manpower, logistic_db)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gsp_df = gsp_df.merge(\n", "    picker_df, on=[\"merchant_id\", \"de_date\", \"hr\"], how=\"left\"\n", ").<PERSON>na(0)\n", "gsp_df[[\"merchant_id\", \"hr\"]] = gsp_df[[\"merchant_id\", \"hr\"]].apply(pd.to_numeric)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gsp_df = gsp_df.loc[gsp_df[\"hr\"] >= now_hr]\n", "gsp_df[\"hour\"] = gsp_df[\"hr\"].apply(lambda x: to_hour(x)).apply(pd.to_datetime)\n", "\n", "if gsp_df.shape[0] != 0:\n", "    gsp_df[\"time_diff\"] = gsp_df.apply(\n", "        lambda row: int(\n", "            -(pd.to_datetime(nowist) - pd.to_datetime(row[\"hour\"])).total_seconds() / 60\n", "        ),\n", "        axis=1,\n", "    )\n", "\n", "    gsp_df = gsp_df.loc[(gsp_df[\"time_diff\"] >= 50) & (gsp_df[\"time_diff\"] <= 180)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combined_capacity_df = gsp_df.merge(active_config_df, on=[\"merchant_id\"], how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def limiting_factor(row):\n", "    minimum = min(row[\"gsp_cap\"], row[\"picker_cap\"])\n", "    if (minimum == row[\"gsp_cap\"]) and (minimum == row[\"picker_cap\"]):\n", "        return \"both\"\n", "    elif minimum == row[\"picker_cap\"]:\n", "        return \"Picker\"\n", "    elif minimum == row[\"gsp_cap\"]:\n", "        return \"GSP\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def manpower_info(df):\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"roster_based_manpower_capacity\",\n", "        \"table_description\": \"roster based capacity update with manpower information considered for the run\",\n", "        \"column_dtypes\": [\n", "            {\n", "                \"name\": \"install_ts_ist\",\n", "                \"type\": \"timestamp\",\n", "                \"description\": \"row install time in flat table\",\n", "            },\n", "            {\n", "                \"name\": \"merchant_id\",\n", "                \"type\": \"bigint\",\n", "                \"description\": \"merchant id\",\n", "            },\n", "            {\n", "                \"name\": \"merchant_name\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"merchant name\",\n", "            },\n", "            {\n", "                \"name\": \"de_date\",\n", "                \"type\": \"timestamp\",\n", "                \"description\": \"capacity update date\",\n", "            },\n", "            {\n", "                \"name\": \"hr\",\n", "                \"type\": \"smallint\",\n", "                \"description\": \"hour considered to increase capacity\",\n", "            },\n", "            {\n", "                \"name\": \"gsp\",\n", "                \"type\": \"bigint\",\n", "                \"description\": \"number of GSP's in system for particular hour\",\n", "            },\n", "            {\n", "                \"name\": \"picker\",\n", "                \"type\": \"bigint\",\n", "                \"description\": \"number of picker's in system for particular hour\",\n", "            },\n", "            {\n", "                \"name\": \"smoothening_factor\",\n", "                \"type\": \"float\",\n", "                \"description\": \"Smoothening factor considered because of SLA breach graded system\",\n", "            },\n", "            {\n", "                \"name\": \"g_oph\",\n", "                \"type\": \"float\",\n", "                \"description\": \"OPH considered for the GSP\",\n", "            },\n", "            {\n", "                \"name\": \"p_oph\",\n", "                \"type\": \"float\",\n", "                \"description\": \"OPH cosnidered for the Picker\",\n", "            },\n", "            {\n", "                \"name\": \"gsp_cap\",\n", "                \"type\": \"float\",\n", "                \"description\": \"individually how many orders GSP can do\",\n", "            },\n", "            {\n", "                \"name\": \"picker_cap\",\n", "                \"type\": \"float\",\n", "                \"description\": \"individually how many orders <PERSON><PERSON> can do\",\n", "            },\n", "            {\n", "                \"name\": \"roster_cap\",\n", "                \"type\": \"numeric\",\n", "                \"description\": \"minimum capacity of what pickers and GSP can do together\",\n", "            },\n", "            {\n", "                \"name\": \"roster_cap_with_sf\",\n", "                \"type\": \"numeric\",\n", "                \"description\": \"roster capacity multiplied with smoothening factor\",\n", "            },\n", "            {\n", "                \"name\": \"limiting_factor\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"reason of shortage manpower\",\n", "            },\n", "        ],\n", "        \"primary_key\": [\"merchant_id\", \"hr\", \"install_ts_ist\"],\n", "        \"sortkey\": [\"merchant_id\", \"install_ts_ist\"],\n", "        \"incremental_key\": \"install_ts_ist\",\n", "        \"load_type\": \"append\",\n", "    }\n", "    if df.shape[0] != 0:\n", "        df[\"install_ts_ist\"] = nowist\n", "        df = df[\n", "            [\n", "                \"install_ts_ist\",\n", "                \"merchant_id\",\n", "                \"merchant_name\",\n", "                \"de_date\",\n", "                \"hr\",\n", "                \"gsp\",\n", "                \"picker\",\n", "                \"smoothening_factor\",\n", "                \"g_oph\",\n", "                \"p_oph\",\n", "                \"gsp_cap\",\n", "                \"picker_cap\",\n", "                \"roster_cap\",\n", "                \"roster_cap_with_sf\",\n", "                \"limiting_factor\",\n", "            ]\n", "        ]\n", "        pb.to_redshift(df, **kwargs)\n", "        pb.send_slack_message(\n", "            channel=\"planning-dag-alerts\",\n", "            text=\"Manpower for roster based capacity update - logs filed\",\n", "        )\n", "        print(\"- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\")\n", "        print(\"Appended successfully\")\n", "        print(\"- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\")\n", "    else:\n", "        print(\"Dataframe empty\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if combined_capacity_df.shape[0] != 0:\n", "    combined_capacity_df[\"gsp_cap\"] = (\n", "        combined_capacity_df[\"g_oph\"] * combined_capacity_df[\"gsp\"]\n", "    )\n", "    combined_capacity_df[\"picker_cap\"] = (\n", "        combined_capacity_df[\"p_oph\"] * combined_capacity_df[\"picker\"]\n", "    )\n", "\n", "    combined_capacity_df[\"roster_cap\"] = combined_capacity_df.apply(\n", "        lambda row: int(\n", "            min(\n", "                (row[\"gsp_cap\"]),\n", "                (row[\"picker_cap\"]),\n", "            )\n", "        ),\n", "        axis=1,\n", "    )\n", "\n", "    combined_capacity_df[\"roster_cap_with_sf\"] = round(\n", "        abs(\n", "            combined_capacity_df[\"roster_cap\"]\n", "            * combined_capacity_df[\"smoothening_factor\"]\n", "        )\n", "    )\n", "    combined_capacity_df[\"limiting_factor\"] = combined_capacity_df.apply(\n", "        lambda row: limiting_factor(row), axis=1\n", "    )\n", "    combined_capacity_df.to_csv(detailed_calculation_mail)\n", "\n", "    combined_capacity_df = combined_capacity_df[\n", "        [\n", "            \"merchant_id\",\n", "            \"merchant_name\",\n", "            \"de_date\",\n", "            \"hr\",\n", "            \"gsp\",\n", "            \"picker\",\n", "            \"smoothening_factor\",\n", "            \"g_oph\",\n", "            \"p_oph\",\n", "            \"gsp_cap\",\n", "            \"picker_cap\",\n", "            \"roster_cap\",\n", "            \"roster_cap_with_sf\",\n", "            \"limiting_factor\",\n", "        ]\n", "    ]\n", "\n", "    combined_capacity_df[\"gsp\"] = combined_capacity_df[\"gsp\"].astype(\"int64\")\n", "    combined_capacity_df[\"picker\"] = combined_capacity_df[\"picker\"].astype(\"int64\")\n", "    # appending the data to redshift table\n", "\n", "    manpower_info(combined_capacity_df)\n", "    collated_capacity_df = combined_capacity_df[\n", "        [\"merchant_id\", \"merchant_name\", \"de_date\", \"hr\", \"roster_cap_with_sf\"]\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_orchestrator = read_sql(\n", "    sco_path_capacity.format(date=str(nowist.date()), now=str(nowist)),\n", "    supply_con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_orchestrator[\"time_diff\"] = df_orchestrator.apply(\n", "    lambda row: int(\n", "        -(\n", "            pd.to_datetime(nowist) - pd.to_datetime(row[\"slot_start_time\"])\n", "        ).total_seconds()\n", "        / 60\n", "    ),\n", "    axis=1,\n", ")\n", "\n", "df_orchestrator = df_orchestrator.loc[\n", "    (df_orchestrator[\"time_diff\"] >= 50) & (df_orchestrator[\"time_diff\"] <= 215)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if (df_orchestrator.shape[0]) != 0:\n", "    # extracting hr info from slot start\n", "    df_orchestrator[\"hr\"] = (\n", "        df_orchestrator[\"slot_start\"].apply(lambda x: x[0:2]).apply(pd.to_numeric)\n", "    )\n", "\n", "    if combined_capacity_df.shape[0] != 0:\n", "        # combining roster_based_capacity_df with sco on merchant_id and hour\n", "        capacity_df = collated_capacity_df.merge(\n", "            df_orchestrator, on=[\"merchant_id\", \"hr\"], how=\"inner\"\n", "        )\n", "\n", "        # getting number of slots for an hour\n", "        slot_count = (\n", "            capacity_df.groupby([\"merchant_id\", \"hr\"]).size().reset_index(name=\"slots\")\n", "        )\n", "\n", "        # pushing number of slots to capacity_df to calculate slot level roster capacity\n", "        capacity_df = capacity_df.merge(\n", "            slot_count, on=[\"merchant_id\", \"hr\"], how=\"inner\"\n", "        )\n", "\n", "        # calculating slot level roster capacity\n", "        capacity_df[\"roster_cap\"] = capacity_df.apply(\n", "            lambda row: round(row[\"roster_cap_with_sf\"] / row[\"slots\"]), axis=1\n", "        )\n", "        capacity_df[\"roster_cap\"] = capacity_df.apply(\n", "            lambda row: max(row[\"roster_cap\"], 6), axis=1\n", "        )\n", "        capacity_df[\"decrease\"] = (\n", "            capacity_df[\"merchant_total_capacity\"] - capacity_df[\"roster_cap\"]\n", "        )\n", "        capacity_df[\"slot_duration\"] = capacity_df[\"slot_duration\"].apply(\"int64\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<H3>DECLARING UPLOAD AND POST LOG DATAFRAMES</H3>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant = []\n", "\n", "df_merchant_upload = pd.DataFrame(\n", "    columns=[\n", "        \"date\",\n", "        \"merchant_id\",\n", "        \"slot_start\",\n", "        \"slot_end\",\n", "        \"merchant_total_capacity\",\n", "        \"merchant_normal_capacity\",\n", "        \"merchant_premium_capacity\",\n", "        \"merchant_heavy_capacity\",\n", "        \"merchant_reverse_pickup_capacity\",\n", "        \"merchant_reschedule_capacity\",\n", "    ]\n", ")\n", "\n", "df_merchant_log = pd.DataFrame(\n", "    columns=[\n", "        \"merchant_id\",\n", "        \"merchant_name\",\n", "        \"date\",\n", "        \"slot\",\n", "        \"merchant_total_capacity\",\n", "        \"merchant_normal_capacity\",\n", "        \"merchant_premium_capacity\",\n", "        \"merchant_heavy_capacity\",\n", "        \"merchant_reverse_pickup_capacity\",\n", "        \"merchant_reschedule_capacity\",\n", "    ]\n", ")\n", "\n", "alert_df = pd.DataFrame(\n", "    columns=[\n", "        \"merchant_id\",\n", "        \"merchant_name_y\",\n", "        \"date\",\n", "        \"slot\",\n", "    ]\n", ")\n", "\n", "zero_cap_alert_df = pd.DataFrame(\n", "    columns=[\n", "        \"merchant_id\",\n", "        \"merchant_name_y\",\n", "        \"date\",\n", "        \"slot\",\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if combined_capacity_df.shape[0] != 0:\n", "    for index, row in capacity_df.iterrows():\n", "        if ((row[\"merchant_id\"], row[\"date\"], row[\"slot\"]) not in merchant) == True:\n", "            if (\n", "                (row[\"decrease\"] < 0)\n", "                & (row[\"merchant_total_capacity\"] > 0)\n", "                & (row[\"slot_duration\"] == 30)\n", "            ):\n", "                # case1 where roster suggested is greater than already in system then we simply have to update asked capacity\n", "                row[\"merchant_total_capacity\"] = int(row[\"roster_cap\"])\n", "                row[\"merchant_premium_capacity\"] = int(\n", "                    round(\n", "                        (\n", "                            row[\"roster_cap\"]\n", "                            * (\n", "                                row[\"merchant_premium_capacity\"]\n", "                                / row[\"merchant_total_capacity\"]\n", "                            )\n", "                        )\n", "                        + 0.49\n", "                    )\n", "                )\n", "                row[\"merchant_reverse_pickup_capacity\"] = int(\n", "                    round(\n", "                        (\n", "                            row[\"roster_cap\"]\n", "                            * (\n", "                                row[\"merchant_reverse_pickup_capacity\"]\n", "                                / row[\"merchant_total_capacity\"]\n", "                            )\n", "                        )\n", "                        + 0.49\n", "                    )\n", "                )\n", "                row[\"merchant_heavy_capacity\"] = int(\n", "                    round(\n", "                        (\n", "                            row[\"roster_cap\"]\n", "                            * (\n", "                                row[\"merchant_heavy_capacity\"]\n", "                                / row[\"merchant_total_capacity\"]\n", "                            )\n", "                        )\n", "                        + 0.49\n", "                    )\n", "                )\n", "                row[\"merchant_reschedule_capacity\"] = int(\n", "                    round(\n", "                        (\n", "                            row[\"roster_cap\"]\n", "                            * (\n", "                                row[\"merchant_reschedule_capacity\"]\n", "                                / row[\"merchant_total_capacity\"]\n", "                            )\n", "                        )\n", "                        + 0.49\n", "                    )\n", "                )\n", "                row[\"merchant_normal_capacity\"] = row[\"merchant_total_capacity\"] - (\n", "                    row[\"merchant_premium_capacity\"]\n", "                    + row[\"merchant_reverse_pickup_capacity\"]\n", "                    + row[\"merchant_heavy_capacity\"]\n", "                    + row[\"merchant_reschedule_capacity\"]\n", "                )\n", "\n", "            elif (\n", "                (row[\"decrease\"] > 0)\n", "                & (row[\"decrease\"] <= row[\"merchant_total_available\"])\n", "                & (row[\"merchant_total_capacity\"] > 0)\n", "                & (row[\"slot_duration\"] == 30)\n", "            ):\n", "                # case2 where roster suggested is lesser than already in system\n", "                # but it is less than equal to available capacity in system then we can directly change capacities in the system\n", "                row[\"merchant_total_capacity\"] = int(round(row[\"roster_cap\"]))\n", "                row[\"merchant_premium_capacity\"] = int(\n", "                    round(\n", "                        (\n", "                            row[\"merchant_total_capacity\"]\n", "                            * (\n", "                                row[\"merchant_premium_capacity\"]\n", "                                / row[\"merchant_total_capacity\"]\n", "                            )\n", "                        )\n", "                        + 0.49\n", "                    )\n", "                )\n", "                row[\"merchant_reverse_pickup_capacity\"] = int(\n", "                    round(\n", "                        (\n", "                            row[\"merchant_total_capacity\"]\n", "                            * (\n", "                                row[\"merchant_reverse_pickup_capacity\"]\n", "                                / row[\"merchant_total_capacity\"]\n", "                            )\n", "                        )\n", "                        + 0.49\n", "                    )\n", "                )\n", "                row[\"merchant_heavy_capacity\"] = int(\n", "                    round(\n", "                        (\n", "                            row[\"merchant_total_capacity\"]\n", "                            * (\n", "                                row[\"merchant_heavy_capacity\"]\n", "                                / row[\"merchant_total_capacity\"]\n", "                            )\n", "                        )\n", "                        + 0.49\n", "                    )\n", "                )\n", "                row[\"merchant_reschedule_capacity\"] = int(\n", "                    round(\n", "                        (\n", "                            row[\"merchant_total_capacity\"]\n", "                            * (\n", "                                row[\"merchant_reschedule_capacity\"]\n", "                                / row[\"merchant_total_capacity\"]\n", "                            )\n", "                        )\n", "                        + 0.49\n", "                    )\n", "                )\n", "                row[\"merchant_normal_capacity\"] = row[\"merchant_total_capacity\"] - (\n", "                    row[\"merchant_premium_capacity\"]\n", "                    + row[\"merchant_reverse_pickup_capacity\"]\n", "                    + row[\"merchant_heavy_capacity\"]\n", "                    + row[\"merchant_reschedule_capacity\"]\n", "                )\n", "\n", "            elif (row[\"roster_cap\"] != 0) & (row[\"merchant_total_capacity\"] == 0):\n", "                # case3 where roster suggested some capacity but in system capacity is already zero\n", "                # need to be put it as same as it is in system because shift timings are stretched to do in store activities\n", "                zero_cap_alert_df = zero_cap_alert_df.append(\n", "                    row[[\"merchant_id\", \"merchant_name_y\", \"date\", \"slot\"]],\n", "                    ignore_index=True,\n", "                    sort=False,\n", "                )\n", "            elif (\n", "                (row[\"decrease\"] > 0)\n", "                & (row[\"decrease\"] > row[\"merchant_total_available\"])\n", "                & (row[\"merchant_total_capacity\"] > 0)\n", "            ):\n", "                # case4 where roster suggested is lesser than already in system\n", "                # but it is greater than available capacity in system then we need to raise an alert that we are doing more than roster wise manpower suggestion\n", "                if row[\"slot_duration\"] == 30:\n", "                    alert_df = alert_df.append(\n", "                        row[\n", "                            [\n", "                                \"merchant_id\",\n", "                                \"merchant_name_y\",\n", "                                \"date\",\n", "                                \"slot\",\n", "                            ]\n", "                        ],\n", "                        ignore_index=True,\n", "                        sort=False,\n", "                    )\n", "\n", "            df_merchant_upload = df_merchant_upload.append(\n", "                row[\n", "                    [\n", "                        \"date\",\n", "                        \"merchant_id\",\n", "                        \"slot_start\",\n", "                        \"slot_end\",\n", "                        \"merchant_total_capacity\",\n", "                        \"merchant_normal_capacity\",\n", "                        \"merchant_premium_capacity\",\n", "                        \"merchant_heavy_capacity\",\n", "                        \"merchant_reverse_pickup_capacity\",\n", "                        \"merchant_reschedule_capacity\",\n", "                    ]\n", "                ],\n", "                ignore_index=True,\n", "                sort=False,\n", "            )\n", "\n", "            df_merchant_log = df_merchant_log.append(\n", "                row[\n", "                    [\n", "                        \"merchant_id\",\n", "                        \"merchant_name_y\",\n", "                        \"date\",\n", "                        \"slot\",\n", "                        \"merchant_total_capacity\",\n", "                        \"merchant_normal_capacity\",\n", "                        \"merchant_premium_capacity\",\n", "                        \"merchant_heavy_capacity\",\n", "                        \"merchant_reverse_pickup_capacity\",\n", "                        \"merchant_reschedule_capacity\",\n", "                    ]\n", "                ],\n", "                ignore_index=True,\n", "                sort=False,\n", "            )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if alert_df.shape[0] != 0:\n", "    alert_df = alert_df.drop_duplicates()\n", "    alert_df = alert_df.rename(columns={\"merchant_name_y\": \"merchant_name\"})\n", "    send_slack_alert(\n", "        alert_df,\n", "        \"Roster based capacity update - more orders recieved than the roster capability\",\n", "    )\n", "if zero_cap_alert_df.shape[0] != 0:\n", "    zero_cap_alert_df = zero_cap_alert_df.drop_duplicates()\n", "    zero_cap_alert_df = zero_cap_alert_df.rename(\n", "        columns={\"merchant_name_y\": \"merchant_name\"}\n", "    )\n", "    send_slack_alert(\n", "        zero_cap_alert_df,\n", "        \"Roster based capacity update - found zero cap for the slot in system but manpower is available\",\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<H3>UPLOADING CAPACITIES</H3>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_merchant_upload = rename(df_merchant_upload, \"merchant\")\n", "if df_merchant_upload.shape[0] != 0:\n", "    if now_hr >= 6:\n", "        df_merchant_upload.to_csv(MERCHANT_FILE_UPLOAD_PATH, index=False)\n", "        merch_status = api_hit(\"Merchant\", MERCHANT_FILE_UPLOAD_PATH)\n", "        print(merch_status)\n", "else:\n", "    print(\"No merchant capacity change\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<H3>LOGGING PRE CAPACITIES</H3>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if combined_capacity_df.shape[0] != 0:\n", "    capacity_df = capacity_df.rename(columns={\"merchant_name_y\": \"merchant_name\"})\n", "    df_merchant_pre = capacity_df[\n", "        [\n", "            \"merchant_id\",\n", "            \"merchant_name\",\n", "            \"date\",\n", "            \"slot\",\n", "            \"merchant_total_capacity\",\n", "            \"merchant_normal_capacity\",\n", "            \"merchant_premium_capacity\",\n", "            \"merchant_reverse_pickup_capacity\",\n", "            \"merchant_heavy_capacity\",\n", "            \"merchant_reschedule_capacity\",\n", "        ]\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if combined_capacity_df.shape[0] != 0:\n", "    df_merchant_pre_allocation = log_data_modifier(df_merchant_pre)\n", "    df_merchant_pre_log, merchant_pre_log_kwargs = pre_post_allocation_logger(\n", "        \"merchant\", \"pre\", df_merchant_pre_allocation\n", "    )\n", "\n", "    if df_merchant_pre_allocation.shape[0] != 0:\n", "        pb.to_redshift(df_merchant_pre_log, **merchant_pre_log_kwargs)\n", "        print(\"-------------------------------------\")\n", "        print(\"Pre merchant capacities are logged\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<H3>LOGGING POST CAPACITIES</H3>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if combined_capacity_df.shape[0] != 0:\n", "    df_merchant_log = df_merchant_log.drop(columns={\"merchant_name\"})\n", "    df_merchant_log = df_merchant_log.rename(\n", "        columns={\"merchant_name_y\": \"merchant_name\"}\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if combined_capacity_df.shape[0] != 0:\n", "    df_merchant_post_allocation = log_data_modifier(df_merchant_log)\n", "    df_merchant_post_log, merchant_post_log_kwargs = pre_post_allocation_logger(\n", "        \"merchant\", \"post\", df_merchant_post_allocation\n", "    )\n", "\n", "    if df_merchant_post_allocation.shape[0] != 0:\n", "        pb.to_redshift(df_merchant_post_log, **merchant_post_log_kwargs)\n", "        print(\"-------------------------------------\")\n", "        print(\"Post merchant capacities are logged\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<H3>MAIL TRIGGER</H3>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_mail = pd.DataFrame(\n", "    columns=[\n", "        \"merchant_id\",\n", "        \"merchant_name\",\n", "        \"date\",\n", "        \"slot\",\n", "        \"pre_merchant_total_cap\",\n", "        \"merchant_total_available\",\n", "        \"post_merchant_total_cap\",\n", "        \"roster_cap\",\n", "        \"decrease\",\n", "        \"change\",\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df_merchant_upload.shape[0] != 0:\n", "    df_pre_post_cap = capacity_df.merge(\n", "        df_merchant_upload,\n", "        on=[\"merchant_id\", \"slot_start\", \"slot_end\", \"date\"],\n", "        how=\"inner\",\n", "    )\n", "    df_pre_post_cap[\"change\"] = \"\"\n", "    df_pre_post_cap = df_pre_post_cap[\n", "        [\n", "            \"merchant_id\",\n", "            \"merchant_name\",\n", "            \"date\",\n", "            \"slot\",\n", "            \"total_capacity\",\n", "            \"merchant_total_available\",\n", "            \"merchant_total_capacity\",\n", "            \"roster_cap\",\n", "            \"decrease\",\n", "            \"change\",\n", "            \"slot_duration\",\n", "        ]\n", "    ]\n", "    df_pre_post_cap = df_pre_post_cap.rename(\n", "        columns={\n", "            \"total_capacity\": \"post_merchant_total_cap\",\n", "            \"merchant_total_capacity\": \"pre_merchant_total_cap\",\n", "        }\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df_merchant_upload.shape[0] != 0:\n", "    for index, row in df_pre_post_cap.iterrows():\n", "        if (row[\"decrease\"] > 0) & (row[\"decrease\"] > row[\"merchant_total_available\"]):\n", "            row[\"change\"] = \"no change\"\n", "        elif (row[\"decrease\"] > 0) & (\n", "            (row[\"decrease\"])\n", "            <= (row[\"merchant_total_available\"]) & (row[\"slot_duration\"] == 30)\n", "        ):\n", "            row[\"change\"] = \"decrease\"\n", "        elif (row[\"decrease\"] < 0) & (row[\"slot_duration\"] == 30):\n", "            row[\"change\"] = \"increase\"\n", "        else:\n", "            row[\"change\"] = \"--\"\n", "        df_mail = df_mail.append(\n", "            row[\n", "                [\n", "                    \"merchant_id\",\n", "                    \"merchant_name\",\n", "                    \"date\",\n", "                    \"slot\",\n", "                    \"pre_merchant_total_cap\",\n", "                    \"merchant_total_available\",\n", "                    \"post_merchant_total_cap\",\n", "                    \"roster_cap\",\n", "                    \"decrease\",\n", "                    \"change\",\n", "                ]\n", "            ],\n", "            ignore_index=True,\n", "            sort=False,\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df_merchant_upload.shape[0] != 0:\n", "    if now_hr >= 6:\n", "        df_mail[\"net_change\"] = df_mail[\"decrease\"] * (-1)\n", "        df_mail = df_mail.drop(columns={\"decrease\"})\n", "        df_mail[\"merchant_name\"] = df_mail[\"merchant_name\"].str[14:]\n", "        df_mail.to_csv(MAIL_UPLOAD_FILE_PATH, index=False)\n", "        df_mail[\"status\"] = merch_status\n", "        subject = (\n", "            \"[Update]\"\n", "            + str(nowist.strftime(\"%Y-%m-%d\"))\n", "            + \"|| Roster based - Capacity Adjusted\"\n", "        )\n", "        html_content = \"\"\"<p>Please find below the list of merchants and slots where capacity was adjusted based on roster</p>\"\"\"\n", "\n", "        send_mail(\n", "            mail_df,\n", "            subject,\n", "            html_content,\n", "            MAIL_UPLOAD_FILE_PATH,\n", "            detailed_calculation_mail,\n", "        )\n", "\n", "        message = pb.send_slack_message(\n", "            channel=\"planning-dag-alerts\",\n", "            text=\"Roster based- capacity was adjusted for \"\n", "            + str(df_merchant_upload[\"merchant_id\"].nunique())\n", "            + \" merchants\",\n", "        )"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}