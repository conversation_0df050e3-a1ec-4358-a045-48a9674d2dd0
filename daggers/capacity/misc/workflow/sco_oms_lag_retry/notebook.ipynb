{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<H2>IMPORT LIBRARIES</H2>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import requests\n", "from datetime import timedelta\n", "import json\n", "import uuid"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<H2>VARIABLE DECLARATION</H2>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["LAG_LIMIT = 50\n", "SCO_OMS_ORDER_RETRY_ALERT_FILE_PATH = \"/tmp/order_retries.csv\"\n", "SCO_OMS_SUBORDER_RETRY_ALERT_FILE_PATH = \"/tmp/suborder_retries.csv\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["<H2>CONNECTIONS AND QUERIES</H2>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<H5>DATABASE CONNECTIONS</H5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["oms_connection = pb.get_connection(\"oms_bifrost\")\n", "orchestrator_connection = pb.get_connection(\"supply_orchestrator\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<H5>QUERIES</H5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Note: Here order_ids may repeat as 1 order can have multiple suborders. Will handle this while retrying these orders.\n", "oms_query = \"\"\"\n", "SELECT DISTINCT\n", "    oo.id AS order_id, os.id AS suborder_id\n", "    \n", "FROM oms_suborder AS os\n", "INNER JOIN oms_order AS oo\n", "    ON os.order_id = oo.id\n", "    AND os.type = 'RetailSuborder'\n", "    AND os.current_status not in ('CANCELLED', 'REPROCURED')\n", "WHERE (oo.install_ts BETWEEN now() - INTERVAL '1 HOUR'\n", "        AND now() - INTERVAL '15 MIN')\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_orders_created_in_sco(order_ids):\n", "    query = \"\"\"\n", "    SELECT DISTINCT\n", "    external_order_id AS order_id\n", "    FROM sco_order\n", "    WHERE external_order_id in {}\n", "    \"\"\".format(\n", "        order_ids\n", "    )\n", "    results = pd.read_sql_query(sql=query, con=orchestrator_connection)\n", "    return results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_suborders_created_in_sco(suborder_ids):\n", "    query = \"\"\"\n", "    SELECT DISTINCT\n", "    external_suborder_id AS suborder_id\n", "    FROM sco_suborder\n", "    WHERE external_suborder_id in {}\n", "    \"\"\".format(\n", "        suborder_ids\n", "    )\n", "    results = pd.read_sql_query(sql=query, con=orchestrator_connection)\n", "    return results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get order received event timestamp from bifrost using order_id\n", "def get_order_event_details(order_id):\n", "    query = \"\"\"\n", "    SELECT *\n", "    FROM oms_order_event\n", "    WHERE order_id={}\n", "    and event_type_key='state_change'\n", "    and extra::json->>'to'='RECEIVED'\n", "    LIMIT 1;\n", "    \"\"\".format(\n", "        order_id\n", "    )\n", "    results = pd.read_sql_query(sql=query, con=oms_connection)\n", "    return results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get order received event from bifrost using order_id\n", "def get_order_rb_outbound_messages(order_id, start_time, end_time):\n", "    query = \"\"\"\n", "    SELECT *\n", "    FROM rb_outbound_messages\n", "    WHERE payload LIKE '%%{}%%'\n", "      AND created_at >= '{}'\n", "      AND created_at < '{}'\n", "      AND topic in ('bifrost-order_lifecycle', 'bifrost-internal_order_lifecycle')\n", "      AND event_name = 'receive_order';\n", "    \"\"\".format(\n", "        order_id, start_time, end_time\n", "    )\n", "    results = pd.read_sql_query(sql=query, con=oms_connection)\n", "    return results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get suborder received event timestamp from bifrost using suborder_id\n", "def get_suborder_event_details(suborder_id):\n", "    query = \"\"\"\n", "    SELECT *\n", "    FROM oms_suborder_event\n", "    WHERE suborder_id={}\n", "    and event_type_key='state_change'\n", "    and extra::json->>'to'='RECEIVED'\n", "    LIMIT 1;\n", "    \"\"\".format(\n", "        suborder_id\n", "    )\n", "    results = pd.read_sql_query(sql=query, con=oms_connection)\n", "    return results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get suborder received event from bifrost using suborder_id\n", "def get_suborder_rb_outbound_messages(suborder_id, start_time, end_time):\n", "    query = \"\"\"\n", "    SELECT *\n", "    FROM rb_outbound_messages\n", "    WHERE payload LIKE '%%{}%%'\n", "      AND created_at >= '{}'\n", "      AND created_at < '{}'\n", "      AND topic = 'bifrost-suborder_lifecycle'\n", "      AND event_name = 'receive_order';\n", "    \"\"\".format(\n", "        suborder_id, start_time, end_time\n", "    )\n", "    results = pd.read_sql_query(sql=query, con=oms_connection)\n", "    return results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# API Calls for creating orders/suborders in SCO"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_url = \"http://supply-orchestrator-planning.prod-sgp-k8s.grofer.io/tracker/api/order_events\"\n", "suborder_url = \"http://supply-orchestrator-planning.prod-sgp-k8s.grofer.io/tracker/api/suborder_events\"\n", "headers = {\n", "    \"Content-Type\": \"application/json\",\n", "    \"Postman-Token\": \"1c1c686f-0afd-477e-af3a-ed2abaf9c78c\",\n", "    \"cache-control\": \"no-cache\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_order_in_capacity(payload):\n", "    payload = json.dumps(payload)\n", "    response = requests.request(\"POST\", order_url, headers=headers, data=payload)\n", "    return response.text.encode(\"utf8\"), response.status_code"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_suborder_in_capacity(payload):\n", "    payload = json.dumps(payload)\n", "    response = requests.request(\"POST\", suborder_url, headers=headers, data=payload)\n", "    return response.text.encode(\"utf8\"), response.status_code"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create orders in SCO\n", "def create_orders_in_sco(orders_to_be_created):\n", "    order_retry_status = []\n", "    for order_id in orders_to_be_created:\n", "        order_event_details = get_order_event_details(order_id)\n", "        start_time = order_event_details[\"install_ts\"][0] - <PERSON><PERSON><PERSON>(minutes=1)\n", "        end_time = start_time + <PERSON><PERSON><PERSON>(minutes=5)\n", "        rb_order_details = get_order_rb_outbound_messages(\n", "            order_id, start_time, end_time\n", "        )\n", "        try:\n", "            payload = json.loads(rb_order_details[\"payload\"][0])\n", "            response, status_code = create_order_in_capacity(payload)\n", "            order_retry_status.append(\n", "                {\n", "                    \"order_id\": order_id,\n", "                    \"response\": response,\n", "                    \"status_code\": status_code,\n", "                }\n", "            )\n", "        except Exception as e:\n", "            order_retry_status.append(\n", "                {\"order_id\": order_id, \"response\": response, \"exception\": e}\n", "            )\n", "    return order_retry_status"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create suborders in SCO\n", "def create_suborders_in_sco(suborders_to_be_created):\n", "    suborder_retry_status = []\n", "    for suborder_id in suborders_to_be_created:\n", "        suborder_event_details = get_suborder_event_details(suborder_id)\n", "        start_time = suborder_event_details[\"install_ts\"][0] - <PERSON><PERSON><PERSON>(minutes=1)\n", "        end_time = start_time + <PERSON><PERSON><PERSON>(minutes=5)\n", "        rb_suborder_details = get_suborder_rb_outbound_messages(\n", "            suborder_id, start_time, end_time\n", "        )\n", "        try:\n", "            payload = json.loads(rb_suborder_details[\"payload\"][0])\n", "            response, status_code = create_suborder_in_capacity(payload)\n", "            suborder_retry_status.append(\n", "                {\n", "                    \"suborder_id\": suborder_id,\n", "                    \"response\": response,\n", "                    \"status_code\": status_code,\n", "                }\n", "            )\n", "        except Exception as e:\n", "            suborder_retry_status.append(\n", "                {\"suborder_id\": suborder_id, \"response\": response, \"exception\": e}\n", "            )\n", "    return suborder_retry_status"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_oms = pd.read_sql_query(oms_query, oms_connection)\n", "distinct_oms_orders = set(df_oms.order_id)\n", "distinct_oms_suborders = set(df_oms.suborder_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_sco_orders = get_orders_created_in_sco(tuple(distinct_oms_orders))\n", "df_sco_suborders = get_suborders_created_in_sco(tuple(distinct_oms_suborders))\n", "\n", "distinct_sco_orders = set(df_sco_orders.order_id)\n", "distinct_sco_suborders = set(df_sco_suborders.suborder_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_to_be_created = list(distinct_oms_orders - distinct_sco_orders)\n", "suborders_to_be_created = list(distinct_oms_suborders - distinct_sco_suborders)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if len(orders_to_be_created) >= LAG_LIMIT:\n", "    df_sco_oms_order_lag = pd.DataFrame(create_orders_in_sco(orders_to_be_created))\n", "    df_sco_oms_suborder_lag = pd.DataFrame(\n", "        create_suborders_in_sco(suborders_to_be_created)\n", "    )\n", "\n", "    df_sco_oms_order_lag.to_csv(SCO_OMS_ORDER_RETRY_ALERT_FILE_PATH)\n", "    df_sco_oms_suborder_lag.to_csv(SCO_OMS_SUBORDER_RETRY_ALERT_FILE_PATH)\n", "\n", "    from_email = \"<EMAIL>\"\n", "    to_email = [\"<EMAIL>\"]\n", "\n", "    subject = \"[ALERT] sco-oms retries\"\n", "    html_content = \"\"\"<b>{} orders & {} suborders </b> were retried<br/>Please find the attached files with retry status\"\"\".format(\n", "        len(orders_to_be_created), len(suborders_to_be_created)\n", "    )\n", "\n", "    pb.send_email(\n", "        from_email,\n", "        to_email,\n", "        subject,\n", "        html_content,\n", "        files=[\n", "            SCO_OMS_ORDER_RETRY_ALERT_FILE_PATH,\n", "            SCO_OMS_SUBORDER_RETRY_ALERT_FILE_PATH,\n", "        ],\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}