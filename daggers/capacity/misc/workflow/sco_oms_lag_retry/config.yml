alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: sco_oms_lag_retry
dag_type: workflow
escalation_priority: low
executor:
  config:
    cpu:
      limit: 0.5
      request: 0.3
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 2G
      request: 500M
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: capacity
notebook:
  parameters:
    end_date: ''
    start_date: ''
owner:
  email: <EMAIL>
  slack_id: U03S5HZ8CJX
path: capacity/misc/workflow/sco_oms_lag_retry
paused: true
project_name: misc
schedule:
  interval: '*/15 2-16 * * *'
  start_date: '2022-08-25T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
