alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: sku_unsold_30mins_store
dag_type: workflow
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: capacity
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03S57HQ0EP
path: capacity/misc/workflow/sku_unsold_30mins_store
paused: true
project_name: misc
schedule:
  interval: 30 4,7 * * *
  start_date: '2022-04-17T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
