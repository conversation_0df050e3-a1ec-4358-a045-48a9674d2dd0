{"cells": [{"cell_type": "code", "execution_count": null, "id": "40dfdf86-7760-431d-abf3-7dc8bb460c88", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "from datetime import datetime, timedelta\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "id": "793f76cd-c46a-4531-ab51-060b5acb63a8", "metadata": {}, "outputs": [], "source": ["red_con = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "94425425-a1fb-4a87-8b25-2da2705ef621", "metadata": {}, "outputs": [], "source": ["merchants_live = \"\"\"SELECT DISTINCT virtual_merchant_id\n", "FROM lake_cms.gr_virtual_to_real_merchant_mapping\n", "WHERE real_merchant_id=30787\n", "  AND enabled_flag=TRUE\n", "  AND priority_order=2\"\"\"\n", "\n", "merchant_df = pd.read_sql_query(merchants_live, con=red_con)"]}, {"cell_type": "code", "execution_count": null, "id": "********-08cf-40ba-85ef-249c474d7d8c", "metadata": {}, "outputs": [], "source": ["merchant_list = tuple(merchant_df[\"virtual_merchant_id\"].tolist())"]}, {"cell_type": "code", "execution_count": null, "id": "b21aecdc-4ba2-4dc7-8ce9-ed81602a9e31", "metadata": {}, "outputs": [], "source": ["live_inventory = pb.from_sheets(\n", "    \"1zLIVMo64TojYJhDroF8d51U-h2haxNEG9QwXEo3Ed-g\",\n", "    \"live_inventory\",\n", "    service_account=\"service_account\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "259fd8db-898a-4098-a86b-edee992a7071", "metadata": {}, "outputs": [], "source": ["live_inventory = live_inventory.rename(columns={\"CI \": \"inventory\", \"PID\": \"pid\"})"]}, {"cell_type": "code", "execution_count": null, "id": "0f1a89a9-1c62-4282-8333-595fc15314b4", "metadata": {}, "outputs": [], "source": ["live_inventory[\"inventory\"] = live_inventory[\"inventory\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "********-feb3-408e-ae05-617c70174d59", "metadata": {}, "outputs": [], "source": ["inventory_df = live_inventory.loc[live_inventory[\"inventory\"] != 0]"]}, {"cell_type": "code", "execution_count": null, "id": "ae5bcbc8-1a47-401d-b5c6-8580da08c6ef", "metadata": {}, "outputs": [], "source": ["pid_list = tuple(inventory_df[\"pid\"].tolist())"]}, {"cell_type": "code", "execution_count": null, "id": "b8a1f830-3467-4cab-b72b-5eb783a30afc", "metadata": {}, "outputs": [], "source": ["sql_data = \"\"\"create temporary table ps as \n", "(select\n", "    at_date_ist date_,\n", "    device_uuid,\n", "    session_uuid,\n", "    properties__widget_id pid,\n", "    properties__widget_title pname,\n", "    min(at_ist) time_\n", "from\n", "    spectrum.mobile_impression_data \n", "where \n", "    at_date_ist between current_date-5 and current_date\n", "    and name = 'Product Shown' \n", "    and  properties__widget_title <> 'Product Widget'\n", "    and properties__widget_impression_count=1\n", "    and properties__widget_id in {pids}\n", "    AND traits__merchant_id IN {merchants}\n", "    -- and properties__page_name in ('Search Page', 'Search List', 'Product List', 'Container')\n", "group by 1,2,3,4,5);\n", "\n", "\n", "create temporary table pa as (\n", "select \n", "    at_date_ist date_,\n", "    device_uuid,\n", "    session_uuid,\n", "    properties__product_id  pid\n", "from\n", "    spectrum.mobile_event_data \n", "where \n", "    at_date_ist between current_date-5 and current_date\n", "    and name = 'Product Added'\n", "        and properties__product_id in {pids}\n", "    AND traits__merchant_id IN {merchants}\n", "    -- and properties__page_name = 'Product Page'\n", "group by 1,2,3,4\n", ");\n", "\n", "with sp as (\n", "select\n", "    ps.date_,\n", "    ps.pid,\n", "    ps.pname,\n", "    count(distinct ps.session_uuid) product_shown,\n", "    count(distinct pa.session_uuid) product_added,\n", "    (product_added*100.00)/product_shown as user_per\n", "from\n", "ps left join pa\n", "        on ps.date_ = pa.date_ \n", "        and ps.session_uuid = pa.session_uuid \n", "        and ps.device_uuid = pa.device_uuid \n", "        and ps.pid=pa.pid\n", "group by 1,2,3\n", ")\n", "\n", "select * from sp\n", "order by 1,2,3\"\"\".format(\n", "    pids=pid_list, merchants=merchant_list\n", ")\n", "\n", "sku_level_funnel = pd.read_sql(sql_data, con=red_con)"]}, {"cell_type": "code", "execution_count": null, "id": "b7534480-5a9f-45f9-8f18-cf7b9e56c63a", "metadata": {}, "outputs": [], "source": ["sku_level_df = sku_level_funnel.merge(inventory_df, on=[\"pid\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "c47869df-df78-4930-9265-4b9be6f420f7", "metadata": {}, "outputs": [], "source": ["sku_level_df.drop(\n", "    columns=[\"Item ID\", \"item_name\", \"item_type\", \"inventory\", \"Open STO\", \"Sum\"],\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "173897c4-68a7-48ef-aae7-60735d18d5bd", "metadata": {}, "outputs": [], "source": ["sku_level_df = sku_level_df[\n", "    [\"date_\", \"pid\", \"pname\", \"l0\", \"product_shown\", \"product_added\", \"user_per\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "2728f1ad-1418-46d8-8e2c-2277cd01d72f", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    sku_level_df,\n", "    \"1zLIVMo64TojYJhDroF8d51U-h2haxNEG9QwXEo3Ed-g\",\n", "    \"products_on_app_raw\",\n", "    service_account=\"service_account\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "bc0afa2d-a61d-4642-b917-8cbd8899de4d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "toc-autonumbering": false, "toc-showcode": true, "toc-showtags": true, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 5}