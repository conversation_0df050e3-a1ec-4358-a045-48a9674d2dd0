{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# CORRECTION OF RPC 'WEIGTH_IN_GMS' COLUMN\n", "\n", "# IMPORTING LIBRARIES\n", "\n", "import pencilbox as pb\n", "import os\n", "import pandas as pd\n", "from datetime import datetime, timedelta\n", "\n", "# VARIABLE DECALRATION\n", "\n", "# DATE TIME VARIABLES\n", "NOW_IST = datetime.now() + <PERSON><PERSON><PERSON>(minutes=30, hours=5)\n", "RUN_DATE = str(NOW_IST.date())\n", "RUN_HOUR = str((NOW_IST + timedelta(hours=1)).hour)\n", "\n", "# FILE PATH VARIABLES\n", "BASE_PATH = \"data\"\n", "OUTPUT_FOLDERS = [\"rpc_weight_correction\"]\n", "\n", "ITEMS_WITH_PARSABLE_UOM_AND_DISCREPANT_WEIGHT_FILE_PATH = (\n", "    \"data/{}/rpc_weight_correction/items_with_parsable_uom_and_discrepant_weight_{}.csv\"\n", ").format(RUN_DATE, RUN_HOUR)\n", "ITEMS_WITH_PARSABLE_UOM_AND_MISSING_WEIGHT_FILE_PATH = (\n", "    \"data/{}/rpc_weight_correction/items_with_parsable_uom_and_missing_weight_{}.csv\"\n", ").format(RUN_DATE, RUN_HOUR)\n", "\n", "# GOOGLE SHEET ACCESS VARIABLES\n", "SHEET_ID = \"1pFI8R6pAfASQQGtGhUr06CiVXCOJ8HE-VyzIb3dAj3w\"\n", "DISCREPANT_WEIGHT_SHEET = \"Discrepant Weight\"\n", "MISSING_WEIGHT_SHEET = \"Missing Weight\"\n", "\n", "\n", "# USER DEFINED FUNCTIONS\n", "\n", "# --------------------------------------------------------------------------------------------\n", "def setup_output_data_folder(RUN_DATE):\n", "\n", "    for folder in OUTPUT_FOLDERS:\n", "        path = os.path.join(BASE_PATH, RUN_DATE, folder)\n", "        os.makedirs(path, exist_ok=True)\n", "\n", "\n", "# --------------------------------------------------------------------------------------------\n", "def find_character_in_string(string, character):\n", "    character_index = [i for i, letter in enumerate(string) if letter == character]\n", "\n", "    if character_index == []:\n", "        character_index = [-1]\n", "\n", "    return character_index\n", "\n", "\n", "# --------------------------------------------------------------------------------------------\n", "def negative_cliper(x):\n", "\n", "    if x < 0:\n", "        x = 0\n", "\n", "    return x\n", "\n", "\n", "# --------------------------------------------------------------------------------------------\n", "\n", "# AS MENTIONED IN THE NAME OF THE FUNCTION, THIS FUNCTION IS USED TO CLEAN UOM'S :\n", "# - WHICH CONTAIN EITHER WEIGHT DATA INSIDE BRACKETS\n", "# - OR HAS MENTIONED NO. OF UNITS IN BRACKETS\n", "# - OR HAS MULTIPLE BRACKETS\n", "\n", "# FOR EXAMPLE:\n", "# - 1 unit(250 gm - 300gm) + 2x20ml (2 units)\n", "\n", "\n", "def extracting_data_from_uom_with_brackets(x):\n", "\n", "    left_side_bracket = \"(\"\n", "    right_side_bracket = \")\"\n", "    separator = \"-\"\n", "    string_to_check = \"unit\"\n", "\n", "    x = str(x)\n", "\n", "    instances = len(find_character_in_string(x, left_side_bracket))\n", "\n", "    for i in range(instances):\n", "\n", "        left_side_bracket_position = find_character_in_string(x, left_side_bracket)\n", "        right_side_bracket_position = find_character_in_string(x, right_side_bracket)\n", "        separator_position = find_character_in_string(x, separator)\n", "\n", "        if left_side_bracket_position[i] >= 0 & right_side_bracket_position[i] >= 0:\n", "\n", "            if (\n", "                string_to_check\n", "                in x[left_side_bracket_position[i] + 1 : right_side_bracket_position[i]]\n", "            ):\n", "                x = (\n", "                    x[: left_side_bracket_position[i]]\n", "                    + x[right_side_bracket_position[i] + 1 :]\n", "                )\n", "\n", "            else:\n", "\n", "                if separator in x:\n", "                    x = (\n", "                        x[: left_side_bracket_position[i] + 1]\n", "                        + x[separator_position[i] :]\n", "                    )\n", "\n", "                if i == instances - 1:\n", "                    x = x.replace(\"units\", \"\")\n", "                    x = x.replace(\"unit\", \"\")\n", "                    x = x.replace(\" \", \"\")\n", "\n", "                    for j in range(instances):\n", "\n", "                        left_side_bracket_position = find_character_in_string(\n", "                            x, left_side_bracket\n", "                        )\n", "                        x = (\n", "                            x[: negative_cliper(left_side_bracket_position[j] - 1)]\n", "                            + x[left_side_bracket_position[j] :]\n", "                        )\n", "\n", "        else:\n", "\n", "            if separator in x:\n", "                x = x.split(\"-\")[1]\n", "\n", "    x = x.replace(\"-\", \"\")\n", "\n", "    return x\n", "\n", "\n", "# --------------------------------------------------------------------------------------------\n", "def striping_left_part_of_string_before_delimeter(x):\n", "\n", "    x = str(x)\n", "\n", "    delimeter = \"=\"\n", "    if delimeter in x:\n", "        x = x.split(\"=\")[1]\n", "\n", "    return x\n", "\n", "\n", "# --------------------------------------------------------------------------------------------\n", "def parser(x):\n", "\n", "    string_base = [\n", "        \"ml\",\n", "        \"ltr\",\n", "        \"lt\",\n", "        \"lr\",\n", "        \"l\",\n", "        \"kg\",\n", "        \"gm\",\n", "        \"g\",\n", "        \"pcs\",\n", "        \"free\",\n", "        \"extra\",\n", "        \"`\",\n", "        \"x\",\n", "    ]\n", "    upper_base = set([\"ltr\", \"lt\", \"lr\", \"l\", \"kg\"])\n", "    lower_base = set([\"ml\", \"gm\", \"g\"])\n", "    removal = set([\"pcs\", \"free\", \"extra\", \"`\"])\n", "    multiplier = set([\"x\"])\n", "\n", "    x = str(x).lower()\n", "\n", "    for i in range(len(string_base)):\n", "        uom = string_base[i]\n", "        if uom in x:\n", "            if uom in upper_base:\n", "                x = x.replace(uom, \"*1000\")\n", "            if uom in lower_base:\n", "                x = x.replace(uom, \"\")\n", "            if uom in removal:\n", "                x = x.replace(uom, \"\")\n", "            if uom in multiplier:\n", "                x = x.replace(uom, \"*\")\n", "\n", "    return x\n", "\n", "\n", "# --------------------------------------------------------------------------------------------\n", "def evaluation(x):\n", "\n", "    try:\n", "        x = eval(str(x))\n", "    except:\n", "        x = None\n", "    return x\n", "\n", "\n", "# --------------------------------------------------------------------------------------------\n", "def discrepancy_threhold_calculator(x):\n", "\n", "    x = int(x)\n", "\n", "    if x <= 200:\n", "        threshold = 100\n", "    elif x <= 500:\n", "        threshold = 50\n", "    elif x <= 1000:\n", "        threshold = 37.5\n", "    elif x <= 2000:\n", "        threshold = 25\n", "    elif x <= 4000:\n", "        threshold = 12.5\n", "    else:\n", "        threshold = 10\n", "\n", "    return threshold\n", "\n", "\n", "# --------------------------------------------------------------------------------------------\n", "\n", "# DATABASE CONNECTION  & DATA FETCHING QUERY\n", "\n", "rpc_connection = pb.get_connection(\"retail\")\n", "\n", "item_query = \"\"\"\n", "SELECT \n", "rpp.item_id,\n", "rpp.name AS item_name,\n", "rpp.variant_uom_text,\n", "rpp.weight_in_gm,\n", "rpp.updated_at AS last_updated_at\n", "\n", "FROM\n", "    (\n", "    SELECT \n", "        item_id,\n", "        max(id) AS id\n", "        \n", "    FROM rpc.product_product\n", "    WHERE active = 1\n", "        AND approved = 1\n", "    GROUP BY 1\n", "    ) AS rpp_id\n", "INNER JOIN rpc.product_product as rpp\n", "    ON rpp_id.id = rpp.id\n", "    AND LOWER(rpp.name) NOT LIKE '%%deleted%%'\n", "    AND (rpp.updated_at > addtime(timestamp(current_date), '-7 5:30:00')\n", "         AND rpp.updated_at <= addtime(timestamp(current_date), '- 5:30:00'))\n", "GROUP BY 1,2,3,4,5\n", "\"\"\"\n", "print(\"**************************\")\n", "print(NOW_IST)\n", "print(\"**************************\")\n", "setup_output_data_folder(RUN_DATE)\n", "\n", "df_weights_rpc = pd.read_sql_query(item_query, rpc_connection)\n", "\n", "\n", "# CLEANING RPC WEIGHTS DATA THROUGH ITEM UOM TEXT\n", "\n", "# DATA CLEANING AND MANIPULATION\n", "\n", "df_weights_rpc[\"calculated_weight\"] = df_weights_rpc[\"variant_uom_text\"].apply(\n", "    extracting_data_from_uom_with_brackets\n", ")\n", "df_weights_rpc[\"calculated_weight\"] = df_weights_rpc[\"calculated_weight\"].apply(\n", "    striping_left_part_of_string_before_delimeter\n", ")\n", "df_weights_rpc[\"calculated_weight\"] = df_weights_rpc[\"calculated_weight\"].apply(parser)\n", "df_weights_rpc[\"calculated_weight\"] = df_weights_rpc[\"calculated_weight\"].apply(\n", "    evaluation\n", ")\n", "\n", "df_weights_rpc = df_weights_rpc[\n", "    [\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"variant_uom_text\",\n", "        \"weight_in_gm\",\n", "        \"calculated_weight\",\n", "        \"last_updated_at\",\n", "    ]\n", "]\n", "\n", "\n", "# GETTING ITEMS WITH PARSABLE UOM\n", "\n", "# GETTING ITEMS WITH PARSABLE UOM AND DISCREPANT WEIGHT\n", "\n", "df_parsable_uom_items = df_weights_rpc[\n", "    df_weights_rpc[\"calculated_weight\"].isna() == False\n", "].reset_index(drop=True)\n", "\n", "df_parsable_uom_items_with_weight = df_parsable_uom_items[\n", "    df_parsable_uom_items[\"weight_in_gm\"].isna() == False\n", "].reset_index(drop=True)\n", "\n", "df_parsable_uom_items_with_weight[\"discrepancy\"] = (\n", "    abs(\n", "        (\n", "            df_parsable_uom_items_with_weight[\"calculated_weight\"]\n", "            - df_parsable_uom_items_with_weight[\"weight_in_gm\"]\n", "        )\n", "        / df_parsable_uom_items_with_weight[\"calculated_weight\"]\n", "    )\n", "    * 100\n", ")\n", "df_parsable_uom_items_with_weight[\n", "    \"discrepancy_threhold\"\n", "] = df_parsable_uom_items_with_weight[\"calculated_weight\"].apply(\n", "    discrepancy_threhold_calculator\n", ")\n", "df_parsable_uom_items_with_weight.loc[\n", "    (\n", "        df_parsable_uom_items_with_weight[\"discrepancy\"]\n", "        > df_parsable_uom_items_with_weight[\"discrepancy_threhold\"]\n", "    ),\n", "    \"discrepancy_flag\",\n", "] = True\n", "df_parsable_uom_items_and_discrepant_weight = df_parsable_uom_items_with_weight[\n", "    df_parsable_uom_items_with_weight[\"discrepancy_flag\"].isna() == False\n", "].reset_index(drop=True)\n", "\n", "# THIS FILTER IS USED TO REMOVE OUT<PERSON><PERSON><PERSON> CASES RESULTING DUE TO WRONG UOM\n", "df_parsable_uom_items_and_discrepant_weight = (\n", "    df_parsable_uom_items_and_discrepant_weight[\n", "        df_parsable_uom_items_and_discrepant_weight[\"calculated_weight\"] >= 10\n", "    ].reset_index(drop=True)\n", ")\n", "\n", "df_parsable_uom_items_and_discrepant_weight = (\n", "    df_parsable_uom_items_and_discrepant_weight.drop(\n", "        columns=[\"discrepancy\", \"discrepancy_threhold\", \"discrepancy_flag\"]\n", "    )\n", ")\n", "df_parsable_uom_items_and_discrepant_weight = (\n", "    df_parsable_uom_items_and_discrepant_weight.rename(\n", "        columns={\"calculated_weight\": \"proposed_weight\"}\n", "    )\n", ")\n", "\n", "# SAVING THE df_parsable_uom_items_and_discrepant_weight AS A LOCAL .CSV FILE\n", "if df_parsable_uom_items_and_discrepant_weight.shape[0] != 0:\n", "    df_parsable_uom_items_and_discrepant_weight.to_csv(\n", "        ITEMS_WITH_PARSABLE_UOM_AND_DISCREPANT_WEIGHT_FILE_PATH, index=False\n", "    )\n", "\n", "# LOADING df_parsable_uom_items_and_discrepant_weight TO WEIGHT CORRECTION SHEET\n", "pb.to_sheets(\n", "    df_parsable_uom_items_and_discrepant_weight, SHEET_ID, DISCREPANT_WEIGHT_SHEET\n", ")\n", "\n", "# SENDING AN UPDATE MAIL\n", "if df_parsable_uom_items_and_discrepant_weight.shape[0]:\n", "\n", "    from_email = \"<EMAIL>\"\n", "    to_email = [\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL> \",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "    ]\n", "    subject = \"Items with discrepant weight in rpc have been updated in the sheet\"\n", "    html_content = \"\"\"<p>Items with discrepant weight in rpc have been updated in 'Discrepant Weight' tab of <a href=\"https://docs.google.com/spreadsheets/d/1pFI8R6pAfASQQGtGhUr06CiVXCOJ8HE-VyzIb3dAj3w/edit?usp=sharing\">Weight Correction Sheet</a>.<br> Kindly validate and update them.</p>\"\"\"\n", "\n", "    pb.send_email(from_email, to_email, subject, html_content, files=[])\n", "\n", "# GETTING ITEMS WITH PARSABLE UOM AND MISSING WEIGHT IN RPC\n", "\n", "df_parsable_uom_items_with_missing_weight = df_parsable_uom_items[\n", "    df_parsable_uom_items[\"weight_in_gm\"].isna() == True\n", "].reset_index(drop=True)\n", "\n", "df_parsable_uom_items_with_missing_weight = (\n", "    df_parsable_uom_items_with_missing_weight.rename(\n", "        columns={\"calculated_weight\": \"proposed_weight\"}\n", "    )\n", ")\n", "\n", "# SAVING THE df_parsable_uom_items_with_missing_weight AS A LOCAL .CSV FILE\n", "if df_parsable_uom_items_with_missing_weight.shape[0] != 0:\n", "    df_parsable_uom_items_with_missing_weight.to_csv(\n", "        ITEMS_WITH_PARSABLE_UOM_AND_MISSING_WEIGHT_FILE_PATH, index=False\n", "    )\n", "\n", "# LOADING df_parsable_uom_items_with_missing_weight TO WEIGHT CORRECTION SHEET\n", "pb.to_sheets(df_parsable_uom_items_with_missing_weight, SHEET_ID, MISSING_WEIGHT_SHEET)\n", "\n", "# SENDING AN UPDATE MAIL\n", "if df_parsable_uom_items_with_missing_weight.shape[0]:\n", "\n", "    from_email = \"<EMAIL>\"\n", "    to_email = [\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "    ]\n", "    subject = \"Items with missing weight in rpc have been updated in the sheet\"\n", "    html_content = \"\"\"<p>Items with missing weight in rpc have been updated in 'Missing Weight' tab of <a href=\"https://docs.google.com/spreadsheets/d/1pFI8R6pAfASQQGtGhUr06CiVXCOJ8HE-VyzIb3dAj3w/edit?usp=sharing\">Weight Correction Sheet</a>.<br> Kindly validate and update them.</p>\"\"\"\n", "\n", "    pb.send_email(from_email, to_email, subject, html_content, files=[])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}