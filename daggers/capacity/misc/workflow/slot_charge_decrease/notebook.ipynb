{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime, timedelta\n", "import copy\n", "import time, os\n", "import warnings\n", "import requests\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["NOW_IST = datetime.now() + <PERSON><PERSON><PERSON>(hours=5, minutes=30)\n", "RUN_HOUR = NOW_IST.hour\n", "print(RUN_HOUR)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SHEET_ID = \"1_kbVYeavxSL1NbsbO-AmWdKGLYy_ZWr97CvHpLmy9Cs\"\n", "CONFIG_SHEET = \"Config\"\n", "RULES_SHEET = \"Utilization_t+1\"\n", "mailing_list = \"Mail list\"\n", "CSV_PATH_CASHBACK_UPLOAD = \"/tmp/slot_charge_upload.csv\"\n", "CSV_MAIL_DECREASE = \"/tmp/slot_charge_decrease.csv\"\n", "CSV_MERCHANTS_AT_0 = \"/tmp/underutilized_merchants.csv\"\n", "CSV_FILE_CHECK = \"/tmp/utilization_check.csv\"\n", "\n", "slot_charge_0_from = 5"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<H2> API</H2>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def epoch_time():\n", "    d = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    p = \"%Y-%m-%d %H:%M:%S\"\n", "    os.environ[\"TZ\"] = \"UTC\"\n", "    epoch = int(time.mktime(time.strptime(d, p)))\n", "    return epoch"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def update_slot_charge_api(CSV_PATH_CASHBACK_UPLOAD, epoch):\n", "    url = \"http://tick-tock-consumer.prod-sgp-k8s.grofer.io/update-slot-cashback\"\n", "    payload = open(CSV_PATH_CASHBACK_UPLOAD, \"rb\").read()\n", "    headers = {\n", "        \"Accept\": \"application/json, text/plain, */*\",\n", "        \"Content-Type\": \"text/plain\",\n", "        \"access_token\": \"de35642d64f6bad5a3112ca48873e278a6a006bd06f0e9e1f52dc0b7904aa1fe\",\n", "        \"auth_key\": \"53b5f3c25c12e7ad2385af6f9cef363ec8095420064b54ddd111f58d2dae8f0b\",\n", "        \"Referer\": \"http://tick-tock-consumer.prod-k8s.grofer.io/dashboard\",\n", "        \"Accept-Encoding\": \"gzip, deflate\",\n", "        \"Accept-Language\": \"en-GB,en-US;q=0.9,en;q=0.8\",\n", "        \"Cookie\": \"user=%7B%22merchant%22%3Afalse%2C%22phone%22%3A%22%22%2C%22verified%22%3Atrue%2C%22name%22%3A%22Mounika%20B%22%2C%22roles%22%3A%5B%22callcenter_exec%22%2C%22promise_api_manager%22%5D%2C%22wallet_id%22%3A%22%22%2C%22date_now%22%3A{}%2C%22image%22%3A%22https%3A%2F%2Flh3.googleusercontent.com%2Fa-%2FAOh14Gjl3sCkdYN6P-hNVoZriRx_ViItUPz7AxIar3We%3Ds96-c%3Fsz%3D250%22%2C%22id%22%3A13203448%2C%22email%22%3A%22badiginchala.saimounika%40grofers.com%22%7D\".format(\n", "            str(epoch)\n", "        ),\n", "    }\n", "    response = requests.request(\"PUT\", url, headers=headers, data=payload)\n", "\n", "    print(\"- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\")\n", "    if response.status_code >= 200 and response.status_code < 300:\n", "        status = \"success\"\n", "        print(\"cashback upload - SUCCESS\")\n", "        print(response.text)\n", "    else:\n", "        status = \"fail\"\n", "        print(\"cashback upload - FAIL\")\n", "        print(response.text)\n", "    return status"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["supply_cn = pb.get_connection(\"[Replica] Supply Orchestrator\")\n", "pt_cn = pb.get_connection(\"[Replica] Promise Time\")\n", "redshift_cn = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config = pb.from_sheets(SHEET_ID, CONFIG_SHEET, service_account=\"service_account\")\n", "config[\"merchant_id\"] = config[\"merchant_id\"].astype(int)\n", "df_no_charge = config[config[\"Rule\"] == \"remove_slot_charge\"]\n", "config = config[config[\"Rule\"] == \"decrease_t+1\"]\n", "merchants_list = config[\"merchant_id\"].unique().tolist()\n", "rules = pb.from_sheets(SHEET_ID, RULES_SHEET, service_account=\"service_account\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_mailer = pb.from_sheets(SHEET_ID, mailing_list, service_account=\"service_account\")\n", "mailer = df_mailer[\"mail\"].apply(lambda x: str(x)).to_list()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<H2>DECREASE</H2>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rules[\"Utilization Threshold\"] = rules.apply(\n", "    lambda row: int(str(row[\"Utilization Threshold\"]).replace(\"%\", \"\")) / 100, axis=1\n", ")\n", "HOUR_CHECK = str(RUN_HOUR) + \":00\"\n", "rules[\"merchant_id\"] = rules[\"merchant_id\"].astype(int)\n", "rules = rules.loc[rules[\"merchant_id\"].isin(merchants_list)].reset_index(drop=True)\n", "rules = rules[(rules[\"Check hour\"] == HOUR_CHECK)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["utilization = \"\"\"select distinct city,merchant_name,merchant_id,date,slot_freeze_hour,hours_left,\n", "case When sum(merchant_available) <= sum(warehouse_available) And sum(merchant_available) < sum(station_available) Then sum(merchant_available)\n", "            When sum(warehouse_available) <= sum(merchant_available) And sum(warehouse_available) < sum(station_available) Then sum(warehouse_available) \n", "            Else sum(station_available)\n", "            End As net_available_capacity,\n", "sum(merchant_total) as merchant_asked,\n", "sum(merchant_available) as merchant_available\n", "from\n", "(select distinct city_name as city,merchant_external_id as merchant_id,\n", "merchant_name,\n", "(slot_start+interval '5.5 hours')::date as date,\n", " TO_CHAR(slot_start + interval '5.5 hours','HH24MI') || ' - ' || \n", "        TO_CHAR(slot_end + interval '5.5 hours','HH24MI') AS slot,\n", "extract(hour from (slot_freeze_time+interval '5.5 hours')::timestamp)::int as slot_freeze_hour,\n", "slot_type,\n", "merchant_asked as merchant_total,\n", "(DATE_PART('day', (slot_freeze_time+interval '5.5 hours')::timestamp - (now()+interval '5.5 hours')::timestamp) * 24 + DATE_PART('hour', (slot_freeze_time+interval '5.5 hours')::timestamp - (now()+interval '5.5 hours')::timestamp))::int as hours_left,\n", "case when (merchant_asked- merchant_actual) <= 0 then 0 else (merchant_asked - merchant_actual) end as merchant_available,\n", "case when warehouse_available<=0 then 0 else warehouse_available end as warehouse_available,\n", "case when min(station_available)<=0 then 0 else min(station_available) end as station_available\n", "from sco_path_capacity spc\n", "inner join sco_merchant_slot sms on sms.slot_id = spc.slot_id \n", "    and sms.merchant_id = spc.merchant_id\n", "    and spc.slot_type in ('normal','premium','heavy')\n", "    and warehouse_name not ilike '%%cold%%' and warehouse_name not ilike '%%fnv%%' and warehouse_name not ilike '%%delight%%'\n", "where (slot_start+interval '5.5 hours')::date = (now()+interval '1 day'+interval '5.5 hours')::date\n", "and spc.active = 'true'\n", "and sms.active = 'true'\n", "and merchant_name not ilike '%% ES%%' and merchant_name not ilike '%%dummy%%' and merchant_name not ilike '%%donation%%'\n", "and merchant_name not ilike '%% FS%%' and merchant_name not ilike '%%b2b%%'\n", "group by 1,2,3,4,5,6,7,8,9,10,11\n", ") a\n", "where merchant_id not in (29108,29009)\n", "group by 1,2,3,4,5,6\n", "order by 1,2,3,4,6\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_utilization = pd.read_sql(utilization, supply_cn)\n", "# df_utilization = df_utilization[df_utilization[\"merchant_asked\"] > 0]\n", "df_utilization[\"slots\"] = (\n", "    df_utilization.groupby([\"city\", \"merchant_id\", \"merchant_name\"]).cumcount() + 1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_utilization[\"slots\"] = df_utilization.apply(\n", "    lambda row: str(row[\"slots\"]).replace(\"1\", \"Slot A\"), axis=1\n", ")\n", "df_utilization[\"slots\"] = df_utilization.apply(\n", "    lambda row: str(row[\"slots\"]).replace(\"2\", \"Slot B\")\n", "    if (\n", "        (\n", "            row[\"hours_left\"]\n", "            - (\n", "                df_utilization[\n", "                    (df_utilization[\"merchant_id\"] == row[\"merchant_id\"])\n", "                    & (df_utilization[\"slots\"] == \"Slot A\")\n", "                ][\"hours_left\"].values[0]\n", "            )\n", "        )\n", "        >= 3\n", "    )\n", "    else str(row[\"slots\"]).replace(\"2\", \"Slot A\"),\n", "    axis=1,\n", ")\n", "df_utilization[\"slots\"] = df_utilization.apply(\n", "    lambda row: str(row[\"slots\"]).replace(\"3\", \"Slot B\")\n", "    if (\n", "        (\n", "            row[\"hours_left\"]\n", "            - (\n", "                df_utilization[\n", "                    (df_utilization[\"merchant_id\"] == row[\"merchant_id\"])\n", "                    & (df_utilization[\"slots\"] == \"Slot A\")\n", "                ][\"hours_left\"].values[0]\n", "            )\n", "        )\n", "        >= 3\n", "    )\n", "    else str(row[\"slots\"]).replace(\"3\", \"Slot A\"),\n", "    axis=1,\n", ")\n", "df_utilization[\"slots\"] = df_utilization.apply(\n", "    lambda row: str(row[\"slots\"]).replace(\"4\", \"Slot B\"), axis=1\n", ")\n", "\n", "df_slots_blocks = (\n", "    df_utilization[\n", "        [\"city\", \"merchant_name\", \"merchant_id\", \"slot_freeze_hour\", \"slots\"]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_utilization = (\n", "    df_utilization.groupby(\n", "        [\"city\", \"merchant_id\", \"merchant_name\", \"slots\"], as_index=False\n", "    )[[\"net_available_capacity\", \"merchant_asked\"]]\n", "    .sum()\n", "    .reset_index(drop=True)\n", ")\n", "df_utilization[\"utilization\"] = 1 - (\n", "    df_utilization[\"net_available_capacity\"] / df_utilization[\"merchant_asked\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_merged = rules.merge(\n", "    df_utilization, how=\"left\", on=[\"city\", \"merchant_id\", \"merchant_name\", \"slots\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_merged.to_csv(CSV_FILE_CHECK, index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_merged = df_merged[df_merged[\"utilization\"] < df_merged[\"Utilization Threshold\"]]\n", "df_merged[\"deviation\"] = df_merged[\"Utilization Threshold\"] - df_merged[\"utilization\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_merged = df_merged[df_merged[\"deviation\"] >= 0.02]\n", "if df_merged.shape[0] > 0:\n", "    df_merged[\"change\"] = df_merged.apply(\n", "        lambda row: 10\n", "        if ((row[\"deviation\"] >= 0.02) and (row[\"deviation\"]) <= 0.05)\n", "        else (20 if ((row[\"deviation\"] > 0.05) and (row[\"deviation\"] <= 0.1)) else 100),\n", "        axis=1,\n", "    )\n", "else:\n", "    df_merged = pd.DataFrame(columns=[\"city\", \"merchant_id\", \"merchant_name\", \"slots\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["slot_freeze_times = \"\"\"select distinct city_name as city,merchant_external_id as merchant_id,\n", "merchant_name,\n", "(slot_start+interval '5.5 hours')::date as date,\n", " TO_CHAR(slot_start + interval '5.5 hours','HH24MI') || '-' || \n", "        TO_CHAR(slot_end + interval '5.5 hours','HH24MI') AS slot,\n", "extract(hour from (slot_freeze_time+interval '5.5 hours')::timestamp)::int as slot_freeze_hour,\n", "(DATE_PART('day', (slot_freeze_time+interval '5.5 hours')::timestamp - (now()+interval '5.5 hours')::timestamp) * 24 + DATE_PART('hour', (slot_freeze_time+interval '5.5 hours')::timestamp - (now()+interval '5.5 hours')::timestamp))::int as hours_left\n", "from sco_path_capacity spc\n", "inner join sco_merchant_slot sms on sms.slot_id = spc.slot_id \n", "    and sms.merchant_id = spc.merchant_id\n", "    and spc.slot_type in ('normal','premium','heavy','crm_reschedule')\n", "    and warehouse_name not ilike '%%cold%%' and warehouse_name not ilike '%%fnv%%'\n", "where (slot_start+interval '5.5 hours')::date = (now()+interval '1 day'+interval '5.5 hours')::date\n", "and spc.active = 'true'\n", "and sms.active = 'true'\n", "and merchant_name not ilike '%% ES%%' and merchant_name not ilike '%% FS%%' and merchant_name not ilike '%%dummy%%' and merchant_name not ilike '%%donation%%' and merchant_name not like '%%B2B%%' and merchant_name not in ('Super Store - 32nd Milestone','Super Store - Nehru Nagar')\n", "group by 1,2,3,4,5,6,7\n", "order by 1,2,3,4,7\"\"\"\n", "df_slots = pd.read_sql(slot_freeze_times, supply_cn)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df_merged.shape[0] > 0:\n", "    df_slots_merge = df_slots_blocks.merge(\n", "        df_slots,\n", "        how=\"left\",\n", "        on=[\"city\", \"merchant_id\", \"merchant_name\", \"slot_freeze_hour\"],\n", "    )\n", "    # df_slots_merge = config.merge(df_slots_merge, how = \"left\",on = [[\"city\",\"merchant_id\",\"merchant_name\",\"slots\"]]\n", "    df_final = df_merged.merge(\n", "        df_slots_merge, how=\"left\", on=[\"city\", \"merchant_id\", \"merchant_name\"]\n", "    )\n", "    df_final = df_final[\n", "        [\n", "            \"city\",\n", "            \"merchant_id\",\n", "            \"merchant_name\",\n", "            \"slot\",\n", "            \"deviation\",\n", "            \"utilization\",\n", "            \"change\",\n", "        ]\n", "    ].rename(columns={\"slots_y\": \"slots\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_slot_charge = \"\"\"select city,merchant_id,\n", "merchant_name,slot,\n", "day_count,slot_charge,\n", "install_ts_ist \n", "from (select city,merchant_id,\n", "merchant_name,replace(slot, ' ', '') as slot,\n", "day_count,slot_charge,install_ts_ist,\n", "RANK() OVER(PARTITION BY city,merchant_id,merchant_name,replace(slot, ' ', ''),day_count ORDER BY install_ts_ist DESC) as rank\n", "from metrics.merchant_slot_charge_logs)\n", "where rank = 1\n", "order by 1,2,3,4,5\"\"\"\n", "df_slot_charge_raw = pd.read_sql(current_slot_charge, redshift_cn)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df_merged.shape[0] > 0:\n", "    df_slot_charge = df_slot_charge_raw[df_slot_charge_raw[\"day_count\"] == 1]\n", "    df_slot_charge_merged = df_final.merge(\n", "        df_slot_charge, how=\"left\", on=[\"city\", \"merchant_id\", \"merchant_name\", \"slot\"]\n", "    )\n", "    df_slot_charge_merged[\"day_count\"] = df_slot_charge_merged[\"day_count\"].fillna(1)\n", "    df_slot_charge_merged[\"slot_charge\"] = df_slot_charge_merged[\"slot_charge\"].fillna(\n", "        0\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df_merged.shape[0] > 0:\n", "    df_slot_charge_merged[\"T\"] = df_slot_charge_merged.apply(\n", "        lambda row: 0,\n", "        axis=1,\n", "    )\n", "    df_slot_charge_merged[\"day_count\"] = 0\n", "    df_append = (\n", "        df_slot_charge_merged[\n", "            [\"city\", \"merchant_id\", \"merchant_name\", \"slot\", \"day_count\", \"T\"]\n", "        ]\n", "        .drop_duplicates()\n", "        .reset_index(drop=True)\n", "    )\n", "    df_append.rename(columns={\"T\": \"slot_charge\"}, inplace=True)\n", "#     df_slot_charge_merged[\"T+1\"] = df_slot_charge_merged.apply(\n", "#         lambda row: max((row[\"slot_charge\"] - row[\"change\"]), 0)\n", "#         if (row[\"slot_charge\"] != 0)\n", "#         else 0,\n", "#         axis=1,\n", "#     )\n", "\n", "else:\n", "    df_slot_charge_merged = pd.DataFrame(\n", "        columns=[\"city\", \"merchant_id\", \"merchant_name\", \"slot_charge\"]\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_slot_charge_merged"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchants_at_0 = df_slot_charge_merged[(df_slot_charge_merged[\"slot_charge\"] == 0)]\n", "merchants_at_0 = (\n", "    merchants_at_0[[\"city\", \"merchant_id\", \"merchant_name\"]]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "merchants_at_0_list = merchants_at_0[\"merchant_id\"].unique().tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df_merged.shape[0] > 0:\n", "    for i in range(1, 31):\n", "        if (i == 1) & (i <= (slot_charge_0_from - 1)):\n", "            count = \"T+\" + str(i)\n", "            df_slot_charge_merged[count] = df_slot_charge_merged.apply(\n", "                lambda row: max((row[\"slot_charge\"] - row[\"change\"]), 0)\n", "                if (row[\"slot_charge\"] != 0)\n", "                else 0,\n", "                axis=1,\n", "            )\n", "            df_slot_charge_merged[\"day_count\"] = i\n", "            df_temp = copy.deepcopy(df_slot_charge_merged)\n", "            df_temp = (\n", "                df_temp[\n", "                    [\"city\", \"merchant_id\", \"merchant_name\", \"slot\", \"day_count\", count]\n", "                ]\n", "                .drop_duplicates()\n", "                .reset_index(drop=True)\n", "            )\n", "            df_temp.rename(columns={count: \"slot_charge\"}, inplace=True)\n", "            if i <= 7:\n", "                df_append = df_append.append(df_temp)\n", "        elif i <= (slot_charge_0_from - 1):\n", "            count = \"T+\" + str(i)\n", "            previous_day = \"T+\" + str(i - 1)\n", "            df_slot_charge_merged[count] = df_slot_charge_merged.apply(\n", "                lambda row: max((row[previous_day] - 10), 0)\n", "                if (row[previous_day] > 10)\n", "                else (10 if (row[previous_day] == 10) else 0),\n", "                axis=1,\n", "            )\n", "            df_slot_charge_merged[\"day_count\"] = i\n", "            df_temp = copy.deepcopy(df_slot_charge_merged)\n", "            df_temp = (\n", "                df_temp[\n", "                    [\"city\", \"merchant_id\", \"merchant_name\", \"slot\", \"day_count\", count]\n", "                ]\n", "                .drop_duplicates()\n", "                .reset_index(drop=True)\n", "            )\n", "            df_temp.rename(columns={count: \"slot_charge\"}, inplace=True)\n", "            if i <= 7:\n", "                df_append = df_append.append(df_temp)\n", "        elif i > (slot_charge_0_from - 1):\n", "            count = \"T+\" + str(i)\n", "            df_slot_charge_merged[count] = 0\n", "            df_slot_charge_merged[\"day_count\"] = i\n", "            df_temp = copy.deepcopy(df_slot_charge_merged)\n", "            df_temp = (\n", "                df_temp[\n", "                    [\"city\", \"merchant_id\", \"merchant_name\", \"slot\", \"day_count\", count]\n", "                ]\n", "                .drop_duplicates()\n", "                .reset_index(drop=True)\n", "            )\n", "            df_temp.rename(columns={count: \"slot_charge\"}, inplace=True)\n", "            if i <= 7:\n", "                df_append = df_append.append(df_temp)\n", "else:\n", "    df_append = pd.DataFrame(\n", "        columns=[\"city\", \"merchant_id\", \"merchant_name\", \"slot\", \"day_count\"]\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df_no_charge.shape[0] > 0:\n", "    df_no_charge = df_no_charge.merge(\n", "        df_slots[[\"merchant_id\", \"slot\"]], on=\"merchant_id\", how=\"inner\"\n", "    )\n", "    for index, row in df_no_charge.iterrows():\n", "        for i in range(31):\n", "            if i == 0:\n", "                day = \"T\"\n", "            else:\n", "                day = \"T+\" + str(i)\n", "            df_no_charge.at[index, day] = 0\n", "            df_no_charge.at[index, \"slot_charge\"] = 0\n", "            if i <= 7:\n", "                row[\"day_count\"] = i\n", "                row[\"slot_charge\"] = df_no_charge.at[index, day]\n", "                df_append = df_append.append(\n", "                    row[\n", "                        [\n", "                            \"city\",\n", "                            \"merchant_id\",\n", "                            \"merchant_name\",\n", "                            \"slot\",\n", "                            \"day_count\",\n", "                            \"slot_charge\",\n", "                        ]\n", "                    ]\n", "                )\n", "    df_no_charge = df_no_charge.drop([\"Days open\", \"Rule\"], axis=\"columns\")\n", "    df_slot_charge_merged = df_slot_charge_merged.append(df_no_charge, sort=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["day_list = [\"T\"]\n", "other_cols = []\n", "for i in range(1, 31):\n", "    day = \"T+\" + str(i)\n", "    day_list.append(day)\n", "for column in df_slot_charge_merged.columns:\n", "    if column not in day_list:\n", "        other_cols.append(column)\n", "df_slot_charge_merged = df_slot_charge_merged[other_cols + day_list]\n", "df_slot_charge_merged.columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<H5>APPEND TO TABLE</H5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_append = df_append.sort_values([\"city\", \"merchant_id\", \"merchant_name\", \"slot\"])\n", "df_row_check = df_append.merge(\n", "    df_slot_charge_raw,\n", "    on=[\"city\", \"merchant_id\", \"merchant_name\", \"slot\", \"day_count\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df_append.shape[0] > 0:\n", "    df_upload = df_row_check[\n", "        df_row_check[\"slot_charge_x\"] != df_row_check[\"slot_charge_y\"]\n", "    ]\n", "    df_upload[\"install_ts_ist\"] = df_upload.apply(\n", "        lambda row: pd.to_datetime(NOW_IST).strftime(\"%Y-%m-%d %H:%M:%S\"), axis=1\n", "    )\n", "    df_upload.rename(columns={\"slot_charge_x\": \"slot_charge\"}, inplace=True)\n", "    df_upload[[\"day_count\", \"slot_charge\"]] = df_upload[\n", "        [\"day_count\", \"slot_charge\"]\n", "    ].astype(\"Int64\")\n", "    df_upload = (\n", "        df_upload[\n", "            [\n", "                \"install_ts_ist\",\n", "                \"city\",\n", "                \"merchant_id\",\n", "                \"merchant_name\",\n", "                \"slot\",\n", "                \"day_count\",\n", "                \"slot_charge\",\n", "            ]\n", "        ]\n", "        .drop_duplicates()\n", "        .reset_index(drop=True)\n", "    )\n", "\n", "    df_slot_charge_merged = df_slot_charge_merged[\n", "        ~df_slot_charge_merged[\"merchant_id\"].isin(merchants_at_0_list)\n", "    ]\n", "    #     df_slot_charge_merged = df_slot_charge_merged[\n", "    #         df_slot_charge_merged[\"T\"] < df_slot_charge_merged[\"slot_charge\"]\n", "    #     ]\n", "    if df_slot_charge_merged.shape[0] > 0:\n", "        if \"utilization\" not in (df_slot_charge_merged.columns):\n", "            df_slot_charge_merged[\"utilization\"] = None\n", "\n", "        df_mail = df_slot_charge_merged[\n", "            [\n", "                \"city\",\n", "                \"merchant_id\",\n", "                \"merchant_name\",\n", "                \"utilization\",\n", "                \"slot_charge\",\n", "                \"T\",\n", "                \"T+1\",\n", "            ]\n", "        ].rename(\n", "            columns={\"slot_charge\": \"Previous slot charge\", \"T+1\": \"New slot charge\"}\n", "        )\n", "        df_mail[\"utilization\"] = df_mail.apply(\n", "            lambda row: str(round(row[\"utilization\"] * 100)) + \"%\"\n", "            if (pd.notnull(row[\"utilization\"]))\n", "            else None,\n", "            axis=1,\n", "        )\n", "\n", "        df_mail[\"change\"] = \"decrease\"\n", "        df_mail[[\"Previous slot charge\", \"New slot charge\"]] = df_mail[\n", "            [\"Previous slot charge\", \"New slot charge\"]\n", "        ].astype(\"Int64\")\n", "        df_mail = df_mail[df_mail[\"Previous slot charge\"] > df_mail[\"New slot charge\"]]\n", "        df_mail = df_mail.drop_duplicates().reset_index(drop=True)\n", "\n", "        exclude_cols = [\n", "            \"city\",\n", "            \"merchant_id\",\n", "            \"slot\",\n", "            \"merchant_name\",\n", "            \"deviation\",\n", "            \"change\",\n", "            \"day_count\",\n", "            \"slot_charge\",\n", "            \"install_ts_ist\",\n", "            \"utilization\",\n", "        ]\n", "        day_list = []\n", "        for col in df_slot_charge_merged.columns:\n", "            if col not in exclude_cols:\n", "                df_slot_charge_merged[col] = (\n", "                    df_slot_charge_merged[col].astype(int).astype(\"Int64\")\n", "                )\n", "                day_list.append(col)\n", "        df_file = (\n", "            df_slot_charge_merged[[\"merchant_id\", \"slot\"] + day_list]\n", "            .drop_duplicates()\n", "            .reset_index(drop=True)\n", "        )\n", "    else:\n", "        df_file = pd.DataFrame()\n", "        df_mail = pd.DataFrame()\n", "else:\n", "    df_file = pd.DataFrame()\n", "    df_mail = pd.DataFrame()\n", "    df_upload = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df_mail.shape[0] > 0:\n", "    df_mail = df_mail.drop_duplicates(\n", "        subset=[\"city\", \"merchant_id\", \"merchant_name\"], keep=\"last\"\n", "    ).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(df_file.shape[0], df_upload.shape[0], merchants_at_0.shape[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df_file.shape[0] > 0:\n", "    df_file.to_csv(CSV_PATH_CASHBACK_UPLOAD, index=False)\n", "\n", "file_list = []\n", "if df_mail.shape[0] > 0:\n", "    df_mail.to_csv(CSV_MAIL_DECREASE, index=False)\n", "    file_list.append(CSV_MAIL_DECREASE)\n", "\n", "if merchants_at_0.shape[0] > 0:\n", "    merchants_at_0.to_csv(CSV_MERCHANTS_AT_0, index=False)\n", "    file_list.append(CSV_MERCHANTS_AT_0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<H2>API HIT</H2>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df_file.shape[0] > 0:\n", "\n", "    epoch = epoch_time()\n", "    status = update_slot_charge_api(CSV_PATH_CASHBACK_UPLOAD, epoch)\n", "    pb.send_slack_message(\n", "        channel=\"planning-dag-alerts\",\n", "        text=\"Slot charge decrease basen on t+1 utilization - charges were modified for some merchants,Mail sent\",\n", "    )\n", "    print(status)\n", "else:\n", "    status = \"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["<H2>APPEND TO TABLE</H2>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# PUSH DATA TO REDSHIFT TABLE\n", "kwargs1 = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"merchant_slot_charge_logs\",\n", "    \"table_description\": \"Merchant slot charge\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"install_ts_ist\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"row install time\",\n", "        },\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"City\"},\n", "        {\n", "            \"name\": \"merchant_id\",\n", "            \"type\": \"bigint\",\n", "            \"description\": \"merchant id\",\n", "        },\n", "        {\n", "            \"name\": \"merchant_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"merchant name\",\n", "        },\n", "        {\n", "            \"name\": \"slot\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"merchant slot\",\n", "        },\n", "        {\n", "            \"name\": \"day_count\",\n", "            \"type\": \"smallint\",\n", "            \"description\": \"Day count from today\",\n", "        },\n", "        {\n", "            \"name\": \"slot_charge\",\n", "            \"type\": \"smallint\",\n", "            \"description\": \"slot charge\",\n", "        },\n", "    ],\n", "    \"sortkey\": [\"city\", \"merchant_id\", \"slot\", \"day_count\"],\n", "    \"incremental_key\": \"install_ts\",\n", "    \"load_type\": \"append\",\n", "}\n", "if (df_upload.shape[0] > 0) & (status == \"success\"):\n", "    pb.to_redshift(df_upload, **kwargs1)\n", "    print(\"- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\")\n", "    print(\"append success\")\n", "    print(\"- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\")\n", "else:\n", "    print(\"Dataframe empty\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<H2>MAIL</H2>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "to_email = mailer\n", "files = file_list\n", "subject = \"Slot charge modifications\"\n", "header = \"\"\"<p>Please find below the changes made to slot charges.<br></p>\"\"\"\n", "df_decrease = \"\"\"<p>Merchants for which slot charge was decreased<br>\n", "                {}<br></p>\"\"\".format(\n", "    df_mail.to_html(justify=\"center\", index=False)\n", ")\n", "df_0 = \"\"\"<p>Merchants for which slot charge is already at 0 and are still underutilized<br>\n", "                {}<br></p>\"\"\".format(\n", "    merchants_at_0.to_html(justify=\"center\", index=False)\n", ")\n", "\n", "if df_mail.shape[0] != 0:\n", "    html_content = header + df_decrease\n", "\n", "if (merchants_at_0.shape[0] != 0) & (df_mail.shape[0] != 0):\n", "    html_content += df_0\n", "elif (merchants_at_0.shape[0] != 0) & (df_mail.shape[0] == 0):\n", "    html_content = df_0\n", "\n", "if (len(files) > 0) & (status == \"success\"):\n", "    pb.send_email(from_email, to_email, subject, html_content, files)\n", "else:\n", "    pb.send_slack_message(\n", "        channel=\"planning-dag-alerts\", text=\"No slot charge modifications at this hour\"\n", "    )\n", "\n", "\n", "if df_merged.shape[0] > 0:\n", "    pb.send_email(\n", "        from_email,\n", "        to_email=[\"<EMAIL>\"],\n", "        subject=\"Utilization check file\",\n", "        html_content=\"\",\n", "        files=[CSV_FILE_CHECK],\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}