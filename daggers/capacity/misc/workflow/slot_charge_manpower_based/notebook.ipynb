{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime, timedelta\n", "import copy\n", "import os\n", "import requests\n", "import json\n", "import time\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["time_now = datetime.now() + <PERSON><PERSON><PERSON>(hours=5, minutes=30)\n", "install_ts_ist = time_now.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "today = time_now.strftime(\"%Y-%m-%d\")\n", "run_hour = (time_now).hour\n", "print(install_ts_ist)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ser_con = pb.get_connection(\"[Replica] Serviceability\")\n", "red_con = pb.get_connection(\"[Warehouse] Redshift\")\n", "gr_con = pb.get_connection(\"[Replica] Grofers DB\")\n", "log_con = pb.get_connection(\"[Replica] Logistic DB\")\n", "surge_sheet_id = \"1-5XKQHzjR_UmWw38Lh99jC6v-Q3iBVY0PnmOJXP8JgE\"\n", "gsp_thresholds_di = \"gsp_thresholds\"\n", "picker_thresholds_di = \"picker_thresholds\"\n", "test_stores = \"test_stores\"\n", "override_thresholds_di = \"override_thresholds\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["CHARGE_UPLOAD = \"/tmp/slot_charge_mp_utilisation_based_file_upload.csv\"\n", "MAIL_UPLOAD = \"/tmp/slot_charge_low_pt.csv\"\n", "MAIL_UPLOAD_1 = \"/tmp/slot_charge_high_pt.csv\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_list = pb.from_sheets(\n", "    \"1eN8O6INpC61FFqoQsR9-0MhD_HWVhk7AP0BDVAcwtcc\",\n", "    \"manp_charge_exclusion_list\",\n", "    service_account=\"priority_account\",\n", ")\n", "merchant_list[\"merchant_id\"] = merchant_list[\"merchant_id\"].astype(int)\n", "exclusion_merchant_list = merchant_list[\"merchant_id\"].unique().tolist()\n", "if merchant_list.shape[0] == 0:\n", "    exclusion_merchant_list.append(0)\n", "exclusion_merchant_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["express_merchants = \"\"\"select gm.id::int as merchant_id,chain_id from lake_cms.view_gr_merchant gm where active_flag = true and chain_id = 1383\"\"\"\n", "df_input_merchant_list = pd.read_sql(\n", "    express_merchants, pb.get_connection(\"[Warehouse] Redshift\")\n", ")\n", "input_merchant_list = df_input_merchant_list[\"merchant_id\"].unique().tolist()\n", "for element in exclusion_merchant_list:\n", "    if element in input_merchant_list:\n", "        input_merchant_list.remove(element)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_mailer = pb.from_sheets(\n", "    \"1eN8O6INpC61FFqoQsR9-0MhD_HWVhk7AP0BDVAcwtcc\",\n", "    \"Mail list\",\n", "    service_account=\"priority_account\",\n", ")\n", "mailer = df_mailer[\"mail\"].apply(lambda x: str(x)).to_list()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picker_thresholds = pb.from_sheets(\n", "    \"1eN8O6INpC61FFqoQsR9-0MhD_HWVhk7AP0BDVAcwtcc\",\n", "    \"picker utilisation thresholds\",\n", "    service_account=\"priority_account\",\n", ")\n", "picker_thresholds[\"utilisation_lower_limit\"] = picker_thresholds[\n", "    \"utilisation_lower_limit\"\n", "].astype(float)\n", "picker_thresholds[\"utilisation_upper_limit\"] = picker_thresholds[\n", "    \"utilisation_upper_limit\"\n", "].astype(float)\n", "picker_thresholds[\"slot_charge\"] = (\n", "    picker_thresholds[\"slot_charge\"].astype(int).astype(\"Int64\")\n", ")\n", "picker_thresholds"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gsp_thresholds = pb.from_sheets(\n", "    \"1eN8O6INpC61FFqoQsR9-0MhD_HWVhk7AP0BDVAcwtcc\",\n", "    \"gsp utilisation thresholds\",\n", "    service_account=\"priority_account\",\n", ")\n", "gsp_thresholds[\"utilisation_lower_limit\"] = gsp_thresholds[\n", "    \"utilisation_lower_limit\"\n", "].astype(float)\n", "gsp_thresholds[\"utilisation_upper_limit\"] = gsp_thresholds[\n", "    \"utilisation_upper_limit\"\n", "].astype(float)\n", "gsp_thresholds[\"slot_charge\"] = (\n", "    gsp_thresholds[\"slot_charge\"].astype(int).astype(\"Int64\")\n", ")\n", "gsp_thresholds"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["override_thresholds = pb.from_sheets(\n", "    \"1eN8O6INpC61FFqoQsR9-0MhD_HWVhk7AP0BDVAcwtcc\",\n", "    \"slot_charge_override\",\n", "    service_account=\"priority_account\",\n", ")\n", "override_thresholds[\"merchant_id\"] = (\n", "    override_thresholds[\"merchant_id\"].astype(int).astype(\"Int64\")\n", ")\n", "override_thresholds"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gsp_override_thresholds = override_thresholds[\n", "    [\"merchant_id\", \"merchant_name\", \"gsp_utilisation_thresholds\"]\n", "]\n", "picker_override_thresholds = override_thresholds[\n", "    [\"merchant_id\", \"merchant_name\", \"picker_utilisation_thresholds\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gsp_override_thresholds[\"gsp_utilisation_thresholds\"] = gsp_override_thresholds[\n", "    \"gsp_utilisation_thresholds\"\n", "].apply(lambda x: None if len(x) == 0 else x)\n", "picker_override_thresholds[\n", "    \"picker_utilisation_thresholds\"\n", "] = picker_override_thresholds[\"picker_utilisation_thresholds\"].apply(\n", "    lambda x: None if len(x) == 0 else x\n", ")\n", "\n", "gsp_override_thresholds = gsp_override_thresholds[\n", "    gsp_override_thresholds[\"gsp_utilisation_thresholds\"].notnull()\n", "]\n", "picker_override_thresholds = picker_override_thresholds[\n", "    picker_override_thresholds[\"picker_utilisation_thresholds\"].notnull()\n", "]\n", "\n", "gsp_override_thresholds"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gsp_override_merchants_test = gsp_override_thresholds[\"merchant_id\"].to_list()\n", "picker_override_merchants_test = picker_override_thresholds[\"merchant_id\"].to_list()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gsp_override_merchants = []\n", "[\n", "    gsp_override_merchants.append(x)\n", "    for x in gsp_override_merchants_test\n", "    if x not in gsp_override_merchants\n", "]\n", "picker_override_merchants = []\n", "[\n", "    picker_override_merchants.append(x)\n", "    for x in picker_override_merchants_test\n", "    if x not in picker_override_merchants\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(picker_override_merchants, gsp_override_merchants)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gsp_override_thresholds[\"gsp_utilisation_thresholds\"] = gsp_override_thresholds[\n", "    \"gsp_utilisation_thresholds\"\n", "].apply(lambda x: x.strip().strip(\"][\").split(\",\\n\"))\n", "picker_override_thresholds[\n", "    \"picker_utilisation_thresholds\"\n", "] = picker_override_thresholds[\"picker_utilisation_thresholds\"].apply(\n", "    lambda x: x.strip().strip(\"][\").split(\",\\n\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["p = (\n", "    picker_override_thresholds.apply(\n", "        lambda x: pd.Series(x[\"picker_utilisation_thresholds\"]), axis=1\n", "    )\n", "    .stack()\n", "    .reset_index(level=1, drop=True)\n", ")\n", "p.name = \"picker_utilisation_thresholds\"\n", "picker_override_thresholds = (\n", "    picker_override_thresholds.drop(\"picker_utilisation_thresholds\", axis=1)\n", "    .join(p)\n", "    .reset_index(drop=True)\n", ")\n", "picker_override_thresholds"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["g = (\n", "    gsp_override_thresholds.apply(\n", "        lambda x: pd.Series(x[\"gsp_utilisation_thresholds\"]), axis=1\n", "    )\n", "    .stack()\n", "    .reset_index(level=1, drop=True)\n", ")\n", "g.name = \"gsp_utilisation_thresholds\"\n", "gsp_override_thresholds = (\n", "    gsp_override_thresholds.drop(\"gsp_utilisation_thresholds\", axis=1)\n", "    .join(g)\n", "    .reset_index(drop=True)\n", ")\n", "gsp_override_thresholds"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picker_override_thresholds[\n", "    \"picker_utilisation_thresholds\"\n", "] = picker_override_thresholds[\"picker_utilisation_thresholds\"].apply(\n", "    lambda x: json.loads(x)\n", ")\n", "gsp_override_thresholds[\"gsp_utilisation_thresholds\"] = gsp_override_thresholds[\n", "    \"gsp_utilisation_thresholds\"\n", "].apply(lambda x: json.loads(x))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if picker_override_thresholds.shape[0] > 0:\n", "    picker_override_thresholds[\n", "        \"utilisation_lower_limit\"\n", "    ] = picker_override_thresholds.apply(\n", "        lambda row: row[\"picker_utilisation_thresholds\"][\"lower_limit\"], axis=1\n", "    )\n", "    picker_override_thresholds[\n", "        \"utilisation_upper_limit\"\n", "    ] = picker_override_thresholds.apply(\n", "        lambda row: row[\"picker_utilisation_thresholds\"][\"upper_limit\"], axis=1\n", "    )\n", "    picker_override_thresholds[\"slot_charge\"] = picker_override_thresholds.apply(\n", "        lambda row: row[\"picker_utilisation_thresholds\"][\"surge_charge\"], axis=1\n", "    )\n", "picker_override_thresholds"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if gsp_override_thresholds.shape[0] > 0:\n", "    gsp_override_thresholds[\"utilisation_lower_limit\"] = gsp_override_thresholds.apply(\n", "        lambda row: row[\"gsp_utilisation_thresholds\"][\"lower_limit\"], axis=1\n", "    )\n", "    gsp_override_thresholds[\"utilisation_upper_limit\"] = gsp_override_thresholds.apply(\n", "        lambda row: row[\"gsp_utilisation_thresholds\"][\"upper_limit\"], axis=1\n", "    )\n", "    gsp_override_thresholds[\"slot_charge\"] = gsp_override_thresholds.apply(\n", "        lambda row: row[\"gsp_utilisation_thresholds\"][\"surge_charge\"], axis=1\n", "    )\n", "\n", "gsp_override_thresholds"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["high_pt_thresholds = pb.from_sheets(\n", "    \"1eN8O6INpC61FFqoQsR9-0MhD_HWVhk7AP0BDVAcwtcc\",\n", "    \"high_pt_charge\",\n", "    service_account=\"priority_account\",\n", ")\n", "high_pt_thresholds[\"slot_number\"] = high_pt_thresholds[\"slot_number\"].astype(int)\n", "high_pt_thresholds[\"charge\"] = high_pt_thresholds[\"charge\"].astype(int)\n", "high_pt_thresholds"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def epoch_time():\n", "    d = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    p = \"%Y-%m-%d %H:%M:%S\"\n", "    os.environ[\"TZ\"] = \"UTC\"\n", "    epoch = int(time.mktime(time.strptime(d, p)))\n", "    return epoch\n", "\n", "\n", "def update_slot_charge_api(CSV_PATH_CASHBACK_UPLOAD, epoch):\n", "    url = \"http://tick-tock-consumer.prod-sgp-k8s.grofer.io/update-slot-cashback\"\n", "    payload = open(CSV_PATH_CASHBACK_UPLOAD, \"rb\").read()\n", "    headers = {\n", "        \"Accept\": \"application/json, text/plain, */*\",\n", "        \"Content-Type\": \"text/plain\",\n", "        \"access_token\": \"de35642d64f6bad5a3112ca48873e278a6a006bd06f0e9e1f52dc0b7904aa1fe\",\n", "        \"auth_key\": \"53b5f3c25c12e7ad2385af6f9cef363ec8095420064b54ddd111f58d2dae8f0b\",\n", "        \"Referer\": \"http://tick-tock-consumer.prod-k8s.grofer.io/dashboard\",\n", "        \"Accept-Encoding\": \"gzip, deflate\",\n", "        \"Accept-Language\": \"en-GB,en-US;q=0.9,en;q=0.8\",\n", "        \"Cookie\": \"user=%7B%22merchant%22%3Afalse%2C%22phone%22%3A%22%22%2C%22verified%22%3Atrue%2C%22name%22%3A%22Mounika%20B%22%2C%22roles%22%3A%5B%22callcenter_exec%22%2C%22promise_api_manager%22%5D%2C%22wallet_id%22%3A%22%22%2C%22date_now%22%3A{}%2C%22image%22%3A%22https%3A%2F%2Flh3.googleusercontent.com%2Fa-%2FAOh14Gjl3sCkdYN6P-hNVoZriRx_ViItUPz7AxIar3We%3Ds96-c%3Fsz%3D250%22%2C%22id%22%3A13203448%2C%22email%22%3A%22badiginchala.saimounika%40grofers.com%22%7D\".format(\n", "            str(epoch)\n", "        ),\n", "    }\n", "    response = requests.request(\"PUT\", url, headers=headers, data=payload)\n", "\n", "    print(\"- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\")\n", "    if response.status_code >= 200 and response.status_code < 300:\n", "        status = \"success\"\n", "        print(\"cashback upload - SUCCESS\")\n", "        print(response.text)\n", "    else:\n", "        status = \"fail\"\n", "        print(\"cashback upload - FAIL\")\n", "        print(response.text)\n", "    return status"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_sql(query, connection, max_tries=5):\n", "    for attempt in range(max_tries):\n", "        print(f\"Trying to read attempt: {attempt}...\")\n", "        try:\n", "            return pd.read_sql(query, connection)\n", "            break\n", "        except Exception as e:\n", "            print(e)\n", "            time.sleep(20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["utilisation = \"\"\"WITH utilisation AS\n", "  (SELECT r.city,\n", "          r.merchant_id,\n", "          r.merchant_name,\n", "          r.employee_id,\n", "          r.user_profile_id,\n", "          r.user_type,\n", "          (q.install_ts+interval'5.5 hours')::date AS delivery_date,\n", "          sum(extract(epoch\n", "                      FROM ((CASE\n", "                                 WHEN exit_ts IS NULL THEN (current_timestamp + interval '5.5 hours')\n", "                                 WHEN (exit_ts) > (current_timestamp) THEN (current_timestamp + interval '5.5 hours')\n", "                                 ELSE (exit_ts + interval '5.5 hours')\n", "                             END) - (CASE\n", "                                         WHEN (entry_ts) < (current_timestamp - interval '{window} minutes') THEN (current_timestamp + interval '5.5 hours' - interval '{window} minutes')\n", "                                         ELSE (entry_ts + interval '5.5 hours')\n", "                                     END)))/60) AS idle_time,\n", "          roster,\n", "          (roster - (sum(extract(epoch\n", "                      FROM ((CASE\n", "                                 WHEN exit_ts IS NULL THEN (current_timestamp + interval '5.5 hours')\n", "                                 WHEN (exit_ts) > (current_timestamp) THEN (current_timestamp + interval '5.5 hours')\n", "                                 ELSE (exit_ts + interval '5.5 hours')\n", "                             END) - (CASE\n", "                                         WHEN (entry_ts) < (current_timestamp - interval '{window} minutes') THEN (current_timestamp  + interval '5.5 hours' - interval '{window} minutes')\n", "                                         ELSE (entry_ts + interval '5.5 hours')\n", "                                     END)))/60)))::float/roster::float AS utilisation_time\n", "   FROM\n", "     (SELECT merchant_id,\n", "             merchant_name,\n", "             employee_id,\n", "             user_type,\n", "             user_profile_id,\n", "             city,\n", "             case when sum(roster_time) > {window} then {window} else sum(roster_time) end AS roster\n", "      FROM\n", "        (SELECT ur.user_profile_id,\n", "                lup.employee_id,\n", "                lna.city,\n", "                start_time,\n", "                end_time,\n", "                ln.external_id AS merchant_id,\n", "                ln.external_name AS merchant_name,\n", "                CASE\n", "                    WHEN lup.type ILIKE '%%picker%%' THEN 'PICKER'\n", "                    WHEN lup.type IN ('GSP',\n", "                                      'GEP') THEN 'GEP'\n", "                    ELSE NULL\n", "                END AS user_type,\n", "                CASE\n", "                    WHEN (start_time) > (current_timestamp - interval '{window} minutes') THEN (start_time + interval '5.5 hours')\n", "                    ELSE (current_timestamp  + interval '5.5 hours' - interval '{window} minutes')\n", "                END AS mod_start_time,\n", "                CASE\n", "                    WHEN (end_time) < (current_timestamp) THEN (end_time + interval '5.5 hours')\n", "                    ELSE (current_timestamp + interval '5.5 hours')\n", "                END AS mod_end_time,\n", "                (extract(epoch\n", "                         FROM ((CASE\n", "                                    WHEN (end_time) < (current_timestamp) THEN (end_time + interval '5.5 hours')\n", "                                    ELSE (current_timestamp + interval '5.5 hours')\n", "                                END) - (CASE\n", "                                            WHEN (start_time) > (current_timestamp - interval '{window} minutes') THEN (start_time + interval '5.5 hours')\n", "                                            ELSE (current_timestamp + interval '5.5 hours' - interval '{window} minutes')\n", "                                        END)))/60) AS roster_time\n", "         FROM logistics_user_roster ur\n", "         INNER JOIN logistics_user_profile lup ON ur.user_profile_id = lup.id\n", "         AND (end_time) >= (current_timestamp - interval '{window} minutes')\n", "         and (start_time) < current_timestamp\n", "         and published_shift_start = true\n", "         AND lup.type IN ('GEP',\n", "                          'GSP',\n", "                          'PICKER')\n", "         INNER JOIN logistics_user_profile_node lupn ON lup.id = lupn.user_profile_id\n", "         INNER JOIN logistics_node ln ON lupn.node_id = ln.id\n", "         INNER JOIN logistics_node_address lna ON ln.node_address_id= lna.id\n", "         GROUP BY 1,2,3,4,5,6,7,8,9) a\n", "      GROUP BY 1,2,3,4,5,6) r\n", "   LEFT JOIN logistics_express_allocation_field_executive_queue q ON q.user_profile_id::int = r.user_profile_id::int\n", "   WHERE (exit_ts IS NULL and (entry_ts) < (current_timestamp) and roster > 0)\n", "     OR ((exit_ts) >= (current_timestamp - interval '{window} minutes') and exit_ts::date = current_date and (exit_ts) < current_timestamp and roster > 0)\n", "     OR ((entry_ts) >= (current_timestamp - interval '{window} minutes') and entry_ts::date =  current_date and (entry_ts) < current_timestamp and roster > 0)\n", "   GROUP BY 1,2,3,4,5,6,7,9\n", "   ORDER BY 2,3,4,5,6,7 DESC)\n", "   \n", "SELECT city,\n", "       a.merchant_id::int as merchant_id,\n", "       a.merchant_name,\n", "       COALESCE(SUM(CASE\n", "                        WHEN user_type = 'PICKER' THEN employees\n", "                        ELSE NULL\n", "                    END),0)::int AS pickers,\n", "       COALESCE(SUM(CASE\n", "                        WHEN user_type = 'PICKER' THEN idle_time\n", "                        ELSE NULL\n", "                    END),0) AS picker_idle_time,\n", "       COALESCE(SUM(CASE\n", "                        WHEN user_type = 'PICKER' THEN utilisation\n", "                        ELSE NULL\n", "                    END),0)::float AS picker_utilisation,\n", "       COALESCE(SUM(CASE\n", "                        WHEN user_type = 'GEP' THEN employees\n", "                        ELSE NULL\n", "                    END),0)::int AS gsps,\n", "       COALESCE(SUM(CASE\n", "                        WHEN user_type = 'GEP' THEN idle_time\n", "                        ELSE NULL\n", "                    END),0) AS gsp_idle_time,\n", "       COALESCE(SUM(CASE\n", "                        WHEN user_type = 'GEP' THEN utilisation\n", "                        ELSE NULL\n", "                    END),0)::float AS gsp_utilisation\n", "FROM\n", "  (SELECT city,\n", "          merchant_id,\n", "          merchant_name,\n", "          user_type,\n", "          count(DISTINCT employee_id) AS employees,\n", "          (PERCENTILE_CONT(0.60) WITHIN\n", "           GROUP (\n", "                  ORDER BY idle_time)) AS idle_time,\n", "          (PERCENTILE_CONT(0.60) WITHIN\n", "           GROUP (\n", "                  ORDER BY (case when idle_time > roster then 0 else utilisation_time end))) AS utilisation\n", "   FROM utilisation\n", "   WHERE idle_time IS NOT NULL\n", "     AND roster > 5\n", "   GROUP BY 1,2,3,4) a\n", "GROUP BY 1,2,3\n", "ORDER BY 1,2,3\"\"\"\n", "df_utilisation_15 = read_sql(\n", "    utilisation.format(window=15), pb.get_connection(\"[Replica] Logistic DB\")\n", ")\n", "df_utilisation_15.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_utilisation_30 = read_sql(\n", "    utilisation.format(window=30), pb.get_connection(\"[Replica] Logistic DB\")\n", ")\n", "df_utilisation_30.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_utilisation_60 = read_sql(\n", "    utilisation.format(window=60), pb.get_connection(\"[Replica] Logistic DB\")\n", ")\n", "df_utilisation_60.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_utilisation_30.rename(\n", "    columns={\n", "        \"pickers\": \"pickers_30\",\n", "        \"picker_utilisation\": \"picker_utilisation_30\",\n", "        \"picker_idle_time\": \"picker_idle_time_30\",\n", "        \"gsps\": \"gsps_30\",\n", "        \"gsp_utilisation\": \"gsp_utilisation_30\",\n", "        \"gsp_idle_time\": \"gsp_idle_time_30\",\n", "    },\n", "    inplace=True,\n", ")\n", "df_utilisation_15.rename(\n", "    columns={\n", "        \"pickers\": \"pickers_15\",\n", "        \"picker_utilisation\": \"picker_utilisation_15\",\n", "        \"picker_idle_time\": \"picker_idle_time_15\",\n", "        \"gsps\": \"gsps_15\",\n", "        \"gsp_utilisation\": \"gsp_utilisation_15\",\n", "        \"gsp_idle_time\": \"gsp_idle_time_15\",\n", "    },\n", "    inplace=True,\n", ")\n", "df_utilisation_60.rename(\n", "    columns={\n", "        \"pickers\": \"pickers_60\",\n", "        \"picker_utilisation\": \"picker_utilisation_60\",\n", "        \"picker_idle_time\": \"picker_idle_time_60\",\n", "        \"gsps\": \"gsps_60\",\n", "        \"gsp_utilisation\": \"gsp_utilisation_60\",\n", "        \"gsp_idle_time\": \"gsp_idle_time_60\",\n", "    },\n", "    inplace=True,\n", ")\n", "\n", "\n", "df_utilisation_60 = df_utilisation_60.merge(\n", "    df_utilisation_30[\n", "        [\n", "            \"merchant_id\",\n", "            \"pickers_30\",\n", "            \"picker_utilisation_30\",\n", "            \"picker_idle_time_30\",\n", "            \"gsps_30\",\n", "            \"gsp_utilisation_30\",\n", "            \"gsp_idle_time_30\",\n", "        ]\n", "    ],\n", "    how=\"left\",\n", "    on=[\"merchant_id\"],\n", ")\n", "df_utilisation_60 = df_utilisation_60.merge(\n", "    df_utilisation_15[\n", "        [\n", "            \"merchant_id\",\n", "            \"pickers_15\",\n", "            \"picker_utilisation_15\",\n", "            \"picker_idle_time_15\",\n", "            \"gsps_15\",\n", "            \"gsp_utilisation_15\",\n", "            \"gsp_idle_time_15\",\n", "        ]\n", "    ],\n", "    how=\"left\",\n", "    on=[\"merchant_id\"],\n", ")\n", "df_utilisation_60.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df_utilisation_60.shape[0] > 0:\n", "    df_utilisation_60[\"gsps\"] = df_utilisation_60.apply(\n", "        lambda row: row[\"gsps_15\"]\n", "        if ((row[\"gsp_utilisation_15\"] > 0) or (row[\"gsps_15\"] > 1))\n", "        else (\n", "            row[\"gsps_30\"]\n", "            if ((row[\"gsp_utilisation_30\"] > 0) or (row[\"gsps_30\"] > 1))\n", "            else row[\"gsps_60\"]\n", "        ),\n", "        axis=1,\n", "    )\n", "    df_utilisation_60[\"gsp_idle_time\"] = df_utilisation_60.apply(\n", "        lambda row: row[\"gsp_idle_time_15\"]\n", "        if ((row[\"gsp_utilisation_15\"] > 0) or (row[\"gsps_15\"] > 1))\n", "        else (\n", "            row[\"gsp_idle_time_30\"]\n", "            if ((row[\"gsp_utilisation_30\"] > 0) or (row[\"gsps_30\"] > 1))\n", "            else row[\"gsp_idle_time_60\"]\n", "        ),\n", "        axis=1,\n", "    )\n", "    df_utilisation_60[\"gsp_utilisation\"] = df_utilisation_60.apply(\n", "        lambda row: row[\"gsp_utilisation_15\"]\n", "        if ((row[\"gsp_utilisation_15\"] > 0) or (row[\"gsps_15\"] > 1))\n", "        else (\n", "            row[\"gsp_utilisation_30\"]\n", "            if ((row[\"gsp_utilisation_30\"] > 0) or (row[\"gsps_30\"] > 1))\n", "            else row[\"gsp_utilisation_60\"]\n", "        ),\n", "        axis=1,\n", "    )\n", "    df_utilisation_60[\"gsp_window\"] = df_utilisation_60.apply(\n", "        lambda row: 15\n", "        if ((row[\"gsp_utilisation_15\"] > 0) or (row[\"gsps_15\"] > 1))\n", "        else (30 if ((row[\"gsp_utilisation_30\"] > 0) or (row[\"gsps_30\"] > 1)) else 60),\n", "        axis=1,\n", "    )\n", "\n", "    df_utilisation_60[\"pickers\"] = df_utilisation_60.apply(\n", "        lambda row: row[\"pickers_15\"]\n", "        if ((row[\"picker_utilisation_15\"] > 0) or (row[\"pickers_15\"] > 1))\n", "        else (\n", "            row[\"pickers_30\"]\n", "            if ((row[\"picker_utilisation_30\"] > 0) or (row[\"pickers_30\"] > 1))\n", "            else row[\"pickers_60\"]\n", "        ),\n", "        axis=1,\n", "    )\n", "    df_utilisation_60[\"picker_idle_time\"] = df_utilisation_60.apply(\n", "        lambda row: row[\"picker_idle_time_15\"]\n", "        if ((row[\"picker_utilisation_15\"] > 0) or (row[\"pickers_15\"] > 1))\n", "        else (\n", "            row[\"picker_idle_time_30\"]\n", "            if ((row[\"picker_utilisation_30\"] > 0) or (row[\"pickers_30\"] > 1))\n", "            else row[\"picker_idle_time_60\"]\n", "        ),\n", "        axis=1,\n", "    )\n", "    df_utilisation_60[\"picker_utilisation\"] = df_utilisation_60.apply(\n", "        lambda row: row[\"picker_utilisation_15\"]\n", "        if ((row[\"picker_utilisation_15\"] > 0) or (row[\"pickers_15\"] > 1))\n", "        else (\n", "            row[\"picker_utilisation_30\"]\n", "            if ((row[\"picker_utilisation_30\"] > 0) or (row[\"pickers_30\"] > 1))\n", "            else row[\"picker_utilisation_60\"]\n", "        ),\n", "        axis=1,\n", "    )\n", "    df_utilisation_60[\"picker_window\"] = df_utilisation_60.apply(\n", "        lambda row: 15\n", "        if ((row[\"picker_utilisation_15\"] > 0) or (row[\"pickers_15\"] > 1))\n", "        else (\n", "            30\n", "            if ((row[\"picker_utilisation_30\"] > 0) or (row[\"pickers_30\"] > 1))\n", "            else 60\n", "        ),\n", "        axis=1,\n", "    )\n", "else:\n", "    df_utilisation_60 = pd.DataFrame(\n", "        columns=[\n", "            \"city\",\n", "            \"merchant_id\",\n", "            \"merchant_name\",\n", "            \"pickers_60\",\n", "            \"picker_idle_time_60\",\n", "            \"picker_utilisation_60\",\n", "            \"pickers_30\",\n", "            \"picker_utilisation_30\",\n", "            \"picker_idle_time_30\",\n", "            \"pickers_15\",\n", "            \"picker_utilisation_15\",\n", "            \"picker_idle_time_15\",\n", "            \"picker_window\",\n", "            \"pickers\",\n", "            \"picker_idle_time\",\n", "            \"picker_utilisation\",\n", "            \"gsps\",\n", "            \"gsp_idle_time\",\n", "            \"gsp_utilisation\",\n", "            \"gsps_60\",\n", "            \"gsp_idle_time_60\",\n", "            \"gsp_utilisation_60\",\n", "            \"gsps_30\",\n", "            \"gsp_utilisation_30\",\n", "            \"gsp_idle_time_30\",\n", "            \"gsps_15\",\n", "            \"gsp_utilisation_15\",\n", "            \"gsp_idle_time_15\",\n", "            \"gsp_window\",\n", "        ]\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_utilisation_60[df_utilisation_60[\"picker_window\"] != 15][\n", "    [\n", "        \"city\",\n", "        \"merchant_id\",\n", "        \"merchant_name\",\n", "        \"pickers_60\",\n", "        \"picker_idle_time_60\",\n", "        \"picker_utilisation_60\",\n", "        \"pickers_30\",\n", "        \"picker_utilisation_30\",\n", "        \"picker_idle_time_30\",\n", "        \"pickers_15\",\n", "        \"picker_utilisation_15\",\n", "        \"picker_idle_time_15\",\n", "        \"picker_window\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_utilisation_60[df_utilisation_60[\"gsp_window\"] != 15][\n", "    [\n", "        \"city\",\n", "        \"merchant_id\",\n", "        \"merchant_name\",\n", "        \"gsps_60\",\n", "        \"gsp_idle_time_60\",\n", "        \"gsp_utilisation_60\",\n", "        \"gsps_30\",\n", "        \"gsp_utilisation_30\",\n", "        \"gsp_idle_time_30\",\n", "        \"gsps_15\",\n", "        \"gsp_utilisation_15\",\n", "        \"gsp_idle_time_15\",\n", "        \"gsp_window\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_utilisation_60 = df_utilisation_60[\n", "    [\n", "        \"city\",\n", "        \"merchant_id\",\n", "        \"merchant_name\",\n", "        \"pickers\",\n", "        \"picker_idle_time\",\n", "        \"picker_utilisation\",\n", "        \"gsps\",\n", "        \"gsp_idle_time\",\n", "        \"gsp_utilisation\",\n", "    ]\n", "]\n", "df_utilisation = copy.deepcopy(df_utilisation_60)\n", "df_utilisation.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pending = \"\"\"with picking_pending as (SELECT (payload::json ->> 'store_id') AS merchant_id,\n", "                                   label_value AS order_id\n", "   FROM logistics_allocation_request lar\n", "   LEFT JOIN logistics_allocation_batch lab ON lab.id=lar.allocation_batch_id\n", "   AND lab.type='AUTO_PICKER_ALLOCATION_REQUEST'\n", "   LEFT JOIN logistics_shipment_allocation sa ON (payload::json ->> 'shipment_id') = sa.shipment_id::text\n", "   WHERE lar.install_ts >= CURRENT_DATE - 1\n", "     AND lar.type='AUTO_PICKER_ALLOCATION_REQUEST'\n", "     AND lar.state IN ('CREATED')\n", "     AND sa.state IN ('ALLOCATION_PENDING') ),\n", "\n", "delivery_pending as (\n", "SELECT (payload::json ->> 'store_id') AS merchant_id,\n", "       label_value AS order_id\n", "FROM logistics_allocation_request lar\n", "LEFT JOIN logistics_allocation_batch lab ON lab.id=lar.allocation_batch_id\n", "   AND lab.type='AUTO_TRIP_ALLOCATION_REQUEST'\n", "LEFT JOIN logistics_shipment_allocation sa ON (payload::json ->> 'shipment_id') = sa.shipment_id::text\n", "WHERE lar.install_ts >= CURRENT_DATE - 1\n", "  AND lar.type in ('AUTO_TRIP_ALLOCATION_REQUEST','AUTO_3PL_DELIVERER_ALLOCATION_REQUEST')\n", "  AND lar.state IN ('CREATED','THIRD_PARTY_PROCESSING')\n", "  AND sa.state IN ('ALLOCATION_PENDING') \n", "),\n", "\n", "\n", "store_pending as (select ln.external_id as merchant_id,\n", "                    ln.external_name as merchant_name,\n", "                    count(DISTINCT p.order_id) AS picking_pending,\n", "                    count(DISTINCT d.order_id) AS delivery_pending\n", "                from logistics_node ln \n", "                left join picking_pending p on ln.external_id = p.merchant_id\n", "                left join delivery_pending d on ln.external_id = d.merchant_id\n", "                group by 1,2),\n", "                \n", "queue as (SELECT ln.external_id AS merchant_id,\n", "                            ln.external_name AS merchant_name,\n", "                            coalesce(count(DISTINCT CASE\n", "                                                        WHEN user_type ILIKE '%%picker%%' THEN q.user_profile_id\n", "                                                        ELSE NULL\n", "                                                    END),0) AS pickers,\n", "                            coalesce(count(DISTINCT CASE\n", "                                                        WHEN user_type NOT ILIKE '%%picker%%' THEN q.user_profile_id\n", "                                                        ELSE NULL\n", "                                                    END),0) AS gsps\n", "            FROM logistics_express_allocation_field_executive_queue q\n", "            INNER JOIN logistics_user_profile_node lpn ON q.user_profile_id = lpn.user_profile_id::text\n", "            AND (entry_ts) >= (CURRENT_TIMESTAMP)::date\n", "            AND active = TRUE\n", "            INNER JOIN logistics_node ln ON lpn.node_id = ln.id\n", "            GROUP BY 1,2)\n", "            \n", "select s.merchant_id,picking_pending as unassigned_picking,\n", "delivery_pending as unassigned_delivery,\n", "CASE\n", "              WHEN pickers IS NULL THEN 0\n", "              ELSE pickers\n", "          END AS pickers_in_queue,\n", "          CASE\n", "              WHEN gsps IS NULL THEN 0\n", "              ELSE gsps\n", "          END AS gsps_in_queue\n", "from store_pending s \n", "left join queue q on s.merchant_id = q.merchant_id\"\"\"\n", "df_pending_allocation = read_sql(pending, pb.get_connection(\"[Replica] Logistic DB\"))\n", "df_pending_allocation.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df_utilisation.shape[0] > 0:\n", "    df_pending_allocation[\"merchant_id\"] = df_pending_allocation[\"merchant_id\"].astype(\n", "        int\n", "    )\n", "    df_utilisation = df_utilisation.merge(\n", "        df_pending_allocation, how=\"left\", on=\"merchant_id\"\n", "    )\n", "    df_utilisation[\"pickers_in_queue\"] = df_utilisation[\"pickers_in_queue\"].fillna(0)\n", "    df_utilisation[\"gsps_in_queue\"] = df_utilisation[\"gsps_in_queue\"].fillna(0)\n", "    df_utilisation[\"unassigned_picking\"] = df_utilisation[\"unassigned_picking\"].fillna(\n", "        0\n", "    )\n", "    df_utilisation[\"unassigned_delivery\"] = df_utilisation[\n", "        \"unassigned_delivery\"\n", "    ].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_utilisation.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df_utilisation.shape[0] > 0:\n", "    df_utilisation[\"picker_utilisation\"] = df_utilisation.apply(\n", "        lambda row: 0 if (row[\"picker_utilisation\"] < 0) else row[\"picker_utilisation\"],\n", "        axis=1,\n", "    )\n", "    df_utilisation[\"gsp_utilisation\"] = df_utilisation.apply(\n", "        lambda row: 0 if (row[\"gsp_utilisation\"] < 0) else row[\"gsp_utilisation\"],\n", "        axis=1,\n", "    )\n", "df_utilisation = df_utilisation[\n", "    df_utilisation[\"merchant_id\"].isin(input_merchant_list)\n", "].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_utilisation[\"picker_utilisation\"] = round(df_utilisation[\"picker_utilisation\"], 2)\n", "df_utilisation[\"gsp_utilisation\"] = round(df_utilisation[\"gsp_utilisation\"], 2)\n", "\n", "df_utilisation[\"unassigned_picking\"] = df_utilisation[\"unassigned_picking\"].fillna(0)\n", "df_utilisation[\"unassigned_delivery\"] = df_utilisation[\"unassigned_delivery\"].fillna(0)\n", "df_utilisation[\"pickers_in_queue\"] = df_utilisation[\"pickers_in_queue\"].fillna(0)\n", "df_utilisation[\"gsps_in_queue\"] = df_utilisation[\"gsps_in_queue\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lg_merchant_name = \"\"\"select distinct ln.external_id::int as merchant_id,ln.external_name as merchant_name from logistics_node ln\"\"\"\n", "df_merchant_name = pd.read_sql(\n", "    lg_merchant_name, pb.get_connection(\"[Replica] Logistic DB\")\n", ")\n", "df_merchant_name.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["slots = \"\"\"select city_name as city,merchant_external_id as merchant_id,\n", "(slot_freeze_time+interval '5.5 hours') as slot_free_time,(slot_start+interval '5.5 hours')::date as date,\n", "(slot_start+interval '5.5 hours')::timestamp as slot_start,\n", "(slot_end+interval '5.5 hours')::timestamp as slot_end,\n", "TO_CHAR((slot_start+interval '5.5 hours'),'HH24MI')||' - '||TO_CHAR((slot_end+interval '5.5 hours'),'HH24MI') as slot,\n", "extract(epoch from (slot_end - slot_start))/60 as slot_duration,\n", "merchant_asked,\n", "case when merchant_available < 0 then 0 else merchant_available end as merchant_available\n", "from \n", "sco_path_capacity\n", "where slot_freeze_time > now()\n", "and slot_start <= current_date + 2\n", "and merchant_asked > 0\n", "and active = 'true'\n", "and slot_type = 'normal'\n", "order by 1,2,3,4,5,6\"\"\"\n", "df_slots = read_sql(slots, pb.get_connection(\"[Replica] Supply Orchestrator\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_slots = df_slots.merge(df_merchant_name, how=\"inner\", on=\"merchant_id\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_slots[\"date\"] = df_slots.apply(\n", "    lambda row: pd.to_datetime(row[\"date\"]).strftime(\"%Y-%m-%d\"), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_slots[\"utilisation\"] = df_slots.apply(\n", "    lambda row: (row[\"merchant_asked\"] - row[\"merchant_available\"])\n", "    / row[\"merchant_asked\"],\n", "    axis=1,\n", ")\n", "df_slots[\"utilised_flag\"] = df_slots.apply(\n", "    lambda row: \"utilised\" if row[\"utilisation\"] >= 1 else \"not_utilised\",\n", "    axis=1,\n", ")\n", "\n", "df_slots[\"slot_number\"] = (\n", "    df_slots.groupby([\"city\", \"merchant_id\", \"merchant_name\"]).cumcount() + 1\n", ")\n", "df_slots[\"slot_number1\"] = (\n", "    df_slots[df_slots[\"utilised_flag\"] == \"not_utilised\"]\n", "    .groupby([\"city\", \"merchant_id\", \"merchant_name\"])\n", "    .cumcount()\n", "    + 1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df_slots = df_slots[\n", "#     ~((df_slots[\"slot_number\"] <= 2) & (df_slots[\"date\"] > today))\n", "# ].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_slots = df_slots[df_slots[\"utilised_flag\"] == \"not_utilised\"]\n", "merchants_with_high_pt = df_slots[\n", "    (df_slots[\"slot_number1\"] == 1) & (df_slots[\"slot_number\"] >= 300)\n", "].reset_index(drop=True)\n", "merchants_with_low_pt = df_slots[(df_slots[\"slot_number1\"] == 1)].reset_index(drop=True)\n", "#     ((df_slots[\"slot_number1\"] == 1) & (df_slots[\"slot_number\"] == 1))\n", "#     | ((df_slots[\"slot_number1\"] == 1) & (df_slots[\"slot_number\"] == 2))\n", "# ].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchants_with_high_pt.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchants_with_low_pt.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["express_merchants = \"\"\"select gm.id as merchant_id,chain_id from lake_cms.view_gr_merchant gm where active_flag = true\"\"\"\n", "df_express_list = pd.read_sql(\n", "    express_merchants, pb.get_connection(\"[Warehouse] Redshift\")\n", ")\n", "express_list = df_express_list[\"merchant_id\"].unique().tolist()\n", "\n", "if merchants_with_high_pt.shape[0] > 0:\n", "    merchants_with_high_pt = merchants_with_high_pt[\n", "        merchants_with_high_pt[\"merchant_id\"].isin(express_list)\n", "    ].reset_index(drop=True)\n", "    for index, row in merchants_with_high_pt.iterrows():\n", "        for index1, row1 in high_pt_thresholds.iterrows():\n", "            if row[\"slot_number\"] >= row1[\"slot_number\"]:\n", "                merchants_with_high_pt.at[index, \"slot_charge\"] = row1[\"charge\"]\n", "    #     merchants_with_high_pt[\"slot_charge\"] = 30\n", "    merchants_with_high_pt[\"day_count\"] = merchants_with_high_pt.apply(\n", "        lambda row: (\n", "            pd.to_datetime(row[\"slot_start\"]).date()\n", "            - pd.to_datetime(datetime.now() + timedelta(hours=5, minutes=30)).date()\n", "        ).days,\n", "        axis=1,\n", "    )\n", "    merchants_with_high_pt[\"slot_charge\"] = merchants_with_high_pt[\n", "        \"slot_charge\"\n", "    ].astype(\"Int64\")\n", "if merchants_with_low_pt.shape[0] > 0:\n", "    merchants_with_low_pt = merchants_with_low_pt[\n", "        merchants_with_low_pt[\"merchant_id\"].isin(express_list)\n", "    ].reset_index(drop=True)\n", "    merchants_with_low_pt[\"day_count\"] = merchants_with_low_pt.apply(\n", "        lambda row: (\n", "            pd.to_datetime(row[\"slot_start\"]).date()\n", "            - pd.to_datetime(datetime.now() + timedelta(hours=5, minutes=30)).date()\n", "        ).days,\n", "        axis=1,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchants_with_high_pt"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_utilisation[\"merchant_id\"] = (\n", "    df_utilisation[\"merchant_id\"].astype(int).astype(\"Int64\")\n", ")\n", "df_express_list[\"merchant_id\"] = (\n", "    df_express_list[\"merchant_id\"].astype(int).astype(\"Int64\")\n", ")\n", "df_utilisation = df_utilisation.merge(df_express_list, how=\"left\", on=\"merchant_id\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_utilisation = df_utilisation[df_utilisation[\"chain_id\"] == 1383].reset_index(\n", "    drop=True\n", ")\n", "df_utilisation = df_utilisation[\n", "    (df_utilisation[\"pickers\"] > 0) | (df_utilisation[\"gsps\"] > 0)\n", "].reset_index(drop=True)\n", "if merchants_with_low_pt.shape[0] > 0:\n", "    df_low_pt_utilisation = df_utilisation.merge(\n", "        merchants_with_low_pt[\n", "            [\n", "                \"merchant_id\",\n", "                \"date\",\n", "                \"slot\",\n", "                \"utilisation\",\n", "                \"slot_number\",\n", "                \"slot_number1\",\n", "                \"day_count\",\n", "            ]\n", "        ],\n", "        how=\"inner\",\n", "        on=\"merchant_id\",\n", "    )\n", "else:\n", "    df_low_pt_utilisation = pd.DataFrame()\n", "if merchants_with_high_pt.shape[0] > 0:\n", "    df_high_pt_utilisation = df_express_list[df_express_list[\"chain_id\"] == 1383].merge(\n", "        merchants_with_high_pt, how=\"inner\", on=\"merchant_id\"\n", "    )\n", "    df_high_pt_utilisation = df_high_pt_utilisation[\n", "        df_high_pt_utilisation[\"merchant_id\"].isin(input_merchant_list)\n", "    ].reset_index(drop=True)\n", "else:\n", "    df_high_pt_utilisation = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def upload_df(df):\n", "    pivot_df = pd.pivot_table(\n", "        df,\n", "        index=[\"merchant_id\", \"slot\"],\n", "        columns=[\"upload_day_count\"],\n", "        values=[\"slot_charge\"],\n", "    )\n", "    pivot_df = pd.DataFrame(pivot_df.to_records())\n", "    pivot_df.columns = pivot_df.columns.str.replace(\"\\('slot_charge', '\", \"\")\n", "    pivot_df.columns = pivot_df.columns.str.replace(\"'\\)\", \"\")\n", "    exclude_cols = [\"merchant_id\"]\n", "    day_columns = [\"T\"]\n", "    for i in range(1, 31):\n", "        day_columns.append(\"T+\" + str(i))\n", "    for col in day_columns:\n", "        if col in pivot_df.columns:\n", "            pivot_df[col] = pivot_df[col].fillna(0)\n", "            pivot_df[col] = pivot_df[col].astype(int).astype(\"Int64\")\n", "        else:\n", "            pivot_df[col] = 0\n", "            pivot_df[col] = pivot_df[col].astype(int).astype(\"Int64\")\n", "    return pivot_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df_low_pt_utilisation.shape[0] > 0:\n", "    df_low_pt_utilisation[\"max_utilisation\"] = df_low_pt_utilisation.apply(\n", "        lambda row: max(row[\"picker_utilisation\"], row[\"gsp_utilisation\"]), axis=1\n", "    )\n", "    df_low_pt_utilisation[\"bottleneck\"] = df_low_pt_utilisation.apply(\n", "        lambda row: \"pickers\"\n", "        if (row[\"picker_utilisation\"] > row[\"gsp_utilisation\"])\n", "        else (\n", "            \"gsps\" if (row[\"gsp_utilisation\"] > row[\"picker_utilisation\"]) else \"both\"\n", "        ),\n", "        axis=1,\n", "    )\n", "    df_low_pt_utilisation[\"picker_charge\"] = None\n", "    df_low_pt_utilisation[\"gsp_charge\"] = None\n", "    df_low_pt_utilisation[\"picker_override_flag\"] = None\n", "    df_low_pt_utilisation[\"gsp_override_flag\"] = None\n", "    for index, row in df_low_pt_utilisation.iterrows():\n", "        if row[\"merchant_id\"] in (picker_override_merchants):\n", "            df_low_pt_utilisation.at[index, \"picker_override_flag\"] = \"override\"\n", "            picker_override_thresholds_temp = picker_override_thresholds[\n", "                picker_override_thresholds[\"merchant_id\"] == row[\"merchant_id\"]\n", "            ].reset_index(drop=True)\n", "            for index1, row1 in picker_override_thresholds_temp.iterrows():\n", "                if (row[\"picker_utilisation\"] >= row1[\"utilisation_lower_limit\"]) and (\n", "                    row[\"picker_utilisation\"] <= row1[\"utilisation_upper_limit\"]\n", "                ):\n", "                    df_low_pt_utilisation.at[index, \"picker_charge\"] = row1[\n", "                        \"slot_charge\"\n", "                    ]\n", "        else:\n", "            for index1, row1 in picker_thresholds.iterrows():\n", "                if (row[\"picker_utilisation\"] >= row1[\"utilisation_lower_limit\"]) and (\n", "                    row[\"picker_utilisation\"] <= row1[\"utilisation_upper_limit\"]\n", "                ):\n", "                    df_low_pt_utilisation.at[index, \"picker_charge\"] = row1[\n", "                        \"slot_charge\"\n", "                    ]\n", "\n", "    for index, row in df_low_pt_utilisation.iterrows():\n", "        if row[\"merchant_id\"] in (gsp_override_merchants):\n", "            df_low_pt_utilisation.at[index, \"gsp_override_flag\"] = \"override\"\n", "            gsp_override_thresholds_temp = gsp_override_thresholds[\n", "                gsp_override_thresholds[\"merchant_id\"] == row[\"merchant_id\"]\n", "            ].reset_index(drop=True)\n", "            for index1, row1 in gsp_override_thresholds_temp.iterrows():\n", "                if (row[\"gsp_utilisation\"] >= row1[\"utilisation_lower_limit\"]) and (\n", "                    row[\"gsp_utilisation\"] <= row1[\"utilisation_upper_limit\"]\n", "                ):\n", "                    df_low_pt_utilisation.at[index, \"gsp_charge\"] = row1[\"slot_charge\"]\n", "        else:\n", "            for index1, row1 in gsp_thresholds.iterrows():\n", "                if (row[\"gsp_utilisation\"] >= row1[\"utilisation_lower_limit\"]) and (\n", "                    row[\"gsp_utilisation\"] <= row1[\"utilisation_upper_limit\"]\n", "                ):\n", "                    df_low_pt_utilisation.at[index, \"gsp_charge\"] = row1[\"slot_charge\"]\n", "\n", "    df_low_pt_utilisation[\"final_charge\"] = df_low_pt_utilisation.apply(\n", "        lambda row: max(row[\"picker_charge\"], row[\"gsp_charge\"]), axis=1\n", "    )\n", "    df_low_pt_utilisation[\"slot_charge\"] = df_low_pt_utilisation.apply(\n", "        lambda row: row[\"final_charge\"]\n", "        if (\n", "            (\n", "                row[\"bottleneck\"] in [\"pickers\", \"both\"]\n", "                and ((row[\"unassigned_picking\"] - row[\"pickers_in_queue\"]) > 4)\n", "                and row[\"unassigned_picking\"] > 4\n", "            )\n", "            or (row[\"picker_override_flag\"] == \"override\")\n", "        )\n", "        else (\n", "            row[\"final_charge\"]\n", "            if (\n", "                (\n", "                    (row[\"bottleneck\"] == \"gsps\")\n", "                    and ((row[\"unassigned_delivery\"] - row[\"gsps_in_queue\"]) > 3)\n", "                    and row[\"unassigned_delivery\"] > 3\n", "                )\n", "                or (row[\"gsp_override_flag\"] == \"override\")\n", "            )\n", "            else 0\n", "        ),\n", "        axis=1,\n", "    )\n", "    low_pt_mail_df = (\n", "        df_low_pt_utilisation[\n", "            [\n", "                \"city\",\n", "                \"merchant_id\",\n", "                \"merchant_name\",\n", "                \"date\",\n", "                \"slot\",\n", "                \"slot_charge\",\n", "                \"bottleneck\",\n", "            ]\n", "        ]\n", "        .drop_duplicates()\n", "        .reset_index(drop=True)\n", "    )\n", "    low_pt_mail_df = low_pt_mail_df[low_pt_mail_df[\"slot_charge\"] > 0].reset_index(\n", "        drop=True\n", "    )\n", "    df_low_pt_utilisation[\"upload_day_count\"] = df_low_pt_utilisation.apply(\n", "        lambda row: \"T\" if (row[\"day_count\"] == 0) else \"T+\" + str(row[\"day_count\"]),\n", "        axis=1,\n", "    )\n", "    debug_df = (\n", "        df_low_pt_utilisation[\n", "            [\n", "                \"city\",\n", "                \"merchant_id\",\n", "                \"merchant_name\",\n", "                \"pickers\",\n", "                \"picker_idle_time\",\n", "                \"picker_utilisation\",\n", "                \"gsps\",\n", "                \"gsp_idle_time\",\n", "                \"gsp_utilisation\",\n", "                \"pickers_in_queue\",\n", "                \"gsps_in_queue\",\n", "                \"unassigned_picking\",\n", "                \"unassigned_delivery\",\n", "                \"date\",\n", "                \"slot\",\n", "                \"utilisation\",\n", "                \"slot_number\",\n", "                \"day_count\",\n", "                \"slot_charge\",\n", "            ]\n", "        ]\n", "        .drop_duplicates()\n", "        .reset_index(drop=True)\n", "    )\n", "    append_df = (\n", "        df_low_pt_utilisation[\n", "            [\"city\", \"merchant_id\", \"merchant_name\", \"slot\", \"day_count\", \"slot_charge\"]\n", "        ]\n", "        .drop_duplicates()\n", "        .reset_index(drop=True)\n", "    )\n", "else:\n", "    debug_df = pd.DataFrame()\n", "    low_pt_mail_df = pd.DataFrame()\n", "#     append_df = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df_high_pt_utilisation.shape[0] > 0:\n", "    high_pt_mail_df = (\n", "        df_high_pt_utilisation[\n", "            [\"city\", \"merchant_id\", \"merchant_name\", \"date\", \"slot\", \"slot_charge\"]\n", "        ]\n", "        .drop_duplicates()\n", "        .reset_index(drop=True)\n", "    )\n", "    df_high_pt_utilisation[\"upload_day_count\"] = df_high_pt_utilisation.apply(\n", "        lambda row: \"T\" if (row[\"day_count\"] == 0) else \"T+\" + str(row[\"day_count\"]),\n", "        axis=1,\n", "    )\n", "    if df_low_pt_utilisation.shape[0] > 0:\n", "        append_df = (\n", "            append_df.append(\n", "                df_high_pt_utilisation[\n", "                    [\n", "                        \"city\",\n", "                        \"merchant_id\",\n", "                        \"merchant_name\",\n", "                        \"slot\",\n", "                        \"day_count\",\n", "                        \"slot_charge\",\n", "                    ]\n", "                ]\n", "            )\n", "            .drop_duplicates()\n", "            .reset_index(drop=True)\n", "        )\n", "    else:\n", "        append_df = (\n", "            df_high_pt_utilisation[\n", "                [\n", "                    \"city\",\n", "                    \"merchant_id\",\n", "                    \"merchant_name\",\n", "                    \"slot\",\n", "                    \"day_count\",\n", "                    \"slot_charge\",\n", "                ]\n", "            ]\n", "            .drop_duplicates()\n", "            .reset_index(drop=True)\n", "        )\n", "    df_high_pt_utilisation[\"install_ts_ist\"] = install_ts_ist\n", "    df_high_pt_utilisation.rename(\n", "        columns={\"utilisation\": \"slot_utilisation\"}, inplace=True\n", "    )\n", "    df_high_pt_append = (\n", "        df_high_pt_utilisation[\n", "            [\n", "                \"install_ts_ist\",\n", "                \"city\",\n", "                \"merchant_id\",\n", "                \"merchant_name\",\n", "                \"date\",\n", "                \"slot\",\n", "                \"slot_utilisation\",\n", "                \"day_count\",\n", "                \"slot_number\",\n", "                \"slot_charge\",\n", "            ]\n", "        ]\n", "        .drop_duplicates()\n", "        .reset_index(drop=True)\n", "    )\n", "\n", "else:\n", "    high_pt_mail_df = pd.DataFrame()\n", "    df_high_pt_append = pd.DataFrame()\n", "#     append_df = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df_low_pt_utilisation.shape[0] > 0:\n", "    upload_low_pt = upload_df(df_low_pt_utilisation)\n", "else:\n", "    upload_low_pt = pd.DataFrame()\n", "\n", "if df_high_pt_utilisation.shape[0] > 0:\n", "    upload_high_pt = upload_df(df_high_pt_utilisation)\n", "else:\n", "    upload_high_pt = pd.DataFrame()\n", "upload_final = upload_high_pt.append(upload_low_pt, sort=False).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if debug_df.shape[0] > 0:\n", "    debug_df[\"utilisation\"] = round(debug_df[\"utilisation\"], 2)\n", "    debug_df[\"picker_idle_time\"] = round(debug_df[\"picker_idle_time\"], 2)\n", "    debug_df[\"gsp_idle_time\"] = round(debug_df[\"gsp_idle_time\"], 2)\n", "    debug_df[\"install_ts_ist\"] = install_ts_ist\n", "    debug_df = debug_df[\n", "        [\n", "            \"install_ts_ist\",\n", "            \"city\",\n", "            \"merchant_id\",\n", "            \"merchant_name\",\n", "            \"pickers\",\n", "            \"picker_idle_time\",\n", "            \"picker_utilisation\",\n", "            \"gsps\",\n", "            \"gsp_idle_time\",\n", "            \"gsp_utilisation\",\n", "            \"pickers_in_queue\",\n", "            \"gsps_in_queue\",\n", "            \"unassigned_picking\",\n", "            \"unassigned_delivery\",\n", "            \"date\",\n", "            \"slot\",\n", "            \"utilisation\",\n", "            \"slot_number\",\n", "            \"day_count\",\n", "            \"slot_charge\",\n", "        ]\n", "    ].reset_index(drop=True)\n", "    debug_df[\"pickers_in_queue\"] = debug_df[\"pickers_in_queue\"].astype(\"Int64\")\n", "    debug_df[\"gsps_in_queue\"] = debug_df[\"gsps_in_queue\"].astype(\"Int64\")\n", "    debug_df[\"unassigned_picking\"] = debug_df[\"unassigned_picking\"].astype(\"Int64\")\n", "    debug_df[\"unassigned_delivery\"] = debug_df[\"unassigned_delivery\"].astype(\"Int64\")\n", "    debug_df.rename(columns={\"utilisation\": \"slot_utilisation\"}, inplace=True)\n", "\n", "if append_df.shape[0] > 0:\n", "    append_df[\"install_ts_ist\"] = install_ts_ist\n", "    append_df = append_df[\n", "        [\n", "            \"install_ts_ist\",\n", "            \"city\",\n", "            \"merchant_id\",\n", "            \"merchant_name\",\n", "            \"slot\",\n", "            \"day_count\",\n", "            \"slot_charge\",\n", "        ]\n", "    ].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if low_pt_mail_df.shape[0] > 0:\n", "    low_pt_mail_df.to_csv(MAIL_UPLOAD, index=False)\n", "\n", "if high_pt_mail_df.shape[0] > 0:\n", "    high_pt_mail_df.to_csv(MAIL_UPLOAD_1, index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["low_pt_mail_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["high_pt_mail_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["upload_final"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if upload_final.shape[0] > 0:\n", "    sheet_upload = upload_final.drop([\"slot\"], axis=\"columns\")\n", "    sheet_upload = sheet_upload.drop_duplicates(\n", "        subset=[\"merchant_id\"], keep=\"first\"\n", "    ).reset_index(drop=True)\n", "    sheet_upload[\"merged\"] = (\n", "        sheet_upload[\"T\"] + sheet_upload[\"T+1\"] + sheet_upload[\"T+2\"]\n", "    )\n", "    sheet_upload = sheet_upload.merge(df_merchant_name, on=\"merchant_id\", how=\"inner\")\n", "    sheet_upload = sheet_upload[[\"merchant_id\", \"merchant_name\", \"merged\"]]\n", "    sheet_upload.rename(\n", "        columns={\"merged\": \"charge\", \"merchant_id\": \"frontend_merchant_id\"},\n", "        inplace=True,\n", "    )\n", "    express = \"select id as frontend_merchant_id,name as merchant_name from lake_cms.view_gr_merchant where chain_id = 1383 and active_flag = true\"\n", "    df_express = pd.read_sql(express, pb.get_connection(\"[Warehouse] Redshift\"))\n", "    df_express[\"frontend_merchant_id\"] = df_express[\"frontend_merchant_id\"].astype(int)\n", "    sheet_upload[\"frontend_merchant_id\"] = sheet_upload[\"frontend_merchant_id\"].astype(\n", "        int\n", "    )\n", "    sheet_upload = df_express.merge(\n", "        sheet_upload[[\"frontend_merchant_id\", \"charge\"]],\n", "        how=\"left\",\n", "        on=\"frontend_merchant_id\",\n", "    )\n", "    sheet_upload[\"charge\"] = sheet_upload[\"charge\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["express_stores = \"\"\"\n", "select distinct gm.id as merchant_id,gl.name as city from lake_cms.view_gr_merchant gm\n", "join lake_cms.view_gr_locality gl on gm.locality_id=gl.id\n", "where gm.chain_id=1383 and gm.name not like '%% FS%%'\n", "\"\"\"\n", "express_stores_df = read_sql(\n", "    express_stores, connection=pb.get_connection(\"[Warehouse] Redshift\")\n", ")\n", "express_stores_df[\"merchant_id\"] = express_stores_df[\"merchant_id\"].astype(\"Int64\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store_data = \"\"\"\n", "with active_store as (\n", "select  ln.external_id as merchant_id,\n", "        ln.external_name as merchant_name\n", "        from logistics_node ln\n", "        where active in ('true')\n", "),\n", "\n", "manpower as (\n", "select ln.external_id as merchant_id,\n", "                         coalesce(count(distinct case when lup.type in ('GEP', 'GSP') then employee_id else '0' end),0) as gsps_in_roster,\n", "                         coalesce(count(distinct case when lup.type in ('PICKER') then employee_id else '0' end),0) as pickers_in_roster\n", "                from logistics_node ln\n", "                left join logistics_user_profile_node lupn on lupn.node_id = ln.id\n", "                inner join logistics_user_profile lup on lup.id = lupn.user_profile_id\n", "                inner join logistics_user_roster ur on ur.user_profile_id = lup.id\n", "                      and ur.published_shift_end = false\n", "                      and ur.published_shift_start = true\n", "                      and ur.start_time >= (current_timestamp)::date\n", "                      and (ur.meta ->> 'type') in ('EXPRESS', 'SUPER_EXPRESS')\n", "                  group by 1),\n", "                  \n", "                  \n", "picking_pending as (SELECT (payload::json ->> 'store_id') AS merchant_id,\n", "                                   label_value AS order_id\n", "   FROM logistics_allocation_request lar\n", "   LEFT JOIN logistics_allocation_batch lab ON lab.id=lar.allocation_batch_id\n", "   AND lab.type='AUTO_PICKER_ALLOCATION_REQUEST'\n", "   LEFT JOIN logistics_shipment_allocation sa ON (payload::json ->> 'shipment_id') = sa.shipment_id::text\n", "   WHERE lar.install_ts >= CURRENT_DATE - 1\n", "     AND lar.type='AUTO_PICKER_ALLOCATION_REQUEST'\n", "     AND lar.state IN ('CREATED')\n", "     AND sa.state IN ('ALLOCATION_PENDING') ),\n", "\n", "delivery_pending as (\n", "SELECT (payload::json ->> 'store_id') AS merchant_id,\n", "       label_value AS order_id\n", "FROM logistics_allocation_request lar\n", "LEFT JOIN logistics_allocation_batch lab ON lab.id=lar.allocation_batch_id\n", "   AND lab.type='AUTO_TRIP_ALLOCATION_REQUEST'\n", "LEFT JOIN logistics_shipment_allocation sa ON (payload::json ->> 'shipment_id') = sa.shipment_id::text\n", "WHERE lar.install_ts >= CURRENT_DATE - 1\n", "  AND lar.type in ('AUTO_TRIP_ALLOCATION_REQUEST','AUTO_3PL_DELIVERER_ALLOCATION_REQUEST')\n", "  AND lar.state IN ('CREATED','THIRD_PARTY_PROCESSING')\n", "  AND sa.state IN ('ALLOCATION_PENDING') \n", "),\n", "\n", "\n", "store_pending as (select ln.external_id as merchant_id,\n", "                    ln.external_name as merchant_name,\n", "                    count(DISTINCT p.order_id) AS unassigned_picking,\n", "                    count(DISTINCT d.order_id) AS unassigned_delivery\n", "                from logistics_node ln \n", "                left join picking_pending p on ln.external_id = p.merchant_id\n", "                left join delivery_pending d on ln.external_id = d.merchant_id\n", "                group by 1,2),\n", "\n", "queue as (\n", "         SELECT DISTINCT ln.external_id   AS merchant_id,\n", "                         coalesce(count(DISTINCT CASE\n", "                            WHEN user_type NOT like '%%PICKER%%' THEN q.user_profile_id\n", "                            ELSE NULL\n", "                            END), 0)     AS gsps_in_queue,\n", "                         coalesce(count(DISTINCT CASE\n", "                            WHEN user_type like '%%PICKER%%' THEN q.user_profile_id\n", "                            ELSE NULL\n", "                             END), 0)     AS pickers_in_queue                             \n", "         FROM logistics_express_allocation_field_executive_queue q\n", "                  INNER JOIN logistics_user_profile_node lpn ON q.user_profile_id::int = lpn.user_profile_id\n", "             AND (entry_ts) >= (current_date)::date\n", "             AND active = TRUE\n", "             and (meta ->> 'roster_type') in ('EXPRESS', 'SUPER_EXPRESS')\n", "                  INNER JOIN logistics_node ln ON lpn.node_id = ln.id\n", "         GROUP BY 1\n", "     ),\n", "\n", "final as (SELECT acts.merchant_id,\n", "       acts.merchant_name,\n", "      case when s.unassigned_delivery is NULL then 0 else s.unassigned_delivery end as delivery_pending,\n", "      case when s.unassigned_picking is NULL then 0 else s.unassigned_picking end as picking_pending, \n", "      CASE\n", "          WHEN gsps_in_queue IS NULL THEN 0\n", "          ELSE gsps_in_queue\n", "          END          AS gsps_queue,\n", "      CASE\n", "          WHEN pickers_in_queue IS NULL THEN 0\n", "          ELSE pickers_in_queue\n", "          END          AS pickers_queue,\n", "      CASE\n", "          WHEN gsps_in_roster IS NULL THEN 0\n", "          ELSE gsps_in_roster\n", "          END          AS gsps_roster,\n", "      CASE\n", "          WHEN pickers_in_roster IS NULL THEN 0\n", "          ELSE pickers_in_roster\n", "          END          AS pickers_roster\n", "from active_store acts left join manpower m on acts.merchant_id=m.merchant_id\n", "left join store_pending s on s.merchant_id = m.merchant_id\n", "         LEFT JOIN queue q ON m.merchant_id = q.merchant_id)\n", "         \n", "select merchant_id,merchant_name,\n", "case when gsps_roster=0 then 0\n", "else (delivery_pending-(gsps_queue))*1.0/ gsps_roster end as disruption_index_delivery,\n", "case when pickers_roster=0 then 0\n", "else (picking_pending-pickers_queue)*1.0/pickers_roster end as disruption_index_picking\n", "from final\n", "         \"\"\"\n", "store_df = read_sql(store_data, connection=log_con)\n", "store_df[\"merchant_id\"] = store_df[\"merchant_id\"].astype(int)\n", "store_df = store_df.merge(express_stores_df, how=\"inner\", on=\"merchant_id\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gsp_thresholds_di_df = pb.from_sheets(\n", "    surge_sheet_id, gsp_thresholds_di, service_account=\"priority_account\"\n", ")\n", "gsp_thresholds_di_df[\"apply\"] = gsp_thresholds_di_df[\"apply\"].astype(float)\n", "gsp_thresholds_di_df[\"remove\"] = gsp_thresholds_di_df[\"remove\"].astype(float)\n", "gsp_thresholds_di_df[\"surge\"] = (\n", "    gsp_thresholds_di_df[\"surge\"].astype(int).astype(\"Int64\")\n", ")\n", "picker_thresholds_di_df = pb.from_sheets(\n", "    surge_sheet_id, picker_thresholds_di, service_account=\"priority_account\"\n", ")\n", "picker_thresholds_di_df[\"apply\"] = picker_thresholds_di_df[\"apply\"].astype(float)\n", "picker_thresholds_di_df[\"remove\"] = picker_thresholds_di_df[\"remove\"].astype(float)\n", "picker_thresholds_di_df[\"surge\"] = (\n", "    picker_thresholds_di_df[\"surge\"].astype(int).astype(\"Int64\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["previous = \"\"\"\n", "with previous_run_log as (\n", "select max(install_ts_ist) as install_ts_ist\n", "from metrics.surge_charge_di_based\n", ")\n", "select merchant_id,\n", "disruption_index_delivery as old_disruption_index_delivery,\n", "disruption_index_picking as old_disruption_index_picking\n", "from metrics.surge_charge_di_based s\n", "join previous_run_log l on s.install_ts_ist=l.install_ts_ist\n", "and s.install_ts_ist>=current_date-interval'5.5 hours'\n", "\"\"\"\n", "previous_df = read_sql(previous, connection=red_con)\n", "previous_df[\"merchant_id\"] = previous_df[\"merchant_id\"].astype(int)\n", "store_df = pd.merge(store_df, previous_df, on=\"merchant_id\", how=\"left\")\n", "store_df[\"old_disruption_index_delivery\"] = store_df[\n", "    \"old_disruption_index_delivery\"\n", "].fillna(0)\n", "store_df[\"old_disruption_index_picking\"] = store_df[\n", "    \"old_disruption_index_picking\"\n", "].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["override_thresholds_df = pb.from_sheets(\n", "    surge_sheet_id,\n", "    override_thresholds_di,\n", "    service_account=\"priority_account\",\n", ")\n", "override_thresholds_df[\"merchant_id\"] = (\n", "    override_thresholds_df[\"merchant_id\"].astype(int).astype(\"Int64\")\n", ")\n", "override_thresholds_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gsp_override_di_thresholds = override_thresholds_df[\n", "    [\"merchant_id\", \"merchant_name\", \"gsp_di_thresholds\"]\n", "]\n", "picker_override_di_thresholds = override_thresholds_df[\n", "    [\"merchant_id\", \"merchant_name\", \"picker_di_thresholds\"]\n", "]\n", "gsp_override_di_thresholds"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gsp_override_di_thresholds[\"gsp_di_thresholds\"] = gsp_override_di_thresholds[\n", "    \"gsp_di_thresholds\"\n", "].apply(lambda x: None if len(x) == 0 else x)\n", "picker_override_di_thresholds[\"picker_di_thresholds\"] = picker_override_di_thresholds[\n", "    \"picker_di_thresholds\"\n", "].apply(lambda x: None if len(x) == 0 else x)\n", "gsp_override_di_thresholds = gsp_override_di_thresholds[\n", "    gsp_override_di_thresholds[\"gsp_di_thresholds\"].notnull()\n", "]\n", "picker_override_di_thresholds = picker_override_di_thresholds[\n", "    picker_override_di_thresholds[\"picker_di_thresholds\"].notnull()\n", "]\n", "\n", "gsp_override_di_thresholds"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gsp_override_di_merchants = gsp_override_di_thresholds[\"merchant_id\"].to_list()\n", "picker_override_di_merchants = picker_override_di_thresholds[\"merchant_id\"].to_list()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gsp_override_di_thresholds[\"gsp_di_thresholds\"] = gsp_override_di_thresholds[\n", "    \"gsp_di_thresholds\"\n", "].apply(lambda x: x.strip().strip(\"][\").split(\",\\n\"))\n", "picker_override_di_thresholds[\"picker_di_thresholds\"] = picker_override_di_thresholds[\n", "    \"picker_di_thresholds\"\n", "].apply(lambda x: x.strip().strip(\"][\").split(\",\\n\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["p = (\n", "    picker_override_di_thresholds.apply(\n", "        lambda x: pd.Series(x[\"picker_di_thresholds\"]), axis=1\n", "    )\n", "    .stack()\n", "    .reset_index(level=1, drop=True)\n", ")\n", "p.name = \"picker_di_thresholds\"\n", "picker_override_di_thresholds = (\n", "    picker_override_di_thresholds.drop(\"picker_di_thresholds\", axis=1)\n", "    .join(p)\n", "    .reset_index(drop=True)\n", ")\n", "g = (\n", "    gsp_override_di_thresholds.apply(\n", "        lambda x: pd.Series(x[\"gsp_di_thresholds\"]), axis=1\n", "    )\n", "    .stack()\n", "    .reset_index(level=1, drop=True)\n", ")\n", "g.name = \"gsp_di_thresholds\"\n", "gsp_override_di_thresholds = (\n", "    gsp_override_di_thresholds.drop(\"gsp_di_thresholds\", axis=1)\n", "    .join(g)\n", "    .reset_index(drop=True)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picker_override_di_thresholds[\"picker_di_thresholds\"] = picker_override_di_thresholds[\n", "    \"picker_di_thresholds\"\n", "].apply(lambda x: json.loads(x))\n", "gsp_override_di_thresholds[\"gsp_di_thresholds\"] = gsp_override_di_thresholds[\n", "    \"gsp_di_thresholds\"\n", "].apply(lambda x: json.loads(x))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if picker_override_di_thresholds.shape[0] > 0:\n", "    picker_override_di_thresholds[\"di_apply\"] = picker_override_di_thresholds.apply(\n", "        lambda row: row[\"picker_di_thresholds\"][\"apply\"], axis=1\n", "    )\n", "    picker_override_di_thresholds[\"di_remove\"] = picker_override_di_thresholds.apply(\n", "        lambda row: row[\"picker_di_thresholds\"][\"remove\"], axis=1\n", "    )\n", "    picker_override_di_thresholds[\"surge\"] = picker_override_di_thresholds.apply(\n", "        lambda row: row[\"picker_di_thresholds\"][\"surge\"], axis=1\n", "    )\n", "if gsp_override_di_thresholds.shape[0] > 0:\n", "    gsp_override_di_thresholds[\"di_apply\"] = gsp_override_di_thresholds.apply(\n", "        lambda row: row[\"gsp_di_thresholds\"][\"apply\"], axis=1\n", "    )\n", "    gsp_override_di_thresholds[\"di_remove\"] = gsp_override_di_thresholds.apply(\n", "        lambda row: row[\"gsp_di_thresholds\"][\"remove\"], axis=1\n", "    )\n", "    gsp_override_di_thresholds[\"surge\"] = gsp_override_di_thresholds.apply(\n", "        lambda row: row[\"gsp_di_thresholds\"][\"surge\"], axis=1\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for index, row in store_df.iterrows():\n", "    if row[\"merchant_id\"] in (gsp_override_di_merchants):\n", "        gsp_override_di_thresholds_temp = gsp_override_di_thresholds[\n", "            gsp_override_di_thresholds[\"merchant_id\"] == row[\"merchant_id\"]\n", "        ].reset_index(drop=True)\n", "        for index1, row1 in gsp_override_di_thresholds_temp.iterrows():\n", "            if (\n", "                row[\"disruption_index_delivery\"] >= row[\"old_disruption_index_delivery\"]\n", "                and row[\"disruption_index_delivery\"] >= row1[\"di_apply\"]\n", "            ):\n", "                store_df.at[index, \"gsp_surge\"] = row1[\"surge\"]\n", "            if (\n", "                row[\"disruption_index_delivery\"] < row[\"old_disruption_index_delivery\"]\n", "                and row[\"disruption_index_delivery\"] >= row1[\"di_remove\"]\n", "            ):\n", "                store_df.at[index, \"gsp_surge\"] = row1[\"surge\"]\n", "    else:\n", "        for index1, row1 in gsp_thresholds_di_df.iterrows():\n", "            if (\n", "                row[\"disruption_index_delivery\"] >= row[\"old_disruption_index_delivery\"]\n", "                and row[\"disruption_index_delivery\"] >= row1[\"apply\"]\n", "            ):\n", "                store_df.at[index, \"gsp_surge\"] = row1[\"surge\"]\n", "            if (\n", "                row[\"disruption_index_delivery\"] < row[\"old_disruption_index_delivery\"]\n", "                and row[\"disruption_index_delivery\"] >= row1[\"remove\"]\n", "            ):\n", "                store_df.at[index, \"gsp_surge\"] = row1[\"surge\"]\n", "for index, row in store_df.iterrows():\n", "    if row[\"merchant_id\"] in (picker_override_di_merchants):\n", "        picker_override_di_thresholds_temp = picker_override_di_thresholds[\n", "            picker_override_di_thresholds[\"merchant_id\"] == row[\"merchant_id\"]\n", "        ].reset_index(drop=True)\n", "        for index1, row1 in picker_override_di_thresholds_temp.iterrows():\n", "            if (\n", "                row[\"disruption_index_picking\"] >= row[\"old_disruption_index_picking\"]\n", "                and row[\"disruption_index_picking\"] >= row1[\"di_apply\"]\n", "            ):\n", "                store_df.at[index, \"picker_surge\"] = row1[\"surge\"]\n", "            if (\n", "                row[\"disruption_index_picking\"] < row[\"old_disruption_index_picking\"]\n", "                and row[\"disruption_index_picking\"] >= row1[\"di_remove\"]\n", "            ):\n", "                store_df.at[index, \"picker_surge\"] = row1[\"surge\"]\n", "    else:\n", "        for index1, row1 in picker_thresholds_di_df.iterrows():\n", "            if (\n", "                row[\"disruption_index_picking\"] >= row[\"old_disruption_index_picking\"]\n", "                and row[\"disruption_index_picking\"] >= row1[\"apply\"]\n", "            ):\n", "                store_df.at[index, \"picker_surge\"] = row1[\"surge\"]\n", "            if (\n", "                row[\"disruption_index_picking\"] < row[\"old_disruption_index_picking\"]\n", "                and row[\"disruption_index_picking\"] >= row1[\"remove\"]\n", "            ):\n", "                store_df.at[index, \"picker_surge\"] = row1[\"surge\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store_df[\"surge\"] = store_df.apply(\n", "    lambda row: max(row[\"gsp_surge\"], row[\"picker_surge\"]),\n", "    axis=1,\n", ")\n", "final_df = store_df.copy(deep=True)\n", "final_df[\"install_ts_ist\"] = final_df.apply(\n", "    lambda row: time_now.strftime(\"%Y-%m-%d %H:%M:%S\"), axis=1\n", ")\n", "final_df[\"surge\"] = final_df[\"surge\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["append_di_df = final_df[\n", "    [\n", "        \"install_ts_ist\",\n", "        \"city\",\n", "        \"merchant_id\",\n", "        \"merchant_name\",\n", "        \"disruption_index_delivery\",\n", "        \"disruption_index_picking\",\n", "        \"old_disruption_index_delivery\",\n", "        \"old_disruption_index_picking\",\n", "        \"surge\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_stores_df = pb.from_sheets(\n", "    surge_sheet_id, test_stores, service_account=\"priority_account\"\n", ")\n", "test_stores_df[\"merchant_id\"] = test_stores_df[\"merchant_id\"].astype(int)\n", "test_stores_df = test_stores_df[[\"merchant_id\"]]\n", "final_df = pd.merge(test_stores_df, final_df, on=\"merchant_id\", how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for index, row in sheet_upload.iterrows():\n", "    for index1, row1 in final_df.iterrows():\n", "        if row[\"frontend_merchant_id\"] == row1[\"merchant_id\"]:\n", "            sheet_upload.at[index, \"charge\"] = row1[\"surge\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_surge_stores = sheet_upload.merge(\n", "    df_utilisation[[\"merchant_id\", \"city\", \"picker_utilisation\", \"gsp_utilisation\"]],\n", "    how=\"left\",\n", "    left_on=[\"frontend_merchant_id\"],\n", "    right_on=[\"merchant_id\"],\n", ")\n", "df_surge_stores = df_surge_stores[df_surge_stores[\"merchant_id\"].notnull()].merge(\n", "    store_df[[\"merchant_id\", \"disruption_index_delivery\", \"disruption_index_picking\"]],\n", "    how=\"left\",\n", "    on=\"merchant_id\",\n", ")\n", "df_surge_stores = df_surge_stores[df_surge_stores[\"charge\"] > 0].reset_index(drop=True)\n", "if df_surge_stores.shape[0] > 0:\n", "    df_surge_stores.rename(columns={\"charge\": \"surge_charge\"}, inplace=True)\n", "    df_surge_stores[\"bottleneck\"] = df_surge_stores.apply(\n", "        lambda row: \"pickers\"\n", "        if (row[\"picker_utilisation\"] > row[\"gsp_utilisation\"])\n", "        else (\n", "            \"gsps\" if (row[\"gsp_utilisation\"] > row[\"picker_utilisation\"]) else \"both\"\n", "        ),\n", "        axis=1,\n", "    )\n", "    df_surge_stores = df_surge_stores[\n", "        [\n", "            \"city\",\n", "            \"merchant_id\",\n", "            \"merchant_name\",\n", "            \"surge_charge\",\n", "            \"bottleneck\",\n", "            \"picker_utilisation\",\n", "            \"gsp_utilisation\",\n", "            \"disruption_index_picking\",\n", "            \"disruption_index_delivery\",\n", "        ]\n", "    ]\n", "    df_surge_stores = df_surge_stores.sort_values(\n", "        [\"city\", \"merchant_name\"]\n", "    ).reset_index(drop=True)\n", "    df_surge_stores[\"disruption_index_picking\"] = round(\n", "        df_surge_stores[\"disruption_index_picking\"], 2\n", "    )\n", "    df_surge_stores[\"disruption_index_delivery\"] = round(\n", "        df_surge_stores[\"disruption_index_delivery\"], 2\n", "    )\n", "\n", "df_surge_stores.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if upload_final.shape[0] > 0:\n", "    pb.to_sheets(\n", "        sheet_upload,\n", "        \"1BHD0BFql8aVM5upKEhSVqLwgY7kYJ7UBqdJhQQ77Oek\",\n", "        \"Surge charge\",\n", "        service_account=\"priority_account\",\n", "    )\n", "    pb.to_sheets(\n", "        df_surge_stores,\n", "        \"10Cb-zzCtebBFfw_2YhS-lqGGBmxASu3TdpUKRMB4EPw\",\n", "        \"Stores on surge\",\n", "        service_account=\"priority_account\",\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if upload_final.shape[0] > 0:\n", "    merchant_list = upload_final[\"merchant_id\"].unique().tolist()\n", "    for merchant in merchant_list:\n", "        df_upload1 = upload_final[upload_final[\"merchant_id\"] == merchant]\n", "        df_upload1.to_csv(CHARGE_UPLOAD, index=False)\n", "        epoch = epoch_time()\n", "        status = update_slot_charge_api(CHARGE_UPLOAD, epoch)\n", "        #         print(status)\n", "        if status == \"fail\":\n", "            message = \"Hi <@U03SMFVLDSL> Manpower utilisation based slot charge upload failed for merchant {}\".format(\n", "                merchant\n", "            )\n", "            print(message)\n", "            message1 = pb.send_slack_message(\n", "                channel=\"planning-dag-alerts\", text=message\n", "            )\n", "else:\n", "    status = \"file upload empty\"\n", "    print(status)\n", "if status == \"success\":\n", "    pb.send_slack_message(\n", "        channel=\"planning-dag-alerts\",\n", "        text=\"Super express slot charge based on manpower utilisation - Charges were applied\",\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"surge_charge_di_based\",\n", "    \"table_description\": \"surge charge di based logs\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"install_ts_ist\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"row install time in ETL\",\n", "        },\n", "        {\n", "            \"name\": \"city\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"city name merchant belongs to\",\n", "        },\n", "        {\n", "            \"name\": \"merchant_id\",\n", "            \"type\": \"bigint\",\n", "            \"description\": \"merchant id orders belong to \",\n", "        },\n", "        {\n", "            \"name\": \"merchant_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"darkstore name orders belong to\",\n", "        },\n", "        {\n", "            \"name\": \"disruption_index_delivery\",\n", "            \"type\": \"float\",\n", "            \"description\": \"disruption_index_delivery\",\n", "        },\n", "        {\n", "            \"name\": \"disruption_index_picking\",\n", "            \"type\": \"float\",\n", "            \"description\": \"disruption_index_picking\",\n", "        },\n", "        {\n", "            \"name\": \"old_disruption_index_delivery\",\n", "            \"type\": \"float\",\n", "            \"description\": \"old_disruption_index_delivery\",\n", "        },\n", "        {\n", "            \"name\": \"old_disruption_index_picking\",\n", "            \"type\": \"float\",\n", "            \"description\": \"old_disruption_index_picking\",\n", "        },\n", "        {\n", "            \"name\": \"surge\",\n", "            \"type\": \"int\",\n", "            \"description\": \"surge_to_be_applied\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"merchant_id\", \"install_ts_ist\"],\n", "    \"sortkey\": [\"city\", \"merchant_id\", \"merchant_name\"],\n", "    \"incremental_key\": \"install_ts_ist\",\n", "    \"load_type\": \"append\",\n", "}\n", "if append_di_df.shape[0] > 0 and run_hour >= 6 and run_hour <= 23:\n", "    pb.to_redshift(append_di_df, **kwargs)\n", "    pb.send_slack_message(\n", "        channel=\"planning-dag-alerts\",\n", "        text=\"surge_charge_di_based table - UPDATED\",\n", "    )\n", "    print(\"- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\")\n", "    print(\"Appended successfully\")\n", "    print(\"- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\")\n", "else:\n", "    print(\"Dataframe empty\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# PUSH DATA TO REDSHIFT TABLE\n", "kwargs1 = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"merchant_high_pt_slot_charge\",\n", "    \"table_description\": \"Merchant slot charge for high PT merchants\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"install_ts_ist\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"row install time\",\n", "        },\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"City\"},\n", "        {\n", "            \"name\": \"merchant_id\",\n", "            \"type\": \"bigint\",\n", "            \"description\": \"merchant id\",\n", "        },\n", "        {\n", "            \"name\": \"merchant_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"merchant name\",\n", "        },\n", "        {\n", "            \"name\": \"date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"date of slot for which charge was applied\",\n", "        },\n", "        {\n", "            \"name\": \"slot\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"merchant slot\",\n", "        },\n", "        {\n", "            \"name\": \"slot_utilisation\",\n", "            \"type\": \"float8\",\n", "            \"description\": \"capacity utilisation of the slot\",\n", "        },\n", "        {\n", "            \"name\": \"day_count\",\n", "            \"type\": \"smallint\",\n", "            \"description\": \"Day count from today\",\n", "        },\n", "        {\n", "            \"name\": \"slot_number\",\n", "            \"type\": \"int\",\n", "            \"description\": \"number of slots away from ideal slot\",\n", "        },\n", "        {\n", "            \"name\": \"slot_charge\",\n", "            \"type\": \"int\",\n", "            \"description\": \"slot charge\",\n", "        },\n", "    ],\n", "    \"sortkey\": [\"city\", \"merchant_id\", \"slot\", \"day_count\"],\n", "    \"incremental_key\": \"install_ts_ist\",\n", "    \"load_type\": \"append\",\n", "}\n", "if (df_high_pt_append.shape[0] > 0) & (status == \"success\"):\n", "    pb.to_redshift(df_high_pt_append, **kwargs1)\n", "    print(\"- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\")\n", "    print(\"append success\")\n", "    print(\"- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\")\n", "else:\n", "    print(\"Dataframe empty\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# PUSH DATA TO REDSHIFT TABLE\n", "kwargs1 = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"merchant_slot_charge_logs\",\n", "    \"table_description\": \"Merchant slot charge\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"install_ts_ist\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"row install time\",\n", "        },\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"City\"},\n", "        {\n", "            \"name\": \"merchant_id\",\n", "            \"type\": \"bigint\",\n", "            \"description\": \"merchant id\",\n", "        },\n", "        {\n", "            \"name\": \"merchant_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"merchant name\",\n", "        },\n", "        {\n", "            \"name\": \"slot\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"merchant slot\",\n", "        },\n", "        {\n", "            \"name\": \"day_count\",\n", "            \"type\": \"smallint\",\n", "            \"description\": \"Day count from today\",\n", "        },\n", "        {\n", "            \"name\": \"slot_charge\",\n", "            \"type\": \"smallint\",\n", "            \"description\": \"slot charge\",\n", "        },\n", "    ],\n", "    \"sortkey\": [\"city\", \"merchant_id\", \"slot\", \"day_count\"],\n", "    \"incremental_key\": \"install_ts_ist\",\n", "    \"load_type\": \"append\",\n", "}\n", "if (append_df.shape[0] > 0) & (status == \"success\"):\n", "    pb.to_redshift(append_df, **kwargs1)\n", "    print(\"- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\")\n", "    print(\"append success\")\n", "    print(\"- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\")\n", "else:\n", "    print(\"Dataframe empty\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["debug_df[\"pickers\"] = debug_df[\"pickers\"].astype(\"Int64\")\n", "debug_df[\"gsps\"] = debug_df[\"gsps\"].astype(\"Int64\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# PUSH DATA TO REDSHIFT TABLE\n", "kwargs1 = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"slot_charge_manpower_based_inputs\",\n", "    \"table_description\": \"Inputs considered to apply slot charge on a slot\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"install_ts_ist\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"row install time\",\n", "        },\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"City\"},\n", "        {\n", "            \"name\": \"merchant_id\",\n", "            \"type\": \"bigint\",\n", "            \"description\": \"merchant id\",\n", "        },\n", "        {\n", "            \"name\": \"merchant_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"merchant name\",\n", "        },\n", "        {\n", "            \"name\": \"pickers\",\n", "            \"type\": \"int\",\n", "            \"description\": \"number of pickers\",\n", "        },\n", "        {\n", "            \"name\": \"picker_idle_time\",\n", "            \"type\": \"float8\",\n", "            \"description\": \"picker_idle_time_75th_perc in the last hour\",\n", "        },\n", "        {\n", "            \"name\": \"picker_utilisation\",\n", "            \"type\": \"float8\",\n", "            \"description\": \"picker utilisation in the last hour\",\n", "        },\n", "        {\n", "            \"name\": \"gsps\",\n", "            \"type\": \"int\",\n", "            \"description\": \"number of gsps\",\n", "        },\n", "        {\n", "            \"name\": \"gsp_idle_time\",\n", "            \"type\": \"float8\",\n", "            \"description\": \"gsp_idle_time_75th_perc in the last hour\",\n", "        },\n", "        {\n", "            \"name\": \"gsp_utilisation\",\n", "            \"type\": \"float8\",\n", "            \"description\": \"gsp utilisation in the last hour\",\n", "        },\n", "        {\n", "            \"name\": \"pickers_in_queue\",\n", "            \"type\": \"int\",\n", "            \"description\": \"number of pickers in queue currently\",\n", "        },\n", "        {\n", "            \"name\": \"gsps_in_queue\",\n", "            \"type\": \"int\",\n", "            \"description\": \"number of gsps in queue currently\",\n", "        },\n", "        {\n", "            \"name\": \"unassigned_picking\",\n", "            \"type\": \"int\",\n", "            \"description\": \"number of orders left to be assigned to pickers currently\",\n", "        },\n", "        {\n", "            \"name\": \"unassigned_delivery\",\n", "            \"type\": \"int\",\n", "            \"description\": \"number of orders left to be assigned to gsps currently\",\n", "        },\n", "        {\n", "            \"name\": \"date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"date of slot for which charge was applied\",\n", "        },\n", "        {\n", "            \"name\": \"slot\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"merchant slot\",\n", "        },\n", "        {\n", "            \"name\": \"slot_utilisation\",\n", "            \"type\": \"float8\",\n", "            \"description\": \"capacity utilisation of the slot\",\n", "        },\n", "        {\n", "            \"name\": \"slot_number\",\n", "            \"type\": \"int\",\n", "            \"description\": \"number of slots away from ideal slot\",\n", "        },\n", "        {\n", "            \"name\": \"day_count\",\n", "            \"type\": \"int\",\n", "            \"description\": \"Day count from today\",\n", "        },\n", "        {\n", "            \"name\": \"slot_charge\",\n", "            \"type\": \"int\",\n", "            \"description\": \"slot charge\",\n", "        },\n", "    ],\n", "    \"sortkey\": [\"install_ts_ist\", \"city\", \"merchant_id\", \"slot\", \"day_count\"],\n", "    \"incremental_key\": \"install_ts_ist\",\n", "    \"load_type\": \"append\",\n", "}\n", "if (debug_df.shape[0] > 0) & (status == \"success\"):\n", "    pb.to_redshift(debug_df, **kwargs1)\n", "    print(\"- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\")\n", "    print(\"append success\")\n", "    print(\"- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\")\n", "else:\n", "    print(\"Dataframe empty\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "to_email = mailer\n", "files = []\n", "subject = \"Slot Charges - Manpower utilisation based\"\n", "header = \"\"\"<p>Please find attached slot charges uploaded for merchants.<br></p>\"\"\"\n", "html_content = \"\"\n", "if low_pt_mail_df.shape[0] > 0:\n", "    df_low_pt_charge = \"\"\"<p>Slot charges on the first available slot<br>\n", "                    {}<br></p>\"\"\".format(\n", "        low_pt_mail_df.to_html(justify=\"center\", index=False)\n", "    )\n", "    html_content = header + df_low_pt_charge\n", "    files.append(MAIL_UPLOAD)\n", "if high_pt_mail_df.shape[0] > 0:\n", "    df_high_pt_charge = \"\"\"<p>Merchants that have 30 Rs charge because PT is high<br>\n", "                    {}<br></p>\"\"\".format(\n", "        high_pt_mail_df.to_html(justify=\"center\", index=False)\n", "    )\n", "    html_content = html_content + df_high_pt_charge\n", "    files.append(MAIL_UPLOAD_1)\n", "if ((low_pt_mail_df.shape[0] > 0) and (status == \"success\")) or (\n", "    (high_pt_mail_df.shape[0] > 0) and (status == \"success\")\n", "):\n", "    pb.send_email(from_email, to_email, subject, html_content, files)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}