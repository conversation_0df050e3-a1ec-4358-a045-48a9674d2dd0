{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime, timedelta\n", "import copy\n", "import time, os\n", "import warnings\n", "import requests\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["NOW_IST = datetime.now() + <PERSON><PERSON><PERSON>(hours=5, minutes=30)\n", "RUN_HOUR = NOW_IST.hour\n", "print(RUN_HOUR)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SHEET_ID = \"1_kbVYeavxSL1NbsbO-AmWdKGLYy_ZWr97CvHpLmy9Cs\"\n", "mailing_list = \"Mail list\"\n", "config_sheet = \"Config\"\n", "CSV_PATH_CASHBACK_UPLOAD = \"/tmp/slot_charge_increase.csv\"\n", "CSV_MAIL_INCREASE = \"/tmp/slot_charge_mail_increase.csv\"\n", "CSV_MERCHANTS_AT_50 = \"/tmp/overutilized_merchants.csv\"\n", "CSV_CLOSED_HOURS_CHECK = \"/tmp/closed_hours.csv\"\n", "\n", "slot_charge_0_from = 4\n", "max_charge = 50"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def epoch_time():\n", "    d = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    p = \"%Y-%m-%d %H:%M:%S\"\n", "    os.environ[\"TZ\"] = \"UTC\"\n", "    epoch = int(time.mktime(time.strptime(d, p)))\n", "    return epoch"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def update_slot_charge_api(CSV_PATH_CASHBACK_UPLOAD, epoch):\n", "    url = \"http://tick-tock-consumer.prod-sgp-k8s.grofer.io/update-slot-cashback\"\n", "    payload = open(CSV_PATH_CASHBACK_UPLOAD, \"rb\").read()\n", "    headers = {\n", "        \"Accept\": \"application/json, text/plain, */*\",\n", "        \"Content-Type\": \"text/plain\",\n", "        \"access_token\": \"de35642d64f6bad5a3112ca48873e278a6a006bd06f0e9e1f52dc0b7904aa1fe\",\n", "        \"auth_key\": \"53b5f3c25c12e7ad2385af6f9cef363ec8095420064b54ddd111f58d2dae8f0b\",\n", "        \"Referer\": \"http://tick-tock-consumer.prod-k8s.grofer.io/dashboard\",\n", "        \"Accept-Encoding\": \"gzip, deflate\",\n", "        \"Accept-Language\": \"en-GB,en-US;q=0.9,en;q=0.8\",\n", "        \"Cookie\": \"user=%7B%22merchant%22%3Afalse%2C%22phone%22%3A%22%22%2C%22verified%22%3Atrue%2C%22name%22%3A%22Mounika%20B%22%2C%22roles%22%3A%5B%22callcenter_exec%22%2C%22promise_api_manager%22%5D%2C%22wallet_id%22%3A%22%22%2C%22date_now%22%3A{}%2C%22image%22%3A%22https%3A%2F%2Flh3.googleusercontent.com%2Fa-%2FAOh14Gjl3sCkdYN6P-hNVoZriRx_ViItUPz7AxIar3We%3Ds96-c%3Fsz%3D250%22%2C%22id%22%3A13203448%2C%22email%22%3A%22badiginchala.saimounika%40grofers.com%22%7D\".format(\n", "            str(epoch)\n", "        ),\n", "    }\n", "    response = requests.request(\"PUT\", url, headers=headers, data=payload)\n", "\n", "    print(\"- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\")\n", "    if response.status_code >= 200 and response.status_code < 300:\n", "        status = \"success\"\n", "        print(\"cashback upload - SUCCESS\")\n", "        print(response.text)\n", "    else:\n", "        status = \"fail\"\n", "        print(\"cashback upload - FAIL\")\n", "        print(response.text)\n", "    return status"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["supply_cn = pb.get_connection(\"[Replica] Supply Orchestrator\")\n", "pt_cn = pb.get_connection(\"[Replica] Promise Time\")\n", "redshift_cn = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["exclusion_merchants = pb.from_sheets(\n", "    SHEET_ID, config_sheet, service_account=\"service_account\"\n", ")\n", "exclusion_merchants = exclusion_merchants[\n", "    exclusion_merchants[\"Rule\"] == \"increase_high_demand\"\n", "]\n", "exclusion_list = exclusion_merchants[\"merchant_id\"].unique().tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_mailer = pb.from_sheets(SHEET_ID, mailing_list, service_account=\"service_account\")\n", "mailer = df_mailer[\"mail\"].apply(lambda x: str(x)).to_list()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["utilization = \"\"\"select distinct city,merchant_name,merchant_id,date,slot_freeze_hour,hours_left,\n", "case When sum(merchant_available) <= sum(warehouse_available) And sum(merchant_available) < sum(station_available) Then sum(merchant_available)\n", "            When sum(warehouse_available) <= sum(merchant_available) And sum(warehouse_available) < sum(station_available) Then sum(warehouse_available) \n", "            Else sum(station_available)\n", "            End As net_available_capacity,\n", "sum(merchant_total) as merchant_asked,\n", "sum(merchant_available) as merchant_available\n", "from\n", "(select distinct city_name as city,merchant_external_id as merchant_id,\n", "merchant_name,\n", "(slot_start+interval '5.5 hours')::date as date,\n", " TO_CHAR(slot_start + interval '5.5 hours','HH24MI') || ' - ' || \n", "        TO_CHAR(slot_end + interval '5.5 hours','HH24MI') AS slot,\n", "extract(hour from (slot_freeze_time+interval '5.5 hours')::timestamp)::int as slot_freeze_hour,\n", "slot_type,\n", "merchant_asked as merchant_total,\n", "(DATE_PART('day', (slot_freeze_time+interval '5.5 hours')::timestamp - (now()+interval '5.5 hours')::timestamp) * 24 + DATE_PART('hour', (slot_freeze_time+interval '5.5 hours')::timestamp - (now()+interval '5.5 hours')::timestamp))::int as hours_left,\n", "case when (merchant_asked- merchant_actual) <= 0 then 0 else (merchant_asked - merchant_actual) end as merchant_available,\n", "case when warehouse_available<=0 then 0 else warehouse_available end as warehouse_available,\n", "case when min(station_available)<=0 then 0 else min(station_available) end as station_available\n", "from sco_path_capacity spc\n", "inner join sco_merchant_slot sms on sms.slot_id = spc.slot_id \n", "    and sms.merchant_id = spc.merchant_id\n", "    and spc.slot_type in ('normal','premium','heavy')\n", "    and warehouse_name not ilike '%%cold%%' and warehouse_name not ilike '%%fnv%%' and warehouse_name not ilike '%%delight%%'\n", "where (slot_start+interval '5.5 hours')::date = (now()+interval '1 day'+interval '5.5 hours')::date\n", "and spc.active = 'true'\n", "and sms.active = 'true'\n", "and merchant_name not ilike '%% ES%%' and merchant_name not ilike '%%dummy%%' and merchant_name not ilike '%%donation%%'\n", "and merchant_name not ilike '%% FS%%' and merchant_name not ilike '%%b2b%%'\n", "group by 1,2,3,4,5,6,7,8,9,10,11\n", ") a\n", "where merchant_id not in (29108,29009)\n", "group by 1,2,3,4,5,6\n", "order by 1,2,3,4,6\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_utilization = pd.read_sql(utilization, supply_cn)\n", "df_utilization = df_utilization[df_utilization[\"merchant_asked\"] > 0]\n", "df_utilization[\"slots\"] = (\n", "    df_utilization.groupby([\"city\", \"merchant_id\", \"merchant_name\"]).cumcount() + 1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_utilization[\"slots\"] = df_utilization.apply(\n", "    lambda row: str(row[\"slots\"]).replace(\"1\", \"Slot A\"), axis=1\n", ")\n", "df_utilization[\"slots\"] = df_utilization.apply(\n", "    lambda row: str(row[\"slots\"]).replace(\"2\", \"Slot B\")\n", "    if (\n", "        (\n", "            row[\"hours_left\"]\n", "            - (\n", "                df_utilization[\n", "                    (df_utilization[\"merchant_id\"] == row[\"merchant_id\"])\n", "                    & (df_utilization[\"slots\"] == \"Slot A\")\n", "                ][\"hours_left\"].values[0]\n", "            )\n", "        )\n", "        >= 3\n", "    )\n", "    else str(row[\"slots\"]).replace(\"2\", \"Slot A\"),\n", "    axis=1,\n", ")\n", "df_utilization[\"slots\"] = df_utilization.apply(\n", "    lambda row: str(row[\"slots\"]).replace(\"3\", \"Slot B\")\n", "    if (\n", "        (\n", "            row[\"hours_left\"]\n", "            - (\n", "                df_utilization[\n", "                    (df_utilization[\"merchant_id\"] == row[\"merchant_id\"])\n", "                    & (df_utilization[\"slots\"] == \"Slot A\")\n", "                ][\"hours_left\"].values[0]\n", "            )\n", "        )\n", "        >= 3\n", "    )\n", "    else str(row[\"slots\"]).replace(\"3\", \"Slot A\"),\n", "    axis=1,\n", ")\n", "df_utilization[\"slots\"] = df_utilization.apply(\n", "    lambda row: str(row[\"slots\"]).replace(\"4\", \"Slot B\"), axis=1\n", ")\n", "\n", "df_slots_blocks = (\n", "    df_utilization[\n", "        [\"city\", \"merchant_name\", \"merchant_id\", \"slot_freeze_hour\", \"slots\"]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_slot_charge = \"\"\"select city,merchant_id,\n", "merchant_name,slot,\n", "day_count,slot_charge,\n", "install_ts_ist \n", "from (select city,merchant_id,\n", "merchant_name,replace(slot, ' ', '') as slot,\n", "day_count,slot_charge,install_ts_ist,\n", "RANK() OVER(PARTITION BY city,merchant_id,merchant_name,replace(slot, ' ', ''),day_count ORDER BY install_ts_ist DESC) as rank\n", "from metrics.merchant_slot_charge_logs)\n", "where rank = 1\n", "order by 1,2,3,4,5\"\"\"\n", "df_slot_charge_raw = pd.read_sql(current_slot_charge, redshift_cn)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["slot_freeze_times = \"\"\"select distinct city_name as city,merchant_external_id as merchant_id,\n", "merchant_name,\n", "(slot_start+interval '5.5 hours')::date as date,\n", " TO_CHAR(slot_start + interval '5.5 hours','HH24MI') || '-' || \n", "        TO_CHAR(slot_end + interval '5.5 hours','HH24MI') AS slot,\n", "extract(hour from (slot_freeze_time+interval '5.5 hours')::timestamp)::int as slot_freeze_hour,\n", "(DATE_PART('day', (slot_freeze_time+interval '5.5 hours')::timestamp - (now()+interval '5.5 hours')::timestamp) * 24 + DATE_PART('hour', (slot_freeze_time+interval '5.5 hours')::timestamp - (now()+interval '5.5 hours')::timestamp))::int as hours_left\n", "from sco_path_capacity spc\n", "inner join sco_merchant_slot sms on sms.slot_id = spc.slot_id \n", "    and sms.merchant_id = spc.merchant_id\n", "    and spc.slot_type in ('normal','premium','heavy','crm_reschedule')\n", "    and warehouse_name not ilike '%%cold%%' and warehouse_name not ilike '%%fnv%%'\n", "where (slot_start+interval '5.5 hours')::date = (now()+interval '1 day'+interval '5.5 hours')::date\n", "and spc.active = 'true'\n", "and sms.active = 'true'\n", "and merchant_name not ilike '%% ES%%' and merchant_name not ilike '%% FS%%' and merchant_name not ilike '%%dummy%%' and merchant_name not ilike '%%donation%%' and merchant_name not like '%%B2B%%' and merchant_name not in ('Super Store - 32nd Milestone','Super Store - Nehru Nagar') and merchant_asked > 0\n", "group by 1,2,3,4,5,6,7\n", "order by 1,2,3,4,7\"\"\"\n", "df_slots = pd.read_sql(slot_freeze_times, supply_cn)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def pt_query():\n", "    promise_time = \"\"\"select distinct city,merchant_id,merchant_name,\n", "    backend_merchant_name,\n", "    extract(hour from _ist_ts::timestamp)::int as hour,\n", "    _ist_ts::date as date,\n", "    promise_time_days,\n", "    count(_ist_ts) as count\n", "    from earliest_slot_details_snapshots \n", "    where _ist_ts::date  = (now()+interval '5.5 hours'-interval '1 day')::date\n", "    and user_type='Non-SBC' \n", "    and order_type = 'normal'\n", "    and backend_merchant_name not ilike '%%cold%%' and backend_merchant_name not ilike '%%fnv%%'\n", "    and backend_merchant_name not ilike '%%delight%%' and backend_merchant_name not ilike '%% ES%%' and backend_merchant_name not ilike '%% FS%%'\n", "    and merchant_type <> 'express'\n", "    and merchant_name not ilike '%% FS%%' and merchant_name not ilike '%%B2B%%'\n", "    group by 1,2,3,4,5,6,7\n", "    order by 1,2,3,4,5,6\"\"\"\n", "    df_pt = pd.read_sql(promise_time, pb.get_connection(\"[Replica] Promise Time\"))\n", "\n", "    #     df_pt = pb.from_sheets(SHEET_ID,\"pt\",service_account = \"service_account\")\n", "    #     df_pt[[\"merchant_id\",\"hour\",\"promise_time_days\",\"count\"]] = df_pt[[\"merchant_id\",\"hour\",\"promise_time_days\",\"count\"]].astype(int)\n", "\n", "    return df_pt"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_pt = pt_query()\n", "\n", "df_copy = copy.deepcopy(df_pt)\n", "df_copy.sort_values(\n", "    [\"city\", \"backend_merchant_name\", \"merchant_id\", \"merchant_name\", \"hour\", \"count\"],\n", "    ascending=False,\n", "    inplace=True,\n", ")\n", "df_copy.drop_duplicates(\n", "    subset=[\"city\", \"backend_merchant_name\", \"merchant_id\", \"merchant_name\", \"hour\"],\n", "    keep=\"first\",\n", "    inplace=True,\n", ")\n", "df_pivot = pd.pivot_table(\n", "    df_copy,\n", "    values=[\"promise_time_days\"],\n", "    index=[\"city\", \"backend_merchant_name\", \"merchant_id\", \"merchant_name\"],\n", "    columns=[\"hour\"],\n", ")\n", "pivot_table = pd.DataFrame(df_pivot.to_records())\n", "pivot_table.columns = pivot_table.columns.str.replace(\"\\('promise_time_days', \", \"\")\n", "pivot_table.columns = pivot_table.columns.str.replace(\"\\)\", \"\")\n", "pivot_table[\"closed_hours\"] = 0\n", "column_list = []\n", "for i in range(8, 24):\n", "    column_list.append(str(i))\n", "    for index, row in pivot_table.iterrows():\n", "        if row[str(i)] == -1:\n", "            pivot_table.at[index, \"closed_hours\"] = (\n", "                pivot_table[\n", "                    (pivot_table[\"merchant_id\"] == row[\"merchant_id\"])\n", "                    & (\n", "                        pivot_table[\"backend_merchant_name\"]\n", "                        == row[\"backend_merchant_name\"]\n", "                    )\n", "                ][\"closed_hours\"].values[0]\n", "            ) + 1\n", "pivot_table.to_csv(CSV_CLOSED_HOURS_CHECK, index=False)\n", "df_closed = pivot_table[pivot_table[\"closed_hours\"] > 5]\n", "if df_closed.shape[0] == 0:\n", "    pb.send_slack_message(\n", "        channel=\"planning-dag-alerts\",\n", "        text=\"No merchants were closed for more than 5 hours between 8am and 11pm\",\n", "    )\n", "df_closed = df_closed.loc[~df_closed[\"merchant_id\"].isin(exclusion_list)]\n", "if df_closed.shape[0] == 0:\n", "    pb.send_slack_message(\n", "        channel=\"planning-dag-alerts\",\n", "        text=\"All merchants closed for more than 5 hours are in the exclusion list\",\n", "    )\n", "df_closed.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_slot_charge = df_slot_charge_raw[df_slot_charge_raw[\"day_count\"] == 1]\n", "df_slots_list = (\n", "    df_slots[[\"city\", \"merchant_id\", \"merchant_name\", \"slot\"]]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "df_closed_slots_merge = df_closed[\n", "    [\"city\", \"merchant_id\", \"merchant_name\", \"closed_hours\"]\n", "].merge(df_slots_list, on=[\"city\", \"merchant_id\", \"merchant_name\"], how=\"left\")\n", "df_closed_slots_merge = df_closed_slots_merge[df_closed_slots_merge[\"slot\"].notnull()]\n", "df_closed_merge = df_closed_slots_merge.merge(\n", "    current_slot_charge, on=[\"city\", \"merchant_id\", \"merchant_name\", \"slot\"], how=\"left\"\n", ")\n", "df_closed_merge.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df_closed_merge.shape[0] > 0:\n", "    df_closed_merge[\"date\"] = df_closed_merge.apply(\n", "        lambda row: pd.to_datetime(row[\"install_ts_ist\"]).date(), axis=1\n", "    )\n", "    df_closed_merge[\"hour\"] = df_closed_merge.apply(\n", "        lambda row: pd.to_datetime(row[\"install_ts_ist\"]).hour, axis=1\n", "    )\n", "    df_closed_merge[\"change\"] = df_closed_merge.apply(\n", "        lambda row: 10\n", "        if ((row[\"closed_hours\"] > 5) & (row[\"closed_hours\"] < 10))\n", "        else 20,\n", "        axis=1,\n", "    )\n", "    df_closed_merge[\"T\"] = df_closed_merge.apply(\n", "        lambda row: min((row[\"slot_charge\"] + row[\"change\"]), max_charge), axis=1\n", "    )\n", "    df_append_increase = (\n", "        df_closed_merge[\n", "            [\"city\", \"merchant_id\", \"merchant_name\", \"slot\", \"day_count\", \"T\"]\n", "        ]\n", "        .drop_duplicates()\n", "        .reset_index(drop=True)\n", "    )\n", "    df_append_increase.rename(columns={\"T\": \"slot_charge\"}, inplace=True)\n", "\n", "    for i in range(1, 31):\n", "        if (i == 1) & (i < (slot_charge_0_from - 1)):\n", "            count = \"T+\" + str(i)\n", "            df_closed_merge[count] = df_closed_merge.apply(\n", "                lambda row: max((row[\"T\"] - 10), 0)\n", "                if (row[\"T\"] > 10)\n", "                else (10 if (row[\"T\"] == 10) else 0),\n", "                axis=1,\n", "            )\n", "            df_closed_merge[\"day_count\"] = i + 1\n", "            df_temp = copy.deepcopy(df_closed_merge)\n", "            df_temp = (\n", "                df_temp[\n", "                    [\"city\", \"merchant_id\", \"merchant_name\", \"slot\", \"day_count\", count]\n", "                ]\n", "                .drop_duplicates()\n", "                .reset_index(drop=True)\n", "            )\n", "            df_temp.rename(columns={count: \"slot_charge\"}, inplace=True)\n", "            if i <= 6:\n", "                df_append_increase = df_append_increase.append(df_temp)\n", "        elif i < (slot_charge_0_from - 1):\n", "            count = \"T+\" + str(i)\n", "            previous_day = \"T+\" + str(i - 1)\n", "            df_closed_merge[count] = df_closed_merge.apply(\n", "                lambda row: max((row[previous_day] - 10), 0)\n", "                if (row[previous_day] > 10)\n", "                else (10 if (row[previous_day] == 10) else 0),\n", "                axis=1,\n", "            )\n", "            df_closed_merge[\"day_count\"] = i + 1\n", "            df_temp = copy.deepcopy(df_closed_merge)\n", "            df_temp = (\n", "                df_temp[\n", "                    [\"city\", \"merchant_id\", \"merchant_name\", \"slot\", \"day_count\", count]\n", "                ]\n", "                .drop_duplicates()\n", "                .reset_index(drop=True)\n", "            )\n", "            df_temp.rename(columns={count: \"slot_charge\"}, inplace=True)\n", "            if i <= 6:\n", "                df_append_increase = df_append_increase.append(df_temp)\n", "        elif i >= (slot_charge_0_from - 1):\n", "            count = \"T+\" + str(i)\n", "            df_closed_merge[count] = 0\n", "            df_closed_merge[\"day_count\"] = i + 1\n", "            df_temp = copy.deepcopy(df_closed_merge)\n", "            df_temp = (\n", "                df_temp[\n", "                    [\"city\", \"merchant_id\", \"merchant_name\", \"slot\", \"day_count\", count]\n", "                ]\n", "                .drop_duplicates()\n", "                .reset_index(drop=True)\n", "            )\n", "            df_temp.rename(columns={count: \"slot_charge\"}, inplace=True)\n", "            if i <= 6:\n", "                df_append_increase = df_append_increase.append(df_temp)\n", "\n", "    merchants_at_50 = df_closed_merge[(df_closed_merge[\"slot_charge\"] == 50)]\n", "    merchants_at_50 = (\n", "        merchants_at_50[[\"city\", \"merchant_id\", \"merchant_name\"]]\n", "        .drop_duplicates()\n", "        .reset_index(drop=True)\n", "    )\n", "    merchants_at_50_list = merchants_at_50[\"merchant_id\"].unique().tolist()\n", "\n", "    df_append_increase = df_append_increase.sort_values(\n", "        [\"city\", \"merchant_id\", \"merchant_name\", \"slot\"]\n", "    )\n", "    df_row_check_increase = df_append_increase.merge(\n", "        df_slot_charge_raw,\n", "        on=[\"city\", \"merchant_id\", \"merchant_name\", \"slot\", \"day_count\"],\n", "        how=\"left\",\n", "    )\n", "    df_upload_increase = df_row_check_increase[\n", "        df_row_check_increase[\"slot_charge_x\"] != df_row_check_increase[\"slot_charge_y\"]\n", "    ]\n", "    df_upload_increase[\"install_ts_ist\"] = df_upload_increase.apply(\n", "        lambda row: pd.to_datetime(NOW_IST).strftime(\"%Y-%m-%d %H:%M:%S\"), axis=1\n", "    )\n", "    df_upload_increase.rename(columns={\"slot_charge_x\": \"slot_charge\"}, inplace=True)\n", "    df_upload_increase[[\"day_count\", \"slot_charge\"]] = df_upload_increase[\n", "        [\"day_count\", \"slot_charge\"]\n", "    ].astype(\"Int64\")\n", "    df_upload_increase = (\n", "        df_upload_increase[\n", "            [\n", "                \"install_ts_ist\",\n", "                \"city\",\n", "                \"merchant_id\",\n", "                \"merchant_name\",\n", "                \"slot\",\n", "                \"day_count\",\n", "                \"slot_charge\",\n", "            ]\n", "        ]\n", "        .drop_duplicates()\n", "        .reset_index(drop=True)\n", "    )\n", "\n", "    df_closed_merge = df_closed_merge[\n", "        ~df_closed_merge[\"merchant_id\"].isin(merchants_at_50_list)\n", "    ]\n", "    df_closed_merge = df_closed_merge[\n", "        df_closed_merge[\"T\"] > df_closed_merge[\"slot_charge\"]\n", "    ]\n", "\n", "    df_mail_increase = df_closed_merge[\n", "        [\"city\", \"merchant_id\", \"merchant_name\", \"closed_hours\", \"slot_charge\", \"T\"]\n", "    ].rename(columns={\"slot_charge\": \"Previous slot charge\", \"T\": \"New slot charge\"})\n", "    df_mail_increase[[\"Previous slot charge\", \"New slot charge\"]] = df_mail_increase[\n", "        [\"Previous slot charge\", \"New slot charge\"]\n", "    ].astype(\"Int64\")\n", "    df_mail_increase[\"change\"] = \"increase\"\n", "    df_mail_increase = df_mail_increase.drop_duplicates().reset_index(drop=True)\n", "\n", "    exclude_cols_increase = [\n", "        \"city\",\n", "        \"merchant_id\",\n", "        \"merchant_name\",\n", "        \"closed_hours\",\n", "        \"slot\",\n", "        \"day_count\",\n", "        \"slot_charge\",\n", "        \"install_ts_ist\",\n", "        \"date\",\n", "        \"hour\",\n", "        \"change\",\n", "    ]\n", "    day_list = []\n", "    for col in df_closed_merge.columns:\n", "        if col not in exclude_cols_increase:\n", "            df_closed_merge[col] = df_closed_merge[col].astype(int).astype(\"Int64\")\n", "            day_list.append(col)\n", "\n", "    df_file_increase = (\n", "        df_closed_merge[[\"merchant_id\", \"slot\"] + day_list]\n", "        .drop_duplicates()\n", "        .reset_index(drop=True)\n", "    )\n", "else:\n", "    df_file_increase = pd.DataFrame()\n", "    df_mail_increase = pd.DataFrame()\n", "    df_upload_increase = pd.DataFrame()\n", "    merchants_at_50 = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df_file_increase.shape[0] > 0:\n", "    df_file_increase.to_csv(CSV_PATH_CASHBACK_UPLOAD, index=False)\n", "\n", "file_list = []\n", "\n", "if df_mail_increase.shape[0] > 0:\n", "    df_mail_increase.to_csv(CSV_MAIL_INCREASE, index=False)\n", "    file_list = file_list.append(CSV_MAIL_INCREASE)\n", "\n", "if merchants_at_50.shape[0] > 0:\n", "    merchants_at_50.to_csv(CSV_MERCHANTS_AT_50, index=False)\n", "    file_list = file_list.append(CSV_MERCHANTS_AT_50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df_file_increase.shape[0] > 0:\n", "    epoch = epoch_time()\n", "    status = update_slot_charge_api(CSV_PATH_CASHBACK_UPLOAD, epoch)\n", "    status = \"success\"\n", "    pb.send_slack_message(\n", "        channel=\"planning-dag-alerts\",\n", "        text=\"Slot charges were increased for some merchants - Mail sent\",\n", "    )\n", "    print(status)\n", "else:\n", "    status = \"\"\n", "    pb.send_slack_message(\n", "        channel=\"planning-dag-alerts\", text=\"No slot charge increase instances\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# PUSH DATA TO REDSHIFT TABLE\n", "kwargs1 = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"merchant_slot_charge_logs\",\n", "    \"table_description\": \"Merchant slot charge\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"install_ts_ist\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"row install time\",\n", "        },\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"City\"},\n", "        {\n", "            \"name\": \"merchant_id\",\n", "            \"type\": \"bigint\",\n", "            \"description\": \"merchant id\",\n", "        },\n", "        {\n", "            \"name\": \"merchant_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"merchant name\",\n", "        },\n", "        {\n", "            \"name\": \"slot\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"merchant slot\",\n", "        },\n", "        {\n", "            \"name\": \"day_count\",\n", "            \"type\": \"smallint\",\n", "            \"description\": \"Day count from today\",\n", "        },\n", "        {\n", "            \"name\": \"slot_charge\",\n", "            \"type\": \"smallint\",\n", "            \"description\": \"slot charge\",\n", "        },\n", "    ],\n", "    \"sortkey\": [\"city\", \"merchant_id\", \"slot\", \"day_count\"],\n", "    \"incremental_key\": \"install_ts\",\n", "    \"load_type\": \"append\",\n", "}\n", "if (df_upload_increase.shape[0] > 0) & (status == \"success\"):\n", "    pb.to_redshift(df_upload_increase, **kwargs1)\n", "    print(\"- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\")\n", "    print(\"append success\")\n", "    print(\"- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\")\n", "else:\n", "    print(\"Dataframe empty\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "to_email = mailer\n", "files = file_list\n", "subject = \"Slot charge increase\"\n", "header = \"\"\"<p>Please find below the changes made to slot charges.<br></p>\"\"\"\n", "\n", "df_increase = \"\"\"<p>Merchants for which slot charge was increased.Consecutive day's charges are reduced by 10 based on the previous day's charge<br>\n", "                {}<br></p>\"\"\".format(\n", "    df_mail_increase.to_html(justify=\"center\", index=False)\n", ")\n", "df_50 = \"\"\"<p>Merchants for which slot charge is already at 50 and were closed for more than 5 hours<br>\n", "                {}<br></p>\"\"\".format(\n", "    merchants_at_50.to_html(justify=\"center\", index=False)\n", ")\n", "if df_mail_increase.shape[0] != 0:\n", "    html_content = header + df_increase\n", "\n", "if (merchants_at_50.shape[0] != 0) & (df_mail_increase.shape[0] != 0):\n", "    html_content += df_50\n", "elif (merchants_at_50.shape[0] != 0) & (df_mail_increase.shape[0] == 0):\n", "    html_content = df_50\n", "\n", "if (len(files) > 0) & (status == \"success\"):\n", "    pb.send_email(from_email, to_email, subject, html_content, files)\n", "\n", "if pivot_table.shape[0] > 0:\n", "    pb.send_email(\n", "        from_email,\n", "        to_email=[\"<EMAIL>\"],\n", "        subject=\"Closed stores check\",\n", "        html_content=\"\",\n", "        files=[CSV_CLOSED_HOURS_CHECK],\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}