dag_name: slot_charge_increase
dag_type: workflow
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: capacity
notebook:
  parameters:
    end_date: ''
    start_date: ''
owner:
  email: <EMAIL>
  slack_id: U03SMFVLDSL
path: capacity/misc/workflow/slot_charge_increase
paused: true
project_name: misc
schedule:
  interval: 30 19 * * *
  start_date: '2021-05-26T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 5
