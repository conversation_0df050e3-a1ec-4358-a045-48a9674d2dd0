{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Importing Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "from datetime import timedelta, datetime\n", "import copy\n", "import requests\n", "import warnings\n", "import sys\n", "\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)\n", "from sql_queries import *\n", "from user_functions import *"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## DateTime Variables"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["nowIst = datetime.now() + <PERSON><PERSON><PERSON>(hours=5, minutes=30)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["runHour = nowIst.hour\n", "runMinute = nowIst.minute"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## G-Sheets Connection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheetId = \"1nj7G-SwoHtBVjlPH52nSdVrOA0XRsW7nN43YJxchl9A\"\n", "cntlSheet = \"delay_config_sheet\"\n", "config_sheet = \"ActiveMerchants\"\n", "mailing_list = \"MailSheet\"\n", "input_sheet = \"ActiveMerchantRules\"\n", "s_factor_sheet = \"SFactorRules\"\n", "ideal_parameters = \"IdealParameters\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["input_df = pb.from_sheets(sheetId, input_sheet, service_account=\"service_account\")\n", "input_df[\n", "    [\n", "        \"pt_hours\",\n", "        \"g_oph\",\n", "        \"p_oph\",\n", "        \"fill_rate\",\n", "        \"delay_time\",\n", "        \"capacity_utilisation\",\n", "        \"manpower_utilisation\",\n", "    ]\n", "] = input_df[\n", "    [\n", "        \"pt_hours\",\n", "        \"g_oph\",\n", "        \"p_oph\",\n", "        \"fill_rate\",\n", "        \"delay_time\",\n", "        \"capacity_utilisation\",\n", "        \"manpower_utilisation\",\n", "    ]\n", "].apply(\n", "    pd.to_numeric\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mail_df = pb.from_sheets(sheetId, mailing_list, service_account=\"service_account\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mail_df = mail_df[\"mail_id\"].apply(lambda x: str(x)).to_list()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_df = pb.from_sheets(sheetId, cntlSheet, service_account=\"service_account\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_df = active_df[active_df[\"default_flag\"] == \"TRUE\"]\n", "active_df = active_df[[\"merchant_id\", \"merchant_name\", \"default_flag\"]]\n", "active_df = active_df.fillna(0)\n", "active_df[\"merchant_id\"] = active_df[\"merchant_id\"].astype(\"int\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## DataBase Connection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["log_db = pb.get_connection(\"[Replica] Logistic DB\")\n", "pt_db = pb.get_connection(\"[Replica] Promise Time\")\n", "# aurora_db = pb.get_connection(\"[Prod] Aurora DB\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["g_oph_df = read_sql(gsp_oph.format(now=nowIst, date=nowIst.date()), log_db)\n", "p_oph_df = read_sql(picker_oph.format(now=(nowIst), date=nowIst.date()), log_db)\n", "pt_df = read_sql(pt, pt_db)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cap_df = read_sql(cap_utili.format(date=nowIst.date()), aurora_db)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["manpower_utilisation_df = read_sql(manpower_utilisation, log_db)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cap_df[\"now\"] = nowIst"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cap_df[\"slot_start\"] = cap_df[\"slot_start\"].apply(\n", "#     lambda x: x.strftime(\"%Y-%m-%d %H:%M\")\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cap_df[\"time_diff\"] = cap_df.apply(\n", "#     lambda row: int(\n", "#         (pd.to_datetime(nowIst) - pd.to_datetime(row[\"slot_start\"])).total_seconds()\n", "#         / 60\n", "#     ),\n", "#     axis=1,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cap_df = cap_df.loc[(cap_df[\"time_diff\"] < 135) & (cap_df[\"time_diff\"] > 0)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cap_df = cap_df.groupby([\"merchant_id\"], as_index=False).agg(\n", "#     {\"cap_utilisation\": \"mean\"}\n", "# )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Processing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if g_oph_df.shape[0] != 0:\n", "    g_oph_df = g_oph_df.groupby([\"merchant_id\"], as_index=False).agg(\n", "        {\"delay_time\": np.mean, \"fill_rate\": np.mean, \"g_oph\": np.mean}\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["g_oph_df[[\"merchant_id\", \"fill_rate\", \"g_oph\"]] = g_oph_df[\n", "    [\"merchant_id\", \"fill_rate\", \"g_oph\"]\n", "].apply(pd.to_numeric)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if p_oph_df.shape[0] != 0:\n", "    p_oph_df = p_oph_df.groupby([\"merchant_id\"], as_index=False).agg({\"p_oph\": np.mean})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["p_oph_df[[\"merchant_id\", \"p_oph\"]] = p_oph_df[[\"merchant_id\", \"p_oph\"]].apply(\n", "    pd.to_numeric\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pt_df[[\"merchant_id\", \"promise_time_days\", \"promise_time_hours\"]] = pt_df[\n", "    [\"merchant_id\", \"promise_time_days\", \"promise_time_hours\"]\n", "].apply(pd.to_numeric)\n", "# pt_df=pt_df[[\"merchant_id\",\"avg_promised_hours\",\"orders\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pt_df = pt_df.rename(columns={\"promise_time_days\": \"pt_days\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if manpower_utilisation_df.shape[0] != 0:\n", "    manpower_utilisation_df[\"picker_utilisation\"] = manpower_utilisation_df[\n", "        \"picker_utilisation\"\n", "    ].apply(pd.to_numeric)\n", "    manpower_utilisation_df[\"gsp_utilisation\"] = manpower_utilisation_df[\n", "        \"gsp_utilisation\"\n", "    ].apply(pd.to_numeric)\n", "    manpower_utilisation_df[\"manpower_utilisation\"] = manpower_utilisation_df[\n", "        [\"picker_utilisation\", \"gsp_utilisation\"]\n", "    ].max(axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df = g_oph_df.merge(p_oph_df, how=\"outer\", on=[\"merchant_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df = final_df.merge(pt_df, how=\"outer\", on=[\"merchant_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df = final_df.merge(manpower_utilisation_df, how=\"outer\", on=[\"merchant_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df[\"on_time\"] = 100 - final_df[\"delay_time\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<h4>User defined functions</h4>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def active_function(row):\n", "    if (\n", "        row[\"delay_time\"] > input_df[\"delay_time\"][0]\n", "        or row[\"fill_rate\"] < input_df[\"fill_rate\"][0]\n", "        or row[\"g_oph\"] > input_df[\"g_oph\"][0]\n", "        or row[\"p_oph\"] > input_df[\"p_oph\"][0]\n", "        or row[\"promise_time_hours\"] > input_df[\"pt_hours\"][0]\n", "    ):\n", "        val = \"FALSE\"\n", "    elif row[\"manpower_utilisation\"] < input_df[\"manpower_utilisation\"][0]:\n", "        val = \"TRUE\"\n", "    else:\n", "        val = \"TRUE\"\n", "    return val\n", "\n", "\n", "def reason_function(row):\n", "    if row[\"promise_time_hours\"] > input_df[\"pt_hours\"][0]:\n", "        val = \"PT\"\n", "    elif row[\"delay_time\"] > input_df[\"delay_time\"][0]:\n", "        val = \"Delay\"\n", "    elif row[\"fill_rate\"] < input_df[\"fill_rate\"][0]:\n", "        val = \"FillRate\"\n", "    elif row[\"g_oph\"] > input_df[\"g_oph\"][0]:\n", "        val = \"GOPH\"\n", "    elif row[\"p_oph\"] > input_df[\"p_oph\"][0]:\n", "        val = \"POPH\"\n", "    elif row[\"manpower_utilisation\"] < input_df[\"manpower_utilisation\"][0]:\n", "        val = \"Manpower utilisation\"\n", "    else:\n", "        val = \"performing well\"\n", "    return val\n", "\n", "\n", "def reason_value(row):\n", "    if row[\"promise_time_hours\"] > input_df[\"pt_hours\"][0]:\n", "        val = str(int(row[\"promise_time_hours\"]))\n", "    elif row[\"delay_time\"] > input_df[\"delay_time\"][0]:\n", "        val = str(int(row[\"delay_time\"]))\n", "    elif row[\"fill_rate\"] < input_df[\"fill_rate\"][0]:\n", "        val = str(int(row[\"fill_rate\"]))\n", "    elif row[\"g_oph\"] > input_df[\"g_oph\"][0]:\n", "        val = str(round((row[\"g_oph\"]), 2))\n", "    elif row[\"p_oph\"] > input_df[\"p_oph\"][0]:\n", "        val = str(round((row[\"p_oph\"]), 2))\n", "    elif row[\"manpower_utilisation\"] < input_df[\"manpower_utilisation\"][0]:\n", "        val = str(round(row[\"manpower_utilisation\"], 2))\n", "    else:\n", "        val = str(round(row[\"manpower_utilisation\"], 2))\n", "    return val\n", "\n", "\n", "def send_mail(subject, html_content, file_path):\n", "    from_email = \"<EMAIL>\"\n", "    to_email = mail_df\n", "    pb.send_email(from_email, to_email, subject, html_content, files=file_path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Active Flag"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if final_df.shape[0] != 0:\n", "    final_df[\"active\"] = final_df.apply(active_function, axis=1)\n", "    final_df[\"reason\"] = final_df.apply(reason_function, axis=1)\n", "    final_df[\"value\"] = final_df.apply(reason_value, axis=1)\n", "\n", "else:\n", "    final_df[\"active\"] = []\n", "    final_df[\"reason\"] = []\n", "    final_df[\"value\"] = []"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["update_active_df = final_df[[\"merchant_id\", \"active\", \"reason\", \"value\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = update_active_df.merge(active_df, how=\"inner\", on=[\"merchant_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = df[[\"merchant_id\", \"merchant_name\", \"active\", \"reason\", \"value\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["no_df = pd.DataFrame(\n", "    columns=[\"merchant_id\", \"merchant_name\", \"active\", \"reason\", \"value\"]\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Update Config Sheet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(df, sheetId, config_sheet, service_account=\"service_account\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["path = \"/tmp/merchant_enable.csv\"\n", "file = df.to_csv(path, index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<H2> Setting Smoothning Factor</H2>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_rule = pb.from_sheets(sheetId, \"SFactorRules\", service_account=\"service_account\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_rule = sf_rule[\n", "    [\"merchant_id\", \"merchant_name\", \"OnTime\", \"FillRate\", \"CapUtili\", \"GOPH\", \"POPH\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_rule[[\"merchant_id\", \"OnTime\", \"FillRate\", \"CapUtili\", \"GOPH\", \"POPH\"]] = sf_rule[\n", "    [\"merchant_id\", \"OnTime\", \"FillRate\", \"CapUtili\", \"GOPH\", \"POPH\"]\n", "].apply(pd.to_numeric)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_rule = sf_rule.rename(\n", "    columns={\n", "        \"OnTime\": \"OnTime_Weightage\",\n", "        \"FillRate\": \"FillRate_Weightage\",\n", "        \"CapUtili\": \"CapUtili_Weightage\",\n", "        \"GOPH\": \"GOPH_Weightage\",\n", "        \"POPH\": \"POPH_Weightage\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ideal_df = pb.from_sheets(sheetId, ideal_parameters, service_account=\"service_account\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ideal_df[[\"merchant_id\", \"GOPH\", \"POPH\"]] = ideal_df[\n", "    [\"merchant_id\", \"GOPH\", \"POPH\"]\n", "].apply(pd.to_numeric)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ideal_df = ideal_df.rename(columns={\"GOPH\": \"ideal_GOPH\", \"POPH\": \"ideal_POPH\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_df = final_df.merge(sf_rule, on=[\"merchant_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_df = sf_df.merge(ideal_df, on=[\"merchant_id\"], how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# sf_df = sf_df.merge(cap_df, on=[\"merchant_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_df = sf_df.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_df[\"goph_rate\"] = sf_df.apply(\n", "    lambda x: int(100 - ((x[\"g_oph\"] * 100) / x[\"ideal_GOPH\"])), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_df[\"poph_rate\"] = sf_df.apply(\n", "    lambda x: int(100 - ((x[\"p_oph\"] * 100) / x[\"ideal_POPH\"])), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_df[\"smoothening_factor\"] = (\n", "    (sf_df[\"on_time\"] / 100) * sf_df[\"OnTime_Weightage\"]\n", "    + (sf_df[\"fill_rate\"] / 100) * sf_df[\"FillRate_Weightage\"]\n", "    #     + (sf_df[\"cap_utilisation\"] / 100) * sf_df[\"CapUtili_Weightage\"]\n", "    + (sf_df[\"goph_rate\"] / 100) * sf_df[\"GOPH_Weightage\"]\n", "    + (sf_df[\"poph_rate\"] / 100) * sf_df[\"POPH_Weightage\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_df[\"smoothening_factor\"] = round(sf_df[\"smoothening_factor\"], 2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_update_df = sf_df[[\"merchant_id\", \"merchant_name\", \"smoothening_factor\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(sf_update_df, sheetId, \"ActiveSFactor\", service_account=\"service_account\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["body_df = df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# body_df = body_df.merge(sf_update_df, on=[\"merchant_id\", \"merchant_name\"], how=\"inner\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Mail Trigger"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if runHour >= 6 and runHour <= 20:\n", "    if body_df.shape[0] != 0:\n", "        subject = (\n", "            \"[Update][\"\n", "            + str(nowIst.strftime(\"%Y-%m-%d\"))\n", "            + \"] || RealTime Capacity Enabled Merchant's\"\n", "        )\n", "        html_content = \"\"\"<p>Please find below for the list of merchants with the smoothening_factor where the realtime capacity is enabled for the next run\n", "                {}\n", "                <p> \n", "                </p>\n", "                <h6>** merchant with reason 'performing well' indicates no disruption and hence reason true with value of manpower utilisation</h6>\"\"\".format(\n", "            body_df.to_html(justify=\"center\", index=False)\n", "        )\n", "        send_mail(subject, html_content, file)\n", "        message = pb.send_slack_message(\n", "            channel=\"planning-dag-alerts\",\n", "            text=\"RealTime Capacity Enable/Disable - Mail Sent\",\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}