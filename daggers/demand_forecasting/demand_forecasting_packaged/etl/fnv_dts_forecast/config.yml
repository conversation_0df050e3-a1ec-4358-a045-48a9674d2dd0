alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 3
dag_name: fnv_dts_forecast
dag_type: etl
escalation_priority: low
execution_timeout: 1440
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: demand_forecasting
notebooks:
- alias: base_prep
  executor_config:
    load_type: very-high-cpu
    node_type: od
  name: notebook_base_prep
  parameters:
    category: fnv
    run_id: '{{ run_id }}'
    task_instance_key_str: '{{ task_instance_key_str }}'
  retries: 2
  retry_delay_in_seconds: 300
  tag: group_1
- alias: training_080
  executor_config:
    load_type: very-high-cpu
    node_type: od
  name: notebook_training_080
  parameters:
    category: fnv
    run_id: '{{ run_id }}'
    task_instance_key_str: '{{ task_instance_key_str }}'
  retries: 2
  retry_delay_in_seconds: 300
  tag: group_2
- alias: training_090
  executor_config:
    load_type: very-high-cpu
    node_type: od
  name: notebook_training_090
  parameters:
    category: fnv
    run_id: '{{ run_id }}'
    task_instance_key_str: '{{ task_instance_key_str }}'
  retries: 2
  retry_delay_in_seconds: 300
  tag: group_2
owner:
  email: <EMAIL>
  slack_id: U071XRARZAS
path: demand_forecasting/demand_forecasting_packaged/etl/fnv_dts_forecast
paused: false
pool: demand_forecasting_pool
project_name: demand_forecasting_packaged
schedule:
  end_date: '2025-09-15T00:00:00'
  interval: 25 0 * * *
  start_date: '2025-06-24T00:00:00'
schedule_type: fixed
sla: 121 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 1
