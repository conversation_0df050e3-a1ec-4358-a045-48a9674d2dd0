alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 10
dag_name: demand_forecasting_packaged_pipeline
dag_type: etl
escalation_priority: low
execution_timeout: 4320
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: demand_forecasting
notebooks:
- alias: notebook_clustering
  executor_config:
    load_type: very-high-cpu
    node_type: spot
  name: notebook_clustering
  parameters:
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_1
- alias: notebook_0
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 0
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_1
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 1
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_2
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 2
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_3
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 3
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_4
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 4
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_5
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 5
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_6
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 6
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_7
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 7
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_8
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 8
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_9
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 9
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_10
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 10
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_11
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 11
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_12
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 12
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_13
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 13
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_14
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 14
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_15
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 15
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_16
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 16
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_17
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 17
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_18
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 18
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_19
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 19
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_20
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 20
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_21
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 21
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_22
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 22
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_23
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 23
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_24
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 24
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_25
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 25
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_26
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 26
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_27
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 27
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_28
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 28
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_29
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 29
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_30
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 30
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_31
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 31
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_32
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 32
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_33
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 33
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_34
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 34
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_35
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 35
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_36
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 36
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_37
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 37
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_38
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 38
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_39
  executor_config:
    load_type: ultra-high-cpu
    node_type: spot
  name: notebook_data_prep
  parameters:
    dag_name: demand_forecasting_packaged_pipeline
    dagger_id: 0
    notebook_id: 39
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_2
- alias: notebook_training_0
  executor_config:
    load_type: ultra-high-cpu
    node_type: od
  name: notebook_training
  parameters:
    notebook_id: 0
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_3
- alias: notebook_training_1
  executor_config:
    load_type: ultra-high-cpu
    node_type: od
  name: notebook_training
  parameters:
    notebook_id: 1
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_3
- alias: notebook_training_2
  executor_config:
    load_type: ultra-high-cpu
    node_type: od
  name: notebook_training
  parameters:
    notebook_id: 2
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_3
- alias: notebook_training_3
  executor_config:
    load_type: ultra-high-cpu
    node_type: od
  name: notebook_training
  parameters:
    notebook_id: 3
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_3
- alias: notebook_training_4
  executor_config:
    load_type: ultra-high-cpu
    node_type: od
  name: notebook_training
  parameters:
    notebook_id: 4
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_3
- alias: notebook_training_5
  executor_config:
    load_type: ultra-high-cpu
    node_type: od
  name: notebook_training
  parameters:
    notebook_id: 5
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_3
- alias: notebook_training_6
  executor_config:
    load_type: ultra-high-cpu
    node_type: od
  name: notebook_training
  parameters:
    notebook_id: 6
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_3
- alias: notebook_training_7
  executor_config:
    load_type: ultra-high-cpu
    node_type: od
  name: notebook_training
  parameters:
    notebook_id: 7
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_3
- alias: notebook_training_8
  executor_config:
    load_type: ultra-high-cpu
    node_type: od
  name: notebook_training
  parameters:
    notebook_id: 8
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_3
- alias: notebook_training_9
  executor_config:
    load_type: ultra-high-cpu
    node_type: od
  name: notebook_training
  parameters:
    notebook_id: 9
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_3
- alias: notebook_guardrails_0
  executor_config:
    load_type: very-high-cpu
    node_type: od
  name: notebook_guardrails
  parameters:
    notebook_id: 0
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_4
- alias: notebook_guardrails_1
  executor_config:
    load_type: very-high-cpu
    node_type: od
  name: notebook_guardrails
  parameters:
    notebook_id: 1
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_4
- alias: notebook_guardrails_2
  executor_config:
    load_type: very-high-cpu
    node_type: od
  name: notebook_guardrails
  parameters:
    notebook_id: 2
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_4
- alias: notebook_guardrails_3
  executor_config:
    load_type: very-high-cpu
    node_type: od
  name: notebook_guardrails
  parameters:
    notebook_id: 3
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_4
- alias: notebook_guardrails_4
  executor_config:
    load_type: very-high-cpu
    node_type: od
  name: notebook_guardrails
  parameters:
    notebook_id: 4
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_4
- alias: notebook_guardrails_5
  executor_config:
    load_type: very-high-cpu
    node_type: od
  name: notebook_guardrails
  parameters:
    notebook_id: 5
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_4
- alias: notebook_guardrails_6
  executor_config:
    load_type: very-high-cpu
    node_type: od
  name: notebook_guardrails
  parameters:
    notebook_id: 6
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_4
- alias: notebook_guardrails_7
  executor_config:
    load_type: very-high-cpu
    node_type: od
  name: notebook_guardrails
  parameters:
    notebook_id: 7
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_4
- alias: notebook_guardrails_8
  executor_config:
    load_type: very-high-cpu
    node_type: od
  name: notebook_guardrails
  parameters:
    notebook_id: 8
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_4
- alias: notebook_guardrails_9
  executor_config:
    load_type: very-high-cpu
    node_type: od
  name: notebook_guardrails
  parameters:
    notebook_id: 9
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_4
- alias: update_qty_preds
  executor_config:
    load_type: very-high-cpu
    node_type: spot
  name: notebook_update_preds
  parameters:
    run_id: '{{ run_id }}'
    variable: qty
  retries: 3
  tag: group_5
- alias: update_search_preds
  executor_config:
    load_type: very-high-cpu
    node_type: spot
  name: notebook_update_preds
  parameters:
    run_id: '{{ run_id }}'
    variable: search
  retries: 3
  tag: group_5
- alias: notebook_cold_start
  executor_config:
    load_type: very-high-cpu
    node_type: spot
  name: notebook_cold_start
  parameters:
    run_id: '{{ run_id }}'
  retries: 3
  tag: group_6
owner:
  email: <EMAIL>
  slack_id: U07CHK59SPN
path: demand_forecasting/demand_forecasting_packaged/etl/demand_forecasting_packaged_pipeline
paused: false
pool: demand_forecasting_pool
project_name: demand_forecasting_packaged
schedule:
  end_date: '2025-08-26T00:00:00'
  interval: null
  start_date: '2025-06-25T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- config.py
tags: []
template_name: multi_notebook
version: 37
