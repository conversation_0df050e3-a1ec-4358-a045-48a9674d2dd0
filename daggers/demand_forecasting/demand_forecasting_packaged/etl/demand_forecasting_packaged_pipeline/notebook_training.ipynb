{"cells": [{"cell_type": "code", "execution_count": null, "id": "9f8a590d-4ef9-437d-bfdb-3b2949a49e8f", "metadata": {"tags": []}, "outputs": [], "source": ["!pip install papermill==2.3.0\n", "!pip install awswrangler==3.9.1\n", "!pip install numpy==1.26.4\n", "!pip install pandas==1.3.5"]}, {"cell_type": "code", "execution_count": null, "id": "24f00029-7810-4f86-8e39-5c2ce02615ff", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import time\n", "import binascii\n", "import pandas as pd\n", "import pencilbox as pb\n", "import papermill as pm\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "e4dc379a-207e-4b31-b4d8-4660b271bac4", "metadata": {}, "outputs": [], "source": ["cwd = \"/usr/local/airflow/dags/repo/dags/demand_forecasting/demand_forecasting_packaged/etl/demand_forecasting_packaged_pipeline\"\n", "sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "9a77de69-a69f-4ccc-aba9-52a27282d4d5", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["notebook_id = 0\n", "run_id = \"manual__2024-11-12T17:57:08.895028+00:00\""]}, {"cell_type": "code", "execution_count": null, "id": "06174d98-37a2-4d3c-a0de-74f2a65581bd", "metadata": {}, "outputs": [], "source": ["from config import city_list, ptypes_list"]}, {"cell_type": "code", "execution_count": null, "id": "5a0dea08-ade8-4d62-8b52-8848cc5e700b", "metadata": {}, "outputs": [], "source": ["search_exp_id = f\"exp_{run_id.split('T')[0].split('__')[1]}_{str(int(run_id.split('.')[1].split('+')[0]))}\".replace(\n", "    \"-\", \"_\"\n", ")\n", "qty_exp_id = f\"exp_{run_id.split('T')[0].split('__')[1]}_{str(int(run_id.split('.')[1].split('+')[0])+1)}\".replace(\n", "    \"-\", \"_\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "42cfa1c8-138d-43e8-a981-8743829e4ad7", "metadata": {}, "outputs": [], "source": ["# # # # # # Use this to manually enter exp ids\n", "# search_exp_id = \"exp_2025_06_20_145202\"\n", "# qty_exp_id = \"exp_2025_06_20_145203\""]}, {"cell_type": "code", "execution_count": null, "id": "9fa69efa-bc23-478d-b071-063c4d6afa14", "metadata": {}, "outputs": [], "source": ["folder_name = f\"prod_run_{run_id.split('T')[0].split('__')[1]}\".replace(\"-\", \"_\")"]}, {"cell_type": "code", "execution_count": null, "id": "f6b228a2-37b5-430f-a31f-7b4a2cc6cab6", "metadata": {}, "outputs": [], "source": ["# # # # # # # # Use this to manually enter folder name\n", "# folder_name = \"prod_run_2025_06_20\""]}, {"cell_type": "code", "execution_count": null, "id": "a0026afd-cf40-465a-b5c9-a4b8caf52227", "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.clone_repo(\"data-science-models\", \"/tmp\", \"master\")\n", "except Exception as e:\n", "    print(f\"Exception occured while cloning, error msg = {e}\")"]}, {"cell_type": "code", "execution_count": null, "id": "9821dc87-55b1-4ab6-93ec-8f596a58f464", "metadata": {}, "outputs": [], "source": ["os.chdir(\"/tmp/data-science-models/projects/forecasting\")"]}, {"cell_type": "code", "execution_count": null, "id": "20f1bf02-5967-4c7f-a85f-cce01cac4d42", "metadata": {"tags": []}, "outputs": [], "source": ["!pip install -r requirements"]}, {"cell_type": "code", "execution_count": null, "id": "06c6bd66-1470-4755-a6f9-18812b203ccf", "metadata": {}, "outputs": [], "source": ["os.chdir(\"/tmp/data-science-models/projects/forecasting/dags\")\n", "\n", "tic = time.time()\n", "\n", "pb.send_slack_message(\n", "    channel=\"bl_forecasting_process_alerts\",\n", "    text=f\"Starting PTYPE CITY TRAINING VIA DAG\",\n", ")\n", "\n", "pm.execute_notebook(\n", "    input_path=\"/tmp/data-science-models/projects/forecasting/dags/training_city_ptype.ipynb\",\n", "    output_path=\"/tmp/data-science-models/projects/forecasting/dags/training_city_ptype_output.ipynb\",\n", "    parameters={\n", "        \"cities\": city_list,\n", "        \"ptypes\": ptypes_list,\n", "        \"s3_base_path\": f\"demand_forecasting/packaged/production_data/{folder_name}\",\n", "        \"read_from_s3\": True,\n", "        \"qty_exp_id\": qty_exp_id,\n", "        \"search_exp_id\": search_exp_id,\n", "        \"reset_run\": 0,\n", "        \"notebook_id\": notebook_id,\n", "    },\n", ")\n", "\n", "toc = time.time()\n", "\n", "pb.send_slack_message(\n", "    channel=\"bl_forecasting_process_alerts\",\n", "    text=f\"CITY PTYPE Training for {len(city_list)} cities and {len(ptypes_list)} ptypes completed at {datetime.now()}. Time taken: {round((toc-tic)/60,2)} minutes\",\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}