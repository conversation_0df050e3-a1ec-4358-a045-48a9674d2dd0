{"cells": [{"cell_type": "code", "execution_count": null, "id": "6581dbe0-a474-4bf3-8687-87dc5f5df6c3", "metadata": {"tags": []}, "outputs": [], "source": ["!pip install papermill==2.3.0\n", "!pip install awswrangler==3.9.1\n", "!pip install numpy==1.26.4\n", "!pip install pandas==1.3.5"]}, {"cell_type": "code", "execution_count": null, "id": "4c2cb159-6361-4b52-89b5-2be7c7cf5fa1", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import time\n", "import pandas as pd\n", "import pencilbox as pb\n", "import papermill as pm\n", "from datetime import datetime, timedelta\n", "import time"]}, {"cell_type": "code", "execution_count": null, "id": "a058b729-66fa-44f9-b40f-320d4c693177", "metadata": {}, "outputs": [], "source": ["time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "48019d7a-1970-409b-9695-bfcc2d21e71f", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["notebook_id = 0\n", "dagger_id = 0\n", "dag_name = \"demand_forecasting_packaged_pipeline\"\n", "run_id = \"manual__2024-11-12T17:57:08.895028+00:00\""]}, {"cell_type": "code", "execution_count": null, "id": "7c400005-1bba-424d-8b4e-3fab80e0301d", "metadata": {}, "outputs": [], "source": ["qty_exp_id = f\"exp_{run_id.split('T')[0].split('__')[1]}_{str(int(run_id.split('.')[1].split('+')[0])+1)}\".replace(\n", "    \"-\", \"_\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "08c0955a-1ad2-4d4e-bbde-e50a9cdf49ac", "metadata": {}, "outputs": [], "source": ["# # # # # # Use this to manually enter exp ids\n", "# qty_exp_id = \"exp_2025_06_20_145203\""]}, {"cell_type": "code", "execution_count": null, "id": "6d7a996e-57b5-42c9-9fe5-33d33192e082", "metadata": {}, "outputs": [], "source": ["cwd = f\"/usr/local/airflow/dags/repo/dags/demand_forecasting/demand_forecasting_packaged/etl/{dag_name}\""]}, {"cell_type": "code", "execution_count": null, "id": "ae5c1358-be2e-45bb-951d-d407f6c5da28", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "7ea70b7a-e33d-44b2-be50-be9f482509ca", "metadata": {}, "outputs": [], "source": ["folder_name = f\"prod_run_{run_id.split('T')[0].split('__')[1]}\".replace(\"-\", \"_\")"]}, {"cell_type": "code", "execution_count": null, "id": "062412f1-2393-43cb-84b0-9bb04693b3f0", "metadata": {}, "outputs": [], "source": ["# # # # # # # Use this to manually enter folder name\n", "# folder_name = \"prod_run_2025_06_20\""]}, {"cell_type": "code", "execution_count": null, "id": "98a2d969-c7c3-4219-a6c3-f11c85d882e3", "metadata": {}, "outputs": [], "source": ["from config import city_list, schema"]}, {"cell_type": "code", "execution_count": null, "id": "15a0dc45-197d-4d88-94e0-8e78e0b871df", "metadata": {}, "outputs": [], "source": ["for i in schema[dagger_id][1]:\n", "    if i[0] % 40 == notebook_id:\n", "        ptypes = list(set(i[1]))"]}, {"cell_type": "code", "execution_count": null, "id": "b1aaa533-cbe8-4e48-a654-8183202228f4", "metadata": {}, "outputs": [], "source": ["if \"ptypes\" in globals():\n", "    pass\n", "else:\n", "    ptypes = []\n", "\n", "if len(ptypes) > 0:\n", "\n", "    ptype_list = ptypes\n", "else:\n", "    ptype_list = []"]}, {"cell_type": "code", "execution_count": null, "id": "220df5eb-1054-4e7a-b212-20cbf87537af", "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.clone_repo(\"data-science-models\", \"/tmp\", \"master\")\n", "except:\n", "    pb.send_slack_message(\n", "        channel=\"bl_forecasting_process_alerts\",\n", "        text=f\"Not able to clone repo or repo already cloned!!\",\n", "    )\n", "    pass"]}, {"cell_type": "code", "execution_count": null, "id": "7943fa10-2970-4000-acc6-fde1daec5c45", "metadata": {}, "outputs": [], "source": ["os.chdir(\"/tmp/data-science-models/projects/forecasting\")"]}, {"cell_type": "code", "execution_count": null, "id": "99f2fffc-054d-49bc-ab0e-ec32ae372f21", "metadata": {}, "outputs": [], "source": ["!pip install -r requirements"]}, {"cell_type": "code", "execution_count": null, "id": "2f441cfc-819a-4e5c-b351-84d52dfbaf59", "metadata": {}, "outputs": [], "source": ["# Uncomment if manual intervention required\n", "testing_start_date = \"2025-06-25\"\n", "testing_end_date = \"2025-10-31\"\n", "validation_start_date = str((pd.to_datetime(testing_start_date) - pd.DateOffset(months=1)).date())"]}, {"cell_type": "code", "execution_count": null, "id": "231ea92d-ec99-4088-8a5b-e2bb37203cba", "metadata": {}, "outputs": [], "source": ["if len(ptype_list) > 0:\n", "\n", "    os.chdir(\"/tmp/data-science-models/projects/forecasting/dags\")\n", "\n", "    tic = time.time()\n", "\n", "    pb.send_slack_message(\n", "        channel=\"bl_forecasting_process_alerts\",\n", "        text=f\"Starting PTYPE CITY Data Prep\",\n", "    )\n", "\n", "    # testing_start_date = run_id.split(\"T\")[0].split(\"__\")[1]\n", "    # testing_end_date = str((pd.to_datetime(testing_start_date) + pd.DateOffset(months=3)).date())\n", "    # validation_start_date = str(\n", "    #     (pd.to_datetime(testing_start_date) - pd.DateOffset(months=1)).date()\n", "    # )\n", "\n", "    pm.execute_notebook(\n", "        input_path=\"./data_prep_city_ptype.ipynb\",\n", "        output_path=\"./data_prep_city_ptype_output.ipynb\",\n", "        parameters={\n", "            \"cities\": city_list,\n", "            \"ptypes\": ptype_list,\n", "            \"training_start_date\": \"2023-01-01\",\n", "            \"validation_start_date\": validation_start_date,\n", "            \"testing_start_date\": testing_start_date,\n", "            \"testing_end_date\": testing_end_date,\n", "            \"s3_output_path\": f\"demand_forecasting/packaged/production_data/{folder_name}\",\n", "            \"write_to_s3\": True,\n", "            \"notebook_id\": notebook_id,\n", "            \"dagger_id\": dagger_id,\n", "            \"qty_exp_id\": qty_exp_id,\n", "        },\n", "    )\n", "\n", "    toc = time.time()\n", "\n", "    try:\n", "        pb.send_slack_message(\n", "            channel=\"bl_forecasting_process_alerts\",\n", "            text=f\"Ptype City Data Prep notebook_id {notebook_id} dagger_id {dagger_id} dag_name {dag_name} for {len(city_list)} cities and {len(ptype_list)} ptypes completed at {datetime.now()}. Time taken: {round((toc-tic)/60,2)} minutes\",\n", "        )\n", "    except:\n", "        pass"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}