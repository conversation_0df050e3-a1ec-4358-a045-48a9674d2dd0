{"cells": [{"cell_type": "code", "execution_count": null, "id": "9f8a590d-4ef9-437d-bfdb-3b2949a49e8f", "metadata": {"tags": []}, "outputs": [], "source": ["!pip install papermill==2.3.0\n", "!pip install awswrangler==3.9.1\n", "!pip install numpy==1.26.4\n", "!pip install pandas==1.3.5"]}, {"cell_type": "code", "execution_count": null, "id": "24f00029-7810-4f86-8e39-5c2ce02615ff", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import time\n", "import binascii\n", "import pandas as pd\n", "import pencilbox as pb\n", "import papermill as pm\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "e4dc379a-207e-4b31-b4d8-4660b271bac4", "metadata": {}, "outputs": [], "source": ["cwd = \"/usr/local/airflow/dags/repo/dags/demand_forecasting/demand_forecasting_packaged/etl/cold_start_predictions\"\n", "sys.path.append(cwd)"]}, {"cell_type": "markdown", "id": "bff6305d-46c8-496d-9815-b378ad62c0b4", "metadata": {}, "source": ["# Modify The Below Cell Before Pushing the DAG"]}, {"cell_type": "code", "execution_count": null, "id": "65d5f611-b214-4e40-abb5-17b47a5a2bfd", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["run_id = \"manual__2024-11-12T17:57:08.895028+00:00\""]}, {"cell_type": "code", "execution_count": null, "id": "367158db-0a9a-4a69-a47f-1245019324aa", "metadata": {}, "outputs": [], "source": ["search_exp_id = f\"exp_{run_id.split('T')[0].split('__')[1]}_{str(int(run_id.split('.')[1].split('+')[0]))}\".replace(\n", "    \"-\", \"_\"\n", ")\n", "qty_exp_id = f\"exp_{run_id.split('T')[0].split('__')[1]}_{str(int(run_id.split('.')[1].split('+')[0])+1)}\".replace(\n", "    \"-\", \"_\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b8dd1e41-5f2e-4ad5-ab2b-c9d8ca425225", "metadata": {}, "outputs": [], "source": ["# # # # Use this to manually enter exp ids\n", "# search_exp_id = \"exp_2025_06_20_145202\"\n", "# qty_exp_id = \"exp_2025_06_20_145203\""]}, {"cell_type": "code", "execution_count": null, "id": "382c8f54-976b-4d5c-838f-140a680e50ea", "metadata": {}, "outputs": [], "source": ["folder_name = f\"prod_run_{run_id.split('T')[0].split('__')[1]}\".replace(\"-\", \"_\")"]}, {"cell_type": "code", "execution_count": null, "id": "84d999ed-3ef8-4683-be98-e6062b8f64be", "metadata": {}, "outputs": [], "source": ["# # # # # # # Use this to manually enter folder name\n", "# folder_name = \"prod_run_2025_06_20\""]}, {"cell_type": "code", "execution_count": null, "id": "a0026afd-cf40-465a-b5c9-a4b8caf52227", "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.clone_repo(\"data-science-models\", \"/tmp\", \"master\")\n", "except Exception as e:\n", "    print(f\"Exception occured while cloning, error msg = {e}\")"]}, {"cell_type": "code", "execution_count": null, "id": "9821dc87-55b1-4ab6-93ec-8f596a58f464", "metadata": {}, "outputs": [], "source": ["os.chdir(\"/tmp/data-science-models/projects/forecasting\")"]}, {"cell_type": "code", "execution_count": null, "id": "20f1bf02-5967-4c7f-a85f-cce01cac4d42", "metadata": {"tags": []}, "outputs": [], "source": ["!pip install -r requirements"]}, {"cell_type": "code", "execution_count": null, "id": "b7e2fa40-2f44-4335-904f-f5b568be331c", "metadata": {}, "outputs": [], "source": ["os.chdir(\"/tmp/data-science-models/projects/forecasting/dags\")"]}, {"cell_type": "code", "execution_count": null, "id": "f96ac7a9-6470-4984-9e87-1c39d180a314", "metadata": {}, "outputs": [], "source": ["tic = time.time()"]}, {"cell_type": "code", "execution_count": null, "id": "d53490e5-1c02-456f-8887-5781557aeb6b", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"bl_forecasting_process_alerts\",\n", "    text=f\"Starting Cold Start VIA DAG\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e8d93791-697b-41f4-b742-79298efedc07", "metadata": {}, "outputs": [], "source": ["# Uncomment if manual intervention required\n", "testing_start_date = \"2025-06-25\"\n", "testing_end_date = \"2025-10-31\"\n", "validation_start_date = str((pd.to_datetime(testing_start_date) - pd.DateOffset(months=1)).date())"]}, {"cell_type": "code", "execution_count": null, "id": "32c9bf52-1aef-4b98-8b2f-26b118fe0643", "metadata": {}, "outputs": [], "source": ["# testing_start_date = run_id.split(\"T\")[0].split(\"__\")[1]\n", "# testing_end_date = str((pd.to_datetime(testing_start_date) + pd.DateOffset(months=3)).date())\n", "# validation_start_date = str((pd.to_datetime(testing_start_date) - pd.DateOffset(months=1)).date())"]}, {"cell_type": "code", "execution_count": null, "id": "77dae1f4-fb8b-49b0-82ae-2bef12423f45", "metadata": {"tags": []}, "outputs": [], "source": ["pm.execute_notebook(\n", "    input_path=\"/tmp/data-science-models/projects/forecasting/cold_start/cold_start_forecast.ipynb\",\n", "    output_path=\"/tmp/data-science-models/projects/forecasting/cold_start/cold_start_forecast_output.ipynb\",\n", "    parameters={\n", "        \"training_start_date\": \"2023-01-01\",\n", "        \"validation_start_date\": validation_start_date,\n", "        \"testing_start_date\": testing_start_date,\n", "        \"testing_end_date\": testing_end_date,\n", "        \"qty_exp_id\": qty_exp_id,\n", "        \"search_exp_id\": search_exp_id,\n", "        \"s3_base_path\": f\"demand_forecasting/packaged/production_data/{folder_name}\",\n", "    },\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "cf6536df-3b07-4d75-b697-2279a95268f9", "metadata": {}, "outputs": [], "source": ["toc = time.time()"]}, {"cell_type": "code", "execution_count": null, "id": "9f94b535-b3d8-4f10-8994-56058ce36c71", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"bl_forecasting_process_alerts\",\n", "    text=f\"COLD START completed at {datetime.now()}. Time taken: {round((toc-tic)/60,2)} minutes\",\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}