import pandas as pd
import numpy as np
import pencilbox as pb


testing_start_date = '2025-06-25'
testing_end_date = '2025-10-31'

ptypes_list_init = [
'Aasan',
 'Agarbatti Stand',
 'Almonds',
 '<PERSON><PERSON>',
 '<PERSON><PERSON><PERSON>',
 '<PERSON> Kit',
 '<PERSON><PERSON><PERSON><PERSON>',
 'Banana Chips',
 '<PERSON><PERSON>',
 '<PERSON><PERSON>',
 '<PERSON>ash<PERSON>',
 'Betel Nuts',
 'Big Sabudana',
 'Bin<PERSON>',
 'Biscuit',
 'Board Game',
 'Book',
 'Bracelet Sets',
 'Camphor / Kapur',
 'Cardamom Powder',
 'Cashew',
 '<PERSON>a Masala',
 '<PERSON><PERSON>',
 'Chandan Sticks',
 'Chandan <PERSON>ika',
 '<PERSON><PERSON><PERSON><PERSON>',
 '<PERSON><PERSON><PERSON>',
 'Clay',
 'Clove Whole',
 'Coconut Powder',
 'Container Set',
 'Contemporary Jewellery Set',
 'Cow Ghee',
 'Craft Kit',
 'Cushioned Hair Brush',
 'Dandiya',
 'Deity Photo',
 'Dhoop',
 'Dhoop Cones',
 'Dhoop Sticks',
 '<PERSON><PERSON><PERSON> Diya',
 'Digital Writing Pad',
 'Dishwash Tablet',
 'Disposable Bowl',
 'Disposable Fork',
 'Disposable Glass',
 'Disposable Plates',
 'Disposable Spoon',
 'Doll',
 'Doll Playset',
 'Dressing Hair Comb',
 'Dried Coconut',
 'Drops & Danglers',
 'Dry Fruit Mix',
 'Dry Shampoo',
 'Ear Chain',
 'Ear Cuffs',
 'Earring Sets',
 'Educational Toy',
 'Ethnic Jewellery Set',
 'Flat Hair Brush',
 'Flavoured Makhana',
 'Gangajal',
 'Ghee',
 'Ghee Diya Batti',
 'God Idol',
 'Gomti Chakra',
 'Groundnut Oil',
 'Guggal',
 'Hair Band',
 'Hair Brush',
 'Hair Clip',
 'Hair Clip Sets',
 'Hair Colouring Brush',
 'Hair Colouring Kit',
 'Hair Comb',
 'Hair Finishing Stick',
 'Hair Mist',
 'Hair Pin',
 'Hair Powder',
 'Hair Styling Cream',
 'Hair Ties',
 'Hoops & Huggies',
 'Incense Sticks / Agarbatti',
 'Itra',
 'Jaggery',
 'Jaggery Cubes',
 'Jaggery Powder',
 'Janeu',
 'Jewellery Kit',
 'Jhumkas',
 'Jigsaw Puzzle',
 'Kala Chana',
 'Kalash',
 'Kamal Seeds',
 'Kaudi shells',
 'Kitchen Play Set',
 'Kurta',
 'Kutki/Little Millet',
 'Kuttu Flour',
 'Kuttu Giri',
 'Lachha',
 'Long Diya Batti',
 'Lotus Flower',
 'Matchbox',
 'Mehendi Cone',
 'Mishri',
 'Modelling Clay Set',
 'Moli',
 'Mouse Pad',
 'Nakul Dana',
 'Nutmeg Whole',
 'Organic Buckwheat/Kuttu Flour',
 'Organic Peanuts',
 'Organic Singhara Atta',
 'Oval Hair Brush',
 'Petha',
 'Phool Diya Batti',
 'Pooja Book',
 'Pooja Chowki',
 'Pooja Cloth',
 'Pooja Oil',
 'Pooja Thali',
 'Potato Wafers',
 'Pull Back Toy',
 'RC Toy',
 'Raisins',
 'Rajgira Flour',
 'Rajgira/Amaranth',
 'Rock Salt',
 'Roli Chawal',
 'Rose',
 'Sabudana',
 'Saffron',
 'Samak/Barnyard Millet',
 'Scrunchie',
 'Shrikhand',
 'Shringar Set',
 'Sindoor',
 'Singhara Flour',
 'Sketch Pens',
 'Slime',
 'Small Sabudana',
 'Sooji',
 'Stationery Kit',
 'Studs',
 'Tail',
 'Toy Car',
 'Toy Phone',
 'Toy Vehicle',
 'Turmeric Whole',
 'Vanaspati',
 'Wax Crayons',
'Bakery Sweets',
'Colour Paper',
'Cookie Mix',
'Deodorant Roll on',
'Fresh Assorted Sweets',
'Nimbu Mirchi',
'Samosas',
'Handi',
'Indian Starters',
'Instant Voucher',
'Makeup Palette',
'Pastries & Cakes',
'Photo Album',
'Premium Earrings',
'Sweet Pheni'
]
    

    
    

ptypes_list = []
for ptype in ptypes_list_init:
    if ptype in ptypes_list:
        continue
    else:
        ptypes_list.append(ptype)

cities_list = ['Kharagpur', 'Kurnool', 'Bharuch', 'Ludhiana', 'Gwalior',
       'Nellore', 'Bhimavaram', 'Dharamshala', 'Rajahmundry', 'Bardhaman',
       'Jind', 'Narmadapuram', 'Manali', 'Mohali', 'Kochi', 'Sirsa',
       'Sangrur', 'Varanasi', 'Rajkot', 'Chandrapur', 'Firozpur', 'Latur',
       'Bhopal', 'Vellore', 'Belgaum', 'Hamirpur', 'Hoshiarpur',
       'Bikaner', 'Unnao', 'Erode', 'Begusarai', 'Bengaluru', 'Faridabad',
       'Patiala', 'Bareilly', 'Kaithal', 'Bhavnagar', 'Hosur', 'Batala',
       'Mysore', 'Cuttack', 'Coimbatore', 'Panipat', 'Jabalpur', 'Rewari',
       'Jaunpur', 'Ahmedabad', 'Haridwar', 'Barabanki', 'Gulbarga',
       'Shillong', 'Indore', 'Jalandhar', 'Anand', 'Dhanbad',
       'Aurangabad', 'Kolhapur', 'Shimoga', 'Madurai', 'New Chandigarh',
       'Zirakpur', 'Dehradun', 'Raebareli', 'Fatehpur', 'Kanpur',
       'Panchkula', 'Bhiwadi', 'Durgapur', 'Ranchi', 'Thrissur', 'Gonda',
       'Rajpura', 'Meerut', 'Agra', 'Roorkee', 'Hisar', 'Solan',
       'Darbhanga', 'Surat', 'Jammu', 'Ankleshwar', 'Dewas', 'Purnia',
       'Jamshedpur', 'Manipal', 'Bhagalpur', 'Yamunanagar', 'Tumkur',
       'Ayodhya', 'Muzaffarnagar', 'Pathankot', 'Gaya', 'Trivandrum',
       'Davanagere', 'Karnal', 'Dharwad', 'Moradabad', 'Kozhikode',
       'Siliguri', 'Goa', 'Ujjain', 'Satna', 'Bhubaneswar', 'Kakinada',
       'Hyderabad', 'Bokaro', 'Ballari', 'Lucknow', 'Bathinda', 'Patna',
       'Chennai', 'Chandigarh', 'Guwahati', 'Navsari', 'Haldwani',
       'Vrindavan', 'Pune', 'Rohtak', 'Kapurthala', 'Prayagraj', 'Raipur',
       'Rampur', 'Vizianagaram', 'Hapur', 'Akola', 'Phagwara', 'Nagpur',
       'Baddi', 'Hubli', 'Warangal', 'Muzaffarpur', 'Kurukshetra',
       'Sundar Nagar', 'Nadiad', 'Kharar', 'Visakhapatnam', 'Alwar',
       'Mathura', 'Nashik', 'Durg', 'Palwal', 'Mehsana', 'Bhuj',
       'Kottayam', 'Salem', 'Deoria', 'Mumbai', 'Sonipat', 'Modinagar',
       'Udupi', 'Sikar', 'Kolkata', 'Tirupati', 'Bahadurgarh',
       'Gandhidham', 'HR-NCR', 'Lonavala', 'Tiruchirapalli', 'Asansol',
       'Puducherry', 'Firozabad', 'Amritsar', 'Jaipur', 'Kota', 'Jodhpur',
       'Guntur', 'Lakhimpur', 'Una', 'Puri', 'Bijapur', 'Vadodara',
       'UP-NCR', 'Jhansi', 'Rudrapur', 'Khanna', 'Satara', 'Mangalore',
       'Kotdwar', 'Delhi', 'Rishikesh', 'Aligarh', 'Sri Ganganagar',
       'Bhiwani', 'Bidar', 'Moga', 'Udaipur', 'Gorakhpur', 'Ambala',
       'Solapur', 'Ajmer', 'Vijayawada', 'Saharanpur', 'Vapi', 'Sitapur',
       'Hassan', 'Amravati']

################################################# DO NOT UPDATE THIS #######################################################
citylist = list([
    "HR-NCR"
    if x in ["Gurugram", "Gurgaon", "HR-NCR"]
    else "UP-NCR"
    if x in ["Noida", "Ghaziabad"]
    else "Goa"
    if x in ["Goa", "NorthGoa", "SouthGoa", "North-Goa", "North Goa", "South-Goa", "South Goa"]
    else x
    for x in cities_list
])

city_list = []
for item in citylist:
    if item not in city_list:
        city_list.append(item)

        

        
### Below is to obtain only those cities which have weather data till test end date    
sql_city_wd_check = f"""
with cte as (
select 
    city,
    max(date) as max_date_wd
from ds_etls.demand_forecasting_global_ft_base
WHERE city in {tuple(city_list)}
AND date >= '{testing_start_date}'
AND date <= '{testing_end_date}'
AND feature_name IN ('temp',
                   'temp_min',
                   'temp_max',
                   'pressure',
                   'humidity',
                   'precipitation')
group by 1
)

select 
    distinct city
from cte
where max_date_wd >= '{testing_end_date}'
"""
CON_TRINO = pb.get_connection("[Warehouse] Trino")
df_city_wd =  pd.read_sql_query(sql = sql_city_wd_check, con = CON_TRINO)
city_list2 = list(df_city_wd['city'].unique())  
# To preserve the ordering
city_list = [i for i in city_list if i in city_list2]
        

cnfg = """
{{
'alias': '{A}',
  'executor_config': {{'load_type': 'ultra-high-cpu', 'node_type': 'spot'}},
  'name': 'notebook_0',
  'parameters': {{'notebook_id': {B}}},
  'retries': 3,
  'tag': 'group_1'
}},
"""

def divide_list(input_list,length,city_flag=1):
    
    result = {}
    last_key = None  
    for i in range(0, len(input_list), length):
        key = (i // length)

        if len(input_list) - i < 2 and last_key is not None and city_flag:
            result[last_key].extend(input_list[i:])
        else:
            result[key] = input_list[i:i+length]
            last_key = key

    return result





def create_config(ptypes,cities,number_nb,config,utilization_perc):
    
    dag = {}
    import numpy as np
    # combo_per_nb = (utilization_perc * 17.00)//len(ptypes)
    combo_per_nb = (utilization_perc * 17.00)//len(cities)
    
    res = divide_list(ptypes,int(combo_per_nb),1)
    
    if len(res) >number_nb:
        print(f"the number of notebooks that will be created will be {len(res)}, however, the limit of notebooks is {number_nb}. Falling back to creating {number_nb} notebooks only and creating {np.ceil(len(res)/number_nb)} dags")
        
    for dag_id in range(int(np.ceil(len(res)/number_nb))):
        
        result = divide_list(list(res.items()),number_nb,0)
        for key in range(0,len(result[dag_id])):
            if key == 0:
                conf = config.format(A = f'notebook_{key}',B = f'{key}')
            else:
                conf += config.format(A = f'notebook_{key}',B = f'{key}')
        conf = conf[0:len(conf)-2]
        dag[dag_id] = (conf,result[dag_id])
        
    return dag

schema = create_config(ptypes_list,city_list,40,cnfg,60)
