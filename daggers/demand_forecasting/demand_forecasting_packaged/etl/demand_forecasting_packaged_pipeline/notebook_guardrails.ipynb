{"cells": [{"cell_type": "code", "execution_count": null, "id": "6581dbe0-a474-4bf3-8687-87dc5f5df6c3", "metadata": {"tags": []}, "outputs": [], "source": ["!pip install papermill==2.3.0\n", "!pip install awswrangler==3.9.1\n", "!pip install numpy==1.26.4\n", "!pip install pandas==1.3.5"]}, {"cell_type": "code", "execution_count": null, "id": "4c2cb159-6361-4b52-89b5-2be7c7cf5fa1", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import time\n", "import pandas as pd\n", "import pencilbox as pb\n", "import papermill as pm\n", "from datetime import datetime, timedelta\n", "import time"]}, {"cell_type": "code", "execution_count": null, "id": "ed2e271f-e41e-4759-b3da-00597d5e8feb", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["notebook_id = 0\n", "run_id = \"manual__2024-11-12T17:57:08.895028+00:00\""]}, {"cell_type": "code", "execution_count": null, "id": "a058b729-66fa-44f9-b40f-320d4c693177", "metadata": {}, "outputs": [], "source": ["time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "6d7a996e-57b5-42c9-9fe5-33d33192e082", "metadata": {}, "outputs": [], "source": ["cwd = f\"/usr/local/airflow/dags/repo/dags/demand_forecasting/demand_forecasting_packaged/etl/demand_forecasting_packaged_pipeline\""]}, {"cell_type": "code", "execution_count": null, "id": "ae5c1358-be2e-45bb-951d-d407f6c5da28", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "6ae12055-3817-410f-b43e-70e938135d4f", "metadata": {}, "outputs": [], "source": ["from config import city_list, ptypes_list"]}, {"cell_type": "code", "execution_count": null, "id": "338ab5bb-333a-488b-a9f4-c9774efacf57", "metadata": {}, "outputs": [], "source": ["search_exp_id = f\"exp_{run_id.split('T')[0].split('__')[1]}_{str(int(run_id.split('.')[1].split('+')[0]))}\".replace(\n", "    \"-\", \"_\"\n", ")\n", "qty_exp_id = f\"exp_{run_id.split('T')[0].split('__')[1]}_{str(int(run_id.split('.')[1].split('+')[0])+1)}\".replace(\n", "    \"-\", \"_\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3b7a4b85-10c4-4e86-82aa-72dd4b085b62", "metadata": {}, "outputs": [], "source": ["# # # # # # Use this to manually enter exp ids\n", "# search_exp_id = \"exp_2025_06_20_145202\"\n", "# qty_exp_id = \"exp_2025_06_20_145203\""]}, {"cell_type": "code", "execution_count": null, "id": "40c54de7-0749-4056-8644-61267dc3a2bb", "metadata": {}, "outputs": [], "source": ["folder_name = f\"prod_run_{run_id.split('T')[0].split('__')[1]}\".replace(\"-\", \"_\")"]}, {"cell_type": "code", "execution_count": null, "id": "d3f6c343-ddb3-4797-a969-b9dde4f7ef35", "metadata": {}, "outputs": [], "source": ["# # # # # # # # # Use this to manually enter folder name\n", "# folder_name = \"prod_run_2025_06_20\""]}, {"cell_type": "code", "execution_count": null, "id": "716caee8-e8dd-4023-97ad-4a503b974faa", "metadata": {}, "outputs": [], "source": ["def divide_into_parts(input_list):\n", "    n = len(input_list)\n", "    part_size = n // 10\n", "    remainder = n % 10\n", "\n", "    result = [[] for _ in range(10)]\n", "\n", "    index = 0\n", "    for i in range(10):\n", "\n", "        current_part_size = part_size + (1 if i < remainder else 0)\n", "\n", "        for _ in range(current_part_size):\n", "            if index < n:\n", "                result[i].append(input_list[index])\n", "                index += 1\n", "\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "id": "bfa2a867-6e1c-496f-889b-24d9697d2c6f", "metadata": {}, "outputs": [], "source": ["ptypes = divide_into_parts(ptypes_list)"]}, {"cell_type": "code", "execution_count": null, "id": "220df5eb-1054-4e7a-b212-20cbf87537af", "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.clone_repo(\"data-science-models\", \"/tmp\", \"master\")\n", "except Exception as e:\n", "    pb.send_slack_message(\n", "        channel=\"bl_forecasting_process_alerts\",\n", "        text=f\"Not able to clone repo or repo already cloned!!\",\n", "    )\n", "    print(f\"Exception occured while cloning, error msg = {e}\")"]}, {"cell_type": "code", "execution_count": null, "id": "7943fa10-2970-4000-acc6-fde1daec5c45", "metadata": {}, "outputs": [], "source": ["os.chdir(\"/tmp/data-science-models/projects/forecasting\")"]}, {"cell_type": "code", "execution_count": null, "id": "99f2fffc-054d-49bc-ab0e-ec32ae372f21", "metadata": {}, "outputs": [], "source": ["!pip install -r requirements"]}, {"cell_type": "code", "execution_count": null, "id": "3b2c4d55-cb43-45da-9ebd-b5e7636193ab", "metadata": {}, "outputs": [], "source": ["# # Uncomment if manual intervention required\n", "testing_start_date = \"2025-06-25\"\n", "testing_end_date = \"2025-10-31\"\n", "validation_start_date = str((pd.to_datetime(testing_start_date) - pd.DateOffset(months=1)).date())"]}, {"cell_type": "code", "execution_count": null, "id": "a62e7af2-c3b8-41af-9707-9c2f39be75b0", "metadata": {}, "outputs": [], "source": ["if len(ptypes[notebook_id]) > 0:\n", "    os.chdir(\"/tmp/data-science-models/projects/forecasting/dags\")\n", "\n", "    tic = time.time()\n", "\n", "    pb.send_slack_message(\n", "        channel=\"bl_forecasting_process_alerts\",\n", "        text=f\"STARTING GUARDRAILS\",\n", "    )\n", "\n", "    # testing_start_date = run_id.split(\"T\")[0].split(\"__\")[1]\n", "    # testing_end_date = str((pd.to_datetime(testing_start_date) + pd.DateOffset(months=3)).date())\n", "    # validation_start_date = str(\n", "    #     (pd.to_datetime(testing_start_date) - pd.DateOffset(months=1)).date()\n", "    # )\n", "\n", "    pm.execute_notebook(\n", "        input_path=\"./guardrails_final.ipynb\",\n", "        output_path=\"./guardrails_final_output.ipynb\",\n", "        parameters={\n", "            \"ptypes\": ptypes[notebook_id],\n", "            \"cities\": city_list,\n", "            \"module\": \"city_ptype_split\",\n", "            \"exp_id\": qty_exp_id,\n", "            \"training_start_date\": \"2023-01-01\",\n", "            \"validation_start_date\": validation_start_date,\n", "            \"testing_start_date\": testing_start_date,\n", "            \"testing_end_date\": testing_end_date,\n", "            \"s3_output_path\": f\"demand_forecasting/packaged/production_data/{folder_name}\",\n", "            \"notebook_id\": notebook_id,\n", "        },\n", "    )\n", "\n", "    toc = time.time()\n", "\n", "    pb.send_slack_message(\n", "        channel=\"bl_forecasting_process_alerts\",\n", "        text=f\"GUARDRAILS COMPLETED at {datetime.now()}. Time taken: {round((toc-tic)/60,2)} minutes\",\n", "    )"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}