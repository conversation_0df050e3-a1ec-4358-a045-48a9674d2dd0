alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: feature_store_data_backfill
dag_type: etl
escalation_priority: low
execution_timeout: 4320
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: very-high-mem
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: demand_forecasting
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RZ0R1WDU
path: demand_forecasting/fraud_prevention_service/etl/feature_store_data_backfill
paused: false
pool: demand_forecasting_pool
project_name: fraud_prevention_service
schedule:
  end_date: '2025-08-11T00:00:00'
  interval: 0 1 * * *
  start_date: '2025-06-05T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 3
