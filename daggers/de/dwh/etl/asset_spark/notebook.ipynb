{"cells": [{"cell_type": "code", "execution_count": null, "id": "61ca95e7-be36-4ff2-889d-033569ed385c", "metadata": {}, "outputs": [], "source": ["# %%configure_eks -f\n", "# {\n", "#     \"conf\": {\n", "#         \"spark.executor.memory\": \"25g\",\n", "#         \"spark.dynamicAllocation.maxExecutors\": \"20\"\n", "#     }\n", "# }"]}, {"cell_type": "code", "execution_count": null, "id": "0d9dc0f1-1152-4eeb-94e0-45eafc80314f", "metadata": {}, "outputs": [], "source": ["spark"]}, {"cell_type": "code", "execution_count": null, "id": "3dee8fe1-2450-4673-9280-a7fb84f29058", "metadata": {}, "outputs": [], "source": ["import zspark as zs"]}, {"cell_type": "code", "execution_count": null, "id": "7d5ca0ea-4e56-4d96-a22f-619a041d8486", "metadata": {}, "outputs": [], "source": ["from pyspark.sql import SparkSession\n", "from pyspark.sql.functions import col, lower, when, coalesce, lit"]}, {"cell_type": "code", "execution_count": null, "id": "9d6775f0-35c3-4674-b91f-92caa3352270", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "from dateutil import tz, parser"]}, {"cell_type": "code", "execution_count": null, "id": "2fc8f540-279d-45da-abaa-65a7e8a73cb3", "metadata": {}, "outputs": [], "source": ["# Define the run date\n", "run_date = datetime.today()\n", "run_date = (run_date - timedelta(days=1)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "5393d6c1-fa1f-4a44-90bb-9d0687641ea7", "metadata": {}, "outputs": [], "source": ["run_date"]}, {"cell_type": "code", "execution_count": null, "id": "d0faa9f6-af0b-462c-8b42-d5d83dc2cd08", "metadata": {}, "outputs": [], "source": ["from pyspark.sql import functions as F\n", "\n", "df = spark.table(\"lake_events.mobile_impression_data\")"]}, {"cell_type": "code", "execution_count": null, "id": "4e029bba-949d-4d2e-ac07-de5d9b4a35a6", "metadata": {}, "outputs": [], "source": ["# source similar for refined page subpage visit\n", "ss_refined_subpage_df = df.filter(\n", "    (\n", "        <PERSON>.col(\"name\").isin(\n", "            \"Product List Visit\",\n", "            \"landing Pages Visit\",\n", "            \"Grouped Products Bottom Sheet Visit\",\n", "            \"Product Page Visit\",\n", "            \"BOTTOM_SHEET_VISIT\",\n", "            \"Product Buy More Bottom Sheet Visit\",\n", "        )\n", "    )\n", "    & (F.col(\"at_date_ist\") == F.to_date(F.lit(run_date)))\n", "    & (~F.col(\"user_id\").like(\"%-%\"))\n", "    & (~<PERSON>.col(\"user_id\").isin(\"0\", \"-1\"))\n", "    & (<PERSON><PERSON>col(\"platform\").isin(\"android\", \"ios\"))\n", ").select(\n", "    \"at_date_ist\",\n", "    \"platform\",\n", "    F.col(\"device_uuid\").alias(\"device_uuid\"),\n", "    \"properties__page_visit_id\",\n", "    F.col(\"properties__entry_source_tracking_id\").alias(\"widget_title\"),\n", "    \"properties__last_page_visit_id\",\n", "    \"properties__last_page_name\",\n", "    \"properties__page_title\",\n", "    \"properties__last_page_title\",\n", "    \"name\",\n", "    \"properties__sub_page_visit_id\",\n", "    \"properties__page_name\",\n", "    \"properties__last_sub_page_visit_id\",\n", "    \"traits__user_id\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8da93b42-9e89-4c9c-a558-ff60d1cf5f5a", "metadata": {}, "outputs": [], "source": ["# ss_refined_subpage_df.show()"]}, {"cell_type": "code", "execution_count": null, "id": "96610f30-f572-4dff-9adc-c7ab70b2a916", "metadata": {}, "outputs": [], "source": ["# source similar for product shown\n", "ss_product_shown_df = df.filter(\n", "    (<PERSON><PERSON>col(\"name\") == \"Product Shown\")\n", "    & (F.col(\"at_date_ist\") == F.to_date(F.lit(run_date)))\n", "    & (<PERSON><PERSON>col(\"platform\").isin(\"android\", \"ios\"))\n", "    & (<PERSON>.col(\"device_uuid\").isNotNull())\n", "    & (<PERSON>.col(\"properties__product_id\").isNotNull())\n", ").select(\n", "    \"at_date_ist\",\n", "    \"platform\",\n", "    \"device_uuid\",\n", "    \"traits__city_name\",\n", "    \"traits__merchant_name\",\n", "    \"traits__merchant_id\",\n", "    \"session_uuid\",\n", "    \"properties__page_name\",\n", "    \"properties__page_title\",\n", "    \"traits__user_id\",\n", "    \"properties__page_type\",\n", "    \"properties__product_id\",\n", "    \"properties__page_visit_id\",\n", "    \"properties__sub_page_name\",\n", "    \"properties__sub_page_visit_id\",\n", "    \"properties__widget_tracking_id\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "65688c8b-6287-43a5-ac7e-a83be5c85aa7", "metadata": {}, "outputs": [], "source": ["# ss_product_shown_df.show()"]}, {"cell_type": "code", "execution_count": null, "id": "43d4bd1c-608b-4305-85e3-40f393c671e0", "metadata": {}, "outputs": [], "source": ["from pyspark import StorageLevel\n", "\n", "# persisting df data over memory\n", "# ss_product_shown_df.persist(StorageLevel.MEMORY_AND_DISK)\n", "\n", "# commenting it out to not persist it on memory, so that memory can be free"]}, {"cell_type": "code", "execution_count": null, "id": "27f2d131-8f19-4c75-b384-ef2d308c40e0", "metadata": {}, "outputs": [], "source": ["# --- page_visit_ids CTE ---\n", "page_visit_ids = (\n", "    ss_refined_subpage_df.filter(\n", "        (F.col(\"at_date_ist\") == F.to_date(F.lit(run_date)))\n", "        & (<PERSON>.col(\"properties__last_page_name\").isin(\"feed\", \"home_tabs\"))\n", "        & (<PERSON><PERSON>col(\"name\").isin(\"TAB_LAYOUT_PAGE_VISIT\", \"Product List Visit\", \"landing Pages Visit\"))\n", "    )\n", "    .select(\n", "        \"at_date_ist\",\n", "        \"platform\",\n", "        \"device_uuid\",\n", "        \"properties__page_visit_id\",\n", "        \"properties__page_title\",\n", "        <PERSON><PERSON>col(\"widget_title\"),\n", "        F.col(\"properties__last_page_title\").alias(\"source_tab\"),\n", "        \"traits__user_id\",\n", "    )\n", "    .dropDuplicates()\n", ")\n", "\n", "# --- indirect_page_visits CTE ---\n", "a = ss_refined_subpage_df.alias(\"a\")\n", "b = page_visit_ids.alias(\"b\")\n", "\n", "indirect_page_visits = (\n", "    a.join(\n", "        b,\n", "        on=F.col(\"a.properties__last_page_visit_id\") == F.col(\"b.properties__page_visit_id\"),\n", "        how=\"inner\",\n", "    )\n", "    .filter(F.col(\"a.at_date_ist\") == F.to_date(F.lit(run_date)))\n", "    .select(\n", "        <PERSON>.col(\"a.at_date_ist\"),\n", "        <PERSON><PERSON>col(\"a.platform\"),\n", "        <PERSON>.col(\"a.device_uuid\"),\n", "        <PERSON>.col(\"a.properties__page_visit_id\"),\n", "        <PERSON>.col(\"a.properties__page_title\"),\n", "        <PERSON><PERSON>col(\"b.widget_title\"),\n", "        <PERSON><PERSON>col(\"b.source_tab\"),\n", "        <PERSON>.col(\"b.traits__user_id\"),\n", "    )\n", "    .dropDuplicates()\n", ")\n", "\n", "# --- Final UNION ALL ---\n", "imd_app_daily_refined_visit = page_visit_ids.unionByName(indirect_page_visits)"]}, {"cell_type": "code", "execution_count": null, "id": "66e6a9ef-2afb-4e4a-b41a-03d9f60bd1b0", "metadata": {}, "outputs": [], "source": ["# imd_app_daily_refined_visit.show()"]}, {"cell_type": "code", "execution_count": null, "id": "294c3009-d274-4a02-a957-9b0a8a21e816", "metadata": {}, "outputs": [], "source": ["# --- Create direct_sub_page_visits CTE ---\n", "direct_sub_page_visits = (\n", "    ss_refined_subpage_df.filter(\n", "        (F.col(\"at_date_ist\") == F.to_date(F.lit(run_date)))\n", "        & (<PERSON>.col(\"properties__page_name\").isin(\"feed\", \"home_tabs\"))\n", "        & (\n", "            <PERSON>.col(\"name\").isin(\n", "                \"Product Page Visit\",\n", "                \"BOTTOM_SHEET_VISIT\",\n", "                \"Product Buy More Bottom Sheet Visit\",\n", "                \"Grouped Products Bottom Sheet Visit\",\n", "            )\n", "        )\n", "    )\n", "    .select(\n", "        \"at_date_ist\",\n", "        \"platform\",\n", "        \"device_uuid\",\n", "        \"traits__user_id\",\n", "        \"properties__sub_page_visit_id\",\n", "        F.col(\"properties__page_title\").alias(\"source_tab\"),\n", "        \"widget_title\",\n", "        \"name\",\n", "        \"properties__last_sub_page_visit_id\",\n", "    )\n", "    .dropDuplicates()\n", ")\n", "\n", "a = ss_refined_subpage_df.alias(\"a\")\n", "b = direct_sub_page_visits.alias(\"b\")\n", "\n", "joined_df = (\n", "    a.join(\n", "        b,\n", "        on=[\n", "            F.col(\"a.at_date_ist\") == F.col(\"b.at_date_ist\"),\n", "            F.col(\"a.device_uuid\") == F.col(\"b.device_uuid\"),\n", "            F.col(\"a.properties__last_sub_page_visit_id\")\n", "            == F.col(\"b.properties__sub_page_visit_id\"),\n", "            F.col(\"b.name\") == \"BOTTOM_SHEET_VISIT\",\n", "        ],\n", "        how=\"inner\",\n", "    )\n", "    .filter(\n", "        (F.col(\"a.at_date_ist\") == F.to_date(F.lit(run_date)))\n", "        & (<PERSON><PERSON>col(\"a.name\").isin(\"Product Page Visit\", \"Product Buy More Bottom Sheet Visit\"))\n", "        & (F.col(\"a.properties__page_name\").isin(\"feed\", \"home_tabs\"))\n", "    )\n", "    .select(\n", "        <PERSON>.col(\"a.at_date_ist\"),\n", "        <PERSON><PERSON>col(\"a.platform\"),\n", "        <PERSON>.col(\"a.device_uuid\"),\n", "        <PERSON>.col(\"b.traits__user_id\"),\n", "        <PERSON>.col(\"a.properties__sub_page_visit_id\"),\n", "        <PERSON><PERSON>col(\"b.source_tab\"),\n", "        <PERSON><PERSON>col(\"b.widget_title\"),\n", "        <PERSON><PERSON>col(\"b.name\"),\n", "    )\n", "    .dropDuplicates()\n", ")\n", "\n", "# --- Second SELECT (direct_sub_page_visits with null/empty checks) ---\n", "filtered_direct_visits = (\n", "    direct_sub_page_visits.filter(\n", "        (<PERSON>.col(\"properties__last_sub_page_visit_id\").isNull())\n", "        | (<PERSON>.col(\"properties__last_sub_page_visit_id\") == \"\")\n", "        | (<PERSON>.col(\"properties__last_sub_page_visit_id\") == \"#-NA\")\n", "    )\n", "    .select(\n", "        \"at_date_ist\",\n", "        \"platform\",\n", "        \"device_uuid\",\n", "        \"traits__user_id\",\n", "        \"properties__sub_page_visit_id\",\n", "        \"source_tab\",\n", "        \"widget_title\",\n", "        \"name\",\n", "    )\n", "    .dropDuplicates()\n", ")\n", "\n", "# --- UNION ALL both parts ---\n", "imd_app_daily_subpage_visit = joined_df.unionByName(filtered_direct_visits)"]}, {"cell_type": "code", "execution_count": null, "id": "ff03e8c8-1ae0-48e8-94ea-c4e01885ef44", "metadata": {}, "outputs": [], "source": ["# imd_app_daily_subpage_visit.show()"]}, {"cell_type": "code", "execution_count": null, "id": "01dc077b-0fd0-45e9-876a-ab71774ddd0b", "metadata": {}, "outputs": [], "source": ["subpage_df = imd_app_daily_subpage_visit\n", "visit_df = imd_app_daily_refined_visit\n", "shown_df = ss_product_shown_df\n", "product_df = spark.read.table(\"blinkit_iceberg.dwh.dim_product\")"]}, {"cell_type": "code", "execution_count": null, "id": "c27ec5cd-a104-4453-a3c5-473d47496b49", "metadata": {}, "outputs": [], "source": ["# Filter for the relevant date and platform\n", "shown_df = shown_df.filter(\n", "    (col(\"platform\") == \"android\") & (col(\"at_date_ist\") == F.to_date(F.lit(run_date)))\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1b509a2b-c91b-4d40-8e61-2b205e76f39e", "metadata": {}, "outputs": [], "source": ["# shown_df.show()"]}, {"cell_type": "code", "execution_count": null, "id": "0dd1ef3a-eee0-42be-95a2-99b8d68596d1", "metadata": {}, "outputs": [], "source": ["# Perform Joins\n", "shown_df = (\n", "    shown_df.alias(\"i\")\n", "    .join(\n", "        visit_df.alias(\"v\"),\n", "        (col(\"v.device_uuid\") == col(\"i.device_uuid\"))\n", "        & (col(\"v.at_date_ist\") == col(\"i.at_date_ist\"))\n", "        & (col(\"v.properties__page_visit_id\") == col(\"i.properties__page_visit_id\")),\n", "        \"left\",\n", "    )\n", "    .join(\n", "        subpage_df.alias(\"s\"),\n", "        (col(\"s.device_uuid\") == col(\"i.device_uuid\"))\n", "        & (col(\"s.at_date_ist\") == col(\"i.at_date_ist\"))\n", "        & (col(\"s.properties__sub_page_visit_id\") == col(\"i.properties__sub_page_visit_id\")),\n", "        \"left\",\n", "    )\n", "    .join(\n", "        product_df.alias(\"p\"),\n", "        (col(\"p.product_id\").cast(\"string\") == col(\"i.properties__product_id\"))\n", "        & (col(\"p.l0_category_id\") != 343)\n", "        & (col(\"p.is_current\") == True),\n", "        \"inner\",\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2d394583-38a0-4e64-b572-162da90cc3bc", "metadata": {}, "outputs": [], "source": ["mapped_df = shown_df.select(\n", "    lower(col(\"i.traits__city_name\")).alias(\"city\"),\n", "    lower(col(\"i.traits__merchant_name\")).alias(\"merchant\"),\n", "    when(\n", "        (col(\"v.properties__page_visit_id\") == col(\"i.properties__page_visit_id\"))\n", "        & (~col(\"i.properties__page_name\").isin(\"empty_search\", \"search\", \"feed\")),\n", "        col(\"v.widget_title\"),\n", "    ).alias(\"visit_widget_title\"),\n", "    when(\n", "        (col(\"v.properties__page_visit_id\") == col(\"i.properties__page_visit_id\"))\n", "        & (~col(\"i.properties__page_name\").isin(\"empty_search\", \"search\", \"feed\")),\n", "        col(\"v.source_tab\"),\n", "    ).alias(\"visit_source_tab\"),\n", "    when(\n", "        (col(\"i.properties__page_name\").isin(\"feed\", \"home_tabs\", \"empty_search\"))\n", "        & (\n", "            (col(\"i.properties__sub_page_name\") != \"product_page\")\n", "            | col(\"i.properties__sub_page_name\").isNull()\n", "        ),\n", "        lit(1),\n", "    ).alias(\"feed_and_tab_flag\"),\n", "    when(\n", "        col(\"s.properties__sub_page_visit_id\") == col(\"i.properties__sub_page_visit_id\"),\n", "        col(\"s.widget_title\"),\n", "    ).alias(\"subpage_widget_title\"),\n", "    when(\n", "        col(\"s.properties__sub_page_visit_id\") == col(\"i.properties__sub_page_visit_id\"),\n", "        col(\"s.source_tab\"),\n", "    ).alias(\"subpage_source_tab\"),\n", "    when(\n", "        (col(\"v.widget_title\").like(\"%pt\\_%\"))\n", "        | (col(\"v.widget_title\").like(\"frequently_bought\\_%\"))\n", "        | (col(\"v.widget_title\").like(\"previously_bought\\_%\"))\n", "        | (\n", "            col(\"v.widget_title\").isin(\n", "                \"your_collections\",\n", "                \"your_collections_v1\",\n", "                \"order_again_repeat_user\",\n", "                \"more_that_you_ordered1\",\n", "                \"previously_bought\",\n", "                \"quick_buys\",\n", "            )\n", "        ),\n", "        lit(1),\n", "    )\n", "    .otherwise(lit(0))\n", "    .alias(\"flag_1\"),\n", "    when(\n", "        (col(\"i.properties__widget_tracking_id\").like(\"%pt\\_%\"))\n", "        | (col(\"i.properties__widget_tracking_id\").like(\"frequently_bought\\_%\"))\n", "        | (col(\"i.properties__widget_tracking_id\").like(\"previously_bought\\_%\"))\n", "        | (\n", "            col(\"i.properties__widget_tracking_id\").isin(\n", "                \"your_collections\",\n", "                \"your_collections_v1\",\n", "                \"order_again_repeat_user\",\n", "                \"more_that_you_ordered1\",\n", "                \"previously_bought\",\n", "                \"quick_buys\",\n", "            )\n", "        ),\n", "        lit(1),\n", "    )\n", "    .otherwise(lit(0))\n", "    .alias(\"flag_2\"),\n", "    when(\n", "        (col(\"s.widget_title\").like(\"%pt\\_%\"))\n", "        | (col(\"s.widget_title\").like(\"frequently_bought\\_%\"))\n", "        | (col(\"s.widget_title\").like(\"previously_bought\\_%\"))\n", "        | (\n", "            col(\"s.widget_title\").isin(\n", "                \"your_collections\",\n", "                \"your_collections_v1\",\n", "                \"order_again_repeat_user\",\n", "                \"more_that_you_ordered1\",\n", "                \"previously_bought\",\n", "                \"quick_buys\",\n", "            )\n", "        ),\n", "        lit(1),\n", "    )\n", "    .otherwise(lit(0))\n", "    .alias(\"flag_3\"),\n", "    lit(1).alias(\"flag_4\"),\n", "    lower(col(\"p.l0_category\")).alias(\"l0_category\"),\n", "    lower(col(\"p.l1_category\")).alias(\"l1_category\"),\n", "    lower(col(\"p.product_type\")).alias(\"product_type\"),\n", "    col(\"i.at_date_ist\"),\n", "    col(\"i.platform\"),\n", "    col(\"i.device_uuid\"),\n", "    col(\"i.session_uuid\"),\n", "    col(\"i.properties__widget_tracking_id\"),\n", "    col(\"i.properties__page_name\"),\n", "    col(\"i.properties__page_title\").alias(\"source_tab\"),\n", "    col(\"i.traits__user_id\"),\n", "    col(\"i.properties__page_type\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "71c3a402-8276-4ac6-8bc3-158dba291f8f", "metadata": {}, "outputs": [], "source": ["# Add Asset Grouping Logic\n", "mapped_df = (\n", "    mapped_df.withColumn(\n", "        \"visit_asset_grouping\",\n", "        when(\n", "            col(\"flag_1\") == 1,\n", "            when(col(\"visit_widget_title\").like(\"%pt\\\\_%\"), \"PT\")\n", "            .when(\n", "                (\n", "                    col(\"visit_widget_title\").isin(\n", "                        \"your_collections\",\n", "                        \"your_collections_v1\",\n", "                        \"order_again_repeat_user\",\n", "                        \"more_that_you_ordered1\",\n", "                        \"previously_bought\",\n", "                        \"quick_buys\",\n", "                    )\n", "                )\n", "                | (col(\"visit_widget_title\").like(\"frequently\\\\_bought\\\\_%\"))\n", "                | (col(\"visit_widget_title\").like(\"%previously\\\\_bought\\\\_%\")),\n", "                \"OA Widget\",\n", "            )\n", "            .when(\n", "                (~col(\"visit_source_tab\").isin(\"feed_default\", \"feed_\", \"_default\"))\n", "                & col(\"visit_source_tab\").rlike(r\".*feed_.*\")\n", "                & col(\"properties__page_name\").isin(\"feed\", \"home_tabs\"),\n", "                \"Tabs\",\n", "            )\n", "            .when(\n", "                (col(\"properties__page_name\") == \"listing_widgets\")\n", "                & (col(\"properties__page_type\") == \"Category\"),\n", "                \"Category grid PLP\",\n", "            )\n", "            .when(\n", "                (col(\"properties__page_name\").isin(\"Product List\", \"Collection\", \"listing_widgets\"))\n", "                & (col(\"properties__page_type\") != \"Category\"),\n", "                \"Other Merchandising\",\n", "            )\n", "            .when(\n", "                col(\"properties__page_name\").isin(\n", "                    \"Landing Pages\", \"layout_page\", \"tab_layout_page\", \"tabs\"\n", "                ),\n", "                \"Other Merchandising\",\n", "            )\n", "            .when(\n", "                col(\"properties__page_name\").isin(\"Search Page\", \"search\", \"empty_search\"),\n", "                \"Search Page\",\n", "            )\n", "            .when(col(\"properties__page_name\").isin(\"homepage\", \"feed\"), \"ROF\")\n", "            .when(\n", "                (col(\"properties__page_name\").isin(\"home_tabs\", \"order_again\"))\n", "                & col(\"visit_source_tab\").isin(\"Order Again\", \"Order Again Header Collections\"),\n", "                \"Order Again tab\",\n", "            )\n", "            .when(\n", "                (col(\"properties__page_name\") == \"Cart\") & (col(\"visit_source_tab\") == \"Cart\"),\n", "                \"Cart Page\",\n", "            )\n", "            .otherwise(\"Others\"),\n", "        ),\n", "    )\n", "    .withColumn(\n", "        \"feed_tab_asset_grouping\",\n", "        when(\n", "            (col(\"feed_and_tab_flag\") == 1) & (col(\"flag_2\") == 1),\n", "            when(col(\"properties__widget_tracking_id\").like(\"%pt\\\\_%\"), \"PT\")\n", "            .when(\n", "                (\n", "                    col(\"properties__widget_tracking_id\").isin(\n", "                        \"your_collections\",\n", "                        \"your_collections_v1\",\n", "                        \"order_again_repeat_user\",\n", "                        \"more_that_you_ordered1\",\n", "                        \"previously_bought\",\n", "                        \"quick_buys\",\n", "                    )\n", "                )\n", "                | (col(\"visit_widget_title\").like(\"frequently\\\\_bought\\\\_%\"))\n", "                | (col(\"visit_widget_title\").like(\"%previously\\\\_bought\\\\_%\")),\n", "                \"OA Widget\",\n", "            )\n", "            .when(\n", "                (~col(\"source_tab\").isin(\"feed_default\", \"feed_\", \"_default\"))\n", "                & col(\"source_tab\").rlike(r\".*feed_.*\")\n", "                & col(\"properties__page_name\").isin(\"feed\", \"home_tabs\"),\n", "                \"Tabs\",\n", "            )\n", "            .when(\n", "                (col(\"properties__page_name\") == \"listing_widgets\")\n", "                & (col(\"properties__page_type\") == \"Category\"),\n", "                \"Category grid PLP\",\n", "            )\n", "            .when(\n", "                (col(\"properties__page_name\").isin(\"Product List\", \"Collection\", \"listing_widgets\"))\n", "                & (col(\"properties__page_type\") != \"Category\"),\n", "                \"Other Merchandising\",\n", "            )\n", "            .when(\n", "                col(\"properties__page_name\").isin(\n", "                    \"Landing Pages\", \"layout_page\", \"tab_layout_page\", \"tabs\"\n", "                ),\n", "                \"Other Merchandising\",\n", "            )\n", "            .when(\n", "                col(\"properties__page_name\").isin(\"Search Page\", \"search\", \"empty_search\"),\n", "                \"Search Page\",\n", "            )\n", "            .when(col(\"properties__page_name\").isin(\"homepage\", \"feed\"), \"ROF\")\n", "            .when(\n", "                (col(\"properties__page_name\").isin(\"home_tabs\", \"order_again\"))\n", "                & col(\"source_tab\").isin(\"Order Again\", \"Order Again Header Collections\"),\n", "                \"Order Again tab\",\n", "            )\n", "            .when(\n", "                (col(\"properties__page_name\") == \"Cart\") & (col(\"source_tab\") == \"Cart\"),\n", "                \"Cart Page\",\n", "            )\n", "            .otherwise(\"Others\"),\n", "        ),\n", "    )\n", "    .withColumn(\n", "        \"pdp_asset_grouping\",\n", "        when(\n", "            (col(\"flag_3\") == 1),\n", "            when(col(\"subpage_widget_title\").like(\"%pt\\\\_%\"), \"PT\")\n", "            .when(\n", "                (\n", "                    col(\"subpage_widget_title\").isin(\n", "                        \"your_collections\",\n", "                        \"your_collections_v1\",\n", "                        \"order_again_repeat_user\",\n", "                        \"more_that_you_ordered1\",\n", "                        \"previously_bought\",\n", "                        \"quick_buys\",\n", "                    )\n", "                )\n", "                | (col(\"visit_widget_title\").like(\"frequently\\\\_bought\\\\_%\"))\n", "                | (col(\"visit_widget_title\").like(\"%previously\\\\_bought\\\\_%\")),\n", "                \"OA Widget\",\n", "            )\n", "            .when(\n", "                (~col(\"subpage_source_tab\").isin(\"feed_default\", \"feed_\", \"_default\"))\n", "                & col(\"subpage_source_tab\").rlike(r\".*feed_.*\")\n", "                & col(\"properties__page_name\").isin(\"feed\", \"home_tabs\"),\n", "                \"Tabs\",\n", "            )\n", "            .when(\n", "                (col(\"properties__page_name\") == \"listing_widgets\")\n", "                & (col(\"properties__page_type\") == \"Category\"),\n", "                \"Category grid PLP\",\n", "            )\n", "            .when(\n", "                (col(\"properties__page_name\").isin(\"Product List\", \"Collection\", \"listing_widgets\"))\n", "                & (col(\"properties__page_type\") != \"Category\"),\n", "                \"Other Merchandising\",\n", "            )\n", "            .when(\n", "                col(\"properties__page_name\").isin(\n", "                    \"Landing Pages\", \"layout_page\", \"tab_layout_page\", \"tabs\"\n", "                ),\n", "                \"Other Merchandising\",\n", "            )\n", "            .when(\n", "                col(\"properties__page_name\").isin(\"Search Page\", \"search\", \"empty_search\"),\n", "                \"Search Page\",\n", "            )\n", "            .when(col(\"properties__page_name\").isin(\"homepage\", \"feed\"), \"ROF\")\n", "            .when(\n", "                (col(\"properties__page_name\").isin(\"home_tabs\", \"order_again\"))\n", "                & col(\"subpage_source_tab\").isin(\"Order Again\", \"Order Again Header Collections\"),\n", "                \"Order Again tab\",\n", "            )\n", "            .when(\n", "                (col(\"properties__page_name\") == \"Cart\") & (col(\"subpage_source_tab\") == \"Cart\"),\n", "                \"Cart Page\",\n", "            )\n", "            .otherwise(\"Others\"),\n", "        ),\n", "    )\n", "    .withColumn(\n", "        \"all_other_asset_grouping\",\n", "        when(\n", "            (col(\"flag_1\") == 0)\n", "            & (col(\"flag_2\") == 0)\n", "            & (col(\"flag_3\") == 0)\n", "            & (col(\"flag_4\") == 1),\n", "            when(col(\"properties__widget_tracking_id\").like(\"%pt\\\\_%\"), \"PT\")\n", "            .when(\n", "                (\n", "                    col(\"properties__widget_tracking_id\").isin(\n", "                        \"your_collections\",\n", "                        \"your_collections_v1\",\n", "                        \"order_again_repeat_user\",\n", "                        \"more_that_you_ordered1\",\n", "                        \"previously_bought\",\n", "                        \"quick_buys\",\n", "                    )\n", "                )\n", "                | (col(\"visit_widget_title\").like(\"frequently\\\\_bought\\\\_%\"))\n", "                | (col(\"visit_widget_title\").like(\"%previously\\\\_bought\\\\_%\")),\n", "                \"OA Widget\",\n", "            )\n", "            .when(\n", "                (~col(\"source_tab\").isin(\"feed_default\", \"feed_\", \"_default\"))\n", "                & col(\"source_tab\").rlike(r\".*feed_.*\")\n", "                & col(\"properties__page_name\").isin(\"feed\", \"home_tabs\"),\n", "                \"Tabs\",\n", "            )\n", "            .when(\n", "                (col(\"properties__page_name\") == \"listing_widgets\")\n", "                & (col(\"properties__page_type\") == \"Category\"),\n", "                \"Category grid PLP\",\n", "            )\n", "            .when(\n", "                (col(\"properties__page_name\").isin(\"Product List\", \"Collection\", \"listing_widgets\"))\n", "                & (col(\"properties__page_type\") != \"Category\"),\n", "                \"Other Merchandising\",\n", "            )\n", "            .when(\n", "                col(\"properties__page_name\").isin(\n", "                    \"Landing Pages\", \"layout_page\", \"tab_layout_page\", \"tabs\"\n", "                ),\n", "                \"Other Merchandising\",\n", "            )\n", "            .when(\n", "                col(\"properties__page_name\").isin(\"Search Page\", \"search\", \"empty_search\"),\n", "                \"Search Page\",\n", "            )\n", "            .when(col(\"properties__page_name\").isin(\"homepage\", \"feed\"), \"ROF\")\n", "            .when(\n", "                (col(\"properties__page_name\").isin(\"home_tabs\", \"order_again\"))\n", "                & col(\"source_tab\").isin(\"Order Again\", \"Order Again Header Collections\"),\n", "                \"Order Again tab\",\n", "            )\n", "            .when(\n", "                (col(\"properties__page_name\") == \"Cart\") & (col(\"source_tab\") == \"Cart\"),\n", "                \"Cart Page\",\n", "            )\n", "            .otherwise(\"Others\"),\n", "        ),\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "927c5c08-847e-4d2f-bf5d-5db7c38b0457", "metadata": {}, "outputs": [], "source": ["# Asset Grouping\n", "asset_df = mapped_df.withColumn(\n", "    \"asset_grouping\",\n", "    coalesce(\n", "        col(\"visit_asset_grouping\"),\n", "        col(\"feed_tab_asset_grouping\"),\n", "        col(\"pdp_asset_grouping\"),\n", "        col(\"all_other_asset_grouping\"),\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b795973f-2b83-4c5c-9b9e-65ba8937c04d", "metadata": {}, "outputs": [], "source": ["# Page Grouping\n", "asset_df = asset_df.withColumn(\n", "    \"page_grouping\",\n", "    when(col(\"asset_grouping\").isin(\"PT\", \"Other Merchandising\"), \"Merchandising\")\n", "    .when(col(\"asset_grouping\").isin(\"OA_widget\", \"Tabs\", \"Order Again tab\", \"ROF\"), \"Homepage\")\n", "    .otherwise(col(\"asset_grouping\")),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6ca89781-c5fa-4254-a6b7-b37fca781cf9", "metadata": {}, "outputs": [], "source": ["# Filter NULL values\n", "final_df = asset_df.filter(\n", "    (col(\"asset_grouping\").isNotNull())\n", "    & (col(\"city\").isNotNull())\n", "    & (col(\"merchant\").isNotNull())\n", "    & (col(\"product_type\").isNotNull())\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "41ba1e28-e02f-4395-84f2-4afff7dd882b", "metadata": {}, "outputs": [], "source": ["# Select final columns\n", "final_df = final_df.select(\n", "    \"i.at_date_ist\",\n", "    \"i.platform\",\n", "    \"i.device_uuid\",\n", "    \"city\",\n", "    \"merchant\",\n", "    \"session_uuid\",\n", "    \"traits__user_id\",\n", "    \"l0_category\",\n", "    \"l1_category\",\n", "    col(\"product_type\").alias(\"ptype\"),\n", "    \"asset_grouping\",\n", "    \"page_grouping\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "489e81a2-acb6-4121-8b9e-8840e46f3c1c", "metadata": {}, "outputs": [], "source": ["# Show results (Optional)\n", "# final_df.show()"]}, {"cell_type": "code", "execution_count": null, "id": "7c34bd62-b7b9-46a8-871d-d2c6fa706cc1", "metadata": {}, "outputs": [], "source": ["# repartioning for\n", "final_df = final_df.repartition(\"merchant\")"]}, {"cell_type": "code", "execution_count": null, "id": "7017e68b-253c-414a-a46d-f6775ccfccbb", "metadata": {}, "outputs": [], "source": ["from pyspark import StorageLevel\n", "\n", "# persisting df data over memory\n", "final_df.persist(StorageLevel.MEMORY_AND_DISK)"]}, {"cell_type": "code", "execution_count": null, "id": "0c7f088c-a727-4d19-aa5e-689035583b82", "metadata": {}, "outputs": [], "source": ["# final_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "1617e591-72ea-42d7-b746-2b2960060f3b", "metadata": {}, "outputs": [], "source": ["df_kwargs = {\n", "    \"tenant\": \"blinkit\",\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"impression_sample_raw_table\",\n", "    \"load_type\": \"partition_overwrite\",  # append, partition_overwrite or upsert,\n", "    \"apply_limit\": \"false\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "2edfdd4e-89b7-41e4-95c9-b16f8d01b70a", "metadata": {}, "outputs": [], "source": ["zs.to_table(spark, final_df, **df_kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "00d9a142-35c5-4a3e-840b-167a94edbd1f", "metadata": {}, "outputs": [], "source": ["from pyspark.sql.functions import col, countDistinct, lit"]}, {"cell_type": "code", "execution_count": null, "id": "2b4c0a91-06e8-4348-a8ce-b421d877fc63", "metadata": {}, "outputs": [], "source": ["# Aggregation without \"Overall\"\n", "agg_df = final_df.groupBy(\n", "    \"at_date_ist\", \"platform\", \"city\", \"merchant\", \"page_grouping\", \"asset_grouping\", \"ptype\"\n", ").agg(\n", "    countDistinct(\"session_uuid\").alias(\"sessions\"),\n", "    countDistinct(\"traits__user_id\").alias(\"dau_product_impressions\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "770027e0-af70-4770-b234-0f4297c84077", "metadata": {}, "outputs": [], "source": ["df_kwargs = {\n", "    \"tenant\": \"blinkit\",\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"impression_sample_base_agg\",\n", "    \"partition_key\": [\"at_date_ist\"],\n", "    \"primary_key\": [\n", "        \"at_date_ist\",\n", "        \"platform\",\n", "        \"city\",\n", "        \"merchant\",\n", "        \"page_grouping\",\n", "        \"asset_grouping\",\n", "    ],\n", "    \"load_type\": \"partition_overwrite\",  # append, partition_overwrite or upsert,\n", "    \"apply_limit\": \"false\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "ee63a983-5c83-4c5a-b083-1d6b2c201e5d", "metadata": {}, "outputs": [], "source": ["zs.to_table(spark, agg_df, **df_kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "3395009b-09b3-4043-a007-acb6ec30947b", "metadata": {}, "outputs": [], "source": ["# Aggregation with 'Overall' for merchant\n", "agg_merchant_overall_df = (\n", "    final_df.groupBy(\"at_date_ist\", \"platform\", \"city\", \"page_grouping\", \"asset_grouping\", \"ptype\")\n", "    .agg(\n", "        countDistinct(\"session_uuid\").alias(\"sessions\"),\n", "        countDistinct(\"traits__user_id\").alias(\"dau_product_impressions\"),\n", "    )\n", "    .withColumn(\"merchant\", lit(\"Overall\"))\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "12ecee24-1e04-4524-a02b-3906c3361a16", "metadata": {}, "outputs": [], "source": ["df_kwargs = {\n", "    \"tenant\": \"blinkit\",\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"impression_sample_table_merchant_agg\",\n", "    \"partition_key\": [\"at_date_ist\"],\n", "    \"primary_key\": [\n", "        \"at_date_ist\",\n", "        \"platform\",\n", "        \"city\",\n", "        \"merchant\",\n", "        \"page_grouping\",\n", "        \"asset_grouping\",\n", "    ],\n", "    \"load_type\": \"partition_overwrite\",  # append, partition_overwrite or upsert,\n", "    \"apply_limit\": \"false\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "b20ab6c1-45d3-4414-83d3-5386139cb73f", "metadata": {}, "outputs": [], "source": ["zs.to_table(spark, agg_merchant_overall_df, **df_kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "78bd01ec-5ea8-45cd-89cc-9b88faf2b5bf", "metadata": {}, "outputs": [], "source": ["# Aggregation with 'Overall' for city and merchant\n", "agg_city_merchant_overall_df = (\n", "    final_df.groupBy(\"at_date_ist\", \"platform\", \"page_grouping\", \"asset_grouping\", \"ptype\")\n", "    .agg(\n", "        countDistinct(\"session_uuid\").alias(\"sessions\"),\n", "        countDistinct(\"traits__user_id\").alias(\"dau_product_impressions\"),\n", "    )\n", "    .withColumn(\"city\", lit(\"Overall\"))\n", "    .withColumn(\"merchant\", lit(\"Overall\"))\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "29e9f265-f6ae-4af1-a5fc-4c9ff26431ba", "metadata": {}, "outputs": [], "source": ["df_kwargs = {\n", "    \"tenant\": \"blinkit\",\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"impression_sample_table_city_agg\",\n", "    \"partition_key\": [\"at_date_ist\"],\n", "    \"primary_key\": [\n", "        \"at_date_ist\",\n", "        \"platform\",\n", "        \"city\",\n", "        \"merchant\",\n", "        \"page_grouping\",\n", "        \"asset_grouping\",\n", "    ],\n", "    \"load_type\": \"partition_overwrite\",  # append, partition_overwrite or upsert,\n", "    \"apply_limit\": \"false\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "c9aa3c89-bc19-4747-94f3-f97d4ea04bf2", "metadata": {}, "outputs": [], "source": ["zs.to_table(spark, agg_city_merchant_overall_df, **df_kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "1d10c442-f677-45f0-a361-b0d7aca70729", "metadata": {}, "outputs": [], "source": ["# # Union all the results\n", "# final_union_df = agg_df.unionByName(agg_merchant_overall_df).unionByName(\n", "#     agg_city_merchant_overall_df\n", "# )\n", "\n", "# union need to be take care separately"]}, {"cell_type": "code", "execution_count": null, "id": "2696364d-c839-4e41-a82b-d7174af59139", "metadata": {}, "outputs": [], "source": ["# final_union_df.show()\n", "# final_union_df = final_union_df.coalesce(10)\n", "\n", "# No need to use of coalesce"]}, {"cell_type": "code", "execution_count": null, "id": "e9a98999-84df-46ef-a88d-39eb69cffe6e", "metadata": {}, "outputs": [], "source": ["# df_kwargs = {\n", "#     \"tenant\": \"blinkit\",\n", "#     \"schema_name\": \"interim\",\n", "#     \"table_name\": \"impression_sample_table_df_new\",\n", "#     \"primary_key\": [\n", "#         \"at_date_ist\",\n", "#         \"platform\",\n", "#         \"city\",\n", "#         \"merchant\",\n", "#         \"page_grouping\",\n", "#         \"asset_grouping\",\n", "#     ],\n", "#     \"load_type\": \"partition_overwrite\",  # append, partition_overwrite or upsert,\n", "#     \"apply_limit\": \"false\",\n", "# }"]}, {"cell_type": "code", "execution_count": null, "id": "9384970a-b9d6-4614-b8d3-7cc451ace6c6", "metadata": {}, "outputs": [], "source": ["# zs.to_table(spark, final_union_df, **df_kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "6758e405-aec8-45d1-b050-4b5c2054d7e7", "metadata": {}, "outputs": [], "source": ["spark.stop()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "python", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "pygments_lexer": "python3"}}, "nbformat": 4, "nbformat_minor": 5}