alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: asset_spark
dag_type: etl
escalation_priority: low
execution_timeout: 840
namespace: de
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03SHUUAUF3
path: de/dwh/etl/asset_spark
paused: false
pool: default_spark_pool
project_name: dwh
schedule:
  end_date: '2025-06-05T00:00:00'
  interval: 0 1 * * *
  start_date: '2025-06-01T00:00:00'
schedule_type: fixed
sla: 126 minutes
spark_props:
  load_type: high
  node_type: spot
  spark_conf:
    spark.kubernetes.memoryOverheadFactor: "0.1"
support_files: []
tags: []
template_name: spark_notebook
version: 1
