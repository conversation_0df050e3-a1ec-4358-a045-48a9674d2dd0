alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: store_referral_mailer
dag_type: workflow
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-common-pii-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: de
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U0758LAP6DQ
path: de/misc/workflow/store_referral_mailer
paused: false
pool: de_pool
project_name: misc
schedule:
  end_date: '2025-09-15T00:00:00'
  interval: 30 5 * * *
  start_date: '2025-05-09T00:00:00'
schedule_type: fixed
sla: 122 minutes
support_files: []
tags: []
template_name: notebook
version: 9
