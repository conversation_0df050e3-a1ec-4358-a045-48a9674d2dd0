{"cells": [{"cell_type": "code", "execution_count": null, "id": "0eca1975-9c1e-4cf6-91ab-70680704e19a", "metadata": {}, "outputs": [], "source": ["# Installs\n", "!pip install pycryptodome"]}, {"cell_type": "code", "execution_count": null, "id": "cde8c95b-a020-451e-bea3-5d80a90dc27c", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import pencilbox as pb\n", "import smtplib\n", "from email.mime.multipart import MIMEMultipart\n", "from email.mime.text import MIMEText\n", "from datetime import datetime, timedelta\n", "import os\n", "\n", "from Crypto.Cipher import AES\n", "from Crypto.Util.Padding import unpad\n", "import base64"]}, {"cell_type": "code", "execution_count": null, "id": "9fe9d191-8b24-495f-add4-104d5b774b2d", "metadata": {}, "outputs": [], "source": ["os.environ[\"VAULT_SERVICE_TOKEN\"] = \"\"\n", "os.environ[\"VAULT_TOKEN\"] = \"\"\n", "os.environ[\"VAULT_K8S_AUTH_ROLE\"] = \"dse-pii-read-only\"\n", "os.environ[\"VAULT_K8S_AUTH_MOUNT\"] = \"eks\""]}, {"cell_type": "code", "execution_count": null, "id": "736df600-5f6c-41f9-81e2-157a5789ae73", "metadata": {}, "outputs": [], "source": ["## Change path before merge\n", "DECRYPTION_KEY = pb.get_secret(\"data/trino/zomato/pii_user/encryption-key/ops_management\")[\n", "    \"secret_key\"\n", "]\n", "\n", "trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "7df6fa36-463b-483d-bf32-21135b46c079", "metadata": {}, "outputs": [], "source": ["def decrypt_dataframe(df: pd.DataFrame, column_names: list, secret_key: str) -> pd.DataFrame:\n", "    def decrypt(encrypted_value: str) -> str:\n", "        key = secret_key.encode(\"utf-8\")\n", "        cipher = AES.new(key, AES.MODE_ECB)\n", "        decoded = base64.b64decode(encrypted_value)\n", "        decrypted = unpad(cipher.decrypt(decoded), AES.block_size)\n", "        return decrypted.decode(\"utf-8\")\n", "\n", "    # Create a copy of the DataFrame to avoid modifying the original\n", "    decrypted_df = df.copy()\n", "\n", "    # Decrypt specified columns\n", "    for column in column_names:\n", "        if column in decrypted_df.columns:\n", "            decrypted_df[column] = decrypted_df[column].apply(lambda x: decrypt(x) if x else x)\n", "        else:\n", "            print(f\"Warning: Column '{column}' not found in the DataFrame\")\n", "\n", "    return decrypted_df"]}, {"cell_type": "code", "execution_count": null, "id": "a5cc1490-0968-434a-b27a-d4ddf3e15bf5", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "\n", "with hiring_gap as \n", "(select \n", "date_ts,\n", "outlet_id,\n", "max_by(gap,updated_timestamp) as gap\n", "from interim.od_to_ft_hiring_gap_logs\n", "where date_ts = current_date - interval '1' day\n", "group by 1,2\n", ")\n", ",\n", "\n", "base as \n", "(select distinct a.employee_id, email_id,name,phone, designation,outlet_ids\n", "from storeops_etls.user_access_mapping a\n", "left join (\n", "  select employee_id, max_by(phone, update_ts) as phone\n", "  from ops_management.user\n", "  group by 1\n", ") b\n", "on a.employee_id = b.employee_id\n", "where designation in \n", "('Store Manager',\n", "'Assistant Store Manager',\n", "'Cluster Manager',\n", "'Senior Assistant Store Manager',\n", "'Senior Cluster Manager',\n", "'Junior City CEO',\n", "'Senior Store Manager',\n", "'City Ops Head',\n", "'City CEO',\n", "'Senior City Ops Head',\n", "'Reliever Assistant Store Manager',\n", "'Reliever Store Manager',\n", "'Reliever City Ops Head')\n", "and is_mapping_active = true\n", "\n", ")\n", ",\n", "\n", "mid_base as \n", "(\n", "select email_id, designation, outlet_id\n", "from base b\n", "cross join unnest(outlet_ids) as  t (outlet_id)\n", ")\n", ",\n", "\n", "referral_lead as \n", "(select\n", "cast(json_extract_scalar(rl.meta, '$.lead_name') as varchar)  as Name,\n", "rl.lead_phone_number_encrypted as Phone_Number,\n", "a.designation,\n", "site_id,\n", "insert_ds_ist\n", "from lake_ops_management.referral_leads rl\n", "left join ops_management.user u\n", "on rl.lead_phone_number = zsha1(u.phone)\n", "left join \n", "(\n", "select lead_phone_number, \n", "CASE \n", "    WHEN qty_1 IN (2,10,30) AND qty_2 IN (2,10,30) THEN 'Captain OD'\n", "    WHEN qty_1 IN (1,15,30,45) AND qty_2 IN (1,15,30,45) THEN 'Captain' \n", "    ELSE 'Captain' \n", "END AS designation\n", "from \n", "(select lead_phone_number, \n", "cast(json_extract_scalar(config,'$.incentives[0].qty') as integer) as qty_1, \n", "cast(json_extract_scalar(config,'$.incentives[1].qty') as integer) as qty_2 \n", "from ops_management.referral_leads\n", "where insert_ds_ist between cast(current_date - interval '7' day as varchar)\n", "and cast(current_date - interval '1' day as var<PERSON><PERSON>)\n", ")\n", ") a \n", "on rl.lead_phone_number = a.lead_phone_number\n", "where insert_ds_ist between cast(current_date - interval '7' day as varchar)\n", "and cast(current_date - interval '1' day as var<PERSON><PERSON>)\n", "and u.employee_id is null\n", ")\n", ",\n", "\n", "referral_lead_final as \n", "(select *\n", "from referral_lead\n", "where designation = 'Captain' )\n", ",\n", "\n", "count_leads as \n", "(select site_id,\n", "count(rl.lead_phone_number) as num_of_leads\n", "from ops_management.referral_leads rl\n", "left join ops_management.user u\n", "on rl.lead_phone_number = zsha1(u.phone)\n", "left join \n", "(\n", "select lead_phone_number, \n", "CASE \n", "    WHEN qty_1 IN (2,10,30) AND qty_2 IN (2,10,30) THEN 'Captain OD'\n", "    WHEN qty_1 IN (1,15,30,45) AND qty_2 IN (1,15,30,45) THEN 'Captain' \n", "    ELSE 'Captain' \n", "END AS designation\n", "from \n", "(select lead_phone_number, \n", "cast(json_extract_scalar(config,'$.incentives[0].qty') as integer) as qty_1, \n", "cast(json_extract_scalar(config,'$.incentives[1].qty') as integer) as qty_2 \n", "from ops_management.referral_leads\n", "where insert_ds_ist between cast(current_date - interval '7' day as varchar)\n", "and cast(current_date - interval '1' day as var<PERSON><PERSON>)\n", ")\n", ") a \n", "on rl.lead_phone_number = a.lead_phone_number\n", "where insert_ds_ist between cast(current_date - interval '7' day as varchar)\n", "and cast(current_date - interval '1' day as var<PERSON><PERSON>)\n", "and a.designation = 'Captain'\n", "and u.employee_id is null\n", "group by 1\n", ")\n", ",\n", "\n", "outlet_mapping as \n", "(select outlet_id, outlet_name\n", "from storeops_etls.employee_metrics_daily\n", "where date_ >= current_date - interval '14' day\n", "group by 1,2)\n", "\n", "\n", "\n", "select\n", "hg.date_ts,\n", "hg.outlet_id,\n", "hg.gap,\n", "m.email_id, \n", "m.designation,\n", "rlf.Name,\n", "rlf.Phone_Number,\n", "rlf.designation as designation_ref,\n", "rlf.site_id,\n", "coalesce(num_of_leads,0) as num_of_leads,\n", "om.outlet_name,\n", "date(insert_ds_ist) as referral_date\n", "from hiring_gap hg\n", "left join mid_base m \n", "on hg.outlet_id = m.outlet_id\n", "left join referral_lead_final rlf\n", "on hg.outlet_id = cast(rlf.site_id as integer)\n", "left join count_leads cl\n", "on hg.outlet_id = cast(cl.site_id as integer)\n", "left join outlet_mapping om \n", "on hg.outlet_id = om.outlet_id\n", "order by 11 desc\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "91b8e860-3d21-4b84-a2de-e85f9b012aa4", "metadata": {}, "outputs": [], "source": ["query2 = \"\"\"\n", "\n", "with base as \n", "(select distinct a.employee_id, email_id,name,phone, designation,outlet_ids\n", "from storeops_etls.user_access_mapping a\n", "left join (\n", "  select employee_id, max_by(phone, update_ts) as phone\n", "  from ops_management.user\n", "  group by 1\n", ") b\n", "on a.employee_id = b.employee_id\n", "where designation in \n", "('Store Manager',\n", "'Assistant Store Manager',\n", "'Cluster Manager',\n", "'Senior Assistant Store Manager',\n", "'Senior Cluster Manager',\n", "'Junior City CEO',\n", "'Senior Store Manager',\n", "'City Ops Head',\n", "'City CEO',\n", "'Senior City Ops Head',\n", "'Reliever Assistant Store Manager',\n", "'Reliever Store Manager',\n", "'Reliever City Ops Head')\n", "and is_mapping_active = true\n", ")\n", ",\n", "\n", "mid_base as \n", "(\n", "select email_id, designation, outlet_id\n", "from base b\n", "cross join unnest(outlet_ids) as  t (outlet_id)\n", ")\n", ",\n", "\n", "emp_base as \n", "(select \n", "name_joinee,\n", "phone_joinee,\n", "employee_id_ref,\n", "referred_date,\n", "outlet_id_referred_for,\n", "CASE \n", "    WHEN qty_1 IN (2,10,30) AND qty_2 IN (2,10,30) THEN 'Captain OD'\n", "    WHEN qty_1 IN (1,15,30,45) AND qty_2 IN (1,15,30,45) THEN 'Captain' \n", "    ELSE 'Captain' \n", "END AS designation\n", "from \n", "(select\n", "cast(json_extract_scalar(rl.meta, '$.lead_name') as varchar)  as name_joinee,\n", "cast(json_extract_scalar(config,'$.incentives[0].qty') as integer) as qty_1, \n", "cast(json_extract_scalar(config,'$.incentives[1].qty') as integer) as qty_2, \n", "rl.lead_phone_number as phone_joinee,\n", "em.employee_id as employee_id_ref,\n", "cast(rl.insert_ds_ist as date) as referred_date, \n", "cast(site_id as int) outlet_id_referred_for\n", "from ops_management.referral_leads rl\n", "left join ops_management.employee_tenant_map em\n", "on rl.referer_id = em.id\n", "where rl.insert_ds_ist >= cast(current_date - interval '3' day as varchar)\n", "and em.insert_ds_ist is not null\n", ")\n", ")\n", ",\n", "\n", "max_emp_id as\n", "(select phone, max_by(employee_id,update_ts) as employee_id\n", "from ops_management.user\n", "where designation = 'Captain'\n", "and status = 'ACTIVE'\n", "group by 1)\n", ",\n", "\n", "fsc as \n", "(select employee_id, min(date_) as fss_date\n", "from storeops_etls.employee_metrics_daily\n", "where date_ >= current_date - interval '2' year\n", "and designation = 'Captain'\n", "group by 1\n", "having sum(total_active_time) > 240)\n", "\n", "select \n", "b.name_joinee,\n", "b.referred_date,\n", "b.outlet_id_referred_for,\n", "b.designation,\n", "me.employee_id,me.phone as phone,\n", "mb.email_id,\n", "mb.designation as store_designation,\n", "mb.outlet_id\n", "from emp_base b\n", "left join max_emp_id me\n", "on b.phone_joinee = zsha1(me.phone)\n", "left join fsc f\n", "on me.employee_id = f.employee_id\n", "left join mid_base mb\n", "on b.outlet_id_referred_for = mb.outlet_id\n", "where b.designation = 'Captain'\n", "and me.employee_id is not null\n", "and fss_date is null\n", "order by 3\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "e9ecc260-3786-4985-9396-839555356922", "metadata": {}, "outputs": [], "source": ["df_sql2 = pd.read_sql(query2, trino_con)"]}, {"cell_type": "code", "execution_count": null, "id": "852d5b9d-8fcc-4918-bda8-d68d69f7c7fc", "metadata": {}, "outputs": [], "source": ["df1 = pd.read_sql(query, trino_con)"]}, {"cell_type": "code", "execution_count": null, "id": "b9194ce4-ccd5-477c-a8ce-b8949d928052", "metadata": {}, "outputs": [], "source": ["df_sql = df1"]}, {"cell_type": "code", "execution_count": null, "id": "4750b3b1-4afc-4083-9516-ed2a03f5cd2b", "metadata": {}, "outputs": [], "source": ["df_csv = pb.from_sheets(\n", "    \"1KcFotxVnyxWbFC6zubJ29Kzu-8CVQqp0kRJfXhuuKNU\",\n", "    \"hr_spoc\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "aaea38c1-4bbd-469f-98e9-38e1a8529fc5", "metadata": {}, "outputs": [], "source": ["# df_csv = pd.read_csv(\"hr_spoc.csv\")  # Replace with actual file path"]}, {"cell_type": "code", "execution_count": null, "id": "0e7441e5-51bd-4017-a168-d4e4ff978efb", "metadata": {}, "outputs": [], "source": ["# df_csv.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "db566a8d-234f-42da-9884-c54ee7365073", "metadata": {}, "outputs": [], "source": ["df_csv = df_csv.drop(columns=[\"City\"])\n", "\n", "# Change data types\n", "df_csv[\"outlet_id\"] = df_csv[\"outlet_id\"].astype(int)\n", "df_csv[\"email_id_spoc\"] = df_csv[\"email_id_spoc\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "e796c8dc-cf13-4f67-a2d5-96caf6fd3622", "metadata": {}, "outputs": [], "source": ["# df_csv.head()"]}, {"cell_type": "code", "execution_count": null, "id": "111d68b7-e974-4675-a154-94e26c5e5294", "metadata": {}, "outputs": [], "source": ["yesterday = (datetime.today() - <PERSON><PERSON>ta(days=1)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "1d5dcf90-3c16-42c3-817f-60da2ac95ece", "metadata": {}, "outputs": [], "source": ["df_new = df_csv.merge(df_sql, left_on=\"outlet_id\", right_on=\"outlet_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "8f22a0d9-bca1-4bd5-af47-2ebdda3fc0e8", "metadata": {}, "outputs": [], "source": ["# df_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "03732cf0-e9ed-4dae-890f-1ce61d1b534b", "metadata": {}, "outputs": [], "source": ["df_new = df_new.drop(columns=[\"date_ts\"], errors=\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "1ef8f2e5-492a-448f-91ff-da97f8c4535b", "metadata": {}, "outputs": [], "source": ["df_new.insert(0, \"date_ts\", yesterday)"]}, {"cell_type": "code", "execution_count": null, "id": "3454b845-0cac-4df0-8c62-397f596f9319", "metadata": {}, "outputs": [], "source": ["# df_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6cb61772-e113-4fbb-8ac6-535dae4fd51b", "metadata": {}, "outputs": [], "source": ["df_new[\"designation\"] = \"HR SPOC\""]}, {"cell_type": "code", "execution_count": null, "id": "37603d30-9d90-4dc4-811f-180c1f9638e3", "metadata": {}, "outputs": [], "source": ["final_columns = [\n", "    \"date_ts\",  # 1st column (manually created)\n", "    \"outlet_id\",  # 2nd column (from CSV)\n", "    df_sql.columns[2],  # 3rd column (from df_sql, avoiding date_ts)\n", "    \"email_id_spoc\",  # 4th column (from CSV)\n", "    \"designation\",  # 5th column (fixed value)\n", "] + list(df_sql.columns[5:12])"]}, {"cell_type": "code", "execution_count": null, "id": "c7dd9d12-ccd4-44aa-a704-ee268ad72a41", "metadata": {}, "outputs": [], "source": ["df_new = df_new[final_columns]"]}, {"cell_type": "code", "execution_count": null, "id": "df733f7d-15e8-4ad7-8a14-524139a1cc29", "metadata": {}, "outputs": [], "source": ["# df_new"]}, {"cell_type": "code", "execution_count": null, "id": "6bd83784-e29f-48fb-9a1a-94753c063e63", "metadata": {}, "outputs": [], "source": ["df_new = df_new.fillna(\"\")"]}, {"cell_type": "code", "execution_count": null, "id": "24f3461c-0a7b-4a89-9b0b-18852eb0f851", "metadata": {}, "outputs": [], "source": ["df_new[\"date_ts\"] = df_new[\"date_ts\"].astype(str)\n", "df_new[\"gap\"] = df_new[\"gap\"].replace(\"\", np.nan)  # Replace empty strings with NaN\n", "df_new[\"gap\"] = (\n", "    pd.to_numeric(df_new[\"gap\"], errors=\"coerce\").fillna(0).astype(\"int64\")\n", ")  # Convert safely\n", "df_new[\"num_of_leads\"] = df_new[\"num_of_leads\"].replace(\n", "    \"\", np.nan\n", ")  # Replace empty strings with NaN\n", "df_new[\"num_of_leads\"] = (\n", "    pd.to_numeric(df_new[\"num_of_leads\"], errors=\"coerce\").fillna(0).astype(\"int64\")\n", ")  # Convert safely"]}, {"cell_type": "code", "execution_count": null, "id": "c035870e-dd76-440c-a279-6f9f1a04ffd7", "metadata": {}, "outputs": [], "source": ["# print(df_new.dtypes)"]}, {"cell_type": "code", "execution_count": null, "id": "283c925f-952f-4598-9cf0-45e7f077b308", "metadata": {}, "outputs": [], "source": ["# print(df_sql.dtypes)"]}, {"cell_type": "code", "execution_count": null, "id": "a66732dd-8a0a-4a0d-b8b8-c7cacbbea03f", "metadata": {}, "outputs": [], "source": ["df_new.rename(columns={\"email_id_spoc\": \"email_id\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "868e02ef-f8ac-4252-965f-1b526030cca2", "metadata": {}, "outputs": [], "source": ["# df_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "aca8dbd7-63da-4065-b8d3-09b4a8f6881f", "metadata": {}, "outputs": [], "source": ["# Concatenate (Union) both DataFrames\n", "df_merged = pd.concat([df_sql, df_new], ignore_index=True)\n", "\n", "df_merged = df_merged.dropna(subset=[\"outlet_name\"])\n", "df_merged = df_merged[df_merged[\"outlet_name\"] != \"\"]\n", "\n", "# Fill NaN values with empty strings (for emails)\n", "df_merged = df_merged.fillna(\"\")"]}, {"cell_type": "code", "execution_count": null, "id": "67cf150c-9545-48ae-80a9-6c6989163c4c", "metadata": {}, "outputs": [], "source": ["df_merged = decrypt_dataframe(df_merged, [\"Phone_Number\"], DECRYPTION_KEY)"]}, {"cell_type": "code", "execution_count": null, "id": "7b1b4077-ae78-4e3d-a9d3-cad63e4007ed", "metadata": {}, "outputs": [], "source": ["email_col_in_df_sql2 = \"email_id\"\n", "\n", "df_merged = df_merged.fillna(\"\")\n", "df_sql2 = df_sql2.fillna(\"\")\n", "\n", "grouped = df_merged.groupby(\"email_id\")\n", "sent_emails = []\n", "\n", "\n", "def generate_table_html(df, headers):\n", "    \"\"\"Generate HTML table with inline styles for email compatibility.\"\"\"\n", "    if df.empty:\n", "        return \"\"\n", "\n", "    table_style = \"border-collapse: collapse; width: 100%; margin: 20px 0;\"\n", "    th_style = \"border: 1px solid black; padding: 8px; text-align: left; background-color: #f2f2f2;\"\n", "    td_style = \"border: 1px solid black; padding: 8px; text-align: left;\"\n", "\n", "    table_html = f'<table style=\"{table_style}\" border=\"1\" cellpadding=\"8\" cellspacing=\"0\">'\n", "    table_html += \"<thead><tr>\"\n", "    table_html += \"\".join(f'<th style=\"{th_style}\">{col}</th>' for col in headers)\n", "    table_html += \"</tr></thead><tbody>\"\n", "\n", "    for i, (_, row) in enumerate(df.iterrows()):\n", "        row_style = td_style\n", "        if i % 2 == 1:\n", "            row_style = td_style + \" background-color: #f9f9f9;\"\n", "        table_html += (\n", "            \"<tr>\" + \"\".join(f'<td style=\"{row_style}\">{val}</td>' for val in row) + \"</tr>\"\n", "        )\n", "\n", "    table_html += \"</tbody></table>\"\n", "    return table_html\n", "\n", "\n", "for email_id, group in grouped:\n", "    hiring_gap_data_raw = group.iloc[:, [1, 10, 2, 9]].drop_duplicates()\n", "    hiring_gap_data_raw.columns = [\"Outlet ID\", \"Outlet Name\", \"Hiring Gap\", \"Referral Leads\"]\n", "    hiring_gap_data_processed = hiring_gap_data_raw.replace(\"\", pd.NA).dropna(how=\"all\")\n", "    table_html_hiring_gap = \"\"\n", "\n", "    if not hiring_gap_data_processed.empty:\n", "        hiring_gap_data_processed[\"Outlet Name\"] = (\n", "            hiring_gap_data_processed[\"Outlet Name\"].astype(str).fillna(\"\")\n", "        )\n", "        hiring_gap_data_processed[\"Hiring Gap\"] = (\n", "            hiring_gap_data_processed[\"Hiring Gap\"].astype(str).fillna(\"\")\n", "        )\n", "        hiring_gap_data_processed[\"Referral Leads\"] = (\n", "            hiring_gap_data_processed[\"Referral Leads\"].astype(str).fillna(\"\")\n", "        )\n", "\n", "        grouped_outlet_hiring = hiring_gap_data_processed.groupby(\"Outlet ID\", as_index=False).agg(\n", "            {\n", "                \"Outlet Name\": lambda x: \", \".join(\n", "                    sorted(set(str(val) for val in x if pd.notna(val) and str(val).strip()))\n", "                ),\n", "                \"Hiring Gap\": lambda x: \", \".join(\n", "                    sorted(set(str(val) for val in x if pd.notna(val) and str(val).strip()))\n", "                ),\n", "                \"Referral Leads\": lambda x: \", \".join(\n", "                    sorted(set(str(val) for val in x if pd.notna(val) and str(val).strip()))\n", "                ),\n", "            }\n", "        )\n", "        grouped_outlet_hiring = grouped_outlet_hiring[\n", "            (grouped_outlet_hiring[\"Outlet Name\"] != \"\")\n", "            | (grouped_outlet_hiring[\"Hiring Gap\"] != \"\")\n", "            | (grouped_outlet_hiring[\"Referral Leads\"] != \"\")\n", "        ]\n", "        if not grouped_outlet_hiring.empty:\n", "            table_html_hiring_gap = generate_table_html(\n", "                grouped_outlet_hiring,\n", "                [\"Outlet ID\", \"Outlet Name\", \"Hiring Gap\", \"Open Referral Leads\"],\n", "            )\n", "\n", "    referral_leads_raw = group.iloc[:, [11, 8, 5, 6, 7]].drop_duplicates()\n", "    referral_leads_raw.columns = [\n", "        \"Referral Date\",\n", "        \"Outlet ID\",\n", "        \"Lead Name\",\n", "        \"Phone Number\",\n", "        \"Designation\",\n", "    ]\n", "    referral_leads_processed = referral_leads_raw.replace(\"\", pd.NA).dropna(how=\"all\")\n", "    table_html_referral_leads = \"\"\n", "\n", "    if not referral_leads_processed.empty:\n", "        referral_leads_processed[\"Referral Date\"] = pd.to_datetime(\n", "            referral_leads_processed[\"Referral Date\"], errors=\"coerce\"\n", "        )\n", "        referral_leads_processed = referral_leads_processed.sort_values(\n", "            by=\"Referral Date\", ascending=False, na_position=\"last\"\n", "        )\n", "        referral_leads_processed[\"Referral Date\"] = (\n", "            referral_leads_processed[\"Referral Date\"].dt.strftime(\"%Y-%m-%d\").fillna(\"N/A\")\n", "        )\n", "        table_html_referral_leads = generate_table_html(\n", "            referral_leads_processed,\n", "            [\"Referral Date\", \"Outlet ID\", \"Lead Name\", \"Phone Number\", \"Designation\"],\n", "        )\n", "\n", "    table_html_fss_data = \"\"\n", "    df_sql2_final_table_data = pd.DataFrame()\n", "\n", "    if email_col_in_df_sql2 in df_sql2.columns:\n", "        relevant_df_sql2 = df_sql2[df_sql2[email_col_in_df_sql2] == email_id].copy()\n", "\n", "        if not relevant_df_sql2.empty:\n", "            fss_col_indices = [1, 0, 5, 2]\n", "            if all(idx < len(relevant_df_sql2.columns) for idx in fss_col_indices):\n", "                selected_cols_df_sql2 = relevant_df_sql2.iloc[:, fss_col_indices]\n", "                fss_headers = [\"Referred Date\", \"Name\", \"Phone\", \"Outlet ID\"]\n", "                selected_cols_df_sql2.columns = fss_headers\n", "\n", "                df_sql2_final_table_data = selected_cols_df_sql2.replace(\"\", pd.NA).dropna(\n", "                    how=\"all\"\n", "                )\n", "\n", "                if not df_sql2_final_table_data.empty:\n", "                    df_sql2_final_table_data = df_sql2_final_table_data.fillna(\"\")\n", "                    table_html_fss_data = generate_table_html(df_sql2_final_table_data, fss_headers)\n", "\n", "    if not table_html_hiring_gap and not table_html_referral_leads and not table_html_fss_data:\n", "        continue\n", "\n", "    message = f\"\"\"\n", "    <p>Hi Team,</p>\n", "    <p>Please find below the details regarding hiring gaps, referral leads, and FSS pending leads for your store(s).</p>\n", "    \"\"\"\n", "\n", "    if table_html_fss_data:\n", "        message += f'<p style=\"margin-top: 20px; font-weight: bold;\">Pending FSS (ID Created):</p>{table_html_fss_data}'\n", "\n", "    if table_html_hiring_gap:\n", "        message += f'<p style=\"margin-top: 20px; font-weight: bold;\">Hiring Gap Data:</p>{table_html_hiring_gap}'\n", "\n", "    if table_html_referral_leads:\n", "        message += f'<p style=\"margin-top: 20px; font-weight: bold;\">Referral Leads:</p>{table_html_referral_leads}'\n", "        message += \"<p>Please ensure you call the referral leads and convert them to close the hiring gap at your store(s).</p>\"\n", "\n", "    message += \"\"\"\n", "    <p>All the best,<br>Blinkit</p>\n", "    <p><i>Note - This is an automated email, please do not reply.</i></p>\n", "    \"\"\"\n", "\n", "    if email_id and email_id.strip():\n", "        pb.send_email(\n", "            from_email=\"<EMAIL>\",\n", "            to_email=[email_id],\n", "            subject=\"Referral Leads, Hiring Gaps & FSS Data\",\n", "            html_content=message,\n", "            files=[],\n", "            cc=[],\n", "            mime_subtype=\"mixed\",\n", "            mime_charset=\"utf-8\",\n", "        )\n", "        sent_emails.append(email_id)"]}, {"cell_type": "code", "execution_count": null, "id": "a42f3350-0696-4e7a-a8dc-a095e5680024", "metadata": {}, "outputs": [], "source": ["df_cleaned = df_merged.replace(\"\", None)\n", "\n", "email_leads = df_cleaned.iloc[:, [3, 1, 5]].copy()\n", "email_leads.columns = [\"email_id\", \"outlet_id\", \"lead_info\"]\n", "\n", "lead_count_df = email_leads.groupby([\"email_id\", \"outlet_id\"], as_index=False).agg(\n", "    Total_Leads=(\"lead_info\", lambda x: x.notna().sum())\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1d1de0c8-5d99-462f-953e-dc8ddf004302", "metadata": {}, "outputs": [], "source": ["sent_email_df = pd.DataFrame(sent_emails, columns=[\"email_id\"])\n", "email_count_df = (\n", "    sent_email_df.value_counts()\n", "    .reset_index(name=\"emails_sent\")\n", "    .sort_values(\"emails_sent\", ascending=False)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e08e0da0-1660-42bc-b119-19655da00148", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    df_merged,\n", "    \"1EWmLZMrO3C1diiWjaor_dffySBSp4sDIQUjEU7Hsdik\",\n", "    \"main_query\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3b77b3d3-d267-4438-812e-1ad46a4db4dd", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    lead_count_df,\n", "    \"1EWmLZMrO3C1diiWjaor_dffySBSp4sDIQUjEU7Hsdik\",\n", "    \"grouped_mails\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c0ea4e0a-02dc-46bf-b601-68cd2a6c8546", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    email_count_df,\n", "    \"1EWmLZMrO3C1diiWjaor_dffySBSp4sDIQUjEU7Hsdik\",\n", "    \"email_count\",\n", "    clear_cache=True,\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 5}