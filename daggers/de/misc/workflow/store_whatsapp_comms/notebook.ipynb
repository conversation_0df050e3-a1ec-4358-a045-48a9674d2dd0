{"cells": [{"cell_type": "code", "execution_count": null, "id": "b37e1229-b237-4d66-b6c9-c274deb0b40e", "metadata": {}, "outputs": [], "source": ["# # Installs\n", "!pip install pycryptodome --quiet"]}, {"cell_type": "code", "execution_count": null, "id": "52a9b193-55dc-4cff-a959-eb5270f1ea48", "metadata": {}, "outputs": [], "source": ["from datetime import date, datetime, timedelta\n", "import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import json\n", "import pytz\n", "import requests\n", "import ast\n", "from zoneinfo import ZoneInfo\n", "import os\n", "import sys"]}, {"cell_type": "code", "execution_count": null, "id": "59b91699-9107-48c3-adcb-1f0b54204d02", "metadata": {}, "outputs": [], "source": ["os.environ[\"VAULT_SERVICE_TOKEN\"] = \"\"\n", "os.environ[\"VAULT_TOKEN\"] = \"\"\n", "os.environ[\"VAULT_K8S_AUTH_ROLE\"] = \"dse-pii-read-only\"\n", "os.environ[\"VAULT_K8S_AUTH_MOUNT\"] = \"eks\""]}, {"cell_type": "code", "execution_count": null, "id": "1e999ac5-171c-4f9e-8b6d-cca0f6aba968", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "3daca19a-2fcd-4b23-ab4f-9fa774167531", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "48b0646e-fcb3-4142-9d37-309e81a5511d", "metadata": {}, "outputs": [], "source": ["from utils import decrypt_dataframe"]}, {"cell_type": "code", "execution_count": null, "id": "7f9ec64a-0daf-48ea-964c-b1f69dd86315", "metadata": {}, "outputs": [], "source": ["whatsapp_comm_url = \"https://consumer-notifications-canary-notification.prod-sgp-k8s.grofer.io/api/v1/notification/whatsapp/send\"\n", "whatsapp_comm_key = pb.get_secret(\"dse/credentials/ops_management\")[\"whatsapp_comm_api_key\"]\n", "headers = {\"Content-Type\": \"application/json\", \"x-api-key\": whatsapp_comm_key}"]}, {"cell_type": "code", "execution_count": null, "id": "7fdae6a9-ebab-45d3-886a-e23ac2c00dc5", "metadata": {}, "outputs": [], "source": ["trino = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "d90d041b-aabc-4496-93bd-067ed5f943cf", "metadata": {}, "outputs": [], "source": ["# data = pd.read_csv(\"referal_sheet.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "e718f16d-8b2f-41be-aeb7-624966824423", "metadata": {}, "outputs": [], "source": ["data = pb.from_sheets(\n", "    \"1CCL2FtjiCcgl2YN8_QbLotmoD3Jt4EDn5fdgGRAYlas\",\n", "    \"referral_wa_com_final\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "620f97b1-2ac1-432c-95eb-71b5073c8dad", "metadata": {}, "outputs": [], "source": ["# data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "67a828fe-6ec2-4a6e-93ec-f7b09dfa4b36", "metadata": {}, "outputs": [], "source": ["data1 = data.astype(\n", "    {\n", "        \"Gupshup Template\": \"str\",\n", "        \"query\": \"str\",\n", "        \"DOW\": \"str\",\n", "        \"HOUR\": \"str\",\n", "        \"variable_1\": \"str\",\n", "        \"variable_2\": \"str\",\n", "        \"variable_3\": \"str\",\n", "        \"variable_4\": \"str\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "67c32829-5879-443e-ab8d-4a5e64c1340c", "metadata": {}, "outputs": [], "source": ["template_query = data1[\n", "    [\n", "        \"<PERSON><PERSON><PERSON><PERSON> Template\",\n", "        \"query\",\n", "        \"DOW\",\n", "        \"HOUR\",\n", "        \"variable_1\",\n", "        \"variable_2\",\n", "        \"variable_3\",\n", "        \"variable_4\",\n", "    ]\n", "].rename(columns={\"Gupshup Template\": \"template_id\", \"DOW\": \"dow\", \"HOUR\": \"hour\"})"]}, {"cell_type": "code", "execution_count": null, "id": "7b03316e-96b2-42f8-90d0-42bfe1ee116b", "metadata": {}, "outputs": [], "source": ["template_query"]}, {"cell_type": "code", "execution_count": null, "id": "fc9d6ac1-347c-4517-9d01-018dd4956d60", "metadata": {}, "outputs": [], "source": ["results = {}\n", "\n", "now_ist = datetime.now(ZoneInfo(\"Asia/Kolkata\"))\n", "current_dow = now_ist.isoweekday()\n", "current_hour = now_ist.strftime(\"%-I:00 %p\")\n", "current_timestamp_str = now_ist.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "for idx, row in template_query.iterrows():\n", "    template_id = row[\"template_id\"]\n", "    query = row[\"query\"]\n", "    dow_raw = row[\"dow\"]\n", "    dow_list = ast.literal_eval(dow_raw) if isinstance(dow_raw, str) else dow_raw\n", "    scheduled_hour = row[\"hour\"]\n", "\n", "    if current_dow in dow_list and (scheduled_hour == \"ALL\" or current_hour in scheduled_hour):\n", "        try:\n", "            df = pd.read_sql(query, trino)\n", "\n", "            # Only decrypt if is_encrypted column exists and indicates encryption\n", "            if \"is_encrypted\" in df.columns:\n", "                if df[\"is_encrypted\"].any():\n", "                    df = decrypt_dataframe(df, [\"phone_joinee\"])\n", "\n", "                # Optionally drop the is_encrypted flag if you don't want it in the final data\n", "                df = df.drop(columns=[\"is_encrypted\"], errors=\"ignore\")\n", "\n", "            results[template_id] = {\n", "                \"data\": df,\n", "                \"variable_1\": row.get(\"variable_1\"),\n", "                \"variable_2\": row.get(\"variable_2\"),\n", "                \"variable_3\": row.get(\"variable_3\"),\n", "                \"variable_4\": row.get(\"variable_4\"),\n", "            }\n", "\n", "        except Exception as e:\n", "            print(f\"Error executing query for template_id {template_id}: {e}\")"]}, {"cell_type": "code", "execution_count": null, "id": "828e5a19-684f-4bc5-af1a-9b752a8ac78b", "metadata": {}, "outputs": [], "source": ["# data"]}, {"cell_type": "code", "execution_count": null, "id": "1d179d1b-5c7c-473e-b30c-88f73ca5d9f4", "metadata": {}, "outputs": [], "source": ["valid_dataframes = []\n", "\n", "for tid, content in results.items():\n", "    df = content.get(\"data\")\n", "\n", "    if isinstance(df, pd.DataFrame) and not df.empty:\n", "        df = df.copy()\n", "\n", "        df[\"template_id\"] = tid\n", "        df[\"variable_1\"] = content.get(\"variable_1\")\n", "        df[\"variable_2\"] = content.get(\"variable_2\")\n", "        df[\"variable_3\"] = content.get(\"variable_3\")\n", "        df[\"variable_4\"] = content.get(\"variable_4\")\n", "\n", "        column_order = [\"template_id\", \"variable_1\", \"variable_2\", \"variable_3\", \"variable_4\"]\n", "\n", "        df = df[\n", "            [\n", "                col\n", "                for col in df.columns\n", "                if col != \"template_id\"\n", "                and col != \"variable_1\"\n", "                and col != \"variable_2\"\n", "                and col != \"variable_3\"\n", "                and col != \"variable_4\"\n", "            ]\n", "            + column_order\n", "        ]\n", "\n", "        valid_dataframes.append(df)\n", "\n", "if valid_dataframes:\n", "    final_df = pd.concat(valid_dataframes, ignore_index=True)\n", "else:\n", "    final_df = pd.DataFrame(\n", "        columns=[\n", "            \"phone_joinee\",\n", "            \"variable_1_value\",\n", "            \"variable_2_value\",\n", "            \"template_id\",\n", "            \"variable_1\",\n", "            \"variable_2\",\n", "            \"variable_3\",\n", "            \"variable_4\",\n", "        ]\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "d31db87d-7040-4391-bdfd-e9a7957896e8", "metadata": {}, "outputs": [], "source": ["distinct_nums = final_df[\"phone_joinee\"].drop_duplicates().tolist()"]}, {"cell_type": "code", "execution_count": null, "id": "37a9f8f0-169e-4dd8-b8e9-3ea624437651", "metadata": {}, "outputs": [], "source": ["values_clause = \",\\n    \".join(f\"({num})\" for num in distinct_nums)"]}, {"cell_type": "code", "execution_count": null, "id": "106ead26-cda6-4b8a-9d44-faf6eae7ef08", "metadata": {}, "outputs": [], "source": ["if not values_clause.strip():\n", "    data_phone = pd.DataFrame(columns=[\"phone\", \"phone_hashed\"])\n", "else:\n", "    query = f\"\"\"SELECT phone, zsha1(CAST(phone AS VARCHAR)) AS phone_hashed\n", "                FROM (\n", "                  VALUES \n", "                    {values_clause}\n", "                ) AS t(phone)\"\"\"\n", "    data_phone = pd.read_sql(query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "66e14988-27e4-4bc1-8c0c-825790cf591f", "metadata": {}, "outputs": [], "source": ["final_df[\"phone_joinee\"] = pd.to_numeric(final_df[\"phone_joinee\"], errors=\"coerce\").astype(\"int\")"]}, {"cell_type": "code", "execution_count": null, "id": "e0c6a16d-ac3d-4c04-9cc2-b4e31027c4e5", "metadata": {}, "outputs": [], "source": ["final_mid = pd.merge(\n", "    final_df,\n", "    data_phone,\n", "    left_on=[\"phone_joinee\"],\n", "    right_on=[\"phone\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "28f00246-ebb5-40ae-a0bc-9c73221afcd8", "metadata": {}, "outputs": [], "source": ["query_2 = \"\"\"select phone_hashed, template_id\n", "from storeops_etls.store_ops_whatsapp_communications_logs\n", "where update_ts_ist >= cast(current_date as timestamp)\n", "\"\"\"\n", "already_sent = pd.read_sql(query_2, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "c0a2fdc9-9bfc-4b29-a3af-68c2e2607921", "metadata": {}, "outputs": [], "source": ["if already_sent is None or already_sent.empty:\n", "    already_sent = pd.DataFrame(columns=[\"template_id\", \"phone_hashed\"])"]}, {"cell_type": "code", "execution_count": null, "id": "9c9c7925-594a-41df-9bdc-a72077ad47a9", "metadata": {}, "outputs": [], "source": ["final_df = pd.merge(\n", "    final_mid, already_sent, on=[\"template_id\", \"phone_hashed\"], how=\"left\", indicator=True\n", ")\n", "\n", "final_df = final_df[final_df[\"_merge\"] == \"left_only\"].drop(columns=[\"_merge\"])"]}, {"cell_type": "code", "execution_count": null, "id": "c4e2975b-f23c-436f-a41a-dd86f7c17bad", "metadata": {}, "outputs": [], "source": ["final_df = final_df.drop(columns=[\"phone\", \"phone_hashed\"], errors=\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "d95f16b5-db77-4bdc-b5be-159e24b99d13", "metadata": {}, "outputs": [], "source": ["batch_size = 50\n", "success_count = 0\n", "failure_count = 0\n", "\n", "print(\"\\nStarting notification process...\")\n", "\n", "all_payloads = []\n", "for i in range(0, len(final_df), batch_size):\n", "    batch = final_df.iloc[i : i + batch_size]\n", "    print(f\"\\nProcessing batch {i//batch_size + 1}...\")\n", "\n", "    for _, row in batch.iterrows():\n", "        phone_joinee = str(row[\"phone_joinee\"]).strip()\n", "\n", "        if not phone_joinee or phone_joinee.lower() in [\"nan\", \"none\"]:\n", "            print(f\"✗ Invalid phone number: {row['phone_joinee']}. Skipping this user.\")\n", "            failure_count += 1\n", "            continue\n", "\n", "        template_vars = {}\n", "\n", "        for col in row.index:\n", "            if col.startswith(\"variable_\") and not col.endswith(\"_value\"):\n", "                var_name = row[col]\n", "                value_column = f\"{col}_value\"\n", "\n", "                if pd.notna(var_name) and value_column in row.index and pd.notna(row[value_column]):\n", "                    template_vars[str(var_name).strip()] = str(row[value_column]).strip()\n", "\n", "        payload = {\n", "            \"type\": \"whatsapp\",\n", "            \"receiver_ids\": [phone_joinee],\n", "            \"template_id\": row[\"template_id\"],\n", "            \"delay\": 0,\n", "            \"template_vars\": template_vars,\n", "            \"expiry_ts\": 1928494038,\n", "        }\n", "\n", "        # print(f\"Payload for {phone_joinee}: {payload}\")\n", "\n", "        try:\n", "            response = requests.post(whatsapp_comm_url, headers=headers, json=payload)\n", "            if response.status_code == 200:\n", "                # if 200 == 200:\n", "                success_count += 1\n", "                # print(f\"✓ Sent to {phone_joinee}\")\n", "                # print(f\"Payload for {phone_joinee}: {payload}\")\n", "                payload[\"status\"] = \"success\"\n", "            else:\n", "                failure_count += 1\n", "                # print(f\"✗ Failed for {phone_joinee} - HTTP {response.status_code}\")\n", "                # print(f\"Reason: {response.text}\")\n", "                payload[\"status\"] = \"failure\"\n", "        except Exception as e:\n", "            failure_count += 1\n", "            print(f\"✗ Exception for {phone_joinee}: {str(e)}\")\n", "\n", "        all_payloads.append(payload)\n", "\n", "print(f\"\\n✔️ Total sent: {success_count} | ❌ Total failed: {failure_count}\")"]}, {"cell_type": "code", "execution_count": null, "id": "c224261f-a581-4a43-b853-d2cfacb79f9b", "metadata": {}, "outputs": [], "source": ["print(f\"\\nTotal successful payloads collected: {len(all_payloads)}\")"]}, {"cell_type": "code", "execution_count": null, "id": "7d4bcc05-59c5-4ed4-b542-04e3f05efd2a", "metadata": {}, "outputs": [], "source": ["expected_columns = [\"receiver_ids\", \"template_id\", \"template_vars\", \"status\", \"update_ts_ist\"]\n", "\n", "if success_count > 0 and all_payloads and isinstance(all_payloads, list) and any(all_payloads):\n", "    df = pd.json_normalize(all_payloads, sep=\"_\", max_level=0)\n", "\n", "    df[\"receiver_ids\"] = (\n", "        df.get(\"receiver_ids\").apply(lambda x: x[0] if isinstance(x, list) and x else None)\n", "        if \"receiver_ids\" in df.columns\n", "        else None\n", "    )\n", "    df[\"receiver_ids\"] = pd.to_numeric(df[\"receiver_ids\"], errors=\"coerce\").astype(\"int\")\n", "\n", "    # Drop unneeded columns\n", "    df = df.drop(columns=[\"type\", \"delay\", \"expiry_ts\"], errors=\"ignore\")\n", "\n", "    # template_vars cleanup\n", "    df[\"template_vars\"] = (\n", "        df.get(\"template_vars\").apply(lambda x: x if isinstance(x, dict) and x else None)\n", "        if \"template_vars\" in df.columns\n", "        else None\n", "    )\n", "\n", "    # Add timestamp\n", "    current_timestamp = datetime.strptime(current_timestamp_str, \"%Y-%m-%d %H:%M:%S\")\n", "    df[\"update_ts_ist\"] = pd.to_datetime(current_timestamp)\n", "\n", "    # Add missing expected columns if any\n", "    for col in expected_columns:\n", "        if col not in df.columns:\n", "            df[col] = None\n", "\n", "    # Reorder columns\n", "    df = df[expected_columns]\n", "\n", "else:\n", "    print(\"No successful sends or payloads are empty, creating dummy DataFrame.\")\n", "    df = pd.DataFrame(columns=expected_columns)"]}, {"cell_type": "code", "execution_count": null, "id": "3b122b00-c1a5-4c12-b780-230af6e4b60a", "metadata": {}, "outputs": [], "source": ["if \"template_vars\" in df.columns:\n", "    df[\"template_vars\"] = df[\"template_vars\"].apply(\n", "        lambda x: json.dumps(x) if isinstance(x, dict) and x else None\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "f968bb7a-58d6-453c-aa7e-d1667b826f45", "metadata": {}, "outputs": [], "source": ["data_phone[\"phone\"] = data_phone[\"phone\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "af9a69c0-f895-4a0b-9575-8caae4359510", "metadata": {}, "outputs": [], "source": ["df[\"receiver_ids\"] = pd.to_numeric(df[\"receiver_ids\"], errors=\"coerce\").astype(\"int\")"]}, {"cell_type": "code", "execution_count": null, "id": "2a4178bd-b6dc-446e-a9ea-13dae356336d", "metadata": {}, "outputs": [], "source": ["df = pd.merge(\n", "    df,\n", "    data_phone,\n", "    left_on=[\"receiver_ids\"],\n", "    right_on=[\"phone\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0d6ffe66-979a-4a4a-b356-d548a32e62bc", "metadata": {}, "outputs": [], "source": ["df = df.drop(columns=[\"receiver_ids\", \"phone\"], errors=\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "cb03d376-ccf1-4cb8-a7a3-0cf90580e525", "metadata": {}, "outputs": [], "source": ["df = df[[\"phone_hashed\", \"template_id\", \"template_vars\", \"status\", \"update_ts_ist\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "3a59c1ca-ec18-4e3c-a910-ab0a7ea31f88", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"storeops_etls\",\n", "    \"table_name\": \"store_ops_whatsapp_communications_logs\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"phone_hashed\", \"type\": \"varchar\", \"description\": \"Phone_hashed\"},\n", "        {\"name\": \"template_id\", \"type\": \"varchar\", \"description\": \"template_id\"},\n", "        {\"name\": \"template_vars\", \"type\": \"varchar\", \"description\": \"template_variables\"},\n", "        {\"name\": \"status\", \"type\": \"varchar\", \"description\": \"status\"},\n", "        {\"name\": \"update_ts_ist\", \"type\": \"timestamp(6)\", \"description\": \"update_timestamp\"},\n", "    ],\n", "    \"primary_key\": [\"phone_hashed\", \"template_id\", \"update_ts_ist\"],\n", "    \"partition_key\": [\"update_ts_ist\"],\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"This Table has logs for whatsapp communications\",\n", "    \"force_upsert_without_increment_check\": True,\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "3f31367d-2de1-4042-9981-ad52d7d0b0e7", "metadata": {}, "outputs": [], "source": ["pb.to_trino(df, **kwargs_trino)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 5}