alert_configs:
  slack:
  - channel: bl-data-alerts-p2
dag_name: oncall_roster
dag_type: workflow
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: de
notebooks:
- alias: bl-data-platform-team
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages: []
    user_group:
      id: S03SA08N8JK
    user_mappings: []
    users:
    - fixed:
        emails:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      type: fixed
  retries: 3
  tag: parallel
- alias: bl-data-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: bl-data-support
      overrideText: "Please go through data.grofer.io/faq first. In case issue persists, click the `Raise Oncall Request`/`Raise PR Review Request` button bookmarked on this channel above to raise your request \nOperating Hours: 10AM - 6PM"
    user_group:
      id: S03TEACQKDW
    user_mappings: []
    users:
    - fixed:
        emails:
        - <EMAIL>
      type: fixed
    - pagerduty:
      - identifier: PVZWH2J
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bl-dp-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages: []
    user_group:
      id: S089D0GQRCJ
    user_mappings: []
    users:
    - pagerduty:
      - identifier: PFFLD4K
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bl-analytics-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages: []
    user_group:
      id: S03SA21QKJB
    user_mappings: []
    users:
    - fixed:
        emails:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      type: fixed
  retries: 3
  tag: parallel
- alias: bl-movement-devs-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: bl-transit-support
    user_group:
      id: S04KTUZG3L2
    user_mappings: []
    users:
    - fixed:
        emails:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      type: fixed
    - pagerduty:
      - identifier: P391RFO
        type: schedule_id
      - identifier: PM5NS2V
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bl-replenishment-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: bl-support-inventory-tech
    user_group:
      id: S03SJ4WQJF8
    user_mappings: []
    users:
    - fixed:
        emails:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      type: fixed
    - pagerduty:
      - identifier: PX7PPZ3
        type: schedule_id
      - identifier: PAGAQOT
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bl-infra-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: blinkit-sre-support
    user_group:
      id: S03T1N2MBE2
    user_mappings:
    - ops_email: <EMAIL>
      slack_email: <EMAIL>
    users:
    - fixed:
        emails:
        - <EMAIL>
      type: fixed
    - pagerduty:
      - identifier: PJTSQA3
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bl-inventory-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: inventory-service-support
    user_group:
      id: S04UKDAFN1X
    users:
    - fixed:
        emails:
        - <EMAIL>
      type: fixed
    - pagerduty:
      - identifier: PUC3HFL
        type: schedule_id
      - identifier: PR2FH3Y
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bl-search-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: bl-support-search-plp
    user_group:
      id: S03SJ4YG03G
    user_mappings:
    - ops_email: <EMAIL>
      slack_email: <EMAIL>
    - ops_email: <EMAIL>
      slack_email: <EMAIL>
    - ops_email: <EMAIL>
      slack_email: <EMAIL>
    - ops_email: <EMAIL>
      slack_email: <EMAIL>
    users:
    - fixed:
        emails:
        - <EMAIL>
      type: fixed
    - pagerduty:
      - identifier: PPKXBRX
        type: schedule_id
      - identifier: P9O9AC7
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bl-wms-sre-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: bl-warehouse-ci
    user_group:
      id: S03SZ9LC4F6
    user_mappings: []
    users:
    - fixed:
        emails:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      type: fixed
    - pagerduty:
      - identifier: PTQ95UA
        type: schedule_id
      - identifier: PPDL4FN
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bl-wms-inbound-dev-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: bl-warehouse-ci
    user_group:
      id: S05LNMQRXPF
    user_mappings: []
    users:
    - fixed:
        emails:
        - <EMAIL>
        - <EMAIL>
      type: fixed
    - pagerduty:
      - identifier: P2XW8D9
        type: schedule_id
      - identifier: PLTF90T
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bl-wms-picking-dev-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: bl-warehouse-ci
    user_group:
      id: S06E8K0U5UZ
    user_mappings: []
    users:
    - fixed:
        emails:
        - <EMAIL>
      type: fixed
    - pagerduty:
      - identifier: PHHU8O3
        type: schedule_id
      - identifier: PE867YD
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bl-wms-post-picking-dev-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: bl-warehouse-ci
    user_group:
      id: S06TE7D813L
    user_mappings: []
    users:
    - fixed:
        emails:
        - <EMAIL>
      type: fixed
    - pagerduty:
      - identifier: PAYI6JQ
        type: schedule_id
      - identifier: PWMX670
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bl-storeops-devoncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: bl-storeops-tech-v2
    user_group:
      id: S040X1LF63E
    user_mappings: []
    users:
    - pagerduty:
      - identifier: PCRSIHA
        type: schedule_id
      - identifier: P3BYEZU
        type: schedule_id
      type: pagerduty
    - fixed:
        emails:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      type: fixed
  retries: 3
  tag: parallel
- alias: bl-store-task-assignment-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: bl-storeops-tech-v2
    user_group:
      id: S050T1U3UBZ
    user_mappings: []
    users:
    - pagerduty:
      - identifier: PV5GHSK
        type: schedule_id
      - identifier: PRWVGDX
        type: schedule_id
      - identifier: PJOFHSI
        type: schedule_id
      - identifier: PFCUGDV
        type: schedule_id
      - identifier: PC36G15
        type: schedule_id
      - identifier: PYTIFOF
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bistro-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: bistro-alerts
    user_group:
      id: S07SXFR90HH
    user_mappings: []
    users:
    - pagerduty:
      - identifier: PYW71KR
        type: schedule_id
      - identifier: PUJJ181
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bl-cms-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: blinkit-support-cms
    user_group:
      id: S03T3AMLH1P
    user_mappings: []
    users:
    - pagerduty:
      - identifier: PAFUDVA
        type: schedule_id
      - identifier: PX66CGN
        type: schedule_id
      type: pagerduty
    - fixed:
        emails:
        - <EMAIL>
      type: fixed
  retries: 3
  tag: parallel
- alias: bl-cart-promo-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: bl-cart-support
    user_group:
      id: S041A4MGM0V
    user_mappings: []
    users:
    - fixed:
        emails:
        - <EMAIL>
        - <EMAIL>
      type: fixed
    - pagerduty:
      - identifier: P6H6FBI
        type: schedule_id
      - identifier: P37IEMV
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bl-payments-tech-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: bl-cart-support
    user_group:
      id: S03TA255CSD
    user_mappings: []
    users:
    - fixed:
        emails:
        - <EMAIL>
      type: fixed
    - pagerduty:
      - identifier: PHGOYVN
        type: schedule_id
      - identifier: PE70XFK
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bl-promo-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: blinkit-promo-oncall
    user_group:
      id: S03UB413GGH
    user_mappings: []
    users:
    - fixed:
        emails:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      type: fixed
    - pagerduty:
      - identifier: PEIVDHM
        type: schedule_id
      - identifier: PB97D5Z
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: blinkit-consumer-platform-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: bl-consumer-platform-support
    user_group:
      id: S06JNU7RKUZ
    user_mappings: []
    users:
    - fixed:
        emails:
        - <EMAIL>
      type: fixed
    - pagerduty:
      - identifier: PBAZT6M
        type: schedule_id
      - identifier: PY1OSUZ
        type: schedule_id
      - identifier: PUS0RF9
        type: schedule_id
      - identifier: PQFCR2J
        type: schedule_id
      - identifier: PFX0JK1
        type: schedule_id
      - identifier: PBKCJ7B
        type: schedule_id
      - identifier: PW9JQP5
        type: schedule_id
      - identifier: PSAYPZF
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bl-ad-tech-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages: []
    user_group:
      id: S0480MDFC9W
    user_mappings: []
    users:
    - fixed:
        emails:
        - <EMAIL>
        - <EMAIL>
      type: fixed
    - pagerduty:
      - identifier: PH4GST8
        type: schedule_id
      - identifier: PDVVRDI
        type: schedule_id
      - identifier: PZJ7R1V
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bl-pricing-tech-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages: []
    user_group:
      id: S040VPAHF52
    user_mappings: []
    users:
    - fixed:
        emails:
        - <EMAIL>
      type: fixed
    - pagerduty:
      - identifier: PZKQ0HO
        type: schedule_id
      - identifier: PWO204L
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bl-oms-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages: []
    user_group:
      id: S03SMNY8Q05
    user_mappings: []
    users:
    - fixed:
        emails:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      type: fixed
    - pagerduty:
      - identifier: PKFN2LR
        type: schedule_id
      - identifier: PH5M291
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bl-logistics-dev-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: bl-internal-oncall-e3
    user_group:
      id: S03SNEJNTD5
    user_mappings: []
    users:
    - pagerduty:
      - identifier: P1D8JB6
        type: schedule_id
      - identifier: PKZUJLQ
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bl-logistics-server-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: bl-internal-oncall-e3
    user_group:
      id: S081181EVFV
    user_mappings: []
    users:
    - pagerduty:
      - identifier: PO4MMTN
        type: schedule_id
      - identifier: P7VBLDK
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bl-serviceability-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages: []
    user_group:
      id: S03SMP3M5EZ
    user_mappings: []
    users:
    - fixed:
        emails:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      type: fixed
    - pagerduty:
      - identifier: P5VP3QX
        type: schedule_id
      - identifier: P1J12B7
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bl-cd-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: blinkit-cd-ops-tech-oncall
    user_group:
      id: S03SNFV3R47
    users:
    - fixed:
        emails:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      type: fixed
    - pagerduty:
      - identifier: PEJBQAF
        type: schedule_id
      - identifier: PBNQPLS
        type: schedule_id
      - identifier: P66H9SZ
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bl-app-exp-backend-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: bl-homepage-support
    user_group:
      id: S053WFNJU8Z
    user_mappings: []
    users:
    - pagerduty:
      - identifier: P7DU560
        type: schedule_id
      - identifier: P4464TA
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bl-personalisation-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: bl-personalization-support
    user_group:
      id: S05Q1PZ2P8U
    user_mappings: []
    users:
    - fixed:
        emails:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      type: fixed
    - pagerduty:
      - identifier: PFY6WCH
        type: schedule_id
      - identifier: PBLIW0U
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: post-checkout-dev-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: bl-support-pox
    user_group:
      id: S069711405T
    user_mappings: []
    users:
    - pagerduty:
      - identifier: P0UI3EN
        type: schedule_id
      - identifier: PJR441U
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bl-layout-platform-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: blinkit-studio-platform-support
    user_group:
      id: S07519YAYH4
    user_mappings: []
    users:
    - pagerduty:
        - identifier: PWLRWR7
          type: schedule_id
        - identifier: PTP3VBH
          type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bl-seller-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: bl-seller-tech-support
    user_group:
      id: S07DHCFBRCM
    user_mappings: []
    users:
    - fixed:
        emails:
        - <EMAIL>
      type: fixed
    - pagerduty:
      - identifier: P7MRLQJ
        type: schedule_id
      - identifier: P4Q3KBW
        type: schedule_id
      - identifier: P0DFJM6
        type: schedule_id
      - identifier: PJA1K9Q
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: storeops-analytics-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages: []
    user_group:
      id: S0809VCG8V9
    user_mappings: []
    users:
    - fixed:
        emails:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      type: fixed
  retries: 3
  tag: parallel
- alias: blinkit-ratings-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: ratings-and-reviews-support
    user_group:
      id: S088SLBCFK2
    user_mappings: []
    users:
    - opsgenie:
      - identifier: ratings-and-reviews_schedule
        type: schedule_name
      type: opsgenie
  retries: 3
  tag: parallel
- alias: bl-consumer-android-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: blinkit-droid-devs
    user_group:
      id: S03SQGBR11R
    user_mappings: []
    users:
    - fixed:
        emails:
        - <EMAIL>
        - <EMAIL>
      type: fixed
    - pagerduty:
      - identifier: PHW85UY
        type: schedule_id
      - identifier: PDJK4F8
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
- alias: bl-le-oncall
  executor_config:
    load_type: tiny
    node_type: spot
  name: oncall
  parameters:
    post_messages:
    - channel: bl-homepage-support
    user_group:
      id: S08FAVB4YE6
    user_mappings: []
    users:
    - pagerduty:
      - identifier: PNYLD7C
        type: schedule_id
      - identifier: P6MACVP
        type: schedule_id
      type: pagerduty
  retries: 3
  tag: parallel
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/misc/workflow/oncall_roster
paused: false
pool: de_pool
project_name: misc
schedule:
  end_date: '2025-12-31T00:00:00'
  interval: 30 */2 * * *
  start_date: '2022-08-12T00:00:00'
schedule_type: fixed
sla: 20 minutes
support_files:
- utils.py
- opsgenie_client.py
- user_proxy_client.py
- slack_client.py
- pagerduty_client.py
tags: []
template_name: multi_notebook
version: 1
concurrency: 3
