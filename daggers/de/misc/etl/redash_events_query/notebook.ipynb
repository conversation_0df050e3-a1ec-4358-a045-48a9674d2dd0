{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install pyarrow"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import json\n", "import datetime\n", "import uuid\n", "import boto3\n", "import os"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["now = datetime.datetime.today()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_max_id_from_spectrum(table_name):\n", "    print(\"Start - connecting redshift\")\n", "    connection_rs = pb.get_connection(\"redpen\")\n", "    connection_rs.autocommit = True\n", "    print(\"Fetching max ID...\")\n", "    query = pd.read_sql_query(\n", "        f\"SELECT max(id) as max from {table_name}\"\n", "        f\" where at_date_ist > CURRENT_DATE - interval '5 days'\",\n", "        connection_rs,\n", "    )\n", "    return query[\"max\"][0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fetch_events_data_from_redash(redash_connection, max_id):\n", "    print(\"Connecting redash\")\n", "    connection_md = redash_connection.connect()\n", "    connection_md.autocommit = True\n", "    print(\"Fetching data...\")\n", "    query = pd.read_sql_query(\n", "        f\"SELECT * from events where id > {max_id}\", connection_md\n", "    )\n", "    return pd.DataFrame(query)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def serialize_events_data(data_df):\n", "    columns = (\n", "        \"screen_resolution\",\n", "        \"file_type\",\n", "        \"ip\",\n", "        \"user_name\",\n", "        \"api_key\",\n", "        \"user_agent\",\n", "        \"query\",\n", "    )\n", "\n", "    for i in columns:\n", "        locals()[i] = []\n", "\n", "    for records in data_df[\"additional_properties\"]:\n", "        meta_data = json.loads(records)\n", "        for i in columns:\n", "            locals()[i].append(meta_data.get(i))\n", "\n", "    for i in columns:\n", "        data_df[i] = locals()[i]\n", "\n", "    data_df.drop([\"additional_properties\"], axis=1, inplace=True)\n", "    data_df = data_df.astype({'user_id':'float32'})\n", "    return data_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_parquet_file_from_df(data_df):\n", "    date = now.strftime(\"%Y%m%d\")\n", "    timestamp = now.strftime(\"%H%M%S\")\n", "    random_sequence = str(uuid.uuid1())\n", "\n", "    file_name = f\"{random_sequence}_{date}_{timestamp}.parquet\"\n", "    data_df.to_parquet(f\"{file_name}\", index=False)\n", "    print(\"File created\")\n", "    return file_name"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def upload_redash_events_to_s3(\n", "    redash_connection, spectrum_table_name, s3_bucket_name, s3_prefix\n", "):\n", "    max_id = get_max_id_from_spectrum(spectrum_table_name)\n", "    if not max_id:\n", "        max_id = 0\n", "    print(f\"Max ID: {max_id}\")\n", "    data_df = fetch_events_data_from_redash(redash_connection, max_id)\n", "    print(f\"Event Df : {data_df.shape}\")\n", "    data_df = serialize_events_data(data_df)\n", "    data_df[\"created_date\"] = data_df[\"created_at\"].dt.date\n", "\n", "    for created_date, date_df in data_df.groupby(\"created_date\"):\n", "        dt_string = created_date.strftime(\"%Y-%m-%d\")\n", "        date_df.drop(\"created_date\", axis=1, inplace=True)\n", "        file_name = generate_parquet_file_from_df(date_df)\n", "        s3_client = boto3.client(\"s3\")\n", "        write_path = f\"{s3_prefix}/at_date_ist={dt_string}/{file_name}\"\n", "        print(s3_bucket_name, write_path)\n", "        with open(f\"{file_name}\", \"rb\") as data:\n", "            s3_client.upload_fileobj(data, s3_bucket_name, write_path)\n", "            os.remove(file_name)\n", "        print(\"Successfully uploaded to S3\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Redash Reports - Writing to Archive Bucket\")\n", "upload_redash_events_to_s3(\n", "    redash_connection=pb.get_connection(\"redash_reports_pencilbox\"),\n", "    spectrum_table_name=\"spectrum.redash_reports_events\",\n", "    s3_bucket_name=\"prod-dse-spark-etl-archive-sgp\",\n", "    s3_prefix=\"redash-reports-events\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Redash Queries - Writing to Archive Bucket\")\n", "upload_redash_events_to_s3(\n", "    redash_connection=pb.get_connection(\"redash_queries\"),\n", "    spectrum_table_name=\"spectrum.redash_queries_events\",\n", "    s3_bucket_name=\"prod-dse-spark-etl-archive-sgp\",\n", "    s3_prefix=\"redash-queries-events\",\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}