{"cells": [{"cell_type": "raw", "id": "d5c73eb3-6d55-44d9-8aa3-edb4de948f4e", "metadata": {}, "source": ["WIP\n", "1. Write IRO only query"]}, {"cell_type": "code", "execution_count": null, "id": "04cda630-e028-4e70-b7c9-5c535d745b72", "metadata": {}, "outputs": [], "source": ["!pip install https://*****************************/ShamanJaggia/wheels/main/joolbox-0.10.3-py3-none-any.whl -q\n", "from joolbox import *\n", "\n", "dt = Cdt()\n", "pd.set_option(\"display.max_colwidth\", None)\n", "import json\n", "from pandas.io.json import json_normalize"]}, {"cell_type": "code", "execution_count": null, "id": "de6ca8b2-1c64-4e76-89a6-948b9a2d80b7", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "a50d1de9-671c-4763-94b5-5f4135ef3483", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["start_date = dt.today_x(2)\n", "# dt.today_x(10)\n", "# '2022-08-01'\n", "end_date = dt.today_x(1)\n", "st_dt = start_date.replace(\"-\", \"\")\n", "ed_dt = end_date.replace(\"-\", \"\")\n", "week_fix = \"0\"\n", "day_week_month = \"day\""]}, {"cell_type": "code", "execution_count": null, "id": "48690bee-f9e2-4f6a-8780-70d6ca71abcb", "metadata": {}, "outputs": [], "source": ["params = {\n", "    \"{{start_date}}\": start_date,\n", "    \"{{end_date}}\": end_date,\n", "    \"{{st_dt}}\": st_dt,\n", "    \"{{ed_dt}}\": ed_dt,\n", "    \"{{week_fix}}\": week_fix,\n", "    \"{{day_week_month}}\": day_week_month,\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "1bf1d4fb-6b8a-4553-9615-b851e9cd11a6", "metadata": {}, "outputs": [], "source": ["print(start_date, end_date)"]}, {"cell_type": "markdown", "id": "b230ee2e-8938-486c-8ef5-229d193cfd3f", "metadata": {}, "source": ["### REFUND"]}, {"cell_type": "code", "execution_count": null, "id": "576a3991-d789-46fc-b44d-289043b21f07", "metadata": {}, "outputs": [], "source": ["refunds = (\n", "    \"\"\"\n", "with delivered_orders AS (\n", "        SELECT DISTINCT\n", "            cart_checkout_ts_ist,\n", "            order_id,\n", "            suborder_id,\n", "            outlet_id,\n", "            cart_id\n", "\n", "        FROM\n", "            dwh.fact_sales_order_item_details \n", "\n", "        WHERE\n", "            cart_checkout_ts_ist >= '{{start_date}}' :: TIMESTAMP -  INTERVAL '30 DAY'\n", "            AND cart_checkout_ts_ist < '{{end_date}}' :: TIMESTAMP + INTERVAL '1 DAY'\n", "            -- AND order_type = 'RetailForwardOrder'\n", "            AND station_name NOT ILIKE '%b2b%'\n", "            AND order_current_status = 'DELIVERED'\n", "),\n", "\n", "refunds as (\n", "select    (gpr.install_ts + interval '5.5 hrs')::DATE as date_ist,\n", "          gpr.order_id,\n", "          gpr.cart_id,\n", "          ticket_id,\n", "          CASE WHEN source_id=5 and issue_type_id IN (18, 20) THEN 'dsr_refund'\n", "          ELSE 'non_dsr_refund' END as refund_type,\n", "           refund_amount,\n", "           sum(refund_amount) over(partition by gpr.cart_id) as total_refund,\n", "           refund_amount/total_refund as refund_ratio\n", "    \n", "    from  lake_grofers_db.gr_payment_refund gpr\n", "    JOIN dwh.fact_sales_order_details od ON gpr.order_id = od.order_id\n", "    LEFT JOIN lake_crm.view_crm_issue iss ON gpr.ticket_id = iss.id and gpr.cart_id = iss.cart_id\n", "     \n", "    where \n", "             gpr.install_ts >= '{{start_date}}'::TIMESTAMP - interval '5.5 hrs'\n", "            and gpr.install_ts < '{{end_date}}'::TIMESTAMP  - interval '5.5 hrs' + interval '1 day'\n", "            and gpr.status = 'SUCCESS'\n", "            and refund_reason not ilike '%%non_acceptable_payment%%'\n", "            and gpr.order_id is not null\n", "            AND station_name NOT ILIKE '%b2b%'\n", "            AND order_current_status = 'DELIVERED'\n", "            and refund_amount::float >0\n", "            -- and gpr.cart_id=224403675\n", "            \n", "    group by 1,2,3,4,5,6\n", "\n", "),\n", "\n", "\n", "procurement_diff as (\n", "select  \n", "               cart_id,\n", "               order_id,\n", "                sum(case when procured_quantity = 0 then unit_selling_price * product_quantity\n", "                      when procured_quantity <> 0 and procured_quantity < product_quantity then unit_selling_price* (product_quantity - procured_quantity) end) as procurement_diff\n", "              \n", "        from dwh.fact_sales_order_item_details od\n", "        where cart_checkout_ts_ist >='{{start_date}}'::TIMESTAMP - interval '30 day'\n", "        and cart_checkout_ts_ist < '{{end_date}}'::TIMESTAMP + interval '1 day'\n", "        and procured_quantity < product_quantity\n", "        and order_current_status = 'DELIVERED'\n", "        and station_name NOT ILIKE '%b2b%'\n", "        -- and cart_id in (select distinct cart_id from refunds)\n", "        -- and oid.cart_id = 247137009\n", "        group by 1,2\n", "),\n", "\n", "-- select sum(procurement_diff) from procurement_diff\n", "\n", "\n", "cashbacks AS (\n", "        SELECT\n", "            source_id AS order_id,\n", "            cart_id,\n", "\n", "            SUM(CASE WHEN entry = 'DEBIT' THEN -amount ELSE amount END) AS crm_cashback\n", "\n", "        FROM\n", "            lake_payments.external_transaction et\n", "        JOIN delivered_orders d ON d.order_id = et.source_id\n", "\n", "        WHERE\n", "            status IN ('SUCCESS')\n", "            \n", "            AND source = 'order'\n", "            AND reason IN (\n", "                 'COD_ORDER_REFUND', \n", "                'TOKEN_OF_APOLOGY', 'ORDER_RESCHEDULE'\n", "            )\n", "\n", "        GROUP BY\n", "            1,2\n", "    ),\n", "\n", "iro_carts as (\n", "select original_order_item_id,\n", "        sum(product_quantity) as exp_return_qty\n", "from dwh.fact_sales_order_item_details oid \n", "where order_type = 'InternalReverseOrder'\n", "    and  order_current_status = 'DELIVERED'\n", "    and  order_create_ts_ist >= '{{start_date}}'::TIMESTAMP - interval '30 day'\n", "    and  order_create_ts_ist < '{{end_date}}'::TIMESTAMP  + interval '1 day'\n", "group by 1\n", "),\n", "\n", "\n", "iro_exp as (\n", "select  \n", "        oid.cart_id,\n", "        oid.product_id,\n", "        sum(exp_return_qty * unit_weighted_landing_price) as iro_exp\n", "from dwh.fact_sales_order_item_details oid\n", "JOIN iro_carts ir ON oid.order_item_id = ir.original_order_item_id\n", "JOIN lake_oms_bifrost.oms_order_item ooi ON oid.order_item_id = ooi.id\n", "where order_create_ts_ist >= '{{start_date}}'::TIMESTAMP - interval '30 day'\n", "    and  order_create_ts_ist < '{{end_date}}'::TIMESTAMP  + interval '1 day'\n", "group by 1,2\n", "),\n", "\n", "mdnd_base as (\n", "select cart_id,group_disposition,count(group_disposition) over(partition by cart_id) cnt from consumer.cd_master_table\n", "where cart_id <> ''\n", "Group by 1,2\n", "),\n", "\n", "mdnd as (\n", "select distinct od.cart_id,\n", "       sum(total_weighted_landing_price) as mdnd_lp\n", "from mdnd_base b\n", "JOIN dwh.fact_sales_order_details od ON b.cart_id::float = od.cart_id::float\n", "where cart_checkout_ts_ist >= '{{start_date}}'::TIMESTAMP - interval '30 day'\n", "      and cart_checkout_ts_ist < '{{end_date}}'::TIMESTAMP + interval '1 day'\n", "      and order_current_status = 'DELIVERED'\n", "      and order_type not ilike '%%internal%%'\n", "      and group_disposition = 'MDND'\n", "      and cnt = 1\n", "group by 1\n", "),\n", "\n", "ratio_base as (\n", "select  OID.order_id,\n", "        OID.cart_id,\n", "        OID.product_id,\n", "        r.ticket_id,\n", "        total_selling_price,\n", "        sum(total_selling_price) over (partition by oid.cart_id,r.ticket_id) as cart_value,\n", "        sum(total_selling_price) over (partition by oid.cart_id,r.ticket_id,oid.product_id) / cart_value as ratio\n", "        \n", "    FROM lake_crm.view_crm_issue AS ISS\n", "    JOIN lake_crm.crm_issue_type AS IT ON ISS.issue_type_id=IT.id\n", "    JOIN lake_crm.crm_issue_item AS II ON ISS.id=II.issue_id\n", "    JOIN dwh.fact_sales_order_item_details AS OID ON OID.cart_id=ISS.cart_id and OID.order_item_id=II.order_item_id\n", "    JOIN refunds r ON oid.cart_id = r.cart_id and r.ticket_id = ISS.id\n", "    WHERE \n", "    iss.install_ts >= '{{start_date}}'::TIMESTAMP   - interval '5.5 hrs'  - interval '30 day' \n", "    and iss.install_ts < '{{end_date}}'::TIMESTAMP  - interval '5.5 hrs' + interval '1 day'\n", "    AND OID.order_current_status='DELIVERED'\n", "    and total_selling_price >0\n", "    group by 1,2,3,4,5\n", "),\n", "\n", "\n", "refunds_final as (\n", "select  date_ist,\n", "        t.order_id,\n", "        t.cart_id,\n", "        t.ticket_id,\n", "        rab.product_id,\n", "        refund_type,\n", "        rab.ratio,\n", "        refund_ratio,\n", "        case when iro.cart_id is not null then True else False end as iro_cart,\n", "        case when md.cart_id is not null and t.total_refund > mdnd_lp then True else False end as mdnd_cart,\n", "        coalesce(case when ratio is not null then refund_amount * ratio else refund_amount end,0) as total_refund,\n", "        coalesce(case when ratio is not null  then procurement_diff * ratio * refund_ratio else procurement_diff * refund_ratio  end,0) as procurement_quantity_diff,\n", "        coalesce(case when ratio is not null then crm_cashback * ratio * refund_ratio else crm_cashback * refund_ratio end,0) as crm_cashback,\n", "        coalesce(case when ratio is not null and mdnd_lp is not null and t.total_refund > mdnd_lp then mdnd_lp * ratio * refund_ratio\n", "                      when ratio is null and mdnd_lp is not null and t.total_refund > mdnd_lp then mdnd_lp * refund_ratio\n", "                      when  iro.cart_id is not null then iro_exp \n", "                      else 0 end,0) as expected_return\n", "\n", "from refunds t\n", "LEFT JOIN ratio_base rab on t.cart_id=rab.cart_id and t.ticket_id = rab.ticket_id\n", "LEFT JOIN procurement_diff pd on t.cart_id = pd.cart_id \n", "LEFT JOIN cashbacks cb ON t.cart_id = cb.cart_id\n", "LEFT JOIN mdnd md ON t.cart_id = md.cart_id\n", "LEFT JOIN iro_exp iro ON t.cart_id = iro.cart_id and rab.product_id = iro.product_id\n", "\n", "),\n", "\n", "outlet_base as (\n", "select order_id,\n", "       cart_checkout_ts_ist::DATE as cart_checkout_date,\n", "       cart_id,\n", "       outlet_id,\n", "       row_number() over(partition by cart_id order by order_id)::int as rn\n", "from dwh.fact_sales_order_details\n", "where cart_id in (select distinct cart_id from refunds)\n", "group by 1,2,3,4\n", "),\n", "\n", "outlet as (\n", "select \n", "      order_id,\n", "      cart_checkout_date,\n", "      case when outlet_id is null then LAG(outlet_id,rn-1) over(partition by cart_id order by order_id asc) else outlet_id end as outlet_id\n", "from outlet_base\n", "\n", "),\n", "\n", "final as (\n", "select  date_trunc('day',date_ist)::DATE as refund_date,\n", "        cart_checkout_date,\n", "        outlet_id,\n", "        r.order_id,\n", "        r.cart_id,\n", "        r.product_id,\n", "        --ratio,\n", "        --item_id,\n", "        ticket_id,\n", "        refund_type,\n", "        iro_cart,\n", "        mdnd_cart,\n", "        total_refund,\n", "        crm_cashback,\n", "        case when (total_refund + crm_cashback < procurement_quantity_diff) then total_refund + crm_cashback else procurement_quantity_diff end as procurement_qty_diff,\n", "        total_refund + crm_cashback - procurement_qty_diff  as refund_incl_cashback_prc,\n", "        case when refund_incl_cashback_prc < expected_return then refund_incl_cashback_prc else expected_return end as exp_return, \n", "        refund_incl_cashback_prc - exp_return as net_refund\n", "from refunds_final r\n", "LEFT JOIN outlet od ON r.order_id = od.order_id\n", ")\n", "\n", "select *\n", "\n", "-- sum(procurement_qty_diff),sum(crm_cashback),sum(net_refund),sum(total_refund),sum(exp_return),sum(unaccounted_returns)\n", "from final \n", "where total_refund>0.5\n", "\n", "\"\"\".replace(\n", "        \"%\", \"%%\"\n", "    )\n", "    .replace(\"{{\", \"{\")\n", "    .replace(\"}}\", \"}\")\n", ")\n", "\n", "\n", "arf = pd.read_sql(\n", "    refunds.format(\n", "        start_date=start_date, end_date=end_date, day_week_month=day_week_month\n", "    ),\n", "    con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "********-ac27-4b5a-85ee-95d609d88708", "metadata": {}, "outputs": [], "source": ["arf.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "ef7651e2-3d0c-43f0-9ccc-3308d7e4466a", "metadata": {}, "outputs": [], "source": ["arf[\"total_refund\"].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "4fbf2e82-dbfa-430c-860b-15fcfaa68028", "metadata": {}, "outputs": [], "source": ["arf[\"exp_return\"].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "3679be40-0f44-49d2-a372-73b8cdf0293e", "metadata": {}, "outputs": [], "source": ["arf[arf[\"cart_id\"] == 230962689]"]}, {"cell_type": "code", "execution_count": null, "id": "5c7bf011-23c1-432d-bff0-7bcd325c71e9", "metadata": {}, "outputs": [], "source": ["arf[arf[\"outlet_id\"].isna()].shape"]}, {"cell_type": "code", "execution_count": null, "id": "b9510b49-c264-41f6-8673-6606e2d8e07d", "metadata": {}, "outputs": [], "source": ["arf = arf[~arf[\"outlet_id\"].isna()]"]}, {"cell_type": "code", "execution_count": null, "id": "d57be4d6-0e40-4418-bc2f-d85ee9100325", "metadata": {}, "outputs": [], "source": ["# arf.to_csv('data_dump2/refunds4 ' +start_date+  ' to '+end_date+ '.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "2493eaa9-d275-45b3-bebd-6e083a4ccae9", "metadata": {}, "outputs": [], "source": ["refund_carts = tuple(arf.cart_id.unique())"]}, {"cell_type": "markdown", "id": "c26f93a4-fd63-4abc-9cb8-6cad7d56d65c", "metadata": {}, "source": ["### Doorstep"]}, {"cell_type": "code", "execution_count": null, "id": "fa240814-5904-4536-9970-f9a0798dcf04", "metadata": {}, "outputs": [], "source": ["q1 = (\n", "    f\"\"\"\n", "\n", "    select outlet_id,\n", "           cart_id,\n", "           product_id,\n", "           sum(total_doorstep_return_quantity * unit_weighted_landing_price) as exp_return_lp_doorstep\n", "    from dwh.fact_sales_order_item_details\n", "    where cart_id in {refund_carts}\n", "    and cart_checkout_ts_ist >= '{{start_date}}'::TIMESTAMP - interval '30 day'\n", "    and cart_checkout_ts_ist < '{{end_date}}'::TIMESTAMP +  interval '1 day'\n", "    \n", "    group by 1,2,3\n", "\n", "                 \n", "\n", "\"\"\".replace(\n", "        \"%\", \"%%\"\n", "    )\n", "    .replace(\"{{\", \"{\")\n", "    .replace(\"}}\", \"}\")\n", ")\n", "\n", "\n", "d1 = pd.read_sql(\n", "    q1.format(start_date=start_date, end_date=end_date, day_week_month=day_week_month),\n", "    con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e6f1f007-009c-4711-a3b5-98b72f01c8f0", "metadata": {}, "outputs": [], "source": ["d1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "30975877-d9ac-4b45-a1fa-92e174ace989", "metadata": {}, "outputs": [], "source": ["d1.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "eac4cb1f-4ef0-491a-bc8d-2d0dc04a5ef3", "metadata": {}, "outputs": [], "source": ["d1 = d1[d1[\"exp_return_lp_doorstep\"] > 0]"]}, {"cell_type": "code", "execution_count": null, "id": "fe6f5fcb-e56a-4809-937e-d132133e3b08", "metadata": {}, "outputs": [], "source": ["d1.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "ffa28968-7a0b-4a73-b20f-ec36dd7e40e1", "metadata": {}, "outputs": [], "source": ["d1.exp_return_lp_doorstep.sum()"]}, {"cell_type": "code", "execution_count": null, "id": "a853468b-60b2-463f-a658-dbc20e003e57", "metadata": {}, "outputs": [], "source": ["doorstep = d1.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "2217631d-c6ed-46f6-bbd5-fea12f83a10f", "metadata": {}, "outputs": [], "source": ["# doorstep.to_csv('data_dump2/expected_lp_df4 ' +start_date+  ' to '+end_date+ '.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "2235f17e-6c08-48e2-9d20-cf39a93920b3", "metadata": {}, "outputs": [], "source": ["# doorstep_agg = pd.pivot_table(doorstep,index=[day_week_month,'outlet_id','order_id','payment_method'],values=['exp_ret_qty','exp_return_lp'],aggfunc=np.sum).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "fde3b1f7-fc18-42bb-84ad-39ff4a33fd0a", "metadata": {}, "outputs": [], "source": ["# doorstep_agg.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "3f7bbfd0-0d0c-45e1-b427-a42c19cace1c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "84948601-ed7b-4b81-86e0-789f4e534976", "metadata": {}, "source": ["## MANIPULATIONS"]}, {"cell_type": "code", "execution_count": null, "id": "8a69fd69-2460-4559-9f1e-9d012f142839", "metadata": {}, "outputs": [], "source": ["# refunds\n", "rf1 = arf.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "4c258017-063e-4653-94f5-5daefeb66ad1", "metadata": {}, "outputs": [], "source": ["# doorstep returns\n", "rf2 = doorstep.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "f61bdc8b-b0b9-4dae-825c-6a7358b7196a", "metadata": {}, "outputs": [], "source": ["rf1.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "66fae877-cc8b-47f6-9973-9d7c9fca0efd", "metadata": {}, "outputs": [], "source": ["rf2.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "0de450de-88d4-4808-8a8e-b3cb231b23e1", "metadata": {}, "outputs": [], "source": ["rf1[rf1[\"outlet_id\"].isna()].shape"]}, {"cell_type": "code", "execution_count": null, "id": "ac9094fb-df8a-44e7-95f9-b81c9d779076", "metadata": {}, "outputs": [], "source": ["rf2.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "a8babc3e-ad61-4c8e-a2eb-66bc337c10a2", "metadata": {}, "outputs": [], "source": ["rf1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "fdb6d21f-6451-406b-9438-1d7ad5d85a4f", "metadata": {}, "outputs": [], "source": ["rf2[rf2[\"cart_id\"] == 264579863]"]}, {"cell_type": "code", "execution_count": null, "id": "1e95eb83-85b6-4576-9ed5-0b62ed5449c0", "metadata": {}, "outputs": [], "source": ["rf1[\"product_id\"] = rf1[\"product_id\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "800d4d08-5ab4-4f7d-a0d9-34161dcff884", "metadata": {}, "outputs": [], "source": ["rf1 = rf1[rf1[\"total_refund\"] > 0]"]}, {"cell_type": "code", "execution_count": null, "id": "c2a9631e-93e7-41e2-a478-707324083780", "metadata": {}, "outputs": [], "source": ["rf1.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "97e62881-b84f-4634-9045-d92328924aba", "metadata": {}, "outputs": [], "source": ["rf1 = rf1.astype({\"outlet_id\": int, \"product_id\": int, \"cart_id\": int})"]}, {"cell_type": "code", "execution_count": null, "id": "a403f1dc-8fb8-4c1c-b143-405f3d8ca311", "metadata": {}, "outputs": [], "source": ["rf2 = rf2.astype({\"outlet_id\": int, \"product_id\": int, \"cart_id\": int})"]}, {"cell_type": "code", "execution_count": null, "id": "704660e4-e36a-4469-a4d7-6b631349cd1b", "metadata": {}, "outputs": [], "source": ["# LEFT MERGE ADDITIONAL REFUNDS AND ADDITIONA RETURNS\n", "df_refund = rf1.merge(rf2, on=[\"outlet_id\", \"cart_id\", \"product_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "4f557927-b165-4f00-adc8-f6424861b21f", "metadata": {}, "outputs": [], "source": ["df_refund.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "9ac5f1cd-e4fe-461a-977d-0cb4c5c4e7c4", "metadata": {}, "outputs": [], "source": ["df_refund[\"exp_return_lp_doorstep\"] = df_refund[\"exp_return_lp_doorstep\"].fillna(0)\n", "df_refund[\"exp_return\"] = df_refund[\"exp_return\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "9ba54a49-f707-4156-b45a-29db3dc46ff0", "metadata": {}, "outputs": [], "source": ["df_refund.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "3e9f2b3a-1772-46f6-abc2-2080c2763084", "metadata": {}, "outputs": [], "source": ["df_refund[\"mdnd_cart\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "2a16286a-5f5c-4fd9-8c51-05c2b3ca7643", "metadata": {}, "outputs": [], "source": ["df_refund[\"exp_return\"] = df_refund.apply(\n", "    lambda x: x[\"exp_return_lp_doorstep\"]\n", "    if (\n", "        (x[\"refund_type\"] == \"dsr_refund\")\n", "        & (x[\"exp_return_lp_doorstep\"] > x[\"exp_return\"])\n", "        & (x[\"exp_return_lp_doorstep\"] <= x[\"refund_incl_cashback_prc\"])\n", "    )\n", "    else x[\"refund_incl_cashback_prc\"]\n", "    if (\n", "        (x[\"refund_type\"] == \"dsr_refund\")\n", "        & (x[\"exp_return_lp_doorstep\"] > x[\"refund_incl_cashback_prc\"])\n", "    )\n", "    else x[\"exp_return\"],\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "50c5efac-c04d-4ce4-9868-fa470046ef6e", "metadata": {}, "outputs": [], "source": ["df_refund.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "43a4ff9b-b386-4fbe-9f22-7673586ce5c4", "metadata": {}, "outputs": [], "source": ["df_refund = df_refund.drop(columns=[\"exp_return_lp_doorstep\"])"]}, {"cell_type": "code", "execution_count": null, "id": "6c238bfc-fc85-4a3e-a357-05b28dc10a30", "metadata": {}, "outputs": [], "source": ["df_refund[\"RM_loss\"] = df_refund.apply(\n", "    lambda x: x[\"refund_incl_cashback_prc\"] - x[\"exp_return\"]\n", "    if ((x[\"exp_return\"] < x[\"refund_incl_cashback_prc\"]) & (x[\"exp_return\"] > 0))\n", "    else 0,\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ca72c446-8468-4343-a92b-6b47513de2a7", "metadata": {}, "outputs": [], "source": ["df_refund[\"net_refund\"] = df_refund.apply(\n", "    lambda x: x[\"RM_loss\"] if x[\"exp_return\"] > 0 else x[\"refund_incl_cashback_prc\"],\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "390150a9-5d40-4073-931a-deff7cc88467", "metadata": {}, "outputs": [], "source": ["# df_refund['mdnd_flag']= df_refund.apply(lambda x:  x['mdnd_flag'] if x['mdnd_flag'] == True  else False,axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "f067da96-c432-4d3a-87c7-a4f57e112702", "metadata": {}, "outputs": [], "source": ["df_refund[\"record_created_ts_ist\"] = datetime.now(timezone(\"Asia/Kolkata\")).strftime(\n", "    \"%Y-%m-%d %H:%M:%S\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "65cacb56-6bcb-41cb-b5bc-0a515882ac21", "metadata": {}, "outputs": [], "source": ["# df_refund['unaccounted_returns'].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "dcba4cba-241b-414c-9dc7-e8d0bad6da9a", "metadata": {}, "outputs": [], "source": ["df_refund[\"net_refund\"].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "3ab38570-c10c-4ed7-b0a8-eeba1773ab38", "metadata": {}, "outputs": [], "source": ["df_refund[\"refund_incl_cashback_prc\"].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "913eda6c-8508-40cf-8b63-08624a4ddac9", "metadata": {}, "outputs": [], "source": ["df_refund.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "********-941a-4c0b-b5d5-a34643b396e5", "metadata": {}, "outputs": [], "source": ["# df_refund_agg = pd.pivot_table(df_refund,index=['refund_date','outlet_id','refund_type','record_created_ts_ist'],values=['total_refund','crm_cashback',\n", "#                           'procurement_qty_diff','refund_incl_cashback_prc',  'exp_return',\n", "#                          'net_refund','RM_loss'],aggfunc=np.sum).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "49f85da5-6830-4c39-bd33-e028b9d5fcce", "metadata": {}, "outputs": [], "source": ["# cols = ['refund_date','outlet_id','refund_type','total_refund','crm_cashback','procurement_qty_diff','refund_incl_cashback_prc',  'exp_return',\n", "#                          'RM_loss','net_refund','record_created_ts_ist']"]}, {"cell_type": "code", "execution_count": null, "id": "305faa00-1658-4a16-ac46-37d65da82f47", "metadata": {}, "outputs": [], "source": ["primary_key = [\"refund_date\", \"outlet_id\", \"order_id\", \"product_id\"]\n", "sort_key = [\"refund_date\", \"outlet_id\", \"order_id\", \"cart_id\"]"]}, {"cell_type": "code", "execution_count": null, "id": "93144e7b-d95e-4bba-ba82-dbb67be240e6", "metadata": {}, "outputs": [], "source": ["table_name = \"rlpo_refunds_v4\"\n", "schema_name = \"consumer\"\n", "column_dtypes = [\n", "    {\n", "        \"name\": \"refund_date\",\n", "        \"type\": \"timestamp\",\n", "        \"description\": \"date\",\n", "    },\n", "    {\n", "        \"name\": \"cart_checkout_date\",\n", "        \"type\": \"timestamp\",\n", "        \"description\": \"cart checkout date\",\n", "    },\n", "    {\n", "        \"name\": \"outlet_id\",\n", "        \"type\": \"BIGINT\",\n", "        \"description\": \"outlet id\",\n", "    },\n", "    {\n", "        \"name\": \"order_id\",\n", "        \"type\": \"BIGINT\",\n", "        \"description\": \"order id\",\n", "    },\n", "    {\n", "        \"name\": \"cart_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"cart id\",\n", "    },\n", "    {\n", "        \"name\": \"product_id\",\n", "        \"type\": \"BIGINT\",\n", "        \"description\": \"product id\",\n", "    },\n", "    {\n", "        \"name\": \"ticket_id\",\n", "        \"type\": \"VARCHAR(256)\",\n", "        \"description\": \"refund ticket id\",\n", "    },\n", "    {\n", "        \"name\": \"refund_type\",\n", "        \"type\": \"VARCHAR(256)\",\n", "        \"description\": \"refund type vis-a-vis doorstep and non- doorstep\",\n", "    },\n", "    {\n", "        \"name\": \"iro_cart\",\n", "        \"type\": \"boolean\",\n", "        \"description\": \"whether i<PERSON> was created for this order or not\",\n", "    },\n", "    {\n", "        \"name\": \"mdnd_cart\",\n", "        \"type\": \"boolean\",\n", "        \"description\": \"whether cart was mdnd\",\n", "    },\n", "    {\n", "        \"name\": \"total_refund\",\n", "        \"type\": \"float\",\n", "        \"description\": \"total refund\",\n", "    },\n", "    {\n", "        \"name\": \"crm_cashback\",\n", "        \"type\": \"float\",\n", "        \"description\": \"crm cashback\",\n", "    },\n", "    {\n", "        \"name\": \"procurement_qty_diff\",\n", "        \"type\": \"float\",\n", "        \"description\": \"procurement qty diff\",\n", "    },\n", "    {\n", "        \"name\": \"refund_incl_cashback_prc\",\n", "        \"type\": \"float\",\n", "        \"description\": \"refund incl cashback_prc\",\n", "    },\n", "    {\n", "        \"name\": \"exp_return\",\n", "        \"type\": \"float\",\n", "        \"description\": \"exp_return\",\n", "    },\n", "    {\n", "        \"name\": \"net_refund\",\n", "        \"type\": \"float\",\n", "        \"description\": \"net refund\",\n", "    },\n", "    {\n", "        \"name\": \"RM_loss\",\n", "        \"type\": \"float\",\n", "        \"description\": \"RM loss\",\n", "    },\n", "    {\n", "        \"name\": \"record_created_ts_ist\",\n", "        \"type\": \"timestamp\",\n", "        \"description\": \"record_created_ts_ist\",\n", "    },\n", "]\n", "table_description = \"rlpo refunds \""]}, {"cell_type": "code", "execution_count": null, "id": "8bfdf8e9-3606-4cbc-882d-ee890bc22413", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": schema_name,  # Redshift schema name\n", "    \"table_name\": table_name,  # Redshift table name\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": primary_key,  # list\n", "    \"sortkey\": sort_key,  # list\n", "    \"incremental_key\": [],  # string\n", "    \"force_upsert_without_increment_check\": True,  # string\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": table_description,  # Description of the table being sent to redshift\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "366b2620-2073-443d-acd2-3891a0205708", "metadata": {}, "outputs": [], "source": ["pb.to_redshift(df_refund, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "8759418c-565b-4923-ad90-94d88a0fa7a5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ee17eb59-2f51-414c-bdc3-ec104a707221", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6a5bf030-8824-4f11-aac0-474a7f7ef8f3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}