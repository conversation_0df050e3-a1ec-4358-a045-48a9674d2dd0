alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: refunds_static
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 8G
      request: 4G
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: de
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03SCGRB152
path: de/misc/etl/refunds_static
paused: true
project_name: misc
schedule:
  interval: 0 1 * * *
  start_date: '2022-11-14T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
