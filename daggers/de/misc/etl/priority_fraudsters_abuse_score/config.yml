dag_name: priority_fraudsters_abuse_score
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: de
notebooks:
- alias: fraud
  name: update_fraudsters_score
  parameters: null
  tag: group_01
- alias: abuse
  name: update_customer_abuse_score
  parameters: null
  tag: group_02
- alias: priority
  name: update_customer_priority_score
  parameters: null
  tag: group_03
owner:
  email: <EMAIL>
  slack_id: UJEJWKTE2
path: de/misc/etl/priority_fraudsters_abuse_score
paused: true
project_name: misc
schedule:
  interval: 0 7,15,23 * * *
  start_date: '2021-04-23T00:00:00'
schedule_type: fixed
sla: 120 minutes
slack_alert_configs:
- channel: bl-data-airflow-alerts
support_files: []
tags: []
template_name: multi_notebook
version: 1
