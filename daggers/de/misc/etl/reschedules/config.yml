template_name: sql
dag_name: reschedules
version: 1
owner:
  name: deepu
  slack_id: U03RF701ZQV

schedule:
  start_date: '2019-10-01T00:00:00'
  interval: '0 */12 * * *'

source:
  conn_type: postgres
  conn_id: redshift_consumer
  # https://airflow.apache.org/macros.html
  # you can define your own parameters
  infer:
    parameters:
      where_condition: " = '2019-06-24' "
  bulk:
    parameters:
      where_condition: " >= '2019-05-30' "
  incremental:
    parameters:
      where_condition: " BETWEEN '{{ macros.ds_add(ds, -1) }}' AND '{{ ds }}' "
sink:
  conn_type: postgres
  conn_id: redshift_consumer
  schema: metrics
  table: reschedules
  primary_key: [delivery_date, city, station, delivery_slot, employee_id]
  incremental_key: 'delivery_date'
  sortkey: [delivery_date, city, station, employee_id]
  load_type: upsert
  column_dtypes:
  - name: city
    type: VARCHAR
  - name: station
    type: VARCHAR
  - name: delivery_date
    type: TIM<PERSON><PERSON><PERSON> WITH TIME ZONE
  - name: delivery_slot_type
    type: VARCHAR
  - name: delivery_slot
    type: VARCHAR
  - name: employee_id
    type: VARCHAR
  - name: fe_category
    type: VARCHAR
  - name: fe_name
    type: VARCHAR
  - name: total_orders
    type: INTEGER
  - name: rescheduled_orders
    type: INTEGER
  - name: customer_culprit
    type: INTEGER
  - name: crm_culprit
    type: INTEGER
  - name: merchant_culprit
    type: INTEGER
  - name: tech_culprit
    type: INTEGER
  - name: delivery_culprit
    type: INTEGER

dag_type: etl
escalation_priority: low
schedule_type: fixed
sla: 120 minutes
support_files: []
namespace: de
project_name: misc
paused: true
path: de/misc/etl/reschedules
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
tags: []
executor:
  type: kubernetes
  config:
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    node_selectors:
      nodetype: spot
    tolerations:
    - key: service
      operator: Equal
      value: airflow
      effect: NoSchedule
    cpu:
      request: 0.5
      limit: 1
    memory:
      request: 500M
      limit: 4G
    volume_mounts:
    - name: airflow-dags
      subPath: airflow/plugins
      mountPath: /usr/local/airflow/plugins
    - name: airflow-dags
      subPath: tmp
      mountPath: /usr/local/airflow/tmp
