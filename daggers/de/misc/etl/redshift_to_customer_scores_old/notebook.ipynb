{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import psycopg2\n", "import sqlalchemy\n", "from sqlalchemy.engine.url import URL"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table_name = \"customer_abuse_score\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_customer_scores_connection():\n", "    customer_scores_conn_params = pb.get_secret(\"dse/customer_scores\")\n", "    engine_url = customer_scores_conn_params[\"DATABASE_URI\"]\n", "    engine = sqlalchemy.create_engine(engine_url, pool_size=1, echo=False)\n", "    return engine"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["engine = get_customer_scores_connection()\n", "connection = engine.connect()\n", "max_update_ts = pd.read_sql(\n", "    sql=f\"select max(created_at_ist) as max_ts from {table_name}\", con=connection\n", ")[\"max_ts\"][0]\n", "connection.execute(f\"Drop table if exists {table_name}_temp\")\n", "connection.close()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["incremental_data_sql = f\"\"\"\n", "with customer_score_cte as( select  grofers_id,customer_id,phone,last_active,total_carts,cod_carts,online_carts,carts_co, \n", "infected_carts,total_cost,aov,cust_age,order_freq,td_churn_prob,net_cost,loss_amount, \n", "loss_frac,wallet_amount,pid,product_id,fnv,non_fnv,priority,merchant_blocked,user_blocked,is_loyal,\n", "pna_cart,pna_id,ipc,case1,case2,case3,case4,case5,segment,score,created_at_ist,city,final_action, \n", "abuse_score_merge,final_score, \n", "row_number() over (partition by customer_id order by created_at_ist desc ) as rnk \n", "from customer_abuse_score\n", "WHERE created_at_ist>'{max_update_ts}' )\n", "\n", "select grofers_id,customer_id,phone,last_active,total_carts,cod_carts,online_carts,carts_co, \n", "infected_carts,total_cost,aov,cust_age,order_freq,td_churn_prob,net_cost,loss_amount, \n", "loss_frac,wallet_amount,pid,product_id,fnv,non_fnv,priority,merchant_blocked,user_blocked,is_loyal,\n", "pna_cart,pna_id,ipc,case1,case2,case3,case4,case5,segment,score,created_at_ist,city,final_action, \n", "abuse_score_merge,final_score from customer_score_cte where rnk=1\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.read_sql(sql=incremental_data_sql, con=pb.get_connection(\"redpen\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["engine = get_customer_scores_connection()\n", "connection = engine.connect()\n", "connection.execute(f\"Create table {table_name}_temp (like {table_name})\")\n", "df.to_sql(\n", "    name=f\"{table_name}_temp\",\n", "    con=connection,\n", "    if_exists=\"append\",\n", "    index=False,\n", "    chunksize=50000,\n", ")\n", "connection.close()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def redshift_to_customer_scores(table_name, incremental_data_sql):\n", "    engine = get_customer_scores_connection()\n", "    with engine.begin() as connection:\n", "        connection.execute(\n", "            f\"Delete from {table_name} main Using {table_name}_temp where main.customer_id={table_name}_temp.customer_id\"\n", "        )\n", "        connection.execute(\n", "            f\"Insert into {table_name} (select grofers_id,customer_id,phone,last_active,total_carts,cod_carts,online_carts,carts_co,infected_carts,total_cost,aov,cust_age,order_freq,td_churn_prob,net_cost,loss_amount,loss_frac,wallet_amount,pid,product_id,fnv,non_fnv,priority,merchant_blocked,user_blocked,is_loyal,pna_cart,pna_id,ipc,case1,case2,case3,case4,case5,segment,score,created_at_ist,city,final_action,abuse_score_merge,cast(final_score as DOUBLE PRECISION) as final_score from {table_name}_temp)\"\n", "        )\n", "        connection.execute(f\"Drop table if exists {table_name}_temp\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_to_customer_scores(\n", "    table_name=table_name, incremental_data_sql=incremental_data_sql\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}