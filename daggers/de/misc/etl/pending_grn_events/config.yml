alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: pending_grn_events
dag_type: etl
escalation_priority: low
execution_timeout: 45
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: airflow-dags-spot
    service_account_name: blinkit-prep-airflow-prep-prep-eks-role
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: de
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03S2CRJG2Z
path: de/misc/etl/pending_grn_events
paused: false
pool: de_pool
project_name: misc
schedule:
  end_date: '2025-01-08T00:00:00'
  interval: '*/15 * * * *'
  start_date: '2024-10-10T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
