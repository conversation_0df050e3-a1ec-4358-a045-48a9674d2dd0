{"cells": [{"cell_type": "markdown", "id": "d113a53c-1479-4b46-9174-d42e3ba3885d", "metadata": {}, "source": ["### Libraries"]}, {"cell_type": "code", "execution_count": null, "id": "4b1605b5-1ce2-4418-be4a-c7d1d20fb99d", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "from datetime import datetime, timezone"]}, {"cell_type": "code", "execution_count": null, "id": "ce6dc420-368c-40f2-b0fc-53776b83ab0e", "metadata": {}, "outputs": [], "source": ["trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "markdown", "id": "a22eb7a7-179e-41d6-be07-8d0b022be8f1", "metadata": {}, "source": ["### Initializations"]}, {"cell_type": "code", "execution_count": null, "id": "69db14d6-bbdb-415e-b936-1f890aeb9de8", "metadata": {}, "outputs": [], "source": ["kafka_conn_id = \"[Kafka] prod-data-corestr\"\n", "kafka_sink_topic = \"corestreams.nexus.pending_grn_events_v3\"\n", "checkpoint_table_schema = \"de_etls\"\n", "checkpoint_table_name = \"pending_grn_events_checkpoint\"\n", "checkpoint_table_kwargs = {\n", "    \"schema_name\": checkpoint_table_schema,\n", "    \"table_name\": checkpoint_table_name,\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"event_name\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"event name which checkpoint time is being captured\",\n", "        },\n", "        {\n", "            \"name\": \"latest_checkpoint_ts\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"Latest checkpoint timestamp (UTC)\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"event_name\"],\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"This table stores the time till when data has been processed for each of the consignment events (specifically for the pending GRN events)\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "23bfb1e9-c58a-4d6b-9d25-e7fe1657de22", "metadata": {}, "outputs": [], "source": ["current_time_utc = datetime.now(timezone.utc)\n", "current_time_utc_str = current_time_utc.strftime(\"%Y-%m-%dT%H:%M:%S.%fZ\")\n", "checkpoint_update_sql = f\"SELECT '{{}}' AS event_name, from_iso8601_timestamp('{current_time_utc_str}') AS latest_checkpoint_ts\"\n", "current_time_utc_str"]}, {"cell_type": "code", "execution_count": null, "id": "6197ff79-3315-47a4-9374-7aa9214f8ec2", "metadata": {}, "outputs": [], "source": ["CONSIGNMENT_DISPTACHED_EVENT_NAME = \"complete-trip-loading\"\n", "PUTAWAY_COMPLETED_EVENT_NAME = \"CONTAINER_PUTAWAY_COMPLETED\"\n", "GRN_COMPLETED_EVENT_NAME = \"GRN_COMPLETED\"\n", "BILLED_TO_DISPATCH_MAX_HOURS = 6"]}, {"cell_type": "markdown", "id": "b36a5b30-f3b6-492c-9c37-650e287b897b", "metadata": {}, "source": ["### Process dispatched event data (consignment-item level)"]}, {"cell_type": "code", "execution_count": null, "id": "ff49cbad-5686-4697-81cb-ae6bb58ee931", "metadata": {}, "outputs": [], "source": ["dispatched_containers_sql = f\"\"\"\n", "WITH\n", "dispatched_containers AS (\n", "    SELECT\n", "        consignment_id || '#' || container_id AS consignment_container_id\n", "    FROM\n", "        lake_events.wms_consignment_dispatch_events\n", "    WHERE\n", "        at_date BETWEEN CURRENT_DATE - INTERVAL '1' DAY AND CURRENT_DATE\n", "        AND from_iso8601_timestamp(arroyo_ingestion_ts) >= (\n", "            SELECT latest_checkpoint_ts FROM {checkpoint_table_schema}.{checkpoint_table_name} WHERE event_name = '{CONSIGNMENT_DISPTACHED_EVENT_NAME}'\n", "        ) \n", "        AND from_iso8601_timestamp(arroyo_ingestion_ts) < from_iso8601_timestamp('{current_time_utc_str}')\n", "),\n", "\n", "dispatched_container_details AS (\n", "    SELECT\n", "        consignment_id,\n", "        site_id,\n", "        item_id,\n", "        DATE_FORMAT(CURRENT_TIMESTAMP AT TIME ZONE 'UTC', '%%Y-%%m-%%d %%H:%%i:%%s') AS dispatch_etl_ts,\n", "        SUM(quantity) AS dispatch_quantity   -- will also include the MISSING crate case automatically\n", "    FROM\n", "        lake_events.wms_consignment_billing_events\n", "    WHERE\n", "        at_date BETWEEN CURRENT_DATE - INTERVAL '2' DAY AND CURRENT_DATE\n", "        AND from_iso8601_timestamp(arroyo_ingestion_ts) >= (\n", "            SELECT latest_checkpoint_ts FROM {checkpoint_table_schema}.{checkpoint_table_name} WHERE event_name = '{CONSIGNMENT_DISPTACHED_EVENT_NAME}'\n", "        ) - INTERVAL '{BILLED_TO_DISPATCH_MAX_HOURS}' HOUR\n", "        AND from_iso8601_timestamp(arroyo_ingestion_ts) < from_iso8601_timestamp('{current_time_utc_str}')\n", "        AND (consignment_id || '#' || container_id) IN (SELECT consignment_container_id FROM dispatched_containers)\n", "    GROUP BY\n", "        1, 2, 3\n", ")\n", "\n", "SELECT\n", "    *\n", "FROM\n", "    dispatched_container_details\n", "\n", "\"\"\"\n", "\n", "dispatched_containers = pd.read_sql_query(dispatched_containers_sql, trino_connection)\n", "dispatched_containers.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b92a5b19-1801-4306-a09c-d10c1c56004d", "metadata": {}, "outputs": [], "source": ["# Push above data to kafka\n", "pb.to_kafka(conn_id=kafka_conn_id, topic=kafka_sink_topic, df=dispatched_containers)"]}, {"cell_type": "code", "execution_count": null, "id": "2b47ca17-c33a-46b0-8efe-1b88fb97d15d", "metadata": {}, "outputs": [], "source": ["pb.to_trino(\n", "    checkpoint_update_sql.format(CONSIGNMENT_DISPTACHED_EVENT_NAME),\n", "    **checkpoint_table_kwargs\n", ")"]}, {"cell_type": "markdown", "id": "c634e31b-9870-4f3c-858c-37cf9c8a0a54", "metadata": {}, "source": ["### Process putaway event data (consignment-item level)"]}, {"cell_type": "code", "execution_count": null, "id": "f88644b8-da6d-441f-809e-e8ad1648ba1b", "metadata": {}, "outputs": [], "source": ["putaway_container_sql = f\"\"\"\n", "    SELECT\n", "        consignment_id,\n", "        site_id,\n", "        item_id,\n", "        DATE_FORMAT(CURRENT_TIMESTAMP AT TIME ZONE 'UTC', '%%Y-%%m-%%d %%H:%%i:%%s') AS putaway_etl_ts,\n", "        SUM(quantity) AS putaway_quantity\n", "    FROM\n", "        lake_events.storeops_consignment_putaway_events\n", "    WHERE\n", "        at_date BETWEEN CURRENT_DATE - INTERVAL '1' DAY AND CURRENT_DATE\n", "        AND from_iso8601_timestamp(arroyo_ingestion_ts) >= (\n", "            SELECT latest_checkpoint_ts FROM {checkpoint_table_schema}.{checkpoint_table_name} WHERE event_name = '{PUTAWAY_COMPLETED_EVENT_NAME}'\n", "        ) \n", "        AND from_iso8601_timestamp(arroyo_ingestion_ts) < from_iso8601_timestamp('{current_time_utc_str}')\n", "    GROUP BY\n", "        1, 2, 3\n", "\"\"\"\n", "\n", "putaway_containers = pd.read_sql_query(putaway_container_sql, trino_connection)\n", "putaway_containers.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ea2ba395-4dc8-46da-bc2b-072d591da6db", "metadata": {}, "outputs": [], "source": ["# Push above data to kafka\n", "pb.to_kafka(conn_id=kafka_conn_id, topic=kafka_sink_topic, df=putaway_containers)"]}, {"cell_type": "code", "execution_count": null, "id": "0c0c83c1-ae84-4c3f-abd8-7e657d590a21", "metadata": {}, "outputs": [], "source": ["pb.to_trino(\n", "    checkpoint_update_sql.format(PUTAWAY_COMPLETED_EVENT_NAME),\n", "    **checkpoint_table_kwargs\n", ")"]}, {"cell_type": "markdown", "id": "e32c2a49-3aa2-4828-b5e2-ef4b553fffb5", "metadata": {}, "source": ["### Process GRN completed event data (consignment-item level)"]}, {"cell_type": "code", "execution_count": null, "id": "e650c82c-2411-498e-a81f-3b271d062813", "metadata": {}, "outputs": [], "source": ["grn_sql = f\"\"\"\n", "    SELECT\n", "        consignment_id,\n", "        site_id,\n", "        event_ts AS grn_ts\n", "    FROM\n", "        lake_events.storeops_consignment_grn_events\n", "    WHERE\n", "        from_iso8601_timestamp(arroyo_ingestion_ts) >= (\n", "            SELECT latest_checkpoint_ts FROM {checkpoint_table_schema}.{checkpoint_table_name} WHERE event_name = '{GRN_COMPLETED_EVENT_NAME}'\n", "        ) \n", "        AND from_iso8601_timestamp(arroyo_ingestion_ts) < from_iso8601_timestamp('{current_time_utc_str}')\n", "        \n", "\"\"\"\n", "\n", "grn_consignments_info = pd.read_sql_query(grn_sql, trino_connection)\n", "grn_consignments = tuple(grn_consignments_info[\"consignment_id\"].unique())\n", "grn_consignments_formatted = str(grn_consignments)"]}, {"cell_type": "markdown", "id": "17612321-d0af-476f-b197-eb09d924e82e", "metadata": {}, "source": ["##### Get pinot data to transform grn data to item level"]}, {"cell_type": "code", "execution_count": null, "id": "e278c875-be4a-4ba8-be64-35d44b8de151", "metadata": {}, "outputs": [], "source": ["if grn_consignments:\n", "    pinot_connection = pb.get_connection(\"[Datastore] Pinot\")\n", "\n", "    consignment_item_sql = f\"\"\"\n", "        SELECT\n", "            consignment_id,\n", "            item_id\n", "        FROM\n", "            consignment_item_pending_grn_events\n", "        WHERE\n", "            consignment_id IN {grn_consignments_formatted}\n", "        GROUP BY\n", "            1, 2\n", "\n", "    \"\"\"\n", "\n", "    consignment_item_mapping = pd.read_sql_query(consignment_item_sql, pinot_connection)\n", "    grn_consignment_items = pd.merge(\n", "        grn_consignments_info,\n", "        consignment_item_mapping,\n", "        how=\"left\",\n", "        on=[\"consignment_id\"],\n", "    )\n", "\n", "    # Push above data to kafka\n", "    pb.to_kafka(conn_id=kafka_conn_id, topic=kafka_sink_topic, df=grn_consignment_items)\n", "    pb.to_trino(\n", "        checkpoint_update_sql.format(GRN_COMPLETED_EVENT_NAME),\n", "        **checkpoint_table_kwargs,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "2e16c363-c61b-4794-9b87-6d55c4ec7b18", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}