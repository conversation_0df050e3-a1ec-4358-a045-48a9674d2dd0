dag_name: redshift_to_presto
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: de
notebooks:
# Redshift to Presto 
- alias: misc.it_pd_log
  name: dump_to_presto
  parameters:
    sql: with logs as (select ipl.product_id,ipl.item_id,ipl.updated_on,COALESCE(ipl.multiplier,1)
      as multiplier,aspr,amrpr,flag,item_id1,OFFER_TYPE_IDENTIFIER,OFFER_TYPE_TAG,max(ipl.updated_on)
      over (partition by ipl.product_id) as last_update from it_pd_log ipl),lu1 as
      (select product_id,item_id,aspr,amrpr,flag,item_id1, OFFER_TYPE_IDENTIFIER,OFFER_TYPE_TAG,updated_on,max(multiplier)
      as multiplier from logs where updated_on = last_update group by 1,2,3,4,5,6,7,8,9)
      select product_id,item_id,item_id1,updated_on,multiplier,flag,offer_type_tag,offer_type_identifier,aspr,amrpr
      from lu1
    table_name: it_pd_log
  tag: parallel
- alias: misc.combo_offer
  name: dump_to_presto
  parameters:
    sql: select offer_type_id,product,quantity,offer_type_name from consumer.combo_offer
    table_name: combo_offer
  tag: parallel
owner:
  email: <EMAIL>
  slack_id: U03SJ3UMQG1
path: de/misc/etl/redshift_to_presto
paused: true
project_name: misc
schedule:
  interval: 30 0 * * *
  start_date: '2020-09-14T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 1
