dag_name: pl_performance_brand_affinity
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: de
notebook:
  parameters:
owner:
  email: <EMAIL>
  slack_id: U03RW0DNSUD
path: de/misc/etl/pl_performance_brand_affinity
paused: true
project_name: misc
schedule:
  interval: 0 10 * * *
  start_date: '2021-06-29T00:00:00'
schedule_type: fixed
sla: 120 minutes
slack_alert_configs:
- channel: bl-data-airflow-alerts
support_files: []
tags: []
template_name: notebook
version: 1
