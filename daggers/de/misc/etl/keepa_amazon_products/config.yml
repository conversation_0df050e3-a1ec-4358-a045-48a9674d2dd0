alert_configs:
  slack:
  - channel: bl-data-alerts-p2
dag_name: keepa_amazon_products
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: de
notebook:
  parameters: null
  retries: 3
  retry_delay_in_seconds: 180
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/misc/etl/keepa_amazon_products
paused: true
pool: de_pool
project_name: misc
schedule:
  end_date: '2026-07-01T00:00:00'
  interval: 47 */1 * * *
  start_date: '2023-07-10T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- columns_metadata.py
- utils.py
- blinkit_upc.sql
tags: []
template_name: notebook
version: 2
