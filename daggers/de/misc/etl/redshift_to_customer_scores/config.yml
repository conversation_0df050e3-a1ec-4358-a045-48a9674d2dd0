dag_name: redshift_to_customer_scores
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 8G
      request: 500M
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: de
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03STBBBGUQ
path: de/misc/etl/redshift_to_customer_scores
paused: true
project_name: misc
schedule:
  interval: 30 0 * * *
  start_date: '2021-07-15T00:00:00'
schedule_type: fixed
sla: 60 minutes
support_files: []
tags: []
template_name: notebook
version: 4
