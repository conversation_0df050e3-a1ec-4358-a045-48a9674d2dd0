template_name: sql
dag_name: order_count
version: 1
owner:
  name: vinayak
  slack_id: UFYKYCST0
schedule:
  start_date: '2019-10-01T00:00:00'
  interval: '0 */12 * * *'
source:
  conn_type: postgres
  conn_id: redshift_consumer
  infer:
    parameters:
      where_condition: " >= '2019-08-28' "
  bulk:
    parameters:
      where_condition: " >= '2018-10-01' "
  incremental:
    parameters:
      where_condition: " BETWEEN '{{ macros.ds_add(ds, -3) }}' AND '{{ ds }}' "
sink:
  conn_type: postgres
  conn_id: redshift_consumer
  schema: metrics
  table: order_count
  primary_key: ['scheduled_date', 'frontend_merchant']
  sortkey: ['scheduled_date', 'city_name', 'frontend_merchant']
  incremental_key: 'scheduled_date'
  load_type: 'upsert'
  column_dtypes:
  - name: scheduled_date
    type: TIMESTAMP WITH TIME ZONE
  - name: city_name
    type: VARCHAR(50)
  - name: frontend_merchant
    type: VARCHAR(50)
  - name: order_count
    type: INTEGER
dag_type: etl
escalation_priority: low
schedule_type: fixed
sla: 120 minutes
support_files: []
namespace: de
project_name: misc
paused: true
path: de/misc/etl/order_count
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
tags: []
executor:
  type: kubernetes
  config:
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    node_selectors:
      nodetype: spot
    tolerations:
    - key: service
      operator: Equal
      value: airflow
      effect: NoSchedule
    cpu:
      request: 0.5
      limit: 1
    memory:
      request: 500M
      limit: 4G
    volume_mounts:
    - name: airflow-dags
      subPath: airflow/plugins
      mountPath: /usr/local/airflow/plugins
    - name: airflow-dags
      subPath: tmp
      mountPath: /usr/local/airflow/tmp
