template_name: sql
dag_name: perfect_orders
version: 1
owner:
  name: vinayak
  slack_id: UFYKYCST0
schedule:
  start_date: '2019-10-01T00:00:00'
  interval: '0 */12 * * *'
source:
  conn_type: postgres
  conn_id: redshift_consumer
  infer:
    parameters:
      where_condition_1: " >= '2019-08-28' "
      where_condition_2: " <= '2019-08-28' "
  bulk:
    parameters:
      where_condition_1: " >= '2018-10-01' "
      where_condition_2: " <= '2018-10-01 '"
  incremental:
    parameters:
      where_condition_1: " >= '{{ macros.ds_add(ds, -3)}}' "
      where_condition_2: " <= '{{ ds }}' "
sink:
  conn_type: postgres
  conn_id: redshift_consumer
  schema: metrics
  table: perfect_orders_last_mile
  primary_key: ['date_', 'city']
  sortkey: ['date_', 'city']
  incremental_key: 'date_'
  load_type: 'upsert'
  column_dtypes:
  - name: city
    type: VARCHAR(50)
  - name: station
    type: <PERSON>RCHA<PERSON>(50)
  - name: station_alias
    type: VARCHAR(50)
  - name: date_
    type: TIM<PERSON><PERSON><PERSON> WITH TIME ZONE
  - name: on_time_delivered_orders
    type: INTEGER
  - name: total_orders
    type: INTEGER
  - name: on_time_delivered_orders_percentage
    type: FLOAT4
dag_type: etl
escalation_priority: low
schedule_type: fixed
sla: 120 minutes
support_files: []
namespace: de
project_name: misc
paused: true
path: de/misc/etl/perfect_orders
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
tags: []
executor:
  type: kubernetes
  config:
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    node_selectors:
      nodetype: spot
    tolerations:
    - key: service
      operator: Equal
      value: airflow
      effect: NoSchedule
    cpu:
      request: 0.5
      limit: 1
    memory:
      request: 500M
      limit: 4G
    volume_mounts:
    - name: airflow-dags
      subPath: airflow/plugins
      mountPath: /usr/local/airflow/plugins
    - name: airflow-dags
      subPath: tmp
      mountPath: /usr/local/airflow/tmp
