{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "import string\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table_name = \"order_frequency\"\n", "table_schema = \"consumer\"\n", "s3_access_iam_role = \"arn:aws:iam::442534439095:role/redshift-data-migration-role\"\n", "s3_dump_path = f\"s3://grofers-prod-dse-sgp/dwh/flattable/order_frequency/{''.join(random.choices(string.ascii_lowercase + string.digits, k = 6))}/\"\n", "column_dtypes = [\n", "    {\n", "        \"name\": \"city\",\n", "        \"type\": \"varchar(100)\",\n", "        \"description\": \"City name\",\n", "    },\n", "    {\n", "        \"name\": \"new_customers\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Count of new customers\",\n", "    },\n", "    {\n", "        \"name\": \"new_carts\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Count of new carts\",\n", "    },\n", "    {\n", "        \"name\": \"old_customers\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Count of old customers\",\n", "    },\n", "    {\n", "        \"name\": \"old_carts\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Count of old carts\",\n", "    },\n", "]\n", "table_description = (\n", "    \"This table contains city wise count of new and old customers and carts\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rebuild_sql = \"\"\"\n", "with y as (\n", "    with a as(\n", "        select \n", "            customer_id,\n", "            min(cart_checkout_ts+interval '5.5 hours')::date as ct\n", "\n", "        from \n", "            order_data o\n", "\n", "        group by \n", "            1\n", "        ),\n", "\n", "b as (\n", "    select \n", "        DISTINCT o.customer_id,\n", "        city,\n", "        min(cart_checkout_ts+interval '5.5 hours') over (partition by o.customer_id)::date as ct1,\n", "        ct\n", "\n", "    from \n", "        order_data o\n", "    left join \n", "        a \n", "            on a.customer_id=o.customer_id\n", "    left join \n", "        gr_cart c \n", "            on c.id=o.cart_id\n", "\n", "    WHERE \n", "        o.city IN ('Delhi',\n", "                    'Bengaluru',\n", "                    'Mumbai',\n", "                    'HR-NCR',\n", "                    'Kolkata',\n", "                    'UP-NCR',\n", "                    'Pune',\n", "                    'Jaipur',\n", "                    'Hyderabad',\n", "                    'Ahmedabad',\n", "                    'Lucknow',\n", "                    'Chennai',\n", "                    'Kanpur')\n", "     AND extract('month' FROM date(convert_timezone('Asia/Kolkata', o.install_ts))) = extract('month' FROM (CURRENT_DATE-interval '1 day'))\n", "     AND extract('year' FROM date(convert_timezone('Asia/Kolkata', o.install_ts))) = extract('year' FROM (CURRENT_DATE-interval '1 day'))\n", "     AND date(convert_timezone('Asia/Kolkata', o.install_ts)) < CURRENT_DATE\n", "     AND o.merchant_name NOT ILIKE '%%grocery%%mart%%'\n", "     AND \n", "        (o.merchant_business_type IS NULL\n", "          OR o.merchant_business_type <> 'b2b')\n", "     AND lower(o.city) IS NOT NULL\n", "     AND (c.source IS NULL OR lower(c.source) NOT IN ('crm_system','INTERNAL_DASHBOARD'))\n", "     )\n", "\n", "    select \n", "        DISTINCT b.city,\n", "        b.customer_id,\n", "        case \n", "            when b.ct=ct1 \n", "            then 'new' \n", "            else 'old' \n", "        end as tags,\n", "        count(distinct cart_id) as carts\n", "\n", "    from \n", "        b\n", "    left join  \n", "        order_data o \n", "            on o.customer_id=b.customer_id \n", "    left join \n", "        a \n", "            on a.customer_id=o.customer_id\n", "    left join \n", "        gr_cart c \n", "            on c.id=o.cart_id\n", "\n", "    WHERE \n", "        o.city IN ('Delhi',\n", "                        'Bengaluru',\n", "                        'Mumbai',\n", "                        'HR-NCR',\n", "                        'Kolkata',\n", "                        'UP-NCR',\n", "                        'Pune',\n", "                        'Jaipur',\n", "                        'Hyderabad',\n", "                        'Ahmedabad',\n", "                        'Lucknow',\n", "                        'Chennai',\n", "                        'Kanpur')\n", "        AND o.merchant_name NOT ILIKE '%%grocery%%mart%%'\n", "        AND (o.merchant_business_type IS NULL\n", "              OR o.merchant_business_type <> 'b2b')\n", "        AND lower(o.city) IS NOT NULL\n", "        AND extract('month' FROM date(convert_timezone('Asia/Kolkata', o.install_ts))) = extract('month' FROM (CURRENT_DATE-interval '1 day'))\n", "        AND extract('year' FROM date(convert_timezone('Asia/Kolkata', o.install_ts))) = extract('year' FROM (CURRENT_DATE-interval '1 day'))\n", "        AND date(convert_timezone('Asia/Kolkata', o.install_ts)) < CURRENT_DATE\n", "         AND \n", "            (c.source IS NULL\n", "            OR lower(c.source) NOT IN ('crm_system','INTERNAL_DASHBOARD')\n", "            )\n", "\n", "    group by \n", "        1,2,b.ct,ct1\n", "    )\n", "select \n", "    city,\n", "    count(distinct  case \n", "                        when tags ilike 'new' \n", "                        then  customer_id\n", "                        else null \n", "                    end)::int  new_customers,\n", "    sum(case \n", "            when tags ilike 'new' \n", "            then carts\n", "        end)::int  as new_carts,\n", "    count(distinct  case \n", "                        when tags ilike 'old' \n", "                        then  customer_id \n", "                        else null \n", "                    end)::int  old_customers,\n", "    sum(case \n", "            when tags ilike 'old' \n", "            then carts \n", "        end)::int  as old_carts\n", "\n", "from y\n", "\n", "group by \n", "    1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["unload_sql = f\"\"\"\n", "UNLOAD ($$ {rebuild_sql} $$)\n", "to '{s3_dump_path}'\n", "iam_role '{s3_access_iam_role}'\n", "FORMAT PARQUET\n", "ALLOWOVERWRITE\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_connection = pb.get_connection(\"redpen\")\n", "redshift_connection.execute(unload_sql)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": table_schema,\n", "    \"table_name\": table_name,\n", "    \"primary_key\": [],\n", "    \"column_dtypes\": column_dtypes,\n", "    \"sortkey\": [],\n", "    \"incremental_key\": [],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": table_description,\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(s3_dump_path, copy_params=[\"FORMAT AS PARQUET\"], **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}