template_name: sql
dag_name: orders_attempted_for_delivery
version: 2
owner:
  email: sang<PERSON><PERSON><EMAIL>
  slack_id: U03RW2K4XBP
schedule:
  start_date: '2020-06-25T00:00:00'
  interval: 0 0 * * *
source:
  conn_type: postgres
  conn_id: redshift_consumer
  infer:
    parameters:
      where_condition: " >= '2019-08-28' "
  bulk:
    parameters:
      where_condition: " >= '2018-10-01' "
  incremental:
    parameters:
      where_condition: " BETWEEN '{{ macros.ds_add(ds, -2) }}' AND '{{ macros.ds_add(ds,\
        \ -1) }}' "
sink:
  conn_type: postgres
  conn_id: redshift_consumer
  schema: metrics
  table: orders_attempted_for_delivery
  primary_key: [delivery_attempted_date, merchant_id]
  sortkey: [delivery_attempted_date, city, station]
  incremental_key: delivery_attempted_date
  load_type: upsert
  column_dtypes:
  - name: city
    type: varchar(65535)
  - name: merchant_id
    type: varchar(21)
  - name: merchant_name
    type: varchar(256)
  - name: station
    type: varchar(65535)
  - name: delivery_attempted_date
    type: date
  - name: delivery_attempted_slot
    type: varchar(59)
  - name: total_attempt_count
    type: bigint
dag_type: etl
escalation_priority: low
schedule_type: fixed
sla: 120 minutes
support_files: []
namespace: de
project_name: misc
paused: true
path: de/misc/etl/orders_attempted_for_delivery
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
tags: []
executor:
  type: kubernetes
  config:
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    node_selectors:
      nodetype: spot
    tolerations:
    - key: service
      operator: Equal
      value: airflow
      effect: NoSchedule
    cpu:
      request: 0.5
      limit: 1
    memory:
      request: 500M
      limit: 4G
    volume_mounts:
    - name: airflow-dags
      subPath: airflow/plugins
      mountPath: /usr/local/airflow/plugins
    - name: airflow-dags
      subPath: tmp
      mountPath: /usr/local/airflow/tmp
