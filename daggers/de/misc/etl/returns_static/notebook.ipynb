{"cells": [{"cell_type": "raw", "id": "d5c73eb3-6d55-44d9-8aa3-edb4de948f4e", "metadata": {}, "source": ["WIP\n", "1. Write IRO only query"]}, {"cell_type": "code", "execution_count": null, "id": "04cda630-e028-4e70-b7c9-5c535d745b72", "metadata": {}, "outputs": [], "source": ["!pip install https://*****************************/ShamanJaggia/wheels/main/joolbox-0.10.3-py3-none-any.whl -q\n", "from joolbox import *\n", "\n", "dt = Cdt()\n", "pd.set_option(\"display.max_colwidth\", None)\n", "import json\n", "from pandas.io.json import json_normalize"]}, {"cell_type": "code", "execution_count": null, "id": "de6ca8b2-1c64-4e76-89a6-948b9a2d80b7", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "a50d1de9-671c-4763-94b5-5f4135ef3483", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["start_date = dt.today_x(2)\n", "# dt.today_x(10)\n", "# '2022-08-01'\n", "end_date = dt.today_x(1)\n", "st_dt = start_date.replace(\"-\", \"\")\n", "ed_dt = end_date.replace(\"-\", \"\")\n", "week_fix = \"0\"\n", "day_week_month = \"day\""]}, {"cell_type": "code", "execution_count": null, "id": "48690bee-f9e2-4f6a-8780-70d6ca71abcb", "metadata": {}, "outputs": [], "source": ["params = {\n", "    \"{{start_date}}\": start_date,\n", "    \"{{end_date}}\": end_date,\n", "    \"{{st_dt}}\": st_dt,\n", "    \"{{ed_dt}}\": ed_dt,\n", "    \"{{week_fix}}\": week_fix,\n", "    \"{{day_week_month}}\": day_week_month,\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "1bf1d4fb-6b8a-4553-9615-b851e9cd11a6", "metadata": {}, "outputs": [], "source": ["print(start_date, end_date)"]}, {"cell_type": "markdown", "id": "b230ee2e-8938-486c-8ef5-229d193cfd3f", "metadata": {}, "source": ["### RETURNS"]}, {"cell_type": "code", "execution_count": null, "id": "576a3991-d789-46fc-b44d-289043b21f07", "metadata": {}, "outputs": [], "source": ["returns = (\n", "    \"\"\"\n", "with ims as (\n", "select    date_trunc('{{day_week_month}}',(pos_timestamp::TIMESTAMP + interval '5.5 hrs')::date)::date AS {{day_week_month}},\n", "          outlet_id,\n", "          grofers_order_id,\n", "          order_id,\n", "          cart_id,\n", "          oo.type,\n", "          oo.direction,\n", "          oo.current_status as order_current_status,\n", "          os.current_status as suborder_current_status,\n", "          p.item_id,\n", "          ipm.product_id,\n", "          name as item_name,\n", "          coalesce(sum(case when inventory_update_type_id in (2,50) then transaction_lp*\"delta\" end),0) as expected_return_lp,\n", "          coalesce(sum(case when inventory_update_type_id not in (2,50) then \"delta\" end),0) as total_returns_qty,\n", "          coalesce(sum(case when inventory_update_type_id not in (2,50) then transaction_lp*\"delta\" end),0) as total_returns,\n", "          coalesce(sum(case when inventory_update_type_id in (3,29,30,31,32)  then \"delta\" end),0) as good_returns_qty,\n", "          coalesce(sum(case when inventory_update_type_id in (3,29,30,31,32)  then transaction_lp*\"delta\" end),0) as good_returns,\n", "          coalesce(sum(case when inventory_update_type_id in (7,9,63,33,34,67) then \"delta\" end),0) as bad_returns_qty,\n", "          coalesce(sum(case when inventory_update_type_id in (7,9,63,33,34,67) then transaction_lp*\"delta\" end),0) as bad_returns\n", "          \n", "                   \n", "  from consumer.view_ims_non_infra_inventory_log i\n", "  JOIN lake_rpc.product_product p ON i.variant_id = p.variant_id and active=1\n", "  JOIN lake_oms_bifrost.oms_suborder os ON i.grofers_order_id = os.id\n", "  JOIN lake_oms_bifrost.oms_order oo ON os.order_id = oo.id\n", "  LEFT JOIN lake_rpc.item_product_mapping ipm ON p.item_id = ipm.item_id and ipm.active=1 and offer_id is null\n", " \n", "  WHERE inventory_update_type_id in (3,29, 30, 31, 32, --good\n", "                                        7,9,63,33, 34, 67,   --bad\n", "                                        2,50)  ---billed\n", "                                        \n", "    and i.pos_timestamp >= '{{start_date}}'::TIMESTAMP - interval '5.5 hrs' \n", "    and i.pos_timestamp < '{{end_date}}'::TIMESTAMP - interval '5.5 hrs' +  interval '1 day' \n", "   group by 1,2,3,4,5,6,7,8,9,10,11,12\n", "),\n", "\n", "ifo_carts as (\n", "select  date_trunc('{{day_week_month}}',(os.install_ts + interval '5.5 hrs')::date)::date AS {{day_week_month}},\n", "        grofers_order_id,\n", "        o.cart_id,\n", "        item_id,\n", "        sum(transaction_lp*\"delta\") as ifo_billed\n", "from lake_oms_bifrost.oms_suborder os\n", "JOIN lake_oms_bifrost.oms_order o  ON os.order_id = o.id\n", "JOIN consumer.view_ims_non_infra_inventory_log i ON os.id = i.grofers_order_id\n", "JOIN lake_rpc.product_product p ON i.variant_id = p.variant_id and active=1 \n", " where o.type = 'InternalForwardOrder'\n", "       and os.current_status = 'AT_DELIVERY_CENTER' \n", "       and os.install_ts >= '{{start_date}}'::TIMESTAMP - interval '5.5 hrs'\n", "       and os.install_ts < '{{end_date}}'::TIMESTAMP - interval '5.5 hrs' + interval '1 day'\n", "group by 1,2,3,4\n", "),\n", "\n", "rpu_carts as (\n", "select original_order_item_id,\n", "        sum(product_quantity) as exp_return_qty\n", "from dwh.fact_sales_order_item_details oid \n", "LEFT JOIN ifo_carts ic ON oid.cart_id = ic.cart_id\n", "where order_type = 'InternalReverseOrder'\n", "    and order_current_status = 'DELIVERED'\n", "    and  order_create_ts_ist >= '{{start_date}}'::TIMESTAMP\n", "    and  order_create_ts_ist < '{{end_date}}'::TIMESTAMP  + interval '1 day'\n", "    and ic.cart_id is null\n", "group by 1\n", "\n", "),\n", "\n", "\n", "iro_exp as (\n", "select  date_trunc('{{day_week_month}}',order_create_ts_ist::date)::date AS {{day_week_month}},\n", "        oid.order_id,\n", "        oid.suborder_id,\n", "        oid.product_id,\n", "        sum(exp_return_qty * unit_weighted_landing_price) as iro_exp_return_lp\n", "from dwh.fact_sales_order_item_details oid\n", "JOIN rpu_carts ir ON oid.order_item_id = ir.original_order_item_id\n", "JOIN lake_oms_bifrost.oms_order_item ooi ON oid.order_item_id = ooi.id\n", "where order_create_ts_ist >= '{{start_date}}'::TIMESTAMP\n", "    and  order_create_ts_ist < '{{end_date}}'::TIMESTAMP  + interval '1 day'\n", "group by 1,2,3,4\n", "),\n", "\n", "doorstep as (\n", " select    date_trunc('{{day_week_month}}',cart_checkout_ts_ist::date)::date AS {{day_week_month}},\n", "           order_id,\n", "           suborder_id,\n", "           product_id\n", "from dwh.fact_sales_order_item_details\n", "    where \n", "     cart_checkout_ts_ist >= '{{start_date}}'::TIMESTAMP \n", "    and cart_checkout_ts_ist < '{{end_date}}'::TIMESTAMP +  interval '1 day'\n", "    and total_doorstep_return_quantity >0\n", "  group by 1,2,3,4\n", "            \n", "),\n", "refunds as (\n", "select    \n", "          gpr.cart_id,\n", "          sum(refund_amount) total_refund\n", "    \n", "    from  lake_grofers_db.gr_payment_refund gpr\n", "    JOIN dwh.fact_sales_order_details od ON gpr.order_id = od.order_id\n", "    LEFT JOIN lake_crm.view_crm_issue iss ON gpr.ticket_id = iss.id and gpr.cart_id = iss.cart_id\n", "     \n", "    where \n", "             gpr.install_ts >= '{{start_date}}'::TIMESTAMP - interval '5.5 hrs'\n", "            and gpr.install_ts < '{{end_date}}'::TIMESTAMP  - interval '5.5 hrs' + interval '1 day'\n", "            and gpr.status = 'SUCCESS'\n", "            and refund_reason not ilike '%%non_acceptable_payment%%'\n", "            and gpr.order_id is not null\n", "            AND station_name NOT ILIKE '%b2b%'\n", "            AND order_current_status = 'DELIVERED'\n", "            and refund_amount::float >0\n", "            -- and gpr.cart_id=224403675\n", "            \n", "    group by 1\n", "\n", "),\n", "\n", "mdnd_base as (\n", "select cart_id,group_disposition,count(group_disposition) over(partition by cart_id) cnt from consumer.cd_master_table\n", "where cart_id <> ''\n", "Group by 1,2\n", "),\n", "\n", "mdnd as (\n", "select  date_trunc('{{day_week_month}}',cart_checkout_ts_ist::date)::date AS {{day_week_month}},\n", "       order_id,\n", "       od.cart_id,\n", "       od.suborder_id,\n", "       product_id,\n", "       total_weighted_landing_price,\n", "       sum(total_weighted_landing_price) over(partition by od.cart_id) as mdnd_lp\n", "from mdnd_base b\n", "JOIN dwh.fact_sales_order_item_details od ON b.cart_id::float = od.cart_id::float\n", "where cart_checkout_ts_ist >= '{{start_date}}'::TIMESTAMP \n", "      and cart_checkout_ts_ist < '{{end_date}}'::TIMESTAMP + interval '1 day'\n", "      and order_current_status = 'DELIVERED'\n", "      and order_type not ilike '%%internal%%'\n", "      and group_disposition = 'MDND'\n", "      and cnt = 1\n", "group by 1,2,3,4,5,6\n", "),\n", " \n", "final as (\n", "select \n", "        i.{{day_week_month}},\n", "        i.outlet_id,\n", "        i.grofers_order_id as suborder_id,\n", "        i.order_id,\n", "        i.cart_id,\n", "        case when suborder_current_status = 'REPROCURED' then 'reschedule'\n", "             when suborder_current_status = 'CANCELLED' then 'cancellation'\n", "             when suborder_current_status = 'AT_DELIVERY_CENTER' and i.type = 'InternalForwardOrder' then 'ifo'\n", "             when suborder_current_status = 'AT_DELIVERY_CENTER' and rc.order_id is not null then 'rpu'\n", "             when suborder_current_status = 'AT_DELIVERY_CENTER' and ds.order_id is not null then 'doorstep'\n", "             when suborder_current_status = 'AT_DELIVERY_CENTER' and md.order_id is not null and rf.total_refund > mdnd_lp then 'mdnd'\n", "             when suborder_current_status = 'AT_DELIVERY_CENTER' and total_returns >0 then 'additional_return'\n", "        end as order_type,  \n", "        i.product_id,\n", "        item_name as product_name,\n", "        case when rc.order_id is not null then iro_exp_return_lp else i.expected_return_lp end as exp_return_lp,\n", "        total_returns,\n", "        good_returns,\n", "        bad_returns,\n", "        exp_return_lp - total_returns  as unaccounted,\n", "        bad_returns + unaccounted as return_loss\n", "from ims i\n", "LEFT JOIN ifo_carts fc ON i.cart_id = fc.cart_id and i.grofers_order_id = fc.grofers_order_id and i.item_id = fc.item_id and i.{{day_week_month}} = fc.{{day_week_month}}\n", "LEFT JOIN iro_exp rc ON i.order_id = rc.order_id and i.grofers_order_id = rc.suborder_id and i.product_id = rc.product_id and i.{{day_week_month}} = rc.{{day_week_month}}\n", "LEFT JOIN doorstep ds  ON i.order_id = ds.order_id and i.grofers_order_id = ds.suborder_id and i.product_id = ds.product_id and i.{{day_week_month}} = ds.{{day_week_month}}\n", "LEFT JOIN mdnd md  ON i.order_id = md.order_id and i.grofers_order_id = md.suborder_id and i.product_id = md.product_id and i.{{day_week_month}} = md.{{day_week_month}}\n", "LEFT JOIN refunds rf ON i.cart_id = rf.cart_id\n", "\n", ")\n", "\n", "select *\n", "--sum(unaccounted) , sum(bad_returns)\n", "from final \n", "where order_type is not null\n", "     \n", "\n", "     \n", "\n", "\"\"\".replace(\n", "        \"%\", \"%%\"\n", "    )\n", "    .replace(\"{{\", \"{\")\n", "    .replace(\"}}\", \"}\")\n", ")\n", "\n", "\n", "returns = pd.read_sql(\n", "    returns.format(\n", "        start_date=start_date, end_date=end_date, day_week_month=day_week_month\n", "    ),\n", "    con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "********-ac27-4b5a-85ee-95d609d88708", "metadata": {}, "outputs": [], "source": ["returns.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "ef7651e2-3d0c-43f0-9ccc-3308d7e4466a", "metadata": {}, "outputs": [], "source": ["returns[returns[\"order_type\"] == \"mdnd\"].exp_return_lp.sum()"]}, {"cell_type": "code", "execution_count": null, "id": "988d1ba8-1b62-4878-b951-e80786f6e02e", "metadata": {}, "outputs": [], "source": ["returns.bad_returns.sum()"]}, {"cell_type": "code", "execution_count": null, "id": "d57be4d6-0e40-4418-bc2f-d85ee9100325", "metadata": {}, "outputs": [], "source": ["# returns.to_csv('data_dump2/returns2 ' +start_date+  ' to '+end_date+ '.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "769aec6a-06e0-485c-b40d-4b51de69832c", "metadata": {}, "outputs": [], "source": ["ifo_wo_carts = (\n", "    \"\"\"\n", "with ifo as (\n", "select  \n", "        cart_id,\n", "        o.id as order_id\n", "        \n", "from lake_oms_bifrost.oms_order o\n", " where o.type = 'InternalForwardOrder'\n", "       and o.current_status = 'DELIVERED' \n", "       and o.install_ts >= '{{start_date}}'::TIMESTAMP - interval '5.5 hrs'\n", "       and o.install_ts < '{{end_date}}'::TIMESTAMP - interval '5.5 hrs' + interval '1 day'\n", "group by 1,2\n", "),\n", "iro as (\n", "select  \n", "        cart_id,\n", "        o.id as order_id\n", "from lake_oms_bifrost.oms_order o\n", " where o.type = 'InternalReverseOrder'\n", "       and o.current_status = 'DELIVERED' \n", "       and o.install_ts >= '{{start_date}}'::TIMESTAMP - interval '5.5 hrs'\n", "       and o.install_ts < '{{end_date}}'::TIMESTAMP - interval '5.5 hrs' + interval '1 day'\n", "group by 1,2\n", ")\n", "\n", "select \n", "       os.id as suborder_id,\n", "       True as ifo_wo_iro\n", "from ifo \n", "JOIN lake_oms_bifrost.oms_order o ON ifo.cart_id = o.cart_id\n", "JOIN lake_oms_bifrost.oms_suborder os ON o.id = os.order_id\n", "LEFT JOIN iro ON ifo.cart_id = iro.cart_id\n", "where iro.cart_id is null\n", "and o.type =  'InternalForwardOrder'\n", "and os.current_status = 'AT_DELIVERY_CENTER' \n", "group by 1,2\n", "\n", "\n", "\"\"\".replace(\n", "        \"%\", \"%%\"\n", "    )\n", "    .replace(\"{{\", \"{\")\n", "    .replace(\"}}\", \"}\")\n", ")\n", "\n", "ifo_wo_iro = pd.read_sql(\n", "    ifo_wo_carts.format(\n", "        start_date=start_date, end_date=end_date, day_week_month=day_week_month\n", "    ),\n", "    con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ee2ada6a-0d38-4aee-a9c1-78d8ed3f8412", "metadata": {}, "outputs": [], "source": ["ifo_wo_iro.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "011722b1-b619-4431-911d-12698a5b1842", "metadata": {}, "outputs": [], "source": ["ifo_wo_iro.shape"]}, {"cell_type": "code", "execution_count": null, "id": "b199eb6a-5b30-4a4b-9438-4988b99528d3", "metadata": {}, "outputs": [], "source": ["cnc_flag = (\n", "    \"\"\"\n", "with cnc as (\n", "select  \n", "        ooe.order_id,\n", "        json_extract_path_text(extra,'to') as event,\n", "        event_type_key,\n", "        count(event_type_key) over(partition by ooe.order_id) as cnt\n", "from lake_oms_bifrost.oms_order_event ooe\n", "JOIN dwh.fact_sales_order_details od on ooe.order_id = od.order_id\n", "where install_ts >= '{{start_date}}'::TIMESTAMP - interval '5.5 hrs'\n", "       and install_ts < '{{end_date}}'::TIMESTAMP - interval '5.5 hrs' + interval '1 day'\n", "    and (json_extract_path_text(extra,'to')='ENROUTE' or event_type_key = 'order_cancellation')\n", "    and order_current_status = 'CANCELLED'\n", "    and order_type not ilike '%%internal%%'\n", "group by 1,2,3\n", ")\n", "\n", "select  order_id,\n", "        CASE WHEN cnt = 1 then 0 else 1 end as pre_post_enroute_flag\n", "from cnc\n", "group by 1,2\n", "--pre_enroute_cnc =0 ; post_enroute_cnc =1\n", "\"\"\".replace(\n", "        \"%\", \"%%\"\n", "    )\n", "    .replace(\"{{\", \"{\")\n", "    .replace(\"}}\", \"}\")\n", ")\n", "\n", "\n", "cnc_flag = pd.read_sql(cnc_flag.format(start_date=start_date, end_date=end_date), con)"]}, {"cell_type": "code", "execution_count": null, "id": "a45dcb2b-eaf4-4d09-b223-7549ec069740", "metadata": {}, "outputs": [], "source": ["cnc_flag.head(1)"]}, {"cell_type": "markdown", "id": "c26f93a4-fd63-4abc-9cb8-6cad7d56d65c", "metadata": {}, "source": ["### Doorstep"]}, {"cell_type": "code", "execution_count": null, "id": "fa240814-5904-4536-9970-f9a0798dcf04", "metadata": {}, "outputs": [], "source": ["q1 = (\n", "    f\"\"\"\n", "\n", "    select  date_trunc('{{day_week_month}}',cart_checkout_ts_ist::date)::date AS {{day_week_month}},\n", "           outlet_id,\n", "           suborder_id,\n", "           product_id,\n", "           sum(total_doorstep_return_quantity * unit_weighted_landing_price) as exp_return_lp_doorstep\n", "    from dwh.fact_sales_order_item_details\n", "    where \n", "     cart_checkout_ts_ist >= '{{start_date}}'::TIMESTAMP \n", "    and cart_checkout_ts_ist < '{{end_date}}'::TIMESTAMP +  interval '1 day'\n", "    and total_doorstep_return_quantity >0\n", "    group by 1,2,3,4\n", "\n", "                 \n", "\n", "\"\"\".replace(\n", "        \"%\", \"%%\"\n", "    )\n", "    .replace(\"{{\", \"{\")\n", "    .replace(\"}}\", \"}\")\n", ")\n", "\n", "\n", "d1 = pd.read_sql(\n", "    q1.format(start_date=start_date, end_date=end_date, day_week_month=day_week_month),\n", "    con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e6f1f007-009c-4711-a3b5-98b72f01c8f0", "metadata": {}, "outputs": [], "source": ["d1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "30975877-d9ac-4b45-a1fa-92e174ace989", "metadata": {}, "outputs": [], "source": ["d1.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "2217631d-c6ed-46f6-bbd5-fea12f83a10f", "metadata": {}, "outputs": [], "source": ["# d1.to_csv('data_dump2/expected_lp_df2 ' +start_date+  ' to '+end_date+ '.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "2235f17e-6c08-48e2-9d20-cf39a93920b3", "metadata": {}, "outputs": [], "source": ["# df4_agg = pd.pivot_table(df4,index=[day_week_month,'outlet_id','order_id','payment_method'],values=['exp_ret_qty','exp_return_lp'],aggfunc=np.sum).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "fde3b1f7-fc18-42bb-84ad-39ff4a33fd0a", "metadata": {}, "outputs": [], "source": ["# df4_agg.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "b33f2d82-9831-49a4-a47d-9a3b2b97ea85", "metadata": {}, "outputs": [], "source": ["# order_ids_dsr = list(df4.order_id.unique())"]}, {"cell_type": "markdown", "id": "84948601-ed7b-4b81-86e0-789f4e534976", "metadata": {}, "source": ["## MANIPULATIONS"]}, {"cell_type": "code", "execution_count": null, "id": "8a69fd69-2460-4559-9f1e-9d012f142839", "metadata": {}, "outputs": [], "source": ["rf1 = returns.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "4c258017-063e-4653-94f5-5daefeb66ad1", "metadata": {}, "outputs": [], "source": ["rf2 = d1.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "f61bdc8b-b0b9-4dae-825c-6a7358b7196a", "metadata": {}, "outputs": [], "source": ["rf1.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "d619bee0-9dfe-4f73-b554-741c78369e5c", "metadata": {}, "outputs": [], "source": ["rf2.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "b6f44b17-0ebc-464f-941d-635b97d82c74", "metadata": {}, "outputs": [], "source": ["rf1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "0de450de-88d4-4808-8a8e-b3cb231b23e1", "metadata": {}, "outputs": [], "source": ["rf1[rf1[\"outlet_id\"].isna()].shape"]}, {"cell_type": "code", "execution_count": null, "id": "ac9094fb-df8a-44e7-95f9-b81c9d779076", "metadata": {}, "outputs": [], "source": ["rf2.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "c64c05c9-67f2-405c-9aa9-a52cc86ae38a", "metadata": {}, "outputs": [], "source": ["rf1 = rf1.astype({\"outlet_id\": int, \"product_id\": int, \"suborder_id\": int})"]}, {"cell_type": "code", "execution_count": null, "id": "b39fce87-bc79-4c1e-bc18-b7309813ccf7", "metadata": {}, "outputs": [], "source": ["rf2 = rf2.astype({\"outlet_id\": int, \"product_id\": int, \"suborder_id\": int})"]}, {"cell_type": "code", "execution_count": null, "id": "704660e4-e36a-4469-a4d7-6b631349cd1b", "metadata": {}, "outputs": [], "source": ["df_return = rf1.merge(\n", "    rf2, on=[day_week_month, \"outlet_id\", \"suborder_id\", \"product_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "eee017b6-cfda-48d2-8929-a76adaf148ed", "metadata": {}, "outputs": [], "source": ["df_return = df_return.merge(cnc_flag, on=[\"order_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "ec5dbe35-abb1-4938-bf77-c1fc8f12e924", "metadata": {}, "outputs": [], "source": ["df_return = df_return.merge(ifo_wo_iro, on=[\"suborder_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "ef854785-be3d-4389-9f11-5c48cb4be17b", "metadata": {}, "outputs": [], "source": ["df_return.shape"]}, {"cell_type": "code", "execution_count": null, "id": "0b410de4-98e5-4569-9890-9b8220b24790", "metadata": {}, "outputs": [], "source": ["df_return.total_returns.sum()"]}, {"cell_type": "code", "execution_count": null, "id": "4f557927-b165-4f00-adc8-f6424861b21f", "metadata": {}, "outputs": [], "source": ["df_return.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "9ac5f1cd-e4fe-461a-977d-0cb4c5c4e7c4", "metadata": {}, "outputs": [], "source": ["df_return[\"exp_return_lp_doorstep\"] = df_return[\"exp_return_lp_doorstep\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "9ba54a49-f707-4156-b45a-29db3dc46ff0", "metadata": {}, "outputs": [], "source": ["df_return.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "62f12303-6149-435b-99f0-7b86e7abcf1f", "metadata": {}, "outputs": [], "source": ["df_return[\"exp_return_lp\"].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "e4d1347f-9549-496f-b815-8b9e827ccb0b", "metadata": {}, "outputs": [], "source": ["df_return[\"ifo_wo_iro\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "2a16286a-5f5c-4fd9-8c51-05c2b3ca7643", "metadata": {}, "outputs": [], "source": ["df_return[\"exp_return_lp\"] = df_return.apply(\n", "    lambda x: 0\n", "    if x[\"order_type\"] == \"additional_return\"\n", "    else x[\"exp_return_lp_doorstep\"]\n", "    if x[\"order_type\"] == \"doorstep\"\n", "    else x[\"exp_return_lp\"],\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d428b1ce-0420-4037-81e4-867a054ec5fe", "metadata": {}, "outputs": [], "source": ["df_return[\"unaccounted\"] = df_return.apply(\n", "    lambda x: 0\n", "    if ((x[\"exp_return_lp\"] > 0) & (x[\"exp_return_lp\"] < x[\"total_returns\"]))\n", "    else x[\"exp_return_lp\"] - x[\"total_returns\"],\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b64b93a5-dd4c-4062-8a97-6cc03fca4732", "metadata": {}, "outputs": [], "source": ["df_return.unaccounted.sum()"]}, {"cell_type": "code", "execution_count": null, "id": "0665e7ab-b97c-4030-9081-6342e711aba5", "metadata": {}, "outputs": [], "source": ["df_return[\"return_loss\"] = df_return.apply(\n", "    lambda x: x[\"unaccounted\"] + x[\"bad_returns\"], axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a86d6139-5665-4e2a-99bc-5bc363d3c64d", "metadata": {}, "outputs": [], "source": ["df_return[\"pre_post_enroute_flag\"] = df_return.apply(\n", "    lambda x: x[\"pre_post_enroute_flag\"]\n", "    if ((x[\"pre_post_enroute_flag\"] == 0) & (x[\"order_type\"] == \"cancellation\"))\n", "    else 1,\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ca30412c-bb0b-4388-b101-a449f5016160", "metadata": {}, "outputs": [], "source": ["df_return[\"ifo_wo_iro\"] = df_return.apply(\n", "    lambda x: x[\"ifo_wo_iro\"] if x[\"ifo_wo_iro\"] == True else False, axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3c0f4bc7-ea69-4826-888c-1ea0705455b9", "metadata": {}, "outputs": [], "source": ["df_return[\"order_type\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "50c5efac-c04d-4ce4-9868-fa470046ef6e", "metadata": {}, "outputs": [], "source": ["df_return.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "4b1d089b-93c4-4286-a526-d84d2237b187", "metadata": {}, "outputs": [], "source": ["df_return.bad_returns.sum()"]}, {"cell_type": "code", "execution_count": null, "id": "3b29e39e-5ff8-476e-98fa-bb7d14f2b739", "metadata": {}, "outputs": [], "source": ["df_return.unaccounted.sum()"]}, {"cell_type": "code", "execution_count": null, "id": "43a4ff9b-b386-4fbe-9f22-7673586ce5c4", "metadata": {}, "outputs": [], "source": ["df_return = df_return.drop(columns=[\"exp_return_lp_doorstep\"])"]}, {"cell_type": "code", "execution_count": null, "id": "f067da96-c432-4d3a-87c7-a4f57e112702", "metadata": {}, "outputs": [], "source": ["df_return[\"record_created_ts_ist\"] = datetime.now(timezone(\"Asia/Kolkata\")).strftime(\n", "    \"%Y-%m-%d %H:%M:%S\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "dcba4cba-241b-414c-9dc7-e8d0bad6da9a", "metadata": {}, "outputs": [], "source": ["df_return[\"return_loss\"].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "b4657291-7265-4adf-b462-8a92c157b63f", "metadata": {}, "outputs": [], "source": ["df_return[(df_return[\"order_type\"] == \"ifo\")].bad_returns.sum()"]}, {"cell_type": "code", "execution_count": null, "id": "ccc9ad39-2c8b-4e32-aa1c-34e030b9d87d", "metadata": {}, "outputs": [], "source": ["df_return[(df_return[\"order_type\"] == \"ifo\")].unaccounted.sum()"]}, {"cell_type": "code", "execution_count": null, "id": "ae1adfaa-467e-4c36-ad21-4ac9997fbd4e", "metadata": {}, "outputs": [], "source": ["df_return[(df_return[\"order_type\"] == \"additional_return\")].unaccounted.sum()"]}, {"cell_type": "code", "execution_count": null, "id": "e16f7adb-d6ee-47d5-a5eb-081ec8e2961c", "metadata": {}, "outputs": [], "source": ["df_return[df_return[\"cart_id\"] == *********]"]}, {"cell_type": "code", "execution_count": null, "id": "8aa3456d-d609-4565-a008-456426984da8", "metadata": {}, "outputs": [], "source": ["df_return.shape"]}, {"cell_type": "code", "execution_count": null, "id": "913eda6c-8508-40cf-8b63-08624a4ddac9", "metadata": {}, "outputs": [], "source": ["df_return = df_return[\n", "    ~((df_return[\"exp_return_lp\"] == 0) & (df_return[\"total_returns\"] == 0))\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "baaaa8b2-7e81-49b0-bd5f-688ba9f6d810", "metadata": {}, "outputs": [], "source": ["df_return.shape"]}, {"cell_type": "code", "execution_count": null, "id": "69ab15ec-4ec9-4b0a-a8e6-84014ec6acd0", "metadata": {}, "outputs": [], "source": ["df_return[df_return[\"product_id\"].isna()].shape"]}, {"cell_type": "code", "execution_count": null, "id": "28b1b4e2-abdf-4b50-8c75-b5c731c86710", "metadata": {}, "outputs": [], "source": ["df_return = df_return[~df_return[\"product_id\"].isna()]\n", "df_return = df_return[~df_return[\"outlet_id\"].isna()]\n", "df_return = df_return[~df_return[\"suborder_id\"].isna()]"]}, {"cell_type": "code", "execution_count": null, "id": "********-941a-4c0b-b5d5-a34643b396e5", "metadata": {}, "outputs": [], "source": ["# df_return_agg = pd.pivot_table(df_return,index=[day_week_month,'outlet_id','record_created_ts_ist'],values=['exp_return_lp','total_returns','good_returns',\n", "#                                                       'bad_returns','unaccounted','return_loss'],aggfunc=np.sum).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "c04749ef-051c-4f2e-9f43-f8743f544e37", "metadata": {}, "outputs": [], "source": ["# cols = [day_week_month,'outlet_id','exp_return_lp','total_returns','good_returns','bad_returns',\n", "#              'unaccounted','return_loss','record_created_ts_ist']"]}, {"cell_type": "code", "execution_count": null, "id": "50ecb5a5-a744-476d-9215-1e15260198a4", "metadata": {}, "outputs": [], "source": ["# df_return_agg = df_return_agg[cols]"]}, {"cell_type": "code", "execution_count": null, "id": "f12e4caa-d6d4-4128-8949-1d9dc052995f", "metadata": {}, "outputs": [], "source": ["# df_return_agg.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "c7f0c6da-d906-4fa5-94e3-709d41c89a74", "metadata": {}, "outputs": [], "source": ["# df_return_agg.return_loss.sum()"]}, {"cell_type": "code", "execution_count": null, "id": "b43b8732-7753-40be-a4b0-ab6f4dbfa17f", "metadata": {}, "outputs": [], "source": ["primary_key = [day_week_month, \"outlet_id\", \"suborder_id\", \"product_id\"]\n", "sort_key = [day_week_month, \"outlet_id\", \"suborder_id\", \"cart_id\", \"order_id\"]"]}, {"cell_type": "code", "execution_count": null, "id": "13986d7f-33f3-4cbe-87d6-8ccfd26fa414", "metadata": {}, "outputs": [], "source": ["table_name = \"rlpo_returns_v2\"\n", "schema_name = \"consumer\"\n", "column_dtypes = [\n", "    {\n", "        \"name\": day_week_month,\n", "        \"type\": \"timestamp\",\n", "        \"description\": \"date\",\n", "    },\n", "    {\n", "        \"name\": \"outlet_id\",\n", "        \"type\": \"BIGINT\",\n", "        \"description\": \"outlet id\",\n", "    },\n", "    {\n", "        \"name\": \"suborder_id\",\n", "        \"type\": \"BIGINT\",\n", "        \"description\": \"suborder_id\",\n", "    },\n", "    {\n", "        \"name\": \"order_id\",\n", "        \"type\": \"BIGINT\",\n", "        \"description\": \"order id\",\n", "    },\n", "    {\n", "        \"name\": \"cart_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"cart id\",\n", "    },\n", "    {\n", "        \"name\": \"order_type\",\n", "        \"type\": \"VARCHAR(256)\",\n", "        \"description\": \"order_type\",\n", "    },\n", "    {\n", "        \"name\": \"product_id\",\n", "        \"type\": \"float\",\n", "        \"description\": \"product id\",\n", "    },\n", "    {\n", "        \"name\": \"product_name\",\n", "        \"type\": \"VARCHAR(256)\",\n", "        \"description\": \"product_name\",\n", "    },\n", "    {\n", "        \"name\": \"exp_return_lp\",\n", "        \"type\": \"float\",\n", "        \"description\": \"exp_return_lp\",\n", "    },\n", "    {\n", "        \"name\": \"total_returns\",\n", "        \"type\": \"float\",\n", "        \"description\": \"total_returns\",\n", "    },\n", "    {\n", "        \"name\": \"good_returns\",\n", "        \"type\": \"float\",\n", "        \"description\": \"good_returns\",\n", "    },\n", "    {\n", "        \"name\": \"bad_returns\",\n", "        \"type\": \"float\",\n", "        \"description\": \"bad_returns\",\n", "    },\n", "    {\n", "        \"name\": \"unaccounted\",\n", "        \"type\": \"float\",\n", "        \"description\": \"unaccounted\",\n", "    },\n", "    {\n", "        \"name\": \"return_loss\",\n", "        \"type\": \"float\",\n", "        \"description\": \"return_loss\",\n", "    },\n", "    {\n", "        \"name\": \"pre_post_enroute_flag\",\n", "        \"type\": \"float\",\n", "        \"description\": \"pre_post_enroute_flag\",\n", "    },\n", "    {\n", "        \"name\": \"ifo_wo_iro\",\n", "        \"type\": \"boolean\",\n", "        \"description\": \"ifo without iro flag\",\n", "    },\n", "    {\n", "        \"name\": \"record_created_ts_ist\",\n", "        \"type\": \"timestamp\",\n", "        \"description\": \"record_created_ts_ist\",\n", "    },\n", "]\n", "table_description = \"rlpo returns \""]}, {"cell_type": "code", "execution_count": null, "id": "ba82dcff-7026-446e-880c-51454d3eb26a", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": schema_name,  # Redshift schema name\n", "    \"table_name\": table_name,  # Redshift table name\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": primary_key,  # list\n", "    \"sortkey\": sort_key,  # list\n", "    \"incremental_key\": [],  # string\n", "    \"force_upsert_without_increment_check\": True,  # string\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": table_description,  # Description of the table being sent to redshift\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "5abb3d4e-1a59-45f9-b7b7-fd5d5af64cd5", "metadata": {}, "outputs": [], "source": ["pb.to_redshift(df_return, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "3cc00b59-3202-4c21-94e0-7097d35b2479", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3b11b87b-f15f-4cfa-a98b-ca73a952f28d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}