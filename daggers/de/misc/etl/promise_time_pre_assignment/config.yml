dag_name: promise_time_pre_assignment
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
    - mountPath: /usr/local/airflow/tmp
      name: airflow-dags
      subPath: tmp
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: de
owner:
  name: deepu
  slack_id: U03RF701ZQV
path: de/misc/etl/promise_time_pre_assignment
paused: true
project_name: misc
schedule:
  interval: 0 */12 * * *
  start_date: '2019-10-01T00:00:00'
schedule_type: fixed
sink:
  column_dtypes:
  - name: city
    type: VARCHA<PERSON>(50)
  - name: outlet
    type: VARCHAR(50)
  - name: delivery_date
    type: TIMESTAMP WITH TIME ZONE
  - name: pre_assignment_time
    type: FLOAT4
  - name: total_order_count
    type: INTEGER
  conn_id: redshift_consumer
  conn_type: postgres
  incremental_key: delivery_date
  load_type: upsert
  primary_key:
  - delivery_date
  - city
  - outlet
  schema: metrics
  sortkey:
  - delivery_date
  - city
  - outlet
  table: promise_time_pre_assignment
sla: 120 minutes
source:
  bulk:
    parameters:
      end_date: '''2019-05-31'''
      start_date: '''2019-05-30'''
  conn_id: redshift_consumer
  conn_type: postgres
  incremental:
    parameters:
      end_date: '''{{ ds }}'''
      start_date: '''{{ macros.ds_add(ds, -2) }}'''
  infer:
    parameters:
      end_date: current_date
      start_date: DATEADD(day, -1, current_date)
support_files: []
tags: []
template_name: sql
version: 1
