dag_name: promise_time
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
    - mountPath: /usr/local/airflow/tmp
      name: airflow-dags
      subPath: tmp
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: de
owner:
  name: deepu
  slack_id: U03RF701ZQV
path: de/misc/etl/promise_time
paused: true
project_name: misc
schedule:
  interval: 0 */12 * * *
  start_date: '2019-10-01T00:00:00'
schedule_type: fixed
sink:
  column_dtypes:
  - name: city
    type: VARCHAR(50)
  - name: scheduled_time
    type: TIM<PERSON><PERSON><PERSON> WITH TIME ZONE
  - name: lm_pm
    type: FLOAT4
  conn_id: redshift_consumer
  conn_type: postgres
  incremental_key: scheduled_time
  load_type: upsert
  primary_key:
  - scheduled_time
  - city
  schema: metrics
  sortkey:
  - scheduled_time
  - city
  table: promise_time_last_mile
sla: 120 minutes
source:
  bulk:
    parameters:
      utc_timestamp_where_condition: '>= ''2019-05-31'''
      where_condition: '>= ''2019-05-31'''
  conn_id: redshift_consumer
  conn_type: postgres
  incremental:
    parameters:
      utc_timestamp_where_condition: BETWEEN Date('{{ macros.ds_add(ds, -2) }}') +
        interval '18 hour' + interval '30 mins' AND Date('{{ ds }}') + interval '18
        hour' + interval '30 mins')
      where_condition: BETWEEN '{{ macros.ds_add(ds, -2) }}' AND '{{ ds }}'
  infer:
    parameters:
      utc_timestamp_where_condition: BETWEEN Date(DATEADD(day, -1, current_date))
        + interval '18 hour' + interval '30 mins' AND Date(current_date) + interval
        '18 hour' + interval '30 mins')
      where_condition: BETWEEN DATEADD(day, -1, current_date) and current_date
support_files: []
tags: []
template_name: sql
version: 1
