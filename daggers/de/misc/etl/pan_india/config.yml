template_name: sql
dag_name: pan_india
version: 1
owner:
  name: deepu
  slack_id: U03RF701ZQV
schedule:
  start_date: '2019-10-01T04:00:00'
  interval: '0 4 * * *'
source:
  conn_type: postgres
  conn_id: redshift_consumer
  infer:
    parameters:
      where_condition: " >= '2019-08-28' "
  bulk:
    parameters:
      where_condition: " >= '2018-10-01' "
  incremental:
    parameters:
      where_condition: " BETWEEN '{{ macros.ds_add(ds, -1) }}' AND '{{ ds }}' "
sink:
  conn_type: postgres
  conn_id: redshift_consumer
  schema: metrics
  table: assortment_pan_india
  primary_key: [order_date]
  sortkey: [order_date]
  incremental_key: order_date
  load_type: upsert
  column_dtypes:
  - name: order_date
    type: date
  - name: distinct_items_pan_india
    type: bigint
dag_type: etl
escalation_priority: low
schedule_type: fixed
sla: 120 minutes
support_files: []
namespace: de
project_name: misc
paused: true
path: de/misc/etl/pan_india
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
tags: []
executor:
  type: kubernetes
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    node_selectors:
      nodetype: spot
    tolerations:
    - key: service
      operator: Equal
      value: airflow
      effect: NoSchedule
    cpu:
      request: 0.5
      limit: 1
    memory:
      request: 500M
      limit: 4G
    volume_mounts:
    - name: airflow-dags
      subPath: airflow/plugins
      mountPath: /usr/local/airflow/plugins
    - name: airflow-dags
      subPath: tmp
      mountPath: /usr/local/airflow/tmp
