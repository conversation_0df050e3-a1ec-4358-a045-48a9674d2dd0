template_name: sql
dag_name: pl_test
version: 1
owner:
  name: deepu
  slack_id: U03RF701ZQV

schedule:
  start_date: '2019-10-01T00:00:00'
  interval: '0 4 * * *'

source:
  conn_type: postgres
  conn_id: redshift_consumer
  infer:
    parameters:
      where_condition: " >= '2019-08-28' "
  bulk:
    parameters:
      where_condition: " >= '2018-10-01' "
  incremental:
    parameters:
      where_condition: " BETWEEN '{{ macros.ds_add(ds, -1) }}' AND '{{ ds }}' "
sink:
  conn_type: postgres
  conn_id: redshift_consumer
  schema: metrics
  table: doi_pl_test
  primary_key: [facility_id, date_of_snapshot, is_pl]
  sortkey: [date_of_snapshot, city, facility_name]
  incremental_key: date_of_snapshot
  load_type: upsert
  column_dtypes: []
dag_type: etl
escalation_priority: low
schedule_type: fixed
sla: 120 minutes
support_files: []
namespace: de
project_name: misc
paused: true
path: de/misc/etl/pl_test
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
tags: []
executor:
  type: kubernetes
  config:
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    node_selectors:
      nodetype: spot
    tolerations:
    - key: service
      operator: Equal
      value: airflow
      effect: NoSchedule
    cpu:
      request: 0.5
      limit: 1
    memory:
      request: 500M
      limit: 4G
    volume_mounts:
    - name: airflow-dags
      subPath: airflow/plugins
      mountPath: /usr/local/airflow/plugins
    - name: airflow-dags
      subPath: tmp
      mountPath: /usr/local/airflow/tmp
