alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: order_projections_test
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: airflow-dags-spot
    service_account_name: blinkit-prep-airflow-prep-prep-eks-role
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: de
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RURMNJJH
path: de/misc/etl/order_projections_test
paused: false
project_name: misc
schedule:
  end_date: '2024-09-02T00:00:00'
  interval: 30 12 * * *
  start_date: '2024-06-05T00:00:00'
schedule_type: fixed
sla: 121 minutes
support_files:
- daus_train_end_20240515.csv
- orders_train_end_20240515.csv
- new_store_city_trend.csv
- nsa_store_city_trend.csv
tags: []
template_name: notebook
version: 7
