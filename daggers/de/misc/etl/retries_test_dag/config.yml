alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: retries_test_dag
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prep-airflow-prep-prep-eks-role
    volume_mounts: []
  type: kubernetes
namespace: de
notebook:
  parameters: null
  retries: 4
owner:
  email: <EMAIL>
  slack_id: U08A6EPAZ0W
path: de/misc/etl/retries_test_dag
paused: false
pool: de_pool
project_name: misc
schedule:
  end_date: '2025-05-10T00:00:00'
  interval: 0 0 * * *
  start_date: '2025-02-14T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
