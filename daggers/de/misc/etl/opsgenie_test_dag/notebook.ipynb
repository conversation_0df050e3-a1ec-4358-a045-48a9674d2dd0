{"cells": [{"cell_type": "code", "execution_count": null, "id": "ef72b7d6-1cd1-4b7d-a728-db176cedb8fb", "metadata": {}, "outputs": [], "source": ["import json\n", "import pandas as pd\n", "import pencilbox as pb\n", "import sqlalchemy as sqla\n", "from datetime import datetime\n", "from airflow.providers.opsgenie.hooks.opsgenie_alert import OpsgenieAlertHook"]}, {"cell_type": "code", "execution_count": null, "id": "58b8edef-163b-44dc-8cde-53a524b715d2", "metadata": {}, "outputs": [], "source": ["ALERTING_DAYS = 7\n", "today = str((datetime.now()).strftime(\"%Y/%m/%d\"))\n", "SHEET_ID = \"1QqmJLblPCPHSbvZcagfRuoYIArl5FL6RaowtpLmeg1U\"\n", "GID = \"647173616\""]}, {"cell_type": "code", "execution_count": null, "id": "ac3a4f60-e030-4d85-9794-8cf48f5deb41", "metadata": {}, "outputs": [], "source": ["airflow = \"dse/postgres/airflow/\"\n", "PLATFORMS = [\"airflow_ro\", \"airflow_de_ro\"]"]}, {"cell_type": "code", "execution_count": null, "id": "6268e7f1-52be-48c1-8280-c74c729f8047", "metadata": {}, "outputs": [], "source": ["def fetch_active_dags(platform):\n", "    conn = sqla.create_engine(pb.get_secret(airflow + platform)[\"uri\"])\n", "    df_scheduled = pd.read_sql_query(\n", "        f\"\"\"\n", "    SELECT \n", "        s.dag_id,\n", "        s.data->'dag'->'default_args'->'__var'->>'owner' AS email,\n", "        s.data->'dag'->'default_args'->'__var'->>'slack_id' AS slack_id,\n", "        s.data->'dag'->'default_args'->'__var'->'end_date'->>'__var' AS end_date,\n", "        s.fileloc AS dag_path,\n", "        s.data->'dag'->'default_args'->'__var'->>'opsgenie_priority' AS opsgenie_priority,\n", "        s.data->'dag'->'default_args'->'__var'->>'opsgenie_responders' AS opsgenie_responders\n", "    FROM \n", "        serialized_dag AS s\n", "    INNER JOIN\n", "        dag AS d\n", "        ON d.dag_id = s.dag_id\n", "    WHERE\n", "        d.is_paused = FALSE\n", "        AND d.is_active = TRUE\n", "    \"\"\",\n", "        conn,\n", "    )\n", "\n", "    df_scheduled[\"dag_id\"] = df_scheduled[\"dag_id\"].astype(str)\n", "    df_scheduled.loc[:, \"dag_id\"] = df_scheduled[\"dag_id\"].apply(lambda x: x.rsplit(\"_v\", 1)[0])\n", "    df_scheduled[\"email\"] = df_scheduled[\"email\"].astype(str)\n", "    df_scheduled[\"dag_path\"] = df_scheduled[\"dag_path\"].astype(str)\n", "    df_scheduled[\"dag_path\"] = (\n", "        df_scheduled[\"dag_path\"]\n", "        .str.split(\"/\")\n", "        .apply(lambda x: x[-5:-1])\n", "        .apply(lambda x: \"/\".join(x))\n", "    )\n", "    df_scheduled[\"end_date\"] = df_scheduled[\"end_date\"].astype(float)\n", "    df_scheduled[\"platform\"] = platform.rsplit(\"_ro\", 1)[0]\n", "    df_scheduled[\"slack_id\"] = df_scheduled[\"slack_id\"].apply(\n", "        lambda x: (\n", "            x\n", "            if not x\n", "            else (\n", "                f\"<@{x}>\"\n", "                if x.startswith(\"U\")\n", "                else (f\"<!subteam^{x}>\" if x.startswith(\"S\") else None)\n", "            )\n", "        )\n", "    )\n", "    # Parse opsgenie_responders only if present\n", "    df_scheduled[\"opsgenie_responders\"] = df_scheduled[\"opsgenie_responders\"].apply(\n", "        lambda x: json.loads(x) if x else None\n", "    )\n", "\n", "    return df_scheduled"]}, {"cell_type": "code", "execution_count": null, "id": "53871594-fa25-45c6-b348-9873fb9e82a1", "metadata": {}, "outputs": [], "source": ["def send_opsgenie_expiry_alert(\n", "    dag_id, dag_path, email, remaining_days, expiry_date, priority, responders\n", "):\n", "    message = f\"Airflow DAG {dag_id} is expiring in {remaining_days} days on {expiry_date.strftime('%Y-%m-%d')}.\"\n", "    payload = {\n", "        \"message\": message,\n", "        \"description\": f\"DAG {dag_id} at {dag_path} owned by {email} will expire soon. Please extend the end_date.\",\n", "        \"responders\": responders,\n", "        \"tags\": [\"Data Engineering\", \"Airflow\", \"Expiry\"],\n", "        \"details\": {\n", "            \"Dag ID\": dag_id,\n", "            \"Dag Path\": dag_path,\n", "            \"Owner\": email,\n", "            \"Remaining Days\": str(remaining_days),\n", "            \"Expiry Date\": expiry_date.strftime(\"%Y-%m-%d\"),\n", "        },\n", "        \"priority\": priority,\n", "    }\n", "    hook = OpsgenieAlertHook(\"opsgenie_default\")\n", "    hook.execute(payload)"]}, {"cell_type": "code", "execution_count": null, "id": "6bf2678a-d308-457a-a525-284c8ad14e69", "metadata": {}, "outputs": [], "source": ["df_active_dags = pd.DataFrame()\n", "for platform in PLATFORMS:\n", "    df_active_dags = df_active_dags.append(fetch_active_dags(platform))"]}, {"cell_type": "code", "execution_count": null, "id": "cc7c7d17-4021-4c75-bfcf-a2b148c74db9", "metadata": {}, "outputs": [], "source": ["df_scheduled_no_nan = df_active_dags.dropna(subset=[\"end_date\"])\n", "\n", "df_scheduled_no_nan.loc[:, \"end_date\"] = df_scheduled_no_nan.loc[:, \"end_date\"].apply(\n", "    lambda x: datetime.fromtimestamp(x).replace(hour=0, minute=0, second=0, microsecond=0)\n", ")\n", "\n", "# end_date of '2023-12-01' means '2023-12-01 00:00:00' so the last day of dag run will be '2023-11-30'\n", "df_scheduled_no_nan.loc[:, \"remaining_days\"] = df_scheduled_no_nan[\"end_date\"].apply(\n", "    lambda x: (x - datetime.now()).days\n", ")\n", "df_scheduled_no_nan = df_scheduled_no_nan[\n", "    (df_scheduled_no_nan[\"remaining_days\"] >= -7)\n", "    & (df_scheduled_no_nan[\"remaining_days\"] <= ALERTING_DAYS)\n", "]\n", "df_scheduled_about_to_expire = df_scheduled_no_nan[(df_scheduled_no_nan[\"remaining_days\"] >= 0)]\n", "df_already_expired = df_scheduled_no_nan[(df_scheduled_no_nan[\"remaining_days\"] < 0)]\n", "df_scheduled_about_to_expire = df_scheduled_about_to_expire.sort_values(\n", "    by=[\"remaining_days\", \"email\", \"dag_path\"]\n", ")\n", "df_already_expired = df_already_expired.sort_values(by=[\"remaining_days\", \"email\", \"dag_path\"])\n", "df_scheduled_no_nan = df_scheduled_about_to_expire.append(df_already_expired)\n", "df_expiring = df_scheduled_no_nan.rename(columns={\"end_date\": \"expiry_date\"})"]}, {"cell_type": "code", "execution_count": null, "id": "0e6d64e6-2810-4d87-b013-ad1c0cd18375", "metadata": {}, "outputs": [], "source": ["df_expiring.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "f59c727b-fdc8-4569-b0a3-4012c196db2a", "metadata": {}, "outputs": [], "source": ["df_expiring_tag_users = df_expiring[df_expiring[\"remaining_days\"] >= 0]\n", "tag_users = \" \".join(list(set(df_expiring_tag_users[\"slack_id\"].dropna())))\n", "message = f\"\"\"*:loudspeaker: Expiring dags on Airflow*\n", "\\n The following dags would expire in the next 7 days.\n", "\\n Please raise a PR and extend the end date to avoid cumbersome predicaments in the future\n", "\\n eg. `dagger extend_end_date -d 45 -p povms/assortment/etl/mydag_1,consumer/misc/etl/mydag_1`\n", "\\n ^^generates a PR to extend the end date of the above 2 dags by 45 days\n", "\\n https://docs.google.com/spreadsheets/d/{SHEET_ID}\\n {tag_users}\"\"\"\n", "print(message)"]}, {"cell_type": "code", "execution_count": null, "id": "e4765a1c-f6ab-47bc-a991-adfd92f43ba2", "metadata": {}, "outputs": [], "source": ["df_to_send_gsheet = df_expiring[[\"dag_path\", \"email\", \"remaining_days\", \"expiry_date\", \"platform\"]]\n", "df_to_send_gsheet = df_to_send_gsheet.reset_index(drop=True)\n", "df_to_send_mail = df_to_send_gsheet.copy()\n", "df_to_send_mail = df_to_send_mail[df_to_send_mail[\"remaining_days\"] >= 0]\n", "df_to_send_mail = df_to_send_mail.reset_index(drop=True)\n", "dftable = df_to_send_mail.to_html(\n", "    classes=\"table table-striped table-bordered table-hover table-condensed\"\n", ")\n", "html = \"\"\"\n", "<head>\n", "  <meta charset=\"utf-8\">\n", "  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n", "  <link rel=\"stylesheet\" href=\"https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css\">\n", "  <script src=\"https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js\"></script>\n", "  <script src=\"https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js\"></script>\n", "  <style>\n", "table {\n", "  border-collapse: collapse;\n", "  width: 100%;\n", "}\n", "\n", "th {\n", "  text-align: center;\n", "  padding: 8px;\n", "}\n", "\n", "td {\n", "  text-align: left;\n", "  padding: 8px;\n", "}\n", "\n", "tr:nth-child(even){background-color: #FFD5D5}\n", "\n", "th {\n", "  background-color: #ff8400;\n", "  color: white;\n", "}\n", "</style>\n", "\n", "</head>\"\"\" + \"\"\" <h2> Expiring dags on Airflow </h2> <p> The following dags would expire in the next 7 days.\n", "\n", " Please raise a PR and extend the end date to avoid cumbersome predicaments in the future </p>\n", "{table}\n", "<br/>\n", "\n", "\"\"\".format(\n", "    table=dftable,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7afc2f24-87d1-4d40-a2b9-9542e90401d8", "metadata": {}, "outputs": [], "source": ["# Send Opsgenie alerts only if both priority and responders are present\n", "if not df_expiring_tag_users.empty:\n", "    for _, row in df_expiring_tag_users.iterrows():\n", "        if row[\"opsgenie_priority\"] and row[\"opsgenie_responders\"]:  # Check for non-None values\n", "            send_opsgenie_expiry_alert(\n", "                dag_id=row[\"dag_id\"],\n", "                dag_path=row[\"dag_path\"],\n", "                email=row[\"email\"],\n", "                remaining_days=row[\"remaining_days\"],\n", "                expiry_date=row[\"expiry_date\"],\n", "                priority=row[\"opsgenie_priority\"],\n", "                responders=row[\"opsgenie_responders\"],\n", "            )"]}, {"cell_type": "code", "execution_count": null, "id": "eceb74a1-eea0-454e-98c2-d6495f44734c", "metadata": {}, "outputs": [], "source": ["# # Send whenever there are expiring as well as expired dags\n", "# if not df_to_send_gsheet.empty:\n", "#     pb.to_sheets(df_to_send_gsheet, SHEET_ID, \"expiring dags\")\n", "\n", "# # Send only when there are expiring dags\n", "# if not df_to_send_mail.empty:\n", "#     pb.send_email(\n", "#         from_email=\"<EMAIL>\",\n", "#         to_email=list(df_to_send_mail[\"email\"]),\n", "#         cc=[\"<EMAIL>\"],\n", "#         subject=\"Airflow Expiring dags {date}\".format(date=today),\n", "#         html_content=html,\n", "#         mime_subtype=\"mixed\",\n", "#         mime_charset=\"utf-8\",\n", "#     )\n", "#     pb.send_slack_message(channel=\"bl-data-support\", text=message)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}