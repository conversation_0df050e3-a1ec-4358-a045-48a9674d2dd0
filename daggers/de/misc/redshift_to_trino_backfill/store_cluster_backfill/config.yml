alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: store_cluster_backfill
dag_type: redshift_to_trino_backfill
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: airflow-dags-spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
kwargs:
  partition_key: []
  primary_key:
  - timestamptz
  - superstore
  - outlet_id
  redshift_table_name: consumer.store_level_clustering
  select_sql_query: select distinct * from consumer.store_level_clustering where date(timestamptz)
    > date('2023-06-01')
  trino_table_name: consumer_etls.store_level_clustering
namespace: de
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U082S793ANM
path: de/misc/redshift_to_trino_backfill/store_cluster_backfill
paused: false
pool: de_pool
project_name: misc
schedule_type: fixed
sla: 120 minutes
support_files: []
tags:
- redshift_to_trino_backfill
template_name: redshift_to_trino_backfill
version: 1
