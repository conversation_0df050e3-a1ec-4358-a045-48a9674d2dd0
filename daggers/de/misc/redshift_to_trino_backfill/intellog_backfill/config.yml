alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: intellog_backfill
dag_type: redshift_to_trino_backfill
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: airflow-dags-spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
kwargs:
  partition_key:
  - created_at_dt
  primary_key:
  - pid
  - city
  - created_at_dt
  - bigbasket_id
  - flipkart_id
  - amazon_id
  - jiomart_id
  redshift_table_name: consumer.intellolabs_gr_kvi_mapping_onetomany
  select_sql_query: select distinct pid, city, title, item_size, multiplier, coalesce(nullif(bigbasket_id,''),'-1')
    as bigbasket_id, nullif(bigbasket_url,''),nullif(bigbasket_mrp,'')::real, coalesce((case
    when trim(bigbasket_selling_price) ~ '^[0-9]+(\.[0-9]+)?$' then bigbasket_selling_price
    else null end)::real,-1) as bigbasket_selling_price, round(nullif(bigbasket_availability,'')::float)::int
    as bigbasket_availability, coalesce(nullif(flipkart_id,''),'-1') as flipkart_id,
    nullif(flipkart_url,''), nullif(flipkart_mrp,'')::real, coalesce((case when trim(flipkart_selling_price)
    ~ '^[0-9]+(\.[0-9]+)?$' then flipkart_selling_price else null end)::real,-1) as
    flipkart_selling_price, round(nullif(flipkart_availability,'')::float)::int as
    flipkart_availability, coalesce(nullif(amazon_id,''),'-1') as amazon_id, nullif(amazon_url,''),
    nullif(amazon_mrp,'')::real, coalesce((case when trim(amazon_selling_price) ~
    '^[0-9]+(\.[0-9]+)?$' then amazon_selling_price else null end)::real,-1) as amazon_selling_price,
    round(nullif(amazon_availability,'')::float)::int as amazon_availability, created_at::date
    as created_at_dt, coalesce(nullif(jiomart_id,''),'-1') as jiomart_id, nullif(jiomart_url,''),
    nullif(jiomart_mrp,'')::real, coalesce((case when trim(jiomart_selling_price)
    ~ '^[0-9]+(\.[0-9]+)?$' then jiomart_selling_price else null end)::real,-1) as
    jiomart_selling_price, round(nullif(jiomart_availability,'')::float)::int as jiomart_availability  from
    consumer.intellolabs_gr_kvi_mapping_onetomany where created_at::date < cast('2024-02-07'
    as date)
  trino_table_name: consumer_etls.intellolabs_gr_kvi_mapping_onetomany
namespace: de
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U082S793ANM
path: de/misc/redshift_to_trino_backfill/intellog_backfill
paused: false
pool: de_pool
project_name: misc
schedule_type: fixed
sla: 120 minutes
support_files: []
tags:
- redshift_to_trino_backfill
template_name: redshift_to_trino_backfill
version: 1
