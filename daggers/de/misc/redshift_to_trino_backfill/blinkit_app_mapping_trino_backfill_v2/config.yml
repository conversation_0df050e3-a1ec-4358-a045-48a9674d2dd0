alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: blinkit_app_mapping_trino_backfill_v2
dag_type: redshift_to_trino_backfill
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: airflow-dags-spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
kwargs:
  partition_key:
  - created_at_dt
  primary_key:
  - created_at
  - pid
  - city
  - multiplier
  - swiggy_productcode
  - swiggy_selling_price
  - zepto_productcode
  - zepto_selling_price
  redshift_table_name: consumer.intellolabs_blinkit_app_mapping_onetomany
  select_sql_query: with base as ( select distinct date(created_at) as created_at_dt,
    created_at, pid, city, title, size, multiplier, coalesce(nullif(swiggy_productcode,''),'-1')
    as swiggy_productcode, swiggy_mrp, coalesce((case when trim(swiggy_selling_price)
    ~ '^[0-9]+(\.[0-9]+)?$' then swiggy_selling_price else null end),'-1') as swiggy_selling_price,
    coalesce(swiggy_availability,-1) as swiggy_availability, coalesce(nullif(zepto_productcode,''),'-1')
    as zepto_productcode, zepto_mrp, coalesce((case when trim(zepto_selling_price)
    ~ '^[0-9]+(\.[0-9]+)?$' then zepto_selling_price else null end),'-1') as zepto_selling_price,
    coalesce(zepto_availability,-1) as zepto_availability from consumer.intellolabs_blinkit_app_mapping_onetomany
    where created_at < '2024-02-08 00:00:00'::timestamp ) select distinct created_at_dt,
    created_at, pid, city, title, size, multiplier, swiggy_productcode, swiggy_mrp,
    swiggy_selling_price, swiggy_availability, zepto_productcode, zepto_mrp, zepto_selling_price,
    zepto_availability from (select * , row_number() over(partition by created_at,
    pid,city,multiplier,swiggy_productcode,swiggy_selling_price,zepto_productcode,zepto_selling_price
    order by coalesce(nullif(nullif(swiggy_availability,0),-1),zepto_availability)
    desc) as rnk from base) where rnk = 1
  trino_table_name: consumer_etls.intellolabs_blinkit_app_mapping_onetomany
namespace: de
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U082S793ANM
path: de/misc/redshift_to_trino_backfill/blinkit_app_mapping_trino_backfill_v2
paused: false
pool: de_pool
project_name: misc
schedule_type: fixed
sla: 120 minutes
support_files: []
tags:
- redshift_to_trino_backfill
template_name: redshift_to_trino_backfill
version: 1
