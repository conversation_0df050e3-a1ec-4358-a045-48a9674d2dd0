#!/usr/bin/env python
# coding: utf-8

import pencilbox as pb
import tempfile
import time
import os
import boto3
import sqlalchemy
import logging
from sqlalchemy.exc import SQLAlchemyError

logger = logging.getLogger(__name__)

# Debug mode StarRocks connection configuration
DEBUG_STARROCKS_CONFIG = {
    "username": "root",
    "password": "",
    "host": "localhost",
    "port": "9030"
}

def get_starrocks_engine(schema, debug_mode=False, starrocks_config=None):
    """Get StarRocks connection engine for a specific schema"""
    try:
        logger.info("Retrieving StarRocks connection details")

        if debug_mode:
            logger.info("DEBUG mode: Using local StarRocks connection")
            starrocks_secret = DEBUG_STARROCKS_CONFIG
        else:
            secret_path = starrocks_config.get("secret_path", "data/services/planner-platform/preprod/databases/starrocks/capacity_planner")
            starrocks_secret = pb.get_secret(secret_path)

        # Add charset and binary_prefix parameters to fix Unicode warnings
        starrocks_uri = f"mysql+pymysql://{starrocks_secret['username']}:{starrocks_secret['password']}@{starrocks_secret['host']}:{starrocks_secret['port']}/{schema}?charset=utf8mb4&binary_prefix=true"
        masked_uri = starrocks_uri.replace(starrocks_secret['password'], '******')
        logger.info(f"Creating StarRocks engine with connection: {masked_uri}")

        starrocks_engine = sqlalchemy.create_engine(starrocks_uri)
        logger.info(f"StarRocks connection established to {starrocks_secret['host']}:{starrocks_secret['port']}")
        return starrocks_engine
    except Exception as e:
        logger.error(f"Failed to establish StarRocks connection: {str(e)}")
        raise

# Global engine variable for backward compatibility
starrocks_engine = None

def ensure_starrocks_schema(source_schema_df, base_table_name, primary_key, schema, model_id=None, debug_mode=False, starrocks_config=None):
    """
    Ensure StarRocks table exists with all necessary columns based on source schema

    Args:
        source_schema_df (DataFrame): DataFrame containing column names and data types from source
        base_table_name (str): Base name of the table (without model suffix)
        primary_key (list): List of primary key columns
        schema (str): Database schema name
        model_id (str, optional): Model ID to add as a column
    """
    logger.info(f"Ensuring StarRocks schema for table: {base_table_name}")

    try:
        engine = get_starrocks_engine(schema, debug_mode=debug_mode, starrocks_config=starrocks_config)
        with engine.connect() as conn:
            # Create schema if it doesn't exist
            if debug_mode:
                logger.info(f"[DEBUG] Would create database: {schema}")
            else:
                conn.execute(f"CREATE DATABASE IF NOT EXISTS {schema}")
                logger.info(f"Created database if not exists: {schema}")

            # Check if table exists
            check_table_sql = f"""
            SELECT COUNT(*) FROM information_schema.tables
            WHERE table_schema = '{schema}' AND table_name = '{base_table_name}'
            """
            table_exists = conn.execute(check_table_sql).fetchone()[0] > 0
            logger.info(f"Table {base_table_name} exists: {table_exists}")

            # Data type mapping from Iceberg/Trino to StarRocks
            data_type_map = {
                "integer": "INT", "bigint": "BIGINT", "double": "DOUBLE",
                "varchar": "STRING", "boolean": "BOOLEAN",
                "timestamp": "DATETIME", "date": "DATE", "real": "FLOAT",
                "decimal": "DECIMAL(38,10)",
                # Simplified types for complex structures
                "array": "VARCHAR(1024)", "map": "VARCHAR(1024)", "row": "VARCHAR(1024)"
            }

            # Process columns
            source_columns = {}
            for _, row in source_schema_df.iterrows():
                col_name = row['column_name']
                data_type = row['data_type'].lower()

                # Extract base type for complex types
                base_type = data_type.split("(")[0] if "(" in data_type else data_type
                source_columns[col_name] = data_type_map.get(base_type, "VARCHAR(255)")

            # Add model_id column
            source_columns['model_id'] = "VARCHAR(50)"
            logger.info(f"Processed {len(source_columns)} columns for table {base_table_name}")

            if not table_exists:
                # Create table with primary keys first
                pk_columns = [f"`{col}` {source_columns[col]} NOT NULL" for col in primary_key if col in source_columns]
                non_pk_columns = [f"`{col}` {col_type} NULL" for col, col_type in source_columns.items()
                                 if col not in primary_key]

                # Combine columns with primary keys first
                columns_def = pk_columns + non_pk_columns
                primary_key_def = ", ".join([f"`{pk}`" for pk in primary_key])

                # Create table SQL
                create_table_sql = f"""
                CREATE TABLE IF NOT EXISTS {schema}.`{base_table_name}` (
                    {", ".join(columns_def)}
                ) ENGINE=OLAP
                PRIMARY KEY({primary_key_def})
                DISTRIBUTED BY HASH(`entity_id`)
                PROPERTIES (
                    "bucket_size" = "4294967296",
                    "compression" = "LZ4",
                    "fast_schema_evolution" = "true",
                    "replicated_storage" = "true",
                    "replication_num" = "3"
                );
                """

                if debug_mode:
                    logger.info(f"[DEBUG] Would create table: {base_table_name}")
                else:
                    conn.execute(create_table_sql)
                    logger.info(f"Created table: {base_table_name}")
            else:
                # Check for missing columns
                columns_query = f"""
                SELECT column_name FROM information_schema.columns
                WHERE table_schema = '{schema}' AND table_name = '{base_table_name}'
                """
                existing_columns = [row[0].lower() for row in conn.execute(columns_query).fetchall()]

                # Add missing columns
                missing_columns = []
                for col, col_type in source_columns.items():
                    if col.lower() not in existing_columns:
                        missing_columns.append(col)
                        is_pk = col in primary_key
                        nullable = "NOT NULL" if is_pk else "NULL"

                        alter_sql = f"""
                        ALTER TABLE {schema}.`{base_table_name}`
                        ADD COLUMN `{col}` {col_type} {nullable}
                        """

                        if debug_mode:
                            logger.info(f"[DEBUG] Would add column: {col}")
                        else:
                            conn.execute(alter_sql)
                            logger.info(f"Added column: {col}")

                if missing_columns:
                    logger.info(f"Added {len(missing_columns)} missing columns")
                else:
                    logger.info(f"No missing columns found")

    except SQLAlchemyError as e:
        logger.error(f"Error ensuring schema: {str(e)}")
        raise

def load_to_starrocks(df, table_name, primary_key, schema, columns_to_update=None, debug_mode=False, starrocks_config=None):
    """
    Load data to StarRocks using Broker Load

    Args:
        df (DataFrame): DataFrame containing the data to load
        table_name (str): Name of the target table
        primary_key (list): List of primary key columns
        schema (str): Database schema name
        columns_to_update (list, optional): List of columns to update on duplicate key

    Returns:
        bool: True if load was successful, False otherwise
    """
    if df.empty:
        logger.info(f"No data to load to {table_name}")
        return False

    logger.info(f"Loading {len(df)} rows to {table_name} using Broker Load")

    # Reorder DataFrame columns to put primary keys first
    ordered_columns = [col for col in primary_key if col in df.columns]
    ordered_columns += [col for col in df.columns if col not in ordered_columns]

    # Reorder the DataFrame
    df = df[ordered_columns]
    logger.info(f"Reordered columns with primary keys first")

    # Create a temporary file for Parquet data
    with tempfile.NamedTemporaryFile(suffix='.parquet', delete=False) as temp_file:
        temp_file_path = temp_file.name
        df.to_parquet(temp_file_path, index=False)
        logger.info(f"Wrote data to temporary file: {temp_file_path}")

    try:
        # Generate a unique label for this load
        label = f"broker_load_{table_name}_{int(time.time())}"
        file_path = temp_file_path

        # Upload to S3 if not in debug mode
        if not debug_mode:
            s3_bucket = starrocks_config.get("s3_bucket", "blinkit-data-staging")
            s3_region = starrocks_config.get("s3_region", "ap-southeast-1")
            s3_key = f"planner/{table_name}/tmp/{os.path.basename(temp_file_path)}"
            s3_path = f"s3://{s3_bucket}/{s3_key}"

            try:
                s3_client = boto3.client('s3')
                logger.info(f"Uploading to S3: {s3_path}")
                s3_client.upload_file(temp_file_path, s3_bucket, s3_key)
                file_path = s3_path
                logger.info(f"Successfully uploaded to S3")
            except Exception as e:
                logger.error(f"S3 upload failed: {str(e)}")
                logger.warning(f"Falling back to local file")

        # Prepare column list for SQL
        columns_str = ", ".join([f"`{col}`" for col in df.columns])

        # Get configuration values
        iam_role_arn = starrocks_config.get("iam_role_arn", "arn:aws:iam::183295456051:role/preprod-blinkit-planner-starrocks-role")
        s3_region = starrocks_config.get("s3_region", "ap-southeast-1")

        # Prepare broker load SQL
        broker_load_sql = f"""
        LOAD LABEL {schema}.{label} (
            DATA INFILE('{file_path}')
            INTO TABLE {table_name}
            FORMAT AS 'parquet'
            ({columns_str})
        )
        WITH BROKER
        (
            "aws.glue.use_aws_sdk_default_behavior" = "true",
            "aws.s3.iam_role_arn" = "{iam_role_arn}",
            "aws.s3.region" = "{s3_region}",
            "aws.s3.use_instance_profile" = "true",
            "aws.s3.use_aws_sdk_default_behavior" = "true"
        )
        PROPERTIES (
            'timeout' = '3600',
            'max_filter_ratio' = '0.1',
            'strict_mode' = 'true'
        )
        """

        if debug_mode:
            logger.info(f"[DEBUG] Would execute Broker Load")
            success = True
        else:
            # Execute Broker Load
            engine = get_starrocks_engine(schema, debug_mode=debug_mode, starrocks_config=starrocks_config)
            with engine.connect() as conn:
                conn.execute(broker_load_sql)
                logger.info(f"Broker Load job submitted with label: {label}")
            return True

        return success

    except Exception as e:
        logger.error(f"Error during Broker Load: {str(e)}")
        return False

    finally:
        # Clean up temporary file
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)
            logger.info(f"Removed temporary file")
