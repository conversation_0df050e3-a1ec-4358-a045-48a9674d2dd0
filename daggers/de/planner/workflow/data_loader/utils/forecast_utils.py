#!/usr/bin/env python
# coding: utf-8

import time
import pandas as pd
import logging

from .starrocks_utils import get_starrocks_engine, ensure_starrocks_schema, load_to_starrocks
from .kafka_utils import send_to_kafka
from config_loader import get_forecast_type_columns

logger = logging.getLogger(__name__)

def process_forecast(forecast_type, entity_config, table_suffix="m1", model_trigger_time=None, debug_mode=False, starrocks_config=None):
    """
    Process a specific forecast type: fetch data, update StarRocks, and send to Kafka

    Args:
        forecast_type (str): Type of forecast (weekly, daily, hourly, custom)
        entity_config (dict): Entity configuration dictionary containing kafka.forecast_types
        table_suffix (str): Suffix for the table name (default: m1)
        model_trigger_time (str): Model trigger time in format 'YYYY-MM-DD HH:MM:SS'
        debug_mode (bool): If True, skip actual operations and only log debug info
        starrocks_config (dict): StarRocks configuration containing connection and S3 settings

    Returns:
        bool: True if data was processed, False otherwise

    Note:
        Kafka publishing is controlled by entity_config.kafka.forecast_types list.
        Only forecast types listed in this configuration will send data to Kafka.
    """
    logger.info(f"Processing forecast type: {forecast_type} with suffix: {table_suffix}")

    # Validate model_trigger_time parameter
    if model_trigger_time is None:
        raise ValueError("model_trigger_time parameter is required")

    logger.info(f"Using model trigger time: {model_trigger_time}")

    # Extract values from entity config
    category = entity_config["category"]
    catalog = entity_config["catalog"]
    schema = entity_config["schema"]
    time_column = entity_config.get("time_column", "updated_ts")

    # Define table names
    table_name = f"planner_{category}_forecasts_{forecast_type}_{table_suffix}"
    base_table_name = f"planner_{category}_forecasts_{forecast_type}"

    # Get configuration for this forecast type
    forecast_configs = entity_config["forecast_types"]
    if forecast_type not in forecast_configs:
        raise ValueError(f"Unknown forecast type: {forecast_type}. Available types: {list(forecast_configs.keys())}")

    forecast_config = forecast_configs[forecast_type]
    primary_key = forecast_config["primary_key"]
    required_columns = get_forecast_type_columns(entity_config, forecast_type)

    logger.info(f"Config for {forecast_type}: primary_key={primary_key}, required_columns={required_columns}")

    try:
        starrocks_engine = get_starrocks_engine(schema, debug_mode=debug_mode, starrocks_config=starrocks_config)
        with starrocks_engine.connect() as conn:
            # Get source schema
            source_schema_query = f"""
            SELECT column_name, data_type
            FROM `{catalog}`.`information_schema`.`columns`
            WHERE table_schema = '{schema}' AND table_name = '{table_name}'
            """
            source_schema_df = pd.read_sql_query(source_schema_query, conn)
            logger.info(f"Retrieved schema with {len(source_schema_df)} columns")

            # Ensure StarRocks table exists with all necessary columns
            ensure_starrocks_schema(source_schema_df, base_table_name, primary_key, schema, table_suffix, debug_mode=debug_mode, starrocks_config=starrocks_config)

            # Prepare columns to select
            source_columns = source_schema_df['column_name'].tolist()
            columns_to_select = required_columns.copy()
            for col in source_columns:
                if col not in required_columns and col != 'model_id':
                    columns_to_select.append(col)

            columns_str = ",\n        ".join([f"`{col}`" for col in columns_to_select])

            # Build date filter based on forecast type
            date_filter = ""
            days_back = 0 if catalog == "blinkit_iceberg_staging" else 7
            if forecast_type == "weekly":
                # For weekly forecasts, filter by week_start_date > current_date - 7 days
                if "week_start_date" in required_columns:
                    date_filter = f" AND `week_start_date` > DATE_SUB(CURDATE(), INTERVAL {days_back} DAY)"
            else:
                # For daily, hourly, and custom forecasts, filter by date > current_date - 7 days
                if "date" in required_columns:
                    date_filter = f" AND `date` > DATE_SUB(CURDATE(), INTERVAL {days_back} DAY)"

            # Count rows to be updated
            count_query = f"""
            SELECT COUNT(*) FROM `{catalog}`.`{schema}`.`{table_name}`
            WHERE `{time_column}` > '{model_trigger_time}'{date_filter}
            """
            row_count = conn.execute(count_query).fetchone()[0]
            logger.info(f"Found {row_count} rows to update")

            if row_count > 0:
                # Fetch data from source
                source_query = f"""
                SELECT {columns_str}
                FROM `{catalog}`.`{schema}`.`{table_name}`
                WHERE `{time_column}` > '{model_trigger_time}'{date_filter}
                """
                source_df = pd.read_sql_query(source_query, conn)
                logger.info(f"Retrieved {len(source_df)} rows from source")

                # Add model_id column
                source_df['model_id'] = table_suffix

                # Determine columns to update (all non-primary key columns)
                columns_to_update = [col for col in source_df.columns if col not in primary_key]

                # Load data to StarRocks
                load_success = load_to_starrocks(
                    df=source_df,
                    table_name=base_table_name,
                    primary_key=primary_key,
                    schema=schema,
                    columns_to_update=columns_to_update,
                    debug_mode=debug_mode,
                    starrocks_config=starrocks_config
                )

                if load_success:
                    logger.info(f"Successfully loaded {len(source_df)} rows")

                    # Check if this forecast type should send data to Kafka
                    kafka_enabled_types = entity_config.get("kafka", {}).get("forecast_types", [])
                    if forecast_type not in kafka_enabled_types:
                        logger.info(f"Skipping Kafka for forecast type '{forecast_type}' (not in forecast_types: {kafka_enabled_types})")
                        return True

                    # Sleeping for 30s, since broker load may take time to execute
                    time.sleep(30)
                    # Fetch data for Kafka
                    kafka_query = f"""
                    SELECT * FROM {schema}.`{base_table_name}`
                    WHERE model_id = '{table_suffix}'
                    AND `{time_column}` > '{model_trigger_time}'{date_filter}
                    """
                    df = pd.read_sql_query(kafka_query, conn)
                    logger.info(f"Retrieved {len(df)} rows for Kafka")

                    # Send to Kafka
                    event_name = f"planner_{category}_forecasts_{forecast_type}"
                    send_to_kafka(df, event_name, entity_config, debug_mode=debug_mode)

                    return True
                else:
                    logger.error(f"Failed to load data")
                    return False
            else:
                logger.info(f"No data to update")
                return True

    except Exception as e:
        logger.error(f"Error processing forecast {forecast_type}: {str(e)}")
        raise
