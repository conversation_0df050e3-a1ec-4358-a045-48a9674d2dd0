alert_configs:
  slack:
  - channel: bl-data-alerts-p1
  - channel: planner-platform
dag_name: data_loader
dag_type: workflow
escalation_priority: medium
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    service_account_name: blinkit-prep-airflow-prep-prep-eks-role
  type: kubernetes
namespace: de
notebooks:
- alias: storeops_forecasts
  executor_config:
    load_type: low
    node_type: spot
  name: notebook
  parameters:
    entity_name: storeops
    ts: '{{ prev_start_date_success }}'
    environment: preprod
  tag: storeops
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/planner/workflow/data_loader
paused: false
project_name: planner
schedule:
  end_date: '2025-12-31T00:00:00'
  interval: 0,30 * * * *
  start_date: '2024-01-01T00:00:00'
schedule_type: fixed
sla: 60 minutes
support_files: 
- entities_config.yaml
- config_loader.py
- utils/forecast_utils.py
- utils/starrocks_utils.py
- utils/kafka_utils.py
tags:
- planner
- forecasting
- capacity-planning
template_name: multi_notebook
version: 1
