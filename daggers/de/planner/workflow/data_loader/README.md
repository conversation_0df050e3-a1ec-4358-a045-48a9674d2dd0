# Planner Workflow Configuration System

This directory contains a generic, YAML-based configuration system for planner workflows that supports multiple entities through a single unified workflow.

## File Structure

```
daggers/de/planner/workflow/data_loader/
├── config.yml               # DAG configuration file (Airflow workflow)
├── entities_config.yaml     # Entity-specific configuration file
├── config_loader.py          # Generic configuration loader utility
├── notebook.ipynb           # Main notebook executed by DAG
├── test_config.py           # Configuration validation script
├── README.md                # This documentation
└── utils/                   # Utilities directory
    ├── forecast_utils.py    # Forecast processing utilities
    ├── starrocks_utils.py   # StarRocks database utilities
    ├── kafka_utils.py       # Kafka messaging utilities
    └── ...                  # Other utility files
```

## Configuration Files

#### Key Parameters Passed from config.yml:

- `entity_name`: Specific entity to process or `null` for all entities
- `model_trigger_days_back`: Number of days back for model trigger time
- `debug_mode`: Enable/disable debug mode (skips actual operations)
- `log_level`: Logging verbosity (DEBUG, INFO, WARNING, ERROR)
- StarRocks configuration (connection, S3, IAM settings)

### Entity Configuration (`entities_config.yaml`)

The `entities_config.yaml` file is organized as follows:

### Global Settings
```yaml
global:
  catalog: "blinkit_iceberg_staging"
  time_column: "updated_ts"
  model_id_column: "model_id"
  kafka:
    connection_id: "[Kafka] prod-data-events"
    topic_template: "blinkit.capacity-planner.{entity}-events"
```

### Standard Forecast Types (Global)
```yaml
# Standard forecast types used across all entities
forecast_types:
  weekly:
    primary_key: ["entity_id", "week_start_date", "model_id"]
  daily:
    primary_key: ["entity_id", "week_start_date", "date", "model_id"]
  hourly:
    primary_key: ["entity_id", "week_start_date", "date", "hour", "model_id"]
  custom:
    primary_key: ["entity_id", "week_start_date", "date", "group_id", "start_ts", "end_ts", "model_id"]
```

**Note**: Required columns are automatically derived from `primary_key + time_column`. The `time_column` (default: "updated_ts") is added automatically if not already present in the primary key.

### Entity Configuration

#### Multi-Category Entity (Recommended)
```yaml
entities:
  storeops:
    categories:
      instore:
        schema: "storeops_etls"
        kafka:
          connection_id: "[Kafka] prod-data-events"
          topic: "blinkit.capacity-planner.storeops-events"
          forecast_types: ["custom", "od_slots"]
        models:
          m1:
            forecast_types: ["weekly", "daily", "hourly", "custom", "od_slots"]
          m2:
            forecast_types: ["weekly", "daily"]
      core:
        schema: "storeops_etls"
        kafka:
          connection_id: "[Kafka] prod-data-events"
          topic: "blinkit.capacity-planner.storeops-events"
          forecast_types: []
        models:
          m1:
            forecast_types: ["weekly", "daily", "hourly", "custom", "od_slots"]
          m2:
            forecast_types: ["weekly", "daily"]
```

#### Single-Category Entity (Legacy)
```yaml
entities:
  warehouse:
    entity: "warehouse"
    category: "warehouse"
    schema: "warehouse_etls"
    models:
      m1:
        forecast_types: ["weekly", "daily"]
```

## Usage

### Multi-Category Entities
```python
from config_loader import (
    load_entity_config,
    get_available_entities,
    get_entity_categories,
    get_entity_models
)

# Get available categories for an entity
categories = get_entity_categories("storeops")
# Returns: ['instore', 'core']

# Load specific category configuration
instore_config = load_entity_config("storeops", "instore")
core_config = load_entity_config("storeops", "core")

# Get models for a specific category
instore_models = get_entity_models("storeops", "instore")
core_models = get_entity_models("storeops", "core")

# Get all available entities
entities = get_available_entities()
```

### Single-Category Entities (Legacy)
```python
# For entities without categories (legacy format)
config = load_entity_config("warehouse")
models = get_entity_models("warehouse")
```

## Using the DAG Workflow

The system is designed to run as an Airflow DAG with multiple notebook execution scenarios defined in `config.yml`.

### Available Execution Modes

#### 1. Production Workflows
```yaml
# Process specific entity (storeops)
alias: storeops_forecasts
parameters:
  entity_name: storeops
  model_trigger_days_back: 2
  debug_mode: false

### Manual Notebook Execution

For development/testing, you can run the notebook manually with papermill:

```bash
# Run specific entity
python main.py

## Adding a New Entity

To add a new entity (e.g., "logistics"), simply add it to the `entities` section in `entities_config.yaml`:

```yaml
entities:
  logistics:
    entity: "logistics"
    category: "logistics"
    schema: "logistics_etls"

    models:
      m1:
        forecast_types: ["hourly", "daily"]
      m2:
        forecast_types: ["weekly"]
```

## Kafka Configuration

### forecast_types

The `forecast_types` setting controls which forecast types send data to Kafka after successful StarRocks loading:

```yaml
kafka:
  connection_id: "[Kafka] prod-data-events"
  topic: "blinkit.capacity-planner.storeops-events"
  forecast_types: ["custom"]  # Only custom forecasts go to Kafka
```

**Behavior:**
- **Included forecast types**: Data is sent to Kafka after StarRocks load
- **Excluded forecast types**: Only loaded to StarRocks, Kafka step is skipped
- **Default**: Empty list (no Kafka publishing)
- **Common usage**: `["custom"]` for event-driven forecasts

**Examples:**
```yaml
# Send only custom forecasts to Kafka
forecast_types: ["custom"]

# Send multiple forecast types to Kafka
forecast_types: ["custom", "hourly"]

# Disable all Kafka publishing
forecast_types: []
```

## Configuration Validation

The system includes built-in validation that checks:

- Required fields are present
- Kafka configuration is complete
- Forecast types are properly defined
- Primary keys and required columns are specified
