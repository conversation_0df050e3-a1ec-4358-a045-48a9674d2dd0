#!/usr/bin/env python

"""Generic configuration loader for planner workflows."""

import os
import yaml
import logging
from typing import Dict, Any, List

logger = logging.getLogger(__name__)

def load_yaml_config(config_path: str = None) -> Dict[str, Any]:
    """Load the main YAML configuration file."""
    if config_path is None:
        config_path = os.path.join(os.path.dirname(__file__), 'entities_config.yaml')

    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Configuration file not found: {config_path}")

    try:
        with open(config_path, 'r') as file:
            config = yaml.safe_load(file)
        logger.info(f"Successfully loaded configuration from {config_path}")
        return config
    except yaml.YAMLError as e:
        logger.error(f"Error parsing YAML configuration: {e}")
        raise
    except Exception as e:
        logger.error(f"Error loading configuration file: {e}")
        raise

def validate_entity_config(entity_name: str, entity_config: Dict[str, Any]) -> None:
    """Validate that an entity configuration has all required fields."""
    required_fields = ['entity', 'category', 'catalog', 'schema', 'kafka', 'forecast_types']

    for field in required_fields:
        if field not in entity_config:
            raise ValueError(f"Missing required field '{field}' in configuration for entity '{entity_name}'")

    kafka_config = entity_config['kafka']
    required_kafka_fields = ['connection_id', 'topic']
    for field in required_kafka_fields:
        if field not in kafka_config:
            raise ValueError(f"Missing required kafka field '{field}' in configuration for entity '{entity_name}'")

    forecast_types = entity_config['forecast_types']
    if not isinstance(forecast_types, dict) or len(forecast_types) == 0:
        raise ValueError(f"Entity '{entity_name}' must have at least one forecast type configured")

    for forecast_type, forecast_config in forecast_types.items():
        required_forecast_fields = ['primary_key']
        for field in required_forecast_fields:
            if field not in forecast_config:
                raise ValueError(f"Missing required field '{field}' in forecast type '{forecast_type}' for entity '{entity_name}'")

def load_entity_config(entity_name: str, config_path: str = None) -> Dict[str, Any]:
    """Load configuration for a specific entity with global defaults applied."""
    config = load_yaml_config(config_path)

    if 'entities' not in config or entity_name not in config['entities']:
        available_entities = list(config.get('entities', {}).keys())
        raise ValueError(f"Entity '{entity_name}' not found in configuration. Available entities: {available_entities}")

    entity_config = config['entities'][entity_name].copy()
    global_config = config.get('global', {})

    # Apply global defaults where not specified
    if 'catalog' not in entity_config and 'catalog' in global_config:
        entity_config['catalog'] = global_config['catalog']

    if 'time_column' not in entity_config and 'time_column' in global_config:
        entity_config['time_column'] = global_config['time_column']

    # Add global column configurations
    entity_config['model_id_column'] = global_config.get('model_id_column', 'model_id')

    # Apply global kafka settings with template support
    if 'kafka' in global_config:
        global_kafka = global_config['kafka']
        entity_kafka = entity_config.get('kafka', {})

        if 'connection_id' not in entity_kafka and 'connection_id' in global_kafka:
            entity_kafka['connection_id'] = global_kafka['connection_id']

        # Generate topic from template if not specified
        if 'topic' not in entity_kafka and 'topic_template' in global_kafka:
            entity_kafka['topic'] = global_kafka['topic_template'].format(entity=entity_name)

        entity_config['kafka'] = entity_kafka

    # Apply global forecast types - these are standard across all entities
    if 'forecast_types' in config:
        entity_config['forecast_types'] = config['forecast_types']

    validate_entity_config(entity_name, entity_config)
    logger.info(f"Successfully loaded and validated configuration for entity '{entity_name}'")
    return entity_config

def get_available_entities(config_path: str = None) -> List[str]:
    """Get a list of all available entities in the configuration."""
    config = load_yaml_config(config_path)
    return list(config.get('entities', {}).keys())

def get_entity_models(entity_name: str, config_path: str = None) -> Dict[str, List[str]]:
    """Get model configurations for a specific entity."""
    entity_config = load_entity_config(entity_name, config_path)
    return entity_config.get('models', {})

def get_forecast_type_columns(entity_config: Dict[str, Any], forecast_type: str) -> List[str]:
    """Get all columns needed for a specific forecast type."""
    forecast_config = entity_config['forecast_types'][forecast_type]
    primary_key = forecast_config['primary_key']

    # Get all columns needed: primary_key + time_column
    columns = primary_key.copy()
    time_column = entity_config.get('time_column', 'updated_ts')
    if time_column not in columns:
        columns.append(time_column)

    return columns

if __name__ == "__main__":
    try:
        storeops_config = load_entity_config("storeops")
        print("Storeops configuration loaded successfully:")
        print(f"Entity: {storeops_config['entity']}")
        print(f"Schema: {storeops_config['schema']}")
        print(f"Kafka topic: {storeops_config['kafka']['topic']}")
        print(f"Available forecast types: {list(storeops_config['forecast_types'].keys())}")

        entities = get_available_entities()
        print(f"\nAvailable entities: {entities}")

        models = get_entity_models("storeops")
        print(f"\nStoreops models: {models}")

    except Exception as e:
        print(f"Error: {e}")
