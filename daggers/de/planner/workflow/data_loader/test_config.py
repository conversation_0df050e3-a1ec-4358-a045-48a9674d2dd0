#!/usr/bin/env python

"""Test script for the YAML configuration system."""

import sys
from config_loader import load_entity_config, get_available_entities, get_entity_models, get_entity_categories

def test_configuration():
    """Test the YAML configuration system"""
    print("=" * 60)
    print("TESTING PLANNER WORKFLOW YAML CONFIGURATION")
    print("=" * 60)

    try:
        print("\n1. Testing available entities...")
        entities = get_available_entities()
        print(f"   Available entities: {entities}")

        if not entities:
            print("   ERROR: No entities found in configuration!")
            return False

        print("\n2. Testing entity configurations...")
        for entity_name in entities:
            print(f"\n   Testing entity: {entity_name}")

            try:
                # Check if entity has categories
                from config_loader import get_entity_categories

                try:
                    categories = get_entity_categories(entity_name)
                    print(f"      Categories: {categories}")

                    # Test each category
                    for category in categories:
                        print(f"\n    Testing category: {category}")
                        config = load_entity_config(entity_name, category)

                        required_fields = ['entity', 'category', 'catalog', 'schema', 'kafka', 'forecast_types']
                        for field in required_fields:
                            if field not in config:
                                print(f"        ERROR: Missing field '{field}'")
                                return False
                            print(f"        ✓ {field}: {config[field] if field != 'forecast_types' else list(config[field].keys())}")

                        models = get_entity_models(entity_name, category)
                        print(f"        ✓ models: {models}")

                        forecast_types = config['forecast_types']
                        print(f"        ✓ forecast_types: {list(forecast_types.keys())}")

                        for forecast_type, forecast_config in forecast_types.items():
                            if 'primary_key' not in forecast_config:
                                print(f"        ERROR: Forecast type '{forecast_type}' missing primary_key")
                                return False

                        print(f"        ✓ Entity '{entity_name}' category '{category}' configuration is valid")

                except ValueError as e:
                    # Single category entity (legacy) - try loading without category
                    if "multiple categories" in str(e):
                        print(f"      ERROR: {str(e)}")
                        return False
                    else:
                        # Try loading without category (legacy format)
                        config = load_entity_config(entity_name)

                        required_fields = ['entity', 'category', 'catalog', 'schema', 'kafka', 'forecast_types']
                        for field in required_fields:
                            if field not in config:
                                print(f"      ERROR: Missing field '{field}'")
                                return False
                            print(f"      ✓ {field}: {config[field] if field != 'forecast_types' else list(config[field].keys())}")

                        models = get_entity_models(entity_name)
                        print(f"      ✓ models: {models}")

                        print(f"      ✓ Entity '{entity_name}' configuration is valid")

            except Exception as e:
                print(f"      ERROR: Failed to load configuration for '{entity_name}': {e}")
                return False
        
        # Test the get_forecast_config function simulation
        print("\n3. Testing forecast configuration retrieval...")
        storeops_config = load_entity_config("storeops", "instore")
        
        # Simulate the get_forecast_config function
        def test_get_forecast_config(forecast_type, config):
            from config_loader import get_forecast_type_columns
            forecast_configs = config["forecast_types"]
            time_column = config.get("time_column", "updated_ts")

            if forecast_type not in forecast_configs:
                raise ValueError(f"Unknown forecast type: {forecast_type}")

            forecast_config = forecast_configs[forecast_type]
            required_columns = get_forecast_type_columns(config, forecast_type)
            return required_columns, forecast_config["primary_key"], time_column
        
        # Test each forecast type for storeops
        for forecast_type in storeops_config['forecast_types'].keys():
            try:
                required_columns, primary_key, time_column = test_get_forecast_config(forecast_type, storeops_config)
                print(f"      ✓ {forecast_type}: columns={len(required_columns)}, pk={len(primary_key)}, time_col={time_column}")
            except Exception as e:
                print(f"      ERROR: Failed to get config for forecast type '{forecast_type}': {e}")
                return False
        
        print("\n4. Testing model-forecast mapping...")
        for entity_name in entities:
            try:
                categories = get_entity_categories(entity_name)

                for category in categories:
                    config = load_entity_config(entity_name, category)
                    models = config.get('models', {})

                    for model_id, model_config in models.items():
                        forecast_types = model_config.get('forecast_types', [])
                        print(f"      ✓ {entity_name}.{category}.{model_id}: {forecast_types}")

                        # Verify all forecast types exist in entity configuration
                        entity_forecast_types = list(config['forecast_types'].keys())
                        for forecast_type in forecast_types:
                            if forecast_type not in entity_forecast_types:
                                print(f"      ERROR: Model {model_id} references unknown forecast type '{forecast_type}'")
                                return False

            except ValueError as e:
                # Legacy single-category entity
                if "multiple categories" not in str(e):
                    config = load_entity_config(entity_name)
                    models = config.get('models', {})

                    for model_id, model_config in models.items():
                        forecast_types = model_config.get('forecast_types', [])
                        print(f"      ✓ {entity_name}.{model_id}: {forecast_types}")

                        # Verify all forecast types exist in entity configuration
                        entity_forecast_types = list(config['forecast_types'].keys())
                        for forecast_type in forecast_types:
                            if forecast_type not in entity_forecast_types:
                                print(f"      ERROR: Model {model_id} references unknown forecast type '{forecast_type}'")
                                return False
                else:
                    raise e
        
        print("\n" + "=" * 60)
        print("✅ ALL TESTS PASSED - Configuration is valid!")
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"\n❌ CONFIGURATION TEST FAILED: {e}")
        print("=" * 60)
        return False

def print_configuration_summary():
    """Print a summary of the current configuration"""
    print("\n" + "=" * 60)
    print("CONFIGURATION SUMMARY")
    print("=" * 60)
    
    try:
        entities = get_available_entities()
        
        for entity_name in entities:
            print(f"\nEntity: {entity_name}")

            try:
                categories = get_entity_categories(entity_name)
                print(f"  Categories: {categories}")

                for category in categories:
                    print(f"\n  Category: {category}")
                    config = load_entity_config(entity_name, category)
                    print(f"    Schema: {config['schema']}")
                    print(f"    Kafka Topic: {config['kafka']['topic']}")

                    # Check forecast_types
                    kafka_enabled_types = config.get('kafka', {}).get('forecast_types', [])
                    print(f"    Kafka Enabled Types: {kafka_enabled_types}")

                    models = config.get('models', {})
                    for model_id, model_config in models.items():
                        forecast_types = model_config.get('forecast_types', [])
                        print(f"    Model {model_id}: {', '.join(forecast_types)}")

                    forecast_types = list(config['forecast_types'].keys())
                    print(f"    Available Forecast Types: {', '.join(forecast_types)}")

                    # Validate forecast_types
                    if kafka_enabled_types:
                        invalid_types = [ft for ft in kafka_enabled_types if ft not in forecast_types]
                        if invalid_types:
                            print(f"    ⚠️  WARNING: Invalid forecast_types: {invalid_types}")
                            print(f"        Valid options: {forecast_types}")

            except ValueError as e:
                # Legacy single-category entity
                if "multiple categories" not in str(e):
                    config = load_entity_config(entity_name)
                    print(f"  Category: {config['category']}")
                    print(f"  Schema: {config['schema']}")
                    print(f"  Kafka Topic: {config['kafka']['topic']}")
                    # ... rest of legacy handling
                else:
                    raise e
    
    except Exception as e:
        print(f"Error generating summary: {e}")

if __name__ == "__main__":
    # Run the tests
    success = test_configuration()
    
    if success:
        print_configuration_summary()
        sys.exit(0)
    else:
        print("\n❌ Configuration validation failed!")
        sys.exit(1)
