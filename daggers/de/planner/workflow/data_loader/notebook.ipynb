{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import logging\n", "import pytz\n", "from datetime import datetime, timedelta\n", "\n", "!pip install PyMySQL==1.1.1"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Configuration parameters - passed from config.yml via papermill\n", "# These parameters are set in config.yml and injected by the DAG framework\n", "ts = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "# Core processing parameters\n", "ENTITY_NAME = \"storeops\"  # Set to specific entity name or None to process all entities\n", "LIST_ENTITIES_ONLY = False  # Set to True to only list available entities\n", "LOG_LEVEL = \"INFO\"  # Logging level\n", "DEBUG_MODE = False  # Set to True to enable debug mode (skip actual operations)\n", "\n", "# Environment configuration - configurable by end user\n", "ENVIRONMENT = \"preprod\"  # Environment setting (preprod, prod, etc.)\n", "\n", "# StarRocks configuration - passed from config.yml\n", "STARROCKS_SECRET_PATH = (\n", "    f\"data/services/planner-platform/{ENVIRONMENT}/databases/starrocks/capacity_planner\"\n", ")\n", "\n", "# Data loader configuration - fetch from vault\n", "DATA_LOADER_VAULT_PATH = f\"data/services/planner-platform/{ENVIRONMENT}/backend/data_loader\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if \"JUPYTERHUB_USER\" in os.environ:\n", "    DEBUG_MODE: bool = True\n", "    cwd = \"/home/<USER>/airflow-de-dags/dags/de/planner/workflow/data_loader\"\n", "    os.ch<PERSON>(cwd)\n", "else:\n", "    DEBUG_MODE: bool = False\n", "    cwd = \"/usr/local/airflow/dags/repo/dags/de/planner/workflow/data_loader\"\n", "    os.ch<PERSON>(cwd)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "from config_loader import load_entity_config, get_available_entities\n", "from utils.forecast_utils import process_forecast"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup logging\n", "logging.basicConfig(\n", "    level=getattr(logging, LOG_LEVEL.upper()),\n", "    format=\"%(asctime)s - %(name)s - %(levelname)s - %(message)s\",\n", "    stream=sys.stdout,\n", "    force=True,\n", ")\n", "logger = logging.getLogger(__name__)\n", "\n", "# Fetch data loader configuration from vault\n", "logger.info(f\"Fetching data loader configuration from vault path: {DATA_LOADER_VAULT_PATH}\")\n", "try:\n", "    data_loader_config = pb.get_secret(DATA_LOADER_VAULT_PATH)\n", "    S3_BUCKET = data_loader_config.get(\"s3_bucket\", \"blinkit-data-staging\")\n", "    S3_REGION = data_loader_config.get(\"s3_region\", \"ap-southeast-1\")\n", "    IAM_ROLE_ARN = data_loader_config.get(\n", "        \"iam_role_arn\", \"arn:aws:iam::183295456051:role/preprod-blinkit-planner-starrocks-role\"\n", "    )\n", "    logger.info(f\"Successfully fetched data loader configuration from vault\")\n", "    logger.info(f\"S3 bucket: {S3_BUCKET}\")\n", "    logger.info(f\"S3 region: {S3_REGION}\")\n", "    logger.info(f\"IAM role ARN: {IAM_ROLE_ARN}\")\n", "except Exception as e:\n", "    logger.warning(f\"Failed to fetch data loader configuration from vault: {str(e)}\")\n", "    logger.warning(\"Using fallback configuration values\")\n", "    # Fallback values - always use preprod\n", "    S3_BUCKET = \"blinkit-data-staging\"\n", "    S3_REGION = \"ap-southeast-1\"\n", "    IAM_ROLE_ARN = \"arn:aws:iam::183295456051:role/preprod-blinkit-planner-starrocks-role\"\n", "\n", "# Calculate model trigger time\n", "if not ts:\n", "    ts = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "# Parse the timestamp and add 5 hours 30 minutes (IST offset)\n", "from datetime import timedelta\n", "\n", "base_time = datetime.fromisoformat(ts)\n", "adjusted_time = base_time + <PERSON><PERSON>ta(hours=5, minutes=30)\n", "MODEL_TRIGGER_TIME = adjusted_time.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "logger.info(f\"Original timestamp: {ts}\")\n", "logger.info(f\"Model trigger time (UTC + 05:30): {MODEL_TRIGGER_TIME}\")\n", "\n", "# Create StarRocks configuration object\n", "STARROCKS_CONFIG = {\n", "    \"secret_path\": STARROCKS_SECRET_PATH,\n", "    \"s3_bucket\": S3_BUCKET,\n", "    \"s3_region\": S3_REGION,\n", "    \"iam_role_arn\": IAM_ROLE_ARN,\n", "}\n", "logger.info(f\"StarRocks configuration initialized\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def process_entity(entity_name, model_trigger_time, debug_mode=False, starrocks_config=None):\n", "    \"\"\"Process all forecasts for a specific entity.\"\"\"\n", "    logger.info(\"=\" * 80)\n", "    logger.info(f\"STARTING {entity_name.upper()} UPDATE PROCESS\")\n", "    logger.info(\"=\" * 80)\n", "\n", "    try:\n", "        logger.info(f\"Using model trigger time: {model_trigger_time}\")\n", "        logger.info(f\"Debug mode: {debug_mode}\")\n", "\n", "        entity_config = load_entity_config(entity_name)\n", "        model_configs = entity_config.get(\"models\", {})\n", "\n", "        if not model_configs:\n", "            logger.warning(f\"No model configurations found for entity '{entity_name}'\")\n", "            return {\"entity\": entity_name, \"processed\": 0, \"total\": 0, \"success\": False}\n", "\n", "        processed_count = 0\n", "        total_forecasts = 0\n", "\n", "        for model_id, model_config in model_configs.items():\n", "            forecast_types = model_config.get(\"forecast_types\", [])\n", "            total_forecasts += len(forecast_types)\n", "\n", "            for forecast_type in forecast_types:\n", "                try:\n", "                    logger.info(f\"Processing {forecast_type} forecasts for model {model_id}\")\n", "                    result = process_forecast(\n", "                        forecast_type,\n", "                        entity_config,\n", "                        table_suffix=model_id,\n", "                        model_trigger_time=MODEL_TRIGGER_TIME,\n", "                        debug_mode=DEBUG_MODE,\n", "                        starrocks_config=STARROCKS_CONFIG,\n", "                    )\n", "                    if result:\n", "                        processed_count += 1\n", "                except Exception as e:\n", "                    logger.error(\n", "                        f\"Failed to process {forecast_type} forecast for model {model_id}: {str(e)}\"\n", "                    )\n", "\n", "        logger.info(\n", "            f\"Completed {processed_count}/{total_forecasts} forecast types for {entity_name}\"\n", "        )\n", "        logger.info(\"=\" * 80)\n", "        logger.info(f\"{entity_name.upper()} UPDATE PROCESS COMPLETED\")\n", "        logger.info(\"=\" * 80)\n", "\n", "        return {\n", "            \"entity\": entity_name,\n", "            \"processed\": processed_count,\n", "            \"total\": total_forecasts,\n", "            \"success\": processed_count > 0,\n", "        }\n", "\n", "    except Exception as e:\n", "        logger.error(f\"Fatal error processing entity '{entity_name}': {str(e)}\")\n", "        return {\n", "            \"entity\": entity_name,\n", "            \"processed\": 0,\n", "            \"total\": 0,\n", "            \"success\": <PERSON><PERSON><PERSON>,\n", "            \"error\": str(e),\n", "        }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def process_all_entities(model_trigger_time, debug_mode=False, starrocks_config=None):\n", "    \"\"\"Process all entities defined in the YAML configuration.\"\"\"\n", "    logger.info(\"=\" * 80)\n", "    logger.info(\"STARTING MULTI-ENTITY UPDATE PROCESS\")\n", "    logger.info(\"=\" * 80)\n", "\n", "    try:\n", "        entities = get_available_entities()\n", "        logger.info(f\"Found {len(entities)} entities to process: {entities}\")\n", "\n", "        results = []\n", "\n", "        for entity_name in entities:\n", "            try:\n", "                result = process_entity(\n", "                    entity_name, MODEL_TRIGGER_TIME, DEBUG_MODE, STARROCKS_CONFIG\n", "                )\n", "                results.append(result)\n", "            except Exception as e:\n", "                logger.error(f\"Failed to process entity '{entity_name}': {str(e)}\")\n", "                results.append(\n", "                    {\n", "                        \"entity\": entity_name,\n", "                        \"processed\": 0,\n", "                        \"total\": 0,\n", "                        \"success\": <PERSON><PERSON><PERSON>,\n", "                        \"error\": str(e),\n", "                    }\n", "                )\n", "\n", "        logger.info(\"=\" * 80)\n", "        logger.info(\"MULTI-ENTITY UPDATE PROCESS SUMMARY\")\n", "        logger.info(\"=\" * 80)\n", "\n", "        total_processed = 0\n", "        total_forecasts = 0\n", "        successful_entities = 0\n", "\n", "        for result in results:\n", "            entity_name = result[\"entity\"]\n", "            processed = result[\"processed\"]\n", "            total = result[\"total\"]\n", "            success = result[\"success\"]\n", "\n", "            status = \"✅ SUCCESS\" if success else \"❌ FAILED\"\n", "            logger.info(f\"{entity_name}: {processed}/{total} forecasts processed - {status}\")\n", "\n", "            total_processed += processed\n", "            total_forecasts += total\n", "            if success:\n", "                successful_entities += 1\n", "\n", "        logger.info(\"=\" * 80)\n", "        logger.info(f\"OVERALL SUMMARY: {successful_entities}/{len(entities)} entities successful\")\n", "        logger.info(f\"TOTAL FORECASTS: {total_processed}/{total_forecasts} processed\")\n", "        logger.info(\"=\" * 80)\n", "\n", "        return results\n", "\n", "    except Exception as e:\n", "        logger.error(f\"Fatal error in multi-entity processing: {str(e)}\")\n", "        return []"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Main execution logic\n", "try:\n", "    # Handle list entities request\n", "    if LIST_ENTITIES_ONLY:\n", "        entities = get_available_entities()\n", "        print(\"Available entities:\")\n", "        for entity in entities:\n", "            print(f\"  - {entity}\")\n", "    else:\n", "        # Process specific entity or all entities\n", "        if ENTITY_NAME:\n", "            # Process single entity\n", "            available_entities = get_available_entities()\n", "            if ENTITY_NAME not in available_entities:\n", "                logger.error(f\"Entity '{ENTITY_NAME}' not found in configuration\")\n", "                logger.error(f\"Available entities: {available_entities}\")\n", "                raise ValueError(f\"Entity '{ENTITY_NAME}' not found\")\n", "\n", "            result = process_entity(ENTITY_NAME, MODEL_TRIGGER_TIME, DEBUG_MODE, STARROCKS_CONFIG)\n", "            if not result[\"success\"]:\n", "                raise RuntimeError(f\"Failed to process entity '{ENTITY_NAME}'\")\n", "        else:\n", "            # Process all entities\n", "            results = process_all_entities(MODEL_TRIGGER_TIME, DEBUG_MODE, STARROCKS_CONFIG)\n", "\n", "            # Check if any entity failed\n", "            failed_entities = [r for r in results if not r[\"success\"]]\n", "            if failed_entities:\n", "                failed_names = [r[\"entity\"] for r in failed_entities]\n", "                logger.error(f\"Some entities failed to process: {failed_names}\")\n", "                raise RuntimeError(f\"Some entities failed: {failed_names}\")\n", "\n", "except Exception as e:\n", "    logger.error(f\"Fatal error: {str(e)}\", exc_info=True)\n", "    raise"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 4}