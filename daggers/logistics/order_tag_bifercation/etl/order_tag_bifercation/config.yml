alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
  - channel: bl-lm-dag-alert
dag_name: order_tag_bifercation
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: low
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: logistics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U082DBVETNX
path: logistics/order_tag_bifercation/etl/order_tag_bifercation
paused: false
pool: logistics_pool
project_name: order_tag_bifercation
schedule:
  end_date: '2025-09-12T00:00:00'
  interval: 00 2 * * *
  start_date: '2024-02-25T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
