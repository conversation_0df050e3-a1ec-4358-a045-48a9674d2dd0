{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql_referral = \"\"\"\n", "SELECT \n", "    (fmur.install_ts + interval '5.5hrs')::date as referral_date,\n", "     CASE WHEN fmur.preferred_work_city ILIKE '%%Up%%Ncr%%' then 'UP-NCR' \n", "            WHEN fmur.preferred_work_city ILIKE  '%%Hr%%Ncr%%'  then 'HR-NCR' \n", "            else fmur.preferred_work_city  \n", "     end as preferred_work_city,\n", "     fmu.old_employee_id as employee_id\n", "FROM \n", "    fleet_management_user_referral as fmurf\n", "INNER JOIN \n", "    fleet_management_user_registration as fmur\n", "            on fmur.user_id=fmurf.user_id\n", "INNER JOIN \n", "    fleet_management_user as fmu\n", "            on fmu.id = fmur.user_id\n", "WHERE fmur.status = 'COMPLETED'\n", "\"\"\"\n", "con_fleet = pb.get_connection(\"[Replica] Fleet Management\")\n", "df_referred_users = pd.read_sql(sql_referral, con_fleet)\n", "df_referred_users.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_referred_users.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql_delivered_orders = f\"\"\"\n", "select \n", "    delivery_fe_id,\n", "    lna.city\n", "from logistics_dark_store_projection\n", "        as p\n", "    inner join logistics_node\n", "        as ln\n", "        on p.dark_store_id=ln.external_id\n", "    inner join logistics_node_address\n", "        as lna\n", "        on ln.node_address_id=lna.id\n", "where p.install_ts>'2022-01-01'::timestamp - interval '5.5 hours'\n", "    AND current_state='DELIVERED'\n", "    and delivery_fe_id in {tuple(df_referred_users.employee_id)}\n", "group by 1,2\n", "\"\"\"\n", "con_redshift = pb.get_connection(\"[Replica] Logistic DB\")\n", "df_emps_who_delivered_order = pd.read_sql(sql_delivered_orders, con_redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_emps_who_delivered_order.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = pd.merge(\n", "    df_referred_users,\n", "    df_emps_who_delivered_order,\n", "    left_on=\"employee_id\",\n", "    right_on=\"delivery_fe_id\",\n", "    how=\"inner\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final[[\"referral_date\", \"preferred_work_city\", \"employee_id\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    final,\n", "    \"1frgqTxWG71XJQz8boOIC8FNY_jzaFU56ptd8rkQsRXE\",\n", "    \"Emps who delivered order\",\n", "    service_account=\"service_account\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}