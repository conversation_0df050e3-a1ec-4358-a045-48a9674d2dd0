alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: referral-employees-fleet
dag_type: report
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: logistics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U02B56LSV1A
path: logistics/referral-employees-fleet/report/referral-employees-fleet
paused: true
project_name: referral-employees-fleet
schedule:
  interval: 45 */1 * * *
  start_date: '2022-04-25T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
