{"cells": [{"cell_type": "code", "execution_count": null, "id": "58949e87-1536-4f8d-bb49-59826416d0c9", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "id": "5479c0e9-ead9-4c22-80b4-df0c8918e7d1", "metadata": {}, "outputs": [], "source": ["conn = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "2e29ba35-4d56-4ed9-83b6-c74c35d87707", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select a.fe_id driver_id,driver_name from\n", "                (\n", "                    select install_ts + interval '330' minute as install_ts,\n", "                    try_cast(json_extract_scalar(extra, '$.store_id') as int) as frontend_merchant_id, order_id,\n", "                    try_cast(json_extract_scalar(extra, '$.partner_emp_id') as varchar) as fe_id,\n", "                    row_number() over(partition by order_id order by install_ts desc) as rnk\n", "                    from  oms_bifrost.oms_order_event\n", "                    where insert_ds_ist = cast(current_date as varchar)\n", "                    and event_type_key = 'delivery_partner_assigned'\n", "                    and try_cast(json_extract_scalar(extra, '$.store_id') as int) in (31250,38212,36818,30614,34752,\n", "                                                                                     31304,38280,36730,31021,36222,\n", "                                                                                     30771,34744,37808,39370,34262,\n", "                                                                                     36475,35322,30720,37516,\n", "                                                                                     30792,31033,31025)\n", "                    and install_ts + interval '330' minute >= cast(current_time - interval '60' minute as timestamp(3))\n", "                )a\n", "                left join zomato.jumbo_derived.delivery_drivers b on\n", "                a.fe_id = b.fe_id\n", "                where rnk=1\n", "                order by 1 desc\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "7caf57ec-0e08-44ab-b1ba-13c90d8e54dc", "metadata": {}, "outputs": [], "source": ["df = pd.read_sql_query(query, conn)"]}, {"cell_type": "code", "execution_count": null, "id": "9f494ba5-e056-49f2-a37a-bdc8b79197b2", "metadata": {"jupyter": {"outputs_hidden": true}, "tags": []}, "outputs": [], "source": ["pb.to_sheets(df, \"163F54JJf7GwspLTetJqxHoEeL6h8n5XB8-y_v4AiOaY\", \"rider_data\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}