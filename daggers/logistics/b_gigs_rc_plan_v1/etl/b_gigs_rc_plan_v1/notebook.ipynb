{"cells": [{"cell_type": "code", "execution_count": null, "id": "7b632461-d196-4e65-bc58-bfe4d35593e7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4eef2286-bd46-4648-af98-dd7260ce58e2", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "id": "9595dab1-9710-4ae0-92f6-eb4d9bced17c", "metadata": {}, "outputs": [], "source": ["red_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "30eeef0f-a2ea-42c5-b092-b5480b0741c4", "metadata": {}, "outputs": [], "source": ["base_sql = f\"\"\"\n", "\n", "\n", "\n", "with base_cpo_v1 as (\n", "\n", "select * from\n", "(\n", "select * from \n", "(\n", "select c.blinkit_store_id , c.blinkit_store_name , c.runnr_city , c.store_id , cid , coalesce( base_pay , final_base_cpo) final_base_cpo , \n", "coalesce( distance_pay_per_km , final_cpk) final_cpk , \n", "coalesce( m.planned_dd , c.planned_dd)  planned_dd \n", "from\n", "(\n", "select sm.blinkit_store_id , sm.blinkit_store_name , sm.runnr_city , sm.store_id  , base_cpo final_base_cpo , cpk final_cpk   , carrier_id cid , \n", "planned_dd\n", "from logistics_data_etls.store_btpo_breakup_v2  bt \n", "left join logistics_data_etls.blinkit_store_mapping sm on sm.blinkit_store_id = bt.blinkit_store_id and is_active = 1 \n", "where update_ts = (select max(update_ts) from logistics_data_etls.store_btpo_breakup_v2 )\n", ") c \n", "left join logistics_data_etls.new_hire_rc_manual m on m.blinkit_store_id = c.blinkit_store_id and m.carrier_id = cid)\n", "\n", "union \n", "\n", "select blinkit_store_id , blinkit_store_name , runnr_city , store_id , carrier_id cid , base_pay final_base_cpo , distance_pay_per_km final_cpk , planned_dd\n", "from \n", "logistics_data_etls.new_hire_rc_manual\n", "where \n", "blinkit_store_id\n", "not in \n", "(select distinct blinkit_store_id from \n", "logistics_data_etls.store_btpo_breakup_v2\n", " where update_ts = (select max(update_ts) from logistics_data_etls.store_btpo_breakup_v2 )\n", " )\n", ")\n", "\n", ") ,\n", "\n", "store_base as(\n", "Select cast(entity_id as int) store_id,cast(max_by(new_model_mix,dt) as double) new_model_mix from logistics_data_etls.b_gigs_model_conversion_input\n", "where dd_approval = 'Yes'\n", "group by 1\n", "),\n", "\n", "gigs_base as (\n", "SELECT * FROM \n", "(SELECT distinct store_id,store_name,slot_id,slot_name,account_date,dow,carrier_type,coalesce(planned_util,2) planned_util,\n", "greatest(coalesce(projected_orders,1),1) projected_orders,final_cap_req,cap_push,cap_push_post_avp_buffer,start_time_seconds_since_midnight/3600 as start_hour,\n", "(end_time_seconds_since_midnight/3600)-1 as end_hour,rank() over(partition by store_id,slot_id,account_date,carrier_type order by dt desc) ranking \n", "FROM logistics_data_etls.b_gigs_capacity_planning_v2 c\n", "left join zomato.driver_shift.slots s on c.slot_id=s.id\n", "WHERE dt>=date_format(current_date - interval '7' day, '%%Y%%m%%d')\n", "\n", "AND account_date >= current_date + interval '0' day  and account_date  <= current_date + interval '7' day \n", ")\n", "WHERE ranking=1\n", "),\n", "\n", "dd_planned as (SELECT account_date,store_id,slot_id,max_by(dd,dt) planned_dd \n", "FROM logistics_data_etls.b_gigs_planned_dd_v2\n", "WHERE dt>=date_format(current_date - interval '7' day, '%%Y%%m%%d') GROUP BY 1,2,3 ),\n", "\n", "dd_final_slot as (\n", "select store_id , slot_id , account_date , case when id is not null then c_projected_orders else projected_orders end as projected_orders   , \n", "case when id is not null then c_planned_dd * 1.00 / c_projected_orders else planned_dd end as planned_dd  \n", "from\n", "(select a.store_id , slot_id , account_date , projected_orders , planned_dd , \n", "sum(case when id is not null then  projected_orders end ) over (partition by id , slot_id , account_date ) c_projected_orders , \n", "sum(case when id is not null then  planned_dd * projected_orders end) over (partition by id , slot_id , account_date ) c_planned_dd , \n", "id\n", "from \n", "(\n", "SELECT gb.store_id,gb.slot_id,gb.account_date,projected_orders,coalesce(de.planned_dd,coalesce(greatest(1,dp.planned_dd),2)) planned_dd\n", "FROM (SELECT store_id,slot_id,account_date,projected_orders FROM gigs_base WHERE carrier_type=1 GROUP BY 1,2,3,4) gb\n", "LEFT JOIN dd_planned dp ON gb.store_id=dp.store_id AND gb.account_date=dp.account_date AND gb.slot_id=dp.slot_id\n", "LEFT JOIN (SELECT try_cast(entity_id as int) entity_id,try_cast(planned_dd as double) planned_dd FROM logistics_data_etls.b_gigs_exception \n", "WHERE dd_exception='Yes' AND try_cast(entity_id as int)>0 AND entity_type='Store') de ON gb.store_id=try_cast(de.entity_id as int)\n", "GROUP BY 1,2,3,4,5\n", ") a\n", "left join logistics_data_etls.store_in_store_mapping_v1 b on a.store_id = b.store_id \n", ")\n", "\n", "\n", "),\n", "\n", "\n", "dd_final_week as (SELECT store_id,sum(projected_orders) projected_orders,sum(projected_orders*planned_dd)*1.00/sum(projected_orders) projected_dd\n", "FROM dd_final_slot WHERE account_date between current_date+interval '1' day AND current_date+interval '7' day GROUP BY 1) ,\n", "\n", "\n", "ming_cpo as (SELECT z_store_id,carrier_id,greatest(ming_cpo*multiple,0.03) ming_cpo\n", "FROM \n", "(SELECT 1.0 multiple, z_store_id,carrier_id,sum(trip_min_guarantee) ming_amount,sum(acc_orders) acc_orders,\n", "case when sum(acc_orders) > 5 then try(sum(trip_min_guarantee)*1.00/sum(acc_orders))\n", "else 0.03 end as ming_cpo\n", "FROM logistics_data_etls.blinkit_rider_daily_accounting_summary_v1\n", "WHERE account_date between current_date-interval '14' day AND current_date-interval '1' day\n", "AND dt>=date_format(current_date - interval '15' day, '%%Y%%m%%d')\n", "GROUP BY 1,2,3)\n", "GROUP BY 1,2,3)\n", "\n", "-- Getting store x carrier_id level planned_dd \n", ",\n", "\n", "carrier_dd as (select  z_store_id store_id , blinkit_store_id bsi , sum(delivered_orders_distance) * 1.0 / sum(delivered_orders) planned_dd \n", "\n", "from logistics_data_etls.blinkit_rider_daily_accounting_summary_v1\n", "\n", "where ( tag_name like '%%b_fleet_gigs%%' or tag_name like '%%b_zsp_acc%%' or tag_name like '%%b_fleet_flex_new%%')\n", "\n", "and account_date >= date_trunc ( 'week' , current_date ) - interval '14' day \n", "\n", "and acc_orders > 0\n", "\n", "and account_date >= current_date - interval '14' day \n", "\n", "group by 1 , 2\n", "\n", ")\n", "\n", ",\n", "weekly_cpo_v1 as  (\n", "select blinkit_store_id , blinkit_store_name , cp.store_id , cid carrier_id  , runnr_city   , coalesce( coalesce( cp.planned_dd , cd.planned_dd ), 2.0) planned_dd  , \n", "\n", "( final_base_cpo + final_cpk * coalesce( coalesce( cp.planned_dd , cd.planned_dd ), 2.0) ) as weekly_cpo  ,final_base_cpo , final_cpk  \n", "from base_cpo_v1 cp\n", "left join carrier_dd cd on cd.store_id = cp.store_id \n", ")\n", ",\n", "\n", "cpo_plan as (select *, max(weekly_cpo) over (partition by store_id) as bike_cpo\n", "from ( SELECT distinct store_id,carrier_id,(((1)*weekly_cpo)-ming_cpo) weekly_cpo  ,final_base_cpo  - ming_cpo*1.00 / weekly_cpo final_base_cpo , final_cpk - ming_cpo*1.00 / weekly_cpo final_cpk\n", "FROM ( select distinct b.store_id,b.carrier_id, coalesce(ming_cpo,0.03) ming_cpo,weekly_cpo  ,final_base_cpo , final_cpk \n", "from weekly_cpo_v1 b\n", "LEFT JOIN ming_cpo m on  b.store_id = m.z_store_id  and b.carrier_id = m.carrier_id \n", "\n", ")))\n", "\n", ",\n", "\n", "skew_input_city as\n", "(SELECT entity_name as entity_id,entity_type,dow,cast(slot_id as int) slot_id,avg(cast(coalesce(skew_factor,1) as double)) skew_factor\n", "FROM \n", "(select entity_name,entity_type,cast(bc.dow as int) dow, hour,slot_id, max_by(cpk_factor,dt) as skew_factor\n", "from logistics_data_etls.new_city_skew_v1 bc\n", "left join (SELECT slot_id,min(cast(hour as int)) start_hour,max(cast(hour as int)) end_hour\n", "FROM zomato.jumbo_external.b_gigs_input_ver2\n", "WHERE dt>'********' \n", "GROUP BY 1) gb ON cast(bc.hour as int) between gb.start_hour and gb.end_hour\n", "where dt>'********' AND entity_type='City'\n", "group by 1,2,3,4,5\n", ")\n", "GROUP BY 1,2,3,4\n", ")\n", ",\n", "\n", "skew_input_store as\n", "(SELECT entity_name as entity_id ,entity_type,dow,cast(slot_id as int) slot_id,avg(cast(coalesce(skew_factor,1) as double)) skew_factor\n", "FROM \n", "(select bc.entity_name,entity_type,cast(bc.dow as int) dow, hour,slot_id, max_by(cpk_factor,dt) as skew_factor\n", "from logistics_data_etls.new_city_skew_v1 bc\n", "INNER JOIN (SELECT entity_id from logistics_data_etls.b_gigs_exception \n", "WHERE store_skew_exception='Yes' AND entity_type='Store' GROUP BY 1) e ON bc.entity_name=cast(e.entity_id as integer)\n", "left join (SELECT slot_id,min(cast(hour as int)) start_hour,max(cast(hour as int)) end_hour\n", "FROM zomato.jumbo_external.b_gigs_input_ver2\n", "WHERE dt>'********' \n", "GROUP BY 1) gb ON cast(bc.hour as int) between gb.start_hour and gb.end_hour\n", "where dt>'********' AND entity_type='Store'\n", "group by 1,2,3,4,5\n", ")\n", "GROUP BY 1,2,3,4\n", ")\n", "\n", "\n", "\n", ",\n", "\n", "join_cpo as\n", "(\n", "select c.*, weekly_cpo, bike_cpo,c.account_date date1 ,final_base_cpo , final_cpk \n", "from gigs_base c\n", "inner join cpo_plan p on c.store_id=cast(p.store_id as int) and cast(p.carrier_id as int)=c.carrier_type\n", "),\n", "store_city as\n", "(\n", "select distinct c.store_id entity_id,sm.city_id as city_id ,runnr_city as city\n", " from gigs_base c\n", " left join logistics_data_etls.blinkit_store_mapping sm on sm.store_id = c.store_id\n", "),\n", "\n", "base_joins as\n", "(\n", " SELECT DISTINCT \n", "  j.slot_id, \n", "  j.carrier_type AS carrier_id, \n", "  j.store_id AS store_id, \n", "  j.store_name, \n", "  cs.city, \n", "  bike_cpo, \n", "  j.slot_name,\n", "  date1, \n", "  j.start_hour, \n", "  j.end_hour, \n", "  CAST(weekly_cpo AS DOUBLE) AS weekly_cpo, \n", "  j.dow, \n", "  projected_orders AS slot_orders, \n", "coalesce(sk1.skew_factor,sk2.skew_factor,sk3.skew_factor) skew_factor,\n", "  cap_push_post_avp_buffer AS cap_req, \n", "  final_base_cpo, \n", "  final_cpk \n", "FROM join_cpo j\n", "LEFT JOIN store_city cs \n", "  ON cs.entity_id = j.store_id\n", "LEFT JOIN skew_input_store sk1 \n", "  ON sk1.entity_id = j.store_id\n", "  AND sk1.slot_id = j.slot_id \n", "  AND CAST(sk1.dow AS INT) = EXTRACT(DOW FROM j.date1)\n", "LEFT JOIN skew_input_city sk2 \n", "  ON sk2.entity_id = cs.city_id\n", "  AND sk2.slot_id = j.slot_id \n", "  AND CAST(sk2.dow AS INT) = EXTRACT(DOW FROM j.date1)\n", "LEFT JOIN (select * from skew_input_city where entity_id = 4 ) sk3\n", "  ON sk3.slot_id = j.slot_id \n", "   AND CAST(sk3.dow AS INT) = EXTRACT(DOW FROM j.date1)  \n", "  \n", "  \n", "\n", "WHERE \n", "  date1 BETWEEN CURRENT_DATE + INTERVAL '0' DAY AND CURRENT_DATE + INTERVAL '10' DAY\n", "  AND CAST(weekly_cpo AS DOUBLE) > 0 \n", "  AND projected_orders > 0\n", "\n", "),\n", "\n", "slot_cpo_skew as\n", "(select distinct store_id c_store_id,\n", "sum(weekly_cpo*skew_factor*slot_orders) over (partition by b.store_id)*1.00 / sum(weekly_cpo*slot_orders) over (partition by b.store_id) as correction_factor\n", "from base_joins b\n", "WHERE carrier_id=1 AND date1 between current_date AND current_date+interval '7' day)\n", ",\n", "\n", "slot_cpo as (select b.*, weekly_cpo*skew_factor as slot_base_cpo,\n", "weekly_cpo*skew_factor*slot_orders as slot_base_earning, \n", "weekly_cpo*slot_orders as weekly_earning,\n", "correction_factor,\n", "skew_factor*1.00/correction_factor as corrected_slot_factor,\n", "weekly_cpo* skew_factor *1.00 / correction_factor as final_slot_cpo,\n", "final_base_cpo* skew_factor *1.00 / correction_factor final_base_cpo_v1 , (final_cpk * skew_factor *1.00) / (correction_factor ) final_cpk_v1  ,\n", "\n", "b.store_id+dow+b.slot_id+extract(week from date1)+carrier_id as rand,\n", "mod(b.store_id+dow+b.slot_id+extract(week from date1)+carrier_id,least(floor(bike_cpo/6),5)) as base_var \n", "\n", "\n", "from base_joins b\n", "LEFT JOIN slot_cpo_skew c on b.store_id=c.c_store_id\n", "\n", ")\n", "\n", "    \n", "\n", ", \n", "\n", "final as (\n", "SELECT *\n", "FROM \n", "(SELECT \n", "    s.*,\n", "    \n", "    case when s.final_base_cpo_v1 + base_var <= 5 then 5 else s.final_base_cpo_v1 + base_var end as   base_pay,\n", "    \n", "    case when s.final_cpk_v1 - (base_var *1.00/ planned_dd) <= 5 then 5 else s.final_cpk_v1 - (base_var *1.00/ planned_dd)  end as   dist_pay_per_km,\n", "    \n", "    case when planned_dd * s.final_cpk_v1 - base_var  <= 10 then 10 else planned_dd * s.final_cpk_v1 - base_var end as  distance_pay \n", "\n", "FROM \n", "    slot_cpo s\n", "LEFT JOIN \n", "    (\n", "    select store_id sid , cid , coalesce(planned_dd , 2) planned_dd from base_cpo_v1\n", "    \n", "    ) b\n", "    on sid = store_id and cid = carrier_id \n", "\n", ")\n", "\n", "\n", ")\n", "\n", ",\n", "\n", "\n", "\n", "\n", "incentive_city as (SELECT store_id,slot_name,slot_id,account_date,max(dinner_incentive) dinner_incentive,max(incentive_multiplier) incentive_multiplier FROM\n", "(SELECT store_name,d.store_id,hour,slot_name,slot_id,account_date,dinner_incentive,max(incentive_multiplier) incentive_multiplier\n", "FROM blinkit.logistics_data_etls.b_gigs_design_ver3 d\n", "INNER JOIN (SELECT try_cast(store_id as int) store_id,\n", "try_cast(start_hour as int) start_hour,\n", "try_cast(end_hour as int) end_hour,\n", "(start_date) start_date,(end_date) end_date,try_cast(incentive_multiplier as int) incentive_multiplier,dinner_incentive\n", "FROM logistics_data_etls.b_gigs_incentive_plan_v3\n", "WHERE approval='Yes' AND incentive_multiplier is not null\n", "GROUP BY 1,2,3,4,5,6,7) c\n", "ON d.hour between c.start_hour and (c.end_hour-1)\n", "AND date_format(d.account_date, '%%Y%%m%%d') BETWEEN c.start_date AND c.end_date\n", "AND c.store_id=d.store_id\n", "WHERE d.account_date>=current_date AND account_date<=current_date+interval '10' day\n", "AND d.dt=date_format(current_date - interval '1' day, '%%Y%%m%%d')\n", "GROUP BY 1,2,3,4,5,6,7)\n", "WHERE incentive_multiplier IS NOT NULL\n", "GROUP BY 1,2,3,4)\n", "\n", "SELECT *\n", "FROM\n", "(select \n", "'DistanceTimeOrderRateCard' as rule_name,\n", "810 as priority,\n", "date1 as rate_card_validity_start_date,\n", "date1 as rate_card_validity_end_date,\n", "c.code as city_codes,\n", "'' as zone_ids,\n", "'' as locality_ids,\n", "cast(carrier_id as int) as carrier_id, \n", "f.slot_name as slot_names,\n", "'b_fleet_flex_new_1' as accounting_tags,\n", "'' as day_of_week,\n", "0 base_pay_pickup_amount,\n", "base_pay as base_pay_drop_amount,\n", "0 as pickup_distance_pay_round_off,\n", "0 as pickup_distance_pay_from,\n", "10 as pickup_distance_pay_to,\n", "0 as pickup_distance_pay_amount,\n", "0 as pickup_distance_pay_amount_breakup_base_distance_pay,\n", "0 as pickup_distance_pay_amount_breakup_incentive_distance_pay,\n", "0 as distance_pay_drop_round_off,\n", "0 as distance_pay_from,\n", "10 distance_pay_to,\n", "dist_pay_per_km distance_pay_amount, \n", "            \n", "            \n", "dist_pay_per_km distance_pay_amount_breakup_base_distance_pay, \n", "            \n", "            \n", "0 as distance_pay_amount_breakup_incentive_distance_pay,\n", "0 as wait_time_pay_drop_round_off,\n", "3 as wait_time_pay_from,\n", "90 as wait_time_pay_to,\n", "0 as wait_time_pay_amount,\n", "case when  upper(city) IN ('<PERSON><PERSON><PERSON><PERSON>' , 'BANG<PERSON><PERSON><PERSON>','BENGALURU'  ,'CHANDIGARH' , 'CHENNAI' , 'SURAT') then 15 \n", "     when  upper(city) IN ('VADODARA') then 18 \n", "     when upper(city) IN ('AHMEDABAD') then 17 \n", "else 12 end as trip_ming_amount,\n", "\n", "0 incentive_pay_multiplier,\n", "'FALSE' as is_back_dated,\n", "'BLINKIT' as service_category, \n", "cast(f.store_id as int) as service_category_entity_id,\n", "case when c.code='HYD' then 1 ELSE 0.00001 END as base_pay_batched_order_drop_amount_multiplier,\n", "10 as weight_pay_slabs_from_0,\n", "100 as weight_pay_slabs_to_0,\n", "0.5 as weight_pay_slabs_amount_0,\n", "12 first_order_ming_amount,\n", "\n", "case when carrier_id in (1,6) then 15 \n", "     when carrier_id = 5 then 12\n", "     when carrier_id = 3 then 10\n", "     else 12 end\n", "as batched_order_ming_amount , \n", "date_format(current_date - interval '1' day, '%%Y%%m%%d') AS dt\n", "from final f\n", "left join zomato.carthero_prod.cities c on f.city = c.city_name\n", "--LEFT JOIN priority p on 1=1\n", "\n", "LEFT JOIN incentive_city ic ON f.date1=ic.account_date AND f.slot_name=ic.slot_name AND f.store_id=ic.store_id\n", "\n", "where date1 >= current_date+interval '0' day\n", ") \n", "where  carrier_id in ( 1,3,5,6)\n", "\n", "and  dt=date_format(current_date - interval '1' day, '%%Y%%m%%d')\n", "\n", "    \"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "976a988d-4ef4-4fd8-9ad2-1efefd691cc9", "metadata": {}, "outputs": [], "source": ["final = pd.read_sql_query(sql=base_sql, con=red_con)"]}, {"cell_type": "code", "execution_count": null, "id": "fa8b714c-64e8-42e4-adf7-5fa2d4ad4c39", "metadata": {}, "outputs": [], "source": ["# final.to_csv('test.csv' , index = False)"]}, {"cell_type": "code", "execution_count": null, "id": "846024c9-8e02-45ae-be42-f464732dd7ab", "metadata": {}, "outputs": [], "source": ["final.info()"]}, {"cell_type": "code", "execution_count": null, "id": "01a6d74f-b5a8-4016-b4a2-d65eb90f3661", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "52c5dacf-9513-4e54-80f0-9f087b72a037", "metadata": {}, "outputs": [], "source": ["(final.dtypes)"]}, {"cell_type": "code", "execution_count": null, "id": "eb162f54-c728-48de-bb20-d5e022ad527e", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "# Sample DataFrame\n", "# df = pd.read_csv(\"your_data.csv\")\n", "\n", "# Define the updated column data types\n", "updated_dtypes = {\n", "    \"rule_name\": \"string\",\n", "    \"priority\": \"int64\",\n", "    \"rate_card_validity_start_date\": \"datetime64[ns]\",\n", "    \"rate_card_validity_end_date\": \"datetime64[ns]\",\n", "    \"city_codes\": \"string\",\n", "    \"zone_ids\": \"string\",\n", "    \"locality_ids\": \"string\",\n", "    \"carrier_id\": \"int64\",\n", "    \"slot_names\": \"string\",\n", "    \"accounting_tags\": \"string\",\n", "    \"day_of_week\": \"string\",\n", "    \"base_pay_pickup_amount\": \"float64\",\n", "    \"base_pay_drop_amount\": \"float64\",\n", "    \"pickup_distance_pay_round_off\": \"int64\",\n", "    \"pickup_distance_pay_from\": \"int64\",\n", "    \"pickup_distance_pay_to\": \"int64\",\n", "    \"pickup_distance_pay_amount\": \"int64\",\n", "    \"pickup_distance_pay_amount_breakup_base_distance_pay\": \"int64\",\n", "    \"pickup_distance_pay_amount_breakup_incentive_distance_pay\": \"int64\",\n", "    \"distance_pay_drop_round_off\": \"int64\",\n", "    \"distance_pay_from\": \"int64\",\n", "    \"distance_pay_to\": \"int64\",\n", "    \"distance_pay_amount\": \"float64\",\n", "    \"distance_pay_amount_breakup_base_distance_pay\": \"float64\",\n", "    \"distance_pay_amount_breakup_incentive_distance_pay\": \"int64\",\n", "    \"wait_time_pay_drop_round_off\": \"int64\",\n", "    \"wait_time_pay_from\": \"int64\",\n", "    \"wait_time_pay_to\": \"int64\",\n", "    \"wait_time_pay_amount\": \"int64\",\n", "    \"trip_ming_amount\": \"float64\",\n", "    \"incentive_pay_multiplier\": \"float64\",\n", "    \"is_back_dated\": \"string\",\n", "    \"service_category\": \"string\",\n", "    \"service_category_entity_id\": \"int64\",\n", "    \"base_pay_batched_order_drop_amount_multiplier\": \"float64\",\n", "    \"weight_pay_slabs_from_0\": \"int64\",\n", "    \"weight_pay_slabs_to_0\": \"int64\",\n", "    \"weight_pay_slabs_amount_0\": \"float64\",\n", "    \"first_order_ming_amount\": \"float64\",\n", "    \"batched_order_ming_amount\": \"int64\",\n", "}\n", "\n", "\n", "final = final.astype(updated_dtypes)\n", "\n", "\n", "date_columns = [\"rate_card_validity_start_date\", \"rate_card_validity_end_date\"]\n", "for col in date_columns:\n", "    final[col] = pd.to_datetime(final[col], errors=\"coerce\")\n", "\n", "\n", "(final.dtypes)"]}, {"cell_type": "code", "execution_count": null, "id": "a3d47155-305f-4f03-a558-07fa37bcd00a", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"b_gigs_rc_plan_v6\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"rule_name\", \"type\": \"varchar\", \"description\": \"rule name\"},\n", "        {\"name\": \"priority\", \"type\": \"integer\", \"description\": \"priority\"},\n", "        {\n", "            \"name\": \"rate_card_validity_start_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"rate_card_validity_start_date\",\n", "        },\n", "        {\n", "            \"name\": \"rate_card_validity_end_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"rate_card_validity_end_date\",\n", "        },\n", "        {\"name\": \"city_codes\", \"type\": \"varchar\", \"description\": \"city_codes\"},\n", "        {\"name\": \"zone_ids\", \"type\": \"varchar\", \"description\": \"zone_ids\"},\n", "        {\"name\": \"locality_ids\", \"type\": \"varchar\", \"description\": \"locality_ids\"},\n", "        {\"name\": \"carrier_id\", \"type\": \"integer\", \"description\": \"carrier_id\"},\n", "        {\"name\": \"slot_names\", \"type\": \"varchar\", \"description\": \"slot_names\"},\n", "        {\n", "            \"name\": \"accounting_tags\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"accounting_tags\",\n", "        },\n", "        {\"name\": \"day_of_week\", \"type\": \"varchar\", \"description\": \"day_of_week\"},\n", "        {\n", "            \"name\": \"base_pay_pickup_amount\",\n", "            \"type\": \"double\",\n", "            \"description\": \"base_pay_pickup_amount\",\n", "        },\n", "        {\n", "            \"name\": \"base_pay_drop_amount\",\n", "            \"type\": \"double\",\n", "            \"description\": \"base_pay_drop_amount\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_round_off\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_round_off\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_from\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_from\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_to\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_to\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_amount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_amount\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_amount_breakup_base_distance_pay\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_amount_breakup_base_distance_pay\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_amount_breakup_incentive_distance_pay\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_amount_breakup_incentive_distance_pay\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_drop_round_off\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"distance_pay_drop_round_off\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_from\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"distance_pay_from\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_to\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"distance_pay_to\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_amount\",\n", "            \"type\": \"double\",\n", "            \"description\": \"distance_pay_amount\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_amount_breakup_base_distance_pay\",\n", "            \"type\": \"double\",\n", "            \"description\": \"distance_pay_amount_breakup_base_distance_pay\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_amount_breakup_incentive_distance_pay\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"distance_pay_amount_breakup_incentive_distance_pay\",\n", "        },\n", "        {\n", "            \"name\": \"wait_time_pay_drop_round_off\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"wait_time_pay_drop_round_off\",\n", "        },\n", "        {\n", "            \"name\": \"wait_time_pay_from\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"wait_time_pay_from\",\n", "        },\n", "        {\n", "            \"name\": \"wait_time_pay_to\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"wait_time_pay_to\",\n", "        },\n", "        {\n", "            \"name\": \"wait_time_pay_amount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"wait_time_pay_amount\",\n", "        },\n", "        {\n", "            \"name\": \"trip_ming_amount\",\n", "            \"type\": \"double\",\n", "            \"description\": \"trip_ming_amount\",\n", "        },\n", "        {\n", "            \"name\": \"incentive_pay_multiplier\",\n", "            \"type\": \"double\",\n", "            \"description\": \"incentive_pay_multiplier\",\n", "        },\n", "        {\"name\": \"is_back_dated\", \"type\": \"varchar\", \"description\": \"is_back_dated\"},\n", "        {\n", "            \"name\": \"service_category\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"service_category\",\n", "        },\n", "        {\n", "            \"name\": \"service_category_entity_id\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"service_category_entity_id\",\n", "        },\n", "        {\n", "            \"name\": \"base_pay_batched_order_drop_amount_multiplier\",\n", "            \"type\": \"double\",\n", "            \"description\": \"base_pay_batched_order_drop_amount_multiplier\",\n", "        },\n", "        {\n", "            \"name\": \"weight_pay_slabs_from_0\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"weight_pay_slabs_from_0\",\n", "        },\n", "        {\n", "            \"name\": \"weight_pay_slabs_to_0\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"weight_pay_slabs_to_0\",\n", "        },\n", "        {\n", "            \"name\": \"weight_pay_slabs_amount_0\",\n", "            \"type\": \"double\",\n", "            \"description\": \"weight_pay_slabs_amount_0\",\n", "        },\n", "        {\n", "            \"name\": \"first_order_ming_amount\",\n", "            \"type\": \"double\",\n", "            \"description\": \"first_order_ming_amount\",\n", "        },\n", "        {\n", "            \"name\": \"batched_order_ming_amount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"batched_order_ming_amount\",\n", "        },\n", "        {\"name\": \"dt\", \"type\": \"varchar\", \"description\": \"dt\"},\n", "    ],\n", "    \"primary_key\": [\n", "        \"rule_name\",\n", "        \"dt\",\n", "        \"slot_names\",\n", "        \"carrier_id\",\n", "        \"priority\",\n", "        \"rate_card_validity_start_date\",\n", "        \"rate_card_validity_end_date\",\n", "        \"service_category\",\n", "        \"service_category_entity_id\",\n", "    ],\n", "    \"partition_key\": [\"dt\"],\n", "    \"incremental_key\": \"dt\",\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"This Table has GIGS RC Config\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "42830537-68f7-448e-9ad0-a339d92983eb", "metadata": {}, "outputs": [], "source": ["pb.to_trino(final, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "cbfd229f-e457-419c-8bb1-00b51760a8d1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "dae7d7e4-bd80-47b8-98f5-fa2552188190", "metadata": {}, "outputs": [], "source": ["# x = final.shape[0]\n", "# x"]}, {"cell_type": "code", "execution_count": null, "id": "9c4afc30-0d40-497b-8a77-78f8c59bbf59", "metadata": {}, "outputs": [], "source": ["# sql_check_1 = f\"\"\"\n", "\n", "# select count(*) as pushed_rows from logistics_data_etls.b_gigs_rc_plan_v6\n", "# where dt=date_format(current_date - interval '1' day, '%%Y%%m%%d')\n", "\n", "\n", "# \"\"\"\n", "# df1 = pd.read_sql_query(sql=sql_check_1, con=red_con)\n", "# df1"]}, {"cell_type": "code", "execution_count": null, "id": "c87d6780-6e03-450e-923e-4daae3e64cca", "metadata": {}, "outputs": [], "source": ["# sql_check_2 = f\"\"\"\n", "\n", "# select avg(1.00*cnt) as avg_pushed_rows_l30d from\n", "# (\n", "# select dt,count(*) as cnt from logistics_data_etls.b_gigs_rc_plan_v6\n", "# where dt between date_format(current_date - interval '31' day, '%%Y%%m%%d')\n", "# and date_format(current_date - interval '2' day, '%%Y%%m%%d')\n", "# group by 1\n", "# )\n", "\n", "\n", "# \"\"\"\n", "# df2 = pd.read_sql_query(sql=sql_check_2, con=red_con)\n", "# df2"]}, {"cell_type": "code", "execution_count": null, "id": "98ce27d5-8d4e-4dd6-b87a-fd8f6b549ec0", "metadata": {}, "outputs": [], "source": ["# pushed_rows = pd.to_numeric(df1.iloc[0, 0], errors=\"coerce\")\n", "# avg_pushed_rows_l30d = pd.to_numeric(df2.iloc[0, 0], errors=\"coerce\")\n", "\n", "\n", "# if pd.isna(pushed_rows) or pd.isna(avg_pushed_rows_l30d):\n", "#     raise ValueError(\"One or more of the extracted values is not numeric.\")\n", "\n", "\n", "# query_output_table_push_delta = 100.00 * ((pushed_rows / x) - 1)\n", "# deviation_wrt_l30d = 100.00 * ((pushed_rows / avg_pushed_rows_l30d) - 1)\n", "\n", "# query_output_table_push_delta = f\"{query_output_table_push_delta:.2f}%\"\n", "# deviation_wrt_l30d = f\"{deviation_wrt_l30d:.2f}%\"\n", "\n", "\n", "# final_df = pd.DataFrame(\n", "#     {\n", "#         \"table_push_time_ist\": [table_push_time_ist],\n", "#         \"base_query_output_rows\": [x],\n", "#         \"pushed_rows\": [pushed_rows],\n", "#         \"avg_pushed_rows_l_30_d\": [avg_pushed_rows_l30d],\n", "#         \"query_output_table_push_delta\": [query_output_table_push_delta],\n", "#         \"deviation_wrt_l_30_d\": [deviation_wrt_l30d],\n", "#     }\n", "# )\n", "\n", "# final_df"]}, {"cell_type": "code", "execution_count": null, "id": "679c5859-daa8-4937-be88-2de4a9554927", "metadata": {}, "outputs": [], "source": ["# null_counts = final.isnull().sum()\n", "\n", "# # Create a new DataFrame with column names and their null counts\n", "# null_values_df = pd.DataFrame(\n", "#     {\"Column Name\": null_counts.index, \"Null Values Count\": null_counts.values}\n", "# )\n", "\n", "# null_values_df"]}, {"cell_type": "code", "execution_count": null, "id": "8067b456-a8d2-453b-9ab9-7e23072e6d49", "metadata": {}, "outputs": [], "source": ["# !pip install matplotlib==3.8\n", "# import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "id": "48416e01-2b95-44fd-af56-0d0d3417c3f9", "metadata": {}, "outputs": [], "source": ["# df_v2 = final_df.T\n", "\n", "# # Reset the index to turn the row labels into a column\n", "# df_v2 = df_v2.reset_index()\n", "\n", "# # Rename columns to 'Column' and 'Value'\n", "# df_v2.columns = [\"Column\", \"Value\"]\n", "\n", "# df_v2"]}, {"cell_type": "code", "execution_count": null, "id": "3eb6f869-334f-4c6c-97cf-2b627e085317", "metadata": {}, "outputs": [], "source": ["# def render_mpl_table(\n", "#     data,\n", "#     col_width=12.0,\n", "#     row_height=2,\n", "#     font_size=40,\n", "#     header_color=\"#E96125\",\n", "#     row_colors=[\"#f1f1f2\", \"w\"],\n", "#     edge_color=\"black\",\n", "#     edge_width=2,  # Added edge width\n", "#     bbox=[0, 0, 1, 1],\n", "#     header_columns=0,\n", "#     ax=None,\n", "#     **kwargs\n", "# ):\n", "#     if ax is None:\n", "#         size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array(\n", "#             [col_width * 1.5, row_height]\n", "#         )\n", "#         fig, ax = plt.subplots(figsize=size)\n", "#         ax.axis(\"off\")\n", "#     mpl_table = ax.table(\n", "#         cellText=data.values, bbox=bbox, cellLoc=\"center\", colLabels=data.columns, **kwargs\n", "#     )\n", "#     mpl_table.auto_set_font_size(False)\n", "#     mpl_table.set_fontsize(font_size)\n", "\n", "#     for k, cell in mpl_table._cells.items():\n", "#         # Set edge properties\n", "#         cell.set_edgecolor(edge_color)\n", "#         cell.set_linewidth(edge_width)  # Set consistent line thickness\n", "\n", "#         if k[0] <= 0 or k[0] < header_columns:  # Header row\n", "#             cell.set_text_props(weight=\"extra bold\", color=\"w\", ma=\"center\")\n", "#             cell.set_facecolor(header_color)\n", "#         elif k[1] == 0:  # Left column\n", "#             cell.set_text_props(weight=\"bold\", color=\"black\", ma=\"center\")\n", "#         else:  # Data cells\n", "#             cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "#     mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "#     return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "88f14d9b-872d-4d29-89f9-bb8795a06955", "metadata": {}, "outputs": [], "source": ["# bmhpc_b_image1 = \"/tmp/alert_1.png\"\n", "\n", "# fig1, ax1 = render_mpl_table(df_v2)\n", "# fig1.savefig(bmhpc_b_image1)"]}, {"cell_type": "code", "execution_count": null, "id": "b502e225-ec16-4aa7-ba34-58bb35770e17", "metadata": {}, "outputs": [], "source": ["# bmhpc_b_image2 = \"/tmp/alert_2.png\"\n", "\n", "# fig2, ax2 = render_mpl_table(null_values_df)\n", "# fig2.savefig(bmhpc_b_image2)"]}, {"cell_type": "code", "execution_count": null, "id": "87c87e8b-87b4-4f1e-ae30-694c635f1576", "metadata": {}, "outputs": [], "source": ["# final_df[\"query_output_table_push_delta_numeric\"] = pd.to_numeric(\n", "#     final_df[\"query_output_table_push_delta\"].str.rstrip(\"%\"), errors=\"coerce\"\n", "# )\n", "# final_df[\"deviation_wrt_l_30_d_numeric\"] = pd.to_numeric(\n", "#     final_df[\"deviation_wrt_l_30_d\"].str.rstrip(\"%\"), errors=\"coerce\"\n", "# )\n", "\n", "# # Check if any value in specified columns is greater than 5%\n", "# if (\n", "#     (final_df[\"query_output_table_push_delta_numeric\"] != 0).any()\n", "#     or (final_df[\"deviation_wrt_l_30_d_numeric\"] > 25).any()\n", "#     or (final_df[\"deviation_wrt_l_30_d_numeric\"] < -5).any()\n", "# ):\n", "\n", "#     pb.send_slack_message(\n", "#         channel=\"bl-cpo-critical-dag-failure-alert\",\n", "#         text=\"alert for *logistics_b_gigs_rc_plan_v1_etl_b_gigs_rc_plan_v1->1*  dag:\\n\",\n", "#         files=[\"/tmp/alert_1.png\"],\n", "#     )\n", "# else:\n", "#     print(\"end\")"]}, {"cell_type": "code", "execution_count": null, "id": "b42ea2df-b5e3-44e5-af21-ab2388ea01d4", "metadata": {}, "outputs": [], "source": ["# if (null_values_df[\"Null Values Count\"] != 0).any():\n", "\n", "#     # Send Slack message\n", "#     pb.send_slack_message(\n", "#         channel=\"bl-cpo-critical-dag-failure-alert\",\n", "#         text=\"alert for *logistics_b_gigs_rc_plan_v1_etl_b_gigs_rc_plan_v1-->2* dag:\\n\",\n", "#         files=[\"/tmp/alert_2.png\"],\n", "#     )\n", "# else:\n", "#     print(\"end\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}