{"cells": [{"cell_type": "code", "execution_count": null, "id": "556e906e-a83d-4473-9563-ac2c0ae5e8d1", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "id": "3549e2f7-6cd0-4a02-a0fd-83467b9def9d", "metadata": {}, "outputs": [], "source": ["conn = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "42abfe26-62e4-4866-9e6e-683dd0c12ab6", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select\n", "extract(hour from cast(from_unixtime(timestamp/1000) as timestamp)) as hour,\n", "extract(minute from cast(from_unixtime(timestamp/1000) as timestamp)) as minute,\n", "cast(from_unixtime(timestamp/1000) as timestamp) as timestamp, \n", "device_serial_number,\n", "rainfall_intensity_mm,\n", "current_timestamp\n", "from zomato.jumbo2.weather_station_events \n", "where device_serial_number > 0\n", "and dt >= date_format(current_date - interval '1' day,'%%Y%%m%%d')\n", "and cast(from_unixtime(timestamp/1000) as timestamp) >= current_timestamp - interval '15' minute\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "39c93878-b330-4ad4-82b6-72860430905e", "metadata": {}, "outputs": [], "source": ["df = pd.read_sql_query(query, conn)"]}, {"cell_type": "code", "execution_count": null, "id": "e45954df-cec9-4647-9279-3c05c53e114b", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(df, \"1iegEYeBUjadizgQn8qdUFi1hym0kyTw2-RcdWIqnALw\", \"device_signals\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}