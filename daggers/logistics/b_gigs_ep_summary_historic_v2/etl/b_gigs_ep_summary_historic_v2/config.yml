alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
  - channel: bl-lm-dag-alert
dag_name: b_gigs_ep_summary_historic_v2
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: logistics
notebook:
  parameters: null
  retries: 4
  retry_delay_in_seconds: 15
owner:
  email: <EMAIL>
  slack_id: U082DBVETNX
path: logistics/b_gigs_ep_summary_historic_v2/etl/b_gigs_ep_summary_historic_v2
paused: false
pool: logistics_pool
project_name: b_gigs_ep_summary_historic_v2
schedule:
  end_date: '2025-09-12T00:00:00'
  interval: 00 05 * * *
  start_date: '2024-03-13T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 14
