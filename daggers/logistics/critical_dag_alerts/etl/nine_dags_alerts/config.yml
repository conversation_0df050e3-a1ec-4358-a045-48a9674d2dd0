alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: nine_dags_alerts
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: logistics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U082DBVETNX
path: logistics/critical_dag_alerts/etl/nine_dags_alerts
paused: false
pool: logistics_pool
project_name: critical_dag_alerts
schedule:
  end_date: '2025-09-12T00:00:00'
  interval: 30 3,4,5,6,15,16 * * *
  start_date: '2025-01-06T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 3
