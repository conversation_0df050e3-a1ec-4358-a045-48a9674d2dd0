alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: pnl_offer_update_alert
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: logistics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U082DBVETNX
path: logistics/pnl_offer_update_alert/etl/pnl_offer_update_alert
paused: false
pool: logistics_pool
project_name: pnl_offer_update_alert
schedule:
  end_date: '2025-09-12T00:00:00'
  interval: 0 4 * * 1
  start_date: '2025-01-08T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
