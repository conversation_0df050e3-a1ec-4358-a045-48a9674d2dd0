{"cells": [{"cell_type": "code", "execution_count": null, "id": "d12441d7-a8be-42cb-ad41-ae0cb5357614", "metadata": {}, "outputs": [], "source": ["!pip install pandasql"]}, {"cell_type": "code", "execution_count": null, "id": "5c50f768-76b5-4667-bd55-34d38bf8c34b", "metadata": {}, "outputs": [], "source": ["import time, random, string\n", "import random, datetime\n", "import json\n", "import string, sys\n", "import pencilbox as pb\n", "import pandas as pd\n", "from dateutil import tz, parser\n", "from datetime import datetime, date, timedelta, datetime\n", "import calendar\n", "import time\n", "import os\n", "import numpy as np\n", "import pandasql as psql\n", "from pandasql import sqldf\n", "\n", "conn = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "98c6111b-9666-45e4-9252-9a76ea894b72", "metadata": {}, "outputs": [], "source": ["# Get today's date\n", "today = pd.Timestamp((datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)).date())"]}, {"cell_type": "code", "execution_count": null, "id": "3f1b134e-5645-4283-8fb3-056817e9c6a8", "metadata": {}, "outputs": [], "source": ["# df = pd.read_csv(\"Store Day View.csv\")\n", "df = pb.from_sheets(\n", "    \"1-7HiAtw14ySRuktbDHqIok6BwgKmJtP_fdTKThsN8gY\",\n", "    \"Store Day View\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "293db1d8-658b-402e-aced-40effeee0678", "metadata": {}, "outputs": [], "source": ["df = df[(df[\"date\"] != \"#VALUE!\") & (df[\"date\"] != None) & (df[\"date\"] != \"NaN\")]\n", "df = df.dropna()"]}, {"cell_type": "code", "execution_count": null, "id": "7d9a8ece-b66b-479d-b4e6-758e3b65b57b", "metadata": {}, "outputs": [], "source": ["df[\"is_budget_applicable\"] = df[\"is_budget_applicable\"].astype(str).str.lower()\n", "df[\"is_budget_applicable\"] = df[\"is_budget_applicable\"].fillna(\"false\")"]}, {"cell_type": "code", "execution_count": null, "id": "25233539-8ee7-4d50-8238-31d74b7f0bcc", "metadata": {}, "outputs": [], "source": ["df[\"is_budget_applicable\"] = np.where(df[\"is_budget_applicable\"] == \"false\", False, True)"]}, {"cell_type": "code", "execution_count": null, "id": "33960a86-cec6-4364-8d05-fb5b8f4e874d", "metadata": {}, "outputs": [], "source": ["df[\"date\"] = pd.to_datetime(df[\"date\"], errors=\"coerce\")\n", "df[\"outlet_id\"] = pd.to_numeric(df[\"outlet_id\"], downcast=\"integer\", errors=\"coerce\")\n", "\n", "df[\"order_projections\"] = round(\n", "    pd.to_numeric(df[\"order_projections\"], downcast=\"integer\", errors=\"coerce\")\n", ")\n", "\n", "df[\"attendance\"] = pd.to_numeric(df[\"attendance\"], downcast=\"float\", errors=\"coerce\")\n", "\n", "df[\"login_fe\"] = pd.to_numeric(df[\"login_fe\"], downcast=\"float\", errors=\"coerce\")\n", "\n", "df[\"required_day_logins\"] = round(\n", "    pd.to_numeric(df[\"required_day_logins\"], downcast=\"integer\", errors=\"coerce\")\n", ")\n", "df[\"required_day_fleet\"] = round(\n", "    pd.to_numeric(df[\"required_day_fleet\"], downcast=\"integer\", errors=\"coerce\")\n", ")\n", "df[\"required_week_fleet\"] = round(\n", "    pd.to_numeric(df[\"required_week_fleet\"], downcast=\"integer\", errors=\"coerce\")\n", ")\n", "\n", "df[\"expected_day_logins\"] = round(\n", "    pd.to_numeric(df[\"expected_day_logins\"], downcast=\"integer\", errors=\"coerce\")\n", ")\n", "df[\"expected_day_fleet\"] = round(\n", "    pd.to_numeric(df[\"expected_day_fleet\"], downcast=\"integer\", errors=\"coerce\")\n", ")\n", "df[\"expected_week_fleet\"] = round(\n", "    pd.to_numeric(df[\"expected_week_fleet\"], downcast=\"integer\", errors=\"coerce\")\n", ")\n", "\n", "df[\"predicted_dh\"] = pd.to_numeric(df[\"predicted_dh\"], downcast=\"float\", errors=\"coerce\")\n", "\n", "df[\"predicted_oph\"] = pd.to_numeric(df[\"predicted_oph\"], downcast=\"float\", errors=\"coerce\")\n", "\n", "df[\"predicted_rider_surge\"] = pd.to_numeric(\n", "    df[\"predicted_rider_surge\"], downcast=\"float\", errors=\"coerce\"\n", ")\n", "df[\"base_incentive_cpo\"] = pd.to_numeric(\n", "    df[\"base_incentive_cpo\"], downcast=\"float\", errors=\"coerce\"\n", ")\n", "df[\"festive_incentive_cpo\"] = pd.to_numeric(\n", "    df[\"festive_incentive_cpo\"], downcast=\"float\", errors=\"coerce\"\n", ")\n", "\n", "df[\"predicted_orders\"] = round(\n", "    pd.to_numeric(df[\"predicted_orders\"], downcast=\"integer\", errors=\"coerce\")\n", ")\n", "\n", "df[\"is_budget_applicable\"] = df[\"is_budget_applicable\"].astype(bool)"]}, {"cell_type": "code", "execution_count": null, "id": "0cd3b288-4d48-44f8-b820-fefab39ccae4", "metadata": {}, "outputs": [], "source": ["df[\"update_ts\"] = datetime.now() + <PERSON><PERSON>ta(hours=5.5)"]}, {"cell_type": "code", "execution_count": null, "id": "bf5bbca2-31e6-4b8e-b1c2-94d8cc8258b8", "metadata": {}, "outputs": [], "source": ["try:\n", "    sql = \"\"\" \n", "    \n", "    SELECT *\n", "    FROM logistics_data_etls.daily_last_mile_fleet_projections_v1\n", "    WHERE date >= current_date - interval '10' day\n", "    \n", "    \"\"\"\n", "\n", "    df_ = pd.read_sql_query(sql, conn)\n", "\n", "    df_[\"is_budget_applicable\"] = np.where(df_[\"is_budget_applicable\"] == \"False\", False, True)\n", "\n", "    df_[\"date\"] = pd.to_datetime(df_[\"date\"], errors=\"coerce\")\n", "    df_[\"update_ts\"] = pd.to_datetime(df_[\"update_ts\"], errors=\"coerce\")\n", "\n", "    # Split df_ based on today's date\n", "    df__past = df_[df_[\"date\"] <= today].copy()\n", "    df__future = df_[df_[\"date\"] > today].copy()\n", "\n", "    # Filter df future rows\n", "    df_future = df[df[\"date\"] > today].copy()\n", "\n", "    # Prepare a key to join on: (outlet, date)\n", "    df_future[\"key\"] = df_future[\"outlet_id\"].astype(str) + \"|\" + df_future[\"date\"].astype(str)\n", "    df__future[\"key\"] = df__future[\"outlet_id\"].astype(str) + \"|\" + df__future[\"date\"].astype(str)\n", "\n", "    # Create a lookup for outlets+dates where is_budget_applicable is True in df\n", "    true_keys = set(df_future[df_future[\"is_budget_applicable\"] == True][\"key\"])\n", "\n", "    # Set is_budget_applicable = False in df__future if key exists in true_keys\n", "    df__future[\"is_budget_applicable\"] = df__future.apply(\n", "        lambda row: False if row[\"key\"] in true_keys else row[\"is_budget_applicable\"], axis=1\n", "    )\n", "\n", "    # Drop the helper key column\n", "    df__future.drop(columns=[\"key\"], inplace=True)\n", "    df_future.drop(columns=[\"key\"], inplace=True)\n", "\n", "    # Final result: always include df__past, then handle future rows\n", "    result_df = pd.concat([df__past, df__future, df_future], ignore_index=True)\n", "\n", "except Exception as e:\n", "    # Fallback: df_ not available, use df as-is\n", "    print(f\"Warning: Failed to retrieve or process df_: {e}\")\n", "    result_df = df"]}, {"cell_type": "code", "execution_count": null, "id": "bc6b8cda-d8a4-4d19-8792-106bd14de107", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"daily_last_mile_fleet_projections\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date\", \"type\": \"date\", \"description\": \"Date\"},\n", "        {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"Outlet id\"},\n", "        {\"name\": \"outlet_name\", \"type\": \"varchar\", \"description\": \"Outlet name\"},\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"City\"},\n", "        {\"name\": \"order_projections\", \"type\": \"double\", \"description\": \"Order projections\"},\n", "        {\"name\": \"attendance\", \"type\": \"double\", \"description\": \"Attendance\"},\n", "        {\"name\": \"login_fe\", \"type\": \"double\", \"description\": \"Login fe\"},\n", "        {\"name\": \"required_day_logins\", \"type\": \"double\", \"description\": \"Required day logins\"},\n", "        {\"name\": \"required_day_fleet\", \"type\": \"double\", \"description\": \"Required day fleet\"},\n", "        {\"name\": \"required_week_fleet\", \"type\": \"double\", \"description\": \"Required week fleet\"},\n", "        {\"name\": \"expected_day_logins\", \"type\": \"double\", \"description\": \"Expected day logins\"},\n", "        {\"name\": \"expected_day_fleet\", \"type\": \"double\", \"description\": \"Expected day fleet\"},\n", "        {\"name\": \"expected_week_fleet\", \"type\": \"double\", \"description\": \"Expected week fleet\"},\n", "        {\"name\": \"predicted_dh\", \"type\": \"double\", \"description\": \"Predicted dh\"},\n", "        {\"name\": \"predicted_oph\", \"type\": \"double\", \"description\": \"Predicted oph\"},\n", "        {\"name\": \"predicted_rider_surge\", \"type\": \"double\", \"description\": \"Predicted rider surge\"},\n", "        {\"name\": \"base_incentive_cpo\", \"type\": \"double\", \"description\": \"Base incentive cpo\"},\n", "        {\"name\": \"festive_incentive_cpo\", \"type\": \"double\", \"description\": \"Festive incentive cpo\"},\n", "        {\"name\": \"predicted_orders\", \"type\": \"double\", \"description\": \"Predicted orders\"},\n", "        {\"name\": \"is_budget_applicable\", \"type\": \"boolean\", \"description\": \"Is budget applicable\"},\n", "        {\"name\": \"update_ts\", \"type\": \"timestamp(6)\", \"description\": \"Table update timestamp\"},\n", "    ],\n", "    \"primary_key\": [\"date\", \"outlet_id\", \"update_ts\"],\n", "    \"partition_key\": [\"date\"],\n", "    \"incremental_key\": \"date\",\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"Last mile fleet supply outlook\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "691b0d67-71b2-4547-9313-cab7cbcee80f", "metadata": {}, "outputs": [], "source": ["pb.to_trino(result_df, **kwargs_trino)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}