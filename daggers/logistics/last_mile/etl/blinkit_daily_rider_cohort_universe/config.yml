alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: blinkit_daily_rider_cohort_universe
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: logistics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U06JYRZ5VSL
path: logistics/last_mile/etl/blinkit_daily_rider_cohort_universe
paused: false
pool: logistics_pool
project_name: last_mile
schedule:
  end_date: '2025-09-20T00:00:00'
  interval: 35 3 * * *
  start_date: '2025-06-22T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
