{"cells": [{"cell_type": "code", "execution_count": null, "id": "5083e251-739c-4aba-b2f5-60d496338b8f", "metadata": {}, "outputs": [], "source": ["!pip install fastkml\n", "!pip install matplotlib==3.8"]}, {"cell_type": "code", "execution_count": null, "id": "82a65176-9dc1-4ce5-b884-85c052e31508", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import shapely as sh\n", "\n", "# import folium\n", "import fiona\n", "import os\n", "import datetime as dt\n", "from datetime import datetime, timedelta\n", "import numpy as np\n", "from pencilbox.connections import get_slack_client\n", "\n", "codsworth_client = get_slack_client(\"bl-analytics-bot\")\n", "\n", "import io\n", "import geopandas as gpd\n", "from fastkml import kml\n", "from shapely.geometry import Point, Polygon\n", "import time\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "id": "e9fc94ce-eafc-4f9c-be77-a009d70ff43e", "metadata": {}, "outputs": [], "source": ["gpd.io.file.fiona.drvsupport.supported_drivers[\"KML\"] = \"rw\""]}, {"cell_type": "code", "execution_count": null, "id": "4262cbc7-630e-4f47-9330-50a87a6a2d34", "metadata": {}, "outputs": [], "source": ["import warnings\n", "from shapely.errors import ShapelyDeprecationWarning\n", "\n", "# Filter out the ShapelyDeprecationWarning\n", "warnings.filterwarnings(\"ignore\", category=ShapelyDeprecationWarning)"]}, {"cell_type": "code", "execution_count": null, "id": "cce92152-fc24-4ee0-8013-9130544651b6", "metadata": {}, "outputs": [], "source": ["# df1 = pd.read_sql_query(\n", "#     sql=f\"\"\"\n", "#    SELECT * FROM logistics_data_etls.sample_test_for_kml_v1\n", "# \"\"\",\n", "#     con=pb.get_connection(\"[Warehouse] Trino\"),\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "e05bcbb5-a0bd-417b-9f71-5918b56f9e1e", "metadata": {}, "outputs": [], "source": ["# df1 = df1.astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "145c1cb5-b702-4ace-aff2-2df06085bce6", "metadata": {}, "outputs": [], "source": ["# df1[\"row\"] = df1[\"row\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "b17b364f-a541-4f48-b253-8419545e37dc", "metadata": {}, "outputs": [], "source": ["# df1"]}, {"cell_type": "code", "execution_count": null, "id": "c321386d-a2a5-40a0-9696-e4ea674785ff", "metadata": {}, "outputs": [], "source": ["# df1.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "24c2213a-daff-4d7e-8718-f6a333c3c9d1", "metadata": {}, "outputs": [], "source": ["# Adjust the row number to specify how many rows you want to test locally. Update the row count in the Google Sheet accordingly, and modify abcd.csv to include only those rows.\n", "# df = df1[df1[\"row\"] >= 470].reset_index(drop=True)\n", "# df = df1[df1[\"row\"] >= 0].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "29c8bb13-a007-44b7-8d58-d76313f6adca", "metadata": {}, "outputs": [], "source": ["# df = df.reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "0c7d3460-035f-405c-8233-dd123a297c10", "metadata": {}, "outputs": [], "source": ["# df[\"row\"] = df.index + 1"]}, {"cell_type": "code", "execution_count": null, "id": "04ea7d89-3d8e-4337-b7fe-4275a6f7c174", "metadata": {}, "outputs": [], "source": ["# df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "2f5db23b-5d88-4b84-8bfc-18b46ed97ac4", "metadata": {}, "outputs": [], "source": ["current_date = pd.Timestamp.now().strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "467f0b35-67cf-4ae8-91f0-2bf5a29d4ff7", "metadata": {}, "outputs": [], "source": ["current_date"]}, {"cell_type": "code", "execution_count": null, "id": "d4a58559-b8b1-44c3-b84f-17df439d3d0c", "metadata": {}, "outputs": [], "source": ["# df = df.astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "a2863163-ba42-4c63-b4f5-1fc5112aff13", "metadata": {}, "outputs": [], "source": ["# df"]}, {"cell_type": "code", "execution_count": null, "id": "24b16d0e-bc38-4715-bdd4-4f07fc6f20da", "metadata": {}, "outputs": [], "source": ["print(current_date)"]}, {"cell_type": "code", "execution_count": null, "id": "02eaeacd-2a6f-4ba6-8b65-781d9fd822f0", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1AHaGkKz84XRLSmmROJ-p6sUj85p5fxczbrGTg0rXm6I\"\n", "sheet_name = \"dumps_kml\"\n", "csv_df = pb.from_sheets(sheet_id, sheet_name)\n", "\n", "# csv_df=pd.read_csv('abcd.csv')\n", "csv_df"]}, {"cell_type": "code", "execution_count": null, "id": "6c3c2bc4-6c11-4a0a-9ece-4d0f025de382", "metadata": {}, "outputs": [], "source": ["csv_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "758e3c37-dfd1-4e28-8043-6a2019ee2d01", "metadata": {}, "outputs": [], "source": ["df = csv_df[\n", "    [\n", "        \"row\",\n", "        \"polygon_name\",\n", "        \"order_type\",\n", "        \"start_date\",\n", "        \"end_date\",\n", "        \"extra_distance_travelled\",\n", "        \"compensation_logic\",\n", "        \"frontend_merchant_id\",\n", "    ]\n", "].copy()"]}, {"cell_type": "code", "execution_count": null, "id": "a46dd033-c8f3-4805-924c-ed5ad4f1dbc6", "metadata": {}, "outputs": [], "source": ["df = df.astype(str)\n", "df"]}, {"cell_type": "code", "execution_count": null, "id": "ae6cdf50-e752-4ac1-bb13-36360dff5c60", "metadata": {}, "outputs": [], "source": ["x = len(df)\n", "Orders = pd.DataFrame()\n", "x"]}, {"cell_type": "code", "execution_count": null, "id": "7879713a-896d-4ab3-8e46-7334f5b2d30e", "metadata": {}, "outputs": [], "source": ["csv_df[\"start_hour\"] = pd.to_numeric(csv_df[\"start_hour\"], errors=\"coerce\").astype(\"Int64\")\n", "csv_df[\"end_hour\"] = pd.to_numeric(csv_df[\"end_hour\"], errors=\"coerce\").astype(\"Int64\")"]}, {"cell_type": "code", "execution_count": null, "id": "a92394ff-8737-434a-ad99-c949e1a40acb", "metadata": {}, "outputs": [], "source": ["BATCH_SIZE = 25\n", "start_date = (pd.Timestamp.now() - timed<PERSON><PERSON>(days=10)).strftime(\"%Y-%m-%d\")\n", "end_date = (pd.Timestamp.now() - timed<PERSON><PERSON>(days=1)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "19bdcd07-1dbc-485e-a0ce-f37036ae6b54", "metadata": {}, "outputs": [], "source": ["start_date"]}, {"cell_type": "code", "execution_count": null, "id": "cea84224-34f8-404a-a443-601cce3bb0bb", "metadata": {}, "outputs": [], "source": ["df[\"end_date\"] = pd.to_datetime(df[\"end_date\"], errors=\"coerce\").dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "03bbf824-d057-4cd9-84f2-5d24a6a807c8", "metadata": {}, "outputs": [], "source": ["# date_to_consider = pd.Timestamp.now() - <PERSON><PERSON><PERSON>(days=25)\n", "# date_to_consider"]}, {"cell_type": "code", "execution_count": null, "id": "44b7f860-3ed8-483f-ae81-8736d0619c9d", "metadata": {}, "outputs": [], "source": ["store_ids = df.loc[\n", "    df[\"end_date\"] >= pd.to_datetime(start_date).date(), \"frontend_merchant_id\"\n", "].unique()\n", "batched_store_ids = [store_ids[i : i + BATCH_SIZE] for i in range(0, len(store_ids), BATCH_SIZE)]\n", "batched_store_ids"]}, {"cell_type": "code", "execution_count": null, "id": "e1b886e3-9997-4060-b099-b82c1b3a372a", "metadata": {}, "outputs": [], "source": ["# filtered_batches = [batch for batch in batched_store_ids if '34360' in batch]\n", "# filtered_batches\n", "# batch_ids_str = filtered_batches\n", "# batch_ids_str"]}, {"cell_type": "code", "execution_count": null, "id": "af99b281-b4fb-478e-9909-afbfca9e7159", "metadata": {}, "outputs": [], "source": ["final_poly_df = pd.DataFrame()\n", "final_poly_df"]}, {"cell_type": "code", "execution_count": null, "id": "b05cf0b5-c73a-4462-a423-b3ad1a329313", "metadata": {}, "outputs": [], "source": ["for batch in batched_store_ids:\n", "    sql_start = time.time()\n", "    # print(batch)\n", "    batch_ids_str = \",\".join(map(str, batch))\n", "    print(batch_ids_str)\n", "    batch_query = f\"\"\"\n", "        SELECT\n", "            frontend_merchant_id,\n", "            order_checkout_ts_ist,\n", "            DATE(order_checkout_ts_ist) snapshot_dt_ist,\n", "            HOUR(order_checkout_ts_ist) snapshot_hr_ist,\n", "            fs.order_id,\n", "            order_current_status,\n", "            partner_id,\n", "            customer_location_latitude,\n", "            customer_location_longitude,\n", "            trip_pay,\n", "            incentive_pay,\n", "            CASE WHEN trip_ming > 0 THEN 6 ELSE TRY(drop_distance_pay * 1.00 / CASE WHEN drop_distance >= 1 THEN drop_distance ELSE 1 END) END AS dd_per_km,\n", "            drop_distance,\n", "            drop_distance_pay,\n", "            trip_ming, \n", "            ldrp, \n", "            surge_pay,\n", "            sm.runnr_city,\n", "            sm.blinkit_store_name,\n", "            drop_ping_distance,\n", "            drop_google_distance,\n", "            flag as distance_to_consider_flag,\n", "            drop_cell_token_l16,\n", "            return_distance,\n", "            google_distance_polyline,\n", "            ping_distance_polyline\n", "        FROM \n", "            (\n", "            Select \n", "                order_checkout_dt_ist,\n", "                order_checkout_ts_ist,\n", "                frontend_merchant_id,\n", "                order_id,\n", "                order_current_status,\n", "                partner_id,\n", "                customer_location_latitude,\n", "                customer_location_longitude\n", "            from dwh.fact_supply_chain_order_details \n", "            where order_checkout_dt_ist between DATE('{start_date}')  and DATE('{end_date}')\n", "\n", "            union \n", "\n", "            Select \n", "                order_checkout_dt_ist,\n", "                order_checkout_ts_ist,\n", "                frontend_merchant_id,\n", "                order_id,\n", "                order_current_status,\n", "                partner_id,\n", "                customer_location_latitude,\n", "                customer_location_longitude\n", "            from bistro_etls.fact_supply_chain_order_details_bistro\n", "            where order_checkout_dt_ist between DATE('{start_date}')  and DATE('{end_date}')\n", "        )fs\n", "        LEFT JOIN zomato.logs_dashboard_etls.trip_pay tp ON tab_id1 = CAST(fs.order_id AS VARCHAR)\n", "            AND dt BETWEEN DATE_FORMAT(DATE('{start_date}'), '%%Y%%m%%d') AND DATE_FORMAT(DATE('{end_date}'), '%%Y%%m%%d')\n", "        LEFT JOIN logistics_data_etls.blinkit_store_mapping sm ON sm.blinkit_store_id = fs.frontend_merchant_id\n", "        LEFT JOIN (\n", "            SELECT\n", "                date, trip_id, order_id tab_id1, drop_accounting_distance, drop_ping_distance, drop_cell_token_l16, merchant_id, blinkit_store_name, runnr_city,\n", "                CASE WHEN ((drop_ping_distance - drop_accounting_distance >= 0.5) OR (return_distance - drop_accounting_distance >= 0.5)) THEN 1 ELSE 0 END AS flag,\n", "                confidence, address_confidence, drop_google_distance, return_distance, google_distance_polyline, ping_distance_polyline\n", "            FROM (\n", "                SELECT \n", "                    DATE(time) date, trip_id, MAX_BY(tab_id1, time) order_id,\n", "                    MAX_BY(drop_accounting_distance, time) drop_accounting_distance,\n", "                    MAX_BY(drop_ping_distance, time) drop_ping_distance,\n", "                    MAX_BY(drop_google_distance, time) drop_google_distance,\n", "                    MAX_BY(drop_cell_token_l16, time) drop_cell_token_l16,\n", "                    MAX_BY(merchant_id, time) merchant_id,\n", "                    MAX_BY(confidence, time) confidence, MAX_BY(address_confidence, time) address_confidence,\n", "                    MAX_BY(return_distance, time) return_distance,\n", "                    MAX_BY(google_distance_polyline, time) AS google_distance_polyline,\n", "                    MAX_BY(ping_distance_polyline, time) AS ping_distance_polyline\n", "                FROM logistics_data_etls.trip_quality_details\n", "                WHERE order_current_status = 'DELIVERED'\n", "                AND dt >= DATE_FORMAT(DATE('{start_date}'), '%%Y-%%m-%%d')\n", "                GROUP BY 1, 2\n", "            ) b\n", "            LEFT JOIN logistics_data_etls.blinkit_store_mapping sm ON sm.blinkit_store_id = b.merchant_id\n", "        ) ping_dis ON ping_dis.tab_id1 = CAST(fs.order_id AS VARCHAR)\n", "        WHERE order_checkout_dt_ist BETWEEN DATE('{start_date}') AND DATE('{end_date}')\n", "        AND frontend_merchant_id IN ({batch_ids_str})\n", "        AND customer_location_latitude IS NOT NULL\n", "        AND customer_location_longitude IS NOT NULL\n", "        AND order_current_status = 'DELIVERED'\n", "    \"\"\"\n", "    batch_poly_df = pd.read_sql_query(batch_query, con=pb.get_connection(\"[Warehouse] Trino\"))\n", "    sql_end = time.time()\n", "    print(f\"Time taken for SQL batch query: {sql_end - sql_start:.2f}s\")\n", "    final_poly_df = final_poly_df.append(batch_poly_df, ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "657722ee-2dc6-44e9-ac92-1694d73ac2e1", "metadata": {}, "outputs": [], "source": ["final_poly_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4e16eca2-acf2-43bb-9b2e-8f2e710cb7f0", "metadata": {}, "outputs": [], "source": ["print(f\"Total records in final_poly_df: {final_poly_df.shape[0]}\")"]}, {"cell_type": "code", "execution_count": null, "id": "e92f6ce2-9814-4fdd-b651-f92f06d5656a", "metadata": {}, "outputs": [], "source": ["# Print all unique store IDs in final_poly_df before filtering\n", "print(\n", "    \"Unique frontend_merchant_id in final_poly_df:\", final_poly_df[\"frontend_merchant_id\"].unique()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2be4aa00-dbfb-466f-8e65-0208b4bfec20", "metadata": {}, "outputs": [], "source": ["df[\"frontend_merchant_id\"] = df[\"frontend_merchant_id\"].astype(int)\n", "final_poly_df[\"frontend_merchant_id\"] = final_poly_df[\"frontend_merchant_id\"].astype(int)\n", "final_poly_df[\"snapshot_dt_ist\"] = pd.to_datetime(\n", "    final_poly_df[\"snapshot_dt_ist\"], errors=\"coerce\"\n", ").dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "2307b125-6f9f-4426-9868-c57242588eef", "metadata": {}, "outputs": [], "source": ["for i in range(len(df)):\n", "    start_time = time.time()\n", "    print(f\"Processing row {i}\")\n", "\n", "    # Extract required fields\n", "    extract_start = time.time()\n", "    order_leg = df.loc[i, \"order_type\"]\n", "    row = df.loc[i, \"row\"]\n", "    polygon_name = df.loc[i, \"polygon_name\"]\n", "    extract_end = time.time()\n", "    # print(f\"Time taken for extraction: {extract_end - extract_start:.2f}s\")\n", "\n", "    # Skip iteration if end_date is before today\n", "    date_check_start = time.time()\n", "    new_start_date = pd.to_datetime(df.loc[i, \"start_date\"])\n", "    new_end_date = pd.to_datetime(df.loc[i, \"end_date\"])\n", "    if new_end_date.date() < pd.to_datetime(start_date).date():\n", "        print(f\"Skipping row {i}: `end_date` is before today.\")  # Debugging print\n", "        continue\n", "    date_check_end = time.time()\n", "    # print(f\"Time taken for date check: {date_check_end - date_check_start:.2f}s\")\n", "\n", "    extra_distance_travelled = df.loc[i, \"extra_distance_travelled\"]\n", "    compensation_logic = df.loc[i, \"compensation_logic\"]\n", "    frontend_merchant_id = df.loc[i, \"frontend_merchant_id\"]\n", "\n", "    # **Read KML data from Google Sheet**\n", "    kml_start = time.time()\n", "    kml_data = csv_df.loc[i, \"kml_dump\"]\n", "    if not isinstance(kml_data, str):\n", "        kml_data = str(kml_data) if pd.notna(kml_data) else \"\"\n", "\n", "    if not kml_data.strip():  # Safe check after conversion\n", "        print(f\"Skipping row {i}: Empty or invalid KML data.\")  # Debugging print\n", "        continue\n", "\n", "    try:\n", "        kml_bytes = io.BytesIO(kml_data.encode(\"utf-8\"))\n", "        new_df = gpd.read_file(kml_bytes, driver=\"KML\")\n", "        if new_df.empty:\n", "            print(f\"Skipping row {i}: KML read but no data found.\")  # Debugging print\n", "            continue\n", "    except Exception as e:\n", "        print(f\"Error reading KML for row {i}: {e}\")\n", "        import traceback\n", "\n", "        traceback.print_exc()  # Print full error traceback for debugging\n", "        continue\n", "    kml_end = time.time()\n", "    # print(f\"Time taken for KML processing: {kml_end - kml_start:.2f}s\")\n", "\n", "    # **Filter affected polygons**\n", "    affected_poly_df = new_df[new_df.Name == row].geometry.reset_index(drop=True)\n", "    if affected_poly_df.empty:\n", "        print(f\"Skipping row {i}: No affected polygons found.\")  # Debugging print\n", "        continue\n", "\n", "    # Filter the pre-fetched poly_df for the current store\n", "    # poly_df = final_poly_df[final_poly_df[\"frontend_merchant_id\"] == frontend_merchant_id].copy()\n", "    poly_df = final_poly_df[\n", "        (final_poly_df[\"frontend_merchant_id\"] == frontend_merchant_id)\n", "        & (final_poly_df[\"snapshot_dt_ist\"] >= new_start_date.date())\n", "        & (final_poly_df[\"snapshot_dt_ist\"] <= new_end_date.date())\n", "    ].copy()\n", "    if poly_df.empty:\n", "        print(\n", "            f\"Skipping row {i}: No matching store found in `final_poly_df` for Store ID {frontend_merchant_id}.\"\n", "        )  # Debugging print\n", "        continue\n", "\n", "    poly_df[\"point\"] = poly_df.apply(\n", "        lambda x: sh.geometry.Point(\n", "            x[\"customer_location_longitude\"], x[\"customer_location_latitude\"]\n", "        ),\n", "        axis=1,\n", "    )\n", "    poly_df[\"order_from_affected_area\"] = poly_df.point.apply(\n", "        lambda x: x.within(affected_poly_df[0])\n", "    )\n", "\n", "    poly_df[\"compensation\"] = compensation_logic\n", "    poly_df[\"info_type\"] = \"Blinkit Differential Distance Pay\"\n", "    poly_df[\"type\"] = \"Differential distance pay\"\n", "    poly_df[\"extra_distance_travelled_in_kms\"] = extra_distance_travelled\n", "    poly_df[\"order_leg\"] = order_leg\n", "    poly_df[\"polygon_name\"] = polygon_name\n", "\n", "    poly_df[\"order_checkout_ts_ist\"] = pd.to_datetime(poly_df[\"order_checkout_ts_ist\"])\n", "\n", "    # **Apply filtering dynamically from CSV**\n", "    start_hour = csv_df.loc[i, \"start_hour\"]\n", "    end_hour = csv_df.loc[i, \"end_hour\"]\n", "\n", "    if pd.notna(start_hour) and pd.notna(end_hour):  # Ensure valid values exist\n", "        if start_hour <= end_hour:\n", "            # Normal case: within the same day\n", "            temp = poly_df[\n", "                (poly_df[\"order_checkout_ts_ist\"].dt.hour >= start_hour)\n", "                & (poly_df[\"order_checkout_ts_ist\"].dt.hour < end_hour)\n", "            ]\n", "        else:\n", "            # Special case: crosses midnight (e.g., 22:00 - 06:00)\n", "            temp = poly_df[\n", "                (\n", "                    (poly_df[\"order_checkout_ts_ist\"].dt.hour >= start_hour)\n", "                    & (poly_df[\"order_checkout_ts_ist\"].dt.hour <= 23)\n", "                )\n", "                | (\n", "                    (poly_df[\"order_checkout_ts_ist\"].dt.hour >= 0)\n", "                    & (poly_df[\"order_checkout_ts_ist\"].dt.hour < end_hour)\n", "                )\n", "            ]\n", "\n", "        poly_df = poly_df.append(temp)\n", "\n", "    Orders = Orders.append(poly_df)  # Append filtered data\n", "\n", "    df.loc[i, \"start_date\"] = current_date  # Update start_date\n", "\n", "    end_time = time.time()\n", "    print(f\"Total time for row {i}: {end_time - start_time:.2f}s\")  # Final time tracking print"]}, {"cell_type": "code", "execution_count": null, "id": "f9db2176-d67c-43c8-ae2f-3269376a850f", "metadata": {}, "outputs": [], "source": ["Orders"]}, {"cell_type": "code", "execution_count": null, "id": "c6cd967f-0f57-41f1-a47d-3e7c21f5b1c0", "metadata": {}, "outputs": [], "source": ["Orders[\"order_checkout_ts_ist\"] = pd.to_datetime(Orders[\"order_checkout_ts_ist\"])\n", "Orders[\"drop_distance\"] = Orders[\"drop_distance\"].astype(float)\n", "Orders[\"drop_ping_distance\"] = Orders[\"drop_ping_distance\"].astype(float)\n", "Orders[\"extra_distance_travelled_in_kms\"] = Orders[\"extra_distance_travelled_in_kms\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "e17c6ade-d0de-4194-b49c-f82667ee4cbe", "metadata": {}, "outputs": [], "source": ["# row = Orders[Orders[\"order_id\"] == *********]\n", "# print(row)"]}, {"cell_type": "code", "execution_count": null, "id": "55db3b5c-3596-4350-9114-501e7f8de128", "metadata": {}, "outputs": [], "source": ["Orders.dtypes\n", "Orders\n", "# Orders.to_csv('1.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "99a97dc3-124a-43a3-9483-a46e3cd16e04", "metadata": {}, "outputs": [], "source": ["affected_poly_df"]}, {"cell_type": "code", "execution_count": null, "id": "5a6bfa7d-bb51-4b20-a297-ef3b797086fc", "metadata": {}, "outputs": [], "source": ["Orders[\"delta_bw_pd_dd\"] = Orders[\"drop_ping_distance\"] - Orders[\"drop_distance\"]"]}, {"cell_type": "code", "execution_count": null, "id": "01960ade-b924-461a-9ef2-6dd61e6eb97e", "metadata": {}, "outputs": [], "source": ["Orders = Orders[Orders[\"order_from_affected_area\"] != False]\n", "Orders.reset_index(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "53c05ae4-6bc5-4bf7-9ea4-4e01f756ea1f", "metadata": {}, "outputs": [], "source": ["Orders_temp = Orders\n", "result_df = (\n", "    Orders_temp.groupby(\n", "        [\n", "            \"snapshot_dt_ist\",\n", "            \"snapshot_hr_ist\",\n", "            \"frontend_merchant_id\",\n", "            \"blinkit_store_name\",\n", "            \"runnr_city\",\n", "            \"drop_cell_token_l16\",\n", "        ]\n", "    )\n", "    .agg(flag_sum=(\"distance_to_consider_flag\", \"sum\"), total_orders=(\"order_id\", \"count\"))\n", "    .reset_index()\n", ")\n", "\n", "result_df"]}, {"cell_type": "code", "execution_count": null, "id": "ca8099aa-908c-48ac-8fe4-35cf868279dd", "metadata": {}, "outputs": [], "source": ["!pip install pandasql\n", "from pandasql import sqldf"]}, {"cell_type": "code", "execution_count": null, "id": "82aae211-a85b-432a-a9e4-6f28e0b3e8ac", "metadata": {}, "outputs": [], "source": ["pysql = sqldf(\n", "    \"\"\"\n", "    select snapshot_dt_ist , frontend_merchant_id , blinkit_store_name , runnr_city , drop_cell_token_l16 , \n", "    sum(case when snapshot_hr_ist in (7, 8, 9 , 10 , 11 ,12 ,13 , 14 , 15, 16 , 17 ,18) then flag_sum end) as flag_sum_shift_1  ,\n", "    sum(case when snapshot_hr_ist in (7, 8, 9 , 10 , 11 ,12 ,13 , 14 , 15, 16 , 17 ,18) then total_orders end) as total_orders_shift_1 ,\n", "    sum(case when snapshot_hr_ist in (19 , 20 , 21 ,22 ,23 , 0 , 1,  2 ,3,4,5,6) then flag_sum end) as flag_sum_shift_2  ,\n", "    sum(case when snapshot_hr_ist in (19 , 20 , 21 ,22 ,23 , 0 , 1,  2 ,3,4,5,6) then total_orders end) as total_orders_shift_2,\n", "    sum(flag_sum) as flag_sum_day\n", "    from result_df\n", "    group by 1 ,2 , 3,4,5\n", "\"\"\"\n", ")\n", "\n", "pysql[\"flag_sum_shift_1\"] = pysql[\"flag_sum_shift_1\"].fillna(0)\n", "pysql[\"total_orders_shift_1\"] = pysql[\"total_orders_shift_1\"].fillna(0)\n", "pysql[\"flag_sum_shift_2\"] = pysql[\"flag_sum_shift_2\"].fillna(0)\n", "pysql[\"total_orders_shift_2\"] = pysql[\"total_orders_shift_2\"].fillna(0)\n", "pysql[\"shift_1_percentage\"] = (pysql[\"flag_sum_shift_1\"] * 100) / pysql[\"total_orders_shift_1\"]\n", "pysql[\"shift_2_percentage\"] = (pysql[\"flag_sum_shift_2\"] * 100) / pysql[\"total_orders_shift_2\"]\n", "pysql[\"flag_sum_day\"] = pysql[\"flag_sum_day\"].fillna(0)\n", "pysql = pysql[\n", "    [\n", "        \"snapshot_dt_ist\",\n", "        \"frontend_merchant_id\",\n", "        \"blinkit_store_name\",\n", "        \"runnr_city\",\n", "        \"drop_cell_token_l16\",\n", "        \"flag_sum_shift_1\",\n", "        \"shift_1_percentage\",\n", "        \"flag_sum_shift_2\",\n", "        \"shift_2_percentage\",\n", "        \"flag_sum_day\",\n", "    ]\n", "]\n", "pysql[\"shift_1_percentage\"] = pysql[\"shift_1_percentage\"].fillna(0)\n", "pysql[\"shift_2_percentage\"] = pysql[\"shift_2_percentage\"].fillna(0)\n", "pysql"]}, {"cell_type": "code", "execution_count": null, "id": "9648f44c-ddf5-4dd9-838c-6b93d15e7116", "metadata": {}, "outputs": [], "source": ["pysql[\"snapshot_dt_ist\"] = pd.to_datetime(pysql[\"snapshot_dt_ist\"]).dt.strftime(\"%Y-%m-%d\")\n", "pysql"]}, {"cell_type": "code", "execution_count": null, "id": "7de6bd19-b692-4a09-a685-7e22af85cdea", "metadata": {}, "outputs": [], "source": ["Orders[\"snapshot_dt_ist\"] = pd.to_datetime(Orders[\"snapshot_dt_ist\"], errors=\"coerce\")\n", "pysql[\"snapshot_dt_ist\"] = pd.to_datetime(pysql[\"snapshot_dt_ist\"], errors=\"coerce\")\n", "\n", "\n", "Orders[\"snapshot_dt_ist\"] = Orders[\"snapshot_dt_ist\"].astype(str)\n", "pysql[\"snapshot_dt_ist\"] = pysql[\"snapshot_dt_ist\"].astype(str)\n", "\n", "join_columns = [\n", "    \"snapshot_dt_ist\",\n", "    \"frontend_merchant_id\",\n", "    \"blinkit_store_name\",\n", "    \"runnr_city\",\n", "    \"drop_cell_token_l16\",\n", "]\n", "Orders = pd.merge(Orders, pysql, how=\"left\", on=join_columns)\n", "Orders"]}, {"cell_type": "code", "execution_count": null, "id": "ffacee15-0ad1-4f4a-9978-f98e142c5184", "metadata": {}, "outputs": [], "source": ["Orders[\"distance_to_consider_flag\"] = Orders[\"distance_to_consider_flag\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "7580b927-6300-4207-9166-4b78849d8d3a", "metadata": {}, "outputs": [], "source": ["Orders.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "c7932710-affa-462e-80de-969ff39fee82", "metadata": {}, "outputs": [], "source": ["data_ = Orders"]}, {"cell_type": "code", "execution_count": null, "id": "31608013-81f6-4f1a-b878-580b8dfb7fe5", "metadata": {}, "outputs": [], "source": ["Orders = Orders[\n", "    (\n", "        (Orders[\"shift_1_percentage\"] >= 50)\n", "        | (Orders[\"shift_2_percentage\"] >= 50)\n", "        | (Orders[\"flag_sum_day\"] <= 5)\n", "        #   | (Orders['flag_sum_shift_1'] <= 5) | (Orders['flag_sum_shift_2'] <= 5)\n", "        | ((Orders[\"order_leg\"] == \"RETURN LEG\") & (Orders[\"distance_to_consider_flag\"] > 0))\n", "    )\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "7efb969f-54f4-44c9-b5e3-b3e061c39bba", "metadata": {}, "outputs": [], "source": ["Orders.head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "9af4352c-c70b-4f13-ad90-0297ab264480", "metadata": {}, "outputs": [], "source": ["Orders.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "07e478ec-1a53-483f-943d-c09dc48d7e53", "metadata": {}, "outputs": [], "source": ["Orders1 = Orders[(Orders[\"delta_bw_pd_dd\"] >= 0.5) & (Orders[\"order_leg\"] == \"ORDER RUN\")]\n", "Orders2 = Orders[Orders[\"order_leg\"] == \"RETURN LEG\"]\n", "Orders1"]}, {"cell_type": "code", "execution_count": null, "id": "eef10c9e-de69-4e9f-98a6-8d2dd3b69e32", "metadata": {}, "outputs": [], "source": ["Orders2"]}, {"cell_type": "code", "execution_count": null, "id": "7e19b035-fae9-4f3a-b3f2-d929cc0dcce8", "metadata": {}, "outputs": [], "source": ["Orders.drop(Orders.index, inplace=True)\n", "Orders = Orders.append(Orders1)\n", "Orders = Orders.append(Orders2)\n", "Orders.drop(\"index\", axis=1, inplace=True)\n", "Orders"]}, {"cell_type": "code", "execution_count": null, "id": "26f77750-61c4-40c0-97c3-cde134edda16", "metadata": {}, "outputs": [], "source": ["Orders[\"extra_distance_travelled_in_kms\"] = pd.to_numeric(\n", "    Orders[\"extra_distance_travelled_in_kms\"], errors=\"coerce\"\n", ")\n", "Orders[\"dd_per_km\"] = pd.to_numeric(Orders[\"dd_per_km\"], errors=\"coerce\")\n", "Orders[\"delta_bw_pd_dd\"] = pd.to_numeric(Orders[\"delta_bw_pd_dd\"], errors=\"coerce\")\n", "Orders[\"ldrp\"] = pd.to_numeric(Orders[\"ldrp\"], errors=\"coerce\")\n", "\n", "Orders[\"amount\"] = Orders.apply(\n", "    lambda row: (\n", "        row[\"delta_bw_pd_dd\"] * row[\"dd_per_km\"]\n", "        if row[\"compensation\"] == \"Order level Distance Pay per KM\"\n", "        else (\n", "            10\n", "            if row[\"compensation\"] == \"Flat Rs 10 per Order\"\n", "            else (\n", "                5\n", "                if row[\"compensation\"] == \"Flat Rs 5 Per Order\"\n", "                else (\n", "                    2.5\n", "                    if row[\"compensation\"] == \"Flat Rs 2.5 Per Order\"\n", "                    else (\n", "                        7\n", "                        if row[\"compensation\"] == \"Flat Rs 7 Per Order\"\n", "                        else (\n", "                            3\n", "                            if row[\"compensation\"] == \"Flat Rs 3 per Order\"\n", "                            else (\n", "                                3.5\n", "                                if row[\"compensation\"] == \"Flat Rs 3.5 per Order\"\n", "                                else (\n", "                                    4\n", "                                    if row[\"compensation\"] == \"Flat Rs 4 per Order\"\n", "                                    else (\n", "                                        8\n", "                                        if row[\"compensation\"] == \"Flat Rs 8 per Order\"\n", "                                        else (\n", "                                            12\n", "                                            if row[\"compensation\"] == \"Flat Rs 12 per Order\"\n", "                                            else (\n", "                                                25\n", "                                                if row[\"compensation\"] == \"Flat Rs 25 per Order\"\n", "                                                else None\n", "                                            )\n", "                                        )\n", "                                    )\n", "                                )\n", "                            )\n", "                        )\n", "                    )\n", "                )\n", "            )\n", "        )\n", "    ),\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1bb1cc20-ba90-4b6b-9995-4416def9b9b1", "metadata": {}, "outputs": [], "source": ["Orders[\"amount\"] = Orders[\"amount\"].apply(lambda x: 0 if x is None else x)\n", "Orders[\"amount\"] = Orders[\"amount\"].apply(lambda x: abs(x))\n", "Orders[\"delta_bw_pd_dd\"] = Orders[\"delta_bw_pd_dd\"].apply(lambda x: abs(x))\n", "Orders[\"delta_ldrp\"] = Orders[\"delta_bw_pd_dd\"].apply(lambda x: np.ceil(abs(x)))\n", "Orders[\"amount\"] = Orders.apply(\n", "    lambda row: (\n", "        row[\"amount\"] + (row[\"delta_ldrp\"] * 5)\n", "        if (\n", "            row[\"drop_ping_distance\"] > 5\n", "            and row[\"compensation\"] == \"Order level Distance Pay per KM\"\n", "        )\n", "        else row[\"amount\"]\n", "    ),\n", "    axis=1,\n", ")\n", "Orders[\"amount\"] = Orders.apply(\n", "    lambda row: (\n", "        row[\"delta_bw_pd_dd\"] * row[\"dd_per_km\"]\n", "        if (\n", "            row[\"delta_bw_pd_dd\"] * row[\"dd_per_km\"] < row[\"amount\"]\n", "            and row[\"runnr_city\"] == \"Gurgaon\"\n", "            and row[\"order_leg\"] == \"ORDER RUN\"\n", "        )\n", "        else row[\"amount\"]\n", "    ),\n", "    axis=1,\n", ")\n", "\n", "Orders"]}, {"cell_type": "code", "execution_count": null, "id": "203cbab2-5398-4b87-a278-d45c99207468", "metadata": {}, "outputs": [], "source": ["remaining_data = data_[~data_[\"order_id\"].isin(Orders[\"order_id\"])]"]}, {"cell_type": "code", "execution_count": null, "id": "da2c3523-4bf7-4aab-8a41-3e4e33cfcaff", "metadata": {}, "outputs": [], "source": ["Orders"]}, {"cell_type": "code", "execution_count": null, "id": "6f1cfbd2-8c80-44cc-9088-e204b8a32b0f", "metadata": {}, "outputs": [], "source": ["Orders.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "53ef6551-cf85-4b6b-b7d8-1b69f653673f", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(Orders, \"1AHaGkKz84XRLSmmROJ-p6sUj85p5fxczbrGTg0rXm6I\", \"or\")\n", "# pb.to_sheets(remaining_data, \"1AHaGkKz84XRLSmmROJ-p6sUj85p5fxczbrGTg0rXm6I\", \"eor\")"]}, {"cell_type": "code", "execution_count": null, "id": "9f6681c3-682e-491a-81bb-ffb1d0adbbaf", "metadata": {}, "outputs": [], "source": ["Orders"]}, {"cell_type": "code", "execution_count": null, "id": "bc8f414c-b945-4862-8143-0ae792190ec8", "metadata": {}, "outputs": [], "source": ["new_orders_df = Orders.merge(\n", "    csv_df[[\"polygon_name\", \"final_capping\"]], on=\"polygon_name\", how=\"left\"\n", ")\n", "new_orders_df[\"final_capping\"] = new_orders_df[\"final_capping\"].astype(float)\n", "new_orders_df[\"amount\"] = new_orders_df[\"amount\"].astype(float)\n", "new_orders_df[\"final_amount_to_consider\"] = np.where(\n", "    new_orders_df[\"amount\"].notnull(),\n", "    new_orders_df[[\"final_capping\", \"amount\"]].min(axis=1),\n", "    np.nan,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c80ba05e-fb5a-4342-89c1-5ba20b814485", "metadata": {}, "outputs": [], "source": ["new_orders_df = new_orders_df.rename(columns={\"amount\": \"actual_amount\"})"]}, {"cell_type": "code", "execution_count": null, "id": "8fb088c2-2656-4268-81b6-5b530a4e4cf7", "metadata": {}, "outputs": [], "source": ["remaining_data[\"actual_amount\"] = None\n", "remaining_data[\"delta_ldrp\"] = None\n", "remaining_data[\"final_amount_to_consider\"] = None\n", "remaining_data[\"category\"] = \"Exempted\"\n", "new_orders_df[\"category\"] = \"To_consider\"\n", "remaining_data = remaining_data.drop(columns=[\"index\"], errors=\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "1608bbc9-90cd-4c16-86aa-56938ae5dc9f", "metadata": {}, "outputs": [], "source": ["exempted_count = remaining_data.shape[0]  # or len(exempted_order_df)\n", "order_count = new_orders_df.shape[0]  # or len(Order_df)\n", "print(f\"Rows in exempted_order_df: {exempted_count}\")\n", "print(f\"Rows in Order_df: {order_count}\")"]}, {"cell_type": "code", "execution_count": null, "id": "17238457-22a5-4ed9-a7d8-84c97de9b3e8", "metadata": {}, "outputs": [], "source": ["remaining_data"]}, {"cell_type": "code", "execution_count": null, "id": "3dad85fd-2194-4577-9384-422dff3d89e1", "metadata": {}, "outputs": [], "source": ["new_orders_df"]}, {"cell_type": "code", "execution_count": null, "id": "83e7abdc-b0f1-479e-a8ca-3bc0ae4f30e1", "metadata": {}, "outputs": [], "source": ["final_df = pd.concat([remaining_data, new_orders_df], ignore_index=True)\n", "final_df"]}, {"cell_type": "code", "execution_count": null, "id": "5a794273-087a-4c11-b226-582bdc85d4b5", "metadata": {}, "outputs": [], "source": ["# final_df.to_csv('output.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "e2e2a5f9-c888-4dcf-adc9-45d7b40697f4", "metadata": {}, "outputs": [], "source": ["# final_df = final_df.drop(columns=['index'], errors='ignore')"]}, {"cell_type": "code", "execution_count": null, "id": "54109ecb-9f70-47ea-9dcc-3e5b0be8207b", "metadata": {}, "outputs": [], "source": ["final_df"]}, {"cell_type": "code", "execution_count": null, "id": "a5b0696d-0999-4a53-907a-829d6d5f8b4b", "metadata": {}, "outputs": [], "source": ["# Keep only the row with the minimum amount for each Order ID\n", "final_df[\"final_amount_to_consider\"] = final_df[\"final_amount_to_consider\"].fillna(0)\n", "final_df_filtered = final_df.loc[final_df.groupby(\"order_id\")[\"final_amount_to_consider\"].idxmin()]\n", "final_df_filtered = final_df_filtered.reset_index(drop=True)\n", "final_df_filtered"]}, {"cell_type": "code", "execution_count": null, "id": "30f1c192-5eb9-4a69-9cc5-7b62c1777ac8", "metadata": {}, "outputs": [], "source": ["# final_df_filtered.to_csv('output1.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "b441f7b6-106a-4850-9e37-b6e4b864fcb4", "metadata": {}, "outputs": [], "source": ["# existing_data = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "id": "d615ae3c-2412-4c33-b2af-aaedfabe8df0", "metadata": {}, "outputs": [], "source": ["# existing_data = f\"\"\"\n", "#     select\n", "#         disitnct order_id\n", "#     from logistics_data_etls.order_polygon_distance_extra_payout\n", "#     where snapshot_dt_ist >= current_date - interval '50' day\n", "# \"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "434eee2d-1c00-4715-b9df-a37b91060e17", "metadata": {}, "outputs": [], "source": ["# existing_data = pd.read_sql_query(existing_data, con=pb.get_connection(\"[Warehouse] Trino\"))"]}, {"cell_type": "code", "execution_count": null, "id": "0f2c1f7e-ee91-464c-87ff-39e1df7019d5", "metadata": {}, "outputs": [], "source": ["# dump_df = final_df[~final_df['order_id'].isin(existing_data['order_id'])]"]}, {"cell_type": "code", "execution_count": null, "id": "b7ea2f60-49ca-48df-9146-ce6897ed8750", "metadata": {}, "outputs": [], "source": ["dump_df = final_df_filtered"]}, {"cell_type": "code", "execution_count": null, "id": "e55fdd96-4ea4-4e31-a57c-553181fbe77e", "metadata": {}, "outputs": [], "source": ["dump_df[\"updated_at_ist\"] = dt.datetime.now() + dt.<PERSON><PERSON><PERSON>(minutes=330)"]}, {"cell_type": "code", "execution_count": null, "id": "73ae168f-8d3a-456b-9262-81c3a3aed463", "metadata": {}, "outputs": [], "source": ["dump_df"]}, {"cell_type": "code", "execution_count": null, "id": "61b80daf-50cc-4425-b604-d0cfbe516850", "metadata": {}, "outputs": [], "source": ["dump_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "f99d7fe8-0906-4099-b5cc-a61ade34855d", "metadata": {}, "outputs": [], "source": ["dump_df[\"snapshot_dt_ist\"] = pd.to_datetime(dump_df[\"snapshot_dt_ist\"], errors=\"coerce\")\n", "dump_df[\"trip_pay\"] = dump_df[\"trip_pay\"].astype(float)\n", "dump_df[\"incentive_pay\"] = dump_df[\"incentive_pay\"].astype(float)\n", "dump_df[\"dd_per_km\"] = dump_df[\"dd_per_km\"].astype(float)\n", "dump_df[\"drop_distance_pay\"] = dump_df[\"drop_distance_pay\"].astype(float)\n", "dump_df[\"surge_pay\"] = dump_df[\"surge_pay\"].astype(float)\n", "dump_df[\"trip_ming\"] = dump_df[\"trip_ming\"].astype(float)\n", "dump_df[\"ldrp\"] = dump_df[\"ldrp\"].astype(float)\n", "dump_df[\"point\"] = dump_df[\"point\"].astype(str)\n", "dump_df[\"extra_distance_travelled_in_kms\"] = dump_df[\"extra_distance_travelled_in_kms\"].astype(\n", "    float\n", ")\n", "dump_df[\"actual_amount\"] = dump_df[\"actual_amount\"].astype(float)\n", "dump_df[\"delta_ldrp\"] = dump_df[\"delta_ldrp\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "9798d488-562d-4cc8-a73a-b7ba238d025c", "metadata": {}, "outputs": [], "source": ["dump_df = dump_df[\n", "    [\n", "        \"frontend_merchant_id\",\n", "        \"order_checkout_ts_ist\",\n", "        \"snapshot_dt_ist\",\n", "        \"snapshot_hr_ist\",\n", "        \"order_id\",\n", "        \"order_current_status\",\n", "        \"partner_id\",\n", "        \"customer_location_latitude\",\n", "        \"customer_location_longitude\",\n", "        \"trip_pay\",\n", "        \"incentive_pay\",\n", "        \"dd_per_km\",\n", "        \"drop_distance\",\n", "        \"drop_distance_pay\",\n", "        \"trip_ming\",\n", "        \"ldrp\",\n", "        \"surge_pay\",\n", "        \"runnr_city\",\n", "        \"blinkit_store_name\",\n", "        \"drop_ping_distance\",\n", "        \"drop_google_distance\",\n", "        \"distance_to_consider_flag\",\n", "        \"drop_cell_token_l16\",\n", "        \"return_distance\",\n", "        \"google_distance_polyline\",\n", "        \"ping_distance_polyline\",\n", "        \"point\",\n", "        \"order_from_affected_area\",\n", "        \"compensation\",\n", "        \"info_type\",\n", "        \"type\",\n", "        \"extra_distance_travelled_in_kms\",\n", "        \"order_leg\",\n", "        \"polygon_name\",\n", "        \"delta_bw_pd_dd\",\n", "        \"actual_amount\",\n", "        \"delta_ldrp\",\n", "        \"final_amount_to_consider\",\n", "        \"category\",\n", "        \"updated_at_ist\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "ceec99b2-263f-4eab-b8b7-44cf91ed63f2", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"rider_extra_distance_payout_order_details\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"frontend_merchant_id\", \"type\": \"integer\", \"description\": \" frontend_merchant_id\"},\n", "        {\n", "            \"name\": \"order_checkout_ts_ist\",\n", "            \"type\": \"timestamp(6)\",\n", "            \"description\": \"order_checkout_ts_ist \",\n", "        },\n", "        {\"name\": \"snapshot_dt_ist\", \"type\": \"date\", \"description\": \"snapshot_dt_ist \"},\n", "        {\"name\": \"snapshot_hr_ist\", \"type\": \"integer\", \"description\": \"snapshot_hr_ist \"},\n", "        {\"name\": \"order_id\", \"type\": \"integer\", \"description\": \" order_id\"},\n", "        {\"name\": \"order_current_status\", \"type\": \"varchar\", \"description\": \" order_current_status\"},\n", "        {\"name\": \"partner_id\", \"type\": \"varchar\", \"description\": \"partner_id \"},\n", "        {\n", "            \"name\": \"customer_location_latitude\",\n", "            \"type\": \"double\",\n", "            \"description\": \"customer_location_latitude \",\n", "        },\n", "        {\n", "            \"name\": \"customer_location_longitude\",\n", "            \"type\": \"double\",\n", "            \"description\": \" customer_location_longitude\",\n", "        },\n", "        {\"name\": \"trip_pay\", \"type\": \"double\", \"description\": \"trip_pay \"},\n", "        {\"name\": \"incentive_pay\", \"type\": \"double\", \"description\": \"incentive_pay \"},\n", "        {\"name\": \"dd_per_km\", \"type\": \"double\", \"description\": \" dd_per_km\"},\n", "        {\"name\": \"drop_distance\", \"type\": \"double\", \"description\": \"drop_distance \"},\n", "        {\"name\": \"drop_distance_pay\", \"type\": \"double\", \"description\": \"drop_distance_pay \"},\n", "        {\"name\": \"trip_ming\", \"type\": \"double\", \"description\": \"trip_ming \"},\n", "        {\"name\": \"ldrp\", \"type\": \"double\", \"description\": \"ldrp \"},\n", "        {\"name\": \"surge_pay\", \"type\": \"double\", \"description\": \"surge_pay \"},\n", "        {\n", "            \"name\": \"runnr_city\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \" runnr_city\",\n", "        },\n", "        {\n", "            \"name\": \"blinkit_store_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \" blinkit_store_name\",\n", "        },\n", "        {\"name\": \"drop_ping_distance\", \"type\": \"double\", \"description\": \" drop_ping_distance\"},\n", "        {\"name\": \"drop_google_distance\", \"type\": \"double\", \"description\": \"drop_google_distance \"},\n", "        {\n", "            \"name\": \"distance_to_consider_flag\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"distance_to_consider_flag \",\n", "        },\n", "        {\"name\": \"drop_cell_token_l16\", \"type\": \"varchar\", \"description\": \"drop_cell_token_l16 \"},\n", "        {\"name\": \"return_distance\", \"type\": \"double\", \"description\": \"return_distance \"},\n", "        {\n", "            \"name\": \"google_distance_polyline\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"google_distance_polyline \",\n", "        },\n", "        {\n", "            \"name\": \"ping_distance_polyline\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"ping_distance_polyline \",\n", "        },\n", "        {\"name\": \"point\", \"type\": \"varchar\", \"description\": \"point\"},\n", "        {\n", "            \"name\": \"order_from_affected_area\",\n", "            \"type\": \"BOOLEAN\",\n", "            \"description\": \"order_from_affected_area \",\n", "        },\n", "        {\"name\": \"compensation\", \"type\": \"varchar\", \"description\": \" Compensation\"},\n", "        {\"name\": \"info_type\", \"type\": \"varchar\", \"description\": \"info_type \"},\n", "        {\"name\": \"type\", \"type\": \"varchar\", \"description\": \" type\"},\n", "        {\n", "            \"name\": \"extra_distance_travelled_in_kms\",\n", "            \"type\": \"double\",\n", "            \"description\": \"Extra_Distance_travelled_in_Kms\",\n", "        },\n", "        {\"name\": \"order_leg\", \"type\": \"varchar\", \"description\": \"order_leg \"},\n", "        {\"name\": \"polygon_name\", \"type\": \"varchar\", \"description\": \"polygon_name \"},\n", "        {\"name\": \"delta_bw_pd_dd\", \"type\": \"double\", \"description\": \" delta_bw_pd_dd\"},\n", "        {\"name\": \"actual_amount\", \"type\": \"double\", \"description\": \"actual_amount \"},\n", "        {\"name\": \"delta_ldrp\", \"type\": \"double\", \"description\": \"delta_ldrp\"},\n", "        {\n", "            \"name\": \"final_amount_to_consider\",\n", "            \"type\": \"double\",\n", "            \"description\": \"final_amount_to_consider\",\n", "        },\n", "        {\"name\": \"category\", \"type\": \"varchar\", \"description\": \"category\"},\n", "        {\"name\": \"updated_at_ist\", \"type\": \"timestamp(6)\", \"description\": \" \"},\n", "    ],\n", "    \"primary_key\": [\"order_id\"],\n", "    \"partition_key\": [\"snapshot_dt_ist\"],\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"Automated process for reading KML file\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "f2585232-5a22-4079-9459-74456dc15570", "metadata": {}, "outputs": [], "source": ["pb.to_trino(dump_df, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "a34e84a3-cc57-4195-b209-a927515de232", "metadata": {}, "outputs": [], "source": ["store_lvl = f\"\"\"\n", "    select frontend_merchant_id , blinkit_store_name , runnr_city  , \n", "    sum(case when category = 'To_consider' then final_amount_to_consider end) as amount_to_be_paid,\n", "    count(distinct case when category = 'To_consider' then order_id end) as orders_to_consider\n", "    from logistics_data_etls.rider_extra_distance_payout_order_details a\n", "    join (Select max(updated_at_ist) updated_at_ist from logistics_data_etls.rider_extra_distance_payout_order_details\n", "    where snapshot_dt_ist >= current_date - interval '1' day) b on a.updated_at_ist = b.updated_at_ist\n", "    where snapshot_dt_ist >= current_date - interval '1' day\n", "    group by 1 ,2 , 3\n", "    order by 5 desc\n", "    limit 25\n", "\"\"\"\n", "store_lvl_df = pd.read_sql_query(store_lvl, con=pb.get_connection(\"[Warehouse] Trino\"))\n", "store_lvl_df"]}, {"cell_type": "code", "execution_count": null, "id": "75373b5d-99d4-49a7-8391-f584bb4ae5bf", "metadata": {}, "outputs": [], "source": ["store_lvl_df = store_lvl_df[\n", "    [\n", "        \"frontend_merchant_id\",\n", "        \"blinkit_store_name\",\n", "        \"runnr_city\",\n", "        \"amount_to_be_paid\",\n", "        \"orders_to_consider\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "3820e2e6-7a7b-452e-9d16-1ede883e6dc4", "metadata": {}, "outputs": [], "source": ["store_lvl_df[\"amount_to_be_paid\"] = store_lvl_df[\"amount_to_be_paid\"].round(2)"]}, {"cell_type": "code", "execution_count": null, "id": "6f303286-b2fb-49e6-b9ff-88a8cafddda3", "metadata": {}, "outputs": [], "source": ["store_lvl_df = store_lvl_df.rename(\n", "    columns={\n", "        \"frontend_merchant_id\": \"Merchant ID\",\n", "        \"blinkit_store_name\": \"Merchant Name\",\n", "        \"runnr_city\": \"City\",\n", "        \"amount_to_be_paid\": \"Amount to be paid\",\n", "        \"orders_to_consider\": \"Orders to consider\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b1285a93-7ee7-4a24-bc91-e4cf243e8daa", "metadata": {}, "outputs": [], "source": ["# poc_sheet_id = \"1laoAQEjK3Rj7Gzn5p6ZmDdZpPOhH4zLutsYQg6DbyCw\"\n", "# poc_sheet_name = \"Final City-Slack Mapping\"\n", "\n", "# poc_list = pb.from_sheets(poc_sheet_id, poc_sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "fe7f1240-70a8-41be-bdf0-9a49ab85d7d5", "metadata": {}, "outputs": [], "source": ["# poc_list = pd.read_csv(\"Scaleup Trigger - Tagging List - Final City-Slack Mapping.csv\")\n", "\n", "# poc_list.info()"]}, {"cell_type": "code", "execution_count": null, "id": "52e7f1da-c09a-4b9e-8574-15273762b119", "metadata": {}, "outputs": [], "source": ["# slack_tagging_df = poc_list.merge(\n", "#     store_lvl[[\"City\"]].drop_duplicates().reset_index(drop=True), how=\"inner\", on=\"City\"\n", "# )\n", "\n", "# slack_tagging_df = slack_tagging_df.sort_values(by=\"City\", ascending=True)\n", "\n", "# slack_tagging_df"]}, {"cell_type": "code", "execution_count": null, "id": "94d3e7c4-3df4-4104-856d-524c900f2b5a", "metadata": {}, "outputs": [], "source": ["# slack_tagging_df = (\n", "#     slack_tagging_df[[\"POC Email\", \"Slack ID\", \"slack_id_str\"]]\n", "#     .drop_duplicates()\n", "#     .reset_index(drop=True)\n", "# )\n", "\n", "# slack_tagging_df"]}, {"cell_type": "code", "execution_count": null, "id": "4c0cad33-42cb-404e-966b-7e57cd76df38", "metadata": {}, "outputs": [], "source": ["tagging_str = \"\"\n", "# tagging_str = \"<@U06JYRZ5VSL>\"\n", "\n", "# for poc_iter in range(len(slack_tagging_df)):\n", "\n", "#     str_i = slack_tagging_df[\"slack_id_str\"][poc_iter]\n", "\n", "#     if poc_iter != (len(slack_tagging_df) - 1):\n", "\n", "#         str_i = str_i + \" \"\n", "\n", "#     tagging_str = tagging_str + str_i\n", "\n", "# tagging_str"]}, {"cell_type": "code", "execution_count": null, "id": "51806e14-0c58-40f8-ac5a-960177f67c15", "metadata": {}, "outputs": [], "source": ["adhoc_poc_tagging = \"<@U01GJS155ML>\"\n", "\n", "tagging_str = tagging_str + \" \" + adhoc_poc_tagging\n", "\n", "tagging_str"]}, {"cell_type": "code", "execution_count": null, "id": "b3ffb547-554e-47f5-bd19-f5d373a9a118", "metadata": {}, "outputs": [], "source": ["# Function to render the table with conditional formatting\n", "def render_mpl_table(\n", "    data,\n", "    col_width=3.0,\n", "    row_height=0.625,\n", "    font_size=12,\n", "    header_color=\"#F7D046\",\n", "    row_colors=[\"#f5f5f5\", \"w\"],\n", "    edge_color=\"grey\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    cell_loc=\"center\",\n", "    conditional_formatting=None,\n", "    **kwargs\n", "):\n", "    fig, ax = plt.subplots(\n", "        figsize=(\n", "            (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        )\n", "    )\n", "    ax.axis(\"off\")\n", "\n", "    # Create the table\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, cellLoc=cell_loc, **kwargs\n", "    )\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    # Set column width\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        row, col = k\n", "        cell.set_edgecolor(edge_color)\n", "\n", "        # Header row\n", "        if row == 0:\n", "            cell.set_text_props(weight=\"bold\", color=\"#1B5E20\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[row % len(row_colors)])\n", "\n", "            # Make the first column bold\n", "            if col == 0:\n", "                cell.set_text_props(weight=\"bold\")\n", "\n", "            # Apply conditional formatting if provided\n", "            if conditional_formatting and data.columns[col] in conditional_formatting:\n", "                formatting_func = conditional_formatting[data.columns[col]]\n", "                cell_val = data.iloc[row - 1, col]\n", "                cell.set_facecolor(formatting_func(cell_val))\n", "\n", "    return fig, ax\n", "\n", "\n", "if len(store_lvl) > 0:\n", "\n", "    fig, ax = render_mpl_table(store_lvl_df)\n", "    fig.savefig(\"store_summary_extra_distance_payout.png\")\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "4b5ca788-743e-4050-b74b-7b87a7b69512", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, time\n", "\n", "today_noon = datetime.combine(datetime.today(), time(14, 0))\n", "max_updated_at = f\"\"\"\n", "    Select max(updated_at_ist) as time from logistics_data_etls.rider_extra_distance_payout_order_details\n", "    where snapshot_dt_ist >= current_date - interval '1' day\n", "\"\"\"\n", "max_updated_at = pd.read_sql_query(max_updated_at, con=pb.get_connection(\"[Warehouse] Trino\"))\n", "max_updated_at"]}, {"cell_type": "code", "execution_count": null, "id": "c2b3d02c-995c-49e8-9ed1-164fd9c1586c", "metadata": {}, "outputs": [], "source": ["order_to_pay_df = f\"\"\"\n", "    select *\n", "    from logistics_data_etls.rider_extra_distance_payout_order_details a\n", "    join (Select max(updated_at_ist) updated_at_ist from logistics_data_etls.rider_extra_distance_payout_order_details\n", "    where snapshot_dt_ist >= current_date - interval '1' day) b on a.updated_at_ist = b.updated_at_ist\n", "    where snapshot_dt_ist >= current_date - interval '1' day\n", "    and category <> 'Exempted'\n", "\"\"\"\n", "order_to_pay_df = pd.read_sql_query(order_to_pay_df, con=pb.get_connection(\"[Warehouse] Trino\"))\n", "order_to_pay_df"]}, {"cell_type": "code", "execution_count": null, "id": "63ac2b4f-6016-4d17-a6e1-411fa902d620", "metadata": {}, "outputs": [], "source": ["exempted_orders_df = f\"\"\"\n", "    select *\n", "    from logistics_data_etls.rider_extra_distance_payout_order_details a\n", "    join (Select max(updated_at_ist) updated_at_ist from logistics_data_etls.rider_extra_distance_payout_order_details\n", "    where snapshot_dt_ist >= current_date - interval '1' day) b on a.updated_at_ist = b.updated_at_ist\n", "    where snapshot_dt_ist >= current_date - interval '1' day\n", "    and category = 'Exempted'\n", "\"\"\"\n", "exempted_orders_df = pd.read_sql_query(\n", "    exempted_orders_df, con=pb.get_connection(\"[Warehouse] Trino\")\n", ")\n", "exempted_orders_df"]}, {"cell_type": "code", "execution_count": null, "id": "b4324797-eb42-417e-92ff-8237171ed266", "metadata": {}, "outputs": [], "source": ["max_updated_at = pd.to_datetime(max_updated_at[\"time\"])"]}, {"cell_type": "code", "execution_count": null, "id": "18a745bd-f515-485f-bff9-eeca6cf1e7b0", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(order_to_pay_df, \"1XkTJZlLIWn3y3l_PWNzvXnUoDZ2U2NAGxfB1_sPOJis\", \"orders_to_consider\")\n", "pb.to_sheets(exempted_orders_df, \"1XkTJZlLIWn3y3l_PWNzvXnUoDZ2U2NAGxfB1_sPOJis\", \"exempted_orders\")"]}, {"cell_type": "code", "execution_count": null, "id": "c11c0ca3-e30b-4bd5-b061-06ad82d5a54b", "metadata": {}, "outputs": [], "source": ["channels = [\"bl_quality_fraud_alert_report\"]\n", "# channels = [\"alert_test_maninder\"]\n", "\n", "# Get today's date and yesterday's date\n", "today = datetime.today().strftime(\"%Y-%m-%d\")\n", "yesterday = (datetime.today() - <PERSON><PERSON>ta(days=1)).strftime(\"%Y-%m-%d\")\n", "\n", "if max_updated_at.max() >= today_noon and len(store_lvl) > 0:\n", "\n", "    text = f\"\"\"*Extra distance pay Report - {yesterday}* \\n\"\"\"\n", "    text += f\"\"\"Please find the summary of the extra distance pay - polygon wise :\\n\"\"\"\n", "    text += f\"\"\"Order wise and DP wise Raw data also attached below. Please check and let me know in case of any discrepancy.\n", "             <https://docs.google.com/spreadsheets/d/1XkTJZlLIWn3y3l_PWNzvXnUoDZ2U2NAGxfB1_sPOJis/edit?gid=1996699521#gid=1996699521|Extra distance Pay - {yesterday}>\\n\"\"\"\n", "\n", "    text += f\"\"\"\\n*Top 25 Stores List for Extra-Distance Payout* \\n\\nFYI: {tagging_str} \\nCC: <@U06JYRZ5VSL>\\n\\n\"\"\"\n", "\n", "    # Send Slack message with all files\n", "    for channel in channels:\n", "        pb.send_slack_message(channel, text, files=[\"store_summary_extra_distance_payout.png\"])\n", "\n", "    print(\"<PERSON><PERSON> sent successfully!\")\n", "else:\n", "    print(\"Conditions not met, no alert sent.\")"]}, {"cell_type": "code", "execution_count": null, "id": "ea0276db-8edf-4e95-b828-b3e3294f43bd", "metadata": {}, "outputs": [], "source": ["# Clean up the files after sending\n", "# os.remove(order_to_pay_csv)\n", "# os.remove(exempted_orders_csv)\n", "os.remove(\"store_summary_extra_distance_payout.png\")  # Remove the PNG file"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}