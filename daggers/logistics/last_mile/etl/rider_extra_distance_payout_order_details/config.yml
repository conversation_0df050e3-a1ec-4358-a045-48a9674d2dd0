alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
  - channel: bl_quality_fraud_alert_report
dag_name: rider_extra_distance_payout_order_details
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: logistics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U06JYRZ5VSL
path: logistics/last_mile/etl/rider_extra_distance_payout_order_details
paused: false
pool: logistics_pool
project_name: last_mile
schedule:
  end_date: '2025-09-10T00:00:00'
  interval: 05 6,9 * * *
  start_date: '2025-06-13T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 4
