alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: blinkit_rider_ob_funnel
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: logistics
notebook:
  parameters: null
  priority_weight: 3
  retries: 2
owner:
  email: <EMAIL>
  slack_id: U087G28D7L4
path: logistics/last_mile/etl/blinkit_rider_ob_funnel
paused: false
pool: logistics_pool
project_name: last_mile
schedule:
  end_date: '2025-08-21T00:00:00'
  interval: 25 3 * * *
  start_date: '2025-06-01T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
