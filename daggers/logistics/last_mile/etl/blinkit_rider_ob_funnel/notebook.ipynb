{"cells": [{"cell_type": "code", "execution_count": null, "id": "2c2b310f-0ed0-4905-aacb-be687a0dda40", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import pencilbox as pb\n", "import numpy as np\n", "import itertools\n", "import datetime as dt\n", "from datetime import datetime\n", "from datetime import date\n", "from datetime import timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "0d1cf0df-8ec6-42f1-b6ff-f34acbb1512a", "metadata": {}, "outputs": [], "source": ["sql_query = f\"\"\"\n", "\n", "with vendor_list as (\n", "select \n", "creator_id, \n", "max_by(vendor_name,dt) as vendor_name\n", "from blinkit.logistics_data_etls.blinkit_runnr_buddy_vendors\n", "group by 1\n", "),\n", "\n", "lead_table as (\n", "select \n", "distinct\n", "a.lead_id,\n", "split_part(pk,'ld#1#',2) as pk,\n", "date(cast(from_unixtime(a.created_at, 'Asia/Kolkata') as timestamp)) as lead_dt,\n", "cast(from_unixtime(a.updated_at, 'Asia/Kolkata') as timestamp) as updated_at,\n", "a.lead_type,\n", "a.name,\n", "a.source ,\n", "a.source_id creator,\n", "a.status,\n", "cast(lead_info.driver_lead_info as json),\n", "cast(json_extract(cast(lead_info.driver_lead_info as json),'$.sub_source') as varchar) as sub_source,\n", "cast(json_extract(cast(lead_info.driver_lead_info as json),'$.carrier_id') as int) as carrier_id,\n", "cast(json_extract(cast(lead_info.driver_lead_info as json),'$.utm_source') as varchar) as utm_source,\n", "cast(json_extract(cast(lead_info.driver_lead_info as json),'$.utm_medium') as varchar) as utm_medium,\n", "cast(json_extract(cast(lead_info.driver_lead_info as json),'$.utm_campaign') as var<PERSON>r) as utm_campaign,\n", "cast(json_extract(cast(location_details as json),'$.city_name') as varchar) as city_name,\n", "cast(json_extract(cast(lead_info.driver_lead_info as json),'$.business') as varchar) as business,\n", "json_extract(cast(lead_info.driver_lead_info as json),'$.appsflyer_data') as appsflyer_data,\n", "-- cast(json_extract(cast(lead_info.driver_lead_info as json),'$.appsflyer_data'),'$.event') as varchar) as event,\n", "json_extract(cast(lead_info.driver_lead_info as json),'$.is_auto_converted') as is_auto_converted,\n", "cast(from_unixtime(cast(a.converted_date as int), 'Asia/Kolkata') as timestamp) as converted_date,\n", "    JSON_EXTRACT(CAST(lead_info.driver_lead_info AS JSON), '$.appsflyer_data.event') AS event,\n", "    \n", "CAST(\n", "    CAST(\n", "        JSON_EXTRACT(CAST(lead_info.driver_lead_info AS JSON), '$.appsflyer_data.app_install_timestamp') \n", "        AS varchar\n", "    ) AS TIMESTAMP\n", ") \n", "+ INTERVAL '5' HOUR + INTERVAL '30' MINUTE AS app_install_timestamp,\n", "\n", "CAST(\n", "    CAST(\n", "        JSON_EXTRACT(CAST(lead_info.driver_lead_info AS JSON), '$.appsflyer_data.attribution_timestamp') \n", "        AS varchar\n", "    ) AS TIMESTAMP\n", ") \n", "+ INTERVAL '5' HOUR + INTERVAL '30' MINUTE AS attribution_timestamp,\n", "    \n", "    JSON_EXTRACT(CAST(lead_info.driver_lead_info AS JSON), '$.appsflyer_data.media_source') AS media_source,\n", "    JSON_EXTRACT(CAST(lead_info.driver_lead_info AS JSON), '$.appsflyer_data.campaign') AS campaign,\n", "    JSON_EXTRACT(CAST(lead_info.driver_lead_info AS JSON), '$.appsflyer_data.af_channel') AS af_channel,\n", "    JSON_EXTRACT(CAST(lead_info.driver_lead_info AS JSON), '$.appsflyer_data.af_site_id') AS af_site_id,\n", "origin,    \n", "converted_id driver_id,\n", "null as source_channel,\n", "a.phone_number,\n", "onboarding_id ob_id,\n", "row_number() over (partition by a.phone_number order by a.created_at desc) rnk\n", "from \n", "zomato.dynamodb.prod_lead_management_service a\n", "left join (\n", "    select \n", "    distinct city_name \n", "    from logistics_data_etls.blinkit_driver_ob_v1\n", "    where b_tag_date >= current_date - interval '14' day\n", "    ) r on r.city_name = cast(json_extract(cast(a.location_details as json),'$.city_name') as varchar)\n", "where \n", "dt >= date_format(current_date - interval '40' day,'%%Y%%m%%d') \n", "and client_id = '1'\n", "and pk like 'ld#%%'\n", ")\n", "\n", "-- signup_base as (\n", "select \n", "cast(cast(substring(replace(mo.created_at,'T',' '),1,19) as timestamp) + interval '330' minute as date) as signup_date,\n", "mo.ob_id, \n", "mo.driver_id, \n", "mo.delivery_driver_id, \n", "business_details.work_hour_id as work_hour_id,\n", "mo.onboarding_app_type as onboarding_app_type,\n", "l2.city_name as lead_city, \n", "\n", "\n", "case when  l2.source = ('DRIVER_LEAD_SOURCE_DRIVER_REFERRALS') then 'Blinkit Driver Referral'\n", "    \n", "    when  l2.source = ('DRIVER_LEAD_SOURCE_CSV_UPLOAD') then 'Blinkit Runnr Buddy Referral'\n", "    when  l2.source = ('DRIVER_LEAD_SOURCE_RUNNR_BUDDY') then 'Blinkit Runnr Buddy Referral'\n", "    \n", "    when  l2.origin like '%%BLINKIT%%' and l2.source = ('DRIVER_LEAD_SOURCE_DIGITAL') then 'Digital'\n", "    when  l2.origin like '%%BLINKIT%%' and l2.source = ('DRIVER_LEAD_SOURCE_WHATSAPP') then 'Digital'\n", "    when  l2.origin like '%%BLINKIT%%' and l2.attribution_timestamp is not null and l2.app_install_timestamp is not null then 'Digital'\n", "    \n", "    when  l2.origin not like '%%BLINKIT%%' then 'Playstore_blinkit'\n", "    \n", "    when  l2.origin like '%%BLINKIT%%' and l2.source = ('DRIVER_LEAD_SOURCE_ORGANIC') then 'Playstore_blinkit'\n", "    when  l2.origin not like '%%BLINKIT%%' then 'Zomato Lead'\n", "else 'Others' end as sorted_source,\n", "\n", "mo.carrier.carrier_id as vehicle, \n", "mo.business_details.store_id as store_id, \n", "CAST(ROW(mo.pan_details._id, mo.pan_details.pan_no, mo.pan_details.name, mo.pan_details.image_url, mo.pan_details.verified, mo.pan_details.verification_status, mo.pan_details.pan_aadhaar_linked, mo.pan_details.pan_no_encrypted, mo.pan_details.duplicate_pan_iterations, mo.pan_details.pan_comprehensive_data) AS ROW(_id VARCHAR, pan_no VARCHAR, name VARCHAR, image_url VARCHAR, verified BOOLEAN, verification_status VARCHAR, pan_aadhaar_linked BOOLEAN, pan_no_encrypted VARCHAR, duplicate_pan_iterations ARRAY(VARCHAR), pan_comprehensive_data ARRAY(ROW(client_id VARCHAR, pan_number VARCHAR, full_name VA<PERSON>HA<PERSON>, masked_aadhaar VARCHAR, gender VARCHAR, dob VARCHAR, aadhaar_linked BOOLEAN, status VARCHAR, linked_aadhaar_ob_ids ARRAY(VARCHAR), address ROW(line_1 VARCHAR, line_2 VARCHAR, street_name VARCHA<PERSON>, zip VARCHAR, city VARCHAR, state VARCHAR, country VARCHAR, \"full\" VARCHAR), pan_number_encrypted VARCHAR)))) as pan_details,\n", "mo.bank_details,\n", "mo.profile_details.image_url as profile_image, \n", "mo.payment_details.security_amount as ob_fee,\n", "ob.lead_table_source,\n", "ob.driver_id as ob_driver_id, \n", "ob.qc_status, \n", "ob.bgv_reason, \n", "ob.b_tag_date as ob_date,\n", "ob.b_tag_date_ist as ob_ist,\n", "ob.qc_updated_at as qc_date, \n", "qc.updated_at as qc_ts,\n", "ob.bgv_date,\n", "ob.fod_date, \n", "ob.ob_week as ob_wk, \n", "ob.bgv_wk, \n", "ob.fod_wk, \n", "ob.last_updated_at,\n", "mo.onboarding_flow,\n", "mo.locality_details.city as City\n", "\n", "from zomato.mongo_driver_onboarding_service_production.mongo_v3_onboarding_applications mo\n", "left join blinkit.logistics_data_etls.blinkit_driver_ob_v1 ob on ob.driver_id = mo.driver_id and ob.source not in ('Food to grocery','Others')\n", "left join zomato.carthero_onboarding_prod.qc_details qc on qc.entity_id = ob.delivery_driver_id\n", "left join lead_table l2 on l2.ob_id = mo.ob_id and l2.rnk=1\n", "left join vendor_list v2 on (case when try_cast(v2.creator_id as int) is not null then v2.creator_id = l2.creator else v2.creator_id = l2.sub_source end)\n", "where mo.dt >= date_format(current_date - interval '4' day, '%%Y%%m%%d') \n", "and (onboarding_app_type = 'BLINKIT' or mo.business_details.store_id is not null)\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "4c55fd08-8efa-4ca4-a368-5003086cde2e", "metadata": {}, "outputs": [], "source": ["red_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "39620ba7-c696-4513-8de8-e932bab6daff", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"bl_rider_ob_funnel_v3\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"signup_date\", \"type\": \"date\", \"description\": \"signup_date\"},\n", "        {\"name\": \"ob_id\", \"type\": \"varchar\", \"description\": \"onboarding_id\"},\n", "        {\"name\": \"driver_id\", \"type\": \"varchar\", \"description\": \"driver_id\"},\n", "        {\"name\": \"delivery_driver_id\", \"type\": \"integer\", \"description\": \"delivery_driver_id\"},\n", "        {\"name\": \"work_hour_id\", \"type\": \"integer\", \"description\": \"work_hour_id\"},\n", "        {\"name\": \"onboarding_app_type\", \"type\": \"varchar\", \"description\": \"onboarding_app_type\"},\n", "        {\"name\": \"lead_city\", \"type\": \"varchar\", \"description\": \"lead_city\"},\n", "        {\"name\": \"sorted_source\", \"type\": \"varchar\", \"description\": \"sorted_source\"},\n", "        {\"name\": \"vehicle\", \"type\": \"varchar\", \"description\": \"vehicle\"},\n", "        {\"name\": \"store_id\", \"type\": \"bigint\", \"description\": \"store_id\"},\n", "        {\n", "            \"name\": \"pan_details\",\n", "            \"type\": 'row(_id varchar, pan_no varchar, name varchar, image_url varchar, verified boolean, verification_status varchar, pan_aadhaar_linked boolean, pan_no_encrypted varchar, duplicate_pan_iterations array(varchar), pan_comprehensive_data array(row(client_id varchar, pan_number varchar, full_name varchar, masked_aadhaar varchar, gender varchar, dob varchar, aadhaar_linked boolean, status varchar, linked_aadhaar_ob_ids array(varchar), address row(line_1 varchar, line_2 varchar, street_name varchar, zip varchar, city varchar, state varchar, country varchar, \"full\" varchar), pan_number_encrypted varchar)))',\n", "            \"description\": \"pan_details\",\n", "        },\n", "        {\n", "            \"name\": \"bank_details\",\n", "            \"type\": \"row(_id varchar, account_no varchar, bank_name varchar, ifsc_code varchar, verified boolean, image_url varchar, beneficiary_name varchar, account_no_encrypted varchar, cbs_status varchar)\",\n", "            \"description\": \"bank_details\",\n", "        },\n", "        {\"name\": \"profile_image\", \"type\": \"varchar\", \"description\": \"profile_image_url\"},\n", "        {\"name\": \"ob_fee\", \"type\": \"double\", \"description\": \"onboarding_fee\"},\n", "        {\"name\": \"lead_table_source\", \"type\": \"varchar\", \"description\": \"lead_table_source\"},\n", "        {\"name\": \"ob_driver_id\", \"type\": \"varchar\", \"description\": \"ob_driver_id\"},\n", "        {\"name\": \"qc_status\", \"type\": \"varchar\", \"description\": \"qc_status\"},\n", "        {\"name\": \"bgv_reason\", \"type\": \"varchar\", \"description\": \"bgv_reason\"},\n", "        {\"name\": \"ob_date\", \"type\": \"date\", \"description\": \"onboarding_date\"},\n", "        {\"name\": \"ob_ist\", \"type\": \"timestamp\", \"description\": \"onboarding_ist_time\"},\n", "        {\"name\": \"qc_date\", \"type\": \"date\", \"description\": \"qc_date\"},\n", "        {\"name\": \"qc_ts\", \"type\": \"timestamp\", \"description\": \"qc_timestamp\"},\n", "        {\"name\": \"bgv_date\", \"type\": \"date\", \"description\": \"bgv_date\"},\n", "        {\"name\": \"fod_date\", \"type\": \"date\", \"description\": \"fod_date\"},\n", "        {\"name\": \"ob_wk\", \"type\": \"integer\", \"description\": \"onboarding_week\"},\n", "        {\"name\": \"bgv_wk\", \"type\": \"integer\", \"description\": \"bgv_week\"},\n", "        {\"name\": \"fod_wk\", \"type\": \"integer\", \"description\": \"fod_week\"},\n", "        {\"name\": \"last_updated_at\", \"type\": \"timestamp\", \"description\": \"last_updated_at\"},\n", "        {\"name\": \"onboarding_flow\", \"type\": \"varchar\", \"description\": \"onboarding_flow\"},\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"city\"},\n", "    ],\n", "    \"primary_key\": [\"ob_id\", \"signup_date\"],\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"This Table contains detailed onboarding info for drivers from new app source\",\n", "}\n", "pb.to_trino(sql_query, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "acaa573e-32c5-45d8-94ab-33fd3c2988b1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}