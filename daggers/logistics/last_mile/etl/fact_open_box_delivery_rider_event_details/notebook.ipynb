{"cells": [{"cell_type": "code", "execution_count": null, "id": "f45cd7ee-d12c-493f-9493-0aaaf48b0e6d", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "from datetime import datetime, timedelta\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "id": "ee58150d-420a-4c19-b8cf-6e0eadf9d8a9", "metadata": {}, "outputs": [], "source": ["conn = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "cd87315a-08e8-4431-be51-c3af9a08c172", "metadata": {}, "outputs": [], "source": ["# Set your start and end dates\n", "start_date = datetime.now().date() - <PERSON><PERSON><PERSON>(days=1)\n", "end_date = datetime.now().date()  # or use your own fixed end date\n", "\n", "# Initialize current date\n", "curr_date = start_date"]}, {"cell_type": "code", "execution_count": null, "id": "fff3ae6e-b84e-48d3-8bc6-30b22a4b882f", "metadata": {}, "outputs": [], "source": ["while curr_date <= end_date:\n", "    dt_str = curr_date.strftime(\"%Y%m%d\")  # for Trino partition\n", "    print(f\"Running for: {dt_str}\")\n", "\n", "    query = f\"\"\"\n", "    WITH obd_order_base AS (\n", "        SELECT * FROM (\n", "            SELECT\n", "                d1.dt AS date,\n", "                REGEXP_REPLACE(d2.o_external_order_id, '^XL-', '') AS o_external_order_id,\n", "                d1.od_additional_info,\n", "                d1.od_additional_info.open_box_delivery AS open_box_delivery_flag,\n", "                d2.od_additional_info.open_box_delivery AS open_box_delivery,\n", "                d1.od_additional_info.picklists AS Picklist\n", "            FROM zomato.dynamodb.prod_order_fulfillment_service d1\n", "            JOIN zomato.dynamodb.prod_order_fulfillment_service d2\n", "              ON d1.id = d2.id AND d2.sort_key = 'order'\n", "            WHERE d1.dt = '{dt_str}' AND d2.dt = '{dt_str}'\n", "              AND d1.sort_key = 'details'\n", "            ORDER BY d1.dt\n", "        ) WHERE open_box_delivery_flag = true\n", "    ),\n", "\n", "    working_base AS (\n", "        SELECT \n", "            date_format(from_unixtime(time), '%%Y-%%m-%%d %%H:%%i:%%s') AS Timestamps,\n", "            element_at(properties, 'order_ids') AS order_id,\n", "            event_name,\n", "            event_value,\n", "            driver_id,\n", "            properties,\n", "            dt\n", "        FROM zomato.jumbo2.raw_driver_events\n", "        WHERE dt = '{dt_str}'\n", "          AND event_name = 'page_opened'\n", "          AND element_at(properties, 'sheet_id') = 'OPEN_BOX_DELIVERY'\n", "          AND element_at(properties, 'screen') = 'universal_bottom_sheet'\n", "          AND element_at(properties, 'order_ids') IN (SELECT o_external_order_id FROM obd_order_base)\n", "\n", "        UNION\n", "\n", "        SELECT \n", "            date_format(from_unixtime(time), '%%Y-%%m-%%d %%H:%%i:%%s') AS Timestamps,\n", "            element_at(properties, 'order_ids') AS order_id,\n", "            event_name,\n", "            event_value,\n", "            driver_id,\n", "            properties,\n", "            dt\n", "        FROM zomato.jumbo2.raw_driver_events\n", "        WHERE dt = '{dt_str}'\n", "          AND event_name IN (\n", "              'aerobar_component_resolver', 'screen_opened', 'qr_scanned', 'valid_qr',\n", "              'invalid_qr_scanned', 'primary_button_clicked', 'file_uploader',\n", "              'negative_button_clicked', 'toolbar_component_resolver',\n", "              'support_issue_selected', 'acknowledge_attachments_upload',\n", "              'checkbox_item_toggle', 'universal_bottom_sheet_shown', 'positive_button_clicked'\n", "          )\n", "          AND element_at(properties, 'order_ids') IN (SELECT o_external_order_id FROM obd_order_base)\n", "    )\n", "\n", "    SELECT \n", "        cast(DATE_FORMAT(DATE_PARSE(dt, '%%Y%%m%%d'), '%%Y-%%m-%%d') AS date) AS snapshot_dt_ist,\n", "        Timestamps AS snapshot_ts_ist,\n", "        order_id,\n", "        CONCAT('FE', CAST(CAST(driver_id AS int) + 10000 AS varchar)) AS FE_ID,\n", "        event_name,\n", "        event_value,\n", "        properties\n", "    FROM working_base\n", "    \"\"\"\n", "\n", "    try:\n", "        df = pd.read_sql(query, conn)\n", "        if df.empty:\n", "            print(f\"No data for {dt_str}\")\n", "        else:\n", "            df[\"properties\"] = df[\"properties\"].apply(json.dumps)\n", "            df[\"snapshot_dt_ist\"] = pd.to_datetime(df[\"snapshot_dt_ist\"]).dt.date\n", "            df[\"snapshot_ts_ist\"] = pd.to_datetime(df[\"snapshot_ts_ist\"])\n", "\n", "            kwargs_trino = {\n", "                \"schema_name\": \"logistics_data_etls\",\n", "                \"table_name\": \"fact_open_box_delivery_rider_event_details\",\n", "                \"column_dtypes\": [\n", "                    {\"name\": \"snapshot_dt_ist\", \"type\": \"date\", \"description\": \"Snapshot dt ist\"},\n", "                    {\n", "                        \"name\": \"snapshot_ts_ist\",\n", "                        \"type\": \"timestamp(6)\",\n", "                        \"description\": \"Snapshot ts ist\",\n", "                    },\n", "                    {\"name\": \"order_id\", \"type\": \"varchar\", \"description\": \"Order id\"},\n", "                    {\"name\": \"FE_ID\", \"type\": \"varchar\", \"description\": \"Fe id\"},\n", "                    {\"name\": \"event_name\", \"type\": \"varchar\", \"description\": \"Event name\"},\n", "                    {\"name\": \"event_value\", \"type\": \"varchar\", \"description\": \"Event value\"},\n", "                    {\"name\": \"properties\", \"type\": \"varchar\", \"description\": \"Properties\"},\n", "                ],\n", "                \"primary_key\": [\"order_id\"],\n", "                \"partition_key\": [\"snapshot_dt_ist\"],\n", "                \"load_type\": \"upsert\",\n", "                \"table_description\": \"This Table has data for open box delivery orders.\",\n", "                \"force_upsert_without_increment_check\": True,\n", "            }\n", "\n", "            pb.to_trino(df, **kwargs_trino)\n", "            print(f\"✅ Data written for {dt_str}\")\n", "    except Exception as e:\n", "        print(f\"❌ Error on {dt_str}: {e}\")\n", "\n", "    curr_date += <PERSON><PERSON><PERSON>(days=1)"]}, {"cell_type": "code", "execution_count": null, "id": "9b7482b3-e2fb-4688-b0f7-4b25db0cefc6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d32272f5-cf9e-4824-a7ae-ee3c07a1e491", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b6cc3e31-5300-417b-84f1-42cc04fe2a47", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "25bdf683-bdf5-43d2-aa3a-2b004213bbef", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d6e99626-6671-43c8-9388-15934143d31d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cef9c170-0c16-41b5-b7ff-ba26f9a87d15", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}