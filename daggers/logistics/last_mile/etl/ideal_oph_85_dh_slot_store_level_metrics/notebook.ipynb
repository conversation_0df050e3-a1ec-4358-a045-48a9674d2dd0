{"cells": [{"cell_type": "code", "execution_count": null, "id": "28dc98e2-521c-4816-abff-7d443401f66f", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "id": "a46ac928-4d44-48d3-828d-e3bd4f6afd1d", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import math\n", "from datetime import datetime, timedelta, date\n", "\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "conn = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "a6018554-2818-40b1-8c17-db8ee7d625e7", "metadata": {}, "outputs": [], "source": ["!pip install pandasql"]}, {"cell_type": "code", "execution_count": null, "id": "4dd3c218-d483-4a94-a20e-6fd450286f60", "metadata": {}, "outputs": [], "source": ["sql_6_hour_1 = \"\"\"\n", "\n", "with mapping as (\n", "select distinct z_store_id, blinkit_store_id, store_name\n", "from (\n", "    select distinct id as z_store_id, external_id as blinkit_store_id, name as store_name,\n", "    rank() over (partition by id order by updated_at desc) as rnk\n", "    from zomato.carthero_prod.users where type = 'merchant' and source_id = 17110 \n", "    and (created_at) >= cast(date('2019-05-01') as timestamp)\n", ") where rnk=1),\n", "\n", "lost_orders_base as (\n", "select \n", "ss.*, m.z_store_id store_id \n", "from zomato.jumbo_external.surge_segg ss\n", "inner join mapping m on cast(ss.merchant_id as varchar) = m.blinkit_store_id\n", "where date(at_date_ist) >= (current_date - interval '1' day)-INTERVAL '60' day),\n", "\n", "rain_base as (\n", "select\n", "account_date,\n", "extract(hour from accounting_time) as hour,\n", "accounting_time,\n", "trip_id,\n", "surge_pay,\n", "frontend_merchant_id,\n", "lead(accounting_time) over(partition by  frontend_merchant_id order by accounting_time) as lead_ac,\n", "lead(surge_pay) over(partition by  frontend_merchant_id order by accounting_time) as surge_pay_2\n", "from logistics_data_etls.delivery_trip_payout p\n", "left join ( \n", "    select \n", "    distinct \n", "    trip_id as tid,\n", "    frontend_merchant_id \n", "    from dwh.fact_supply_chain_order_details \n", "    where order_checkout_dt_ist >= date(current_date - interval '1' day)-INTERVAL '60' day) x\n", "on cast(p.trip_id as varchar) = x.tid\n", "where dt >= date_format(date(current_date - interval '1' day)-INTERVAL '60' day, '%%Y%%m%%d')),\n", "\n", "log_base3 as (\n", "select * from (\n", "select\n", "account_date,\n", "hour,\n", "frontend_merchant_id,\n", "sm.store_id,\n", "sum(case when surge_pay>0 and surge_pay_2>0 then date_diff('second',accounting_time,lead_ac) end )*1.00/60 as rain_mins\n", "from rain_base b\n", "left join logistics_data_etls.blinkit_store_mapping sm on b.frontend_merchant_id = sm.blinkit_store_id\n", "group by 1,2,3,4)\n", "where rain_mins >= 15),\n", "\n", "-- logs_base3 as (\n", "-- select\n", "-- account_date date_, z_store_id store_id,hour hour_\n", "-- from (\n", "--     select ss.blinkit_store_id,account_date,hour,rain_time_in_mins,z_store_id\n", "--     FROM zomato.blinkit_etls.b_hourly_rain_time ss\n", "--     INNER JOIN mapping sd on sd.blinkit_store_id=ss.blinkit_store_id\n", "--     WHERE dt>=date_format(current_date - interval '60' day, '%%Y%%m%%d')\n", "--     GROUP BY 1,2,3,4,5\n", "-- ) WHERE rain_time_in_mins<=15\n", "-- GROUP BY 1,2,3),\n", "\n", "b2a_base as (\n", "select \n", "date_,hour_,z_store_id,b2a_orders,order_count,b2a_orders*1.00/order_count b2a_perc\n", "FROM (\n", "    SELECT date(order_checkout_ts_ist) date_,extract(hour from order_checkout_ts_ist) hour_,z_store_id,\n", "    count(distinct case when date_diff('Second',order_billing_completed_ts_ist,order_partner_assigned_ts_ist)*1.00/60>0 then order_id else NULL end) as b2a_orders,\n", "    count(distinct order_id) order_count\n", "    FROM dwh.fact_supply_chain_order_details fsc\n", "    LEFT JOIN mapping m ON cast(fsc.frontend_merchant_id as varchar)=m.blinkit_store_id\n", "    WHERE date(order_checkout_dt_ist) between ((current_date - interval '1' day)-interval '60' day) AND ((current_date )-interval '1' day)\n", "    AND order_current_status='DELIVERED'\n", "    GROUP BY 1,2,3\n", ")),\n", "\n", "working_hours as (\n", "select  \n", "account_date as date_,\n", "extract(hour from hr) hour_,\n", "delivery_driver_id,\n", "-- sum(logins_min_calculated)*1.0000 as login\n", "CAST(sum(logins_min_calculated) * 1.0000 / 60 AS DECIMAL(10, 4)) as login_hr\n", "-- ROUND(sum(logins_min_calculated*1.0000) * 1.0000 / 60, 4)\n", "from logistics_data_etls.hourly_dp_wasted_login_v1\n", "where account_date >= (date(current_date - interval '1' day) - INTERVAL '60' day)\n", "GROUP BY 1,2,3\n", "),\n", "\n", "orders1 as (\n", "select \n", "distinct delivery_driver_id, --trip_id,  \n", "o.pickup_user_id, date(accounting_time+interval '330' minute) as account_date, \n", "hour(accounting_time+interval '330' minute) as account_hour,\n", "count(case when leg_status='COMPLETE' and lower(report_type)='drop' then ar.id else null end) as orders \n", "from zomato.accounting_production.accounting_reports ar\n", "left join zomato.carthero_prod.shipment_legs sl on cast(sl.id as varchar) = coalesce(cast(ar.shipment_leg_id as varchar),ar.logistics_order_id)\n", "left join zomato.carthero_prod.orders o on o.id=sl.order_id\n", "INNER JOIN mapping m on o.pickup_user_id=m.z_store_id \n", "where ar.dt>=date_format(date(current_date - interval '1' day) - INTERVAL '60' day, '%%Y%%m%%d')\n", "and sl.dt>=date_format(date(current_date - interval '1' day) - INTERVAL '60' day, '%%Y%%m%%d')\n", "and o.dt>=date_format(date(current_date - interval '1' day) - INTERVAL '60' day, '%%Y%%m%%d')\n", "and merchant_category='BLINKIT'\n", "group by 1,2,3,4),\n", "\n", "orders2 as (\n", "select distinct delivery_driver_id, --trip_id , \n", "o.pickup_user_id, date(accounting_time+interval '330' minute) as account_date, \n", "hour(accounting_time+interval '330' minute) as account_hour, \n", "count(case when leg_status='COMPLETE' and lower(touch_point_type)='drop' then tp.id else null end) as orders \n", "from zomato.accounting_production.driver_touch_point_reports tp\n", "left join zomato.carthero_prod.shipment_legs sl on cast(sl.id as varchar)=coalesce(cast(tp.shipment_leg_id as varchar),tp.logistics_order_id)\n", "left join zomato.carthero_prod.orders o on o.id=sl.order_id\n", "INNER JOIN mapping m on o.pickup_user_id=m.z_store_id \n", "--left join carthero_prod.orders o on sl.order_id=o.id\n", "where tp.dt>=date_format(date(current_date - interval '1' day) - INTERVAL '60' day, '%%Y%%m%%d')\n", "and sl.dt>=date_format(date(current_date - interval '1' day) - INTERVAL '60' day, '%%Y%%m%%d')\n", "and o.dt>=date_format(date(current_date - interval '1' day) - INTERVAL '60' day, '%%Y%%m%%d')\n", "and merchant_category='BLINKIT'\n", "group by 1,2,3,4),\n", "\n", "hourly_orders_base as(\n", "select * from orders1 \n", "union\n", "select * from orders2),\n", "\n", "hourly_orders as (\n", "select delivery_driver_id, pickup_user_id,\n", "account_date,\n", "account_hour,\n", "sum(orders) orders\n", "from\n", "hourly_orders_base\n", "group by 1,2,3,4\n", "),\n", "\n", "login_base_x as (\n", "select \n", "distinct\n", "date_, \n", "hour_,\n", "l.delivery_driver_id,\n", "sum(login_hr) as login_hr,\n", "max_by(z_store_id,account_date) as z_store_id\n", "from working_hours l\n", "left join logistics_data_etls.blinkit_rider_daily_accounting_summary_v1 ho on ho.account_date = l.date_ \n", "     and ho.delivery_driver_id = l.delivery_driver_id and ho.account_date between date(current_date - interval '1' day)-interval '60' day AND date(current_date - interval '1' day)-interval '1' day\n", "where date_ between date(current_date - interval '1' day)-interval '60' day AND date(current_date - interval '1' day)-interval '1' day\n", "group by 1,2,3),\n", "\n", "base_pre as (\n", "SELECT \n", "wh.date_ as account_date,\n", "wh.hour_ as account_hour,\n", "wh.z_store_id as pickup_user_id,\n", "wh.delivery_driver_id,\n", "sum(ho.orders) orders,\n", "sum(login_hr) login_hr\n", "FROM login_base_x wh \n", "LEFT JOIN hourly_orders ho ON ho.account_date=wh.date_ AND ho.account_hour=wh.hour_ AND ho.delivery_driver_id = wh.delivery_driver_id\n", "GROUP BY 1,2,3,4)\n", "\n", "\n", "\n", "\n", ",\n", "\n", "slots as (\n", "select \n", "store_id,\n", "hour,\n", "max_by(slot_id,account_date) as slot_id,\n", "max_by(slot_name,account_date) as slot_name\n", "from logistics_data_etls.b_gigs_design_ver3\n", "where dt > '********'\n", "group by 1,2),\n", "\n", "base_pre_login as (\n", "select \n", "-- account_hour,    \n", "\n", "case when account_hour between 1 and 6 then 'a Late Night + Early Morning'\n", "     when account_hour between 7 and 12 then 'b Morning'\n", "     when account_hour between 13 and 18 then 'c Afternoon + Evening'\n", "     when account_hour between 19 and 23 then 'd Night'\n", "     when account_hour = 0 then 'd Night'\n", "else null end as slot,\n", "\n", "pickup_user_id,\n", "\n", "sum(login_hr) login_hr,\n", "sum(orders) orders\n", "from base_pre \n", "where account_date between (current_date - interval '1' day)-interval '7' day AND (current_date - interval '1' day)-interval '1' day\n", "GROUP BY 1,2),\n", "\n", "base as (\n", "SELECT \n", "fsc.date_,\n", "extract(dow from fsc.date_) dow,\n", "case when extract(dow from fsc.date_) between 1 AND 5 then 'WEEKDAY' ELSE 'WEEKEND' END AS dow1,\n", "\n", "case when fsc.hour_ between 1 and 6 then 'a Late Night + Early Morning'\n", "     when fsc.hour_ between 7 and 12 then 'b Morning'\n", "     when fsc.hour_ between 13 and 18 then 'c Afternoon + Evening'\n", "     when fsc.hour_ between 19 and 23 then 'd Night'\n", "     when fsc.hour_ = 0 then 'd Night'\n", "else null end as slot,\n", "\n", "-- s.slot_id,\n", "-- s.slot_name,\n", "fsc.pickup_user_id store_id,\n", "sum(fsc.order_count) order_count,\n", "sum(fsc.total_working_hour) total_working_hour,\n", "try(sum(fsc.order_count)*1.0000/sum(fsc.total_working_hour)) hour_util,\n", "sum(fsc.rider_count) partner_count,\n", "COALESCE(sum(b2a_orders),0) b2a_orders,\n", "try(COALESCE(sum(b2a_orders),0)*100.00/sum(bb.order_count)) b2a_orders_perc\n", "\n", "FROM (\n", "    SELECT account_date date_,account_hour hour_,pickup_user_id pickup_user_id,\n", "    sum(orders) order_count,\n", "    sum(login_hr) total_working_hour,count(distinct delivery_driver_id) rider_count FROM base_pre\n", "    GROUP BY 1,2,3\n", ") fsc\n", "LEFT JOIN b2a_base bb on fsc.date_=bb.date_ AND fsc.hour_=bb.hour_ AND fsc.pickup_user_id=bb.z_store_id \n", "left join log_base3 lb3 ON lb3.account_date = fsc.date_ AND lb3.store_id = fsc.pickup_user_id AND lb3.hour = fsc.hour_\n", "-- left join slots s on s.hour = fsc.hour_  and s.store_id = fsc.pickup_user_id\n", "Where fsc.date_ >= (date(current_date - interval '1' day) - interval '60' day)\n", "and lb3.hour is null\n", "-- and fsc.hour_ > 5\n", "GROUP BY 1,2,3,4,5\n", "-- having sum(fsc.order_count)>0\n", ")\n", "\n", "\n", ",\n", "\n", "theo_OPH as (\n", "SELECT \n", "frontend_merchant_id,\n", "\n", "case when a.hour_ between 1 and 6 then 'a Late Night + Early Morning'\n", "     when a.hour_ between 7 and 12 then 'b Morning'\n", "     when a.hour_ between 13 and 18 then 'c Afternoon + Evening'\n", "     when a.hour_ between 19 and 23 then 'd Night'\n", "     when a.hour_ = 0 then 'd Night'\n", "else null end as slot,\n", "\n", "\n", "-- slot_id,\n", "-- slot_name,\n", "z_store_id,\n", "SUM(enroute_time)*1.00/sum(order_count) avg_enroute_hr,\n", "sum(order_count)*0.50/sum(enroute_time) theo_rider_OPH\n", "FROM (\n", "    SELECT frontend_merchant_id,extract(hour from order_checkout_ts_ist) hour_,z_store_id,\n", "    case when extract(dow from order_checkout_ts_ist) between 1 AND 5 then 'WEEKDAY' ELSE 'WEEKEND' END as dow,\n", "    count(distinct order_id) order_count,\n", "    sum(date_diff('second',order_enroute_ts_ist,order_delivered_ts_ist))*1.00/3600 enroute_time\n", "    FROM dwh.fact_supply_chain_order_details oh\n", "    INNER JOIN mapping m on cast(oh.frontend_merchant_id as varchar)=m.blinkit_store_id\n", "    WHERE (order_current_status)='DELIVERED'\n", "    AND order_checkout_dt_ist>(current_date - interval '1' day)-interval '60' day\n", "    AND (order_checkout_ts_ist)>=cast((current_date - interval '1' day)-interval '60' day as timestamp)\n", "    GROUP BY 1,2,3,4\n", ") a\n", "-- left join slots s on s.hour = a.hour_ and s.store_id = a.z_store_id\n", "GROUP BY 1,2,3\n", "Having sum(order_count)>0 AND sum(enroute_time)>0),\n", "\n", "DS95 as (WITH ranked_data AS (\n", "    SELECT \n", "        date_,\n", "        slot,\n", "        store_id,\n", "        hour_util,\n", "        b2a_perc_bucket,\n", "        percent_rank() OVER(PARTITION BY slot,store_id,b2a_perc_bucket ORDER BY hour_util ASC) perc_rank,\n", "        ROW_NUMBER() OVER(PARTITION BY slot,store_id,b2a_perc_bucket ORDER BY hour_util ASC) as row_num,\n", "        COUNT(*) OVER(PARTITION BY slot,store_id,b2a_perc_bucket) as total_rows\n", "    FROM (\n", "        SELECT *,\n", "            case when b2a_orders_perc<=3 then '1 DS97'\n", "                 when b2a_orders_perc<=6 then '2 DS94'\n", "                 WHEN b2a_orders_perc>5 AND b2a_orders_perc<=10 then '3 DS90'\n", "                 WHEN b2a_orders_perc>10 AND b2a_orders_perc<=15 then '4 DS85'\n", "                 WHEN b2a_orders_perc>15 AND b2a_orders_perc<=20 then '5 DS80'\n", "                 WHEN b2a_orders_perc <=30 then '6 DS70'\n", "                 WHEN b2a_orders_perc <=40 then '7 DS60'\n", "             ELSE '8 DS<60' END as b2a_perc_bucket\n", "        FROM base\n", "    )\n", ")\n", "\n", "SELECT \n", "    slot,\n", "    store_id,\n", "    \n", "    count(distinct case when b2a_perc_bucket='1 DS97' then date_ END) DS97_instances,\n", "    count(distinct case when b2a_perc_bucket='2 DS94' then date_ END) DS94_instances,\n", "    count(distinct case when b2a_perc_bucket='3 DS90' then date_ END) DS90_instances,\n", "    count(distinct case when b2a_perc_bucket='4 DS85' then date_ END) DS85_instances,\n", "    count(distinct case when b2a_perc_bucket='5 DS80' then date_ END) DS80_instances,\n", "    count(distinct case when b2a_perc_bucket='6 DS70' then date_ END) DS70_instances,\n", "    count(distinct case when b2a_perc_bucket='7 DS60' then date_ END) DS60_instances,\n", "    count(distinct case when b2a_perc_bucket='8 DS<60' then date_ END) DS50_instances,\n", "\n", "    max(case when perc_rank<=0.50 AND b2a_perc_bucket='1 DS97' then hour_util else 0 end) perc_DS97_util,\n", "    max(case when perc_rank<=0.50 AND b2a_perc_bucket='2 DS94' then hour_util else 0 end) perc_DS94_util,\n", "    max(case when perc_rank<=0.50 AND b2a_perc_bucket='3 DS90' then hour_util else 0 end) perc_DS90_util,\n", "    max(case when perc_rank<=0.50 AND b2a_perc_bucket='4 DS85' then hour_util else 0 end) perc_DS85_util,\n", "    max(case when perc_rank<=0.50 AND b2a_perc_bucket='5 DS80' then hour_util else 0 end) perc_DS80_util,\n", "    max(case when perc_rank<=0.50 AND b2a_perc_bucket='6 DS70' then hour_util else 0 end) perc_DS70_util,\n", "    max(case when perc_rank<=0.50 AND b2a_perc_bucket='7 DS60' then hour_util else 0 end) perc_DS60_util,\n", "    max(case when perc_rank<=0.50 AND b2a_perc_bucket='8 DS<60' then hour_util else 0 end) perc_DS50_util,\n", "    \n", "    -- Add date columns for each bucket's median\n", "    MAX(CASE WHEN row_num = CEIL(total_rows/2.0) AND b2a_perc_bucket='1 DS97' THEN date_ END) AS perc_DS97_util_date,\n", "    MAX(CASE WHEN row_num = CEIL(total_rows/2.0) AND b2a_perc_bucket='2 DS94' THEN date_ END) AS perc_DS94_util_date,\n", "    MAX(CASE WHEN row_num = CEIL(total_rows/2.0) AND b2a_perc_bucket='3 DS90' THEN date_ END) AS perc_DS90_util_date,\n", "    MAX(CASE WHEN row_num = CEIL(total_rows/2.0) AND b2a_perc_bucket='4 DS85' THEN date_ END) AS perc_DS85_util_date,\n", "    MAX(CASE WHEN row_num = CEIL(total_rows/2.0) AND b2a_perc_bucket='5 DS80' THEN date_ END) AS perc_DS80_util_date,\n", "    MAX(CASE WHEN row_num = CEIL(total_rows/2.0) AND b2a_perc_bucket='6 DS70' THEN date_ END) AS perc_DS70_util_date,\n", "    MAX(CASE WHEN row_num = CEIL(total_rows/2.0) AND b2a_perc_bucket='7 DS60' THEN date_ END) AS perc_DS60_util_date,\n", "    MAX(CASE WHEN row_num = CEIL(total_rows/2.0) AND b2a_perc_bucket='8 DS<60' THEN date_ END) AS perc_DS50_util_date\n", "\n", "FROM ranked_data\n", "GROUP BY 1,2),\n", "\n", "ds_base as (\n", "SELECT \n", "c.blinkit_store_name,\n", "c.runnr_city,\n", "a.*,b.login_hr last_week_logins,\n", "b.orders orders_last_week,\n", "(DS90_instances + DS85_instances + DS80_instances + DS70_instances + DS60_instances + DS94_instances + DS97_instances + DS50_instances) as total_instances\n", "FROM DS95 a\n", "LEFT JOIN base_pre_login b on a.slot = b.slot and a.store_id = b.pickup_user_id\n", "INNER JOIN logistics_data_etls.blinkit_store_mapping c on a.store_id = c.store_id\n", "WHERE c.is_active=1),\n", "\n", "last_week_dh as (\n", "select\n", "week_start_date, slot, frontend_merchant_id, try(sum(direct_orders)*1.00/sum(delivered_orders)) as last_week_DH\n", "from (\n", "    Select \n", "    date_trunc('week', order_checkout_ts_ist) as week_start_date,\n", "    \n", "    case when extract(hour from order_checkout_ts_ist) between 1 and 6 then 'a Late Night + Early Morning'\n", "     when extract(hour from order_checkout_ts_ist) between 7 and 12 then 'b Morning'\n", "     when extract(hour from order_checkout_ts_ist) between 12 and 14 then 'c Afternoon'\n", "     when extract(hour from order_checkout_ts_ist) between 13 and 18 then 'c Afternoon + Evening'\n", "     when extract(hour from order_checkout_ts_ist) between 19 and 23 then 'd Night'\n", "     when extract(hour from order_checkout_ts_ist) = 0 then 'd Night'\n", "else null end as slot,\n", "\n", "    a.frontend_merchant_id,\n", "    count(distinct a.order_id) as delivered_orders,\n", "    count(distinct case when date_diff('second' ,order_billing_completed_ts_ist,order_partner_assigned_ts_ist)<=0 then a.order_id end) direct_orders\n", "    from dwh.fact_supply_chain_order_details a\n", "    INNER JOIN mapping m on cast(a.frontend_merchant_id as varchar)=m.blinkit_store_id\n", "    where order_checkout_dt_ist >= (current_date - interval '1' day) - interval '15' day\n", "    and date_trunc('week', order_checkout_ts_ist) = date_trunc('week', (current_date - interval '1' day)- interval '7' day)\n", "    and a.order_current_status = 'DELIVERED'\n", "    group by 1,2,3\n", ") a\n", "-- left join slots s on s.hour = a.hour and s.store_id = a.z_store_id\n", "group by 1,2,3),\n", "\n", "last_week_oph as (\n", "select\n", "date_trunc('week',account_date) as week_start_date,\n", "case when account_hour between 1 and 6 then 'a Late Night + Early Morning'\n", "     when account_hour between 7 and 12 then 'b Morning'\n", "     when account_hour between 13 and 18 then 'c Afternoon + Evening'\n", "     when account_hour between 19 and 23 then 'd Night'\n", "     when account_hour = 0 then 'd Night'\n", "else null end as slot,\n", "pickup_user_id,\n", "sum(orders) orders,\n", "sum(login_hr) login_hr,\n", "sum(orders)*1.00/sum(login_hr) as last_week_oph\n", "from base_pre\n", "where account_date >= (current_date - interval '1' day) - interval '15' day\n", "and date_trunc('week',account_date) = date_trunc('week', (current_date - interval '1' day) - interval '7' day)\n", "group by 1,2,3),\n", "\n", "final as (\n", "SELECT \n", "db.blinkit_store_name,\n", "db.runnr_city,\n", "db.slot,\n", "-- db.slot_name,\n", "db.store_id,\n", "last_week_logins,\n", "orders_last_week,\n", "sm.blinkit_store_id,\n", "perc_ds97_util,\n", "perc_ds94_util,\n", "perc_ds90_util,\n", "perc_ds85_util,\n", "perc_ds80_util,\n", "perc_ds70_util,\n", "perc_ds60_util,\n", "perc_ds50_util,\n", "\n", "ds97_instances,\n", "ds94_instances,\n", "ds90_instances,\n", "ds85_instances,\n", "ds80_instances,\n", "ds70_instances,\n", "ds60_instances,\n", "ds50_instances,\n", "\n", "total_instances,\n", "\n", "theo_rider_OPH theo_rider_overall_oph,\n", "\n", "case when DS85_instances >= 4 or (total_instances <= 10 and DS85_instances >=1) then perc_DS85_util\n", "     when DS80_instances >= 4 or (total_instances <= 10 and DS80_instances >=1) then perc_DS80_util\n", "     when DS70_instances >= 4 or (total_instances <= 10 and DS70_instances >=1) then perc_DS70_util\n", "     when DS60_instances >= 4 or (total_instances <= 10 and DS60_instances >=1) then perc_DS60_util\n", "     when DS90_instances >= 4 or (total_instances <= 10 and DS90_instances >=1) then perc_DS90_util\n", "     when DS94_instances >= 4 or (total_instances <= 10 and DS94_instances >=1) then perc_DS94_util\n", "     when DS97_instances >= 4 or (total_instances <= 10 and DS97_instances >=1) then perc_DS97_util\n", "     \n", "     when DS85_instances >= 1 then perc_DS85_util\n", "     when DS80_instances >= 1 then perc_DS80_util\n", "     when DS70_instances >= 1 then perc_DS70_util\n", "     when DS60_instances >= 1 then perc_DS60_util\n", "     when DS90_instances >= 1 then perc_DS90_util\n", "     when DS94_instances >= 1 then perc_DS94_util\n", "     when DS97_instances >= 1 then perc_DS97_util\n", "     when perc_DS50_util is not null then perc_DS50_util\n", "     \n", "else 2 end as OPH,\n", "\n", "case when DS85_instances >= 4 or (total_instances <= 10 and DS85_instances >=1) then perc_DS85_util_date\n", "     when DS80_instances >= 4 or (total_instances <= 10 and DS80_instances >=1) then perc_DS80_util_date\n", "     when DS70_instances >= 4 or (total_instances <= 10 and DS70_instances >=1) then perc_DS70_util_date\n", "     when DS60_instances >= 4 or (total_instances <= 10 and DS60_instances >=1) then perc_DS60_util_date\n", "     when DS90_instances >= 4 or (total_instances <= 10 and DS90_instances >=1) then perc_DS90_util_date\n", "     when DS94_instances >= 4 or (total_instances <= 10 and DS94_instances >=1) then perc_DS94_util_date\n", "     when DS97_instances >= 4 or (total_instances <= 10 and DS97_instances >=1) then perc_DS97_util_date\n", "     \n", "     when DS85_instances >= 1 then perc_DS85_util_date\n", "     when DS80_instances >= 1 then perc_DS80_util_date\n", "     when DS70_instances >= 1 then perc_DS70_util_date\n", "     when DS60_instances >= 1 then perc_DS60_util_date\n", "     when DS90_instances >= 1 then perc_DS90_util_date\n", "     when DS94_instances >= 1 then perc_DS94_util_date\n", "     when DS97_instances >= 1 then perc_DS97_util_date\n", "     \n", "else perc_DS50_util_date end as OPH_date,\n", "\n", "last_week_dh,\n", "last_week_oph\n", "\n", "FROM ds_base  DB \n", "left join logistics_data_etls.blinkit_store_mapping sm on sm.store_id = db.store_id\n", "LEFT JOIN theo_oph to on to.frontend_merchant_id = sm.blinkit_store_id and to.slot = db.slot\n", "left join last_week_dh l on l.frontend_merchant_id = sm.blinkit_store_id and l.slot = db.slot\n", "left join last_week_oph o on o.pickup_user_id = db.store_id and o.slot = db.slot\n", "\n", "GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29)\n", "\n", "\n", "select *,\n", "\n", "case when OPH = perc_DS97_util then 1.16\n", "     when OPH = perc_DS94_util then 1.11\n", "     when OPH = perc_DS90_util then 1.05\n", "     when OPH = perc_DS85_util then 1.00\n", "     when OPH = perc_DS80_util then 0.95\n", "     when OPH = perc_DS70_util then 0.89\n", "     when OPH = perc_DS60_util then 0.85\n", "     when OPH = perc_DS50_util then 0.81\n", "else 1.0 end as multiplier,\n", "\n", "case when OPH = perc_DS97_util then 1.16*OPH\n", "     when OPH = perc_DS94_util then 1.11*OPH\n", "     when OPH = perc_DS90_util then 1.05*OPH\n", "     when OPH = perc_DS85_util then 1.00*OPH\n", "     when OPH = perc_DS80_util then 0.95*OPH\n", "     when OPH = perc_DS70_util then 0.89*OPH\n", "     when OPH = perc_DS60_util then 0.85*OPH\n", "     when OPH = perc_DS50_util then 0.81*OPH\n", "else OPH end as optimal_oph\n", "\n", "\n", "from final\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "d3286100-91d8-4233-a5b5-10568e5a584c", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"oph_6_hour_85\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"blinkit_store_name\", \"type\": \"varchar\", \"description\": \"blinkit_store_name\"},\n", "        {\"name\": \"runnr_city\", \"type\": \"varchar\", \"description\": \"runnr_city\"},\n", "        {\"name\": \"slot\", \"type\": \"varchar\", \"description\": \"slot\"},\n", "        {\"name\": \"store_id\", \"type\": \"integer\", \"description\": \"store_id\"},\n", "        {\"name\": \"last_week_logins\", \"type\": \"double\", \"description\": \"logins_last_week\"},\n", "        {\"name\": \"orders_last_week\", \"type\": \"double\", \"description\": \"orders_last_week\"},\n", "        {\"name\": \"blinkit_store_id\", \"type\": \"integer\", \"description\": \"blinkit_store_id\"},\n", "        {\"name\": \"perc_DS97_util\", \"type\": \"double\", \"description\": \"perc_ds97_util\"},\n", "        {\"name\": \"perc_DS94_util\", \"type\": \"double\", \"description\": \"perc_ds94_util\"},\n", "        {\"name\": \"perc_DS90_util\", \"type\": \"double\", \"description\": \"perc_ds90_util\"},\n", "        {\"name\": \"perc_DS85_util\", \"type\": \"double\", \"description\": \"perc_ds85_util\"},\n", "        {\"name\": \"perc_DS80_util\", \"type\": \"double\", \"description\": \"perc_ds80_util\"},\n", "        {\"name\": \"perc_DS70_util\", \"type\": \"double\", \"description\": \"perc_ds70_util\"},\n", "        {\"name\": \"perc_DS60_util\", \"type\": \"double\", \"description\": \"perc_ds60_util\"},\n", "        {\"name\": \"perc_DS50_util\", \"type\": \"double\", \"description\": \"perc_ds50_util\"},\n", "        {\"name\": \"DS97_instances\", \"type\": \"integer\", \"description\": \"ds97_instances\"},\n", "        {\"name\": \"DS94_instances\", \"type\": \"integer\", \"description\": \"ds94_instances\"},\n", "        {\"name\": \"DS90_instances\", \"type\": \"integer\", \"description\": \"ds90_instances\"},\n", "        {\"name\": \"DS85_instances\", \"type\": \"integer\", \"description\": \"ds85_instances\"},\n", "        {\"name\": \"DS80_instances\", \"type\": \"integer\", \"description\": \"ds80_instances\"},\n", "        {\"name\": \"DS70_instances\", \"type\": \"integer\", \"description\": \"ds70_instances\"},\n", "        {\"name\": \"DS60_instances\", \"type\": \"integer\", \"description\": \"ds60_instances\"},\n", "        {\"name\": \"DS50_instances\", \"type\": \"integer\", \"description\": \"ds50_instances\"},\n", "        {\"name\": \"total_instances\", \"type\": \"integer\", \"description\": \"total_instances\"},\n", "        {\n", "            \"name\": \"theo_rider_overall_OPH\",\n", "            \"type\": \"double\",\n", "            \"description\": \"theo_rider_overall_oph\",\n", "        },\n", "        {\"name\": \"OPH\", \"type\": \"double\", \"description\": \"oph\"},\n", "        {\"name\": \"OPH_date\", \"type\": \"date\", \"description\": \"oph_date\"},\n", "        {\"name\": \"last_week_dh\", \"type\": \"double\", \"description\": \"dh_last_week\"},\n", "        {\"name\": \"last_week_oph\", \"type\": \"double\", \"description\": \"oph_last_week\"},\n", "        {\"name\": \"multiplier\", \"type\": \"double\", \"description\": \"multiplier\"},\n", "        {\"name\": \"optimal_oph\", \"type\": \"double\", \"description\": \"optimal_oph\"},\n", "    ],\n", "    \"primary_key\": [\"store_id\", \"slot\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"This table contains the ideal OPH for stores at multiple slot level at 90-95 and 85-90 DH\",\n", "}\n", "pb.to_trino(sql_6_hour_1, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "4c4bbe53-737e-487d-817f-eb0bea3005d9", "metadata": {}, "outputs": [], "source": ["sql_6_hour_2 = \"\"\"\n", "\n", "with btpo_base_day_slot as (\n", "\n", "with mapping as (\n", "select z_store_id, blinkit_store_id, store_name\n", "from (\n", "    select distinct id as z_store_id, external_id as blinkit_store_id, name as store_name,\n", "    rank() over (partition by id order by updated_at desc) as rnk\n", "    from zomato.carthero_prod.users where type = 'merchant' and source_id = 17110 \n", "    and (created_at) >= cast(date('2019-05-01') as timestamp)\n", ") where rnk=1\n", "group by 1,2,3\n", "),\n", "\n", "base as\n", "(   \n", "  Select \n", "   date_,\n", "   date_trunc('week',date_) as week_start_date,\n", "   hour_,\n", "   frontend_merchant_id,\n", "   sum(log_orders) log_orders,\n", "   sum(delivered_orders) delivered_orders,\n", "   sum(direct_handover_orders) direct_handover_orders,\n", "   sum(store_handshake_time) store_handshake_time,\n", "   sum(enroute_to_doorstep) enroute_to_doorstep,\n", "   sum(doorstep_to_delivered) doorstep_to_delivered\n", "   from logistics_data_etls.hourly_supply_metrics_etl\n", "   where date_ >= current_date - interval '65' day\n", "   group by 1,2,3,4\n", " ),\n", " \n", " trip as \n", "(\n", "  Select trip_id as trip_id_,accounting_time\n", "  from logistics_data_etls.delivery_trip_payout\n", "  where dt>=date_format(current_date - interval '65' day, '%%Y%%m%%d')\n", "  group by 1,2\n", "),\n", " \n", " fact as \n", " (\n", "    SELECT frontend_merchant_id,\n", "    date(order_checkout_ts_ist) date_,\n", "    extract(hour from order_checkout_ts_ist) hour_,\n", "    sum(case when order_partner_assigned_ts_ist < order_billing_completed_ts_ist then \n", "                      (date_diff('second',order_partner_assigned_ts_ist,order_billing_completed_ts_ist)) end) wait_time\n", "    FROM dwh.fact_supply_chain_order_details oh\n", "    INNER JOIN mapping m on cast(oh.frontend_merchant_id as varchar)=m.blinkit_store_id\n", "    WHERE (order_current_status)='DELIVERED'\n", "    AND order_checkout_dt_ist>date(current_date - interval '1' day)-interval '65' day\n", "    AND (order_checkout_ts_ist)>=cast(date(current_date - interval '1' day)-interval '65' day as timestamp)\n", "    GROUP BY 1,2,3\n", "),\n", " \n", " \n", " btpo_base as \n", " (\n", "    select \n", "    date,\n", "    extract(hour from cast(coalesce(cast(accounting_time as varchaR),ts) as timestamp)) as hour,\n", "    cast(blinkit_store_id as int) as blinkit_store_id,\n", "    store_name,\n", "    city_name,\n", "    coalesce(sum(case when lead_event!='Logout' then return_time end),0) as total_return_time,\n", "    coalesce(sum(idle_time),0) as total_idle_time,\n", "    date_trunc('week',cast(date as date)) as week_start\n", "   from  serviceability_etls.btpo_rider_events_v2 as b\n", "   left join trip t on cast(t.trip_id_ as varchar) = b.trip_id\n", "   WHERE store_name NOT LIKE 'NA'\n", "   and date >= cast(current_date - interval '65' day as varchar)\n", "   group by 1,2,3,4,5\n", "   order by 1,2\n", "   )\n", "   \n", "   \n", "   select \n", "   date,\n", "     case when hour between 1 and 6 then 'a Late Night + Early Morning'\n", "     when hour between 7 and 12 then 'b Morning'\n", "     when hour between 13 and 18 then 'c Afternoon + Evening'\n", "     when hour between 19 and 23 then 'd Night'\n", "     when hour = 0 then 'd Night'\n", "   else null end as slot,\n", "   city_name,\n", "   blinkit_store_name,\n", "   blinkit_store_id,\n", "   sum(log_orders) as log_orders,\n", "   sum(delivered_orders) as delivered_orders,\n", "           sum(store_handshake_time) store_handshake_time,\n", "        sum(enroute_to_doorstep) enroute_to_doorstep,\n", "        sum(doorstep_to_delivered) doorstep_to_delivered,\n", "        sum(total_return_time) total_return_time,\n", "        sum(total_idle_time) total_idle_time,\n", "        sum(wait_time) wait_time\n", "        \n", "        from (\n", "   Select bt.blinkit_store_id,\n", "        sm.runnr_city as city_name,\n", "        date_trunc('week',date(bt.date)) as week_start_date,\n", "        extract(week from date(bt.date)) as wk,\n", "        extract(dow from date(bt.date)) as dow,\n", "        date(bt.date) as date,\n", "        blinkit_store_name,\n", "        store_id,\n", "        bt.hour,\n", "        coalesce(log_orders,0) log_orders,\n", "        coalesce(delivered_orders,0) delivered_orders,\n", "        coalesce(store_handshake_time,0) store_handshake_time,\n", "        coalesce(enroute_to_doorstep,0) enroute_to_doorstep,\n", "        coalesce(doorstep_to_delivered,0) doorstep_to_delivered,\n", "        coalesce(bt.total_return_time,0) total_return_time,\n", "        coalesce(bt.total_idle_time,0) total_idle_time,\n", "        coalesce(f.wait_time,0) wait_time\n", "     from btpo_base bt\n", "     left join base b on b.frontend_merchant_id = bt.blinkit_store_id and b.hour_ = bt.hour and cast(b.date_ as varchar) = bt.date\n", "     left join fact f on f.frontend_merchant_id = bt.blinkit_store_id and bt.hour = f.hour_ and bt.date = cast(f.date_ as varchar)\n", "     left join logistics_data_etls.blinkit_store_mapping sm on sm.blinkit_store_id = bt.blinkit_store_id\n", "     )\n", "     group by 1,2,3,4,5\n", "),\n", "\n", "btpo_base_week as (\n", "select \n", "date_trunc('week',date) as week_start_date,\n", "city_name,\n", "blinkit_store_name,\n", "blinkit_store_id,\n", "sum(log_orders) as log_orders,\n", "sum(delivered_orders) as delivered_orders,\n", "sum(store_handshake_time) store_handshake_time,\n", "sum(enroute_to_doorstep) enroute_to_doorstep,\n", "sum(doorstep_to_delivered) doorstep_to_delivered,\n", "sum(total_return_time) total_return_time,\n", "sum(total_idle_time) total_idle_time,\n", "sum(wait_time) wait_time\n", "from\n", "btpo_base_day_slot\n", "group by 1,2,3,4\n", ")\n", "\n", "\n", "select \n", "a.*,\n", "b.log_orders log_orders_oph_day_slot,\n", "b.delivered_orders delivered_orders_oph_day_slot,\n", "b.store_handshake_time store_handshake_time_oph_day_slot,\n", "b.enroute_to_doorstep enroute_to_doorstep_oph_day_slot,\n", "b.doorstep_to_delivered doorstep_to_delivered_oph_day_slot,\n", "b.total_return_time total_return_time_oph_day_slot,\n", "b.total_idle_time total_idle_time_oph_day_slot,\n", "b.wait_time wait_time_oph_day_slot,\n", "(b.store_handshake_time + b.enroute_to_doorstep +  b.doorstep_to_delivered +\n", "b.total_return_time + b.total_idle_time + b.wait_time) total_time_oph_day_slot,\n", "try(((b.store_handshake_time + b.enroute_to_doorstep + b.doorstep_to_delivered +\n", "b.total_return_time + b.total_idle_time + b.wait_time)*1.0000 / (b.log_orders))) as total_time_avg_oph_day_slot,\n", "try((3600.0000)/(((b.store_handshake_time + b.enroute_to_doorstep + b.doorstep_to_delivered +\n", "b.total_return_time + b.total_idle_time + b.wait_time)*1.0000 / (b.log_orders)))) as theo_util_oph_day_slot,\n", "\n", "\n", "c.log_orders log_orders_oph_week,\n", "c.delivered_orders delivered_orders_oph_week,\n", "c.store_handshake_time store_handshake_time_oph_week,\n", "c.enroute_to_doorstep enroute_to_doorstep_oph_week,\n", "c.doorstep_to_delivered doorstep_to_delivered_oph_week,\n", "c.total_return_time total_return_time_oph_week,\n", "c.total_idle_time total_idle_time_oph_week,\n", "c.wait_time wait_time_oph_week,\n", "try(((c.store_handshake_time + c.enroute_to_doorstep + c.doorstep_to_delivered +\n", "c.total_return_time + c.total_idle_time + c.wait_time)*1.0000 / (c.log_orders))) as total_time_avg_oph_week,\n", "try((3600.0000)/(((c.store_handshake_time + c.enroute_to_doorstep + c.doorstep_to_delivered +\n", "c.total_return_time + c.total_idle_time + c.wait_time)*1.0000 / (c.log_orders)))) as theo_util_oph_week\n", "\n", "\n", "from\n", "interim.oph_6_hour_85 a\n", "left join btpo_base_day_slot b on a.blinkit_store_id = b.blinkit_store_id and a.slot = b.slot and a.oph_date = b.date\n", "left join btpo_base_week c on a.blinkit_store_id = c.blinkit_store_id and date_trunc('week',a.oph_date) = c.week_start_date\n", "\n", "     \n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "1f40e61b-3fe5-4459-a80c-1bad2c160285", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"final_oph_6_hour_85\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"blinkit_store_name\", \"type\": \"varchar\", \"description\": \"blinkit_store_name\"},\n", "        {\"name\": \"runnr_city\", \"type\": \"varchar\", \"description\": \"runnr_city\"},\n", "        {\"name\": \"slot\", \"type\": \"varchar\", \"description\": \"slot\"},\n", "        {\"name\": \"store_id\", \"type\": \"integer\", \"description\": \"store_id\"},\n", "        {\"name\": \"last_week_logins\", \"type\": \"double\", \"description\": \"logins_last_week\"},\n", "        {\"name\": \"orders_last_week\", \"type\": \"double\", \"description\": \"orders_last_week\"},\n", "        {\"name\": \"blinkit_store_id\", \"type\": \"integer\", \"description\": \"blinkit_store_id\"},\n", "        {\"name\": \"perc_DS97_util\", \"type\": \"double\", \"description\": \"perc_ds97_util\"},\n", "        {\"name\": \"perc_DS94_util\", \"type\": \"double\", \"description\": \"perc_ds94_util\"},\n", "        {\"name\": \"perc_DS90_util\", \"type\": \"double\", \"description\": \"perc_ds90_util\"},\n", "        {\"name\": \"perc_DS85_util\", \"type\": \"double\", \"description\": \"perc_ds85_util\"},\n", "        {\"name\": \"perc_DS80_util\", \"type\": \"double\", \"description\": \"perc_ds80_util\"},\n", "        {\"name\": \"perc_DS70_util\", \"type\": \"double\", \"description\": \"perc_ds70_util\"},\n", "        {\"name\": \"perc_DS60_util\", \"type\": \"double\", \"description\": \"perc_ds60_util\"},\n", "        {\"name\": \"perc_DS50_util\", \"type\": \"double\", \"description\": \"perc_ds50_util\"},\n", "        {\"name\": \"DS97_instances\", \"type\": \"integer\", \"description\": \"ds97_instances\"},\n", "        {\"name\": \"DS94_instances\", \"type\": \"integer\", \"description\": \"ds94_instances\"},\n", "        {\"name\": \"DS90_instances\", \"type\": \"integer\", \"description\": \"ds90_instances\"},\n", "        {\"name\": \"DS85_instances\", \"type\": \"integer\", \"description\": \"ds85_instances\"},\n", "        {\"name\": \"DS80_instances\", \"type\": \"integer\", \"description\": \"ds80_instances\"},\n", "        {\"name\": \"DS70_instances\", \"type\": \"integer\", \"description\": \"ds70_instances\"},\n", "        {\"name\": \"DS60_instances\", \"type\": \"integer\", \"description\": \"ds60_instances\"},\n", "        {\"name\": \"DS50_instances\", \"type\": \"integer\", \"description\": \"ds50_instances\"},\n", "        {\"name\": \"total_instances\", \"type\": \"integer\", \"description\": \"total_instances\"},\n", "        {\n", "            \"name\": \"theo_rider_overall_OPH\",\n", "            \"type\": \"double\",\n", "            \"description\": \"theo_rider_overall_oph\",\n", "        },\n", "        {\"name\": \"OPH\", \"type\": \"double\", \"description\": \"oph\"},\n", "        {\"name\": \"OPH_date\", \"type\": \"date\", \"description\": \"oph_date\"},\n", "        {\"name\": \"last_week_dh\", \"type\": \"double\", \"description\": \"dh_last_week\"},\n", "        {\"name\": \"last_week_oph\", \"type\": \"double\", \"description\": \"oph_last_week\"},\n", "        {\"name\": \"multiplier\", \"type\": \"double\", \"description\": \"multiplier\"},\n", "        {\"name\": \"optimal_oph\", \"type\": \"double\", \"description\": \"optimal_oph\"},\n", "        {\n", "            \"name\": \"log_orders_oph_day_slot\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"log_orders_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"delivered_orders_oph_day_slot\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"delivered_orders_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"store_handshake_time_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"store_handshake_time_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"enroute_to_doorstep_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"enroute_to_doorstep_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"doorstep_to_delivered_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"doorstep_to_delivered_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"total_return_time_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_return_time_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"total_idle_time_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_idle_time_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"wait_time_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"wait_time_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"total_time_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_time_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"total_time_avg_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_time_avg_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"theo_util_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"theo_util_oph_day_slot\",\n", "        },\n", "        {\"name\": \"log_orders_oph_week\", \"type\": \"integer\", \"description\": \"log_orders_oph_week\"},\n", "        {\n", "            \"name\": \"delivered_orders_oph_week\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"delivered_orders_oph_week\",\n", "        },\n", "        {\n", "            \"name\": \"store_handshake_time_oph_week\",\n", "            \"type\": \"double\",\n", "            \"description\": \"store_handshake_time_oph_week\",\n", "        },\n", "        {\n", "            \"name\": \"enroute_to_doorstep_oph_week\",\n", "            \"type\": \"double\",\n", "            \"description\": \"enroute_to_doorstep_oph_week\",\n", "        },\n", "        {\n", "            \"name\": \"doorstep_to_delivered_oph_week\",\n", "            \"type\": \"double\",\n", "            \"description\": \"doorstep_to_delivered_oph_week\",\n", "        },\n", "        {\n", "            \"name\": \"total_return_time_oph_week\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_return_time_oph_week\",\n", "        },\n", "        {\n", "            \"name\": \"total_idle_time_oph_week\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_idle_time_oph_week\",\n", "        },\n", "        {\"name\": \"wait_time_oph_week\", \"type\": \"double\", \"description\": \"wait_time_oph_week\"},\n", "        {\n", "            \"name\": \"total_time_avg_oph_week\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_time_avg_oph_week\",\n", "        },\n", "        {\"name\": \"theo_util_oph_week\", \"type\": \"double\", \"description\": \"theo_util_oph_week\"},\n", "    ],\n", "    \"primary_key\": [\"date\", \"slot\", \"store_id\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"This table contains the ideal OPH for stores at multiple slot level at 90-95 and 85-90 DH\",\n", "}\n", "pb.to_trino(sql_6_hour_2, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "f5d23161-a4a7-4787-8656-f1f51e419e9d", "metadata": {}, "outputs": [], "source": ["sql_6_hour_3 = \"\"\"\n", "\n", "with delivery_distance as (\n", "select\n", "extract(week from account_date) as week_number,\n", "date_trunc('week',account_date) as week_start_date,\n", "blinkit_store_id,\n", "blinkit_store_name,\n", "sum(delivered_orders_distance) as delivered_distance,\n", "sum(delivered_orders) as delivered_orders,\n", "try(sum(delivered_orders_distance) / sum(delivered_orders)) as average_dd\n", "from\n", "logistics_data_etls.blinkit_rider_daily_accounting_summary_v1\n", "where account_date >= current_date - interval '70' day\n", "group by 1,2,3,4\n", "),\n", "\n", "final_base as (\n", "select *,\n", "try(total_idle_time_oph_day_slot*100.00/total_time_oph_day_slot) as idle_time_ratio_oph_day_slot,\n", "\n", "(0.35* (store_handshake_time_oph_day_slot + doorstep_to_delivered_oph_day_slot +  total_return_time_oph_day_slot\n", "+ enroute_to_doorstep_oph_day_slot + wait_time_oph_day_slot ) / 0.65) as ideal_idle_time_oph_day_slot,\n", "\n", "\n", "try((3600.0000)/(((store_handshake_time_oph_week + doorstep_to_delivered_oph_week +\n", "(total_return_time_oph_week + enroute_to_doorstep_oph_week)*\n", "(final_dd_correction_factor)\n", "+ total_idle_time_oph_week + wait_time_oph_week)*1.0000 / (log_orders_oph_week)))) as theo_util_oph_week_dd_corrected,\n", "\n", "try((3600.0000) / (((1.0000* (store_handshake_time_oph_day_slot + doorstep_to_delivered_oph_day_slot +  total_return_time_oph_day_slot\n", "+ enroute_to_doorstep_oph_day_slot + wait_time_oph_day_slot ) / 0.65)) *1.0000 / (log_orders_oph_day_slot))) as theo_util_ideal_oph_day_slot\n", "\n", "from\n", "(\n", "\n", "select \n", "a.*,\n", "\n", "ddl.average_dd dd_avg_last_week,\n", "dd.average_dd dd_avg_oph_week,\n", "\n", "case when try((ddl.average_dd * 1.0000 / dd.average_dd)) >= 1.30 then 1.30\n", "     when try((ddl.average_dd * 1.0000 / dd.average_dd)) <= 0.70 then 0.70\n", "else try((ddl.average_dd * 1.0000 / dd.average_dd)) end as final_dd_correction_factor\n", "\n", "\n", "from\n", "interim.final_oph_6_hour_85 a\n", "left join delivery_distance ddl on ddl.blinkit_store_id = a.blinkit_store_id and ddl.week_start_date = date_trunc('week',(current_date - interval '1' day)) - interval '7' day\n", "left join delivery_distance dd on a.blinkit_store_id = dd.blinkit_store_id and date_trunc('week',a.OPH_date) = dd.week_start_date\n", ")\n", "),\n", "\n", "\n", "final as (\n", "\n", "select *, extract(week from OPH_date) as oph_week_number,\n", "date_trunc('week',OPH_date) as oph_week_start_date,\n", "\n", "try((dd_avg_last_week)*1.0000/(dd_avg_oph_week)) as dd_correction_factor,\n", "\n", "try(abs(theo_util_oph_week_dd_corrected - theo_util_oph_week)*1.0000/theo_util_oph_week) as perc_change_theo_oph_dd,\n", "\n", "try((theo_util_ideal_oph_day_slot - theo_util_oph_day_slot)*1.0000/theo_util_oph_day_slot) as perc_change_theo_oph_it\n", "\n", "from final_base\n", "),\n", "\n", "final_dd_correction as (\n", "select *,\n", "case when final_dd_correction_factor >= 1.10 then optimal_oph * (1 + perc_change_theo_oph_dd) \n", "     when final_dd_correction_factor <= 0.90 then optimal_oph * (1 - perc_change_theo_oph_dd)\n", "     else optimal_oph\n", "end as optimal_oph_corrected_dd\n", "from\n", "final\n", ")\n", "\n", "select \n", "    blinkit_store_name,\n", "    runnr_city,\n", "    slot,\n", "    store_id,\n", "    last_week_logins logins_last_week,\n", "    orders_last_week,\n", "    blinkit_store_id,\n", "    perc_DS97_util,\n", "    perc_DS94_util,\n", "    perc_DS90_util,\n", "    perc_DS85_util,\n", "    perc_DS80_util,\n", "    perc_DS70_util,\n", "    perc_DS60_util,\n", "    perc_DS50_util,\n", "    DS97_instances,\n", "    DS94_instances,\n", "    DS90_instances,\n", "    DS85_instances,\n", "    DS80_instances,\n", "    DS70_instances,\n", "    DS60_instances,\n", "    DS50_instances,\n", "    theo_rider_overall_OPH,\n", "    OPH,\n", "    OPH_date,\n", "    last_week_dh dh_last_week,\n", "    last_week_oph oph_last_week,\n", "    dd_avg_last_week,\n", "    dd_avg_oph_week,\n", "    log_orders_oph_week,\n", "    delivered_orders_oph_week,\n", "    store_handshake_time_oph_week,\n", "    enroute_to_doorstep_oph_week,\n", "    doorstep_to_delivered_oph_week,\n", "    total_return_time_oph_week,\n", "    total_idle_time_oph_week,\n", "    wait_time_oph_week,\n", "    total_time_avg_oph_week,\n", "    theo_util_oph_week,\n", "    log_orders_oph_day_slot,\n", "    delivered_orders_oph_day_slot,\n", "    store_handshake_time_oph_day_slot,\n", "    enroute_to_doorstep_oph_day_slot,\n", "    doorstep_to_delivered_oph_day_slot,\n", "    total_return_time_oph_day_slot,\n", "    total_idle_time_oph_day_slot,\n", "    wait_time_oph_day_slot,\n", "    total_time_oph_day_slot,\n", "    total_time_avg_oph_day_slot,\n", "    theo_util_oph_day_slot,\n", "    final_dd_correction_factor,\n", "    null as dh_oph_day_slot,\n", "    idle_time_ratio_oph_day_slot,\n", "    ideal_idle_time_oph_day_slot,\n", "    theo_util_oph_week_dd_corrected,\n", "    theo_util_ideal_oph_day_slot,\n", "    oph_week_number,\n", "    oph_week_start_date,\n", "    dd_correction_factor,\n", "    perc_change_theo_oph_dd,\n", "    perc_change_theo_oph_it,\n", "    multiplier,\n", "    optimal_oph,\n", "    optimal_oph_corrected_dd,\n", "    final_idle_time_correction_factor,\n", "    optimal_oph_corrected_idle_time,\n", "    slot_level,\n", "    Final_OPH_dh_bucket,\n", "    updated_on\n", "    \n", "    from\n", "    (\n", "\n", "select *,\n", "case when idle_time_ratio_oph_day_slot >= 35 then least(((1 + perc_change_theo_oph_it)/multiplier),1.40)\n", "else 1 end as final_idle_time_correction_factor,\n", "coalesce(greatest(least(case when idle_time_ratio_oph_day_slot >= 35 then optimal_oph_corrected_dd * least(((1 + perc_change_theo_oph_it)/multiplier),1.40) \n", "     else optimal_oph_corrected_dd\n", "end,4),0.5),2) as optimal_oph_corrected_idle_time,\n", "\n", "'6_hour_slot' as slot_level,\n", "'85-90' as Final_OPH_dh_bucket,\n", "current_date as updated_on\n", "\n", "from final_dd_correction\n", ")\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "e66daf5a-df6d-4100-a23e-afa9f86c0658", "metadata": {}, "outputs": [], "source": ["six_hour_85 = pd.read_sql_query(sql_6_hour_3, conn)"]}, {"cell_type": "code", "execution_count": null, "id": "40e0b735-b1d7-4353-87cf-c1347a31f9d6", "metadata": {}, "outputs": [], "source": ["sql_4_hour_1 = \"\"\"\n", "\n", "with mapping as (\n", "select distinct z_store_id, blinkit_store_id, store_name\n", "from (\n", "    select distinct id as z_store_id, external_id as blinkit_store_id, name as store_name,\n", "    rank() over (partition by id order by updated_at desc) as rnk\n", "    from zomato.carthero_prod.users where type = 'merchant' and source_id = 17110 \n", "    and (created_at) >= cast(date('2019-05-01') as timestamp)\n", ") where rnk=1),\n", "\n", "lost_orders_base as (\n", "select \n", "ss.*, m.z_store_id store_id \n", "from zomato.jumbo_external.surge_segg ss\n", "inner join mapping m on cast(ss.merchant_id as varchar) = m.blinkit_store_id\n", "where date(at_date_ist) >= (current_date - interval '1' day)-INTERVAL '60' day),\n", "\n", "rain_base as (\n", "select\n", "account_date,\n", "extract(hour from accounting_time) as hour,\n", "accounting_time,\n", "trip_id,\n", "surge_pay,\n", "frontend_merchant_id,\n", "lead(accounting_time) over(partition by  frontend_merchant_id order by accounting_time) as lead_ac,\n", "lead(surge_pay) over(partition by  frontend_merchant_id order by accounting_time) as surge_pay_2\n", "from logistics_data_etls.delivery_trip_payout p\n", "left join ( \n", "    select \n", "    distinct \n", "    trip_id as tid,\n", "    frontend_merchant_id \n", "    from dwh.fact_supply_chain_order_details \n", "    where order_checkout_dt_ist >= date(current_date - interval '1' day)-INTERVAL '60' day) x\n", "on cast(p.trip_id as varchar) = x.tid\n", "where dt >= date_format(date(current_date - interval '1' day)-INTERVAL '60' day, '%%Y%%m%%d')),\n", "\n", "log_base3 as (\n", "select * from (\n", "select\n", "account_date,\n", "hour,\n", "frontend_merchant_id,\n", "sm.store_id,\n", "sum(case when surge_pay>0 and surge_pay_2>0 then date_diff('second',accounting_time,lead_ac) end )*1.00/60 as rain_mins\n", "from rain_base b\n", "left join logistics_data_etls.blinkit_store_mapping sm on b.frontend_merchant_id = sm.blinkit_store_id\n", "group by 1,2,3,4)\n", "where rain_mins >= 15),\n", "\n", "b2a_base as (\n", "select \n", "date_,hour_,z_store_id,b2a_orders,order_count,b2a_orders*1.00/order_count b2a_perc\n", "FROM (\n", "    SELECT date(order_checkout_ts_ist) date_,extract(hour from order_checkout_ts_ist) hour_,z_store_id,\n", "    count(distinct case when date_diff('Second',order_billing_completed_ts_ist,order_partner_assigned_ts_ist)*1.00/60>0 then order_id else NULL end) as b2a_orders,\n", "    count(distinct order_id) order_count\n", "    FROM dwh.fact_supply_chain_order_details fsc\n", "    LEFT JOIN mapping m ON cast(fsc.frontend_merchant_id as varchar)=m.blinkit_store_id\n", "    WHERE date(order_checkout_dt_ist) between current_date-interval '61' day AND current_date-interval '1' day\n", "    AND order_current_status='DELIVERED'\n", "    GROUP BY 1,2,3\n", ")),\n", "\n", "working_hours as (\n", "select  \n", "account_date as date_,\n", "extract(hour from hr) hour_,\n", "delivery_driver_id,\n", "-- sum(logins_min_calculated)*1.0000 as login\n", "CAST(sum(logins_min_calculated) * 1.0000 / 60 AS DECIMAL(10, 4)) as login_hr\n", "-- ROUND(sum(logins_min_calculated*1.0000) * 1.0000 / 60, 4)\n", "from logistics_data_etls.hourly_dp_wasted_login_v1\n", "where account_date >= (date(current_date - interval '1' day) - INTERVAL '60' day)\n", "GROUP BY 1,2,3\n", "),\n", "\n", "orders1 as (\n", "select \n", "distinct delivery_driver_id, --trip_id,  \n", "o.pickup_user_id, date(accounting_time+interval '330' minute) as account_date, \n", "hour(accounting_time+interval '330' minute) as account_hour,\n", "count(case when leg_status='COMPLETE' and lower(report_type)='drop' then ar.id else null end) as orders \n", "from zomato.accounting_production.accounting_reports ar\n", "left join zomato.carthero_prod.shipment_legs sl on cast(sl.id as varchar) = coalesce(cast(ar.shipment_leg_id as varchar),ar.logistics_order_id)\n", "left join zomato.carthero_prod.orders o on o.id=sl.order_id\n", "INNER JOIN mapping m on o.pickup_user_id=m.z_store_id \n", "where ar.dt>=date_format(date(current_date - interval '1' day) - INTERVAL '60' day, '%%Y%%m%%d')\n", "and sl.dt>=date_format(date(current_date - interval '1' day) - INTERVAL '60' day, '%%Y%%m%%d')\n", "and o.dt>=date_format(date(current_date - interval '1' day) - INTERVAL '60' day, '%%Y%%m%%d')\n", "and merchant_category='BLINKIT'\n", "group by 1,2,3,4),\n", "\n", "orders2 as (\n", "select distinct delivery_driver_id, --trip_id , \n", "o.pickup_user_id, date(accounting_time+interval '330' minute) as account_date, \n", "hour(accounting_time+interval '330' minute) as account_hour, \n", "count(case when leg_status='COMPLETE' and lower(touch_point_type)='drop' then tp.id else null end) as orders \n", "from zomato.accounting_production.driver_touch_point_reports tp\n", "left join zomato.carthero_prod.shipment_legs sl on cast(sl.id as varchar)=coalesce(cast(tp.shipment_leg_id as varchar),tp.logistics_order_id)\n", "left join zomato.carthero_prod.orders o on o.id=sl.order_id\n", "INNER JOIN mapping m on o.pickup_user_id=m.z_store_id \n", "--left join carthero_prod.orders o on sl.order_id=o.id\n", "where tp.dt>=date_format(date(current_date - interval '1' day) - INTERVAL '60' day, '%%Y%%m%%d')\n", "and sl.dt>=date_format(date(current_date - interval '1' day) - INTERVAL '60' day, '%%Y%%m%%d')\n", "and o.dt>=date_format(date(current_date - interval '1' day) - INTERVAL '60' day, '%%Y%%m%%d')\n", "and merchant_category='BLINKIT'\n", "group by 1,2,3,4),\n", "\n", "hourly_orders_base as(\n", "select * from orders1 \n", "union\n", "select * from orders2),\n", "\n", "hourly_orders as (\n", "select delivery_driver_id, pickup_user_id,\n", "account_date,\n", "account_hour,\n", "sum(orders) orders\n", "from\n", "hourly_orders_base\n", "group by 1,2,3,4\n", "),\n", "\n", "login_base_x as (\n", "select \n", "distinct\n", "date_, \n", "hour_,\n", "l.delivery_driver_id,\n", "sum(login_hr) as login_hr,\n", "max_by(z_store_id,account_date) as z_store_id\n", "from working_hours l\n", "left join logistics_data_etls.blinkit_rider_daily_accounting_summary_v1 ho on ho.account_date = l.date_ \n", "     and ho.delivery_driver_id = l.delivery_driver_id and ho.account_date between date(current_date - interval '1' day)-interval '60' day AND date(current_date - interval '1' day)-interval '1' day\n", "where date_ between date(current_date - interval '1' day)-interval '60' day AND date(current_date - interval '1' day)-interval '1' day\n", "group by 1,2,3),\n", "\n", "base_pre as (\n", "SELECT \n", "wh.date_ as account_date,\n", "wh.hour_ as account_hour,\n", "wh.z_store_id as pickup_user_id,\n", "wh.delivery_driver_id,\n", "sum(ho.orders) orders,\n", "sum(login_hr) login_hr\n", "FROM login_base_x wh \n", "LEFT JOIN hourly_orders ho ON ho.account_date=wh.date_ AND ho.account_hour=wh.hour_ AND ho.delivery_driver_id = wh.delivery_driver_id\n", "GROUP BY 1,2,3,4),\n", "\n", "slots as (\n", "select \n", "store_id,\n", "hour,\n", "max_by(slot_id,account_date) as slot_id,\n", "max_by(slot_name,account_date) as slot_name\n", "from logistics_data_etls.b_gigs_design_ver3\n", "where dt > '********'\n", "group by 1,2),\n", "\n", "base_pre_login as (\n", "select \n", "\n", "case when account_hour between 1 and 3 then 'a Late Night'\n", "     when account_hour between 4 and 7 then 'b Early Morning'\n", "     when account_hour between 8 and 12 then 'c Morniing'\n", "     when account_hour between 13 and 16 then 'd Afternoon'\n", "     when account_hour between 17 and 20 then 'e Evening'\n", "     when account_hour between 21 and 23 then 'f Night'\n", "     when account_hour = 0 then 'f Night'\n", "else null end as slot,\n", "\n", "pickup_user_id,\n", "\n", "sum(login_hr) login_hr,\n", "sum(orders) orders\n", "from base_pre \n", "where account_date between (current_date - interval '1' day)-interval '7' day AND (current_date - interval '1' day) -interval '1' day\n", "GROUP BY 1,2),\n", "\n", "base as (\n", "SELECT \n", "fsc.date_,\n", "extract(dow from fsc.date_) dow,\n", "case when extract(dow from fsc.date_) between 1 AND 5 then 'WEEKDAY' ELSE 'WEEKEND' END AS dow1,\n", "\n", "\n", "case when fsc.hour_ between 1 and 3 then 'a Late Night'\n", "     when fsc.hour_ between 4 and 7 then 'b Early Morning'\n", "     when fsc.hour_ between 8 and 12 then 'c Morniing'\n", "     when fsc.hour_ between 13 and 16 then 'd Afternoon'\n", "     when fsc.hour_ between 17 and 20 then 'e Evening'\n", "     when fsc.hour_ between 21 and 23 then 'f Night'\n", "     when fsc.hour_ = 0 then 'f Night'\n", "else null end as slot,\n", "\n", "\n", "\n", "-- s.slot_id,\n", "-- s.slot_name,\n", "fsc.pickup_user_id store_id,\n", "sum(fsc.order_count) order_count,\n", "sum(fsc.total_working_hour) total_working_hour,\n", "try(sum(fsc.order_count)*1.0000/sum(fsc.total_working_hour)) hour_util,\n", "sum(fsc.rider_count) partner_count,\n", "COALESCE(sum(b2a_orders),0) b2a_orders,\n", "try(COALESCE(sum(b2a_orders),0)*100.00/sum(bb.order_count)) b2a_orders_perc\n", "\n", "FROM (\n", "    SELECT account_date date_,account_hour hour_,pickup_user_id pickup_user_id,\n", "    sum(orders) order_count,\n", "    sum(login_hr) total_working_hour,count(distinct delivery_driver_id) rider_count FROM base_pre\n", "    GROUP BY 1,2,3\n", ") fsc\n", "LEFT JOIN b2a_base bb on fsc.date_=bb.date_ AND fsc.hour_=bb.hour_ AND fsc.pickup_user_id=bb.z_store_id \n", "left join log_base3 lb3 ON lb3.account_date = fsc.date_ AND lb3.store_id = fsc.pickup_user_id AND lb3.hour = fsc.hour_\n", "-- left join slots s on s.hour = fsc.hour_  and s.store_id = fsc.pickup_user_id\n", "Where fsc.date_ >= (date(current_date - interval '1' day) - interval '60' day)\n", "and lb3.hour is null\n", "-- and fsc.hour_ > 5\n", "GROUP BY 1,2,3,4,5\n", "-- having sum(fsc.order_count)>0\n", ")\n", "\n", "\n", ",\n", "\n", "theo_OPH as (\n", "SELECT \n", "frontend_merchant_id,\n", "\n", "case when a.hour_ between 1 and 3 then 'a Late Night'\n", "     when a.hour_ between 4 and 7 then 'b Early Morning'\n", "     when a.hour_ between 8 and 12 then 'c Morniing'\n", "     when a.hour_ between 13 and 16 then 'd Afternoon'\n", "     when a.hour_ between 17 and 20 then 'e Evening'\n", "     when a.hour_ between 21 and 23 then 'f Night'\n", "     when a.hour_ = 0 then 'f Night'\n", "else null end as slot,\n", "\n", "z_store_id,\n", "SUM(enroute_time)*1.00/sum(order_count) avg_enroute_hr,\n", "sum(order_count)*0.50/sum(enroute_time) theo_rider_OPH\n", "FROM (\n", "    SELECT frontend_merchant_id,extract(hour from order_checkout_ts_ist) hour_,z_store_id,\n", "    case when extract(dow from order_checkout_ts_ist) between 1 AND 5 then 'WEEKDAY' ELSE 'WEEKEND' END as dow,\n", "    count(distinct order_id) order_count,\n", "    sum(date_diff('second',order_enroute_ts_ist,order_delivered_ts_ist))*1.00/3600 enroute_time\n", "    FROM dwh.fact_supply_chain_order_details oh\n", "    INNER JOIN mapping m on cast(oh.frontend_merchant_id as varchar)=m.blinkit_store_id\n", "    WHERE (order_current_status)='DELIVERED'\n", "    AND order_checkout_dt_ist>(current_date - interval '1' day)-interval '60' day\n", "    AND (order_checkout_ts_ist)>=cast((current_date - interval '1' day)-interval '60' day as timestamp)\n", "    GROUP BY 1,2,3,4\n", ") a\n", "-- left join slots s on s.hour = a.hour_ and s.store_id = a.z_store_id\n", "GROUP BY 1,2,3\n", "Having sum(order_count)>0 AND sum(enroute_time)>0),\n", "\n", "DS95 as (WITH ranked_data AS (\n", "    SELECT \n", "        date_,\n", "        slot,\n", "        store_id,\n", "        hour_util,\n", "        b2a_perc_bucket,\n", "        percent_rank() OVER(PARTITION BY slot,store_id,b2a_perc_bucket ORDER BY hour_util ASC) perc_rank,\n", "        ROW_NUMBER() OVER(PARTITION BY slot,store_id,b2a_perc_bucket ORDER BY hour_util ASC) as row_num,\n", "        COUNT(*) OVER(PARTITION BY slot,store_id,b2a_perc_bucket) as total_rows\n", "    FROM (\n", "        SELECT *,\n", "            case when b2a_orders_perc<=3 then '1 DS97'\n", "                 when b2a_orders_perc<=6 then '2 DS94'\n", "                 WHEN b2a_orders_perc>5 AND b2a_orders_perc<=10 then '3 DS90'\n", "                 WHEN b2a_orders_perc>10 AND b2a_orders_perc<=15 then '4 DS85'\n", "                 WHEN b2a_orders_perc>15 AND b2a_orders_perc<=20 then '5 DS80'\n", "                 WHEN b2a_orders_perc <=30 then '6 DS70'\n", "                 WHEN b2a_orders_perc <=40 then '7 DS60'\n", "             ELSE '8 DS<60' END as b2a_perc_bucket\n", "        FROM base\n", "    )\n", ")\n", "\n", "SELECT \n", "    slot,\n", "    store_id,\n", "    \n", "    count(distinct case when b2a_perc_bucket='1 DS97' then date_ END) DS97_instances,\n", "    count(distinct case when b2a_perc_bucket='2 DS94' then date_ END) DS94_instances,\n", "    count(distinct case when b2a_perc_bucket='3 DS90' then date_ END) DS90_instances,\n", "    count(distinct case when b2a_perc_bucket='4 DS85' then date_ END) DS85_instances,\n", "    count(distinct case when b2a_perc_bucket='5 DS80' then date_ END) DS80_instances,\n", "    count(distinct case when b2a_perc_bucket='6 DS70' then date_ END) DS70_instances,\n", "    count(distinct case when b2a_perc_bucket='7 DS60' then date_ END) DS60_instances,\n", "    count(distinct case when b2a_perc_bucket='8 DS<60' then date_ END) DS50_instances,\n", "\n", "    max(case when perc_rank<=0.50 AND b2a_perc_bucket='1 DS97' then hour_util else 0 end) perc_DS97_util,\n", "    max(case when perc_rank<=0.50 AND b2a_perc_bucket='2 DS94' then hour_util else 0 end) perc_DS94_util,\n", "    max(case when perc_rank<=0.50 AND b2a_perc_bucket='3 DS90' then hour_util else 0 end) perc_DS90_util,\n", "    max(case when perc_rank<=0.50 AND b2a_perc_bucket='4 DS85' then hour_util else 0 end) perc_DS85_util,\n", "    max(case when perc_rank<=0.50 AND b2a_perc_bucket='5 DS80' then hour_util else 0 end) perc_DS80_util,\n", "    max(case when perc_rank<=0.50 AND b2a_perc_bucket='6 DS70' then hour_util else 0 end) perc_DS70_util,\n", "    max(case when perc_rank<=0.50 AND b2a_perc_bucket='7 DS60' then hour_util else 0 end) perc_DS60_util,\n", "    max(case when perc_rank<=0.50 AND b2a_perc_bucket='8 DS<60' then hour_util else 0 end) perc_DS50_util,\n", "    \n", "    -- Add date columns for each bucket's median\n", "    MAX(CASE WHEN row_num = CEIL(total_rows/2.0) AND b2a_perc_bucket='1 DS97' THEN date_ END) AS perc_DS97_util_date,\n", "    MAX(CASE WHEN row_num = CEIL(total_rows/2.0) AND b2a_perc_bucket='2 DS94' THEN date_ END) AS perc_DS94_util_date,\n", "    MAX(CASE WHEN row_num = CEIL(total_rows/2.0) AND b2a_perc_bucket='3 DS90' THEN date_ END) AS perc_DS90_util_date,\n", "    MAX(CASE WHEN row_num = CEIL(total_rows/2.0) AND b2a_perc_bucket='4 DS85' THEN date_ END) AS perc_DS85_util_date,\n", "    MAX(CASE WHEN row_num = CEIL(total_rows/2.0) AND b2a_perc_bucket='5 DS80' THEN date_ END) AS perc_DS80_util_date,\n", "    MAX(CASE WHEN row_num = CEIL(total_rows/2.0) AND b2a_perc_bucket='6 DS70' THEN date_ END) AS perc_DS70_util_date,\n", "    MAX(CASE WHEN row_num = CEIL(total_rows/2.0) AND b2a_perc_bucket='7 DS60' THEN date_ END) AS perc_DS60_util_date,\n", "    MAX(CASE WHEN row_num = CEIL(total_rows/2.0) AND b2a_perc_bucket='8 DS<60' THEN date_ END) AS perc_DS50_util_date\n", "\n", "FROM ranked_data\n", "GROUP BY 1,2),\n", "\n", "ds_base as (\n", "SELECT \n", "c.blinkit_store_name,\n", "c.runnr_city,\n", "a.*,b.login_hr last_week_logins,\n", "b.orders orders_last_week,\n", "(DS90_instances + DS85_instances + DS80_instances + DS70_instances + DS60_instances + DS94_instances + DS97_instances + DS50_instances) as total_instances\n", "FROM DS95 a\n", "LEFT JOIN base_pre_login b on a.slot = b.slot and a.store_id = b.pickup_user_id\n", "INNER JOIN logistics_data_etls.blinkit_store_mapping c on a.store_id = c.store_id\n", "WHERE c.is_active=1),\n", "\n", "last_week_dh as (\n", "select\n", "week_start_date, slot, frontend_merchant_id, try(sum(direct_orders)*1.00/sum(delivered_orders)) as last_week_DH\n", "from (\n", "    Select \n", "    date_trunc('week', order_checkout_ts_ist) as week_start_date,\n", "\n", "\n", "case when extract(hour from order_checkout_ts_ist) between 1 and 3 then 'a Late Night'\n", "     when extract(hour from order_checkout_ts_ist) between 4 and 7 then 'b Early Morning'\n", "     when extract(hour from order_checkout_ts_ist) between 8 and 12 then 'c Morniing'\n", "     when extract(hour from order_checkout_ts_ist) between 13 and 16 then 'd Afternoon'\n", "     when extract(hour from order_checkout_ts_ist) between 17 and 20 then 'e Evening'\n", "     when extract(hour from order_checkout_ts_ist) between 21 and 23 then 'f Night'\n", "     when extract(hour from order_checkout_ts_ist) = 0 then 'f Night'\n", "else null end as slot,\n", "\n", "    a.frontend_merchant_id,\n", "    count(distinct a.order_id) as delivered_orders,\n", "    count(distinct case when date_diff('second' ,order_billing_completed_ts_ist,order_partner_assigned_ts_ist)<=0 then a.order_id end) direct_orders\n", "    from dwh.fact_supply_chain_order_details a\n", "    INNER JOIN mapping m on cast(a.frontend_merchant_id as varchar)=m.blinkit_store_id\n", "    where order_checkout_dt_ist >= (current_date - interval '1' day) - interval '15' day\n", "    and date_trunc('week', order_checkout_ts_ist) = date_trunc('week', (current_date - interval '1' day) - interval '7' day)\n", "    and a.order_current_status = 'DELIVERED'\n", "    group by 1,2,3\n", ") a\n", "-- left join slots s on s.hour = a.hour and s.store_id = a.z_store_id\n", "group by 1,2,3),\n", "\n", "last_week_oph as (\n", "select\n", "date_trunc('week',account_date) as week_start_date,\n", "\n", "case when account_hour between 1 and 3 then 'a Late Night'\n", "     when account_hour between 4 and 7 then 'b Early Morning'\n", "     when account_hour between 8 and 12 then 'c Morniing'\n", "     when account_hour between 13 and 16 then 'd Afternoon'\n", "     when account_hour between 17 and 20 then 'e Evening'\n", "     when account_hour between 21 and 23 then 'f Night'\n", "     when account_hour = 0 then 'f Night'\n", "else null end as slot,\n", "\n", "\n", "pickup_user_id,\n", "sum(orders) orders,\n", "sum(login_hr) login_hr,\n", "sum(orders)*1.00/sum(login_hr) as last_week_oph\n", "from base_pre\n", "where account_date >= (current_date - interval '1' day) - interval '15' day\n", "and date_trunc('week',account_date) = date_trunc('week', (current_date - interval '1' day) - interval '7' day)\n", "group by 1,2,3),\n", "\n", "final as (\n", "SELECT \n", "db.blinkit_store_name,\n", "db.runnr_city,\n", "db.slot,\n", "-- db.slot_name,\n", "db.store_id,\n", "last_week_logins,\n", "orders_last_week,\n", "sm.blinkit_store_id,\n", "perc_ds97_util,\n", "perc_ds94_util,\n", "perc_ds90_util,\n", "perc_ds85_util,\n", "perc_ds80_util,\n", "perc_ds70_util,\n", "perc_ds60_util,\n", "perc_ds50_util,\n", "\n", "ds97_instances,\n", "ds94_instances,\n", "ds90_instances,\n", "ds85_instances,\n", "ds80_instances,\n", "ds70_instances,\n", "ds60_instances,\n", "ds50_instances,\n", "\n", "total_instances,\n", "\n", "theo_rider_OPH theo_rider_overall_oph,\n", "\n", "case when DS85_instances >= 4 or (total_instances <= 10 and DS85_instances >=1) then perc_DS85_util\n", "     when DS80_instances >= 4 or (total_instances <= 10 and DS80_instances >=1) then perc_DS80_util\n", "     when DS70_instances >= 4 or (total_instances <= 10 and DS70_instances >=1) then perc_DS70_util\n", "     when DS60_instances >= 4 or (total_instances <= 10 and DS60_instances >=1) then perc_DS60_util\n", "     when DS90_instances >= 4 or (total_instances <= 10 and DS90_instances >=1) then perc_DS90_util\n", "     when DS94_instances >= 4 or (total_instances <= 10 and DS94_instances >=1) then perc_DS94_util\n", "     when DS97_instances >= 4 or (total_instances <= 10 and DS97_instances >=1) then perc_DS97_util\n", "     \n", "     when DS85_instances >= 1 then perc_DS85_util\n", "     when DS80_instances >= 1 then perc_DS80_util\n", "     when DS70_instances >= 1 then perc_DS70_util\n", "     when DS60_instances >= 1 then perc_DS60_util\n", "     when DS90_instances >= 1 then perc_DS90_util\n", "     when DS94_instances >= 1 then perc_DS94_util\n", "     when DS97_instances >= 1 then perc_DS97_util\n", "     when perc_DS50_util is not null then perc_DS50_util\n", "     \n", "else 2 end as OPH,\n", "\n", "case when DS85_instances >= 4 or (total_instances <= 10 and DS85_instances >=1) then perc_DS85_util_date\n", "     when DS80_instances >= 4 or (total_instances <= 10 and DS80_instances >=1) then perc_DS80_util_date\n", "     when DS70_instances >= 4 or (total_instances <= 10 and DS70_instances >=1) then perc_DS70_util_date\n", "     when DS60_instances >= 4 or (total_instances <= 10 and DS60_instances >=1) then perc_DS60_util_date\n", "     when DS90_instances >= 4 or (total_instances <= 10 and DS90_instances >=1) then perc_DS90_util_date\n", "     when DS94_instances >= 4 or (total_instances <= 10 and DS94_instances >=1) then perc_DS94_util_date\n", "     when DS97_instances >= 4 or (total_instances <= 10 and DS97_instances >=1) then perc_DS97_util_date\n", "     \n", "     when DS85_instances >= 1 then perc_DS85_util_date\n", "     when DS80_instances >= 1 then perc_DS80_util_date\n", "     when DS70_instances >= 1 then perc_DS70_util_date\n", "     when DS60_instances >= 1 then perc_DS60_util_date\n", "     when DS90_instances >= 1 then perc_DS90_util_date\n", "     when DS94_instances >= 1 then perc_DS94_util_date\n", "     when DS97_instances >= 1 then perc_DS97_util_date\n", "     \n", "else perc_DS50_util_date end as OPH_date,\n", "\n", "last_week_dh,\n", "last_week_oph\n", "\n", "FROM ds_base  DB \n", "left join logistics_data_etls.blinkit_store_mapping sm on sm.store_id = db.store_id\n", "LEFT JOIN theo_oph to on to.frontend_merchant_id = sm.blinkit_store_id and to.slot = db.slot\n", "left join last_week_dh l on l.frontend_merchant_id = sm.blinkit_store_id and l.slot = db.slot\n", "left join last_week_oph o on o.pickup_user_id = db.store_id and o.slot = db.slot\n", "\n", "GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29)\n", "\n", "\n", "select *,\n", "\n", "case when OPH = perc_DS97_util then 1.16\n", "     when OPH = perc_DS94_util then 1.11\n", "     when OPH = perc_DS90_util then 1.05\n", "     when OPH = perc_DS85_util then 1.00\n", "     when OPH = perc_DS80_util then 0.95\n", "     when OPH = perc_DS70_util then 0.89\n", "     when OPH = perc_DS60_util then 0.85\n", "     when OPH = perc_DS50_util then 0.81\n", "else 1.0 end as multiplier,\n", "\n", "case when OPH = perc_DS97_util then 1.16*OPH\n", "     when OPH = perc_DS94_util then 1.11*OPH\n", "     when OPH = perc_DS90_util then 1.05*OPH\n", "     when OPH = perc_DS85_util then 1.00*OPH\n", "     when OPH = perc_DS80_util then 0.95*OPH\n", "     when OPH = perc_DS70_util then 0.89*OPH\n", "     when OPH = perc_DS60_util then 0.85*OPH\n", "     when OPH = perc_DS50_util then 0.81*OPH\n", "else OPH end as optimal_oph\n", "\n", "\n", "from final\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "ad903d67-eeac-481b-bf13-dc3cc74574b1", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"oph_4_hour_85\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"blinkit_store_name\", \"type\": \"varchar\", \"description\": \"blinkit_store_name\"},\n", "        {\"name\": \"runnr_city\", \"type\": \"varchar\", \"description\": \"runnr_city\"},\n", "        {\"name\": \"slot\", \"type\": \"varchar\", \"description\": \"slot\"},\n", "        {\"name\": \"store_id\", \"type\": \"integer\", \"description\": \"store_id\"},\n", "        {\"name\": \"last_week_logins\", \"type\": \"double\", \"description\": \"logins_last_week\"},\n", "        {\"name\": \"orders_last_week\", \"type\": \"double\", \"description\": \"orders_last_week\"},\n", "        {\"name\": \"blinkit_store_id\", \"type\": \"integer\", \"description\": \"blinkit_store_id\"},\n", "        {\"name\": \"perc_DS97_util\", \"type\": \"double\", \"description\": \"perc_ds97_util\"},\n", "        {\"name\": \"perc_DS94_util\", \"type\": \"double\", \"description\": \"perc_ds94_util\"},\n", "        {\"name\": \"perc_DS90_util\", \"type\": \"double\", \"description\": \"perc_ds90_util\"},\n", "        {\"name\": \"perc_DS85_util\", \"type\": \"double\", \"description\": \"perc_ds85_util\"},\n", "        {\"name\": \"perc_DS80_util\", \"type\": \"double\", \"description\": \"perc_ds80_util\"},\n", "        {\"name\": \"perc_DS70_util\", \"type\": \"double\", \"description\": \"perc_ds70_util\"},\n", "        {\"name\": \"perc_DS60_util\", \"type\": \"double\", \"description\": \"perc_ds60_util\"},\n", "        {\"name\": \"perc_DS50_util\", \"type\": \"double\", \"description\": \"perc_ds50_util\"},\n", "        {\"name\": \"DS97_instances\", \"type\": \"integer\", \"description\": \"ds97_instances\"},\n", "        {\"name\": \"DS94_instances\", \"type\": \"integer\", \"description\": \"ds94_instances\"},\n", "        {\"name\": \"DS90_instances\", \"type\": \"integer\", \"description\": \"ds90_instances\"},\n", "        {\"name\": \"DS85_instances\", \"type\": \"integer\", \"description\": \"ds85_instances\"},\n", "        {\"name\": \"DS80_instances\", \"type\": \"integer\", \"description\": \"ds80_instances\"},\n", "        {\"name\": \"DS70_instances\", \"type\": \"integer\", \"description\": \"ds70_instances\"},\n", "        {\"name\": \"DS60_instances\", \"type\": \"integer\", \"description\": \"ds60_instances\"},\n", "        {\"name\": \"DS50_instances\", \"type\": \"integer\", \"description\": \"ds50_instances\"},\n", "        {\"name\": \"total_instances\", \"type\": \"integer\", \"description\": \"total_instances\"},\n", "        {\n", "            \"name\": \"theo_rider_overall_OPH\",\n", "            \"type\": \"double\",\n", "            \"description\": \"theo_rider_overall_oph\",\n", "        },\n", "        {\"name\": \"OPH\", \"type\": \"double\", \"description\": \"oph\"},\n", "        {\"name\": \"OPH_date\", \"type\": \"date\", \"description\": \"oph_date\"},\n", "        {\"name\": \"last_week_dh\", \"type\": \"double\", \"description\": \"dh_last_week\"},\n", "        {\"name\": \"last_week_oph\", \"type\": \"double\", \"description\": \"oph_last_week\"},\n", "        {\"name\": \"multiplier\", \"type\": \"double\", \"description\": \"multiplier\"},\n", "        {\"name\": \"optimal_oph\", \"type\": \"double\", \"description\": \"optimal_oph\"},\n", "    ],\n", "    \"primary_key\": [\"store_id\", \"slot\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"This table contains the ideal OPH for stores at multiple slot level at 90-95 and 85-90 DH\",\n", "}\n", "pb.to_trino(sql_4_hour_1, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "88893d41-0010-417b-b89b-c1eae3dc29dc", "metadata": {}, "outputs": [], "source": ["sql_4_hour_2 = \"\"\"\n", "\n", "with btpo_base_day_slot as (\n", "\n", "with mapping as (\n", "select z_store_id, blinkit_store_id, store_name\n", "from (\n", "    select distinct id as z_store_id, external_id as blinkit_store_id, name as store_name,\n", "    rank() over (partition by id order by updated_at desc) as rnk\n", "    from zomato.carthero_prod.users where type = 'merchant' and source_id = 17110 \n", "    and (created_at) >= cast(date('2019-05-01') as timestamp)\n", ") where rnk=1\n", "group by 1,2,3\n", "),\n", "\n", "base as\n", "(   \n", "  Select \n", "   date_,\n", "   date_trunc('week',date_) as week_start_date,\n", "   hour_,\n", "   frontend_merchant_id,\n", "   sum(log_orders) log_orders,\n", "   sum(delivered_orders) delivered_orders,\n", "   sum(direct_handover_orders) direct_handover_orders,\n", "   sum(store_handshake_time) store_handshake_time,\n", "   sum(enroute_to_doorstep) enroute_to_doorstep,\n", "   sum(doorstep_to_delivered) doorstep_to_delivered\n", "   from logistics_data_etls.hourly_supply_metrics_etl\n", "   where date_ >= current_date - interval '65' day\n", "   group by 1,2,3,4\n", " ),\n", " \n", " trip as \n", "(\n", "  Select trip_id as trip_id_,accounting_time\n", "  from logistics_data_etls.delivery_trip_payout\n", "  where dt>=date_format(current_date - interval '65' day, '%%Y%%m%%d')\n", "  group by 1,2\n", "),\n", " \n", " fact as \n", " (\n", "    SELECT frontend_merchant_id,\n", "    date(order_checkout_ts_ist) date_,\n", "    extract(hour from order_checkout_ts_ist) hour_,\n", "    sum(case when order_partner_assigned_ts_ist < order_billing_completed_ts_ist then \n", "                      (date_diff('second',order_partner_assigned_ts_ist,order_billing_completed_ts_ist)) end) wait_time\n", "    FROM dwh.fact_supply_chain_order_details oh\n", "    INNER JOIN mapping m on cast(oh.frontend_merchant_id as varchar)=m.blinkit_store_id\n", "    WHERE (order_current_status)='DELIVERED'\n", "    AND order_checkout_dt_ist>date(current_date - interval '1' day)-interval '65' day\n", "    AND (order_checkout_ts_ist)>=cast(date(current_date - interval '1' day)-interval '65' day as timestamp)\n", "    GROUP BY 1,2,3\n", "),\n", " \n", " \n", " btpo_base as \n", " (\n", "    select \n", "    date,\n", "    extract(hour from cast(coalesce(cast(accounting_time as varchaR),ts) as timestamp)) as hour,\n", "    cast(blinkit_store_id as int) as blinkit_store_id,\n", "    store_name,\n", "    city_name,\n", "    coalesce(sum(case when lead_event!='Logout' then return_time end),0) as total_return_time,\n", "    coalesce(sum(idle_time),0) as total_idle_time,\n", "    date_trunc('week',cast(date as date)) as week_start\n", "   from  serviceability_etls.btpo_rider_events_v2 as b\n", "   left join trip t on cast(t.trip_id_ as varchar) = b.trip_id\n", "   WHERE store_name NOT LIKE 'NA'\n", "   and date >= cast(current_date - interval '65' day as varchar)\n", "   group by 1,2,3,4,5\n", "   order by 1,2\n", "   )\n", "   \n", "   \n", "   select \n", "   date,\n", "--      case when hour between 1 and 6 then 'a Late Night + Early Morning'\n", "--      when hour between 7 and 12 then 'b Morning'\n", "--      when hour between 13 and 18 then 'c Afternoon + Evening'\n", "--      when hour between 19 and 23 then 'd Night'\n", "--      when hour = 0 then 'd Night'\n", "--   else null end as slot,\n", "   \n", "case when hour between 1 and 3 then 'a Late Night'\n", "     when hour between 4 and 7 then 'b Early Morning'\n", "     when hour between 8 and 12 then 'c <PERSON><PERSON>iing'\n", "     when hour between 13 and 16 then 'd Afternoon'\n", "     when hour between 17 and 20 then 'e Evening'\n", "     when hour between 21 and 23 then 'f Night'\n", "     when hour = 0 then 'f Night'\n", "else null end as slot,\n", "\n", "\n", "   city_name,\n", "   blinkit_store_name,\n", "   blinkit_store_id,\n", "   sum(log_orders) as log_orders,\n", "   sum(delivered_orders) as delivered_orders,\n", "           sum(store_handshake_time) store_handshake_time,\n", "        sum(enroute_to_doorstep) enroute_to_doorstep,\n", "        sum(doorstep_to_delivered) doorstep_to_delivered,\n", "        sum(total_return_time) total_return_time,\n", "        sum(total_idle_time) total_idle_time,\n", "        sum(wait_time) wait_time\n", "        \n", "        from (\n", "   Select bt.blinkit_store_id,\n", "        sm.runnr_city as city_name,\n", "        date_trunc('week',date(bt.date)) as week_start_date,\n", "        extract(week from date(bt.date)) as wk,\n", "        extract(dow from date(bt.date)) as dow,\n", "        date(bt.date) as date,\n", "        blinkit_store_name,\n", "        store_id,\n", "        bt.hour,\n", "        coalesce(log_orders,0) log_orders,\n", "        coalesce(delivered_orders,0) delivered_orders,\n", "        coalesce(store_handshake_time,0) store_handshake_time,\n", "        coalesce(enroute_to_doorstep,0) enroute_to_doorstep,\n", "        coalesce(doorstep_to_delivered,0) doorstep_to_delivered,\n", "        coalesce(bt.total_return_time,0) total_return_time,\n", "        coalesce(bt.total_idle_time,0) total_idle_time,\n", "        coalesce(f.wait_time,0) wait_time\n", "     from btpo_base bt\n", "     left join base b on b.frontend_merchant_id = bt.blinkit_store_id and b.hour_ = bt.hour and cast(b.date_ as varchar) = bt.date\n", "     left join fact f on f.frontend_merchant_id = bt.blinkit_store_id and bt.hour = f.hour_ and bt.date = cast(f.date_ as varchar)\n", "     left join logistics_data_etls.blinkit_store_mapping sm on sm.blinkit_store_id = bt.blinkit_store_id\n", "     )\n", "     group by 1,2,3,4,5\n", "),\n", "\n", "btpo_base_week as (\n", "select \n", "date_trunc('week',date) as week_start_date,\n", "city_name,\n", "blinkit_store_name,\n", "blinkit_store_id,\n", "sum(log_orders) as log_orders,\n", "sum(delivered_orders) as delivered_orders,\n", "sum(store_handshake_time) store_handshake_time,\n", "sum(enroute_to_doorstep) enroute_to_doorstep,\n", "sum(doorstep_to_delivered) doorstep_to_delivered,\n", "sum(total_return_time) total_return_time,\n", "sum(total_idle_time) total_idle_time,\n", "sum(wait_time) wait_time\n", "from\n", "btpo_base_day_slot\n", "group by 1,2,3,4\n", ")\n", "\n", "\n", "select \n", "a.*,\n", "b.log_orders log_orders_oph_day_slot,\n", "b.delivered_orders delivered_orders_oph_day_slot,\n", "b.store_handshake_time store_handshake_time_oph_day_slot,\n", "b.enroute_to_doorstep enroute_to_doorstep_oph_day_slot,\n", "b.doorstep_to_delivered doorstep_to_delivered_oph_day_slot,\n", "b.total_return_time total_return_time_oph_day_slot,\n", "b.total_idle_time total_idle_time_oph_day_slot,\n", "b.wait_time wait_time_oph_day_slot,\n", "(b.store_handshake_time + b.enroute_to_doorstep +  b.doorstep_to_delivered +\n", "b.total_return_time + b.total_idle_time + b.wait_time) total_time_oph_day_slot,\n", "try(((b.store_handshake_time + b.enroute_to_doorstep + b.doorstep_to_delivered +\n", "b.total_return_time + b.total_idle_time + b.wait_time)*1.0000 / (b.log_orders))) as total_time_avg_oph_day_slot,\n", "try((3600.0000)/(((b.store_handshake_time + b.enroute_to_doorstep + b.doorstep_to_delivered +\n", "b.total_return_time + b.total_idle_time + b.wait_time)*1.0000 / (b.log_orders)))) as theo_util_oph_day_slot,\n", "\n", "\n", "c.log_orders log_orders_oph_week,\n", "c.delivered_orders delivered_orders_oph_week,\n", "c.store_handshake_time store_handshake_time_oph_week,\n", "c.enroute_to_doorstep enroute_to_doorstep_oph_week,\n", "c.doorstep_to_delivered doorstep_to_delivered_oph_week,\n", "c.total_return_time total_return_time_oph_week,\n", "c.total_idle_time total_idle_time_oph_week,\n", "c.wait_time wait_time_oph_week,\n", "try(((c.store_handshake_time + c.enroute_to_doorstep + c.doorstep_to_delivered +\n", "c.total_return_time + c.total_idle_time + c.wait_time)*1.0000 / (c.log_orders))) as total_time_avg_oph_week,\n", "try((3600.0000)/(((c.store_handshake_time + c.enroute_to_doorstep + c.doorstep_to_delivered +\n", "c.total_return_time + c.total_idle_time + c.wait_time)*1.0000 / (c.log_orders)))) as theo_util_oph_week\n", "\n", "\n", "from\n", "interim.oph_4_hour_85 a\n", "left join btpo_base_day_slot b on a.blinkit_store_id = b.blinkit_store_id and a.slot = b.slot and a.oph_date = b.date\n", "left join btpo_base_week c on a.blinkit_store_id = c.blinkit_store_id and date_trunc('week',a.oph_date) = c.week_start_date\n", "\n", "     \n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "39a4ab7a-0759-4d3a-a9c5-613f5708abf7", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"final_oph_4_hour_85\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"blinkit_store_name\", \"type\": \"varchar\", \"description\": \"blinkit_store_name\"},\n", "        {\"name\": \"runnr_city\", \"type\": \"varchar\", \"description\": \"runnr_city\"},\n", "        {\"name\": \"slot\", \"type\": \"varchar\", \"description\": \"slot\"},\n", "        {\"name\": \"store_id\", \"type\": \"integer\", \"description\": \"store_id\"},\n", "        {\"name\": \"last_week_logins\", \"type\": \"double\", \"description\": \"logins_last_week\"},\n", "        {\"name\": \"orders_last_week\", \"type\": \"double\", \"description\": \"orders_last_week\"},\n", "        {\"name\": \"blinkit_store_id\", \"type\": \"integer\", \"description\": \"blinkit_store_id\"},\n", "        {\"name\": \"perc_DS97_util\", \"type\": \"double\", \"description\": \"perc_ds97_util\"},\n", "        {\"name\": \"perc_DS94_util\", \"type\": \"double\", \"description\": \"perc_ds94_util\"},\n", "        {\"name\": \"perc_DS90_util\", \"type\": \"double\", \"description\": \"perc_ds90_util\"},\n", "        {\"name\": \"perc_DS85_util\", \"type\": \"double\", \"description\": \"perc_ds85_util\"},\n", "        {\"name\": \"perc_DS80_util\", \"type\": \"double\", \"description\": \"perc_ds80_util\"},\n", "        {\"name\": \"perc_DS70_util\", \"type\": \"double\", \"description\": \"perc_ds70_util\"},\n", "        {\"name\": \"perc_DS60_util\", \"type\": \"double\", \"description\": \"perc_ds60_util\"},\n", "        {\"name\": \"perc_DS50_util\", \"type\": \"double\", \"description\": \"perc_ds50_util\"},\n", "        {\"name\": \"DS97_instances\", \"type\": \"integer\", \"description\": \"ds97_instances\"},\n", "        {\"name\": \"DS94_instances\", \"type\": \"integer\", \"description\": \"ds94_instances\"},\n", "        {\"name\": \"DS90_instances\", \"type\": \"integer\", \"description\": \"ds90_instances\"},\n", "        {\"name\": \"DS85_instances\", \"type\": \"integer\", \"description\": \"ds85_instances\"},\n", "        {\"name\": \"DS80_instances\", \"type\": \"integer\", \"description\": \"ds80_instances\"},\n", "        {\"name\": \"DS70_instances\", \"type\": \"integer\", \"description\": \"ds70_instances\"},\n", "        {\"name\": \"DS60_instances\", \"type\": \"integer\", \"description\": \"ds60_instances\"},\n", "        {\"name\": \"DS50_instances\", \"type\": \"integer\", \"description\": \"ds50_instances\"},\n", "        {\"name\": \"total_instances\", \"type\": \"integer\", \"description\": \"total_instances\"},\n", "        {\n", "            \"name\": \"theo_rider_overall_OPH\",\n", "            \"type\": \"double\",\n", "            \"description\": \"theo_rider_overall_oph\",\n", "        },\n", "        {\"name\": \"OPH\", \"type\": \"double\", \"description\": \"oph\"},\n", "        {\"name\": \"OPH_date\", \"type\": \"date\", \"description\": \"oph_date\"},\n", "        {\"name\": \"last_week_dh\", \"type\": \"double\", \"description\": \"dh_last_week\"},\n", "        {\"name\": \"last_week_oph\", \"type\": \"double\", \"description\": \"oph_last_week\"},\n", "        {\"name\": \"multiplier\", \"type\": \"double\", \"description\": \"multiplier\"},\n", "        {\"name\": \"optimal_oph\", \"type\": \"double\", \"description\": \"optimal_oph\"},\n", "        {\n", "            \"name\": \"log_orders_oph_day_slot\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"log_orders_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"delivered_orders_oph_day_slot\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"delivered_orders_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"store_handshake_time_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"store_handshake_time_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"enroute_to_doorstep_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"enroute_to_doorstep_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"doorstep_to_delivered_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"doorstep_to_delivered_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"total_return_time_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_return_time_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"total_idle_time_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_idle_time_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"wait_time_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"wait_time_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"total_time_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_time_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"total_time_avg_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_time_avg_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"theo_util_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"theo_util_oph_day_slot\",\n", "        },\n", "        {\"name\": \"log_orders_oph_week\", \"type\": \"integer\", \"description\": \"log_orders_oph_week\"},\n", "        {\n", "            \"name\": \"delivered_orders_oph_week\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"delivered_orders_oph_week\",\n", "        },\n", "        {\n", "            \"name\": \"store_handshake_time_oph_week\",\n", "            \"type\": \"double\",\n", "            \"description\": \"store_handshake_time_oph_week\",\n", "        },\n", "        {\n", "            \"name\": \"enroute_to_doorstep_oph_week\",\n", "            \"type\": \"double\",\n", "            \"description\": \"enroute_to_doorstep_oph_week\",\n", "        },\n", "        {\n", "            \"name\": \"doorstep_to_delivered_oph_week\",\n", "            \"type\": \"double\",\n", "            \"description\": \"doorstep_to_delivered_oph_week\",\n", "        },\n", "        {\n", "            \"name\": \"total_return_time_oph_week\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_return_time_oph_week\",\n", "        },\n", "        {\n", "            \"name\": \"total_idle_time_oph_week\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_idle_time_oph_week\",\n", "        },\n", "        {\"name\": \"wait_time_oph_week\", \"type\": \"double\", \"description\": \"wait_time_oph_week\"},\n", "        {\n", "            \"name\": \"total_time_avg_oph_week\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_time_avg_oph_week\",\n", "        },\n", "        {\"name\": \"theo_util_oph_week\", \"type\": \"double\", \"description\": \"theo_util_oph_week\"},\n", "    ],\n", "    \"primary_key\": [\"store_id\", \"slot\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"This table contains the ideal OPH for stores at multiple slot level at 90-95 and 85-90 DH\",\n", "}\n", "pb.to_trino(sql_4_hour_2, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "fa48ddb9-2a5b-4134-b7ab-af71811b8bfb", "metadata": {}, "outputs": [], "source": ["sql_4_hour_3 = \"\"\"\n", "\n", "with delivery_distance as (\n", "select\n", "extract(week from account_date) as week_number,\n", "date_trunc('week',account_date) as week_start_date,\n", "blinkit_store_id,\n", "blinkit_store_name,\n", "sum(delivered_orders_distance) as delivered_distance,\n", "sum(delivered_orders) as delivered_orders,\n", "try(sum(delivered_orders_distance) / sum(delivered_orders)) as average_dd\n", "from\n", "logistics_data_etls.blinkit_rider_daily_accounting_summary_v1\n", "where account_date >= current_date - interval '70' day\n", "group by 1,2,3,4\n", "),\n", "\n", "final_base as (\n", "select *,\n", "try(total_idle_time_oph_day_slot*100.00/total_time_oph_day_slot) as idle_time_ratio_oph_day_slot,\n", "\n", "(0.35* (store_handshake_time_oph_day_slot + doorstep_to_delivered_oph_day_slot +  total_return_time_oph_day_slot\n", "+ enroute_to_doorstep_oph_day_slot + wait_time_oph_day_slot ) / 0.65) as ideal_idle_time_oph_day_slot,\n", "\n", "\n", "try((3600.0000)/(((store_handshake_time_oph_week + doorstep_to_delivered_oph_week +\n", "(total_return_time_oph_week + enroute_to_doorstep_oph_week)*\n", "(final_dd_correction_factor)\n", "+ total_idle_time_oph_week + wait_time_oph_week)*1.0000 / (log_orders_oph_week)))) as theo_util_oph_week_dd_corrected,\n", "\n", "try((3600.0000) / (((1.0000* (store_handshake_time_oph_day_slot + doorstep_to_delivered_oph_day_slot +  total_return_time_oph_day_slot\n", "+ enroute_to_doorstep_oph_day_slot + wait_time_oph_day_slot ) / 0.65)) *1.0000 / (log_orders_oph_day_slot))) as theo_util_ideal_oph_day_slot\n", "\n", "from\n", "(\n", "\n", "select \n", "a.*,\n", "\n", "ddl.average_dd dd_avg_last_week,\n", "dd.average_dd dd_avg_oph_week,\n", "\n", "case when try((ddl.average_dd * 1.0000 / dd.average_dd)) >= 1.30 then 1.30\n", "     when try((ddl.average_dd * 1.0000 / dd.average_dd)) <= 0.70 then 0.70\n", "else try((ddl.average_dd * 1.0000 / dd.average_dd)) end as final_dd_correction_factor\n", "\n", "\n", "from\n", "interim.final_oph_4_hour_85 a\n", "left join delivery_distance ddl on ddl.blinkit_store_id = a.blinkit_store_id and ddl.week_start_date = date_trunc('week',(current_date - interval '1' day)) - interval '7' day\n", "left join delivery_distance dd on a.blinkit_store_id = dd.blinkit_store_id and date_trunc('week',a.OPH_date) = dd.week_start_date\n", ")\n", "),\n", "\n", "\n", "final as (\n", "\n", "select *, extract(week from OPH_date) as oph_week_number,\n", "date_trunc('week',OPH_date) as oph_week_start_date,\n", "\n", "try((dd_avg_last_week)*1.0000/(dd_avg_oph_week)) as dd_correction_factor,\n", "\n", "try(abs(theo_util_oph_week_dd_corrected - theo_util_oph_week)*1.0000/theo_util_oph_week) as perc_change_theo_oph_dd,\n", "\n", "try((theo_util_ideal_oph_day_slot - theo_util_oph_day_slot)*1.0000/theo_util_oph_day_slot) as perc_change_theo_oph_it\n", "\n", "from final_base\n", "),\n", "\n", "final_dd_correction as (\n", "select *,\n", "case when final_dd_correction_factor >= 1.10 then optimal_oph * (1 + perc_change_theo_oph_dd) \n", "     when final_dd_correction_factor <= 0.90 then optimal_oph * (1 - perc_change_theo_oph_dd)\n", "     else optimal_oph\n", "end as optimal_oph_corrected_dd\n", "from\n", "final\n", ")\n", "\n", "select \n", "    blinkit_store_name,\n", "    runnr_city,\n", "    slot,\n", "    store_id,\n", "    last_week_logins logins_last_week,\n", "    orders_last_week,\n", "    blinkit_store_id,\n", "    perc_DS97_util,\n", "    perc_DS94_util,\n", "    perc_DS90_util,\n", "    perc_DS85_util,\n", "    perc_DS80_util,\n", "    perc_DS70_util,\n", "    perc_DS60_util,\n", "    perc_DS50_util,\n", "    DS97_instances,\n", "    DS94_instances,\n", "    DS90_instances,\n", "    DS85_instances,\n", "    DS80_instances,\n", "    DS70_instances,\n", "    DS60_instances,\n", "    DS50_instances,\n", "    theo_rider_overall_OPH,\n", "    OPH,\n", "    OPH_date,\n", "    last_week_dh dh_last_week,\n", "    last_week_oph oph_last_week,\n", "    dd_avg_last_week,\n", "    dd_avg_oph_week,\n", "    log_orders_oph_week,\n", "    delivered_orders_oph_week,\n", "    store_handshake_time_oph_week,\n", "    enroute_to_doorstep_oph_week,\n", "    doorstep_to_delivered_oph_week,\n", "    total_return_time_oph_week,\n", "    total_idle_time_oph_week,\n", "    wait_time_oph_week,\n", "    total_time_avg_oph_week,\n", "    theo_util_oph_week,\n", "    log_orders_oph_day_slot,\n", "    delivered_orders_oph_day_slot,\n", "    store_handshake_time_oph_day_slot,\n", "    enroute_to_doorstep_oph_day_slot,\n", "    doorstep_to_delivered_oph_day_slot,\n", "    total_return_time_oph_day_slot,\n", "    total_idle_time_oph_day_slot,\n", "    wait_time_oph_day_slot,\n", "    total_time_oph_day_slot,\n", "    total_time_avg_oph_day_slot,\n", "    theo_util_oph_day_slot,\n", "    final_dd_correction_factor,\n", "    null as dh_oph_day_slot,\n", "    idle_time_ratio_oph_day_slot,\n", "    ideal_idle_time_oph_day_slot,\n", "    theo_util_oph_week_dd_corrected,\n", "    theo_util_ideal_oph_day_slot,\n", "    oph_week_number,\n", "    oph_week_start_date,\n", "    dd_correction_factor,\n", "    perc_change_theo_oph_dd,\n", "    perc_change_theo_oph_it,\n", "    multiplier,\n", "    optimal_oph,\n", "    optimal_oph_corrected_dd,\n", "    final_idle_time_correction_factor,\n", "    optimal_oph_corrected_idle_time,\n", "    slot_level,\n", "    Final_OPH_dh_bucket,\n", "    updated_on\n", "    \n", "    from\n", "    (\n", "select *,\n", "case when idle_time_ratio_oph_day_slot >= 35 then least(((1 + perc_change_theo_oph_it)/multiplier),1.40)\n", "else 1 end as final_idle_time_correction_factor,\n", "coalesce(greatest(least(case when idle_time_ratio_oph_day_slot >= 35 then optimal_oph_corrected_dd * least(((1 + perc_change_theo_oph_it)/multiplier),1.40) \n", "     else optimal_oph_corrected_dd\n", "end,4),0.5),2) as optimal_oph_corrected_idle_time,\n", "\n", "'4_hour_slot' as slot_level,\n", "'85-90' as Final_OPH_dh_bucket,\n", "current_date as updated_on\n", "\n", "from final_dd_correction\n", ")\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "b00546f7-4a38-4271-b0b3-9a637825d400", "metadata": {}, "outputs": [], "source": ["four_hour_85 = pd.read_sql_query(sql_4_hour_3, conn)"]}, {"cell_type": "code", "execution_count": null, "id": "c2933317-366f-4e4d-9756-64feea3c2364", "metadata": {}, "outputs": [], "source": ["sql_full_day_1 = \"\"\"\n", "\n", "with mapping as (\n", "select distinct z_store_id, blinkit_store_id, store_name\n", "from (\n", "    select distinct id as z_store_id, external_id as blinkit_store_id, name as store_name,\n", "    rank() over (partition by id order by updated_at desc) as rnk\n", "    from zomato.carthero_prod.users where type = 'merchant' and source_id = 17110 \n", "    and (created_at) >= cast(date('2019-05-01') as timestamp)\n", ") where rnk=1),\n", "\n", "lost_orders_base as (\n", "select \n", "ss.*, m.z_store_id store_id \n", "from zomato.jumbo_external.surge_segg ss\n", "inner join mapping m on cast(ss.merchant_id as varchar) = m.blinkit_store_id\n", "where date(at_date_ist) >= (current_date - interval '1' day) -INTERVAL '60' day),\n", "\n", "rain_base as (\n", "select\n", "account_date,\n", "extract(hour from accounting_time) as hour,\n", "accounting_time,\n", "trip_id,\n", "surge_pay,\n", "frontend_merchant_id,\n", "lead(accounting_time) over(partition by  frontend_merchant_id order by accounting_time) as lead_ac,\n", "lead(surge_pay) over(partition by  frontend_merchant_id order by accounting_time) as surge_pay_2\n", "from logistics_data_etls.delivery_trip_payout p\n", "left join ( \n", "    select \n", "    distinct \n", "    trip_id as tid,\n", "    frontend_merchant_id \n", "    from dwh.fact_supply_chain_order_details \n", "    where order_checkout_dt_ist >= date(current_date - interval '1' day)-INTERVAL '60' day) x\n", "on cast(p.trip_id as varchar) = x.tid\n", "where dt >= date_format(date(current_date - interval '1' day)-INTERVAL '60' day, '%%Y%%m%%d')),\n", "\n", "log_base3 as (\n", "select * from (\n", "select\n", "account_date,\n", "hour,\n", "frontend_merchant_id,\n", "sm.store_id,\n", "sum(case when surge_pay>0 and surge_pay_2>0 then date_diff('second',accounting_time,lead_ac) end )*1.00/60 as rain_mins\n", "from rain_base b\n", "left join logistics_data_etls.blinkit_store_mapping sm on b.frontend_merchant_id = sm.blinkit_store_id\n", "group by 1,2,3,4)\n", "where rain_mins >= 15),\n", "\n", "b2a_base as (\n", "select \n", "date_,hour_,z_store_id,b2a_orders,order_count,b2a_orders*1.00/order_count b2a_perc\n", "FROM (\n", "    SELECT date(order_checkout_ts_ist) date_,extract(hour from order_checkout_ts_ist) hour_,z_store_id,\n", "    count(distinct case when date_diff('Second',order_billing_completed_ts_ist,order_partner_assigned_ts_ist)*1.00/60>0 then order_id else NULL end) as b2a_orders,\n", "    count(distinct order_id) order_count\n", "    FROM dwh.fact_supply_chain_order_details fsc\n", "    LEFT JOIN mapping m ON cast(fsc.frontend_merchant_id as varchar)=m.blinkit_store_id\n", "    WHERE date(order_checkout_dt_ist) between (current_date - interval '1' day) - interval '60' day AND (current_date )-interval '1' day\n", "    AND order_current_status='DELIVERED'\n", "    GROUP BY 1,2,3\n", ")),\n", "\n", "working_hours as (\n", "select  \n", "account_date as date_,\n", "extract(hour from hr) hour_,\n", "delivery_driver_id,\n", "-- sum(logins_min_calculated)*1.0000 as login\n", "CAST(sum(logins_min_calculated) * 1.0000 / 60 AS DECIMAL(10, 4)) as login_hr\n", "-- ROUND(sum(logins_min_calculated*1.0000) * 1.0000 / 60, 4)\n", "from logistics_data_etls.hourly_dp_wasted_login_v1\n", "where account_date >= (date(current_date - interval '1' day) - INTERVAL '60' day)\n", "GROUP BY 1,2,3\n", "),\n", "\n", "orders1 as (\n", "select \n", "distinct delivery_driver_id, --trip_id,  \n", "o.pickup_user_id, date(accounting_time+interval '330' minute) as account_date, \n", "hour(accounting_time+interval '330' minute) as account_hour,\n", "count(case when leg_status='COMPLETE' and lower(report_type)='drop' then ar.id else null end) as orders \n", "from zomato.accounting_production.accounting_reports ar\n", "left join zomato.carthero_prod.shipment_legs sl on cast(sl.id as varchar) = coalesce(cast(ar.shipment_leg_id as varchar),ar.logistics_order_id)\n", "left join zomato.carthero_prod.orders o on o.id=sl.order_id\n", "INNER JOIN mapping m on o.pickup_user_id=m.z_store_id \n", "where ar.dt>=date_format(date(current_date - interval '1' day) - INTERVAL '60' day, '%%Y%%m%%d')\n", "and sl.dt>=date_format(date(current_date - interval '1' day) - INTERVAL '60' day, '%%Y%%m%%d')\n", "and o.dt>=date_format(date(current_date - interval '1' day) - INTERVAL '60' day, '%%Y%%m%%d')\n", "and merchant_category='BLINKIT'\n", "group by 1,2,3,4),\n", "\n", "orders2 as (\n", "select distinct delivery_driver_id, --trip_id , \n", "o.pickup_user_id, date(accounting_time+interval '330' minute) as account_date, \n", "hour(accounting_time+interval '330' minute) as account_hour, \n", "count(case when leg_status='COMPLETE' and lower(touch_point_type)='drop' then tp.id else null end) as orders \n", "from zomato.accounting_production.driver_touch_point_reports tp\n", "left join zomato.carthero_prod.shipment_legs sl on cast(sl.id as varchar)=coalesce(cast(tp.shipment_leg_id as varchar),tp.logistics_order_id)\n", "left join zomato.carthero_prod.orders o on o.id=sl.order_id\n", "INNER JOIN mapping m on o.pickup_user_id=m.z_store_id \n", "--left join carthero_prod.orders o on sl.order_id=o.id\n", "where tp.dt>=date_format(date(current_date - interval '1' day) - INTERVAL '60' day, '%%Y%%m%%d')\n", "and sl.dt>=date_format(date(current_date - interval '1' day) - INTERVAL '60' day, '%%Y%%m%%d')\n", "and o.dt>=date_format(date(current_date - interval '1' day) - INTERVAL '60' day, '%%Y%%m%%d')\n", "and merchant_category='BLINKIT'\n", "group by 1,2,3,4),\n", "\n", "hourly_orders_base as(\n", "select * from orders1 \n", "union\n", "select * from orders2),\n", "\n", "hourly_orders as (\n", "select delivery_driver_id, pickup_user_id,\n", "account_date,\n", "account_hour,\n", "sum(orders) orders\n", "from\n", "hourly_orders_base\n", "group by 1,2,3,4\n", "),\n", "\n", "login_base_x as (\n", "select \n", "distinct\n", "date_, \n", "hour_,\n", "l.delivery_driver_id,\n", "sum(login_hr) as login_hr,\n", "max_by(z_store_id,account_date) as z_store_id\n", "from working_hours l\n", "left join logistics_data_etls.blinkit_rider_daily_accounting_summary_v1 ho on ho.account_date = l.date_ \n", "     and ho.delivery_driver_id = l.delivery_driver_id and ho.account_date between date(current_date - interval '1' day)-interval '60' day AND date(current_date - interval '1' day)-interval '1' day\n", "where date_ between date(current_date - interval '1' day)-interval '60' day AND date(current_date - interval '1' day)-interval '1' day\n", "group by 1,2,3),\n", "\n", "base_pre as (\n", "SELECT \n", "wh.date_ as account_date,\n", "wh.hour_ as account_hour,\n", "wh.z_store_id as pickup_user_id,\n", "wh.delivery_driver_id,\n", "sum(ho.orders) orders,\n", "sum(login_hr) login_hr\n", "FROM login_base_x wh \n", "LEFT JOIN hourly_orders ho ON ho.account_date=wh.date_ AND ho.account_hour=wh.hour_ AND ho.delivery_driver_id = wh.delivery_driver_id\n", "GROUP BY 1,2,3,4)\n", "\n", "\n", "\n", "\n", ",\n", "\n", "slots as (\n", "select \n", "store_id,\n", "hour,\n", "max_by(slot_id,account_date) as slot_id,\n", "max_by(slot_name,account_date) as slot_name\n", "from logistics_data_etls.b_gigs_design_ver3\n", "where dt > '********'\n", "group by 1,2),\n", "\n", "base_pre_login as (\n", "select \n", "\n", "case when account_hour between 1 and 23 then 'Full day'\n", "     when account_hour = 0 then 'Full day'\n", "else null end as slot,\n", "\n", "pickup_user_id,\n", "\n", "sum(login_hr) login_hr,\n", "sum(orders) orders\n", "from base_pre \n", "where account_date between (current_date - interval '1' day)-interval '7' day AND (current_date - interval '1' day)-interval '1' day\n", "GROUP BY 1,2),\n", "\n", "base as (\n", "SELECT \n", "fsc.date_,\n", "extract(dow from fsc.date_) dow,\n", "case when extract(dow from fsc.date_) between 1 AND 5 then 'WEEKDAY' ELSE 'WEEKEND' END AS dow1,\n", "\n", "case when fsc.hour_ between 1 and 23 then 'Full day'\n", "     when fsc.hour_ = 0 then 'Full day'\n", "else null end as slot,\n", "\n", "-- s.slot_id,\n", "-- s.slot_name,\n", "fsc.pickup_user_id store_id,\n", "sum(fsc.order_count) order_count,\n", "sum(fsc.total_working_hour) total_working_hour,\n", "try(sum(fsc.order_count)*1.0000/sum(fsc.total_working_hour)) hour_util,\n", "sum(fsc.rider_count) partner_count,\n", "COALESCE(sum(b2a_orders),0) b2a_orders,\n", "try(COALESCE(sum(b2a_orders),0)*100.00/sum(bb.order_count)) b2a_orders_perc\n", "\n", "FROM (\n", "    SELECT account_date date_,account_hour hour_,pickup_user_id pickup_user_id,\n", "    sum(orders) order_count,\n", "    sum(login_hr) total_working_hour,count(distinct delivery_driver_id) rider_count FROM base_pre\n", "    GROUP BY 1,2,3\n", ") fsc\n", "LEFT JOIN b2a_base bb on fsc.date_=bb.date_ AND fsc.hour_=bb.hour_ AND fsc.pickup_user_id=bb.z_store_id \n", "left join log_base3 lb3 ON lb3.account_date = fsc.date_ AND lb3.store_id = fsc.pickup_user_id AND lb3.hour = fsc.hour_\n", "-- left join slots s on s.hour = fsc.hour_  and s.store_id = fsc.pickup_user_id\n", "Where fsc.date_ >= (date(current_date - interval '1' day) - interval '60' day)\n", "and lb3.hour is null\n", "-- and fsc.hour_ > 5\n", "GROUP BY 1,2,3,4,5\n", "-- having sum(fsc.order_count)>0\n", ")\n", "\n", "\n", ",\n", "\n", "theo_OPH as (\n", "SELECT \n", "frontend_merchant_id,\n", "\n", "case when a.hour_ between 1 and 23 then 'Full day'\n", "     when a.hour_ = 0 then 'Full day'\n", "else null end as slot,\n", "\n", "\n", "-- slot_id,\n", "-- slot_name,\n", "z_store_id,\n", "SUM(enroute_time)*1.00/sum(order_count) avg_enroute_hr,\n", "sum(order_count)*0.50/sum(enroute_time) theo_rider_OPH\n", "FROM (\n", "    SELECT frontend_merchant_id,extract(hour from order_checkout_ts_ist) hour_,z_store_id,\n", "    case when extract(dow from order_checkout_ts_ist) between 1 AND 5 then 'WEEKDAY' ELSE 'WEEKEND' END as dow,\n", "    count(distinct order_id) order_count,\n", "    sum(date_diff('second',order_enroute_ts_ist,order_delivered_ts_ist))*1.00/3600 enroute_time\n", "    FROM dwh.fact_supply_chain_order_details oh\n", "    INNER JOIN mapping m on cast(oh.frontend_merchant_id as varchar)=m.blinkit_store_id\n", "    WHERE (order_current_status)='DELIVERED'\n", "    AND order_checkout_dt_ist>(current_date - interval '1' day)-interval '61' day\n", "    AND (order_checkout_ts_ist)>=cast((current_date - interval '1' day)-interval '61' day as timestamp)\n", "    GROUP BY 1,2,3,4\n", ") a\n", "-- left join slots s on s.hour = a.hour_ and s.store_id = a.z_store_id\n", "GROUP BY 1,2,3\n", "Having sum(order_count)>0 AND sum(enroute_time)>0),\n", "\n", "DS95 as (WITH ranked_data AS (\n", "    SELECT \n", "        date_,\n", "        slot,\n", "        store_id,\n", "        hour_util,\n", "        b2a_perc_bucket,\n", "        percent_rank() OVER(PARTITION BY slot,store_id,b2a_perc_bucket ORDER BY hour_util ASC) perc_rank,\n", "        ROW_NUMBER() OVER(PARTITION BY slot,store_id,b2a_perc_bucket ORDER BY hour_util ASC) as row_num,\n", "        COUNT(*) OVER(PARTITION BY slot,store_id,b2a_perc_bucket) as total_rows\n", "    FROM (\n", "        SELECT *,\n", "            case when b2a_orders_perc<=3 then '1 DS97'\n", "                 when b2a_orders_perc<=6 then '2 DS94'\n", "                 WHEN b2a_orders_perc>5 AND b2a_orders_perc<=10 then '3 DS90'\n", "                 WHEN b2a_orders_perc>10 AND b2a_orders_perc<=15 then '4 DS85'\n", "                 WHEN b2a_orders_perc>15 AND b2a_orders_perc<=20 then '5 DS80'\n", "                 WHEN b2a_orders_perc <=30 then '6 DS70'\n", "                 WHEN b2a_orders_perc <=40 then '7 DS60'\n", "             ELSE '8 DS<60' END as b2a_perc_bucket\n", "        FROM base\n", "    )\n", ")\n", "\n", "SELECT \n", "    slot,\n", "    store_id,\n", "    \n", "    count(distinct case when b2a_perc_bucket='1 DS97' then date_ END) DS97_instances,\n", "    count(distinct case when b2a_perc_bucket='2 DS94' then date_ END) DS94_instances,\n", "    count(distinct case when b2a_perc_bucket='3 DS90' then date_ END) DS90_instances,\n", "    count(distinct case when b2a_perc_bucket='4 DS85' then date_ END) DS85_instances,\n", "    count(distinct case when b2a_perc_bucket='5 DS80' then date_ END) DS80_instances,\n", "    count(distinct case when b2a_perc_bucket='6 DS70' then date_ END) DS70_instances,\n", "    count(distinct case when b2a_perc_bucket='7 DS60' then date_ END) DS60_instances,\n", "    count(distinct case when b2a_perc_bucket='8 DS<60' then date_ END) DS50_instances,\n", "\n", "    max(case when perc_rank<=0.50 AND b2a_perc_bucket='1 DS97' then hour_util else 0 end) perc_DS97_util,\n", "    max(case when perc_rank<=0.50 AND b2a_perc_bucket='2 DS94' then hour_util else 0 end) perc_DS94_util,\n", "    max(case when perc_rank<=0.50 AND b2a_perc_bucket='3 DS90' then hour_util else 0 end) perc_DS90_util,\n", "    max(case when perc_rank<=0.50 AND b2a_perc_bucket='4 DS85' then hour_util else 0 end) perc_DS85_util,\n", "    max(case when perc_rank<=0.50 AND b2a_perc_bucket='5 DS80' then hour_util else 0 end) perc_DS80_util,\n", "    max(case when perc_rank<=0.50 AND b2a_perc_bucket='6 DS70' then hour_util else 0 end) perc_DS70_util,\n", "    max(case when perc_rank<=0.50 AND b2a_perc_bucket='7 DS60' then hour_util else 0 end) perc_DS60_util,\n", "    max(case when perc_rank<=0.50 AND b2a_perc_bucket='8 DS<60' then hour_util else 0 end) perc_DS50_util,\n", "    \n", "    -- Add date columns for each bucket's median\n", "    MAX(CASE WHEN row_num = CEIL(total_rows/2.0) AND b2a_perc_bucket='1 DS97' THEN date_ END) AS perc_DS97_util_date,\n", "    MAX(CASE WHEN row_num = CEIL(total_rows/2.0) AND b2a_perc_bucket='2 DS94' THEN date_ END) AS perc_DS94_util_date,\n", "    MAX(CASE WHEN row_num = CEIL(total_rows/2.0) AND b2a_perc_bucket='3 DS90' THEN date_ END) AS perc_DS90_util_date,\n", "    MAX(CASE WHEN row_num = CEIL(total_rows/2.0) AND b2a_perc_bucket='4 DS85' THEN date_ END) AS perc_DS85_util_date,\n", "    MAX(CASE WHEN row_num = CEIL(total_rows/2.0) AND b2a_perc_bucket='5 DS80' THEN date_ END) AS perc_DS80_util_date,\n", "    MAX(CASE WHEN row_num = CEIL(total_rows/2.0) AND b2a_perc_bucket='6 DS70' THEN date_ END) AS perc_DS70_util_date,\n", "    MAX(CASE WHEN row_num = CEIL(total_rows/2.0) AND b2a_perc_bucket='7 DS60' THEN date_ END) AS perc_DS60_util_date,\n", "    MAX(CASE WHEN row_num = CEIL(total_rows/2.0) AND b2a_perc_bucket='8 DS<60' THEN date_ END) AS perc_DS50_util_date\n", "\n", "FROM ranked_data\n", "GROUP BY 1,2),\n", "\n", "ds_base as (\n", "SELECT \n", "c.blinkit_store_name,\n", "c.runnr_city,\n", "a.*,b.login_hr last_week_logins,\n", "b.orders orders_last_week,\n", "(DS90_instances + DS85_instances + DS80_instances + DS70_instances + DS60_instances + DS94_instances + DS97_instances + DS50_instances) as total_instances\n", "FROM DS95 a\n", "LEFT JOIN base_pre_login b on a.slot = b.slot and a.store_id = b.pickup_user_id\n", "INNER JOIN logistics_data_etls.blinkit_store_mapping c on a.store_id = c.store_id\n", "WHERE c.is_active=1),\n", "\n", "last_week_dh as (\n", "select\n", "week_start_date, slot, frontend_merchant_id, try(sum(direct_orders)*1.00/sum(delivered_orders)) as last_week_DH\n", "from (\n", "    Select \n", "    date_trunc('week', order_checkout_ts_ist) as week_start_date,\n", "\n", "\n", "   case when extract(hour from order_checkout_ts_ist) between 1 and 23 then 'Full day'\n", "     when extract(hour from order_checkout_ts_ist) = 0 then 'Full day'\n", "else null end as slot,\n", "\n", "    a.frontend_merchant_id,\n", "    count(distinct a.order_id) as delivered_orders,\n", "    count(distinct case when date_diff('second' ,order_billing_completed_ts_ist,order_partner_assigned_ts_ist)<=0 then a.order_id end) direct_orders\n", "    from dwh.fact_supply_chain_order_details a\n", "    INNER JOIN mapping m on cast(a.frontend_merchant_id as varchar)=m.blinkit_store_id\n", "    where order_checkout_dt_ist >= (current_date - interval '1' day) - interval '15' day\n", "    and date_trunc('week', order_checkout_ts_ist) = date_trunc('week', (current_date - interval '1' day) - interval '7' day)\n", "    and a.order_current_status = 'DELIVERED'\n", "    group by 1,2,3\n", ") a\n", "-- left join slots s on s.hour = a.hour and s.store_id = a.z_store_id\n", "group by 1,2,3),\n", "\n", "last_week_oph as (\n", "select\n", "date_trunc('week',account_date) as week_start_date,\n", "\n", "case when account_hour between 1 and 23 then 'Full day'\n", "     when account_hour = 0 then 'Full day'\n", "else null end as slot,\n", "\n", "\n", "\n", "pickup_user_id,\n", "sum(orders) orders,\n", "sum(login_hr) login_hr,\n", "sum(orders)*1.00/sum(login_hr) as last_week_oph\n", "from base_pre\n", "where account_date >= (current_date - interval '1' day) - interval '15' day\n", "and date_trunc('week',account_date) = date_trunc('week', (current_date - interval '1' day) - interval '7' day)\n", "group by 1,2,3),\n", "\n", "final as (\n", "SELECT \n", "db.blinkit_store_name,\n", "db.runnr_city,\n", "db.slot,\n", "-- db.slot_name,\n", "db.store_id,\n", "last_week_logins,\n", "orders_last_week,\n", "sm.blinkit_store_id,\n", "perc_ds97_util,\n", "perc_ds94_util,\n", "perc_ds90_util,\n", "perc_ds85_util,\n", "perc_ds80_util,\n", "perc_ds70_util,\n", "perc_ds60_util,\n", "perc_ds50_util,\n", "\n", "ds97_instances,\n", "ds94_instances,\n", "ds90_instances,\n", "ds85_instances,\n", "ds80_instances,\n", "ds70_instances,\n", "ds60_instances,\n", "ds50_instances,\n", "\n", "total_instances,\n", "\n", "theo_rider_OPH theo_rider_overall_oph,\n", "\n", "case when DS85_instances >= 4 or (total_instances <= 10 and DS85_instances >=1) then perc_DS85_util\n", "     when DS80_instances >= 4 or (total_instances <= 10 and DS80_instances >=1) then perc_DS80_util\n", "     when DS70_instances >= 4 or (total_instances <= 10 and DS70_instances >=1) then perc_DS70_util\n", "     when DS60_instances >= 4 or (total_instances <= 10 and DS60_instances >=1) then perc_DS60_util\n", "     when DS90_instances >= 4 or (total_instances <= 10 and DS90_instances >=1) then perc_DS90_util\n", "     when DS94_instances >= 4 or (total_instances <= 10 and DS94_instances >=1) then perc_DS94_util\n", "     when DS97_instances >= 4 or (total_instances <= 10 and DS97_instances >=1) then perc_DS97_util\n", "     \n", "     when DS85_instances >= 1 then perc_DS85_util\n", "     when DS80_instances >= 1 then perc_DS80_util\n", "     when DS70_instances >= 1 then perc_DS70_util\n", "     when DS60_instances >= 1 then perc_DS60_util\n", "     when DS90_instances >= 1 then perc_DS90_util\n", "     when DS94_instances >= 1 then perc_DS94_util\n", "     when DS97_instances >= 1 then perc_DS97_util\n", "     when perc_DS50_util is not null then perc_DS50_util\n", "     \n", "else 2 end as OPH,\n", "\n", "case when DS85_instances >= 4 or (total_instances <= 10 and DS85_instances >=1) then perc_DS85_util_date\n", "     when DS80_instances >= 4 or (total_instances <= 10 and DS80_instances >=1) then perc_DS80_util_date\n", "     when DS70_instances >= 4 or (total_instances <= 10 and DS70_instances >=1) then perc_DS70_util_date\n", "     when DS60_instances >= 4 or (total_instances <= 10 and DS60_instances >=1) then perc_DS60_util_date\n", "     when DS90_instances >= 4 or (total_instances <= 10 and DS90_instances >=1) then perc_DS90_util_date\n", "     when DS94_instances >= 4 or (total_instances <= 10 and DS94_instances >=1) then perc_DS94_util_date\n", "     when DS97_instances >= 4 or (total_instances <= 10 and DS97_instances >=1) then perc_DS97_util_date\n", "     \n", "     when DS85_instances >= 1 then perc_DS85_util_date\n", "     when DS80_instances >= 1 then perc_DS80_util_date\n", "     when DS70_instances >= 1 then perc_DS70_util_date\n", "     when DS60_instances >= 1 then perc_DS60_util_date\n", "     when DS90_instances >= 1 then perc_DS90_util_date\n", "     when DS94_instances >= 1 then perc_DS94_util_date\n", "     when DS97_instances >= 1 then perc_DS97_util_date\n", "     \n", "else perc_DS50_util_date end as OPH_date,\n", "\n", "last_week_dh,\n", "last_week_oph\n", "\n", "FROM ds_base  DB \n", "left join logistics_data_etls.blinkit_store_mapping sm on sm.store_id = db.store_id\n", "LEFT JOIN theo_oph to on to.frontend_merchant_id = sm.blinkit_store_id and to.slot = db.slot\n", "left join last_week_dh l on l.frontend_merchant_id = sm.blinkit_store_id and l.slot = db.slot\n", "left join last_week_oph o on o.pickup_user_id = db.store_id and o.slot = db.slot\n", "\n", "GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29)\n", "\n", "\n", "select *,\n", "\n", "case when OPH = perc_DS97_util then 1.16\n", "     when OPH = perc_DS94_util then 1.11\n", "     when OPH = perc_DS90_util then 1.05\n", "     when OPH = perc_DS85_util then 1.00\n", "     when OPH = perc_DS80_util then 0.95\n", "     when OPH = perc_DS70_util then 0.89\n", "     when OPH = perc_DS60_util then 0.85\n", "     when OPH = perc_DS50_util then 0.81\n", "else 1.0 end as multiplier,\n", "\n", "case when OPH = perc_DS97_util then 1.16*OPH\n", "     when OPH = perc_DS94_util then 1.11*OPH\n", "     when OPH = perc_DS90_util then 1.05*OPH\n", "     when OPH = perc_DS85_util then 1.00*OPH\n", "     when OPH = perc_DS80_util then 0.95*OPH\n", "     when OPH = perc_DS70_util then 0.89*OPH\n", "     when OPH = perc_DS60_util then 0.85*OPH\n", "     when OPH = perc_DS50_util then 0.81*OPH\n", "else OPH end as optimal_oph\n", "\n", "\n", "from final\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "bfc07a5f-da5d-48ec-9592-22eaba6a2885", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"oph_full_day_85\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"blinkit_store_name\", \"type\": \"varchar\", \"description\": \"blinkit_store_name\"},\n", "        {\"name\": \"runnr_city\", \"type\": \"varchar\", \"description\": \"runnr_city\"},\n", "        {\"name\": \"slot\", \"type\": \"varchar\", \"description\": \"slot\"},\n", "        {\"name\": \"store_id\", \"type\": \"integer\", \"description\": \"store_id\"},\n", "        {\"name\": \"last_week_logins\", \"type\": \"double\", \"description\": \"logins_last_week\"},\n", "        {\"name\": \"orders_last_week\", \"type\": \"double\", \"description\": \"orders_last_week\"},\n", "        {\"name\": \"blinkit_store_id\", \"type\": \"integer\", \"description\": \"blinkit_store_id\"},\n", "        {\"name\": \"perc_DS97_util\", \"type\": \"double\", \"description\": \"perc_ds97_util\"},\n", "        {\"name\": \"perc_DS94_util\", \"type\": \"double\", \"description\": \"perc_ds94_util\"},\n", "        {\"name\": \"perc_DS90_util\", \"type\": \"double\", \"description\": \"perc_ds90_util\"},\n", "        {\"name\": \"perc_DS85_util\", \"type\": \"double\", \"description\": \"perc_ds85_util\"},\n", "        {\"name\": \"perc_DS80_util\", \"type\": \"double\", \"description\": \"perc_ds80_util\"},\n", "        {\"name\": \"perc_DS70_util\", \"type\": \"double\", \"description\": \"perc_ds70_util\"},\n", "        {\"name\": \"perc_DS60_util\", \"type\": \"double\", \"description\": \"perc_ds60_util\"},\n", "        {\"name\": \"perc_DS50_util\", \"type\": \"double\", \"description\": \"perc_ds50_util\"},\n", "        {\"name\": \"DS97_instances\", \"type\": \"integer\", \"description\": \"ds97_instances\"},\n", "        {\"name\": \"DS94_instances\", \"type\": \"integer\", \"description\": \"ds94_instances\"},\n", "        {\"name\": \"DS90_instances\", \"type\": \"integer\", \"description\": \"ds90_instances\"},\n", "        {\"name\": \"DS85_instances\", \"type\": \"integer\", \"description\": \"ds85_instances\"},\n", "        {\"name\": \"DS80_instances\", \"type\": \"integer\", \"description\": \"ds80_instances\"},\n", "        {\"name\": \"DS70_instances\", \"type\": \"integer\", \"description\": \"ds70_instances\"},\n", "        {\"name\": \"DS60_instances\", \"type\": \"integer\", \"description\": \"ds60_instances\"},\n", "        {\"name\": \"DS50_instances\", \"type\": \"integer\", \"description\": \"ds50_instances\"},\n", "        {\"name\": \"total_instances\", \"type\": \"integer\", \"description\": \"total_instances\"},\n", "        {\n", "            \"name\": \"theo_rider_overall_OPH\",\n", "            \"type\": \"double\",\n", "            \"description\": \"theo_rider_overall_oph\",\n", "        },\n", "        {\"name\": \"OPH\", \"type\": \"double\", \"description\": \"oph\"},\n", "        {\"name\": \"OPH_date\", \"type\": \"date\", \"description\": \"oph_date\"},\n", "        {\"name\": \"last_week_dh\", \"type\": \"double\", \"description\": \"dh_last_week\"},\n", "        {\"name\": \"last_week_oph\", \"type\": \"double\", \"description\": \"oph_last_week\"},\n", "        {\"name\": \"multiplier\", \"type\": \"double\", \"description\": \"multiplier\"},\n", "        {\"name\": \"optimal_oph\", \"type\": \"double\", \"description\": \"optimal_oph\"},\n", "    ],\n", "    \"primary_key\": [\"store_id\", \"slot\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"This table contains the ideal OPH for stores at multiple slot level at 90-95 and 85-90 DH\",\n", "}\n", "pb.to_trino(sql_full_day_1, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "ddff08b5-1a45-4c40-82cf-a68b36cc7b47", "metadata": {}, "outputs": [], "source": ["sql_full_day_2 = \"\"\"\n", "\n", "with btpo_base_day_slot as (\n", "\n", "with mapping as (\n", "select z_store_id, blinkit_store_id, store_name\n", "from (\n", "    select distinct id as z_store_id, external_id as blinkit_store_id, name as store_name,\n", "    rank() over (partition by id order by updated_at desc) as rnk\n", "    from zomato.carthero_prod.users where type = 'merchant' and source_id = 17110 \n", "    and (created_at) >= cast(date('2019-05-01') as timestamp)\n", ") where rnk=1\n", "group by 1,2,3\n", "),\n", "\n", "base as\n", "(   \n", "  Select \n", "   date_,\n", "   date_trunc('week',date_) as week_start_date,\n", "   hour_,\n", "   frontend_merchant_id,\n", "   sum(log_orders) log_orders,\n", "   sum(delivered_orders) delivered_orders,\n", "   sum(direct_handover_orders) direct_handover_orders,\n", "   sum(store_handshake_time) store_handshake_time,\n", "   sum(enroute_to_doorstep) enroute_to_doorstep,\n", "   sum(doorstep_to_delivered) doorstep_to_delivered\n", "   from logistics_data_etls.hourly_supply_metrics_etl\n", "   where date_ >= current_date - interval '65' day\n", "   group by 1,2,3,4\n", " ),\n", " \n", " trip as \n", "(\n", "  Select trip_id as trip_id_,accounting_time\n", "  from logistics_data_etls.delivery_trip_payout\n", "  where dt>=date_format(current_date - interval '65' day, '%%Y%%m%%d')\n", "  group by 1,2\n", "),\n", " \n", " fact as \n", " (\n", "    SELECT frontend_merchant_id,\n", "    date(order_checkout_ts_ist) date_,\n", "    extract(hour from order_checkout_ts_ist) hour_,\n", "    sum(case when order_partner_assigned_ts_ist < order_billing_completed_ts_ist then \n", "                      (date_diff('second',order_partner_assigned_ts_ist,order_billing_completed_ts_ist)) end) wait_time\n", "    FROM dwh.fact_supply_chain_order_details oh\n", "    INNER JOIN mapping m on cast(oh.frontend_merchant_id as varchar)=m.blinkit_store_id\n", "    WHERE (order_current_status)='DELIVERED'\n", "    AND order_checkout_dt_ist>date(current_date - interval '1' day)-interval '65' day\n", "    AND (order_checkout_ts_ist)>=cast(date(current_date - interval '1' day)-interval '65' day as timestamp)\n", "    GROUP BY 1,2,3\n", "),\n", " \n", " \n", " btpo_base as \n", " (\n", "    select \n", "    date,\n", "    extract(hour from cast(coalesce(cast(accounting_time as varchaR),ts) as timestamp)) as hour,\n", "    cast(blinkit_store_id as int) as blinkit_store_id,\n", "    store_name,\n", "    city_name,\n", "    coalesce(sum(case when lead_event!='Logout' then return_time end),0) as total_return_time,\n", "    coalesce(sum(idle_time),0) as total_idle_time,\n", "    date_trunc('week',cast(date as date)) as week_start\n", "   from  serviceability_etls.btpo_rider_events_v2 as b\n", "   left join trip t on cast(t.trip_id_ as varchar) = b.trip_id\n", "   WHERE store_name NOT LIKE 'NA'\n", "   and date >= cast(current_date - interval '65' day as varchar)\n", "   group by 1,2,3,4,5\n", "   order by 1,2\n", "   )\n", "   \n", "   \n", "   select \n", "   date,\n", "\n", "   case when hour between 1 and 23 then 'Full day'\n", "     when hour = 0 then 'Full day'\n", "else null end as slot,\n", "\n", "\n", "   city_name,\n", "   blinkit_store_name,\n", "   blinkit_store_id,\n", "   sum(log_orders) as log_orders,\n", "   sum(delivered_orders) as delivered_orders,\n", "           sum(store_handshake_time) store_handshake_time,\n", "        sum(enroute_to_doorstep) enroute_to_doorstep,\n", "        sum(doorstep_to_delivered) doorstep_to_delivered,\n", "        sum(total_return_time) total_return_time,\n", "        sum(total_idle_time) total_idle_time,\n", "        sum(wait_time) wait_time\n", "        \n", "        from (\n", "   Select bt.blinkit_store_id,\n", "        sm.runnr_city as city_name,\n", "        date_trunc('week',date(bt.date)) as week_start_date,\n", "        extract(week from date(bt.date)) as wk,\n", "        extract(dow from date(bt.date)) as dow,\n", "        date(bt.date) as date,\n", "        blinkit_store_name,\n", "        store_id,\n", "        bt.hour,\n", "        coalesce(log_orders,0) log_orders,\n", "        coalesce(delivered_orders,0) delivered_orders,\n", "        coalesce(store_handshake_time,0) store_handshake_time,\n", "        coalesce(enroute_to_doorstep,0) enroute_to_doorstep,\n", "        coalesce(doorstep_to_delivered,0) doorstep_to_delivered,\n", "        coalesce(bt.total_return_time,0) total_return_time,\n", "        coalesce(bt.total_idle_time,0) total_idle_time,\n", "        coalesce(f.wait_time,0) wait_time\n", "     from btpo_base bt\n", "     left join base b on b.frontend_merchant_id = bt.blinkit_store_id and b.hour_ = bt.hour and cast(b.date_ as varchar) = bt.date\n", "     left join fact f on f.frontend_merchant_id = bt.blinkit_store_id and bt.hour = f.hour_ and bt.date = cast(f.date_ as varchar)\n", "     left join logistics_data_etls.blinkit_store_mapping sm on sm.blinkit_store_id = bt.blinkit_store_id\n", "     )\n", "     group by 1,2,3,4,5\n", "),\n", "\n", "btpo_base_week as (\n", "select \n", "date_trunc('week',date) as week_start_date,\n", "city_name,\n", "blinkit_store_name,\n", "blinkit_store_id,\n", "sum(log_orders) as log_orders,\n", "sum(delivered_orders) as delivered_orders,\n", "sum(store_handshake_time) store_handshake_time,\n", "sum(enroute_to_doorstep) enroute_to_doorstep,\n", "sum(doorstep_to_delivered) doorstep_to_delivered,\n", "sum(total_return_time) total_return_time,\n", "sum(total_idle_time) total_idle_time,\n", "sum(wait_time) wait_time\n", "from\n", "btpo_base_day_slot\n", "group by 1,2,3,4\n", ")\n", "\n", "\n", "select \n", "a.*,\n", "b.log_orders log_orders_oph_day_slot,\n", "b.delivered_orders delivered_orders_oph_day_slot,\n", "b.store_handshake_time store_handshake_time_oph_day_slot,\n", "b.enroute_to_doorstep enroute_to_doorstep_oph_day_slot,\n", "b.doorstep_to_delivered doorstep_to_delivered_oph_day_slot,\n", "b.total_return_time total_return_time_oph_day_slot,\n", "b.total_idle_time total_idle_time_oph_day_slot,\n", "b.wait_time wait_time_oph_day_slot,\n", "(b.store_handshake_time + b.enroute_to_doorstep +  b.doorstep_to_delivered +\n", "b.total_return_time + b.total_idle_time + b.wait_time) total_time_oph_day_slot,\n", "try(((b.store_handshake_time + b.enroute_to_doorstep + b.doorstep_to_delivered +\n", "b.total_return_time + b.total_idle_time + b.wait_time)*1.0000 / (b.log_orders))) as total_time_avg_oph_day_slot,\n", "try((3600.0000)/(((b.store_handshake_time + b.enroute_to_doorstep + b.doorstep_to_delivered +\n", "b.total_return_time + b.total_idle_time + b.wait_time)*1.0000 / (b.log_orders)))) as theo_util_oph_day_slot,\n", "\n", "\n", "c.log_orders log_orders_oph_week,\n", "c.delivered_orders delivered_orders_oph_week,\n", "c.store_handshake_time store_handshake_time_oph_week,\n", "c.enroute_to_doorstep enroute_to_doorstep_oph_week,\n", "c.doorstep_to_delivered doorstep_to_delivered_oph_week,\n", "c.total_return_time total_return_time_oph_week,\n", "c.total_idle_time total_idle_time_oph_week,\n", "c.wait_time wait_time_oph_week,\n", "try(((c.store_handshake_time + c.enroute_to_doorstep + c.doorstep_to_delivered +\n", "c.total_return_time + c.total_idle_time + c.wait_time)*1.0000 / (c.log_orders))) as total_time_avg_oph_week,\n", "try((3600.0000)/(((c.store_handshake_time + c.enroute_to_doorstep + c.doorstep_to_delivered +\n", "c.total_return_time + c.total_idle_time + c.wait_time)*1.0000 / (c.log_orders)))) as theo_util_oph_week\n", "\n", "\n", "from\n", "interim.oph_full_day_85 a\n", "left join btpo_base_day_slot b on a.blinkit_store_id = b.blinkit_store_id and a.slot = b.slot and a.oph_date = b.date\n", "left join btpo_base_week c on a.blinkit_store_id = c.blinkit_store_id and date_trunc('week',a.oph_date) = c.week_start_date\n", "\n", "     \n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "fc2279ed-c029-4795-970e-c54a616e7601", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"final_oph_full_day_85\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"blinkit_store_name\", \"type\": \"varchar\", \"description\": \"blinkit_store_name\"},\n", "        {\"name\": \"runnr_city\", \"type\": \"varchar\", \"description\": \"runnr_city\"},\n", "        {\"name\": \"slot\", \"type\": \"varchar\", \"description\": \"slot\"},\n", "        {\"name\": \"store_id\", \"type\": \"integer\", \"description\": \"store_id\"},\n", "        {\"name\": \"last_week_logins\", \"type\": \"double\", \"description\": \"logins_last_week\"},\n", "        {\"name\": \"orders_last_week\", \"type\": \"double\", \"description\": \"orders_last_week\"},\n", "        {\"name\": \"blinkit_store_id\", \"type\": \"integer\", \"description\": \"blinkit_store_id\"},\n", "        {\"name\": \"perc_DS97_util\", \"type\": \"double\", \"description\": \"perc_ds97_util\"},\n", "        {\"name\": \"perc_DS94_util\", \"type\": \"double\", \"description\": \"perc_ds94_util\"},\n", "        {\"name\": \"perc_DS90_util\", \"type\": \"double\", \"description\": \"perc_ds90_util\"},\n", "        {\"name\": \"perc_DS85_util\", \"type\": \"double\", \"description\": \"perc_ds85_util\"},\n", "        {\"name\": \"perc_DS80_util\", \"type\": \"double\", \"description\": \"perc_ds80_util\"},\n", "        {\"name\": \"perc_DS70_util\", \"type\": \"double\", \"description\": \"perc_ds70_util\"},\n", "        {\"name\": \"perc_DS60_util\", \"type\": \"double\", \"description\": \"perc_ds60_util\"},\n", "        {\"name\": \"perc_DS50_util\", \"type\": \"double\", \"description\": \"perc_ds50_util\"},\n", "        {\"name\": \"DS97_instances\", \"type\": \"integer\", \"description\": \"ds97_instances\"},\n", "        {\"name\": \"DS94_instances\", \"type\": \"integer\", \"description\": \"ds94_instances\"},\n", "        {\"name\": \"DS90_instances\", \"type\": \"integer\", \"description\": \"ds90_instances\"},\n", "        {\"name\": \"DS85_instances\", \"type\": \"integer\", \"description\": \"ds85_instances\"},\n", "        {\"name\": \"DS80_instances\", \"type\": \"integer\", \"description\": \"ds80_instances\"},\n", "        {\"name\": \"DS70_instances\", \"type\": \"integer\", \"description\": \"ds70_instances\"},\n", "        {\"name\": \"DS60_instances\", \"type\": \"integer\", \"description\": \"ds60_instances\"},\n", "        {\"name\": \"DS50_instances\", \"type\": \"integer\", \"description\": \"ds50_instances\"},\n", "        {\"name\": \"total_instances\", \"type\": \"integer\", \"description\": \"total_instances\"},\n", "        {\n", "            \"name\": \"theo_rider_overall_OPH\",\n", "            \"type\": \"double\",\n", "            \"description\": \"theo_rider_overall_oph\",\n", "        },\n", "        {\"name\": \"OPH\", \"type\": \"double\", \"description\": \"oph\"},\n", "        {\"name\": \"OPH_date\", \"type\": \"date\", \"description\": \"oph_date\"},\n", "        {\"name\": \"last_week_dh\", \"type\": \"double\", \"description\": \"dh_last_week\"},\n", "        {\"name\": \"last_week_oph\", \"type\": \"double\", \"description\": \"oph_last_week\"},\n", "        {\"name\": \"multiplier\", \"type\": \"double\", \"description\": \"multiplier\"},\n", "        {\"name\": \"optimal_oph\", \"type\": \"double\", \"description\": \"optimal_oph\"},\n", "        {\n", "            \"name\": \"log_orders_oph_day_slot\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"log_orders_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"delivered_orders_oph_day_slot\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"delivered_orders_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"store_handshake_time_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"store_handshake_time_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"enroute_to_doorstep_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"enroute_to_doorstep_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"doorstep_to_delivered_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"doorstep_to_delivered_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"total_return_time_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_return_time_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"total_idle_time_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_idle_time_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"wait_time_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"wait_time_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"total_time_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_time_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"total_time_avg_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_time_avg_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"theo_util_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"theo_util_oph_day_slot\",\n", "        },\n", "        {\"name\": \"log_orders_oph_week\", \"type\": \"integer\", \"description\": \"log_orders_oph_week\"},\n", "        {\n", "            \"name\": \"delivered_orders_oph_week\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"delivered_orders_oph_week\",\n", "        },\n", "        {\n", "            \"name\": \"store_handshake_time_oph_week\",\n", "            \"type\": \"double\",\n", "            \"description\": \"store_handshake_time_oph_week\",\n", "        },\n", "        {\n", "            \"name\": \"enroute_to_doorstep_oph_week\",\n", "            \"type\": \"double\",\n", "            \"description\": \"enroute_to_doorstep_oph_week\",\n", "        },\n", "        {\n", "            \"name\": \"doorstep_to_delivered_oph_week\",\n", "            \"type\": \"double\",\n", "            \"description\": \"doorstep_to_delivered_oph_week\",\n", "        },\n", "        {\n", "            \"name\": \"total_return_time_oph_week\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_return_time_oph_week\",\n", "        },\n", "        {\n", "            \"name\": \"total_idle_time_oph_week\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_idle_time_oph_week\",\n", "        },\n", "        {\"name\": \"wait_time_oph_week\", \"type\": \"double\", \"description\": \"wait_time_oph_week\"},\n", "        {\n", "            \"name\": \"total_time_avg_oph_week\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_time_avg_oph_week\",\n", "        },\n", "        {\"name\": \"theo_util_oph_week\", \"type\": \"double\", \"description\": \"theo_util_oph_week\"},\n", "    ],\n", "    \"primary_key\": [\"store_id\", \"slot\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"This table contains the ideal OPH for stores at multiple slot level at 90-95 and 85-90 DH\",\n", "}\n", "pb.to_trino(sql_full_day_2, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "6df07480-4765-4635-9947-4445db3648c7", "metadata": {}, "outputs": [], "source": ["sql_full_day_3 = \"\"\"\n", "\n", "with delivery_distance as (\n", "select\n", "extract(week from account_date) as week_number,\n", "date_trunc('week',account_date) as week_start_date,\n", "blinkit_store_id,\n", "blinkit_store_name,\n", "sum(delivered_orders_distance) as delivered_distance,\n", "sum(delivered_orders) as delivered_orders,\n", "try(sum(delivered_orders_distance) / sum(delivered_orders)) as average_dd\n", "from\n", "logistics_data_etls.blinkit_rider_daily_accounting_summary_v1\n", "where account_date >= current_date - interval '70' day\n", "group by 1,2,3,4\n", "),\n", "\n", "final_base as (\n", "select *,\n", "try(total_idle_time_oph_day_slot*100.00/total_time_oph_day_slot) as idle_time_ratio_oph_day_slot,\n", "\n", "(0.35* (store_handshake_time_oph_day_slot + doorstep_to_delivered_oph_day_slot +  total_return_time_oph_day_slot\n", "+ enroute_to_doorstep_oph_day_slot + wait_time_oph_day_slot ) / 0.65) as ideal_idle_time_oph_day_slot,\n", "\n", "\n", "try((3600.0000)/(((store_handshake_time_oph_week + doorstep_to_delivered_oph_week +\n", "(total_return_time_oph_week + enroute_to_doorstep_oph_week)*\n", "(final_dd_correction_factor)\n", "+ total_idle_time_oph_week + wait_time_oph_week)*1.0000 / (log_orders_oph_week)))) as theo_util_oph_week_dd_corrected,\n", "\n", "try((3600.0000) / (((1.0000* (store_handshake_time_oph_day_slot + doorstep_to_delivered_oph_day_slot +  total_return_time_oph_day_slot\n", "+ enroute_to_doorstep_oph_day_slot + wait_time_oph_day_slot ) / 0.65)) *1.0000 / (log_orders_oph_day_slot))) as theo_util_ideal_oph_day_slot\n", "\n", "from\n", "(\n", "\n", "select \n", "a.*,\n", "\n", "ddl.average_dd dd_avg_last_week,\n", "dd.average_dd dd_avg_oph_week,\n", "\n", "case when try((ddl.average_dd * 1.0000 / dd.average_dd)) >= 1.30 then 1.30\n", "     when try((ddl.average_dd * 1.0000 / dd.average_dd)) <= 0.70 then 0.70\n", "else try((ddl.average_dd * 1.0000 / dd.average_dd)) end as final_dd_correction_factor\n", "\n", "\n", "from\n", "interim.final_oph_full_day_85 a\n", "left join delivery_distance ddl on ddl.blinkit_store_id = a.blinkit_store_id and ddl.week_start_date = date_trunc('week',current_date - interval '1' day) - interval '7' day\n", "left join delivery_distance dd on a.blinkit_store_id = dd.blinkit_store_id and date_trunc('week',a.OPH_date) = dd.week_start_date\n", ")\n", "),\n", "\n", "\n", "final as (\n", "\n", "select *, extract(week from OPH_date) as oph_week_number,\n", "date_trunc('week',OPH_date) as oph_week_start_date,\n", "\n", "try((dd_avg_last_week)*1.0000/(dd_avg_oph_week)) as dd_correction_factor,\n", "\n", "try(abs(theo_util_oph_week_dd_corrected - theo_util_oph_week)*1.0000/theo_util_oph_week) as perc_change_theo_oph_dd,\n", "\n", "try((theo_util_ideal_oph_day_slot - theo_util_oph_day_slot)*1.0000/theo_util_oph_day_slot) as perc_change_theo_oph_it\n", "\n", "from final_base\n", "),\n", "\n", "final_dd_correction as (\n", "select *,\n", "case when final_dd_correction_factor >= 1.10 then optimal_oph * (1 + perc_change_theo_oph_dd) \n", "     when final_dd_correction_factor <= 0.90 then optimal_oph * (1 - perc_change_theo_oph_dd)\n", "     else optimal_oph\n", "end as optimal_oph_corrected_dd\n", "from\n", "final\n", ")\n", "\n", "select \n", "    blinkit_store_name,\n", "    runnr_city,\n", "    slot,\n", "    store_id,\n", "    last_week_logins logins_last_week,\n", "    orders_last_week,\n", "    blinkit_store_id,\n", "    perc_DS97_util,\n", "    perc_DS94_util,\n", "    perc_DS90_util,\n", "    perc_DS85_util,\n", "    perc_DS80_util,\n", "    perc_DS70_util,\n", "    perc_DS60_util,\n", "    perc_DS50_util,\n", "    DS97_instances,\n", "    DS94_instances,\n", "    DS90_instances,\n", "    DS85_instances,\n", "    DS80_instances,\n", "    DS70_instances,\n", "    DS60_instances,\n", "    DS50_instances,\n", "    theo_rider_overall_OPH,\n", "    OPH,\n", "    OPH_date,\n", "    last_week_dh dh_last_week,\n", "    last_week_oph oph_last_week,\n", "    dd_avg_last_week,\n", "    dd_avg_oph_week,\n", "    log_orders_oph_week,\n", "    delivered_orders_oph_week,\n", "    store_handshake_time_oph_week,\n", "    enroute_to_doorstep_oph_week,\n", "    doorstep_to_delivered_oph_week,\n", "    total_return_time_oph_week,\n", "    total_idle_time_oph_week,\n", "    wait_time_oph_week,\n", "    total_time_avg_oph_week,\n", "    theo_util_oph_week,\n", "    log_orders_oph_day_slot,\n", "    delivered_orders_oph_day_slot,\n", "    store_handshake_time_oph_day_slot,\n", "    enroute_to_doorstep_oph_day_slot,\n", "    doorstep_to_delivered_oph_day_slot,\n", "    total_return_time_oph_day_slot,\n", "    total_idle_time_oph_day_slot,\n", "    wait_time_oph_day_slot,\n", "    total_time_oph_day_slot,\n", "    total_time_avg_oph_day_slot,\n", "    theo_util_oph_day_slot,\n", "    final_dd_correction_factor,\n", "    null as dh_oph_day_slot,\n", "    idle_time_ratio_oph_day_slot,\n", "    ideal_idle_time_oph_day_slot,\n", "    theo_util_oph_week_dd_corrected,\n", "    theo_util_ideal_oph_day_slot,\n", "    oph_week_number,\n", "    oph_week_start_date,\n", "    dd_correction_factor,\n", "    perc_change_theo_oph_dd,\n", "    perc_change_theo_oph_it,\n", "    multiplier,\n", "    optimal_oph,\n", "    optimal_oph_corrected_dd,\n", "    final_idle_time_correction_factor,\n", "    optimal_oph_corrected_idle_time,\n", "    slot_level,\n", "    Final_OPH_dh_bucket,\n", "    updated_on\n", "    \n", "    from\n", "    (\n", "\n", "\n", "select *,\n", "case when idle_time_ratio_oph_day_slot >= 35 then least(((1 + perc_change_theo_oph_it)/multiplier),1.40)\n", "else 1 end as final_idle_time_correction_factor,\n", "coalesce(greatest(least(case when idle_time_ratio_oph_day_slot >= 35 then optimal_oph_corrected_dd * least(((1 + perc_change_theo_oph_it)/multiplier),1.40) \n", "     else optimal_oph_corrected_dd\n", "end,4),0.5),2) as optimal_oph_corrected_idle_time,\n", "\n", "'full_day_slot' as slot_level,\n", "'85-90' as Final_OPH_dh_bucket,\n", "current_date as updated_on\n", "\n", "from final_dd_correction\n", ")\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "2eca3169-7a36-4338-9811-392f85f60616", "metadata": {}, "outputs": [], "source": ["full_day_85 = pd.read_sql_query(sql_full_day_3, conn)"]}, {"cell_type": "code", "execution_count": null, "id": "e9f70a41-ff48-44f2-9ff4-779c8cb7ee4c", "metadata": {}, "outputs": [], "source": ["df_final = pd.concat([four_hour_85, full_day_85, six_hour_85], ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "0cbe7e20-3774-4bf1-8565-e99607399b91", "metadata": {}, "outputs": [], "source": ["df_final[\"blinkit_store_name\"] = df_final[\"blinkit_store_name\"].astype(str)\n", "df_final[\"runnr_city\"] = df_final[\"runnr_city\"].astype(str)\n", "df_final[\"slot\"] = df_final[\"slot\"].astype(str)\n", "df_final[\"store_id\"] = df_final[\"store_id\"].fillna(0).astype(int)\n", "df_final[\"logins_last_week\"] = df_final[\"logins_last_week\"].fillna(0.0).astype(float)\n", "df_final[\"orders_last_week\"] = df_final[\"orders_last_week\"].fillna(0.0).astype(float)\n", "df_final[\"blinkit_store_id\"] = df_final[\"blinkit_store_id\"].fillna(0).astype(int)\n", "df_final[\"perc_DS97_util\"] = df_final[\"perc_DS97_util\"].fillna(0.0).astype(float)\n", "df_final[\"perc_DS94_util\"] = df_final[\"perc_DS94_util\"].fillna(0.0).astype(float)\n", "df_final[\"perc_DS90_util\"] = df_final[\"perc_DS90_util\"].fillna(0.0).astype(float)\n", "df_final[\"perc_DS85_util\"] = df_final[\"perc_DS85_util\"].fillna(0.0).astype(float)\n", "df_final[\"perc_DS80_util\"] = df_final[\"perc_DS80_util\"].fillna(0.0).astype(float)\n", "df_final[\"perc_DS70_util\"] = df_final[\"perc_DS70_util\"].fillna(0.0).astype(float)\n", "df_final[\"perc_DS60_util\"] = df_final[\"perc_DS60_util\"].fillna(0.0).astype(float)\n", "df_final[\"perc_DS50_util\"] = df_final[\"perc_DS50_util\"].fillna(0.0).astype(float)\n", "df_final[\"DS97_instances\"] = df_final[\"DS97_instances\"].fillna(0).astype(int)\n", "df_final[\"DS94_instances\"] = df_final[\"DS94_instances\"].fillna(0).astype(int)\n", "df_final[\"DS90_instances\"] = df_final[\"DS90_instances\"].fillna(0).astype(int)\n", "df_final[\"DS85_instances\"] = df_final[\"DS85_instances\"].fillna(0).astype(int)\n", "df_final[\"DS80_instances\"] = df_final[\"DS80_instances\"].fillna(0).astype(int)\n", "df_final[\"DS70_instances\"] = df_final[\"DS70_instances\"].fillna(0).astype(int)\n", "df_final[\"DS60_instances\"] = df_final[\"DS60_instances\"].fillna(0).astype(int)\n", "df_final[\"DS50_instances\"] = df_final[\"DS50_instances\"].fillna(0).astype(int)\n", "df_final[\"theo_rider_overall_OPH\"] = df_final[\"theo_rider_overall_OPH\"].fillna(0.0).astype(float)\n", "df_final[\"OPH\"] = df_final[\"OPH\"].fillna(0.0).astype(float)\n", "df_final[\"OPH_date\"] = pd.to_datetime(df_final[\"OPH_date\"], errors=\"coerce\")\n", "df_final[\"dh_last_week\"] = df_final[\"dh_last_week\"].fillna(0.0).astype(float)\n", "df_final[\"oph_last_week\"] = df_final[\"oph_last_week\"].fillna(0.0).astype(float)\n", "df_final[\"dd_avg_last_week\"] = df_final[\"dd_avg_last_week\"].fillna(0.0).astype(float)\n", "df_final[\"dd_avg_oph_week\"] = df_final[\"dd_avg_oph_week\"].fillna(0.0).astype(float)\n", "df_final[\"log_orders_oph_week\"] = df_final[\"log_orders_oph_week\"].fillna(0).astype(int)\n", "df_final[\"delivered_orders_oph_week\"] = df_final[\"delivered_orders_oph_week\"].fillna(0).astype(int)\n", "df_final[\"store_handshake_time_oph_week\"] = (\n", "    df_final[\"store_handshake_time_oph_week\"].fillna(0.0).astype(float)\n", ")\n", "df_final[\"enroute_to_doorstep_oph_week\"] = (\n", "    df_final[\"enroute_to_doorstep_oph_week\"].fillna(0.0).astype(float)\n", ")\n", "df_final[\"doorstep_to_delivered_oph_week\"] = (\n", "    df_final[\"doorstep_to_delivered_oph_week\"].fillna(0.0).astype(float)\n", ")\n", "df_final[\"total_return_time_oph_week\"] = (\n", "    df_final[\"total_return_time_oph_week\"].fillna(0.0).astype(float)\n", ")\n", "df_final[\"total_idle_time_oph_week\"] = (\n", "    df_final[\"total_idle_time_oph_week\"].fillna(0.0).astype(float)\n", ")\n", "df_final[\"wait_time_oph_week\"] = df_final[\"wait_time_oph_week\"].fillna(0.0).astype(float)\n", "df_final[\"total_time_avg_oph_week\"] = df_final[\"total_time_avg_oph_week\"].fillna(0.0).astype(float)\n", "df_final[\"theo_util_oph_week\"] = df_final[\"theo_util_oph_week\"].fillna(0.0).astype(float)\n", "df_final[\"log_orders_oph_day_slot\"] = df_final[\"log_orders_oph_day_slot\"].fillna(0).astype(int)\n", "df_final[\"delivered_orders_oph_day_slot\"] = (\n", "    df_final[\"delivered_orders_oph_day_slot\"].fillna(0).astype(int)\n", ")\n", "df_final[\"store_handshake_time_oph_day_slot\"] = (\n", "    df_final[\"store_handshake_time_oph_day_slot\"].fillna(0.0).astype(float)\n", ")\n", "df_final[\"enroute_to_doorstep_oph_day_slot\"] = (\n", "    df_final[\"enroute_to_doorstep_oph_day_slot\"].fillna(0.0).astype(float)\n", ")\n", "df_final[\"doorstep_to_delivered_oph_day_slot\"] = (\n", "    df_final[\"doorstep_to_delivered_oph_day_slot\"].fillna(0.0).astype(float)\n", ")\n", "df_final[\"total_return_time_oph_day_slot\"] = (\n", "    df_final[\"total_return_time_oph_day_slot\"].fillna(0.0).astype(float)\n", ")\n", "df_final[\"total_idle_time_oph_day_slot\"] = (\n", "    df_final[\"total_idle_time_oph_day_slot\"].fillna(0.0).astype(float)\n", ")\n", "df_final[\"wait_time_oph_day_slot\"] = df_final[\"wait_time_oph_day_slot\"].fillna(0.0).astype(float)\n", "df_final[\"total_time_oph_day_slot\"] = df_final[\"total_time_oph_day_slot\"].fillna(0.0).astype(float)\n", "df_final[\"total_time_avg_oph_day_slot\"] = (\n", "    df_final[\"total_time_avg_oph_day_slot\"].fillna(0.0).astype(float)\n", ")\n", "df_final[\"theo_util_oph_day_slot\"] = df_final[\"theo_util_oph_day_slot\"].fillna(0.0).astype(float)\n", "df_final[\"final_dd_correction_factor\"] = (\n", "    df_final[\"final_dd_correction_factor\"].fillna(0.0).astype(float)\n", ")\n", "df_final[\"dh_oph_day_slot\"] = df_final[\"dh_oph_day_slot\"].fillna(0.0).astype(float)\n", "df_final[\"idle_time_ratio_oph_day_slot\"] = (\n", "    df_final[\"idle_time_ratio_oph_day_slot\"].fillna(0.0).astype(float)\n", ")\n", "df_final[\"ideal_idle_time_oph_day_slot\"] = (\n", "    df_final[\"ideal_idle_time_oph_day_slot\"].fillna(0.0).astype(float)\n", ")\n", "df_final[\"theo_util_oph_week_dd_corrected\"] = (\n", "    df_final[\"theo_util_oph_week_dd_corrected\"].fillna(0.0).astype(float)\n", ")\n", "df_final[\"theo_util_ideal_oph_day_slot\"] = (\n", "    df_final[\"theo_util_ideal_oph_day_slot\"].fillna(0.0).astype(float)\n", ")\n", "df_final[\"oph_week_number\"] = df_final[\"oph_week_number\"].fillna(0).astype(int)\n", "df_final[\"oph_week_start_date\"] = pd.to_datetime(df_final[\"oph_week_start_date\"], errors=\"coerce\")\n", "df_final[\"dd_correction_factor\"] = df_final[\"dd_correction_factor\"].fillna(0.0).astype(float)\n", "df_final[\"perc_change_theo_oph_dd\"] = df_final[\"perc_change_theo_oph_dd\"].fillna(0.0).astype(float)\n", "df_final[\"perc_change_theo_oph_it\"] = df_final[\"perc_change_theo_oph_it\"].fillna(0.0).astype(float)\n", "df_final[\"multiplier\"] = df_final[\"multiplier\"].fillna(0.0).astype(float)\n", "df_final[\"optimal_oph\"] = df_final[\"optimal_oph\"].fillna(0.0).astype(float)\n", "df_final[\"optimal_oph_corrected_dd\"] = (\n", "    df_final[\"optimal_oph_corrected_dd\"].fillna(0.0).astype(float)\n", ")\n", "df_final[\"final_idle_time_correction_factor\"] = (\n", "    df_final[\"final_idle_time_correction_factor\"].fillna(0.0).astype(float)\n", ")\n", "df_final[\"optimal_oph_corrected_idle_time\"] = (\n", "    df_final[\"optimal_oph_corrected_idle_time\"].fillna(0.0).astype(float)\n", ")\n", "df_final[\"slot_level\"] = df_final[\"slot_level\"].astype(str)\n", "df_final[\"Final_OPH_dh_bucket\"] = df_final[\"Final_OPH_dh_bucket\"].astype(str)\n", "df_final[\"updated_on\"] = pd.to_datetime(df_final[\"updated_on\"], errors=\"coerce\")"]}, {"cell_type": "code", "execution_count": null, "id": "17f4f0a7-7e65-4c68-afe7-df1ab001fa7c", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"ideal_oph_85_dh_slot_store_level_metrics\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"blinkit_store_name\", \"type\": \"varchar\", \"description\": \"blinkit_store_name\"},\n", "        {\"name\": \"runnr_city\", \"type\": \"varchar\", \"description\": \"runnr_city\"},\n", "        {\"name\": \"slot\", \"type\": \"varchar\", \"description\": \"slot\"},\n", "        {\"name\": \"store_id\", \"type\": \"integer\", \"description\": \"store_id\"},\n", "        {\"name\": \"logins_last_week\", \"type\": \"double\", \"description\": \"logins_last_week\"},\n", "        {\"name\": \"orders_last_week\", \"type\": \"double\", \"description\": \"orders_last_week\"},\n", "        {\"name\": \"blinkit_store_id\", \"type\": \"integer\", \"description\": \"blinkit_store_id\"},\n", "        {\"name\": \"perc_DS97_util\", \"type\": \"double\", \"description\": \"perc_ds97_util\"},\n", "        {\"name\": \"perc_DS94_util\", \"type\": \"double\", \"description\": \"perc_ds94_util\"},\n", "        {\"name\": \"perc_DS90_util\", \"type\": \"double\", \"description\": \"perc_ds90_util\"},\n", "        {\"name\": \"perc_DS85_util\", \"type\": \"double\", \"description\": \"perc_ds85_util\"},\n", "        {\"name\": \"perc_DS80_util\", \"type\": \"double\", \"description\": \"perc_ds80_util\"},\n", "        {\"name\": \"perc_DS70_util\", \"type\": \"double\", \"description\": \"perc_ds70_util\"},\n", "        {\"name\": \"perc_DS60_util\", \"type\": \"double\", \"description\": \"perc_ds60_util\"},\n", "        {\"name\": \"perc_DS50_util\", \"type\": \"double\", \"description\": \"perc_ds50_util\"},\n", "        {\"name\": \"DS97_instances\", \"type\": \"integer\", \"description\": \"ds97_instances\"},\n", "        {\"name\": \"DS94_instances\", \"type\": \"integer\", \"description\": \"ds94_instances\"},\n", "        {\"name\": \"DS90_instances\", \"type\": \"integer\", \"description\": \"ds90_instances\"},\n", "        {\"name\": \"DS85_instances\", \"type\": \"integer\", \"description\": \"ds85_instances\"},\n", "        {\"name\": \"DS80_instances\", \"type\": \"integer\", \"description\": \"ds80_instances\"},\n", "        {\"name\": \"DS70_instances\", \"type\": \"integer\", \"description\": \"ds70_instances\"},\n", "        {\"name\": \"DS60_instances\", \"type\": \"integer\", \"description\": \"ds60_instances\"},\n", "        {\"name\": \"DS50_instances\", \"type\": \"integer\", \"description\": \"ds50_instances\"},\n", "        {\n", "            \"name\": \"theo_rider_overall_OPH\",\n", "            \"type\": \"double\",\n", "            \"description\": \"theo_rider_overall_oph\",\n", "        },\n", "        {\"name\": \"OPH\", \"type\": \"double\", \"description\": \"oph\"},\n", "        {\"name\": \"OPH_date\", \"type\": \"date\", \"description\": \"oph_date\"},\n", "        {\"name\": \"dh_last_week\", \"type\": \"double\", \"description\": \"dh_last_week\"},\n", "        {\"name\": \"oph_last_week\", \"type\": \"double\", \"description\": \"oph_last_week\"},\n", "        {\"name\": \"dd_avg_last_week\", \"type\": \"double\", \"description\": \"dd_avg_last_week\"},\n", "        {\"name\": \"dd_avg_oph_week\", \"type\": \"double\", \"description\": \"dd_avg_oph_week\"},\n", "        {\"name\": \"log_orders_oph_week\", \"type\": \"integer\", \"description\": \"log_orders_oph_week\"},\n", "        {\n", "            \"name\": \"delivered_orders_oph_week\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"delivered_orders_oph_week\",\n", "        },\n", "        {\n", "            \"name\": \"store_handshake_time_oph_week\",\n", "            \"type\": \"double\",\n", "            \"description\": \"store_handshake_time_oph_week\",\n", "        },\n", "        {\n", "            \"name\": \"enroute_to_doorstep_oph_week\",\n", "            \"type\": \"double\",\n", "            \"description\": \"enroute_to_doorstep_oph_week\",\n", "        },\n", "        {\n", "            \"name\": \"doorstep_to_delivered_oph_week\",\n", "            \"type\": \"double\",\n", "            \"description\": \"doorstep_to_delivered_oph_week\",\n", "        },\n", "        {\n", "            \"name\": \"total_return_time_oph_week\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_return_time_oph_week\",\n", "        },\n", "        {\n", "            \"name\": \"total_idle_time_oph_week\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_idle_time_oph_week\",\n", "        },\n", "        {\"name\": \"wait_time_oph_week\", \"type\": \"double\", \"description\": \"wait_time_oph_week\"},\n", "        {\n", "            \"name\": \"total_time_avg_oph_week\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_time_avg_oph_week\",\n", "        },\n", "        {\"name\": \"theo_util_oph_week\", \"type\": \"double\", \"description\": \"theo_util_oph_week\"},\n", "        {\n", "            \"name\": \"log_orders_oph_day_slot\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"log_orders_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"delivered_orders_oph_day_slot\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"delivered_orders_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"store_handshake_time_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"store_handshake_time_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"enroute_to_doorstep_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"enroute_to_doorstep_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"doorstep_to_delivered_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"doorstep_to_delivered_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"total_return_time_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_return_time_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"total_idle_time_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_idle_time_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"wait_time_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"wait_time_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"total_time_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_time_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"total_time_avg_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_time_avg_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"theo_util_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"theo_util_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"final_dd_correction_factor\",\n", "            \"type\": \"double\",\n", "            \"description\": \"final_dd_correction_factor\",\n", "        },\n", "        {\"name\": \"dh_oph_day_slot\", \"type\": \"double\", \"description\": \"dh_oph_day_slot\"},\n", "        {\n", "            \"name\": \"idle_time_ratio_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"idle_time_ratio_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"ideal_idle_time_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"ideal_idle_time_oph_day_slot\",\n", "        },\n", "        {\n", "            \"name\": \"theo_util_oph_week_dd_corrected\",\n", "            \"type\": \"double\",\n", "            \"description\": \"theo_util_oph_week_dd_corrected\",\n", "        },\n", "        {\n", "            \"name\": \"theo_util_ideal_oph_day_slot\",\n", "            \"type\": \"double\",\n", "            \"description\": \"theo_util_ideal_oph_day_slot\",\n", "        },\n", "        {\"name\": \"oph_week_number\", \"type\": \"integer\", \"description\": \"oph_week_number\"},\n", "        {\"name\": \"oph_week_start_date\", \"type\": \"date\", \"description\": \"oph_week_start_date\"},\n", "        {\"name\": \"dd_correction_factor\", \"type\": \"double\", \"description\": \"dd_correction_factor\"},\n", "        {\n", "            \"name\": \"perc_change_theo_oph_dd\",\n", "            \"type\": \"double\",\n", "            \"description\": \"perc_change_theo_oph_dd\",\n", "        },\n", "        {\n", "            \"name\": \"perc_change_theo_oph_it\",\n", "            \"type\": \"double\",\n", "            \"description\": \"perc_change_theo_oph_it\",\n", "        },\n", "        {\"name\": \"multiplier\", \"type\": \"double\", \"description\": \"multiplier\"},\n", "        {\"name\": \"optimal_oph\", \"type\": \"double\", \"description\": \"optimal_oph\"},\n", "        {\n", "            \"name\": \"optimal_oph_corrected_dd\",\n", "            \"type\": \"double\",\n", "            \"description\": \"optimal_oph_corrected_dd\",\n", "        },\n", "        {\n", "            \"name\": \"final_idle_time_correction_factor\",\n", "            \"type\": \"double\",\n", "            \"description\": \"final_idle_time_correction_factor\",\n", "        },\n", "        {\n", "            \"name\": \"optimal_oph_corrected_idle_time\",\n", "            \"type\": \"double\",\n", "            \"description\": \"optimal_oph_corrected_idle_time\",\n", "        },\n", "        {\"name\": \"slot_level\", \"type\": \"varchar\", \"description\": \"slot_level\"},\n", "        {\"name\": \"Final_OPH_dh_bucket\", \"type\": \"varchar\", \"description\": \"final_oph_dh_bucket\"},\n", "        {\"name\": \"updated_on\", \"type\": \"date\", \"description\": \"updated_on\"},\n", "    ],\n", "    \"primary_key\": [\"store_id\", \"slot\", \"slot_level\", \"final_oph_dh_bucket\", \"updated_on\"],\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"This table contains the ideal OPH for stores at multiple slot level at 85-90 DH\",\n", "}\n", "pb.to_trino(df_final, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "d5af8470-6cb5-4c2d-a580-a5d26f8e8dc9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}