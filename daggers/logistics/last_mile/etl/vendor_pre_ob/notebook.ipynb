{"cells": [{"cell_type": "code", "execution_count": null, "id": "2462e4fd-b31a-4cbc-bae3-35d8ddc109c2", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import pencilbox as pb\n", "import numpy as np\n", "import itertools\n", "import datetime as dt\n", "from datetime import datetime\n", "from datetime import date\n", "from datetime import timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "c0daeb82-c654-41fc-9d23-548a6086cf49", "metadata": {}, "outputs": [], "source": ["sql_query = f\"\"\"\n", "\n", "with vendor_list as (\n", "select \n", "creator_id,\n", "max_by(vendor_name,dt) as vendor_name,\n", "max_by(tag,dt) as tag\n", "from blinkit.logistics_data_etls.blinkit_runnr_buddy_vendors\n", "group by 1),\n", "\n", "new_lms as (\n", "select \n", "distinct\n", "lead_id,\n", "split_part(pk,'ld#1#',2) as pk,\n", "cast(from_unixtime(created_at, 'Asia/Kolkata') as timestamp) as created_at,\n", "cast(from_unixtime(updated_at, 'Asia/Kolkata') as timestamp) as updated_at,\n", "lead_type,\n", "name,\n", "source,\n", "source_id,\n", "status,\n", "-- cast(lead_info.driver_lead_info as json),\n", "cast(json_extract(cast(lead_info.driver_lead_info as json),'$.sub_source') as varchar) as sub_source,\n", "cast(json_extract(cast(lead_info.driver_lead_info as json),'$.carrier_id') as int) as carrier_id,\n", "cast(json_extract(cast(lead_info.driver_lead_info as json),'$.utm_source') as varchar) as utm_source,\n", "cast(json_extract(cast(lead_info.driver_lead_info as json),'$.utm_medium') as varchar) as utm_medium,\n", "cast(json_extract(cast(lead_info.driver_lead_info as json),'$.utm_campaign') as var<PERSON>r) as utm_campaign,\n", "cast(json_extract(cast(location_details as json),'$.city_name') as varchar) as city_name,\n", "cast(json_extract(cast(lead_info.driver_lead_info as json),'$.business') as varchar) as business,\n", "cast(from_unixtime(cast(converted_date as int), 'Asia/Kolkata') as timestamp) as converted_date,\n", "cast(json_extract(cast(lead_info.driver_lead_info AS json),'$.appsflyer_data.media_source') as varchar) AS media_source,\n", "converted_id,\n", "phone_number,\n", "onboarding_id,\n", "cast(json_extract(cast(lead_info.driver_lead_info as json),'$.is_auto_converted') as varchar) as is_auto_converted,\n", "cast(cast(json_extract(cast(lead_info.driver_lead_info AS JSON), '$.appsflyer_data.app_install_timestamp')AS varchar) AS TIMESTAMP) + INTERVAL '5' HOUR + INTERVAL '30' MINUTE AS app_install_timestamp,\n", "cast(cast(json_extract(cast(lead_info.driver_lead_info AS JSON), '$.appsflyer_data.attribution_timestamp')AS varchar) AS TIMESTAMP) + INTERVAL '5' HOUR + INTERVAL '30' MINUTE AS attribution_timestamp,\n", "row_number() over (partition by phone_number order by created_at desc) rnk\n", "from zomato.dynamodb.prod_lead_management_service l\n", "where dt >= date_format(current_date - interval '30' day, '%%Y%%m%%d') \n", "and cast(from_unixtime(created_at, 'Asia/Kolkata') as timestamp) >= (current_date - interval '31' day)\n", "and client_id = '1'\n", "and pk like 'ld#%%'\n", "and l.status not in ('LEAD_STATUS_CONVERTED','LEAD_STATUS_EXPIRED')\n", "),\n", "\n", "store_mapping as (\n", "select \n", "store_id, \n", "max_by(blinkit_store_id, inserted_at_ist) as blinkit_store_id, \n", "max_by(runnr_city, inserted_at_ist) as runnr_city,\n", "max_by(blinkit_store_name, inserted_at_ist) as blinkit_store_name\n", "from logistics_data_etls.blinkit_store_mapping\n", "group by 1)\n", ",\n", "\n", "base as (\n", "  \n", "  select \n", "    a.ob_id,\n", "    date(cast(SUBSTRING(replace(a.created_at,'T',' '),1,19) as timestamp) + interval '330' minute) as ob_id_created_date,\n", "    date(cast(SUBSTRING(replace(a.updated_at,'T',' '),1,19) as timestamp) + interval '330' minute) as ob_id_updated_date,\n", "    a.driver_id,\n", "    sm.blinkit_store_id as blinkit_store_id,\n", "    sm.blinkit_store_name as blinkit_store_name,\n", "    sm.runnr_city as runnr_city,\n", "    carrier.carrier_id as vehicle,\n", "    business_details.store_id as store_assign,\n", "    pan_details as pan_details,\n", "    bank_details as bank_details,\n", "    profile_details.image_url as profile_image,\n", "    payment_details.security_amount as ob_fee,\n", "    -- v.vendor_name,\n", "    \n", "    l1.source as source,\n", "l1.source_id as creator,\n", "(l1.created_at) as lead_date, \n", "cast(l1.lead_id as varchar) as lead_id, \n", "l1.name as name,\n", "v1.vendor_name as vendor_name,\n", "v1.tag as tag\n", "    \n", "  from \n", "  zomato.mongo_driver_onboarding_service_production.mongo_v3_onboarding_applications as a \n", "  left join new_lms l1 on l1.onboarding_id = a.ob_id\n", "    and a.dt <> '0' \n", "    and date(SUBSTR(cast(a.updated_at as varchar), 1, 10)) >= cast(l1.created_at + interval '330' minute as date) \n", "  left join vendor_list v1 on (case when try_cast(v1.creator_id as int) is not null then v1.creator_id = l1.source_id else v1.creator_id = l1.sub_source end)\n", "  left join zomato.carthero_prod.cities c on l1.city_name = c.city_name\n", "  left join store_mapping sm on sm.store_id = business_details.store_id\n", "  where l1.rnk = 1 \n", "    and ((lower(l1.business) like '%%blinkit%%'))\n", "    and payment_details.security_amount is null\n", "    group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20\n", "),\n", "\n", "\n", "final as (\n", "select\n", "lead_date,\n", "lead_id,\n", "creator,\n", "name,\n", "source,\n", "\n", "runnr_city as city_name,\n", "date_diff('day',lead_date,current_date) as lead_ageing_days,\n", "date(ob_id_created_date) as app_download_date,\n", "date_diff('day',ob_id_created_date,current_date) as in_app_ageing_days,\n", "date(ob_id_updated_date) as last_app_section_update_date,\n", "\n", "blinkit_store_name as store,\n", "\n", "case when ob_id is not null then 1 else 0 end as signup,\n", "case when vehicle is not null then 1 else 0 end as vehicle,\n", "\n", "case when store_assign is not null then 1 else 0 end as store_selected,\n", "case when pan_details is not null then 1 else 0 end as pan_details,\n", "\n", "case when bank_details is not null then 1 else 0 end as bank_details,\n", "case when profile_image is not null then 1 else 0 end as selfie,\n", "\n", "case when ob_fee is not null then 1 else 0 end as ob_fee_paid,\n", "\n", "case \n", "when bank_details is not null and (profile_image is null or ob_fee is null) then 'P0'\n", "when vehicle is not null and (bank_details is null or pan_details is null) then 'P1'\n", "when ob_id is not null and vehicle is null then 'P2'\n", "when ob_id is null then 'P3'\n", "when ob_fee is not null then 'OB-fees-paid'\n", "end as calling_priority,\n", "\n", "vendor_name\n", "\n", "from base b \n", "where ob_fee is null\n", "order by 19 asc, 1 desc)\n", "\n", "select * from final\n", "where\n", " case \n", "    when (lead_date >= current_date - interval '30' day) and calling_priority IN ('P0','P1','P2') then 1\n", "    when (lead_date >= current_date - interval '2' day) and calling_priority IN ('P3') then 1\n", "    else 0\n", " end = 1\n", " \n", " \"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "9c82ae26-8476-45c2-b947-bba8d340df2c", "metadata": {}, "outputs": [], "source": ["red_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "43976a4a-91f8-4a0e-9d7e-235626b26247", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"lm_vendor_pre_ob_v1\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"lead_date\", \"type\": \"timestamp\", \"description\": \"lead_creation_time\"},\n", "        {\"name\": \"lead_id\", \"type\": \"varchar\", \"description\": \"unique_lead_id\"},\n", "        {\"name\": \"creator\", \"type\": \"varchar\", \"description\": \"lead_creator_id\"},\n", "        {\"name\": \"name\", \"type\": \"varchar\", \"description\": \"lead_name\"},\n", "        {\"name\": \"source\", \"type\": \"varchar\", \"description\": \"lead_source\"},\n", "        {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"city_name\"},\n", "        {\"name\": \"lead_ageing_days\", \"type\": \"integer\", \"description\": \"days_since_lead_created\"},\n", "        {\"name\": \"app_download_date\", \"type\": \"date\", \"description\": \"app_download_date\"},\n", "        {\"name\": \"in_app_ageing_days\", \"type\": \"integer\", \"description\": \"days_since_app_download\"},\n", "        {\n", "            \"name\": \"last_app_section_update_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"last_update_in_app\",\n", "        },\n", "        {\"name\": \"store\", \"type\": \"varchar\", \"description\": \"store_selected\"},\n", "        {\"name\": \"signup\", \"type\": \"integer\", \"description\": \"is_signed_up\"},\n", "        {\"name\": \"vehicle\", \"type\": \"integer\", \"description\": \"vehicle_provided\"},\n", "        {\"name\": \"store_selected\", \"type\": \"integer\", \"description\": \"is_store_selected\"},\n", "        {\"name\": \"pan_details\", \"type\": \"integer\", \"description\": \"pan_verification_status\"},\n", "        {\"name\": \"bank_details\", \"type\": \"integer\", \"description\": \"bank_verification_status\"},\n", "        {\"name\": \"selfie\", \"type\": \"integer\", \"description\": \"is_selfie_uploaded\"},\n", "        {\"name\": \"ob_fee_paid\", \"type\": \"integer\", \"description\": \"onboarding_fee_paid\"},\n", "        {\"name\": \"calling_priority\", \"type\": \"varchar\", \"description\": \"lead_calling_priority\"},\n", "        {\"name\": \"vendor_name\", \"type\": \"varchar\", \"description\": \"vendor_name\"},\n", "    ],\n", "    \"primary_key\": [\"lead_id\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"This Table contains data for vendor pre ob\",\n", "}\n", "pb.to_trino(sql_query, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "7d25b9e4-ec1b-4e7e-a2e4-c7d9d4fe6409", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}