alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: new_cpk_temp
dag_type: etl
escalation_priority: low
execution_timeout: 800
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: logistics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U060684S18V
path: logistics/last_mile/etl/new_cpk_temp
paused: false
pool: logistics_pool
project_name: last_mile
schedule:
  end_date: '2025-09-12T00:00:00'
  interval: 0 4 * * *
  start_date: '2024-10-17T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
