alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: superset_agg_weekly_last_mile_longtail_metrics
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: logistics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U06SQT8NR3L
path: logistics/last_mile/etl/superset_agg_weekly_last_mile_longtail_metrics
paused: false
pool: logistics_pool
project_name: last_mile
schedule:
  end_date: '2025-09-02T00:00:00'
  interval: 0 3 * * *
  start_date: '2024-12-05T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
