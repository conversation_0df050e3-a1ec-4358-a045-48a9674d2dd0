{"cells": [{"cell_type": "code", "execution_count": null, "id": "c05988f3-1b5f-4b4c-bb02-49a2ce39e5a1", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "id": "9cf3c217-6be3-41c6-a3a8-0bfd3b4528cf", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import math\n", "from datetime import datetime, timedelta, date\n", "\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "conn = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "59337887-90e9-4907-9272-ded0e6f6c0f2", "metadata": {}, "outputs": [], "source": ["!pip install pandasql"]}, {"cell_type": "code", "execution_count": null, "id": "f5ad8091-ea89-4515-a67a-bdb1085ffe0a", "metadata": {}, "outputs": [], "source": ["sheet_id = \"14KybKiqvReZcyFa1rjdKiSswuW-YL0gq2oXUgAuxULY\"\n", "sheet_name = \"input\"\n", "df = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "f45953a9-8b64-4048-990c-5c462417f670", "metadata": {}, "outputs": [], "source": ["df[\"blinkit_store_id\"] = df[\"blinkit_store_id\"].fillna(0).astype(int)\n", "df[\"store_cohort\"] = df[\"store_cohort\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "12223a26-8c81-4d03-9d3c-dbe69a3f43f0", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"store_cohorts\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"blinkit_store_id\", \"type\": \"integer\", \"description\": \"blinkit_store_id\"},\n", "        {\"name\": \"store_cohort\", \"type\": \"varchar\", \"description\": \"store_cohort\"},\n", "    ],\n", "    \"primary_key\": [\"blinkit_store_id\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"This table contains the store cohorts\",\n", "}\n", "pb.to_trino(df, **kwargs_trino)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}