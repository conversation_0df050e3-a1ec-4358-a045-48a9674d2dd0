alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: ideal_oph_dh_slot_store_level_metrics
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: logistics
notebook:
  parameters: null
  priority_weight: 3
  retries: 2
  retry_delay_in_seconds: 15
owner:
  email: <EMAIL>
  slack_id: U087G28D7L4
path: logistics/last_mile/etl/ideal_oph_dh_slot_store_level_metrics
paused: false
pool: logistics_pool
project_name: last_mile
schedule:
  end_date: '2025-09-01T00:00:00'
  interval: 0 1 * * *
  start_date: '2025-06-05T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
