{"cells": [{"cell_type": "code", "execution_count": null, "id": "115e5458-7a78-4e4a-8738-f03e81409513", "metadata": {}, "outputs": [], "source": ["!pip install tabulate\n", "!pip install matplotlib\n", "!pip install pyauto<PERSON>i\n", "!pip install --upgrade Pillow\n", "!pip install numpy==1.26.4\n", "!pip install slack-sdk\n", "import numpy as np\n", "\n", "print(np.__version__)\n", "import pandas as pd\n", "import pencilbox as pb\n", "from tabulate import tabulate\n", "import textwrap as twp\n", "import time\n", "import PIL\n", "import matplotlib.pyplot as plt\n", "from pencilbox.connections import get_slack_client\n", "import os\n", "\n", "codsworth_client = get_slack_client(\"bl-analytics-bot\")"]}, {"cell_type": "code", "execution_count": null, "id": "53f2da5f-b820-469c-bd12-2c852c3b2bb7", "metadata": {}, "outputs": [], "source": ["trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "55a54b72-8069-4318-9d83-9f56baeb698a", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "\n", "-- https://docs.google.com/spreadsheets/d/1ifULeOaUFGbP_VBRV5HSCPZTfdIlz4SIe7XhHpyGyqQ/edit?gid=0#gid=0\n", "\n", "with hourly as(With orders as(\n", "Select snapshot_hour_ist,\n", "snapshot_date_ist,\n", "city,\n", "sum(rider_login_min)*1.00/60 as login_hours\n", "from storeops_etls.agg_hourly_snapshot_summary\n", "where snapshot_date_ist in (current_date,current_date - interval '7' day,current_date - interval '14' day)\n", "and snapshot_hour_ist = (select max(hour(order_checkout_ts_ist) - 1)\n", "from dwh.fact_supply_chain_order_details\n", "where order_checkout_dt_ist in (current_date)\n", ")\n", "group by 1,2,3\n", "\n", "-- union \n", "\n", "-- Select snapshot_hour_ist,\n", "-- snapshot_date_ist,\n", "-- 'Overall' as city,\n", "-- sum(rider_login_min)*1.00/60 as login_hours,\n", "-- sum(delivered_order_count) as delivered_order_count\n", "-- from storeops_etls.agg_hourly_snapshot_summary\n", "-- where snapshot_date_ist in (current_date,current_date - interval '7' day,current_date - interval '14' day)\n", "-- and snapshot_hour_ist = (select max(hour(order_checkout_ts_ist) - 1)\n", "-- from dwh.fact_supply_chain_order_details\n", "-- where order_checkout_dt_ist in (current_date)\n", "-- )\n", "-- group by 1,2,3\n", ")\n", ",surge_seen as(\n", "Select \n", "date(date) as date_,\n", "hour,\n", "sm.city as city,\n", "sum(carts) as carts,\n", "sum(gsp_surge_carts + gsp_and_picker_surge_carts) as surge_carts\n", "from serviceability_etls.surge_seen_hourly s \n", "left join (Select distinct merchant_id, city from storeops_etls.agg_hourly_snapshot_summary\n", "where snapshot_date_ist in (current_date,current_date - interval '7' day,current_date - interval '14' day)\n", ")sm on s.merchant_id = sm.merchant_id\n", "where date in (cast(current_Date as varchar),cast(current_Date - interval '7' day as varchar),cast(current_Date - interval '14' day as varchar))\n", "and hour = (select max(hour(order_checkout_ts_ist) - 1)\n", "from dwh.fact_supply_chain_order_details\n", "where order_checkout_dt_ist in (current_date)\n", ")\n", "group by 1,2,3\n", "\n", "-- union \n", "\n", "-- Select \n", "-- date(date) as date_,\n", "-- hour,\n", "-- 'Overall' as city,\n", "-- sum(carts) as carts,\n", "-- sum(surge_carts) as surge_carts\n", "-- from serviceability_etls.surge_seen_hourly s \n", "-- where date in (cast(current_Date as varchar),cast(current_Date - interval '7' day as varchar),cast(current_Date - interval '14' day as varchar))\n", "-- and hour = (select max(hour(order_checkout_ts_ist) - 1)\n", "-- from dwh.fact_supply_chain_order_details\n", "-- where order_checkout_dt_ist in (current_date)\n", "-- )\n", "-- group by 1,2,3\n", ")\n", ",fact as(\n", "Select \n", "order_checkout_dt_ist,\n", "hour(order_checkout_ts_ist) as hour_,\n", "sm.city as city,\n", "count(distinct fsc.order_id ) as del_order_count,\n", "count(case when is_batched_order = true then fsc.order_id end) as batched_orders,\n", "count(case when order_billing_completed_ts_ist >= order_partner_assigned_ts_ist then fsc.order_id end) as dh_orders,\n", "count(distinct case when coalesce(y.surge_pay,0) > 0 then fsc.order_id end) as rain_orders\n", "from dwh.fact_supply_chain_order_details fsc \n", "left join (Select distinct merchant_id, city from storeops_etls.agg_hourly_snapshot_summary\n", "where snapshot_date_ist in (current_date,current_date - interval '7' day,current_date - interval '14' day)\n", ")sm on fsc.frontend_merchant_id = sm.merchant_id\n", "left join (\n", "    select \n", "    e.ref_id trip_id,\n", "    entity_id as order_id,\n", "    max_by(cast(json_extract(cast(ledger_breakups as json),'$[0].amount') as integer),time) as surge_pay,\n", "    max_by(cast(json_extract(cast(ledger_breakups as json),'$[0].rate_card.id') as varchar),time) as rain_rate_card_id,\n", "    max(rain_level) as rain_level\n", "    from zomato.jumbo2.logistics_accounting_event_ledger e\n", "    cross join unnest(reports) as report\n", "    cross join unnest(report.accounting_info.rain_info) as t (order_id, location, merchant, rain_levels) \n", "    cross join unnest(rain_levels) as level (rain_level)\n", "    where dt in (date_format(current_date - interval '0' day, '%%Y%%m%%d'),date_format(current_date - interval '7' day, '%%Y%%m%%d'),date_format(current_date - interval '14' day, '%%Y%%m%%d'))\n", "    and business = 'blinkit'\n", "    and type = 'rain_report'\n", "    and trip_details.order_status = 'completed'\n", "    group by 1,2\n", ") y on fsc.trip_id = y.trip_id\n", "where order_Checkout_dt_ist in (current_date,current_date - interval '7' day,current_date - interval '14' day)\n", "and hour(order_checkout_ts_ist) = (select max(hour(order_checkout_ts_ist) - 1)\n", "from dwh.fact_supply_chain_order_details\n", "where order_checkout_dt_ist in (current_date)\n", "and order_current_status = 'DELIVERED' and order_type = 'RetailForwardOrder'\n", ")\n", "group by 1,2,3\n", "\n", "-- union \n", "\n", "-- Select \n", "-- order_checkout_dt_ist,\n", "-- hour(order_checkout_ts_ist) as hour_,\n", "-- 'Overall' as city,\n", "-- count(case when is_batched_order = true then fsc.order_id end) as batched_orders,\n", "-- count(case when order_billing_completed_ts_ist >= order_partner_assigned_ts_ist then fsc.order_id end) as dh_orders,\n", "-- count(distinct case when coalesce(y.surge_pay,0) > 0 then fsc.order_id end) as rain_orders\n", "-- from dwh.fact_supply_chain_order_details fsc \n", "-- left join (Select distinct merchant_id, city from storeops_etls.agg_hourly_snapshot_summary\n", "-- where snapshot_date_ist in (current_date,current_date - interval '7' day,current_date - interval '14' day)\n", "-- )sm on fsc.frontend_merchant_id = sm.merchant_id\n", "-- left join (\n", "--     select \n", "--     e.ref_id trip_id,\n", "--     entity_id as order_id,\n", "--     max_by(cast(json_extract(cast(ledger_breakups as json),'$[0].amount') as integer),time) as surge_pay,\n", "--     max_by(cast(json_extract(cast(ledger_breakups as json),'$[0].rate_card.id') as varchar),time) as rain_rate_card_id,\n", "--     max(rain_level) as rain_level\n", "--     from zomato.jumbo2.logistics_accounting_event_ledger e\n", "--     cross join unnest(reports) as report\n", "--     cross join unnest(report.accounting_info.rain_info) as t (order_id, location, merchant, rain_levels) \n", "--     cross join unnest(rain_levels) as level (rain_level)\n", "--     where dt in (date_format(current_date - interval '0' day, '%%Y%%m%%d'),date_format(current_date - interval '7' day, '%%Y%%m%%d'),date_format(current_date - interval '14' day, '%%Y%%m%%d'))\n", "--     and business = 'blinkit'\n", "--     and type = 'rain_report'\n", "--     and trip_details.order_status = 'completed'\n", "--     group by 1,2\n", "-- ) y on fsc.trip_id = y.trip_id\n", "-- where order_Checkout_dt_ist in (current_date,current_date - interval '7' day,current_date - interval '14' day)\n", "-- and hour(order_checkout_ts_ist) = (select max(hour(order_checkout_ts_ist) - 1)\n", "-- from dwh.fact_supply_chain_order_details\n", "-- where order_checkout_dt_ist in (current_date)\n", "-- and order_current_status = 'DELIVERED' and order_type = 'RetailForwardOrder'\n", "-- )\n", "-- group by 1,2,3\n", ")\n", "Select o1.*,\n", "s1.carts,\n", "s1.surge_carts,\n", "f1.del_order_count,\n", "f1.batched_orders,\n", "f1.dh_orders,\n", "f1.rain_orders\n", "from orders o1\n", "left join surge_seen s1 on s1.city = o1.city and s1.date_ = o1.snapshot_date_ist and s1.city is not null\n", "left join fact f1 on f1.city = o1.city and f1.order_checkout_dt_ist = o1.snapshot_date_ist and f1.city is not null\n", "-- order by del_order_count desc\n", ")\n", "\n", "\n", ",till_hour as\n", "(With orders as(\n", "Select \n", "-1 as snapshot_hour_ist,\n", "snapshot_date_ist,\n", "city,\n", "sum(rider_login_min)*1.00/60 as login_hours\n", "from storeops_etls.agg_hourly_snapshot_summary\n", "where snapshot_date_ist in (current_date,current_date - interval '7' day,current_date - interval '14' day)\n", "and snapshot_hour_ist <= (select max(hour(order_checkout_ts_ist) - 1)\n", "from dwh.fact_supply_chain_order_details\n", "where order_checkout_dt_ist in (current_date)\n", ")\n", "group by 1,2,3\n", "\n", "-- union \n", "\n", "-- Select \n", "-- -1 as snapshot_hour_ist,\n", "-- snapshot_date_ist,\n", "-- 'Overall' as city,\n", "-- sum(rider_login_min)*1.00/60 as login_hours,\n", "-- sum(delivered_order_count) as delivered_order_count\n", "-- from storeops_etls.agg_hourly_snapshot_summary\n", "-- where snapshot_date_ist in (current_date,current_date - interval '7' day,current_date - interval '14' day)\n", "-- and snapshot_hour_ist <= (select max(hour(order_checkout_ts_ist) - 1)\n", "-- from dwh.fact_supply_chain_order_details\n", "-- where order_checkout_dt_ist in (current_date)\n", "-- )\n", "-- group by 1,2,3\n", ")\n", ",surge_seen as(\n", "Select \n", "date(date) as date_,\n", "-1 as hour,\n", "sm.city as city,\n", "sum(carts) as carts,\n", "sum(gsp_surge_carts + gsp_and_picker_surge_carts) as surge_carts\n", "from serviceability_etls.surge_seen_hourly s \n", "left join (Select distinct merchant_id, city from storeops_etls.agg_hourly_snapshot_summary\n", "where snapshot_date_ist in (current_date,current_date - interval '7' day,current_date - interval '14' day)\n", ")sm on s.merchant_id = sm.merchant_id\n", "where date in (cast(current_Date as varchar),cast(current_Date - interval '7' day as varchar),cast(current_Date - interval '14' day as varchar))\n", "and hour <= (select max(hour(order_checkout_ts_ist) - 1)\n", "from dwh.fact_supply_chain_order_details\n", "where order_checkout_dt_ist in (current_date)\n", ")\n", "group by 1,2,3\n", "\n", "-- union \n", "\n", "-- Select \n", "-- date(date) as date_,\n", "-- -1 as hour,\n", "-- 'Overall' as city,\n", "-- sum(carts) as carts,\n", "-- sum(surge_carts) as surge_carts\n", "-- from serviceability_etls.surge_seen_hourly s \n", "-- where date in (cast(current_Date as varchar),cast(current_Date - interval '7' day as varchar),cast(current_Date - interval '14' day as varchar))\n", "-- and hour <= (select max(hour(order_checkout_ts_ist) - 1)\n", "-- from dwh.fact_supply_chain_order_details\n", "-- where order_checkout_dt_ist in (current_date)\n", "-- )\n", "-- group by 1,2,3\n", ")\n", ",fact as(\n", "Select \n", "order_checkout_dt_ist,\n", "-1 as hour_,\n", "sm.city as city,\n", "count(distinct fsc.order_id ) as del_order_count,\n", "count(case when is_batched_order = true then fsc.order_id end) as batched_orders,\n", "count(case when order_billing_completed_ts_ist >= order_partner_assigned_ts_ist then fsc.order_id end) as dh_orders,\n", "count(distinct case when coalesce(y.surge_pay,0) > 0 then fsc.order_id end) as rain_orders\n", "from dwh.fact_supply_chain_order_details fsc \n", "left join (Select distinct merchant_id, city from storeops_etls.agg_hourly_snapshot_summary\n", "where snapshot_date_ist in (current_date,current_date - interval '7' day,current_date - interval '14' day)\n", ")sm on fsc.frontend_merchant_id = sm.merchant_id\n", "left join (\n", "    select \n", "    e.ref_id trip_id,\n", "    entity_id as order_id,\n", "    max_by(cast(json_extract(cast(ledger_breakups as json),'$[0].amount') as integer),time) as surge_pay,\n", "    max_by(cast(json_extract(cast(ledger_breakups as json),'$[0].rate_card.id') as varchar),time) as rain_rate_card_id,\n", "    max(rain_level) as rain_level\n", "    from zomato.jumbo2.logistics_accounting_event_ledger e\n", "    cross join unnest(reports) as report\n", "    cross join unnest(report.accounting_info.rain_info) as t (order_id, location, merchant, rain_levels) \n", "    cross join unnest(rain_levels) as level (rain_level)\n", "    where dt in (date_format(current_date - interval '0' day, '%%Y%%m%%d'),date_format(current_date - interval '7' day, '%%Y%%m%%d'),date_format(current_date - interval '14' day, '%%Y%%m%%d'))\n", "    and business = 'blinkit'\n", "    and type = 'rain_report'\n", "    and trip_details.order_status = 'completed'\n", "    group by 1,2\n", ") y on fsc.trip_id = y.trip_id\n", "where order_Checkout_dt_ist in (current_date,current_date - interval '7' day,current_date - interval '14' day)\n", "and hour(order_checkout_ts_ist) <= (select max(hour(order_checkout_ts_ist) - 1)\n", "from dwh.fact_supply_chain_order_details\n", "where order_checkout_dt_ist in (current_date)\n", "and order_current_status = 'DELIVERED' and order_type = 'RetailForwardOrder'\n", ")\n", "group by 1,2,3\n", "\n", "\n", "-- union \n", "\n", "-- Select \n", "-- order_checkout_dt_ist,\n", "-- -1 as hour_,\n", "-- sm.city as city,\n", "-- count(case when is_batched_order = true then fsc.order_id end) as batched_orders,\n", "-- count(case when order_billing_completed_ts_ist >= order_partner_assigned_ts_ist then fsc.order_id end) as dh_orders,\n", "-- count(distinct case when coalesce(y.surge_pay,0) > 0 then fsc.order_id end) as rain_orders\n", "-- from dwh.fact_supply_chain_order_details fsc \n", "-- left join (Select distinct merchant_id, city from storeops_etls.agg_hourly_snapshot_summary\n", "-- where snapshot_date_ist in (current_date,current_date - interval '7' day,current_date - interval '14' day)\n", "-- )sm on fsc.frontend_merchant_id = sm.merchant_id\n", "-- left join (\n", "--     select \n", "--     e.ref_id trip_id,\n", "--     entity_id as order_id,\n", "--     max_by(cast(json_extract(cast(ledger_breakups as json),'$[0].amount') as integer),time) as surge_pay,\n", "--     max_by(cast(json_extract(cast(ledger_breakups as json),'$[0].rate_card.id') as varchar),time) as rain_rate_card_id,\n", "--     max(rain_level) as rain_level\n", "--     from zomato.jumbo2.logistics_accounting_event_ledger e\n", "--     cross join unnest(reports) as report\n", "--     cross join unnest(report.accounting_info.rain_info) as t (order_id, location, merchant, rain_levels) \n", "--     cross join unnest(rain_levels) as level (rain_level)\n", "--     where dt in (date_format(current_date - interval '0' day, '%%Y%%m%%d'),date_format(current_date - interval '7' day, '%%Y%%m%%d'),date_format(current_date - interval '14' day, '%%Y%%m%%d'))\n", "--     and business = 'blinkit'\n", "--     and type = 'rain_report'\n", "--     and trip_details.order_status = 'completed'\n", "--     group by 1,2\n", "-- ) y on fsc.trip_id = y.trip_id\n", "-- where order_Checkout_dt_ist in (current_date,current_date - interval '7' day,current_date - interval '14' day)\n", "-- and hour(order_checkout_ts_ist) = (select max(hour(order_checkout_ts_ist) - 1)\n", "-- from dwh.fact_supply_chain_order_details\n", "-- where order_checkout_dt_ist in (current_date)\n", "-- and order_current_status = 'DELIVERED' and order_type = 'RetailForwardOrder'\n", "-- )\n", "-- group by 1,2,3\n", "\n", ")\n", "Select o1.*,\n", "s1.carts,\n", "s1.surge_carts,\n", "f1.del_order_count,\n", "f1.batched_orders,\n", "f1.dh_orders,\n", "f1.rain_orders\n", "from orders o1\n", "left join surge_seen s1 on s1.city = o1.city and s1.date_ = o1.snapshot_date_ist and s1.city is not null\n", "left join fact f1 on f1.city = o1.city and f1.order_checkout_dt_ist = o1.snapshot_date_ist and f1.city is not null\n", "-- order by del_order_count desc\n", ")\n", "Select a.*,coalesce(final_region,'Others') as final_region\n", "from (\n", "Select *, 'Hourly' as flag from hourly\n", "where city is not null\n", "union \n", "Select * , 'Till Hour' as flag from till_hour\n", "where city is not null\n", ") a\n", "left join (Select distinct pos_outlet_city_name,final_region from interim.blinkit_store_details_with_ceo_region_mapping) b on a.city = b.pos_outlet_city_name\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "5d44aa58-9e2a-496c-8394-075f9cf3bbac", "metadata": {}, "outputs": [], "source": ["data = pd.read_sql(query, trino_con)\n", "data\n", "pb.to_sheets(data, \"1FZPmqNE1wqnd2Xt4okRgk3F3KYE2P68RYtQ7RTY5kRo\", \"Raw\")"]}, {"cell_type": "code", "execution_count": null, "id": "6a91996b-fbf0-41e6-9a6a-211a150dfb93", "metadata": {}, "outputs": [], "source": ["time.sleep(20)"]}, {"cell_type": "code", "execution_count": null, "id": "32051f6f-db74-4d9b-a2fe-a64b2cf8455d", "metadata": {}, "outputs": [], "source": ["# Load the data\n", "df = pb.from_sheets(\n", "    \"1FZPmqNE1wqnd2Xt4okRgk3F3KYE2P68RYtQ7RTY5kRo\",  # Replace with your Google Sheets ID\n", "    \"Cluster-View-Hourly\",  # Replace with your actual sheet name\n", "    clear_cache=True,\n", ")\n", "\n", "# Load the data\n", "df1 = pb.from_sheets(\n", "    \"1FZPmqNE1wqnd2Xt4okRgk3F3KYE2P68RYtQ7RTY5kRo\",  # Replace with your Google Sheets ID\n", "    \"Cluster-View-Till_Hour\",  # Replace with your actual sheet name\n", "    clear_cache=True,\n", ")\n", "\n", "\n", "# df = pd.read_csv(\"hourly.csv\")  # Replace with actual file\n", "df.fillna(\"\", inplace=True)\n", "hour_value = df.iloc[0, 0]\n", "\n", "df = df.iloc[0:39, 1:17]  # Columns A1 to I51\n", "\n", "\n", "# df1 = pd.read_csv(\"tillhour.csv\")  # Replace with actual file\n", "df1.fillna(\"\", inplace=True)\n", "hour_value = df1.iloc[0, 0]\n", "\n", "df1 = df1.iloc[0:39, 1:17]  # Columns A1 to I51"]}, {"cell_type": "code", "execution_count": null, "id": "d7eb4f87-f945-4fec-a68c-c680162c4a60", "metadata": {}, "outputs": [], "source": ["if not df.empty:\n", "\n", "    def render_mpl_table(\n", "        data,\n", "        col_width=3.0,\n", "        row_height=0.625,\n", "        font_size=12,\n", "        header_color=\"#F7D046\",\n", "        row_colors=[\"#f5f5f5\", \"w\"],\n", "        edge_color=\"grey\",\n", "        bbox=[0, 0, 1, 1],\n", "        header_columns=0,\n", "        cell_loc=\"center\",\n", "        conditional_formatting=None,\n", "        title=None,\n", "        **kwargs\n", "    ):\n", "        fig, ax = plt.subplots(\n", "            figsize=(\n", "                (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "            )\n", "        )\n", "        ax.axis(\"off\")\n", "\n", "        mpl_table = ax.table(\n", "            cellText=data.values, bbox=bbox, colLabels=data.columns, cellLoc=cell_loc, **kwargs\n", "        )\n", "        mpl_table.auto_set_font_size(False)\n", "        mpl_table.set_fontsize(font_size)\n", "        mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "\n", "        for k, cell in mpl_table._cells.items():\n", "            row, col = k\n", "            cell.set_edgecolor(edge_color)\n", "            if row == 0:\n", "                cell.set_text_props(weight=\"bold\", color=\"#1B5E20\")\n", "                cell.set_facecolor(header_color)\n", "            else:\n", "                cell.set_facecolor(row_colors[row % len(row_colors)])\n", "                if col == 0:\n", "                    cell.set_text_props(weight=\"bold\")\n", "                if conditional_formatting and data.columns[col] in conditional_formatting:\n", "                    formatting_func = conditional_formatting[data.columns[col]]\n", "                    cell_val = data.iloc[row - 1, col]\n", "                    cell.set_facecolor(formatting_func(cell_val))\n", "\n", "        if title:\n", "            fig.text(\n", "                0.5,\n", "                0.95,\n", "                title,\n", "                ha=\"center\",\n", "                va=\"bottom\",\n", "                fontsize=20,\n", "                fontweight=\"bold\",\n", "                color=\"white\",\n", "                bbox=dict(facecolor=\"black\", pad=10, edgecolor=\"none\"),\n", "            )\n", "        return fig, ax"]}, {"cell_type": "code", "execution_count": null, "id": "d49125ec-48dc-44f8-a830-e05c3af03510", "metadata": {}, "outputs": [], "source": ["# Generate and save the figure\n", "fig, ax = render_mpl_table(df, title=f\"Logs Report (Hourly - {hour_value})\")\n", "fig.savefig(\"output_image.png\", bbox_inches=\"tight\", dpi=150)\n", "plt.close(fig)\n", "plt.show()\n", "\n", "\n", "# Generate and save the figure\n", "fig, ax = render_mpl_table(df1, title=f\"Logs Report (Till Hour - {hour_value})\")\n", "fig.savefig(\"output_image1.png\", bbox_inches=\"tight\", dpi=150)\n", "plt.close(fig)\n", "plt.show()\n", "\n", "# Spreadsheet links\n", "spreadsheet_url_1 = \"https://docs.google.com/spreadsheets/d/1AicN-taf9WEJue9v-M6cJzQ5QUNm_AFTxjjobCfawNI/edit?gid=1733768326#gid=1733768326\"\n", "hourly_logs_report_snapshot = f\"<{spreadsheet_url_1}|Hourly Snapshot - Logs Report >\"\n", "\n", "# Send Slack message\n", "output_image = \"output_image.png\"\n", "output_image1 = \"output_image1.png\"\n", "pb.send_slack_message(\n", "    channel=\"bl-hourly-supply-ops-report\",\n", "    files=[output_image, output_image1],\n", "    text=(\n", "        \"*Hi,*\\n\"\n", "        \"Please find the <PERSON>ly and Till Hour Snapshot.\\n\"\n", "        f\"Refer to the {hourly_logs_report_snapshot} Tracker link for more details.\\n\"\n", "    ),\n", "),\n", "\n", "os.remove(\"output_image1.png\")\n", "os.remove(\"output_image.png\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}