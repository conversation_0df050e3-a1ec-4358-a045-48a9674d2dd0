{"cells": [{"cell_type": "code", "execution_count": null, "id": "35f0e5d7-a12c-4326-a997-699219ce3a45", "metadata": {}, "outputs": [], "source": ["!pip install tabulate\n", "!pip install matplotlib\n", "!pip install pyauto<PERSON>i\n", "!pip install --upgrade Pillow\n", "!pip install numpy==1.26.4\n", "!pip install slack-sdk\n", "import numpy as np\n", "\n", "\n", "print(np.__version__)\n", "import pandas as pd\n", "import pencilbox as pb\n", "from tabulate import tabulate\n", "import textwrap as twp\n", "import time\n", "import PIL\n", "import matplotlib.pyplot as plt\n", "from pencilbox.connections import get_slack_client\n", "import os\n", "\n", "codsworth_client = get_slack_client(\"bl-analytics-bot\")"]}, {"cell_type": "code", "execution_count": null, "id": "f6e86976-d8d3-4025-897f-922f030cba85", "metadata": {}, "outputs": [], "source": ["trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e68e1bad-c183-4460-a5cc-a7c092d9aae5", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "with store_age as(\n", "Select *,\n", "date_diff('day',min_date,current_date) as store_age\n", "from (\n", "Select blinkit_store_id,\n", "min(account_date) as min_date\n", "from logistics_data_etls.blinkit_rider_daily_accounting_summary_v1\n", "where login_hours > 0\n", "group by 1\n", ")\n", ")\n", ",base as(\n", "Select \n", "order_checkout_dt_ist date_,\n", "hour(order_checkout_ts_ist) as hour,\n", "frontend_merchant_id,\n", "count(order_id) as total_orders,\n", "count(case when surge_pay > 0 then order_id end) as total_rain_orders\n", "from (\n", "Select fsc.*,surge_pay\n", "from (\n", "Select order_id,order_checkout_dt_ist,frontend_merchant_id,trip_id,order_checkout_ts_ist from dwh.fact_supply_chain_order_details\n", "where order_checkout_dt_ist >= current_date - interval '25' day\n", "and order_current_status = 'DELIVERED' and order_type = 'RetailForwardOrder'\n", "\n", "union \n", "\n", "Select order_id,order_checkout_dt_ist,frontend_merchant_id,trip_id,order_checkout_ts_ist from bistro_etls.fact_supply_chain_order_details_bistro\n", "where order_checkout_dt_ist >= current_date - interval '25' day\n", "and order_current_status = 'DELIVERED' \n", ")fsc\n", "left join logistics_data_etls.delivery_trip_payout tp on cast(tp.trip_id as varchar) = fsc.trip_id \n", "and dt between date_format(current_date - interval '25' day,'%%Y%%m%%d') and date_format(current_date - interval '1' day,'%%Y%%m%%d')\n", "where order_checkout_dt_ist between current_date - interval '25' day and current_date - interval '1' day\n", "\n", "union \n", "\n", "select fsc.*,surge_pay \n", "from (\n", "Select order_id,order_checkout_dt_ist,frontend_merchant_id,trip_id,order_checkout_ts_ist from dwh.fact_supply_chain_order_details\n", "where order_checkout_dt_ist >= current_date - interval '25' day\n", "and order_current_status = 'DELIVERED' and order_type = 'RetailForwardOrder'\n", "\n", "union \n", "\n", "Select order_id,order_checkout_dt_ist,frontend_merchant_id,trip_id,order_checkout_ts_ist from bistro_etls.fact_supply_chain_order_details_bistro\n", "where order_checkout_dt_ist >= current_date - interval '25' day\n", "and order_current_status = 'DELIVERED' \n", ") fsc \n", "left join (\n", "    select \n", "    e.ref_id trip_id,\n", "    entity_id as order_id,\n", "    max_by(cast(json_extract(cast(ledger_breakups as json),'$[0].amount') as integer),time) as surge_pay,\n", "    max_by(cast(json_extract(cast(ledger_breakups as json),'$[0].rate_card.id') as varchar),time) as rain_rate_card_id,\n", "    max(rain_level) as rain_level\n", "    from zomato.jumbo2.logistics_accounting_event_ledger e\n", "    cross join unnest(reports) as report\n", "    cross join unnest(report.accounting_info.rain_info) as t (order_id, location, merchant, rain_levels) \n", "    cross join unnest(rain_levels) as level (rain_level)\n", "    where dt in (date_format(current_date - interval '0' day, '%%Y%%m%%d'))\n", "    and business = 'blinkit'\n", "    and type = 'rain_report'\n", "    and trip_details.order_status = 'completed'\n", "    group by 1,2\n", ") y on fsc.trip_id = y.trip_id\n", "where order_Checkout_dt_ist in (current_date)\n", "\n", ")\n", "group by 1,2,3\n", ")\n", ",login as(\n", "select\n", "v.account_date as date,\n", "hour(hr) as hour,\n", "a.blinkit_Store_id frontend_merchant_id,\n", "sum(logins_mins)*1.00/60 as login_hours\n", "from logistics_data_etls.hourly_dp_wasted_login_v1 v\n", "left join (Select \n", "account_date,\n", "delivery_driver_id,\n", "blinkit_Store_id\n", "from logistics_data_etls.blinkit_rider_daily_accounting_summary_v1\n", "where account_date >= current_date - interval '25' day\n", ") a on a.account_date = v.account_date and a.delivery_driver_id = v.delivery_Driver_id\n", "where v.account_date >= current_Date - interval '25' day\n", "group by 1,2,3\n", "\n", "union\n", "\n", "Select \n", "date(date) as date,\n", "hour,\n", "cast(frontend_merchant_id as int) as frontend_merchant_id,\n", "sum(total_active_mins)*1.00/60 as login_hours\n", "from logistics_data_etls.store_hourly_orders_and_login_realtime\n", "where inserted_at_ist >= current_date - interval '1' day\n", "and date >= cast(current_date as varchar)\n", "group by 1,2,3\n", ")\n", ",mid as(\n", "Select b.*,\n", "login_hours,\n", "blinkit_store_name,\n", "runnr_city,\n", "avg(total_orders) OVER (PARTITION BY b.frontend_merchant_id, b.hour ORDER BY date ROWS BETWEEN 7 PRECEDING AND 1 PRECEDING) as total_orders_l_7,\n", "avg(total_rain_orders) OVER (PARTITION BY b.frontend_merchant_id, b.hour ORDER BY date ROWS BETWEEN 7 PRECEDING AND 1 PRECEDING) as total_rain_orders_l_7,\n", "avg(login_hours) OVER (PARTITION BY b.frontend_merchant_id, b.hour ORDER BY date ROWS BETWEEN 7 PRECEDING AND 1 PRECEDING) as login_hours_l_7,\n", "lag(login_hours) over(partition by  b.frontend_merchant_id,date order by b.hour) as prev_hour_logins,\n", "lag(total_orders) over(partition by  b.frontend_merchant_id,date order by b.hour) as prev_hour_order,\n", "sum(total_orders) over(partition by  b.frontend_merchant_id,date order by b.hour) as till_hour_order\n", "from base b\n", "left join login l on b.date_ = date(l.date) and b.hour = l.hour \n", "and (b.frontend_merchant_id) = l.frontend_merchant_id\n", "left join logistics_data_etls.blinkit_store_mapping sm on sm.blinkit_store_id = b.frontend_merchant_id\n", ")\n", ",mid_mid as(\n", "Select m.*,\n", "m1.total_orders as wow_total_orders,\n", "m1.total_rain_orders as wow_rain_orders,\n", "m1.login_hours as wow_login_hours,\n", "m2.total_orders as wo2w_total_orders,\n", "m2.total_rain_orders as wo2w_rain_orders,\n", "m2.login_hours as wo2w_login_hours,\n", "m3.total_orders as wo3w_total_orders,\n", "m3.total_rain_orders as wo3w_rain_orders,\n", "m3.login_hours as wo3w_login_hours\n", "from mid m\n", "left join mid m1 on m.frontend_merchant_id = m1.frontend_merchant_id and m.date_ = m1.date_ + interval '7' day and m.hour = m1.hour\n", "left join mid m2 on m.frontend_merchant_id = m2.frontend_merchant_id and m.date_ = m2.date_ + interval '14' day and m.hour = m2.hour\n", "left join mid m3 on m.frontend_merchant_id = m3.frontend_merchant_id and m.date_ = m3.date_ + interval '21' day and m.hour = m3.hour\n", "where m.date_ = current_date -- interval '2' day\n", "and m.frontend_merchant_id not in (\n", "Select distinct blinkit_store_id\n", "from logistics_data_etls.blinkit_rider_daily_accounting_summary_v1\n", "where login_hours > 0\n", "group by 1\n", "having count(distinct account_date) <= 7\n", ")\n", "and m.hour not in (23,0,1,2,3,4,5)\n", ")\n", ",final as(\n", "Select *,\n", "case when \n", "    (try(login_hours*1.00/prev_hour_logins - 1) < -0.50\n", "    and try(total_orders*1.00/prev_hour_order - 1) < -0.50) or\n", "     try(total_orders*1.00/prev_hour_order - 1) < -0.80 or \n", "    (try(login_hours*1.00/login_check_wow - 1) < -0.50 \n", "    and (try(total_orders*1.00/prev_hour_order - 1) < -0.50)) or\n", "    (try(login_hours*1.00/login_hours_l_7 - 1) < -0.50\n", "    and (try(total_orders*1.00/prev_hour_order - 1) < -0.50))\n", "then 1 else 0 end as strike,\n", "case  \n", "    when (try(login_hours*1.00/prev_hour_logins - 1) < -0.50\n", "    and try(total_orders*1.00/prev_hour_order - 1) < -0.50) then 'prev_hour_login_dip'\n", "    when try(total_orders*1.00/prev_hour_order - 1) < -0.80 then 'order_dip'\n", "    when (try(login_hours*1.00/login_check_wow - 1) < -0.50 \n", "    and (try(total_orders*1.00/prev_hour_order - 1) < -0.50)) then 'wow_login_hour_dip'\n", "    when (try(login_hours*1.00/login_hours_l_7 - 1) < -0.50\n", "    and (try(total_orders*1.00/prev_hour_order - 1) < -0.50)) then 'l-7_login_hour_dip'\n", "    end as strike_flag,\n", "login_hours*100.00/login_check_wow - 100 as wow_login_change_perc,\n", " login_hours*100.00/login_hours_l_7 - 100  as l_7_login_change_perc,\n", " login_hours*100.00/prev_hour_logins - 100 as prev_hour_login_change_perc,\n", " total_orders*100.00/prev_hour_order - 100 as prev_hour_order_dip_perc\n", "from (\n", "Select mm.*,\n", "case when wow_rain_orders*1.00/wow_total_orders < 0.10 then wow_login_hours \n", "    when wo2w_rain_orders*1.00/wo2w_total_orders < 0.10 then wo2w_login_hours \n", "    when wo3w_rain_orders*1.00/wo3w_total_orders < 0.10 then wo3w_login_hours \n", "end as login_check_wow\n", "from mid_mid mm\n", ")\n", "where hour = (select max(hour) - 1 from mid_mid) \n", "and total_rain_orders*1.00/total_orders < 0.10 and hour >= 8\n", "and frontend_merchant_id not in (Select distinct blinkit_store_id from store_age where store_age <= 7) \n", ")\n", "-- (\n", "select \n", "distinct \n", "frontend_merchant_id,\n", "blinkit_store_name,\n", "runnr_city,\n", "hour,\n", "strike_flag,\n", "wow_login_change_perc,\n", "l_7_login_change_perc,\n", "prev_hour_login_change_perc,\n", "prev_hour_order_dip_perc,\n", "till_hour_order\n", "from final\n", "where strike = 1\n", "and hour(current_timestamp) >= 8\n", "\n", "-- )\n", "\n", "-- union\n", "\n", "-- (select \n", "-- distinct blinkit_store_id,\n", "-- blinkit_store_name,\n", "-- runnr_city,\n", "-- -1 hour ,\n", "-- 'Live but not operational' as strike_flag,\n", "-- 0 as wow_login_change_perc,\n", "-- 0 as l_7_login_change_perc,\n", "-- 0 as prev_hour_login_change_perc,\n", "-- 0 as prev_hour_order_dip_perc,\n", "-- 0 as till_hour_order\n", "-- from logistics_data_etls.blinkit_store_mapping\n", "-- where blinkit_store_id not in (Select distinct frontend_merchant_id from mid)\n", "-- and is_active = 1)\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "52378cae-4294-471b-aa57-7bf556d88a60", "metadata": {}, "outputs": [], "source": ["df = pd.read_sql_query(sql=query, con=trino_con)"]}, {"cell_type": "code", "execution_count": null, "id": "9106863f-79ba-42d4-ade9-d2f559c0d37b", "metadata": {}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "id": "f01e9071-9ef4-40e6-8b63-cdead59fd6bb", "metadata": {}, "outputs": [], "source": ["df.fillna(\"\", inplace=True)\n", "\n", "if not df.empty:\n", "    # Function to render the table with conditional formatting\n", "    def render_mpl_table(\n", "        data,\n", "        col_width=3.0,\n", "        row_height=0.625,\n", "        font_size=12,\n", "        header_color=\"#F7D046\",\n", "        row_colors=[\"#f5f5f5\", \"w\"],\n", "        edge_color=\"grey\",\n", "        bbox=[0, 0, 1, 1],\n", "        header_columns=0,\n", "        cell_loc=\"center\",\n", "        conditional_formatting=None,\n", "        **kwargs\n", "    ):\n", "        fig, ax = plt.subplots(\n", "            figsize=(\n", "                (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "            )\n", "        )\n", "        ax.axis(\"off\")\n", "\n", "        # Create the table\n", "        mpl_table = ax.table(\n", "            cellText=data.values, bbox=bbox, colLabels=data.columns, cellLoc=cell_loc, **kwargs\n", "        )\n", "        mpl_table.auto_set_font_size(False)\n", "        mpl_table.set_fontsize(font_size)\n", "\n", "        # Set column width\n", "        mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "\n", "        for k, cell in mpl_table._cells.items():\n", "            row, col = k\n", "            cell.set_edgecolor(edge_color)\n", "\n", "            # Header row\n", "            if row == 0:\n", "                cell.set_text_props(weight=\"bold\", color=\"#1B5E20\")\n", "                cell.set_facecolor(header_color)\n", "            else:\n", "                cell.set_facecolor(row_colors[row % len(row_colors)])\n", "\n", "                # Make the first column bold\n", "                if col == 0:\n", "                    cell.set_text_props(weight=\"bold\")\n", "\n", "                # Apply conditional formatting if provided\n", "                if conditional_formatting and data.columns[col] in conditional_formatting:\n", "                    formatting_func = conditional_formatting[data.columns[col]]\n", "                    cell_val = data.iloc[row - 1, col]\n", "                    cell.set_facecolor(formatting_func(cell_val))\n", "\n", "        return fig, ax\n", "\n", "    if len(df) > 0:\n", "\n", "        fig, ax = render_mpl_table(df)\n", "        fig.savefig(\"strike.png\")\n", "\n", "    plt.show()\n", "    alert = \"strike.png\"\n", "    pb.send_slack_message(\n", "        channel=\"solving-strike\",\n", "        files=[alert],\n", "        text=(\"*Hi,*\\n\" \"Please find the Strike Stores.\\n\"),\n", "    )\n", "\n", "    # Optional cleanup\n", "    os.remove(\"strike.png\")\n", "\n", "# else:\n", "#     # If DataFrame is empty, send no-strike message\n", "#     pb.send_slack_message(\n", "#         channel=\"solving-strike\",\n", "#         text=(\"*Hi,*\\n\" \"No strike store for the current hour.\\n\" \"All operations are normal ✅.\"),\n", "#     )"]}, {"cell_type": "code", "execution_count": null, "id": "da1db978-c588-4a4f-a14b-170cb818da61", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0bf5db9b-0c5d-43f4-a5f1-53ee46f9345b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9b485f91-af30-49b8-8e99-7bfaa517cb27", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}