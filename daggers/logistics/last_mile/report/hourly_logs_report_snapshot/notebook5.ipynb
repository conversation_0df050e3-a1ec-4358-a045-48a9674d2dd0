{"cells": [{"cell_type": "code", "execution_count": null, "id": "115e5458-7a78-4e4a-8738-f03e81409513", "metadata": {}, "outputs": [], "source": ["!pip install tabulate\n", "!pip install matplotlib\n", "!pip install pyauto<PERSON>i\n", "!pip install --upgrade Pillow\n", "!pip install numpy==1.26.4\n", "!pip install slack-sdk\n", "import numpy as np\n", "\n", "print(np.__version__)\n", "import pandas as pd\n", "import pencilbox as pb\n", "from tabulate import tabulate\n", "import textwrap as twp\n", "import time\n", "import PIL\n", "import matplotlib.pyplot as plt\n", "from pencilbox.connections import get_slack_client\n", "import os\n", "\n", "codsworth_client = get_slack_client(\"bl-analytics-bot\")"]}, {"cell_type": "code", "execution_count": null, "id": "74a4fabc-81db-4728-bb3b-6c529cd0012e", "metadata": {}, "outputs": [], "source": ["import datetime as dt\n", "import sys\n", "from datetime import datetime, timedelta, date"]}, {"cell_type": "code", "execution_count": null, "id": "6a91996b-fbf0-41e6-9a6a-211a150dfb93", "metadata": {}, "outputs": [], "source": ["time.sleep(15)"]}, {"cell_type": "code", "execution_count": null, "id": "af23722c-3af7-41d8-b691-2c4b450817fe", "metadata": {}, "outputs": [], "source": ["# df = pd.read_csv(\"Festive Day Growth Tracker - view.csv\")\n", "\n", "# Load the data\n", "df = pb.from_sheets(\n", "    \"1-U73UM-yo8UOtEEk27n1IJiqbDwfCLLwPqtQX3EfEbc\",  # Replace with your Google Sheets ID\n", "    \"view\",  # Replace with your actual sheet name\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3d140d48-b87c-453c-947c-e71e4478e87b", "metadata": {}, "outputs": [], "source": ["now = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)"]}, {"cell_type": "code", "execution_count": null, "id": "64ac5479-38b2-4159-8b85-3b3edd9dc8f0", "metadata": {}, "outputs": [], "source": ["df.fillna(\"\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "09ee5d05-35d0-4041-b840-06b07b60d3ff", "metadata": {}, "outputs": [], "source": ["if df.columns[1] != str(now.date()):\n", "    print(\"Date mismatch. Halting script.\")\n", "    sys.exit()  # Stops execution completely\n", "\n", "# All remaining steps will only run if the date matches\n", "print(\"Continuing with processing...\")"]}, {"cell_type": "code", "execution_count": null, "id": "70119c33-e37b-468e-8f69-c1acac8754f2", "metadata": {}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "id": "ef77f64f-7e2c-47ee-a5da-b0b72a36b1f1", "metadata": {}, "outputs": [], "source": ["hour_value_1 = df.iloc[3:5, 0:2].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "539c7afe-12a2-46bb-bee4-1ebbde3d43f4", "metadata": {}, "outputs": [], "source": ["hour_value_2 = df.iloc[33, 0:2].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "170bb224-2324-4fc6-bdcf-1a49a9c1baf1", "metadata": {}, "outputs": [], "source": ["hour_value_1"]}, {"cell_type": "code", "execution_count": null, "id": "ffd48cc4-2844-4b65-ae35-ce8ab5c5d0b1", "metadata": {}, "outputs": [], "source": ["festival = df.columns[3]"]}, {"cell_type": "code", "execution_count": null, "id": "b7ebf23d-4415-4c63-9892-6928ea7d9521", "metadata": {}, "outputs": [], "source": ["partition_1 = df.iloc[7:29, 0:19].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "87de49a9-3393-4756-81c6-383c16f34eda", "metadata": {}, "outputs": [], "source": ["new_header_1 = partition_1.iloc[0]\n", "partition_1 = partition_1[1:]\n", "partition_1.columns = new_header_1"]}, {"cell_type": "code", "execution_count": null, "id": "5f9354af-3d8e-4024-ac61-6461<PERSON><PERSON><PERSON>de", "metadata": {}, "outputs": [], "source": ["partition_2 = df.iloc[36:58, 0:19].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "42800b56-738d-4dc7-b13c-059d90251730", "metadata": {}, "outputs": [], "source": ["new_header_2 = partition_2.iloc[0]\n", "partition_2 = partition_2[1:]\n", "partition_2.columns = new_header_2"]}, {"cell_type": "code", "execution_count": null, "id": "32051f6f-db74-4d9b-a2fe-a64b2cf8455d", "metadata": {}, "outputs": [], "source": ["# Function to render a partition with balanced column widths\n", "def render_partition(data, title, ax, font_size=14, row_height=0.08):\n", "    ax.axis(\"off\")\n", "\n", "    # Define colors\n", "    header_color = \"#F7D046\"  # Yellow for title\n", "    dark_header = \"#F7D046\"  # Deep blue for table header\n", "    row_colors = [\"#f9f9f9\", \"white\"]\n", "    edge_color = \"#bdbdbd\"\n", "\n", "    # Dynamic column widths\n", "    col_widths = [\n", "        max(len(str(x)) for x in [col] + list(data[col])) * 0.3  # includes header too\n", "        for col in data.columns\n", "    ]\n", "    col_widths = [max(1.5, min(3.0, w)) for w in col_widths]\n", "\n", "    # Table dimensions\n", "    num_rows = len(data)\n", "    table_height = row_height * (num_rows + 1)  # +1 for header\n", "\n", "    # Create table\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        colLabels=data.columns,\n", "        cellLoc=\"center\",\n", "        loc=\"center\",\n", "        bbox=[0, 0, 1, table_height],\n", "    )\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    # Style the table and set column widths\n", "    for key, cell in mpl_table._cells.items():\n", "        row, col = key\n", "        cell.set_edgecolor(edge_color)\n", "\n", "        if row == 0:\n", "            cell.set_facecolor(dark_header)\n", "            cell.set_text_props(\n", "                weight=\"bold\", color=\"#1B5E20\", fontsize=font_size + 3\n", "            )  # ⬅️ Increase header font\n", "            cell.set_height(row_height)\n", "            cell.set_width(col_widths[col])\n", "        else:\n", "            cell.set_facecolor(row_colors[(row - 1) % len(row_colors)])\n", "            cell.set_text_props(\n", "                color=\"black\", ha=\"center\", va=\"center\", weight=\"bold\" if col == 0 else \"normal\"\n", "            )  # ⬅️ Bold City column\n", "            cell.set_height(row_height)\n", "            cell.set_width(col_widths[col])\n", "\n", "    # Add title\n", "    ax.set_title(title, fontsize=font_size + 4, weight=\"bold\", color=header_color, pad=15)\n", "    return mpl_table\n", "\n", "\n", "# # Create figure\n", "# fig, axes = plt.subplots(nrows=2, ncols=1, figsize=(35, 16))\n", "\n", "# # Draw both tables\n", "# render_partition(partition_1, \"\", axes[0])  # Don't use title inside\n", "# render_partition(partition_2, \"\", axes[1])\n", "\n", "# fig.text(\n", "#     0.25,\n", "#     0.94,\n", "#     f\"Festive Day Report (From - {hour_value_1.iloc[1, 1]} to {hour_value_1.iloc[0, 1]})\",\n", "#     ha=\"center\",\n", "#     va=\"top\",\n", "#     fontsize=20,\n", "#     fontweight=\"bold\",\n", "#     color=\"white\",\n", "#     bbox=dict(facecolor=\"black\", pad=10, edgecolor=\"none\"),\n", "# )\n", "\n", "# fig.text(\n", "#     0.75,\n", "#     0.94,\n", "#     f\"Festive Day Report (Hour - {hour_value_2[1]})\",  # Right partition\n", "#     ha=\"center\",\n", "#     va=\"top\",\n", "#     fontsize=20,\n", "#     fontweight=\"bold\",\n", "#     color=\"white\",\n", "#     bbox=dict(facecolor=\"black\", pad=10, edgecolor=\"none\"),\n", "# )\n", "\n", "# Title for top table (axes[0])\n", "# Create figure\n", "fig, axes = plt.subplots(nrows=2, ncols=1, figsize=(35, 16))\n", "\n", "# Draw tables\n", "render_partition(partition_1, \"\", axes[0])\n", "render_partition(partition_2, \"\", axes[1])\n", "\n", "# Add headers ABOVE each table using fig.text and Y positions\n", "fig.text(\n", "    0.5,  # Centered\n", "    0.96,  # Top table title\n", "    f\"Festive Day Report (From - {hour_value_1.iloc[1, 1]} to {hour_value_1.iloc[0, 1]})\",\n", "    ha=\"center\",\n", "    va=\"bottom\",\n", "    fontsize=22,\n", "    fontweight=\"bold\",\n", "    color=\"white\",\n", "    bbox=dict(facecolor=\"black\", pad=10, edgecolor=\"none\"),\n", ")\n", "\n", "fig.text(\n", "    0.5,  # Centered\n", "    0.475,  # Bottom table title (adjust if needed)\n", "    f\"Festive Day Report (Hour - {hour_value_2[1]})\",\n", "    ha=\"center\",\n", "    va=\"bottom\",\n", "    fontsize=22,\n", "    fontweight=\"bold\",\n", "    color=\"white\",\n", "    bbox=dict(facecolor=\"black\", pad=10, edgecolor=\"none\"),\n", ")\n", "\n", "\n", "# Adjust spacing to create room for titles\n", "fig.subplots_adjust(top=0.8, wspace=1)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "\n", "# Save the figure as an image\n", "output_image_India_Kolkata = \"Festive Day.png\"\n", "fig.savefig(output_image_India_Kolkata, bbox_inches=\"tight\", dpi=150)\n", "plt.close(fig)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "dd0247b8-05f3-4448-bcf8-4096536d062a", "metadata": {}, "outputs": [], "source": ["# Spreadsheet links\n", "spreadsheet_url_1 = \"https://docs.google.com/spreadsheets/d/1-U73UM-yo8UOtEEk27n1IJiqbDwfCLLwPqtQX3EfEbc/edit?gid=1483924331#gid=1483924331\"\n", "hourly_logs_report_snapshot = f\"<{spreadsheet_url_1}| Festive Day Tracker>\""]}, {"cell_type": "code", "execution_count": null, "id": "f4f1ddbf-831c-4910-ab04-97203846b77d", "metadata": {}, "outputs": [], "source": ["hourly_logs_report_snapshot"]}, {"cell_type": "code", "execution_count": null, "id": "f2395737-28c0-4739-a123-be49f34f3a7c", "metadata": {}, "outputs": [], "source": ["# Upload image and send Slack message\n", "pb.send_slack_message(\n", "    channel=\"bl_last_mile_supply_planning\",\n", "    files=[output_image_India_Kolkata],  # Using the correct image name\n", "    text=(\n", "        \"*Hi,*\\n\"\n", "        f\"Please find the Hourly and Till Hour {festival} Planning vs Actual snapshot.\\n\"\n", "        f\"Refer to the {hourly_logs_report_snapshot} link for more details.\\n\"\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9fd496b4-9a53-4225-8a7d-cdd6f9d913c7", "metadata": {}, "outputs": [], "source": ["os.remove(\"Festive Day.png\")  # Remove the PNG file"]}, {"cell_type": "code", "execution_count": null, "id": "7aa425df-49e4-4bde-b658-4002980dce0b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}