alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 3
dag_name: hourly_logs_report_snapshot
dag_type: report
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: logistics
notebooks:
- executor_config:
    load_type: low
    node_type: spot
  name: notebook1
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: low
    node_type: spot
  name: notebook3
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: low
    node_type: spot
  name: notebook4
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: low
    node_type: spot
  name: notebook5
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: low
    node_type: spot
  name: notebook2
  parameters: null
  retries: 2
  tag: second
owner:
  email: <EMAIL>
  slack_id: U06JYRZ5VSL
path: logistics/last_mile/report/hourly_logs_report_snapshot
paused: false
pool: logistics_pool
project_name: last_mile
schedule:
  end_date: '2025-09-19T00:00:00'
  interval: 5 * * * *
  start_date: '2025-06-26T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 2
