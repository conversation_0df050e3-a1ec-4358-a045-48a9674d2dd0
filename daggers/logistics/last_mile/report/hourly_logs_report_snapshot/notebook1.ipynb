{"cells": [{"cell_type": "code", "execution_count": null, "id": "115e5458-7a78-4e4a-8738-f03e81409513", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "id": "34d295b9-75a4-4e7d-b64a-d5f4413b4bca", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "f463e034-9d26-4a73-a8e0-02b5ba920bae", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1MqlTVTvdbW9KOppZF35-w4ioXyVC4KZyCZ1Njc_kBB0\"\n", "sheet_name = \"main\"\n", "df = pb.from_sheets(sheet_id, sheet_name)\n", "\n", "\n", "# https://docs.google.com/spreadsheets/d/1MqlTVTvdbW9KOppZF35-w4ioXyVC4KZyCZ1Njc_kBB0/edit?gid=0#gid=0\n", "# df=pd.read_csv('csv.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "5b629fa9-f569-491a-95a5-e765234920df", "metadata": {}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "id": "6a91996b-fbf0-41e6-9a6a-211a150dfb93", "metadata": {}, "outputs": [], "source": ["df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "b53ec357-fb67-4b2c-bcd8-69e83c9e672c", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "Select \n", "store_id as z_store_id,\n", "blinkit_Store_id,\n", "blinkit_store_name,\n", "sm.runnr_city,\n", "frontend_merchant_city_name,\n", "pos_outlet_id,\n", "pos_outlet_name,\n", "x.pos_outlet_city_name,\n", "total_orders,\n", "dense_rank() over(order by total_orders desc) as rank\n", "from logistics_data_etls.blinkit_store_mapping sm\n", "left join (Select distinct *\n", "    from dwh.dim_merchant_outlet_facility_mapping\n", "    where is_current = TRUE\n", "                 AND is_mapping_enabled = TRUE\n", "                 AND is_current_mapping_active = TRUE\n", "                 and pos_outlet_id not in (4146,4381)\n", ") x on x.frontend_merchant_id = sm.blinkit_Store_id\n", "left join (\n", "Select pos_outlet_city_name,\n", "count(distinct order_id) as total_orders\n", "from dwh.fact_supply_chain_order_details fsc\n", "left join (Select distinct *\n", "    from dwh.dim_merchant_outlet_facility_mapping\n", "    where is_current = TRUE\n", "                 AND is_mapping_enabled = TRUE\n", "                 AND is_current_mapping_active = TRUE\n", "                 and pos_outlet_id not in (4146,4381)\n", ") x on x.frontend_merchant_id = fsc.frontend_merchant_id\n", "where order_checkout_dt_ist >= current_Date - interval '3' day\n", "and order_current_status = 'DELIVERED'\n", "group by 1) y on y.pos_outlet_city_name = x.pos_outlet_city_name\n", "where is_active = 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "eb0640ec-bcc3-47c7-8cf4-fde6004a54f6", "metadata": {}, "outputs": [], "source": ["main = pd.read_sql_query(sql=query, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "1d709ee0-7040-4b17-854b-0b4caf7d95ae", "metadata": {}, "outputs": [], "source": ["main"]}, {"cell_type": "code", "execution_count": null, "id": "893c401a-df44-4bd6-b023-70940468e6f6", "metadata": {}, "outputs": [], "source": ["# Merge on runnr_city and city\n", "merged_df = main.merge(df, left_on=\"runnr_city\", right_on=\"city\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "1debe8ac-2829-4699-bd89-a3e3d9415538", "metadata": {}, "outputs": [], "source": ["merged_df[\"final_region\"] = np.where(\n", "    merged_df[\"rank\"] <= 20, merged_df[\"runnr_city\"], merged_df[\"region\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "94d3e36f-b230-4801-90d5-5d934017002b", "metadata": {}, "outputs": [], "source": ["merged_df"]}, {"cell_type": "code", "execution_count": null, "id": "17f40b6c-2b42-4824-a892-6d97828c009c", "metadata": {}, "outputs": [], "source": ["merged_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "397b3e85-0533-41ac-a3a9-692f527b534a", "metadata": {}, "outputs": [], "source": ["final_cols = [\n", "    \"z_store_id\",\n", "    \"blinkit_Store_id\",\n", "    \"blinkit_store_name\",\n", "    \"runnr_city\",\n", "    \"frontend_merchant_city_name\",\n", "    \"pos_outlet_id\",\n", "    \"pos_outlet_name\",\n", "    \"pos_outlet_city_name\",\n", "    \"cluster_ceo\",\n", "    \"state\",\n", "    \"region\",\n", "    \"final_region\",\n", "]\n", "final_df = merged_df[final_cols]"]}, {"cell_type": "code", "execution_count": null, "id": "6d25e400-2125-4268-97c9-7394f35fa61e", "metadata": {}, "outputs": [], "source": ["final_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "d43c2360-c144-487a-8882-c0d2858c811e", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"blinkit_store_details_with_ceo_region_mapping\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"z_store_id\", \"type\": \"integer\", \"description\": \"serial_no\"},\n", "        {\"name\": \"blinkit_Store_id\", \"type\": \"integer\", \"description\": \"driver_id\"},\n", "        {\"name\": \"blinkit_store_name\", \"type\": \"varchar\", \"description\": \"date_of_incident\"},\n", "        {\"name\": \"runnr_city\", \"type\": \"varchar\", \"description\": \"gmc_cpa\"},\n", "        {\"name\": \"frontend_merchant_city_name\", \"type\": \"varchar\", \"description\": \"claim_type\"},\n", "        {\"name\": \"pos_outlet_id\", \"type\": \"integer\", \"description\": \"serial_no\"},\n", "        {\"name\": \"pos_outlet_name\", \"type\": \"varchar\", \"description\": \"driver_id\"},\n", "        {\"name\": \"pos_outlet_city_name\", \"type\": \"varchar\", \"description\": \"date_of_incident\"},\n", "        {\"name\": \"cluster_ceo\", \"type\": \"varchar\", \"description\": \"gmc_cpa\"},\n", "        {\"name\": \"state\", \"type\": \"varchar\", \"description\": \"claim_type\"},\n", "        {\"name\": \"region\", \"type\": \"varchar\", \"description\": \"gmc_cpa\"},\n", "        {\"name\": \"final_region\", \"type\": \"varchar\", \"description\": \"claim_type\"},\n", "    ],\n", "    # \"primary_key\": [\"blinkit_store_id\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"This ETL have details of Cluster Managers of Blinkit\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "de035376-bbf5-4e9a-9648-fb7640410939", "metadata": {}, "outputs": [], "source": ["pb.to_trino(final_df, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "4e1a751f-63eb-4f04-b3ad-83cf9a89da4a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}