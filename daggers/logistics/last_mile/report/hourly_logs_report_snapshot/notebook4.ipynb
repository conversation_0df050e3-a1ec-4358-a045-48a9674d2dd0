{"cells": [{"cell_type": "code", "execution_count": null, "id": "35f0e5d7-a12c-4326-a997-699219ce3a45", "metadata": {}, "outputs": [], "source": ["!pip install tabulate\n", "!pip install matplotlib\n", "!pip install pyauto<PERSON>i\n", "!pip install --upgrade Pillow\n", "!pip install numpy==1.26.4\n", "!pip install slack-sdk\n", "import numpy as np\n", "\n", "\n", "print(np.__version__)\n", "import pandas as pd\n", "import pencilbox as pb\n", "from tabulate import tabulate\n", "import textwrap as twp\n", "import time\n", "import PIL\n", "import matplotlib.pyplot as plt\n", "from pencilbox.connections import get_slack_client\n", "import os\n", "\n", "codsworth_client = get_slack_client(\"bl-analytics-bot\")"]}, {"cell_type": "code", "execution_count": null, "id": "f6e86976-d8d3-4025-897f-922f030cba85", "metadata": {}, "outputs": [], "source": ["trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e68e1bad-c183-4460-a5cc-a7c092d9aae5", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "with base as(\n", "Select \n", "order_checkout_dt_ist date_,\n", "hour(order_checkout_ts_ist) as hour_,\n", "frontend_merchant_id,\n", "count(order_id) as total_orders,\n", "count(case when surge_pay > 0 then order_id end) as total_rain_orders\n", "from (\n", "Select fsc.*,surge_pay\n", "from dwh.fact_supply_chain_order_details fsc\n", "left join logistics_data_etls.delivery_trip_payout tp on cast(tp.trip_id as varchar) = fsc.trip_id \n", "and dt between date_format(current_date - interval '25' day,'%%Y%%m%%d') and date_format(current_date - interval '1' day,'%%Y%%m%%d')\n", "where order_checkout_dt_ist between current_date - interval '25' day and current_date - interval '1' day\n", "\n", "union \n", "\n", "select fsc.*,surge_pay \n", "from dwh.fact_supply_chain_order_details fsc \n", "left join (\n", "    select \n", "    e.ref_id trip_id,\n", "    entity_id as order_id,\n", "    max_by(cast(json_extract(cast(ledger_breakups as json),'$[0].amount') as integer),time) as surge_pay,\n", "    max_by(cast(json_extract(cast(ledger_breakups as json),'$[0].rate_card.id') as varchar),time) as rain_rate_card_id,\n", "    max(rain_level) as rain_level\n", "    from zomato.jumbo2.logistics_accounting_event_ledger e\n", "    cross join unnest(reports) as report\n", "    cross join unnest(report.accounting_info.rain_info) as t (order_id, location, merchant, rain_levels) \n", "    cross join unnest(rain_levels) as level (rain_level)\n", "    where dt in (date_format(current_date - interval '0' day, '%%Y%%m%%d'))\n", "    and business = 'blinkit'\n", "    and type = 'rain_report'\n", "    and trip_details.order_status = 'completed'\n", "    group by 1,2\n", ") y on fsc.trip_id = y.trip_id\n", "where order_Checkout_dt_ist in (current_date)\n", "and order_current_status = 'DELIVERED' and order_type = 'RetailForwardOrder'\n", ")\n", "group by 1,2,3\n", ") \n", ",dau as(\n", "select \n", "    snapshot_date_ist as date_,\n", "    snapshot_hour_ist as hour_,\n", "    cast(merchant_id as int) as merchant_id,\n", "    sum(daily_active_users) as dau,\n", "    sum(overall_conversion) as overall_conversion\n", "from dwh.agg_daily_consumer_conversion_details\n", "where\n", "(snapshot_date_ist between current_date - interval '25' day and current_date - interval '1' day)\n", "and snapshot_hour_ist <> 24\n", "and merchant_id <> 'Overall'\n", "and city <> 'Overall'\n", "group by 1,2,3\n", ")\n", "Select \n", "date_,\n", "hour_,\n", "frontend_merchant_id,\n", "blinkit_store_name,\n", "runnr_City,\n", "try((dau/overall_conversion)*100.00/(prev_hour_dau/prev_hour_conversion)) - 100 as dau_change\n", "from\n", "(Select b.*,dau,overall_conversion,\n", "lag(dau) over(partition by  b.frontend_merchant_id,b.date_ order by b.hour_) as prev_hour_dau,\n", "lag(overall_conversion) over(partition by  b.frontend_merchant_id,b.date_ order by b.hour_) as prev_hour_conversion\n", "from base b\n", "left join dau d on b.date_ = d.date_ and b.hour_ = d.hour_ and b.frontend_merchant_id = d.merchant_id\n", ") x\n", "left join logistics_data_etls.blinkit_store_mapping sm on sm.blinkit_Store_id = x.frontend_merchant_id\n", "where try((dau/overall_conversion)*1.00/(prev_hour_dau/prev_hour_conversion)) - 1 > 0.10\n", "and date_ = current_date\n", "and hour_ not in (0,1,2,3,4,5,6)\n", "and hour_ = (select max(hour_) - 1 from base where date_ = current_Date)\n", "and total_rain_orders*1.00/total_orders < 0.10 \n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "52378cae-4294-471b-aa57-7bf556d88a60", "metadata": {}, "outputs": [], "source": ["df = pd.read_sql_query(sql=query, con=trino_con)"]}, {"cell_type": "code", "execution_count": null, "id": "9106863f-79ba-42d4-ade9-d2f559c0d37b", "metadata": {}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "id": "f01e9071-9ef4-40e6-8b63-cdead59fd6bb", "metadata": {}, "outputs": [], "source": ["df.fillna(\"\", inplace=True)\n", "\n", "if not df.empty:\n", "    # Function to render the table with conditional formatting\n", "    def render_mpl_table(\n", "        data,\n", "        col_width=3.0,\n", "        row_height=0.625,\n", "        font_size=12,\n", "        header_color=\"#F7D046\",\n", "        row_colors=[\"#f5f5f5\", \"w\"],\n", "        edge_color=\"grey\",\n", "        bbox=[0, 0, 1, 1],\n", "        header_columns=0,\n", "        cell_loc=\"center\",\n", "        conditional_formatting=None,\n", "        **kwargs\n", "    ):\n", "        fig, ax = plt.subplots(\n", "            figsize=(\n", "                (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "            )\n", "        )\n", "        ax.axis(\"off\")\n", "\n", "        # Create the table\n", "        mpl_table = ax.table(\n", "            cellText=data.values, bbox=bbox, colLabels=data.columns, cellLoc=cell_loc, **kwargs\n", "        )\n", "        mpl_table.auto_set_font_size(False)\n", "        mpl_table.set_fontsize(font_size)\n", "\n", "        # Set column width\n", "        mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "\n", "        for k, cell in mpl_table._cells.items():\n", "            row, col = k\n", "            cell.set_edgecolor(edge_color)\n", "\n", "            # Header row\n", "            if row == 0:\n", "                cell.set_text_props(weight=\"bold\", color=\"#1B5E20\")\n", "                cell.set_facecolor(header_color)\n", "            else:\n", "                cell.set_facecolor(row_colors[row % len(row_colors)])\n", "\n", "                # Make the first column bold\n", "                if col == 0:\n", "                    cell.set_text_props(weight=\"bold\")\n", "\n", "                # Apply conditional formatting if provided\n", "                if conditional_formatting and data.columns[col] in conditional_formatting:\n", "                    formatting_func = conditional_formatting[data.columns[col]]\n", "                    cell_val = data.iloc[row - 1, col]\n", "                    cell.set_facecolor(formatting_func(cell_val))\n", "\n", "        return fig, ax\n", "\n", "    if len(df) > 0:\n", "\n", "        fig, ax = render_mpl_table(df)\n", "        fig.savefig(\"dau.png\")\n", "\n", "    plt.show()\n", "    alert = \"dau.png\"\n", "    pb.send_slack_message(\n", "        channel=\"alert_test_maninder\",\n", "        files=[alert],\n", "        text=(\n", "            \"*Hi,*\\n\" \"Please find the list of stores where DAU has increased by at least 10%.\\n\"\n", "        ),\n", "    )\n", "\n", "    # Optional cleanup\n", "    os.remove(\"dau.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "da1db978-c588-4a4f-a14b-170cb818da61", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0bf5db9b-0c5d-43f4-a5f1-53ee46f9345b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9b485f91-af30-49b8-8e99-7bfaa517cb27", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}