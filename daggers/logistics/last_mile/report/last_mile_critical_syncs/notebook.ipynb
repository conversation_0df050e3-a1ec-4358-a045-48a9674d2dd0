{"cells": [{"cell_type": "code", "execution_count": null, "id": "63037ac1-8d52-4c4d-9ea8-f31527681438", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import pencilbox as pb\n", "import numpy as np\n", "import itertools\n", "import datetime as dt\n", "from datetime import datetime\n", "from datetime import date\n", "from datetime import timedelta\n", "import math\n", "\n", "\n", "pd.set_option(\"display.max_rows\", 500)\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.width\", 1000)"]}, {"cell_type": "code", "execution_count": null, "id": "99d88168-5c2a-45d8-a5a5-240312860aa8", "metadata": {}, "outputs": [], "source": ["trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "4389ea92-981f-4455-8219-f0ebcd2823eb", "metadata": {}, "outputs": [], "source": ["today = dt.datetime.now()\n", "\n", "# Get day of the week\n", "day_of_week_num = today.weekday() + 1\n", "\n", "# Get week of year\n", "week_num = today.isocalendar()[1]"]}, {"cell_type": "code", "execution_count": null, "id": "23614245-916f-4232-9375-e2050144cf5e", "metadata": {}, "outputs": [], "source": ["# query_sync_list = pd.read_csv('query_sync_list.csv')\n", "\n", "query_sync_list = pb.from_sheets(\n", "    \"19_6p_C7fD4fjqbCXrokKu_TDjXeBN5B90Yph4Dig5Lo\",\n", "    \"query_sync_list\",\n", "    clear_cache=True,\n", ")\n", "\n", "query_sync_list"]}, {"cell_type": "code", "execution_count": null, "id": "c549b099-2524-474e-a1de-cc83b9a5d270", "metadata": {}, "outputs": [], "source": ["query_sync_list"]}, {"cell_type": "code", "execution_count": null, "id": "b4b0cad2-e4c4-4c3b-9cc3-145a19582c75", "metadata": {"tags": []}, "outputs": [], "source": ["# Assuming query_sync_list is your DataFrame\n", "query_sync_dict = query_sync_list.to_dict(orient=\"records\")"]}, {"cell_type": "code", "execution_count": null, "id": "9457e915-0fcb-43d6-b69c-11e78f3f7434", "metadata": {}, "outputs": [], "source": ["query_sync_dict"]}, {"cell_type": "code", "execution_count": null, "id": "90f8f54b-d4a3-4340-a8d2-424c03b75a85", "metadata": {}, "outputs": [], "source": ["now = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "current_hour = now.hour"]}, {"cell_type": "code", "execution_count": null, "id": "bd66022c-e0b3-4415-963c-c6f8bc533379", "metadata": {}, "outputs": [], "source": ["for i in query_sync_dict:\n", "\n", "    query = i[\"QUERY\"]\n", "    hour = i[\"HOUR INTERVAL IST\"]\n", "    sheet_id = i[\"SHEET ID\"]\n", "    tab_name = i[\"TAB NAME\"]\n", "    dow = i[\"DOW\"]\n", "\n", "    if hour != \"*\":\n", "        # Ensure hour is a list\n", "        if isinstance(hour, int):  # If hour is a single integer\n", "            hour = [hour]\n", "        elif not isinstance(hour, list):  # If it's not a list and not '*'\n", "            hour = list(\n", "                map(int, hour.split(\",\"))\n", "            )  # Assuming hour can be a comma-separated string of numbers\n", "        print(hour)\n", "\n", "    if dow != \"*\":\n", "        # Ensure hour is a list\n", "        if isinstance(dow, int):  # If hour is a single integer\n", "            dow = [dow]\n", "        elif not isinstance(dow, list):  # If it's not a list and not '*'\n", "            dow = list(\n", "                map(int, dow.split(\",\"))\n", "            )  # Assuming hour can be a comma-separated string of numbers\n", "        print(dow)\n", "\n", "    try:\n", "        if dow == \"*\":\n", "            if hour != \"*\":\n", "                for h in hour:\n", "                    if current_hour == h:\n", "                        query = f\"\"\"\n", "\n", "                         {query}\n", "\n", "                        \"\"\"\n", "                        try:\n", "                            print(hour)\n", "                            print(dow)\n", "\n", "                            data = pd.read_sql(query, trino_con)\n", "                            pb.to_sheets(data, f\"{sheet_id}\", f\"{tab_name}\")\n", "                            print(f\"Successfully synced the query with sheet {sheet_id}\")\n", "\n", "                        except Exception as e:\n", "                            print(f\"Error executing query or uploading to sheet for hour {h}: {e}\")\n", "                    else:\n", "                        print(hour)\n", "                        print(dow)\n", "\n", "                        print(\n", "                            f\"Error executing query or uploading to sheet for hour because it's not matching with current_hour {h}\"\n", "                        )\n", "\n", "            elif hour == \"*\":\n", "                try:\n", "                    print(hour)\n", "                    print(dow)\n", "                    data = pd.read_sql(query, trino_con)\n", "                    pb.to_sheets(data, f\"{sheet_id}\", f\"{tab_name}\")\n", "                    print(f\"Successfully synced the query with sheet {sheet_id}\")\n", "\n", "                except Exception as e:\n", "                    print(f\"Error executing query or uploading to sheet for hour {hour} : {e}\")\n", "\n", "        elif day_of_week_num in dow:\n", "            if hour != \"*\":\n", "                for h in hour:\n", "                    if current_hour == h:\n", "                        query = f\"\"\"\n", "\n", "                         {query}\n", "\n", "                        \"\"\"\n", "                        try:\n", "                            print(hour)\n", "                            print(dow)\n", "\n", "                            data = pd.read_sql(query, trino_con)\n", "                            pb.to_sheets(data, f\"{sheet_id}\", f\"{tab_name}\")\n", "                            print(f\"Successfully synced the query with sheet {sheet_id}\")\n", "\n", "                        except Exception as e:\n", "                            print(f\"Error executing query or uploading to sheet for hour {h}: {e}\")\n", "                    else:\n", "                        print(\n", "                            f\"Error executing query or uploading to sheet for hour because it's not matching with current_hour {h}\"\n", "                        )\n", "\n", "            elif hour == \"*\":\n", "                try:\n", "                    print(hour)\n", "                    print(dow)\n", "                    data = pd.read_sql(query, trino_con)\n", "                    pb.to_sheets(data, f\"{sheet_id}\", f\"{tab_name}\")\n", "                    print(f\"Successfully synced the query with sheet {sheet_id}\")\n", "\n", "                except Exception as e:\n", "                    print(f\"Error executing query or uploading to sheet for hour {hour} : {e}\")\n", "\n", "    except Exception as e:\n", "        print(f\"Error processing query for sheet ID {sheet_id}: {e}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}