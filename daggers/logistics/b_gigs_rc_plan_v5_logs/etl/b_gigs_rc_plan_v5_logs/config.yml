alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: b_gigs_rc_plan_v5_logs
dag_type: etl
escalation_priority: low
execution_timeout: 800
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: logistics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U060684S18V
path: logistics/b_gigs_rc_plan_v5_logs/etl/b_gigs_rc_plan_v5_logs
paused: false
pool: logistics_pool
project_name: b_gigs_rc_plan_v5_logs
schedule:
  end_date: '2025-09-05T00:00:00'
  interval: 45 2,13 * * *
  start_date: '2025-03-24T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
