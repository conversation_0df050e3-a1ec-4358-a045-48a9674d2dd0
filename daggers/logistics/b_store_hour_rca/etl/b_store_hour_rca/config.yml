alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: b_store_hour_rca
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: logistics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U082DBVETNX
path: logistics/b_store_hour_rca/etl/b_store_hour_rca
paused: false
pool: logistics_pool
project_name: b_store_hour_rca
schedule:
  end_date: '2025-09-05T00:00:00'
  interval: 30 3,4 * * *
  start_date: '2024-07-26T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 3
