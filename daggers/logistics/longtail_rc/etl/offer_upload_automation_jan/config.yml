alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
  - channel: lm_offer_uploader_dag_alert
dag_name: offer_upload_automation_jan
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: logistics
notebook:
  parameters: null
  retries: 2
owner:
  email: <EMAIL>
  slack_id: U08D6SCUHB6
path: logistics/longtail_rc/etl/offer_upload_automation_jan
paused: false
pool: logistics_pool
project_name: longtail_rc
schedule:
  end_date: '2025-08-24T00:00:00'
  interval: 45 2,4,7,9,10,13,16 * * *
  start_date: '2025-03-20T00:00:00'
schedule_type: fixed
sla: 122 minutes
support_files: []
tags: []
template_name: notebook
version: 5
