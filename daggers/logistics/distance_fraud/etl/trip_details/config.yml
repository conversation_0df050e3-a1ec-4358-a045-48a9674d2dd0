alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: trip_details
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: logistics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U082DBVETNX
path: logistics/distance_fraud/etl/trip_details
paused: false
pool: logistics_pool
project_name: distance_fraud
schedule:
  end_date: '2025-09-19T00:00:00'
  interval: 0 4 * * *
  start_date: '2025-06-24T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 7
