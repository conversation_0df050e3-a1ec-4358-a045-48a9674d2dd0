{"cells": [{"cell_type": "code", "execution_count": null, "id": "65fa8c2e-232f-42f8-927f-014359220e42", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import pencilbox as pb\n", "import numpy as np\n", "import itertools\n", "import datetime as dt\n", "from datetime import datetime\n", "from datetime import date\n", "from datetime import timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "79db17b9-2fb1-48d7-9baa-ac9f934ec211", "metadata": {}, "outputs": [], "source": ["trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "a20f7c57-1e24-4100-a87b-ca1d80caeab4", "metadata": {}, "outputs": [], "source": ["base_sql = f\"\"\"\n", "\n", "WITH trip_base AS (\n", "    SELECT DISTINCT\n", "        account_date,\n", "        cast(trip_id as varchar) as trip_id,\n", "        tab_id1,\n", "        tab_id2,\n", "        tab_id3,\n", "        tab_id4,\n", "        delivery_driver_id,\n", "        drop_distance,\n", "        drop_distance_pay,\n", "        ldrp,\n", "        clubbing_flag\n", "    FROM logistics_data_etls.delivery_trip_payout\n", "    WHERE dt BETWEEN DATE_FORMAT((CURRENT_DATE - INTERVAL '4' DAY), '%%Y%%m%%d') AND DATE_FORMAT((CURRENT_DATE - INTERVAL '1' DAY), '%%Y%%m%%d') \n", "    AND merchant_category = 'BLINKIT'\n", "),\n", "\n", "trip_data AS (\n", "    SELECT\n", "        dt,\n", "        city_id,\n", "        try_cast(trip_id as bigint) as trip_id,\n", "        order_id,\n", "        CAST(FROM_UNIXTIME(ingestion_time / 1000) AS TIMESTAMP) AS time,\n", "        clubbing_flag as is_clubbed_trip,\n", "        cast(0.00 as double) AS expected_drop_distance,\n", "        cast(0.00 as double) AS carthero_raw_google,\n", "        cast(0.00 as double) AS aerial_distance,\n", "        drop_accounting_distance,\n", "        drop_google_distance,\n", "        ping_distance_km AS drop_ping_distance,\n", "        ping_confidence AS confidence,\n", "        FALSE AS drop_used_ping_distance,\n", "        cast(from_lat as double) from_lat,\n", "        cast(from_long as double) from_long,\n", "        cast(to_lat as double) to_lat,\n", "        cast(to_long as double) to_long,\n", "        cast(0.00 as double) AS osrm_distance,\n", "        FALSE AS used_osrm_distance,\n", "        FALSE AS drop_used_rd,\n", "        cast(0.00 as double) AS drop_rd,\n", "        cast(0.00 as double) AS ping_to_google_percentage,\n", "        cast(0.00 as double) AS ping_to_google_percentage_threshold,\n", "        cast(0.00 as double) AS google_to_aerial_ratio,\n", "        cast(0.00 as double) AS google_to_aerial_ratio_threshold,\n", "        FALSE AS is_distance_fraud,\n", "        FALSE AS google_routes_car_enabled,\n", "        FALSE AS used_google_routes_car_distance,\n", "        cast(0.00 as double) AS google_routes_car_distance,\n", "        cast(0.00 as double) AS google_routes_bike_distance,\n", "        FALSE AS google_routes_bike_enabled,\n", "        FALSE AS used_google_routes_bike_distance,\n", "        actual_earning AS actual_earnings,\n", "        cast(0.00 as double) AS expected_earnings,\n", "        tab_id1,\n", "        tab_id2,\n", "        tab_id3,\n", "        tab_id4,\n", "        delivery_driver_id,\n", "        drop_distance,\n", "        drop_distance_pay,\n", "        ldrp,\n", "        google_distance_km,\n", "        ping_distance_km,\n", "        return_distance,\n", "        google_distance_polyline,\n", "        ping_distance_polyline\n", "    FROM (\n", "       select \n", "            te.trip_id,\n", "            MAX(tb.tab_id1) AS tab_id1,\n", "            MAX(tb.tab_id2) AS tab_id2,\n", "            MAX(tb.tab_id3) AS tab_id3,\n", "            MAX(tb.tab_id4) AS tab_id4,\n", "            MAX(tb.delivery_driver_id) AS delivery_driver_id,\n", "            MAX(tb.drop_distance) AS drop_distance,\n", "            MAX(tb.drop_distance_pay) AS drop_distance_pay,\n", "            MAX(tb.clubbing_flag) AS clubbing_flag,\n", "            MAX(tb.ldrp) AS ldrp,\n", "            MAX(dt) AS dt,\n", "            MAX(ingestion_time) AS ingestion_time,\n", "            MAX_BY(reports, ingestion_time) AS trip_metadata,\n", "            MAX_BY(order_id,ingestion_time) order_id,\n", "            MAX_BY(city_id,ingestion_time) AS city_id,\n", "            MAX_BY(amount,ingestion_time) AS actual_earning,\n", "            MAX_BY(drop_google_distance,ingestion_time) AS drop_google_distance,\n", "            MAX_BY(drop_accounting_distance,ingestion_time) AS drop_accounting_distance,\n", "            MAX_BY(google_distance_km,ingestion_time) AS google_distance_km,\n", "            MAX_BY(google_confidence,ingestion_time) AS google_confidence,\n", "            MAX_BY(ping_distance_km,ingestion_time) AS ping_distance_km,\n", "            MAX_BY(ping_confidence,ingestion_time) AS ping_confidence,\n", "            MAX_BY(return_distance,ingestion_time) AS return_distance,\n", "            MAX_BY(google_distance_polyline,ingestion_time) AS google_distance_polyline,\n", "            MAX_BY(ping_distance_polyline,ingestion_time) AS ping_distance_polyline,\n", "            MAX_BY(json_extract(json_extract(json_extract(checkpoints_json, '$[0]'), '$.location'), '$.latitude'),ingestion_time) AS from_lat,\n", "            MAX_BY(json_extract(json_extract(json_extract(checkpoints_json, '$[0]'), '$.location'), '$.longitude'),ingestion_time) AS from_long,\n", "            MAX_BY(json_extract(json_extract(json_extract(checkpoints_json, '$[1]'), '$.location'), '$.latitude'),ingestion_time) AS to_lat,\n", "            MAX_BY(json_extract(json_extract(json_extract(checkpoints_json, '$[1]'), '$.location'), '$.longitude'),ingestion_time) AS to_long\n", "        FROM (\n", "            select \n", "            dt,\n", "            te.ref_id trip_id,\n", "            entity_id as order_id,\n", "            trip_details.order_status AS trip_order_status,\n", "            trip_details.flow_type AS flow_type,\n", "             -- Unnesting ledger_breakups array\n", "            accounting_info[1] AS drop_google_distance,\n", "            accounting_info[3] AS drop_accounting_distance,\n", "\n", "            driver[5] city_id,\n", "    \n", "            accounting_info.google_distance_info[1] as google_distance_km,\n", "            accounting_info.google_distance_info[3] as google_confidence,\n", "            accounting_info.ping_distance_info[1] as ping_distance_km,\n", "            accounting_info.ping_distance_info[3] as ping_confidence,\n", "            ingestion_time,\n", "            reports,\n", "            te.amount,\n", "            accounting_info.return_distance as return_distance,\n", "            accounting_info.google_distance_info[2] AS google_distance_polyline,\n", "            accounting_info.ping_distance_info[2] AS ping_distance_polyline,\n", "            cast(checkpoints AS json) as checkpoints_json\n", "            FROM zomato.jumbo2.logistics_accounting_event_ledger te\n", "            CROSS JOIN UNNEST(reports) AS report\n", "            WHERE dt BETWEEN DATE_FORMAT((CURRENT_DATE - INTERVAL '4' DAY), '%%Y%%m%%d') AND DATE_FORMAT((CURRENT_DATE - INTERVAL '1' DAY), '%%Y%%m%%d') \n", "            and business = 'blinkit'\n", "            and type in ('drop_report', 'drop_touch_point')) te\n", "        INNER JOIN trip_base tb ON tb.trip_id = te.trip_id\n", "        group by 1\n", "    )\n", ")\n", "\n", "\n", ", address_confidence AS (\n", "    SELECT\n", "        order_id,\n", "        date_,\n", "        CASE\n", "            WHEN verified_address IN ('HIGH', 'CX_LOCATION_CORRECT', 'CX_ADD_ADDRESS_VERIFICATION', 'EXPERIMENTAL_HIGH') THEN 'High Confidence'\n", "            WHEN verified_address IN ('CX_ORDER_DELIVERY_VERIFICATION', 'CX_POI_VERIFICATION', 'MEDIUM', 'VERY_LOW', 'EXPERIMENTAL_MEDIUM') THEN 'Medium Confidence'\n", "            ELSE 'Low confidence'\n", "        END AS flag\n", "    FROM (\n", "        SELECT\n", "            lsm.label_value AS order_id,\n", "            DATE_FORMAT(FROM_UNIXTIME(install_ts / 1000000), '%%Y-%%m-%%d') AS date_,\n", "            CAST(JSON_EXTRACT(JSON_EXTRACT(metadata, '$.corrected_location_info'), '$.confidence') AS VARCHAR) AS verified_address\n", "        FROM lake_logistics.logistics_shipment_label_metadata lsm\n", "        WHERE (insert_ds_ist between CAST((CURRENT_DATE - INTERVAL '4' DAY) AS VARCHAR) AND CAST((CURRENT_DATE - INTERVAL '1' DAY) AS VARCHAR))\n", "        AND label_type = 'ORDER_ID'\n", "    )\n", "    WHERE date_ between DATE_FORMAT(CURRENT_DATE - INTERVAL '4' DAY, '%%Y-%%m-%%d') and DATE_FORMAT(CURRENT_DATE - INTERVAL '1' DAY, '%%Y-%%m-%%d')\n", ")\n", "\n", "\n", "select \n", "dt,\n", "city_id,\n", "trip_id,\n", "try_cast(order_id as bigint) as order_id,\n", "cast(time as timestamp(6)) time,\n", "is_clubbed_trip,\n", "expected_drop_distance,\n", "carthero_raw_google,\n", "aerial_distance,\n", "drop_accounting_distance,\n", "drop_google_distance,\n", "drop_ping_distance,\n", "confidence,\n", "drop_used_ping_distance,\n", "cast(from_lat as double)*1.0000000 from_lat,\n", "cast(from_long as double)*1.0000000  from_long,\n", "cast(to_lat as double)*1.0000000 to_lat,\n", "cast(to_long as double)*1.0000000 to_long,\n", "google_routes_car_enabled,\n", "used_google_routes_car_distance,\n", "google_routes_car_distance,\n", "google_routes_bike_distance,\n", "google_routes_bike_enabled,\n", "used_google_routes_bike_distance,\n", "actual_earnings,\n", "expected_earnings,\n", "tab_id1 ,\n", "tab_id2 ,\n", "tab_id3,\n", "tab_id4 ,\n", "delivery_driver_id ,\n", "drop_distance ,\n", "drop_distance_pay ,\n", "ldrp    ,\n", "customer_id  ,\n", "merchant_id  ,\n", "cx_karma_label ,\n", "cast(cx_karma_score as int) cx_karma_score,\n", "dp_karma_score ,\n", "accounting_distance_used ,\n", "busy_time_sec,\n", "total_items_quantity_delivered ,\n", "total_product_quantity ,\n", "total_selling_price ,\n", "order_current_status ,\n", "address_confidence ,\n", "drop_cell_token_l17 ,\n", "store_cell_token_l17 ,\n", "osrm_distance ,\n", "used_osrm_distance ,\n", "drop_used_rd ,\n", "drop_rd ,\n", "ping_to_google_percentage ,\n", "ping_to_google_percentage_threshold ,\n", "google_to_aerial_ratio  ,\n", "google_to_aerial_ratio_threshold ,\n", "is_distance_fraud,\n", "drop_cell_token_l16,\n", "store_cell_token_l16,\n", "return_distance,\n", "google_distance_polyline,\n", "ping_distance_polyline\n", "from (\n", "SELECT\n", "    t.dt,\n", "    t.city_id,\n", "    t.trip_id,\n", "    t.order_id,\n", "    t.time,\n", "    t.is_clubbed_trip,\n", "    t.expected_drop_distance, \n", "    t.carthero_raw_google,\n", "    t.aerial_distance,\n", "    t.drop_accounting_distance,\n", "    t.drop_google_distance,\n", "    t.drop_ping_distance,\n", "    t.confidence,\n", "    t.drop_used_ping_distance,\n", "    t.from_lat,\n", "    t.from_long,\n", "    t.to_lat,\n", "    t.to_long,\n", "    t.google_routes_car_enabled,\n", "    t.used_google_routes_car_distance,\n", "    t.google_routes_car_distance,\n", "    t.google_routes_bike_distance,\n", "    t.google_routes_bike_enabled,\n", "    t.used_google_routes_bike_distance,\n", "    t.actual_earnings,\n", "    t.expected_earnings,\n", "    t.tab_id1,\n", "    t.tab_id2,\n", "    t.tab_id3,\n", "    t.tab_id4,\n", "    t.delivery_driver_id,\n", "    try_cast(t.drop_distance as double) as drop_distance,\n", "    try_cast(t.drop_distance_pay as double) as drop_distance_pay,\n", "    try_cast(t.ldrp as double) as ldrp,\n", "    try_cast(customer_id as int) as customer_id,\n", "    try_cast(fso.frontend_merchant_id as int) as merchant_id,\n", "    fso.karma_label AS cx_karma_label,  -- Ensure this alias is correct\n", "    cast(ceil(cast(fso.karma_score as double)) as int) as cx_karma_score,\n", "    try_cast(ks.score as double) AS dp_karma_score,\n", "    CASE\n", "        WHEN t.used_google_routes_car_distance = TRUE THEN 'google_distance'\n", "        WHEN t.used_google_routes_bike_distance = TRUE THEN 'google_distance'\n", "        WHEN t.drop_used_ping_distance = TRUE THEN 'ping_distance'\n", "        ELSE 'others'\n", "    END AS accounting_distance_used,\n", "    DATE_DIFF('SECOND', fs.order_partner_assigned_ts_ist, fs.order_delivered_ts_ist) AS busy_time_sec,\n", "    fs.total_items_quantity_delivered,\n", "    fso.total_product_quantity,\n", "    fso.total_selling_price,\n", "    fso.order_current_status,\n", "    a.flag AS address_confidence,\n", "    S2_CELL_TOKEN(S2_CELL_ID(S2_CELL(ua.latitude, ua.longitude, 17))) AS drop_cell_token_l17,\n", "    S2_CELL_TOKEN(S2_CELL_ID(S2_CELL(uap.latitude, uap.longitude, 17))) AS store_cell_token_l17,\n", "    t.osrm_distance,\n", "    t.used_osrm_distance,\n", "    t.drop_used_rd,\n", "    t.drop_rd,\n", "    t.ping_to_google_percentage,\n", "    t.ping_to_google_percentage_threshold,\n", "    t.google_to_aerial_ratio,\n", "    t.google_to_aerial_ratio_threshold,\n", "    t.is_distance_fraud , \n", "    S2_CELL_TOKEN(S2_CELL_ID(S2_CELL(ua.latitude, ua.longitude, 16))) AS drop_cell_token_l16,\n", "    S2_CELL_TOKEN(S2_CELL_ID(S2_CELL(uap.latitude, uap.longitude, 16))) AS store_cell_token_l16,\n", "    return_distance,\n", "    t.google_distance_polyline,\n", "    t.ping_distance_polyline,\n", "    row_number() over (partition by t.dt , t.tab_id1 order by t.time ) rnk \n", "FROM trip_data t\n", "LEFT JOIN (select * from zomato.carthero_prod.orders co where co.dt BETWEEN DATE_FORMAT((CURRENT_DATE - INTERVAL '4' DAY), '%%Y%%m%%d') AND DATE_FORMAT((CURRENT_DATE - INTERVAL '1' DAY), '%%Y%%m%%d') ) co\n", "    ON t.order_id = CAST(co.id AS VARCHAR)\n", "LEFT JOIN zomato.carthero_prod.user_addresses ua \n", "    ON ua.id = co.drop_user_address_id\n", "LEFT JOIN zomato.carthero_prod.user_addresses uap \n", "    ON uap.id = co.pickup_user_address_id\n", "LEFT JOIN (select * from cd_etls.master_cancel_table cd \n", "where cd.order_date BETWEEN (CURRENT_DATE - INTERVAL '4' DAY) AND (CURRENT_DATE - INTERVAL '1' DAY)) cd \n", "    ON CAST(cd.order_id AS VARCHAR) = co.external_order_id\n", "LEFT JOIN (select \n", "                order_create_dt_ist,\n", "                order_id,\n", "                frontend_merchant_id,\n", "                karma_label,\n", "                karma_score,\n", "                total_product_quantity,\n", "                total_selling_price,\n", "                order_current_status\n", "            from dwh.fact_sales_order_details fso \n", "            where fso.order_create_dt_ist BETWEEN (CURRENT_DATE - INTERVAL '4' DAY) AND (CURRENT_DATE - INTERVAL '1' DAY)\n", "            \n", "            union \n", "        \n", "            select \n", "                order_create_dt_ist,\n", "                order_id,\n", "                frontend_merchant_id,\n", "                null as karma_label,\n", "                null as karma_score,\n", "                total_product_quantity,\n", "                total_selling_price,\n", "                order_current_status\n", "            from bistro_etls.fact_sales_order_details_bistro fso \n", "            where fso.order_create_dt_ist BETWEEN (CURRENT_DATE - INTERVAL '4' DAY) AND (CURRENT_DATE - INTERVAL '1' DAY)\n", ") fso\n", "    ON co.external_order_id = CAST(fso.order_id AS VARCHAR)\n", "LEFT JOIN (select \n", "                    order_checkout_dt_ist,\n", "                    order_checkout_ts_ist,\n", "                    ordeR_id,\n", "                    order_partner_assigned_ts_ist,\n", "                    order_delivered_ts_ist,\n", "                    total_items_quantity_delivered\n", "            from dwh.fact_supply_chain_order_details fs \n", "            where fs.order_checkout_dt_ist BETWEEN (CURRENT_DATE - INTERVAL '4' DAY) AND (CURRENT_DATE - INTERVAL '1' DAY)\n", "            \n", "            union \n", "            \n", "            select \n", "                    order_checkout_dt_ist,\n", "                    order_checkout_ts_ist,\n", "                    ordeR_id,\n", "                    order_partner_assigned_ts_ist,\n", "                    order_delivered_ts_ist,\n", "                    total_items_quantity_delivered\n", "            from bistro_etls.fact_supply_chain_order_details_bistro fs \n", "            where fs.order_checkout_dt_ist BETWEEN (CURRENT_DATE - INTERVAL '4' DAY) AND (CURRENT_DATE - INTERVAL '1' DAY)\n", ") fs\n", "    ON co.external_order_id = CAST(fs.order_id AS VARCHAR)\n", "LEFT JOIN (SELECT * FROM zomato.blinkit_etls.blinkit_cash_limt_karma_score ks \n", "WHERE ks.dt BETWEEN DATE_FORMAT((CURRENT_DATE - INTERVAL '4' DAY), '%%Y%%m%%d') AND DATE_FORMAT((CURRENT_DATE - INTERVAL '1' DAY), '%%Y%%m%%d')\n", ") ks \n", "    ON ks.dt = DATE_FORMAT(fso.order_create_dt_ist, '%%Y%%m%%d')  \n", "   AND t.delivery_driver_id = ks.delivery_driver_id\n", "LEFT JOIN address_confidence a \n", "    ON co.external_order_id = CAST(a.order_id AS VARCHAR)\n", "where t.dt BETWEEN DATE_FORMAT((CURRENT_DATE - INTERVAL '4' DAY), '%%Y%%m%%d') AND DATE_FORMAT((CURRENT_DATE - INTERVAL '1' DAY), '%%Y%%m%%d') \n", ")\n", "where rnk = 1 \n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "8c87c699-7e3d-4c8f-9c35-c2186e9fe8a0", "metadata": {}, "outputs": [], "source": ["# data = pd.read_sql(base_sql, trino_con)"]}, {"cell_type": "code", "execution_count": null, "id": "52c0e189-7354-489b-868e-5be7a0beaf98", "metadata": {}, "outputs": [], "source": ["# data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9c578398-6464-4426-981a-de0e628bcebe", "metadata": {}, "outputs": [], "source": ["# data[\"time\"] = pd.to_datetime(data[\"time\"], format=\"%%Y-%%m-%%d %%H:%%M:%%S\")"]}, {"cell_type": "code", "execution_count": null, "id": "83d8c768-eade-4369-aabb-dbc83c02e9ff", "metadata": {}, "outputs": [], "source": ["# data[\"dp_karma_score\"] = data[\"dp_karma_score\"].astype(float)\n", "# data[\"osrm_distance\"] = data[\"osrm_distance\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "c0395f5e-3834-456f-bb86-db422f1c881d", "metadata": {}, "outputs": [], "source": ["# data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "e52f4c19-0e4e-4a5c-95ad-75dab731201e", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"trip_quality_details\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"dt\", \"type\": \"varchar\", \"description\": \"dt\"},\n", "        {\"name\": \"city_id\", \"type\": \"integer\", \"description\": \"city id\"},\n", "        {\"name\": \"trip_id\", \"type\": \"bigint\", \"description\": \"trip_id\"},\n", "        {\"name\": \"order_id\", \"type\": \"bigint\", \"description\": \"order_id\"},\n", "        {\"name\": \"time\", \"type\": \"timestamp(6)\", \"description\": \"timestamp\"},\n", "        {\"name\": \"is_clubbed_trip\", \"type\": \"integer\", \"description\": \"cubbed_flag\"},\n", "        {\n", "            \"name\": \"expected_drop_distance\",\n", "            \"type\": \"double\",\n", "            \"description\": \"expected_drop_distance\",\n", "        },\n", "        {\n", "            \"name\": \"carthero_raw_google\",\n", "            \"type\": \"double\",\n", "            \"description\": \"carthero_raw_google\",\n", "        },\n", "        {\"name\": \"aerial_distance\", \"type\": \"double\", \"description\": \"aerial_distance\"},\n", "        {\n", "            \"name\": \"drop_accounting_distance\",\n", "            \"type\": \"double\",\n", "            \"description\": \"drop_accounting_distance\",\n", "        },\n", "        {\n", "            \"name\": \"drop_google_distance\",\n", "            \"type\": \"double\",\n", "            \"description\": \"drop_google_distance\",\n", "        },\n", "        {\n", "            \"name\": \"drop_ping_distance\",\n", "            \"type\": \"double\",\n", "            \"description\": \"drop_ping_distance\",\n", "        },\n", "        {\"name\": \"confidence\", \"type\": \"varchar\", \"description\": \"confidence\"},\n", "        {\n", "            \"name\": \"drop_used_ping_distance\",\n", "            \"type\": \"boolean\",\n", "            \"description\": \"drop_used_ping_distance\",\n", "        },\n", "        {\"name\": \"from_lat\", \"type\": \"double\", \"description\": \"from_lat\"},\n", "        {\"name\": \"from_long\", \"type\": \"double\", \"description\": \"from_long\"},\n", "        {\"name\": \"to_lat\", \"type\": \"double\", \"description\": \"to_lat\"},\n", "        {\"name\": \"to_long\", \"type\": \"double\", \"description\": \"to_long\"},\n", "        {\n", "            \"name\": \"google_routes_car_enabled\",\n", "            \"type\": \"boolean\",\n", "            \"description\": \"google_routes_car_enabled\",\n", "        },\n", "        {\n", "            \"name\": \"used_google_routes_car_distance\",\n", "            \"type\": \"boolean\",\n", "            \"description\": \"used_google_routes_car_distance\",\n", "        },\n", "        {\n", "            \"name\": \"google_routes_car_distance\",\n", "            \"type\": \"double\",\n", "            \"description\": \"google_routes_car_distance\",\n", "        },\n", "        {\n", "            \"name\": \"google_routes_bike_distance\",\n", "            \"type\": \"double\",\n", "            \"description\": \"google_routes_bike_distance\",\n", "        },\n", "        {\n", "            \"name\": \"google_routes_bike_enabled\",\n", "            \"type\": \"boolean\",\n", "            \"description\": \"google_routes_bike_enabled\",\n", "        },\n", "        {\n", "            \"name\": \"used_google_routes_bike_distance\",\n", "            \"type\": \"boolean\",\n", "            \"description\": \"used_google_routes_bike_distance\",\n", "        },\n", "        {\"name\": \"actual_earnings\", \"type\": \"double\", \"description\": \"actual_earnings\"},\n", "        {\n", "            \"name\": \"expected_earnings\",\n", "            \"type\": \"double\",\n", "            \"description\": \"expected_earnings\",\n", "        },\n", "        {\"name\": \"tab_id1\", \"type\": \"varchar\", \"description\": \"tab_id1\"},\n", "        {\"name\": \"tab_id2\", \"type\": \"varchar\", \"description\": \"tab_id2\"},\n", "        {\"name\": \"tab_id3\", \"type\": \"varchar\", \"description\": \"tab_id3\"},\n", "        {\"name\": \"tab_id4\", \"type\": \"varchar\", \"description\": \"tab_id4\"},\n", "        {\n", "            \"name\": \"delivery_driver_id\",\n", "            \"type\": \"bigint\",\n", "            \"description\": \"delivery_driver_id\",\n", "        },\n", "        {\"name\": \"drop_distance\", \"type\": \"double\", \"description\": \"drop_distance\"},\n", "        {\n", "            \"name\": \"drop_distance_pay\",\n", "            \"type\": \"double\",\n", "            \"description\": \"drop_distance_pay\",\n", "        },\n", "        {\"name\": \"ldrp\", \"type\": \"double\", \"description\": \"ldrp\"},\n", "        {\"name\": \"customer_id\", \"type\": \"bigint\", \"description\": \"customer_id\"},\n", "        {\"name\": \"merchant_id\", \"type\": \"bigint\", \"description\": \"merchant_id\"},\n", "        {\"name\": \"cx_karma_label\", \"type\": \"varchar\", \"description\": \"cx_karma_label\"},\n", "        {\"name\": \"cx_karma_score\", \"type\": \"integer\", \"description\": \"cx_karma_score\"},\n", "        {\"name\": \"dp_karma_score\", \"type\": \"double\", \"description\": \"dp_karma_score\"},\n", "        {\n", "            \"name\": \"accounting_distance_used\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"accounting_distance_used\",\n", "        },\n", "        {\"name\": \"busy_time_sec\", \"type\": \"bigint\", \"description\": \"busy_time_sec\"},\n", "        {\n", "            \"name\": \"total_items_quantity_delivered\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_items_quantity_delivered\",\n", "        },\n", "        {\n", "            \"name\": \"total_product_quantity\",\n", "            \"type\": \"bigint\",\n", "            \"description\": \"total_product_quantity\",\n", "        },\n", "        {\n", "            \"name\": \"total_selling_price\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_selling_price\",\n", "        },\n", "        {\n", "            \"name\": \"order_current_status\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"order_current_status\",\n", "        },\n", "        {\n", "            \"name\": \"address_confidence\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"address_confidence\",\n", "        },\n", "        {\n", "            \"name\": \"drop_cell_token_l17\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"drop_cell_token_l17\",\n", "        },\n", "        {\n", "            \"name\": \"store_cell_token_l17\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"store_cell_token_l17\",\n", "        },\n", "        {\n", "            \"name\": \"osrm_distance\",\n", "            \"type\": \"double\",\n", "            \"description\": \"osrm_distance\",\n", "        },\n", "        {\n", "            \"name\": \"used_osrm_distance\",\n", "            \"type\": \"boolean\",\n", "            \"description\": \"used_osrm_distance\",\n", "        },\n", "        {\n", "            \"name\": \"drop_used_rd\",\n", "            \"type\": \"boolean\",\n", "            \"description\": \"used_osrm_distance\",\n", "        },\n", "        {\n", "            \"name\": \"drop_rd\",\n", "            \"type\": \"double\",\n", "            \"description\": \"drop_road\",\n", "        },\n", "        {\n", "            \"name\": \"ping_to_google_percentage\",\n", "            \"type\": \"double\",\n", "            \"description\": \"drop_road\",\n", "        },\n", "        {\n", "            \"name\": \"ping_to_google_percentage_threshold\",\n", "            \"type\": \"double\",\n", "            \"description\": \"ping_to_google_percentage_threshold\",\n", "        },\n", "        {\n", "            \"name\": \"google_to_aerial_ratio\",\n", "            \"type\": \"double\",\n", "            \"description\": \"google_to_aerial_ratio\",\n", "        },\n", "        {\n", "            \"name\": \"google_to_aerial_ratio_threshold\",\n", "            \"type\": \"double\",\n", "            \"description\": \"google_to_aerial_ratio_threshold\",\n", "        },\n", "        {\n", "            \"name\": \"is_distance_fraud\",\n", "            \"type\": \"boolean\",\n", "            \"description\": \"is_distance_fraud\",\n", "        },\n", "        {\n", "            \"name\": \"drop_cell_token_l16\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"drop_cell_token_l16\",\n", "        },\n", "        {\n", "            \"name\": \"store_cell_token_l16\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"store_cell_token_l16\",\n", "        },\n", "        {\n", "            \"name\": \"return_distance\",\n", "            \"type\": \"double\",\n", "            \"description\": \"return_distance\",\n", "        },\n", "        {\n", "            \"name\": \"google_distance_polyline\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"google_distance_polyline\",\n", "        },\n", "        {\n", "            \"name\": \"ping_distance_polyline\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"ping_distance_polyline\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"dt\", \"trip_id\", \"order_id\"],\n", "    \"incremental_key\": \"dt\",\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"Trip pings data\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}\n", "\n", "pb.to_trino(base_sql, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "a1e4ad1d-f3b6-45e0-8e14-e2867f6188bb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "be0716bd-1ecf-428b-906b-966e1def7130", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "173e4282-2b5a-4f8e-b3de-e82d45f0daf7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "17377795-294f-4170-935c-09589f26b46b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}