alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 3
dag_name: agg_weekly_rider_cash_pilferage_new
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: logistics
notebooks:
- executor_config:
    load_type: medium
    node_type: spot
  name: notebook1
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: medium
    node_type: spot
  name: notebook2
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: medium
    node_type: spot
  name: notebook3
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: medium
    node_type: spot
  name: notebook_final
  parameters: null
  retries: 2
  tag: second
owner:
  email: <EMAIL>
  slack_id: U082DBVETNX
path: logistics/agg_weekly_rider_cash_pilferage/etl/agg_weekly_rider_cash_pilferage_new
paused: false
pool: logistics_pool
project_name: agg_weekly_rider_cash_pilferage
schedule:
  end_date: '2025-09-05T00:00:00'
  interval: 0 2 * * *
  start_date: '2025-03-23T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 2
