{"cells": [{"cell_type": "code", "execution_count": null, "id": "1748cb95-c3bb-4a2c-a697-1c3e13b120ea", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import datetime as dt\n", "from tqdm import tqdm"]}, {"cell_type": "code", "execution_count": null, "id": "4e438ff9-b076-4992-b59c-7d0f4e6b0aa6", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "1bf30409-762a-4ce8-b9fa-20336288d3e5", "metadata": {}, "outputs": [], "source": ["final_df = pd.read_sql_query(\n", "    sql=f\"\"\"\n", "Select * from interim.agg_weekly_rider_cash_pilferage_part1\n", "\"\"\",\n", "    con=con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "40ea441b-17f0-4bb7-bab4-5cf8d6edeb69", "metadata": {}, "outputs": [], "source": ["final_df"]}, {"cell_type": "code", "execution_count": null, "id": "d679dbad-53a6-48e3-a1bd-59b439d648e0", "metadata": {}, "outputs": [], "source": ["final_df_2 = pd.read_sql_query(\n", "    sql=f\"\"\"\n", "Select * from interim.agg_weekly_rider_cash_pilferage_part2\n", "\"\"\",\n", "    con=con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "99aee3a6-34a7-425d-893b-7e6c0cad40bd", "metadata": {}, "outputs": [], "source": ["final_df_2"]}, {"cell_type": "code", "execution_count": null, "id": "b79b2cb6-db89-4e36-9185-112fc308b93b", "metadata": {}, "outputs": [], "source": ["final_df_3 = pd.read_sql_query(\n", "    sql=f\"\"\"\n", "Select * from interim.agg_weekly_rider_cash_pilferage_part3\n", "\"\"\",\n", "    con=con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "384265dd-fafa-45bc-9cfd-6f5cc03ffc1a", "metadata": {}, "outputs": [], "source": ["final_df_3"]}, {"cell_type": "code", "execution_count": null, "id": "9b06974b-bf92-4f86-8681-423918d22c16", "metadata": {}, "outputs": [], "source": ["final_df_collated = pd.concat([final_df, final_df_2, final_df_3])"]}, {"cell_type": "code", "execution_count": null, "id": "efbaef8b-2c31-4f2c-a3d1-afa2d73c3967", "metadata": {}, "outputs": [], "source": ["final_df_collated"]}, {"cell_type": "code", "execution_count": null, "id": "8beb398b-5b70-4627-9b56-c9494c2c5192", "metadata": {}, "outputs": [], "source": ["final_df_collated[\"updated_at_ist\"] = dt.datetime.now() + dt.<PERSON><PERSON><PERSON>(minutes=330)"]}, {"cell_type": "code", "execution_count": null, "id": "2db18e5c-9e55-468a-811d-508f3332cd2e", "metadata": {}, "outputs": [], "source": ["final_df_collated.drop(\"rider_date_rank\", axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "6500a599-0063-467e-81c3-dab9cb91fe1f", "metadata": {}, "outputs": [], "source": ["final_df_collated.columns = final_df_collated.columns.str.lower()"]}, {"cell_type": "code", "execution_count": null, "id": "dac4c80d-db0c-4dd2-8385-65e57428dc72", "metadata": {}, "outputs": [], "source": ["final_df_collated = final_df_collated[\n", "    [\n", "        \"week\",\n", "        \"week_ending\",\n", "        \"driver_id\",\n", "        \"delivery_driver_id\",\n", "        \"last_delivery_date\",\n", "        \"cod_transactions\",\n", "        \"refund_transactions\",\n", "        \"upi\",\n", "        \"razorpay\",\n", "        \"airtel\",\n", "        \"sm_cash_deposit\",\n", "        \"payout_deduction\",\n", "        \"pending\",\n", "        \"deposits\",\n", "        \"z_cod_transactions\",\n", "        \"b_cod_transactions\",\n", "        \"pending_cum_sum\",\n", "        \"total_liability_on_week\",\n", "        \"b_share\",\n", "        \"z_share\",\n", "        \"b_share_perc\",\n", "        \"z_share_perc\",\n", "        \"b_dep\",\n", "        \"z_dep\",\n", "        \"rider_bal\",\n", "        \"b_updated\",\n", "        \"z_updated\",\n", "        \"rider_date_rank_all\",\n", "        \"max_zero_pilferage_rank\",\n", "        \"driver_tag\",\n", "        \"updated_at_ist\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "9ec27814-a44e-4684-b3b2-f794d3cbfdcb", "metadata": {}, "outputs": [], "source": ["final_df_collated.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "780967f8-9375-4d2f-9c5b-44ed37df51a4", "metadata": {}, "outputs": [], "source": ["final_df_collated = final_df_collated.astype(\n", "    {\n", "        \"week_ending\": \"datetime64[ns]\",\n", "        \"last_delivery_date\": \"datetime64[ns]\",\n", "        \"cod_transactions\": \"float64\",\n", "        \"refund_transactions\": \"float64\",\n", "        \"upi\": \"float64\",\n", "        \"razorpay\": \"float64\",\n", "        \"airtel\": \"float64\",\n", "        \"sm_cash_deposit\": \"float64\",\n", "        \"payout_deduction\": \"float64\",\n", "        \"pending\": \"float64\",\n", "        \"deposits\": \"float64\",\n", "        \"z_cod_transactions\": \"float64\",\n", "        \"b_cod_transactions\": \"float64\",\n", "        \"pending_cum_sum\": \"float64\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8608cef3-9d2c-4f14-8d7d-24ea1d6d45d9", "metadata": {}, "outputs": [], "source": ["final_df_collated.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "f1e27676-391a-4875-a616-738696e69997", "metadata": {}, "outputs": [], "source": ["final_df_collated[\n", "    final_df_collated.dtypes[final_df_collated.dtypes == \"float64\"].index\n", "] = final_df_collated[final_df_collated.dtypes[final_df_collated.dtypes == \"float64\"].index].apply(\n", "    lambda x: np.round(x, 2)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b8047f6d-affe-408d-a520-c6220f58f1c4", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"agg_weekly_rider_cash_pilferage\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"week\", \"type\": \"integer\", \"description\": \" \"},\n", "        {\"name\": \"week_ending\", \"type\": \"date\", \"description\": \" \"},\n", "        {\"name\": \"driver_id\", \"type\": \"varchar\", \"description\": \" \"},\n", "        {\"name\": \"delivery_driver_id\", \"type\": \"integer\", \"description\": \" \"},\n", "        {\n", "            \"name\": \"last_delivery_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"last delivery date of partner as of updated_at_ist\",\n", "        },\n", "        {\n", "            \"name\": \"z_cod_transactions\",\n", "            \"type\": \"double\",\n", "            \"description\": \"z_cod_collected_amount \",\n", "        },\n", "        {\n", "            \"name\": \"b_cod_transactions\",\n", "            \"type\": \"double\",\n", "            \"description\": \"b_cod_collected_amount \",\n", "        },\n", "        {\n", "            \"name\": \"cod_transactions\",\n", "            \"type\": \"double\",\n", "            \"description\": \"cod amount collected by partner\",\n", "        },\n", "        {\n", "            \"name\": \"refund_transactions\",\n", "            \"type\": \"double\",\n", "            \"description\": \" refund_deposit_amount\",\n", "        },\n", "        {\"name\": \"upi\", \"type\": \"double\", \"description\": \" upi_deposit_amount\"},\n", "        {\n", "            \"name\": \"razorpay\",\n", "            \"type\": \"double\",\n", "            \"description\": \"razorpay_deposit_amount \",\n", "        },\n", "        {\"name\": \"airtel\", \"type\": \"double\", \"description\": \" airtel_deposit_amount\"},\n", "        {\n", "            \"name\": \"sm_cash_deposit\",\n", "            \"type\": \"double\",\n", "            \"description\": \"sm_cash_deposit_amount\",\n", "        },\n", "        {\n", "            \"name\": \"payout_deduction\",\n", "            \"type\": \"double\",\n", "            \"description\": \"payout_deduction_amount\",\n", "        },\n", "        {\"name\": \"pending\", \"type\": \"double\", \"description\": \"weekly_pending_amount\"},\n", "        {\n", "            \"name\": \"deposits\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total amount recovered from partner\",\n", "        },\n", "        {\n", "            \"name\": \"pending_cum_sum\",\n", "            \"type\": \"double\",\n", "            \"description\": \"running provision\",\n", "        },\n", "        {\"name\": \"total_liability_on_week\", \"type\": \"double\", \"description\": \" \"},\n", "        {\"name\": \"b_share\", \"type\": \"double\", \"description\": \" \"},\n", "        {\"name\": \"z_share\", \"type\": \"double\", \"description\": \" \"},\n", "        {\"name\": \"b_share_perc\", \"type\": \"double\", \"description\": \" \"},\n", "        {\"name\": \"z_share_perc\", \"type\": \"double\", \"description\": \" \"},\n", "        {\"name\": \"b_dep\", \"type\": \"double\", \"description\": \" \"},\n", "        {\"name\": \"z_dep\", \"type\": \"double\", \"description\": \" \"},\n", "        {\"name\": \"rider_bal\", \"type\": \"double\", \"description\": \" \"},\n", "        {\"name\": \"b_updated\", \"type\": \"double\", \"description\": \" \"},\n", "        {\"name\": \"z_updated\", \"type\": \"double\", \"description\": \" \"},\n", "        {\"name\": \"rider_date_rank_all\", \"type\": \"double\", \"description\": \" \"},\n", "        {\"name\": \"max_zero_pilferage_rank\", \"type\": \"double\", \"description\": \" \"},\n", "        {\n", "            \"name\": \"driver_tag\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"based on how many services driver's been with and if driver has ever cleared pendency 100%\",\n", "        },\n", "        {\"name\": \"updated_at_ist\", \"type\": \"timestamp(6)\", \"description\": \" \"},\n", "    ],\n", "    \"primary_key\": [\n", "        \"week_ending\",\n", "        \"delivery_driver_id\",\n", "        \"driver_tag\",\n", "        \"updated_at_ist\",\n", "    ],\n", "    \"partition_key\": [\"updated_at_ist\"],\n", "    \"incremental_key\": \"updated_at_ist\",\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"this table has data for blinkit and z pendency of riders who worked with b, based on proportion of weekly pendency\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "0906a6af-9607-43e8-a60e-f170da484d6a", "metadata": {}, "outputs": [], "source": ["pb.to_trino(final_df_collated, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "9f35bb4f-e888-4a7b-b5c6-5a91a9049019", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}