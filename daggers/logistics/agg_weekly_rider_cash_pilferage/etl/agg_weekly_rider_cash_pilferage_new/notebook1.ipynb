{"cells": [{"cell_type": "code", "execution_count": null, "id": "6667313b-0df4-4209-994d-d5703f9eec0b", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import datetime as dt\n", "from tqdm import tqdm"]}, {"cell_type": "code", "execution_count": null, "id": "1663263f-cc35-41d6-bb11-efe0a80cd67e", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "8986bfb6-facb-44f3-bee5-33f4942ecc23", "metadata": {}, "outputs": [], "source": ["driver_cash_history = pd.read_sql_query(\n", "    sql=f\"\"\"\n", "    WITH drivers_moving_bw_service AS (\n", "    SELECT delivery_driver_id\n", "    FROM (\n", "        SELECT\n", "            delivery_driver_id,\n", "            COUNT(DISTINCT driver_category) AS unique_driver_categories_count\n", "        FROM interim.riders_cash_pilferage_tesing2\n", "        GROUP BY delivery_driver_id\n", "    )\n", "    WHERE unique_driver_categories_count > 1\n", "),\n", "driver_cash_history AS (\n", "    SELECT *\n", "    FROM interim.riders_cash_pilferage_tesing2\n", "    WHERE delivery_driver_id IN (SELECT delivery_driver_id FROM drivers_moving_bw_service)\n", "),\n", "driver_cash_history_1 AS (\n", "    SELECT\n", "        *,\n", "        refund_transactions + upi + razorpay + airtel + sm_cash_deposit + payout_deduction AS deposits,\n", "        CASE WHEN driver_category = 'zomato' THEN cod_transactions ELSE 0 END AS z_cod_transactions,\n", "        CASE WHEN driver_category = 'blinkit' THEN cod_transactions ELSE 0 END AS b_cod_transactions\n", "    FROM driver_cash_history\n", "),\n", "max_ref_date_per_week AS (\n", "    SELECT\n", "        week,\n", "        MAX(ref_date) AS week_ending\n", "    FROM driver_cash_history_1\n", "    GROUP BY week\n", "),\n", "base AS (\n", "    SELECT\n", "        dch.*,\n", "        mrd.week_ending\n", "    FROM driver_cash_history_1 dch\n", "    LEFT JOIN max_ref_date_per_week mrd\n", "    ON dch.week = mrd.week \n", "),\n", "base2 AS (\n", "    SELECT\n", "        week,\n", "        week_ending,\n", "        driver_id,\n", "        delivery_driver_id,\n", "        last_delivery_date,\n", "        SUM(cod_transactions) AS cod_transactions,\n", "        SUM(refund_transactions) AS refund_transactions,\n", "        SUM(upi) AS upi,\n", "        SUM(razorpay) AS razorpay,\n", "        SUM(airtel) AS airtel,\n", "        SUM(sm_cash_deposit) AS sm_cash_deposit,\n", "        SUM(payout_deduction) AS payout_deduction,\n", "        SUM(pending) AS pending,\n", "        SUM(deposits) AS deposits,\n", "        SUM(z_cod_transactions) AS z_cod_transactions,\n", "        SUM(b_cod_transactions) AS b_cod_transactions\n", "    FROM base\n", "    GROUP BY week,week_ending, driver_id, delivery_driver_id, last_delivery_date\n", ")\n", ",base3 AS (\n", "    SELECT\n", "        *,\n", "        SUM(pending) OVER (PARTITION BY delivery_driver_id ORDER BY week_ending) AS pending_cum_sum,\n", "        RANK() OVER (PARTITION BY delivery_driver_id ORDER BY week_ending) AS rider_date_rank_all\n", "    FROM base2\n", ")\n", ",max_zero_pilferage_rank AS (\n", "    SELECT\n", "        delivery_driver_id,\n", "        MAX(rider_date_rank_all) AS max_zero_pilferage_rank\n", "    FROM base3\n", "    WHERE pending_cum_sum = 0\n", "    GROUP BY delivery_driver_id\n", ")\n", "    select *\n", "    from(\n", "    Select * ,\n", "    RANK() OVER (PARTITION BY delivery_driver_id ORDER BY week_ending) as rider_date_rank\n", "    FROM (SELECT\n", "        fo.*,\n", "        coalesce(mzp.max_zero_pilferage_rank,0) as max_zero_pilferage_rank\n", "    FROM base3 fo\n", "    LEFT JOIN max_zero_pilferage_rank mzp\n", "    ON fo.delivery_driver_id = mzp.delivery_driver_id\n", "    )\n", "    WHERE rider_date_rank_all > max_zero_pilferage_rank\n", "    )\n", "\"\"\",\n", "    con=con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "709dcd5b-fe27-4687-b179-79b2c45ae6ff", "metadata": {}, "outputs": [], "source": ["driver_cash_history"]}, {"cell_type": "code", "execution_count": null, "id": "db0c02ff-097e-49d7-bf4e-762edacdaced", "metadata": {}, "outputs": [], "source": ["driver_cash_history = driver_cash_history.assign(\n", "    total_liability_on_week=0,\n", "    b_share=0,\n", "    z_share=0,\n", "    b_share_perc=0,\n", "    z_share_perc=0,\n", "    b_dep=0,\n", "    z_dep=0,\n", "    rider_bal=0,\n", "    b_updated=0,\n", "    z_updated=0,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5e5a42ed-a402-4daf-87fa-f9a9f8e6048a", "metadata": {}, "outputs": [], "source": ["driver_cash_history = driver_cash_history.astype(\n", "    {\n", "        \"week_ending\": \"datetime64[ns]\",\n", "        \"last_delivery_date\": \"datetime64[ns]\",\n", "        \"cod_transactions\": \"float64\",\n", "        \"refund_transactions\": \"float64\",\n", "        \"upi\": \"float64\",\n", "        \"razorpay\": \"float64\",\n", "        \"airtel\": \"float64\",\n", "        \"sm_cash_deposit\": \"float64\",\n", "        \"payout_deduction\": \"float64\",\n", "        \"pending\": \"float64\",\n", "        \"deposits\": \"float64\",\n", "        \"z_cod_transactions\": \"float64\",\n", "        \"b_cod_transactions\": \"float64\",\n", "        \"pending_cum_sum\": \"float64\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "80209c7c-c494-49d4-90f2-d68c8a872233", "metadata": {}, "outputs": [], "source": ["# since there's no preceding row, first row won't be refering any other row, after that, every row will refer n-1th row, like a for loop\n", "\n", "# total liability that day (before cash deposit)\n", "driver_cash_history.loc[driver_cash_history.rider_date_rank == 1, \"total_liability_on_week\"] = (\n", "    driver_cash_history[driver_cash_history.rider_date_rank == 1].pending_cum_sum\n", "    - driver_cash_history[driver_cash_history.rider_date_rank == 1].deposits\n", ")\n", "\n", "# ===  running liability -->\n", "driver_cash_history.loc[driver_cash_history.rider_date_rank == 1, \"b_share\"] = driver_cash_history[\n", "    driver_cash_history.rider_date_rank == 1\n", "].b_cod_transactions\n", "driver_cash_history.loc[driver_cash_history.rider_date_rank == 1, \"z_share\"] = driver_cash_history[\n", "    driver_cash_history.rider_date_rank == 1\n", "].z_cod_transactions\n", "\n", "\n", "# ===  running liability/ total liability -->\n", "driver_cash_history.loc[driver_cash_history.rider_date_rank == 1, \"b_share_perc\"] = (\n", "    driver_cash_history[driver_cash_history.rider_date_rank == 1].b_cod_transactions\n", "    / driver_cash_history[driver_cash_history.rider_date_rank == 1].total_liability_on_week\n", ")\n", "driver_cash_history.loc[driver_cash_history.rider_date_rank == 1, \"z_share_perc\"] = (\n", "    driver_cash_history[driver_cash_history.rider_date_rank == 1].z_cod_transactions\n", "    / driver_cash_history[driver_cash_history.rider_date_rank == 1].total_liability_on_week\n", ")\n", "\n", "\n", "# === deposits as per proportions, and at max the liability, rest goes to rider balance\n", "driver_cash_history.loc[driver_cash_history.rider_date_rank == 1, \"b_dep\"] = np.minimum(\n", "    np.abs(\n", "        driver_cash_history.b_share_perc[driver_cash_history.rider_date_rank == 1]\n", "        * driver_cash_history.deposits[driver_cash_history.rider_date_rank == 1]\n", "    ),\n", "    driver_cash_history.b_share[driver_cash_history.rider_date_rank == 1],\n", ")\n", "\n", "driver_cash_history.loc[driver_cash_history.rider_date_rank == 1, \"z_dep\"] = np.minimum(\n", "    np.abs(\n", "        driver_cash_history.z_share_perc[driver_cash_history.rider_date_rank == 1]\n", "        * driver_cash_history.deposits[driver_cash_history.rider_date_rank == 1]\n", "    ),\n", "    driver_cash_history.z_share[driver_cash_history.rider_date_rank == 1],\n", ")\n", "\n", "\n", "driver_cash_history.loc[\n", "    driver_cash_history.rider_date_rank == 1, \"rider_bal\"\n", "] = driver_cash_history.deposits[driver_cash_history.rider_date_rank == 1] + driver_cash_history[\n", "    driver_cash_history.rider_date_rank == 1\n", "][\n", "    [\"b_dep\", \"z_dep\"]\n", "].sum(\n", "    axis=1\n", ")\n", "\n", "\n", "### final balance = share - deposited amount->\n", "driver_cash_history.loc[driver_cash_history.rider_date_rank == 1, \"b_updated\"] = (\n", "    driver_cash_history.b_share[driver_cash_history.rider_date_rank == 1]\n", "    - driver_cash_history.b_dep[driver_cash_history.rider_date_rank == 1]\n", ")\n", "driver_cash_history.loc[driver_cash_history.rider_date_rank == 1, \"z_updated\"] = (\n", "    driver_cash_history.z_share[driver_cash_history.rider_date_rank == 1]\n", "    - driver_cash_history.z_dep[driver_cash_history.rider_date_rank == 1]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8fafb41c-c745-4280-9d6f-be4378e9e0aa", "metadata": {}, "outputs": [], "source": ["driver_cash_history.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "9e584d28-dc73-443f-93f4-4a0a9fda9a20", "metadata": {}, "outputs": [], "source": ["final_list = []\n", "a = 0\n", "for j in tqdm(driver_cash_history.delivery_driver_id.unique()):\n", "    temp_df = driver_cash_history[driver_cash_history.delivery_driver_id == j].reset_index(\n", "        drop=True\n", "    )\n", "    # doing the same for preceding rows\n", "    if temp_df.shape[0] > 1:\n", "        for i in temp_df.index:\n", "            if i > 0:\n", "                a += 1\n", "                # total liability that day (before cash deposit)\n", "                temp_df.loc[i, \"total_liability_on_week\"] = (\n", "                    temp_df.pending_cum_sum[i] - temp_df.deposits[i] - temp_df.rider_bal[i - 1]\n", "                )\n", "                # running liability = today's cod - yday's balance\n", "                temp_df.loc[i, \"b_share\"] = np.nansum(\n", "                    [temp_df.b_cod_transactions[i], temp_df.b_updated[i - 1]]\n", "                )\n", "                temp_df.loc[i, \"z_share\"] = np.nansum(\n", "                    [temp_df.z_cod_transactions[i], temp_df.z_updated[i - 1]]\n", "                )\n", "                # ===  liability share -->\n", "                temp_df.loc[i, \"b_share_perc\"] = np.nansum(\n", "                    [temp_df.b_share[i] / temp_df.total_liability_on_week[i]]\n", "                )  # to convert na to 0\n", "                temp_df.loc[i, \"z_share_perc\"] = np.nansum(\n", "                    [temp_df.z_share[i] / temp_df.total_liability_on_week[i]]\n", "                )\n", "                # print(i)\n", "\n", "                # === deposits as per proportions, and at max the liability, rest goes to rider balance\n", "                temp_df.loc[i, \"b_dep\"] = np.minimum(\n", "                    np.abs(\n", "                        temp_df.b_share_perc[i] * (temp_df.deposits[i] + temp_df.rider_bal[i - 1])\n", "                    ),\n", "                    temp_df.b_share[i],\n", "                )\n", "                temp_df.loc[i, \"z_dep\"] = np.minimum(\n", "                    np.abs(\n", "                        temp_df.z_share_perc[i] * (temp_df.deposits[i] + temp_df.rider_bal[i - 1])\n", "                    ),\n", "                    temp_df.z_share[i],\n", "                )\n", "\n", "                temp_df.loc[i, \"rider_bal\"] = (\n", "                    temp_df.deposits[i]\n", "                    + temp_df.loc[i, [\"b_dep\", \"z_dep\"]].sum()\n", "                    + temp_df.rider_bal[i - 1]\n", "                )\n", "\n", "                ### final balance = share - deposited amount->\n", "                temp_df.loc[i, \"b_updated\"] = temp_df.b_share[i] - temp_df.b_dep[i]\n", "                temp_df.loc[i, \"z_updated\"] = temp_df.z_share[i] - temp_df.z_dep[i]\n", "\n", "    final_list.append(temp_df)"]}, {"cell_type": "code", "execution_count": null, "id": "2952c80a-6c48-4e46-b414-555754a2dd96", "metadata": {}, "outputs": [], "source": ["final_df = pd.concat(final_list)"]}, {"cell_type": "code", "execution_count": null, "id": "bb2d354c-9950-490a-940a-c8efd3460f78", "metadata": {}, "outputs": [], "source": ["final_df[\"driver_tag\"] = \"two_or_more_post_last_clearance\""]}, {"cell_type": "code", "execution_count": null, "id": "c9959442-7708-4610-aa75-5fd96c4ce2d1", "metadata": {}, "outputs": [], "source": ["final_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "f5eaed1d-8fb7-4f11-8af4-df808629e1b2", "metadata": {}, "outputs": [], "source": ["final_df = final_df[\n", "    [\n", "        \"week\",\n", "        \"week_ending\",\n", "        \"driver_id\",\n", "        \"delivery_driver_id\",\n", "        \"last_delivery_date\",\n", "        \"cod_transactions\",\n", "        \"refund_transactions\",\n", "        \"upi\",\n", "        \"razorpay\",\n", "        \"airtel\",\n", "        \"sm_cash_deposit\",\n", "        \"payout_deduction\",\n", "        \"pending\",\n", "        \"deposits\",\n", "        \"z_cod_transactions\",\n", "        \"b_cod_transactions\",\n", "        \"pending_cum_sum\",\n", "        \"total_liability_on_week\",\n", "        \"b_share\",\n", "        \"z_share\",\n", "        \"b_share_perc\",\n", "        \"z_share_perc\",\n", "        \"b_dep\",\n", "        \"z_dep\",\n", "        \"rider_bal\",\n", "        \"b_updated\",\n", "        \"z_updated\",\n", "        \"rider_date_rank_all\",\n", "        \"max_zero_pilferage_rank\",\n", "        \"driver_tag\",\n", "        \"rider_date_rank\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "2f15b038-ce80-4a7b-9fae-10ec253167e1", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"agg_weekly_rider_cash_pilferage_part1\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"week\", \"type\": \"integer\", \"description\": \" \"},\n", "        {\"name\": \"week_ending\", \"type\": \"date\", \"description\": \" \"},\n", "        {\"name\": \"driver_id\", \"type\": \"varchar\", \"description\": \" \"},\n", "        {\"name\": \"delivery_driver_id\", \"type\": \"integer\", \"description\": \" \"},\n", "        {\n", "            \"name\": \"last_delivery_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"last delivery date of partner as of updated_at_ist\",\n", "        },\n", "        {\n", "            \"name\": \"z_cod_transactions\",\n", "            \"type\": \"double\",\n", "            \"description\": \"z_cod_collected_amount \",\n", "        },\n", "        {\n", "            \"name\": \"b_cod_transactions\",\n", "            \"type\": \"double\",\n", "            \"description\": \"b_cod_collected_amount \",\n", "        },\n", "        {\n", "            \"name\": \"cod_transactions\",\n", "            \"type\": \"double\",\n", "            \"description\": \"cod amount collected by partner\",\n", "        },\n", "        {\n", "            \"name\": \"refund_transactions\",\n", "            \"type\": \"double\",\n", "            \"description\": \" refund_deposit_amount\",\n", "        },\n", "        {\"name\": \"upi\", \"type\": \"double\", \"description\": \" upi_deposit_amount\"},\n", "        {\n", "            \"name\": \"razorpay\",\n", "            \"type\": \"double\",\n", "            \"description\": \"razorpay_deposit_amount \",\n", "        },\n", "        {\"name\": \"airtel\", \"type\": \"double\", \"description\": \" airtel_deposit_amount\"},\n", "        {\n", "            \"name\": \"sm_cash_deposit\",\n", "            \"type\": \"double\",\n", "            \"description\": \"sm_cash_deposit_amount\",\n", "        },\n", "        {\n", "            \"name\": \"payout_deduction\",\n", "            \"type\": \"double\",\n", "            \"description\": \"payout_deduction_amount\",\n", "        },\n", "        {\"name\": \"pending\", \"type\": \"double\", \"description\": \"weekly_pending_amount\"},\n", "        {\n", "            \"name\": \"deposits\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total amount recovered from partner\",\n", "        },\n", "        {\n", "            \"name\": \"pending_cum_sum\",\n", "            \"type\": \"double\",\n", "            \"description\": \"running provision\",\n", "        },\n", "        {\"name\": \"total_liability_on_week\", \"type\": \"double\", \"description\": \" \"},\n", "        {\"name\": \"b_share\", \"type\": \"double\", \"description\": \" \"},\n", "        {\"name\": \"z_share\", \"type\": \"double\", \"description\": \" \"},\n", "        {\"name\": \"b_share_perc\", \"type\": \"double\", \"description\": \" \"},\n", "        {\"name\": \"z_share_perc\", \"type\": \"double\", \"description\": \" \"},\n", "        {\"name\": \"b_dep\", \"type\": \"double\", \"description\": \" \"},\n", "        {\"name\": \"z_dep\", \"type\": \"double\", \"description\": \" \"},\n", "        {\"name\": \"rider_bal\", \"type\": \"double\", \"description\": \" \"},\n", "        {\"name\": \"b_updated\", \"type\": \"double\", \"description\": \" \"},\n", "        {\"name\": \"z_updated\", \"type\": \"double\", \"description\": \" \"},\n", "        {\"name\": \"rider_date_rank_all\", \"type\": \"double\", \"description\": \" \"},\n", "        {\"name\": \"max_zero_pilferage_rank\", \"type\": \"double\", \"description\": \" \"},\n", "        {\n", "            \"name\": \"driver_tag\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"based on how many services driver's been with and if driver has ever cleared pendency 100%\",\n", "        },\n", "        {\"name\": \"rider_date_rank\", \"type\": \"double\", \"description\": \" \"},\n", "        # {\"name\": \"updated_at_ist\", \"type\": \"timestamp(6)\", \"description\": \" \"},\n", "    ],\n", "    \"primary_key\": [\n", "        \"week_ending\",\n", "        \"delivery_driver_id\",\n", "        \"driver_tag\",\n", "        # \"updated_at_ist\",\n", "    ],\n", "    # \"partition_key\": [\"updated_at_ist\"],\n", "    # \"incremental_key\": \"updated_at_ist\",\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"this table has data for blinkit and z pendency of riders who worked with b, based on proportion of weekly pendency\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "ade7c2e9-b6d4-4341-8bd5-75efd38735f1", "metadata": {}, "outputs": [], "source": ["pb.to_trino(final_df, **kwargs_trino)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}