alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: dp_login_chohort_week
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: logistics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U06JYRZ5VSL
path: logistics/dp_login_cohort/etl/dp_login_chohort_week
paused: false
pool: logistics_pool
project_name: dp_login_cohort
schedule:
  end_date: '2025-09-15T00:00:00'
  interval: 5 3 * * 1
  start_date: '2025-06-19T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 3
