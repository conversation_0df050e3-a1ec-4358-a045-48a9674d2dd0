{"cells": [{"cell_type": "code", "execution_count": null, "id": "250c3e4e-f3d4-4198-bbfa-465895c9c0f3", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import date"]}, {"cell_type": "code", "execution_count": null, "id": "5539766b-d213-4448-b73d-9e2294622c5c", "metadata": {}, "outputs": [], "source": ["df = pb.from_sheets(\n", "    sheetid=\"1hEtXAn9ykUbKb2SGn0JIFvqOgPC6IcmNG68iUkNI9dg\", sheetname=\"cpk_skew_new\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "841f9a17-5763-4885-b679-5f5555d02de9", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "93428acc-27c3-4f94-afcb-4f0a7eaece0c", "metadata": {}, "outputs": [], "source": ["df[\"entity_name\"] = df[\"entity_name\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "4d7c8417-edc1-4797-8ab7-dd210d90eddf", "metadata": {}, "outputs": [], "source": ["df[\"dow\"] = df[\"dow\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "20abc1a3-775a-46c4-b535-9ef9903fdc3a", "metadata": {}, "outputs": [], "source": ["df[\"hour\"] = df[\"hour\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "b702b0d2-4af0-4724-beaa-bc80e8cd70ff", "metadata": {}, "outputs": [], "source": ["df[\"cpk_factor\"] = df[\"cpk_factor\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "8729a5b2-94b7-44ca-b673-8b3c5a830e3c", "metadata": {}, "outputs": [], "source": ["df[\"start_date\"] = df[\"start_date\"].astype(str)\n", "df[\"dt\"] = df[\"dt\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "1abcebda-1f6a-46af-aa4f-c956b0693cd1", "metadata": {}, "outputs": [], "source": ["df[\"City_Store\"] = df[\"City_Store\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "d6cd1f31-f22e-4a38-a6e8-51fe1fd3e622", "metadata": {}, "outputs": [], "source": ["df = df.dropna()"]}, {"cell_type": "code", "execution_count": null, "id": "550cee2f-7dfd-4943-baa4-684c275c4b67", "metadata": {}, "outputs": [], "source": ["df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "f0bc6c59-a7b8-466d-81fa-1f79942f1425", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"new_city_skew_V1\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"entity_type\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"entity_type\",\n", "        },\n", "        {\n", "            \"name\": \"entity_name\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"unique identifier for city\",\n", "        },\n", "        {\n", "            \"name\": \"dow\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"day of the week\",\n", "        },\n", "        {\n", "            \"name\": \"hour\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"hour\",\n", "        },\n", "        {\n", "            \"name\": \"cpk_factor\",\n", "            \"type\": \"double\",\n", "            \"description\": \"slot wise skew of base cpo\",\n", "        },\n", "        {\n", "            \"name\": \"start_date\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"slot wise skew of base cpo\",\n", "        },\n", "        {\n", "            \"name\": \"dt\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"slot wise skew of base cpo\",\n", "        },\n", "        {\n", "            \"name\": \"City_Store\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Store/City Name\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"entity_name\", \"City_store\", \"dow\", \"hour\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"slot wise skew\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "eb6308ec-e565-4189-9be4-51dc72e53f36", "metadata": {}, "outputs": [], "source": ["pb.to_trino(df, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "9decef7f-ab3b-4edc-a858-4ab8efcb2b35", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4459f4ab-12ca-4485-b8ca-66dc0c1c65ab", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "67fcdfef-f8a6-412b-a556-c5c4c9749b25", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}