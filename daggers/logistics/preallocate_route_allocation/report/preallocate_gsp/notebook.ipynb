{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import requests\n", "import geopandas as gpd\n", "import pencilbox as pb\n", "\n", "red = pb.get_connection(\"redpen\")\n", "con = pb.get_connection(\"route_allocation\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import json\n", "import datetime"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MAX_RETRIES = 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_table(table_name, con, query=None):\n", "    \"\"\" For reading prerequisite data from database.\"\"\"\n", "    if query == None:\n", "        temp = pd.read_sql(\n", "            sql=\"SELECT * from route_allocation.route.{};\".format(table_name), con=con\n", "        )\n", "    else:\n", "        temp = pd.read_sql(sql=query.format(table_name), con=con)\n", "\n", "    return temp"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["capacity = read_table(\"capacity\", con)\n", "curr_date = str(capacity[\"delivery_date\"].iloc[0].date())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["curr_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eligible_stations = [\"HR22\", \"KOL1\", \"KOL6\", \"KOL9\", \"KOL8\", \"BA18\"]\n", "# eligible_stations = list(capacity[\"station\"].value_counts().keys())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "with\n", "base as\n", "(\n", "    select distinct\n", "        o.station as station, \n", "        m.external_id as frontend_merchant_id,\n", "        m.name as frontend_merchant_name,\n", "        oe.order_id,\n", "        o.cart_id,\n", "        oe.id as action_id,\n", "        (oe.update_ts  + interval '5.5 hour') as event_ts_ist,\n", "        oe.event_type_key,\n", "        json_extract_path_text(oe.extra ,'to',true) as event_state,\n", "        case when (event_state is null or event_state = '') and event_type_key = 'order_rescheduling' then 'RESCHEDULED'\n", "            else case when event_state is null or event_state = '' then event_type_key \n", "                    else event_state \n", "                end \n", "        end as event,\n", "        case when oe.event_type_key = 'order_rescheduling' \n", "            then (TIMESTAMP 'epoch' + \n", "                    (case when json_extract_path_text(json_extract_path_text(oe.extra,'old_slot_properties',true),'slot_freeze_time',true) = '' \n", "                            or json_extract_path_text(json_extract_path_text(oe.extra,'old_slot_properties',true),'slot_freeze_time',true) is null then null\n", "                    else cast(json_extract_path_text(json_extract_path_text(oe.extra,'old_slot_properties',true),'slot_freeze_time',true) as int)\n", "                    end) * INTERVAL '1 second' + interval '5.5 hour')\n", "            else (TIMESTAMP 'epoch' + \n", "                    (case when json_extract_path_text(o.slot_properties,'slot_freeze_time',true) = '' or json_extract_path_text(o.slot_properties,'slot_freeze_time',true) is null then null \n", "                            else cast(json_extract_path_text(o.slot_properties,'slot_freeze_time',true) as int) \n", "                    end) * INTERVAL '1 second' + interval '5.5 hour')\n", "        end as slot_freeze_time_ist,\n", "        case when oe.event_type_key='order_rescheduling' and is_valid_json(extra) = True \n", "                then (TIMESTAMP 'epoch' + \n", "                        (case when json_extract_path_text(extra,'old_slot_start',true) = '' or json_extract_path_text(extra,'old_slot_start',true) is null then 0 \n", "                        else cast(json_extract_path_text(extra,'old_slot_start',true) as int) end) * INTERVAL '1 second' + interval '5.5 hour') \n", "                else (TIMESTAMP 'epoch' + cast(o.slot_start as int) * INTERVAL '1 second' + interval '5.5 hour')\n", "        end as sch_slot_start_ist,\n", "        case when oe.event_type_key='order_rescheduling'and is_valid_json(extra) = True \n", "                then (TIMESTAMP 'epoch' + \n", "                    (case when json_extract_path_text(extra,'old_slot_end',true) = '' or json_extract_path_text(extra,'old_slot_end',true) is null then null \n", "                    else cast(json_extract_path_text(extra,'old_slot_end',true) as int) end) * INTERVAL '1 second' + interval '5.5 hour')\n", "            else (TIMESTAMP 'epoch' + cast(o.slot_end as int) * INTERVAL '1 second' + interval '5.5 hour')\n", "        end AS sch_slot_end_ist,\n", "        case when oe.event_type_key='order_rescheduling'and is_valid_json(extra) = True \n", "                then (TIMESTAMP 'epoch' + \n", "                    (case when json_extract_path_text(extra,'new_slot_start',true) = '' or json_extract_path_text(extra,'new_slot_start',true) is null then null\n", "                    else cast(json_extract_path_text(extra,'new_slot_start',true) as int) end) * INTERVAL '1 second' + interval '5.5 hour') \n", "            else (TIMESTAMP 'epoch' + cast(o.slot_start as int) * INTERVAL '1 second' + interval '5.5 hour')\n", "        end AS new_slot_start_ist,\n", "        case when oe.event_type_key='order_rescheduling'and is_valid_json(extra) = True \n", "                then (TIMESTAMP 'epoch' + \n", "                    (case when json_extract_path_text(extra,'new_slot_end',true) = '' or json_extract_path_text(extra,'new_slot_end',true) is null then null \n", "                    else cast(json_extract_path_text(extra,'new_slot_end',true) as int) end) * INTERVAL '1 second' + interval '5.5 hour')\n", "            else (TIMESTAMP 'epoch'+ cast(o.slot_end as int) * INTERVAL '1 second' + interval '5.5 hour')\n", "        end AS new_slot_end_ist,\n", "        case when oe.event_type_key='order_rescheduling' and is_valid_json(extra) = true\n", "                then json_extract_path_text(extra,'old_slot_start',true)\n", "        end as old_slot_start,\n", "        case when oe.event_type_key='order_rescheduling'and is_valid_json(extra) = true \n", "                then json_extract_path_text(extra,'old_slot_end',true) \n", "        end old_slot_end,\n", "        case when oe.event_type_key='order_rescheduling' and is_valid_json(extra) = true\n", "                then json_extract_path_text(extra,'new_slot_start',true)\n", "        end as new_slot_start,\n", "        case when oe.event_type_key='order_rescheduling'and is_valid_json(extra) = true \n", "                then json_extract_path_text(extra,'new_slot_end',true) \n", "        end new_slot_end,\n", "        case when oe.event_type_key = 'order_rescheduling' then oe.reason_key end as rescheduled_reason,\n", "        max(case when oe.event_type_key = 'order_cancellation' then oe.reason_key end) over(partition by oe.order_id) as cancelled_reason\n", "    from lake_oms_bifrost.oms_order o \n", "    inner join lake_oms_bifrost.oms_order_event oe on o.id = oe.order_id\n", "    left join (select distinct order_id,type from lake_oms_bifrost.oms_suborder) os on os.order_id = o.id\n", "    left join lake_oms_bifrost.view_oms_merchant m on o.merchant_id = m.id\n", "        and  m.virtual_merchant_type = 'superstore_merchant'\n", "    where \n", "    o.id in\n", "    (\n", "        select distinct(order_id)\n", "        from lake_oms_bifrost.oms_order_event as oe\n", "        where (oe.update_ts  + interval '5.5 hour')::date between '{delivery_date}'::date - interval '14 day' and '{delivery_date}'::date + interval '3 day'\n", "        or ( oe.install_ts  + interval '5.5 hour')::date between '{delivery_date}'::date - interval '14 day' and '{delivery_date}'::date + interval '3 day'\n", "    )\n", "    and \n", "    (o.type like 'InternalReverse%%'\n", "        or (os.order_id||os.type) in \n", "        (\n", "            select distinct order_id||type\n", "            from lake_oms_bifrost.oms_suborder \n", "            where type not like 'Digital%%'\n", "                and type not like 'Drop%%'\n", "        )\n", "    )\n", "    -- and (oe.event_type_key in ('order_rescheduling','order_cancellation') or json_extract_path_text(oe.extra ,'to',true) in ('CREATED','DELIVERED'))\n", "    and lower(m.city_name) not in ('not in service area')\n", "    and lower(m.city_name) not like '%%dum%%'\n", "    and lower(m.city_name) not like '%%test%%'\n", "    and lower(m.city_name) not like '%%b2b%%'\n", "    and m.id not in (28759,28760,26680) -- removing load testing IDs\n", "    and lower(o.station) not like '%%dum%%'\n", "    and lower(o.station) not like '%%test%%'\n", "    and lower(o.station) not like '%%b2b%%'\n", "),\n", "station_city_mapping as\n", "(\n", "    select * \n", "    from\n", "    (\n", "        select s.*,dense_rank()over(partition by station order by rows desc) as rnk\n", "        from\n", "        (\n", "            select \n", "                cast(m.city_id as int) as city_id,\n", "                m.city_name as city, \n", "                o.station,\n", "                count(*) as rows\n", "            from lake_oms_bifrost.oms_order o\n", "            left join lake_oms_bifrost.view_oms_merchant m \n", "                on o.merchant_id = m.id\n", "                and  m.virtual_merchant_type = 'superstore_merchant'\n", "            where \n", "             (m.update_ts  + interval '5.5 hour')::date between '{delivery_date}'::date - interval '30 day' and '{delivery_date}'::date\n", "            and lower(m.city_name) not in ('not in service area')\n", "            and lower(m.city_name) not like '%%b2b%%'\n", "            and lower(m.city_name) not like '%%test%%'\n", "            and m.id not in (28759,28760,26680) -- removing load testing IDs\n", "            and lower(o.station) not like '%%dum%%'\n", "            and lower(o.station) not like '%%test%%'\n", "            and lower(o.station) not like '%%b2b%%'\n", "            group by 1,2,3\n", "        )s\n", "    )s\n", "    where s.rnk = 1\n", "),\n", "scheduled as\n", "(\n", "    select\n", "        sc.city,\n", "        case when b.station in ('NSD') then b.station\n", "            when b.station is null then 'NULL'\n", "            else upper(substring(b.station,8,length(b.station)-7))\n", "        end as station,\n", "        b.frontend_merchant_id,\n", "        b.frontend_merchant_name,\n", "        b.cart_id,\n", "        b.order_id,\n", "        b.event,\n", "        to_timestamp(b.event_ts_ist, 'YYYY-MM-DD HH24:MI') as event_ts_ist,\n", "        case when event = 'CREATED' then null \n", "            else b.slot_freeze_time_ist\n", "        end as slot_freeze_time_ist,\n", "        b.sch_slot_start_ist,\n", "        b.sch_slot_end_ist,\n", "        case when event <> 'RESCHEDULED' then null \n", "            else b.new_slot_start_ist\n", "        end as new_slot_start_ist,\n", "        case when event <> 'RESCHEDULED' then null \n", "            else b.new_slot_end_ist\n", "        end as new_slot_end_ist,\n", "        b.reschedule_type,\n", "        b.reason,\n", "        case when b.event = 'RESCHEDULED' then b.before_blocked_time_resch\n", "            when b.event = 'CANCELLED' then b.before_blocked_time_canc\n", "            else null\n", "        end as before_blocked_time,\n", "        min(b.action_id) as oms_event_id,\n", "        sum(case when lmoi.quantity > 0 and lmoi.total_weight/lmoi.quantity > 50 then lmoi.total_weight/1000 \n", "                else lmoi.total_weight end) as order_weight\n", "    from\n", "    (\n", "        select distinct\n", "            station,\n", "            frontend_merchant_id,\n", "            frontend_merchant_name,\n", "            cart_id,\n", "            b.order_id,\n", "            action_id,\n", "            event,\n", "            to_timestamp(event_ts_ist, 'YYYY-MM-DD HH24:MI') as event_ts_ist,\n", "            slot_freeze_time_ist,\n", "            sch_slot_start_ist,\n", "            sch_slot_end_ist,\n", "            new_slot_start_ist,\n", "            new_slot_end_ist,\n", "            case when event = 'RESCHEDULED' then rescheduled_reason\n", "                 when event = 'CANCELLED' then cancelled_reason \n", "                 else null\n", "            end as reason,\n", "            case when event = 'RESCHEDULED' and slot_freeze_time_ist is null and event_ts_ist <= (b.sch_slot_start_ist - interval '14 hour') then 1\n", "                when event = 'RESCHEDULED' and slot_freeze_time_ist is null and event_ts_ist > (b.sch_slot_start_ist - interval '14 hour') then 0\n", "                when event = 'RESCHEDULED' and slot_freeze_time_ist is not null and event_ts_ist <= slot_freeze_time_ist then 1\n", "                when event = 'RESCHEDULED' and slot_freeze_time_ist is not null and event_ts_ist > slot_freeze_time_ist then 0\n", "                else null\n", "            end as before_blocked_time_resch,\n", "            case when event = 'CANCELLED' and slot_freeze_time_ist is null and event_ts_ist <= b.sch_slot_start_ist then 1\n", "                when event = 'CANCELLED' and slot_freeze_time_ist is null and event_ts_ist > b.sch_slot_start_ist then 0\n", "                when event = 'CANCELLED' and slot_freeze_time_ist is not null and event_ts_ist <= slot_freeze_time_ist then 1\n", "                when event = 'CANCELLED' and slot_freeze_time_ist is not null and event_ts_ist > slot_freeze_time_ist then 0\n", "                else null\n", "            end as before_blocked_time_canc,\n", "            case when event = 'RESCHEDULED' and b.sch_slot_start_ist::date > new_slot_start_ist::date then 'PRE-SCHEDULED'\n", "                when event = 'RESCHEDULED' and b.sch_slot_start_ist::date = new_slot_start_ist::date then 'SAME DAY'\n", "                when event = 'RESCHEDULED' and b.sch_slot_start_ist::date < new_slot_start_ist::date then 'LATER DATE'\n", "                else null\n", "            end as reschedule_type,\n", "            concat(old_slot_start,concat(old_slot_end,concat(new_slot_start,new_slot_end))) AS ooe_slot,\n", "            dense_rank() OVER (PARTITION BY b.order_id,event,event_ts_ist::date,ooe_slot ORDER BY to_timestamp(event_ts_ist, 'YYYY-MM-DD HH24:MI')) AS ooe_rank\n", "        from base b\n", "        where b.event in('CREATED','RESCHEDULED','CANCELLED','DELIVERED')\n", "    )b\n", "    inner join station_city_mapping sc on b.station = sc.station\n", "    left join lake_last_mile.order lmo on lmo.external_order_id = b.order_id\n", "    left join lake_last_mile.order_item lmoi on lmoi.order_id = lmo.id\n", "    left join lake_last_mile.station s on s.id = lmo.station_id\n", "    left join lake_last_mile.city c on c.id = s.city_id\n", "    where b.ooe_rank = 1\n", "    and sch_slot_start_ist::date between '{delivery_date}'::date and '{delivery_date}'::date + interval '3 day'\n", "    group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16\n", "    order by 1,2,3,4,5,6,8\n", "),\n", "agg as \n", "(\n", "    select\n", "        sch_slot_start_ist::date as sch_date,\n", "        count(distinct case when (event = 'RESCHEDULED' and reschedule_type = 'LATER DATE') or (event = 'CREATED') then oms_event_id end) as total_scheduled_orders,\n", "        count(distinct case when event = 'RESCHEDULED' and reschedule_type = 'LATER DATE' then oms_event_id end) as rescheduled_orders,\n", "        count(distinct case when event = 'CANCELLED'  then oms_event_id end) as cancelled_orders,\n", "        count(distinct case when event = 'DELIVERED' then oms_event_id end) as delivered_orders\n", "        from scheduled \n", "        group by 1\n", "        order by 1\n", "),\n", "scheduled_orders as\n", "(\n", "    select distinct\n", "    sch_slot_start_ist::date as sch_date,\n", "    s.city,\n", "    s.station,\n", "    order_id,\n", "    sch_slot_start_ist,\n", "    sch_slot_end_ist,\n", "    order_weight,\n", "    cart_id,\n", "    omc.customer_id,\n", "    oca.location_lat as customer_latitude,\n", "    oca.location_lon as customer_longitude\n", "    from scheduled s\n", "    inner join lake_oms_bifrost.oms_cart omc on omc.id = s.cart_id\n", "    left join lake_oms_bifrost.oms_cart_address oca on oca.id = omc.address_id\n", "    where (event = 'RESCHEDULED' and reschedule_type = 'LATER DATE') or (event = 'CREATED')\n", "    and sch_slot_start_ist::date = '{delivery_date}'\n", ")\n", "select * \n", "from scheduled_orders\n", "\"\"\".format(\n", "    delivery_date=curr_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = pd.read_sql(sql=query, con=red)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.dropna(how=\"any\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"customer_latitude\"] = data[\"customer_latitude\"] / 1000000\n", "data[\"customer_longitude\"] = data[\"customer_longitude\"] / 1000000"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def slot_format(x):\n", "    x = x.strip()\n", "    begin = int(x.split(\":\")[0])\n", "    end = x.split(\":\")[1]\n", "    if begin < 12:\n", "        if begin == 0:\n", "            begin = 12\n", "        return str(begin) + \":\" + end + \"AM\"\n", "    else:\n", "        if begin != 12:\n", "            begin -= 12\n", "        return str(begin) + \":\" + end + \"PM\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_order(df, curr_date):\n", "    final_dict = {\"orders\": []}\n", "    print(df.index)\n", "    for order in list(df.index):\n", "        temp_dict = {}\n", "\n", "        temp_dict[\"order_id\"] = str(df.loc[order, \"order_id\"])\n", "        temp_dict[\"lat\"] = str(df.loc[order, \"customer_latitude\"])\n", "        temp_dict[\"long\"] = str(df.loc[order, \"customer_longitude\"])\n", "        temp_dict[\"distribution_center\"] = str(df.loc[order, \"station\"])\n", "        temp_dict[\"slot\"] = str(df.loc[order, \"Slot\"])\n", "        temp_dict[\"weight\"] = str(df.loc[order, \"order_weight\"])\n", "        temp_dict[\"city\"] = str(df.loc[order, \"city\"])\n", "        temp_dict[\"curr_date\"] = curr_date\n", "\n", "        final_dict[\"orders\"].append(temp_dict)\n", "    return final_dict"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"slot_start\"] = (\n", "    data[\"sch_slot_start_ist\"]\n", "    .astype(\"str\")\n", "    .str.split(\" \")\n", "    .apply(lambda x: slot_format(x[1]))\n", ")\n", "data[\"slot_end\"] = (\n", "    data[\"sch_slot_end_ist\"]\n", "    .astype(\"str\")\n", "    .str.split(\" \")\n", "    .apply(lambda x: slot_format(x[1]))\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"Slot\"] = data.apply(lambda x: x[\"slot_start\"] + \"-\" + x[\"slot_end\"], axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[data[\"station\"] == \"KOL9\"].info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = data[data[\"station\"].isin(eligible_stations)].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"sch_date\"] = data[\"sch_date\"].astype(\"str\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = data[data[\"sch_date\"] == curr_date]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["headers = {\"Content-type\": \"application/json\", \"Accept\": \"text/plain\"}\n", "# url = \"http://dse-route-allocation-data.prod-k8s.grofer.io/order/allocate/gsp\"\n", "url = (\n", "    \"http://dse-route-allocation-service-data.prod-sgp-k8s.grofer.io/order/allocate/gsp\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query_curr_date = capacity[\"delivery_date\"].iloc[0].date()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["diff = 50\n", "super_start = time.time()\n", "for i in range(0, data.shape[0], diff):\n", "    num_retries = 0\n", "    order_dict = create_order(data.loc[i : i + diff - 1], curr_date)\n", "    print(\"On: \", i)\n", "    start = time.time()\n", "\n", "    timeout = 0\n", "\n", "    #     while num_retries<=MAX_RETRIES:\n", "    try:\n", "        r = requests.post(url, data=json.dumps(order_dict), headers=headers)\n", "        tempo = pd.read_sql(\n", "            sql=\"SELECT * from route_allocation.route.allocated_orders\", con=con\n", "        )\n", "        tempo1 = tempo[tempo[\"curr_date\"] == query_curr_date]\n", "        print(tempo1.shape)\n", "    except requests.exceptions.Timeout:\n", "        time.sleep(5)\n", "        print(\"TIMEOUT: Continuing\")\n", "        timeout = 1\n", "    #             time.sleep(10)\n", "    #             print('TIMEOUT: RETRYING')\n", "    #             if num_retries == MAX_RETRIES:\n", "    #                 raise\n", "    #             num_retries+=1\n", "    except requests.exceptions.ConnectionError:\n", "        time.sleep(5)\n", "        print(\"TIMEOUT: Continuing\")\n", "        timeout = 1\n", "    #             time.sleep(10)\n", "    #             print('Connection error: RETRYING')\n", "    #             if num_retries == MAX_RETRIES:\n", "    #                 raise\n", "    #             num_retries+=1\n", "\n", "    if timeout:\n", "        continue\n", "    end = time.time()\n", "    print(end - start)\n", "\n", "    if r.status_code != 200:\n", "        print(r.status_code)\n", "        print(r.text)\n", "        if r.status_code == 500:  # for cases when API is unresponsive\n", "            tries = 0\n", "            while (r.status_code != 200) & (tries < MAX_RETRIES):\n", "                time.sleep(5)\n", "                r = requests.post(url, data=json.dumps(order_dict), headers=headers)\n", "                print(\"retrying, now ERROR code: \", r.status_code)\n", "                tries += 1\n", "\n", "    response_dict = json.loads(r.text)\n", "\n", "    if i % 500 == 0:\n", "        for i in response_dict:\n", "            print(\"key: \", i, \"val: \", response_dict[i])\n", "end = time.time()\n", "print(end - super_start)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}