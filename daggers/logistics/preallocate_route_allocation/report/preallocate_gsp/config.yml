template_name: notebook
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-orbis-common:stable
path: logistics/preallocate_route_allocation/report/preallocate_gsp
dag_name: preallocate_gsp
version: 2
owner:
  email: <PERSON><PERSON><PERSON><PERSON>@grofers.com
  slack_id: U03RW2K4XBP
schedule:
  start_date: '2020-07-01T10:00:00'
  interval: 0 */2 * * *
notebook:
  parameters:
dag_type: report
escalation_priority: low
schedule_type: fixed
sla: 120 minutes
support_files: []
namespace: logistics
tags: []
project_name: preallocate_route_allocation
paused: true
executor:
  type: kubernetes
  config:
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-orbis-common:stable
    node_selectors:
      nodetype: spot
    tolerations:
    - key: service
      operator: Equal
      value: airflow
      effect: NoSchedule
    cpu:
      request: 0.5
      limit: 1
    memory:
      request: 500M
      limit: 4G
    volume_mounts:
    - name: airflow-dags
      subPath: airflow/plugins
      mountPath: /usr/local/airflow/plugins
