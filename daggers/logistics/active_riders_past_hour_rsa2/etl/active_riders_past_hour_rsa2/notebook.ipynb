{"cells": [{"cell_type": "code", "execution_count": null, "id": "d1b5245c-c689-4ccc-896c-91fd23d88055", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "id": "c7b183df-bc9f-40c8-8653-2589be5b4c25", "metadata": {}, "outputs": [], "source": ["conn = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "8139f829-8ddf-4224-98c0-5db5e1d669fb", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select a.fe_id driver_id,driver_name from\n", "                (\n", "                    select install_ts + interval '330' minute as install_ts,\n", "                    try_cast(json_extract_scalar(extra, '$.store_id') as int) as frontend_merchant_id, order_id,\n", "                    try_cast(json_extract_scalar(extra, '$.partner_emp_id') as varchar) as fe_id,\n", "                    row_number() over(partition by order_id order by install_ts desc) as rnk\n", "                    from  oms_bifrost.oms_order_event\n", "                    where insert_ds_ist = cast(current_date as varchar)\n", "                    and event_type_key = 'delivery_partner_assigned'\n", "                    and try_cast(json_extract_scalar(extra, '$.store_id') as int) in (31250,38212,36818,30614,34752,\n", "                                                                                     31304,38280,36730,31021,36222,\n", "                                                                                     30771,34744,37808,39370,34262,\n", "                                                                                     36475,35322,30720,37516,\n", "                                                                                     30792,31033,31025)\n", "                    and install_ts + interval '330' minute >= cast(current_time - interval '60' minute as timestamp(3))\n", "                )a\n", "                left join zomato.jumbo_derived.delivery_drivers b on\n", "                a.fe_id = b.fe_id\n", "                where rnk=1\n", "                order by 1 desc\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "8bb045a2-5db4-4843-be87-0f80320a2076", "metadata": {}, "outputs": [], "source": ["df = pd.read_sql_query(query, conn)"]}, {"cell_type": "code", "execution_count": null, "id": "057ce3a8-f4e2-4476-9b61-1cfa68746041", "metadata": {"jupyter": {"outputs_hidden": true}, "tags": []}, "outputs": [], "source": ["pb.to_sheets(df, \"163F54JJf7GwspLTetJqxHoEeL6h8n5XB8-y_v4AiOaY\", \"rider_data\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}