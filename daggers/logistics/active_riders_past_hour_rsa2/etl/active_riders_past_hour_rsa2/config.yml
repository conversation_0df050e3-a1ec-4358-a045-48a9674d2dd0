alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: active_riders_past_hour_rsa2
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: logistics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U08D6SCUHB6
path: logistics/active_riders_past_hour_rsa2/etl/active_riders_past_hour_rsa2
paused: false
pool: logistics_pool
project_name: active_riders_past_hour_rsa2
schedule:
  end_date: '2025-08-26T00:00:00'
  interval: 00,30 * * * *
  start_date: '2025-06-06T00:00:00'
schedule_type: fixed
sla: 119 minutes
support_files: []
tags: []
template_name: notebook
version: 1
