alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: rup_visibility_lm_cost
dag_type: report
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: logistics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03S077DE04
path: logistics/rpu_visibility/report/rup_visibility_lm_cost
paused: true
project_name: rpu_visibility
schedule:
  interval: 0 2 * * *
  start_date: '2022-07-29T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
