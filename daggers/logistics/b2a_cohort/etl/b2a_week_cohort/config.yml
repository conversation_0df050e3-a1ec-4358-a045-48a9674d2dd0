alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: b2a_week_cohort
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: logistics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U082DBVETNX
path: logistics/b2a_cohort/etl/b2a_week_cohort
paused: false
pool: logistics_pool
project_name: b2a_cohort
schedule:
  end_date: '2025-09-12T00:00:00'
  interval: 30 2 * * *
  start_date: '2023-10-12T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
