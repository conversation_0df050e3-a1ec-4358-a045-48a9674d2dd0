alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: rider_daily_performance_summary
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: logistics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U08D6SCUHB6
path: logistics/blinkit_rider/etl/rider_daily_performance_summary
paused: false
pool: logistics_pool
project_name: blinkit_rider
schedule:
  end_date: '2025-08-24T00:00:00'
  interval: 0 3 * * *
  start_date: '2025-03-20T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 4
