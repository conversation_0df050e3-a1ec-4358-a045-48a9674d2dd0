{"cells": [{"cell_type": "code", "execution_count": null, "id": "55ad67d9-a0a9-4a20-97d8-50c1bd7367a1", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import pencilbox as pb\n", "import numpy as np\n", "import itertools\n", "import datetime as dt\n", "from datetime import datetime\n", "from datetime import date\n", "from datetime import timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "480575cd-5f35-4834-ba1f-f7c8869f8725", "metadata": {}, "outputs": [], "source": ["sql_query = f\"\"\"\n", "\n", "with btpo_base as (\n", "select *,\n", "coalesce(date_diff('second', greatest_partner_assigned, partner_enroute),0) AS assigned_to_enroute,\n", "coalesce(date_diff('second', partner_enroute, partner_entry_drop_geo),0) AS enroute_to_reached_cx,\n", "coalesce(date_diff('second', partner_entry_drop_geo, order_delivered_ts),0) AS cx_hanshake,\n", "case when return_flag = 1 then coalesce(date_diff('second', order_delivered_ts, next_qr_scan),0) end as return_time\n", "from (\n", "    select\n", "    d.*, partner_assigned_ts_ist AS partner_assigned,\n", "    greatest(partner_assigned_ts_ist, order_billing_completed_ts_ist) AS greatest_partner_assigned,\n", "    fsd_enroute AS marked_enroute,\n", "    pickup_geofence_exit_50_ts_ist AS geofence_enroute,\n", "    reached_cx_marked_ts_ist AS marked_reached,\n", "    drop_geofence_entry_50_ts AS cx_reached_geofence,\n", "    fsd_delivered AS order_delivered_ts,\n", "    drop_geofence_exit_50_ts AS enroute_to_store,\n", "    next_qr_time AS next_qr_scan\n", "    from logistics_data_etls.logistics_order_details d\n", "    left join logistics_data_etls.blinkit_store_mapping m ON d.frontend_merchant_id = cast(m.blinkit_store_id as varchar)\n", "    where creation_date >= current_date - interval '21' day\n", "    and prev_qr_time is not null\n", "    and order_checkout_ts_ist is not null\n", "    and partner_assigned_ts_ist is not null\n", "    and fsd_enroute is not null\n", ")),\n", "\n", "base as (\n", "select\n", "date_trunc('week',d.order_checkout_dt_ist) as week_start_date,\n", "d.order_checkout_dt_ist as date,\n", "extract(hour from d.order_checkout_ts_ist) as hour,\n", "m.runnr_city,\n", "blinkit_store_name, \n", "d.frontend_merchant_id as blinkit_store_id,\n", "\n", "coalesce(sum(x.orders),0) as total_orders,\n", "coalesce(sum(case when d.order_billing_completed_ts_ist>=d.order_partner_assigned_ts_ist then x.orders end),0) as dh_orders,\n", "coalesce(sum(case when x.surge_pay > 0 then x.orders end),0) as rain_orders,\n", "coalesce(sum(assigned_to_enroute+enroute_to_reached_cx+cx_hanshake+return_time)*1.00/60,0.0) as btpo,\n", "coalesce(sum(drop_distance),0.0) as drop_distance,\n", "coalesce(sum(trip_pay),0.0) as trip_pay,\n", "coalesce(sum(x.surge_pay),0.0) as surge_pay,\n", "coalesce(sum(case when x.surge_pay > 0 then x.orders end)*1.00/sum(x.orders),0.0) as rain_perc,\n", "coalesce(count(distinct x.trip_id),0) as total_trips\n", "\n", "from dwh.fact_supply_chain_order_details d\n", "left join logistics_data_etls.blinkit_store_mapping m on d.frontend_merchant_id = m.blinkit_store_id\n", "inner join (\n", "    select distinct tab_id1,drop_distance, trip_pay, orders,surge_pay, trip_id from zomato.logs_dashboard_etls.trip_pay\n", "    where dt >= date_format(current_date - interval '21' day,'%%Y%%m%%d')\n", ")x on cast(d.order_id as varchar)=x.tab_id1\n", "left join (select * from logistics_data_etls.fact_rain_intensity_details where date_  >= current_date - interval '21' day ) z on  d.order_id=z.order_id\n", "left join btpo_base xoo on d.order_id = xoo.base_order_id\n", "where order_checkout_dt_ist >= current_date - interval '21' day\n", "and is_order_delivered\n", "and d.order_type='RetailForwardOrder'\n", "group by 1,2,3,4,5,6),\n", "\n", "conv as (\n", "select \n", "snapshot_date_ist as date, \n", "extract(dow from snapshot_date_ist) as dow, \n", "merchant_id, \n", "snapshot_hour_ist as hour, \n", "sum(daily_active_users) as daily_active_users,\n", "sum(overall_conversion) as overall_conversion,\n", "sum(overall_conversion)*1.00/sum(daily_active_users) conv\n", "from dwh.agg_hourly_consumer_conversion_details a\n", "inner join logistics_data_etls.blinkit_store_mapping b on a.merchant_id = cast(b.blinkit_store_id as varchar)\n", "where snapshot_date_ist  >= current_date - interval '21' day\n", "and channel = 'BLINKIT'\n", "and city not in ('Not in service area','Overall') \n", "and merchant_id <> 'Overall'\n", "group by 1,2,3,4),\n", "\n", "logins as (\n", "SELECT \n", "date(start_time) as date,\n", "extract(hour from start_time) as hour,\n", "x.blinkit_store_id,\n", "sum(delta)*1.00/3600 as login\n", "from zomato.jumbo_derived.driver_login_10_min l\n", "inner join(\n", "    select distinct account_date,delivery_driver_id,blinkit_store_id,blinkit_store_name,runnr_city,tag_name \n", "    from logistics_data_etls.blinkit_rider_daily_accounting_summary_v1\n", "    where dt >= date_format(current_date - interval '21' day,'%%Y%%m%%d')\n", ")x on date(l.start_time)=x.account_date and l.delivery_driver_id=x.delivery_driver_id\n", "where dt >= date_format(current_date - interval '21' day,'%%Y%%m%%d')\n", "group by 1,2,3),\n", "\n", "final_base as (\n", "select f.*,\n", "coalesce(daily_active_users,0) dau ,\n", "coalesce(overall_conversion,0) overall_conversion,\n", "coalesce(login,0.0) as login,\n", "\n", "case when rain_perc >= 0.1 then 'rain' else 'no rain' end as hour_flag\n", "from base f\n", "left join conv c on f.date = c.date and cast(f.blinkit_store_id as varchar)=c.merchant_id and f.hour = c.hour\n", "left join logins l on f.date = l.date and f.blinkit_store_id=l.blinkit_store_id and f.hour = l.hour),\n", "\n", "star as (\n", "select\n", "f.week_start_date,\n", "f.date,\n", "f.hour,\n", "f.runnr_city,\n", "f.blinkit_store_name,\n", "f.blinkit_store_id,\n", "f.hour_flag,\n", "'' as intensity,\n", "f.total_orders,\n", "f.dh_orders,\n", "f.rain_orders,\n", "f.btpo,\n", "f.drop_distance,\n", "f.trip_pay,\n", "f.surge_pay,\n", "f.rain_perc,\n", "f.dau,\n", "f.overall_conversion,\n", "f.login,\n", "\n", "case when  b2.rain_perc < 0.1 then b2.total_orders\n", "     when  b3.rain_perc < 0.1 then b3.total_orders\n", "else 0 end as lw_orders,\n", "\n", "case when  b2.rain_perc < 0.1 then b2.dh_orders\n", "     when  b3.rain_perc < 0.1 then b3.dh_orders\n", "else 0 end as lw_dh_orders,\n", "\n", "case when  b2.rain_perc < 0.1 then b2.login\n", "     when  b3.rain_perc < 0.1 then b3.login\n", "else 0 end as lw_logins,\n", "\n", "case when  b2.rain_perc < 0.1 then b2.trip_pay\n", "     when  b3.rain_perc < 0.1 then b3.trip_pay\n", "else 0 end as lw_trip_pay,\n", "\n", "case when  b2.rain_perc < 0.1 then b2.drop_distance\n", "     when  b3.rain_perc < 0.1 then b3.drop_distance\n", "else 0 end as lw_dd,\n", "\n", "case when  b2.rain_perc < 0.1 then b2.btpo\n", "     when  b3.rain_perc < 0.1 then b3.btpo\n", "else 0 end as lw_btpo,\n", "\n", "case when  b2.rain_perc < 0.1 then b2.dau\n", "     when  b3.rain_perc < 0.1 then b3.dau\n", "else 0 end as lw_dau,\n", "\n", "case when  b2.rain_perc < 0.1 then b2.overall_conversion\n", "     when  b3.rain_perc < 0.1 then b3.overall_conversion\n", "else 0 end as lw_overall_conversion,\n", "\n", "f.total_trips,\n", "\n", "case when  b2.rain_perc < 0.1 then b2.total_trips\n", "     when  b3.rain_perc < 0.1 then b3.total_trips\n", "else 0 end as lw_trips\n", "\n", "from final_base f \n", "left join  final_base b2 on f.date = b2.date + interval '7' day and f.hour = b2.hour and f.blinkit_store_id = b2.blinkit_store_id\n", "left join  final_base b3 on f.date = b3.date + interval '14' day and f.hour = b3.hour and f.blinkit_store_id = b3.blinkit_store_id\n", "where f.date >= current_date - interval '3' day)\n", "\n", "select * from star\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "b89b3764-75d9-4510-b72e-18fd5ec077a3", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"blinkit_lm_hourly_rain\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"hour\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"runnr_city\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"blinkit_store_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"blinkit_store_id\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"hour_flag\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"intensity\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"total_orders\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"dh_orders\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"rain_orders\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"btpo\",\n", "            \"type\": \"double\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"drop_distance\",\n", "            \"type\": \"double\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"trip_pay\",\n", "            \"type\": \"double\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"surge_pay\",\n", "            \"type\": \"double\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"rain_perc\",\n", "            \"type\": \"double\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"dau\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"overall_conversion\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"login\",\n", "            \"type\": \"double\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"lw_orders\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"lw_dh_orders\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"lw_logins\",\n", "            \"type\": \"double\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"lw_trip_pay\",\n", "            \"type\": \"double\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"lw_dd\",\n", "            \"type\": \"double\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"lw_btpo\",\n", "            \"type\": \"double\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"lw_dau\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"lw_overall_conversion\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"total_trips\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"account_date\",\n", "        },\n", "        {\n", "            \"name\": \"lw_trips\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"account_date\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"date\", \"hour\", \"blinkit_store_id\"],\n", "    \"partition_key\": [\"week_start_date\"],\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"This Table has rain data\",\n", "}\n", "pb.to_trino(sql_query, **kwargs_trino)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}