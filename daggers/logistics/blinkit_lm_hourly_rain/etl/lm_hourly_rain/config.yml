alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: lm_hourly_rain
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: logistics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U04NZ00LU49
path: logistics/blinkit_lm_hourly_rain/etl/lm_hourly_rain
paused: false
pool: logistics_pool
project_name: blinkit_lm_hourly_rain
schedule:
  end_date: '2025-09-05T00:00:00'
  interval: 30 2,3 * * *
  start_date: '2025-06-10T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 3
