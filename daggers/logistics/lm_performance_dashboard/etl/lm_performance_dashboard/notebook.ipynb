{"cells": [{"cell_type": "code", "execution_count": null, "id": "295dc72b-d0cb-437c-ba66-6002d964b6ea", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "id": "0a3d409f-8a95-4262-8059-411e78e9910f", "metadata": {}, "outputs": [], "source": ["conn = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "b7ddb549-5cef-466d-b97f-bff5cbf73086", "metadata": {}, "outputs": [], "source": ["btpo_breakup_query = \"\"\"\n", "    with \n", "    -- complaints as \n", "    -- (\n", "    -- select distinct order_id as complaint_order_id\n", "    -- from storeops_etls.complaint_details_reso\n", "    -- where order_date between current_date-interval '36' day and current_date - interval '1' day\n", "    -- ),\n", "\n", "    slot_buckets AS (\n", "        SELECT 'f night' AS slot, 0 as start_hour, 2 as end_hour\n", "        UNION ALL\n", "        SELECT 'late_night',3,5\n", "        UNION ALL\n", "        SELECT 'early_morning',6,8\n", "        UNION ALL\n", "        SELECT 'morning',9,11\n", "        UNION ALL\n", "        SELECT 'lunch',12,14\n", "        UNION ALL\n", "        SELECT 'evening',15,17\n", "        UNION ALL\n", "        SELECT 'dinner',18,20\n", "        UNION ALL\n", "        SELECT 'late_dinner',21,23\n", "    )\n", "    ,\n", "    surge_orders as (\n", "        select\n", "            date,\n", "            merchant_id,\n", "            slot as time_tag,\n", "            sum(surge_carts) - sum(rain_surge_carts) as surge_seen_carts,\n", "            -- sum(gsp_surge_carts) as rider_surge_seen_carts,\n", "            -- sum(picker_surge_carts) as picker_surge_seen_carts,\n", "            -- sum(gsp_and_picker_surge_carts) as both_surge_seen_carts,\n", "            sum(carts) - sum(rain_surge_carts) as total_carts\n", "        from serviceability_etls.surge_seen_hourly a\n", "        left join slot_buckets sb\n", "        on a.hour between sb.start_hour and sb.end_hour\n", "        where \n", "            date between cast(current_date - interval '35' day as varchar) and cast(current_date - interval '1' day as varchar)\n", "            and hour <> -1\n", "        group by 1,2,3\n", "    ),\n", "\n", "    -- order_confidence as (\n", "    --     select \n", "    --         distinct cast(lsm.label_value as int) as order_id, \n", "    --         install_ts as date_,\n", "    --         -- date(install_ts) as date_,\n", "    --         case \n", "    --             when try_cast(json_extract(json_extract(metadata,'$.corrected_location_info'), '$.confidence') as varchar) in ('HIGH', 'CX_LOCATION_CORRECT','CX_ADD_ADDRESS_VERIFICATION','EXPERIMENTAL_HIGH')\n", "    --             then 'High Confidence'\n", "    --             when try_cast(json_extract(json_extract(metadata,'$.corrected_location_info'), '$.confidence') as varchar) in ('CX_ORDER_DELIVERY_VERIFICATION','CX_POI_VERIFICATION','MEDIUM','VERY_LOW','EXPERIMENTAL_MEDIUM')\n", "    --             then 'Medium Confidence'\n", "    --             else 'Low confidence' \n", "    --         end as order_confidence_flag\n", "    --     from logistics.logistics_shipment_label_metadata lsm\n", "    --     where \n", "    --         insert_ds_ist >= try_cast((current_date - interval '36' day) - interval '1' day as varchar) and \n", "    --         label_type = 'ORDER_ID'\n", "    -- ),\n", "\n", "    Base as\n", "    (\n", "      Select \n", "         date(a.order_checkout_ts_ist) as date,\n", "         extract(dow from a.order_checkout_ts_ist) as dow,\n", "         extract(week from a.order_checkout_ts_ist) as wk,\n", "         slot as time_tag,\n", "         a.frontend_merchant_id,\n", "         city,\n", "    --  a.trip_id,\n", "         count(distinct a.order_id) as log_orders,\n", "         count(distinct case when a.order_current_status = 'DELIVERED' then a.order_id end) as delivered_orders,\n", "         count(distinct case when a.order_current_status = 'CANCELLED' then a.order_id end) as cancelled_orders,\n", "         count(distinct case when a.order_current_status = 'DELIVERED' and is_DH_order = true then a.order_id end) as dh_orders,\n", "         --count(distinct case when a.order_current_status = 'DELIVERED' and order_partner_assigned_ts_ist>a.order_billing_completed_ts_ist then a.order_id end) indirect_handover_orders,\n", "         --count(distinct case when date_diff('second',a.order_checkout_ts_ist,order_delivered_ts_ist)*1.00/60 <15.0 and a.order_current_status = 'DELIVERED' then a.order_id end) as delivered_under_15_min,\n", "         count(distinct case when slot_charges>0 and a.order_current_status = 'DELIVERED' then a.order_id end) as orders_delivered_with_surge,\n", "         --count(distinct c.complaint_order_id) as complaint_orders,\n", "\n", "         sum(case when a.order_current_status = 'DELIVERED' then date_diff('second',partner_assigned_ts_ist,next_qr_time) end) as btpo,\n", "         sum(case when a.order_current_status = 'DELIVERED' then date_diff('second',a.order_billing_completed_ts_ist,order_partner_assigned_ts_ist) end) as bill_to_rider_assign,\n", "         sum(case when a.order_current_status = 'DELIVERED' then date_diff('second',order_partner_assigned_ts_ist,a.order_scanned_ts_ist) end) as assign_to_scan,\n", "         sum(case when a.order_current_status = 'DELIVERED' then date_diff('second',order_enroute_ts_ist,order_reached_doorstep_ts_ist) end) as enroute_to_doorstep,\n", "         sum(case when a.order_current_status = 'DELIVERED' then date_diff('second',order_reached_doorstep_ts_ist,order_delivered_ts_ist) end) as doorstep_to_delivered,\n", "         sum(case when a.order_current_status = 'DELIVERED' then date_diff('second',order_enroute_ts_ist,order_delivered_ts_ist) end) as enroute_to_deliver,\n", "         sum(case when a.order_current_status = 'DELIVERED' then date_diff('second',a.order_billing_completed_ts_ist,a.order_scanned_ts_ist) end) as bill_to_scan,\n", "         sum(case when a.order_current_status = 'DELIVERED' then date_diff('second',order_partner_assigned_ts_ist,order_enroute_ts_ist) end) as partner_assign_to_enroute,\n", "         sum(case when a.order_current_status = 'DELIVERED' then date_diff('second',a.order_checkout_ts_ist,order_delivered_ts_ist) end) as checkout_to_deliver,\n", "        --sum(case when a.order_current_status = 'DELIVERED' then date_diff('second',greatest(order_partner_assigned_ts_ist,a.order_billing_completed_ts_ist),order_enroute_ts_ist) end) as store_hx,\n", "        --  sum(case when a.order_current_status = 'DELIVERED' and order_partner_assigned_ts_ist>a.order_billing_completed_ts_ist \n", "        --       then date_diff('second',order_partner_assigned_ts_ist,order_enroute_ts_ist) end) indirect_assign_to_enroute,\n", "        --  sum(case when a.order_current_status = 'DELIVERED' and order_partner_assigned_ts_ist<=a.order_billing_completed_ts_ist \n", "        --       then date_diff('second',order_partner_assigned_ts_ist,order_enroute_ts_ist) end) direct_assign_to_enroute,\n", "\n", "\n", "        --  SUM(CASE WHEN a.order_billing_completed_ts_ist > order_partner_assigned_ts_ist \n", "        --                     AND a.order_current_status = 'DELIVERED' \n", "        --               THEN date_diff('second', order_partner_assigned_ts_ist, a.order_billing_completed_ts_ist) \n", "        --           END) AS direct_assign_seconds,\n", "\n", "        --  SUM(CASE WHEN order_partner_assigned_ts_ist > a.order_billing_completed_ts_ist \n", "        --                     AND a.order_current_status = 'DELIVERED' \n", "        --               THEN date_diff('second', a.order_billing_completed_ts_ist, order_partner_assigned_ts_ist) \n", "        --           END) AS indirect_assign_seconds,\n", "\n", "        --  SUM(CASE WHEN a.order_billing_completed_ts_ist > order_partner_assigned_ts_ist \n", "        --               AND a.order_current_status = 'DELIVERED' \n", "        --          THEN 1 \n", "        --          ELSE 0 \n", "        --     END) AS direct_orders,\n", "\n", "        --  SUM(CASE WHEN order_partner_assigned_ts_ist > a.order_billing_completed_ts_ist \n", "        --               AND a.order_current_status = 'DELIVERED' \n", "        --          THEN 1 \n", "        --          ELSE 0 \n", "        --     END) AS indirect_orders,\n", "         count(case when surge_pay > 0 then t.trip_id end) as rain_orders,\n", "         count(b.base_order_id) as btpo_orders,\n", "         sum(case when a.order_current_status = 'DELIVERED' then date_diff('second', b.prev_qr_time, b.partner_assigned_ts_ist) end) as idle_time,\n", "         sum(case when a.order_partner_assigned_ts_ist < a.order_picking_completed_ts_ist AND a.order_current_status = 'DELIVERED' then \n", "                date_diff('second', a.order_partner_assigned_ts_ist, a.order_picking_completed_ts_ist) end) as wait_time_dh,\n", "         sum(case when a.order_current_status = 'DELIVERED' then date_diff('second', GREATEST(a.order_billing_completed_ts_ist, a.order_partner_assigned_ts_ist), a.order_enroute_ts_ist) end) as shs,\n", "         sum(case when a.order_current_status = 'DELIVERED' then date_diff('second', a.order_reached_doorstep_ts_ist, a.order_delivered_ts_ist) end) as chs,\n", "         sum(CASE WHEN a.order_partner_assigned_ts_ist > a.order_billing_completed_ts_ist AND a.order_current_status = 'DELIVERED' then\n", "                date_diff('second', GREATEST(a.order_billing_completed_ts_ist, a.order_partner_assigned_ts_ist), a.order_enroute_ts_ist) end) as shs_idh,\n", "         sum(case when a.order_current_status = 'DELIVERED' then date_diff('second', GREATEST(a.order_picking_completed_ts_ist, b.partner_assigned_ts_ist), b.pickup_geofence_exit_50_ts_ist) end) as shs_50m,\n", "         sum(case when a.order_current_status = 'DELIVERED' then t.drop_distance end) as drop_distance\n", "         --count(distinct case when oc.order_confidence_flag = 'High Confidence' then a.order_id end) as verified,\n", "         --count(distinct case when oc.order_confidence_flag not in ('High Confidence') then a.order_id end) as unverified\n", "     from dwh.fact_supply_chain_order_details a\n", "     inner join dwh.fact_sales_order_details b on a.order_id =b.order_id\n", "     --left join complaints c on a.order_id =c.complaint_order_id\n", "     --left join order_confidence oc on a.order_id=oc.order_id -- and date(a.order_checkout_ts_ist)=oc.date_\n", "     left join (select account_date as date_,\n", "                trip_id, drop_distance, surge_pay\n", "                from logistics_data_etls.delivery_trip_payout \n", "                where dt >= cast(current_date - interval '35' day as varchar)\n", "          ) t on a.trip_id = cast(t.trip_id as varchar)\n", "     left join logistics_data_etls.logistics_order_details b\n", "     on a.order_id = b.base_order_id\n", "     and creation_date between current_date  - interval '35' day and current_date - interval '1' day\n", "     and return_flag = 1 and next_qr_time>fsd_delivered\n", "     left join (select blinkit_store_id,max(blinkit_store_name) as blinkit_store_name,max(runnr_city) as city \n", "                from logistics_data_etls.blinkit_store_mapping\n", "                where is_active = 1 group by 1\n", "                )dm\n", "     on dm.blinkit_store_id = a.frontend_merchant_id\n", "     left join slot_buckets sb\n", "        on extract(hour from a.order_checkout_ts_ist) between sb.start_hour and sb.end_hour\n", "     where order_create_dt_ist between current_date  - interval '35' day and current_date - interval '1' day\n", "     and order_checkout_dt_ist between current_date  - interval '35' day and current_date - interval '1' day\n", "     group by 1,2,3,4,5,6\n", "     )\n", "\n", "    , orders as(\n", "            Select \n", "                a.date,\n", "                dow,\n", "                wk,\n", "                city,\n", "                a.time_tag,\n", "                coalesce(sum(log_orders),0) log_orders,\n", "                coalesce(sum(delivered_orders),0) delivered_orders,\n", "                coalesce(sum(cancelled_orders),0) cancelled_orders,\n", "                --coalesce(sum(0),0) lost_orders,\n", "                --coalesce(sum(indirect_handover_orders),0) indirect_handover_orders,\n", "                --coalesce(sum(delivered_under_15_min),0) delivered_under_15_min,\n", "                -- coalesce(sum(orders_delivered_with_surge),0) orders_delivered_with_surge,\n", "                coalesce(sum(surge_seen_carts),0) surge_seen_carts,\n", "                --coalesce(sum(complaint_orders),0) complaint_orders,\n", "                -- coalesce(sum(bill_to_rider_assign),0) bill_to_rider_assign,\n", "                coalesce(sum(shs), 0) as shs,\n", "                -- coalesce(sum(assign_to_scan),0) assign_to_scan,\n", "                coalesce(sum(enroute_to_doorstep),0) enroute_to_doorstep,\n", "                coalesce(sum(shs_idh), 0) as shs_idh,\n", "                -- coalesce(sum(doorstep_to_delivered),0) doorstep_to_delivered,\n", "                coalesce(sum(shs_50m), 0) as shs_50m,\n", "                -- coalesce(sum(enroute_to_deliver),0) enroute_to_deliver,\n", "                --coalesce(sum(verified), 0) as verified,\n", "                coalesce(sum(bill_to_scan),0) bill_to_scan,\n", "                -- coalesce(sum(partner_assign_to_enroute),0) partner_assign_to_enroute,\n", "                coalesce(sum(chs), 0) as chs,\n", "                -- coalesce(sum(checkout_to_deliver),0) checkout_to_deliver,\n", "                coalesce(sum(idle_time), 0) as idle_time,\n", "                -- coalesce(sum(store_hx),0) store_hx,\n", "                coalesce(sum(wait_time_dh), 0) as wait_time_dh,\n", "               -- coalesce(sum(indirect_assign_to_enroute),0) indirect_assign_to_enroute,\n", "               --coalesce(sum(direct_assign_to_enroute),0) direct_assign_to_enroute,\n", "                -- coalesce(sum(direct_assign_seconds),0) direct_assign_seconds,\n", "                -- coalesce(sum(indirect_assign_seconds),0) indirect_assign_seconds,\n", "                coalesce(sum(btpo),0) btpo,\n", "                coalesce(sum(drop_distance), 0) as drop_distance,\n", "                --coalesce(sum(direct_orders),0) direct_orders,\n", "                --coalesce(sum(indirect_orders),0) indirect_orders,\n", "                coalesce(sum(rain_orders),0) rain_orders,\n", "                -- coalesce(sum(btpo),0) btpo,\n", "                -- coalesce(sum(btpo_orders),0) as btpo_orders,\n", "                --coalesce(sum(unverified), 0) as unverified,\n", "                coalesce(sum(btpo_orders),0) as btpo_orders,\n", "                coalesce(sum(total_carts),0) as total_carts,\n", "                coalesce(sum(dh_orders),0) as dh_orders\n", "            from base a\n", "            left join surge_orders b\n", "            on a.frontend_merchant_id = b.merchant_id\n", "            and a.time_tag = b.time_tag\n", "            and a.date = date(b.date)\n", "            where a.date >= date_trunc('week', current_date) - interval '28' day\n", "            group by 1,2,3,4,5\n", "    )\n", "\n", "    select * from orders\n", "\n", "\n", "    -- , cohort as (\n", "    --     select\n", "    --               partner_id,\n", "    --               max_by(l_cohort,week_start_date) as l_cohort\n", "    --         from\n", "    --         (\n", "    --             select \n", "    --                 week_start_date,\n", "    --                 partner_id,\n", "    --                 cohorts,\n", "    --                 login_bucket,\n", "    --                 case when login_bucket = 'more_than_60' then 'UM'\n", "    --                      when login_bucket = '60_to_40' then 'Marathoner'\n", "    --                      when login_bucket in ('40_to_20','0_to_20') then 'Substitute'\n", "    --                      else 'Substitute' \n", "    --                 end as l_cohort\n", "    --             from logistics_data_etls.dp_login_cohort_current_week dp\n", "    --             where week_start_date >=  date_trunc('week', current_date) - interval '35' day\n", "    --         )\n", "    --         group by 1\n", "    -- )\n", "\n", "\n", "    -- select \n", "    --       o.*,\n", "    --       s.<PERSON>_cohort,\n", "    --       s.orders,\n", "    --       s.login_hours,\n", "    --       s.active_fleet\n", "    -- from orders o\n", "    -- left join logins_by_slot s\n", "    -- on o.date = s.account_date\n", "    -- and o.time_tag = s.time_tag\n", "    -- and o.blinkit_store_id = s.blinkit_store_id\n", "\n", "\"\"\"\n", "\n", "btpo_breakup_df = pd.read_sql_query(btpo_breakup_query, conn)"]}, {"cell_type": "code", "execution_count": null, "id": "e329ca87-9a92-457c-92e0-5772ea070124", "metadata": {}, "outputs": [], "source": ["btpo_breakup_df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "e54486c9-f7de-41eb-a26f-13d155e0cf46", "metadata": {}, "outputs": [], "source": ["btpo_breakup_df[\"drop_distance\"] = btpo_breakup_df[\"drop_distance\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "cac8cef8-0abe-419c-a223-80fb843d47f7", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"lm_performance_dashboard_btpo_breakup\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date\", \"type\": \"VARCHAR\", \"description\": \"Date of the data record\"},\n", "        {\"name\": \"dow\", \"type\": \"DOUBLE\", \"description\": \"Day of the week\"},\n", "        {\"name\": \"wk\", \"type\": \"DOUBLE\", \"description\": \"Week number\"},\n", "        {\"name\": \"city\", \"type\": \"VARCHAR\", \"description\": \"City name\"},\n", "        {\"name\": \"time_tag\", \"type\": \"VARCHAR\", \"description\": \"Time slot or time-based tag\"},\n", "        {\"name\": \"log_orders\", \"type\": \"DOUBLE\", \"description\": \"Number of logged orders\"},\n", "        {\n", "            \"name\": \"delivered_orders\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Number of successfully delivered orders\",\n", "        },\n", "        {\"name\": \"cancelled_orders\", \"type\": \"DOUBLE\", \"description\": \"Number of cancelled orders\"},\n", "        {\n", "            \"name\": \"surge_seen_carts\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Number of carts seen during surge\",\n", "        },\n", "        {\"name\": \"shs\", \"type\": \"DOUBLE\", \"description\": \"Store handling speed metric\"},\n", "        {\n", "            \"name\": \"enroute_to_doorstep\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Orders en route to doorstep\",\n", "        },\n", "        {\"name\": \"shs_idh\", \"type\": \"DOUBLE\", \"description\": \"Store handling speed - IDH metric\"},\n", "        {\n", "            \"name\": \"shs_50m\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Store handling speed within 50 meters\",\n", "        },\n", "        {\n", "            \"name\": \"bill_to_scan\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Time or count from billing to scanning\",\n", "        },\n", "        {\"name\": \"chs\", \"type\": \"DOUBLE\", \"description\": \"Customer handling speed metric\"},\n", "        {\"name\": \"idle_time\", \"type\": \"DOUBLE\", \"description\": \"Idle time in seconds or minutes\"},\n", "        {\n", "            \"name\": \"wait_time_dh\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Wait time for delivery handover\",\n", "        },\n", "        {\"name\": \"btpo\", \"type\": \"DOUBLE\", \"description\": \"Bill to pick order time\"},\n", "        {\"name\": \"drop_distance\", \"type\": \"DOUBLE\", \"description\": \"Drop distance for orders\"},\n", "        {\"name\": \"rain_orders\", \"type\": \"DOUBLE\", \"description\": \"Orders delivered during rain\"},\n", "        {\"name\": \"btpo_orders\", \"type\": \"DOUBLE\", \"description\": \"Orders with bill-to-pick metric\"},\n", "        {\"name\": \"total_carts\", \"type\": \"DOUBLE\", \"description\": \"Total carts seen\"},\n", "        {\n", "            \"name\": \"dh_orders\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Orders from dark stores (delivery hubs)\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"date\", \"city\", \"time_tag\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"lm_performance_dashboard_btpo_breakup\",\n", "}\n", "pb.to_trino(btpo_breakup_df, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "e35c553f-02dc-47ad-8189-cf28adb1c3bd", "metadata": {}, "outputs": [], "source": ["logins_query = \"\"\"\n", "    with slot_buckets AS (\n", "        SELECT 'night' AS slot, 0 as start_hour, 2 as end_hour\n", "        UNION ALL\n", "        SELECT 'late_night',3,5\n", "        UNION ALL\n", "        SELECT 'early_morning',6,8\n", "        UNION ALL\n", "        SELECT 'morning',9,11\n", "        UNION ALL\n", "        SELECT 'lunch',12,14\n", "        UNION ALL\n", "        SELECT 'evening',15,17\n", "        UNION ALL\n", "        SELECT 'dinner',18,20\n", "        UNION ALL\n", "        SELECT 'late_dinner',21,23\n", "    )\n", "\n", "    , cohort as (\n", "        select \n", "                week_start_date,\n", "                partner_id,\n", "                cohorts,\n", "                login_bucket,\n", "                case when login_bucket = 'more_than_60' then 'UM'\n", "                     when login_bucket = '60_to_40' then 'Marathoner'\n", "                     when login_bucket = '40_to_20' then 'Substitute'\n", "                     when login_bucket = '0_to_20' then 'Sprinter'\n", "                     else 'Substitute' \n", "                end as l_cohort\n", "            from logistics_data_etls.dp_login_cohort_current_week dp\n", "            where week_start_date >=  date_trunc('week', current_date) - interval '28' day\n", "    )\n", "\n", "    ,orders_hour_level as (\n", "        select \n", "              frontend_merchant_id as blinkit_store_id,\n", "              partner_id,\n", "              order_checkout_dt_ist,\n", "              Extract(hour from order_enroute_ts_ist) as hour,\n", "              count(distinct order_id) as orders\n", "        from dwh.fact_supply_chain_order_details\n", "        where order_checkout_dt_ist >= date_trunc('week', current_date) - interval '28' day\n", "        group by 1,2,3,4\n", "    )\n", "\n", "\n", "    , logins_by_slot as (\n", "        select \n", "              a.account_date,\n", "              a.week,\n", "              a.dow,\n", "              --a.blinkit_store_id,\n", "              sm.runnr_city,\n", "              sb.slot as time_tag,\n", "              coalesce(c.l_cohort, 'Substitute') as l_cohort,\n", "              sum(wl.logins_min_calculated) as login_minutes,\n", "              count(distinct wl.delivery_driver_id) as active_fleet,\n", "              coalesce(sum(so.orders),0) as orders\n", "        from logistics_data_etls.hourly_dp_wasted_login_v1 wl\n", "        inner join logistics_data_etls.blinkit_rider_daily_accounting_summary_v1 a\n", "        on wl.delivery_driver_id = a.delivery_driver_id\n", "        and wl.account_date = a.account_date\n", "        left join cohort c\n", "        on c.partner_id = concat('FE', cast(a.delivery_driver_id +10000 as varchar))\n", "        and c.week_start_date = date_trunc('week',a.account_date)\n", "        left join slot_buckets sb\n", "        on extract(hour from wl.hr) between sb.start_hour and sb.end_hour\n", "        left join logistics_data_etls.blinkit_store_mapping sm\n", "        on a.blinkit_store_id = sm.blinkit_store_id\n", "        left join orders_hour_level so\n", "        on so.partner_id = concat('FE', cast(wl.delivery_driver_id+10000 as varchar))\n", "        and so.hour = extract(hour from wl.hr)\n", "        and order_checkout_dt_ist = wl.account_date\n", "        where acc_orders > 0\n", "        and a.account_date >= date_trunc('week', current_date) - interval '28' day\n", "        group by 1,2,3,4,5,6\n", "    )\n", "\n", "    select * from logins_by_slot\n", "\n", "    --140565\n", "\"\"\"\n", "\n", "logins_df = pd.read_sql_query(logins_query, conn)"]}, {"cell_type": "code", "execution_count": null, "id": "0047ac00-d2dd-49a4-a5d4-1eb9edb753b1", "metadata": {}, "outputs": [], "source": ["logins_df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "a8ea28f6-2a3a-4c35-b4df-b3f7970cedc0", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"lm_performance_dashboard_logins\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"account_date\", \"type\": \"VARCHAR\", \"description\": \"Accounting date\"},\n", "        {\"name\": \"week\", \"type\": \"DOUBLE\", \"description\": \"Week number of the year\"},\n", "        {\"name\": \"dow\", \"type\": \"DOUBLE\", \"description\": \"Day of week (0=Sunday, 6=Saturday)\"},\n", "        {\"name\": \"runnr_city\", \"type\": \"VARCHAR\", \"description\": \"City associated with the runner\"},\n", "        {\"name\": \"time_tag\", \"type\": \"VARCHAR\", \"description\": \"Time tag or slot\"},\n", "        {\"name\": \"l_cohort\", \"type\": \"VARCHAR\", \"description\": \"Login cohort of the driver\"},\n", "        {\n", "            \"name\": \"login_minutes\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Total login minutes for the day\",\n", "        },\n", "        {\n", "            \"name\": \"active_fleet\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Number of active delivery personnel\",\n", "        },\n", "        {\"name\": \"orders\", \"type\": \"DOUBLE\", \"description\": \"Total number of orders completed\"},\n", "    ],\n", "    \"primary_key\": [\"account_date\", \"city\", \"time_tag\", \"l_cohort\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"lm_performance_dashboard_logins\",\n", "}\n", "pb.to_trino(logins_df, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "1d981d29-171b-4c36-99ab-0d5e64c30f9f", "metadata": {}, "outputs": [], "source": ["churn_query = \"\"\"\n", "\n", "    with churn_base AS (\n", "        SELECT\n", "            ac.delivery_driver_id,\n", "            ac.account_date,\n", "            week,\n", "            dow,\n", "            -- ac.blinkit_store_id,\n", "            -- ac.blinkit_store_name,\n", "            sm.runnr_city,\n", "            date_trunc('week',ac.account_date) as week_start_date\n", "        FROM logistics_data_etls.blinkit_rider_daily_accounting_summary_v1 as ac\n", "        left join logistics_data_etls.blinkit_store_mapping sm\n", "        on ac.blinkit_store_id = sm.blinkit_store_id\n", "        WHERE acc_orders > 0\n", "          AND account_date >= date_trunc('week', current_date) - interval '42' day\n", "        group by 1,2,3,4,5\n", "    )\n", "    , churn_base_week AS (\n", "        select *\n", "        from\n", "        (\n", "            SELECT\n", "                delivery_driver_id,\n", "                -- blinkit_store_id,\n", "                -- blinkit_store_name,\n", "                runnr_city,\n", "                week,\n", "                week_start_date\n", "            FROM churn_base\n", "            group by 1,2,3,4\n", "        )a\n", "        cross join \n", "        (\n", "            select 1 as dow\n", "            UNION ALL \n", "            select 2\n", "            UNION ALL \n", "            select 3\n", "            UNION ALL \n", "            select 4\n", "            UNION ALL \n", "            select 5\n", "            UNION ALL \n", "            select 6\n", "            UNION ALL \n", "            select 7\n", "        ) b\n", "    )\n", "\n", "    ,\n", "    churn as(\n", "        select \n", "            x.week_start_date,\n", "            x.week,\n", "            runnr_city,\n", "            dow,\n", "            w0_riders as active_fleet,\n", "            w1_riders + w2_riders + w3_riders as wow_churn,\n", "            w2_riders + w3_riders as wo2w_churn,\n", "            w3_riders as wo3w_churn,\n", "            w1_riders_res + w2_riders_res + w3_riders_res as wow_res,\n", "            w2_riders_res + w3_riders_res as wo2w_res,\n", "            w3_riders_res as wo3w_res\n", "\n", "        from (\n", "            select\n", "                b.week,\n", "                b.week_start_date,\n", "                b.runnr_city,\n", "                b.dow,\n", "                count(distinct b.delivery_driver_id) as w0_riders,\n", "                count(distinct case when b.week is not null and b2.week is null and b3.week is null and b4.week is null then b.delivery_driver_id end) as w3_riders,\n", "                count(distinct case when b.week is not null and b2.week is null and b3.week is null and b4.week is not null then b.delivery_driver_id end) as w2_riders,\n", "                count(distinct case when b.week is not null and b2.week is null and b3.week is not null  then b.delivery_driver_id end) as w1_riders,\n", "\n", "                count(distinct case when b.week is not null and b5.week is null and b6.week is null and b7.week is null then b.delivery_driver_id end) as w3_riders_res,\n", "                count(distinct case when b.week is not null and b5.week is null and b6.week is null and b7.week is not null then b.delivery_driver_id end) as w2_riders_res,\n", "                count(distinct case when b.week is not null and b5.week is null and b6.week is not null  then b.delivery_driver_id end) as w1_riders_res\n", "\n", "            from churn_base_week b\n", "            left join churn_base b2 \n", "            on b.delivery_driver_id = b2.delivery_driver_id  and b.runnr_city = b2.runnr_city and b.week_start_date = b2.week_start_date - interval '7' day and b.dow>= b2.dow\n", "            left join churn_base b3\n", "            on b.delivery_driver_id = b3.delivery_driver_id  and b.runnr_city = b3.runnr_city and b.week_start_date = b3.week_start_date - interval '14' day and b.dow>= b3.dow\n", "            left join churn_base b4\n", "            on b.delivery_driver_id = b4.delivery_driver_id  and b.runnr_city = b4.runnr_city and b.week_start_date = b4.week_start_date - interval '21' day and b.dow>= b4.dow\n", "\n", "            left join churn_base b5\n", "            on b.delivery_driver_id = b5.delivery_driver_id  and b.runnr_city = b5.runnr_city and b.week_start_date = b5.week_start_date + interval '7' day and b.dow<= b5.dow\n", "            left join churn_base b6\n", "            on b.delivery_driver_id = b6.delivery_driver_id  and b.runnr_city = b6.runnr_city and b.week_start_date = b6.week_start_date + interval '14' day and b.dow<= b6.dow\n", "            left join churn_base b7\n", "            on b.delivery_driver_id = b7.delivery_driver_id  and b.runnr_city = b7.runnr_city and b.week_start_date = b7.week_start_date + interval '21' day and b.dow<= b7.dow\n", "\n", "            group by 1,2,3,4\n", "        ) x\n", "        --where week_start_date < date_trunc('week',current_date)-interval'7'day\n", "    )    \n", "\n", "    , fod as (\n", "    select \n", "         fod_date,\n", "         sm.runnr_city,\n", "         count(distinct driver_id) as fod\n", "    from logistics_data_etls.blinkit_driver_ob_v1 ob\n", "    left join logistics_data_etls.blinkit_store_mapping sm \n", "    on ob.blinkit_store_id = sm.blinkit_store_id\n", "    where year(fod_date) >= 2025\n", "    group by 1,2\n", "    )\n", "\n", "    select  \n", "          churn.*,\n", "          fod\n", "    from churn\n", "    left join fod \n", "    on churn.week = week(fod.fod_date) \n", "    and churn.dow = day_of_week(fod.fod_date) \n", "    and churn.runnr_city = fod.runnr_city\n", "\"\"\"\n", "\n", "churn_df = pd.read_sql_query(churn_query, conn)"]}, {"cell_type": "code", "execution_count": null, "id": "19befa23-b10d-4ca4-8de5-8d3097159665", "metadata": {}, "outputs": [], "source": ["churn_df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "603654e3-72c6-4ca2-811e-f8f501767b30", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"lm_performance_dashboard_churn\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"week_start_date\", \"type\": \"varchar\", \"description\": \"Start date of the week\"},\n", "        {\"name\": \"week\", \"type\": \"DOUBLE\", \"description\": \"Week number\"},\n", "        {\"name\": \"runnr_city\", \"type\": \"VARCHAR\", \"description\": \"City associated with the runner\"},\n", "        {\"name\": \"dow\", \"type\": \"DOUBLE\", \"description\": \"Day of week (0=Sunday, 6=Saturday)\"},\n", "        {\"name\": \"active_fleet\", \"type\": \"DOUBLE\", \"description\": \"Number of active fleet drivers\"},\n", "        {\"name\": \"wow_churn\", \"type\": \"DOUBLE\", \"description\": \"Week-over-week churn\"},\n", "        {\"name\": \"wo2w_churn\", \"type\": \"DOUBLE\", \"description\": \"2-week-over-2-week churn\"},\n", "        {\"name\": \"wo3w_churn\", \"type\": \"DOUBLE\", \"description\": \"3-week-over-3-week churn\"},\n", "        {\"name\": \"wow_res\", \"type\": \"DOUBLE\", \"description\": \"Week-over-week resurrection\"},\n", "        {\"name\": \"wo2w_res\", \"type\": \"DOUBLE\", \"description\": \"2-week-over-2-week resurrection\"},\n", "        {\"name\": \"wo3w_res\", \"type\": \"DOUBLE\", \"description\": \"3-week-over-3-week resurrection\"},\n", "        {\"name\": \"fod\", \"type\": \"DOUBLE\", \"description\": \"First order date churn metric\"},\n", "    ],\n", "    \"primary_key\": [\"week_start_date\", \"runnr_city\", \"dow\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"lm_performance_dashboard_churn\",\n", "}\n", "pb.to_trino(churn_df, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "9e26381d-c6aa-4d1b-a214-21809a6382a3", "metadata": {}, "outputs": [], "source": ["offer_amount_query = \"\"\"\n", "\n", "    with slot_buckets AS (\n", "        SELECT 'night' AS slot, 0 as start_hour, 2 as end_hour\n", "        UNION ALL\n", "        SELECT 'late_night',3,5\n", "        UNION ALL\n", "        SELECT 'early_morning',6,8\n", "        UNION ALL\n", "        SELECT 'morning',9,11\n", "        UNION ALL\n", "        SELECT 'lunch',12,14\n", "        UNION ALL\n", "        SELECT 'evening',15,17\n", "        UNION ALL\n", "        SELECT 'dinner',18,20\n", "        UNION ALL\n", "        SELECT 'late_dinner',21,23\n", "    )\n", "\n", "    , cohort as (\n", "        select \n", "                week_start_date,\n", "                partner_id,\n", "                cohorts,\n", "                login_bucket,\n", "                case when login_bucket = 'more_than_60' then 'UM'\n", "                     when login_bucket = '60_to_40' then 'Marathoner'\n", "                     when login_bucket = '40_to_20' then 'Substitute'\n", "                     when login_bucket = '0_to_20' then 'Sprinter'\n", "                     else 'Substitute' \n", "                end as l_cohort\n", "            from logistics_data_etls.dp_login_cohort_current_week dp\n", "            where week_start_date >=  date_trunc('week', current_date) - interval '28' day\n", "    )\n", "\n", "    , offer_amount as(\n", "        select \n", "                  a.account_date,\n", "                --  a.blinkit_store_id,\n", "                  sm.runnr_city,\n", "                  coalesce(c.l_cohort, 'Substitute') l_cohort,\n", "                  sum(a.offer_amount) as offer_amount,\n", "                  sum(a.acc_orders) as orders,\n", "                  sum(case when a.offer_amount> 0 then a.acc_orders else 0 end) as orders_with_achievement,\n", "                  sum(base_pay_amount+distance_amount+weight_pay+trip_min_guarantee+pickup_drop_incentive_amount+a.offer_amount - 5 * delivered_orders_distance) AS net_earnings,\n", "                  sum(delivered_orders_distance) AS total_dd,\n", "                  sum(base_pay_amount+distance_amount+weight_pay+trip_min_guarantee+pickup_drop_incentive_amount+a.offer_amount) as earnings\n", "            from logistics_data_etls.blinkit_rider_offers_split_v2 a\n", "            inner join logistics_data_etls.blinkit_rider_daily_accounting_summary_v1 v1\n", "            on a.delivery_driver_id = v1.delivery_driver_id\n", "            and a.account_date = v1.account_date\n", "            left join cohort c\n", "            on c.partner_id = concat('FE', cast(a.delivery_driver_id +10000 as varchar))\n", "            and c.week_start_date = date_trunc('week',a.account_date)\n", "            left join logistics_data_etls.blinkit_store_mapping sm\n", "            on a.blinkit_store_id = sm.blinkit_store_id\n", "            where a.acc_orders > 0\n", "            and a.account_date >= date_trunc('week', current_date) - interval '28' day\n", "            group by 1,2,3\n", "\n", "    )\n", "\n", "    select * from offer_amount\n", "\"\"\"\n", "\n", "offer_amount_df = pd.read_sql_query(offer_amount_query, conn)"]}, {"cell_type": "code", "execution_count": null, "id": "c1ddfda9-fc77-4171-a1ab-77a3ca0fa425", "metadata": {}, "outputs": [], "source": ["offer_amount_df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "833de7d4-3b5f-451b-9eaa-01913be3c846", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"lm_performance_dashboard_offer_amount\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"account_date\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Accounting date for the record\",\n", "        },\n", "        {\"name\": \"runnr_city\", \"type\": \"VARCHAR\", \"description\": \"City associated with the runner\"},\n", "        {\"name\": \"l_cohort\", \"type\": \"VARCHAR\", \"description\": \"Login cohort of the driver\"},\n", "        {\"name\": \"offer_amount\", \"type\": \"DOUBLE\", \"description\": \"Amount offered as incentive\"},\n", "        {\"name\": \"orders\", \"type\": \"DOUBLE\", \"description\": \"Total number of orders\"},\n", "        {\n", "            \"name\": \"orders_with_achievement\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Orders where achievement criteria was met\",\n", "        },\n", "        {\"name\": \"net_earnings\", \"type\": \"DOUBLE\", \"description\": \"Net earnings of the driver\"},\n", "        {\"name\": \"total_dd\", \"type\": \"DOUBLE\", \"description\": \"Total distance delivered\"},\n", "        {\n", "            \"name\": \"earnings\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Total earnings including incentives\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"account_date\", \"runnr_city\", \"l_cohort\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"lm_performance_dashboard_offer_amount\",\n", "}\n", "pb.to_trino(offer_amount_df, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "7131a265-7b0b-47d3-bd42-58bbffc07cfa", "metadata": {}, "outputs": [], "source": ["fleet_query = \"\"\"\n", "\n", "    --EXPLAIN (TYPE DISTRIBUTED)\n", "\n", "    with slot_buckets AS (\n", "        SELECT 'night' AS slot, 0 as start_hour, 2 as end_hour\n", "        UNION ALL\n", "        SELECT 'late_night',3,5\n", "        UNION ALL\n", "        SELECT 'early_morning',6,8\n", "        UNION ALL\n", "        SELECT 'morning',9,11\n", "        UNION ALL\n", "        SELECT 'lunch',12,14\n", "        UNION ALL\n", "        SELECT 'evening',15,17\n", "        UNION ALL\n", "        SELECT 'dinner',18,20\n", "        UNION ALL\n", "        SELECT 'late_dinner',21,23\n", "    )\n", "\n", "    , cohort as (\n", "        select \n", "                week_start_date,\n", "                partner_id,\n", "                cohorts,\n", "                login_bucket,\n", "                case when login_bucket = 'more_than_60' then 'UM'\n", "                     when login_bucket = '60_to_40' then 'Marathoner'\n", "                     when login_bucket = '40_to_20' then 'Substitute'\n", "                     when login_bucket = '0_to_20' then 'Sprinter'\n", "                     else 'Substitute' \n", "                end as l_cohort\n", "            from logistics_data_etls.dp_login_cohort_current_week dp\n", "            where week_start_date >=  date_trunc('week', current_date) - interval '28' day\n", "    )\n", "\n", "    , logins as (\n", "            select \n", "                  a.account_date,\n", "                  a.delivery_driver_id,\n", "                  a.week,\n", "                  a.dow,\n", "                  --a.blinkit_store_id,\n", "                  sm.runnr_city,\n", "                  sb.slot as time_tag,\n", "                  coalesce(c.l_cohort, 'Substitute') l_cohort\n", "            from logistics_data_etls.hourly_dp_wasted_login_v1 wl\n", "            inner join logistics_data_etls.blinkit_rider_daily_accounting_summary_v1 a\n", "            on wl.delivery_driver_id = a.delivery_driver_id\n", "            and wl.account_date = a.account_date\n", "            left join cohort c\n", "            on c.partner_id = concat('FE', cast(a.delivery_driver_id +10000 as varchar))\n", "            and c.week_start_date = date_trunc('week',a.account_date)\n", "            left join slot_buckets sb\n", "            on extract(hour from wl.hr) between sb.start_hour and sb.end_hour\n", "            left join logistics_data_etls.blinkit_store_mapping sm\n", "            on a.blinkit_store_id = sm.blinkit_store_id\n", "            where acc_orders > 0\n", "            and a.account_date >= date_trunc('week', current_date) - interval '28' day\n", "            group by 1,2,3,4,5,6,7\n", "    )\n", "\n", "    ,\n", "    logins_updated as (\n", "        select *\n", "        from\n", "        (\n", "            select\n", "                  account_date,\n", "                  delivery_driver_id,\n", "                  week,\n", "                  dow,\n", "                  --a.blinkit_store_id,\n", "                  runnr_city,\n", "                  time_tag,\n", "                  l_cohort\n", "            from logins\n", "            UNION All\n", "            select \n", "                  null,\n", "                  delivery_driver_id,\n", "                  week,\n", "                  0,\n", "                  --a.blinkit_store_id,\n", "                  runnr_city,\n", "                  time_tag,\n", "                  l_cohort\n", "            from logins\n", "        )\n", "    )\n", "\n", "    , active_fleet as(\n", "        select \n", "              l1.week,\n", "              l1.runnr_city,\n", "              l1.l_cohort,\n", "              l1.dow,\n", "              l1.account_date,\n", "              --blinkit_store_id,\n", "              l1.time_tag,\n", "              count(distinct l1.delivery_driver_id) as active_fleet\n", "        from logins_updated l1\n", "        group by 1,2,3,4,5,6\n", "    )\n", "\n", "    , active_fleet_inc_wtd as(\n", "        select \n", "              l1.week,\n", "              l1.runnr_city,\n", "              l1.l_cohort,\n", "              l1.dow,\n", "              l1.account_date,\n", "              --blinkit_store_id,\n", "              l1.time_tag,\n", "              l1.active_fleet,\n", "              count(distinct l2.delivery_driver_id) as active_fleet_wtd\n", "        from active_fleet l1\n", "        left join logins_updated l2\n", "        on l1.runnr_city= l2.runnr_city\n", "        and l1.dow >= l2.dow\n", "        and l2.dow <> 0 \n", "        and l1.week = l2.week\n", "        and l1.l_cohort = l2.l_cohort\n", "        and l1.time_tag = l2.time_tag\n", "        group by 1,2,3,4,5,6,7\n", "    )\n", "\n", "    , active_fleet_wo_slot as(\n", "        select \n", "              l1.week,\n", "              l1.runnr_city,\n", "              l1.l_cohort,\n", "              l1.dow,\n", "              l1.account_date,\n", "              --blinkit_store_id,\n", "              count(distinct l1.delivery_driver_id) as active_fleet_wo_slot\n", "        from logins_updated l1\n", "        group by 1,2,3,4,5\n", "    )\n", "\n", "    , active_fleet_wo_slot_inc_wtd as(\n", "        select \n", "              l1.week,\n", "              l1.runnr_city,\n", "              l1.l_cohort,\n", "              l1.dow,\n", "              l1.account_date,\n", "              --blinkit_store_id,\n", "              active_fleet_wo_slot,\n", "              count(distinct l2.delivery_driver_id) as active_fleet_wtd_wo_slot\n", "        from active_fleet_wo_slot l1\n", "        left join logins_updated l2\n", "        on l1.runnr_city= l2.runnr_city\n", "        and l1.dow >= l2.dow\n", "        and l2.dow <> 0 \n", "        and l1.week = l2.week\n", "        and l1.l_cohort = l2.l_cohort\n", "        group by 1,2,3,4,5,6\n", "    )\n", "\n", "    select *\n", "    from\n", "    (\n", "        select \n", "              week,\n", "              dow,\n", "              runnr_city,\n", "              time_tag,\n", "              l_cohort,\n", "              active_fleet,\n", "              active_fleet_wtd\n", "        from active_fleet_inc_wtd\n", "        UNION All\n", "        (select \n", "              week,\n", "              dow,\n", "              runnr_city,\n", "              'All',\n", "              l_cohort,\n", "              active_fleet_wo_slot,\n", "              active_fleet_wtd_wo_slot\n", "        from active_fleet_wo_slot_inc_wtd\n", "        )\n", "    )\n", "\n", "    --140565\n", "\"\"\"\n", "\n", "fleet_df = pd.read_sql_query(fleet_query, conn)"]}, {"cell_type": "code", "execution_count": null, "id": "f4f3d10e-86d9-4c07-94ee-f04c4d41d510", "metadata": {}, "outputs": [], "source": ["fleet_df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "d1830771-3013-4cb1-bf25-fea1b09078da", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"lm_performance_dashboard_fleet\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"week\", \"type\": \"double\", \"description\": \"week\"},\n", "        {\"name\": \"dow\", \"type\": \"double\", \"description\": \"day of week\"},\n", "        {\"name\": \"runnr_city\", \"type\": \"VARCHAR\", \"description\": \"City associated with the runner\"},\n", "        {\"name\": \"time_tag\", \"type\": \"VARCHAR\", \"description\": \"time slot\"},\n", "        {\"name\": \"l_cohort\", \"type\": \"VARCHAR\", \"description\": \"Login cohort of the driver\"},\n", "        {\"name\": \"active_fleet\", \"type\": \"double\", \"description\": \"active fleet\"},\n", "        {\n", "            \"name\": \"active_fleet_wtd\",\n", "            \"type\": \"double\",\n", "            \"description\": \"active fleet week till date\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"week\", \"dow\", \"runnr_city\", \"time_tag\", \"l_cohort\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"lm_performance_dashboard_fleet\",\n", "}\n", "pb.to_trino(fleet_df, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "67c4cdf7-d2a9-4179-b890-8738202ef1b4", "metadata": {}, "outputs": [], "source": ["attendance_query = \"\"\"\n", "    with cohort as (\n", "    select \n", "            week_start_date,\n", "            partner_id,\n", "            cohorts,\n", "            login_bucket,\n", "            case when login_bucket = 'more_than_60' then 'UM'\n", "                 when login_bucket = '60_to_40' then 'Marathoner'\n", "                 when login_bucket = '40_to_20' then 'Substitute'\n", "                 when login_bucket = '0_to_20' then 'Sprinter'\n", "                 else 'Substitute' \n", "            end as l_cohort\n", "        from logistics_data_etls.dp_login_cohort_current_week dp\n", "        where week_start_date >=  date_trunc('week', current_date) - interval '28' day\n", "    )\n", "    , attendance_by_rider as (\n", "        select \n", "              a.delivery_driver_id,\n", "              a.week,\n", "              sm.runnr_city,\n", "              coalesce(c.l_cohort, 'Substitute') as l_cohort,\n", "              sum(wl.logins_min_calculated) as login_minutes,\n", "              count(distinct a.account_date) as rider_attendance_days      \n", "        from logistics_data_etls.hourly_dp_wasted_login_v1 wl\n", "        inner join logistics_data_etls.blinkit_rider_daily_accounting_summary_v1 a\n", "        on wl.delivery_driver_id = a.delivery_driver_id\n", "        and wl.account_date = a.account_date\n", "        left join cohort c\n", "        on c.partner_id = concat('FE', cast(a.delivery_driver_id +10000 as varchar))\n", "        and c.week_start_date = date_trunc('week',a.account_date)\n", "        left join logistics_data_etls.blinkit_store_mapping sm\n", "        on a.blinkit_store_id = sm.blinkit_store_id\n", "        where acc_orders > 0\n", "        and a.account_date >= date_trunc('week', current_date) - interval '28' day\n", "        group by 1,2,3,4\n", "    )\n", "    ,\n", "    attendance as (\n", "        select *\n", "        from\n", "        (\n", "        select \n", "              week,\n", "              runnr_city,\n", "              l_cohort,\n", "              sum(login_minutes) as login_minutes,\n", "              --sum(login_minutes*rider_attendance_days)/Nullif(sum(login_minutes),0) as rider_attendance_days\n", "              avg(rider_attendance_days) as rider_attendance_days\n", "        from attendance_by_rider\n", "        group by 1,2,3\n", "        Union ALl\n", "        select \n", "              week,\n", "              runnr_city,\n", "              'All',\n", "              sum(login_minutes) as login_minutes,\n", "              --sum(login_minutes*rider_attendance_days)/Nullif(sum(login_minutes),0) as rider_attendance_days\n", "              avg(rider_attendance_days) as rider_attendance_days\n", "        from attendance_by_rider\n", "        group by 1,2,3\n", "        )\n", "    )\n", "\n", "select * from attendance\n", "\"\"\"\n", "\n", "attendance_df = pd.read_sql_query(attendance_query, conn)"]}, {"cell_type": "code", "execution_count": null, "id": "7d835a63-f3ac-44b7-84d4-2f623340ab5e", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"lm_performance_dashboard_attendance\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"week\", \"type\": \"double\", \"description\": \"week\"},\n", "        {\"name\": \"runnr_city\", \"type\": \"VARCHAR\", \"description\": \"City associated with the runner\"},\n", "        {\"name\": \"l_cohort\", \"type\": \"VARCHAR\", \"description\": \"Login cohort of the driver\"},\n", "        {\"name\": \"login_minutes\", \"type\": \"double\", \"description\": \"login minutes\"},\n", "        {\n", "            \"name\": \"rider_attendance_days\",\n", "            \"type\": \"double\",\n", "            \"description\": \"rider attendance days per week\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"week\", \"runnr_city\", \"l_cohort\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"lm_performance_dashboard_attendance\",\n", "}\n", "pb.to_trino(attendance_df, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "b83fcb4f-4a89-4eef-a305-00561fe52fc7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}