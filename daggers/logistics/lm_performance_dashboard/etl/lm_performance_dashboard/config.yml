alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: lm_performance_dashboard
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: logistics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U08FR9S0MGD
path: logistics/lm_performance_dashboard/etl/lm_performance_dashboard
paused: false
pool: logistics_pool
project_name: lm_performance_dashboard
schedule:
  end_date: '2025-09-07T00:00:00'
  interval: 5 4 * * *
  start_date: '2025-06-10T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
