{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Pre-requisite data creation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["What all is needed for the final program to consume:\n", "* FE_DPs\n", "* GSP_dps\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Importing necessary libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import os"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start = time.time()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from shapely import wkb\n", "import shapely.vectorized as sv"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import math\n", "import requests\n", "import geopandas as gpd\n", "import pencilbox as pb\n", "import datetime\n", "\n", "red = pb.get_connection(\"redshift\")\n", "con = pb.get_connection(\"route_allocation\")\n", "carto = pb.get_connection(\"carto\")\n", "\n", "# #stage\n", "# carto = pb.get_connection(\"carto_stage\")\n", "# con = create_engine(pb.get_secret('dse/gis_db/route_allocation_stage')['uri'])\n", "\n", "import random\n", "import shapely.geometry as geometry\n", "from shapely.geometry import Point\n", "\n", "import json\n", "from h3 import h3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# gis api\n", "headers = {\"Content-type\": \"application/json\", \"Accept\": \"text/plain\"}\n", "url = \"https://dse-gis-api-service-data.prod-k8s.grofer.io/layers/actions/tag\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["curr_date = str(\n", "    (datetime.datetime.utcnow() + datetime.timedelta(hours=5.5)).date()\n", "    + datetime.<PERSON><PERSON><PERSON>(days=1)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cities = {}\n", "cities[\"HR-NCR\"] = [\"HR22\", \"HR16\", \"HR32\", \"HR18\"]\n", "cities[\"Bengaluru\"] = [\"BA18\"]\n", "cities[\"Kolkata\"] = [\"KOL1\", \"KOL6\", \"KOL9\", \"KOL8\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stations = []\n", "for key, values in cities.items():\n", "    stations.extend(values)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["THRESHOLD = 50  # Boundary of old and new GSP\n", "THRESH_NEW = 0.2  # DEFINES PROPORTION FOR NEWER GSPS\n", "THRESH_OLD = 20  # DEFINES NUMBER OF ORDERS FOR EXPERIENCED GSPS\n", "NUM_HISTORICAL_ORDERS = 500\n", "T_30 = 30\n", "T_2 = 2\n", "MIN_NEW_GSP_CAPACITY = 2  # NE<PERSON> is someone with less than 7 days expereince\n", "MAX_NEW_GSP_CAPACITY = 3  # 3\n", "MIN_OLD_GSP_CAPACITY = 4\n", "MAX_OLD_GSP_CAPACITY = 7  # 7\n", "MIN_FE_CAPACITY = 10  # (decreasing this as it will enforce order allocation)\n", "MAX_FE_CAPACITY = 14  # 14\n", "HEAVY_WEIGHT = 35\n", "HEX_GRID_LEVEL = 9\n", "NEW_GSP_AGE = 7\n", "ORDERS_THRESH = (\n", "    2  # An old GSP must have greater than orders_thresh order to subscribe to a hexagon\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Extract GSP and DP association utils"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "    with\n", "    crates as\n", "    (\n", "        select os.order_id, \n", "            sum(total_crates_count) as total_no_of_crates,\n", "            sum(cold_crates) as no_of_cold_crates\n", "        from\n", "        (\n", "            select distinct\n", "                grofers_order_id, \n", "                case when crate_ids = '' THEN 0\n", "                     else length(crate_ids) - length(replace(crate_ids,',',''))+1\n", "                end as total_crates_count,\n", "                case when crate_ids = '' THEN 0\n", "                     else REGEXP_COUNT(crate_ids, '(CBX|CLD|C1)')\n", "                end as cold_crates\n", "            from\n", "            (\n", "                select distinct \n", "                    grofers_order_id,\n", "                    crate_ids,\n", "                    pos_timestamp, \n", "                    dense_rank()over(partition by grofers_order_id order by pos_timestamp desc) as latest\n", "                from pos_invoice_summary\n", "                where invoice_type_id = 1\n", "            )pos\n", "            where pos.latest = 1 --remove duplicates\n", "        )pos\n", "        join oms_suborder os on os.id = pos.grofers_order_id\n", "        join oms_order o on os.order_id = o.id\n", "        left join oms_merchant m on o.merchant_id = m.id and  m.virtual_merchant_type = 'superstore_merchant'\n", "        where \n", "        (o.update_ts + interval '5.5 hour')::date between '{START_DATE}' and '{END_DATE}'\n", "        and os.type not like 'Digital%%'\n", "        and os.type not like 'Drop%%'\n", "        and o.current_status = 'DELIVERED'\n", "        and lower(m.city_name) not in ('not in service area')\n", "        and lower(m.city_name) not like '%%b2b%%'\n", "        and m.id not in (28759,28760,26680) -- removing load testing IDs\n", "        and lower(o.station) not like '%%dum%%'\n", "        and lower(o.station) not like '%%test%%'\n", "        and lower(o.station) not like '%%b2b%%'\n", "        and lower(o.station ) not like 'nsd'\n", "        group by 1\n", "    ),\n", "    weight_items as\n", "    (\n", "        select distinct\n", "        c.name as city,\n", "        s.name as station,\n", "        (o.delivery_slot_start_time + interval '5.5 hours')::date as delivery_date,\n", "        o.route_id as lm_route_id,\n", "        (o.updated_at+ interval '5.5 hours')::timestamp as updated_ts,\n", "        lmfe.id as lm_fe_id,\n", "        lmfe.employee_id,\n", "        case when (lmfe.employee_id like 'GSP%%' or lmfe.employee_id like 'VSP%%') then 'GSP'\n", "            when dp.id is not null then 'DP'\n", "            when lmfe.id is not null then 'GROFERS FE'\n", "            else 'UNASSIGNED'\n", "        end as fe_category,\n", "        case when lmr.route_state <> 'DELIVERED' then 'unassigned'\n", "            else case when smfe.delivery_store_id is not null then 'GSP'\n", "                    when smdp.id is not null then 'DP'\n", "                    when smfe.id is not null then 'DC'\n", "                    when smr.id is null then 'NO SECOND MILE'\n", "                    else 'UNASSIGNED'\n", "                end\n", "        end as routed_via,\n", "        smdp.name as DP_name,\n", "        case when extract(hour from (o.delivery_slot_start_time + interval '5.5 hours'))/10 < 1 then '0'||cast(extract(hour from (o.delivery_slot_start_time + interval '5.5 hours')) as text)\n", "            else cast(extract(hour from (o.delivery_slot_start_time + interval '5.5 hours')) as text) end \n", "        ||':'||\n", "        case when extract(minute from (o.delivery_slot_start_time + interval '5.5 hours'))/10 < 1 then '0'||cast(extract(minute from (o.delivery_slot_start_time + interval '5.5 hours')) as text)\n", "            else cast(extract(minute from (o.delivery_slot_start_time + interval '5.5 hours')) as text) end\n", "        ||':'||\n", "        case when extract(second from (o.delivery_slot_start_time + interval '5.5 hours'))/10 <1 then '0'||cast(extract(second from (o.delivery_slot_start_time + interval '5.5 hours')) as text)\n", "            else cast(extract(second from (o.delivery_slot_start_time + interval '5.5 hours')) as text) end \n", "        ||'-'||\n", "        case when extract(hour from (o.delivery_slot_end_time + interval '5.5 hours'))/10 < 1 then '0'||cast(extract(hour from (o.delivery_slot_end_time + interval '5.5 hours')) as text)\n", "            else cast(extract(hour from (o.delivery_slot_end_time + interval '5.5 hours')) as text) end \n", "        ||':'||\n", "        case when extract(minute from (o.delivery_slot_end_time + interval '5.5 hours'))/10 < 1 then '0'||cast(extract(minute from (o.delivery_slot_end_time + interval '5.5 hours')) as text)\n", "            else cast(extract(minute from (o.delivery_slot_end_time + interval '5.5 hours')) as text) end \n", "        ||':'||\n", "        case when extract(second from (o.delivery_slot_end_time + interval '5.5 hours'))/10 < 1 then '0'||cast(extract(second from (o.delivery_slot_end_time + interval '5.5 hours')) as text)\n", "            else cast(extract(second from (o.delivery_slot_end_time + interval '5.5 hours')) as text) end\n", "        as slot,\n", "        omc.customer_id,\n", "        oca.location_lat as customer_latitude,\n", "        oca.location_lon as customer_longitude,\n", "        o.cart_id,\n", "        oo.type as order_type,\n", "        o.external_order_id as order_id,\n", "        coalesce(sum(case when storage_type in ('REFRIGERATED','DEEP_FREEZE') then oi.quantity end),0) as no_of_cold_items,\n", "        coalesce(sum(case when handling_type = 'LARGE' then oi.quantity end),0) as no_of_large_items,\n", "        sum(case when oi.quantity > 0 and oi.total_weight/oi.quantity > 50 then cast(oi.total_weight as decimal(18,2))/1000 \n", "                else oi.total_weight end) as order_weight\n", "        from lake_last_mile.order o \n", "        inner join lake_last_mile.order_action oa on oa.order_id = o.id and oa.action_type = 'DELIVERED'\n", "        inner join lake_last_mile.order_item oi on oi.order_id = o.id\n", "        inner join oms_order oo on o.external_order_id = oo.id\n", "        inner join oms_cart omc on omc.id = o.cart_id\n", "        left join oms_cart_address oca on oca.id = omc.address_id\n", "        left join lake_last_mile.route as lmr on o.route_id = lmr.id  and lmr.route_state = 'DELIVERED'\n", "        left join lake_last_mile.secondmile_route smr on smr.id = lmr.second_mile_route_id\n", "        left join lake_last_mile.field_executive smfe on smr.field_executive_id = smfe.id\n", "        left join lake_last_mile.distribution_point smdp on smfe.id = smdp.primary_fe_id \n", "        left join lake_last_mile.field_executive lmfe on oa.field_executive_id = lmfe.id\n", "        left join lake_last_mile.distribution_point dp on lmfe.id = dp.primary_fe_id \n", "        inner join lake_last_mile.station s on s.id = o.station_id\n", "        inner join lake_last_mile.city c on c.id = s.city_id\n", "        where oa.action_type = 'DELIVERED'\n", "        and extract(year from oa.actual_event_time) in ('2019','2020')\n", "        and (o.delivery_slot_start_time + interval '5.5 hours')::date between '{START_DATE}' and '{END_DATE}'\n", "        and oo.type not like 'Drop%%'\n", "        and oo.type not like 'Digital%%'\n", "        and oi.item_delivery_action_type <> 'RETURN'\n", "        and lower(c.name) not in ('not in service area')\n", "        and lower(c.name) not like '%%b2b%%'\n", "        and lower(s.name) not like '%%dum%%'\n", "        and lower(s.name) not like '%%test%%'\n", "        and lower(s.name) not like '%%b2b%%'\n", "        and lower(s.name) not like 'nsd'\n", "        group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17\n", "    ),\n", "    order_level_base as\n", "    (\n", "        select distinct \n", "            wi.*,\n", "            coalesce(c.total_no_of_crates,0) as total_no_of_crates,\n", "            coalesce(c.no_of_cold_crates,0) as no_of_cold_crates,\n", "            case when no_of_cold_items > 0 then 1 else 0 end as has_cold_item,\n", "            case when no_of_large_items > 0 then 1 else 0 end as has_large_item\n", "        from weight_items wi\n", "        left join crates c on wi.order_id = c.order_id\n", "    )\n", "    select distinct * \n", "    from order_level_base\n", "    where station in ({stations});\n", "    \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def format_slots_assoc(x):\n", "    \"\"\" Assigns ID to a slot.\n", "    A, B, C, D, K, L are the slots which can be assigned.\"\"\"\n", "    s = x\n", "\n", "    slots = {\n", "        \"06:00:00 - 09:00:00\": \"A\",\n", "        \"10:00:00 - 13:00:00\": \"B\",\n", "        \"16:00:00 - 19:00:00\": \"C\",\n", "        \"20:00:00 - 23:00:00\": \"D\",\n", "    }\n", "    slot_start = np.array([(6 + 9), (10 + 13), (16 + 19), (20 + 23)]) / 2\n", "    arg_slot_map = {0: \"A\", 1: \"B\", 2: \"C\", 3: \"D\"}\n", "\n", "    if s in slots.keys():\n", "        return slots[s]\n", "    else:\n", "        start, end = (\n", "            int(s.split(\"-\")[0].strip().split(\":\")[0]),\n", "            int(s.split(\"-\")[1].strip().split(\":\")[0]),\n", "        )\n", "        # st is mean of start and end slot\n", "        st = (\n", "            (((24 - start) + end) / 2 + start) % 24\n", "            if start > end\n", "            else (start + end) / 2\n", "        )\n", "\n", "        if ((end >= start) & (end - start <= 3)) | (\n", "            (start > end) & ((24 - start + end) <= 3)\n", "        ):\n", "            return arg_slot_map[\n", "                np.argmin(\n", "                    np.absolute(\n", "                        list(map(lambda x: 24 - x if x > 12 else x, slot_start - st))\n", "                    )\n", "                )\n", "            ]\n", "        else:\n", "            return \"K\" if st < 14 else \"L\"\n", "\n", "    return None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def make_query_new(query, curr_date, stations):  # use stations\n", "\n", "    # To be used by format_slots\n", "    data = {}\n", "    data[\"START_DATE\"] = str(\n", "        (pd.to_datetime(curr_date) - pd.to_timedelta(T_30, unit=\"d\")).date()\n", "    )\n", "    data[\"END_DATE\"] = str(\n", "        (pd.to_datetime(curr_date) - pd.to_timedelta(T_2, unit=\"d\")).date()\n", "    )  # asssuming route making happens\n", "    # at T-2\n", "\n", "    historic_df = pd.read_sql(\n", "        sql=query.format(\n", "            stations=\",\".join(repr(e) for e in stations),\n", "            START_DATE=data[\"START_DATE\"],\n", "            END_DATE=data[\"END_DATE\"],\n", "        ),\n", "        con=red,\n", "    )\n", "\n", "    df = historic_df\n", "\n", "    df[\"slot_id\"] = df.apply(lambda x: format_slots_assoc(x[\"slot\"]), axis=1)\n", "    df[\"customer_longitude\"] = df[\"customer_longitude\"] / 1000000\n", "    df[\"customer_latitude\"] = df[\"customer_latitude\"] / 1000000\n", "\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_dp_other(p, polys):\n", "    \"\"\" Extarct DP for lat,long not found\n", "    in station's polygons\n", "    \"\"\"\n", "    dist = p.distance(polys[\"centroid\"][0])\n", "    dp = polys[\"Name\"][0]\n", "    for i in range(1, polys.shape[0]):\n", "        if p.distance(polys[\"centroid\"][i]) < dist:\n", "            dp = polys[\"Name\"][i]\n", "            dist = p.distance(polys[\"centroid\"][i])\n", "\n", "    return dp"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def check_station_gsp(x, df, station):\n", "    \"\"\" To check if the last served station of GSP\n", "    is same as station. Use of this function is to\n", "    ensure old stations are not taken into account.\n", "    \"\"\"\n", "    last_station = (\n", "        df[df[\"lm_fe_id\"] == x]\n", "        .sort_values([\"delivery_date\"], ascending=False)\n", "        .iloc[0][\"station\"]\n", "        == station\n", "    )\n", "    return last_station"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_dp(filter_df, polys):\n", "    \"\"\" Extract DPs for historical orders.\n", "    \"\"\"\n", "    filter_df[\"DP_name\"] = \"other\"\n", "\n", "    for i in range(polys.shape[0]):\n", "        temp = sv.contains(\n", "            polys[\"geometry\"][i],\n", "            x=list(filter_df[\"customer_longitude\"]),\n", "            y=list(filter_df[\"customer_latitude\"]),\n", "        )\n", "        filter_df.loc[np.where(temp == True)[0], \"DP_name\"] = polys[\"Name\"][i]\n", "\n", "    polys[\"centroid\"] = polys[\"geometry\"].apply(lambda x: x.centroid)\n", "\n", "    temp_df = filter_df[filter_df[\"DP_name\"] == \"other\"]\n", "\n", "    if temp_df.shape[0] > 0:  # adding condition (chnage in original notebook)\n", "        temp_df[\"DP_name\"] = temp_df.apply(\n", "            lambda x: extract_dp_other(\n", "                Point(x[\"customer_longitude\"], x[\"customer_latitude\"]), polys\n", "            ),\n", "            axis=1,\n", "        )\n", "        filter_df.loc[filter_df[\"DP_name\"] == \"other\", \"DP_name\"] = temp_df[\"DP_name\"]\n", "\n", "    return filter_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_global_gsp(query, df, curr_date, slot, station, city, polys):\n", "    \"\"\"\n", "    Extracts GSP associations for one month\n", "    \"\"\"\n", "    df_station = df[df[\"station\"] == station]\n", "    if slot == \"morning\":  # starting time less than 2 then morning\n", "        filter_df = df_station[\n", "            (df_station[\"slot_id\"] == \"A\")\n", "            | (df_station[\"slot_id\"] == \"B\")\n", "            | (df_station[\"slot_id\"] == \"K\")\n", "        ]\n", "    else:\n", "        filter_df = df_station[\n", "            (df_station[\"slot_id\"] == \"C\")\n", "            | (df_station[\"slot_id\"] == \"D\")\n", "            | (df_station[\"slot_id\"] == \"L\")\n", "        ]\n", "\n", "    #     df, filter_df = make_query(query, curr_date, slot, station, city)\n", "\n", "    filter_df.reset_index(inplace=True, drop=True)\n", "    filter_df1 = filter_df.copy()\n", "\n", "    filter_df = extract_dp(filter_df, polys)\n", "\n", "    fe_list = filter_df.loc[\n", "        filter_df[\"fe_category\"] == \"GROFERS FE\", \"lm_fe_id\"\n", "    ].unique()\n", "    gsp_list = filter_df.loc[filter_df[\"fe_category\"] == \"GSP\", \"lm_fe_id\"].unique()\n", "\n", "    gsp_list = list(\n", "        filter(lambda x: check_station_gsp(x, df, station), gsp_list)\n", "    )  # checks if last station is the same\n", "    fe_list = list(filter(lambda x: check_station_gsp(x, df, station), fe_list))\n", "\n", "    fe_dps = {}\n", "    gsp_dps = {}\n", "\n", "    for gsp in gsp_list:\n", "        temp = (\n", "            filter_df[filter_df[\"lm_fe_id\"] == gsp]\n", "            .sort_values([\"delivery_date\"], ascending=False)\n", "            .reset_index(drop=True)\n", "            .iloc[:NUM_HISTORICAL_ORDERS, :]\n", "        )\n", "        if temp.shape[0] > THRESHOLD:  # Threshold\n", "            thresh = THRESH_OLD\n", "        else:\n", "            thresh = THRESH_NEW * temp.shape[0]\n", "\n", "        dp_count = temp[\"DP_name\"].value_counts()\n", "        dp_names = list(dp_count[dp_count > thresh].index)\n", "\n", "        gsp_dps[gsp] = dp_names\n", "\n", "    for fe in fe_list:\n", "        temp = (\n", "            filter_df[filter_df[\"lm_fe_id\"] == fe]\n", "            .sort_values([\"delivery_date\"], ascending=False)\n", "            .reset_index(drop=True)\n", "            .iloc[:NUM_HISTORICAL_ORDERS, :]\n", "        )\n", "        if temp.shape[0] > THRESHOLD:\n", "            thresh = THRESH_OLD\n", "        else:\n", "            thresh = THRESH_NEW * temp.shape[0]\n", "\n", "        dp_count = temp[\"DP_name\"].value_counts()\n", "        dp_names = list(dp_count[dp_count > thresh].index)\n", "\n", "        fe_dps[fe] = dp_names\n", "\n", "    return fe_dps, gsp_dps, df_station, filter_df1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Extract DP and GSP association"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_dp_assoc(query, df, curr_date, station, city, polys):\n", "    Mfe_dps, Mgsp_dps, data1, temp = extract_global_gsp(\n", "        query, df, curr_date, \"morning\", station, city, polys\n", "    )\n", "    Efe_dps, Egsp_dps, data1, temp = extract_global_gsp(\n", "        query, df, curr_date, \"evening\", station, city, polys\n", "    )\n", "    return Mfe_dps, Mgsp_dps, Efe_dps, Egsp_dps, data1, temp"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Extract Dp for an order"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_dp_poly(station, con):\n", "    temp = station\n", "\n", "    if (station == \"JAI1\") | (station == \"JAI8\"):\n", "        temp = \"Jaipur\"\n", "    if station[:2] == \"HP\":\n", "        temp = \"Hapur\"\n", "    if station[:2] == \"AL\":\n", "        temp = \"Aligarh\"\n", "\n", "    polys = pd.read_sql(sql=\"SELECT * FROM grofers.{}\".format(temp.lower()), con=con)\n", "    polys[\"station\"] = polys[\"dp_display_id\"].apply(lambda x: x.split(\"-\")[0])\n", "    polys = polys[polys[\"station\"] == station]\n", "    polys[\"geometry\"] = polys[\"the_geom\"].apply(lambda x: wkb.loads(x, hex=True))\n", "    polys.rename({\"name\": \"Name\"}, axis=1, inplace=True)\n", "    polys.reset_index(drop=True, inplace=True)\n", "    return polys"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Identify new GSPs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def label_new_GSP(past_data):\n", "    new_gsps = {}\n", "\n", "    for i in past_data[\"lm_fe_id\"].unique():\n", "        if (\n", "            past_data[past_data[\"lm_fe_id\"] == i]\n", "            .groupby([\"delivery_date\"])\n", "            .count()\n", "            .shape[0]\n", "            < 7\n", "        ):\n", "            new_gsps[i] = 1\n", "        else:\n", "            new_gsps[i] = 0\n", "\n", "    return new_gsps"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_fe_metadata(worker, df, station, curr_date, morning_dps, evening_dps):\n", "    \"\"\" Creating table for workers which were present the previous day/\"\"\"\n", "    temp = pd.DataFrame(\n", "        df.loc[df[\"fe_category\"] == worker, \"lm_fe_id\"].unique(), columns=[\"lm_fe_id\"]\n", "    )\n", "    if temp.shape[0] > 0:\n", "        df2 = df.drop_duplicates([\"lm_fe_id\", \"employee_id\"])[\n", "            [\"lm_fe_id\", \"employee_id\"]\n", "        ]\n", "        temp = pd.merge(temp, df2, on=\"lm_fe_id\")\n", "        temp[\"fe_category\"] = worker\n", "        temp[\"Station\"] = station\n", "        temp[\"Delivery_date\"] = curr_date\n", "        temp[\"morning_dp\"] = temp[\"lm_fe_id\"].map(morning_dps)\n", "        temp[\"evening_dp\"] = temp[\"lm_fe_id\"].map(evening_dps)\n", "        temp[\"slot\"] = temp.apply(\n", "            lambda x: df.loc[df[\"lm_fe_id\"] == x[\"lm_fe_id\"], \"slot\"].unique(), axis=1\n", "        )\n", "        return temp\n", "    return None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_fe_gsp_list(attend, worker, slots):\n", "    worker_list = (\n", "        attend.loc[attend[\"fe_category\"] == worker]\n", "        .apply(lambda x: filter_fe_updated(x, slots), axis=1)\n", "        .unique()\n", "    )\n", "    if worker_list.any() != None:\n", "        worker_list = worker_list[~np.isnan(worker_list)]\n", "    return worker_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def filter_fe_updated(x, slots):\n", "    for i in slots:\n", "        if i in x[\"slot_id\"]:\n", "            return x[\"lm_fe_id\"]\n", "    return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Creating hexagons for all the GSPs/FEs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_hexagons(\n", "    gsp_fe_orders, gsp_fe_data, HEX_GRID_LEVEL, NEW_GSP_AGE, ORDERS_THRESH\n", "):\n", "    \"\"\" Creates hexagons for GSPs historical data. \"\"\"\n", "    test = pd.DataFrame([])\n", "    test[\"gsps\"] = list(gsp_fe_orders.keys())\n", "    test[\"geo\"] = test[\"gsps\"]\n", "    test[\"geo\"] = test[\"geo\"].map(gsp_fe_orders)\n", "    test.index = test[\"gsps\"]\n", "    test = (\n", "        test.apply(lambda x: pd.Series(x[\"geo\"].tolist()), axis=1)\n", "        .stack()\n", "        .reset_index(level=1, drop=True)\n", "        .reset_index()\n", "    )\n", "    test.columns = [\"GSP ID\", \"Past order\"]\n", "    test[\"customer_latitude\"] = test[\"Past order\"].apply(lambda x: x[0])\n", "    test[\"customer_longitude\"] = test[\"Past order\"].apply(lambda x: x[1])\n", "    test.drop([\"Past order\"], axis=1, inplace=True)\n", "    test[\"hexagon_index\"] = test.apply(\n", "        lambda x: h3.geo_to_h3(\n", "            x[\"customer_latitude\"], x[\"customer_longitude\"], HEX_GRID_LEVEL\n", "        ),\n", "        axis=1,\n", "    )\n", "    hex_density = test.groupby([\"GSP ID\", \"hexagon_index\"]).count()\n", "    hex_density.reset_index(inplace=True)\n", "\n", "    hexagons = list(\n", "        hex_density[\"hexagon_index\"].unique()\n", "    )  # Hexagons allocated to the GSPs\n", "\n", "    gsp_hex_relation = {}\n", "\n", "    for hexi in hexagons:\n", "        temp_hex = hex_density[hex_density[\"hexagon_index\"] == hexi]\n", "        temp_hex.reset_index(inplace=True, drop=True)\n", "        temp_dict = {}\n", "        for i in range(temp_hex.shape[0]):\n", "            age_gsp = (\n", "                gsp_fe_data[gsp_fe_data[\"lm_fe_id\"] == temp_hex[\"GSP ID\"][i]]\n", "                .groupby([\"delivery_date\"])\n", "                .count()\n", "                .shape[0]\n", "            )\n", "            if (age_gsp > NEW_GSP_AGE) & (\n", "                temp_hex[\"customer_latitude\"][i] > ORDERS_THRESH\n", "            ):  # Delivered more than 2 orders for experienced FE\n", "                temp_dict[int(temp_hex[\"GSP ID\"][i])] = np.float(\n", "                    temp_hex[\"customer_latitude\"][i]\n", "                )  # Correct this, it\n", "            elif age_gsp <= NEW_GSP_AGE:\n", "                temp_dict[int(temp_hex[\"GSP ID\"][i])] = np.float(\n", "                    temp_hex[\"customer_latitude\"][i]\n", "                )\n", "        if len(temp_dict) != 0:\n", "            gsp_hex_relation[hexi] = temp_dict\n", "\n", "    return gsp_hex_relation, hex_density"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def format_slots_list(x):\n", "    \"\"\" Create list of slot_ids for attend dataframe.\n", "    \"\"\"\n", "    ll = []\n", "    for s in x:\n", "        ll.append(format_slots_assoc(s))\n", "\n", "    return ll"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Creating attendance and hexagon data"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"outputs_hidden": true}}, "outputs": [], "source": ["df = make_query_new(query, curr_date, stations)\n", "all_hexagons = pd.DataFrame([])\n", "all_attend = pd.DataFrame([])\n", "for city in cities.keys():\n", "    for station in cities[city]:\n", "        print(\"On station: \", station)\n", "\n", "        polys = extract_dp_poly(station, carto)\n", "\n", "        (\n", "            morning_fe_dps,\n", "            morning_gsp_dps,\n", "            evening_fe_dps,\n", "            evening_gsp_dps,\n", "            data1,\n", "            temp1,\n", "        ) = extract_dp_assoc(query, df, curr_date, station, city, polys)\n", "        print(data1[\"delivery_date\"].value_counts())\n", "        print(len(morning_fe_dps), len(morning_fe_dps))\n", "        # last day list of GSPs and FEs as proxy for attendance\n", "        last_date = data1.sort_values([\"delivery_date\"], ascending=False)[\n", "            \"delivery_date\"\n", "        ].iloc[0]\n", "        filter_df = data1[data1[\"delivery_date\"] == last_date]  # T-2 proxy\n", "        print(filter_df.shape)\n", "        attend_gsp = create_fe_metadata(\n", "            \"GSP\", filter_df, station, curr_date, morning_gsp_dps, evening_gsp_dps\n", "        )\n", "        attend_fe = create_fe_metadata(\n", "            \"GROFERS FE\", filter_df, station, curr_date, morning_fe_dps, evening_fe_dps\n", "        )\n", "\n", "        attend = pd.concat([attend_gsp, attend_fe], axis=0).reset_index(drop=True)\n", "        # convert date to database format\n", "        attend[\"slot_id\"] = attend.apply(lambda x: format_slots_list(x[\"slot\"]), axis=1)\n", "\n", "        print(attend.head())\n", "\n", "        data1[\"delivery_date\"] = pd.to_datetime(data1[\"delivery_date\"])\n", "        data1 = data1[data1[\"delivery_date\"] < curr_date].reset_index(drop=True)\n", "\n", "        # Marking the new GSPs (tenure < 7) for allocating less orders\n", "\n", "        attend[\"isNew\"] = attend[\"lm_fe_id\"].map(label_new_GSP(data1))\n", "\n", "        # storing hexagon ids which were serviced in the past\n", "        total_hexagon = pd.DataFrame([])\n", "\n", "        for slot in [\"morning\", \"evening\"]:\n", "\n", "            print(\"On slot: \", slot)\n", "            dc_gsp_list, dc_fe_list = np.array([]), np.array([])\n", "            if slot == \"morning\":\n", "                possible_slots = [\"A\", \"B\", \"K\"]\n", "            elif slot == \"evening\":\n", "                possible_slots = [\"C\", \"D\", \"L\"]\n", "\n", "            dc_gsp_list = create_fe_gsp_list(attend, \"GSP\", possible_slots)\n", "            if attend[attend[\"fe_category\"] == \"GROFERS FE\"].shape[0] > 0:\n", "                dc_fe_list = create_fe_gsp_list(attend, \"GROFERS FE\", possible_slots)\n", "\n", "            # edge case checking\n", "            if ((len(dc_gsp_list) == 0) | (dc_gsp_list.all() == None)) & (\n", "                (len(dc_fe_list) == 0) | (dc_fe_list.all() == None)\n", "            ):  # No GSPs or FEs\n", "                continue\n", "\n", "            elif ((len(dc_fe_list) == 0) | (dc_fe_list.all() == None)) & (\n", "                len(dc_gsp_list) != 0\n", "            ):  # FE list has only None or len zero\n", "                dc_gsp_fe_list = dc_gsp_list\n", "\n", "            elif ((len(dc_gsp_list) == 0) | (dc_gsp_list.all() == None)) & (\n", "                len(dc_fe_list) != 0\n", "            ):  # GSPs is NONE or len zero\n", "                dc_gsp_fe_list = dc_fe_list\n", "\n", "            else:\n", "                dc_gsp_fe_list = np.append(dc_fe_list, dc_gsp_list)\n", "\n", "            # Extracting historical data for the lm_fe_id\n", "            gsp_fe_data = data1[data1[\"lm_fe_id\"].isin(dc_gsp_fe_list)]\n", "            print(gsp_fe_data.shape)\n", "            gsp_fe_orders = {}\n", "\n", "            for i in dc_gsp_fe_list:\n", "                temp = gsp_fe_data[gsp_fe_data[\"lm_fe_id\"] == i]\n", "                if (\n", "                    temp.shape[0] > 0\n", "                ):  # Make sure no new GSPs are in past data, their distance metric will always be zero\n", "                    gsp_fe_orders[i] = (\n", "                        temp.sort_values([\"updated_ts\"], ascending=False)  # change this\n", "                        .reset_index(drop=True)\n", "                        .iloc[:NUM_HISTORICAL_ORDERS, :][\n", "                            [\"customer_latitude\", \"customer_longitude\"]\n", "                        ]\n", "                        .as_matrix()\n", "                    )\n", "\n", "            gsp_hex_relation, hex_density = create_hexagons(\n", "                gsp_fe_orders, gsp_fe_data, HEX_GRID_LEVEL, NEW_GSP_AGE, ORDERS_THRESH\n", "            )\n", "\n", "            hex_df = pd.DataFrame(list(gsp_hex_relation.keys()), columns=[\"hexagon_id\"])\n", "            hex_df[\"FE/GSP list\"] = hex_df[\"hexagon_id\"].map(gsp_hex_relation)\n", "            hex_df[\"Delivery_date\"] = curr_date\n", "            hex_df[\"Station\"] = station\n", "            hex_df[\"Shift\"] = slot\n", "\n", "            total_hexagon = pd.concat([total_hexagon, hex_df])\n", "        all_attend = pd.concat([all_attend, attend])\n", "        all_hexagons = pd.concat([all_hexagons, total_hexagon])\n", "end = time.time()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create capacity table"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Follows the following format:\n", "City-->Station-->Slot-->DP-->{GSP ID, max_capacity, min_capacity, new_GSP}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def assign_min_capacity(x, attend, capacity):\n", "    if (x[\"Slot\"] == \"A\") | (x[\"Slot\"] == \"B\") | (x[\"Slot\"] == \"K\"):\n", "        num_dps = len(\n", "            attend.loc[attend[\"lm_fe_id\"] == x[\"lm_fe_id\"], \"morning_dp\"].iloc[0]\n", "        )\n", "    else:\n", "        num_dps = len(\n", "            attend.loc[attend[\"lm_fe_id\"] == x[\"lm_fe_id\"], \"evening_dp\"].iloc[0]\n", "        )\n", "    new = attend.loc[attend[\"lm_fe_id\"] == x[\"lm_fe_id\"], \"isNew\"].iloc[0]\n", "    #     print(num_dps)\n", "    if (new) & (x[\"fe_category\"] == \"GSP\"):\n", "        return MIN_NEW_GSP_CAPACITY // num_dps\n", "    elif (~new) & (x[\"fe_category\"] == \"GSP\"):\n", "        return MIN_OLD_GSP_CAPACITY // num_dps\n", "    else:\n", "        return MIN_FE_CAPACITY // num_dps"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fe_filter(x, slot, dp, col_name):\n", "    if slot in x[\"slot_id\"]:\n", "        if dp in x[col_name]:\n", "            return True\n", "    return False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["capacity = pd.DataFrame([])\n", "for city in cities.keys():\n", "    for station in cities[city]:\n", "\n", "        polys = extract_dp_poly(station, carto)\n", "\n", "        temp = all_attend[all_attend[\"Station\"] == station]\n", "        for slot in [\"A\", \"B\", \"C\", \"D\", \"K\", \"L\"]:\n", "            if (slot == \"A\") | (slot == \"B\") | (slot == \"K\"):\n", "                col_name = \"morning_dp\"\n", "            else:\n", "                col_name = \"evening_dp\"\n", "\n", "            for dp in polys[\"Name\"].unique():\n", "                temp[\"status\"] = temp.apply(\n", "                    lambda x: fe_filter(x, slot, dp, col_name), axis=1\n", "                )\n", "                temp_cap = temp[temp[\"status\"] == True][\n", "                    [\"lm_fe_id\", \"fe_category\", \"Station\", \"isNew\"]\n", "                ]\n", "                #                 print(temp[temp[\"status\"] == True])\n", "                temp_cap[\"Slot\"] = slot\n", "                temp_cap[\"DP_name\"] = dp\n", "                temp_cap[\"city\"] = city\n", "                capacity = pd.concat([capacity, temp_cap], axis=0).reset_index(\n", "                    drop=True\n", "                )\n", "\n", "capacity.loc[\n", "    (capacity[\"fe_category\"] == \"GSP\") & (capacity[\"isNew\"] == 1), \"max_capacity\"\n", "] = MAX_NEW_GSP_CAPACITY\n", "capacity.loc[\n", "    (capacity[\"fe_category\"] == \"GSP\") & (capacity[\"isNew\"] == 0), \"max_capacity\"\n", "] = MAX_OLD_GSP_CAPACITY\n", "capacity.loc[capacity[\"fe_category\"] == \"GROFERS FE\", \"max_capacity\"] = MAX_FE_CAPACITY\n", "capacity[\"min_capacity\"] = capacity.apply(\n", "    lambda x: assign_min_capacity(x, all_attend, capacity), axis=1\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Add to route_allocation database"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_hexagons.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["capacity[\"delivery_date\"] = curr_date\n", "capacity.reset_index(inplace=True)\n", "capacity.rename({\"index\": \"capacity_id\"}, axis=1, inplace=True)\n", "capacity[\"delivery_date\"] = pd.to_datetime(capacity[\"delivery_date\"])\n", "capacity.columns = capacity.columns.str.lower()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_attend.reset_index(inplace=True)\n", "all_attend.rename({\"index\": \"meta_id\"}, axis=1, inplace=True)\n", "all_attend[\"Delivery_date\"] = pd.to_datetime(all_attend[\"Delivery_date\"])\n", "all_attend.columns = all_attend.columns.str.lower()\n", "all_attend[\"slot\"] = all_attend[\"slot\"].apply(lambda x: list(x))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_hexagons.reset_index(drop=True, inplace=True)\n", "all_hexagons.reset_index(inplace=True)\n", "all_hexagons.rename({\"index\": \"worker_hexagon_id\"}, axis=1, inplace=True)\n", "all_hexagons[\"Delivery_date\"] = pd.to_datetime(all_hexagons[\"Delivery_date\"])\n", "all_hexagons.columns = all_hexagons.columns.str.lower()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_hexagons[\"fe/gsp list\"] = all_hexagons.apply(\n", "    lambda x: json.dumps(x[\"fe/gsp list\"]), axis=1\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Saving to <PERSON>, DONOT EXECUTE UNTIL SURE!!!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_attend.to_sql(\n", "    name=\"fe_metadata\", con=con, schema=\"route\", if_exists=\"replace\", index=False\n", ")\n", "all_hexagons.to_sql(\n", "    name=\"worker_hexagon\", con=con, schema=\"route\", if_exists=\"replace\", index=False\n", ")\n", "# all_hexagons.to_sql(name = 'worker_hexagon', con=con, schema = 'route', if_exists = 'replace', index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["capacity.to_sql(\n", "    name=\"capacity\", con=con, schema=\"route\", if_exists=\"replace\", index=False\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}}, "nbformat": 4, "nbformat_minor": 4}