template_name: notebook
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-orbis-common:stable
path: logistics/route_allocation/etl/preq_data_route_allocation
dag_name: preq_data_route_allocation
version: 2
owner:
  name: sang<PERSON><PERSON><PERSON>
  slack_id: U03RW2K4XBP

schedule:
  start_date: '2020-07-01T11:00:00'
  interval: '0 21 * * *'

notebook:
  parameters:
dag_type: etl
escalation_priority: low
schedule_type: fixed
sla: 120 minutes
support_files: []
namespace: logistics
tags: []
project_name: route_allocation
paused: true
executor:
  type: kubernetes
  config:
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-orbis-common:stable
    node_selectors:
      nodetype: spot
    tolerations:
    - key: service
      operator: Equal
      value: airflow
      effect: NoSchedule
    cpu:
      request: 0.5
      limit: 1
    memory:
      request: 500M
      limit: 4G
    volume_mounts:
    - name: airflow-dags
      subPath: airflow/plugins
      mountPath: /usr/local/airflow/plugins
