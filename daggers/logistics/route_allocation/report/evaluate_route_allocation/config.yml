dag_name: evaluate_route_allocation
dag_type: report
escalation_priority: low
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-orbis-common:stable
namespace: logistics
notebook:
  parameters:
owner:
  name: udit
  slack_id: US218EV1D
path: logistics/route_allocation/report/evaluate_route_allocation
paused: true
project_name: route_allocation
schedule:
  interval: 0 18 * * *
  start_date: '2020-03-04T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
executor:
  type: kubernetes
  config:
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-orbis-common:stable
    node_selectors:
      nodetype: spot
    tolerations:
    - key: service
      operator: Equal
      value: airflow
      effect: NoSchedule
    cpu:
      request: 0.5
      limit: 1
    memory:
      request: 500M
      limit: 4G
    volume_mounts:
    - name: airflow-dags
      subPath: airflow/plugins
      mountPath: /usr/local/airflow/plugins
