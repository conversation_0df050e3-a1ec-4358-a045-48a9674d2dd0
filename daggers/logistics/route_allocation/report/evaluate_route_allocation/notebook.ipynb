{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Performance evaluation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* Similarity between assigned GSPs groups\n", "* % of orders which GSPs had subsribed to \n", "* actual allocation to GSPs subscription\n", "* If not, how many new GSPs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Performance evaluation at T, sent at EOD"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import datetime\n", "\n", "red = pb.get_connection(\"redshift\")\n", "con = pb.get_connection(\"route_allocation\")\n", "\n", "import random\n", "import shapely.geometry as geometry\n", "from shapely.geometry import Point\n", "from sklearn import metrics\n", "\n", "import json\n", "from h3 import h3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["curr_date = str(datetime.datetime.utcnow().date() + datetime.timedelta(hours=5.5))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["NUM_HISTORICAL_ORDERS = 500\n", "HEX_GRID_LEVEL = 9\n", "NEW_GSP_AGE = 7\n", "ORDERS_THRESH = 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "with\n", "crates as\n", "(\n", "    select os.order_id, \n", "        sum(total_crates_count) as total_no_of_crates,\n", "        sum(cold_crates) as no_of_cold_crates\n", "    from\n", "    (\n", "        select distinct\n", "            grofers_order_id, \n", "            case when crate_ids = '' THEN 0\n", "                 else length(crate_ids) - length(replace(crate_ids,',',''))+1\n", "            end as total_crates_count,\n", "            case when crate_ids = '' THEN 0\n", "                 else REGEXP_COUNT(crate_ids, '(CBX|CLD|C1)')\n", "            end as cold_crates\n", "        from\n", "        (\n", "            select distinct \n", "                grofers_order_id,\n", "                crate_ids,\n", "                pos_timestamp, \n", "                dense_rank()over(partition by grofers_order_id order by pos_timestamp desc) as latest\n", "            from pos_invoice_summary\n", "            where invoice_type_id = 1\n", "        )pos\n", "        where pos.latest = 1 --remove duplicates\n", "    )pos\n", "    join oms_suborder os on os.id = pos.grofers_order_id\n", "    join oms_order o on os.order_id = o.id\n", "    left join oms_merchant m on o.merchant_id = m.id and  m.virtual_merchant_type = 'superstore_merchant'\n", "    where \n", "    (o.update_ts + interval '5.5 hour')::date between '{START_DATE}' and '{END_DATE}'\n", "    and os.type not like 'Digital%%'\n", "    and os.type not like 'Drop%%'\n", "    and o.current_status = 'DELIVERED'\n", "    and lower(m.city_name) not in ('not in service area')\n", "    and lower(m.city_name) not like '%%b2b%%'\n", "    and m.id not in (28759,28760,26680) -- removing load testing IDs\n", "    and lower(o.station) not like '%%dum%%'\n", "    and lower(o.station) not like '%%test%%'\n", "    and lower(o.station) not like '%%b2b%%'\n", "    and lower(o.station ) not like 'nsd'\n", "    group by 1\n", "),\n", "weight_items as\n", "(\n", "    select distinct\n", "    c.name as city,\n", "    s.name as station,\n", "    (o.delivery_slot_start_time + interval '5.5 hours')::date as delivery_date,\n", "    o.route_id as lm_route_id,\n", "    (o.updated_at+ interval '5.5 hours')::timestamp as updated_ts,\n", "    lmfe.id as lm_fe_id,\n", "    lmfe.employee_id,\n", "    case when (lmfe.employee_id like 'GSP%%' or lmfe.employee_id like 'VSP%%') then 'GSP'\n", "        when dp.id is not null then 'DP'\n", "        when lmfe.id is not null then 'GROFERS FE'\n", "        else 'UNASSIGNED'\n", "    end as fe_category,\n", "    case when lmr.route_state <> 'DELIVERED' then 'unassigned'\n", "        else case when smfe.delivery_store_id is not null then 'GSP'\n", "                when smdp.id is not null then 'DP'\n", "                when smfe.id is not null then 'DC'\n", "                when smr.id is null then 'NO SECOND MILE'\n", "                else 'UNASSIGNED'\n", "            end\n", "    end as routed_via,\n", "    smdp.name as DP_name,\n", "    case when extract(hour from (o.delivery_slot_start_time + interval '5.5 hours'))/10 < 1 then '0'||cast(extract(hour from (o.delivery_slot_start_time + interval '5.5 hours')) as text)\n", "        else cast(extract(hour from (o.delivery_slot_start_time + interval '5.5 hours')) as text) end \n", "    ||':'||\n", "    case when extract(minute from (o.delivery_slot_start_time + interval '5.5 hours'))/10 < 1 then '0'||cast(extract(minute from (o.delivery_slot_start_time + interval '5.5 hours')) as text)\n", "        else cast(extract(minute from (o.delivery_slot_start_time + interval '5.5 hours')) as text) end\n", "    ||':'||\n", "    case when extract(second from (o.delivery_slot_start_time + interval '5.5 hours'))/10 <1 then '0'||cast(extract(second from (o.delivery_slot_start_time + interval '5.5 hours')) as text)\n", "        else cast(extract(second from (o.delivery_slot_start_time + interval '5.5 hours')) as text) end \n", "    ||'-'||\n", "    case when extract(hour from (o.delivery_slot_end_time + interval '5.5 hours'))/10 < 1 then '0'||cast(extract(hour from (o.delivery_slot_end_time + interval '5.5 hours')) as text)\n", "        else cast(extract(hour from (o.delivery_slot_end_time + interval '5.5 hours')) as text) end \n", "    ||':'||\n", "    case when extract(minute from (o.delivery_slot_end_time + interval '5.5 hours'))/10 < 1 then '0'||cast(extract(minute from (o.delivery_slot_end_time + interval '5.5 hours')) as text)\n", "        else cast(extract(minute from (o.delivery_slot_end_time + interval '5.5 hours')) as text) end \n", "    ||':'||\n", "    case when extract(second from (o.delivery_slot_end_time + interval '5.5 hours'))/10 < 1 then '0'||cast(extract(second from (o.delivery_slot_end_time + interval '5.5 hours')) as text)\n", "        else cast(extract(second from (o.delivery_slot_end_time + interval '5.5 hours')) as text) end\n", "    as slot,\n", "    omc.customer_id,\n", "    oca.location_lat as customer_latitude,\n", "    oca.location_lon as customer_longitude,\n", "    o.cart_id,\n", "    oo.type as order_type,\n", "    o.external_order_id as order_id,\n", "    coalesce(sum(case when storage_type in ('REFRIGERATED','DEEP_FREEZE') then oi.quantity end),0) as no_of_cold_items,\n", "    coalesce(sum(case when handling_type = 'LARGE' then oi.quantity end),0) as no_of_large_items,\n", "    sum(case when oi.quantity > 0 and oi.total_weight/oi.quantity > 50 then cast(oi.total_weight as decimal(18,2))/1000 \n", "            else oi.total_weight end) as order_weight\n", "    from lm_order o \n", "    inner join lm_order_action oa on oa.order_id = o.id and oa.action_type = 'DELIVERED'\n", "    inner join lm_order_item oi on oi.order_id = o.id\n", "    inner join oms_order oo on o.external_order_id = oo.id\n", "    inner join oms_cart omc on omc.id = o.cart_id\n", "    left join oms_cart_address oca on oca.id = omc.address_id\n", "    left join lm_route as lmr on o.route_id = lmr.id  and lmr.route_state = 'DELIVERED'\n", "    left join lm_secondmile_route smr on smr.id = lmr.second_mile_route_id\n", "    left join lm_field_executive smfe on smr.field_executive_id = smfe.id\n", "    left join lm_distribution_point smdp on smfe.id = smdp.primary_fe_id \n", "    left join lm_field_executive lmfe on oa.field_executive_id = lmfe.id\n", "    left join lm_distribution_point dp on lmfe.id = dp.primary_fe_id \n", "    inner join lm_station s on s.id = o.station_id\n", "    inner join lm_city c on c.id = s.city_id\n", "    where oa.action_type = 'DELIVERED'\n", "    and extract(year from oa.actual_event_time) in ('2019','2020')\n", "    and (o.delivery_slot_start_time + interval '5.5 hours')::date between '{START_DATE}' and '{END_DATE}'\n", "    and oo.type not like 'Drop%%'\n", "    and oo.type not like 'Digital%%'\n", "    and oi.item_delivery_action_type <> 'RETURN'\n", "    and lower(c.name) not in ('not in service area')\n", "    and lower(c.name) not like '%%b2b%%'\n", "    and lower(s.name) not like '%%dum%%'\n", "    and lower(s.name) not like '%%test%%'\n", "    and lower(s.name) not like '%%b2b%%'\n", "    and lower(s.name) not like 'nsd'\n", "    group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17\n", "),\n", "order_level_base as\n", "(\n", "    select distinct \n", "        wi.*,\n", "        coalesce(c.total_no_of_crates,0) as total_no_of_crates,\n", "        coalesce(c.no_of_cold_crates,0) as no_of_cold_crates,\n", "        case when no_of_cold_items > 0 then 1 else 0 end as has_cold_item,\n", "        case when no_of_large_items > 0 then 1 else 0 end as has_large_item\n", "    from weight_items wi\n", "    left join crates c on wi.order_id = c.order_id\n", ")\n", "select distinct * \n", "from order_level_base\n", "where station in ({stations});\n", "\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Utility"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_table(table_name, con, query=None):\n", "    \"\"\" For reading prerequisite data from database.\"\"\"\n", "    if query == None:\n", "        temp = pd.read_sql(\n", "            sql=\"SELECT * from route_allocation.route.{};\".format(table_name), con=con\n", "        )\n", "    else:\n", "        temp = pd.read_sql(sql=query.format(table_name), con=con)\n", "\n", "    return temp"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Extracting suggested and assigned orders"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Merge of delivered and predicted orders"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fetch_data(query, curr_date, connections, pred_orders=pd.DataFrame([])):\n", "    red, con = connections\n", "    if pred_orders.empty:\n", "        print(\"yes\")\n", "        pred_orders = read_table(\n", "            \"allocated_orders\",\n", "            con,\n", "            \" Select * from route_allocation.route.{} where curr_date = '\"\n", "            + curr_date\n", "            + \"';\",\n", "        )\n", "        pred_orders.drop_duplicates([\"order_id\"], inplace=True)\n", "    orders_predicted = pred_orders.shape[0] > 0\n", "    pred_orders[\"order_id\"] = pred_orders[\"order_id\"].astype(\"str\")\n", "\n", "    if orders_predicted:\n", "        stations = list(pred_orders[\"distribution_center\"].unique())\n", "        query1 = query.format(\n", "            stations=\",\".join(repr(e) for e in stations),\n", "            START_DATE=curr_date,\n", "            END_DATE=curr_date,\n", "        )\n", "        df = pd.read_sql(sql=query1, con=red)\n", "        df.drop_duplicates([\"order_id\"], inplace=True)\n", "        df[\"slot\"] = df[\"slot\"].apply(lambda x: format_slots(x))\n", "        final_df = pd.merge(\n", "            pred_orders,\n", "            df[[\"order_id\", \"lm_fe_id\", \"station\", \"slot\"]],\n", "            left_on=[\"order_id\", \"distribution_center\", \"slot_id\"],\n", "            right_on=[\"order_id\", \"station\", \"slot\"],\n", "            how=\"inner\",\n", "        )\n", "        return final_df, orders_predicted, df, pred_orders, stations\n", "\n", "    return None, orders_predicted, None, None, None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Historic data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fetch_historic_data(query, curr_date, stations, con):\n", "    data = {}\n", "    data[\"START_DATE\"] = str(\n", "        (pd.to_datetime(curr_date) - pd.to_timedelta(30, unit=\"d\")).date()\n", "    )\n", "    data[\"END_DATE\"] = str(\n", "        (pd.to_datetime(curr_date) - pd.to_timedelta(2, unit=\"d\")).date()\n", "    )  # asssuming route making happens\n", "    # at T-2\n", "\n", "    historic_df = pd.read_sql(\n", "        sql=query.format(\n", "            stations=\",\".join(repr(e) for e in stations),\n", "            START_DATE=data[\"START_DATE\"],\n", "            END_DATE=data[\"END_DATE\"],\n", "        ),\n", "        con=con,\n", "    )\n", "\n", "    historic_df[\"customer_longitude\"] = historic_df[\"customer_longitude\"] / 1000000\n", "    historic_df[\"customer_latitude\"] = historic_df[\"customer_latitude\"] / 1000000\n", "    return historic_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Format slots"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def format_slots(x):\n", "    \"\"\" Assigns ID to a slot.\n", "    A, B, C, D, K, L are the slots which can be assigned.\"\"\"\n", "    s = x\n", "\n", "    slots = {\n", "        \"06:00:00 - 09:00:00\": \"A\",\n", "        \"10:00:00 - 13:00:00\": \"B\",\n", "        \"16:00:00 - 19:00:00\": \"C\",\n", "        \"20:00:00 - 23:00:00\": \"D\",\n", "    }\n", "    slot_start = np.array([(6 + 9), (10 + 13), (16 + 19), (20 + 23)]) / 2\n", "    arg_slot_map = {0: \"A\", 1: \"B\", 2: \"C\", 3: \"D\"}\n", "\n", "    if s in slots.keys():\n", "        return slots[s]\n", "    else:\n", "        start, end = (\n", "            int(s.split(\"-\")[0].strip().split(\":\")[0]),\n", "            int(s.split(\"-\")[1].strip().split(\":\")[0]),\n", "        )\n", "        # st is mean of start and end slot\n", "        st = (\n", "            (((24 - start) + end) / 2 + start) % 24\n", "            if start > end\n", "            else (start + end) / 2\n", "        )\n", "\n", "        if ((end >= start) & (end - start <= 3)) | (\n", "            (start > end) & ((24 - start + end) <= 3)\n", "        ):\n", "            return arg_slot_map[\n", "                np.argmin(\n", "                    np.absolute(\n", "                        list(map(lambda x: 24 - x if x > 12 else x, slot_start - st))\n", "                    )\n", "                )\n", "            ]\n", "        else:\n", "            return \"K\" if st < 14 else \"L\"\n", "\n", "    return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Hexagons utility functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_hexagons(\n", "    gsp_fe_orders, gsp_fe_data, HEX_GRID_LEVEL, NEW_GSP_AGE, ORDERS_THRESH\n", "):\n", "    \"\"\" Creates hexagons for GSPs historical data. \"\"\"\n", "    test = pd.DataFrame([])\n", "    test[\"gsps\"] = list(gsp_fe_orders.keys())\n", "    test[\"geo\"] = test[\"gsps\"]\n", "    test[\"geo\"] = test[\"geo\"].map(gsp_fe_orders)\n", "    test.index = test[\"gsps\"]\n", "    test = (\n", "        test.apply(lambda x: pd.Series(x[\"geo\"].tolist()), axis=1)\n", "        .stack()\n", "        .reset_index(level=1, drop=True)\n", "        .reset_index()\n", "    )\n", "    test.columns = [\"GSP ID\", \"Past order\"]\n", "    test[\"customer_latitude\"] = test[\"Past order\"].apply(lambda x: x[0])\n", "    test[\"customer_longitude\"] = test[\"Past order\"].apply(lambda x: x[1])\n", "    test.drop([\"Past order\"], axis=1, inplace=True)\n", "    test[\"hexagon_index\"] = test.apply(\n", "        lambda x: h3.geo_to_h3(\n", "            x[\"customer_latitude\"], x[\"customer_longitude\"], HEX_GRID_LEVEL\n", "        ),\n", "        axis=1,\n", "    )\n", "    hex_density = test.groupby([\"GSP ID\", \"hexagon_index\"]).count()\n", "    hex_density.reset_index(inplace=True)\n", "\n", "    hexagons = list(\n", "        hex_density[\"hexagon_index\"].unique()\n", "    )  # Hexagons allocated to the GSPs\n", "\n", "    gsp_hex_relation = {}\n", "\n", "    for hexi in hexagons:\n", "        temp_hex = hex_density[hex_density[\"hexagon_index\"] == hexi]\n", "        temp_hex.reset_index(inplace=True, drop=True)\n", "        temp_dict = []\n", "        for i in range(temp_hex.shape[0]):\n", "            age_gsp = (\n", "                gsp_fe_data[gsp_fe_data[\"lm_fe_id\"] == temp_hex[\"GSP ID\"][i]]\n", "                .groupby([\"delivery_date\"])\n", "                .count()\n", "                .shape[0]\n", "            )\n", "            if (age_gsp > NEW_GSP_AGE) & (\n", "                temp_hex[\"customer_latitude\"][i] > ORDERS_THRESH\n", "            ):  # Delivered more than 2 orders for experienced FE\n", "                temp_dict.append(temp_hex[\"GSP ID\"][i])  # Correct this, it\n", "            elif age_gsp <= NEW_GSP_AGE:\n", "                temp_dict.append(temp_hex[\"GSP ID\"][i])\n", "        if len(temp_dict) != 0:\n", "            gsp_hex_relation[hexi] = temp_dict\n", "\n", "    return gsp_hex_relation, hex_density"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def hex_evaluation(temp, temp1, temp2, gsp_hex_relation):\n", "    for index, val in temp.iterrows():\n", "        hexi = h3.geo_to_h3(float(val[\"lat\"]), float(val[\"long\"]), HEX_GRID_LEVEL)\n", "        if hexi in gsp_hex_relation.keys():\n", "            num = gsp_hex_relation[hexi]\n", "\n", "            allocated = int(val[\"allocated_gsp\"])\n", "            assigned = int(val[\"lm_fe_id\"])\n", "            if (allocated in num) & (assigned in num) & (allocated == assigned):\n", "                temp.loc[\n", "                    index, \"result\"\n", "                ] = \"Same hexagon Same GSP\"  # this might not be the exact time\n", "            elif (\n", "                (allocated in num) & (assigned in num) & (allocated != assigned)\n", "            ):  # & (allocated in temp2):\n", "                temp.loc[index, \"result\"] = \"Same hexagon different GSP\"\n", "            elif (allocated not in num) & (\n", "                assigned in num\n", "            ):  # & (assigned in temp1): # the assigned was in the list\n", "                temp.loc[index, \"result\"] = \"Wrong suggestion\"\n", "            elif (allocated in num) & (assigned not in num):  # & (allocated in temp2):\n", "                temp.loc[index, \"result\"] = \"Wrong assignment\"\n", "            elif (allocated not in num) & (assigned not in num):\n", "                temp.loc[index, \"result\"] = \"Both wrong\"\n", "\n", "        else:\n", "            temp.loc[index, \"result\"] = \"new region\"\n", "    return temp"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Do order allocation first and then run the loop for morning and evening both"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Metric calculation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["connections = [red, con]\n", "final_df, orders_predicted, df, pred_orders, stations = fetch_data(\n", "    query, curr_date, connections\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["final_data_ops is the data sent to the operations people whiel final_data is circulated internally."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if orders_predicted:\n", "    historic_df = fetch_historic_data(query, curr_date, stations, connections[0])\n", "    final_data = pd.DataFrame([])\n", "    final_data_ops = pd.DataFrame([])\n", "    for station in stations:\n", "        # Mutual adjustment metric\n", "        temp_pre = final_df[final_df[\"distribution_center\"] == station]\n", "        temp_pre[\"shift\"] = None\n", "        temp_pre.loc[temp_pre[\"slot_id\"].isin([\"A\", \"B\", \"K\"]), \"shift\"] = \"morning\"\n", "        temp_pre.loc[temp_pre[\"slot_id\"].isin([\"C\", \"D\", \"L\"]), \"shift\"] = \"evening\"\n", "\n", "        for shift in [\"morning\", \"evening\"]:\n", "            data, data_ops = pd.DataFrame([]), pd.DataFrame([])\n", "            temp = temp_pre[temp_pre[\"shift\"] == shift]  # station and shiftwise info\n", "            if shift == \"morning\":\n", "                slots = [\"A\", \"B\", \"K\"]\n", "            else:\n", "                slots = [\"C\", \"D\", \"L\"]\n", "\n", "            if temp.shape[0] > 0:\n", "                data_ops.loc[0, \"Delivered orders\"] = df[\n", "                    (df[\"station\"] == station) & (df[\"slot\"].isin(slots))\n", "                ].shape[0]\n", "                data_ops.loc[0, \"Predicted orders\"] = pred_orders[\n", "                    (pred_orders[\"distribution_center\"] == station)\n", "                    & (pred_orders[\"slot_id\"].isin(slots))\n", "                ].shape[0]\n", "\n", "                data.loc[0, \"count\"] = temp.shape[0]\n", "                # data_ops.loc[0, \"Predicted and delivered orders\"] = temp.shape[0]\n", "\n", "                data.loc[0, \"station\"] = station\n", "                data_ops.loc[0, \"station\"] = station\n", "\n", "                data.loc[0, \"shift\"] = shift\n", "                data_ops.loc[0, \"shift\"] = shift\n", "                # exact match percentage\n", "                count = 0\n", "                for index, val in temp.iterrows():\n", "                    if int(val[\"allocated_gsp\"]) == val[\"lm_fe_id\"]:\n", "                        count += 1\n", "                exact_match = count / temp.shape[0]\n", "                data.loc[0, \"exact_match\"] = str(int(np.round(exact_match * 100))) + \"%\"\n", "                data_ops.loc[0, \"match_suggestion_vs_actual\"] = (\n", "                    str(int(np.round(exact_match * 100))) + \"%\"\n", "                )\n", "\n", "                temp1 = list(map(int, temp[\"allocated_gsp\"].unique()))\n", "                temp2 = list(temp[\"lm_fe_id\"].unique())\n", "\n", "                combined = temp1.copy()\n", "                combined.extend(temp2)\n", "                combined = set(combined)\n", "\n", "                # data_ops.loc[0, \"Predicted workers (count)\"] = len(temp1)\n", "                # num = 0\n", "                # for i in temp1:\n", "                # if i in temp2:\n", "                # num += 1\n", "\n", "                # data_ops.loc[0, \"% workers delivered (count)\"] = (\n", "                # str(np.round((num / len(temp1)) * 100)) + \"%\"\n", "                # )\n", "\n", "                data.loc[0, \"Suggested_not_delivered\"] = int(\n", "                    len(list(set(temp1) - set(temp2)))\n", "                )\n", "                data.loc[0, \"delivered_not_Suggested\"] = int(\n", "                    len(list(set(temp2) - set(temp1)))\n", "                )\n", "\n", "                pred_gsps = list(\n", "                    pred_orders[\n", "                        (pred_orders[\"distribution_center\"] == station)\n", "                        & (pred_orders[\"slot_id\"].isin(slots))\n", "                    ][\"allocated_gsp\"].unique()\n", "                )\n", "                delivered_gsps = list(\n", "                    df[(df[\"station\"] == station) & (df[\"slot\"].isin(slots))][\n", "                        \"lm_fe_id\"\n", "                    ].unique()\n", "                )\n", "\n", "                data_ops.loc[0, \"no_users_Predicted_not_delivered\"] = int(\n", "                    len(list(set(pred_gsps) - set(delivered_gsps)))\n", "                )\n", "                data_ops.loc[0, \"no_users_delivered_not_predicted\"] = int(\n", "                    len(list(set(delivered_gsps) - set(pred_gsps)))\n", "                )\n", "\n", "                historic_temp = historic_df[historic_df[\"station\"] == station]\n", "                list_of_workers = historic_temp[\"lm_fe_id\"].unique()\n", "\n", "                category = {}  # extracting GROFERS_FE, GSP category\n", "                for i in combined:\n", "                    if df[df[\"lm_fe_id\"] == i].shape[0] > 0:\n", "                        #                         print('Category in last day: ',i)\n", "                        category[i] = df[df[\"lm_fe_id\"] == i][\"fe_category\"].iloc[0]\n", "                    else:\n", "                        #                         print('Category in history: ',i)\n", "                        category[i] = historic_temp[historic_temp[\"lm_fe_id\"] == i][\n", "                            \"fe_category\"\n", "                        ].iloc[0]\n", "\n", "                new_workers = []\n", "                for i in temp2:\n", "                    if i not in list_of_workers:\n", "                        new_workers.append(i)\n", "\n", "                data.loc[0, \"Number of new workers\"] = int(len(new_workers))\n", "\n", "                historic_temp = historic_temp[\n", "                    historic_temp[\"lm_fe_id\"].isin(temp1)\n", "                ]  # because we want hexagons that we allocated\n", "                gsp_fe_orders = {}\n", "                for i in temp1:  # Computing hexagons for own evelaution\n", "                    temp_inner = historic_temp[historic_temp[\"lm_fe_id\"] == i]\n", "                    if (\n", "                        temp_inner.shape[0] > 0\n", "                    ):  # Make sure no new GSPs are in past data, their distance metric will always be zero\n", "                        gsp_fe_orders[i] = (\n", "                            temp_inner.sort_values([\"updated_ts\"], ascending=False)\n", "                            .reset_index(drop=True)\n", "                            .iloc[:NUM_HISTORICAL_ORDERS, :][\n", "                                [\"customer_latitude\", \"customer_longitude\"]\n", "                            ]\n", "                            .as_matrix()\n", "                        )\n", "\n", "                gsp_hex_relation, hex_density = create_hexagons(\n", "                    gsp_fe_orders,\n", "                    historic_temp,\n", "                    HEX_GRID_LEVEL,\n", "                    NEW_GSP_AGE,\n", "                    ORDERS_THRESH,\n", "                )\n", "\n", "                temp = hex_evaluation(temp, temp1, temp2, gsp_hex_relation)\n", "                print(\"Geographich region performance\")\n", "                print(\"Available:\")\n", "                for i, j in temp[\"result\"].value_counts().items():\n", "                    data.loc[0, i] = str(int(np.round((j / temp.shape[0]) * 100))) + \"%\"\n", "                print((temp[\"result\"].value_counts() / temp.shape[0]) * 100)\n", "\n", "                temp[\"allocated_gsp\"] = temp[\"allocated_gsp\"].astype(\"int\")\n", "                temp[\"allocated_category\"] = temp[\"allocated_gsp\"].map(category)\n", "                temp[\"assigned_category\"] = temp[\"lm_fe_id\"].map(category)\n", "                print(data)\n", "                final_data = pd.concat([final_data, data])\n", "                final_data_ops = pd.concat([final_data_ops, data_ops])\n", "else:\n", "    print(\"No order allocated\")\n", "    # can add capacity metric too"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_ops"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data = final_data.drop([\"count\"], axis=1)\n", "cols = final_data.columns.to_list()\n", "final_data = final_data[\n", "    [cols[0]]\n", "    + cols[2:4]\n", "    + cols[5:7]\n", "    + [cols[9]]\n", "    + [cols[1]]\n", "    + [cols[4]]\n", "    + cols[7:9]\n", "    + cols[10:12]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Email"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Internal Email"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "to_email = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "subject = \"GSP allocation performance report for delivery date \" + datetime.datetime.strptime(\n", "    curr_date, \"%Y-%m-%d\"\n", ").strftime(\n", "    \"%d/%m/%Y\"\n", ")\n", "html_content = \"\"\"\n", "Hi<br>Please find attached the performance report.\n", "This report evaluates the performance of the GSP route allocation with respect to GSP/FEs allocated by SM.<br><br>\n", "<strong>New workers</strong>: Workers who are new and were not in the DB at or before T-2 but were allocated orders by the station (why? because we take T-2 attendance as proxy)\n", "<br><strong>Pred_not_delivered</strong>: Numbers of workers which were suggested but were not assigned any order by the station (to track difference between attendance)\n", "<br><strong>delivered_not_pred</strong>: Number of workers which were assigned order by station but were not suggested (again, to track difference between attendance)\n", "<br><strong>Same hexagon Same GSP</strong>: Order suggested and assigned to the same worker and the worker falls in the order’s hexagon.\n", "<br><strong>Same hexagon different GSP</strong>: Both suggested worker and assigned worker have subscribed to the order’s hexagon, however they are not the same.\n", "<br><strong>Wrong assignment</strong>: Suggested worker subscribed to order’s hexagon while assigned worker has not subscribed.\n", "<br><strong>Wrong prediction</strong>: Suggested worker not subscribed to order’s hexagon while assigned has subscribed.\n", "<br><strong>New region</strong>: The order has fallen in an untraversed hexagon for that station.\n", "<br><strong>Both wrong</strong>: Both assigned and suggested workers have not subscribed to the orders.\n", "\n", "The percentages till <strong>new region</strong> will add up, exact match column is seperately calculated, so don't count that in total percentage.\n", "\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Operations email"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email_ops = \"<EMAIL>\"\n", "to_email_ops = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "subject_ops = \"GSP allocation statistics for delivery date \" + datetime.datetime.strptime(\n", "    curr_date, \"%Y-%m-%d\"\n", ").strftime(\n", "    \"%d/%m/%Y\"\n", ")\n", "html_content_ops = \"\"\"\n", "Hi<br>Please find attached the statistics.<br><br>\n", "<strong>Delivered orders</strong>: The number of orders delivered.\n", "<br><strong>Predicted orders</strong>: The number of orders allocated by the GSP allocation algorithm.\n", "<br><strong>match_suggestion_vs_actual</strong>: Percentage of orders (which were allocated and delivered) which had same predicted (by GSP allocation) and assigned (by SM) GSP.\n", "<br><strong>no_users_Predicted_not_delivered</strong>: Number of workers who were given orders by GSP allocation algorithm, but who did not deliver.\n", "<br><strong>no_users_delivered_not_predicted</strong>: Number of workers who delivered orders, but were not predicted by GSP allocation algorithm because future attendence is unknown. \n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if orders_predicted:\n", "    final_data[\"Number of new workers\"] = final_data[\"Number of new workers\"].astype(\n", "        \"int\"\n", "    )\n", "    final_data[\"Suggested_not_delivered\"] = final_data[\n", "        \"Suggested_not_delivered\"\n", "    ].astype(\"int\")\n", "    final_data[\"delivered_not_Suggested\"] = final_data[\n", "        \"delivered_not_Suggested\"\n", "    ].astype(\"int\")\n", "    final_data.fillna(value=\"0%\", axis=1, inplace=True)\n", "    filepath = pb.to_csv(final_data, \"metrics.csv\")\n", "\n", "    pb.send_email(\n", "        from_email, to_email, subject, html_content, files=[\"/tmp/metrics.csv\"]\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if orders_predicted:\n", "    final_data_ops = final_data_ops[\n", "        final_data_ops[\"station\"].isin([\"HR22\", \"BA18\", \"KOL8\"])\n", "    ]\n", "    final_data_ops.fillna(value=\"0%\", axis=1, inplace=True)\n", "    x = final_data_ops.copy()\n", "    x[\"match_suggestion_vs_actual\"] = x[\"match_suggestion_vs_actual\"].apply(\n", "        lambda x: int(x[0:-1])\n", "    )\n", "    sorter = list(\n", "        x.groupby(\"station\", as_index=False)[\"match_suggestion_vs_actual\"]\n", "        .mean()\n", "        .sort_values(\"match_suggestion_vs_actual\", ascending=False)[\"station\"]\n", "    )\n", "    sorr = dict(zip(sorter, range(len(sorter))))\n", "    final_data_ops[\"sortt\"] = final_data_ops[\"station\"].apply(lambda x: sorr[x])\n", "    final_data_ops = final_data_ops.sort_values(by=[\"sortt\", \"shift\"]).drop(\n", "        [\"sortt\"], axis=1\n", "    )\n", "    final_data_ops = final_data_ops[\n", "        [\n", "            \"station\",\n", "            \"shift\",\n", "            \"Predicted orders\",\n", "            \"Delivered orders\",\n", "            \"no_users_Predicted_not_delivered\",\n", "            \"no_users_delivered_not_predicted\",\n", "            \"match_suggestion_vs_actual\",\n", "        ]\n", "    ]\n", "    final_data_ops[\"Predicted orders\"] = final_data_ops[\"Predicted orders\"].astype(int)\n", "    final_data_ops[\"Delivered orders\"] = final_data_ops[\"Delivered orders\"].astype(int)\n", "    final_data_ops[\"no_users_Predicted_not_delivered\"] = final_data_ops[\n", "        \"no_users_Predicted_not_delivered\"\n", "    ].astype(int)\n", "    final_data_ops[\"no_users_delivered_not_predicted\"] = final_data_ops[\n", "        \"no_users_delivered_not_predicted\"\n", "    ].astype(int)\n", "    final_data_ops = final_data_ops.reset_index(drop=True)\n", "    filepath = pb.to_csv(final_data_ops, \"metrics_ops.csv\")\n", "    html = final_data_ops.to_html()\n", "    html_content_ops += html\n", "    pb.send_email(\n", "        from_email_ops,\n", "        to_email_ops,\n", "        subject_ops,\n", "        html_content_ops,\n", "        files=[\"/tmp/metrics_ops.csv\"],\n", "    )"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.1"}}, "nbformat": 4, "nbformat_minor": 4}