alert_configs:
  slack:
  - channel: bl-cpo-critical-dag-failure-alert
dag_name: b_gigs_rc_v5
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: logistics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U082DBVETNX
path: logistics/b_gigs/etl/b_gigs_rc_v5
paused: false
pool: logistics_pool
project_name: b_gigs
schedule:
  end_date: '2025-08-26T00:00:00'
  interval: 45 2,13 * * *
  start_date: '2025-06-23T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 5
