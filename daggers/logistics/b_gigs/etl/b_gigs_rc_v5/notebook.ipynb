{"cells": [{"cell_type": "code", "execution_count": null, "id": "69af8980-6bba-4cd5-a70d-413e1dbd6b73", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import pencilbox as pb\n", "import numpy as np\n", "import itertools\n", "import datetime as dt\n", "from datetime import datetime\n", "from datetime import date\n", "from datetime import timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "6872b163-5189-4603-86a9-7cee44eb0918", "metadata": {}, "outputs": [], "source": ["sql_query = f\"\"\"\n", "with store_base as(\n", "Select cast(entity_id as int) store_id,cast(max_by(new_model_mix,dt) as double) new_model_mix from logistics_data_etls.b_gigs_model_conversion_input\n", "where dd_approval = 'Yes'\n", "group by 1\n", "),\n", "\n", "gigs_base as (\n", "SELECT * FROM \n", "(SELECT distinct store_id,store_name,slot_id,slot_name,account_date,dow,carrier_type,coalesce(planned_util,2) planned_util,\n", "greatest(coalesce(projected_orders,1),1) projected_orders,final_cap_req,cap_push,cap_push_post_avp_buffer,start_time_seconds_since_midnight/3600 as start_hour,\n", "(end_time_seconds_since_midnight/3600)-1 as end_hour,rank() over(partition by store_id,slot_id,account_date,carrier_type order by dt desc) ranking \n", "FROM logistics_data_etls.b_gigs_capacity_planning_v2 c\n", "left join zomato.driver_shift.slots s on c.slot_id=s.id\n", "WHERE dt>=date_format(current_date - interval '7' day, '%%Y%%m%%d')\n", "AND account_date>=current_date+interval '0' day \n", ")\n", "WHERE ranking=1\n", "AND store_id in (Select store_id from store_base)),\n", "\n", "cpk_input as (\n", "SELECT try_cast(b.store_id as int) store_id,try_cast(b.carrier_id as int) carrier_id,try_cast(max_by(weekly_cpk,dt) as double) as weekly_cpk\n", "FROM logistics_data_etls.b_temp_cpk_sheet b\n", "WHERE dt>'********'\n", "GROUP BY 1,2),\n", "\n", "dd_planned as (SELECT account_date,store_id,slot_id,max_by(dd,dt) planned_dd \n", "FROM logistics_data_etls.b_gigs_planned_dd_v2\n", "WHERE dt>=date_format(current_date - interval '7' day, '%%Y%%m%%d') GROUP BY 1,2,3),\n", "\n", "dd_final_slot as (\n", "\n", "select store_id , slot_id , account_date , case when id is not null then c_projected_orders else projected_orders end as projected_orders   , \n", "case when id is not null then c_planned_dd * 1.00 / c_projected_orders else planned_dd end as planned_dd  \n", "from\n", "(select a.store_id , slot_id , account_date , projected_orders , planned_dd , \n", "sum(case when id is not null then  projected_orders end ) over (partition by id , slot_id , account_date ) c_projected_orders , \n", "sum(case when id is not null then  planned_dd * projected_orders end) over (partition by id , slot_id , account_date ) c_planned_dd , \n", "id\n", "from \n", "(\n", "SELECT gb.store_id,gb.slot_id,gb.account_date,projected_orders,coalesce(de.planned_dd,coalesce(greatest(1,dp.planned_dd),2)) planned_dd\n", "FROM (SELECT store_id,slot_id,account_date,projected_orders FROM gigs_base WHERE carrier_type=1 GROUP BY 1,2,3,4) gb\n", "LEFT JOIN dd_planned dp ON gb.store_id=dp.store_id AND gb.account_date=dp.account_date AND gb.slot_id=dp.slot_id\n", "LEFT JOIN (SELECT try_cast(entity_id as int) entity_id,try_cast(planned_dd as double) planned_dd FROM logistics_data_etls.b_gigs_exception \n", "WHERE dd_exception='Yes' AND try_cast(entity_id as int)>0 AND entity_type='Store') de ON gb.store_id=try_cast(de.entity_id as int)\n", "GROUP BY 1,2,3,4,5\n", ") a\n", "left join logistics_data_etls.store_in_store_mapping_v1 b on a.store_id = b.store_id \n", ")\n", "\n", "\n", "),\n", "\n", "dd_final_week as (SELECT store_id,sum(projected_orders) projected_orders,sum(projected_orders*planned_dd)*1.00/sum(projected_orders) projected_dd\n", "FROM dd_final_slot WHERE account_date between current_date+interval '1' day AND current_date+interval '7' day GROUP BY 1),\n", "\n", "--SELECT * FROM dd_final_week\n", "\n", "\n", "--SELECT store_id,slot_id,account_date,planned_dd FROM dd_final_slot GROUP BY 1,2,3,4\n", "\n", "cpo_input as (SELECT ci.store_id,carrier_id,weekly_cpk,\n", "case when carrier_id=3 then weekly_cpk*coalesce(projected_dd,2)*1.00/1.5 ELSE weekly_cpk*coalesce(projected_dd,2)*1.00 END as weekly_cpo , projected_dd\n", "FROM cpk_input ci\n", "LEFT JOIN dd_final_week dfw ON ci.store_id=dfw.store_id GROUP BY 1,2,3,4,5),\n", "\n", "milestone_buffer as (SELECT store_id,carrier_id,try_cast(max_by(cpo_buffer,dt) as double) cpo_buffer\n", "FROM zomato.blinkit_etls.b_gigs_milestone_ver1\n", "WHERE dt>'********'\n", "GROUP BY 1,2),\n", "\n", "ldrp_cpo as (SELECT z_store_id,carrier_id,least(ldrp_cpo*multiple,3) ldrp_cpo\n", "FROM \n", "(SELECT least((extract(week from current_date)+52-40)*0.20,1.00) multiple, z_store_id,carrier_id,sum(ldrp_amount) ldrp_amount,sum(acc_orders) acc_orders,\n", "try(sum(ldrp_amount)*1.00/greatest(1,sum(acc_orders))) ldrp_cpo\n", "FROM logistics_data_etls.blinkit_rider_daily_accounting_summary_v1\n", "WHERE account_date between current_date-interval '14' day AND current_date-interval '1' day\n", "AND dt>=date_format(current_date - interval '15' day, '%%Y%%m%%d')\n", "GROUP BY 1,2,3)\n", "GROUP BY 1,2,3),\n", "\n", "cancellation_factor as (SELECT z_store_id,multiple, coalesce(least((1-cancellation_factor)*multiple,1-cancellation_factor),0.02) cancellation_factor\n", "FROM (SELECT z_store_id,least((extract(week from current_date)+52-45)*0.25,1.00)  multiple,\n", "greatest(coalesce(delivered_orders,1),1)*1.00/greatest(coalesce(acc_orders,1),1) cancellation_factor\n", "FROM \n", "(SELECT z_store_id,sum(acc_orders) acc_orders,sum(delivered_orders) delivered_orders\n", "FROM logistics_data_etls.blinkit_rider_daily_accounting_summary_v1\n", "WHERE account_date between current_date-interval '14' day AND current_date-interval '1' day\n", "GROUP BY 1)\n", "WHERE z_store_id IS NOT NULL)),\n", "\n", "ming_cpo as (SELECT z_store_id,carrier_id,greatest(ming_cpo*multiple,0.03) ming_cpo\n", "FROM \n", "(SELECT 1.0 multiple, z_store_id,carrier_id,sum(trip_min_guarantee) ming_amount,sum(acc_orders) acc_orders,\n", "case when sum(acc_orders) > 5 then try(sum(trip_min_guarantee)*1.00/sum(acc_orders))\n", "else 0.03 end as ming_cpo\n", "FROM logistics_data_etls.blinkit_rider_daily_accounting_summary_v1\n", "WHERE account_date between current_date-interval '14' day AND current_date-interval '1' day\n", "AND dt>=date_format(current_date - interval '15' day, '%%Y%%m%%d')\n", "GROUP BY 1,2,3)\n", "GROUP BY 1,2,3),\n", "\n", "cpo_plan as\n", "(select *, max(weekly_cpo) over (partition by store_id) as bike_cpo\n", "from ( SELECT distinct store_id,carrier_id,(((1-cpo_buffer)*weekly_cpo)-ldrp_cpo-ming_cpo)*(1-coalesce(cancellation_factor,0.02)) weekly_cpo , projected_dd\n", "FROM (select b.store_id,b.carrier_id,coalesce(cpo_buffer,0.05) cpo_buffer,coalesce(ldrp_cpo,0.25) ldrp_cpo,coalesce(cancellation_factor,0.02*cf.multiple) cancellation_factor,\n", "     coalesce(ming_cpo,0.03) ming_cpo,weekly_cpo , projected_dd\n", "from cpo_input b\n", "LEFT JOIN milestone_buffer mf ON b.store_id=try_cast(mf.store_id as int) AND b.carrier_id=try_cast(mf.carrier_id as int)\n", "LEFT JOIN ldrp_cpo ON b.store_id=ldrp_cpo.z_store_id AND b.carrier_id=ldrp_cpo.carrier_id \n", "LEFT JOIN cancellation_factor cf on b.store_id=cf.z_store_id\n", "LEFT JOIN ming_cpo m on  b.store_id = m.z_store_id  and b.carrier_id = m.carrier_id\n", "--where dt>'********'\n", "group by 1,2,3,4,5,6,7 , 8\n", "))),\n", "skew_input_city as\n", "(SELECT entity_name,entity_type,dow,cast(slot_id as int) slot_id,avg(cast(coalesce(skew_factor,1) as double)) skew_factor\n", "FROM \n", "(select entity_name,entity_type,cast(bc.dow as int) dow, hour,slot_id, max_by(cpk_factor,dt) as skew_factor\n", "from logistics_data_etls.new_city_skew_v1 bc\n", "left join (SELECT slot_id,min(cast(hour as int)) start_hour,max(cast(hour as int)) end_hour\n", "FROM zomato.jumbo_external.b_gigs_input_ver2\n", "WHERE dt>'********' \n", "GROUP BY 1) gb ON cast(bc.hour as int) between gb.start_hour and gb.end_hour\n", "where dt>'********' AND entity_type='City'\n", "group by 1,2,3,4,5\n", ")\n", "GROUP BY 1,2,3,4\n", ")\n", ",\n", "\n", "skew_input_store as\n", "(SELECT entity_name,entity_type,dow,cast(slot_id as int) slot_id,avg(cast(coalesce(skew_factor,1) as double)) skew_factor\n", "FROM \n", "(select bc.entity_name,entity_type,cast(bc.dow as int) dow, hour,slot_id, max_by(cpk_factor,dt) as skew_factor\n", "from logistics_data_etls.new_city_skew_v1 bc\n", "INNER JOIN (SELECT entity_id from logistics_data_etls.b_gigs_exception \n", "WHERE store_skew_exception='Yes' AND entity_type='Store' GROUP BY 1) e ON bc.entity_name=cast(e.entity_id as integer)\n", "left join (SELECT slot_id,min(cast(hour as int)) start_hour,max(cast(hour as int)) end_hour\n", "FROM zomato.jumbo_external.b_gigs_input_ver2\n", "WHERE dt>'********' \n", "GROUP BY 1) gb ON cast(bc.hour as int) between gb.start_hour and gb.end_hour\n", "where dt>'********' AND entity_type='Store'\n", "group by 1,2,3,4,5\n", ")\n", "GROUP BY 1,2,3,4\n", ")\n", ",\n", "join_cpo as\n", "(\n", "select c.*, weekly_cpo, bike_cpo,c.account_date date1 , projected_dd\n", "from gigs_base c\n", "left join cpo_plan p on c.store_id=cast(p.store_id as int) and cast(p.carrier_id as int)=c.carrier_type\n", "),\n", "store_city as\n", "(\n", "select distinct c.store_id entity_id,sm.city_id as city_id ,runnr_city as city\n", " from gigs_base c\n", " left join logistics_data_etls.blinkit_store_mapping sm on sm.store_id = c.store_id\n", "),\n", "\n", "base_joins as\n", "(\n", "\n", "select j.slot_id, j.carrier_type as carrier_id, j.store_id as store_id, j.store_name, cs.city,bike_cpo, j.slot_name,\n", "date1, start_hour, end_hour, cast(weekly_cpo as double) as weekly_cpo, j.dow, coalesce(df.projected_orders , j.projected_orders)  as slot_orders,\n", "coalesce(sk1.skew_factor,sk2.skew_factor,sk3.skew_factor) skew_factor,\n", "cap_push_post_avp_buffer as cap_req , \n", "planned_dd , projected_dd\n", "from join_cpo j\n", "left join store_city cs on cs.entity_id=j.store_id\n", "left join skew_input_store sk1 on sk1.entity_name=j.store_id and sk1.slot_id=j.slot_id and cast(sk1.dow as int)=extract(dow from j.date1)\n", "left join skew_input_city sk2 on sk2.entity_name=cs.city_id\n", "and sk2.slot_id=j.slot_id and cast(sk2.dow as int)=extract(dow from j.date1)\n", "LEFT JOIN (select * from skew_input_city where entity_name = 4 ) sk3\n", "ON sk3.slot_id = j.slot_id \n", "   AND CAST(sk3.dow AS INT) = EXTRACT(DOW FROM j.date1) \n", "left join dd_final_slot df on df.account_Date = j.date1 and df.slot_id = j.slot_id and df.store_id  = j.store_id \n", "where date1 between current_date+interval '0' day and current_date + interval '10' day\n", "and cast(weekly_cpo as double)>0 and j.projected_orders>0\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17\n", "\n", "),\n", "\n", "slot_cpo_skew as\n", "(select distinct store_id c_store_id,\n", "sum(weekly_cpo*skew_factor*slot_orders) over (partition by b.store_id)*1.00 / sum(weekly_cpo*slot_orders) over (partition by b.store_id) as correction_factor\n", "from base_joins b\n", "WHERE carrier_id=1 AND date1 between current_date+interval '1' day AND current_date+interval '7' day)\n", "\n", "\n", ",slot_cpo as\n", "(\n", "select b.*, weekly_cpo*skew_factor as slot_base_cpo,\n", "weekly_cpo*skew_factor*slot_orders as slot_base_earning, \n", "weekly_cpo*slot_orders as weekly_earning,\n", "correction_factor,\n", "skew_factor*1.00/correction_factor as corrected_slot_factor,\n", "weekly_cpo* skew_factor *1.00 / correction_factor as final_slot_cpo,\n", "greatest(coalesce(coalesce(planned_dd,projected_dd),2.00),1.00) as past_drop_distance, b.store_id+dow+b.slot_id+extract(week from date1)+carrier_id as rand,\n", "mod(b.store_id+dow+b.slot_id+extract(week from date1)+carrier_id,least(floor(bike_cpo/4.5),10))/100 as base_var\n", "from base_joins b\n", "LEFT JOIN slot_cpo_skew c on b.store_id=c.c_store_id\n", "),\n", "\n", "\n", "final as (\n", "SELECT *,(final_slot_cpo-base_pay) distance_pay,\n", "least(greatest((final_slot_cpo-base_pay)*1.00/(case when carrier_id=3 then greatest(past_drop_distance*1.00/1.5,1) else past_drop_distance end),4.00),40.00) as dist_pay_per_km\n", "FROM \n", "(\n", "select s.*,new_model_mix,\n", "case WHEN carrier_id IN (1,6) then \n", "    greatest(((0.20*new_model_mix)+greatest(0,(1-new_model_mix)*0.5)+base_var)*bike_cpo,4)\n", "    --- fixing minimum base_pay value = 4 \n", "when carrier_id=3 then greatest(((0.25*new_model_mix)+greatest(0,(1-new_model_mix)*0.5)+base_var)*bike_cpo,4)\n", "    else greatest(((0.20*new_model_mix)+greatest(0,(1-new_model_mix)*0.5)+base_var)*bike_cpo,4) end base_pay\n", "from slot_cpo s\n", "left join store_base sb on s.store_id = sb.store_id\n", ")\n", "),\n", "\n", "incentive_central_dow as (SELECT extract(dow from account_date) dow,store_id,slot_name,\n", "max_by(incentive_multiplier_post_checks,dt) incentive_multiplier_post_checks\n", "FROM logistics_data_etls.b_gigs_incentive_multiplier_week_v1\n", "WHERE dt>=date_format(current_date - interval '2' day, '%%Y%%m%%d')\n", "AND account_date between current_date+interval '1' day AND current_date+interval '7' day\n", "GROUP BY 1,2,3),\n", "\n", "\n", "\n", "incentive_city as (SELECT store_id,slot_name,slot_id,account_date,max(dinner_incentive) dinner_incentive,max(incentive_multiplier) incentive_multiplier FROM\n", "(SELECT store_name,d.store_id,hour,slot_name,slot_id,account_date,dinner_incentive,max(incentive_multiplier) incentive_multiplier\n", "FROM blinkit.logistics_data_etls.b_gigs_design_ver3 d\n", "INNER JOIN (SELECT try_cast(store_id as int) store_id,\n", "try_cast(start_hour as int) start_hour,\n", "try_cast(end_hour as int) end_hour,\n", "(start_date) start_date,(end_date) end_date,try_cast(incentive_multiplier as int) incentive_multiplier,dinner_incentive\n", "FROM logistics_data_etls.b_gigs_incentive_plan_v3\n", "WHERE approval='Yes' AND incentive_multiplier is not null\n", "GROUP BY 1,2,3,4,5,6,7) c\n", "ON d.hour between c.start_hour and (c.end_hour-1)\n", "AND date_format(d.account_date, '%%Y%%m%%d') BETWEEN c.start_date AND c.end_date\n", "AND c.store_id=d.store_id\n", "WHERE d.account_date>=current_date AND account_date<=current_date+interval '10' day\n", "AND d.dt=date_format(current_date - interval '1' day, '%%Y%%m%%d')\n", "GROUP BY 1,2,3,4,5,6,7)\n", "WHERE incentive_multiplier IS NOT NULL\n", "GROUP BY 1,2,3,4),\n", "\n", "\n", "pay_buffer as (SELECT store_id,max(try_cast(dd_buffer_flex as double)) dd_buffer_flex,max(try_cast(base_buffer_flex as double)) base_buffer_flex\n", "FROM logistics_data_etls.b_gigs_pay_buffer GROUP BY 1),\n", "\n", "ming_city as \n", "(Select entity_name as city_name,cast(ming_input as double) ming_input\n", " from logistics_data_etls.b_gigs_model_conversion_input\n", " where ming_approval = 'Yes'\n", " and entity_type = 'City'),\n", "\n", "ming_store as \n", "(Select cast(entity_id as bigint) store_id,cast(ming_input as double) ming_input\n", " from logistics_data_etls.b_gigs_model_conversion_input\n", " where ming_approval = 'Yes'\n", " and entity_type = 'Store')\n", "\n", "SELECT *\n", "FROM\n", "(select \n", "'DistanceTimeOrderRateCard' as rule_name,\n", "810 as priority,\n", "date1 as rate_card_validity_start_date,\n", "date1 as rate_card_validity_end_date,\n", "c.code as city_codes,\n", "'' as zone_ids,\n", "'' as locality_ids,\n", "cast(carrier_id as int) as carrier_id, \n", "f.slot_name as slot_names,\n", "'b_fleet_gigs_1' as accounting_tags,\n", "'' as day_of_week,\n", "0 as base_pay_pickup_amount,\n", "cast(ceil(base_pay-coalesce(base_buffer_flex,0)) as double) as base_pay_drop_amount,\n", "0 as pickup_distance_pay_round_off,\n", "0 as pickup_distance_pay_from,\n", "10 as pickup_distance_pay_to,\n", "0 as pickup_distance_pay_amount,\n", "0 as pickup_distance_pay_amount_breakup_base_distance_pay,\n", "0 as pickup_distance_pay_amount_breakup_incentive_distance_pay,\n", "0 as distance_pay_drop_round_off,\n", "0 as distance_pay_from,\n", "case when f.store_id=********* then 12 ELSE 10 END as distance_pay_to,\n", "case when f.slot_name IN ('SLOT_00_01','SLOT_01_02','SLOT_02_03','SLOT_03_04','SLOT_04_05','SLOT_05_06') \n", "                          then cast(dist_pay_per_km*0.90 as double)\n", "     WHEN f.carrier_id =3 then cast(dist_pay_per_km*coalesce(dd_buffer_flex,0.96)*0.96 as double)\n", "            ELSE cast(dist_pay_per_km*coalesce(dd_buffer_flex,0.96) as double) END as distance_pay_amount, \n", "case when f.slot_name IN ('SLOT_00_01','SLOT_01_02','SLOT_02_03','SLOT_03_04','SLOT_04_05','SLOT_05_06') \n", "                          then cast(dist_pay_per_km*0.90 as double)\n", "     WHEN f.carrier_id =3 then cast(dist_pay_per_km*coalesce(dd_buffer_flex,0.96)*0.96 as double)\n", "            ELSE cast(dist_pay_per_km*coalesce(dd_buffer_flex,0.96) as double) END as distance_pay_amount_breakup_base_distance_pay, \n", "0 as distance_pay_amount_breakup_incentive_distance_pay,\n", "0 as wait_time_pay_drop_round_off,\n", "3 as wait_time_pay_from,\n", "90 as wait_time_pay_to,\n", "0 as wait_time_pay_amount,\n", "case when carrier_id in (1,6)  then coalesce(coalesce(ms.ming_input,mc.ming_input),cast(floor(base_pay) as double)) \n", "     when carrier_id = 3  then coalesce(coalesce(ms.ming_input,mc.ming_input),cast(floor(base_pay) as double)) - 2 \n", "     when carrier_id  = 5 then coalesce(coalesce(ms.ming_input,mc.ming_input),cast(floor(base_pay) as double)) - 1\n", "     else  coalesce(coalesce(ms.ming_input,mc.ming_input),cast(floor(base_pay) as double)) end\n", "as trip_ming_amount,\n", "-- case when c.code IN ('PUN','M<PERSON>','<PERSON><PERSON>') AND f.slot_name IN ('SLOT_00_01','SLOT_01_02','SLOT_02_03','SLOT_03_04','SLOT_04_05','SLOT_05_06')\n", "-- then coalesce(incentive_multiplier,0)*1.00/100\n", "-- when f.slot_name IN ('SLOT_00_01','SLOT_01_02','SLOT_02_03','SLOT_03_04','SLOT_04_05','SLOT_05_06') then greatest(8,coalesce(incentive_multiplier,coalesce(late_night_incentive,0)))*1.00/100\n", "-- ELSE coalesce(coalesce(incentive_multiplier,late_night_incentive),0)*1.00/100 END as incentive_pay_multiplier,\n", "coalesce(incentive_multiplier,0)*1.00/100 incentive_pay_multiplier,\n", "'FALSE' as is_back_dated,\n", "'BLINKIT' as service_category, \n", "cast(f.store_id as int) as service_category_entity_id,\n", "case when c.code='HYD' then 1 ELSE 0.00001 END as base_pay_batched_order_drop_amount_multiplier,\n", "10 as weight_pay_slabs_from_0,\n", "100 as weight_pay_slabs_to_0,\n", "0.5 as weight_pay_slabs_amount_0,\n", "case when carrier_id in (1,6)  then coalesce(coalesce(ms.ming_input,mc.ming_input),cast(floor(base_pay) as double)) \n", "     when carrier_id = 3  then coalesce(coalesce(ms.ming_input,mc.ming_input),cast(floor(base_pay) as double)) - 2 \n", "     when carrier_id  = 5 then coalesce(coalesce(ms.ming_input,mc.ming_input),cast(floor(base_pay) as double)) - 1\n", "     else  coalesce(coalesce(ms.ming_input,mc.ming_input),cast(floor(base_pay) as double))  end \n", " as first_order_ming_amount,\n", "case when carrier_id in (1,6) then 15 \n", "     when carrier_id = 5 then 12\n", "     when carrier_id = 3 then 10\n", "     else 12 end\n", "as batched_order_ming_amount,\n", "date_format(current_date - interval '1' day, '%%Y%%m%%d') AS dt\n", "from final f\n", "left join zomato.carthero_prod.cities c on f.city = c.city_name\n", "--LEFT JOIN priority p on 1=1\n", "-- LEFT JOIN final_incentive_central fic ON f.date1=fic.account_date AND f.slot_name=fic.slot_name AND f.store_id=fic.store_id\n", "LEFT JOIN incentive_city ic ON f.date1=ic.account_date AND f.slot_name=ic.slot_name AND f.store_id=ic.store_id\n", "-- LEFT JOIN late_night_incentive lni ON f.date1=lni.account_date AND f.slot_name=lni.slot_name AND f.store_id=lni.store_id\n", "LEFT JOIN pay_buffer pf ON cast(f.store_id as varchar)=pf.store_id\n", "LEFT JOIN ming_store ms ON ms.store_id = f.store_id\n", "LEFT JOIN ming_city mc ON mc.city_name = f.city\n", "where date1 >= current_date+interval '0' day\n", ") \n", "where dt=date_format(current_date - interval '1' day, '%%Y%%m%%d')\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "8c5549ae-1d3c-47f7-bd11-99f86d933b3b", "metadata": {}, "outputs": [], "source": ["# red_con = pb.get_connection(\"[Warehouse] Trino\")\n", "# final = pd.read_sql_query(sql_query, con=red_con)"]}, {"cell_type": "code", "execution_count": null, "id": "ebb2e99c-8f0b-4c22-a573-ec0f17aa9a63", "metadata": {}, "outputs": [], "source": ["# final.info()"]}, {"cell_type": "code", "execution_count": null, "id": "fe21ef07-4ad1-4a94-ad0e-6f0a55e9e1e8", "metadata": {}, "outputs": [], "source": ["# final.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "bf861a88-365e-4f0a-ab0e-58dc050bc224", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"b_gigs_rc_plan_v5\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"rule_name\", \"type\": \"varchar\", \"description\": \"rule name\"},\n", "        {\"name\": \"priority\", \"type\": \"integer\", \"description\": \"priority\"},\n", "        {\n", "            \"name\": \"rate_card_validity_start_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"rate_card_validity_start_date\",\n", "        },\n", "        {\n", "            \"name\": \"rate_card_validity_end_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"rate_card_validity_end_date\",\n", "        },\n", "        {\"name\": \"city_codes\", \"type\": \"varchar\", \"description\": \"city_codes\"},\n", "        {\"name\": \"zone_ids\", \"type\": \"varchar\", \"description\": \"zone_ids\"},\n", "        {\"name\": \"locality_ids\", \"type\": \"varchar\", \"description\": \"locality_ids\"},\n", "        {\"name\": \"carrier_id\", \"type\": \"integer\", \"description\": \"carrier_id\"},\n", "        {\"name\": \"slot_names\", \"type\": \"varchar\", \"description\": \"slot_names\"},\n", "        {\n", "            \"name\": \"accounting_tags\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"accounting_tags\",\n", "        },\n", "        {\"name\": \"day_of_week\", \"type\": \"varchar\", \"description\": \"day_of_week\"},\n", "        {\n", "            \"name\": \"base_pay_pickup_amount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"base_pay_pickup_amount\",\n", "        },\n", "        {\n", "            \"name\": \"base_pay_drop_amount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"base_pay_drop_amount\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_round_off\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_round_off\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_from\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_from\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_to\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_to\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_amount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_amount\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_amount_breakup_base_distance_pay\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_amount_breakup_base_distance_pay\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_amount_breakup_incentive_distance_pay\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_amount_breakup_incentive_distance_pay\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_drop_round_off\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"distance_pay_drop_round_off\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_from\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"distance_pay_from\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_to\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"distance_pay_to\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_amount\",\n", "            \"type\": \"double\",\n", "            \"description\": \"distance_pay_amount\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_amount_breakup_base_distance_pay\",\n", "            \"type\": \"double\",\n", "            \"description\": \"distance_pay_amount_breakup_base_distance_pay\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_amount_breakup_incentive_distance_pay\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"distance_pay_amount_breakup_incentive_distance_pay\",\n", "        },\n", "        {\n", "            \"name\": \"wait_time_pay_drop_round_off\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"wait_time_pay_drop_round_off\",\n", "        },\n", "        {\n", "            \"name\": \"wait_time_pay_from\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"wait_time_pay_from\",\n", "        },\n", "        {\n", "            \"name\": \"wait_time_pay_to\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"wait_time_pay_to\",\n", "        },\n", "        {\n", "            \"name\": \"wait_time_pay_amount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"wait_time_pay_amount\",\n", "        },\n", "        {\n", "            \"name\": \"trip_ming_amount\",\n", "            \"type\": \"double\",\n", "            \"description\": \"trip_ming_amount\",\n", "        },\n", "        {\n", "            \"name\": \"incentive_pay_multiplier\",\n", "            \"type\": \"double\",\n", "            \"description\": \"incentive_pay_multiplier\",\n", "        },\n", "        {\"name\": \"is_back_dated\", \"type\": \"varchar\", \"description\": \"is_back_dated\"},\n", "        {\n", "            \"name\": \"service_category\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"service_category\",\n", "        },\n", "        {\n", "            \"name\": \"service_category_entity_id\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"service_category_entity_id\",\n", "        },\n", "        {\n", "            \"name\": \"base_pay_batched_order_drop_amount_multiplier\",\n", "            \"type\": \"double\",\n", "            \"description\": \"base_pay_batched_order_drop_amount_multiplier\",\n", "        },\n", "        {\n", "            \"name\": \"weight_pay_slabs_from_0\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"weight_pay_slabs_from_0\",\n", "        },\n", "        {\n", "            \"name\": \"weight_pay_slabs_to_0\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"weight_pay_slabs_to_0\",\n", "        },\n", "        {\n", "            \"name\": \"weight_pay_slabs_amount_0\",\n", "            \"type\": \"double\",\n", "            \"description\": \"weight_pay_slabs_amount_0\",\n", "        },\n", "        {\n", "            \"name\": \"first_order_ming_amount\",\n", "            \"type\": \"double\",\n", "            \"description\": \"first_order_ming_amount\",\n", "        },\n", "        {\n", "            \"name\": \"batched_order_ming_amount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"batched_order_ming_amount\",\n", "        },\n", "        {\"name\": \"dt\", \"type\": \"varchar\", \"description\": \"dt\"},\n", "    ],\n", "    \"primary_key\": [\n", "        \"rule_name\",\n", "        \"dt\",\n", "        \"slot_names\",\n", "        \"carrier_id\",\n", "        \"priority\",\n", "        \"rate_card_validity_start_date\",\n", "        \"rate_card_validity_end_date\",\n", "        \"service_category\",\n", "        \"service_category_entity_id\",\n", "    ],\n", "    \"partition_key\": [\"dt\"],\n", "    \"incremental_key\": \"dt\",\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"This Table has GIGS RC Config\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}\n", "pb.to_trino(sql_query, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "44cf20bf-5b72-4bd0-ad1d-52e943d019da", "metadata": {}, "outputs": [], "source": ["# import pytz"]}, {"cell_type": "code", "execution_count": null, "id": "341f7bf7-de36-4e2d-955c-28c77c64747e", "metadata": {}, "outputs": [], "source": ["# ist_timezone = pytz.timezone(\"Asia/Kolkata\")\n", "# table_push_time_ist = datetime.now(ist_timezone).strftime(\"%Y-%m-%d %H:%M:%S\")"]}, {"cell_type": "code", "execution_count": null, "id": "9d3d1928-6f29-480b-8a33-45f683edadd1", "metadata": {}, "outputs": [], "source": ["# table_push_time_ist"]}, {"cell_type": "code", "execution_count": null, "id": "90222e25-6c01-4dc6-8da5-d4d123aa0a2e", "metadata": {}, "outputs": [], "source": ["# con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "1d1f9047-07e0-4226-b8c6-4ad3ff4abc96", "metadata": {}, "outputs": [], "source": ["# df_base = pd.read_sql_query(sql=sql_query, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "5d32416d-256f-405c-815b-a6f8c1c856f2", "metadata": {}, "outputs": [], "source": ["# x = df_base.shape[0]\n", "# x"]}, {"cell_type": "code", "execution_count": null, "id": "0b48ad7a-7e5c-4cdb-94e0-4e2e40f0d1c7", "metadata": {}, "outputs": [], "source": ["# sql_check_1 = f\"\"\"\n", "\n", "# select count(*) as pushed_rows from logistics_data_etls.b_gigs_rc_plan_v5\n", "# where dt=date_format(current_date - interval '1' day, '%%Y%%m%%d')\n", "\n", "\n", "# \"\"\"\n", "# df1 = pd.read_sql_query(sql=sql_check_1, con=con)\n", "# df1"]}, {"cell_type": "code", "execution_count": null, "id": "717c1eb3-c428-4df2-80e7-34f7776eb16f", "metadata": {}, "outputs": [], "source": ["# sql_check_2 = f\"\"\"\n", "\n", "# select avg(1.00*cnt) as avg_pushed_rows_l30d from\n", "# (\n", "# select dt,count(*) as cnt from logistics_data_etls.b_gigs_rc_plan_v5\n", "# where dt between date_format(current_date - interval '31' day, '%%Y%%m%%d')\n", "# and date_format(current_date - interval '2' day, '%%Y%%m%%d')\n", "# group by 1\n", "# )\n", "\n", "\n", "# \"\"\"\n", "# df2 = pd.read_sql_query(sql=sql_check_2, con=con)\n", "# df2"]}, {"cell_type": "code", "execution_count": null, "id": "b9d44cc8-b892-4f1a-a20b-31a3c6d6aa9a", "metadata": {}, "outputs": [], "source": ["# pushed_rows = pd.to_numeric(df1.iloc[0, 0], errors=\"coerce\")\n", "# avg_pushed_rows_l30d = pd.to_numeric(df2.iloc[0, 0], errors=\"coerce\")\n", "\n", "\n", "# if pd.isna(pushed_rows) or pd.isna(avg_pushed_rows_l30d):\n", "#     raise ValueError(\"One or more of the extracted values is not numeric.\")\n", "\n", "\n", "# query_output_table_push_delta = 100.00 * ((pushed_rows / x) - 1)\n", "# deviation_wrt_l30d = 100.00 * ((pushed_rows / avg_pushed_rows_l30d) - 1)\n", "\n", "# query_output_table_push_delta = f\"{query_output_table_push_delta:.2f}%\"\n", "# deviation_wrt_l30d = f\"{deviation_wrt_l30d:.2f}%\"\n", "\n", "\n", "# final_df = pd.DataFrame(\n", "#     {\n", "#         \"table_push_time_ist\": [table_push_time_ist],\n", "#         \"base_query_output_rows\": [x],\n", "#         \"pushed_rows\": [pushed_rows],\n", "#         \"avg_pushed_rows_l_30_d\": [avg_pushed_rows_l30d],\n", "#         \"query_output_table_push_delta\": [query_output_table_push_delta],\n", "#         \"deviation_wrt_l_30_d\": [deviation_wrt_l30d],\n", "#     }\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "5168d5e8-ec15-472b-86bf-b890367a1607", "metadata": {}, "outputs": [], "source": ["# null_counts = df_base.isnull().sum()\n", "\n", "# # Create a new DataFrame with column names and their null counts\n", "# null_values_df = pd.DataFrame(\n", "#     {\"Column Name\": null_counts.index, \"Null Values Count\": null_counts.values}\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "29838302-c826-44f0-a6e8-087ce1807ab7", "metadata": {}, "outputs": [], "source": ["# from tabulate import tabulate"]}, {"cell_type": "code", "execution_count": null, "id": "3c07e067-d599-46b1-bffa-917afe34c71c", "metadata": {}, "outputs": [], "source": ["# ascii_table_1 = \"```\" + tabulate(final_df, headers=\"keys\", tablefmt=\"psql\", showindex=False) + \"```\"\n", "# ascii_table_2 = (\n", "#     \"```\" + tabulate(null_values_df, headers=\"keys\", tablefmt=\"psql\", showindex=False) + \"```\"\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "9116c639-b073-4f8d-9f0d-8d82045b4e0f", "metadata": {}, "outputs": [], "source": ["# final_df[\"query_output_table_push_delta_numeric\"] = pd.to_numeric(\n", "#     final_df[\"query_output_table_push_delta\"].str.rstrip(\"%\"), errors=\"coerce\"\n", "# )\n", "# final_df[\"deviation_wrt_l_30_d_numeric\"] = pd.to_numeric(\n", "#     final_df[\"deviation_wrt_l_30_d\"].str.rstrip(\"%\"), errors=\"coerce\"\n", "# )\n", "\n", "# # Check if any value in specified columns is greater than 5%\n", "# if (\n", "#     (final_df[\"query_output_table_push_delta_numeric\"] != 0).any()\n", "#     or (final_df[\"deviation_wrt_l_30_d_numeric\"] > 5).any()\n", "#     or (final_df[\"deviation_wrt_l_30_d_numeric\"] < -5).any()\n", "# ):\n", "\n", "#     pb.send_slack_message(\n", "#         channel=\"bl-cpo-critical-dag-failure-alert\",\n", "#         text=\"report for *logistics_b_gigs_etl_b_gigs_rc_v5->1*  dag:\\n\" + ascii_table_1,\n", "#     )\n", "# else:\n", "#     print(\"end\")"]}, {"cell_type": "code", "execution_count": null, "id": "33ed7870-51dd-4f0b-84bd-de6c7d4cbd32", "metadata": {}, "outputs": [], "source": ["# if (null_values_df[\"Null Values Count\"] > 0).any():\n", "\n", "#     # Send Slack message\n", "#     pb.send_slack_message(\n", "#         channel=\"bl-cpo-critical-dag-failure-alert\",\n", "#         text=\"report for *logistics_b_gigs_etl_b_gigs_rc_v5->2* dag:\\n\" + ascii_table_2,\n", "#     )\n", "# else:\n", "#     print(\"end\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}