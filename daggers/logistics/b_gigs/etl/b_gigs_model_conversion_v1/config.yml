alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: b_gigs_model_conversion_v1
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: logistics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U082DBVETNX
path: logistics/b_gigs/etl/b_gigs_model_conversion_v1
paused: false
pool: logistics_pool
project_name: b_gigs
schedule:
  end_date: '2025-09-12T00:00:00'
  interval: 30 12 * * *
  start_date: '2024-05-17T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
