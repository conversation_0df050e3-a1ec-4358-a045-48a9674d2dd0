{"cells": [{"cell_type": "code", "execution_count": null, "id": "9d969bdd-6c1e-428d-b811-a7f9671a0015", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "id": "29464f23-4c62-44ea-8e1b-a2515f77b7d4", "metadata": {}, "outputs": [], "source": ["conn = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "97884173-9851-4283-97c1-4d87546faf5f", "metadata": {}, "outputs": [], "source": ["number_of_weeks = 4"]}, {"cell_type": "code", "execution_count": null, "id": "a31fd16a-11ae-4ee2-9098-987a5b7430f2", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "    -- last store worked by a drive in a week, table can be used to get the active fleet\n", "    With last_store_worked_in_the_week as(\n", "        select \n", "              delivery_driver_id,\n", "              week,\n", "              max_by(blinkit_store_id,account_date) as blinkit_store_id\n", "        from logistics_data_etls.blinkit_rider_daily_accounting_summary_v1 a\n", "        where account_date between date_trunc('week', current_date) -interval'7'day*({number_of_weeks}+1) and date_trunc('week', current_date) -interval'1'day\n", "        group by 1,2\n", "    )\n", "\n", "    , worked_weeks as(\n", "        select \n", "              delivery_driver_id,\n", "              week,\n", "              min(account_date) as week_start_date,\n", "              max(account_date) as week_end_date\n", "        from logistics_data_etls.blinkit_rider_daily_accounting_summary_v1 a\n", "        where account_date between date_trunc('week', current_date) -interval'7'day*({number_of_weeks}+1) and date_trunc('week', current_date) -interval'1'day\n", "        group by 1,2\n", "    )\n", "\n", "    --drivers that did not arrive in the system the next week\n", "    , churned_riders_by_week as(\n", "        select \n", "              w.delivery_driver_id,\n", "              w.week,\n", "              w.week_start_date,\n", "              w.week_end_date,\n", "              sw.blinkit_store_id\n", "        from worked_weeks w\n", "        left join worked_weeks w1\n", "        on w.delivery_driver_id = w1.delivery_driver_id and w.week = w1.week-1\n", "        left join last_store_worked_in_the_week sw\n", "        on w.delivery_driver_id = sw.delivery_driver_id\n", "        and w.week = sw.week\n", "        where w1.delivery_driver_id is null\n", "        and w.week < week(date_trunc('week', current_date) -interval'1'day)\n", "    )\n", "    select * from churned_riders_by_week\n", "\"\"\"\n", "churn_riders_by_week_df = pd.read_sql_query(query, conn)"]}, {"cell_type": "code", "execution_count": null, "id": "68d47e7d-854b-47ec-abfb-78cc7c9acefc", "metadata": {}, "outputs": [], "source": ["churn_riders_by_week_df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "48bc05c3-7d33-4a26-ac30-77a46bf271a3", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"churned_riders_by_week\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"delivery_driver_id\",\n", "            \"type\": \"double\",\n", "            \"description\": \"Unique identifier for the driver\",\n", "        },\n", "        {\"name\": \"week\", \"type\": \"double\", \"double\": \"week\", \"description\": \"week\"},\n", "        {\n", "            \"name\": \"blinkit_store_id\",\n", "            \"type\": \"double\",\n", "            \"description\": \"Identifier for the Blinkit store\",\n", "        },\n", "        {\"name\": \"week_start_date\", \"type\": \"varchar\", \"description\": \"week start date\"},\n", "        {\"name\": \"week_end_date\", \"type\": \"varchar\", \"description\": \"week end date\"},\n", "    ],\n", "    \"primary_key\": [\"delivery_driver_id\", \"week\", \"blinkit_store_id\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"churned riders by week\",\n", "}\n", "pb.to_trino(churn_riders_by_week_df, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "73169231-b447-4160-8b9a-80902e0eeff4", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "    select \n", "          distinct cast(week as int)\n", "    from logistics_data_etls.churned_riders_by_week\n", "\"\"\"\n", "week_df = pd.read_sql_query(query, conn)\n", "week_df.columns = [\"week\"]\n", "\n", "week = list(week_df[\"week\"])\n", "week.sort()"]}, {"cell_type": "code", "execution_count": null, "id": "9c007801-7218-40e9-99ee-487de1fe25e6", "metadata": {}, "outputs": [], "source": ["week"]}, {"cell_type": "code", "execution_count": null, "id": "cec9dae2-65bb-4192-9c07-ec47dbefcb14", "metadata": {}, "outputs": [], "source": ["# week = [19,20,21,22]\n", "dates_df = None\n", "for i in week:\n", "    query = f\"\"\"\n", "    select \n", "          format_datetime(cast(week_start_date as date) + interval'7'day, 'yyyyMMdd') as start_date,\n", "          format_datetime(cast(week_start_date as date) + interval'13'day, 'yyyyMMdd') as end_date\n", "    from\n", "    (\n", "        select week_start_date \n", "        from dwh.dim_date where year = 2025 and week = {i}\n", "        limit 1\n", "    )\n", "    \"\"\"\n", "    if dates_df is None:\n", "        dates_df = pd.read_sql_query(query, conn)\n", "        dates_df.index = [i]\n", "    else:\n", "        df = pd.read_sql_query(query, conn)\n", "        df.index = [i]\n", "        dates_df = pd.concat([dates_df, df], ignore_index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "6c5511ed-bd51-45ec-89e1-bb71059954f8", "metadata": {}, "outputs": [], "source": ["dates_df"]}, {"cell_type": "code", "execution_count": null, "id": "c41e71a5-0ade-43f9-ae21-e307379e976e", "metadata": {}, "outputs": [], "source": ["driver_pings_df = None\n", "\n", "import time\n", "\n", "start = time.time()\n", "\n", "for i in week:\n", "    query = f\"\"\"\n", "        With store_location as (\n", "            select * from\n", "            (\n", "                select \n", "                      merchant_id as blinkit_store_id,\n", "                      merchant_store_latitude,\n", "                      merchant_store_longitude,\n", "                      row_number() over(partition by merchant_id order by 1) as rno\n", "                from dwh.dim_merchant_polygon\n", "                where is_current = true\n", "            )\n", "            where rno = 1\n", "        )\n", "        --driver pings of the drivers that did not come the next week\n", "        , driver_pings as(\n", "            select *\n", "            from\n", "            (\n", "                select\n", "                  b.delivery_driver_id,\n", "                  b.week,\n", "                  b.blinkit_store_id,\n", "                  avg(latitude) as latitude,\n", "                  avg(longitude) as longitude\n", "                from zomato.jumbo2.driver_pings p\n", "                inner join (select * from logistics_data_etls.churned_riders_by_week where week = {i}) b\n", "                on try_cast(p.driver_id as int) = b.delivery_driver_id\n", "                where latitude > 0 and longitude > 0 and login_status = 'offline'\n", "                and dt between cast({dates_df.loc[i][0]} as varchar) and cast({dates_df.loc[i][1]} as varchar)\n", "                group by 1,2,3\n", "            )\n", "        )\n", "        , driver_distance as(\n", "            select \n", "              concat('FE',cast(dp.delivery_driver_id + 10000 as varchar)) as driver_id,\n", "              dp.blinkit_store_id,\n", "              merchant_store_latitude,\n", "              merchant_store_longitude,\n", "              dp.week,\n", "              dp.latitude as driver_lat,\n", "              dp.longitude as driver_long\n", "            from driver_pings dp\n", "            left join store_location sl\n", "            on sl.blinkit_store_id = dp.blinkit_store_id\n", "        )\n", "        select * from driver_distance\n", "    \"\"\"\n", "    if driver_pings_df is None:\n", "        driver_pings_df = pd.read_sql_query(query, conn)\n", "    else:\n", "        driver_pings_df = pd.concat(\n", "            [driver_pings_df, pd.read_sql_query(query, conn)], ignore_index=True\n", "        )\n", "\n", "end = time.time()\n", "\n", "elapsed_seconds = end - start\n", "elapsed_minutes = elapsed_seconds / 60\n", "\n", "print(f\"Execution time: {elapsed_minutes:.2f} minutes\")"]}, {"cell_type": "code", "execution_count": null, "id": "575fbb03-fbd1-4236-8a03-0033182e1eb0", "metadata": {}, "outputs": [], "source": ["driver_pings_df[\"week\"].value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "e30b3ff7-8d70-456a-aa58-2da4083af5e7", "metadata": {}, "outputs": [], "source": ["from math import radians, sin, cos, sqrt, atan2\n", "\n", "\n", "def haversine(coord1, coord2):\n", "    R = 6371  # Earth radius in km\n", "    lat1, lon1 = coord1\n", "    lat2, lon2 = coord2\n", "\n", "    # Convert to radians\n", "    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])\n", "\n", "    dlat = lat2 - lat1\n", "    dlon = lon2 - lon1\n", "\n", "    a = sin(dlat / 2) ** 2 + cos(lat1) * cos(lat2) * sin(dlon / 2) ** 2\n", "    c = 2 * atan2(sqrt(a), sqrt(1 - a))\n", "\n", "    return R * c"]}, {"cell_type": "code", "execution_count": null, "id": "1f21a6fb-648e-4898-a693-f675a5a0286d", "metadata": {}, "outputs": [], "source": ["driver_pings_df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "8248ad36-f8f8-4b02-ae5f-bd31628da271", "metadata": {}, "outputs": [], "source": ["driver_pings_df[\"distance_km\"] = [\n", "    haversine((lat1, lon1), (lat2, lon2))\n", "    for lat1, lon1, lat2, lon2 in zip(\n", "        driver_pings_df[\"merchant_store_latitude\"].astype(float),\n", "        driver_pings_df[\"merchant_store_longitude\"].astype(float),\n", "        driver_pings_df[\"driver_lat\"].astype(float),\n", "        driver_pings_df[\"driver_long\"].astype(float),\n", "    )\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "7c41b03b-64cd-4204-8aad-123f4ff20beb", "metadata": {}, "outputs": [], "source": ["driver_pings_df[\"distance_km\"] = driver_pings_df[\"distance_km\"].fillna(-1)"]}, {"cell_type": "code", "execution_count": null, "id": "74bee11a-2bf0-485a-af66-9ceb05647f5e", "metadata": {}, "outputs": [], "source": ["driver_pings_df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "e14d3517-99d7-4f9f-963d-bb9fcccf0b2d", "metadata": {}, "outputs": [], "source": ["driver_pings_df = driver_pings_df.astype(\n", "    {\n", "        \"driver_id\": str,\n", "        \"blinkit_store_id\": int,\n", "        \"merchant_store_latitude\": float,\n", "        \"merchant_store_longitude\": float,\n", "        \"week\": int,\n", "        \"driver_lat\": float,\n", "        \"driver_long\": float,\n", "        \"distance_km\": float,\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6df0adab-3d81-4452-864f-0bf3069f64c3", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"churned_riders_ping_distance\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"driver_id\", \"type\": \"VARCHAR\", \"description\": \"Unique identifier for the driver\"},\n", "        {\n", "            \"name\": \"blinkit_store_id\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Identifier for the Blinkit store\",\n", "        },\n", "        {\n", "            \"name\": \"merchant_store_latitude\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Latitude of the merchant store\",\n", "        },\n", "        {\n", "            \"name\": \"merchant_store_longitude\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Longitude of the merchant store\",\n", "        },\n", "        {\"name\": \"week\", \"type\": \"DOUBLE\", \"description\": \"Week number\"},\n", "        {\"name\": \"driver_lat\", \"type\": \"DOUBLE\", \"description\": \"Latitude from driver’s device\"},\n", "        {\"name\": \"driver_long\", \"type\": \"DOUBLE\", \"description\": \"Longitude from driver’s device\"},\n", "        {\n", "            \"name\": \"distance_km\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Distance between store and driver (km)\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"driver_id\", \"week\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"return drivers distance from his store\",\n", "}\n", "pb.to_trino(driver_pings_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "678c118a-33c2-4887-aa04-4d893f626814", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}