alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: b_gigs_final_rc_upload_v3_multiplier
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: logistics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U082DBVETNX
path: logistics/b_gigs_final_rc_upload_v3_multiplier/etl/b_gigs_final_rc_upload_v3_multiplier
paused: false
pool: logistics_pool
project_name: b_gigs_final_rc_upload_v3_multiplier
schedule:
  end_date: '2025-09-12T00:00:00'
  interval: 0 7,13,17 * * *
  start_date: '2025-04-10T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 4
