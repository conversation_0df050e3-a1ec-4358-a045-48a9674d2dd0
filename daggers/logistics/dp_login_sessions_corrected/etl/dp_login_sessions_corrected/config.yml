alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: dp_login_sessions_corrected
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: logistics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U08D6SCUHB6
path: logistics/dp_login_sessions_corrected/etl/dp_login_sessions_corrected
paused: false
pool: logistics_pool
project_name: dp_login_sessions_corrected
schedule:
  end_date: '2025-08-29T00:00:00'
  interval: 30 0,1,2,3 * * *
  start_date: '2025-03-25T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
