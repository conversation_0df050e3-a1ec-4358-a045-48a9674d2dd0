{"cells": [{"cell_type": "code", "execution_count": null, "id": "69af8980-6bba-4cd5-a70d-413e1dbd6b73", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "id": "6872b163-5189-4603-86a9-7cee44eb0918", "metadata": {}, "outputs": [], "source": ["sql_query = f\"\"\"\n", "\n", "with store_slot_map as (\n", "select  external_store_id , slot_id , slot_name , start_time , end_time , city_name  , blinkit_store_name \n", "from \n", "(\n", "select date , external_store_id , slot_id , slot_name , start_time , end_time , city_name \n", "from \n", "(\n", "select * , row_number() over (partition by  external_store_id , slot_id order by date desc ) rnk \n", "from logistics_data_etls.model_gigs_info_input_v1\n", "where date >= current_date - interval '10' day )\n", "where rnk = 1 \n", ")\n", "left join logistics_data_etls.blinkit_store_mapping sm on external_store_id = store_id \n", "\n", ")\n", " ,\n", "\n", "city_slot_map as (\n", "select distinct city_name ,  slot_id , slot_name , start_time , end_time  \n", "from \n", "(select  external_store_id , slot_id , slot_name , start_time , end_time , city_name \n", "from \n", "(\n", "select * , row_number() over (partition by  external_store_id , slot_id order by date desc ) rnk \n", "from logistics_data_etls.model_gigs_info_input_v1\n", "where date >= current_date - interval '10' day )\n", "where rnk = 1 \n", ") \n", "\n", ")\n", ",\n", "\n", " store_base as(\n", "\n", "select store_id , new_model_mix \n", "from \n", "(\n", "select  entity_id  store_id , new_model_mix new_model_mix \n", "from logistics_data_etls.model_cpk_input_v1\n", "where entity_type = 'store'\n", "and model_type = '25-75 model'\n", "and carrier_id = 1 \n", ")\n", "\n", "\n", "),\n", "\n", "gigs_base as (\n", "\n", "\n", "select order_projection_date account_date ,  dow(order_projection_date) dow ,\n", "store_id , slot_id , slot_name , greatest(projected_orders , 1) projected_orders , carrier_id carrier_type  , store_name\n", "from logistics_data_etls.projected_orders_v1 op\n", "inner join (\n", "select  distinct entity_id , carrier_id\n", "from logistics_data_etls.model_cpk_input_v1\n", "where entity_type = 'store'\n", "and model_type = '25-75 model'\n", ") carrier on entity_id = store_id\n", "AND store_id in (Select store_id from store_base)),\n", "\n", "\n", "\n", "cpk_input as (\n", "select store_id , carrier_id , weekly_cpk  , cpo_buffer\n", "from\n", "(\n", "select  entity_id  store_id  , carrier_id , weekly_cpk , cpo_buffer \n", "from logistics_data_etls.model_cpk_input_v1\n", "where entity_type = 'store'\n", "and model_type = '25-75 model'\n", "\n", ")\n", "),\n", "\n", "dd_planned as (\n", "select account_date , store_id , slot_id , final_dd planned_dd \n", "from logistics_data_etls.planned_dd_input_v1\n", "),\n", "\n", "dd_final_slot as (\n", "\n", "select store_id , slot_id , account_date , case when id is not null then c_projected_orders else projected_orders end as projected_orders   , \n", "case when id is not null then c_planned_dd * 1.00 / c_projected_orders else planned_dd end as planned_dd  \n", "from\n", "(select a.store_id , slot_id , account_date , projected_orders , planned_dd , \n", "sum(case when id is not null then  projected_orders end ) over (partition by id , slot_id , account_date ) c_projected_orders , \n", "sum(case when id is not null then  planned_dd * projected_orders end) over (partition by id , slot_id , account_date ) c_planned_dd , \n", "id\n", "from \n", "(\n", "SELECT gb.store_id,gb.slot_id,gb.account_date,projected_orders,coalesce(de.planned_dd,coalesce(greatest(1,dp.planned_dd),2)) planned_dd\n", "FROM (SELECT store_id,slot_id,account_date,projected_orders FROM gigs_base WHERE carrier_type=1 GROUP BY 1,2,3,4) gb\n", "LEFT JOIN dd_planned dp ON gb.store_id=dp.store_id AND gb.account_date=dp.account_date AND gb.slot_id=dp.slot_id\n", "LEFT JOIN (SELECT try_cast(entity_id as int) entity_id,try_cast(planned_dd as double) planned_dd FROM logistics_data_etls.b_gigs_exception \n", "WHERE dd_exception='Yes' AND try_cast(entity_id as int)>0 AND entity_type='Store') de ON gb.store_id=try_cast(de.entity_id as int)\n", "GROUP BY 1,2,3,4,5\n", ") a\n", "left join logistics_data_etls.store_in_store_mapping_v1 b on a.store_id = b.store_id \n", ")\n", "\n", "\n", "),\n", "\n", "dd_final_week as (SELECT store_id,sum(projected_orders) projected_orders,sum(projected_orders*planned_dd)*1.00/sum(projected_orders) projected_dd\n", "FROM dd_final_slot WHERE account_date between current_date+interval '1' day AND current_date+interval '7' day GROUP BY 1),\n", "\n", "\n", "cpo_input as (SELECT ci.store_id,carrier_id,weekly_cpk,\n", "case when carrier_id=3 then weekly_cpk*coalesce(projected_dd,2)*1.00/1.5 ELSE weekly_cpk*coalesce(projected_dd,2)*1.00 END as weekly_cpo , projected_dd\n", ",cpo_buffer\n", "FROM cpk_input ci\n", "LEFT JOIN dd_final_week dfw ON ci.store_id=dfw.store_id GROUP BY 1,2,3,4,5,6)\n", "\n", "\n", ",\n", "\n", "------------------------------------------------------------------------------------------------------------\n", "cpo_factor as (\n", "\n", "\n", "select store_id , ldrp_cpo , cancellation_factor ,  ming_cpo ,multiple\n", "from \n", "(\n", "select external_store_id store_id , least(ldrp*least((extract(week from current_date)+52-40)*0.20,1.00) , 3) ldrp_cpo , \n", "coalesce(least((1-greatest(coalesce(del_orders,1),1)*1.00/greatest(coalesce(total_orders,1),1))*\n", "least((extract(week from current_date)+52-45)*0.25,1.00),1-greatest(coalesce(del_orders,1),1)\n", "*1.00/greatest(coalesce(total_orders,1),1)),0.02) cancellation_factor ,\n", "case when total_orders > 5 then greatest(trip_ming , 0.03)  else 0.03 end as ming_cpo , \n", "least((extract(week from current_date)+52-45)*0.25,1.00)  multiple  \n", " from logistics_data_etls.cpo_factor_input_v1\n", " \n", " )\n", " \n", "\n", ")\n", "\n", "\n", ",\n", "cpo_plan as\n", "(select *, max(weekly_cpo) over (partition by store_id) as bike_cpo\n", "from ( SELECT distinct store_id,carrier_id,(((1-cpo_buffer)*weekly_cpo)-ldrp_cpo-ming_cpo)*(1-coalesce(cancellation_factor,0.02)) weekly_cpo , projected_dd\n", "FROM (select b.store_id,b.carrier_id,coalesce(cpo_buffer,0.05) cpo_buffer,coalesce(ldrp_cpo,0.25) ldrp_cpo,coalesce(cancellation_factor,0.02*cf.multiple) cancellation_factor,\n", " coalesce(ming_cpo,0.03) ming_cpo,weekly_cpo , projected_dd\n", "from cpo_input b\n", "\n", "LEFT JOIN cpo_factor cf on b.store_id=cf.store_id\n", "\n", "group by 1,2,3,4,5,6,7 , 8\n", "))) ,\n", "\n", "-- select * from cpo_plan\n", "\n", "\n", "\n", "skew_input_city as (select entity_name , dow , slot_id  , avg(cpk_factor) skew_factor\n", "from\n", "(\n", "select entity_type , entity_name , dow , hour , cpk_factor \n", "from \n", "(\n", "select entity_type , entity_name , dow , hour , cpk_factor \n", "from logistics_data_etls.model_skew_input_v1\n", "where  entity_type = 'City'\n", ")\n", " )\n", "inner join city_slot_map on city_name = entity_name and hour between start_time and end_time  - 1 \n", "group by 1 , 2, 3\n", ")\n", "\n", "\n", ",\n", "\n", "\n", "skew_input_store as (select entity_name , dow , slot_id  , avg(cpk_factor) skew_factor\n", "from\n", "(\n", "select entity_type , entity_name , dow , hour , cpk_factor \n", "from \n", "(\n", "select entity_type , entity_name , dow , hour , cpk_factor \n", "from logistics_data_etls.model_skew_input_v1\n", "where entity_type = 'Store'\n", ")\n", " )\n", "inner join store_slot_map on blinkit_store_name = entity_name and hour between start_time and end_time  - 1 \n", "group by 1 , 2, 3\n", ")\n", ",\n", "\n", "\n", "join_cpo as\n", "(\n", "select c.*, weekly_cpo, bike_cpo,c.account_date date1 , projected_dd\n", "from gigs_base c\n", "left join cpo_plan p on c.store_id=cast(p.store_id as int) and cast(p.carrier_id as int)=c.carrier_type\n", "),\n", "\n", "\n", "store_city as\n", "(\n", " select distinct c.store_id entity_id,sm.runnr_city as city\n", " from gigs_base c\n", " left join logistics_data_etls.blinkit_store_mapping sm on sm.store_id = c.store_id\n", ")\n", "\n", ",\n", "\n", "\n", "base_joins as\n", "(\n", "\n", "select j.slot_id, j.carrier_type as carrier_id, j.store_id as store_id, j.store_name, cs.city,bike_cpo, j.slot_name,\n", "date1, cast(weekly_cpo as double) as weekly_cpo, j.dow, coalesce(df.projected_orders , j.projected_orders)  as slot_orders,\n", "coalesce(sk1.skew_factor,sk2.skew_factor) skew_factor,\n", "planned_dd , projected_dd\n", "from join_cpo j\n", "left join store_city cs on cs.entity_id=j.store_id\n", "left join skew_input_store sk1 on sk1.entity_name=j.store_name and sk1.slot_id=j.slot_id and cast(sk1.dow as int)=extract(dow from j.date1)\n", "left join skew_input_city sk2 on sk2.entity_name=cs.city\n", "and sk2.slot_id=j.slot_id and cast(sk2.dow as int)=extract(dow from j.date1)\n", "left join dd_final_slot df on df.account_Date = j.date1 and df.slot_id = j.slot_id and df.store_id  = j.store_id \n", "where date1 between current_date+interval '0' day and current_date + interval '10' day\n", "and cast(weekly_cpo as double)>0 and j.projected_orders>0\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14\n", "\n", ")\n", ",\n", "\n", "\n", "slot_cpo_skew as\n", "(select distinct store_id c_store_id,\n", "sum(weekly_cpo*skew_factor*slot_orders) over (partition by b.store_id)*1.00 / sum(weekly_cpo*slot_orders) over (partition by b.store_id) as correction_factor\n", "from base_joins b\n", "WHERE carrier_id=1 AND date1 between current_date+interval '1' day AND current_date+interval '7' day)\n", "\n", "\n", "\n", ",slot_cpo as\n", "(\n", "select b.*, weekly_cpo*skew_factor as slot_base_cpo,\n", "weekly_cpo*skew_factor*slot_orders as slot_base_earning, \n", "weekly_cpo*slot_orders as weekly_earning,\n", "correction_factor,\n", "skew_factor*1.00/correction_factor as corrected_slot_factor,\n", "weekly_cpo* skew_factor *1.00 / correction_factor as final_slot_cpo,\n", "greatest(coalesce(coalesce(planned_dd,projected_dd),2.00),1.00) as past_drop_distance, b.store_id+dow+b.slot_id+extract(week from date1)+carrier_id as rand,\n", "mod(b.store_id+dow+b.slot_id+extract(week from date1)+carrier_id,least(floor(bike_cpo/4.5),10))/100 as base_var\n", "from base_joins b\n", "LEFT JOIN slot_cpo_skew c on b.store_id=c.c_store_id\n", "),\n", "\n", "\n", "final as (\n", "SELECT *,(final_slot_cpo-base_pay) distance_pay,\n", "least(greatest((final_slot_cpo-base_pay)*1.00/(case when carrier_id=3 then greatest(past_drop_distance*1.00/1.5,1) else past_drop_distance end),4.00),40.00) as dist_pay_per_km\n", "FROM \n", "(\n", "select s.*,new_model_mix,\n", "case WHEN carrier_id IN (1,6) then \n", "    greatest(((0.20*new_model_mix)+greatest(0,(1-new_model_mix)*0.5)+base_var)*bike_cpo,4)\n", "    --- fixing minimum base_pay value = 4 \n", "when carrier_id=3 then greatest(((0.25*new_model_mix)+greatest(0,(1-new_model_mix)*0.5)+base_var)*bike_cpo,4)\n", "    else greatest(((0.20*new_model_mix)+greatest(0,(1-new_model_mix)*0.5)+base_var)*bike_cpo,4) end base_pay\n", "from slot_cpo s\n", "left join store_base sb on s.store_id = sb.store_id\n", ")\n", ")\n", "\n", "\n", ",\n", "\n", "\n", "\n", "incentive_city as (\n", "select store_id , account_date , slot_id  , slot_name , max(incentive_multiplier) incentive_multiplier \n", "from \n", "(select account_date , a.store_id , slot_id , slot_name , start_time , end_time , hr , max(incentive_multiplier) incentive_multiplier\n", "from \n", "(select date account_date , external_store_id store_id , slot_id , slot_name , start_time , end_time  , hr \n", "from \n", "(select date , external_store_id , slot_id , slot_name , start_time , end_time , city_name  \n", "from \n", "(select * \n", "from logistics_data_etls.model_gigs_info_input_v1\n", "where date >= current_date - interval '10' day )\n", ")\n", "inner join (SELECT hr FROM UNNEST(sequence(0 , 23, 1)) as t(hr) ) on hr between start_time and end_time - 1 \n", ") a \n", "inner join \n", "(\n", "SELECT store_id,\n", "start_hour,\n", "end_hour,\n", "(start_date) start_date,(end_date) end_date,try_cast(incentive_multiplier as int) incentive_multiplier\n", "FROM logistics_data_etls.incentive_multiplier_input_v1\n", "WHERE approval='Yes' AND incentive_multiplier is not null\n", "GROUP BY 1,2,3,4,5,6)\n", "b on a.account_date between start_date and end_date \n", "and hr between start_hour and end_hour - 1 and a.store_id = b.store_id \n", "group by 1 , 2, 3, 4, 5, 6 , 7)\n", "group by 1 , 2,3,4),\n", "\n", "\n", "pay_buffer as (SELECT entity_id store_id,max(try_cast(dd_buffer_flex as double)) dd_buffer_flex , max(ming_input) ming_input\n", "FROM logistics_data_etls.model_cpk_input_v1\n", "where entity_type = 'store'\n", "GROUP BY 1\n", ")\n", "\n", "\n", "\n", "\n", "SELECT *\n", "FROM\n", "(select \n", "'DistanceTimeOrderRateCard' as rule_name,\n", "910 as priority,\n", "date1 as rate_card_validity_start_date,\n", "date1 as rate_card_validity_end_date,\n", "c.code as city_codes,\n", "'' as zone_ids,\n", "'' as locality_ids,\n", "cast(carrier_id as int) as carrier_id, \n", "f.slot_name as slot_names,\n", "'b_fleet_gigs_1' as accounting_tags,\n", "'' as day_of_week,\n", "0 as base_pay_pickup_amount,\n", "cast(ceil(base_pay) as double) as base_pay_drop_amount,\n", "0 as pickup_distance_pay_round_off,\n", "0 as pickup_distance_pay_from,\n", "10 as pickup_distance_pay_to,\n", "0 as pickup_distance_pay_amount,\n", "0 as pickup_distance_pay_amount_breakup_base_distance_pay,\n", "0 as pickup_distance_pay_amount_breakup_incentive_distance_pay,\n", "0 as distance_pay_drop_round_off,\n", "0 as distance_pay_from,\n", "case when f.store_id=********* then 12 ELSE 10 END as distance_pay_to,\n", "case when f.slot_name IN ('SLOT_00_01','SLOT_01_02','SLOT_02_03','SLOT_03_04','SLOT_04_05','SLOT_05_06') \n", "                          then cast(dist_pay_per_km*0.90 as double)\n", "     WHEN f.carrier_id =3 then cast(dist_pay_per_km*coalesce(dd_buffer_flex,0.96)*0.96 as double)\n", "            ELSE cast(dist_pay_per_km*coalesce(dd_buffer_flex,0.96) as double) END as distance_pay_amount, \n", "case when f.slot_name IN ('SLOT_00_01','SLOT_01_02','SLOT_02_03','SLOT_03_04','SLOT_04_05','SLOT_05_06') \n", "                          then cast(dist_pay_per_km*0.90 as double)\n", "     WHEN f.carrier_id =3 then cast(dist_pay_per_km*coalesce(dd_buffer_flex,0.96)*0.96 as double)\n", "            ELSE cast(dist_pay_per_km*coalesce(dd_buffer_flex,0.96) as double) END as distance_pay_amount_breakup_base_distance_pay, \n", "0 as distance_pay_amount_breakup_incentive_distance_pay,\n", "0 as wait_time_pay_drop_round_off,\n", "3 as wait_time_pay_from,\n", "90 as wait_time_pay_to,\n", "0 as wait_time_pay_amount,\n", "case when carrier_id in (1,6)  then coalesce(ming_input,cast(floor(base_pay) as double)) \n", "     when carrier_id = 3  then coalesce(ming_input,cast(floor(base_pay) as double)) - 2 \n", "     when carrier_id  = 5 then coalesce(ming_input,cast(floor(base_pay) as double)) - 1\n", "     else  coalesce(ming_input,cast(floor(base_pay) as double)) end\n", "as trip_ming_amount,\n", "\n", "coalesce(incentive_multiplier,0)*1.00/100 incentive_pay_multiplier,\n", "'FALSE' as is_back_dated,\n", "'BLINKIT' as service_category, \n", "cast(f.store_id as int) as service_category_entity_id,\n", "case when c.code='HYD' then 1 ELSE 0.00001 END as base_pay_batched_order_drop_amount_multiplier,\n", "10 as weight_pay_slabs_from_0,\n", "100 as weight_pay_slabs_to_0,\n", "0.5 as weight_pay_slabs_amount_0,\n", "case when carrier_id in (1,6)  then coalesce(ming_input,cast(floor(base_pay) as double)) \n", "     when carrier_id = 3  then coalesce(ming_input,cast(floor(base_pay) as double)) - 2 \n", "     when carrier_id  = 5 then coalesce(ming_input,cast(floor(base_pay) as double)) - 1\n", "     else  coalesce(ming_input,cast(floor(base_pay) as double))  end \n", " as first_order_ming_amount,\n", "case when carrier_id in (1,6) then 15 \n", "     when carrier_id = 5 then 12\n", "     when carrier_id = 3 then 10\n", "     else 12 end\n", "as batched_order_ming_amount,\n", "date_format(current_date - interval '1' day, '%%Y%%m%%d') AS dt\n", "from final f\n", "left join zomato.carthero_prod.cities c on f.city = c.city_name\n", "\n", "LEFT JOIN incentive_city ic ON f.date1=ic.account_date AND f.slot_name=ic.slot_name AND f.store_id=ic.store_id\n", "\n", "LEFT JOIN pay_buffer pf ON (f.store_id )=pf.store_id\n", "\n", "where date1 >= current_date+interval '0' day\n", ") \n", "where dt=date_format(current_date - interval '1' day, '%%Y%%m%%d')\n", "\n", "\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "bf861a88-365e-4f0a-ab0e-58dc050bc224", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"model_processing_v1\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"rule_name\", \"type\": \"varchar\", \"description\": \"rule name\"},\n", "        {\"name\": \"priority\", \"type\": \"integer\", \"description\": \"priority\"},\n", "        {\n", "            \"name\": \"rate_card_validity_start_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"rate_card_validity_start_date\",\n", "        },\n", "        {\n", "            \"name\": \"rate_card_validity_end_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"rate_card_validity_end_date\",\n", "        },\n", "        {\"name\": \"city_codes\", \"type\": \"varchar\", \"description\": \"city_codes\"},\n", "        {\"name\": \"zone_ids\", \"type\": \"varchar\", \"description\": \"zone_ids\"},\n", "        {\"name\": \"locality_ids\", \"type\": \"varchar\", \"description\": \"locality_ids\"},\n", "        {\"name\": \"carrier_id\", \"type\": \"integer\", \"description\": \"carrier_id\"},\n", "        {\"name\": \"slot_names\", \"type\": \"varchar\", \"description\": \"slot_names\"},\n", "        {\n", "            \"name\": \"accounting_tags\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"accounting_tags\",\n", "        },\n", "        {\"name\": \"day_of_week\", \"type\": \"varchar\", \"description\": \"day_of_week\"},\n", "        {\n", "            \"name\": \"base_pay_pickup_amount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"base_pay_pickup_amount\",\n", "        },\n", "        {\n", "            \"name\": \"base_pay_drop_amount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"base_pay_drop_amount\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_round_off\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_round_off\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_from\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_from\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_to\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_to\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_amount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_amount\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_amount_breakup_base_distance_pay\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_amount_breakup_base_distance_pay\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_amount_breakup_incentive_distance_pay\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_amount_breakup_incentive_distance_pay\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_drop_round_off\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"distance_pay_drop_round_off\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_from\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"distance_pay_from\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_to\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"distance_pay_to\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_amount\",\n", "            \"type\": \"double\",\n", "            \"description\": \"distance_pay_amount\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_amount_breakup_base_distance_pay\",\n", "            \"type\": \"double\",\n", "            \"description\": \"distance_pay_amount_breakup_base_distance_pay\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_amount_breakup_incentive_distance_pay\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"distance_pay_amount_breakup_incentive_distance_pay\",\n", "        },\n", "        {\n", "            \"name\": \"wait_time_pay_drop_round_off\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"wait_time_pay_drop_round_off\",\n", "        },\n", "        {\n", "            \"name\": \"wait_time_pay_from\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"wait_time_pay_from\",\n", "        },\n", "        {\n", "            \"name\": \"wait_time_pay_to\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"wait_time_pay_to\",\n", "        },\n", "        {\n", "            \"name\": \"wait_time_pay_amount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"wait_time_pay_amount\",\n", "        },\n", "        {\n", "            \"name\": \"trip_ming_amount\",\n", "            \"type\": \"double\",\n", "            \"description\": \"trip_ming_amount\",\n", "        },\n", "        {\n", "            \"name\": \"incentive_pay_multiplier\",\n", "            \"type\": \"double\",\n", "            \"description\": \"incentive_pay_multiplier\",\n", "        },\n", "        {\"name\": \"is_back_dated\", \"type\": \"varchar\", \"description\": \"is_back_dated\"},\n", "        {\n", "            \"name\": \"service_category\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"service_category\",\n", "        },\n", "        {\n", "            \"name\": \"service_category_entity_id\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"service_category_entity_id\",\n", "        },\n", "        {\n", "            \"name\": \"base_pay_batched_order_drop_amount_multiplier\",\n", "            \"type\": \"double\",\n", "            \"description\": \"base_pay_batched_order_drop_amount_multiplier\",\n", "        },\n", "        {\n", "            \"name\": \"weight_pay_slabs_from_0\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"weight_pay_slabs_from_0\",\n", "        },\n", "        {\n", "            \"name\": \"weight_pay_slabs_to_0\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"weight_pay_slabs_to_0\",\n", "        },\n", "        {\n", "            \"name\": \"weight_pay_slabs_amount_0\",\n", "            \"type\": \"double\",\n", "            \"description\": \"weight_pay_slabs_amount_0\",\n", "        },\n", "        {\n", "            \"name\": \"first_order_ming_amount\",\n", "            \"type\": \"double\",\n", "            \"description\": \"first_order_ming_amount\",\n", "        },\n", "        {\n", "            \"name\": \"batched_order_ming_amount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"batched_order_ming_amount\",\n", "        },\n", "        {\"name\": \"dt\", \"type\": \"varchar\", \"description\": \"dt\"},\n", "    ],\n", "    \"primary_key\": [\n", "        \"rule_name\",\n", "        \"dt\",\n", "        \"slot_names\",\n", "        \"carrier_id\",\n", "        \"priority\",\n", "        \"rate_card_validity_start_date\",\n", "        \"rate_card_validity_end_date\",\n", "        \"service_category\",\n", "        \"service_category_entity_id\",\n", "    ],\n", "    \"incremental_key\": \"dt\",\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"This Table has GIGS RC Config\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}\n", "pb.to_trino(sql_query, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "8bf01288-0739-4ba1-a8e4-6aa942b9f8fd", "metadata": {}, "outputs": [], "source": ["sql_query = f\"\"\"\n", "\n", "\n", "select rule_name,priority,\n", "rate_card_validity_start_date,rate_card_validity_end_date,city_codes,zone_ids,locality_ids,carrier_ids,slot_names,\n", "accounting_tags,day_of_week,base_pay_pickup_amount,\n", "case when incentive_pay_multiplier>=0.30 then base_pay_drop_amount-1.00\n", "                       ELSE base_pay_drop_amount END as base_pay_drop_amount,\n", "pickup_distance_pay_round_off,pickup_distance_pay_from,pickup_distance_pay_to,pickup_distance_pay_amount,pickup_distance_pay_amount_breakup_base_distance_pay,\n", "pickup_distance_pay_amount_breakup_incentive_distance_pay,distance_pay_drop_round_off,distance_pay_from,distance_pay_to,distance_pay_amount,\n", "distance_pay_amount_breakup_base_distance_pay,distance_pay_amount_breakup_incentive_distance_pay,wait_time_pay_drop_round_off,\n", "wait_time_pay_from,wait_time_pay_to,wait_time_pay_amount,trip_ming_amount,incentive_pay_multiplier,is_back_dated,service_category,\n", "service_category_entity_id,base_pay_batched_order_drop_amount_multiplier,weight_pay_slabs_from_0,weight_pay_slabs_to_0,weight_pay_slabs_amount_0,\n", "first_order_ming_amount,batched_order_ming_amount\n", "from (\n", "(SELECT rule_name,\n", "case when rate_card_validity_start_date=current_date+interval '0' day then priority+40 \n", "when rate_card_validity_start_date=current_date+interval '1' day then priority+30 \n", "when rate_card_validity_start_date=current_date+interval '2' day then priority+20\n", "when rate_card_validity_start_date=current_date+interval '3' day then priority+10\n", "when rate_card_validity_start_date=current_date+interval '4' day then priority\n", "ELSE 650 END AS priority,\n", "rate_card_validity_start_date,rate_card_validity_end_date,city_codes,zone_ids,\n", "locality_ids,carrier_id carrier_ids,slot_names,accounting_tags,day_of_week,base_pay_pickup_amount,\n", "base_pay_drop_amount,pickup_distance_pay_round_off,pickup_distance_pay_from,pickup_distance_pay_to,\n", "pickup_distance_pay_amount,pickup_distance_pay_amount_breakup_base_distance_pay,\n", "pickup_distance_pay_amount_breakup_incentive_distance_pay,distance_pay_drop_round_off,\n", "distance_pay_from,distance_pay_to,distance_pay_amount,distance_pay_amount_breakup_base_distance_pay,\n", "distance_pay_amount_breakup_incentive_distance_pay,wait_time_pay_drop_round_off,\n", "wait_time_pay_from,wait_time_pay_to,wait_time_pay_amount,trip_ming_amount,incentive_pay_multiplier,\n", "is_back_dated,service_category,service_category_entity_id,base_pay_batched_order_drop_amount_multiplier,\n", "weight_pay_slabs_from_0,weight_pay_slabs_to_0,weight_pay_slabs_amount_0,first_order_ming_amount,batched_order_ming_amount\n", "FROM logistics_data_etls.model_processing_v1\n", "where dt=date_format(current_date - interval '1' day, '%%Y%%m%%d')\n", "AND rate_card_validity_start_date between current_date+interval '0' day AND current_date+interval '7' day\n", ")\n", "\n", "UNION \n", "(SELECT rule_name,\n", "case when rate_card_validity_start_date=current_date+interval '0' day then priority+40 \n", "when rate_card_validity_start_date=current_date+interval '1' day then priority+30 \n", "when rate_card_validity_start_date=current_date+interval '2' day then priority+20\n", "when rate_card_validity_start_date=current_date+interval '3' day then priority+10\n", "when rate_card_validity_start_date=current_date+interval '4' day then priority\n", "ELSE 650 END AS priority,\n", "rate_card_validity_start_date,rate_card_validity_end_date,city_codes,zone_ids,\n", "locality_ids,carrier_id carrier_ids,slot_names,'b_zsp_acc_2' accounting_tags,day_of_week,base_pay_pickup_amount,\n", "base_pay_drop_amount,pickup_distance_pay_round_off,pickup_distance_pay_from,pickup_distance_pay_to,\n", "pickup_distance_pay_amount,pickup_distance_pay_amount_breakup_base_distance_pay,\n", "pickup_distance_pay_amount_breakup_incentive_distance_pay,distance_pay_drop_round_off,\n", "distance_pay_from,distance_pay_to,distance_pay_amount,distance_pay_amount_breakup_base_distance_pay,\n", "distance_pay_amount_breakup_incentive_distance_pay,wait_time_pay_drop_round_off,\n", "wait_time_pay_from,wait_time_pay_to,wait_time_pay_amount,trip_ming_amount,incentive_pay_multiplier,\n", "is_back_dated,service_category,service_category_entity_id, base_pay_batched_order_drop_amount_multiplier,\n", "weight_pay_slabs_from_0,weight_pay_slabs_to_0,weight_pay_slabs_amount_0,first_order_ming_amount,batched_order_ming_amount\n", "FROM logistics_data_etls.model_processing_v1\n", "where dt=date_format(current_date - interval '1' day, '%%Y%%m%%d')\n", "AND rate_card_validity_start_date between current_date+interval '0' day AND current_date+interval '7' day\n", "--AND carrier_id IN (1,3,6)\n", ")\n", "\n", "UNION \n", "(SELECT rule_name,\n", "case when rate_card_validity_start_date=current_date+interval '0' day then priority+40 \n", "when rate_card_validity_start_date=current_date+interval '1' day then priority+30 \n", "when rate_card_validity_start_date=current_date+interval '2' day then priority+20\n", "when rate_card_validity_start_date=current_date+interval '3' day then priority+10\n", "when rate_card_validity_start_date=current_date+interval '4' day then priority\n", "ELSE 650 END AS priority,\n", "rate_card_validity_start_date,rate_card_validity_end_date,city_codes,zone_ids,\n", "locality_ids,carrier_id carrier_ids,slot_names,'b_fleet_gigs_w' accounting_tags,day_of_week,base_pay_pickup_amount,\n", "ROUND(base_pay_drop_amount*1.1) base_pay_drop_amount,pickup_distance_pay_round_off,pickup_distance_pay_from,pickup_distance_pay_to,\n", "pickup_distance_pay_amount,pickup_distance_pay_amount_breakup_base_distance_pay,\n", "pickup_distance_pay_amount_breakup_incentive_distance_pay,distance_pay_drop_round_off,\n", "distance_pay_from,distance_pay_to,distance_pay_amount*1.2 distance_pay_amount,distance_pay_amount_breakup_base_distance_pay*1.2 distance_pay_amount_breakup_base_distance_pay,\n", "distance_pay_amount_breakup_incentive_distance_pay,wait_time_pay_drop_round_off,\n", "wait_time_pay_from,wait_time_pay_to,wait_time_pay_amount,trip_ming_amount,incentive_pay_multiplier,\n", "is_back_dated,service_category,service_category_entity_id,base_pay_batched_order_drop_amount_multiplier,\n", "weight_pay_slabs_from_0,weight_pay_slabs_to_0,weight_pay_slabs_amount_0,first_order_ming_amount,batched_order_ming_amount\n", "FROM logistics_data_etls.model_processing_v1\n", "where dt=date_format(current_date - interval '1' day, '%%Y%%m%%d')\n", "AND rate_card_validity_start_date between current_date+interval '0' day AND current_date+interval '7' day\n", "AND city_codes in ('AMD','ST','VADO'))\n", "\n", "UNION \n", "(SELECT rule_name,\n", "case when rate_card_validity_start_date=current_date+interval '0' day then priority+40 \n", "when rate_card_validity_start_date=current_date+interval '1' day then priority+30 \n", "when rate_card_validity_start_date=current_date+interval '2' day then priority+20\n", "when rate_card_validity_start_date=current_date+interval '3' day then priority+10\n", "when rate_card_validity_start_date=current_date+interval '4' day then priority\n", "ELSE 650 END AS priority,\n", "rate_card_validity_start_date,rate_card_validity_end_date,city_codes,zone_ids,\n", "locality_ids,carrier_id carrier_ids,slot_names,'b_fleet_gigs_4' accounting_tags,day_of_week,base_pay_pickup_amount,\n", "base_pay_drop_amount,pickup_distance_pay_round_off,pickup_distance_pay_from,pickup_distance_pay_to,\n", "pickup_distance_pay_amount,pickup_distance_pay_amount_breakup_base_distance_pay,\n", "pickup_distance_pay_amount_breakup_incentive_distance_pay,distance_pay_drop_round_off,\n", "distance_pay_from,distance_pay_to,distance_pay_amount,distance_pay_amount_breakup_base_distance_pay,\n", "distance_pay_amount_breakup_incentive_distance_pay,wait_time_pay_drop_round_off,\n", "wait_time_pay_from,wait_time_pay_to,wait_time_pay_amount,trip_ming_amount,incentive_pay_multiplier,\n", "is_back_dated,service_category,service_category_entity_id,base_pay_batched_order_drop_amount_multiplier,\n", "weight_pay_slabs_from_0,weight_pay_slabs_to_0,weight_pay_slabs_amount_0,first_order_ming_amount,batched_order_ming_amount\n", "FROM logistics_data_etls.model_processing_v1\n", "where dt=date_format(current_date - interval '1' day, '%%Y%%m%%d')\n", "AND rate_card_validity_start_date between current_date+interval '0' day AND current_date+interval '7' day\n", ")\n", "\n", "UNION \n", "\n", "(SELECT rule_name,\n", "case when rate_card_validity_start_date=current_date+interval '0' day then priority+40 \n", "when rate_card_validity_start_date=current_date+interval '1' day then priority+30 \n", "when rate_card_validity_start_date=current_date+interval '2' day then priority+20\n", "when rate_card_validity_start_date=current_date+interval '3' day then priority+10\n", "when rate_card_validity_start_date=current_date+interval '4' day then priority\n", "ELSE 650 END AS priority,\n", "rate_card_validity_start_date,rate_card_validity_end_date,city_codes,zone_ids,\n", "locality_ids,carrier_id carrier_ids,slot_names,'b_fleet_gigs_7' accounting_tags,day_of_week,base_pay_pickup_amount,\n", "base_pay_drop_amount,pickup_distance_pay_round_off,pickup_distance_pay_from,pickup_distance_pay_to,\n", "pickup_distance_pay_amount,pickup_distance_pay_amount_breakup_base_distance_pay,\n", "pickup_distance_pay_amount_breakup_incentive_distance_pay,distance_pay_drop_round_off,\n", "distance_pay_from,distance_pay_to,distance_pay_amount,distance_pay_amount_breakup_base_distance_pay,\n", "distance_pay_amount_breakup_incentive_distance_pay,wait_time_pay_drop_round_off,\n", "wait_time_pay_from,wait_time_pay_to,wait_time_pay_amount,trip_ming_amount,incentive_pay_multiplier,\n", "is_back_dated,service_category,service_category_entity_id,base_pay_batched_order_drop_amount_multiplier,\n", "weight_pay_slabs_from_0,weight_pay_slabs_to_0,weight_pay_slabs_amount_0,first_order_ming_amount,batched_order_ming_amount\n", "FROM logistics_data_etls.model_processing_v1\n", "where dt=date_format(current_date - interval '1' day, '%%Y%%m%%d')\n", "AND rate_card_validity_start_date between current_date+interval '0' day AND current_date+interval '7' day\n", ")\n", ")\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "1f977ccf-e1c9-4d3b-ae23-0d5cb811df05", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"model_processing_v2\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"rule_name\", \"type\": \"varchar\", \"description\": \"rule name\"},\n", "        {\"name\": \"priority\", \"type\": \"integer\", \"description\": \"priority\"},\n", "        {\n", "            \"name\": \"rate_card_validity_start_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"rate_card_validity_start_date\",\n", "        },\n", "        {\n", "            \"name\": \"rate_card_validity_end_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"rate_card_validity_end_date\",\n", "        },\n", "        {\"name\": \"city_codes\", \"type\": \"varchar\", \"description\": \"city_codes\"},\n", "        {\"name\": \"zone_ids\", \"type\": \"varchar\", \"description\": \"zone_ids\"},\n", "        {\"name\": \"locality_ids\", \"type\": \"varchar\", \"description\": \"locality_ids\"},\n", "        {\"name\": \"carrier_ids\", \"type\": \"integer\", \"description\": \"carrier_id\"},\n", "        {\"name\": \"slot_names\", \"type\": \"varchar\", \"description\": \"slot_names\"},\n", "        {\n", "            \"name\": \"accounting_tags\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"accounting_tags\",\n", "        },\n", "        {\"name\": \"day_of_week\", \"type\": \"varchar\", \"description\": \"day_of_week\"},\n", "        {\n", "            \"name\": \"base_pay_pickup_amount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"base_pay_pickup_amount\",\n", "        },\n", "        {\n", "            \"name\": \"base_pay_drop_amount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"base_pay_drop_amount\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_round_off\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_round_off\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_from\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_from\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_to\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_to\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_amount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_amount\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_amount_breakup_base_distance_pay\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_amount_breakup_base_distance_pay\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_amount_breakup_incentive_distance_pay\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_amount_breakup_incentive_distance_pay\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_drop_round_off\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"distance_pay_drop_round_off\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_from\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"distance_pay_from\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_to\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"distance_pay_to\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_amount\",\n", "            \"type\": \"double\",\n", "            \"description\": \"distance_pay_amount\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_amount_breakup_base_distance_pay\",\n", "            \"type\": \"double\",\n", "            \"description\": \"distance_pay_amount_breakup_base_distance_pay\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_amount_breakup_incentive_distance_pay\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"distance_pay_amount_breakup_incentive_distance_pay\",\n", "        },\n", "        {\n", "            \"name\": \"wait_time_pay_drop_round_off\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"wait_time_pay_drop_round_off\",\n", "        },\n", "        {\n", "            \"name\": \"wait_time_pay_from\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"wait_time_pay_from\",\n", "        },\n", "        {\n", "            \"name\": \"wait_time_pay_to\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"wait_time_pay_to\",\n", "        },\n", "        {\n", "            \"name\": \"wait_time_pay_amount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"wait_time_pay_amount\",\n", "        },\n", "        {\n", "            \"name\": \"trip_ming_amount\",\n", "            \"type\": \"double\",\n", "            \"description\": \"trip_ming_amount\",\n", "        },\n", "        {\n", "            \"name\": \"incentive_pay_multiplier\",\n", "            \"type\": \"double\",\n", "            \"description\": \"incentive_pay_multiplier\",\n", "        },\n", "        {\"name\": \"is_back_dated\", \"type\": \"varchar\", \"description\": \"is_back_dated\"},\n", "        {\n", "            \"name\": \"service_category\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"service_category\",\n", "        },\n", "        {\n", "            \"name\": \"service_category_entity_id\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"service_category_entity_id\",\n", "        },\n", "        {\n", "            \"name\": \"base_pay_batched_order_drop_amount_multiplier\",\n", "            \"type\": \"double\",\n", "            \"description\": \"base_pay_batched_order_drop_amount_multiplier\",\n", "        },\n", "        {\n", "            \"name\": \"weight_pay_slabs_from_0\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"weight_pay_slabs_from_0\",\n", "        },\n", "        {\n", "            \"name\": \"weight_pay_slabs_to_0\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"weight_pay_slabs_to_0\",\n", "        },\n", "        {\n", "            \"name\": \"weight_pay_slabs_amount_0\",\n", "            \"type\": \"double\",\n", "            \"description\": \"weight_pay_slabs_amount_0\",\n", "        },\n", "        {\n", "            \"name\": \"first_order_ming_amount\",\n", "            \"type\": \"double\",\n", "            \"description\": \"first_order_ming_amount\",\n", "        },\n", "        {\n", "            \"name\": \"batched_order_ming_amount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"batched_order_ming_amount\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\n", "        \"rule_name\",\n", "        \"slot_names\",\n", "        \"carrier_ids\",\n", "        \"priority\",\n", "        \"rate_card_validity_start_date\",\n", "        \"rate_card_validity_end_date\",\n", "        \"service_category\",\n", "        \"service_category_entity_id\",\n", "    ],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"This Table has GIGS RC Final Upload Value\",\n", "}\n", "pb.to_trino(sql_query, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "fa716e9b-f4d5-4124-bb07-728d8678e40b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}