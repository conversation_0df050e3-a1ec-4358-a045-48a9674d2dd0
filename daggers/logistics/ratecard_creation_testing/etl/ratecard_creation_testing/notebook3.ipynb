{"cells": [{"cell_type": "code", "execution_count": null, "id": "69af8980-6bba-4cd5-a70d-413e1dbd6b73", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import json\n", "import warnings\n", "import math\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "8bf01288-0739-4ba1-a8e4-6aa942b9f8fd", "metadata": {}, "outputs": [], "source": ["sql_query = f\"\"\"\n", "\n", "select * , current_date dt \n", "from\n", "logistics_data_etls.model_processing_v2\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "1f977ccf-e1c9-4d3b-ae23-0d5cb811df05", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"model_processed_output_v1\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"rule_name\", \"type\": \"varchar\", \"description\": \"rule name\"},\n", "        {\"name\": \"priority\", \"type\": \"integer\", \"description\": \"priority\"},\n", "        {\n", "            \"name\": \"rate_card_validity_start_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"rate_card_validity_start_date\",\n", "        },\n", "        {\n", "            \"name\": \"rate_card_validity_end_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"rate_card_validity_end_date\",\n", "        },\n", "        {\"name\": \"city_codes\", \"type\": \"varchar\", \"description\": \"city_codes\"},\n", "        {\"name\": \"zone_ids\", \"type\": \"varchar\", \"description\": \"zone_ids\"},\n", "        {\"name\": \"locality_ids\", \"type\": \"varchar\", \"description\": \"locality_ids\"},\n", "        {\"name\": \"carrier_ids\", \"type\": \"integer\", \"description\": \"carrier_id\"},\n", "        {\"name\": \"slot_names\", \"type\": \"varchar\", \"description\": \"slot_names\"},\n", "        {\n", "            \"name\": \"accounting_tags\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"accounting_tags\",\n", "        },\n", "        {\"name\": \"day_of_week\", \"type\": \"varchar\", \"description\": \"day_of_week\"},\n", "        {\n", "            \"name\": \"base_pay_pickup_amount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"base_pay_pickup_amount\",\n", "        },\n", "        {\n", "            \"name\": \"base_pay_drop_amount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"base_pay_drop_amount\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_round_off\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_round_off\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_from\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_from\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_to\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_to\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_amount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_amount\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_amount_breakup_base_distance_pay\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_amount_breakup_base_distance_pay\",\n", "        },\n", "        {\n", "            \"name\": \"pickup_distance_pay_amount_breakup_incentive_distance_pay\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"pickup_distance_pay_amount_breakup_incentive_distance_pay\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_drop_round_off\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"distance_pay_drop_round_off\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_from\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"distance_pay_from\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_to\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"distance_pay_to\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_amount\",\n", "            \"type\": \"double\",\n", "            \"description\": \"distance_pay_amount\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_amount_breakup_base_distance_pay\",\n", "            \"type\": \"double\",\n", "            \"description\": \"distance_pay_amount_breakup_base_distance_pay\",\n", "        },\n", "        {\n", "            \"name\": \"distance_pay_amount_breakup_incentive_distance_pay\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"distance_pay_amount_breakup_incentive_distance_pay\",\n", "        },\n", "        {\n", "            \"name\": \"wait_time_pay_drop_round_off\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"wait_time_pay_drop_round_off\",\n", "        },\n", "        {\n", "            \"name\": \"wait_time_pay_from\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"wait_time_pay_from\",\n", "        },\n", "        {\n", "            \"name\": \"wait_time_pay_to\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"wait_time_pay_to\",\n", "        },\n", "        {\n", "            \"name\": \"wait_time_pay_amount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"wait_time_pay_amount\",\n", "        },\n", "        {\n", "            \"name\": \"trip_ming_amount\",\n", "            \"type\": \"double\",\n", "            \"description\": \"trip_ming_amount\",\n", "        },\n", "        {\n", "            \"name\": \"incentive_pay_multiplier\",\n", "            \"type\": \"double\",\n", "            \"description\": \"incentive_pay_multiplier\",\n", "        },\n", "        {\"name\": \"is_back_dated\", \"type\": \"varchar\", \"description\": \"is_back_dated\"},\n", "        {\n", "            \"name\": \"service_category\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"service_category\",\n", "        },\n", "        {\n", "            \"name\": \"service_category_entity_id\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"service_category_entity_id\",\n", "        },\n", "        {\n", "            \"name\": \"base_pay_batched_order_drop_amount_multiplier\",\n", "            \"type\": \"double\",\n", "            \"description\": \"base_pay_batched_order_drop_amount_multiplier\",\n", "        },\n", "        {\n", "            \"name\": \"weight_pay_slabs_from_0\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"weight_pay_slabs_from_0\",\n", "        },\n", "        {\n", "            \"name\": \"weight_pay_slabs_to_0\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"weight_pay_slabs_to_0\",\n", "        },\n", "        {\n", "            \"name\": \"weight_pay_slabs_amount_0\",\n", "            \"type\": \"double\",\n", "            \"description\": \"weight_pay_slabs_amount_0\",\n", "        },\n", "        {\n", "            \"name\": \"first_order_ming_amount\",\n", "            \"type\": \"double\",\n", "            \"description\": \"first_order_ming_amount\",\n", "        },\n", "        {\n", "            \"name\": \"batched_order_ming_amount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"batched_order_ming_amount\",\n", "        },\n", "        {\n", "            \"name\": \"dt\",\n", "            \"type\": \"date\",\n", "            \"description\": \"timestamp\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\n", "        \"rule_name\",\n", "        \"slot_names\",\n", "        \"carrier_ids\",\n", "        \"priority\",\n", "        \"accounting_tags\",\n", "        \"rate_card_validity_start_date\",\n", "        \"rate_card_validity_end_date\",\n", "        \"service_category\",\n", "        \"service_category_entity_id\",\n", "        \"dt\",\n", "    ],\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"This Table has GIGS RC Final Upload Value\",\n", "    \"partition_key\": [\"dt\"],\n", "}\n", "pb.to_trino(sql_query, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "fa716e9b-f4d5-4124-bb07-728d8678e40b", "metadata": {}, "outputs": [], "source": ["df = pd.read_sql_query(\n", "    sql=f\"\"\"\n", "\n", "\n", "with raw_base as (\n", "    SELECT \n", "        a.*,\n", "        DATE_DIFF('second', CAST('1970-01-01' AS TIMESTAMP), CAST(rate_card_validity_start_date AS TIMESTAMP)) - 19800 AS start_epoch,\n", "        DATE_DIFF('second', CAST('1970-01-01' AS TIMESTAMP), CAST(rate_card_validity_end_date AS TIMESTAMP)) - 19800 + 86399 AS end_epoch,\n", "        n.id tag_id,\n", "        '' zone_id,\n", "        DATE_FORMAT((CURRENT_DATE),'%%Y%%m%%d') as dt\n", "    FROM \n", "        logistics_data_etls.model_processing_v2 a\n", "        INNER JOIN (SELECT id,name FROM zomato.driver_service.tags) n ON a.accounting_tags=n.name\n", "        LEFT JOIN \n", "        (\n", "            SELECT \n", "                store_id,\n", "                max_by(zone_id,inserted_at_ist) zone_id\n", "            FROM \n", "                logistics_data_etls.blinkit_store_mapping\n", "            WHERE \n", "                inserted_at_ist>=current_date-interval '15' day\n", "            GROUP BY 1\n", "        ) c on a.service_category_entity_id=c.store_id\n", "    WHERE \n", "        rate_card_validity_start_date between current_date and current_date + interval '3' day \n", "        and n.id not in (840, 860, 865, 1158)\n", "),\n", "\n", "\n", "base as(\n", "    SELECT\n", "        b.*\n", "    from \n", "        raw_base b \n", " \n", "),\n", "rc_formatted as (\n", "    SELECT \n", "        *,\n", "        CAST(MAP_FROM_ENTRIES(ARRAY[\n", "                                ('city', CAST(ARRAY[city_codes] AS JSON)),\n", "                                ('localityId', IF(TRY_CAST(locality_ids AS INT) IS NULL, CAST(ARRAY[] AS JSON), CAST(ARRAY[TRY_CAST(locality_ids AS INT) ] AS JSON))),\n", "                                ('carrierId', CAST(ARRAY[TRY_CAST(carrier_ids AS INT)] AS JSON)),\n", "                                ('tag', IF(TRY_CAST(tag_id AS INT) IS NULL, CAST(ARRAY[] AS JSON), CAST(ARRAY[TRY_CAST(tag_id AS INT) ] AS JSON))),\n", "                                ('slotName', CAST(ARRAY[slot_names] AS JSON)),\n", "                                ('serviceCategory', CAST(service_category as JSON)),\n", "                                ('serviceCategoryEntityId', CAST(ARRAY[TRY_CAST(service_category_entity_id AS INT)] AS JSON))\n", "                               ]) AS JSON) AS rule_conditions,\n", "        CAST(MAP_FROM_ENTRIES(ARRAY[\n", "                                    ('time_zone', CAST('Asia/Kolkata' AS JSON)),\n", "                                    ('start_date_epoch_second', CAST(TRY_CAST(start_epoch AS BIGINT) AS JSON)),\n", "                                    ('end_date_epoch_second', CAST(TRY_CAST(end_epoch AS BIGINT) AS JSON))\n", "                                ]) AS JSON) AS validity_period,\n", "        CAST(MAP_FROM_ENTRIES(ARRAY[\n", "                                    ('pickup_amount', CAST(TRY_CAST(base_pay_pickup_amount AS REAL) AS JSON)),\n", "                                    ('drop_amount', CAST(TRY_CAST(base_pay_drop_amount AS REAL) AS JSON)),\n", "                                    ('batched_order_drop_amount_multiplier', CAST(TRY_CAST(base_pay_batched_order_drop_amount_multiplier AS REAL) AS JSON))\n", "                                ]) AS JSON) AS base_pay,\n", "        CAST(MAP_FROM_ENTRIES(ARRAY[\n", "                                    ('round_off', CAST(TRY_CAST(pickup_distance_pay_round_off AS REAL) AS JSON)),\n", "                                    ('from', CAST(TRY_CAST(pickup_distance_pay_from AS REAL) AS JSON)),\n", "                                    ('to', CAST(TRY_CAST(pickup_distance_pay_to AS REAL) AS JSON)),\n", "                                    ('amount', CAST(TRY_CAST(pickup_distance_pay_amount AS REAL) AS JSON))\n", "                                ]) AS JSON) AS pd_pay,\n", "        CAST(MAP_FROM_ENTRIES(ARRAY[\n", "                                    ('round_off', CAST(TRY_CAST(wait_time_pay_drop_round_off AS REAL) AS JSON)),\n", "                                    ('from', CAST(TRY_CAST(wait_time_pay_from AS REAL) AS JSON)),\n", "                                    ('to', CAST(TRY_CAST(wait_time_pay_to AS REAL) AS JSON)),\n", "                                    ('amount', CAST(TRY_CAST(wait_time_pay_amount AS REAL) AS JSON))\n", "                                ]) AS JSON) AS wait_time_pay,\n", "        CAST(MAP_FROM_ENTRIES(ARRAY[\n", "                                    ('round_off', CAST(TRY_CAST(pickup_distance_pay_amount_breakup_base_distance_pay AS REAL) AS JSON)),\n", "                                    ('from', CAST(TRY_CAST(pickup_distance_pay_amount_breakup_incentive_distance_pay AS REAL) AS JSON)),\n", "                                    ('to', CAST(TRY_CAST(distance_pay_to AS REAL) AS JSON)),\n", "                                    ('amount', CAST(TRY_CAST(distance_pay_amount AS REAL) AS JSON)),\n", "                                    ('amount_breakup', CAST(MAP_FROM_ENTRIES(ARRAY[\n", "                                                                                ('base_distance_pay', CAST(TRY_CAST(distance_pay_amount_breakup_base_distance_pay AS REAL) AS JSON)),\n", "                                                                                ('incentive_distance_pay', CAST(TRY_CAST(distance_pay_amount_breakup_incentive_distance_pay AS REAL) AS JSON))\n", "                                                                                ]) AS JSON))\n", "                                ]) AS JSON) AS distance_pay,\n", "        CAST(MAP_FROM_ENTRIES(ARRAY[\n", "                                    ('from', CAST(TRY_CAST(weight_pay_slabs_from_0 AS REAL) AS JSON)),\n", "                                    ('to', CAST(TRY_CAST(weight_pay_slabs_to_0 AS REAL) AS JSON)),\n", "                                    ('amount', CAST(TRY_CAST(weight_pay_slabs_amount_0 AS REAL) AS JSON))\n", "                                ]) AS JSON) AS weight_pay_slabs\n", "    FROM   \n", "        base\n", ")\n", "SELECT \n", "    *,\n", "    CAST(MAP_FROM_ENTRIES(ARRAY[\n", "                                ('rule_name', CAST(TRY_CAST(rule_name AS varchar) AS JSON)),\n", "                                ('priority', CAST(TRY_CAST(priority AS INT) AS JSON)),\n", "                                ('trip_ming_amount', CAST(TRY_CAST(trip_ming_amount AS REAL) AS JSON)),\n", "                                ('incentive_pay_multiplier', CAST(TRY_CAST(incentive_pay_multiplier AS REAL) AS JSON)),\n", "                                ('first_order_ming_amount', CAST(TRY_CAST(first_order_ming_amount AS INT) AS JSON)),\n", "                                ('batched_order_ming_amount', CAST(TRY_CAST(batched_order_ming_amount AS INT) AS JSON)),\n", "                                ('validity_period', validity_period),\n", "                                ('rule_conditions', rule_conditions),\n", "                                ('base_pay', base_pay),\n", "                                ('distance_pay', distance_pay),\n", "                                ('pickup_distance_pay', pd_pay),\n", "                                ('wait_time_pay', wait_time_pay),\n", "                                ('weight_pay_slabs', weight_pay_slabs)\n", "                            ]) AS JSON) AS json_obj\n", "FROM \n", "    rc_formatted\n", "\n", "\n", "      \n", "\"\"\",\n", "    con=pb.get_connection(\"[Warehouse] Trino\"),\n", ")\n", "\n", "df"]}, {"cell_type": "code", "execution_count": null, "id": "3a55517d-197d-42d8-91cf-dcba1c0bae69", "metadata": {}, "outputs": [], "source": ["jumbo_columns = [\n", "    \"rule_name\",\n", "    \"priority\",\n", "    \"rate_card_validity_start_date\",\n", "    \"rate_card_validity_end_date\",\n", "    \"city_codes\",\n", "    \"zone_ids\",\n", "    \"locality_ids\",\n", "    \"carrier_ids\",\n", "    \"slot_names\",\n", "    \"accounting_tags\",\n", "    \"day_of_week\",\n", "    \"base_pay_pickup_amount\",\n", "    \"base_pay_drop_amount\",\n", "    \"pickup_distance_pay_round_off\",\n", "    \"pickup_distance_pay_from\",\n", "    \"pickup_distance_pay_to\",\n", "    \"pickup_distance_pay_amount\",\n", "    \"pickup_distance_pay_amount_breakup_base_distance_pay\",\n", "    \"pickup_distance_pay_amount_breakup_incentive_distance_pay\",\n", "    \"distance_pay_drop_round_off\",\n", "    \"distance_pay_from\",\n", "    \"distance_pay_to\",\n", "    \"distance_pay_amount\",\n", "    \"distance_pay_amount_breakup_base_distance_pay\",\n", "    \"distance_pay_amount_breakup_incentive_distance_pay\",\n", "    \"wait_time_pay_drop_round_off\",\n", "    \"wait_time_pay_from\",\n", "    \"wait_time_pay_to\",\n", "    \"wait_time_pay_amount\",\n", "    \"trip_ming_amount\",\n", "    \"incentive_pay_multiplier\",\n", "    \"is_back_dated\",\n", "    \"service_category\",\n", "    \"service_category_entity_id\",\n", "    \"base_pay_batched_order_drop_amount_multiplier\",\n", "    \"weight_pay_slabs_from_0\",\n", "    \"weight_pay_slabs_to_0\",\n", "    \"weight_pay_slabs_amount_0\",\n", "    \"first_order_ming_amount\",\n", "    \"batched_order_ming_amount\",\n", "    \"dt\",\n", "]\n", "\n", "\n", "kafka_columns = [\n", "    \"base_pay\",\n", "    \"distance_pay\",\n", "    \"incentive_pay_multiplier\",\n", "    \"pickup_distance_pay\",\n", "    \"priority\",\n", "    \"rule_conditions\",\n", "    \"rule_name\",\n", "    \"trip_ming_amount\",\n", "    \"first_order_ming_amount\",\n", "    \"batched_order_ming_amount\",\n", "    \"validity_period\",\n", "    \"wait_time_pay\",\n", "    \"weight_pay_slabs\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "adbb762a-56cf-4322-ad79-904e3a134736", "metadata": {}, "outputs": [], "source": ["jumbo_df = df[jumbo_columns]\n", "kafka_df = df[[\"json_obj\"]]\n", "kafka_df[\"base_pay\"] = kafka_df[\"json_obj\"].apply(lambda x: json.loads(x)[\"base_pay\"])\n", "kafka_df[\"distance_pay\"] = kafka_df[\"json_obj\"].apply(lambda x: json.loads(x)[\"distance_pay\"])\n", "kafka_df[\"incentive_pay_multiplier\"] = kafka_df[\"json_obj\"].apply(\n", "    lambda x: json.loads(x)[\"incentive_pay_multiplier\"]\n", ")\n", "kafka_df[\"pickup_distance_pay\"] = kafka_df[\"json_obj\"].apply(\n", "    lambda x: json.loads(x)[\"pickup_distance_pay\"]\n", ")\n", "kafka_df[\"priority\"] = kafka_df[\"json_obj\"].apply(lambda x: json.loads(x)[\"priority\"])\n", "kafka_df[\"rule_conditions\"] = kafka_df[\"json_obj\"].apply(lambda x: json.loads(x)[\"rule_conditions\"])\n", "kafka_df[\"rule_name\"] = kafka_df[\"json_obj\"].apply(lambda x: json.loads(x)[\"rule_name\"])\n", "kafka_df[\"trip_ming_amount\"] = kafka_df[\"json_obj\"].apply(\n", "    lambda x: json.loads(x)[\"trip_ming_amount\"]\n", ")\n", "kafka_df[\"first_order_ming_amount\"] = kafka_df[\"json_obj\"].apply(\n", "    lambda x: json.loads(x)[\"first_order_ming_amount\"]\n", ")\n", "kafka_df[\"batched_order_ming_amount\"] = kafka_df[\"json_obj\"].apply(\n", "    lambda x: json.loads(x)[\"batched_order_ming_amount\"]\n", ")\n", "kafka_df[\"validity_period\"] = kafka_df[\"json_obj\"].apply(lambda x: json.loads(x)[\"validity_period\"])\n", "kafka_df[\"wait_time_pay\"] = kafka_df[\"json_obj\"].apply(lambda x: json.loads(x)[\"wait_time_pay\"])\n", "kafka_df[\"weight_pay_slabs\"] = kafka_df[\"json_obj\"].apply(\n", "    lambda x: [json.loads(x)[\"weight_pay_slabs\"]]\n", ")\n", "\n", "kafka_df = kafka_df[kafka_columns]"]}, {"cell_type": "code", "execution_count": null, "id": "b0ee9472-cc8b-4b7e-ae13-998cfdfca4bc", "metadata": {}, "outputs": [], "source": ["kafka_df.head(3)\n", "kafka_topic = \"blinkit.logistics-insights-service.etl.rate-card\"\n", "conn_id = \"[Kafka] prod-data-events\""]}, {"cell_type": "code", "execution_count": null, "id": "7ad9a09a-1bc9-4fac-9819-d1b839fdf3bf", "metadata": {}, "outputs": [], "source": ["kafka_df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "8ce46161-0903-4ae8-910c-682b207aece2", "metadata": {}, "outputs": [], "source": ["pb.to_kafka(conn_id=conn_id, topic=kafka_topic, df=kafka_df)"]}, {"cell_type": "code", "execution_count": null, "id": "55ee2412-e27b-43af-be2c-01cfeca3be84", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}