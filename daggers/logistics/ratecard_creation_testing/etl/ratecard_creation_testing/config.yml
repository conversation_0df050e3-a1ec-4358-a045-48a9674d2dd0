alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 3
dag_name: ratecard_creation_testing
dag_type: etl
escalation_priority: low
execution_timeout: 800
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    service_account_name: blinkit-prep-airflow-prep-prep-eks-role
    volume_mounts: []
  type: kubernetes
namespace: logistics
notebooks:
- executor_config:
    load_type: low
    node_type: spot
  name: notebook1
  parameters: null
  retries: 4
  tag: first
- executor_config:
    load_type: low
    node_type: spot
  name: notebook2
  parameters: null
  retries: 4
  tag: second
- executor_config:
    load_type: low
    node_type: spot
  name: notebook3
  parameters: null
  retries: 4
  tag: third

owner:
  email: <EMAIL>
  slack_id: U060684S18V
path: logistics/ratecard_creation_testing/etl/ratecard_creation_testing
paused: false
pool: logistics_pool
project_name: ratecard_creation_testing
schedule:
  end_date: '2025-07-10T00:00:00'
  interval: 30 19 * * *
  start_date: '2025-04-16T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 1
