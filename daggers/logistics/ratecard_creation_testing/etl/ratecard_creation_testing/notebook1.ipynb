{"cells": [{"cell_type": "code", "execution_count": null, "id": "e9c364b5-9e95-4135-83df-4094de7077a0", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "id": "93ede6f1-14d5-4d71-ba0c-a44694e66d2d", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1m57ZvapiLwqsf1HV5Y2JIE5rStUYXzrqG14u4VC2WQ8\"\n", "sheet_name = \"cpk_input\"\n", "cpk_input = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "5221cc55-05bb-414b-88b5-1ff4e0c34481", "metadata": {}, "outputs": [], "source": ["cpk_input.columns\n", "dup_cols = [\"entity_type\", \"entity_name\", \"entity_id\", \"carrier_id\"]\n", "\n", "# use this dataframe to send the slack alerts\n", "duplicates = cpk_input[cpk_input.duplicated(subset=dup_cols, keep=False)]\n", "\n", "cpk_input = cpk_input.drop_duplicates(subset=dup_cols, keep=\"first\")\n", "\n", "cpk_input.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "73de37c8-5c29-4156-bf5e-d81e298ae0a0", "metadata": {}, "outputs": [], "source": ["cpk_input = cpk_input.astype(\n", "    {\n", "        \"entity_type\": \"string\",\n", "        \"entity_name\": \"string\",\n", "        \"entity_id\": \"int64\",\n", "        \"carrier_id\": \"int64\",\n", "        \"weekly_cpk\": \"float64\",\n", "        \"cpo_buffer\": \"float64\",\n", "        \"dd_input\": \"float64\",\n", "        \"dd_approval\": \"string\",\n", "        \"dd_buffer_flex\": \"float64\",\n", "        \"dd_buffer_gigs\": \"float64\",\n", "        \"ming_input\": \"float64\",\n", "        \"new_model_mix\": \"float64\",\n", "        \"model_type\": \"string\",\n", "    }\n", ")\n", "cpk_input"]}, {"cell_type": "code", "execution_count": null, "id": "b22ef0d2-d55b-4791-8e17-56eaa761a028", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"model_cpk_input_v1\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"entity_type\", \"type\": \"varchar\", \"description\": \"entity_type\"},\n", "        {\"name\": \"entity_name\", \"type\": \"varchar\", \"description\": \"entity_name\"},\n", "        {\"name\": \"entity_id\", \"type\": \"integer\", \"description\": \"entity_id\"},\n", "        {\"name\": \"carrier_id\", \"type\": \"integer\", \"description\": \"carrier_id\"},\n", "        {\"name\": \"weekly_cpk\", \"type\": \"double\", \"description\": \"weekly_cpk\"},\n", "        {\"name\": \"cpo_buffer\", \"type\": \"double\", \"description\": \"cpo_buffer\"},\n", "        {\"name\": \"dd_input\", \"type\": \"double\", \"description\": \"dd_input\"},\n", "        {\"name\": \"dd_approval\", \"type\": \"varchar\", \"description\": \"dd_approval\"},\n", "        {\"name\": \"dd_buffer_flex\", \"type\": \"double\", \"description\": \"dd_buffer_flex\"},\n", "        {\"name\": \"dd_buffer_gigs\", \"type\": \"double\", \"description\": \"dd_buffer_gigs\"},\n", "        {\"name\": \"ming_input\", \"type\": \"double\", \"description\": \"ming_input\"},\n", "        {\"name\": \"new_model_mix\", \"type\": \"double\", \"description\": \"new_model_mix\"},\n", "        {\"name\": \"model_type\", \"type\": \"varchar\", \"description\": \"model_type\"},\n", "    ],\n", "    \"primary_key\": [\"entity_type\", \"entity_name\", \"entity_id\", \"carrier_id\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"cpk input\",\n", "}\n", "pb.to_trino(cpk_input, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "5454326d-16b7-4c24-b7da-412a3f8b3578", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1m57ZvapiLwqsf1HV5Y2JIE5rStUYXzrqG14u4VC2WQ8\"\n", "sheet_name = \"multiplier\"\n", "multiplier = pb.from_sheets(sheet_id, sheet_name)\n", "\n", "\n", "multiplier[\"start_date\"] = pd.to_datetime(multiplier[\"start_date\"]).dt.date\n", "multiplier[\"end_date\"] = pd.to_datetime(multiplier[\"end_date\"]).dt.date\n", "\n", "multiplier = multiplier.astype(\n", "    {\n", "        \"store_name\": \"string\",\n", "        \"store_id\": \"int64\",\n", "        \"start_hour\": \"int64\",\n", "        \"end_hour\": \"int64\",\n", "        \"incentive_multiplier\": \"int64\",\n", "        \"approval\": \"string\",\n", "        \"tag_flag\": \"string\",\n", "    }\n", ")\n", "multiplier.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "4fbc15b3-5f27-424c-a274-ef5087903dc9", "metadata": {}, "outputs": [], "source": ["multiplier"]}, {"cell_type": "code", "execution_count": null, "id": "6f720ae9-21a5-40af-9162-935ff3317019", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"incentive_multiplier_input_v1\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"store_name\", \"type\": \"varchar\", \"description\": \"store_name\"},\n", "        {\"name\": \"store_id\", \"type\": \"integer\", \"description\": \"store_id\"},\n", "        {\"name\": \"start_hour\", \"type\": \"integer\", \"description\": \"start_hour\"},\n", "        {\"name\": \"end_hour\", \"type\": \"integer\", \"description\": \"end_hour\"},\n", "        {\"name\": \"start_date\", \"type\": \"date\", \"description\": \"start_date\"},\n", "        {\"name\": \"end_date\", \"type\": \"date\", \"description\": \"end_date\"},\n", "        {\n", "            \"name\": \"incentive_multiplier\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"incentive_multiplier\",\n", "        },\n", "        {\"name\": \"approval\", \"type\": \"varchar\", \"description\": \"approval\"},\n", "        {\"name\": \"tag_flag\", \"type\": \"varchar\", \"description\": \"tag_flag\"},\n", "    ],\n", "    \"primary_key\": [\"store_id\", \"start_hour\", \"end_hour\", \"start_date\", \"end_date\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"Incentive Multiplier for FLEX\",\n", "}\n", "pb.to_trino(multiplier, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "2ebb5e08-3e23-49dc-9126-f05c1e4f0291", "metadata": {}, "outputs": [], "source": ["multiplier"]}, {"cell_type": "code", "execution_count": null, "id": "767c32fd-1f41-4b3a-bc9c-412e3f7dbb83", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1m57ZvapiLwqsf1HV5Y2JIE5rStUYXzrqG14u4VC2WQ8\"\n", "sheet_name = \"skew\"\n", "skew = pb.from_sheets(sheet_id, sheet_name)\n", "\n", "skew.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "7f6a1ac4-3d09-4d4a-8d30-ce679576421a", "metadata": {}, "outputs": [], "source": ["skew.columns"]}, {"cell_type": "code", "execution_count": null, "id": "26020710-fdd7-4a9d-9e57-51b9857838e1", "metadata": {}, "outputs": [], "source": ["skew = skew.astype(\n", "    {\n", "        \"entity_type\": \"string\",\n", "        \"entity_name\": \"string\",\n", "        \"dow\": \"int64\",\n", "        \"hour\": \"int64\",\n", "        \"cpk_factor\": \"float64\",\n", "    }\n", ")\n", "skew.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "b0936bce-5922-4833-b9d5-190433908dcf", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"model_skew_input_v1\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"entity_type\", \"type\": \"varchar\", \"description\": \"entity_type\"},\n", "        {\"name\": \"entity_name\", \"type\": \"varchar\", \"description\": \"entity_name\"},\n", "        {\"name\": \"dow\", \"type\": \"integer\", \"description\": \"day of week\"},\n", "        {\"name\": \"hour\", \"type\": \"integer\", \"description\": \"hour\"},\n", "        {\"name\": \"cpk_factor\", \"type\": \"double\", \"description\": \"cpk factor value\"},\n", "    ],\n", "    \"primary_key\": [\"entity_type\", \"entity_name\", \"hour\", \"dow\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"store x dow x hour : skew factor \",\n", "}\n", "pb.to_trino(skew, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "1bd32a62-f7ec-4d25-b3e5-ea465e52da71", "metadata": {}, "outputs": [], "source": ["gigs_info = pd.read_sql_query(\n", "    sql=f\"\"\"\n", "\n", "  with store as (\n", "     \n", "    SELECT distinct \n", "        u.id as store_id,\n", "        u.name as store_name,\n", "        try_cast(u.external_id as integer) as blinkit_store_id,\n", "        ua.city_id,\n", "        c.code as city_code,\n", "        c.city_name\n", "    from \n", "        zomato.carthero_prod.users u \n", "        join zomato.carthero_prod.user_addresses ua on ua.user_id = u.id and active = 1\n", "        join zomato.carthero_prod.cities c on c.id = ua.city_id \n", "    where \n", "        source_id = 17110 and\n", "        try_cast(u.external_id as integer) is not null\n", "        and lower(u.name) not like ('%%hyperlocal%%')\n", "    )\n", "    \n", "    \n", "    select date , external_store_id , slot_id , slot_capacity_id , slot_name , \n", "    start_time , case when end_time = 0 and start_time = 23 then 24 else end_time end as end_time , \n", "    city_name \n", "    from \n", "   (  SELECT distinct \n", "        date(sds.date + INTERVAL '330' MINUTE ) as date,\n", "        sds.entity_id as external_store_id,\n", "        sds.slot_id,\n", "        sds.id as slot_capacity_id,\n", "        sl.name as slot_name, \n", "        hour(cast(from_unixtime(sl.start_time_seconds_since_midnight) as time) - interval '330' minute  ) as start_time,\n", "        \n", "        hour(cast(from_unixtime(sl.end_time_seconds_since_midnight) as time ) - interval '330' minute) as end_time ,\n", "        \n", "        city_name\n", "    from\n", "        zomato.driver_shift.slots_daily_capacities sds\n", "        join zomato.driver_shift.slots sl on sl.id = sds.slot_id\n", "        join store s on sds.entity_id = s.store_id\n", "    where\n", "        date(date + INTERVAL '330' MINUTE) between CURRENT_DATE and CURRENT_DATE + INTERVAL '7' DAY\n", " \n", "        and carrier_type = 1 \n", "       \n", ")\n", "\n", "      \n", "\"\"\",\n", "    con=pb.get_connection(\"[Warehouse] Trino\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4cb63c19-d162-4cde-a3c5-52b70ae39d47", "metadata": {}, "outputs": [], "source": ["gigs_info\n", "\n", "gigs_info[\"date\"] = pd.to_datetime(gigs_info[\"date\"])\n", "\n", "gigs_info = gigs_info.astype(\n", "    {\n", "        \"external_store_id\": \"int64\",\n", "        \"slot_id\": \"int64\",\n", "        \"slot_capacity_id\": \"int64\",\n", "        \"slot_name\": \"string\",\n", "        \"start_time\": \"int64\",\n", "        \"end_time\": \"int64\",\n", "        \"city_name\": \"string\",\n", "    }\n", ")\n", "\n", "\n", "# gigs_info['dt'] = pd.Timestamp.now(tz='Asia/Kolkata')\n", "gigs_info.info()"]}, {"cell_type": "code", "execution_count": null, "id": "44fdd1b1-8f91-4b02-b5b7-975c0e66bd2c", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"model_gigs_info_input_v1\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date\", \"type\": \"date\", \"description\": \"date\"},\n", "        {\"name\": \"external_store_id\", \"type\": \"integer\", \"description\": \"external_store_id\"},\n", "        {\"name\": \"slot_id\", \"type\": \"integer\", \"description\": \"slot_id\"},\n", "        {\"name\": \"slot_capacity_id\", \"type\": \"integer\", \"description\": \"slot_capacity_id\"},\n", "        {\"name\": \"slot_name\", \"type\": \"varchar\", \"description\": \"slot_name\"},\n", "        {\"name\": \"start_time\", \"type\": \"integer\", \"description\": \"start_time\"},\n", "        {\"name\": \"end_time\", \"type\": \"integer\", \"description\": \"end_time\"},\n", "        {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"city_name\"},\n", "    ],\n", "    \"primary_key\": [\"date\", \"external_store_id\", \"slot_id\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \" gigs info \",\n", "}\n", "pb.to_trino(gigs_info, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "0860a101-bb1e-4aeb-9c59-8b65d0e62a2e", "metadata": {}, "outputs": [], "source": ["## planned dd , ldrp cpo , canc orders , ming cpo"]}, {"cell_type": "code", "execution_count": null, "id": "f26c4945-fe4c-48b3-b401-615b9bdff937", "metadata": {}, "outputs": [], "source": ["cpo = pd.read_sql_query(\n", "    sql=f\"\"\"\n", "\n", " with store as (\n", "\n", "    SELECT distinct \n", "        u.id as store_id,\n", "        u.name as store_name,\n", "        try_cast(u.external_id as integer) as blinkit_store_id,\n", "        ua.city_id,\n", "        c.code as city_code,\n", "        c.city_name\n", "    from \n", "        zomato.carthero_prod.users u \n", "        join zomato.carthero_prod.user_addresses ua on ua.user_id = u.id and active = 1\n", "        join zomato.carthero_prod.cities c on c.id = ua.city_id \n", "    where \n", "        source_id = 17110 and\n", "        try_cast(u.external_id as integer) is not null\n", "        and lower(u.name) not like ('%%hyperlocal%%')\n", "\n", "),\n", "base_orders as (\n", "    SELECT \n", "        fod.order_checkout_dt_ist as date ,\n", "        fod.partner_id,\n", "        dd.id as delivery_driver_id,\n", "        s.store_id as z_store_id,\n", "        s.blinkit_store_id as b_store_id,\n", "        dd.carrier_id,\n", "        count(distinct fod.order_id) as orders_delivered\n", "    from\n", "        dwh.fact_supply_chain_order_details fod\n", "        join store s on s.blinkit_store_id = fod.frontend_merchant_id\n", "        join zomato.carthero_prod.delivery_drivers dd on dd.driver_id = fod.partner_id\n", "    where \n", "        fod.order_checkout_dt_ist >= CURRENT_DATE - INTERVAL '21' DAY and \n", "        extract(week from fod.order_checkout_dt_ist) = extract(week from CURRENT_DATE) - 1 and\n", "        fod.order_current_status = 'DELIVERED'\n", "    group by 1,2,3,4,5,6\n", "    \n", "    \n", "    \n", "    union \n", "    \n", "      SELECT \n", "        fod.order_checkout_dt_ist as date ,\n", "        fod.partner_id,\n", "        dd.id as delivery_driver_id,\n", "        s.store_id as z_store_id,\n", "        s.blinkit_store_id as b_store_id,\n", "        dd.carrier_id,\n", "        count(distinct fod.order_id) as orders_delivered\n", "    from\n", "        bistro_etls.fact_supply_chain_order_details_bistro fod\n", "        join store s on s.blinkit_store_id = fod.frontend_merchant_id\n", "        join zomato.carthero_prod.delivery_drivers dd on dd.driver_id = fod.partner_id\n", "    where \n", "        fod.order_checkout_dt_ist >= CURRENT_DATE - INTERVAL '21' DAY and \n", "        extract(week from fod.order_checkout_dt_ist) = extract(week from CURRENT_DATE) - 1 and\n", "        fod.order_current_status = 'DELIVERED'\n", "    group by 1,2,3,4,5,6\n", "    \n", "    \n", "    \n", "    \n", ")\n", "\n", "\n", ",\n", "\n", "\n", "\n", "\n", "canc_factor as (\n", "\n", "with store_mapping as (select store_id,\n", "max_by(blinkit_store_id,inserted_at_ist) blinkit_store_id,\n", "max_by(blinkit_store_name,inserted_at_ist) blinkit_store_name,\n", "max_by(runnr_city,inserted_at_ist) runnr_city,\n", "max_by(city_id,inserted_at_ist) city_id,\n", "max_by(city_code,inserted_at_ist) city_code,\n", "max_by(zone_id,inserted_at_ist) zone_id,\n", "max_by(zone_primary_id,inserted_at_ist) zone_primary_id,\n", "max_by(locality_id,inserted_at_ist) locality_id,\n", "max_by(locality_name,inserted_at_ist) locality_name\n", "FROM logistics_data_etls.blinkit_store_mapping\n", "GROUP BY 1),\n", "\n", "orders as (\n", "SELECT delivery_driver_id,account_date,sum(acc_orders) acc_orders,SUM(delivered_orders) delivered_orders,\n", "SUM(returned_orders) returned_orders,SUM(delivered_orders_distance) delivered_orders_distance\n", "FROM\n", "(select delivery_driver_id, \n", "date(accounting_time+interval '330' minute) as account_date, \n", "count(case when (((leg_status IN ('COMPLETE','RETURN')) and lower(report_type)='drop') or ((leg_status IN ('CANCEL')) and lower(report_type)='pickup')) then ar.id else null end) as acc_orders ,\n", "count(distinct case when om.type in ('RetailForwardOrder','BistroForwardOrder') and om.current_status='DELIVERED' then om.id end) as delivered_orders ,\n", "count(case when sm.store_id IS NULL AND leg_status='COMPLETE' and lower(report_type)='drop' then ar.id else null end) as returned_orders ,\n", "sum(case when om.type in ('RetailForwardOrder','BistroForwardOrder') and om.current_status='DELIVERED' and lower(report_type)='drop' then accounting_distance else 0 end) as delivered_orders_distance\n", "from zomato.accounting_production.accounting_reports ar\n", "left join zomato.carthero_prod.shipment_legs sl on cast(sl.id as varchar)=coalesce(cast(ar.shipment_leg_id as varchar),ar.logistics_order_id)\n", "        and sl.dt >=DATE_FORMAT( CURRENT_DATE - INTERVAL '21' DAY,'%%Y%%m%%d')\n", "left join zomato.carthero_prod.orders o on o.id=sl.order_id and o.dt >= DATE_FORMAT( CURRENT_DATE - INTERVAL '21' DAY,'%%Y%%m%%d')\n", "LEFT JOIN store_mapping sm on sm.store_id=o.pickup_user_id\n", "left join oms_bifrost.oms_order om  on cast(om.id as varchar)=o.external_order_id and insert_ds_ist>cast((current_date - interval '23' day) as varchar)\n", "WHERE ar.dt >= DATE_FORMAT( CURRENT_DATE - INTERVAL '21' DAY,'%%Y%%m%%d') \n", "    AND merchant_category='BLINKIT'\n", "group by 1,2\n", "union\n", "select delivery_driver_id, \n", "date(accounting_time+interval '330' minute) as account_date, \n", "    count(case when (((leg_status IN ('COMPLETE','RETURN')) and lower(touch_point_type)='drop') or ((leg_status IN ('CANCEL')) and lower(touch_point_type)='pickup')) then tp.id else null end) as acc_orders ,\n", "    count(distinct case when om.type in ('RetailForwardOrder','BistroForwardOrder') and om.current_status='DELIVERED' then om.id end) as delivered_orders ,\n", "    count(case when sm.store_id IS NULL AND leg_status='COMPLETE' and lower(touch_point_type)='drop' then tp.id else null end) as returned_orders ,\n", "    sum(case when om.type in ('RetailForwardOrder','BistroForwardOrder') and om.current_status='DELIVERED' and lower(touch_point_type)='drop' then touch_point_distance else 0 end) as delivered_orders_distance\n", "from zomato.accounting_production.driver_touch_point_reports tp\n", "left join zomato.carthero_prod.shipment_legs sl on cast(sl.id as varchar)=coalesce(cast(tp.shipment_leg_id as varchar),tp.logistics_order_id)\n", "    and sl.dt>=DATE_FORMAT( CURRENT_DATE - INTERVAL '21' DAY,'%%Y%%m%%d')\n", "    \n", "left join zomato.carthero_prod.orders o on o.id=sl.order_id\n", "        and o.dt>=DATE_FORMAT( CURRENT_DATE - INTERVAL '21' DAY,'%%Y%%m%%d')\n", "LEFT JOIN store_mapping sm on sm.store_id=o.pickup_user_id\n", "left join oms_bifrost.oms_order om  on cast(om.id as varchar)=o.external_order_id and insert_ds_ist>cast((current_date - interval '23' day) as varchar)\n", "where \n", "    tp.dt>=DATE_FORMAT( CURRENT_DATE - INTERVAL '21' DAY,'%%Y%%m%%d')\n", "    AND merchant_category='BLINKIT'\n", "group by 1,2)\n", "group by 1,2)\n", "\n", "\n", ",\n", "\n", "rider_base as (\n", "select partner_id , frontend_merchant_id , order_checkout_dt_ist , store_id \n", "from dwh.fact_supply_chain_order_details fsc \n", "left join logistics_data_etls.blinkit_store_mapping sm on blinkit_store_id = frontend_merchant_id \n", "where order_checkout_dt_ist between current_date - interval '30' day and current_date - interval '1' day \n", "group by 1 , 2, 3, 4\n", "\n", "union \n", "\n", "\n", "select partner_id , frontend_merchant_id , order_checkout_dt_ist , store_id \n", "from bistro_etls.fact_supply_chain_order_details_bistro fsc \n", "left join logistics_data_etls.blinkit_store_mapping sm on blinkit_store_id = frontend_merchant_id \n", "where order_checkout_dt_ist between current_date - interval '30' day and current_date - interval '1' day \n", "group by 1 , 2, 3, 4\n", "\n", ")\n", "\n", "\n", "\n", "select  store_id , sum(delivered_orders) del_orders  , sum(acc_orders)  total_orders \n", "from orders os\n", "inner join rider_base rb on concat('FE' , cast(delivery_driver_id + 10000 as varchar )) = partner_id \n", "and order_checkout_dt_ist = account_date \n", "where account_Date between current_date - interval '14' day and current_date - interval '1' day \n", "group by 1 \n", "\n", "\n", "\n", "\n", "\n", ")\n", "\n", "\n", ",\n", "\n", "cpo_base as(\n", "    select \n", "        dael.account_date,\n", "        dael.delivery_driver_id,\n", "        \n", "        coalesce(sum(case when(head_type='base_pay')then amount else 0 end),0)*1.000 as base_pay_amount,\n", "        coalesce(sum(case when(head_type='long_distance_return_pay')then amount else 0 end),0)*1.000 as ldrp_amount,\n", "        coalesce(sum(case when(head_type in('distance_pay','over_distance_pay'))then amount else 0 end),0)*1.000 as distance_amount,\n", "        coalesce(sum(case when(head_type='wait_time_pay')then amount else 0 end),0)*1.000 as wait_time_pay,\n", "        coalesce(sum(case when(head_type='trip_min_guarantee')then amount else 0 end),0)*1.000 as trip_min_guarantee,\n", "        coalesce(sum(case when(head_type='weight_pay')then amount else 0 end),0)*1.000 as weight_pay,\n", "        coalesce(sum(case when(head_type='offer_incentive' and rate_card_type='Offers' and ref_type='AccountDate')then amount else 0 end),0)*1.000 as offer_payout,\n", "        coalesce(\n", "            sum(\n", "                case \n", "                    when((head_type='addition')and(ref_type is null or ref_type in('Dispute','ShipmentLeg','ManualAdjustment','PendingDeductionLedger'))\n", "                        and(info not like '%%eferra%%')and(info not like '%%oining%%onus%%')and(info not like '%%oining%%ncentiv%%')\n", "                        and(info not like '%%rime%%ayout%%')and(info not like '%%onthly%%ncentiv%%')and(info not like '%%entor%%onus%%')\n", "                        and(info not like '%%Support for Zomato riders in Odisha%%')and(info not like '%%ecurity%%epo%%it%%')\n", "                        and(info not like '%%TD%%educti%%')and(info not like '%%eekly%%oot%%a%%h%%')and(info not like '%%ca%%h%%paid%%by%%')\n", "                        and(info not like '%%ca%%h%%ollected%%by%%')and(info not like '%%ecuirty%%epo%%it%%')and(info not like '%%river%%tip%%')\n", "                        and(info not like '%%ending%%educti%%ecover%%revious%%')and(info not like '%%ending_deductions_adjustmen%%')\n", "                        and(info not like '%%ending_deductions_to_be_recovere%%')and(info not like '%%onthly%%ncentiv%%')and(info not like '%%_DBP%%')\n", "                        and(info not like'%%ettlement Adjustment - Carry Forward from previous week%%')\n", "                        and(info not like '%%ettlement Adjustment - Carry Forward to next wee%%')and(info not like '%%artner%%emi%%deduction%%')\n", "                        and(info not like '%%disbursement_closure_adjustment%%')and(info not like '%%disbursement_recovery_from_contribution%%')\n", "                        and(info not like '%%ifferential%%OB%%ee%%eduction%%')and(info not like '%%ifferential%%oining%%sd%%eduction')\n", "                        and (info not like '%%ash%%eposit%%ssue%%'))\n", "                    then amount \n", "                    end\n", "            ),0\n", "        )*1.000 as manual_additions\n", "    FROM \n", "        zomato.accounting_production.driver_accounting_event_ledgers dael\n", "    where \n", "        dael.dt >= DATE_FORMAT( CURRENT_DATE - INTERVAL '31' DAY,'%%Y%%m%%d') and \n", "        dael.account_date between current_date - interval '14' day and current_date - interval '1' day\n", "    group by 1,2\n", ")\n", "\n", "\n", "\n", "\n", "select \n", "external_store_id , \n", "try(ldrp *1.00 / total_orders) ldrp , \n", "try(trip_ming  *1.00 / total_orders) trip_ming  , \n", "coalesce(del_orders ,  0) del_orders , \n", "coalesce(total_orders , 0) total_orders\n", "from \n", "(\n", "SELECT \n", "    z_store_id as external_store_id,\n", "    sum(base_pay_amount+ldrp_amount+distance_amount+wait_time_pay+trip_min_guarantee+weight_pay+offer_payout+manual_additions) as total_earnings,\n", "    sum(ldrp_amount) ldrp,\n", "    sum(trip_min_guarantee) as trip_ming,\n", "    1.00*sum(base_pay_amount+ldrp_amount+distance_amount+wait_time_pay+trip_min_guarantee+weight_pay+offer_payout+manual_additions)/sum(orders_delivered) as cpo\n", "from    \n", "    cpo_base c\n", "    join base_orders b on b.date = c.account_date and b.delivery_driver_id = c.delivery_driver_id\n", "\n", "group by 1\n", ") base \n", "left join canc_factor on store_id = external_store_id \n", "       \n", "\"\"\",\n", "    con=pb.get_connection(\"[Warehouse] Trino\"),\n", ")\n", "\n", "cpo.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "5c687543-2782-4a21-8995-6bf03eedb313", "metadata": {}, "outputs": [], "source": ["cpo = cpo.astype(\n", "    {\n", "        \"external_store_id\": \"int64\",\n", "        \"ldrp\": \"float64\",\n", "        \"trip_ming\": \"float64\",\n", "        \"del_orders\": \"int64\",\n", "        \"total_orders\": \"int64\",\n", "    }\n", ")\n", "\n", "cpo.info()"]}, {"cell_type": "code", "execution_count": null, "id": "4817ab5f-ee8a-4daf-a90b-c4395affb4e3", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"cpo_factor_input_v1\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"external_store_id\", \"type\": \"integer\", \"description\": \"external_store_id\"},\n", "        {\"name\": \"ldrp\", \"type\": \"double\", \"description\": \"ldrp\"},\n", "        {\"name\": \"trip_ming\", \"type\": \"double\", \"description\": \"trip_ming\"},\n", "        {\"name\": \"del_orders\", \"type\": \"integer\", \"description\": \"delivered orders\"},\n", "        {\"name\": \"total_orders\", \"type\": \"integer\", \"description\": \"total orders\"},\n", "    ],\n", "    \"primary_key\": [\"external_store_id\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \" gigs info \",\n", "}\n", "pb.to_trino(cpo, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "78c7ec6c-3bc6-428f-9528-951ed7c36ed6", "metadata": {}, "outputs": [], "source": ["x = pd.Timestamp.now(tz=\"Asia/Kolkata\")\n", "x"]}, {"cell_type": "code", "execution_count": null, "id": "ba5ef400-85a2-4939-97e6-09863815ced3", "metadata": {}, "outputs": [], "source": ["# cpo[cpo[\"external_store_id\"] == 232215274]"]}, {"cell_type": "code", "execution_count": null, "id": "2e3d2b40-46ca-4981-ba6d-d2379b4e7616", "metadata": {}, "outputs": [], "source": ["planned_dd = pd.read_sql_query(\n", "    sql=f\"\"\"\n", "\n", " with store as (\n", "\n", "    SELECT distinct \n", "        u.id as store_id,\n", "        u.name as store_name,\n", "        try_cast(u.external_id as integer) as blinkit_store_id,\n", "        ua.city_id,\n", "        c.code as city_code,\n", "        c.city_name\n", "    from \n", "        zomato.carthero_prod.users u \n", "        join zomato.carthero_prod.user_addresses ua on ua.user_id = u.id and active = 1\n", "        join zomato.carthero_prod.cities c on c.id = ua.city_id \n", "    where \n", "        source_id = 17110 and\n", "        try_cast(u.external_id as integer) is not null\n", "        and lower(u.name) not like ('%%hyperlocal%%')\n", "\n", "),\n", "base_orders as (\n", "\n", "select date , \n", "order_checkout_ts_ist , \n", "partner_id , \n", "delivery_driver_id , \n", "z_store_id , \n", "b_store_id , \n", "order_id , \n", "accounting_distance , \n", "city_name \n", "from\n", "(\n", "select account_date date , \n", "accounting_time order_checkout_ts_ist , \n", "concat('FE' ,cast( delivery_driver_id + 10000 as varchar  )) partner_id , \n", "delivery_driver_id ,\n", "main.pickup_user_id z_store_id ,\n", "blinkit_store_id as b_store_id,\n", "external_order_id order_id  , \n", "accounting_distance , \n", "city_name , \n", "row_number() over (partition by external_order_id order by accounting_distance desc ) rnk , \n", "trip_id\n", "from\n", "(select distinct (accounting_time + interval '330' minute) as accounting_time,\n", "date(accounting_time + Interval '330' minute) as account_date, \n", "id, coalesce(cast(shipment_leg_id as varchar),logistics_order_id ) as shipment_leg_id, trip_id, city, delivery_driver_id, report_type, accounting_distance, merchant_category,wait_time, \n", "pickup_user_id\n", "from zomato.accounting_production.accounting_reports\n", "where  date(accounting_time + Interval '330' minute) between current_date - interval '36' day and current_date - interval '1' day \n", "and leg_status = 'COMPLETE'\n", "and dt between date_format(current_date- INTERVAL '40' DAY,'%%Y%%m%%d') and date_format(current_date + INTERVAL '0' DAY,'%%Y%%m%%d')\n", "and report_type = 'drop'\n", "and  merchant_category = 'BLINKIT'\n", ") main\n", "join zomato.carthero_prod.shipment_legs sl on sl.delivery_trip_id = main.trip_id\n", "and sl.dt between date_format(current_date- INTERVAL '40' DAY,'%%Y%%m%%d') and date_format(current_date + INTERVAL '0' DAY,'%%Y%%m%%d')\n", "\n", "join zomato.carthero_prod.orders o on o.id = sl.order_id\n", "join store on store.store_id = main.pickup_user_id \n", "and o.dt between date_format(current_date- INTERVAL '40' DAY,'%%Y%%m%%d') and date_format(current_date + INTERVAL '0' DAY,'%%Y%%m%%d')\n", ")\n", "where rnk = 1 \n", "and trip_id not in (\n", "select distinct trip_id from zomato.logs_dashboard_etls.trip_pay \n", "where dt >= date_format(current_date - interval '50' day ,'%%Y%%m%%d' )\n", "and orders > 1 \n", ")\n", "\n", "\n", ")\n", "\n", " ,\n", " \n", " manual_input as \n", " \n", "(\n", "select entity_id , max(dd_input) dd_input ,  max(dd_approval) dd_approval\n", "from \n", "logistics_data_etls.model_cpk_input_v1\n", "where entity_type = 'store'\n", "group by 1 \n", ")\n", "\n", ",\n", "\n", "store_age as (select z_store_id zid, date_diff('day' , dt , current_date) age \n", "from \n", "(\n", "select z_store_id , min(date) dt  from \n", "base_orders \n", "group by 1 \n", ")\n", ")\n", "\n", ",\n", "\n", "\n", "planned_dd as (select hour , z_store_id  ,\n", "accounting_dd ,\n", "del_orders \n", "from\n", "(\n", "select hour , z_store_id   , city_name , accounting_distance accounting_dd   , del_orders \n", "from \n", "(select hour(order_checkout_ts_ist ) hour , z_store_id  , city_name , coalesce(sum(accounting_distance)  , 0 ) accounting_distance , count(order_id ) del_orders \n", "from\n", "base_orders \n", "where date between current_Date - interval '15' day and current_date - interval '1' day \n", "group by 1, 2,3\n", ")\n", "))\n", "\n", ",\n", "\n", "\n", "store_gigs as  \n", "(\n", "select date , external_store_id , slot_id , slot_name , start_time , end_time , city_name \n", "from logistics_data_etls.model_gigs_info_input_v1\n", "\n", ")\n", "\n", "select date account_date  , external_store_id store_id, slot_id , slot_name , \n", "case when dd_approval = 'yes' or (age between 0 and 20 ) then dd_input \n", "     when age >= 36 then accounting_dd * 1.00 / del_orders  \n", "     else  (((35 - age ) * 1.00 / 15 )*dd_input) + ((age - 20 ) * 1.00 / 15 *(accounting_dd * 1.00 / del_orders)) end as final_dd \n", "     \n", "from\n", "(\n", "select date, external_store_id , slot_id , slot_name , age , dd_approval , dd_input ,\n", "case when del_orders < 20 or del_orders is null then store_orders else del_orders end as del_orders , \n", "case when del_orders < 20 or del_orders is null then store_distance else accounting_dd end as accounting_dd \n", "from\n", "(select date , external_store_id , slot_id , slot_name , coalesce(sum(del_orders) , 1) del_orders , coalesce(sum(accounting_dd) , 1 ) accounting_dd \n", ", max(age) age , max(dd_approval) dd_approval , max(dd_input) dd_input , sum(store_orders) store_orders , sum(store_distance) store_distance\n", "from\n", "(\n", "select date , external_store_id , slot_id , slot_name , start_time , end_time ,\n", "city_name , del_orders , accounting_dd  , coalesce(age , 0) age  , coalesce(dd_input , 2.00) dd_input, dd_approval , \n", "sum(del_orders) over (partition by external_store_id , date ) store_orders  , \n", "sum(accounting_dd) over (partition by external_store_id , date ) store_distance \n", "from store_gigs \n", "left join planned_dd on z_store_id = external_store_id \n", "and hour between start_time and end_time - 1 \n", "left join store_age on zid = external_store_id \n", "left join manual_input on entity_id = external_store_id \n", ")\n", "group by 1 , 2, 3, 4\n", ")\n", "\n", ")\n", "\n", "\"\"\",\n", "    con=pb.get_connection(\"[Warehouse] Trino\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e9f272fe-dc82-47f9-ab74-737a1969c868", "metadata": {}, "outputs": [], "source": ["planned_dd[\"account_date\"] = pd.to_datetime(planned_dd[\"account_date\"]).dt.date\n", "\n", "planned_dd[\"final_dd\"] = pd.to_numeric(planned_dd[\"final_dd\"], errors=\"coerce\")\n", "planned_dd = planned_dd.astype({\"store_id\": \"int64\", \"slot_id\": \"int64\", \"slot_name\": \"string\"})\n", "planned_dd.info()"]}, {"cell_type": "code", "execution_count": null, "id": "70bac848-33f6-4c63-95b2-a60bc1f37ad9", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"planned_dd_input_v1\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"account_date\", \"type\": \"date\", \"description\": \"account_date\"},\n", "        {\"name\": \"store_id\", \"type\": \"integer\", \"description\": \"store_id\"},\n", "        {\"name\": \"slot_id\", \"type\": \"integer\", \"description\": \"slot_id\"},\n", "        {\"name\": \"slot_name\", \"type\": \"varchar\", \"description\": \"slot_name\"},\n", "        {\"name\": \"final_dd\", \"type\": \"double\", \"description\": \"final_dd\"},\n", "    ],\n", "    \"primary_key\": [\"store_id\", \"account_date\", \"slot_id\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \" planned dd \",\n", "}\n", "pb.to_trino(planned_dd, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "b927972e-8ee1-4ae1-9f06-adabcd2d5701", "metadata": {}, "outputs": [], "source": ["order_projections = pd.read_sql_query(\n", "    sql=f\"\"\"\n", "\n", "with store_gigs as  \n", "(\n", "select date , external_store_id , slot_id , slot_name , start_time , end_time , city_name ,blinkit_store_name store_name \n", "from logistics_data_etls.model_gigs_info_input_v1 v\n", "left join logistics_data_etls.blinkit_store_mapping sm on v.external_Store_id = store_id \n", ")\n", "\n", ",\n", "\n", "order_pro as (select hr , projection_date , merchant_id , orders , store_id  \n", "from \n", "(select cast(hr as bigint ) hr , cast(dt as date) projection_date , cast(merchant_id as bigint) merchant_id , ceil(cast(orders as double)) orders ,\n", "row_number() over (partition by hr , dt , merchant_id order by updated_at_ts desc)  rnk \n", "from \n", "consumer_etls.merchant_hourly_forecasting_v2\n", ")\n", "left join logistics_data_etls.blinkit_store_mapping sm on merchant_id = blinkit_store_id \n", "where rnk = 1 \n", ")\n", "\n", "\n", "\n", "select order_projection_date , \n", "store_id , \n", "store_name ,\n", "city_name , \n", "slot_id , \n", "slot_name , \n", "sum(projected_orders) projected_orders \n", "from \n", "(select \n", "date as order_projection_date , \n", "slot_id , \n", "slot_name , \n", "external_store_id store_id , \n", "hr hour , \n", "city_name  , \n", "store_name ,\n", "coalesce(orders , 0) projected_orders \n", "from store_gigs sg \n", "left join order_pro op\n", "on sg.external_store_id = store_id \n", "and date = projection_date \n", "and hr between start_time and end_time - 1 \n", ")\n", "\n", "group by 1 , 2, 3, 4,5 ,6\n", "\n", "\n", "\n", "      \n", "\"\"\",\n", "    con=pb.get_connection(\"[Warehouse] Trino\"),\n", ")\n", "\n", "order_projections"]}, {"cell_type": "code", "execution_count": null, "id": "28b51c1d-1beb-4122-91b5-aae330503721", "metadata": {}, "outputs": [], "source": ["order_projections.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "8189fab1-6c88-4e5f-ad90-202ab2657719", "metadata": {}, "outputs": [], "source": ["order_projections[\"order_projection_date\"] = pd.to_datetime(\n", "    order_projections[\"order_projection_date\"]\n", ").dt.date\n", "\n", "order_projections = order_projections.astype(\n", "    {\n", "        \"store_id\": \"int64\",\n", "        \"city_name\": \"string\",\n", "        \"slot_id\": \"int64\",\n", "        \"slot_name\": \"string\",\n", "        \"projected_orders\": \"int64\",\n", "        \"store_name\": \"string\",\n", "    }\n", ")\n", "order_projections.info()"]}, {"cell_type": "code", "execution_count": null, "id": "1ff935c0-1c56-4010-84aa-d65c71619d10", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"logistics_data_etls\",\n", "    \"table_name\": \"projected_orders_v1\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"order_projection_date\", \"type\": \"date\", \"description\": \"account_date\"},\n", "        {\"name\": \"store_id\", \"type\": \"integer\", \"description\": \"store_id\"},\n", "        {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"city_name\"},\n", "        {\"name\": \"slot_id\", \"type\": \"integer\", \"description\": \"slot_id\"},\n", "        {\"name\": \"slot_name\", \"type\": \"varchar\", \"description\": \"slot_name\"},\n", "        {\"name\": \"projected_orders\", \"type\": \"double\", \"description\": \"orders\"},\n", "        {\"name\": \"store_name\", \"type\": \"varchar\", \"description\": \"store_name\"},\n", "    ],\n", "    \"primary_key\": [\"store_id\", \"order_projection_date\", \"slot_id\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \" Order Projection \",\n", "}\n", "pb.to_trino(order_projections, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "5ff0d76d-52d1-4ae8-ac12-680d375ade03", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e13c09da-3385-471b-a530-9462f9bf8878", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}