alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: picker_partner_live_availability
dag_type: report
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: logistics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RXQ06G2E
path: logistics/picker_partner_live_availability/report/picker_partner_live_availability
paused: true
project_name: picker_partner_live_availability
schedule:
  interval: '*/15 * * * *'
  start_date: '2021-11-18T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 6
