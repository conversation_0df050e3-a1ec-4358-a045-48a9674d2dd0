alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: rc_constructs
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 16G
      request: 4G
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: logistics
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U04U9KUEZDE
path: logistics/rc_constructs/etl/rc_constructs
paused: false
project_name: rc_constructs
schedule:
  interval: ' 0 2 * * *'
  start_date: '2023-04-03T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
