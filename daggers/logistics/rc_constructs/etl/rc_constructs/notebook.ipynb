{"cells": [{"cell_type": "code", "execution_count": null, "id": "8228ca02-52c2-419a-9a91-10c87f75321a", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime, timedelta\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "id": "37fd0dd7-26ae-4196-bc7f-ab0bfb5f0467", "metadata": {}, "outputs": [], "source": ["trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "6e1a7c5a-7997-4185-94e9-4940ce346074", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "select _id,city\n", ",tag,\n", "car_id,\n", "startdt,\n", "enddt,\n", "Amount1,Orders1,Amount2,Orders2,Amount3,Orders3,Amount4,Orders4\n", "from \n", "(\n", "select _id,cast(json_extract(rule_conditions,'$.carrierid') as array(varchar)) carrier_id,\n", "cast(json_extract(rule_conditions,'$.city') as json) city,\n", "cast(json_extract(rule_conditions,'$.tag') as ARRAY(varchar)) tags,\n", "json_extract(ts,'$.startdateepochsecond') as startdt,\n", "json_extract(ts,'$.enddateepochsecond') as enddt,\n", "Amount1,Orders1,Amount2,Orders2,Amount3,Orders3,Amount4,Orders4\n", "from\n", "(select _id,cast(rule_conditions as json) rule_conditions,\n", "rule_conditions as rc,\n", "cast(validity_period as json) as ts,\n", "json_extract_scalar(cast(mrc.milestoneslabs as json), '$[0].bonusamount') AS Amount1,\n", "json_extract_scalar(cast(mrc.milestoneslabs as json), '$[0].totaltouchpoints') as Orders1,\n", "json_extract_scalar(cast(mrc.milestoneslabs as json), '$[1].bonusamount') AS Amount2,\n", "json_extract_scalar(cast(mrc.milestoneslabs as json), '$[1].totaltouchpoints') as Orders2,\n", "json_extract_scalar(cast(mrc.milestoneslabs as json), '$[2].bonusamount') AS Amount3,\n", "json_extract_scalar(cast(mrc.milestoneslabs as json), '$[2].totaltouchpoints') as Orders3,\n", "json_extract_scalar(cast(mrc.milestoneslabs as json), '$[3].bonusamount') AS Amount4,\n", "json_extract_scalar(cast(mrc.milestoneslabs as json), '$[3].totaltouchpoints') as Orders4,\n", "row_number() over(partition by _id order by priority desc) as rn\n", "from zomato.mongo_rule_engine_prod.rule_configs mrc\n", "join zomato.accounting_production.driver_rate_card_infos rc on mrc._id = rc.rate_card_id\n", "join zomato.carthero_prod.delivery_drivers dd on dd.id = rc.delivery_driver_id\n", "join zomato.driver_service.driver_store_mappings sm on sm.driver_id = dd.id\n", "where dt>='********'  \n", "and mrc.classname like '%%JoiningBonus%%' \n", "and sm.active=1 \n", "and sm.category_name = 'BLINKIT')\n", "where rn=1)\n", "cross join unnest(tags) as tags(tag)\n", "cross join unnest(carrier_id) as carrier_id(car_id)\n", "\"\"\"\n", "\n", "df = pd.read_sql(sql, trino_con)"]}, {"cell_type": "code", "execution_count": null, "id": "da3f7546-e1ce-45c7-afdc-27a708ea4f62", "metadata": {}, "outputs": [], "source": ["df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "7fa0d27b-a073-41a2-87af-ad410f4d0ccf", "metadata": {"tags": []}, "outputs": [], "source": ["df[\"enddt\"] = pd.to_datetime(df[\"enddt\"], unit=\"s\")\n", "df[\"startdt\"] = pd.to_datetime(df[\"startdt\"], unit=\"s\")"]}, {"cell_type": "code", "execution_count": null, "id": "9e659cce-4448-4083-9f80-13acbd9b9e8a", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "80db4850-150d-4e79-ac50-b19ba2d380d1", "metadata": {}, "outputs": [], "source": ["df[\"startdt\"] = df[\"startdt\"].dt.date\n", "df[\"enddt\"] = df[\"enddt\"].dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "ada1535e-9119-4d08-b5d1-86227dda3353", "metadata": {}, "outputs": [], "source": ["df[\"car_id\"] = df[\"car_id\"].str[0]"]}, {"cell_type": "code", "execution_count": null, "id": "aa941c00-79a0-497a-a7af-e9055abc7bc4", "metadata": {}, "outputs": [], "source": ["df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "9fbd006f-800f-4e6a-b06f-1d50ffce2d40", "metadata": {}, "outputs": [], "source": ["df[\"city\"] = df[\"city\"].str.replace(\"/\", \"\", regex=True)"]}, {"cell_type": "code", "execution_count": null, "id": "78440a53-5c54-4533-878e-8a13e40dad6b", "metadata": {}, "outputs": [], "source": ["df[\"city\"] = df[\"city\"].str.replace('\"', \"\", regex=True)"]}, {"cell_type": "code", "execution_count": null, "id": "b04da05b-6fdf-403d-9f1e-f12c56052986", "metadata": {}, "outputs": [], "source": ["df[\"city\"] = df[\"city\"].str.replace(\"[\", \"\", regex=True)"]}, {"cell_type": "code", "execution_count": null, "id": "aee43daf-e3cc-48c2-850a-c2e18e7100bf", "metadata": {}, "outputs": [], "source": ["df[\"city\"] = df[\"city\"].str.replace(\"]\", \"\", regex=True)"]}, {"cell_type": "code", "execution_count": null, "id": "233e4259-80e2-4165-abd9-e1fdc375eefa", "metadata": {}, "outputs": [], "source": ["df[\"city\"] = df[\"city\"].str.replace(\"\\\\\", \"\", regex=True)"]}, {"cell_type": "code", "execution_count": null, "id": "ab7b1508-d549-4b0a-922b-e9a2d3d97acf", "metadata": {}, "outputs": [], "source": ["df[\"car_id\"] = df[\"car_id\"].astype(int)\n", "df[\"tag\"] = df[\"tag\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "59fb7bae-d2ff-45c9-87b7-b161946cac85", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "select distinct id,carrier_type from zomato.carthero_prod.carriers\n", "\"\"\"\n", "df_ca = pd.read_sql(sql, trino_con)"]}, {"cell_type": "code", "execution_count": null, "id": "322fdd03-08d9-40d5-936e-2557e1c68466", "metadata": {}, "outputs": [], "source": ["df_ca[\"id\"] = df_ca[\"id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "1a9b18fc-947a-4513-93d8-348c2fb72738", "metadata": {}, "outputs": [], "source": ["df_final = pd.merge(df, df_ca, left_on=\"car_id\", right_on=\"id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "1192bcf0-13ea-417e-9ac9-79cda0d245b8", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "select distinct id,name from zomato.driver_service.tags\n", "\"\"\"\n", "df_tag = pd.read_sql(sql, trino_con)"]}, {"cell_type": "code", "execution_count": null, "id": "1ad9a0e8-a06c-4b4c-8c2b-80c41f57a6de", "metadata": {}, "outputs": [], "source": ["df_tag[\"id\"] = df_tag[\"id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "0cd55bfa-2671-4f60-858a-a8c4524e9a2d", "metadata": {}, "outputs": [], "source": ["df_final = pd.merge(df_final, df_tag, left_on=\"tag\", right_on=\"id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "436af8ee-fe68-4bb7-92ae-04c6c519dc54", "metadata": {}, "outputs": [], "source": ["df = df_final.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "469c49d4-f1df-4bdc-9efb-9663dba9692b", "metadata": {}, "outputs": [], "source": ["final_df = pd.DataFrame(columns=[\"_id\", \"startdt\", \"enddt\"])\n", "for _id in df[\"_id\"].unique():\n", "    help_df = pd.DataFrame(columns=[\"_id\", \"startdt\", \"enddt\"])\n", "    help_df[\"startdt\"] = pd.date_range(\n", "        start=df[df[\"_id\"] == _id][\"startdt\"].values[0],\n", "        end=df[df[\"_id\"] == _id][\"enddt\"].values[0],\n", "    )\n", "    help_df[\"enddt\"] = df[df[\"_id\"] == _id][\"enddt\"].values[0]\n", "    help_df[\"_id\"] = _id\n", "    final_df = pd.concat([final_df, help_df], axis=0)"]}, {"cell_type": "code", "execution_count": null, "id": "0c978461-bcfe-4fd3-8edd-dea48313d6ce", "metadata": {}, "outputs": [], "source": ["d_f = pd.merge(df_final, final_df, on=\"_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "b6a2291b-f8e5-471f-af34-e6deb3cc4bec", "metadata": {}, "outputs": [], "source": ["d_f = d_f[\n", "    [\n", "        \"_id\",\n", "        \"city\",\n", "        \"Amount1\",\n", "        \"Orders1\",\n", "        \"Amount2\",\n", "        \"Orders2\",\n", "        \"Amount3\",\n", "        \"Orders3\",\n", "        \"Amount4\",\n", "        \"Orders4\",\n", "        \"carrier_type\",\n", "        \"name_x\",\n", "        \"name_y\",\n", "        \"startdt_y\",\n", "        \"enddt_y\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "9846f1bb-7d30-47f6-b6b1-9c61fc4fc8e0", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1XtnY5qZZS1b57O90fMmsyLPTxM8hLD_TXhDjgvwDYNQ\"\n", "sheet_name = \"Sheet10\"\n", "\n", "pb.to_sheets(d_f, sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "858d44cc-0f39-44d9-aa47-7d0365f52317", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}