alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: user_checkins_daily
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: fiternal
notebook:
  parameters: null
    # end_timestamp_utc: '{{ data_interval_end }}'
owner:
  email: <EMAIL>
  slack_id: U06A1F2V8RK
path: fiternal/workflow/etl/user_checkins_daily
paused: false
pool: fiternal_pool
project_name: workflow
schedule:
  end_date: '2025-08-25T00:00:00'
  interval: 30 */2 * * *
  start_date: '2025-06-01T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 5
