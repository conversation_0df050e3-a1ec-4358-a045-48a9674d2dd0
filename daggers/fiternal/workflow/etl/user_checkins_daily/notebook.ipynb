{"cells": [{"cell_type": "code", "execution_count": null, "id": "ec400c87-cf53-434c-a9b3-34f7fd9f6c78", "metadata": {}, "outputs": [], "source": ["!pip install google-cloud-firestore==2.20.2"]}, {"cell_type": "code", "execution_count": null, "id": "8e3083ae-b561-4f22-8308-35a54a2174ce", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "from datetime import datetime, timedelta, timezone, time\n", "import pytz\n", "from google.cloud import firestore\n", "\n", "conn_trino = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "f8420e90-6b38-4f36-95a4-c61673c63179", "metadata": {}, "outputs": [], "source": ["service_account_info = pb.get_secret(\"dse/credentials/fiternal/gym_app/google_oauth\")\n", "db = firestore.Client.from_service_account_info(service_account_info)\n", "# db = firestore.Client.from_service_account_json(\"service-account.json\")\n", "\n", "collections = db.collections()"]}, {"cell_type": "code", "execution_count": null, "id": "f0ed7364-c468-43d9-bc15-05d71aeb511e", "metadata": {}, "outputs": [], "source": ["last_timestamp_utc_query = \"\"\"\n", "select max(etl_snapshot_at_ist) - interval '330' minute as last_run_timestamp\n", "from fiternal_etls.user_checkins_daily\n", "where checkin_date_ist >= current_date - interval '7' day\n", "\"\"\"\n", "\n", "current_max_timestamp_utc_df = pd.read_sql_query(last_timestamp_utc_query, conn_trino)\n", "\n", "last_timestamp_utc = current_max_timestamp_utc_df.loc[0, \"last_run_timestamp\"]\n", "last_timestamp_utc = datetime.fromisoformat(last_timestamp_utc).replace(tzinfo=timezone.utc)"]}, {"cell_type": "code", "execution_count": null, "id": "ca84768d-10b9-4229-9d68-b7d4c15c0aee", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# end_timestamp_utc = None"]}, {"cell_type": "code", "execution_count": null, "id": "b0cfa5e5-4d6a-496c-932e-700f9ec8b034", "metadata": {}, "outputs": [], "source": ["# try:\n", "#     end_timestamp_utc = datetime.fromisoformat(end_timestamp_utc)\n", "# except NameError:\n", "#     end_timestamp_utc = previous_day_630pm_utc"]}, {"cell_type": "code", "execution_count": null, "id": "66fb7c73-7c51-4e26-88ce-98f736354c4e", "metadata": {}, "outputs": [], "source": ["now_utc = datetime.now(timezone.utc)\n", "today_utc = now_utc.date()\n", "previous_day_630pm_utc = datetime.combine(\n", "    today_utc - timedelta(days=1), time(18, 30), tzinfo=timezone.utc\n", ")\n", "\n", "start_datetime = min(last_timestamp_utc, previous_day_630pm_utc)\n", "end_datetime = now_utc"]}, {"cell_type": "code", "execution_count": null, "id": "a320da48-7b7f-413c-8679-6fff6174cc3b", "metadata": {}, "outputs": [], "source": ["print(\"Fetching data from:\", start_datetime, \"to:\", end_datetime)"]}, {"cell_type": "code", "execution_count": null, "id": "c87bd611-09a5-4d36-bc2e-a31a792734a3", "metadata": {}, "outputs": [], "source": ["query = (\n", "    db.collection(\"checkins\")\n", "    .where(filter=firestore.FieldFilter(\"checkinTime\", \">=\", start_datetime))\n", "    .where(filter=firestore.FieldFilter(\"checkinTime\", \"<\", end_datetime))\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "dd90f8be-2b2d-4a45-83af-d2fe01ac7853", "metadata": {}, "outputs": [], "source": ["rows = [\n", "    {\n", "        \"email_id\": doc.to_dict().get(\"email\"),\n", "        \"name\": doc.to_dict().get(\"name\"),\n", "        \"uid\": doc.to_dict().get(\"uid\"),\n", "        \"photo_url\": doc.to_dict().get(\"photoURL\"),\n", "        \"checkin_time_utc\": doc.to_dict().get(\"checkinTime\"),\n", "    }\n", "    for doc in query.stream()\n", "]\n", "\n", "checkins_df = pd.DataFrame(rows)\n", "\n", "# Converting checkin_time_utc to datetime and handle timezone\n", "checkins_df[\"checkin_time_utc\"] = pd.to_datetime(checkins_df[\"checkin_time_utc\"])\n", "if checkins_df[\"checkin_time_utc\"].dt.tz is None:\n", "    checkins_df[\"checkin_time_utc\"] = checkins_df[\"checkin_time_utc\"].dt.tz_localize(\"UTC\")\n", "checkins_df[\"checkin_time_ist\"] = checkins_df[\"checkin_time_utc\"].dt.tz_convert(\"Asia/Kolkata\")\n", "checkins_df.drop(columns=[\"checkin_time_utc\"], inplace=True)\n", "checkins_df[\"checkin_time_ist\"] = checkins_df[\"checkin_time_ist\"].dt.tz_localize(None)\n", "\n", "checkins_df = checkins_df.sort_values(by=[\"uid\", \"checkin_time_ist\"], ascending=True).reset_index(\n", "    drop=True\n", ")\n", "\n", "# Filtering out consecutive check-ins within 15 minutes\n", "checkins_df[\"prev_checkin\"] = checkins_df.groupby(\"uid\")[\"checkin_time_ist\"].shift(1)\n", "checkins_df[\"time_diff_minutes\"] = (\n", "    checkins_df[\"checkin_time_ist\"] - checkins_df[\"prev_checkin\"]\n", ").dt.total_seconds() / 60\n", "checkins_filtered = checkins_df[\n", "    (checkins_df[\"time_diff_minutes\"].isna()) | (checkins_df[\"time_diff_minutes\"] >= 15)\n", "].copy()\n", "\n", "# Assigning check-in sequence for each user on each day\n", "checkins_filtered[\"checkin_date_ist\"] = checkins_filtered[\"checkin_time_ist\"].dt.date\n", "checkins_filtered[\"checkin_sequence\"] = (\n", "    checkins_filtered.groupby([\"uid\", \"checkin_date_ist\"])[\"checkin_time_ist\"]\n", "    .rank(method=\"first\")\n", "    .astype(int)\n", ")\n", "\n", "# Dropping unnecessary columns\n", "checkins_filtered = checkins_filtered.drop(columns=[\"prev_checkin\", \"time_diff_minutes\"])\n", "checkins_filtered = checkins_filtered[\n", "    [\n", "        \"checkin_date_ist\",\n", "        \"checkin_time_ist\",\n", "        \"name\",\n", "        \"email_id\",\n", "        \"uid\",\n", "        \"photo_url\",\n", "        \"checkin_sequence\",\n", "    ]\n", "]\n", "checkins_filtered = checkins_filtered.sort_values(\n", "    by=[\"checkin_time_ist\", \"email_id\"], ascending=True\n", ").reset_index(drop=True)\n", "\n", "# Adding snapshot timestamp in IST\n", "now_ist = datetime.now(pytz.timezone(\"Asia/Kolkata\")).replace(tzinfo=None)\n", "checkins_filtered.insert(0, \"etl_snapshot_at_ist\", now_ist)"]}, {"cell_type": "code", "execution_count": null, "id": "9c7ffd00-0027-47b0-b73c-a8c19edc7e21", "metadata": {}, "outputs": [], "source": ["if len(checkins_filtered) > 0:\n", "    kwargs_trino = {\n", "        \"schema_name\": \"fiternal_etls\",\n", "        \"table_name\": \"user_checkins_daily\",\n", "        \"column_dtypes\": [\n", "            {\n", "                \"name\": \"etl_snapshot_at_ist\",\n", "                \"type\": \"timestamp(6)\",\n", "                \"description\": \"Timestamp of ETL snapshot in IST\",\n", "            },\n", "            {\n", "                \"name\": \"checkin_date_ist\",\n", "                \"type\": \"date\",\n", "                \"description\": \"Check-in date (local)\",\n", "            },\n", "            {\n", "                \"name\": \"checkin_time_ist\",\n", "                \"type\": \"timestamp(6)\",\n", "                \"description\": \"Check-in time in IST (with millisecond precision)\",\n", "            },\n", "            {\n", "                \"name\": \"name\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"Name of the user\",\n", "            },\n", "            {\n", "                \"name\": \"email_id\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"Email ID of the user\",\n", "            },\n", "            {\n", "                \"name\": \"uid\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"Unique user identifier\",\n", "            },\n", "            {\n", "                \"name\": \"photo_url\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"URL of user's photo\",\n", "            },\n", "            {\n", "                \"name\": \"checkin_sequence\",\n", "                \"type\": \"integer\",\n", "                \"description\": \"Order of check-in on the given date\",\n", "            },\n", "        ],\n", "        \"primary_key\": [\"checkin_date_ist\", \"checkin_time_ist\", \"email_id\"],\n", "        \"partition_key\": [\"checkin_date_ist\"],\n", "        \"incremental_key\": \"checkin_date_ist\",\n", "        \"load_type\": \"upsert\",\n", "        \"table_description\": (\n", "            \"Daily snapshot of user check-ins with timestamps converted to IST, \"\n", "            \"including check-in sequence and user metadata.\"\n", "        ),\n", "        \"force_upsert_without_increment_check\": False,\n", "    }\n", "\n", "    pb.to_trino(checkins_filtered, **kwargs_trino)\n", "    print(\"Inserted \" + str(len(checkins_filtered)) + \" rows successfully.\")"]}, {"cell_type": "code", "execution_count": null, "id": "ca1e29eb-011e-4624-913c-44f4f00d0716", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}