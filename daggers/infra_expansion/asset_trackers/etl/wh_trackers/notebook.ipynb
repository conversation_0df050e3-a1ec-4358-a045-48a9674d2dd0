{"cells": [{"cell_type": "code", "execution_count": null, "id": "056f93fa-d7b4-40b8-a5dd-4ef02fb4e6b6", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from io import BytesIO, StringIO\n", "import re\n", "import requests\n", "from urllib.parse import urlencode\n", "from datetime import datetime, timedelta\n", "from pytz import timezone\n", "import warnings\n", "\n", "import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "from datetime import datetime\n", "from pytz import timezone\n", "from collections import defaultdict\n", "\n", "import json\n", "import os\n", "import base64\n", "import io\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "\n", "def convert_date_format(date_str, date_form):\n", "    try:\n", "        if pd.isna(date_str) or str(date_str).lower() == \"nan\":\n", "            return None\n", "        return datetime.strptime(str(date_str).strip(), date_form).strftime(\"%Y-%m-%d\")\n", "    except (ValueErro<PERSON>, TypeError):\n", "        return None\n", "\n", "\n", "def rename_duplicate_columns(df):\n", "    counts = defaultdict(int)\n", "    new_columns = []\n", "\n", "    for col in df.columns:\n", "        counts[col] += 1\n", "        if counts[col] == 1:\n", "            new_columns.append(col)\n", "        else:\n", "            new_columns.append(f\"{col}_{counts[col]-1}\")\n", "\n", "    df.columns = new_columns\n", "    return df\n", "\n", "\n", "def get_kwargs_from_df(df, table_name):\n", "\n", "    dtypes = pd.DataFrame(df.dtypes, columns=[\"type_\"]).reset_index().astype({\"type_\": \"str\"})\n", "    type_map = {\n", "        \"object\": \"VARCHAR\",\n", "        \"int64\": \"INTEGER\",\n", "        \"int32\": \"INTEGER\",\n", "        \"float64\": \"REAL\",\n", "        \"float32\": \"REAL\",\n", "        \"datetime64[ns]\": \"TIMESTAMP(6)\",\n", "    }\n", "\n", "    dtypes[\"type\"] = dtypes[\"type_\"].map(type_map)\n", "    columns = [\n", "        {\"name\": row[\"index\"], \"type\": row[\"type\"], \"description\": row[\"index\"]}\n", "        for _, row in dtypes.iterrows()\n", "    ]\n", "\n", "    return {\n", "        \"schema_name\": \"infra_expansion_etls\",\n", "        \"table_name\": table_name,\n", "        \"column_dtypes\": columns,\n", "        \"partition_key\": [],\n", "        \"load_type\": \"truncate\",\n", "        \"table_description\": \"Table for warehouse trackers Dump\",\n", "    }\n", "\n", "\n", "def read_and_clean_data(df, header_index, primary, cols, date_cols, date_format, table_name):\n", "\n", "    if header_index != -1:\n", "        df.columns = df.iloc[header_index]\n", "        df = df.iloc[header_index + 1 :].reset_index(drop=True)\n", "\n", "    df = df.loc[:, df.columns.notna()]\n", "\n", "    df.columns = [\n", "        str(col)\n", "        .replace(\" \", \"_\")\n", "        .replace(\"\\n\", \"\")\n", "        .replace(\"/\", \"\")\n", "        .replace(\"(\", \"\")\n", "        .replace(\")\", \"\")\n", "        .replace(\"?\", \"\")\n", "        .replace(\"-\", \"\")\n", "        .replace(\"&\", \"\")\n", "        .replace(\"+\", \"\")\n", "        .lower()\n", "        for col in df.columns\n", "    ]\n", "\n", "    df = rename_duplicate_columns(df)\n", "\n", "    df = df[cols]\n", "    df = df.where(pd.notnull(df), None)\n", "\n", "    for col in date_cols:\n", "        df[col] = df[col].apply(lambda x: convert_date_format(x, date_format))\n", "        # df[col] = df[col].astype(\"datetime64[ns]\")\n", "\n", "    return df.reset_index(drop=True)\n", "\n", "\n", "def main(sheet_id, sheet_name, header_index, cols, date_cols, date_format, table_name, primary):\n", "    user_id = \"U08JKUHL52B\"\n", "    channel_name = \"tables-infra-alerts\"\n", "\n", "    try:\n", "        df = pb.from_sheets(sheet_id, sheet_name)\n", "\n", "    except Exception as e:\n", "        pb.send_slack_message(\n", "            channel=channel_name,\n", "            text=f\"<@{user_id}> || Error occured while reading {sheet_id}\\n Exception: {e}\",\n", "        )\n", "        raise ValueError(f\"Failed to read Google Sheet {sheet_id}/{sheet_name}: {e}\")\n", "\n", "    try:\n", "        df = read_and_clean_data(\n", "            df, header_index, primary, cols, date_cols, date_format, table_name\n", "        )\n", "        print(\"Data Cleaned\")\n", "    except Exception as e:\n", "        pb.send_slack_message(\n", "            channel=channel_name,\n", "            text=f\"<@{user_id}> || Error occured while Cleaning {table_name}\\n Exception: {e}\",\n", "        )\n", "        raise Exception(\"Not able to Clean data\")\n", "\n", "    final_df = df\n", "    final_df[\"date_updated_ist\"] = (\n", "        datetime.now(timezone(\"Asia/Kolkata\")) - <PERSON><PERSON><PERSON>(days=0)\n", "    ).strftime(\"%Y-%m-%d\")\n", "    final_df[\"is_current\"] = \"True\"\n", "    final_df[\"version\"] = 1\n", "\n", "    # final_df = update_audit_table(df_new, df_existing)\n", "\n", "    print(\"Data updated in audit format: \", final_df.shape)\n", "\n", "    try:\n", "        kwargs = get_kwargs_from_df(final_df, table_name)\n", "        pb.to_trino(data_obj=final_df, **kwargs)\n", "        print(f\"Data successfully written to Trino table infra_expansion_etls.{table_name}\")\n", "    except Exception as e:\n", "        pb.send_slack_message(\n", "            channel=channel_name,\n", "            text=f\"<@{user_id}> || Error occured while writing in {table_name}\\n Exception: {e}\",\n", "        )\n", "        print(f\"Error writing to database: {e}\")\n", "\n", "    print(f\"{table_name} Ingested :|\")\n", "    return final_df"]}, {"cell_type": "code", "execution_count": null, "id": "7ffcd77d-3995-43e7-9c23-c9c51da3c6ad", "metadata": {}, "outputs": [], "source": ["# ---------WH main Tracker------------\n", "\n", "cols = [\n", "    \"s_no\",\n", "    \"fc\",\n", "    \"region\",\n", "    \"tentative_area\",\n", "    \"pm1\",\n", "    \"pm2\",\n", "    \"project_status\",\n", "    \"project_type\",\n", "    \"eta\",\n", "    \"re_layout_sharing_date\",\n", "    \"dry_access_date\",\n", "    \"rfi_date\",\n", "    \"business_type\",\n", "    \"business\",\n", "    \"layout_status\",\n", "    \"layout_link\",\n", "    \"layout_approval_date\",\n", "    \"toilets\",\n", "    \"internal_lighting\",\n", "    \"external_lighting\",\n", "    \"roof_insulation\",\n", "    \"flooring\",\n", "    \"electrical_power_\",\n", "    \"water_supply_works\",\n", "    \"shutter\",\n", "    \"plinth_work\",\n", "    \"project_schedules\",\n", "    \"ordering_status\",\n", "    \"rtsa_closure\",\n", "    \"rtsa_link\",\n", "    \"rtsa_points_open\",\n", "    \"ci__electrical\",\n", "    \"project_scope\",\n", "    \"elv\",\n", "    \"design_services\",\n", "    \"pmc_\",\n", "    \"multitier_shelving_system\",\n", "    \"vrc\",\n", "    \"spiral_gravity_conveyor\",\n", "    \"spr\",\n", "    \"flat_bed_racks_cr_mts\",\n", "    \"servo_stabilizer\",\n", "    \"hvls_fans\",\n", "    \"ups\",\n", "    \"reach_truck\",\n", "    \"bopt\",\n", "    \"bhpt\",\n", "    \"signages\",\n", "    \"stacker\",\n", "    \"cold_room\",\n", "    \"loose_assets\",\n", "    \"tessol_blast_freezers\",\n", "    \"amf_panel_for_dg\",\n", "]\n", "\n", "date_cols = [\n", "    \"eta\",\n", "    \"re_layout_sharing_date\",\n", "    \"dry_access_date\",\n", "    \"rfi_date\",\n", "    \"layout_approval_date\",\n", "    \"toilets\",\n", "    \"internal_lighting\",\n", "    \"external_lighting\",\n", "    \"roof_insulation\",\n", "    \"flooring\",\n", "    \"electrical_power_\",\n", "    \"water_supply_works\",\n", "    \"shutter\",\n", "    \"plinth_work\",\n", "]\n", "\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"16o3KHdP4TobDuTvHmpLt2SRrQ13BhqUUHZLz9brZs_8\",\n", "    \"Project Tracking\",\n", "    0,\n", "    cols,\n", "    date_cols,\n", "    \"%d-%b-%Y\",\n", "    \"wh_base_tracker_v2\",\n", "    \"s_no\",\n", ")\n", "\n", "print(\"Warehouse Base Done\")"]}, {"cell_type": "code", "execution_count": null, "id": "3bb0d97e-80a7-4c70-88b5-9e44dd2e4eb6", "metadata": {}, "outputs": [], "source": ["# ---------wh Tracker1------------\n", "\n", "cols = [\n", "    \"fc_business\",\n", "    \"fc\",\n", "    \"business\",\n", "    \"mts_status\",\n", "    \"sprs_status\",\n", "    \"mezanine_status\",\n", "    \"cr_mts_status\",\n", "]\n", "\n", "date_cols = []\n", "\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"1S08ap1hHr0UOQCIuL6PC2eKRyxlxRLYXHFxPA4Vkoj4\",\n", "    \"Target\",\n", "    -1,\n", "    cols,\n", "    date_cols,\n", "    \"%d-%b-%Y\",\n", "    \"wh_racks_v2\",\n", "    \"fc_business\",\n", ")\n", "\n", "print(\"Warehouse Racks Done\")"]}, {"cell_type": "code", "execution_count": null, "id": "040b3fb1-a26d-46c2-a58e-3826a670882d", "metadata": {}, "outputs": [], "source": ["# ---------wh Tracker2------------\n", "\n", "cols = [\"fc_business\", \"fc\", \"business_type\", \"cr_status\"]\n", "\n", "date_cols = []\n", "\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"1_A9EdjIIDSNs25fQXPMMi4d-zdRGuvMNImeCSta2ZHA\",\n", "    \"Target\",\n", "    -1,\n", "    cols,\n", "    date_cols,\n", "    \"%d-%b-%Y\",\n", "    \"wh_cold_room_v2\",\n", "    \"fc_business\",\n", ")\n", "\n", "print(\"Warehouse CR Done\")"]}, {"cell_type": "code", "execution_count": null, "id": "774e7601-4fe8-43a4-9141-7b878486e586", "metadata": {}, "outputs": [], "source": ["# ---------wh Tracker3------------\n", "\n", "cols = [\"fc_business\", \"fc\", \"business\", \"ci_status\", \"amf_status\"]\n", "\n", "date_cols = []\n", "\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"1b1ESAUGDXC_sHEaSp1X9A2LYOj4n1OgJE0hDLUm-jEM\",\n", "    \"Target\",\n", "    -1,\n", "    cols,\n", "    date_cols,\n", "    \"%d-%b-%Y\",\n", "    \"wh_electrical_v2\",\n", "    \"fc_business\",\n", ")\n", "\n", "print(\"Warehouse Electrical Done\")"]}, {"cell_type": "code", "execution_count": null, "id": "fb0e91c8-3984-4e53-8f85-f8df29738dbf", "metadata": {}, "outputs": [], "source": ["# ---------wh Tracker4------------\n", "\n", "cols = [\"fc_business\", \"fc\", \"business_type\", \"elv_status\"]\n", "\n", "date_cols = []\n", "\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"1QA8FJdogDRV8E3B7iqK7a45I1HCt8m80_r2Y44eo4w8\",\n", "    \"Target\",\n", "    -1,\n", "    cols,\n", "    date_cols,\n", "    \"%d-%b-%Y\",\n", "    \"wh_cctv_v2\",\n", "    \"fc_business\",\n", ")\n", "\n", "print(\"Warehouse CCTV Done\")"]}, {"cell_type": "code", "execution_count": null, "id": "2a74af4b-fe30-4b50-bf07-46a378696ffc", "metadata": {}, "outputs": [], "source": ["# ---------wh Tracker5------------\n", "\n", "cols = [\"fc_business\", \"fc\", \"business\", \"servo_status\", \"ups_status\"]\n", "\n", "date_cols = []\n", "\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"1xS5m4r6Hk_B3oO_rPxGGiEPNTy1frIl14JqAVPBGDIc\",\n", "    \"Target\",\n", "    -1,\n", "    cols,\n", "    date_cols,\n", "    \"%d-%b-%Y\",\n", "    \"wh_servo_ups_v2\",\n", "    \"fc_business\",\n", ")\n", "\n", "print(\"Warehouse Servo UPS Done\")"]}, {"cell_type": "code", "execution_count": null, "id": "67e6272d-15cb-4253-93ea-6a846e820e88", "metadata": {}, "outputs": [], "source": ["# ---------wh Tracker6------------\n", "\n", "cols = [\"fc_business\", \"fc\", \"business\", \"asset\", \"status\", \"cost_id\", \"remark\", \"edd\", \"dd\"]\n", "\n", "date_cols = [\"edd\", \"dd\"]\n", "\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"1F7p6SnAFjnW86LveiWQZC9ujR-1P1AK7QSArf5eoCAk\",\n", "    \"Target\",\n", "    -1,\n", "    cols,\n", "    date_cols,\n", "    \"%d-%b-%Y\",\n", "    \"wh_loose_asset_v2\",\n", "    \"fc_business\",\n", ")\n", "\n", "print(\"Warehouse Loose Asset Done\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}