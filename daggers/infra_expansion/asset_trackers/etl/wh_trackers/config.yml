alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: wh_trackers
dag_type: etl
escalation_priority: low
execution_timeout: 300
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: infra_expansion
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U08JKUHL52B
path: infra_expansion/asset_trackers/etl/wh_trackers
paused: false
pool: infra_expansion_pool
project_name: asset_trackers
schedule:
  end_date: '2025-09-06T00:00:00'
  interval: 0 3-14/4 * * *
  start_date: '2025-06-19T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 3
