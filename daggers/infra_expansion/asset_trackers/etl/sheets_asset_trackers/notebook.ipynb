{"cells": [{"cell_type": "code", "execution_count": null, "id": "f31498f6-d6cc-4a36-be87-427a8b8d5d75", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "from datetime import datetime\n", "from pytz import timezone\n", "from collections import defaultdict\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "\n", "def convert_date_format(date_str, date_form):\n", "\n", "    try:\n", "        if pd.isna(date_str) or str(date_str).lower() == \"nan\":\n", "            return None\n", "        return datetime.strptime(str(date_str).strip(), date_form).strftime(\"%Y-%m-%d\")\n", "    except (ValueErro<PERSON>, TypeError):\n", "        return None\n", "\n", "\n", "def rename_duplicate_columns(df):\n", "    counts = defaultdict(int)\n", "    new_columns = []\n", "\n", "    for col in df.columns:\n", "        counts[col] += 1\n", "        if counts[col] == 1:\n", "            new_columns.append(col)\n", "        else:\n", "            new_columns.append(f\"{col}_{counts[col]-1}\")\n", "\n", "    df.columns = new_columns\n", "    return df\n", "\n", "\n", "def get_kwargs_from_df(df, table_name):\n", "\n", "    dtypes = pd.DataFrame(df.dtypes, columns=[\"type_\"]).reset_index().astype({\"type_\": \"str\"})\n", "    type_map = {\n", "        \"object\": \"VARCHAR\",\n", "        \"int64\": \"INTEGER\",\n", "        \"int32\": \"INTEGER\",\n", "        \"float64\": \"REAL\",\n", "        \"float32\": \"REAL\",\n", "        \"datetime64[ns]\": \"TIMESTAMP(6)\",\n", "    }\n", "    dtypes[\"type\"] = dtypes[\"type_\"].map(type_map)\n", "    columns = [\n", "        {\"name\": row[\"index\"], \"type\": row[\"type\"], \"description\": row[\"index\"]}\n", "        for _, row in dtypes.iterrows()\n", "    ]\n", "\n", "    return {\n", "        \"schema_name\": \"infra_expansion_etls\",\n", "        \"table_name\": table_name,\n", "        \"column_dtypes\": columns,\n", "        \"partition_key\": [],\n", "        \"load_type\": \"truncate\",\n", "        \"table_description\": \"Table with asset tracker\",\n", "    }\n", "\n", "\n", "def update_audit_table(df_new: pd.DataFrame, df_existing: pd.DataFrame = None) -> pd.DataFrame:\n", "    if df_existing is None:\n", "        df_existing = pd.DataFrame(columns=df_new.columns)\n", "    else:\n", "        # Add new columns from df_new to df_existing with None values\n", "        new_columns = [col for col in df_new.columns if col not in df_existing.columns]\n", "        for col in new_columns:\n", "            df_existing[col] = None\n", "\n", "    required_cols = [\"outlet_id\", \"date_updated_ist\", \"is_current\", \"version\"]\n", "    for col in required_cols:\n", "        df_existing[col] = df_existing.get(col, None)\n", "        df_new[col] = df_new.get(col, None)\n", "\n", "    compare_cols = [\n", "        col\n", "        for col in df_new.columns\n", "        if col not in [\"date_updated_ist\", \"updated_ist\", \"is_current\", \"version\"]\n", "    ]\n", "\n", "    updated_rows = []\n", "    invalidated_rows = []\n", "    new_entries = []\n", "\n", "    all_existing = df_existing.copy()\n", "    current_existing = all_existing[all_existing[\"is_current\"] == \"True\"]\n", "\n", "    for _, new_row in df_new.iterrows():\n", "        outlet_id = new_row[\"outlet_id\"]\n", "\n", "        if outlet_id in current_existing[\"outlet_id\"].values:\n", "            old_row = current_existing[current_existing[\"outlet_id\"] == outlet_id].iloc[0]\n", "            old_row_comp = old_row[compare_cols].astype(str)\n", "            new_row_comp = new_row[compare_cols].astype(str)\n", "\n", "            if not old_row_comp.equals(new_row_comp):\n", "                old_row_updated = old_row.copy()\n", "                old_row_updated[\"is_current\"] = \"False\"\n", "                invalidated_rows.append(old_row_updated)\n", "\n", "                new_version = int(old_row[\"version\"]) + 1 if pd.notnull(old_row[\"version\"]) else 1\n", "                new_entry = new_row.copy()\n", "                new_entry[\"version\"] = new_version\n", "                new_entry[\"is_current\"] = \"True\"\n", "                new_entry[\"date_updated_ist\"] = datetime.now(timezone(\"Asia/Kolkata\")).strftime(\n", "                    \"%Y-%m-%d %H:%M:%S\"\n", "                )\n", "                updated_rows.append(new_entry)\n", "        else:\n", "            new_entry = new_row.copy()\n", "            new_entry[\"version\"] = 1\n", "            new_entry[\"is_current\"] = \"True\"\n", "            new_entry[\"date_updated_ist\"] = datetime.now(timezone(\"Asia/Kolkata\")).strftime(\n", "                \"%Y-%m-%d %H:%M:%S\"\n", "            )\n", "            new_entries.append(new_entry)\n", "\n", "    final_df = pd.concat(\n", "        [\n", "            all_existing[~all_existing.index.isin([r.name for r in invalidated_rows])],\n", "            pd.DataFrame(invalidated_rows),\n", "            pd.DataFrame(updated_rows),\n", "            pd.DataFrame(new_entries),\n", "        ],\n", "        ignore_index=True,\n", "    )\n", "\n", "    final_df[\"is_current\"] = final_df[\"is_current\"].astype(str)\n", "    final_df[\"version\"] = final_df[\"version\"].astype(pd.Int64Dtype(), errors=\"ignore\")\n", "\n", "    return final_df.reset_index(drop=True)\n", "\n", "\n", "def read_and_clean_data(\n", "    sheet_id: str,\n", "    sheet_name: str,\n", "    header_index: int,\n", "    cols: list,\n", "    date_cols: list,\n", "    date_format: str,\n", "    table_name: str,\n", ") -> pd.DataFrame:\n", "\n", "    try:\n", "        df = pb.from_sheets(sheet_id, sheet_name)\n", "\n", "    except Exception as e:\n", "        raise ValueError(f\"Failed to read Google Sheet {sheet_id}/{sheet_name}: {e}\")\n", "\n", "    if header_index != -1:\n", "        df.columns = df.iloc[header_index]\n", "        df = df.iloc[header_index + 1 :].reset_index(drop=True)\n", "\n", "    df = df.loc[:, df.columns.notna()]\n", "\n", "    df.columns = [\n", "        str(col)\n", "        .replace(\" \", \"_\")\n", "        .replace(\"\\n\", \"\")\n", "        .replace(\"/\", \"\")\n", "        .replace(\"(\", \"\")\n", "        .replace(\")\", \"\")\n", "        .replace(\"?\", \"\")\n", "        .replace(\"-\", \"\")\n", "        .lower()\n", "        for col in df.columns\n", "    ]\n", "\n", "    df = rename_duplicate_columns(df)\n", "\n", "    df = df[cols]\n", "    df[\"outlet_id\"] = pd.to_numeric(df[\"outlet_id\"], errors=\"coerce\")\n", "    df = df.dropna(subset=[\"outlet_id\"])\n", "    df[\"outlet_id\"] = df[\"outlet_id\"].astype(int)\n", "    df = df.where(pd.notnull(df), None)\n", "\n", "    for col in date_cols:\n", "        df[col] = df[col].apply(lambda x: convert_date_format(x, date_format))\n", "        # df[col] = df[col].astype(\"datetime64[ns]\")\n", "\n", "    return df.reset_index(drop=True)\n", "\n", "\n", "def main(sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name):\n", "    user_id = \"U08JKUHL52B\"\n", "    channel_name = \"tables-infra-alerts\"\n", "\n", "    try:\n", "        df_new = read_and_clean_data(\n", "            sheet_id=sheet_id,\n", "            sheet_name=sheet_name,\n", "            header_index=header_index,\n", "            cols=cols,\n", "            date_cols=date_col,\n", "            date_format=date_form,\n", "            table_name=table_name,\n", "        )\n", "    except Exception as e:\n", "        print(f\"Error reading data: {e}\")\n", "        pb.send_slack_message(\n", "            channel=channel_name,\n", "            text=f\"<@{user_id}> || Error occured while running dag for Sheet_ID: {sheet_id} and Sheet_Name: {sheet_name}\\n Exception: {e}\",\n", "        )\n", "        return None\n", "\n", "    try:\n", "        sql = f\"SELECT * FROM blinkit.infra_expansion_etls.{table_name}\"\n", "        df_existing = pd.read_sql_query(sql=sql, con=con)\n", "    except Exception as e:\n", "        print(f\"Error reading from database: Proceeding with empty df_existing.\")\n", "        pb.send_slack_message(\n", "            channel=channel_name,\n", "            text=f\"<@{user_id}> || Error occured while reading {table_name}\\n Exception: {e}\",\n", "        )\n", "        df_existing = None\n", "\n", "    print(\"\\n\")\n", "\n", "    final_df = update_audit_table(df_new, df_existing)\n", "    final_df[\"version\"] = final_df[\"version\"].astype(int)\n", "    final_df[\"is_current\"] = final_df[\"is_current\"].astype(str)\n", "    final_df[\"outlet_id\"] = final_df[\"outlet_id\"].astype(int)\n", "\n", "    try:\n", "        kwargs = get_kwargs_from_df(final_df, table_name)\n", "        pb.to_trino(data_obj=final_df, **kwargs)\n", "        print(f\"Data successfully written to Trino table infra_expansion_etls.{table_name}\")\n", "    except Exception as e:\n", "        pb.send_slack_message(\n", "            channel=channel_name,\n", "            text=f\"<@{user_id}> || Error occured while writing in {table_name}\\n Exception: {e}\",\n", "        )\n", "        print(f\"Error writing to database: {e}\")\n", "\n", "    return final_df"]}, {"cell_type": "code", "execution_count": null, "id": "16c550f9-aef5-4133-8ceb-286749636b77", "metadata": {}, "outputs": [], "source": ["# ---------Dark Store Projects------------\n", "\n", "cols = [\n", "    \"zone\",\n", "    \"outlet_id\",\n", "    \"cc_code\",\n", "    \"outlet_name\",\n", "    \"city\",\n", "    \"mode_of_ops\",\n", "    \"go_live_date\",\n", "    \"ob_month\",\n", "    \"pm_name\",\n", "    \"team_member_name\",\n", "    \"property_type\",\n", "    \"layout_closure_date\",\n", "    \"layout_status__link_\",\n", "    \"cold_room_config\",\n", "    \"fnv_room_config\",\n", "    \"clear_height_at_cr_area\",\n", "    \"extra_panel_requirementyn\",\n", "    \"re_handover_date\",\n", "    \"final_confirmation_re_ho_date\",\n", "    \"act_re_handover\",\n", "    \"re_handover_status\",\n", "    \"infra_start_date\",\n", "    \"racks_delivery_date\",\n", "    \"racks_start_date\",\n", "    \"racks_completion_date\",\n", "    \"electrical_vendor_deployment_date\",\n", "    \"electrical_completeion_date\",\n", "    \"cctv_start_date\",\n", "    \"cctv_completion_date\",\n", "    \"cr_delivery_date\",\n", "    \"cr_installation_start_date\",\n", "    \"cr_installation_completion_date\",\n", "    \"cold_room_rack_start\",\n", "    \"cold_room_rack_completion\",\n", "    \"expected_infra_handover_date\",\n", "    \"actual_infra_handover_date\",\n", "    \"ib_date\",\n", "    \"printer_racks\",\n", "    \"merchant_access_for_cctv\",\n", "    \"tat_from_re_handover_to_infra_handover\",\n", "    \"infra_remark\",\n", "    \"remarks_on_re_or_other_issues\",\n", "    \"infra_status\",\n", "    \"store_status\",\n", "    \"mayank_status\",\n", "    \"actual_url_link_of_layout\",\n", "    \"first_day_power_reading\",\n", "    \"images\",\n", "    \"loose_asset_delivery_date\",\n", "    \"servoups_delivery_date\",\n", "    \"design_poc\",\n", "    \"seperators_delivery_date\",\n", "]\n", "\n", "date_cols = [\n", "    \"go_live_date\",\n", "    \"layout_closure_date\",\n", "    \"re_handover_date\",\n", "    \"final_confirmation_re_ho_date\",\n", "    \"act_re_handover\",\n", "    \"infra_start_date\",\n", "    \"racks_delivery_date\",\n", "    \"racks_start_date\",\n", "    \"racks_completion_date\",\n", "    \"electrical_vendor_deployment_date\",\n", "    \"electrical_completeion_date\",\n", "    \"cctv_start_date\",\n", "    \"cctv_completion_date\",\n", "    \"cr_delivery_date\",\n", "    \"cr_installation_start_date\",\n", "    \"cr_installation_completion_date\",\n", "    \"cold_room_rack_start\",\n", "    \"cold_room_rack_completion\",\n", "    \"expected_infra_handover_date\",\n", "    \"actual_infra_handover_date\",\n", "    \"ib_date\",\n", "    \"tat_from_re_handover_to_infra_handover\",\n", "    \"loose_asset_delivery_date\",\n", "    \"servoups_delivery_date\",\n", "    \"seperators_delivery_date\",\n", "]\n", "\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"13mr97a0nqF88ktcl4iJHCoWt36eNHgIsQoaHj3-c_tQ\",\n", "    \"New DS implementation_V2\",\n", "    1,\n", "    cols,\n", "    date_cols,\n", "    \"%d-%b-%Y\",\n", "    \"dark_store_projects_tracker_v2\",\n", ")\n", "\n", "print(\"Dark Store Projects Done\")"]}, {"cell_type": "code", "execution_count": null, "id": "5c3ac400-1b7e-4ddf-859f-1377504250bf", "metadata": {}, "outputs": [], "source": ["# -------- <PERSON><PERSON> -----------\n", "cols = [\n", "    \"outlet_id\",\n", "    \"outlet_name\",\n", "    \"final_cc_code\",\n", "    \"erp_1\",\n", "    \"po_1\",\n", "    \"erp_2\",\n", "    \"po_2\",\n", "    \"final_status\",\n", "    \"la_vendor_name\",\n", "]\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"1lMBr-NUcfP9qbt_qEFtfgdtmMOzlia04TbDVub7V-no\",\n", "    \"Dark Stores LA\",\n", "    -1,\n", "    cols,\n", "    [],\n", "    \"%d-%b-%Y\",\n", "    \"loose_asset_tracker\",\n", ")\n", "\n", "print(\"Loose asset Done\")"]}, {"cell_type": "code", "execution_count": null, "id": "d47cb437-322c-46f5-aaef-0def2582b6a1", "metadata": {}, "outputs": [], "source": ["# -------- Racks -----------\n", "\n", "cols = [\n", "    \"outlet_id\",\n", "    \"outlet_name\",\n", "    \"cc_code\",\n", "    \"racks_supply_vendor\",\n", "    \"racks_status\",\n", "    \"racks_erp\",\n", "    \"racks_supplier_po\",\n", "    \"pm\",\n", "    \"quantity\",\n", "    \"dispatch_po\",\n", "]\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"1S08ap1hHr0UOQCIuL6PC2eKRyxlxRLYXHFxPA4Vkoj4\",\n", "    \"Dark Stores\",\n", "    2,\n", "    cols,\n", "    [],\n", "    \"%d-%b-%Y\",\n", "    \"racks_tracker\",\n", ")\n", "\n", "print(\"<PERSON>cks Done\")"]}, {"cell_type": "code", "execution_count": null, "id": "4e9a0c15-ba16-4141-8cd8-affce80dfaf0", "metadata": {}, "outputs": [], "source": ["# -------- Cold Room -----------\n", "\n", "cols = [\n", "    \"outlet_id\",\n", "    \"city\",\n", "    \"store_name\",\n", "    \"final_cc_code\",\n", "    \"cr_status\",\n", "    \"pm_name\",\n", "    \"final_vendor\",\n", "    \"erp_no\",\n", "    \"po_number\",\n", "]\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"1_A9EdjIIDSNs25fQXPMMi4d-zdRGuvMNImeCSta2ZHA\",\n", "    \"FInal CR Working\",\n", "    -1,\n", "    cols,\n", "    [],\n", "    \"%d-%b-%Y\",\n", "    \"cold_room_tracker\",\n", ")\n", "\n", "print(\"Cold Room Done\")"]}, {"cell_type": "code", "execution_count": null, "id": "2b4aa0b8-9a29-4225-a790-3ed2623e7b63", "metadata": {}, "outputs": [], "source": ["# -------- <PERSON><PERSON>s -----------\n", "\n", "cols = [\n", "    \"outlet_id\",\n", "    \"racks_supply_vendor\",\n", "    \"racks_status\",\n", "    \"racks_erp\",\n", "    \"racks_supplier_po\",\n", "    \"servo_ordered\",\n", "    \"servo_status\",\n", "    \"servo_erp\",\n", "    \"servo_po\",\n", "    \"ups_ordered\",\n", "    \"ups_status\",\n", "    \"ups_erp\",\n", "    \"ups_po\",\n", "]\n", "\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"1GzumPbDvMEEr7G0rg5HcfPCfYuzEfdG7QCy9YWXnlfc\",\n", "    \"Master Ordering Sheet\",\n", "    2,\n", "    cols,\n", "    [],\n", "    \"%d-%b-%Y\",\n", "    \"racks_servo_ups_tracker\",\n", ")\n", "\n", "print(\"Racks Servo Ups Done\")"]}, {"cell_type": "code", "execution_count": null, "id": "be33aec4-785b-4d50-9901-88b58602a3ce", "metadata": {}, "outputs": [], "source": ["# -------- <PERSON>ty -----------\n", "\n", "cols = [\n", "    \"outlet_id\",\n", "    \"ordering_date\",\n", "    \"vendor\",\n", "    \"status\",\n", "    \"erp\",\n", "    \"po\",\n", "    \"po_status\",\n", "    \"wcc_summited_date\",\n", "    \"wcc_date\",\n", "    \"grn_date\",\n", "    \"invoice_number\",\n", "    \"utr\",\n", "]\n", "\n", "date_cols = [\"ordering_date\", \"wcc_summited_date\", \"wcc_date\", \"grn_date\"]\n", "\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"1b1ESAUGDXC_sHEaSp1X9A2LYOj4n1OgJE0hDLUm-jEM\",\n", "    \"Master_Sheet\",\n", "    1,\n", "    cols,\n", "    date_cols,\n", "    \"%d-%b-%Y\",\n", "    \"electrical_tracker_v2\",\n", ")\n", "\n", "print(\"Electrical Done\")"]}, {"cell_type": "code", "execution_count": null, "id": "612bb1fc-5de6-4a2c-8352-5d44160af486", "metadata": {}, "outputs": [], "source": ["# -------- CCTV -----------\n", "\n", "\n", "cols = [\"outlet_id\", \"erp_no\", \"po_number\", \"final_status\", \"vendor_name\", \"pm_name\"]\n", "\n", "date_cols = []\n", "\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"1QA8FJdogDRV8E3B7iqK7a45I1HCt8m80_r2Y44eo4w8\",\n", "    \"Dark Stores\",\n", "    -1,\n", "    cols,\n", "    date_cols,\n", "    \"%d-%b-%Y\",\n", "    \"cctv_tracker\",\n", ")\n", "\n", "print(\"CCTV Done\")"]}, {"cell_type": "code", "execution_count": null, "id": "cfb05c1a-1a15-4ee7-a214-f43246f8f274", "metadata": {}, "outputs": [], "source": ["# -------- Seperators -----------\n", "\n", "\n", "cols = [\n", "    \"outlet_id\",\n", "    \"final_cc_code\",\n", "    \"erp_no\",\n", "    \"po_number\",\n", "    \"final_delivery_status\",\n", "    \"final_status\",\n", "    \"city\",\n", "    \"state_mapping\",\n", "    \"zone_mapping\",\n", "    \"manualvendor\",\n", "    \"date_committed_by_1st_vendor\",\n", "    \"actual_delivery_date\",\n", "    \"final_delivery_date\",\n", "]\n", "\n", "date_cols = [\"date_committed_by_1st_vendor\", \"actual_delivery_date\", \"final_delivery_date\"]\n", "\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"1Qu1pc9CQwDMjOKwfPcwfFP1YXAvx5QLqvSpxd4AdMsU\",\n", "    \"FInal Working\",\n", "    0,\n", "    cols,\n", "    date_cols,\n", "    \"%d-%b-%Y\",\n", "    \"seperators_tracker\",\n", ")\n", "\n", "print(\"Seperators Done\")"]}, {"cell_type": "code", "execution_count": null, "id": "a6b1e278-901c-496a-b8b5-69d2bc0cb564", "metadata": {}, "outputs": [], "source": ["cols = date_cols = [\"outlet_id\", \"vendor_name\", \"price\", \"asset_type\"]\n", "\n", "date_cols = []\n", "\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"1gqOPwWW__eL2cIzSsb9-DzaKUFIapFRAlFKJPmLmoaA\",\n", "    \"Main Price Sheet\",\n", "    -1,\n", "    cols,\n", "    date_cols,\n", "    \"%d-%b-%Y\",\n", "    \"price_vendor_list\",\n", ")\n", "\n", "print(\"Price sheet Done\")"]}, {"cell_type": "code", "execution_count": null, "id": "efab910f-a078-4ca9-994e-01681e6a40eb", "metadata": {}, "outputs": [], "source": ["# -------- <PERSON><PERSON> Updated -----------\n", "\n", "\n", "cols = [\n", "    \"outlet_id\",\n", "    \"outlet_name\",\n", "    \"cc_code\",\n", "    \"servo_ordered\",\n", "    \"servo_status\",\n", "    \"servo_erp\",\n", "    \"servo_po\",\n", "    \"servo_dispatch_po\",\n", "    \"servo_etd\",\n", "    \"ups_ordered\",\n", "    \"ups_status\",\n", "    \"ups_erp\",\n", "    \"ups_po\",\n", "    \"ups_dispatch_po\",\n", "    \"ups_etd\",\n", "]\n", "\n", "\n", "date_cols = [\"servo_etd\", \"ups_etd\"]\n", "\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"1xS5m4r6Hk_B3oO_rPxGGiEPNTy1frIl14JqAVPBGDIc\",\n", "    \"Dark Stores\",\n", "    2,\n", "    cols,\n", "    date_cols,\n", "    \"%d-%b-%Y\",\n", "    \"servo_ups_tracker_v2\",\n", ")\n", "\n", "print(\"Servo Ups Updated Done\")"]}, {"cell_type": "code", "execution_count": null, "id": "0142d225-b283-4374-8894-3d4c2b4a4193", "metadata": {}, "outputs": [], "source": ["# -------- PMCS Updated -----------\n", "\n", "\n", "cols = [\n", "    \"property_name\",\n", "    \"property_type\",\n", "    \"updated_cost_center\",\n", "    \"outlet_id\",\n", "    \"sap_cc_valid_from_ddmmyyyy\",\n", "    \"sap_cc_valid_till_ddmmyyyy\",\n", "    \"location_status\",\n", "    \"city\",\n", "    \"sap_address\",\n", "    \"pin_code\",\n", "    \"status_as_per_e3\",\n", "    \"state\",\n", "    \"wh_tagging\",\n", "    \"old_cost_center\",\n", "]\n", "\n", "\n", "date_cols = [\"sap_cc_valid_from_ddmmyyyy\", \"sap_cc_valid_till_ddmmyyyy\"]\n", "\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"161liOdrMSQN6TevSvNlFADt_D9khf8g1G38UiqqOO9A\",\n", "    \"BCPL Dark Stores List\",\n", "    0,\n", "    cols,\n", "    date_cols,\n", "    \"%d-%b-%Y\",\n", "    \"pmcs_dark_stores_tracker\",\n", ")\n", "\n", "print(\"PMCS Done\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}