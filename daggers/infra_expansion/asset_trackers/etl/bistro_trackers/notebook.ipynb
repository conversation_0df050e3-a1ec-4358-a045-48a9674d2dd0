{"cells": [{"cell_type": "code", "execution_count": null, "id": "32067d6e-1b94-4051-b575-7e181524acc2", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from io import BytesIO, StringIO\n", "import re\n", "import requests\n", "from urllib.parse import urlencode\n", "from datetime import datetime, timedelta\n", "from pytz import timezone\n", "import warnings\n", "\n", "import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "from datetime import datetime\n", "from pytz import timezone\n", "from collections import defaultdict\n", "\n", "import json\n", "import os\n", "import base64\n", "import io\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "\n", "def convert_date_format(date_str, date_form):\n", "    try:\n", "        if pd.isna(date_str) or str(date_str).lower() == \"nan\":\n", "            return None\n", "        return datetime.strptime(str(date_str).strip(), date_form).strftime(\"%Y-%m-%d\")\n", "    except (ValueErro<PERSON>, TypeError):\n", "        return None\n", "\n", "\n", "def rename_duplicate_columns(df):\n", "    counts = defaultdict(int)\n", "    new_columns = []\n", "\n", "    for col in df.columns:\n", "        counts[col] += 1\n", "        if counts[col] == 1:\n", "            new_columns.append(col)\n", "        else:\n", "            new_columns.append(f\"{col}_{counts[col]-1}\")\n", "\n", "    df.columns = new_columns\n", "    return df\n", "\n", "\n", "def get_kwargs_from_df(df, table_name):\n", "\n", "    dtypes = pd.DataFrame(df.dtypes, columns=[\"type_\"]).reset_index().astype({\"type_\": \"str\"})\n", "    type_map = {\n", "        \"object\": \"VARCHAR\",\n", "        \"int64\": \"INTEGER\",\n", "        \"int32\": \"INTEGER\",\n", "        \"float64\": \"REAL\",\n", "        \"float32\": \"REAL\",\n", "        \"datetime64[ns]\": \"TIMESTAMP(6)\",\n", "    }\n", "\n", "    dtypes[\"type\"] = dtypes[\"type_\"].map(type_map)\n", "    columns = [\n", "        {\"name\": row[\"index\"], \"type\": row[\"type\"], \"description\": row[\"index\"]}\n", "        for _, row in dtypes.iterrows()\n", "    ]\n", "\n", "    return {\n", "        \"schema_name\": \"infra_expansion_etls\",\n", "        \"table_name\": table_name,\n", "        \"column_dtypes\": columns,\n", "        \"partition_key\": [],\n", "        \"load_type\": \"truncate\",\n", "        \"table_description\": \"Table for warehouse trackers Dump\",\n", "    }\n", "\n", "\n", "def read_and_clean_data(df, header_index, primary, cols, date_cols, date_format, table_name):\n", "\n", "    if header_index != -1:\n", "        df.columns = df.iloc[header_index]\n", "        df = df.iloc[header_index + 1 :].reset_index(drop=True)\n", "\n", "    df = df.loc[:, df.columns.notna()]\n", "\n", "    df.columns = [\n", "        str(col)\n", "        .replace(\" \", \"_\")\n", "        .replace(\"\\n\", \"\")\n", "        .replace(\"/\", \"\")\n", "        .replace(\"(\", \"\")\n", "        .replace(\")\", \"\")\n", "        .replace(\"?\", \"\")\n", "        .replace(\"-\", \"\")\n", "        .replace(\"&\", \"\")\n", "        .replace(\"+\", \"\")\n", "        .lower()\n", "        for col in df.columns\n", "    ]\n", "\n", "    df = rename_duplicate_columns(df)\n", "\n", "    df = df[cols]\n", "    df = df.where(pd.notnull(df), None)\n", "\n", "    for col in date_cols:\n", "        df[col] = df[col].apply(lambda x: convert_date_format(x, date_format))\n", "        # df[col] = df[col].astype(\"datetime64[ns]\")\n", "\n", "    return df.reset_index(drop=True)\n", "\n", "\n", "def main(sheet_id, sheet_name, header_index, cols, date_cols, date_format, table_name, primary):\n", "    user_id = \"U08JKUHL52B\"\n", "    channel_name = \"tables-infra-alerts\"\n", "\n", "    try:\n", "        df = pb.from_sheets(sheet_id, sheet_name)\n", "\n", "    except Exception as e:\n", "        pb.send_slack_message(\n", "            channel=channel_name,\n", "            text=f\"<@{user_id}> || Error occured while reading {sheet_id}\\n Exception: {e}\",\n", "        )\n", "        raise ValueError(f\"Failed to read Google Sheet {sheet_id}/{sheet_name}: {e}\")\n", "\n", "    try:\n", "        df = read_and_clean_data(\n", "            df, header_index, primary, cols, date_cols, date_format, table_name\n", "        )\n", "        print(\"Data Cleaned\")\n", "    except Exception as e:\n", "        pb.send_slack_message(\n", "            channel=channel_name,\n", "            text=f\"<@{user_id}> || Error occured while Cleaning {table_name}\\n Exception: {e}\",\n", "        )\n", "        raise Exception(\"Not able to Clean data\")\n", "\n", "    final_df = df\n", "    final_df[\"date_updated_ist\"] = (\n", "        datetime.now(timezone(\"Asia/Kolkata\")) - <PERSON><PERSON><PERSON>(days=0)\n", "    ).strftime(\"%Y-%m-%d\")\n", "    final_df[\"is_current\"] = \"True\"\n", "    final_df[\"version\"] = 1\n", "\n", "    # final_df = update_audit_table(df_new, df_existing)\n", "\n", "    print(\"Data updated in audit format: \", final_df.shape)\n", "\n", "    try:\n", "        kwargs = get_kwargs_from_df(final_df, table_name)\n", "        pb.to_trino(data_obj=final_df, **kwargs)\n", "        print(f\"Data successfully written to Trino table infra_expansion_etls.{table_name}\")\n", "    except Exception as e:\n", "        pb.send_slack_message(\n", "            channel=channel_name,\n", "            text=f\"<@{user_id}> || Error occured while writing in {table_name}\\n Exception: {e}\",\n", "        )\n", "        print(f\"Error writing to database: {e}\")\n", "\n", "    print(f\"{table_name} Ingested :|\")\n", "    return final_df"]}, {"cell_type": "code", "execution_count": null, "id": "4402f0ab-05f0-49d2-8c71-bec59262b122", "metadata": {}, "outputs": [], "source": ["# ---------Bistro main Tracker------------\n", "\n", "cols = [\n", "    \"scouting_location\",\n", "    \"store_name\",\n", "    \"city_\",\n", "    \"cc_code\",\n", "    \"status\",\n", "    \"infra_entry_date\",\n", "    \"infra_completion_date\",\n", "    \"rfid\",\n", "    \"actual_infra_completion_date\",\n", "    \"ops_handover_date\",\n", "    \"tentative_go_live_date\",\n", "    \"load\",\n", "]\n", "\n", "date_cols = [\n", "    \"infra_entry_date\",\n", "    \"infra_completion_date\",\n", "    \"actual_infra_completion_date\",\n", "    \"ops_handover_date\",\n", "    \"tentative_go_live_date\",\n", "]\n", "\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"1eT47fH-ntglywFq8xB4HHy3NiJWLkJ_9r94XYK_Vh8Q\",\n", "    \"Dependency Tracker\",\n", "    0,\n", "    cols,\n", "    date_cols,\n", "    \"%d-%b-%Y\",\n", "    \"bistro_base_tracker\",\n", "    \"scouting_location\",\n", ")\n", "\n", "print(\"Bistro Base Done\")"]}, {"cell_type": "code", "execution_count": null, "id": "5f1d0269-77dc-4bc0-a947-cb5862040b2a", "metadata": {}, "outputs": [], "source": ["# --------- CR Tracker------------\n", "\n", "cols = [\n", "    \"outlet_name\",\n", "    \"cc_code\",\n", "    \"scouting_location\",\n", "    \"store_type\",\n", "    \"store_status\",\n", "    \"status\",\n", "    \"kitchen_layout_\",\n", "    \"fr_layout\",\n", "    \"layout_config\",\n", "    \"layout_date\",\n", "    \"target_cr_delivery_date\",\n", "    \"erp_number\",\n", "    \"cr_vendor\",\n", "    \"status_1\",\n", "    \"cr_delivery_date\",\n", "    \"cr_installation_start_date\",\n", "    \"cr_completion_date\",\n", "    \"rca_remarks\",\n", "]\n", "\n", "date_cols = [\n", "    \"layout_date\",\n", "    \"target_cr_delivery_date\",\n", "    \"cr_delivery_date\",\n", "    \"cr_installation_start_date\",\n", "    \"cr_completion_date\",\n", "]\n", "\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"1_A9EdjIIDSNs25fQXPMMi4d-zdRGuvMNImeCSta2ZHA\",\n", "    \"Bistro\",\n", "    0,\n", "    cols,\n", "    date_cols,\n", "    \"%d-%b-%Y\",\n", "    \"bistro_cr_tracker\",\n", "    \"outlet_name\",\n", ")\n", "\n", "print(\"Bistro CR Done\")"]}, {"cell_type": "code", "execution_count": null, "id": "30fa29f8-2832-49f1-ba4f-b762c0806134", "metadata": {}, "outputs": [], "source": ["# --------- <PERSON>vo UPS Tracker------------\n", "\n", "cols = [\n", "    \"outlet_name\",\n", "    \"cc_code\",\n", "    \"cc_code_status\",\n", "    \"status\",\n", "    \"target_servo_delivery_date\",\n", "    \"store_type\",\n", "    \"store_status\",\n", "    \"servo_supply_vendor\",\n", "    \"servo_status\",\n", "    \"servo_erp\",\n", "    \"servo_supplier_po\",\n", "    \"servo_dispatch_po\",\n", "    \"servo_kva\",\n", "    \"ups_supply_vendor\",\n", "    \"ups_status\",\n", "    \"ups_erp\",\n", "    \"ups_supplier_po\",\n", "    \"ups_dispatch_po\",\n", "    \"comments\",\n", "    \"ups_final_status\",\n", "    \"servo_final_status\",\n", "]\n", "\n", "date_cols = [\"target_servo_delivery_date\"]\n", "\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"1xS5m4r6Hk_B3oO_rPxGGiEPNTy1frIl14JqAVPBGDIc\",\n", "    \"Bistro\",\n", "    0,\n", "    cols,\n", "    date_cols,\n", "    \"%d-%b-%Y\",\n", "    \"bistro_servo_ups_tracker\",\n", "    \"outlet_name\",\n", ")\n", "\n", "print(\"Bistro servoups Done\")"]}, {"cell_type": "code", "execution_count": null, "id": "d8f25e0d-db4e-44fa-a843-13a025a07652", "metadata": {}, "outputs": [], "source": ["# --------- <PERSON><PERSON> Tracker------------\n", "\n", "cols = [\n", "    \"outlet_name\",\n", "    \"cc_code\",\n", "    \"status\",\n", "    \"racks_supply_vendor\",\n", "    \"racks_status\",\n", "    \"racks_erp\",\n", "    \"racks_supplier_po\",\n", "    \"racks_dispatch_po\",\n", "    \"final_status\",\n", "    \"remarks\",\n", "]\n", "\n", "date_cols = []\n", "\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"1S08ap1hHr0UOQCIuL6PC2eKRyxlxRLYXHFxPA4Vkoj4\",\n", "    \"Bistro\",\n", "    0,\n", "    cols,\n", "    date_cols,\n", "    \"%d-%b-%Y\",\n", "    \"bistro_racks_tracker\",\n", "    \"outlet_name\",\n", ")\n", "\n", "print(\"Bistro Racks Done\")"]}, {"cell_type": "code", "execution_count": null, "id": "04a89888-c3d9-40bb-9c0e-f6ef7ebead63", "metadata": {}, "outputs": [], "source": ["# --------- FB LA Tracker------------\n", "\n", "cols = [\n", "    \"outlet_name\",\n", "    \"cc_code\",\n", "    \"scouting_location\",\n", "    \"store_type\",\n", "    \"store_status\",\n", "    \"status\",\n", "    \"target_fabricated_equipement_delivery_date_\",\n", "    \"erp_number_\",\n", "    \"po_number_\",\n", "    \"cc_code_1\",\n", "    \"fabricated_equipment_vendor\",\n", "    \"fabricated_equipment_delivery_status\",\n", "    \"fabricated_equipment_delivery_date\",\n", "    \"rca_remarks\",\n", "    \"target_loose_asset_delivery_date\",\n", "    \"erp_number__1\",\n", "    \"po_number__1\",\n", "    \"loose_asset_vendor\",\n", "    \"loose_asset_delivery_status\",\n", "    \"loose_asset_delivery_date\",\n", "    \"rca_remarks_1\",\n", "]\n", "\n", "date_cols = [\n", "    \"fabricated_equipment_delivery_date\",\n", "    \"target_loose_asset_delivery_date\",\n", "    \"target_loose_asset_delivery_date\",\n", "    \"loose_asset_delivery_date\",\n", "]\n", "\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"1srParNPQ-V4FZBwZ7ICCVzC2BYr2M5Sdf-4efyiCrYw\",\n", "    \"Bistro Mastersheet\",\n", "    1,\n", "    cols,\n", "    date_cols,\n", "    \"%d-%b-%Y\",\n", "    \"bistro_fb_la_tracker\",\n", "    \"outlet_name\",\n", ")\n", "\n", "print(\"Bistro FB LA Done\")"]}, {"cell_type": "code", "execution_count": null, "id": "5e6a7c78-a289-4d2c-9827-f5cf22e81883", "metadata": {}, "outputs": [], "source": ["# --------- C<PERSON> Tracker------------\n", "\n", "cols = [\n", "    \"outlet_id\",\n", "    \"store_name\",\n", "    \"cc_code\",\n", "    \"z_id\",\n", "    \"address\",\n", "    \"lat_long_\",\n", "    \"google_map_location_\",\n", "    \"property_status\",\n", "    \"cost_approval_date\",\n", "    \"status\",\n", "    \"order_date\",\n", "    \"vendor\",\n", "    \"target_ci_delivery_date\",\n", "    \"ci_delivery_date\",\n", "    \"ci_erp\",\n", "    \"ci_po\",\n", "    \"wcc_date\",\n", "    \"grn_date\",\n", "    \"remarks\",\n", "]\n", "\n", "date_cols = [\n", "    \"cost_approval_date\",\n", "    \"target_ci_delivery_date\",\n", "    \"ci_delivery_date\",\n", "    \"wcc_date\",\n", "    \"grn_date\",\n", "]\n", "\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"1b1ESAUGDXC_sHEaSp1X9A2LYOj4n1OgJE0hDLUm-jEM\",\n", "    \"<PERSON><PERSON><PERSON>_Master\",\n", "    1,\n", "    cols,\n", "    date_cols,\n", "    \"%d-%b-%Y\",\n", "    \"bistro_ci_tracker\",\n", "    \"outlet_id\",\n", ")\n", "\n", "print(\"Bistro CI Done\")"]}, {"cell_type": "code", "execution_count": null, "id": "8947aad0-2845-434d-8b50-f5d81f23710e", "metadata": {}, "outputs": [], "source": ["# --------- CCTV Tracker------------\n", "\n", "cols = [\n", "    \"outlet_id\",\n", "    \"store_name\",\n", "    \"cc_code\",\n", "    \"z_id\",\n", "    \"address\",\n", "    \"lat_long_\",\n", "    \"google_map_location_\",\n", "    \"property_status\",\n", "    \"cost_approval_date\",\n", "    \"vendor\",\n", "    \"target_cctv_delivery_date\",\n", "    \"cctv_delivery_date\",\n", "    \"cctv_erp\",\n", "    \"cctv_po\",\n", "    \"cctv_delivery_status\",\n", "]\n", "\n", "date_cols = [\"cost_approval_date\", \"target_cctv_delivery_date\", \"cctv_delivery_date\"]\n", "\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"1QA8FJdogDRV8E3B7iqK7a45I1HCt8m80_r2Y44eo4w8\",\n", "    \"Bistro\",\n", "    1,\n", "    cols,\n", "    date_cols,\n", "    \"%d-%b-%Y\",\n", "    \"bistro_cctv_tracker\",\n", "    \"outlet_id\",\n", ")\n", "\n", "print(\"Bistro CCTV Done\")"]}, {"cell_type": "code", "execution_count": null, "id": "647259d1-9fe7-4627-9750-b667950a2a83", "metadata": {}, "outputs": [], "source": ["# --------- Layout Tracker------------\n", "\n", "cols = [\"store_name_\", \"outlet_id_\", \"cc_code_\", \"fr_layout\", \"layout_config\", \"layout_date\"]\n", "\n", "date_cols = [\"layout_date\"]\n", "\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"1kXo8HNM_YipVEaOGoY1eX9tkK4mJhQaGLfXGCTxgICs\",\n", "    \"Data\",\n", "    -1,\n", "    cols,\n", "    date_cols,\n", "    \"%d-%b-%Y\",\n", "    \"bistro_layout_tracker\",\n", "    \"store_name_\",\n", ")\n", "\n", "print(\"Bistro Layout Done\")"]}, {"cell_type": "code", "execution_count": null, "id": "894b60aa-b664-4b3d-9985-c070d19a2048", "metadata": {}, "outputs": [], "source": ["cols = [\"vendor\", \"poc\", \"asset\"]\n", "\n", "date_cols = []\n", "\n", "# (sheet_id, sheet_name, header_index, cols, date_col, date_form, table_name)\n", "final_df = main(\n", "    \"1NWKSIA8g95uMcW0BuhRCSsp-jZ_V7739VjcM59tEuXk\",\n", "    \"POC Details\",\n", "    0,\n", "    cols,\n", "    date_cols,\n", "    \"%d-%b-%Y\",\n", "    \"vendor_asset_mapping\",\n", "    \"vendor\",\n", ")\n", "\n", "print(\"Warehouse Racks Done\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}