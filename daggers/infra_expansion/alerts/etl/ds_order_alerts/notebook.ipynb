{"cells": [{"cell_type": "code", "execution_count": null, "id": "55a5d4fe-17e1-421e-b776-7135342edc99", "metadata": {}, "outputs": [], "source": ["!pip install plotly==5.14.1 folium==0.14.0 matplotlib==3.5.1 pandas==1.5.3 numpy==1.26.4"]}, {"cell_type": "code", "execution_count": null, "id": "95d68c93-6997-4ecf-9817-7a9419f17c60", "metadata": {}, "outputs": [], "source": ["!pip install Pillow"]}, {"cell_type": "code", "execution_count": null, "id": "2ed5b5af-141b-436f-9b15-5139a08cf07b", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import textwrap\n", "from datetime import datetime\n", "\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.set_option(\"display.max_rows\", 100)\n", "pd.set_option(\"display.max_columns\", 80)\n", "user_id = \"U08JKUHL52B\"\n", "channel_name = \"tables-infra-alerts\"\n", "actual_channel = \"infra-vendor-podcast\"\n", "ds_channel = \"stores-infra-ordering-delivery-pendency\"\n", "\n", "dic1 = {\n", "    \"Racks\": [\"U06FLBL5W8G\"],\n", "    \"Cold Room\": [\"U0827D2M94M\"],\n", "    \"Electrical\": [\"U03RSAH5H0W\"],\n", "    \"CCTV\": [\"U08PL9QCXUL\"],\n", "    \"Loose Asset\": [\"U086B6H47CN\"],\n", "    \"Servo\": [\"U07SVCN9GS3\", \"U06FLBL5W8G\"],\n", "    \"UPS\": [\"U07SVCN9GS3\", \"U06FLBL5W8G\"],\n", "    \"Separator\": [\"U07JPQFEZ5W\"],\n", "    \"niket\": [\"U03RLJ28N9J\"],\n", "    \"rahul\": [\"U07HMK8T411\"],\n", "}\n", "\n", "\n", "def truncate_with_ellipsis(text, max_len=30):\n", "    return text if len(text) <= max_len else text[: max_len - 3] + \"...\"\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    header_color,\n", "    color_cols,\n", "    cond=0,\n", "    min_day=3,\n", "    col_width=3.5,\n", "    row_height=0.625,\n", "    font_size=14,\n", "    row_colors=[\"w\"],\n", "    edge_color=\"#dedede\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    cell_loc=\"center\",\n", "    pad_spaces=2,\n", "    **kwargs,\n", "):\n", "    def pad_and_truncate(x, col_name):\n", "        x_str = str(x)\n", "        if col_name == data.columns[0]:  # Only truncate first column\n", "            x_str = truncate_with_ellipsis(x_str, max_len=50)\n", "        return f\"{'   ' * pad_spaces}{x_str}{'   ' * pad_spaces}\"\n", "\n", "    # Apply padding and optional truncation\n", "    padded_data = data.copy().astype(str)\n", "    for col in padded_data.columns:\n", "        padded_data[col] = padded_data[col].apply(lambda x: pad_and_truncate(x, col))\n", "\n", "    fig, ax = plt.subplots(\n", "        figsize=(\n", "            (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        )\n", "    )\n", "    ax.axis(\"off\")\n", "\n", "    mpl_table = ax.table(\n", "        cellText=padded_data.values,\n", "        bbox=bbox,\n", "        colLabels=[f\"{' ' * pad_spaces}{col}{' ' * pad_spaces}\" for col in data.columns],\n", "        cellLoc=cell_loc,\n", "        **kwargs,\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "\n", "    for (row, col), cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "\n", "        if row == 0:\n", "            # Header row\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            if col == 0:\n", "                cell.set_text_props(weight=\"bold\")\n", "\n", "            if col not in color_cols:\n", "                cell.set_facecolor(row_colors[(row - 1) % len(row_colors)])\n", "                continue\n", "\n", "            try:\n", "                ageing = pd.to_numeric(data.iloc[row - 1][\"Ageing\"], errors=\"coerce\")\n", "                act_re = data.iloc[row - 1][\"Act RE HO Date\"]\n", "                status = str(data.iloc[row - 1][\"Status\"]).strip()\n", "                layout = data.iloc[row - 1][\"Layout Date\"]\n", "                rfi = data.iloc[row - 1][\"RFI Date\"]\n", "                if pd.isna(act_re) and (pd.isna(layout) and cond == 1):\n", "                    continue\n", "\n", "                elif pd.isna(act_re) and not pd.isna(layout) and cond == 1:\n", "\n", "                    if status in [\"ERP Pending\", \"PO Pending\", \"Vendor Selection Pending\"]:\n", "                        cell.set_facecolor(\"#fff3cc\")  # Yellow\n", "                    else:\n", "                        cell.set_facecolor(\"white\")  # White\n", "                    continue\n", "                elif cond == 2 and pd.isna(act_re):\n", "                    if status in [\"ERP Pending\", \"PO Pending\", \"Vendor Selection Pending\"]:\n", "                        cell.set_facecolor(\"#fff3cc\")  # Light pink\n", "                    else:\n", "                        cell.set_facecolor(\"white\")\n", "\n", "                # print(f\"row: {row} - Ageing: {ageing}\")\n", "                else:\n", "                    if ageing >= min_day:\n", "                        cell.set_facecolor(\"#ea9999\")  # Red\n", "                    elif 1 <= ageing <= min_day - 1 or status in [\n", "                        \"ERP Pending\",\n", "                        \"PO Pending\",\n", "                        \"Vendor Selection Pending\",\n", "                        \"transit\",\n", "                    ]:\n", "                        cell.set_facecolor(\"#f5cbcc\")  # Light pink\n", "                    elif ageing == 0:\n", "                        cell.set_facecolor(\"#fff3cc\")\n", "            except Exception as e:\n", "                print(f\"Error processing cell ({row}, {col}): {e}\")\n", "                cell.set_facecolor(\"white\")\n", "\n", "            except Exception:\n", "                cell.set_facecolor(row_colors[row % len(row_colors)])\n", "\n", "    return fig, ax\n", "\n", "\n", "from PIL import Image, ImageDraw, ImageFont\n", "\n", "\n", "def create_header_image(text, width, height=60, font_size=24):\n", "    # Create blank image\n", "    header_img = Image.new(\"RGB\", (width, height), color=(246, 246, 246))  # Light blue\n", "    draw = ImageDraw.Draw(header_img)\n", "\n", "    # Try loading a large bold font\n", "    try:\n", "        font = ImageFont.truetype(\"arialbd.ttf\", font_size)  # bold Arial if available\n", "    except IOError:\n", "        try:\n", "            font = ImageFont.truetype(\"DejaVuSans-Bold.ttf\", font_size)  # common on Linux\n", "        except IOError:\n", "            font = ImageFont.load_default()\n", "\n", "    # Center text\n", "    text_width, text_height = draw.textbbox((0, 0), text, font=font)[\n", "        2:\n", "    ]  # more accurate than textsize\n", "    position = ((width - text_width) // 2, (height - text_height) // 2)\n", "    draw.text(position, text, fill=\"black\", font=font)\n", "\n", "    return header_img\n", "\n", "\n", "def save_empty_image(\n", "    message, output_path=\"no_data.png\", col_width=3.5, row_height=0.625, font_size=12, n_cols=6\n", "):\n", "    # Estimate width/height like render_mpl_table\n", "    width = int((n_cols + 1) * col_width * 20)  # scale factor ~20 for pixels\n", "    height = int((1 + 1) * row_height * 80)  # one row + header, scale factor ~80\n", "\n", "    img = Image.new(\"RGB\", (width, height), color=(246, 246, 246))  # same bg as tables\n", "    draw = ImageDraw.Draw(img)\n", "\n", "    # Load font matching render_mpl_table style\n", "    try:\n", "        font = ImageFont.truetype(\"DejaVuSans.ttf\", font_size)\n", "    except IOError:\n", "        font = ImageFont.load_default()\n", "\n", "    text_width, text_height = draw.textbbox((0, 0), message, font=font)[2:]\n", "    position = ((width - text_width) // 2, (height - text_height) // 2)\n", "\n", "    draw.text(position, message, fill=\"black\", font=font)\n", "    img.save(output_path)\n", "\n", "\n", "def combine_images_with_headers(image_paths, headers, output_path=\"combined_output.png\"):\n", "    assert len(image_paths) == len(headers), \"Each image must have a corresponding header.\"\n", "\n", "    combined_blocks = []\n", "\n", "    for path, header in zip(image_paths, headers):\n", "        table_img = Image.open(path)\n", "        header_img = create_header_image(header, table_img.width)\n", "\n", "        # Make block image with same width as the widest (header or table)\n", "        block_width = max(header_img.width, table_img.width)\n", "        block_height = header_img.height + table_img.height\n", "        block_img = Image.new(\"RGB\", (block_width, block_height), color=(255, 255, 255))\n", "\n", "        # Center header (already matches table width)\n", "        block_img.paste(header_img, ((block_width - header_img.width) // 2, 0))\n", "\n", "        # Center-align table image under header\n", "        x_offset = (block_width - table_img.width) // 2\n", "        block_img.paste(table_img, (x_offset, header_img.height))\n", "\n", "        combined_blocks.append(block_img)\n", "\n", "    # Final image combining all blocks vertically\n", "    total_height = sum(block.height for block in combined_blocks)\n", "    max_width = max(block.width for block in combined_blocks)\n", "    final_img = Image.new(\"RGB\", (max_width, total_height), color=(255, 255, 255))\n", "\n", "    y_offset = 0\n", "    for block in combined_blocks:\n", "        x_offset = (max_width - block.width) // 2\n", "        final_img.paste(block, (x_offset, y_offset))\n", "        y_offset += block.height\n", "\n", "    final_img.save(output_path)\n", "    return output_path\n", "\n", "\n", "def slack_alert_at(df, asset, dic1, channel, niket, rahul, files):\n", "    # Handle list inputs\n", "    niket = niket[0] if isinstance(niket, list) else niket\n", "    rahul = rahul[0] if isinstance(rahul, list) else rahul\n", "\n", "    mentions = []\n", "\n", "    if asset in dic1:\n", "        user_tags = [f\"<@{user_id}>\" for user_id in dic1[asset]]\n", "        mentions.append(f\"POC: {', '.join(user_tags)}\")\n", "\n", "    mentions_text = \"\\n\".join(mentions)\n", "\n", "    # Compose message\n", "    message = f\"Express Stores Pendency for {asset} {mentions_text}\\n\" f\"cc: <@{niket}> <@{rahul}>\"\n", "\n", "    # Send Slack message with attachment\n", "    pb.send_slack_message(\n", "        channel=channel,\n", "        text=message,\n", "        files=files,\n", "    )"]}, {"cell_type": "markdown", "id": "8993bdbd-fafa-4acf-9b8b-e869b785007e", "metadata": {}, "source": ["<h2>Racks</h2>"]}, {"cell_type": "code", "execution_count": null, "id": "b50514de-d335-4528-b477-5569272d8375", "metadata": {}, "outputs": [], "source": ["query = \"\"\"with pms as (select zone, outlet_id, cc_code, ob_month, \n", "layout_closure_date, final_confirmation_re_ho_date,\n", "outlet_name, city, mode_of_ops, go_live_date, \n", "re_handover_date, act_re_handover, pm_name,\n", "re_handover_status, infra_start_date, \n", "racks_delivery_date, racks_start_date, racks_completion_date, \n", "electrical_vendor_deployment_date, electrical_completeion_date, \n", "cctv_start_date, cctv_completion_date, \n", "cr_delivery_date, cr_installation_start_date, cr_installation_completion_date,\n", "cold_room_rack_start, cold_room_rack_completion, \n", "loose_asset_delivery_date, servoups_delivery_date, seperators_delivery_date,\n", "expected_infra_handover_date, actual_infra_handover_date, \n", "ib_date, infra_status, regexp_replace(lower(store_status), '\\\\b([a-z])', x -> upper(x[1])) as store_status\n", "from blinkit_iceberg.infra_expansion_etls.dark_store_projects_tracker_v2\n", "where is_current = 'True'),\n", "\n", "pms_exp as (select *, date(date_trunc('month', try(date_parse(trim(ob_date_live_date), '%d-%b-%Y')))) as ob_date,\n", "date(try(date_parse(trim(ready_for_infra_date_for_asset_deployment), '%d-%b-%Y'))) as rfi_date\n", "FROM ds_etls.pms_v3_master_daily_snapshot\n", "WHERE \n", "    data_updated_on = (\n", "        SELECT\n", "            max(data_updated_on)\n", "        FROM\n", "            ds_etls.pms_v3_master_daily_snapshot\n", "        WHERE\n", "            data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY\n", "    )\n", "    AND data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY and store_type is not null\n", "    AND 1 = 1),\n", "    \n", "\n", "racks as (select distinct a.outlet_id, a.outlet_name, a.cc_code, date_trunc('month', date(actual_infra_handover_date))  as infra_handover_month,\n", "regexp_replace(lower(store_type), '(\\b[a-z])', x -> upper(x[1])) as store_type, a.racks_delivery_date as delivery_date,\n", "date_trunc('month', ob_date) as ob_month, \n", "a.city, a.re_handover_date, a.act_re_handover, a.store_status, a.actual_infra_handover_date, 'Racks' as asset_type, \n", "nullif(trim(b.racks_erp), '') as erp_no, nullif(trim(b.racks_supplier_po), '') as po_no, nullif(trim(b.racks_supply_vendor), '') as vendor, nullif(trim(b.racks_status), '') as status,\n", "layout_closure_date, rfi_date, nullif(trim(b.dispatch_po), '') as dispatch_po\n", "from pms a\n", "left join blinkit.infra_expansion_etls.racks_tracker b on a.outlet_id = b.outlet_id and b.is_current = 'True'\n", "left join pms_exp d on d.outlet_id = cast(a.outlet_id as varchar) \n", "where (date(d.rfi_date) >= date '2025-01-01' or date(re_handover_date) >= date '2025-01-01')  and a.store_status not in ('Hold', 'Dropped')),\n", "\n", "\n", "dates as (SELECT date_value as dt\n", "FROM UNNEST(\n", "    sequence(DATE '2025-05-01', current_date, INTERVAL '1' DAY)\n", ") AS t(date_value)),\n", "\n", "\n", "data as (select distinct outlet_id, outlet_name, store_status, cc_code,\n", "case when lower(store_type) in ('new store', 'replacement') then 'Express Store' else store_type end as store_type, \n", "delivery_date, ob_month, city, re_handover_date, act_re_handover, actual_infra_handover_date, \n", "asset_type, erp_no, po_no, vendor, status,\n", "layout_closure_date, rfi_date\n", "from racks\n", "where lower(store_status) not in ('live', 'ib started', 'infra done', 'ob scheduled', 'ib mail initiated', 'dropped')\n", "   and (date(layout_closure_date) < current_date) )\n", "\n", "select *\n", "from(select distinct a.outlet_id, \n", "  a.cc_code,\n", "  a.outlet_name, \n", "  a.layout_closure_date as date,\n", "  date_format(date(a.rfi_date), '%d-%b-%Y') as rfi_date,\n", "  date_format(date(a.act_re_handover), '%d-%b-%Y') as act_re_handover,\n", "  date_format(date(a.layout_closure_date), '%d-%b-%Y') as layout_closure_date,\n", "  b.asset_type,\n", "  case when  b.vendor is null then 'Vendor Selection Pending'\n", "    when b.erp_no is null and b.dispatch_po is null then 'ERP Pending'\n", "    when b.po_no is null and b.dispatch_po is null then 'PO Pending'\n", "    when b.status is null then 'Status Pending'\n", "    else b.status \n", "  end as status\n", "  \n", "from data a\n", "left join racks b on a.outlet_id = b.outlet_id and b.asset_type = 'Racks'\n", "left join dates c on c.dt between date(a.layout_closure_date) and current_date\n", "where a.store_type = 'Express Store' and trim(lower(b.vendor)) not in ('not required')\n", "and (date(a.act_re_handover) > current_date or date(a.act_re_handover) is null)\n", "group by 1, 2, 3, 4, 5, 6, 7, 8, 9)\n", "where (status in ('ERP Pending', 'PO Pending', 'Vendor Selection Pending') or status is null)\n", "order by date\n", "\"\"\"\n", "\n", "query = query.replace(\"%\", \"%%\")\n", "df = pd.read_sql_query(sql=query, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "0240a7aa-1635-48d1-8d59-a336489a5bc9", "metadata": {}, "outputs": [], "source": ["df.columns = [\n", "    \"Outlet ID\",\n", "    \"CC Code\",\n", "    \"Outlet Name\",\n", "    \"date\",\n", "    \"RFI Date\",\n", "    \"Act RE HO Date\",\n", "    \"Layout Date\",\n", "    \"Asset\",\n", "    \"Status\",\n", "]\n", "\n", "if len(df) == 0:\n", "    fig = save_empty_image(\"No Order Pendency\", \"formatted_dataframe_with_header1.png\")\n", "else:\n", "    fig, ax = render_mpl_table(\n", "        df.drop(columns=[\"date\", \"Asset\"]),\n", "        header_color=\"#bed1fe\",\n", "        color_cols=[],\n", "        pad_spaces=2,\n", "        cond=1,\n", "        min_day=1,\n", "    )\n", "\n", "    fig.savefig(\"formatted_dataframe_with_header1.png\", bbox_inches=\"tight\", dpi=100)\n", "\n", "img = Image.open(\"formatted_dataframe_with_header1.png\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "267c6a07-5db6-4a9e-9196-8837e2a28803", "metadata": {}, "outputs": [], "source": ["query = \"\"\"with pms as (select zone, outlet_id, cc_code, ob_month, \n", "layout_closure_date, final_confirmation_re_ho_date,\n", "outlet_name, city, mode_of_ops, go_live_date, \n", "re_handover_date, act_re_handover, pm_name,\n", "re_handover_status, infra_start_date, \n", "racks_delivery_date, racks_start_date, racks_completion_date, \n", "electrical_vendor_deployment_date, electrical_completeion_date, \n", "cctv_start_date, cctv_completion_date, \n", "cr_delivery_date, cr_installation_start_date, cr_installation_completion_date,\n", "cold_room_rack_start, cold_room_rack_completion, \n", "loose_asset_delivery_date, servoups_delivery_date, seperators_delivery_date,\n", "expected_infra_handover_date, actual_infra_handover_date, \n", "ib_date, infra_status, regexp_replace(lower(store_status), '\\\\b([a-z])', x -> upper(x[1])) as store_status\n", "from blinkit_iceberg.infra_expansion_etls.dark_store_projects_tracker_v2\n", "where is_current = 'True'),\n", "\n", "pms_exp as (select *, date(date_trunc('month', try(date_parse(trim(ob_date_live_date), '%d-%b-%Y')))) as ob_date,\n", "date(try(date_parse(trim(ready_for_infra_date_for_asset_deployment), '%d-%b-%Y'))) as rfi_date,\n", "date(try(date_parse(trim(ds_approval_initiated), '%d-%b-%Y'))) as ds_approval_initiated1,\n", "date(try(date_parse(trim(first_payment_release_date), '%d-%b-%Y'))) as first_payment_release_date1,\n", "date(try(date_parse(trim(original_handover_date_from_ll), '%d-%b-%Y'))) as original_handover_date_from_ll1\n", "FROM ds_etls.pms_v3_master_daily_snapshot\n", "WHERE \n", "    data_updated_on = (\n", "        SELECT\n", "            max(data_updated_on)\n", "        FROM\n", "            ds_etls.pms_v3_master_daily_snapshot\n", "        WHERE\n", "            data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY\n", "    )\n", "    AND data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY\n", "    AND 1 = 1),\n", "    \n", "\n", "racks as (select distinct a.outlet_id, a.outlet_name, a.cc_code, date_trunc('month', date(actual_infra_handover_date))  as infra_handover_month,\n", "regexp_replace(lower(store_type), '(\\b[a-z])', x -> upper(x[1])) as store_type, a.racks_delivery_date as delivery_date,\n", "date_trunc('month', ob_date) as ob_month, \n", "a.city, a.re_handover_date, a.act_re_handover, a.store_status, a.actual_infra_handover_date, 'Racks' as asset_type, \n", "nullif(trim(b.racks_erp), '') as erp_no, nullif(trim(b.racks_supplier_po), '') as po_no, nullif(trim(b.racks_supply_vendor), '') as vendor, nullif(trim(b.racks_status), '') as status,\n", "layout_closure_date, rfi_date, nullif(trim(b.dispatch_po), '') as dispatch_po\n", "from pms a\n", "left join blinkit.infra_expansion_etls.racks_tracker b on a.outlet_id = b.outlet_id and b.is_current = 'True'\n", "left join pms_exp d on d.outlet_id = cast(a.outlet_id as varchar) \n", "where (date(d.rfi_date) >= date '2025-01-01' or date(re_handover_date) >= date '2025-01-01')  and a.store_status not in ('Hold', 'Dropped')),\n", "\n", "dates as (SELECT date_value as dt\n", "FROM UNNEST(\n", "    sequence(DATE '2025-05-01', current_date, INTERVAL '1' DAY)\n", ") AS t(date_value)),\n", "\n", "data as (select distinct outlet_id, outlet_name, store_status, cc_code,\n", "case when lower(store_type) in ('new store', 'replacement') then 'Express Store' else store_type end as store_type, \n", "delivery_date, ob_month, city, re_handover_date, act_re_handover, actual_infra_handover_date, \n", "asset_type, erp_no, po_no, vendor, status,\n", "layout_closure_date, rfi_date\n", "from racks\n", "where lower(store_status) not in ('live', 'ib started', 'infra done', 'ob scheduled', 'ib mail initiated', 'dropped'))\n", "\n", "select *\n", "from(select distinct a.outlet_id, a.outlet_name, \n", "      date(a.rfi_date) as date,\n", "      date_format(date(a.rfi_date), '%d-%b-%Y') as rfi_date,\n", "      date_format(date(a.act_re_handover), '%d-%b-%Y') as act_re_handover,\n", "      date_format(date(a.layout_closure_date), '%d-%b-%Y') as layout_closure_date,\n", "      b.delivery_date,\n", "      b.asset_type,\n", "      case when  b.vendor is null then 'Vendor Selection Pending'\n", "        when b.erp_no is null and b.dispatch_po is null then 'ERP Pending'\n", "        when b.po_no is null and b.dispatch_po is null then 'PO Pending'\n", "        when b.status is null then 'Status Pending'\n", "        else b.status end as status,\n", "      count(distinct dt) as ageing\n", "\n", "    from data a\n", "    left join racks b on a.outlet_id = b.outlet_id \n", "    left join dates c on c.dt between date(a.act_re_handover) and current_date - interval '1' day\n", "    where a.store_type = 'Express Store'\n", "        and lower(b.vendor) not in ('not required')\n", "        and date(a.act_re_handover) <= current_date\n", "    group by 1, 2, 3, 4, 5, 6, 7, 8, 9)\n", "    \n", "where lower(status) not in ('wcc submitted', 'po sent to vendor', 'delivered')\n", "and (date(delivery_date)>= current_date - interval '0' day or delivery_date is null)\n", "order by 3\n", "\"\"\"\n", "\n", "query = query.replace(\"%\", \"%%\")\n", "df = pd.read_sql_query(sql=query, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "7a5f2d36-f943-4027-ae24-2a8eef837dda", "metadata": {}, "outputs": [], "source": ["df.columns = [\n", "    \"Outlet ID\",\n", "    \"Outlet Name\",\n", "    \"date\",\n", "    \"RFI Date\",\n", "    \"Act RE HO Date\",\n", "    \"Layout Date\",\n", "    \"Delivery Date\",\n", "    \"Asset\",\n", "    \"Status\",\n", "    \"Ageing\",\n", "]\n", "\n", "if len(df) == 0:\n", "    fig = save_empty_image(\"No Order Pendency\", \"formatted_dataframe_with_header2.png\")\n", "else:\n", "    fig, ax = render_mpl_table(\n", "        df.drop(columns=[\"date\", \"Asset\"]),\n", "        header_color=\"#bed1fe\",\n", "        color_cols=[6, 7],\n", "        pad_spaces=2,\n", "        cond=1,\n", "        min_day=1,\n", "    )\n", "\n", "    fig.savefig(\"formatted_dataframe_with_header2.png\", bbox_inches=\"tight\", dpi=100)\n", "\n", "img = Image.open(\"formatted_dataframe_with_header2.png\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "035d8fa3-ec62-4f5c-8947-59ed2ebf2e04", "metadata": {}, "outputs": [], "source": ["headers = [\"Racks Order Pendency\", \"Racks Delivery Pendency\"]\n", "\n", "files = [\"formatted_dataframe_with_header1.png\", \"formatted_dataframe_with_header2.png\"]\n", "\n", "combined_file = combine_images_with_headers(\n", "    files, headers=headers, output_path=\"combined_dataframe_with_headers.png\"\n", ")\n", "\n", "slack_alert_at(df, \"Racks\", dic1, ds_channel, dic1[\"niket\"], dic1[\"rahul\"], [combined_file])"]}, {"cell_type": "markdown", "id": "7c5691ac-8e6e-47dc-aef2-a57deb07ccff", "metadata": {}, "source": ["<h2> Cold Room </h2>"]}, {"cell_type": "code", "execution_count": null, "id": "d33a18c6-a261-41e7-9add-a9a6822fbad2", "metadata": {}, "outputs": [], "source": ["query = \"\"\"with pms as (select zone, outlet_id, cc_code, ob_month, \n", "layout_closure_date, final_confirmation_re_ho_date,\n", "outlet_name, city, mode_of_ops, go_live_date, \n", "re_handover_date, act_re_handover, pm_name,\n", "re_handover_status, infra_start_date, \n", "racks_delivery_date, racks_start_date, racks_completion_date, \n", "electrical_vendor_deployment_date, electrical_completeion_date, \n", "cctv_start_date, cctv_completion_date, \n", "cr_delivery_date, cr_installation_start_date, cr_installation_completion_date,\n", "cold_room_rack_start, cold_room_rack_completion, \n", "loose_asset_delivery_date, servoups_delivery_date, seperators_delivery_date,\n", "expected_infra_handover_date, actual_infra_handover_date, \n", "ib_date, infra_status, regexp_replace(lower(store_status), '\\\\b([a-z])', x -> upper(x[1])) as store_status\n", "from blinkit_iceberg.infra_expansion_etls.dark_store_projects_tracker_v2\n", "where is_current = 'True'),\n", "\n", "pms_exp as (select *, date(date_trunc('month', try(date_parse(trim(ob_date_live_date), '%d-%b-%Y')))) as ob_date,\n", "date(try(date_parse(trim(ready_for_infra_date_for_asset_deployment), '%d-%b-%Y'))) as rfi_date\n", "FROM ds_etls.pms_v3_master_daily_snapshot\n", "WHERE \n", "    data_updated_on = (\n", "        SELECT\n", "            max(data_updated_on)\n", "        FROM\n", "            ds_etls.pms_v3_master_daily_snapshot\n", "        WHERE\n", "            data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY\n", "    )\n", "    AND data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY and store_type is not null\n", "    AND 1 = 1),\n", "\n", "dates as (SELECT date_value as dt\n", "FROM UNNEST(\n", "    sequence(DATE '2025-05-01', current_date, INTERVAL '1' DAY)\n", ") AS t(date_value)),\n", "\n", "cr as (\n", "  select distinct \n", "    a.outlet_id, \n", "    a.outlet_name, \n", "    a.cc_code,\n", "    date_trunc('month', date(actual_infra_handover_date)) as infra_handover_month,\n", "    regexp_replace(lower(store_type), '(\\b[a-z])', x -> upper(x[1])) as store_type, \n", "    a.cr_delivery_date as delivery_date,\n", "    date_trunc('month', ob_date) as ob_month, \n", "    a.city, \n", "    a.re_handover_date, \n", "    a.act_re_handover, \n", "    a.store_status, \n", "    a.actual_infra_handover_date, \n", "    'Cold Room' as asset_type,\n", "    nullif(trim(b.erp_no), '') as erp_no, \n", "    nullif(trim(b.po_number), '') as po_no, \n", "    nullif(trim(b.final_vendor), '') as vendor, \n", "    nullif(trim(b.cr_status), '') as status,\n", "    layout_closure_date, rfi_date\n", "  from pms a\n", "  left join blinkit.infra_expansion_etls.cold_room_tracker b on a.outlet_id = b.outlet_id and b.is_current = 'True'\n", "  left join pms_exp d on d.outlet_id = cast(a.outlet_id as varchar)\n", "  where (date(d.rfi_date) >= date '2025-01-01' or date(re_handover_date) >= date '2025-01-01')\n", "    and a.store_status not in ('Hold', 'Dropped')),\n", "\n", "data as (select distinct outlet_id, outlet_name, store_status, cc_code,\n", "case when lower(store_type) in ('new store', 'replacement') then 'Express Store' else store_type end as store_type, \n", "delivery_date, ob_month, city, re_handover_date, act_re_handover, actual_infra_handover_date, \n", "asset_type, erp_no, po_no, vendor, status,\n", "layout_closure_date, rfi_date\n", "from cr\n", "where lower(store_status) not in ('live', 'ib started', 'infra done', 'ob scheduled', 'ib mail initiated', 'dropped')\n", "   and (date(layout_closure_date) <= current_date) )\n", "\n", "select *\n", "from(select distinct a.outlet_id, \n", "  a.cc_code,\n", "  a.outlet_name, \n", "  a.layout_closure_date as date,\n", "  date_format(date(a.rfi_date), '%d-%b-%Y') as rfi_date,\n", "  date_format(date(a.act_re_handover), '%d-%b-%Y') as act_re_handover,\n", "  date_format(date(a.layout_closure_date), '%d-%b-%Y') as layout_closure_date,\n", "  b.asset_type,\n", "  case when  b.vendor is null then 'Vendor Selection Pending'\n", "    when b.erp_no is null then 'ERP Pending'\n", "    when b.po_no is null then 'PO Pending'\n", "    when b.status is null then 'Status Pending'\n", "    else b.status \n", "  end as status\n", "  \n", "from data a\n", "left join cr b on a.outlet_id = b.outlet_id \n", "left join dates c on c.dt between date(a.layout_closure_date) and current_date\n", "where a.store_type = 'Express Store' and trim(lower(b.vendor)) not in ('not required')\n", "and (date(a.act_re_handover) > current_date or date(a.act_re_handover) is null)\n", "group by 1, 2, 3, 4, 5, 6, 7, 8, 9)\n", "where (status in ('ERP Pending', 'PO Pending', 'Vendor Selection Pending') or status is null)\n", "order by date\n", "\"\"\"\n", "\n", "query = query.replace(\"%\", \"%%\")\n", "df = pd.read_sql_query(sql=query, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "283dc901-60cf-467e-b37f-fa06dfa0283d", "metadata": {}, "outputs": [], "source": ["df.columns = [\n", "    \"Outlet ID\",\n", "    \"CC Code\",\n", "    \"Outlet Name\",\n", "    \"date\",\n", "    \"RFI Date\",\n", "    \"Act RE HO Date\",\n", "    \"Layout Date\",\n", "    \"Asset\",\n", "    \"Status\",\n", "]\n", "\n", "if len(df) == 0:\n", "    fig = save_empty_image(\"No Order Pendency\", \"formatted_dataframe_with_header1.png\")\n", "else:\n", "    fig, ax = render_mpl_table(\n", "        df.drop(columns=[\"date\", \"Asset\"]),\n", "        header_color=\"#bed1fe\",\n", "        color_cols=[],\n", "        pad_spaces=2,\n", "        cond=1,\n", "        min_day=1,\n", "    )\n", "\n", "    fig.savefig(\"formatted_dataframe_with_header1.png\", bbox_inches=\"tight\", dpi=100)\n", "\n", "img = Image.open(\"formatted_dataframe_with_header1.png\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "11689f9c-b8d9-4317-bd77-9afc22e5ebbe", "metadata": {}, "outputs": [], "source": ["query = \"\"\"with pms as (select zone, outlet_id, cc_code, ob_month, \n", "layout_closure_date, final_confirmation_re_ho_date,\n", "outlet_name, city, mode_of_ops, go_live_date, \n", "re_handover_date, act_re_handover, pm_name,\n", "re_handover_status, infra_start_date, \n", "racks_delivery_date, racks_start_date, racks_completion_date, \n", "electrical_vendor_deployment_date, electrical_completeion_date, \n", "cctv_start_date, cctv_completion_date, \n", "cr_delivery_date, cr_installation_start_date, cr_installation_completion_date,\n", "cold_room_rack_start, cold_room_rack_completion, \n", "loose_asset_delivery_date, servoups_delivery_date, seperators_delivery_date,\n", "expected_infra_handover_date, actual_infra_handover_date, \n", "ib_date, infra_status, regexp_replace(lower(store_status), '\\\\b([a-z])', x -> upper(x[1])) as store_status\n", "from blinkit_iceberg.infra_expansion_etls.dark_store_projects_tracker_v2\n", "where is_current = 'True'),\n", "\n", "pms_exp as (select *, date(date_trunc('month', try(date_parse(trim(ob_date_live_date), '%d-%b-%Y')))) as ob_date,\n", "date(try(date_parse(trim(ready_for_infra_date_for_asset_deployment), '%d-%b-%Y'))) as rfi_date\n", "FROM ds_etls.pms_v3_master_daily_snapshot\n", "WHERE \n", "    data_updated_on = (\n", "        SELECT\n", "            max(data_updated_on)\n", "        FROM\n", "            ds_etls.pms_v3_master_daily_snapshot\n", "        WHERE\n", "            data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY\n", "    )\n", "    AND data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY and store_type is not null\n", "    AND 1 = 1),\n", "\n", "dates as (SELECT date_value as dt\n", "FROM UNNEST(\n", "    sequence(DATE '2025-05-01', current_date, INTERVAL '1' DAY)\n", ") AS t(date_value)),\n", "\n", "cr as (\n", "  select distinct \n", "    a.outlet_id, \n", "    a.outlet_name, \n", "    a.cc_code,\n", "    date_trunc('month', date(actual_infra_handover_date)) as infra_handover_month,\n", "    regexp_replace(lower(store_type), '(\\b[a-z])', x -> upper(x[1])) as store_type, \n", "    a.cr_delivery_date as delivery_date,\n", "    date_trunc('month', ob_date) as ob_month, \n", "    a.city, \n", "    a.re_handover_date, \n", "    a.act_re_handover, \n", "    a.store_status, \n", "    a.actual_infra_handover_date, \n", "    'Cold Room' as asset_type,\n", "    nullif(trim(b.erp_no), '') as erp_no, \n", "    nullif(trim(b.po_number), '') as po_no, \n", "    nullif(trim(b.final_vendor), '') as vendor, \n", "    nullif(trim(b.cr_status), '') as status,\n", "    layout_closure_date, rfi_date\n", "  from pms a\n", "  left join blinkit.infra_expansion_etls.cold_room_tracker b on a.outlet_id = b.outlet_id and b.is_current = 'True'\n", "  left join pms_exp d on d.outlet_id = cast(a.outlet_id as varchar)\n", "  where (date(d.rfi_date) >= date '2025-01-01' or date(re_handover_date) >= date '2025-01-01')\n", "    and a.store_status not in ('Hold', 'Dropped')),\n", "\n", "data as (select distinct outlet_id, outlet_name, store_status, cc_code,\n", "case when lower(store_type) in ('new store', 'replacement') then 'Express Store' else store_type end as store_type, \n", "delivery_date, ob_month, city, re_handover_date, act_re_handover, actual_infra_handover_date, \n", "asset_type, erp_no, po_no, vendor, status,\n", "layout_closure_date, rfi_date\n", "from cr\n", "where lower(store_status) not in ('live', 'ib started', 'infra done', 'ob scheduled', 'ib mail initiated', 'dropped')\n", "   and (date(layout_closure_date) <= current_date) )\n", "\n", "\n", "select *\n", "from(select distinct a.outlet_id, a.outlet_name, \n", "      date(a.rfi_date) as date,\n", "      date_format(date(a.rfi_date), '%d-%b-%Y') as rfi_date,\n", "      date_format(date(a.act_re_handover), '%d-%b-%Y') as act_re_handover,\n", "      date_format(date(a.layout_closure_date), '%d-%b-%Y') as layout_closure_date,\n", "      b.delivery_date,\n", "      b.asset_type,\n", "      case when  b.vendor is null then 'Vendor Selection Pending'\n", "        when b.erp_no is null then 'ERP Pending'\n", "        when b.po_no is null then 'PO Pending'\n", "        when b.status is null then 'Status Pending'\n", "        else b.status end as status,\n", "      count(distinct dt) as ageing\n", "\n", "    from data a\n", "    left join cr b on a.outlet_id = b.outlet_id \n", "    left join dates c on c.dt between date(a.act_re_handover) and current_date - interval '1' day\n", "    where a.store_type = 'Express Store'\n", "        and date(a.act_re_handover) <= current_date \n", "        and lower(b.vendor) not in ('not required')\n", "    group by 1, 2, 3, 4, 5, 6, 7, 8, 9)\n", "    \n", "where lower(status) not in ('wcc submitted', 'po sent to vendor', 'delivered')\n", "and (date(delivery_date) >= current_date - interval '0' day or delivery_date is null)\n", "order by 3\n", "\"\"\"\n", "\n", "query = query.replace(\"%\", \"%%\")\n", "df = pd.read_sql_query(sql=query, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "610fe675-f9c4-4c8c-941e-27e480f4c27a", "metadata": {}, "outputs": [], "source": ["df.columns = [\n", "    \"Outlet ID\",\n", "    \"Outlet Name\",\n", "    \"date\",\n", "    \"RFI Date\",\n", "    \"Act RE HO Date\",\n", "    \"Layout Date\",\n", "    \"Delivery Date\",\n", "    \"Asset\",\n", "    \"Status\",\n", "    \"Ageing\",\n", "]\n", "\n", "if len(df) == 0:\n", "    fig = save_empty_image(\"No Order Pendency\", \"formatted_dataframe_with_header2.png\")\n", "else:\n", "    fig, ax = render_mpl_table(\n", "        df.drop(columns=[\"date\", \"Asset\"]),\n", "        header_color=\"#bed1fe\",\n", "        color_cols=[6, 7],\n", "        pad_spaces=2,\n", "        cond=1,\n", "        min_day=1,\n", "    )\n", "\n", "    fig.savefig(\"formatted_dataframe_with_header2.png\", bbox_inches=\"tight\", dpi=100)\n", "\n", "img = Image.open(\"formatted_dataframe_with_header2.png\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "35f72320-f030-4afa-a7ee-197b52a1a715", "metadata": {}, "outputs": [], "source": ["headers = [\"Cold Room Order Pendency\", \"Cold Room Delivery Pendency\"]\n", "\n", "files = [\"formatted_dataframe_with_header1.png\", \"formatted_dataframe_with_header2.png\"]\n", "\n", "\n", "combined_file = combine_images_with_headers(\n", "    files, headers=headers, output_path=\"combined_dataframe_with_headers.png\"\n", ")\n", "\n", "slack_alert_at(df, \"Cold Room\", dic1, ds_channel, dic1[\"niket\"], dic1[\"rahul\"], [combined_file])"]}, {"cell_type": "markdown", "id": "e17de1c5-33be-4a13-929d-a913030b45fe", "metadata": {}, "source": ["<h2> Electrical </h2>"]}, {"cell_type": "code", "execution_count": null, "id": "62957f5f-0afc-4e93-bede-660f9afef453", "metadata": {}, "outputs": [], "source": ["query = \"\"\"with pms as (select zone, outlet_id, cc_code, ob_month, \n", "layout_closure_date, final_confirmation_re_ho_date,\n", "outlet_name, city, mode_of_ops, go_live_date, \n", "re_handover_date, act_re_handover, pm_name,\n", "re_handover_status, infra_start_date, \n", "racks_delivery_date, racks_start_date, racks_completion_date, \n", "electrical_vendor_deployment_date, electrical_completeion_date, \n", "cctv_start_date, cctv_completion_date, \n", "cr_delivery_date, cr_installation_start_date, cr_installation_completion_date,\n", "cold_room_rack_start, cold_room_rack_completion, \n", "loose_asset_delivery_date, servoups_delivery_date, seperators_delivery_date,\n", "expected_infra_handover_date, actual_infra_handover_date, \n", "ib_date, infra_status, regexp_replace(lower(store_status), '\\\\b([a-z])', x -> upper(x[1])) as store_status\n", "from blinkit_iceberg.infra_expansion_etls.dark_store_projects_tracker_v2\n", "where is_current = 'True'),\n", "\n", "pms_exp as (select *, date(date_trunc('month', try(date_parse(trim(ob_date_live_date), '%d-%b-%Y')))) as ob_date,\n", "date(try(date_parse(trim(ready_for_infra_date_for_asset_deployment), '%d-%b-%Y'))) as rfi_date\n", "FROM ds_etls.pms_v3_master_daily_snapshot\n", "WHERE \n", "    data_updated_on = (\n", "        SELECT\n", "            max(data_updated_on)\n", "        FROM\n", "            ds_etls.pms_v3_master_daily_snapshot\n", "        WHERE\n", "            data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY\n", "    )\n", "    AND data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY and store_type is not null\n", "    AND 1 = 1),\n", "\n", "dates as (SELECT date_value as dt\n", "FROM UNNEST(\n", "    sequence(DATE '2025-05-01', current_date, INTERVAL '1' DAY)\n", ") AS t(date_value)),\n", "\n", "electrical as (\n", "  select distinct \n", "    a.outlet_id, \n", "    a.outlet_name, \n", "    a.cc_code,\n", "    date_trunc('month', date(actual_infra_handover_date)) as infra_handover_month,\n", "    regexp_replace(lower(store_type), '(\\b[a-z])', x -> upper(x[1])) as store_type, \n", "    a.electrical_vendor_deployment_date as delivery_date,\n", "    date_trunc('month', ob_date) as ob_month, \n", "    a.city, \n", "    a.re_handover_date, \n", "    a.act_re_handover, \n", "    a.store_status, \n", "    a.actual_infra_handover_date, \n", "    'Electrical' as asset_type,\n", "    nullif(trim(b.erp), '') as erp_no, \n", "    nullif(trim(b.po), '') as po_no, \n", "    nullif(trim(b.vendor), '') as vendor, \n", "    nullif(trim(b.status), '') as status,\n", "    layout_closure_date, \n", "    rfi_date\n", "  from pms a\n", "  left join blinkit.infra_expansion_etls.electrical_tracker_v2 b on a.outlet_id = b.outlet_id and b.is_current = 'True'\n", "  left join pms_exp d on d.outlet_id = cast(a.outlet_id as varchar)\n", "  where \n", "    (date(d.rfi_date) >= date '2025-01-01' or date(re_handover_date) >= date '2025-01-01')\n", "    and a.store_status not in ('Hold', 'Dropped')),\n", "\n", "data as (select distinct outlet_id, outlet_name, store_status, cc_code,\n", "case when lower(store_type) in ('new store', 'replacement') then 'Express Store' else store_type end as store_type, \n", "delivery_date, ob_month, city, re_handover_date, act_re_handover, actual_infra_handover_date, \n", "asset_type, erp_no, po_no, vendor, status,\n", "layout_closure_date, rfi_date\n", "from electrical\n", "where lower(store_status) not in ('live', 'ib started', 'infra done', 'ob scheduled', 'ib mail initiated', 'dropped'))\n", "\n", "select *\n", "from(select distinct a.outlet_id, \n", "  a.cc_code,\n", "  a.outlet_name, \n", "  a.rfi_date as date,\n", "  date_format(date(a.rfi_date), '%d-%b-%Y') as rfi_date,\n", "  date_format(date(a.act_re_handover), '%d-%b-%Y') as act_re_handover,\n", "--  date_format(date(a.layout_closure_date), '%d-%b-%Y') as layout_closure_date,\n", "  b.asset_type,\n", "  case when  b.vendor is null then 'Vendor Selection Pending'\n", "    when b.erp_no is null then 'ERP Pending'\n", "    when b.po_no is null then 'PO Pending'\n", "    when b.status is null then 'Status Pending'\n", "    else b.status \n", "  end as status\n", "  \n", "from data a\n", "left join electrical b on a.outlet_id = b.outlet_id \n", "left join dates c on c.dt between date(a.rfi_date) and current_date\n", "where a.store_type = 'Express Store' and trim(lower(b.vendor)) not in ('not required')\n", "and date(a.rfi_date) is not null and a.cc_code is not null\n", "and (date(a.act_re_handover) > current_date or date(a.act_re_handover) is null)\n", "group by 1, 2, 3, 4, 5, 6, 7, 8)\n", "where status in ('ERP Pending', 'PO Pending', 'Vendor Selection Pending') or status is null\n", "order by date\n", "\"\"\"\n", "\n", "query = query.replace(\"%\", \"%%\")\n", "df = pd.read_sql_query(sql=query, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "e4ba37f7-c374-4f9e-8fe0-1a0c692465d8", "metadata": {}, "outputs": [], "source": ["df.columns = [\n", "    \"Outlet ID\",\n", "    \"CC Code\",\n", "    \"Outlet Name\",\n", "    \"date\",\n", "    \"RFI Date\",\n", "    \"Act RE HO Date\",\n", "    \"Asset\",\n", "    \"Status\",\n", "]\n", "\n", "if len(df) == 0:\n", "    fig = save_empty_image(\"No Order Pendency\", \"formatted_dataframe_with_header1.png\")\n", "else:\n", "    fig, ax = render_mpl_table(\n", "        df.drop(columns=[\"date\", \"Asset\"]),\n", "        header_color=\"#bed1fe\",\n", "        color_cols=[],\n", "        pad_spaces=2,\n", "        cond=1,\n", "        min_day=1,\n", "    )\n", "\n", "    fig.savefig(\"formatted_dataframe_with_header1.png\", bbox_inches=\"tight\", dpi=100)\n", "\n", "img = Image.open(\"formatted_dataframe_with_header1.png\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "f2f61ad8-f664-4e2f-a77c-badb334a376b", "metadata": {}, "outputs": [], "source": ["query = \"\"\"with pms as (select zone, outlet_id, cc_code, ob_month, \n", "layout_closure_date, final_confirmation_re_ho_date,\n", "outlet_name, city, mode_of_ops, go_live_date, \n", "re_handover_date, act_re_handover, pm_name,\n", "re_handover_status, infra_start_date, \n", "racks_delivery_date, racks_start_date, racks_completion_date, \n", "electrical_vendor_deployment_date, electrical_completeion_date, \n", "cctv_start_date, cctv_completion_date, \n", "cr_delivery_date, cr_installation_start_date, cr_installation_completion_date,\n", "cold_room_rack_start, cold_room_rack_completion, \n", "loose_asset_delivery_date, servoups_delivery_date, seperators_delivery_date,\n", "expected_infra_handover_date, actual_infra_handover_date, \n", "ib_date, infra_status, regexp_replace(lower(store_status), '\\\\b([a-z])', x -> upper(x[1])) as store_status\n", "from blinkit_iceberg.infra_expansion_etls.dark_store_projects_tracker_v2\n", "where is_current = 'True'),\n", "\n", "pms_exp as (select *, date(date_trunc('month', try(date_parse(trim(ob_date_live_date), '%d-%b-%Y')))) as ob_date,\n", "date(try(date_parse(trim(ready_for_infra_date_for_asset_deployment), '%d-%b-%Y'))) as rfi_date\n", "FROM ds_etls.pms_v3_master_daily_snapshot\n", "WHERE \n", "    data_updated_on = (\n", "        SELECT\n", "            max(data_updated_on)\n", "        FROM\n", "            ds_etls.pms_v3_master_daily_snapshot\n", "        WHERE\n", "            data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY\n", "    )\n", "    AND data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY and store_type is not null\n", "    AND 1 = 1),\n", "\n", "dates as (SELECT date_value as dt\n", "FROM UNNEST(\n", "    sequence(DATE '2025-05-01', current_date, INTERVAL '1' DAY)\n", ") AS t(date_value)),\n", "\n", "electrical as (\n", "  select distinct \n", "    a.outlet_id, \n", "    a.outlet_name, \n", "    a.cc_code,\n", "    date_trunc('month', date(actual_infra_handover_date)) as infra_handover_month,\n", "    regexp_replace(lower(store_type), '(\\b[a-z])', x -> upper(x[1])) as store_type, \n", "    a.electrical_vendor_deployment_date as delivery_date,\n", "    date_trunc('month', ob_date) as ob_month, \n", "    a.city, \n", "    a.re_handover_date, \n", "    a.act_re_handover, \n", "    a.store_status, \n", "    a.actual_infra_handover_date, \n", "    'Electrical' as asset_type,\n", "    nullif(trim(b.erp), '') as erp_no, \n", "    nullif(trim(b.po), '') as po_no, \n", "    nullif(trim(b.vendor), '') as vendor, \n", "    nullif(trim(b.status), '') as status,\n", "    layout_closure_date, \n", "    rfi_date\n", "  from pms a\n", "  left join blinkit.infra_expansion_etls.electrical_tracker_v2 b on a.outlet_id = b.outlet_id and b.is_current = 'True'\n", "  left join pms_exp d on d.outlet_id = cast(a.outlet_id as varchar)\n", "  where \n", "    (date(d.rfi_date) >= date '2025-01-01' or date(re_handover_date) >= date '2025-01-01')\n", "    and a.store_status not in ('Hold', 'Dropped')),\n", "\n", "data as (select distinct outlet_id, outlet_name, store_status, cc_code,\n", "case when lower(store_type) in ('new store', 'replacement') then 'Express Store' else store_type end as store_type, \n", "delivery_date, ob_month, city, re_handover_date, act_re_handover, actual_infra_handover_date, \n", "asset_type, erp_no, po_no, vendor, status,\n", "layout_closure_date, rfi_date\n", "from electrical\n", "where lower(store_status) not in ('live', 'ib started', 'infra done', 'ob scheduled', 'ib mail initiated', 'dropped'))\n", "\n", "\n", "select *\n", "from(select distinct a.outlet_id, a.outlet_name, \n", "      date(a.rfi_date) as date,\n", "      date_format(date(a.rfi_date), '%d-%b-%Y') as rfi_date,\n", "      date_format(date(a.act_re_handover), '%d-%b-%Y') as act_re_handover,\n", "      date_format(date(a.layout_closure_date), '%d-%b-%Y') as layout_closure_date,\n", "      b.delivery_date,\n", "      b.asset_type,\n", "      case when  b.vendor is null then 'Vendor Selection Pending'\n", "        when b.erp_no is null then 'ERP Pending'\n", "        when b.po_no is null then 'PO Pending'\n", "        when b.status is null then 'Status Pending'\n", "        else b.status end as status,\n", "      count(distinct dt) as ageing\n", "\n", "    from data a\n", "    left join electrical b on a.outlet_id = b.outlet_id \n", "    left join dates c on c.dt between date(a.act_re_handover) and current_date - interval '1' day\n", "    where a.store_type = 'Express Store'\n", "        and date(a.act_re_handover) <= current_date \n", "        and lower(b.vendor) not in ('not required')\n", "    group by 1, 2, 3, 4, 5, 6, 7, 8, 9)\n", "    \n", "where lower(status) not in ('wcc submitted', 'po sent to vendor', 'delivered')\n", "and (date(delivery_date)>= current_date - interval '0' day or delivery_date is null)\n", "order by 3\n", "\"\"\"\n", "\n", "query = query.replace(\"%\", \"%%\")\n", "df = pd.read_sql_query(sql=query, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "3bc4014f-e5e3-411c-8997-67a07c47ae42", "metadata": {}, "outputs": [], "source": ["df.columns = [\n", "    \"Outlet ID\",\n", "    \"Outlet Name\",\n", "    \"date\",\n", "    \"RFI Date\",\n", "    \"Act RE HO Date\",\n", "    \"Layout Date\",\n", "    \"Delivery Date\",\n", "    \"Asset\",\n", "    \"Status\",\n", "    \"Ageing\",\n", "]\n", "\n", "if len(df) == 0:\n", "    fig = save_empty_image(\"No Delivery Pendency\", \"formatted_dataframe_with_header2.png\")\n", "else:\n", "    fig, ax = render_mpl_table(\n", "        df.drop(columns=[\"date\", \"Asset\"]),\n", "        header_color=\"#bed1fe\",\n", "        color_cols=[6, 7],\n", "        pad_spaces=2,\n", "        cond=1,\n", "        min_day=1,\n", "    )\n", "\n", "    fig.savefig(\"formatted_dataframe_with_header2.png\", bbox_inches=\"tight\", dpi=100)\n", "\n", "img = Image.open(\"formatted_dataframe_with_header2.png\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "e982202e-9c2a-4146-9999-3c222044fd9c", "metadata": {}, "outputs": [], "source": ["headers = [\"Electrical Order Pendency\", \"Electrical Delivery Pendency\"]\n", "\n", "files = [\"formatted_dataframe_with_header1.png\", \"formatted_dataframe_with_header2.png\"]\n", "\n", "\n", "combined_file = combine_images_with_headers(\n", "    files, headers=headers, output_path=\"combined_dataframe_with_headers.png\"\n", ")\n", "\n", "slack_alert_at(df, \"Electrical\", dic1, ds_channel, dic1[\"niket\"], dic1[\"rahul\"], [combined_file])"]}, {"cell_type": "markdown", "id": "9e27c30b-8054-4c7e-8731-0507fdca1dc8", "metadata": {}, "source": ["<h2>CCTV</h2>"]}, {"cell_type": "code", "execution_count": null, "id": "94ba6348-4415-4d3f-8e46-8a6dfbbf041b", "metadata": {}, "outputs": [], "source": ["query = \"\"\"with pms as (select zone, outlet_id, cc_code, ob_month, \n", "layout_closure_date, final_confirmation_re_ho_date,\n", "outlet_name, city, mode_of_ops, go_live_date, \n", "re_handover_date, act_re_handover, pm_name,\n", "re_handover_status, infra_start_date, \n", "racks_delivery_date, racks_start_date, racks_completion_date, \n", "electrical_vendor_deployment_date, electrical_completeion_date, \n", "cctv_start_date, cctv_completion_date, \n", "cr_delivery_date, cr_installation_start_date, cr_installation_completion_date,\n", "cold_room_rack_start, cold_room_rack_completion, \n", "loose_asset_delivery_date, servoups_delivery_date, seperators_delivery_date,\n", "expected_infra_handover_date, actual_infra_handover_date, \n", "ib_date, infra_status, regexp_replace(lower(store_status), '\\\\b([a-z])', x -> upper(x[1])) as store_status\n", "from blinkit_iceberg.infra_expansion_etls.dark_store_projects_tracker_v2\n", "where is_current = 'True'),\n", "\n", "pms_exp as (select *, date(date_trunc('month', try(date_parse(trim(ob_date_live_date), '%d-%b-%Y')))) as ob_date,\n", "date(try(date_parse(trim(ready_for_infra_date_for_asset_deployment), '%d-%b-%Y'))) as rfi_date\n", "FROM ds_etls.pms_v3_master_daily_snapshot\n", "WHERE \n", "    data_updated_on = (\n", "        SELECT\n", "            max(data_updated_on)\n", "        FROM\n", "            ds_etls.pms_v3_master_daily_snapshot\n", "        WHERE\n", "            data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY\n", "    )\n", "    AND data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY and store_type is not null\n", "    AND 1 = 1),\n", "\n", "dates as (SELECT date_value as dt\n", "FROM UNNEST(\n", "    sequence(DATE '2025-05-01', current_date, INTERVAL '1' DAY)\n", ") AS t(date_value)),\n", "\n", "cctv as (\n", "  select distinct \n", "    a.outlet_id, \n", "    a.outlet_name, \n", "    a.cc_code,\n", "    date_trunc('month', date(actual_infra_handover_date)) as infra_handover_month,\n", "    regexp_replace(lower(store_type), '(\\b[a-z])', x -> upper(x[1])) as store_type, \n", "    a.cctv_start_date as delivery_date,\n", "    date_trunc('month', ob_date) as ob_month,  \n", "    a.city, \n", "    a.re_handover_date, \n", "    a.act_re_handover, \n", "    a.store_status, \n", "    a.actual_infra_handover_date, \n", "    'CCTV' as asset_type, \n", "    nullif(trim(b.erp_no), '') as erp_no, \n", "    nullif(trim(b.po_number), '') as po_no, \n", "    nullif(trim(b.vendor_name), '') as vendor, \n", "    nullif(trim(b.final_status), '') as status,\n", "    layout_closure_date, \n", "    rfi_date\n", "  from pms a\n", "  left join blinkit.infra_expansion_etls.cctv_tracker b on a.outlet_id = b.outlet_id and b.is_current = 'True'\n", "  left join pms_exp d on d.outlet_id = cast(a.outlet_id as varchar)\n", "  where \n", "    (date(d.rfi_date) >= date '2025-01-01' or date(re_handover_date) >= date '2025-01-01')  \n", "    and a.store_status not in ('Hold', 'Dropped')),\n", "\n", "data as (select distinct outlet_id, outlet_name, store_status, cc_code,\n", "case when lower(store_type) in ('new store', 'replacement') then 'Express Store' else store_type end as store_type, \n", "delivery_date, ob_month, city, re_handover_date, act_re_handover, actual_infra_handover_date, \n", "asset_type, erp_no, po_no, vendor, status,\n", "layout_closure_date, rfi_date\n", "from cctv\n", "where lower(store_status) not in ('live', 'ib started', 'infra done', 'ob scheduled', 'ib mail initiated', 'dropped'))\n", "\n", "select *\n", "from(select distinct a.outlet_id, \n", "  a.cc_code,\n", "  a.outlet_name, \n", "  a.rfi_date as date,\n", "  date_format(date(a.rfi_date), '%d-%b-%Y') as rfi_date,\n", "  date_format(date(a.act_re_handover), '%d-%b-%Y') as act_re_handover,\n", "--  date_format(date(a.layout_closure_date), '%d-%b-%Y') as layout_closure_date,\n", "  b.asset_type,\n", "  case when  b.vendor is null then 'Vendor Selection Pending'\n", "    when b.erp_no is null then 'ERP Pending'\n", "    when b.po_no is null then 'PO Pending'\n", "    when b.status is null then 'Status Pending'\n", "    else b.status \n", "  end as status\n", "  \n", "from data a\n", "left join cctv b on a.outlet_id = b.outlet_id \n", "left join dates c on c.dt between date(a.rfi_date) and current_date\n", "where a.store_type = 'Express Store' and trim(lower(b.vendor)) not in ('not required')\n", "and date(a.rfi_date) is not null and a.cc_code is not null\n", "and (date(a.act_re_handover) > current_date or date(a.act_re_handover) is null)\n", "group by 1, 2, 3, 4, 5, 6, 7, 8)\n", "where status in ('ERP Pending', 'PO Pending', 'Vendor Selection Pending') or status is null\n", "order by date\n", "\"\"\"\n", "\n", "query = query.replace(\"%\", \"%%\")\n", "df = pd.read_sql_query(sql=query, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "dc41878d-c644-4ffa-9d2b-4583d2655705", "metadata": {}, "outputs": [], "source": ["df.columns = [\n", "    \"Outlet ID\",\n", "    \"CC Code\",\n", "    \"Outlet Name\",\n", "    \"date\",\n", "    \"RFI Date\",\n", "    \"Act RE HO Date\",\n", "    \"Asset\",\n", "    \"Status\",\n", "]\n", "\n", "if len(df) == 0:\n", "    fig = save_empty_image(\"No Order Pendency\", \"formatted_dataframe_with_header1.png\")\n", "else:\n", "    fig, ax = render_mpl_table(\n", "        df.drop(columns=[\"date\", \"Asset\"]),\n", "        header_color=\"#bed1fe\",\n", "        color_cols=[],\n", "        pad_spaces=2,\n", "        cond=1,\n", "        min_day=1,\n", "    )\n", "\n", "    fig.savefig(\"formatted_dataframe_with_header1.png\", bbox_inches=\"tight\", dpi=100)\n", "\n", "img = Image.open(\"formatted_dataframe_with_header1.png\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "43d87cbb-f5f2-405d-9b79-61290993c3c8", "metadata": {}, "outputs": [], "source": ["query = \"\"\"with pms as (select zone, outlet_id, cc_code, ob_month, \n", "layout_closure_date, final_confirmation_re_ho_date,\n", "outlet_name, city, mode_of_ops, go_live_date, \n", "re_handover_date, act_re_handover, pm_name,\n", "re_handover_status, infra_start_date, \n", "racks_delivery_date, racks_start_date, racks_completion_date, \n", "electrical_vendor_deployment_date, electrical_completeion_date, \n", "cctv_start_date, cctv_completion_date, \n", "cr_delivery_date, cr_installation_start_date, cr_installation_completion_date,\n", "cold_room_rack_start, cold_room_rack_completion, \n", "loose_asset_delivery_date, servoups_delivery_date, seperators_delivery_date,\n", "expected_infra_handover_date, actual_infra_handover_date, \n", "ib_date, infra_status, regexp_replace(lower(store_status), '\\\\b([a-z])', x -> upper(x[1])) as store_status\n", "from blinkit_iceberg.infra_expansion_etls.dark_store_projects_tracker_v2\n", "where is_current = 'True'),\n", "\n", "pms_exp as (select *, date(date_trunc('month', try(date_parse(trim(ob_date_live_date), '%d-%b-%Y')))) as ob_date,\n", "date(try(date_parse(trim(ready_for_infra_date_for_asset_deployment), '%d-%b-%Y'))) as rfi_date\n", "FROM ds_etls.pms_v3_master_daily_snapshot\n", "WHERE \n", "    data_updated_on = (\n", "        SELECT\n", "            max(data_updated_on)\n", "        FROM\n", "            ds_etls.pms_v3_master_daily_snapshot\n", "        WHERE\n", "            data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY\n", "    )\n", "    AND data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY and store_type is not null\n", "    AND 1 = 1),\n", "\n", "dates as (SELECT date_value as dt\n", "FROM UNNEST(\n", "    sequence(DATE '2025-05-01', current_date, INTERVAL '1' DAY)\n", ") AS t(date_value)),\n", "\n", "cctv as (\n", "  select distinct \n", "    a.outlet_id, \n", "    a.outlet_name, \n", "    a.cc_code,\n", "    date_trunc('month', date(actual_infra_handover_date)) as infra_handover_month,\n", "    regexp_replace(lower(store_type), '(\\b[a-z])', x -> upper(x[1])) as store_type, \n", "    a.cctv_start_date as delivery_date,\n", "    date_trunc('month', ob_date) as ob_month,  \n", "    a.city, \n", "    a.re_handover_date, \n", "    a.act_re_handover, \n", "    a.store_status, \n", "    a.actual_infra_handover_date, \n", "    'CCTV' as asset_type, \n", "    nullif(trim(b.erp_no), '') as erp_no, \n", "    nullif(trim(b.po_number), '') as po_no, \n", "    nullif(trim(b.vendor_name), '') as vendor, \n", "    nullif(trim(b.final_status), '') as status,\n", "    layout_closure_date, \n", "    rfi_date\n", "  from pms a\n", "  left join blinkit.infra_expansion_etls.cctv_tracker b on a.outlet_id = b.outlet_id and b.is_current = 'True'\n", "  left join pms_exp d on d.outlet_id = cast(a.outlet_id as varchar)\n", "  where \n", "    (date(d.rfi_date) >= date '2025-01-01' or date(re_handover_date) >= date '2025-01-01')  \n", "    and a.store_status not in ('Hold', 'Dropped')),\n", "\n", "data as (select distinct outlet_id, outlet_name, store_status, cc_code,\n", "case when lower(store_type) in ('new store', 'replacement') then 'Express Store' else store_type end as store_type, \n", "delivery_date, ob_month, city, re_handover_date, act_re_handover, actual_infra_handover_date, \n", "asset_type, erp_no, po_no, vendor, status,\n", "layout_closure_date, rfi_date\n", "from cctv\n", "where lower(store_status) not in ('live', 'ib started', 'infra done', 'ob scheduled', 'ib mail initiated', 'dropped'))\n", "\n", "\n", "select *\n", "from(select distinct a.outlet_id, a.outlet_name, \n", "      date(a.rfi_date) as date,\n", "      date_format(date(a.rfi_date), '%d-%b-%Y') as rfi_date,\n", "      date_format(date(a.act_re_handover), '%d-%b-%Y') as act_re_handover,\n", "      date_format(date(a.layout_closure_date), '%d-%b-%Y') as layout_closure_date,\n", "      b.delivery_date,\n", "      b.asset_type,\n", "      case when  b.vendor is null then 'Vendor Selection Pending'\n", "        when b.erp_no is null then 'ERP Pending'\n", "        when b.po_no is null then 'PO Pending'\n", "        when b.status is null then 'Status Pending'\n", "        else b.status end as status,\n", "      count(distinct dt) as ageing\n", "\n", "    from data a\n", "    left join cctv b on a.outlet_id = b.outlet_id \n", "    left join dates c on c.dt between date(a.act_re_handover) and current_date - interval '3' day\n", "    where a.store_type = 'Express Store'\n", "        and date(a.act_re_handover) <= current_date \n", "        and lower(b.vendor) not in ('not required')\n", "    group by 1, 2, 3, 4, 5, 6, 7, 8, 9)\n", "    \n", "where lower(status) not in ('wcc submitted', 'po sent to vendor', 'delivered')\n", "and (date(delivery_date) >= current_date - interval '0' day or delivery_date is null)\n", "and ageing > 0\n", "order by 3\n", "\"\"\"\n", "\n", "query = query.replace(\"%\", \"%%\")\n", "df = pd.read_sql_query(sql=query, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "d83fc9bf-9b73-4d90-9253-c3355c70f1c6", "metadata": {}, "outputs": [], "source": ["df.columns = [\n", "    \"Outlet ID\",\n", "    \"Outlet Name\",\n", "    \"date\",\n", "    \"RFI Date\",\n", "    \"Act RE HO Date\",\n", "    \"Layout Date\",\n", "    \"Delivery Date\",\n", "    \"Asset\",\n", "    \"Status\",\n", "    \"Ageing\",\n", "]\n", "\n", "if len(df) == 0:\n", "    fig = save_empty_image(\"No Order Pendency\", \"formatted_dataframe_with_header2.png\")\n", "else:\n", "    fig, ax = render_mpl_table(\n", "        df.drop(columns=[\"date\", \"Asset\"]),\n", "        header_color=\"#bed1fe\",\n", "        color_cols=[6, 7],\n", "        pad_spaces=2,\n", "        cond=1,\n", "        min_day=1,\n", "    )\n", "\n", "    fig.savefig(\"formatted_dataframe_with_header2.png\", bbox_inches=\"tight\", dpi=100)\n", "\n", "img = Image.open(\"formatted_dataframe_with_header2.png\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "759b8c73-afec-47a3-ac23-f09e5ecb9e44", "metadata": {}, "outputs": [], "source": ["headers = [\"CCTV Order Pendency\", \"CCTV Delivery Pendency\"]\n", "\n", "files = [\"formatted_dataframe_with_header1.png\", \"formatted_dataframe_with_header2.png\"]\n", "\n", "\n", "combined_file = combine_images_with_headers(\n", "    files, headers=headers, output_path=\"combined_dataframe_with_headers.png\"\n", ")\n", "\n", "slack_alert_at(df, \"CCTV\", dic1, ds_channel, dic1[\"niket\"], dic1[\"rahul\"], [combined_file])"]}, {"cell_type": "markdown", "id": "9fbc5a37-09fa-42ec-b7bf-8ef2216fef24", "metadata": {}, "source": ["<h2> <PERSON><PERSON></h2>"]}, {"cell_type": "code", "execution_count": null, "id": "28b69c1c-a48b-48e1-8d0f-27ca7d2575b2", "metadata": {}, "outputs": [], "source": ["query = \"\"\"with pms as (select zone, outlet_id, cc_code, ob_month, \n", "layout_closure_date, final_confirmation_re_ho_date,\n", "outlet_name, city, mode_of_ops, go_live_date, \n", "re_handover_date, act_re_handover, pm_name,\n", "re_handover_status, infra_start_date, \n", "racks_delivery_date, racks_start_date, racks_completion_date, \n", "electrical_vendor_deployment_date, electrical_completeion_date, \n", "cctv_start_date, cctv_completion_date, \n", "cr_delivery_date, cr_installation_start_date, cr_installation_completion_date,\n", "cold_room_rack_start, cold_room_rack_completion, \n", "loose_asset_delivery_date, servoups_delivery_date, seperators_delivery_date,\n", "expected_infra_handover_date, actual_infra_handover_date, \n", "ib_date, infra_status, regexp_replace(lower(store_status), '\\\\b([a-z])', x -> upper(x[1])) as store_status\n", "from blinkit_iceberg.infra_expansion_etls.dark_store_projects_tracker_v2\n", "where is_current = 'True'),\n", "\n", "pms_exp as (select *, date(date_trunc('month', try(date_parse(trim(ob_date_live_date), '%d-%b-%Y')))) as ob_date,\n", "date(try(date_parse(trim(ready_for_infra_date_for_asset_deployment), '%d-%b-%Y'))) as rfi_date\n", "FROM ds_etls.pms_v3_master_daily_snapshot\n", "WHERE \n", "    data_updated_on = (\n", "        SELECT\n", "            max(data_updated_on)\n", "        FROM\n", "            ds_etls.pms_v3_master_daily_snapshot\n", "        WHERE\n", "            data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY\n", "    )\n", "    AND data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY and store_type is not null\n", "    AND 1 = 1),\n", "\n", "dates as (SELECT date_value as dt\n", "FROM UNNEST(\n", "    sequence(DATE '2025-05-01', current_date, INTERVAL '1' DAY)\n", ") AS t(date_value)),\n", "\n", "la as (\n", "  select distinct \n", "    a.outlet_id, \n", "    a.outlet_name, \n", "    a.cc_code,\n", "    date_trunc('month', date(actual_infra_handover_date)) as infra_handover_month,\n", "    regexp_replace(lower(store_type), '(\\b[a-z])', x -> upper(x[1])) as store_type, \n", "    loose_asset_delivery_date as delivery_date,\n", "    date_trunc('month', ob_date) as ob_month,  \n", "    a.city, \n", "    a.re_handover_date, \n", "    a.act_re_handover, \n", "    a.store_status, \n", "    a.actual_infra_handover_date, \n", "    'Loose Asset' as asset_type, \n", "    nullif(trim(b.erp_1), '') as erp_no, \n", "    nullif(trim(b.po_1), '') as po_no, \n", "    nullif(trim(b.la_vendor_name), '') as vendor, \n", "    nullif(trim(b.final_status), '') as status,\n", "    layout_closure_date, \n", "    rfi_date\n", "  from pms a\n", "  left join blinkit.infra_expansion_etls.loose_asset_tracker b on a.outlet_id = b.outlet_id and b.is_current = 'True'\n", "  left join pms_exp d on d.outlet_id = cast(a.outlet_id as varchar)\n", "  where \n", "    (date(d.rfi_date) >= date '2025-01-01' or date(re_handover_date) >= date '2025-01-01')  \n", "    and a.store_status not in ('Hold', 'Dropped')),\n", "\n", "data as (select distinct outlet_id, outlet_name, store_status, cc_code,\n", "case when lower(store_type) in ('new store', 'replacement') then 'Express Store' else store_type end as store_type, \n", "delivery_date, ob_month, city, re_handover_date, act_re_handover, actual_infra_handover_date, \n", "asset_type, erp_no, po_no, vendor, status,\n", "layout_closure_date, rfi_date\n", "from la\n", "where lower(store_status) not in ('live', 'ib started', 'infra done', 'ob scheduled', 'ib mail initiated', 'dropped'))\n", "\n", "select *\n", "from(select distinct a.outlet_id, \n", "  a.cc_code,\n", "  a.outlet_name, \n", "  a.rfi_date as date,\n", "  date_format(date(a.rfi_date), '%d-%b-%Y') as rfi_date,\n", "  date_format(date(a.act_re_handover), '%d-%b-%Y') as act_re_handover,\n", "--  date_format(date(a.layout_closure_date), '%d-%b-%Y') as layout_closure_date,\n", "  b.asset_type,\n", "  case when  b.vendor is null then 'Vendor Selection Pending'\n", "    when b.erp_no is null then 'ERP Pending'\n", "    when b.po_no is null then 'PO Pending'\n", "    when b.status is null then 'Status Pending'\n", "    else b.status \n", "  end as status\n", "  \n", "from data a\n", "left join la b on a.outlet_id = b.outlet_id \n", "left join dates c on c.dt between date(a.rfi_date) and current_date\n", "where a.store_type = 'Express Store' and trim(lower(b.vendor)) not in ('not required')\n", "and date(a.rfi_date) is not null and a.cc_code is not null\n", "and (date(a.act_re_handover) > current_date or date(a.act_re_handover) is null)\n", "group by 1, 2, 3, 4, 5, 6, 7, 8)\n", "where status in ('ERP Pending', 'PO Pending', 'Vendor Selection Pending') or status is null\n", "order by date\n", "\"\"\"\n", "\n", "query = query.replace(\"%\", \"%%\")\n", "df = pd.read_sql_query(sql=query, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "0ecf6d47-cf48-439b-bfe9-3a9c72d5ada3", "metadata": {}, "outputs": [], "source": ["df.columns = [\n", "    \"Outlet ID\",\n", "    \"CC Code\",\n", "    \"Outlet Name\",\n", "    \"date\",\n", "    \"RFI Date\",\n", "    \"Act RE HO Date\",\n", "    \"Asset\",\n", "    \"Status\",\n", "]\n", "\n", "if len(df) == 0:\n", "    fig = save_empty_image(\"No Order Pendency\", \"formatted_dataframe_with_header1.png\")\n", "else:\n", "    fig, ax = render_mpl_table(\n", "        df.drop(columns=[\"date\", \"Asset\"]),\n", "        header_color=\"#bed1fe\",\n", "        color_cols=[],\n", "        pad_spaces=2,\n", "        cond=1,\n", "        min_day=1,\n", "    )\n", "\n", "    fig.savefig(\"formatted_dataframe_with_header1.png\", bbox_inches=\"tight\", dpi=100)\n", "\n", "img = Image.open(\"formatted_dataframe_with_header1.png\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "630537ac-6330-4148-a175-69d2b8284f71", "metadata": {}, "outputs": [], "source": ["query = \"\"\"with pms as (select zone, outlet_id, cc_code, ob_month, \n", "layout_closure_date, final_confirmation_re_ho_date,\n", "outlet_name, city, mode_of_ops, go_live_date, \n", "re_handover_date, act_re_handover, pm_name,\n", "re_handover_status, infra_start_date, \n", "racks_delivery_date, racks_start_date, racks_completion_date, \n", "electrical_vendor_deployment_date, electrical_completeion_date, \n", "cctv_start_date, cctv_completion_date, \n", "cr_delivery_date, cr_installation_start_date, cr_installation_completion_date,\n", "cold_room_rack_start, cold_room_rack_completion, \n", "loose_asset_delivery_date, servoups_delivery_date, seperators_delivery_date,\n", "expected_infra_handover_date, actual_infra_handover_date, \n", "ib_date, infra_status, regexp_replace(lower(store_status), '\\\\b([a-z])', x -> upper(x[1])) as store_status\n", "from blinkit_iceberg.infra_expansion_etls.dark_store_projects_tracker_v2\n", "where is_current = 'True'),\n", "\n", "pms_exp as (select *, date(date_trunc('month', try(date_parse(trim(ob_date_live_date), '%d-%b-%Y')))) as ob_date,\n", "date(try(date_parse(trim(ready_for_infra_date_for_asset_deployment), '%d-%b-%Y'))) as rfi_date\n", "FROM ds_etls.pms_v3_master_daily_snapshot\n", "WHERE \n", "    data_updated_on = (\n", "        SELECT\n", "            max(data_updated_on)\n", "        FROM\n", "            ds_etls.pms_v3_master_daily_snapshot\n", "        WHERE\n", "            data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY\n", "    )\n", "    AND data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY and store_type is not null\n", "    AND 1 = 1),\n", "\n", "dates as (SELECT date_value as dt\n", "FROM UNNEST(\n", "    sequence(DATE '2025-05-01', current_date, INTERVAL '1' DAY)\n", ") AS t(date_value)),\n", "\n", "la as (\n", "  select distinct \n", "    a.outlet_id, \n", "    a.outlet_name, \n", "    a.cc_code,\n", "    date_trunc('month', date(actual_infra_handover_date)) as infra_handover_month,\n", "    regexp_replace(lower(store_type), '(\\b[a-z])', x -> upper(x[1])) as store_type, \n", "    loose_asset_delivery_date as delivery_date,\n", "    date_trunc('month', ob_date) as ob_month,  \n", "    a.city, \n", "    a.re_handover_date, \n", "    a.act_re_handover, \n", "    a.store_status, \n", "    a.actual_infra_handover_date, \n", "    'Loose Asset' as asset_type, \n", "    nullif(trim(b.erp_1), '') as erp_no, \n", "    nullif(trim(b.po_1), '') as po_no, \n", "    nullif(trim(b.la_vendor_name), '') as vendor, \n", "    nullif(trim(b.final_status), '') as status,\n", "    layout_closure_date, \n", "    rfi_date\n", "  from pms a\n", "  left join blinkit.infra_expansion_etls.loose_asset_tracker b on a.outlet_id = b.outlet_id and b.is_current = 'True'\n", "  left join pms_exp d on d.outlet_id = cast(a.outlet_id as varchar)\n", "  where \n", "    (date(d.rfi_date) >= date '2025-01-01' or date(re_handover_date) >= date '2025-01-01')  \n", "    and a.store_status not in ('Hold', 'Dropped')),\n", "\n", "data as (select distinct outlet_id, outlet_name, store_status, cc_code,\n", "case when lower(store_type) in ('new store', 'replacement') then 'Express Store' else store_type end as store_type, \n", "delivery_date, ob_month, city, re_handover_date, act_re_handover, actual_infra_handover_date, \n", "asset_type, erp_no, po_no, vendor, status,\n", "layout_closure_date, rfi_date\n", "from la\n", "where lower(store_status) not in ('live', 'ib started', 'infra done', 'ob scheduled', 'ib mail initiated', 'dropped'))\n", "\n", "select *\n", "from(select distinct a.outlet_id, a.outlet_name, \n", "      date(a.rfi_date) as date,\n", "      date_format(date(a.rfi_date), '%d-%b-%Y') as rfi_date,\n", "      date_format(date(a.act_re_handover), '%d-%b-%Y') as act_re_handover,\n", "      date_format(date(a.layout_closure_date), '%d-%b-%Y') as layout_closure_date,\n", "      b.delivery_date,\n", "      b.asset_type,\n", "      case when  b.vendor is null then 'Vendor Selection Pending'\n", "        when b.erp_no is null then 'ERP Pending'\n", "        when b.po_no is null then 'PO Pending'\n", "        when b.status is null then 'Status Pending'\n", "        else b.status end as status,\n", "      count(distinct dt) as ageing\n", "\n", "    from data a\n", "    left join la b on a.outlet_id = b.outlet_id \n", "    left join dates c on c.dt between date(a.act_re_handover) and current_date - interval '7' day\n", "    where a.store_type = 'Express Store'\n", "        and date(a.act_re_handover) <= current_date \n", "        and lower(b.vendor) not in ('not required')\n", "    group by 1, 2, 3, 4, 5, 6, 7, 8, 9)\n", "    \n", "where lower(status) not in ('wcc submitted', 'po sent to vendor', 'delivered')\n", "and (date(delivery_date) >= current_date or delivery_date is null)\n", "and ageing > 0\n", "order by 3\n", "\"\"\"\n", "\n", "query = query.replace(\"%\", \"%%\")\n", "df = pd.read_sql_query(sql=query, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "952fe9af-6885-4acf-be0a-00cb2e880381", "metadata": {}, "outputs": [], "source": ["df.columns = [\n", "    \"Outlet ID\",\n", "    \"Outlet Name\",\n", "    \"date\",\n", "    \"RFI Date\",\n", "    \"Act RE HO Date\",\n", "    \"Layout Date\",\n", "    \"Delivery Date\",\n", "    \"Asset\",\n", "    \"Status\",\n", "    \"Ageing\",\n", "]\n", "\n", "if len(df) == 0:\n", "    fig = save_empty_image(\"No Order Pendency\", \"formatted_dataframe_with_header2.png\")\n", "else:\n", "    fig, ax = render_mpl_table(\n", "        df.drop(columns=[\"date\", \"Asset\"]),\n", "        header_color=\"#bed1fe\",\n", "        color_cols=[6, 7],\n", "        pad_spaces=2,\n", "        cond=1,\n", "        min_day=2,\n", "    )\n", "\n", "    fig.savefig(\"formatted_dataframe_with_header2.png\", bbox_inches=\"tight\", dpi=100)\n", "\n", "img = Image.open(\"formatted_dataframe_with_header2.png\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "bf07d2ac-01c5-4014-a308-1f1b6d83b164", "metadata": {}, "outputs": [], "source": ["headers = [\"Loose Asset Order Pendency\", \"Loose Asset Delivery Pendency\"]\n", "\n", "files = [\"formatted_dataframe_with_header1.png\", \"formatted_dataframe_with_header2.png\"]\n", "\n", "\n", "combined_file = combine_images_with_headers(\n", "    files, headers=headers, output_path=\"combined_dataframe_with_headers.png\"\n", ")\n", "\n", "slack_alert_at(df, \"Loose Asset\", dic1, ds_channel, dic1[\"niket\"], dic1[\"rahul\"], [combined_file])"]}, {"cell_type": "markdown", "id": "2cb9f83b-d352-4398-af78-2fd682a78df5", "metadata": {}, "source": ["<h2> Servo UPS</h2>"]}, {"cell_type": "code", "execution_count": null, "id": "994cdf20-8655-43f5-9b03-368023cc22e2", "metadata": {}, "outputs": [], "source": ["query = \"\"\"with pms as (select zone, outlet_id, cc_code, ob_month, \n", "layout_closure_date, final_confirmation_re_ho_date,\n", "outlet_name, city, mode_of_ops, go_live_date, \n", "re_handover_date, act_re_handover, pm_name,\n", "re_handover_status, infra_start_date, \n", "racks_delivery_date, racks_start_date, racks_completion_date, \n", "electrical_vendor_deployment_date, electrical_completeion_date, \n", "cctv_start_date, cctv_completion_date, \n", "cr_delivery_date, cr_installation_start_date, cr_installation_completion_date,\n", "cold_room_rack_start, cold_room_rack_completion, \n", "loose_asset_delivery_date, servoups_delivery_date, seperators_delivery_date,\n", "expected_infra_handover_date, actual_infra_handover_date, \n", "ib_date, infra_status, regexp_replace(lower(store_status), '\\\\b([a-z])', x -> upper(x[1])) as store_status\n", "from blinkit_iceberg.infra_expansion_etls.dark_store_projects_tracker_v2\n", "where is_current = 'True'),\n", "\n", "pms_exp as (select *, date(date_trunc('month', try(date_parse(trim(ob_date_live_date), '%d-%b-%Y')))) as ob_date,\n", "date(try(date_parse(trim(ready_for_infra_date_for_asset_deployment), '%d-%b-%Y'))) as rfi_date\n", "FROM ds_etls.pms_v3_master_daily_snapshot\n", "WHERE \n", "    data_updated_on = (\n", "        SELECT\n", "            max(data_updated_on)\n", "        FROM\n", "            ds_etls.pms_v3_master_daily_snapshot\n", "        WHERE\n", "            data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY\n", "    )\n", "    AND data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY and store_type is not null\n", "    AND 1 = 1),\n", "\n", "dates as (SELECT date_value as dt\n", "FROM UNNEST(\n", "    sequence(DATE '2025-05-01', current_date, INTERVAL '1' DAY)\n", ") AS t(date_value)),\n", "\n", "servo as (\n", "  select distinct \n", "    a.outlet_id, \n", "    a.outlet_name, \n", "    a.cc_code,\n", "    date_trunc('month', date(actual_infra_handover_date)) as infra_handover_month,\n", "    regexp_replace(lower(store_type), '(\\b[a-z])', x -> upper(x[1])) as store_type, \n", "    servoups_delivery_date as delivery_date,\n", "    date_trunc('month', ob_date) as ob_month, \n", "    a.city, \n", "    a.re_handover_date, \n", "    a.act_re_handover, \n", "    a.store_status, \n", "    a.actual_infra_handover_date, \n", "    'Servo' as asset_type, \n", "    nullif(trim(b.servo_erp), '') as erp_no, \n", "    nullif(trim(b.servo_po), '') as po_no, \n", "    nullif(trim(b.servo_ordered), '') as vendor, \n", "    nullif(trim(b.servo_status), '') as status,\n", "    layout_closure_date, \n", "    rfi_date\n", "  from pms a\n", "  left join blinkit.infra_expansion_etls.servo_ups_tracker_v2 b \n", "    on a.outlet_id = b.outlet_id and b.is_current = 'True'\n", "  left join pms_exp d \n", "    on d.outlet_id = cast(a.outlet_id as varchar)\n", "  where \n", "    (date(d.rfi_date) >= date '2025-01-01' or date(re_handover_date) >= date '2025-01-01')\n", "    and a.store_status not in ('Hold', 'Dropped')),\n", "\n", "ups as (\n", "  select distinct \n", "    a.outlet_id, \n", "    a.outlet_name, \n", "    a.cc_code,\n", "    date_trunc('month', date(actual_infra_handover_date)) as infra_handover_month,\n", "    regexp_replace(lower(store_type), '(\\b[a-z])', x -> upper(x[1])) as store_type, \n", "    servoups_delivery_date as delivery_date,\n", "    date_trunc('month', ob_date) as ob_month, \n", "    a.city, \n", "    a.re_handover_date, \n", "    a.act_re_handover, \n", "    a.store_status, \n", "    a.actual_infra_handover_date, \n", "    'UPS' as asset_type, \n", "    nullif(trim(b.ups_erp), '') as erp_no, \n", "    nullif(trim(b.ups_po), '') as po_no, \n", "    nullif(trim(b.ups_ordered), '') as vendor, \n", "    nullif(trim(b.ups_status), '') as status,\n", "    layout_closure_date, \n", "    rfi_date\n", "  from pms a\n", "  left join blinkit.infra_expansion_etls.servo_ups_tracker_v2 b \n", "    on a.outlet_id = b.outlet_id and b.is_current = 'True'\n", "  left join pms_exp d \n", "    on d.outlet_id = cast(a.outlet_id as varchar)\n", "  where \n", "    (date(d.rfi_date) >= date '2025-01-01' or date(re_handover_date) >= date '2025-01-01')\n", "    and a.store_status not in ('Hold', 'Dropped')),\n", "\n", "\n", "data as (select distinct outlet_id, outlet_name, store_status, cc_code,\n", "case when lower(store_type) in ('new store', 'replacement') then 'Express Store' else store_type end as store_type, \n", "delivery_date, ob_month, city, re_handover_date, act_re_handover, actual_infra_handover_date, \n", "asset_type, erp_no, po_no, vendor, status,\n", "layout_closure_date, rfi_date\n", "from servo\n", "where lower(store_status) not in ('live', 'ib started', 'infra done', 'ob scheduled', 'ib mail initiated', 'dropped'))\n", "\n", "select *\n", "from(select distinct a.outlet_id, \n", "  a.cc_code,\n", "  a.outlet_name, \n", "  a.rfi_date as date,\n", "  date_format(date(a.rfi_date), '%d-%b-%Y') as rfi_date,\n", "  date_format(date(a.act_re_handover), '%d-%b-%Y') as act_re_handover,\n", "--  date_format(date(a.layout_closure_date), '%d-%b-%Y') as layout_closure_date,\n", "  b.asset_type,\n", "  case when  b.vendor is null then 'Vendor Selection Pending'\n", "    when b.erp_no is null then 'ERP Pending'\n", "    when b.po_no is null then 'PO Pending'\n", "    when b.status is null then 'Status Pending'\n", "    else b.status \n", "  end as status\n", "  \n", "from data a\n", "left join servo b on a.outlet_id = b.outlet_id \n", "left join dates c on c.dt between date(a.rfi_date) and current_date\n", "where a.store_type = 'Express Store' and trim(lower(b.vendor)) not in ('not required')\n", "and date(a.rfi_date) is not null and a.cc_code is not null\n", "and (date(a.act_re_handover) > current_date or date(a.act_re_handover) is null)\n", "group by 1, 2, 3, 4, 5, 6, 7, 8)\n", "where status in ('ERP Pending', 'PO Pending', 'Vendor Selection Pending') or status is null\n", "order by date\n", "\"\"\"\n", "\n", "query = query.replace(\"%\", \"%%\")\n", "df = pd.read_sql_query(sql=query, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "76c274f3-adc5-4b90-9160-774dc5d083f3", "metadata": {}, "outputs": [], "source": ["df.columns = [\n", "    \"Outlet ID\",\n", "    \"CC Code\",\n", "    \"Outlet Name\",\n", "    \"date\",\n", "    \"RFI Date\",\n", "    \"Act RE HO Date\",\n", "    \"Asset\",\n", "    \"Status\",\n", "]\n", "\n", "if len(df) == 0:\n", "    fig = save_empty_image(\"No Delivery Pendency\", \"formatted_dataframe_with_header1.png\")\n", "else:\n", "    fig, ax = render_mpl_table(\n", "        df.drop(columns=[\"date\", \"Asset\"]),\n", "        header_color=\"#bed1fe\",\n", "        color_cols=[],\n", "        pad_spaces=2,\n", "        cond=1,\n", "        min_day=1,\n", "    )\n", "\n", "    fig.savefig(\"formatted_dataframe_with_header1.png\", bbox_inches=\"tight\", dpi=100)\n", "\n", "img = Image.open(\"formatted_dataframe_with_header1.png\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "82326e32-69b5-4aca-9269-ec55e84f501b", "metadata": {}, "outputs": [], "source": ["query = \"\"\"with pms as (select zone, outlet_id, cc_code, ob_month, \n", "layout_closure_date, final_confirmation_re_ho_date,\n", "outlet_name, city, mode_of_ops, go_live_date, \n", "re_handover_date, act_re_handover, pm_name,\n", "re_handover_status, infra_start_date, \n", "racks_delivery_date, racks_start_date, racks_completion_date, \n", "electrical_vendor_deployment_date, electrical_completeion_date, \n", "cctv_start_date, cctv_completion_date, \n", "cr_delivery_date, cr_installation_start_date, cr_installation_completion_date,\n", "cold_room_rack_start, cold_room_rack_completion, \n", "loose_asset_delivery_date, servoups_delivery_date, seperators_delivery_date,\n", "expected_infra_handover_date, actual_infra_handover_date, \n", "ib_date, infra_status, regexp_replace(lower(store_status), '\\\\b([a-z])', x -> upper(x[1])) as store_status\n", "from blinkit_iceberg.infra_expansion_etls.dark_store_projects_tracker_v2\n", "where is_current = 'True'),\n", "\n", "pms_exp as (select *, date(date_trunc('month', try(date_parse(trim(ob_date_live_date), '%d-%b-%Y')))) as ob_date,\n", "date(try(date_parse(trim(ready_for_infra_date_for_asset_deployment), '%d-%b-%Y'))) as rfi_date\n", "FROM ds_etls.pms_v3_master_daily_snapshot\n", "WHERE \n", "    data_updated_on = (\n", "        SELECT\n", "            max(data_updated_on)\n", "        FROM\n", "            ds_etls.pms_v3_master_daily_snapshot\n", "        WHERE\n", "            data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY\n", "    )\n", "    AND data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY and store_type is not null\n", "    AND 1 = 1),\n", "\n", "dates as (SELECT date_value as dt\n", "FROM UNNEST(\n", "    sequence(DATE '2025-05-01', current_date, INTERVAL '1' DAY)\n", ") AS t(date_value)),\n", "\n", "servo as (\n", "  select distinct \n", "    a.outlet_id, \n", "    a.outlet_name, \n", "    a.cc_code,\n", "    date_trunc('month', date(actual_infra_handover_date)) as infra_handover_month,\n", "    regexp_replace(lower(store_type), '(\\b[a-z])', x -> upper(x[1])) as store_type, \n", "    servoups_delivery_date as delivery_date,\n", "    date_trunc('month', ob_date) as ob_month, \n", "    a.city, \n", "    a.re_handover_date, \n", "    a.act_re_handover, \n", "    a.store_status, \n", "    a.actual_infra_handover_date, \n", "    'Servo' as asset_type, \n", "    nullif(trim(b.servo_erp), '') as erp_no, \n", "    nullif(trim(b.servo_po), '') as po_no, \n", "    nullif(trim(b.servo_ordered), '') as vendor, \n", "    nullif(trim(b.servo_status), '') as status,\n", "    layout_closure_date, \n", "    rfi_date\n", "  from pms a\n", "  left join blinkit.infra_expansion_etls.servo_ups_tracker_v2 b \n", "    on a.outlet_id = b.outlet_id and b.is_current = 'True'\n", "  left join pms_exp d \n", "    on d.outlet_id = cast(a.outlet_id as varchar)\n", "  where \n", "    (date(d.rfi_date) >= date '2025-01-01' or date(re_handover_date) >= date '2025-01-01')\n", "    and a.store_status not in ('Hold', 'Dropped')),\n", "\n", "ups as (\n", "  select distinct \n", "    a.outlet_id, \n", "    a.outlet_name, \n", "    a.cc_code,\n", "    date_trunc('month', date(actual_infra_handover_date)) as infra_handover_month,\n", "    regexp_replace(lower(store_type), '(\\b[a-z])', x -> upper(x[1])) as store_type, \n", "    servoups_delivery_date as delivery_date,\n", "    date_trunc('month', ob_date) as ob_month, \n", "    a.city, \n", "    a.re_handover_date, \n", "    a.act_re_handover, \n", "    a.store_status, \n", "    a.actual_infra_handover_date, \n", "    'UPS' as asset_type, \n", "    nullif(trim(b.ups_erp), '') as erp_no, \n", "    nullif(trim(b.ups_po), '') as po_no, \n", "    nullif(trim(b.ups_ordered), '') as vendor, \n", "    nullif(trim(b.ups_status), '') as status,\n", "    layout_closure_date, \n", "    rfi_date\n", "  from pms a\n", "  left join blinkit.infra_expansion_etls.servo_ups_tracker_v2 b \n", "    on a.outlet_id = b.outlet_id and b.is_current = 'True'\n", "  left join pms_exp d \n", "    on d.outlet_id = cast(a.outlet_id as varchar)\n", "  where \n", "    (date(d.rfi_date) >= date '2025-01-01' or date(re_handover_date) >= date '2025-01-01')\n", "    and a.store_status not in ('Hold', 'Dropped')),\n", "\n", "\n", "data as (select distinct outlet_id, outlet_name, store_status, cc_code,\n", "case when lower(store_type) in ('new store', 'replacement') then 'Express Store' else store_type end as store_type, \n", "delivery_date, ob_month, city, re_handover_date, act_re_handover, actual_infra_handover_date, \n", "asset_type, erp_no, po_no, vendor, status,\n", "layout_closure_date, rfi_date\n", "from servo\n", "where lower(store_status) not in ('live', 'ib started', 'infra done', 'ob scheduled', 'ib mail initiated', 'dropped'))\n", "\n", "select *\n", "from(select distinct a.outlet_id, a.outlet_name, \n", "      date(a.rfi_date) as date,\n", "      date_format(date(a.rfi_date), '%d-%b-%Y') as rfi_date,\n", "      date_format(date(a.act_re_handover), '%d-%b-%Y') as act_re_handover,\n", "      date_format(date(a.layout_closure_date), '%d-%b-%Y') as layout_closure_date,\n", "      b.delivery_date,\n", "      b.asset_type,\n", "      case when  b.vendor is null then 'Vendor Selection Pending'\n", "        when b.erp_no is null then 'ERP Pending'\n", "        when b.po_no is null then 'PO Pending'\n", "        when b.status is null then 'Status Pending'\n", "        else b.status end as status,\n", "      count(distinct dt) as ageing\n", "\n", "    from data a\n", "    left join servo b on a.outlet_id = b.outlet_id \n", "    left join dates c on c.dt between date(a.act_re_handover) and current_date - interval '4' day\n", "    where a.store_type = 'Express Store'\n", "        and date(a.act_re_handover) <= current_date \n", "        and lower(b.vendor) not in ('not required')\n", "    group by 1, 2, 3, 4, 5, 6, 7, 8, 9)\n", "    \n", "where lower(status) not in ('wcc submitted', 'po sent to vendor', 'delivered')\n", "and (date(delivery_date) >= current_date or delivery_date is null)\n", "and ageing > 0\n", "order by 3\n", "\"\"\"\n", "\n", "query = query.replace(\"%\", \"%%\")\n", "df = pd.read_sql_query(sql=query, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "4ca3539c-6887-43f6-98ea-c8c017992388", "metadata": {}, "outputs": [], "source": ["df.columns = [\n", "    \"Outlet ID\",\n", "    \"Outlet Name\",\n", "    \"date\",\n", "    \"RFI Date\",\n", "    \"Act RE HO Date\",\n", "    \"Layout Date\",\n", "    \"Delivery Date\",\n", "    \"Asset\",\n", "    \"Status\",\n", "    \"Ageing\",\n", "]\n", "\n", "if len(df) == 0:\n", "    fig = save_empty_image(\"No Delivery Pendency\", \"formatted_dataframe_with_header2.png\")\n", "else:\n", "    fig, ax = render_mpl_table(\n", "        df.drop(columns=[\"date\", \"Asset\"]),\n", "        header_color=\"#bed1fe\",\n", "        color_cols=[6, 7],\n", "        pad_spaces=2,\n", "        cond=1,\n", "        min_day=1,\n", "    )\n", "\n", "    fig.savefig(\"formatted_dataframe_with_header2.png\", bbox_inches=\"tight\", dpi=100)\n", "\n", "img = Image.open(\"formatted_dataframe_with_header2.png\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "40f85094-6a87-4029-a1d4-54a7ec97bdbb", "metadata": {}, "outputs": [], "source": ["headers = [\"Servo Order Pendency\", \"Servo Delivery Pendency\"]\n", "\n", "files = [\"formatted_dataframe_with_header1.png\", \"formatted_dataframe_with_header2.png\"]\n", "\n", "\n", "combined_file = combine_images_with_headers(\n", "    files, headers=headers, output_path=\"combined_dataframe_with_headers.png\"\n", ")\n", "\n", "slack_alert_at(df, \"Servo\", dic1, ds_channel, dic1[\"niket\"], dic1[\"rahul\"], [combined_file])"]}, {"cell_type": "markdown", "id": "c057836e-de05-429a-9eae-9e7e57223ce3", "metadata": {}, "source": ["<h2> Separators</h2>"]}, {"cell_type": "code", "execution_count": null, "id": "f2bd9f77-44bb-4b31-90e5-50faa9f9d81f", "metadata": {}, "outputs": [], "source": ["query = \"\"\"with pms as (select zone, outlet_id, cc_code, ob_month, \n", "layout_closure_date, final_confirmation_re_ho_date,\n", "outlet_name, city, mode_of_ops, go_live_date, \n", "re_handover_date, act_re_handover, pm_name,\n", "re_handover_status, infra_start_date, \n", "racks_delivery_date, racks_start_date, racks_completion_date, \n", "electrical_vendor_deployment_date, electrical_completeion_date, \n", "cctv_start_date, cctv_completion_date, \n", "cr_delivery_date, cr_installation_start_date, cr_installation_completion_date,\n", "cold_room_rack_start, cold_room_rack_completion, \n", "loose_asset_delivery_date, servoups_delivery_date, seperators_delivery_date,\n", "expected_infra_handover_date, actual_infra_handover_date, \n", "ib_date, infra_status, regexp_replace(lower(store_status), '\\\\b([a-z])', x -> upper(x[1])) as store_status\n", "from blinkit_iceberg.infra_expansion_etls.dark_store_projects_tracker_v2\n", "where is_current = 'True'),\n", "\n", "pms_exp as (select *, date(date_trunc('month', try(date_parse(trim(ob_date_live_date), '%d-%b-%Y')))) as ob_date,\n", "date(try(date_parse(trim(ready_for_infra_date_for_asset_deployment), '%d-%b-%Y'))) as rfi_date\n", "FROM ds_etls.pms_v3_master_daily_snapshot\n", "WHERE \n", "    data_updated_on = (\n", "        SELECT\n", "            max(data_updated_on)\n", "        FROM\n", "            ds_etls.pms_v3_master_daily_snapshot\n", "        WHERE\n", "            data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY\n", "    )\n", "    AND data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY and store_type is not null\n", "    AND 1 = 1),\n", "\n", "dates as (SELECT date_value as dt\n", "FROM UNNEST(\n", "    sequence(DATE '2025-05-01', current_date, INTERVAL '1' DAY)\n", ") AS t(date_value)),\n", "\n", "seperator as (\n", "  select distinct \n", "    a.outlet_id, \n", "    a.outlet_name, \n", "    a.cc_code,\n", "    date_trunc('month', date(actual_infra_handover_date)) as infra_handover_month,\n", "    regexp_replace(lower(store_type), '(\\b[a-z])', x -> upper(x[1])) as store_type, \n", "    seperators_delivery_date as delivery_date,\n", "    date_trunc('month', ob_date) as ob_month, \n", "    a.city, \n", "    a.re_handover_date, \n", "    a.act_re_handover, \n", "    a.store_status, \n", "    a.actual_infra_handover_date, \n", "    'Separator' as asset_type, \n", "    nullif(trim(b.erp_no), '') as erp_no, \n", "    nullif(trim(b.po_number), '') as po_no, \n", "    nullif(trim(b.manual<PERSON>dor), '') as vendor, \n", "    nullif(trim(b.final_status), '') as status,\n", "    layout_closure_date, \n", "    rfi_date\n", "  from pms a\n", "  left join blinkit.infra_expansion_etls.seperators_tracker b on a.outlet_id = b.outlet_id and b.is_current = 'True'\n", "  left join pms_exp d on d.outlet_id = cast(a.outlet_id as varchar)\n", "  where (date(d.rfi_date) >= date '2025-01-01' or date(re_handover_date) >= date '2025-01-01')\n", "    and a.store_status not in ('Hold', 'Dropped')),\n", "\n", "data as (select distinct outlet_id, outlet_name, store_status, cc_code,\n", "case when lower(store_type) in ('new store', 'replacement') then 'Express Store' else store_type end as store_type, \n", "delivery_date, ob_month, city, re_handover_date, act_re_handover, actual_infra_handover_date, \n", "asset_type, erp_no, po_no, vendor, status,\n", "layout_closure_date, rfi_date\n", "from seperator\n", "where lower(store_status) not in ('live', 'ib started', 'infra done', 'ob scheduled', 'ib mail initiated', 'dropped'))\n", "\n", "select *\n", "from(select distinct a.outlet_id, \n", "  a.cc_code,\n", "  a.outlet_name, \n", "  a.rfi_date as date,\n", "  date_format(date(a.rfi_date), '%d-%b-%Y') as rfi_date,\n", "  date_format(date(a.act_re_handover), '%d-%b-%Y') as act_re_handover,\n", "--  date_format(date(a.layout_closure_date), '%d-%b-%Y') as layout_closure_date,\n", "  b.asset_type,\n", "  case when  b.vendor is null then 'Vendor Selection Pending'\n", "    when b.erp_no is null then 'ERP Pending'\n", "    when b.po_no is null then 'PO Pending'\n", "    when b.status is null then 'Status Pending'\n", "    else b.status \n", "  end as status\n", "  \n", "from data a\n", "left join seperator b on a.outlet_id = b.outlet_id  \n", "left join dates c on c.dt between date(a.rfi_date) and current_date\n", "where a.store_type = 'Express Store' and trim(lower(b.vendor)) not in ('not required')\n", "and date(a.rfi_date) is not null and a.cc_code is not null\n", "and (date(a.act_re_handover) > current_date or date(a.act_re_handover) is null)\n", "group by 1, 2, 3, 4, 5, 6, 7, 8)\n", "where status in ('ERP Pending', 'PO Pending', 'Vendor Selection Pending') or status is null\n", "order by date\n", "\"\"\"\n", "\n", "query = query.replace(\"%\", \"%%\")\n", "df = pd.read_sql_query(sql=query, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "10e4cd97-976d-4975-8917-8ee5faa45a58", "metadata": {}, "outputs": [], "source": ["df.columns = [\n", "    \"Outlet ID\",\n", "    \"CC Code\",\n", "    \"Outlet Name\",\n", "    \"date\",\n", "    \"RFI Date\",\n", "    \"Act RE HO Date\",\n", "    \"Asset\",\n", "    \"Status\",\n", "]\n", "\n", "if len(df) == 0:\n", "    fig = save_empty_image(\"No Order Pendency\", \"formatted_dataframe_with_header1.png\")\n", "else:\n", "    fig, ax = render_mpl_table(\n", "        df.drop(columns=[\"date\", \"Asset\"]),\n", "        header_color=\"#bed1fe\",\n", "        color_cols=[],\n", "        pad_spaces=2,\n", "        cond=1,\n", "        min_day=1,\n", "    )\n", "\n", "    fig.savefig(\"formatted_dataframe_with_header1.png\", bbox_inches=\"tight\", dpi=100)\n", "\n", "img = Image.open(\"formatted_dataframe_with_header1.png\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "6c5f102c-ec62-4777-b2a8-2cc0ab3875a6", "metadata": {}, "outputs": [], "source": ["query = \"\"\"with pms as (select zone, outlet_id, cc_code, ob_month, \n", "layout_closure_date, final_confirmation_re_ho_date,\n", "outlet_name, city, mode_of_ops, go_live_date, \n", "re_handover_date, act_re_handover, pm_name,\n", "re_handover_status, infra_start_date, \n", "racks_delivery_date, racks_start_date, racks_completion_date, \n", "electrical_vendor_deployment_date, electrical_completeion_date, \n", "cctv_start_date, cctv_completion_date, \n", "cr_delivery_date, cr_installation_start_date, cr_installation_completion_date,\n", "cold_room_rack_start, cold_room_rack_completion, \n", "loose_asset_delivery_date, servoups_delivery_date, seperators_delivery_date,\n", "expected_infra_handover_date, actual_infra_handover_date, \n", "ib_date, infra_status, regexp_replace(lower(store_status), '\\\\b([a-z])', x -> upper(x[1])) as store_status\n", "from blinkit_iceberg.infra_expansion_etls.dark_store_projects_tracker_v2\n", "where is_current = 'True'),\n", "\n", "pms_exp as (select *, date(date_trunc('month', try(date_parse(trim(ob_date_live_date), '%d-%b-%Y')))) as ob_date,\n", "date(try(date_parse(trim(ready_for_infra_date_for_asset_deployment), '%d-%b-%Y'))) as rfi_date\n", "FROM ds_etls.pms_v3_master_daily_snapshot\n", "WHERE \n", "    data_updated_on = (\n", "        SELECT\n", "            max(data_updated_on)\n", "        FROM\n", "            ds_etls.pms_v3_master_daily_snapshot\n", "        WHERE\n", "            data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY\n", "    )\n", "    AND data_updated_on >= CURRENT_DATE - INTERVAL '3' DAY and store_type is not null\n", "    AND 1 = 1),\n", "\n", "dates as (SELECT date_value as dt\n", "FROM UNNEST(\n", "    sequence(DATE '2025-05-01', current_date, INTERVAL '1' DAY)\n", ") AS t(date_value)),\n", "\n", "seperator as (\n", "  select distinct \n", "    a.outlet_id, \n", "    a.outlet_name, \n", "    a.cc_code,\n", "    date_trunc('month', date(actual_infra_handover_date)) as infra_handover_month,\n", "    regexp_replace(lower(store_type), '(\\b[a-z])', x -> upper(x[1])) as store_type, \n", "    seperators_delivery_date as delivery_date,\n", "    date_trunc('month', ob_date) as ob_month, \n", "    a.city, \n", "    a.re_handover_date, \n", "    a.act_re_handover, \n", "    a.store_status, \n", "    a.actual_infra_handover_date, \n", "    'Separator' as asset_type, \n", "    nullif(trim(b.erp_no), '') as erp_no, \n", "    nullif(trim(b.po_number), '') as po_no, \n", "    nullif(trim(b.manual<PERSON>dor), '') as vendor, \n", "    nullif(trim(b.final_status), '') as status,\n", "    layout_closure_date, \n", "    rfi_date\n", "  from pms a\n", "  left join blinkit.infra_expansion_etls.seperators_tracker b on a.outlet_id = b.outlet_id and b.is_current = 'True'\n", "  left join pms_exp d on d.outlet_id = cast(a.outlet_id as varchar)\n", "  where (date(d.rfi_date) >= date '2025-01-01' or date(re_handover_date) >= date '2025-01-01')\n", "    and a.store_status not in ('Hold', 'Dropped')),\n", "\n", "data as (select distinct outlet_id, outlet_name, store_status, cc_code,\n", "case when lower(store_type) in ('new store', 'replacement') then 'Express Store' else store_type end as store_type, \n", "delivery_date, ob_month, city, re_handover_date, act_re_handover, actual_infra_handover_date, \n", "asset_type, erp_no, po_no, vendor, status,\n", "layout_closure_date, rfi_date\n", "from seperator\n", "where lower(store_status) not in ('live', 'ib started', 'infra done', 'ob scheduled', 'ib mail initiated', 'dropped'))\n", "\n", "select *\n", "from(select distinct a.outlet_id, a.outlet_name, \n", "      date(a.rfi_date) as date,\n", "      date_format(date(a.rfi_date), '%d-%b-%Y') as rfi_date,\n", "      date_format(date(a.act_re_handover), '%d-%b-%Y') as act_re_handover,\n", "      date_format(date(a.layout_closure_date), '%d-%b-%Y') as layout_closure_date,\n", "      b.delivery_date,\n", "      b.asset_type,\n", "      case when  b.vendor is null then 'Vendor Selection Pending'\n", "        when b.erp_no is null then 'ERP Pending'\n", "        when b.po_no is null then 'PO Pending'\n", "        when b.status is null then 'Status Pending'\n", "        else b.status end as status,\n", "      count(distinct dt) as ageing\n", "\n", "    from data a\n", "    left join seperator b on a.outlet_id = b.outlet_id \n", "    left join dates c on c.dt between date(a.act_re_handover) and current_date - interval '7' day\n", "    where a.store_type = 'Express Store'\n", "        and date(a.act_re_handover) <= current_date\n", "        and lower(b.vendor) not in ('not required')\n", "    group by 1, 2, 3, 4, 5, 6, 7, 8, 9)\n", "    \n", "where lower(status) not in ('wcc submitted', 'po sent to vendor', 'delivered')\n", "and (date(delivery_date) >= current_date - interval '0' day or delivery_date is null)\n", "and ageing > 0\n", "order by 3\n", "\"\"\"\n", "\n", "query = query.replace(\"%\", \"%%\")\n", "df = pd.read_sql_query(sql=query, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "23eeee61-3fc9-4fa6-9a5c-91a1fd217df1", "metadata": {}, "outputs": [], "source": ["df.columns = [\n", "    \"Outlet ID\",\n", "    \"Outlet Name\",\n", "    \"date\",\n", "    \"RFI Date\",\n", "    \"Act RE HO Date\",\n", "    \"Layout Date\",\n", "    \"Delivery Date\",\n", "    \"Asset\",\n", "    \"Status\",\n", "    \"Ageing\",\n", "]\n", "\n", "if len(df) == 0:\n", "    fig = save_empty_image(\"No Delivery Pendency\", \"formatted_dataframe_with_header2.png\")\n", "else:\n", "    fig, ax = render_mpl_table(\n", "        df.drop(columns=[\"date\", \"Asset\"]),\n", "        header_color=\"#bed1fe\",\n", "        color_cols=[6, 7],\n", "        pad_spaces=2,\n", "        cond=1,\n", "        min_day=3,\n", "    )\n", "\n", "    fig.savefig(\"formatted_dataframe_with_header2.png\", bbox_inches=\"tight\", dpi=100)\n", "\n", "img = Image.open(\"formatted_dataframe_with_header2.png\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "60e5f831-e013-4c9d-92bf-bcf0b1c7159b", "metadata": {}, "outputs": [], "source": ["headers = [\"Seperators Order Pendency\", \"Seperators Delivery Pendency\"]\n", "\n", "files = [\"formatted_dataframe_with_header1.png\", \"formatted_dataframe_with_header2.png\"]\n", "\n", "\n", "combined_file = combine_images_with_headers(\n", "    files, headers=headers, output_path=\"combined_dataframe_with_headers.png\"\n", ")\n", "\n", "slack_alert_at(df, \"Separator\", dic1, ds_channel, dic1[\"niket\"], dic1[\"rahul\"], [combined_file])"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}