alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: wh_order_alerts1
dag_type: etl
escalation_priority: low
execution_timeout: 200
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: infra_expansion
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U08JKUHL52B
path: infra_expansion/alerts/etl/wh_order_alerts1
paused: false
pool: infra_expansion_pool
project_name: alerts
schedule:
  end_date: '2025-09-09T00:00:00'
  interval: 0 4 * * 1-5
  start_date: '2025-06-19T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 3
