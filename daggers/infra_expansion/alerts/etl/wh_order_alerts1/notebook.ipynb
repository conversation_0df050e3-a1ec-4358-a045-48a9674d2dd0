{"cells": [{"cell_type": "code", "execution_count": null, "id": "d2f1494e-6c9b-4a79-8c2c-6b3fc5727154", "metadata": {}, "outputs": [], "source": ["!pip install plotly==5.14.1 folium==0.14.0 matplotlib==3.5.1 pandas==1.5.3 numpy==1.26.4"]}, {"cell_type": "code", "execution_count": null, "id": "fc2af47b-692a-42b8-a4fd-d5c77a21baea", "metadata": {}, "outputs": [], "source": ["!pip install Pillow"]}, {"cell_type": "code", "execution_count": null, "id": "4d761c41-4dfb-472d-9c3c-5cb5fd5e96c8", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import textwrap\n", "\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.set_option(\"display.max_rows\", 100)\n", "pd.set_option(\"display.max_columns\", 80)\n", "user_id = \"U08JKUHL52B\"\n", "channel_name = \"tables-infra-alerts\"\n", "actual_channel = \"infra-vendor-podcast\"\n", "wh_channel = \"warehouse-order-status\"\n", "ds_channel = \"stores-infra-ordering-delivery-pendency\"\n", "\n", "dic1 = {\n", "    \"Racks - Cold Room\": [\"U06FLBL5W8G\", \"U0827D2M94M\"],\n", "    \"Rest\": [\"U03RSAH5H0W\", \"U0827D2M94M\", \"U08PL9QCXUL\"],\n", "    \"Loose Asset\": [\"U08DLTF1ML2\"],\n", "    \"niket\": [\"U03RLJ28N9J\"],\n", "    \"sarah\": [\"U03RPLBU1DY\"],\n", "}\n", "\n", "\n", "def truncate_with_ellipsis(text, max_len=30):\n", "    return text if len(text) <= max_len else text[: max_len - 3] + \"...\"\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    header_color,\n", "    color_cols,\n", "    cond=0,\n", "    col_width=3.5,\n", "    row_height=0.625,\n", "    font_size=14,\n", "    row_colors=[\"w\"],\n", "    edge_color=\"#dedede\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    cell_loc=\"center\",\n", "    pad_spaces=2,\n", "    **kwargs,\n", "):\n", "    def pad_and_truncate(x, col_name):\n", "        x_str = str(x)\n", "        if col_name == data.columns[0]:  # Only truncate first column\n", "            x_str = truncate_with_ellipsis(x_str, max_len=50)\n", "        return f\"{'   ' * pad_spaces}{x_str}{'   ' * pad_spaces}\"\n", "\n", "    # Apply padding and optional truncation\n", "    padded_data = data.copy().astype(str)\n", "    for col in padded_data.columns:\n", "        padded_data[col] = padded_data[col].apply(lambda x: pad_and_truncate(x, col))\n", "\n", "    fig, ax = plt.subplots(\n", "        figsize=(\n", "            (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        )\n", "    )\n", "    ax.axis(\"off\")\n", "\n", "    mpl_table = ax.table(\n", "        cellText=padded_data.values,\n", "        bbox=bbox,\n", "        colLabels=[f\"{' ' * pad_spaces}{col}{' ' * pad_spaces}\" for col in data.columns],\n", "        cellLoc=cell_loc,\n", "        **kwargs,\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "\n", "    for (row, col), cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "\n", "        if row == 0:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            if col == 0:\n", "                cell.set_text_props(weight=\"bold\")\n", "\n", "            try:\n", "                if col not in color_cols:\n", "                    cell.set_facecolor(row_colors[row % len(row_colors)])\n", "                    continue\n", "\n", "                status = str(data.iloc[row - 1, col]).strip()\n", "\n", "                if status in (\"Not Present in Tracker\"):\n", "                    cell.set_facecolor(\"#ffece3\")\n", "                elif status in (\n", "                    \"Project Completed\",\n", "                    \"Not Required\",\n", "                    \"Order Delivered\",\n", "                    \"Req. not received yet\",\n", "                ):\n", "                    cell.set_facecolor(row_colors[row % len(row_colors)])\n", "                elif status == \"Delivery Pending\":\n", "                    cell.set_facecolor(\"#fff3cd\")  # yellow\n", "                elif \"Pending\" in status:\n", "                    cell.set_facecolor(\"#f8d7da\")  # light red\n", "                else:\n", "                    cell.set_facecolor(\"#f8d7da\")  # fallback for anything else\n", "\n", "            except Exception:\n", "                cell.set_facecolor(row_colors[row % len(row_colors)])\n", "\n", "    return fig, ax"]}, {"cell_type": "code", "execution_count": null, "id": "9ffd79e1-f6a9-4d99-9e80-978bdd89f260", "metadata": {}, "outputs": [], "source": ["def slack_alert_at(df, asset, dic1, channel, niket, sarah, files):\n", "    # Handle list inputs\n", "    niket = niket[0] if isinstance(niket, list) else niket\n", "    sarah = sarah[0] if isinstance(sarah, list) else sarah\n", "\n", "    # Get unique asset types from DataFrame\n", "    workflow_states = df[\"Asset\"].dropna().unique()\n", "\n", "    # Build mention lines\n", "    mentions = []\n", "    for state in workflow_states:\n", "        if state in dic1:\n", "            user_tags = [f\"<@{user_id}>\" for user_id in dic1[state]]\n", "            mentions.append(f\"POC: {', '.join(user_tags)}\")\n", "\n", "    mentions_text = \"\\n\".join(mentions)\n", "\n", "    # Compose message\n", "    message = f\"Order Status Pendency for - {mentions_text}\\n\" f\"cc: <@{niket}> <@{sarah}>\"\n", "\n", "    # Send Slack message with attachment\n", "    pb.send_slack_message(\n", "        channel=channel,\n", "        text=message,\n", "        files=files,\n", "    )"]}, {"cell_type": "markdown", "id": "0e4044fc-a686-4711-8f0a-d22bdc5af030", "metadata": {}, "source": ["<h3>MTS, SPRS, Mezanine, CR MTS, CR</h3>"]}, {"cell_type": "code", "execution_count": null, "id": "289fad5a-7f37-454e-a01d-8181db576219", "metadata": {}, "outputs": [], "source": ["query = \"\"\"with base as (select concat(trim(fc), trim(business_type), trim(project_type)) as uniq_id, * \n", "from infra_expansion_etls.wh_base_tracker_v2 a\n", "where is_current = 'True'),\n", "\n", "tracker1 as (select *\n", "from infra_expansion_etls.wh_racks_v2\n", "where is_current = 'True'),\n", "\n", "tracker2 as (select *\n", "from infra_expansion_etls.wh_cold_room_v2\n", "where is_current = 'True')\n", "\n", "select distinct a.fc, a.business_type, a.project_type, \n", "a.region, a.project_status, a.ordering_status, \n", "date(a.rfi_date) as date_rfi, date_format(date(a.rfi_date), '%d-%b-%Y') as rfi_date,\n", "coalesce(mts_status, 'Not Present in Tracker') as mts_status, \n", "coalesce(sprs_status, 'Not Present in Tracker') as sprs_status, \n", "coalesce(mezanine_status, 'Not Present in Tracker') as mezanine_status, \n", "coalesce(cr_mts_status, 'Not Present in Tracker') as cr_mts_status,\n", "coalesce(c.cr_status, 'Not Present in Tracker') as cr_status, \n", "'Ra<PERSON> - Cold Room' as <PERSON><PERSON>\n", "from base a\n", "left join tracker1 b on b.fc_business = a.uniq_id\n", "left join tracker2 c on c.fc_business = a.uniq_id\n", "where a.ordering_status in ('Ongoing') and a.project_status not in ('On Hold') and a.layout_status in ('Approved', 'In Progress')\n", "and date(a.rfi_date) is not null \n", "order by date_rfi\"\"\"\n", "query = query.replace(\"%\", \"%%\")\n", "df = pd.read_sql_query(sql=query, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "2c45bb0d-029d-4e7d-90b9-5d99b1d6b706", "metadata": {}, "outputs": [], "source": ["df.columns = [\n", "    \"FC\",\n", "    \"Business\",\n", "    \"Project\",\n", "    \"Region\",\n", "    \"Project Status\",\n", "    \"Order Status\",\n", "    \"date_rfi\",\n", "    \"RFI Date\",\n", "    \"MTS Status\",\n", "    \"SPRS Status\",\n", "    \"Mezanine Status\",\n", "    \"CR MTS Status\",\n", "    \"CR Status\",\n", "    \"Asset\",\n", "]\n", "\n", "fig, ax = render_mpl_table(\n", "    df[\n", "        [\n", "            \"FC\",\n", "            \"Business\",\n", "            \"Project\",\n", "            \"Region\",\n", "            \"RFI Date\",\n", "            \"MTS Status\",\n", "            \"SPRS Status\",\n", "            \"Mezanine Status\",\n", "            \"CR MTS Status\",\n", "            \"CR Status\",\n", "        ]\n", "    ],\n", "    header_color=\"#c9febe\",\n", "    color_cols=[5, 6, 7, 8, 9],\n", "    pad_spaces=2,\n", "    cond=1,\n", ")\n", "\n", "fig.savefig(\"formatted_dataframe_with_header.png\", bbox_inches=\"tight\", dpi=300)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "25e2a651-8b4b-4d5d-87bb-78a72de42002", "metadata": {}, "outputs": [], "source": ["files = [\"formatted_dataframe_with_header.png\"]\n", "\n", "slack_alert_at(df, df[\"Asset\"].unique()[0], dic1, wh_channel, dic1[\"niket\"], dic1[\"sarah\"], files)"]}, {"cell_type": "markdown", "id": "931e96d5-8746-481f-a199-c787ac5fd189", "metadata": {}, "source": ["<h3>CI Status, AMF Status, ELV Status, Servo Status, UPS Status</h3>"]}, {"cell_type": "code", "execution_count": null, "id": "94f36b8b-0a5d-4961-96bf-53837b537b08", "metadata": {}, "outputs": [], "source": ["query = \"\"\"with base as (select concat(trim(fc), trim(business_type), trim(project_type)) as uniq_id, * \n", "from infra_expansion_etls.wh_base_tracker_v2 a\n", "where is_current = 'True'),\n", "\n", "tracker1 as (select fc_business, \n", "max_by(ci_status, date_updated_ist) as ci_status, \n", "max_by(amf_status, date_updated_ist) as amf_status\n", "from infra_expansion_etls.wh_electrical_v2\n", "where is_current = 'True'\n", "group by 1),\n", "\n", "tracker2 as (select fc_business, \n", "max_by(elv_status, date_updated_ist) as elv_status\n", "from infra_expansion_etls.wh_cctv_v2\n", "where is_current = 'True'\n", "group by 1),\n", "\n", "tracker3 as (select fc_business, \n", "max_by(servo_status, date_updated_ist) as servo_status,\n", "max_by(ups_status, date_updated_ist) as ups_status\n", "from infra_expansion_etls.wh_servo_ups_v2\n", "where is_current = 'True'\n", "group by 1)\n", "\n", "select distinct trim(a.fc) as fc, trim(a.business_type) as business_type, trim(a.project_type) as project_type, \n", "region, project_status, ordering_status, date(rfi_date) as date_rfi, \n", "date_format(date(rfi_date), '%d-%b-%Y') as rfi_date,\n", "COALESCE(ci_status, 'Not Present in Tracker') AS ci_status, \n", "COALESCE(amf_status, 'Not Present in Tracker') AS amf_status, \n", "COALESCE(elv_status, 'Not Present in Tracker') AS elv_status, \n", "COALESCE(servo_status, 'Not Present in Tracker') AS servo_status, \n", "COALESCE(ups_status, 'Not Present in Tracker') AS ups_status, 'Rest' as Asset \n", "from base a\n", "left join tracker1 b on b.fc_business = a.uniq_id\n", "left join tracker2 c on c.fc_business = a.uniq_id\n", "left join tracker3 d on d.fc_business = a.uniq_id\n", "where a.ordering_status in ('Ongoing') and a.project_status not in ('On Hold') and a.layout_status in ('Approved', 'In Progress')\n", "and (date(rfi_date) is not null or ordering_status = 'Ongoing')\n", "order by date_rfi\"\"\"\n", "query = query.replace(\"%\", \"%%\")\n", "df = pd.read_sql_query(sql=query, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "256f3a2b-cb1f-424a-8521-b545b668c408", "metadata": {}, "outputs": [], "source": ["df.columns = [\n", "    \"FC\",\n", "    \"Business\",\n", "    \"Project\",\n", "    \"Region\",\n", "    \"Project Status\",\n", "    \"Order Status\",\n", "    \"date_rfi\",\n", "    \"RFI Date\",\n", "    \"CI Status\",\n", "    \"AMF Status\",\n", "    \"ELV Status\",\n", "    \"Servo Status\",\n", "    \"UPS Status\",\n", "    \"Asset\",\n", "]\n", "\n", "fig, ax = render_mpl_table(\n", "    df[\n", "        [\n", "            \"FC\",\n", "            \"Business\",\n", "            \"Project\",\n", "            \"Region\",\n", "            \"RFI Date\",\n", "            \"CI Status\",\n", "            \"AMF Status\",\n", "            \"ELV Status\",\n", "            \"Servo Status\",\n", "            \"UPS Status\",\n", "        ]\n", "    ],\n", "    header_color=\"#c9febe\",\n", "    color_cols=[5, 6, 7, 8, 9],\n", "    pad_spaces=2,\n", "    cond=1,\n", ")\n", "\n", "fig.savefig(\"formatted_dataframe_with_header.png\", bbox_inches=\"tight\", dpi=300)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "375acb7c-a18e-4617-983d-73420f75e437", "metadata": {}, "outputs": [], "source": ["files = [\"formatted_dataframe_with_header.png\"]\n", "\n", "\n", "slack_alert_at(df, df[\"Asset\"].unique()[0], dic1, wh_channel, dic1[\"niket\"], dic1[\"sarah\"], files)"]}, {"cell_type": "markdown", "id": "4e79d546-1c84-4f4d-ba0e-3a3172c72254", "metadata": {}, "source": ["<h3><PERSON><PERSON></h3>"]}, {"cell_type": "code", "execution_count": null, "id": "7fdee04b-8af0-458c-894e-281f2a311bfa", "metadata": {}, "outputs": [], "source": ["query = \"\"\"with base as (select concat(trim(fc), trim(business_type), trim(project_type)) as uniq_id, * \n", "from infra_expansion_etls.wh_base_tracker_v2 a\n", "where is_current = 'True'),\n", "\n", "tracker1 as (select *\n", "from infra_expansion_etls.wh_loose_asset_v2\n", "where is_current = 'True' and asset in ('BOPT', 'HOPT', 'BHPT', 'Spiral Converyor', 'VRC', \n", "    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Blast Freezer', 'Signages', 'PP Sheet', \n", "    'PP bins', 'Plastic Pallets (Heavy Duty)- Static Load - 5.75 MT - 6MT Dynamic - 900 - 1200 Kgs- Perforated Top')),\n", "\n", "status_help AS (SELECT 'LL Layout Pending' AS status, 1 AS prio UNION ALL\n", "    SELECT 'Layout Pending', 2 UNION ALL\n", "    SELECT 'Vendor Selection Pending', 3 UNION ALL\n", "    SELECT 'Order Pending', 4 UNION ALL\n", "    SELECT 'Cost ID Creation Pending', 5 UNION ALL\n", "    SELECT 'PO Pending', 6 UNION ALL\n", "    SELECT 'Delivery Pending', 7 UNION ALL\n", "    SELECT 'Order Delayed', 8 UNION ALL\n", "    SELECT 'Order On Time', 9 UNION ALL\n", "    SELECT 'Order Delivered', 10 UNION ALL\n", "    SELECT 'On Hold', 11 UNION ALL\n", "    SELECT 'Project Completed', 12 UNION ALL\n", "    SELECT 'Req. not received yet', 13 UNION ALL\n", "    SELECT 'Not Required', 14 UNION ALL\n", "    SELECT 'Not Present in Tracker', 15),\n", "\n", "\n", "tracker2 as (select distinct fc_business, 'Other Assets' as asset, min_by(a.status, b.prio) as status\n", "from infra_expansion_etls.wh_loose_asset_v2 a\n", "left join status_help b on a.status = b.status\n", "where is_current = 'True' \n", "and asset not in ('BOPT', 'HOPT',\n", "                  'BHPT', 'Spiral Converyor',\n", "                  'V<PERSON>', '<PERSON><PERSON><PERSON>', 'HVLS',\n", "                  'Blast Freezer', 'Signages',\n", "                  'PP sheet', 'PP bins',\n", "                  'Plastic Pallets (Heavy Duty)- Static Load - 5.75 MT - 6MT Dynamic - 900 - 1200 Kgs- Perforated Top')\n", "group by 1, 2)\n", "\n", "select distinct trim(a.fc) as fc, trim(a.business_type) as business_type, trim(a.project_type) as project_type, region,\n", "  project_status, ordering_status,\n", "  case when a.business_type like '%Frozen%' then 'Frozen'\n", "       when a.business_type like '%mini%' then 'Mini CPC'\n", "       else 'WHS New' end as clean_type,\n", "  DATE(rfi_date) AS date_rfi, DATE_FORMAT(DATE(rfi_date), '%d-%b-%Y') AS rfi_date,\n", "  coalesce(max(case when b.asset = 'BOPT' THEN b.status END), 'Not Present in Tracker') AS bopt_status,\n", "  coalesce(max(case when b.asset = 'HOPT' THEN b.status END), 'Not Present in Tracker')  AS hopt_status,\n", "  coalesce(max(case when b.asset = 'BHPT' THEN b.status END), 'Not Present in Tracker')  AS bhpt_status,\n", "  coalesce(max(case when b.asset = 'Spiral Converyor' THEN b.status END), 'Not Present in Tracker')  AS spiral_conveyor_status,\n", "  coalesce(max(case when b.asset = 'VRC' THEN b.status END), 'Not Present in Tracker')  AS vrc_status,\n", "  coalesce(max(case when b.asset = 'Stacker' THEN b.status END), 'Not Present in Tracker')  AS stacker_status,\n", "  coalesce(max(case when b.asset = 'HVLS' THEN b.status END), 'Not Present in Tracker')  AS hvls_status,\n", "  coalesce(max(case when b.asset = 'Blast Freezer' THEN b.status END), 'Not Present in Tracker')  AS blast_freezer_status,\n", "  coalesce(max(case when b.asset = 'Signages' THEN b.status END), 'Not Present in Tracker')  AS signages_status,\n", "  coalesce(max(case when b.asset = 'PP Sheet' THEN b.status END), 'Not Present in Tracker')  AS pp_sheet_status,\n", "  coalesce(max(case when b.asset = 'PP bins' THEN b.status END), 'Not Present in Tracker')  AS pp_bins_status,\n", "  coalesce(max(case when b.asset like '%Plastic Pallets%' THEN b.status END), 'Not Present in Tracker')  AS plastic_pallets_status,\n", "  coalesce(max(c.status), 'Not Present in Tracker')  AS Loose_asset_status,\n", "  'Loose Asset' AS asset\n", "from base a\n", "left join tracker1 b on b.fc_business = a.uniq_id\n", "left join tracker2 c on c.fc_business = a.uniq_id\n", "where ordering_status in ('Ongoing')\n", "  and project_status not in ('On Hold')\n", "  and layout_status in ('Approved', 'In Progress')\n", "  and (date(rfi_date) is not null or ordering_status = 'Ongoing')\n", "  and project_type not in ('Poultry Room', 'New Dairy', 'Poultry Room & Dairy room')\n", "group by 1, 2, 3, 4, 5, 6, 7, 8, 9, 23\n", "order by date_rfi\n", "\"\"\"\n", "query = query.replace(\"%\", \"%%\")\n", "df = pd.read_sql_query(sql=query, con=con)\n", "\n", "df.columns = [\n", "    \"FC\",\n", "    \"Business\",\n", "    \"Project\",\n", "    \"Region\",\n", "    \"Project Status\",\n", "    \"Order Status\",\n", "    \"Clean_type\",\n", "    \"date_rfi\",\n", "    \"RFI Date\",\n", "    \"BOPT\",\n", "    \"HOPT\",\n", "    \"BHPT\",\n", "    \"Spiral Converyor\",\n", "    \"VRC\",\n", "    \"Stacker\",\n", "    \"HVLS\",\n", "    \"Blast Freezer\",\n", "    \"Signages\",\n", "    \"PP sheet\",\n", "    \"PP bins\",\n", "    \"Plastic Pallets\",\n", "    \"Loose <PERSON>\",\n", "    \"Asset\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "dd51b356-f711-4098-a176-b108b6166f9f", "metadata": {}, "outputs": [], "source": ["fig, ax = render_mpl_table(\n", "    df.loc[df[\"Clean_type\"] == \"Frozen\"][\n", "        [\"FC\", \"Business\", \"Project\", \"Region\", \"RFI Date\", \"HOPT\", \"Stacker\", \"Loose Asset\"]\n", "    ],\n", "    header_color=\"#c9febe\",\n", "    color_cols=[5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],\n", "    pad_spaces=2,\n", "    cond=1,\n", ")\n", "\n", "fig.savefig(\"formatted_dataframe_with_header1.png\", bbox_inches=\"tight\", dpi=100)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "857f86f6-6449-4777-a7cc-701bb915fe5b", "metadata": {}, "outputs": [], "source": ["fig, ax = render_mpl_table(\n", "    df.loc[df[\"Clean_type\"] == \"WHS New\"][\n", "        [\n", "            \"FC\",\n", "            \"Business\",\n", "            \"Project\",\n", "            \"Region\",\n", "            \"RFI Date\",\n", "            \"BOPT\",\n", "            \"HOPT\",\n", "            \"BHPT\",\n", "            \"Spiral Converyor\",\n", "            \"VRC\",\n", "            \"Stacker\",\n", "            \"HVLS\",\n", "            \"Blast Freezer\",\n", "            \"Signages\",\n", "            \"Plastic Pallets\",\n", "            \"PP sheet\",\n", "            \"PP bins\",\n", "            \"Loose <PERSON>\",\n", "        ]\n", "    ],\n", "    header_color=\"#c9febe\",\n", "    color_cols=[5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17],\n", "    pad_spaces=2,\n", "    cond=1,\n", ")\n", "\n", "fig.savefig(\"formatted_dataframe_with_header2.png\", bbox_inches=\"tight\", dpi=100)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "ea2d3451-e71f-4e69-bdc2-d20a0b325200", "metadata": {}, "outputs": [], "source": ["fig, ax = render_mpl_table(\n", "    df.loc[df[\"Clean_type\"] == \"Mini CPC\"][\n", "        [\n", "            \"FC\",\n", "            \"Business\",\n", "            \"Project\",\n", "            \"Region\",\n", "            \"RFI Date\",\n", "            \"HOPT\",\n", "            \"Plastic Pallets\",\n", "            \"Loose <PERSON>\",\n", "        ]\n", "    ],\n", "    header_color=\"#c9febe\",\n", "    color_cols=[5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17],\n", "    pad_spaces=2,\n", "    cond=1,\n", ")\n", "\n", "fig.savefig(\"formatted_dataframe_with_header3.png\", bbox_inches=\"tight\", dpi=100)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "b93c5861-b420-47ba-a660-3c9d224fcd57", "metadata": {}, "outputs": [], "source": ["from PIL import Image, ImageDraw, ImageFont\n", "\n", "\n", "def create_header_image(text, width, height=60, font_size=24):\n", "    # Create blank image\n", "    header_img = Image.new(\"RGB\", (width, height), color=(246, 246, 246))  # Light blue\n", "    draw = ImageDraw.Draw(header_img)\n", "\n", "    # Try loading a large bold font\n", "    try:\n", "        font = ImageFont.truetype(\"arialbd.ttf\", font_size)  # bold Arial if available\n", "    except IOError:\n", "        try:\n", "            font = ImageFont.truetype(\"DejaVuSans-Bold.ttf\", font_size)  # common on Linux\n", "        except IOError:\n", "            font = ImageFont.load_default()\n", "\n", "    # Center text\n", "    text_width, text_height = draw.textbbox((0, 0), text, font=font)[\n", "        2:\n", "    ]  # more accurate than textsize\n", "    position = ((width - text_width) // 2, (height - text_height) // 2)\n", "    draw.text(position, text, fill=\"black\", font=font)\n", "\n", "    return header_img\n", "\n", "\n", "def combine_images_with_headers(image_paths, headers, output_path=\"combined_output.png\"):\n", "    assert len(image_paths) == len(headers), \"Each image must have a corresponding header.\"\n", "\n", "    combined_blocks = []\n", "\n", "    for path, header in zip(image_paths, headers):\n", "        table_img = Image.open(path)\n", "        header_img = create_header_image(header, table_img.width)\n", "\n", "        # Create a new block (header + table)\n", "        block_height = header_img.height + table_img.height\n", "        block_img = Image.new(\"RGB\", (table_img.width, block_height), color=(255, 255, 255))\n", "        block_img.paste(header_img, (0, 0))\n", "        block_img.paste(table_img, (0, header_img.height))\n", "\n", "        combined_blocks.append(block_img)\n", "\n", "    # Final image\n", "    total_height = sum(block.height for block in combined_blocks)\n", "    max_width = max(block.width for block in combined_blocks)\n", "    final_img = Image.new(\"RGB\", (max_width, total_height), color=(255, 255, 255))\n", "\n", "    y_offset = 0\n", "    for block in combined_blocks:\n", "        final_img.paste(block, (0, y_offset))\n", "        y_offset += block.height\n", "\n", "    final_img.save(output_path)\n", "    return output_path"]}, {"cell_type": "code", "execution_count": null, "id": "90d5157f-fef4-4d45-9f67-a15bf02c3ed9", "metadata": {}, "outputs": [], "source": ["headers = [\"New WHS\"]\n", "\n", "files = [\"formatted_dataframe_with_header2.png\"]\n", "\n", "combined_file = combine_images_with_headers(\n", "    files, headers=headers, output_path=\"combined_dataframe_with_headers.png\"\n", ")\n", "\n", "slack_alert_at(\n", "    df, df[\"Asset\"].unique()[0], dic1, wh_channel, dic1[\"niket\"], dic1[\"sarah\"], [combined_file]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7e75d759-cc77-4bcb-87c9-47a326ee3140", "metadata": {}, "outputs": [], "source": ["headers = [\"Frozen\", \"Mini CPC\"]\n", "\n", "files = [\"formatted_dataframe_with_header1.png\", \"formatted_dataframe_with_header3.png\"]\n", "\n", "combined_file = combine_images_with_headers(\n", "    files, headers=headers, output_path=\"combined_dataframe_with_headers.png\"\n", ")\n", "\n", "slack_alert_at(\n", "    df, df[\"Asset\"].unique()[0], dic1, wh_channel, dic1[\"niket\"], dic1[\"sarah\"], [combined_file]\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}