alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: erp_grn_reports
dag_type: etl
escalation_priority: low
execution_timeout: 180
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: low
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: infra_expansion
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U08JKUHL52B
path: infra_expansion/alerts/etl/erp_grn_reports
paused: false
pool: infra_expansion_pool
project_name: alerts
schedule:
  end_date: '2025-08-24T00:00:00'
  interval: 0 4 * * 1-5
  start_date: '2025-06-12T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 6
