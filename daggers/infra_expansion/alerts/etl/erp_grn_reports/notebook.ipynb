{"cells": [{"cell_type": "code", "execution_count": null, "id": "573bbe77-87fb-4715-9fc3-91d5cae1df38", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from io import BytesIO, StringIO\n", "import re\n", "import requests\n", "from urllib.parse import urlencode\n", "from datetime import datetime, timedelta\n", "from pytz import timezone\n", "import warnings\n", "\n", "import json\n", "import os\n", "import base64\n", "import io\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "\n", "def get_kwargs_from_df(df, table_name):\n", "\n", "    dtypes = pd.DataFrame(df.dtypes, columns=[\"type_\"]).reset_index().astype({\"type_\": \"str\"})\n", "    type_map = {\n", "        \"object\": \"VARCHAR\",\n", "        \"int64\": \"INTEGER\",\n", "        \"int32\": \"INTEGER\",\n", "        \"float64\": \"REAL\",\n", "        \"float32\": \"REAL\",\n", "        \"datetime64[ns]\": \"TIMESTAMP(6)\",\n", "    }\n", "\n", "    dtypes[\"type\"] = dtypes[\"type_\"].map(type_map)\n", "    columns = [\n", "        {\"name\": row[\"index\"], \"type\": row[\"type\"], \"description\": row[\"index\"]}\n", "        for _, row in dtypes.iterrows()\n", "    ]\n", "\n", "    return {\n", "        \"schema_name\": \"infra_expansion_etls\",\n", "        \"table_name\": table_name,\n", "        \"column_dtypes\": columns,\n", "        \"partition_key\": [],\n", "        \"load_type\": \"truncate\",\n", "        \"table_description\": \"Table for ERP PO owners Dump\",\n", "    }\n", "\n", "\n", "def read_and_clean_data(df, header_index, primary, cols):\n", "\n", "    if header_index != -1:\n", "        df.columns = df.iloc[header_index]\n", "        df = df.iloc[header_index + 1 :].reset_index(drop=True)\n", "\n", "    df = df.loc[:, df.columns.notna()]\n", "\n", "    df.columns = [\n", "        re.sub(r\"\\d+\", \"\", str(i))\n", "        .replace(\"%\", \"\")\n", "        .strip()\n", "        .replace(\"'\", \"\")\n", "        .replace(\" \", \"_\")\n", "        .replace(\"\\n\", \"\")\n", "        .replace(\"/\", \"\")\n", "        .replace(\"(\", \"\")\n", "        .replace(\")\", \"\")\n", "        .replace(\"?\", \"\")\n", "        .replace(\"-\", \"\")\n", "        .lower()\n", "        for i in df.columns\n", "    ]\n", "\n", "    df = df.where(pd.notnull(df), None)\n", "    df = df[cols]\n", "\n", "    return df.reset_index(drop=True)\n", "\n", "\n", "def main(sheet_id, sheet_name, header_index, cols, table_name, primary):\n", "    user_id = \"U08JKUHL52B\"\n", "    channel_name = \"tables-infra-alerts\"\n", "\n", "    try:\n", "        df = pb.from_sheets(sheet_id, sheet_name)\n", "        # df = pd.read_csv(f\"{table_name}.csv\")\n", "\n", "    except Exception as e:\n", "        pb.send_slack_message(\n", "            channel=channel_name,\n", "            text=f\"<@{user_id}> || Error occured while reading {sheet_id}\\n Exception: {e}\",\n", "        )\n", "        raise ValueError(f\"Failed to read Google Sheet {sheet_id}/{sheet_name}: {e}\")\n", "\n", "    try:\n", "        df = read_and_clean_data(df, header_index, primary, cols)\n", "        print(\"Data Cleaned\")\n", "    except Exception as e:\n", "        pb.send_slack_message(\n", "            channel=channel_name,\n", "            text=f\"<@{user_id}> || Error occured while fetching {table_name}\\n Exception: {e}\",\n", "        )\n", "        raise Exception(\"Not able to Clean data\")\n", "\n", "    final_df = df\n", "    final_df[\"date_updated_ist\"] = (\n", "        datetime.now(timezone(\"Asia/Kolkata\")) - <PERSON><PERSON><PERSON>(days=0)\n", "    ).strftime(\"%Y-%m-%d\")\n", "    final_df[\"is_active\"] = pd.to_numeric(final_df[\"is_active\"], errors=\"coerce\")\n", "\n", "    print(\"Data updated in audit format: \", final_df.shape)\n", "\n", "    try:\n", "        kwargs = get_kwargs_from_df(final_df, table_name)\n", "        pb.to_trino(data_obj=final_df, **kwargs)\n", "        print(f\"Data successfully written to Trino table infra_expansion_etls.{table_name}\")\n", "    except Exception as e:\n", "        pb.send_slack_message(\n", "            channel=channel_name,\n", "            text=f\"<@{user_id}> || Error occured while writing in {table_name}\\n Exception: {e}\",\n", "        )\n", "        print(f\"Error writing to database: {e}\")\n", "\n", "    print(f\"ERP Owners Data Ingested :|\")\n", "    return final_df"]}, {"cell_type": "code", "execution_count": null, "id": "11035760-122e-420c-beb2-4f5aa9bc2662", "metadata": {}, "outputs": [], "source": ["cols = [\"owners_mail\", \"name\", \"is_active\"]\n", "\n", "# (sheet_id, sheet_name, header_index, cols, table_name, primary)\n", "final_df = main(\n", "    \"1vjwZp5nlUdEpYcXkpWfExND0FcqhbQ-sOx4SCiqPK4o\", \"Data\", -1, cols, \"erp_owners\", \"owners_mail\"\n", ")\n", "\n", "print(\"Owners updated\")"]}, {"cell_type": "code", "execution_count": null, "id": "b0abdfb4-4627-45d9-8bbe-b840c2561732", "metadata": {}, "outputs": [], "source": ["!pip install plotly==5.14.1 folium==0.14.0 matplotlib==3.5.1 pandas==1.5.3 numpy==1.26.4"]}, {"cell_type": "code", "execution_count": null, "id": "109a2333-9fee-4b40-8da7-7b7e23375496", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import textwrap\n", "\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.set_option(\"display.max_rows\", 100)\n", "pd.set_option(\"display.max_columns\", 80)\n", "user_id = \"U08JKUHL52B\"\n", "channel_name = \"tables-infra-alerts\"\n", "actual_channel = \"infra-vendor-podcast\"\n", "erp_channel = \"erp-approval-infra\"\n", "bistro_channel = \"bistro-erp-approval\"\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    header_color,\n", "    cond=0,\n", "    col_width=3.5,\n", "    row_height=0.625,\n", "    font_size=14,\n", "    row_colors=[\"#e8e8e8\", \"w\"],\n", "    edge_color=\"#dedede\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    cell_loc=\"center\",\n", "    pad_spaces=2,\n", "    **kwargs,\n", "):\n", "    # Pad each cell's content with spaces\n", "    padded_data = (\n", "        data.copy().astype(str).applymap(lambda x: f\"{'   ' * pad_spaces}{x}{'   ' * pad_spaces}\")\n", "    )\n", "\n", "    fig, ax = plt.subplots(\n", "        figsize=(\n", "            (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        )\n", "    )\n", "    ax.axis(\"off\")\n", "\n", "    # Table creation\n", "    mpl_table = ax.table(\n", "        cellText=padded_data.values,\n", "        bbox=bbox,\n", "        colLabels=[f\"{' ' * pad_spaces}{col}{' ' * pad_spaces}\" for col in data.columns],\n", "        cellLoc=cell_loc,\n", "        **kwargs,\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "\n", "    for (row, col), cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "\n", "        if row == 0:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[row % len(row_colors)])\n", "            if col == 0:\n", "                cell.set_text_props(weight=\"bold\")\n", "\n", "            # --- Conditional formatting for pending > 10 ---\n", "            try:\n", "                raw_value = data.iloc[row - 1, col]\n", "                numeric_val = pd.to_numeric(raw_value, errors=\"coerce\")\n", "                if (\n", "                    col in [4, 5, 6, 7, 8]\n", "                    and pd.notna(numeric_val)\n", "                    and numeric_val >= 1\n", "                    and cond != 0\n", "                ):\n", "                    cell.set_facecolor(\"#f8d7da\")  # Light red fill\n", "            except Exception:\n", "                pass\n", "\n", "    return fig, ax"]}, {"cell_type": "markdown", "id": "be335218-2ffd-4b54-a389-e986d3d1db7c", "metadata": {}, "source": ["<h2>ERP/GRN Output</h2>"]}, {"cell_type": "code", "execution_count": null, "id": "ffa19294-36c5-4986-a085-52863b5a4443", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"with erp_data as (select date, owner, count(distinct id) as erps\n", "from(select distinct a.workflow_state, a.id, b.docnum as sap_po, b.docdate as sap_po_date, \n", "case when b.canceled = 'Y' and a.workflow_state = 'Approved' then 'ERP Approved but PO Cancelled'\n", "     when b.docnum is not null then 'Created On SAP PO'\n", "     when a.workflow_state like '%%Pending%%' then 'ERP Approval Pending'\n", "     when a.workflow_state = 'Approved' and b.docnum is null then 'Pending to create on SAP'\n", "     when a.workflow_state = 'Draft' then 'ERP Draft' else null end as sap_po_status, \n", "supplier_name, date, grand_total, department, po_requester_email_id, a.owner,\n", "creation, modified, email_subject_line, b.canceled\n", "from infra_expansion_etls.frappe_erp_dump_v2 a\n", "left join infra_expansion_etls.sap_po_dump b on b.special_instructions = a.id or b.erp_number = a.id\n", "where  date(date) >= current_date - interval '5' day)\n", "group by 1, 2),\n", "\n", "grn_dump as (select docentry, grn_docnum as grn_doc_no, grn_remarks, podocnum,\n", "date(date_parse(grn_posting_docdate, '%%d/%%m/%%Y')) as grn_posting_docdate, vendor_refno, cardname,\n", "doctotal, grn<PERSON>er, freetxt as remarks, date(date_parse(grncheckingdate, '%%d/%%m/%%Y')) as action_date\n", "from infra_expansion_etls.sap_grn_dump\n", "\n", "union all\n", "\n", "select docentry, docnum as grn_doc_no, approvalstatus, po_no, \n", "    date(DATE_PARSE(grn_posting_date, '%%Y-%%m-%%dT%%H:%%i:%%s')) as grn_posting_date, vendorreffnum, cardname, \n", "    doctotal, grn_doer, rejectionremarks, date(DATE_PARSE(rejecteddate, '%%Y-%%m-%%dT%%H:%%i:%%s')) as action_date\n", "from infra_expansion_etls.sap_grn_rejected_dump\n", "\n", "union all\n", "\n", "select draft_doc_entry, draft_doc_num as grn_doc_no, status, base_po, \n", "    date(date_parse(docdate, '%%d/%%m/%%Y')) as grn_posting_docdate, vendor_ref_num, cardname, \n", "    doctotal, doc_owner_name, null as remarks, null as action_date\n", "from infra_expansion_etls.sap_grn_pending_dump),\n", "\n", "grn_data as (select grn_posting_docdate as date, lower(grndoer) as owner, count(distinct docentry) as grns\n", "from grn_dump a\n", "where date(grn_posting_docdate) >= current_date - interval '5' day\n", "group by 1, 2)\n", "\n", "select distinct current_date - interval '1' day * i.dow as date, a.name, a.owners_mail, coalesce(c.erps, 0) as erps, coalesce(b.grns, 0) as grns, coalesce(c.erps, 0) + coalesce(b.grns, 0) as Total\n", "from infra_expansion_etls.erp_owners a\n", "left join (select case when day_of_week(current_date) = 1 then 3 else 1 end as dow) i on 1 = 1\n", "left join grn_data b on lower(a.owners_mail) = lower(b.owner) and date(b.date) = current_date - interval '1' day * i.dow\n", "left join erp_data c on lower(a.owners_mail) = lower(c.owner) and date(c.date) = current_date - interval '1' day * i.dow\n", "where try_cast(a.is_active as bigint) = 1\n", "\n", "union all\n", "\n", "select current_date - interval '1' day * i.dow as date, '-', 'Grand Total', sum(c.erps), sum(b.grns), sum(c.erps) + sum(b.grns) as Total\n", "from infra_expansion_etls.erp_owners a\n", "left join (select case when day_of_week(current_date) = 1 then 3 else 1 end as dow) i on 1 = 1\n", "left join grn_data b on lower(a.owners_mail) = lower(b.owner) and date(b.date) = current_date - interval '1' day * i.dow\n", "left join erp_data c on lower(a.owners_mail) = lower(c.owner) and date(c.date) = current_date - interval '1' day * i.dow\n", "where try_cast(a.is_active as bigint) = 1\n", "group by 1, 2, 3\n", "order by 6\n", "\"\"\"\n", "df = pd.read_sql_query(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "b06b71bb-5867-4d8c-a6e9-a1b371997159", "metadata": {}, "outputs": [], "source": ["df.columns = [\"date\", \"Owner\", \"Owner's Mail\", \"ERP's\", \"GRN's\", \"Total\"]\n", "fig, ax = render_mpl_table(df.drop(columns=[\"date\"]), header_color=\"#9cf2f9\", pad_spaces=2, cond=0)\n", "fig.savefig(\"formatted_dataframe_with_header.png\", bbox_inches=\"tight\", dpi=300)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "90d4c86a-edb0-406f-8f3a-bfbaa30338c8", "metadata": {}, "outputs": [], "source": ["if max(df[\"Total\"]) > 0:\n", "    dates = df[\"date\"].unique()[0]\n", "    niket = \"U03RLJ28N9J\"\n", "    pb.send_slack_message(\n", "        channel=actual_channel,\n", "        text=(f\"Find the ERP Output for Dark Stores for *{dates}* \" f\"\\ncc: <@{niket}>\"),\n", "        files=[\"formatted_dataframe_with_header.png\"],\n", "    )"]}, {"cell_type": "markdown", "id": "157b307e-9e2c-4871-ab14-e6a9420dfc2e", "metadata": {}, "source": ["<h2>ERP Pendency</h2>"]}, {"cell_type": "code", "execution_count": null, "id": "7c090cfa-3222-4b07-a0d5-d933416796c8", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "with pendency_erps as (\n", "    select distinct t1.id, date(t1.date) as date, \n", "    modified, supplier_name,\n", "    t1.workflow_state, t1.po_requester_email_id, \n", "    t1.owner, t1.department,\n", "    (   SELECT COUNT(CASE WHEN day_of_week(d) NOT IN (6, 7) THEN 1 END) \n", "        FROM UNNEST(SEQUENCE( CAST(t1.modified AS DATE), current_date, INTERVAL '1' DAY)) AS t_dates(d)\n", "    ) - 1 AS ageing\n", "    FROM (\n", "        SELECT DISTINCT\n", "            a.workflow_state, a.id, supplier_name,\n", "            a.date, grand_total, department, po_requester_email_id, a.owner,\n", "            creation, email_subject_line, date(substr(a.modified, 1, 10)) as modified\n", "        FROM infra_expansion_etls.frappe_erp_dump_v2 a\n", "        join blinkit.infra_expansion_etls.erp_owners b on lower(a.owner) = lower(b.owners_mail)\n", "        WHERE a.workflow_state NOT IN ('Draft', 'Approved') \n", "            and a.department IN ('Dark Stores_Site_Capex - GP', 'Dark Stores_Capex - GP', 'Dark Stores_Bulk Capex - GP', 'Dark Stores_Bulk_Site Capex - GP')\n", "    ) AS t1 )\n", "    \n", "select department, workflow_state, min(date) as min_date, max(date) as max_date,\n", "    count(distinct case when ageing >= 0 then id end) as total_erps, \n", "    count(distinct case when ageing <= 1 then id end) as ageing_1_day,\n", "    count(distinct case when ageing between 2 and 3  then id end) as ageing_2_day,\n", "    count(distinct case when ageing between 4 and 6  then id end) as ageing_3_day,\n", "    count(distinct case when ageing >= 7 then id end) as ageing_4_day,\n", "        approx_percentile(ageing, 0.75) as p75_ageing,\n", "    approx_percentile(ageing, 0.90) as p90_ageing\n", "from pendency_erps\n", "where po_requester_email_id is not null and date >= current_date - interval '30' day\n", "group by 1, 2\n", "order by 1, 2\n", "\"\"\"\n", "df = pd.read_sql_query(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "9feb5af7-2487-4a90-8816-271c27da2e6e", "metadata": {}, "outputs": [], "source": ["df.columns = [\n", "    \"Department\",\n", "    \"Status\",\n", "    \"min_date\",\n", "    \"max_date\",\n", "    \"Total ERP's\",\n", "    \"Ageing <1 Day\",\n", "    \"Ageing 2-3 Days\",\n", "    \"Ageing 4-6 Days\",\n", "    \"Ageing >7 Days\",\n", "    \"P75 Ageing\",\n", "    \"P90 Ageing\",\n", "]\n", "fig, ax = render_mpl_table(\n", "    df.drop(columns=[\"min_date\", \"max_date\"]), header_color=\"#f8e17b\", pad_spaces=2, cond=1\n", ")\n", "fig.savefig(\"formatted_dataframe_with_header.png\", bbox_inches=\"tight\", dpi=300)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "85abd4cf-2c99-427a-b3ea-5f2543374bc3", "metadata": {}, "outputs": [], "source": ["dic1 = {\n", "    \"Pending_Capex_Controllership\": [\"U07MCRXD856\", \"U03S0243Q0L\"],\n", "    \"Pending_BFM_Supply Chain & Others\": [\"U03SDCYCDCG\", \"U073U7502MS\"],\n", "}\n", "niket = \"U03RLJ28N9J\"\n", "sonali = \"U0827D2M94M\"\n", "rahul = \"U07HMK8T411\"\n", "\n", "# Get unique workflow states from the DataFrame\n", "workflow_states = df[\"Status\"].unique()\n", "\n", "# Collect mentions\n", "mentions = []\n", "for state in workflow_states:\n", "    if state in dic1:\n", "        user_tags = [f\"<@{user_id}>\" for user_id in dic1[state]]\n", "        mentions.append(f\"{state}: {', '.join(user_tags)}\")\n", "\n", "# Join all mentions into a single message line\n", "mentions_text = \"\\n\".join(mentions)\n", "\n", "pb.send_slack_message(\n", "    channel=erp_channel,\n", "    text=(\n", "        f\"Find the Dark Stores ERP Pendency Report for the last 4 weeks from: \"\n", "        f\"{min(df['min_date'])} - {max(df['max_date'])}\\n\\n\"\n", "        f\"\\n{mentions_text}\"\n", "        f\"\\ncc: <@{niket}>, <@{rahul}>\"\n", "    ),\n", "    files=[\"formatted_dataframe_with_header.png\"],\n", ")"]}, {"cell_type": "markdown", "id": "f572b787-f774-47da-ad4b-f6c86a6ab466", "metadata": {}, "source": ["<h2> ID Level ERP Pendency</h2>"]}, {"cell_type": "code", "execution_count": null, "id": "69a1166f-5155-495d-89c9-cb38aa1399e3", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "with pendency_erps as (\n", "    select distinct t1.id, date(t1.date) as date, \n", "    modified, supplier_name,\n", "    t1.workflow_state, t1.po_requester_email_id, \n", "    t1.owner, t1.department,\n", "    (   SELECT COUNT(CASE WHEN day_of_week(d) NOT IN (6, 7) THEN 1 END) \n", "        FROM UNNEST(SEQUENCE( CAST(t1.modified AS DATE), current_date, INTERVAL '1' DAY)) AS t_dates(d)\n", "    ) - 1 AS ageing\n", "    FROM (\n", "        SELECT DISTINCT\n", "            a.workflow_state, a.id, supplier_name,\n", "            a.date, grand_total, department, po_requester_email_id, a.owner,\n", "            creation, email_subject_line, date(substr(a.modified, 1, 10)) as modified\n", "        FROM infra_expansion_etls.frappe_erp_dump_v2 a\n", "        join blinkit.infra_expansion_etls.erp_owners b on lower(a.owner) = lower(b.owners_mail)\n", "        WHERE a.workflow_state NOT IN ('Draft', 'Approved') \n", "            and a.department IN ('Dark Stores_Site_Capex - GP', 'Dark Stores_Capex - GP', 'Dark Stores_Bulk Capex - GP', 'Dark Stores_Bulk_Site Capex - GP')\n", "    ) AS t1 )\n", "    \n", "select id, date as created_date, modified as modified_date, supplier_name, department, po_requester_email_id, workflow_state, \n", "case when ageing >= 0 then ageing else 0 end as ageing\n", "from pendency_erps\n", "where po_requester_email_id is not null and date >= current_date - interval '30' day\n", "   and ageing >= 0\n", "order by ageing desc\n", "\"\"\"\n", "df = pd.read_sql_query(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "19812d56-48ca-4a93-8e60-50a20629d2ab", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1vjwZp5nlUdEpYcXkpWfExND0FcqhbQ-sOx4SCiqPK4o\"\n", "sheet_name = \"DS Pendency\"\n", "sheet_link = \"https://docs.google.com/spreadsheets/d/1vjwZp5nlUdEpYcXkpWfExND0FcqhbQ-sOx4SCiqPK4o/edit?gid=0#gid=0\"\n", "pb.to_sheets(df, sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "f2388332-8cee-4346-ba18-cfbe70dfbb0b", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=erp_channel,\n", "    text=f\"Pending: <{sheet_link}|*ERP Sheet*>\",\n", ")"]}, {"cell_type": "markdown", "id": "794acef4-be38-4cad-a798-40d97446270b", "metadata": {}, "source": ["<h2> Bistro ERP Pendency</h2>"]}, {"cell_type": "code", "execution_count": null, "id": "2e0c0973-c927-4110-b834-bb7b06e7258e", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "with pendency_erps as (\n", "    select distinct t1.id, date(t1.date) as date, \n", "    modified, supplier_name,\n", "    t1.workflow_state, t1.po_requester_email_id, \n", "    t1.owner, t1.department,\n", "    (   SELECT COUNT(CASE WHEN day_of_week(d) NOT IN (6, 7) THEN 1 END) \n", "        FROM UNNEST(SEQUENCE( CAST(t1.modified AS DATE), current_date, INTERVAL '1' DAY)) AS t_dates(d)\n", "    ) - 1 AS ageing\n", "    FROM (\n", "        SELECT DISTINCT\n", "            a.workflow_state, a.id, supplier_name,\n", "            a.date, grand_total, department, po_requester_email_id, a.owner,\n", "            creation, email_subject_line, date(substr(a.modified, 1, 10)) as modified\n", "        FROM infra_expansion_etls.frappe_erp_dump_v2 a\n", "        join blinkit.infra_expansion_etls.erp_owners b on lower(a.owner) = lower(b.owners_mail)\n", "        WHERE a.workflow_state NOT IN ('Draft', 'Approved') \n", "            and a.department IN ('Bistro_Bulk_Capex - GP', 'Blinkit Meal - GP', 'Bistro_Bulk_Site Capex - GP')\n", "    ) AS t1 )\n", "    \n", "select department, workflow_state, min(date) as min_date, max(date) as max_date,\n", "    count(distinct case when ageing >= 0 then id end) as total_erps, \n", "    count(distinct case when ageing <= 1 then id end) as ageing_1_day,\n", "    count(distinct case when ageing between 2 and 3  then id end) as ageing_2_day,\n", "    count(distinct case when ageing between 4 and 6  then id end) as ageing_3_day,\n", "    count(distinct case when ageing >= 7 then id end) as ageing_4_day,\n", "    approx_percentile(ageing, 0.75) as p75_ageing,\n", "    approx_percentile(ageing, 0.90) as p90_ageing\n", "from pendency_erps\n", "where po_requester_email_id is not null and date >= current_date - interval '30' day\n", "group by 1, 2\n", "order by 1, 2\n", "\"\"\"\n", "df = pd.read_sql_query(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "244be525-610e-4662-8329-78980e2434f3", "metadata": {}, "outputs": [], "source": ["df.columns = [\n", "    \"Department\",\n", "    \"Status\",\n", "    \"min_date\",\n", "    \"max_date\",\n", "    \"Total ERP's\",\n", "    \"Ageing <1 Day\",\n", "    \"Ageing 2-3 Days\",\n", "    \"Ageing 4-6 Days\",\n", "    \"Ageing >7 Days\",\n", "    \"P75 Ageing\",\n", "    \"P90 Ageing\",\n", "]\n", "fig, ax = render_mpl_table(\n", "    df.drop(columns=[\"min_date\", \"max_date\"]), header_color=\"#f8e17b\", pad_spaces=2, cond=1\n", ")\n", "fig.savefig(\"formatted_dataframe_with_header.png\", bbox_inches=\"tight\", dpi=300)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "ad10c75a-bd61-4ee7-9b9e-385f6cae7810", "metadata": {}, "outputs": [], "source": ["dic1 = {\n", "    \"Pending_Central Finance_Meal\": [\"U03S9RESL02\", \"U07EYQ4L0H3\"],\n", "    \"Pending_Department Head_Meal\": [\"U1ECDKACD\"],\n", "    \"Pending_Business Finance Lead\": [\"U08KGU1317X\", \"U089UFFSFJ9\"],\n", "    \"Pending_BFM_Supply Chain & Others\": [\"U08KGU1317X\", \"U089UFFSFJ9\"],\n", "}\n", "niket = \"U03RLJ28N9J\"\n", "sonali = \"U0827D2M94M\"\n", "rahul = \"U07HMK8T411\"\n", "\n", "# Get unique workflow states from the DataFrame\n", "workflow_states = df[\"Status\"].unique()\n", "\n", "# Collect mentions\n", "mentions = []\n", "for state in workflow_states:\n", "    if state in dic1:\n", "        user_tags = [f\"<@{user_id}>\" for user_id in dic1[state]]\n", "        mentions.append(f\"{state}: {', '.join(user_tags)}\")\n", "\n", "# Join all mentions into a single message line\n", "mentions_text = \"\\n\".join(mentions)\n", "\n", "pb.send_slack_message(\n", "    channel=bistro_channel,\n", "    text=(\n", "        f\"Find the Bistro ERP Pendency Report for the last 4 weeks from: \"\n", "        f\"{min(df['min_date'])} - {max(df['max_date'])}\\n\\n\"\n", "        f\"\\n{mentions_text}\"\n", "        f\"\\ncc: <@{niket}>, <@{sonali}>\"\n", "    ),\n", "    files=[\"formatted_dataframe_with_header.png\"],\n", ")"]}, {"cell_type": "markdown", "id": "b5d28957-0504-4121-be05-2f325055a578", "metadata": {}, "source": ["<h2> Bistro ERP ID Pending</h2>"]}, {"cell_type": "code", "execution_count": null, "id": "f2946b4b-6c50-4bf9-85cd-751a0b22b6f4", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "with pendency_erps as (\n", "    select distinct t1.id, date(t1.date) as date, \n", "    modified, supplier_name,\n", "    t1.workflow_state, t1.po_requester_email_id, \n", "    t1.owner, t1.department,\n", "    (   SELECT COUNT(CASE WHEN day_of_week(d) NOT IN (6, 7) THEN 1 END) \n", "        FROM UNNEST(SEQUENCE( CAST(t1.modified AS DATE), current_date, INTERVAL '1' DAY)) AS t_dates(d)\n", "    ) - 1 AS ageing\n", "    FROM (\n", "        SELECT DISTINCT\n", "            a.workflow_state, a.id, supplier_name,\n", "            a.date, grand_total, department, po_requester_email_id, a.owner,\n", "            creation, email_subject_line, date(substr(a.modified, 1, 10)) as modified\n", "        FROM infra_expansion_etls.frappe_erp_dump_v2 a\n", "        join blinkit.infra_expansion_etls.erp_owners b on lower(a.owner) = lower(b.owners_mail)\n", "        WHERE a.workflow_state NOT IN ('Draft', 'Approved') \n", "            and a.department IN ('Bistro_Bulk_Capex - GP', 'Blinkit Meal - GP', 'Bistro_Bulk_Site Capex - GP')\n", "    ) AS t1 )\n", "    \n", "select id, date as created_date, modified as modified_date, supplier_name, department, po_requester_email_id, workflow_state, \n", "case when ageing >= 0 then ageing else 0 end as ageing\n", "from pendency_erps\n", "where po_requester_email_id is not null and date >= current_date - interval '30' day\n", "   and ageing >= 0\n", "order by ageing desc\n", "\"\"\"\n", "df = pd.read_sql_query(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "082b6217-ba36-4a5b-82ce-b97c63ff9996", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1vjwZp5nlUdEpYcXkpWfExND0FcqhbQ-sOx4SCiqPK4o\"\n", "sheet_name = \"Bistro Pendency\"\n", "sheet_link = \"https://docs.google.com/spreadsheets/d/1vjwZp5nlUdEpYcXkpWfExND0FcqhbQ-sOx4SCiqPK4o/edit?gid=0#gid=0\"\n", "pb.to_sheets(df, sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "69c0f20a-b846-462c-ac63-5b91c2f0701d", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=bistro_channel,\n", "    text=f\"Pending: <{sheet_link}|*Bistro ERP Sheet*>\",\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}