alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: keyword_search_growth_alert
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: category
notebook:
  parameters:
owner:
  email: <EMAIL>
  slack_id: U01SCQY98BS
path: category/monet_search/etl/keyword_search_growth_alert
paused: true
project_name: monet_search
schedule:
  interval: 00 3 * * *
  start_date: '2022-03-30T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
