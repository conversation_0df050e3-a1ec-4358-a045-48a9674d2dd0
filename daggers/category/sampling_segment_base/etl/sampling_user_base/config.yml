dag_name: sampling_user_base
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: category
notebook:
  parameters:
owner:
  email: <EMAIL>
  slack_id: U03RLF61Q1K
path: category/sampling_segment_base/etl/sampling_user_base
paused: true
project_name: sampling_segment_base
schedule:
  interval: 0 3 * * *
  start_date: '2021-03-01T00:00:00'
schedule_type: fixed
sla: 120 minutes
slack_alert_configs:
- channel: bl-data-airflow-alerts
support_files: []
tags: []
template_name: notebook
version: 1
