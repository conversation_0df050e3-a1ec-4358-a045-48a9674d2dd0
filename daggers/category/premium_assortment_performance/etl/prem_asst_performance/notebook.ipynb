{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Redshift\")\n", "r_con = pb.get_connection(\"[Replica] POS\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["month = (datetime.now() + timedelta(hours=5.5, days=-1)).strftime(\"%b-%Y\")\n", "\n", "dt = (datetime.now() + <PERSON><PERSON>ta(hours=5.5)).strftime(\"%d-%m-%Y\")\n", "\n", "month, dt"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1rNGSLJwswO_pzgk5CsJk_t9vTAVIvCBQVeK0SaNb2sU\"\n", "sheet_name = \"dump\"\n", "dump = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dump = dump.loc[dump[\"item_id\"].isnull() == False, [\"facility_id\", \"item_id\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dump[\"facility_id\"] = dump[\"facility_id\"].astype(\"int\")\n", "dump[\"item_id\"] = dump[\"item_id\"].astype(\"int\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dump.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dump[\"fac_item_key\"] = dump[\"facility_id\"].astype(\"str\") + dump[\"item_id\"].astype(\"str\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["keys = tuple([i for i in dump[\"fac_item_key\"].unique()])\n", "len(keys)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items = tuple([i for i in dump[\"item_id\"].unique()])\n", "len(items)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fac = tuple([i for i in dump[\"facility_id\"].unique()])\n", "len(fac)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_sql = \"\"\"\n", "with it_pd as (\n", " select product_id, item_id, multiplier, aspr\n", " from (select product_id, item_id, multiplier, aspr, dense_rank () over (partition by product_id order by updated_on desc) as rnk\n", " from it_pd_log) i\n", " where rnk = 1\n", " ),\n", "\n", "map as (\n", "select distinct \n", "backend_merchant_id, \n", "facility_id\n", "\n", "from dwh.dim_merchant_outlet_facility_mapping \n", "where is_current\n", "and is_mapping_enabled\n", "and merchant_business_type_id in (1,2,3,7)\n", " )\n", " \n", " select distinct \n", " facility_id,\n", " item_id, \n", " sum(product_quantity*coalesce(multiplier,1)) as qty_sold,\n", " sum(total_selling_price*coalesce(aspr,1))as gmv,\n", " SUM(TOTAL_RETAINED_margin) as rgm\n", " \n", "from dwh.fact_sales_order_item_details oid \n", "inner join it_pd i on i.product_id = oid.product_id\n", "inner join map m on m.backend_merchant_id = oid.backend_merchant_id\n", "\n", "where (cart_checkout_ts_ist) between date_trunc('month', CURRENT_TIMESTAMP + interval '5.5 hours' - 1)::timestamp and (((CURRENT_TIMESTAMP + interval '5.5 hours')::date)::timestamp) -interval '1 second'\n", "and city_name not in ('Not in service area', 'Hapur', 'test1207898732')\n", "and city_name not ilike '%%b2b%%'\n", "and (oid.order_type not ilike '%%internal%%' or oid.order_type is null)\n", "and facility_id||item_id in {keys}\n", "and unit_selling_price > 0\n", "group by 1,2\n", "\n", "\n", "\"\"\".format(\n", "    keys=keys\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sale = pd.read_sql(con=con, sql=sales_sql)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sale.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["soh_sql = \"\"\"\n", "select facility_id,\n", "         item_id,\n", "         sum(Actual_Quantity) as \"Actual Quantity\",\n", "         sum(Order_Blocked_Quantity) as \"Order Blocked Quantity\",\n", "         sum(SSO_Blocked_Quantity) as \"SSO Blocked Quantity\",\n", "         sum(STO_Blocked_Quantity) as \"STO Blocked Quantity\",\n", "         sum(Net_Quantity) as \"Net Quantity\",\n", "         sum(Pending_Putaway_Quantity) as \"Pending Putaway Quantity\"\n", "         \n", "         from (\n", "          \n", "   SELECT iii.outlet_id AS \"Outlet ID\",\n", "   rwf.id as facility_id,\n", "   rwf.name as facility,\n", "    iii.item_id AS Item_ID,\n", "    iii.quantity AS Actual_Quantity,\n", "         sum((CASE\n", "                  WHEN iibi.blocked_type = 1 THEN iibi.quantity\n", "                  ELSE 0\n", "              END)) AS Order_Blocked_Quantity,\n", "         sum((CASE\n", "                  WHEN iibi.blocked_type = 2 THEN iibi.quantity\n", "                  ELSE 0\n", "              END)) AS STO_Blocked_Quantity,\n", "         sum((CASE\n", "                  WHEN iibi.blocked_type = 5 THEN iibi.quantity\n", "                  ELSE 0\n", "              END)) AS SSO_Blocked_Quantity,\n", "              \n", "         (iii.quantity - COALESCE(sum(iibi.quantity),0)) AS Net_Quantity,\n", "         \n", "         CASE\n", "             WHEN igi.quantity IS NOT NULL THEN igi.quantity\n", "             ELSE 0\n", "         END AS Pending_Putaway_Quantity\n", "  FROM ims.ims_item_inventory iii\n", "\n", "  LEFT JOIN ims.ims_item_blocked_inventory iibi ON iii.item_id=iibi.item_id\n", "  and  iii.active=1\n", "    AND iibi.blocked_type IN (1,\n", "                            2,\n", "                            5)\n", "  \n", "  AND iii.outlet_id=iibi.outlet_id\n", "  and iii.item_id in {items}\n", "  \n", "  INNER JOIN retail.console_outlet co ON co.id=iii.outlet_id\n", "\n", "  INNER JOIN retail.console_location cl ON cl.id=co.tax_location_id\n", "    inner join retail.warehouse_facility rwf on rwf.id = co.facility_id\n", "    and rwf.id in {fac}\n", "\n", "  INNER JOIN\n", "    (SELECT item_id,\n", "            name\n", "     FROM rpc.product_product pp2\n", "     WHERE pp2.id IN\n", "         (SELECT max(id) AS id\n", "          FROM rpc.product_product pp\n", "          WHERE pp.active=1\n", "            AND pp.approved=1\n", "          GROUP BY item_id) ) product ON product.item_id=iii.item_id \n", "          and iii.item_id in {items}\n", "\n", "  LEFT JOIN\n", "    (SELECT igi.outlet_id,\n", "            pp.item_id,\n", "            sum(igi.quantity) AS quantity\n", "     FROM ims.ims_good_inventory igi\n", "     INNER JOIN rpc.product_product pp ON igi.upc_id = pp.upc\n", "     AND igi.variant_id = pp.variant_id\n", "     AND pp.active = 1\n", "     AND pp.approved = 1\n", "     and pp.item_id in {items}\n", "     WHERE igi.inventory_update_type_id IN (28,\n", "                                            76)\n", "       AND igi.active = 1\n", "     GROUP BY 1,\n", "              2\n", "     HAVING sum(igi.quantity) > 0) igi ON iii.outlet_id = igi.outlet_id\n", "  AND product.item_id = igi.item_id\n", "  \n", "                         \n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4,\n", "         5,\n", "         \n", "        10\n", "         ) fin\n", "         \n", "         \n", "         group by 1,2\n", "\"\"\".format(\n", "    items=items, fac=fac\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["soh = pd.read_sql_query(sql=soh_sql, con=r_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["soh.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["soh = soh.loc[:, [\"facility_id\", \"item_id\", \"Net Quantity\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in [\"facility_id\", \"item_id\"]:\n", "    soh[i] = soh[i].astype(\"int\")\n", "    sale[i] = sale[i].astype(\"int\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_n_sale = pd.merge(\n", "    left=dump,\n", "    right=sale,\n", "    how=\"left\",\n", "    left_on=[\"facility_id\", \"item_id\"],\n", "    right_on=[\"facility_id\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_n_sale.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_n_sale_n_soh = pd.merge(\n", "    left=base_n_sale,\n", "    right=soh,\n", "    how=\"left\",\n", "    left_on=[\"facility_id\", \"item_id\"],\n", "    right_on=[\"facility_id\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_n_sale_n_soh.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in [\"qty_sold\", \"gmv\", \"Net Quantity\"]:\n", "    base_n_sale_n_soh[i] = base_n_sale_n_soh[i].fillna(value=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fac_sql = \"\"\"\n", "select id as facility_id, name as facility_name from\n", "lake_retail.warehouse_facility\n", "where id in {fac}\n", "\"\"\".format(\n", "    fac=fac\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fac_name = pd.read_sql_query(con=con, sql=fac_sql)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fac_name.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_fac_name = pd.merge(\n", "    left=base_n_sale_n_soh,\n", "    right=fac_name,\n", "    how=\"left\",\n", "    left_on=[\"facility_id\"],\n", "    right_on=[\"facility_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cat_sql = \"\"\"\n", "\n", "WITH PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name as product_type,\n", "          pt.id as product_type_id,\n", "          p.storage_type\n", "          \n", "   from lake_cms.gr_product P\n", "   INNER join lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER join lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER join lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER join lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   inner join lake_cms.gr_product_type pt on p.type_id=pt.id\n", "\n", "   \n", "   ) \n", "  \n", "SELECT rpc.item_id,\n", "product_id,\n", "       (cat.product || ' ' || cat.unit) AS name,\n", "       cat.L0,\n", "       l1,\n", "       cat.brand\n", "\n", "       \n", "from  lake_rpc.item_product_mapping  rpc\n", "INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL\n", "and rpc.item_id in {items}\n", "\n", "\n", "\n", "\"\"\".format(\n", "    items=items\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cat_map = pd.read_sql(con=con, sql=cat_sql)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data = pd.merge(\n", "    left=base_fac_name,\n", "    right=cat_map,\n", "    how=\"left\",\n", "    left_on=[\"item_id\"],\n", "    right_on=[\"item_id\"],\n", ").<PERSON>na(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data[\"Month\"] = month"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data = final_data.loc[\n", "    :,\n", "    [\n", "        \"Month\",\n", "        \"facility_name\",\n", "        \"fac_item_key\",\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"product_id\",\n", "        \"name\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"brand\",\n", "        \"qty_sold\",\n", "        \"gmv\",\n", "        \"rgm\",\n", "        \"Net Quantity\",\n", "    ],\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data[\"soh_ts\"] = (datetime.now() + timedelta(hours=5.5)).strftime(\n", "    \"%d-%m-%Y %H:%m\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(final_data, sheet_id, \"raw\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ts = (datetime.now() + timed<PERSON>ta(hours=5.5)).strftime(\"%d-%m-%Y %H:%m\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\"r3-data-updates\", f\"Premium assortment data updated on {ts}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}