{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import time as t\n", "from datetime import datetime, timedelta\n", "from pytz import timezone"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start = t.time()\n", "ts = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "ts = ts.date() - <PERSON><PERSON><PERSON>(days=3)\n", "# dt = (datetime.now() + timedelta(hours=5.5)).strftime(\"%d-%m-%Y\")\n", "ts"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["numdays = 3\n", "date_list = [ts + timedelta(days=x) for x in range(numdays)]\n", "# for x in date_list:\n", "#     print(x)\n", "\n", "sd = ts\n", "ed = ts + <PERSON><PERSON>ta(days=(numdays - 1))\n", "\n", "# print(sd, ed)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Redshift\")\n", "con_pre = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["atc_sql = \"\"\"\n", "\n", "with\n", "atc as (\n", " Select\n", "        e.at_date_ist,\n", "        e.traits__city_name as city,\n", "        e.traits__merchant_id as merchant_id,\n", "        e.properties__product_id::int as pid,\n", "        e.properties__mrp as mrp,\n", "        e.properties__price as sp,\n", "        e.properties__page_name,\n", "        e.properties__page_visit_id\n", "    from\n", "        spectrum.mobile_event_data e\n", "    where\n", "        e.at_date_ist between '{sd}' and '{ed}'\n", "        and e.name = 'Product Added'\n", "        and e.traits__city_name not in ('Not in service area')\n", "        and e.traits__city_name not ilike ('B2B%%')\n", "        and pid > 0\n", "        and pid/1 = pid\n", "),\n", "\n", "pdp_visit as(\n", "    Select\n", "        i.properties__last_page_name as page_name,\n", "        -- i.properties__page_name,\n", "        i.properties__page_visit_id\n", "    from\n", "        spectrum.mobile_impression_data i\n", "    WHERE \n", "        i.at_date_ist between '{sd}' and '{ed}'\n", "        and i.name = 'Product Page Visit'\n", "        and i.traits__city_name not in ('Not in service area')\n", "        and i.traits__city_name not ilike ('B2B%%')\n", "        and i.properties__page_visit_id is not null\n", "        and i.properties__page_visit_id <> 'pdp'\n", "    Group by 1,2\n", "    ),\n", "\n", "all_atc as(\n", "    Select\n", "        a.at_date_ist,\n", "        a.city,\n", "        merchant_id,\n", "        case when pdp.page_name is not null then pdp.page_name else a.properties__page_name end as page_name,\n", "        a.pid,\n", "        a.sp,\n", "        (case when am.offer_type ilike '%%flat%%'\n", "            then COALESCE((am.bf_margin * (a.mrp - am.sp)),0.0)\n", "            else COALESCE((am.bf_margin * a.mrp), 0.0)\n", "        end) as bf\n", "    from\n", "        atc a\n", "        left join pdp_visit pdp on pdp.properties__page_visit_id = a.properties__page_visit_id\n", "        left join all_margins am on lower(a.city) = lower(case when am.city ilike 'bangalore' then 'bengaluru' else am.city end)\n", "                                and a.pid = am.pid\n", "                                and a.at_date_ist between am.start_date::date and am.end_date::date   \n", "    )\n", "\n", "\n", "Select\n", "    at_date_ist, city, merchant_id, page_name, pid,\n", "    count(sp) as atc,\n", "    sum(sp) as atc_gmv,\n", "    sum(bf) as bf\n", "from all_atc\n", "group by 1,2,3,4,5\n", "\n", "\"\"\".format(\n", "    sd=sd, ed=ed\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["atc_list = []\n", "for chunk in pd.read_sql(con=con, sql=atc_sql, chunksize=30000):\n", "    atc_list.append(chunk)\n", "atc_df = pd.concat(atc_list, axis=0, ignore_index=True)\n", "atc_df[\"pid\"] = atc_df[\"pid\"].astype(int)\n", "atc_df[\"at_date_ist\"] = pd.to_datetime(atc_df[\"at_date_ist\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["del atc_list\n", "# atc_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pid_df = pd.DataFrame()\n", "pid_df = atc_df[\"pid\"].drop_duplicates().to_frame()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(type(pid_df))\n", "# pid_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"playground\",  # Redshift schema name\n", "    \"table_name\": \"atc_pid\",  # Redshift table name\n", "    \"column_dtypes\": [\n", "        {\"name\": \"pid\", \"type\": \"integer\", \"description\": \"product_id\"},\n", "    ],\n", "    \"primary_key\": [\"pid\"],  # list\n", "    # \"sortkey\": [\"at_date_ist\"],  # list\n", "    # \"incremental_key\": \"at_date_ist\",  # string\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"staging table to hold product ids from the event product added\",  # Description of the table being sent to redshift\n", "}\n", "\n", "pb.to_redshift(pid_df, **kwargs)\n", "# print(\"Data has been inserted to table on redshift\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### WLP"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "tags": []}, "outputs": [], "source": ["# wlp_sql = \"\"\"\n", "\n", "# with\n", "\n", "# it_pd_mapping as (\n", "#     Select item_id, product_id, multiplier\n", "#     from dwh.dim_item_product_offer_mapping\n", "#     where product_id in {pid}\n", "#     group by 1,2,3\n", "#     ),\n", "\n", "# wlp_base as (\n", "#     Select\n", "#         '{sd}' as at_date_ist,\n", "#         w.merchant_id,\n", "#         w.item_id,\n", "#         m.product_id,\n", "#         m.multiplier,\n", "#         avg(w.wlp) as wlp\n", "#     from dwh.logs_weighted_landing_price w\n", "#         inner join it_pd_mapping m on m.item_id = w.item_id\n", "#     where\n", "#         '{sd}' between start_ts_ist and end_ts_ist\n", "#         and w.merchant_flag = 'frontend'\n", "#      group by 1,2,3,4,5\n", "#     )\n", "\n", "#     Select\n", "#         at_date_ist,\n", "#         merchant_id,\n", "#         product_id as pid,\n", "#         sum(multiplier * wlp) as wlp\n", "#     from wlp_base\n", "#     group by 1,2,3\n", "\n", "# \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["wlp_sql = \"\"\"\n", "\n", "with\n", "\n", "it_pd_mapping as (\n", "    Select item_id, product_id, multiplier\n", "    from dwh.dim_item_product_offer_mapping i\n", "        inner join playground.atc_pid p on p.pid = i.product_id\n", "    group by 1,2,3\n", "    ),\n", "\n", "wlp_base as (\n", "    Select\n", "        '{sd}' as at_date_ist,\n", "        w.merchant_id,\n", "        w.item_id,\n", "        m.product_id,\n", "        m.multiplier,\n", "        avg(w.wlp) as wlp\n", "    from dwh.logs_weighted_landing_price w\n", "        inner join it_pd_mapping m on m.item_id = w.item_id\n", "    where\n", "        start_ts_ist < ('{sd}' + 1) \n", "        and end_ts_ist >= '{sd}'\n", "        and w.merchant_flag = 'frontend'\n", "     group by 1,2,3,4,5\n", "    )\n", "\n", "    Select\n", "        at_date_ist,\n", "        merchant_id,\n", "        product_id as pid,\n", "        sum(multiplier * wlp) as wlp\n", "    from wlp_base\n", "    group by 1,2,3\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["wlp_list = []\n", "for x in date_list:\n", "    # print(x)\n", "    wlp = pd.read_sql(con=con, sql=wlp_sql.format(sd=x))\n", "    # print(wlp.shape)\n", "    wlp_list.append(wlp)\n", "wlp_df = pd.concat(wlp_list, axis=0, ignore_index=True)\n", "wlp_df[\"at_date_ist\"] = pd.to_datetime(wlp_df[\"at_date_ist\"])\n", "wlp_df[\"merchant_id\"] = wlp_df[\"merchant_id\"].astype(int)\n", "wlp_df[\"pid\"] = wlp_df[\"pid\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = pd.merge(\n", "    left=atc_df,\n", "    right=wlp_df,\n", "    how=\"left\",\n", "    left_on=[\"at_date_ist\", \"merchant_id\", \"pid\"],\n", "    right_on=[\"at_date_ist\", \"merchant_id\", \"pid\"],\n", ").fillna({\"atc\": 0, \"atc_gmv\": 0, \"bf\": 0, \"wlp\": 0})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# data['wlp'].isna().sum()\n", "# data.loc[(data['pid'] == 436775) & (data['merchant_id'] == 30912)]\n", "del atc_df, wlp_df, wlp_list, pid_df\n", "# data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"atc_rgm\"] = (data[\"atc_gmv\"] - (data[\"wlp\"] * data[\"atc\"])) + data[\"bf\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = data.pivot_table(\n", "    index=[\"at_date_ist\", \"city\", \"page_name\", \"pid\"],\n", "    values=[\"atc\", \"atc_gmv\", \"atc_rgm\"],\n", "    aggfunc=\"sum\",\n", ").reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Impressions"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["imp_sql = \"\"\"\n", "\n", "    with \n", "\n", "base as (\n", "Select\n", "        i.at_date_ist,\n", "        i.traits__city_name as city,\n", "        i.properties__page_name as page_name,\n", "        case\n", "            when i.properties__child_widget_id is not null then i.properties__child_widget_id\n", "            else i.properties__widget_id\n", "            end as pid,\n", "        count(distinct i.session_uuid) as impressions\n", "    From\n", "        lake_events.mobile_impression_data i\n", "    WHERE\n", "        i.at_date_ist = date '{sd}'\n", "        and i.name = 'Product Shown'\n", "        and (i.properties__inventory != 0 or i.properties__inventory is null)\n", "        and i.traits__city_name not in ('Not in service area')\n", "        and i.traits__city_name not like ('B2B%%')\n", "    Group by 1,2,3,4\n", ")\n", "\n", "Select * from base\n", "where not regexp_like(pid, '[a-zA-Z]')\n", "and not regexp_like(city, '[0-9]')\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["imp_list = []\n", "for x in date_list:\n", "    # print(x)\n", "    imp = pd.read_sql_query(con=con_pre, sql=imp_sql.format(sd=x))\n", "    imp_list.append(imp)\n", "    # print(imp.shape)\n", "imp_df = pd.concat(imp_list, axis=0, ignore_index=True)\n", "imp_df[\"at_date_ist\"] = pd.to_datetime(imp_df[\"at_date_ist\"])\n", "imp_df[\"pid\"] = imp_df[\"pid\"].fillna(0).astype(int)\n", "imp_df[\"impressions\"] = imp_df[\"impressions\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# del imp_list\n", "print(imp_df.shape)\n", "# imp_df.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df = pd.merge(\n", "    left=imp_df,\n", "    right=data,\n", "    how=\"left\",\n", "    left_on=[\"at_date_ist\", \"city\", \"page_name\", \"pid\"],\n", "    right_on=[\"at_date_ist\", \"city\", \"page_name\", \"pid\"],\n", ").fillna({\"atc\": 0, \"atc_gmv\": 0, \"atc_rgm\": 0, \"impressions\": 0})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["del data, imp_list, imp_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df = final_df.loc[\n", "    :,\n", "    [\n", "        \"at_date_ist\",\n", "        \"city\",\n", "        \"pid\",\n", "        \"page_name\",\n", "        \"impressions\",\n", "        \"atc\",\n", "        \"atc_gmv\",\n", "        \"atc_rgm\",\n", "    ],\n", "]\n", "final_df[\"impressions\"] = final_df[\"impressions\"].astype(int)\n", "final_df[\"atc\"] = final_df[\"atc\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"consumer\",  # Redshift schema name\n", "    \"table_name\": \"pid_impressions_and_atc\",  # Redshift table name\n", "    \"column_dtypes\": [\n", "        {\"name\": \"at_date_ist\", \"type\": \"date\", \"description\": \"Date IST\"},\n", "        {\"name\": \"city\", \"type\": \"text\", \"description\": \"city name\"},\n", "        {\"name\": \"pid\", \"type\": \"integer\", \"description\": \"product_id\"},\n", "        {\n", "            \"name\": \"page_name\",\n", "            \"type\": \"text\",\n", "            \"description\": \"page name where impression happened\",\n", "        },\n", "        {\n", "            \"name\": \"impressions\",\n", "            \"type\": \"int8\",\n", "            \"description\": \"PID impressions (distinct session_uuid) from Product Shown\",\n", "        },\n", "        {\"name\": \"atc\", \"type\": \"integer\", \"description\": \"Product added event count\"},\n", "        {\n", "            \"name\": \"atc_gmv\",\n", "            \"type\": \"float\",\n", "            \"description\": \"Sum of price from product added event\",\n", "        },\n", "        {\n", "            \"name\": \"atc_rgm\",\n", "            \"type\": \"float\",\n", "            \"description\": \"Sum of retained of atc item\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"at_date_ist\", \"city\", \"pid\", \"page_name\"],  # list\n", "    \"sortkey\": [\"at_date_ist\"],  # list\n", "    \"incremental_key\": \"at_date_ist\",  # string\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Daily city_pid level impressionsa and atc measures from mobile_impression_data and mobile_event_data tables\",  # Description of the table being sent to redshift\n", "}\n", "\n", "pb.to_redshift(final_df, **kwargs)\n", "# print(\"Data has been inserted to table on redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["del final_df\n", "time_taken = round((t.time() - start) / 60, 2)\n", "pb.send_slack_message(\n", "    \"bl-k-data-updates\",\n", "    f\" The table <consumer.pid_impressions_and_atc> table has been updated and it took {time_taken} mins.\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}