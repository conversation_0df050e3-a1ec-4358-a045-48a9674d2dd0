alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: dod_pid_impressions
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 16G
      request: 500M
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: category
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RLF61Q1K
path: category/pid_impression_dashboard/etl/dod_pid_impressions
paused: true
project_name: pid_impression_dashboard
schedule:
  interval: 30 23 * * *
  start_date: '2022-10-06T00:00:00'
schedule_type: fixed
sla: 121 minutes
support_files: []
tags: []
template_name: notebook
version: 11
