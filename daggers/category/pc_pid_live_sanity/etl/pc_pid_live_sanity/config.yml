alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: pc_pid_live_sanity
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: category
notebook:
  parameters:
owner:
  email: <EMAIL>
  slack_id: U03RLF61Q1K
path: category/pc_pid_live_sanity/etl/pc_pid_live_sanity
paused: true
project_name: pc_pid_live_sanity
schedule:
  interval: 0 4-17/4 * * *
  start_date: '2021-12-08T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
