alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: ggn_key_sku_performance
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: category
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RLF61Q1K
path: category/sku_performance_ggn_core/etl/ggn_key_sku_performance
paused: true
project_name: sku_performance_ggn_core
schedule:
  interval: 50 2 * * *
  start_date: '2021-11-09T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
