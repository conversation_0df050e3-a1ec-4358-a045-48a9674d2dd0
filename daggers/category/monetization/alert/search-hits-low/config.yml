dag_name: search-hits-low
dag_type: alert
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: category
notebook:
  parameters:
owner:
  email: <EMAIL>
  slack_id: U01BPDA034H
path: category/monetization/alert/search-hits-low
paused: true
project_name: monetization
schedule:
  interval: 25 4 * * *
  start_date: '2020-12-16T00:00:00'
schedule_type: fixed
sla: 120 minutes
slack_alert_configs:
- channel: test-alert-channel
support_files: []
tags: []
template_name: notebook
version: 1
