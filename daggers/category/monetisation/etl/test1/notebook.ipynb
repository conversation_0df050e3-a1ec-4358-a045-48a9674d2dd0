{"cells": [{"cell_type": "code", "execution_count": null, "id": "2d24ef48-4dbe-4a2d-a2b0-14065e1bd5c2", "metadata": {}, "outputs": [], "source": ["!pip install UliPlot\n", "!pip install openpyxl\n", "!pip install xlsxwriter\n", "import os\n", "import sys\n", "import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import openpyxl\n", "from dateutil import tz, parser\n", "from datetime import date, timedelta, datetime\n", "import boto3\n", "from botocore.exceptions import NoCredentialsError\n", "\n", "from UliPlot.XLSX import auto_adjust_xlsx_column_width\n", "import xlsxwriter\n", "\n", "\n", "sheet_id = \"1cYx-ZrGM7cwHtN5s46w6j_3N3Zpim6EMymcvUzETE50\"\n", "sheet_name = \"Version1\"\n", "\n", "email_df = pb.from_sheets(sheet_id, sheet_name)\n", "email_df[\"Emails\"] = email_df[\"Emails\"].replace(r\"\\r+|\\n+|\\t+\", \"\", regex=True)\n", "email_df[\"Emails\"] = email_df[\"Emails\"].replace(\" \", \"\", regex=True)\n", "email_df[\"POC1 Email\"] = email_df[\"POC1 Email\"].replace(r\"\\r+|\\n+|\\t+\", \"\", regex=True)\n", "email_df[\"POC1 Email\"] = email_df[\"POC1 Email\"].replace(\" \", \"\", regex=True)\n", "email_df = email_df.astype(str)\n", "email_df.head()\n", "html_content = f\"\"\"\n", "<p>Dear Partner,</p>\n", "<p>Please find attached the performance campaign report for this month until yesterday.This includes both Keyword Targeting and Category Targeting of your Search Campaigns along with Listing Spotlight campaigns on Blinkit Brand Central.</p>\n", "<p>Thank you</p>\n", "<p>Blinkit Ads Team</p>\n", "\"\"\"\n", "for i in range(email_df.shape[0]):\n", "    try:\n", "        filename = \"Rakhi Event Campaign Aug-23.pdf\"\n", "\n", "        from_email = email_df[\"POC Email\"][i]\n", "\n", "        subject = str(email_df[\"manufacturer\"][i]) + \" || \" + \"Rakhi Event Campaigns\"\n", "\n", "        to_email = list((email_df[\"Emails\"][i].split(\",\")))\n", "        cc = list((email_df[\"POC1 Email\"][i].split(\",\")))\n", "\n", "        if to_email[0] != \"\":\n", "            pb.send_email(\n", "                from_email,\n", "                to_email,\n", "                cc,\n", "                subject,\n", "                html_content,\n", "                files=[filename],\n", "            )\n", "\n", "    except Exception as e:\n", "        print(e)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}