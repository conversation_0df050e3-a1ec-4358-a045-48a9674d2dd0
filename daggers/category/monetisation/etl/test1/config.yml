alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: test1
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: airflow-dags-spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: category
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U04HWGMN1NH
path: category/monetisation/etl/test1
paused: false
project_name: monetisation
schedule:
  end_date: '2023-07-21T00:00:00'
  interval: 00 10 * * *
  start_date: '2023-07-19T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: 
- Rakhi Event Campaign Aug-23.pdf
tags: []
template_name: notebook
version: 1
