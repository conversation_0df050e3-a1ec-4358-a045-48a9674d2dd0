{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1trJErrJDVs_nHvAsTTfWHCSzwG56o33ibdYpb6fDJkA\"\n", "sheet_name = \"manf_map_do_not_edit\"\n", "manf_list_df = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["manf_list_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["manf_list_df = manf_list_df.loc[((manf_list_df[\"manf_id\"] == \"\") == False), :]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["manf_list_df[\"manf_id\"] = manf_list_df[\"manf_id\"].astype(\"int\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["manf_id = tuple(\n", "    [\n", "        i\n", "        for i in manf_list_df.loc[manf_list_df[\"manf_id\"].isnull() == False, :][\n", "            \"manf_id\"\n", "        ].unique()\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(len(manf_id))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pids_sql = \"\"\"\n", "select distinct pid::int\n", "from lake_rpc.product_product pp \n", "inner join lake_rpc.product_brand pb on pb.id = pp.brand_id\n", "inner join lake_rpc.product_manufacturer pm on pm.id = pb.manufacturer_id\n", "inner join (select distinct item_id, product_id as pid from\n", "   (select distinct item_id, product_id , dense_rank () over (partition by product_id order by updated_on desc) r\n", "   from it_pd_log) g1\n", "   where r = 1 ) g on g.item_id = pp.item_id\n", "inner join dwh.dim_product p on p.product_id = g.pid\n", "\n", "where pm.id in {lst}\n", "and p.is_current\n", "and p.l0_category != 'Specials'\n", "\n", "\"\"\".format(\n", "    lst=manf_id\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pid_df = pd.read_sql_query(con=con, sql=pids_sql)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pids = tuple([i for i in pid_df[\"pid\"].unique()])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(pids)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sale_sql = \"\"\"\n", "select distinct \n", "date(cart_checkout_ts_ist) as dt,\n", "case when city_name in ('Delhi', 'HR-NCR', 'UP-NCR', 'Jaipur', 'Lucknow', 'Kanpur', 'Mumbai', 'Pune', 'Ahmedabad', 'Bengaluru', 'Kolkata', \n", "'Hyderabad', 'Chennai', 'Guwahati', 'Chandigarh', 'Mohali', 'Panchkula', 'Faridabad') then city_name else 'Other Cities' end as city_tag,\n", "case when (c.l0_category = 'Breakfast & Dairy' and c.l1_category = 'Breakfast Cereal & Mixes') then 'Breakfast'\n", "        when (c.l0_category = 'Breakfast & Dairy' and c.l1_category != 'Breakfast Cereal & Mixes') then 'Dairy'\n", "            else c.l0_category end AS l_cat,\n", "c.l1_category as l1_cat,\n", "c.l2_category as l2_cat,\n", "c.manufacturer_name as manf,\n", "c.brand_name as brand,\n", "\n", "sum(total_selling_price) as gmv,\n", "sum(total_mrp) as mrp_gmv,\n", "sum(product_quantity) as qty,\n", "sum(total_brand_fund) as bf,\n", "sum(total_mrp-total_selling_price) as discount,\n", "sum(total_retained_margin) as rgm,\n", "sum(unit_weighted_landing_price*product_quantity) as lp_value\n", "\n", "from dwh.fact_sales_order_item_details oid \n", "inner join dwh.dim_product c on c.product_id = oid.product_id\n", "and c.is_current    \n", "\n", "where c.product_id in {pids}\n", "\n", "and (cart_checkout_ts_ist) between date_trunc('month', CURRENT_TIMESTAMP + interval '5.5 hours' - 1)::timestamp and\n", "((((CURRENT_TIMESTAMP + interval '5.5 hours')::date)::timestamp) -interval '1 second')\n", "\n", "and city_name not in ('Not in service area', 'Hapur', 'test1207898732')\n", "and city_name not ilike '%%b2b%%'\n", "and item_cancellation_reason is null\n", "and order_current_status != 'CANCELLED'\n", "and (oid.order_type not ilike '%%internal%%' or oid.order_type is null)\n", "\n", "and unit_selling_price > 0\n", "\n", "and dim_product_key not in (select distinct product_key from dwh.dim_product where product_name = 'Smart Bachat Club Membership')\n", "and c.product_type_id not in (11780, 11778, 11791,11927, 11958,11778,11934, 11957,11960, 11939, 9759)\n", "\n", "group by 1,2,3,4,5,6,7\n", "\n", "\"\"\".format(\n", "    pids=pids\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = pd.read_sql(con=con, sql=sale_sql)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"gmv\"].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["carts_sql = \"\"\"\n", "select distinct \n", "date(cart_checkout_ts_ist) as dt,\n", "case when city_name in ('Delhi', 'HR-NCR', 'UP-NCR', 'Jaipur', 'Lucknow', 'Kanpur', 'Mumbai', 'Pune', 'Ahmedabad', 'Bengaluru', 'Kolkata', \n", "'Hyderabad', 'Chennai', 'Guwahati', 'Chandigarh', 'Mohali', 'Panchkula', 'Faridabad') then city_name else 'Other Cities' end as city_tag,\n", "\n", "count(distinct cart_id) as dt_city_carts\n", "\n", "from dwh.fact_sales_order_item_details oid \n", "inner join dwh.dim_product c on c.product_id = oid.product_id\n", "and c.is_current    \n", "and dim_product_key not in (select distinct product_key from dwh.dim_product where product_name = 'Smart Bachat Club Membership')\n", "\n", "where (cart_checkout_ts_ist) between date_trunc('month', CURRENT_TIMESTAMP + interval '5.5 hours' - 1)::timestamp and\n", "((((CURRENT_TIMESTAMP + interval '5.5 hours')::date)::timestamp) -interval '1 second')\n", "\n", "and city_name not in ('Not in service area', 'Hapur', 'test1207898732')\n", "and city_name not ilike '%%b2b%%'\n", "and (oid.order_type not ilike '%%internal%%' or oid.order_type is null)\n", "\n", "and unit_selling_price > 0\n", "\n", "\n", "\n", "group by 1,2\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["carts_df = pd.read_sql_query(con=con, sql=carts_sql)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data = pd.merge(\n", "    data, carts_df, how=\"inner\", left_on=[\"dt\", \"city_tag\"], right_on=[\"dt\", \"city_tag\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data = final_data.loc[\n", "    :,\n", "    [\n", "        \"dt\",\n", "        \"dt_city_carts\",\n", "        \"city_tag\",\n", "        \"l_cat\",\n", "        \"l1_cat\",\n", "        \"l2_cat\",\n", "        \"manf\",\n", "        \"brand\",\n", "        \"gmv\",\n", "        \"mrp_gmv\",\n", "        \"qty\",\n", "        \"bf\",\n", "        \"discount\",\n", "        \"rgm\",\n", "        \"lp_value\",\n", "    ],\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(final_data, sheetid=sheet_id, sheetname=\"raw_data\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\"r3-data-updates\", \"new accounts data updated\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}