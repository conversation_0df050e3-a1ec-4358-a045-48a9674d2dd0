alert_configs:
  slack:
  - channel: bl-adonp-cases-alert
dag_name: adonp_daily_cases
dag_type: etl
escalation_priority: low
execution_timeout: 90
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: crm
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03S5ACSW3C
path: crm/payments/etl/adonp_daily_cases
paused: false
pool: crm_pool
project_name: payments
schedule:
  end_date: '2025-07-28T00:00:00'
  interval: 1 0 * * *
  start_date: '2025-01-14T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 4
