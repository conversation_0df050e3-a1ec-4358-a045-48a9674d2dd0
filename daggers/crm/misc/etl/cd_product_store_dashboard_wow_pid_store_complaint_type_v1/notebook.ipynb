{"cells": [{"cell_type": "code", "execution_count": null, "id": "b6ecb023-8f04-427e-9b04-2ecab5186acb", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "from datetime import date, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "568b0d71-9e2a-42be-8df9-b00531f2a8c5", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "d511784c-5c8e-4f48-b7d7-807ebe2f78eb", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"cd_etls\",\n", "    \"table_name\": \"cd_product_store_dashboard_wow_pid_store_complaint_type_v1\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"week\", \"type\": \"date\", \"description\": \"text\"},\n", "        {\"name\": \"week_no\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"product_id\", \"type\": \"VARCHAR\", \"description\": \"text\"},\n", "        {\"name\": \"city\", \"type\": \"VARCHAR\", \"description\": \"text\"},\n", "        {\"name\": \"manufacturer_id\", \"type\": \"VARCHAR\", \"description\": \"text\"},\n", "        {\"name\": \"frontend_merchant_id\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"l0_category_id\", \"type\": \"VARCHAR\", \"description\": \"text\"},\n", "        {\"name\": \"l1_category_id\", \"type\": \"VARCHAR\", \"description\": \"text\"},\n", "        {\"name\": \"l2_category_id\", \"type\": \"VARCHAR\", \"description\": \"text\"},\n", "        {\"name\": \"complaint_type\", \"type\": \"VARCHAR\", \"description\": \"text\"},\n", "        {\"name\": \"brand_id\", \"type\": \"VARCHAR\", \"description\": \"text\"},\n", "        {\"name\": \"product_type_id\", \"type\": \"VARCHAR\", \"description\": \"text\"},\n", "        {\"name\": \"carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_pid_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"current_week_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"current_week_complaint_carts\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_no\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_complaint_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"pid_complaint_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_pid_complaints\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"pid_resolved_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_pid_resolved_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"new_product_flag\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"l0_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_l0_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"l0_complaint_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_l0_complaints\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"l0_resolved_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_l0_resolved_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"l1_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_l1_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"l1_complaint_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_l1_complaints\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"l1_resolved_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_l1_resolved_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"l2_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_l2_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"l2_complaint_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_l2_complaints\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"l2_resolved_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_l2_resolved_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"snapshot_date\", \"type\": \"date\", \"description\": \"text\"},\n", "    ],\n", "    \"primary_key\": [\n", "        \"week_no\",\n", "        \"complaint_type\",\n", "        \"city\",\n", "        \"frontend_merchant_id\",\n", "        \"l0_category_id\",\n", "        \"l1_category_id\",\n", "        \"l2_category_id\",\n", "        \"product_type_id\",\n", "        \"brand_id\",\n", "        \"manufacturer_id\",\n", "        \"product_id\",\n", "    ],\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"Product Store week on week complaints\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "f427ab21-e45e-483c-8da4-14c3c8d05fd3", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "\n", "with sales_base as \n", "(\n", "    Select \n", "    brand_id, \n", "    date_, \n", "    frontend_merchant_id, \n", "    l0_category_id, \n", "    l1_category_id, \n", "    l2_category_id, \n", "    manufacturer_id, \n", "    product_id, \n", "    product_type_id,\n", "    carts\n", "    \n", "    from cd_etls.cd_product_store_dashboard_sales_v1\n", "    where\n", "    date_ >= current_date - interval '30' day\n", "    and manufacturer_id !='all'\n", "  \n", "    union all \n", "      \n", "    Select \n", "    \n", "    brand_id, \n", "    date_, \n", "    frontend_merchant_id, \n", "    l0_category_id, \n", "    l1_category_id, \n", "    l2_category_id, \n", "    'overall' as manufacturer_id, \n", "    product_id, \n", "    product_type_id,\n", "    carts\n", "    \n", "    from cd_etls.cd_product_store_dashboard_sales\n", "    where date_ >= current_date - interval '30' day\n", ") \n", "\n", "\n", ",complaints_base as\n", "(\n", "    Select \n", "    brand_id, \n", "    date_, \n", "     case\n", "                   when complaint_type in ('COMPLAINT_MISSING_ITEM',\n", "                                           'COMPLAINT_PARTIAL_ITEM_MISSING',\n", "                                           'COMPLAINT_FREEBIE_MISSING',\n", "                                           'COMPLAINT_QUANTITY_MISMATCH',\n", "                                           'COMPLAINT_MISSING_FREE_GIFT',\n", "                                           'COMPLAINT_MISSING_PART') then 'ITEM MISSING'\n", "                   when complaint_type in ('COMPLAINT_DAMAGED_ITEM',\n", "                                           'COMPLAINT_SPILLED_ITEM',\n", "                                           'COMPLAINT_SEAL_TAMPERED',\n", "                                           'COMPLAINT_MELTED_ISSUE',\n", "                                           'COMPLAINT_TORN_ITEM',\n", "                                           'COMPLAINT_PACKAGING_ISSUE',\n", "                                           'COMPLAINT_LEAKAGE_ISSUE',\n", "                                           'COMPLAINT_DAMAGED_FREE_GIFT') then 'ITEM DAMAGED'\n", "                   when complaint_type in ('COMPLAINT_EXPIRED_ITEM',\n", "                                           'COMPLAINT_NEAR_EXPIRY') then 'ITEM EXPIRED'\n", "                   when complaint_type in ('COMPLAINT_FAULTY_ITEM',\n", "                                           'COMPLAINT_AUDIO_ISSUE',\n", "                                           'COMPLAINT_BLUETOOTH_ISSUE') then 'ITEM FAULTY'\n", "                   when complaint_type in ('COMPLAINT_QUALITY_ISSUE',\n", "                                           'COMPLAINT_ROTTEN_ITEM',\n", "                                           'COMPLAINT_SMELL_OR_TASTE_ISSUE',\n", "                                           'COMPLAINT_PRINT_QUALITY_ISSUE',\n", "                                           'COMPLAINT_PAPER_QUALITY_ISSUE',\n", "                                           'COMPLAINT_FAKE_OR_DUPLICATE_ITEM',\n", "                                           'COMPLAINT_QUALITY_ISSUE_FREE_GIFT') then 'QUALITY NOT GOOD'\n", "                   when complaint_type in ('COMPLAINT_WRONG_ITEM',\n", "                                           'COMPLAINT_EXPECTATION_MISMATCH',\n", "                                           'COMPLAINT_ORDERED_BY_MISTAKE',\n", "                                           'COMPLAINT_INCORRECT_PAGE',\n", "                                           'COMPLAINT_COLOR_ISSUE',\n", "                                           'COMPLAINT_PRICE_MISMATCH',\n", "                                           'COMPLAINT_ORIENTATION_MISMATCH',\n", "                                           'COMPLAINT_WRONG_VARIATION',\n", "                                           'COMPLAINT_EXCEPTIONAL_RESOLUTION') then 'WRONG ITEM'\n", "                   when complaint_type in ('COMPLAINT_GIFT_PACKING_CHARGE') then 'OTHER CHARGES'\n", "                   when complaint_type in ('COMPLAINT_SIZE_ISSUE') then 'SIZE ISSUE'\n", "                   ELSE COMPLAINT_TYPE\n", "               end AS complaint_type,\n", "    frontend_merchant_id, \n", "    l0_category_id, \n", "    l1_category_id, \n", "    l2_category_id, \n", "    manufacturer_id, \n", "    product_id, \n", "    product_type_id, \n", "    complaint_carts,\n", "    resolved_carts\n", "\n", "    from cd_etls.cd_product_store_dashboard_complaints_v1\n", "    where complaint_type<>'all' \n", "    and manufacturer_id !='all'\n", "    and date_ >= current_date - interval '30' day \n", "\n", "    union all\n", "    \n", "    Select \n", "    brand_id, \n", "    date_, \n", "     case\n", "                   when complaint_type in ('COMPLAINT_MISSING_ITEM',\n", "                                           'COMPLAINT_PARTIAL_ITEM_MISSING',\n", "                                           'COMPLAINT_FREEBIE_MISSING',\n", "                                           'COMPLAINT_QUANTITY_MISMATCH',\n", "                                           'COMPLAINT_MISSING_FREE_GIFT',\n", "                                           'COMPLAINT_MISSING_PART') then 'ITEM MISSING'\n", "                   when complaint_type in ('COMPLAINT_DAMAGED_ITEM',\n", "                                           'COMPLAINT_SPILLED_ITEM',\n", "                                           'COMPLAINT_SEAL_TAMPERED',\n", "                                           'COMPLAINT_MELTED_ISSUE',\n", "                                           'COMPLAINT_TORN_ITEM',\n", "                                           'COMPLAINT_PACKAGING_ISSUE',\n", "                                           'COMPLAINT_LEAKAGE_ISSUE',\n", "                                           'COMPLAINT_DAMAGED_FREE_GIFT') then 'ITEM DAMAGED'\n", "                   when complaint_type in ('COMPLAINT_EXPIRED_ITEM',\n", "                                           'COMPLAINT_NEAR_EXPIRY') then 'ITEM EXPIRED'\n", "                   when complaint_type in ('COMPLAINT_FAULTY_ITEM',\n", "                                           'COMPLAINT_AUDIO_ISSUE',\n", "                                           'COMPLAINT_BLUETOOTH_ISSUE') then 'ITEM FAULTY'\n", "                   when complaint_type in ('COMPLAINT_QUALITY_ISSUE',\n", "                                           'COMPLAINT_ROTTEN_ITEM',\n", "                                           'COMPLAINT_SMELL_OR_TASTE_ISSUE',\n", "                                           'COMPLAINT_PRINT_QUALITY_ISSUE',\n", "                                           'COMPLAINT_PAPER_QUALITY_ISSUE',\n", "                                           'COMPLAINT_FAKE_OR_DUPLICATE_ITEM',\n", "                                           'COMPLAINT_QUALITY_ISSUE_FREE_GIFT') then 'QUALITY NOT GOOD'\n", "                   when complaint_type in ('COMPLAINT_WRONG_ITEM',\n", "                                           'COMPLAINT_EXPECTATION_MISMATCH',\n", "                                           'COMPLAINT_ORDERED_BY_MISTAKE',\n", "                                           'COMPLAINT_INCORRECT_PAGE',\n", "                                           'COMPLAINT_COLOR_ISSUE',\n", "                                           'COMPLAINT_PRICE_MISMATCH',\n", "                                           'COMPLAINT_ORIENTATION_MISMATCH',\n", "                                           'COMPLAINT_WRONG_VARIATION',\n", "                                           'COMPLAINT_EXCEPTIONAL_RESOLUTION') then 'WRONG ITEM'\n", "                   when complaint_type in ('COMPLAINT_GIFT_PACKING_CHARGE') then 'OTHER CHARGES'\n", "                   when complaint_type in ('COMPLAINT_SIZE_ISSUE') then 'SIZE ISSUE'\n", "                   ELSE COMPLAINT_TYPE\n", "               end AS complaint_type,\n", "    frontend_merchant_id, \n", "    l0_category_id, \n", "    l1_category_id, \n", "    l2_category_id, \n", "    'overall' as manufacturer_id,\n", "    product_id, \n", "    product_type_id, \n", "    complaint_carts,\n", "    resolved_carts\n", "    \n", "    from cd_etls.cd_product_store_dashboard_complaints\n", "    where complaint_type<>'all' \n", "    and date_ >= current_date - interval '30' day \n", "    \n", "),\n", "\n", "\n", "store_base as (\n", "Select \n", "    frontend_merchant_id,\n", "    frontend_merchant_name,\n", "    frontend_merchant_city_name\n", "from dwh.dim_merchant_outlet_facility_mapping\n", "where is_current\n", "and is_current_mapping_active)\n", "\n", "    ----------------------------------------------------------------------------------------------------------------------------------------------------------\n", ",pid_sales_base_1 as (\n", "    Select \n", "        DATE_TRUNC('week',date_)  as week,\n", "        extract(week from date_) as week_no,\n", "        ds.product_id,\n", "        ds.l0_category_id,\n", "        ds.l1_category_id,\n", "        ds.l2_category_id,\n", "        ds.brand_id,\n", "        ds.manufacturer_id,\n", "        ds.product_type_id,\n", "        ds.frontend_merchant_id,\n", "        frontend_merchant_city_name as city,\n", "        carts carts\n", "    from sales_base ds \n", "    left join store_base sb \n", "    on ds.frontend_merchant_id=sb.frontend_merchant_id\n", "    where ds.product_id!='all'\n", "    )\n", "    \n", ",pid_sales_base as (\n", "    Select \n", "        week,\n", "        week_no,\n", "        product_id,\n", "        l0_category_id,\n", "        l1_category_id,\n", "        l2_category_id,\n", "        brand_id,\n", "        manufacturer_id,\n", "        product_type_id,\n", "        coalesce(frontend_merchant_id,0) as frontend_merchant_id,\n", "        coalesce(city, 'Overall') as city,\n", "        sum(carts) carts\n", "    from pid_sales_base_1\n", "     group by grouping sets (\n", "     (week, week_no, manufacturer_id, product_id,l0_category_id, l1_category_id, l2_category_id,  brand_id, product_type_id, frontend_merchant_id, city),\n", "     (week, week_no, manufacturer_id, product_id,l0_category_id, l1_category_id, l2_category_id, brand_id, product_type_id),\n", "     (week, week_no, manufacturer_id, product_id,l0_category_id, l1_category_id, l2_category_id, brand_id, product_type_id, city))\n", "    )\n", "    \n", "--select * from pid_sales_base where product_id='127083' and week_no=40\n", ",pid_sales_base_final as (\n", "    Select \n", "        week,\n", "        week_no,\n", "        product_id,\n", "        l0_category_id,\n", "        l1_category_id,\n", "        manufacturer_id,\n", "        l2_category_id,\n", "        brand_id,\n", "        product_type_id,\n", "        frontend_merchant_id,\n", "        city,\n", "        carts,\n", "        lag(carts,1) over (partition by product_id, frontend_merchant_id, manufacturer_id, city order by week_no) as prev_week_pid_carts\n", "    from pid_sales_base )\n", "    \n", "    \n", "    -- select *\n", "    -- from pid_sales_base_final\n", "    -- where week = date'2025-06-02'\n", "    -- and product_id = '128661'\n", "    \n", "\n", ",pid_complaints_base_1 as (\n", "    Select  \n", "        DATE_TRUNC('week',date_)  as week,\n", "        extract(week from date_) as week_no,\n", "        product_id,\n", "        l2_category_id,\n", "        complaint_type,\n", "        manufacturer_id,\n", "        frontend_merchant_city_name as city\n", "        ,ds.frontend_merchant_id\n", "        , complaint_carts\n", "        ,resolved_carts\n", "      \n", "    from complaints_base ds \n", "    left join store_base sb \n", "    on ds.frontend_merchant_id=sb.frontend_merchant_id\n", "    where ds.product_id!='all'\n", "    )\n", "    \n", "    ,pid_complaints_base as (\n", "    Select  \n", "        week,\n", "        week_no,\n", "        product_id,\n", "        l2_category_id,\n", "        complaint_type,\n", "        manufacturer_id,\n", "        coalesce(city, 'Overall') as city\n", "        ,coalesce(frontend_merchant_id,0) as frontend_merchant_id\n", "        ,sum(complaint_carts) complaint_carts\n", "        ,sum(resolved_carts) resolved_carts\n", "       \n", "    from pid_complaints_base_1 \n", "    group by grouping sets \n", "    ((week, week_no, manufacturer_id, product_id, l2_category_id, complaint_type, frontend_merchant_id, city),\n", "    (week, week_no, manufacturer_id, product_id, l2_category_id,  complaint_type),\n", "    (week, week_no, manufacturer_id,  product_id, l2_category_id, complaint_type, city)\n", "    )\n", "    )\n", "\n", "\n", "    \n", ", pid_base as (\n", "    Select \n", "    \n", "        ps.week,\n", "        ps.week_no,\n", "        ps.product_id,\n", "        ps.l0_category_id,\n", "        ps.l1_category_id,\n", "        ps.manufacturer_id,\n", "        ps.l2_category_id,\n", "        ps.brand_id,\n", "        ps.product_type_id,\n", "        ps.frontend_merchant_id,\n", "        ps.city,\n", "        ps.carts,\n", "        ps.prev_week_pid_carts,\n", "        complaint_type,\n", "        coalesce(complaint_carts,0) complaint_carts,\n", "        coalesce(resolved_carts,0) resolved_carts\n", "   \n", "    from pid_sales_base_final ps \n", "        left join pid_complaints_base pc \n", "            on ps.product_id=pc.product_id\n", "            and ps.l2_category_id=pc.l2_category_id\n", "            AND ps.week_no=pc.week_no\n", "            and ps.frontend_merchant_id=pc.frontend_merchant_id\n", "            and ps.city=pc.city\n", "            and ps.manufacturer_id=pc.manufacturer_id\n", "            \n", "    )\n", "\n", "    -- select distinct *\n", "    -- from pid_base \n", "    -- where week = date'2025-06-02'\t\n", "    -- and product_id= '128661'\n", "    -- and manufacturer_id='overall'\n", "    -- and city= 'Overall'\n", "    -- and frontend_merchant_id = 0\n", "    \n", "    , Product_Weekly_Data AS (\n", "    SELECT\n", "        product_id,\n", "        week_no,\n", "        manufacturer_id,\n", "        complaint_type,\n", "        city,\n", "        frontend_merchant_id,\n", "        SUM(carts) AS total_carts,\n", "        SUM(complaint_carts) AS total_complaint_carts\n", "    FROM pid_base\n", "    GROUP BY product_id, week_no,manufacturer_id, complaint_type,5,6\n", "),\n", "\n", "\n", "\n", "pid_final_weekly as (\n", "SELECT\n", "    current_week.product_id as pid,\n", "    current_week.manufacturer_id as manufacturer_id, \n", "    current_week.week_no as wn,\n", "    current_week.complaint_type,\n", "    current_week.city,\n", "    current_week.frontend_merchant_id,\n", "    current_week.total_carts AS current_week_carts,\n", "    current_week.total_complaint_carts AS current_week_complaint_carts,\n", "    previous_week.week_no AS prev_week_no,\n", "    previous_week.total_carts AS prev_week_carts,\n", "    previous_week.total_complaint_carts AS prev_week_complaint_carts\n", "FROM Product_Weekly_Data AS current_week\n", "LEFT JOIN Product_Weekly_Data AS previous_week\n", "ON current_week.product_id = previous_week.product_id\n", "and current_week.complaint_type=previous_week.complaint_type\n", "AND current_week.week_no = previous_week.week_no + 1\n", "and current_week.city=previous_week.city\n", "and current_week.frontend_merchant_id= previous_week.frontend_merchant_id\n", "and current_week.manufacturer_id= previous_week.manufacturer_id\n", ")\n", "\n", "    \n", "    ,pid_final_base as (\n", "    Select \n", "        a.week,\n", "        a.week_no,\n", "        a.product_id,\n", "        a.city,\n", "        a.manufacturer_id,\n", "        a.frontend_merchant_id,\n", "        a.l0_category_id,\n", "        a.l1_category_id,\n", "        a.l2_category_id,\n", "        a.brand_id,\n", "        a.product_type_id,\n", "        a.carts,\n", "        a.prev_week_pid_carts,\n", "        current_week_carts,\n", "        current_week_complaint_carts,\n", "        prev_week_no,\n", "        prev_week_carts,\n", "        prev_week_complaint_carts,\n", "        a.complaint_type,\n", "        a.complaint_carts as pid_complaint_carts,\n", "        b.complaint_carts as prev_week_pid_complaints,\n", "        a.resolved_carts as pid_resolved_carts,\n", "        b.resolved_carts as prev_week_pid_resolved_carts\n", "\n", "       \n", "\n", "    from pid_base a left join pid_base b on a.product_id=b.product_id and a.frontend_merchant_id=b.frontend_merchant_id and a.city=b.city and a.week_no=b.week_no+1\n", "    and a.complaint_type=b.complaint_type and a.l2_category_id=b.l2_category_id and a.manufacturer_id = b.manufacturer_id\n", "    left join pid_final_weekly pfw on a.product_id=pfw.pid and a.week_no=pfw.wn and a.complaint_type=pfw.complaint_type and a.city=pfw.city and\n", "    a.frontend_merchant_id=pfw.frontend_merchant_id and a.manufacturer_id=pfw.manufacturer_id)\n", "\n", "\n", "\n", "    -- select distinct *\n", "    -- from pid_final_base \n", "    -- where week = date'2025-06-02'\t\n", "    -- and product_id= '128661'\n", "    -- and manufacturer_id='overall'\n", "    -- and city= 'Overall'\n", "    -- and frontend_merchant_id = 0\n", "\n", "\n", "\n", "-----------------------------------------------------------------------------------------------------------------------------------------------------------------\n", ",l2_sales_base as (\n", "    Select \n", "\n", "        ds.l2_category_id,\n", "      DATE_TRUNC('week',date_) as week,\n", "        extract(week from date_) as week_no,\n", "        manufacturer_id,\n", "       -- 0 as frontend_merchant_id,\n", "       frontend_merchant_id,\n", "        sum(carts) carts\n", "\n", "    from sales_base ds \n", "    where ds.l2_category_id!='all' \n", "    and ds.brand_id='all'\n", "    and ds.product_type_id='all'\n", "    and product_id='all'\n", "    group by 1,2,3,4,5\n", "    \n", "    union\n", "    \n", "    Select \n", "\n", "        ds.l2_category_id,\n", "        DATE_TRUNC('week',date_)  as week,\n", "        extract(week from date_) as week_no,\n", "        manufacturer_id,\n", "        0 as frontend_merchant_id,\n", "       --frontend_merchant_id,\n", "        sum(carts) carts\n", "    from sales_base ds \n", "    where ds.l2_category_id!='all' \n", "    and ds.brand_id='all'\n", "    and ds.product_type_id='all'\n", "    and product_id='all'\n", "    group by 1,2,3,4,5\n", "    )\n", "\n", "\n", ",l2_sales_base_final as (\n", "    Select \n", "\n", "        l2_category_id,\n", "        week,\n", "        week_no,\n", "        manufacturer_id,\n", "        frontend_merchant_id,\n", "        carts l2_carts,\n", "        lag(carts,1) over (partition by l2_category_id,frontend_merchant_id, manufacturer_id order by week_no) as prev_week_l2_carts\n", "    from l2_sales_base ds \n", "    )\n", "\n", "--select * from l2_sales_base_final where l2_category_id='2117'\n", "\n", "\n", "    ,l2_complaints_base as (\n", "    Select  \n", "        ds.frontend_merchant_id,\n", "       -- 0 as frontend_merchant_id,\n", "        l2_category_id,\n", "        manufacturer_id,\n", "        DATE_TRUNC('week',date_) as week,\n", "         extract(week from date_) as week_no,\n", "        complaint_type,\n", "        sum(complaint_carts) complaint_carts\n", "        ,sum(resolved_carts) resolved_carts\n", "     \n", "    from complaints_base ds \n", "    where ds.l2_category_id!='all'\n", "    and ds.brand_id='all'\n", "    and ds.product_type_id='all'\n", "    and product_id='all'\n", "    group by 1,2,3,4,5,6\n", "    \n", "    union\n", "    \n", "    Select  \n", "       -- ds.frontend_merchant_id,\n", "        0 as frontend_merchant_id,\n", "        l2_category_id,\n", "        manufacturer_id,\n", "        \n", "      \n", "        DATE_TRUNC('week',date_) as week, \n", "        extract(week from date_) as week_no,\n", "        complaint_type,\n", "        sum(complaint_carts) complaint_carts\n", "        ,sum(resolved_carts) resolved_carts\n", "      \n", "    from complaints_base ds \n", "    where ds.l2_category_id!='all'\n", "    and ds.brand_id='all'\n", "    and ds.product_type_id='all'\n", "    and product_id='all'\n", "    group by 1,2,3,4,5,6\n", "    )\n", "    \n", "    \n", "    \n", "    \n", "    \n", "    \n", "--  Select *\n", "--     from l2_complaints_base \n", "--     where l2_category_id='418'\n", "--     and week = date'2025-06-02'\t\n", "--     and frontend_merchant_id=0\n", "--     and manufacturer_id= 'overall'\n", "        \n", "    \n", "    \n", "    \n", "    \n", "\n", "    , l2_base as (\n", "    Select \n", "        l2sb.l2_category_id,\n", "        l2sb.week,\n", "        l2sb.week_no,\n", "        l2sb.manufacturer_id,\n", "        l2sb.frontend_merchant_id,\n", "        l2sb.l2_carts,\n", "        l2sb.prev_week_l2_carts,\n", "        complaint_type,\n", "        coalesce(complaint_carts,0) l2_complaint_carts,\n", "        coalesce(resolved_carts,0) l2_resolved_carts\n", "       \n", "    from l2_sales_base_final l2sb\n", "        left join l2_complaints_base l2cb\n", "            on l2sb.L2_category_id=l2cb.L2_category_id\n", "            and l2sb.week_no=l2cb.week_no\n", "            and l2sb.frontend_merchant_id=l2cb.frontend_merchant_id\n", "            and l2sb.manufacturer_id= l2cb.manufacturer_id\n", "            \n", "    )\n", "    \n", "\n", ",l2_final_base as (\n", "    Select \n", "        a.week_no,\n", "        a.week,\n", "        a.frontend_merchant_id,\n", "        a.l2_category_id,\n", "        a.manufacturer_id,\n", "        a.l2_carts,\n", "        a.prev_week_l2_carts,\n", "\n", "        a.complaint_type,\n", "        a.l2_complaint_carts,\n", "        b.l2_complaint_carts as prev_week_l2_complaints,\n", "\n", "        a.l2_resolved_carts,\n", "        b.l2_resolved_carts as prev_week_l2_resolved_carts\n", "\n", "    from l2_base a left join l2_base b on a.l2_category_id=b.l2_category_id and a.frontend_merchant_id=b.frontend_merchant_id and a.complaint_type=b.complaint_type\n", "    and a.week_no =b.week_no+1 and a.manufacturer_id=b.manufacturer_id)\n", "    \n", "    --------------------------------------------------------------------------------------------------------------------\n", "\n", "    ,l1_sales_base as (\n", "    Select \n", "        l1_category_id,\n", "        DATE_TRUNC('week',date_)  as week, \n", "        manufacturer_id,\n", "        extract(week from date_) as week_no,\n", "        --0 as frontend_merchant_id,\n", "        frontend_merchant_id,\n", "         sum(carts) carts\n", "\n", "    from sales_base ds \n", "    where ds.l2_category_id='all' \n", "    and ds.brand_id='all'\n", "    and ds.product_type_id='all'\n", "    and product_id='all'\n", "    and l1_category_id!='all'\n", "    group by 1,2,3,4,5\n", "    \n", "    union\n", "    \n", "     Select \n", "        l1_category_id,\n", "        DATE_TRUNC('week',date_) as week,\n", "        manufacturer_id,\n", "         extract(week from date_) as week_no,\n", "        0 as frontend_merchant_id,\n", "         sum(carts) carts\n", "    from sales_base ds \n", "    where ds.l2_category_id='all' \n", "    and ds.brand_id='all'\n", "    and ds.product_type_id='all'\n", "    and product_id='all'\n", "    and l1_category_id!='all'\n", "    group by 1,2,3,4,5\n", "    )\n", "    \n", "    ,l1_sales_base_final as (\n", "    Select \n", "        l1_category_id,\n", "        week,\n", "        week_no,\n", "        manufacturer_id,\n", "        frontend_merchant_id,\n", "        carts l1_carts,\n", "        lag(carts,1) over (partition by l1_category_id,frontend_merchant_id, manufacturer_id order by week_no) as prev_week_l1_carts\n", "    from l1_sales_base ds \n", "    )\n", "\n", "\n", "    ,l1_complaints_base as (\n", "    Select  \n", "       l1_category_id,\n", "       DATE_TRUNC('week',date_) as week,\n", "       extract(week from date_) as week_no,\n", "       manufacturer_id,\n", "       -- 0 as frontend_merchant_id\n", "        frontend_merchant_id\n", "        ,complaint_type\n", "        ,sum(complaint_carts) complaint_carts\n", "        ,sum(resolved_carts) resolved_carts\n", "    from complaints_base ds \n", "    where ds.l2_category_id='all' \n", "    and ds.brand_id='all'\n", "    and ds.product_type_id='all'\n", "    and product_id='all'\n", "    and l1_category_id!='all'\n", "    group by 1,2,3,4,5,6\n", "    \n", "    union\n", "    \n", "    Select  \n", "       l1_category_id,\n", "        DATE_TRUNC('week',date_) as week,\n", "       extract(week from date_) as week_no,\n", "       manufacturer_id,\n", "        0 as frontend_merchant_id\n", "        --frontend_merchant_id\n", "        ,complaint_type\n", "        ,sum(complaint_carts) complaint_carts\n", "        ,sum(resolved_carts) resolved_carts\n", "  \n", "    from complaints_base ds \n", "    where ds.l2_category_id='all' \n", "    and ds.brand_id='all'\n", "    and ds.product_type_id='all'\n", "    and product_id='all'\n", "    and l1_category_id!='all'\n", "    group by 1,2,3,4,5,6\n", "    )\n", "\n", "    , l1_base as (\n", "    Select \n", "        l1sb.l1_category_id,\n", "        l1sb.week,\n", "        l1sb.week_no,\n", "        l1sb.manufacturer_id,\n", "        l1sb.frontend_merchant_id,\n", "        l1sb.l1_carts,\n", "        l1sb.prev_week_l1_carts,\n", "        \n", "        complaint_type,\n", "        coalesce(complaint_carts,0) l1_complaint_carts,\n", "        coalesce(resolved_carts,0) l1_resolved_carts\n", "    \n", "    from l1_sales_base_final l1sb\n", "        left join l1_complaints_base l1cb\n", "            on l1sb.l1_category_id=l1cb.l1_category_id\n", "            and l1sb.week_no=l1cb.week_no\n", "            and l1sb.frontend_merchant_id=l1cb.frontend_merchant_id\n", "            and l1sb.manufacturer_id=l1cb.manufacturer_id\n", "    )\n", "\n", "\n", "\n", "\n", "\n", "    ,l1_final_base as (\n", "    Select \n", "        a.week_no,\n", "        a.week,\n", "        a.frontend_merchant_id,\n", "        a.l1_category_id,\n", "        a.manufacturer_id,\n", "        a.l1_carts,\n", "        a.prev_week_l1_carts,\n", "\n", "        a.complaint_type,\n", "        a.l1_complaint_carts,\n", "        b.l1_complaint_carts as prev_week_l1_complaints,\n", "\n", "        a.l1_resolved_carts,\n", "        b.l1_resolved_carts as prev_week_l1_resolved_carts\n", "\n", "     \n", "    from l1_base a left join l1_base b on  a.l1_category_id=b.l1_category_id and a.frontend_merchant_id=b.frontend_merchant_id and a.complaint_type=b.complaint_type\n", "    and a.week_no=b.week_no+1 and a.manufacturer_id=b.manufacturer_id)\n", "\n", "-- select * from l1_final_base where l1_category_id='1387'\n", "\n", "    -------------------------------------------------------------------------------------------------------------------\n", "\n", "    ,l0_sales_base as (\n", "    Select \n", "        -- date_,\n", "         ds.frontend_merchant_id,\n", "         DATE_TRUNC('week',date_) as week,\n", "         extract(week from date_) as week_no,\n", "         manufacturer_id,\n", "         l0_category_id,\n", "\n", "        sum(carts) carts\n", "        \n", "    from sales_base ds \n", "    where ds.l1_category_id='all' \n", "    and ds.brand_id='all'\n", "    and ds.product_type_id='all'\n", "    and product_id='all'\n", "    and l0_category_id!='all'\n", "    group by 1,2,3,4,5\n", "    \n", "    union\n", "    Select \n", "       \n", "         0 as frontend_merchant_id,\n", "       DATE_TRUNC('week',date_) as week,\n", "       extract(week from date_) as week_no,\n", "       manufacturer_id,\n", "         l0_category_id,\n", "        sum(carts) carts\n", "   \n", "    from sales_base ds \n", "    where ds.l1_category_id='all' \n", "    and ds.brand_id='all'\n", "    and ds.product_type_id='all'\n", "    and product_id='all'\n", "    and l0_category_id!='all'\n", "    group by 1,2,3,4,5\n", "    )\n", "    \n", "    ,l0_sales_base_final as (\n", "    Select \n", "        -- date_,\n", "        -- ds.frontend_merchant_id,\n", "         week,\n", "        week_no,\n", "        manufacturer_id,\n", "         l0_category_id,\n", "         frontend_merchant_id,\n", "        carts as l0_carts,\n", "        lag(carts,1) over (partition by l0_category_id,frontend_merchant_id,manufacturer_id order by week_no) as prev_week_l0_carts\n", "    from l0_sales_base ds \n", "    )\n", "\n", "\n", "    ,l0_complaints_base as (\n", "    Select  \n", "        -- date_,\n", "         ds.frontend_merchant_id,\n", "         DATE_TRUNC('week',date_) as week,         \n", "         extract(week from date_) as week_no,\n", "         manufacturer_id,\n", "         \n", "         l0_category_id\n", "         ,complaint_type\n", "        ,sum(complaint_carts) complaint_carts\n", "        ,sum(resolved_carts) resolved_carts\n", "    from complaints_base ds \n", "    where ds.l1_category_id='all' \n", "    and ds.brand_id='all'\n", "    and ds.product_type_id='all'\n", "    and product_id='all'\n", "    and l0_category_id!='all'\n", "    group by 1,2,3,4,5,6\n", "    \n", "    union\n", "    \n", "    Select  \n", "         0 as frontend_merchant_id,\n", "        DATE_TRUNC('week',date_) as week,  \n", "        extract(week from date_) as week_no,\n", "        manufacturer_id,\n", "        l0_category_id\n", "        ,complaint_type\n", "        ,sum(complaint_carts) complaint_carts\n", "        ,sum(resolved_carts) resolved_carts\n", "    from complaints_base ds \n", "    where ds.l1_category_id='all' \n", "    and ds.brand_id='all'\n", "    and ds.product_type_id='all'\n", "    and product_id='all'\n", "    and l0_category_id!='all'\n", "    group by 1,2,3,4,5,6\n", "    )\n", "\n", "\n", "    , l0_base as (\n", "    Select \n", "    \n", "        l0sb.week,\n", "        l0sb.week_no,\n", "        l0sb.manufacturer_id,\n", "         l0sb.l0_category_id,\n", "         l0sb.frontend_merchant_id,\n", "        l0sb.l0_carts,\n", "        l0sb.prev_week_l0_carts,\n", " \n", "        complaint_type,\n", "        coalesce(complaint_carts,0) l0_complaint_carts,\n", "        coalesce(resolved_carts,0) l0_resolved_carts\n", "        \n", "    from  l0_sales_base_final l0sb\n", "        left join l0_complaints_base l0cb\n", "            on l0sb.L0_category_id=l0cb.L0_category_id\n", "            and l0sb.week_no=l0cb.week_no\n", "            and l0sb.frontend_merchant_id=l0cb.frontend_merchant_id\n", "            and l0sb.manufacturer_id=l0cb.manufacturer_id\n", "    )\n", "\n", "\n", "\n", "\n", "    ,l0_final_base as (\n", "    Select \n", "        a.week_no,\n", "        a.week,\n", "        a.frontend_merchant_id,\n", "        a.l0_category_id,\n", "        a.manufacturer_id,\n", "        a.l0_carts,\n", "        a.prev_week_l0_carts,\n", "        a.complaint_type,\n", "        a.l0_complaint_carts,\n", "       b.l0_complaint_carts as prev_week_l0_complaints,\n", "\n", "        a.l0_resolved_carts,\n", "        b.l0_resolved_carts as prev_week_l0_resolved_carts\n", "\n", "    from l0_base a left join l0_base b on a.l0_category_id=b.l0_category_id  and a.frontend_merchant_id=b.frontend_merchant_id and a.complaint_type=b.complaint_type and \n", "    a.week_no=b.week_no+1 and a.manufacturer_id=b.manufacturer_id)\n", "\n", "-- select * from l0_final_base where l0_category_id='5159'\n", "\n", "\n", "\n", "\n", ",new_product_base as (select product_id, 1 as flag, min(order_create_dt_ist) as launch_date from dwh.fact_sales_order_item_details where order_create_dt_ist\n", ">= current_date - interval '180' day group by 1,2\n", "having DATE_DIFF('day', MIN(order_create_dt_ist), CURRENT_DATE) <= 14)\n", "\n", "\n", "\n", "   (\n", "    Select distinct  \n", "      \n", "        p.week,\n", "        p.week_no,\n", "        p.product_id,\n", "        p.city,\n", "        p.manufacturer_id,\n", "        p.frontend_merchant_id,\n", "        p.l0_category_id,\n", "        p.l1_category_id,\n", "        p.l2_category_id,\n", "        p.brand_id,\n", "        p.product_type_id,\n", "        p.carts,\n", "        p.prev_week_pid_carts,\n", "        p.current_week_carts,\n", "        p.current_week_complaint_carts,\n", "        p.prev_week_no,\n", "        p.prev_week_carts,\n", "        p.prev_week_complaint_carts,\n", "        p.complaint_type,\n", "        p.pid_complaint_carts,\n", "        p.prev_week_pid_complaints,\n", "        p.pid_resolved_carts,\n", "        p.prev_week_pid_resolved_carts,\n", "        coalesce(b.flag,0) as new_product_flag,\n", "  \n", "      \n", "        l0_carts,\n", "        prev_week_l0_carts,\n", "        l0_complaint_carts,\n", "        prev_week_l0_complaints,\n", "        l0_resolved_carts,\n", "        prev_week_l0_resolved_carts,\n", "\n", "\n", "        l1_carts,\n", "        prev_week_l1_carts,\n", "        l1_complaint_carts,\n", "        prev_week_l1_complaints,\n", "        l1_resolved_carts,\n", "        prev_week_l1_resolved_carts,\n", "\n", "\n", "        l2_carts,\n", "        prev_week_l2_carts,\n", "        l2_complaint_carts,\n", "        prev_week_l2_complaints,\n", "        l2_resolved_carts,\n", "        prev_week_l2_resolved_carts,\n", "        current_date as snapshot_date\n", "\n", "\n", "    from pid_final_base p \n", "        left join l0_final_base l0\n", "            on p.week_no=l0.week_no\n", "            and p.l0_category_id=l0.l0_category_id\n", "            and p.frontend_merchant_id=l0.frontend_merchant_id\n", "            and p.complaint_type= l0.complaint_type\n", "            and p.manufacturer_id=l0.manufacturer_id\n", "        left join l2_final_base l2\n", "            on p.week_no=l2.week_no\n", "            and p.l2_category_id=l2.l2_category_id\n", "            and p.frontend_merchant_id=l2.frontend_merchant_id\n", "            and p.complaint_type= l2.complaint_type\n", "            and p.manufacturer_id=l2.manufacturer_id\n", "        left join l1_final_base l1\n", "            on p.week_no=l1.week_no\n", "            and p.l1_category_id=l1.l1_category_id\n", "            and p.frontend_merchant_id=l1.frontend_merchant_id\n", "            and p.complaint_type= l1.complaint_type\n", "            and p.manufacturer_id=l1.manufacturer_id\n", "            \n", "      left join new_product_base b on p.product_id=cast(b.product_id as varchar)\n", ")\n", "\n", "\n", "\n", "\n", "\n", "    \"\"\"\n", "\n", "pb.to_trino(data_obj=query, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "78218865-3c49-4c03-8ab6-a70be37091cf", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "56c6640b-f899-4f16-b624-c0ae2c3209f3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ca907a98-f613-4ef8-a1af-9a2496f2e23e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}