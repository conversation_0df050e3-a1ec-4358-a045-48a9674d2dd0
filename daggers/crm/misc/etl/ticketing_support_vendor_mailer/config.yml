alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: ticketing_support_vendor_mailer
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: crm
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03S5ACSW3C
path: crm/misc/etl/ticketing_support_vendor_mailer
paused: false
pool: crm_pool
project_name: misc
schedule:
  end_date: '2025-07-28T00:00:00'
  interval: 0 5 * * *
  start_date: '2024-10-25T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
