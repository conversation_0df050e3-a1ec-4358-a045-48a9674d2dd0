alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: item_missing_audit
dag_type: etl
escalation_priority: low
execution_timeout: 120
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: crm
notebook:
  parameters: null
  retries: 3
  retry_delay_in_seconds: 60
owner:
  email: <EMAIL>
  slack_id: U044TS954HY
path: crm/misc/etl/item_missing_audit
paused: false
pool: crm_pool
project_name: misc
schedule:
  end_date: '2025-08-20T00:00:00'
  interval: 30 1-18/2 * * *
  start_date: '2025-06-20T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 22
