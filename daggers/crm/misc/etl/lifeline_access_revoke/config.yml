alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: lifeline_access_revoke
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: crm
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U044TS954HY
path: crm/misc/etl/lifeline_access_revoke
paused: false
pool: crm_pool
project_name: misc
schedule:
  end_date: '2025-08-20T00:00:00'
  interval: 0 9 * * *
  start_date: '2025-06-09T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 20
