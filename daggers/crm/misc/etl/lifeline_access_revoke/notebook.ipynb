{"cells": [{"cell_type": "code", "execution_count": null, "id": "756ac419-ac6a-4d33-97c7-df96037f5145", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import requests\n", "import json"]}, {"cell_type": "code", "execution_count": null, "id": "fe262fca-dd2b-400b-9fce-42316293e902", "metadata": {}, "outputs": [], "source": ["PERMANENT_MEMBERS = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "28959778-b12b-4273-bb4d-895829e5eda0", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "\n", "with agent_id_base as\n", "(\n", "select \n", "   cl.agent_id,\n", "   max(at_date) as last_seen_on\n", "from\n", "lumber_events.crm_lifeline_api_event_logs cl \n", "where \n", "at_date >= cast((CURRENT_DATE - interval '180' day) as varchar) and\n", "lower(cl.source) in ('lifeline_dashboard', 'lifelinez', 'smartplug')\n", "and JSON_EXTRACT_SCALAR(meta,'$.user_source') = 'CRM_USER' and agent_id != 'None'\n", "GROUP BY 1\n", "HAVING DATE(MAX(at_date)) <= DATE(CURRENT_DATE) - interval '4' day \n", "),\n", "agent_emails as\n", "(\n", "    select external_user_id,\n", "    name,\n", "    email_id,\n", "    case when email_id like '%%@grofers%%' or email_id like '%%@zomato%%' then 'internal' else 'external' end as user_flag\n", "    from lake_crm.crm_user\n", "    where is_deleted = false\n", "),\n", "permission as\n", "(\n", "select user_id,\n", "permission_id,\n", "CASE WHEN install_ts <= current_date - interval '3' day then 1 else 0 end as grant_flag\n", "from crm.crm_user_permission\n", ")\n", "\n", "select ai.agent_id,\n", "ai.last_seen_on,\n", "ae.name,\n", "ae.email_id,\n", "user_flag\n", "from agent_id_base ai\n", "join agent_emails ae on cast(ai.agent_id as varchar) = cast(ae.external_user_id as varchar) \n", "join permission p on cast(ai.agent_id as varchar) = cast(p.user_id as varchar) \n", "where user_flag = 'external' AND grant_flag = 1\n", "group by 1,2,3,4,5\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "b798cac3-a3c9-4945-967a-a2116ee37ded", "metadata": {}, "outputs": [], "source": ["df_ext = pd.read_sql(query, con=pb.get_connection(\"[Warehouse] Trino\"))"]}, {"cell_type": "code", "execution_count": null, "id": "78552079-3ed9-468e-ba04-019cb23ea8db", "metadata": {}, "outputs": [], "source": ["if not df_ext.empty:\n", "    id_list_ext = df_ext.agent_id.to_list()\n", "else:\n", "    id_list_ext = []"]}, {"cell_type": "code", "execution_count": null, "id": "abe64bf0-cd63-4bf5-8e27-aed6cbc8eaf3", "metadata": {}, "outputs": [], "source": ["# for users in id_list_ext:\n", "# print(\"*\",users)"]}, {"cell_type": "code", "execution_count": null, "id": "62042c29-cc34-469a-a29a-232bcdeedb33", "metadata": {}, "outputs": [], "source": ["if not df_ext.empty:\n", "    agent_id_list_ext = df_ext.email_id.to_list()\n", "else:\n", "    agent_id_list_ext = []"]}, {"cell_type": "code", "execution_count": null, "id": "5f4ddbb1-e16a-42f0-b316-382eee4b17d4", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "\n", "with agent_id_base as\n", "(\n", "select\n", "   cl.agent_id,\n", "   max(at_date) as last_seen_on\n", "from\n", "lumber_events.crm_lifeline_api_event_logs cl\n", "where\n", "at_date >= cast((CURRENT_DATE - interval '180' day) as varchar) and\n", "lower(cl.source) in ('lifeline_dashboard', 'lifelinez', 'smartplug')\n", "and JSON_EXTRACT_SCALAR(meta,'$.user_source') = 'CRM_USER' and agent_id != 'None'\n", "GROUP BY 1\n", "HAVING DATE(MAX(at_date)) <= DATE(CURRENT_DATE) - interval '28' day\n", "),\n", "agent_emails as\n", "(\n", "    select external_user_id,\n", "    name,\n", "    email_id,\n", "    case when email_id like '%%@grofers%%' or email_id like '%%@zomato%%' then 'internal' else 'external' end as user_flag\n", "    from lake_crm.crm_user\n", "    where is_deleted = false\n", "),\n", "permission as\n", "(\n", "select user_id,\n", "permission_id,\n", "CASE WHEN install_ts <= current_date - interval '7' day then 1 else 0 end as grant_flag\n", "from crm.crm_user_permission\n", ")\n", "\n", "select ai.agent_id,\n", "ai.last_seen_on,\n", "ae.name,\n", "ae.email_id,\n", "user_flag\n", "from agent_id_base ai\n", "join agent_emails ae on cast(ai.agent_id as varchar) = cast(ae.external_user_id as varchar)\n", "join permission p on cast(ai.agent_id as varchar) = cast(p.user_id as varchar)\n", "where user_flag = 'internal' and email_id not in ('<EMAIL>','<EMAIL>','<EMAIL>',\n", "'<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>',\n", "'<EMAIL>','<EMAIL>','<EMAIL>',\n", "'nishtha.khand<PERSON><PERSON>@grofers.com','<EMAIL>','<EMAIL>',\n", "'<EMAIL>','<EMAIL>','kumar.g<PERSON><PERSON>@zomato.com','<EMAIL>','<EMAIL>','<EMAIL>') and grant_flag = 1\n", "group by 1,2,3,4,5\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "fe66ccf5-cff3-46d7-b7cd-0cbdedc7af30", "metadata": {}, "outputs": [], "source": ["df_int = pd.read_sql(query, con=pb.get_connection(\"[Warehouse] Trino\"))"]}, {"cell_type": "code", "execution_count": null, "id": "7d4144cb-8565-4cbc-ac8a-8be1ac550495", "metadata": {}, "outputs": [], "source": ["if not df_int.empty:\n", "    id_list_int = df_int.agent_id.to_list()\n", "else:\n", "    id_list_int = []"]}, {"cell_type": "code", "execution_count": null, "id": "468b3370-2497-4b67-affe-cf26a44fbe76", "metadata": {}, "outputs": [], "source": ["if not df_int.empty:\n", "    agent_id_list_int = df_int.email_id.to_list()\n", "else:\n", "    agent_id_list_int = []"]}, {"cell_type": "code", "execution_count": null, "id": "6b505344-b516-44b5-9569-6cbdf6ee7068", "metadata": {}, "outputs": [], "source": ["def revoke_lifeline_access(id_list):\n", "    # print(id_list)\n", "    url = \"https://crm-api-crm.prod-sgp-k8s.grofer.io/external_apis/bots/remove_lifeline_access\"\n", "    auth = pb.get_secret(\"dse/crm/api/self-auth\").get(\"TOKEN\")\n", "\n", "    # print(\"auth:\", auth)\n", "    payload = json.dumps({\"user_ids\": id_list, \"reason\": \"REASON_USER_INACTIVE\"})\n", "    headers = {\n", "        \"Authorization\": f\"Token {auth}\",\n", "        \"Content-Type\": \"application/json\",\n", "        \"source\": \"ACCESS_CLEANUP_DAG\",\n", "    }\n", "\n", "    response = requests.request(\"POST\", url, headers=headers, data=payload)\n", "    # print(url, response, payload)\n", "    return response.status_code"]}, {"cell_type": "code", "execution_count": null, "id": "8fa6946e-73c7-4c9d-868d-6d8a9a94ada9", "metadata": {}, "outputs": [], "source": ["revoke_lifeline_access(id_list_ext)"]}, {"cell_type": "code", "execution_count": null, "id": "2b2b39d1-0352-4af5-931f-42c6c21d2717", "metadata": {}, "outputs": [], "source": ["revoke_lifeline_access(id_list_int)"]}, {"cell_type": "code", "execution_count": null, "id": "05a0fe02-3de0-4e23-afa8-524d5991727b", "metadata": {}, "outputs": [], "source": ["def send_email(agent_list):\n", "\n", "    report_sub = f\"Lifeline Access Revoked\"\n", "\n", "    from_email = \"<EMAIL>\"\n", "    to_email = PERMANENT_MEMBERS\n", "    subject = report_sub\n", "    html_content = f\"Hi, <br> The access has been revoked for the following agents - <br> {agent_list} <br> Thanks\"\n", "    pb.send_email(\n", "        from_email=from_email,\n", "        to_email=to_email,\n", "        subject=subject,\n", "        html_content=html_content,\n", "        # cc=PERMANENT_MEMBERS,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "11933e6b-c720-40ea-ad54-db4bece8c9ff", "metadata": {}, "outputs": [], "source": ["send_email(agent_id_list_ext)"]}, {"cell_type": "code", "execution_count": null, "id": "64381e75-84cc-4520-bc22-8d3c47900c4b", "metadata": {}, "outputs": [], "source": ["# for i in agent_id_list_int:\n", "#     send_email([i])"]}, {"cell_type": "code", "execution_count": null, "id": "351e0cbc-38d2-43e7-8cea-51c0e884740e", "metadata": {}, "outputs": [], "source": ["send_email(agent_id_list_int)"]}, {"cell_type": "code", "execution_count": null, "id": "0518f168-ea22-4aa4-8bdd-0a7850721a9c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}