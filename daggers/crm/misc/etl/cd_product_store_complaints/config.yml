alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 3
dag_name: cd_product_store_complaints
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: crm
notebooks:
- alias: product_store_sales
  executor_config:
    load_type: tiny
    node_type: spot
  name: product_store_sales
  parameters: null
  retries: 3
  tag: group_01
- alias: product_complaints
  executor_config:
    load_type: tiny
    node_type: spot
  name: product_complaints
  parameters: null
  retries: 3
  tag: group_01
owner:
  email: <EMAIL>
  slack_id: S046Q8JF09M
path: crm/misc/etl/cd_product_store_complaints
paused: false
pool: crm_pool
project_name: misc
schedule:
  end_date: '2025-07-28T00:00:00'
  interval: 0 0 * * *
  start_date: '2025-01-07T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 29
