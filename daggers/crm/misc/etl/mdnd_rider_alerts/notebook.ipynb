{"cells": [{"cell_type": "code", "execution_count": null, "id": "1f576f33-9a3b-4f6c-b326-87c3ac00376f", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib==3.8\n", "import time"]}, {"cell_type": "code", "execution_count": null, "id": "a4642c27-c81c-434b-b826-72e97ccd3766", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "id": "d35929cb-afc9-40b4-b2ad-3d71844b7887", "metadata": {}, "outputs": [], "source": ["Query = \"\"\"\n", "with mdnd_base as\n", "(\n", "SELECT reso_date,\n", "order_id, \n", "sum(resol_amount) as resol_amount\n", "from cd_etls.item_wise_reso b\n", "where reso_date >= current_date - interval '1' day\n", "and complaint_type in ('COMPLAINT_ORDER_NOT_DELIVERED')\n", "group by 1,2\n", "), \n", "\n", "\n", "orders_data as\n", "(\n", "select \n", "reso_date,\n", "a.order_id, \n", "partner_id, \n", "order_checkout_ts_ist, \n", "resol_amount, \n", "a.outlet_id,\n", "lower(trim(c.name)) as Store_name\n", "\n", "from dwh.fact_supply_chain_order_details a\n", "inner join mdnd_base b on a.order_id = b.order_id\n", "left join retail.console_outlet c on a.outlet_id = c.id\n", "\n", "where order_checkout_dt_ist>= current_date - interval '4' day\n", "\n", "group by 1,2,3,4,5,6,7\n", ")\n", "\n", "\n", "select \n", "partner_id, \n", "outlet_id,\n", "Store_name,\n", "count(distinct order_id) as MDND_orders, \n", "sum(resol_amount) as Resolved_Amount\n", "\n", "\n", "from orders_data\n", "\n", "group by 1,2,3\n", "having count(distinct order_id)>3\n", "\n", "\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "8fff65a3-615c-40a7-a7c9-82c5f5fd119b", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "5f415e66-6bd7-4846-8833-31daf6b4e68a", "metadata": {}, "outputs": [], "source": ["df = pd.read_sql(Query, con)"]}, {"cell_type": "code", "execution_count": null, "id": "f0154c77-ff69-4423-8333-a084cf158dad", "metadata": {}, "outputs": [], "source": ["df_mdnd = df[[\"partner_id\", \"MDND_orders\", \"Resolved_Amount\", \"Store_name\", \"outlet_id\"]]\n", "df_mdnd"]}, {"cell_type": "code", "execution_count": null, "id": "e446a5a3-279e-42aa-a837-de25a74bd9ff", "metadata": {}, "outputs": [], "source": ["index_dict = {}"]}, {"cell_type": "code", "execution_count": null, "id": "410760bf-d58c-4c22-93f1-7819c518b2e6", "metadata": {}, "outputs": [], "source": ["def render_mpl_table(\n", "    data,\n", "    index_dict,\n", "    col_width=8.0,\n", "    row_height=1.225,\n", "    font_size=25,\n", "    header_color=\"#E96125\",\n", "    # other_color=\"#E96125\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs,\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array(\n", "            [col_width * 0.7, row_height]\n", "        )\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, cellLoc=\"center\", colLabels=data.columns, **kwargs\n", "    )\n", "    # mpl_table.auto_set_column_width(col=list(range(len(df_v1.columns))))\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "    print(type(mpl_table))\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        # print(k[0:2][1])\n", "        # print(k[0]) # Gets column k[1]\n", "\n", "        # print(mpl_table._cells)\n", "\n", "        if k[0] == 0 or k[0] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\", ma=\"center\")\n", "            cell.set_facecolor(header_color)\n", "            if (k[1] in list(index_dict.keys())) & (k[0] >= 1):\n", "                if k[0] in (index_dict)[k[1]]:\n", "                    cell.set_text_props(weight=\"extra bold\", color=\"red\", ma=\"center\")\n", "                else:\n", "                    cell.set_text_props(weight=\"extra bold\", color=\"green\", ma=\"center\")\n", "        elif k[0] <= 0:\n", "            cell.set_text_props(weight=\"bold\", ma=\"center\", color=\"black\")\n", "            cell.set_facecolor(\"#eafff5\")\n", "            if (k[1] in list(index_dict.keys())) & (k[0] >= 1):\n", "                if k[0] in (index_dict)[k[1]]:\n", "                    cell.set_text_props(weight=\"extra bold\", color=\"red\", ma=\"center\")\n", "                else:\n", "                    cell.set_text_props(weight=\"extra bold\", color=\"green\", ma=\"center\")\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "            if (k[1] in list(index_dict.keys())) & (k[0] >= 1):\n", "                if k[0] in (index_dict)[k[1]]:\n", "                    cell.set_text_props(weight=\"extra bold\", color=\"red\", ma=\"center\")\n", "                else:\n", "                    cell.set_text_props(weight=\"extra bold\", color=\"green\", ma=\"center\")\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "daily_report_image = f\"df_mdnd.png\"\n", "fig1, ax1 = render_mpl_table(df_mdnd, index_dict)\n", "fig1.savefig(daily_report_image)"]}, {"cell_type": "code", "execution_count": null, "id": "af46f9ce-6403-4f2b-a082-ebb9b82b4aa9", "metadata": {}, "outputs": [], "source": ["SLACK_COMM = f\"\"\"\n", "Rider Block Alert: More than 3 MDND Orders since yesterday\n", "\n", "\"\"\"\n", "pb.send_slack_message(channel=\"solving-store-quality-core\", text=SLACK_COMM, files=[\"df_mdnd.png\"])"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}