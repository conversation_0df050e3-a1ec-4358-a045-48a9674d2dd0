{"cells": [{"cell_type": "code", "execution_count": null, "id": "3210e8ff-ed86-4cc4-88e6-5c615d2c772d", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import pencilbox as pb\n", "import json\n", "import time\n", "from datetime import date, datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "f11b658f-23af-4ae7-86e3-b897a76d6dc6", "metadata": {}, "outputs": [], "source": ["CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "1b8dc306-6c84-4b83-ba1f-61d2fdb942c0", "metadata": {}, "outputs": [], "source": ["INTERIM_PAYMENT_SCHEMA = \"interim\"\n", "INTERIM_PAYMENT_TABLE_NAME = \"payments_master_v2\"\n", "\n", "INTERIM_DRIVER_SCHEMA = \"interim\"\n", "INTERIM_DRIVER_TABLE_NAME = \"driver_master_table_2\"\n", "\n", "\n", "TABLE_SCHEMA = \"fps_etls\"\n", "TABLE_NAME = \"payments_master_v2\""]}, {"cell_type": "code", "execution_count": null, "id": "2ea51599-45b1-4cb7-88a7-4753e1286f83", "metadata": {}, "outputs": [], "source": ["INTERIM_DTYPES = [\n", "    {\"name\": \"pkey\", \"type\": \"BIGINT\", \"description\": \"Primary Key of a Txn\"},\n", "    {\"name\": \"order_id\", \"type\": \"BIGINT\", \"description\": \"order_id\"},\n", "    {\"name\": \"cart_id\", \"type\": \"BIGINT\", \"description\": \"cart_id\"},\n", "    {\"name\": \"ord_order_id\", \"type\": \"VARCHAR\", \"description\": \"ORD_order_id\"},\n", "    {\"name\": \"b_user_id\", \"type\": \"BIGINT\", \"description\": \"customer_id\"},\n", "    {\"name\": \"order_cost\", \"type\": \"DOUBLE\", \"description\": \"payment\"},\n", "    {\"name\": \"order_date\", \"type\": \"DATE\", \"description\": \"order_date\"},\n", "    {\"name\": \"order_time\", \"type\": \"TIMESTAMP(6)\", \"description\": \"order_time\"},\n", "    {\"name\": \"order_deliver_date\", \"type\": \"DATE\", \"description\": \"order_deliver_date\"},\n", "    {\"name\": \"order_deliver_ts_ist\", \"type\": \"TIMESTAMP(6)\", \"description\": \"order_deliver_ts_ist\"},\n", "    {\"name\": \"order_current_status\", \"type\": \"VARCHAR\", \"description\": \"order_current_status\"},\n", "    {\"name\": \"driver_name\", \"type\": \"VARCHAR\", \"description\": \"driver_name\"},\n", "    {\"name\": \"driver_number\", \"type\": \"VARCHAR\", \"description\": \"driver_number\"},\n", "    {\"name\": \"gr_transaction_id\", \"type\": \"VARCHAR\", \"description\": \"track_id\"},\n", "    {\"name\": \"payment_mode\", \"type\": \"VARCHAR\", \"description\": \"payment_mode\"},\n", "    {\"name\": \"payment_method_type\", \"type\": \"VARCHAR\", \"description\": \"payment_method_type\"},\n", "    {\"name\": \"gateway_name\", \"type\": \"VARCHAR\", \"description\": \"name\"},\n", "    {\"name\": \"issuing_bank_name\", \"type\": \"VARCHAR\", \"description\": \"bank_name\"},\n", "    {\"name\": \"last_four_digits\", \"type\": \"VARCHAR\", \"description\": \"last_four_digits\"},\n", "    {\"name\": \"first_six_digits\", \"type\": \"VARCHAR\", \"description\": \"first_six_digits\"},\n", "    {\"name\": \"status\", \"type\": \"VARCHAR\", \"description\": \"status\"},\n", "    {\"name\": \"auth_code\", \"type\": \"VARCHAR\", \"description\": \"auth_code\"},\n", "    {\"name\": \"request_ip_address\", \"type\": \"VARCHAR\", \"description\": \"request_ip_address\"},\n", "    {\"name\": \"fingerprint\", \"type\": \"VARCHAR\", \"description\": \"Card Fingerprint\"},\n", "    {\"name\": \"city\", \"type\": \"VARCHAR\", \"description\": \"city\"},\n", "    {\"name\": \"address\", \"type\": \"VARCHAR\", \"description\": \"address\"},\n", "    {\"name\": \"name\", \"type\": \"VARCHAR\", \"description\": \"name\"},\n", "    {\"name\": \"latitude\", \"type\": \"VARCHAR\", \"description\": \"latitude\"},\n", "    {\"name\": \"longitude\", \"type\": \"VARCHAR\", \"description\": \"longitude\"},\n", "    {\"name\": \"landmark\", \"type\": \"VARCHAR\", \"description\": \"landmark\"},\n", "    {\"name\": \"is_indian_card_bin\", \"type\": \"BOOLEAN\", \"description\": \"indian_card\"},\n", "    {\"name\": \"otp\", \"type\": \"VARCHAR\", \"description\": \"otp\"},\n", "    {\"name\": \"fe_id\", \"type\": \"VARCHAR\", \"description\": \"fe_id\"},\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"VARCHAR\", \"description\": \"Partition Key\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "d80482e2-3c39-48fd-81bf-f9a5148c5d43", "metadata": {}, "outputs": [], "source": ["for i in INTERIM_DTYPES:\n", "    print(i.get(\"name\"))"]}, {"cell_type": "code", "execution_count": null, "id": "3e5ffb38-ce98-45cc-b578-0b1888f19fd6", "metadata": {}, "outputs": [], "source": ["PAYMENTS_KWARGS = {\n", "    \"schema_name\": TABLE_SCHEMA,\n", "    \"table_name\": TABLE_NAME,\n", "    \"column_dtypes\": INTERIM_DTYPES,\n", "    \"primary_key\": [\"pkey\"],\n", "    \"load_type\": \"upsert\",\n", "    \"partition_key\": [\"insert_ds_ist\"],\n", "    \"table_description\": \"payments for Customers\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "d6cd1b21-3475-442c-8152-3cb99d091f63", "metadata": {}, "outputs": [], "source": ["INTERIM_PAYMENT_DTYPES = [\n", "    {\"name\": \"pkey\", \"type\": \"BIGINT\", \"description\": \"Primary Key of a Txn\"},\n", "    {\"name\": \"order_id\", \"type\": \"BIGINT\", \"description\": \"order_id\"},\n", "    {\"name\": \"cart_id\", \"type\": \"BIGINT\", \"description\": \"cart_id\"},\n", "    {\"name\": \"ord_order_id\", \"type\": \"VARCHAR\", \"description\": \"ORD_order_id\"},\n", "    {\"name\": \"b_user_id\", \"type\": \"BIGINT\", \"description\": \"customer_id\"},\n", "    {\"name\": \"order_cost\", \"type\": \"DOUBLE\", \"description\": \"payment\"},\n", "    {\"name\": \"order_date\", \"type\": \"DATE\", \"description\": \"order_date\"},\n", "    {\"name\": \"order_time\", \"type\": \"TIMESTAMP(6)\", \"description\": \"order_time\"},\n", "    {\"name\": \"order_deliver_date\", \"type\": \"DATE\", \"description\": \"order_deliver_date\"},\n", "    {\"name\": \"order_deliver_ts_ist\", \"type\": \"TIMESTAMP(6)\", \"description\": \"order_deliver_ts_ist\"},\n", "    {\"name\": \"order_current_status\", \"type\": \"VARCHAR\", \"description\": \"order_current_status\"},\n", "    {\"name\": \"gr_transaction_id\", \"type\": \"VARCHAR\", \"description\": \"track_id\"},\n", "    {\"name\": \"payment_mode\", \"type\": \"VARCHAR\", \"description\": \"payment_mode\"},\n", "    {\"name\": \"payment_method_type\", \"type\": \"VARCHAR\", \"description\": \"payment_method_type\"},\n", "    {\"name\": \"gateway_name\", \"type\": \"VARCHAR\", \"description\": \"name\"},\n", "    {\"name\": \"issuing_bank_name\", \"type\": \"VARCHAR\", \"description\": \"bank_name\"},\n", "    {\"name\": \"last_four_digits\", \"type\": \"VARCHAR\", \"description\": \"last_four_digits\"},\n", "    {\"name\": \"first_six_digits\", \"type\": \"VARCHAR\", \"description\": \"first_six_digits\"},\n", "    {\"name\": \"status\", \"type\": \"VARCHAR\", \"description\": \"status\"},\n", "    {\"name\": \"auth_code\", \"type\": \"VARCHAR\", \"description\": \"auth_code\"},\n", "    {\"name\": \"request_ip_address\", \"type\": \"VARCHAR\", \"description\": \"request_ip_address\"},\n", "    {\"name\": \"fingerprint\", \"type\": \"VARCHAR\", \"description\": \"Card Fingerprint\"},\n", "    {\"name\": \"city\", \"type\": \"VARCHAR\", \"description\": \"city\"},\n", "    {\"name\": \"address\", \"type\": \"VARCHAR\", \"description\": \"address\"},\n", "    {\"name\": \"name\", \"type\": \"VARCHAR\", \"description\": \"name\"},\n", "    {\"name\": \"latitude\", \"type\": \"VARCHAR\", \"description\": \"latitude\"},\n", "    {\"name\": \"longitude\", \"type\": \"VARCHAR\", \"description\": \"longitude\"},\n", "    {\"name\": \"landmark\", \"type\": \"VARCHAR\", \"description\": \"landmark\"},\n", "    {\"name\": \"otp\", \"type\": \"VARCHAR\", \"description\": \"otp\"},\n", "    {\"name\": \"is_indian_card_bin\", \"type\": \"BOOLEAN\", \"description\": \"indian_card\"},\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"VARCHAR\", \"description\": \"Partition Key\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "c925bb3e-2eb2-43b6-9eed-ee6c014505e7", "metadata": {}, "outputs": [], "source": ["INTERIM_PAYMENTS_KWARGS = {\n", "    \"schema_name\": INTERIM_PAYMENT_SCHEMA,\n", "    \"table_name\": INTERIM_PAYMENT_TABLE_NAME,\n", "    \"column_dtypes\": INTERIM_PAYMENT_DTYPES,\n", "    \"primary_key\": [\"pkey\"],\n", "    \"load_type\": \"upsert\",\n", "    \"partition_key\": [\"insert_ds_ist\"],\n", "    \"table_description\": \"payments for Customers\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "b1c9f061-a1eb-463a-b33b-a4420fb2bc19", "metadata": {}, "outputs": [], "source": ["INTERIM_DRIVER_DTYPES = [\n", "    {\"name\": \"order_id\", \"type\": \"BIGINT\", \"description\": \"order_id\"},\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"VARCHAR\", \"description\": \"Partition Key\"},\n", "    {\"name\": \"driver_name\", \"type\": \"VARCHAR\", \"description\": \"driver_name\"},\n", "    {\"name\": \"driver_number\", \"type\": \"VARCHAR\", \"description\": \"driver_number\"},\n", "    {\"name\": \"fe_id\", \"type\": \"VARCHAR\", \"description\": \"fe_id\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "b6b4a41d-8bee-4b34-b344-db9e56070244", "metadata": {}, "outputs": [], "source": ["INTERIM_DRIVER_KWARGS = {\n", "    \"schema_name\": INTERIM_DRIVER_SCHEMA,\n", "    \"table_name\": INTERIM_DRIVER_TABLE_NAME,\n", "    \"column_dtypes\": INTERIM_DRIVER_DTYPES,\n", "    \"primary_key\": [\"order_id\"],\n", "    \"load_type\": \"upsert\",\n", "    \"partition_key\": [\"insert_ds_ist\"],\n", "    \"table_description\": \"payments for Customers\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "b3450cb7-a4da-4aca-8b86-e741881940d1", "metadata": {}, "outputs": [], "source": ["def query(start_date, end_date):\n", "    QUERY = f\"\"\"\n", "    \n", "        WITH payments AS\n", "    (\n", "    SELECT\n", "        id AS pkey,\n", "        cart_id AS Cart_id,\n", "        payment,\n", "        status,\n", "        json_extract_scalar(other_details, '$.event_data.txns[0].payment_method_type') AS payment_method_type,\n", "        json_extract_scalar(other_details, '$.event_data.txns[0].gateway_transaction_id') AS gateway_transaction_id,\n", "        json_extract_scalar(other_details, '$.event_data.txns[0].transaction_status') AS transaction_status,\n", "        json_extract_scalar(other_details, '$.event_data.txns[0].transaction_reference_id') AS transaction_reference_id,\n", "        json_extract_scalar(other_details, '$.event_data.txns[0].payment_method_id') AS payment_method_id,\n", "        json_extract_scalar(other_details, '$.event_data.txns[0].gateway_id') AS gateway_id,\n", "        json_extract_scalar(other_details, '$.event_data.txns[0].track_id') AS track_id,\n", "        json_extract_scalar(other_details, '$.order_id') AS z_order_id,\n", "        json_extract_scalar(other_details, '$.payment_source.issuing_bank') AS bank_name,\n", "        json_extract_scalar(other_details, '$.payment_source.last_four_digits') AS last_four_digits,\n", "        json_extract_scalar(other_details, '$.payment_source.bin_country_code') AS bin_country_code,\n", "        json_extract_scalar(other_details, '$.event_data.txns[0].auth_code') AS auth_code,\n", "        json_extract_scalar(other_details, '$.payment_source.first_six_digits') AS first_six_digits,\n", "        json_extract_scalar(other_details, '$.payment_source.card_name') AS card_name,\n", "        json_extract_scalar(other_details, '$.payment_source.card_token') AS card_token,\n", "        json_extract_Scalar(json_extract(other_details, '$.payment_source'),'$.fingerprint') as fingerprint,\n", "        json_extract_scalar(other_details, '$.event_data.txns[0].request_ip_address') AS request_ip_address,\n", "        TRY_CAST(json_extract_scalar(other_details, '$.event_data.company_user_id') AS BIGINT) AS company_user_id,\n", "        pg.name,\n", "        insert_ds_ist\n", "\n", "    FROM\n", "        payments_db.gr_payment p\n", "    LEFT JOIN \n", "        btransactions.payment_gateways pg\n", "    ON \n", "        CAST(json_extract_scalar(other_details, '$.event_data.txns[0].gateway_id') AS SMALLINT)=pg.gateway_id\n", "    WHERE   \n", "        insert_ds_ist >= '{start_date}'\n", "    AND insert_ds_ist <= '{end_date}'\n", "    ),\n", "    \n", "    order_details AS\n", "    (\n", "    SELECT  \n", "        cart_id,\n", "        MAX(order_id) AS order_id,\n", "        MAX(order_create_dt_ist) AS order_date,\n", "        MAX(dim_customer_key) AS customer_id,\n", "        MAX(order_current_status) AS order_current_status,\n", "        MAX(order_deliver_ts_ist) AS order_deliver_ts_ist,\n", "        MAX(device_id) AS device_id,\n", "        MAX(city_name) AS city_name,\n", "        MAX(dim_customer_address_key) AS dim_customer_address_key,\n", "        MAX(DATE(order_deliver_ts_ist)) AS order_deliver_date,\n", "        MAX(order_external_id) AS ORD_order_id,\n", "        MAX(order_create_ts_ist) AS Order_time,\n", "        MAX(payment_method) AS payment_mode\n", "\n", "    FROM\n", "        dwh.fact_sales_order_details\n", "    WHERE\n", "        order_create_dt_ist >= DATE('{start_date}') AND order_create_dt_ist <= DATE('{end_date}') \n", "    AND is_internal_order  = FALSE\n", "    GROUP BY 1\n", "    ),\n", "\n", "    address AS\n", "    (\n", "    SELECT  \n", "        id,\n", "        line1 || ' ' || line2 AS address,\n", "        location_info,\n", "        JSON_EXTRACT(corrected_location_info,'$.latitude') AS latitude,\n", "        JSON_EXTRACT(corrected_location_info,'$.longitude') AS longitude,\n", "        landmark,\n", "        name,\n", "        state,\n", "        city\n", "    FROM\n", "        grofers_db.gr_address\n", "    ),\n", "    \n", "    final AS\n", "    (\n", "    SELECT\n", "        p.pkey,\n", "        o.order_id AS order_id,\n", "        o.cart_id,\n", "        o.ORD_order_id AS ord_order_id,\n", "        o.customer_id AS b_user_id,\n", "        p.payment AS order_cost,\n", "        o.order_date AS order_date,\n", "        o.order_time,\n", "        o.order_deliver_date,\n", "        o.order_deliver_ts_ist,\n", "        o.order_current_status,\n", "     \n", "\n", "        p.track_id AS gr_transaction_id,\n", "        o.payment_mode,\n", "        p.payment_method_type,\n", "        p.name AS gateway_name,\n", "        p.bank_name AS issuing_bank_name,\n", "        p.last_four_digits,\n", "        p.first_six_digits,\n", "        p.status,\n", "        p.auth_code,\n", "        p.request_ip_address,\n", "        p.fingerprint,\n", "\n", "\n", "        a.city,\n", "        a.address,\n", "        a.name,\n", "        CAST(a.latitude AS VARCHAR) AS latitude,\n", "        CAST(a.longitude AS VARCHAR) AS longitude,\n", "        a.landmark,\n", "        \n", "        CAST(JSON_EXTRACT(ob.meta,'$.delivery_otp') AS VARCHAR) AS otp,\n", "        TRY_CAST(JSON_EXTRACT(ob.meta,'$.is_indian_card_bin') AS BOOLEAN) AS is_indian_card_bin,\n", "        \n", "        p.insert_ds_ist\n", "    FROM\n", "        payments p\n", "    LEFT JOIN\n", "        order_details o ON p.cart_id = o.cart_id \n", "    LEFT JOIN\n", "        address a ON o.dim_customer_address_key = a.id\n", "    LEFT JOIN\n", "         oms_bifrost.oms_order  ob \n", "    ON\n", "        ob.id=o.order_id\n", "    WHERE \n", "    ob.insert_ds_ist >= '{start_date}'\n", "    AND ob.insert_ds_ist <= '{end_date}'\n", "        \n", "    )\n", "\n", "    SELECT * FROM final GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31\n", "    \n", "    \n", "    \"\"\"\n", "    return QUERY"]}, {"cell_type": "code", "execution_count": null, "id": "04f766d2-f93f-4ab4-8233-eb1322ce17f2", "metadata": {}, "outputs": [], "source": ["final_end_date = datetime.today()\n", "st_date = final_end_date - <PERSON><PERSON><PERSON>(days=2)\n", "en_date = st_date  # Start with en_date equal to st_date\n", "\n", "# Initialize final dataset\n", "final_data = pd.DataFrame()\n", "\n", "while en_date <= final_end_date:\n", "    # Format dates\n", "    start_date = st_date.strftime(\"%Y-%m-%d\")\n", "    end_date = en_date.strftime(\"%Y-%m-%d\")\n", "\n", "    # Simulate query\n", "    QUERY = query(start_date, end_date)\n", "\n", "    pb.send_slack_message(\n", "        channel=\"bl-fps-airflow-alerts\",\n", "        text=f\"Data start for Payments Table - {start_date} - {end_date}\",\n", "    )\n", "\n", "    print(start_date, end_date)\n", "\n", "    pb.to_trino(QUERY, **INTERIM_PAYMENTS_KWARGS)\n", "\n", "    pb.send_slack_message(\n", "        channel=\"bl-fps-airflow-alerts\",\n", "        text=f\"Data updated for Payments Table - {start_date} - {end_date}\",\n", "    )\n", "\n", "    # Move to next day\n", "    st_date += <PERSON><PERSON><PERSON>(days=1)\n", "    en_date += <PERSON><PERSON><PERSON>(days=1)"]}, {"cell_type": "code", "execution_count": null, "id": "714de97c-e844-47af-ba6a-45119f3e3d19", "metadata": {}, "outputs": [], "source": ["def query(start_date, end_date):\n", "    QUERY = f\"\"\"\n", "    WITH driver_details AS (\n", "    SELECT\n", "        TRY_CAST(order_id AS BIGINT) AS order_id,\n", "        MAX(cast(json_extract(metadata, '$.driver_id') as varchar)) as fe_id,\n", "        MAX(cast(json_extract(metadata, '$.driver_name') as varchar)) as driver_name,\n", "        MAX(cast(json_extract(metadata, '$.driver_number') as varchar)) as driver_number,\n", "        MAX(insert_ds_ist) AS insert_ds_ist\n", "    FROM\n", "        lake_logistics.logistics_order_3pl_event_log\n", "    WHERE\n", "        insert_ds_ist >=  '{start_date}'\n", "    AND insert_ds_ist <=  '{end_date}'\n", "    AND state = 'SHIPPED'\n", "    GROUP BY 1\n", "    )\n", "\n", "    SELECT * FROM driver_details\n", "    \n", "    \"\"\"\n", "    return QUERY"]}, {"cell_type": "code", "execution_count": null, "id": "2caf4c08-cc2a-47b4-a1c5-588c7a659e2d", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pandas as pd\n", "\n", "# Set dynamic date range\n", "final_end_date = datetime.today()\n", "st_date = final_end_date - <PERSON><PERSON><PERSON>(days=2)\n", "en_date = st_date  # Start both from the same date\n", "\n", "# Initialize final dataset\n", "final_data = pd.DataFrame()\n", "\n", "while en_date <= final_end_date:\n", "    # Format dates\n", "    start_date = st_date.strftime(\"%Y-%m-%d\")\n", "    end_date = en_date.strftime(\"%Y-%m-%d\")\n", "\n", "    # Simulate query\n", "    QUERY = query(start_date, end_date)\n", "    print(start_date, end_date)\n", "\n", "    pb.to_trino(QUERY, **INTERIM_DRIVER_KWARGS)\n", "\n", "    pb.send_slack_message(\n", "        channel=\"bl-fps-airflow-alerts\",\n", "        text=f\"Data updated for Payments Table - {start_date} - {end_date}\",\n", "    )\n", "\n", "    # Move both dates forward by 1 day\n", "    st_date += <PERSON><PERSON><PERSON>(days=1)\n", "    en_date += <PERSON><PERSON><PERSON>(days=1)"]}, {"cell_type": "code", "execution_count": null, "id": "153e998e-8484-4b3a-aa9b-0135b9737d2d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c9817cd3-74e3-4d11-83d7-df00b1ff95ec", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "deef7c81-31cf-4681-a510-3da9c4c32f53", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}