{"cells": [{"cell_type": "code", "execution_count": null, "id": "d53939ce-cb07-485a-a628-3257f031f889", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "from datetime import date, timedelta\n", "from datetime import datetime"]}, {"cell_type": "code", "execution_count": null, "id": "9fc078e5-b8a7-4667-9170-246081e9a998", "metadata": {}, "outputs": [], "source": ["!pip install reportlab\n", "from reportlab.lib import colors\n", "from reportlab.lib.pagesizes import letter\n", "from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer\n", "from reportlab.lib.styles import getSampleStyleSheet\n", "from reportlab.lib.units import inch"]}, {"cell_type": "code", "execution_count": null, "id": "5bd00185-b248-4249-814b-f73666f9b744", "metadata": {}, "outputs": [], "source": ["CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "markdown", "id": "15afb6f2-2cf9-4e27-b75b-f0774c10f415", "metadata": {}, "source": ["### Chat metrics - CSAT%/Rated%/AHT/FRT"]}, {"cell_type": "code", "execution_count": null, "id": "560913bd-c178-4b6e-9ef7-c8c8325ac409", "metadata": {}, "outputs": [], "source": ["master_table_raw = f\"\"\"\n", "\n", "WITH Master_table_base AS (\n", "    SELECT\n", "        p_key,\n", "        EXTRACT(WEEK FROM created_date) AS week,\n", "        EXTRACT(MONTH FROM created_date) AS month,\n", "        created_date AS ticket_date,\n", "        a.session_id,\n", "        conversation_id,\n", "        order_id,\n", "        cart_id,\n", "        bot_last_node,\n", "        agent_disposition,\n", "        group_disposition,\n", "        customer_id,\n", "        karma_label,\n", "        CASE \n", "            WHEN CAST(karma_score AS BIGINT) IN (19, 20) THEN 'Gold 19-20' \n", "            WHEN CAST(karma_score AS BIGINT) IN (17, 18) THEN 'Gold 17-18'\n", "            ELSE karma_label \n", "        END AS new_karma_label,\n", "        karma_score,\n", "        resolved_by_client_id,\n", "        resolved_by,\n", "        resolved_by_name,\n", "        csat,\n", "        chat_rated_flag,\n", "        extreme_ratings,\n", "        chat_history_id,\n", "        issue_id,\n", "        current_skill,\n", "        channel_name,\n", "        vendor_name,\n", "        LOWER(agent_email) AS agent_email,\n", "        assigned_at,\n", "        queued_at,\n", "        aat_secs,\n", "        frt,\n", "        agent_frt,\n", "        aht_sys,\n", "        aht,\n", "        art_secs,\n", "        rt_ulrt,\n", "        IVA_flag,\n", "        employment_type,\n", "        Agent_stores_flag,\n", "        gold_metric_flag,\n", "        escalation_flag,\n", "        agent_tenure,\n", "        created_at,\n", "        CASE \n", "            WHEN agent_tenure BETWEEN 0 AND 30 THEN '0-30'\n", "            WHEN agent_tenure > 30 THEN '>30' \n", "        END AS tenure_bucket,\n", "        ROW_NUMBER() OVER (PARTITION BY a.session_id, chat_history_id ORDER BY assigned_at DESC) AS rn,\n", "        ROW_NUMBER() OVER (PARTITION BY a.session_id ORDER BY assigned_at DESC) AS chat_rank\n", "    FROM interim.cd_dashboard_base_table a\n", "    LEFT JOIN (\n", "        SELECT \n", "            session_id, \n", "            created_at \n", "        FROM cd_etls.master_table \n", "        WHERE created_date BETWEEN DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1' DAY) AND CURRENT_DATE - INTERVAL '1' DAY\n", "        GROUP BY session_id, created_at\n", "    ) b ON a.session_id = b.session_id\n", "    WHERE created_date BETWEEN DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1' DAY) AND CURRENT_DATE - INTERVAL '1' DAY\n", "    AND channel_name NOT IN ('Gold 19-20 Image Verification', 'Image Verification')\n", "),\n", "\n", "metric_calc AS (\n", "    SELECT\n", "        month,\n", "        agent_email,\n", "        channel_name,\n", "        CASE \n", "            WHEN group_disposition IN (\n", "                'Wrong Order or Items', 'issue with an order-parts missing',\n", "                'issue with an order-partial item/freebie missing', 'Items missing or incorrect',\n", "                'Product not available'\n", "            ) THEN 'Items missing or incorrect'\n", "            WHEN group_disposition IN (\n", "                'Payment Related Issue/Query', 'issue with an order-delivery/surge/convenience/tip/any other charge',\n", "                'Refund or Invoice Related Queries', 'Promo/Surge related Queries', 'Price Description',\n", "                'cashback/refund/invoice-unsubscribe from sms', 'cashback/refund/invoice-wallet delink',\n", "                'cashback/refund/invoice-gc transfer to omp/imps', 'ADONP/DD'\n", "            ) THEN 'Payment/resolution related'\n", "            WHEN group_disposition IN (\n", "                'Product Quality', 'the item(s) are **defective** or **not working properly**',\n", "                'issue with packaging-packaging not good/feedback for packaging',\n", "                'yes, the **packaging** of the item(s) **is intact**', 'issue with packaging-discreet packaging not done',\n", "                'the item(s) are **physically damaged**', 'expectation mismatch-exchange/return request',\n", "                'the item(s) did not meet my **expectations**', 'the item(s) are **not what i wanted**'\n", "            ) THEN 'Product/Packaging Quality'\n", "            WHEN group_disposition IN (\n", "                'Delay in Delivery', 'Order Status', 'Delivery Instruction or Address change',\n", "                'Modify Order Request', 'Order Cancellation'\n", "            ) THEN 'Pre Delivery Concerns'\n", "            WHEN group_disposition IN (\n", "                'Cx Feedback dp/agent/general', 'i want to report delivery partner misconduct',\n", "                'Complaint against Delivery Partner or Agent'\n", "            ) THEN 'Feedback'\n", "            WHEN group_disposition IN (\n", "                'business with blinkit-business with blinkit', 'no', 'Miscellaneous or non-queries',\n", "                'issue with an order-ordered by mistake', 'Issue Placing an order', 'Exchange or Return Item'\n", "            ) THEN 'Miscellaneous'\n", "            ELSE group_disposition \n", "        END AS final_group_disposition,\n", "        COUNT(DISTINCT CASE WHEN chat_rank = 1 AND resolved_by_name = 'sys-inactivity-resolve-worker' THEN session_id END) AS timed_out,\n", "        COUNT(DISTINCT CASE WHEN issue_id IS NOT NULL THEN chat_history_id END) AS chats,\n", "        AVG(CASE WHEN issue_id IS NOT NULL THEN aht END) * 1.00 / 60 AS avg_aht,\n", "        AVG(CASE WHEN issue_id IS NOT NULL THEN agent_frt END) * 1.00 / 60 AS avg_agent_frt,\n", "      --COUNT(DISTINCT CASE WHEN issue_id IS NOT NULL AND chat_rank = 1 THEN chat_history_id END) AS total_chats,\n", "--        COALESCE(SUM(CASE WHEN issue_id IS NOT NULL AND chat_rank = 1 THEN csat END), 0) AS csat_sum,\n", "        --COALESCE(COUNT(DISTINCT CASE WHEN issue_id IS NOT NULL AND chat_rank = 1 AND csat IN (4,5) THEN chat_history_id END), 0) * 1.0000 /\n", "        --(COUNT(DISTINCT CASE WHEN issue_id IS NOT NULL AND chat_rank = 1 AND csat IN (1,2,3,4,5) THEN chat_history_id END) + 0.001) AS AVG_CSAT,\n", "                COALESCE(COUNT(DISTINCT CASE WHEN issue_id IS NOT NULL AND chat_rank = 1 AND csat IN (4,5) THEN chat_history_id END), 0) * 1.0000 csat,\n", "        COUNT(DISTINCT CASE WHEN issue_id IS NOT NULL AND chat_rank = 1 AND csat IN (1,2,3,4,5) THEN chat_history_id END) AS rated,\n", "        \n", "        \n", "        case when \n", "\n", "COUNT(DISTINCT CASE WHEN issue_id IS NOT NULL AND chat_rank = 1 AND csat IN (1,2,3,4,5) THEN chat_history_id END)=0 then 0 \n", "else\n", "COALESCE(COUNT(DISTINCT CASE WHEN issue_id IS NOT NULL AND chat_rank = 1 AND csat IN (4,5) THEN chat_history_id END), 0) * 1.0000\n", "/COUNT(DISTINCT CASE WHEN issue_id IS NOT NULL AND chat_rank = 1 AND csat IN (1,2,3,4,5) THEN chat_history_id END) end csat_perc\n", ",\n", "case when \n", "COUNT(DISTINCT CASE WHEN issue_id IS NOT NULL THEN chat_history_id END)=0 then 0 \n", "else\n", "COUNT(DISTINCT CASE WHEN issue_id IS NOT NULL AND chat_rank = 1 AND csat IN (1,2,3,4,5) THEN chat_history_id END) * 1.0000/COUNT(DISTINCT CASE WHEN issue_id IS NOT NULL THEN chat_history_id END) end rated_perc\n", "        \n", "        \n", "        \n", "        --,\n", "        --COUNT(DISTINCT CASE WHEN issue_id IS NOT NULL AND chat_rank = 1 AND csat IN (1,2,3,4,5) THEN chat_history_id END) * 100.00 /\n", "        --(COUNT(DISTINCT CASE WHEN issue_id IS NOT NULL AND chat_rank = 1 THEN chat_history_id END) + 0.0001) AS rated_perc\n", "    FROM Master_table_base a\n", "    WHERE rn = 1 \n", "    AND channel_name <> 'Combined chat'\n", "    AND IVA_flag = 0\n", "    GROUP BY 1,2,3,4\n", "),\n", "\n", "total_chats AS (\n", "    SELECT \n", "        month,\n", "        agent_email,\n", "        SUM(chats) AS overall_chats\n", "    FROM metric_calc\n", "    GROUP BY month, agent_email\n", ")\n", "\n", "SELECT a.*\n", "FROM metric_calc a\n", "LEFT JOIN total_chats b \n", "    ON a.month = b.month \n", "    AND a.agent_email = b.agent_email\n", "WHERE overall_chats > 15\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "74958954-2c89-4ac6-8bda-965a0a63c503", "metadata": {}, "outputs": [], "source": ["dataframe = pd.read_sql_query(master_table_raw, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "6779c3a6-cdd3-48f9-ae66-266b1e237e37", "metadata": {}, "outputs": [], "source": ["dataframe"]}, {"cell_type": "code", "execution_count": null, "id": "7c9dc352-4c3d-4541-884b-1bda52bc93c1", "metadata": {"tags": []}, "outputs": [], "source": ["# sorted_df = dataframe[dataframe['agent_email'] == '<EMAIL>'].sort_values(by='rated_perc', ascending=False)\n", "# sorted_df.head(20)"]}, {"cell_type": "code", "execution_count": null, "id": "49b35741-db09-45a3-bbf0-22f755464023", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8fe6607a-7be2-4c2a-8b49-8e4f51f82132", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "86ebdd2b-5c32-4381-ad6c-a268e453a075", "metadata": {}, "outputs": [], "source": ["dataframe[\"csat\"] = dataframe[\"csat\"].astype(\"float64\")\n", "dataframe[\"rated_perc\"] = dataframe[\"rated_perc\"].astype(\"float64\")\n", "dataframe[\"csat_perc\"] = dataframe[\"csat_perc\"].astype(\"float64\")"]}, {"cell_type": "code", "execution_count": null, "id": "0ab1a89d-14be-4c4e-8996-6c64bdb41dd7", "metadata": {}, "outputs": [], "source": ["ref_df_mean = dataframe.groupby([\"channel_name\", \"final_group_disposition\"]).mean()\n", "ref_df_std = dataframe.groupby([\"channel_name\", \"final_group_disposition\"]).std()"]}, {"cell_type": "code", "execution_count": null, "id": "74983206-1944-4c19-bd6f-cfe452e79b3f", "metadata": {}, "outputs": [], "source": ["ref_df_mean"]}, {"cell_type": "code", "execution_count": null, "id": "3ef664e9-0ecb-45d7-b071-65b9fb879e17", "metadata": {}, "outputs": [], "source": ["ref_csat_std = (\n", "    dataframe.query(f\"rated > 3\").groupby([\"channel_name\", \"final_group_disposition\"]).std()\n", ")\n", "ref_csat_avg = (\n", "    dataframe.query(f\"rated > 3\").groupby([\"channel_name\", \"final_group_disposition\"]).mean()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "93c0f9fd-37a0-48a6-bf16-978241404178", "metadata": {}, "outputs": [], "source": ["ref_csat_std"]}, {"cell_type": "code", "execution_count": null, "id": "34b70c28-cdb1-41ec-b572-22814d06174f", "metadata": {}, "outputs": [], "source": ["dataframe.groupby([\"channel_name\", \"final_group_disposition\"])[\"rated_perc\"].describe()"]}, {"cell_type": "code", "execution_count": null, "id": "884636b4-b986-47c7-91f3-b84c4578d117", "metadata": {}, "outputs": [], "source": ["def getZScore(\n", "    feature,\n", "    val,\n", "    channel_name,\n", "    final_group_disposition,\n", "    ref_df_mean,\n", "    ref_df_std,\n", "    rated,\n", "    # csat_abs,\n", "):\n", "\n", "    if (\n", "        feature == \"csat_perc\" or feature == \"refunded_perc\" or feature == \"rated_perc\"\n", "    ) and rated <= 3:\n", "        # z = csat_abs*5\n", "        return 0\n", "    else:\n", "        mean = ref_df_mean[[feature]].loc[channel_name, final_group_disposition].values[0]\n", "        std = ref_df_std[[feature]].loc[channel_name, final_group_disposition].values[0]\n", "\n", "        z = (val - mean) / std\n", "\n", "    return z"]}, {"cell_type": "code", "execution_count": null, "id": "cad8a14a-51e9-478a-9cb3-cfc7c88553f6", "metadata": {}, "outputs": [], "source": ["for i in [\"avg_aht\", \"avg_agent_frt\", \"rated_perc\"]:\n", "    dataframe[f\"z_{i}\"] = dataframe.apply(\n", "        lambda x: getZScore(\n", "            i,\n", "            x[i],\n", "            x.channel_name,\n", "            x.final_group_disposition,\n", "            ref_df_mean,\n", "            ref_df_std,\n", "            x.rated,\n", "            # x.csat_abs  # <-- Add this\n", "        ),\n", "        axis=1,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "fcf9de7c-02ca-402a-8058-4b3bf8d0c9d3", "metadata": {}, "outputs": [], "source": ["# dataframe"]}, {"cell_type": "code", "execution_count": null, "id": "46e882c5-a0bd-4117-8046-8d4f5884b127", "metadata": {}, "outputs": [], "source": ["dataframe[\"z_AVG_CSAT\"] = dataframe.apply(\n", "    lambda x: getZScore(\n", "        \"csat_perc\",\n", "        x[\"csat_perc\"],\n", "        x.channel_name,\n", "        x.final_group_disposition,\n", "        ref_csat_avg,\n", "        ref_csat_std,\n", "        x.rated,\n", "        # x.csat_perc,\n", "    ),\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1f8fb3b1-37e8-45b3-985d-4126eaa6cbc0", "metadata": {}, "outputs": [], "source": ["# dataframe"]}, {"cell_type": "code", "execution_count": null, "id": "1cd01630-39b4-4b52-acde-c9d33359161c", "metadata": {}, "outputs": [], "source": ["min_z_score = dataframe.groupby([\"channel_name\", \"final_group_disposition\"]).min()\n", "max_z_score = dataframe.groupby([\"channel_name\", \"final_group_disposition\"]).max()\n", "\n", "# min_agent_frt_score = dataframe.groupby(['channel_name']).z_avg_agent_frt.min()\n", "# max_agent_frt_score = dataframe.groupby(['channel_name']).z_avg_agent_frt.max()"]}, {"cell_type": "code", "execution_count": null, "id": "9ddfa944-f53a-437c-ba8b-fbddfe31643e", "metadata": {}, "outputs": [], "source": ["# dataframe\n", "def getFinalscore(\n", "    feature,\n", "    val,\n", "    channel_name,\n", "    final_group_disposition,\n", "    min_val,\n", "    max_val,\n", "    # rated,\n", "    # csat_abs,\n", "):\n", "\n", "    # if (feature == \"z_AVG_CSAT\" or feature == \"refunded_perc\") and rated <= 3:\n", "    #     return 0\n", "    # else:\n", "    min_z = min_val[[feature]].loc[channel_name, final_group_disposition].values[0]\n", "    max_z = max_val[[feature]].loc[channel_name, final_group_disposition].values[0]\n", "\n", "    final_score = (val - min_z) / (max_z - min_z)\n", "\n", "    return final_score"]}, {"cell_type": "code", "execution_count": null, "id": "ee7c16ad-0a89-4196-8047-e4c7c4839316", "metadata": {}, "outputs": [], "source": ["min_z_score"]}, {"cell_type": "code", "execution_count": null, "id": "a18f1b59-cea8-496d-9e39-58a2b1060bb9", "metadata": {}, "outputs": [], "source": ["for i in [\"z_avg_aht\", \"z_avg_agent_frt\", \"z_rated_perc\", \"z_AVG_CSAT\"]:\n", "    # , \"z_rated_perc\", \"z_AVG_CSAT\"\n", "    dataframe[f\"normal_{i}\"] = dataframe.apply(\n", "        lambda x: getFinalscore(\n", "            i,\n", "            x[i],\n", "            x.channel_name,\n", "            x.final_group_disposition,\n", "            min_z_score,\n", "            max_z_score,\n", "            # x.rated,\n", "            # x.AVG_CSAT,\n", "        ),\n", "        axis=1,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "16c5fca3-d2da-47fd-bfcd-197f53b7ece9", "metadata": {}, "outputs": [], "source": ["dataframe[\"normal_z_avg_aht\"] = 1 - dataframe[\"normal_z_avg_aht\"]\n", "dataframe[\"normal_z_avg_agent_frt\"] = 1 - dataframe[\"normal_z_avg_agent_frt\"]\n", "\n", "# dataframe.query('agent_email == \"<EMAIL>\"')"]}, {"cell_type": "code", "execution_count": null, "id": "bdbd273a-137b-405b-a35a-1a23a9b7b51f", "metadata": {}, "outputs": [], "source": ["display(dataframe[\"normal_z_avg_aht\"].min())\n", "display(dataframe[\"normal_z_avg_aht\"].max())\n", "display(dataframe[\"normal_z_avg_agent_frt\"].min())\n", "display(dataframe[\"normal_z_avg_agent_frt\"].max())\n", "display(dataframe[\"normal_z_rated_perc\"].min())\n", "display(dataframe[\"normal_z_rated_perc\"].max())\n", "display(dataframe[\"normal_z_AVG_CSAT\"].min())\n", "display(dataframe[\"normal_z_AVG_CSAT\"].max())"]}, {"cell_type": "code", "execution_count": null, "id": "50ce518a-3f1c-4659-a978-81d6eda690fd", "metadata": {}, "outputs": [], "source": ["# dataframe"]}, {"cell_type": "code", "execution_count": null, "id": "5b09efac-40b0-49a2-88fd-c01702105644", "metadata": {}, "outputs": [], "source": ["def weightedNormal(val, wt):\n", "    return (val * wt).sum() / wt.sum()"]}, {"cell_type": "code", "execution_count": null, "id": "44143342-90a8-46cb-8018-8b096e4552cf", "metadata": {}, "outputs": [], "source": ["dataframe.columns"]}, {"cell_type": "code", "execution_count": null, "id": "6d0b9f6e-2551-43cd-b85c-6d61d625cd63", "metadata": {}, "outputs": [], "source": ["# dataframe = dataframe.reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "093fe505-59a3-4031-b3fb-1ac38eaf3c2b", "metadata": {}, "outputs": [], "source": ["# min_aht_z_score = dataframe.groupby(['channel_name']).min()\n", "# for in ['avg_aht','avg_agent_frt','rated_perc','csat_perc']:\n", "df = pd.DataFrame()\n", "df[\"aht_score\"] = dataframe.groupby([\"month\", \"agent_email\"]).apply(\n", "    lambda x: weightedNormal(x.normal_z_avg_aht, x.chats)\n", ")\n", "df[\"avg_frt_score\"] = dataframe.groupby([\"month\", \"agent_email\"]).apply(\n", "    lambda x: weightedNormal(x.normal_z_avg_agent_frt, x.chats)\n", ")\n", "df[\"csat_perc\"] = dataframe.groupby([\"month\", \"agent_email\"]).apply(\n", "    lambda x: weightedNormal(x.normal_z_AVG_CSAT, x.rated)\n", ")\n", "df[\"rated_perc\"] = dataframe.groupby([\"month\", \"agent_email\"]).apply(\n", "    lambda x: weightedNormal(x.normal_z_rated_perc, x.chats)\n", ")\n", "df[\"timed_out\"] = dataframe.groupby([\"month\", \"agent_email\"])[\"timed_out\"].sum()\n", "\n", "\n", "# df = dataframe[dataframe['agent_email'] == '<EMAIL>'].sort_values(by='csat', ascending=False)\n", "\n", "# df[\"csat\"] = pd.to_numeric(df[\"csat\"], errors=\"coerce\")\n", "# df[\"rated\"] = pd.to_numeric(df[\"rated\"], errors=\"coerce\")\n", "\n", "\n", "df[\"csat\"] = dataframe.groupby([\"month\", \"agent_email\"])[\"csat\"].sum()\n", "df[\"chats\"] = dataframe.groupby([\"month\", \"agent_email\"])[\"chats\"].sum()\n", "df[\"rated\"] = dataframe.groupby([\"month\", \"agent_email\"])[\"rated\"].sum()\n", "\n", "# df[\"csat_perc\"] = df[\"csat\"] / df[\"rated\"].replace(0, np.nan)\n", "# df[\"rated_perc\"] = df[\"rated\"] / df[\"chats\"].replace(0, np.nan)\n", "\n", "\n", "# chats = sorted_df.groupby([\"month\", \"agent_email\"])[\"chats\"].sum()\n", "# print(csat_sum,chats)\n", "# chats\n", "# csat_ratio = csat_sum / chats.replace(0, np.nan)\n", "\n", "\n", "# First, make sure csat and rated columns are numeric\n", "# df[\"csat\"] = pd.to_numeric(df[\"csat\"], errors=\"coerce\")\n", "# df[\"rated\"] = pd.to_numeric(df[\"rated\"], errors=\"coerce\")\n", "\n", "# Group and compute the ratio\n", "# csat_sum = dataframe.groupby([\"month\", \"agent_email\"])[\"csat\"].sum()\n", "# rated_sum = dataframe.groupby([\"month\", \"agent_email\"])[\"rated\"].sum()\n", "# rated_sum = dataframe.groupby([\"month\", \"agent_email\"])[\"chats\"].sum()\n", "\n", "# # Avoid division by zero\n", "# csat_ratio = csat_sum / rated_sum.replace(0, np.nan)\n", "# rated_ratio = csat_sum / rated_sum.replace(0, np.nan)\n", "# df = df.reset_index()\n", "# # # Assign back to original dataframe using index mapping\n", "# df[\"csat\"] = df.set_index([\"month\", \"agent_email\"]).index.map(csat_ratio)\n", "# df[\"rated\"] = df.set_index([\"month\", \"agent_email\"]).index.map(rated_ratio)\n", "\n", "# df[\"csat\"] = dataframe.set_index([\"month\", \"agent_email\"]).index.map(csat_ratio)\n", "# df[\"rated\"] = dataframe.set_index([\"month\", \"agent_email\"]).index.map(rated_ratio)\n", "\n", "\n", "# df = df.reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "8e49718a-4e83-47dd-a3c2-72babb089e22", "metadata": {}, "outputs": [], "source": ["# df"]}, {"cell_type": "code", "execution_count": null, "id": "6d92a96f-5210-42a9-ae4e-21825a00a1e3", "metadata": {}, "outputs": [], "source": ["# df.to_csv('score.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "df3f1ae0-7ea7-4385-8c1d-14b0a2675d20", "metadata": {}, "outputs": [], "source": ["# sorted_df = dataframe[dataframe['agent_email'] == '<EMAIL>'].sort_values(by='csat', ascending=False)\n", "\n", "# sorted_df[\"csat\"] = pd.to_numeric(sorted_df[\"csat\"], errors=\"coerce\")\n", "# sorted_df[\"rated\"] = pd.to_numeric(sorted_df[\"rated\"], errors=\"coerce\")\n", "\n", "\n", "# csat_sum = sorted_df.groupby([\"month\", \"agent_email\"])[\"csat\"].sum()\n", "# chats = sorted_df.groupby([\"month\", \"agent_email\"])[\"chats\"].sum()\n", "# # print(csat_sum,chats)\n", "# chats\n", "# csat_ratio = csat_sum / chats.replace(0, np.nan)"]}, {"cell_type": "code", "execution_count": null, "id": "dc1c66f8-fac4-4453-ab38-10cfe9abce1b", "metadata": {}, "outputs": [], "source": ["# # df = df.reset_index()\n", "\n", "\n", "# sorted_df = df[df['agent_email'] == '<EMAIL>'].sort_values(by='csat', ascending=False)\n", "# sorted_df.head(20)"]}, {"cell_type": "markdown", "id": "2921e80d-fe41-4bea-ba57-bbf33f1fae75", "metadata": {}, "source": ["### Resolution%/ Refund%"]}, {"cell_type": "code", "execution_count": null, "id": "75644d02-9b97-4cc7-b747-6dc77a656ca4", "metadata": {}, "outputs": [], "source": ["Resolution_query = f\"\"\"\n", "With\n", "Master_table_base as(\n", "Select\n", "    p_key\n", "    ,extract(month from created_date) as month\n", "    ,created_date as ticket_date\n", "    ,a.session_id\n", "    ,conversation_id\n", "    ,order_id\n", "    ,cart_id\n", "    ,bot_last_node\n", "    ,agent_disposition\n", "    ,group_disposition\n", "    ,customer_id\n", "    ,karma_label\n", "    ,case when cast(karma_score as bigint) in (19,20) then 'Gold 19-20' \n", "        when cast(karma_score as bigint) in (17,18) then 'Gold 17-18'\n", "        else karma_label end as new_karma_label\n", "    ,karma_score\n", "    ,resolved_by_client_id\n", "    ,resolved_by\n", "    ,resolved_by_name\n", "    ,csat\n", "    ,chat_rated_flag\n", "    ,extreme_ratings\n", "    ,chat_history_id\n", "    ,issue_id\n", "    ,current_skill\n", "    ,channel_name\n", "    ,vendor_name\n", "    ,lower(agent_email) as agent_email\n", "    ,assigned_at\n", "    ,queued_at\n", "    ,aat_secs\n", "    ,frt\n", "    ,agent_frt\n", "    ,aht_sys\n", "    ,aht\n", "    ,art_secs\n", "    ,rt_ulrt\n", "    ,IVA_flag\n", "    ,employment_type\n", "    ,Agent_stores_flag\n", "    ,gold_metric_flag\n", "    ,escalation_flag\n", "    ,agent_tenure\n", "    ,created_at\n", "    ,case when agent_tenure >= 0 and agent_tenure <= 30 then '0-30'\n", "    -- when agent_tenure > 5 and agent_tenure <= 10 then '5-10'\n", "    when agent_tenure > 30 then '>30' end as tenure_bucket\n", "    ,row_number() over (partition by a.session_id,chat_history_id order by assigned_at desc) as rn\n", "    ,row_number() over (partition by a.session_id order by assigned_at desc) as chat_rank\n", "from interim.cd_dashboard_base_table a\n", "left join (Select session_id, created_at from cd_etls.master_table \n", "    where created_date between DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1' DAY) AND CURRENT_DATE - INTERVAL '1' DAY\n", "    -- current_date - interval '2' day and CURRENT_DATE - INTERVAL '2' DAY \n", "    ) b on a.session_id = b.session_id\n", "-- where created_date between current_date - interval '2' day and CURRENT_DATE - INTERVAL '2' DAY \n", "where created_date between  DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1' DAY) AND CURRENT_DATE - INTERVAL '1' DAY\n", "and channel_name not in ('Gold 19-20 Image Verification','Image Verification')\n", "),\n", "\n", "Reso_base as(\n", "Select\n", "reso_date,\n", "session_id as reso_session_id,\n", "email_id,\n", "sum(resol_amount) as resolution_amt,\n", "sum(case when reso_type = 'refund' then resol_amount end) as refund_amt,\n", "sum(case when reso_type = 'replace' then resol_amount end) as replace_amt\n", "from cd_etls.item_wise_reso\n", "where --reso_date >= current_date - interval '30' day\n", "reso_date between  DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1' DAY) AND CURRENT_DATE - INTERVAL '1' DAY\n", "and reason in ('Items','MDND')\n", "group by 1,2,3\n", "),\n", "\n", "karma_resolution_base as(\n", "Select\n", "month,\n", "agent_email,\n", "channel_name,\n", "case when group_disposition in (\n", "                    'Wrong Order or Items','issue with an order-parts missing',\n", "                    'issue with an order-partial item/freebie missing',\n", "                    'Items missing or incorrect',\n", "                    'Product not available') then 'Items missing or incorrect'\n", "        when group_disposition in (\n", "                    'Payment Related Issue/Query' ,\n", "                    'issue with an order-delivery/surge/convenience/tip/any other charge',\n", "                    'Refund or Invoice Related Queries','Promo/Surge related Queries',\n", "                    'Price Description','cashback/refund/invoice-unsubscribe from sms',\n", "                    'cashback/refund/invoice-wallet delink',\n", "                    'cashback/refund/invoice-gc transfer to omp/imps',\n", "                    'ADONP/DD') then 'Payment/resolution related'\n", "        when group_disposition in (\n", "                    'Product Quality',\n", "                    'the item(s) are **defective** or **not working properly**',\n", "                    'issue with packaging-packaging not good/feedback for packaging',\n", "                    'yes, the **packaging** of the item(s) **is intact**',\n", "                    'issue with packaging-discreet packaging not done',\n", "                    'the item(s) are **physically damaged**',\n", "                    'expectation mismatch-exchange/return request',\n", "                    'the item(s) did not meet my **expectations**',\n", "                    'the item(s) are **not what i wanted**') then 'Product/Packagaing Quality'\n", "        when group_disposition in (\n", "                    'Delay in Delivery',\n", "                    'Order Status',\n", "                    'Delivery Instruction or Address change',\n", "                    'Modify Order Request',\n", "                    'Order Cancellation') then 'Pre Delivery Concerns'\n", "        when group_disposition in (\n", "                    'Cx Feedback dp/agent/general',\n", "                    'i want to report delivery partner misconduct',\n", "                    'Complaint against Delivery Partner or Agent') then 'Feedback'\n", "        when group_disposition in (\n", "                    'business with blinkit-business with blinkit',\n", "                    'no',\n", "                    'Miscellaneous or non-queries',\n", "                    'issue with an order-ordered by mistake',\n", "                    'Issue Placing an order',\n", "                    'Exchange or Return Item') then 'Miscellaneous'\n", "    else  group_disposition end as final_group_disposition,\n", "count(distinct case when issue_id is not null then chat_history_id end) as chats,\n", "count(distinct case when issue_id is not null and resolution_amt is not null then chat_history_id end) as resoltn_given_chats,\n", "count(distinct case when issue_id is not null and resolution_amt is not null then chat_history_id end)*100.00/(count(distinct case when issue_id is not null then chat_history_id end)+0.00001) as Resoltn_perc,\n", "count(distinct case when issue_id is not null and refund_amt is not null then chat_history_id end) as refunded_chats,\n", "count(distinct case when issue_id is not null and refund_amt is not null then chat_history_id end)*100.00/(count(distinct case when issue_id is not null and resolution_amt is not null then chat_history_id end)+0.0001) as refunded_perc,\n", "count(distinct case when issue_id is not null and refund_amt is not null then chat_history_id end)*100.00/(count(distinct case when issue_id is not null then chat_history_id end)+0.00001) as ref_reso\n", "from Master_table_base a\n", "left join Reso_base b on a.session_id = cast(b.reso_session_id as bigint)\n", "and a.agent_email = b.email_id\n", "where rn = 1 \n", "--and channel_name like '%%delivered%%'\n", "and IVA_flag = 0\n", "group by 1,2,3,4\n", "),\n", "\n", "total_chats as(\n", "Select \n", "month,\n", "agent_email,\n", "sum(chats) as overall_chats\n", "from karma_resolution_base\n", "group by 1,2\n", ")\n", "\n", "Select \n", "a.*\n", "--refunded_perc*Resoltn_perc as ref_reso\n", "from karma_resolution_base a\n", "left join total_chats b on a.month = b.month and a.agent_email = b.agent_email \n", "where overall_chats > 15\n", "--and chats > 5\n", "--and a.agent_email='<EMAIL>'\n", "\n", "order by 3,4\n", "-- 12206\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "cfe94d4f-07d2-476e-b23b-76328f687386", "metadata": {}, "outputs": [], "source": ["df_reso = pd.read_sql_query(Resolution_query, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "5ab3b9cf-b1aa-4b7f-be64-652422a1796c", "metadata": {}, "outputs": [], "source": ["df_reso"]}, {"cell_type": "code", "execution_count": null, "id": "90e9cb6f-3bcb-488a-b0a7-42df2988056e", "metadata": {}, "outputs": [], "source": ["df_reso[\"Resoltn_perc\"] = df_reso[\"Resoltn_perc\"].astype(\"float64\")\n", "df_reso[\"refunded_perc\"] = df_reso[\"refunded_perc\"].astype(\"float64\")\n", "df_reso[\"ref_reso\"] = df_reso[\"ref_reso\"].astype(\"float64\")"]}, {"cell_type": "code", "execution_count": null, "id": "22ffdd00-61c4-4779-b413-41228cc718d9", "metadata": {}, "outputs": [], "source": ["reso_df_mean = df_reso.groupby([\"channel_name\", \"final_group_disposition\"]).mean()\n", "reso_df_std = df_reso.groupby([\"channel_name\", \"final_group_disposition\"]).std()"]}, {"cell_type": "code", "execution_count": null, "id": "ec3cd542-8412-4970-920c-3e316a9d9656", "metadata": {}, "outputs": [], "source": ["reso_df_mean"]}, {"cell_type": "code", "execution_count": null, "id": "3be9024b-4e04-43c7-8e81-e9f0f407bd51", "metadata": {}, "outputs": [], "source": ["df_reso[\"z_ref_reso\"] = df_reso.apply(\n", "    lambda x: getZScore(\n", "        \"ref_reso\",\n", "        x[\"ref_reso\"],\n", "        x.channel_name,\n", "        x.final_group_disposition,\n", "        reso_df_mean,\n", "        reso_df_std,\n", "        # x.chats,\n", "        x.refunded_chats,\n", "    ),\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e7cefe30-d9e9-4a4e-82d2-6cc6e7d791df", "metadata": {}, "outputs": [], "source": ["df_reso"]}, {"cell_type": "code", "execution_count": null, "id": "980fb25b-6839-464c-843a-673f1e29eaee", "metadata": {}, "outputs": [], "source": ["df_reso[\"z_Resol_perc\"] = df_reso.apply(\n", "    lambda x: getZScore(\n", "        \"Resoltn_perc\",\n", "        x[\"Resoltn_perc\"],\n", "        x.channel_name,\n", "        x.final_group_disposition,\n", "        reso_df_mean,\n", "        reso_df_std,\n", "        x.chats,\n", "        # x.refunded_chats,\n", "    ),\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "28984178-e99a-48ef-ba15-49d80ba2e10a", "metadata": {}, "outputs": [], "source": ["df_reso"]}, {"cell_type": "code", "execution_count": null, "id": "26e02612-b5fe-42c6-98f3-7c4b2c1184da", "metadata": {}, "outputs": [], "source": ["min_z_score_reso = df_reso.groupby([\"channel_name\", \"final_group_disposition\"]).min()\n", "max_z_score_reso = df_reso.groupby([\"channel_name\", \"final_group_disposition\"]).max()"]}, {"cell_type": "code", "execution_count": null, "id": "93441165-6ceb-4199-a2de-b92efb9d3276", "metadata": {}, "outputs": [], "source": ["for i in [\"z_Resol_perc\", \"z_ref_reso\"]:\n", "    df_reso[f\"normal_{i}\"] = df_reso.apply(\n", "        lambda x: getFinalscore(\n", "            i,\n", "            x[i],\n", "            x.channel_name,\n", "            x.final_group_disposition,\n", "            min_z_score_reso,\n", "            max_z_score_reso,\n", "            # x.chats,\n", "            # x.refunded_chats,\n", "        ),\n", "        axis=1,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "38dc8b0f-a85a-4b06-a2bb-e3960db31716", "metadata": {}, "outputs": [], "source": ["df_reso[\"normal_z_Resol_perc\"] = 1 - df_reso[\"normal_z_Resol_perc\"]\n", "df_reso[\"normal_z_ref_reso\"] = 1 - df_reso[\"normal_z_ref_reso\"]\n", "\n", "df_reso\n", "# # df_reso.to_csv('resolution_score_raw.csv')\n", "# # dataframe.query('agent_email == \"<EMAIL>\"')"]}, {"cell_type": "code", "execution_count": null, "id": "648d29ea-6a03-44db-ad69-07c062462806", "metadata": {}, "outputs": [], "source": ["df1 = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "id": "2abc2258-9763-4dcf-86a9-12521d417436", "metadata": {}, "outputs": [], "source": ["def weightedNormal(val, wt):\n", "    return (val * wt).sum() / wt.sum()\n", "\n", "\n", "df1[\"Resoltn_score\"] = df_reso.groupby([\"month\", \"agent_email\"]).apply(\n", "    lambda x: weightedNormal(x.normal_z_Resol_perc, x.chats)\n", ")\n", "df1[\"ref_reso_score\"] = df_reso.groupby([\"month\", \"agent_email\"]).apply(\n", "    lambda x: weightedNormal(x.normal_z_ref_reso, x.chats)\n", ")\n", "# df1['avg_resoltn_perc'] = df_reso.groupby(['week','agent_email']).apply(lambda x: weightedNormal(x.Resoltn_perc,x.chats))\n", "# df1['avg_ref_reso'] = df_reso.groupby(['week','agent_email']).apply(lambda x: weightedNormal(x.ref_reso,x.chats))\n", "# df1['avg_refunded_perc'] = df_reso.groupby(['week','agent_email']).apply(lambda x: weightedNormal(x.refunded_perc,x.resoltn_given_chats))"]}, {"cell_type": "code", "execution_count": null, "id": "438d09dc-66a9-45a5-9544-49809a794dfd", "metadata": {}, "outputs": [], "source": ["df1"]}, {"cell_type": "code", "execution_count": null, "id": "0bbeacd6-d840-4262-99ed-84e9b3a13a85", "metadata": {}, "outputs": [], "source": ["df1[\"resoltn_given_chats\"] = df_reso.groupby([\"month\", \"agent_email\"])[\"resoltn_given_chats\"].sum()\n", "# df1[\"chats\"] = df_reso.groupby([\"month\", \"agent_email\"])[\"chats\"].sum()\n", "df1[\"refunded_chats\"] = df_reso.groupby([\"month\", \"agent_email\"])[\"refunded_chats\"].sum()\n", "\n", "# df1[\"Resoltn_score\"] = df1[\"resoltn_given_chats\"] / df1[\"chats\"].replace(0, np.nan)\n", "# df1[\"ref_reso_score\"] = df1[\"refunded_chats\"] / df1[\"chats\"].replace(0, np.nan)"]}, {"cell_type": "code", "execution_count": null, "id": "01c7c338-7683-45ba-9369-1892cc936dba", "metadata": {}, "outputs": [], "source": ["# df1[\"Resoltn_score\"] = 1 - df1[\"resoltn_given_chats\"] / df1[\"chats\"].replace(0, np.nan)\n", "# df1[\"ref_reso_score\"] = 1 - df1[\"refunded_chats\"] / df1[\"chats\"].replace(0, np.nan)"]}, {"cell_type": "code", "execution_count": null, "id": "5ca0917b-2f37-4771-8436-2895bb47996f", "metadata": {}, "outputs": [], "source": ["df1"]}, {"cell_type": "code", "execution_count": null, "id": "6356c313-ba85-482d-b1b4-ddbfc2e9c1b0", "metadata": {}, "outputs": [], "source": ["# df1.to_csv('score.csv')"]}, {"cell_type": "markdown", "id": "3b95c75c-b7a4-4a15-b082-2c2f987e217a", "metadata": {}, "source": ["### Chats/hour"]}, {"cell_type": "code", "execution_count": null, "id": "0655e1b1-755b-424b-943e-98df18678af5", "metadata": {}, "outputs": [], "source": ["Chat_per_hr = f\"\"\"With\n", "\n", "Master_table_base as(\n", "Select\n", "extract(month from created_date) as month,\n", "extract(hour from created_at) as hrs,\n", "*\n", "from cd_etls.os_agent_sessions\n", "-- where created_date between current_date - interval '7' day and CURRENT_DATE - INTERVAL '2' DAY \n", "where created_date between  DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1' DAY) AND CURRENT_DATE - INTERVAL '1' DAY\n", "--and agent_tenure > 30\n", "-- limit 1000 \n", ")\n", "\n", "\n", ",total_chats as\n", "(\n", "Select\n", "month,\n", "hrs,\n", "created_date,\n", "CASE WHEN (lower(site_name) like '%%pte%%') THEN 'PTE' ELSE 'FTE' END as employment_type,\n", "case when channel_name in ('Gold 19-20 delivered order','Gold 19-20 live order') then 'Gold 19-20' else 'Non-Gold 19-20' end as channel_flag,\n", "lower(agent_email) as agent_email,\n", "count(distinct chat_history_id) as total_chats\n", "from Master_table_base\n", "where channel_name not in ('Gold 19-20 Image Verification','Image Verification')\n", "group by 1,2,3,4,5,6\n", ")\n", "\n", ",total_chats_overall as\n", "(\n", "Select\n", "month,\n", "agent_email,\n", "sum(total_chats) as overall_chats\n", "from total_chats\n", "group by 1,2\n", ")\n", ",\n", "\n", "z_prod_hours as (\n", "select \n", "agentid as agent_id,\n", "role,\n", "businessid as business_id,\n", "from_unixtime(if(length(try_cast(createdat as varchar))<=12,createdat,createdat/1000)) as created_at,\n", "t.type,\n", "t.secondcount as second_count,\n", "from_unixtime(t.slotstarttimestamp) as slot_start_timestamp,\n", "from_unixtime(t.slotendtimestamp) as slot_end_timestamp,\n", "format_datetime(from_unixtime(if(length(try_cast(createdat as varchar))<=12,createdat,createdat/1000)),'yyyyMMdd') as dt  /*to avoid cases mongo was synced at UTC time*/\n", "from zomato.mongo_blinkitchat.blinkit_production_hours\n", "cross join unnest(productionhours) as t\n", "-- where dt >= Replace(cast(current_date - interval '7' day as varchar),'-','') and dt<=Replace(cast(CURRENT_DATE - INTERVAL '2' DAY as varchar),'-','')\n", "WHERE dt BETWEEN REPLACE(CAST(DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1' DAY) AS VARCHAR), '-', '') \n", "             AND REPLACE(CAST(CURRENT_DATE - INTERVAL '1' DAY AS VARCHAR), '-', '')\n", "\n", ")\n", "\n", "-- 2024-12-23 12:30:00.000 Asia/Kolkata\n", "-- 2024-12-23 13:00:00.000 Asia/Kolkata\n", "-- 20250104    \n", "-- cqqvm5ema5rc738t10u0    agent   2\t    2025-01-04 00:49:31.000 Asia/Kolkata    0\t                        2024-12-23 13:00:00.000 Asia/Kolkata    2024-12-23 13:30:00.000 Asia/Kolkata    20250104\n", "-- chj08gema5rc1sa5sbdg    agent   2\t    2025-01-02 16:02:08.000 Asia/Kolkata          active_hour 290\t    2024-12-28 19:00:00.000 Asia/Kolkata    2024-12-28 19:30:00.000 Asia/Kolkata    20250102\n", "-- cofaeomma5rc73afjjf0    agent   2\t    2024-12-30 12:34:07.000 Asia/Kolkata    0\t                        2024-12-28 22:00:0\n", "\n", "\n", "\n", ",\n", "z_final as\n", "(\n", "select \n", "p.agent_id,\n", "a.email,\n", "p.role,\n", "p.business_id,\n", "b.name as business_name,\n", "v.name as vendor_name,\n", "s.name as site_name,\n", "p.created_at,\n", "p.type,\n", "p.second_count,\n", "p.slot_start_timestamp,\n", "p.slot_end_timestamp,\n", "p.dt,\n", "SUBSTR(p.dt, 1, 4) || '-' || SUBSTR(p.dt, 5, 2) || '-' || SUBSTR(p.dt, 7, 2) as date_\n", "from z_prod_hours p\n", "left join zomato.blinkitchatdb.agent_site_mappings asm on p.agent_id=asm.agent_id\n", "left join zomato.blinkitchatdb.agents a on p.agent_id=a.agent_id\n", "left join zomato.blinkitchatdb.agent_business_mappings abm on a.agent_id=abm.agent_id\n", "left join zomato.blinkitchatdb.businesses b on abm.business_id=b.id\n", "left join zomato.blinkitchatdb.sites s on asm.site_id=s.id\n", "left join zomato.blinkitchatdb.vendors v on a.vendor_id=v.id \n", ")\n", ",\n", "\n", "\n", "final as\n", "(\n", "select \n", "lower(email) as agent_email,\n", "CASE WHEN (lower(site_name) like '%%pte%%') THEN 'PTE' ELSE 'FTE' END as employment_type,\n", "cast(slot_start_timestamp as date) as ticket_date,\n", "extract(hour from slot_start_timestamp) as hrs,\n", "type,\n", "second_count,\n", "date_\n", "from z_final \n", "where type = 'active_hour'\n", "and role = 'agent'\n", "and business_id = 2\n", ")\n", ",\n", "\n", "\n", "minute_sum as\n", "(\n", "select \n", "extract(month from ticket_date) as month,\n", "a.agent_email,\n", "employment_type,\n", "-- hrs,\n", "-- dt,\n", "sum(second_count)/60 as total_minutes\n", "from final a\n", "    inner join (\n", "            Select agent_email \n", "            from Master_table_base \n", "            group by 1) b \n", "        on a.agent_email = lower(b.agent_email)\n", "where --ticket_date  >= current_date - interval '30' day\n", "-- ticket_date between current_date - interval '7' day and CURRENT_DATE - INTERVAL '2' DAY \n", "-- where \n", "ticket_date between  DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1' DAY) AND CURRENT_DATE - INTERVAL '1' DAY\n", "GROUP BY 1,2,3\n", ")\n", ",\n", "\n", "\n", "active_mins_slots as \n", "(\n", "Select\n", "extract(month from ticket_date) as month,\n", "hrs,\n", "date_,\n", "employment_type,\n", "lower(agent_email) as agent_email,\n", "sum(second_count)/60 as total_active_mins\n", "from final\n", "group by 1,2,3,4,5\n", ")\n", ",\n", "\n", "\n", "chat_per_hour_base as\n", "(\n", "Select\n", "a.month,\n", "a.hrs,\n", "a.created_date,\n", "a.agent_email,\n", "a.employment_type,\n", "a.channel_flag,\n", "a.total_chats,\n", "total_active_mins,\n", "(total_chats)*60.00/(total_active_mins) as chats_per_hr\n", "from total_chats a\n", "    left join active_mins_slots b \n", "        on a.agent_email = b.agent_email \n", "        and a.month = b.month\n", "        and a.hrs = b.hrs\n", "        and a.created_date=cast(b.date_ as date)\n", "        and a.employment_type=b.employment_type\n", "    left join total_chats_overall c \n", "        on a.month = c.month \n", "        and a.agent_email = c.agent_email\n", "left join (\n", "        Select \n", "            month,\n", "            agent_email, \n", "            sum(total_minutes) as overall_mins \n", "        from minute_sum group by 1,2) d \n", "    on a.month = d.month \n", "    and a.agent_email = d.agent_email\n", "where overall_chats > 15\n", "and overall_mins > 150\n", "and total_active_mins > 45\n", ")\n", "\n", "Select * from chat_per_hour_base\n", "-- where agent_email='<EMAIL>'\n", "--where total_chats > 5\n", "-- order by 1,2,3,4\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "cc4b9c78-eba2-48b6-b800-5a71ca8e2e3c", "metadata": {}, "outputs": [], "source": ["df_prodMins = pd.read_sql_query(Chat_per_hr, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "5c7a5fd0-4ce5-4c30-8aa1-36ca8070d78a", "metadata": {}, "outputs": [], "source": ["df_prodMins"]}, {"cell_type": "code", "execution_count": null, "id": "efc3f8e4-5530-4328-983e-e9b7652dd78c", "metadata": {}, "outputs": [], "source": ["df_prodMins[\"chats_per_hr\"] = df_prodMins[\"chats_per_hr\"].astype(\"float64\")\n", "# df_reso['ref_reso'] = df_reso['ref_reso'].astype('float64')"]}, {"cell_type": "code", "execution_count": null, "id": "b4f826de-7899-4e0d-b184-389cd88b0efc", "metadata": {}, "outputs": [], "source": ["prod_df_mean = df_prodMins.groupby(\n", "    [\"hrs\", \"created_date\", \"employment_type\", \"channel_flag\"]\n", ").mean()\n", "prod_df_std = df_prodMins.groupby([\"hrs\", \"created_date\", \"employment_type\", \"channel_flag\"]).std()"]}, {"cell_type": "code", "execution_count": null, "id": "1a383f3a-d591-4ff4-9c1b-28f4a7a5c8eb", "metadata": {}, "outputs": [], "source": ["# df_prodMins.query('agent_email == \"<EMAIL>\"')\n", "prod_df_mean"]}, {"cell_type": "code", "execution_count": null, "id": "bcd42ea7-de3d-4d84-9f9c-929631708bc6", "metadata": {}, "outputs": [], "source": ["def getZScore_1(\n", "    feature,\n", "    val,\n", "    hrs,\n", "    created_date,\n", "    employment_type,\n", "    channel_flag,\n", "    ref_df_mean,\n", "    ref_df_std,\n", "    rated,\n", "    csat_abs,\n", "):\n", "    mean = ref_df_mean[[feature]].loc[hrs, created_date, employment_type, channel_flag].values[0]\n", "    std = ref_df_std[[feature]].loc[hrs, created_date, employment_type, channel_flag].values[0]\n", "    z = (val - mean) / std\n", "    return z"]}, {"cell_type": "code", "execution_count": null, "id": "3d588493-2450-4f86-a95c-10884e226797", "metadata": {}, "outputs": [], "source": ["df_prodMins[\"z_chats_per_hr\"] = df_prodMins.apply(\n", "    lambda x: getZScore_1(\n", "        \"chats_per_hr\",\n", "        x[\"chats_per_hr\"],\n", "        x.hrs,\n", "        x.created_date,\n", "        x.employment_type,\n", "        x.channel_flag,\n", "        prod_df_mean,\n", "        prod_df_std,\n", "        x.total_chats,\n", "        x.total_active_mins,\n", "    ),\n", "    axis=1,\n", ")\n", "\n", "# df_reso['z_ref_reso'] = df_reso.apply(lambda x: getZScore('ref_reso',x['ref_reso'],x.new_karma_label,reso_df_mean,reso_df_std,x.chats,x.refunded_chats),axis = 1)"]}, {"cell_type": "code", "execution_count": null, "id": "0cc92f91-0f04-4038-89b7-b1480f31f092", "metadata": {}, "outputs": [], "source": ["# df_prodMins.query('agent_email == \"<EMAIL>\"')\n", "df_prodMins"]}, {"cell_type": "code", "execution_count": null, "id": "7e85349a-29d8-4137-9246-505f082fc544", "metadata": {}, "outputs": [], "source": ["min_z_score_cht = df_prodMins.groupby(\n", "    [\"hrs\", \"created_date\", \"employment_type\", \"channel_flag\"]\n", ").min()\n", "max_z_score_cht = df_prodMins.groupby(\n", "    [\"hrs\", \"created_date\", \"employment_type\", \"channel_flag\"]\n", ").max()"]}, {"cell_type": "code", "execution_count": null, "id": "8147fc38-bc54-4e81-ac58-484e5bb3a649", "metadata": {}, "outputs": [], "source": ["# min_z_score_cht[['z_chats_per_hr']].loc[17,'2024-03-12','PTE','Non-Gold 19-20'].values[0]\n", "# df_reso.to_csv('reso.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "0c88331a-6760-45ee-a9b0-9422e97f488a", "metadata": {}, "outputs": [], "source": ["def getFinalscore_1(\n", "    feature, val, hrs, created_date, employment_type, channel_flag, min_val, max_val\n", "):\n", "    min_z = min_val[[feature]].loc[hrs, created_date, employment_type, channel_flag].values[0]\n", "    max_z = max_val[[feature]].loc[hrs, created_date, employment_type, channel_flag].values[0]\n", "    final_score = (val - min_z) / (max_z - min_z)\n", "    return final_score"]}, {"cell_type": "code", "execution_count": null, "id": "5a72db09-5876-4036-ac13-f706f3e07acd", "metadata": {}, "outputs": [], "source": ["df_prodMins[\"normal_chat_per_hr\"] = df_prodMins.apply(\n", "    lambda x: getFinalscore_1(\n", "        \"z_chats_per_hr\",\n", "        x[\"z_chats_per_hr\"],\n", "        x.hrs,\n", "        x.created_date,\n", "        x.employment_type,\n", "        x.channel_flag,\n", "        min_z_score_cht,\n", "        max_z_score_cht,\n", "    ),\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ae7d4d3b-cc4a-4d34-8a30-407d15ccf8b7", "metadata": {}, "outputs": [], "source": ["df_prodMins"]}, {"cell_type": "code", "execution_count": null, "id": "4df05fde-d912-4961-964e-86fa7e0ead94", "metadata": {}, "outputs": [], "source": ["display(df_prodMins[\"normal_chat_per_hr\"].min())\n", "display(df_prodMins[\"normal_chat_per_hr\"].max())"]}, {"cell_type": "code", "execution_count": null, "id": "8b88d266-73de-40ec-8257-9ccb5470fe3c", "metadata": {}, "outputs": [], "source": ["# def weightedNormal(val,wt):\n", "#     return (val * wt).sum() / wt.sum()\n", "df2 = pd.DataFrame()\n", "df2[\"Chat_per_hour_score\"] = df_prodMins.groupby([\"month\", \"agent_email\"]).apply(\n", "    lambda x: weightedNormal(x.normal_chat_per_hr, x.total_chats)\n", ")\n", "# df2['ref_reso'] = df_reso.groupby(['agent_email']).apply(lambda x: weightedNormal(x.normal_z_ref_reso,x.chats))"]}, {"cell_type": "code", "execution_count": null, "id": "1d6e0a93-33da-4fc9-a5fc-56b7019b8859", "metadata": {}, "outputs": [], "source": ["df2"]}, {"cell_type": "markdown", "id": "dfe7677c-358c-4423-911d-e4d74944487b", "metadata": {}, "source": ["### Active Mins"]}, {"cell_type": "code", "execution_count": null, "id": "bc0df4be-d360-4d1b-8d96-c67d693bad64", "metadata": {}, "outputs": [], "source": ["active_mins = f\"\"\"\n", "With\n", "\n", "Master_table_base as(\n", "Select\n", "extract(month from created_date) as month,\n", "extract(hour from created_at) as hrs,\n", "*\n", "from cd_etls.os_agent_sessions\n", "-- where created_date between current_date - interval '7' day and CURRENT_DATE - INTERVAL '2' DAY \n", "where created_date between  DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1' DAY) AND CURRENT_DATE - INTERVAL '1' DAY\n", "--and agent_tenure > 30\n", "),\n", "\n", "total_chats_overall as(\n", "Select\n", "month,\n", "agent_email,\n", "count(distinct chat_history_id) as overall_chats\n", "from Master_table_base\n", "group by 1,2\n", "),\n", "\n", "z_prod_hours as (\n", "select \n", "agentid as agent_id,\n", "role,\n", "businessid as business_id,\n", "from_unixtime(if(length(try_cast(createdat as varchar))<=12,createdat,createdat/1000)) as created_at,\n", "t.type,\n", "t.secondcount as second_count,\n", "from_unixtime(t.slotstarttimestamp) as slot_start_timestamp,\n", "from_unixtime(t.slotendtimestamp) as slot_end_timestamp,\n", "format_datetime(from_unixtime(if(length(try_cast(createdat as varchar))<=12,createdat,createdat/1000)),'yyyyMMdd') as dt  /*to avoid cases mongo was synced at UTC time*/\n", "from zomato.mongo_blinkitchat.blinkit_production_hours\n", "cross join unnest(productionhours) as t\n", "where  dt >= Replace(cast(DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1' DAY) as varchar),'-','')\n", "AND dt <= REPLACE(CAST(CURRENT_DATE - INTERVAL '1' DAY AS VARCHAR), '-', '')\n", "\n", "\n", "),\n", "\n", "\n", "z_final as (\n", "select \n", "p.agent_id,\n", "a.email,\n", "p.role,\n", "p.business_id,\n", "b.name as business_name,\n", "v.name as vendor_name,\n", "s.name as site_name,\n", "p.created_at,\n", "p.type,\n", "p.second_count,\n", "p.slot_start_timestamp,\n", "p.slot_end_timestamp,\n", "p.dt\n", "from z_prod_hours p\n", "left join zomato.blinkitchatdb.agent_site_mappings asm on p.agent_id=asm.agent_id\n", "left join zomato.blinkitchatdb.agents a on p.agent_id=a.agent_id\n", "left join zomato.blinkitchatdb.agent_business_mappings abm on a.agent_id=abm.agent_id\n", "left join zomato.blinkitchatdb.businesses b on abm.business_id=b.id\n", "left join zomato.blinkitchatdb.sites s on asm.site_id=s.id\n", "left join zomato.blinkitchatdb.vendors v on a.vendor_id=v.id \n", "),\n", "\n", "--vendor name\n", "final as(\n", "select \n", "agent_id,\n", "lower(email) as agent_email,\n", "role,\n", "business_id,\n", "business_name,\n", "site_name,\n", "CASE WHEN (lower(site_name) like '%%pte%%') THEN 'PTE' ELSE 'FTE' END as employment_type,\n", "vendor_name,\n", "created_at,\n", "extract(month from cast(slot_start_timestamp as date)) as month,\n", "cast(slot_start_timestamp as date) as ticket_date,\n", "extract(hour from slot_start_timestamp) as hrs,\n", "type,\n", "second_count,\n", "slot_start_timestamp,\n", "slot_end_timestamp,\n", "(dt)\n", "from z_final \n", "where type = 'active_hour'\n", "and role = 'agent'\n", "and business_id = 2\n", ")\n", "\n", "--Select distinct  vendor_name from final\n", "\n", "\n", ",overall_mins as(\n", "Select\n", "month,\n", "agent_email,\n", "employment_type,\n", "--case when vendor_name = 'V5 Global' and employment_type='PTE' then 1275\n", "--    when vendor_name = 'Niftel' and employment_type='PTE' then 1425\n", "--    when vendor_name = 'Niftel' and employment_type='FTE' then 2400\n", "--    when vendor_name = 'Startek' and employment_type='FTE' then 2400\n", "--    when vendor_name = 'Niftel' and employment_type='FTE' then 2400\n", "--    end as total_required_mins,\n", "case when vendor_name = 'V5 Global' and employment_type='PTE' then 7280\n", "    when vendor_name = 'Niftel' and employment_type='PTE' then 7280\n", "    when vendor_name = 'Niftel' and employment_type='FTE' then 12870\n", "    when vendor_name = 'Startek' and employment_type='FTE' then 12870\n", "    when vendor_name = 'Teleperformance' and employment_type='FTE' then 12870\n", "    else 12870\n", "     end as total_required_mins,\n", "sum(second_count)/60 as total_active_mins\n", "from final\n", "group by 1,2,3,4\n", ")\n", "\n", "--Select * from overall_mins\n", "\n", "--minute_sum as(\n", "select \n", "a.*,\n", "total_active_mins*100.00/total_required_mins as active_perc\n", "from overall_mins a\n", "inner join total_chats_overall b \n", "    on a.agent_email = lower(b.agent_email)\n", "    and a.month = b.month\n", "    and overall_chats > 15\n", "where  total_active_mins > 45\n", "and total_required_mins is not null\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "d587dec3-feef-433d-bed8-acd580e4b3c5", "metadata": {}, "outputs": [], "source": ["df_active_mins = pd.read_sql_query(active_mins, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "d4c85f17-7a8f-4003-a652-a649124af841", "metadata": {}, "outputs": [], "source": ["df_active_mins"]}, {"cell_type": "code", "execution_count": null, "id": "62cf42c1-899a-444c-888a-f3d25d2b7f86", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2d14ea47-b920-4cfb-a284-8f72af8ac43d", "metadata": {}, "outputs": [], "source": ["df_active_mins[\"active_perc\"] = df_active_mins[\"active_perc\"].astype(\"float64\")\n", "df_active_mins[\"active_perc\"] = np.where(\n", "    df_active_mins[\"active_perc\"] > 100, 100, df_active_mins[\"active_perc\"]\n", ")\n", "# df_reso['ref_reso'] = df_reso['ref_reso'].astype('float64')"]}, {"cell_type": "code", "execution_count": null, "id": "150e4fa9-0530-46ec-8022-4eea684b6892", "metadata": {}, "outputs": [], "source": ["total_mins_df_mean = df_active_mins.groupby([\"employment_type\"]).mean()\n", "total_mins_df_std = df_active_mins.groupby([\"employment_type\"]).std()"]}, {"cell_type": "code", "execution_count": null, "id": "b15a81b6-002f-4015-a8b7-8dbde531a9f1", "metadata": {}, "outputs": [], "source": ["total_mins_df_mean"]}, {"cell_type": "code", "execution_count": null, "id": "6255099f-e20f-4af2-b411-c1f304800753", "metadata": {}, "outputs": [], "source": ["def getZScore_2(feature, val, channel_name, ref_df_mean, ref_df_std):\n", "    mean = ref_df_mean[[feature]].loc[channel_name].values[0]\n", "    std = ref_df_std[[feature]].loc[channel_name].values[0]\n", "    z = (val - mean) / std\n", "\n", "    return z"]}, {"cell_type": "code", "execution_count": null, "id": "fa54aa4e-3a10-4911-b535-6b3e820354a7", "metadata": {}, "outputs": [], "source": ["df_active_mins[\"z_active_perc\"] = df_active_mins.apply(\n", "    lambda x: getZScore_2(\n", "        \"active_perc\",\n", "        x[\"active_perc\"],\n", "        x.employment_type,\n", "        total_mins_df_mean,\n", "        total_mins_df_std,\n", "    ),\n", "    axis=1,\n", ")\n", "\n", "# df_reso['z_ref_reso'] = df_reso.apply(lambda x: getZScore('ref_reso',x['ref_reso'],x.new_karma_label,reso_df_mean,reso_df_std,x.chats,x.refunded_chats),axis = 1)"]}, {"cell_type": "code", "execution_count": null, "id": "0d9ef919-ec59-40f3-9f62-84d8f489863c", "metadata": {}, "outputs": [], "source": ["# df_reso.query('agent_email == \"<EMAIL>\"')\n", "df_active_mins"]}, {"cell_type": "code", "execution_count": null, "id": "eade8a31-4f9b-466e-b545-3fdefea0856f", "metadata": {}, "outputs": [], "source": ["min_z_score_active_mins = df_active_mins.groupby([\"employment_type\"]).min()\n", "max_z_score_active_mins = df_active_mins.groupby([\"employment_type\"]).max()"]}, {"cell_type": "code", "execution_count": null, "id": "df5b93ec-12a1-4dd4-8371-804a1977ebaf", "metadata": {}, "outputs": [], "source": ["# max_z_score_reso\n", "# df_reso.to_csv('reso.csv')\n", "max_z_score_active_mins"]}, {"cell_type": "code", "execution_count": null, "id": "2cf70e79-f701-4f32-8652-6e44cf2a8ee4", "metadata": {}, "outputs": [], "source": ["def getFinalscore_2(feature, val, channel_name, min_val, max_val):\n", "\n", "    min_z = min_val[[feature]].loc[channel_name].values[0]\n", "    max_z = max_val[[feature]].loc[channel_name].values[0]\n", "    final_score = (val - min_z) / (max_z - min_z)\n", "\n", "    return final_score"]}, {"cell_type": "code", "execution_count": null, "id": "5c2699b7-3c2a-4dbb-b8b1-a946240653f9", "metadata": {}, "outputs": [], "source": ["# for i in ['z_Resol_perc','z_ref_reso']:\n", "df_active_mins[f\"normal_z_active_perc\"] = df_active_mins.apply(\n", "    lambda x: getFinalscore_2(\n", "        \"z_active_perc\",\n", "        x.z_active_perc,\n", "        x.employment_type,\n", "        min_z_score_active_mins,\n", "        max_z_score_active_mins,\n", "    ),\n", "    axis=1,\n", ")\n", "# dataframe[f'normal_{i}'] = dataframe.apply(lambda x: getFinalscore(i,x[i],x.channel_name,min_z_score,max_z_score,x.rated,x.AVG_CSAT),axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "c53618f3-3664-4b5a-b05a-cdd96b9cefc6", "metadata": {}, "outputs": [], "source": ["df_active_mins"]}, {"cell_type": "code", "execution_count": null, "id": "ff17f0ac-a798-49fe-a75a-43b3ae45604c", "metadata": {}, "outputs": [], "source": ["# def weightedNormal(val,wt):\n", "#     return (val * wt).sum() / wt.sum()\n", "df3 = pd.DataFrame()\n", "df3[\"active_perc_score\"] = df_active_mins.groupby([\"month\", \"agent_email\"]).apply(\n", "    lambda x: weightedNormal(x.normal_z_active_perc, x.total_required_mins)\n", ")\n", "# df3['ref_reso'] = df_active_mins.groupby(['agent_email']).apply(lambda x: weightedNormal(x.normal_z_ref_reso,x.chats))"]}, {"cell_type": "code", "execution_count": null, "id": "14c5728c-1a5c-4e5b-ae5f-8647543f971e", "metadata": {}, "outputs": [], "source": ["df3"]}, {"cell_type": "code", "execution_count": null, "id": "4d411c16-879b-4cc3-9c90-e9d72287ee17", "metadata": {}, "outputs": [], "source": ["result = pd.merge(df, df1, on=[\"month\", \"agent_email\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "2a230ba2-ecf0-47c4-9483-63874e21f95a", "metadata": {}, "outputs": [], "source": ["result = pd.merge(result, df2, on=[\"month\", \"agent_email\"], how=\"left\")\n", "result = pd.merge(result, df3, on=[\"month\", \"agent_email\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "87d2a35a-d9dd-4afc-aa73-100e75f6d93e", "metadata": {}, "outputs": [], "source": ["# result_cuts = result['csat_score'].quantile([0.90,0.75,0.50,0.25])\n", "# result = pd.read_csv(\"result.csv\")\n", "result"]}, {"cell_type": "code", "execution_count": null, "id": "7da87464-fdb8-43ec-a3c8-80b4cca63f83", "metadata": {}, "outputs": [], "source": ["result = result.reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "6594c563-900c-4859-9b84-1b0d1f09a139", "metadata": {}, "outputs": [], "source": ["print(result.columns)"]}, {"cell_type": "code", "execution_count": null, "id": "c2270bbd-b0f7-4b59-9ec1-08d98e4f32a2", "metadata": {}, "outputs": [], "source": ["agent_tenure = f\"\"\"select a.email as agent_email,\n", "--a.created_at,\n", "date_diff('day',a.created_at,current_timestamp) tenure\n", "from zomato.blinkitchatdb.agents a\n", "group by 1,2\"\"\"\n", "\n", "agent_tenure = pd.read_sql_query(agent_tenure, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "47c6f06e-793c-4c5c-a82d-cb78dda1ffec", "metadata": {}, "outputs": [], "source": ["agent_tenure"]}, {"cell_type": "code", "execution_count": null, "id": "427a7ed4-e936-4027-aac9-acaa88e4ff6a", "metadata": {}, "outputs": [], "source": ["# Ensure both DataFrames have lowercase agent_email columns\n", "agent_tenure[\"agent_email\"] = agent_tenure[\"agent_email\"].str.lower()\n", "result[\"agent_email\"] = result[\"agent_email\"].str.lower()\n", "# filtered_tenure = agent_tenure[agent_tenure[\"tenure\"] > 30]\n", "# Merge on the lowercase version\n", "merged_result = pd.merge(result, agent_tenure, on=\"agent_email\", how=\"left\")\n", "\n", "# merged_result"]}, {"cell_type": "code", "execution_count": null, "id": "a7af3f48-ae30-42f0-92b9-7cd60d7cc5b8", "metadata": {}, "outputs": [], "source": ["# merged_result = merged_result.drop(columns=[\"index\"])\n", "merged_result"]}, {"cell_type": "code", "execution_count": null, "id": "fcd86c63-794f-4031-8009-5706e2c3ea5f", "metadata": {}, "outputs": [], "source": ["dfactive = pd.DataFrame()\n", "dfactive[\"active_perc\"] = df_active_mins.groupby([\"month\", \"agent_email\"]).apply(\n", "    lambda x: weightedNormal(x.active_perc, x.total_required_mins)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1bbf476c-7504-4453-8c69-8073d3d435fe", "metadata": {}, "outputs": [], "source": ["dfchat = pd.DataFrame()\n", "dfchat[\"Chat_per_hour\"] = df_prodMins.groupby([\"month\", \"agent_email\"]).apply(\n", "    lambda x: weightedNormal(x.chats_per_hr, x.total_chats)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "47cea05c-8279-40d5-8b05-2f4b1b644f0b", "metadata": {}, "outputs": [], "source": ["dfchat"]}, {"cell_type": "code", "execution_count": null, "id": "536f4f9d-6f65-4435-9cf5-58e1da4b05ac", "metadata": {}, "outputs": [], "source": ["# dfreso = pd.DataFrame()\n", "# dfreso[\"Resoltn_perc\"] = df_reso.groupby([\"month\", \"agent_email\"]).apply(\n", "#     lambda x: weightedNormal(x.<PERSON>n_perc, x.chats)\n", "# )\n", "# dfreso[\"ref_reso\"] = df_reso.groupby([\"month\", \"agent_email\"]).apply(\n", "#     lambda x: weightedNormal(x.ref_reso, x.chats))"]}, {"cell_type": "code", "execution_count": null, "id": "f22a1292-fdfc-42f2-91d4-233ea2f695c0", "metadata": {}, "outputs": [], "source": ["dfcsat = pd.DataFrame()\n", "dfcsat[\"avg_aht\"] = dataframe.groupby([\"month\", \"agent_email\"]).apply(\n", "    lambda x: weightedNormal(x.avg_aht, x.chats)\n", ")\n", "dfcsat[\"avg_agent_frt\"] = dataframe.groupby([\"month\", \"agent_email\"]).apply(\n", "    lambda x: weightedNormal(x.avg_agent_frt, x.chats)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c8cc2c30-7d22-4276-ae46-a944cefb86bb", "metadata": {}, "outputs": [], "source": ["# result2"]}, {"cell_type": "code", "execution_count": null, "id": "942b3ebf-440e-4572-84e7-9e28a4c6bea4", "metadata": {}, "outputs": [], "source": ["result2 = dfcsat.merge(dfchat, on=[\"month\", \"agent_email\"], how=\"left\").merge(\n", "    dfactive, on=[\"month\", \"agent_email\"], how=\"left\"\n", ")  # .merge(dfactive, on=[\"month\", \"agent_email\"], how=\"left\")\n", "\n", "\n", "result2"]}, {"cell_type": "code", "execution_count": null, "id": "9e47054e-3733-4a22-9167-0070ca677cf3", "metadata": {}, "outputs": [], "source": ["# dfcsat['month'].unique()\n", "# dfactive = dfactive.reset_index()\n", "# dfactive['month'].unique()\n", "# print(dfcsat.columns)"]}, {"cell_type": "code", "execution_count": null, "id": "2f07545e-30ab-45ea-928f-17cdaf481fd6", "metadata": {}, "outputs": [], "source": ["result3 = pd.merge(merged_result, result2, on=[\"month\", \"agent_email\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "34b3e813-7ba1-4315-90c7-ea7d3f7b0c6d", "metadata": {}, "outputs": [], "source": ["result3"]}, {"cell_type": "code", "execution_count": null, "id": "6dc84674-cd43-4206-ad24-c97319c5ec49", "metadata": {}, "outputs": [], "source": ["# Get yesterday's date\n", "yesterday = datetime.today().date() - <PERSON><PERSON><PERSON>(days=1)\n", "\n", "# Start of the current month\n", "start_of_month = yesterday.replace(day=1)\n", "\n", "# Calculate number of days between start of month and yesterday (inclusive)\n", "days_count = (yesterday - start_of_month).days + 1  # +1 if you want to include both days\n", "\n", "# print(days_count)\n", "\n", "# Step 4: Compute threshold\n", "threshold = days_count * 25 * (26 / 30)\n", "\n", "# considering no of day in month multiplying with min chat 25 per day * multiplying with 26 day working in 30 days month\n", "\n", "# Step 5: Apply filter\n", "result3 = result3[result3[\"chats\"] > threshold]"]}, {"cell_type": "code", "execution_count": null, "id": "5c1aa659-63d3-4236-abe1-5c8b9d7c735a", "metadata": {}, "outputs": [], "source": ["# # Get yesterday's date\n", "# yesterday = datetime.today().date() - <PERSON><PERSON><PERSON>(days=1)\n", "\n", "# # Start of the current month\n", "# start_of_month = yesterday.replace(day=1)\n", "\n", "# # Calculate number of days between start of month and yesterday (inclusive)\n", "# days_count = (yesterday - start_of_month).days + 1  # +1 if you want to include both days\n", "\n", "# print(days_count)"]}, {"cell_type": "code", "execution_count": null, "id": "88084c1b-0947-4ebd-8df8-33d52b712eb6", "metadata": {}, "outputs": [], "source": ["# yesterday = datetime.today() - <PERSON><PERSON><PERSON>(days=1)\n", "# start_of_month = yesterday.replace(day=1)\n", "\n", "# today = pd.to_datetime(datetime.today().date())- <PERSON><PERSON><PERSON>(days=1)\n", "\n", "# date_range = pd.date_range(start=start_of_month, end=today, freq='D')\n", "# date_range"]}, {"cell_type": "code", "execution_count": null, "id": "98f8b1e0-347b-4b88-83e9-cf5909de602a", "metadata": {}, "outputs": [], "source": ["weights = {\n", "    \"Chat_per_hour_score\": 10,\n", "    \"aht_score\": 25,\n", "    \"avg_frt_score\": 5,\n", "    \"csat_score\": 25,\n", "    \"timed_out\": -5,  # Assuming this is in your dataframe\n", "    \"active_perc_score\": 5,\n", "    \"rated_score\": 10,\n", "    \"Resoltn_score\": 10,\n", "    \"ref_reso_score\": 10,\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "b9e3c7ed-2433-4685-9ea9-c160420b1e4a", "metadata": {}, "outputs": [], "source": ["result3[\"Agent_Score\"] = (\n", "    result3[\"Chat_per_hour_score\"] * 10\n", "    + result3[\"aht_score\"] * 25\n", "    + result3[\"avg_frt_score\"] * 5\n", "    + result3[\"csat_perc\"] * 25\n", "    + result3[\"timed_out\"] * -5\n", "    + result3[\"active_perc_score\"] * 5\n", "    + result3[\"rated_perc\"] * 10\n", "    + result3[\"Resoltn_score\"] * 10\n", "    + result3[\"ref_reso_score\"] * 10\n", "    # result3['chats_x'].rank(pct=True) * 5\n", ")\n", "result3"]}, {"cell_type": "code", "execution_count": null, "id": "0ac5b4ac-7431-4f0a-a3da-5f34b41ac1ca", "metadata": {}, "outputs": [], "source": ["agent_tl = f\"\"\"select \n", "\n", "a.email as agent_email,\n", "role\n", "-- --a.created_at,\n", "-- date_diff('day',a.created_at,current_timestamp) tenure\n", "from zomato.blinkitchatdb.agents a\n", "where is_deleted=false\n", "and role<>'agent'\n", "group by 1,2\"\"\"\n", "\n", "agent_tl = pd.read_sql_query(agent_tl, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "b1cd6f4f-bd3e-4671-bcfa-555a35f708b6", "metadata": {}, "outputs": [], "source": ["result4 = result3[~result3[\"agent_email\"].isin(agent_tl[\"agent_email\"])]"]}, {"cell_type": "code", "execution_count": null, "id": "3b15a8cb-7287-4486-bd48-9bad179b5de8", "metadata": {}, "outputs": [], "source": ["# Rank1: For agents with tenure > 30\n", "result4.loc[result4[\"tenure\"] > 30, \"Rank1\"] = (\n", "    result4[result4[\"tenure\"] > 30].groupby(\"month\")[\"Agent_Score\"].rank(ascending=False)\n", ")\n", "\n", "# Rank2: For agents with tenure <= 30\n", "result4.loc[result4[\"tenure\"] <= 30, \"Rank2\"] = (\n", "    result4[result4[\"tenure\"] <= 30].groupby(\"month\")[\"Agent_Score\"].rank(ascending=False)\n", ")\n", "\n", "result4"]}, {"cell_type": "code", "execution_count": null, "id": "6ab95583-1f20-433b-a664-c6cd52d6faf1", "metadata": {}, "outputs": [], "source": ["result4 = result4.copy()\n", "result4[\"aht_wt_score\"] = result4[\"aht_score\"].rank(pct=True) * 25\n", "result4[\"Chat_per_hour_wt_score\"] = result4[\"Chat_per_hour_score\"].rank(pct=True) * 10\n", "result4[\"avg_frt_wt_score\"] = result4[\"avg_frt_score\"].rank(pct=True) * 5\n", "result4[\"csat_wt_score\"] = result4[\"csat_perc\"].rank(pct=True) * 25\n", "result4[\"timed_out_wt_score\"] = result4[\"timed_out\"] * -5\n", "result4[\"active_wt_score\"] = result4[\"active_perc_score\"].rank(pct=True) * 5\n", "result4[\"rated_wt_score\"] = result4[\"rated_perc\"].rank(pct=True) * 10\n", "result4[\"Resoltn_wt_score\"] = result4[\"Resoltn_score\"].rank(pct=True) * 10\n", "result4[\"ref_reso_wt_score\"] = result4[\"ref_reso_score\"].rank(pct=True) * 10\n", "result4[\"chats_wt_score\"] = result4[\"chats\"].rank(pct=True) * 5"]}, {"cell_type": "code", "execution_count": null, "id": "09d1c075-1c51-4f61-9154-0070b2b4aa7d", "metadata": {}, "outputs": [], "source": ["result4_sorted = result4.sort_values(by=[\"Rank1\", \"Rank2\"], ascending=True)\n", "result4_sorted.head(20)"]}, {"cell_type": "code", "execution_count": null, "id": "02bf043c-1414-4358-b257-a1fc836e3ec5", "metadata": {}, "outputs": [], "source": ["# result4_sorted = \"\"\"select * from cd_etls.crm_agent_scorecard_weighted\n", "# order by  rank1\"\"\"\n", "\n", "# result4_sorted = pd.read_sql_query(result4_sorted, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "3b62606a-c9bc-41ef-991f-c6a164c3d0aa", "metadata": {}, "outputs": [], "source": ["# result4_sorted.to_csv('score.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "9a34c684-3349-4d83-9ce1-0e1a46474500", "metadata": {}, "outputs": [], "source": ["# result4_sorted = result4_sorted.drop(columns=[\"chats_y\"])\n", "# result4_sorted.rename(columns={\"chats_x\": \"chats\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "82c8ea58-95f5-48f8-a4ad-a210c949d20f", "metadata": {}, "outputs": [], "source": ["result4_sorted[\"Company\"] = result4_sorted[\"agent_email\"].str.extract(\n", "    r\"@([a-zA-Z0-9\\-]+)\\.(?:com|co\\.in|in)\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6ad66c59-d356-4ec0-8fae-2b3933a75077", "metadata": {}, "outputs": [], "source": ["result4_sorted[\"Company\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "495c188c-6021-4266-bbe1-fdafa919edc2", "metadata": {}, "outputs": [], "source": ["result4_sorted = result4_sorted[result4_sorted[\"Company\"] != \"grofers\"]"]}, {"cell_type": "code", "execution_count": null, "id": "a2396fd4-1ca4-4beb-903d-c6f9a0d67f89", "metadata": {}, "outputs": [], "source": ["result4_sorted[\"date\"] = pd.to_datetime(\"today\").normalize()"]}, {"cell_type": "code", "execution_count": null, "id": "a0d9855c-1383-4198-a633-b1ccdfb64b56", "metadata": {}, "outputs": [], "source": ["filtered = result4_sorted[result4_sorted[\"Rank1\"].notnull()]"]}, {"cell_type": "code", "execution_count": null, "id": "f755d189-d9d5-4a35-b3d7-cc11d4a28fcb", "metadata": {}, "outputs": [], "source": ["filtered"]}, {"cell_type": "code", "execution_count": null, "id": "82b0b08a-1089-4082-bc29-ef1803c08168", "metadata": {}, "outputs": [], "source": ["# result4_sorted.columns\n", "# result4_sorted = result4_sorted.reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "d07a87a3-2b12-4146-802d-430dda3467cc", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"cd_etls\",\n", "    \"table_name\": \"crm_agent_scorecard_weighted\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"month\", \"type\": \"INTEGER\", \"description\": \"week\"},\n", "        {\"name\": \"agent_email\", \"type\": \"VARCHAR\", \"description\": \"agent_email\"},\n", "        {\n", "            \"name\": \"aht_score\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"aht_score\",\n", "        },\n", "        {\n", "            \"name\": \"avg_frt_score\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"avg_frt_score\",\n", "        },\n", "        {\n", "            \"name\": \"csat_perc\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"csat_perc\",\n", "        },\n", "        {\n", "            \"name\": \"rated\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"rated\",\n", "        },\n", "        {\n", "            \"name\": \"timed_out\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"timed_out\",\n", "        },\n", "        {\n", "            \"name\": \"Resoltn_score\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Resoltn_score\",\n", "        },\n", "        {\n", "            \"name\": \"ref_reso_score\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"ref_reso_score\",\n", "        },\n", "        {\n", "            \"name\": \"Chat_per_hour_score\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Chat_per_hour_score\",\n", "        },\n", "        {\n", "            \"name\": \"active_perc_score\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"active_perc_score\",\n", "        },\n", "        {\"name\": \"tenure\", \"type\": \"INTEGER\", \"description\": \"tenure of agent\"},\n", "        {\n", "            \"name\": \"avg_aht\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"avg_aht\",\n", "        },\n", "        {\n", "            \"name\": \"avg_agent_frt\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"avg_agent_frt\",\n", "        },\n", "        {\n", "            \"name\": \"csat\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"csat\",\n", "        },\n", "        {\n", "            \"name\": \"rated_perc\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"rated_perc\",\n", "        },\n", "        {\n", "            \"name\": \"chats\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"chats\",\n", "        },\n", "        {\n", "            \"name\": \"resoltn_given_chats\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"resoltn_given_chats\",\n", "        },\n", "        {\n", "            \"name\": \"refunded_chats\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"refunded_chats\",\n", "        },\n", "        {\n", "            \"name\": \"Chat_per_hour\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Chat_per_hour\",\n", "        },\n", "        {\n", "            \"name\": \"active_perc\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"active_perc\",\n", "        },\n", "        {\n", "            \"name\": \"Agent_Score\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Agent_Score\",\n", "        },\n", "        {\n", "            \"name\": \"Rank1\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Rank1\",\n", "        },\n", "        {\n", "            \"name\": \"Rank2\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Rank2\",\n", "        },\n", "        {\n", "            \"name\": \"aht_wt_score\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"aht_wt_score\",\n", "        },\n", "        {\n", "            \"name\": \"Chat_per_hour_wt_score\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Chat_per_hour_wt_score\",\n", "        },\n", "        {\n", "            \"name\": \"avg_frt_wt_score\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"avg_frt_wt_score\",\n", "        },\n", "        {\n", "            \"name\": \"csat_wt_score\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"csat_wt_score\",\n", "        },\n", "        {\n", "            \"name\": \"timed_out_wt_score\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"timed_out_wt_score\",\n", "        },\n", "        {\n", "            \"name\": \"active_wt_score\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"active_wt_score\",\n", "        },\n", "        {\n", "            \"name\": \"rated_wt_score\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"rated_wt_score\",\n", "        },\n", "        {\n", "            \"name\": \"Resoltn_wt_score\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Resoltn_wt_score\",\n", "        },\n", "        {\n", "            \"name\": \"ref_reso_wt_score\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"ref_reso_wt_score\",\n", "        },\n", "        {\n", "            \"name\": \"chats_wt_score\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"chats_wt_score\",\n", "        },\n", "        {\n", "            \"name\": \"Company\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"Company\",\n", "        },\n", "        {\n", "            \"name\": \"date\",\n", "            \"type\": \"DATE\",\n", "            \"description\": \"hoodie_commit_date\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"month\", \"agent_email\"],\n", "    # \"partition_key\": [\"updated_at_ist\"],\n", "    # \"incremental_key\": \"updated_at_ist\",\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"Chat Support Agents Scores\",\n", "}\n", "pb.to_trino(data_obj=result4_sorted, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "68d00e02-b7c0-49b4-b59a-8d5b2c88b9c5", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1QaPanLofCYwqG3CzUshrjZkm0JMPs_zcIBd2fbDbcYY\"\n", "sheet_name = \"Score Normal Raw Sheet- Non IVA\"\n", "pb.to_sheets(filtered, sheet_id, sheet_name, service_account=\"service_account\")"]}, {"cell_type": "code", "execution_count": null, "id": "dc7e69ad-a65f-4b15-a45f-e31a0587f600", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}