{"cells": [{"cell_type": "code", "execution_count": null, "id": "cb47afe8-2938-4490-8005-d367e748abbb", "metadata": {"papermill": {"duration": 38.823648, "end_time": "2025-03-15T04:07:34.131540", "exception": false, "start_time": "2025-03-15T04:06:55.307892", "status": "completed"}, "tags": []}, "outputs": [], "source": ["!pip install xlsxwriter\n", "!pip install openpyxl"]}, {"cell_type": "code", "execution_count": null, "id": "d6d13e6b-87dc-4802-ba84-a6b5c80bb310", "metadata": {"papermill": {"duration": 2.092467, "end_time": "2025-03-15T04:07:36.310406", "exception": false, "start_time": "2025-03-15T04:07:34.217939", "status": "completed"}, "tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import ast\n", "from datetime import date, timedelta\n", "import zipfile"]}, {"cell_type": "code", "execution_count": null, "id": "5922b13c-fbc0-4a9c-b328-991d0761fc26", "metadata": {"papermill": {"duration": 0.073417, "end_time": "2025-03-15T04:07:36.444880", "exception": false, "start_time": "2025-03-15T04:07:36.371463", "status": "completed"}, "tags": []}, "outputs": [], "source": ["today = date.today()\n", "day = today - <PERSON><PERSON><PERSON>(days=1)\n", "start_date = str(day)\n", "end_date = str(today)"]}, {"cell_type": "code", "execution_count": null, "id": "ffd13107-645c-4c67-b8ee-57746399f80a", "metadata": {}, "outputs": [], "source": ["print(start_date)\n", "print(end_date)"]}, {"cell_type": "code", "execution_count": null, "id": "3e4fafdf-380f-4d82-ae00-7666441441d5", "metadata": {"papermill": {"duration": 0.366047, "end_time": "2025-03-15T04:07:36.906984", "exception": false, "start_time": "2025-03-15T04:07:36.540937", "status": "completed"}, "tags": []}, "outputs": [], "source": ["CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "SHEET_NAME = \"Main Sheet\"\n", "SHEET_ID = \"1Rax7TsY26B0tz3WhIxV0WvoW2FsiJtdqlSStW_-QGBI\"\n", "AUDITOR_SHEET_NAME = \"Main - Auditor Count\"\n", "AUDITOR_COUNT = {}"]}, {"cell_type": "code", "execution_count": null, "id": "6b90e859-8016-4546-ba81-cb7f9b04d27a", "metadata": {"papermill": {"duration": 0.054875, "end_time": "2025-03-15T04:07:37.029372", "exception": false, "start_time": "2025-03-15T04:07:36.974497", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def fetch_sheets(SHEET_ID, SHEET_NAME):\n", "    df = pb.from_sheets(SHEET_ID, SHEET_NAME)\n", "    return df.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "bc86cf9a-746b-427f-834e-20fe43e0ff15", "metadata": {"papermill": {"duration": 0.092024, "end_time": "2025-03-15T04:07:37.200713", "exception": false, "start_time": "2025-03-15T04:07:37.108689", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def create_empty_df():\n", "    return pd.DataFrame({})"]}, {"cell_type": "code", "execution_count": null, "id": "924be380-eab7-4b8a-94d3-573e1022dcf6", "metadata": {"papermill": {"duration": 0.090169, "end_time": "2025-03-15T04:07:37.339638", "exception": false, "start_time": "2025-03-15T04:07:37.249469", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def get_data(query):\n", "    df = pd.read_sql_query(sql=query, con=CON_TRINO)\n", "    return df.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "1ae3d14f-948f-4481-9626-e183b9720748", "metadata": {"papermill": {"duration": 1.289263, "end_time": "2025-03-15T04:07:38.686596", "exception": false, "start_time": "2025-03-15T04:07:37.397333", "status": "completed"}, "tags": []}, "outputs": [], "source": ["try:\n", "    AUDIT_DF = fetch_sheets(SHEET_ID, AUDITOR_SHEET_NAME)\n", "\n", "    try:\n", "        VENDOR_AUDIT_COUNT = int(\n", "            AUDIT_DF.query(\"Vendor == 'qa_sessions'\")[\"Auditor Count\"].values[0]\n", "        )\n", "    except Exception as e:\n", "        print(e)\n", "        VENDOR_AUDIT_COUNT = 30\n", "\n", "    AUDIT_DF = AUDIT_DF.query(\"Vendor != 'qa_sessions'\")\n", "\n", "    AUDITOR_COUNT = AUDIT_DF.set_index(\"Vendor\")[\"Auditor Count\"].fillna(1).astype(\"int\").to_dict()\n", "    print(AUDITOR_COUNT)\n", "except:\n", "    AUDITOR_COUNT = {\n", "        \"startek\": 6,\n", "        \"conneqt\": 0,\n", "        \"teleperformance\": 2,\n", "        \"niftel\": 3,\n", "        \"v5 global\": 3,\n", "        \"bld outsourcing\": 2,\n", "    }\n", "    print(\"Failed AUDITOR_COUNT, restored to primary values\")"]}, {"cell_type": "code", "execution_count": null, "id": "92c02195-5103-44b9-8edd-ca7a60274c93", "metadata": {"papermill": {"duration": 0.069797, "end_time": "2025-03-15T04:07:38.829891", "exception": false, "start_time": "2025-03-15T04:07:38.760094", "status": "completed"}, "tags": []}, "outputs": [], "source": ["ALM_QUERY = f\"\"\"\n", "\n", "WITH conversations AS (\n", "\n", "SELECT\n", "    \n", "    date_format(parse_datetime(dt, 'yyyyMMdd'), '%%Y-%%m-%%d') AS chat_date,\n", "    \n", "    LOWER(REPLACE(agent_email,'blinkit','grofers')) AS agent_email,\n", "    LOWER(vendor_name) as vendor,\n", "    AVG(agent_frt) AS agent_frt_secs,\n", "    AVG(aht) AS aht_secs,\n", "    \n", "    AVG(avg_irt) AS irt,\n", "    AVG(rt_ulrt) AS rt_ulrt, \n", "    AVG(rt_lrt) AS rt_alrt,\n", "    \n", "    COUNT(DISTINCT chat_history_id) as chats_resolved,\n", "    COUNT(DISTINCT CASE WHEN csat IN ('1','2','3','4','5') THEN chat_history_id END) as rated_chats,\n", "    COUNT(DISTINCT CASE WHEN csat IN ('4','5') then chat_history_id end) csat_rated_chats,\n", "    COUNT(DISTINCT CASE WHEN csat IN ('1','2') then chat_history_id end) dsat_rated_chats\n", "\n", "FROM zomato.blinkit_etls.blinkit_one_support_sessions AS OS\n", "WHERE \n", "    dt >= REPLACE('{start_date}','-','') \n", "    AND dt <= REPLACE('{end_date}','-','')\n", "    AND business_id IN (2,24)\n", "\n", "GROUP BY 1,2,3\n", ")\n", "\n", "SELECT\n", "    chat_date,\n", "    agent_email,\n", "    vendor,\n", "    COALESCE(chats_resolved,0) as chats_resolved,\n", "    agent_frt_secs,\n", "    aht_secs,\n", "    irt,\n", "    rt_ulrt,\n", "    rt_alrt,\n", "    rated_chats,\n", "    csat_rated_chats,\n", "    dsat_rated_chats\n", "FROM conversations \n", "ORDER BY 1 DESC, 4 DESC\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "73115b5f-f838-49f7-b252-2c4de53a4d5c", "metadata": {"papermill": {"duration": 0.103964, "end_time": "2025-03-15T04:07:39.010359", "exception": false, "start_time": "2025-03-15T04:07:38.906395", "status": "completed"}, "tags": []}, "outputs": [], "source": ["OS_RAW = f\"\"\"\n", "\n", "WITH os_base AS (\n", "\n", "SELECT  \n", "    created_date,\n", "    session_id,\n", "    chat_history_id,\n", "    order_id,\n", "    user_id,\n", "    conversation_id,\n", "    issue_id,\n", "    queued_at,\n", "    assigned_at,\n", "    agent_unassigned_at,\n", "    resolved_at,\n", "    channel_id,\n", "    channel_name,\n", "    agent_email,\n", "    LOWER(vendor_name) AS vendor,\n", "    site_name,\n", "    resolved_by_client_id,\n", "    resolved_by_name,\n", "    rt_ulrt,\n", "    DATE_DIFF('second',queued_at, assigned_at) AS aat,\n", "    frt,\n", "    agent_frt,\n", "    date_diff('second', assigned_at, agent_unassigned_at) AS aht_secs,\n", "    CASE WHEN channel_name IN ('Gold 19-20 Image Verification','Image Verification') THEN 1 ELSE 0 END AS IVA_flag,\n", "    \n", "    CASE WHEN (lower(site_name) LIKE '%%pte%%') THEN 'PTE' ELSE 'FTE' END AS employment_type,\n", "    \n", "    CASE WHEN agent_tenure BETWEEN 0 AND 10 THEN '0-10 Days'\n", "        WHEN agent_tenure BETWEEN 11 AND 20 THEN '6-14 Days'\n", "        WHEN agent_tenure BETWEEN 21 AND 30 THEN '21-30 Days'\n", "        WHEN agent_tenure BETWEEN 31 AND 50 THEN '31-50 Days'\n", "        WHEN agent_tenure >50 THEN '>50 Days' \n", "    END AS tenure_bucket,\n", "    \n", "    current_skill AS concurrency\n", "\n", "FROM cd_etls.os_agent_sessions \n", "WHERE created_date >= DATE('{start_date}')\n", "AND created_date <= DATE('{end_date}')\n", "\n", "),\n", "\n", "master_table_base AS (\n", "\n", "SELECT \n", "    created_date,\n", "    session_id,\n", "    a.order_id,\n", "    cart_id,\n", "    conversation_id,\n", "    user_id,\n", "    karma,\n", "    karma_score,\n", "    last_channel_name,\n", "    bot_first_node,\n", "    bot_last_node,\n", "    group_disposition,\n", "    issue_item_ids,\n", "    node_journey,\n", "    session_resolved_at,\n", "    rated_at,\n", "    csat,\n", "    CASE \n", "        WHEN resolved_by_client_id = 100 THEN 'Agent'\n", "        WHEN resolved_by_client_id = 200 THEN 'Bot'\n", "        WHEN resolved_by_client_id = 0 THEN 'Chat timed-out'\n", "    ELSE 'not-resolved' \n", "    END AS resolved_by,\n", "    order_create_dt_ist AS order_created_date,\n", "    cart_checkout_ts_ist as cart_checkout_time,\n", "    order_deliver_ts_ist AS order_deliver_time,\n", "    order_cancel_ts_ist AS order_cancel_time,\n", "    eta_shown_mins,\n", "    order_current_status,\n", "    order_type,\n", "    frontend_merchant_id AS merchant_id,\n", "    merchant_name,\n", "    city_name,\n", "    picker_id,\n", "    picker_name,\n", "    partner_id AS rider_id,\n", "    rider_name,\n", "    LISTAGG(agent_disposition,',') WITHIN GROUP (ORDER BY agent_disposition) AS agent_disposition\n", "FROM cd_etls.master_table a\n", "left join (Select cart_checkout_ts_ist, order_id from dwh.fact_sales_order_details where order_create_dt_ist >= date'{start_date}' - interval '10' day\n", " and order_create_dt_ist <= date'{end_date}' group by 1,2) b on a.order_id = b.order_id\n", "WHERE created_date >= DATE'{start_date}' \n", "AND created_date <= DATE'{end_date}'\n", "AND group_disposition IS NOT NULL\n", "GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32\n", "),\n", "\n", "master_base_1 as(\n", "SELECT\n", "    a.created_date,\n", "    a.session_id,\n", "    a.order_id,\n", "    a.cart_id,\n", "    a.conversation_id,\n", "    a.user_id as customer_id,\n", "    a.karma,\n", "    a.karma_score,\n", "    a.last_channel_name,\n", "    a.bot_first_node,\n", "    a.bot_last_node,\n", "    a.agent_disposition,\n", "    a.group_disposition,\n", "    a.issue_item_ids,\n", "    a.node_journey,\n", "    a.session_resolved_at,\n", "    CASE WHEN last_channel_name in ('Gold 19-20 Image Verification','Image Verification') and a.resolved_by = 'Bot'THEN 'iva' ELSE a.resolved_by END AS resolved_by,\n", "    CASE WHEN IVA_flag = 1 then 'iva' ELSE 'non-iva' END AS IVA_flag,\n", "    chat_history_id,\n", "    b.queued_at,\n", "    b.concurrency,\n", "    b.rt_ulrt,\n", "    b.assigned_at,\n", "    b.agent_unassigned_at,\n", "    b.resolved_at,\n", "    b.channel_name,\n", "    lower(agent_email) as agent_email,\n", "    site_name,\n", "    vendor,\n", "    rated_at,\n", "    csat,\n", "    aat AS aat,\n", "    frt AS frt,\n", "    agent_frt AS agent_frt,\n", "    aht_secs AS aht,\n", "    employment_type,\n", "    tenure_bucket,\n", "    order_created_date,\n", "    cart_checkout_time,\n", "    order_deliver_time,\n", "    order_cancel_time,\n", "    eta_shown_mins,\n", "    date_diff('minute',cart_checkout_time,order_deliver_time) as delivery_time,\n", "    order_current_status,\n", "    order_type,\n", "    merchant_id,\n", "    merchant_name,\n", "    city_name,\n", "    picker_id,\n", "    picker_name,\n", "    rider_id,\n", "    rider_name\n", "FROM master_table_base a\n", "LEFT JOIN os_base b ON a.session_id = b.session_id\n", "GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52\n", "),\n", "\n", "\n", "master_base AS\n", "(\n", "SELECT\n", "    *,\n", "    ROW_NUMBER() OVER (PARTITION BY session_id,chat_history_id ORDER BY assigned_at DESC) AS rn\n", "FROM master_base_1\n", "),\n", "\n", "reso_base AS(\n", "\n", "SELECT\n", "    reso_date,\n", "    CAST(session_id AS BIGINT) as session_id,\n", "    lower(email_id) as email_id,\n", "    reso_type,\n", "    order_id,\n", "    cart_id,\n", "    payment_method,\n", "    sum(case when reso_type = 'refund' then resol_amount end) as refund,\n", "    sum(case when reso_type = 'replace' then resol_amount end) as replace\n", "FROM cd_etls.item_wise_reso\n", "WHERE reso_date >= DATE('{start_date}')\n", "    AND reso_date <= DATE('{end_date}')\n", "    AND reason in ('Items','MDND')\n", "GROUP BY 1,2,3,4,5,6,7\n", ")\n", "\n", "\n", "SELECT\n", "    a.*,\n", "    b.reso_date,\n", "    b.payment_method,\n", "    b.reso_type,\n", "    b.refund as refund_amt,\n", "    b.replace as replace_amt\n", "FROM master_base a\n", "LEFT JOIN reso_base b ON a.session_id = b.session_id and a.agent_email = b.email_id\n", "WHERE rn = 1\n", "and resolved_by in ('Agent','iva') and IVA_flag in ('iva','non-iva')\n", "GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,\n", "31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "9b3a924b-c7ae-4364-aa73-2c79284d66ab", "metadata": {"papermill": {"duration": 0.064634, "end_time": "2025-03-15T04:07:39.132853", "exception": false, "start_time": "2025-03-15T04:07:39.068219", "status": "completed"}, "tags": []}, "outputs": [], "source": ["OS_TIMEOUT = f\"\"\"\n", "\n", "WITH base AS (\n", "\n", "SELECT \n", "    TRY_CAST(session_id AS BIGINT) AS session_id,\n", "    conversation_id,\n", "    created_at\n", "FROM \n", "    zomato.blinkit_etls.blinkit_session_csat_details\n", "    \n", "WHERE \n", "    dt >= REPLACE('{start_date}','-','') \n", "    AND dt <= REPLACE('{end_date}','-','') \n", "    AND resolved_by_client_id=0\n", "    AND (resolved_by_name='sys-inactivity-resolve-worker' or resolved_by_name='sys-stuck-chat-resolve-worker')\n", "    AND business_id in (2,24)\n", "),\n", "\n", "csat_base AS\n", "(\n", "SELECT  \n", "    TRY_CAST(session_id AS BIGINT) AS session_id,\n", "    vendor_name,\n", "    fr_given_by,\n", "    final_resolved_at,\n", "    tl_email,\n", "    agent_email\n", "FROM\n", "    zomato.blinkit_etls.blinkit_one_support_sessions\n", "WHERE \n", "    dt >= REPLACE('{start_date}','-','') \n", "    AND dt <= REPLACE('{end_date}','-','') \n", ")\n", "\n", "SELECT\n", "    B.session_id,\n", "    B.conversation_id,\n", "    <PERSON>.created_at,\n", "    LOWER(OS.vendor_name) AS vendor,\n", "    CASE \n", "        WHEN OS.fr_given_by IS NULL THEN agent_email \n", "        ELSE fr_given_by \n", "    END AS agent_email,\n", "    tl_email,\n", "    final_resolved_at\n", "FROM base AS B\n", "JOIN csat_base AS OS ON OS.session_id=B.session_id\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "922da7d5-d33c-402f-b36d-af388fe2748d", "metadata": {"papermill": {"duration": 0.112853, "end_time": "2025-03-15T04:07:39.320805", "exception": false, "start_time": "2025-03-15T04:07:39.207952", "status": "completed"}, "tags": []}, "outputs": [], "source": ["REFUND_DETAILS = f\"\"\"\n", "WITH reso_base AS\n", "(\n", "SELECT \n", "    reso_date,\n", "    session_id,\n", "    status,\n", "    customer_id,\n", "    cart_id,\n", "    order_id,\n", "    merchant_name,\n", "    payment_method,\n", "    karma_score,\n", "    CASE \n", "        WHEN karma_label = 'gold' AND karma_score IN (19,20) THEN 'gold 19-20'\n", "        WHEN karma_label = 'gold' AND karma_score IN (17,18) THEN 'gold 17-18'\n", "    ELSE karma_label\n", "    END AS karma_label,\n", "    resolve_flow,\n", "    partial_amount,\n", "    product_type,\n", "    product_name,\n", "    l1_category,\n", "    l2_category,\n", "    reso_type,\n", "    sub_type,\n", "    complaint_type,\n", "    resol_amount,\n", "    LOWER(email_id) AS agent_email\n", "FROM cd_etls.item_wise_reso\n", "WHERE \n", "    reso_date >= DATE('{start_date}') \n", "    AND reso_date <= DATE('{end_date}') \n", "    AND reason IN ('Items','MDND')\n", "),\n", "\n", "\n", "agent_id_mapping AS\n", "(\n", "SELECT \n", "    agent_id,\n", "    LOWER(agent_email) AS agent_email,\n", "    LOWER(vendor_name) AS vendor\n", "FROM \n", "    zomato.blinkit_etls.blinkit_agent_info\n", "WHERE\n", "    dt >= REPLACE('{start_date}','-','') \n", "    AND dt <= REPLACE('{end_date}','-','') \n", "GROUP BY 1,2,3\n", ")\n", "\n", "SELECT \n", "    rb.*,\n", "    aim.vendor\n", "FROM reso_base rb\n", "LEFT JOIN agent_id_mapping aim ON rb.agent_email = aim.agent_email\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "9f581468-3ff7-4e33-a847-2a9eb58e3ff7", "metadata": {"papermill": {"duration": 0.119928, "end_time": "2025-03-15T04:07:39.516922", "exception": false, "start_time": "2025-03-15T04:07:39.396994", "status": "completed"}, "tags": []}, "outputs": [], "source": ["AGENT_PRODUCTIVITY = f\"\"\"\n", "\n", "WITH z_prod_hours AS\n", "(\n", "SELECT \n", "    agentid as agent_id,\n", "    role,\n", "    businessid as business_id,\n", "    FROM_UNIXTIME(IF(LENGTH(TRY_CAST(createdat AS VARCHAR))<=12,createdat,createdat/1000)) AS created_at,\n", "    t.type,\n", "    t.secondcount as second_count,\n", "    FROM_UNIXTIME(t.slotstarttimestamp) AS slot_start_timestamp,\n", "    FROM_UNIXTIME(t.slotendtimestamp) AS slot_end_timestamp,\n", "    format_datetime(FROM_UNIXTIME(IF(LENGTH(TRY_CAST(createdat as varchar))<=12,createdat,createdat/1000)),'yyyyMMdd') AS dt \n", "FROM zomato.mongo_blinkitchat.blinkit_production_hours\n", "CROSS JOIN UNNEST(productionhours) AS t\n", "WHERE\n", "    dt >= REPLACE('{start_date}','-','')\n", "    AND dt <= REPLACE('{end_date}','-','')\n", "),\n", "\n", "z_final as (\n", "SELECT \n", "    p.agent_id,\n", "    a.email,\n", "    p.role,\n", "    p.business_id,\n", "    b.name as business_name,\n", "    v.name as vendor_name,\n", "    s.name as site_name,\n", "    p.created_at,\n", "    p.type,\n", "    p.second_count,\n", "    p.slot_start_timestamp,\n", "    p.slot_end_timestamp,\n", "    p.dt\n", "FROM z_prod_hours p\n", "LEFT JOIN zomato.blinkitchatdb.agent_site_mappings asm ON p.agent_id=asm.agent_id\n", "LEFT JOIN zomato.blinkitchatdb.agents a ON p.agent_id=a.agent_id\n", "LEFT JOIN zomato.blinkitchatdb.agent_business_mappings abm ON a.agent_id=abm.agent_id\n", "LEFT JOIN zomato.blinkitchatdb.businesses b ON abm.business_id=b.id\n", "LEFT JOIN zomato.blinkitchatdb.sites s ON asm.site_id=s.id\n", "LEFT JOIN zomato.blinkitchatdb.vendors v ON a.vendor_id=v.id \n", "),\n", "\n", "final AS(\n", "SELECT  \n", "    email,\n", "    role,\n", "    business_id,\n", "    business_name,\n", "    site_name,\n", "    LOWER(vendor_name) AS vendor,\n", "    TRY_CAST(created_at AS DATE) as ticket_date,\n", "    type,\n", "    second_count,\n", "    slot_start_timestamp,\n", "    slot_end_timestamp,\n", "    (dt)\n", "FROM\n", "    z_final \n", ")\n", "\n", "SELECT * FROM final\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "af35a04e-8140-4563-972c-f6c9f5332aa0", "metadata": {"papermill": {"duration": 0.059698, "end_time": "2025-03-15T04:07:39.624444", "exception": false, "start_time": "2025-03-15T04:07:39.564746", "status": "completed"}, "tags": []}, "outputs": [], "source": ["REASSIGNMENT = f\"\"\"\n", "\n", "WITH base AS (\n", "SELECT \n", "\n", "    landed_at,\n", "    session_id,\n", "    chat_history_id,\n", "    CASE \n", "        WHEN COUNT(*) OVER(PARTITION BY session_id) > 1 THEN TRUE \n", "        ELSE FALSE \n", "    END AS is_reassigned\n", "    \n", "FROM zomato.blinkit_etls.blinkit_one_support_sessions\n", "WHERE \n", "    dt>=REPLACE('{start_date}','-','') \n", "    AND dt<=REPLACE('{end_date}','-','') \n", "    AND business_id in (2,24)\n", "    ),\n", "    \n", "os_base AS\n", "(\n", "SELECT \n", "    chat_history_id,\n", "    session_id,\n", "    channel_name,\n", "    agent_email,\n", "    conversation_id,\n", "    landed_at,\n", "    assigned_at,\n", "    LOWER(vendor_name) AS vendor,\n", "    LOWER(tl_email) AS tl_email,\n", "    reassignment_reason,\n", "    sub_tags\n", "    \n", "FROM \n", "    zomato.blinkit_etls.blinkit_one_support_sessions\n", "WHERE \n", "    dt>=REPLACE('{start_date}','-','') \n", "    AND dt<=REPLACE('{end_date}','-','') \n", "),\n", "\n", "final AS\n", "(\n", "    SELECT\n", "        DATE(base.landed_at) AS at_date_ist,\n", "        OS.channel_name,\n", "        lower(agent_email) as agent_email,\n", "        OS.session_id,\n", "        conversation_id,\n", "        assigned_at,\n", "        OS.chat_history_id,\n", "        vendor,\n", "        lower(tl_email) as tl_email,\n", "        LEAD(channel_name) OVER(PARTITION BY OS.session_id ORDER BY OS.landed_at) AS next_channel,\n", "        sub_tags as disposition,\n", "        reassignment_reason,\n", "        DENSE_RANK() OVER(PARTITION BY OS.session_id ORDER BY OS.landed_at) AS chat_rank\n", "        FROM base\n", "        JOIN os_base OS ON base.session_id=OS.session_id\n", "        AND OS.chat_history_id=base.chat_history_id AND is_reassigned=TRUE\n", ")\n", "\n", "SELECT \n", "    T1.*\n", "FROM \n", "    final AS T1\n", "WHERE \n", "    chat_rank=1\n", "ORDER BY session_id, assigned_at DESC\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "935bb209-99f5-4a26-b08b-46928503fbb4", "metadata": {"papermill": {"duration": 0.071143, "end_time": "2025-03-15T04:07:39.756707", "exception": false, "start_time": "2025-03-15T04:07:39.685564", "status": "completed"}, "tags": []}, "outputs": [], "source": ["CALLS_DETAILS = f\"\"\"\n", "\n", "WITH Master_table_base AS(\n", "\n", "SELECT\n", "    p_key\n", "    ,extract(hour from created_at) as hrs\n", "    ,created_date as ticket_date\n", "    ,TRY_CAST(a.session_id AS BIGINT) AS session_id\n", "    ,conversation_id\n", "    ,order_id\n", "    ,try_cast(cart_id as varchar) as cart_id\n", "    ,bot_last_node\n", "    ,agent_disposition\n", "    ,group_disposition\n", "    ,customer_id\n", "    ,karma_label\n", "    ,karma_score\n", "    ,resolved_by_client_id\n", "    ,case when resolved_by = '<PERSON><PERSON>' and last_channel_name in ('Gold 19-20 Image Verification','Image Verification') then 'iva'\n", "    else resolved_by end as resolved_by\n", "    ,resolved_by_name\n", "    ,csat\n", "    ,chat_rated_flag\n", "    ,extreme_ratings\n", "    ,a.chat_history_id\n", "    ,issue_id\n", "    ,current_skill\n", "    ,channel_name\n", "    ,LOWER(vendor_name) AS vendor\n", "    ,LOWER(agent_email) AS agent_email\n", "    ,assigned_at\n", "    ,queued_at\n", "    ,aat_secs\n", "    ,frt\n", "    ,agent_frt\n", "    ,aht_sys\n", "    ,aht\n", "    ,art_secs\n", "    ,rt_ulrt\n", "    ,case when IVA_flag = 1 then 'IVA' else 'Non-IVA' end as IVA_flag\n", "    ,employment_type\n", "    ,case when Agent_stores_flag = 1 then true else false end as Agent_stores_flag\n", "    ,case when gold_metric_flag = 1 then true else false end as gold_metric_flag\n", "    ,case when escalation_flag = 1 then true else false end as escalation_flag\n", "    ,case when a.agent_tenure >= 0 and a.agent_tenure <= 15 then '0 to 15'\n", "     when a.agent_tenure > 15 and a.agent_tenure <= 30 then '15 to 30'\n", "     when a.agent_tenure >= 30 and a.agent_tenure <= 60 then '30 to 60'\n", "     when a.agent_tenure > 60 then '> 60' end as tenure_bucket\n", "    ,site_name\n", "    ,row_number() over (partition by a.session_id,a.chat_history_id order by assigned_at desc) as rn\n", "    \n", "FROM interim.cd_dashboard_base_table a\n", "LEFT JOIN (SELECT created_at, session_id FROM cd_etls.master_table where created_date >=  DATE'{start_date}'  GROUP BY 1,2) b on a.session_id = b.session_id\n", "WHERE\n", "    created_date >= DATE'{start_date}' \n", "    AND created_date <= DATE'{end_date}'\n", "),\n", "\n", "reso_base AS(\n", "\n", "SELECT \n", "    reso_date,\n", "    TRY_CAST(session_id AS BIGINT) AS session_id,\n", "    email_id,\n", "    SUM(resol_amount) as resolution_amt,\n", "    SUM(CASE WHEN reso_type = 'refund' THEN resol_amount END) as refund_amt,\n", "    SUM(case WHEN reso_type = 'replace' THEN resol_amount END) as replace_amt\n", "    \n", "FROM cd_etls.item_wise_reso\n", "WHERE reso_date >= date'{start_date}' \n", "    AND reso_date <= date'{end_date}'\n", "    AND reason in ('Items','MDND')\n", "GROUP BY 1,2,3\n", "\n", "),\n", "\n", "Call_logs_entry as(\n", "\n", "SELECT\n", "    cle.id,\n", "    cle.insert_ds_ist,\n", "    cle.order_id,\n", "    cle.call_start_time,\n", "    cast(json_extract(json_parse(additional_info), '$.os_session_id') as bigint) AS os_session_id,\n", "    CAST(call_duration AS INTEGER) AS call_duration,\n", "    cle.caller_user_role,\n", "    cle.call_status,\n", "    callee_user_role,\n", "    TRY_CAST(cle.session_id AS BIGINT) AS session_id,\n", "    LOWER(cu.email_id) AS agent_email\n", "    \n", "FROM crm.crm_call_logs_entry CLE\n", "    LEFT JOIN lake_crm.crm_user CU ON cle.caller_user_id = cu.external_user_id \n", "WHERE \n", "CLE.insert_ds_ist >= '{start_date}' and CLE.insert_ds_ist <= '{end_date}'\n", ")\n", "\n", "SELECT \n", "    \n", "    a.*,\n", "    b.resolution_amt,\n", "    refund_amt,\n", "    replace_amt,\n", "    c.id as call_id,\n", "    c.call_start_time as call_start_time,\n", "    c.call_duration as call_duration,\n", "    c.caller_user_role as caller_user_role,\n", "    c.call_status,\n", "    c.callee_user_role,\n", "    c.agent_email\n", "\n", "FROM\n", "    Master_table_base a\n", "LEFT JOIN Reso_base b on a.session_id = b.session_id\n", "AND a.agent_email = b.email_id\n", "LEFT JOIN Call_logs_entry c on a.session_id = c.session_id AND a.agent_email = c.agent_email\n", "WHERE rn = 1 AND issue_id IS NOT NULL\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "72bbb90a-2dd0-4620-bef9-1f83afdce2ba", "metadata": {"papermill": {"duration": 0.059601, "end_time": "2025-03-15T04:07:39.879600", "exception": false, "start_time": "2025-03-15T04:07:39.819999", "status": "completed"}, "tags": []}, "outputs": [], "source": ["FCR = f\"\"\"\n", "\n", "With Master_table_base as(\n", "Select\n", "    p_key\n", "    ,created_date as ticket_date\n", "    ,a.session_id\n", "    ,conversation_id\n", "    ,order_id\n", "    ,cart_id\n", "    ,bot_last_node\n", "    ,agent_disposition\n", "    ,group_disposition\n", "    ,customer_id\n", "    ,karma_label\n", "    ,karma_score\n", "    ,resolved_by_client_id\n", "    ,resolved_by\n", "    ,resolved_by_name\n", "    ,csat\n", "    ,chat_rated_flag\n", "    ,extreme_ratings\n", "    ,chat_history_id\n", "    ,issue_id\n", "    ,current_skill\n", "    ,channel_name\n", "    ,vendor_name\n", "    ,agent_email\n", "    ,assigned_at\n", "    ,queued_at\n", "    ,aat_secs\n", "    ,frt\n", "    ,agent_frt\n", "    ,aht_sys\n", "    ,aht\n", "    ,art_secs\n", "    ,rt_ulrt\n", "    ,IVA_flag\n", "    ,employment_type\n", "    ,Agent_stores_flag\n", "    ,gold_metric_flag\n", "    ,escalation_flag\n", "    ,created_at\n", "    ,row_number() over (partition by a.session_id,chat_history_id order by assigned_at desc) as rn\n", "from interim.cd_dashboard_base_table a\n", "left join (Select session_id, created_at from cd_etls.master_table where created_date >= date'{start_date}' \n", "and created_date <= date'{end_date}' group by 1,2) b on a.session_id = b.session_id\n", "where created_date >= date'{start_date}' and created_date <= date'{end_date}'\n", "),\n", "\n", "Session_level_base as(\n", "Select\n", "    session_id\n", "    ,ticket_date\n", "    ,max(created_at) as created_at\n", "    ,max(conversation_id) as conversation_id\n", "    ,max(order_id) as order_id\n", "    ,max(cart_id) as cart_id\n", "    ,max(bot_last_node) as bot_last_node\n", "    ,max_by(agent_disposition,assigned_at) as agent_disposition\n", "    ,max_by(group_disposition,assigned_at) as group_disposition\n", "    ,max(customer_id) as customer_id\n", "    ,max(karma_label) as karma_label\n", "    ,max(karma_score) as karma_score\n", "    ,max(resolved_by_client_id) as resolved_by_client_id\n", "    ,max(resolved_by) as resolved_by\n", "    ,max(resolved_by_name) as resolved_by_name\n", "    ,max(csat) as csat\n", "    ,max(chat_rated_flag) as chat_rated_flag\n", "    ,max(extreme_ratings) as extreme_ratings\n", "    ,max_by(chat_history_id,assigned_at) as chat_history_id\n", "    ,max_by(issue_id,assigned_at) as issue_id\n", "    ,max_by(current_skill,assigned_at) as current_skill\n", "    ,max_by(channel_name,assigned_at) as channel_name\n", "    ,max_by(vendor_name,assigned_at) as vendor_name\n", "    ,max_by(agent_email,assigned_at) as agent_email\n", "    ,max(assigned_at) as assigned_at\n", "    ,max(queued_at) as queued_at\n", "    ,max_by(aat_secs,assigned_at) as aat_secs\n", "    ,max_by(frt,assigned_at) as frt\n", "    ,max_by(agent_frt,assigned_at) as agent_frt\n", "    ,max_by(aht_sys,assigned_at) as aht_sys\n", "    ,max_by(aht,assigned_at) as aht\n", "    ,max_by(art_secs,assigned_at) as art_secs\n", "    ,max_by(rt_ulrt,assigned_at) as rt_ulrt\n", "    ,max_by(IVA_flag,assigned_at) as IVA_flag\n", "from Master_table_base\n", "where rn = 1\n", "group by 1,2\n", "),\n", "\n", "dump_base as(\n", "Select\n", "    ticket_date,\n", "    customer_id,\n", "    karma_label,\n", "    session_id,\n", "    coalesce(cart_id,-1) as cart_id,\n", "    group_disposition,\n", "    channel_name,\n", "    agent_email,\n", "    LOWER(vendor_name) AS vendor,\n", "    created_at as session_created_at,\n", "    resolved_by,\n", "    IVA_flag,\n", "    row_number() over (partition by ticket_date,customer_id,cart_id,group_disposition order by created_at) as session_rank\n", "from Session_level_base\n", "where (resolved_by = 'Agent' or IVA_flag = 1)\n", "),\n", "\n", "fcr_base as(\n", "SELECT\n", "    ticket_date,\n", "    customer_id,\n", "    coalesce(cart_id,-1) as cart_id,\n", "    group_disposition,\n", "    max(case when resolved_by = 'Agent' then session_rank end) as agent_max_rank,\n", "    max(case when IVA_flag = 1 then session_rank end) as iva_max_rank,\n", "    count(distinct case when resolved_by = 'Agent' then session_id end) as Agent_sessions,\n", "    count(distinct case when IVA_flag = 1 then session_id end) as IVA_sessions\n", "    from dump_base\n", "    group by 1,2,3,4\n", ")\n", "\n", "Select\n", "a.*,\n", "Agent_sessions,\n", "IVA_sessions,\n", "agent_max_rank,\n", "iva_max_rank,\n", "case when Agent_sessions = 1 then 'Agent FCR' else 'Agent Non-Fcr' end as agent_fcr_flag,\n", "case when IVA_sessions = 1 then 'IVA FCR' else 'IVA Non-Fcr' end as iva_fcr_flag\n", "from dump_base a\n", "left join fcr_base b on a.ticket_date = b.ticket_date and a.customer_id = b.customer_id \n", "and a.cart_id = b.cart_id and a.group_disposition = b.group_disposition\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "3ddb1b11-0644-4346-8eac-bc145e9fd1e9", "metadata": {"papermill": {"duration": 0.111809, "end_time": "2025-03-15T04:07:40.060398", "exception": false, "start_time": "2025-03-15T04:07:39.948589", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def get_audits(vendor):\n", "\n", "    AUDIT_QUERY = f\"\"\"\n", "    \n", "    \n", "    WITH vendor AS (\n", "    SELECT\n", "        session_id,\n", "        order_id,\n", "        max_by(issue_id, assigned_at) issue_id,\n", "        max_by(resolved_by_name, assigned_at) agent_name,\n", "        max_by(agent_email, assigned_at) agent_email,\n", "        max_by(csat, assigned_at) csat,\n", "        max_by(aht, assigned_at) aht,\n", "        max_by(chat_history_id, assigned_at) chat_history_id,\n", "        max_by(queued_at, assigned_at) queued_at,\n", "        max_by(rt_ulrt, assigned_at) rt_ulrt,\n", "        max(assigned_at) assigned_at,\n", "        max_by(agent_unassigned_at, assigned_at) agent_unassigned_at,\n", "        max_by(rated_at, assigned_at) rated_at,\n", "        max_by(CASE WHEN (lower(site_name) LIKE '%%pte%%') THEN 'PTE' ELSE 'FTE' END, assigned_at) AS employment_type,\n", "        max_by(agent_tenure, assigned_at) agent_tenure,\n", "        max_by(current_skill, assigned_at) concurrency\n", "    FROM cd_etls.os_agent_sessions\n", "    WHERE created_date = current_date - INTERVAL '1' DAY\n", "    GROUP BY 1, 2\n", "    HAVING LOWER(max_by(vendor_name, assigned_at)) = '{vendor}'\n", "),\n", "\n", "master_table AS (\n", "    SELECT\n", "        DISTINCT\n", "        session_id,\n", "        group_disposition,\n", "        resolved_by_name,\n", "        created_date,\n", "        order_id,\n", "        cart_id,\n", "        conversation_id,\n", "        karma,\n", "        karma_score,\n", "        last_channel_name,\n", "        bot_first_node,\n", "        bot_last_node,\n", "        agent_disposition,\n", "        issue_item_ids,\n", "        node_journey,\n", "        session_resolved_at,\n", "        CASE \n", "            WHEN resolved_by_client_id = 100 THEN 'Agent'\n", "            WHEN resolved_by_client_id = 200 THEN 'Bot'\n", "            WHEN resolved_by_client_id = 0 THEN 'Chat timed-out'\n", "            ELSE 'not-resolved' \n", "        END AS resolved_by,\n", "        city_name,\n", "        merchant_name,\n", "        payment_method\n", "    FROM cd_etls.master_table\n", "    WHERE created_date = current_date - INTERVAL '1' DAY\n", "    AND last_channel_name NOT IN ('Gold 19-20 Image Verification', 'Image Verification')\n", "    AND group_disposition IS NOT NULL\n", "),\n", "\n", "final AS (\n", "    SELECT\n", "        v.*,\n", "        group_disposition,\n", "        'overall' AS overall,\n", "        row_number() OVER (PARTITION BY group_disposition ORDER BY RAND()) AS rank_row\n", "    FROM vendor v\n", "    JOIN master_table m ON v.session_id = m.session_id AND v.agent_name = m.resolved_by_name\n", "),\n", "\n", "perc AS (\n", "    SELECT\n", "        overall,\n", "        COALESCE(group_disposition, 'all') group_disposition,\n", "        COUNT(session_id) AS sessions\n", "    FROM final \n", "    GROUP BY GROUPING SETS (\n", "        (overall),\n", "        (group_disposition, overall)\n", "    )\n", "),\n", "\n", "audit AS (\n", "    SELECT\n", "        pp.group_disposition,\n", "        pp.sessions,\n", "        pp.sessions * 1.000000 / p.sessions AS percentage,\n", "        CAST(ROUND({AUDITOR_COUNT.get(vendor)} * {VENDOR_AUDIT_COUNT} * (pp.sessions * 1.000000 / p.sessions), 0) AS INT) AS chats_to_audit\n", "    FROM perc pp\n", "    JOIN perc p ON p.group_disposition = 'all'\n", "),\n", "\n", "reso AS (\n", "    SELECT\n", "        session_id,\n", "        order_id,\n", "        SUM(CASE WHEN reso_type = 'replace' THEN resol_amount END) AS replaced_amt,\n", "        SUM(CASE WHEN reso_type = 'refund' THEN resol_amount END) AS refunded_amt\n", "    FROM cd_etls.item_wise_reso\n", "    WHERE reso_date = current_date - INTERVAL '1' DAY\n", "    GROUP BY 1, 2\n", ")\n", "\n", "SELECT\n", "    DISTINCT\n", "    f.*,\n", "    resolved_by_name,\n", "    created_date,\n", "    cart_id,\n", "    conversation_id,\n", "    karma,\n", "    karma_score,\n", "    last_channel_name,\n", "    bot_first_node,\n", "    bot_last_node,\n", "    agent_disposition,\n", "    issue_item_ids,\n", "    node_journey,\n", "    session_resolved_at,\n", "    resolved_by,\n", "    city_name,\n", "    merchant_name,\n", "    payment_method,\n", "    replaced_amt,\n", "    refunded_amt\n", "FROM final f\n", "JOIN audit a ON a.group_disposition = f.group_disposition\n", "JOIN vendor v ON v.session_id = f.session_id AND v.order_id = f.order_id\n", "JOIN master_table mt ON mt.session_id = f.session_id AND mt.group_disposition = f.group_disposition AND mt.order_id = f.order_id\n", "LEFT JOIN reso r ON f.session_id = CAST(r.session_id AS BIGINT) AND r.order_id = f.order_id\n", "WHERE rank_row <= chats_to_audit\n", "    \n", "    \"\"\"\n", "\n", "    audit_df = get_data(AUDIT_QUERY)\n", "    return audit_df"]}, {"cell_type": "code", "execution_count": null, "id": "a6320b6a-acba-4394-8563-cf4dfd293e93", "metadata": {"papermill": {"duration": 354.92344, "end_time": "2025-03-15T04:13:35.040478", "exception": false, "start_time": "2025-03-15T04:07:40.117038", "status": "completed"}, "tags": []}, "outputs": [], "source": ["alm = get_data(ALM_QUERY)\n", "os_timeout = get_data(OS_TIMEOUT)\n", "calls_data = get_data(CALLS_DETAILS)\n", "os_raw = get_data(OS_RAW)"]}, {"cell_type": "code", "execution_count": null, "id": "b34dc314-53df-4313-a687-3eeaa3cf4719", "metadata": {"papermill": {"duration": 153.465868, "end_time": "2025-03-15T04:16:08.626690", "exception": false, "start_time": "2025-03-15T04:13:35.160822", "status": "completed"}, "tags": []}, "outputs": [], "source": ["agent_productivity = get_data(AGENT_PRODUCTIVITY)\n", "agent_reassign = get_data(REASSIGNMENT)\n", "agent_refund = get_data(REFUND_DETAILS)\n", "fcr_raw = get_data(FCR)"]}, {"cell_type": "code", "execution_count": null, "id": "59700395-162e-4cb5-afaa-d05cd4a33677", "metadata": {"papermill": {"duration": 0.070953, "end_time": "2025-03-15T04:16:08.789613", "exception": false, "start_time": "2025-03-15T04:16:08.718660", "status": "completed"}, "tags": []}, "outputs": [], "source": ["startek_alm = alm.query(\"vendor == 'startek' \")\n", "niftel_alm = alm.query(\"vendor == 'niftel' \")\n", "v5_global_alm = alm.query(\"vendor == 'v5 global' \")\n", "bld_alm = alm.query(\"vendor == 'bld outsourcing'\")\n", "teleperformance_alm = alm.query(\"vendor == 'teleperformance'\")"]}, {"cell_type": "code", "execution_count": null, "id": "426fb616-5283-44c2-a8d8-f4c8ec6c3369", "metadata": {"papermill": {"duration": 0.198707, "end_time": "2025-03-15T04:16:09.063433", "exception": false, "start_time": "2025-03-15T04:16:08.864726", "status": "completed"}, "tags": []}, "outputs": [], "source": ["startek_os_raw = os_raw.query(\"vendor == 'startek' \")\n", "niftel_os_raw = os_raw.query(\"vendor == 'niftel' \")\n", "v5_global_os_raw = os_raw.query(\"vendor == 'v5 global' \")\n", "bld_os_raw = os_raw.query(\"vendor == 'bld outsourcing'\")\n", "teleperformance_os_raw = os_raw.query(\"vendor == 'teleperformance'\")"]}, {"cell_type": "code", "execution_count": null, "id": "da9b121f-28e1-4990-8f6e-96fac8f6c985", "metadata": {"papermill": {"duration": 0.116523, "end_time": "2025-03-15T04:16:09.236466", "exception": false, "start_time": "2025-03-15T04:16:09.119943", "status": "completed"}, "tags": []}, "outputs": [], "source": ["startek_os_tout = os_timeout.query(\"vendor == 'startek' \")\n", "niftel_os_tout = os_timeout.query(\"vendor == 'niftel' \")\n", "v5_global_os_tout = os_timeout.query(\"vendor == 'v5 global' \")\n", "bld_os_tout = os_timeout.query(\"vendor == 'bld outsourcing' \")\n", "teleperformance_os_tout = os_timeout.query(\"vendor == 'teleperformance'\")"]}, {"cell_type": "code", "execution_count": null, "id": "05844d5b-bd71-431c-a857-9d4f89695868", "metadata": {"papermill": {"duration": 0.142589, "end_time": "2025-03-15T04:16:09.480154", "exception": false, "start_time": "2025-03-15T04:16:09.337565", "status": "completed"}, "tags": []}, "outputs": [], "source": ["startek_agent_prod = agent_productivity.query(\"vendor == 'startek' \")\n", "niftel_agent_prod = agent_productivity.query(\"vendor == 'niftel' \")\n", "v5_global_agent_prod = agent_productivity.query(\"vendor == 'v5 global' \")\n", "bld_agent_prod = agent_productivity.query(\"vendor == 'bld outsourcing' \")\n", "teleperformance_agent_prod = agent_productivity.query(\"vendor == 'teleperformance'\")"]}, {"cell_type": "code", "execution_count": null, "id": "4f214f2c-f1db-4eca-ad95-7d80f34f5452", "metadata": {"papermill": {"duration": 0.099759, "end_time": "2025-03-15T04:16:09.626056", "exception": false, "start_time": "2025-03-15T04:16:09.526297", "status": "completed"}, "tags": []}, "outputs": [], "source": ["startek_agent_refund = agent_refund.query(\"vendor == 'startek' \")\n", "niftel_agent_refund = agent_refund.query(\"vendor == 'niftel' \")\n", "v5_global_agent_refund = agent_refund.query(\"vendor == 'v5 global' \")\n", "bld_agent_refund = agent_refund.query(\"vendor == 'bld outsourcing' \")\n", "teleperformance_agent_refund = agent_refund.query(\"vendor == 'teleperformance'\")"]}, {"cell_type": "code", "execution_count": null, "id": "501c1f22-27a0-4d1a-8ebb-9e821a6e6fc2", "metadata": {"papermill": {"duration": 0.225467, "end_time": "2025-03-15T04:16:09.898507", "exception": false, "start_time": "2025-03-15T04:16:09.673040", "status": "completed"}, "tags": []}, "outputs": [], "source": ["startek_calls_data = calls_data.query(\"vendor == 'startek' \")\n", "niftel_calls_data = calls_data.query(\"vendor == 'niftel' \")\n", "v5_global_calls_data = calls_data.query(\"vendor == 'v5 global' \")\n", "bld_calls_data = calls_data.query(\"vendor == 'bld outsourcing' \")\n", "teleperformance_calls_data = calls_data.query(\"vendor == 'teleperformance'\")"]}, {"cell_type": "code", "execution_count": null, "id": "d6d86b36-1a74-4b0c-8a34-56e2ece80360", "metadata": {"papermill": {"duration": 0.068379, "end_time": "2025-03-15T04:16:10.089959", "exception": false, "start_time": "2025-03-15T04:16:10.021580", "status": "completed"}, "tags": []}, "outputs": [], "source": ["startek_reassign_data = agent_reassign.query(\"vendor == 'startek' \")\n", "niftel_reassign_data = agent_reassign.query(\"vendor == 'niftel' \")\n", "v5_global_reassign_data = agent_reassign.query(\"vendor == 'v5 global' \")\n", "bld_agent_reassign_data = agent_reassign.query(\"vendor == 'bld outsourcing' \")\n", "teleperformance_reassign_data = agent_reassign.query(\"vendor == 'teleperformance'\")"]}, {"cell_type": "code", "execution_count": null, "id": "6ce50d0d-4df2-48b2-9ea7-88697a60c21d", "metadata": {"papermill": {"duration": 0.109898, "end_time": "2025-03-15T04:16:10.248683", "exception": false, "start_time": "2025-03-15T04:16:10.138785", "status": "completed"}, "tags": []}, "outputs": [], "source": ["startek_fcr_data = fcr_raw.query(\"vendor == 'startek' \")\n", "niftel_fcr_data = fcr_raw.query(\"vendor == 'niftel' \")\n", "v5_global_fcr_data = fcr_raw.query(\"vendor == 'v5 global' \")\n", "bld_fcr_data = fcr_raw.query(\"vendor == 'bld outsourcing' \")\n", "teleperformance_fcr_data = fcr_raw.query(\"vendor == 'teleperformance'\")"]}, {"cell_type": "code", "execution_count": null, "id": "9c84e886-4226-4e12-bb4e-408545c6d569", "metadata": {"papermill": {"duration": 0.081913, "end_time": "2025-03-15T04:16:10.387124", "exception": false, "start_time": "2025-03-15T04:16:10.305211", "status": "completed"}, "tags": []}, "outputs": [], "source": ["try:\n", "    startek_agent_refund_audit = startek_agent_refund.query(\"resolve_flow != 'iva' \").sample(75)\n", "except:\n", "    startek_agent_refund_audit = create_empty_df()\n", "    print(\"startek_agent_refund_audit\")\n", "try:\n", "    niftel_agent_refund_audit = niftel_agent_refund.sample(75)\n", "except:\n", "    niftel_agent_refund_audit = create_empty_df()\n", "    print(\"niftel_agent_refund_audit\")\n", "try:\n", "    v5_global_agent_refund_audit = v5_global_agent_refund.sample(75)\n", "except:\n", "    v5_global_agent_refund_audit = create_empty_df()\n", "    print(\"v5_global_agent_refund_audit\")\n", "try:\n", "    bld_agent_refund_audit = bld_agent_refund.sample(75)\n", "except:\n", "    bld_agent_refund_audit = create_empty_df()\n", "    print(\"bld_agent_refund_audit\")\n", "try:\n", "    teleperformance_agent_refund_audit = teleperformance_agent_refund.sample(75)\n", "except:\n", "    teleperformance_agent_refund_audit = create_empty_df()\n", "    print(\"teleperformance_agent_refund_audit\")"]}, {"cell_type": "code", "execution_count": null, "id": "53921320-c9ab-42ef-84f0-65d4df649e16", "metadata": {"papermill": {"duration": 350.25035, "end_time": "2025-03-15T04:22:00.686184", "exception": false, "start_time": "2025-03-15T04:16:10.435834", "status": "completed"}, "tags": []}, "outputs": [], "source": ["try:\n", "    startek_audit_sheet = get_audits(\"startek\")\n", "except:\n", "    startek_audit_sheet = create_empty_df()\n", "    print(\"startek_audit_sheet\")\n", "\n", "try:\n", "    conneqt_audit_sheet = get_audits(\"conneqt\")\n", "except:\n", "    conneqt_audit_sheet = create_empty_df()\n", "    print(\"conneqt_audit_sheet\")\n", "\n", "try:\n", "    niftel_audit_sheet = get_audits(\"niftel\")\n", "except:\n", "    niftel_audit_sheet = create_empty_df()\n", "    print(\"niftel_audit_sheet\")\n", "\n", "try:\n", "    v5_audit_sheet = get_audits(\"v5 global\")\n", "except:\n", "    v5_audit_sheet = create_empty_df()\n", "    print(\"v5_audit_sheet\")\n", "\n", "try:\n", "    bld_audit_sheet = get_audits(\"bld outsourcing\")\n", "except:\n", "    bld_audit_sheet = create_empty_df()\n", "    print(\"bld_audit_sheet\")\n", "\n", "try:\n", "    teleperformance_audit_sheet = get_audits(\"teleperformance\")\n", "except:\n", "    teleperformance_audit_sheet = create_empty_df()\n", "    print(\"teleperformance_audit_sheet\")"]}, {"cell_type": "code", "execution_count": null, "id": "11178582-5eb5-49bf-ab6c-c71f3e22e6b4", "metadata": {"papermill": {"duration": 2.137389, "end_time": "2025-03-15T04:22:02.923925", "exception": false, "start_time": "2025-03-15T04:22:00.786536", "status": "completed"}, "tags": []}, "outputs": [], "source": ["try:\n", "\n", "    df_distro = fetch_sheets(SHEET_ID, SHEET_NAME)\n", "    startek_emails = df_distro.query(\"Vendor == 'startek'\")[\"Email\"].to_list()\n", "    v5_emails = df_distro.query(\"Vendor == 'v5 global' \")[\"Email\"].to_list()\n", "    niftel_emails = df_distro.query(\"Vendor == 'niftel' \")[\"Email\"].to_list()\n", "    bld_emails = df_distro.query(\"Vendor == 'bld outsourcing' \")[\"Email\"].to_list()\n", "    teleperformance_emails = df_distro.query(\"Vendor == 'teleperformance' \")[\"Email\"].to_list()\n", "    inhouse_emails = df_distro.query(\"Vendor == 'inhouse' \")[\"Email\"].to_list()\n", "except:\n", "    print(\"Distro Failed\")"]}, {"cell_type": "code", "execution_count": null, "id": "dda25dc7-e308-49a6-beaf-23bba22efaee", "metadata": {"papermill": {"duration": 0.090371, "end_time": "2025-03-15T04:22:03.238885", "exception": false, "start_time": "2025-03-15T04:22:03.148514", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def write_file(name, lst):\n", "    dct = {\n", "        0: \"OS - Raw\",\n", "        1: \"Refunds\",\n", "        2: \"Agent Metrics\",\n", "        3: \"Time Out - OS\",\n", "        4: \"Agent Productivity\",\n", "        5: \"Calls Data\",\n", "        6: \"Reassigned Chats\",\n", "        7: \"FCR - Raw\",\n", "        8: \"Audit Sheet - Refund\",\n", "        9: \"<PERSON><PERSON>\",\n", "    }\n", "\n", "    # Step 1: Create a zip file and write CSV files into it\n", "    with zipfile.ZipFile(f\"{name}.zip\", \"w\", compression=zipfile.ZIP_DEFLATED) as zipf:\n", "        for i, df in enumerate(lst):\n", "            sheet_name = dct.get(i, f\"Sheet{i}\")\n", "            # Convert DataFrame to CSV format (as bytes) and write to zip\n", "            csv_content = df.to_csv(index=False).encode(\"utf-8\")\n", "            zipf.writestr(f\"{sheet_name}.csv\", csv_content)\n", "\n", "    print(f\"Files saved and compressed into {name}.zip\")"]}, {"cell_type": "code", "execution_count": null, "id": "ef3c6ec4-ad4f-4759-9d8a-e1210aaba9e4", "metadata": {"papermill": {"duration": 186.290778, "end_time": "2025-03-15T04:25:09.576009", "exception": false, "start_time": "2025-03-15T04:22:03.285231", "status": "completed"}, "tags": []}, "outputs": [], "source": ["startek = [\n", "    startek_os_raw,\n", "    startek_agent_refund,\n", "    startek_alm,\n", "    startek_os_tout,\n", "    startek_agent_prod,\n", "    startek_calls_data,\n", "    startek_reassign_data,\n", "    startek_fcr_data,\n", "    startek_agent_refund_audit,\n", "    startek_audit_sheet,\n", "]\n", "\n", "niftel = [\n", "    niftel_os_raw,\n", "    niftel_agent_refund,\n", "    niftel_alm,\n", "    niftel_os_tout,\n", "    niftel_agent_prod,\n", "    niftel_calls_data,\n", "    niftel_reassign_data,\n", "    niftel_fcr_data,\n", "    niftel_agent_refund_audit,\n", "    niftel_audit_sheet,\n", "]\n", "\n", "v5_global = [\n", "    v5_global_os_raw,\n", "    v5_global_agent_refund,\n", "    v5_global_alm,\n", "    v5_global_os_tout,\n", "    v5_global_agent_prod,\n", "    v5_global_calls_data,\n", "    v5_global_reassign_data,\n", "    v5_global_fcr_data,\n", "    v5_global_agent_refund_audit,\n", "    v5_audit_sheet,\n", "]\n", "\n", "tp = [\n", "    teleperformance_os_raw,\n", "    teleperformance_agent_refund,\n", "    teleperformance_alm,\n", "    teleperformance_os_tout,\n", "    teleperformance_agent_prod,\n", "    teleperformance_calls_data,\n", "    teleperformance_reassign_data,\n", "    teleperformance_fcr_data,\n", "    teleperformance_agent_refund_audit,\n", "    teleperformance_audit_sheet,\n", "]\n", "\n", "write_file(f\"startek - {str(day)}\", startek)\n", "write_file(f\"niftel - {str(day)}\", niftel)\n", "write_file(f\"v5_global - {str(day)}\", v5_global)\n", "write_file(f\"teleperformance - {str(day)}\", tp)"]}, {"cell_type": "code", "execution_count": null, "id": "3a644c37-41b3-4513-8948-0da3e6a07538", "metadata": {"papermill": {"duration": 0.057269, "end_time": "2025-03-15T04:25:09.728042", "exception": false, "start_time": "2025-03-15T04:25:09.670773", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def send_email(report_name, Email):\n", "\n", "    from_email = \"<EMAIL>\"\n", "\n", "    report_sub = f\"Blinkit - Customer Delight Report - {str(day)}\"\n", "\n", "    to_email = Email\n", "    subject = report_sub\n", "    html_content = f\"Hi Team, Please find the report for {str(day)}.\"\n", "\n", "    pb.send_email(\n", "        from_email,\n", "        to_email,\n", "        subject,\n", "        html_content,\n", "        files=[f\"{report_name}.zip\"],\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "13d0aa8a-d7b8-4518-bf2f-245986e75e15", "metadata": {"papermill": {"duration": 2.664473, "end_time": "2025-03-15T04:25:12.440809", "exception": true, "start_time": "2025-03-15T04:25:09.776336", "status": "failed"}, "tags": []}, "outputs": [], "source": ["send_email(f\"startek - {str(day)}\", inhouse_emails)\n", "send_email(f\"niftel - {str(day)}\", inhouse_emails)\n", "send_email(f\"v5_global - {str(day)}\", inhouse_emails)\n", "send_email(f\"teleperformance - {str(day)}\", inhouse_emails)"]}, {"cell_type": "code", "execution_count": null, "id": "9c42a42f-8635-4961-874c-6d6ce7e12027", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["send_email(f\"startek - {str(day)}\", startek_emails)\n", "send_email(f\"niftel - {str(day)}\", niftel_emails)\n", "send_email(f\"v5_global - {str(day)}\", v5_emails)\n", "send_email(f\"teleperformance - {str(day)}\", teleperformance_emails)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}