alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: chargeback_alert_slack
dag_type: etl
escalation_priority: low
execution_timeout: 2000
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: crm
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U087G237876
path: crm/misc/etl/chargeback_alert_slack
paused: false
pool: crm_pool
project_name: misc
schedule:
  end_date: '2025-08-24T00:00:00'
  interval: 30 2 * * *
  start_date: '2025-06-09T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 5
