{"cells": [{"cell_type": "code", "execution_count": null, "id": "245223f3-1ab3-4f9f-a3f1-1c08b8d06e3e", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib\n", "!pip install --upgrade pip\n", "!pip install numpy==1.26.4\n", "import numpy as np\n", "import pandas as pd\n", "import pencilbox as pb\n", "import time\n", "from datetime import date, datetime, timedelta\n", "from matplotlib import pyplot as plt\n", "import matplotlib.pyplot as plt\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "a4d2a9bc-3c10-4b54-9beb-652f121d8221", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    start = time.time()\n", "    df = pd.read_sql_query(sql, con)\n", "    end = time.time()\n", "    if (end - start) > 60:\n", "        print(\"Time: \", (end - start) / 60, \"min\")\n", "    else:\n", "        print(\"Time: \", end - start, \"s\")\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "8afe87b8-43fd-4f4f-a1d2-38c5d3e43cb3", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "WITH chargeback_base AS (\n", "Select \n", "    pcc.cart_id,\n", "    txn_date,\n", "    mode,\n", "    total_selling_price,\n", "    order_create_dt_ist,\n", "    order_type,\n", "    format_datetime(parse_datetime(chargeback_dt, 'd-MMM-yyyy'), 'yyyy-MM-dd') AS formatted_date\n", "FROM fps_etls.payments_chargeback_cases pcc\n", "JOIN dwh.fact_sales_order_details fsod\n", "ON\n", "fsod.cart_id=pcc.cart_id\n", "WHERE chargeback_dt <>'Na'\n", "AND order_create_dt_ist>=DATE('2025-05-11')\n", ")\n", "    \n", "SELECT \n", "Count(Distinct CASE WHEN DATE(formatted_date)>=CURRENT_DATE-INTERVAL '2' DAY THEN cart_id END) AS cases_last_2_days,\n", "COUNT (Distinct CASE WHEN DATE(formatted_date)>=CURRENT_DATE-INTERVAL '2' DAY AND order_type='DigitalVoucherOrder' THEN cart_id END) AS digital_voucher_last_2_day,\n", "SUM(CASE WHEN DATE(formatted_date)>=CURRENT_DATE-INTERVAL '2' DAY THEN total_selling_price END ) AS GMV_last_2_days,\n", "COUNT(DISTINCT CASE WHEN DATE(formatted_date)>=DATE('2025-05-11') THEN cart_id END ) AS cases_since_mothers_day,\n", "SUM(CASE WHEN DATE(formatted_date)>=DATE('2025-05-11') THEN total_selling_price END ) AS GMV_since_mothers_day\n", "\n", "FROM\n", "chargeback_base\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "7f77607c-9cd1-40fb-86ab-d3705a136fe1", "metadata": {}, "outputs": [], "source": ["query_2 = \"\"\"\n", "\n", "WITH chargeback_base AS (\n", "Select \n", "    pcc.cart_id,\n", "    txn_date,\n", "    mode,\n", "    total_selling_price,\n", "    order_create_dt_ist,\n", "    order_type,\n", "    format_datetime(parse_datetime(chargeback_dt, 'd-MMM-yyyy'), 'yyyy-MM-dd') AS formatted_date\n", "FROM fps_etls.payments_chargeback_cases pcc\n", "JOIN dwh.fact_sales_order_details fsod\n", "ON\n", "fsod.cart_id=pcc.cart_id\n", "WHERE chargeback_dt <>'Na'\n", "AND order_create_dt_ist>=DATE('2025-05-11')\n", ")\n", "    \n", "SELECT \n", "formatted_date AS order_date,\n", "COUNT(DISTINCT cart_id ) AS Total_chargeback_cases,\n", "COUNT(DISTINCT CASE WHEN order_type='DigitalVoucherOrder' THEN cart_id END) AS Digital_order_cases,\n", "SUM(total_selling_price) AS Total_GMV_of_orders\n", "\n", "FROM\n", "chargeback_base\n", "GROUP BY 1\n", "order by \n", "formatted_date DESC\n", "\n", "\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "57dc0899-d635-45a3-986e-7842b37708c4", "metadata": {}, "outputs": [], "source": ["df = pd.read_sql_query(query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "01f8e079-e159-419e-b520-27448d52748f", "metadata": {}, "outputs": [], "source": ["df_2 = pd.read_sql_query(query_2, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "8fc446c6-0778-4e4d-83e2-a25ce8234b9f", "metadata": {}, "outputs": [], "source": ["def render_mpl_table(\n", "    data,\n", "    col_width=4.3,\n", "    row_height=1.2,\n", "    font_size=17,\n", "    header_color=\"#ff7f0e\",\n", "    row_colors=[\"#f1f1f2\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, cellLoc=\"center\", colLabels=data.columns, **kwargs\n", "    )\n", "    # mpl_table.auto_set_column_width(col=list(range(len(df_v1.columns))))\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        #         elif k[0] == data.shape[0]:\n", "        #             cell.set_text_props(weight=\"bold\", ma=\"center\")\n", "        # cell.set_facecolor('#f1f1f2')\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "bb331ecb-3aee-4938-9724-53ba0b05fc8b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4ebb9a39-c5d7-49dd-b199-0080e33b1b95", "metadata": {}, "outputs": [], "source": ["if df.empty:\n", "    print(\"No new Chargeback cases\")\n", "else:\n", "    fig1, ax1 = render_mpl_table(df_2)\n", "    fig1.savefig(\"chargeback_image.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "ddc74edd-a97d-48d8-b50b-3b3b1d149d4a", "metadata": {}, "outputs": [], "source": ["variables = [\n", "    \"cases_last_2_days\",\n", "    \"digital_voucher_last_2_day\",\n", "    \"GMV_last_2_days\",\n", "    \"cases_since_mothers_day\",\n", "    \"GMV_since_mothers_day\",\n", "]\n", "\n", "# Extract values from the first (and only) row\n", "results = {var: df.at[0, var] for var in variables}\n", "\n", "# Access individual variables if needed\n", "cases_last_2_days = results[\"cases_last_2_days\"]\n", "digital_voucher_last_2_day = results[\"digital_voucher_last_2_day\"]\n", "GMV_last_2_days = results[\"GMV_last_2_days\"]\n", "cases_since_mothers_day = results[\"cases_since_mothers_day\"]\n", "GMV_since_mothers_day = results[\"GMV_since_mothers_day\"]\n", "\n", "summary = f\"\"\"Cases last 2 days: {cases_last_2_days}\n", "Digital Voucher orders last 2_days: {digital_voucher_last_2_day}\n", "GMV last 2 days: {GMV_last_2_days}\n", "Cases since Mother's Day: {cases_since_mothers_day}\n", "Total_GMV_of_orders: {GMV_since_mothers_day}\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "bbd3e959-0524-4d30-9021-aa8fb2c9345e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d3c78fe3-0c08-47c4-95d8-7615e8fea743", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"bl-fps-alerts\",\n", "    # \"testing_me\",\n", "    # channel=\"bmh-sample\",\n", "    text=f\"{summary} for more details refer https://reports.grofer.io/dashboards/15662-fraud-karma-dashboard---overview?p_start_date=2025-04-16\",\n", "    files=[\"chargeback_image.png\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b0ba87e0-c8bc-4e7f-acd8-38ec7febedbf", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "565b6141-b6c8-46ff-b834-7533613b3f94", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c84c76c3-a277-4c72-9321-35d557204468", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}