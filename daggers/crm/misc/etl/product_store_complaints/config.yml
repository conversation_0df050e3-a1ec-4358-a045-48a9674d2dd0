alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: product_store_complaints
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: crm
notebook:
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
owner:
  email: <EMAIL>
  slack_id: U03S5ACSW3C
path: crm/misc/etl/product_store_complaints
paused: false
pool: crm_pool
project_name: misc
schedule:
  end_date: '2025-07-28T00:00:00'
  interval: 30 1 * * *
  start_date: '2024-05-06T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 4
