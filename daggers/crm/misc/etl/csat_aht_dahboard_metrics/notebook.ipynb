{"cells": [{"cell_type": "code", "execution_count": null, "id": "186053e0-b752-46cd-88a7-11d6a88a6642", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "from datetime import date, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "53d307e0-d97e-4555-9fa5-315b99141c6c", "metadata": {}, "outputs": [], "source": ["today = date.today()"]}, {"cell_type": "code", "execution_count": null, "id": "48b579bb-ec4c-42d6-aae3-0c0e138e8057", "metadata": {}, "outputs": [], "source": ["today = date.today()\n", "yesterday = today - <PERSON><PERSON><PERSON>(days=1)\n", "day2 = today - <PERSON><PERSON><PERSON>(days=2)\n", "# day1 = today - <PERSON><PERSON><PERSON>(days = 120)\n", "start_date = str(day2)\n", "end_date = str(today)"]}, {"cell_type": "code", "execution_count": null, "id": "9e9ca745-6d81-46de-94bd-ab5322feb807", "metadata": {}, "outputs": [], "source": ["# start_date, end_date"]}, {"cell_type": "code", "execution_count": null, "id": "7de38247-5c7a-4962-83b6-bd5df176d4b5", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "\n", "\n", "With\n", "\n", "Master_table_base as(\n", "\n", "Select\n", "\n", "    p_key\n", "\n", "    ,extract(week from created_date) as week\n", "\n", "    ,created_date as ticket_date\n", "\n", "    ,a.session_id\n", "\n", "    ,conversation_id\n", "\n", "    ,order_id\n", "\n", "    ,cart_id\n", "\n", "    ,bot_last_node\n", "\n", "    ,agent_disposition\n", "\n", "    ,group_disposition\n", "\n", "    ,customer_id\n", "\n", "    ,karma_label\n", "\n", "    ,case when cast(karma_score as bigint) in (19,20) then 'Gold 19-20' \n", "\n", "        when cast(karma_score as bigint) in (17,18) then 'Gold 17-18'\n", "\n", "        else karma_label end as new_karma_label\n", "\n", "    ,karma_score\n", "\n", "    ,resolved_by_client_id\n", "\n", "    ,resolved_by\n", "\n", "    ,resolved_by_name\n", "\n", "    ,csat\n", "\n", "    ,chat_rated_flag\n", "\n", "    ,extreme_ratings\n", "\n", "    ,chat_history_id\n", "\n", "    ,issue_id\n", "\n", "    ,current_skill\n", "\n", "    ,channel_name\n", "\n", "    ,vendor_name\n", "\n", "    -- ,csat\n", "\n", "    ,lower(agent_email) as agent_email\n", "\n", "    ,assigned_at\n", "\n", "    ,queued_at\n", "\n", "    ,aat_secs\n", "\n", "    ,frt\n", "\n", "    ,agent_frt\n", "\n", "    ,aht_sys\n", "\n", "    ,aht\n", "\n", "    ,art_secs\n", "\n", "    ,rt_ulrt\n", "\n", "    ,IVA_flag\n", "\n", "    ,employment_type\n", "\n", "    ,Agent_stores_flag\n", "\n", "    ,gold_metric_flag\n", "\n", "    ,escalation_flag\n", "\n", "    ,agent_tenure\n", "\n", "    ,created_at\n", "\n", "    ,case when agent_tenure >= 0 and agent_tenure <= 30 then '0-30'\n", "\n", "    when agent_tenure > 30 then '>30' end as tenure_bucket\n", "\n", "    ,row_number() over (partition by a.session_id,chat_history_id order by assigned_at desc) as rn\n", "\n", "    ,row_number() over (partition by a.session_id order by assigned_at desc) as chat_rank\n", "\n", "from interim.cd_dashboard_base_table a\n", "\n", "left join (\n", "\n", "        Select \n", "\n", "            session_id, \n", "\n", "            created_at \n", "\n", "        from cd_etls.master_table \n", "\n", "        where \n", "        created_date >= cast(current_date -interval '7' day as date)\n", "\n", "    -- and created_date <= cast('2024-03-17' as date)\n", "\n", "       group by 1,2) b\n", "\n", "    on a.session_id = b.session_id\n", "\n", "where created_date >= cast(current_date -interval '7' day as date)\n", "\n", "    -- and created_date <= cast('2024-03-17' as date)\n", "\n", "-- and channel_name not in ('Gold 19-20 Image Verification','Image Verification')\n", "\n", ")\n", "\n", "\n", "\n", "-- ,metric_calc as\n", "\n", "\n", "Select\n", "\n", "-- week,\n", "\n", "ticket_date,\n", "\n", "agent_email,\n", "\n", "channel_name,\n", "case when channel_name like '%% live %%' then 'Live Channel' else 'Delivered Channel' end as Channel,\n", "case when (vendor_name is null and agent_email<>'<EMAIL>') then 'Blinkit Inhouse' else vendor_name end vendor_name,\n", "agent_disposition,\n", "current_skill,\n", "\n", "\n", "\n", "case when group_disposition in (\n", "\n", "                    'Wrong Order or Items','issue with an order-parts missing',\n", "\n", "                    'issue with an order-partial item/freebie missing',\n", "\n", "                    'Items missing or incorrect',\n", "\n", "                    'Product not available') then 'Items missing or incorrect'\n", "\n", "        when group_disposition in (\n", "\n", "                    'Payment Related Issue/Query' ,\n", "\n", "                    'issue with an order-delivery/surge/convenience/tip/any other charge',\n", "\n", "                    'Refund or Invoice Related Queries','Promo/Surge related Queries',\n", "\n", "                    'Price Description','cashback/refund/invoice-unsubscribe from sms',\n", "\n", "                    'cashback/refund/invoice-wallet delink',\n", "\n", "                    'cashback/refund/invoice-gc transfer to omp/imps',\n", "\n", "                    'ADONP/DD') then 'Payment/resolution related'\n", "\n", "        when group_disposition in (\n", "\n", "                    'Product Quality',\n", "\n", "                    'the item(s) are **defective** or **not working properly**',\n", "\n", "                    'issue with packaging-packaging not good/feedback for packaging',\n", "\n", "                    'yes, the **packaging** of the item(s) **is intact**',\n", "\n", "                    'issue with packaging-discreet packaging not done',\n", "\n", "                    'the item(s) are **physically damaged**',\n", "\n", "                    'expectation mismatch-exchange/return request',\n", "\n", "                    'the item(s) did not meet my **expectations**',\n", "\n", "                    'the item(s) are **not what i wanted**') then 'Product/Packagaing Quality'\n", "\n", "        when group_disposition in (\n", "\n", "                    'Delay in Delivery',\n", "\n", "                    'Order Status',\n", "\n", "                    'Delivery Instruction or Address change',\n", "\n", "                    'Modify Order Request',\n", "\n", "                    'Order Cancellation') then 'Pre Delivery Concerns'\n", "\n", "        when group_disposition in (\n", "\n", "                    'Cx Feedback dp/agent/general',\n", "\n", "                    'i want to report delivery partner misconduct',\n", "\n", "                    'Complaint against Delivery Partner or Agent') then 'Feedback'\n", "\n", "        when group_disposition in (\n", "\n", "                    'business with blinkit-business with blinkit',\n", "\n", "                    'no',\n", "\n", "                    'Miscellaneous or non-queries',\n", "\n", "                    'issue with an order-ordered by mistake',\n", "\n", "                    'Issue Placing an order',\n", "\n", "                    'Exchange or Return Item') then 'Miscellaneous'\n", "\n", "    else  group_disposition end as final_group_disposition,\n", "    \n", "    \n", "\n", "count(distinct case when chat_rank = 1 and (resolved_by_name = 'sys-inactivity-resolve-worker') then session_id end) as timed_out,\n", "\n", "count(distinct case when issue_id is not null then chat_history_id end) as chats,\n", "\n", "-- avg(case when issue_id is not null then aht end)*1.00/60 as avg_aht,\n", "sum(case when issue_id is not null then aht end)*1.00 aht_cal,\n", "\n", "avg(case when issue_id is not null then agent_frt end)*1.00/60 as avg_agent_frt,\n", "\n", "count(distinct case when issue_id is not null and chat_rank = 1 then chat_history_id end) as total_chats,\n", "\n", "coalesce(sum(case when issue_id is not null and chat_rank = 1 then csat end),0) as csat_sum,\n", "\n", "coalesce(sum(case when issue_id is not null and chat_rank = 1 then csat end),0)*1.0000/(count(distinct case when issue_id is not null and chat_rank = 1 and csat in (1,2,3,4,5) then chat_history_id end)+0.001) as AVG_CSAT,\n", "\n", "count(distinct case when issue_id is not null and chat_rank = 1 and csat in (1,2,3,4,5) then chat_history_id end) as rated,\n", "\n", "count(distinct case when issue_id is not null and chat_rank = 1 and csat in (1,2,3,4,5) then chat_history_id end)*100.00/(count(distinct case when issue_id is not null and chat_rank = 1 then chat_history_id end)+0.0001) as rated_perc,\n", "\n", "count(distinct case when issue_id is not null and chat_rank = 1 and csat in (4,5) then chat_history_id end) CSAT,\n", "IVA_flag\n", "\n", "from Master_table_base a\n", "\n", "where rn = 1 --and tenure_bucket = '>30'\n", "\n", "and channel_name <> 'Combined chat'\n", "\n", "-- and IVA_flag = 0\n", "\n", "group by 1,2,3,4,5,6,7,8,19\n", "order by 18 desc\n", "-- IVA_flag\n", "\n", "\n", "\n", "\"\"\"\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "df = pd.read_sql_query(sql=query, con=con)\n", "# df"]}, {"cell_type": "code", "execution_count": null, "id": "25fc2498-0f6b-453c-891c-df0f58cd5781", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1YgYGge5ZACb9n9TH3TqHA1gPswpfl7u8JZBeiVhmxSQ\"\n", "sheet_name = \"Raw\"\n", "pb.to_sheets(df, sheet_id, sheet_name, service_account=\"service_account\")\n", "sheet_id = \"1Rc1noVpV9VJzwM2FclhwHb_sCSpTSaAy0EVSgjABPFk\"\n", "sheet_name = \"Raw\"\n", "pb.to_sheets(df, sheet_id, sheet_name, service_account=\"service_account\")"]}, {"cell_type": "code", "execution_count": null, "id": "1fcdb646-ded2-4f39-a366-e437cad2cf5e", "metadata": {}, "outputs": [], "source": ["query1 = f\"\"\"\n", "\n", "\n", "With\n", "\n", "Master_table_base as(\n", "\n", "Select\n", "\n", "    p_key\n", "\n", "    ,extract(week from created_date) as week\n", "\n", "    ,created_date as ticket_date\n", "\n", "    ,a.session_id\n", "\n", "    ,conversation_id\n", "\n", "    ,order_id\n", "\n", "    ,cart_id\n", "\n", "    ,bot_last_node\n", "\n", "    ,agent_disposition\n", "\n", "    ,group_disposition\n", "\n", "    ,customer_id\n", "\n", "    ,karma_label\n", "\n", "    ,case when cast(karma_score as bigint) in (19,20) then 'Gold 19-20' \n", "\n", "        when cast(karma_score as bigint) in (17,18) then 'Gold 17-18'\n", "\n", "        else karma_label end as new_karma_label\n", "\n", "    ,karma_score\n", "\n", "    ,resolved_by_client_id\n", "\n", "    ,resolved_by\n", "\n", "    ,resolved_by_name\n", "\n", "    ,csat\n", "\n", "    ,chat_rated_flag\n", "\n", "    ,extreme_ratings\n", "\n", "    ,chat_history_id\n", "\n", "    ,issue_id\n", "\n", "    ,current_skill\n", "\n", "    ,channel_name\n", "\n", "    ,vendor_name\n", "\n", "    -- ,csat\n", "\n", "    ,lower(agent_email) as agent_email\n", "\n", "    ,assigned_at\n", "\n", "    ,queued_at\n", "\n", "    ,aat_secs\n", "\n", "    ,frt\n", "\n", "    ,agent_frt\n", "\n", "    ,aht_sys\n", "\n", "    ,aht\n", "\n", "    ,art_secs\n", "\n", "    ,rt_ulrt\n", "\n", "    ,IVA_flag\n", "\n", "    ,employment_type\n", "\n", "    ,Agent_stores_flag\n", "\n", "    ,gold_metric_flag\n", "\n", "    ,escalation_flag\n", "\n", "    ,agent_tenure\n", "\n", "    ,created_at\n", "\n", "    ,case when agent_tenure >= 0 and agent_tenure <= 30 then '0-30'\n", "\n", "    when agent_tenure > 30 then '>30' end as tenure_bucket\n", "\n", "    ,row_number() over (partition by a.session_id,chat_history_id order by assigned_at desc) as rn\n", "\n", "    ,row_number() over (partition by a.session_id order by assigned_at desc) as chat_rank\n", "\n", "from interim.cd_dashboard_base_table a\n", "\n", "left join (\n", "\n", "        Select \n", "\n", "            session_id, \n", "\n", "            created_at \n", "\n", "        from cd_etls.master_table \n", "\n", "        where \n", "        created_date >= cast(current_date -interval '1' day as date)\n", "\n", "    -- and created_date <= cast('2024-03-17' as date)\n", "\n", "       group by 1,2) b\n", "\n", "    on a.session_id = b.session_id\n", "\n", "where created_date >= cast(current_date -interval '35' day as date)\n", "\n", "    -- and created_date <= cast('2024-03-17' as date)\n", "\n", " and channel_name not in ('Gold 19-20 Image Verification','Image Verification')\n", "\n", ")\n", "\n", "\n", "\n", "-- ,metric_calc as\n", "\n", "\n", "Select\n", "\n", "week,\n", "\n", "-- ticket_date,\n", "\n", "'agent' agent_email,\n", "\n", "channel_name,\n", "case when channel_name like '%% live %%' then 'Live Channel' else 'Delivered Channel' end as Channel,\n", "case when (vendor_name is null and agent_email<>'<EMAIL>') then 'Blinkit Inhouse' else vendor_name end vendor_name,\n", "agent_disposition,\n", "current_skill,\n", "\n", "\n", "\n", "case when group_disposition in (\n", "\n", "                    'Wrong Order or Items','issue with an order-parts missing',\n", "\n", "                    'issue with an order-partial item/freebie missing',\n", "\n", "                    'Items missing or incorrect',\n", "\n", "                    'Product not available') then 'Items missing or incorrect'\n", "\n", "        when group_disposition in (\n", "\n", "                    'Payment Related Issue/Query' ,\n", "\n", "                    'issue with an order-delivery/surge/convenience/tip/any other charge',\n", "\n", "                    'Refund or Invoice Related Queries','Promo/Surge related Queries',\n", "\n", "                    'Price Description','cashback/refund/invoice-unsubscribe from sms',\n", "\n", "                    'cashback/refund/invoice-wallet delink',\n", "\n", "                    'cashback/refund/invoice-gc transfer to omp/imps',\n", "\n", "                    'ADONP/DD') then 'Payment/resolution related'\n", "\n", "        when group_disposition in (\n", "\n", "                    'Product Quality',\n", "\n", "                    'the item(s) are **defective** or **not working properly**',\n", "\n", "                    'issue with packaging-packaging not good/feedback for packaging',\n", "\n", "                    'yes, the **packaging** of the item(s) **is intact**',\n", "\n", "                    'issue with packaging-discreet packaging not done',\n", "\n", "                    'the item(s) are **physically damaged**',\n", "\n", "                    'expectation mismatch-exchange/return request',\n", "\n", "                    'the item(s) did not meet my **expectations**',\n", "\n", "                    'the item(s) are **not what i wanted**') then 'Product/Packagaing Quality'\n", "\n", "        when group_disposition in (\n", "\n", "                    'Delay in Delivery',\n", "\n", "                    'Order Status',\n", "\n", "                    'Delivery Instruction or Address change',\n", "\n", "                    'Modify Order Request',\n", "\n", "                    'Order Cancellation') then 'Pre Delivery Concerns'\n", "\n", "        when group_disposition in (\n", "\n", "                    'Cx Feedback dp/agent/general',\n", "\n", "                    'i want to report delivery partner misconduct',\n", "\n", "                    'Complaint against Delivery Partner or Agent') then 'Feedback'\n", "\n", "        when group_disposition in (\n", "\n", "                    'business with blinkit-business with blinkit',\n", "\n", "                    'no',\n", "\n", "                    'Miscellaneous or non-queries',\n", "\n", "                    'issue with an order-ordered by mistake',\n", "\n", "                    'Issue Placing an order',\n", "\n", "                    'Exchange or Return Item') then 'Miscellaneous'\n", "\n", "    else  group_disposition end as final_group_disposition,\n", "    \n", "    \n", "\n", "count(distinct case when chat_rank = 1 and (resolved_by_name = 'sys-inactivity-resolve-worker') then session_id end) as timed_out,\n", "\n", "count(distinct case when issue_id is not null then chat_history_id end) as chats,\n", "\n", "-- avg(case when issue_id is not null then aht end)*1.00/60 as avg_aht,\n", "sum(case when issue_id is not null then aht end)*1.00 aht_cal,\n", "\n", "avg(case when issue_id is not null then agent_frt end)*1.00/60 as avg_agent_frt,\n", "\n", "count(distinct case when issue_id is not null and chat_rank = 1 then chat_history_id end) as total_chats,\n", "\n", "coalesce(sum(case when issue_id is not null and chat_rank = 1 then csat end),0) as csat_sum,\n", "\n", "coalesce(sum(case when issue_id is not null and chat_rank = 1 then csat end),0)*1.0000/(count(distinct case when issue_id is not null and chat_rank = 1 and csat in (1,2,3,4,5) then chat_history_id end)+0.001) as AVG_CSAT,\n", "\n", "count(distinct case when issue_id is not null and chat_rank = 1 and csat in (1,2,3,4,5) then chat_history_id end) as rated,\n", "\n", "count(distinct case when issue_id is not null and chat_rank = 1 and csat in (1,2,3,4,5) then chat_history_id end)*100.00/(count(distinct case when issue_id is not null and chat_rank = 1 then chat_history_id end)+0.0001) as rated_perc,\n", "\n", "count(distinct case when issue_id is not null and chat_rank = 1 and csat in (4,5) then chat_history_id end) CSAT\n", "\n", "from Master_table_base a\n", "\n", "where rn = 1 --and tenure_bucket = '>30'\n", "\n", "and channel_name <> 'Combined chat'\n", "\n", "-- and IVA_flag = 0\n", "\n", "group by 1,2,3,4,5,6,7,8\n", "--order by 18 desc\n", "-- IVA_flag\n", "\n", "\n", "\n", "\"\"\"\n", "\n", "df1 = pd.read_sql_query(sql=query1, con=con)\n", "# df"]}, {"cell_type": "code", "execution_count": null, "id": "39c750d5-f631-4c2d-ad0e-77d1e8fb3c15", "metadata": {}, "outputs": [], "source": ["df1"]}, {"cell_type": "code", "execution_count": null, "id": "52f85992-f695-4ad8-94f0-dd44726d84f7", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1YgYGge5ZACb9n9TH3TqHA1gPswpfl7u8JZBeiVhmxSQ\"\n", "sheet_name = \"Raw Week\"\n", "pb.to_sheets(df1, sheet_id, sheet_name, service_account=\"service_account\")\n", "sheet_id = \"1Rc1noVpV9VJzwM2FclhwHb_sCSpTSaAy0EVSgjABPFk\"\n", "sheet_name = \"Raw Week\"\n", "pb.to_sheets(df1, sheet_id, sheet_name, service_account=\"service_account\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}