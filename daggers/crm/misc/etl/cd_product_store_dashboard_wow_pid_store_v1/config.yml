alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: cd_product_store_dashboard_wow_pid_store_v1
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: crm
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U080U626BEF
path: crm/misc/etl/cd_product_store_dashboard_wow_pid_store_v1
paused: false
pool: crm_pool
project_name: misc
schedule:
  end_date: '2025-08-31T00:00:00'
  interval: 0 */6 * * *
  start_date: '2025-06-05T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 4
