{"cells": [{"cell_type": "code", "execution_count": null, "id": "b6ecb023-8f04-427e-9b04-2ecab5186acb", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "from datetime import date, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "568b0d71-9e2a-42be-8df9-b00531f2a8c5", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "14d7aa68-91ce-4742-b01d-9dc44ff503ec", "metadata": {}, "outputs": [], "source": ["# start_dates = [25, 26, 27, 28, 29]"]}, {"cell_type": "code", "execution_count": null, "id": "38a2593b-1123-4747-af42-61057a0d2f03", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d511784c-5c8e-4f48-b7d7-807ebe2f78eb", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"cd_etls\",\n", "    \"table_name\": \"cd_product_store_dashboard_wow_pid_store_1\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"week\", \"type\": \"date\", \"description\": \"text\"},\n", "        {\"name\": \"week_no\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"manufacturer_id\", \"type\": \"VARCHAR\", \"description\": \"manufacturer_id\"},\n", "        {\"name\": \"product_id\", \"type\": \"VARCHAR\", \"description\": \"text\"},\n", "        {\"name\": \"frontend_merchant_id\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"city\", \"type\": \"VARCHAR\", \"description\": \"text\"},\n", "        {\"name\": \"l0_category_id\", \"type\": \"VARCHAR\", \"description\": \"text\"},\n", "        {\"name\": \"l1_category_id\", \"type\": \"VARCHAR\", \"description\": \"text\"},\n", "        {\"name\": \"l2_category_id\", \"type\": \"VARCHAR\", \"description\": \"text\"},\n", "        {\"name\": \"brand_id\", \"type\": \"VARCHAR\", \"description\": \"text\"},\n", "        {\"name\": \"product_type_id\", \"type\": \"VARCHAR\", \"description\": \"text\"},\n", "        {\"name\": \"current_week_carts\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\"name\": \"current_week_complaint_carts\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\"name\": \"current_week_resolved_carts\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_carts\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_complaint_carts\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_resolved_carts\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\n", "            \"name\": \"current_week_total_carts_city\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"text\",\n", "        },\n", "        {\n", "            \"name\": \"current_week_total_complaint_carts_city\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"text\",\n", "        },\n", "        {\n", "            \"name\": \"current_week_total_resolved_carts_city\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"text\",\n", "        },\n", "        {\"name\": \"prev_week_total_carts_city\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\n", "            \"name\": \"prev_week_total_complaint_carts_city\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"text\",\n", "        },\n", "        {\n", "            \"name\": \"prev_week_total_resolved_carts_city\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"text\",\n", "        },\n", "        {\"name\": \"carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_pid_carts\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\"name\": \"pid_complaint_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_pid_complaints\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\"name\": \"pid_resolved_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_pid_resolved_carts\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\"name\": \"brand_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_brand_carts\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\"name\": \"brand_complaint_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_brand_complaints\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\"name\": \"brand_resolved_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\n", "            \"name\": \"prev_week_brand_resolved_carts\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"text\",\n", "        },\n", "        {\"name\": \"ptype_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_ptype_carts\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\"name\": \"ptype_complaint_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_ptype_complaints\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\"name\": \"ptype_resolved_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\n", "            \"name\": \"prev_week_ptype_resolved_carts\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"text\",\n", "        },\n", "        {\"name\": \"ptype_brand_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_ptype_brand_carts\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\n", "            \"name\": \"ptype_brand_complaint_carts\",\n", "            \"type\": \"BIGINT\",\n", "            \"description\": \"text\",\n", "        },\n", "        {\n", "            \"name\": \"prev_week_ptype_brand_complaints\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"text\",\n", "        },\n", "        {\"name\": \"ptype_brand_resolved_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\n", "            \"name\": \"prev_week_ptype_brand_resolved_carts\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"text\",\n", "        },\n", "        {\"name\": \"l2_brand_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_l2_brand_carts\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\"name\": \"l2_brand_complaint_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\n", "            \"name\": \"prev_week_l2_brand_complaints\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"text\",\n", "        },\n", "        {\"name\": \"l2_brand_resolved_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\n", "            \"name\": \"prev_week_l2_brand_resolved_carts\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"text\",\n", "        },\n", "        {\"name\": \"l2_ptype_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_l2_ptype_carts\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\"name\": \"l2_ptype_complaint_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\n", "            \"name\": \"prev_week_l2_ptype_complaints\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"text\",\n", "        },\n", "        {\"name\": \"l2_ptype_resolved_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\n", "            \"name\": \"prev_week_l2_ptype_resolved_carts\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"text\",\n", "        },\n", "        {\"name\": \"l0_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_l0_carts\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\"name\": \"l0_complaint_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_l0_complaints\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\"name\": \"l0_resolved_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_l0_resolved_carts\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\"name\": \"l1_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_l1_carts\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\"name\": \"l1_complaint_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_l1_complaints\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\"name\": \"l1_resolved_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_l1_resolved_carts\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\"name\": \"l2_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_l2_carts\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\"name\": \"l2_complaint_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_l2_complaints\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\"name\": \"l2_resolved_carts\", \"type\": \"BIGINT\", \"description\": \"text\"},\n", "        {\"name\": \"prev_week_l2_resolved_carts\", \"type\": \"REAL\", \"description\": \"text\"},\n", "        {\n", "            \"name\": \"new_product_flag\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"flag\",\n", "        },\n", "        {\n", "            \"name\": \"weighted_ptype_carts\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"flag\",\n", "        },\n", "        {\n", "            \"name\": \"weighted_prev_week_ptype_carts\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"flag\",\n", "        },\n", "        {\n", "            \"name\": \"weighted_ptype_complaints\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"flag\",\n", "        },\n", "        {\n", "            \"name\": \"weighted_prev_week_ptype_complaints\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"flag\",\n", "        },\n", "        {\n", "            \"name\": \"weighted_ptype_resolved\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"flag\",\n", "        },\n", "        {\n", "            \"name\": \"weighted_brand_carts\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"flag\",\n", "        },\n", "        {\n", "            \"name\": \"weighted_prev_week_brand_carts\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"flag\",\n", "        },\n", "        {\n", "            \"name\": \"weighted_brand_complaints\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"flag\",\n", "        },\n", "        {\n", "            \"name\": \"weighted_prev_week_brand_complaints\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"flag\",\n", "        },\n", "        {\n", "            \"name\": \"weighted_brand_resolved\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"flag\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\n", "        \"week_no\",\n", "        \"frontend_merchant_id\",\n", "        \"manufacturer_id\",\n", "        \"l0_category_id\",\n", "        \"l1_category_id\",\n", "        \"l2_category_id\",\n", "        \"product_type_id\",\n", "        \"brand_id\",\n", "        \"product_id\",\n", "    ],\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"Product Store week on week complaints\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "10bb5ab8-e12c-44a5-9066-8dd7eae8293c", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "\n", "with sales_base as \n", "(\n", "    Select \n", "    brand_id, \n", "    date_, \n", "    frontend_merchant_id, \n", "    l0_category_id, \n", "    l1_category_id, \n", "    l2_category_id, \n", "    manufacturer_id, \n", "    product_id, \n", "    product_type_id,\n", "    carts\n", "    \n", "    from cd_etls.cd_product_store_dashboard_sales_v1\n", "    where\n", "    date_ >= current_date - interval '30' day\n", "    and manufacturer_id !='all'\n", "  \n", "    union all \n", "      \n", "    Select \n", "    \n", "    brand_id, \n", "    date_, \n", "    frontend_merchant_id, \n", "    l0_category_id, \n", "    l1_category_id, \n", "    l2_category_id, \n", "    'overall' as manufacturer_id, \n", "    product_id, \n", "    product_type_id,\n", "    carts\n", "    \n", "    from cd_etls.cd_product_store_dashboard_sales\n", "    where date_ >= current_date - interval '30' day\n", ") \n", "\n", "\n", ",complaints_base as\n", "(\n", "    Select \n", "    brand_id, \n", "    date_, \n", "    frontend_merchant_id, \n", "    l0_category_id, \n", "    l1_category_id, \n", "    l2_category_id, \n", "    manufacturer_id, \n", "    product_id, \n", "    product_type_id, \n", "    complaint_carts,\n", "    resolved_carts\n", "\n", "    from cd_etls.cd_product_store_dashboard_complaints_v1\n", "    where complaint_type='all'\n", "    and manufacturer_id !='all'\n", "    and date_ >= current_date - interval '30' day \n", "\n", "    union all\n", "    \n", "    Select \n", "    brand_id, \n", "    date_, \n", "    frontend_merchant_id, \n", "    l0_category_id, \n", "    l1_category_id, \n", "    l2_category_id, \n", "    'overall' as manufacturer_id,\n", "    product_id, \n", "    product_type_id, \n", "    complaint_carts,\n", "    resolved_carts\n", "    \n", "    \n", "    from cd_etls.cd_product_store_dashboard_complaints\n", "    where complaint_type='all'\n", "    and date_ >= current_date - interval '30' day \n", "    \n", "),\n", "\n", "\n", "store_base as (\n", "Select \n", "    frontend_merchant_id,\n", "    frontend_merchant_name,\n", "    frontend_merchant_city_name\n", "from dwh.dim_merchant_outlet_facility_mapping\n", "where is_current\n", "and is_current_mapping_active)\n", "\n", "    ----------------------------------------------------------------------------------------------------------------------------------------------------------\n", "   ,pid_sales_base as (\n", "    Select \n", "       DATE_TRUNC('week',date_) as week,\n", "        extract(week from date_) as week_no,\n", "        ds.product_id,\n", "        ds.manufacturer_id,\n", "        ds.l0_category_id,\n", "        ds.l1_category_id,\n", "        ds.l2_category_id,\n", "        ds.brand_id,\n", "        ds.product_type_id,\n", "        frontend_merchant_id,\n", "        sum(carts) carts\n", "    from sales_base ds \n", "    where ds.product_id!='all'\n", "    group by 1,2,3,4,5,6,7,8,9,10\n", "    )\n", "\n", "    \n", "    \n", " ,pid_complaints_base as (\n", "    Select  \n", "        --  date_,\n", "        -- ds.frontend_merchant_id,\n", "        DATE_TRUNC('week',date_) as week,       \n", "        extract(week from date_) as week_no,\n", "        manufacturer_id, \n", "        product_id,\n", "        l2_category_id\n", "        ,frontend_merchant_id\n", "        ,sum(complaint_carts) complaint_carts\n", "        ,sum(resolved_carts) resolved_carts\n", "   \n", "    from complaints_base ds \n", "    where ds.product_id!='all'\n", "    group by 1,2,3,4,5,6\n", ")\n", "\n", "\n", "    , pid_base as (\n", "    Select \n", "        ps.week,\n", "        ps.week_no,\n", "        ps.product_id,\n", "        ps.manufacturer_id,\n", "        ps.l0_category_id,\n", "        ps.l1_category_id,\n", "        ps.l2_category_id,\n", "        ps.brand_id,\n", "        ps.product_type_id,\n", "        ps.frontend_merchant_id,\n", "        ps.carts,\n", "        frontend_merchant_city_name as city,\n", "        frontend_merchant_name,\n", "        coalesce(complaint_carts,0) complaint_carts,\n", "        coalesce(resolved_carts,0) resolved_carts\n", "    from pid_sales_base ps \n", "        left join pid_complaints_base pc \n", "            on ps.product_id=pc.product_id\n", "            and ps.l2_category_id=pc.l2_category_id\n", "            AND ps.week_no=pc.week_no\n", "            and ps.frontend_merchant_id=pc.frontend_merchant_id\n", "            and ps.manufacturer_id=pc.manufacturer_id\n", "        left join store_base sb \n", "        on ps.frontend_merchant_id=sb.frontend_merchant_id\n", "    )\n", "\n", "\n", "\n", "    -- Select *\n", "    -- from pid_base\n", "    -- where product_id='520568'\n", "    -- and frontend_merchant_id=29856\n", "\n", ", Product_Weekly_Data AS\n", "(\n", "    SELECT\n", "        product_id,\n", "        week_no,\n", "        SUM(carts) AS total_carts,\n", "        SUM(complaint_carts) AS total_complaint_carts,\n", "        SUM(resolved_carts) AS total_resolved_carts\n", "    FROM pid_base\n", "    where manufacturer_id='overall'\n", "    GROUP BY product_id, week_no\n", "),\n", "\n", "\n", "\n", "pid_final_weekly as (\n", "SELECT\n", "    current_week.product_id as pid,\n", "    current_week.week_no as wn,\n", "    current_week.total_carts AS current_week_carts,\n", "    current_week.total_complaint_carts AS current_week_complaint_carts,\n", "    current_week.total_resolved_carts AS current_week_resolved_carts,\n", "    previous_week.week_no AS prev_week_no,\n", "    previous_week.total_carts AS prev_week_carts,\n", "    previous_week.total_complaint_carts AS prev_week_complaint_carts,\n", "    previous_week.total_resolved_carts AS prev_week_resolved_carts\n", "FROM Product_Weekly_Data AS current_week\n", "LEFT JOIN Product_Weekly_Data AS previous_week\n", "ON current_week.product_id = previous_week.product_id\n", "AND current_week.week_no = previous_week.week_no + 1\n", ")\n", "\n", ", Product_Weekly_Data_city AS (\n", "    SELECT\n", "        product_id,\n", "        week_no,\n", "        city,\n", "        SUM(carts) AS total_carts_city,\n", "        SUM(complaint_carts) AS total_complaint_carts_city,\n", "        SUM(resolved_carts) AS total_resolved_carts_city\n", "    FROM pid_base\n", "    where manufacturer_id='overall'\n", "    GROUP BY product_id, week_no,3\n", "),\n", "\n", "pid_final_weekly_city as \n", "(\n", "SELECT\n", "    current_week.product_id as pid,\n", "    current_week.week_no as wn,\n", "    current_week.city,\n", "    current_week.total_carts_city AS current_week_total_carts_city,\n", "    current_week.total_complaint_carts_city AS current_week_total_complaint_carts_city,\n", "    current_week.total_resolved_carts_city AS current_week_total_resolved_carts_city,\n", "    previous_week.week_no AS prev_week_no,\n", "    previous_week.total_carts_city AS prev_week_total_carts_city,\n", "    previous_week.total_complaint_carts_city AS prev_week_total_complaint_carts_city,\n", "    previous_week.total_resolved_carts_city AS prev_week_total_resolved_carts_city\n", "FROM Product_Weekly_Data_city AS current_week\n", "LEFT JOIN Product_Weekly_Data_city AS previous_week\n", "ON current_week.product_id = previous_week.product_id\n", "AND current_week.week_no = previous_week.week_no + 1 and current_week.city=previous_week.city\n", ")\n", "\n", "\n", " ,pid_final_base as \n", " (\n", "    Select distinct \n", "        week,\n", "        ps.week_no,\n", "        ps.product_id,\n", "        ps.manufacturer_id,\n", "        frontend_merchant_id,\n", "        frontend_merchant_name,\n", "        ps.city,\n", "        l0_category_id,\n", "        l1_category_id,\n", "        l2_category_id,\n", "        brand_id,\n", "        product_type_id,\n", "        current_week_carts,\n", "current_week_complaint_carts,\n", "current_week_resolved_carts,\n", "prev_week_carts,\n", "prev_week_complaint_carts,\n", "prev_week_resolved_carts,\n", "current_week_total_carts_city,\n", "current_week_total_complaint_carts_city,\n", "current_week_total_resolved_carts_city,\n", "prev_week_total_carts_city,\n", "prev_week_total_complaint_carts_city,\n", "prev_week_total_resolved_carts_city,\n", "        carts,\n", "        lag(carts,1) over (partition by ps.product_id, ps.frontend_merchant_id, ps.manufacturer_id order by ps.week_no) as prev_week_pid_carts,\n", "        \n", "        complaint_carts as pid_complaint_carts,\n", "        lag(complaint_carts,1) over (partition by product_id, frontend_merchant_id, ps.manufacturer_id order by week_no) as prev_week_pid_complaints,\n", "\n", "        resolved_carts as pid_resolved_carts,\n", "        lag(resolved_carts,1) over (partition by product_id, frontend_merchant_id, ps.manufacturer_id order by week_no) as prev_week_pid_resolved_carts\n", "\n", "\n", "    from pid_base ps \n", "    left join pid_final_weekly pfw on ps.product_id=pfw.pid and ps.week_no=pfw.wn \n", "    left join pid_final_weekly_city pfwc on ps.product_id=pfwc.pid and ps.week_no=pfwc.wn and ps.city=pfwc.city \n", "    \n", "    \n", "    )\n", "\n", "\n", "\n", "\n", "-- select *\n", "-- from pid_final_base\n", "-- where product_id = '522793'\n", "-- and manufacturer_id = 'overall'\n", "\n", "\n", "--     SELECT *\n", "-- FROM pid_final_base\n", "-- WHERE product_id='520568'\n", "\n", "\n", "    -- Select \n", "    --     * \n", "    -- from pid_final_base\n", "    -- where product_id='160'\n", "    -- and frontend_merchant_id=29856\n", "\n", "\n", "    -------------------------------------------------------------------------------------------------------------------------------------------\n", "    ,brand_sales_base as (\n", "    Select \n", "        DATE_TRUNC('week',date_) as  week,\n", "        extract(week from date_) as week_no,\n", "        manufacturer_id,\n", "        brand_id,\n", "        frontend_merchant_id,\n", "        sum(carts) carts\n", "    from sales_base ds \n", "    where ds.product_id='all' and \n", "    l0_category_id='all'\n", "    and product_type_id='all'\n", "    group by 1,2,3,4,5\n", "    )\n", "\n", "\n", "    ,brand_complaints_base as \n", "    (\n", "    Select  \n", "\n", "        DATE_TRUNC('week',date_)  as  week,        \n", "        extract(week from date_) as week_no,\n", "        brand_id, \n", "        manufacturer_id,\n", "        frontend_merchant_id\n", "        ,sum(complaint_carts) complaint_carts\n", "        ,sum(resolved_carts) resolved_carts\n", "    from complaints_base ds \n", "    where ds.product_id='all'\n", "    and l0_category_id='all'\n", "    and product_type_id='all'\n", "    group by 1,2,3,4,5\n", "    )\n", "\n", "\n", "    , brand_base as (\n", "    Select \n", "        bs.week_no,\n", "        bs.week,\n", "        bs.manufacturer_id,\n", "        bs.frontend_merchant_id,\n", "        bs.brand_id,\n", "        carts as brand_carts,\n", "        coalesce(complaint_carts,0) brand_complaint_carts,\n", "        coalesce(resolved_carts,0) brand_resolved_carts\n", "       \n", "    from  brand_sales_base bs \n", "        left join brand_complaints_base bc \n", "            on bs.brand_id=bc.brand_id\n", "            and bs.week_no=bc.week_no\n", "            and bs.frontend_merchant_id=bc.frontend_merchant_id\n", "            and bs.manufacturer_id=bc.manufacturer_id\n", "    )\n", "\n", "\n", "    ,brand_final_base as \n", "    (\n", "    Select \n", "        week_no,\n", "        week,\n", "        frontend_merchant_id,\n", "        brand_id,\n", "        manufacturer_id,\n", "\n", "        brand_carts,\n", "        lag(brand_carts,1) over (partition by brand_id,frontend_merchant_id, manufacturer_id order by week_no) as prev_week_brand_carts,\n", "\n", "        brand_complaint_carts,\n", "        lag(brand_complaint_carts,1) over (partition by brand_id,frontend_merchant_id,manufacturer_id order by week_no) as prev_week_brand_complaints,\n", "\n", "        brand_resolved_carts,\n", "        lag(brand_resolved_carts,1) over (partition by brand_id,frontend_merchant_id,manufacturer_id order by week_no) as prev_week_brand_resolved_carts\n", "\n", "\n", "    from brand_base )\n", "\n", "    --------------------------------------------------------------------------------------------------------------------------------------\n", "    ,ptype_sales_base as (\n", "    Select \n", "        DATE_TRUNC('week',date_)  as  week,\n", "        extract(week from date_) as week_no,\n", "        manufacturer_id,\n", "        product_type_id,\n", "        frontend_merchant_id,\n", "        sum(carts) carts\n", "    from sales_base ds \n", "    where ds.product_id='all' and \n", "    l0_category_id='all'\n", "    and brand_id='all'\n", "    group by 1,2,3,4,5\n", "    )\n", "\n", "\n", "    ,ptype_complaints_base as (\n", "      Select  \n", "  \n", "        DATE_TRUNC('week',date_) as  week,        \n", "        extract(week from date_) as week_no,\n", "        product_type_id,\n", "        manufacturer_id\n", "        ,frontend_merchant_id\n", "        ,sum(complaint_carts) complaint_carts\n", "        ,sum(resolved_carts) resolved_carts\n", "    from complaints_base ds \n", "    where ds.product_id='all'\n", "    and l0_category_id='all'\n", "    and brand_id='all'\n", "    group by 1,2,3,4,5\n", "    )\n", "\n", "    , ptype_base as (\n", "    Select \n", "        pts.week_no,\n", "        pts.week,\n", "        pts.manufacturer_id,\n", "        pts.frontend_merchant_id,\n", "        pts.product_type_id,\n", "        carts as ptype_carts,\n", "        coalesce(complaint_carts,0) ptype_complaint_carts,\n", "        coalesce(resolved_carts,0) ptype_resolved_carts\n", "        from ptype_sales_base pts \n", "        left join ptype_complaints_base ptc \n", "            on pts.product_type_id=ptc.product_type_id\n", "            and pts.week_no=ptc.week_no\n", "            and pts.frontend_merchant_id=ptc.frontend_merchant_id\n", "            and pts.manufacturer_id=ptc.manufacturer_id\n", "           \n", "            \n", "    )\n", "\n", "\n", "\n", "     , ptype_final_base as (\n", "\n", "    Select \n", "        week_no,\n", "        week,\n", "        frontend_merchant_id,\n", "        product_type_id,\n", "        manufacturer_id,\n", "\n", "        ptype_carts,\n", "        lag(ptype_carts,1) over (partition by product_type_id,frontend_merchant_id,manufacturer_id order by week_no) as prev_week_ptype_carts,\n", "\n", "        ptype_complaint_carts,\n", "        lag(ptype_complaint_carts,1) over (partition by product_type_id,frontend_merchant_id,manufacturer_id order by week_no) as prev_week_ptype_complaints,\n", "\n", "        ptype_resolved_carts,\n", "        lag(ptype_resolved_carts,1) over (partition by product_type_id,frontend_merchant_id,manufacturer_id order by week_no) as prev_week_ptype_resolved_carts\n", "\n", "    from ptype_base )\n", "\n", "\n", "\n", "    --------------------------------------------------------------------------------------------------------------------------\n", "    ,ptype_brand_sales_base as (\n", "    Select \n", "        DATE_TRUNC('week',date_) as week,\n", "        extract(week from date_) as week_no,\n", "        manufacturer_id,\n", "        product_type_id,\n", "        brand_id,\n", "        frontend_merchant_id,\n", "        sum(carts) carts\n", "    from sales_base ds \n", "    where ds.l0_category_id='all'\n", "    and brand_id!='all'\n", "    and product_type_id!='all'\n", "    and product_id='all'\n", "    group by 1,2,3,4,5,6\n", "    )\n", "\n", "\n", "    ,ptype_brand_complaints_base as (\n", "    Select  \n", "        DATE_TRUNC('week',date_) as week,\n", "        extract(week from date_) as week_no,\n", "        product_type_id,\n", "        manufacturer_id,\n", "        brand_id,\n", "        frontend_merchant_id\n", "        ,sum(complaint_carts) complaint_carts\n", "        ,sum(resolved_carts) resolved_carts\n", "    from complaints_base ds \n", "    where ds.l0_category_id='all'\n", "    and brand_id!='all'\n", "    and product_type_id!='all'\n", "    and product_id='all'\n", "    group by 1,2,3,4,5,6\n", "    )\n", "\n", "\n", "    , ptype_brand_base as (\n", "    Select \n", "        pbb.week_no,\n", "        pbb.week,\n", "        pbb.manufacturer_id,\n", "        pbb.frontend_merchant_id,\n", "        pbb.product_type_id,\n", "        pbb.brand_id,\n", "        carts as ptype_brand_carts,\n", "        coalesce(complaint_carts,0) ptype_brand_complaint_carts,\n", "        coalesce(resolved_carts,0) ptype_brand_resolved_carts\n", "    from  ptype_brand_sales_base pbb \n", "        left join ptype_brand_complaints_base pcb\n", "            on pbb.product_type_id=pcb.product_type_id\n", "            and pbb.brand_id=pcb.brand_id\n", "            and pbb.week_no=pcb.week_no\n", "            and pbb.frontend_merchant_id=pcb.frontend_merchant_id\n", "            and pbb.manufacturer_id=pcb.manufacturer_id\n", "            \n", "    ), \n", "    \n", "    ptype_brand_final_base as (\n", "    Select \n", "        week_no,\n", "        week,\n", "        manufacturer_id,\n", "        frontend_merchant_id,\n", "        product_type_id,\n", "        brand_id,\n", "\n", "        ptype_brand_carts,\n", "        lag(ptype_brand_carts,1) over (partition by product_type_id, brand_id,frontend_merchant_id,manufacturer_id order by week_no) as prev_week_ptype_brand_carts,\n", "\n", "        ptype_brand_complaint_carts,\n", "        lag(ptype_brand_complaint_carts,1) over (partition by product_type_id, brand_id,frontend_merchant_id,manufacturer_id order by week_no) as prev_week_ptype_brand_complaints,\n", "\n", "        ptype_brand_resolved_carts,\n", "        lag(ptype_brand_resolved_carts,1) over (partition by product_type_id, brand_id,frontend_merchant_id,manufacturer_id order by week_no) as prev_week_ptype_brand_resolved_carts\n", "\n", "    from ptype_brand_base )\n", "\n", "\n", "    -----------------------------------------------------------------------------------------------------------\n", "    ,l2_brand_sales_base as (\n", "    Select \n", "     DATE_TRUNC('week',date_) as week,\n", "        extract(week from date_) as week_no,\n", "        l2_category_id,\n", "        brand_id,\n", "        manufacturer_id,\n", "        frontend_merchant_id,\n", "        sum(carts) carts\n", "    from sales_base ds \n", "    where ds.l2_category_id!='all'\n", "    and brand_id!='all'\n", "    and product_type_id='all'\n", "    and product_id='all'\n", "    group by 1,2,3,4,5,6\n", "    )\n", "\n", "\n", "\n", "    ,l2_brand_complaints_base as (\n", "    Select  \n", "        DATE_TRUNC('week',date_) as week,       \n", "        extract(week from date_) as week_no,\n", "        l2_category_id,\n", "        manufacturer_id,\n", "        brand_id\n", "        ,frontend_merchant_id\n", "        ,sum(complaint_carts) complaint_carts\n", "        ,sum(resolved_carts) resolved_carts\n", "    from complaints_base ds \n", "    where ds.l2_category_id!='all'\n", "    and brand_id!='all'\n", "    and product_type_id='all'\n", "    and product_id='all'\n", "    group by 1,2,3,4,5,6\n", "    )\n", "\n", "    , l2_brand_base as (\n", "    Select \n", "        l2bb.week_no,\n", "        l2bb.week,\n", "        l2bb.frontend_merchant_id,\n", "        l2bb.l2_category_id,\n", "        l2bb.manufacturer_id,\n", "        l2bb.brand_id,\n", "        carts as l2_brand_carts,\n", "        coalesce(complaint_carts,0) l2_brand_complaint_carts,\n", "        coalesce(resolved_carts,0) l2_brand_resolved_carts\n", "    from  l2_brand_sales_base l2bb \n", "        left join l2_brand_complaints_base l2cbb\n", "            on l2bb.l2_category_id=l2cbb.l2_category_id\n", "            and l2bb.brand_id=l2cbb.brand_id\n", "            and l2bb.week_no=l2cbb.week_no\n", "            and l2bb.frontend_merchant_id=l2cbb.frontend_merchant_id\n", "            \n", "            and l2bb.manufacturer_id=l2cbb.manufacturer_id\n", "            \n", "    )\n", "\n", "    ,l2_brand_final_base as (\n", "    Select \n", "        week_no,\n", "        week,\n", "        manufacturer_id,\n", "        frontend_merchant_id,\n", "        L2_category_id,\n", "        brand_id,\n", "\n", "        l2_brand_carts,\n", "        lag(l2_brand_carts,1) over (partition by L2_category_id, brand_id,frontend_merchant_id, manufacturer_id order by week_no) as prev_week_l2_brand_carts,\n", "\n", "        l2_brand_complaint_carts,\n", "        lag(l2_brand_complaint_carts,1) over (partition by L2_category_id, brand_id,frontend_merchant_id,manufacturer_id order by week_no) as prev_week_l2_brand_complaints,\n", "\n", "        l2_brand_resolved_carts,\n", "        lag(l2_brand_resolved_carts,1) over (partition by L2_category_id, brand_id,frontend_merchant_id,manufacturer_id order by week_no) as prev_week_l2_brand_resolved_carts\n", "\n", "    from l2_brand_base )\n", "\n", "    ------------------------------------------------------------------------------------------------------------------------\n", "    ,ptype_l2_sales_base as (\n", "    Select \n", "        DATE_TRUNC('week',date_) as week,       \n", "        extract(week from date_) as week_no,\n", "        manufacturer_id,\n", "        l2_category_id,\n", "        product_type_id,\n", "        frontend_merchant_id,\n", "        sum(carts) carts\n", "    from sales_base ds \n", "    where ds.l2_category_id!='all'\n", "    and brand_id='all'\n", "    and product_type_id!='all'\n", "    and product_id='all'\n", "    group by 1,2,3,4,5,6\n", "    )\n", "\n", "\n", "    ,ptype_l2_complaints as (\n", "    Select  \n", "        DATE_TRUNC('week',date_) as week,\n", "        extract(week from date_) as week_no,\n", "        l2_category_id,\n", "        manufacturer_id,\n", "        product_type_id\n", "        ,frontend_merchant_id\n", "        ,sum(complaint_carts) complaint_carts\n", "        ,sum(resolved_carts) resolved_carts\n", "    from complaints_base ds \n", "    where ds.l2_category_id!='all'\n", "    and brand_id='all'\n", "    and product_type_id!='all'\n", "    and product_id='all'\n", "    group by 1,2,3,4,5,6\n", "    )\n", "\n", "\n", "    , l2_ptype_base as (\n", "    Select \n", "        ptl2b.week_no,\n", "        ptl2b.week,\n", "        ptl2b.manufacturer_id,\n", "        ptl2b.frontend_merchant_id,\n", "        ptl2b.l2_category_id,\n", "        ptl2b.product_type_id,\n", "        carts as l2_ptype_carts,\n", "        coalesce(complaint_carts,0) l2_ptype_complaint_carts,\n", "        coalesce(resolved_carts,0) l2_ptype_resolved_carts\n", "    from  ptype_l2_sales_base ptl2b \n", "        left join ptype_l2_complaints ptl2cb\n", "            on ptl2b.l2_category_id=ptl2cb.l2_category_id\n", "            and ptl2b.product_type_id=ptl2cb.product_type_id\n", "            and ptl2b.week_no=ptl2cb.week_no\n", "            and ptl2b.frontend_merchant_id=ptl2cb.frontend_merchant_id\n", "             and ptl2b.manufacturer_id=ptl2cb.manufacturer_id\n", "            \n", "    )\n", "\n", "\n", "    ,ptype_l2_final_base as (\n", "    Select \n", "        week_no,\n", "        week,\n", "        manufacturer_id,\n", "        frontend_merchant_id,\n", "        L2_category_id,\n", "        product_type_id,\n", "\n", "        l2_ptype_carts,\n", "        lag(l2_ptype_carts,1) over (partition by L2_category_id, product_type_id,frontend_merchant_id,manufacturer_id order by week_no) as prev_week_l2_ptype_carts,\n", "\n", "        l2_ptype_complaint_carts,\n", "        lag(l2_ptype_complaint_carts,1) over (partition by L2_category_id, product_type_id,frontend_merchant_id,manufacturer_id order by week_no) as prev_week_l2_ptype_complaints,\n", "\n", "        l2_ptype_resolved_carts,\n", "        lag(l2_ptype_resolved_carts,1) over (partition by L2_category_id, product_type_id,frontend_merchant_id,manufacturer_id order by week_no) as prev_week_l2_ptype_resolved_carts\n", "\n", "    from l2_ptype_base )\n", "\n", "-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n", "    ,l2_sales_base as (\n", "    Select \n", "\n", "        ds.l2_category_id,\n", "       DATE_TRUNC('week',date_) as week,\n", "       extract(week from date_) as week_no,\n", "        manufacturer_id,\n", "        frontend_merchant_id,\n", "        sum(carts) carts\n", "    from sales_base ds \n", "    where ds.l2_category_id!='all' \n", "    and ds.brand_id='all'\n", "    and ds.product_type_id='all'\n", "    and product_id='all'\n", "    group by 1,2,3,4,5\n", "    )\n", "\n", "    ,l2_complaints_base as (\n", "    Select  \n", "        ds.frontend_merchant_id,\n", "        l2_category_id,\n", "        DATE_TRUNC('week',date_) as week,\n", "        extract(week from date_) as week_no,\n", "        manufacturer_id,\n", "        sum(complaint_carts) complaint_carts\n", "        ,sum(resolved_carts) resolved_carts\n", "    from complaints_base ds \n", "    where ds.l2_category_id!='all'\n", "    and ds.brand_id='all'\n", "    and ds.product_type_id='all'\n", "    and product_id='all'\n", "    group by 1,2,3,4,5\n", "    )\n", "\n", "    , l2_base as (\n", "    Select \n", "        l2sb.week_no,\n", "        l2sb.week,\n", "        l2sb.manufacturer_id,\n", "        l2sb.frontend_merchant_id,\n", "        l2sb.l2_category_id,\n", "        carts as l2_carts,\n", "        coalesce(complaint_carts,0) l2_complaint_carts,\n", "        coalesce(resolved_carts,0) l2_resolved_carts\n", "    from l2_sales_base l2sb\n", "        left join l2_complaints_base l2cb\n", "            on l2sb.L2_category_id=l2cb.L2_category_id\n", "            and l2sb.week_no=l2cb.week_no\n", "            and l2sb.frontend_merchant_id=l2cb.frontend_merchant_id\n", "            and  l2sb.manufacturer_id= l2cb.manufacturer_id\n", "    )\n", "\n", "\n", "\n", "\n", "    ,l2_final_base as (\n", "    Select \n", "        week_no,\n", "        week,\n", "        frontend_merchant_id,\n", "        manufacturer_id,\n", "        l2_category_id,\n", "\n", "        l2_carts,\n", "        lag(l2_carts,1) over (partition by l2_category_id,frontend_merchant_id,manufacturer_id order by week_no) as prev_week_l2_carts,\n", "\n", "        l2_complaint_carts,\n", "        lag(l2_complaint_carts,1) over (partition by l2_category_id,frontend_merchant_id,manufacturer_id order by week_no) as prev_week_l2_complaints,\n", "\n", "        l2_resolved_carts,\n", "        lag(l2_resolved_carts,1) over (partition by l2_category_id,frontend_merchant_id,manufacturer_id order by week_no) as prev_week_l2_resolved_carts\n", "  \n", "    from l2_base )\n", "\n", "    -- Select *\n", "    -- from l2_final_base \n", "    -- where l2_category_id='952'\n", "    -- and frontend_merchant_id=32398\n", "\n", "    --------------------------------------------------------------------------------------------------------------------\n", "\n", "    ,l1_sales_base as (\n", "    Select \n", "        l1_category_id,\n", "        DATE_TRUNC('week',date_) as week,\n", "        extract(week from date_) as week_no,\n", "         manufacturer_id,\n", "        frontend_merchant_id,\n", "         sum(carts) carts\n", "    from sales_base ds \n", "    where ds.l2_category_id='all' \n", "    and ds.brand_id='all'\n", "    and ds.product_type_id='all'\n", "    and product_id='all'\n", "    and l1_category_id!='all'\n", "    group by 1,2,3,4,5\n", "    )\n", "\n", "\n", "    ,l1_complaints_base as (\n", "    Select  \n", "       l1_category_id,\n", "       DATE_TRUNC('week',date_) as week,\n", "       extract(week from date_) as week_no,\n", "       frontend_merchant_id,\n", "        manufacturer_id\n", "        ,sum(complaint_carts) complaint_carts\n", "        ,sum(resolved_carts) resolved_carts\n", "    from complaints_base ds \n", "    where ds.l2_category_id='all' \n", "    and ds.brand_id='all'\n", "    and ds.product_type_id='all'\n", "    and product_id='all'\n", "    and l1_category_id!='all'\n", "    group by 1,2,3,4,5\n", "    )\n", "\n", "    , l1_base as (\n", "    Select \n", "        l1sb.week_no,\n", "        l1sb.week,\n", "        l1sb.frontend_merchant_id,\n", "        l1sb.manufacturer_id,\n", "        l1sb.l1_category_id,\n", "        carts as l1_carts,\n", "        coalesce(complaint_carts,0) l1_complaint_carts,\n", "        coalesce(resolved_carts,0) l1_resolved_carts\n", "    from l1_sales_base l1sb\n", "        left join l1_complaints_base l1cb\n", "            on l1sb.l1_category_id=l1cb.l1_category_id\n", "            and l1sb.week_no=l1cb.week_no\n", "            and l1sb.frontend_merchant_id=l1cb.frontend_merchant_id\n", "            and  l1sb.manufacturer_id= l1cb.manufacturer_id\n", "            \n", "    )\n", "\n", "\n", "\n", "\n", "    ,l1_final_base as (\n", "    Select \n", "        week_no,\n", "        week,\n", "        frontend_merchant_id,\n", "        l1_category_id,\n", "        manufacturer_id,\n", "  \n", "        l1_carts,\n", "        lag(l1_carts,1) over (partition by l1_category_id,frontend_merchant_id,manufacturer_id order by week_no) as prev_week_l1_carts,\n", "\n", "        l1_complaint_carts,\n", "        lag(l1_complaint_carts,1) over (partition by l1_category_id,frontend_merchant_id,manufacturer_id order by week_no) as prev_week_l1_complaints,\n", "\n", "        l1_resolved_carts,\n", "        lag(l1_resolved_carts,1) over (partition by l1_category_id,frontend_merchant_id,manufacturer_id order by week_no) as prev_week_l1_resolved_carts\n", "\n", "    from l1_base )\n", "\n", "\n", "    -------------------------------------------------------------------------------------------------------------------\n", "\n", "    ,l0_sales_base as (\n", "    Select \n", "        -- date_,\n", "        -- ds.frontend_merchant_id,\n", "         DATE_TRUNC('week',date_) as week,   \n", "         extract(week from date_) as week_no,\n", "         l0_category_id,\n", "         manufacturer_id,\n", "         frontend_merchant_id,\n", "        sum(carts) carts\n", "         \n", "    from sales_base ds \n", "    where ds.l1_category_id='all' \n", "    and ds.brand_id='all'\n", "    and ds.product_type_id='all'\n", "    and product_id='all'\n", "    and l0_category_id!='all'\n", "    group by 1,2,3,4,5\n", "    )\n", "\n", "\n", "    ,l0_complaints_base as (\n", "    Select  \n", "        -- date_,\n", "        -- ds.frontend_merchant_id,\n", "         DATE_TRUNC('week',date_) as week,\n", "         extract(week from date_) as week_no,\n", "         l0_category_id,\n", "         manufacturer_id\n", "         ,frontend_merchant_id\n", "        ,sum(complaint_carts) complaint_carts\n", "        ,sum(resolved_carts) resolved_carts\n", "    from complaints_base ds \n", "    where ds.l1_category_id='all' \n", "    and ds.brand_id='all'\n", "    and ds.product_type_id='all'\n", "    and product_id='all'\n", "    and l0_category_id!='all'\n", "    group by 1,2,3,4,5\n", "    )\n", "\n", "\n", "    , l0_base as (\n", "    Select \n", "        l0sb.week_no,\n", "        l0sb.week,\n", "        l0sb.manufacturer_id,\n", "        l0sb.frontend_merchant_id,\n", "        l0sb.l0_category_id,\n", "        carts as l0_carts,\n", "        coalesce(complaint_carts,0) l0_complaint_carts,\n", "        coalesce(resolved_carts,0) l0_resolved_carts\n", "        from l0_sales_base l0sb\n", "        left join l0_complaints_base l0cb\n", "            on l0sb.L0_category_id=l0cb.L0_category_id\n", "            and l0sb.week_no=l0cb.week_no\n", "            and l0sb.frontend_merchant_id=l0cb.frontend_merchant_id\n", "            and l0sb.manufacturer_id=l0cb.manufacturer_id\n", "            \n", "    )\n", "\n", "\n", "\n", "\n", "    ,l0_final_base as (\n", "    Select \n", "        week_no,\n", "        week,\n", "        frontend_merchant_id,\n", "        l0_category_id,\n", "       manufacturer_id,\n", "        l0_carts,\n", "        lag(l0_carts,1) over (partition by l0_category_id,frontend_merchant_id,manufacturer_id order by week_no) as prev_week_l0_carts,\n", "\n", "        l0_complaint_carts,\n", "        lag(l0_complaint_carts,1) over (partition by l0_category_id,frontend_merchant_id,manufacturer_id order by week_no) as prev_week_l0_complaints,\n", "\n", "        l0_resolved_carts,\n", "        lag(l0_resolved_carts,1) over (partition by l0_category_id,frontend_merchant_id,manufacturer_id order by week_no) as prev_week_l0_resolved_carts\n", "\n", "    from l0_base )\n", "\n", ",new_product_base as (select product_id, 1 as flag, min(order_create_dt_ist) as launch_date from dwh.fact_sales_order_item_details where order_create_dt_ist\n", ">= current_date - interval '180' day group by 1,2\n", "having DATE_DIFF('day', MIN(order_create_dt_ist), CURRENT_DATE) <= 14)\n", "\n", "   -- , final as (\n", "    Select \n", "    \n", "        p.week,\n", "        p.week_no,\n", "        p.product_id,\n", "        p.manufacturer_id,\n", "        p.frontend_merchant_id,\n", "        p.frontend_merchant_name,\n", "        p.city,\n", "        p.l0_category_id,\n", "        p.l1_category_id,\n", "        p.l2_category_id,\n", "        p.brand_id,\n", "        p.product_type_id,\n", "        p.current_week_carts,\n", "        p.current_week_complaint_carts,\n", "        p.current_week_resolved_carts,\n", "        p.prev_week_carts,\n", "        p.prev_week_complaint_carts,\n", "        p.prev_week_resolved_carts,\n", "        p.current_week_total_carts_city,\n", "        p.current_week_total_complaint_carts_city,\n", "        p.current_week_total_resolved_carts_city,\n", "        p.prev_week_total_carts_city,\n", "        p.prev_week_total_complaint_carts_city,\n", "        p.prev_week_total_resolved_carts_city,\n", "        p.carts,\n", "        p.prev_week_pid_carts,\n", "        p.pid_complaint_carts,\n", "        p.prev_week_pid_complaints,\n", "        p.pid_resolved_carts,\n", "        p.prev_week_pid_resolved_carts,\n", "   \n", "    \n", "    \n", "        brand_carts,\n", "        prev_week_brand_carts,\n", "        brand_complaint_carts,\n", "        prev_week_brand_complaints,\n", "        brand_resolved_carts,\n", "        prev_week_brand_resolved_carts,\n", "\n", "        ptype_carts,\n", "        prev_week_ptype_carts,\n", "        ptype_complaint_carts,\n", "        prev_week_ptype_complaints,\n", "        ptype_resolved_carts,\n", "        prev_week_ptype_resolved_carts,\n", "\n", "        ptype_brand_carts,\n", "        prev_week_ptype_brand_carts,\n", "        ptype_brand_complaint_carts,\n", "        prev_week_ptype_brand_complaints,\n", "        ptype_brand_resolved_carts,\n", "        prev_week_ptype_brand_resolved_carts,\n", "\n", "\n", "        l2_brand_carts,\n", "        prev_week_l2_brand_carts,\n", "        l2_brand_complaint_carts,\n", "        prev_week_l2_brand_complaints,\n", "        l2_brand_resolved_carts,\n", "        prev_week_l2_brand_resolved_carts,\n", "\n", "        l2_ptype_carts,\n", "        prev_week_l2_ptype_carts,\n", "        l2_ptype_complaint_carts,\n", "        prev_week_l2_ptype_complaints,\n", "        l2_ptype_resolved_carts,\n", "        prev_week_l2_ptype_resolved_carts,\n", "\n", "        l0_carts,\n", "        prev_week_l0_carts,\n", "        l0_complaint_carts,\n", "        prev_week_l0_complaints,\n", "        l0_resolved_carts,\n", "        prev_week_l0_resolved_carts,\n", "\n", "\n", "        l1_carts,\n", "        prev_week_l1_carts,\n", "        l1_complaint_carts,\n", "        prev_week_l1_complaints,\n", "        l1_resolved_carts,\n", "        prev_week_l1_resolved_carts,\n", "\n", "\n", "        l2_carts,\n", "        prev_week_l2_carts,\n", "        l2_complaint_carts,\n", "        prev_week_l2_complaints,\n", "        l2_resolved_carts,\n", "        prev_week_l2_resolved_carts,\n", "        \n", "        coalesce(npb.flag,0) as new_product_flag,\n", "        cast(ptype_carts*1.0000000/ count(*) over(partition by p.frontend_merchant_id, p.week_no, pt.product_type_id, p.manufacturer_id) as real) as weighted_ptype_carts,\n", "        cast(prev_week_ptype_carts*1.0000000/ count(*) over(partition by p.frontend_merchant_id, p.week_no,pt.product_type_id, p.manufacturer_id) as real) as weighted_prev_week_ptype_carts,\n", "        cast(ptype_complaint_carts*1.0000000/ count(*) over(partition by p.frontend_merchant_id, p.week_no,pt.product_type_id, p.manufacturer_id) as real) as weighted_ptype_complaints,\n", "        cast(prev_week_ptype_complaints*1.0000000/ count(*) over(partition by p.frontend_merchant_id, p.week_no,pt.product_type_id, p.manufacturer_id) as real) as weighted_prev_week_ptype_complaints,\n", "        cast(ptype_resolved_carts*1.0000000/ count(*) over(partition by p.frontend_merchant_id, p.week_no,pt.product_type_id, p.manufacturer_id) as real) as weighted_ptype_resolved,\n", "        \n", "        cast(brand_carts*1.0000000/ count(*) over(partition by p.frontend_merchant_id, p.week_no,b.brand_id, p.manufacturer_id) as real) as weighted_brand_carts,\n", "        cast(prev_week_brand_carts*1.0000000/ count(*) over(partition by p.frontend_merchant_id, p.week_no,b.brand_id, p.manufacturer_id) as real) as weighted_prev_week_brand_carts,\n", "        cast(brand_complaint_carts*1.0000000/ count(*) over(partition by p.frontend_merchant_id, p.week_no,b.brand_id, p.manufacturer_id) as real) as weighted_brand_complaints,\n", "        cast(prev_week_brand_complaints*1.0000000/ count(*) over(partition by p.frontend_merchant_id, p.week_no,b.brand_id, p.manufacturer_id) as real) as weighted_prev_week_brand_complaints,\n", "        cast(brand_resolved_carts*1.0000000/ count(*) over(partition by p.frontend_merchant_id, p.week_no,b.brand_id, p.manufacturer_id) as real) as weighted_brand_resolved\n", "        \n", "    from pid_final_base p \n", "        join brand_final_base b \n", "            on p.week_no=b.week_no\n", "            and p.brand_id=b.brand_id\n", "            and p.frontend_merchant_id=b.frontend_merchant_id\n", "            and p.manufacturer_id=b.manufacturer_id\n", "            \n", "        join ptype_final_base pt\n", "            on p.week_no=pt.week_no\n", "            and p.product_type_id=pt.product_type_id\n", "            and p.frontend_merchant_id=pt.frontend_merchant_id\n", "            and p.manufacturer_id=pt.manufacturer_id\n", "            \n", "        join ptype_brand_final_base ptb\n", "            on p.week_no=ptb.week_no\n", "            and p.product_type_id=ptb.product_type_id\n", "            and p.brand_id=ptb.brand_id\n", "            and p.frontend_merchant_id=ptb.frontend_merchant_id\n", "            and p.manufacturer_id=ptb.manufacturer_id\n", "            \n", "        join l2_brand_final_base ltb\n", "            on p.week_no=ltb.week_no\n", "            and p.l2_category_id=ltb.l2_category_id\n", "            and p.frontend_merchant_id=ltb.frontend_merchant_id\n", "            and p.brand_id=ltb.brand_id\n", "            and p.manufacturer_id=ltb.manufacturer_id\n", "            \n", "        join ptype_l2_final_base pltb\n", "            on p.week_no=pltb.week_no\n", "            and p.l2_category_id=pltb.l2_category_id\n", "            and p.product_type_id=pltb.product_type_id \n", "            and p.frontend_merchant_id=pltb.frontend_merchant_id\n", "            and p.manufacturer_id=pltb.manufacturer_id\n", "            \n", "            \n", "        join l0_final_base l0\n", "            on p.week_no=l0.week_no\n", "            and p.l0_category_id=l0.l0_category_id\n", "            and p.frontend_merchant_id=l0.frontend_merchant_id\n", "             and p.manufacturer_id=l0.manufacturer_id\n", "            \n", "        join l2_final_base l2\n", "            on p.week_no=l2.week_no\n", "            and p.l2_category_id=l2.l2_category_id\n", "            and p.frontend_merchant_id=l2.frontend_merchant_id\n", "             and p.manufacturer_id=l2.manufacturer_id\n", "            \n", "        join l1_final_base l1\n", "            on p.week_no=l1.week_no\n", "            and p.l1_category_id=l1.l1_category_id\n", "            and p.frontend_merchant_id=l1.frontend_merchant_id\n", "            and p.manufacturer_id=l1.manufacturer_id\n", "        left join new_product_base npb on p.product_id=cast(npb.product_id as varchar)\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\"\"\"\n", "\n", "pb.to_trino(data_obj=query, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "559d693f-6662-4915-ad41-6b449c2daa0e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3bb17e7b-78a1-4f63-bd52-b532b7c7a3eb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e9780097-a3e6-4950-bd24-13c31f0edf16", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "78218865-3c49-4c03-8ab6-a70be37091cf", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "60c36068-c3fd-409f-a488-23e9914b6bb8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2bebf092-8f0d-44d0-9066-9e3452c51ff1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}