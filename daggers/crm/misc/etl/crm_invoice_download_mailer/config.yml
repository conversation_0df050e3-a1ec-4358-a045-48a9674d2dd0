alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
  - channel: bl-cd-invoice-dag-update
dag_name: crm_invoice_download_mailer
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: crm
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03S5ACSW3C
path: crm/misc/etl/crm_invoice_download_mailer
paused: false
pool: crm_pool
project_name: misc
schedule:
  end_date: '2025-07-28T00:00:00'
  interval: 0 0 1 1 *
  start_date: '2024-02-15T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 11
