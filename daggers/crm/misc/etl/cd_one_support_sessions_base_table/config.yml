alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 3
dag_name: cd_one_support_sessions_base_table
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: crm
notebooks:
- alias: one_support_sessions_table
  executor_config:
    load_type: low
    node_type: spot
  name: one_support_sessions_table
  parameters: null
  retries: 2
  tag: group_1
owner:
  email: <EMAIL>
  slack_id: S046Q8JF09M
path: crm/misc/etl/cd_one_support_sessions_base_table
paused: false
pool: crm_pool
project_name: misc
schedule:
  end_date: '2025-07-28T00:00:00'
  interval: 30 02 * * *
  start_date: '2023-09-18T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 2
