alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: esto
dag_type: etl
escalation_priority: low
executor:
  config:
    service_account_name: blinkit-prod-airflow-primary-eks-role
    cpu:
      limit: 1
      request: 0.5
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: airflow-dags-spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: povms
notebook:
  parameters:
owner:
  email: <EMAIL>
  slack_id: U03S2BZCBEZ
path: povms/esto/etl/esto
paused: true
project_name: esto
schedule:
  end_date: '2023-06-06T00:00:00'
  interval: 0 0 * * *
  start_date: '2022-08-05T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
pool: povms_pool
