{"cells": [{"cell_type": "code", "execution_count": null, "id": "9de84339-674c-4b2b-bd5d-b25770929c2d", "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "from collections import defaultdict\n", "from datetime import date, datetime, timedelta\n", "import pytz\n", "import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import sqlalchemy as sqla\n", "\n", "pd.set_option(\"display.max_columns\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "38f478a5-54c1-45ae-a851-28bfc5f39aed", "metadata": {}, "outputs": [], "source": ["retail_db = pb.get_connection(\"retail\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "trino = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "c7b83544-dea4-4431-98d3-8c89a8f48e81", "metadata": {}, "outputs": [], "source": ["tz = pytz.timezone(\"Asia/Calcutta\")\n", "end = datetime.now(tz).date() - <PERSON><PERSON><PERSON>(days=0)\n", "start = datetime.now(tz).date() - <PERSON><PERSON><PERSON>(days=90)\n", "print(start, end)"]}, {"cell_type": "code", "execution_count": null, "id": "9ccca626-ad61-45f9-b610-3ec564f06be3", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"with sto as (select\n", "    distinct\n", "    sto.created_at + interval '5' HOUR + interval '30' MINUTE as sto_created_at,\n", "    sto.sto_id,\n", "    items.run_id as ars_run_id,\n", "    sto.outlet_id as sender_outlet_id,\n", "    sto.merchant_outlet_id as receiving_outlet_id,\n", "    manual_run\n", "from ims.ims_sto_details sto\n", "inner join po.sto_items as items on sto.sto_id=items.sto_id\n", "left join (select run_id as ars_run_id, \n", "                replace (JSON_EXTRACT_SCALAR(simulation_params,'$.run'), '\"', '') as ars_run_flag, \n", "                JSON_EXTRACT_SCALAR(simulation_params, '$.manual') as manual_run, \n", "                coalesce(replace(JSON_EXTRACT_SCALAR(simulation_params, '$.mode'),'\"',''),'normal') as ars_mode\n", "from lake_ars.job_run) as ars_mode on items.run_id=ars_mode.ars_run_id\n", "where sto.created_at between (cast('{start}' as timestamp) - interval '5' HOUR - interval '30' MINUTE)\n", "          AND (cast('{end}' as timestamp) + interval '19' HOUR - interval '30' MINUTE)\n", "          and cast(ars_run_id as varchar) like '202%%'\n", "          and manual_run is NULL\n", "          )\n", "          \n", ",invoice as\n", "(\n", "    Select \n", "        distinct\n", "            cast(p.grofers_order_id as int) as sto_id,\n", "            p.invoice_id,\n", "            p.pos_timestamp + interval '5' HOUR +interval '30' minute as sto_invoice_created_at,\n", "            grn_started_at\n", "        from (   \n", "                select * from pos.pos_invoice where insert_ds_ist between '{start}' AND '{end}'\n", "                AND invoice_type_id IN (5,14,16) AND grofers_order_id != '' AND grofers_order_id IS NOT NULL\n", "        ) p\n", "    INNER JOIN (SELECT \n", "               x.vendor_invoice_id as invoice_id,\n", "               max(x.created_at + interval '5' hour + interval '30' minute) as grn_started_at\n", "    FROM (select inward.grn_id, \n", "                 vendor_invoice_id, \n", "                 max(sd.created_at) as created_at\n", "                 from\n", "        (select * from ims.ims_inward_invoice where insert_ds_ist between '{start}' and '{end}' and source_type = 2) inward\n", "            INNER JOIN (select * from ims.ims_inventory_stock_details where insert_ds_ist between '{start}' and '{end}') sd on inward.grn_id = sd.grn_id\n", "            group by 1,2) x\n", "        group by 1) x on p.invoice_id=x.invoice_id\n", ")\n", "\n", ",esto_details as\n", "(select \n", "    distinct\n", "    a.sto_created_at,\n", "    a.sto_id,\n", "    a.ars_run_id,\n", "    a.sender_outlet_id,\n", "    a.receiving_outlet_id,\n", "    b.invoice_id,\n", "    b.sto_invoice_created_at as invoice_created_at,\n", "    b.grn_started_at\n", "from sto as a\n", "inner join invoice as b on a.sto_id=b.sto_id)\n", "\n", "select * from esto_details \"\"\"\n", "\n", "\n", "result = pd.read_sql_query(sql=query, con=trino)"]}, {"cell_type": "code", "execution_count": null, "id": "41e62ec3-26ef-4ee1-b2f2-cf5bd9a07611", "metadata": {}, "outputs": [], "source": ["result[\"sto_created_at\"] = pd.to_datetime(result[\"sto_created_at\"])\n", "result[\"grn_started_at\"] = pd.to_datetime(result[\"grn_started_at\"])\n", "result[\"invoice_created_at\"] = pd.to_datetime(result[\"invoice_created_at\"])"]}, {"cell_type": "code", "execution_count": null, "id": "3742a448-cd3b-4e67-9886-5184b738e76d", "metadata": {}, "outputs": [], "source": ["result.dtypes"]}, {"cell_type": "markdown", "id": "aacd50e5-e34a-4410-b492-a0a6e4cf35fe", "metadata": {}, "source": ["## pushing in tables"]}, {"cell_type": "code", "execution_count": null, "id": "ab0235b6-ab43-4696-8053-df0adcc359c7", "metadata": {}, "outputs": [], "source": ["esto_ = [\n", "    {\"name\": \"sto_created_at\", \"type\": \"datetime\", \"description\": \"sto creation time\"},\n", "    {\"name\": \"sto_id\", \"type\": \"int\", \"description\": \"sto_id\"},\n", "    {\"name\": \"ars_run_id\", \"type\": \"varchar\", \"description\": \"ars_run\"},\n", "    {\"name\": \"sender_outlet_id\", \"type\": \"int\", \"description\": \"sneder_outlet\"},\n", "    {\n", "        \"name\": \"receiving_outlet_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"receiving outlet id\",\n", "    },\n", "    {\"name\": \"invoice_id\", \"type\": \"varchar\", \"description\": \"invoice id\"},\n", "    {\n", "        \"name\": \"invoice_created_at\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"invoice creation date\",\n", "    },\n", "    {\"name\": \"grn_started_at\", \"type\": \"datetime\", \"description\": \"grn start date\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "1ba8d8d8-772c-4ff3-b1d5-6df4335bdb23", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"esto_\",\n", "    \"column_dtypes\": esto_,\n", "    \"primary_key\": [\"sto_id\", \"invoice_id\"],\n", "    \"sortkey\": \"sto_created_at\",\n", "    \"incremental_key\": \"sto_created_at\",\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"stocking level results\",  # Description of the table being sent to redshift\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "58f603e1-8ca7-493f-9311-0e1b377b4d37", "metadata": {}, "outputs": [], "source": ["pb.to_redshift(result, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "25364d24-493e-4f13-9448-cc90dd3cadaf", "metadata": {}, "outputs": [], "source": ["result"]}, {"cell_type": "code", "execution_count": null, "id": "2f199444-9a98-48a3-86b3-1c2f7c7d016c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b9136913-7cf1-4e9e-9a30-04ffb66a8153", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}