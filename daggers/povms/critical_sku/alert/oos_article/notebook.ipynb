{"cells": [{"cell_type": "code", "execution_count": null, "id": "cc2a3835-1bc6-43cf-b28c-fafe81e4038f", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib==3.8\n", "import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "import matplotlib.pyplot as plt\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e717d3fd-582e-49e1-9235-1d7852b43887", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "57bdc0aa-5ff7-4153-a57b-6c5af2c836c8", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "d41b0124-d898-4660-9eee-ea465f506abb", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "fc7b7270-d8a6-4418-907c-56e22b28913a", "metadata": {}, "outputs": [], "source": ["sheet_id = \"13Vg9u1Pk1V3LSuRS-LivcmfE4xeJI7j_wEC-IqLMJRY\"\n", "sheet_name = \"Item_Upload\"\n", "item_upload_df = pb.from_sheets(sheet_id, sheet_name)\n", "item_list = item_upload_df[\"item_id\"].unique()\n", "# item_list = [10000429, 10000430]\n", "item_list = \",\".join(map(str, item_list))"]}, {"cell_type": "code", "execution_count": null, "id": "6b0b952e-ceff-4cba-909d-e0dd4e694e0d", "metadata": {}, "outputs": [], "source": ["oos_item_df = read_sql_query(\n", "    f\"\"\"\n", "\n", "WITH date_outlet_level AS (\n", "SELECT      a.date_ist, b.city_name, b.facility_name, a.outlet_id, a.item_id, id.item_name, SUM(a.current_inventory) AS current_inventory \n", "FROM        supply_etls.hourly_inventory_snapshots a\n", "INNER JOIN  supply_etls.outlet_details b\n", "            ON  a.facility_id = b.facility_id AND b.ars_check = 1 and b.business_type_id = 7\n", "INNER JOIN  supply_etls.item_details id\n", "            ON id.item_id = a.item_id\n", "WHERE       a.date_ist >= CURRENT_DATE - INTERVAL '2' DAY and a.item_id IN ({item_list})\n", "GROUP BY    1,2,3,4,5,6 ),\n", "\n", "oos_items_last_n_days AS (\n", "SELECT      item_id, item_name, city_name, facility_name, outlet_id, COUNT(DISTINCT date_ist) AS last_n_days\n", "FROM        date_outlet_level\n", "WHERE       current_inventory = 0 \n", "GROUP BY    1,2,3,4,5 ),\n", "\n", "open_sto_info AS (\n", "SELECT       si.item_id, s.frontend_outlet_id\n", "            , (SUM(ii.reserved_quantity) - SUM(ii.inward_quantity) - SUM(ii.released_reserved_quantity) \n", "                - SUM(ii.released_billed_quantity)) AS open_sto_qty_l2d\n", "\n", "FROM        po.sto s \n", "INNER JOIN  po.sto_items si\n", "            ON  s.active = 1 AND s.lake_active_record = true AND si.active = 1 AND si.lake_active_record = true AND s.id = si.sto_id \n", "                -- AND s.sto_state_id IN (2,3)\n", "INNER JOIN  ims.ims_sto_item ii\n", "            ON ii.lake_active_record = true AND ii.sto_id = si.sto_id AND ii.item_id = si.item_id\n", "INNER JOIN   ims.ims_sto_details ims_sto\n", "            ON ims_sto.lake_active_record = true AND ims_sto.sto_id = ii.sto_id \n", "WHERE       ims_sto.sto_state IN (1,2,5) AND s.created_at >= (CAST(CURRENT_DATE AS TIMESTAMP) - INTERVAL '2' DAY - INTERVAL '330' MINUTE)\n", "            AND si.created_at >= (CAST(CURRENT_DATE AS TIMESTAMP) - INTERVAL '2' DAY - INTERVAL '330' MINUTE)\n", "            AND ii.created_at >= (CAST(CURRENT_DATE AS TIMESTAMP) - INTERVAL '2' DAY - INTERVAL '330' MINUTE)\n", "            AND si.item_id IN ({item_list})\n", "GROUP BY    1,2 ),\n", "\n", "carts_cnt AS (\n", "SELECT  outlet_id, COUNT(DISTINCT cart_id) AS num_of_carts\n", "FROM    dwh.fact_sales_order_details\n", "WHERE   order_create_dt_ist >= current_date - interval '3' day AND order_current_status = 'DELIVERED' and is_internal_order = false \n", "        and cart_checkout_ts_ist >= current_date - interval '3' day\n", "GROUP BY 1 )\n", "\n", "SELECT      city_name, facility_name, item_name, COALESCE(open_sto_qty_l2d,0) AS open_sto_qty\n", "FROM        oos_items_last_n_days i\n", "LEFT JOIN   open_sto_info j\n", "            ON i.outlet_id = j.frontend_outlet_id \n", "LEFT JOIN   carts_cnt k\n", "            ON i.outlet_id = k.outlet_id \n", "WHERE       i.last_n_days = 3 AND k.num_of_carts >= 200\n", "\n", "\"\"\",\n", "    CON_TRINO,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e7586dd7-cb42-4c6a-a617-aa616e5861d2", "metadata": {}, "outputs": [], "source": ["# oos_item_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "5c94dd7f-898a-4ac5-a33c-4483cb98bc3c", "metadata": {}, "outputs": [], "source": ["city_df = (\n", "    oos_item_df.groupby([\"item_name\"])[\"city_name\"].unique().reset_index(name=\"city_belongingness\")\n", ")\n", "city_df[\"city_belongingness\"] = city_df[\"city_belongingness\"].apply(lambda x: \", \".join(sorted(x)))"]}, {"cell_type": "code", "execution_count": null, "id": "4be2e3e6-0d67-441e-b607-5cd01a3f0f39", "metadata": {}, "outputs": [], "source": ["stores_zero_inv = oos_item_df.groupby([\"item_name\"]).agg(\n", "    num_stores_zero_inv=(\"facility_name\", \"nunique\"), open_sto_qty=(\"open_sto_qty\", \"sum\")\n", ")\n", "stores_zero_open_sto = (\n", "    oos_item_df.loc[oos_item_df[\"open_sto_qty\"] == 0]\n", "    .groupby([\"item_name\"])[\"facility_name\"]\n", "    .nunique()\n", "    .reset_index(name=\"# stores_zero_open_sto\")\n", ")\n", "summary_df = pd.merge(stores_zero_inv, stores_zero_open_sto, how=\"left\", on=\"item_name\")\n", "summary_df = pd.merge(summary_df, city_df, how=\"left\", on=\"item_name\")\n", "summary_df = summary_df.rename(\n", "    columns={\n", "        \"item_name\": \"Item Name\",\n", "        \"city_belongingness\": \"City Belongness\",\n", "        \"num_stores_zero_inv\": \"# stores_zero_inv\",\n", "        \"open_sto_qty\": \"∑ open_sto_qty\",\n", "    }\n", ")\n", "summary_df[\"∑ open_sto_qty\"] = summary_df[\"∑ open_sto_qty\"].fillna(0)\n", "summary_df = summary_df[\n", "    [\n", "        \"Item Name\",\n", "        \"City Belongness\",\n", "        \"# stores_zero_inv\",\n", "        \"# stores_zero_open_sto\",\n", "        \"∑ open_sto_qty\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "6f96fabd-5f7a-4aa3-a98b-d37f4c11b9a3", "metadata": {}, "outputs": [], "source": ["# summary_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9fbfee43-29b3-4f36-b193-ec193a05037a", "metadata": {}, "outputs": [], "source": ["sheet_id = \"13Vg9u1Pk1V3LSuRS-LivcmfE4xeJI7j_wEC-IqLMJRY\"\n", "sheet_name = \"Store_List\"\n", "pb.to_sheets(oos_item_df, sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "d890baab-671d-4240-8287-13259a7e665f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "295ffaf9-b3b8-491d-bf66-2e93acc860e3", "metadata": {}, "outputs": [], "source": ["channel = \"solving-amul-availability\"  # 'test_channel'\n", "# channel = \"test_channel\"  # 'test_channel'\n", "\n", "if summary_df[\"Item Name\"].count() == 0:\n", "    text = \"Hi Team, no stores were OOS for the listed Amul items in the past two days\"\n", "    # text = \"Hi Team, No stores are oos for item_id - 10000429 today\"\n", "    pb.send_slack_message(channel=channel, text=text)\n", "    print(\"No record found. Hence no slack alert required\")\n", "\n", "else:\n", "\n", "    def render_mpl_table(\n", "        data,\n", "        col_width=8,  # 3.0,\n", "        row_height=2,  # 0.625,\n", "        font_size=20,\n", "        header_color=\"#E96125\",\n", "        row_colors=[\"#f1f1f2\", \"w\"],\n", "        edge_color=\"black\",\n", "        bbox=[0, 0, 1, 1],\n", "        header_columns=0,\n", "        ax=None,\n", "        **kwargs\n", "    ):\n", "        if ax is None:\n", "            size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array(\n", "                [col_width, row_height]\n", "            )\n", "            fig, ax = plt.subplots(figsize=size)\n", "            ax.axis(\"off\")\n", "        mpl_table = ax.table(cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs)\n", "        mpl_table.auto_set_font_size(False)\n", "        mpl_table.set_fontsize(font_size)\n", "\n", "        for k, cell in mpl_table._cells.items():\n", "            cell.set_edgecolor(edge_color)\n", "            if k[0] == 0 or k[1] < header_columns:\n", "                cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "                cell.set_facecolor(header_color)\n", "            #         elif k[0] == data.shape[0]:\n", "            #             cell.set_text_props(weight=\"bold\", ma=\"center\")\n", "            # cell.set_facecolor('#f1f1f2')\n", "            else:\n", "                cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "        mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "        return ax.get_figure(), ax\n", "\n", "    fig, ax = render_mpl_table(summary_df, header_columns=0)\n", "    fig.savefig(\"oos_item.png\", bbox_inches=\"tight\")\n", "    # oos_item_df.to_csv(\"oos_stores.csv\", index=False)\n", "\n", "    text = \"Hi Team, attached is the list of stores where Amul items were OOS in the past 2 days, along with their open_sto_qty (if any) : https://docs.google.com/spreadsheets/d/13Vg9u1Pk1V3LSuRS-LivcmfE4xeJI7j_wEC-IqLMJRY\"\n", "\n", "    # text = \"Hi Team, below are the list of cities where Amul_Salted_Butter_100gm article has been oos for last more than 2 days and respective open_sto_qty if any. Sheet Reference for Store List : https://docs.google.com/spreadsheets/d/13Vg9u1Pk1V3LSuRS-LivcmfE4xeJI7j_wEC-IqLMJRY\"\n", "\n", "    pb.send_slack_message(channel=channel, text=text, files=[\"./oos_item.png\"])\n", "\n", "    print(\n", "        \"slack alert has been sent to respective slack channel to inforn the same to the team for <PERSON>ul oos article\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "92119b5a-5116-4bc0-bf78-ea3f72f9c945", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}