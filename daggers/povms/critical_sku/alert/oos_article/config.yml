alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: oos_article
dag_type: alert
escalation_priority: low
execution_timeout: 120
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U06LLMJHFCK
path: povms/critical_sku/alert/oos_article
paused: false
pool: povms_pool
project_name: critical_sku
schedule:
  end_date: '2025-07-15T00:00:00'
  interval: 30 6 * * *
  start_date: '2025-06-12T00:00:00'
schedule_type: fixed
sla: 125 minutes
support_files: []
tags: []
template_name: notebook
version: 5
