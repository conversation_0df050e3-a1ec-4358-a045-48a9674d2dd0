alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: bau_performance
dag_type: report
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RURE15HB
path: povms/entity_performance_tracking/report/bau_performance
paused: true
pool: povms_pool
project_name: entity_performance_tracking
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 30 3 * * *
  start_date: '2023-01-11T16:10:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 7
