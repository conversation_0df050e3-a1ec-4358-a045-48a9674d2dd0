{"cells": [{"cell_type": "code", "execution_count": null, "id": "ccf9d7e3-bb55-40f1-9cee-ec7d7fa66f94", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "from datetime import date, datetime, timedelta\n", "\n", "import gc\n", "\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "CON_IMS = pb.get_connection(\"[Replica] RDS IMS\")\n", "CON_PO = pb.get_connection(\"[Replica] RDS PO\")\n", "CON_PRESTO = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "id": "175ae6a3-4eb1-4002-a600-f8326811a09c", "metadata": {}, "outputs": [], "source": ["# import dask.dataframe as dd\n", "# add_cycle_con = dd.from_pandas(add_cycle_con, npartitions=10)"]}, {"cell_type": "code", "execution_count": null, "id": "94fe68cd-e29d-4c3c-aa8a-42f35d3a49b9", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    start = time.time()\n", "    df = pd.read_sql_query(sql, con)\n", "    end = time.time()\n", "    if (end - start) > 60:\n", "        print(\"Time: \", (end - start) / 60, \"min\")\n", "    else:\n", "        print(\"Time: \", end - start, \"s\")\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "5b451fea-a717-407e-9af2-42bc08c7e449", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)\n", "pd.set_option(\"display.max_rows\", None)\n", "pd.options.mode.chained_assignment = None  # default='warn'"]}, {"cell_type": "code", "execution_count": null, "id": "48fd2f00-c197-4dd3-830a-53a800a0ce73", "metadata": {}, "outputs": [], "source": ["# Get All Entity"]}, {"cell_type": "code", "execution_count": null, "id": "5d5f25bb-5c50-4801-b1b9-4233798d76b5", "metadata": {}, "outputs": [], "source": ["entity_vendor_base_query = f\"\"\"\n", "select \n", "    distinct entity_vendor_type_id, entity_vendor_id, vendor_name, base.outlet_id, co.name as outlet_name, co.facility_id\n", "from \n", "    lake_view_retail.outlet_entity_vendor_mapping base\n", "left join \n", "    lake_view_vms.vms_vendor vms_vendor on base.entity_vendor_id = vms_vendor.id and vms_vendor.active = 1 and vms_vendor.lake_active_record = true\n", "left join \n", "    lake_retail.console_outlet co on co.active = 1 and base.outlet_id = co.id and co.lake_active_record = true\n", "where \n", "    entity_vendor_type_id = 5 -- Filter for Active Entities,  --- outlet entity mapping entity_vendor_type_id != 5 -> old & 5 -> new;\n", "and \n", "    base.active = 1\n", "and \n", "    base.lake_active_record = true\"\"\"\n", "\n", "entity_vendor_base_df = read_sql_query(entity_vendor_base_query, CON_PRESTO)\n", "\n", "entity_vendor_base_df[\"entity_type\"] = np.where(\n", "    entity_vendor_base_df[\"vendor_name\"].str.contains(\"TAMS\"),\n", "    \"TAMS\",\n", "    np.where(\n", "        entity_vendor_base_df[\"vendor_name\"].str.contains(\"Kemexel\"),\n", "        \"Kemexel\",\n", "        np.where(\n", "            entity_vendor_base_df[\"vendor_name\"].str.contains(\"Storeasy\"),\n", "            \"Storeasy\",\n", "            np.where(\n", "                entity_vendor_base_df[\"vendor_name\"].str.contains(\"Superwell\"),\n", "                \"Superwell\",\n", "                \"Other\",\n", "            ),\n", "        ),\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8e6d24e2-2beb-4954-9966-ef1070b8149e", "metadata": {}, "outputs": [], "source": ["# entity_vendor_base_df = dd.from_pandas(entity_vendor_base_df, npartitions=10)"]}, {"cell_type": "code", "execution_count": null, "id": "dc2d77e4-b74b-40a5-843f-508d5dea835f", "metadata": {}, "outputs": [], "source": ["outlet_mapping_query = f\"\"\"\n", "select \n", "    distinct id as outlet_id, name as outlet_name, facility_id\n", "from \n", "    lake_retail.console_outlet\n", "where \n", "    active = 1\n", "\"\"\"\n", "outlet_mapping_df = read_sql_query(outlet_mapping_query, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "f2eeceea-d8aa-4228-b969-b6d302db504e", "metadata": {}, "outputs": [], "source": ["entity_vendor_base_df[entity_vendor_base_df.entity_vendor_id == 16384]"]}, {"cell_type": "markdown", "id": "70bb4d9d-35ac-4028-ad0e-89bdf82a1362", "metadata": {"tags": []}, "source": ["# Tag Performance\n", "#### 1. PO\n", "#### 2. In<PERSON>ory\n", "#### 3. B2C Sales"]}, {"cell_type": "markdown", "id": "235a40b9-8c4a-4856-ad23-438d98047d81", "metadata": {}, "source": ["# En<PERSON><PERSON>endor - All POs"]}, {"cell_type": "code", "execution_count": null, "id": "9da5a21c-1343-415a-8a4b-4c4cdc83ac86", "metadata": {}, "outputs": [], "source": ["entity_po_current_query = f\"\"\"\n", "select distinct\n", "    p.destination_entity_vendor_id as entity_vendor_id,\n", "    is_multiple_grn,\n", "    posa.po_state_id,\n", "    p.id as po_id,\n", "    p.po_number,\n", "    p.outlet_id,\n", "    o.facility_id,\n", "    issue_date + interval '330' Minute as issue_date,\n", "    date(expiry_date + interval '330' Minute) as expiry_date,\n", "    date(ps.schedule_date_time + interval '330' Minute) as scheduled_date,\n", "    poi.item_id,\n", "    poi.units_ordered as expected_po_qty,\n", "    poi.total_amount as po_amount,\n", "    posta.name as po_state,\n", "    grn.created_at + interval '330' Minute as grn_ts,\n", "    grn.quantity as grn_qty,\n", "    \n", "    coalesce(grn.landing_price,0) * grn.quantity as grn_value\n", "\n", "from lake_view_po.purchase_order p\n", "    left join lake_view_po.po_schedule ps on p.id=ps.po_id_id\n", "    inner join lake_view_po.purchase_order_items poi on p.id=poi.po_id\n", "    inner join lake_view_retail.console_outlet o on o.id=p.outlet_id\n", "    inner join lake_view_po.purchase_order_status posa on posa.po_id = p.id\n", "    inner join lake_view_po.purchase_order_state posta on posta.id = posa.po_state_id\n", "    left join lake_view_po.po_grn grn on grn.item_id = poi.item_id and grn.po_id = p.id\n", "where p.destination_entity_vendor_id in (select distinct entity_vendor_id from lake_retail.outlet_entity_vendor_mapping where entity_vendor_type_id = 5 and active = 1 and lake_active_record = true)\n", "--  and posta.name in ('Rescheduled','Expired','Fulfilled','Scheduled','Created')\n", "and issue_date between cast(current_date as timestamp) - interval '1' Day and cast(current_date as timestamp) + interval '1' Day\n", "order by issue_date desc\"\"\"\n", "entity_po_current_df = read_sql_query(entity_po_current_query, CON_PRESTO)\n", "\n", "\n", "entity_po_past_query = f\"\"\"\n", "select distinct\n", "    p.destination_entity_vendor_id as entity_vendor_id,\n", "    is_multiple_grn,\n", "    posa.po_state_id,\n", "    p.id as po_id,\n", "    p.po_number,\n", "    p.outlet_id,\n", "    o.facility_id,\n", "    issue_date + interval '330 Minutes' as issue_date,\n", "    date(expiry_date + interval '330 Minutes') as expiry_date,\n", "    date(ps.schedule_date_time + interval '330 Minutes') as scheduled_date,\n", "    poi.item_id,\n", "    poi.units_ordered as expected_po_qty,\n", "    poi.total_amount as po_amount,\n", "    posta.name as po_state,\n", "    grn.created_at + interval '330 Minutes' as grn_ts,\n", "    grn.quantity as grn_qty,\n", "    \n", "    coalesce(grn.landing_price,0) * grn.quantity as grn_value\n", "\n", "from lake_po.purchase_order p\n", "    left join lake_po.po_schedule ps on p.id=ps.po_id_id\n", "    inner join lake_po.purchase_order_items poi on p.id=poi.po_id\n", "    inner join lake_retail.console_outlet o on o.id=p.outlet_id\n", "    inner join lake_po.purchase_order_status posa on posa.po_id = p.id\n", "    inner join lake_po.purchase_order_state posta on posta.id = posa.po_state_id\n", "    left join lake_po.po_grn grn on grn.item_id = poi.item_id and grn.po_id = p.id\n", "where p.destination_entity_vendor_id in (select distinct entity_vendor_id from lake_retail.outlet_entity_vendor_mapping where entity_vendor_type_id = 5 and active = 1)\n", "-- and posta.name in ('Rescheduled','Expired','Fulfilled','Scheduled','Created')\n", "and issue_date between cast('2021-12-01' as timestamp) - interval '330 Minutes' and cast(current_date as timestamp) - interval '1 Days'\n", "order by issue_date desc\"\"\"\n", "entity_po_past_df = read_sql_query(entity_po_past_query, CON_REDSHIFT)\n", "\n", "\n", "entity_po_concat_df = pd.concat([entity_po_current_df, entity_po_past_df]).drop_duplicates()\n", "\n", "entity_po_df = entity_po_concat_df[\n", "    entity_po_concat_df[\"po_state\"].isin(\n", "        [\"Rescheduled\", \"Expired\", \"Fulfilled\", \"Scheduled\", \"Created\"]\n", "    )\n", "]\n", "\n", "del [[entity_po_current_df, entity_po_past_df]]\n", "gc.collect()\n", "\n", "print(entity_po_df.shape[0])\n", "\n", "entity_po_df[\"grn_qty\"] = entity_po_df[\"grn_qty\"].fillna(0)\n", "entity_po_df[\"issue_date\"] = pd.to_datetime(entity_po_df[\"issue_date\"])\n", "entity_po_df[\"issue_date_dt\"] = entity_po_df[\"issue_date\"].dt.date\n", "entity_po_df[\"issue_date_month\"] = entity_po_df[\"issue_date\"].dt.month\n", "entity_po_df[\"issue_date_year\"] = entity_po_df[\"issue_date\"].dt.year\n", "# Mapping Entity Type\n", "entity_po_df = pd.merge(\n", "    entity_po_df,\n", "    entity_vendor_base_df[[\"entity_vendor_id\", \"vendor_name\", \"entity_type\"]].drop_duplicates(),\n", "    on=[\"entity_vendor_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "entity_po_df.head(2)"]}, {"cell_type": "markdown", "id": "a1ffc67b-f5d0-4a18-9fc1-2cf72a883042", "metadata": {}, "source": ["# SSC vs Entity"]}, {"cell_type": "code", "execution_count": null, "id": "605931ff-597f-4b51-853a-e3d4c56a08ff", "metadata": {}, "outputs": [], "source": ["po_outlets_list = list(entity_po_df[\"outlet_id\"].unique())\n", "po_outlets_list = tuple(po_outlets_list)"]}, {"cell_type": "code", "execution_count": null, "id": "cb67dd39-d30a-4797-bbc4-65a5db6a0f26", "metadata": {}, "outputs": [], "source": ["# Filtering on Issue Date"]}, {"cell_type": "code", "execution_count": null, "id": "690d3ff8-450b-48e6-80f2-599c059bc5c7", "metadata": {}, "outputs": [], "source": ["# Monthly Aggregates\n", "## 1. Overall\n", "## 2. Entity Type Level\n", "## 3. Enti<PERSON> Level"]}, {"cell_type": "code", "execution_count": null, "id": "d437b152-47c1-408f-be2a-3f8bc7847b7a", "metadata": {}, "outputs": [], "source": ["entity_po_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "84d029f4-104c-466c-bed9-864eb45b9790", "metadata": {}, "outputs": [], "source": ["entity_po_df[\"grn_date_set\"] = pd.to_datetime(entity_po_df[\"grn_ts\"]).dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "b6d3f9e4-0924-458b-8e5a-e3da0e8bbfff", "metadata": {}, "outputs": [], "source": ["# Monthly Aggregates\n", "## 1. Overall\n", "\n", "# Aggregate at year and month and po_state\n", "mtd_aggregate_po = (\n", "    entity_po_df.groupby([\"issue_date_year\", \"issue_date_month\"])\n", "    .agg({\"expected_po_qty\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"issue_date_year\", \"issue_date_month\"], ascending=False)\n", ")\n", "mtd_aggregate_po[\"identifier\"] = (\n", "    mtd_aggregate_po[\"issue_date_year\"].astype(str)\n", "    + \" \"\n", "    + mtd_aggregate_po[\"issue_date_month\"].astype(str)\n", ")\n", "mtd_aggregate_po = mtd_aggregate_po.drop(columns={\"issue_date_year\", \"issue_date_month\"})\n", "mtd_aggregate_po = mtd_aggregate_po.rename(columns={\"expected_po_qty\": \"po_quantity_raised\"})\n", "\n", "\n", "mtd_aggregate_grn = entity_po_df[(pd.to_datetime(entity_po_df[\"grn_date_set\"]).isna() == False)]\n", "mtd_aggregate_grn[\"grn_date_set\"] = pd.to_datetime(mtd_aggregate_grn[\"grn_date_set\"])\n", "mtd_aggregate_grn[\"grn_date_year\"] = mtd_aggregate_grn[\"grn_date_set\"].dt.year\n", "mtd_aggregate_grn[\"grn_date_month\"] = mtd_aggregate_grn[\"grn_date_set\"].dt.month\n", "\n", "mtd_aggregate_grn[\"identifier\"] = (\n", "    mtd_aggregate_grn[\"grn_date_year\"].astype(str)\n", "    + \" \"\n", "    + mtd_aggregate_grn[\"grn_date_month\"].astype(str)\n", ")\n", "mtd_aggregate_grn = (\n", "    mtd_aggregate_grn.groupby([\"identifier\"])\n", "    .agg({\"grn_qty\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"identifier\"], ascending=False)\n", ")\n", "mtd_aggregate_grn = mtd_aggregate_grn[[\"grn_qty\", \"identifier\"]]\n", "\n", "mtd_aggregate_po = pd.merge(mtd_aggregate_po, mtd_aggregate_grn, on=[\"identifier\"], how=\"outer\")\n", "\n", "\n", "# Yesterday Scheduled PO\n", "entity_po_df[\"issue_date_dt\"] = pd.to_datetime(entity_po_df[\"issue_date_dt\"])\n", "yesterday_po = entity_po_df[\n", "    (\n", "        pd.to_datetime(entity_po_df[\"issue_date_dt\"])\n", "        == pd.to_datetime(date.today() - timed<PERSON>ta(days=1))\n", "    )\n", "]\n", "yesterday_po = (\n", "    yesterday_po.groupby([\"issue_date_dt\"])\n", "    .agg({\"expected_po_qty\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"issue_date_dt\"], ascending=False)\n", ")\n", "yesterday_po[\"identifier\"] = \"Yesterday - (t-1)\"\n", "yesterday_po = yesterday_po.drop(columns={\"issue_date_dt\"}).rename(\n", "    columns={\"expected_po_qty\": \"po_quantity_raised\"}\n", ")\n", "\n", "# Yesterday GRN\n", "entity_po_df[\"grn_date_set\"] = pd.to_datetime(entity_po_df[\"grn_ts\"]).dt.date\n", "yesterday_grn = entity_po_df[\n", "    (\n", "        pd.to_datetime(entity_po_df[\"grn_date_set\"])\n", "        == pd.to_datetime(date.today() - timed<PERSON>ta(days=1))\n", "    )\n", "]\n", "yesterday_grn = (\n", "    yesterday_grn.groupby([\"grn_date_set\"])\n", "    .agg({\"grn_qty\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"grn_date_set\"], ascending=False)\n", ")\n", "yesterday_grn[\"identifier\"] = \"Yesterday - (t-1)\"\n", "yesterday_grn = yesterday_grn.drop(columns={\"grn_date_set\"})\n", "\n", "yesterday_po = pd.merge(yesterday_po, yesterday_grn, on=[\"identifier\"], how=\"outer\")\n", "yesterday_po = yesterday_po[[\"po_quantity_raised\", \"grn_qty\", \"identifier\"]]\n", "\n", "# L7 Scheduled PO\n", "l7_po = entity_po_df[\n", "    (\n", "        pd.to_datetime(entity_po_df[\"issue_date_dt\"])\n", "        >= pd.to_datetime(date.today() - <PERSON><PERSON><PERSON>(days=7))\n", "    )\n", "    & (\n", "        pd.to_datetime(entity_po_df[\"issue_date_dt\"])\n", "        < pd.to_datetime(date.today() - timed<PERSON>ta(days=0))\n", "    )\n", "]\n", "l7_po[\"identifier\"] = \"last 7 days\"\n", "l7_po = (\n", "    l7_po.groupby([\"identifier\"])\n", "    .agg({\"expected_po_qty\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"identifier\"], ascending=False)\n", ")\n", "l7_po = l7_po.rename(columns={\"expected_po_qty\": \"po_quantity_raised\"})\n", "l7_po = l7_po[[\"po_quantity_raised\", \"identifier\"]]\n", "\n", "# L7 GRN\n", "l7_grn = entity_po_df[\n", "    (\n", "        pd.to_datetime(entity_po_df[\"grn_date_set\"])\n", "        >= pd.to_datetime(date.today() - <PERSON><PERSON><PERSON>(days=7))\n", "    )\n", "    & (\n", "        pd.to_datetime(entity_po_df[\"grn_date_set\"])\n", "        < pd.to_datetime(date.today() - timed<PERSON>ta(days=0))\n", "    )\n", "]\n", "l7_grn[\"identifier\"] = \"last 7 days\"\n", "l7_grn = (\n", "    l7_grn.groupby([\"identifier\"])\n", "    .agg({\"grn_qty\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"identifier\"], ascending=False)\n", ")\n", "l7_grn = l7_grn[[\"grn_qty\", \"identifier\"]]\n", "\n", "l7_po = pd.merge(l7_po, l7_grn, on=[\"identifier\"], how=\"outer\")\n", "l7_po = l7_po[[\"po_quantity_raised\", \"grn_qty\", \"identifier\"]]\n", "\n", "mtd_aggregate_po = pd.concat([mtd_aggregate_po, yesterday_po, l7_po])\n", "\n", "mtd_aggregate_po[\"index_key\"] = \"1\"\n", "\n", "po_summary_df = mtd_aggregate_po.pivot_table(\n", "    index=[\"index_key\"],\n", "    columns=[\"identifier\"],\n", "    values=[\"po_quantity_raised\", \"grn_qty\"],\n", "    fill_value=0,\n", ").reset_index()\n", "\n", "po_summary_df = po_summary_df.drop(columns={\"index_key\"})\n", "\n", "po_summary_df"]}, {"cell_type": "code", "execution_count": null, "id": "556025bb-9c1a-4bbb-bf37-e49eeb3962d2", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1MhYtIM3TGvQoNnC6ciRuKXAnoeEBqTLOh_pQ-uPUtWM\"\n", "output_sheet = \"po_summary_df\"\n", "pb.to_sheets(po_summary_df, sheet_id, output_sheet)"]}, {"cell_type": "code", "execution_count": null, "id": "f0b6a2bb-dad5-41e4-989c-39860a5e789d", "metadata": {}, "outputs": [], "source": ["del [\n", "    [\n", "        po_summary_df,\n", "        mtd_aggregate_po,\n", "        yesterday_po,\n", "        l7_po,\n", "        mtd_aggregate_grn,\n", "        yesterday_grn,\n", "        l7_grn,\n", "    ]\n", "]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "ae308dbb-ef19-4d30-857b-8068bdf9d454", "metadata": {}, "outputs": [], "source": ["alldfs = [var for var in dir() if isinstance(eval(var), pd.core.frame.DataFrame)]\n", "print(alldfs)"]}, {"cell_type": "code", "execution_count": null, "id": "9147eb69-e190-4633-a675-8845c17a6aa7", "metadata": {}, "outputs": [], "source": ["entity_po_df[(pd.to_datetime(entity_po_df[\"grn_date_set\"]).isna() == False)].head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "507988bb-1830-4b05-93dc-a3cf3642c4f1", "metadata": {}, "outputs": [], "source": ["## 2. Entity Type Level\n", "\n", "# Aggregate at entity type, year and month and po_state\n", "mtd_aggregate_entity_type_po = (\n", "    entity_po_df.groupby([\"entity_type\", \"issue_date_year\", \"issue_date_month\"])\n", "    .agg({\"expected_po_qty\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"issue_date_year\", \"issue_date_month\"], ascending=False)\n", ")\n", "mtd_aggregate_entity_type_po[\"identifier\"] = (\n", "    mtd_aggregate_entity_type_po[\"issue_date_year\"].astype(str)\n", "    + \" \"\n", "    + mtd_aggregate_entity_type_po[\"issue_date_month\"].astype(str)\n", ")\n", "mtd_aggregate_entity_type_po = mtd_aggregate_entity_type_po.drop(\n", "    columns={\"issue_date_year\", \"issue_date_month\"}\n", ").rename(columns={\"expected_po_qty\": \"po_quantity_raised\"})\n", "\n", "# MTD GRN\n", "\n", "mtd_aggregate_entity_type_grn = entity_po_df[\n", "    (pd.to_datetime(entity_po_df[\"grn_date_set\"]).isna() == False)\n", "]\n", "mtd_aggregate_entity_type_grn[\"grn_date_set\"] = pd.to_datetime(\n", "    mtd_aggregate_entity_type_grn[\"grn_date_set\"]\n", ")\n", "mtd_aggregate_entity_type_grn[\"grn_date_year\"] = mtd_aggregate_entity_type_grn[\n", "    \"grn_date_set\"\n", "].dt.year\n", "mtd_aggregate_entity_type_grn[\"grn_date_month\"] = mtd_aggregate_entity_type_grn[\n", "    \"grn_date_set\"\n", "].dt.month\n", "\n", "mtd_aggregate_entity_type_grn[\"identifier\"] = (\n", "    mtd_aggregate_entity_type_grn[\"grn_date_year\"].astype(str)\n", "    + \" \"\n", "    + mtd_aggregate_entity_type_grn[\"grn_date_month\"].astype(str)\n", ")\n", "mtd_aggregate_entity_type_grn = (\n", "    mtd_aggregate_entity_type_grn.groupby([\"entity_type\", \"identifier\"])\n", "    .agg({\"grn_qty\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"entity_type\", \"identifier\"], ascending=False)\n", ")\n", "mtd_aggregate_entity_type_grn = mtd_aggregate_entity_type_grn[\n", "    [\"entity_type\", \"grn_qty\", \"identifier\"]\n", "]\n", "\n", "mtd_aggregate_entity_type_po = pd.merge(\n", "    mtd_aggregate_entity_type_po,\n", "    mtd_aggregate_entity_type_grn,\n", "    on=[\"entity_type\", \"identifier\"],\n", "    how=\"outer\",\n", ")\n", "\n", "\n", "# Yesterday Scheduled PO\n", "\n", "yesterday_entity_type_po = entity_po_df[\n", "    (\n", "        pd.to_datetime(entity_po_df[\"issue_date_dt\"])\n", "        == pd.to_datetime(date.today() - timed<PERSON>ta(days=1))\n", "    )\n", "]\n", "yesterday_entity_type_po = (\n", "    yesterday_entity_type_po.groupby([\"entity_type\", \"issue_date_dt\"])\n", "    .agg({\"expected_po_qty\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"issue_date_dt\"], ascending=False)\n", ")\n", "yesterday_entity_type_po[\"identifier\"] = \"Yesterday - (t-1)\"\n", "yesterday_entity_type_po = yesterday_entity_type_po.drop(columns={\"issue_date_dt\"}).rename(\n", "    columns={\"expected_po_qty\": \"po_quantity_raised\"}\n", ")\n", "\n", "\n", "# Yesterday GRN\n", "\n", "yesterday_entity_type_grn = entity_po_df[\n", "    (\n", "        pd.to_datetime(entity_po_df[\"grn_date_set\"])\n", "        == pd.to_datetime(date.today() - timed<PERSON>ta(days=1))\n", "    )\n", "]\n", "yesterday_entity_type_grn = (\n", "    yesterday_entity_type_grn.groupby([\"entity_type\", \"grn_date_set\"])\n", "    .agg({\"grn_qty\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"grn_date_set\"], ascending=False)\n", ")\n", "yesterday_entity_type_grn[\"identifier\"] = \"Yesterday - (t-1)\"\n", "yesterday_entity_type_grn = yesterday_entity_type_grn.drop(columns={\"grn_date_set\"})\n", "\n", "yesterday_entity_type_po = pd.merge(\n", "    yesterday_entity_type_po,\n", "    yesterday_entity_type_grn,\n", "    on=[\"identifier\", \"entity_type\"],\n", "    how=\"outer\",\n", ")\n", "\n", "# L7 Scheduled PO\n", "\n", "l7_entity_type_po = entity_po_df[\n", "    (\n", "        pd.to_datetime(entity_po_df[\"issue_date_dt\"])\n", "        >= pd.to_datetime(date.today() - <PERSON><PERSON><PERSON>(days=7))\n", "    )\n", "    & (\n", "        pd.to_datetime(entity_po_df[\"issue_date_dt\"])\n", "        < pd.to_datetime(date.today() - timed<PERSON>ta(days=0))\n", "    )\n", "]\n", "l7_entity_type_po[\"identifier\"] = \"last 7 days\"\n", "l7_entity_type_po = (\n", "    l7_entity_type_po.groupby([\"entity_type\", \"identifier\"])\n", "    .agg({\"expected_po_qty\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"entity_type\", \"identifier\"], ascending=False)\n", ")\n", "l7_entity_type_po = l7_entity_type_po.rename(columns={\"expected_po_qty\": \"po_quantity_raised\"})\n", "l7_entity_type_po = l7_entity_type_po[[\"entity_type\", \"po_quantity_raised\", \"identifier\"]]\n", "\n", "\n", "# L7 GRN\n", "l7_entity_type_grn = entity_po_df[\n", "    (\n", "        pd.to_datetime(entity_po_df[\"grn_date_set\"])\n", "        >= pd.to_datetime(date.today() - <PERSON><PERSON><PERSON>(days=7))\n", "    )\n", "    & (\n", "        pd.to_datetime(entity_po_df[\"grn_date_set\"])\n", "        < pd.to_datetime(date.today() - timed<PERSON>ta(days=0))\n", "    )\n", "]\n", "l7_entity_type_grn[\"identifier\"] = \"last 7 days\"\n", "l7_entity_type_grn = (\n", "    l7_entity_type_grn.groupby([\"entity_type\", \"identifier\"])\n", "    .agg({\"grn_qty\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"entity_type\", \"identifier\"], ascending=False)\n", ")\n", "l7_entity_type_grn = l7_entity_type_grn[[\"entity_type\", \"grn_qty\", \"identifier\"]]\n", "\n", "l7_entity_type_po = pd.merge(\n", "    l7_entity_type_po, l7_entity_type_grn, on=[\"entity_type\", \"identifier\"], how=\"outer\"\n", ")\n", "l7_entity_type_po = l7_entity_type_po[\n", "    [\"entity_type\", \"po_quantity_raised\", \"grn_qty\", \"identifier\"]\n", "]\n", "\n", "\n", "yesterday_entity_type_po = yesterday_entity_type_po[\n", "    [\"entity_type\", \"po_quantity_raised\", \"grn_qty\", \"identifier\"]\n", "]\n", "\n", "mtd_aggregate_entity_type_po = pd.concat(\n", "    [mtd_aggregate_entity_type_po, yesterday_entity_type_po, l7_entity_type_po]\n", ")\n", "\n", "\n", "po_summary_entity_type_df = mtd_aggregate_entity_type_po.pivot_table(\n", "    index=[\"entity_type\"],\n", "    columns=[\"identifier\"],\n", "    values=[\"grn_qty\", \"po_quantity_raised\"],\n", "    fill_value=0,\n", ").reset_index()\n", "\n", "po_summary_entity_type_df"]}, {"cell_type": "code", "execution_count": null, "id": "f023a780-3295-4963-804d-f18fbb73047a", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1MhYtIM3TGvQoNnC6ciRuKXAnoeEBqTLOh_pQ-uPUtWM\"\n", "output_sheet = \"po_summary_entity_type_df\"\n", "pb.to_sheets(po_summary_entity_type_df, sheet_id, output_sheet)"]}, {"cell_type": "code", "execution_count": null, "id": "965b7889-9ffe-4052-8037-5710dbb7d2ba", "metadata": {}, "outputs": [], "source": ["del [\n", "    [\n", "        po_summary_entity_type_df,\n", "        mtd_aggregate_entity_type_po,\n", "        yesterday_entity_type_po,\n", "        l7_entity_type_po,\n", "        mtd_aggregate_entity_type_grn,\n", "        yesterday_entity_type_grn,\n", "        l7_entity_type_grn,\n", "    ]\n", "]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "8548ec4f-5c43-418a-b747-1b550e4994b2", "metadata": {}, "outputs": [], "source": ["alldfs = [var for var in dir() if isinstance(eval(var), pd.core.frame.DataFrame)]\n", "print(alldfs)"]}, {"cell_type": "code", "execution_count": null, "id": "6fd12c13-ccda-4591-80e3-c40050e2371f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "10d87168-b6b5-4383-b772-b38e2073b767", "metadata": {}, "outputs": [], "source": ["## 3. Enti<PERSON> Level\n", "\n", "# Aggregate at entity, year and month and po_state\n", "mtd_aggregate_entity_po = (\n", "    entity_po_df.groupby([\"vendor_name\", \"issue_date_year\", \"issue_date_month\"])\n", "    .agg({\"expected_po_qty\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"issue_date_year\", \"issue_date_month\"], ascending=False)\n", ")\n", "mtd_aggregate_entity_po[\"identifier\"] = (\n", "    mtd_aggregate_entity_po[\"issue_date_year\"].astype(str)\n", "    + \" \"\n", "    + mtd_aggregate_entity_po[\"issue_date_month\"].astype(str)\n", ")\n", "mtd_aggregate_entity_po = mtd_aggregate_entity_po.drop(\n", "    columns={\"issue_date_year\", \"issue_date_month\"}\n", ").rename(columns={\"expected_po_qty\": \"po_quantity_raised\"})\n", "\n", "\n", "# MTD GRN\n", "\n", "mtd_aggregate_entity_grn = entity_po_df[\n", "    (pd.to_datetime(entity_po_df[\"grn_date_set\"]).isna() == False)\n", "]\n", "mtd_aggregate_entity_grn[\"grn_date_set\"] = pd.to_datetime(mtd_aggregate_entity_grn[\"grn_date_set\"])\n", "mtd_aggregate_entity_grn[\"grn_date_year\"] = mtd_aggregate_entity_grn[\"grn_date_set\"].dt.year\n", "mtd_aggregate_entity_grn[\"grn_date_month\"] = mtd_aggregate_entity_grn[\"grn_date_set\"].dt.month\n", "\n", "mtd_aggregate_entity_grn[\"identifier\"] = (\n", "    mtd_aggregate_entity_grn[\"grn_date_year\"].astype(str)\n", "    + \" \"\n", "    + mtd_aggregate_entity_grn[\"grn_date_month\"].astype(str)\n", ")\n", "mtd_aggregate_entity_grn = (\n", "    mtd_aggregate_entity_grn.groupby([\"vendor_name\", \"identifier\"])\n", "    .agg({\"grn_qty\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"vendor_name\", \"identifier\"], ascending=False)\n", ")\n", "mtd_aggregate_entity_grn = mtd_aggregate_entity_grn[[\"vendor_name\", \"grn_qty\", \"identifier\"]]\n", "\n", "mtd_aggregate_entity_po = pd.merge(\n", "    mtd_aggregate_entity_po,\n", "    mtd_aggregate_entity_grn,\n", "    on=[\"vendor_name\", \"identifier\"],\n", "    how=\"outer\",\n", ")\n", "\n", "\n", "# Yesterday Scheduled PO\n", "\n", "yesterday_entity_po = entity_po_df[\n", "    (\n", "        pd.to_datetime(entity_po_df[\"issue_date_dt\"])\n", "        == pd.to_datetime(date.today() - timed<PERSON>ta(days=1))\n", "    )\n", "]\n", "yesterday_entity_po = (\n", "    yesterday_entity_po.groupby([\"vendor_name\", \"issue_date_dt\"])\n", "    .agg({\"expected_po_qty\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"issue_date_dt\"], ascending=False)\n", ")\n", "yesterday_entity_po[\"identifier\"] = \"Yesterday - (t-1)\"\n", "yesterday_entity_po = yesterday_entity_po.drop(columns={\"issue_date_dt\"}).rename(\n", "    columns={\"expected_po_qty\": \"po_quantity_raised\"}\n", ")\n", "\n", "# Yesterday GRN\n", "\n", "yesterday_entity_grn = entity_po_df[\n", "    (\n", "        pd.to_datetime(entity_po_df[\"grn_date_set\"])\n", "        == pd.to_datetime(date.today() - timed<PERSON>ta(days=1))\n", "    )\n", "]\n", "yesterday_entity_grn = (\n", "    yesterday_entity_grn.groupby([\"vendor_name\", \"grn_date_set\"])\n", "    .agg({\"grn_qty\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"grn_date_set\"], ascending=False)\n", ")\n", "yesterday_entity_grn[\"identifier\"] = \"Yesterday - (t-1)\"\n", "yesterday_entity_grn = yesterday_entity_grn.drop(columns={\"grn_date_set\"})\n", "\n", "yesterday_entity_po = pd.merge(\n", "    yesterday_entity_po,\n", "    yesterday_entity_grn,\n", "    on=[\"identifier\", \"vendor_name\"],\n", "    how=\"outer\",\n", ")\n", "yesterday_entity_po = yesterday_entity_po[\n", "    [\"vendor_name\", \"po_quantity_raised\", \"grn_qty\", \"identifier\"]\n", "]\n", "\n", "# L7 Scheduled PO\n", "\n", "l7_entity_po = entity_po_df[\n", "    (\n", "        pd.to_datetime(entity_po_df[\"issue_date_dt\"])\n", "        >= pd.to_datetime(date.today() - <PERSON><PERSON><PERSON>(days=7))\n", "    )\n", "    & (\n", "        pd.to_datetime(entity_po_df[\"issue_date_dt\"])\n", "        < pd.to_datetime(date.today() - timed<PERSON>ta(days=0))\n", "    )\n", "]\n", "l7_entity_po[\"identifier\"] = \"last 7 days\"\n", "l7_entity_po = (\n", "    l7_entity_po.groupby([\"vendor_name\", \"identifier\"])\n", "    .agg({\"expected_po_qty\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"vendor_name\", \"identifier\"], ascending=False)\n", ")\n", "l7_entity_po = l7_entity_po.rename(columns={\"expected_po_qty\": \"po_quantity_raised\"})\n", "l7_entity_po = l7_entity_po[[\"vendor_name\", \"po_quantity_raised\", \"identifier\"]]\n", "\n", "# L7 GRN\n", "\n", "l7_entity_grn = entity_po_df[\n", "    (\n", "        pd.to_datetime(entity_po_df[\"grn_date_set\"])\n", "        >= pd.to_datetime(date.today() - <PERSON><PERSON><PERSON>(days=7))\n", "    )\n", "    & (\n", "        pd.to_datetime(entity_po_df[\"grn_date_set\"])\n", "        < pd.to_datetime(date.today() - timed<PERSON>ta(days=0))\n", "    )\n", "]\n", "l7_entity_grn[\"identifier\"] = \"last 7 days\"\n", "l7_entity_grn = (\n", "    l7_entity_grn.groupby([\"vendor_name\", \"identifier\"])\n", "    .agg({\"grn_qty\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"vendor_name\", \"identifier\"], ascending=False)\n", ")\n", "l7_entity_grn = l7_entity_grn[[\"vendor_name\", \"grn_qty\", \"identifier\"]]\n", "\n", "l7_entity_po = pd.merge(l7_entity_po, l7_entity_grn, on=[\"vendor_name\", \"identifier\"], how=\"outer\")\n", "l7_entity_po = l7_entity_po[[\"vendor_name\", \"po_quantity_raised\", \"grn_qty\", \"identifier\"]]\n", "\n", "mtd_aggregate_entity_po = pd.concat([mtd_aggregate_entity_po, yesterday_entity_po, l7_entity_po])\n", "\n", "po_summary_entity_df = mtd_aggregate_entity_po.pivot_table(\n", "    index=[\"vendor_name\"],\n", "    columns=[\"identifier\"],\n", "    values=[\"grn_qty\", \"po_quantity_raised\"],\n", "    fill_value=0,\n", ").reset_index()\n", "po_summary_entity_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "c38d61da-23d5-40ac-93e9-0bddbb940b5c", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1MhYtIM3TGvQoNnC6ciRuKXAnoeEBqTLOh_pQ-uPUtWM\"\n", "output_sheet = \"po_summary_entity_df\"\n", "pb.to_sheets(po_summary_entity_df, sheet_id, output_sheet)"]}, {"cell_type": "code", "execution_count": null, "id": "d4265f66-78a7-4289-8d65-9649f1bb5792", "metadata": {}, "outputs": [], "source": ["del [\n", "    [\n", "        po_summary_entity_df,\n", "        yesterday_entity_po,\n", "        l7_entity_po,\n", "        mtd_aggregate_entity_grn,\n", "        yesterday_entity_grn,\n", "        l7_entity_grn,\n", "    ]\n", "]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "a7da79cf-b37c-42ce-8106-646a68cddef4", "metadata": {}, "outputs": [], "source": ["alldfs = [var for var in dir() if isinstance(eval(var), pd.core.frame.DataFrame)]\n", "print(alldfs)"]}, {"cell_type": "code", "execution_count": null, "id": "37b798ca-edab-4f90-b522-7108c6814c02", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "31285e74-ee02-483d-8b8f-affdb2a2f772", "metadata": {}, "outputs": [], "source": ["# En<PERSON><PERSON>or -  Inventory"]}, {"cell_type": "code", "execution_count": null, "id": "bd9e8e8c-38a7-4782-91ae-84ad0dc9f2ca", "metadata": {}, "outputs": [], "source": ["entity_inventory_query = f\"\"\"\n", "select \n", "    entity_vendor_id, outlet_id, evm.variant_id, item_id, evm.upc_id, quantity, selling_price\n", "from \n", "    lake_view_ims.ims_entity_vendor_inventory evm\n", "left join \n", "    lake_view_rpc.product_product pp on pp.variant_id = evm.variant_id and pp.approved = 1 and pp.active = 1 and pp.lake_active_record = true\n", "where \n", "    evm.lake_active_record = true and evm.active = true\n", "and \n", "    entity_vendor_id in \n", "        (\n", "            select distinct entity_vendor_id \n", "            from lake_retail.outlet_entity_vendor_mapping \n", "            where entity_vendor_type_id = 5 and active = 1 and lake_active_record = true\n", "        )\"\"\"\n", "entity_inventory_df = read_sql_query(entity_inventory_query, CON_PRESTO)"]}, {"cell_type": "code", "execution_count": null, "id": "ffbe718b-466a-42b2-962d-7c4c6cb0f006", "metadata": {}, "outputs": [], "source": ["wlp_query = f\"\"\"\n", "with wlp_merchant as \n", "(select date(current_date) as date,merchant_id, merchant_flag, item_id, avg(wlp) as wlp\n", "from\n", "(select * \n", "from dwh.logs_weighted_landing_price\n", "where date(current_date + interval '5.5 Hours') between start_ts_ist and end_ts_ist -- and item_id = 10002888 and merchant_id = 25568--and merchant_flag = 'frontend'-- and merchant_id = 29520\n", "order by start_ts_ist desc\n", ")\n", "group by 1,2,3,4\n", "),\n", "\n", "frontend_merchant as \n", "(\n", "select distinct date(order_checkout_ts_ist) as date, frontend_merchant_id as merchant_id, outlet_id\n", "from dwh.fact_supply_chain_order_details\n", "where date(order_checkout_ts_ist) =  date(current_date)\n", "),\n", "\n", "backend_merchant as \n", "(\n", "select distinct cms_store as merchant_id, outlet_id\n", "from lake_retail.console_outlet_cms_store\n", "where active = 1\n", "),\n", "\n", "landing_price as \n", "(\n", "select \n", "base.outlet_id, item_id, avg(wlp) as wlp\n", "FROM\n", "    (select wlp.date, wlp.merchant_id, wlp.merchant_flag, wlp.item_id, wlp.wlp,\n", "    case when merchant_flag = 'frontend' then fm.outlet_id else bm.outlet_id end as outlet_id\n", "    from wlp_merchant wlp\n", "    left join frontend_merchant fm on wlp.merchant_id = fm.merchant_id and wlp.date = fm.date\n", "    left join backend_merchant bm on wlp.merchant_id = bm.merchant_id\n", "    ) base\n", "left join lake_retail.console_outlet co on base.outlet_id = co.id and co.active = 1\n", "group by 1,2\n", ")\n", "\n", "select * from landing_price\n", "\n", "\"\"\"\n", "wlp_df = read_sql_query(wlp_query, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "d35bc675-8336-485c-8cf0-5408be767a72", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4bf8fdd0-cdc1-482a-8bcd-f9ee0996e6a6", "metadata": {}, "outputs": [], "source": ["product_product_query = f\"\"\"\n", "select distinct upc as upc_id, item_id, variant_id\n", "from lake_view_rpc.product_product\n", "where active = 1 and approved = 1\n", "\"\"\"\n", "product_product_df = read_sql_query(product_product_query, CON_PRESTO)"]}, {"cell_type": "code", "execution_count": null, "id": "4f548252-843f-4446-a70d-50d9d6243e1e", "metadata": {}, "outputs": [], "source": ["entity_inventory_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c09e973e-8bd8-443e-a17d-c36153594677", "metadata": {}, "outputs": [], "source": ["# Map on upc_id, where we are unable to fetch item_id on variant_id join with lake_rpc.product_product\n", "\n", "# 1. Merge with product_product\n", "entity_inventory_df = pd.merge(\n", "    entity_inventory_df,\n", "    product_product_df[[\"upc_id\", \"item_id\"]].drop_duplicates(),\n", "    on=[\"upc_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "# 2. Replace missing item_id with item_id_y (got by merge on upc_id)\n", "entity_inventory_df[\"item_id_x\"] = np.where(\n", "    entity_inventory_df[\"item_id_x\"].isna(),\n", "    entity_inventory_df[\"item_id_y\"],\n", "    entity_inventory_df[\"item_id_x\"],\n", ")\n", "entity_inventory_df = entity_inventory_df.drop(columns={\"item_id_y\"}).rename(\n", "    columns={\"item_id_x\": \"item_id\"}\n", ")\n", "\n", "# 3. Add Inventory column on logic - if quantity < 0 then 0 else quantity\n", "entity_inventory_df[\"inventory\"] = np.where(\n", "    entity_inventory_df[\"quantity\"] < 0, 0, entity_inventory_df[\"quantity\"]\n", ")\n", "entity_inventory_df = (\n", "    entity_inventory_df.groupby([\"entity_vendor_id\", \"outlet_id\", \"item_id\"])\n", "    .agg({\"inventory\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "entity_inventory_df = pd.merge(entity_inventory_df, wlp_df, on=[\"outlet_id\", \"item_id\"], how=\"left\")\n", "\n", "wlp_df_item = wlp_df.groupby([\"item_id\"]).agg({\"wlp\": \"mean\"}).reset_index()\n", "\n", "entity_inventory_df = pd.merge(entity_inventory_df, wlp_df_item, on=[\"item_id\"], how=\"left\")\n", "\n", "entity_inventory_df[\"wlp_x\"] = np.where(\n", "    entity_inventory_df[\"wlp_x\"].isna(),\n", "    entity_inventory_df[\"wlp_y\"],\n", "    entity_inventory_df[\"wlp_x\"],\n", ")\n", "\n", "entity_inventory_df = entity_inventory_df.drop(columns={\"wlp_y\"}).rename(columns={\"wlp_x\": \"wlp\"})\n", "entity_inventory_df[\"inventory_value\"] = (\n", "    entity_inventory_df[\"inventory\"] * entity_inventory_df[\"wlp\"]\n", ")\n", "entity_inventory_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "67bdc95f-418b-4f8b-ace7-d8991633c4f8", "metadata": {}, "outputs": [], "source": ["# Current Inventory\n", "\n", "aggregate_inventory = (\n", "    entity_inventory_df.groupby([\"entity_vendor_id\", \"outlet_id\"])\n", "    .agg({\"inventory\": \"sum\", \"inventory_value\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "aggregate_inventory = pd.merge(\n", "    aggregate_inventory,\n", "    entity_vendor_base_df[\n", "        [\"entity_vendor_id\", \"vendor_name\", \"outlet_name\", \"facility_id\", \"entity_type\"]\n", "    ].drop_duplicates(),\n", "    on=[\"entity_vendor_id\"],\n", "    how=\"left\",\n", ")\n", "aggregate_inventory = aggregate_inventory[\n", "    [\n", "        \"entity_vendor_id\",\n", "        \"vendor_name\",\n", "        \"entity_type\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"facility_id\",\n", "        \"inventory\",\n", "        \"inventory_value\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "32f27d92-b1e3-4a3b-9cb2-5d6a13104c1c", "metadata": {}, "outputs": [], "source": ["# Inventory tagged to missing item_id\n", "inventory_tagged_to_missing_item_id = entity_inventory_df.copy()\n", "inventory_tagged_to_missing_item_id[\"flag\"] = 1\n", "inventory_tagged_to_missing_item_id = (\n", "    inventory_tagged_to_missing_item_id[inventory_tagged_to_missing_item_id.item_id.isna()]\n", "    .groupby([\"flag\"])\n", "    .agg({\"inventory\": \"sum\"})\n", "    .reset_index()\n", ")\n", "inventory_tagged_to_missing_item_id"]}, {"cell_type": "code", "execution_count": null, "id": "2aca9a29-2bdb-4e6a-bd56-2433f80a91b3", "metadata": {}, "outputs": [], "source": ["aggregate_inventory.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "5d49ccd3-21fb-4f0a-97a8-8f4b11aa61bf", "metadata": {}, "outputs": [], "source": ["aggregate_inventory_entity_type = (\n", "    aggregate_inventory.groupby([\"entity_type\"])\n", "    .agg({\"inventory\": \"sum\", \"inventory_value\": \"sum\"})\n", "    .reset_index()\n", ")\n", "aggregate_inventory_entity_type.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "48799d39-e36c-42f0-863f-fde40d43c62a", "metadata": {}, "outputs": [], "source": ["# Inventory\n", "\n", "sheet_id = \"1MhYtIM3TGvQoNnC6ciRuKXAnoeEBqTLOh_pQ-uPUtWM\"\n", "output_sheet = \"aggregate_inventory\"\n", "pb.to_sheets(aggregate_inventory, sheet_id, output_sheet)\n", "\n", "\n", "sheet_id = \"1MhYtIM3TGvQoNnC6ciRuKXAnoeEBqTLOh_pQ-uPUtWM\"\n", "output_sheet = \"aggregate_inventory_entity_type\"\n", "pb.to_sheets(aggregate_inventory_entity_type, sheet_id, output_sheet)"]}, {"cell_type": "code", "execution_count": null, "id": "f6a71d23-8e17-479e-b8f3-14d919608468", "metadata": {}, "outputs": [], "source": ["del [\n", "    [\n", "        aggregate_inventory,\n", "        aggregate_inventory_entity_type,\n", "        entity_inventory_df,\n", "        inventory_tagged_to_missing_item_id,\n", "        wlp_df_item,\n", "    ]\n", "]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "e94976ef-c8a7-463f-a14d-2f97c46d1b6c", "metadata": {}, "outputs": [], "source": ["alldfs = [var for var in dir() if isinstance(eval(var), pd.core.frame.DataFrame)]\n", "print(alldfs)"]}, {"cell_type": "code", "execution_count": null, "id": "cab425ef-e4cc-434b-a047-4d6f2d0686da", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "40e55255-1301-4fd3-b62c-fd0abadb043c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "21d0288a-8b70-4ab2-9965-f97f1822fc49", "metadata": {"tags": []}, "outputs": [], "source": ["# Enti<PERSON> Vendor - All B2C Sales"]}, {"cell_type": "code", "execution_count": null, "id": "f70a659f-b892-43d2-b956-9e57a7653562", "metadata": {}, "outputs": [], "source": ["y_day = pd.to_datetime(datetime.now() + timedelta(minutes=330)).date() - timedelta(days=1)\n", "y_day"]}, {"cell_type": "code", "execution_count": null, "id": "af33ea75-766f-482e-a7f9-48c30be94f69", "metadata": {}, "outputs": [], "source": ["entity_sales_query = f\"\"\"\n", "\n", "SELECT entity_vendor_id, insert_ds_ist, outlet_id, SUM(total_sold_quantity) as total_sold_quantity, SUM(total_return_quantity) as total_return_quantity, SUM(total_gross_bill_amount) as total_gross_bill_amount, SUM(total_return_amount) as total_return_amount\n", "FROM\n", "(\n", "    SELECT p.invoice_id,\n", "           p.source_entity_vendor_id as entity_vendor_id,\n", "        --   convert_timezone('Asia/Kolkata', p.pos_timestamp) AS pos_timestamp,\n", "        pi.insert_ds_ist,\n", "           r.id as outlet_id, -- added\n", "           p.outlet_name AS outlet_name,\n", "           r.legal_name AS seller_company,\n", "           pi.variant_id,\n", "           rp.item_id,\n", "           sum(coalesce(pi.cgst_value,0)+coalesce(pi.sgst_value,0)+ coalesce(pi.igst_value,0)+coalesce(pi.cess_value,0)) AS total_tax,\n", "\n", "           sum(CASE\n", "               WHEN p.invoice_type_id = 1 THEN pi.quantity\n", "               ELSE 0\n", "           END) AS total_sold_quantity,\n", "           sum(CASE\n", "               WHEN p.invoice_type_id = 2 THEN pi.quantity\n", "               ELSE 0\n", "           END) AS total_return_quantity,\n", "\n", "           sum(CASE\n", "               WHEN p.invoice_type_id = 1 THEN pi.selling_price*pi.quantity\n", "               ELSE 0\n", "           END) AS total_gross_bill_amount,\n", "           sum(CASE\n", "               WHEN p.invoice_type_id = 2 THEN pi.selling_price*pi.quantity\n", "               ELSE 0\n", "           END) AS total_return_amount\n", "\n", "    FROM (select * from lake_view_pos.pos_invoice_product_details where store_offer_id IS NULL) pi\n", "    INNER JOIN (select * from lake_view_pos.pos_invoice where invoice_type_id IN (1,2) \n", "                                and insert_ds_ist >= '{y_day}'-- and '2022-03-29'\n", "\n", "                                -- '{{start_date}}' and '{{end_date}}'\n", "                                -- and date(convert_timezone('Asia/Kolkata', pos_timestamp)) between '2022-01-01' and '2022-03-29'-- '{{start_date}}' and '{{end_date}}'\n", "\n", "                                ) p ON p.id = pi.invoice_id\n", "    INNER JOIN lake_view_retail.console_outlet r ON r.id = p.outlet_id\n", "    inner join lake_view_retail.console_company_type comp on comp.id = r.company_type_id\n", "    -- and comp.entity_type <> 'THIRD_PARTY'\n", "    -- and comp.id in (1,\n", "    --                 2,\n", "    --                 3,\n", "    --                 7,\n", "    --                 8,\n", "    --                 10)\n", "    -- INNER JOIN lake_view_retail.console_location l1 ON l1.\"id\"=r.\"tax_location_id\"\n", "    -- INNER JOIN lake_view_retail.console_location l2 ON l2.\"id\"=p.customer_city\n", "    INNER JOIN lake_view_rpc.product_product rp ON rp.variant_id = pi.variant_id\n", "    Where p.source_entity_vendor_id in (select distinct entity_vendor_id from lake_retail.outlet_entity_vendor_mapping where entity_vendor_type_id = 5 and active = 1 and lake_active_record = true)\n", "    group by 1,2,3,4,5,6,7,8\n", ")\n", "GROUP BY 1,2,3\n", "\"\"\"\n", "entity_sales_df = read_sql_query(entity_sales_query, CON_PRESTO)\n", "\n", "\n", "entity_sales_query = f\"\"\"\n", "SELECT entity_vendor_id, insert_ds_ist, outlet_id, SUM(total_sold_quantity) as total_sold_quantity, SUM(total_return_quantity) as total_return_quantity, SUM(total_gross_bill_amount) as total_gross_bill_amount, SUM(total_return_amount) as total_return_amount\n", "FROM\n", "(\n", "    SELECT p.invoice_id,\n", "           p.source_entity_vendor_id as entity_vendor_id,\n", "        --   convert_timezone('Asia/Kolkata', p.pos_timestamp) AS pos_timestamp,\n", "           pi.pos_timestamp + interval '330 minutes' as insert_ds_ist,\n", "           r.id as outlet_id, -- added\n", "           p.outlet_name AS outlet_name,\n", "           r.legal_name AS seller_company,\n", "           pi.variant_id,\n", "           rp.item_id,\n", "           sum(coalesce(pi.cgst_value,0)+coalesce(pi.sgst_value,0)+ coalesce(pi.igst_value,0)+coalesce(pi.cess_value,0)) AS total_tax,\n", "\n", "           sum(CASE\n", "               WHEN p.invoice_type_id = 1 THEN pi.quantity\n", "               ELSE 0\n", "           END) AS total_sold_quantity,\n", "           sum(CASE\n", "               WHEN p.invoice_type_id = 2 THEN pi.quantity\n", "               ELSE 0\n", "           END) AS total_return_quantity,\n", "\n", "           sum(CASE\n", "               WHEN p.invoice_type_id = 1 THEN pi.selling_price*pi.quantity\n", "               ELSE 0\n", "           END) AS total_gross_bill_amount,\n", "           sum(CASE\n", "               WHEN p.invoice_type_id = 2 THEN pi.selling_price*pi.quantity\n", "               ELSE 0\n", "           END) AS total_return_amount\n", "\n", "    FROM (select * from lake_pos.pos_invoice_product_details where store_offer_id IS NULL) pi\n", "    INNER JOIN (select * from lake_pos.pos_invoice where invoice_type_id IN (1,2) \n", "                                and pos_timestamp >= date('2021-12-01') - interval '330 minutes' and pos_timestamp < date('{y_day}') - interval '330 minutes' \n", "\n", "                                ) p ON p.id = pi.invoice_id\n", "    INNER JOIN lake_retail.console_outlet r ON r.id = p.outlet_id\n", "    inner join lake_retail.console_company_type comp on comp.id = r.company_type_id\n", "\n", "    INNER JOIN lake_rpc.product_product rp ON rp.variant_id = pi.variant_id\n", "    Where p.source_entity_vendor_id in (select distinct entity_vendor_id from lake_retail.outlet_entity_vendor_mapping where entity_vendor_type_id = 5 and active = 1)\n", "    group by 1,2,3,4,5,6,7,8\n", ")\n", "GROUP BY 1,2,3\"\"\"\n", "entity_sales_0_df = read_sql_query(entity_sales_query, CON_REDSHIFT)\n", "\n", "entity_sales_df = pd.concat([entity_sales_df, entity_sales_0_df]).drop_duplicates()\n", "del entity_sales_0_df"]}, {"cell_type": "code", "execution_count": null, "id": "66c1bc68-5d15-4ec0-825d-75976f66d94e", "metadata": {}, "outputs": [], "source": ["entity_sales_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "a8658c04-4f2e-4323-83de-3d256eb12208", "metadata": {}, "outputs": [], "source": ["# Aggregate DF - Move to Tracker"]}, {"cell_type": "code", "execution_count": null, "id": "edf18726-cf56-4353-8f3f-eb489e03eb05", "metadata": {}, "outputs": [], "source": ["# entity_vendor_base_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "686dc0fa-7749-42fd-905f-0e644da04748", "metadata": {}, "outputs": [], "source": ["# Sales across Dates\n", "\n", "\n", "aggregated_sales = (\n", "    entity_sales_df.groupby([\"insert_ds_ist\", \"entity_vendor_id\", \"outlet_id\"])\n", "    .agg(\n", "        {\n", "            \"total_sold_quantity\": \"sum\",\n", "            \"total_return_quantity\": \"sum\",\n", "            \"total_gross_bill_amount\": \"sum\",\n", "            \"total_return_amount\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "aggregated_sales = aggregated_sales.sort_values(by=[\"entity_vendor_id\", \"insert_ds_ist\"])\n", "\n", "print(aggregated_sales.shape[0])\n", "aggregated_sales = pd.merge(\n", "    aggregated_sales,\n", "    entity_vendor_base_df[\n", "        [\"entity_vendor_id\", \"vendor_name\", \"outlet_name\", \"facility_id\"]\n", "    ].drop_duplicates(),\n", "    on=[\"entity_vendor_id\"],\n", "    how=\"left\",\n", ")\n", "print(aggregated_sales.shape[0])\n", "\n", "aggregated_sales = aggregated_sales[\n", "    [\n", "        \"insert_ds_ist\",\n", "        \"entity_vendor_id\",\n", "        \"vendor_name\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"facility_id\",\n", "        \"total_sold_quantity\",\n", "        \"total_return_quantity\",\n", "        \"total_gross_bill_amount\",\n", "        \"total_return_amount\",\n", "    ]\n", "]\n", "\n", "aggregated_sales[\"insert_ds_ist\"] = pd.to_datetime(aggregated_sales[\"insert_ds_ist\"])\n", "aggregated_sales[\"month\"] = aggregated_sales[\"insert_ds_ist\"].dt.month\n", "aggregated_sales[\"year\"] = aggregated_sales[\"insert_ds_ist\"].dt.year\n", "aggregated_sales[\"identifier\"] = (\n", "    aggregated_sales[\"year\"].astype(str) + \" \" + aggregated_sales[\"month\"].astype(str)\n", ")\n", "\n", "aggregated_sales[\"net_quantity_sold\"] = np.where(\n", "    aggregated_sales[\"total_sold_quantity\"] - aggregated_sales[\"total_return_quantity\"] < 0,\n", "    0,\n", "    aggregated_sales[\"total_sold_quantity\"] - aggregated_sales[\"total_return_quantity\"],\n", ")\n", "aggregated_sales[\"net_gmv\"] = np.where(\n", "    aggregated_sales[\"total_gross_bill_amount\"] - aggregated_sales[\"total_return_amount\"] < 0,\n", "    0,\n", "    aggregated_sales[\"total_gross_bill_amount\"] - aggregated_sales[\"total_return_amount\"],\n", ")\n", "aggregated_sales = pd.merge(\n", "    aggregated_sales,\n", "    entity_vendor_base_df[[\"entity_vendor_id\", \"entity_type\"]].drop_duplicates(),\n", "    on=[\"entity_vendor_id\"],\n", "    how=\"left\",\n", ")\n", "aggregated_sales.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f941aaaf-befd-409e-b20d-8cca56c2fa64", "metadata": {}, "outputs": [], "source": ["# entity_vendor_base_df[['entity_vendor_id','entity_type']].drop_duplicates()"]}, {"cell_type": "markdown", "id": "e212fdc7-ccfa-45bc-a579-218e1eaa51e7", "metadata": {}, "source": ["# Sales"]}, {"cell_type": "code", "execution_count": null, "id": "29f28f8a-7b1b-4529-a2a2-e82a90a508f2", "metadata": {}, "outputs": [], "source": ["# Monthly Aggregates\n", "## 1. Overall\n", "\n", "# Aggregate at identifier\n", "mtd_sales = (\n", "    aggregated_sales.groupby([\"identifier\"])\n", "    .agg({\"net_quantity_sold\": \"sum\", \"net_gmv\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "\n", "# Yesterday Scheduled PO\n", "\n", "yesterday_sales = aggregated_sales[\n", "    (\n", "        pd.to_datetime(aggregated_sales[\"insert_ds_ist\"])\n", "        == pd.to_datetime(date.today() - timed<PERSON>ta(days=1))\n", "    )\n", "]\n", "yesterday_sales[\"identifier\"] = \"Yesterday - (t-1)\"\n", "yesterday_sales = (\n", "    yesterday_sales.groupby([\"identifier\"])\n", "    .agg({\"net_quantity_sold\": \"sum\", \"net_gmv\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "\n", "# L7 Scheduled PO\n", "l7_sales = aggregated_sales[\n", "    (\n", "        pd.to_datetime(aggregated_sales[\"insert_ds_ist\"])\n", "        >= pd.to_datetime(date.today() - <PERSON><PERSON><PERSON>(days=7))\n", "    )\n", "    & (\n", "        pd.to_datetime(aggregated_sales[\"insert_ds_ist\"])\n", "        < pd.to_datetime(date.today() - timed<PERSON>ta(days=0))\n", "    )\n", "]\n", "l7_sales[\"identifier\"] = \"last 7 days\"\n", "l7_sales = (\n", "    l7_sales.groupby([\"identifier\"])\n", "    .agg({\"net_quantity_sold\": \"sum\", \"net_gmv\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "\n", "print(mtd_sales.columns)\n", "print(yesterday_sales.columns)\n", "print(l7_sales.columns)\n", "\n", "mtd_sales = pd.concat([mtd_sales, yesterday_sales, l7_sales])\n", "mtd_sales[\"dummy_index\"] = 1\n", "\n", "sales_summary_df = mtd_sales.pivot_table(\n", "    index=[\"dummy_index\"],\n", "    columns=[\"identifier\"],\n", "    values=[\"net_quantity_sold\", \"net_gmv\"],\n", "    fill_value=0,\n", ").reset_index()\n", "\n", "\n", "sales_summary_df = sales_summary_df.drop(columns={\"dummy_index\"})\n", "sales_summary_df"]}, {"cell_type": "code", "execution_count": null, "id": "170434ec-279d-484b-8fbd-302f55c52796", "metadata": {}, "outputs": [], "source": ["sales_summary_df"]}, {"cell_type": "code", "execution_count": null, "id": "3b8c4a60-ed60-44fd-bee0-2d763848f728", "metadata": {}, "outputs": [], "source": ["# Monthly Aggregates\n", "## 1. Overall\n", "\n", "# Aggregate at identifier\n", "mtd_sales_type = (\n", "    aggregated_sales.groupby([\"identifier\", \"entity_type\"])\n", "    .agg({\"net_quantity_sold\": \"sum\", \"net_gmv\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "\n", "# Yesterday Scheduled PO\n", "\n", "yesterday_sales_type = aggregated_sales[\n", "    (\n", "        pd.to_datetime(aggregated_sales[\"insert_ds_ist\"])\n", "        == pd.to_datetime(date.today() - timed<PERSON>ta(days=1))\n", "    )\n", "]\n", "yesterday_sales_type[\"identifier\"] = \"Yesterday - (t-1)\"\n", "yesterday_sales_type = (\n", "    yesterday_sales_type.groupby([\"identifier\", \"entity_type\"])\n", "    .agg({\"net_quantity_sold\": \"sum\", \"net_gmv\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "\n", "# L7 Scheduled PO\n", "l7_sales_type = aggregated_sales[\n", "    (\n", "        pd.to_datetime(aggregated_sales[\"insert_ds_ist\"])\n", "        >= pd.to_datetime(date.today() - <PERSON><PERSON><PERSON>(days=7))\n", "    )\n", "    & (\n", "        pd.to_datetime(aggregated_sales[\"insert_ds_ist\"])\n", "        < pd.to_datetime(date.today() - timed<PERSON>ta(days=0))\n", "    )\n", "]\n", "l7_sales_type[\"identifier\"] = \"last 7 days\"\n", "l7_sales_type = (\n", "    l7_sales_type.groupby([\"identifier\", \"entity_type\"])\n", "    .agg({\"net_quantity_sold\": \"sum\", \"net_gmv\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "\n", "print(mtd_sales_type.columns)\n", "print(yesterday_sales_type.columns)\n", "print(l7_sales_type.columns)\n", "\n", "mtd_sales_type = pd.concat([mtd_sales_type, yesterday_sales_type, l7_sales_type])\n", "\n", "sales_type_summary_df = mtd_sales_type.pivot_table(\n", "    index=[\"entity_type\"],\n", "    columns=[\"identifier\"],\n", "    values=[\"net_quantity_sold\", \"net_gmv\"],\n", "    fill_value=0,\n", ").reset_index()\n", "\n", "sales_type_summary_df"]}, {"cell_type": "code", "execution_count": null, "id": "c5e6424d-1596-4f64-93f1-a65ae14e19e7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4ff5a7fc-9e4c-4952-8343-8c28eeadfcd3", "metadata": {}, "outputs": [], "source": ["# Monthly Aggregates\n", "## 1. Overall\n", "\n", "# Aggregate at identifier\n", "mtd_sales_entity = (\n", "    aggregated_sales.groupby([\"identifier\", \"entity_vendor_id\", \"vendor_name\"])\n", "    .agg({\"net_quantity_sold\": \"sum\", \"net_gmv\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "\n", "# Yesterday Scheduled PO\n", "\n", "yesterday_sales_entity = aggregated_sales[\n", "    (\n", "        pd.to_datetime(aggregated_sales[\"insert_ds_ist\"])\n", "        == pd.to_datetime(date.today() - timed<PERSON>ta(days=1))\n", "    )\n", "]\n", "yesterday_sales_entity[\"identifier\"] = \"Yesterday - (t-1)\"\n", "yesterday_sales_entity = (\n", "    yesterday_sales_entity.groupby([\"identifier\", \"entity_vendor_id\", \"vendor_name\"])\n", "    .agg({\"net_quantity_sold\": \"sum\", \"net_gmv\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "\n", "# L7 Scheduled PO\n", "l7_sales_entity = aggregated_sales[\n", "    (\n", "        pd.to_datetime(aggregated_sales[\"insert_ds_ist\"])\n", "        >= pd.to_datetime(date.today() - <PERSON><PERSON><PERSON>(days=7))\n", "    )\n", "    & (\n", "        pd.to_datetime(aggregated_sales[\"insert_ds_ist\"])\n", "        < pd.to_datetime(date.today() - timed<PERSON>ta(days=0))\n", "    )\n", "]\n", "l7_sales_entity[\"identifier\"] = \"last 7 days\"\n", "l7_sales_entity = (\n", "    l7_sales_entity.groupby([\"identifier\", \"entity_vendor_id\", \"vendor_name\"])\n", "    .agg({\"net_quantity_sold\": \"sum\", \"net_gmv\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "\n", "print(mtd_sales_entity.columns)\n", "print(yesterday_sales_entity.columns)\n", "print(l7_sales_entity.columns)\n", "\n", "mtd_sales_entity = pd.concat([mtd_sales_entity, yesterday_sales_entity, l7_sales_entity])\n", "\n", "sales_entity_summary_df = mtd_sales_entity.pivot_table(\n", "    index=[\"entity_vendor_id\", \"vendor_name\"],\n", "    columns=[\"identifier\"],\n", "    values=[\"net_quantity_sold\", \"net_gmv\"],\n", "    fill_value=0,\n", ").reset_index()\n", "\n", "\n", "sales_entity_summary_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "b7e71c90-2604-46c4-9b0e-63118c2fac11", "metadata": {}, "outputs": [], "source": ["# Outputs\n", "\n", "\n", "# Sales\n", "\n", "sheet_id = \"1MhYtIM3TGvQoNnC6ciRuKXAnoeEBqTLOh_pQ-uPUtWM\"\n", "output_sheet = \"sales_entity_summary_df\"\n", "pb.to_sheets(sales_entity_summary_df, sheet_id, output_sheet)\n", "\n", "\n", "sheet_id = \"1MhYtIM3TGvQoNnC6ciRuKXAnoeEBqTLOh_pQ-uPUtWM\"\n", "output_sheet = \"sales_type_summary_df\"\n", "pb.to_sheets(sales_type_summary_df, sheet_id, output_sheet)\n", "\n", "\n", "sheet_id = \"1MhYtIM3TGvQoNnC6ciRuKXAnoeEBqTLOh_pQ-uPUtWM\"\n", "output_sheet = \"sales_summary_df\"\n", "pb.to_sheets(sales_summary_df, sheet_id, output_sheet)\n", "\n", "\n", "# # POs - SSC VS Entity\n", "\n", "# sheet_id = \"1MhYtIM3TGvQoNnC6ciRuKXAnoeEBqTLOh_pQ-uPUtWM\"\n", "# output_sheet = \"po_com_summary_entity_type\"\n", "# pb.to_sheets(po_com_summary_entity_type, sheet_id, output_sheet)\n", "\n", "# sheet_id = \"1MhYtIM3TGvQoNnC6ciRuKXAnoeEBqTLOh_pQ-uPUtWM\"\n", "# output_sheet = \"po_com_summary\"\n", "# pb.to_sheets(po_com_summary, sheet_id, output_sheet)"]}, {"cell_type": "code", "execution_count": null, "id": "5bc2c179-1711-440b-b2ad-e0f2c815f0ca", "metadata": {}, "outputs": [], "source": ["del [\n", "    [\n", "        aggregated_sales,\n", "        entity_sales_df,\n", "        l7_sales,\n", "        l7_sales_entity,\n", "        l7_sales_type,\n", "        mtd_sales,\n", "        mtd_sales_entity,\n", "        mtd_sales_type,\n", "        outlet_mapping_df,\n", "        product_product_df,\n", "        sales_entity_summary_df,\n", "        sales_summary_df,\n", "        sales_type_summary_df,\n", "        yesterday_sales,\n", "        yesterday_sales_entity,\n", "        yesterday_sales_type,\n", "    ]\n", "]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "69f64d07-497c-42c9-8c7b-4cb38017d224", "metadata": {}, "outputs": [], "source": ["alldfs = [var for var in dir() if isinstance(eval(var), pd.core.frame.DataFrame)]\n", "print(alldfs)"]}, {"cell_type": "code", "execution_count": null, "id": "031e9e2a-7264-4d9e-914a-a24709ad16a1", "metadata": {}, "outputs": [], "source": ["# po_com_summary_entity_type, po_com_summary"]}, {"cell_type": "code", "execution_count": null, "id": "306f7547-31fa-479f-91e8-19eba523ab0a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "4e06cb4f-ba66-4aaf-b179-60d1787410b9", "metadata": {"tags": []}, "source": ["# PO Value + GRN Value"]}, {"cell_type": "code", "execution_count": null, "id": "c37e0ce6-8c0b-4dca-8e76-d9d62255cd9e", "metadata": {}, "outputs": [], "source": ["# Monthly Aggregates\n", "## 1. Overall\n", "## 2. Entity Type Level\n", "## 3. Enti<PERSON> Level"]}, {"cell_type": "code", "execution_count": null, "id": "07dcc711-7e49-407e-818c-525e4054f9d8", "metadata": {}, "outputs": [], "source": ["entity_po_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "47a6996e-42f4-4128-bae4-8ce296ae4bb7", "metadata": {}, "outputs": [], "source": ["entity_po_df[\"grn_value\"] = entity_po_df[\"grn_value\"].fillna(0)\n", "entity_po_df[\"po_amount\"] = entity_po_df[\"po_amount\"].astype(float).fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "44b50c56-53c5-4d6a-b79a-8839db8da24d", "metadata": {}, "outputs": [], "source": ["entity_po_df.groupby([\"issue_date_year\", \"issue_date_month\"]).agg(\n", "    {\"po_amount\": \"sum\", \"grn_value\": \"sum\"}\n", ").reset_index().sort_values(by=[\"issue_date_year\", \"issue_date_month\"], ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "e345996c-9457-4334-b1b6-8e5024a42dc4", "metadata": {}, "outputs": [], "source": ["entity_po_df[(pd.to_datetime(entity_po_df[\"grn_date_set\"]).isna() == False)].head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "bfc4c8e7-82ca-4c97-b84e-bd8aa9237c6c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4679a74e-a04c-4649-b555-0dc3089d1694", "metadata": {}, "outputs": [], "source": ["entity_po_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "a04f7b70-0ded-4023-a8dd-f580f21855ce", "metadata": {}, "outputs": [], "source": ["# Monthly Aggregates\n", "## 1. Overall\n", "\n", "# Aggregate at year and month and po_state\n", "\n", "entity_po_df[[\"po_amount\", \"grn_value\"]] = entity_po_df[[\"po_amount\", \"grn_value\"]].astype(float)\n", "\n", "mtd_aggregate_po = (\n", "    entity_po_df.groupby([\"issue_date_year\", \"issue_date_month\"])\n", "    .agg({\"po_amount\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"issue_date_year\", \"issue_date_month\"], ascending=False)\n", ")\n", "mtd_aggregate_po[\"identifier\"] = (\n", "    mtd_aggregate_po[\"issue_date_year\"].astype(str)\n", "    + \" \"\n", "    + mtd_aggregate_po[\"issue_date_month\"].astype(str)\n", ")\n", "mtd_aggregate_po = mtd_aggregate_po.drop(columns={\"issue_date_year\", \"issue_date_month\"})\n", "mtd_aggregate_po = mtd_aggregate_po.rename(columns={\"po_amount\": \"po_amount_raised\"})\n", "\n", "\n", "mtd_aggregate_grn = entity_po_df[(pd.to_datetime(entity_po_df[\"grn_date_set\"]).isna() == False)]\n", "mtd_aggregate_grn[\"grn_date_set\"] = pd.to_datetime(mtd_aggregate_grn[\"grn_date_set\"])\n", "mtd_aggregate_grn[\"grn_date_year\"] = mtd_aggregate_grn[\"grn_date_set\"].dt.year\n", "mtd_aggregate_grn[\"grn_date_month\"] = mtd_aggregate_grn[\"grn_date_set\"].dt.month\n", "mtd_aggregate_grn[\"grn_value\"] = mtd_aggregate_grn[\"grn_value\"].fillna(0)\n", "\n", "mtd_aggregate_grn[\"identifier\"] = (\n", "    mtd_aggregate_grn[\"grn_date_year\"].astype(str)\n", "    + \" \"\n", "    + mtd_aggregate_grn[\"grn_date_month\"].astype(str)\n", ")\n", "mtd_aggregate_grn = (\n", "    mtd_aggregate_grn.groupby([\"identifier\"])\n", "    .agg({\"grn_value\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"identifier\"], ascending=False)\n", ")\n", "mtd_aggregate_grn = mtd_aggregate_grn[[\"grn_value\", \"identifier\"]]\n", "\n", "mtd_aggregate_po = pd.merge(mtd_aggregate_po, mtd_aggregate_grn, on=[\"identifier\"], how=\"outer\")\n", "\n", "\n", "# Yesterday Scheduled PO\n", "entity_po_df[\"issue_date_dt\"] = pd.to_datetime(entity_po_df[\"issue_date_dt\"])\n", "yesterday_po = entity_po_df[\n", "    (\n", "        pd.to_datetime(entity_po_df[\"issue_date_dt\"])\n", "        == pd.to_datetime(date.today() - timed<PERSON>ta(days=1))\n", "    )\n", "]\n", "yesterday_po = (\n", "    yesterday_po.groupby([\"issue_date_dt\"])\n", "    .agg({\"po_amount\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"issue_date_dt\"], ascending=False)\n", ")\n", "yesterday_po[\"identifier\"] = \"Yesterday - (t-1)\"\n", "yesterday_po = yesterday_po.drop(columns={\"issue_date_dt\"}).rename(\n", "    columns={\"po_amount\": \"po_amount_raised\"}\n", ")\n", "\n", "# Yesterday GRN\n", "entity_po_df[\"grn_date_set\"] = pd.to_datetime(entity_po_df[\"grn_ts\"]).dt.date\n", "yesterday_grn = entity_po_df[\n", "    (\n", "        pd.to_datetime(entity_po_df[\"grn_date_set\"])\n", "        == pd.to_datetime(date.today() - timed<PERSON>ta(days=1))\n", "    )\n", "]\n", "yesterday_grn = (\n", "    yesterday_grn.groupby([\"grn_date_set\"])\n", "    .agg({\"grn_value\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"grn_date_set\"], ascending=False)\n", ")\n", "yesterday_grn[\"identifier\"] = \"Yesterday - (t-1)\"\n", "yesterday_grn = yesterday_grn.drop(columns={\"grn_date_set\"})\n", "\n", "yesterday_po = pd.merge(yesterday_po, yesterday_grn, on=[\"identifier\"], how=\"outer\")\n", "yesterday_po = yesterday_po[[\"po_amount_raised\", \"grn_value\", \"identifier\"]]\n", "\n", "# L7 Scheduled PO\n", "l7_po = entity_po_df[\n", "    (\n", "        pd.to_datetime(entity_po_df[\"issue_date_dt\"])\n", "        >= pd.to_datetime(date.today() - <PERSON><PERSON><PERSON>(days=7))\n", "    )\n", "    & (\n", "        pd.to_datetime(entity_po_df[\"issue_date_dt\"])\n", "        < pd.to_datetime(date.today() - timed<PERSON>ta(days=0))\n", "    )\n", "]\n", "l7_po[\"identifier\"] = \"last 7 days\"\n", "l7_po = (\n", "    l7_po.groupby([\"identifier\"])\n", "    .agg({\"po_amount\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"identifier\"], ascending=False)\n", ")\n", "l7_po = l7_po.rename(columns={\"po_amount\": \"po_amount_raised\"})\n", "l7_po = l7_po[[\"po_amount_raised\", \"identifier\"]]\n", "\n", "# L7 GRN\n", "l7_grn = entity_po_df[\n", "    (\n", "        pd.to_datetime(entity_po_df[\"grn_date_set\"])\n", "        >= pd.to_datetime(date.today() - <PERSON><PERSON><PERSON>(days=7))\n", "    )\n", "    & (\n", "        pd.to_datetime(entity_po_df[\"grn_date_set\"])\n", "        < pd.to_datetime(date.today() - timed<PERSON>ta(days=0))\n", "    )\n", "]\n", "l7_grn[\"identifier\"] = \"last 7 days\"\n", "l7_grn = (\n", "    l7_grn.groupby([\"identifier\"])\n", "    .agg({\"grn_value\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"identifier\"], ascending=False)\n", ")\n", "l7_grn = l7_grn[[\"grn_value\", \"identifier\"]]\n", "\n", "l7_po = pd.merge(l7_po, l7_grn, on=[\"identifier\"], how=\"outer\")\n", "l7_po = l7_po[[\"po_amount_raised\", \"grn_value\", \"identifier\"]]\n", "\n", "mtd_aggregate_po = pd.concat([mtd_aggregate_po, yesterday_po, l7_po])\n", "\n", "mtd_aggregate_po[\"index_key\"] = \"1\"\n", "\n", "po_summary_df = mtd_aggregate_po.pivot_table(\n", "    index=[\"index_key\"],\n", "    columns=[\"identifier\"],\n", "    values=[\"po_amount_raised\", \"grn_value\"],\n", "    fill_value=0,\n", ").reset_index()\n", "\n", "po_summary_df = po_summary_df.drop(columns={\"index_key\"})\n", "\n", "# y_po_summary_df = yesterday_po.pivot_table(index=['po_state']\n", "#                     , columns=['identifier']\n", "#                     , values=['grn_value','po_amount_raised'], fill_value = 0).reset_index()\n", "\n", "# po_summary_df = pd.merge(po_summary_df, y_po_summary_df, on = ['po_state'], how = 'outer')\n", "\n", "\n", "po_summary_df"]}, {"cell_type": "code", "execution_count": null, "id": "83060050-44ed-4027-9eb2-a92e6a48c73c", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1MhYtIM3TGvQoNnC6ciRuKXAnoeEBqTLOh_pQ-uPUtWM\"\n", "output_sheet = \"po_amount_df\"\n", "pb.to_sheets(po_summary_df, sheet_id, output_sheet)"]}, {"cell_type": "code", "execution_count": null, "id": "25a53023-282a-4610-8276-0f1135d1f354", "metadata": {}, "outputs": [], "source": ["del [[l7_grn, l7_po, mtd_aggregate_grn, po_summary_df, yesterday_grn, yesterday_po]]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "268a32bc-de18-44a7-9b1a-93fe437ca16e", "metadata": {}, "outputs": [], "source": ["alldfs = [var for var in dir() if isinstance(eval(var), pd.core.frame.DataFrame)]\n", "print(alldfs)"]}, {"cell_type": "code", "execution_count": null, "id": "4093d54c-a93f-4b27-b803-38e6b01b6619", "metadata": {}, "outputs": [], "source": ["## 2. Entity Type Level\n", "\n", "# Aggregate at entity type, year and month and po_state\n", "mtd_aggregate_entity_type_po = (\n", "    entity_po_df.groupby([\"entity_type\", \"issue_date_year\", \"issue_date_month\"])\n", "    .agg({\"po_amount\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"issue_date_year\", \"issue_date_month\"], ascending=False)\n", ")\n", "mtd_aggregate_entity_type_po[\"identifier\"] = (\n", "    mtd_aggregate_entity_type_po[\"issue_date_year\"].astype(str)\n", "    + \" \"\n", "    + mtd_aggregate_entity_type_po[\"issue_date_month\"].astype(str)\n", ")\n", "mtd_aggregate_entity_type_po = mtd_aggregate_entity_type_po.drop(\n", "    columns={\"issue_date_year\", \"issue_date_month\"}\n", ").rename(columns={\"po_amount\": \"po_amount_raised\"})\n", "\n", "\n", "# MTD GRN\n", "\n", "mtd_aggregate_entity_grn = entity_po_df[\n", "    (pd.to_datetime(entity_po_df[\"grn_date_set\"]).isna() == False)\n", "]\n", "mtd_aggregate_entity_grn[\"grn_date_set\"] = pd.to_datetime(mtd_aggregate_entity_grn[\"grn_date_set\"])\n", "mtd_aggregate_entity_grn[\"grn_date_year\"] = mtd_aggregate_entity_grn[\"grn_date_set\"].dt.year\n", "mtd_aggregate_entity_grn[\"grn_date_month\"] = mtd_aggregate_entity_grn[\"grn_date_set\"].dt.month\n", "\n", "mtd_aggregate_entity_grn[\"identifier\"] = (\n", "    mtd_aggregate_entity_grn[\"grn_date_year\"].astype(str)\n", "    + \" \"\n", "    + mtd_aggregate_entity_grn[\"grn_date_month\"].astype(str)\n", ")\n", "mtd_aggregate_entity_grn = (\n", "    mtd_aggregate_entity_grn.groupby([\"vendor_name\", \"identifier\"])\n", "    .agg({\"grn_value\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"vendor_name\", \"identifier\"], ascending=False)\n", ")\n", "mtd_aggregate_entity_grn = mtd_aggregate_entity_grn[[\"vendor_name\", \"grn_value\", \"identifier\"]]\n", "\n", "mtd_aggregate_entity_po = pd.merge(\n", "    mtd_aggregate_entity_po,\n", "    mtd_aggregate_entity_grn,\n", "    on=[\"vendor_name\", \"identifier\"],\n", "    how=\"outer\",\n", ")\n", "\n", "\n", "# Yesterday Scheduled PO\n", "\n", "yesterday_entity_type_po = entity_po_df[\n", "    (\n", "        pd.to_datetime(entity_po_df[\"issue_date_dt\"])\n", "        == pd.to_datetime(date.today() - timed<PERSON>ta(days=1))\n", "    )\n", "]\n", "yesterday_entity_type_po = (\n", "    yesterday_entity_type_po.groupby([\"entity_type\", \"issue_date_dt\"])\n", "    .agg({\"po_amount\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"issue_date_dt\"], ascending=False)\n", ")\n", "yesterday_entity_type_po[\"identifier\"] = \"Yesterday - (t-1)\"\n", "yesterday_entity_type_po = yesterday_entity_type_po.drop(columns={\"issue_date_dt\"}).rename(\n", "    columns={\"po_amount\": \"po_amount_raised\"}\n", ")\n", "\n", "\n", "# Yesterday GRN\n", "\n", "yesterday_entity_type_grn = entity_po_df[\n", "    (\n", "        pd.to_datetime(entity_po_df[\"grn_date_set\"])\n", "        == pd.to_datetime(date.today() - timed<PERSON>ta(days=1))\n", "    )\n", "]\n", "yesterday_entity_type_grn = (\n", "    yesterday_entity_type_grn.groupby([\"entity_type\", \"grn_date_set\"])\n", "    .agg({\"grn_value\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"grn_date_set\"], ascending=False)\n", ")\n", "yesterday_entity_type_grn[\"identifier\"] = \"Yesterday - (t-1)\"\n", "yesterday_entity_type_grn = yesterday_entity_type_grn.drop(columns={\"grn_date_set\"})\n", "\n", "yesterday_entity_type_po = pd.merge(\n", "    yesterday_entity_type_po,\n", "    yesterday_entity_type_grn,\n", "    on=[\"identifier\", \"entity_type\"],\n", "    how=\"outer\",\n", ")\n", "\n", "# L7 Scheduled PO\n", "\n", "l7_entity_type_po = entity_po_df[\n", "    (\n", "        pd.to_datetime(entity_po_df[\"issue_date_dt\"])\n", "        >= pd.to_datetime(date.today() - <PERSON><PERSON><PERSON>(days=7))\n", "    )\n", "    & (\n", "        pd.to_datetime(entity_po_df[\"issue_date_dt\"])\n", "        < pd.to_datetime(date.today() - timed<PERSON>ta(days=0))\n", "    )\n", "]\n", "l7_entity_type_po[\"identifier\"] = \"last 7 days\"\n", "l7_entity_type_po = (\n", "    l7_entity_type_po.groupby([\"entity_type\", \"identifier\"])\n", "    .agg({\"po_amount\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"entity_type\", \"identifier\"], ascending=False)\n", ")\n", "l7_entity_type_po = l7_entity_type_po.rename(columns={\"po_amount\": \"po_amount_raised\"})\n", "l7_entity_type_po = l7_entity_type_po[[\"entity_type\", \"po_amount_raised\", \"identifier\"]]\n", "\n", "\n", "# L7 GRN\n", "l7_entity_type_grn = entity_po_df[\n", "    (\n", "        pd.to_datetime(entity_po_df[\"grn_date_set\"])\n", "        >= pd.to_datetime(date.today() - <PERSON><PERSON><PERSON>(days=7))\n", "    )\n", "    & (\n", "        pd.to_datetime(entity_po_df[\"grn_date_set\"])\n", "        < pd.to_datetime(date.today() - timed<PERSON>ta(days=0))\n", "    )\n", "]\n", "l7_entity_type_grn[\"identifier\"] = \"last 7 days\"\n", "l7_entity_type_grn = (\n", "    l7_entity_type_grn.groupby([\"entity_type\", \"identifier\"])\n", "    .agg({\"grn_value\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"entity_type\", \"identifier\"], ascending=False)\n", ")\n", "l7_entity_type_grn = l7_entity_type_grn[[\"entity_type\", \"grn_value\", \"identifier\"]]\n", "\n", "l7_entity_type_po = pd.merge(\n", "    l7_entity_type_po, l7_entity_type_grn, on=[\"entity_type\", \"identifier\"], how=\"outer\"\n", ")\n", "l7_entity_type_po = l7_entity_type_po[\n", "    [\"entity_type\", \"po_amount_raised\", \"grn_value\", \"identifier\"]\n", "]\n", "\n", "\n", "yesterday_entity_type_po = yesterday_entity_type_po[\n", "    [\"entity_type\", \"po_amount_raised\", \"grn_value\", \"identifier\"]\n", "]\n", "\n", "mtd_aggregate_entity_type_po = pd.concat(\n", "    [mtd_aggregate_entity_type_po, yesterday_entity_type_po, l7_entity_type_po]\n", ")\n", "\n", "\n", "po_summary_entity_type_df = mtd_aggregate_entity_type_po.pivot_table(\n", "    index=[\"entity_type\"],\n", "    columns=[\"identifier\"],\n", "    values=[\"grn_value\", \"po_amount_raised\"],\n", "    fill_value=0,\n", ").reset_index()\n", "\n", "po_summary_entity_type_df"]}, {"cell_type": "code", "execution_count": null, "id": "56e2fb29-9942-47f7-ae1f-f179eaca9fa9", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1MhYtIM3TGvQoNnC6ciRuKXAnoeEBqTLOh_pQ-uPUtWM\"\n", "output_sheet = \"po_amount_entity_type_df\"\n", "pb.to_sheets(po_summary_entity_type_df, sheet_id, output_sheet)"]}, {"cell_type": "code", "execution_count": null, "id": "bac3c9d3-45d2-4167-9a71-515514a16eeb", "metadata": {}, "outputs": [], "source": ["del [\n", "    [\n", "        l7_entity_type_grn,\n", "        l7_entity_type_po,\n", "        mtd_aggregate_entity_grn,\n", "        mtd_aggregate_entity_po,\n", "        mtd_aggregate_entity_type_po,\n", "        mtd_aggregate_po,\n", "        po_summary_entity_type_df,\n", "        yesterday_entity_type_grn,\n", "        yesterday_entity_type_po,\n", "    ]\n", "]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "14008b4c-c059-4236-8ff4-4e3d19359b8d", "metadata": {}, "outputs": [], "source": ["alldfs = [var for var in dir() if isinstance(eval(var), pd.core.frame.DataFrame)]\n", "print(alldfs)"]}, {"cell_type": "code", "execution_count": null, "id": "4b7bfab2-aad7-4a07-89b9-ad9d9e7fd294", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8256b538-4e74-421e-bdc8-f8a8faab3865", "metadata": {}, "outputs": [], "source": ["## 3. Enti<PERSON> Level\n", "\n", "# Aggregate at entity, year and month and po_state\n", "mtd_aggregate_entity_po = (\n", "    entity_po_df.groupby([\"vendor_name\", \"issue_date_year\", \"issue_date_month\"])\n", "    .agg({\"expected_po_qty\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"issue_date_year\", \"issue_date_month\"], ascending=False)\n", ")\n", "mtd_aggregate_entity_po[\"identifier\"] = (\n", "    mtd_aggregate_entity_po[\"issue_date_year\"].astype(str)\n", "    + \" \"\n", "    + mtd_aggregate_entity_po[\"issue_date_month\"].astype(str)\n", ")\n", "mtd_aggregate_entity_po = mtd_aggregate_entity_po.drop(\n", "    columns={\"issue_date_year\", \"issue_date_month\"}\n", ").rename(columns={\"expected_po_qty\": \"po_quantity_raised\"})\n", "\n", "\n", "# MTD GRN\n", "\n", "mtd_aggregate_entity_grn = entity_po_df[\n", "    (pd.to_datetime(entity_po_df[\"grn_date_set\"]).isna() == False)\n", "]\n", "mtd_aggregate_entity_grn[\"grn_date_set\"] = pd.to_datetime(mtd_aggregate_entity_grn[\"grn_date_set\"])\n", "mtd_aggregate_entity_grn[\"grn_date_year\"] = mtd_aggregate_entity_grn[\"grn_date_set\"].dt.year\n", "mtd_aggregate_entity_grn[\"grn_date_month\"] = mtd_aggregate_entity_grn[\"grn_date_set\"].dt.month\n", "\n", "mtd_aggregate_entity_grn[\"identifier\"] = (\n", "    mtd_aggregate_entity_grn[\"grn_date_year\"].astype(str)\n", "    + \" \"\n", "    + mtd_aggregate_entity_grn[\"grn_date_month\"].astype(str)\n", ")\n", "mtd_aggregate_entity_grn = (\n", "    mtd_aggregate_entity_grn.groupby([\"vendor_name\", \"identifier\"])\n", "    .agg({\"grn_qty\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"vendor_name\", \"identifier\"], ascending=False)\n", ")\n", "mtd_aggregate_entity_grn = mtd_aggregate_entity_grn[[\"vendor_name\", \"grn_qty\", \"identifier\"]]\n", "\n", "mtd_aggregate_entity_po = pd.merge(\n", "    mtd_aggregate_entity_po,\n", "    mtd_aggregate_entity_grn,\n", "    on=[\"vendor_name\", \"identifier\"],\n", "    how=\"outer\",\n", ")\n", "\n", "\n", "# Yesterday Scheduled PO\n", "\n", "yesterday_entity_po = entity_po_df[\n", "    (\n", "        pd.to_datetime(entity_po_df[\"issue_date_dt\"])\n", "        == pd.to_datetime(date.today() - timed<PERSON>ta(days=1))\n", "    )\n", "]\n", "yesterday_entity_po = (\n", "    yesterday_entity_po.groupby([\"vendor_name\", \"issue_date_dt\"])\n", "    .agg({\"expected_po_qty\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"issue_date_dt\"], ascending=False)\n", ")\n", "yesterday_entity_po[\"identifier\"] = \"Yesterday - (t-1)\"\n", "yesterday_entity_po = yesterday_entity_po.drop(columns={\"issue_date_dt\"}).rename(\n", "    columns={\"expected_po_qty\": \"po_quantity_raised\"}\n", ")\n", "\n", "# Yesterday GRN\n", "\n", "yesterday_entity_grn = entity_po_df[\n", "    (\n", "        pd.to_datetime(entity_po_df[\"grn_date_set\"])\n", "        == pd.to_datetime(date.today() - timed<PERSON>ta(days=1))\n", "    )\n", "]\n", "yesterday_entity_grn = (\n", "    yesterday_entity_grn.groupby([\"vendor_name\", \"grn_date_set\"])\n", "    .agg({\"grn_qty\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"grn_date_set\"], ascending=False)\n", ")\n", "yesterday_entity_grn[\"identifier\"] = \"Yesterday - (t-1)\"\n", "yesterday_entity_grn = yesterday_entity_grn.drop(columns={\"grn_date_set\"})\n", "\n", "yesterday_entity_po = pd.merge(\n", "    yesterday_entity_po,\n", "    yesterday_entity_grn,\n", "    on=[\"identifier\", \"vendor_name\"],\n", "    how=\"outer\",\n", ")\n", "yesterday_entity_po = yesterday_entity_po[\n", "    [\"vendor_name\", \"po_quantity_raised\", \"grn_qty\", \"identifier\"]\n", "]\n", "\n", "# L7 Scheduled PO\n", "\n", "l7_entity_po = entity_po_df[\n", "    (\n", "        pd.to_datetime(entity_po_df[\"issue_date_dt\"])\n", "        >= pd.to_datetime(date.today() - <PERSON><PERSON><PERSON>(days=7))\n", "    )\n", "    & (\n", "        pd.to_datetime(entity_po_df[\"issue_date_dt\"])\n", "        < pd.to_datetime(date.today() - timed<PERSON>ta(days=0))\n", "    )\n", "]\n", "l7_entity_po[\"identifier\"] = \"last 7 days\"\n", "l7_entity_po = (\n", "    l7_entity_po.groupby([\"vendor_name\", \"identifier\"])\n", "    .agg({\"expected_po_qty\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"vendor_name\", \"identifier\"], ascending=False)\n", ")\n", "l7_entity_po = l7_entity_po.rename(columns={\"expected_po_qty\": \"po_quantity_raised\"})\n", "l7_entity_po = l7_entity_po[[\"vendor_name\", \"po_quantity_raised\", \"identifier\"]]\n", "\n", "# L7 GRN\n", "\n", "l7_entity_grn = entity_po_df[\n", "    (\n", "        pd.to_datetime(entity_po_df[\"grn_date_set\"])\n", "        >= pd.to_datetime(date.today() - <PERSON><PERSON><PERSON>(days=7))\n", "    )\n", "    & (\n", "        pd.to_datetime(entity_po_df[\"grn_date_set\"])\n", "        < pd.to_datetime(date.today() - timed<PERSON>ta(days=0))\n", "    )\n", "]\n", "l7_entity_grn[\"identifier\"] = \"last 7 days\"\n", "l7_entity_grn = (\n", "    l7_entity_grn.groupby([\"vendor_name\", \"identifier\"])\n", "    .agg({\"grn_qty\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"vendor_name\", \"identifier\"], ascending=False)\n", ")\n", "l7_entity_grn = l7_entity_grn[[\"vendor_name\", \"grn_qty\", \"identifier\"]]\n", "\n", "l7_entity_po = pd.merge(l7_entity_po, l7_entity_grn, on=[\"vendor_name\", \"identifier\"], how=\"outer\")\n", "l7_entity_po = l7_entity_po[[\"vendor_name\", \"po_quantity_raised\", \"grn_qty\", \"identifier\"]]\n", "\n", "mtd_aggregate_entity_po = pd.concat([mtd_aggregate_entity_po, yesterday_entity_po, l7_entity_po])\n", "\n", "po_summary_entity_df = mtd_aggregate_entity_po.pivot_table(\n", "    index=[\"vendor_name\"],\n", "    columns=[\"identifier\"],\n", "    values=[\"grn_qty\", \"po_quantity_raised\"],\n", "    fill_value=0,\n", ").reset_index()\n", "po_summary_entity_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "dd8c979a-32d0-4e57-aac1-c0555494a665", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1MhYtIM3TGvQoNnC6ciRuKXAnoeEBqTLOh_pQ-uPUtWM\"\n", "output_sheet = \"po_amount_entity_df\"\n", "pb.to_sheets(po_summary_entity_df, sheet_id, output_sheet)"]}, {"cell_type": "code", "execution_count": null, "id": "cb7b37ad-8049-419c-91be-c8825495fcac", "metadata": {}, "outputs": [], "source": ["del [\n", "    [\n", "        l7_entity_grn,\n", "        l7_entity_po,\n", "        mtd_aggregate_entity_grn,\n", "        mtd_aggregate_entity_po,\n", "        po_summary_entity_df,\n", "        yesterday_entity_grn,\n", "        yesterday_entity_po,\n", "    ]\n", "]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "4c845e3b-e45e-4c13-a81d-487d8d44007c", "metadata": {}, "outputs": [], "source": ["alldfs = [var for var in dir() if isinstance(eval(var), pd.core.frame.DataFrame)]\n", "print(alldfs)"]}, {"cell_type": "code", "execution_count": null, "id": "787b71e3-63b6-48dc-ad04-7de19ce1a4be", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d6246531-c48e-4ede-96e0-e9352ad08fea", "metadata": {}, "outputs": [], "source": ["entity_transfer_query = f\"\"\"\n", "\n", "select\n", "    date(created_at) as date, outlet_id as sender_outlet_id, merchant_outlet_id as receiving_outlet_id, destination_entity_vendor_id as entity_vendor_id, SUM(billed_quantity) as billed_quantity, \n", "    SUM(inward_quantity) as inward_quantity\n", "FROM\n", "(\n", "    select \n", "        base.sto_id, base.outlet_id, base.merchant_outlet_id, destination_entity_vendor_id,\n", "        base.created_at + interval '330' Minute as created_at, \n", "        item_id, billed_quantity, inward_quantity\n", "    from \n", "        lake_view_ims.ims_sto_details base\n", "    inner join lake_view_po.sto po_sto\n", "        on po_sto.id = base.sto_id and base.outlet_id = po_sto.outlet_id\n", "    left join \n", "        lake_view_ims.ims_sto_item item\n", "        on base.sto_id = item.sto_id\n", "    where \n", "        po_sto.destination_entity_vendor_id in (select distinct entity_vendor_id from lake_retail.outlet_entity_vendor_mapping where entity_vendor_type_id = 5 and active = 1)\n", "        and po_sto.created_at >= date('2021-12-01')\n", "        and base.created_at >= date('2021-12-01')\n", ")\n", "GROUP BY \n", "    1,2,3,4\n", "\"\"\"\n", "entity_transfer_df = read_sql_query(entity_transfer_query, CON_PRESTO)\n", "entity_transfer_df = pd.merge(\n", "    entity_transfer_df,\n", "    entity_vendor_base_df[[\"entity_vendor_id\", \"vendor_name\", \"entity_type\"]],\n", "    on=[\"entity_vendor_id\"],\n", "    how=\"left\",\n", ")\n", "entity_transfer_df[\"year\"] = pd.to_datetime(entity_transfer_df[\"date\"]).dt.year\n", "entity_transfer_df[\"month\"] = pd.to_datetime(entity_transfer_df[\"date\"]).dt.month"]}, {"cell_type": "code", "execution_count": null, "id": "01ebbcac-1532-4084-a04b-55d0b73f896b", "metadata": {}, "outputs": [], "source": ["entity_transfer_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f369452c-9ddb-429e-9ea2-46ea793d5431", "metadata": {}, "outputs": [], "source": ["# 1. Overall Transfers\n", "# 2. Entity Type Transfers\n", "# 3. Entity Transfers"]}, {"cell_type": "code", "execution_count": null, "id": "01c26aa2-6a6a-4c45-8210-2dbe3eaacab6", "metadata": {"tags": []}, "outputs": [], "source": ["# 1. Overall Transfers\n", "\n", "mtd = (\n", "    entity_transfer_df.groupby([\"year\", \"month\"])\n", "    .agg({\"inward_quantity\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"year\", \"month\"], ascending=False)\n", ")\n", "mtd[\"identifier\"] = mtd[\"year\"].astype(str) + \" \" + mtd[\"month\"].astype(str)\n", "mtd = mtd.drop(columns={\"month\", \"year\"})\n", "mtd = mtd[[\"identifier\", \"inward_quantity\"]]\n", "\n", "l1 = entity_transfer_df[\n", "    (pd.to_datetime(entity_transfer_df[\"date\"]) == pd.to_datetime(date.today() - timedelta(days=1)))\n", "]\n", "l1[\"identifier\"] = \"Yesterday - (t-1)\"\n", "l1 = (\n", "    l1.groupby([\"identifier\"])\n", "    .agg({\"inward_quantity\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"identifier\"], ascending=False)\n", ")\n", "\n", "l7 = entity_transfer_df[\n", "    (pd.to_datetime(entity_transfer_df[\"date\"]) >= pd.to_datetime(date.today() - timed<PERSON>ta(days=7)))\n", "    & (\n", "        pd.to_datetime(entity_transfer_df[\"date\"])\n", "        < pd.to_datetime(date.today() - timed<PERSON>ta(days=0))\n", "    )\n", "]\n", "l7[\"identifier\"] = \"last 7 days\"\n", "l7 = (\n", "    l7.groupby([\"identifier\"])\n", "    .agg({\"inward_quantity\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"identifier\"], ascending=False)\n", ")\n", "\n", "mtd = pd.concat([mtd, l7, l1])\n", "\n", "mtd[\"index_key\"] = \"1\"\n", "\n", "mtd_final_transfers = mtd.pivot_table(\n", "    index=[\"index_key\"],\n", "    columns=[\"identifier\"],\n", "    values=[\"inward_quantity\"],\n", "    fill_value=0,\n", ").reset_index()\n", "\n", "mtd_final_transfers = mtd_final_transfers.drop(columns={\"index_key\"})\n", "\n", "mtd_final_transfers.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "05549364-f93d-4a6a-a152-f770c2d5844f", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1MhYtIM3TGvQoNnC6ciRuKXAnoeEBqTLOh_pQ-uPUtWM\"\n", "output_sheet = \"mtd_final_transfers\"\n", "pb.to_sheets(mtd_final_transfers, sheet_id, output_sheet)"]}, {"cell_type": "code", "execution_count": null, "id": "5e265b62-07bc-489c-866b-917e35b742d8", "metadata": {}, "outputs": [], "source": ["del [[l1, l7, mtd, mtd_final_transfers]]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "ac0f0412-5ca9-4653-9c82-8a722c8da0e7", "metadata": {}, "outputs": [], "source": ["alldfs = [var for var in dir() if isinstance(eval(var), pd.core.frame.DataFrame)]\n", "print(alldfs)"]}, {"cell_type": "code", "execution_count": null, "id": "c8e79d89-ad2c-4eda-84d4-0f684212923e", "metadata": {}, "outputs": [], "source": ["# 1. Overall Transfers\n", "\n", "mtd_entity_type = (\n", "    entity_transfer_df.groupby([\"entity_type\", \"year\", \"month\"])\n", "    .agg({\"inward_quantity\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"year\", \"month\"], ascending=False)\n", ")\n", "mtd_entity_type[\"identifier\"] = (\n", "    mtd_entity_type[\"year\"].astype(str) + \" \" + mtd_entity_type[\"month\"].astype(str)\n", ")\n", "mtd_entity_type = mtd_entity_type.drop(columns={\"month\", \"year\"})\n", "mtd_entity_type = mtd_entity_type[[\"entity_type\", \"identifier\", \"inward_quantity\"]]\n", "\n", "l1_entity_type = entity_transfer_df[\n", "    (pd.to_datetime(entity_transfer_df[\"date\"]) == pd.to_datetime(date.today() - timedelta(days=1)))\n", "]\n", "l1_entity_type[\"identifier\"] = \"Yesterday - (t-1)\"\n", "l1_entity_type = (\n", "    l1_entity_type.groupby([\"entity_type\", \"identifier\"])\n", "    .agg({\"inward_quantity\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"identifier\"], ascending=False)\n", ")\n", "\n", "l7_entity_type = entity_transfer_df[\n", "    (pd.to_datetime(entity_transfer_df[\"date\"]) >= pd.to_datetime(date.today() - timed<PERSON>ta(days=7)))\n", "    & (\n", "        pd.to_datetime(entity_transfer_df[\"date\"])\n", "        < pd.to_datetime(date.today() - timed<PERSON>ta(days=0))\n", "    )\n", "]\n", "l7_entity_type[\"identifier\"] = \"last 7 days\"\n", "l7_entity_type = (\n", "    l7_entity_type.groupby([\"entity_type\", \"identifier\"])\n", "    .agg({\"inward_quantity\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"identifier\"], ascending=False)\n", ")\n", "\n", "mtd_entity_type = pd.concat([mtd_entity_type, l7_entity_type, l1_entity_type])\n", "\n", "\n", "mtd_final_transfers_entity_type = mtd_entity_type.pivot_table(\n", "    index=[\"entity_type\"],\n", "    columns=[\"identifier\"],\n", "    values=[\"inward_quantity\"],\n", "    fill_value=0,\n", ").reset_index()\n", "\n", "\n", "mtd_final_transfers_entity_type.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "c2b1d9eb-cf90-4d35-8377-046e11e6a1bd", "metadata": {}, "outputs": [], "source": ["# Transfers -\n", "\n", "sheet_id = \"1MhYtIM3TGvQoNnC6ciRuKXAnoeEBqTLOh_pQ-uPUtWM\"\n", "output_sheet = \"mtd_final_transfers_entity_type\"\n", "pb.to_sheets(mtd_final_transfers_entity_type, sheet_id, output_sheet)"]}, {"cell_type": "code", "execution_count": null, "id": "8a65deb8-ee6a-463f-aa68-602ce74c5a87", "metadata": {}, "outputs": [], "source": ["del [[l1_entity_type, l7_entity_type, mtd_entity_type, mtd_final_transfers_entity_type]]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "cee0056e-cdb2-46ce-94d9-5942f20cdd61", "metadata": {}, "outputs": [], "source": ["alldfs = [var for var in dir() if isinstance(eval(var), pd.core.frame.DataFrame)]\n", "print(alldfs)"]}, {"cell_type": "code", "execution_count": null, "id": "42297631-2f0b-4684-b931-b1c54b532688", "metadata": {}, "outputs": [], "source": ["# 1. Overall Transfers\n", "\n", "mtd_vendor_name = (\n", "    entity_transfer_df.groupby([\"vendor_name\", \"year\", \"month\"])\n", "    .agg({\"inward_quantity\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"year\", \"month\"], ascending=False)\n", ")\n", "mtd_vendor_name[\"identifier\"] = (\n", "    mtd_vendor_name[\"year\"].astype(str) + \" \" + mtd_vendor_name[\"month\"].astype(str)\n", ")\n", "mtd_vendor_name = mtd_vendor_name.drop(columns={\"month\", \"year\"})\n", "mtd_vendor_name = mtd_vendor_name[[\"vendor_name\", \"identifier\", \"inward_quantity\"]]\n", "\n", "l1_vendor_name = entity_transfer_df[\n", "    (pd.to_datetime(entity_transfer_df[\"date\"]) == pd.to_datetime(date.today() - timedelta(days=1)))\n", "]\n", "l1_vendor_name[\"identifier\"] = \"Yesterday - (t-1)\"\n", "l1_vendor_name = (\n", "    l1_vendor_name.groupby([\"vendor_name\", \"identifier\"])\n", "    .agg({\"inward_quantity\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"identifier\"], ascending=False)\n", ")\n", "\n", "l7_vendor_name = entity_transfer_df[\n", "    (pd.to_datetime(entity_transfer_df[\"date\"]) >= pd.to_datetime(date.today() - timed<PERSON>ta(days=7)))\n", "    & (\n", "        pd.to_datetime(entity_transfer_df[\"date\"])\n", "        < pd.to_datetime(date.today() - timed<PERSON>ta(days=0))\n", "    )\n", "]\n", "l7_vendor_name[\"identifier\"] = \"last 7 days\"\n", "l7_vendor_name = (\n", "    l7_vendor_name.groupby([\"vendor_name\", \"identifier\"])\n", "    .agg({\"inward_quantity\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"identifier\"], ascending=False)\n", ")\n", "\n", "mtd_vendor_name = pd.concat([mtd_vendor_name, l7_vendor_name, l1_vendor_name])\n", "\n", "\n", "mtd_final_transfers_vendor_name = mtd_vendor_name.pivot_table(\n", "    index=[\"vendor_name\"],\n", "    columns=[\"identifier\"],\n", "    values=[\"inward_quantity\"],\n", "    fill_value=0,\n", ").reset_index()\n", "\n", "mtd_final_transfers_vendor_name.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "f5a49beb-f14b-4791-99ab-4ea835c5709a", "metadata": {}, "outputs": [], "source": ["# Transfers -\n", "\n", "sheet_id = \"1MhYtIM3TGvQoNnC6ciRuKXAnoeEBqTLOh_pQ-uPUtWM\"\n", "output_sheet = \"mtd_final_transfers_vendor_name\"\n", "pb.to_sheets(mtd_final_transfers_vendor_name, sheet_id, output_sheet)"]}, {"cell_type": "code", "execution_count": null, "id": "522f3167-dc33-498c-97cf-d101b96dcc32", "metadata": {}, "outputs": [], "source": ["del [\n", "    [\n", "        l1_vendor_name,\n", "        l7_vendor_name,\n", "        mtd_final_transfers_vendor_name,\n", "        mtd_vendor_name,\n", "        entity_po_df,\n", "        entity_transfer_df,\n", "    ]\n", "]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "8f9cedcd-87fa-497d-abe7-88275784dabe", "metadata": {}, "outputs": [], "source": ["alldfs = [var for var in dir() if isinstance(eval(var), pd.core.frame.DataFrame)]\n", "print(alldfs)"]}, {"cell_type": "code", "execution_count": null, "id": "a4d49868-2c35-40e1-a504-1d5f17d3fadf", "metadata": {}, "outputs": [], "source": ["entity_transfer_item_query = f\"\"\"\n", "with sto_details as \n", "(\n", "    select distinct\n", "        base.sto_id, base.outlet_id, base.merchant_outlet_id,\n", "        base.created_at + interval '330' Minute as created_at\n", "    from \n", "        lake_view_ims.ims_sto_details base\n", "    where\n", "        base.created_at between date('2021-12-01') and date('2022-03-01')\n", "),\n", "\n", "sto_items as\n", "(\n", "    SELECT\n", "        distinct sto_id, item_id, billed_quantity, inward_quantity\n", "    FROM\n", "        lake_view_ims.ims_sto_item item\n", "    WHERE\n", "        created_at between date('2021-12-01') and date('2022-03-01')\n", "    AND\n", "        sto_id in (select distinct sto_id from sto_details)\n", "),\n", "\n", "po_sto as\n", "(\n", "    SELECT\n", "        distinct po_sto.id, po_sto.outlet_id, destination_entity_vendor_id\n", "    FROM\n", "        lake_view_po.sto po_sto\n", "    where \n", "        po_sto.destination_entity_vendor_id in (select distinct entity_vendor_id from lake_retail.outlet_entity_vendor_mapping where entity_vendor_type_id = 5 and active = 1)\n", "        and po_sto.created_at between date('2021-12-01') and date('2022-03-01')\n", "        and id in (select distinct sto_id from sto_details)\n", "),\n", "\n", "base as \n", "(\n", "    select \n", "        base.sto_id, base.outlet_id, base.merchant_outlet_id, destination_entity_vendor_id,\n", "        base.created_at + interval '330' Minute as created_at, \n", "        item_id, billed_quantity, inward_quantity\n", "    from \n", "        sto_details base\n", "    inner join po_sto po_sto\n", "        on po_sto.id = base.sto_id and base.outlet_id = po_sto.outlet_id\n", "    left join \n", "        sto_items item\n", "        on base.sto_id = item.sto_id\n", "    where \n", "        po_sto.destination_entity_vendor_id in (select distinct entity_vendor_id from lake_retail.outlet_entity_vendor_mapping where entity_vendor_type_id = 5 and active = 1)\n", "        and base.created_at between date('2021-12-01') and date('2022-03-01')\n", ")\n", "\n", "select\n", "    date(created_at) as date, outlet_id as sender_outlet_id, merchant_outlet_id as receiving_outlet_id, destination_entity_vendor_id as entity_vendor_id, item_id, SUM(billed_quantity) as billed_quantity, \n", "    SUM(inward_quantity) as inward_quantity\n", "FROM\n", "    base\n", "GROUP BY \n", "    1,2,3,4,5\n", "\n", "\"\"\"\n", "entity_transfer_item_df_0 = read_sql_query(entity_transfer_item_query, CON_PRESTO)\n", "\n", "\n", "entity_transfer_item_query = f\"\"\"\n", "with sto_details as \n", "(\n", "    select distinct\n", "        base.sto_id, base.outlet_id, base.merchant_outlet_id,\n", "        base.created_at + interval '330' Minute as created_at\n", "    from \n", "        lake_view_ims.ims_sto_details base\n", "    where\n", "        base.created_at between date('2022-03-01') and date('2022-05-01')\n", "),\n", "\n", "sto_items as\n", "(\n", "    SELECT\n", "        distinct sto_id, item_id, billed_quantity, inward_quantity\n", "    FROM\n", "        lake_view_ims.ims_sto_item item\n", "    WHERE\n", "        created_at between date('2022-03-01') and date('2022-05-01')\n", "    AND\n", "        sto_id in (select distinct sto_id from sto_details)\n", "),\n", "\n", "po_sto as\n", "(\n", "    SELECT\n", "        distinct po_sto.id, po_sto.outlet_id, destination_entity_vendor_id\n", "    FROM\n", "        lake_view_po.sto po_sto\n", "    where \n", "        po_sto.destination_entity_vendor_id in (select distinct entity_vendor_id from lake_retail.outlet_entity_vendor_mapping where entity_vendor_type_id = 5 and active = 1)\n", "        and po_sto.created_at between date('2022-03-01') and date('2022-05-01')\n", "        and id in (select distinct sto_id from sto_details)\n", "),\n", "\n", "base as \n", "(\n", "    select \n", "        base.sto_id, base.outlet_id, base.merchant_outlet_id, destination_entity_vendor_id,\n", "        base.created_at + interval '330' Minute as created_at, \n", "        item_id, billed_quantity, inward_quantity\n", "    from \n", "        sto_details base\n", "    inner join po_sto po_sto\n", "        on po_sto.id = base.sto_id and base.outlet_id = po_sto.outlet_id\n", "    left join \n", "        sto_items item\n", "        on base.sto_id = item.sto_id\n", "    where \n", "        po_sto.destination_entity_vendor_id in (select distinct entity_vendor_id from lake_retail.outlet_entity_vendor_mapping where entity_vendor_type_id = 5 and active = 1)\n", "        and base.created_at between date('2022-03-01') and date('2022-05-01')\n", ")\n", "\n", "select\n", "    date(created_at) as date, outlet_id as sender_outlet_id, merchant_outlet_id as receiving_outlet_id, destination_entity_vendor_id as entity_vendor_id, item_id, SUM(billed_quantity) as billed_quantity, \n", "    SUM(inward_quantity) as inward_quantity\n", "FROM\n", "    base\n", "GROUP BY \n", "    1,2,3,4,5\n", "\n", "\"\"\"\n", "entity_transfer_item_df_1 = read_sql_query(entity_transfer_item_query, CON_PRESTO)\n", "\n", "\n", "entity_transfer_item_query = f\"\"\"\n", "with sto_details as \n", "(\n", "    select distinct\n", "        base.sto_id, base.outlet_id, base.merchant_outlet_id,\n", "        base.created_at + interval '330' Minute as created_at\n", "    from \n", "        lake_view_ims.ims_sto_details base\n", "    where\n", "        base.created_at between date('2022-05-01') and date('2022-07-01')\n", "),\n", "\n", "sto_items as\n", "(\n", "    SELECT\n", "        distinct sto_id, item_id, billed_quantity, inward_quantity\n", "    FROM\n", "        lake_view_ims.ims_sto_item item\n", "    WHERE\n", "        created_at between date('2022-05-01') and date('2022-07-01')\n", "    AND\n", "        sto_id in (select distinct sto_id from sto_details)\n", "),\n", "\n", "po_sto as\n", "(\n", "    SELECT\n", "        distinct po_sto.id, po_sto.outlet_id, destination_entity_vendor_id\n", "    FROM\n", "        lake_view_po.sto po_sto\n", "    where \n", "        po_sto.destination_entity_vendor_id in (select distinct entity_vendor_id from lake_retail.outlet_entity_vendor_mapping where entity_vendor_type_id = 5 and active = 1)\n", "        and po_sto.created_at between date('2022-05-01') and date('2022-07-01')\n", "        and id in (select distinct sto_id from sto_details)\n", "),\n", "\n", "base as \n", "(\n", "    select \n", "        base.sto_id, base.outlet_id, base.merchant_outlet_id, destination_entity_vendor_id,\n", "        base.created_at + interval '330' Minute as created_at, \n", "        item_id, billed_quantity, inward_quantity\n", "    from \n", "        sto_details base\n", "    inner join po_sto po_sto\n", "        on po_sto.id = base.sto_id and base.outlet_id = po_sto.outlet_id\n", "    left join \n", "        sto_items item\n", "        on base.sto_id = item.sto_id\n", "    where \n", "        po_sto.destination_entity_vendor_id in (select distinct entity_vendor_id from lake_retail.outlet_entity_vendor_mapping where entity_vendor_type_id = 5 and active = 1)\n", "        and base.created_at between date('2022-05-01') and date('2022-07-01')\n", ")\n", "\n", "select\n", "    date(created_at) as date, outlet_id as sender_outlet_id, merchant_outlet_id as receiving_outlet_id, destination_entity_vendor_id as entity_vendor_id, item_id, SUM(billed_quantity) as billed_quantity, \n", "    SUM(inward_quantity) as inward_quantity\n", "FROM\n", "    base\n", "GROUP BY \n", "    1,2,3,4,5\n", "\n", "\"\"\"\n", "entity_transfer_item_df_2 = read_sql_query(entity_transfer_item_query, CON_PRESTO)\n", "\n", "\n", "entity_transfer_item_query = f\"\"\"\n", "with sto_details as \n", "(\n", "    select distinct\n", "        base.sto_id, base.outlet_id, base.merchant_outlet_id,\n", "        base.created_at + interval '330' Minute as created_at\n", "    from \n", "        lake_view_ims.ims_sto_details base\n", "    where\n", "        base.created_at between date('2022-07-01') and current_date + 1\n", "),\n", "\n", "sto_items as\n", "(\n", "    SELECT\n", "        distinct sto_id, item_id, billed_quantity, inward_quantity\n", "    FROM\n", "        lake_view_ims.ims_sto_item item\n", "    WHERE\n", "        created_at between date('2022-07-01') and current_date + 1\n", "    AND\n", "        sto_id in (select distinct sto_id from sto_details)\n", "),\n", "\n", "po_sto as\n", "(\n", "    SELECT\n", "        distinct po_sto.id, po_sto.outlet_id, destination_entity_vendor_id\n", "    FROM\n", "        lake_view_po.sto po_sto\n", "    where \n", "        po_sto.destination_entity_vendor_id in (select distinct entity_vendor_id from lake_retail.outlet_entity_vendor_mapping where entity_vendor_type_id = 5 and active = 1)\n", "        and po_sto.created_at between date('2022-07-01') and current_date + 1\n", "        and id in (select distinct sto_id from sto_details)\n", "),\n", "\n", "base as \n", "(\n", "    select \n", "        base.sto_id, base.outlet_id, base.merchant_outlet_id, destination_entity_vendor_id,\n", "        base.created_at + interval '330' Minute as created_at, \n", "        item_id, billed_quantity, inward_quantity\n", "    from \n", "        sto_details base\n", "    inner join po_sto po_sto\n", "        on po_sto.id = base.sto_id and base.outlet_id = po_sto.outlet_id\n", "    left join \n", "        sto_items item\n", "        on base.sto_id = item.sto_id\n", "    where \n", "        po_sto.destination_entity_vendor_id in (select distinct entity_vendor_id from lake_retail.outlet_entity_vendor_mapping where entity_vendor_type_id = 5 and active = 1)\n", "        and base.created_at between date('2022-07-01') and current_date + 1\n", ")\n", "\n", "select\n", "    date(created_at) as date, outlet_id as sender_outlet_id, merchant_outlet_id as receiving_outlet_id, destination_entity_vendor_id as entity_vendor_id, item_id, SUM(billed_quantity) as billed_quantity, \n", "    SUM(inward_quantity) as inward_quantity\n", "FROM\n", "    base\n", "GROUP BY \n", "    1,2,3,4,5\n", "\n", "\"\"\"\n", "entity_transfer_item_df_3 = read_sql_query(entity_transfer_item_query, CON_PRESTO)\n", "\n", "entity_transfer_item_df = pd.concat(\n", "    [\n", "        entity_transfer_item_df_0,\n", "        entity_transfer_item_df_1,\n", "        entity_transfer_item_df_2,\n", "        entity_transfer_item_df_3,\n", "    ]\n", ").drop_duplicates()\n", "\n", "del [\n", "    [\n", "        entity_transfer_item_df_0,\n", "        entity_transfer_item_df_1,\n", "        entity_transfer_item_df_2,\n", "        entity_transfer_item_df_3,\n", "    ]\n", "]\n", "gc.collect()\n", "\n", "entity_transfer_item_df = pd.merge(\n", "    entity_transfer_item_df,\n", "    entity_vendor_base_df[[\"entity_vendor_id\", \"vendor_name\", \"entity_type\"]],\n", "    on=[\"entity_vendor_id\"],\n", "    how=\"left\",\n", ")\n", "entity_transfer_item_df[\"year\"] = pd.to_datetime(entity_transfer_item_df[\"date\"]).dt.year\n", "entity_transfer_item_df[\"month\"] = pd.to_datetime(entity_transfer_item_df[\"date\"]).dt.month\n", "entity_transfer_item_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "85e2ff39-2848-41e9-8bdb-1d75890b8064", "metadata": {}, "outputs": [], "source": ["entity_transfer_item_df = pd.merge(\n", "    entity_transfer_item_df,\n", "    wlp_df.rename(columns={\"outlet_id\": \"sender_outlet_id\"}),\n", "    on=[\"sender_outlet_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "entity_transfer_item_df = pd.merge(\n", "    entity_transfer_item_df,\n", "    wlp_df.groupby([\"item_id\"])\n", "    .agg({\"wlp\": \"mean\"})\n", "    .reset_index()\n", "    .rename(columns={\"wlp\": \"wlp_item\"}),\n", "    on=[\"item_id\"],\n", "    how=\"left\",\n", ")\n", "entity_transfer_item_df[\"wlp\"] = np.where(\n", "    entity_transfer_item_df[\"wlp\"].isna(),\n", "    entity_transfer_item_df[\"wlp_item\"],\n", "    entity_transfer_item_df[\"wlp\"],\n", ")\n", "\n", "entity_transfer_item_df = entity_transfer_item_df.drop(columns={\"wlp_item\"})\n", "\n", "missing_wlp = list(entity_transfer_item_df[entity_transfer_item_df.wlp.isna()][\"item_id\"].unique())\n", "\n", "if (\n", "    entity_transfer_item_df[\n", "        (entity_transfer_item_df.wlp.isna()) & (entity_transfer_item_df.item_id.isna() == False)\n", "    ][[\"item_id\"]]\n", "    .drop_duplicates()\n", "    .shape[0]\n", "    < 2\n", "):\n", "    missing_wlp.append(-1)\n", "\n", "missing_wlp = tuple(missing_wlp)\n", "\n", "entity_transfer_item_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bd0cc3b6-f95c-474d-8928-c75a192c532a", "metadata": {}, "outputs": [], "source": ["missing_wlp"]}, {"cell_type": "code", "execution_count": null, "id": "57ff4e98-9543-4d7c-b329-af51e2a50dfc", "metadata": {}, "outputs": [], "source": ["mrp_query = f\"\"\"\n", "select \n", "    item_id, avg(variant_mrp)*.80 as mrp\n", "from \n", "    lake_view_rpc.item_details\n", "where \n", "    item_id in {missing_wlp} and active = 1 and approved = 1\n", "group by 1\n", "\"\"\"\n", "mrp_df = read_sql_query(mrp_query, CON_PRESTO)"]}, {"cell_type": "code", "execution_count": null, "id": "17666349-aad6-4f9b-bc97-9f91a6b4704f", "metadata": {}, "outputs": [], "source": ["entity_transfer_item_df = pd.merge(entity_transfer_item_df, mrp_df, on=[\"item_id\"], how=\"left\")\n", "entity_transfer_item_df[\"wlp\"] = np.where(\n", "    entity_transfer_item_df[\"wlp\"].isna(),\n", "    entity_transfer_item_df[\"mrp\"],\n", "    entity_transfer_item_df[\"wlp\"],\n", ")\n", "\n", "entity_transfer_item_df[\"inward_value\"] = (\n", "    entity_transfer_item_df[\"inward_quantity\"] * entity_transfer_item_df[\"wlp\"]\n", ")\n", "\n", "entity_transfer_item_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f323a3ba-bcc4-4038-ba86-efb5c1627066", "metadata": {}, "outputs": [], "source": ["entity_transfer_item_df = (\n", "    entity_transfer_item_df.groupby(\n", "        [\n", "            \"date\",\n", "            \"sender_outlet_id\",\n", "            \"receiving_outlet_id\",\n", "            \"entity_vendor_id\",\n", "            \"vendor_name\",\n", "            \"entity_type\",\n", "            \"year\",\n", "            \"month\",\n", "        ]\n", "    )\n", "    .agg({\"inward_value\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "768dadd8-a588-4f5e-b074-a25f40611330", "metadata": {"tags": []}, "outputs": [], "source": ["# 1. Overall Transfers\n", "# 2. Entity Type Transfers\n", "# 3. Entity Transfers"]}, {"cell_type": "code", "execution_count": null, "id": "138b675b-bf37-4f07-9ded-d3f6eb64aa41", "metadata": {"tags": []}, "outputs": [], "source": ["# 1. Overall Transfers\n", "\n", "mtd = (\n", "    entity_transfer_item_df.groupby([\"year\", \"month\"])\n", "    .agg({\"inward_value\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"year\", \"month\"], ascending=False)\n", ")\n", "mtd[\"identifier\"] = mtd[\"year\"].astype(str) + \" \" + mtd[\"month\"].astype(str)\n", "mtd = mtd.drop(columns={\"month\", \"year\"})\n", "mtd = mtd[[\"identifier\", \"inward_value\"]]\n", "\n", "l1 = entity_transfer_item_df[\n", "    (\n", "        pd.to_datetime(entity_transfer_item_df[\"date\"])\n", "        == pd.to_datetime(date.today() - timed<PERSON>ta(days=1))\n", "    )\n", "]\n", "l1[\"identifier\"] = \"Yesterday - (t-1)\"\n", "l1 = (\n", "    l1.groupby([\"identifier\"])\n", "    .agg({\"inward_value\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"identifier\"], ascending=False)\n", ")\n", "\n", "l7 = entity_transfer_item_df[\n", "    (\n", "        pd.to_datetime(entity_transfer_item_df[\"date\"])\n", "        >= pd.to_datetime(date.today() - <PERSON><PERSON><PERSON>(days=7))\n", "    )\n", "    & (\n", "        pd.to_datetime(entity_transfer_item_df[\"date\"])\n", "        < pd.to_datetime(date.today() - timed<PERSON>ta(days=0))\n", "    )\n", "]\n", "l7[\"identifier\"] = \"last 7 days\"\n", "l7 = (\n", "    l7.groupby([\"identifier\"])\n", "    .agg({\"inward_value\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"identifier\"], ascending=False)\n", ")\n", "\n", "mtd = pd.concat([mtd, l7, l1])\n", "\n", "mtd[\"index_key\"] = \"1\"\n", "\n", "mtd_final_transfers = mtd.pivot_table(\n", "    index=[\"index_key\"], columns=[\"identifier\"], values=[\"inward_value\"], fill_value=0\n", ").reset_index()\n", "\n", "mtd_final_transfers = mtd_final_transfers.drop(columns={\"index_key\"})\n", "\n", "mtd_final_transfers.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "05a1b131-8b42-4108-b4f5-ad2c64be8922", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1MhYtIM3TGvQoNnC6ciRuKXAnoeEBqTLOh_pQ-uPUtWM\"\n", "output_sheet = \"mtd_final_transfers_value\"\n", "pb.to_sheets(mtd_final_transfers, sheet_id, output_sheet)"]}, {"cell_type": "code", "execution_count": null, "id": "a2b46806-dc9e-4f01-940c-a5915788f41e", "metadata": {}, "outputs": [], "source": ["del [[entity_po_df, entity_transfer_df, l1, l7, mrp_df, mtd, mtd_final_transfers]]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "25defc54-6631-4393-8ded-c1f80f1262a8", "metadata": {}, "outputs": [], "source": ["alldfs = [var for var in dir() if isinstance(eval(var), pd.core.frame.DataFrame)]\n", "print(alldfs)"]}, {"cell_type": "code", "execution_count": null, "id": "2646ab66-7999-46bf-8634-86ead903b169", "metadata": {}, "outputs": [], "source": ["# 1. Overall Transfers\n", "\n", "mtd_entity_type = (\n", "    entity_transfer_item_df.groupby([\"entity_type\", \"year\", \"month\"])\n", "    .agg({\"inward_value\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"year\", \"month\"], ascending=False)\n", ")\n", "mtd_entity_type[\"identifier\"] = (\n", "    mtd_entity_type[\"year\"].astype(str) + \" \" + mtd_entity_type[\"month\"].astype(str)\n", ")\n", "mtd_entity_type = mtd_entity_type.drop(columns={\"month\", \"year\"})\n", "mtd_entity_type = mtd_entity_type[[\"entity_type\", \"identifier\", \"inward_value\"]]\n", "\n", "l1_entity_type = entity_transfer_item_df[\n", "    (\n", "        pd.to_datetime(entity_transfer_item_df[\"date\"])\n", "        == pd.to_datetime(date.today() - timed<PERSON>ta(days=1))\n", "    )\n", "]\n", "l1_entity_type[\"identifier\"] = \"Yesterday - (t-1)\"\n", "l1_entity_type = (\n", "    l1_entity_type.groupby([\"entity_type\", \"identifier\"])\n", "    .agg({\"inward_value\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"identifier\"], ascending=False)\n", ")\n", "\n", "l7_entity_type = entity_transfer_item_df[\n", "    (\n", "        pd.to_datetime(entity_transfer_item_df[\"date\"])\n", "        >= pd.to_datetime(date.today() - <PERSON><PERSON><PERSON>(days=7))\n", "    )\n", "    & (\n", "        pd.to_datetime(entity_transfer_item_df[\"date\"])\n", "        < pd.to_datetime(date.today() - timed<PERSON>ta(days=0))\n", "    )\n", "]\n", "l7_entity_type[\"identifier\"] = \"last 7 days\"\n", "l7_entity_type = (\n", "    l7_entity_type.groupby([\"entity_type\", \"identifier\"])\n", "    .agg({\"inward_value\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"identifier\"], ascending=False)\n", ")\n", "\n", "mtd_entity_type = pd.concat([mtd_entity_type, l7_entity_type, l1_entity_type])\n", "\n", "\n", "mtd_final_transfers_entity_type = mtd_entity_type.pivot_table(\n", "    index=[\"entity_type\"], columns=[\"identifier\"], values=[\"inward_value\"], fill_value=0\n", ").reset_index()\n", "\n", "\n", "mtd_final_transfers_entity_type.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "31cbd372-91af-445e-87ec-9a40163b7fda", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1MhYtIM3TGvQoNnC6ciRuKXAnoeEBqTLOh_pQ-uPUtWM\"\n", "output_sheet = \"mtd_final_transfers_entity_type_value\"\n", "pb.to_sheets(mtd_final_transfers_entity_type, sheet_id, output_sheet)"]}, {"cell_type": "code", "execution_count": null, "id": "77eb7dcd-ff13-435b-8484-8815673b500d", "metadata": {}, "outputs": [], "source": ["del [[l1_entity_type, l7_entity_type, mtd_entity_type, mtd_final_transfers_entity_type]]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "69499c93-6dbf-435a-8d33-8e559d86235d", "metadata": {}, "outputs": [], "source": ["alldfs = [var for var in dir() if isinstance(eval(var), pd.core.frame.DataFrame)]\n", "print(alldfs)"]}, {"cell_type": "code", "execution_count": null, "id": "c1bb731e-5065-4046-91cc-aa5d4b3368b1", "metadata": {}, "outputs": [], "source": ["# 1. Overall Transfers\n", "\n", "mtd_vendor_name = (\n", "    entity_transfer_item_df.groupby([\"vendor_name\", \"year\", \"month\"])\n", "    .agg({\"inward_value\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"year\", \"month\"], ascending=False)\n", ")\n", "mtd_vendor_name[\"identifier\"] = (\n", "    mtd_vendor_name[\"year\"].astype(str) + \" \" + mtd_vendor_name[\"month\"].astype(str)\n", ")\n", "mtd_vendor_name = mtd_vendor_name.drop(columns={\"month\", \"year\"})\n", "mtd_vendor_name = mtd_vendor_name[[\"vendor_name\", \"identifier\", \"inward_value\"]]\n", "\n", "l1_vendor_name = entity_transfer_item_df[\n", "    (\n", "        pd.to_datetime(entity_transfer_item_df[\"date\"])\n", "        == pd.to_datetime(date.today() - timed<PERSON>ta(days=1))\n", "    )\n", "]\n", "l1_vendor_name[\"identifier\"] = \"Yesterday - (t-1)\"\n", "l1_vendor_name = (\n", "    l1_vendor_name.groupby([\"vendor_name\", \"identifier\"])\n", "    .agg({\"inward_value\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"identifier\"], ascending=False)\n", ")\n", "\n", "l7_vendor_name = entity_transfer_item_df[\n", "    (\n", "        pd.to_datetime(entity_transfer_item_df[\"date\"])\n", "        >= pd.to_datetime(date.today() - <PERSON><PERSON><PERSON>(days=7))\n", "    )\n", "    & (\n", "        pd.to_datetime(entity_transfer_item_df[\"date\"])\n", "        < pd.to_datetime(date.today() - timed<PERSON>ta(days=0))\n", "    )\n", "]\n", "l7_vendor_name[\"identifier\"] = \"last 7 days\"\n", "l7_vendor_name = (\n", "    l7_vendor_name.groupby([\"vendor_name\", \"identifier\"])\n", "    .agg({\"inward_value\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"identifier\"], ascending=False)\n", ")\n", "\n", "mtd_vendor_name = pd.concat([mtd_vendor_name, l7_vendor_name, l1_vendor_name])\n", "\n", "\n", "mtd_final_transfers_vendor_name = mtd_vendor_name.pivot_table(\n", "    index=[\"vendor_name\"], columns=[\"identifier\"], values=[\"inward_value\"], fill_value=0\n", ").reset_index()\n", "\n", "mtd_final_transfers_vendor_name.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "f46ab6fc-a37e-478f-964b-67bfe2409e86", "metadata": {}, "outputs": [], "source": ["# Transfers -\n", "\n", "sheet_id = \"1MhYtIM3TGvQoNnC6ciRuKXAnoeEBqTLOh_pQ-uPUtWM\"\n", "output_sheet = \"mtd_final_transfers_vendor_name_value\"\n", "pb.to_sheets(mtd_final_transfers_vendor_name, sheet_id, output_sheet)"]}, {"cell_type": "code", "execution_count": null, "id": "b2ca464e-f868-4639-a0a4-1cfcbc97951c", "metadata": {}, "outputs": [], "source": ["del [\n", "    [\n", "        entity_transfer_item_df,\n", "        l1_vendor_name,\n", "        l7_vendor_name,\n", "        mtd_final_transfers_vendor_name,\n", "        mtd_vendor_name,\n", "        wlp_df,\n", "    ]\n", "]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "ad2748a6-85a6-4ac3-87ae-422970e764f0", "metadata": {}, "outputs": [], "source": ["alldfs = [var for var in dir() if isinstance(eval(var), pd.core.frame.DataFrame)]\n", "print(alldfs)"]}, {"cell_type": "code", "execution_count": null, "id": "bf9efd70-fcb0-47cd-94f5-c07bef7f46ad", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "16ad99bf-8b9d-4606-bb90-ad17bcd42d2c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7a306aa9-48d6-4b6e-ba4f-90a890f5c124", "metadata": {}, "outputs": [], "source": ["# PO Funnel"]}, {"cell_type": "code", "execution_count": null, "id": "0ed69a57-df75-422b-8c46-875abe6c9e76", "metadata": {}, "outputs": [], "source": ["entity_po_concat_df[\"grn_qty\"] = entity_po_concat_df[\"grn_qty\"].fillna(0)\n", "entity_po_concat_df[\"grn_value\"] = entity_po_concat_df[\"grn_value\"].fillna(0)\n", "entity_po_concat_df[\"issue_date\"] = pd.to_datetime(entity_po_concat_df[\"issue_date\"])\n", "entity_po_concat_df[\"issue_date_dt\"] = entity_po_concat_df[\"issue_date\"].dt.date\n", "entity_po_concat_df[\"issue_date_month\"] = entity_po_concat_df[\"issue_date\"].dt.month\n", "entity_po_concat_df[\"issue_date_year\"] = entity_po_concat_df[\"issue_date\"].dt.year\n", "# Mapping Entity Type\n", "entity_po_concat_df = pd.merge(\n", "    entity_po_concat_df,\n", "    entity_vendor_base_df[[\"entity_vendor_id\", \"vendor_name\", \"entity_type\"]].drop_duplicates(),\n", "    on=[\"entity_vendor_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "entity_po_concat_df[\"po_type\"] = np.where(\n", "    (\n", "        (\n", "            entity_po_concat_df[\"po_state\"].isin(\n", "                [\"Created\", \"Scheduled\", \"Unscheduled\", \"Rescheduled\", \"Edit Pending\"]\n", "            )\n", "        )\n", "        | (entity_po_concat_df[\"is_multiple_grn\"] == 1)\n", "    )\n", "    & (~entity_po_concat_df[\"po_state\"].isin([\"Expired\", \"Cancelled\", \"Cancelled post Creation\"]))\n", "    & (~entity_po_concat_df[\"po_state_id\"].isin([4, 5, 10])),\n", "    \"Open PO\",\n", "    np.where(\n", "        (entity_po_concat_df[\"po_state\"].isin([\"Cancelled\", \"Cancelled post Creation\"])),\n", "        \"Cancelled\",\n", "        entity_po_concat_df[\"po_state\"],\n", "    ),\n", ")\n", "\n", "entity_po_concat_df[\"identifier\"] = (\n", "    entity_po_concat_df[\"issue_date_year\"].astype(str)\n", "    + \" \"\n", "    + entity_po_concat_df[\"issue_date_month\"].astype(str)\n", ")\n", "\n", "entity_po_concat_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "876b074c-a62a-43c6-8cd0-8bd595fda1e7", "metadata": {}, "outputs": [], "source": ["# Get Total\n", "\n", "po_total_funnel_df = (\n", "    entity_po_concat_df.groupby([\"identifier\"])\n", "    .agg({\"expected_po_qty\": \"sum\", \"grn_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "po_total_funnel_df[\"po_type\"] = \"Total\"\n", "po_total_funnel_df = po_total_funnel_df[[\"identifier\", \"po_type\", \"expected_po_qty\", \"grn_qty\"]]\n", "\n", "po_funnel_type_df = (\n", "    entity_po_concat_df.groupby([\"identifier\", \"po_type\"])\n", "    .agg({\"expected_po_qty\": \"sum\", \"grn_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "po_funnel_type_df = pd.concat([po_funnel_type_df, po_total_funnel_df])\n", "\n", "po_funnel_type_df[\"rank_types\"] = np.where(\n", "    po_funnel_type_df[\"po_type\"] == \"Total\",\n", "    1,\n", "    np.where(\n", "        po_funnel_type_df[\"po_type\"] == \"Fulfilled\",\n", "        2,\n", "        np.where(\n", "            po_funnel_type_df[\"po_type\"] == \"Expired\",\n", "            3,\n", "            np.where(po_funnel_type_df[\"po_type\"] == \"Open\", 4, 5),\n", "        ),\n", "    ),\n", ")\n", "\n", "\n", "po_funnel_type_df = po_funnel_type_df.sort_values(by=[\"identifier\", \"rank_types\"])\n", "# po_funnel_type_df = po_funnel_type_df.drop(columns = {'rank_types'})\n", "\n", "po_funnel_type_df = po_funnel_type_df.rename(columns={\"expected_po_qty\": \"po_quantity_raised\"})\n", "\n", "po_funnel_type_df_x = (\n", "    po_funnel_type_df.pivot_table(\n", "        index=[\"po_type\", \"rank_types\"],\n", "        columns=[\"identifier\"],\n", "        values=[\"po_quantity_raised\"],\n", "        fill_value=0,\n", "    )\n", "    .reset_index()\n", "    .sort_values(by=\"rank_types\")\n", "    .drop(columns={\"rank_types\"})\n", ")\n", "\n", "\n", "po_funnel_type_df_y = (\n", "    po_funnel_type_df.pivot_table(\n", "        index=[\"po_type\", \"rank_types\"],\n", "        columns=[\"identifier\"],\n", "        values=[\"grn_qty\"],\n", "        fill_value=0,\n", "    )\n", "    .reset_index()\n", "    .sort_values(by=\"rank_types\")\n", "    .drop(columns={\"rank_types\"})\n", ")\n", "\n", "\n", "po_funnel_type_df = pd.merge(po_funnel_type_df_x, po_funnel_type_df_y, on=[\"po_type\"], how=\"left\")\n", "po_funnel_type_df"]}, {"cell_type": "code", "execution_count": null, "id": "48f1b526-c93e-4176-96c6-6d869997e9d5", "metadata": {}, "outputs": [], "source": ["del [\n", "    [\n", "        entity_po_concat_df,\n", "        entity_vendor_base_df,\n", "        po_funnel_type_df_x,\n", "        po_funnel_type_df_y,\n", "        po_total_funnel_df,\n", "    ]\n", "]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "79b3af64-7831-4306-9ce9-57539bfdbf49", "metadata": {}, "outputs": [], "source": ["alldfs = [var for var in dir() if isinstance(eval(var), pd.core.frame.DataFrame)]\n", "print(alldfs)"]}, {"cell_type": "code", "execution_count": null, "id": "ef452a94-4ea9-42b1-83d2-5eac6f02e7e0", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1MhYtIM3TGvQoNnC6ciRuKXAnoeEBqTLOh_pQ-uPUtWM\"\n", "output_sheet = \"po_funnel_raw\"\n", "pb.to_sheets(po_funnel_type_df, sheet_id, output_sheet)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}