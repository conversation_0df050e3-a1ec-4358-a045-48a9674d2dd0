{"cells": [{"cell_type": "code", "execution_count": null, "id": "097c4d44-aee7-423a-8cfe-478e95fce280", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)\n", "pd.set_option(\"display.float_format\", lambda x: \"%.3f\" % x)"]}, {"cell_type": "code", "execution_count": null, "id": "3dd13c89-b6ce-46ad-bc18-d9a01a84e2c9", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\""]}, {"cell_type": "code", "execution_count": null, "id": "3a1f6d62-a377-44ad-bfc0-b8a1b814c71f", "metadata": {}, "outputs": [], "source": ["suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])"]}, {"cell_type": "code", "execution_count": null, "id": "c9ee6e65-8956-42bc-83c5-ef0b4cc98c13", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 1\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "16e2b8dd-279e-464c-a8aa-34432c77d7d0", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, **kwargs):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            print(f\"Time: {formatted_duration} and data pushed in table\")\n", "\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "d36fae29-0515-47d5-8398-5ec4c8be5211", "metadata": {}, "outputs": [], "source": ["def sheets(data, operation, sheet_id, sheet_name, clear_cache=True):\n", "    max_retries = 5\n", "    retry_interval = 10  # in seconds\n", "\n", "    for attempt in range(max_retries):\n", "        try:\n", "            start_time = time.time()  # Start timing\n", "\n", "            if operation == \"write\":\n", "                pb.to_sheets(data, sheet_id, sheet_name)\n", "                end_time = time.time()  # End timing\n", "                duration = end_time - start_time\n", "                formatted_duration = format_time(duration)\n", "                print(f\"Attempt {attempt + 1} for {operation}... done! Time: {formatted_duration}\")\n", "\n", "            elif operation == \"read\":\n", "                fetched_data = pb.from_sheets(sheet_id, sheet_name)\n", "                end_time = time.time()  # End timing\n", "                duration = end_time - start_time\n", "                formatted_duration = format_time(duration)\n", "                print(f\"Attempt {attempt + 1} for {operation}... done! Time: {formatted_duration}\")\n", "                return fetched_data\n", "\n", "            break  # Break out of the loop if successful\n", "\n", "        except Exception as e:\n", "            print(f\"Error: {e}\")\n", "\n", "            if attempt < max_retries - 1:\n", "                print(f\"Retrying in {retry_interval} seconds...\")\n", "                time.sleep(retry_interval)\n", "            else:\n", "                print(f\"Max retries reached. Unable to perform {operation} operation.\")\n", "                return None  # Return None if max retries reached and break out of the loop"]}, {"cell_type": "code", "execution_count": null, "id": "924c5d93-ebd2-4f8c-9913-cacd10530366", "metadata": {}, "outputs": [], "source": ["def t1_raw():\n", "    t1_raw = \"\"\"\n", "    \n", "    with\n", "    entity_sales as\n", "        (select\n", "            od.city_name,\n", "            iid.pos_outlet_id,\n", "            od.facility_name,\n", "            upper(iid.source_entity_vendor_legal_name) as seller_name,\n", "            count(distinct iid.cart_id) as t1_cart_count,\n", "            sum(iid.total_net_quantity) as t1_sales_quantity,\n", "            sum(iid.total_net_selling_price) as t1_sales_value\n", "\n", "                from dwh.fact_sales_invoice_item_details iid\n", "\n", "                    join\n", "                        dwh.fact_sales_order_details sod on sod.cart_id = iid.cart_id and sod.order_id = iid.order_id\n", "                        and\n", "                            sod.order_create_dt_ist >= current_date - interval '60' day\n", "                        and\n", "                            (sod.order_type not like '%%Internal%%' or sod.order_type is null)\n", "                        and\n", "                            sod.order_current_status = 'DELIVERED'\n", "                        and\n", "                            sod.is_internal_order = false\n", "\n", "                    join\n", "                        supply_etls.outlet_details od on od.hot_outlet_id = iid.pos_outlet_id\n", "\n", "                    where\n", "                        iid.order_create_date_ist = (current_date - interval '1' day)\n", "                        and\n", "                            iid.total_net_quantity > 0\n", "                        and\n", "                            source_entity_vendor_legal_name is not null\n", "                        and\n", "                            source_entity_vendor_legal_name <> 'NA'\n", "\n", "                        group by 1,2,3,4\n", "        )\n", "\n", "            select * from entity_sales\n", "    \n", "    \"\"\"\n", "    return read_sql_query(t1_raw, trino)\n", "\n", "\n", "t1_raw = t1_raw()\n", "\n", "print(t1_raw.shape)\n", "t1_raw.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "a05afe85-d9d2-4d92-a4e2-861d025889e9", "metadata": {}, "outputs": [], "source": ["def w_raw():\n", "    w_raw = \"\"\"\n", "    \n", "    with\n", "    entity_sales as\n", "        (select\n", "            extract(week from iid.order_create_date_ist) as week_,\n", "            od.city_name,\n", "            iid.pos_outlet_id,\n", "            od.facility_name,\n", "            upper(iid.source_entity_vendor_legal_name) as seller_name,\n", "            count(distinct iid.cart_id) as w_cart_count,\n", "            sum(iid.total_net_quantity) as w_sales_quantity,\n", "            sum(iid.total_net_selling_price) as w_sales_value\n", "\n", "                from dwh.fact_sales_invoice_item_details iid\n", "\n", "                    join\n", "                        dwh.fact_sales_order_details sod on sod.cart_id = iid.cart_id and sod.order_id = iid.order_id\n", "                        and\n", "                            sod.order_create_dt_ist >= current_date - interval '60' day\n", "                        and\n", "                            (sod.order_type not like '%%Internal%%' or sod.order_type is null)\n", "                        and\n", "                            sod.order_current_status = 'DELIVERED'\n", "                        and\n", "                            sod.is_internal_order = false\n", "\n", "                    join\n", "                        supply_etls.outlet_details od on od.hot_outlet_id = iid.pos_outlet_id\n", "\n", "                    where\n", "                        iid.order_create_date_ist between (current_date - interval '7' day) and (current_date - interval '1' day)\n", "                        and\n", "                            iid.total_net_quantity > 0\n", "                        and\n", "                            source_entity_vendor_legal_name is not null\n", "                        and\n", "                            source_entity_vendor_legal_name <> 'NA'\n", "\n", "                        group by 1,2,3,4,5\n", "        )\n", "\n", "            select * from entity_sales\n", "                where\n", "                    week_ = extract(week from current_date)\n", "    \n", "    \"\"\"\n", "    return read_sql_query(w_raw, trino)\n", "\n", "\n", "w_raw = w_raw()\n", "\n", "print(w_raw.shape)\n", "w_raw.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "71cf3b2a-1691-48a7-a4fb-7943ca9cb48f", "metadata": {}, "outputs": [], "source": ["w_raw = w_raw.drop(columns={\"week_\"})"]}, {"cell_type": "code", "execution_count": null, "id": "3089ed0c-b876-42ef-bd66-74ca507043a7", "metadata": {}, "outputs": [], "source": ["def cm_raw():\n", "    cm_raw = \"\"\"\n", "    \n", "    with\n", "    entity_sales as\n", "        (select\n", "            extract(month from iid.order_create_date_ist) as month_,\n", "            od.city_name,\n", "            iid.pos_outlet_id,\n", "            od.facility_name,\n", "            upper(iid.source_entity_vendor_legal_name) as seller_name,\n", "            count(distinct iid.cart_id) as cm_cart_count,\n", "            sum(iid.total_net_quantity) as cm_sales_quantity,\n", "            sum(iid.total_net_selling_price) as cm_sales_value\n", "\n", "                from dwh.fact_sales_invoice_item_details iid\n", "\n", "                    join\n", "                        dwh.fact_sales_order_details sod on sod.cart_id = iid.cart_id and sod.order_id = iid.order_id\n", "                        and\n", "                            sod.order_create_dt_ist >= current_date - interval '60' day\n", "                        and\n", "                            (sod.order_type not like '%%Internal%%' or sod.order_type is null)\n", "                        and\n", "                            sod.order_current_status = 'DELIVERED'\n", "                        and\n", "                            sod.is_internal_order = false\n", "\n", "                    join\n", "                        supply_etls.outlet_details od on od.hot_outlet_id = iid.pos_outlet_id\n", "\n", "                    where\n", "                        iid.order_create_date_ist >= (current_date - interval '40' day)\n", "                        and\n", "                            iid.total_net_quantity > 0\n", "                        and\n", "                            source_entity_vendor_legal_name is not null\n", "                        and\n", "                            source_entity_vendor_legal_name <> 'NA'\n", "\n", "                        group by 1,2,3,4,5\n", "        )\n", "\n", "            select * from entity_sales\n", "                where\n", "                    month_ = extract(month from current_date)\n", "    \n", "    \"\"\"\n", "    return read_sql_query(cm_raw, trino)\n", "\n", "\n", "cm_raw = cm_raw()\n", "\n", "print(cm_raw.shape)\n", "cm_raw.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "c6577b45-dd44-404c-a750-ba6408b3fc07", "metadata": {}, "outputs": [], "source": ["cm_raw = cm_raw.drop(columns={\"month_\"})"]}, {"cell_type": "code", "execution_count": null, "id": "36428259-55fb-4254-a9ab-b5a01e5990a9", "metadata": {}, "outputs": [], "source": ["def lm_raw():\n", "    lm_raw = \"\"\"\n", "    \n", "    with\n", "    entity_sales as\n", "        (select\n", "            extract(month from iid.order_create_date_ist) as month_,\n", "            od.city_name,\n", "            iid.pos_outlet_id,\n", "            od.facility_name,\n", "            upper(iid.source_entity_vendor_legal_name) as seller_name,\n", "            count(distinct iid.cart_id) as lm_cart_count,\n", "            sum(iid.total_net_quantity) as lm_sales_quantity,\n", "            sum(iid.total_net_selling_price) as lm_sales_value\n", "\n", "                from dwh.fact_sales_invoice_item_details iid\n", "\n", "                    join\n", "                        dwh.fact_sales_order_details sod on sod.cart_id = iid.cart_id and sod.order_id = iid.order_id\n", "                        and\n", "                            sod.order_create_dt_ist >= current_date - interval '60' day\n", "                        and\n", "                            (sod.order_type not like '%%Internal%%' or sod.order_type is null)\n", "                        and\n", "                            sod.order_current_status = 'DELIVERED'\n", "                        and\n", "                            sod.is_internal_order = false\n", "\n", "                    join\n", "                        supply_etls.outlet_details od on od.hot_outlet_id = iid.pos_outlet_id\n", "\n", "                    where\n", "                        iid.order_create_date_ist >= (current_date - interval '70' day)\n", "                        and\n", "                            iid.total_net_quantity > 0\n", "                        and\n", "                            source_entity_vendor_legal_name is not null\n", "                        and\n", "                            source_entity_vendor_legal_name <> 'NA'\n", "\n", "                        group by 1,2,3,4,5\n", "        )\n", "\n", "            select * from entity_sales\n", "                where\n", "                    month_ = extract(month from current_date - interval '40' day)\n", "    \n", "    \"\"\"\n", "    return read_sql_query(lm_raw, trino)\n", "\n", "\n", "lm_raw = lm_raw()\n", "\n", "print(lm_raw.shape)\n", "lm_raw.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "b3ff38d0-5ed3-4e8f-b0f1-325bfb6ded71", "metadata": {}, "outputs": [], "source": ["lm_raw = lm_raw.drop(columns={\"month_\"})"]}, {"cell_type": "code", "execution_count": null, "id": "e22f16af-f9bb-455d-8bdc-a25131329cae", "metadata": {}, "outputs": [], "source": ["final_base = t1_raw.append([w_raw, cm_raw, lm_raw])"]}, {"cell_type": "code", "execution_count": null, "id": "4d088fb5-8c2a-4f54-b691-a8be5c9cb0c2", "metadata": {}, "outputs": [], "source": ["final = (\n", "    final_base.groupby([\"city_name\", \"pos_outlet_id\", \"facility_name\", \"seller_name\"])[\n", "        [\n", "            \"t1_cart_count\",\n", "            \"t1_sales_quantity\",\n", "            \"t1_sales_value\",\n", "            \"w_cart_count\",\n", "            \"w_sales_quantity\",\n", "            \"w_sales_value\",\n", "            \"cm_cart_count\",\n", "            \"cm_sales_quantity\",\n", "            \"cm_sales_value\",\n", "            \"lm_cart_count\",\n", "            \"lm_sales_quantity\",\n", "            \"lm_sales_value\",\n", "        ]\n", "    ]\n", "    .sum()\n", "    .reset_index()\n", ")\n", "\n", "final"]}, {"cell_type": "code", "execution_count": null, "id": "e3ac191e-8d73-4eba-853b-7637617b1491", "metadata": {}, "outputs": [], "source": ["sheets(final, \"write\", \"1xK02vEphox-STYH7THfjZebE6_djMy9keQsA3l_xNt0\", \"raw_data\")"]}, {"cell_type": "code", "execution_count": null, "id": "deed26a3-f25c-4850-ac52-1ea0ac690b26", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "abaa9363-23e1-408c-9269-f7e2b832f2f4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}