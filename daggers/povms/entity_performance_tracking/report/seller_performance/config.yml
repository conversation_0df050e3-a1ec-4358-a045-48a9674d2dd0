alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: seller_performance
dag_type: report
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: low
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RJ5FRXFC
path: povms/entity_performance_tracking/report/seller_performance
paused: false
pool: povms_pool
project_name: entity_performance_tracking
schedule:
  end_date: '2025-08-17T00:00:00'
  interval: 5 2 * * *
  start_date: '2024-09-02T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 10
