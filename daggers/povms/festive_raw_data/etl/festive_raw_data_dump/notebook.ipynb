{"cells": [{"cell_type": "code", "execution_count": null, "id": "9e8fadcd-8722-4a13-be98-5afe2cb21a33", "metadata": {}, "outputs": [], "source": ["!pip install pytz"]}, {"cell_type": "code", "execution_count": null, "id": "ca5170bb-a30f-4882-9ba8-03ce96f9a83f", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "from tqdm import tqdm\n", "\n", "# import datetime\n", "import pytz\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "markdown", "id": "b4194c74-3a05-4427-af21-747a1c88d70e", "metadata": {}, "source": ["### IMP FUNCTIONS"]}, {"cell_type": "code", "execution_count": null, "id": "318146a8-313b-4c25-aa75-6b93e322a985", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"alerts-test\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)\n", "\n", "\n", "# GSheet to DataFrame\n", "def gsheet_to_df(sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            df = pb.from_sheets(sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Read Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Read {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and created a dataframe of size {size}\"\n", "            )\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# DataFrame to GSheet\n", "def df_to_gsheet(df, sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            pb.to_sheets(df, sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Write Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Write {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and for a dataframe of size {size}\"\n", "            )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# Convert Pandas DataFrame to Image\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5,\n", "    row_height=0.825,\n", "    font_size=18,\n", "    header_color=\"#fed8b1\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs,\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        bbox=bbox,\n", "        colLabels=data.columns,\n", "        cellLoc=\"center\",\n", "        **kwargs,\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "# Send to Slack\n", "def send_to_slack(channel_id, text_to_send=None, file_paths=None):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            if file_paths is None:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send)\n", "                print(\n", "                    f\"Message: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            else:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send, files=file_paths)\n", "                print(\n", "                    f\"Message with attached file: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "753670e4-fdfb-4661-b640-e133eafa2268", "metadata": {}, "outputs": [], "source": ["df = pb.from_sheets(\"1dL1grhTKlu5RcGH4T0It1Rg5y3MnKxweCKu8ssWixC8\", \"date_input\", clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "id": "976ae3bc-52a9-4189-ab8c-b3d674e588cf", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "766910a9-0490-4aab-a4cc-e6a0192639a1", "metadata": {}, "outputs": [], "source": ["holi_data = df[\n", "    [\n", "        \"festival\",\n", "        \"cpd_date\",\n", "        \"bau_start_date\",\n", "        \"bau_end_date\",\n", "        \"festival_start_date\",\n", "        \"festival_end_date\",\n", "        \"sale_start_date\",\n", "        \"sale_end_date\",\n", "        \"gap_days\",\n", "        \"flag\",\n", "        \"festival_name_in_bulk_upload\",\n", "    ]\n", "].copy()"]}, {"cell_type": "code", "execution_count": null, "id": "50c4542c-1a2e-4802-ac66-51c018a292a8", "metadata": {}, "outputs": [], "source": ["os.getcwd()"]}, {"cell_type": "markdown", "id": "692364fe-91d3-42c3-9dcf-f2d8fec5af98", "metadata": {}, "source": ["#### <span style=\"color:red\">Converting time to ist</span>"]}, {"cell_type": "code", "execution_count": null, "id": "1b98651e-3be5-4129-b7ff-487206fb38ee", "metadata": {}, "outputs": [], "source": ["ist_timezone = pytz.timezone(\"Asia/Kolkata\")\n", "ist_now = datetime.now(ist_timezone)"]}, {"cell_type": "code", "execution_count": null, "id": "8e3b0bc0-07e9-4a78-a8ed-866ea43ea34b", "metadata": {}, "outputs": [], "source": ["current_date = ist_now.date()\n", "current_date_str = current_date.strftime(\"%Y-%m-%d\")\n", "yesterday_date = current_date - <PERSON><PERSON><PERSON>(days=1)\n", "yesterday_date_str = yesterday_date.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "3acae2a7-9079-4a30-aa0e-cd56fe51c7df", "metadata": {}, "outputs": [], "source": ["festive_df = []\n", "for index, row in tqdm(holi_data.iterrows()):\n", "    # Check if for a particular fetival is finished\n", "    curr_date = datetime.strptime(current_date_str, \"%Y-%m-%d\")\n", "    sale_start = datetime.strptime(row[\"sale_start_date\"], \"%Y-%m-%d\") - <PERSON><PERSON><PERSON>(days=5)\n", "    sale_end = datetime.strptime(row[\"sale_end_date\"], \"%Y-%m-%d\") + <PERSON><PERSON><PERSON>(days=1)\n", "\n", "    # Check if t1 is between t2_start and t2_end\n", "\n", "    if sale_start <= curr_date <= sale_end:\n", "        continue\n", "\n", "    start_date = (pd.to_datetime(row[\"sale_start_date\"]) - timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "\n", "    festival_name = row[\"festival\"]\n", "    # Check if festival data is present in bulk upload\n", "\n", "    festival_name_in_bulk_upload = row[\"festival_name_in_bulk_upload\"]\n", "    sql = f\"\"\"\n", "            SELECT sei.id AS id,\n", "                CONCAT(name, '_', cast(start_date as varchar)) AS event_name\n", "            FROM rpc.supply_event_info sei\n", "            JOIN rpc.supply_event se ON se.id = sei.supply_event_id\n", "            WHERE sei.active = TRUE\n", "            AND se.active = TRUE\n", "            and CONCAT(name, '_', cast(start_date as varchar)) = '{festival_name}'\n", "            \"\"\"\n", "    df_dummy = read_sql_query(sql, CON_TRINO)\n", "    if len(df_dummy) == 0:\n", "        print(f\"no_data_for {festival_name}\")\n", "        continue\n", "\n", "    print(f\"started for festival {festival_name}\")\n", "\n", "    # Creating list of dates\n", "\n", "    start_date = datetime.strptime(row[\"sale_start_date\"], \"%Y-%m-%d\") - <PERSON><PERSON><PERSON>(days=5)\n", "    end_date = datetime.strptime(row[\"sale_end_date\"], \"%Y-%m-%d\") + <PERSON><PERSON><PERSON>(days=1)\n", "    date_list = [start_date + timedelta(days=i) for i in range((end_date - start_date).days + 1)]\n", "    list_of_dates = [date.strftime(\"%Y-%m-%d\") for date in date_list]\n", "    start_date = (\n", "        datetime.strptime(row[\"sale_start_date\"], \"%Y-%m-%d\") - <PERSON><PERSON><PERSON>(days=5)\n", "    ).strftime(\"%Y-%m-%d\")\n", "    end_date = (datetime.strptime(row[\"sale_end_date\"], \"%Y-%m-%d\") + timedelta(days=1)).strftime(\n", "        \"%Y-%m-%d\"\n", "    )\n", "\n", "    list_df = []\n", "\n", "    for date in list_of_dates:\n", "        sql = f\"\"\"\n", "\n", "    with bhw as \n", "        (\n", "            select cast(cast(backend_facility_id as double) as integer) as backend_facility_id, cast(order_hour as integer) as order_hour, cast(weights as double) as weights\n", "            from supply_etls.backend_hour_weights \n", "            where updated_at =(select max(updated_at) from supply_etls.backend_hour_weights where updated_at <= date '{start_date}')\n", "        ),\n", "        \n", "        bhw_fall as\n", "        (\n", "            select backend_facility_id, min(weights) as min_weight\n", "            from bhw \n", "            group by 1\n", "        ),\n", "        \n", "        bsw as \n", "        (\n", "            select cast(cast(backend_facility_id as double) as integer) as backend_facility_id, facility_id, \n", "            cast(store_weight as double) as store_weight\n", "            from supply_etls.backend_store_weights\n", "            where updated_at = (select max(updated_at) from supply_etls.backend_store_weights where updated_at <= date '{start_date}')\n", "        ), \n", "\n", "        bsw_fall_check as\n", "        (\n", "            select backend_facility_id, min(store_weight) as min_store_weight\n", "            from bsw \n", "            group by 1\n", "        ),\n", "        \n", "        ciw as \n", "        (\n", "            select city, item_id, cast(weights as double) as weight from\n", "            supply_etls.city_item_weights \n", "            where updated_at = (select max(updated_at) from supply_etls.city_item_weights  where updated_at <= date '{start_date}')\n", "            \n", "        ),\n", "        \n", "        csw as\n", "        (\n", "            select city, outlet_id, facility_id, cast(store_weight as double) as s_weight from \n", "            supply_etls.city_store_weights\n", "            where  updated_at = (select max(updated_at) from supply_etls.city_store_weights where updated_at <= date '{start_date}')\n", "            \n", "        ),\n", "        \n", "        chw as \n", "        (\n", "            select city, order_hour, cast(weights as double) as h_weight from\n", "            supply_etls.city_hour_weights\n", "            where updated_at = (select max(updated_at) from supply_etls.city_hour_weights where updated_at <= date '{start_date}')\n", "        ),\n", "        \n", "        outlet_details as \n", "        (\n", "            select city_name, facility_id\n", "            from supply_etls.outlet_details\n", "            where ars_active = 1 \n", "            and business_type_id = 7\n", "        ),\n", "        \n", "        city_weight as\n", "        (\n", "            select city, weight\n", "            from supply_etls.city_weights\n", "            where updated_at = (select max(updated_at) from supply_etls.city_weights where updated_at <= date '{start_date}')\n", "        ),\n", "        \n", "        csw_fall_back_check as\n", "        (\n", "            select city, min(s_weight) as min_store_weight\n", "            from csw \n", "            group by 1\n", "        ),\n", "        \n", "        \n", "        item_prop as \n", "        (\n", "            with raw as \n", "           (select \n", "                festival_name,\n", "                product_type,\n", "                try(sum(cy_search_device_count_festival) / sum(cy_search_device_count_bau)) as cy_search_spike,\n", "                try(sum(py_search_device_count_festival) / sum(py_search_device_count_bau)) as py_search_spike\n", "            from supply_etls.festival_forecast_final_date_city_ptype\n", "            where festival_name in ('{festival_name}')\n", "            group by 1,2\n", "            ),\n", "\n", "            item_flag as \n", "            (\n", "                select festival_name, product_type, cy_search_spike, py_search_spike,\n", "                case when cy_search_spike >= 1.5  then 1 else 0 end as critical_flag,\n", "                case when cy_search_spike >= 3 then 1 else 0 end as festive_core_flag\n", "                from raw\n", "            )\n", "\n", "            select * from item_flag\n", "\n", "        ),\n", "\n", "        biw as \n", "        (\n", "            select cast(cast(backend_facility_id as double) as integer) as backend_facility_id, item_id, cast(weights as double) as weights from supply_etls.backend_item_weights\n", "            where\n", "            updated_at = (select max(updated_at) from supply_etls.backend_item_weights where updated_at <= date '{start_date}')\n", "        ),\n", "\n", "        fmt as (\n", "            select cast(insert_ds_ist as varchar) as insert_ds_ist, hour_, be_facility_id, be_facility_name, fe_inv_outlet_id, fe_facility_id, fe_inv_outlet_name, express_longtail, l0_id, l0_category, l1_id, l1_category,\n", "                    l2_id, l2_category, p_type, item_id,\n", "                    case when item_substate = 1 then 'active' \n", "                         when item_substate = 2 then 'inactive' \n", "                         when item_substate = 3 then 'temp_inactive' \n", "                         else 'discontinued' end as assortment,\n", "                    fe_avail_flag, fe_inv,\n", "                    be_inv,active_outlet_flag\n", "                    po_cpd, sto_cpd, sto_aps_adjusted, qty_sold, gmv, sto_created, sto_qty_created, open_po_qty, overall_scheduled_po_qty, overall_unscheduled_po_qty\n", "            from supply_etls.inventory_replenishment_metrics \n", "            where insert_ds_ist = date '{date}' \n", "            -- date '2025-03-15'\n", "            -- insert_ds_ist = cast(current_timestamp - interval '55' minute as date)\n", "            and (active_outlet_flag=1 or active_outlet_flag is null) and item_substate in (1,3)\n", "        ),\n", "        \n", "        bebe_req as (\n", "            select insert_ds_ist, hour_, be_facility_id, fe_inv_outlet_id, item_id,\n", "                    case when fe_avail_flag = 0 and (2 * sto_cpd) <= 1 then 1\n", "                        when fe_avail_flag = 0 and (2 * sto_cpd) > 1 then (2 * sto_cpd)\n", "                        else 0\n", "                    end as req_inv\n", "            from fmt\n", "        ),\n", "\n", "        bebe_req_core as (\n", "            select insert_ds_ist, hour_, be_facility_id, item_id, sum(req_inv) as req_inv\n", "            from bebe_req\n", "            group by 1,2,3,4\n", "        ),\n", "\n", "        be_be as (\n", "            select fmt.insert_ds_ist, fmt.hour_, fmt.be_facility_id, fe_inv_outlet_id, \n", "                    fmt.item_id, \n", "                    case when req_inv <= be_inv then 1 else fe_avail_flag end as avail_flag\n", "                    \n", "            from fmt\n", "            left join bebe_req_core bc on bc.be_facility_id = fmt.be_facility_id and bc.item_id = fmt.item_id and bc.insert_ds_ist = fmt.insert_ds_ist\n", "                    and bc.hour_ = fmt.hour_\n", "        ),\n", "\n", "        festive_weights_biw as\n", "        (with raw as \n", "        (select \n", "                festival_name,\n", "                product_type,\n", "                try(sum(cy_search_device_count_festival) / sum(cy_search_device_count_bau)) as cy_search_spike,\n", "                try(sum(py_search_device_count_festival) / sum(py_search_device_count_bau)) as py_search_spike\n", "            from supply_etls.festival_forecast_final_date_city_ptype\n", "            where festival_name in ('{festival_name}')\n", "            group by 1,2\n", "        ),\n", "\n", "        item_flag as \n", "        (\n", "            select festival_name, product_type, cy_search_spike, py_search_spike,\n", "            case when cy_search_spike >= 1.5  then 1 else 0 end as critical_flag,\n", "            case when cy_search_spike >= 3 then 1 else 0 end as festive_core_flag\n", "            from raw\n", "        ),\n", "\n", "        event_name as \n", "        (\n", "                    SELECT sei.id AS id,\n", "                        CONCAT(name, '_', cast(start_date as varchar)) AS event_name\n", "                    FROM rpc.supply_event_info sei\n", "                    JOIN rpc.supply_event se ON se.id = sei.supply_event_id\n", "                    WHERE sei.active = TRUE\n", "                      AND se.active = TRUE\n", "                      and CONCAT(name, '_', cast(start_date as varchar)) = '{festival_name_in_bulk_upload}'\n", "\n", "        ),\n", "\n", "\n", "        plan_qty_raw as \n", "        (\n", "            select outlet_id, eoid.item_id, id.p_type,sum(quantity) as plan_qty from ars.event_outlet_item_distribution eoid inner join \n", "            supply_etls.item_details id \n", "            on eoid.item_id = id.item_id and id.assortment_type = 'Packaged Goods'\n", "            where event_id = (select id from event_name) and partition_field is not null -- event_id = 14 is for <PERSON><PERSON>\n", "            group by 1,2,3\n", "        ),\n", "\n", "        plan_qty_raw_filtered as \n", "        (   -- Filtering for only festive_core items\n", "            select outlet_id, item_id, plan_qty\n", "            from item_flag inner join plan_qty_raw pqr on item_flag.product_type = pqr.p_type and item_flag.festive_core_flag = 1\n", "        ),\n", "\n", "        fmt as (\n", "            select insert_ds_ist, hour_, be_facility_id, be_facility_name, fe_inv_outlet_id, fe_facility_id, fe_inv_outlet_name, express_longtail, l0_id, l0_category, l1_id, l1_category,\n", "                    l2_id, l2_category, p_type, item_id,\n", "                    case when item_substate = 1 then 'active' \n", "                         when item_substate = 2 then 'inactive' \n", "                         when item_substate = 3 then 'temp_inactive' \n", "                         else 'discontinued' end as assortment,\n", "                    fe_avail_flag, sto_cpd, be_inv,active_outlet_flag\n", "                    po_cpd, sto_cpd, sto_aps_adjusted, qty_sold, gmv, sto_created, sto_qty_created, open_po_qty, overall_scheduled_po_qty, overall_unscheduled_po_qty\n", "            from supply_etls.inventory_replenishment_metrics \n", "            where insert_ds_ist = date '{start_date}' -- Considering 5th march tea tagging for calculating festive weights\n", "            and hour_ = (select max(hour_) from supply_etls.inventory_replenishment_metrics where insert_ds_ist = date '{start_date}' )\n", "            and (active_outlet_flag=1 or active_outlet_flag is null) and item_substate in (1,3)\n", "        ),\n", "\n", "        festive_weight_sum as \n", "        (\n", "            select fmt.be_facility_id, fmt.be_facility_name, cast(sum(plan_qty) as double) as sum_qty\n", "            from plan_qty_raw_filtered pqrf inner join fmt on pqrf.outlet_id = fmt.fe_inv_outlet_id and pqrf.item_id = fmt.item_id\n", "            group by 1,2\n", "        ),\n", "\n", "        festive_weight as \n", "        (\n", "            select fmt.be_facility_id, fmt.be_facility_name, fmt.item_id, cast(sum(plan_qty) as double) as tot_qty\n", "            from plan_qty_raw_filtered pqrf inner join fmt on pqrf.outlet_id = fmt.fe_inv_outlet_id and pqrf.item_id = fmt.item_id \n", "            inner join festive_weight_sum fws on fws.be_facility_id = fmt.be_facility_id\n", "            group by 1,2,3\n", "        ),\n", "\n", "        festive_biw as \n", "        (\n", "            select fw.be_facility_id, fw.be_facility_name, fw.item_id, fw.tot_qty/NULLIF(fws.sum_qty,0) as weight\n", "            from festive_weight fw inner join festive_weight_sum fws on fw.be_facility_id = fws.be_facility_id\n", "        )\n", "\n", "        select * from festive_biw),\n", "        \n", "event_name as \n", "    (\n", "                SELECT sei.id AS id,\n", "                    CONCAT(name, '_', cast(start_date as varchar)) AS event_name\n", "                FROM rpc.supply_event_info sei\n", "                JOIN rpc.supply_event se ON se.id = sei.supply_event_id\n", "                WHERE sei.active = TRUE\n", "                  AND se.active = TRUE\n", "                  and CONCAT(name, '_', cast(start_date as varchar)) = '{festival_name_in_bulk_upload}'\n", "\n", "    ),\n", "    \n", "be_pan_india as (\n", "    with raw as (select irm.insert_ds_ist, irm.hour_, irm.be_facility_id, irm.be_facility_name, irm.fe_inv_outlet_id, irm.fe_facility_id, irm.fe_inv_outlet_name, \n", "           irm.item_id, sum(coalesce(oiadc.aps_adjusted,0)) as cpd\n", "    from supply_etls.inventory_replenishment_metrics irm\n", "    left join ars.outlet_item_aps_derived_cpd oiadc on irm.fe_inv_outlet_id = oiadc.outlet_id and irm.item_id = oiadc.item_id\n", "    where irm.insert_ds_ist = date '{start_date}'\n", "    and oiadc.insert_ds_ist = '{start_date}'\n", "    and hour_ = (select max(hour_) from supply_etls.inventory_replenishment_metrics where insert_ds_ist = date '{start_date}' )\n", "    and (active_outlet_flag=1 or active_outlet_flag is null) and item_substate in (1,3)\n", "    group by 1,2,3,4,5,6,7,8),\n", "    \n", "    be_raw as (select be_facility_id, sum(cpd) as final_ob\n", "    from raw \n", "    group by 1),\n", "    \n", "    be_tot as (\n", "    select sum(final_ob) as final_tot\n", "    from be_raw\n", "    )\n", "    \n", "    \n", "    select be_facility_id, final_ob/final_tot as weight \n", "    from be_raw br cross join be_tot bt \n", "    group by 1,2\n", "),\n", "\n", "\n", "final_raw as \n", "(\n", "\n", "    select en.event_name as  festival_name, en.id as supply_event_info_event_id,\n", "    fmt.* , bb.avail_flag as be_fe_avail_flag,\n", "    coalesce(od.city_name, 'NA') as city, ip.critical_flag, ip.festive_core_flag, \n", "    coalesce(biw.weights,0) as item_weight,\n", "    coalesce(bhw.weights,bf.min_weight,1) as hour_weight,\n", "    case when bfc.min_store_weight is not null then \n", "    coalesce(bsw.store_weight,0) else 1 end as store_weight,\n", "    coalesce(fwb.weight,0) as festive_item_weight,\n", "    coalesce(ciw.weight,0) as city_item_weight,\n", "    case when cf.min_store_weight is not null then \n", "    coalesce(csw.s_weight,0) else 1 end as city_store_weight,\n", "    coalesce(chw.h_weight,0) as city_hour_weight,\n", "    coalesce(bpi.weight,0) as be_pan_india_weight\n", "    \n", "    \n", "    from fmt left join item_prop ip on fmt.p_type = ip.product_type\n", "    left join biw on biw.backend_facility_id = fmt.be_facility_id and biw.item_id = fmt.item_id\n", "    left join bhw on bhw.backend_facility_id = fmt.be_facility_id and bhw.order_hour = fmt.hour_\n", "    left join bsw on bsw.backend_facility_id = fmt.be_facility_id and bsw.facility_id = fmt.fe_facility_id\n", "    left join bhw_fall bf on bf.backend_facility_id = fmt.be_facility_id\n", "    left join festive_weights_biw fwb on fwb.be_facility_id = fmt.be_facility_id and fwb.item_id = fmt.item_id\n", "    left join bsw_fall_check bfc on bfc.backend_facility_id = fmt.be_facility_id\n", "    left join outlet_details od on od.facility_id = fmt.fe_facility_id\n", "    left join ciw on ciw.city = od.city_name and ciw.item_id = fmt.item_id\n", "    left join csw on csw.city = od.city_name and fmt.fe_facility_id = csw.facility_id\n", "    left join csw_fall_back_check cf on cf.city = od.city_name\n", "    left join chw on chw.city = od.city_name and fmt.hour_ = chw.order_hour\n", "    left join be_pan_india bpi on bpi.be_facility_id = fmt.be_facility_id\n", "    \n", "    left join be_be bb on bb.insert_ds_ist = fmt.insert_ds_ist and bb.hour_ = fmt.hour_ \n", "    and bb.be_facility_id = fmt.be_facility_id and bb.fe_inv_outlet_id = fmt.fe_inv_outlet_id \n", "    and bb.item_id = fmt.item_id\n", "    \n", "    cross join event_name en\n", "\n", ")\n", "select * from final_raw\n", "\n", "\"\"\"\n", "\n", "        column_dtypes = [\n", "            {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"Festival Name\"},\n", "            {\n", "                \"name\": \"supply_event_info_event_id\",\n", "                \"type\": \"integer\",\n", "                \"description\": \"Supply Event Info Event ID\",\n", "            },\n", "            {\"name\": \"insert_ds_ist\", \"type\": \"varchar\", \"description\": \"Insert Date of Data\"},\n", "            {\"name\": \"hour_\", \"type\": \"integer\", \"description\": \"Hour of Data\"},\n", "            {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"Warehouse Facility ID\"},\n", "            {\n", "                \"name\": \"be_facility_name\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"Warehouse Facility Name\",\n", "            },\n", "            {\"name\": \"fe_inv_outlet_id\", \"type\": \"integer\", \"description\": \"Dark Store Outlet ID\"},\n", "            {\"name\": \"fe_facility_id\", \"type\": \"integer\", \"description\": \"Dark Store Facility ID\"},\n", "            {\n", "                \"name\": \"fe_inv_outlet_name\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"Dark Store Outlet Name\",\n", "            },\n", "            {\n", "                \"name\": \"express_longtail\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"Express Longtail Indicator\",\n", "            },\n", "            {\"name\": \"l0_id\", \"type\": \"integer\", \"description\": \"L0 ID\"},\n", "            {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \"L0 Category\"},\n", "            {\"name\": \"l1_id\", \"type\": \"integer\", \"description\": \"L1 ID\"},\n", "            {\"name\": \"l1_category\", \"type\": \"varchar\", \"description\": \"L1 Category\"},\n", "            {\"name\": \"l2_id\", \"type\": \"integer\", \"description\": \"L2 ID\"},\n", "            {\"name\": \"l2_category\", \"type\": \"varchar\", \"description\": \"L2 Category\"},\n", "            {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"Product Type\"},\n", "            {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"Item ID\"},\n", "            {\"name\": \"assortment\", \"type\": \"varchar\", \"description\": \"Assortment Type\"},\n", "            {\n", "                \"name\": \"fe_avail_flag\",\n", "                \"type\": \"integer\",\n", "                \"description\": \"Dark Store Availability Flag\",\n", "            },\n", "            {\"name\": \"fe_inv\", \"type\": \"real\", \"description\": \"Dark Store Inventory\"},\n", "            {\"name\": \"be_inv\", \"type\": \"real\", \"description\": \"Warehouse Inventory\"},\n", "            {\"name\": \"po_cpd\", \"type\": \"real\", \"description\": \"PO Ordering CPD\"},\n", "            {\"name\": \"sto_cpd\", \"type\": \"real\", \"description\": \"STO Transfer CPD\"},\n", "            {\"name\": \"sto_aps_adjusted\", \"type\": \"real\", \"description\": \"STO APS Adjusted\"},\n", "            {\n", "                \"name\": \"qty_sold\",\n", "                \"type\": \"real\",\n", "                \"description\": \"Quantity Sold for a particular day\",\n", "            },\n", "            {\"name\": \"gmv\", \"type\": \"real\", \"description\": \"GMV Sold for a particular day\"},\n", "            {\"name\": \"sto_created\", \"type\": \"real\", \"description\": \"STO Created\"},\n", "            {\"name\": \"sto_qty_created\", \"type\": \"real\", \"description\": \"STO Quantity Created\"},\n", "            {\"name\": \"open_po_qty\", \"type\": \"real\", \"description\": \"Open PO Quantity\"},\n", "            {\n", "                \"name\": \"overall_scheduled_po_qty\",\n", "                \"type\": \"real\",\n", "                \"description\": \"Overall Scheduled PO Quantity\",\n", "            },\n", "            {\n", "                \"name\": \"overall_unscheduled_po_qty\",\n", "                \"type\": \"real\",\n", "                \"description\": \"Overall Unscheduled PO Quantity\",\n", "            },\n", "            {\"name\": \"be_fe_avail_flag\", \"type\": \"integer\", \"description\": \"bexfe avail_flag\"},\n", "            {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"City Name\"},\n", "            {\"name\": \"critical_flag\", \"type\": \"real\", \"description\": \"Critical Flag Indicator\"},\n", "            {\n", "                \"name\": \"festive_core_flag\",\n", "                \"type\": \"real\",\n", "                \"description\": \"Festive Core Flag Indicator\",\n", "            },\n", "            {\"name\": \"item_weight\", \"type\": \"real\", \"description\": \"Backend Item Weight\"},\n", "            {\"name\": \"hour_weight\", \"type\": \"real\", \"description\": \"Backend Hour Weight\"},\n", "            {\"name\": \"store_weight\", \"type\": \"real\", \"description\": \"Backend Store Weight\"},\n", "            {\"name\": \"festive_item_weight\", \"type\": \"real\", \"description\": \"Festive Item Weight\"},\n", "            {\"name\": \"city_item_weight\", \"type\": \"real\", \"description\": \"City Item Weight\"},\n", "            {\"name\": \"city_store_weight\", \"type\": \"real\", \"description\": \"City Store Weight\"},\n", "            {\"name\": \"city_hour_weight\", \"type\": \"real\", \"description\": \"City Hour Weight\"},\n", "            {\"name\": \"be_pan_india_weight\", \"type\": \"real\", \"description\": \"be_pan_india_weight\"},\n", "        ]\n", "\n", "        kwargs = {\n", "            \"schema_name\": \"supply_etls\",\n", "            \"table_name\": \"festive_raw_data\",\n", "            \"column_dtypes\": column_dtypes,\n", "            \"primary_key\": [\n", "                \"festival_name\",\n", "                \"supply_event_info_event_id\",\n", "                \"insert_ds_ist\",\n", "                \"hour_\",\n", "                \"be_facility_id\",\n", "                \"fe_facility_id\",\n", "                \"item_id\",\n", "            ],\n", "            \"partition_key\": [\"insert_ds_ist\", \"hour_\"],\n", "            \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "            \"table_description\": \"This table contains raw data at festival<>hour<>date<>be<>fe<>item\",\n", "        }\n", "\n", "        try:\n", "\n", "            sql_test_1 = f\"\"\"\n", "                select count(*) as tot_rows from supply_etls.festive_raw_data \n", "            where festival_name = '{festival_name}' and insert_ds_ist = '{date}'\n", "            \"\"\"\n", "            sql_test_2 = f\"\"\"\n", "                select count(*) as tot_rows from supply_etls.inventory_replenishment_metrics\n", "            where insert_ds_ist = date '{date}'\n", "            and (active_outlet_flag=1 or active_outlet_flag is null) and item_substate in (1,3)\n", "            \"\"\"\n", "            con = pb.get_connection(\"[Warehouse] Trino\")\n", "            try:\n", "                df_1 = read_sql_query(sql=sql_test_1, con=con)\n", "                df_2 = read_sql_query(sql=sql_test_2, con=con)\n", "            except:\n", "                print(\"error\")\n", "            # print(len(df))\n", "            if df_1[\"tot_rows\"].iloc[0] == df_2[\"tot_rows\"].iloc[0]:\n", "                print(f\"data is already present for {festival_name},{date}\")\n", "                continue\n", "            else:\n", "                con = pb.get_connection(\"[Warehouse] Trino\")\n", "                to_trino(sql, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)\n", "                print(f\"done for date {festival_name + '_' + date}\")\n", "        except:\n", "            con = pb.get_connection(\"[Warehouse] Trino\")\n", "            # con = pb.get_connection(\"[Warehouse] Trino\")\n", "            # df_date = pd.read_sql_query(sql=sql, con=con)\n", "            # list_df.append(df_date)\n", "            to_trino(sql, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)\n", "            print(f\"done for date {festival_name + '_' + date}\")\n", "\n", "    # combined_df = pd.concat(list_df, ignore_index=True)\n", "\n", "    print(f\"done for festival {festival_name}\")\n", "    # festive_df.append(combined_df)\n", "    # break"]}, {"cell_type": "code", "execution_count": null, "id": "a8481671-f816-4931-a559-17f275ff28bf", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6fb526b5-e7b1-4c8a-a339-8c23952b9ff7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}