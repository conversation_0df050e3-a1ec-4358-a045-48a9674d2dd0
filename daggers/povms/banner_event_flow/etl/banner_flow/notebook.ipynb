{"cells": [{"cell_type": "code", "execution_count": null, "id": "2ccc7a5b-364a-49e2-8d2e-94e26154fa7f", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from calendar import monthrange\n", "from datetime import datetime, date, timedelta\n", "import math\n", "import warnings\n", "import time\n", "\n", "\n", "presto = pb.get_connection(\"[Warehouse] Presto\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "markdown", "id": "bd8c35ca-ceeb-47be-bbfd-ee36af0dab52", "metadata": {}, "source": ["# outlet details"]}, {"cell_type": "code", "execution_count": null, "id": "fbc39ad5-fdba-49c8-8183-3bae8196dcf0", "metadata": {}, "outputs": [], "source": ["def outlets():\n", "    outlets = \"\"\"\n", "    \n", "    select z.zone,\n", "        rcl.name as city_name,\n", "        om.outlet_id as hot_outlet_id, \n", "        om.facility_id, cf.facility_name,\n", "        case when wom.cloud_store_id is null then om.outlet_id else wom.cloud_store_id end as inv_outlet_id,\n", "        rco.business_type_id,\n", "        case when rco.business_type_id !=7 then 'be' else 'fe' end as taggings\n", "        \n", "            from lake_po.physical_facility_outlet_mapping om\n", "            \n", "            left join (select id, tax_location_id,\n", "                case when id = 581 then 12 else business_type_id end as business_type_id\n", "                    from lake_retail.console_outlet\n", "                        ) rco on rco.id = om.outlet_id\n", "            \n", "            left join (select id, name as facility_name \n", "                from lake_crates.facility\n", "                        ) cf on cf.id = om.facility_id\n", "            \n", "            left join (select distinct warehouse_id, cloud_store_id from lake_retail.warehouse_outlet_mapping \n", "                where active = 1\n", "                        ) wom on wom.warehouse_id = om.outlet_id\n", "            \n", "            left join lake_retail.console_location rcl on rcl.id = rco.tax_location_id\n", "            \n", "            left join (select distinct facility_id, zone from metrics.outlet_zone_mapping where business_type_id in (1,12,7,19,20,21)\n", "                        ) z on z.facility_id = om.facility_id\n", "            \n", "                where rco.business_type_id in (1,12,7,19,20,21) and om.outlet_id not in (0,1739)\n", "                and om.active = 1 and ars_active = 1 and is_primary = 1\n", "                and om.outlet_name not like '%%SSC%%'\n", "                and om.outlet_name not like '%%MODI%%'\n", "                and om.outlet_name not like '%%hot ff%%'\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(outlets, redshift)\n", "\n", "\n", "outlets = outlets()"]}, {"cell_type": "code", "execution_count": null, "id": "64058297-9031-4690-8110-a70fe00cfe36", "metadata": {}, "outputs": [], "source": ["outlets.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4a7733c3-e7a5-4c0e-a440-81b6d80e68e0", "metadata": {}, "outputs": [], "source": ["# all inv outlets\n", "all_inv_outlet_id = list(outlets[\"inv_outlet_id\"].unique())\n", "all_inv_outlet_id = tuple(all_inv_outlet_id)\n", "# len(all_inv_outlet_id)\n", "\n", "\n", "# all hot outlets\n", "all_hot_outlet_id = list(outlets[\"hot_outlet_id\"].unique())\n", "all_hot_outlet_id = tuple(all_hot_outlet_id)\n", "# len(all_hot_outlet_id)\n", "\n", "# all hot outlets_name\n", "all_hot_name = outlets[[\"hot_outlet_id\", \"inv_outlet_id\", \"facility_id\", \"facility_name\"]].rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_hot_outlet_id\",\n", "        \"inv_outlet_id\": \"be_inv_outlet_id\",\n", "        \"facility_id\": \"be_facility_id\",\n", "        \"facility_name\": \"be_facility_name\",\n", "    }\n", ")\n", "all_hot_name.head()\n", "\n", "# all facility\n", "all_facility_id = list(outlets[\"facility_id\"].unique())\n", "all_facility_id = tuple(all_facility_id)\n", "# len(all_facility_id)\n", "\n", "# frontend outlets\n", "frontend_outlet_details = outlets[outlets[\"taggings\"] == \"fe\"].reset_index().drop(columns={\"index\"})\n", "fe_facility_details = list(frontend_outlet_details[\"hot_outlet_id\"].unique())\n", "fe_facility_details = tuple(fe_facility_details)\n", "# len(fe_facility_details)\n", "\n", "\n", "# backend inv outlets\n", "be_inv_outlet_id = outlets[outlets[\"taggings\"] == \"be\"].reset_index().drop(columns={\"index\"})\n", "be_inv_outlet_id = list(be_inv_outlet_id[\"inv_outlet_id\"].unique())\n", "be_inv_outlet_id = tuple(be_inv_outlet_id)\n", "# len(be_inv_outlet_id)\n", "\n", "\n", "# backend hot outlets\n", "be_hot_outlet_id = outlets[outlets[\"taggings\"] == \"be\"].reset_index().drop(columns={\"index\"})\n", "be_hot_outlet_id = list(be_hot_outlet_id[\"hot_outlet_id\"].unique())\n", "be_hot_outlet_id = tuple(be_hot_outlet_id)\n", "# len(be_hot_outlet_id)"]}, {"cell_type": "markdown", "id": "525567b3-d11a-41d6-b8a2-1ba731a1c5ed", "metadata": {}, "source": ["# assortment details"]}, {"cell_type": "code", "execution_count": null, "id": "fc9f0402-6f6c-49fa-86cf-30cec30bf733", "metadata": {}, "outputs": [], "source": ["start = time.time()\n", "\n", "assortment = pb.from_sheets(\n", "    \"1Mng5N67HSoWkcasmZc2-AMrE3oGMx5khTvPN8j017TE\", \"Banner_Input\", clear_cache=True\n", ")\n", "assortment = assortment[\n", "    [\"city\", \"item_id\", \"go_live_date\", \"event_flag\", \"assortment_type\", \"active\"]\n", "]\n", "\n", "assortment = assortment.rename(columns={\"city\": \"city_name\"})\n", "\n", "assortment = assortment[\n", "    ~(\n", "        (assortment[\"city_name\"] == \"\")\n", "        | (assortment[\"item_id\"] == \"\")\n", "        | (assortment[\"go_live_date\"] == \"\")\n", "        | (assortment[\"event_flag\"] == \"\")\n", "        | (assortment[\"assortment_type\"] == \"\")\n", "        | (assortment[\"active\"] == \"\")\n", "    )\n", "]\n", "\n", "assortment[[\"item_id\", \"active\"]] = assortment[[\"item_id\", \"active\"]].astype(int)\n", "\n", "assortment[\"go_live_date\"] = pd.to_datetime(assortment[\"go_live_date\"])\n", "\n", "assortment.dropna(inplace=True)\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "ba128d3c-2fe3-4e32-a5c5-168c6619e42d", "metadata": {}, "outputs": [], "source": ["assortment.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6581f2d9-c36e-410a-b9d9-a6937c4e841a", "metadata": {}, "outputs": [], "source": ["item_id_list = list(assortment[\"item_id\"].unique())\n", "item_id_list = tuple(item_id_list)\n", "len(item_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "dcbaeb1c-1a48-4164-90a5-6e0a09b7426a", "metadata": {}, "outputs": [], "source": ["first_live_date = assortment.agg({\"go_live_date\": \"min\"}).reset_index().iloc[:, 1][0]\n", "first_live_date"]}, {"cell_type": "markdown", "id": "420327a1-8696-4e3d-bcba-3ce8c04033de", "metadata": {}, "source": ["# Assortment base setup"]}, {"cell_type": "code", "execution_count": null, "id": "a04d69eb-49c4-4e21-9546-6be3519380af", "metadata": {}, "outputs": [], "source": ["final_assortment = pd.merge(frontend_outlet_details, assortment, on=[\"city_name\"], how=\"inner\")\n", "\n", "final_assortment = final_assortment[\n", "    [\n", "        \"zone\",\n", "        \"city_name\",\n", "        \"facility_id\",\n", "        \"hot_outlet_id\",\n", "        \"facility_name\",\n", "        \"item_id\",\n", "        \"go_live_date\",\n", "        \"event_flag\",\n", "        \"assortment_type\",\n", "    ]\n", "]\n", "\n", "final_assortment.head()"]}, {"cell_type": "markdown", "id": "17f5f48f-71a0-4e7e-8cef-4086dd9cec60", "metadata": {}, "source": ["# item details"]}, {"cell_type": "code", "execution_count": null, "id": "4b2f964f-ba9d-4bb6-92e7-1ef4bfacfcdb", "metadata": {}, "outputs": [], "source": ["def item_details():\n", "    item_details = \"\"\"\n", "    \n", "    with\n", "    item_details as\n", "        (select rpp.item_id, (rpp.name || ' ' || variant_description) as item_name, p_type,\n", "            \n", "            l0, l1, l2, pb.manufacturer_id, pm.name as manufacturer_name\n", "\n", "                    from lake_rpc.product_product rpp\n", "\n", "                        join lake_rpc.item_category_details cd on cd.item_id = rpp.item_id\n", "\n", "                        left join (select item_id, product_id from lake_rpc.item_product_mapping) ipm on ipm.item_id = rpp.item_id\n", "                        left join (select id, type_id from lake_cms.gr_product) gp on gp.id = ipm.product_id\n", "                        left join (select id, name as p_type from lake_cms.gr_product_type) pt on pt.id = gp.type_id\n", "\n", "                        left join lake_rpc.product_brand pb on pb.id = rpp.brand_id\n", "                        left join lake_rpc.product_manufacturer pm on pm.id = pb.manufacturer_id\n", "\n", "                            where rpp.id in (select max(id) as id from lake_rpc.product_product where active = 1 and approved = 1 group by item_id)\n", "        )\n", "        \n", "            select * from item_details\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(item_details, redshift)\n", "\n", "\n", "start = time.time()\n", "\n", "item_details = item_details()\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "4904f8d5-01e6-4387-b885-2f3cbb43d69d", "metadata": {}, "outputs": [], "source": ["item_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f9c180d0-bf63-411b-99b5-8a8bf22e610c", "metadata": {}, "outputs": [], "source": ["adding_item_details = pd.merge(final_assortment, item_details, on=[\"item_id\"], how=\"left\")\n", "\n", "adding_item_details.head()"]}, {"cell_type": "markdown", "id": "26b5225f-e404-4d55-b7ea-f24ad9e5c553", "metadata": {}, "source": ["# active assortment details"]}, {"cell_type": "code", "execution_count": null, "id": "46996b65-dddc-428b-935a-7b9f3a1bc0a0", "metadata": {}, "outputs": [], "source": ["def active_assortment():\n", "    active_assortment = f\"\"\"\n", "    \n", "    with\n", "    active_assortment as\n", "        (select item_id, facility_id, master_assortment_substate_id as status\n", "            from lake_view_rpc.product_facility_master_assortment\n", "                where active = 1\n", "        )\n", "        \n", "            select * from active_assortment where item_id in {item_id_list} and facility_id in {fe_facility_details}\n", "    \"\"\"\n", "    return pd.read_sql_query(active_assortment, presto)\n", "\n", "\n", "start = time.time()\n", "active_assortment = active_assortment()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "c65160f8-410b-4210-a723-4688232dc792", "metadata": {}, "outputs": [], "source": ["active_assortment.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ad079b84-dd04-4ed4-bd65-4c6206f10eff", "metadata": {}, "outputs": [], "source": ["adding_active_assortment = pd.merge(\n", "    adding_item_details, active_assortment, on=[\"item_id\", \"facility_id\"], how=\"left\"\n", ")\n", "\n", "adding_active_assortment[\"status\"] = adding_active_assortment[\"status\"].fillna(0).astype(int)\n", "\n", "adding_active_assortment[\"assortment_status\"] = np.where(\n", "    (adding_active_assortment[\"status\"] == 0),\n", "    \"not_part_of_assortment\",\n", "    \"part_of_assortment\",\n", ")\n", "\n", "adding_active_assortment.head()"]}, {"cell_type": "markdown", "id": "f494387e-a333-4bba-8575-694330bf7057", "metadata": {}, "source": ["# tea tagging details"]}, {"cell_type": "code", "execution_count": null, "id": "c32f9319-d595-435c-aeb2-fea10f9a8491", "metadata": {}, "outputs": [], "source": ["def tea_taggings():\n", "    tea_taggings = f\"\"\"\n", "    \n", "    with\n", "    tea_taggings as\n", "        (select item_id, outlet_id as hot_outlet_id, tag_value as be_hot_outlet_id\n", "            from lake_view_rpc.item_outlet_tag_mapping\n", "                where tag_type_id = 8 and active = 1\n", "        )\n", "        \n", "            select * from tea_taggings where item_id in {item_id_list} and hot_outlet_id in {fe_facility_details}\n", "    \"\"\"\n", "    return pd.read_sql_query(tea_taggings, presto)\n", "\n", "\n", "start = time.time()\n", "tea_taggings = tea_taggings()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "803844b2-86df-4fcc-b7c6-bd2d34d4fdea", "metadata": {}, "outputs": [], "source": ["tea_taggings.head()"]}, {"cell_type": "code", "execution_count": null, "id": "cab2b75b-a7cc-482a-9f38-140769fe5a10", "metadata": {}, "outputs": [], "source": ["tea_taggings[\"be_hot_outlet_id\"] = tea_taggings[\"be_hot_outlet_id\"].astype(int)\n", "\n", "tea_taggings_adding_be = pd.merge(tea_taggings, all_hot_name, on=[\"be_hot_outlet_id\"], how=\"inner\")\n", "\n", "tea_taggings_adding_be.head()"]}, {"cell_type": "markdown", "id": "049ff9f8-a8f9-42eb-a60c-ebc2e086d62c", "metadata": {}, "source": ["# ARS mapping"]}, {"cell_type": "code", "execution_count": null, "id": "ba7bbdd3-f6e3-4b43-9404-d2632c77b3e2", "metadata": {}, "outputs": [], "source": ["def ars_mapping():\n", "    ars_mapping = \"\"\"\n", "    \n", "    with\n", "    ars_mapping as\n", "        (select facility_id as be_facility_id, outlet_id as hot_outlet_id\n", "\n", "            from lake_view_po.bulk_facility_outlet_mapping\n", "\n", "                where active = true\n", "        )\n", "\n", "            select * from ars_mapping\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(ars_mapping, presto)\n", "\n", "\n", "start = time.time()\n", "ars_mapping = ars_mapping()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "a2f44fe8-6c82-41f5-ad0f-b014bc496da5", "metadata": {}, "outputs": [], "source": ["ars_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "481f475d-26a8-47da-a4f5-4433ba60fd45", "metadata": {}, "outputs": [], "source": ["adding_ars_mapping = pd.merge(\n", "    tea_taggings_adding_be,\n", "    ars_mapping,\n", "    on=[\"be_facility_id\", \"hot_outlet_id\"],\n", "    how=\"inner\",\n", ")\n", "\n", "adding_ars_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "15de9091-d5af-4f60-a568-65a218d81730", "metadata": {}, "outputs": [], "source": ["adding_tea_details = pd.merge(\n", "    adding_active_assortment,\n", "    adding_ars_mapping,\n", "    on=[\"item_id\", \"hot_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_tea_details[[\"be_hot_outlet_id\", \"be_inv_outlet_id\", \"be_facility_id\"]] = (\n", "    adding_tea_details[[\"be_hot_outlet_id\", \"be_inv_outlet_id\", \"be_facility_id\"]]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_tea_details[\"be_facility_name\"] = adding_tea_details[\"be_facility_name\"].fillna(\n", "    \"No Backend Tagged\"\n", ")\n", "\n", "adding_tea_details.head()"]}, {"cell_type": "markdown", "id": "fc96c829-070f-4b1b-8718-162c9151ab05", "metadata": {}, "source": ["# inventory details"]}, {"cell_type": "code", "execution_count": null, "id": "b5f26958-ad40-4f2b-9e9a-a152580f31af", "metadata": {}, "outputs": [], "source": ["def inventory():\n", "    inventory = f\"\"\"\n", "    \n", "    with\n", "    inv as\n", "        (select item_id, outlet_id as hot_outlet_id, sum(quantity) as actual_inv\n", "            from lake_view_ims.ims_item_inventory\n", "                where active = 1\n", "                    group by 1,2\n", "        )\n", "        \n", "            select * from inv where item_id in {item_id_list} and hot_outlet_id in {all_inv_outlet_id}\n", "    \"\"\"\n", "    return pd.read_sql_query(inventory, presto)\n", "\n", "\n", "start = time.time()\n", "inventory = inventory()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "ec544ded-f16a-4173-9dc1-133dfbdf9642", "metadata": {}, "outputs": [], "source": ["inventory.head()"]}, {"cell_type": "code", "execution_count": null, "id": "48cdfe50-7a4c-4f96-b0a5-3621470640ff", "metadata": {}, "outputs": [], "source": ["be_inv = inventory.rename(\n", "    columns={\"hot_outlet_id\": \"be_inv_outlet_id\", \"actual_inv\": \"be_actual_inv\"}\n", ")\n", "be_inv.head()"]}, {"cell_type": "code", "execution_count": null, "id": "facb0530-69d6-4cf8-b5f6-cc8645f5aa31", "metadata": {}, "outputs": [], "source": ["adding_fe_inv_details = pd.merge(\n", "    adding_tea_details, inventory, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "adding_be_inv_details = pd.merge(\n", "    adding_fe_inv_details, be_inv, on=[\"item_id\", \"be_inv_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_inv_details[[\"actual_inv\", \"be_actual_inv\"]] = (\n", "    adding_be_inv_details[[\"actual_inv\", \"be_actual_inv\"]].fillna(0).astype(int)\n", ")\n", "\n", "adding_be_inv_details.head()"]}, {"cell_type": "markdown", "id": "6f4bf9af-4378-4523-b7e9-24476a2ac8d2", "metadata": {}, "source": ["# blocked inv details"]}, {"cell_type": "code", "execution_count": null, "id": "99aa4b9f-376a-4991-a1f8-9446043ed8f3", "metadata": {}, "outputs": [], "source": ["def blocked_inv():\n", "    blocked_inv = f\"\"\"\n", "    \n", "    with\n", "    blocked as\n", "        (select item_id, outlet_id as hot_outlet_id,\n", "            sum(case when blocked_type in (1,2,5) then quantity else 0 end) as ttl_blocked_qty\n", "                from lake_view_ims.ims_item_blocked_inventory\n", "                    where active = 1 and blocked_type in (1,2,5)\n", "                        group by 1,2\n", "        )\n", "        \n", "            select * from blocked where item_id in {item_id_list} and hot_outlet_id in {all_inv_outlet_id}\n", "    \"\"\"\n", "    return pd.read_sql_query(blocked_inv, presto)\n", "\n", "\n", "start = time.time()\n", "blocked_inv = blocked_inv()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "d43d73e5-76c7-48e3-81ff-6e09b1946ca1", "metadata": {}, "outputs": [], "source": ["blocked_inv.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d460d147-620a-4e9e-9f7f-71b7077f22c0", "metadata": {}, "outputs": [], "source": ["be_blocked = blocked_inv.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_inv_outlet_id\",\n", "        \"ttl_blocked_qty\": \"be_ttl_blocked_qty\",\n", "    }\n", ")\n", "be_blocked.head()"]}, {"cell_type": "code", "execution_count": null, "id": "25936a8f-b941-4ddc-8a74-517f08b800b5", "metadata": {}, "outputs": [], "source": ["adding_fe_blocked_details = pd.merge(\n", "    adding_be_inv_details, blocked_inv, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_blocked_details = pd.merge(\n", "    adding_fe_blocked_details,\n", "    be_blocked,\n", "    on=[\"item_id\", \"be_inv_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_be_blocked_details[[\"ttl_blocked_qty\", \"be_ttl_blocked_qty\"]] = (\n", "    adding_be_blocked_details[[\"ttl_blocked_qty\", \"be_ttl_blocked_qty\"]].fillna(0).astype(int)\n", ")\n", "\n", "adding_be_blocked_details[\"net_inventory\"] = np.where(\n", "    (adding_be_blocked_details[\"actual_inv\"] - adding_be_blocked_details[\"ttl_blocked_qty\"]) < 0,\n", "    0,\n", "    (adding_be_blocked_details[\"actual_inv\"] - adding_be_blocked_details[\"ttl_blocked_qty\"]),\n", ")\n", "\n", "adding_be_blocked_details[\"be_net_inventory\"] = np.where(\n", "    (adding_be_blocked_details[\"be_actual_inv\"] - adding_be_blocked_details[\"be_ttl_blocked_qty\"])\n", "    < 0,\n", "    0,\n", "    (adding_be_blocked_details[\"be_actual_inv\"] - adding_be_blocked_details[\"be_ttl_blocked_qty\"]),\n", ")\n", "\n", "\n", "adding_be_blocked_details.head()"]}, {"cell_type": "markdown", "id": "33a114e8-90c8-42a4-814c-97f314e48cbf", "metadata": {}, "source": ["# pending putaway"]}, {"cell_type": "code", "execution_count": null, "id": "0679d3a2-75b6-4007-a28f-151c7c034d53", "metadata": {}, "outputs": [], "source": ["def pen_put():\n", "    pen_put = f\"\"\"\n", "    \n", "    with\n", "    pp as\n", "        (select rpc.item_id, outlet_id as hot_outlet_id, sum(quantity) as pending_putaway\n", "            from lake_view_ims.ims_good_inventory igi\n", "                join lake_view_rpc.product_product rpc on rpc.upc = igi.upc_id and igi.variant_id = rpc.variant_id\n", "                    where igi.active = 1 and igi.inventory_update_type_id in (28,76)\n", "                        group by 1,2\n", "        )\n", "        \n", "            select * from pp where item_id in {item_id_list} and hot_outlet_id in {all_inv_outlet_id}\n", "    \"\"\"\n", "    return pd.read_sql_query(pen_put, presto)\n", "\n", "\n", "start = time.time()\n", "pen_put = pen_put()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "1cb6ca6d-843b-4f11-bc96-ebcc6e222306", "metadata": {}, "outputs": [], "source": ["pen_put.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bd6d53f0-87ae-4891-9ec9-5155e4cfaf73", "metadata": {}, "outputs": [], "source": ["be_pen_put = pen_put.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_inv_outlet_id\",\n", "        \"pending_putaway\": \"be_pending_putaway\",\n", "    }\n", ")\n", "be_pen_put.head()"]}, {"cell_type": "code", "execution_count": null, "id": "dabcb4d8-f648-43ac-99f0-08d3a266f1a2", "metadata": {}, "outputs": [], "source": ["adding_fe_pen_putaway_details = pd.merge(\n", "    adding_be_blocked_details, pen_put, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_pen_putaway_details = pd.merge(\n", "    adding_fe_pen_putaway_details,\n", "    be_pen_put,\n", "    on=[\"item_id\", \"be_inv_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "adding_be_pen_putaway_details[[\"pending_putaway\", \"be_pending_putaway\"]] = (\n", "    adding_be_pen_putaway_details[[\"pending_putaway\", \"be_pending_putaway\"]].fillna(0).astype(int)\n", ")\n", "\n", "adding_be_pen_putaway_details.head()"]}, {"cell_type": "markdown", "id": "a64ed5f6-813c-4e8f-8333-b10b50bdfe03", "metadata": {}, "source": ["# open STO details"]}, {"cell_type": "code", "execution_count": null, "id": "d5ee4bf0-728c-43b1-a9c7-b703f0333955", "metadata": {}, "outputs": [], "source": ["def sto_details():\n", "    sto_details = f\"\"\"\n", "    \n", "    with\n", "    inward_invoice_details as\n", "        (select vendor_invoice_id from lake_view_ims.ims_inward_invoice\n", "            where cast(insert_ds_ist as date) >= current_date - interval '40' day\n", "                and source_type in (2)\n", "        ),\n", "\n", "    billed_invoice as\n", "        (select grofers_order_id as sto_id, id, outlet_id, \n", "            case when iid.vendor_invoice_id is null then 'raised' else 'grn_done' end as invoice_status\n", "\n", "                from lake_view_pos.pos_invoice pi\n", "\n", "                    left join inward_invoice_details iid on iid.vendor_invoice_id = pi.invoice_id\n", "\n", "                    where cast(insert_ds_ist as date) >= current_date - interval '40' day\n", "                        and invoice_type_id in (5,14,16)\n", "        ),\n", "\n", "    invoice_item_details as\n", "        (select item_id, outlet_id, sto_id, sum(quantity) as billed_qty\n", "\n", "            from lake_view_pos.pos_invoice_product_details pd\n", "\n", "                join (select item_id, upc from lake_view_rpc.product_product\n", "                    where id in (select max(id) as id from lake_rpc.product_product where active = 1 and approved = 1 group by upc)\n", "                        ) rpp on rpp.upc = pd.upc_id\n", "\n", "                join billed_invoice bi on bi.id = pd.invoice_id and invoice_status in ('raised')\n", "\n", "                    where insert_ds_ist >= cast((current_date - interval '41' day) as varchar)\n", "\n", "                        group by 1,2,3\n", "        ),\n", "\n", "    sto_id as\n", "        (select sd.merchant_outlet_id, sd.sto_id, invoice_status from lake_view_ims.ims_sto_details sd\n", "            left join billed_invoice bi on cast(bi.sto_id as bigint) = sd.sto_id\n", "                where created_at between cast(current_date as timestamp)-interval '40' day - interval '330' minute and\n", "                    cast(current_date as timestamp)+interval '1' day - interval '330' minute\n", "                    and sto_state in (2,3) and invoice_status not in ('grn_done')\n", "        ),\n", "\n", "    sto_item_details as\n", "        (select sto_id, item_id, reserved_quantity as sto_quantity\n", "\n", "            from lake_view_po.sto_items\n", "\n", "                where created_at between cast(current_date as timestamp)-interval '40' day - interval '330' minute and\n", "                    cast(current_date as timestamp)+interval '1' day - interval '330' minute\n", "        ),\n", "\n", "    final as\n", "        (select sid.sto_id, si.merchant_outlet_id, sid.item_id, sid.sto_quantity, iid.billed_qty,\n", "            case when iid.sto_id is null then sid.sto_quantity else iid.billed_qty end as open_sto_qty\n", "\n", "                from sto_item_details sid\n", "\n", "                    join sto_id si on si.sto_id = sid.sto_id\n", "                    left join invoice_item_details iid on cast(iid.sto_id as int) = sid.sto_id and iid.item_id = sid.item_id\n", "        )\n", "\n", "            select merchant_outlet_id as hot_outlet_id, item_id, sum(open_sto_qty) as open_sto_qty\n", "\n", "                from final\n", "                    \n", "                    where item_id in {item_id_list} and merchant_outlet_id in {all_inv_outlet_id}\n", "                    \n", "                        group by 1,2\n", "    \"\"\"\n", "    return pd.read_sql_query(sto_details, presto)\n", "\n", "\n", "start = time.time()\n", "sto_details = sto_details()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "57abe229-8ab9-4efc-a340-3228f43a83b3", "metadata": {}, "outputs": [], "source": ["sto_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "5fb2c67e-857c-43fe-ba82-4b0202b4b710", "metadata": {}, "outputs": [], "source": ["be_sto = sto_details.rename(\n", "    columns={\"hot_outlet_id\": \"be_inv_outlet_id\", \"open_sto_qty\": \"be_open_sto_qty\"}\n", ")\n", "be_sto.head()"]}, {"cell_type": "code", "execution_count": null, "id": "72d03714-f9b1-4b1e-8ab1-54998890370e", "metadata": {}, "outputs": [], "source": ["adding_fe_sto_details = pd.merge(\n", "    adding_be_pen_putaway_details,\n", "    sto_details,\n", "    on=[\"item_id\", \"hot_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_be_sto_details = pd.merge(\n", "    adding_fe_sto_details, be_sto, on=[\"item_id\", \"be_inv_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_sto_details[[\"open_sto_qty\", \"be_open_sto_qty\"]] = (\n", "    adding_be_sto_details[[\"open_sto_qty\", \"be_open_sto_qty\"]].fillna(0).astype(int)\n", ")\n", "\n", "adding_be_sto_details.head()"]}, {"cell_type": "markdown", "id": "606b2761-b51f-4d9d-bcb4-350d10929c5e", "metadata": {}, "source": ["# PO details"]}, {"cell_type": "code", "execution_count": null, "id": "a2caf877-4560-4af6-83e2-bfc736c8e012", "metadata": {}, "outputs": [], "source": ["def po_details():\n", "    po_details = f\"\"\"\n", "    \n", "    with\n", "    open_po_details as\n", "        (select po_number, po_scheduled_date, po.outlet_id as hot_outlet_id, poi.item_id,\n", "            poi.units_ordered as po_quantity, \n", "            case when grn.grn_quantity is null then 0 else grn.grn_quantity end as grn_quantity,\n", "            case when (ppos.po_state_id in (8) or (ppos.po_state_id in (9) and po.is_multiple_grn != 1)) then 0 else 1 end po_status\n", "\n", "                from lake_view_po.purchase_order_items poi\n", "\n", "                    join lake_view_po.purchase_order po on po.id = poi.po_id\n", "\n", "                    join lake_view_po.purchase_order_status ppos on ppos.po_id = po.id\n", "\n", "                    left join (select po_id_id, date(schedule_date_time + interval '330' minute) as po_scheduled_date\n", "                        from lake_view_po.po_schedule) ps on ps.po_id_id = po.id\n", "\n", "                    left join (select po_id, item_id, sum(quantity) as grn_quantity\n", "                        from lake_view_po.po_grn\n", "                            where created_at >= cast('{first_live_date}' as timestamp) - interval '330' minute\n", "                                group by 1,2\n", "                                ) grn on grn.item_id = poi.item_id and grn.po_id = poi.po_id\n", "\n", "                        where ppos.po_state_id in (2,3,8,9,13,14,15)\n", "                            and ppos.po_state_id not in (4,5,10) and po.po_type_id != 11\n", "                            and po.created_at >= cast('{first_live_date}' as timestamp) - interval '330' minute\n", "        ),\n", "\n", "    final as\n", "        (select po_number,\n", "            case when po_status = 0 then null else po_scheduled_date end po_scheduled_date,\n", "            hot_outlet_id, item_id, po_quantity, grn_quantity,\n", "            case when po_status = 0 then 0 else (po_quantity - grn_quantity) end as open_po_quantity\n", "\n", "                from open_po_details\n", "        )\n", "\n", "            select hot_outlet_id, item_id, sum(po_quantity) as ttl_po_quantity, sum(grn_quantity) as ttl_grn_quantity,\n", "                sum(open_po_quantity) as ttl_open_po_quantity\n", "\n", "                    from final\n", "                    \n", "                        where item_id in {item_id_list} and hot_outlet_id in {all_hot_outlet_id}\n", "\n", "                            group by 1,2\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(po_details, presto)\n", "\n", "\n", "start = time.time()\n", "po_details = po_details()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "1c866878-1fd7-4c08-a211-b5dbe6e3b102", "metadata": {}, "outputs": [], "source": ["po_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8364b261-74e7-40c5-8b6e-dc47a597d3b8", "metadata": {}, "outputs": [], "source": ["be_po_details = po_details.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_hot_outlet_id\",\n", "        \"ttl_po_quantity\": \"be_ttl_po_quantity\",\n", "        \"ttl_grn_quantity\": \"be_ttl_grn_quantity\",\n", "        \"ttl_open_po_quantity\": \"be_ttl_open_po_quantity\",\n", "    }\n", ")\n", "\n", "be_po_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "33a873d8-62ad-4927-9fc5-b870e2ca999a", "metadata": {}, "outputs": [], "source": ["adding_fe_po_details = pd.merge(\n", "    adding_be_sto_details, po_details, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_po_details = pd.merge(\n", "    adding_fe_po_details, be_po_details, on=[\"item_id\", \"be_hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_po_details[\n", "    [\n", "        \"ttl_po_quantity\",\n", "        \"ttl_grn_quantity\",\n", "        \"ttl_open_po_quantity\",\n", "        \"be_ttl_po_quantity\",\n", "        \"be_ttl_grn_quantity\",\n", "        \"be_ttl_open_po_quantity\",\n", "    ]\n", "] = (\n", "    adding_be_po_details[\n", "        [\n", "            \"ttl_po_quantity\",\n", "            \"ttl_grn_quantity\",\n", "            \"ttl_open_po_quantity\",\n", "            \"be_ttl_po_quantity\",\n", "            \"be_ttl_grn_quantity\",\n", "            \"be_ttl_open_po_quantity\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_be_po_details.head()"]}, {"cell_type": "markdown", "id": "a621b85c-e9fc-4f34-9a75-e93164877a9d", "metadata": {}, "source": ["# frontend min max details"]}, {"cell_type": "code", "execution_count": null, "id": "6b46e1cb-4687-44ac-a7c4-4fb9f30e00b1", "metadata": {}, "outputs": [], "source": ["def min_max():\n", "    min_max = f\"\"\"\n", "    \n", "    with\n", "    min_max as\n", "        (select facility_id, item_id, min_quantity, max_quantity\n", "        \n", "            from lake_view_ars.item_min_max_quantity\n", "        )\n", "        \n", "            select * from min_max where item_id in {item_id_list} and facility_id in {all_facility_id}\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(min_max, presto)\n", "\n", "\n", "start = time.time()\n", "min_max = min_max()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "114bfcd6-c288-4a6c-9d68-6bba8020e0ba", "metadata": {}, "outputs": [], "source": ["min_max.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bf8289f0-2164-4a8d-a463-360835baf721", "metadata": {}, "outputs": [], "source": ["adding_min_max = pd.merge(adding_be_po_details, min_max, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "adding_min_max[[\"min_quantity\", \"max_quantity\"]] = (\n", "    adding_min_max[[\"min_quantity\", \"max_quantity\"]].fillna(0).astype(int)\n", ")\n", "\n", "adding_min_max.head()"]}, {"cell_type": "markdown", "id": "1df50ec2-be44-4ac2-8f59-5cadf18efa2f", "metadata": {}, "source": ["# sales details"]}, {"cell_type": "code", "execution_count": null, "id": "83a1eb8a-810d-4be5-93cd-e1f4829f173e", "metadata": {}, "outputs": [], "source": ["def sales():\n", "    sales = f\"\"\"\n", "    \n", "    with\n", "    sales as\n", "        (select\n", "            date(oi.install_ts + interval '5.5 Hours') as date_,\n", "            od.hot_outlet_id,\n", "            oi.product_id,\n", "            pl.item_id,\n", "            oi.selling_price,\n", "            (oi.selling_price*1.000/pl.multiplier+0.000) as item_selling_price,\n", "            oi.order_id,\n", "            pl.multiplier as combo_qty,\n", "            (oi.quantity * combo_qty) as qty,\n", "            ((oi.quantity * combo_qty) - (oi.cancelled_quantity * combo_qty)) as f_ordered_qty\n", "\n", "                from lake_oms_bifrost.oms_order_item oi\n", "\n", "                    left join (select distinct item_id, product_id, coalesce(multiplier,1) as multiplier \n", "                        from dwh.dim_item_product_offer_mapping\n", "                                ) pl on pl.product_id = oi.product_id\n", "\n", "                    left join lake_oms_bifrost.oms_order oo on oo.id = oi.order_id and oo.type in ('RetailForwardOrder','RetailSuborder','')\n", "\n", "                    join (select distinct outlet as hot_outlet_id, ancestor from lake_ims.ims_order_details od\n", "                        join lake_retail.console_outlet rco on rco.id = outlet and business_type_id in (7)\n", "                            where status_id not in (3,4,5) and od.created_at >= cast('{first_live_date}' - 2 as timestamp) - interval '5.5 Hours'\n", "                                ) od on od.ancestor = oi.order_id\n", "\n", "                    where oi.install_ts >= cast('{first_live_date}' as timestamp) - interval '5.5 Hours'\n", "        ),\n", "\n", "    final_sales as\n", "        (select order_id, date_, hot_outlet_id, item_id, sum(f_ordered_qty) as sales_qty,\n", "            avg(item_selling_price) as avg_selling_price, (sales_qty * avg_selling_price) as sales_value\n", "                from sales\n", "                    group by 1,2,3,4\n", "        ),\n", "\n", "    final as\n", "        (select date_, hot_outlet_id, item_id, count(distinct order_id) as carts, sum(sales_qty) as sales_qty, sum(sales_value) as sales_value\n", "            from final_sales\n", "                group by 1,2,3\n", "        )\n", "\n", "            select hot_outlet_id, item_id,\n", "                sum(case when date_ = current_date then sales_qty end) as today_sales_qty,\n", "                sum(case when date_ = current_date then sales_value end) as today_sales_value,\n", "                sum(case when date_ = current_date then carts end) as today_carts,\n", "\n", "                sum(case when date_ = current_date - 1 then sales_qty end) as yesterday_sales_qty,\n", "                sum(case when date_ = current_date - 1 then sales_value end) as yesterday_sales_value,\n", "                sum(case when date_ = current_date - 1 then carts end) as yesterday_carts,\n", "\n", "                sum(case when date_ between current_date - 7 and current_date - 1 then sales_qty end) as l7days_sales_qty,\n", "                sum(case when date_ between current_date - 7 and current_date - 1 then sales_value end) as l7days_sales_value,\n", "                sum(case when date_ between current_date - 7 and current_date - 1 then carts end) as l7days_carts,\n", "\n", "                sum(sales_qty) as ttl_sales_qty,\n", "                sum(sales_value) as ttl_sales_value,\n", "                sum(carts) as ttl_carts\n", "\n", "                    from final\n", "\n", "                        where item_id in {item_id_list} and hot_outlet_id in {all_inv_outlet_id}\n", "\n", "                            group by 1,2\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(sales, redshift)\n", "\n", "\n", "start = time.time()\n", "sales = sales()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "501e57aa-599c-402c-bbe7-a89580557928", "metadata": {}, "outputs": [], "source": ["sales.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7e5a2e32-26b4-44e4-b78f-0bdb971a6198", "metadata": {}, "outputs": [], "source": ["adding_sales_details = pd.merge(adding_min_max, sales, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\")\n", "\n", "adding_sales_details[\n", "    [\n", "        \"today_sales_qty\",\n", "        \"today_sales_value\",\n", "        \"today_carts\",\n", "        \"yesterday_sales_qty\",\n", "        \"yesterday_sales_value\",\n", "        \"yesterday_carts\",\n", "        \"l7days_sales_qty\",\n", "        \"l7days_sales_value\",\n", "        \"l7days_carts\",\n", "        \"ttl_sales_qty\",\n", "        \"ttl_sales_value\",\n", "        \"ttl_carts\",\n", "    ]\n", "] = (\n", "    adding_sales_details[\n", "        [\n", "            \"today_sales_qty\",\n", "            \"today_sales_value\",\n", "            \"today_carts\",\n", "            \"yesterday_sales_qty\",\n", "            \"yesterday_sales_value\",\n", "            \"yesterday_carts\",\n", "            \"l7days_sales_qty\",\n", "            \"l7days_sales_value\",\n", "            \"l7days_carts\",\n", "            \"ttl_sales_qty\",\n", "            \"ttl_sales_value\",\n", "            \"ttl_carts\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_sales_details.head()"]}, {"cell_type": "markdown", "id": "59855046-0ffb-481d-961c-26c8af3f57ad", "metadata": {}, "source": ["# dump details"]}, {"cell_type": "code", "execution_count": null, "id": "a1e018a3-b523-4bcf-9329-2165844469a9", "metadata": {}, "outputs": [], "source": ["def dump():\n", "    dump = f\"\"\"\n", "    \n", "    with\n", "    dump as\n", "        (select date(pos_timestamp + interval '330' minute) as date_, \n", "            outlet_id as hot_outlet_id, item_id, sum(il.\"delta\") as dump_quantity, avg(il.weighted_lp) as avg_lp\n", "\n", "                from lake_view_ims.ims_inventory_log il\n", "\n", "                    join (select distinct item_id, variant_id from lake_view_rpc.product_product) rpp on rpp.variant_id = il.variant_id\n", "\n", "                        where cast(insert_ds_ist as date) >= cast('{first_live_date}' as timestamp) - interval '330' minute\n", "                            and inventory_update_type_id in (11,12,13,64,87,88,89,7,33,9,34,63,67)\n", "\n", "                                group by 1,2,3\n", "        ),\n", "        \n", "    final as\n", "        (select *, (dump_quantity * avg_lp) as dump_value\n", "            from dump\n", "        )\n", "        \n", "            select hot_outlet_id, item_id,\n", "                sum(case when date_ = current_date then dump_quantity end) as today_dump_quantity,\n", "                sum(case when date_ = current_date then dump_value end) as today_dump_value,\n", "\n", "                sum(case when date_ = current_date - interval '1' day then dump_quantity end) as yesterday_dump_quantity,\n", "                sum(case when date_ = current_date - interval '1' day then dump_value end) as yesterday_dump_value,\n", "\n", "                sum(case when date_ between current_date - interval '7' day and current_date - interval '1' day then dump_quantity end) as l7days_dump_quantity,\n", "                sum(case when date_ between current_date - interval '7' day and current_date - interval '1' day then dump_value end) as l7days_dump_value,\n", "                \n", "                sum(dump_quantity) as ttl_dump_quantity,\n", "                sum(dump_value) as ttl_dump_value\n", "                \n", "                    from final\n", "                    \n", "                        where item_id in {item_id_list} and hot_outlet_id in {all_inv_outlet_id}\n", "                    \n", "                            group by 1,2\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(dump, presto)\n", "\n", "\n", "start = time.time()\n", "dump = dump()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "2c3a1ec0-f17a-4468-a79a-9b3b3be7da10", "metadata": {}, "outputs": [], "source": ["dump.head()"]}, {"cell_type": "code", "execution_count": null, "id": "96bf2d43-8a4d-4ea6-9d25-6a65f91ca1de", "metadata": {}, "outputs": [], "source": ["be_dump_details = dump.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_inv_outlet_id\",\n", "        \"today_dump_quantity\": \"be_today_dump_quantity\",\n", "        \"today_dump_value\": \"be_today_dump_value\",\n", "        \"yesterday_dump_quantity\": \"be_yesterday_dump_quantity\",\n", "        \"yesterday_dump_value\": \"be_yesterday_dump_value\",\n", "        \"l7days_dump_quantity\": \"be_l7days_dump_quantity\",\n", "        \"l7days_dump_value\": \"be_l7days_dump_value\",\n", "        \"ttl_dump_quantity\": \"be_ttl_dump_quantity\",\n", "        \"ttl_dump_value\": \"be_ttl_dump_value\",\n", "    }\n", ")\n", "\n", "be_dump_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "72a551c6-b0ab-46c8-99df-d67b70878a11", "metadata": {}, "outputs": [], "source": ["adding_fe_dump_details = pd.merge(\n", "    adding_sales_details, dump, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_dump_details = pd.merge(\n", "    adding_fe_dump_details,\n", "    be_dump_details,\n", "    on=[\"item_id\", \"be_inv_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_be_dump_details[\n", "    [\n", "        \"today_dump_quantity\",\n", "        \"be_today_dump_quantity\",\n", "        \"today_dump_value\",\n", "        \"be_today_dump_value\",\n", "        \"yesterday_dump_quantity\",\n", "        \"be_yesterday_dump_quantity\",\n", "        \"yesterday_dump_value\",\n", "        \"be_yesterday_dump_value\",\n", "        \"l7days_dump_quantity\",\n", "        \"be_l7days_dump_quantity\",\n", "        \"l7days_dump_value\",\n", "        \"be_l7days_dump_value\",\n", "        \"ttl_dump_quantity\",\n", "        \"be_ttl_dump_quantity\",\n", "        \"ttl_dump_value\",\n", "        \"be_ttl_dump_value\",\n", "    ]\n", "] = (\n", "    adding_be_dump_details[\n", "        [\n", "            \"today_dump_quantity\",\n", "            \"be_today_dump_quantity\",\n", "            \"today_dump_value\",\n", "            \"be_today_dump_value\",\n", "            \"yesterday_dump_quantity\",\n", "            \"be_yesterday_dump_quantity\",\n", "            \"yesterday_dump_value\",\n", "            \"be_yesterday_dump_value\",\n", "            \"l7days_dump_quantity\",\n", "            \"be_l7days_dump_quantity\",\n", "            \"l7days_dump_value\",\n", "            \"be_l7days_dump_value\",\n", "            \"ttl_dump_quantity\",\n", "            \"be_ttl_dump_quantity\",\n", "            \"ttl_dump_value\",\n", "            \"be_ttl_dump_value\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(float)\n", ")\n", "\n", "adding_be_dump_details.head()"]}, {"cell_type": "markdown", "id": "941db097-6edd-46ad-8ca8-ced93863efcf", "metadata": {}, "source": ["# ARS run id"]}, {"cell_type": "code", "execution_count": null, "id": "303dbcbc-ac8b-4a2e-b46d-6bf5a88689d5", "metadata": {}, "outputs": [], "source": ["def ars_run_id():\n", "    ars_run_id = f\"\"\"\n", "    \n", "    with\n", "    ars_run_id as\n", "        (select \n", "            extract(hour from (started_at + interval '330' minute)) as hour_,\n", "            id,\n", "            run_id,\n", "            outlet_id,\n", "            run_date,\n", "            success,\n", "            (started_at + interval '330' minute) as started_at,\n", "            (completed_at + interval '330' minute) as completed_at,\n", "            details,\n", "            facility_id,\n", "            is_simulation,\n", "            simulation_params,\n", "            extra,\n", "            json_format(json_extract(extra,'$.auto_sto_outlets')) AS ars_run,\n", "            replace(json_extract_scalar(simulation_params,'$.run'), '\"', '') as ars_run_flag,\n", "            json_extract_scalar(simulation_params, '$.manual') as manual_run,\n", "            replace(json_extract_scalar(simulation_params, '$.mode'),'\"','') as ars_mode\n", "\n", "                from lake_view_ars.job_run\n", "\n", "                    where completed_at >= cast(current_date as timestamp) - interval '10' day - interval '330' minute\n", "                        and json_format(json_extract(replace(replace(extra, '[', ''), ']', ''),'$.auto_sto_outlets')) is not null\n", "        ),\n", "\n", "    ars_frontend_outlets as\n", "        (select date(completed_at) as date_, hour_, facility_id as be_facility_id, cast(replace(replace(split_b, '[', ''), ']', '') as int) as fe_outlet_id, run_id\n", "            from ars_run_id\n", "                cross join unnest(split(ars_run,',')) AS t (split_b)\n", "\n", "                    where ars_run is not null\n", "                        -- lower(ars_mode) in ('perishable') and \n", "        ),\n", "\n", "    final_ars_run_id as\n", "        (select afo.date_, afo.hour_, afo.be_facility_id as be_facility_id, afo.fe_outlet_id as hot_outlet_id, rco.facility_id, afo.run_id\n", "\n", "            from ars_frontend_outlets afo\n", "\n", "                join lake_view_retail.console_outlet rco on rco.id = afo.fe_outlet_id\n", "        )\n", "\n", "            select * from final_ars_run_id   \n", "            \n", "    \"\"\"\n", "    return pd.read_sql_query(ars_run_id, presto)\n", "\n", "\n", "start = time.time()\n", "ars_run_id = ars_run_id()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "a7053d75-5f40-44be-9426-3be609575e14", "metadata": {}, "outputs": [], "source": ["ars_run_id.head()"]}, {"cell_type": "code", "execution_count": null, "id": "cf8abe8f-520d-434e-ac9f-9d21348ef315", "metadata": {}, "outputs": [], "source": ["run_id_list = list(ars_run_id[\"run_id\"].unique())\n", "run_id_list = tuple(run_id_list)\n", "len(run_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "d8d6fa70-4e6b-497f-97e2-a2bc5406d2f8", "metadata": {}, "outputs": [], "source": ["min_live_date = ars_run_id.agg({\"date_\": \"min\"}).reset_index().iloc[:, 1][0]\n", "min_live_date"]}, {"cell_type": "markdown", "id": "4979e140-3d9e-4ec9-bc8c-9e868c7fb77e", "metadata": {}, "source": ["# truncation details"]}, {"cell_type": "code", "execution_count": null, "id": "a59d5046-4201-4c6d-8765-f0004fe65bc1", "metadata": {}, "outputs": [], "source": ["def truncation_details():\n", "    truncation_details = f\"\"\"\n", "    \n", "    with\n", "    truncation_details as\n", "        (select backend_outlet_id as be_hot_outlet_id, frontend_outlet_id as hot_outlet_id, item_id, sto_quantity as v1_indent,\n", "            sto_quantity_post_truncation_in_case as v2_indent, run_id,\n", "            json_extract_scalar(quantity_drops, '$.inward_drop') as inward_drop,\n", "            json_extract_scalar(quantity_drops, '$.storage_drop') as storage_drop,\n", "            json_extract_scalar(quantity_drops, '$.truck_load_drop') as truck_load_drop,\n", "            json_extract_scalar(quantity_drops, '$.picking_capacity_sku_drop') as picking_capacity_sku_drop,\n", "            json_extract_scalar(quantity_drops, '$.picking_capacity_quantity_drop') as picking_capacity_quantity_drop,\n", "            (sto_quantity_post_truncation - sto_quantity_post_truncation_in_case) as loose_quantity_drop\n", "                from lake_view_ars.transfers_optimization_results_v2\n", "                    where cast(insert_ds_ist as date) >= cast('{min_live_date}' as date)\n", "        )\n", "\n", "            select be_hot_outlet_id, hot_outlet_id, item_id, cast(v1_indent as int) as v1_indent, cast(v2_indent as int) as v2_indent, run_id,\n", "                inward_drop, storage_drop, truck_load_drop, picking_capacity_sku_drop, picking_capacity_quantity_drop, loose_quantity_drop\n", "\n", "                    from truncation_details\n", "\n", "                        where run_id in {run_id_list} and item_id in {item_id_list}\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(truncation_details, presto)\n", "\n", "\n", "start = time.time()\n", "truncation_details = truncation_details()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "46cd78a7-76bc-4be9-be4a-acbd0ff14aca", "metadata": {}, "outputs": [], "source": ["truncation_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "82b86666-5048-4809-8a8e-501fc64d2a56", "metadata": {}, "outputs": [], "source": ["truncation_details[\n", "    [\n", "        \"v1_indent\",\n", "        \"v2_indent\",\n", "        \"inward_drop\",\n", "        \"storage_drop\",\n", "        \"truck_load_drop\",\n", "        \"picking_capacity_sku_drop\",\n", "        \"picking_capacity_quantity_drop\",\n", "        \"loose_quantity_drop\",\n", "    ]\n", "] = (\n", "    truncation_details[\n", "        [\n", "            \"v1_indent\",\n", "            \"v2_indent\",\n", "            \"inward_drop\",\n", "            \"storage_drop\",\n", "            \"truck_load_drop\",\n", "            \"picking_capacity_sku_drop\",\n", "            \"picking_capacity_quantity_drop\",\n", "            \"loose_quantity_drop\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_run_id = pd.merge(\n", "    truncation_details, ars_run_id, on=[\"run_id\", \"hot_outlet_id\"], how=\"inner\"\n", ")\n", "\n", "adding_run_id[\"date_\"] = pd.to_datetime(adding_run_id[\"date_\"])\n", "\n", "adding_run_id.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c872bd3b-ede2-4725-a517-7d56b1127751", "metadata": {}, "outputs": [], "source": ["new_truncation_details = adding_run_id.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "4212a14f-3664-4e09-bb2f-61b52edd6d2a", "metadata": {}, "outputs": [], "source": ["new_truncation_details[\"new_inward_drop\"] = np.where(\n", "    (new_truncation_details[\"inward_drop\"] > 0)\n", "    & (new_truncation_details[\"inward_drop\"] > new_truncation_details[\"storage_drop\"])\n", "    & (new_truncation_details[\"inward_drop\"] > new_truncation_details[\"truck_load_drop\"])\n", "    & (new_truncation_details[\"inward_drop\"] > new_truncation_details[\"picking_capacity_sku_drop\"])\n", "    & (\n", "        new_truncation_details[\"inward_drop\"]\n", "        > new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "    )\n", "    & (new_truncation_details[\"inward_drop\"] > new_truncation_details[\"loose_quantity_drop\"]),\n", "    new_truncation_details[\"inward_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"new_storage_drop\"] = np.where(\n", "    (new_truncation_details[\"storage_drop\"] > 0)\n", "    & (new_truncation_details[\"storage_drop\"] > new_truncation_details[\"inward_drop\"])\n", "    & (new_truncation_details[\"storage_drop\"] > new_truncation_details[\"truck_load_drop\"])\n", "    & (new_truncation_details[\"storage_drop\"] > new_truncation_details[\"picking_capacity_sku_drop\"])\n", "    & (\n", "        new_truncation_details[\"storage_drop\"]\n", "        > new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "    )\n", "    & (new_truncation_details[\"storage_drop\"] > new_truncation_details[\"loose_quantity_drop\"]),\n", "    new_truncation_details[\"storage_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"new_truck_load_drop\"] = np.where(\n", "    (new_truncation_details[\"truck_load_drop\"] > 0)\n", "    & (new_truncation_details[\"truck_load_drop\"] > new_truncation_details[\"inward_drop\"])\n", "    & (new_truncation_details[\"truck_load_drop\"] > new_truncation_details[\"storage_drop\"])\n", "    & (\n", "        new_truncation_details[\"truck_load_drop\"]\n", "        > new_truncation_details[\"picking_capacity_sku_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"truck_load_drop\"]\n", "        > new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "    )\n", "    & (new_truncation_details[\"truck_load_drop\"] > new_truncation_details[\"loose_quantity_drop\"]),\n", "    new_truncation_details[\"truck_load_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"new_picking_capacity_sku_drop\"] = np.where(\n", "    (new_truncation_details[\"picking_capacity_sku_drop\"] > 0)\n", "    & (new_truncation_details[\"picking_capacity_sku_drop\"] > new_truncation_details[\"inward_drop\"])\n", "    & (new_truncation_details[\"picking_capacity_sku_drop\"] > new_truncation_details[\"storage_drop\"])\n", "    & (\n", "        new_truncation_details[\"picking_capacity_sku_drop\"]\n", "        > new_truncation_details[\"truck_load_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_sku_drop\"]\n", "        > new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_sku_drop\"]\n", "        > new_truncation_details[\"loose_quantity_drop\"]\n", "    ),\n", "    new_truncation_details[\"picking_capacity_sku_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"new_picking_capacity_quantity_drop\"] = np.where(\n", "    (new_truncation_details[\"picking_capacity_quantity_drop\"] > 0)\n", "    & (\n", "        new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "        > new_truncation_details[\"inward_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "        > new_truncation_details[\"storage_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "        > new_truncation_details[\"truck_load_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "        > new_truncation_details[\"picking_capacity_sku_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "        > new_truncation_details[\"loose_quantity_drop\"]\n", "    ),\n", "    new_truncation_details[\"picking_capacity_quantity_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"new_loose_quantity_drop\"] = np.where(\n", "    (new_truncation_details[\"loose_quantity_drop\"] > 0)\n", "    & (new_truncation_details[\"loose_quantity_drop\"] > new_truncation_details[\"inward_drop\"])\n", "    & (new_truncation_details[\"loose_quantity_drop\"] > new_truncation_details[\"storage_drop\"])\n", "    & (new_truncation_details[\"loose_quantity_drop\"] > new_truncation_details[\"truck_load_drop\"])\n", "    & (\n", "        new_truncation_details[\"loose_quantity_drop\"]\n", "        > new_truncation_details[\"picking_capacity_sku_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"loose_quantity_drop\"]\n", "        > new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "    ),\n", "    new_truncation_details[\"loose_quantity_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"total_drop_quantity\"] = (\n", "    new_truncation_details[\"new_inward_drop\"]\n", "    + new_truncation_details[\"new_storage_drop\"]\n", "    + new_truncation_details[\"new_truck_load_drop\"]\n", "    + new_truncation_details[\"new_picking_capacity_sku_drop\"]\n", "    + new_truncation_details[\"new_picking_capacity_quantity_drop\"]\n", "    + new_truncation_details[\"new_loose_quantity_drop\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "82100af5-37e0-4b47-9e99-329f7c3bdea9", "metadata": {}, "outputs": [], "source": ["new_truncation_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9d01164f-99fb-4792-a685-c2852aa25a3e", "metadata": {}, "outputs": [], "source": ["new_truncation_details = new_truncation_details[\n", "    [\n", "        \"date_\",\n", "        \"run_id\",\n", "        \"be_facility_id\",\n", "        \"be_hot_outlet_id\",\n", "        \"facility_id\",\n", "        \"hot_outlet_id\",\n", "        \"item_id\",\n", "        \"v1_indent\",\n", "        \"v2_indent\",\n", "        \"new_inward_drop\",\n", "        \"new_storage_drop\",\n", "        \"new_truck_load_drop\",\n", "        \"new_picking_capacity_sku_drop\",\n", "        \"new_picking_capacity_quantity_drop\",\n", "        \"new_loose_quantity_drop\",\n", "        \"total_drop_quantity\",\n", "    ]\n", "]\n", "\n", "\n", "new_truncation_details.head()"]}, {"cell_type": "markdown", "id": "d0b60339-de00-4805-82aa-370cab443a4f", "metadata": {}, "source": ["# frontend truncation details"]}, {"cell_type": "code", "execution_count": null, "id": "06ee79c7-b3d1-458f-a39d-95c6505e584f", "metadata": {}, "outputs": [], "source": ["frontend_current_date_truncation = new_truncation_details.copy()\n", "\n", "frontend_current_date_truncation = frontend_current_date_truncation[\n", "    frontend_current_date_truncation[\"date_\"] == datetime.today().strftime(\"%Y-%m-%d\")\n", "]\n", "\n", "frontend_current_date_truncation = (\n", "    frontend_current_date_truncation.groupby(\n", "        [\n", "            \"be_facility_id\",\n", "            \"be_hot_outlet_id\",\n", "            \"facility_id\",\n", "            \"hot_outlet_id\",\n", "            \"item_id\",\n", "        ]\n", "    )[\n", "        [\n", "            \"v1_indent\",\n", "            \"v2_indent\",\n", "            \"new_inward_drop\",\n", "            \"new_storage_drop\",\n", "            \"new_truck_load_drop\",\n", "            \"new_picking_capacity_sku_drop\",\n", "            \"new_picking_capacity_quantity_drop\",\n", "            \"new_loose_quantity_drop\",\n", "            \"total_drop_quantity\",\n", "        ]\n", "    ]\n", "    .agg(\"sum\")\n", "    .reset_index()\n", ").rename(\n", "    columns={\n", "        \"v1_indent\": \"t_v1_indent\",\n", "        \"v2_indent\": \"t_v2_indent\",\n", "        \"new_inward_drop\": \"t_new_inward_drop\",\n", "        \"new_storage_drop\": \"t_new_storage_drop\",\n", "        \"new_truck_load_drop\": \"t_new_truck_load_drop\",\n", "        \"new_picking_capacity_sku_drop\": \"t_new_picking_capacity_sku_drop\",\n", "        \"new_picking_capacity_quantity_drop\": \"t_new_picking_capacity_quantity_drop\",\n", "        \"new_loose_quantity_drop\": \"t_new_loose_quantity_drop\",\n", "        \"total_drop_quantity\": \"t_total_drop_quantity\",\n", "    }\n", ")\n", "\n", "frontend_l_1_date_truncation = new_truncation_details.copy()\n", "\n", "frontend_l_1_date_truncation = frontend_l_1_date_truncation[\n", "    frontend_l_1_date_truncation[\"date_\"]\n", "    == (datetime.today() - pd.DateOffset(days=1)).strftime(\"%Y-%m-%d\")\n", "]\n", "\n", "frontend_l_1_date_truncation = (\n", "    frontend_l_1_date_truncation.groupby(\n", "        [\n", "            \"be_facility_id\",\n", "            \"be_hot_outlet_id\",\n", "            \"facility_id\",\n", "            \"hot_outlet_id\",\n", "            \"item_id\",\n", "        ]\n", "    )[\n", "        [\n", "            \"v1_indent\",\n", "            \"v2_indent\",\n", "            \"new_inward_drop\",\n", "            \"new_storage_drop\",\n", "            \"new_truck_load_drop\",\n", "            \"new_picking_capacity_sku_drop\",\n", "            \"new_picking_capacity_quantity_drop\",\n", "            \"new_loose_quantity_drop\",\n", "            \"total_drop_quantity\",\n", "        ]\n", "    ]\n", "    .agg(\"sum\")\n", "    .reset_index()\n", ").rename(\n", "    columns={\n", "        \"v1_indent\": \"t1_v1_indent\",\n", "        \"v2_indent\": \"t1_v2_indent\",\n", "        \"new_inward_drop\": \"t1_new_inward_drop\",\n", "        \"new_storage_drop\": \"t1_new_storage_drop\",\n", "        \"new_truck_load_drop\": \"t1_new_truck_load_drop\",\n", "        \"new_picking_capacity_sku_drop\": \"t1_new_picking_capacity_sku_drop\",\n", "        \"new_picking_capacity_quantity_drop\": \"t1_new_picking_capacity_quantity_drop\",\n", "        \"new_loose_quantity_drop\": \"t1_new_loose_quantity_drop\",\n", "        \"total_drop_quantity\": \"t1_total_drop_quantity\",\n", "    }\n", ")\n", "\n", "frontend_l_7_date_truncation = new_truncation_details.copy()\n", "\n", "frontend_l_7_date_truncation = frontend_l_7_date_truncation[\n", "    (\n", "        frontend_l_7_date_truncation[\"date_\"]\n", "        >= (datetime.today() - pd.DateOffset(days=7)).strftime(\"%Y-%m-%d\")\n", "    )\n", "    & (\n", "        frontend_l_7_date_truncation[\"date_\"]\n", "        <= (datetime.today() - pd.DateOffset(days=1)).strftime(\"%Y-%m-%d\")\n", "    )\n", "]\n", "\n", "frontend_l_7_date_truncation = (\n", "    frontend_l_7_date_truncation.groupby(\n", "        [\n", "            \"be_facility_id\",\n", "            \"be_hot_outlet_id\",\n", "            \"facility_id\",\n", "            \"hot_outlet_id\",\n", "            \"item_id\",\n", "        ]\n", "    )[\n", "        [\n", "            \"v1_indent\",\n", "            \"v2_indent\",\n", "            \"new_inward_drop\",\n", "            \"new_storage_drop\",\n", "            \"new_truck_load_drop\",\n", "            \"new_picking_capacity_sku_drop\",\n", "            \"new_picking_capacity_quantity_drop\",\n", "            \"new_loose_quantity_drop\",\n", "            \"total_drop_quantity\",\n", "        ]\n", "    ]\n", "    .agg(\"sum\")\n", "    .reset_index()\n", ").rename(\n", "    columns={\n", "        \"v1_indent\": \"t7_v1_indent\",\n", "        \"v2_indent\": \"t7_v2_indent\",\n", "        \"new_inward_drop\": \"t7_new_inward_drop\",\n", "        \"new_storage_drop\": \"t7_new_storage_drop\",\n", "        \"new_truck_load_drop\": \"t7_new_truck_load_drop\",\n", "        \"new_picking_capacity_sku_drop\": \"t7_new_picking_capacity_sku_drop\",\n", "        \"new_picking_capacity_quantity_drop\": \"t7_new_picking_capacity_quantity_drop\",\n", "        \"new_loose_quantity_drop\": \"t7_new_loose_quantity_drop\",\n", "        \"total_drop_quantity\": \"t7_total_drop_quantity\",\n", "    }\n", ")\n", "\n", "new_truncation_details.shape, frontend_current_date_truncation.shape, frontend_l_1_date_truncation.shape, frontend_l_7_date_truncation.shape"]}, {"cell_type": "code", "execution_count": null, "id": "e2287091-ad10-41ee-a1c6-41e9f5cb4b1c", "metadata": {}, "outputs": [], "source": ["adding_t_truncation_details = pd.merge(\n", "    adding_be_dump_details,\n", "    frontend_current_date_truncation,\n", "    on=[\n", "        \"item_id\",\n", "        \"be_facility_id\",\n", "        \"be_hot_outlet_id\",\n", "        \"facility_id\",\n", "        \"hot_outlet_id\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "adding_t1_truncation_details = pd.merge(\n", "    adding_t_truncation_details,\n", "    frontend_l_1_date_truncation,\n", "    on=[\n", "        \"item_id\",\n", "        \"be_facility_id\",\n", "        \"be_hot_outlet_id\",\n", "        \"facility_id\",\n", "        \"hot_outlet_id\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "adding_t7_truncation_details = pd.merge(\n", "    adding_t1_truncation_details,\n", "    frontend_l_7_date_truncation,\n", "    on=[\n", "        \"item_id\",\n", "        \"be_facility_id\",\n", "        \"be_hot_outlet_id\",\n", "        \"facility_id\",\n", "        \"hot_outlet_id\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "adding_t7_truncation_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b692b657-615b-4c3c-83ec-d8e529352936", "metadata": {}, "outputs": [], "source": ["adding_t7_truncation_details[\n", "    [\n", "        \"t_v1_indent\",\n", "        \"t_v2_indent\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t_total_drop_quantity\",\n", "        \"t1_v1_indent\",\n", "        \"t1_v2_indent\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t1_total_drop_quantity\",\n", "        \"t7_v1_indent\",\n", "        \"t7_v2_indent\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "        \"t7_total_drop_quantity\",\n", "    ]\n", "] = (\n", "    adding_t7_truncation_details[\n", "        [\n", "            \"t_v1_indent\",\n", "            \"t_v2_indent\",\n", "            \"t_new_inward_drop\",\n", "            \"t_new_storage_drop\",\n", "            \"t_new_truck_load_drop\",\n", "            \"t_new_picking_capacity_sku_drop\",\n", "            \"t_new_picking_capacity_quantity_drop\",\n", "            \"t_new_loose_quantity_drop\",\n", "            \"t_total_drop_quantity\",\n", "            \"t1_v1_indent\",\n", "            \"t1_v2_indent\",\n", "            \"t1_new_inward_drop\",\n", "            \"t1_new_storage_drop\",\n", "            \"t1_new_truck_load_drop\",\n", "            \"t1_new_picking_capacity_sku_drop\",\n", "            \"t1_new_picking_capacity_quantity_drop\",\n", "            \"t1_new_loose_quantity_drop\",\n", "            \"t1_total_drop_quantity\",\n", "            \"t7_v1_indent\",\n", "            \"t7_v2_indent\",\n", "            \"t7_new_inward_drop\",\n", "            \"t7_new_storage_drop\",\n", "            \"t7_new_truck_load_drop\",\n", "            \"t7_new_picking_capacity_sku_drop\",\n", "            \"t7_new_picking_capacity_quantity_drop\",\n", "            \"t7_new_loose_quantity_drop\",\n", "            \"t7_total_drop_quantity\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_t7_truncation_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "cf751b4f-d69d-4b62-a36e-fe302b34a15d", "metadata": {}, "outputs": [], "source": ["final = adding_t7_truncation_details[\n", "    [\n", "        \"go_live_date\",\n", "        \"zone\",\n", "        \"city_name\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"event_flag\",\n", "        \"assortment_type\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"p_type\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"manufacturer_id\",\n", "        \"manufacturer_name\",\n", "        \"status\",\n", "        \"assortment_status\",\n", "        \"actual_inv\",\n", "        \"ttl_blocked_qty\",\n", "        \"net_inventory\",\n", "        \"pending_putaway\",\n", "        \"open_sto_qty\",\n", "        \"ttl_po_quantity\",\n", "        \"ttl_grn_quantity\",\n", "        \"ttl_open_po_quantity\",\n", "        \"min_quantity\",\n", "        \"max_quantity\",\n", "        \"today_sales_qty\",\n", "        \"today_sales_value\",\n", "        \"today_carts\",\n", "        \"yesterday_sales_qty\",\n", "        \"yesterday_sales_value\",\n", "        \"yesterday_carts\",\n", "        \"l7days_sales_qty\",\n", "        \"l7days_sales_value\",\n", "        \"l7days_carts\",\n", "        \"ttl_sales_qty\",\n", "        \"ttl_sales_value\",\n", "        \"ttl_carts\",\n", "        \"today_dump_quantity\",\n", "        \"today_dump_value\",\n", "        \"yesterday_dump_quantity\",\n", "        \"yesterday_dump_value\",\n", "        \"l7days_dump_quantity\",\n", "        \"l7days_dump_value\",\n", "        \"ttl_dump_quantity\",\n", "        \"ttl_dump_value\",\n", "        \"t_v1_indent\",\n", "        \"t_v2_indent\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t_total_drop_quantity\",\n", "        \"t1_v1_indent\",\n", "        \"t1_v2_indent\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t1_total_drop_quantity\",\n", "        \"t7_v1_indent\",\n", "        \"t7_v2_indent\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "        \"t7_total_drop_quantity\",\n", "        \"be_hot_outlet_id\",\n", "        \"be_inv_outlet_id\",\n", "        \"be_facility_id\",\n", "        \"be_facility_name\",\n", "        \"be_actual_inv\",\n", "        \"be_ttl_blocked_qty\",\n", "        \"be_net_inventory\",\n", "        \"be_pending_putaway\",\n", "        \"be_open_sto_qty\",\n", "        \"be_ttl_po_quantity\",\n", "        \"be_ttl_grn_quantity\",\n", "        \"be_ttl_open_po_quantity\",\n", "        \"be_today_dump_quantity\",\n", "        \"be_today_dump_value\",\n", "        \"be_yesterday_dump_quantity\",\n", "        \"be_yesterday_dump_value\",\n", "        \"be_l7days_dump_quantity\",\n", "        \"be_l7days_dump_value\",\n", "        \"be_ttl_dump_quantity\",\n", "        \"be_ttl_dump_value\",\n", "    ]\n", "]\n", "\n", "final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f1a630a1-361e-44c1-a2ab-2bc6b95c7df2", "metadata": {}, "outputs": [], "source": ["final.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "4c9b08dc-e10b-49da-b4c2-989fdbd8148b", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"go_live_date\", \"type\": \"date\", \"description\": \"name of zone\"},\n", "    {\"name\": \"zone\", \"type\": \"varchar\", \"description\": \"name of zone\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"name of city\"},\n", "    {\n", "        \"name\": \"facility_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"unique identifier for DS facility\",\n", "    },\n", "    {\n", "        \"name\": \"facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"unique identifier for DS facility name\",\n", "    },\n", "    {\"name\": \"event_flag\", \"type\": \"varchar\", \"description\": \"type of assortment\"},\n", "    {\"name\": \"assortment_type\", \"type\": \"varchar\", \"description\": \"type of event\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"unique identifier for item\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"product name\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"product type\"},\n", "    {\"name\": \"l0\", \"type\": \"varchar\", \"description\": \"l0 category\"},\n", "    {\"name\": \"l1\", \"type\": \"varchar\", \"description\": \"l1 category\"},\n", "    {\"name\": \"l2\", \"type\": \"varchar\", \"description\": \"l2 category\"},\n", "    {\"name\": \"manufacturer_id\", \"type\": \"float\", \"description\": \"manufacturer id\"},\n", "    {\n", "        \"name\": \"manufacturer_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"manufacturer name\",\n", "    },\n", "    {\"name\": \"status\", \"type\": \"integer\", \"description\": \"assortment flag\"},\n", "    {\n", "        \"name\": \"assortment_status\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"identify part of assortment\",\n", "    },\n", "    {\"name\": \"actual_inv\", \"type\": \"integer\", \"description\": \"shelf inventory\"},\n", "    {\"name\": \"ttl_blocked_qty\", \"type\": \"integer\", \"description\": \"blocked inventory\"},\n", "    {\n", "        \"name\": \"net_inventory\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"after blocked inventory\",\n", "    },\n", "    {\"name\": \"pending_putaway\", \"type\": \"integer\", \"description\": \"pending putaway\"},\n", "    {\"name\": \"open_sto_qty\", \"type\": \"integer\", \"description\": \"open STO quantity\"},\n", "    {\n", "        \"name\": \"ttl_po_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"total PO raised quantity\",\n", "    },\n", "    {\n", "        \"name\": \"ttl_grn_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"total GRN quantity\",\n", "    },\n", "    {\n", "        \"name\": \"ttl_open_po_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"total Open PO quantity\",\n", "    },\n", "    {\"name\": \"min_quantity\", \"type\": \"integer\", \"description\": \"min quantity\"},\n", "    {\"name\": \"max_quantity\", \"type\": \"integer\", \"description\": \"max quantoty\"},\n", "    {\n", "        \"name\": \"today_sales_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today sold quantity\",\n", "    },\n", "    {\"name\": \"today_sales_value\", \"type\": \"integer\", \"description\": \"today sold value\"},\n", "    {\"name\": \"today_carts\", \"type\": \"integer\", \"description\": \"today carts\"},\n", "    {\n", "        \"name\": \"yesterday_sales_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday sold quantity\",\n", "    },\n", "    {\n", "        \"name\": \"yesterday_sales_value\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday sold value\",\n", "    },\n", "    {\"name\": \"yesterday_carts\", \"type\": \"integer\", \"description\": \"yesterday carts\"},\n", "    {\n", "        \"name\": \"l7days_sales_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days sold quantity\",\n", "    },\n", "    {\n", "        \"name\": \"l7days_sales_value\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days sold value\",\n", "    },\n", "    {\"name\": \"l7days_carts\", \"type\": \"integer\", \"description\": \"last 7 days carts\"},\n", "    {\"name\": \"ttl_sales_qty\", \"type\": \"integer\", \"description\": \"total sales quantity\"},\n", "    {\"name\": \"ttl_sales_value\", \"type\": \"integer\", \"description\": \"total sales value\"},\n", "    {\"name\": \"ttl_carts\", \"type\": \"integer\", \"description\": \"total carts\"},\n", "    {\n", "        \"name\": \"today_dump_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"today dump quantity\",\n", "    },\n", "    {\"name\": \"today_dump_value\", \"type\": \"float\", \"description\": \"today dump value\"},\n", "    {\n", "        \"name\": \"yesterday_dump_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"yesterday dump quantity\",\n", "    },\n", "    {\n", "        \"name\": \"yesterday_dump_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"yesterday dump value\",\n", "    },\n", "    {\n", "        \"name\": \"l7days_dump_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"last 7 days dump quantity\",\n", "    },\n", "    {\n", "        \"name\": \"l7days_dump_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"last 7 day dump value\",\n", "    },\n", "    {\n", "        \"name\": \"ttl_dump_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"total dump quantity\",\n", "    },\n", "    {\"name\": \"ttl_dump_value\", \"type\": \"float\", \"description\": \"total dump value\"},\n", "    {\n", "        \"name\": \"t_v1_indent\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today actual transfer demand\",\n", "    },\n", "    {\n", "        \"name\": \"t_v2_indent\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today final transfer initiate\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_inward_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today inward drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_storage_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today storage drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_truck_load_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today truck load drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_picking_capacity_sku_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today picking capacity skus drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_picking_capacity_quantity_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today picking capacity drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_loose_quantity_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today loose drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t_total_drop_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today total drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t1_v1_indent\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday actual transfer demand\",\n", "    },\n", "    {\n", "        \"name\": \"t1_v2_indent\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday final transfer initiate\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_inward_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday inward drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_storage_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday storage drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_truck_load_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday truck load drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_picking_capacity_sku_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday picking capacity skus drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_picking_capacity_quantity_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday picking capacity drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_loose_quantity_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday loose drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t1_total_drop_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday total drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t7_v1_indent\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days actual transfer demand\",\n", "    },\n", "    {\n", "        \"name\": \"t7_v2_indent\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days final transfer initiate\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_inward_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days inward drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_storage_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days storage drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_truck_load_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days truck load drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_picking_capacity_sku_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days picking capacity skus drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_picking_capacity_quantity_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days picking capacity drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_loose_quantity_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days loose drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t7_total_drop_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days total drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_hot_outlet_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend hot outlet id\",\n", "    },\n", "    {\n", "        \"name\": \"be_inv_outlet_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend inv outlet id\",\n", "    },\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"backend facility id\"},\n", "    {\n", "        \"name\": \"be_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"backend facility name\",\n", "    },\n", "    {\n", "        \"name\": \"be_actual_inv\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend shelf inventory\",\n", "    },\n", "    {\n", "        \"name\": \"be_ttl_blocked_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend blocked inventory\",\n", "    },\n", "    {\n", "        \"name\": \"be_net_inventory\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend after blocked inventory\",\n", "    },\n", "    {\n", "        \"name\": \"be_pending_putaway\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend pending putaway\",\n", "    },\n", "    {\n", "        \"name\": \"be_open_sto_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend open STO quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_ttl_po_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend total PO raised quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_ttl_grn_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend total GRN quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_ttl_open_po_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend total Open PO quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_today_dump_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend today dump quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_today_dump_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend today dump value\",\n", "    },\n", "    {\n", "        \"name\": \"be_yesterday_dump_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend yesterday dump quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_yesterday_dump_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend yesterday dump value\",\n", "    },\n", "    {\n", "        \"name\": \"be_l7days_dump_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend last 7 days dump quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_l7days_dump_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend last 7 day dump value\",\n", "    },\n", "    {\n", "        \"name\": \"be_ttl_dump_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend total dump quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_ttl_dump_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend total dump value\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "76c9804d-8061-46b7-adf2-469e3deb89a1", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"banner_event_metrics_reporting\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"facility_id\", \"item_id\"],\n", "    \"sortkey\": [\"city_name\"],\n", "    \"incremental_key\": \"date\",\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"Reporting metrics for Events\",\n", "}\n", "pb.to_redshift(final, **kwargs)"]}, {"cell_type": "markdown", "id": "2b98ccb1-ce73-41b6-ac3d-631158df7295", "metadata": {"tags": []}, "source": ["# p_type summary"]}, {"cell_type": "code", "execution_count": null, "id": "010c8849-0713-400c-934a-e55fcbeef0c5", "metadata": {}, "outputs": [], "source": ["def p_type_summary():\n", "    p_type_summary = \"\"\"\n", "    \n", "    with\n", "    base as\n", "        (select *,\n", "            row_number() over (partition by item_id, be_hot_outlet_id order by item_id) as be_item_count\n", "            from metrics.banner_event_metrics_reporting\n", "        ),\n", "\n", "    total_view as\n", "        (select 'All' as city_name, event_flag, assortment_type, p_type,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 \n", "                        when be_net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as after_be_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end)*1.00/count(item_id) as after_pp_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 else 0 end)*1.00/count(item_id) as after_intranist_availability,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(case when be_item_count = 1 then be_net_inventory else 0 end) as be_inv,\n", "            sum(case when be_item_count = 1 then be_pending_putaway else 0 end) as be_pending_putaway_inv,\n", "            sum(case when be_item_count = 1 then be_open_sto_qty else 0 end) as be_open_sto_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_po_quantity else 0 end) as be_ttl_po_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_grn_quantity else 0 end) as be_ttl_po_grn_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_open_po_quantity else 0 end) as be_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(case when be_item_count = 1 then be_today_dump_quantity else 0 end) as be_today_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_today_dump_value else 0 end) as be_today_dump_value,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_quantity else 0 end) as be_yesterday_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_value else 0 end) as be_yesterday_dump_value,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_quantity else 0 end) as be_l7days_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_value else 0 end) as be_l7days_dump_value,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_quantity else 0 end) as be_ttl_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_value else 0 end) as be_ttl_dump_value,\n", "            \n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "            \n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "            \n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "            \n", "            \n", "                from base\n", "\n", "                    group by 1,2,3,4\n", "        ),\n", "\n", "    city_view as\n", "        (select city_name, event_flag, assortment_type, p_type, \n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 \n", "                        when be_net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as after_be_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end)*1.00/count(item_id) as after_pp_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 else 0 end)*1.00/count(item_id) as after_intranist_availability,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(case when be_item_count = 1 then be_net_inventory else 0 end) as be_inv,\n", "            sum(case when be_item_count = 1 then be_pending_putaway else 0 end) as be_pending_putaway_inv,\n", "            sum(case when be_item_count = 1 then be_open_sto_qty else 0 end) as be_open_sto_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_po_quantity else 0 end) as be_ttl_po_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_grn_quantity else 0 end) as be_ttl_po_grn_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_open_po_quantity else 0 end) as be_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(case when be_item_count = 1 then be_today_dump_quantity else 0 end) as be_today_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_today_dump_value else 0 end) as be_today_dump_value,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_quantity else 0 end) as be_yesterday_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_value else 0 end) as be_yesterday_dump_value,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_quantity else 0 end) as be_l7days_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_value else 0 end) as be_l7days_dump_value,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_quantity else 0 end) as be_ttl_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_value else 0 end) as be_ttl_dump_value,\n", "            \n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "            \n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "            \n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "                from base\n", "\n", "                    group by 1,2,3,4\n", "        ),\n", "\n", "    final as\n", "        (select * from total_view\n", "\n", "            union\n", "\n", "                select * from city_view\n", "        )\n", "\n", "            select * from final \n", "                order by \n", "                    case when city_name = 'All' then 'A' else city_name end, \n", "                    event_flag, assortment_type, p_type\n", "\n", "    \"\"\"\n", "    return pd.read_sql_query(p_type_summary, redshift)\n", "\n", "\n", "start = time.time()\n", "p_type_summary = p_type_summary()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "e35b8491-c377-4669-8386-4ecc0c3b1d4e", "metadata": {}, "outputs": [], "source": ["p_type_summary.head()"]}, {"cell_type": "code", "execution_count": null, "id": "64055e3f-54ca-4f8d-915d-1c883c28be8e", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(p_type_summary, \"13u1H-zyxVWKR1AjZ6dNjdrdHJW5i6HmvCcj8AjpTGHE\", \"city_p_type_raw\")"]}, {"cell_type": "markdown", "id": "9b24c9db-ca64-42ab-add9-e40626e0969a", "metadata": {"tags": []}, "source": ["# store wise summary"]}, {"cell_type": "code", "execution_count": null, "id": "92c219ca-44ca-4acb-a1f5-e2dbe934a4a8", "metadata": {}, "outputs": [], "source": ["def store_summary():\n", "    store_summary = \"\"\"\n", "    \n", "    with\n", "    base as\n", "        (select *,\n", "            row_number() over (partition by item_id, be_hot_outlet_id order by item_id) as be_item_count\n", "            from metrics.banner_event_metrics_reporting\n", "        ),\n", "\n", "    total_view as\n", "        (select 'All' as city_name, event_flag, assortment_type, \n", "            facility_id as fe_facility_id, be_facility_id, facility_name as fe_facility_name, be_facility_name,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 \n", "                        when be_net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as after_be_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end)*1.00/count(item_id) as after_pp_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 else 0 end)*1.00/count(item_id) as after_intranist_availability,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(min_quantity) as min_quantity,\n", "            sum(max_quantity) as max_quantity,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(case when be_item_count = 1 then be_net_inventory else 0 end) as be_inv,\n", "            sum(case when be_item_count = 1 then be_pending_putaway else 0 end) as be_pending_putaway_inv,\n", "            sum(case when be_item_count = 1 then be_open_sto_qty else 0 end) as be_open_sto_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_po_quantity else 0 end) as be_ttl_po_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_grn_quantity else 0 end) as be_ttl_po_grn_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_open_po_quantity else 0 end) as be_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(case when be_item_count = 1 then be_today_dump_quantity else 0 end) as be_today_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_today_dump_value else 0 end) as be_today_dump_value,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_quantity else 0 end) as be_yesterday_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_value else 0 end) as be_yesterday_dump_value,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_quantity else 0 end) as be_l7days_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_value else 0 end) as be_l7days_dump_value,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_quantity else 0 end) as be_ttl_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_value else 0 end) as be_ttl_dump_value,\n", "            \n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "            \n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "            \n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "                from base\n", "\n", "                    group by 1,2,3,4,5,6,7\n", "        ),\n", "\n", "    facility_view as\n", "        (select city_name, event_flag, assortment_type,\n", "            facility_id as fe_facility_id, be_facility_id, facility_name as fe_facility_name, be_facility_name,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 \n", "                        when be_net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as after_be_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end)*1.00/count(item_id) as after_pp_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 else 0 end)*1.00/count(item_id) as after_intranist_availability,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(min_quantity) as min_quantity,\n", "            sum(max_quantity) as max_quantity,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(case when be_item_count = 1 then be_net_inventory else 0 end) as be_inv,\n", "            sum(case when be_item_count = 1 then be_pending_putaway else 0 end) as be_pending_putaway_inv,\n", "            sum(case when be_item_count = 1 then be_open_sto_qty else 0 end) as be_open_sto_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_po_quantity else 0 end) as be_ttl_po_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_grn_quantity else 0 end) as be_ttl_po_grn_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_open_po_quantity else 0 end) as be_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(case when be_item_count = 1 then be_today_dump_quantity else 0 end) as be_today_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_today_dump_value else 0 end) as be_today_dump_value,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_quantity else 0 end) as be_yesterday_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_value else 0 end) as be_yesterday_dump_value,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_quantity else 0 end) as be_l7days_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_value else 0 end) as be_l7days_dump_value,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_quantity else 0 end) as be_ttl_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_value else 0 end) as be_ttl_dump_value,\n", "            \n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "            \n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "            \n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "                from base\n", "\n", "                    group by 1,2,3,4,5,6,7\n", "        ),\n", "\n", "    final as\n", "        (select * from total_view\n", "\n", "            union\n", "\n", "                select * from facility_view\n", "        )\n", "\n", "            select * from final \n", "                order by \n", "                    case when city_name = 'All' then 'A' else city_name end, \n", "                    event_flag, assortment_type, fe_facility_id, be_facility_id\n", "\n", "    \"\"\"\n", "    return pd.read_sql_query(store_summary, redshift)\n", "\n", "\n", "start = time.time()\n", "store_summary = store_summary()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "ea352a1b-573a-47f2-888c-ef80275fd018", "metadata": {}, "outputs": [], "source": ["store_summary.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6aa6bb9f-2ea5-44fa-bce7-d9873c8f1911", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(store_summary, \"13u1H-zyxVWKR1AjZ6dNjdrdHJW5i6HmvCcj8AjpTGHE\", \"store_wise_raw\")"]}, {"cell_type": "markdown", "id": "d54e5550-a633-4b24-99e5-94b1d510aa66", "metadata": {}, "source": ["# backend p_type summary"]}, {"cell_type": "code", "execution_count": null, "id": "5000500d-6a6a-438c-8441-a08a1ca2f514", "metadata": {}, "outputs": [], "source": ["def be_p_type_summary():\n", "    be_p_type_summary = \"\"\"\n", "    \n", "    with\n", "    base as\n", "        (select *,\n", "            row_number() over (partition by item_id, be_hot_outlet_id order by item_id) as be_item_count\n", "            from metrics.banner_event_metrics_reporting\n", "        ),\n", "\n", "    total_view as\n", "        (select event_flag, assortment_type, \n", "            0 as be_facility_id, 'All' as be_facility_name, p_type,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 \n", "                        when be_net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as after_be_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end)*1.00/count(item_id) as after_pp_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 else 0 end)*1.00/count(item_id) as after_intranist_availability,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(min_quantity) as min_quantity,\n", "            sum(max_quantity) as max_quantity,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(case when be_item_count = 1 then be_net_inventory else 0 end) as be_inv,\n", "            sum(case when be_item_count = 1 then be_pending_putaway else 0 end) as be_pending_putaway_inv,\n", "            sum(case when be_item_count = 1 then be_open_sto_qty else 0 end) as be_open_sto_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_po_quantity else 0 end) as be_ttl_po_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_grn_quantity else 0 end) as be_ttl_po_grn_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_open_po_quantity else 0 end) as be_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(case when be_item_count = 1 then be_today_dump_quantity else 0 end) as be_today_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_today_dump_value else 0 end) as be_today_dump_value,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_quantity else 0 end) as be_yesterday_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_value else 0 end) as be_yesterday_dump_value,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_quantity else 0 end) as be_l7days_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_value else 0 end) as be_l7days_dump_value,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_quantity else 0 end) as be_ttl_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_value else 0 end) as be_ttl_dump_value,\n", "            \n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "            \n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "            \n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "                from base\n", "\n", "                    group by 1,2,3,4,5\n", "        ),\n", "\n", "    backend_view as\n", "        (select event_flag, assortment_type, \n", "            be_facility_id, be_facility_name, p_type,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 \n", "                        when be_net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as after_be_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end)*1.00/count(item_id) as after_pp_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 else 0 end)*1.00/count(item_id) as after_intranist_availability,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(min_quantity) as min_quantity,\n", "            sum(max_quantity) as max_quantity,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(case when be_item_count = 1 then be_net_inventory else 0 end) as be_inv,\n", "            sum(case when be_item_count = 1 then be_pending_putaway else 0 end) as be_pending_putaway_inv,\n", "            sum(case when be_item_count = 1 then be_open_sto_qty else 0 end) as be_open_sto_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_po_quantity else 0 end) as be_ttl_po_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_grn_quantity else 0 end) as be_ttl_po_grn_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_open_po_quantity else 0 end) as be_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(case when be_item_count = 1 then be_today_dump_quantity else 0 end) as be_today_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_today_dump_value else 0 end) as be_today_dump_value,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_quantity else 0 end) as be_yesterday_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_value else 0 end) as be_yesterday_dump_value,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_quantity else 0 end) as be_l7days_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_value else 0 end) as be_l7days_dump_value,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_quantity else 0 end) as be_ttl_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_value else 0 end) as be_ttl_dump_value,\n", "            \n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "            \n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "            \n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "                from base\n", "\n", "                    group by 1,2,3,4,5\n", "        ),\n", "\n", "    final as\n", "        (select * from total_view\n", "\n", "            union\n", "\n", "                select * from backend_view\n", "        )\n", "\n", "            select * from final \n", "                order by \n", "                    case when be_facility_name = 'All' then 'A' end,\n", "                        event_flag, assortment_type, p_type\n", "\n", "    \"\"\"\n", "    return pd.read_sql_query(be_p_type_summary, redshift)\n", "\n", "\n", "start = time.time()\n", "be_p_type_summary = be_p_type_summary()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "1fd38414-00db-49f8-a166-8ae5a9a09aa3", "metadata": {}, "outputs": [], "source": ["be_p_type_summary.head()"]}, {"cell_type": "code", "execution_count": null, "id": "65bd24da-7f27-4c2e-a5f8-484b6f5223e5", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(be_p_type_summary, \"13u1H-zyxVWKR1AjZ6dNjdrdHJW5i6HmvCcj8AjpTGHE\", \"be_p_type_raw\")"]}, {"cell_type": "markdown", "id": "4312483e-290a-4dfa-ae0f-8cb6f437e9f8", "metadata": {"tags": []}, "source": ["# backend store wise summary"]}, {"cell_type": "code", "execution_count": null, "id": "68c02aa7-8c56-4ec5-a388-515a79fc1645", "metadata": {}, "outputs": [], "source": ["def be_store_summary():\n", "    be_store_summary = \"\"\"\n", "    \n", "    with\n", "    base as\n", "        (select *,\n", "            row_number() over (partition by item_id, be_hot_outlet_id order by item_id) as be_item_count\n", "            from metrics.banner_event_metrics_reporting\n", "        ),\n", "\n", "    be_facility_view as\n", "        (select city_name, event_flag, assortment_type,\n", "            facility_id as fe_facility_id, be_facility_id, facility_name as fe_facility_name, be_facility_name,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 \n", "                        when be_net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as after_be_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end)*1.00/count(item_id) as after_pp_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 else 0 end)*1.00/count(item_id) as after_intranist_availability,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(min_quantity) as min_quantity,\n", "            sum(max_quantity) as max_quantity,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(case when be_item_count = 1 then be_net_inventory else 0 end) as be_inv,\n", "            sum(case when be_item_count = 1 then be_pending_putaway else 0 end) as be_pending_putaway_inv,\n", "            sum(case when be_item_count = 1 then be_open_sto_qty else 0 end) as be_open_sto_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_po_quantity else 0 end) as be_ttl_po_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_grn_quantity else 0 end) as be_ttl_po_grn_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_open_po_quantity else 0 end) as be_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(case when be_item_count = 1 then be_today_dump_quantity else 0 end) as be_today_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_today_dump_value else 0 end) as be_today_dump_value,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_quantity else 0 end) as be_yesterday_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_value else 0 end) as be_yesterday_dump_value,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_quantity else 0 end) as be_l7days_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_value else 0 end) as be_l7days_dump_value,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_quantity else 0 end) as be_ttl_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_value else 0 end) as be_ttl_dump_value,\n", "            \n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "            \n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "            \n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "                from base\n", "\n", "                    group by 1,2,3,4,5,6,7\n", "        )\n", "\n", "            select * from be_facility_view \n", "                order by \n", "                    case when be_facility_name = 'All' then 'A' else be_facility_name end, \n", "                    event_flag, assortment_type, fe_facility_id\n", "\n", "    \"\"\"\n", "    return pd.read_sql_query(be_store_summary, redshift)\n", "\n", "\n", "start = time.time()\n", "be_store_summary = be_store_summary()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "28f11927-4e2d-4307-b36e-46b2ce94b3cd", "metadata": {}, "outputs": [], "source": ["be_store_summary.head()"]}, {"cell_type": "code", "execution_count": null, "id": "464b43c3-d4a2-4973-810a-af4ab26d49d1", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    be_store_summary,\n", "    \"13u1H-zyxVWKR1AjZ6dNjdrdHJW5i6HmvCcj8AjpTGHE\",\n", "    \"be_store_wise_raw\",\n", ")"]}, {"cell_type": "markdown", "id": "822c2eee-0760-46aa-8fa1-fd16f69cc9e8", "metadata": {}, "source": ["# city backend summary"]}, {"cell_type": "code", "execution_count": null, "id": "ff5edaf3-60d5-4bc5-b394-d9c7951b9fd1", "metadata": {}, "outputs": [], "source": ["def city_be_summary():\n", "    city_be_summary = \"\"\"\n", "    \n", "    with\n", "    base as\n", "        (select *,\n", "            row_number() over (partition by item_id, be_hot_outlet_id order by item_id) as be_item_count\n", "            from metrics.banner_event_metrics_reporting\n", "        ),\n", "\n", "    total_view as\n", "        (select 'All' as city_name, event_flag, assortment_type, \n", "            be_facility_id, be_facility_name,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 \n", "                        when be_net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as after_be_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end)*1.00/count(item_id) as after_pp_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 else 0 end)*1.00/count(item_id) as after_intranist_availability,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(min_quantity) as min_quantity,\n", "            sum(max_quantity) as max_quantity,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(case when be_item_count = 1 then be_net_inventory else 0 end) as be_inv,\n", "            sum(case when be_item_count = 1 then be_pending_putaway else 0 end) as be_pending_putaway_inv,\n", "            sum(case when be_item_count = 1 then be_open_sto_qty else 0 end) as be_open_sto_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_po_quantity else 0 end) as be_ttl_po_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_grn_quantity else 0 end) as be_ttl_po_grn_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_open_po_quantity else 0 end) as be_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(case when be_item_count = 1 then be_today_dump_quantity else 0 end) as be_today_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_today_dump_value else 0 end) as be_today_dump_value,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_quantity else 0 end) as be_yesterday_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_value else 0 end) as be_yesterday_dump_value,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_quantity else 0 end) as be_l7days_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_value else 0 end) as be_l7days_dump_value,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_quantity else 0 end) as be_ttl_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_value else 0 end) as be_ttl_dump_value,\n", "            \n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "            \n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "            \n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "                from base\n", "\n", "                    group by 1,2,3,4,5\n", "        ),\n", "\n", "    backend_view as\n", "        (select city_name, event_flag, assortment_type, \n", "            be_facility_id, be_facility_name,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 \n", "                        when be_net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as after_be_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end)*1.00/count(item_id) as after_pp_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 else 0 end)*1.00/count(item_id) as after_intranist_availability,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(min_quantity) as min_quantity,\n", "            sum(max_quantity) as max_quantity,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(case when be_item_count = 1 then be_net_inventory else 0 end) as be_inv,\n", "            sum(case when be_item_count = 1 then be_pending_putaway else 0 end) as be_pending_putaway_inv,\n", "            sum(case when be_item_count = 1 then be_open_sto_qty else 0 end) as be_open_sto_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_po_quantity else 0 end) as be_ttl_po_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_grn_quantity else 0 end) as be_ttl_po_grn_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_open_po_quantity else 0 end) as be_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(case when be_item_count = 1 then be_today_dump_quantity else 0 end) as be_today_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_today_dump_value else 0 end) as be_today_dump_value,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_quantity else 0 end) as be_yesterday_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_value else 0 end) as be_yesterday_dump_value,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_quantity else 0 end) as be_l7days_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_value else 0 end) as be_l7days_dump_value,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_quantity else 0 end) as be_ttl_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_value else 0 end) as be_ttl_dump_value,\n", "            \n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "            \n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "            \n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "                from base\n", "\n", "                    group by 1,2,3,4,5\n", "        ),\n", "\n", "    final as\n", "        (select * from total_view\n", "\n", "            union\n", "\n", "                select * from backend_view\n", "        )\n", "\n", "            select * from final \n", "                order by \n", "                    case when city_name = 'All' then 'A' else city_name end,\n", "                        event_flag, assortment_type, be_facility_name\n", "\n", "    \"\"\"\n", "    return pd.read_sql_query(city_be_summary, redshift)\n", "\n", "\n", "start = time.time()\n", "city_be_summary = city_be_summary()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "f5ff7048-6149-48bd-a9f9-50f1307b971e", "metadata": {}, "outputs": [], "source": ["city_be_summary.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b8893329-7a4b-4163-869d-e15304108902", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(city_be_summary, \"13u1H-zyxVWKR1AjZ6dNjdrdHJW5i6HmvCcj8AjpTGHE\", \"be_city_raw\")"]}, {"cell_type": "code", "execution_count": null, "id": "47e55129-8c94-4285-b959-ff61f89c9685", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "73a5e7bd-12b8-421d-94c3-14fd7f6e24e8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}