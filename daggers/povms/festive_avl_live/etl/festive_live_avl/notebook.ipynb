{"cells": [{"cell_type": "code", "execution_count": null, "id": "978fdfb3-0d3c-4948-80b6-62083c33ae5c", "metadata": {}, "outputs": [], "source": ["!pip install pytz"]}, {"cell_type": "code", "execution_count": null, "id": "cbff3c43-5853-473c-818f-32be5e362642", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import os\n", "from datetime import date, datetime, timedelta, time\n", "from tqdm import tqdm\n", "import time\n", "\n", "# import datetime\n", "import pytz\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "f67d29cc-f33b-463a-8fc6-a5feafa41ae8", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "def to_trino(df_to_trino, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)\n", "\n", "\n", "def gsheet_to_df(sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            df = pb.from_sheets(sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Read Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Read {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and created a dataframe of size {size}\"\n", "            )\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "55af66d1-a8e9-459b-b6b5-6db1fc971379", "metadata": {}, "outputs": [], "source": ["manual = False"]}, {"cell_type": "code", "execution_count": null, "id": "d27e7edc-c31d-4bac-96e0-b252eb479879", "metadata": {}, "outputs": [], "source": ["if manual:\n", "    sheet = pd.read_csv(\"live.csv\")\n", "else:\n", "    sheet = gsheet_to_df(\"1zeYlST37g5ig8u5DwCvSVoGZtI6VgfJMWgIGCW-w3Cs\", \"live_festival_view\")"]}, {"cell_type": "code", "execution_count": null, "id": "b0cd9835-de0e-48cc-b91c-a09d773412b2", "metadata": {}, "outputs": [], "source": ["ist = pytz.timezone(\"Asia/Kolkata\")\n", "current_ist = datetime.now(ist)"]}, {"cell_type": "code", "execution_count": null, "id": "cdd8d461-512f-4ec4-b765-600edd4a5a8f", "metadata": {}, "outputs": [], "source": ["sheet"]}, {"cell_type": "code", "execution_count": null, "id": "9b1865f3-8679-4460-98ef-15a61ac3c901", "metadata": {}, "outputs": [], "source": ["ist = pytz.timezone(\"Asia/Kolkata\")\n", "\n", "# Get current UTC time, subtract 55 minutes\n", "utc_now = datetime.utcnow() - <PERSON><PERSON><PERSON>(minutes=55)\n", "\n", "# Localize to IST\n", "ist_time = utc_now.replace(tzinfo=pytz.utc).astimezone(ist)\n", "\n", "# Format as yyyy-mm-dd\n", "ist_date_str = ist_time.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "2ff7eae8-8b4a-4744-b3e8-60fa436f2210", "metadata": {}, "outputs": [], "source": ["ist_date_str"]}, {"cell_type": "markdown", "id": "b62570b1-5030-4501-a915-dc171f128ff8", "metadata": {}, "source": ["### City_Lvl_View"]}, {"cell_type": "code", "execution_count": null, "id": "db6d04f9-3f7c-4fd1-90c7-beb2c0ef0488", "metadata": {}, "outputs": [], "source": ["for index, row in sheet.iterrows():\n", "    if row[\"city_live\"] != \"Yes\":\n", "        print(f\"city_live_view is not active\")\n", "        break\n", "    print(f\"Started for festival {row['festival']}\")\n", "    festival = row[\"festival\"]\n", "    festival_name = row[\"festival\"]\n", "    cpd_date = row[\"cpd_date\"]\n", "    sale_end_date = row[\"sale_end_date\"]\n", "    festive_start_date = row[\"festival_start_date\"]\n", "    festive_end_date = row[\"festival_end_date\"]\n", "    bau_start_date = row[\"bau_start_date\"]\n", "    bau_end_date = row[\"bau_end_date\"]\n", "    print(f\"reading sql for {festival_name}\")\n", "    sql_min = f\"\"\"\n", "    select min(date_) as min_date, cast(current_timestamp AT TIME ZONE 'Asia/Kolkata' as date) as curr_date\n", "        from blinkit_iceberg.supply_etls.festive_city_date_item_weight\n", "        where festival_name = '{festival}'\n", "    \"\"\"\n", "    df_min = read_sql_query(sql_min, CON_TRINO)\n", "    if df_min[\"min_date\"].iloc[0] > df_min[\"curr_date\"].iloc[0]:\n", "        date = df_min[\"min_date\"].iloc[0]\n", "    else:\n", "        date = df_min[\"curr_date\"].iloc[0]\n", "    sql_city = f\"\"\"\n", "                            with bhw as (\n", "          select\n", "            cast(cast(backend_facility_id as double) as integer) as backend_facility_id,\n", "            cast(order_hour as integer) as order_hour,\n", "            cast(weights as double) as weights\n", "          from\n", "            blinkit_iceberg.supply_etls.backend_hour_weights\n", "          where\n", "            updated_at =(\n", "              select\n", "                max(updated_at)\n", "              from\n", "                blinkit_iceberg.supply_etls.backend_hour_weights\n", "              where\n", "                updated_at <= date '{cpd_date}'  -- cpd_date\n", "            )\n", "        ),\n", "        bhw_fall as -- If there is no hour weight available then consider minimum weight and also it helps to elimate cases where there are no weights (In that case min_weight will be null) in those cases all the hours are given equal weight\n", "        (\n", "          select\n", "            backend_facility_id,\n", "            min(weights) as min_weight\n", "          from\n", "            bhw\n", "          group by\n", "            1\n", "        ),\n", "        bsw as (\n", "          select\n", "            cast(cast(backend_facility_id as double) as integer) as backend_facility_id,\n", "            facility_id,\n", "            cast(store_weight as double) as store_weight\n", "          from\n", "            blinkit_iceberg.supply_etls.backend_store_weights\n", "          where\n", "            updated_at = (\n", "              select\n", "                max(updated_at)\n", "              from\n", "                blinkit_iceberg.supply_etls.backend_store_weights\n", "              where\n", "                updated_at <= date '{cpd_date}' -- cpd_date\n", "            )\n", "        ),\n", "        bsw_fall_check as -- similar logic as why bhw_fall is there\n", "        (\n", "          select\n", "            backend_facility_id,\n", "            min(store_weight) as min_store_weight\n", "          from\n", "            bsw\n", "          group by\n", "            1\n", "        ),\n", "        chw as (\n", "          select\n", "            city,\n", "            order_hour,\n", "            cast(weights as double) as weights\n", "          from\n", "            blinkit_iceberg.supply_etls.city_hour_weights\n", "          where\n", "            updated_at = (\n", "              select\n", "                max(updated_at)\n", "              from\n", "                blinkit_iceberg.supply_etls.city_hour_weights\n", "              where\n", "                updated_at <= date '{cpd_date}' -- cpd_date\n", "            )\n", "        ),\n", "        chw_fall as (\n", "          select\n", "            city,\n", "            min(weights) as min_weight\n", "          from\n", "            chw\n", "          group by\n", "            1\n", "        ),\n", "        csw as (\n", "          select\n", "            city,\n", "            facility_id,\n", "            cast(store_weight as double) as weight\n", "          from\n", "            blinkit_iceberg.supply_etls.city_store_weights\n", "          where\n", "            updated_at = (\n", "              select\n", "                max(updated_at)\n", "              from\n", "                blinkit_iceberg.supply_etls.city_store_weights\n", "              where\n", "                updated_at <= date '{cpd_date}' -- cpd_date\n", "            )\n", "            -- and cast(store_weight as double)>0\n", "        ),\n", "        csw_fall as (\n", "          select\n", "            city,\n", "            min(weight) as min_store_weight\n", "          from\n", "            csw\n", "          -- where weight > 0\n", "          group by\n", "            1\n", "          -- having min(weight)>0\n", "        ),\n", "        -- This cte gives cy_year search spikes and tags few articles as both critical and festive_core\n", "        -- Some tweaks has to be done while computing current festival avl view\n", "        item_prop as (\n", "          select * from \n", "          blinkit_iceberg.supply_etls.festive_flags\n", "          where festival_name = '{festival}'\n", "        ),\n", "        -- This is to filter festive_in_out  articles\n", "        festive_in_out as (\n", "          select * from \n", "          blinkit_iceberg.supply_etls.festive_in_out_flag\n", "          where festival_name = '{festival}'\n", "        ),\n", "        biw as (\n", "          select\n", "            backend_facility_id,\n", "            item_id,\n", "            cast(weights as double) as weights\n", "          from\n", "            blinkit_iceberg.supply_etls.backend_item_weights\n", "          where\n", "            updated_at = (\n", "              select\n", "                max(updated_at)\n", "              from\n", "                blinkit_iceberg.supply_etls.backend_item_weights\n", "              where\n", "                updated_at <= date '{cpd_date}' --cpd_date\n", "            )\n", "        ),\n", "        ciw as (\n", "          select\n", "            city,\n", "            item_id,\n", "            cast(weights as double) as weights\n", "          from\n", "            blinkit_iceberg.supply_etls.city_item_weights\n", "          where\n", "            updated_at = (\n", "              select\n", "                max(updated_at)\n", "              from\n", "                blinkit_iceberg.supply_etls.city_item_weights\n", "              where\n", "                updated_at <= date '{cpd_date}' --cpd_date\n", "            )\n", "        ),\n", "        city_weight as (\n", "          select\n", "            city,\n", "            cast(weight as double) as weights\n", "          from\n", "            blinkit_iceberg.supply_etls.city_weights\n", "          where\n", "            updated_at = (\n", "              select\n", "                max(updated_at)\n", "              from\n", "                blinkit_iceberg.supply_etls.city_weights\n", "              where\n", "                updated_at <= date '{cpd_date}' --cpd_date\n", "            )\n", "        ),\n", "        city_weight_fall as (\n", "          select\n", "            min(weights) as min_weight\n", "          from\n", "            city_weight\n", "        ),\n", "        be_pan_india as (\n", "          select * from\n", "          blinkit_iceberg.interim.festive_be_pan_india\n", "        ),\n", "        be_pan_india_fall as (\n", "          select\n", "            min(weight) as min_weight\n", "          from\n", "            be_pan_india\n", "        ),\n", "        raw_filtered as \n", "        (SELECT \n", "                distinct insert_ds_ist,\n", "            hour_,\n", "            fe_facility_id,\n", "            be_facility_id,\n", "            item_id,\n", "            fe_city_name,\n", "            fe_inv_outlet_id,\n", "            p_type,\n", "            item_substate,\n", "            fe_avail_flag,\n", "            sto_cpd,\n", "            be_inv\n", "          FROM blinkit_iceberg.supply_etls.inventory_replenishment_metrics\n", "          where\n", "            insert_ds_ist = date '{ist_date_str}'\n", "            and (\n", "              active_outlet_flag = 1\n", "              or active_outlet_flag is null\n", "            )\n", "            and item_substate in (1, 3)\n", "            -- and ((fastival_tag = '{festival_name}') or (fastival_tag = 'BAU')) -- festival_name\n", "            and hour_ = (select max(hour_) from blinkit_iceberg.supply_etls.inventory_replenishment_metrics\n", "            where insert_ds_ist = date '{ist_date_str}' )\n", "            -- cast(CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata' - interval '55' minute as date)\n", "        ),\n", "        fmtt as (\n", "          select\n", "            distinct insert_ds_ist,\n", "            hour_,\n", "            fe_facility_id,\n", "            be_facility_id,\n", "            item_id,\n", "            fe_city_name,\n", "            fe_inv_outlet_id,\n", "            p_type,\n", "            fe_avail_flag,\n", "            sto_cpd,\n", "            be_inv,\n", "            case\n", "              when fe_avail_flag = 0\n", "              and (2 * sto_cpd) <= 1 then 1\n", "              when fe_avail_flag = 0\n", "              and (2 * sto_cpd) > 1 then (2 * sto_cpd)\n", "              else 0 end as req_inv\n", "          from\n", "            raw_filtered\n", "        ),\n", "        bebe_req_core as (\n", "          select\n", "            insert_ds_ist,\n", "            hour_,\n", "            be_facility_id,\n", "            item_id,\n", "            sum(req_inv) as req_inv\n", "          from\n", "            fmtt\n", "          group by\n", "            1,2,3,4\n", "        ),\n", "        fmta as (\n", "          select\n", "            fmtt.*,\n", "            case\n", "              when bc.req_inv <= be_inv then 1\n", "              else fe_avail_flag\n", "            end as be_fe_avail_flag\n", "          from\n", "            fmtt\n", "            left join bebe_req_core bc on bc.be_facility_id = fmtt.be_facility_id\n", "            and bc.item_id = fmtt.item_id\n", "            and bc.insert_ds_ist = fmtt.insert_ds_ist\n", "            and bc.hour_ = fmtt.hour_\n", "        ),\n", "        festive_weigths_ciw as (\n", "        select city as city_name, item_id, final_weight as weight\n", "        from blinkit_iceberg.supply_etls.festive_city_date_item_weight\n", "        where festival_name = '{festival_name}'\n", "        and date_ = date '{date}'\n", "        ),\n", "        festive_weights_biw as (\n", "        select be_facility_id, item_id, weight\n", "        from blinkit_iceberg.supply_etls.festive_backend_item_weight\n", "        where event_name = '{festival_name}'\n", "        and event_name is not null\n", "        ),\n", "\n", "        final_raw as \n", "        (\n", "          select\n", "            fmta.*,\n", "            ip.critical_flag,\n", "            ip.festive_core_flag,\n", "            coalesce(biw.weights, 0) as item_weight,\n", "            coalesce(bhw.weights, bf.min_weight, 1) as hour_weight,\n", "            case\n", "              when bfc.min_store_weight is not null then coalesce(bsw.store_weight, 0)\n", "              else 1\n", "            end as store_weight,\n", "            coalesce(fwb.weight, 0) as festive_item_weight,\n", "            case\n", "              when fio.product_type is null then 0\n", "              else 1\n", "            end as fes_in_out_flag,\n", "            coalesce(fwc.weight, 0) as festive_city_item_weight,\n", "            coalesce(ciw.weights, 0) as city_item_weight,\n", "            coalesce(chw.weights, cf.min_weight, 1) as city_hour_weight,\n", "            coalesce(csw.weight, cfc.min_store_weight, 1) as city_store_weight,\n", "            -- coalesce(csw.weight,0) as city_store_weight,\n", "            coalesce(cw.weights, cwf.min_weight, 0) as pan_city_weight,\n", "            coalesce(bpi.weight, bpif.min_weight, 0) as pan_be_weight\n", "          from\n", "            fmta\n", "            left join item_prop ip on fmta.p_type = ip.product_type\n", "            left join biw on biw.backend_facility_id = fmta.be_facility_id\n", "            and biw.item_id = fmta.item_id\n", "            left join bhw on bhw.backend_facility_id = fmta.be_facility_id\n", "            and bhw.order_hour = fmta.hour_\n", "            left join bsw on bsw.backend_facility_id = fmta.be_facility_id\n", "            and bsw.facility_id = fmta.fe_facility_id\n", "            left join bhw_fall bf on bf.backend_facility_id = fmta.be_facility_id\n", "            left join festive_weights_biw fwb on fwb.be_facility_id = fmta.be_facility_id\n", "            and fwb.item_id = fmta.item_id\n", "            left join bsw_fall_check bfc on bfc.backend_facility_id = fmta.be_facility_id\n", "            left join festive_in_out fio on fio.product_type = fmta.p_type\n", "            left join festive_weigths_ciw fwc on fwc.city_name = fmta.fe_city_name\n", "            and fwc.item_id = fmta.item_id\n", "            left join ciw on ciw.city = fmta.fe_city_name\n", "            and ciw.item_id = fmta.item_id\n", "            left join chw on chw.city = fmta.fe_city_name\n", "            and chw.order_hour = fmta.hour_\n", "            left join csw on csw.city = fmta.fe_city_name\n", "            and csw.facility_id = fmta.fe_facility_id\n", "            left join chw_fall cf on cf.city = fmta.fe_city_name\n", "            left join csw_fall cfc on cfc.city = fmta.fe_city_name\n", "            left join city_weight cw on cw.city = fmta.fe_city_name\n", "            left join be_pan_india bpi on fmta.be_facility_id = bpi.be_facility_id\n", "            cross join be_pan_india_fall bpif\n", "            cross join city_weight_fall cwf\n", "        ),\n", "        avl_raw as (\n", "            select \n", "                fe_city_name,\n", "                insert_ds_ist,\n", "                p_type,\n", "                fe_facility_id,\n", "                city_store_weight,\n", "                city_hour_weight,\n", "\n", "                -- fe_avail_flag-based metrics\n", "                case when sum(city_item_weight) = 0 then 0 else\n", "                    sum(fe_avail_flag * city_item_weight) / sum(city_item_weight) \n", "                end as fe_avl,\n", "\n", "                case when sum((1 - critical_flag) * city_item_weight) = 0 then 0 else\n", "                    sum(fe_avail_flag * (1 - critical_flag) * city_item_weight) / sum((1 - critical_flag) * city_item_weight)\n", "                end as non_festive_avl,\n", "\n", "                case when sum((case when critical_flag = 0 then 0 else \n", "                (critical_flag - greatest(festive_core_flag, fes_in_out_flag))\n", "                end) * city_item_weight) = 0 then 0 else\n", "                    sum(fe_avail_flag * (case when critical_flag = 0 then 0 else \n", "                (critical_flag - greatest(festive_core_flag, fes_in_out_flag))\n", "                end) * city_item_weight) /\n", "                    nullif(sum((case when critical_flag = 0 then 0 else \n", "                (critical_flag - greatest(festive_core_flag, fes_in_out_flag))\n", "                end) * city_item_weight), 0)\n", "                end as critical_avl,\n", "                \n", "                \n", "                \n", "                case when sum((case when festive_core_flag = 0 then 0 else \n", "                (festive_core_flag - fes_in_out_flag) end) * festive_city_item_weight) = 0 then 0 else\n", "                    sum(fe_avail_flag * (case when festive_core_flag = 0 then 0 else \n", "                (festive_core_flag - fes_in_out_flag) end) * festive_city_item_weight) /\n", "                    nullif(sum((case when festive_core_flag = 0 then 0 else \n", "                (festive_core_flag - fes_in_out_flag) end) * festive_city_item_weight), 0)\n", "                end as festive_core_avl,\n", "\n", "                case when sum(fes_in_out_flag * festive_city_item_weight) = 0 then 0 else\n", "                    sum(fes_in_out_flag * fe_avail_flag * festive_city_item_weight) /\n", "                    nullif(sum(fes_in_out_flag * festive_city_item_weight), 0)\n", "                end as festive_in_out_avl,\n", "\n", "                -- be_fe_avail_flag-based metrics\n", "                case when sum(city_item_weight) = 0 then 0 else\n", "                    sum(be_fe_avail_flag * city_item_weight) / sum(city_item_weight) \n", "                end as be_fe_avl,\n", "\n", "                case when sum((1 - critical_flag) * city_item_weight) = 0 then 0 else\n", "                    sum(be_fe_avail_flag * (1 - critical_flag) * city_item_weight) / sum((1 - critical_flag) * city_item_weight)\n", "                end as be_non_festive_avl,\n", "\n", "                \n", "                \n", "                case when sum((case when critical_flag = 0 then 0 \n", "                else (critical_flag - greatest(festive_core_flag, fes_in_out_flag)) end) * city_item_weight) = 0 then 0 else\n", "                    sum(be_fe_avail_flag * (case when critical_flag = 0 then 0 \n", "                else (critical_flag - greatest(festive_core_flag, fes_in_out_flag)) end) * city_item_weight) /\n", "                    nullif(sum((case when critical_flag = 0 then 0 \n", "                else (critical_flag - greatest(festive_core_flag, fes_in_out_flag)) end) * city_item_weight), 0)\n", "                end as be_critical_avl,\n", "\n", "                \n", "\n", "                case when sum((case when festive_core_flag = 0 then 0 else\n", "                (festive_core_flag - fes_in_out_flag) end) * festive_city_item_weight) = 0 then 0 else\n", "                    sum(be_fe_avail_flag * (case when festive_core_flag = 0 then 0 else\n", "                (festive_core_flag - fes_in_out_flag) end) * festive_city_item_weight) /\n", "                    nullif(sum((case when festive_core_flag = 0 then 0 else\n", "                (festive_core_flag - fes_in_out_flag) end) * festive_city_item_weight), 0)\n", "                end as be_festive_core_avl,\n", "\n", "                case when sum(fes_in_out_flag * festive_city_item_weight) = 0 then 0 else\n", "                    sum(fes_in_out_flag * be_fe_avail_flag * festive_city_item_weight) /\n", "                    nullif(sum(fes_in_out_flag * festive_city_item_weight), 0)\n", "                end as be_festive_in_out_avl\n", "\n", "            from final_raw \n", "            group by 1,2,3,4,5,6\n", "        ),\n", "        avl_raw_overall as (\n", "            select \n", "                fe_city_name,\n", "                insert_ds_ist,\n", "                'overall' as p_type,\n", "                fe_facility_id,\n", "                city_store_weight,\n", "                city_hour_weight,\n", "\n", "                -- fe_avail_flag-based metrics\n", "                case when sum(city_item_weight) = 0 then 0 else\n", "                    sum(fe_avail_flag * city_item_weight) / sum(city_item_weight) \n", "                end as fe_avl,\n", "\n", "                case when sum((1 - critical_flag) * city_item_weight) = 0 then 0 else\n", "                    sum(fe_avail_flag * (1 - critical_flag) * city_item_weight) / sum((1 - critical_flag) * city_item_weight)\n", "                end as non_festive_avl,\n", "\n", "                case when sum((case when critical_flag = 0 then 0 else \n", "                (critical_flag - greatest(festive_core_flag, fes_in_out_flag))\n", "                end) * city_item_weight) = 0 then 0 else\n", "                    sum(fe_avail_flag * (case when critical_flag = 0 then 0 else \n", "                (critical_flag - greatest(festive_core_flag, fes_in_out_flag))\n", "                end) * city_item_weight) /\n", "                    nullif(sum((case when critical_flag = 0 then 0 else \n", "                (critical_flag - greatest(festive_core_flag, fes_in_out_flag))\n", "                end) * city_item_weight), 0)\n", "                end as critical_avl,\n", "                \n", "                \n", "                \n", "                case when sum((case when festive_core_flag = 0 then 0 else \n", "                (festive_core_flag - fes_in_out_flag) end) * festive_city_item_weight) = 0 then 0 else\n", "                    sum(fe_avail_flag * (case when festive_core_flag = 0 then 0 else \n", "                (festive_core_flag - fes_in_out_flag) end) * festive_city_item_weight) /\n", "                    nullif(sum((case when festive_core_flag = 0 then 0 else \n", "                (festive_core_flag - fes_in_out_flag) end) * festive_city_item_weight), 0)\n", "                end as festive_core_avl,\n", "\n", "\n", "                case when sum(fes_in_out_flag * festive_city_item_weight) = 0 then 0 else\n", "                    sum(fes_in_out_flag * fe_avail_flag * festive_city_item_weight) /\n", "                    nullif(sum(fes_in_out_flag * festive_city_item_weight), 0)\n", "                end as festive_in_out_avl,\n", "\n", "                -- be_fe_avail_flag-based metrics\n", "                case when sum(city_item_weight) = 0 then 0 else\n", "                    sum(be_fe_avail_flag * city_item_weight) / sum(city_item_weight) \n", "                end as be_fe_avl,\n", "\n", "                case when sum((1 - critical_flag) * city_item_weight) = 0 then 0 else\n", "                    sum(be_fe_avail_flag * (1 - critical_flag) * city_item_weight) / sum((1 - critical_flag) * city_item_weight)\n", "                end as be_non_festive_avl,\n", "\n", "                case when sum((case when critical_flag = 0 then 0 \n", "                else (critical_flag - greatest(festive_core_flag, fes_in_out_flag)) end) * city_item_weight) = 0 then 0 else\n", "                    sum(be_fe_avail_flag * (case when critical_flag = 0 then 0 \n", "                else (critical_flag - greatest(festive_core_flag, fes_in_out_flag)) end) * city_item_weight) /\n", "                    nullif(sum((case when critical_flag = 0 then 0 \n", "                else (critical_flag - greatest(festive_core_flag, fes_in_out_flag)) end) * city_item_weight), 0)\n", "                end as be_critical_avl,\n", "\n", "                \n", "\n", "                case when sum((case when festive_core_flag = 0 then 0 else\n", "                (festive_core_flag - fes_in_out_flag) end) * festive_city_item_weight) = 0 then 0 else\n", "                    sum(be_fe_avail_flag * (case when festive_core_flag = 0 then 0 else\n", "                (festive_core_flag - fes_in_out_flag) end) * festive_city_item_weight) /\n", "                    nullif(sum((case when festive_core_flag = 0 then 0 else\n", "                (festive_core_flag - fes_in_out_flag) end) * festive_city_item_weight), 0)\n", "                end as be_festive_core_avl,\n", "\n", "                case when sum(fes_in_out_flag * festive_city_item_weight) = 0 then 0 else\n", "                    sum(fes_in_out_flag * be_fe_avail_flag * festive_city_item_weight) /\n", "                    nullif(sum(fes_in_out_flag * festive_city_item_weight), 0)\n", "                end as be_festive_in_out_avl\n", "\n", "            from final_raw \n", "            group by 1,2,3,4,5,6\n", "        ),\n", "        avl_raw_new as (\n", "            select * from avl_raw\n", "            union all \n", "            select * from avl_raw_overall\n", "        ),\n", "        avl_city as (\n", "            select \n", "                fe_city_name,\n", "                insert_ds_ist,\n", "                p_type,\n", "                city_hour_weight,\n", "\n", "                -- fe_avail_flag-based city metrics\n", "                sum(fe_avl * city_store_weight) / sum(city_store_weight) as avl,\n", "                sum(non_festive_avl * city_store_weight) / sum(city_store_weight) as non_festive_avl,\n", "                sum(critical_avl * city_store_weight) / sum(city_store_weight) as critical_avl,\n", "                sum(festive_core_avl * city_store_weight) / sum(city_store_weight) as festive_core_avl,\n", "                sum(festive_in_out_avl * city_store_weight) / sum(city_store_weight) as festive_in_out_avl,\n", "\n", "                -- be_fe_avail_flag-based city metrics\n", "                sum(be_fe_avl * city_store_weight) / sum(city_store_weight) as be_avl,\n", "                sum(be_non_festive_avl * city_store_weight) / sum(city_store_weight) as be_non_festive_avl,\n", "                sum(be_critical_avl * city_store_weight) / sum(city_store_weight) as be_critical_avl,\n", "                sum(be_festive_core_avl * city_store_weight) / sum(city_store_weight) as be_festive_core_avl,\n", "                sum(be_festive_in_out_avl * city_store_weight) / sum(city_store_weight) as be_festive_in_out_avl\n", "\n", "            from avl_raw_new \n", "            group by 1,2,3,4\n", "        )\n", "        select \n", "        \n", "            \n", "            '{festival_name}' as festive_name,\n", "            cast((current_timestamp + INTERVAL '330' MINUTE) as timestamp) AS updated_at_ist,\n", "            insert_ds_ist,\n", "            fe_city_name,\n", "            p_type,\n", "            avl,\n", "            non_festive_avl,\n", "            critical_avl,\n", "            festive_core_avl,\n", "            festive_in_out_avl,\n", "            be_avl as be_fe_avl,\n", "            be_non_festive_avl as be_fe_non_festive_avl,\n", "            be_critical_avl as be_fe_critical_avl,\n", "            be_festive_core_avl as be_fe_festive_core_avl,\n", "            be_festive_in_out_avl as be_fe_festive_in_out_avl\n", "        from avl_city\n", "\"\"\"\n", "    column_dtypes = [\n", "        {\"name\": \"festive_name\", \"type\": \"varchar\", \"description\": \"name_of_festival\"},\n", "        {\n", "            \"name\": \"updated_at_ist\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"Updated timestamp in IST\",\n", "        },\n", "        {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"Data snapshot date (yyyy-mm-dd)\"},\n", "        {\"name\": \"fe_city_name\", \"type\": \"varchar\", \"description\": \"FE city name\"},\n", "        {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"Product type (e.g., bau, sale)\"},\n", "        {\"name\": \"avl\", \"type\": \"double\", \"description\": \"FE availability\"},\n", "        {\"name\": \"non_festive_avl\", \"type\": \"double\", \"description\": \"FE non-festive availability\"},\n", "        {\"name\": \"critical_avl\", \"type\": \"double\", \"description\": \"FE critical availability\"},\n", "        {\n", "            \"name\": \"festive_core_avl\",\n", "            \"type\": \"double\",\n", "            \"description\": \"FE festive core availability\",\n", "        },\n", "        {\n", "            \"name\": \"festive_in_out_avl\",\n", "            \"type\": \"double\",\n", "            \"description\": \"FE festive in-out availability\",\n", "        },\n", "        {\"name\": \"be_fe_avl\", \"type\": \"double\", \"description\": \"BE-FE availability\"},\n", "        {\n", "            \"name\": \"be_fe_non_festive_avl\",\n", "            \"type\": \"double\",\n", "            \"description\": \"BE-FE non-festive availability\",\n", "        },\n", "        {\n", "            \"name\": \"be_fe_critical_avl\",\n", "            \"type\": \"double\",\n", "            \"description\": \"BE-FE critical availability\",\n", "        },\n", "        {\n", "            \"name\": \"be_fe_festive_core_avl\",\n", "            \"type\": \"double\",\n", "            \"description\": \"BE-FE festive core availability\",\n", "        },\n", "        {\n", "            \"name\": \"be_fe_festive_in_out_avl\",\n", "            \"type\": \"double\",\n", "            \"description\": \"BE-FE festive in-out availability\",\n", "        },\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"supply_etls\",\n", "        \"table_name\": \"festive_city_avl_live\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"updated_at_ist\", \"festive_name\", \"fe_city_name\", \"p_type\"],\n", "        \"partition_key\": [\"festive_name\"],\n", "        \"load_type\": \"append\",  # append, truncate or upsert,\n", "        \"table_description\": \"festive_city_avl_live\",\n", "    }\n", "    # print(sql_city)\n", "    try:\n", "        # to_trino(sql_city, **kwargs)\n", "        pb.to_trino(sql_city, **kwargs)\n", "        # print(sql_city)\n", "        # df = read_sql_query(sql_city, CON_TRINO)\n", "        print(f\"pushed data to  for festive_city_avl_live table {festival_name}\")\n", "    except Exception as e:\n", "        print(\"An error occurred:\", str(e))\n", "        print(f\"failed for festival {festival_name}\")\n", "        break"]}, {"cell_type": "markdown", "id": "4b34429d-2616-4ae4-82fd-61387a9be736", "metadata": {}, "source": ["### backend_view"]}, {"cell_type": "code", "execution_count": null, "id": "df71e8c0-8caf-4262-9473-980590a17c29", "metadata": {}, "outputs": [], "source": ["for index, row in sheet.iterrows():\n", "    #     Creating interim table-1\n", "    #     Need to check if data is present or not if not available then no data\n", "    if row[\"be_live\"] != \"Yes\":\n", "        print(f\"be_view is not active still\")\n", "        break\n", "    print(f\"Started for festival {row['festival']}\")\n", "    festival = row[\"festival\"]\n", "    festival_name = row[\"festival\"]\n", "    cpd_date = row[\"cpd_date\"]\n", "    sale_end_date = row[\"sale_end_date\"]\n", "    festive_start_date = row[\"festival_start_date\"]\n", "    festive_end_date = row[\"festival_end_date\"]\n", "    bau_start_date = row[\"bau_start_date\"]\n", "    bau_end_date = row[\"bau_end_date\"]\n", "    print(f\"reading sql for {festival_name}\")\n", "    sql_min = f\"\"\"\n", "    select min(date_) as min_date, cast(current_timestamp AT TIME ZONE 'Asia/Kolkata' as date) as curr_date\n", "        from blinkit_iceberg.supply_etls.festive_city_date_item_weight\n", "        where festival_name = '{festival}'\n", "    \"\"\"\n", "    df_min = read_sql_query(sql_min, CON_TRINO)\n", "    if df_min[\"min_date\"].iloc[0] > df_min[\"curr_date\"].iloc[0]:\n", "        date = df_min[\"min_date\"].iloc[0]\n", "    else:\n", "        date = df_min[\"curr_date\"].iloc[0]\n", "    sql_city = f\"\"\"\n", "    with bhw as (\n", "          select\n", "            cast(cast(backend_facility_id as double) as integer) as backend_facility_id,\n", "            cast(order_hour as integer) as order_hour,\n", "            cast(weights as double) as weights\n", "          from\n", "            blinkit_iceberg.supply_etls.backend_hour_weights\n", "          where\n", "            updated_at =(\n", "              select\n", "                max(updated_at)\n", "              from\n", "                blinkit_iceberg.supply_etls.backend_hour_weights\n", "              where\n", "                updated_at <= date '{cpd_date}'  -- cpd_date\n", "            )\n", "        ),\n", "        bhw_fall as -- If there is no hour weight available then consider minimum weight and also it helps to elimate cases where there are no weights (In that case min_weight will be null) in those cases all the hours are given equal weight\n", "        (\n", "          select\n", "            backend_facility_id,\n", "            min(weights) as min_weight\n", "          from\n", "            bhw\n", "          group by\n", "            1\n", "        ),\n", "        bsw as (\n", "          select\n", "            cast(cast(backend_facility_id as double) as integer) as backend_facility_id,\n", "            facility_id,\n", "            cast(store_weight as double) as store_weight\n", "          from\n", "            blinkit_iceberg.supply_etls.backend_store_weights\n", "          where\n", "            updated_at = (\n", "              select\n", "                max(updated_at)\n", "              from\n", "                blinkit_iceberg.supply_etls.backend_store_weights\n", "              where\n", "                updated_at <= date '{cpd_date}' -- cpd_date\n", "            )\n", "            and cast(store_weight as double)>0\n", "        ),\n", "        bsw_fall_check as -- similar logic as why bhw_fall is there\n", "        (\n", "          select\n", "            backend_facility_id,\n", "            min(store_weight) as min_store_weight\n", "          from\n", "            bsw\n", "          group by\n", "            1\n", "        ),\n", "        chw as (\n", "          select\n", "            city,\n", "            order_hour,\n", "            cast(weights as double) as weights\n", "          from\n", "            blinkit_iceberg.supply_etls.city_hour_weights\n", "          where\n", "            updated_at = (\n", "              select\n", "                max(updated_at)\n", "              from\n", "                blinkit_iceberg.supply_etls.city_hour_weights\n", "              where\n", "                updated_at <= date '{cpd_date}' -- cpd_date\n", "            )\n", "        ),\n", "        chw_fall as (\n", "          select\n", "            city,\n", "            min(weights) as min_weight\n", "          from\n", "            chw\n", "          group by\n", "            1\n", "        ),\n", "        csw as (\n", "          select\n", "            city,\n", "            facility_id,\n", "            cast(store_weight as double) as weight\n", "          from\n", "            blinkit_iceberg.supply_etls.city_store_weights\n", "          where\n", "            updated_at = (\n", "              select\n", "                max(updated_at)\n", "              from\n", "                blinkit_iceberg.supply_etls.city_store_weights\n", "              where\n", "                updated_at <= date '{cpd_date}' -- cpd_date\n", "            )\n", "        ),\n", "        csw_fall as (\n", "          select\n", "            city,\n", "            min(weight) as min_store_weight\n", "          from\n", "            csw\n", "          group by\n", "            1\n", "        ),\n", "        -- This cte gives cy_year search spikes and tags few articles as both critical and festive_core\n", "        -- Some tweaks has to be done while computing current festival avl view\n", "        item_prop as (\n", "          select * from \n", "          blinkit_iceberg.supply_etls.festive_flags\n", "          where festival_name = '{festival}'\n", "        ),\n", "        -- This is to filter festive_in_out  articles\n", "        festive_in_out as (\n", "          select * from \n", "          blinkit_iceberg.supply_etls.festive_in_out_flag\n", "          where festival_name = '{festival}'\n", "        ),\n", "        biw as (\n", "          select\n", "            backend_facility_id,\n", "            item_id,\n", "            cast(weights as double) as weights\n", "          from\n", "            blinkit_iceberg.supply_etls.backend_item_weights\n", "          where\n", "            updated_at = (\n", "              select\n", "                max(updated_at)\n", "              from\n", "                blinkit_iceberg.supply_etls.backend_item_weights\n", "              where\n", "                updated_at <= date '{cpd_date}' --cpd_date\n", "            )\n", "        ),\n", "        ciw as (\n", "          select\n", "            city,\n", "            item_id,\n", "            cast(weights as double) as weights\n", "          from\n", "            blinkit_iceberg.supply_etls.city_item_weights\n", "          where\n", "            updated_at = (\n", "              select\n", "                max(updated_at)\n", "              from\n", "                blinkit_iceberg.supply_etls.city_item_weights\n", "              where\n", "                updated_at <= date '{cpd_date}' --cpd_date\n", "            )\n", "        ),\n", "        city_weight as (\n", "          select\n", "            city,\n", "            cast(weight as double) as weights\n", "          from\n", "            blinkit_iceberg.supply_etls.city_weights\n", "          where\n", "            updated_at = (\n", "              select\n", "                max(updated_at)\n", "              from\n", "                blinkit_iceberg.supply_etls.city_weights\n", "              where\n", "                updated_at <= date '{cpd_date}' --cpd_date\n", "            )\n", "        ),\n", "        city_weight_fall as (\n", "          select\n", "            min(weights) as min_weight\n", "          from\n", "            city_weight\n", "        ),\n", "        be_pan_india as (\n", "          select * from\n", "          blinkit_iceberg.interim.festive_be_pan_india\n", "        ),\n", "        be_pan_india_fall as (\n", "          select\n", "            min(weight) as min_weight\n", "          from\n", "            be_pan_india\n", "        ),\n", "        raw_filtered as \n", "        (SELECT \n", "                distinct insert_ds_ist,\n", "            hour_,\n", "            fe_facility_id,\n", "            be_facility_id,\n", "            be_facility_name,\n", "            item_id,\n", "            fe_city_name,\n", "            fe_inv_outlet_id,\n", "            p_type,\n", "            item_substate,\n", "            fe_avail_flag,\n", "            sto_cpd,\n", "            be_inv\n", "          FROM blinkit_iceberg.supply_etls.inventory_replenishment_metrics\n", "          where\n", "            insert_ds_ist = date '{ist_date_str}'\n", "            and (\n", "              active_outlet_flag = 1\n", "              or active_outlet_flag is null\n", "            )\n", "            and item_substate in (1, 3)\n", "            -- and ((fastival_tag = '{festival_name}') or (fastival_tag = 'BAU')) -- festival_name\n", "            and hour_ = (select max(hour_) from blinkit_iceberg.supply_etls.inventory_replenishment_metrics\n", "            where insert_ds_ist = date '{ist_date_str}')\n", "        ),\n", "        fmtt as (\n", "          select\n", "            distinct insert_ds_ist,\n", "            hour_,\n", "            fe_facility_id,\n", "            be_facility_id,\n", "            be_facility_name,\n", "            item_id,\n", "            fe_city_name,\n", "            fe_inv_outlet_id,\n", "            p_type,\n", "            fe_avail_flag,\n", "            sto_cpd,\n", "            be_inv,\n", "            case\n", "              when fe_avail_flag = 0\n", "              and (2 * sto_cpd) <= 1 then 1\n", "              when fe_avail_flag = 0\n", "              and (2 * sto_cpd) > 1 then (2 * sto_cpd)\n", "              else 0 end as req_inv\n", "          from\n", "            raw_filtered\n", "        ),\n", "        bebe_req_core as (\n", "          select\n", "            insert_ds_ist,\n", "            hour_,\n", "            be_facility_id,\n", "            item_id,\n", "            sum(req_inv) as req_inv\n", "          from\n", "            fmtt\n", "          group by\n", "            1,2,3,4\n", "        ),\n", "        fmta as (\n", "          select\n", "            fmtt.*,\n", "            case\n", "              when bc.req_inv <= be_inv then 1\n", "              else fe_avail_flag\n", "            end as be_fe_avail_flag\n", "          from\n", "            fmtt\n", "            left join bebe_req_core bc on bc.be_facility_id = fmtt.be_facility_id\n", "            and bc.item_id = fmtt.item_id\n", "            and bc.insert_ds_ist = fmtt.insert_ds_ist\n", "            and bc.hour_ = fmtt.hour_\n", "        ),\n", "        festive_weigths_ciw as \n", "        (\n", "            select city as city_name, item_id, final_weight as weight\n", "            from blinkit_iceberg.supply_etls.festive_city_date_item_weight\n", "            where festival_name = '{festival_name}'\n", "            and date_ = date '{date}'\n", "        ),\n", "        festive_weights_biw as\n", "        (   \n", "            select be_facility_id, item_id, weight\n", "            from \n", "            blinkit_iceberg.supply_etls.festive_backend_date_item_weight\n", "            where festival_name = '{festival_name}'\n", "            and date_ = date '{date}'\n", "        ),\n", "        final_raw as \n", "        (\n", "          select\n", "            fmta.*,\n", "            ip.critical_flag,\n", "            ip.festive_core_flag,\n", "            coalesce(biw.weights, 0) as item_weight,\n", "            coalesce(bhw.weights, bf.min_weight, 1) as hour_weight,\n", "            case\n", "              when bfc.min_store_weight is not null then coalesce(bsw.store_weight, 0)\n", "              else 1\n", "            end as store_weight,\n", "            coalesce(fwb.weight, 0) as festive_item_weight,\n", "            case\n", "              when fio.product_type is null then 0\n", "              else 1\n", "            end as fes_in_out_flag,\n", "            coalesce(fwc.weight, 0) as festive_city_item_weight,\n", "            coalesce(ciw.weights, 0) as city_item_weight,\n", "            coalesce(chw.weights, cf.min_weight, 1) as city_hour_weight,\n", "            coalesce(csw.weight, cfc.min_store_weight, 1) as city_store_weight,\n", "            coalesce(cw.weights, cwf.min_weight, 0) as pan_city_weight,\n", "            coalesce(bpi.weight, bpif.min_weight, 0) as pan_be_weight\n", "          from\n", "            fmta\n", "            left join item_prop ip on fmta.p_type = ip.product_type\n", "            left join biw on biw.backend_facility_id = fmta.be_facility_id\n", "            and biw.item_id = fmta.item_id\n", "            left join bhw on bhw.backend_facility_id = fmta.be_facility_id\n", "            and bhw.order_hour = fmta.hour_\n", "            left join bsw on bsw.backend_facility_id = fmta.be_facility_id\n", "            and bsw.facility_id = fmta.fe_facility_id\n", "            left join bhw_fall bf on bf.backend_facility_id = fmta.be_facility_id\n", "            left join festive_weights_biw fwb on fwb.be_facility_id = fmta.be_facility_id\n", "            and fwb.item_id = fmta.item_id\n", "            left join bsw_fall_check bfc on bfc.backend_facility_id = fmta.be_facility_id\n", "            left join festive_in_out fio on fio.product_type = fmta.p_type\n", "            left join festive_weigths_ciw fwc on fwc.city_name = fmta.fe_city_name\n", "            and fwc.item_id = fmta.item_id\n", "            left join ciw on ciw.city = fmta.fe_city_name\n", "            and ciw.item_id = fmta.item_id\n", "            left join chw on chw.city = fmta.fe_city_name\n", "            and chw.order_hour = fmta.hour_\n", "            left join csw on csw.city = fmta.fe_city_name\n", "            and csw.facility_id = fmta.fe_facility_id\n", "            left join chw_fall cf on cf.city = fmta.fe_city_name\n", "            left join csw_fall cfc on cfc.city = fmta.fe_city_name\n", "            left join city_weight cw on cw.city = fmta.fe_city_name\n", "            left join be_pan_india bpi on fmta.be_facility_id = bpi.be_facility_id\n", "            cross join be_pan_india_fall bpif\n", "            cross join city_weight_fall cwf\n", "        ),\n", "    avl_raw as (\n", "        select \n", "            be_facility_name,\n", "            insert_ds_ist, \n", "            p_type,\n", "            fe_facility_id, \n", "            store_weight, \n", "            hour_weight,\n", "            hour_,\n", "    \n", "            -- fe_avail_flag-based metrics\n", "            case when sum(item_weight) = 0 then 0 else\n", "                sum(fe_avail_flag * item_weight) / sum(item_weight) \n", "            end as fe_avl,\n", "    \n", "            case when sum((1 - critical_flag) * item_weight) = 0 then 0 else\n", "                sum(fe_avail_flag * (1 - critical_flag) * item_weight) / sum((1 - critical_flag) * item_weight)\n", "            end as non_festive_avl,\n", "            \n", "    \n", "            case when sum((case when critical_flag = 0 then 0 \n", "            else (critical_flag - greatest(festive_core_flag, fes_in_out_flag)) end) * item_weight) = 0 then 0 else\n", "                sum(fe_avail_flag * (case when critical_flag = 0 then 0 \n", "            else (critical_flag - greatest(festive_core_flag, fes_in_out_flag)) end) * item_weight) / \n", "                nullif(sum((case when critical_flag = 0 then 0 \n", "            else (critical_flag - greatest(festive_core_flag, fes_in_out_flag)) end) * item_weight), 0)\n", "            end as critical_avl,\n", "            \n", "            \n", "    \n", "            case when sum((case when festive_core_flag = 0 then 0 \n", "            else (festive_core_flag - fes_in_out_flag) end) * festive_item_weight) = 0 then 0 else\n", "                sum(fe_avail_flag * (case when festive_core_flag = 0 then 0 \n", "            else (festive_core_flag - fes_in_out_flag) end) * festive_item_weight) / \n", "                nullif(sum((case when festive_core_flag = 0 then 0 \n", "            else (festive_core_flag - fes_in_out_flag) end) * festive_item_weight), 0)\n", "            end as festive_core_avl,\n", "            \n", "    \n", "            case when sum(fes_in_out_flag * festive_item_weight) = 0 then 0 else\n", "                sum(fes_in_out_flag * fe_avail_flag * festive_item_weight) / \n", "                nullif(sum(fes_in_out_flag * festive_item_weight), 0)\n", "            end as festive_in_out_avl,\n", "    \n", "            -- be_fe_avail_flag-based metrics\n", "            case when sum(item_weight) = 0 then 0 else\n", "                sum(be_fe_avail_flag * item_weight) / sum(item_weight) \n", "            end as be_fe_avl,\n", "    \n", "            case when sum((1 - critical_flag) * item_weight) = 0 then 0 else\n", "                sum(be_fe_avail_flag * (1 - critical_flag) * item_weight) / sum((1 - critical_flag) * item_weight)\n", "            end as be_non_festive_avl,\n", "    \n", "            case when sum((case when critical_flag = 0 then 0 \n", "            else (critical_flag - greatest(festive_core_flag, fes_in_out_flag)) end) * item_weight) = 0 then 0 else\n", "                sum(be_fe_avail_flag * (case when critical_flag = 0 then 0 \n", "            else (critical_flag - greatest(festive_core_flag, fes_in_out_flag)) end) * item_weight) / \n", "                nullif(sum((case when critical_flag = 0 then 0 \n", "            else (critical_flag - greatest(festive_core_flag, fes_in_out_flag)) end) * item_weight), 0)\n", "            end as be_critical_avl,\n", "    \n", "            case when sum((case when festive_core_flag = 0 then 0 \n", "            else (festive_core_flag - fes_in_out_flag) end) * festive_item_weight) = 0 then 0 else\n", "                sum(be_fe_avail_flag * (case when festive_core_flag = 0 then 0 \n", "            else (festive_core_flag - fes_in_out_flag) end) * festive_item_weight) / \n", "                nullif(sum((case when festive_core_flag = 0 then 0 \n", "            else (festive_core_flag - fes_in_out_flag) end) * festive_item_weight), 0)\n", "            end as be_festive_core_avl,\n", "    \n", "            case when sum(fes_in_out_flag * festive_item_weight) = 0 then 0 else\n", "                sum(fes_in_out_flag * be_fe_avail_flag * festive_item_weight) / \n", "                nullif(sum(fes_in_out_flag * festive_item_weight), 0)\n", "            end as be_festive_in_out_avl\n", "    \n", "        from final_raw \n", "        group by 1,2,3,4,5,6,7\n", "    ),\n", "\n", "    avl_raw_overall as (\n", "        select \n", "            be_facility_name,\n", "            insert_ds_ist, \n", "            'overall' as p_type,\n", "            fe_facility_id, \n", "            store_weight, \n", "            hour_weight,\n", "            hour_,\n", "    \n", "            -- fe_avail_flag-based metrics\n", "            case when sum(item_weight) = 0 then 0 else\n", "                sum(fe_avail_flag * item_weight) / sum(item_weight) \n", "            end as fe_avl,\n", "    \n", "            case when sum((1 - critical_flag) * item_weight) = 0 then 0 else\n", "                sum(fe_avail_flag * (1 - critical_flag) * item_weight) / sum((1 - critical_flag) * item_weight)\n", "            end as non_festive_avl,\n", "    \n", "            case when sum((case when critical_flag = 0 then 0 \n", "            else (critical_flag - greatest(festive_core_flag, fes_in_out_flag)) end) * item_weight) = 0 then 0 else\n", "                sum(fe_avail_flag * (case when critical_flag = 0 then 0 \n", "            else (critical_flag - greatest(festive_core_flag, fes_in_out_flag)) end) * item_weight) / \n", "                nullif(sum((case when critical_flag = 0 then 0 \n", "            else (critical_flag - greatest(festive_core_flag, fes_in_out_flag)) end) * item_weight), 0)\n", "            end as critical_avl,\n", "            \n", "            \n", "    \n", "            case when sum((case when festive_core_flag = 0 then 0 \n", "            else (festive_core_flag - fes_in_out_flag) end) * festive_item_weight) = 0 then 0 else\n", "                sum(fe_avail_flag * (case when festive_core_flag = 0 then 0 \n", "            else (festive_core_flag - fes_in_out_flag) end) * festive_item_weight) / \n", "                nullif(sum((case when festive_core_flag = 0 then 0 \n", "            else (festive_core_flag - fes_in_out_flag) end) * festive_item_weight), 0)\n", "            end as festive_core_avl,\n", "    \n", "            case when sum(fes_in_out_flag * festive_item_weight) = 0 then 0 else\n", "                sum(fes_in_out_flag * fe_avail_flag * festive_item_weight) / \n", "                nullif(sum(fes_in_out_flag * festive_item_weight), 0)\n", "            end as festive_in_out_avl,\n", "    \n", "            -- be_fe_avail_flag-based metrics\n", "            case when sum(item_weight) = 0 then 0 else\n", "                sum(be_fe_avail_flag * item_weight) / sum(item_weight) \n", "            end as be_fe_avl,\n", "    \n", "            case when sum((1 - critical_flag) * item_weight) = 0 then 0 else\n", "                sum(be_fe_avail_flag * (1 - critical_flag) * item_weight) / sum((1 - critical_flag) * item_weight)\n", "            end as be_non_festive_avl,\n", "    \n", "            case when sum((case when critical_flag = 0 then 0 \n", "            else (critical_flag - greatest(festive_core_flag, fes_in_out_flag)) end) * item_weight) = 0 then 0 else\n", "                sum(be_fe_avail_flag * (case when critical_flag = 0 then 0 \n", "            else (critical_flag - greatest(festive_core_flag, fes_in_out_flag)) end) * item_weight) / \n", "                nullif(sum((case when critical_flag = 0 then 0 \n", "            else (critical_flag - greatest(festive_core_flag, fes_in_out_flag)) end) * item_weight), 0)\n", "            end as be_critical_avl,\n", "    \n", "            case when sum((case when festive_core_flag = 0 then 0 \n", "            else (festive_core_flag - fes_in_out_flag) end) * festive_item_weight) = 0 then 0 else\n", "                sum(be_fe_avail_flag * (case when festive_core_flag = 0 then 0 \n", "            else (festive_core_flag - fes_in_out_flag) end) * festive_item_weight) / \n", "                nullif(sum((case when festive_core_flag = 0 then 0 \n", "            else (festive_core_flag - fes_in_out_flag) end) * festive_item_weight), 0)\n", "            end as be_festive_core_avl,\n", "    \n", "    \n", "            case when sum(fes_in_out_flag * festive_item_weight) = 0 then 0 else\n", "                sum(fes_in_out_flag * be_fe_avail_flag * festive_item_weight) / \n", "                nullif(sum(fes_in_out_flag * festive_item_weight), 0)\n", "            end as be_festive_in_out_avl\n", "    \n", "        from final_raw \n", "        group by 1,2,3,4,5,6,7\n", "    ),\n", "\n", "    avl_raw_new as (\n", "        select * from avl_raw\n", "        union all \n", "        select * from avl_raw_overall\n", "    ),\n", "    avl_city as (\n", "        select \n", "            be_facility_name,\n", "            insert_ds_ist,\n", "            p_type,\n", "            hour_weight,\n", "            hour_,\n", "\n", "            -- fe_avail_flag-based city metrics\n", "            sum(fe_avl * store_weight) / sum(store_weight) as avl,\n", "            sum(non_festive_avl * store_weight) / sum(store_weight) as non_festive_avl,\n", "            sum(critical_avl * store_weight) / sum(store_weight) as critical_avl,\n", "            sum(festive_core_avl * store_weight) / sum(store_weight) as festive_core_avl,\n", "            sum(festive_in_out_avl * store_weight) / sum(store_weight) as festive_in_out_avl,\n", "\n", "            -- be_fe_avail_flag-based city metrics\n", "            sum(be_fe_avl * store_weight) / sum(store_weight) as be_avl,\n", "            sum(be_non_festive_avl * store_weight) / sum(store_weight) as be_non_festive_avl,\n", "            sum(be_critical_avl * store_weight) / sum(store_weight) as be_critical_avl,\n", "            sum(be_festive_core_avl * store_weight) / sum(store_weight) as be_festive_core_avl,\n", "            sum(be_festive_in_out_avl * store_weight) / sum(store_weight) as be_festive_in_out_avl\n", "\n", "        from avl_raw_new \n", "        group by 1,2,3,4,5\n", "    )\n", "    select \n", "        '{festival_name}' as festive_name,\n", "        cast((current_timestamp + INTERVAL '330' MINUTE) as timestamp) AS updated_at_ist,\n", "        insert_ds_ist,\n", "        hour_,\n", "        be_facility_name,\n", "        p_type,\n", "        avl,\n", "        non_festive_avl,\n", "        critical_avl,\n", "        festive_core_avl,\n", "        festive_in_out_avl,\n", "        be_avl as be_fe_avl,\n", "        be_non_festive_avl as be_fe_non_festive_avl,\n", "        be_critical_avl as be_fe_critical_avl,\n", "        be_festive_core_avl as be_fe_festive_core_avl,\n", "        be_festive_in_out_avl as be_fe_festive_in_out_avl\n", "    from avl_city\n", "\"\"\"\n", "    column_dtypes = [\n", "        {\"name\": \"festive_name\", \"type\": \"varchar\", \"description\": \"name_of_festival\"},\n", "        {\n", "            \"name\": \"updated_at_ist\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"Updated timestamp in IST\",\n", "        },\n", "        {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"Data snapshot date (yyyy-mm-dd)\"},\n", "        {\"name\": \"hour_\", \"type\": \"integer\", \"description\": \"Hour of the day (0–23)\"},\n", "        {\"name\": \"be_facility_name\", \"type\": \"varchar\", \"description\": \"Backend facility name\"},\n", "        {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"Product type (e.g., bau, sale)\"},\n", "        {\"name\": \"avl\", \"type\": \"double\", \"description\": \"FE availability\"},\n", "        {\"name\": \"non_festive_avl\", \"type\": \"double\", \"description\": \"FE non-festive availability\"},\n", "        {\"name\": \"critical_avl\", \"type\": \"double\", \"description\": \"FE critical availability\"},\n", "        {\n", "            \"name\": \"festive_core_avl\",\n", "            \"type\": \"double\",\n", "            \"description\": \"FE festive core availability\",\n", "        },\n", "        {\n", "            \"name\": \"festive_in_out_avl\",\n", "            \"type\": \"double\",\n", "            \"description\": \"FE festive in-out availability\",\n", "        },\n", "        {\"name\": \"be_fe_avl\", \"type\": \"double\", \"description\": \"BE-FE availability\"},\n", "        {\n", "            \"name\": \"be_fe_non_festive_avl\",\n", "            \"type\": \"double\",\n", "            \"description\": \"BE-FE non-festive availability\",\n", "        },\n", "        {\n", "            \"name\": \"be_fe_critical_avl\",\n", "            \"type\": \"double\",\n", "            \"description\": \"BE-FE critical availability\",\n", "        },\n", "        {\n", "            \"name\": \"be_fe_festive_core_avl\",\n", "            \"type\": \"double\",\n", "            \"description\": \"BE-FE festive core availability\",\n", "        },\n", "        {\n", "            \"name\": \"be_fe_festive_in_out_avl\",\n", "            \"type\": \"double\",\n", "            \"description\": \"BE-FE festive in-out availability\",\n", "        },\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"supply_etls\",\n", "        \"table_name\": \"festive_backend_avl_live\",  # update this as needed\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"updated_at_ist\", \"festive_name\", \"be_facility_name\", \"p_type\"],\n", "        \"partition_key\": [\"festive_name\"],\n", "        \"load_type\": \"append\",  # options: append, truncate, upsert\n", "        \"table_description\": \"BE-FE festive and non-festive availability metrics per facility and hour\",\n", "    }\n", "    try:\n", "        # to_trino(sql_city, **kwargs)\n", "        pb.to_trino(sql_city, **kwargs)\n", "        # print(sql_city)\n", "        # df = read_sql_query(sql_city, CON_TRINO)\n", "        print(f\"pushed data to be_live_avl_table for {festival_name}\")\n", "    except Exception as e:\n", "        print(\"An error occurred:\", str(e))\n", "        print(f\"failed for festival {festival_name}\")\n", "        break"]}, {"cell_type": "code", "execution_count": null, "id": "b564002f-abf9-44c6-8430-49f75b118bcb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}