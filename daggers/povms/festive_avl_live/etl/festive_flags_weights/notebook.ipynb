{"cells": [{"cell_type": "code", "execution_count": null, "id": "0911cfd8-e724-4983-b714-d2cbb4dc5292", "metadata": {}, "outputs": [], "source": ["!pip install pytz"]}, {"cell_type": "code", "execution_count": null, "id": "54a03f3d-136b-4393-8f43-226107c5b809", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import os\n", "from datetime import date, datetime, timedelta, time\n", "from tqdm import tqdm\n", "import time\n", "\n", "# import datetime\n", "import pytz\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "4b76a6db-3eed-43cc-9ad4-790b16fe000c", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "def to_trino(df_to_trino, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)\n", "\n", "\n", "def gsheet_to_df(sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            df = pb.from_sheets(sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Read Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Read {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and created a dataframe of size {size}\"\n", "            )\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "cc65e578-a695-40c8-a910-c29f31633b92", "metadata": {}, "outputs": [], "source": ["manual = False"]}, {"cell_type": "code", "execution_count": null, "id": "28e1c78a-80f6-474e-ace3-91bebb03dcbd", "metadata": {}, "outputs": [], "source": ["if manual:\n", "    sheet = pd.read_csv(\"live.csv\")\n", "else:\n", "    sheet = gsheet_to_df(\"1zeYlST37g5ig8u5DwCvSVoGZtI6VgfJMWgIGCW-w3Cs\", \"live_festival_view\")"]}, {"cell_type": "code", "execution_count": null, "id": "08419969-beb9-468d-8dc6-f1b32cc349a4", "metadata": {}, "outputs": [], "source": ["ist = pytz.timezone(\"Asia/Kolkata\")\n", "current_ist = datetime.now(ist)"]}, {"cell_type": "code", "execution_count": null, "id": "01aa4427-3fd4-44c7-9e60-29aa14afb8b2", "metadata": {}, "outputs": [], "source": ["sheet"]}, {"cell_type": "code", "execution_count": null, "id": "1ebba916-9243-4bcd-b6de-05ac32ce769b", "metadata": {}, "outputs": [], "source": ["ist = pytz.timezone(\"Asia/Kolkata\")\n", "current_date_ist_str = datetime.now(ist).astimezone(ist).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "markdown", "id": "400d826d-6a00-4631-81f8-9745525797dd", "metadata": {}, "source": ["### Weights Calculation city"]}, {"cell_type": "code", "execution_count": null, "id": "7f7a1942-295b-4c9a-8d4c-005b34509414", "metadata": {}, "outputs": [], "source": ["for index, row in sheet.iterrows():\n", "    if row[\"weights\"] != \"Yes\":\n", "        continue\n", "    festival = row[\"festival\"]  # Festive_name+start_date format\n", "    festival_end = row[\"festival_end\"]  # Festive_name+end_date format\n", "    cpd_date = row[\"cpd_date\"]\n", "    bau_start_date = row[\"bau_start_date\"]\n", "    bau_end_date = row[\"bau_end_date\"]\n", "    sale_start_date = row[\"sale_start_date\"]\n", "    sale_end_date = row[\"sale_end_date\"]\n", "    last_year = row[\"Last_Year\"]\n", "    festival_start_date = row[\"festival_start_date\"]\n", "    festival_end_date = row[\"festival_end_date\"]\n", "\n", "    print(f\"Processing DAG for {festival}\")\n", "    print(f\"festive_name_end_date is {festival_end}\")\n", "    print(f\"CPD Date: {cpd_date}, BAU Period: {bau_start_date} to {bau_end_date}\")\n", "    print(f\"Sale Period: {sale_start_date} to {sale_end_date}, Last Year: {last_year}\")\n", "\n", "    # Create a table for festive_weights\n", "    sql_weight = f\"\"\"\n", "    with  event_name as \n", "    (\n", "      SELECT sei.id AS id,\n", "          CONCAT(name, '_', cast(start_date as varchar)) AS event_name\n", "      FROM rpc.supply_event_info sei\n", "      JOIN rpc.supply_event se ON se.id = sei.supply_event_id\n", "      WHERE sei.active = TRUE\n", "        AND se.active = TRUE\n", "        and CONCAT(name, '_', cast(start_date as varchar)) =  '{festival}'\n", "     ),\n", "   \n", "    plan_qty_raw as \n", "    (\n", "    select\n", "      eoid.event_id,\n", "      en.event_name,\n", "      od.city_name,\n", "      outlet_id,\n", "      eoid.item_id,\n", "      id.p_type,\n", "      sum(quantity) as plan_qty\n", "    from\n", "      ars.event_outlet_item_distribution eoid\n", "      inner join supply_etls.item_details id on eoid.item_id = id.item_id\n", "      and id.assortment_type = 'Packaged Goods'\n", "      inner join (\n", "        select\n", "          hot_outlet_id,\n", "          city_name\n", "        from\n", "          supply_etls.outlet_details\n", "        where\n", "          ars_active = 1\n", "          and business_type_id = 7\n", "      ) od on od.hot_outlet_id = eoid.outlet_id\n", "      inner join\n", "      event_name en on en.id = eoid.event_id\n", "      where partition_field is not null\n", "\n", "    --   where event_id = (select id from event_name) and partition_field is not null -- event_id = 14 is for <PERSON><PERSON>\n", "    group by\n", "      1,2,3,4,5,6\n", "    ),\n", "\n", "    festive_weight_city as \n", "    (\n", "    select\n", "      event_id,\n", "      event_name,\n", "      city_name,\n", "      item_id,\n", "      sum(plan_qty) as tot_qty\n", "    from\n", "      plan_qty_raw\n", "    group by\n", "      1,2,3,4\n", "    ),\n", "    festive_weight_city_sum as \n", "    (\n", "    select\n", "      event_id,\n", "      event_name,\n", "      city_name,\n", "      sum(tot_qty) as final_qty\n", "    from\n", "      festive_weight_city\n", "    group by\n", "      1,2,3\n", "    ),\n", "    festive_ciw as \n", "    (\n", "    select\n", "      fwc.event_id,\n", "      fwc.event_name,\n", "      fwc.city_name,\n", "      item_id,\n", "      fwc.tot_qty,\n", "      fwcs.final_qty,\n", "      cast(fwc.tot_qty as double) / fwcs.final_qty as weight\n", "    from\n", "      festive_weight_city fwc\n", "      inner join festive_weight_city_sum fwcs on fwc.city_name = fwcs.city_name and fwc.event_id = fwcs.event_id\n", "    )\n", "    select \n", "    '{festival}' as event_name,\n", "    fc.city_name,\n", "    fc.item_id,\n", "    round(coalesce(fc.weight,0),10) as weight,\n", "    cast(current_timestamp AT TIME ZONE 'Asia/Kolkata' as timestamp) as updated_at_ist\n", "    from\n", "    festive_ciw fc \n", "    \"\"\"\n", "    column_dtypes = [\n", "        {\"name\": \"event_name\", \"type\": \"varchar\", \"description\": \"Name of the event\"},\n", "        {\n", "            \"name\": \"city_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Name of the city -- tax city location\",\n", "        },\n", "        {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"Unique identifier for the item\"},\n", "        {\"name\": \"weight\", \"type\": \"real\", \"description\": \"Weight of the item\"},\n", "        {\n", "            \"name\": \"updated_at_ist\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"Timestamp of the last update in IST\",\n", "        },\n", "    ]\n", "    kwargs = {\n", "        \"schema_name\": \"supply_etls\",\n", "        \"table_name\": \"festive_city_item_weight\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"event_name\", \"city_name\", \"item_id\"],\n", "        \"partition_key\": [\"event_name\"],\n", "        \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "        \"table_description\": \"It has Festive City Item Weights in it\",\n", "    }\n", "\n", "    try:\n", "        to_trino(sql_weight, **kwargs)\n", "        # print(sql_weight)\n", "        print(f\"pushed data to city_festival_weights for festival {festival}\")\n", "    except Exception as e:\n", "        print(\"An error occurred:\", str(e))\n", "        print(f\"failed for festival {festival}\")\n", "        break\n", "\n", "    sql_weight_2 = f\"\"\"\n", "    with  event_name as \n", "    (\n", "      SELECT sei.id AS id,\n", "          CONCAT(name, '_', cast(start_date as varchar)) AS event_name\n", "      FROM rpc.supply_event_info sei\n", "      JOIN rpc.supply_event se ON se.id = sei.supply_event_id\n", "      WHERE sei.active = TRUE\n", "        AND se.active = TRUE\n", "        and CONCAT(name, '_', cast(start_date as varchar)) =  '{festival}'\n", "     ),\n", "   \n", "    plan_qty_raw as \n", "    (\n", "    select\n", "      eoid.event_id,\n", "      en.event_name,\n", "      od.city_name,\n", "      outlet_id,\n", "      eoid.item_id,\n", "      id.p_type,\n", "      sum(quantity) as plan_qty\n", "    from\n", "      ars.event_outlet_item_distribution eoid\n", "      inner join supply_etls.item_details id on eoid.item_id = id.item_id\n", "      and id.assortment_type = 'Packaged Goods'\n", "      inner join (\n", "        select\n", "          hot_outlet_id,\n", "          city_name\n", "        from\n", "          supply_etls.outlet_details\n", "        where\n", "          ars_active = 1\n", "          and business_type_id = 7\n", "      ) od on od.hot_outlet_id = eoid.outlet_id\n", "      inner join\n", "      event_name en on en.id = eoid.event_id\n", "      where partition_field is not null\n", "\n", "    --   where event_id = (select id from event_name) and partition_field is not null -- event_id = 14 is for <PERSON><PERSON>\n", "    group by\n", "      1,2,3,4,5,6\n", "    ),\n", "\n", "    festive_weight_city as \n", "    (\n", "    select\n", "      event_id,\n", "      event_name,\n", "      city_name,\n", "      p_type,\n", "      item_id,\n", "      sum(plan_qty) as tot_qty\n", "    from\n", "      plan_qty_raw\n", "    group by\n", "      1,2,3,4,5\n", "    ),\n", "    forecast as\n", "    (\n", "        select festival_name, city_name as city, date_, product_type as p_type, \n", "        cast(coalesce(final_forecast_aps, 0) as double)/\n", "        (cast(sum(coalesce(final_forecast_aps, 0)) over(partition by product_type,city_name, festival_name) as double)) as forecast_ratio\n", "        from supply_etls.festival_forecast_final_date_city_ptype\n", "        where festival_name  in ('{festival}', '{festival_end}')\n", "    ),\n", "    forecast_fallback_date as \n", "    (\n", "        select festival_name, city, date_\n", "        from forecast \n", "        group by 1,2,3\n", "    ),\n", "    festival_fallback_count as \n", "    (\n", "        select festival_name, city, count(distinct date_) as tot\n", "        from forecast_fallback_date\n", "        group by 1,2\n", "    ),\n", "    \n", "    festival_fallback as \n", "    (\n", "        select ffd.festival_name, ffd.city, ffd.date_, ffc.tot \n", "        from forecast_fallback_date ffd inner join\n", "        festival_fallback_count ffc on \n", "        ffd.city = ffc.city \n", "    ),\n", "    \n", "    raw_fin as \n", "    (\n", "        select fwc.event_name, fwc.p_type, fwc.item_id, f.date_, fwc.tot_qty, f.forecast_ratio, f.p_type as product_type,\n", "        (fwc.tot_qty*f.forecast_ratio) as fin_qty, fwc.city_name\n", "        from \n", "        festive_weight_city fwc left join\n", "        forecast f on fwc.city_name = f.city and fwc.p_type = f.p_type\n", "    ),\n", "    \n", "    raw_union as \n", "    (   \n", "        select a.event_name, a.p_type, a.city_name, a.item_id, ff.date_, a.tot_qty/ff.tot as fin_qty\n", "        from\n", "        (select event_name, p_type, item_id, tot_qty, city_name\n", "        from raw_fin\n", "        where product_type is null) a\n", "        left join\n", "        festival_fallback ff on a.city_name = ff.city \n", "    ),\n", "    \n", "    final_raw as \n", "    (\n", "        select date_, city_name as city,\n", "        p_type, item_id, cast(tot_qty as double) as final_weight,\n", "        Null as weight_p_type, Null as multiplier, Null as dist_type\n", "            from raw_fin\n", "            where product_type is not null\n", "        union all\n", "        select date_, city_name as city, \n", "        p_type, item_id, cast(fin_qty as double) as final_weight,\n", "        Null as weight_p_type, Null as multiplier, Null as dist_type\n", "        from raw_union\n", "    )\n", "    select date_, '{festival}' as festival_name, city, p_type, item_id, \n", "    final_weight/sum(final_weight) over(partition by date_, city) as final_weight,\n", "    -1 as weight_p_type, -1 as multiplier, 'NA' as dist_type\n", "    from \n", "    final_raw\n", "    \"\"\"\n", "    column_dtypes_2 = [\n", "        {\"name\": \"date_\", \"type\": \"date\", \"description\": \"The date for the weight record\"},\n", "        {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"Name of the festival\"},\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"City related to the weight data\"},\n", "        {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"Promotion type or category\"},\n", "        {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"Unique identifier for the item\"},\n", "        {\n", "            \"name\": \"final_weight\",\n", "            \"type\": \"real\",\n", "            \"description\": \"Final weight assigned to the item\",\n", "        },\n", "        {\n", "            \"name\": \"weight_p_type\",\n", "            \"type\": \"real\",\n", "            \"description\": \"Weighting method based on p_type\",\n", "        },\n", "        {\"name\": \"multiplier\", \"type\": \"real\", \"description\": \"Multiplier applied to base weight\"},\n", "        {\"name\": \"dist_type\", \"type\": \"varchar\", \"description\": \"Type of distribution applied\"},\n", "    ]\n", "\n", "    kwargs_2 = {\n", "        \"schema_name\": \"supply_etls\",\n", "        \"table_name\": \"festive_city_date_item_weight\",\n", "        \"column_dtypes\": column_dtypes_2,\n", "        \"primary_key\": [\"date_\", \"festival_name\", \"city\", \"item_id\"],\n", "        \"partition_key\": [\"festival_name\"],\n", "        \"load_type\": \"truncate\",  # append, truncate or upsert\n", "        \"table_description\": \"Adjusted weights for festive city items at date level\",\n", "    }\n", "    try:\n", "        to_trino(sql_weight_2, **kwargs_2)\n", "        print(f\"pushed data to city_festival_weights_date for festival {festival}\")\n", "    except Exception as e:\n", "        print(\"An error occurred:\", str(e))\n", "        print(f\"failed for festival {festival}\")\n", "        break\n", "    sql_weight_3 = f\"\"\"\n", "            with forecast as\n", "        (\n", "            select festival_name, city_name as city, date_, sum(cast(coalesce(final_forecast_aps, 0) as double)) as forecast\n", "            from supply_etls.festival_forecast_final_date_city_ptype\n", "            where festival_name in ('{festival}', '{festival_end}')\n", "            group by 1,2,3\n", "        ),\n", "\n", "        forecast_agg as \n", "        (\n", "            select festival_name, city, cast(sum(forecast) as double) as forecast_tot\n", "            from forecast\n", "            group by 1,2\n", "        ),\n", "\n", "        forecast_final as\n", "        (\n", "            select '{festival}' as festival_name, f.date_, f.city,  \n", "            case when fa.forecast_tot = 0 then 0 else\n", "            f.forecast/fa.forecast_tot end as weight\n", "            from forecast f inner join\n", "            forecast_agg fa on f.festival_name = fa.festival_name and f.city = fa.city \n", "        )\n", "        select * from\n", "        forecast_final\n", "    \"\"\"\n", "    column_dtypes_3 = [\n", "        {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"Name of the festival\"},\n", "        {\"name\": \"date_\", \"type\": \"date\", \"description\": \"Date for the weight entry\"},\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"City related to the weight data\"},\n", "        {\n", "            \"name\": \"weight\",\n", "            \"type\": \"real\",\n", "            \"description\": \"Weight assigned to the city for the festival\",\n", "        },\n", "    ]\n", "\n", "    kwargs_3 = {\n", "        \"schema_name\": \"supply_etls\",\n", "        \"table_name\": \"festive_city_day_weight\",\n", "        \"column_dtypes\": column_dtypes_3,\n", "        \"primary_key\": [\"festival_name\", \"date_\", \"city\"],\n", "        \"partition_key\": [\"festival_name\"],\n", "        \"load_type\": \"partition_overwrite\",  # append, truncate or upsert\n", "        \"table_description\": \"Table of festive weights by city and date\",\n", "    }\n", "    try:\n", "        to_trino(sql_weight_3, **kwargs_3)\n", "        print(f\"pushed data to city_festival_date_weight for festival {festival}\")\n", "    except Exception as e:\n", "        print(\"An error occurred:\", str(e))\n", "        print(f\"failed for festival {festival}\")\n", "        break\n", "\n", "    # df_test = read_sql_query(sql_weight_2, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "b56b97df-7564-4b3c-aade-93537f55915b", "metadata": {}, "outputs": [], "source": ["ist = pytz.timezone(\"Asia/Kolkata\")\n", "current_date_ist_str = datetime.now(ist).astimezone(ist).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "markdown", "id": "7b5961f9-3753-4772-afe7-3a15ec921f9f", "metadata": {}, "source": ["### Weights Calculation backend"]}, {"cell_type": "code", "execution_count": null, "id": "bf6da8cc-e9b3-41af-8f83-11f7a1446e11", "metadata": {}, "outputs": [], "source": ["for index, row in sheet.iterrows():\n", "    if row[\"be_weights\"] != \"Yes\":\n", "        continue\n", "    festival = row[\"festival\"]  # Festive_name+start_date format\n", "    festival_name = row[\"festival\"]\n", "    festival_end = row[\"festival_end\"]  # Festive_name+end_date format\n", "    cpd_date = row[\"cpd_date\"]\n", "    bau_start_date = row[\"bau_start_date\"]\n", "    bau_end_date = row[\"bau_end_date\"]\n", "    sale_start_date = row[\"sale_start_date\"]\n", "    sale_end_date = row[\"sale_end_date\"]\n", "    last_year = row[\"Last_Year\"]\n", "    festival_start_date = row[\"festival_start_date\"]\n", "    festival_end_date = row[\"festival_end_date\"]\n", "\n", "    sale_start_date_obj = datetime.strptime(sale_start_date, \"%Y-%m-%d\").date()\n", "    current_date_obj = datetime.strptime(current_date_ist_str, \"%Y-%m-%d\").date()\n", "\n", "    # Determine tea_date as string\n", "\n", "    tea_date = current_date_ist_str if sale_start_date_obj > current_date_obj else sale_start_date\n", "    print(f\"Tea_Date used for festival is {tea_date}\")\n", "    ist_date_str = tea_date\n", "    sql = f\"\"\"\n", "with ciw as (\n", "          select\n", "            city,\n", "            item_id,\n", "            cast(weights as double) as weights\n", "          from\n", "            blinkit_iceberg.supply_etls.city_item_weights\n", "          where\n", "            updated_at = (\n", "              select\n", "                max(updated_at)\n", "              from\n", "                blinkit_iceberg.supply_etls.city_item_weights\n", "              where\n", "                updated_at <= date '{cpd_date}' --cpd_date\n", "            )\n", "        ),\n", "        city_weight as (\n", "          select\n", "            city,\n", "            cast(weight as double) as weights\n", "          from\n", "            blinkit_iceberg.supply_etls.city_weights\n", "          where\n", "            updated_at = (\n", "              select\n", "                max(updated_at)\n", "              from\n", "                blinkit_iceberg.supply_etls.city_weights\n", "              where\n", "                updated_at <= date '{cpd_date}' --cpd_date\n", "            )\n", "        ),\n", "        city_weight_fall as (\n", "          select\n", "            min(weights) as min_weight\n", "          from\n", "            city_weight\n", "        ),\n", "        bsw as (\n", "          select\n", "            cast(cast(backend_facility_id as double) as integer) as backend_facility_id,\n", "            facility_id,\n", "            cast(store_weight as double) as store_weight\n", "          from\n", "            blinkit_iceberg.supply_etls.backend_store_weights\n", "          where\n", "            updated_at = (\n", "              select\n", "                max(updated_at)\n", "              from\n", "                blinkit_iceberg.supply_etls.backend_store_weights\n", "              where\n", "                updated_at <= date '{cpd_date}' -- cpd_date\n", "            )\n", "            and cast(store_weight as double)>0\n", "        ),\n", "        bsw_fall_check as -- similar logic as why bhw_fall is there\n", "        (\n", "          select\n", "            backend_facility_id,\n", "            min(store_weight) as min_store_weight\n", "          from\n", "            bsw\n", "          group by\n", "            1\n", "        ),\n", "        raw_filtered as \n", "        (SELECT \n", "                distinct insert_ds_ist,\n", "            hour_,\n", "            fe_facility_id,\n", "            be_facility_id,\n", "            be_facility_name,\n", "            item_id,\n", "            fe_city_name,\n", "            fe_inv_outlet_id,\n", "            p_type,\n", "            item_substate,\n", "            fe_avail_flag,\n", "            sto_cpd,\n", "            be_inv\n", "          FROM blinkit_iceberg.supply_etls.inventory_replenishment_metrics\n", "          where\n", "            insert_ds_ist = date '{ist_date_str}'\n", "            and (\n", "              active_outlet_flag = 1\n", "              or active_outlet_flag is null\n", "            )\n", "            and item_substate in (1, 3)\n", "            -- and ((fastival_tag = '{festival_name}') or (fastival_tag = 'BAU')) -- festival_name\n", "            and hour_ = (select max(hour_) from blinkit_iceberg.supply_etls.inventory_replenishment_metrics\n", "            where insert_ds_ist = date '{ist_date_str}')\n", "        ),\n", "        \n", "        \n", "        festive_weigths_ciw as (\n", "        select date_, city as city_name, item_id, p_type, final_weight as weight\n", "        from blinkit_iceberg.supply_etls.festive_city_date_item_weight\n", "        where festival_name = '{festival_name}' \n", "        ),\n", "        \n", "        festive_biw_raw as (\n", "            SELECT\n", "            fwc.date_,\n", "            fmta.be_facility_id,\n", "            fmta.fe_facility_id,\n", "            fmta.fe_city_name,\n", "            fmta.item_id,\n", "            fwc.p_type,\n", "            CASE\n", "            WHEN TRY_CAST(fwc.weight AS double) IS NULL OR TRY_CAST(bsw.store_weight AS double) IS NULL OR TRY_CAST(cw.weights AS double) IS NULL\n", "            THEN 0\n", "            ELSE\n", "              TRY_CAST(fwc.weight AS double) *\n", "              TRY_CAST(bsw.store_weight AS double) *\n", "              TRY_CAST(cw.weights AS double)\n", "          END AS final_weight\n", "          FROM raw_filtered fmta\n", "          INNER JOIN festive_weigths_ciw fwc\n", "            ON fwc.city_name = fmta.fe_city_name\n", "            AND fmta.item_id = fwc.item_id\n", "          LEFT JOIN city_weight cw\n", "            ON cw.city = fmta.fe_city_name\n", "          LEFT JOIN bsw\n", "            ON bsw.backend_facility_id = fmta.be_facility_id\n", "            AND bsw.facility_id = fmta.fe_facility_id\n", "                ),\n", "        festive_biw_raw_sum as (\n", "            SELECT date_, be_facility_id, SUM(case when TRY_CAST(final_weight AS DOUBLE) IS NULL then 0 else final_weight end) as weight_tot\n", "            from festive_biw_raw\n", "            group by 1,2\n", "        ),\n", "        festive_weights_biw as\n", "        (   select fir.date_,fir.be_facility_id, fir.item_id, fir.p_type,\n", "        case when coalesce(firs.weight_tot,0) = 0 then 0 else\n", "        fir.final_weight/firs.weight_tot end as weight\n", "            from \n", "            festive_biw_raw fir inner join \n", "            festive_biw_raw_sum firs on fir.be_facility_id = firs.be_facility_id and fir.date_ = firs.date_\n", "        )\n", "        select *,'{festival_name}' as festival_name from festive_weights_biw\n", "        \n", "    \"\"\"\n", "    column_dtypes = [\n", "        {\"name\": \"date_\", \"type\": \"date\", \"description\": \"Date in yyyy-mm-dd format\"},\n", "        {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"Backend facility identifier\"},\n", "        {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"Item identifier\"},\n", "        {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"Product type\"},\n", "        {\"name\": \"weight\", \"type\": \"real\", \"description\": \"Weight value\"},\n", "        {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"festival_name\"},\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"supply_etls\",\n", "        \"table_name\": \"festive_backend_date_item_weight\",  # replace with your actual table name\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"date_\", \"be_facility_id\", \"item_id\"],\n", "        \"partition_key\": [\"festival_name\"],\n", "        \"load_type\": \"partition_overwrite\",  # append, truncate or upsert\n", "        \"table_description\": \"Table containing weights and related info per be_facility and item and date_\",\n", "    }\n", "\n", "    try:\n", "        to_trino(sql, **kwargs)\n", "        print(f\"pushed data to backend_festival_weights_date for festival {festival}\")\n", "    except Exception as e:\n", "        print(\"An error occurred:\", str(e))\n", "        print(f\"failed for festival {festival}\")\n", "        break"]}, {"cell_type": "markdown", "id": "92f31aae-0cf1-4eba-9ed7-c5b83a5677d3", "metadata": {}, "source": ["### Spikes Calculation"]}, {"cell_type": "code", "execution_count": null, "id": "5bc41a7a-2d54-4bb0-8c93-25d93d59e744", "metadata": {}, "outputs": [], "source": ["sheet"]}, {"cell_type": "code", "execution_count": null, "id": "4e12620f-d130-4337-94b6-ecdd71f10523", "metadata": {}, "outputs": [], "source": ["for index, row in sheet.iterrows():\n", "    if row[\"flags\"] != \"Yes\":\n", "        continue\n", "    festival = row[\"festival\"]  # Festive_name+start_date format\n", "    festival_end = row[\"festival_end\"]  # Festive_name+end_date format\n", "    cpd_date = row[\"cpd_date\"]\n", "    bau_start_date = row[\"bau_start_date\"]\n", "    bau_end_date = row[\"bau_end_date\"]\n", "    sale_start_date = row[\"sale_start_date\"]\n", "    sale_end_date = row[\"sale_end_date\"]\n", "    last_year = row[\"Last_Year\"]\n", "    festival_start_date = row[\"festival_start_date\"]\n", "    festival_end_date = row[\"festival_end_date\"]\n", "    if last_year == \"Yes\":\n", "        flag = \"last_year\"\n", "        spike = \"py_search_spike\"\n", "    else:\n", "        flag = \"current_year\"\n", "        spike = \"cy_search_spike\"\n", "    sql_flag = f\"\"\"\n", "        with raw as (\n", "    select\n", "      festival_name,\n", "      product_type,\n", "      try(\n", "        sum(cy_search_device_count_festival) / sum(cy_search_device_count_bau)\n", "      ) as cy_search_spike,\n", "      try(\n", "        sum(py_search_device_count_festival) / sum(py_search_device_count_bau)\n", "      ) as py_search_spike\n", "    from\n", "      blinkit_iceberg.supply_etls.festival_forecast_final_date_city_ptype\n", "    where\n", "      festival_name in ('{festival}', '{festival_end}')\n", "    group by\n", "      1,\n", "      2\n", "  ),\n", "  item_flag as (\n", "    select\n", "      festival_name,\n", "      product_type,\n", "      cy_search_spike,\n", "      py_search_spike,\n", "      case\n", "        when coalesce({spike},0) >= 1.5 then 1\n", "        else 0\n", "      end as critical_flag,\n", "      case\n", "        when coalesce({spike},0) >= 3 then 1\n", "        else 0\n", "      end as festive_core_flag\n", "    from\n", "      raw\n", "  )\n", "  select\n", "    '{festival}' as festival_name, product_type, cy_search_spike, py_search_spike, critical_flag, festive_core_flag, '{flag}' as type\n", "  from\n", "    item_flag\n", "    \"\"\"\n", "    column_dtypes = [\n", "        {\n", "            \"name\": \"festival_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Name and date of the festival\",\n", "        },\n", "        {\n", "            \"name\": \"product_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Type or category of the product\",\n", "        },\n", "        {\n", "            \"name\": \"cy_search_spike\",\n", "            \"type\": \"real\",\n", "            \"description\": \"Current year search spike metric (nullable)\",\n", "        },\n", "        {\n", "            \"name\": \"py_search_spike\",\n", "            \"type\": \"real\",\n", "            \"description\": \"Previous year search spike metric\",\n", "        },\n", "        {\n", "            \"name\": \"critical_flag\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Flag indicating if the product is critical during the festival\",\n", "        },\n", "        {\n", "            \"name\": \"festive_core_flag\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Flag indicating if the product is a core festive item\",\n", "        },\n", "        {\n", "            \"name\": \"type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Type of the classification (e.g. critical, core, last_year, etc.)\",\n", "        },\n", "    ]\n", "\n", "    kwargs_flag = {\n", "        \"schema_name\": \"supply_etls\",\n", "        \"table_name\": \"festive_flags\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"festival_name\", \"product_type\"],\n", "        \"partition_key\": [\"festival_name\"],\n", "        \"load_type\": \"partition_overwrite\",  # options: append, truncate, upsert\n", "        \"table_description\": \"Item-level flags and search spike metrics by festival and classification type\",\n", "    }\n", "\n", "    try:\n", "        to_trino(sql_flag, **kwargs_flag)\n", "        print(f\"pushed data to festive_flags for festival {festival}\")\n", "    except Exception as e:\n", "        print(\"An error occurred:\", str(e))\n", "        print(f\"failed for festival {festival}\")\n", "        break\n", "    sql_in_out = f\"\"\"\n", "        with product_item_mapping as (\n", "    select\n", "      product_id,\n", "      count(distinct item_id) as sku_count\n", "    from\n", "      blinkit_iceberg.dwh.dim_item_product_offer_mapping\n", "    where\n", "      is_current = true\n", "    group by\n", "      1\n", "    having\n", "      count(distinct item_id) = 1\n", "  ),\n", "  pid_to_ptype as (\n", "    select\n", "      distinct dp.product_id,\n", "      dp.product_type_id,\n", "      product_type\n", "    from\n", "      dwh.dim_product dp\n", "      inner join product_item_mapping pim on dp.product_id = pim.product_id\n", "    where\n", "      is_current = true\n", "      and product_type not in ('combo', 'Combo')\n", "  ),\n", "  carts_ptype as (\n", "    select\n", "      order_create_dt_ist as date_,\n", "      product_type,\n", "      count(distinct cart_id) as carts\n", "    from\n", "      dwh.fact_sales_order_item_details x\n", "      left join pid_to_ptype dp on x.product_id = dp.product_id\n", "      left join supply_etls.outlet_details od on x.outlet_id = od.inv_outlet_id\n", "    where\n", "      (\n", "        (\n", "          order_create_dt_ist between date '{festival_start_date}'\n", "          and date '{festival_end_date}'\n", "        ) \n", "        or (\n", "          order_create_dt_ist between date '{bau_start_date}'\n", "          and date '{bau_end_date}'\n", "        )\n", "      ) -- bau period is cpd_Date - interval '10' days\n", "      and order_current_status = 'DELIVERED'\n", "      and order_type not in (\n", "        'InternalForwardOrder',\n", "        'InternalReverseOrder',\n", "        'DropShippingInternalReverseOrder'\n", "      )\n", "    group by\n", "      1,\n", "      2\n", "  ),\n", "  festive_tagging as (\n", "    select\n", "      product_type, max(\n", "            case\n", "              when date_ between date '{festival_start_date}'\n", "              and date '{festival_end_date}'\n", "              then carts\n", "            end\n", "          ) as max_carts\n", "    from\n", "      carts_ptype\n", "    group by\n", "      1\n", "    having\n", "      (\n", "        coalesce(\n", "          avg(\n", "            case\n", "              when date_ between date '{bau_start_date}'\n", "              and date '{bau_end_date}' then carts\n", "            end\n", "          ),\n", "          0\n", "        ) / nullif(\n", "          max(\n", "            case\n", "              when date_ between date '{festival_start_date}'\n", "              and date '{festival_end_date}'\n", "              then carts\n", "            end\n", "          ),\n", "          0\n", "        )\n", "      ) <= 0.15\n", "      and max(\n", "        case\n", "          when date_ between date '{festival_start_date}'\n", "              and date '{festival_end_date}'\n", "          then carts\n", "        end\n", "      ) >= 1000\n", "      and coalesce(\n", "        avg(\n", "          case\n", "            when date_ between date '{bau_start_date}'\n", "              and date '{bau_end_date}' then carts\n", "          end\n", "        ),\n", "        0\n", "      ) < 1000\n", "  )\n", "  select\n", "    '{festival}' as festival_name, product_type, max_carts\n", "  from\n", "    festive_tagging\n", "    \"\"\"\n", "    column_dtypes = [\n", "        {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"festival_name\"},\n", "        {\n", "            \"name\": \"product_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Type or category of the product\",\n", "        },\n", "        {\n", "            \"name\": \"max_carts\",\n", "            \"type\": \"int\",\n", "            \"description\": \"Maximum number of carts containing the product\",\n", "        },\n", "    ]\n", "\n", "    kwargs_in_out = {\n", "        \"schema_name\": \"supply_etls\",\n", "        \"table_name\": \"festive_in_out_flag\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"festival_name\", \"product_type\"],\n", "        \"load_type\": \"upsert\",  # options: append, truncate, upsert\n", "        \"table_description\": \"in_out_festive_tags\",\n", "    }\n", "    try:\n", "        to_trino(sql_in_out, **kwargs_in_out)\n", "        print(f\"pushed data to festive_flags_in_out for festival {festival}\")\n", "    except Exception as e:\n", "        print(\"An error occurred:\", str(e))\n", "        print(f\"failed for festival_in_out_flags {festival}\")\n", "        break"]}, {"cell_type": "markdown", "id": "eab8f90c-32c5-45c8-91c4-9e3250f44715", "metadata": {}, "source": ["### Compute flags for current_year for retro"]}, {"cell_type": "code", "execution_count": null, "id": "889abc00-5956-4b23-b6c1-219bc5cf2228", "metadata": {}, "outputs": [], "source": ["for index, row in sheet.iterrows():\n", "    festival = row[\"festival\"]  # Festive_name+start_date format\n", "    festival_end = row[\"festival_end\"]  # Festive_name+end_date format\n", "    cpd_date = row[\"cpd_date\"]\n", "    bau_start_date = row[\"bau_start_date\"]\n", "    bau_end_date = row[\"bau_end_date\"]\n", "    sale_start_date = row[\"sale_start_date\"]\n", "    sale_end_date = row[\"sale_end_date\"]\n", "    last_year = row[\"Last_Year\"]\n", "    current_year = row[\"Current_Year\"]\n", "    festival_start_date = row[\"festival_start_date\"]\n", "    festival_end_date = row[\"festival_end_date\"]\n", "    if current_year == \"Yes\":\n", "        flag = \"current_year\"\n", "        spike = \"cy_search_spike\"\n", "        bau_end_date = cpd_date\n", "        bau_start_date = datetime.strptime(cpd_date, \"%Y-%m-%d\") - <PERSON><PERSON><PERSON>(days=10)\n", "        print(f\"bau_start and end date are {bau_start_date} and {bau_end_date} for retro\")\n", "    else:\n", "        print(f\"retro calc are not enabled for {festival}\")\n", "        break\n", "    sql_in_out = f\"\"\"\n", "        with product_item_mapping as (\n", "    select\n", "      product_id,\n", "      count(distinct item_id) as sku_count\n", "    from\n", "      blinkit_iceberg.dwh.dim_item_product_offer_mapping\n", "    where\n", "      is_current = true\n", "    group by\n", "      1\n", "    having\n", "      count(distinct item_id) = 1\n", "  ),\n", "  pid_to_ptype as (\n", "    select\n", "      distinct dp.product_id,\n", "      dp.product_type_id,\n", "      product_type\n", "    from\n", "      dwh.dim_product dp\n", "      inner join product_item_mapping pim on dp.product_id = pim.product_id\n", "    where\n", "      is_current = true\n", "      and product_type not in ('combo', 'Combo')\n", "  ),\n", "  carts_ptype as (\n", "    select\n", "      order_create_dt_ist as date_,\n", "      product_type,\n", "      count(distinct cart_id) as carts\n", "    from\n", "      dwh.fact_sales_order_item_details x\n", "      left join pid_to_ptype dp on x.product_id = dp.product_id\n", "      left join supply_etls.outlet_details od on x.outlet_id = od.inv_outlet_id\n", "    where\n", "      (\n", "        (\n", "          order_create_dt_ist between date '{sale_start_date}'\n", "          and date '{sale_end_date}'\n", "        ) \n", "        or (\n", "          order_create_dt_ist between date '{bau_start_date}'\n", "          and date '{bau_end_date}'\n", "        )\n", "      ) -- bau period is cpd_Date - interval '10' days\n", "      and order_current_status = 'DELIVERED'\n", "      and order_type not in (\n", "        'InternalForwardOrder',\n", "        'InternalReverseOrder',\n", "        'DropShippingInternalReverseOrder'\n", "      )\n", "    group by\n", "      1,\n", "      2\n", "  ),\n", "  festive_tagging as (\n", "    select\n", "      product_type, max(\n", "            case\n", "              when date_ between date '{sale_start_date}'\n", "              and date '{sale_end_date}'\n", "              then carts\n", "            end\n", "          ) as max_carts\n", "    from\n", "      carts_ptype\n", "    group by\n", "      1\n", "    having\n", "      (\n", "        coalesce(\n", "          avg(\n", "            case\n", "              when date_ between date '{bau_start_date}'\n", "              and date '{bau_end_date}' then carts\n", "            end\n", "          ),\n", "          0\n", "        ) / nullif(\n", "          max(\n", "            case\n", "              when date_ between date '{sale_start_date}'\n", "              and date '{sale_end_date}'\n", "              then carts\n", "            end\n", "          ),\n", "          0\n", "        )\n", "      ) <= 0.15\n", "      and max(\n", "        case\n", "          when date_ between date '{sale_start_date}'\n", "              and date '{sale_end_date}'\n", "          then carts\n", "        end\n", "      ) >= 1000\n", "      and coalesce(\n", "        avg(\n", "          case\n", "            when date_ between date '{bau_start_date}'\n", "              and date '{bau_end_date}' then carts\n", "          end\n", "        ),\n", "        0\n", "      ) < 1000\n", "  )\n", "  select\n", "    '{festival}' as festival_name, product_type, max_carts, 'retro' as flag\n", "  from\n", "    festive_tagging\n", "    \"\"\"\n", "    column_dtypes = [\n", "        {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"festival_name\"},\n", "        {\n", "            \"name\": \"product_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Type or category of the product\",\n", "        },\n", "        {\n", "            \"name\": \"max_carts\",\n", "            \"type\": \"int\",\n", "            \"description\": \"Maximum number of carts containing the product\",\n", "        },\n", "        {\"name\": \"flag\", \"type\": \"varchar\", \"description\": \"flag\"},\n", "    ]\n", "\n", "    kwargs_in_out = {\n", "        \"schema_name\": \"supply_etls\",\n", "        \"table_name\": \"festive_in_out_flag\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"festival_name\", \"product_type\", \"flag\"],\n", "        \"load_type\": \"upsert\",  # options: append, truncate, upsert\n", "        \"table_description\": \"in_out_festive_tags\",\n", "    }\n", "    try:\n", "        to_trino(sql_in_out, **kwargs_in_out)\n", "        print(f\"pushed data to festive_flags_in_out retro for festival {festival}\")\n", "    except Exception as e:\n", "        print(\"An error occurred:\", str(e))\n", "        print(f\"failed for festival_in_out_flags {festival} retro\")\n", "        break"]}, {"cell_type": "code", "execution_count": null, "id": "d66f347c-55db-41d5-b814-90da2a78b8f0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}