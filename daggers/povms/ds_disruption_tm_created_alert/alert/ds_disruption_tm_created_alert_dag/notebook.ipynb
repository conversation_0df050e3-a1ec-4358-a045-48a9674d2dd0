{"cells": [{"cell_type": "code", "execution_count": null, "id": "4ce50b50-268d-4bf5-8e74-7c127f11ec0d", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import time\n", "import numpy as np\n", "from datetime import date, datetime, timedelta\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "8ed92df6-0cff-4620-b01b-71a99c3ebd20", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        print(\n", "            f\"Read attempt: {attempt}... \\nRun started at {datetime.now() + timed<PERSON>ta(hours = 5.5)}\"\n", "        )\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "660d1a99-6eda-4ce8-ab97-22c95138c125", "metadata": {}, "outputs": [], "source": ["def disrupt_stores():\n", "    disrupt_stores = \"\"\"\n", "\n", "WITH disr_info AS (\n", "SELECT  insert_ds_ist, outlet_id AS frontend_outlet_id, start_date_time AS start_date, end_date_time AS end_date, action\n", "        , percent AS disruption_percentage, created_by, reason_id, (created_at + INTERVAL '330' MINUTE) AS created_at\n", "FROM    ars.disruption_v2_log\n", "WHERE   lake_active_record = true AND insert_ds_ist >= CAST(CURRENT_DATE - INTERVAL '1' DAY AS VARCHAR) AND mode = 'normal'\n", "        AND created_at >= (CURRENT_TIMESTAMP - INTERVAL '360' MINUTE) )\n", "\n", "SELECT      d.action, d.created_at AS timestamp_at, u.username AS created_by, d.frontend_outlet_id, o.name AS fe_name\n", "            , d.start_date, d.end_date, z.description AS reason_type, d.disruption_percentage\n", "            \n", "FROM        disr_info d\n", "LEFT JOIN   retail.console_outlet o\n", "            ON o.lake_active_record = true AND o.active = 1 AND o.id = d.frontend_outlet_id\n", "LEFT JOIN   ars.reasons z\n", "            ON z.lake_active_record = true AND z.id = d.reason_id\n", "LEFT JOIN   retail.auth_user u\n", "            ON u.lake_active_record = true AND u.is_active = 1 AND u.id = d.created_by\n", "\n", "\"\"\"\n", "    return read_sql_query(disrupt_stores, trino)\n", "\n", "\n", "disrupt_stores = disrupt_stores()"]}, {"cell_type": "code", "execution_count": null, "id": "8d6f852f-bf77-4a48-adca-3dbdb91b42ba", "metadata": {}, "outputs": [], "source": ["# disrupt_stores"]}, {"cell_type": "code", "execution_count": null, "id": "180cced6-0f3c-41d5-b77c-46586724e0ab", "metadata": {"tags": []}, "outputs": [], "source": ["if disrupt_stores[\"action\"].count() == 0:\n", "    print(\"No record found. Hence no slack alert required\")\n", "\n", "else:\n", "\n", "    def render_mpl_table(\n", "        data,\n", "        col_width=8,  # 3.0,\n", "        row_height=2,  # 0.625,\n", "        font_size=20,\n", "        header_color=\"#E96125\",\n", "        row_colors=[\"#f1f1f2\", \"w\"],\n", "        edge_color=\"black\",\n", "        bbox=[0, 0, 1, 1],\n", "        header_columns=0,\n", "        ax=None,\n", "        **kwargs\n", "    ):\n", "        if ax is None:\n", "            size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array(\n", "                [col_width, row_height]\n", "            )\n", "            fig, ax = plt.subplots(figsize=size)\n", "            ax.axis(\"off\")\n", "        mpl_table = ax.table(\n", "            cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs\n", "        )\n", "        mpl_table.auto_set_font_size(False)\n", "        mpl_table.set_fontsize(font_size)\n", "\n", "        for k, cell in mpl_table._cells.items():\n", "            cell.set_edgecolor(edge_color)\n", "            if k[0] == 0 or k[1] < header_columns:\n", "                cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "                cell.set_facecolor(header_color)\n", "            #         elif k[0] == data.shape[0]:\n", "            #             cell.set_text_props(weight=\"bold\", ma=\"center\")\n", "            # cell.set_facecolor('#f1f1f2')\n", "            else:\n", "                cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "        mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "        return ax.get_figure(), ax\n", "\n", "    fig, ax = render_mpl_table(disrupt_stores, header_columns=0)\n", "    fig.savefig(\"disrupt_stores.png\", bbox_inches=\"tight\")\n", "\n", "    channel = \"pan_india_store_disruption_information\"  # 'test_channel'\n", "    text = \"List of dark store has been created by our team members. Please check time duration where dark store disruption will last longer\"\n", "\n", "    pb.send_slack_message(channel=channel, text=text, files=[\"./disrupt_stores.png\"])\n", "\n", "    print(\n", "        \"slack alert has been sent to respective slack channel to inforn the same to the team for dark store disruption created\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "5b082f45-d1d2-4043-aa77-04f9f35fa7a0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ecd7947f-b30f-4036-9f41-a74e5d16f4c8", "metadata": {}, "outputs": [], "source": ["# import io\n", "# from contextlib import redirect_stdout\n", "\n", "# output=io.StringIO()\n", "# with redirect_stdout(output):\n", "#     text = \"List of dark store has been created by our folks.\\nPlease check the duration of each dark store where disruption will last longer\"\n", "#     lines = text.split('\\n')\n", "#     print('\\n'.join(lines), file=s)\n", "# result = output.getvalue()"]}, {"cell_type": "code", "execution_count": null, "id": "bce93caa-0365-4e45-b078-829f7deeb8a5", "metadata": {}, "outputs": [], "source": ["def disrupt_stores_dr():\n", "    disrupt_stores_dr = \"\"\"\n", "\n", "WITH disr_info AS (\n", "SELECT  insert_ds_ist, outlet_id AS frontend_outlet_id, start_date_time AS start_date, end_date_time AS end_date, action\n", "        , percent AS disruption_percentage, created_by, reason_id, (created_at + INTERVAL '330' MINUTE) AS created_at\n", "FROM    ars.disruption_v2_log\n", "WHERE   lake_active_record = true AND insert_ds_ist >= CAST(CURRENT_DATE - INTERVAL '1' DAY AS VARCHAR) AND mode = 'normal'\n", "        AND created_at >= (CAST((CURRENT_DATE - INTERVAL '1' DAY) AS TIMESTAMP) + INTERVAL '12' hour + INTERVAL '30' MINUTE)\n", "        AND created_at <= (CAST(CURRENT_DATE AS TIMESTAMP) + INTERVAL '12' HOUR + INTERVAL '30' MINUTE) )\n", "\n", "\n", "SELECT      d.action, d.created_at AS timestamp_at, u.username AS created_by, d.frontend_outlet_id, o.name AS fe_name\n", "            , d.start_date, d.end_date, z.description AS reason_type, d.disruption_percentage\n", "            \n", "FROM        disr_info d\n", "LEFT JOIN   retail.console_outlet o\n", "            ON o.lake_active_record = true AND o.active = 1 AND o.id = d.frontend_outlet_id\n", "LEFT JOIN   ars.reasons z\n", "            ON z.lake_active_record = true AND z.id = d.reason_id\n", "LEFT JOIN   retail.auth_user u\n", "            ON u.lake_active_record = true AND u.is_active = 1 AND u.id = d.created_by\n", "\n", "\"\"\"\n", "    return read_sql_query(disrupt_stores_dr, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "368a5fd4-b7e6-43da-bb76-e1ec61bf7b51", "metadata": {"tags": []}, "outputs": [], "source": ["current_timestamp = datetime.now().replace(second=0, microsecond=0) + <PERSON><PERSON>ta(\n", "    hours=5, minutes=30\n", ")\n", "start_timestamp = datetime.now().replace(hour=18, minute=30, second=0, microsecond=0)\n", "end_timestamp = datetime.now().replace(hour=19, minute=0, second=0, microsecond=0)"]}, {"cell_type": "code", "execution_count": null, "id": "61b188e3-2570-4a3a-a7fa-c3bcd575899f", "metadata": {}, "outputs": [], "source": ["if (current_timestamp >= start_timestamp) and (current_timestamp <= end_timestamp):\n", "    print(\"Yes, this table update 1 time and at bewteen 6:30pm to 7:00pm\")\n", "\n", "    disrupt_stores_dr = disrupt_stores_dr()\n", "\n", "    disrupt_stores_dr[\"created_date\"] = pd.to_datetime(\n", "        disrupt_stores_dr[\"timestamp_at\"]\n", "    ).dt.date\n", "    agg_df = (\n", "        disrupt_stores_dr.groupby([\"created_by\", \"created_date\", \"reason_type\"])[\n", "            \"frontend_outlet_id\"\n", "        ]\n", "        .count()\n", "        .reset_index(name=\"num_of_ds\")\n", "    )\n", "    pivot_df = pd.pivot_table(\n", "        agg_df,\n", "        index=[\"created_date\", \"created_by\"],\n", "        columns=\"reason_type\",\n", "        values=\"num_of_ds\",\n", "    ).reset_index()\n", "    pivot_df = pivot_df.fillna(0)\n", "\n", "    if pivot_df[\"created_date\"].count() == 0:\n", "        print(\"No record found. Hence no slack alert for dark store disruption\")\n", "\n", "    else:\n", "\n", "        def render_mpl_table(\n", "            data,\n", "            col_width=8,  # 3.0,\n", "            row_height=2,  # 0.625,\n", "            font_size=20,\n", "            header_color=\"#E96125\",\n", "            row_colors=[\"#f1f1f2\", \"w\"],\n", "            edge_color=\"black\",\n", "            bbox=[0, 0, 1, 1],\n", "            header_columns=0,\n", "            ax=None,\n", "            **kwargs\n", "        ):\n", "            if ax is None:\n", "                size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array(\n", "                    [col_width, row_height]\n", "                )\n", "                fig, ax = plt.subplots(figsize=size)\n", "                ax.axis(\"off\")\n", "            mpl_table = ax.table(\n", "                cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs\n", "            )\n", "            mpl_table.auto_set_font_size(False)\n", "            mpl_table.set_fontsize(font_size)\n", "\n", "            for k, cell in mpl_table._cells.items():\n", "                cell.set_edgecolor(edge_color)\n", "                if k[0] == 0 or k[1] < header_columns:\n", "                    cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "                    cell.set_facecolor(header_color)\n", "                #         elif k[0] == data.shape[0]:\n", "                #             cell.set_text_props(weight=\"bold\", ma=\"center\")\n", "                # cell.set_facecolor('#f1f1f2')\n", "                else:\n", "                    cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "            mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "            return ax.get_figure(), ax\n", "\n", "        fig, ax = render_mpl_table(pivot_df, header_columns=0)\n", "        fig.savefig(\"pivot_df.png\", bbox_inches=\"tight\")\n", "\n", "        channel = \"pan_india_store_disruption_information\"  # 'pan_india_store_disruption_information'\n", "        text = \"Hi Team, Summary of dark store disruption created by our team members from yesterday 7pm till today 6pm.\"\n", "\n", "        pb.send_slack_message(channel=channel, text=text, files=[\"./pivot_df.png\"])\n", "    print(\n", "        \"slack alert has been sent to respective slack channel to inforn the same to the team for dark store disruption created\"\n", "    )\n", "\n", "else:\n", "    print(\"Not need to send summary slack alet\")"]}, {"cell_type": "code", "execution_count": null, "id": "607fabf2-9129-4831-89f7-bc3f42c842b7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0ff7351c-acb5-45b5-86d9-7ad7e398ce62", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6c43ef27-53e8-4a86-b609-8fd3598275af", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}