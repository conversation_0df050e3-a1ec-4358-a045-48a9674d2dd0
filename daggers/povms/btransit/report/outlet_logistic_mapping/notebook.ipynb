{"cells": [{"cell_type": "code", "execution_count": null, "id": "cdcd6321-41ef-4561-83ea-71b315bebef0", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "id": "b763fe74-fa58-4513-8bc2-97d69dc8ee11", "metadata": {}, "outputs": [], "source": ["slack_channel = \"#bl-warehouse-support\""]}, {"cell_type": "code", "execution_count": null, "id": "9d9dd139-8515-426c-8bb6-0077f0fcb615", "metadata": {}, "outputs": [], "source": ["redshift_conn = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "b208e9ea-182b-4c8c-b877-36a36205cb60", "metadata": {}, "outputs": [], "source": ["final_df = pd.read_sql_query(\n", "    \"\"\"\n", "    SELECT DISTINCT isd.merchant_outlet_id AS outlet_id, co.name AS outlet_name, co.business_type_id\n", "    FROM lake_ims.ims_sto_details isd\n", "    INNER JOIN lake_retail.console_outlet co ON co.id = isd.merchant_outlet_id\n", "    LEFT JOIN lake_retail.console_outlet_logistic_mapping lm ON lm.outlet_id = co.id\n", "    WHERE isd.created_at >= CURRENT_TIMESTAMP - interval '1 day' AND co.business_type_id in (1,7,11,12,19,20) AND lm.logistic_node_id is NULL\n", "    \"\"\",\n", "    redshift_conn,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "afb2a2a1-4a09-4151-bfa5-63a02d723107", "metadata": {}, "outputs": [], "source": ["if not final_df.empty:\n", "    final_df.to_csv(\"console_outlet_logistic_mapping.csv\", index=False)\n", "    client = pb.connections.get_slack_client(\"bl-analytics-bot\")\n", "    message = \"<!subteam^S03SWP62SBU> Please make mappings for the following outlets in <https://retail.grofers.com/retail/adminconsole/outletlogisticmapping/|Outlet Logistics mapping>. \"\n", "    message += \"Use <https://redash-queries.grofers.com/queries/239800|this query> to find out node_id if needed.\"\n", "    client.files_upload(\n", "        channels=slack_channel,\n", "        file=\"./console_outlet_logistic_mapping.csv\",\n", "        initial_comment=message,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "74ef0217-07d0-4236-b588-3fc074091f89", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}