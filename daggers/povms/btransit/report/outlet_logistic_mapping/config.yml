alert_configs:
  slack:
  - channel: bl-invt-alerts
dag_name: outlet_logistic_mapping
dag_type: report
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03S8EPUZ6G
path: povms/btransit/report/outlet_logistic_mapping
paused: true
pool: povms_pool
project_name: btransit
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 0 */4 * * *
  start_date: '2023-01-10T16:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
