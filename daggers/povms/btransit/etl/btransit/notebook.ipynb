{"cells": [{"cell_type": "code", "execution_count": null, "id": "6aac7e95-720b-4491-8114-edd304e17787", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import psycopg2\n", "import pandas as pd\n", "\n", "secret = pb.get_secret(\"retail/postgres/vendor_console/vendor_console_application_api\")\n", "user = secret.get(\"user\")\n", "password = secret.get(\"password\")\n", "connection = psycopg2.connect(\n", "    dbname=\"vendor_console\",\n", "    host=\"purchaseorder.cmn74bewi8rx.ap-southeast-1.rds.amazonaws.com\",\n", "    port=\"5432\",\n", "    user=user,\n", "    password=password,\n", ")\n", "\n", "sql = \"\"\"\n", "SELECT td.id AS trip_id,\n", "       fc.id AS consignment_id,\n", "       td.source_outlet_name,\n", "       td.source_outlet_id,\n", "       fc.destination_outlet_id,\n", "       fc.destination_outlet_name,\n", "       td.vehicle_number,\n", "       td.state AS trip_state,\n", "       fc.state AS consignment_state,\n", "       td.created_at AT TIME ZONE 'Asia/Kolkata' as created_at,\n", "       (fc.started_at) AT TIME ZONE 'Asia/Kolkata' as started_at,\n", "       (fc.completed_at) AT TIME ZONE 'Asia/Kolkata' as completed_at,\n", "       (fc.updated_at) AT TIME ZONE 'Asia/Kolkata' as updated_at,\n", "       td.created_by_id AS phone_number,\n", "       fd.name\n", "FROM fleet_trip_details td\n", "INNER JOIN fleet_consignment fc ON td.id=fc.trip_id\n", "INNER JOIN fleet_user_details fd on td.created_by_id=fd.phone_number\n", "AND td.created_by_id NOT IN ('9003726824',\n", "                             '9958095891')\n", "AND fc.id NOT IN (4);\n", "\"\"\"\n", "fleet_trip = pd.read_sql_query(sql=sql, con=connection)\n", "\n", "fleet_trip[\"time_taken\"] = (fleet_trip[\"completed_at\"] - fleet_trip[\"started_at\"]).astype(\n", "    \"timedelta64[m]\"\n", ")\n", "\n", "fleet_trip[\"date\"] = fleet_trip[\"started_at\"].dt.date\n", "\n", "fleet_trip = fleet_trip.sort_values(by=\"started_at\", ascending=False)\n", "\n", "reporting_times = fleet_trip[\n", "    [\n", "        \"trip_id\",\n", "        \"date\",\n", "        \"source_outlet_id\",\n", "        \"source_outlet_name\",\n", "        \"destination_outlet_name\",\n", "        \"trip_state\",\n", "        \"consignment_state\",\n", "        \"created_at\",\n", "        \"started_at\",\n", "        \"completed_at\",\n", "        \"updated_at\",\n", "        \"time_taken\",\n", "    ]\n", "]\n", "\n", "reporting_times.columns = [\n", "    \"trip_id\",\n", "    \"Date\",\n", "    \"BE Outlet\",\n", "    \"Backend Name\",\n", "    \"Darkstore Name\",\n", "    \"Trip State\",\n", "    \"Consignment State\",\n", "    \"Check-In\",\n", "    \"Dispatch\",\n", "    \"Arrival at DS\",\n", "    \"Last Updated Time\",\n", "    \"Transit Time\",\n", "]\n", "\n", "sheet_id = \"1Ptl8JMDdYGVbNfIsygB_I9j8wti7b0Sy9p8Z9v_AGBg\"\n", "sheet_name = \"trip_details\"\n", "try:\n", "    pb.to_sheets(fleet_trip, sheet_id, sheet_name, service_account=\"service_account\")\n", "except Exception as e:\n", "    print(e)\n", "\n", "sheet_id = \"1Ptl8JMDdYGVbNfIsygB_I9j8wti7b0Sy9p8Z9v_AGBg\"\n", "sheet_name = \"reporting_times\"\n", "try:\n", "    pb.to_sheets(reporting_times, sheet_id, sheet_name, service_account=\"service_account\")\n", "except Exception as e:\n", "    print(e)\n", "\n", "adherence_states = reporting_times.groupby(\n", "    by=[\"Trip State\", \"Date\", \"BE Outlet\", \"Backend Name\"]\n", ").count()[[\"Darkstore Name\"]]\n", "\n", "adherence_df = (\n", "    pd.pivot_table(\n", "        adherence_states,\n", "        values=\"Darkstore Name\",\n", "        index=[\"Date\", \"BE Outlet\", \"Backend Name\"],\n", "        columns=[\"Trip State\"],\n", "    )\n", "    .sort_values(by=[\"Date\"], ascending=False)\n", "    .reset_index()\n", ")\n", "\n", "# sql = \"select outlet_id,facility_id,outlet_name from lake_po.bulk_facility_outlet_mapping where active=1\"\n", "sql = \"\"\"with tea_tagged_stores as (\n", "    SELECT bfom.outlet_id, pfom.facility_id, bfom.facility_id as backend_facility_id, cf.name as backend_facility_name, wom.warehouse_id as backend_outlet_id \n", "    FROM lake_po.bulk_facility_outlet_mapping bfom\n", "    INNER JOIN lake_po.physical_facility_outlet_mapping pfom ON bfom.outlet_id=pfom.outlet_id\n", "    INNER JOIN lake_crates.facility cf on cf.id = bfom.facility_id\n", "    INNER JOIN lake_retail.console_outlet co on co.facility_id = bfom.facility_id\n", "    INNER JOIN lake_retail.warehouse_outlet_mapping wom on wom.warehouse_id = co.id\n", "    WHERE bfom.active = True\n", "    AND pfom.active = 1\n", "    AND pfom.ars_active = 1\n", "    AND cf.name like ('%%CPC%%') and co.name not like ('%%Liquidation%%')\n", "    -- AND wom.cloud_store_id <> co.id\n", "    group by 1,2,3,4,5\n", ")\n", "select * from tea_tagged_stores\n", "order by 1,2,3\"\"\"\n", "con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "fnv_replenishments = pd.read_sql_query(sql=sql, con=con)\n", "\n", "fnv_count = (\n", "    fnv_replenishments.groupby(\n", "        by=[\"backend_facility_id\", \"backend_outlet_id\", \"backend_facility_name\"]\n", "    )\n", "    .count()\n", "    .reset_index()[\n", "        [\n", "            \"backend_facility_id\",\n", "            \"backend_outlet_id\",\n", "            \"backend_facility_name\",\n", "            \"outlet_id\",\n", "        ]\n", "    ]\n", "    .rename(columns={\"outlet_id\": \"count\"})\n", ")\n", "\n", "fnv_count[[\"count\"]] = fnv_count[[\"count\"]] * 2\n", "\n", "adherence_df[[\"BE Outlet\"]] = adherence_df[[\"BE Outlet\"]].astype(float)\n", "fnv_count[[\"backend_outlet_id\"]] = fnv_count[[\"backend_outlet_id\"]].astype(float)\n", "\n", "fnv_count = fnv_count[[\"backend_facility_id\", \"count\"]]\n", "\n", "import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "\n", "backend_facility_ids_list = [\n", "    1,\n", "    3,\n", "    15,\n", "    22,\n", "    42,\n", "    43,\n", "    92,\n", "    264,\n", "    513,\n", "    517,\n", "    554,\n", "    555,\n", "    1206,\n", "    1320,\n", "]\n", "# sql = \"select outlet_id,facility_id,outlet_name from lake_po.bulk_facility_outlet_mapping where active=1\"\n", "sql = \"select * from lake_po.bulk_facility_outlet_mapping where active=1\"\n", "con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "b_outlet_facility = pd.read_sql_query(sql=sql, con=con)\n", "b_outlet_facility = b_outlet_facility.loc[\n", "    b_outlet_facility.facility_id.isin(\n", "        [1, 3, 15, 22, 42, 43, 92, 264, 513, 517, 554, 555, 1206, 1320]\n", "    )\n", "].reset_index(drop=True)\n", "\n", "temp = b_outlet_facility[[\"facility_id\", \"outlet_id\"]]\n", "sql = \"select outlet_id,facility_id,outlet_name from lake_po.physical_facility_outlet_mapping\"\n", "con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "outlet_facility = pd.read_sql_query(sql=sql, con=con)\n", "outlet_facility.insert(2, \"backend_facility\", 0)\n", "outlet_facility.insert(4, \"slot_1\", \"\")\n", "outlet_facility.insert(5, \"slot_2\", \"\")\n", "\n", "import requests\n", "\n", "for backend_facility_id in backend_facility_ids_list:\n", "    url = \"https://retail-internal.grofer.io/ars/v1/sto-slot-config/\" + str(backend_facility_id)\n", "    response = requests.get(url)\n", "    response.json()[\"data\"]\n", "    slot_timings = response.json()[\"data\"]\n", "    slots = list(slot_timings[\"slot_config\"].keys())\n", "    for slot in slots:\n", "        outlet_ids_list = slot_timings[\"slot_config\"][slot]\n", "        for outlet_id_current in outlet_ids_list:\n", "            back_main_list = list(\n", "                temp.loc[temp.outlet_id == outlet_id_current][\"facility_id\"].astype(float).unique()\n", "            )\n", "\n", "            if backend_facility_id in back_main_list:\n", "                if (\n", "                    outlet_facility.loc[outlet_facility.outlet_id == outlet_id_current]\n", "                    .reset_index(drop=True)\n", "                    .iloc[0][\"slot_1\"]\n", "                    == \"\"\n", "                ):\n", "                    outlet_facility.loc[\n", "                        outlet_facility.outlet_id == outlet_id_current, \"slot_1\"\n", "                    ] = slot\n", "                else:\n", "                    outlet_facility.loc[\n", "                        outlet_facility.outlet_id == outlet_id_current, \"slot_2\"\n", "                    ] = slot\n", "                outlet_facility.loc[\n", "                    outlet_facility.outlet_id == outlet_id_current, \"backend_facility\"\n", "                ] = backend_facility_id\n", "outlet_facility = outlet_facility.loc[\n", "    ~((outlet_facility.slot_1 == \"\") & (outlet_facility.slot_2 == \"\"))\n", "].reset_index(drop=True)\n", "\n", "outlet_facility.insert(6, \"replenishments\", 0)\n", "for i in range(len(outlet_facility)):\n", "    condition = outlet_facility.iloc[i][\"slot_2\"]\n", "    if condition == \"\":\n", "        outlet_facility.at[i, \"replenishments\"] = 1\n", "    else:\n", "        outlet_facility.at[i, \"replenishments\"] = 2\n", "backends = list(outlet_facility.backend_facility.unique())\n", "total_trips = {}\n", "for i in backends:\n", "    total_trips[i] = 0\n", "for i in range(len(outlet_facility)):\n", "    b_id = outlet_facility.iloc[i][\"backend_facility\"]\n", "    replenishments = outlet_facility.iloc[i][\"replenishments\"]\n", "    total_trips[b_id] = total_trips[b_id] + replenishments\n", "\n", "trips_df = pd.DataFrame(columns=[\"backend_facility_id\", \"count\"])\n", "for i in range(len(backends)):\n", "    trips_df.at[i, \"backend_facility_id\"] = backends[i]\n", "    trips_df.at[i, \"count\"] = total_trips[backends[i]]\n", "\n", "sheet_id = \"1fxnIdN50IySokMC7_HY5RtM3mJ7tqCMKnEye4GnZsZA\"\n", "sheet_name = \"Grocery_Replenishment_Slots\"\n", "try:\n", "    pb.to_sheets(outlet_facility, sheet_id, sheet_name, service_account=\"service_account\")\n", "except Exception as e:\n", "    print(e)\n", "trips_df = trips_df.append(fnv_count).reset_index(drop=True)\n", "\n", "# sql = \"select outlet_id,facility_id,outlet_name from lake_po.bulk_facility_outlet_mapping where active=1\"\n", "sql = \"\"\"select id as outlet_id, facility_id from lake_retail.console_outlet \"\"\"\n", "con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "b_o_f_mapping = pd.read_sql_query(sql=sql, con=con)\n", "\n", "temp = (\n", "    adherence_df.set_index(\"BE Outlet\")\n", "    .join(b_o_f_mapping.set_index(\"outlet_id\"))\n", "    .reset_index()\n", "    .set_index(\"facility_id\")\n", "    .join(trips_df.set_index(\"backend_facility_id\"))\n", ")\n", "\n", "temp = temp[[\"Date\", \"Backend Name\", \"COMPLETED\", \"FORCE_COMPLETED\", \"IN_TRANSIT\", \"count\"]]\n", "\n", "temp = temp.sort_values(by=\"Date\", ascending=False).reset_index(drop=True)\n", "\n", "temp[\"adherence (%)\"] = (temp[\"COMPLETED\"] / temp[\"count\"]) * 100\n", "\n", "sheet_id = \"1Ptl8JMDdYGVbNfIsygB_I9j8wti7b0Sy9p8Z9v_AGBg\"\n", "sheet_name = \"Overall_adherence\""]}, {"cell_type": "code", "execution_count": null, "id": "943122ec-774f-4de6-8d62-2ef15fc22177", "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.to_sheets(temp, sheet_id, sheet_name, service_account=\"service_account\")\n", "except Exception as e:\n", "    print(e)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}