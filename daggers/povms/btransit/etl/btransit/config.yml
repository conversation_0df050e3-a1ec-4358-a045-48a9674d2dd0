alert_configs:
  slack:
  - channel: C03U15SFX6F
dag_name: btransit
dag_type: etl
escalation_priority: medium
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03SADKEA0Z
path: povms/btransit/etl/btransit
paused: true
pool: povms_pool
project_name: btransit
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 0/30 * * * *
  start_date: '2022-11-30T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
