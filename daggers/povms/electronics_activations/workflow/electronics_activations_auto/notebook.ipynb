{"cells": [{"cell_type": "code", "execution_count": null, "id": "7b73d630-dd4d-4b97-ac79-88f718597040", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "from tqdm import tqdm\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "e7f87941-8c77-4957-b9e4-519d84de6604", "metadata": {}, "outputs": [], "source": ["CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "c32bb7a4-cd35-43cf-92cc-5aae280673c0", "metadata": {}, "outputs": [], "source": ["!pip uninstall -y openpyxl"]}, {"cell_type": "code", "execution_count": null, "id": "0df6fa5b-4ddd-4ece-91be-d2582f6f65fd", "metadata": {}, "outputs": [], "source": ["!pip install openpyxl==3.1.5"]}, {"cell_type": "code", "execution_count": null, "id": "d7319d4d-eaa9-4e48-b7bf-83e3e523f3ce", "metadata": {}, "outputs": [], "source": ["import boto3\n", "import requests\n", "import os"]}, {"cell_type": "markdown", "id": "bcb2212d-dc56-4297-af2d-c827f4808883", "metadata": {}, "source": ["### serialized items"]}, {"cell_type": "code", "execution_count": null, "id": "c59484e7-671d-405c-a7ac-8e36adda393d", "metadata": {}, "outputs": [], "source": ["def items():\n", "\n", "    sql = f\"\"\"\n", "    \n", "        with festive_items as (\n", "        select distinct item_id, product_type\n", "        from supply_etls.event_bulk_upload_live_tea_outlet_level_tracker be\n", "        where event_id = 67\n", "        and product_type not in ('Hair Dryer', 'Hair Straightener')\n", "        ),\n", "\n", "        keys as (\n", "        select icd.item_id, icd.name, product_type, \n", "            mgf.name as vendor_name,\n", "            variant_mrp, \n", "            cast(weight_in_gm as int) weight, \n", "            cast(length_in_cm as int) length, cast(height_in_cm as int) height, cast(breadth_in_cm as int) breadth, \n", "            cast((length_in_cm*breadth_in_cm) as int) lb,\n", "            cast((breadth_in_cm*height_in_cm) as int) bh,\n", "            cast((length_in_cm*height_in_cm) as int) lh\n", "        from rpc.item_category_details icd\n", "        inner join rpc.item_details id on icd.item_id = id.item_id\n", "        inner join rpc.product_manufacturer mgf on id.manufacturer_id = mgf.id\n", "        where icd.item_id in (select distinct item_id from festive_items)\n", "        ),\n", "\n", "        area as (\n", "        select i.*, greatest(lb,bh,lh) as max_area, (lb+bh+lh - greatest(lb,bh,lh) - least(lb,bh,lh)) as area, least(lb,bh,lh) as min_area\n", "        from keys i\n", "        ),\n", "\n", "        metrics as (\n", "        select a.*,\n", "            case when variant_mrp > 7500 then 1 else 0 end as high_mrp,\n", "            case when weight > 5000 then 1 else 0 end as high_weight,\n", "            -- case when weight > 12000 then 1 else 0 end as v_high_weight,\n", "            case when (max_area > 4209 or area > 2789 or min_area > 1402) then 1 else 0 end as high_area,\n", "            -- case when area > 2789 then 1 else 0 end as high_area,\n", "            -- case when min_area > 1402 then 1 else 0 end as high_min_area,\n", "            case when vendor_name in ('APPLE INC', 'Unicorn Infosolutions') then 1 else 0 end as unicorn_product\n", "        from area a\n", "        )\n", "\n", "        select distinct m.*, case\n", "            when high_mrp = 0 and high_weight = 0 and high_area = 0 and unicorn_product = 0 then 'all_city_lt'\n", "            when high_mrp = 0 and high_weight = 0 and high_area = 0 and unicorn_product = 1 then 'major_city_lt'\n", "            when high_mrp = 1 and high_weight = 0 and high_area = 0 and unicorn_product = 0 then 'major_city_lt'\n", "            when high_mrp = 1 and high_weight = 0 and high_area = 0 and unicorn_product = 1 then 'unicorn_stores'\n", "            when (high_area = 1 or high_weight = 1) and product_type not in ('Desert Air Cooler', 'Split AC') then 'heavy_stores'\n", "            when product_type = 'Desert Air Cooler' then 'palletes'\n", "            when product_type = 'Split AC' then 'fragile_heavy'\n", "            else null\n", "            end as assortment_type\n", "        from metrics m\n", "\n", "        \"\"\"\n", "\n", "    con = pb.get_connection(\"[Warehouse] Trino\")\n", "    df = pd.read_sql_query(sql=sql, con=con)\n", "    return df\n", "\n", "\n", "items = items()"]}, {"cell_type": "markdown", "id": "498c9a02-bd37-400e-8ef2-831b9b05b0ff", "metadata": {}, "source": ["### bundle ids"]}, {"cell_type": "code", "execution_count": null, "id": "b16c1336-57f4-4928-9d7b-4d027273e848", "metadata": {}, "outputs": [], "source": ["def bundles():\n", "\n", "    sql = f\"\"\"\n", "    \n", "       select * from supply_etls.serialized_inventory_bundle_item_ids\n", "       where active = 1\n", "    \n", "    \"\"\"\n", "    con = pb.get_connection(\"[Warehouse] Trino\")\n", "    df = pd.read_sql_query(sql=sql, con=con)\n", "    return df\n", "\n", "\n", "bundles = bundles()"]}, {"cell_type": "code", "execution_count": null, "id": "1643d13e-63da-46a8-892e-ac101b7ce3df", "metadata": {}, "outputs": [], "source": ["item_tuple = tuple(items[\"item_id\"].unique())"]}, {"cell_type": "markdown", "id": "73d8f5bb-68c9-45e3-9cce-e62552eb24bf", "metadata": {}, "source": ["### stores identification"]}, {"cell_type": "code", "execution_count": null, "id": "d41f259f-f943-4a9a-8a7b-6e374dd7b983", "metadata": {}, "outputs": [], "source": ["def storage():\n", "\n", "    sql = \"\"\"\n", "    \n", "        with mapping as (\n", "        select pfom.facility_id, pfom.outlet_id, pfom.outlet_name, cc.cluster_id, cluster_name, cl.id as city_id, cl.name as city_name, count(distinct cart_id) carts, sum(product_quantity * unit_selling_price) aov\n", "        from po.physical_facility_outlet_mapping pfom\n", "        left join retail.console_outlet co on co.id = pfom.outlet_id\n", "        left join retail.console_location cl on cl.id = tax_location_id\n", "        left join rpc.ams_city_cluster_mapping cc on cl.id = cc.city_id\n", "        left join rpc.ams_cluster clus on cc.cluster_id = clus.cluster_id\n", "        left join dwh.fact_sales_order_item_details fsoid on fsoid.outlet_id = pfom.outlet_id\n", "        where cc.cluster_id > 15000\n", "        and cc.lake_active_record\n", "        and clus.lake_active_record\n", "        and order_create_dt_ist = current_date - interval '2' day\n", "        group by 1,2,3,4,5,6,7\n", "        ),\n", "\n", "        total_aov as (\n", "        select 1 as pan_india, sum(aov) pan_india_aov\n", "        from mapping\n", "        group by 1\n", "        ),\n", "\n", "        lt as (\n", "        select distinct pos_outlet_id, type, 1 as long_tail_flag\n", "        from serviceability.ser_store_polygons pol\n", "        inner join dwh.dim_merchant_outlet_facility_mapping m on cast(pol.merchant_id as int) = m.frontend_merchant_id\n", "        where is_active = true\n", "        and type = 'LONGTAIL_POLYGON'\n", "        ),\n", "\n", "        unicorn as (\n", "        select distinct pos_outlet_id, type, 1 as unicorn_check\n", "        from serviceability.ser_store_polygons pol\n", "        inner join dwh.dim_merchant_outlet_facility_mapping m on cast(pol.merchant_id as int) = m.frontend_merchant_id\n", "        where is_active = true\n", "        and type = 'UNICORN'\n", "        and pos_outlet_id in (2693,3522,5101,4702,4968,3234,3244,5032,4598,4790,4494,5049,2346,3710,2912,2268,2991,2764,4754,3575,2987,4544,5557,5694,4832,3747,3326,5366,3900,5086,3768,2990,3570,2733,4776,5044,1956,2446,4704,3991,3952,3378,5393,2149,5572,5485,3143,2780,5508,5637,2776,3383,3943,3481,3302,5071,4446,3350,2189,3417,1419,4901,3419,5279,4441,5496,4547,4464,4438,5126,5472,4442,5562,4918,1314,4727,3252,3409,2796,2395,3179,3645,3519,4561,4559,4923,1916,1910,5210,4609,3199,5402,6003,5846,5968,5227,4115,5041,4938,5219,4800,5571,4576,3791,1393,4570,3351,4113)\n", "        ),\n", "\n", "        infra as (\n", "        select facility_id, storage_type, 1 as infra_check\n", "        from ars.physical_facility_storage_capacity pfsc\n", "        where pfsc.storage_type IN ('LARGE_HEAVY')\n", "        and pfsc.active\n", "        and pfsc.lake_active_record \n", "        and pfsc.storage_capacity > 0\n", "        ),\n", "\n", "        pallet as (\n", "        select facility_id, storage_type, 1 as pallet_check\n", "        from ars.physical_facility_storage_capacity pfsc\n", "        where pfsc.storage_type IN ('PALLET_HEAVY')\n", "        and pfsc.active\n", "        and pfsc.lake_active_record \n", "        and pfsc.storage_capacity > 0\n", "        ),\n", "\n", "        fragile as (\n", "        select facility_id, storage_type, 1 as fragile_check\n", "        from ars.physical_facility_storage_capacity pfsc\n", "        where pfsc.storage_type IN ('LARGE_FRAGILE_HEAVY')\n", "        and pfsc.active\n", "        and pfsc.lake_active_record \n", "        and pfsc.storage_capacity > 0\n", "        ),\n", "\n", "        last_mile_base as (\n", "        select\n", "        date(date_parse(dt,'%%Y%%m%%d')) as account_date,\n", "        date_trunc('week',date(date_parse(dt,'%%Y%%m%%d'))) as week_start_date,\n", "        driver_id,\n", "        name,\n", "        lifecycle_status,\n", "        city_name,\n", "        store_id,\n", "        store_name,\n", "        driver_tag,\n", "        other_tags,\n", "        other_tag\n", "        from (\n", "            select \n", "            dt,\n", "            driver_id,\n", "            name,\n", "            lifecycle_status,\n", "            city_name,\n", "            store_id,\n", "            store_name,\n", "            driver_tag,\n", "            other_tags\n", "            from zomato.jumbo_derived.delivery_drivers_dt\n", "            where dt = date_format(current_date - interval '1' day,'%%Y%%m%%d')\n", "            and driver_category = 'blinkit'\n", "            and carrier_id = 7\n", "            )\n", "        cross join unnest(other_tags) as other_tags(other_tag)\n", "        where other_tag in ('3_WH','LARGE_ORDER_FLEET_BLINKIT')\n", "        ),\n", "\n", "        last_mile_final as (\n", "        select *,\n", "        dense_rank() over (partition by driver_id order by login_time asc) as asc_rnk,\n", "        dense_rank() over (partition by driver_id order by login_time desc) as desc_rnk\n", "        from (\n", "            select \n", "            distinct\n", "            b.driver_id,\n", "            b.name,\n", "            b.city_name,\n", "            b.store_id,\n", "            b.store_name,\n", "            b.driver_tag,\n", "            b.other_tag,\n", "            cast(from_unixtime(login_time/1000) as timestamp) as login_time,\n", "            max_by(case when logout_time > 0 then cast(from_unixtime(logout_time/1000) as timestamp) else null end,cast(from_unixtime(timestamp/1000) as timestamp)) as logout_time\n", "            from zomato.jumbo2.driver_session_events e\n", "            inner join last_mile_base b on concat('FE',cast(e.delivery_driver_id+10000 as varchar)) = b.driver_id\n", "            where dt = date_format(current_date,'%%Y%%m%%d')\n", "            group by 1,2,3,4,5,6,7,8\n", "        )),\n", "\n", "        last_mile as (\n", "        select pos_outlet_id, 1 as lm_flag\n", "        from last_mile_final lm\n", "        left join logistics_data_etls.blinkit_store_mapping store on store.store_id = lm.store_id\n", "        left join dwh.dim_merchant_outlet_facility_mapping map on map.frontend_merchant_id = store.blinkit_store_id\n", "        where (asc_rnk = 1 or desc_rnk = 1)\n", "        and other_tag = 'LARGE_ORDER_FLEET_BLINKIT'\n", "        ),\n", "\n", "        flag as (\n", "        select distinct m.facility_id, m.outlet_id, m.outlet_name,\n", "        cluster_id, cluster_name, city_id, city_name,\n", "        coalesce(carts,0) carts,\n", "        coalesce(aov,0) aov,\n", "        coalesce(long_tail_flag,0) lt_flag,\n", "        coalesce(unicorn_check,0) as unicorn_flag,\n", "        coalesce(infra_check,0) as infra_check,\n", "        coalesce(pallet_check,0) as pallet_check,\n", "        coalesce(fragile_check,0) as fragile_check,\n", "        coalesce(lm_flag,0) as last_mile_check,\n", "        sum(aov) over (partition by city_id) city_aov,\n", "        1 as flag\n", "        from mapping m\n", "        left join lt on lt.pos_outlet_id = m.outlet_id\n", "        left join unicorn on unicorn.pos_outlet_id = m.outlet_id\n", "        left join infra on infra.facility_id = m.facility_id\n", "        left join pallet on pallet.facility_id = m.facility_id\n", "        left join fragile on fragile.facility_id = m.facility_id\n", "        left join last_mile on last_mile.pos_outlet_id = m.outlet_id\n", "        )\n", "\n", "        select distinct facility_id, outlet_id, outlet_name,\n", "        cluster_id, cluster_name, city_id, city_name,\n", "        coalesce(carts,0) carts,\n", "        coalesce(aov,0) aov,\n", "        lt_flag,\n", "        unicorn_flag,\n", "        infra_check,\n", "        pallet_check,\n", "        fragile_check,\n", "        last_mile_check,\n", "        rank() over (partition by cluster_id order by city_aov desc) city_aov_rank,\n", "        (city_aov*100.000)/pan_india_aov as per_aov\n", "        from flag\n", "        left join total_aov on total_aov.pan_india = flag.flag\n", "\n", "    \"\"\"\n", "\n", "    con = pb.get_connection(\"[Warehouse] Trino\")\n", "    df = pd.read_sql_query(sql=sql, con=con)\n", "    return df\n", "\n", "\n", "stores = storage()"]}, {"cell_type": "code", "execution_count": null, "id": "6e0c047c-dbd1-4e62-adbe-6a5a034ff930", "metadata": {}, "outputs": [], "source": ["stores[\"major_city_check\"] = 0"]}, {"cell_type": "code", "execution_count": null, "id": "9e67a2e5-10bb-4340-92f8-daa17fcaaee6", "metadata": {}, "outputs": [], "source": ["stores[\"major_city_check\"] = np.where((stores[\"per_aov\"] >= 0.99), 1, 0)"]}, {"cell_type": "code", "execution_count": null, "id": "7e21bde1-2d42-4dad-8b3a-6078f15b0811", "metadata": {}, "outputs": [], "source": ["stores[\"major_city_lt_flag\"] = stores[\"major_city_check\"] & stores[\"lt_flag\"]"]}, {"cell_type": "code", "execution_count": null, "id": "91f559a8-b9d1-4ce5-9f78-211e344cb375", "metadata": {}, "outputs": [], "source": ["stores_df = stores.drop([\"city_aov_rank\", \"per_aov\"], axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "bd4d76b7-4fda-4925-ac37-77db6cbebb2a", "metadata": {}, "outputs": [], "source": ["stores_df[\"lt_flag\"] = stores_df[\"lt_flag\"].astype(int)\n", "stores_df[\"unicorn_flag\"] = stores_df[\"unicorn_flag\"].astype(int)\n", "stores_df[\"infra_check\"] = stores_df[\"infra_check\"].astype(int)\n", "stores_df[\"pallet_check\"] = stores_df[\"pallet_check\"].astype(int)\n", "stores_df[\"fragile_check\"] = stores_df[\"fragile_check\"].astype(int)\n", "stores_df[\"last_mile_check\"] = stores_df[\"last_mile_check\"].astype(int)\n", "stores_df[\"major_city_check\"] = stores_df[\"major_city_check\"].astype(int)\n", "stores_df[\"major_city_lt_flag\"] = stores_df[\"major_city_lt_flag\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "0e148636-ad94-423f-a8eb-9fd8c50c515d", "metadata": {}, "outputs": [], "source": ["conditions = [\n", "    (\"assortment_type\", \"all_city_lt\", \"lt_flag\", 1, None, None),\n", "    (\"assortment_type\", \"major_city_lt\", \"major_city_lt_flag\", 1, \"lt_flag\", 1),\n", "    (\"assortment_type\", \"unicorn_stores\", \"unicorn_flag\", 1, \"major_city_lt_flag\", 1),\n", "    (\"assortment_type\", \"heavy_stores\", \"infra_check\", 1, \"last_mile_check\", 1),\n", "    (\"assortment_type\", \"palletes\", \"pallet_check\", 1, \"last_mile_check\", 1),\n", "    (\"assortment_type\", \"fragile_heavy\", \"fragile_check\", 1, \"last_mile_check\", 1),\n", "]\n", "\n", "result_dfs = []\n", "\n", "for col1, val1, col2, val2, col3, val3 in conditions:\n", "    items_filtered = items[items[col1] == val1]\n", "\n", "    if col3 is not None and val3 is not None:\n", "        stores_filtered = stores[(stores[col2] == val2) & (stores[col3] == val3)]\n", "    else:\n", "        stores_filtered = stores[stores[col2] == val2]\n", "\n", "    merged_df = items_filtered.merge(stores_filtered, how=\"cross\")\n", "\n", "    result_dfs.append(merged_df)\n", "\n", "final_df = pd.concat(result_dfs, ignore_index=True)"]}, {"cell_type": "markdown", "id": "8dda07ee-03f7-42d9-b8b7-6761cb7654b7", "metadata": {}, "source": ["### lt flag"]}, {"cell_type": "code", "execution_count": null, "id": "929f8354-d386-4646-a30a-bc7999494b9d", "metadata": {}, "outputs": [], "source": ["def lt_stores():\n", "\n", "    sql = f\"\"\"\n", "    \n", "        select distinct facility_id, type, 1 as long_tail_flag\n", "        from serviceability.ser_store_polygons pol\n", "        inner join dwh.dim_merchant_outlet_facility_mapping m on cast(pol.merchant_id as int) = m.frontend_merchant_id\n", "        where is_active = true\n", "        and type = 'LONGTAIL_POLYGON'\n", "        \n", "        \"\"\"\n", "\n", "    con = pb.get_connection(\"[Warehouse] Trino\")\n", "    df = pd.read_sql_query(sql=sql, con=con)\n", "    return df\n", "\n", "\n", "lt_stores = lt_stores()"]}, {"cell_type": "markdown", "id": "ab121168-46c4-4fd7-92ad-e7d7bf0f078b", "metadata": {}, "source": ["### activations file"]}, {"cell_type": "code", "execution_count": null, "id": "cc4fee69-4e47-4706-be82-bdae8e377fb9", "metadata": {}, "outputs": [], "source": ["activation_file = final_df[[\"item_id\", \"facility_id\"]].copy()"]}, {"cell_type": "code", "execution_count": null, "id": "e1d908c9-c610-4398-9060-b6301d2dd9d8", "metadata": {}, "outputs": [], "source": ["activation_file[\"master_assortment_substate\"] = \"Temporarily Inactive\""]}, {"cell_type": "code", "execution_count": null, "id": "a1283a3b-7215-4330-9955-74d73d9c9d5d", "metadata": {}, "outputs": [], "source": ["activation_file.rename(columns={\"facility_id\": \"frontend_facility_id\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "c3df3ece-bdb3-410c-beb0-3be50da4712a", "metadata": {}, "outputs": [], "source": ["def current_activations():\n", "\n", "    sql = f\"\"\"\n", "    \n", "    select pfma.facility_id, item_id, case when master_assortment_substate_id = 1 then 'Active' when master_assortment_substate_id = 2 then 'Inactive' when master_assortment_substate_id = 3 then 'Temporarily Inactive' when master_assortment_substate_id = 4 then 'Discontinued' else null end as current_assortment_state\n", "    from rpc.product_facility_master_assortment pfma\n", "    where item_id in {item_tuple}\n", "    and pfma.facility_id in (select distinct fe_facility_id from supply_etls.invs_tea_tagging1 tag where is_primary_tea_tag = 1)\n", "    and active = 1\n", "    and lake_active_record\n", "    \"\"\"\n", "    con = pb.get_connection(\"[Warehouse] Trino\")\n", "    df = pd.read_sql_query(sql=sql, con=con)\n", "    return df\n", "\n", "\n", "current_activations = current_activations()"]}, {"cell_type": "code", "execution_count": null, "id": "2560aa90-3cd4-4c12-8a0c-f85d7d18b850", "metadata": {}, "outputs": [], "source": ["current_activations[\"current_assortment_state\"] = np.where(\n", "    current_activations[\"current_assortment_state\"] == \"Active\",\n", "    \"Temporarily Inactive\",\n", "    current_activations[\"current_assortment_state\"],  # Keep other values unchanged\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c77bf71c-0faa-42a4-bb3e-ad994c155289", "metadata": {}, "outputs": [], "source": ["changes = pd.merge(\n", "    activation_file,\n", "    current_activations,\n", "    left_on=[\"item_id\", \"frontend_facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", "    how=\"outer\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3b9cc048-a7de-4dff-94c7-ebba<PERSON><PERSON>bee1c", "metadata": {}, "outputs": [], "source": ["changes[changes[\"master_assortment_substate\"] != changes[\"current_assortment_state\"]].count()"]}, {"cell_type": "code", "execution_count": null, "id": "5514d255-4fe4-417c-a1bc-13060fd17042", "metadata": {}, "outputs": [], "source": ["ch_changes = changes[changes[\"master_assortment_substate\"] != changes[\"current_assortment_state\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "6ba966fb-61cf-4db1-837c-7f1f00b317f7", "metadata": {}, "outputs": [], "source": ["ch_changes = ch_changes[\n", "    ~(\n", "        ch_changes[\"master_assortment_substate\"].isna()\n", "        & ch_changes[\"current_assortment_state\"].isin([\"Discontinued\", \"Inactive\"])\n", "    )\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "15da25cc-0262-4d4c-8e52-f7923cf7c816", "metadata": {}, "outputs": [], "source": ["ch_changes[\"master_assortment_substate\"] = np.where(\n", "    ch_changes[\"master_assortment_substate\"].isna()\n", "    & ch_changes[\"current_assortment_state\"].notna(),\n", "    \"Inactive\",\n", "    ch_changes[\"master_assortment_substate\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "365bac27-844e-480e-b641-c3d3f11c2cc7", "metadata": {}, "outputs": [], "source": ["ch_changes[\"frontend_facility_id\"] = np.where(\n", "    ch_changes[\"frontend_facility_id\"].isna() & ch_changes[\"facility_id\"].notna(),\n", "    ch_changes[\"facility_id\"],\n", "    ch_changes[\"frontend_facility_id\"],  # Keeps existing values if condition is False\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3b50b4f1-444a-4dee-8638-447705e6e3af", "metadata": {}, "outputs": [], "source": ["ch_changes[\"substate_reason_type\"] = \"BAU\"\n", "ch_changes[\"request_reason_type\"] = \"NEW_LAUNCH\"\n", "ch_changes[\"city_name\"] = \"\"\n", "ch_changes[\"backend_facility_id\"] = \"\""]}, {"cell_type": "code", "execution_count": null, "id": "c79f4090-a961-43ed-8653-8162cc8819e9", "metadata": {}, "outputs": [], "source": ["ch_changes = ch_changes.drop([\"facility_id\", \"current_assortment_state\"], axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "7a3e707e-741c-41c3-906a-cfde21c1af24", "metadata": {}, "outputs": [], "source": ["dataf = pd.merge(\n", "    ch_changes, lt_stores, left_on=\"frontend_facility_id\", right_on=\"facility_id\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d93a2b22-1e2c-41b8-97e6-72037a1fcb6d", "metadata": {}, "outputs": [], "source": ["dataf[\"long_tail_flag\"] = np.where(\n", "    dataf[\"long_tail_flag\"].isna(), dataf[\"long_tail_flag\"] == 0, dataf[\"long_tail_flag\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a7919497-9ac8-44f1-b8c5-c559f50624d6", "metadata": {}, "outputs": [], "source": ["dataf[\"assortment_type\"] = np.where(dataf[\"long_tail_flag\"] == 1, \"LONGTAIL\", \"EXPRESS_ALL\")"]}, {"cell_type": "code", "execution_count": null, "id": "a43cee58-2737-48d6-a372-6806208943f0", "metadata": {}, "outputs": [], "source": ["dataf = dataf.drop([\"facility_id\", \"type\", \"long_tail_flag\"], axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "d1453456-0a1a-4e02-92ad-a7d447f1302e", "metadata": {}, "outputs": [], "source": ["new_order = [\n", "    \"item_id\",\n", "    \"city_name\",\n", "    \"backend_facility_id\",\n", "    \"frontend_facility_id\",\n", "    \"master_assortment_substate\",\n", "    \"assortment_type\",\n", "    \"substate_reason_type\",\n", "    \"request_reason_type\",\n", "]\n", "dataf = dataf[new_order]"]}, {"cell_type": "markdown", "id": "ef6324da-fad3-4c37-ba9a-c2e9b301ae80", "metadata": {}, "source": ["### bundle activations"]}, {"cell_type": "code", "execution_count": null, "id": "1d0eea2d-7ded-4ddf-9474-06bb7fd847ac", "metadata": {}, "outputs": [], "source": ["def current_activations_log():\n", "\n", "    sql = f\"\"\"\n", "    \n", "        with item_fac as (\n", "        select bundle_id, bundle.item_id, facility_id, new_assortment_type, \n", "        case when new_substate = 1 then 'Active' when new_substate = 2 then 'Inactive' when new_substate = 3 then 'Temporarily Inactive' when new_substate = 4 then 'Discontinued' else null end as new_substate,\n", "        created_at, rank() over (partition by bundle.item_id, facility_id order by created_at desc) activation_rank\n", "        from supply_etls.serialized_inv_bundle_ids bundle\n", "        left join rpc.product_facility_master_assortment_log pfma on pfma.item_id = bundle.item_id\n", "        where insert_ds_ist > cast(current_date - interval '45' day as varchar)\n", "        and active = 1\n", "        and lake_active_record\n", "        ),\n", "\n", "        bundles as (\n", "        select bundle_id, item_id, facility_id, new_assortment_type, new_substate, created_at, rank() over (partition by bundle_id, facility_id order by created_at desc) bundle_rank\n", "        from item_fac\n", "        where activation_rank = 1\n", "        ),\n", "\n", "        final as (\n", "        select bundle_id, item_id, facility_id, \n", "            new_assortment_type, \n", "            new_substate, \n", "            case when bundle_rank = 1 then new_assortment_type else null end as ideal_assortment_type, \n", "            case when bundle_rank = 1 then new_substate else null end as ideal_substate\n", "        from bundles\n", "        )\n", "\n", "        select bundle_id, item_id, facility_id as frontend_facility_id, \n", "            case when new_assortment_type != ideal_assortment_type then ideal_assortment_type else new_assortment_type end as assortment_type, \n", "            case when new_substate != ideal_substate then ideal_substate else new_substate end as master_assortment_substate,\n", "            null as city_name, null as backend_facility_id, 'BAU' as substate_reason_type, 'NEW_LAUNCH' as request_reason_type\n", "        from final\n", "        where ((new_assortment_type != ideal_assortment_type) or (new_substate != ideal_substate))\n", "\n", "        \"\"\"\n", "\n", "    con = pb.get_connection(\"[Warehouse] Trino\")\n", "    df = pd.read_sql_query(sql=sql, con=con)\n", "    return df\n", "\n", "\n", "current_activations_log = current_activations_log()"]}, {"cell_type": "code", "execution_count": null, "id": "3b000104-f055-4482-8b2f-428718e02dcd", "metadata": {}, "outputs": [], "source": ["current_activations_log = current_activations_log.drop(\"bundle_id\", axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "ba691d79-97aa-4fd1-82a6-37ece38eed0e", "metadata": {}, "outputs": [], "source": ["current_activations_log = current_activations_log[new_order]"]}, {"cell_type": "code", "execution_count": null, "id": "1b6d79dd-254a-41dc-8d83-ee7ff34566de", "metadata": {}, "outputs": [], "source": ["dataf = dataf.append(current_activations_log)"]}, {"cell_type": "code", "execution_count": null, "id": "5603e4b8-6549-4caf-a607-b6f748a288c0", "metadata": {}, "outputs": [], "source": ["int_cols = [\"city_name\", \"backend_facility_id\", \"frontend_facility_id\"]\n", "dataf[int_cols] = dataf[int_cols].apply(pd.to_numeric, errors=\"coerce\").astype(\"Int64\")\n", "dataf[int_cols] = dataf[int_cols].astype(str).replace(\"<NA>\", \"\")"]}, {"cell_type": "markdown", "id": "0c488e80-fbc3-4010-ae60-01afa9f59123", "metadata": {}, "source": ["### api key"]}, {"cell_type": "code", "execution_count": null, "id": "81de9018-5f83-4502-ac31-5a0763e79355", "metadata": {}, "outputs": [], "source": ["slack_alert_channel = \"assortment-alerts-plus-discussions\"\n", "EXECUTION_MODE = \"PROD\"\n", "spreadSheetId = \"1mGuEUevGwt7MPz869i3vOBD16cdLNuUmjDUbuv_oNGQ\"\n", "sheetName = \"input\"\n", "logTable = \"assortment_bulk_upload_dag_log_v2\"\n", "CHUNK = 2000\n", "configSheetName = \"config\""]}, {"cell_type": "code", "execution_count": null, "id": "05beb2a9-97c3-4f30-8fca-eb23e263c2d1", "metadata": {}, "outputs": [], "source": ["req_columns = [\n", "    \"item_id\",\n", "    \"city_name\",\n", "    \"backend_facility_id\",\n", "    \"frontend_facility_id\",\n", "    \"master_assortment_substate\",\n", "    \"assortment_type\",\n", "    \"substate_reason_type\",\n", "    \"request_reason_type\",\n", "]\n", "column_types = {\n", "    \"item_id\": \"int\",\n", "    \"city_name\": \"str\",\n", "    \"backend_facility_id\": \"int\",\n", "    \"frontend_facility_id\": \"int\",\n", "    \"master_assortment_substate\": \"str\",\n", "    \"assortment_type\": \"str\",\n", "    \"substate_reason_type\": \"str\",\n", "    \"request_reason_type\": \"str\",\n", "}\n", "\n", "\n", "def convert_columns(df, columnTypeJson=column_types):\n", "    # Iterate over each column in the dataframe\n", "    for col in df.columns:\n", "        if col in columnTypeJson and columnTypeJson[col] == \"str\":\n", "            df[col] = df[col].astype(str)\n", "        elif col in columnTypeJson and columnTypeJson[col] == \"int\":\n", "            df[col] = pd.to_numeric(df[col])\n", "            df[col] = df[col].apply(lambda x: int(x) if not np.isnan(x) else x)\n", "    return df\n", "\n", "\n", "def check_required_columns(df, req_columns):\n", "    # Get the current columns of the DataFrame\n", "    current_columns = set(df.columns)\n", "    # Convert required columns to a set\n", "    required = set(req_columns)\n", "    # Check if all required columns are present\n", "    return required.issubset(current_columns)"]}, {"cell_type": "code", "execution_count": null, "id": "1d8be388-ad34-4ac7-9a5b-15b0636e4aa5", "metadata": {}, "outputs": [], "source": ["def sendSlackAlert(execution_mode, text):\n", "    if execution_mode == \"PROD\":\n", "        try:\n", "            pb.send_slack_message(channel=slack_alert_channel, text=text)\n", "        except Exception as e:\n", "            print(f\"Error sending Slack message: {e}, {text}\")\n", "        # handle the error further if needed\n", "    else:\n", "        print(\"local environment, just printing to console\")\n", "        print(text)\n", "\n", "\n", "def upload_to_bu(file_path, upload_type):\n", "    try:\n", "        url = \"https://retail-internal.grofer.io/bulk_upload/v1/upload_jobs\"\n", "        payload = {\n", "            \"file\": file_path,\n", "            \"created_by\": \"<PERSON><PERSON>\",\n", "            \"user_id\": 14,\n", "            \"is_auto_po\": True,\n", "            \"upload_type_id\": upload_type,\n", "            \"content_type\": \"text/csv\",\n", "        }\n", "        response = requests.post(url, json=payload)\n", "        return response.status_code, response.json()\n", "    except Exception as e:\n", "        print(\"error in bulk upload api\", e)\n", "        return 404, {}\n", "\n", "\n", "def readFromSheetsWithRetry(spreadSheetId, sheetName, raiseExceptionFlag=True, mode=EXECUTION_MODE):\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            if mode == \"LOCAL\":\n", "                script_dir = \"/home/<USER>/inventory-planning-playbooks/\"\n", "                sys.path.insert(0, script_dir)\n", "                from utils.commonFunc import readSpreadsheet\n", "\n", "                values = readSpreadsheet(spreadSheetId, sheetName)\n", "                df = pd.DataFrame(values[1:], columns=values[0])\n", "                return df\n", "            else:\n", "                df = pb.from_sheets(spreadSheetId, sheetName)\n", "                return df\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error fetching data (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to fetch data after {max_retries} attempts: {e}\")\n", "                if raiseExceptionFlag:\n", "                    raise Exception(\n", "                        \"Failed to read sheets after {} attempts\".format(max_retries)\n", "                    ) from e\n", "                else:\n", "                    sendSlackAlert(\n", "                        mode,\n", "                        \"povms_assortment_rationalisation_etl_retail_bot_bulk_upload dag failed to read sheet\"\n", "                        + spreadSheetId\n", "                        + sheetName,\n", "                    )\n", "                    return pd.DataFrame()\n", "\n", "\n", "def pushD<PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "    df,\n", "    tableName,\n", "    description=None,\n", "    primaryKeys=[\"insert_ds_ist\"],\n", "    load_type=\"upsert\",\n", "    environ=\"playground\",\n", "    partition_key=[],\n", "):\n", "    from datetime import datetime, timezone, timedelta\n", "    import time\n", "    import pandas as pd\n", "\n", "    utc_now = datetime.now(timezone.utc)\n", "    ist_now = utc_now + <PERSON><PERSON><PERSON>(hours=5, minutes=30)\n", "    formatted_date = ist_now.strftime(\"%Y-%m-%d\")\n", "    df[\"insert_ds_ist\"] = formatted_date\n", "    df[\"updated_at\"] = pd.to_datetime(utc_now)\n", "    column_dtypes = [\n", "        {\n", "            \"name\": col,\n", "            \"type\": str(df[col].dtype)\n", "            .replace(\"int64\", \"integer\")\n", "            .replace(\"float64\", \"real\")\n", "            .replace(\"str\", \"varchar\")\n", "            .replace(\"datetime64[ns]\", \"timestamp(6)\")\n", "            .replace(\"object\", \"varchar\")\n", "            .replace(\"bool\", \"boolean\")\n", "            .replace(\"datetime64[ns, UTC]\", \"timestamp(6)\"),\n", "            \"description\": col,\n", "        }\n", "        for col in df.columns\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": environ,\n", "        \"table_name\": tableN<PERSON>,\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": primary<PERSON><PERSON><PERSON>,\n", "        \"load_type\": load_type,\n", "        \"table_description\": (description if description else tableName),\n", "    }\n", "\n", "    if len(partition_key) > 0:\n", "        kwargs[\"partition_key\"] = partition_key\n", "\n", "    import pencilbox as pb\n", "\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            pb.to_trino(data_obj=df, **kwargs)\n", "            return\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error pushDfToTrino (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to pushDfToTrino after {max_retries} attempts: {e}\")\n", "                return\n", "\n", "\n", "def processFile(input_df, chunk_size=CHUNK):\n", "    if input_df.shape[0] == 0:\n", "        print(\"nothing to process, returning\")\n", "        return\n", "    elif input_df.shape[0] < chunk_size:\n", "        list_df = [input_df]\n", "    else:\n", "        list_df = [input_df[i : i + chunk_size] for i in range(0, input_df.shape[0], chunk_size)]\n", "\n", "    responses = []\n", "    df_logs = pd.DataFrame()\n", "    for df in list_df:\n", "        uid = int(time.time())\n", "        local_file_path = f\"product_team_assortment_request_upload_bot_{uid}.xlsx\"\n", "        df.to_excel(local_file_path, index=False, engine=\"openpyxl\")\n", "        file_path = f\"assortment/{local_file_path}\"\n", "        secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "        bucket_name = \"retail-bulk-upload\"\n", "        aws_key = secrets.get(\"aws_key\")\n", "        aws_secret = secrets.get(\"aws_secret\")\n", "        session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "        s3 = session.resource(\"s3\")\n", "        bucket_obj = s3.Bucket(bucket_name)\n", "        bucket_obj.upload_file(local_file_path, file_path)\n", "        status_code, responseJson = upload_to_bu(file_path, 100)\n", "        os.remove(local_file_path)\n", "        df[\"uid\"] = uid\n", "        responses.append(responseJson)\n", "        df[\"response\"] = json.dumps(responseJson)\n", "        df[\"status_code\"] = status_code\n", "        df_logs = pd.concat([df_logs, df])\n", "        time.sleep(2)\n", "    df_logs[\"source\"] = \"povms_assortment_rationalisation_etl_retail_bot_bulk_upload\"\n", "    pushDfToTrino(\n", "        df_logs,\n", "        logTable,\n", "        description=\"manual upload via dag log table\",\n", "        load_type=\"append\",\n", "        environ=\"supply_etls\",\n", "        partition_key=[\"insert_ds_ist\"],\n", "    )\n", "    return responses\n", "\n", "\n", "def pushToGoogleSheetsWithRetry(df, sheet_id, sheet_name):\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            pb.to_sheets(df, sheet_id, sheet_name)\n", "            return\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error pushing data (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to push data after {max_retries} attempts: {e}\")\n", "                return"]}, {"cell_type": "code", "execution_count": null, "id": "d0067823-da59-4a61-a392-ddc16b715728", "metadata": {}, "outputs": [], "source": ["input_df = dataf"]}, {"cell_type": "code", "execution_count": null, "id": "dd51d41f-d7f1-4718-9996-9ba76fb352f1", "metadata": {}, "outputs": [], "source": ["input_df[\"backend_facility_id\"] = input_df[\"backend_facility_id\"].fillna(\"\")"]}, {"cell_type": "code", "execution_count": null, "id": "53e4c8e5-b5c4-46b4-a52f-ffb2578047e1", "metadata": {}, "outputs": [], "source": ["try:\n", "    config_df = readFromSheetsWithRetry(\n", "        spreadSheetId, configSheetName, raiseExceptionFlag=False, mode=EXECUTION_MODE\n", "    )\n", "    if config_df.shape[0] > 0 and \"param\" in config_df.columns:\n", "        MAX_RECORDS_PER_RUN = int(\n", "            config_df[config_df[\"param\"] == \"MAX_RECORDS_PER_RUN\"][\"value\"].values[0]\n", "        )\n", "        MAX_RECORDS_PER_REQUEST = int(\n", "            config_df[config_df[\"param\"] == \"MAX_RECORDS_PER_REQUEST\"][\"value\"].values[0]\n", "        )\n", "    else:\n", "        MAX_RECORDS_PER_RUN = 2000\n", "        MAX_RECORDS_PER_REQUEST = 2000\n", "except Exception as e:\n", "    MAX_RECORDS_PER_RUN = 2000\n", "    MAX_RECORDS_PER_REQUEST = 2000"]}, {"cell_type": "code", "execution_count": null, "id": "ce0538a1-11a6-4d25-992b-80c8171f81de", "metadata": {}, "outputs": [], "source": ["import time"]}, {"cell_type": "code", "execution_count": null, "id": "469bf16f-c902-44b8-99cf-fd3639706c95", "metadata": {}, "outputs": [], "source": ["req_columns = [\n", "    \"item_id\",\n", "    \"city_name\",\n", "    \"backend_facility_id\",\n", "    \"frontend_facility_id\",\n", "    \"master_assortment_substate\",\n", "    \"assortment_type\",\n", "    \"substate_reason_type\",\n", "    \"request_reason_type\",\n", "]\n", "flag = check_required_columns(input_df, req_columns)\n", "if not flag or input_df.shape[0] == 0:\n", "    print(\"invalid columns in the google sheet, doing nothing\", input_df.columns)\n", "else:\n", "    if input_df.shape[0] > MAX_RECORDS_PER_RUN:\n", "        print(\"truncating the file to max \", MAX_RECORDS_PER_RUN, input_df.shape[0])\n", "        input_df = input_df.head(MAX_RECORDS_PER_RUN)\n", "\n", "    responses = processFile(input_df, chunk_size=MAX_RECORDS_PER_REQUEST)\n", "    json_string = json.dumps(responses)\n", "    print(json_string)\n", "    sendSlackAlert(EXECUTION_MODE, \"response of the file upload \" + json_string)\n", "    print(\"cleaning up the input sheet\")\n", "    pushToGoogleSheetsWithRetry(pd.DataFrame({\"response\": [json_string]}), spreadSheetId, sheetName)"]}, {"cell_type": "code", "execution_count": null, "id": "51af6a50-091c-423f-a31c-ca70b69196fe", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}