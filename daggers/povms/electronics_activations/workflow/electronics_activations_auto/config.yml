alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: electronics_activations_auto
dag_type: workflow
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U07DBKX9KA9
path: povms/electronics_activations/workflow/electronics_activations_auto
paused: true
pool: povms_pool
project_name: electronics_activations
schedule:
  end_date: '2025-08-06T00:00:00'
  interval: 5 4 * * *
  start_date: '2025-05-19T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 3
