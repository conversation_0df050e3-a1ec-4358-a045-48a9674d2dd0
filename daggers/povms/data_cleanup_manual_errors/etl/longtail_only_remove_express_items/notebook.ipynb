{"cells": [{"cell_type": "code", "execution_count": null, "id": "7b6cbee7-af04-48e0-a002-8c3edc1dd136", "metadata": {}, "outputs": [], "source": ["!pip uninstall -y openpyxl\n", "!pip install openpyxl==3.1.5"]}, {"cell_type": "code", "execution_count": null, "id": "2387d337-01bc-4fce-928d-a28cf4637331", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import requests\n", "import datetime\n", "from pytz import timezone\n", "import boto3\n", "import os\n", "import sys\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import json\n", "import openpyxl"]}, {"cell_type": "code", "execution_count": null, "id": "8d59ff6c-3eb3-4f40-9c1a-be2aafb67035", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "3e416d2c-efe3-4cb9-94b3-3b6845e1374b", "metadata": {}, "outputs": [], "source": ["slack_alert_channel = \"assortment-alerts-plus-discussions\"\n", "EXECUTION_MODE = \"PROD\"\n", "logTable = \"assortment_bulk_upload_dag_log_v2\""]}, {"cell_type": "code", "execution_count": null, "id": "9e0f8cb9-807d-45d4-bff8-eefac4d5f8e5", "metadata": {}, "outputs": [], "source": ["req_columns = [\n", "    \"item_id\",\n", "    \"city_name\",\n", "    \"backend_facility_id\",\n", "    \"frontend_facility_id\",\n", "    \"master_assortment_substate\",\n", "    \"assortment_type\",\n", "    \"substate_reason_type\",\n", "    \"request_reason_type\",\n", "]\n", "column_types = {\n", "    \"item_id\": \"int\",\n", "    \"city_name\": \"str\",\n", "    \"backend_facility_id\": \"int\",\n", "    \"frontend_facility_id\": \"int\",\n", "    \"master_assortment_substate\": \"str\",\n", "    \"assortment_type\": \"str\",\n", "    \"substate_reason_type\": \"str\",\n", "    \"request_reason_type\": \"str\",\n", "}\n", "\n", "\n", "def convert_columns(df, columnTypeJson=column_types):\n", "    # Iterate over each column in the dataframe\n", "    for col in df.columns:\n", "        if col in columnTypeJson and columnTypeJson[col] == \"str\":\n", "            df[col] = df[col].astype(str)\n", "        elif col in columnTypeJson and columnTypeJson[col] == \"int\":\n", "            df[col] = pd.to_numeric(df[col])\n", "            df[col] = df[col].apply(lambda x: int(x) if not np.isnan(x) else x)\n", "    return df\n", "\n", "\n", "def check_required_columns(df, req_columns):\n", "    # Get the current columns of the DataFrame\n", "    current_columns = set(df.columns)\n", "    # Convert required columns to a set\n", "    required = set(req_columns)\n", "    # Check if all required columns are present\n", "    return required.issubset(current_columns)"]}, {"cell_type": "code", "execution_count": null, "id": "1627d052-712c-486d-847c-d205ca084c4a", "metadata": {}, "outputs": [], "source": ["def sendSlackAlert(execution_mode, text):\n", "    if execution_mode == \"PROD\":\n", "        try:\n", "            pb.send_slack_message(channel=slack_alert_channel, text=text)\n", "        except Exception as e:\n", "            print(f\"Error sending Slack message: {e}, {text}\")\n", "        # handle the error further if needed\n", "    else:\n", "        print(\"local environment, just printing to console\")\n", "        print(text)"]}, {"cell_type": "code", "execution_count": null, "id": "00797fad-0093-4be7-9ef3-e1812497e4b7", "metadata": {}, "outputs": [], "source": ["def upload_to_bu(file_path, upload_type):\n", "    try:\n", "        url = \"https://retail-internal.grofer.io/bulk_upload/v1/upload_jobs\"\n", "        payload = {\n", "            \"file\": file_path,\n", "            \"created_by\": \"<PERSON><PERSON>\",\n", "            \"user_id\": 14,\n", "            \"is_auto_po\": True,\n", "            \"upload_type_id\": upload_type,\n", "            \"content_type\": \"text/csv\",\n", "        }\n", "        response = requests.post(url, json=payload)\n", "        return response.status_code, response.json()\n", "    except Exception as e:\n", "        print(\"error in bulk upload api\", e)\n", "        return 404, {}"]}, {"cell_type": "code", "execution_count": null, "id": "07de08c2-edc8-42ea-b670-d435fbf1023b", "metadata": {}, "outputs": [], "source": ["def pushD<PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "    df,\n", "    tableName,\n", "    description=None,\n", "    primaryKeys=[\"insert_ds_ist\"],\n", "    load_type=\"upsert\",\n", "    environ=\"supply_etls\",\n", "    partition_key=[],\n", "):\n", "    from datetime import datetime, timezone, timedelta\n", "    import time\n", "    import pandas as pd\n", "\n", "    utc_now = datetime.now(timezone.utc)\n", "    ist_now = utc_now + <PERSON><PERSON><PERSON>(hours=5, minutes=30)\n", "    formatted_date = ist_now.strftime(\"%Y-%m-%d\")\n", "    df[\"insert_ds_ist\"] = formatted_date\n", "    df[\"updated_at\"] = pd.to_datetime(utc_now)\n", "    column_dtypes = [\n", "        {\n", "            \"name\": col,\n", "            \"type\": str(df[col].dtype)\n", "            .replace(\"int64\", \"integer\")\n", "            .replace(\"float64\", \"real\")\n", "            .replace(\"str\", \"varchar\")\n", "            .replace(\"datetime64[ns]\", \"timestamp(6)\")\n", "            .replace(\"object\", \"varchar\")\n", "            .replace(\"bool\", \"boolean\")\n", "            .replace(\"datetime64[ns, UTC]\", \"timestamp(6)\"),\n", "            \"description\": col,\n", "        }\n", "        for col in df.columns\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": environ,\n", "        \"table_name\": tableN<PERSON>,\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": primary<PERSON><PERSON><PERSON>,\n", "        \"load_type\": load_type,\n", "        \"table_description\": (description if description else tableName),\n", "    }\n", "\n", "    if len(partition_key) > 0:\n", "        kwargs[\"partition_key\"] = partition_key\n", "\n", "    import pencilbox as pb\n", "\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            pb.to_trino(data_obj=df, **kwargs)\n", "            return\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error pushDfToTrino (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to pushDfToTrino after {max_retries} attempts: {e}\")\n", "                return"]}, {"cell_type": "code", "execution_count": null, "id": "db355fa3-f053-4a97-83bc-f349d673bcaa", "metadata": {}, "outputs": [], "source": ["def processFile(input_df, chunk_size=20000):\n", "    if input_df.shape[0] == 0:\n", "        print(\"nothing to process, returning\")\n", "        return\n", "    elif input_df.shape[0] < chunk_size:\n", "        list_df = [input_df]\n", "    else:\n", "        list_df = [input_df[i : i + chunk_size] for i in range(0, input_df.shape[0], chunk_size)]\n", "\n", "    responses = []\n", "    df_logs = pd.DataFrame()\n", "    all_job_ids = []\n", "    for df in list_df:\n", "        uid = int(time.time())\n", "        local_file_path = (\n", "            f\"express_inactive_longtail_store_assortment_request_upload_bot_{uid}.xlsx\"\n", "        )\n", "        df.to_excel(local_file_path, index=False, engine=\"openpyxl\")\n", "        file_path = f\"assortment/{local_file_path}\"\n", "        secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "        bucket_name = \"retail-bulk-upload\"\n", "        aws_key = secrets.get(\"aws_key\")\n", "        aws_secret = secrets.get(\"aws_secret\")\n", "        session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "        s3 = session.resource(\"s3\")\n", "        bucket_obj = s3.Bucket(bucket_name)\n", "        bucket_obj.upload_file(local_file_path, file_path)\n", "        status_code, responseJson = upload_to_bu(file_path, 100)\n", "        os.remove(local_file_path)\n", "        df[\"uid\"] = uid\n", "        # if status_code:\n", "        df[\"job_id\"] = responseJson.get(\"id\") if responseJson else 0  # Ensure it's a list\n", "        # all_job_ids = pd.DataFrame({'job_id': job_ids})\n", "        df[\"status_code\"] = status_code\n", "        df_logs = pd.concat([df_logs, df])\n", "        time.sleep(2)\n", "    df_logs[\"source\"] = \"express_items_removal_from_standalone_longtail_store\"\n", "    pushDfToTrino(\n", "        df_logs,\n", "        logTable,\n", "        description=\"manual upload via dag log table\",\n", "        load_type=\"append\",\n", "        environ=\"supply_etls\",\n", "        partition_key=[\"insert_ds_ist\"],\n", "    )\n", "    return df_logs"]}, {"cell_type": "code", "execution_count": null, "id": "1d62d3e5-9eea-411d-8700-4fb1a4b8aa2f", "metadata": {}, "outputs": [], "source": ["data = f\"\"\"\n", "WITH longtail_fo AS\n", "  (SELECT om.facility_id,\n", "          outlet_id,\n", "          city_id,\n", "          outlet_name,\n", "          type_id\n", "   FROM po.physical_facility_outlet_mapping om\n", "   left join retail.console_outlet co on co.id = om.outlet_id\n", "   WHERE om.active=1\n", "     AND ars_active=1\n", "        and business_type_id = 7 and ((outlet_name not like '%%Test%%') and (outlet_name not like '%%Dummy%%'))\n", "        and company_type_id not in (771)\n", "        and type_id = 3\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5)\n", "\n", "\n", "select ma.item_id, lo.facility_id, assortment_type  \n", "from longtail_fo lo left join\n", "rpc.product_facility_master_assortment ma on ma.facility_id = lo.facility_id\n", "inner join rpc.item_category_details icd on icd.item_id = ma.item_id\n", "inner join rpc.product_product id on id.item_id = icd.item_id and id.active = 1 and id.approved = 1 \n", "where ma.active = 1 and master_assortment_substate_id in (1,3) and assortment_type = 'EXPRESS'\n", "group by 1,2,3\n", "\"\"\"\n", "df = pd.read_sql_query(sql=data, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "96aaed7a-08d4-46e2-91e7-264a385fc38b", "metadata": {}, "outputs": [], "source": ["upload_df = df.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "f6865faf-11f5-4b9b-8ae5-1d8905ce69bc", "metadata": {}, "outputs": [], "source": ["upload_df[\"city_name\"] = \"\"\n", "upload_df[\"backend_facility_id\"] = \"\"\n", "upload_df[\"master_assortment_substate\"] = \"Inactive\"\n", "upload_df[\"substate_reason_type\"] = \"BAU\"\n", "upload_df[\"request_reason_type\"] = \"express_items_removal_from_standalone_longtail_store\""]}, {"cell_type": "code", "execution_count": null, "id": "706df592-0b12-4252-805a-a6e0e805d406", "metadata": {}, "outputs": [], "source": ["upload_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "46febd40-c8d2-4c9a-99b4-025318c985d8", "metadata": {}, "outputs": [], "source": ["upload_df.rename(columns={\"facility_id\": \"frontend_facility_id\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "f6689c06-4d0e-4586-a24b-d92e002c9a20", "metadata": {}, "outputs": [], "source": ["upload_df[\"assortment_type\"] = np.where(\n", "    upload_df[\"assortment_type\"] == \"EXPRESS\", \"EXPRESS_ALL\", \"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0f8a665c-f350-41ba-a2f6-eb22905de264", "metadata": {}, "outputs": [], "source": ["upload_df = upload_df[\n", "    [\n", "        \"item_id\",\n", "        \"city_name\",\n", "        \"backend_facility_id\",\n", "        \"frontend_facility_id\",\n", "        \"master_assortment_substate\",\n", "        \"assortment_type\",\n", "        \"substate_reason_type\",\n", "        \"request_reason_type\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "30c7d61e-5752-48bd-ba82-6257635657c6", "metadata": {}, "outputs": [], "source": ["# Iterate over rows in the input dataframe\n", "if upload_df.shape[0] > 0:\n", "    request_df = convert_columns(upload_df, column_types)\n", "    req_columns = [\n", "        \"item_id\",\n", "        \"city_name\",\n", "        \"backend_facility_id\",\n", "        \"frontend_facility_id\",\n", "        \"master_assortment_substate\",\n", "        \"assortment_type\",\n", "        \"substate_reason_type\",\n", "        \"request_reason_type\",\n", "    ]\n", "    flag = check_required_columns(request_df, req_columns)\n", "    if not flag or request_df.shape[0] == 0:\n", "        print(\"invalid columns in the final df for upload, doing nothing\", input_df.columns)\n", "    else:\n", "        output_df = processFile(request_df)\n", "        job_ids = output_df.job_id.unique()\n", "        # for job_id in job_ids:\n", "        print(\"job_id:\", job_ids)\n", "        sendSlackAlert(\n", "            EXECUTION_MODE,\n", "            f\"response of the file upload standalone longtail store : \" + str(job_ids),\n", "        )\n", "        # output_df['replicated_stores'] = input_facility_id\n", "        print(\"uploading the output file\")\n", "        # pushToGoogleSheetsWithRetry\n", "        # (output_df, spreadSheetId, writesheetName)\n", "else:\n", "    print(\"input df is empty\")"]}, {"cell_type": "code", "execution_count": null, "id": "737a65ba-5cb3-4d5a-ab49-25d93180e60c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}