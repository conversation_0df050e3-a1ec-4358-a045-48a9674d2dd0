alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: longtail_only_remove_express_items
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RR41CKQX
path: povms/data_cleanup_manual_errors/etl/longtail_only_remove_express_items
paused: false
pool: povms_pool
project_name: data_cleanup_manual_errors
schedule:
  end_date: '2025-07-31T00:00:00'
  interval: 30 8,14 * * *
  start_date: '2025-03-26T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
