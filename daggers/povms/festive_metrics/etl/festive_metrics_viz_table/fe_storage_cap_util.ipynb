{"cells": [{"cell_type": "code", "execution_count": null, "id": "cc2a3835-1bc6-43cf-b28c-fafe81e4038f", "metadata": {"tags": []}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e717d3fd-582e-49e1-9235-1d7852b43887", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "57bdc0aa-5ff7-4153-a57b-6c5af2c836c8", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "d41b0124-d898-4660-9eee-ea465f506abb", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "6b0b952e-ceff-4cba-909d-e0dd4e694e0d", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df = read_sql_query(f\"\"\"\n", "festive_metrics_query = f\"\"\"\n", "\n", "WITH store_cap_util AS (\n", "SELECT  CAST(updated_at AS DATE) as insert_ds_ist\n", "        , facility_id AS fe_facility_id\n", "        , facility_name AS fe_facility_name\n", "        , final_storage_type AS storage_type\n", "        , AVG(storage_cap) AS avg_storage_cap\n", "        , MAX(storage_cap) AS max_storage_cap\n", "        , AVG(scaled_onshelf_inventory) AS avg_storage_util\n", "        , MAX(scaled_onshelf_inventory) AS max_storage_util\n", "        , ((100.00*AVG(scaled_onshelf_inventory) )/AVG(storage_cap)) AS avg_storage_util_per\n", "        , CAST(((100.00*MAX(scaled_onshelf_inventory) )/MAX(storage_cap)) AS DOUBLE) AS max_storage_util_per\n", "    \n", "FROM    supply_etls.hourly_storage_util\n", "WHERE   updated_at >= CAST(CURRENT_DATE - INTERVAL '7' DAY AS TIMESTAMP)\n", "        AND updated_at <= CAST(CURRENT_DATE + INTERVAL '1' DAY AS TIMESTAMP)\n", "GROUP BY 1,2,3,4 )\n", "\n", "\n", "SELECT  s.insert_ds_ist, od.city_name AS fe_city_name, s.fe_facility_id, s.fe_facility_name, od.taggings\n", "        , ( CASE \n", "            WHEN storage_type LIKE '%%REGULAR%%' THEN 'REGULAR'\n", "            WHEN storage_type LIKE '%%COLD%%' THEN 'COLD'\n", "            WHEN storage_type LIKE '%%FROZEN%%' THEN 'FROZEN'\n", "            WHEN storage_type LIKE '%%HEAVY%%' THEN 'HEAVY'\n", "          END ) AS storage_type\n", "        , SUM(s.avg_storage_cap) AS avg_storage_cap\n", "        , SUM(s.avg_storage_util) AS avg_storage_util\n", "        , SUM(s.max_storage_cap) AS max_storage_cap\n", "        , SUM(s.max_storage_util) AS max_storage_util\n", "        , CAST(((100.00*SUM(avg_storage_util) )/SUM(avg_storage_cap)) AS DOUBLE) AS avg_storage_util_per\n", "        , CAST(((100.00*SUM(max_storage_util) )/SUM(max_storage_cap)) AS DOUBLE) AS max_storage_util_per\n", "\n", "FROM        store_cap_util  s\n", "JOIN   supply_etls.outlet_details od \n", "    ON od.facility_id = s.fe_facility_id \n", "    and taggings='fe' and ars_check=1 \n", "    and grocery_active_count>=1 \n", "    and store_type='Dark Store'\n", "GROUP BY    1,2,3,4,5,6\n", "        \n", "\"\"\"\n", "# , CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "98b77cbd-47e5-4e41-a925-f9f199e06447", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0c0d7b08-d140-4fac-8932-a15b5347cda4", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df[['insert_ds_ist','updated_at_ist','sale_start','sale_end','cutt_off']] = festive_metrics_df[['insert_ds_ist','updated_at_ist','sale_start','sale_end','cutt_off']].astype('datetime64[ns]')\n", "# festive_metrics_df[['fe_inv','po_qty_created','scheduled_po_qty','unscheduled_po_qty','po_grn_qty','po_remaining_qty','t_scheduled_quantity','t1_scheduled_quantity','t2_scheduled_quantity','t3_scheduled_quantity']] = festive_metrics_df[['fe_inv','po_qty_created','scheduled_po_qty','unscheduled_po_qty','po_grn_qty','po_remaining_qty','t_scheduled_quantity','t1_scheduled_quantity','t2_scheduled_quantity','t3_scheduled_quantity']].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "eaa9d2cc-879b-4fca-9c11-e2924bd614b9", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "f87fed17-ac34-4c98-add2-b0c1c4ef7496", "metadata": {}, "outputs": [], "source": ["# column_dtypes = [\n", "#     {\n", "#         \"name\": col,\n", "#         \"type\": str(festive_metrics_df[col].dtype).replace(\"int64\", \"integer\").replace(\"float64\", \"real\").replace(\"str\",\"varchar\").replace(\"datetime64[ns]\",\"date\").replace(\"object\",\"varchar\"),\n", "#         \"description\": \"sample description\"\n", "#     } for col in festive_metrics_df.columns\n", "# ]\n", "\n", "# column_dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "5520c15c-0ed7-465f-8c17-ea466a656c1f", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"fe_city_name\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"fe_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"taggings\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"fe_facility_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"storage_type\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"avg_storage_cap\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"avg_storage_util\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"avg_storage_util_per\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"max_storage_cap\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"max_storage_util\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"max_storage_util_per\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "1ecb3a26-ad62-40f4-b1c6-dc25cf32f750", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festive_metrics_temp_viz_fe_storage_cap_util\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"insert_ds_ist\",\n", "        \"fe_facility_id\",\n", "        \"fe_facility_name\",\n", "        \"fe_city_name\",\n", "        \"storage_type\",\n", "    ],\n", "    \"partition key\": \"insert_ds_ist\",\n", "    \"incremental_key\": \"insert_ds_ist\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive metrics\",\n", "}\n", "\n", "to_trino(festive_metrics_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}