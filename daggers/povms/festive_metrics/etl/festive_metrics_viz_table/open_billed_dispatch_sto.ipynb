{"cells": [{"cell_type": "code", "execution_count": null, "id": "cc2a3835-1bc6-43cf-b28c-fafe81e4038f", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e717d3fd-582e-49e1-9235-1d7852b43887", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "57bdc0aa-5ff7-4153-a57b-6c5af2c836c8", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "d41b0124-d898-4660-9eee-ea465f506abb", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "6b0b952e-ceff-4cba-909d-e0dd4e694e0d", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df = read_sql_query(f\"\"\"\n", "festive_metrics_query = f\"\"\"\n", "        WITH base AS(\n", "\n", "        SELECT \n", "            CAST(indent_raised_ts_ist AS DATE) AS insert_ds_ist,\n", "            be_outlet_id,\n", "            fe_outlet_id,\n", "            be_facility_id,\n", "            item_id,\n", "            product_type,\n", "            SUM(indent_raised_qty) AS sto_qty_created,\n", "            SUM(billed_qty) AS billed_sto_quantity,\n", "            SUM(intransit_qty) AS dispatch_qty,\n", "            SUM(grn_qty) AS grn_quantity,\n", "            SUM(intransit_qty - grn_qty) AS pending_grn_quantity,\n", "            SUM(dn_qty) AS dn_quantity,\n", "            SUM(indent_raised_qty - grn_qty) AS open_sto_quantity,\n", "            0.00 AS bill_fillrate,\n", "            0.00 AS dispatch_fillrate,\n", "            0.00 AS gmv_sto_qty_created, \n", "            0.00 AS sku_count_sto_qty_created, \n", "            0.00 AS gmv_billed_sto_quantity, \n", "            0.00 AS sku_count_billed_sto_quantity, \n", "            0.00 AS gmv_dispatch_qty, \n", "            0.00 AS sku_count_dispatch_qty, \n", "            0.00 AS gmv_grn_qty, \n", "            0.00 AS sku_count_grn_qty, \n", "            0.00 AS gmv_pending_grn, \n", "            0.00 AS sku_count_pending_grn, \n", "            0.00 AS gmv_total_dn_qty, \n", "            0.00 AS sku_count_total_dn_qty, \n", "            0.00 AS gmv_open_sto_quantity, \n", "            0.00 AS sku_count_open_sto_quantity\n", "\n", "        FROM\n", "            viz.dwh_flat_ars_inventory_transfer_details\n", "\n", "        WHERE\n", "            indent_raised_ts_ist >= CURRENT_DATE - INTERVAL '15' DAY\n", "            AND indent_raised_ts_ist IS NOT NULL\n", "\n", "        GROUP BY\n", "            1, 2, 3, 4, 5, 6\n", "        ),\n", "        \n", "        festive_details as (\n", "                    \n", "            select distinct\n", "                be_facility_id as facility_id, \n", "                item_id,\n", "                festival_name as festival,\n", "                assortment_reason_type as festival_bau,\n", "                sale_start_date as sale_start, \n", "                sale_end_date as sale_end\n", "            from ars_etls.final_festival_planning\n", "            where festival_name is not null\n", "\n", "        ),\n", "\n", "        outlet_details AS (\n", "            SELECT \n", "                city_id, \n", "                city_name, \n", "                hot_outlet_id, \n", "                hot_outlet_name,\n", "                inv_outlet_id, \n", "                inv_outlet_name, \n", "                facility_id, \n", "                facility_name, \n", "                business_type_id, \n", "                taggings\n", "            FROM \n", "                supply_etls.outlet_details\n", "            where \n", "                ars_check = 1\n", "                and grocery_active_count >= 1\n", "                and (store_type in ('Packaged Goods', 'Dark Store'))\n", "        ),\n", "\n", "        item_details AS (\n", "            SELECT \n", "                item_id, \n", "                item_name, \n", "                l0_id, \n", "                l0_category,\n", "                l1_id, \n", "                l1_category, \n", "                l2_id, \n", "                l2_category, \n", "                p_type, \n", "                assortment_type\n", "            FROM \n", "                supply_etls.item_details\n", "            WHERE \n", "                assortment_type='Packaged Goods'\n", "                and handling_type = 'Non Packaging Material'\n", "        ),\n", "        \n", "        sto AS (\n", "            select for_date, item_id, outlet_id, cpd, aps_adjusted\n", "            from ars.outlet_item_aps_derived_cpd\n", "            where for_date >= CURRENT_DATE-INTERVAL '15' DAY\n", "            and insert_ds_ist >= cast((CURRENT_DATE-INTERVAL '15' DAY) as varchar)\n", "            and lake_active_record \n", "        )\n", "\n", "        SELECT\n", "            b.insert_ds_ist,\n", "            od.facility_id as fe_facility_id,\n", "            bd.facility_id as be_facility_id,\n", "            od.city_name AS fe_city_name, \n", "            od.facility_name AS fe_facility_name, \n", "            bd.facility_name AS be_facility_name,\n", "            id.p_type,\n", "            id.l0_category, \n", "            id.l1_category, \n", "            id.l2_category, \n", "            id.l0_id, \n", "            id.l1_id, \n", "            id.l2_id,\n", "            (CASE WHEN fd.festival IS NOT NULL then fd.festival ELSE 'BAU' END) fastival_tag, \n", "            (CASE WHEN fd.festival IS NOT NULL then fd.festival_bau ELSE 'BAU' END) core_bau_flag,\n", "            coalesce(sum(s.cpd),0) as cpd,\n", "            sum(sto_qty_created) as sto_qty_created,\n", "            sum(billed_sto_quantity) as billed_sto_quantity,\n", "            sum(dispatch_qty) as dispatch_qty,\n", "            sum(grn_quantity) as grn_quantity,\n", "            sum(pending_grn_quantity) as pending_grn_quantity,\n", "            sum(dn_quantity) as dn_quantity,\n", "            sum(open_sto_quantity) as open_sto_quantity,\n", "            sum(bill_fillrate) as bill_fillrate,\n", "            sum(dispatch_fillrate) as dispatch_fillrate,\n", "            sum(gmv_sto_qty_created) as gmv_sto_qty_created, \n", "            coalesce(count(distinct case when sto_qty_created>0 then id.item_id end),0) as sku_count_sto_qty_created, \n", "            sum(gmv_billed_sto_quantity) as gmv_billed_sto_quantity, \n", "            coalesce(count(distinct case when billed_sto_quantity>0 then id.item_id end),0) as sku_count_billed_sto_quantity, \n", "            sum(gmv_dispatch_qty) as gmv_dispatch_qty, \n", "            coalesce(count(distinct case when dispatch_qty>0 then id.item_id end),0) as sku_count_dispatch_qty, \n", "            sum(gmv_grn_qty) as gmv_grn_qty, \n", "            coalesce(count(distinct case when grn_quantity>0 then id.item_id end),0) as sku_count_grn_qty, \n", "            sum(gmv_pending_grn) as gmv_pending_grn, \n", "            coalesce(count(distinct case when pending_grn_quantity>0 then id.item_id end),0) as sku_count_pending_grn, \n", "            sum(gmv_total_dn_qty) as gmv_total_dn_qty, \n", "            coalesce(count(distinct case when dn_quantity>0 then id.item_id end),0) as sku_count_total_dn_qty, \n", "            sum(gmv_open_sto_quantity) as gmv_open_sto_quantity, \n", "            coalesce(count(distinct case when open_sto_quantity>0 then id.item_id end),0) as sku_count_open_sto_quantity\n", "\n", "        FROM\n", "            base \n", "                AS b\n", "        LEFT JOIN\n", "            festive_details\n", "                AS fd\n", "                ON b.be_facility_id = fd.facility_id\n", "                AND b.item_id = fd.item_id\n", "                AND insert_ds_ist BETWEEN sale_start - INTERVAL '10' DAY AND sale_end\n", "        LEFT JOIN\n", "            outlet_details\n", "                AS bd\n", "                ON b.be_facility_id = bd.facility_id\n", "        LEFT JOIN\n", "            outlet_details\n", "                AS od\n", "                ON b.fe_outlet_id = od.inv_outlet_id\n", "        LEFT JOIN\n", "            item_details\n", "                AS id\n", "                ON b.item_id = id.item_id\n", "        LEFT JOIN \n", "            sto\n", "                AS s\n", "                ON b.item_id = s.item_id\n", "                AND b.fe_outlet_id = s.outlet_id\n", "                and b.insert_ds_ist = s.for_date\n", "\n", "        WHERE\n", "        (sto_qty_created > 0 or  billed_sto_quantity > 0 or dispatch_qty > 0 or \n", "                        grn_quantity > 0 or pending_grn_quantity > 0 or dn_quantity > 0 or open_sto_quantity > 0)\n", "\n", "        group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15\n", "\n", "        \"\"\"\n", "# , CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "98b77cbd-47e5-4e41-a925-f9f199e06447", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0c0d7b08-d140-4fac-8932-a15b5347cda4", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df[['insert_ds_ist','updated_at_ist','sale_start','sale_end','cutt_off']] = festive_metrics_df[['insert_ds_ist','updated_at_ist','sale_start','sale_end','cutt_off']].astype('datetime64[ns]')\n", "# festive_metrics_df[['fe_inv','po_qty_created','scheduled_po_qty','unscheduled_po_qty','po_grn_qty','po_remaining_qty','t_scheduled_quantity','t1_scheduled_quantity','t2_scheduled_quantity','t3_scheduled_quantity']] = festive_metrics_df[['fe_inv','po_qty_created','scheduled_po_qty','unscheduled_po_qty','po_grn_qty','po_remaining_qty','t_scheduled_quantity','t1_scheduled_quantity','t2_scheduled_quantity','t3_scheduled_quantity']].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "eaa9d2cc-879b-4fca-9c11-e2924bd614b9", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "f87fed17-ac34-4c98-add2-b0c1c4ef7496", "metadata": {}, "outputs": [], "source": ["# column_dtypes = [\n", "#     {\n", "#         \"name\": col,\n", "#         \"type\": str(festive_metrics_df[col].dtype).replace(\"int64\", \"integer\").replace(\"float64\", \"real\").replace(\"str\",\"varchar\").replace(\"datetime64[ns]\",\"date\").replace(\"object\",\"varchar\"),\n", "#         \"description\": \"sample description\"\n", "#     } for col in festive_metrics_df.columns\n", "# ]\n", "\n", "# column_dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "5520c15c-0ed7-465f-8c17-ea466a656c1f", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l1_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l2_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l0_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l1_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l2_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"fastival_tag\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"core_bau_flag\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"cpd\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"be_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"fe_city_name\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"fe_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"fe_facility_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"sto_qty_created\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"billed_sto_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"dispatch_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"grn_quantity\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"pending_grn_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"dn_quantity\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"open_sto_quantity\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"bill_fillrate\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"dispatch_fillrate\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"gmv_sto_qty_created\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"sku_count_sto_qty_created\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"gmv_billed_sto_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"sku_count_billed_sto_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"gmv_dispatch_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"sku_count_dispatch_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"gmv_grn_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"sku_count_grn_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"gmv_pending_grn\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"sku_count_pending_grn\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"gmv_total_dn_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"sku_count_total_dn_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"gmv_open_sto_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"sku_count_open_sto_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "1ecb3a26-ad62-40f4-b1c6-dc25cf32f750", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festive_metrics_temp_viz_open_billed_dispatch_sto\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"insert_ds_ist\",\n", "        \"be_facility_id\",\n", "        \"be_facility_name\",\n", "        \"fe_facility_id\",\n", "        \"fe_facility_name\",\n", "        \"fe_city_name\",\n", "        \"p_type\",\n", "        \"l0_id\",\n", "        \"l0_category\",\n", "        \"l1_id\",\n", "        \"l1_category\",\n", "        \"l2_id\",\n", "        \"l2_category\",\n", "        \"fastival_tag\",\n", "        \"core_bau_flag\",\n", "    ],\n", "    \"partition key\": \"insert_ds_ist\",\n", "    \"incremental_key\": \"insert_ds_ist\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive metrics\",\n", "}\n", "\n", "to_trino(festive_metrics_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}