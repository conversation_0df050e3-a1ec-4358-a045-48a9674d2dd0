{"cells": [{"cell_type": "code", "execution_count": null, "id": "e4ff2d28-006b-4265-9c3e-5e2e4ca777f1", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "b92f2e94-9e37-46b1-aafe-b99fb8de895c", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "f43e55d0-bbaf-44d4-aa15-bea3f1e4f48a", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "78617774-ea1f-44c5-93b9-89d68c54a7d2", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "8c0ec028-07f1-4399-85f4-8860e586ef34", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df = read_sql_query(f\"\"\"\n", "festive_metrics_query = f\"\"\"      \n", "SELECT insert_ds_ist,\n", "       be_facility_name,\n", "       fe_city_name,\n", "       fe_facility_name,\n", "       be_facility_id,\n", "       fe_facility_id,\n", "       fastival_tag,\n", "       core_bau_flag,\n", "       be_avail_flag,\n", "       fe_avail_flag,\n", "       item_id,\n", "       item_substate,\n", "       item_name,\n", "       p_type,\n", "       l0_category,\n", "       l1_category,\n", "       l2_category,\n", "       l0_id,\n", "       l1_id,\n", "       l2_id,\n", "       sum(coalesce(be_inv,0)) AS be_inv,\n", "       sum(coalesce(fe_inv,0)) AS fe_inv,\n", "       SUM(billed_qty) AS billed_qty,\n", "       SUM(v1) AS v1,\n", "       SUM(v2) AS v2,\n", "       SUM(picking_capacity_quantity_drop) AS picking_capacity_quantity_drop,\n", "       SUM(picking_capacity_sku_drop) AS picking_capacity_sku_drop,\n", "       SUM(truck_load_drop) AS truck_load_drop,\n", "       SUM(inward_drop) AS inward_drop,\n", "       SUM(storage_drop) AS storage_drop,\n", "       count(DISTINCT CASE\n", "                          WHEN v1>0 THEN item_id\n", "                          ELSE NULL\n", "                      END) AS v1_lines,\n", "       count(DISTINCT CASE\n", "                          WHEN v2>0 THEN item_id\n", "                          ELSE NULL\n", "                      END) AS v2_lines,\n", "       coalesce(abs(sum(v2) - sum(v1)),0) AS quantity_drop,\n", "       coalesce(abs(sum(sto_cpd)),0) AS sto_cpd,\n", "       coalesce(abs(sum(sto_cpd*(gmv*1.00/qty_sold))),0) AS gmv_drop\n", "FROM supply_etls.inventory_replenishment_metrics\n", "WHERE (fe_inv*1.00/nullif(sto_cpd,0)) = 0\n", "  AND hour_ = (select max(hour_) from supply_etls.inventory_replenishment_metrics where insert_ds_ist = cast(current_timestamp - interval '55' minute as date))\n", "  AND insert_ds_ist = cast(CURRENT_TIMESTAMP - interval '55' MINUTE AS date)\n", "  AND be_avail_flag=1\n", "  AND fe_avail_flag=0\n", "  AND (v1 = 0\n", "       OR v1 IS NULL)\n", "GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20\n", "        \"\"\"\n", "# , CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "3fc58a89-dc66-4404-8d67-9bc1878c117a", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a571635a-c79d-414e-8af3-ce4c1940d339", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df[['insert_ds_ist','updated_at_ist','sale_start','sale_end','cutt_off']] = festive_metrics_df[['insert_ds_ist','updated_at_ist','sale_start','sale_end','cutt_off']].astype('datetime64[ns]')\n", "# festive_metrics_df[['fe_inv','po_qty_created','scheduled_po_qty','unscheduled_po_qty','po_grn_qty','po_remaining_qty','t_scheduled_quantity','t1_scheduled_quantity','t2_scheduled_quantity','t3_scheduled_quantity']] = festive_metrics_df[['fe_inv','po_qty_created','scheduled_po_qty','unscheduled_po_qty','po_grn_qty','po_remaining_qty','t_scheduled_quantity','t1_scheduled_quantity','t2_scheduled_quantity','t3_scheduled_quantity']].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "5fa62a2f-c32c-40a7-8c87-85478770a83b", "metadata": {}, "outputs": [], "source": ["# column_dtypes = [\n", "#     {\n", "#         \"name\": col,\n", "#         \"type\": str(festive_metrics_df[col].dtype).replace(\"int64\", \"integer\").replace(\"float64\", \"real\").replace(\"str\",\"varchar\").replace(\"datetime64[ns]\",\"date\").replace(\"object\",\"varchar\"),\n", "#         \"description\": \"sample description\"\n", "#     } for col in festive_metrics_df.columns\n", "# ]\n", "\n", "# column_dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "23439f81-3871-452b-aedf-a16a628d99eb", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"be_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"fe_city_name\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"fe_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"fe_facility_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"fastival_tag\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"core_bau_flag\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"be_avail_flag\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"fe_avail_flag\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"item_substate\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l1_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l2_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l0_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l1_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l2_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"be_inv\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"fe_inv\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"billed_qty\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"v1\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"v2\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"picking_capacity_quantity_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"picking_capacity_sku_drop\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"truck_load_drop\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"inward_drop\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"storage_drop\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"v1_lines\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"v2_lines\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"quantity_drop\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"sto_cpd\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"gmv_drop\", \"type\": \"real\", \"description\": \"sample description\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "51362eda-25a9-484e-b85e-d6fed1d6c376", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festive_metrics_temp_viz_truncation_v1_not_created\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"insert_ds_ist\",\n", "        \"be_facility_id\",\n", "        \"be_facility_name\",\n", "        \"fe_facility_id\",\n", "        \"fe_facility_name\",\n", "        \"fe_city_name\",\n", "        \"p_type\",\n", "        \"l0_id\",\n", "        \"l0_category\",\n", "        \"l1_id\",\n", "        \"l1_category\",\n", "        \"l2_id\",\n", "        \"l2_category\",\n", "        \"fastival_tag\",\n", "        \"core_bau_flag\",\n", "        \"item_id\",\n", "        \"item_substate\",\n", "        \"item_name\",\n", "        \"be_avail_flag\",\n", "        \"fe_avail_flag\",\n", "    ],\n", "    \"partition key\": \"insert_ds_ist\",\n", "    \"incremental_key\": \"insert_ds_ist\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive metrics\",\n", "}\n", "\n", "to_trino(festive_metrics_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "6441829a-a733-49fe-8d41-5d8a2bea6d38", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}