{"cells": [{"cell_type": "code", "execution_count": null, "id": "09e5dcfe-c40f-4306-9d14-c04496264466", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "5589d319-023d-412b-9c2a-24c1e536df4e", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "67897012-b098-483b-bc62-909afe44ff12", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "a7e378f5-a242-449e-a3af-7d28c07bb578", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "3e3ab264-c1bb-4c86-aa2e-fb3e0ec7be0d", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df = read_sql_query(f\"\"\"\n", "festive_metrics_query = f\"\"\"\n", "with outlet_details as (\n", "    select \n", "        city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "        inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id, taggings \n", "    from supply_etls.outlet_details\n", "    where ars_check=1\n", "),\n", "\n", "item_details as (\n", "    select  \n", "        item_id, item_name, l0_id, l0_category, \n", "        l1_id, l1_category, l2_id, l2_category, p_type, assortment_type \n", "    from supply_etls.item_details\n", "),\n", "\n", "tea_tagging as (\n", "    select\n", "        fe_outlet_id, fe_facility_id, item_id, assortment_type, \n", "        be_hot_outlet_id, be_inv_outlet_id, be_facility_id, assortment_status_id\n", "    from supply_etls.inventory_metrics_tea_tagging\n", "    where flag = 'correct_tea' and be_facility_id <> fe_facility_id\n", "),\n", "\n", "po_ordering_cpd_fe as (\n", "    select \n", "        item_id, \n", "        outlet_id, cpd\n", "    from ars.default_cpd\n", "    where active and lake_active_record\n", "),\n", "\n", "po_ordering_cpd_be as (\n", "    select \n", "        tea.item_id, tea.be_facility_id, \n", "        tea.be_inv_outlet_id, tea.be_hot_outlet_id, sum(cpd) as po_cpd\n", "    from tea_tagging tea \n", "    join po_ordering_cpd_fe cpd_fe on cpd_fe.item_id=tea.item_id and cpd_fe.outlet_id=tea.fe_outlet_id\n", "    group by 1,2,3,4\n", "),\n", "\n", "sto_transfer_cpd as (\n", "    select \n", "        for_date, item_id, outlet_id, \n", "        cpd, aps_adjusted\n", "    from ars.outlet_item_aps_derived_cpd\n", "    where insert_ds_ist >= cast((current_date - interval '7' day) as varchar)\n", "        and lake_active_record\n", "        and for_date >= current_date - interval '7' day\n", "),\n", "\n", "sto_transfer_cpd_be as (\n", "    select \n", "        for_date, be_facility_id, be_inv_outlet_id, be_hot_outlet_id,\n", "        cpd_fe.item_id, sum(coalesce(cpd,0)) as sto_cpd\n", "    from tea_tagging tea \n", "    join sto_transfer_cpd cpd_fe on cpd_fe.item_id=tea.item_id and cpd_fe.outlet_id=tea.fe_outlet_id\n", "    group by 1,2,3,4,5\n", "),\n", "\n", "po_fillrates_base as(\n", "    select \n", "        impm.insert_ds_ist, impm.updated_at_ist, impm.po_schedule_at_ist, impm.po_id, impm.facility_id, impm.item_id, impm.po_number,\n", "        impm.po_quantity, impm.grn_quantity, impm.remaining_po_quantity, impm.po_fill_rate, impm.po_expiry_at_ist, impm.is_multiple_grn,\n", "        impm.po_created_at_ist, impm.po_landing_price, impm.po_type_id, impm.po_to_be_consider, impm.po_status, impm.po_extension_count,\n", "        ut.reference_number, ut.insert_ds_ist as landing_date, \n", "        ut.created_at + interval '330' minute as po_landed_at_ist, \n", "        case when impm.is_multiple_grn = 0\n", "                then coalesce(cast(ut.insert_ds_ist as date), date(po_expiry_at_ist))\n", "             when impm.is_multiple_grn = 1\n", "                then date(po_expiry_at_ist)\n", "        end as land_expiry_date,\n", "        row_number() over (partition by item_id, facility_id order by po_number desc) as po_rn \n", "    from supply_etls.inventory_metrics_purchase_mis impm\n", "    left join (select reference_number, max(insert_ds_ist) as insert_ds_ist, max(created_at) as created_at\n", "                from wms.unloading_task\n", "                where insert_ds_ist >= cast(current_date - interval '10' day as varchar)\n", "                group by 1) ut\n", "            on ut.reference_number = impm.po_number\n", "    where impm.insert_ds_ist > current_date - interval '100' day\n", "),\n", "\n", "po_fillrates as(\n", "    select *\n", "    from po_fillrates_base pfb\n", "    where (reference_number is not null\n", "        or po_status = 'Expired'\n", "        or po_status = 'Fulfilled')\n", "        and land_expiry_date >= current_date - interval '10' day \n", "),\n", "\n", "festive_details as (\n", "            \n", "    select distinct\n", "        be_facility_id as facility_id, \n", "        item_id,\n", "        festival_name as festival,\n", "        assortment_reason_type as festival_bau,\n", "        sale_start_date as sale_start, \n", "        sale_end_date as sale_end\n", "    from ars_etls.final_festival_planning\n", "    where festival_name is not null\n", "\n", "),    \n", "\n", "final_base as(\n", "    select \n", "        pofr.insert_ds_ist as po_created_at_ist, \n", "        po_landed_at_ist, \n", "        land_expiry_date,\n", "        date(pofr.po_schedule_at_ist) as po_schedule_at_ist, \n", "        po_id, pofr.item_id, pofr.facility_id, od.facility_name, --pos.fe_city_name, \n", "        p_type, l0_id, l0_category, l1_id, l1_category, l2_id, l2_category,\n", "        (CASE WHEN fd.festival IS NOT NULL then fd.festival ELSE 'BAU' END) fastival_tag, \n", "        (CASE WHEN fd.festival IS NOT NULL then fd.festival_bau ELSE 'BAU' END) core_bau_flag,\n", "        coalesce(sum(pocpd.po_cpd),0) as po_cpd,\n", "        coalesce(sum(scpd.sto_cpd),0) as sto_cpd,\n", "        \n", "        coalesce(sum(\n", "            case when ((po_landed_at_ist >= cast(current_date - interval '7' day as timestamp)\n", "                        and po_landed_at_ist < cast(current_date - interval '1' day as timestamp))\n", "                    or (po_expiry_at_ist >= cast(current_date - interval '7' day as timestamp)\n", "                        and po_expiry_at_ist< cast(current_date - interval '1' day as timestamp)))\n", "                    and is_multiple_grn = 0\n", "                then pofr.po_quantity\n", "                when (po_expiry_at_ist >= cast(current_date - interval '7' day as timestamp)\n", "                        and po_expiry_at_ist< cast(current_date - interval '1' day as timestamp))\n", "                    and is_multiple_grn = 1\n", "                then pofr.po_quantity\n", "            end),0) as po_quantity,\n", "            \n", "        coalesce(sum(\n", "            case when ((po_landed_at_ist >= cast(current_date - interval '7' day as timestamp)\n", "                        and po_landed_at_ist < cast(current_date - interval '1' day as timestamp))\n", "                    or (po_expiry_at_ist >= cast(current_date - interval '7' day as timestamp)\n", "                        and po_expiry_at_ist< cast(current_date - interval '1' day as timestamp)))\n", "                    and is_multiple_grn = 0\n", "                then pofr.grn_quantity\n", "                when (po_expiry_at_ist >= cast(current_date - interval '7' day as timestamp)\n", "                        and po_expiry_at_ist< cast(current_date - interval '1' day as timestamp))\n", "                    and is_multiple_grn = 1\n", "                then pofr.grn_quantity\n", "            end),0) as grn_quantity,\n", "            \n", "        coalesce(sum(\n", "            case when ((po_landed_at_ist >= cast(current_date - interval '7' day as timestamp)\n", "                        and po_landed_at_ist < cast(current_date - interval '1' day as timestamp))\n", "                    or (po_expiry_at_ist >= cast(current_date - interval '7' day as timestamp)\n", "                        and po_expiry_at_ist< cast(current_date - interval '1' day as timestamp)))\n", "                    and is_multiple_grn = 0\n", "                then pofr.remaining_po_quantity\n", "                when (po_expiry_at_ist >= cast(current_date - interval '7' day as timestamp)\n", "                        and po_expiry_at_ist< cast(current_date - interval '1' day as timestamp))\n", "                    and is_multiple_grn = 1\n", "                then pofr.remaining_po_quantity\n", "            end),0) as remaining_po_quantity,\n", "            \n", "        cast(coalesce(sum(\n", "            case when ((po_landed_at_ist >= cast(current_date - interval '7' day as timestamp)\n", "                        and po_landed_at_ist < cast(current_date - interval '1' day as timestamp))\n", "                    or (po_expiry_at_ist >= cast(current_date - interval '7' day as timestamp)\n", "                        and po_expiry_at_ist< cast(current_date - interval '1' day as timestamp)))\n", "                    and is_multiple_grn = 0\n", "                then pofr.grn_quantity\n", "                when (po_expiry_at_ist >= cast(current_date - interval '7' day as timestamp)\n", "                        and po_expiry_at_ist< cast(current_date - interval '1' day as timestamp))\n", "                    and is_multiple_grn = 1\n", "                then pofr.grn_quantity\n", "            end),0) as decimal(10,2)) /\n", "                nullif(coalesce(sum(\n", "            case when ((po_landed_at_ist >= cast(current_date - interval '7' day as timestamp)\n", "                        and po_landed_at_ist < cast(current_date - interval '1' day as timestamp))\n", "                    or (po_expiry_at_ist >= cast(current_date - interval '7' day as timestamp)\n", "                        and po_expiry_at_ist< cast(current_date - interval '1' day as timestamp)))\n", "                    and is_multiple_grn = 0\n", "                then pofr.grn_quantity\n", "                when (po_expiry_at_ist >= cast(current_date - interval '7' day as timestamp)\n", "                        and po_expiry_at_ist< cast(current_date - interval '1' day as timestamp))\n", "                    and is_multiple_grn = 1\n", "                then pofr.grn_quantity\n", "            end),0),0) as po_fill_rate,\n", "            \n", "        \n", "        \n", "        case when date_format(po_schedule_at_ist, '%%Y.%%m.%%d') <> date_format(po_landed_at_ist, '%%Y.%%m.%%d') or po_schedule_at_ist is null then 'unscheduled' else 'scheduled' end as po_schedule\n", "    from po_fillrates pofr\n", "    left join po_ordering_cpd_be pocpd on pocpd.item_id=pofr.item_id and pocpd.be_facility_id=pofr.facility_id\n", "    left join sto_transfer_cpd_be scpd on scpd.be_facility_id=pofr.facility_id and scpd.for_date=pofr.land_expiry_date and scpd.item_id=pofr.item_id\n", "    join outlet_details od on od.facility_id = pofr.facility_id \n", "    join item_details id on id.item_id = pofr.item_id\n", "    left join festive_details AS fd\n", "        on pofr.facility_id = fd.facility_id\n", "        and pofr.item_id = fd.item_id\n", "        and ((pofr.po_landed_at_ist BETWEEN sale_start - INTERVAL '10' DAY AND sale_end)\n", "            or (pofr.po_expiry_at_ist BETWEEN sale_start - INTERVAL '10' DAY AND sale_end))\n", "    group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,24\n", ")\n", "\n", "select * from final_base \n", "        \"\"\"\n", "# , CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "58e2f3af-f417-4f7a-8981-4d9dcff9c6d1", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "cf6d9fd0-0dc6-46a0-a731-422155e0bb51", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df[['insert_ds_ist','updated_at_ist','sale_start','sale_end','cutt_off']] = festive_metrics_df[['insert_ds_ist','updated_at_ist','sale_start','sale_end','cutt_off']].astype('datetime64[ns]')\n", "# festive_metrics_df[['fe_inv','po_qty_created','scheduled_po_qty','unscheduled_po_qty','po_grn_qty','po_remaining_qty','t_scheduled_quantity','t1_scheduled_quantity','t2_scheduled_quantity','t3_scheduled_quantity']] = festive_metrics_df[['fe_inv','po_qty_created','scheduled_po_qty','unscheduled_po_qty','po_grn_qty','po_remaining_qty','t_scheduled_quantity','t1_scheduled_quantity','t2_scheduled_quantity','t3_scheduled_quantity']].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "b7a57344-bada-40c3-b035-5d48e68c4a03", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "6d0c39fa-99fe-4857-b256-a921247fba33", "metadata": {}, "outputs": [], "source": ["# column_dtypes = [\n", "#     {\n", "#         \"name\": col,\n", "#         \"type\": str(festive_metrics_df[col].dtype).replace(\"int64\", \"integer\").replace(\"float64\", \"real\").replace(\"str\",\"varchar\").replace(\"datetime64[ns]\",\"date\").replace(\"object\",\"varchar\"),\n", "#         \"description\": \"sample description\"\n", "#     } for col in festive_metrics_df.columns\n", "# ]\n", "\n", "# column_dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "1d1a8b31-0a19-4a8c-937d-13d528c05686", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"po_created_at_ist\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"land_expiry_date\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"po_landed_at_ist\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"po_schedule_at_ist\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"po_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"facility_name\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l0_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l1_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l1_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l2_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l2_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"fastival_tag\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"core_bau_flag\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"po_cpd\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"sto_cpd\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"po_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"grn_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"remaining_po_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"po_fill_rate\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"po_schedule\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "12c20aeb-d44c-4523-a6eb-04675666ce46", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festive_metrics_temp_viz_po_fill_rates\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"po_created_at_ist\",\n", "        \"land_expiry_date\",\n", "        \"po_landed_at_ist\",\n", "        \"po_schedule_at_ist\",\n", "        \"po_id\",\n", "        \"item_id\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"p_type\",\n", "        \"l0_id\",\n", "        \"l0_category\",\n", "        \"l1_id\",\n", "        \"l1_category\",\n", "        \"l2_id\",\n", "        \"l2_category\",\n", "        \"fastival_tag\",\n", "        \"core_bau_flag\",\n", "    ],\n", "    \"partition key\": [\"land_expiry_date\"],\n", "    \"incremental_key\": \"land_expiry_date\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive metrics\",\n", "}\n", "\n", "to_trino(festive_metrics_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "87351683-2e27-49cb-bb43-9d19aec3ea28", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}