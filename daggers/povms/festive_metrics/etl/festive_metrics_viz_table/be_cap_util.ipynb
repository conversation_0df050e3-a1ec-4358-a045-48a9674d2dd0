{"cells": [{"cell_type": "code", "execution_count": null, "id": "cc2a3835-1bc6-43cf-b28c-fafe81e4038f", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e717d3fd-582e-49e1-9235-1d7852b43887", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "57bdc0aa-5ff7-4153-a57b-6c5af2c836c8", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "d41b0124-d898-4660-9eee-ea465f506abb", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "6b0b952e-ceff-4cba-909d-e0dd4e694e0d", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df = read_sql_query(f\"\"\"\n", "festive_metrics_query = f\"\"\"\n", "\n", "WITH packaged_goods_outlet AS (\n", "SELECT  facility_id, facility_name, inv_outlet_id, hot_outlet_id, city_name\n", "FROM    supply_etls.outlet_details\n", "WHERE   taggings IN ('be', 'fe') AND store_type IN ('Packaged Goods', 'Dark Store') AND ars_active = 1 AND active = 1 \n", "        AND ars_check = 1 AND grocery_active_count > 0 ),\n", "\n", "pg_items as (\n", "select item_id from supply_etls.item_details\n", "where handling_type = 'Non Packaging Material' and assortment_type = 'Packaged Goods' ),\n", "\n", "valid_sto AS (\n", "SELECT  sto_id FROM po.sto_state_transition_log \n", "WHERE   sto_state_id != 7 AND insert_ds_ist >= CAST((CURRENT_DATE - INTERVAL '7' DAY) AS VARCHAR)\n", "        AND lake_active_record = true\n", "GROUP BY 1 ),\n", "\n", "pg_runs AS (\n", "SELECT  run_id FROM ars.job_run\n", "WHERE   lake_active_record = true AND success = 1 AND simulation_params LIKE '%%ars_lite%%'\n", "        AND simulation_params not like '%%mode%%' AND simulation_params not like '%%hyperpure_run%%'\n", "        AND simulation_params not like '%%test%%' AND simulation_params not like '%%type%%' \n", "        AND simulation_params not like '%%any_day_po%%' AND simulation_params not like '%%spr_migration_simulation%%' \n", "        AND started_at >= (CAST((CURRENT_DATE - INTERVAL '7' DAY) AS TIMESTAMP) - INTERVAL '330' MINUTE) ),\n", "\n", "sto_data AS (\n", "SELECT      DATE(s.created_at + INTERVAL '330' MINUTE) AS created_date, si.run_id, be.facility_id AS be_facility_id\n", "            , s.frontend_outlet_id, SUM(si.expected_quantity) AS sto, COUNT(DISTINCT si.item_id) AS sto_line_items\n", "\n", "FROM        po.sto s  \n", "INNER JOIN  po.sto_items si\n", "            ON  s.id = si.sto_id AND s.active = 1 AND s.lake_active_record = true AND si.active = 1 \n", "                AND si.lake_active_record = true\n", "                AND s.created_at >= (CAST((CURRENT_DATE - INTERVAL '7' DAY) AS TIMESTAMP) - INTERVAL '330' MINUTE) \n", "                AND si.created_at >= (CAST((CURRENT_DATE - INTERVAL '7' DAY) AS TIMESTAMP) - INTERVAL '330' MINUTE) \n", "INNER JOIN  valid_sto v \n", "            ON v.sto_id = s.id\n", "INNER JOIN  packaged_goods_outlet fe \n", "            ON fe.hot_outlet_id = s.frontend_outlet_id\n", "INNER JOIN  packaged_goods_outlet be \n", "            ON be.inv_outlet_id = s.outlet_id\n", "INNER JOIN  pg_items i \n", "            ON i.item_id = si.item_id\n", "LEFT JOIN   rpc.item_details id \n", "            ON id.item_id = si.item_id AND id.lake_active_record = true \n", "GROUP BY    1,2,3,4 ),\n", "\n", "cap_util AS (\n", "-- ars sto raise\n", "SELECT  DATE(b.insert_ds_ist) AS insert_ds_ist, 'Auto' AS run_type, CAST(split(b.shift_key, '_')[1] AS DOUBLE) AS be_facility_id\n", "        , COALESCE(MAX(CASE WHEN b.current_slot != '99:99' THEN b.picking_capacity_total END),0) AS picking_qty_cap\n", "        , COALESCE(MAX(CASE WHEN b.current_slot != '99:99' THEN b.picking_sku_total END),0) AS picking_sku_cap\n", "        , SUM(s.sto) AS picking_qty_util, SUM(s.sto_line_items) AS picking_sku_util\n", "\n", "FROM        ars.backend_facility_transfer_constraints b\n", "INNER JOIN  pg_runs r \n", "        ON  r.run_id = b.run_id AND b.lake_active_record = true AND b.insert_ds_ist >= CAST((CURRENT_DATE - INTERVAL '7' DAY) AS VARCHAR) \n", "INNER JOIN  sto_data s \n", "            ON  r.run_id = s.run_id \n", "GROUP BY    1,2,3 \n", "\n", "UNION ALL\n", "\n", "-- manual sto raise\n", "SELECT  created_date, 'Manual' AS run_type, be_facility_id, SUM(0) AS picking_qty_cap, SUM(0) AS picking_sku_cap\n", "        , SUM(sto) AS picking_qty_util, SUM(sto_line_items) AS picking_sku_util\n", "FROM    sto_data\n", "WHERE   run_id = '0' \n", "GROUP BY    1,2,3 )\n", "\n", "SELECT  cu.insert_ds_ist, cu.be_facility_id, be.facility_name AS be_facility_name\n", "        , COALESCE(SUM(cu.picking_qty_cap),0) AS picking_qty_cap, COALESCE(SUM(cu.picking_sku_cap),0) AS picking_sku_cap\n", "        , COALESCE(SUM(cu.picking_qty_util),0) AS picking_qty_util, COALESCE(SUM(cu.picking_sku_util),0) AS picking_sku_util\n", "        ,   CAST((CASE \n", "                WHEN (SUM(cu.picking_qty_cap) = 0 OR SUM(cu.picking_qty_cap) IS NULL) THEN 0 \n", "                ELSE ((100.00 * SUM(cu.picking_qty_util)) / SUM(cu.picking_qty_cap) )\n", "            END ) AS DOUBLE) AS picking_qty_util_per\n", "        ,   CAST((CASE \n", "                WHEN (SUM(cu.picking_sku_cap) = 0 OR SUM(cu.picking_sku_cap) IS NULL) THEN 0 \n", "                ELSE ((100.00 * SUM(cu.picking_sku_util)) / SUM(cu.picking_sku_cap) )\n", "            END ) AS DOUBLE) AS picking_sku_util_per\n", "\n", "FROM        cap_util cu\n", "LEFT JOIN   packaged_goods_outlet be ON be.facility_id = cu.be_facility_id\n", "GROUP BY    1,2,3\n", "\n", "\"\"\"\n", "# , CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "e1c2ba79-6204-44a9-8076-a1a65f180c27", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "98b77cbd-47e5-4e41-a925-f9f199e06447", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0c0d7b08-d140-4fac-8932-a15b5347cda4", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df[['insert_ds_ist','updated_at_ist','sale_start','sale_end','cutt_off']] = festive_metrics_df[['insert_ds_ist','updated_at_ist','sale_start','sale_end','cutt_off']].astype('datetime64[ns]')\n", "# festive_metrics_df[['fe_inv','po_qty_created','scheduled_po_qty','unscheduled_po_qty','po_grn_qty','po_remaining_qty','t_scheduled_quantity','t1_scheduled_quantity','t2_scheduled_quantity','t3_scheduled_quantity']] = festive_metrics_df[['fe_inv','po_qty_created','scheduled_po_qty','unscheduled_po_qty','po_grn_qty','po_remaining_qty','t_scheduled_quantity','t1_scheduled_quantity','t2_scheduled_quantity','t3_scheduled_quantity']].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "eaa9d2cc-879b-4fca-9c11-e2924bd614b9", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "f87fed17-ac34-4c98-add2-b0c1c4ef7496", "metadata": {}, "outputs": [], "source": ["# column_dtypes = [\n", "#     {\n", "#         \"name\": col,\n", "#         \"type\": str(festive_metrics_df[col].dtype).replace(\"int64\", \"integer\").replace(\"float64\", \"real\").replace(\"str\",\"varchar\").replace(\"datetime64[ns]\",\"date\").replace(\"object\",\"varchar\"),\n", "#         \"description\": \"sample description\"\n", "#     } for col in festive_metrics_df.columns\n", "# ]\n", "\n", "# column_dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "5520c15c-0ed7-465f-8c17-ea466a656c1f", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"be_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"picking_qty_util\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"picking_qty_cap\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"picking_qty_util_per\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"picking_sku_util\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"picking_sku_cap\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"picking_sku_util_per\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "1ecb3a26-ad62-40f4-b1c6-dc25cf32f750", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festive_metrics_temp_viz_be_cap_util\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"insert_ds_ist\",\n", "        \"be_facility_id\",\n", "        \"be_facility_name\",\n", "    ],\n", "    \"partition key\": \"insert_ds_ist\",\n", "    \"incremental_key\": \"insert_ds_ist\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive metrics\",\n", "}\n", "\n", "to_trino(festive_metrics_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}