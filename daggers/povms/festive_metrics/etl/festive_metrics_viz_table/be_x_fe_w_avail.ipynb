{"cells": [{"cell_type": "code", "execution_count": null, "id": "b6561d8e-fb54-4b70-bd13-e06a7e6c75da", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "019834a2-7bc0-4b1e-a659-d3a20810f113", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "a1626543-8e30-406e-98c8-f16517554af5", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "dc979789-d942-4bf4-bc22-135ecc7d1957", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "b7960e9c-c7c4-4d0b-b713-1e5291015072", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"be_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"assortment\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"express_longtail\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"current_backend_weight_avail\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"current_backend_binary_avail\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"backend_weight_avail\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"backend_binary_avail\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "187d360c-d235-43ca-bc42-a6488e53ec84", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"backend_x_frontend_weighted_availability\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"insert_ds_ist\",\n", "        \"be_facility_id\",\n", "        \"be_facility_name\",\n", "        \"assortment\",\n", "        \"express_longtail\",\n", "    ],\n", "    \"partition key\": \"insert_ds_ist\",\n", "    \"incremental_key\": \"insert_ds_ist\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains backend x frontend weighted availability \",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "eaa72e8f-ded4-48ab-99ef-8f56e3c9d9c8", "metadata": {}, "outputs": [], "source": ["be_avail = f\"\"\" \n", "\n", "with city_hour_weights as (\n", "    select city, order_hour, cast(max_by(weights,updated_at) as real) * 1.00000000000 as ch_weights\n", "    from supply_etls.city_hour_weights\n", "    group by 1,2\n", "),\n", "city_item_weights as (\n", "    select city, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.00000000000 as ci_weights\n", "    from supply_etls.city_item_weights\n", "    group by 1,2,3\n", "),\n", "city_store_weights as (\n", "    select city, facility_id, outlet_id, cast(max_by(store_weight,updated_at) as real) * 1.00000000000 as cs_weights\n", "    from supply_etls.city_store_weights\n", "    group by 1,2,3\n", "),\n", "city_weights as (\n", "    select city, cast(max_by(weight,updated_at) as real) * 1.00000000000 as city_weights\n", "    from supply_etls.city_weights\n", "    group by 1\n", "),\n", "backend_hour_weights as (\n", "    select cast(cast(backend_facility_id as real) as int) backend_facility_id, order_hour, cast(max_by(weights,updated_at) as real) * 1.00000000000 as bh_weights\n", "    from supply_etls.backend_hour_weights\n", "    group by 1,2\n", "),\n", "backend_item_weights as (\n", "    select backend_facility_id, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.00000000000 as bi_weights\n", "    from supply_etls.backend_item_weights\n", "    group by 1,2,3\n", "),\n", "\n", "backend_store_weights as (\n", "    select cast(backend_facility_id as int) backend_facility_id, facility_id, outlet_id, cast(max_by(store_weight,updated_at) as real) * 1.00000000000 as bs_weights\n", "    from supply_etls.backend_store_weights\n", "    group by 1,2,3\n", "),\n", "\n", "fmt as (\n", "    select insert_ds_ist, hour_, be_facility_id, be_facility_name, fe_inv_outlet_id, fe_facility_id, fe_inv_outlet_name, express_longtail, l0_id, l0_category, l1_id, l1_category,\n", "            l2_id, l2_category, p_type, item_id,\n", "            case when item_substate = 1 then 'active' \n", "                 when item_substate = 2 then 'inactive' \n", "                 when item_substate = 3 then 'temp_inactive' \n", "                 else 'discontinued' end as assortment,\n", "            fe_avail_flag, sto_cpd, be_inv\n", "    from supply_etls.inventory_replenishment_metrics\n", "    where insert_ds_ist = cast(current_timestamp - interval '55' minute as date)\n", "    and active_outlet_flag=1\n", "),\n", "\n", "bebe_req as (\n", "    select insert_ds_ist, hour_, be_facility_id, fe_inv_outlet_id, item_id,\n", "            case when fe_avail_flag = 0 and (2 * sto_cpd) <= 1 then 1\n", "                when fe_avail_flag = 0 and (2 * sto_cpd) > 1 then (2 * sto_cpd)\n", "                else 0\n", "            end as req_inv\n", "    from fmt\n", "),\n", "\n", "bebe_req_core as (\n", "    select insert_ds_ist, hour_, be_facility_id, item_id, sum(req_inv) as req_inv\n", "    from bebe_req\n", "    group by 1,2,3,4\n", "),\n", "\n", "be_be as (\n", "    select fmt.insert_ds_ist, fmt.hour_, fmt.be_facility_id, be_facility_name, fe_inv_outlet_id, fe_inv_outlet_name, express_longtail, l0_id, l0_category, l1_id, l1_category,\n", "            l2_id, l2_category, p_type, fmt.item_id, assortment, fe_avail_flag, sto_cpd, req_inv,\n", "            case when req_inv <= be_inv then 1 else fe_avail_flag end as avail_flag,\n", "            bs_weights, bi_weights, bh_weights\n", "    from fmt\n", "    left join bebe_req_core bc on bc.be_facility_id = fmt.be_facility_id and bc.item_id = fmt.item_id and bc.insert_ds_ist = fmt.insert_ds_ist\n", "            and bc.hour_ = fmt.hour_\n", "    left join backend_hour_weights bh on fmt.be_facility_id = bh.backend_facility_id and fmt.hour_ = bh.order_hour\n", "    left join backend_item_weights bi on (fmt.be_facility_id) = bi.backend_facility_id and fmt.item_id = bi.item_id\n", "    left join backend_store_weights bs on fmt.be_facility_id = bs.backend_facility_id and fmt.fe_facility_id = bs.facility_id\n", "),\n", "\n", "detailed_availability as(\n", "select b.insert_ds_ist, b.be_facility_id, b.be_facility_name,\n", "b.assortment,\n", "b.express_longtail ,\n", "sum(avail_flag*bi_weights*bs_weights*bh_weights)*1.000000/nullif(sum(bi_weights*bs_weights*bh_weights),0) as backend_weight_avail,\n", "SUM(avail_flag) * 1.00000000 / count(avail_flag) AS backend_binary_avail,\n", "sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (avail_flag*bi_weights*bs_weights)*1.000000 END) /\n", "nullif(sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (bi_weights*bs_weights)*1.000000 END),0) AS current_backend_weight_avail,\n", "sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (avail_flag)*1.000000 END) /\n", "nullif(count(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (avail_flag)*1.000000 END),0) AS current_backend_binary_avail\n", "from be_be b\n", "group by 1,2,3,4,5\n", ")\n", "\n", "SELECT *\n", "FROM detailed_availability\n", "\n", "\"\"\"\n", "to_trino(be_avail, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "5d4e2c74-93e7-419d-a85a-0b74e37ed5cd", "metadata": {}, "outputs": [], "source": ["be_avail_overall_one = f\"\"\" \n", "\n", "with city_hour_weights as (\n", "    select city, order_hour, cast(max_by(weights,updated_at) as real) * 1.00000000000 as ch_weights\n", "    from supply_etls.city_hour_weights\n", "    group by 1,2\n", "),\n", "city_item_weights as (\n", "    select city, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.00000000000 as ci_weights\n", "    from supply_etls.city_item_weights\n", "    group by 1,2,3\n", "),\n", "city_store_weights as (\n", "    select city, facility_id, outlet_id, cast(max_by(store_weight,updated_at) as real) * 1.00000000000 as cs_weights\n", "    from supply_etls.city_store_weights\n", "    group by 1,2,3\n", "),\n", "city_weights as (\n", "    select city, cast(max_by(weight,updated_at) as real) * 1.00000000000 as city_weights\n", "    from supply_etls.city_weights\n", "    group by 1\n", "),\n", "backend_hour_weights as (\n", "    select cast(cast(backend_facility_id as real) as int) backend_facility_id, order_hour, cast(max_by(weights,updated_at) as real) * 1.00000000000 as bh_weights\n", "    from supply_etls.backend_hour_weights\n", "    group by 1,2\n", "),\n", "backend_item_weights as (\n", "    select backend_facility_id, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.00000000000 as bi_weights\n", "    from supply_etls.backend_item_weights\n", "    group by 1,2,3\n", "),\n", "\n", "backend_store_weights as (\n", "    select cast(backend_facility_id as int) backend_facility_id, facility_id, outlet_id, cast(max_by(store_weight,updated_at) as real) * 1.00000000000 as bs_weights\n", "    from supply_etls.backend_store_weights\n", "    group by 1,2,3\n", "),\n", "\n", "fmt as (\n", "    select insert_ds_ist, hour_, be_facility_id, be_facility_name, fe_inv_outlet_id, fe_facility_id, fe_inv_outlet_name, express_longtail, l0_id, l0_category, l1_id, l1_category,\n", "            l2_id, l2_category, p_type, item_id,\n", "            case when item_substate = 1 then 'active' \n", "                 when item_substate = 2 then 'inactive' \n", "                 when item_substate = 3 then 'temp_inactive' \n", "                 else 'discontinued' end as assortment,\n", "            fe_avail_flag, sto_cpd, be_inv\n", "    from supply_etls.inventory_replenishment_metrics\n", "    where insert_ds_ist = cast(current_timestamp - interval '55' minute as date)\n", "    and active_outlet_flag=1\n", "),\n", "\n", "bebe_req as (\n", "    select insert_ds_ist, hour_, be_facility_id, fe_inv_outlet_id, item_id,\n", "            case when fe_avail_flag = 0 and (2 * sto_cpd) <= 1 then 1\n", "                when fe_avail_flag = 0 and (2 * sto_cpd) > 1 then (2 * sto_cpd)\n", "                else 0\n", "            end as req_inv\n", "    from fmt\n", "),\n", "\n", "bebe_req_core as (\n", "    select insert_ds_ist, hour_, be_facility_id, item_id, sum(req_inv) as req_inv\n", "    from bebe_req\n", "    group by 1,2,3,4\n", "),\n", "\n", "be_be as (\n", "    select fmt.insert_ds_ist, fmt.hour_, fmt.be_facility_id, be_facility_name, fe_inv_outlet_id, fe_inv_outlet_name, express_longtail, l0_id, l0_category, l1_id, l1_category,\n", "            l2_id, l2_category, p_type, fmt.item_id, assortment, fe_avail_flag, sto_cpd, req_inv,\n", "            case when req_inv <= be_inv then 1 else fe_avail_flag end as avail_flag,\n", "            bs_weights, bi_weights, bh_weights\n", "    from fmt\n", "    left join bebe_req_core bc on bc.be_facility_id = fmt.be_facility_id and bc.item_id = fmt.item_id and bc.insert_ds_ist = fmt.insert_ds_ist\n", "            and bc.hour_ = fmt.hour_\n", "    left join backend_hour_weights bh on fmt.be_facility_id = bh.backend_facility_id and fmt.hour_ = bh.order_hour\n", "    left join backend_item_weights bi on (fmt.be_facility_id) = bi.backend_facility_id and fmt.item_id = bi.item_id\n", "    left join backend_store_weights bs on fmt.be_facility_id = bs.backend_facility_id and fmt.fe_facility_id = bs.facility_id\n", "),\n", "\n", "overall_availability as(\n", "    select b.insert_ds_ist, b.be_facility_id, b.be_facility_name,\n", "    'overall' as assortment,\n", "    'overall' as express_longtail ,\n", "    sum(avail_flag*bi_weights*bs_weights*bh_weights)*1.000000/nullif(sum(bi_weights*bs_weights*bh_weights),0) as backend_weight_avail,\n", "    SUM(avail_flag) * 1.00000000 / count(avail_flag) AS backend_binary_avail,\n", "    sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (avail_flag*bi_weights*bs_weights)*1.000000 END) /\n", "    nullif(sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (bi_weights*bs_weights)*1.000000 END),0) AS current_backend_weight_avail,\n", "    sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (avail_flag)*1.000000 END) /\n", "    nullif(count(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (avail_flag)*1.000000 END),0) AS current_backend_binary_avail\n", "    from be_be b\n", "    where b.assortment NOT IN ('inactive','discontinued')\n", "    group by 1,2,3,4,5\n", ")\n", "\n", "SELECT *\n", "FROM overall_availability\n", "\n", "\"\"\"\n", "to_trino(be_avail_overall_one, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "200be261-c894-4d17-b469-5d6611caf9e4", "metadata": {}, "outputs": [], "source": ["be_avail_overall_two = f\"\"\" \n", "\n", "with city_hour_weights as (\n", "    select city, order_hour, cast(max_by(weights,updated_at) as real) * 1.00000000000 as ch_weights\n", "    from supply_etls.city_hour_weights\n", "    group by 1,2\n", "),\n", "city_item_weights as (\n", "    select city, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.00000000000 as ci_weights\n", "    from supply_etls.city_item_weights\n", "    group by 1,2,3\n", "),\n", "city_store_weights as (\n", "    select city, facility_id, outlet_id, cast(max_by(store_weight,updated_at) as real) * 1.00000000000 as cs_weights\n", "    from supply_etls.city_store_weights\n", "    group by 1,2,3\n", "),\n", "city_weights as (\n", "    select city, cast(max_by(weight,updated_at) as real) * 1.00000000000 as city_weights\n", "    from supply_etls.city_weights\n", "    group by 1\n", "),\n", "backend_hour_weights as (\n", "    select cast(cast(backend_facility_id as real) as int) backend_facility_id, order_hour, cast(max_by(weights,updated_at) as real) * 1.00000000000 as bh_weights\n", "    from supply_etls.backend_hour_weights\n", "    group by 1,2\n", "),\n", "backend_item_weights as (\n", "    select backend_facility_id, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.00000000000 as bi_weights\n", "    from supply_etls.backend_item_weights\n", "    group by 1,2,3\n", "),\n", "\n", "backend_store_weights as (\n", "    select cast(backend_facility_id as int) backend_facility_id, facility_id, outlet_id, cast(max_by(store_weight,updated_at) as real) * 1.00000000000 as bs_weights\n", "    from supply_etls.backend_store_weights\n", "    group by 1,2,3\n", "),\n", "\n", "fmt as (\n", "    select insert_ds_ist, hour_, be_facility_id, be_facility_name, fe_inv_outlet_id, fe_facility_id, fe_inv_outlet_name, express_longtail, l0_id, l0_category, l1_id, l1_category,\n", "            l2_id, l2_category, p_type, item_id,\n", "            case when item_substate = 1 then 'active' \n", "                 when item_substate = 2 then 'inactive' \n", "                 when item_substate = 3 then 'temp_inactive' \n", "                 else 'discontinued' end as assortment,\n", "            fe_avail_flag, sto_cpd, be_inv\n", "    from supply_etls.inventory_replenishment_metrics\n", "    where insert_ds_ist = cast(current_timestamp - interval '55' minute as date)\n", "    and active_outlet_flag=1\n", "),\n", "\n", "bebe_req as (\n", "    select insert_ds_ist, hour_, be_facility_id, fe_inv_outlet_id, item_id,\n", "            case when fe_avail_flag = 0 and (2 * sto_cpd) <= 1 then 1\n", "                when fe_avail_flag = 0 and (2 * sto_cpd) > 1 then (2 * sto_cpd)\n", "                else 0\n", "            end as req_inv\n", "    from fmt\n", "),\n", "\n", "bebe_req_core as (\n", "    select insert_ds_ist, hour_, be_facility_id, item_id, sum(req_inv) as req_inv\n", "    from bebe_req\n", "    group by 1,2,3,4\n", "),\n", "\n", "be_be as (\n", "    select fmt.insert_ds_ist, fmt.hour_, fmt.be_facility_id, be_facility_name, fe_inv_outlet_id, fe_inv_outlet_name, express_longtail, l0_id, l0_category, l1_id, l1_category,\n", "            l2_id, l2_category, p_type, fmt.item_id, assortment, fe_avail_flag, sto_cpd, req_inv,\n", "            case when req_inv <= be_inv then 1 else fe_avail_flag end as avail_flag,\n", "            bs_weights, bi_weights, bh_weights\n", "    from fmt\n", "    left join bebe_req_core bc on bc.be_facility_id = fmt.be_facility_id and bc.item_id = fmt.item_id and bc.insert_ds_ist = fmt.insert_ds_ist\n", "            and bc.hour_ = fmt.hour_\n", "    left join backend_hour_weights bh on fmt.be_facility_id = bh.backend_facility_id and fmt.hour_ = bh.order_hour\n", "    left join backend_item_weights bi on (fmt.be_facility_id) = bi.backend_facility_id and fmt.item_id = bi.item_id\n", "    left join backend_store_weights bs on fmt.be_facility_id = bs.backend_facility_id and fmt.fe_facility_id = bs.facility_id\n", "),\n", "\n", "overall_availability as(\n", "    select b.insert_ds_ist, b.be_facility_id, b.be_facility_name,\n", "    'overall' as assortment,\n", "    b.express_longtail ,\n", "    sum(avail_flag*bi_weights*bs_weights*bh_weights)*1.000000/nullif(sum(bi_weights*bs_weights*bh_weights),0) as backend_weight_avail,\n", "    SUM(avail_flag) * 1.00000000 / count(avail_flag) AS backend_binary_avail,\n", "    sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (avail_flag*bi_weights*bs_weights)*1.000000 END) /\n", "    nullif(sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (bi_weights*bs_weights)*1.000000 END),0) AS current_backend_weight_avail,\n", "    sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (avail_flag)*1.000000 END) /\n", "    nullif(count(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (avail_flag)*1.000000 END),0) AS current_backend_binary_avail\n", "    from be_be b\n", "    where b.assortment NOT IN ('inactive','discontinued')\n", "    group by 1,2,3,4,5\n", ")\n", "\n", "SELECT *\n", "FROM overall_availability\n", "\n", "\"\"\"\n", "to_trino(be_avail_overall_two, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "4a4a0c86-5de6-4fc4-ac8d-4e7582c10af4", "metadata": {}, "outputs": [], "source": ["be_avail_overall_three = f\"\"\" \n", "\n", "with city_hour_weights as (\n", "    select city, order_hour, cast(max_by(weights,updated_at) as real) * 1.00000000000 as ch_weights\n", "    from supply_etls.city_hour_weights\n", "    group by 1,2\n", "),\n", "city_item_weights as (\n", "    select city, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.00000000000 as ci_weights\n", "    from supply_etls.city_item_weights\n", "    group by 1,2,3\n", "),\n", "city_store_weights as (\n", "    select city, facility_id, outlet_id, cast(max_by(store_weight,updated_at) as real) * 1.00000000000 as cs_weights\n", "    from supply_etls.city_store_weights\n", "    group by 1,2,3\n", "),\n", "city_weights as (\n", "    select city, cast(max_by(weight,updated_at) as real) * 1.00000000000 as city_weights\n", "    from supply_etls.city_weights\n", "    group by 1\n", "),\n", "backend_hour_weights as (\n", "    select cast(cast(backend_facility_id as real) as int) backend_facility_id, order_hour, cast(max_by(weights,updated_at) as real) * 1.00000000000 as bh_weights\n", "    from supply_etls.backend_hour_weights\n", "    group by 1,2\n", "),\n", "backend_item_weights as (\n", "    select backend_facility_id, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.00000000000 as bi_weights\n", "    from supply_etls.backend_item_weights\n", "    group by 1,2,3\n", "),\n", "\n", "backend_store_weights as (\n", "    select cast(backend_facility_id as int) backend_facility_id, facility_id, outlet_id, cast(max_by(store_weight,updated_at) as real) * 1.00000000000 as bs_weights\n", "    from supply_etls.backend_store_weights\n", "    group by 1,2,3\n", "),\n", "\n", "fmt as (\n", "    select insert_ds_ist, hour_, be_facility_id, be_facility_name, fe_inv_outlet_id, fe_facility_id, fe_inv_outlet_name, express_longtail, l0_id, l0_category, l1_id, l1_category,\n", "            l2_id, l2_category, p_type, item_id,\n", "            case when item_substate = 1 then 'active' \n", "                 when item_substate = 2 then 'inactive' \n", "                 when item_substate = 3 then 'temp_inactive' \n", "                 else 'discontinued' end as assortment,\n", "            fe_avail_flag, sto_cpd, be_inv\n", "    from supply_etls.inventory_replenishment_metrics\n", "    where insert_ds_ist = cast(current_timestamp - interval '55' minute as date)\n", "    and active_outlet_flag=1\n", "),\n", "\n", "bebe_req as (\n", "    select insert_ds_ist, hour_, be_facility_id, fe_inv_outlet_id, item_id,\n", "            case when fe_avail_flag = 0 and (2 * sto_cpd) <= 1 then 1\n", "                when fe_avail_flag = 0 and (2 * sto_cpd) > 1 then (2 * sto_cpd)\n", "                else 0\n", "            end as req_inv\n", "    from fmt\n", "),\n", "\n", "bebe_req_core as (\n", "    select insert_ds_ist, hour_, be_facility_id, item_id, sum(req_inv) as req_inv\n", "    from bebe_req\n", "    group by 1,2,3,4\n", "),\n", "\n", "be_be as (\n", "    select fmt.insert_ds_ist, fmt.hour_, fmt.be_facility_id, be_facility_name, fe_inv_outlet_id, fe_inv_outlet_name, express_longtail, l0_id, l0_category, l1_id, l1_category,\n", "            l2_id, l2_category, p_type, fmt.item_id, assortment, fe_avail_flag, sto_cpd, req_inv,\n", "            case when req_inv <= be_inv then 1 else fe_avail_flag end as avail_flag,\n", "            bs_weights, bi_weights, bh_weights\n", "    from fmt\n", "    left join bebe_req_core bc on bc.be_facility_id = fmt.be_facility_id and bc.item_id = fmt.item_id and bc.insert_ds_ist = fmt.insert_ds_ist\n", "            and bc.hour_ = fmt.hour_\n", "    left join backend_hour_weights bh on fmt.be_facility_id = bh.backend_facility_id and fmt.hour_ = bh.order_hour\n", "    left join backend_item_weights bi on (fmt.be_facility_id) = bi.backend_facility_id and fmt.item_id = bi.item_id\n", "    left join backend_store_weights bs on fmt.be_facility_id = bs.backend_facility_id and fmt.fe_facility_id = bs.facility_id\n", "),\n", "\n", "overall_availability as(\n", "    select b.insert_ds_ist, b.be_facility_id, b.be_facility_name,\n", "    b.assortment,\n", "    'overall' as express_longtail ,\n", "    sum(avail_flag*bi_weights*bs_weights*bh_weights)*1.000000/nullif(sum(bi_weights*bs_weights*bh_weights),0) as backend_weight_avail,\n", "    SUM(avail_flag) * 1.00000000 / count(avail_flag) AS backend_binary_avail,\n", "    sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (avail_flag*bi_weights*bs_weights)*1.000000 END) /\n", "    nullif(sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (bi_weights*bs_weights)*1.000000 END),0) AS current_backend_weight_avail,\n", "    sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (avail_flag)*1.000000 END) /\n", "    nullif(count(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (avail_flag)*1.000000 END),0) AS current_backend_binary_avail\n", "    from be_be b\n", "    where b.assortment NOT IN ('inactive','discontinued')\n", "    group by 1,2,3,4,5\n", ")\n", "\n", "SELECT *\n", "FROM overall_availability\n", "\n", "\"\"\"\n", "to_trino(be_avail_overall_three, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}