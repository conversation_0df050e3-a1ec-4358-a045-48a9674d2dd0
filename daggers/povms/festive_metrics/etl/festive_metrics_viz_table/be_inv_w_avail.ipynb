{"cells": [{"cell_type": "code", "execution_count": null, "id": "27aa1b58-d8be-4e5c-bd9a-3ced56125de8", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "be040438-b99e-4eec-b0a6-230bbb47851a", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "44821ff0-5469-4405-869f-9ac139994112", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "d8a0f648-e2f5-455e-be09-e65ce6413d55", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "05963613-d1a3-4e64-b6c1-db12103feca9", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"be_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"assortment\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"express_longtail\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"current_backend_inv_weight_avail\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"current_backend_inv_binary_avail\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"backend_inv_weight_avail\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"backend_inv_binary_avail\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "df966a5e-4682-4cc8-bfea-23ba9f8d80b6", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"be_inv_weighted_availability\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"insert_ds_ist\",\n", "        \"be_facility_id\",\n", "        \"be_facility_name\",\n", "        \"assortment\",\n", "        \"express_longtail\",\n", "    ],\n", "    \"partition key\": \"insert_ds_ist\",\n", "    \"incremental_key\": \"insert_ds_ist\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains backend x frontend weighted availability \",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "0f2f176a-0ba3-41e4-90b4-6ca8343ac186", "metadata": {}, "outputs": [], "source": ["be_inv_avail = f\"\"\" \n", "\n", "with city_hour_weights as (\n", "    select city, order_hour, cast(max_by(weights,updated_at) as real) * 1.00000000000 as ch_weights\n", "    from supply_etls.city_hour_weights\n", "    group by 1,2\n", "),\n", "\n", "city_item_weights as (\n", "    select city, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.00000000000 as ci_weights\n", "    from supply_etls.city_item_weights\n", "    group by 1,2,3\n", "),\n", "\n", "city_store_weights as (\n", "    select city, facility_id, outlet_id, cast(max_by(store_weight,updated_at) as real) * 1.00000000000 as cs_weights\n", "    from supply_etls.city_store_weights\n", "    group by 1,2,3\n", "),\n", "\n", "city_weights as (\n", "    select city, cast(max_by(weight,updated_at) as real) * 1.00000000000 as city_weights\n", "    from supply_etls.city_weights\n", "    group by 1\n", "),\n", "\n", "backend_hour_weights as (\n", "    select cast(cast(backend_facility_id as real) as int) backend_facility_id, order_hour, cast(max_by(weights,updated_at) as real) * 1.00000000000 as bh_weights\n", "    from supply_etls.backend_hour_weights\n", "    group by 1,2\n", "),\n", "\n", "backend_item_weights as (\n", "    select backend_facility_id, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.00000000000 as bi_weights\n", "    from supply_etls.backend_item_weights\n", "    group by 1,2,3\n", ")\n", ",\n", "\n", "backend_store_weights as (\n", "    select cast(backend_facility_id as int) backend_facility_id, facility_id, outlet_id, cast(max_by(store_weight,updated_at) as real) * 1.00000000000 as bs_weights \n", "    from supply_etls.backend_store_weights\n", "    group by 1,2,3\n", ")\n", ",\n", "\n", "fmt as (\n", "select insert_ds_ist, hour_, be_facility_id, be_facility_name, \n", "l0_id, l0_category, \n", "l1_id, l1_category, l2_id, l2_category, p_type , item_id, express_longtail,\n", "case when item_substate = 1 then 'active' \n", "     when item_substate = 2 then 'inactive' \n", "     when item_substate = 3 then 'temp_inactive' \n", "     else 'discontinued' end as assortment,\n", "be_avail_flag\n", "from \n", "supply_etls.inventory_replenishment_metrics \n", "where insert_ds_ist = cast(current_timestamp - interval '55' minute as date)\n", "),\n", "\n", "be_be as (\n", "    select fmt.insert_ds_ist, fmt.hour_, fmt.be_facility_id, be_facility_name, \n", "    l0_id, l0_category, l1_id, l1_category,\n", "    l2_id, l2_category, p_type, fmt.item_id, assortment, express_longtail,\n", "    be_avail_flag, bi_weights, bh_weights\n", "    from fmt\n", "    left join backend_hour_weights bh on fmt.be_facility_id = bh.backend_facility_id and fmt.hour_ = bh.order_hour\n", "    left join backend_item_weights bi on (fmt.be_facility_id) = bi.backend_facility_id and fmt.item_id = bi.item_id\n", "),\n", "\n", "detailed_availability as(\n", "    select b.insert_ds_ist, b.be_facility_id, b.be_facility_name, \n", "    b.assortment, b.express_longtail,\n", "    sum(be_avail_flag*bi_weights*bh_weights)*1.000000/nullif(sum(bi_weights*bh_weights),0) as backend_inv_weight_avail,\n", "    SUM(be_avail_flag) * 1.00000000 / count(be_avail_flag) AS backend_inv_binary_avail,\n", "    sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (be_avail_flag*bi_weights)*1.000000 END) /\n", "    nullif(sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (bi_weights)*1.000000 END),0) AS current_backend_inv_weight_avail,\n", "    sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (be_avail_flag)*1.000000 END) /\n", "    nullif(count(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (be_avail_flag)*1.000000 END),0) AS current_backend_inv_binary_avail\n", "    from be_be b\n", "    group by 1,2,3,4,5\n", ")\n", "\n", "SELECT *\n", "FROM detailed_availability\n", "\n", "\"\"\"\n", "to_trino(be_inv_avail, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "2777cfb2-89c5-4e51-9c3c-6cf60f1a87fd", "metadata": {}, "outputs": [], "source": ["be_inv_avail_overall_one = f\"\"\" \n", "\n", "with city_hour_weights as (\n", "    select city, order_hour, cast(max_by(weights,updated_at) as real) * 1.00000000000 as ch_weights\n", "    from supply_etls.city_hour_weights\n", "    group by 1,2\n", "),\n", "\n", "city_item_weights as (\n", "    select city, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.00000000000 as ci_weights\n", "    from supply_etls.city_item_weights\n", "    group by 1,2,3\n", "),\n", "\n", "city_store_weights as (\n", "    select city, facility_id, outlet_id, cast(max_by(store_weight,updated_at) as real) * 1.00000000000 as cs_weights\n", "    from supply_etls.city_store_weights\n", "    group by 1,2,3\n", "),\n", "\n", "city_weights as (\n", "    select city, cast(max_by(weight,updated_at) as real) * 1.00000000000 as city_weights\n", "    from supply_etls.city_weights\n", "    group by 1\n", "),\n", "\n", "backend_hour_weights as (\n", "    select cast(cast(backend_facility_id as real) as int) backend_facility_id, order_hour, cast(max_by(weights,updated_at) as real) * 1.00000000000 as bh_weights\n", "    from supply_etls.backend_hour_weights\n", "    group by 1,2\n", "),\n", "\n", "backend_item_weights as (\n", "    select backend_facility_id, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.00000000000 as bi_weights\n", "    from supply_etls.backend_item_weights\n", "    group by 1,2,3\n", ")\n", ",\n", "\n", "backend_store_weights as (\n", "    select cast(backend_facility_id as int) backend_facility_id, facility_id, outlet_id, cast(max_by(store_weight,updated_at) as real) * 1.00000000000 as bs_weights \n", "    from supply_etls.backend_store_weights\n", "    group by 1,2,3\n", ")\n", ",\n", "\n", "fmt as (\n", "select insert_ds_ist, hour_, be_facility_id, be_facility_name, \n", "l0_id, l0_category, \n", "l1_id, l1_category, l2_id, l2_category, p_type , item_id, express_longtail,\n", "case when item_substate = 1 then 'active' \n", "     when item_substate = 2 then 'inactive' \n", "     when item_substate = 3 then 'temp_inactive' \n", "     else 'discontinued' end as assortment,\n", "be_avail_flag\n", "from \n", "supply_etls.inventory_replenishment_metrics \n", "where insert_ds_ist = cast(current_timestamp - interval '55' minute as date)\n", "),\n", "\n", "be_be as (\n", "    select fmt.insert_ds_ist, fmt.hour_, fmt.be_facility_id, be_facility_name, \n", "    l0_id, l0_category, l1_id, l1_category,\n", "    l2_id, l2_category, p_type, fmt.item_id, assortment, express_longtail,\n", "    be_avail_flag, bi_weights, bh_weights\n", "    from fmt\n", "    left join backend_hour_weights bh on fmt.be_facility_id = bh.backend_facility_id and fmt.hour_ = bh.order_hour\n", "    left join backend_item_weights bi on (fmt.be_facility_id) = bi.backend_facility_id and fmt.item_id = bi.item_id\n", "),\n", "\n", "overall_availability as(\n", "    select b.insert_ds_ist, b.be_facility_id, b.be_facility_name, \n", "    'overall' as assortment,\n", "    'overall' as express_longtail,\n", "    sum(be_avail_flag*bi_weights*bh_weights)*1.000000/nullif(sum(bi_weights*bh_weights),0) as backend_inv_weight_avail,\n", "    SUM(be_avail_flag) * 1.00000000 / count(be_avail_flag) AS backend_inv_binary_avail,\n", "    sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (be_avail_flag*bi_weights)*1.000000 END) /\n", "    nullif(sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (bi_weights)*1.000000 END),0) AS current_backend_inv_weight_avail,\n", "    sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (be_avail_flag)*1.000000 END) /\n", "    nullif(count(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (be_avail_flag)*1.000000 END),0) AS current_backend_inv_binary_avail\n", "    from be_be b\n", "    where b.assortment NOT IN ('inactive','discontinued')\n", "    group by 1,2,3,4,5\n", ")\n", "\n", "SELECT *\n", "FROM overall_availability\n", "\n", "\"\"\"\n", "to_trino(be_inv_avail_overall_one, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "cb697866-9815-4624-b58c-be4dcb3ee600", "metadata": {}, "outputs": [], "source": ["be_inv_avail_overall_two = f\"\"\" \n", "\n", "with city_hour_weights as (\n", "    select city, order_hour, cast(max_by(weights,updated_at) as real) * 1.00000000000 as ch_weights\n", "    from supply_etls.city_hour_weights\n", "    group by 1,2\n", "),\n", "\n", "city_item_weights as (\n", "    select city, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.00000000000 as ci_weights\n", "    from supply_etls.city_item_weights\n", "    group by 1,2,3\n", "),\n", "\n", "city_store_weights as (\n", "    select city, facility_id, outlet_id, cast(max_by(store_weight,updated_at) as real) * 1.00000000000 as cs_weights\n", "    from supply_etls.city_store_weights\n", "    group by 1,2,3\n", "),\n", "\n", "city_weights as (\n", "    select city, cast(max_by(weight,updated_at) as real) * 1.00000000000 as city_weights\n", "    from supply_etls.city_weights\n", "    group by 1\n", "),\n", "\n", "backend_hour_weights as (\n", "    select cast(cast(backend_facility_id as real) as int) backend_facility_id, order_hour, cast(max_by(weights,updated_at) as real) * 1.00000000000 as bh_weights\n", "    from supply_etls.backend_hour_weights\n", "    group by 1,2\n", "),\n", "\n", "backend_item_weights as (\n", "    select backend_facility_id, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.00000000000 as bi_weights\n", "    from supply_etls.backend_item_weights\n", "    group by 1,2,3\n", ")\n", ",\n", "\n", "backend_store_weights as (\n", "    select cast(backend_facility_id as int) backend_facility_id, facility_id, outlet_id, cast(max_by(store_weight,updated_at) as real) * 1.00000000000 as bs_weights \n", "    from supply_etls.backend_store_weights\n", "    group by 1,2,3\n", ")\n", ",\n", "\n", "fmt as (\n", "select insert_ds_ist, hour_, be_facility_id, be_facility_name, \n", "l0_id, l0_category, \n", "l1_id, l1_category, l2_id, l2_category, p_type , item_id, express_longtail,\n", "case when item_substate = 1 then 'active' \n", "     when item_substate = 2 then 'inactive' \n", "     when item_substate = 3 then 'temp_inactive' \n", "     else 'discontinued' end as assortment,\n", "be_avail_flag\n", "from \n", "supply_etls.inventory_replenishment_metrics \n", "where insert_ds_ist = cast(current_timestamp - interval '55' minute as date)\n", "),\n", "\n", "be_be as (\n", "    select fmt.insert_ds_ist, fmt.hour_, fmt.be_facility_id, be_facility_name, \n", "    l0_id, l0_category, l1_id, l1_category,\n", "    l2_id, l2_category, p_type, fmt.item_id, assortment, express_longtail,\n", "    be_avail_flag, bi_weights, bh_weights\n", "    from fmt\n", "    left join backend_hour_weights bh on fmt.be_facility_id = bh.backend_facility_id and fmt.hour_ = bh.order_hour\n", "    left join backend_item_weights bi on (fmt.be_facility_id) = bi.backend_facility_id and fmt.item_id = bi.item_id\n", "),\n", "\n", "overall_availability as(\n", "    select b.insert_ds_ist, b.be_facility_id, b.be_facility_name, \n", "    'overall' as assortment,\n", "    b.express_longtail,\n", "    sum(be_avail_flag*bi_weights*bh_weights)*1.000000/nullif(sum(bi_weights*bh_weights),0) as backend_inv_weight_avail,\n", "    SUM(be_avail_flag) * 1.00000000 / count(be_avail_flag) AS backend_inv_binary_avail,\n", "    sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (be_avail_flag*bi_weights)*1.000000 END) /\n", "    nullif(sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (bi_weights)*1.000000 END),0) AS current_backend_inv_weight_avail,\n", "    sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (be_avail_flag)*1.000000 END) /\n", "    nullif(count(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (be_avail_flag)*1.000000 END),0) AS current_backend_inv_binary_avail\n", "    from be_be b\n", "    where b.assortment NOT IN ('inactive','discontinued')\n", "    group by 1,2,3,4,5\n", ")\n", "\n", "SELECT *\n", "FROM overall_availability\n", "\n", "\"\"\"\n", "to_trino(be_inv_avail_overall_two, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "6378a4e6-6398-4b1e-baa5-f74a007c3a92", "metadata": {}, "outputs": [], "source": ["be_inv_avail_overall_three = f\"\"\" \n", "\n", "with city_hour_weights as (\n", "    select city, order_hour, cast(max_by(weights,updated_at) as real) * 1.00000000000 as ch_weights\n", "    from supply_etls.city_hour_weights\n", "    group by 1,2\n", "),\n", "\n", "city_item_weights as (\n", "    select city, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.00000000000 as ci_weights\n", "    from supply_etls.city_item_weights\n", "    group by 1,2,3\n", "),\n", "\n", "city_store_weights as (\n", "    select city, facility_id, outlet_id, cast(max_by(store_weight,updated_at) as real) * 1.00000000000 as cs_weights\n", "    from supply_etls.city_store_weights\n", "    group by 1,2,3\n", "),\n", "\n", "city_weights as (\n", "    select city, cast(max_by(weight,updated_at) as real) * 1.00000000000 as city_weights\n", "    from supply_etls.city_weights\n", "    group by 1\n", "),\n", "\n", "backend_hour_weights as (\n", "    select cast(cast(backend_facility_id as real) as int) backend_facility_id, order_hour, cast(max_by(weights,updated_at) as real) * 1.00000000000 as bh_weights\n", "    from supply_etls.backend_hour_weights\n", "    group by 1,2\n", "),\n", "\n", "backend_item_weights as (\n", "    select backend_facility_id, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.00000000000 as bi_weights\n", "    from supply_etls.backend_item_weights\n", "    group by 1,2,3\n", ")\n", ",\n", "\n", "backend_store_weights as (\n", "    select cast(backend_facility_id as int) backend_facility_id, facility_id, outlet_id, cast(max_by(store_weight,updated_at) as real) * 1.00000000000 as bs_weights \n", "    from supply_etls.backend_store_weights\n", "    group by 1,2,3\n", ")\n", ",\n", "\n", "fmt as (\n", "select insert_ds_ist, hour_, be_facility_id, be_facility_name, \n", "l0_id, l0_category, \n", "l1_id, l1_category, l2_id, l2_category, p_type , item_id, express_longtail,\n", "case when item_substate = 1 then 'active' \n", "     when item_substate = 2 then 'inactive' \n", "     when item_substate = 3 then 'temp_inactive' \n", "     else 'discontinued' end as assortment,\n", "be_avail_flag\n", "from \n", "supply_etls.inventory_replenishment_metrics \n", "where insert_ds_ist = cast(current_timestamp - interval '55' minute as date)\n", "),\n", "\n", "be_be as (\n", "    select fmt.insert_ds_ist, fmt.hour_, fmt.be_facility_id, be_facility_name, \n", "    l0_id, l0_category, l1_id, l1_category,\n", "    l2_id, l2_category, p_type, fmt.item_id, assortment, express_longtail,\n", "    be_avail_flag, bi_weights, bh_weights\n", "    from fmt\n", "    left join backend_hour_weights bh on fmt.be_facility_id = bh.backend_facility_id and fmt.hour_ = bh.order_hour\n", "    left join backend_item_weights bi on (fmt.be_facility_id) = bi.backend_facility_id and fmt.item_id = bi.item_id\n", "),\n", "\n", "overall_availability as(\n", "    select b.insert_ds_ist, b.be_facility_id, b.be_facility_name, \n", "    b.assortment,\n", "    'overall' as express_longtail,\n", "    sum(be_avail_flag*bi_weights*bh_weights)*1.000000/nullif(sum(bi_weights*bh_weights),0) as backend_inv_weight_avail,\n", "    SUM(be_avail_flag) * 1.00000000 / count(be_avail_flag) AS backend_inv_binary_avail,\n", "    sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (be_avail_flag*bi_weights)*1.000000 END) /\n", "    nullif(sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (bi_weights)*1.000000 END),0) AS current_backend_inv_weight_avail,\n", "    sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (be_avail_flag)*1.000000 END) /\n", "    nullif(count(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (be_avail_flag)*1.000000 END),0) AS current_backend_inv_binary_avail\n", "    from be_be b\n", "    where b.assortment NOT IN ('inactive','discontinued')\n", "    group by 1,2,3,4,5\n", ")\n", "\n", "SELECT *\n", "FROM overall_availability\n", "\n", "\"\"\"\n", "to_trino(be_inv_avail_overall_three, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}