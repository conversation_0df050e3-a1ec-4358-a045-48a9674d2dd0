{"cells": [{"cell_type": "code", "execution_count": null, "id": "24a7ef92-4a4b-4980-9836-b8591ead1177", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "15448c13-92ac-4dcc-9ec3-a1f666eb6005", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "5d1230d4-7f19-4dcc-b7fe-42b4b92a72ae", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "2eea5a66-fcc6-471d-96aa-1980b080d7d6", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "28a5cb83-2a18-4f69-a3ac-adddebd0b834", "metadata": {}, "outputs": [], "source": ["be_feinv_p_avail = f\"\"\" \n", "\n", "with city_hour_weights as (\n", "    select city, order_hour, cast(max_by(weights,updated_at) as real) * 1.00000000000 as ch_weights\n", "    from supply_etls.city_hour_weights\n", "    group by 1,2\n", "),\n", "\n", "city_item_weights as (\n", "    select city, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.00000000000 as ci_weights\n", "    from supply_etls.city_item_weights\n", "    group by 1,2,3\n", "),\n", "\n", "city_store_weights as (\n", "    select city, facility_id, outlet_id, cast(max_by(store_weight,updated_at) as real) * 1.00000000000 as cs_weights\n", "    from supply_etls.city_store_weights\n", "    group by 1,2,3\n", "),\n", "\n", "city_weights as (\n", "    select city, cast(max_by(weight,updated_at) as real) * 1.00000000000 as city_weights\n", "    from supply_etls.city_weights\n", "    group by 1\n", "),\n", "\n", "backend_hour_weights as (\n", "    select cast(cast(backend_facility_id as real) as int) backend_facility_id, order_hour, cast(max_by(weights,updated_at) as real) * 1.00000000000 as bh_weights\n", "    from supply_etls.backend_hour_weights\n", "    group by 1,2\n", "),\n", "\n", "backend_item_weights as (\n", "    select backend_facility_id, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.00000000000 as bi_weights\n", "    from supply_etls.backend_item_weights\n", "    group by 1,2,3\n", "),\n", "\n", "backend_store_weights as (\n", "    select cast(backend_facility_id as int) backend_facility_id, facility_id, outlet_id, cast(max_by(store_weight,updated_at) as real) * 1.00000000000 as bs_weights \n", "    from supply_etls.backend_store_weights\n", "    group by 1,2,3\n", "),\n", "\n", "fmt as (\n", "select insert_ds_ist, hour_, t.be_facility_id, t.be_facility_name, fe_facility_id, fe_inv_outlet_id, fe_inv_outlet_name, t.express_longtail,\n", "l0_id, l0_category, \n", "l1_id, l1_category, l2_id, l2_category, p_type , item_id,\n", "case when item_substate = 1 then 'active' \n", "     when item_substate = 2 then 'inactive' \n", "     when item_substate = 3 then 'temp_inactive' \n", "     else 'discontinued' end as assortment,\n", "fe_avail_flag\n", "from \n", "supply_etls.inventory_replenishment_metrics t\n", "where insert_ds_ist = cast(current_timestamp - interval '55' minute as date)\n", "and active_outlet_flag=1\n", "),\n", "\n", "be_be as (\n", "    select fmt.insert_ds_ist, fmt.hour_, fmt.be_facility_id, be_facility_name, fe_inv_outlet_id, \n", "    fe_inv_outlet_name, express_longtail, l0_id, l0_category, l1_id, l1_category,\n", "    l2_id, l2_category, p_type, fmt.item_id, assortment, fe_avail_flag,\n", "    bs_weights, bi_weights, bh_weights\n", "    from fmt\n", "    left join backend_hour_weights bh on fmt.be_facility_id = bh.backend_facility_id and fmt.hour_ = bh.order_hour\n", "    left join backend_item_weights bi on (fmt.be_facility_id) = bi.backend_facility_id and fmt.item_id = bi.item_id\n", "    left join backend_store_weights bs on fmt.be_facility_id = bs.backend_facility_id and fmt.fe_facility_id = bs.facility_id\n", ")\n", "\n", "    select b.insert_ds_ist, b.be_facility_id, b.be_facility_name, \n", "    b.express_longtail,\n", "    b.assortment,\n", "    b.l0_id, b.l0_category ,\n", "    b.p_type,\n", "    sum(fe_avail_flag*bi_weights*bh_weights*bs_weights)*1.000000/nullif(sum(bi_weights*bh_weights*bs_weights),0) as backend_fe_weight_avail,\n", "    SUM(fe_avail_flag) * 1.00000000 / count(fe_avail_flag) AS backend_fe_binary_avail,\n", "    sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (fe_avail_flag*bi_weights*bs_weights)*1.000000 END) /\n", "    nullif(sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (bi_weights*bs_weights)*1.000000 END),0) AS current_backend_fe_weight_avail,\n", "    sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (fe_avail_flag)*1.000000 END) /\n", "    nullif(count(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (fe_avail_flag)*1.000000 END),0) AS current_backend_fe_binary_avail\n", "    from be_be b\n", "    group by 1,2,3,4,5,6,7,8\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "89876ce2-6400-4fad-9cf7-d26cd648404e", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"be_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"express_longtail\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"assortment\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l0_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"current_backend_fe_weight_avail\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"current_backend_fe_binary_avail\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"backend_fe_weight_avail\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"backend_fe_binary_avail\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "e7bbc7d3-2c7c-4e7f-9d38-dda79a284b3f", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"be_fe_inv_ptype_weighted_availability\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"insert_ds_ist\",\n", "        \"be_facility_id\",\n", "        \"be_facility_name\",\n", "        \"express_longtail\",\n", "        \"assortment\",\n", "        \"l0_id\",\n", "        \"l0_category\",\n", "        \"p_type\",\n", "    ],\n", "    \"partition key\": \"insert_ds_ist\",\n", "    \"incremental_key\": \"insert_ds_ist\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains backend inv l0 weighted availability \",\n", "}\n", "\n", "to_trino(be_feinv_p_avail, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}