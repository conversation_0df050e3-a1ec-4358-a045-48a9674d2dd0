{"cells": [{"cell_type": "code", "execution_count": null, "id": "2fb55776-f4c9-4a02-85ad-fa6a8297a971", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "81626c26-2c8d-4966-af74-f3d58b8d836d", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "88a1c351-1741-4eb6-a80f-1b2261aaa51a", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "c53a262a-52c9-4902-8ec1-706732f37b20", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "b52d794c-fae7-4d44-bab8-ec178cea0036", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df = read_sql_query(f\"\"\"\n", "festive_metrics_query = f\"\"\"      \n", "WITH final AS (\n", "    SELECT \n", "        insert_ds_ist,\n", "        be_facility_name, \n", "        fe_city_name,\n", "        fe_facility_name,\n", "        be_facility_id,\n", "        fe_facility_id,\n", "        fastival_tag,\n", "        core_bau_flag,\n", "        be_avail_flag,\n", "        fe_avail_flag,\n", "        item_id,\n", "        item_substate,\n", "        item_name,\n", "        p_type,\n", "        l0_category,\n", "        l1_category,\n", "        l2_category,\n", "        l0_id,\n", "        l1_id,\n", "        l2_id,\n", "        SUM(COALESCE(be_inv, 0)) AS be_inv,\n", "        SUM(COALESCE(fe_inv, 0)) AS fe_inv,\n", "        SUM(COALESCE(open_sto_quantity, 0)) AS open_sto_quantity,\n", "        SUM(COALESCE(billed_qty, 0)) AS billed_qty,\n", "        SUM(v1) AS v1,\n", "        SUM(v2) AS v2,\n", "        SUM(picking_capacity_quantity_drop) AS picking_capacity_quantity_drop, \n", "        SUM(picking_capacity_sku_drop) AS picking_capacity_sku_drop, \n", "        SUM(truck_load_drop) AS truck_load_drop, \n", "        SUM(inward_drop) AS inward_drop, \n", "        SUM(storage_drop) AS storage_drop,\n", "        COUNT(DISTINCT CASE WHEN v1 > 0 THEN item_id ELSE NULL END) AS v1_lines,\n", "        COUNT(DISTINCT CASE WHEN v2 > 0 THEN item_id ELSE NULL END) AS v2_lines,\n", "        COALESCE(ABS(SUM(v2) - SUM(v1)), 0) AS quantity_drop, \n", "        COALESCE(ABS(SUM(sto_cpd)), 0) AS sto_cpd,\n", "        COALESCE(ABS(SUM(sto_cpd * (gmv * 1.00 / qty_sold))), 0) AS gmv_drop \n", "    FROM supply_etls.inventory_replenishment_metrics\n", "    WHERE hour_ = (select max(hour_) from supply_etls.inventory_replenishment_metrics where insert_ds_ist = cast(current_timestamp - interval '55' minute as date))\n", "        AND insert_ds_ist = CAST(current_timestamp - INTERVAL '55' MINUTE AS DATE)\n", "    GROUP BY \n", "        1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20\n", ")\n", "SELECT *\n", "FROM final\n", "WHERE \n", "    (fe_inv * 1.00 + open_sto_quantity * 1.00 + billed_qty * 1.00) / NULLIF(sto_cpd, 0) <= 2 * sto_cpd\n", "        \"\"\"\n", "# , CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "9f51f922-d6a7-4a62-ac46-6eed92326903", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ce9caf8d-37ed-4db7-8b3d-f576423ca6cd", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df[['insert_ds_ist','updated_at_ist','sale_start','sale_end','cutt_off']] = festive_metrics_df[['insert_ds_ist','updated_at_ist','sale_start','sale_end','cutt_off']].astype('datetime64[ns]')\n", "# festive_metrics_df[['fe_inv','po_qty_created','scheduled_po_qty','unscheduled_po_qty','po_grn_qty','po_remaining_qty','t_scheduled_quantity','t1_scheduled_quantity','t2_scheduled_quantity','t3_scheduled_quantity']] = festive_metrics_df[['fe_inv','po_qty_created','scheduled_po_qty','unscheduled_po_qty','po_grn_qty','po_remaining_qty','t_scheduled_quantity','t1_scheduled_quantity','t2_scheduled_quantity','t3_scheduled_quantity']].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "72a7b371-2b44-4027-bd3d-cce6472b7b9d", "metadata": {}, "outputs": [], "source": ["# column_dtypes = [\n", "#     {\n", "#         \"name\": col,\n", "#         \"type\": str(festive_metrics_df[col].dtype).replace(\"int64\", \"integer\").replace(\"float64\", \"real\").replace(\"str\",\"varchar\").replace(\"datetime64[ns]\",\"date\").replace(\"object\",\"varchar\"),\n", "#         \"description\": \"sample description\"\n", "#     } for col in festive_metrics_df.columns\n", "# ]\n", "\n", "# column_dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "dc45adc3-e4b4-4775-ac17-272bcac9a84b", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"be_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"fe_city_name\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"fe_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"fe_facility_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"fastival_tag\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"core_bau_flag\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"be_avail_flag\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"fe_avail_flag\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"item_substate\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l1_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l2_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l0_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l1_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l2_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"be_inv\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"fe_inv\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"open_sto_quantity\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"billed_qty\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"v1\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"v2\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"picking_capacity_quantity_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"picking_capacity_sku_drop\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"truck_load_drop\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"inward_drop\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"storage_drop\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"v1_lines\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"v2_lines\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"quantity_drop\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"sto_cpd\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"gmv_drop\", \"type\": \"real\", \"description\": \"sample description\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "1c72f405-3576-4777-993f-899519a73a57", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festive_metrics_temp_viz_truncation_two_doi\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"insert_ds_ist\",\n", "        \"be_facility_id\",\n", "        \"be_facility_name\",\n", "        \"fe_facility_id\",\n", "        \"fe_facility_name\",\n", "        \"fe_city_name\",\n", "        \"p_type\",\n", "        \"l0_id\",\n", "        \"l0_category\",\n", "        \"l1_id\",\n", "        \"l1_category\",\n", "        \"l2_id\",\n", "        \"l2_category\",\n", "        \"fastival_tag\",\n", "        \"core_bau_flag\",\n", "        \"item_id\",\n", "        \"item_substate\",\n", "        \"item_name\",\n", "        \"be_avail_flag\",\n", "        \"fe_avail_flag\",\n", "    ],\n", "    \"partition key\": \"insert_ds_ist\",\n", "    \"incremental_key\": \"insert_ds_ist\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive metrics\",\n", "}\n", "\n", "to_trino(festive_metrics_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}