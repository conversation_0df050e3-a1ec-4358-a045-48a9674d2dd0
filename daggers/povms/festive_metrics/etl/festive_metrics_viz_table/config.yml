alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
  - channel: bl-festive-metrics
concurrency: 3
dag_name: festive_metrics_viz_table
dag_type: etl
escalation_priority: low
execution_timeout: 60
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebooks:
- executor_config:
    load_type: tiny
    node_type: spot
  name: unscheduled_po_qty
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_pending_putaway
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: fe_pending_grn
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_pending_grn
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_fe_sales
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: open_billed_dispatch_sto
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: truncation_v1_not_created
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: truncation_low_doi_sku
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: truncation_oos_sku
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: truncation_two_doi
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_x_fe_w_avail
  parameters: null
  retries: 2
  tag: second
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_fe_inv_w_avail
  parameters: null
  retries: 2
  tag: second
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_inv_w_avail
  parameters: null
  retries: 2
  tag: second
- executor_config:
    load_type: tiny
    node_type: spot
  name: city_w_avail
  parameters: null
  retries: 2
  tag: second
- executor_config:
    load_type: tiny
    node_type: spot
  name: fe_w_avail
  parameters: null
  retries: 2
  tag: second
- executor_config:
    load_type: tiny
    node_type: spot
  name: fe_invt_cpd
  parameters: null
  retries: 2
  tag: second
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_invt_cpd
  parameters: null
  retries: 2
  tag: second
- executor_config:
    load_type: tiny
    node_type: spot
  name: fe_truncation_type_drop
  parameters: null
  retries: 2
  tag: second
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_po_fill_rates
  parameters: null
  retries: 2
  tag: second
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_x_fe_l0_w_avail
  parameters: null
  retries: 2
  tag: second
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_fe_inv_l0_w_avail
  parameters: null
  retries: 2
  tag: third
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_inv_l0_w_avail
  parameters: null
  retries: 2
  tag: third
- executor_config:
    load_type: tiny
    node_type: spot
  name: city_l0_w_avail
  parameters: null
  retries: 2
  tag: third
- executor_config:
    load_type: tiny
    node_type: spot
  name: fe_l0_w_avail
  parameters: null
  retries: 2
  tag: third
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_x_fe_ptype_w_avail
  parameters: null
  retries: 2
  tag: third
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_fe_inv_ptype_w_avail
  parameters: null
  retries: 2
  tag: third
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_inv_ptype_w_avail
  parameters: null
  retries: 2
  tag: third
- executor_config:
    load_type: tiny
    node_type: spot
  name: city_ptype_w_avail
  parameters: null
  retries: 2
  tag: third
- executor_config:
    load_type: tiny
    node_type: spot
  name: fe_ptype_w_avail
  parameters: null
  retries: 2
  tag: third
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_truncation_type_drop
  parameters: null
  retries: 2
  tag: third
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_cap_util
  parameters: null
  retries: 2
  tag: fourth
- executor_config:
    load_type: tiny
    node_type: spot
  name: fe_cap_util
  parameters: null
  retries: 2
  tag: fourth
- executor_config:
    load_type: tiny
    node_type: spot
  name: fe_storage_cap_util
  parameters: null
  retries: 2
  tag: fourth
- executor_config:
    load_type: tiny
    node_type: spot
  name: assortment_coverage
  parameters: null
  retries: 2
  tag: fourth
- executor_config:
    load_type: tiny
    node_type: spot
  name: assortment_coverage_fe
  parameters: null
  retries: 2
  tag: fourth
- executor_config:
    load_type: tiny
    node_type: spot
  name: sto_festive_capacity
  parameters: null
  retries: 2
  tag: fourth
- executor_config:
    load_type: tiny
    node_type: spot
  name: sku_inactive_planning_sheet
  parameters: null
  retries: 2
  tag: fourth
owner:
  email: <EMAIL>
  slack_id: U078HMPLR7H
path: povms/festive_metrics/etl/festive_metrics_viz_table
paused: false
pool: povms_pool
project_name: festive_metrics
schedule:
  end_date: '2025-09-04T00:00:00'
  interval: 55 * * * *
  start_date: '2025-04-02T00:00:00'
schedule_type: fixed
sla: 60 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 56
