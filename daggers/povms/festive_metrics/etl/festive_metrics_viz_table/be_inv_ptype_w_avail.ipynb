{"cells": [{"cell_type": "code", "execution_count": null, "id": "c6c94a49-410a-4533-b2bc-02e5bc09d221", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "b1985d63-3f7a-4170-9a2c-f709f8f665d7", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "178cb722-d139-45e8-ae43-7e51d5568924", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "a1588fb8-7794-4c96-8524-5a7ccd2f4268", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "967535c3-8c16-44a9-8f63-b7a8c8418f9d", "metadata": {}, "outputs": [], "source": ["be_inv_p_avail = f\"\"\" \n", "\n", "with city_hour_weights as (\n", "    select city, order_hour, cast(max_by(weights,updated_at) as real) * 1.00000000000 as ch_weights\n", "    from supply_etls.city_hour_weights\n", "    group by 1,2\n", "),\n", "\n", "city_item_weights as (\n", "    select city, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.00000000000 as ci_weights\n", "    from supply_etls.city_item_weights\n", "    group by 1,2,3\n", "),\n", "\n", "city_store_weights as (\n", "    select city, facility_id, outlet_id, cast(max_by(store_weight,updated_at) as real) * 1.00000000000 as cs_weights\n", "    from supply_etls.city_store_weights\n", "    group by 1,2,3\n", "),\n", "\n", "city_weights as (\n", "    select city, cast(max_by(weight,updated_at) as real) * 1.00000000000 as city_weights\n", "    from supply_etls.city_weights\n", "    group by 1\n", "),\n", "\n", "backend_hour_weights as (\n", "    select cast(cast(backend_facility_id as real) as int) backend_facility_id, order_hour, cast(max_by(weights,updated_at) as real) * 1.00000000000 as bh_weights\n", "    from supply_etls.backend_hour_weights\n", "    group by 1,2\n", "),\n", "\n", "backend_item_weights as (\n", "    select backend_facility_id, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.00000000000 as bi_weights\n", "    from supply_etls.backend_item_weights\n", "    group by 1,2,3\n", ")\n", ",\n", "\n", "backend_store_weights as (\n", "    select cast(backend_facility_id as int) backend_facility_id, facility_id, outlet_id, cast(max_by(store_weight,updated_at) as real) * 1.00000000000 as bs_weights \n", "    from supply_etls.backend_store_weights\n", "    group by 1,2,3\n", ")\n", ",\n", "\n", "fmt as (\n", "select insert_ds_ist, hour_, be_facility_id, be_facility_name, \n", "l0_id, l0_category, \n", "l1_id, l1_category, l2_id, l2_category, p_type , item_id, express_longtail,\n", "case when item_substate = 1 then 'active' \n", "     when item_substate = 2 then 'inactive' \n", "     when item_substate = 3 then 'temp_inactive' \n", "     else 'discontinued' end as assortment,\n", "be_avail_flag\n", "from \n", "supply_etls.inventory_replenishment_metrics \n", "where insert_ds_ist = cast(current_timestamp - interval '55' minute as date)\n", "and active_outlet_flag=1\n", "),\n", "\n", "be_be as (\n", "    select fmt.insert_ds_ist, fmt.hour_, fmt.be_facility_id, be_facility_name, \n", "    l0_id, l0_category, l1_id, l1_category,\n", "    l2_id, l2_category, p_type, fmt.item_id, assortment, express_longtail,\n", "    be_avail_flag, bi_weights, bh_weights\n", "    from fmt\n", "    left join backend_hour_weights bh on fmt.be_facility_id = bh.backend_facility_id and fmt.hour_ = bh.order_hour\n", "    left join backend_item_weights bi on (fmt.be_facility_id) = bi.backend_facility_id and fmt.item_id = bi.item_id\n", ")\n", "\n", "select b.insert_ds_ist, b.be_facility_id, b.be_facility_name, \n", "b.assortment, b.express_longtail,\n", "b.l0_id, b.l0_category,\n", "b.p_type,\n", "sum(be_avail_flag*bi_weights*bh_weights)*1.000000/nullif(sum(bi_weights*bh_weights),0) as backend_inv_weight_avail,\n", "SUM(be_avail_flag) * 1.00000000 / count(be_avail_flag) AS backend_inv_binary_avail,\n", "sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (be_avail_flag*bi_weights)*1.000000 END) /\n", "nullif(sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (bi_weights)*1.000000 END),0) AS current_backend_inv_weight_avail,\n", "sum(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (be_avail_flag)*1.000000 END) /\n", "nullif(count(CASE WHEN hour_=(SELECT max(hour_) from be_be) THEN (be_avail_flag)*1.000000 END),0) AS current_backend_inv_binary_avail\n", "from be_be b\n", "group by 1,2,3,4,5,6,7,8\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "5e51af99-e3fd-4214-af67-6117aeccf595", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"be_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"assortment\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"express_longtail\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"l0_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"current_backend_inv_weight_avail\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"current_backend_inv_binary_avail\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"backend_inv_weight_avail\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"backend_inv_binary_avail\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "b690a938-0484-4ae3-ae4c-e72ac55f328c", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"be_inv_ptype_weighted_availability\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"insert_ds_ist\",\n", "        \"be_facility_id\",\n", "        \"be_facility_name\",\n", "        \"assortment\",\n", "        \"express_longtail\",\n", "        \"l0_id\",\n", "        \"l0_category\",\n", "        \"p_type\",\n", "    ],\n", "    \"partition key\": \"insert_ds_ist\",\n", "    \"incremental_key\": \"insert_ds_ist\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains backend inv ptype weighted availability \",\n", "}\n", "\n", "to_trino(be_inv_p_avail, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}