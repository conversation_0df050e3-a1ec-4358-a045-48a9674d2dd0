{"cells": [{"cell_type": "code", "execution_count": null, "id": "cc2a3835-1bc6-43cf-b28c-fafe81e4038f", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e717d3fd-582e-49e1-9235-1d7852b43887", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "57bdc0aa-5ff7-4153-a57b-6c5af2c836c8", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "d41b0124-d898-4660-9eee-ea465f506abb", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "6b0b952e-ceff-4cba-909d-e0dd4e694e0d", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df = read_sql_query(f\"\"\"\n", "festive_metrics_query = f\"\"\"\n", "        with backend_pending_putaway as (\n", "        select insert_ds_ist, be_facility_id, be_facility_name, fe_city_name, \n", "        l0_id, l0_category, l1_id, l1_category, l2_id, l2_category, p_type, item_id, fastival_tag, core_bau_flag,\n", "        be_pending_putaway_inventory,\n", "        be_putaway_pending_6hr, be_putaway_pending_6_12hr, be_putaway_pending_12_24hr, be_putaway_pending_24hr,\n", "        coalesce(sum(sto_cpd),0) as sto_cpd,\n", "        sum(sto_cpd*(gmv*1.00/qty_sold)) as gmv\n", "        from \n", "        supply_etls.inventory_replenishment_metrics\n", "        WHERE hour_ = (select max(hour_) from supply_etls.inventory_replenishment_metrics where insert_ds_ist = cast(current_timestamp - interval '55' minute as date))\n", "        and insert_ds_ist = cast(current_timestamp - interval '55' minute as date)\n", "        and (be_pending_putaway_inventory>0 or \n", "        (be_putaway_pending_6hr + be_putaway_pending_6_12hr + be_putaway_pending_12_24hr + be_putaway_pending_24hr)>0)\n", "        group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19\n", "        )\n", "\n", "        select insert_ds_ist, be_facility_id, be_facility_name, fe_city_name, l0_id, l0_category, \n", "        l1_id, l1_category, l2_id, l2_category, p_type, fastival_tag, core_bau_flag,\n", "        coalesce(sum(sto_cpd),0) as sto_cpd,\n", "        coalesce(sum(be_pending_putaway_inventory),0) as be_pending_putaway_inventory,\n", "        coalesce(sum(gmv),0) as gmv,\n", "        coalesce(sum(be_putaway_pending_6hr),0) as be_putaway_pending_6hr, \n", "        coalesce(sum(be_putaway_pending_6_12hr),0) as be_putaway_pending_6_12hr,\n", "        coalesce(sum(be_putaway_pending_12_24hr),0) as be_putaway_pending_12_24hr,\n", "        coalesce(sum(be_putaway_pending_24hr),0) as be_putaway_pending_24hr\n", "        from backend_pending_putaway\n", "        group by 1,2,3,4,5,6,7,8,9,10,11,12,13\n", "        \"\"\"\n", "# , CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "98b77cbd-47e5-4e41-a925-f9f199e06447", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0c0d7b08-d140-4fac-8932-a15b5347cda4", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df[['insert_ds_ist','updated_at_ist','sale_start','sale_end','cutt_off']] = festive_metrics_df[['insert_ds_ist','updated_at_ist','sale_start','sale_end','cutt_off']].astype('datetime64[ns]')\n", "# festive_metrics_df[['fe_inv','po_qty_created','scheduled_po_qty','unscheduled_po_qty','po_grn_qty','po_remaining_qty','t_scheduled_quantity','t1_scheduled_quantity','t2_scheduled_quantity','t3_scheduled_quantity']] = festive_metrics_df[['fe_inv','po_qty_created','scheduled_po_qty','unscheduled_po_qty','po_grn_qty','po_remaining_qty','t_scheduled_quantity','t1_scheduled_quantity','t2_scheduled_quantity','t3_scheduled_quantity']].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "eaa9d2cc-879b-4fca-9c11-e2924bd614b9", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "f87fed17-ac34-4c98-add2-b0c1c4ef7496", "metadata": {}, "outputs": [], "source": ["# column_dtypes = [\n", "#     {\n", "#         \"name\": col,\n", "#         \"type\": str(festive_metrics_df[col].dtype).replace(\"int64\", \"integer\").replace(\"float64\", \"real\").replace(\"str\",\"varchar\").replace(\"datetime64[ns]\",\"date\").replace(\"object\",\"varchar\"),\n", "#         \"description\": \"sample description\"\n", "#     } for col in festive_metrics_df.columns\n", "# ]\n", "\n", "# column_dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "5520c15c-0ed7-465f-8c17-ea466a656c1f", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"be_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"fe_city_name\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l0_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l1_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l1_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l2_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l2_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"fastival_tag\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"core_bau_flag\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"sto_cpd\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"be_pending_putaway_inventory\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"gmv\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"be_putaway_pending_6hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"be_putaway_pending_6_12hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"be_putaway_pending_12_24hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"be_putaway_pending_24hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "1ecb3a26-ad62-40f4-b1c6-dc25cf32f750", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festive_metrics_temp_viz_be_pending_putaway\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"insert_ds_ist\",\n", "        \"be_facility_id\",\n", "        \"be_facility_name\",\n", "        \"fe_city_name\",\n", "        \"p_type\",\n", "        \"l0_id\",\n", "        \"l0_category\",\n", "        \"l1_id\",\n", "        \"l1_category\",\n", "        \"l2_id\",\n", "        \"l2_category\",\n", "        \"fastival_tag\",\n", "        \"core_bau_flag\",\n", "    ],\n", "    \"partition key\": \"insert_ds_ist\",\n", "    \"incremental_key\": \"insert_ds_ist\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive metrics\",\n", "}\n", "\n", "to_trino(festive_metrics_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}