{"cells": [{"cell_type": "code", "execution_count": null, "id": "1dc0a0f4-0947-4511-9aac-11af089103e4", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "1447ef85-f3ce-4fb5-81cc-2b79188fa2dd", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "ba3f6ef0-f67e-4017-b409-14d4f01104df", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "f5b33549-90fb-4230-afc9-f5cd1af461b3", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "d4e70bb9-0e1c-4512-a64b-7a3bece0ba88", "metadata": {}, "outputs": [], "source": ["feinv = f\"\"\"\n", "    with base as (\n", "    select distinct insert_ds_ist, be_facility_id, be_facility_name, fe_city_name, fe_inv_outlet_id, fe_facility_id, \n", "    fe_inv_outlet_name, item_id, fastival_tag, core_bau_flag,\n", "    l0_id, l0_category, l1_id, l1_category, l2_id, l2_category, \n", "    p_type, be_inv, po_cpd, sto_cpd, fe_inv\n", "    from supply_etls.inventory_replenishment_metrics\n", "    WHERE hour_ = (select max(hour_) from supply_etls.inventory_replenishment_metrics where insert_ds_ist = cast(current_timestamp - interval '55' minute as date))\n", "    and insert_ds_ist = cast(current_timestamp - interval '55' minute as date)\n", "    )\n", "\n", "    select insert_ds_ist, be_facility_id, be_facility_name, fe_city_name, fe_inv_outlet_id, fe_facility_id, \n", "    fe_inv_outlet_name ,\n", "    fastival_tag , core_bau_flag,\n", "    l0_id, l0_category, l1_id, l1_category, l2_id, l2_category, \n", "    p_type,\n", "    sum(fe_inv) fe_inv, sum(sto_cpd) sto_cpd\n", "    from base\n", "    group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16\n", "     \"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "7a0d1a44-ebda-4797-8a98-d1d0f28e5849", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"be_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"fe_city_name\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"fe_inv_outlet_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"fe_facility_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"fe_inv_outlet_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"fastival_tag\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"core_bau_flag\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l0_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l1_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l1_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l2_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l2_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"fe_inv\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"sto_cpd\", \"type\": \"real\", \"description\": \"sample description\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "e02ca704-2e69-43b3-a1e9-bbe5a36af75f", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festive_metrics_temp_viz_fe_invt_cpd\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"insert_ds_ist\",\n", "        \"be_facility_id\",\n", "        \"be_facility_name\",\n", "        \"fe_city_name\",\n", "        \"fe_inv_outlet_id\",\n", "        \"fe_facility_id\",\n", "        \"fe_inv_outlet_name\",\n", "        \"fastival_tag\",\n", "        \"core_bau_flag\",\n", "        \"l0_id\",\n", "        \"l0_category\",\n", "        \"l1_id\",\n", "        \"l1_category\",\n", "        \"l2_id\",\n", "        \"l2_category\",\n", "        \"p_type\",\n", "    ],\n", "    \"partition key\": \"insert_ds_ist\",\n", "    \"incremental_key\": \"insert_ds_ist\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains frontend inventory and cpd on the ptype level \",\n", "}\n", "\n", "to_trino(feinv, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}