{"cells": [{"cell_type": "code", "execution_count": null, "id": "0e96fa48-251d-4d04-bdf5-9b814e8d8eec", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "import json\n", "import shutil\n", "\n", "import boto3\n", "import io\n", "\n", "import uuid\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import gc\n", "import os\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "267d7d01-8b76-4908-8ac2-f50fe884f1e9", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "6b3d9e8f-0e1a-4342-8285-543afa7b4c88", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "78a6e010-ee00-4fd8-8c15-de1244da5e55", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "5028e48e-f53e-4b78-972e-bd6fecab27ac", "metadata": {}, "outputs": [], "source": ["# mu = read_sql_query(f\"\"\" select max(updated_at_ist) as updated_at_ist, max(hour_) as hour_,\n", "#                                 max(insert_ds_ist) as insert_ds_ist\n", "#                         from supply_etls.festive_metrics_temp\n", "#                         where insert_ds_ist >= date(current_timestamp - interval '100' minute)\n", "#                         \"\"\"\n", "#                     , CON_TRINO)\n", "# mu"]}, {"cell_type": "code", "execution_count": null, "id": "28ddfc7c-3bab-4756-bbca-97bbdca8cb59", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df = read_sql_query(f\"\"\"\n", "\n", "festive_metrics_query = f\"\"\"\n", "                with \n", "        festive_details as (\n", "                    \n", "            select \n", "                be_facility_id as facility_id, \n", "                item_id,\n", "                festival_name as festival,\n", "                sale_start_date as sale_start, \n", "                sale_end_date as sale_end, \n", "                cut_off_date as cutt_off, \n", "                assortment_reason_type as festival_bau,\n", "                sum(planned_quantity) as planned_qty\n", "            from ars_etls.final_festival_planning\n", "            where festival_name is not null\n", "            and current_date between sale_start_date - interval '10' day and sale_end_date\n", "            group by 1,2,3,4,5,6,7\n", "            \n", "            --select --current_date as updated_at, \n", "            --    facility_id, item_id, storage, catg_segment,\n", "            --        festival, festival_bau, sale_start, sale_end, cutt_off, planned_qty \n", "            --from supply_etls.final_festival_planning\n", "            --where current_date between sale_start - interval '10' day and sale_end\n", "\n", "        ),\n", "\n", "        outlet_details as (\n", "            select --current_date as updated_at, \n", "                city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "                    inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id, taggings \n", "            from supply_etls.outlet_details\n", "            where ars_check=1\n", "            and grocery_active_count >= 1\n", "            and (store_type in ('Packaged Goods', 'Dark Store'))\n", "        ),\n", "        \n", "        active_outlets as(\n", "            select outlet_id, 1 as active_flag\n", "            from dwh.dim_outlet \n", "            where is_current\n", "            and live_date >= date '2005-01-01'\n", "            and valid_to_ts_ist >= current_date\n", "        ),\n", "\n", "        item_details as (\n", "            select --current_date as updated_at, \n", "                item_id, item_name, l0_id, l0_category, \n", "                        l1_id, l1_category, l2_id, l2_category, p_type, assortment_type \n", "            from supply_etls.item_details\n", "            where assortment_type='Packaged Goods'\n", "            and handling_type = 'Non Packaging Material'\n", "        ),\n", "\n", "        tea_tagging as (\n", "            select --current_date as updated_at, \n", "                fe_outlet_id, fe_facility_id, item_id, assortment_type, \n", "                    be_hot_outlet_id, be_inv_outlet_id, be_facility_id, assortment_status_id\n", "            from supply_etls.inventory_metrics_tea_tagging\n", "            where flag = 'correct_tea' and be_facility_id <> fe_facility_id\n", "        ),\n", "\n", "        assortment_status as (\n", "            select item_id, facility_id, city_id, assortment_type, master_assortment_substate_id \n", "            from rpc.product_facility_master_assortment\n", "            where lake_active_record and active=1 and master_assortment_substate_id in (1,2,3)\n", "        ),\n", "\n", "        polygon as (\n", "            select x.facility_id, x.outlet_id, polygon_type, ST_GeometryFromText(polygon) as polygons\n", "            from lake_po.physical_facility_outlet_mapping x\n", "            inner join dwh.dim_merchant_outlet_facility_mapping m on m.pos_outlet_id = x.outlet_id and is_pos_outlet_active=1 and is_backend_merchant_active=true and is_frontend_merchant_active=true and is_current and is_mapping_enabled=true\n", "                    and x.facility_id in (select facility_id from retail.console_outlet where business_type_id = 7)\n", "            inner join (select cast(merchant_id as int) merchant_id, case when type = 'STORE_POLYGON' then 'EXPRESS' when type = 'LONGTAIL_POLYGON' then 'LONGTAIL' else type end as polygon_type, polygon \n", "                        from serviceability.ser_store_polygons where is_active = true and type in ('STORE_POLYGON','LONGTAIL_POLYGON')) poly on poly.merchant_id = m.frontend_merchant_id\n", "        ),\n", "\n", "        po_ordering_cpd as (\n", "            select item_id, outlet_id, cpd\n", "            from ars.default_cpd\n", "            where active and lake_active_record\n", "        ),\n", "\n", "        sto_transfer_cpd as (\n", "            select for_date, item_id, outlet_id, cpd, aps_adjusted\n", "            from ars.outlet_item_aps_derived_cpd\n", "            where insert_ds_ist >= cast(date(current_timestamp - interval '180' minute) as varchar)\n", "            and lake_active_record\n", "            and for_date >= (case when hour(current_timestamp) in (0,1,2) \n", "            then date(current_timestamp - interval '180' minute) else current_date end)\n", "            and for_date < (case when hour(current_timestamp) in (0,1,2) \n", "            then current_date else current_date + interval '1' day end)\n", "        ),\n", "\n", "        item_product_mapping as (\n", "            select item_id, product_id, multiplier, avg_selling_price_ratio\n", "            from dwh.dim_item_product_offer_mapping\n", "            where is_current\n", "        ),\n", "\n", "        sales as (\n", "            select date(cart_checkout_ts_ist) as date_, fs.outlet_id, ip.item_id, \n", "                    sum(procured_quantity * multiplier) as qty_sold, sum(total_procurement_price*avg_selling_price_ratio) as gmv\n", "            from dwh.fact_sales_order_item_details fs\n", "            inner join item_product_mapping ip on ip.product_id = fs.product_id\n", "            inner join outlet_details od on od.hot_outlet_id = fs.outlet_id\n", "            where fs.order_create_dt_ist >=  date(current_timestamp - interval '55' minute)\n", "            and cart_checkout_ts_ist >= (case when hour(current_timestamp) = 0 \n", "            then date(current_timestamp - interval '55' minute) else current_date end)\n", "            and cart_checkout_ts_ist < (case when hour(current_timestamp) = 0 \n", "            then current_date else current_date + interval '1' day end)\n", "            and fs.order_current_status = 'DELIVERED'\n", "            group by 1,2,3\n", "        ),\n", "        \n", "        block_invt_be AS ( \n", "            SELECT  outlet_id, item_id, SUM(quantity) AS blocked_inventory\n", "            FROM    ims.ims_item_blocked_inventory\n", "            WHERE   lake_active_record = true AND active = 1 and quantity > 0\n", "            GROUP BY    1,2 ),\n", "\n", "        inventory_be AS ( \n", "            SELECT  i.outlet_id, i.item_id,\n", "                    SUM(CASE WHEN (i.quantity - coalesce(b.blocked_inventory,0) > 0) THEN (i.quantity - coalesce(b.blocked_inventory,0)) ELSE 0 END) AS net_inv\n", "            FROM        ims.ims_item_inventory i\n", "            LEFT JOIN   block_invt_be b \n", "                        ON i.item_id = b.item_id AND i.outlet_id = b.outlet_id\n", "            WHERE i.active = 1 and i.lake_active_record = true\n", "            GROUP BY    1,2 ),\n", "            \n", "        inventory_fe_all as(\n", "            select try_cast(item_id as int) as item_id, \n", "                try_cast(outlet_id as int) as outlet_id, \n", "                sum(quantity) as quantity\n", "            from dynamodb.blinkit_store_inventory_service_oi_rt_view_v2\n", "            where quantity > 0 \n", "                and state = 'GOOD' \n", "            group by 1,2\n", "        ),\n", "\n", "        inventory_fe_blocked as(\n", "            select try_cast(item_id as int) as item_id, \n", "                try_cast(outlet_id as int) as outlet_id,\n", "                sum(quantity) as blocked_inv\n", "            from dynamodb.blinkit_store_inventory_service_blk_rt_view_v2\n", "            where status = 'BLOCKED'\n", "                and quantity > 0\n", "            group by 1, 2\n", "        ),\n", "\n", "        inventory_fe as(\n", "            select ifa.item_id,\n", "                ifa.outlet_id,\n", "                greatest(ifa.quantity - coalesce(ifb.blocked_inv, 0), 0) as net_inv\n", "            from inventory_fe_all ifa  \n", "            left join inventory_fe_blocked ifb on ifb.item_id = ifa.item_id\n", "                and ifb.outlet_id = ifa.outlet_id\n", "        ),\n", "\n", "        storage as (\n", "            select outlet_id, facility_id,\n", "                    sum(storage_cap) as storage_cap, sum(scaled_onshelf_inventory) as scaled_onshelf_inventory, sum(scaled_open_po_qty) as scaled_open_po_qty,\n", "\n", "                    sum(case when final_storage_type like ('%%REGULAR%%') then storage_cap end) as regular_storage_cap,\n", "                    sum(case when final_storage_type like ('%%REGULAR%%') then scaled_onshelf_inventory end) as regular_scaled_onshelf_inventory,\n", "                    sum(case when final_storage_type like ('%%REGULAR%%') then scaled_open_po_qty end) as regular_scaled_open_po_qty,\n", "\n", "                    sum(case when final_storage_type like ('%%HEAVY%%') then storage_cap end) as heavy_storage_cap,\n", "                    sum(case when final_storage_type like ('%%HEAVY%%') then scaled_onshelf_inventory end) as heavy_scaled_onshelf_inventory,\n", "                    sum(case when final_storage_type like ('%%HEAVY%%') then scaled_open_po_qty end) as heavy_scaled_open_po_qty,\n", "\n", "                    sum(case when final_storage_type like ('%%COLD%%') then storage_cap end) as cold_storage_cap,\n", "                    sum(case when final_storage_type like ('%%COLD%%') then scaled_onshelf_inventory end) as cold_scaled_onshelf_inventory,\n", "                    sum(case when final_storage_type like ('%%COLD%%') then scaled_open_po_qty end) as cold_scaled_open_po_qty,\n", "                    \n", "                    sum(case when final_storage_type like ('%%FROZEN%%') then storage_cap end) as frozen_storage_cap,\n", "                    sum(case when final_storage_type like ('%%FROZEN%%') then scaled_onshelf_inventory end) as frozen_scaled_onshelf_inventory,\n", "                    sum(case when final_storage_type like ('%%FROZEN%%') then scaled_open_po_qty end) as frozen_scaled_open_po_qty\n", "            from (\n", "                select outlet_id, facility_id, facility_name, final_storage_type, \n", "                        max_by(threshold,updated_at) as threshold, \n", "                        max_by(storage_cap,updated_at) as storage_cap,\n", "                        max_by(scaled_onshelf_inventory,updated_at) as scaled_onshelf_inventory, \n", "                        max_by(scaled_open_po_qty,updated_at) as scaled_open_po_qty\n", "                from supply_etls.hourly_storage_util \n", "                where updated_at >= date(current_timestamp - interval '100' minute)\n", "                group by 1,2,3,4\n", "            )\n", "            group by 1,2\n", "        ),\n", "\n", "        sto_created as (\n", "            select sto_raised_date_ist, --sender_outlet_id, \n", "                    receiver_outlet_id, item_id, \n", "                    sum(sto_raised_qty) as sto_qty, \n", "                    count(distinct sto_id) as sto_count\n", "            from dwh.flat_sto_item_details\n", "            where sto_raised_date_ist >= current_date - interval '15' day\n", "            group by 1,2,3\n", "        ),\n", "\n", "        sto_billed as ( -- this will cover the changes in previous sto's as well\n", "            select sto_raised_date_ist, --sender_outlet_id, \n", "                    receiver_outlet_id, item_id, \n", "                    count(distinct sto_id) as stos,\n", "                    sum(sto_raised_qty) as sto_raised_qty, \n", "                    sum(billed_qty) as billed_qty, \n", "                    sum(dispatch_qty) as dispatch_qty, \n", "                    sum(grn_qty) as grn_qty, \n", "                    sum(dispatch_qty-grn_qty-total_dn_qty) as pending_grn,\n", "                    sum(total_dn_qty) as total_dn_qty, \n", "                    sum(b2b_qty) as b2b_qty\n", "            from dwh.fact_inventory_transfer_details\n", "            where invoice_billed_date_ist >= current_date - interval '20' day\n", "            and sto_raised_date_ist >= current_date - interval '15' day -- need to check for past data here\n", "            group by 1,2,3\n", "        ),\n", "\n", "        sto_open as ( -- to get the current picture\n", "            select item_id, receiver_inv_outlet_id,\n", "                sum(open_sto_quantity) as open_sto_quantity, sum(billed_sto_quantity) as billed_sto_quantity\n", "            from supply_etls.inventory_metrics_open_sto\n", "            group by 1,2\n", "        ),\n", "\n", "        pending_putaway as (\n", "            select current_date as updated_at, item_id, outlet_id, sum(quantity) as pending_putaway_inventory\n", "            from ims.ims_good_inventory igi\n", "            join rpc.product_product rpp on rpp.variant_id = igi.variant_id and rpp.lake_active_record and rpp.active = 1\n", "            where igi.active = 1 and igi.lake_active_record\n", "            and igi.inventory_update_type_id in (28,76)\n", "            group by 1,2,3\n", "        ),\n", "\n", "        good_runs as (\n", "            select run_id\n", "            from ars.job_run\n", "            where started_at >= current_date - interval '2' day\n", "            and json_extract_scalar(simulation_params, '$.run') = 'ars_lite'\n", "            and simulation_params not like '%%mode%%'\n", "            and simulation_params not like '%%hyperpure_run%%'\n", "            and simulation_params not like '%%test%%'\n", "            and simulation_params not like '%%type%%'\n", "            and simulation_params not like '%%any_day_po%%'\n", "            and simulation_params not like '%%spr_migration_simulation%%'\n", "            group by 1\n", "        ),\n", "\n", "        ars_runs as (\n", "            select sto_date, --backend_outlet_id, \n", "                    frontend_outlet_id, item_id,\n", "                    count(distinct torv.run_id) as runs,\n", "                    sum(sto_quantity) as sto_quantity,\n", "                    sum(case when sto_quantity>0 then 1 end) as v1_line_items,\n", "                    approx_percentile(normalised_score,0.5) as normalised_score_kinda_cpd,\n", "                    sum(case when sto_quantity_post_truncation_in_case>0 then 1 end) as v2_line_items,\n", "                    sum(sto_quantity_post_truncation_in_case) as sto_quantity_post_truncation_in_case,\n", "                    sum(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.inward_drop') AS INTEGER)) AS inward_drop,\n", "                    sum(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.storage_drop') AS INTEGER)) AS storage_drop,\n", "                    sum(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.truck_load_drop') AS INTEGER)) AS truck_load_drop,\n", "                    sum(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.picking_capacity_quantity_drop') AS INTEGER)) AS picking_capacity_quantity_drop,\n", "                    sum(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.picking_capacity_sku_drop') AS INTEGER)) AS picking_capacity_sku_drop\n", "            from ars.transfers_optimization_results_v2 torv\n", "            inner join good_runs gr on gr.run_id = torv.run_id\n", "            where insert_ds_ist >= cast(date(current_timestamp - interval '55' minute) as varchar)\n", "            and torv.lake_active_record = TRUE\n", "            group by 1,2,3\n", "        ),\n", "\n", "        frontend_cycle as\n", "        (\n", "        select sto_date, item_id, --from_outlet_id, \n", "                to_outlet_id, \n", "                count(distinct fc.run_id) as runs,\n", "                sum(cycle_start_inventory) as seq_fe_inv,\n", "                sum(max_transfer_quantity) as be_allocated_inv,\n", "                sum(sto_quantity) as sto_quantity,\n", "                sum(initial_demand_qty) as initial_demand,\n", "                sum(default_transfer_quantity) as min_qty,\n", "                sum(cpd_sum) as max_qty,\n", "                sum(cycle_cpd_sum) as cpd\n", "        from ars.frontend_cycle_sto_quantity fc\n", "        inner join good_runs gr on gr.run_id = fc.run_id\n", "        where partition_field >= cast(current_date as varchar)\n", "        group by 1,2,3\n", "        ),\n", "\n", "        po_details as (\n", "            select po_created_at_ist, outlet_id, facility_id, item_id, \n", "                    count(distinct po_id) as po_count,\n", "                    count(distinct case when po_schedule_at_ist is not null then po_id end) as scheduled_po_count,\n", "                    count(distinct case when po_schedule_at_ist is null then po_id end) as unscheduled_po_count,\n", "                    sum(po_quantity) as po_qty, \n", "                    sum(distinct case when po_schedule_at_ist is not null then po_quantity end) as scheduled_po_qty, \n", "                    sum(distinct case when po_schedule_at_ist is null then po_quantity end) as unscheduled_po_qty, \n", "                    sum(grn_quantity) as po_grn_qty, \n", "                    sum(remaining_po_quantity) as po_remaining_qty,\n", "                    sum(case when (date(po_schedule_at_ist) = current_date) then remaining_po_quantity else 0 end) as t_scheduled_quantity,\n", "                    sum(case when (date(po_schedule_at_ist) = current_date + interval '1' day) then remaining_po_quantity else 0 end) as t1_scheduled_quantity,\n", "                    sum(case when (date(po_schedule_at_ist) = current_date + interval '2' day) then remaining_po_quantity else 0 end) as t2_scheduled_quantity,\n", "                    sum(case when (date(po_schedule_at_ist) = current_date + interval '3' day) then remaining_po_quantity else 0 end) as t3_scheduled_quantity\n", "            from supply_etls.inventory_metrics_purchase_mis\n", "            where insert_ds_ist >= current_date - interval '15' day\n", "            and po_created_at_ist >= current_date - interval '15' day -- need to check for past data here\n", "            group by 1,2,3,4\n", "        --and open_po_flag = 1\n", "        ),\n", "\n", "        po_open as (\n", "            select item_id, outlet_id,\n", "                    sum(remaining_po_quantity) as total_po_quantity,\n", "                    sum(case when (date(po_schedule_at_ist) = current_date) then remaining_po_quantity else 0 end) as t_scheduled_quantity,\n", "                    sum(case when (date(po_schedule_at_ist) = current_date + interval '1' day) then remaining_po_quantity else 0 end) as t1_scheduled_quantity,\n", "                    sum(case when (date(po_schedule_at_ist) = current_date + interval '2' day) then remaining_po_quantity else 0 end) as t2_scheduled_quantity,\n", "                    sum(case when (date(po_schedule_at_ist) = current_date + interval '3' day) then remaining_po_quantity else 0 end) as t3_scheduled_quantity\n", "            from supply_etls.inventory_metrics_open_po\n", "            where remaining_po_quantity > 0 -- not needed\n", "            group by 1,2\n", "        ),\n", "\n", "        be_capacity as (\n", "            select outlet_id, facility_id,\n", "                    -- sum(case when type_ ='sto_inbound_skus' then monthly_capacity end) as sto_inbound_skus_monthly_capacity,\n", "                    -- sum(case when type_ ='sto_inbound_skus' then daily_capacity end) as sto_inbound_skus_daily_capacity,\n", "                    sum(case when type_ ='po_inbound_skus' then monthly_capacity end) as po_inbound_skus_monthly_capacity,\n", "                    sum(case when type_ ='po_inbound_skus' then daily_capacity end) as po_inbound_skus_daily_capacity,\n", "                    sum(case when type_ ='po_inbound' then monthly_capacity end) as po_inbound_monthly_capacity,\n", "                    sum(case when type_ ='po_inbound' then daily_capacity end) as po_inbound_daily_capacity,\n", "                    sum(case when type_ ='ds_transfer' then monthly_capacity end) as ds_transfer_monthly_capacity,\n", "                    sum(case when type_ ='ds_transfer' then daily_capacity end) as ds_transfer_daily_capacity\n", "            from (\n", "                select outlet_id, facility_id, type_, \n", "                        max_by(monthly_capacity,updated_at_ist) as monthly_capacity, \n", "                        max_by(daily_capacity,updated_at_ist) as daily_capacity\n", "                from supply_etls.inbound_and_outbound_capacity \n", "                where insert_ds_ist >= current_date - interval '1' day\n", "                and assortment_type='packaged goods' \n", "                group by 1,2,3\n", "            )\n", "            group by 1,2\n", "        ),\n", "\n", "        city_hour_weights as (\n", "            select city, order_hour, cast(max_by(weights,updated_at) as real) * 1.00000000000 as ch_weights\n", "            from supply_etls.city_hour_weights\n", "            --WHERE updated_at IN (SELECT MAX(updated_at) FROM supply_etls.city_hour_weights)\n", "            group by 1,2\n", "        ),\n", "\n", "        city_item_weights as (\n", "            select city, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.00000000000 as ci_weights\n", "            from supply_etls.city_item_weights\n", "            --WHERE updated_at IN (SELECT MAX(updated_at) FROM supply_etls.city_item_weights)\n", "            group by 1,2,3\n", "        ),\n", "\n", "        city_store_weights as (\n", "            select city, facility_id, outlet_id, cast(max_by(store_weight,updated_at) as real) * 1.00000000000 as cs_weights\n", "            from supply_etls.city_store_weights\n", "            --WHERE updated_at IN (SELECT MAX(updated_at) FROM supply_etls.city_store_weights)\n", "            group by 1,2,3\n", "        ),\n", "\n", "        city_weights as (\n", "            select city, cast(max_by(weight,updated_at) as real) * 1.00000000000 as city_weights\n", "            from supply_etls.city_weights\n", "            --WHERE updated_at IN (SELECT MAX(updated_at) FROM supply_etls.city_weights)\n", "            group by 1\n", "        ),\n", "\n", "        backend_hour_weights as (\n", "            select backend_facility_id, order_hour, cast(max_by(weights,updated_at) as real) * 1.00000000000 as bh_weights\n", "            from supply_etls.backend_hour_weights\n", "            --WHERE updated_at IN (SELECT MAX(updated_at) FROM supply_etls.backend_hour_weights)\n", "            group by 1,2\n", "        ),\n", "\n", "        backend_item_weights as (\n", "            select backend_facility_id, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.00000000000 as bi_weights\n", "            from supply_etls.backend_item_weights\n", "            --WHERE updated_at IN (SELECT MAX(updated_at) FROM supply_etls.backend_item_weights)\n", "            group by 1,2,3\n", "        ),\n", "\n", "        backend_store_weights as (\n", "            select backend_facility_id, facility_id, outlet_id, cast(max_by(store_weight,updated_at) as real) * 1.00000000000 as bs_weights \n", "            from supply_etls.backend_store_weights\n", "            --WHERE updated_at IN (SELECT MAX(updated_at) FROM supply_etls.backend_store_weights)\n", "            group by 1,2,3\n", "        ),\n", "\n", "        store_grn_pendency as (\n", "            with base as (\n", "                select\n", "                    receiver_outlet_id as outlet_id,\n", "                    item_id,\n", "                    date_diff('minute',max_ds_vehicle_arrival_at_ist,current_timestamp)/60.00 as grn_pending_since_hr,\n", "                    sum(billed_qty)-sum(grn_qty)-sum(total_dn_qty) as pending_qty\n", "                from dwh.fact_inventory_transfer_details\n", "                join retail.console_outlet co on co.id=fact_inventory_transfer_details.receiver_outlet_id and business_type_id=7\n", "                    where invoice_billed_date_ist >= (current_date - interval '7' day)\n", "                    and max_ds_vehicle_arrival_at_ist is not null\n", "                    group by 1,2,3\n", "            )\n", "\n", "                select outlet_id, item_id,\n", "                sum(case when grn_pending_since_hr <= 2 then pending_qty else 0 end) as fe_grn_pending_2hr, \n", "                sum(case when grn_pending_since_hr > 2 and grn_pending_since_hr <= 4 then pending_qty else 0 end) as fe_grn_pending_2_4hr, \n", "                sum(case when grn_pending_since_hr > 4 and grn_pending_since_hr <= 6 then pending_qty else 0 end) as fe_grn_pending_4_6hr, \n", "                sum(case when grn_pending_since_hr > 6 and grn_pending_since_hr <= 8 then pending_qty else 0 end) as fe_grn_pending_6_8hr, \n", "                sum(case when grn_pending_since_hr > 8 and grn_pending_since_hr <= 12 then pending_qty else 0 end) as fe_grn_pending_8_12hr, \n", "                sum(case when grn_pending_since_hr > 12 then pending_qty else 0 end) as fe_grn_pending_12hr\n", "                from base\n", "                where pending_qty>0\n", "                group by 1,2\n", "        ),\n", "\n", "        inbound_pendency as(\n", "            with unloading_base as (\n", "                select\n", "                    ut.outlet_id,\n", "                    'Pending GRN' as inbound_bucket,\n", "                    uti.item_id,\n", "                    ut.entity_vendor_id,\n", "                    case when date_diff('hour',uti.created_at + interval '330' minute,current_timestamp) <= 6 then 'A 6hrs or less'\n", "                         when date_diff('hour',uti.created_at + interval '330' minute,current_timestamp) > 6 and date_diff('hour',uti.created_at + interval '330' minute,current_timestamp) <= 12 then 'B >6hrs and <=12hrs'\n", "                         when date_diff('hour',uti.created_at + interval '330' minute,current_timestamp) > 12 and date_diff('hour',uti.created_at + interval '330' minute,current_timestamp) <= 24 then 'C >12hrs and <=24hrs'\n", "                         else 'D greater than 24hrs'\n", "                    end as pendency_bucket,\n", "                    sum(case when uti.state ='CREATED' then uti.quantity else 0 end) qty_pending\n", "                from wms.unloading_task ut\n", "                join wms.unloading_task_item uti on ut.id = uti.unloading_task_id\n", "                where ut.insert_ds_ist > cast(current_date - interval '7' day as varchar)\n", "                  and uti.insert_ds_ist > cast(current_date - interval '7' day as varchar)\n", "                --   and ut.entity_vendor_id not in (select distinct entity_vendor_id from \n", "                --                             retail.outlet_entity_vendor_mapping o\n", "                --                             join vms.vms_vendor v on v.id = o.entity_vendor_id \n", "                --                             where v.vendor_name like '%%BCPL%%')\n", "                group by 1,2,3,4,5\n", "            ),\n", "                            \n", "            putlist_creation_pending as (\n", "                select\n", "                    pb.outlet_id,\n", "                    'Pending Putlist Creation' as inbound_bucket,\n", "                    rp.item_id,\n", "                    pb.entity_vendor_id,\n", "                    case when date_diff('hour',pb.created_at + interval '330' minute,current_timestamp) <= 6 then 'A 6hrs or less'\n", "                         when date_diff('hour',pb.created_at + interval '330' minute,current_timestamp) > 6 and date_diff('hour',pb.created_at + interval '330' minute,current_timestamp) <= 12 then 'B >6hrs and <=12hrs'\n", "                         when date_diff('hour',pb.created_at + interval '330' minute,current_timestamp) > 12 and date_diff('hour',pb.created_at + interval '330' minute,current_timestamp) <= 24 then 'C >12hrs and <=24hrs'\n", "                         else 'D greater than 24hrs'\n", "                    end as pendency_bucket,\n", "                    sum(case when pb.quantity<>0 then pb.quantity else 0 end) qty_pending\n", "                from wms.putlist_bucket pb\n", "                left join rpc.product_product rp on rp.variant_id = pb.variant_id\n", "                where pb.insert_ds_ist > cast(current_date - interval '7' day as varchar)\n", "                    and bucket_type <> 2\n", "                    -- and pb.entity_vendor_id not in (select distinct entity_vendor_id from \n", "                    --                                 retail.outlet_entity_vendor_mapping o\n", "                    --                                 join vms.vms_vendor v on v.id = o.entity_vendor_id \n", "                    --                                 where v.vendor_name like '%%BCPL%%')\n", "                group by 1,2,3,4,5\n", "            ),\n", "                            \n", "                            \n", "            putlist_creation_base as (\n", "                select \n", "                    pl.outlet_id,\n", "                    'Pending Putaway' as inbound_bucket,\n", "                    pli.item_id,\n", "                    pli.entity_vendor_id,\n", "                    case when date_diff('hour',pli.created_at + interval '330' minute,current_timestamp)  <= 6 then 'A 6hrs or less'\n", "                         when date_diff('hour', pli.created_at + interval '330' minute,current_timestamp)  > 6 and date_diff('hour',pli.created_at + interval '330' minute,current_timestamp) <= 12 then 'B >6hrs and <=12hrs'\n", "                         when date_diff('hour', pli.created_at + interval '330' minute,current_timestamp)  > 12 and date_diff('hour',pli.created_at + interval '330' minute,current_timestamp) <= 24 then 'C >12hrs and <=24hrs'\n", "                         else 'D greater than 24hrs'\n", "                    end as pendency_bucket,\n", "                    sum(pli.quantity) - sum(pli.placed_quantity) as qty_pending\n", "                from wms.put_list pl\n", "                join wms.put_list_item pli ON pl.id = pli.put_list_id\n", "                where pl.insert_ds_ist > cast(current_date - interval '7' day as varchar)\n", "                  and pli.insert_ds_ist > cast(current_date - interval '7' day as varchar)\n", "                  and pl.state <> 'COMPLETED'\n", "                --   and pli.entity_vendor_id not in (select distinct entity_vendor_id from \n", "                --                             retail.outlet_entity_vendor_mapping o\n", "                --                             join vms.vms_vendor v on v.id = o.entity_vendor_id \n", "                --                             where v.vendor_name like '%%BCPL%%')\n", "                group by 1,2,3,4,5\n", "            ),\n", "        \n", "            unloading_pendency as(\n", "                with unloading_pendency_base as(\n", "                    select ut.outlet_id, uti.item_id, ut.entity_vendor_id,\n", "                        case when ps.po_state_id in (2,3,13,14,15) and uti.state <> 'DEACTIVATED' then uti.quantity end as quantity, \n", "                        case when date_diff('hour',ut.created_at + interval '330' minute,current_timestamp)  <= 6 then 'A 6hrs or less'\n", "                             when date_diff('hour', ut.created_at + interval '330' minute,current_timestamp)  > 6 and date_diff('hour',ut.created_at + interval '330' minute,current_timestamp) <= 12 then 'B >6hrs and <=12hrs'\n", "                             when date_diff('hour', ut.created_at + interval '330' minute,current_timestamp)  > 12 and date_diff('hour',ut.created_at + interval '330' minute,current_timestamp) <= 24 then 'C >12hrs and <=24hrs'\n", "                             else 'D greater than 24hrs'\n", "                        end as pendency_bucket,\n", "                        sum(case when ps.po_state_id in (2,3,13,14,15) then units_ordered end) as units_ordered\n", "                    from wms.unloading_task ut\n", "                    left join po.purchase_order po on po.po_number = ut.reference_number\n", "                        and po.is_multiple_grn = 0\n", "                        and po.active = 1 \n", "                        and po.lake_active_record\n", "                        and po.po_type_id <> 11\n", "                    left join po.purchase_order_items poi on poi.po_id = po.id\n", "                        and poi.active = 1 \n", "                        and poi.lake_active_record\n", "                    left join po.purchase_order_status ps on ps.po_id = poi.po_id\n", "                        and ps.po_state_id in (2,3,13,14,15)\n", "                        and ps.lake_active_record\n", "                    left join wms.unloading_task_item uti on ut.id = uti.unloading_task_id\n", "                        and uti.item_id = poi.item_id\n", "                        and uti.insert_ds_ist > cast(current_date - interval '7' day as varchar)\n", "                    where ut.insert_ds_ist > cast(current_date - interval '7' day as varchar)\n", "                        and ut.state not in ('COMPLETED', 'UNLOADING_COMPLETED')\n", "                        -- and ut.entity_vendor_id not in (select distinct entity_vendor_id from \n", "                        --                                     retail.outlet_entity_vendor_mapping o\n", "                        --                                     join vms.vms_vendor v on v.id = o.entity_vendor_id \n", "                        --                                     where v.vendor_name like '%%BCPL%%')\n", "                    group by 1,2,3,4,5\n", "                )\n", "            select \n", "                outlet_id, \n", "                'Pending Unloading' as inbound_bucket,\n", "                item_id,\n", "                entity_vendor_id,\n", "                pendency_bucket,\n", "                sum(units_ordered) - sum(quantity) as qty_pending\n", "            from unloading_pendency_base\n", "            where units_ordered > 0\n", "            group by 1,2,3,4,5\n", "            ),\n", "            \n", "            combined_data as (\n", "                    select * from putlist_creation_base where qty_pending <> 0 \n", "                    union all\n", "                    select * from putlist_creation_pending where qty_pending <> 0 \n", "                    union all\n", "                    select * from unloading_base where qty_pending <> 0\n", "                    union all\n", "                    select * from unloading_pendency where qty_pending <> 0\n", "            )\n", "            \n", "            select outlet_id, item_id,\n", "                sum(case when pendency_bucket='A 6hrs or less' and inbound_bucket='Pending Putaway' then qty_pending else 0 end) as be_putaway_pending_6hr, \n", "                sum(case when pendency_bucket='B >6hrs and <=12hrs' and inbound_bucket='Pending Putaway' then qty_pending else 0 end) as be_putaway_pending_6_12hr, \n", "                sum(case when pendency_bucket='C >12hrs and <=24hrs' and inbound_bucket='Pending Putaway' then qty_pending else 0 end) as be_putaway_pending_12_24hr, \n", "                sum(case when pendency_bucket='D greater than 24hrs' and inbound_bucket='Pending Putaway' then qty_pending else 0 end) as be_putaway_pending_24hr,\n", "                sum(case when pendency_bucket='A 6hrs or less' and inbound_bucket='Pending Putlist Creation' then qty_pending else 0 end) as be_putlist_pending_6hr, \n", "                sum(case when pendency_bucket='B >6hrs and <=12hrs' and inbound_bucket='Pending Putlist Creation' then qty_pending else 0 end) as be_putlist_pending_6_12hr, \n", "                sum(case when pendency_bucket='C >12hrs and <=24hrs' and inbound_bucket='Pending Putlist Creation' then qty_pending else 0 end) as be_putlist_pending_12_24hr, \n", "                sum(case when pendency_bucket='D greater than 24hrs' and inbound_bucket='Pending Putlist Creation' then qty_pending else 0 end) as be_putlist_pending_24hr,\n", "                sum(case when pendency_bucket='A 6hrs or less' and inbound_bucket='Pending GRN' then qty_pending else 0 end) as be_grn_pending_6hr, \n", "                sum(case when pendency_bucket='B >6hrs and <=12hrs' and inbound_bucket='Pending GRN' then qty_pending else 0 end) as be_grn_pending_6_12hr, \n", "                sum(case when pendency_bucket='C >12hrs and <=24hrs' and inbound_bucket='Pending GRN' then qty_pending else 0 end) as be_grn_pending_12_24hr, \n", "                sum(case when pendency_bucket='D greater than 24hrs' and inbound_bucket='Pending GRN' then qty_pending else 0 end) as be_grn_pending_24hr,\n", "                sum(case when pendency_bucket='A 6hrs or less' and inbound_bucket='Pending Unloading' then qty_pending else 0 end) as be_unloading_pending_6hr, \n", "                sum(case when pendency_bucket='B >6hrs and <=12hrs' and inbound_bucket='Pending Unloading' then qty_pending else 0 end) as be_unloading_pending_6_12hr, \n", "                sum(case when pendency_bucket='C >12hrs and <=24hrs' and inbound_bucket='Pending Unloading' then qty_pending else 0 end) as be_unloading_pending_12_24hr, \n", "                sum(case when pendency_bucket='D greater than 24hrs' and inbound_bucket='Pending Unloading' then qty_pending else 0 end) as be_unloading_pending_24h\n", "            from combined_data\n", "            where entity_vendor_id not in (select distinct entity_vendor_id \n", "                                           from retail.outlet_entity_vendor_mapping o\n", "                                           join vms.vms_vendor v on v.id = o.entity_vendor_id \n", "                                           where v.vendor_name like '%%BCPL%%')\n", "            group by 1,2\n", "        ),\n", "        \n", "        be_grn_putaway_qty as (\n", "                with grn as (\n", "                    select\n", "                        ut.outlet_id,\n", "                        uti.item_id,\n", "                        sum(quantity) as grn_qty\n", "                    from wms.unloading_task ut\n", "                    join wms.unloading_task_item uti on ut.id = uti.unloading_task_id\n", "                    where ut.insert_ds_ist > cast(current_date - interval '7' day as varchar)\n", "                      and uti.insert_ds_ist > cast(current_date - interval '7' day as varchar)\n", "                      and (uti.updated_at + interval '330' minute) >= cast(date(current_timestamp - interval '55' minute) as timestamp)\n", "                      and uti.state = 'GRN_COMPLETED'\n", "                      and ut.entity_vendor_id not in (select distinct entity_vendor_id from \n", "                                                        retail.outlet_entity_vendor_mapping o\n", "                                                        join vms.vms_vendor v on v.id = o.entity_vendor_id \n", "                                                        where v.vendor_name like '%%BCPL%%')\n", "                    group by 1,2\n", "                ),\n", "\n", "                putlist as (\n", "                    select \n", "                        pl.outlet_id,\n", "                        pli.item_id,\n", "                        sum(placed_quantity) as pl_qty\n", "                    from wms.put_list pl\n", "                    join wms.put_list_item pli ON pl.id = pli.put_list_id\n", "                    where pl.insert_ds_ist > cast(current_date - interval '7' day as varchar)\n", "                      and pli.insert_ds_ist > cast(current_date - interval '7' day as varchar)\n", "                      and (pli.updated_at + interval '330' minute) >= cast(date(current_timestamp - interval '55' minute) as timestamp)\n", "                      and pli.entity_vendor_id not in (select distinct entity_vendor_id from \n", "                                                        retail.outlet_entity_vendor_mapping o\n", "                                                        join vms.vms_vendor v on v.id = o.entity_vendor_id \n", "                                                        where v.vendor_name like '%%BCPL%%')\n", "                    group by 1,2\n", "                )\n", "\n", "                select\n", "                    coalesce(grn.outlet_id, pl.outlet_id) as outlet_id, \n", "                    coalesce(grn.item_id,pl.item_id) as item_id, \n", "                    coalesce(grn.grn_qty,0) as be_grn_qty,\n", "                    coalesce(pl.pl_qty,0) as be_putaway_qty\n", "                from grn\n", "                full join putlist pl on grn.item_id = pl.item_id and grn.outlet_id = pl.outlet_id\n", "        ),\n", "        \n", "        tags as (\n", "            select\n", "                item_id,\n", "                outlet_id,\n", "                tag_value\n", "                from rpc.item_outlet_tag_mapping\n", "                    where tag_type_id in (1,6)\n", "                        and active = 1 and lake_active_record\n", "                        and tag_value in ('Y','A')\n", "        ),\n", "        \n", "        sku_ranking as (\n", "            select be_facility_id, item_id, final_rnking \n", "            from supply_etls.packaged_goods_top_skus_list\n", "            where insert_ds_ist = (select max(insert_ds_ist) from supply_etls.packaged_goods_top_skus_list)\n", "        ),\n", "        \n", "        po_fillrates_base as(\n", "            select \n", "                impm.insert_ds_ist, impm.updated_at_ist, impm.po_schedule_at_ist, impm.po_id, impm.facility_id, impm.item_id, impm.po_number,\n", "                impm.po_quantity, impm.grn_quantity, impm.remaining_po_quantity, impm.po_fill_rate, impm.po_expiry_at_ist, impm.is_multiple_grn,\n", "                impm.po_created_at_ist, impm.po_landing_price, impm.po_type_id, impm.po_to_be_consider, impm.po_status, ut.reference_number, ut.insert_ds_ist as landing_date, date(ut.created_at + interval '330' minute) as po_landed_at_ist\n", "            from supply_etls.inventory_metrics_purchase_mis impm\n", "            left join (select reference_number, max(insert_ds_ist) as insert_ds_ist, max(created_at) as created_at\n", "                        from wms.unloading_task\n", "                        where insert_ds_ist >= cast(current_date - interval '60' day as varchar)\n", "                        group by 1) ut\n", "                    on ut.reference_number = impm.po_number and ut.insert_ds_ist >= cast(current_date - interval '60' day as varchar)\n", "            where impm.insert_ds_ist > current_date - interval '60' day\n", "        ),\n", "\n", "        po_fillrates as(\n", "            select *\n", "            from po_fillrates_base pfb\n", "            where reference_number is not null\n", "                or po_status = 'Expired'\n", "                or po_status = 'Fulfilled'\n", "        ),\n", "\n", "        po_fillrates_final as(\n", "            select \n", "                pofr.item_id, pofr.facility_id,\n", "                coalesce(sum(case when ((po_landed_at_ist >= cast(current_date - interval '1' day as timestamp)\n", "                                        and po_landed_at_ist < current_date)\n", "                                    or (po_expiry_at_ist >= cast(current_date - interval '1' day as timestamp)\n", "                                        and po_expiry_at_ist< current_date))\n", "                                    and is_multiple_grn = 0 \n", "                                  then pofr.po_quantity end),0) as po_quantity, \n", "                coalesce(sum(case when ((po_landed_at_ist >= cast(current_date - interval '1' day as timestamp)\n", "                                        and po_landed_at_ist < current_date)\n", "                                    or (po_expiry_at_ist >= cast(current_date - interval '1' day as timestamp)\n", "                                        and po_expiry_at_ist< current_date))\n", "                                    and is_multiple_grn = 0 \n", "                                  then pofr.grn_quantity end),0) as grn_quantity,\n", "                coalesce(sum(case when ((po_landed_at_ist >= cast(current_date - interval '1' day as timestamp)\n", "                                        and po_landed_at_ist < current_date)\n", "                                    or (po_expiry_at_ist >= cast(current_date - interval '1' day as timestamp)\n", "                                        and po_expiry_at_ist< current_date))\n", "                                    and is_multiple_grn = 0 \n", "                                  then pofr.remaining_po_quantity end),0) as remaining_po_quantity,\n", "\n", "                coalesce(sum(case when po_expiry_at_ist >= cast(current_date - interval '1' day as timestamp)\n", "                                    and po_expiry_at_ist< current_date\n", "                                    and is_multiple_grn = 1 \n", "                                  then pofr.po_quantity end),0) as po_quantity_multi_grn, \n", "                coalesce(sum(case when po_expiry_at_ist >= cast(current_date - interval '1' day as timestamp)\n", "                                    and po_expiry_at_ist< current_date\n", "                                    and is_multiple_grn = 1\n", "                                  then pofr.grn_quantity end),0) as grn_quantity_multi_grn,\n", "                coalesce(sum(case when po_expiry_at_ist >= cast(current_date - interval '1' day as timestamp)\n", "                                    and po_expiry_at_ist< current_date\n", "                                    and is_multiple_grn = 1\n", "                                  then pofr.remaining_po_quantity end),0) as remaining_po_quantity_multi_grn\n", "\n", "            from po_fillrates pofr\n", "            where\n", "                (((po_landed_at_ist >= cast(current_date - interval '1' day as timestamp)\n", "                        and po_landed_at_ist < current_date)\n", "                    or (po_expiry_at_ist >= cast(current_date - interval '1' day as timestamp)\n", "                        and po_expiry_at_ist< current_date))\n", "                    and is_multiple_grn = 0)\n", "                or \n", "                (po_expiry_at_ist >= cast(current_date - interval '1' day as timestamp)\n", "                    and po_expiry_at_ist< current_date\n", "                    and is_multiple_grn = 1)\n", "            group by 1,2\n", "        ),\n", "\n", "        final_base as (\n", "            select \n", "                    cast(current_timestamp - interval '55' minute as date) as insert_ds_ist,\n", "                    cast(current_timestamp - interval '55' minute as timestamp) as updated_at_ist,\n", "                    hour(cast(current_timestamp - interval '55' minute as timestamp)) as hour_,\n", "\n", "                    id.item_id, id.item_name, id.l0_id, id.l0_category, \n", "                    id.l1_id, id.l1_category, id.l2_id, id.l2_category, id.p_type, id.assortment_type,\n", "\n", "                    od_fe.city_id as fe_city_id, od_fe.city_name as fe_city_name, od_fe.hot_outlet_id as fe_hot_outlet_id, od_fe.hot_outlet_name as fe_hot_outlet_name, \n", "                    od_fe.inv_outlet_id as fe_inv_outlet_id, od_fe.inv_outlet_name as fe_inv_outlet_name, od_fe.facility_id as fe_facility_id, od_fe.facility_name as fe_facility_name, od_fe.business_type_id as fe_business_type_id, od_fe.taggings as fe_taggings,\n", "                        \n", "                    coalesce(ac.active_flag,0) as active_outlet_flag,    \n", "                    \n", "                    od_be.city_id as be_city_id, od_be.city_name as be_city_name, od_be.hot_outlet_id as be_hot_outlet_id, od_be.hot_outlet_name as be_hot_outlet_name,\n", "                    od_be.inv_outlet_id as be_inv_outlet_id, od_be.inv_outlet_name as be_inv_outlet_name, od_be.facility_id as be_facility_id, od_be.facility_name as be_facility_name, od_be.business_type_id as be_business_type_id, od_be.taggings as be_taggings,\n", "\n", "                    --fd.storage, fd.catg_segment (need to have fallback ptype instead of this), \n", "                    (case when fd.festival is not null then fd.festival else 'BAU' end) as fastival_tag, \n", "                    (case when fd.festival is not null then 1 else 0 end) as planning_sheet_flag,\n", "                    (case when fd.festival is not null then fd.festival_bau else 'BAU' end) as core_bau_flag, \n", "                    fd.sale_start, fd.sale_end, fd.cutt_off, fd.planned_qty as festive_planned_qty_be,\n", "\n", "                    assort.assortment_type as express_longtail, assort.master_assortment_substate_id as item_substate,\n", "\n", "                    pg.polygon_type, \n", "                    -- pg.polygons,\n", "\n", "\n", "                    pocpd.cpd as po_cpd, stocpd.cpd as sto_cpd,\n", "                    stocpd.aps_adjusted as sto_aps_adjusted,\n", "\n", "                    s.qty_sold, s.gmv, inv_be.net_inv as be_inv, inv_fe.net_inv as fe_inv,\n", "                    case when inv_be.net_inv > 0 then 1 else 0 end as be_avail_flag,\n", "                    case when inv_fe.net_inv > 0 then 1 else 0 end as fe_avail_flag,\n", "\n", "                    stfe.storage_cap, stfe.scaled_onshelf_inventory, stfe.scaled_open_po_qty,\n", "                    stfe.regular_storage_cap, stfe.regular_scaled_onshelf_inventory, stfe.regular_scaled_open_po_qty,\n", "                    stfe.heavy_storage_cap, stfe.heavy_scaled_onshelf_inventory, stfe.heavy_scaled_open_po_qty,\n", "                    stfe.cold_storage_cap, stfe.cold_scaled_onshelf_inventory, stfe.cold_scaled_open_po_qty,\n", "                    stfe.frozen_storage_cap, stfe.frozen_scaled_onshelf_inventory, stfe.frozen_scaled_open_po_qty,\n", "                    \n", "                    stbe.storage_cap as storage_cap_be, stbe.scaled_onshelf_inventory as scaled_onshelf_inventory_be, stbe.scaled_open_po_qty as scaled_open_po_qty_be,\n", "                    stbe.regular_storage_cap as regular_storage_cap_be, stbe.regular_scaled_onshelf_inventory as regular_scaled_onshelf_inventory_be, stbe.regular_scaled_open_po_qty as regular_scaled_open_po_qty_be,\n", "                    stbe.heavy_storage_cap as heavy_storage_cap_be, stbe.heavy_scaled_onshelf_inventory as heavy_scaled_onshelf_inventory_be, stbe.heavy_scaled_open_po_qty as heavy_scaled_open_po_qty_be,\n", "                    stbe.cold_storage_cap as cold_storage_cap_be, stbe.cold_scaled_onshelf_inventory as cold_scaled_onshelf_inventory_be, stbe.cold_scaled_open_po_qty as cold_scaled_open_po_qty_be,\n", "                    stbe.frozen_storage_cap as frozen_storage_cap_be, stbe.frozen_scaled_onshelf_inventory as frozen_scaled_onshelf_inventory_be, stbe.frozen_scaled_open_po_qty as frozen_scaled_open_po_qty_be,\n", "                    \n", "\n", "                    stoc.sto_count as sto_created, stoc.sto_qty as sto_qty_created,\n", "                    stob.stos as sto_raised, stob.sto_raised_qty,\n", "                    stob.billed_qty, stob.dispatch_qty, stob.grn_qty, stob.pending_grn, stob.total_dn_qty, stob.b2b_qty,\n", "                    stoo.open_sto_quantity, stoo.billed_sto_quantity,\n", "                    stoo2.open_sto_quantity as be_open_sto_quantity, stoo2.billed_sto_quantity as be_billed_sto_quantity,\n", "\n", "                    pp_fe.pending_putaway_inventory as fe_pending_putaway_inventory, pp_be.pending_putaway_inventory as be_pending_putaway_inventory,\n", "\n", "                    ars.sto_quantity as v1, ars.sto_quantity_post_truncation_in_case as v2, \n", "                    ars.inward_drop, ars.storage_drop, ars.truck_load_drop, \n", "                    ars.picking_capacity_quantity_drop, ars.picking_capacity_sku_drop,\n", "\n", "                    po.po_qty as po_qty_created, po.scheduled_po_qty, po.unscheduled_po_qty, po.po_grn_qty, po.po_remaining_qty, \n", "                    po.t_scheduled_quantity, po.t1_scheduled_quantity, po.t2_scheduled_quantity, po.t3_scheduled_quantity,\n", "\n", "                    poo.total_po_quantity as open_po_qty, poo.t_scheduled_quantity as overall_t_scheduled_quantity, poo.t1_scheduled_quantity as overall_t1_scheduled_quantity, poo.t2_scheduled_quantity as overall_t2_scheduled_quantity, poo.t3_scheduled_quantity as overall_t3_scheduled_quantity,\n", "                    (poo.t_scheduled_quantity+poo.t1_scheduled_quantity+poo.t2_scheduled_quantity+poo.t3_scheduled_quantity) as overall_scheduled_po_qty,\n", "                    (poo.total_po_quantity-(poo.t_scheduled_quantity+poo.t1_scheduled_quantity+poo.t2_scheduled_quantity+poo.t3_scheduled_quantity)) as overall_unscheduled_po_qty,\n", "\n", "                    bec.po_inbound_skus_monthly_capacity, bec.po_inbound_skus_daily_capacity,\n", "                    bec.po_inbound_monthly_capacity, bec.po_inbound_daily_capacity,\n", "                    bec.ds_transfer_monthly_capacity, bec.ds_transfer_daily_capacity,\n", "\n", "                    '0' as cluster,\n", "                    '0' as fallback_ptype,\n", "                    0.00 as forecast_cpd_be_ptype,\n", "                    0.00 as predicted_cpd_be_ptype,\n", "                    0.00 as festive_planned_qty_fe,\n", "                    (whp.be_putaway_pending_6hr+ whp.be_putaway_pending_6_12hr+ whp.be_putaway_pending_12_24hr+ whp.be_putaway_pending_24hr+\n", "                    whp.be_grn_pending_6hr+ whp.be_grn_pending_6_12hr+ whp.be_grn_pending_12_24hr+ whp.be_grn_pending_24hr) as pending_grn_be,\n", "                    (dsp.fe_grn_pending_2hr + dsp.fe_grn_pending_2_4hr + dsp.fe_grn_pending_4_6hr +\n", "                    dsp.fe_grn_pending_6_8hr + dsp.fe_grn_pending_8_12hr + dsp.fe_grn_pending_12hr) as pending_grn_fe,\n", "                    0.00 as inward_capacity,\n", "                    0.00 as spillover,\n", "                    0.00 as store_dist_ratio,\n", "                    0.00 as cs_weights, 0.00 as ch_weights, 0.00 as ci_weights,\n", "                    0.00 as bs_weights, 0.00 as bh_weights, 0.00 as bi_weights,\n", "                    0.00 as city_weights,\n", "\n", "                    whp.be_putaway_pending_6hr, whp.be_putaway_pending_6_12hr, whp.be_putaway_pending_12_24hr, whp.be_putaway_pending_24hr,\n", "                    whp.be_grn_pending_6hr, whp.be_grn_pending_6_12hr, whp.be_grn_pending_12_24hr, whp.be_grn_pending_24hr,\n", "                    whp.be_putlist_pending_6hr, whp.be_putlist_pending_6_12hr, whp.be_putlist_pending_12_24hr, whp.be_putlist_pending_24hr,\n", "                    whp.be_unloading_pending_6hr, whp.be_unloading_pending_6_12hr, whp.be_unloading_pending_12_24hr, whp.be_unloading_pending_24h,\n", "                    \n", "\n", "                    dsp.fe_grn_pending_2hr, dsp.fe_grn_pending_2_4hr, dsp.fe_grn_pending_4_6hr,\n", "                    dsp.fe_grn_pending_6_8hr, dsp.fe_grn_pending_8_12hr, dsp.fe_grn_pending_12hr,\n", "                     \n", "                    (case when (inv_be.net_inv) <= 0 and (2*stocpd.cpd) <= 1 then 1 \n", "                    when (inv_be.net_inv) <= 0 and (2*stocpd.cpd) > 1 then ceiling(2*stocpd.cpd) else 0 end) as fe_item_req_inv,\n", "                     \n", "                    --sum((case when inv_be.net_inv <= 0 and (2*stocpd.cpd) <= 1 then 1 \n", "                    --when inv_be.net_inv <= 0 and (2*stocpd.cpd) > 1 then ceiling(2*stocpd.cpd) else 0 end)) over \n", "                    --(partition by od_be.facility_id, id.item_id) as be_item_req_inv,\n", "                    \n", "                    gpq.be_grn_qty, gpq.be_putaway_qty,\n", "                    \n", "                    case when x.item_id is not null then 'Bucket X'\n", "                         when a.item_id is not null then 'Bucket A'\n", "                         else 'Bucket B'\n", "                        end as be_item_bucket,\n", "                    \n", "                    case when x1.item_id is not null then 'Bucket X'\n", "                         when a1.item_id is not null then 'Bucket A'\n", "                         else 'Bucket B'\n", "                        end as fe_item_bucket,\n", "                    \n", "                    sr.final_rnking as ranking_top_200_sku,\n", "                    \n", "                    pofrf.po_quantity, pofrf.grn_quantity, pofrf.remaining_po_quantity, \n", "                    cast(pofrf.grn_quantity as decimal(10,2))/nullif(cast(pofrf.po_quantity as decimal(10,2)),0) as po_fill_rate, '0' as po_schedule,\n", "                    pofrf.po_quantity_multi_grn, pofrf.grn_quantity_multi_grn, pofrf.remaining_po_quantity_multi_grn, \n", "                    cast(pofrf.grn_quantity_multi_grn as decimal(10,2))/nullif(cast(pofrf.po_quantity_multi_grn as decimal(10,2)),0) as po_fill_rate_multi_grn, '0' as po_schedule_multi_grn,\n", "                     \n", "                    cast(date(current_timestamp - interval '55' minute) as varchar) || ' ' || cast(hour(current_timestamp - interval '55' minute) as varchar) as dt_hour\n", "\n", "            from tea_tagging tea \n", "            join outlet_details od_fe on tea.fe_facility_id=od_fe.facility_id\n", "            join outlet_details od_be on tea.be_facility_id=od_be.facility_id\n", "            join item_details id on tea.item_id=id.item_id\n", "            left join festive_details fd on fd.item_id=id.item_id and tea.be_facility_id=fd.facility_id\n", "            left join assortment_status assort on assort.item_id=id.item_id and assort.facility_id=od_fe.facility_id\n", "            left join polygon pg on pg.facility_id=tea.fe_facility_id and assort.assortment_type = pg.polygon_type\n", "            left join po_ordering_cpd pocpd on pocpd.item_id=tea.item_id and pocpd.outlet_id=tea.fe_outlet_id\n", "            left join sto_transfer_cpd stocpd on stocpd.item_id=tea.item_id and stocpd.outlet_id=tea.fe_outlet_id\n", "            left join sales s on s.outlet_id=tea.fe_outlet_id and s.item_id=tea.item_id\n", "            left join inventory_be inv_be on inv_be.item_id=tea.item_id and inv_be.outlet_id=tea.be_inv_outlet_id\n", "            left join inventory_fe inv_fe on inv_fe.item_id=tea.item_id and inv_fe.outlet_id=tea.fe_outlet_id\n", "            left join storage stfe on stfe.facility_id=tea.fe_facility_id\n", "            left join storage stbe on stbe.facility_id=tea.be_facility_id\n", "            left join sto_created stoc on stoc.receiver_outlet_id=tea.fe_outlet_id and stoc.item_id=tea.item_id and stoc.sto_raised_date_ist=date(current_timestamp - interval '55' minute)\n", "            left join sto_billed stob on stob.receiver_outlet_id=tea.fe_outlet_id and stob.item_id=tea.item_id and stob.sto_raised_date_ist=date(current_timestamp - interval '55' minute)\n", "            left join sto_open stoo on stoo.receiver_inv_outlet_id=tea.fe_outlet_id and stoo.item_id=tea.item_id\n", "            left join sto_open stoo2 on stoo2.receiver_inv_outlet_id=tea.be_inv_outlet_id and stoo2.item_id=tea.item_id\n", "            left join pending_putaway pp_fe on pp_fe.item_id=tea.item_id and pp_fe.outlet_id=tea.fe_outlet_id\n", "            left join pending_putaway pp_be on pp_be.item_id=tea.item_id and pp_be.outlet_id=tea.be_inv_outlet_id\n", "            left join ars_runs ars on ars.frontend_outlet_id=tea.fe_outlet_id and ars.item_id=tea.item_id\n", "            --left join frontend_cycle fc on fc.to_outlet_id=tea.fe_outlet_id and fc.item_id=tea.item_id\n", "            left join po_details po on po.facility_id=tea.be_facility_id and po.item_id=tea.item_id and po.po_created_at_ist=date(current_timestamp - interval '55' minute)\n", "            left join po_open poo on poo.item_id=tea.item_id and poo.outlet_id=tea.be_hot_outlet_id\n", "            left join be_capacity bec on bec.facility_id=tea.be_facility_id\n", "            -- left join city_store_weights csw on csw.facility_id=tea.fe_facility_id\n", "            -- left join city_hour_weights chw on chw.order_hour=hour(cast(current_timestamp - interval '55' minute as timestamp)) and chw.city=od_fe.city_name\n", "            -- left join city_item_weights ciw on ciw.city=od_fe.city_name and ciw.item_id=tea.item_id\n", "            -- left join backend_store_weights bsw on cast(bsw.backend_facility_id as int)=tea.be_facility_id and bsw.facility_id=tea.fe_facility_id\n", "            -- left join backend_hour_weights bhw on bhw.order_hour=hour(cast(current_timestamp - interval '55' minute as timestamp)) and cast(bsw.backend_facility_id as int) = tea.be_facility_id\n", "            -- left join backend_item_weights biw on cast(bsw.backend_facility_id as int)=tea.be_facility_id and biw.item_id=tea.item_id\n", "            -- left join city_weights cw on cw.city=od_fe.city_name \n", "            left join inbound_pendency whp on whp.outlet_id=tea.be_inv_outlet_id and whp.item_id=tea.item_id\n", "            left join store_grn_pendency dsp on dsp.outlet_id=tea.fe_outlet_id and dsp.item_id=tea.item_id\n", "            left join be_grn_putaway_qty gpq on gpq.outlet_id=tea.be_inv_outlet_id and gpq.item_id=tea.item_id\n", "            left join tags x on x.item_id = tea.item_id\n", "                    and cast(x.outlet_id as int) = tea.be_hot_outlet_id\n", "                    and x.tag_value = 'Y'\n", "            left join tags x1 on x1.item_id = tea.item_id\n", "                    and cast(x1.outlet_id as int) = tea.fe_outlet_id\n", "                    and x1.tag_value = 'Y'\n", "            left join tags a on a.item_id = tea.item_id\n", "                    and cast(a.outlet_id as int) = tea.be_hot_outlet_id\n", "                    and a.tag_value = 'A'\n", "            left join tags a1 on a1.item_id = tea.item_id\n", "                    and cast(a1.outlet_id as int) = tea.fe_outlet_id\n", "                    and a1.tag_value = 'A' \n", "            left join sku_ranking sr on sr.item_id = tea.item_id and sr.be_facility_id = tea.be_facility_id\n", "            left join po_fillrates_final pofrf on pofrf.item_id = tea.item_id \n", "                    and pofrf.facility_id = tea.be_facility_id\n", "            left join active_outlets ac on ac.outlet_id=tea.fe_outlet_id\n", "        )\n", "\n", "       select * from final_base\n", "        \"\"\"\n", "# , CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "08ce7aae-5408-4718-8383-fe1ab06e10ce", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"Actual Date of Data\"},\n", "    {\n", "        \"name\": \"updated_at_ist\",\n", "        \"type\": \"timestamp(6)\",\n", "        \"description\": \"Timestamp of Data\",\n", "    },\n", "    {\"name\": \"hour_\", \"type\": \"integer\", \"description\": \"Hour of Data\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"Item ID\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"Item Name\"},\n", "    {\"name\": \"l0_id\", \"type\": \"integer\", \"description\": \"L0 ID\"},\n", "    {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \"L0 Name\"},\n", "    {\"name\": \"l1_id\", \"type\": \"integer\", \"description\": \"L1 ID\"},\n", "    {\"name\": \"l1_category\", \"type\": \"varchar\", \"description\": \"L1 Name\"},\n", "    {\"name\": \"l2_id\", \"type\": \"integer\", \"description\": \"L2 ID\"},\n", "    {\"name\": \"l2_category\", \"type\": \"varchar\", \"description\": \"L2 Name\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"Product Type\"},\n", "    {\"name\": \"assortment_type\", \"type\": \"varchar\", \"description\": \"Assortment Type\"},\n", "    {\"name\": \"fe_city_id\", \"type\": \"integer\", \"description\": \"Dark Store City ID\"},\n", "    {\"name\": \"fe_city_name\", \"type\": \"varchar\", \"description\": \"Dark Store City Name\"},\n", "    {\"name\": \"active_outlet_flag\", \"type\": \"integer\", \"description\": \"Active Outlet Flag\"},\n", "    {\n", "        \"name\": \"fe_hot_outlet_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Dark Store HOT Outlet ID\",\n", "    },\n", "    {\n", "        \"name\": \"fe_hot_outlet_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Dark Store HOT Outlet Name\",\n", "    },\n", "    {\n", "        \"name\": \"fe_inv_outlet_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Dark Store INV Outlet ID\",\n", "    },\n", "    {\n", "        \"name\": \"fe_inv_outlet_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Dark Store INV Outlet Name\",\n", "    },\n", "    {\"name\": \"fe_facility_id\", \"type\": \"integer\", \"description\": \"Dark Store Facility ID\"},\n", "    {\n", "        \"name\": \"fe_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Dark Store Facility Name\",\n", "    },\n", "    {\n", "        \"name\": \"fe_business_type_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"FE Business Type ID\",\n", "    },\n", "    {\"name\": \"fe_taggings\", \"type\": \"varchar\", \"description\": \"FE Tag\"},\n", "    {\"name\": \"be_city_id\", \"type\": \"integer\", \"description\": \"Warehouse City ID\"},\n", "    {\"name\": \"be_city_name\", \"type\": \"varchar\", \"description\": \"Warehouse City Name\"},\n", "    {\n", "        \"name\": \"be_hot_outlet_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Warehouse HOT Outlet ID\",\n", "    },\n", "    {\n", "        \"name\": \"be_hot_outlet_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Warehouse HOT Outlet Name\",\n", "    },\n", "    {\n", "        \"name\": \"be_inv_outlet_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Warehouse INV Outlet ID\",\n", "    },\n", "    {\n", "        \"name\": \"be_inv_outlet_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Warehouse INV Outlet Name\",\n", "    },\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"Warehouse Facility ID\"},\n", "    {\n", "        \"name\": \"be_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Warehouse Facility Name\",\n", "    },\n", "    {\n", "        \"name\": \"be_business_type_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"BE Business Type ID\",\n", "    },\n", "    {\"name\": \"be_taggings\", \"type\": \"varchar\", \"description\": \"BE Tag\"},\n", "    {\"name\": \"fastival_tag\", \"type\": \"varchar\", \"description\": \"Festival Name\"},\n", "    {\n", "        \"name\": \"planning_sheet_flag\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Bulk Upload Flag\",\n", "    },\n", "    {\"name\": \"core_bau_flag\", \"type\": \"varchar\", \"description\": \"Festive BAU Tag\"},\n", "    {\"name\": \"sale_start\", \"type\": \"date\", \"description\": \"Sale Start Date\"},\n", "    {\"name\": \"sale_end\", \"type\": \"date\", \"description\": \"Sale End Date\"},\n", "    {\"name\": \"cutt_off\", \"type\": \"date\", \"description\": \"Sale Cutt Off Date\"},\n", "    {\n", "        \"name\": \"festive_planned_qty_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse level Festive Planned Quantity\",\n", "    },\n", "    {\n", "        \"name\": \"express_longtail\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Express Longtail Tag\",\n", "    },\n", "    {\"name\": \"item_substate\", \"type\": \"real\", \"description\": \"Item Substate\"},\n", "    {\"name\": \"polygon_type\", \"type\": \"varchar\", \"description\": \"Polygon Type\"},\n", "    {\"name\": \"po_cpd\", \"type\": \"real\", \"description\": \"PO Ordering CPD\"},\n", "    {\"name\": \"sto_cpd\", \"type\": \"real\", \"description\": \"STO Transfer CPD\"},\n", "    {\"name\": \"sto_aps_adjusted\", \"type\": \"real\", \"description\": \"STO APS Adjusted\"},\n", "    {\"name\": \"qty_sold\", \"type\": \"real\", \"description\": \"Quantity Sold\"},\n", "    {\"name\": \"gmv\", \"type\": \"real\", \"description\": \"GMV Sold\"},\n", "    {\"name\": \"be_inv\", \"type\": \"real\", \"description\": \"Warehouse Inventory\"},\n", "    {\"name\": \"fe_inv\", \"type\": \"real\", \"description\": \"Dark Store Inventory\"},\n", "    {\"name\": \"be_avail_flag\", \"type\": \"integer\", \"description\": \"Warehouse Availability Flag\"},\n", "    {\"name\": \"fe_avail_flag\", \"type\": \"integer\", \"description\": \"Dark Store Availability Flag\"},\n", "    {\"name\": \"storage_cap\", \"type\": \"real\", \"description\": \"Dark Store Storage Capacity\"},\n", "    {\n", "        \"name\": \"scaled_onshelf_inventory\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Scaled On Shelf Inventory\",\n", "    },\n", "    {\n", "        \"name\": \"scaled_open_po_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Scaled Open PO Quantity\",\n", "    },\n", "    {\n", "        \"name\": \"regular_storage_cap\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Regular Storage Capacity\",\n", "    },\n", "    {\n", "        \"name\": \"regular_scaled_onshelf_inventory\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Regular Scaled On Shelf Inventory\",\n", "    },\n", "    {\n", "        \"name\": \"regular_scaled_open_po_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Regular Scaled Open PO Quantity\",\n", "    },\n", "    {\n", "        \"name\": \"heavy_storage_cap\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Heavy Storage Capacity\",\n", "    },\n", "    {\n", "        \"name\": \"heavy_scaled_onshelf_inventory\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Heavy Scaled On Shelf Inventory\",\n", "    },\n", "    {\n", "        \"name\": \"heavy_scaled_open_po_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Heavy Scaled Open PO Quantity\",\n", "    },\n", "    {\"name\": \"cold_storage_cap\", \"type\": \"real\", \"description\": \"Dark Store Cold Storage Capacity\"},\n", "    {\n", "        \"name\": \"cold_scaled_onshelf_inventory\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Cold Scaled On Shelf Inventory\",\n", "    },\n", "    {\n", "        \"name\": \"cold_scaled_open_po_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Cold Scaled Open PO Quantity\",\n", "    },\n", "    {\n", "        \"name\": \"frozen_storage_cap\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Frozen Storage Capacity\",\n", "    },\n", "    {\n", "        \"name\": \"frozen_scaled_onshelf_inventory\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Frozen Scaled On Shelf Inventory\",\n", "    },\n", "    {\n", "        \"name\": \"frozen_scaled_open_po_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Frozen Scaled Open PO Quantity\",\n", "    },\n", "    {\"name\": \"storage_cap_be\", \"type\": \"real\", \"description\": \"Warehouse Storage Capacity\"},\n", "    {\n", "        \"name\": \"scaled_onshelf_inventory_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Scaled On Shelf Inventory\",\n", "    },\n", "    {\n", "        \"name\": \"scaled_open_po_qty_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Scaled Open PO Quantity\",\n", "    },\n", "    {\n", "        \"name\": \"regular_storage_cap_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Regular Storage Capacity\",\n", "    },\n", "    {\n", "        \"name\": \"regular_scaled_onshelf_inventory_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Regular Scaled On Shelf Inventory\",\n", "    },\n", "    {\n", "        \"name\": \"regular_scaled_open_po_qty_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Regular Scaled Open PO Quantity\",\n", "    },\n", "    {\n", "        \"name\": \"heavy_storage_cap_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Heavy Storage Capacity\",\n", "    },\n", "    {\n", "        \"name\": \"heavy_scaled_onshelf_inventory_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Heavy Scaled On Shelf Inventory\",\n", "    },\n", "    {\n", "        \"name\": \"heavy_scaled_open_po_qty_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Heavy Scaled Open PO Quantity\",\n", "    },\n", "    {\n", "        \"name\": \"cold_storage_cap_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Cold Storage Capacity\",\n", "    },\n", "    {\n", "        \"name\": \"cold_scaled_onshelf_inventory_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Cold Scaled On Shelf Inventory\",\n", "    },\n", "    {\n", "        \"name\": \"cold_scaled_open_po_qty_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Cold Scaled Open PO Quantity\",\n", "    },\n", "    {\n", "        \"name\": \"frozen_storage_cap_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Frozen Storage Capacity\",\n", "    },\n", "    {\n", "        \"name\": \"frozen_scaled_onshelf_inventory_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Frozen Scaled On Shelf Inventory\",\n", "    },\n", "    {\n", "        \"name\": \"frozen_scaled_open_po_qty_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Frozen Scaled Open PO Quantity\",\n", "    },\n", "    {\"name\": \"sto_created\", \"type\": \"real\", \"description\": \"STOs created\"},\n", "    {\"name\": \"sto_qty_created\", \"type\": \"real\", \"description\": \"STO Quantity Created\"},\n", "    {\"name\": \"sto_raised\", \"type\": \"real\", \"description\": \"STOs Raised\"},\n", "    {\"name\": \"sto_raised_qty\", \"type\": \"real\", \"description\": \"STO Quantity Raised\"},\n", "    {\n", "        \"name\": \"billed_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"STO Billed Quantity from STO Raised Today\",\n", "    },\n", "    {\n", "        \"name\": \"dispatch_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"STO Dispatch Quantity from STO Raised Today\",\n", "    },\n", "    {\"name\": \"grn_qty\", \"type\": \"real\", \"description\": \"STO GRN Quantity from STO Raised Today\"},\n", "    {\"name\": \"pending_grn\", \"type\": \"real\", \"description\": \"STO Pending GRN from STO Raised Today\"},\n", "    {\n", "        \"name\": \"total_dn_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"STO DN Quantity from STO Raised Today\",\n", "    },\n", "    {\"name\": \"b2b_qty\", \"type\": \"real\", \"description\": \"STO B2B Quantity from STO Raised Today\"},\n", "    {\n", "        \"name\": \"open_sto_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Open STO Quantity as of Today from Overall STO Raised across days\",\n", "    },\n", "    {\n", "        \"name\": \"billed_sto_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Billed STO Quantity as of Today from Overall STO Raised across days\",\n", "    },\n", "    {\n", "        \"name\": \"be_open_sto_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Open STO Quantity as of Today from Overall STO Raised across days where reciever is BE\",\n", "    },\n", "    {\n", "        \"name\": \"be_billed_sto_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Billed STO Quantity as of Today from Overall STO Raised across days where reciever is BE\",\n", "    },\n", "    {\n", "        \"name\": \"fe_pending_putaway_inventory\",\n", "        \"type\": \"real\",\n", "        \"description\": \"FE Pending Putaway IMS\",\n", "    },\n", "    {\n", "        \"name\": \"be_pending_putaway_inventory\",\n", "        \"type\": \"real\",\n", "        \"description\": \"BE Pending Putaway IMS\",\n", "    },\n", "    {\"name\": \"v1\", \"type\": \"real\", \"description\": \"ARS V1\"},\n", "    {\"name\": \"v2\", \"type\": \"real\", \"description\": \"ARS V2\"},\n", "    {\"name\": \"inward_drop\", \"type\": \"real\", \"description\": \"ARS Inward Capacity Drop\"},\n", "    {\"name\": \"storage_drop\", \"type\": \"real\", \"description\": \"ARS Storage Capacity Drop\"},\n", "    {\"name\": \"truck_load_drop\", \"type\": \"real\", \"description\": \"ARS Truck Capacity Drop\"},\n", "    {\n", "        \"name\": \"picking_capacity_quantity_drop\",\n", "        \"type\": \"real\",\n", "        \"description\": \"ARS Picking Quantity Capacity Drop\",\n", "    },\n", "    {\n", "        \"name\": \"picking_capacity_sku_drop\",\n", "        \"type\": \"real\",\n", "        \"description\": \"ARS Picking SKU Capacity Drop\",\n", "    },\n", "    {\"name\": \"po_qty_created\", \"type\": \"real\", \"description\": \"PO Created Quantity\"},\n", "    {\n", "        \"name\": \"scheduled_po_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Scheduled PO Quantity from PO Created Today\",\n", "    },\n", "    {\n", "        \"name\": \"unscheduled_po_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Unscheduled PO Quantity from PO Created Today\",\n", "    },\n", "    {\"name\": \"po_grn_qty\", \"type\": \"real\", \"description\": \"PO GRN Quantity from PO Created Today\"},\n", "    {\n", "        \"name\": \"po_remaining_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Open PO Quantity from PO Created Today\",\n", "    },\n", "    {\n", "        \"name\": \"t_scheduled_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"PO Quantity Scheduled for Current Date from PO Created Today\",\n", "    },\n", "    {\n", "        \"name\": \"t1_scheduled_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"PO Quantity Scheduled for Current Date plus One from PO Created Today\",\n", "    },\n", "    {\n", "        \"name\": \"t2_scheduled_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"PO Quantity Scheduled for Current Date plus Two from PO Created Today\",\n", "    },\n", "    {\n", "        \"name\": \"t3_scheduled_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"PO Quantity Scheduled for Current Date plus Three from PO Created Today\",\n", "    },\n", "    {\n", "        \"name\": \"open_po_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Open PO Quantity as of Today from Overall PO Raised across days\",\n", "    },\n", "    {\n", "        \"name\": \"overall_t_scheduled_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"PO Quantity Scheduled for Current Date as of Today from Overall PO Raised across days\",\n", "    },\n", "    {\n", "        \"name\": \"overall_t1_scheduled_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"PO Quantity Scheduled for Current Date plus One as of Today from Overall PO Raised across days\",\n", "    },\n", "    {\n", "        \"name\": \"overall_t2_scheduled_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"PO Quantity Scheduled for Current Date plus Two as of Today from Overall PO Raised across days\",\n", "    },\n", "    {\n", "        \"name\": \"overall_t3_scheduled_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"PO Quantity Scheduled for Current Date plus Three as of Today from Overall PO Raised across days\",\n", "    },\n", "    {\n", "        \"name\": \"overall_scheduled_po_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Total PO Quantity Scheduled as of Today from Overall PO Raised across days\",\n", "    },\n", "    {\n", "        \"name\": \"overall_unscheduled_po_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Total PO Quantity Unscheduled as of Today from Overall PO Raised across days\",\n", "    },\n", "    {\n", "        \"name\": \"po_inbound_skus_monthly_capacity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Inbound SKU Monthly Capacity\",\n", "    },\n", "    {\n", "        \"name\": \"po_inbound_skus_daily_capacity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Inbound SKU Daily Capacity\",\n", "    },\n", "    {\n", "        \"name\": \"po_inbound_monthly_capacity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Inbound Quantity Monthly Capacity\",\n", "    },\n", "    {\n", "        \"name\": \"po_inbound_daily_capacity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Inbound Quantity Daily Capacity\",\n", "    },\n", "    {\n", "        \"name\": \"ds_transfer_monthly_capacity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Outbound Quantity Monthly Capacity\",\n", "    },\n", "    {\n", "        \"name\": \"ds_transfer_daily_capacity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Outbound Quantity Daily Capacity\",\n", "    },\n", "    {\"name\": \"cluster\", \"type\": \"varchar\", \"description\": \"Warehouse Cluster\"},\n", "    {\"name\": \"fallback_ptype\", \"type\": \"varchar\", \"description\": \"Fallback Product Type\"},\n", "    {\n", "        \"name\": \"forecast_cpd_be_ptype\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Forecast CPD Warehouse Product Type\",\n", "    },\n", "    {\n", "        \"name\": \"predicted_cpd_be_ptype\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Predicted CPD Warehouse Product Type basis actual Sale\",\n", "    },\n", "    {\n", "        \"name\": \"festive_planned_qty_fe\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Planned Quantity\",\n", "    },\n", "    {\"name\": \"pending_grn_be\", \"type\": \"real\", \"description\": \"Overall Warehouse Pending GRN\"},\n", "    {\"name\": \"pending_grn_fe\", \"type\": \"real\", \"description\": \"Overall Dark Store Pending GRN\"},\n", "    {\"name\": \"inward_capacity\", \"type\": \"real\", \"description\": \"Dark Store Inbound Capacity\"},\n", "    {\"name\": \"spillover\", \"type\": \"real\", \"description\": \"Truck Spillover\"},\n", "    {\"name\": \"store_dist_ratio\", \"type\": \"real\", \"description\": \"Dark Store Distribution\"},\n", "    {\"name\": \"cs_weights\", \"type\": \"real\", \"description\": \"City Store Weights\"},\n", "    {\"name\": \"ch_weights\", \"type\": \"real\", \"description\": \"City Hour Weights\"},\n", "    {\"name\": \"ci_weights\", \"type\": \"real\", \"description\": \"City Item Weights\"},\n", "    {\"name\": \"bs_weights\", \"type\": \"real\", \"description\": \"Warehouse Store Weights\"},\n", "    {\"name\": \"bh_weights\", \"type\": \"real\", \"description\": \"Warehouse Hour Weights\"},\n", "    {\"name\": \"bi_weights\", \"type\": \"real\", \"description\": \"Warehouse Item Weights\"},\n", "    {\"name\": \"city_weights\", \"type\": \"real\", \"description\": \"City Weight\"},\n", "    {\n", "        \"name\": \"be_putaway_pending_6hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending Putaway Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_putaway_pending_6_12hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending Putaway Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_putaway_pending_12_24hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending Putaway Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_putaway_pending_24hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending Putaway Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_grn_pending_6hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending GRN Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_grn_pending_6_12hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending GRN Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_grn_pending_12_24hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending GRN Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_grn_pending_24hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending GRN Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_putlist_pending_6hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending Putlist Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_putlist_pending_6_12hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending Putlist Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_putlist_pending_12_24hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending Putlist Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_putlist_pending_24hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending Putlist Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_unloading_pending_6hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending Unloading Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_unloading_pending_6_12hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending Unloading Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_unloading_pending_12_24hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending Unloading Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_unloading_pending_24h\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending Unloading Buckets\",\n", "    },\n", "    {\"name\": \"po_quantity\", \"type\": \"real\", \"description\": \"PO Quantity\"},\n", "    {\"name\": \"grn_quantity\", \"type\": \"real\", \"description\": \"GRN Quantity\"},\n", "    {\"name\": \"remaining_po_quantity\", \"type\": \"real\", \"description\": \"Remaining PO Quantity\"},\n", "    {\"name\": \"po_fill_rate\", \"type\": \"real\", \"description\": \"PO Fill Rate\"},\n", "    {\"name\": \"po_schedule\", \"type\": \"varchar\", \"description\": \"PO Schedule\"},\n", "    {\"name\": \"po_quantity_multi_grn\", \"type\": \"real\", \"description\": \"PO Quantity (Multi GRN)\"},\n", "    {\"name\": \"grn_quantity_multi_grn\", \"type\": \"real\", \"description\": \"GRN Quantity (Multi GRN)\"},\n", "    {\n", "        \"name\": \"remaining_po_quantity_multi_grn\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Remaining PO Quantity (Multi GRN)\",\n", "    },\n", "    {\"name\": \"po_fill_rate_multi_grn\", \"type\": \"real\", \"description\": \"PO Fill Rate (Multi GRN)\"},\n", "    {\"name\": \"po_schedule_multi_grn\", \"type\": \"varchar\", \"description\": \"PO Schedule (Multi GRN)\"},\n", "    {\n", "        \"name\": \"fe_grn_pending_2hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Dark Store Pending GRN Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"fe_grn_pending_2_4hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Dark Store Pending GRN Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"fe_grn_pending_4_6hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Dark Store Pending GRN Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"fe_grn_pending_6_8hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Dark Store Pending GRN Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"fe_grn_pending_8_12hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Dark Store Pending GRN Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"fe_grn_pending_12hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Dark Store Pending GRN Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"fe_item_req_inv\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Item Required Inventory\",\n", "    },\n", "    {\n", "        \"name\": \"be_grn_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Total GRN Quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_putaway_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Total Putaway Quantity\",\n", "    },\n", "    {\n", "        \"name\": \"fe_item_bucket\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Dark Store Item level buckets (X>A>B)\",\n", "    },\n", "    {\n", "        \"name\": \"be_item_bucket\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Dark Store Item level buckets (X>A>B)\",\n", "    },\n", "    {\n", "        \"name\": \"ranking_top_200_sku\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Ranking of top 200 SKUs\",\n", "    },\n", "    {\n", "        \"name\": \"dt_hour\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Date Hour String\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "701ebb03-4caa-44b5-900c-acfa8596e0ec", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"inventory_replenishment_metrics\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"dt_hour\",\n", "        \"fe_facility_id\",\n", "        \"item_id\",\n", "        \"be_facility_id\",\n", "        \"fastival_tag\",\n", "    ],\n", "    \"partition_key\": [\"insert_ds_ist\", \"hour_\"],\n", "    \"incremental_key\": \"dt_hour\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive metrics\",\n", "    \"run_maintenance\": <PERSON><PERSON><PERSON>,\n", "}\n", "\n", "to_trino(festive_metrics_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "b3b028bb-9f06-423c-9927-a24ac0d9ea2e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}