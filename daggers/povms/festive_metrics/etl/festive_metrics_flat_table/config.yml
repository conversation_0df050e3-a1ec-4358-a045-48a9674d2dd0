alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
  - channel: bl-festive-metrics
dag_name: festive_metrics_flat_table
dag_type: etl
escalation_priority: low
execution_timeout: 60
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
  retries: 3
  retry_delay_in_seconds: 60
owner:
  email: <EMAIL>
  slack_id: U078HMPLR7H
path: povms/festive_metrics/etl/festive_metrics_flat_table
paused: false
pool: priority_pool
project_name: festive_metrics
schedule:
  end_date: '2025-09-04T00:00:00'
  interval: 35 * * * *
  start_date: '2025-05-14T00:00:00'
schedule_type: fixed
sla: 60 minutes
support_files: []
tags: []
template_name: notebook
version: 34
