{"cells": [{"cell_type": "code", "execution_count": null, "id": "f0026733-1bab-4892-9aec-3972f17af7bb", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "import json\n", "import shutil\n", "\n", "import boto3\n", "import io\n", "\n", "import uuid\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import gc\n", "import os\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "19105d0e-1190-4073-8d04-5963847606b1", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "31b62948-4ce3-4b2f-8a2e-7e48cbac52fd", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "a4be6793-b17f-42e9-9880-9dc04829f37b", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "6d29b2f5-16c8-4b5e-8985-d06934eaa7bd", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"Actual Date of Data\"},\n", "    {\n", "        \"name\": \"updated_at_ist\",\n", "        \"type\": \"timestamp(6)\",\n", "        \"description\": \"Timestamp of Data\",\n", "    },\n", "    {\"name\": \"hour_\", \"type\": \"integer\", \"description\": \"Hour of Data\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"Item ID\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"Item Name\"},\n", "    {\"name\": \"l0_id\", \"type\": \"integer\", \"description\": \"L0 ID\"},\n", "    {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \"L0 Name\"},\n", "    {\"name\": \"l1_id\", \"type\": \"integer\", \"description\": \"L1 ID\"},\n", "    {\"name\": \"l1_category\", \"type\": \"varchar\", \"description\": \"L1 Name\"},\n", "    {\"name\": \"l2_id\", \"type\": \"integer\", \"description\": \"L2 ID\"},\n", "    {\"name\": \"l2_category\", \"type\": \"varchar\", \"description\": \"L2 Name\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"Product Type\"},\n", "    {\"name\": \"assortment_type\", \"type\": \"varchar\", \"description\": \"Assortment Type\"},\n", "    {\"name\": \"fe_city_id\", \"type\": \"integer\", \"description\": \"Dark Store City ID\"},\n", "    {\"name\": \"fe_city_name\", \"type\": \"varchar\", \"description\": \"Dark Store City Name\"},\n", "    {\n", "        \"name\": \"fe_hot_outlet_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Dark Store HOT Outlet ID\",\n", "    },\n", "    {\n", "        \"name\": \"fe_hot_outlet_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Dark Store HOT Outlet Name\",\n", "    },\n", "    {\n", "        \"name\": \"fe_inv_outlet_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Dark Store INV Outlet ID\",\n", "    },\n", "    {\n", "        \"name\": \"fe_inv_outlet_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Dark Store INV Outlet Name\",\n", "    },\n", "    {\"name\": \"fe_facility_id\", \"type\": \"integer\", \"description\": \"Dark Store Facility ID\"},\n", "    {\n", "        \"name\": \"fe_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Dark Store Facility Name\",\n", "    },\n", "    {\n", "        \"name\": \"fe_business_type_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"FE Business Type ID\",\n", "    },\n", "    {\"name\": \"fe_taggings\", \"type\": \"varchar\", \"description\": \"FE Tag\"},\n", "    {\"name\": \"be_city_id\", \"type\": \"integer\", \"description\": \"Warehouse City ID\"},\n", "    {\"name\": \"be_city_name\", \"type\": \"varchar\", \"description\": \"Warehouse City Name\"},\n", "    {\n", "        \"name\": \"be_hot_outlet_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Warehouse HOT Outlet ID\",\n", "    },\n", "    {\n", "        \"name\": \"be_hot_outlet_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Warehouse HOT Outlet Name\",\n", "    },\n", "    {\n", "        \"name\": \"be_inv_outlet_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Warehouse INV Outlet ID\",\n", "    },\n", "    {\n", "        \"name\": \"be_inv_outlet_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Warehouse INV Outlet Name\",\n", "    },\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"Warehouse Facility ID\"},\n", "    {\n", "        \"name\": \"be_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Warehouse Facility Name\",\n", "    },\n", "    {\n", "        \"name\": \"be_business_type_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"BE Business Type ID\",\n", "    },\n", "    {\"name\": \"be_taggings\", \"type\": \"varchar\", \"description\": \"BE Tag\"},\n", "    {\"name\": \"fastival_tag\", \"type\": \"varchar\", \"description\": \"Festival Name\"},\n", "    {\n", "        \"name\": \"planning_sheet_flag\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Bulk Upload Flag\",\n", "    },\n", "    {\"name\": \"core_bau_flag\", \"type\": \"varchar\", \"description\": \"Festive BAU Tag\"},\n", "    {\"name\": \"sale_start\", \"type\": \"date\", \"description\": \"Sale Start Date\"},\n", "    {\"name\": \"sale_end\", \"type\": \"date\", \"description\": \"Sale End Date\"},\n", "    {\"name\": \"cutt_off\", \"type\": \"date\", \"description\": \"Sale Cutt Off Date\"},\n", "    {\n", "        \"name\": \"festive_planned_qty_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse level Festive Planned Quantity\",\n", "    },\n", "    {\n", "        \"name\": \"express_longtail\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Express Longtail Tag\",\n", "    },\n", "    {\"name\": \"item_substate\", \"type\": \"real\", \"description\": \"Item Substate\"},\n", "    {\"name\": \"polygon_type\", \"type\": \"varchar\", \"description\": \"Polygon Type\"},\n", "    {\"name\": \"po_cpd\", \"type\": \"real\", \"description\": \"PO Ordering CPD\"},\n", "    {\"name\": \"sto_cpd\", \"type\": \"real\", \"description\": \"STO Transfer CPD\"},\n", "    {\"name\": \"sto_aps_adjusted\", \"type\": \"real\", \"description\": \"STO APS Adjusted\"},\n", "    {\"name\": \"qty_sold\", \"type\": \"real\", \"description\": \"Quantity Sold\"},\n", "    {\"name\": \"gmv\", \"type\": \"real\", \"description\": \"GMV Sold\"},\n", "    {\"name\": \"be_inv\", \"type\": \"real\", \"description\": \"Warehouse Inventory\"},\n", "    {\"name\": \"fe_inv\", \"type\": \"real\", \"description\": \"Dark Store Inventory\"},\n", "    {\"name\": \"be_avail_flag\", \"type\": \"integer\", \"description\": \"Warehouse Availability Flag\"},\n", "    {\"name\": \"fe_avail_flag\", \"type\": \"integer\", \"description\": \"Dark Store Availability Flag\"},\n", "    {\"name\": \"storage_cap\", \"type\": \"real\", \"description\": \"Dark Store Storage Capacity\"},\n", "    {\n", "        \"name\": \"scaled_onshelf_inventory\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Scaled On Shelf Inventory\",\n", "    },\n", "    {\n", "        \"name\": \"scaled_open_po_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Scaled Open PO Quantity\",\n", "    },\n", "    {\n", "        \"name\": \"regular_storage_cap\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Regular Storage Capacity\",\n", "    },\n", "    {\n", "        \"name\": \"regular_scaled_onshelf_inventory\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Regular Scaled On Shelf Inventory\",\n", "    },\n", "    {\n", "        \"name\": \"regular_scaled_open_po_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Regular Scaled Open PO Quantity\",\n", "    },\n", "    {\n", "        \"name\": \"heavy_storage_cap\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Heavy Storage Capacity\",\n", "    },\n", "    {\n", "        \"name\": \"heavy_scaled_onshelf_inventory\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Heavy Scaled On Shelf Inventory\",\n", "    },\n", "    {\n", "        \"name\": \"heavy_scaled_open_po_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Heavy Scaled Open PO Quantity\",\n", "    },\n", "    {\"name\": \"cold_storage_cap\", \"type\": \"real\", \"description\": \"Dark Store Cold Storage Capacity\"},\n", "    {\n", "        \"name\": \"cold_scaled_onshelf_inventory\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Cold Scaled On Shelf Inventory\",\n", "    },\n", "    {\n", "        \"name\": \"cold_scaled_open_po_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Cold Scaled Open PO Quantity\",\n", "    },\n", "    {\n", "        \"name\": \"frozen_storage_cap\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Frozen Storage Capacity\",\n", "    },\n", "    {\n", "        \"name\": \"frozen_scaled_onshelf_inventory\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Frozen Scaled On Shelf Inventory\",\n", "    },\n", "    {\n", "        \"name\": \"frozen_scaled_open_po_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Frozen Scaled Open PO Quantity\",\n", "    },\n", "    {\"name\": \"storage_cap_be\", \"type\": \"real\", \"description\": \"Warehouse Storage Capacity\"},\n", "    {\n", "        \"name\": \"scaled_onshelf_inventory_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Scaled On Shelf Inventory\",\n", "    },\n", "    {\n", "        \"name\": \"scaled_open_po_qty_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Scaled Open PO Quantity\",\n", "    },\n", "    {\n", "        \"name\": \"regular_storage_cap_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Regular Storage Capacity\",\n", "    },\n", "    {\n", "        \"name\": \"regular_scaled_onshelf_inventory_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Regular Scaled On Shelf Inventory\",\n", "    },\n", "    {\n", "        \"name\": \"regular_scaled_open_po_qty_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Regular Scaled Open PO Quantity\",\n", "    },\n", "    {\n", "        \"name\": \"heavy_storage_cap_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Heavy Storage Capacity\",\n", "    },\n", "    {\n", "        \"name\": \"heavy_scaled_onshelf_inventory_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Heavy Scaled On Shelf Inventory\",\n", "    },\n", "    {\n", "        \"name\": \"heavy_scaled_open_po_qty_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Heavy Scaled Open PO Quantity\",\n", "    },\n", "    {\n", "        \"name\": \"cold_storage_cap_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Cold Storage Capacity\",\n", "    },\n", "    {\n", "        \"name\": \"cold_scaled_onshelf_inventory_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Cold Scaled On Shelf Inventory\",\n", "    },\n", "    {\n", "        \"name\": \"cold_scaled_open_po_qty_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Cold Scaled Open PO Quantity\",\n", "    },\n", "    {\n", "        \"name\": \"frozen_storage_cap_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Frozen Storage Capacity\",\n", "    },\n", "    {\n", "        \"name\": \"frozen_scaled_onshelf_inventory_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Frozen Scaled On Shelf Inventory\",\n", "    },\n", "    {\n", "        \"name\": \"frozen_scaled_open_po_qty_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Frozen Scaled Open PO Quantity\",\n", "    },\n", "    {\"name\": \"sto_created\", \"type\": \"real\", \"description\": \"STOs created\"},\n", "    {\"name\": \"sto_qty_created\", \"type\": \"real\", \"description\": \"STO Quantity Created\"},\n", "    {\"name\": \"sto_raised\", \"type\": \"real\", \"description\": \"STOs Raised\"},\n", "    {\"name\": \"sto_raised_qty\", \"type\": \"real\", \"description\": \"STO Quantity Raised\"},\n", "    {\n", "        \"name\": \"billed_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"STO Billed Quantity from STO Raised Today\",\n", "    },\n", "    {\n", "        \"name\": \"dispatch_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"STO Dispatch Quantity from STO Raised Today\",\n", "    },\n", "    {\"name\": \"grn_qty\", \"type\": \"real\", \"description\": \"STO GRN Quantity from STO Raised Today\"},\n", "    {\"name\": \"pending_grn\", \"type\": \"real\", \"description\": \"STO Pending GRN from STO Raised Today\"},\n", "    {\n", "        \"name\": \"total_dn_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"STO DN Quantity from STO Raised Today\",\n", "    },\n", "    {\"name\": \"b2b_qty\", \"type\": \"real\", \"description\": \"STO B2B Quantity from STO Raised Today\"},\n", "    {\n", "        \"name\": \"open_sto_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Open STO Quantity as of Today from Overall STO Raised across days\",\n", "    },\n", "    {\n", "        \"name\": \"billed_sto_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Billed STO Quantity as of Today from Overall STO Raised across days\",\n", "    },\n", "    {\n", "        \"name\": \"fe_pending_putaway_inventory\",\n", "        \"type\": \"real\",\n", "        \"description\": \"FE Pending Putaway IMS\",\n", "    },\n", "    {\n", "        \"name\": \"be_pending_putaway_inventory\",\n", "        \"type\": \"real\",\n", "        \"description\": \"BE Pending Putaway IMS\",\n", "    },\n", "    {\"name\": \"v1\", \"type\": \"real\", \"description\": \"ARS V1\"},\n", "    {\"name\": \"v2\", \"type\": \"real\", \"description\": \"ARS V2\"},\n", "    {\"name\": \"inward_drop\", \"type\": \"real\", \"description\": \"ARS Inward Capacity Drop\"},\n", "    {\"name\": \"storage_drop\", \"type\": \"real\", \"description\": \"ARS Storage Capacity Drop\"},\n", "    {\"name\": \"truck_load_drop\", \"type\": \"real\", \"description\": \"ARS Truck Capacity Drop\"},\n", "    {\n", "        \"name\": \"picking_capacity_quantity_drop\",\n", "        \"type\": \"real\",\n", "        \"description\": \"ARS Picking Quantity Capacity Drop\",\n", "    },\n", "    {\n", "        \"name\": \"picking_capacity_sku_drop\",\n", "        \"type\": \"real\",\n", "        \"description\": \"ARS Picking SKU Capacity Drop\",\n", "    },\n", "    {\"name\": \"po_qty_created\", \"type\": \"real\", \"description\": \"PO Created Quantity\"},\n", "    {\n", "        \"name\": \"scheduled_po_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Scheduled PO Quantity from PO Created Today\",\n", "    },\n", "    {\n", "        \"name\": \"unscheduled_po_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Unscheduled PO Quantity from PO Created Today\",\n", "    },\n", "    {\"name\": \"po_grn_qty\", \"type\": \"real\", \"description\": \"PO GRN Quantity from PO Created Today\"},\n", "    {\n", "        \"name\": \"po_remaining_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Open PO Quantity from PO Created Today\",\n", "    },\n", "    {\n", "        \"name\": \"t_scheduled_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"PO Quantity Scheduled for Current Date from PO Created Today\",\n", "    },\n", "    {\n", "        \"name\": \"t1_scheduled_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"PO Quantity Scheduled for Current Date plus One from PO Created Today\",\n", "    },\n", "    {\n", "        \"name\": \"t2_scheduled_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"PO Quantity Scheduled for Current Date plus Two from PO Created Today\",\n", "    },\n", "    {\n", "        \"name\": \"t3_scheduled_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"PO Quantity Scheduled for Current Date plus Three from PO Created Today\",\n", "    },\n", "    {\n", "        \"name\": \"open_po_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Open PO Quantity as of Today from Overall PO Raised across days\",\n", "    },\n", "    {\n", "        \"name\": \"overall_t_scheduled_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"PO Quantity Scheduled for Current Date as of Today from Overall PO Raised across days\",\n", "    },\n", "    {\n", "        \"name\": \"overall_t1_scheduled_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"PO Quantity Scheduled for Current Date plus One as of Today from Overall PO Raised across days\",\n", "    },\n", "    {\n", "        \"name\": \"overall_t2_scheduled_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"PO Quantity Scheduled for Current Date plus Two as of Today from Overall PO Raised across days\",\n", "    },\n", "    {\n", "        \"name\": \"overall_t3_scheduled_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"PO Quantity Scheduled for Current Date plus Three as of Today from Overall PO Raised across days\",\n", "    },\n", "    {\n", "        \"name\": \"overall_scheduled_po_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Total PO Quantity Scheduled as of Today from Overall PO Raised across days\",\n", "    },\n", "    {\n", "        \"name\": \"overall_unscheduled_po_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Total PO Quantity Unscheduled as of Today from Overall PO Raised across days\",\n", "    },\n", "    {\n", "        \"name\": \"po_inbound_skus_monthly_capacity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Inbound SKU Monthly Capacity\",\n", "    },\n", "    {\n", "        \"name\": \"po_inbound_skus_daily_capacity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Inbound SKU Daily Capacity\",\n", "    },\n", "    {\n", "        \"name\": \"po_inbound_monthly_capacity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Inbound Quantity Monthly Capacity\",\n", "    },\n", "    {\n", "        \"name\": \"po_inbound_daily_capacity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Inbound Quantity Daily Capacity\",\n", "    },\n", "    {\n", "        \"name\": \"ds_transfer_monthly_capacity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Outbound Quantity Monthly Capacity\",\n", "    },\n", "    {\n", "        \"name\": \"ds_transfer_daily_capacity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Outbound Quantity Daily Capacity\",\n", "    },\n", "    {\"name\": \"cluster\", \"type\": \"varchar\", \"description\": \"Warehouse Cluster\"},\n", "    {\"name\": \"fallback_ptype\", \"type\": \"varchar\", \"description\": \"Fallback Product Type\"},\n", "    {\n", "        \"name\": \"forecast_cpd_be_ptype\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Forecast CPD Warehouse Product Type\",\n", "    },\n", "    {\n", "        \"name\": \"predicted_cpd_be_ptype\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Predicted CPD Warehouse Product Type basis actual Sale\",\n", "    },\n", "    {\n", "        \"name\": \"festive_planned_qty_fe\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Planned Quantity\",\n", "    },\n", "    {\"name\": \"pending_grn_be\", \"type\": \"real\", \"description\": \"Overall Warehouse Pending GRN\"},\n", "    {\"name\": \"pending_grn_fe\", \"type\": \"real\", \"description\": \"Overall Dark Store Pending GRN\"},\n", "    {\"name\": \"inward_capacity\", \"type\": \"real\", \"description\": \"Dark Store Inbound Capacity\"},\n", "    {\"name\": \"spillover\", \"type\": \"real\", \"description\": \"Truck Spillover\"},\n", "    {\"name\": \"store_dist_ratio\", \"type\": \"real\", \"description\": \"Dark Store Distribution\"},\n", "    {\"name\": \"cs_weights\", \"type\": \"real\", \"description\": \"City Store Weights\"},\n", "    {\"name\": \"ch_weights\", \"type\": \"real\", \"description\": \"City Hour Weights\"},\n", "    {\"name\": \"ci_weights\", \"type\": \"real\", \"description\": \"City Item Weights\"},\n", "    {\"name\": \"bs_weights\", \"type\": \"real\", \"description\": \"Warehouse Store Weights\"},\n", "    {\"name\": \"bh_weights\", \"type\": \"real\", \"description\": \"Warehouse Hour Weights\"},\n", "    {\"name\": \"bi_weights\", \"type\": \"real\", \"description\": \"Warehouse Item Weights\"},\n", "    {\"name\": \"city_weights\", \"type\": \"real\", \"description\": \"City Weight\"},\n", "    {\n", "        \"name\": \"be_putaway_pending_6hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending Putaway Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_putaway_pending_6_12hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending Putaway Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_putaway_pending_12_24hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending Putaway Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_putaway_pending_24hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending Putaway Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_grn_pending_6hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending GRN Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_grn_pending_6_12hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending GRN Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_grn_pending_12_24hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending GRN Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_grn_pending_24hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending GRN Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_putlist_pending_6hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending Putlist Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_putlist_pending_6_12hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending Putlist Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_putlist_pending_12_24hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending Putlist Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_putlist_pending_24hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending Putlist Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_unloading_pending_6hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending Unloading Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_unloading_pending_6_12hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending Unloading Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_unloading_pending_12_24hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending Unloading Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"be_unloading_pending_24h\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Warehouse Pending Unloading Buckets\",\n", "    },\n", "    {\"name\": \"po_quantity\", \"type\": \"real\", \"description\": \"PO Quantity\"},\n", "    {\"name\": \"grn_quantity\", \"type\": \"real\", \"description\": \"GRN Quantity\"},\n", "    {\"name\": \"remaining_po_quantity\", \"type\": \"real\", \"description\": \"Remaining PO Quantity\"},\n", "    {\"name\": \"po_fill_rate\", \"type\": \"real\", \"description\": \"PO Fill Rate\"},\n", "    {\"name\": \"po_schedule\", \"type\": \"varchar\", \"description\": \"PO Schedule\"},\n", "    {\"name\": \"po_quantity_multi_grn\", \"type\": \"real\", \"description\": \"PO Quantity (Multi GRN)\"},\n", "    {\"name\": \"grn_quantity_multi_grn\", \"type\": \"real\", \"description\": \"GRN Quantity (Multi GRN)\"},\n", "    {\n", "        \"name\": \"remaining_po_quantity_multi_grn\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Remaining PO Quantity (Multi GRN)\",\n", "    },\n", "    {\"name\": \"po_fill_rate_multi_grn\", \"type\": \"real\", \"description\": \"PO Fill Rate (Multi GRN)\"},\n", "    {\"name\": \"po_schedule_multi_grn\", \"type\": \"varchar\", \"description\": \"PO Schedule (Multi GRN)\"},\n", "    {\n", "        \"name\": \"fe_grn_pending_2hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Dark Store Pending GRN Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"fe_grn_pending_2_4hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Dark Store Pending GRN Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"fe_grn_pending_4_6hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Dark Store Pending GRN Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"fe_grn_pending_6_8hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Dark Store Pending GRN Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"fe_grn_pending_8_12hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Dark Store Pending GRN Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"fe_grn_pending_12hr\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Overall Dark Store Pending GRN Buckets\",\n", "    },\n", "    {\n", "        \"name\": \"fe_item_req_inv\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Dark Store Item Required Inventory\",\n", "    },\n", "    {\n", "        \"name\": \"be_grn_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Total GRN Quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_putaway_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Warehouse Total Putaway Quantity\",\n", "    },\n", "    {\n", "        \"name\": \"fe_item_bucket\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Dark Store Item level buckets (X>A>B)\",\n", "    },\n", "    {\n", "        \"name\": \"be_item_bucket\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Dark Store Item level buckets (X>A>B)\",\n", "    },\n", "    {\n", "        \"name\": \"ranking_top_200_sku\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Ranking of top 200 SKUs\",\n", "    },\n", "    {\n", "        \"name\": \"dt_hour\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Date Hour String\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "a05f7cf4-c427-42af-be09-2632fe5401c5", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"inventory_replenishment_metrics\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"dt_hour\",\n", "        \"fe_facility_id\",\n", "        \"item_id\",\n", "        \"be_facility_id\",\n", "        \"fastival_tag\",\n", "    ],\n", "    \"partition_key\": [\"insert_ds_ist\", \"hour_\"],\n", "    \"incremental_key\": \"dt_hour\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive metrics\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "a01f812b-db12-4b7f-8e7e-1bb68fb34060", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df = read_sql_query(f\"\"\"\n", "\n", "# Define date range\n", "end_date = date(2024, 10, 6)\n", "start_date = date(2024, 7, 1)\n", "date_range = [start_date + timedelta(days=x) for x in range((end_date - start_date).days + 1)][\n", "    ::-1\n", "]  # Reverse order\n", "\n", "for date in date_range:\n", "    festive_metrics_query = f\"\"\"\n", "            SELECT * \n", "            FROM supply_etls.festive_metrics_temp \n", "            WHERE insert_ds_ist = date('{date.strftime('%Y-%m-%d')}')\n", "            AND hour_ = (\n", "                SELECT MAX(hour_) \n", "                FROM supply_etls.festive_metrics_temp \n", "                WHERE insert_ds_ist = date('{date.strftime('%Y-%m-%d')}')\n", "            )\n", "        \"\"\"\n", "    print(f\"Data for date {date.strftime('%Y-%m-%d')} being processed.\")\n", "    to_trino(festive_metrics_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)\n", "    print(f\"Data for date {date.strftime('%Y-%m-%d')} inserted successfully.\")\n", "# , CON_TRINO)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}