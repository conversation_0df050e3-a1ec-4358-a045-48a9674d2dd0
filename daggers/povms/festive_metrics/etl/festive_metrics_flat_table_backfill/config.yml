alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: festive_metrics_flat_table_backfill
dag_type: etl
escalation_priority: low
execution_timeout: 4000
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U078HMPLR7H
path: povms/festive_metrics/etl/festive_metrics_flat_table_backfill
paused: false
pool: povms_pool
project_name: festive_metrics
schedule:
  end_date: '2025-03-21T00:00:00'
  interval: 30 8 17 * *
  start_date: '2025-03-20T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 7
