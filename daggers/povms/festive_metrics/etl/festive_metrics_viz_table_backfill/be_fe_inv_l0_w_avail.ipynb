{"cells": [{"cell_type": "code", "execution_count": null, "id": "7d1afb8f-4424-4f13-9dd5-1eedd1a2f852", "metadata": {}, "outputs": [], "source": ["# Parameters\n", "sheet_id = \"1l_WV5QxQV02Yqkd6-SlQ2E7I9dRvEb0mjl_uhKejUfo\"\n", "current_table = \"supply_etls.be_fe_inv_l0_category_weighted_availability\"\n", "sheet_name = \"Backfill_Demand\""]}, {"cell_type": "code", "execution_count": null, "id": "dcdde5a0-e06e-462d-957b-9bd5299efb9d", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "5debec28-d548-45a6-a043-526a45cbf366", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "9f7abdb5-01f8-45c6-9fd1-cd79c08dd52d", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "e5f73372-df78-4aa9-9b85-f118aa09c345", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            return True\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            error_message = str(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)\n", "    return error_message"]}, {"cell_type": "code", "execution_count": null, "id": "e2c5f40f-1e47-4225-91b3-bdcfeb483226", "metadata": {}, "outputs": [], "source": ["def from_sheets(sheet_id, sheet_name):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            try:\n", "                df = pb.from_sheets(sheet_id, sheet_name)\n", "            except:\n", "                df = pb.from_sheets(sheet_id, sheet_name)\n", "\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pulled from sheet in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pulled from sheet in: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "1ab04138-096d-47d9-a3cf-49c7a87346a3", "metadata": {}, "outputs": [], "source": ["def to_sheets(df, sheet_id, sheet_name):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            try:\n", "                pb.to_sheets(df, sheet_id, sheet_name)\n", "            except:\n", "                pb.to_sheets(df, sheet_id, sheet_name)\n", "\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed to sheet in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed to sheet in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "markdown", "id": "1679a7d9-f337-462c-a011-597b27f47934", "metadata": {}, "source": ["## Creating an array for dates"]}, {"cell_type": "code", "execution_count": null, "id": "e5aff366-dbc9-445a-81a1-b525125a01d7", "metadata": {}, "outputs": [], "source": ["df = from_sheets(sheet_id, sheet_name)\n", "# df = pd.read_csv('Inventory Viz Tables Backfill - Backfill_Demand.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "6579624b-37bc-4257-af4e-2874a22eb2a1", "metadata": {}, "outputs": [], "source": ["# Fill NaN values\n", "df[\"table_name\"] = df[\"table_name\"].fillna(\"\").astype(str)\n", "df[\"flag\"] = df[\"flag\"].fillna(0).astype(\"int64\")\n", "\n", "df = df[(df[\"table_name\"] == current_table) & (df[\"flag\"] == 1)]\n", "\n", "df = df.reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "4e5a06e3-cd75-4f3c-a59a-fb3ed9407cb4", "metadata": {}, "outputs": [], "source": ["sheet_update = True\n", "if not df.empty:\n", "    # Convert to datetime\n", "    df[\"start_date\"] = pd.to_datetime(df[\"start_date\"])\n", "    df[\"end_date\"] = pd.to_datetime(df[\"end_date\"])\n", "    # Create the flatten_array\n", "    all_dates = np.array(\n", "        sorted(\n", "            set(\n", "                np.concatenate(\n", "                    [\n", "                        pd.date_range(row[\"start_date\"], row[\"end_date\"])\n", "                        .strftime(\"%Y-%m-%d\")\n", "                        .to_numpy()\n", "                        for _, row in df.iterrows()\n", "                    ]\n", "                )\n", "            )\n", "        )\n", "    )\n", "else:\n", "    sheet_update = False\n", "    all_dates = np.array([])"]}, {"cell_type": "markdown", "id": "9e731450-77ff-406c-9d73-a174d84bde7c", "metadata": {}, "source": ["### Getting dates for last 30 days missing data"]}, {"cell_type": "code", "execution_count": null, "id": "76df95a6-267f-4121-a3d8-937c1ec504ef", "metadata": {}, "outputs": [], "source": ["fetching_dates_query = f\"\"\"\n", "    SELECT distinct insert_ds_ist\n", "    FROM {current_table}\n", "    WHERE insert_ds_ist >= CURRENT_DATE -  INTERVAL '30' DAY\n", "    ORDER BY 1 DESC\n", "\"\"\"\n", "fetched_dates = read_sql_query(fetching_dates_query, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "3be6cab9-eb6e-4b40-883f-d909c97ded52", "metadata": {}, "outputs": [], "source": ["fetched_dates = np.array(fetched_dates)"]}, {"cell_type": "code", "execution_count": null, "id": "ca948658-57e3-4436-ac8d-064d0e7e8d6c", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "\n", "# Get current date\n", "current_date = datetime.today()\n", "\n", "# Loop from (current date - 30 days) to current date\n", "for i in range(31):\n", "    date = (current_date - timed<PERSON>ta(days=i)).strftime(\"%Y-%m-%d\")\n", "    if date not in fetched_dates and date not in all_dates:\n", "        all_dates = np.append(all_dates, date)"]}, {"cell_type": "markdown", "id": "0c91123a-1681-432d-8e7c-9c334c6c963c", "metadata": {}, "source": ["### Adding previous day date if not in all_dates"]}, {"cell_type": "code", "execution_count": null, "id": "269cdfc4-0d41-4eb5-9acd-d05fb23ef3ea", "metadata": {}, "outputs": [], "source": ["prev_date = (current_date - timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "if prev_date not in all_dates:\n", "    all_dates = np.append(all_dates, prev_date)"]}, {"cell_type": "markdown", "id": "ea2120bd-0f3b-4ca9-bd99-05a2f5a3f240", "metadata": {}, "source": ["## Table Configurations"]}, {"cell_type": "code", "execution_count": null, "id": "e81cb0ca-9a82-4cad-b872-f5c00d12131a", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"be_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"express_longtail\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"assortment\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l0_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"current_backend_fe_weight_avail\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"current_backend_fe_binary_avail\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"backend_fe_weight_avail\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"backend_fe_binary_avail\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "63b1a60c-9573-4f56-8b47-33cbc3c4719e", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"be_fe_inv_l0_category_weighted_availability\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"insert_ds_ist\",\n", "        \"be_facility_id\",\n", "        \"be_facility_name\",\n", "        \"express_longtail\",\n", "        \"assortment\",\n", "        \"l0_id\",\n", "        \"l0_category\",\n", "    ],\n", "    \"partition key\": \"insert_ds_ist\",\n", "    \"incremental_key\": \"insert_ds_ist\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains backend inv l0 weighted availability \",\n", "}"]}, {"cell_type": "markdown", "id": "a8b9fcf6-22ef-46eb-bf74-85b820624090", "metadata": {}, "source": ["## Back Fill for each date in all_dates"]}, {"cell_type": "code", "execution_count": null, "id": "ddcf567f-a8dc-40e0-b591-9a0303b67fa8", "metadata": {}, "outputs": [], "source": ["all_dates"]}, {"cell_type": "code", "execution_count": null, "id": "367f5328-8f95-4ec6-9d1b-c532742197fa", "metadata": {}, "outputs": [], "source": ["def backfill_query_1(date_):\n", "    return f\"\"\"\n", "        with city_hour_weights as (\n", "            select city, order_hour, cast(max_by(weights,updated_at) as real) * 1.00000000000 as ch_weights\n", "            from supply_etls.city_hour_weights\n", "            where date(updated_at) <= date('{date_}')\n", "            group by 1,2\n", "        ),\n", "        \n", "        city_item_weights as (\n", "            select city, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.00000000000 as ci_weights\n", "            from supply_etls.city_item_weights\n", "            where date(updated_at) <= date('{date_}')\n", "            group by 1,2,3\n", "        ),\n", "        \n", "        city_store_weights as (\n", "            select city, facility_id, outlet_id, cast(max_by(store_weight,updated_at) as real) * 1.00000000000 as cs_weights\n", "            from supply_etls.city_store_weights\n", "            where date(updated_at) <= date('{date_}')\n", "            group by 1,2,3\n", "        ),\n", "        \n", "        city_weights as (\n", "            select city, cast(max_by(weight,updated_at) as real) * 1.00000000000 as city_weights\n", "            from supply_etls.city_weights\n", "            where date(updated_at) <= date('{date_}')\n", "            group by 1\n", "        ),\n", "        \n", "        backend_hour_weights as (\n", "            select cast(cast(backend_facility_id as real) as int) backend_facility_id, order_hour, cast(max_by(weights,updated_at) as real) * 1.00000000000 as bh_weights\n", "            from supply_etls.backend_hour_weights\n", "            where date(updated_at) <= date('{date_}')\n", "            group by 1,2\n", "        ),\n", "        \n", "        backend_item_weights as (\n", "            select backend_facility_id, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.00000000000 as bi_weights\n", "            from supply_etls.backend_item_weights\n", "            where date(updated_at) <= date('{date_}')\n", "            group by 1,2,3\n", "        ),\n", "        \n", "        backend_store_weights as (\n", "            select cast(backend_facility_id as int) backend_facility_id, facility_id, outlet_id, cast(max_by(store_weight,updated_at) as real) * 1.00000000000 as bs_weights \n", "            from supply_etls.backend_store_weights\n", "            where date(updated_at) <= date('{date_}')\n", "            group by 1,2,3\n", "        ),\n", "        \n", "        fmt as (\n", "        select insert_ds_ist, hour_, t.be_facility_id, t.be_facility_name, fe_facility_id, fe_inv_outlet_id, fe_inv_outlet_name, t.express_longtail,\n", "        l0_id, l0_category, \n", "        l1_id, l1_category, l2_id, l2_category, p_type , item_id,\n", "        case when item_substate = 1 then 'active' \n", "             when item_substate = 2 then 'inactive' \n", "             when item_substate = 3 then 'temp_inactive' \n", "             else 'discontinued' end as assortment,\n", "        fe_avail_flag\n", "        from \n", "        supply_etls.inventory_replenishment_metrics t\n", "        where insert_ds_ist = date('{date_}')\n", "        and active_outlet_flag=1\n", "        ),\n", "        \n", "        be_be as (\n", "            select fmt.insert_ds_ist, fmt.hour_, fmt.be_facility_id, be_facility_name, fe_inv_outlet_id, \n", "            fe_inv_outlet_name, express_longtail, l0_id, l0_category, l1_id, l1_category,\n", "            l2_id, l2_category, p_type, fmt.item_id, assortment, fe_avail_flag,\n", "            bs_weights, bi_weights, bh_weights\n", "            from fmt\n", "            left join backend_hour_weights bh on fmt.be_facility_id = bh.backend_facility_id and fmt.hour_ = bh.order_hour\n", "            left join backend_item_weights bi on (fmt.be_facility_id) = bi.backend_facility_id and fmt.item_id = bi.item_id\n", "            left join backend_store_weights bs on fmt.be_facility_id = bs.backend_facility_id and fmt.fe_facility_id = bs.facility_id\n", "        )\n", "        \n", "            select b.insert_ds_ist, b.be_facility_id, b.be_facility_name, \n", "            b.express_longtail,\n", "            b.assortment ,\n", "            b.l0_id, b.l0_category,\n", "            sum(fe_avail_flag*bi_weights*bh_weights*bs_weights)*1.000000/nullif(sum(bi_weights*bh_weights*bs_weights),0) as backend_fe_weight_avail,\n", "            SUM(fe_avail_flag) * 1.00000000 / count(fe_avail_flag) AS backend_fe_binary_avail,\n", "            NULL AS current_backend_fe_weight_avail,\n", "            NULL AS current_backend_fe_binary_avail\n", "            from be_be b\n", "            group by 1,2,3,4,5,6,7\n", "    \"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "23c0858a-4045-4c8c-be9b-59f2d9495ca6", "metadata": {"tags": []}, "outputs": [], "source": ["for date_ in all_dates:\n", "    print(f\"Backfilling data for {date_} :\\n\\n\")\n", "    result_1 = to_trino(\n", "        backfill_query_1(date_), kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs\n", "    )"]}, {"cell_type": "markdown", "id": "ded18108-0289-4200-b3a9-0ec9b12a116f", "metadata": {}, "source": ["## Updating Sheet "]}, {"cell_type": "code", "execution_count": null, "id": "a1fba0ea-6dc1-4071-aa7e-f927ea584728", "metadata": {}, "outputs": [], "source": ["data = None\n", "if sheet_update:\n", "    data = from_sheets(sheet_id, sheet_name)\n", "    data[\"table_name\"] = data[\"table_name\"].fillna(\"\").astype(str)\n", "    data[\"flag\"] = data[\"flag\"].fillna(0).astype(\"int64\")\n", "    data.loc[(data[\"table_name\"] == current_table) & (data[\"flag\"] == 1), \"flag\"] = 0\n", "    to_sheets(data, sheet_id, sheet_name)\n", "    print(\"Date Updated in Sheet\")\n", "data"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}