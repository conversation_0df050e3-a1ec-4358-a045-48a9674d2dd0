alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 3
dag_name: festive_metrics_viz_table_backfill
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebooks:
- executor_config:
    load_type: tiny
    node_type: spot
  name: unscheduled_po_qty
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_pending_putaway
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: fe_pending_grn
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_pending_grn
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_fe_sales
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: truncation_v1_not_created
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: truncation_low_doi_sku
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: truncation_oos_sku
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: truncation_two_doi
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_x_fe_w_avail
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_fe_inv_w_avail
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_inv_w_avail
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: city_w_avail
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: fe_w_avail
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: fe_invt_cpd
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_invt_cpd
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: fe_truncation_type_drop
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_x_fe_l0_w_avail
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_fe_inv_l0_w_avail
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_inv_l0_w_avail
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: city_l0_w_avail
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: fe_l0_w_avail
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_x_fe_ptype_w_avail
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_fe_inv_ptype_w_avail
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_inv_ptype_w_avail
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: city_ptype_w_avail
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: fe_ptype_w_avail
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_truncation_type_drop
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: assortment_coverage
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: assortment_coverage_fe
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: sto_festive_capacity
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: sku_inactive_planning_sheet
  parameters: null
  retries: 2
  tag: first
owner:
  email: <EMAIL>
  slack_id: U08E61V4FRQ
path: povms/festive_metrics/etl/festive_metrics_viz_table_backfill
paused: false
pool: povms_pool
project_name: festive_metrics
schedule:
  end_date: '2025-09-04T00:00:00'
  interval: 30 2 * * *
  start_date: '2025-04-02T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 7
