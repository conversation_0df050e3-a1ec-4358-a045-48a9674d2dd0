{"cells": [{"cell_type": "code", "execution_count": null, "id": "cc2a3835-1bc6-43cf-b28c-fafe81e4038f", "metadata": {"tags": []}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e717d3fd-582e-49e1-9235-1d7852b43887", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "57bdc0aa-5ff7-4153-a57b-6c5af2c836c8", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "d41b0124-d898-4660-9eee-ea465f506abb", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "6b0b952e-ceff-4cba-909d-e0dd4e694e0d", "metadata": {}, "outputs": [], "source": ["festive_metrics_query = f\"\"\"\n", "\n", "WITH tea_tagging AS (\n", "select fe_outlet_id, fe_facility_id, item_id, assortment_type, be_hot_outlet_id, be_inv_outlet_id, be_facility_id, assortment_status_id\n", "from supply_etls.inventory_metrics_tea_tagging\n", "where flag = 'correct_tea' and be_facility_id <> fe_facility_id ),\n", "\n", "packaged_goods_outlet AS (\n", "SELECT  facility_id, facility_name, inv_outlet_id, hot_outlet_id, city_name, taggings\n", "FROM    supply_etls.outlet_details\n", "WHERE   taggings IN ('be', 'fe') AND store_type IN ('Packaged Goods', 'Dark Store') AND ars_check = 1 ),\n", "\n", "pg_items AS (\n", "select  item_id, item_name, l0_id, l0_category, l1_id, l1_category, l2_id, l2_category, p_type\n", "FROM    supply_etls.item_details\n", "WHERE   handling_type = 'Non Packaging Material' AND assortment_type = 'Packaged Goods' ),\n", "\n", "cluster_city_mapping AS (\n", "SELECT  m.city_id, c.cluster_id, c.cluster_name\n", "FROM        rpc.ams_city_cluster_mapping m\n", "INNER JOIN  rpc.ams_cluster c \n", "            ON  c.cluster_id = m.cluster_id and c.cluster_id >= 15000 AND m.lake_active_record = true AND m.active = true \n", "                AND c.lake_active_record = true ),\n", "\n", "proposed_store_movement as (\n", "select  outlet_id, festival_name, proposed_be_facility_id as be_facility_id\n", "from supply_etls.festival_tea_tagging_v2\n", "WHERE active = 1 AND festival_name IS NOT NULL ),\n", "\n", "event_name as (\n", "SELECT sei.id AS id,\n", "    CONCAT(name, '_', cast(start_date as varchar)) AS event_name,\n", "    start_date,\n", "    end_date\n", "FROM rpc.supply_event_info sei\n", "JOIN rpc.supply_event se ON se.id = sei.supply_event_id\n", "WHERE sei.active = TRUE\n", "  AND se.active = TRUE\n", "),\n", "\n", "planner as (\n", "select \n", "    location_id,\n", "    event_id,\n", "    item_id, \n", "    event_name as festival_name,\n", "    start_date AS festival_start_date,\n", "    end_date AS festival_end_date,\n", "    sale_start_date, \n", "    sale_end_date, \n", "    cut_off_date, \n", "    assortment_type, \n", "    assortment_launch_type, \n", "    substitute_ptype\n", "from ars.bulk_process_event_planner bpep\n", "join event_name en on bpep.event_id=en.id\n", "where partition_field is not null and active = 1\n", "), \n", "\n", "store_level_data as (\n", "select \n", "    event_id, \n", "    city_id,\n", "    outlet_id,\n", "    item_id, \n", "    quantity\n", "from ars.event_outlet_item_distribution where partition_field is not null and active = 1 \n", "), \n", "\n", "assortment_tea as (\n", "        select \n", "            cast(outlet_id as int) as outlet_id, \n", "            longtail_be_outlet_id as be_outlet_id, \n", "            'LONGTAIL' as assortment_type\n", "        from supply_etls.hybrid_longtail_backend_mapping     \n", "            where tagging = 'LONGTAIL'\n", "            \n", "        union \n", "        \n", "        select \n", "            cast(outlet_id as int) as outlet_id, \n", "            longtail_be_outlet_id as be_outlet_id, \n", "            'HYBRID' as assortment_type\n", "        from supply_etls.hybrid_longtail_backend_mapping     \n", "            where launch_type = 'HYBRID' \n", "            \n", "        union \n", "        \n", "        select \n", "            cast(fe_outlet_id as int) as outlet_id, \n", "            be_outlet_id, \n", "            'EXPRESS' as assortment_type\n", "        from supply_etls.invs_tea_tagging\n", "        where \n", "            ((count_teas = 1) or (count_teas = 2 and be_facility_id = 2076) or (count_teas = 2 and be_facility_id = 2124)) -- Split assortment case in Dehradun\n", "            and carts > 10 \n", "),\n", "\n", "store_planned_distribution AS (\n", "SELECT      p.festival_name, COALESCE(t.be_facility_id, be.facility_id) AS be_facility_id\n", "            , oid.outlet_id AS frontend_outlet_id, fe.facility_id AS fe_facility_id, oid.item_id, p.festival_start_date, p.festival_end_date\n", "            , p.sale_start_date, p.sale_end_date\n", "            , (CASE WHEN t.be_facility_id IS NOT NULL THEN 1 ELSE 0 END) AS tea_tagged_flag\n", "            , (CASE WHEN t.be_facility_id IS NULL AND oid.quantity > 0 THEN oid.quantity ELSE 0 END) AS dynamic_planned_qty_untagged_be\n", "            , (CASE WHEN t.be_facility_id IS NOT NULL AND oid.quantity > 0 THEN oid.quantity ELSE 0 END) AS dynamic_planned_qty_tagged_be\n", "\n", "FROM        planner p\n", "INNER JOIN  cluster_city_mapping m \n", "            ON  m.cluster_id = p.location_id  \n", "INNER JOIN  store_level_data oid\n", "            ON  oid.city_id = m.city_id and oid.item_id = p.item_id AND oid.event_id = p.event_id\n", "LEFT JOIN   tea_tagging t\n", "            ON oid.outlet_id = t.fe_outlet_id AND oid.item_id = t.item_id\n", "LEFT JOIN   assortment_tea a\n", "            ON oid.outlet_id = a.outlet_id AND a.assortment_type = p.assortment_type\n", "LEFT JOIN   packaged_goods_outlet be \n", "            ON be.hot_outlet_id = a.be_outlet_id AND be.taggings = 'be' \n", "LEFT JOIN   packaged_goods_outlet fe \n", "            ON fe.hot_outlet_id = oid.outlet_id AND fe.taggings = 'fe' ),\n", "\n", "festive_sales_duration_ts AS (\n", "SELECT      festival_name, CAST(MIN(sale_start_date) AS TIMESTAMP) AS sale_start_ts\n", "            , CAST((MAX(sale_end_date) + INTERVAL '1' DAY) AS TIMESTAMP) AS sale_end_ts_plus1\n", "            , DATE_ADD('DAY', -30, MIN(sale_start_date)) AS last30d_PO_check\n", "FROM        planner p\n", "GROUP BY    1 ),\n", "\n", "base AS (\n", "SELECT      festival_name, be_facility_id, be.inv_outlet_id AS be_inv_outlet_id, be.facility_name AS be_facility_name, item_id\n", "            , MIN(festival_start_date) AS festival_start_date, MAX(festival_end_date) AS festival_end_date\n", "            , MIN(sale_start_date) AS sale_start_date, MAX(sale_end_date) AS sale_end_date\n", "            , COUNT(CASE WHEN tea_tagged_flag = 0 THEN frontend_outlet_id END) AS untagged_stores\n", "            , SUM(tea_tagged_flag) AS tea_tagged_flag, SUM(dynamic_planned_qty_untagged_be) AS dynamic_planned_qty_untagged_be\n", "            , SUM(dynamic_planned_qty_tagged_be) AS dynamic_planned_qty_tagged_be\n", "            , DATE_ADD('DAY', -30, MIN(sale_start_date)) AS last30d_PO_check\n", "\n", "            , (CASE WHEN DATE_DIFF('DAY', CURRENT_DATE, MIN(sale_start_date)) > 0 THEN DATE_DIFF('DAY', CURRENT_DATE, MIN(sale_start_date)) \n", "                    ELSE 0 \n", "                END) AS festival_start_countdown\n", "    \n", "            , (CASE WHEN DATE_DIFF('DAY', CURRENT_DATE, MAX(sale_end_date)) > 0 THEN DATE_DIFF('DAY', CURRENT_DATE, MAX(sale_end_date))\n", "                    ELSE 0 \n", "                END) AS festival_end_countdown\n", "    \n", "            , (CASE WHEN (DATE_DIFF('DAY', MIN(sale_start_date), MAX(sale_end_date)) + 1) > 0 THEN (DATE_DIFF('DAY', MIN(sale_start_date), MAX(sale_end_date)) + 1)\n", "                    ELSE 0 \n", "                END) AS festival_duration\n", "\n", "FROM        store_planned_distribution f\n", "LEFT JOIN   packaged_goods_outlet be \n", "            ON f.be_facility_id = be.facility_id AND be.taggings = 'be' \n", "GROUP BY 1,2,3,4,5 ),\n", "\n", "po_data AS (\n", "SELECT  b.festival_name, b.be_facility_id, b.item_id, DATE(p.po_created_at_ist) AS po_created_date, DATE(p.po_expiry_at_ist) AS po_expiry_date\n", "        , DATE(p.po_schedule_at_ist) AS po_scheduled_date, b.last30d_PO_check, p.po_state_id, p.po_status, p.po_id, p.po_number, p.is_multiple_grn\n", "        , (CASE WHEN p.po_state_id IN (2,13,15) OR (p.po_state_id = 9 AND p.is_multiple_grn = 1) THEN 1 ELSE 0 END) AS open_po_flag\n", "        , p.po_quantity, p.grn_quantity, p.remaining_po_quantity, b.sale_start_date, b.sale_end_date, p.last_item_grn_at_ist\n", "\n", "FROM        base b\n", "INNER JOIN  supply_etls.inventory_metrics_purchase_mis p\n", "            ON  p.facility_id = b.be_facility_id AND p.item_id = b.item_id  \n", "WHERE       p.insert_ds_ist IS NOT NULL AND p.insert_ds_ist >= (SELECT MIN(last30d_PO_check) FROM festive_sales_duration_ts) AND p.po_to_be_consider = 1 \n", "            AND p.po_created_at_ist >= (SELECT CAST(MIN(last30d_PO_check) AS TIMESTAMP) FROM festive_sales_duration_ts) ),\n", "\n", "relevant_po AS (\n", "SELECT  festival_name, be_facility_id, item_id, po_created_date, po_expiry_date, po_scheduled_date, last30d_PO_check, open_po_flag, po_state_id\n", "        , po_quantity, grn_quantity, po_id, po_number, remaining_po_quantity, sale_start_date, sale_end_date, last_item_grn_at_ist\n", "        , (CASE WHEN last_item_grn_at_ist IS NOT NULL THEN \n", "                ROW_NUMBER() OVER (PARTITION BY festival_name, be_facility_id, item_id ORDER BY last_item_grn_at_ist DESC) END) AS rn\n", "\n", "FROM    po_data\n", "WHERE   po_created_date >= last30d_PO_check AND (po_scheduled_date <= sale_end_date OR po_expiry_date <= sale_end_date) ),\n", "\n", "festive_po_info AS (\n", "SELECT      festival_name, CAST(be_facility_id AS INT) AS be_facility_id, item_id\n", "\n", "            , SUM(CASE WHEN po_created_date >= last30d_PO_check THEN po_quantity ELSE 0 END) AS po_qty_last30d_fest_start\n", "            , SUM(CASE WHEN po_created_date >= last30d_PO_check THEN grn_quantity ELSE 0 END) AS grn_qty_last30d_fest_start\n", "            \n", "            , SUM(CASE WHEN po_created_date >= last30d_PO_check AND open_po_flag = 1 AND remaining_po_quantity > 0 \n", "                    THEN remaining_po_quantity END) AS open_po_qty_last30d_fest_start\n", "\n", "            , SUM(CASE WHEN po_created_date >= last30d_PO_check AND open_po_flag = 0 AND remaining_po_quantity > 0 \n", "                    THEN remaining_po_quantity END) AS closed_po_qty_last30d_fest_start\n", "\n", "            , SUM(CASE WHEN po_created_date >= last30d_PO_check AND open_po_flag = 1 AND remaining_po_quantity > 0 \n", "                    AND po_scheduled_date IS NOT NULL THEN remaining_po_quantity END) AS scheduled_po_qty_last30d_fest_start\n", "\n", "            , SUM(CASE WHEN po_created_date >= last30d_PO_check AND open_po_flag = 1 AND remaining_po_quantity > 0 \n", "                    AND po_scheduled_date IS NULL THEN remaining_po_quantity END) AS unscheduled_po_qty_last30d_fest_start\n", "\n", "            , SUM(CASE WHEN po_created_date >= last30d_PO_check AND open_po_flag = 1 AND remaining_po_quantity > 0 \n", "                    AND po_scheduled_date < CURRENT_DATE AND po_scheduled_date != CURRENT_DATE THEN remaining_po_quantity END) AS scheduled_po_qty_past_today_last30d_fest_start\n", "\n", "            , SUM(CASE WHEN po_created_date >= last30d_PO_check AND open_po_flag = 1 AND remaining_po_quantity > 0 \n", "                    AND po_scheduled_date > CURRENT_DATE AND po_scheduled_date != CURRENT_DATE THEN remaining_po_quantity END) AS scheduled_po_qty_post_today_last30d_fest_start\n", "\n", "            , SUM(CASE WHEN po_created_date >= last30d_PO_check AND open_po_flag = 1 AND po_state_id = 9 AND remaining_po_quantity > 0 \n", "                    THEN remaining_po_quantity END) AS fulfilled_po_qty_last30d_fest_start\n", "\n", "            -- , SUM(CASE WHEN po_created_date >= last30d_PO_check AND open_po_flag = 0 AND remaining_po_quantity > 0 \n", "            --         THEN remaining_po_quantity END) AS expired_qty_wo_deliverd_last30d_fest_start\n", "\n", "            , SUM(CASE WHEN po_created_date >= sale_start_date AND po_created_date <= sale_end_date\n", "                        THEN po_quantity ELSE 0 END) AS po_qty_during_fest_duration\n", "                        \n", "            , SUM(CASE WHEN po_created_date >= sale_start_date AND po_created_date <= sale_end_date\n", "                        THEN grn_quantity ELSE 0 END) AS grn_qty_during_fest_duration\n", "\n", "            , SUM(CASE WHEN po_created_date >= sale_start_date AND po_created_date <= sale_end_date \n", "                        AND open_po_flag = 1 AND remaining_po_quantity > 0 THEN remaining_po_quantity END) AS open_po_qty_during_fest_duration\n", "                        \n", "            , SUM(CASE WHEN po_created_date >= sale_start_date AND po_created_date <= sale_end_date \n", "                        AND open_po_flag = 0 AND remaining_po_quantity > 0 THEN remaining_po_quantity END) AS closed_po_qty_during_fest_duration\n", "\n", "            , SUM(CASE WHEN po_created_date >= sale_start_date AND po_created_date <= sale_end_date AND open_po_flag = 1 \n", "                    AND remaining_po_quantity > 0 AND po_scheduled_date IS NOT NULL THEN remaining_po_quantity END) AS scheduled_po_qty_fest_duration\n", "\n", "            , SUM(CASE WHEN po_created_date >= sale_start_date AND po_created_date <= sale_end_date AND open_po_flag = 1 \n", "                    AND remaining_po_quantity > 0 AND po_scheduled_date < CURRENT_DATE THEN remaining_po_quantity END) AS scheduled_po_qty_past_today_fest_duration\n", "\n", "            , SUM(CASE WHEN po_created_date >= sale_start_date AND open_po_flag = 1 AND po_scheduled_date = CURRENT_DATE AND remaining_po_quantity > 0 THEN remaining_po_quantity END) AS t_overall_po_scheduled_qty_fest_duration\n", "\n", "            , SUM(CASE WHEN po_created_date >= sale_start_date AND po_created_date <= sale_end_date AND open_po_flag = 1 \n", "                    AND remaining_po_quantity > 0 AND po_scheduled_date > CURRENT_DATE THEN remaining_po_quantity END) AS scheduled_po_qty_post_today_fest_duration\n", "\n", "            , SUM(CASE WHEN po_created_date >= sale_start_date AND po_created_date <= sale_end_date AND open_po_flag = 1 \n", "                    AND remaining_po_quantity > 0 AND po_scheduled_date IS NULL THEN remaining_po_quantity END) AS unscheduled_po_qty_fest_duration\n", "\n", "            , SUM(CASE WHEN po_created_date >= sale_start_date AND po_created_date <= sale_end_date\n", "                        AND open_po_flag = 1 AND po_state_id = 9 AND remaining_po_quantity > 0 THEN remaining_po_quantity END) AS fulfilled_po_qty_fest_duration\n", "\n", "            -- , SUM(CASE WHEN po_created_date >= sale_start_date AND po_created_date <= sale_end_date AND open_po_flag = 0 \n", "            --             AND remaining_po_quantity > 0 THEN remaining_po_quantity END) AS expired_qty_wo_deliverd_during_fest_duration\n", "\n", "        , COUNT(CASE WHEN po_created_date >= last30d_PO_check THEN po_number END) AS po_count_last30d_fest_start\n", "\n", "        , COUNT(CASE WHEN po_created_date >= last30d_PO_check AND remaining_po_quantity = 0 \n", "                THEN po_number END) AS po_count_delivered_last30d_fest_start\n", "\n", "        , COUNT(CASE WHEN po_created_date >= last30d_PO_check AND open_po_flag = 1 AND remaining_po_quantity > 0 \n", "                THEN po_number END) AS open_po_count_last30d_fest_start\n", "\n", "        -- , COUNT(CASE WHEN po_created_date >= last30d_PO_check AND open_po_flag = 0 AND remaining_po_quantity > 0 \n", "        --             THEN po_number END) AS expired_po_wo_deliverd_count_last30d_fest_start\n", "\n", "            , COUNT(CASE WHEN open_po_flag = 1 AND po_scheduled_date = CURRENT_DATE AND remaining_po_quantity > 0 THEN po_number END) AS t_overall_po_scheduled_cnt\n", "            , COUNT(CASE WHEN open_po_flag = 1 AND po_scheduled_date = CURRENT_DATE + INTERVAL '1' DAY AND remaining_po_quantity > 0 THEN po_number END) AS t1_overall_po_scheduled_cnt\n", "            , COUNT(CASE WHEN open_po_flag = 1 AND po_scheduled_date = CURRENT_DATE + INTERVAL '2' DAY AND remaining_po_quantity > 0 THEN po_number END) AS t2_overall_po_scheduled_cnt\n", "            , COUNT(CASE WHEN open_po_flag = 1 AND po_scheduled_date = CURRENT_DATE + INTERVAL '3' DAY AND remaining_po_quantity > 0 THEN po_number END) AS t3_overall_po_scheduled_cnt\n", "            , COUNT(CASE WHEN open_po_flag = 1 AND po_scheduled_date = CURRENT_DATE + INTERVAL '4' DAY AND remaining_po_quantity > 0 THEN po_number END) AS t4_overall_po_scheduled_cnt\n", "            , COUNT(CASE WHEN open_po_flag = 1 AND po_scheduled_date = CURRENT_DATE + INTERVAL '5' DAY AND remaining_po_quantity > 0 THEN po_number END) AS t5_overall_po_scheduled_cnt\n", "            \n", "            , SUM(CASE WHEN open_po_flag = 1 AND po_scheduled_date = CURRENT_DATE AND remaining_po_quantity > 0 THEN remaining_po_quantity END) AS t_overall_po_scheduled_qty\n", "            , SUM(CASE WHEN open_po_flag = 1 AND po_scheduled_date = CURRENT_DATE + INTERVAL '1' DAY AND remaining_po_quantity > 0 THEN remaining_po_quantity END) AS t1_overall_po_scheduled_qty\n", "            , SUM(CASE WHEN open_po_flag = 1 AND po_scheduled_date = CURRENT_DATE + INTERVAL '2' DAY AND remaining_po_quantity > 0 THEN remaining_po_quantity END) AS t2_overall_po_scheduled_qty\n", "            , SUM(CASE WHEN open_po_flag = 1 AND po_scheduled_date = CURRENT_DATE + INTERVAL '3' DAY AND remaining_po_quantity > 0 THEN remaining_po_quantity END) AS t3_overall_po_scheduled_qty\n", "            , SUM(CASE WHEN open_po_flag = 1 AND po_scheduled_date = CURRENT_DATE + INTERVAL '4' DAY AND remaining_po_quantity > 0 THEN remaining_po_quantity END) AS t4_overall_po_scheduled_qty\n", "            , SUM(CASE WHEN open_po_flag = 1 AND po_scheduled_date = CURRENT_DATE + INTERVAL '5' DAY AND remaining_po_quantity > 0 THEN remaining_po_quantity END) AS t5_overall_po_scheduled_qty\n", "    \n", "            , COUNT(CASE WHEN open_po_flag = 1 AND po_expiry_date = CURRENT_DATE AND po_expiry_date <= sale_end_date AND remaining_po_quantity > 0 \n", "                    THEN po_number END) AS t_overall_po_expired_cnt\n", "                    \n", "            , COUNT(CASE WHEN open_po_flag = 1 AND po_expiry_date = CURRENT_DATE + INTERVAL '1' DAY AND po_expiry_date <= sale_end_date AND remaining_po_quantity > 0\n", "                    THEN po_number END) AS t1_overall_po_expired_cnt\n", "                    \n", "            , COUNT(CASE WHEN open_po_flag = 1 AND po_expiry_date = CURRENT_DATE + INTERVAL '2' DAY AND po_expiry_date <= sale_end_date AND remaining_po_quantity > 0\n", "                    THEN po_number END) AS t2_overall_po_expired_cnt\n", "                    \n", "            , COUNT(CASE WHEN open_po_flag = 1 AND po_expiry_date = CURRENT_DATE + INTERVAL '3' DAY AND po_expiry_date <= sale_end_date AND remaining_po_quantity > 0\n", "                    THEN po_number END) AS t3_overall_po_expired_cnt\n", "                    \n", "            , COUNT(CASE WHEN open_po_flag = 1 AND po_expiry_date = CURRENT_DATE + INTERVAL '4' DAY AND po_expiry_date <= sale_end_date AND remaining_po_quantity > 0\n", "                    THEN po_number END) AS t4_overall_po_expired_cnt\n", "                    \n", "            , COUNT(CASE WHEN open_po_flag = 1 AND po_expiry_date = CURRENT_DATE + INTERVAL '5' DAY AND po_expiry_date <= sale_end_date AND remaining_po_quantity > 0\n", "                    THEN po_number END) AS t5_overall_po_expired_cnt\n", "    \n", "            , SUM(CASE WHEN open_po_flag = 1 AND po_expiry_date = CURRENT_DATE AND po_expiry_date <= sale_end_date AND remaining_po_quantity > 0 \n", "                    THEN remaining_po_quantity END) AS t_overall_po_expired_qty\n", "            \n", "              , SUM(CASE WHEN open_po_flag = 1 AND po_expiry_date = CURRENT_DATE + INTERVAL '1' DAY AND po_expiry_date <= sale_end_date AND remaining_po_quantity > 0 \n", "                    THEN remaining_po_quantity END) AS t1_overall_po_expired_qty\n", "                    \n", "            , SUM(CASE WHEN open_po_flag = 1 AND po_expiry_date = CURRENT_DATE + INTERVAL '2' DAY AND po_expiry_date <= sale_end_date AND remaining_po_quantity > 0 \n", "                    THEN remaining_po_quantity END) AS t2_overall_po_expired_qty\n", "\n", "            , SUM(CASE WHEN open_po_flag = 1 AND po_expiry_date = CURRENT_DATE + INTERVAL '3' DAY  AND po_expiry_date <= sale_end_date AND remaining_po_quantity > 0\n", "                    THEN remaining_po_quantity END) AS t3_overall_po_expired_qty\n", "                    \n", "            , SUM(CASE WHEN open_po_flag = 1 AND po_expiry_date = CURRENT_DATE + INTERVAL '4' DAY AND po_expiry_date <= sale_end_date AND remaining_po_quantity > 0\n", "                    THEN remaining_po_quantity END) AS t4_overall_po_expired_qty\n", "\n", "            , SUM(CASE WHEN open_po_flag = 1 AND po_expiry_date = CURRENT_DATE + INTERVAL '5' DAY AND po_expiry_date <= sale_end_date AND remaining_po_quantity > 0\n", "                    THEN remaining_po_quantity END) AS t5_overall_po_expired_qty\n", "\n", "            , SUM(CASE WHEN open_po_flag = 1 AND po_expiry_date = CURRENT_DATE AND po_expiry_date <= sale_end_date AND remaining_po_quantity > 0 \n", "                    AND po_scheduled_date IS NULL THEN remaining_po_quantity END) AS t_overall_po_expired_unscheduled_qty\n", "\n", "            , SUM(CASE WHEN open_po_flag = 1 AND po_expiry_date = CURRENT_DATE AND po_expiry_date <= sale_end_date AND remaining_po_quantity > 0 \n", "                    AND po_scheduled_date < CURRENT_DATE THEN remaining_po_quantity END) AS t_overall_po_expired_past_today_scheduled_qty\n", "\n", "            , SUM(CASE WHEN open_po_flag = 1 AND po_expiry_date = CURRENT_DATE AND po_expiry_date <= sale_end_date AND remaining_po_quantity > 0 \n", "                    AND po_scheduled_date = CURRENT_DATE THEN remaining_po_quantity END) AS t_overall_po_expired_today_scheduled_qty\n", "\n", "            , COUNT(CASE WHEN po_created_date = CURRENT_DATE - INTERVAL '1' DAY THEN po_number END) AS y_po_created_cnt\n", "            \n", "            , COUNT(CASE WHEN po_created_date = CURRENT_DATE - INTERVAL '1' DAY AND po_scheduled_date = CURRENT_DATE - INTERVAL '1' DAY \n", "                    AND remaining_po_quantity > 0 THEN po_number END) AS y_po_scheduled_cnt\n", "            \n", "            , COUNT(CASE WHEN po_created_date = CURRENT_DATE - INTERVAL '1' DAY AND po_state_id = 14 AND remaining_po_quantity > 0 \n", "                    THEN po_number END) AS y_po_unscheduled_cnt\n", "    \n", "            , SUM(CASE WHEN po_created_date = CURRENT_DATE - INTERVAL '1' DAY THEN po_quantity END) AS y_po_created_qty\n", "            \n", "            , SUM(CASE WHEN po_created_date = CURRENT_DATE - INTERVAL '1' DAY AND po_scheduled_date = CURRENT_DATE - INTERVAL '1' DAY \n", "                    AND remaining_po_quantity > 0 THEN remaining_po_quantity END) AS y_po_scheduled_qty\n", "                    \n", "            , SUM(CASE WHEN po_created_date = CURRENT_DATE - INTERVAL '1' DAY AND po_state_id = 14 AND remaining_po_quantity > 0 \n", "                    THEN remaining_po_quantity END) AS y_po_unscheduled_qty\n", "    \n", "            , COUNT(CASE WHEN po_created_date = CURRENT_DATE THEN po_number END) AS t_po_created_cnt\n", "    \n", "            , COUNT(CASE WHEN po_created_date = CURRENT_DATE AND po_scheduled_date = CURRENT_DATE AND remaining_po_quantity > 0 \n", "                    THEN po_number END) AS t_po_scheduled_cnt\n", "    \n", "            , COUNT(CASE WHEN po_created_date = CURRENT_DATE AND po_state_id = 14 AND remaining_po_quantity > 0 \n", "                    THEN po_number END) AS t_po_unscheduled_cnt\n", "    \n", "            , SUM(CASE WHEN po_created_date = CURRENT_DATE THEN po_quantity END) AS t_po_created_qty\n", "\n", "            , SUM(CASE WHEN po_created_date = CURRENT_DATE AND po_scheduled_date = CURRENT_DATE AND remaining_po_quantity > 0 \n", "                    THEN remaining_po_quantity END) AS t_po_scheduled_qty\n", "                    \n", "            , SUM(CASE WHEN po_created_date = CURRENT_DATE AND po_state_id = 14 AND remaining_po_quantity > 0 \n", "                    THEN remaining_po_quantity END) AS t_po_unscheduled_qty\n", "\n", "            , MAX(CASE WHEN rn = 1 AND last_item_grn_at_ist IS NOT NULL THEN last_item_grn_at_ist ELSE NULL END) AS latest_grn_ts\n", "            , MAX(CASE WHEN rn = 1 AND last_item_grn_at_ist IS NOT NULL THEN po_number ELSE NULL END) AS latest_grn_po_number\n", "            , MAX(CASE WHEN rn = 1 AND last_item_grn_at_ist IS NOT NULL THEN grn_quantity ELSE 0 END) AS latest_grn_qty\n", "            , CAST(SUM(CASE WHEN rn IN (1,2,3) AND last_item_grn_at_ist IS NOT NULL THEN grn_quantity END) AS DOUBLE) AS last3_grn_qty\n", "            , SUM(CASE WHEN rn IN (1,2,3) AND last_item_grn_at_ist IS NOT NULL THEN po_quantity END) AS last3_po_qty\n", "\n", "FROM        relevant_po\n", "GROUP BY    1,2,3 )\n", "\n", "SELECT      CAST(CURRENT_TIMESTAMP AS TIMESTAMP) AS updated_at_ist, current_date as date_,\n", "            b.festival_name, b.be_facility_id, b.be_facility_name, b.item_id, i.item_name, i.l0_id, i.l0_category, i.l1_id, i.l1_category\n", "            , i.l2_id, i.l2_category, i.p_type, b.festival_start_date, b.festival_end_date, b.sale_start_date, b.sale_end_date, b.last30d_PO_check\n", "            , b.dynamic_planned_qty_untagged_be, b.dynamic_planned_qty_tagged_be\n", "            \n", "            , COALESCE(p.po_qty_last30d_fest_start,0) AS po_qty_last30d_fest_start\n", "            , COALESCE(p.grn_qty_last30d_fest_start,0) AS grn_qty_last30d_fest_start\n", "            , COALESCE(p.open_po_qty_last30d_fest_start,0) AS open_po_qty_last30d_fest_start\n", "            , COALESCE(p.closed_po_qty_last30d_fest_start,0) AS closed_po_qty_last30d_fest_start\n", "            , COALESCE(p.scheduled_po_qty_last30d_fest_start,0) AS scheduled_po_qty_last30d_fest_start\n", "            , COALESCE(p.unscheduled_po_qty_last30d_fest_start,0) AS unscheduled_po_qty_last30d_fest_start\n", "            , COALESCE(p.scheduled_po_qty_past_today_last30d_fest_start,0) AS scheduled_po_qty_past_today_last30d_fest_start\n", "            , COALESCE(p.scheduled_po_qty_post_today_last30d_fest_start,0) AS scheduled_po_qty_post_today_last30d_fest_start\n", "\n", "            , COALESCE(p.fulfilled_po_qty_last30d_fest_start,0) AS fulfilled_po_qty_last30d_fest_start\n", "            -- , COALESCE(p.expired_qty_wo_deliverd_last30d_fest_start,0) AS expired_qty_wo_deliverd_last30d_fest_start\n", "            , COALESCE(((CAST(p.grn_qty_last30d_fest_start AS DOUBLE) * 100) / p.po_qty_last30d_fest_start),0) AS po_fill_rate_last30d_fest_start\n", "\n", "            , COALESCE(p.po_qty_during_fest_duration,0) AS po_qty_during_fest_duration\n", "            , COALESCE(p.grn_qty_during_fest_duration,0) AS grn_qty_during_fest_duration\n", "            , COALESCE(p.open_po_qty_during_fest_duration,0) AS open_po_qty_during_fest_duration\n", "            , COALESCE(p.closed_po_qty_during_fest_duration,0) AS closed_po_qty_during_fest_duration\n", "            , COALESCE(p.scheduled_po_qty_fest_duration,0) AS scheduled_po_qty_fest_duration\n", "            , COALESCE(p.unscheduled_po_qty_fest_duration,0) AS unscheduled_po_qty_fest_duration\n", "            , COALESCE(p.scheduled_po_qty_past_today_fest_duration,0) AS scheduled_po_qty_past_today_fest_duration\n", "            , COALESCE(p.scheduled_po_qty_post_today_fest_duration,0) AS scheduled_po_qty_post_today_fest_duration\n", "            , COALESCE(p.t_overall_po_scheduled_qty_fest_duration,0) AS t_overall_po_scheduled_qty_fest_duration\n", "            \n", "            , COALESCE(p.fulfilled_po_qty_fest_duration,0) AS fulfilled_po_qty_fest_duration\n", "            -- , COALESCE(p.expired_qty_wo_deliverd_during_fest_duration,0) AS expired_qty_wo_deliverd_during_fest_duration\n", "            , COALESCE(((CAST(p.grn_qty_during_fest_duration AS DOUBLE) * 100) / p.po_qty_during_fest_duration),0) AS po_fill_rate_during_fest_duration\n", "\n", "            , p.latest_grn_ts\n", "            , COALESCE(p.latest_grn_po_number,'0') AS latest_grn_po_number\n", "            , COALESCE(p.latest_grn_qty,0) AS latest_grn_qty\n", "            , COALESCE(((p.last3_grn_qty * 100) / p.last3_po_qty),0) AS last3po_fill_rate\n", "\n", "            , COALESCE(p.po_count_last30d_fest_start,0) AS po_count_last30d_fest_start\n", "            , COALESCE(p.po_count_delivered_last30d_fest_start,0) AS po_count_delivered_last30d_fest_start\n", "            , COALESCE(p.open_po_count_last30d_fest_start,0) AS open_po_count_last30d_fest_start\n", "            -- , COALESCE(p.expired_po_wo_deliverd_count_last30d_fest_start,0) AS expired_po_wo_deliverd_count_last30d_fest_start\n", "\n", "            , COALESCE(p.t_overall_po_scheduled_cnt,0) AS t_overall_po_scheduled_cnt\n", "            , COALESCE(p.t1_overall_po_scheduled_cnt,0) AS t1_overall_po_scheduled_cnt\n", "            , COALESCE(p.t2_overall_po_scheduled_cnt,0) AS t2_overall_po_scheduled_cnt\n", "            , COALESCE(p.t3_overall_po_scheduled_cnt,0) AS t3_overall_po_scheduled_cnt\n", "            , COALESCE(p.t4_overall_po_scheduled_cnt,0) AS t4_overall_po_scheduled_cnt\n", "            , COALESCE(p.t5_overall_po_scheduled_cnt,0) AS t5_overall_po_scheduled_cnt\n", "            \n", "            , COALESCE(p.t_overall_po_scheduled_qty,0) AS t_overall_po_scheduled_qty\n", "            , COALESCE(p.t1_overall_po_scheduled_qty,0) AS t1_overall_po_scheduled_qty\n", "            , COALESCE(p.t2_overall_po_scheduled_qty,0) AS t2_overall_po_scheduled_qty\n", "            , COALESCE(p.t3_overall_po_scheduled_qty,0) AS t3_overall_po_scheduled_qty\n", "            , COALESCE(p.t4_overall_po_scheduled_qty,0) AS t4_overall_po_scheduled_qty\n", "            , COALESCE(p.t5_overall_po_scheduled_qty,0) AS t5_overall_po_scheduled_qty\n", "                    \n", "            , COALESCE(p.t_overall_po_expired_cnt,0) AS t_overall_po_expired_cnt\n", "            , COALESCE(p.t1_overall_po_expired_cnt,0) AS t1_overall_po_expired_cnt\n", "            , COALESCE(p.t2_overall_po_expired_cnt,0) AS t2_overall_po_expired_cnt\n", "            , COALESCE(p.t3_overall_po_expired_cnt,0) AS t3_overall_po_expired_cnt\n", "            , COALESCE(p.t4_overall_po_expired_cnt,0) AS t4_overall_po_expired_cnt\n", "            , COALESCE(p.t5_overall_po_expired_cnt,0) AS t5_overall_po_expired_cnt\n", "            \n", "            , COALESCE(p.t_overall_po_expired_qty,0) AS t_overall_po_expired_qty\n", "            , COALESCE(p.t1_overall_po_expired_qty,0) AS t1_overall_po_expired_qty\n", "            , COALESCE(p.t2_overall_po_expired_qty,0) AS t2_overall_po_expired_qty\n", "            , COALESCE(p.t3_overall_po_expired_qty,0) AS t3_overall_po_expired_qty\n", "            , COALESCE(p.t4_overall_po_expired_qty,0) AS t4_overall_po_expired_qty\n", "            , COALESCE(p.t5_overall_po_expired_qty,0) AS t5_overall_po_expired_qty\n", "\n", "            , COALESCE(p.t_overall_po_expired_unscheduled_qty,0) AS t_overall_po_expired_unscheduled_qty\n", "            , COALESCE(p.t_overall_po_expired_past_today_scheduled_qty,0) AS t_overall_po_expired_past_today_scheduled_qty\n", "            , COALESCE(p.t_overall_po_expired_today_scheduled_qty,0) AS t_overall_po_expired_today_scheduled_qty\n", "\n", "            , COALESCE(p.y_po_created_cnt,0) AS y_po_created_cnt\n", "            , COALESCE(p.y_po_scheduled_cnt,0) AS y_po_scheduled_cnt\n", "            , COALESCE(p.y_po_unscheduled_cnt,0) AS y_po_unscheduled_cnt\n", "            , COALESCE(p.y_po_created_qty,0) AS y_po_created_qty\n", "            , COALESCE(p.y_po_scheduled_qty,0) AS y_po_scheduled_qty\n", "            , COALESCE(p.y_po_unscheduled_qty,0) AS y_po_unscheduled_qty\n", "            \n", "            , COALESCE(p.t_po_created_cnt,0) AS t_po_created_cnt\n", "            , COALESCE(p.t_po_scheduled_cnt,0) AS t_po_scheduled_cnt\n", "            , COALESCE(p.t_po_unscheduled_cnt,0) AS t_po_unscheduled_cnt\n", "            , COALESCE(p.t_po_created_qty,0) AS t_po_created_qty\n", "            , COALESCE(p.t_po_scheduled_qty,0) AS t_po_scheduled_qty\n", "            , COALESCE(p.t_po_unscheduled_qty,0) AS t_po_unscheduled_qty\n", "            \n", "\n", "FROM        base b\n", "\n", "LEFT JOIN   pg_items i\n", "            ON i.item_id = b.item_id\n", "\n", "LEFT JOIN   festive_po_info p\n", "            ON b.festival_name = p.festival_name AND b.be_facility_id = p.be_facility_id AND b.item_id = p.item_id  \n", "WHERE       b.sale_end_date >= CURRENT_DATE\n", "            \n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "5520c15c-0ed7-465f-8c17-ea466a656c1f", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"updated_at_ist\", \"type\": \"timestamp(6)\", \"description\": \"sample description\"},\n", "    {\"name\": \"date_\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"be_facility_name\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l0_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l1_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l1_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l2_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l2_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"festival_start_date\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"festival_end_date\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"sale_start_date\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"sale_end_date\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"last30d_PO_check\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"dynamic_planned_qty_untagged_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"dynamic_planned_qty_tagged_be\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"po_qty_last30d_fest_start\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"grn_qty_last30d_fest_start\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"open_po_qty_last30d_fest_start\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"closed_po_qty_last30d_fest_start\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"scheduled_po_qty_last30d_fest_start\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"unscheduled_po_qty_last30d_fest_start\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"scheduled_po_qty_past_today_last30d_fest_start\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"scheduled_po_qty_post_today_last30d_fest_start\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"fulfilled_po_qty_last30d_fest_start\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"po_fill_rate_last30d_fest_start\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"po_qty_during_fest_duration\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"grn_qty_during_fest_duration\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"open_po_qty_during_fest_duration\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"closed_po_qty_during_fest_duration\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"scheduled_po_qty_fest_duration\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"unscheduled_po_qty_fest_duration\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"scheduled_po_qty_past_today_fest_duration\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"t_overall_po_scheduled_qty_fest_duration\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"scheduled_po_qty_post_today_fest_duration\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"fulfilled_po_qty_fest_duration\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"po_fill_rate_during_fest_duration\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"latest_grn_ts\", \"type\": \"timestamp(6)\", \"description\": \"sample description\"},\n", "    {\"name\": \"latest_grn_po_number\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"latest_grn_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"last3po_fill_rate\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"po_count_last30d_fest_start\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"po_count_delivered_last30d_fest_start\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"open_po_count_last30d_fest_start\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"t_overall_po_scheduled_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t1_overall_po_scheduled_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t2_overall_po_scheduled_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t3_overall_po_scheduled_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t4_overall_po_scheduled_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t5_overall_po_scheduled_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t_overall_po_scheduled_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t1_overall_po_scheduled_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t2_overall_po_scheduled_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t3_overall_po_scheduled_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t4_overall_po_scheduled_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t5_overall_po_scheduled_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t_overall_po_expired_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t1_overall_po_expired_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t2_overall_po_expired_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t3_overall_po_expired_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t4_overall_po_expired_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t5_overall_po_expired_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t_overall_po_expired_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t1_overall_po_expired_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t2_overall_po_expired_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t3_overall_po_expired_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t4_overall_po_expired_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t5_overall_po_expired_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"t_overall_po_expired_unscheduled_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"t_overall_po_expired_past_today_scheduled_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"t_overall_po_expired_today_scheduled_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"y_po_created_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"y_po_scheduled_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"y_po_unscheduled_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"y_po_created_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"y_po_scheduled_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"y_po_unscheduled_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t_po_created_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t_po_scheduled_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t_po_unscheduled_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t_po_created_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t_po_scheduled_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t_po_unscheduled_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "1ecb3a26-ad62-40f4-b1c6-dc25cf32f750", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festive_replenish_tracker_v2\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"festival_name\", \"be_facility_id\", \"item_id\", \"date_\"],\n", "    # \"partition_key\": [\"date_\"],\n", "    # \"incremental_key\": \"date_\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive metrics\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "d1284057-11b3-49b3-9ba4-8b3a3ac4a145", "metadata": {}, "outputs": [], "source": ["to_trino(festive_metrics_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "5f558101-3cc6-4dc3-9ead-ef87b8e68faa", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}