{"cells": [{"cell_type": "code", "execution_count": null, "id": "cc2a3835-1bc6-43cf-b28c-fafe81e4038f", "metadata": {"tags": []}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e717d3fd-582e-49e1-9235-1d7852b43887", "metadata": {"tags": []}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "57bdc0aa-5ff7-4153-a57b-6c5af2c836c8", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "d41b0124-d898-4660-9eee-ea465f506abb", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "6b0b952e-ceff-4cba-909d-e0dd4e694e0d", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df = read_sql_query(# f\"\"\"\n", "festive_metrics_query = f\"\"\"\n", "\n", "WITH tea_tagging AS (\n", "select fe_outlet_id, fe_facility_id, item_id, assortment_type, be_hot_outlet_id, be_inv_outlet_id, be_facility_id, assortment_status_id\n", "from supply_etls.inventory_metrics_tea_tagging\n", "where flag = 'correct_tea' and be_facility_id <> fe_facility_id ),\n", "\n", "packaged_goods_outlet AS (\n", "SELECT  facility_id, facility_name, inv_outlet_id, hot_outlet_id, city_name, taggings\n", "FROM    supply_etls.outlet_details\n", "WHERE   taggings IN ('be', 'fe') AND store_type IN ('Packaged Goods', 'Dark Store') AND ars_check = 1 ),\n", "\n", "pg_items AS (\n", "select  item_id, item_name, l0_id, l0_category, l1_id, l1_category, l2_id, l2_category, p_type\n", "FROM    supply_etls.item_details\n", "WHERE   handling_type = 'Non Packaging Material' AND assortment_type = 'Packaged Goods' ),\n", "\n", "cluster_city_mapping AS (\n", "SELECT  m.city_id, c.cluster_id, c.cluster_name\n", "FROM        rpc.ams_city_cluster_mapping m\n", "INNER JOIN  rpc.ams_cluster c \n", "            ON  c.cluster_id = m.cluster_id and c.cluster_id >= 15000 AND m.lake_active_record = true AND m.active = true \n", "                AND c.lake_active_record = true ),\n", "\n", "proposed_store_movement as (\n", "select outlet_id, festival_name, proposed_be_facility_id as be_facility_id\n", "from supply_etls.festival_tea_tagging_v2\n", "WHERE active = 1 AND festival_name IS NOT NULL ),\n", "\n", "event_name as (\n", "SELECT sei.id AS id,\n", "    CONCAT(name, '_', cast(start_date as varchar)) AS event_name, \n", "    start_date,\n", "    end_date\n", "FROM rpc.supply_event_info sei\n", "JOIN rpc.supply_event se ON se.id = sei.supply_event_id\n", "WHERE sei.active = TRUE\n", "  AND se.active = TRUE ),\n", "\n", "planner as (\n", "select \n", "    location_id,\n", "    event_id,\n", "    item_id, \n", "    event_name as festival_name,\n", "    start_date AS festival_start_date,\n", "    end_date AS festival_end_date,\n", "    sale_start_date, \n", "    sale_end_date, \n", "    cut_off_date, \n", "    assortment_type, \n", "    assortment_launch_type, \n", "    substitute_ptype\n", "from ars.bulk_process_event_planner bpep\n", "join event_name en on bpep.event_id=en.id\n", "where partition_field is not null and active = 1\n", "), \n", "\n", "store_level_data as (\n", "select \n", "    event_id, \n", "    city_id,\n", "    outlet_id,\n", "    item_id, \n", "    quantity\n", "from ars.event_outlet_item_distribution where partition_field is not null and active = 1 \n", "), \n", "\n", "assortment_tea as (\n", "        select \n", "            cast(outlet_id as int) as outlet_id, \n", "            longtail_be_outlet_id as be_outlet_id, \n", "            'LONGTAIL' as assortment_type\n", "        from supply_etls.hybrid_longtail_backend_mapping     \n", "            where tagging = 'LONGTAIL'\n", "            \n", "        union \n", "        \n", "        select \n", "            cast(outlet_id as int) as outlet_id, \n", "            longtail_be_outlet_id as be_outlet_id, \n", "            'HYBRID' as assortment_type\n", "        from supply_etls.hybrid_longtail_backend_mapping     \n", "            where launch_type = 'HYBRID' \n", "            \n", "        union \n", "        \n", "        select \n", "            cast(fe_outlet_id as int) as outlet_id, \n", "            be_outlet_id, \n", "            'EXPRESS' as assortment_type\n", "        from supply_etls.invs_tea_tagging\n", "        where \n", "            ((count_teas = 1) or (count_teas = 2 and be_facility_id = 2076) or (count_teas = 2 and be_facility_id = 2124)) -- Split assortment case in Dehradun\n", "            and carts > 10 \n", "),\n", "    \n", "store_planned_distribution AS (\n", "SELECT      p.festival_name, COALESCE(t.be_facility_id, be.facility_id) AS be_facility_id\n", "            , oid.outlet_id AS frontend_outlet_id, fe.facility_id AS fe_facility_id, oid.item_id, p.festival_start_date, p.festival_end_date\n", "            , p.sale_start_date, p.sale_end_date\n", "            , (CASE WHEN t.be_facility_id IS NOT NULL THEN 1 ELSE 0 END) AS tea_tagged_flag\n", "            , (CASE WHEN t.be_facility_id IS NULL AND oid.quantity > 0 THEN oid.quantity ELSE 0 END) AS dynamic_planned_qty_untagged_be\n", "            , (CASE WHEN t.be_facility_id IS NOT NULL AND oid.quantity > 0 THEN oid.quantity ELSE 0 END) AS dynamic_planned_qty_tagged_be\n", "\n", "FROM        planner p\n", "INNER JOIN  cluster_city_mapping m \n", "            ON  m.cluster_id = p.location_id  \n", "INNER JOIN  store_level_data oid\n", "            ON  oid.city_id = m.city_id and oid.item_id = p.item_id AND oid.event_id = p.event_id\n", "LEFT JOIN   tea_tagging t\n", "            ON oid.outlet_id = t.fe_outlet_id AND oid.item_id = t.item_id\n", "LEFT JOIN   assortment_tea a\n", "            ON oid.outlet_id = a.outlet_id AND a.assortment_type = p.assortment_type\n", "LEFT JOIN   packaged_goods_outlet be \n", "            ON be.hot_outlet_id = a.be_outlet_id AND be.taggings = 'be' \n", "LEFT JOIN   packaged_goods_outlet fe \n", "            ON fe.hot_outlet_id = oid.outlet_id AND fe.taggings = 'fe' ),\n", "\n", "base AS (\n", "SELECT      festival_name, be_facility_id, be.inv_outlet_id AS be_inv_outlet_id, be.facility_name AS be_facility_name, item_id\n", "            , MIN(festival_start_date) AS festival_start_date, MAX(festival_end_date) AS festival_end_date\n", "            , MIN(sale_start_date) AS sale_start_date, MAX(sale_end_date) AS sale_end_date\n", "            , COUNT(CASE WHEN tea_tagged_flag = 0 THEN frontend_outlet_id END) AS untagged_stores\n", "            , SUM(tea_tagged_flag) AS tea_tagged_flag, SUM(dynamic_planned_qty_untagged_be) AS dynamic_planned_qty_untagged_be\n", "            , SUM(dynamic_planned_qty_tagged_be) AS dynamic_planned_qty_tagged_be\n", "            , DATE_ADD('DAY', -30, MIN(sale_start_date)) AS last30d_PO_check\n", "\n", "            , (CASE WHEN DATE_DIFF('DAY', CURRENT_DATE, MIN(sale_start_date)) > 0 THEN DATE_DIFF('DAY', CURRENT_DATE, MIN(sale_start_date)) \n", "                    ELSE 0 \n", "                END) AS festival_start_countdown\n", "    \n", "            , (CASE WHEN DATE_DIFF('DAY', CURRENT_DATE, MAX(sale_end_date)) > 0 THEN DATE_DIFF('DAY', CURRENT_DATE, MAX(sale_end_date))\n", "                    ELSE 0 \n", "                END) AS festival_end_countdown\n", "    \n", "            , (CASE WHEN (DATE_DIFF('DAY', MIN(sale_start_date), MAX(sale_end_date)) + 1) > 0 THEN (DATE_DIFF('DAY', MIN(sale_start_date), MAX(sale_end_date)) + 1)\n", "                    ELSE 0 \n", "                END) AS festival_duration\n", "\n", "FROM        store_planned_distribution f\n", "LEFT JOIN   packaged_goods_outlet be \n", "            ON f.be_facility_id = be.facility_id AND be.taggings = 'be' \n", "GROUP BY 1,2,3,4,5 ),\n", "\n", "festive_assortment AS (\n", "SELECT      f.festival_name, f.be_facility_id, f.item_id\n", "            , COUNT(f.frontend_outlet_id) AS planned_festival_stores\n", "            , COUNT(CASE WHEN a.master_assortment_substate_id IN (1,2,3,4) THEN f.frontend_outlet_id END) AS total_assortment_stores\n", "            , COUNT(CASE WHEN a.master_assortment_substate_id = 1 THEN f.frontend_outlet_id END) AS active_stores\n", "            , COUNT(CASE WHEN a.master_assortment_substate_id = 2 THEN f.frontend_outlet_id END) AS inactive_stores\n", "            , COUNT(CASE WHEN a.master_assortment_substate_id = 3 THEN f.frontend_outlet_id END) AS temp_inactive_stores \n", "            , COUNT(CASE WHEN a.master_assortment_substate_id = 4 THEN f.frontend_outlet_id END) AS discountinued_stores\n", "\n", "FROM        store_planned_distribution f \n", "INNER JOIN  rpc.product_facility_master_assortment a \n", "            ON a.facility_id = f.fe_facility_id AND a.item_id = f.item_id AND active = 1 AND lake_active_record = true \n", "GROUP BY    1,2,3 ),\n", "            \n", "festive_sales_duration_ts AS (\n", "SELECT      festival_name, CAST(MIN(sale_start_date) AS TIMESTAMP) AS sale_start_ts\n", "            , CAST((MAX(sale_end_date) + INTERVAL '1' DAY) AS TIMESTAMP) AS sale_end_ts_plus1\n", "            , DATE_ADD('DAY', -30, MIN(sale_start_date)) AS last30d_PO_check\n", "FROM        planner p\n", "GROUP BY    1 ),\n", "\n", "festive_sales AS (\n", "select  f.festival_name, f.be_facility_id, f.item_id\n", "        , SUM(fs.procured_quantity * ip.multiplier) as qty_sold\n", "        \n", "        , CAST(SUM(CASE  WHEN DATE(fs.order_create_dt_ist) = CURRENT_DATE THEN (fs.procured_quantity * ip.multiplier)\n", "            END) AS DOUBLE) AS today_sales\n", "\n", "        , CAST(SUM(CASE WHEN DATE(fs.order_create_dt_ist) = CURRENT_DATE - INTERVAL '1' DAY THEN (fs.procured_quantity * ip.multiplier)\n", "                END) AS DOUBLE) AS yesterday_sales\n", "\n", "        , CAST(SUM(CASE WHEN DATE(fs.order_create_dt_ist) >= b.sale_start_date AND DATE(fs.order_create_dt_ist) <= b.sale_end_date \n", "                        THEN (fs.procured_quantity * ip.multiplier)\n", "              END) AS DOUBLE) AS overall_festive_sales\n", "\n", "FROM        dwh.fact_sales_order_item_details fs\n", "INNER JOIN  dwh.dim_item_product_offer_mapping ip \n", "            ON ip.product_id = fs.product_id and ip.is_current \n", "INNER JOIN  store_planned_distribution f\n", "            ON fs.outlet_id = f.frontend_outlet_id AND f.item_id = ip.item_id \n", "LEFT JOIN   base b\n", "            ON b.be_facility_id = f.be_facility_id AND b.item_id = f.item_id AND f.festival_name = b.festival_name\n", "where       fs.order_create_dt_ist is not null AND fs.order_create_dt_ist >=  (SELECT MIN(sale_start_ts) FROM festive_sales_duration_ts)\n", "            AND fs.order_create_dt_ist <=  (SELECT MAX(sale_end_ts_plus1) FROM festive_sales_duration_ts)        \n", "            AND fs.cart_checkout_ts_ist >= (SELECT MIN(sale_start_ts) FROM festive_sales_duration_ts) \n", "            AND fs.cart_checkout_ts_ist <= (SELECT MAX(sale_end_ts_plus1) FROM festive_sales_duration_ts) \n", "            AND fs.order_current_status = 'DELIVERED' and is_internal_order = false\n", "group by    1,2,3 ),\n", "\n", "block_invt AS ( \n", "SELECT  outlet_id, item_id, SUM(quantity) AS blocked_inventory\n", "FROM    ims.ims_item_blocked_inventory\n", "WHERE   lake_active_record = true AND active = 1 and quantity > 0\n", "GROUP BY    1,2 ),\n", "\n", "outlet_inventory_info AS ( \n", "SELECT  i.outlet_id, i.item_id, SUM(CASE WHEN i.quantity > 0 THEN i.quantity ELSE 0 END) AS onshelf_invt\n", "        , SUM(b.blocked_inventory) AS block_invt\n", "        , SUM(CASE WHEN (i.quantity - coalesce(b.blocked_inventory,0) > 0) THEN (i.quantity - coalesce(b.blocked_inventory,0)) ELSE 0 END) AS net_onshelf_invt\n", "\n", "FROM        ims.ims_item_inventory i\n", "LEFT JOIN   block_invt b \n", "            ON i.item_id = b.item_id AND i.outlet_id = b.outlet_id\n", "WHERE i.active = 1 and i.lake_active_record = true\n", "GROUP BY    1,2 ),\n", "    \n", "festive_assortment_cpd AS (\n", "SELECT      o.festival_name, o.frontend_outlet_id, o.item_id, ROUND(SUM(cpd.cpd),1) AS t_transfer_cpd, ROUND(SUM(cpd.aps_adjusted),1) AS t_aps\n", "FROM        store_planned_distribution o\n", "INNER JOIN  ars.outlet_item_aps_derived_cpd cpd\n", "            ON  o.frontend_outlet_id = cpd.outlet_id AND o.item_id = cpd.item_id \n", "WHERE       cpd.lake_active_record = true AND cpd.insert_ds_ist = CAST(CURRENT_DATE AS VARCHAR)\n", "GROUP BY     1,2,3 ),  \n", "\n", "festive_open_sto_info AS (\n", "SELECT      s.frontend_outlet_id, si.item_id, SUM(ii.billed_quantity) AS billed_quantity, SUM(ii.reserved_quantity) AS reserved_quantity\n", "            , (SUM(ii.reserved_quantity) - SUM(ii.inward_quantity) - SUM(ii.released_reserved_quantity) \n", "                - SUM(ii.released_billed_quantity)) AS open_sto_qty_l2d\n", "\n", "FROM        po.sto s \n", "INNER JOIN  po.sto_items si \n", "            ON  s.active = 1 AND s.lake_active_record = true AND si.active = 1 AND si.lake_active_record = true AND s.id = si.sto_id \n", "                -- AND s.sto_state_id IN (2,3)\n", "\n", "INNER JOIN   ims.ims_sto_item ii\n", "            ON ii.lake_active_record = true AND ii.sto_id = si.sto_id AND ii.item_id = si.item_id\n", "\n", "INNER JOIN   ims.ims_sto_details ims_sto\n", "            ON ims_sto.lake_active_record = true AND ims_sto.sto_id = ii.sto_id \n", "\n", "WHERE       ims_sto.sto_state IN (1,2,5) AND s.created_at >= (CAST(CURRENT_DATE AS TIMESTAMP) - INTERVAL '2' DAY - INTERVAL '330' MINUTE)\n", "            AND si.created_at >= (CAST(CURRENT_DATE AS TIMESTAMP) - INTERVAL '2' DAY - INTERVAL '330' MINUTE)\n", "            AND ii.created_at >= (CAST(CURRENT_DATE AS TIMESTAMP) - INTERVAL '2' DAY - INTERVAL '330' MINUTE)\n", "GROUP BY    1,2 ),\n", "\n", "doi_calc AS (\n", "SELECT      d.festival_name, d.be_facility_id, d.frontend_outlet_id, d.item_id\n", "            , COALESCE(fe.onshelf_invt,0) AS fe_onshelf_invt, COALESCE(fe.block_invt,0) AS fe_block_invt\n", "            , COALESCE(fe.net_onshelf_invt,0) AS fe_soh, cpd.t_transfer_cpd, cpd.t_aps, COALESCE(os.open_sto_qty_l2d,0) AS open_sto_qty_l2d\n", "            , (COALESCE(fe.net_onshelf_invt,0) + COALESCE(os.open_sto_qty_l2d,0)) AS fe_soh_open_sto\n", "            , os.reserved_quantity, os.billed_quantity\n", "            , COALESCE((CAST(fe.net_onshelf_invt AS DOUBLE) / cpd.t_transfer_cpd),0) AS doi_wrt_fe_soh\n", "            , COALESCE((CAST((COALESCE(fe.net_onshelf_invt,0) + COALESCE(os.open_sto_qty_l2d,0)) AS DOUBLE) / cpd.t_transfer_cpd),0) AS doi_wrt_fe_soh_open_sto\n", "            \n", "FROM        store_planned_distribution d\n", "LEFT JOIN   outlet_inventory_info fe\n", "            ON d.frontend_outlet_id = fe.outlet_id AND d.item_id = fe.item_id\n", "LEFT JOIN   festive_assortment_cpd cpd\n", "            ON d.frontend_outlet_id = cpd.frontend_outlet_id AND d.item_id = cpd.item_id AND d.festival_name = cpd.festival_name\n", "LEFT JOIN   festive_open_sto_info os\n", "            ON d.frontend_outlet_id = os.frontend_outlet_id AND d.item_id = os.item_id \n", "),\n", "\n", "festive_fe_invt_and_doi AS (\n", "SELECT  festival_name, be_facility_id, item_id, SUM(t_transfer_cpd) AS t_transfer_cpd, SUM(t_aps) AS t_aps\n", "        , SUM(fe_onshelf_invt) AS fe_onshelf_invt, SUM(fe_block_invt) AS fe_block_invt\n", "        , SUM(open_sto_qty_l2d) AS open_sto_qty_l2d, SUM(fe_soh) AS fe_soh, SUM(fe_soh_open_sto) AS fe_soh_open_sto\n", "        , CAST(SUM(billed_quantity) AS DOUBLE) AS billed_quantity, SUM(reserved_quantity) AS reserved_quantity\n", "        , ROUND(((CAST(SUM(billed_quantity) AS DOUBLE) * 100)/ SUM(reserved_quantity)),1) AS open_sto_billing_fill_rate\n", "\n", "        , COUNT(frontend_outlet_id) AS total_doi_stores\n", "        , COUNT(CASE WHEN doi_wrt_fe_soh = 0 THEN frontend_outlet_id END) AS oos_store_cnt_wrt_fe_soh\n", "        , COUNT(CASE WHEN doi_wrt_fe_soh > 0 AND doi_wrt_fe_soh < 1 THEN frontend_outlet_id END) AS store_cnt_0_1_doi_wrt_fe_soh\n", "        , COUNT(CASE WHEN doi_wrt_fe_soh >= 1 AND doi_wrt_fe_soh < 3 THEN frontend_outlet_id END) AS store_cnt_1_3_doi_wrt_fe_soh\n", "        , COUNT(CASE WHEN doi_wrt_fe_soh >= 3 AND doi_wrt_fe_soh < 5 THEN frontend_outlet_id END) AS store_cnt_3_5_doi_wrt_fe_soh\n", "        , COUNT(CASE WHEN doi_wrt_fe_soh >= 5 AND doi_wrt_fe_soh < 8 THEN frontend_outlet_id END) AS store_cnt_5_8_doi_wrt_fe_soh\n", "        , COUNT(CASE WHEN doi_wrt_fe_soh >= 8 THEN frontend_outlet_id END) AS store_cnt_more_equal_8_doi_wrt_fe_soh\n", "\n", "        , COUNT(CASE WHEN doi_wrt_fe_soh_open_sto = 0 THEN frontend_outlet_id END) AS oos_store_cnt_wrt_fe_soh_open_sto\n", "        , COUNT(CASE WHEN doi_wrt_fe_soh_open_sto > 0 AND doi_wrt_fe_soh_open_sto < 1 THEN frontend_outlet_id END) AS store_cnt_0_1_doi_wrt_fe_soh_open_sto\n", "        , COUNT(CASE WHEN doi_wrt_fe_soh_open_sto >= 1 AND doi_wrt_fe_soh_open_sto < 3 THEN frontend_outlet_id END) AS store_cnt_1_3_doi_wrt_fe_soh_open_sto\n", "        , COUNT(CASE WHEN doi_wrt_fe_soh_open_sto >= 3 AND doi_wrt_fe_soh_open_sto < 5 THEN frontend_outlet_id END) AS store_cnt_3_5_doi_wrt_fe_soh_open_sto\n", "        , COUNT(CASE WHEN doi_wrt_fe_soh_open_sto >= 5 AND doi_wrt_fe_soh_open_sto < 8 THEN frontend_outlet_id END) AS store_cnt_5_8_doi_wrt_fe_soh_open_sto\n", "        , COUNT(CASE WHEN doi_wrt_fe_soh_open_sto >= 8 THEN frontend_outlet_id END) AS store_cnt_more_equal_8_doi_wrt_fe_soh_open_sto\n", "\n", "FROM    doi_calc\n", "GROUP BY 1,2,3 )\n", "\n", "SELECT      CAST(CURRENT_TIMESTAMP AS TIMESTAMP) AS updated_at_ist, current_date as date_,b.festival_name, b.be_facility_id, b.be_facility_name, b.item_id, i.item_name, i.l0_id, i.l0_category\n", "            , i.l1_id, i.l1_category, i.l2_id, i.l2_category, i.p_type, b.festival_start_date, b.festival_end_date, b.sale_start_date\n", "            , b.sale_end_date, b.dynamic_planned_qty_untagged_be, b.dynamic_planned_qty_tagged_be, b.festival_start_countdown, b.festival_end_countdown, b.festival_duration \n", "            \n", "            , COALESCE(a.planned_festival_stores,0) AS planned_festival_stores\n", "            , COALESCE(a.total_assortment_stores,0) AS total_assortment_stores\n", "            , COALESCE(a.active_stores,0) AS active_stores\n", "            , COALESCE(a.inactive_stores,0) AS inactive_stores\n", "            , COALESCE(a.temp_inactive_stores,0) AS temp_inactive_stores\n", "            , COALESCE(a.discountinued_stores,0) AS discountinued_stores\n", "            \n", "            , COALESCE(s.overall_festive_sales,0) AS overall_festive_sales\n", "            , COALESCE(s.today_sales,0) AS today_sales\n", "            , COALESCE(s.yesterday_sales,0) AS yesterday_sales\n", "\n", "            , COALESCE(fe.open_sto_qty_l2d,0) AS open_sto_qty_l2d\n", "            , COALESCE(fe.reserved_quantity,0) AS reserved_quantity\n", "            , COALESCE(fe.billed_quantity,0) AS billed_quantity\n", "            , COALESCE(fe.open_sto_billing_fill_rate,0) AS open_sto_billing_fill_rate\n", "            , COALESCE(fe.fe_onshelf_invt,0) AS fe_onshelf_invt, COALESCE(fe.fe_block_invt,0) AS fe_block_invt\n", "            , COALESCE(fe.fe_soh,0) AS fe_soh\n", "            \n", "            , COALESCE(be_invt.onshelf_invt,0) AS be_onshelf_invt, COALESCE(be_invt.block_invt,0) AS be_block_invt\n", "            , COALESCE(be_invt.net_onshelf_invt,0) AS be_soh\n", "            \n", "            , (COALESCE(fe.fe_soh,0) + COALESCE(be_invt.net_onshelf_invt,0)) AS total_soh\n", "            , (COALESCE(fe.fe_soh,0) + COALESCE(be_invt.net_onshelf_invt,0) + COALESCE(fe.open_sto_qty_l2d,0)) AS total_soh_open_sto\n", "            \n", "            , (COALESCE(fe.fe_soh,0) + COALESCE(fe.open_sto_qty_l2d,0)) AS fe_soh_open_sto\n", "            , (COALESCE(fe.fe_soh,0) + COALESCE(fe.open_sto_qty_l2d,0) + COALESCE(s.overall_festive_sales,0)) AS fe_soh_open_sto_sales\n", "            \n", "            , ((COALESCE(fe.fe_soh,0) + COALESCE(be_invt.net_onshelf_invt,0)) \n", "                - (COALESCE(fe.t_transfer_cpd,0) * festival_start_countdown)) AS total_soh_with_depletion\n", "                \n", "            , ((COALESCE(fe.fe_soh,0) + COALESCE(be_invt.net_onshelf_invt,0) + COALESCE(fe.open_sto_qty_l2d,0)) \n", "                - (COALESCE(fe.t_transfer_cpd,0) * festival_start_countdown)) AS total_soh_open_sto_with_depletion\n", "            \n", "            , ((COALESCE(fe.fe_soh,0) + COALESCE(fe.open_sto_qty_l2d,0)) - (COALESCE(fe.t_transfer_cpd,0) * festival_start_countdown)) AS fe_soh_open_sto_with_depletion\n", "            , ((COALESCE(fe.fe_soh,0) + COALESCE(fe.open_sto_qty_l2d,0) + COALESCE(s.overall_festive_sales,0))\n", "                - (COALESCE(fe.t_transfer_cpd,0) * festival_start_countdown)) AS fe_soh_open_sto_sales_with_depletion\n", "\n", "            , COALESCE(fe.total_doi_stores,0) AS total_doi_stores\n", "            , COALESCE(fe.oos_store_cnt_wrt_fe_soh,0) AS oos_store_cnt_wrt_fe_soh\n", "            , COALESCE(fe.store_cnt_0_1_doi_wrt_fe_soh,0) AS store_cnt_0_1_doi_wrt_fe_soh\n", "            , COALESCE(fe.store_cnt_1_3_doi_wrt_fe_soh,0) AS store_cnt_1_3_doi_wrt_fe_soh\n", "            , COALESCE(fe.store_cnt_3_5_doi_wrt_fe_soh,0) AS store_cnt_3_5_doi_wrt_fe_soh\n", "            , COALESCE(fe.store_cnt_5_8_doi_wrt_fe_soh,0) AS store_cnt_5_8_doi_wrt_fe_soh\n", "            , COALESCE(fe.store_cnt_more_equal_8_doi_wrt_fe_soh,0) AS store_cnt_more_equal_8_doi_wrt_fe_soh\n", "            \n", "            , COALESCE(fe.oos_store_cnt_wrt_fe_soh_open_sto,0) AS oos_store_cnt_wrt_fe_soh_open_sto\n", "            , COALESCE(fe.store_cnt_0_1_doi_wrt_fe_soh_open_sto,0) AS store_cnt_0_1_doi_wrt_fe_soh_open_sto\n", "            , COALESCE(fe.store_cnt_1_3_doi_wrt_fe_soh_open_sto,0) AS store_cnt_1_3_doi_wrt_fe_soh_open_sto\n", "            , COALESCE(fe.store_cnt_3_5_doi_wrt_fe_soh_open_sto,0) AS store_cnt_3_5_doi_wrt_fe_soh_open_sto\n", "            , COALESCE(fe.store_cnt_5_8_doi_wrt_fe_soh_open_sto,0) AS store_cnt_5_8_doi_wrt_fe_soh_open_sto\n", "            , COALESCE(fe.store_cnt_more_equal_8_doi_wrt_fe_soh_open_sto,0) AS store_cnt_more_equal_8_doi_wrt_fe_soh_open_sto\n", "        \n", "            , COALESCE(fe.t_transfer_cpd,0) AS t_transfer_cpd\n", "            , COALESCE(fe.t_aps,0) AS t_aps\n", "            \n", "FROM        base b\n", "\n", "LEFT JOIN   pg_items i\n", "            ON i.item_id = b.item_id\n", "            \n", "LEFT JOIN   festive_sales s\n", "            ON b.festival_name = s.festival_name AND b.be_facility_id = s.be_facility_id AND b.item_id = s.item_id\n", "\n", "LEFT JOIN   festive_fe_invt_and_doi fe \n", "            ON b.festival_name = fe.festival_name AND b.be_facility_id = fe.be_facility_id AND b.item_id = fe.item_id \n", "\n", "LEFT JOIN   outlet_inventory_info be_invt\n", "            ON b.be_inv_outlet_id = be_invt.outlet_id AND b.item_id = be_invt.item_id\n", "            \n", "LEFT JOIN   festive_assortment a\n", "            ON b.festival_name = a.festival_name AND b.be_facility_id = a.be_facility_id AND b.item_id = a.item_id \n", "WHERE       b.sale_end_date >= CURRENT_DATE\n", "\n", "\"\"\"\n", "# , CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "98b77cbd-47e5-4e41-a925-f9f199e06447", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "fa141ce4-5211-4459-8e77-839be005764e", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df['updated_at'] = pd.Timestamp.now()"]}, {"cell_type": "code", "execution_count": null, "id": "0c0d7b08-d140-4fac-8932-a15b5347cda4", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df[['insert_ds_ist','updated_at_ist','sale_start','sale_end','cutt_off']] = festive_metrics_df[['insert_ds_ist','updated_at_ist','sale_start','sale_end','cutt_off']].astype('datetime64[ns]')\n", "# festive_metrics_df[['fe_inv','po_qty_created','scheduled_po_qty','unscheduled_po_qty','po_grn_qty','po_remaining_qty','t_scheduled_quantity','t1_scheduled_quantity','t2_scheduled_quantity','t3_scheduled_quantity']] = festive_metrics_df[['fe_inv','po_qty_created','scheduled_po_qty','unscheduled_po_qty','po_grn_qty','po_remaining_qty','t_scheduled_quantity','t1_scheduled_quantity','t2_scheduled_quantity','t3_scheduled_quantity']].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "eaa9d2cc-879b-4fca-9c11-e2924bd614b9", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "f87fed17-ac34-4c98-add2-b0c1c4ef7496", "metadata": {}, "outputs": [], "source": ["# column_dtypes = [\n", "#     {\n", "#         \"name\": col,\n", "#         \"type\": str(festive_metrics_df[col].dtype).replace(\"int64\", \"integer\").replace(\"float64\", \"real\").replace(\"str\",\"varchar\").replace(\"datetime64[ns]\",\"date\").replace(\"object\",\"varchar\"),\n", "#         \"description\": \"sample description\"\n", "#     } for col in festive_metrics_df.columns\n", "# ]\n", "\n", "# column_dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "5520c15c-0ed7-465f-8c17-ea466a656c1f", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"updated_at_ist\", \"type\": \"timestamp(6)\", \"description\": \"sample description\"},\n", "    {\"name\": \"date_\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"be_facility_name\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l0_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l1_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l1_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l2_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l2_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"festival_start_date\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"festival_end_date\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"sale_start_date\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"sale_end_date\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"dynamic_planned_qty_untagged_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"dynamic_planned_qty_tagged_be\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"festival_start_countdown\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"festival_end_countdown\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"festival_duration\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"planned_festival_stores\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"total_assortment_stores\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"active_stores\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"inactive_stores\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"temp_inactive_stores\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"discountinued_stores\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"overall_festive_sales\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"today_sales\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"yesterday_sales\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"open_sto_qty_l2d\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"reserved_quantity\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"billed_quantity\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"open_sto_billing_fill_rate\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"fe_onshelf_invt\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"fe_block_invt\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"fe_soh\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"be_onshelf_invt\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"be_block_invt\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"be_soh\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"total_soh\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"total_soh_open_sto\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"fe_soh_open_sto\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"fe_soh_open_sto_sales\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"total_soh_with_depletion\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"total_soh_open_sto_with_depletion\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"fe_soh_open_sto_with_depletion\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"fe_soh_open_sto_sales_with_depletion\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"total_doi_stores\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"oos_store_cnt_wrt_fe_soh\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"store_cnt_0_1_doi_wrt_fe_soh\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"store_cnt_1_3_doi_wrt_fe_soh\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"store_cnt_3_5_doi_wrt_fe_soh\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"store_cnt_5_8_doi_wrt_fe_soh\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"store_cnt_more_equal_8_doi_wrt_fe_soh\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"oos_store_cnt_wrt_fe_soh_open_sto\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"store_cnt_0_1_doi_wrt_fe_soh_open_sto\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"store_cnt_1_3_doi_wrt_fe_soh_open_sto\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"store_cnt_3_5_doi_wrt_fe_soh_open_sto\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"store_cnt_5_8_doi_wrt_fe_soh_open_sto\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"store_cnt_more_equal_8_doi_wrt_fe_soh_open_sto\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"t_transfer_cpd\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t_aps\", \"type\": \"real\", \"description\": \"sample description\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "1ecb3a26-ad62-40f4-b1c6-dc25cf32f750", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festive_replenish_tracker_v1\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"festival_name\", \"be_facility_id\", \"item_id\", \"date_\"],\n", "    # \"partition_key\": [\"date_\"],\n", "    # \"incremental_key\": \"date_\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive metrics\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "d1284057-11b3-49b3-9ba4-8b3a3ac4a145", "metadata": {}, "outputs": [], "source": ["to_trino(festive_metrics_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "5f558101-3cc6-4dc3-9ead-ef87b8e68faa", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}