{"cells": [{"cell_type": "code", "execution_count": null, "id": "cc2a3835-1bc6-43cf-b28c-fafe81e4038f", "metadata": {"tags": []}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e717d3fd-582e-49e1-9235-1d7852b43887", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "57bdc0aa-5ff7-4153-a57b-6c5af2c836c8", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "d41b0124-d898-4660-9eee-ea465f506abb", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "6b0b952e-ceff-4cba-909d-e0dd4e694e0d", "metadata": {}, "outputs": [], "source": ["festive_metrics_query = f\"\"\"\n", "\n", "SELECT      CAST(CURRENT_TIMESTAMP AS TIMESTAMP) AS updated_at_ist\n", "            , a.date_\n", "            , a.festival_name\n", "            , a.be_facility_id\n", "            , a.be_facility_name\n", "            , a.item_id\n", "            , a.item_name\n", "            , a.l0_id\n", "            , a.l0_category\n", "            , a.l1_id\n", "            , a.l1_category\n", "            , a.l2_id\n", "            , a.l2_category\n", "            , a.p_type\n", "            , a.festival_start_date\n", "            , a.festival_end_date\n", "            , a.sale_start_date\n", "            , a.sale_end_date\n", "            , b.last30d_PO_check\n", "            , a.dynamic_planned_qty_untagged_be\n", "            , a.dynamic_planned_qty_tagged_be\n", "            , a.festival_start_countdown\n", "            , a.festival_end_countdown\n", "            , a.festival_duration\n", "            , a.planned_festival_stores\n", "            , a.total_assortment_stores\n", "            , a.active_stores\n", "            , a.inactive_stores\n", "            , a.temp_inactive_stores\n", "            , a.discountinued_stores\n", "            , a.overall_festive_sales\n", "            , a.today_sales\n", "            , a.yesterday_sales\n", "            , a.open_sto_qty_l2d\n", "            , a.reserved_quantity\n", "            , a.billed_quantity\n", "            , a.open_sto_billing_fill_rate\n", "            , a.fe_onshelf_invt\n", "            , a.fe_block_invt\n", "            , a.fe_soh\n", "            , a.be_onshelf_invt\n", "            , a.be_block_invt\n", "            , a.be_soh\n", "            , a.total_soh\n", "            , a.total_soh_open_sto\n", "            , a.fe_soh_open_sto\n", "            , a.fe_soh_open_sto_sales\n", "            , a.total_soh_with_depletion\n", "            , a.total_soh_open_sto_with_depletion\n", "            , a.fe_soh_open_sto_with_depletion\n", "            , a.fe_soh_open_sto_sales_with_depletion\n", "            , a.total_doi_stores\n", "            , a.oos_store_cnt_wrt_fe_soh\n", "            , a.store_cnt_0_1_doi_wrt_fe_soh\n", "            , a.store_cnt_1_3_doi_wrt_fe_soh\n", "            , a.store_cnt_3_5_doi_wrt_fe_soh\n", "            , a.store_cnt_5_8_doi_wrt_fe_soh\n", "            , a.store_cnt_more_equal_8_doi_wrt_fe_soh\n", "            , a.oos_store_cnt_wrt_fe_soh_open_sto\n", "            , a.store_cnt_0_1_doi_wrt_fe_soh_open_sto\n", "            , a.store_cnt_1_3_doi_wrt_fe_soh_open_sto\n", "            , a.store_cnt_3_5_doi_wrt_fe_soh_open_sto\n", "            , a.store_cnt_5_8_doi_wrt_fe_soh_open_sto\n", "            , a.store_cnt_more_equal_8_doi_wrt_fe_soh_open_sto\n", "            , a.t_transfer_cpd\n", "            , a.t_aps\n", "            , b.po_qty_last30d_fest_start\n", "            , b.grn_qty_last30d_fest_start\n", "            , b.open_po_qty_last30d_fest_start\n", "            , b.closed_po_qty_last30d_fest_start\n", "            , b.scheduled_po_qty_last30d_fest_start\n", "            , b.unscheduled_po_qty_last30d_fest_start\n", "            , b.scheduled_po_qty_past_today_last30d_fest_start\n", "            , b.scheduled_po_qty_post_today_last30d_fest_start\n", "            , b.t_overall_po_scheduled_qty_fest_duration\n", "            \n", "            , b.fulfilled_po_qty_last30d_fest_start\n", "            , b.po_fill_rate_last30d_fest_start\n", "            \n", "            , b.po_qty_during_fest_duration\n", "            , b.grn_qty_during_fest_duration\n", "            , b.open_po_qty_during_fest_duration\n", "            , b.closed_po_qty_during_fest_duration\n", "            , b.scheduled_po_qty_fest_duration\n", "            , b.unscheduled_po_qty_fest_duration\n", "            , b.scheduled_po_qty_past_today_fest_duration\n", "            , b.scheduled_po_qty_post_today_fest_duration\n", "            , b.fulfilled_po_qty_fest_duration\n", "            , b.po_fill_rate_during_fest_duration\n", "            \n", "            , b.latest_grn_ts\n", "            , b.latest_grn_po_number\n", "            , b.latest_grn_qty\n", "            , b.last3po_fill_rate\n", "            , b.po_count_last30d_fest_start\n", "            , b.po_count_delivered_last30d_fest_start\n", "            , b.open_po_count_last30d_fest_start\n", "            , b.t_overall_po_scheduled_cnt\n", "            , b.t1_overall_po_scheduled_cnt\n", "            , b.t2_overall_po_scheduled_cnt\n", "            , b.t3_overall_po_scheduled_cnt\n", "            , b.t4_overall_po_scheduled_cnt\n", "            , b.t5_overall_po_scheduled_cnt\n", "            , b.t_overall_po_scheduled_qty\n", "            , b.t1_overall_po_scheduled_qty\n", "            , b.t2_overall_po_scheduled_qty\n", "            , b.t3_overall_po_scheduled_qty\n", "            , b.t4_overall_po_scheduled_qty\n", "            , b.t5_overall_po_scheduled_qty\n", "            \n", "            , b.t_overall_po_expired_cnt\n", "            , b.t1_overall_po_expired_cnt\n", "            , b.t2_overall_po_expired_cnt\n", "            , b.t3_overall_po_expired_cnt\n", "            , b.t4_overall_po_expired_cnt\n", "            , b.t5_overall_po_expired_cnt\n", "            , b.t_overall_po_expired_qty\n", "            , b.t1_overall_po_expired_qty\n", "            , b.t2_overall_po_expired_qty\n", "            , b.t3_overall_po_expired_qty\n", "            , b.t4_overall_po_expired_qty\n", "            , b.t5_overall_po_expired_qty\n", "            \n", "            , b.t_overall_po_expired_unscheduled_qty\n", "            , b.t_overall_po_expired_past_today_scheduled_qty\n", "            , b.t_overall_po_expired_today_scheduled_qty\n", "            \n", "            , b.y_po_created_cnt\n", "            , b.y_po_scheduled_cnt\n", "            , b.y_po_unscheduled_cnt\n", "            , b.y_po_created_qty\n", "            , b.y_po_scheduled_qty\n", "            , b.y_po_unscheduled_qty\n", "            \n", "            , b.t_po_created_cnt\n", "            , b.t_po_scheduled_cnt\n", "            , b.t_po_unscheduled_cnt\n", "            , b.t_po_created_qty\n", "            , b.t_po_scheduled_qty\n", "            , b.t_po_unscheduled_qty\n", "\n", "            , (a.fe_soh + a.be_soh + a.open_sto_qty_l2d + b.t_overall_po_scheduled_qty) AS total_soh_open_sto_scheduled_po_today\n", "            , (a.fe_soh + a.be_soh + a.open_sto_qty_l2d + b.scheduled_po_qty_last30d_fest_start) AS total_soh_open_sto_scheduled_po\n", "            , (a.fe_soh + a.be_soh + a.open_sto_qty_l2d + b.open_po_qty_last30d_fest_start) AS total_soh_open_sto_open_po\n", "\n", "            , ((a.fe_soh + a.be_soh + a.open_sto_qty_l2d + b.t_overall_po_scheduled_qty) - \n", "                (a.t_transfer_cpd * a.festival_start_countdown)) AS total_soh_open_sto_scheduled_po_today_with_depletion\n", "                \n", "            , ((a.fe_soh + a.be_soh + a.open_sto_qty_l2d + b.scheduled_po_qty_last30d_fest_start) - \n", "                (a.t_transfer_cpd * a.festival_start_countdown)) AS total_soh_open_sto_scheduled_po_with_depletion\n", "                \n", "            , ((a.fe_soh + a.be_soh + a.open_sto_qty_l2d + b.open_po_qty_last30d_fest_start) - \n", "                (a.t_transfer_cpd * a.festival_start_countdown)) AS total_soh_open_sto_open_po_with_depletion\n", "\n", "FROM        supply_etls.festive_replenish_tracker_v1 a\n", "LEFT JOIN   supply_etls.festive_replenish_tracker_v2 b\n", "            ON  a.festival_name = b.festival_name AND a.be_facility_id = b.be_facility_id \n", "                AND a.item_id = b.item_id AND a.date_ = b.date_\n", "WHERE   a.date_ = CURRENT_DATE AND b.date_ = CURRENT_DATE\n", "                            \n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "5520c15c-0ed7-465f-8c17-ea466a656c1f", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"updated_at_ist\", \"type\": \"timestamp(6)\", \"description\": \"sample description\"},\n", "    {\"name\": \"date_\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"be_facility_name\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l0_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l1_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l1_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l2_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l2_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"festival_start_date\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"festival_end_date\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"sale_start_date\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"sale_end_date\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"last30d_PO_check\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"dynamic_planned_qty_untagged_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"dynamic_planned_qty_tagged_be\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"po_qty_last30d_fest_start\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"grn_qty_last30d_fest_start\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"open_po_qty_last30d_fest_start\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"closed_po_qty_last30d_fest_start\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"scheduled_po_qty_last30d_fest_start\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"unscheduled_po_qty_last30d_fest_start\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"scheduled_po_qty_past_today_last30d_fest_start\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"scheduled_po_qty_post_today_last30d_fest_start\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"fulfilled_po_qty_last30d_fest_start\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"po_fill_rate_last30d_fest_start\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"po_qty_during_fest_duration\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"grn_qty_during_fest_duration\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"open_po_qty_during_fest_duration\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"closed_po_qty_during_fest_duration\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"scheduled_po_qty_fest_duration\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"unscheduled_po_qty_fest_duration\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"scheduled_po_qty_past_today_fest_duration\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"t_overall_po_scheduled_qty_fest_duration\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"scheduled_po_qty_post_today_fest_duration\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"fulfilled_po_qty_fest_duration\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"po_fill_rate_during_fest_duration\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"latest_grn_ts\", \"type\": \"timestamp(6)\", \"description\": \"sample description\"},\n", "    {\"name\": \"latest_grn_po_number\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"latest_grn_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"last3po_fill_rate\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"po_count_last30d_fest_start\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"po_count_delivered_last30d_fest_start\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"open_po_count_last30d_fest_start\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"t_overall_po_scheduled_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t1_overall_po_scheduled_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t2_overall_po_scheduled_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t3_overall_po_scheduled_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t4_overall_po_scheduled_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t5_overall_po_scheduled_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t_overall_po_scheduled_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t1_overall_po_scheduled_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t2_overall_po_scheduled_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t3_overall_po_scheduled_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t4_overall_po_scheduled_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t5_overall_po_scheduled_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t_overall_po_expired_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t1_overall_po_expired_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t2_overall_po_expired_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t3_overall_po_expired_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t4_overall_po_expired_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t5_overall_po_expired_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t_overall_po_expired_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t1_overall_po_expired_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t2_overall_po_expired_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t3_overall_po_expired_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t4_overall_po_expired_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t5_overall_po_expired_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"t_overall_po_expired_unscheduled_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"t_overall_po_expired_past_today_scheduled_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"t_overall_po_expired_today_scheduled_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"y_po_created_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"y_po_scheduled_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"y_po_unscheduled_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"y_po_created_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"y_po_scheduled_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"y_po_unscheduled_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t_po_created_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t_po_scheduled_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t_po_unscheduled_cnt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"t_po_created_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t_po_scheduled_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t_po_unscheduled_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"total_soh_open_sto_scheduled_po_today\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"total_soh_open_sto_scheduled_po\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"total_soh_open_sto_open_po\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"total_soh_open_sto_scheduled_po_today_with_depletion\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"total_soh_open_sto_scheduled_po_with_depletion\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"total_soh_open_sto_open_po_with_depletion\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"festival_start_countdown\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"festival_end_countdown\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"festival_duration\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"planned_festival_stores\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"total_assortment_stores\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"active_stores\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"inactive_stores\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"temp_inactive_stores\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"discountinued_stores\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"overall_festive_sales\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"today_sales\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"yesterday_sales\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"open_sto_qty_l2d\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"reserved_quantity\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"billed_quantity\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"open_sto_billing_fill_rate\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"fe_onshelf_invt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"fe_block_invt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"fe_soh\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"be_onshelf_invt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"be_block_invt\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"be_soh\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"total_soh\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"total_soh_open_sto\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"fe_soh_open_sto\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"fe_soh_open_sto_sales\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"total_soh_with_depletion\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"total_soh_open_sto_with_depletion\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"fe_soh_open_sto_with_depletion\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"fe_soh_open_sto_sales_with_depletion\",\n", "        \"type\": \"real\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"oos_store_cnt_wrt_fe_soh\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"store_cnt_0_1_doi_wrt_fe_soh\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"store_cnt_1_3_doi_wrt_fe_soh\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"store_cnt_3_5_doi_wrt_fe_soh\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"store_cnt_5_8_doi_wrt_fe_soh\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"store_cnt_more_equal_8_doi_wrt_fe_soh\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"oos_store_cnt_wrt_fe_soh_open_sto\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"store_cnt_0_1_doi_wrt_fe_soh_open_sto\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"store_cnt_1_3_doi_wrt_fe_soh_open_sto\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"store_cnt_3_5_doi_wrt_fe_soh_open_sto\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"store_cnt_5_8_doi_wrt_fe_soh_open_sto\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\n", "        \"name\": \"store_cnt_more_equal_8_doi_wrt_fe_soh_open_sto\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"t_transfer_cpd\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"t_aps\", \"type\": \"real\", \"description\": \"sample description\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "1ecb3a26-ad62-40f4-b1c6-dc25cf32f750", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festive_replenish_tracker\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"festival_name\", \"be_facility_id\", \"item_id\", \"date_\"],\n", "    \"partition_key\": [\"date_\"],\n", "    \"incremental_key\": \"date_\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive metrics\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "d1284057-11b3-49b3-9ba4-8b3a3ac4a145", "metadata": {}, "outputs": [], "source": ["to_trino(festive_metrics_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "5f558101-3cc6-4dc3-9ead-ef87b8e68faa", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}