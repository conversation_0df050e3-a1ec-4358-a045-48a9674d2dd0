{"cells": [{"cell_type": "code", "execution_count": null, "id": "cc2a3835-1bc6-43cf-b28c-fafe81e4038f", "metadata": {"tags": []}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e717d3fd-582e-49e1-9235-1d7852b43887", "metadata": {"tags": []}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "57bdc0aa-5ff7-4153-a57b-6c5af2c836c8", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "d41b0124-d898-4660-9eee-ea465f506abb", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "                break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "6b0b952e-ceff-4cba-909d-e0dd4e694e0d", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df = read_sql_query(# f\"\"\"\n", "festive_metrics_query = f\"\"\"\n", "\n", "WITH po_data AS (\n", "SELECT  b.date_, b.festival_name, b.be_facility_id, b.be_facility_name, b.item_id, b.item_name, b.l0_id, b.l0_category, b.l1_id, b.l1_category, b.l2_id\n", "        , b.l2_category, b.p_type, b.sale_start_date, b.sale_end_date\n", "        , (CASE WHEN p.po_state_id IN (2,13,15) OR (p.po_state_id = 9 AND p.is_multiple_grn = 1) THEN 1 ELSE 0 END) AS open_po_flag\n", "        , p.po_state_id, p.po_status, p.po_number, p.run_id\n", "        , CAST(p.po_created_at_ist AS DATE) AS po_created_date, p.po_created_at_ist, DATE(p.po_expiry_at_ist) AS po_expiry_date\n", "        , p.po_expiry_at_ist, DATE(p.po_schedule_at_ist) AS po_scheduled_date, p.po_schedule_at_ist, p.vendor_id, p.vendor_name, p.po_quantity\n", "        , p.po_mrp, p.po_landing_price, p.grn_quantity, p.is_multiple_grn, p.grn_mrp, p.grn_landing_price, p.remaining_po_quantity, p.buckets\n", "        , b.last30d_PO_check -- , p.manufacturer_name, p.storage_type, p.handling_type, p.po_id, p.po_extension_count, p.first_expiry_at_ist, p.last_item_grn_at_ist\n", "\n", "FROM        supply_etls.festive_replenish_tracker b\n", "INNER JOIN  supply_etls.inventory_metrics_purchase_mis p\n", "            ON  p.facility_id = b.be_facility_id AND p.item_id = b.item_id \n", "WHERE       p.insert_ds_ist IS NOT NULL AND p.insert_ds_ist >= b.last30d_PO_check AND p.po_created_at_ist >= b.last30d_PO_check\n", "            AND p.po_to_be_consider = 1 AND b.date_ = CURRENT_DATE )\n", "\n", "SELECT  CAST(CURRENT_TIMESTAMP AS TIMESTAMP) AS updated_at_ist, date_, festival_name, be_facility_id, be_facility_name, item_id, item_name, l0_id, l0_category, l1_id , l1_category, l2_id, l2_category, p_type\n", "        , sale_start_date, sale_end_date, open_po_flag, po_state_id, po_status, po_number, run_id, po_created_at_ist, po_expiry_at_ist\n", "        , po_schedule_at_ist, vendor_id, vendor_name, po_quantity, po_mrp, po_landing_price, grn_quantity, is_multiple_grn, grn_mrp\n", "        , grn_landing_price, remaining_po_quantity AS open_po_qty, buckets\n", "\n", "FROM    po_data\n", "WHERE   (po_scheduled_date <= sale_end_date OR po_expiry_date <= sale_end_date) AND open_po_flag = 1 AND remaining_po_quantity > 0\n", "\n", "\"\"\"\n", "# , CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "5520c15c-0ed7-465f-8c17-ea466a656c1f", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"updated_at_ist\", \"type\": \"timestamp(6)\", \"description\": \"sample description\"},\n", "    {\"name\": \"date_\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"be_facility_name\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l0_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l1_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l1_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l2_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"l2_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"sale_start_date\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"sale_end_date\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"open_po_flag\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"po_state_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"po_status\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"po_number\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"run_id\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"po_created_at_ist\", \"type\": \"timestamp(6)\", \"description\": \"sample description\"},\n", "    {\"name\": \"po_expiry_at_ist\", \"type\": \"timestamp(6)\", \"description\": \"sample description\"},\n", "    {\"name\": \"po_schedule_at_ist\", \"type\": \"timestamp(6)\", \"description\": \"sample description\"},\n", "    {\"name\": \"vendor_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"vendor_name\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"po_quantity\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"po_mrp\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"po_landing_price\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"grn_quantity\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"is_multiple_grn\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"grn_mrp\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"grn_landing_price\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"open_po_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"buckets\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "1ecb3a26-ad62-40f4-b1c6-dc25cf32f750", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festive_replenish_tracker_open_po\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"festival_name\", \"be_facility_id\", \"item_id\", \"po_number\"],\n", "    # \"partition_key\": [\"date_\"],\n", "    # \"incremental_key\": \"date_\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive metrics\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "d1284057-11b3-49b3-9ba4-8b3a3ac4a145", "metadata": {}, "outputs": [], "source": ["to_trino(festive_metrics_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "5f558101-3cc6-4dc3-9ead-ef87b8e68faa", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}