alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: replenish_tracker
dag_type: etl
escalation_priority: low
execution_timeout: 120
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebooks:
- executor_config:
    load_type: tiny
    node_type: spot
  name: festive_replenish_tracker_v1
  parameters: null
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: festive_replenish_tracker_v2
  parameters: null
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: festive_replenish_tracker
  parameters: null
  tag: second
- executor_config:
    load_type: tiny
    node_type: spot
  name: festive_replenish_tracker_open_po
  parameters: null
  tag: third
owner:
  email: <EMAIL>
  slack_id: U06LLMJHFCK
path: povms/festive_metrics/etl/replenish_tracker
paused: false
pool: povms_pool
project_name: festive_metrics
schedule:
  end_date: '2025-04-13T00:00:00'
  interval: 40 2-17 * * *
  start_date: '2025-01-07T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 18
concurrency: 3
