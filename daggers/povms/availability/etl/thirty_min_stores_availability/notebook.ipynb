{"cells": [{"cell_type": "code", "execution_count": null, "id": "c8144531-6035-449b-907d-adf3f87a499c", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "f228aadc-2b6d-4b62-a812-3539d5bd163b", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(15)"]}, {"cell_type": "markdown", "id": "15f7ce6b-19b2-4066-a9f1-8b663f12adf7", "metadata": {}, "source": ["# remove facility details"]}, {"cell_type": "code", "execution_count": null, "id": "26ea9ef8-017c-43ae-a2b0-ce2f3ce8834c", "metadata": {}, "outputs": [], "source": ["remove_facility = pb.from_sheets(\n", "    \"1RpTXUhJmDuF12MGEUwBE_2Es41APrFIQJOi1Kefr_rU\",\n", "    \"remove_facility_id\",\n", "    clear_cache=True,\n", ")\n", "\n", "# remove_facility = pd.read_csv('new.csv')\n", "\n", "remove_facility = remove_facility[~((remove_facility[\"facility_id\"] == \"\"))]\n", "\n", "remove_facility[\"facility_id\"] = remove_facility[\"facility_id\"].fillna(0).astype(int)\n", "\n", "remove_facility = remove_facility[remove_facility[\"facility_id\"] > 0]\n", "\n", "remove_facility.dropna(inplace=True)\n", "\n", "# vendor_input_data['item_id'] = 0\n", "\n", "remove_facility.head()"]}, {"cell_type": "code", "execution_count": null, "id": "091ff574-4d56-4578-a8af-04bbed6a8cf3", "metadata": {}, "outputs": [], "source": ["remove_facility = tuple(list(remove_facility[\"facility_id\"].unique()))\n", "if len(remove_facility) < 2:\n", "    remove_facility.append(-1)\n", "    remove_facility.append(-2)\n", "remove_facility = tuple(remove_facility)\n", "remove_facility"]}, {"cell_type": "markdown", "id": "03713f50-ae47-4741-90b0-ad4cb8bc3597", "metadata": {}, "source": ["# Active ARS Details"]}, {"cell_type": "code", "execution_count": null, "id": "7961096e-1d0c-45fd-bf53-31301c46457e", "metadata": {}, "outputs": [], "source": ["def outlets():\n", "    outlets = f\"\"\"\n", "    \n", "    with\n", "    outlets as \n", "        (select * from supply_etls.outlet_details\n", "            where ars_check = 1\n", "            and facility_id not in {remove_facility}\n", "        )\n", "        \n", "            select * from outlets\n", "\n", "    \"\"\"\n", "    return read_sql_query(outlets, trino)\n", "\n", "\n", "outlets = outlets()"]}, {"cell_type": "code", "execution_count": null, "id": "1ef457e2-c55e-4225-a522-3d1686efc01f", "metadata": {}, "outputs": [], "source": ["outlets.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b5924997-bd75-4d7e-977b-a532fbb7dec0", "metadata": {}, "outputs": [], "source": ["# all inv outlets\n", "all_inv_outlet_id_list = tuple(list(outlets[\"inv_outlet_id\"].unique()))\n", "\n", "# all hot outlets\n", "all_hot_outlet_id_list = tuple(list(outlets[\"hot_outlet_id\"].unique()))\n", "\n", "# be hot outlets\n", "all_hot_name = outlets[\n", "    [\"hot_outlet_id\", \"inv_outlet_id\", \"facility_id\", \"facility_name\"]\n", "].rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_hot_outlet_id\",\n", "        \"inv_outlet_id\": \"be_inv_outlet_id\",\n", "        \"facility_id\": \"be_facility_id\",\n", "        \"facility_name\": \"be_facility_name\",\n", "    }\n", ")\n", "\n", "# all facility id\n", "all_facility_id_list = tuple(list(outlets[\"facility_id\"].unique()))\n", "\n", "# frontend outlets\n", "frontend_outlet_details = (\n", "    outlets[outlets[\"taggings\"] == \"fe\"].reset_index().drop(columns={\"index\"})\n", ")\n", "\n", "fe_facility_id_list = tuple(list(frontend_outlet_details[\"facility_id\"].unique()))"]}, {"cell_type": "markdown", "id": "626a4a3d-4164-4e6f-a0b4-470fad9b607f", "metadata": {}, "source": ["# item Details"]}, {"cell_type": "code", "execution_count": null, "id": "0e176f38-31cd-4b4d-ac85-99c56265d178", "metadata": {}, "outputs": [], "source": ["def item_details():\n", "    item_details = \"\"\"\n", "    \n", "    with\n", "    item_details as\n", "        (select * from supply_etls.item_details\n", "            where handling_type = 'Non Packaging Material'\n", "\n", "        )\n", "\n", "            select * from item_details    \n", "    \"\"\"\n", "    return read_sql_query(item_details, trino)\n", "\n", "\n", "item_details = item_details()"]}, {"cell_type": "code", "execution_count": null, "id": "ad016b34-d202-416f-94e3-92d5e97302c5", "metadata": {}, "outputs": [], "source": ["item_details.head()"]}, {"cell_type": "markdown", "id": "3c566c5e-44d4-4037-b530-482fef134a0d", "metadata": {}, "source": ["# Active Assortment Details"]}, {"cell_type": "code", "execution_count": null, "id": "543d15cd-106c-4470-b3f5-a23ce834134e", "metadata": {}, "outputs": [], "source": ["def active_assortment():\n", "    active_assortment = f\"\"\"\n", "    \n", "    with\n", "    active_assortment as\n", "        (select item_id, facility_id\n", "            from rpc.product_facility_master_assortment\n", "                where active = 1 and master_assortment_substate_id = 1 and assortment_type in ('LONGTAIL')\n", "        )\n", "\n", "            select * from active_assortment\n", "\n", "    \"\"\"\n", "    return read_sql_query(active_assortment, trino)\n", "\n", "\n", "active_assortment = active_assortment()"]}, {"cell_type": "code", "execution_count": null, "id": "68feb755-2f04-4095-9488-88381cea3fcf", "metadata": {}, "outputs": [], "source": ["active_assortment.head()"]}, {"cell_type": "code", "execution_count": null, "id": "672ba40c-6bbf-4ab9-bf65-ada21cb08def", "metadata": {}, "outputs": [], "source": ["adding_item_details = pd.merge(\n", "    active_assortment, item_details, on=[\"item_id\"], how=\"inner\"\n", ")\n", "\n", "adding_outlet_details = pd.merge(\n", "    adding_item_details, frontend_outlet_details, on=[\"facility_id\"], how=\"inner\"\n", ")\n", "\n", "adding_outlet_details = adding_outlet_details.drop(\n", "    columns={\"active\", \"ars_active\", \"is_primary\"}\n", ")\n", "\n", "adding_outlet_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bdef436d-425c-4f3f-96d3-fde87ff333b9", "metadata": {}, "outputs": [], "source": ["adding_outlet_details[\"assortment\"] = np.where(\n", "    (adding_outlet_details[\"l0_category\"] == \"Beauty & Cosmetics\"),\n", "    \"Beauty\",\n", "    np.where(\n", "        (adding_outlet_details[\"l0_category\"] == \"Books\"),\n", "        \"Books\",\n", "        np.where(\n", "            (adding_outlet_details[\"l0_category\"] == \"Toys & Games\"), \"Toys\", \"30 Mins\"\n", "        ),\n", "    ),\n", ")\n", "\n", "adding_outlet_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6c2b5136-e241-4572-b0c7-6c4ded06ec3d", "metadata": {}, "outputs": [], "source": ["item_id_list = tuple(list(adding_outlet_details[\"item_id\"].unique()))\n", "len(item_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "f9a7c44a-1011-4b59-9248-bcccd4ab565e", "metadata": {}, "outputs": [], "source": ["facility_id_list = tuple(list(adding_outlet_details[\"facility_id\"].unique()))\n", "len(facility_id_list)"]}, {"cell_type": "markdown", "id": "78cc6b3d-d35c-4910-8a2a-c2f04f4378b4", "metadata": {}, "source": ["# Tea Taggings Details"]}, {"cell_type": "code", "execution_count": null, "id": "731a67d0-6076-4f4e-a5be-b76b02a0be60", "metadata": {}, "outputs": [], "source": ["def tea_taggings():\n", "    tea_taggings = f\"\"\"\n", "    \n", "    with\n", "    tea_taggings as\n", "        (select item_id, outlet_id as hot_outlet_id, cast(tag_value as int) as be_hot_outlet_id\n", "            from rpc.item_outlet_tag_mapping\n", "                where tag_type_id = 8 and active = 1\n", "        )\n", "        \n", "            select * from tea_taggings\n", "                where item_id in {item_id_list}\n", "    \"\"\"\n", "    return read_sql_query(tea_taggings, trino)\n", "\n", "\n", "tea_taggings = tea_taggings()"]}, {"cell_type": "code", "execution_count": null, "id": "ee86361d-d733-4902-8fdd-ad667378ddcb", "metadata": {}, "outputs": [], "source": ["tea_taggings.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c17d133c-2c10-4066-81cb-12e417b48733", "metadata": {}, "outputs": [], "source": ["adding_tea_tagging_outlet = pd.merge(\n", "    tea_taggings, all_hot_name, on=[\"be_hot_outlet_id\"], how=\"inner\"\n", ")\n", "\n", "adding_tea_tagging_outlet.head()"]}, {"cell_type": "markdown", "id": "b0194e2d-0f6f-4ea3-a0a7-daa13f2b0a06", "metadata": {}, "source": ["# ARS Mapping Details"]}, {"cell_type": "code", "execution_count": null, "id": "8440774a-0b00-4e67-8533-8676ed8ce74d", "metadata": {}, "outputs": [], "source": ["def ars_mapping():\n", "    ars_mapping = \"\"\"\n", "    \n", "    with\n", "    ars_mapping as\n", "        (select facility_id as be_facility_id, outlet_id as hot_outlet_id\n", "\n", "            from po.bulk_facility_outlet_mapping\n", "\n", "                where active = true\n", "        )\n", "\n", "            select * from ars_mapping\n", "    \n", "    \"\"\"\n", "    return read_sql_query(ars_mapping, trino)\n", "\n", "\n", "ars_mapping = ars_mapping()"]}, {"cell_type": "code", "execution_count": null, "id": "3b3935aa-ffad-402b-ab07-70928f297ed5", "metadata": {}, "outputs": [], "source": ["ars_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9830ae0d-0b55-4a46-bcca-588f9938caa2", "metadata": {}, "outputs": [], "source": ["adding_ars_mapping = pd.merge(\n", "    adding_tea_tagging_outlet,\n", "    ars_mapping,\n", "    on=[\"be_facility_id\", \"hot_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_ars_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "da623b2e-45ca-413d-b18f-e61727b396f9", "metadata": {}, "outputs": [], "source": ["adding_tea_details = pd.merge(\n", "    adding_outlet_details,\n", "    adding_ars_mapping,\n", "    on=[\"item_id\", \"hot_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "\n", "adding_tea_details[\"be_inv_outlet_id\"] = np.where(\n", "    (adding_tea_details[\"be_hot_outlet_id\"] == 0),\n", "    0,\n", "    adding_tea_details[\"be_inv_outlet_id\"],\n", ")\n", "\n", "adding_tea_details[\"be_facility_id\"] = np.where(\n", "    (adding_tea_details[\"be_hot_outlet_id\"] == 0),\n", "    0,\n", "    adding_tea_details[\"be_facility_id\"],\n", ")\n", "\n", "adding_tea_details[\"be_facility_name\"] = np.where(\n", "    (adding_tea_details[\"be_hot_outlet_id\"] == 0),\n", "    \"Direct Frontend Alignment\",\n", "    adding_tea_details[\"be_facility_name\"],\n", ")\n", "\n", "adding_tea_details[[\"be_hot_outlet_id\", \"be_inv_outlet_id\", \"be_facility_id\"]] = (\n", "    adding_tea_details[[\"be_hot_outlet_id\", \"be_inv_outlet_id\", \"be_facility_id\"]]\n", "    .fillna(-1)\n", "    .astype(int)\n", ")\n", "\n", "adding_tea_details[\"be_facility_name\"] = adding_tea_details[\"be_facility_name\"].fillna(\n", "    \"Non-TEA\"\n", ")\n", "\n", "\n", "adding_tea_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8a72b5b7-818b-470c-a0be-6ad5538239f6", "metadata": {}, "outputs": [], "source": ["del [\n", "    active_assortment,\n", "    adding_outlet_details,\n", "    tea_taggings,\n", "    adding_tea_tagging_outlet,\n", "    ars_mapping,\n", "    adding_ars_mapping,\n", "    item_details,\n", "]\n", "\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "5b1618fb-65ba-48a6-8e6c-6525477fe285", "metadata": {}, "outputs": [], "source": ["be_inv_outlet_list = (\n", "    adding_tea_details[[\"be_inv_outlet_id\"]]\n", "    .rename(columns={\"be_inv_outlet_id\": \"inv_outlet_id\"})\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "fe_inv_outlet_list = (\n", "    adding_tea_details[[\"hot_outlet_id\"]]\n", "    .rename(columns={\"hot_outlet_id\": \"inv_outlet_id\"})\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "all_inv_outlet_list = be_inv_outlet_list.append(fe_inv_outlet_list)\n", "all_inv_outlet_list = tuple(list(all_inv_outlet_list[\"inv_outlet_id\"].unique()))\n", "print(all_inv_outlet_list)"]}, {"cell_type": "code", "execution_count": null, "id": "955d8b33-d2d9-4a64-9878-6e7ad954aada", "metadata": {}, "outputs": [], "source": ["be_hot_outlet_list = (\n", "    adding_tea_details[[\"be_hot_outlet_id\"]]\n", "    .rename(columns={\"be_hot_outlet_id\": \"hot_outlet_id\"})\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "fe_hot_outlet_list = (\n", "    adding_tea_details[[\"hot_outlet_id\"]]\n", "    .rename(columns={\"hot_outlet_id\": \"hot_outlet_id\"})\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "all_hot_outlet_list = be_hot_outlet_list.append(fe_hot_outlet_list)\n", "all_hot_outlet_list = tuple(list(all_hot_outlet_list[\"hot_outlet_id\"].unique()))\n", "print(all_hot_outlet_list)"]}, {"cell_type": "markdown", "id": "1d0c407d-3877-4047-a43e-4f23ac0822a9", "metadata": {}, "source": ["# Inventory Details"]}, {"cell_type": "code", "execution_count": null, "id": "e5faae2c-e763-46cd-befa-00ba02c42e2d", "metadata": {}, "outputs": [], "source": ["def inventory():\n", "    inventory = f\"\"\"\n", "    \n", "    with\n", "    inv as\n", "        (select item_id, outlet_id as hot_outlet_id, sum(quantity) as actual_inv\n", "            from ims.ims_item_inventory\n", "                where active = 1\n", "                    group by 1,2\n", "        )\n", "        \n", "            select * from inv\n", "                where hot_outlet_id in {all_inv_outlet_list}\n", "    \"\"\"\n", "    return read_sql_query(inventory, trino)\n", "\n", "\n", "inventory = inventory()"]}, {"cell_type": "code", "execution_count": null, "id": "9966de71-39e0-40f9-b430-04debff213c9", "metadata": {}, "outputs": [], "source": ["inventory.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c8f72105-ffdf-4b95-b067-07464b6e262b", "metadata": {}, "outputs": [], "source": ["be_inv = inventory.rename(\n", "    columns={\"hot_outlet_id\": \"be_inv_outlet_id\", \"actual_inv\": \"be_actual_inv\"}\n", ")\n", "be_inv.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3fb1d180-751d-41d8-98d9-7f9ff0e2273b", "metadata": {}, "outputs": [], "source": ["adding_fe_inv_details = pd.merge(\n", "    adding_tea_details, inventory, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "adding_be_inv_details = pd.merge(\n", "    adding_fe_inv_details, be_inv, on=[\"item_id\", \"be_inv_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_inv_details[[\"actual_inv\", \"be_actual_inv\"]] = (\n", "    adding_be_inv_details[[\"actual_inv\", \"be_actual_inv\"]].fillna(0).astype(int)\n", ")\n", "\n", "adding_be_inv_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8ba7eb62-14e1-4727-be4f-3cf41808e6cd", "metadata": {}, "outputs": [], "source": ["del [\n", "    adding_tea_details,\n", "    inventory,\n", "    adding_fe_inv_details,\n", "]\n", "\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "8851c6a6-bbfc-4aba-aab1-d8b34181ac98", "metadata": {}, "source": ["# Blocked Inventory Details"]}, {"cell_type": "code", "execution_count": null, "id": "82c7be24-f3fc-4b02-8581-68e1b77501fd", "metadata": {}, "outputs": [], "source": ["def blocked_inv():\n", "    blocked_inv = f\"\"\"\n", "    \n", "    with\n", "    blocked as\n", "        (select item_id, outlet_id as hot_outlet_id,\n", "            sum(quantity) as ttl_blocked_qty\n", "                from ims.ims_item_blocked_inventory\n", "                    where active = 1 and blocked_type in (1,2,5) and quantity > 0\n", "                        group by 1,2\n", "        )\n", "        \n", "            select * from blocked \n", "                where hot_outlet_id in {all_inv_outlet_list}\n", "    \"\"\"\n", "    return read_sql_query(blocked_inv, trino)\n", "\n", "\n", "blocked_inv = blocked_inv()"]}, {"cell_type": "code", "execution_count": null, "id": "7cc9b248-c5dd-4f54-a4db-5bb02a8b7c32", "metadata": {}, "outputs": [], "source": ["blocked_inv.head()"]}, {"cell_type": "code", "execution_count": null, "id": "72ccdd42-6925-49c6-a44c-1d77abe9c45a", "metadata": {}, "outputs": [], "source": ["be_blocked = blocked_inv.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_inv_outlet_id\",\n", "        \"ttl_blocked_qty\": \"be_ttl_blocked_qty\",\n", "    }\n", ")\n", "be_blocked.head()"]}, {"cell_type": "code", "execution_count": null, "id": "35660335-61ab-4300-95f5-cc3a2e0562ee", "metadata": {}, "outputs": [], "source": ["adding_fe_blocked_details = pd.merge(\n", "    adding_be_inv_details, blocked_inv, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_blocked_details = pd.merge(\n", "    adding_fe_blocked_details,\n", "    be_blocked,\n", "    on=[\"item_id\", \"be_inv_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_be_blocked_details[[\"ttl_blocked_qty\", \"be_ttl_blocked_qty\"]] = (\n", "    adding_be_blocked_details[[\"ttl_blocked_qty\", \"be_ttl_blocked_qty\"]]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_be_blocked_details[\"net_inventory\"] = np.where(\n", "    (\n", "        adding_be_blocked_details[\"actual_inv\"]\n", "        - adding_be_blocked_details[\"ttl_blocked_qty\"]\n", "    )\n", "    < 0,\n", "    0,\n", "    (\n", "        adding_be_blocked_details[\"actual_inv\"]\n", "        - adding_be_blocked_details[\"ttl_blocked_qty\"]\n", "    ),\n", ")\n", "\n", "adding_be_blocked_details[\"be_net_inventory\"] = np.where(\n", "    (\n", "        adding_be_blocked_details[\"be_actual_inv\"]\n", "        - adding_be_blocked_details[\"be_ttl_blocked_qty\"]\n", "    )\n", "    < 0,\n", "    0,\n", "    (\n", "        adding_be_blocked_details[\"be_actual_inv\"]\n", "        - adding_be_blocked_details[\"be_ttl_blocked_qty\"]\n", "    ),\n", ")\n", "\n", "\n", "adding_be_blocked_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0dbd3e9a-5a58-435b-bca5-17180a6a2147", "metadata": {}, "outputs": [], "source": ["del [\n", "    blocked_inv,\n", "    adding_fe_blocked_details,\n", "    adding_be_inv_details,\n", "]\n", "\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "a6cebf26-7b65-4291-8aa1-3be2b4802e03", "metadata": {}, "source": ["# Pending Putaway Details"]}, {"cell_type": "code", "execution_count": null, "id": "73de3390-74b8-45c2-9df4-4e2ce9b2bfad", "metadata": {}, "outputs": [], "source": ["def pen_put():\n", "    pen_put = f\"\"\"\n", "    \n", "    with\n", "    pp as\n", "        (select rpc.item_id, outlet_id as hot_outlet_id, sum(quantity) as pending_putaway\n", "            from ims.ims_good_inventory igi\n", "                join rpc.product_product rpc on rpc.upc = igi.upc_id and igi.variant_id = rpc.variant_id\n", "                    where igi.active = 1 and igi.inventory_update_type_id in (28,76)\n", "                        group by 1,2\n", "        )\n", "        \n", "            select * from pp \n", "                where hot_outlet_id in {all_inv_outlet_list}\n", "    \"\"\"\n", "    return read_sql_query(pen_put, trino)\n", "\n", "\n", "pen_put = pen_put()"]}, {"cell_type": "code", "execution_count": null, "id": "6e9f9e63-aee4-4be3-807c-e035d6250380", "metadata": {}, "outputs": [], "source": ["pen_put.head()"]}, {"cell_type": "code", "execution_count": null, "id": "60d6a8d7-0e1e-4c8e-b556-8ea0d9162b99", "metadata": {}, "outputs": [], "source": ["be_pen_put = pen_put.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_inv_outlet_id\",\n", "        \"pending_putaway\": \"be_pending_putaway\",\n", "    }\n", ")\n", "be_pen_put.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4ff53d7a-bb5f-4ba8-b4d6-0b9bbfad8688", "metadata": {}, "outputs": [], "source": ["adding_fe_pen_putaway_details = pd.merge(\n", "    adding_be_blocked_details, pen_put, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_pen_putaway_details = pd.merge(\n", "    adding_fe_pen_putaway_details,\n", "    be_pen_put,\n", "    on=[\"item_id\", \"be_inv_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "adding_be_pen_putaway_details[[\"pending_putaway\", \"be_pending_putaway\"]] = (\n", "    adding_be_pen_putaway_details[[\"pending_putaway\", \"be_pending_putaway\"]]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_be_pen_putaway_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ac5876fc-d1ba-42ff-bb23-5e719226bf7e", "metadata": {}, "outputs": [], "source": ["del [\n", "    pen_put,\n", "    adding_be_blocked_details,\n", "    adding_fe_pen_putaway_details,\n", "]\n", "\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "6699f828-14ee-46fe-845a-4e7b48086a04", "metadata": {}, "source": ["# frontend min max details"]}, {"cell_type": "code", "execution_count": null, "id": "32a825a3-b2c1-403b-ba87-c85082730342", "metadata": {}, "outputs": [], "source": ["def min_max():\n", "    min_max = f\"\"\"\n", "    \n", "    with\n", "    min_max as\n", "        (select facility_id, item_id, min_quantity, max_quantity\n", "        \n", "            from ars.item_min_max_quantity\n", "        )\n", "        \n", "            select * from min_max \n", "                where facility_id in {facility_id_list}\n", "    \n", "    \"\"\"\n", "    return read_sql_query(min_max, trino)\n", "\n", "\n", "min_max = min_max()"]}, {"cell_type": "code", "execution_count": null, "id": "d989c274-4e62-4fee-bf85-ebfef2221b18", "metadata": {}, "outputs": [], "source": ["min_max.head()"]}, {"cell_type": "code", "execution_count": null, "id": "1337826e-181a-40a2-ad9a-f46125151600", "metadata": {}, "outputs": [], "source": ["adding_min_max_details = pd.merge(\n", "    adding_be_pen_putaway_details, min_max, on=[\"item_id\", \"facility_id\"], how=\"left\"\n", ")\n", "\n", "adding_min_max_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "012f067a-1d69-481e-9b05-9bde925b8df2", "metadata": {}, "outputs": [], "source": ["del [\n", "    adding_be_pen_putaway_details,\n", "    min_max,\n", "]\n", "\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "53d4cbf6-a4b6-4981-b64d-f471578a35fb", "metadata": {}, "source": ["# Open STO Details"]}, {"cell_type": "code", "execution_count": null, "id": "09c52819-ece8-45dd-a2e2-1dea6f39ec61", "metadata": {}, "outputs": [], "source": ["def sto_details():\n", "    sto_details = f\"\"\"\n", "    \n", "    with\n", "    sto_details as\n", "        (select (created_at + interval '330' minute) as created_time,\n", "            date(delivery_date + interval '330' minute) as expiry_date, extract(hour from (delivery_date + interval '330' minute)) as expiry_hour,\n", "            sto_id, outlet_id as sender_outlet_id, merchant_outlet_id as receiver_outlet_id,\n", "                case when sto_state = 1 then 'created'\n", "                    when sto_state = 2 then 'billed' when sto_state = 3 then 'expired'\n", "                    when sto_state = 4 then 'inward' when sto_state = 5 then 'partial-billed'\n", "                        else null end as sto_state\n", "\n", "                from ims.ims_sto_details sd\n", "\n", "                    where sto_state not in (3,4)\n", "                        and created_at >= current_date - interval '42' day\n", "        ),\n", "\n", "    sto_item_details as\n", "        (select created_time, expiry_date, expiry_hour,\n", "            si.sto_id, sender_outlet_id, receiver_outlet_id, item_id, reserved_quantity as sto_quantity, sto_state\n", "\n", "                from ims.ims_sto_item si\n", "\n", "                    join\n", "                        sto_details sd on sd.sto_id = si.sto_id\n", "                        where created_at >= current_date - interval '42' day\n", "        ),\n", "\n", "    invoice_details as\n", "        (select cast(grofers_order_id as int) as sto_id,\n", "            invoice_id, pi.id, pi.outlet_id,\n", "            (pos_timestamp + interval '330' minute) as last_billed_time,\n", "\n", "            case when cast(transfer_state as int) = 1 then 'raised'\n", "                when cast(transfer_state as int) = 2 then 'grn complete' when cast(transfer_state as int) = 3 then 'grn partial'\n", "                when cast(transfer_state as int) = 4 then 'grn force complete' when cast(transfer_state as int) = 3 then 'discrepancy note generated'\n", "                    else null end as invoice_state\n", "\n", "                from pos.pos_invoice pi\n", "\n", "                    where insert_ds_ist >= cast((current_date - interval '42' day) as varchar)\n", "                        and invoice_type_id in (5,14,16)\n", "                        and grofers_order_id != ''\n", "                        and grofers_order_id is not null\n", "        ),\n", "\n", "    invoice_item_details as\n", "        (select item_id, outlet_id, sto_id, sum(quantity) as billed_quantity\n", "            from pos.pos_invoice_product_details pd\n", "\n", "                join (select distinct item_id, upc from rpc.product_product\n", "                    where id in (select max(id) as id from rpc.product_product where active = 1 and approved = 1 group by upc)\n", "                        ) rpp on rpp.upc = pd.upc_id\n", "\n", "                join \n", "                    invoice_details id on id.id = pd.invoice_id\n", "\n", "                    where insert_ds_ist >= cast((current_date - interval '42' day) as varchar)\n", "\n", "                        group by 1,2,3\n", "        ),\n", "\n", "    grn_details as\n", "        (select ii.grn_id, ii.vendor_invoice_id, id.sto_id\n", "            from ims.ims_inward_invoice ii\n", "\n", "                join\n", "                    invoice_details id on id.invoice_id = ii.vendor_invoice_id\n", "\n", "                    where insert_ds_ist >= cast((current_date - interval '42' day) as varchar)\n", "                        and source_type = 2\n", "        ),\n", "\n", "    grn_item_details as\n", "        (select item_id, outlet_id, sto_id, max(created_at + interval '330' minute) as last_inward_time,\n", "            sum(\"delta\") as grn_quantity\n", "            from ims.ims_inventory_stock_details sd\n", "\n", "                join (select distinct item_id, upc from rpc.product_product\n", "                    where id in (select max(id) as id from rpc.product_product where active = 1 and approved = 1 group by upc)\n", "                        ) rpp on rpp.upc = sd.upc_id\n", "\n", "                join \n", "                    grn_details gd on gd.grn_id = sd.grn_id\n", "\n", "                    where insert_ds_ist >= cast((current_date - interval '42' day) as varchar)\n", "\n", "                        group by 1,2,3\n", "        ),\n", "\n", "    discrepancy_details as\n", "        (select dn.id, dn.vendor_invoice_id, sto_id, max(created_at + interval '330' minute) as last_discrepancy_time from pos.discrepancy_note dn\n", "            join\n", "                invoice_details id on id.invoice_id = dn.vendor_invoice_id\n", "            where created_at >= current_date - interval '42' day\n", "                group by 1,2,3\n", "        ),\n", "\n", "    discrepancy_item_details as\n", "        (select item_id, sto_id, sum(quantity) as discrepancy_quantity\n", "            from pos.discrepancy_note_product_detail pd\n", "\n", "                join (select distinct item_id, upc from rpc.product_product\n", "                    where id in (select max(id) as id from rpc.product_product where active = 1 and approved = 1 group by upc)\n", "                        ) rpp on rpp.upc = pd.upc_id\n", "\n", "                join\n", "                    discrepancy_details dd on dd.id = pd.dn_id_id\n", "\n", "                    group by 1,2\n", "        ),\n", "\n", "    final as\n", "        (select sid.created_time, sid.expiry_date, sid.expiry_hour,\n", "            sid.sto_id, sid.sender_outlet_id, sid.receiver_outlet_id,\n", "            sid.item_id, sid.sto_quantity, \n", "            case when billed_quantity is null then 0 else billed_quantity end as billed_quantity,\n", "            case when grn_quantity is null then 0 else grn_quantity end as grn_quantity,\n", "            case when discrepancy_quantity is null then 0 else discrepancy_quantity end as discrepancy_quantity,\n", "            sto_state,\n", "            case when sto_quantity = ((case when grn_quantity is null then 0 else grn_quantity end) + (case when discrepancy_quantity is null then 0 else discrepancy_quantity end)) then 1 else 0 end as flag\n", "\n", "                from sto_item_details sid\n", "\n", "                    left join\n", "                        invoice_item_details iid on iid.sto_id = sid.sto_id \n", "                            and iid.item_id = sid.item_id\n", "\n", "                    left join\n", "                        grn_item_details gid on gid.sto_id = sid.sto_id \n", "                            and gid.item_id = sid.item_id\n", "\n", "                    left join\n", "                        discrepancy_item_details did on did.sto_id = sid.sto_id\n", "                            and did.item_id = sid.item_id\n", "        ),\n", "\n", "    open_details as\n", "        (select receiver_outlet_id as hot_outlet_id, item_id,\n", "            sum(case when billed_quantity > 0 then 0\n", "                when grn_quantity > 0 then 0\n", "                when discrepancy_quantity > 0 then 0 else sto_quantity end) as open_sto_quantity,\n", "\n", "            sum(case when \n", "                billed_quantity - (grn_quantity + discrepancy_quantity) > 0\n", "                    then billed_quantity - (grn_quantity + discrepancy_quantity) else billed_quantity end) as in_transit_open_sto_quantity\n", "\n", "                from final\n", "                    where flag = 0\n", "                        group by 1,2\n", "        )\n", "        \n", "            select hot_outlet_id, item_id, in_transit_open_sto_quantity, open_sto_quantity\n", "                    from open_details\n", "                        where hot_outlet_id in {all_inv_outlet_id_list} and item_id in {item_id_list}\n", "                        \n", "    \"\"\"\n", "    return read_sql_query(sto_details, trino)\n", "\n", "\n", "sto_details = sto_details()"]}, {"cell_type": "code", "execution_count": null, "id": "d20ac981-4f67-48aa-b6ea-ccfff41f2cd1", "metadata": {}, "outputs": [], "source": ["sto_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "dd79b038-1bd2-47d1-8fc7-e22daafc005a", "metadata": {}, "outputs": [], "source": ["be_sto = sto_details.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_inv_outlet_id\",\n", "        \"in_transit_open_sto_quantity\": \"be_in_transit_open_sto_quantity\",\n", "        \"open_sto_quantity\": \"be_open_sto_quantity\",\n", "    }\n", ")\n", "be_sto.head()"]}, {"cell_type": "code", "execution_count": null, "id": "fa469201-33bb-48e0-9e9a-6d468dad5f5f", "metadata": {}, "outputs": [], "source": ["adding_fe_sto_details = pd.merge(\n", "    adding_min_max_details,\n", "    sto_details,\n", "    on=[\"item_id\", \"hot_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_be_sto_details = pd.merge(\n", "    adding_fe_sto_details, be_sto, on=[\"item_id\", \"be_inv_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_sto_details[\n", "    [\"open_sto_quantity\", \"be_in_transit_open_sto_quantity\", \"be_open_sto_quantity\"]\n", "] = (\n", "    adding_be_sto_details[\n", "        [\"open_sto_quantity\", \"be_in_transit_open_sto_quantity\", \"be_open_sto_quantity\"]\n", "    ]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_be_sto_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a5de1c93-22c0-4650-beb1-6854f45f922c", "metadata": {}, "outputs": [], "source": ["del [sto_details, adding_min_max_details, adding_fe_sto_details, be_sto]\n", "\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "eb5d4552-595e-4ede-9637-0daa9488ea28", "metadata": {}, "source": ["# Open PO Details"]}, {"cell_type": "code", "execution_count": null, "id": "1ccc31b8-4182-4dae-8c15-394132b91a84", "metadata": {}, "outputs": [], "source": ["def po_details():\n", "    po_details = f\"\"\"\n", "    \n", "    with\n", "    po_details as\n", "        (select\n", "            poi.po_id,\n", "            po.po_number,\n", "            date(po.created_at + interval '330' minute) as po_issue_date,\n", "            date(po.expiry_date + interval '330' minute) as po_expiry_date,\n", "            ps.po_scheduled_date,\n", "            poi.manufacturer_id,\n", "            poi.manufacturer_name,\n", "            po.vendor_id,\n", "            po.vendor_name,\n", "            po.outlet_id as be_hot_outlet_id,\n", "            poi.item_id,\n", "            poi.name || ' ' || poi.uom_text as item_name,\n", "            poi.units_ordered as po_quantity,\n", "            case when grn.grn_quantity is null then 0 else grn.grn_quantity end as grn_quantity\n", "\n", "                from po.purchase_order_items poi\n", "\n", "                    join\n", "                        po.purchase_order po on po.id = poi.po_id\n", "                            and po.created_at >= cast(current_date as timestamp) - interval '60' day - interval '330' minute\n", "\n", "                    left join\n", "                        (select po_id, po_state_id\n", "                            from po.purchase_order_status pos\n", "                                where pos.created_at >= cast(current_date as timestamp) - interval '60' day - interval '330' minute\n", "                        ) pos on pos.po_id = po.id\n", "\n", "                    left join\n", "                        (select po_id, item_id, sum(quantity) as grn_quantity\n", "                            from po.po_grn\n", "                                where (insert_ds_ist >= cast(current_date - interval '60' day as varchar))\n", "                                        group by 1,2\n", "                        ) grn on grn.item_id = poi.item_id and grn.po_id = poi.po_id\n", "\n", "                    left join\n", "                        (select po_id_id,  (schedule_date_time + interval '330' minute) as po_scheduled_date\n", "                            from po.po_schedule\n", "                        ) ps on ps.po_id_id = po.id\n", "\n", "                        where (pos.po_state_id in (2,3,13,14,15) or po.is_multiple_grn = 1)\n", "                            and pos.po_state_id not in (4,5,8,10) and po.po_type_id !=11\n", "                            and (po.created_at >= cast(current_date as timestamp) - interval '60' day - interval '330' minute)\n", "                            and po.active = 1\n", "        ),\n", "\n", "    final_po_details as\n", "        (select po_number, po_issue_date, po_expiry_date, date(po_scheduled_date) as po_scheduled_date,\n", "            be_hot_outlet_id, item_id, po_quantity, grn_quantity, (po_quantity - grn_quantity) as remaining_po_quantity\n", "\n", "                from po_details\n", "        )\n", "\n", "            select be_hot_outlet_id, item_id,\n", "                sum(remaining_po_quantity) as total_po_quantity,\n", "                sum(case when po_scheduled_date = date(current_date) then remaining_po_quantity else 0 end) as t_scheduled_quantity,\n", "                sum(case when po_scheduled_date = date(current_date + interval '1' day) then remaining_po_quantity else 0 end) as t1_scheduled_quantity,\n", "                sum(case when po_scheduled_date = date(current_date + interval '2' day) then remaining_po_quantity else 0 end) as t2_scheduled_quantity,\n", "                sum(case when po_scheduled_date = date(current_date + interval '3' day) then remaining_po_quantity else 0 end) as t3_scheduled_quantity,\n", "                sum(case when po_scheduled_date = date(current_date + interval '4' day) then remaining_po_quantity else 0 end) as t4_scheduled_quantity\n", "\n", "                    from final_po_details\n", "\n", "                        where be_hot_outlet_id in {all_hot_outlet_list} and item_id in {item_id_list}\n", "\n", "                            group by 1,2\n", "                        \n", "    \"\"\"\n", "    return read_sql_query(po_details, trino)\n", "\n", "\n", "po_details = po_details()"]}, {"cell_type": "code", "execution_count": null, "id": "706a6242-456d-46b4-9cbd-c02f66e97b47", "metadata": {}, "outputs": [], "source": ["po_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "25d5571c-6ac3-458f-a6e6-7f2a48e46633", "metadata": {}, "outputs": [], "source": ["fe_po_details = po_details.copy()\n", "\n", "fe_po_details = fe_po_details[[\"item_id\", \"be_hot_outlet_id\", \"total_po_quantity\"]]\n", "\n", "fe_po_details = fe_po_details.rename(\n", "    columns={\n", "        \"be_hot_outlet_id\": \"hot_outlet_id\",\n", "        \"total_po_quantity\": \"fe_total_po_quantity\",\n", "    }\n", ")\n", "\n", "fe_po_details.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "e167fde4-4c03-48ca-8f75-de3920925d73", "metadata": {}, "outputs": [], "source": ["adding_fe_po_details = pd.merge(\n", "    adding_be_sto_details, fe_po_details, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_po_details = pd.merge(\n", "    adding_fe_po_details, po_details, on=[\"item_id\", \"be_hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_po_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ca782803-dc3e-46ce-9187-40adf8e14f09", "metadata": {}, "outputs": [], "source": ["del [\n", "    adding_be_sto_details,\n", "    po_details,\n", "    fe_po_details,\n", "    adding_fe_po_details,\n", "]\n", "\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "2f181f98-a53d-4a68-a696-0fe608a3f287", "metadata": {}, "source": ["# Sales Details"]}, {"cell_type": "code", "execution_count": null, "id": "adafa773-0816-40a9-b206-51eec9e3d4cb", "metadata": {}, "outputs": [], "source": ["def sales():\n", "    sales = f\"\"\"\n", "    \n", "    with\n", "    item_mapping as\n", "        (select distinct ipr.product_id,\n", "            case when ipr.item_id is null then ipom_0.item_id else ipr.item_id end as item_id,\n", "            case when ipr.item_id is not null then COALESCE(ipom.multiplier,1) else COALESCE(ipom_0.multiplier,1) end as multiplier\n", "\n", "                from lake_rpc.item_product_mapping ipr\n", "\n", "                    left join\n", "                        dwh.dim_item_product_offer_mapping ipom on ipom.product_id = ipr.product_id\n", "                            and ipr.item_id = ipom.item_id\n", "                    left join\n", "                        dwh.dim_item_product_offer_mapping ipom_0 on ipom_0.product_id = ipr.product_id\n", "        ),\n", "\n", "    sales as\n", "        (select \n", "            (oid.cart_checkout_ts_ist) as order_date,\n", "            oid.outlet_id,\n", "            oid.product_id,\n", "            im.item_id,\n", "            oid.order_id,\n", "            oid.dim_customer_key,\n", "            ((unit_selling_price * 1.00)/im.multiplier) as item_selling_price,\n", "            ((oid.procured_quantity - oid.total_doorstep_return_quantity) * im.multiplier) as sales_quantity,\n", "            (sales_quantity * item_selling_price) as sales_value\n", "\n", "                from dwh.fact_sales_order_item_details oid\n", "\n", "                    join item_mapping im on im.product_id = oid.product_id\n", "\n", "                    join lake_retail.console_outlet rco on rco.id = oid.outlet_id and business_type_id in (7) and rco.id in {all_inv_outlet_list}\n", "\n", "                        where (oid.cart_checkout_ts_ist between (current_date - 30 || ' 00:00:00')::timestamp and (current_date || ' 23:59:59')::timestamp)\n", "                            and oid.is_internal_order = false\n", "                            and (oid.order_type not ilike '%%internal%%' or oid.order_type is null)\n", "                            and oid.procured_quantity > 0\n", "                            and oid.order_current_status = 'DELIVERED'\n", "        ),\n", "\n", "    final_sales as\n", "        (select \n", "            date(order_date) as date_,\n", "            outlet_id as hot_outlet_id,\n", "            s.item_id,\n", "            s.order_id,\n", "            sum(sales_quantity) as sales_qty,\n", "            sum(sales_value) as sales_value\n", "\n", "                from sales s\n", "\n", "                    where sales_quantity > 0\n", "\n", "                        group by 1,2,3,4\n", "        ),\n", "\n", "    final as\n", "        (select date_, hot_outlet_id, item_id, count(distinct order_id) as carts, sum(sales_qty) as sales_qty, sum(sales_value) as sales_value\n", "            from final_sales\n", "                group by 1,2,3\n", "        )\n", "\n", "            select hot_outlet_id, item_id,\n", "                sum(case when date_ = current_date then sales_qty end) as today_sales_qty,\n", "                sum(case when date_ = current_date then sales_value end) as today_sales_value,\n", "                sum(case when date_ = current_date then carts end) as today_carts,\n", "\n", "                sum(case when date_ = current_date - 1 then sales_qty end) as yesterday_sales_qty,\n", "                sum(case when date_ = current_date - 1 then sales_value end) as yesterday_sales_value,\n", "                sum(case when date_ = current_date - 1 then carts end) as yesterday_carts,\n", "\n", "                sum(case when date_ between current_date - 7 and current_date - 1 then sales_qty end) as l7days_sales_qty,\n", "                sum(case when date_ between current_date - 7 and current_date - 1 then sales_value end) as l7days_sales_value,\n", "                sum(case when date_ between current_date - 7 and current_date - 1 then carts end) as l7days_carts,\n", "\n", "                sum(case when date_ between current_date - 30 and current_date - 1 then sales_qty end) as l30days_sales_qty,\n", "                sum(case when date_ between current_date - 30 and current_date - 1 then sales_value end) as l30days_sales_value,\n", "                sum(case when date_ between current_date - 30 and current_date - 1 then carts end) as l30days_carts\n", "\n", "                    from final\n", "\n", "                            group by 1,2\n", "    \n", "    \"\"\"\n", "    return read_sql_query(sales, redshift)\n", "\n", "\n", "sales = sales()"]}, {"cell_type": "code", "execution_count": null, "id": "5d399868-c911-4ad5-aa42-364bec6c38ba", "metadata": {}, "outputs": [], "source": ["sales.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3e493ef3-ebde-49cb-bc7b-38ccccc3710a", "metadata": {}, "outputs": [], "source": ["adding_sales_details = pd.merge(\n", "    adding_po_details, sales, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_sales_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6b354bf5-ae54-4ab9-9243-1858b98faa90", "metadata": {}, "outputs": [], "source": ["del [\n", "    adding_po_details,\n", "    sales,\n", "]\n", "\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "b2441d12-9c23-4bd1-ab79-af20f2740624", "metadata": {}, "source": ["# dump details"]}, {"cell_type": "code", "execution_count": null, "id": "ed8713e3-e3fc-408d-83b6-77886e076aeb", "metadata": {}, "outputs": [], "source": ["def dump():\n", "    dump = f\"\"\"\n", "    \n", "    with\n", "    dump as\n", "        (select date(pos_timestamp + interval '5.5 Hours') as date_, log.outlet_id as hot_outlet_id, rpc.item_id, \n", "            sum(log.\"delta\") as dump_quantity, sum(log.\"delta\" * weighted_lp) as dump_value\n", "\n", "                from lake_ims.ims_inventory_log log\n", "\n", "                    join\n", "                        lake_rpc.product_product rpc on rpc.variant_id = log.variant_id\n", "\n", "                        where inventory_update_type_id in (11,12,13,64,87,88,89,7,33,9,34,63,67)\n", "                            and pos_timestamp between (current_date - 30 || ' 00:00:00')::timestamp - interval '5.5 Hours'\n", "                            and (current_date || ' 23:59:59')::timestamp - interval '5.5 Hours'\n", "                            and outlet_id in {all_inv_outlet_list}\n", "\n", "                            group by 1,2,3\n", "        ),\n", "\n", "    final as\n", "        (select hot_outlet_id, item_id, \n", "            sum(case when date_ = current_date then dump_quantity end) as today_dump_qty,\n", "            sum(case when date_ = current_date then dump_value end) as today_dump_value,\n", "\n", "            sum(case when date_ = current_date - 1 then dump_quantity end) as yesterday_dump_qty,\n", "            sum(case when date_ = current_date - 1 then dump_value end) as yesterday_dump_value,\n", "\n", "            sum(case when date_ between current_date - 7 and current_date - 1 then dump_quantity end) as l7days_dump_qty,\n", "            sum(case when date_ between current_date - 7 and current_date - 1 then dump_value end) as l7days_dump_value,\n", "\n", "            sum(case when date_ between current_date - 30 and current_date - 1 then dump_quantity end) as l30days_dump_qty,\n", "            sum(case when date_ between current_date - 30 and current_date - 1 then dump_value end) as l30days_dump_value\n", "\n", "                from dump\n", "\n", "                    group by 1,2\n", "        )\n", "\n", "            select * from final\n", "    \n", "    \"\"\"\n", "    return read_sql_query(dump, redshift)\n", "\n", "\n", "dump = dump()"]}, {"cell_type": "code", "execution_count": null, "id": "766ac408-2192-4c3b-a5c0-30cd7014ef02", "metadata": {}, "outputs": [], "source": ["dump.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7bf28fdc-7fac-421c-ad6b-8066ac5fd7a0", "metadata": {}, "outputs": [], "source": ["be_dump = dump.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_inv_outlet_id\",\n", "        \"today_dump_qty\": \"be_today_dump_qty\",\n", "        \"today_dump_value\": \"be_today_dump_value\",\n", "        \"yesterday_dump_qty\": \"be_yesterday_dump_qty\",\n", "        \"yesterday_dump_value\": \"be_yesterday_dump_value\",\n", "        \"l7days_dump_qty\": \"be_l7days_dump_qty\",\n", "        \"l7days_dump_value\": \"be_l7days_dump_value\",\n", "        \"l30days_dump_qty\": \"be_l30days_dump_qty\",\n", "        \"l30days_dump_value\": \"be_l30days_dump_value\",\n", "    }\n", ")\n", "\n", "be_dump.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c311662b-49b3-4f1b-b230-8f584308441b", "metadata": {}, "outputs": [], "source": ["adding_dump_details = pd.merge(\n", "    adding_sales_details, dump, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "adding_be_dump_details = pd.merge(\n", "    adding_dump_details, be_dump, on=[\"item_id\", \"be_inv_outlet_id\"], how=\"left\"\n", ")\n", "\n", "\n", "adding_be_dump_details[\n", "    [\n", "        \"today_dump_qty\",\n", "        \"yesterday_dump_qty\",\n", "        \"l7days_dump_qty\",\n", "        \"l30days_dump_qty\",\n", "        \"be_today_dump_qty\",\n", "        \"be_yesterday_dump_qty\",\n", "        \"be_l7days_dump_qty\",\n", "        \"be_l30days_dump_qty\",\n", "    ]\n", "] = (\n", "    adding_be_dump_details[\n", "        [\n", "            \"today_dump_qty\",\n", "            \"yesterday_dump_qty\",\n", "            \"l7days_dump_qty\",\n", "            \"l30days_dump_qty\",\n", "            \"be_today_dump_qty\",\n", "            \"be_yesterday_dump_qty\",\n", "            \"be_l7days_dump_qty\",\n", "            \"be_l30days_dump_qty\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_be_dump_details[\n", "    [\n", "        \"today_dump_value\",\n", "        \"yesterday_dump_value\",\n", "        \"l7days_dump_value\",\n", "        \"l30days_dump_value\",\n", "        \"be_today_dump_value\",\n", "        \"be_yesterday_dump_value\",\n", "        \"be_l7days_dump_value\",\n", "        \"be_l30days_dump_value\",\n", "    ]\n", "] = (\n", "    adding_be_dump_details[\n", "        [\n", "            \"today_dump_value\",\n", "            \"yesterday_dump_value\",\n", "            \"l7days_dump_value\",\n", "            \"l30days_dump_value\",\n", "            \"be_today_dump_value\",\n", "            \"be_yesterday_dump_value\",\n", "            \"be_l7days_dump_value\",\n", "            \"be_l30days_dump_value\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(float)\n", ")\n", "\n", "adding_be_dump_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "581f3cc6-4c5b-45b3-b2e7-ab3db296c0e4", "metadata": {}, "outputs": [], "source": ["del [\n", "    adding_sales_details,\n", "    dump,\n", "    be_dump,\n", "    adding_dump_details,\n", "]\n", "\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "d27a49ba-2316-4d05-8427-d58a17f87753", "metadata": {}, "source": ["# ARS Run_id Detail"]}, {"cell_type": "code", "execution_count": null, "id": "b074810b-5fc0-49b3-a0c4-f6e4b94c1130", "metadata": {}, "outputs": [], "source": ["def ars_run_id():\n", "    ars_run_id = f\"\"\"\n", "    \n", "    with\n", "    ars_run_id as\n", "        (select \n", "            extract(hour from (started_at + interval '330' minute)) as hour_,\n", "            id,\n", "            run_id,\n", "            outlet_id,\n", "            run_date,\n", "            success,\n", "            (started_at + interval '330' minute) as started_at,\n", "            (completed_at + interval '330' minute) as completed_at,\n", "            details,\n", "            facility_id,\n", "            is_simulation,\n", "            simulation_params,\n", "            extra,\n", "            json_format(json_extract(extra,'$.auto_sto_outlets')) AS ars_run,\n", "            replace(json_extract_scalar(simulation_params,'$.run'), '\"', '') as ars_run_flag,\n", "            json_extract_scalar(simulation_params, '$.manual') as manual_run,\n", "            replace(json_extract_scalar(simulation_params, '$.mode'),'\"','') as ars_mode\n", "\n", "                from ars.job_run\n", "\n", "                    where started_at >= cast(current_date as timestamp) - interval '8' day - interval '330' minute\n", "                        and replace(replace(json_format(json_extract(extra,'$.auto_sto_outlets')), '[',''), ']','') is not null\n", "                        and replace(replace(json_format(json_extract(extra,'$.auto_sto_outlets')), '[',''), ']','') != ''\n", "        ),\n", "\n", "    ars_frontend_outlets as\n", "        (select date(started_at) as date_, hour_, facility_id as be_facility_id, cast(replace(replace(split_b, '[', ''), ']', '') as int) as fe_outlet_id, run_id\n", "            from ars_run_id\n", "                cross join unnest(split(ars_run,',')) AS t (split_b)\n", "\n", "                    where ars_run is not null\n", "        ),\n", "\n", "    final_ars_run_id as\n", "        (select afo.date_, afo.hour_, afo.be_facility_id as be_facility_id, afo.fe_outlet_id as hot_outlet_id, rco.facility_id, afo.run_id\n", "\n", "            from ars_frontend_outlets afo\n", "\n", "                join retail.console_outlet rco on rco.id = afo.fe_outlet_id\n", "        )\n", "\n", "            select * from final_ars_run_id   \n", "            \n", "    \"\"\"\n", "    return read_sql_query(ars_run_id, trino)\n", "\n", "\n", "ars_run_id = ars_run_id()"]}, {"cell_type": "code", "execution_count": null, "id": "33aa6f7a-8dd0-475e-aad5-f80c84cc7f89", "metadata": {}, "outputs": [], "source": ["ars_run_id.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a9fa9c62-4716-4eb6-a0b7-aaed20f4dc91", "metadata": {}, "outputs": [], "source": ["run_id_list = list(ars_run_id[\"run_id\"].unique())\n", "run_id_list = tuple(run_id_list)\n", "len(run_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "0b660bd0-7111-4ed0-b5b9-ab900a80e8a7", "metadata": {}, "outputs": [], "source": ["min_live_date = ars_run_id.agg({\"date_\": \"min\"}).reset_index().iloc[:, 1][0]\n", "min_live_date"]}, {"cell_type": "markdown", "id": "9d32e221-68b3-4134-a42a-700df5faf6f8", "metadata": {}, "source": ["# truncation details"]}, {"cell_type": "code", "execution_count": null, "id": "1478be12-55dc-46a0-9262-e1c7b5ce9997", "metadata": {}, "outputs": [], "source": ["def truncation_details():\n", "    truncation_details = f\"\"\"\n", "    \n", "    with\n", "    truncation_details as\n", "        (select backend_outlet_id as be_hot_outlet_id, frontend_outlet_id as hot_outlet_id, item_id, sto_quantity as v1_indent,\n", "            sto_quantity_post_truncation_in_case as v2_indent, run_id,\n", "            json_extract_scalar(quantity_drops, '$.inward_drop') as inward_drop,\n", "            json_extract_scalar(quantity_drops, '$.storage_drop') as storage_drop,\n", "            json_extract_scalar(quantity_drops, '$.truck_load_drop') as truck_load_drop,\n", "            json_extract_scalar(quantity_drops, '$.picking_capacity_sku_drop') as picking_capacity_sku_drop,\n", "            json_extract_scalar(quantity_drops, '$.picking_capacity_quantity_drop') as picking_capacity_quantity_drop,\n", "            (sto_quantity_post_truncation - sto_quantity_post_truncation_in_case) as loose_quantity_drop\n", "                from ars.transfers_optimization_results_v2\n", "                    where insert_ds_ist >= cast('{min_live_date}' as varchar)\n", "        )\n", "\n", "            select be_hot_outlet_id, hot_outlet_id, item_id, cast(v1_indent as int) as v1_indent, cast(v2_indent as int) as v2_indent, run_id,\n", "                inward_drop, storage_drop, truck_load_drop, picking_capacity_sku_drop, picking_capacity_quantity_drop, loose_quantity_drop\n", "\n", "                    from truncation_details\n", "\n", "                        where run_id in {run_id_list} and item_id in {item_id_list}\n", "    \n", "    \"\"\"\n", "    return read_sql_query(truncation_details, trino)\n", "\n", "\n", "truncation_details = truncation_details()"]}, {"cell_type": "code", "execution_count": null, "id": "c06191ff-e653-4a44-9027-ea6ecac1b71a", "metadata": {}, "outputs": [], "source": ["truncation_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0d0ce6ea-5675-442e-94e0-7363568da3b8", "metadata": {}, "outputs": [], "source": ["truncation_details[\n", "    [\n", "        \"v1_indent\",\n", "        \"v2_indent\",\n", "        \"inward_drop\",\n", "        \"storage_drop\",\n", "        \"truck_load_drop\",\n", "        \"picking_capacity_sku_drop\",\n", "        \"picking_capacity_quantity_drop\",\n", "        \"loose_quantity_drop\",\n", "    ]\n", "] = (\n", "    truncation_details[\n", "        [\n", "            \"v1_indent\",\n", "            \"v2_indent\",\n", "            \"inward_drop\",\n", "            \"storage_drop\",\n", "            \"truck_load_drop\",\n", "            \"picking_capacity_sku_drop\",\n", "            \"picking_capacity_quantity_drop\",\n", "            \"loose_quantity_drop\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(float)\n", ")\n", "\n", "adding_run_id = pd.merge(\n", "    truncation_details, ars_run_id, on=[\"run_id\", \"hot_outlet_id\"], how=\"inner\"\n", ")\n", "\n", "adding_run_id[\"date_\"] = pd.to_datetime(adding_run_id[\"date_\"])\n", "\n", "adding_run_id.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d3c10f8b-4bf4-4340-a29c-4c3ef6685ced", "metadata": {}, "outputs": [], "source": ["new_truncation_details = adding_run_id.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "2c0060b6-7b21-4a87-b900-48d688c77554", "metadata": {}, "outputs": [], "source": ["new_truncation_details[\"new_inward_drop\"] = np.where(\n", "    (new_truncation_details[\"inward_drop\"] > 0)\n", "    & (new_truncation_details[\"inward_drop\"] > new_truncation_details[\"storage_drop\"])\n", "    & (\n", "        new_truncation_details[\"inward_drop\"]\n", "        > new_truncation_details[\"truck_load_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"inward_drop\"]\n", "        > new_truncation_details[\"picking_capacity_sku_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"inward_drop\"]\n", "        > new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"inward_drop\"]\n", "        > new_truncation_details[\"loose_quantity_drop\"]\n", "    ),\n", "    new_truncation_details[\"inward_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"new_storage_drop\"] = np.where(\n", "    (new_truncation_details[\"storage_drop\"] > 0)\n", "    & (new_truncation_details[\"storage_drop\"] > new_truncation_details[\"inward_drop\"])\n", "    & (\n", "        new_truncation_details[\"storage_drop\"]\n", "        > new_truncation_details[\"truck_load_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"storage_drop\"]\n", "        > new_truncation_details[\"picking_capacity_sku_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"storage_drop\"]\n", "        > new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"storage_drop\"]\n", "        > new_truncation_details[\"loose_quantity_drop\"]\n", "    ),\n", "    new_truncation_details[\"storage_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"new_truck_load_drop\"] = np.where(\n", "    (new_truncation_details[\"truck_load_drop\"] > 0)\n", "    & (\n", "        new_truncation_details[\"truck_load_drop\"]\n", "        > new_truncation_details[\"inward_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"truck_load_drop\"]\n", "        > new_truncation_details[\"storage_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"truck_load_drop\"]\n", "        > new_truncation_details[\"picking_capacity_sku_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"truck_load_drop\"]\n", "        > new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"truck_load_drop\"]\n", "        > new_truncation_details[\"loose_quantity_drop\"]\n", "    ),\n", "    new_truncation_details[\"truck_load_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"new_picking_capacity_sku_drop\"] = np.where(\n", "    (new_truncation_details[\"picking_capacity_sku_drop\"] > 0)\n", "    & (\n", "        new_truncation_details[\"picking_capacity_sku_drop\"]\n", "        > new_truncation_details[\"inward_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_sku_drop\"]\n", "        > new_truncation_details[\"storage_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_sku_drop\"]\n", "        > new_truncation_details[\"truck_load_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_sku_drop\"]\n", "        > new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_sku_drop\"]\n", "        > new_truncation_details[\"loose_quantity_drop\"]\n", "    ),\n", "    new_truncation_details[\"picking_capacity_sku_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"new_picking_capacity_quantity_drop\"] = np.where(\n", "    (new_truncation_details[\"picking_capacity_quantity_drop\"] > 0)\n", "    & (\n", "        new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "        > new_truncation_details[\"inward_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "        > new_truncation_details[\"storage_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "        > new_truncation_details[\"truck_load_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "        > new_truncation_details[\"picking_capacity_sku_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "        > new_truncation_details[\"loose_quantity_drop\"]\n", "    ),\n", "    new_truncation_details[\"picking_capacity_quantity_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"new_loose_quantity_drop\"] = np.where(\n", "    (new_truncation_details[\"loose_quantity_drop\"] > 0)\n", "    & (\n", "        new_truncation_details[\"loose_quantity_drop\"]\n", "        > new_truncation_details[\"inward_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"loose_quantity_drop\"]\n", "        > new_truncation_details[\"storage_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"loose_quantity_drop\"]\n", "        > new_truncation_details[\"truck_load_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"loose_quantity_drop\"]\n", "        > new_truncation_details[\"picking_capacity_sku_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"loose_quantity_drop\"]\n", "        > new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "    ),\n", "    new_truncation_details[\"loose_quantity_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"total_drop_quantity\"] = (\n", "    new_truncation_details[\"new_inward_drop\"]\n", "    + new_truncation_details[\"new_storage_drop\"]\n", "    + new_truncation_details[\"new_truck_load_drop\"]\n", "    + new_truncation_details[\"new_picking_capacity_sku_drop\"]\n", "    + new_truncation_details[\"new_picking_capacity_quantity_drop\"]\n", "    + new_truncation_details[\"new_loose_quantity_drop\"]\n", ")\n", "\n", "new_truncation_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "72cadffe-2367-4029-b212-bea1e5ab01ac", "metadata": {}, "outputs": [], "source": ["new_truncation_details = new_truncation_details[\n", "    [\n", "        \"date_\",\n", "        \"run_id\",\n", "        \"be_facility_id\",\n", "        \"be_hot_outlet_id\",\n", "        \"facility_id\",\n", "        \"hot_outlet_id\",\n", "        \"item_id\",\n", "        \"v1_indent\",\n", "        \"v2_indent\",\n", "        \"new_inward_drop\",\n", "        \"new_storage_drop\",\n", "        \"new_truck_load_drop\",\n", "        \"new_picking_capacity_sku_drop\",\n", "        \"new_picking_capacity_quantity_drop\",\n", "        \"new_loose_quantity_drop\",\n", "        \"total_drop_quantity\",\n", "    ]\n", "]\n", "\n", "\n", "new_truncation_details.head()"]}, {"cell_type": "markdown", "id": "c690a03e-b3bb-4acd-a9db-4e77a8c38ede", "metadata": {}, "source": ["# frontend truncation details"]}, {"cell_type": "code", "execution_count": null, "id": "e501242e-c444-41c1-98de-522e8ee5fd98", "metadata": {}, "outputs": [], "source": ["frontend_current_date_truncation = new_truncation_details.copy()\n", "\n", "frontend_current_date_truncation = frontend_current_date_truncation[\n", "    frontend_current_date_truncation[\"date_\"] == datetime.today().strftime(\"%Y-%m-%d\")\n", "]\n", "\n", "frontend_current_date_truncation = (\n", "    frontend_current_date_truncation.groupby(\n", "        [\n", "            \"be_facility_id\",\n", "            \"be_hot_outlet_id\",\n", "            \"facility_id\",\n", "            \"hot_outlet_id\",\n", "            \"item_id\",\n", "        ]\n", "    )[\n", "        [\n", "            \"v1_indent\",\n", "            \"v2_indent\",\n", "            \"new_inward_drop\",\n", "            \"new_storage_drop\",\n", "            \"new_truck_load_drop\",\n", "            \"new_picking_capacity_sku_drop\",\n", "            \"new_picking_capacity_quantity_drop\",\n", "            \"new_loose_quantity_drop\",\n", "            \"total_drop_quantity\",\n", "        ]\n", "    ]\n", "    .agg(\"sum\")\n", "    .reset_index()\n", ").rename(\n", "    columns={\n", "        \"v1_indent\": \"t_v1_indent\",\n", "        \"v2_indent\": \"t_v2_indent\",\n", "        \"new_inward_drop\": \"t_new_inward_drop\",\n", "        \"new_storage_drop\": \"t_new_storage_drop\",\n", "        \"new_truck_load_drop\": \"t_new_truck_load_drop\",\n", "        \"new_picking_capacity_sku_drop\": \"t_new_picking_capacity_sku_drop\",\n", "        \"new_picking_capacity_quantity_drop\": \"t_new_picking_capacity_quantity_drop\",\n", "        \"new_loose_quantity_drop\": \"t_new_loose_quantity_drop\",\n", "        \"total_drop_quantity\": \"t_total_drop_quantity\",\n", "    }\n", ")\n", "\n", "frontend_l_1_date_truncation = new_truncation_details.copy()\n", "\n", "frontend_l_1_date_truncation = frontend_l_1_date_truncation[\n", "    frontend_l_1_date_truncation[\"date_\"]\n", "    == (datetime.today() - pd.DateOffset(days=1)).strftime(\"%Y-%m-%d\")\n", "]\n", "\n", "frontend_l_1_date_truncation = (\n", "    frontend_l_1_date_truncation.groupby(\n", "        [\n", "            \"be_facility_id\",\n", "            \"be_hot_outlet_id\",\n", "            \"facility_id\",\n", "            \"hot_outlet_id\",\n", "            \"item_id\",\n", "        ]\n", "    )[\n", "        [\n", "            \"v1_indent\",\n", "            \"v2_indent\",\n", "            \"new_inward_drop\",\n", "            \"new_storage_drop\",\n", "            \"new_truck_load_drop\",\n", "            \"new_picking_capacity_sku_drop\",\n", "            \"new_picking_capacity_quantity_drop\",\n", "            \"new_loose_quantity_drop\",\n", "            \"total_drop_quantity\",\n", "        ]\n", "    ]\n", "    .agg(\"sum\")\n", "    .reset_index()\n", ").rename(\n", "    columns={\n", "        \"v1_indent\": \"t1_v1_indent\",\n", "        \"v2_indent\": \"t1_v2_indent\",\n", "        \"new_inward_drop\": \"t1_new_inward_drop\",\n", "        \"new_storage_drop\": \"t1_new_storage_drop\",\n", "        \"new_truck_load_drop\": \"t1_new_truck_load_drop\",\n", "        \"new_picking_capacity_sku_drop\": \"t1_new_picking_capacity_sku_drop\",\n", "        \"new_picking_capacity_quantity_drop\": \"t1_new_picking_capacity_quantity_drop\",\n", "        \"new_loose_quantity_drop\": \"t1_new_loose_quantity_drop\",\n", "        \"total_drop_quantity\": \"t1_total_drop_quantity\",\n", "    }\n", ")\n", "\n", "frontend_l_7_date_truncation = new_truncation_details.copy()\n", "\n", "frontend_l_7_date_truncation = frontend_l_7_date_truncation[\n", "    (\n", "        frontend_l_7_date_truncation[\"date_\"]\n", "        >= (datetime.today() - pd.DateOffset(days=7)).strftime(\"%Y-%m-%d\")\n", "    )\n", "    & (\n", "        frontend_l_7_date_truncation[\"date_\"]\n", "        <= (datetime.today() - pd.DateOffset(days=1)).strftime(\"%Y-%m-%d\")\n", "    )\n", "]\n", "\n", "frontend_l_7_date_truncation = (\n", "    frontend_l_7_date_truncation.groupby(\n", "        [\n", "            \"be_facility_id\",\n", "            \"be_hot_outlet_id\",\n", "            \"facility_id\",\n", "            \"hot_outlet_id\",\n", "            \"item_id\",\n", "        ]\n", "    )[\n", "        [\n", "            \"v1_indent\",\n", "            \"v2_indent\",\n", "            \"new_inward_drop\",\n", "            \"new_storage_drop\",\n", "            \"new_truck_load_drop\",\n", "            \"new_picking_capacity_sku_drop\",\n", "            \"new_picking_capacity_quantity_drop\",\n", "            \"new_loose_quantity_drop\",\n", "            \"total_drop_quantity\",\n", "        ]\n", "    ]\n", "    .agg(\"sum\")\n", "    .reset_index()\n", ").rename(\n", "    columns={\n", "        \"v1_indent\": \"t7_v1_indent\",\n", "        \"v2_indent\": \"t7_v2_indent\",\n", "        \"new_inward_drop\": \"t7_new_inward_drop\",\n", "        \"new_storage_drop\": \"t7_new_storage_drop\",\n", "        \"new_truck_load_drop\": \"t7_new_truck_load_drop\",\n", "        \"new_picking_capacity_sku_drop\": \"t7_new_picking_capacity_sku_drop\",\n", "        \"new_picking_capacity_quantity_drop\": \"t7_new_picking_capacity_quantity_drop\",\n", "        \"new_loose_quantity_drop\": \"t7_new_loose_quantity_drop\",\n", "        \"total_drop_quantity\": \"t7_total_drop_quantity\",\n", "    }\n", ")\n", "\n", "new_truncation_details.shape, frontend_current_date_truncation.shape, frontend_l_1_date_truncation.shape, frontend_l_7_date_truncation.shape"]}, {"cell_type": "code", "execution_count": null, "id": "fad105c1-ba6c-4b6c-95fb-2a4d1245aca7", "metadata": {}, "outputs": [], "source": ["adding_t_truncation_details = pd.merge(\n", "    adding_be_dump_details,\n", "    frontend_current_date_truncation,\n", "    on=[\n", "        \"item_id\",\n", "        \"be_facility_id\",\n", "        \"be_hot_outlet_id\",\n", "        \"facility_id\",\n", "        \"hot_outlet_id\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "adding_t1_truncation_details = pd.merge(\n", "    adding_t_truncation_details,\n", "    frontend_l_1_date_truncation,\n", "    on=[\n", "        \"item_id\",\n", "        \"be_facility_id\",\n", "        \"be_hot_outlet_id\",\n", "        \"facility_id\",\n", "        \"hot_outlet_id\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "adding_t7_truncation_details = pd.merge(\n", "    adding_t1_truncation_details,\n", "    frontend_l_7_date_truncation,\n", "    on=[\n", "        \"item_id\",\n", "        \"be_facility_id\",\n", "        \"be_hot_outlet_id\",\n", "        \"facility_id\",\n", "        \"hot_outlet_id\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "adding_t7_truncation_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "158bec1e-ba22-4551-8f6f-3bfb81e1ddd6", "metadata": {}, "outputs": [], "source": ["adding_t7_truncation_details[\n", "    [\n", "        \"min_quantity\",\n", "        \"max_quantity\",\n", "        \"actual_inv\",\n", "        \"be_actual_inv\",\n", "        \"ttl_blocked_qty\",\n", "        \"be_ttl_blocked_qty\",\n", "        \"net_inventory\",\n", "        \"be_net_inventory\",\n", "        \"pending_putaway\",\n", "        \"be_pending_putaway\",\n", "        \"in_transit_open_sto_quantity\",\n", "        \"open_sto_quantity\",\n", "        \"be_in_transit_open_sto_quantity\",\n", "        \"be_open_sto_quantity\",\n", "        \"fe_total_po_quantity\",\n", "        \"total_po_quantity\",\n", "        \"t_scheduled_quantity\",\n", "        \"t1_scheduled_quantity\",\n", "        \"t2_scheduled_quantity\",\n", "        \"t3_scheduled_quantity\",\n", "        \"t4_scheduled_quantity\",\n", "        \"today_sales_qty\",\n", "        \"today_carts\",\n", "        \"yesterday_sales_qty\",\n", "        \"yesterday_carts\",\n", "        \"l7days_sales_qty\",\n", "        \"l7days_carts\",\n", "        \"l30days_sales_qty\",\n", "        \"l30days_carts\",\n", "        \"today_dump_qty\",\n", "        \"yesterday_dump_qty\",\n", "        \"l7days_dump_qty\",\n", "        \"l30days_dump_qty\",\n", "        \"be_today_dump_qty\",\n", "        \"be_yesterday_dump_qty\",\n", "        \"be_l7days_dump_qty\",\n", "        \"be_l30days_dump_qty\",\n", "        \"t_v1_indent\",\n", "        \"t_v2_indent\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t_total_drop_quantity\",\n", "        \"t1_v1_indent\",\n", "        \"t1_v2_indent\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t1_total_drop_quantity\",\n", "        \"t7_v1_indent\",\n", "        \"t7_v2_indent\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "        \"t7_total_drop_quantity\",\n", "    ]\n", "] = (\n", "    adding_t7_truncation_details[\n", "        [\n", "            \"min_quantity\",\n", "            \"max_quantity\",\n", "            \"actual_inv\",\n", "            \"be_actual_inv\",\n", "            \"ttl_blocked_qty\",\n", "            \"be_ttl_blocked_qty\",\n", "            \"net_inventory\",\n", "            \"be_net_inventory\",\n", "            \"pending_putaway\",\n", "            \"be_pending_putaway\",\n", "            \"in_transit_open_sto_quantity\",\n", "            \"open_sto_quantity\",\n", "            \"be_in_transit_open_sto_quantity\",\n", "            \"be_open_sto_quantity\",\n", "            \"fe_total_po_quantity\",\n", "            \"total_po_quantity\",\n", "            \"t_scheduled_quantity\",\n", "            \"t1_scheduled_quantity\",\n", "            \"t2_scheduled_quantity\",\n", "            \"t3_scheduled_quantity\",\n", "            \"t4_scheduled_quantity\",\n", "            \"today_sales_qty\",\n", "            \"today_carts\",\n", "            \"yesterday_sales_qty\",\n", "            \"yesterday_carts\",\n", "            \"l7days_sales_qty\",\n", "            \"l7days_carts\",\n", "            \"l30days_sales_qty\",\n", "            \"l30days_carts\",\n", "            \"today_dump_qty\",\n", "            \"yesterday_dump_qty\",\n", "            \"l7days_dump_qty\",\n", "            \"l30days_dump_qty\",\n", "            \"be_today_dump_qty\",\n", "            \"be_yesterday_dump_qty\",\n", "            \"be_l7days_dump_qty\",\n", "            \"be_l30days_dump_qty\",\n", "            \"t_v1_indent\",\n", "            \"t_v2_indent\",\n", "            \"t_new_inward_drop\",\n", "            \"t_new_storage_drop\",\n", "            \"t_new_truck_load_drop\",\n", "            \"t_new_picking_capacity_sku_drop\",\n", "            \"t_new_picking_capacity_quantity_drop\",\n", "            \"t_new_loose_quantity_drop\",\n", "            \"t_total_drop_quantity\",\n", "            \"t1_v1_indent\",\n", "            \"t1_v2_indent\",\n", "            \"t1_new_inward_drop\",\n", "            \"t1_new_storage_drop\",\n", "            \"t1_new_truck_load_drop\",\n", "            \"t1_new_picking_capacity_sku_drop\",\n", "            \"t1_new_picking_capacity_quantity_drop\",\n", "            \"t1_new_loose_quantity_drop\",\n", "            \"t1_total_drop_quantity\",\n", "            \"t7_v1_indent\",\n", "            \"t7_v2_indent\",\n", "            \"t7_new_inward_drop\",\n", "            \"t7_new_storage_drop\",\n", "            \"t7_new_truck_load_drop\",\n", "            \"t7_new_picking_capacity_sku_drop\",\n", "            \"t7_new_picking_capacity_quantity_drop\",\n", "            \"t7_new_loose_quantity_drop\",\n", "            \"t7_total_drop_quantity\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_t7_truncation_details[\n", "    [\n", "        \"today_sales_value\",\n", "        \"yesterday_sales_value\",\n", "        \"l7days_sales_value\",\n", "        \"l30days_sales_value\",\n", "        \"today_dump_value\",\n", "        \"yesterday_dump_value\",\n", "        \"l7days_dump_value\",\n", "        \"l30days_dump_value\",\n", "        \"be_today_dump_value\",\n", "        \"be_yesterday_dump_value\",\n", "        \"be_l7days_dump_value\",\n", "        \"be_l30days_dump_value\",\n", "    ]\n", "] = (\n", "    adding_t7_truncation_details[\n", "        [\n", "            \"today_sales_value\",\n", "            \"yesterday_sales_value\",\n", "            \"l7days_sales_value\",\n", "            \"l30days_sales_value\",\n", "            \"today_dump_value\",\n", "            \"yesterday_dump_value\",\n", "            \"l7days_dump_value\",\n", "            \"l30days_dump_value\",\n", "            \"be_today_dump_value\",\n", "            \"be_yesterday_dump_value\",\n", "            \"be_l7days_dump_value\",\n", "            \"be_l30days_dump_value\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(float)\n", ")\n", "\n", "\n", "adding_t7_truncation_details[\"updated_at_ist\"] = pd.to_datetime(\n", "    datetime.today() + <PERSON><PERSON><PERSON>(hours=5.5)\n", ")\n", "\n", "adding_t7_truncation_details.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "e6193f97-efa2-464e-a51e-35a7a88f3c73", "metadata": {}, "outputs": [], "source": ["adding_t7_truncation_details = adding_t7_truncation_details[\n", "    [\n", "        \"updated_at_ist\",\n", "        \"city_name\",\n", "        \"facility_id\",\n", "        \"hot_outlet_id\",\n", "        \"facility_name\",\n", "        \"be_hot_outlet_id\",\n", "        \"be_inv_outlet_id\",\n", "        \"be_facility_id\",\n", "        \"be_facility_name\",\n", "        \"assortment\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"p_type\",\n", "        \"l0_category\",\n", "        \"l1_category\",\n", "        \"l2_category\",\n", "        \"manufacturer_id\",\n", "        \"manufacturer_name\",\n", "        \"min_quantity\",\n", "        \"max_quantity\",\n", "        \"actual_inv\",\n", "        \"ttl_blocked_qty\",\n", "        \"net_inventory\",\n", "        \"pending_putaway\",\n", "        \"in_transit_open_sto_quantity\",\n", "        \"open_sto_quantity\",\n", "        \"fe_total_po_quantity\",\n", "        \"be_actual_inv\",\n", "        \"be_ttl_blocked_qty\",\n", "        \"be_net_inventory\",\n", "        \"be_pending_putaway\",\n", "        \"be_in_transit_open_sto_quantity\",\n", "        \"be_open_sto_quantity\",\n", "        \"total_po_quantity\",\n", "        \"t_scheduled_quantity\",\n", "        \"t1_scheduled_quantity\",\n", "        \"t2_scheduled_quantity\",\n", "        \"t3_scheduled_quantity\",\n", "        \"t4_scheduled_quantity\",\n", "        \"today_sales_qty\",\n", "        \"today_sales_value\",\n", "        \"today_carts\",\n", "        \"yesterday_sales_qty\",\n", "        \"yesterday_sales_value\",\n", "        \"yesterday_carts\",\n", "        \"l7days_sales_qty\",\n", "        \"l7days_sales_value\",\n", "        \"l7days_carts\",\n", "        \"l30days_sales_qty\",\n", "        \"l30days_sales_value\",\n", "        \"l30days_carts\",\n", "        \"today_dump_qty\",\n", "        \"today_dump_value\",\n", "        \"yesterday_dump_qty\",\n", "        \"yesterday_dump_value\",\n", "        \"l7days_dump_qty\",\n", "        \"l7days_dump_value\",\n", "        \"l30days_dump_qty\",\n", "        \"l30days_dump_value\",\n", "        \"be_today_dump_qty\",\n", "        \"be_today_dump_value\",\n", "        \"be_yesterday_dump_qty\",\n", "        \"be_yesterday_dump_value\",\n", "        \"be_l7days_dump_qty\",\n", "        \"be_l7days_dump_value\",\n", "        \"be_l30days_dump_qty\",\n", "        \"be_l30days_dump_value\",\n", "        \"t_v1_indent\",\n", "        \"t_v2_indent\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t_total_drop_quantity\",\n", "        \"t1_v1_indent\",\n", "        \"t1_v2_indent\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t1_total_drop_quantity\",\n", "        \"t7_v1_indent\",\n", "        \"t7_v2_indent\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "        \"t7_total_drop_quantity\",\n", "    ]\n", "]\n", "\n", "adding_t7_truncation_details.head()"]}, {"cell_type": "markdown", "id": "1e3cff25-0c2e-4677-909b-1a3892c9016a", "metadata": {}, "source": ["# item city weights details"]}, {"cell_type": "code", "execution_count": null, "id": "8e9ff36e-fafa-4c47-8234-d3b7bc299058", "metadata": {}, "outputs": [], "source": ["def item_city_weight():\n", "    item_city_weight = \"\"\"\n", "    \n", "    with\n", "    item_city_weights as\n", "        (select city as city_name, item_id, weights as item_city_weight\n", "            from metrics.city_item_cart_penetration icp\n", "                where icp.updated_at = (select max(updated_at) as updated_at from metrics.city_item_cart_penetration)\n", "        )\n", "            select * from item_city_weights\n", "    \n", "    \"\"\"\n", "    return read_sql_query(item_city_weight, redshift)\n", "\n", "\n", "item_city_weight = item_city_weight()"]}, {"cell_type": "code", "execution_count": null, "id": "3794ab1c-bc0a-4ed1-bf83-01024c79ba0c", "metadata": {}, "outputs": [], "source": ["item_city_weight.head()"]}, {"cell_type": "code", "execution_count": null, "id": "78fab74a-515d-443e-89ff-96019260d752", "metadata": {}, "outputs": [], "source": ["adding_item_city_weight = pd.merge(\n", "    adding_t7_truncation_details,\n", "    item_city_weight,\n", "    on=[\"item_id\", \"city_name\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_item_city_weight[\"item_city_weight\"] = (\n", "    adding_item_city_weight[\"item_city_weight\"].fillna(0).astype(float)\n", ")\n", "\n", "adding_item_city_weight.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8366bed5-acd8-4517-98c6-48c39a40336e", "metadata": {}, "outputs": [], "source": ["del [\n", "    ars_run_id,\n", "    min_live_date,\n", "    truncation_details,\n", "    adding_run_id,\n", "    new_truncation_details,\n", "    frontend_current_date_truncation,\n", "    frontend_l_1_date_truncation,\n", "    frontend_l_7_date_truncation,\n", "    adding_t_truncation_details,\n", "    adding_t1_truncation_details,\n", "    adding_t7_truncation_details,\n", "    item_city_weight,\n", "]\n", "\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "75699a6f-2169-46e9-a424-7ba8ac8e1ad4", "metadata": {}, "outputs": [], "source": ["be_facility_id_list = tuple(list(adding_item_city_weight[\"be_facility_id\"].unique()))\n", "len(be_facility_id_list)"]}, {"cell_type": "markdown", "id": "4fbdca44-e60e-443e-8d8c-1950b5a25c87", "metadata": {}, "source": ["# vendor details query"]}, {"cell_type": "code", "execution_count": null, "id": "5ff2ba51-f7a2-413c-b863-10f2edca8b4c", "metadata": {}, "outputs": [], "source": ["def vendor_details():\n", "    vendor_details = f\"\"\"\n", "    \n", "    with\n", "    vendor_details as\n", "        (select * from vms.vms_vendor_facility_alignment),\n", "\n", "    vendor_name as\n", "        (select * from vms.vms_vendor),\n", "\n", "    pi_logic as\n", "        (select * from vms.vms_vendor_new_pi_logic),\n", "\n", "    final_view as\n", "        (select \n", "            vd.facility_id as be_facility_id,\n", "            vd.item_id,\n", "            vd.vendor_id,\n", "            vn.vendor_name,\n", "            pl.load_size,\n", "            pl.load_type,\n", "            vd.group_id,\n", "            pl.po_days,\n", "            pl.po_cycle,\n", "            vd.case_sensitivity_type,\n", "            pl.tat_day\n", "\n", "                from vendor_details vd\n", "\n", "                    left join\n", "                        vendor_name vn on vn.id = vd.vendor_id and vn.lake_active_record = true and vn.active = 1\n", "\n", "                    left join\n", "                        pi_logic pl on pl.vendor_id = vd.vendor_id and pl.facility_id = vd.facility_id and pl.group_id = vd.group_id and pl.lake_active_record = true and pl.active = true\n", "\n", "                     where vd.lake_active_record = true and vd.active = 1\n", "        )\n", "\n", "            select * from final_view\n", "                where be_facility_id in {be_facility_id_list}\n", "    \n", "    \"\"\"\n", "    return read_sql_query(vendor_details, trino)\n", "\n", "\n", "vendor_details = vendor_details()"]}, {"cell_type": "code", "execution_count": null, "id": "658e9a59-a371-4598-a103-ef3aae11fd40", "metadata": {}, "outputs": [], "source": ["vendor_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bd0bcfe5-5880-4d6f-93a2-6f7b91c214b4", "metadata": {}, "outputs": [], "source": ["adding_vendor_details = pd.merge(\n", "    adding_item_city_weight,\n", "    vendor_details,\n", "    on=[\"item_id\", \"be_facility_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_vendor_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e1b2182c-cc57-44cc-9fa2-3808e9bf42d1", "metadata": {}, "outputs": [], "source": ["del [\n", "    adding_item_city_weight,\n", "    vendor_details,\n", "]\n", "\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "d62dd38e-a86b-4fe1-a174-29765455130c", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"updated_at_ist\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"table update timestamp in ist\",\n", "    },\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"frontend city name\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"frontend facility id\"},\n", "    {\n", "        \"name\": \"hot_outlet_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend hot outlet id\",\n", "    },\n", "    {\n", "        \"name\": \"facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"unique identifier for facility name\",\n", "    },\n", "    {\n", "        \"name\": \"be_hot_outlet_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend hot outlet id\",\n", "    },\n", "    {\n", "        \"name\": \"be_inv_outlet_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend inv outlet id\",\n", "    },\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"be facility id\"},\n", "    {\n", "        \"name\": \"be_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"backend facility name\",\n", "    },\n", "    {\"name\": \"assortment\", \"type\": \"varchar\", \"description\": \"type of assortment\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item id details\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"product name\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"product type name\"},\n", "    {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \"l0 category\"},\n", "    {\"name\": \"l1_category\", \"type\": \"varchar\", \"description\": \"l1 category\"},\n", "    {\"name\": \"l2_category\", \"type\": \"varchar\", \"description\": \"l2 categroy\"},\n", "    {\n", "        \"name\": \"manufacturer_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"procduct manufacturer id\",\n", "    },\n", "    {\n", "        \"name\": \"manufacturer_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"product manufacturer name\",\n", "    },\n", "    {\"name\": \"min_quantity\", \"type\": \"integer\", \"description\": \"frontend min quantity\"},\n", "    {\"name\": \"max_quantity\", \"type\": \"integer\", \"description\": \"frontend max quantity\"},\n", "    {\"name\": \"actual_inv\", \"type\": \"integer\", \"description\": \"frontend actual inv\"},\n", "    {\n", "        \"name\": \"ttl_blocked_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend blocked inv\",\n", "    },\n", "    {\n", "        \"name\": \"net_inventory\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend net inventory\",\n", "    },\n", "    {\n", "        \"name\": \"pending_putaway\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend pending putaway inv\",\n", "    },\n", "    {\n", "        \"name\": \"in_transit_open_sto_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend in transit sto inv\",\n", "    },\n", "    {\n", "        \"name\": \"open_sto_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend created sto inv\",\n", "    },\n", "    {\n", "        \"name\": \"fe_total_po_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend total open po quantity\",\n", "    },\n", "    {\"name\": \"be_actual_inv\", \"type\": \"integer\", \"description\": \"backend actual inv\"},\n", "    {\n", "        \"name\": \"be_ttl_blocked_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend blocked inv\",\n", "    },\n", "    {\n", "        \"name\": \"be_net_inventory\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend net inventory\",\n", "    },\n", "    {\n", "        \"name\": \"be_pending_putaway\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend pending putaway inv\",\n", "    },\n", "    {\n", "        \"name\": \"be_in_transit_open_sto_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend in transit sto inv\",\n", "    },\n", "    {\n", "        \"name\": \"be_open_sto_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend created sto inv\",\n", "    },\n", "    {\n", "        \"name\": \"total_po_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend total open po quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t_scheduled_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend today schedule inv\",\n", "    },\n", "    {\n", "        \"name\": \"t1_scheduled_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend today + 1 schedule inv\",\n", "    },\n", "    {\n", "        \"name\": \"t2_scheduled_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend today + 2 schedule inv\",\n", "    },\n", "    {\n", "        \"name\": \"t3_scheduled_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend today + 3 schedule inv\",\n", "    },\n", "    {\n", "        \"name\": \"t4_scheduled_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend today + 4 schedule inv\",\n", "    },\n", "    {\n", "        \"name\": \"today_sales_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today sales qty\",\n", "    },\n", "    {\n", "        \"name\": \"today_sales_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"frontend today sales value\",\n", "    },\n", "    {\n", "        \"name\": \"today_carts\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today carts for item\",\n", "    },\n", "    {\n", "        \"name\": \"yesterday_sales_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today - 1 sales qty\",\n", "    },\n", "    {\n", "        \"name\": \"yesterday_sales_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"frontend today - 1 sales value\",\n", "    },\n", "    {\n", "        \"name\": \"yesterday_carts\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today - 1 carts for item\",\n", "    },\n", "    {\n", "        \"name\": \"l7days_sales_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today - 7 sales qty\",\n", "    },\n", "    {\n", "        \"name\": \"l7days_sales_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"frontend today - 7 sales value\",\n", "    },\n", "    {\n", "        \"name\": \"l7days_carts\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today - 7 carts for item\",\n", "    },\n", "    {\n", "        \"name\": \"l30days_sales_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today - 30 sales qty\",\n", "    },\n", "    {\n", "        \"name\": \"l30days_sales_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"frontend today - 30 sales value\",\n", "    },\n", "    {\n", "        \"name\": \"l30days_carts\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today - 30 carts for item\",\n", "    },\n", "    {\n", "        \"name\": \"today_dump_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today dump qty\",\n", "    },\n", "    {\n", "        \"name\": \"today_dump_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"frontend today dump value\",\n", "    },\n", "    {\n", "        \"name\": \"yesterday_dump_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today - 1 dump qty\",\n", "    },\n", "    {\n", "        \"name\": \"yesterday_dump_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"frontend today - 1 dump value\",\n", "    },\n", "    {\n", "        \"name\": \"l7days_dump_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today - 7 dump qty\",\n", "    },\n", "    {\n", "        \"name\": \"l7days_dump_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"frontend today - 7 dump value\",\n", "    },\n", "    {\n", "        \"name\": \"l30days_dump_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today - 30 dump qty\",\n", "    },\n", "    {\n", "        \"name\": \"l30days_dump_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"frontend today - 30 dump value\",\n", "    },\n", "    {\n", "        \"name\": \"be_today_dump_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend today dump qty\",\n", "    },\n", "    {\n", "        \"name\": \"be_today_dump_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend today dump value\",\n", "    },\n", "    {\n", "        \"name\": \"be_yesterday_dump_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend today - 1 dump qty\",\n", "    },\n", "    {\n", "        \"name\": \"be_yesterday_dump_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend today - 1 dump value\",\n", "    },\n", "    {\n", "        \"name\": \"be_l7days_dump_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend today - 7 dump qty\",\n", "    },\n", "    {\n", "        \"name\": \"be_l7days_dump_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend today - 7 dump value\",\n", "    },\n", "    {\n", "        \"name\": \"be_l30days_dump_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend today - 30 dump qty\",\n", "    },\n", "    {\n", "        \"name\": \"be_l30days_dump_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend today - 30 dump value\",\n", "    },\n", "    {\n", "        \"name\": \"t_v1_indent\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today v1 indent qty\",\n", "    },\n", "    {\n", "        \"name\": \"t_v2_indent\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today v2 indent qty\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_inward_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today inward drop qty\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_storage_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today storage drop qty\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_truck_load_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today truck drop qty\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_picking_capacity_sku_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today picking sku drop qty\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_picking_capacity_quantity_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today picking quantity drop qty\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_loose_quantity_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today loose drop qty\",\n", "    },\n", "    {\n", "        \"name\": \"t_total_drop_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend total drop qty\",\n", "    },\n", "    {\n", "        \"name\": \"t1_v1_indent\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today v1 -1 indent qty\",\n", "    },\n", "    {\n", "        \"name\": \"t1_v2_indent\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today v2 - 1 indent qty\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_inward_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today - 1 inward drop qty\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_storage_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today - 1 storage drop qty\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_truck_load_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today - 1 truck drop qty\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_picking_capacity_sku_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today - 1 picking sku drop qty\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_picking_capacity_quantity_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today - 1 picking quantity drop qty\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_loose_quantity_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today - 1 loose drop qty\",\n", "    },\n", "    {\n", "        \"name\": \"t1_total_drop_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend total  - 1 drop qty\",\n", "    },\n", "    {\n", "        \"name\": \"t7_v1_indent\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today v1 -7 indent qty\",\n", "    },\n", "    {\n", "        \"name\": \"t7_v2_indent\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today v2 - 7 indent qty\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_inward_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today - 7 inward drop qty\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_storage_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today - 7 storage drop qty\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_truck_load_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today - 7 truck drop qty\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_picking_capacity_sku_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today - 7 picking sku drop qty\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_picking_capacity_quantity_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today - 7 picking quantity drop qty\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_loose_quantity_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend today - 7 loose drop qty\",\n", "    },\n", "    {\n", "        \"name\": \"t7_total_drop_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend total  - 7 drop qty\",\n", "    },\n", "    {\"name\": \"item_city_weight\", \"type\": \"float\", \"description\": \"city item weight\"},\n", "    {\"name\": \"vendor_id\", \"type\": \"float\", \"description\": \"backend aligned vendor id\"},\n", "    {\n", "        \"name\": \"vendor_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"backend aligned vendor name\",\n", "    },\n", "    {\"name\": \"load_size\", \"type\": \"float\", \"description\": \"backend aligned load size\"},\n", "    {\"name\": \"load_type\", \"type\": \"float\", \"description\": \"backend aligned load type\"},\n", "    {\n", "        \"name\": \"group_id\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend aligned vendor group id\",\n", "    },\n", "    {\n", "        \"name\": \"po_days\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"backend aligned vendor po day\",\n", "    },\n", "    {\n", "        \"name\": \"po_cycle\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend aligned vendor po cycle\",\n", "    },\n", "    {\n", "        \"name\": \"case_sensitivity_type\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend aligned vendor case sensitivity typecycle\",\n", "    },\n", "    {\"name\": \"tat_day\", \"type\": \"float\", \"description\": \"backend aligned vendor tat\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "133eef44-c508-43b8-ac9d-93b0cd368b2f", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"thirty_min_beauty_availability\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"facility_id\", \"be_facility_id\", \"item_id\"],\n", "    \"sortkey\": [\"facility_id\", \"be_facility_id\", \"item_id\"],\n", "    \"incremental_key\": \"updated_at_ist\",\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"30 min and beauty assortment tracking\",\n", "}\n", "pb.to_redshift(adding_vendor_details, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "980eb9a0-6325-4b92-8287-b165164edd5e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}