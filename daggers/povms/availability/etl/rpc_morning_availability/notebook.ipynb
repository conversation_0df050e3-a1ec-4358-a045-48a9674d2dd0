{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "import pandas as pd\n", "import pencilbox as pb\n", "import sqlalchemy as sqla\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "import numpy as np\n", "import json\n", "\n", "con = pb.get_connection(\"redshift\")\n", "capacity_system = pb.get_connection(\"supply_orchestrator\")\n", "\n", "import os"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "\n", "WITH \n", "\n", "item_availability AS\n", "      (SELECT a.*\n", "       FROM rpc_daily_availability AS a\n", "       INNER JOIN\n", "         (SELECT max(order_date) AS maxi\n", "          FROM rpc_daily_availability\n", "          WHERE date(order_date) = current_date\n", "          -- AND extract(hour from order_date) < 9\n", "          ) AS b ON a.order_date = b.maxi\n", "       WHERE date(a.order_date) = current_date),\n", "\n", "\n", "item_vendor_facility AS\n", "      (SELECT a.*,\n", "              b.vendor_id\n", "       FROM item_availability AS a\n", "       LEFT JOIN lake_vms.vms_vendor_facility_alignment AS b ON a.item_id=b.item_id\n", "       AND a.facility_id=b.facility_id\n", "       AND b.active=1),\n", "   \n", "   \n", "item_brand_details AS\n", "  (SELECT ivl.*,\n", "          item_name,\n", "          brand_id,\n", "          brand_name,\n", "          manufacture_name,\n", "          manufacturer_id\n", "   FROM item_vendor_facility ivl\n", "   LEFT JOIN\n", "     (SELECT *\n", "      FROM\n", "        (SELECT DISTINCT item_id,\n", "                         rpp.NAME AS item_name,\n", "                         row_number() OVER(PARTITION BY item_id\n", "                                           ORDER BY item_name DESC) AS rank_1,\n", "                         is_pl AS is_gb,\n", "                         brand_id,\n", "                         rpb.NAME AS brand_name,\n", "                         rpm.NAME AS manufacture_name,\n", "                         rpm.id AS manufacturer_id\n", "         FROM lake_rpc.product_product AS rpp\n", "         INNER JOIN lake_rpc.product_brand AS rpb ON rpp.brand_id=rpb.id\n", "         INNER JOIN lake_rpc.product_manufacturer AS rpm ON rpm.id=rpb.manufacturer_id)item_details\n", "      WHERE rank_1=1 )item_details ON ivl.item_id=item_details.item_id),\n", "\n", "\n", "po_details AS\n", "  (SELECT ibd.*,\n", "          pfa.po_cycle\n", "   FROM item_brand_details ibd\n", "   LEFT JOIN lake_vms.vendor_manufacturer_physical_facility_attributes pfa ON ibd.facility_id = pfa.facility_id\n", "   AND ibd.manufacturer_id = pfa.manufacturer_id\n", "   AND ibd.vendor_id = pfa.vendor_id),\n", "\n", "\n", "vendor_name AS\n", "  (SELECT po.*,\n", "          vv.vendor_name\n", "   FROM po_details po\n", "   LEFT JOIN lake_vms.vms_vendor vv ON po.vendor_id = vv.id\n", "   AND vv.active = 1),\n", "\n", "\n", "facility_check AS\n", "  (SELECT min(oms_order.install_ts) AS start_date,\n", "          max(oms_order.install_ts) AS end_date,\n", "          facility_id\n", "   FROM lake_oms_bifrost.oms_order\n", "   INNER JOIN lake_oms_bifrost.oms_suborder ON oms_order.id=oms_suborder.order_id\n", "   INNER JOIN lake_oms_bifrost.oms_merchant ON oms_suborder.backend_merchant_id=oms_merchant.id\n", "   INNER JOIN lake_retail.console_outlet_cms_store ON oms_merchant.external_id=lake_retail.console_outlet_cms_store.cms_store\n", "   INNER JOIN lake_retail.console_outlet ON lake_retail.console_outlet_cms_store.outlet_id=lake_retail.console_outlet.id\n", "   GROUP BY 3),\n", "\n", "\n", "availability_with_facility_flag AS\n", "  (SELECT item_id,\n", "          order_date,\n", "          recent_assortment,\n", "          assortment_date,\n", "          new_substate,\n", "          a.facility_id,\n", "          date_of_activation,\n", "          actual_quantity,\n", "          blocked_quantity,\n", "          availability,\n", "          facility_name,\n", "          active_flag,\n", "          month,\n", "          l0,\n", "          l0_id,\n", "          is_gb,\n", "          uom,\n", "          weight_in_gm,\n", "          buckets,\n", "          bucket_x,\n", "          vendor_id,\n", "          po_cycle,\n", "          vendor_name,\n", "          item_name,\n", "          brand_id,\n", "          brand_name,\n", "          manufacture_name,\n", "          manufacturer_id AS manufacture_id,\n", "          app_live,\n", "          inv_flag,\n", "          unreliable,\n", "          unorderable,\n", "          CASE\n", "              WHEN (a.facility_id=b.facility_id\n", "                    AND a.order_date>=b.start_date\n", "                    AND a.order_date<=b.end_date) THEN 1\n", "              ELSE 0\n", "          END AS facility_flag\n", "   FROM vendor_name AS a\n", "   LEFT JOIN facility_check AS b ON a.facility_id=b.facility_id),\n", "\n", "\n", "item_transfer_flag AS\n", "  (SELECT item_id,\n", "          facility_id,\n", "          CASE\n", "              WHEN tag_value IS NOT NULL THEN 'yes'\n", "              ELSE 'no'\n", "          END AS transfer_flag\n", "   FROM lake_rpc.item_outlet_tag_mapping AS a\n", "   LEFT JOIN lake_retail.console_outlet AS b ON a.outlet_id=b.id\n", "   WHERE tag_type_id=8\n", "     AND a.active=1\n", "   GROUP BY 1,\n", "            2,\n", "            3),\n", "PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.ID AS L2_ID,\n", "          C2.NAME AS L2,\n", "          C1.ID AS L1_ID,\n", "          C1.name AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name as product_type\n", "   FROM lake_cms.GR_PRODUCT P\n", "   INNER JOIN lake_cms.GR_PRODUCT_CATEGORY_MAPPING PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.GR_CATEGORY C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.GR_CATEGORY C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.GR_CATEGORY C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   inner join lake_cms.gr_product_type pt on p.type_id=pt.id\n", "   \n", "   ), \n", "  \n", "item_details as (SELECT item_id,\n", "        product_type,\n", "       max(cat.l1_id) AS l1_id,\n", "       max(cat.l1) AS l1,\n", "       max(cat.l2_id) AS l2_id,\n", "       max(cat.l2) AS l2\n", "       FROM lake_rpc.item_product_mapping rpc\n", "INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL\n", "GROUP BY 1,2),\n", "\n", "\n", "availability_transfer AS\n", "  (SELECT a.*,\n", "          b.transfer_flag,\n", "          c.product_type,\n", "          c.l1_id,\n", "          c.l1,\n", "          c.l2_id,\n", "          c.l2\n", "   FROM availability_with_facility_flag AS a\n", "   LEFT JOIN item_transfer_flag AS b ON a.item_id=b.item_id AND a.facility_id=b.facility_id\n", "   LEFT JOIN item_details AS c ON a.item_id=c.item_id)\n", "\n", "\n", "SELECT distinct *\n", "FROM availability_transfer\n", "\n", "\n", "\"\"\"\n", "availability_value = pd.read_sql(sql=query, con=con)\n", "availability_value.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_value[\"l0\"] = np.where(\n", "    availability_value.l0 == \"\", \"Mapping not present\", availability_value.l0\n", ")\n", "\n", "availability_value[\"l0_id\"] = np.where(\n", "    availability_value.l0_id.isna(),\n", "    1000,\n", "    availability_value.l0_id,\n", ")\n", "\n", "availability_value[\"buckets\"] = np.where(\n", "    availability_value.buckets.isna(),\n", "    \"Mapping not present\",\n", "    availability_value.buckets,\n", ")\n", "availability_value[availability_value.buckets == \"Mapping not present\"].head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_query = \"\"\"\n", "select distinct facility_id from\n", "    (select facility_id, count(distinct order_id) as order_count\n", "    from lake_ims.ims_order_details a\n", "    left join lake_retail.console_outlet o on a.outlet = o.id\n", "    where date(a.created_at) between current_date-interval '2 days' and current_date-interval '1 days'\n", "    and a.status_id <> 5\n", "    and business_type not ilike '%%b2b%%'\n", "    and business_type_id in (1,2,3)\n", "    and facility_id not in (29,139)\n", "    group by 1) b\n", "where order_count > 10\n", "\n", "\"\"\"\n", "\n", "active_facility = pd.read_sql_query(sql=facility_query, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### Active Dark stores"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "select distinct facility_id::int, max(case when business_type_id=7 then 'ES' when business_type_id=8 then 'GM' end) as store_type\n", "from lake_retail.console_outlet\n", "where business_type_id in (7,8)\n", "\n", "group by 1\n", "\"\"\"\n", "ds_mapping = pd.read_sql(sql, con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dark_stores_list = list(filter(None, ds_mapping[\"facility_id\"].astype(int).values))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_active = active_facility.append(ds_mapping[[\"facility_id\"]]).reset_index()\n", "all_active.drop([\"index\"], inplace=True, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_value = availability_value.merge(all_active, on=\"facility_id\", how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_value = availability_value.merge(ds_mapping, on=\"facility_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_value.store_type = np.where(\n", "    availability_value.store_type.isna(), \"NDD\", availability_value.store_type\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "raw", "metadata": {}, "source": ["# Reading slot wise capacity details from capacity system and manipulation\n", "query = \"\"\"select distinct backend_merchant_id,\n", "backend_merchant_name,\n", "delivery_date,\n", "delivery_slot_type,\n", "sum(given_capacity) as planned_capacity,\n", "sum(capacity_utilised) as actual_capacity\n", "from\n", "(SELECT \n", "    warehouse_external_id AS backend_merchant_id,\n", "    warehouse_name AS backend_merchant_name,\n", "    DATE(slot_start AT TIME ZONE 'ASIA/KOLKATA') AS delivery_date,\n", "    TO_CHAR(slot_start AT TIME ZONE 'ASIA/KOLKATA','HH24:MI:SS') || ' - ' || \n", "    TO_CHAR(slot_end AT TIME ZONE 'ASIA/KOLKATA','HH24:MI:SS') AS delivery_slot,\n", "    slot_type,\n", "    CASE WHEN EXTRACT(HOUR FROM (slot_start AT TIME ZONE 'ASIA/KOLKATA'))< 16 THEN 'SLOT A' ELSE  'SLOT B' END AS delivery_slot_type,\n", "    warehouse_asked AS asked_capcity,\n", "    warehouse_planned AS given_capacity,\n", "    warehouse_actual AS capacity_utilised,\n", "    warehouse_available AS capacity_under_utilised,\n", "    min(update_ts) AS update_ts\n", "FROM sco_path_capacity\n", "WHERE date(slot_start) >= date(now())\n", "and warehouse_name not like '%%Cold%%'\n", "and warehouse_name not like '%%FNV%%'\n", "and warehouse_planned < 9999\n", "GROUP BY 1,2,3,4,5,6,7,8,9,10\n", "ORDER BY 2,3,4,5) a\n", "where\n", "delivery_date = date(current_date)\n", "and given_capacity > 0\n", "group by 1,2,3,4\n", "order by 2,3;\n", "\"\"\"\n", "\n", "capacity_details = pd.read_sql_query(query, capacity_system)"]}, {"cell_type": "raw", "metadata": {}, "source": ["query = \"\"\"\n", "select distinct cms.outlet_id,o.name as outlet,o.facility_id,cms.cms_store\n", "from\n", "lake_oms_bifrost.oms_merchant m\n", "join lake_retail.console_outlet_cms_store cms on m.external_id = cms.cms_store\n", "join lake_retail.console_outlet o on cms.outlet_id= o.id\n", "where\n", "cms.active = 1 and cms.cms_update_active = 1;\n", "\"\"\"\n", "cms_outlet_details = pd.read_sql(query, con)"]}, {"cell_type": "raw", "metadata": {}, "source": ["warehouse_capacities = (\n", "    capacity_details.merge(\n", "        cms_outlet_details,\n", "        how=\"inner\",\n", "        left_on=[\"backend_merchant_id\"],\n", "        right_on=[\"cms_store\"],\n", "    )[\n", "        [\n", "            \"outlet_id\",\n", "            \"outlet\",\n", "            \"facility_id\",\n", "            \"delivery_date\",\n", "            \"planned_capacity\",\n", "            \"backend_merchant_name\",\n", "        ]\n", "    ]\n", "    .rename(columns={\"planned_capacity\": \"capacity\"})\n", "    .drop_duplicates()\n", ")\n", "\n", "warehouse_capacities = (\n", "    warehouse_capacities.groupby(\n", "        [\"delivery_date\", \"outlet_id\", \"outlet\", \"facility_id\"]\n", "    )\n", "    .agg({\"capacity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "warehouse_capacities[\"delivery_date\"] = pd.to_datetime(\n", "    warehouse_capacities[\"delivery_date\"]\n", ")\n", "\n", "warehouse_capacities = (\n", "    warehouse_capacities.groupby([\"facility_id\"]).agg({\"capacity\": \"sum\"}).reset_index()\n", ")\n", "\n", "warehouse_capacities.head()"]}, {"cell_type": "raw", "metadata": {}, "source": ["availability_value = availability_value.merge(\n", "    warehouse_capacities, on=\"facility_id\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_value[\"capacity\"] = 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# availability_value.to_csv('data.csv')\n", "availability_value.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_value = availability_value[\n", "    [\n", "        \"item_id\",\n", "        \"order_date\",\n", "        \"recent_assortment\",\n", "        \"assortment_date\",\n", "        \"new_substate\",\n", "        \"facility_id\",\n", "        \"date_of_activation\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"availability\",\n", "        \"facility_name\",\n", "        \"active_flag\",\n", "        \"month\",\n", "        \"l0\",\n", "        \"l0_id\",\n", "        \"is_gb\",\n", "        \"uom\",\n", "        \"weight_in_gm\",\n", "        \"buckets\",\n", "        \"bucket_x\",\n", "        \"vendor_id\",\n", "        \"po_cycle\",\n", "        \"vendor_name\",\n", "        \"item_name\",\n", "        \"brand_id\",\n", "        \"brand_name\",\n", "        \"manufacture_name\",\n", "        \"manufacture_id\",\n", "        \"app_live\",\n", "        \"inv_flag\",\n", "        \"unreliable\",\n", "        \"unorderable\",\n", "        \"facility_flag\",\n", "        \"transfer_flag\",\n", "        \"product_type\",\n", "        \"l1_id\",\n", "        \"l1\",\n", "        \"l2_id\",\n", "        \"l2\",\n", "        \"capacity\",\n", "        \"store_type\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_value[\"l0_id\"] = availability_value.l0_id.fillna(0)\n", "availability_value[\"actual_quantity\"] = availability_value.actual_quantity.fillna(0)\n", "availability_value[\"blocked_quantity\"] = availability_value.blocked_quantity.fillna(0)\n", "availability_value[\"new_substate\"] = availability_value.new_substate.fillna(0)\n", "availability_value[\"brand_id\"] = availability_value.brand_id.fillna(0)\n", "availability_value[\"manufacture_id\"] = availability_value.manufacture_id.fillna(0)\n", "availability_value[\"capacity\"] = availability_value.capacity.fillna(0)\n", "availability_value[\"l0_id\"] = availability_value.l0_id.astype(int)\n", "availability_value[\"item_id\"] = availability_value.item_id.astype(int)\n", "availability_value[\"po_cycle\"] = availability_value.po_cycle.fillna(0).astype(int)\n", "availability_value[\"vendor_id\"] = availability_value.vendor_id.fillna(0).astype(int)\n", "availability_value[\"new_substate\"] = availability_value.new_substate.astype(int)\n", "availability_value[\"facility_id\"] = availability_value.facility_id.astype(int)\n", "availability_value[\"actual_quantity\"] = availability_value.actual_quantity.astype(int)\n", "availability_value[\"blocked_quantity\"] = availability_value.blocked_quantity.astype(int)\n", "availability_value[\"app_live\"] = availability_value.app_live.astype(int)\n", "availability_value[\"inv_flag\"] = availability_value.inv_flag.astype(int)\n", "availability_value[\"item_name\"] = availability_value[\"item_name\"].astype(str)\n", "availability_value[\"brand_id\"] = availability_value.brand_id.astype(int)\n", "availability_value[\"manufacture_id\"] = availability_value.manufacture_id.astype(int)\n", "availability_value[\"capacity\"] = availability_value.capacity.astype(int)\n", "availability_value[\"store_type\"] = availability_value.store_type.astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"Item ID\"},\n", "    {\"name\": \"order_date\", \"type\": \"timestamp\", \"description\": \"Date time of analysis\"},\n", "    {\n", "        \"name\": \"recent_assortment\",\n", "        \"type\": \"timestamp\",\n", "        \"description\": \"Date when the item facility combination is updated\",\n", "    },\n", "    {\n", "        \"name\": \"assortment_date\",\n", "        \"type\": \"timestamp\",\n", "        \"description\": \"Date when the item facility combination was first created\",\n", "    },\n", "    {\n", "        \"name\": \"new_substate\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"substate_id of item at order_date\",\n", "    },\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"Facility ID\"},\n", "    {\n", "        \"name\": \"date_of_activation\",\n", "        \"type\": \"timestamp\",\n", "        \"description\": \"First arrival of item in stock after becoming active\",\n", "    },\n", "    {\n", "        \"name\": \"actual_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Actual physical inventory at order_date\",\n", "    },\n", "    {\n", "        \"name\": \"blocked_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Blocked inventory at order_date\",\n", "    },\n", "    {\n", "        \"name\": \"availability\",\n", "        \"type\": \"varchar(30)\",\n", "        \"description\": \"'live' if unblocked inventory > 0 otherwise 'not live'\",\n", "    },\n", "    {\"name\": \"facility_name\", \"type\": \"varchar(500)\", \"description\": \"Facility Name\"},\n", "    {\n", "        \"name\": \"active_flag\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Active if substate_id = 1 otherwise inactive\",\n", "    },\n", "    {\"name\": \"month\", \"type\": \"integer\", \"description\": \"Month from order_date\"},\n", "    {\"name\": \"l0\", \"type\": \"varchar(500)\", \"description\": \"Level 0 category name\"},\n", "    {\"name\": \"l0_id\", \"type\": \"float\", \"description\": \"Level 0 category id\"},\n", "    {\"name\": \"is_gb\", \"type\": \"varchar\", \"description\": \"is grofer's brand\"},\n", "    {\"name\": \"uom\", \"type\": \"varchar\", \"description\": \"unit of measurement\"},\n", "    {\"name\": \"weight_in_gm\", \"type\": \"float\", \"description\": \"Weight in gms\"},\n", "    {\"name\": \"buckets\", \"type\": \"varchar\", \"description\": \"Bucket A/B\"},\n", "    {\n", "        \"name\": \"bucket_x\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Y if item is Bucket X otherwise N\",\n", "    },\n", "    {\"name\": \"vendor_id\", \"type\": \"int\", \"description\": \"Vendor ID\"},\n", "    {\"name\": \"po_cycle\", \"type\": \"int\", \"description\": \"PO cycle of vendor\"},\n", "    {\"name\": \"vendor_name\", \"type\": \"varchar(1000)\", \"description\": \"Vendor name\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar(500)\", \"description\": \"Item name\"},\n", "    {\"name\": \"brand_id\", \"type\": \"int\", \"description\": \"Brand ID\"},\n", "    {\"name\": \"brand_name\", \"type\": \"varchar(1000)\", \"description\": \"Brand name\"},\n", "    {\n", "        \"name\": \"manufacture_name\",\n", "        \"type\": \"varchar(1000)\",\n", "        \"description\": \"Manufacturer name\",\n", "    },\n", "    {\"name\": \"manufacture_id\", \"type\": \"int\", \"description\": \"Manufacturer ID\"},\n", "    {\n", "        \"name\": \"app_live\",\n", "        \"type\": \"int\",\n", "        \"description\": \"status on the app at order_date\",\n", "    },\n", "    {\"name\": \"inv_flag\", \"type\": \"int\", \"description\": \"1 if unblocked inventory > 0\"},\n", "    {\"name\": \"unreliable\", \"type\": \"varchar\", \"description\": \"unreliable tag\"},\n", "    {\"name\": \"unorderable\", \"type\": \"varchar\", \"description\": \"unorderable tag\"},\n", "    {\"name\": \"facility_flag\", \"type\": \"int\", \"description\": \"is facility active\"},\n", "    {\"name\": \"transfer_flag\", \"type\": \"varchar\", \"description\": \"is TEA\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar(1000)\", \"description\": \"Product type\"},\n", "    {\"name\": \"l1_id\", \"type\": \"float\", \"description\": \"Level 1 category id\"},\n", "    {\"name\": \"l1\", \"type\": \"varchar(1000)\", \"description\": \"Level 1 category name\"},\n", "    {\"name\": \"l2_id\", \"type\": \"float\", \"description\": \"Level 2 category id\"},\n", "    {\"name\": \"l2\", \"type\": \"varchar(1000)\", \"description\": \"Level 2 category name\"},\n", "    {\"name\": \"capacity\", \"type\": \"int\", \"description\": \"Capacity of facility\"},\n", "    {\"name\": \"store_type\", \"type\": \"varchar(10)\", \"description\": \"Store type\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"rpc_morning_availability\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"item_id\", \"facility_id\", \"order_date\"],\n", "    \"sortkey\": [\"order_date\", \"item_id\", \"facility_id\"],\n", "    \"incremental_key\": \"order_date\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"Stores 9am snapshot of rpc_daily_availability table and other item details\",\n", "}\n", "\n", "pb.to_redshift(availability_value, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}