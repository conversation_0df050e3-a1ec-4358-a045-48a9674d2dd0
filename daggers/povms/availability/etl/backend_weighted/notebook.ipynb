{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "from datetime import date, datetime, timedelta\n", "from datetime import datetime, timedelta\n", "import pytz\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")\n", "\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 1\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", round((end - start) / 60, 2), \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_hour = (datetime.today() + timedelta(hours=4.5)).hour\n", "current_hour"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pb.from_sheets(\n", "    sheetid=\"1i5g3cY3g09iIlEdWwatqYQ4jSLK81dhYR7wXp40BvKc\", sheetname=\"dags\"\n", ")\n", "df = (\n", "    df[df[\"dag\"] == \"backend_weighted_availability_etl\"][[\"backfill\", \"start_date\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "backfill = df.iloc[:, 0][0]\n", "\n", "if backfill == \"yes\":\n", "    start_date = df.iloc[:, 1][0]\n", "else:\n", "    start_date = pd.to_datetime(datetime.today() + timedelta(hours=4.5)).strftime(\n", "        \"%Y-%m-%d\"\n", "    )\n", "\n", "start_date"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## Active Dark Stores"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_mapping_query = f\"\"\"\n", "    WITH outbound_base AS (\n", "        SELECT outlet AS outlet_id, COUNT(DISTINCT ancestor) AS carts \n", "        FROM lake_ims.ims_order_details od \n", "        WHERE status_id IN (1,2) AND od.created_at BETWEEN DATE('{start_date}') - 4 AND DATE('{start_date}') - 1 \n", "        GROUP BY 1\n", "    )\n", "    SELECT facility_id, outlet_id, outlet_name \n", "    FROM lake_po.physical_facility_outlet_mapping pfom  \n", "    WHERE ars_active = 1 AND active = 1 AND is_primary = 1 \n", "    AND outlet_id IN (SELECT DISTINCT id FROM lake_retail.console_outlet WHERE business_type_id = 7 AND active = 1) \n", "    AND outlet_id IN (SELECT DISTINCT outlet_id FROM lake_po.bulk_facility_outlet_mapping WHERE active = 1) \n", "    AND outlet_id IN (SELECT DISTINCT outlet_id FROM outbound_base WHERE carts >= 5)\n", "\"\"\"\n", "outlet_mapping_df = read_sql_query(outlet_mapping_query, CON_REDSHIFT)\n", "\n", "outlet_mapping_df[[\"facility_id\", \"outlet_id\"]] = outlet_mapping_df[\n", "    [\"facility_id\", \"outlet_id\"]\n", "].astype(int)\n", "\n", "outlet_mapping_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_id_list = tuple(list(outlet_mapping_df[\"facility_id\"].unique()))\n", "outlet_id_list = tuple(list(outlet_mapping_df[\"outlet_id\"].unique()))\n", "\n", "len(facility_id_list), len(outlet_id_list)"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## Hourly Weighted Availability (Grocery, Perishable, Icecream, FnV)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["avail_query = f\"\"\"\n", "    CREATE TEMPORARY TABLE avail_base AS (\n", "        WITH perishable_assortment AS (\n", "            SELECT DISTINCT item_id\n", "            FROM metrics.perishable_assortment \n", "            WHERE updated_at BETWEEN DATE('{start_date}') - 4 AND DATE('{start_date}') - 1\n", "            AND item_id NOT IN (SELECT DISTINCT item_id FROM lake_rpc.item_category_details WHERE l2_id = 1185)\n", "        ),\n", "        \n", "        item_details AS (\n", "            SELECT DISTINCT rpc.item_id, \n", "            CASE \n", "                WHEN l0_id = 1487 THEN 'FnV' \n", "                WHEN l2_id IN (1185) THEN 'Milk'\n", "                WHEN l1_id IN (183) THEN 'Ice Cream'\n", "                WHEN l2_id IN (1961, 1367, 63, 1732, 1733, 1734, 1369) THEN 'Perishable'\n", "                WHEN perishable = 1 THEN 'Perishable' \n", "                WHEN fd.item_id IS NOT NULL THEN 'Perishable'\n", "                ELSE 'Packaged Goods'\n", "            END AS assortment_type\n", "            FROM lake_rpc.product_product rpc \n", "            LEFT JOIN lake_rpc.item_category_details cd ON cd.item_id = rpc.item_id\n", "            LEFT JOIN (SELECT DISTINCT item_id FROM perishable_assortment) fd ON fd.item_id = rpc.item_id\n", "            LEFT JOIN (\n", "                SELECT DISTINCT item_id \n", "                FROM lake_rpc.item_tag_mapping \n", "                WHERE active = true AND CAST(tag_value as int) = 3 AND tag_type_id = 3\n", "            ) i ON i.item_id = rpc.item_id\n", "            WHERE rpc.id IN (SELECT MAX(id) AS id FROM lake_rpc.product_product pp WHERE pp.active = 1 AND pp.approved = 1 GROUP BY item_id) \n", "            AND i.item_id IS NULL AND rpc.handling_type <> 8\n", "        ),\n", "        \n", "        tmin_assortment AS (\n", "            SELECT DISTINCT pfma.item_id, facility_id, 1 AS tmin_flag\n", "            FROM lake_rpc.product_facility_master_assortment pfma \n", "            WHERE substate_reason_id = 7 AND active = 1 AND master_assortment_substate_id = 1\n", "        ),\n", "    \n", "        rpc_pre AS (\n", "            SELECT order_date AS updated_at, facility_id, item_id, actual_quantity - blocked_quantity AS current_inv \n", "            FROM consumer.rpc_daily_availability \n", "            WHERE order_date BETWEEN ('{start_date}' || ' 00:00:00')::timestamp AND ('{start_date}' || ' 23:59:59')::timestamp AND new_substate = 1\n", "        ),\n", "        \n", "        grocery_pre AS (\n", "            SELECT DISTINCT updated_at, facility_id, item_id, current_inv\n", "            FROM (\n", "                SELECT a.updated_at, a.facility_id, a.item_id, current_inv, CASE WHEN tmin_flag IS NULL THEN 0 ELSE tmin_flag END AS tmin_flag\n", "                FROM rpc_pre a\n", "                LEFT JOIN tmin_assortment t ON t.facility_id = a.facility_id AND a.item_id = t.item_id\n", "                WHERE a.facility_id IN {facility_id_list}\n", "            )\n", "            WHERE tmin_flag = 0\n", "        ),\n", "        \n", "        perishable_pre AS (\n", "            SELECT DISTINCT updated_at, facility_id, item_id, current_inv\n", "            FROM (\n", "                SELECT updated_at, a.item_id, a.facility_id, current_inv, CASE WHEN tmin_flag IS NULL THEN 0 ELSE tmin_flag END AS tmin_flag\n", "                FROM metrics.perishable_hourly_details_v2 a\n", "                LEFT JOIN tmin_assortment t ON t.facility_id = a.facility_id AND a.item_id = t.item_id\n", "                WHERE updated_at BETWEEN ('{start_date}' || ' 00:00:00')::timestamp AND ('{start_date}' || ' 23:59:59')::timestamp \n", "                AND a.facility_id IN {facility_id_list}\n", "            )\n", "            WHERE tmin_flag = 0   \n", "        ),\n", "        \n", "    \n", "        fnv_pre AS (\n", "            SELECT DISTINCT updated_at, facility_id, item_id, current_inv\n", "            FROM (\n", "                SELECT updated_at, a.item_id, a.facility_id, current_inv, CASE WHEN tmin_flag IS NULL THEN 0 ELSE tmin_flag END AS tmin_flag\n", "                FROM metrics.fnv_hourly_details a \n", "                LEFT JOIN tmin_assortment t ON t.facility_id = a.facility_id AND a.item_id = t.item_id \n", "                WHERE updated_at BETWEEN ('{start_date}' || ' 00:00:00')::timestamp AND ('{start_date}' || ' 23:59:59')::timestamp \n", "                AND a.facility_id IN {facility_id_list}\n", "            )\n", "            WHERE tmin_flag = 0\n", "            \n", "        ),\n", "        \n", "        grocery_hourly_base AS (\n", "            SELECT DATE(a.updated_at) AS date_, EXTRACT(hour FROM a.updated_at) AS hour_, a.item_id, a.facility_id, \n", "            CASE WHEN current_inv > 0 THEN 1 ELSE 0 END AS is_available\n", "            FROM grocery_pre a \n", "            WHERE a.item_id IN (SELECT DISTINCT item_id FROM item_details WHERE assortment_type IN ('Packaged Goods'))\n", "        ),\n", "        \n", "        perishable_hourly_base AS (\n", "            SELECT DATE(a.updated_at) AS date_, EXTRACT(hour FROM a.updated_at) AS hour_, a.item_id, a.facility_id, \n", "            CASE WHEN current_inv > 0 THEN 1 ELSE 0 END AS is_available\n", "            FROM perishable_pre a \n", "            WHERE a.item_id IN (SELECT DISTINCT item_id FROM item_details WHERE assortment_type IN ('Perishable'))\n", "        ),\n", "    \n", "        fnv_hourly_base AS (\n", "            SELECT DATE(a.updated_at) AS date_, EXTRACT(hour FROM a.updated_at) AS hour_, a.item_id, a.facility_id, \n", "            CASE WHEN current_inv > 0 THEN 1 ELSE 0 END AS is_available\n", "            FROM fnv_pre a \n", "            WHERE a.item_id IN (SELECT DISTINCT item_id FROM item_details WHERE assortment_type IN ('FnV'))\n", "        ),\n", "        \n", "        icecream_hourly_base AS (\n", "            SELECT DATE(a.updated_at) AS date_, EXTRACT(hour FROM a.updated_at) AS hour_, a.item_id, a.facility_id, \n", "            CASE WHEN current_inv > 0 THEN 1 ELSE 0 END AS is_available\n", "            FROM perishable_pre a \n", "            WHERE a.item_id IN (SELECT DISTINCT item_id FROM item_details WHERE assortment_type IN ('Ice Cream'))\n", "        ),\n", "        \n", "        agg_base AS (\n", "            SELECT DISTINCT date_, hour_, facility_id, item_id, is_available, 'Packaged Goods' AS assortment_type\n", "            FROM grocery_hourly_base\n", "            UNION\n", "            SELECT DISTINCT date_, hour_, facility_id, item_id, is_available, 'Perishable' AS assortment_type\n", "            FROM perishable_hourly_base\n", "            UNION\n", "            SELECT DISTINCT date_, hour_, facility_id, item_id, is_available, 'FnV' AS assortment_type\n", "            FROM fnv_hourly_base\n", "            UNION\n", "            SELECT DISTINCT date_, hour_, facility_id, item_id, is_available, 'Ice Cream' AS assortment_type\n", "            FROM icecream_hourly_base\n", "        )\n", "        \n", "        SELECT * FROM agg_base\n", "    );\n", "    \n", "    WITH base_pre AS (\n", "        SELECT date_, hour_, item_id, assortment_type, co.id AS outlet_id, a.facility_id, is_available\n", "        FROM avail_base a\n", "        JOIN lake_retail.console_outlet co ON co.facility_id = a.facility_id AND co.active = 1 AND co.business_type_id = 7\n", "    ),\n", "    \n", "    fe_be_mapping AS (\n", "        SELECT DISTINCT a.item_id, ro.facility_id AS facility_id, r.facility_id AS be_facility_id\n", "        FROM lake_rpc.item_outlet_tag_mapping a\n", "        JOIN lake_retail.console_outlet ro ON ro.id = a.outlet_id\n", "        JOIN lake_retail.console_outlet r ON r.id = a.tag_value\n", "        JOIN (\n", "            SELECT om.facility_id AS be_facility_id, rco.facility_id AS fe_facility_id \n", "            FROM lake_po.bulk_facility_outlet_mapping om \n", "            JOIN lake_retail.console_outlet rco ON rco.id = om.outlet_id \n", "            WHERE om.active = true\n", "        ) om ON om.fe_facility_id = ro.facility_id AND om.be_facility_id = r.facility_id\n", "        WHERE tag_type_id = 8 AND a.active = 1\n", "    ),\n", "    \n", "    base AS (\n", "        SELECT date_, hour_, a.item_id, assortment_type, b.be_facility_id, a.outlet_id, a.facility_id, is_available\n", "        FROM base_pre a\n", "        LEFT JOIN fe_be_mapping b ON a.item_id = b.item_id AND a.facility_id = b.facility_id\n", "        JOIN lake_po.physical_facility_outlet_mapping pfom ON b.be_facility_id = pfom.facility_id AND pfom.active = 1 AND pfom.ars_active = 1\n", "    ),\n", "    \n", "    backend_item_weights AS (\n", "        SELECT DISTINCT backend_facility_id AS be_facility_id, item_id, CAST(weights AS float) AS iw \n", "        FROM metrics.backend_item_cart_penetration \n", "        WHERE updated_at = (SELECT MAX(updated_at) FROM metrics.backend_item_cart_penetration WHERE DATE(updated_at) <= DATE('{start_date}'))\n", "    ),\n", "    \n", "    score_merge_base AS (\n", "        SELECT a.*, iw\n", "        FROM base a\n", "        LEFT JOIN backend_item_weights ciw ON ciw.be_facility_id = a.be_facility_id AND ciw.item_id = a.item_id\n", "    ),\n", "    \n", "    agg_item_weights AS (\n", "        SELECT date_, be_facility_id, hour_, assortment_type, facility_id, SUM(iw) AS tot_iw\n", "        FROM (\n", "            SELECT DISTINCT be_facility_id, date_, hour_, assortment_type, facility_id, item_id, iw\n", "            FROM score_merge_base\n", "        )\n", "        GROUP BY 1,2,3,4,5\n", "    ),\n", "    \n", "    final_base AS (\n", "        SELECT a.date_, a.be_facility_id, a.item_id, a.assortment_type, a.facility_id, a.hour_, is_available, iw, tot_iw,\n", "        is_available * (iw * 1.00000 / NULLIF(tot_iw,0)) AS wt_available, 1 * (iw * 1.00000 / NULLIF(tot_iw,0)) AS tot_available\n", "        FROM score_merge_base a\n", "        LEFT JOIN agg_item_weights i ON a.be_facility_id = i.be_facility_id AND a.hour_ = i.hour_ AND a.date_ = i.date_ AND a.assortment_type = i.assortment_type AND a.facility_id = i.facility_id\n", "    )\n", "    \n", "    SELECT be_facility_id, date_, hour_, facility_id, assortment_type, SUM(is_available) AS avail_hour, COUNT(is_available) AS tot_hour, \n", "    SUM(wt_available) / SUM(tot_available) AS wt_score\n", "    FROM final_base\n", "    WHERE hour_ BETWEEN 6 AND 23\n", "    GROUP BY 1,2,3,4,5\n", "    \"\"\"\n", "avail_base_df = read_sql_query(avail_query, CON_REDSHIFT)\n", "avail_base_df.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Hourly Weighted Availability (Milk)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["milk_avail_query = f\"\"\"\n", "    WITH milk_assortment AS (\n", "        WITH base AS (\n", "            SELECT DISTINCT DATE(forecast_date) AS date_, a.item_id, a.facility_id \n", "            FROM metrics.milk_ordering_working_steps_logs a \n", "            JOIN (\n", "                SELECT DATE(forecast_date) AS date, item_id, facility_id, MAX(updated_at) AS updated_at \n", "                FROM metrics.milk_ordering_working_steps_logs \n", "                WHERE DATE(forecast_date) = DATE('{start_date}')\n", "                GROUP BY 1,2,3\n", "            ) b ON a.item_id = b.item_id AND a.facility_id = b.facility_id AND a.updated_at = b.updated_at\n", "        ),\n", "\n", "        bucketing AS (\n", "            SELECT DISTINCT DATE(a.forecast_date) AS date_, a.item_id, a.facility_id, stype, ftype \n", "            FROM metrics.milk_ordering_bucket_calculation a \n", "            JOIN ( \n", "                SELECT DATE(forecast_date) AS date, item_id, facility_id, MAX(updated_at) AS updated_at \n", "                FROM metrics.milk_ordering_bucket_calculation \n", "                WHERE DATE(forecast_date) = DATE('{start_date}')\n", "                GROUP BY 1,2,3\n", "            ) b ON a.item_id = b.item_id AND a.facility_id = b.facility_id AND a.updated_at = b.updated_at AND b.date = DATE(a.forecast_date)\n", "        ),\n", "        \n", "        pre_summary AS (\n", "            SELECT a.date_, a.facility_id, a.item_id, CASE WHEN stype IS NULL THEN 'bottom' ELSE stype END AS stype\n", "            FROM base a \n", "            LEFT JOIN (\n", "                SELECT DISTINCT facility_id, item_id, stype, date_ \n", "                FROM bucketing\n", "            ) c ON a.item_id = c.item_id AND a.facility_id = c.facility_id AND a.date_ = c.date_\n", "        ),\n", "\n", "        summary AS (\n", "            SELECT DISTINCT facility_id, item_id, \n", "            CASE \n", "                WHEN stype = 'top' THEN 1\n", "                ELSE 0\n", "            END AS flag\n", "            FROM pre_summary\n", "        )\n", "        \n", "        SELECT * FROM summary\n", "    ),\n", "    \n", "    perishable_pre AS (\n", "        SELECT DISTINCT updated_at, a.item_id, a.facility_id, current_inv, flag \n", "        FROM metrics.perishable_hourly_details_v2 a  \n", "        JOIN milk_assortment m ON m.facility_id = a.facility_id AND a.item_id = m.item_id  \n", "        WHERE updated_at BETWEEN ('{start_date}' || ' 00:00:00')::timestamp AND ('{start_date}' || ' 23:59:59')::timestamp \n", "        AND a.facility_id IN {facility_id_list} \n", "        AND a.item_id IN (SELECT DISTINCT item_id FROM lake_rpc.item_category_details WHERE l2_id = 1185)\n", "    ),\n", "    \n", "    perishable_hourly_base AS (\n", "        SELECT date_, hour_, item_id, facility_id, is_available\n", "        FROM (\n", "            SELECT DISTINCT date_, hour_, item_id, facility_id, is_available,\n", "            CASE \n", "                WHEN flag = 1 AND hour_ BETWEEN 6 AND 22 THEN 1\n", "                WHEN flag = 0 AND hour_ BETWEEN 6 AND 19 THEN 1\n", "                ELSE 0\n", "            END AS time_flag\n", "            FROM (\n", "                SELECT DISTINCT DATE(a.updated_at) AS date_, EXTRACT(hour FROM a.updated_at) AS hour_, a.item_id, a.facility_id, flag, \n", "                CASE WHEN current_inv > 0 THEN 1 ELSE 0 END AS is_available \n", "                FROM perishable_pre a\n", "            )\n", "        )\n", "        WHERE time_flag = 1\n", "    ),\n", "    \n", "    base_pre AS (\n", "        SELECT date_, hour_, item_id, a.facility_id, is_available\n", "        FROM perishable_hourly_base a\n", "        JOIN lake_retail.console_outlet co ON co.facility_id = a.facility_id AND co.active = 1 AND co.business_type_id = 7\n", "    ),\n", "    \n", "    fe_be_mapping AS (\n", "        SELECT DISTINCT a.item_id, ro.facility_id AS facility_id, r.facility_id AS be_facility_id\n", "        FROM lake_rpc.item_outlet_tag_mapping a\n", "        JOIN lake_retail.console_outlet ro ON ro.id = a.outlet_id\n", "        JOIN lake_retail.console_outlet r ON r.id = a.tag_value\n", "        JOIN (\n", "            SELECT om.facility_id AS be_facility_id, rco.facility_id AS fe_facility_id \n", "            FROM lake_po.bulk_facility_outlet_mapping om \n", "            JOIN lake_retail.console_outlet rco ON rco.id = om.outlet_id \n", "            WHERE om.active = true\n", "        ) om ON om.fe_facility_id = ro.facility_id AND om.be_facility_id = r.facility_id\n", "        WHERE tag_type_id = 8 AND a.active = 1\n", "    ),\n", "    \n", "    base AS (\n", "        SELECT date_, hour_, a.item_id, b.be_facility_id, a.facility_id, is_available\n", "        FROM base_pre a\n", "        LEFT JOIN fe_be_mapping b ON a.item_id = b.item_id AND a.facility_id = b.facility_id\n", "        JOIN lake_po.physical_facility_outlet_mapping pfom ON b.be_facility_id = pfom.facility_id AND pfom.active = 1 AND pfom.ars_active = 1\n", "    ),\n", " \n", "    backend_item_weights AS (\n", "        SELECT DISTINCT backend_facility_id AS be_facility_id, item_id, CAST(weights AS float) AS iw \n", "        FROM metrics.backend_item_cart_penetration \n", "        WHERE updated_at = (SELECT MAX(updated_at) FROM metrics.backend_item_cart_penetration WHERE DATE(updated_at) <= DATE('{start_date}'))\n", "    ),\n", "    \n", "    score_merge_base AS (\n", "        SELECT a.*, iw\n", "        FROM base a\n", "        LEFT JOIN backend_item_weights ciw ON ciw.be_facility_id = a.be_facility_id AND ciw.item_id = a.item_id\n", "    ),\n", " \n", "    agg_item_weights AS (\n", "        SELECT date_, be_facility_id, hour_, facility_id, SUM(iw) AS tot_iw\n", "        FROM (\n", "            SELECT DISTINCT be_facility_id, date_, hour_, facility_id, item_id, iw\n", "            FROM score_merge_base\n", "        )\n", "        GROUP BY 1,2,3,4\n", "    ),\n", "    \n", "    final_base AS (\n", "        SELECT a.date_, a.be_facility_id, a.item_id, a.facility_id, a.hour_, is_available, iw, tot_iw,\n", "        \n", "        is_available * (iw * 1.00000 / NULLIF(tot_iw,0)) AS wt_available,\n", "        1 * (iw * 1.00000 / NULLIF(tot_iw,0)) AS tot_available\n", "        \n", "        FROM score_merge_base a\n", "        LEFT JOIN agg_item_weights i ON a.be_facility_id = i.be_facility_id AND a.hour_ = i.hour_ AND a.date_ = i.date_\n", "    )\n", "    \n", "    SELECT a.be_facility_id, date_, hour_, facility_id, SUM(is_available) AS avail_hour, COUNT(is_available) AS tot_hour, \n", "    SUM(wt_available) / SUM(tot_available) AS wt_score\n", "    FROM final_base a\n", "    GROUP BY 1,2,3,4\n", "    \"\"\"\n", "milk_avail_base_df = read_sql_query(milk_avail_query, CON_REDSHIFT)\n", "milk_avail_base_df[\"assortment_type\"] = \"Milk\"\n", "milk_avail_base_df = milk_avail_base_df[\n", "    [\n", "        \"be_facility_id\",\n", "        \"date_\",\n", "        \"hour_\",\n", "        \"facility_id\",\n", "        \"assortment_type\",\n", "        \"avail_hour\",\n", "        \"tot_hour\",\n", "        \"wt_score\",\n", "    ]\n", "]\n", "milk_avail_base_df.shape"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## Aggregated Hourly Weighted Availability Calculation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hour_weight_sql = f\"\"\"\n", "    SELECT backend_facility_id AS be_facility_id, order_hour AS hour_, CAST(weights AS float) AS hw\n", "    FROM metrics.backend_hour_cart_penetration\n", "    WHERE updated_at = (SELECT MAX(updated_at) FROM metrics.backend_hour_cart_penetration WHERE DATE(updated_at) <= DATE('{start_date}'))\n", "    \n", "    \"\"\"\n", "hour_weight = read_sql_query(hour_weight_sql, CON_REDSHIFT)\n", "hour_weight[\"be_facility_id\"] = (hour_weight[\"be_facility_id\"].astype(float)).astype(\n", "    int\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_df = pd.concat([avail_base_df, milk_avail_base_df])\n", "\n", "base_df = base_df.merge(hour_weight, on=[\"be_facility_id\", \"hour_\"], how=\"left\")\n", "\n", "base_df[\"wt_agg_score\"] = base_df[\"wt_score\"] * base_df[\"hw\"]\n", "\n", "base_df[\"availability\"] = base_df[\"avail_hour\"] / base_df[\"tot_hour\"]\n", "\n", "base_df = base_df.sort_values(\n", "    by=[\"be_facility_id\", \"facility_id\", \"assortment_type\", \"date_\", \"hour_\"],\n", "    ascending=[True, True, True, False, True],\n", ").reset_index()\n", "\n", "base_df[[\"hw\", \"wt_agg_score\"]] = (\n", "    base_df[[\"hw\", \"wt_agg_score\"]].fillna(0).astype(float)\n", ")\n", "\n", "base_df[\"cum_hw\"] = base_df.groupby([\"facility_id\", \"assortment_type\", \"date_\"]).agg(\n", "    {\"hw\": \"cumsum\"}\n", ")\n", "base_df[\"cum_score\"] = base_df.groupby([\"facility_id\", \"assortment_type\", \"date_\"]).agg(\n", "    {\"wt_agg_score\": \"cumsum\"}\n", ")\n", "\n", "base_df[\"cum_wt_score\"] = base_df[\"cum_score\"] / base_df[\"cum_hw\"]\n", "\n", "base_df = base_df[\n", "    [\n", "        \"be_facility_id\",\n", "        \"date_\",\n", "        \"hour_\",\n", "        \"facility_id\",\n", "        \"assortment_type\",\n", "        \"avail_hour\",\n", "        \"tot_hour\",\n", "        \"availability\",\n", "        \"wt_score\",\n", "        \"cum_wt_score\",\n", "    ]\n", "].drop_duplicates()"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## Aggregated Backend Weighted Availability Calculations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store_weight_sql = f\"\"\"\n", "    SELECT backend_facility_id AS be_facility_id, facility_id, outlet_id, CAST(store_weight AS float) AS sw\n", "    FROM metrics.backend_store_penetration\n", "    WHERE updated_at = (SELECT MAX(updated_at) FROM metrics.backend_store_penetration WHERE DATE(updated_at) <= DATE('{start_date}'))\n", "    \"\"\"\n", "store_weight = read_sql_query(store_weight_sql, CON_REDSHIFT)\n", "store_weight[\"be_facility_id\"] = (store_weight[\"be_facility_id\"].astype(float)).astype(\n", "    int\n", ")\n", "store_weight.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["backend_base_df = pd.merge(\n", "    base_df, store_weight, on=[\"be_facility_id\", \"facility_id\"], how=\"left\"\n", ")\n", "\n", "backend_base_df[\"wt_store_score\"] = backend_base_df[\"wt_score\"] * backend_base_df[\"sw\"]\n", "\n", "backend_base_df[\"cum_wt_store_score\"] = (\n", "    backend_base_df[\"cum_wt_score\"] * backend_base_df[\"sw\"]\n", ")\n", "\n", "backend_df = (\n", "    backend_base_df.groupby([\"be_facility_id\", \"assortment_type\", \"date_\", \"hour_\"])\n", "    .agg(\n", "        {\n", "            \"availability\": \"mean\",\n", "            \"wt_store_score\": \"sum\",\n", "            \"cum_wt_store_score\": \"sum\",\n", "            \"sw\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "backend_df[\"wt_score\"] = backend_df[\"wt_store_score\"] / backend_df[\"sw\"]\n", "backend_df[\"cum_wt_score\"] = backend_df[\"cum_wt_store_score\"] / backend_df[\"sw\"]\n", "\n", "backend_df = backend_df[\n", "    [\n", "        \"be_facility_id\",\n", "        \"date_\",\n", "        \"hour_\",\n", "        \"assortment_type\",\n", "        \"availability\",\n", "        \"wt_score\",\n", "        \"cum_wt_score\",\n", "    ]\n", "].drop_duplicates()\n", "\n", "backend_df.head(1)"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## Upload to Tables"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["### Store Hourly Weighted Availability"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#### Store Name Mapping\n", "store_query = \"\"\"\n", "    SELECT DISTINCT co.facility_id AS id, cf.name AS name\n", "    FROM lake_retail.console_outlet co \n", "    JOIN lake_crates.facility cf ON cf.id = co.facility_id\n", "    WHERE co.active = 1\n", "\"\"\"\n", "store_df = read_sql_query(store_query, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store_upload_df = base_df.copy()\n", "\n", "store_upload_df = store_upload_df.merge(\n", "    store_df, left_on=[\"be_facility_id\"], right_on=[\"id\"], how=\"left\"\n", ").drop(columns={\"id\"})\n", "store_upload_df = store_upload_df.rename(\n", "    columns={\"name\": \"backend_facility_name\", \"be_facility_id\": \"backend_facility_id\"}\n", ")\n", "\n", "store_upload_df = store_upload_df.merge(\n", "    store_df, left_on=[\"facility_id\"], right_on=[\"id\"], how=\"left\"\n", ").drop(columns={\"id\"})\n", "store_upload_df = store_upload_df.rename(columns={\"name\": \"facility_name\"})\n", "\n", "store_upload_df[\"updated_at\"] = datetime.today() + timedelta(hours=4.5)\n", "\n", "store_upload_df = store_upload_df[\n", "    [\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"assortment_type\",\n", "        \"date_\",\n", "        \"hour_\",\n", "        \"avail_hour\",\n", "        \"tot_hour\",\n", "        \"availability\",\n", "        \"wt_score\",\n", "        \"cum_wt_score\",\n", "        \"updated_at\",\n", "    ]\n", "].drop_duplicates()\n", "store_upload_df = store_upload_df.rename(\n", "    columns={\n", "        \"hour_\": \"hour\",\n", "        \"avail_hour\": \"live_sku\",\n", "        \"tot_hour\": \"total_sku\",\n", "        \"wt_score\": \"weighted_availability\",\n", "        \"cum_wt_score\": \"cum_weighted_availability\",\n", "    }\n", ")\n", "\n", "store_upload_df.head(1)"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["#### Main Table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"backend_facility_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend facility id\",\n", "    },\n", "    {\n", "        \"name\": \"backend_facility_name\",\n", "        \"type\": \"varchar(100)\",\n", "        \"description\": \"backend facility name\",\n", "    },\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility id\"},\n", "    {\"name\": \"facility_name\", \"type\": \"varchar(100)\", \"description\": \"facility name\"},\n", "    {\n", "        \"name\": \"assortment_type\",\n", "        \"type\": \"varchar(20)\",\n", "        \"description\": \"fnv/perishable/packaged_goods\",\n", "    },\n", "    {\"name\": \"date_\", \"type\": \"timestamp\", \"description\": \"date of snapshot\"},\n", "    {\"name\": \"hour\", \"type\": \"integer\", \"description\": \"hour of snapshot\"},\n", "    {\n", "        \"name\": \"live_sku\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"count of SKUs with positive inventory\",\n", "    },\n", "    {\"name\": \"total_sku\", \"type\": \"integer\", \"description\": \"distinct count of SKUs\"},\n", "    {\"name\": \"availability\", \"type\": \"float\", \"description\": \"live_sku/total_sku\"},\n", "    {\n", "        \"name\": \"weighted_availability\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Weighted availability\",\n", "    },\n", "    {\n", "        \"name\": \"cum_weighted_availability\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Cumulative weighted availability\",\n", "    },\n", "    {\"name\": \"updated_at\", \"type\": \"timestamp\", \"description\": \"timestamp of run\"},\n", "]\n", "\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"backend_store_hourly_weighted_availability\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"backend_facility_id\",\n", "        \"facility_id\",\n", "        \"assortment_type\",\n", "        \"date_\",\n", "        \"hour\",\n", "    ],\n", "    \"sortkey\": [\n", "        \"backend_facility_id\",\n", "        \"facility_id\",\n", "        \"assortment_type\",\n", "        \"date_\",\n", "        \"hour\",\n", "    ],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This table contains backned-store level hourly weighted availability for all SKUs\",\n", "}\n", "\n", "if backfill == \"no\":\n", "    if store_upload_df.shape[0] > 0:\n", "        pb.to_redshift(store_upload_df, **kwargs)"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["#### Logs Table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"backend_facility_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend facility id\",\n", "    },\n", "    {\n", "        \"name\": \"backend_facility_name\",\n", "        \"type\": \"varchar(100)\",\n", "        \"description\": \"backend facility name\",\n", "    },\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility id\"},\n", "    {\"name\": \"facility_name\", \"type\": \"varchar(100)\", \"description\": \"facility name\"},\n", "    {\n", "        \"name\": \"assortment_type\",\n", "        \"type\": \"varchar(20)\",\n", "        \"description\": \"fnv/perishable/packaged_goods\",\n", "    },\n", "    {\"name\": \"date_\", \"type\": \"timestamp\", \"description\": \"date of snapshot\"},\n", "    {\"name\": \"hour\", \"type\": \"integer\", \"description\": \"hour of snapshot\"},\n", "    {\n", "        \"name\": \"live_sku\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"count of SKUs with positive inventory\",\n", "    },\n", "    {\"name\": \"total_sku\", \"type\": \"integer\", \"description\": \"distinct count of SKUs\"},\n", "    {\"name\": \"availability\", \"type\": \"float\", \"description\": \"live_sku/total_sku\"},\n", "    {\n", "        \"name\": \"weighted_availability\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Weighted availability\",\n", "    },\n", "    {\n", "        \"name\": \"cum_weighted_availability\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Cumulative weighted availability\",\n", "    },\n", "    {\"name\": \"updated_at\", \"type\": \"timestamp\", \"description\": \"timestamp of run\"},\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"backend_store_hourly_weighted_availability_logs\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"backend_facility_id\",\n", "        \"facility_id\",\n", "        \"assortment_type\",\n", "        \"date_\",\n", "        \"hour\",\n", "    ],\n", "    \"sortkey\": [\n", "        \"backend_facility_id\",\n", "        \"facility_id\",\n", "        \"assortment_type\",\n", "        \"date_\",\n", "        \"hour\",\n", "    ],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This table contains backned-store level hourly weighted availability logs for all SKUs\",\n", "}\n", "\n", "if backfill == \"no\":\n", "    if current_hour >= 22:\n", "        pb.to_redshift(store_upload_df, **kwargs)\n", "else:\n", "    pb.to_redshift(store_upload_df, **kwargs)"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["### Backend Hourly Weighted Availability"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["backend_upload_df = backend_df.copy()\n", "backend_upload_df[\"updated_at\"] = datetime.today() + timedelta(hours=4.5)\n", "\n", "backend_upload_df = backend_upload_df.merge(\n", "    store_df, left_on=[\"be_facility_id\"], right_on=[\"id\"], how=\"left\"\n", ").drop(columns={\"id\"})\n", "backend_upload_df = backend_upload_df.rename(\n", "    columns={\"name\": \"backend_facility_name\", \"be_facility_id\": \"backend_facility_id\"}\n", ")\n", "\n", "backend_upload_df = backend_upload_df[\n", "    [\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"assortment_type\",\n", "        \"date_\",\n", "        \"hour_\",\n", "        \"availability\",\n", "        \"wt_score\",\n", "        \"cum_wt_score\",\n", "        \"updated_at\",\n", "    ]\n", "].drop_duplicates()\n", "backend_upload_df = backend_upload_df.rename(\n", "    columns={\n", "        \"wt_score\": \"weighted_availability\",\n", "        \"cum_wt_score\": \"cum_weighted_availability\",\n", "        \"hour_\": \"hour\",\n", "    }\n", ")\n", "\n", "backend_upload_df.head(1)"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["#### Main Table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"backend_facility_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend facility id\",\n", "    },\n", "    {\n", "        \"name\": \"backend_facility_name\",\n", "        \"type\": \"varchar(100)\",\n", "        \"description\": \"backend facility name\",\n", "    },\n", "    {\n", "        \"name\": \"assortment_type\",\n", "        \"type\": \"varchar(20)\",\n", "        \"description\": \"fnv/perishable/packaged_goods\",\n", "    },\n", "    {\"name\": \"date_\", \"type\": \"timestamp\", \"description\": \"date of snapshot\"},\n", "    {\"name\": \"hour\", \"type\": \"integer\", \"description\": \"hour of snapshot\"},\n", "    {\"name\": \"availability\", \"type\": \"float\", \"description\": \"live_sku/total_sku\"},\n", "    {\n", "        \"name\": \"weighted_availability\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Weighted availability\",\n", "    },\n", "    {\n", "        \"name\": \"cum_weighted_availability\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Cumulative weighted availability\",\n", "    },\n", "    {\"name\": \"updated_at\", \"type\": \"timestamp\", \"description\": \"timestamp of run\"},\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"backend_hourly_weighted_availability\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"backend_facility_id\", \"assortment_type\", \"date_\", \"hour\"],\n", "    \"sortkey\": [\"backend_facility_id\", \"assortment_type\", \"date_\", \"hour\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This table contains backend level hourly weighted availability for all SKUs\",\n", "}\n", "\n", "if backfill == \"no\":\n", "    if backend_upload_df.shape[0] > 0:\n", "        pb.to_redshift(backend_upload_df, **kwargs)"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["#### Logs Table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"backend_facility_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend facility id\",\n", "    },\n", "    {\n", "        \"name\": \"backend_facility_name\",\n", "        \"type\": \"varchar(100)\",\n", "        \"description\": \"backend facility name\",\n", "    },\n", "    {\n", "        \"name\": \"assortment_type\",\n", "        \"type\": \"varchar(20)\",\n", "        \"description\": \"fnv/perishable/packaged_goods\",\n", "    },\n", "    {\"name\": \"date_\", \"type\": \"timestamp\", \"description\": \"date of snapshot\"},\n", "    {\"name\": \"hour\", \"type\": \"integer\", \"description\": \"hour of snapshot\"},\n", "    {\"name\": \"availability\", \"type\": \"float\", \"description\": \"live_sku/total_sku\"},\n", "    {\n", "        \"name\": \"weighted_availability\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Weighted availability\",\n", "    },\n", "    {\n", "        \"name\": \"cum_weighted_availability\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Cumulative weighted availability\",\n", "    },\n", "    {\"name\": \"updated_at\", \"type\": \"timestamp\", \"description\": \"timestamp of run\"},\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"backend_hourly_weighted_availability_logs\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"backend_facility_id\", \"assortment_type\", \"date_\", \"hour\"],\n", "    \"sortkey\": [\"backend_facility_id\", \"assortment_type\", \"date_\", \"hour\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This table contains backend level hourly weighted availability for all SKUs\",\n", "}\n", "\n", "if backfill == \"no\":\n", "    if current_hour >= 22:\n", "        pb.to_redshift(backend_upload_df, **kwargs)\n", "else:\n", "    pb.to_redshift(backend_upload_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}