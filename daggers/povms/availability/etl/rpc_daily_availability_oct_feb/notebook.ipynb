{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": 0.926042, "end_time": "2019-06-07T11:11:49.856958", "exception": false, "start_time": "2019-06-07T11:11:48.930916", "status": "completed"}, "tags": []}, "outputs": [], "source": ["import sqlalchemy as sqla\n", "import pandas\n", "from collections import defaultdict\n", "from datetime import datetime, date as dt\n", "from datetime import timedelta\n", "import numpy as np\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": 0.416997, "end_time": "2019-06-07T11:11:50.673969", "exception": false, "start_time": "2019-06-07T11:11:50.256972", "status": "completed"}, "tags": []}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": 3.216747, "end_time": "2019-06-07T11:11:54.275791", "exception": false, "start_time": "2019-06-07T11:11:51.059044", "status": "completed"}, "tags": []}, "outputs": [], "source": ["redshift = pb.get_connection(\"redshift\")\n", "redshift.schema = \"consumer\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": 2.872828, "end_time": "2019-06-07T11:11:57.747112", "exception": false, "start_time": "2019-06-07T11:11:54.874284", "status": "completed"}, "tags": []}, "outputs": [], "source": ["ars = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": 0.427429, "end_time": "2019-06-07T11:11:58.718203", "exception": false, "start_time": "2019-06-07T11:11:58.290774", "status": "completed"}, "tags": ["parameters"]}, "outputs": [], "source": ["start_date = \"2019-03-01\"\n", "end_date = \"2019-03-10\"\n", "print(start_date)\n", "print(end_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": 0.41249, "end_time": "2019-06-07T11:11:59.536295", "exception": false, "start_time": "2019-06-07T11:11:59.123805", "status": "completed"}, "tags": []}, "outputs": [], "source": ["query = \"\"\"--Item -Outlet Assortment\n", "\n", "WITH dates AS\n", "  (SELECT DISTINCT date(install_ts) AS order_date\n", "                   \n", "   FROM oms_suborder\n", "   WHERE date(install_ts) BETWEEN '{start_date}' AND '{end_date}'),\n", "     --Step 2a:Cross join with product_master_Assortment_log for getting latest substate change\n", "\n", "     substate AS\n", "  (SELECT item_id,\n", "          city_id ,\n", "          created_at,\n", "          order_date,\n", "          new_substate\n", "   FROM product_master_Assortment_log\n", "   JOIN dates ON 1=1),\n", "     --Step 2b:Getting latest substate change for every item\n", "\n", "     item_city_assortment_plan AS\n", "  (SELECT item_id,\n", "          city_id,\n", "          order_date,\n", "          max(created_at)AS recent_assortment\n", "   FROM substate\n", "   WHERE order_date-created_at>=0 \n", "   GROUP BY 1,\n", "            2,\n", "            3),\n", "\n", "     --Step 3:With latest change,getting item-outlet whose substate is 1 which is active\n", "\n", "     substate_id AS\n", "  (SELECT ica.*,date(recent_assortment) as assortment_date,\n", "          new_substate,\n", "          case when new_substate in (1) then 'active'\n", "when new_substate in (2) then 'inactive'\n", "when new_substate in (3) then 'Tem_inactive' \n", "when new_substate in (4) then 'Non-moving' else null end as active_flag\n", "   FROM item_city_assortment_plan ica\n", "   LEFT JOIN product_master_Assortment_log pma ON ica.item_id=pma.item_id\n", "   AND ica.city_id=pma.city_id\n", "   AND ica.recent_assortment=pma.created_at\n", " )\n", "--  select * from substate_id limit 100\n", "                          \n", "                         ,\n", "                         \n", "substate_assort as\n", "(\n", "select distinct\n", "item_id,\n", "city_id,\n", "order_date,\n", "date(created_at) as recent_assortment,\n", "date(recent_assortment) as assortment_date,\n", "master_assortment_substate_id as new_substate,\n", "case when master_assortment_substate_id in (1) then 'active'\n", "when master_assortment_substate_id in (2) then 'inactive'\n", "when master_assortment_substate_id in (3) then 'Tem_inactive' \n", "when master_assortment_substate_id in (4) then 'Non-moving' else null end as active_flag\n", "from consumer.product_master_assortment\n", "join dates on 1=1\n", "and order_date-created_at>=0 )\n", "\n", "-- select * from substate_assort limit 100\n", ",\n", "\n", "union_assortment as\n", "(\n", "select a.*\n", "from substate_id as a\n", "union \n", "select b.*\n", "from substate_assort as b\n", ")\n", ",\n", "\n", "\n", "assortment_level as \n", "(\n", "select a.*\n", "from union_assortment as a\n", "inner join\n", "(select item_id,city_id, min(recent_assortment) as min_assortment from union_assortment group by 1,2) as b\n", "on a.item_id=b.item_id\n", "and a.city_id=b.city_id\n", "and a.recent_assortment=b.min_assortment\n", "where active_flag is not null\n", ")\n", "\n", "-- select count(*),count(distinct item_id||city_id||order_date) from assortment_level---939,652\t939,652\n", ",\n", "city_facility as \n", "  (select tax_location_id as city_id,facility_id from pos_console_outlet\n", "    where upper(name) LIKE '%%SSC%%'\n", "        AND upper(name) NOT LIKE '%%AC%%'\n", "        AND upper(name) NOT LIKE '%%HOT%%'\n", "        and active='true'\n", "        group by 1,2)\n", "        ,\n", "        \n", "substate_facility as \n", "                  (select si.*,facility_id from assortment_level si\n", "                  inner join city_facility cf\n", "                  on si.city_id=cf.city_id\n", "                  )\n", "                  ,\n", "\n", "assortment_daily as \n", "                 ( select sf.*,\n", "                 case when date_of_activation is null then sf.assortment_date+21 else  date_of_activation end as date_of_activation \n", "                 from substate_facility sf\n", "                  left join consumer.rpc_assortment_activation aa\n", "                  on sf.city_id=aa.assortment_city_id\n", "                  and sf.facility_id=aa.facility_id\n", "                  and sf.item_id=aa.ASSORTMENT_ITEM_ID \n", "                  and sf.assortment_date=aa.assortment_date\n", "                  and sf.new_substate=aa.new_substate)\n", "                  \n", "                --   select count(*),count(distinct item_id||facility_id||order_date) from assortment_daily--1,442,092\t1,442,092\n", "                \n", "            \n", "                --   select count(*),count(distinct item_id||facility_id||order_date) from assortment_daily--1,442,092\t1,442,092\n", "                \n", "            select * from assortment_daily\"\"\".format(\n", "    start_date=start_date, end_date=end_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": false, "start_time": "2019-06-07T11:11:59.918539", "status": "running"}, "tags": []}, "outputs": [], "source": ["assortment = pandas.read_sql_query(sql=query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["assortment.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["query_inv = \"\"\"\n", "SELECT item_id,\n", "             date(snapshot_datetime) AS assortment_date,\n", "             outlet_id,\n", "             actual_quantity,\n", "             blocked_quantity,\n", "             facility_id,\n", "             tax_location_id,\n", "\n", "          CASE\n", "              WHEN actual_quantity-blocked_quantity >0 THEN 'live'\n", "              ELSE'not live'\n", "          END AS availability\n", "\n", "      FROM reports.reports_item_inventory_snapshot riis\n", "      left join retail.console_outlet co\n", "      on riis.outlet_id=co.id\n", "      where date(snapshot_datetime) between '{start_date}' and '{end_date}'\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["inv_values = pandas.read_sql_query(query_inv, con=ars)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["inventory = (\n", "    inv_values.groupby([\"item_id\", \"facility_id\", \"assortment_date\", \"tax_location_id\"])[\n", "        \"actual_quantity\", \"blocked_quantity\"\n", "    ]\n", "    .sum()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["inventory[\"availability\"] = np.where(\n", "    inventory.actual_quantity - inventory.blocked_quantity > 0, \"live\", \"not live\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["inventory.item_id = inventory.item_id.astype(int)\n", "inventory.facility_id = inventory.facility_id.astype(float)\n", "inventory.tax_location_id = inventory.tax_location_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["availability_merge = assortment.merge(\n", "    inventory,\n", "    left_on=[\"item_id\", \"facility_id\", \"city_id\", \"order_date\"],\n", "    right_on=[\"item_id\", \"facility_id\", \"tax_location_id\", \"assortment_date\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["availability_merge[\"availability\"] = np.where(\n", "    availability_merge.availability.isna(), \"not live\", availability_merge.availability\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["city_facility_names = \"\"\"select tax_location_id as city_id,pcl.name as city_name,facility_id ,cf.name as facility_name from pos_console_outlet pco\n", "left join pos_console_location pcl\n", "on pco.tax_location_id=pcl.id\n", "left join crates_facility cf\n", "on pco.facility_id=cf.id\n", "where upper(pco.name) LIKE '%%SSC%%'\n", "        AND upper(pco.name) NOT LIKE '%%AC%%'\n", "        AND upper(pco.name) NOT LIKE '%%HOT%%' \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["city_facility_mapping = pandas.read_sql_query(sql=city_facility_names, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["avail_naming = availability_merge.merge(\n", "    city_facility_mapping, on=[\"city_id\", \"facility_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["avail_naming = avail_naming.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["avail_naming.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["avail_naming = avail_naming[\n", "    [\n", "        \"item_id\",\n", "        \"city_id\",\n", "        \"order_date\",\n", "        \"recent_assortment\",\n", "        \"assortment_date_x\",\n", "        \"new_substate\",\n", "        \"facility_id\",\n", "        \"date_of_activation\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"availability\",\n", "        \"city_name\",\n", "        \"facility_name\",\n", "        \"active_flag\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["avail_naming = avail_naming.rename(columns={\"assortment_date_x\": \"assortment_date\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["avail_naming[\"flag\"] = (avail_naming.active_flag == \"inactive\") & (\n", "    avail_naming.availability == \"not live\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["active_live = avail_naming[avail_naming.flag == False]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["active_live[\"Tem_inactive_flag\"] = (active_live.active_flag == \"Tem_inactive\") & (\n", "    avail_naming.availability == \"not live\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["active_live_temp = active_live[active_live.Tem_inactive_flag == False]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["availability = active_live_temp[~active_live_temp.active_flag.isna()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["availability = availability.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["availability.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["item_query = \"\"\"\n", "with item_detail as\n", "(\n", "SELECT DISTINCT pp.item_id,\n", "                   pp.name,\n", "                   CAT.NAME AS l0,\n", "                   CAT.ID AS l0_ID,\n", "           case when pp.is_pl='true' then 'TRUE'\n", "           when pp.is_pl='false' then 'FALSE'\n", "           else 'False' end as Is_GB,\n", "           pp.VARIANT_DESCRIPTION as UOM,\n", "           weight_in_gm\n", "        --   row_number()over(order by pp.item_id) as wh_rnk\n", "FROM GR_PRODUCT P\n", "   INNER JOIN GR_PRODUCT_CATEGORY_MAPPING PCM ON P.ID = PCM.PRODUCT_ID\n", "    INNER JOIN GR_CATEGORY CAT2 ON PCM.CATEGORY_ID = CAT2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN GR_CATEGORY CAT1 ON CAT2.PARENT_CATEGORY_ID = CAT1.ID\n", "   INNER JOIN GR_CATEGORY CAT ON CAT1.PARENT_CATEGORY_ID = CAT.ID \n", "   INNER JOIN consumer.rpc_item_product_mapping ipm ON ipm.product_id=p.id\n", "   AND ipm.item_id IS NOT NULL\n", "   left JOIN \n", "   (select a.* \n", "from\n", "(\n", "select a.item_id,\n", "name,\n", "is_pl,\n", "variant_description,\n", "weight_in_gm,\n", "row_number()over(partition by a.item_id order by VARIANT_DESCRIPTION) as rank_1\n", "from pos_product_product as a\n", "inner join\n", "(select item_id,max(updated_at) as updated_at from pos_product_product\n", "group by 1)b\n", "on a.item_id=b.item_id\n", "and a.updated_at=b.updated_at\n", "and a.active=1\n", "and a.approved=1\n", ")as a\n", "where rank_1=1\n", ") pp ON pp.item_id=ipm.item_id\n", "  )\n", "--  select item_id from item_detail group by 1 having count(*)>1;\n", "select * from item_detail \n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["item_pl_detail = pandas.read_sql_query(sql=item_query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["availability[\"order_date\"] = pandas.to_datetime(availability[\"order_date\"])\n", "availability[\"month\"] = availability[\"order_date\"].dt.month"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["avail_item = availability.merge(item_pl_detail, on=[\"item_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["avail_item.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["item_bucket_query = \"\"\"\n", "select distinct \n", "null as  Facility_id,\n", "null as item_id,\n", "null as  buckets,\n", "null as bucket_x,\n", "null as month\n", "from consumer.item_bucket_mapping\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["item_bucket = pandas.read_sql_query(item_bucket_query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["avail_item.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["item_bucket.month = item_bucket.month.astype(float)\n", "item_bucket.facility_id = item_bucket.facility_id.astype(float)\n", "item_bucket.item_id = item_bucket.item_id.astype(float)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["availability_item = avail_item.merge(\n", "    item_bucket, on=[\"item_id\", \"facility_id\", \"month\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["availability_item.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["availability_item.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["availability_item[\"buckets\"] = np.where(\n", "    availability_item.buckets.isna(), \"NA\", availability_item.buckets\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["availability_item[\"is_gb\"] = np.where(\n", "    availability_item.is_gb.isna(), \"FALSE\", availability_item.is_gb\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["final_availability = availability_item.drop(columns=[\"flag\", \"Tem_inactive_flag\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["final_availability.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["final_availability.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"item_id\", \"type\": \"integer\"},\n", "    {\"name\": \"city_id\", \"type\": \"integer\"},\n", "    {\"name\": \"order_date\", \"type\": \"timestamp\"},\n", "    {\"name\": \"recent_assortment\", \"type\": \"timestamp\"},\n", "    {\"name\": \"assortment_date\", \"type\": \"timestamp\"},\n", "    {\"name\": \"new_substate\", \"type\": \"smallint\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\"},\n", "    {\"name\": \"date_of_activation\", \"type\": \"timestamp\"},\n", "    {\"name\": \"actual_quantity\", \"type\": \"float\"},\n", "    {\"name\": \"blocked_quantity\", \"type\": \"float\"},\n", "    {\"name\": \"availability\", \"type\": \"varchar(30)\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar(500)\"},\n", "    {\"name\": \"facility_name\", \"type\": \"varchar(500)\"},\n", "    {\"name\": \"active_flag\", \"type\": \"varchar\"},\n", "    {\"name\": \"month\", \"type\": \"integer\"},\n", "    {\"name\": \"item_name \", \"type\": \"varchar(500)\"},\n", "    {\"name\": \"l0\", \"type\": \"varchar(500)\"},\n", "    {\"name\": \"l0_id\", \"type\": \"float\"},\n", "    {\"name\": \"is_gb\", \"type\": \"varchar\"},\n", "    {\"name\": \"uom\", \"type\": \"varchar\"},\n", "    {\"name\": \"weight_in_gm\", \"type\": \"float\"},\n", "    {\"name\": \"buckets\", \"type\": \"varchar\"},\n", "    {\"name\": \"bucket_x\", \"type\": \"varchar\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"consumer\",\n", "    \"table_name\": \"rpc_daily_availability\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"item_id\", \"facility_id\", \"order_date\"],\n", "    \"sortkey\": [\"order_date\", \"item_id\", \"facility_id\"],\n", "    \"incremental_key\": \"order_date\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "}\n", "\n", "pb.to_redshift(final_availability, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": []}], "metadata": {"celltoolbar": "Tags", "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.5.2"}, "papermill": {"duration": null, "end_time": null, "environment_variables": {}, "exception": null, "input_path": "/opt/grofers/airflow/dags/po_vms/notebooks/po_vms_rpc_daily_availability_oct_feb_v1.ipynb", "output_path": "s3://grofers-prod-dse/airflow/notebooks/po_vms_rpc_daily_availability_oct_feb_v1/scheduled__2018-10-01T00:00:00+00:00/output-2018-10-01.ipynb", "parameters": {"end_date": "2018-10-02", "start_date": "2018-10-01"}, "start_time": "2019-06-07T11:11:45.399919", "version": "1.0.0"}}, "nbformat": 4, "nbformat_minor": 2}