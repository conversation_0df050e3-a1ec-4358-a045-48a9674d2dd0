dag_name: realtime
dag_type: etl
escalation_priority: high
executor:
  config:
    cpu:
      limit: 2
      request: 2
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 20G
      request: 20G
    node_selectors:
      nodetype: airflow-dags-spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: povms
notebook:
  parameters: null
  retries: 3
owner:
  email: <EMAIL>
  slack_id: U03RXQ8KA0J
path: povms/availability/etl/realtime
paused: false
project_name: availability
schedule:
  end_date: '2024-02-22T00:00:00'
  interval: 35 * * * *
  start_date: '2023-12-31T00:00:00'
schedule_type: fixed
sla: 118 minutes
slack_alert_configs:
- channel: bl-data-airflow-alerts
support_files:
- availability_alerts.html
tags: []
template_name: notebook
version: 84
pool: povms_pool
