<head>
<style type="text/css">
.tg {
    border-collapse: collapse;
    border-spacing: 0;
    border-color: #aaa;
    width: 100%;
    max-width: 690px;
}

.tg td {
    font-family: Roboto, sans-serif;
    font-size: 12px;
    padding: 5px 5px;
    border-style: solid;
    border-width: 1px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    color: #333;
    border-left-width:1px;
    border-right-width:1px;
    background-color: #fff;
}

.tg tr:last-child td{
    background-color: #aaa;
}
.tg th {
    font-family: Arial, sans-serif;
    font-size: 12px;
    font-weight: normal;
    padding: 5px 5px;
    border-style: solid;
    border-width: 6px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    color: #fff;
    border-left-width:1px;
    border-right-width:1px;
    background-color: #f38630;
}

.tg .tg-0lax {
    text-align: left;
    vertical-align: top
}

.tg .tg-dg7a {
    background-color: #F0F0F0;
    text-align: left;
    vertical-align: top
}
</style>
</head>
<p>Hi All,</p>
<p>Availability alerts are enabled as following conditions have failed, kindly get back to your kind and awesome data folks to recover <br/>

</p>

<p>Conditions which are listed below have failed:</p>

<li>
    {{products|join(', ')}}

</li>

<p>Best Regards,<br />
Data Bangalore
</p>
<p></p>