{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sqlalchemy as sqla\n", "import pandas as pd\n", "from collections import defaultdict\n", "from datetime import datetime, date as dt\n", "from datetime import timedelta\n", "import numpy as np\n", "import pencilbox as pb\n", "import gc\n", "import time\n", "\n", "!pip install pandasql\n", "import pandasql as ps"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "redshift.schema = \"consumer\"\n", "trino = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_hour = int(pd.to_datetime(datetime.now() + timedelta(hours=5.5)).strftime(\"%H\"))\n", "current_hour"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### fetch from trino "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["his_trino = read_sql_query(\n", "    f\"\"\"\n", "                with his as (\n", "                    select *, extract(hour from updated_at_ist) as hour_\n", "                    from supply_etls.hourly_inventory_snapshots\n", "                    where date_ist >= current_date\n", "                ),\n", "                \n", "                cf as (\n", "                    select id as facility_id, max(name) as facility_name from crates.facility\n", "                    where lake_active_record\n", "                    group by 1\n", "                ),\n", "                \n", "                icd as (\n", "                    select item_id, l0, l0_id\n", "                    from rpc.item_category_details\n", "                    where lake_active_record\n", "                ),\n", "                \n", "                pp as (\n", "                    select item_id, name as item_name, variant_uom_text as uom, weight_in_gm\n", "                    from rpc.product_product\n", "                    where id in (select max(id) from rpc.product_product where active = 1 and approved = 1 group by item_id)\n", "                )\n", "                \n", "                \n", "                \n", "                select his.item_id, updated_at_ist as order_date, date('1970-01-01') as recent_assortment, date('1970-01-01') as assortment_date, substate_id as new_substate, his.facility_id,\n", "                        date('1970-01-01') as date_of_activation, current_inventory as actual_quantity, 0 as blocked_quantity, \n", "                        case when current_inventory > 0 then 'live' else 'not live' end as availability, facility_name, \n", "                        case when substate_id in (1) then 'active' else 'inactvie' end as active_flag, extract(month from date_ist) as month, item_name, l0, l0_id, 'False' as is_gb, uom, weight_in_gm, null as buckets, null as bucket_x, \n", "                        case when current_inventory > 0 then 1 else 0 end as inv_flag, \n", "                        case when current_inventory > 0 then 1 else 0 end as app_live, \n", "                        null as unreliable, null as unorderable\n", "                        \n", "                        \n", "                         \n", "                from his\n", "                left join cf on cf.facility_id = his.facility_id\n", "                left join icd on icd.item_id = his.item_id\n", "                left join pp on pp.item_id = his.item_id\n", "                where hour_ = {current_hour - 1}\n", "                \n", "            \"\"\",\n", "    trino,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["his_trino = his_trino.astype(\n", "    {\n", "        \"item_id\": \"int\",\n", "        \"new_substate\": \"int\",\n", "        \"facility_id\": \"int\",\n", "        \"actual_quantity\": \"float\",\n", "        \"blocked_quantity\": \"float\",\n", "        \"month\": \"int\",\n", "        \"l0_id\": \"float\",\n", "        \"weight_in_gm\": \"float\",\n", "        \"inv_flag\": \"int\",\n", "        \"app_live\": \"int\",\n", "    }\n", ")\n", "\n", "his_trino[\"order_date\"] = pd.to_datetime(his_trino[\"order_date\"])\n", "his_trino[\"recent_assortment\"] = pd.to_datetime(his_trino[\"recent_assortment\"])\n", "his_trino[\"assortment_date\"] = pd.to_datetime(his_trino[\"assortment_date\"])\n", "his_trino[\"date_of_activation\"] = pd.to_datetime(his_trino[\"date_of_activation\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["his_trino = his_trino[\n", "    [\n", "        \"item_id\",\n", "        \"order_date\",\n", "        \"recent_assortment\",\n", "        \"assortment_date\",\n", "        \"new_substate\",\n", "        \"facility_id\",\n", "        \"date_of_activation\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"availability\",\n", "        \"facility_name\",\n", "        \"active_flag\",\n", "        \"month\",\n", "        \"item_name\",\n", "        \"l0\",\n", "        \"l0_id\",\n", "        \"is_gb\",\n", "        \"uom\",\n", "        \"weight_in_gm\",\n", "        \"buckets\",\n", "        \"bucket_x\",\n", "        \"inv_flag\",\n", "        \"app_live\",\n", "        \"unreliable\",\n", "        \"unorderable\",\n", "    ]\n", "]\n", "\n", "his_trino.new_substate = his_trino.new_substate.fillna(0)\n", "his_trino.new_substate = his_trino.new_substate.astype(int)\n", "his_trino[\"inv_flag\"] = his_trino.inv_flag.astype(int)\n", "his_trino[\"app_live\"] = his_trino.app_live.astype(int)\n", "his_trino[\"facility_id\"] = his_trino.facility_id.astype(int)\n", "his_trino[\"new_substate\"] = his_trino.new_substate.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["his_trino.rename(columns={\"item_name\": \"item_name \"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["his_trino.drop_duplicates([\"item_id\", \"facility_id\"], inplace=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# table push"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"Item ID\"},\n", "    {\"name\": \"order_date\", \"type\": \"timestamp\", \"description\": \"Date time of analysis\"},\n", "    {\n", "        \"name\": \"recent_assortment\",\n", "        \"type\": \"timestamp\",\n", "        \"description\": \"Date when the item facility combination is updated\",\n", "    },\n", "    {\n", "        \"name\": \"assortment_date\",\n", "        \"type\": \"timestamp\",\n", "        \"description\": \"Date when the item facility combination was first created\",\n", "    },\n", "    {\n", "        \"name\": \"new_substate\",\n", "        \"type\": \"smallint\",\n", "        \"description\": \"substate_id of item at order_date\",\n", "    },\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"Facility ID\"},\n", "    {\n", "        \"name\": \"date_of_activation\",\n", "        \"type\": \"timestamp\",\n", "        \"description\": \"First arrival of item in stock after becoming active\",\n", "    },\n", "    {\n", "        \"name\": \"actual_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Actual physical inventory at order_date\",\n", "    },\n", "    {\n", "        \"name\": \"blocked_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Blocked inventory at order_date\",\n", "    },\n", "    {\n", "        \"name\": \"availability\",\n", "        \"type\": \"varchar(30)\",\n", "        \"description\": \"'live' if unblocked inventory > 0 otherwise 'not live'\",\n", "    },\n", "    {\"name\": \"facility_name\", \"type\": \"varchar(500)\", \"description\": \"Facility Name\"},\n", "    {\n", "        \"name\": \"active_flag\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Active if substate_id = 1 otherwise inactive\",\n", "    },\n", "    {\"name\": \"month\", \"type\": \"integer\", \"description\": \"Month from order_date\"},\n", "    {\"name\": \"item_name \", \"type\": \"varchar(500)\", \"description\": \"Item Name\"},\n", "    {\"name\": \"l0\", \"type\": \"varchar(500)\", \"description\": \"Level 0 category name\"},\n", "    {\"name\": \"l0_id\", \"type\": \"float\", \"description\": \"Level 0 category id\"},\n", "    {\"name\": \"is_gb\", \"type\": \"varchar\", \"description\": \"is grofer's brand\"},\n", "    {\"name\": \"uom\", \"type\": \"varchar\", \"description\": \"unit of measurement\"},\n", "    {\"name\": \"weight_in_gm\", \"type\": \"float\", \"description\": \"Weight in gms\"},\n", "    {\"name\": \"buckets\", \"type\": \"varchar\", \"description\": \"Bucket A/B\"},\n", "    {\n", "        \"name\": \"bucket_x\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Y if item is Bucket X otherwise N\",\n", "    },\n", "    {\"name\": \"inv_flag\", \"type\": \"int\", \"description\": \"1 if unblocked inventory > 0\"},\n", "    {\n", "        \"name\": \"app_live\",\n", "        \"type\": \"int\",\n", "        \"description\": \"status on the app at order_date\",\n", "    },\n", "    {\"name\": \"unreliable\", \"type\": \"varchar\", \"description\": \"unreliable tag\"},\n", "    {\"name\": \"unorderable\", \"type\": \"varchar\", \"description\": \"unorderable tag\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"consumer\",\n", "    \"table_name\": \"rpc_daily_availability\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"item_id\", \"facility_id\", \"order_date\"],\n", "    \"sortkey\": [\"order_date\", \"item_id\", \"facility_id\"],\n", "    \"incremental_key\": \"order_date\",\n", "    \"distkey\": \"item_id\",\n", "    \"load_type\": \"append\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"Stores hourly inventory data on item facility level\",\n", "}\n", "\n", "pb.to_redshift(his_trino, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final_availability.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "pycharm": {"stem_cell": {"cell_type": "raw", "metadata": {"collapsed": false}, "source": []}}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 4}