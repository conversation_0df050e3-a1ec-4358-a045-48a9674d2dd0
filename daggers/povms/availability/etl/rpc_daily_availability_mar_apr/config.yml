dag_name: rpc_daily_availability_mar_apr
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters:
    end_date: '{{ next_ds }}'
    start_date: '{{ ds }}'
owner:
  name: sonal
  slack_id: UABB9AK9V
path: povms/availability/etl/rpc_daily_availability_mar_apr
paused: true
pool: povms_pool
project_name: availability
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 0 0 * * *
  start_date: '2019-04-30T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
