{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "from datetime import date, datetime, timedelta\n", "\n", "\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    start = time.time()\n", "    df = pd.read_sql_query(sql, con)\n", "    end = time.time()\n", "    if (end - start) > 60:\n", "        print(\"Time: \", (end - start) / 60, \"min\")\n", "    else:\n", "        print(\"Time: \", end - start, \"s\")\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_mapping_query = f\"\"\"\n", "    WITH eligible_fcs AS (\n", "        SELECT a.facility_id \n", "        FROM (\n", "            SELECT facility_id, COUNT(DISTINCT order_id) AS order_count\n", "            FROM lake_ims.ims_order_details a\n", "            LEFT JOIN lake_retail.console_outlet o ON a.outlet = o.id\n", "            WHERE DATE(a.created_at) BETWEEN current_date - interval '1 days' AND current_date\n", "            AND a.status_id IN (1,2)\n", "            AND facility_id <> 1028\n", "            AND business_type NOT ILIKE '%%b2b%%'\n", "            GROUP BY 1\n", "            HAVING order_count > 10\n", "        ) a\n", "        INNER JOIN lake_retail.console_outlet o ON a.facility_id = o.facility_id AND business_type_id = 7\n", "    )\n", "    SELECT co.id as outlet_id, co.name AS outlet_name, co.facility_id\n", "    FROM lake_retail.console_outlet co\n", "    INNER JOIN lake_retail.console_location cl ON co.tax_location_id = cl.id\n", "    INNER JOIN eligible_fcs ef ON co.facility_id = ef.facility_id\n", "    WHERE co.active = 1 AND co.facility_id IS NOT NULL AND business_type_id = 7\n", "    \"\"\"\n", "outlet_mapping_df = read_sql_query(outlet_mapping_query, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_mapping_query = f\"\"\"\n", "    SELECT id as facility_id, name AS facility_name\n", "    FROM lake_crates.facility\n", "    GROUP BY 1,2\n", "    \"\"\"\n", "facility_mapping_df = read_sql_query(facility_mapping_query, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_mapping_df[[\"facility_id\", \"outlet_id\"]] = outlet_mapping_df[\n", "    [\"facility_id\", \"outlet_id\"]\n", "].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_id = list(outlet_mapping_df[\"facility_id\"].unique())\n", "facility_id = tuple(facility_id)\n", "\n", "outlet_id = list(outlet_mapping_df[\"outlet_id\"].unique())\n", "outlet_id = tuple(outlet_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date = (datetime.now() + timedelta(days=0, hours=5.5)).date()\n", "end_date = (datetime.now() + timedelta(days=0, hours=5.5)).date()\n", "\n", "start_date, end_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_query = f\"\"\"\n", "    SELECT date_, base.hour, base.city, facility_id, facility_name,\n", "    COUNT(DISTINCT CASE WHEN net_inv > 0 THEN base.item_id END) AS live_sku, \n", "    COUNT(DISTINCT base.item_id) AS total_sku, live_sku * 1.00/total_sku AS availability,\n", "    (CASE WHEN SUM(weights::float) > 0 THEN SUM(CASE WHEN net_inv > 0 THEN weights::float ELSE 0 END) * 1.00 / SUM(weights::float) ELSE 0 END) AS weighted_availability\n", "    FROM (\n", "        (\n", "            SELECT DATE(base.order_date) AS date_, EXTRACT(hour FROM base.order_date) AS hour, base.item_id, ib.l0, ib.l2, base.facility_id, \n", "            cf.name AS facility_name, cl.name AS city,\n", "            CASE \n", "                WHEN actual_quantity - blocked_quantity < 0 THEN 0 \n", "                ELSE actual_quantity - blocked_quantity \n", "            END AS net_inv\n", "            FROM (\n", "                SELECT order_date, item_id, a.facility_id, o.tax_location_id, actual_quantity, blocked_quantity\n", "                FROM consumer.rpc_daily_availability a\n", "                INNER JOIN lake_retail.console_outlet o ON a.facility_id = o.facility_id AND business_type_id = 7\n", "                INNER JOIN lake_po.physical_facility_outlet_mapping pfom ON a.facility_id = pfom.facility_id AND pfom.active = 1 AND pfom.ars_active = 1\n", "                WHERE (order_date) BETWEEN ('{start_date}' || ' 00:00:00')::timestamp AND ('{end_date}' || ' 23:55:55')::timestamp\n", "                AND EXTRACT(hour FROM order_date) BETWEEN 6 AND 23\n", "                AND active_flag = 'active'\n", "                GROUP BY 1,2,3,4,5,6\n", "            ) base\n", "            LEFT JOIN (\n", "                SELECT DISTINCT item_id, l0, l1, l2, l2_id, l0_id \n", "                FROM lake_rpc.item_category_details\n", "            ) ib ON ib.item_id = base.item_id\n", "            LEFT JOIN lake_crates.facility cf ON base.facility_id = cf.id\n", "            LEFT JOIN lake_retail.console_location cl ON base.tax_location_id = cl.id\n", "            WHERE ib.l2_id NOT IN (1425, 198, 31, 1097, 197, 1956, 1185, 1367, 63, 1369, 1389, 1778, 97, 1094, 1093, 1091, 138, 33, 949, 950)\n", "            AND (l0_id NOT IN (1487) OR LOWER(ib.l2) = 'frozen veg')\n", "            AND base.facility_id IN {facility_id}\n", "            GROUP BY 1,2,3,4,5,6,7,8,9\n", "        )\n", "        UNION\n", "        (\n", "            SELECT DATE(base.updated_at) AS date_, EXTRACT(hour FROM base.updated_at) AS hour, base.item_id, ib.l0, ib.l2, base.facility_id,\n", "            cf.name AS facility_name, cl.name AS city,\n", "            CASE \n", "                WHEN base.current_inv < 0 THEN 0 \n", "                ELSE base.current_inv \n", "            END AS net_inv\n", "            FROM (\n", "                SELECT a.updated_at, a.facility_id, o.tax_location_id, item_id, current_inv \n", "                FROM metrics.fnv_hourly_details a\n", "                INNER JOIN lake_retail.console_outlet o ON a.facility_id = o.facility_id AND business_type_id = 7\n", "                INNER JOIN lake_po.physical_facility_outlet_mapping pfom ON a.facility_id = pfom.facility_id AND pfom.active = 1 AND pfom.ars_active = 1\n", "                WHERE (a.updated_at) BETWEEN ('{start_date}' || ' 00:00:00')::timestamp AND ('{end_date}' || ' 23:55:55')::timestamp\n", "                AND EXTRACT(hour FROM a.updated_at) BETWEEN 6 AND 23\n", "                GROUP BY 1,2,3,4,5\n", "            ) base\n", "            LEFT JOIN (\n", "                SELECT DISTINCT item_id, l0, l1, l2, l0_id \n", "                FROM lake_rpc.item_category_details\n", "            ) ib ON ib.item_id = base.item_id\n", "            LEFT JOIN lake_crates.facility cf ON base.facility_id = cf.id\n", "            LEFT JOIN lake_retail.console_location cl ON base.tax_location_id = cl.id\n", "            WHERE l0_id IN (1487) AND LOWER(l2) !='frozen veg'\n", "            AND base.facility_id IN {facility_id}\n", "            GROUP BY 1,2,3,4,5,6,7,8,9\n", "        )\n", "        UNION \n", "        (\n", "            SELECT date(base.updated_at) AS date_, EXTRACT(hour FROM base.updated_at) AS hour, base.item_id, ib.l0, ib.l2, base.facility_id,\n", "            cf.name AS facility_name, cl.name AS city,\n", "            CASE \n", "                WHEN base.current_inv < 0 THEN 0 \n", "                ELSE base.current_inv \n", "            END AS net_inv\n", "            FROM (\n", "                SELECT a.facility_id, o.tax_location_id, item_id, a.updated_at, current_inv\n", "                FROM metrics.perishable_hourly_details_v2 a\n", "                INNER JOIN lake_retail.console_outlet o ON a.facility_id = o.facility_id AND business_type_id = 7\n", "                INNER JOIN lake_po.physical_facility_outlet_mapping pfom ON a.facility_id = pfom.facility_id AND pfom.active = 1 AND pfom.ars_active = 1\n", "                WHERE (a.updated_at) BETWEEN ('{start_date}' || ' 00:00:00')::timestamp AND ('{end_date}' || ' 23:55:55')::timestamp\n", "                AND EXTRACT(hour FROM a.updated_at) between 6 and 23 AND is_perishable = 1\n", "                GROUP BY 1,2,3,4,5\n", "            ) base\n", "            LEFT JOIN (\n", "                SELECT DISTINCT item_id, l0, l1, l2, l2_id \n", "                FROM lake_rpc.item_category_details\n", "            ) ib ON ib.item_id = base.item_id\n", "            LEFT JOIN lake_crates.facility cf ON base.facility_id = cf.id\n", "            LEFT JOIN lake_retail.console_location cl ON base.tax_location_id = cl.id\n", "            WHERE l2_id IN (1425, 198, 31, 1097, 197, 1956, 1185, 1367, 63, 1369, 1389, 1778, 97, 1094, 1093, 1091, 138, 33, 949, 950)\n", "            AND base.facility_id IN {facility_id}\n", "            GROUP BY 1,2,3,4,5,6,7,8,9\n", "        )\n", "    ) base\n", "    LEFT JOIN (\n", "        SELECT * from metrics.city_item_cart_penetration \n", "        WHERE updated_at = (\n", "            SELECT MAX(updated_at) \n", "            FROM metrics.city_item_cart_penetration\n", "        )\n", "    ) cp ON base.item_id = cp.item_id AND base.city = cp.city\n", "    GROUP BY 1,2,3,4,5\n", "    \"\"\"\n", "overall_df = read_sql_query(overall_query, CON_REDSHIFT)\n", "overall_df[\"assortment_type\"] = \"Overall\"\n", "overall_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# overall_df[(overall_df.facility_id == 455) & (overall_df.hour == 11)].sort_values(by=[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_df = overall_df.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_df = base_df[\n", "    [\n", "        \"city\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"assortment_type\",\n", "        \"date_\",\n", "        \"hour\",\n", "        \"live_sku\",\n", "        \"total_sku\",\n", "        \"availability\",\n", "        \"weighted_availability\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_df = base_df.sort_values(\n", "    by=[\"city\", \"facility_name\", \"assortment_type\", \"date_\", \"hour\"],\n", "    ascending=[True, True, True, False, True],\n", ").reset_index()\n", "\n", "base_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hour_weight_sql = \"\"\"\n", "    SELECT city, order_hour AS hour, weights AS hour_weight\n", "    FROM metrics.city_hour_cart_penetration\n", "    WHERE updated_at = (SELECT MAX(updated_at) FROM metrics.city_hour_cart_penetration)\n", "    \"\"\"\n", "hour_weight = read_sql_query(hour_weight_sql, CON_REDSHIFT)\n", "hour_weight.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_df1 = base_df.merge(hour_weight, how=\"left\", on=[\"city\", \"hour\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_df1.hour_weight.fillna(0, inplace=True)\n", "base_df1.hour_weight = base_df1.hour_weight.astype(float)\n", "base_df1.weighted_availability.fillna(0, inplace=True)\n", "base_df1.weighted_availability = base_df1.weighted_availability.astype(float)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_df1[\"avail_x_hour_weight\"] = (\n", "    base_df1[\"weighted_availability\"] * base_df1[\"hour_weight\"]\n", ")\n", "base_df1[\"avail_x_hour_weight\"] = base_df1[\"avail_x_hour_weight\"].astype(float)\n", "\n", "base_df1[\"updated_at\"] = datetime.today() + timedelta(hours=5.5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# base_df1[(base_df1.facility_id == 1175) & (base_df1.assortment_type == \"Overall\")].head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_df1[\"cum_hour_weight\"] = base_df1.groupby(\n", "    [\"facility_id\", \"assortment_type\", \"date_\"]\n", ").agg({\"hour_weight\": \"cumsum\"})\n", "\n", "base_df1[\"cum_avail_x_hour_weight\"] = base_df1.groupby(\n", "    [\"facility_id\", \"assortment_type\", \"date_\"]\n", ").agg({\"avail_x_hour_weight\": \"cumsum\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_df1[\"cum_weighted_availability\"] = (\n", "    base_df1[\"cum_avail_x_hour_weight\"] * 1.00 / base_df1[\"cum_hour_weight\"]\n", ")\n", "\n", "base_df1[\"cum_weighted_availability\"].fillna(0, inplace=True)\n", "\n", "base_df1.drop(\n", "    columns=[\n", "        \"avail_x_hour_weight\",\n", "        \"hour_weight\",\n", "        \"cum_hour_weight\",\n", "        \"cum_avail_x_hour_weight\",\n", "    ],\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# base_df1[(base_df1.facility_id == 1351) & (base_df1.assortment_type == \"Overall\")].head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_df1.drop(columns=[\"index\"], inplace=True)\n", "base_df1.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store_weight_sql = \"\"\"\n", "    SELECT city, facility_id, outlet_id, store_weight\n", "    FROM metrics.city_store_penetration\n", "    WHERE updated_at = (SELECT MAX(updated_at) FROM metrics.city_store_penetration)\n", "    \"\"\"\n", "\n", "store_weight = read_sql_query(store_weight_sql, CON_REDSHIFT)\n", "\n", "store_weight[\"store_weight\"] = store_weight[\"store_weight\"].astype(float)\n", "store_weight.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store_weight[store_weight[\"city\"] == \"Gurgaon\"][\"store_weight\"].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_df2 = base_df1.merge(store_weight, how=\"left\", on=[\"city\", \"facility_id\"])\n", "\n", "base_df2[\"weighted_availability_wt\"] = (\n", "    base_df2[\"weighted_availability\"] * base_df2[\"store_weight\"]\n", ")\n", "base_df2[\"cum_weighted_availability_wt\"] = (\n", "    base_df2[\"cum_weighted_availability\"] * base_df2[\"store_weight\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_df2[base_df2.city == \"Ahmedabad\"].tail()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_df = base_df2.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_df1 = (\n", "    city_df.groupby([\"city\", \"assortment_type\", \"date_\", \"hour\"])\n", "    .agg(\n", "        {\n", "            \"availability\": \"mean\",\n", "            \"weighted_availability_wt\": \"sum\",\n", "            \"cum_weighted_availability_wt\": \"sum\",\n", "            \"store_weight\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_df1[\"weighted_availability_wt\"] = (\n", "    city_df1[\"weighted_availability_wt\"] * 1.00 / city_df1[\"store_weight\"]\n", ")\n", "\n", "city_df1[\"cum_weighted_availability_wt\"] = (\n", "    city_df1[\"cum_weighted_availability_wt\"] * 1.00 / city_df1[\"store_weight\"]\n", ")\n", "\n", "city_df1.rename(\n", "    columns={\n", "        \"weighted_availability_wt\": \"weighted_availability\",\n", "        \"cum_weighted_availability_wt\": \"cum_weighted_availability\",\n", "    },\n", "    inplace=True,\n", ")\n", "\n", "city_df1.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_df1 = city_df1.sort_values(\n", "    by=[\"city\", \"assortment_type\", \"date_\", \"hour\"], ascending=[True, True, False, True]\n", ").reset_index()\n", "city_df1.drop(columns=[\"index\", \"store_weight\"], inplace=True)\n", "\n", "city_df1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_df1[[\"date_\", \"hour\"]].drop_duplicates()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Upload to Tables"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Store Level"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_df1.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Main Table\n", "column_dtypes1 = [\n", "    {\"name\": \"city\", \"type\": \"varchar(50)\", \"description\": \"city\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility id\"},\n", "    {\"name\": \"facility_name\", \"type\": \"varchar(100)\", \"description\": \"facility name\"},\n", "    {\n", "        \"name\": \"assortment_type\",\n", "        \"type\": \"varchar(20)\",\n", "        \"description\": \"fnv/perishable/packaged_goods\",\n", "    },\n", "    {\"name\": \"date_\", \"type\": \"timestamp\", \"description\": \"date of snapshot\"},\n", "    {\"name\": \"hour\", \"type\": \"integer\", \"description\": \"hour of snapshot\"},\n", "    {\n", "        \"name\": \"live_sku\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"count of SKUs with positive inventory\",\n", "    },\n", "    {\"name\": \"total_sku\", \"type\": \"integer\", \"description\": \"distinct count of SKUs\"},\n", "    {\"name\": \"availability\", \"type\": \"float\", \"description\": \"live_sku/total_sku\"},\n", "    {\n", "        \"name\": \"weighted_availability\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Weighted availability\",\n", "    },\n", "    {\"name\": \"updated_at\", \"type\": \"timestamp\", \"description\": \"timestamp of run\"},\n", "    {\n", "        \"name\": \"cum_weighted_availability\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Cumulative weighted availability\",\n", "    },\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"store_hourly_weighted_avail_2\",\n", "    \"column_dtypes\": column_dtypes1,\n", "    \"primary_key\": [\"facility_id\", \"assortment_type\", \"date_\", \"hour\"],\n", "    \"sortkey\": [\"facility_id\", \"assortment_type\", \"date_\", \"hour\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This table contains store level hourly weighted availability for all SKUs\",\n", "}\n", "if base_df1.shape[0] > 0:\n", "    pb.to_redshift(base_df1, **kwargs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### City Level"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_df1[\"updated_at\"] = datetime.today() + timedelta(hours=5.5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_df1.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_df1 = city_df1[\n", "    [\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"date_\",\n", "        \"hour\",\n", "        \"availability\",\n", "        \"weighted_availability\",\n", "        \"updated_at\",\n", "        \"cum_weighted_availability\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Main Table\n", "column_dtypes3 = [\n", "    {\"name\": \"city\", \"type\": \"varchar(50)\", \"description\": \"city\"},\n", "    {\n", "        \"name\": \"assortment_type\",\n", "        \"type\": \"varchar(20)\",\n", "        \"description\": \"fnv/perishable/packaged_goods\",\n", "    },\n", "    {\"name\": \"date_\", \"type\": \"timestamp\", \"description\": \"date of snapshot\"},\n", "    {\"name\": \"hour\", \"type\": \"integer\", \"description\": \"hour of snapshot\"},\n", "    {\"name\": \"availability\", \"type\": \"float\", \"description\": \"live_sku/total_sku\"},\n", "    {\n", "        \"name\": \"weighted_availability\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Weighted availability\",\n", "    },\n", "    {\"name\": \"updated_at\", \"type\": \"timestamp\", \"description\": \"timestamp of run\"},\n", "    {\n", "        \"name\": \"cum_weighted_availability\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Cumulative weighted availability\",\n", "    },\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"city_hourly_weighted_availability_2\",\n", "    \"column_dtypes\": column_dtypes3,\n", "    \"primary_key\": [\"city\", \"assortment_type\", \"date_\", \"hour\"],\n", "    \"sortkey\": [\"city\", \"assortment_type\", \"date_\", \"hour\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This table contains city level hourly weighted availability for all SKUs\",\n", "}\n", "if city_df1.shape[0] > 0:\n", "    pb.to_redshift(city_df1, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}