{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import date, datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Prepare base"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    start = time.time()\n", "    df = pd.read_sql_query(sql, con)\n", "    end = time.time()\n", "    if (end - start) > 60:\n", "        print(\"Time: \", (end - start) / 60, \"min\")\n", "    else:\n", "        print(\"Time: \", end - start, \"s\")\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_sql = \"\"\"with outlet_city as (select \n", "pos_outlet_id as outlet_id, facility_id, pos_outlet_city_name as city\n", "from dwh.dim_merchant_outlet_facility_mapping\n", "where valid_to_utc >= current_date\n", "and is_current = True\n", "and is_mapping_enabled = True\n", "group by 1,2,3),\n", "\n", "order_outlet as (SELECT  \n", "    ancestor, \n", "    outlet AS outlet_id\n", "FROM lake_ims.ims_order_details od\n", "LEFT JOIN lake_ims.ims_order_items oi on od.id = oi.order_details_id \n", "LEFT JOIN lake_ims.ims_order_actuals oa on od.id = oa.order_details_id and oi.item_id=oa.item_id\n", "WHERE\n", "od.created_at between current_date-interval '30 days' and current_date-interval '1 days'\n", "GROUP BY 1,2),\n", "\n", "\n", "sales as (\n", "    SELECT city,\n", "        (case when lower(l0_category) = 'vegetables & fruits' and lower(l2_category) !='frozen veg' then 'FnV'\n", "        when lower(l2_category) in ('tofu',\n", "        'fresh milk',\n", "        'lassi & chaach',\n", "        'yogurt',\n", "        'curd',\n", "        'buns, pavs & pizza base',\n", "        'speciality milk',\n", "        'paneer',\n", "        'farm-fresh eggs',\n", "        'batter',\n", "        'high protein & brown eggs',\n", "        'fresh chicken',\n", "        'fresh mutton',\n", "        'fresh fish & seafood',\n", "        'fresh sausage, salami, & ham',\n", "        'brown & multigrain breads',\n", "        'milk & white breads',\n", "        'speciality breads') then 'Perishable'\n", "         else 'Packaged Goods' end) as assortment_type,\n", "        dp.l2_category,\n", "        ipm.item_id,\n", "        date(o.install_ts + interval '5.5 Hours') as order_date,\n", "        extract(hour from (o.install_ts + interval '5.5 Hours')) as order_hour,\n", "        o.id as order_id,\n", "        sum(quantity) as quantity\n", "        \n", "    FROM lake_oms_bifrost.oms_order o\n", "    INNER JOIN lake_oms_bifrost.oms_order_item oi ON oi.order_id=o.id\n", "    \n", "    LEFT JOIN dwh.dim_item_product_offer_mapping ipm ON oi.product_id = ipm.product_id -- AND ipm.active=1\n", "    inner join dwh.dim_product dp on ipm.product_id = dp.product_id and is_current and is_product_enabled\n", "    \n", "    INNER JOIN order_outlet oo on o.id = oo.ancestor\n", "    \n", "    LEFT JOIN outlet_city oc on oo.outlet_id = oc.outlet_id\n", "    \n", "    WHERE date(o.install_ts + interval '5.5 Hours') between current_date-interval '30 days' and current_date-interval '1 days'\n", "    AND o.type='RetailForwardOrder'\n", "    AND o.current_Status <> 'CANCELLED'\n", " \n", "    GROUP BY 1,2,3,4,5,6,7\n", ")\n", "\n", "select a.*, city_carts from\n", "\n", "(select city, assortment_type, l2_category, item_id, order_date, order_hour, count(distinct order_id) as itm_order_cnt\n", "from sales\n", "group by 1,2,3,4,5,6) a\n", "\n", "left join\n", "\n", "(select city, order_date, order_hour, count(distinct order_id) as city_carts\n", "from sales\n", "group by 1,2,3) total_carts on a.city = total_carts.city and a.order_date = total_carts.order_date and a.order_hour = total_carts.order_hour\n", "\n", "-- order by city, item_id, order_date, order_hour\n", "\n", "\"\"\"\n", "\n", "sales = read_sql_query(sales_sql, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales[\"cart_penetration\"] = sales[\"itm_order_cnt\"] / sales[\"city_carts\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_cp = (\n", "    sales.groupby([\"city\", \"item_id\", \"assortment_type\", \"order_hour\"])\n", "    .agg({\"cart_penetration\": \"mean\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_cp.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_cp[(hourly_cp.item_id == 10043867) & (hourly_cp.city == \"Ahmedabad\")]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_cp.assortment_type.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_cp.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cp = hourly_cp[[\"city\", \"assortment_type\", \"item_id\", \"order_hour\", \"cart_penetration\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_city_hour_cp = (\n", "    final_cp.groupby([\"city\", \"order_hour\"])[\"cart_penetration\"].sum().reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_city_hour_cp.rename(columns={\"cart_penetration\": \"tot_cart_penetration\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cp_weights = final_cp.merge(total_city_hour_cp, how=\"left\", on=[\"city\", \"order_hour\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cp_weights[\"weights\"] = (\n", "    final_cp_weights[\"cart_penetration\"] / final_cp_weights[\"tot_cart_penetration\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cp_weights[\n", "    (final_cp_weights.city == \"Gurgaon\") & (final_cp_weights.order_hour == 11)\n", "].weights.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cp_weights.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cp_weights.drop(columns=[\"tot_cart_penetration\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cp_weights[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cp_weights.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cp_weights.cart_penetration = np.round(final_cp_weights.cart_penetration, 5).astype(str)\n", "final_cp_weights.weights = np.round(final_cp_weights.weights, 5).astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"city\", \"type\": \"varchar(50)\", \"description\": \"city\"},\n", "    {\n", "        \"name\": \"assortment_type\",\n", "        \"type\": \"varchar(50)\",\n", "        \"description\": \"fnv/perishable/packaged_goods\",\n", "    },\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item id\"},\n", "    {\"name\": \"order_hour\", \"type\": \"integer\", \"description\": \"hour of data\"},\n", "    {\n", "        \"name\": \"cart_penetration\",\n", "        \"type\": \"varchar(20)\",\n", "        \"description\": \"cart penetration\",\n", "    },\n", "    {\n", "        \"name\": \"weights\",\n", "        \"type\": \"varchar(20)\",\n", "        \"description\": \"normalized cart penetration\",\n", "    },\n", "    {\"name\": \"updated_at\", \"type\": \"timestamp\", \"description\": \"date/time of run\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"city_item_cart_penetration\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"updated_at\", \"city\", \"item_id\", \"order_hour\"],\n", "    \"sortkey\": [\"updated_at\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"append\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This table contains cart penetration on item-city level\",\n", "}\n", "pb.to_redshift(final_cp_weights, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store_weight_sql = \"\"\"with outlet_city as (select \n", "pos_outlet_id as outlet_id, facility_id, pos_outlet_city_name as city\n", "from dwh.dim_merchant_outlet_facility_mapping\n", "where valid_to_utc >= current_date\n", "and is_current = True\n", "and is_mapping_enabled = True\n", "group by 1,2,3),\n", "\n", "order_outlet as (SELECT  \n", "    ancestor, \n", "    outlet AS outlet_id\n", "FROM lake_ims.ims_order_details od\n", "LEFT JOIN lake_ims.ims_order_items oi on od.id = oi.order_details_id \n", "LEFT JOIN lake_ims.ims_order_actuals oa on od.id = oa.order_details_id and oi.item_id=oa.item_id\n", "WHERE\n", "od.created_at between current_date-interval '30 days' and current_date-interval '1 days'\n", "GROUP BY 1,2),\n", "\n", "\n", "sales as (\n", "    SELECT city,\n", "        facility_id,\n", "        oo.outlet_id,\n", "        date(o.install_ts + interval '5.5 Hours') as order_date,\n", "        extract(hour from (o.install_ts + interval '5.5 Hours')) as order_hour,\n", "        o.id as order_id,\n", "        sum(quantity) as quantity\n", "        \n", "    FROM lake_oms_bifrost.oms_order o\n", "    INNER JOIN lake_oms_bifrost.oms_order_item oi ON oi.order_id=o.id\n", "    \n", "    LEFT JOIN dwh.dim_item_product_offer_mapping ipm ON oi.product_id = ipm.product_id -- AND ipm.active=1\n", "    inner join dwh.dim_product dp on ipm.product_id = dp.product_id and is_current and is_product_enabled\n", "    \n", "    INNER JOIN order_outlet oo on o.id = oo.ancestor\n", "    \n", "    LEFT JOIN outlet_city oc on oo.outlet_id = oc.outlet_id\n", "    \n", "    WHERE date(o.install_ts + interval '5.5 Hours') between current_date-interval '30 days' and current_date-interval '1 days'\n", "    AND o.type='RetailForwardOrder'\n", "    AND o.current_Status <> 'CANCELLED'\n", " \n", "    GROUP BY 1,2,3,4,5,6\n", ")\n", "\n", "select a.*, city_carts from\n", "\n", "(select city, facility_id, outlet_id, count(distinct order_id) as outlet_carts\n", "from sales\n", "group by 1,2,3) a\n", "\n", "left join\n", "\n", "(select city, count(distinct order_id) as city_carts\n", "from sales\n", "group by 1) b on a.city = b.city\n", "\n", "where a.city is not null\n", "\"\"\"\n", "\n", "store_weight = read_sql_query(store_weight_sql, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store_weight[\"store_weight\"] = np.round(\n", "    store_weight[\"outlet_carts\"] * 1.00 / store_weight[\"city_carts\"], 5\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store_weight.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store_weight = store_weight[[\"city\", \"facility_id\", \"outlet_id\", \"store_weight\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store_weight.store_weight = store_weight.store_weight.astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store_weight[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store_weight.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"city\", \"type\": \"varchar(50)\", \"description\": \"city\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility id\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"outlet id\"},\n", "    {\n", "        \"name\": \"store_weight\",\n", "        \"type\": \"varchar(20)\",\n", "        \"description\": \"normalized store penetration in city\",\n", "    },\n", "    {\"name\": \"updated_at\", \"type\": \"timestamp\", \"description\": \"date/time of run\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"city_store_penetration\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"updated_at\", \"outlet_id\"],\n", "    \"sortkey\": [\"updated_at\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"append\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This table contains cart penetration on store-city level\",\n", "}\n", "pb.to_redshift(store_weight, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_sql = \"\"\"with outlet_city as (select \n", "pos_outlet_id as outlet_id, facility_id, pos_outlet_city_name as city\n", "from dwh.dim_merchant_outlet_facility_mapping\n", "where valid_to_utc >= current_date\n", "and is_current = True\n", "and is_mapping_enabled = True\n", "group by 1,2,3),\n", "\n", "order_outlet as (SELECT  \n", "    ancestor, \n", "    outlet AS outlet_id\n", "FROM lake_ims.ims_order_details od\n", "LEFT JOIN lake_ims.ims_order_items oi on od.id = oi.order_details_id \n", "LEFT JOIN lake_ims.ims_order_actuals oa on od.id = oa.order_details_id and oi.item_id=oa.item_id\n", "WHERE\n", "od.created_at between current_date-interval '30 days' and current_date-interval '1 days'\n", "GROUP BY 1,2),\n", "\n", "\n", "sales as (\n", "    SELECT city,\n", "        (case when lower(l0_category) = 'vegetables & fruits' and lower(l2_category) !='frozen veg' then 'FnV'\n", "        when lower(l2_category) in ('tofu',\n", "        'fresh milk',\n", "        'lassi & chaach',\n", "        'yogurt',\n", "        'curd',\n", "        'buns, pavs & pizza base',\n", "        'speciality milk',\n", "        'paneer',\n", "        'farm-fresh eggs',\n", "        'batter',\n", "        'high protein & brown eggs',\n", "        'fresh chicken',\n", "        'fresh mutton',\n", "        'fresh fish & seafood',\n", "        'fresh sausage, salami, & ham',\n", "        'brown & multigrain breads',\n", "        'milk & white breads',\n", "        'speciality breads') then 'Perishable'\n", "         else 'Packaged Goods' end) as assortment_type,\n", "        dp.l2_category,\n", "        ipm.item_id,\n", "        date(o.install_ts + interval '5.5 Hours') as order_date,\n", "        extract(hour from (o.install_ts + interval '5.5 Hours')) as order_hour,\n", "        o.id as order_id,\n", "        sum(quantity) as quantity\n", "        \n", "    FROM lake_oms_bifrost.oms_order o\n", "    INNER JOIN lake_oms_bifrost.oms_order_item oi ON oi.order_id=o.id\n", "    \n", "    LEFT JOIN dwh.dim_item_product_offer_mapping ipm ON oi.product_id = ipm.product_id -- AND ipm.active=1\n", "    inner join dwh.dim_product dp on ipm.product_id = dp.product_id and is_current and is_product_enabled\n", "    \n", "    INNER JOIN order_outlet oo on o.id = oo.ancestor\n", "    \n", "    LEFT JOIN outlet_city oc on oo.outlet_id = oc.outlet_id\n", "    \n", "    WHERE date(o.install_ts + interval '5.5 Hours') between current_date-interval '30 days' and current_date-interval '1 days'\n", "    AND o.type='RetailForwardOrder'\n", "    AND o.current_Status <> 'CANCELLED'\n", " \n", "    GROUP BY 1,2,3,4,5,6,7\n", ")\n", "\n", "select a.*, city_carts from\n", "\n", "(select city, order_hour, count(distinct order_id) as order_cnt\n", "from sales\n", "group by 1,2) a\n", "\n", "left join\n", "\n", "(select city, count(distinct order_id) as city_carts\n", "from sales\n", "group by 1) total_carts on a.city = total_carts.city \n", "--and a.order_hour = total_carts.order_hour\n", "\n", "-- order by city, item_id, order_date, order_hour\n", "\n", "\"\"\"\n", "\n", "sales = read_sql_query(sales_sql, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales[\"cart_penetration\"] = sales[\"order_cnt\"] / sales[\"city_carts\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_cp = sales.groupby([\"city\", \"order_hour\"]).agg({\"cart_penetration\": \"mean\"}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_cp[(hourly_cp.city == \"Ahmedabad\")]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_cp[(hourly_cp.city == \"Ahmedabad\")][\"cart_penetration\"].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# hourly_cp.assortment_type.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_cp.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cp = hourly_cp[[\"city\", \"order_hour\", \"cart_penetration\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_city_hour_cp = final_cp.groupby([\"city\"])[\"cart_penetration\"].sum().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_city_hour_cp.rename(columns={\"cart_penetration\": \"tot_cart_penetration\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cp_weights = final_cp.merge(total_city_hour_cp, how=\"left\", on=[\"city\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cp_weights[\"weights\"] = (\n", "    final_cp_weights[\"cart_penetration\"] / final_cp_weights[\"tot_cart_penetration\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cp_weights[(final_cp_weights.city == \"Gurgaon\")].weights.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cp_weights.head(18)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cp_weights.drop(columns=[\"tot_cart_penetration\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cp_weights[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cp_weights.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cp_weights.cart_penetration = np.round(final_cp_weights.cart_penetration, 5).astype(str)\n", "final_cp_weights.weights = np.round(final_cp_weights.weights, 5).astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"city\", \"type\": \"varchar(50)\", \"description\": \"city\"},\n", "    {\"name\": \"order_hour\", \"type\": \"integer\", \"description\": \"hour of data\"},\n", "    {\n", "        \"name\": \"cart_penetration\",\n", "        \"type\": \"varchar(20)\",\n", "        \"description\": \"cart penetration\",\n", "    },\n", "    {\n", "        \"name\": \"weights\",\n", "        \"type\": \"varchar(20)\",\n", "        \"description\": \"normalized cart penetration\",\n", "    },\n", "    {\"name\": \"updated_at\", \"type\": \"timestamp\", \"description\": \"date/time of run\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"city_hour_cart_penetration\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"updated_at\", \"city\", \"order_hour\"],\n", "    \"sortkey\": [\"updated_at\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"append\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This table contains cart penetration of each hour on city level\",\n", "}\n", "pb.to_redshift(final_cp_weights, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}