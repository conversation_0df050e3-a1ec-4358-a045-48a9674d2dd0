{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sqlalchemy as sqla\n", "import pandas\n", "from collections import defaultdict\n", "from datetime import datetime, date as dt\n", "from datetime import timedelta\n", "import numpy as np\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"redshift\")\n", "redshift.schema = \"consumer\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["start_date = \"2019-03-01\"\n", "end_date = \"2019-03-02\"\n", "\n", "print(start_date)\n", "print(end_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"--Item -Outlet Assortment\n", "\n", "WITH dates AS\n", "  (SELECT DISTINCT date(install_ts) AS order_date\n", "                   \n", "   FROM oms_suborder\n", "   WHERE date(install_ts) BETWEEN '{start_date}' AND '{end_date}'),\n", "     --Step 2a:Cross join with product_master_Assortment_log for getting latest substate change\n", "\n", "     substate AS\n", "  (SELECT item_id,\n", "          city_id ,\n", "          created_at,\n", "          order_date,\n", "          new_substate\n", "   FROM product_master_Assortment_log\n", "   JOIN dates ON 1=1),\n", "     --Step 2b:Getting latest substate change for every item\n", "\n", "     item_city_assortment_plan AS\n", "  (SELECT item_id,\n", "          city_id,\n", "          order_date,\n", "          max(created_at)AS recent_assortment\n", "   FROM substate\n", "   WHERE order_date-created_at>=0 \n", "   GROUP BY 1,\n", "            2,\n", "            3),\n", "\n", "     --Step 3:With latest change,getting item-outlet whose substate is 1 which is active\n", "\n", "     substate_id AS\n", "  (SELECT ica.*,date(recent_assortment) as assortment_date,\n", "          new_substate,\n", "          case when new_substate in (1,4) then 'active'\n", "when new_substate in (2) then 'inactive' else null end as active_flag\n", "   FROM item_city_assortment_plan ica\n", "   LEFT JOIN product_master_Assortment_log pma ON ica.item_id=pma.item_id\n", "   AND ica.city_id=pma.city_id\n", "   AND ica.recent_assortment=pma.created_at\n", " )\n", "--  select * from substate_id limit 100\n", "                          \n", "                         ,\n", "                         \n", "substate_assort as\n", "(\n", "select distinct\n", "item_id,\n", "city_id,\n", "order_date,\n", "date(created_at) as recent_assortment,\n", "date(recent_assortment) as assortment_date,\n", "master_assortment_substate_id as new_substate,\n", "case when master_assortment_substate_id in (1,4) then 'active'\n", "when master_assortment_substate_id in (2) then 'inactive' else null end as active_flag\n", "from consumer.product_master_assortment\n", "join dates on 1=1\n", "and order_date-created_at>=0 )\n", "\n", "-- select * from substate_assort limit 100\n", ",\n", "\n", "union_assortment as\n", "(\n", "select a.*\n", "from substate_id as a\n", "union \n", "select b.*\n", "from substate_assort as b\n", ")\n", ",\n", "\n", "\n", "assortment_level as \n", "(\n", "select a.*\n", "from union_assortment as a\n", "inner join\n", "(select item_id,city_id, min(recent_assortment) as min_assortment from union_assortment group by 1,2) as b\n", "on a.item_id=b.item_id\n", "and a.city_id=b.city_id\n", "and a.recent_assortment=b.min_assortment\n", "where active_flag is not null\n", ")\n", "\n", "-- select count(*),count(distinct item_id||city_id||order_date) from assortment_level---939,652\t939,652\n", ",\n", "city_facility as \n", "  (select tax_location_id as city_id,facility_id from pos_console_outlet\n", "    where upper(name) LIKE '%%SSC%%'\n", "        AND upper(name) NOT LIKE '%%AC%%'\n", "        AND upper(name) NOT LIKE '%%HOT%%'\n", "        and active='true'\n", "        group by 1,2)\n", "        ,\n", "        \n", "substate_facility as \n", "                  (select si.*,facility_id from assortment_level si\n", "                  inner join city_facility cf\n", "                  on si.city_id=cf.city_id\n", "                  )\n", "                  ,\n", "\n", "assortment_daily as \n", "                 ( select sf.*,\n", "                 case when date_of_activation is null then sf.assortment_date+21 else  date_of_activation end as date_of_activation \n", "                 from substate_facility sf\n", "                  left join consumer.rpc_assortment_activation aa\n", "                  on sf.city_id=aa.assortment_city_id\n", "                  and sf.facility_id=aa.facility_id\n", "                  and sf.item_id=aa.ASSORTMENT_ITEM_ID \n", "                  and sf.assortment_date=aa.assortment_date\n", "                  and sf.new_substate=aa.new_substate)\n", "                  \n", "                --   select count(*),count(distinct item_id||facility_id||order_date) from assortment_daily--1,442,092\t1,442,092\n", "                \n", "            select * from assortment_daily\"\"\".format(\n", "    start_date=start_date, end_date=end_date\n", ")\n", "query"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment = pandas.read_sql_query(sql=query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query_inv = \"\"\"\n", "SELECT item_id,\n", "             date(snapshot_datetime) AS assortment_date,\n", "             outlet_id,\n", "             actual_quantity,\n", "             blocked_quantity,\n", "             facility_id,\n", "             tax_location_id,\n", "\n", "          CASE\n", "              WHEN actual_quantity-blocked_quantity >0 THEN 'live'\n", "              ELSE'not live'\n", "          END AS availability\n", "\n", "      FROM reports.reports_item_inventory_snapshot riis\n", "      left join retail.console_outlet co\n", "      on riis.outlet_id=co.id\n", "      where date(snapshot_datetime) between '{start_date}' and '{end_date}'\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_values = pandas.read_sql_query(query_inv, con=ars)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory = (\n", "    inv_values.groupby([\"item_id\", \"facility_id\", \"assortment_date\", \"tax_location_id\"])[\n", "        \"actual_quantity\", \"blocked_quantity\"\n", "    ]\n", "    .sum()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory[\"availability\"] = np.where(\n", "    inventory.actual_quantity - inventory.blocked_quantity > 0, \"live\", \"not live\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory.item_id = inventory.item_id.astype(int)\n", "inventory.facility_id = inventory.facility_id.astype(float)\n", "inventory.tax_location_id = inventory.tax_location_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_merge = assortment.merge(\n", "    inventory,\n", "    left_on=[\"item_id\", \"facility_id\", \"city_id\", \"order_date\"],\n", "    right_on=[\"item_id\", \"facility_id\", \"tax_location_id\", \"assortment_date\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_merge[\"availability\"] = np.where(\n", "    availability_merge.availability.isna(), \"not live\", availability_merge.availability\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_facility_names = \"\"\"select tax_location_id as city_id,pcl.name as city_name,facility_id ,cf.name as facility_name from pos_console_outlet pco\n", "left join pos_console_location pcl\n", "on pco.tax_location_id=pcl.id\n", "left join crates_facility cf\n", "on pco.facility_id=cf.id\n", "where upper(pco.name) LIKE '%%SSC%%'\n", "        AND upper(pco.name) NOT LIKE '%%AC%%'\n", "        AND upper(pco.name) NOT LIKE '%%HOT%%' \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_facility_mapping = pandas.read_sql_query(sql=city_facility_names, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["avail_naming = availability_merge.merge(\n", "    city_facility_mapping, on=[\"city_id\", \"facility_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["avail_naming = avail_naming.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["avail_naming.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["avail_naming = avail_naming[\n", "    [\n", "        \"item_id\",\n", "        \"city_id\",\n", "        \"order_date\",\n", "        \"recent_assortment\",\n", "        \"assortment_date_x\",\n", "        \"new_substate\",\n", "        \"facility_id\",\n", "        \"date_of_activation\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"availability\",\n", "        \"city_name\",\n", "        \"facility_name\",\n", "        \"active_flag\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["avail_naming = avail_naming.rename(columns={\"assortment_date_x\": \"assortment_date\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["avail_naming[\"flag\"] = (avail_naming.active_flag == \"inactive\") & (\n", "    avail_naming.availability == \"not live\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_live = avail_naming[avail_naming.flag == False]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability = active_live[~active_live.active_flag.isna()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability = availability.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_query = \"\"\"\n", "WITH item_detail AS\n", "  (SELECT DISTINCT pp.item_id,\n", "                   CAT.NAME AS l0,\n", "                   CAT.ID AS l0_ID,\n", "                   CAT1.NAME AS l1,\n", "                   CAT1.ID AS l1_id,\n", "                   CAT2.NAME AS l2,\n", "                   CAT2.ID AS l2_ID,\n", "                   p.type AS product_type,\n", "                   p.type_id AS p_type,\n", "                   pb.name AS brand,\n", "                   pb.id AS brand_id,\n", "                   pm.id AS manufacturer_id,\n", "                   pm.name AS manufacture,\n", "                   mpm.merchant_id,\n", "                   pp.is_pl --P.ENABLED_FLAG\n", "FROM GR_PRODUCT P\n", "   INNER JOIN GR_PRODUCT_CATEGORY_MAPPING PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN GR_CATEGORY CAT2 ON PCM.CATEGORY_ID = CAT2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN GR_CATEGORY CAT1 ON CAT2.PARENT_CATEGORY_ID = CAT1.ID\n", "   INNER JOIN GR_CATEGORY CAT ON CAT1.PARENT_CATEGORY_ID = CAT.ID\n", "   INNER JOIN consumer.rpc_item_product_mapping ipm ON ipm.product_id=p.id\n", "   AND ipm.item_id IS NOT NULL\n", "   INNER JOIN gr_merchant_product_mapping mpm ON p.id=mpm.product_id\n", "   LEFT JOIN pos_product_product pp ON pp.item_id=ipm.item_id\n", "   LEFT JOIN rpc_product_brand pb ON pb.id=pp.brand_id\n", "   AND pb.active='true'\n", "   LEFT JOIN rpc_product_manufacturer pm ON pm.id=pb.manufacturer_id\n", "   AND pm.active='true'\n", "   WHERE pp.active =1\n", "     AND pp.approved=1) ,\n", "     final_item AS\n", "  (SELECT a.*\n", "   FROM item_detail AS a\n", "   INNER JOIN\n", "     (SELECT item_id,\n", "             min(brand_id) AS brand_id\n", "      FROM item_detail\n", "      GROUP BY 1) AS b ON a.item_id=b.item_id\n", "   AND a.brand_id=b.brand_id) -- select count(*),count(distinct item_id||merchant_id) from final_item\n", "\n", "                             ,\n", "                             merchant_mapping AS\n", "  (SELECT frontend.id frontend_id,\n", "                      frontend.name frontend_name,\n", "                                    backend.id backend_id,\n", "                                               backend.name backend_name\n", "   FROM gr_merchant frontend\n", "   JOIN gr_virtual_to_real_merchant_mapping vmm ON frontend.id=vmm.virtual_merchant_id\n", "   JOIN gr_merchant backend ON backend.id=vmm.real_merchant_id\n", "   WHERE frontend.enabled_flag ='true'\n", "     AND vmm.enabled_flag='true') ,\n", "     \n", "   final_data AS                          \n", "  (SELECT DISTINCT a.*\n", "   FROM final_item a\n", "   INNER JOIN merchant_mapping m ON m.backend_id=a.merchant_id),\n", "                             \n", "                             item_city AS\n", "  (SELECT DISTINCT mpm.item_id,\n", "                   l0,\n", "                   l0_ID,\n", "                   is_pl,\n", "                   pos.facility_id as facility_id\n", "   FROM final_data mpm\n", "   INNER JOIN Rt_console_outlet_cms_store rt ON mpm.merchant_id=rt.cms_store\n", "   INNER JOIN Pos_console_outlet pos ON rt.outlet_id=pos.id\n", "   INNER JOIN pos_console_location pcl ON pos.tax_location_id=pcl.id\n", "   WHERE rt.Active='true'\n", "     AND pos.Active='true'\n", " )\n", " \n", "select * from item_city \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_pl_detail = pandas.read_sql_query(sql=item_query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability[\"order_date\"] = pandas.to_datetime(availability[\"order_date\"])\n", "availability[\"month\"] = availability[\"order_date\"].dt.month"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["avail_item = availability.merge(item_pl_detail, on=[\"item_id\", \"facility_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["avail_item.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_bucket_query = \"\"\"\n", "select distinct \n", "case when city like '%%Jaipur%%' then 15\n", "when city like '%%K 2%%' then 34 \n", "when city like '%%G 4%%' then 29\n", "when city like '%%Bengaluru%%'then 3\n", "when city like '%%N 2%%' then 31\n", "when city like '%%Ahmedabad%%' then 1\n", "when city like '%%P3%%' then 11\n", "when city like '%%Noida%%' then 21\n", "when city like '%%B 2%%' then 32\n", "when city like '%%Lucknow%%' then 30\n", "when city like '%%Chennai%%' then 4\n", "when city like '%%Faridabad%%' then 33\n", "when city like '%%PL_Bulk%%' then 35\n", "when city like '%%G 2%%' then 13\n", "when city like '%%Pune%%' then 22\n", "when city like '%%Hyderabad%%' then 14\n", "when city like '%%G 3%%' then 26\n", "when city like '%%Kolkata%%' then 24\n", "when city like '%%Delhi%%' then 9\n", "when city like '%%Gurgaon%%' then 12\n", "when city like '%%Mumbai%%' then 18\n", "when city like '%%Sonepath%%' then 39\n", "when city like '%%hyderabad%%' then 14\n", "when city like '%%P1%%' then 5\n", "end as Facility_id,\n", "item_id,\n", "split_part(bucket,' ',2) as bucket,\n", "null as bucket_x,\n", "extract(month from date) as month\n", "from consumer.item_bucket_mapping\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_bucket = pandas.read_sql_query(item_bucket_query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["avail_item.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_bucket.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_item = avail_item.merge(\n", "    item_bucket, on=[\"item_id\", \"facility_id\", \"month\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_item.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_item.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_item.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# item_id                        int64\n", "# city_id                        int64\n", "# order_date            datetime64[ns]\n", "# recent_assortment     datetime64[ns]\n", "# assortment_date               object\n", "# new_substate                   int64\n", "# facility_id                    int64\n", "# date_of_activation    datetime64[ns]\n", "# actual_quantity              float64\n", "# blocked_quantity             float64\n", "# availability                  object\n", "# city_name                     object\n", "# facility_name                 object\n", "# active_flag                   object\n", "# flag                            bool\n", "# month                          int64\n", "# l0                            object\n", "# l0_id                        float64\n", "# is_pl                         object\n", "# bucket                        object\n", "# bucket_x                      object\n", "\n", "column_dtypes = [\n", "    {\"name\": \"item_id\", \"type\": \"integer\"},\n", "    {\"name\": \"city_id\", \"type\": \"integer\"},\n", "    {\"name\": \"order_date\", \"type\": \"timestamp\"},\n", "    {\"name\": \"recent_assortment\", \"type\": \"timestamp\"},\n", "    {\"name\": \"assortment_date\", \"type\": \"timestamp\"},\n", "    {\"name\": \"new_substate\", \"type\": \"smallint\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\"},\n", "    {\"name\": \"date_of_activation\", \"type\": \"timestamp\"},\n", "    {\"name\": \"actual_quantity\", \"type\": \"float\"},\n", "    {\"name\": \"blocked_quantity\", \"type\": \"float\"},\n", "    {\"name\": \"availability\", \"type\": \"varchar(30)\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar(500)\"},\n", "    {\"name\": \"facility_name\", \"type\": \"varchar(500)\"},\n", "    {\"name\": \"active_flag\", \"type\": \"varchar\"},\n", "    {\"name\": \"flag\", \"type\": \"boolean\"},\n", "    {\"name\": \"month\", \"type\": \"integer\"},\n", "    {\"name\": \"l0\", \"type\": \"varchar(500)\"},\n", "    {\"name\": \"l0_id\", \"type\": \"float\"},\n", "    {\"name\": \"is_pl\", \"type\": \"varchar\"},\n", "    {\"name\": \"bucket\", \"type\": \"varchar\"},\n", "    {\"name\": \"bucket_x\", \"type\": \"varchar\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"consumer\",\n", "    \"table_name\": \"rpc_daily_availability\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"item_id\", \"facility_id\", \"order_date\"],\n", "    \"sortkey\": [\"order_date\", \"item_id\", \"facility_id\"],\n", "    \"incremental_key\": \"order_date\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "}\n", "\n", "pb.to_redshift(availability_item, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"celltoolbar": "Tags", "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.5.2"}}, "nbformat": 4, "nbformat_minor": 2}