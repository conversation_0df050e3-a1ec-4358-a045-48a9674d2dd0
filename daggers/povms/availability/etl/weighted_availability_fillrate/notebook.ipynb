{"cells": [{"cell_type": "code", "execution_count": null, "id": "9b4b7143-240b-40ad-97e8-b758108d295f", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "import json\n", "import shutil\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "\n", "import boto3\n", "import io\n", "\n", "!pip install matplotlib\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "!pip install pandasql\n", "import pandasql as ps\n", "\n", "\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "# CON_IMS = pb.get_connection(\"[Replica] RDS IMS\")\n", "# CON_PO = pb.get_connection(\"[Replica] RDS PO\")\n", "\n", "# secrets = pb.get_secret(\"retail/po/db/po.db.credential\")\n", "# host_name = secrets.get(\"host\")\n", "# user_name = secrets.get(\"db_user\")\n", "# password = secrets.get(\"db_password\")\n", "# CON_PO = pymysql.connect(\n", "#     host=host_name, user=user_name, password=password, autocommit=True, local_infile=1\n", "# )\n", "\n", "# rpc_secret = pb.get_secret(\"retail/noto-reports/mysql/rpc-rds-read\")\n", "# host = rpc_secret.get(\"host\")\n", "# username = rpc_secret.get(\"username\")\n", "# password = rpc_secret.get(\"password\")\n", "# CON_RPC = pymysql.connect(host=host, user=username, password=password)\n", "\n", "\n", "start_time = time.time()"]}, {"cell_type": "code", "execution_count": null, "id": "a2ecd3a6-11a4-4174-b71b-4b6c60e2b5b4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "111f0c4f-f87b-4543-965e-639a9411565c", "metadata": {"tags": []}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "dd1835de-c581-42cd-b419-6b493<PERSON>beec0", "metadata": {"tags": []}, "outputs": [], "source": ["def sqldf(sql, con):\n", "    start = time.time()\n", "    df = ps.sqldf(sql, con)\n", "    end = time.time()\n", "    if (end - start) > 60:\n", "        print(\"Time: \", (end - start) / 60, \"min\")\n", "    else:\n", "        print(\"Time: \", end - start, \"s\")\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "8a0bafbc-f50d-4d85-b54d-87f48549a094", "metadata": {}, "outputs": [], "source": ["pd.options.mode.chained_assignment = None  # default='warn'"]}, {"cell_type": "code", "execution_count": null, "id": "c0195248-78cc-4c9f-88ba-f601fa0bd445", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "8cd62b83-9292-45f4-8a1b-f578df63880b", "metadata": {}, "source": ["### Store-Cluster Mapping"]}, {"cell_type": "code", "execution_count": null, "id": "e778d3d8-7cb8-4bea-a2f6-7b1aed0e8218", "metadata": {}, "outputs": [], "source": ["store_cluster_sql = f\"\"\"\n", "    \n", "    select ssc_id, case  when is_hp = 1 then cluster||'_HP' else cluster end as cluster\n", "    from (\n", "        select a.ssc_id, a.cluster, a.is_hp\n", "        from consumer.dark_stores_fnv_assortment a\n", "        join (\n", "            select ssc_id, max(updated_at_ist) midd\n", "            from consumer.dark_stores_fnv_assortment\n", "            where date_ist between current_date - 30 and current_date\n", "            group by 1\n", "            ) b on a.updated_at_ist = b.midd and a.ssc_id = b.ssc_id\n", "        group by 1,2,3\n", "        )\n", "    where cluster is not null or cluster <>''\n", "    group by 1,2\n", "\n", "    \"\"\"\n", "\n", "store_cluster_df = read_sql_query(store_cluster_sql, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "72cfe89c-5939-4c39-a0cd-34c666f05754", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "2b9f1b7e-c0cb-4f22-bcf8-918b4f76b709", "metadata": {}, "source": ["### Clean Outlet-City Mapping"]}, {"cell_type": "code", "execution_count": null, "id": "1fa0df62-45d3-419b-9bdf-a05a1e46e819", "metadata": {}, "outputs": [], "source": ["outlet_city_sql = f\"\"\"\n", "\n", "    select \n", "        id as outlet_id, \n", "        CASE\n", "        WHEN trim(lower(a.location)) in ('gurgaon','hrncr','hr-ncr') then 'gurgaon'  \n", "        WHEN trim(lower(a.location)) in ('upncr','up-ncr') then 'noida' \n", "        WHEN trim(lower(a.location)) in ('bangalore') then 'bengaluru'  \n", "        WHEN trim(lower(a.location)) in ('kanpur') then 'kanpur' \n", "        WHEN trim(lower(a.location)) in ('zirakpur') then 'zirakpur'   \n", "        WHEN trim(lower(a.location)) in ('faridabad') then 'faridabad' \n", "        else trim(lower(a.location))\n", "        end as city\n", "    from \n", "        lake_retail.console_outlet a\n", "    group by 1,2\n", "    \n", "    \"\"\"\n", "\n", "outlet_city_df = read_sql_query(outlet_city_sql, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "4a73f894-0d0b-43ff-9317-d18b46784ba2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "5ff4e9d6-536b-483d-8660-c70b649d2b81", "metadata": {}, "source": ["### Fn<PERSON>"]}, {"cell_type": "code", "execution_count": null, "id": "e27bf232-8b68-44b6-b8e7-fe6c67712ddc", "metadata": {}, "outputs": [], "source": ["fnv_assortment_sql = f\"\"\"\n", "\n", "    select date_, case when is_hp = 1 then cluster + '_HP' else cluster end as cluster, item_id, outlet_id\n", "    from (\n", "        select date(fa.date_ist) as date_, fa.cluster, fa.is_hp, fa.item_id, fa.ssc_id as outlet_id\n", "        from consumer.dark_stores_fnv_assortment fa\n", "        where fa.date_ist between current_date - interval '7 day' and current_date\n", "        group by 1,2,3,4,5\n", "        )\n", "    group by 1,2,3,4\n", "    order by 1 desc\n", "\n", "    \"\"\"\n", "\n", "fnv_assortment_df = read_sql_query(fnv_assortment_sql, CON_REDSHIFT)\n", "fnv_assortment_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "049cb8a8-7884-4ba5-b297-85c33cfc5e1d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "6682c3b4-9374-4ecf-8816-9d656b36146a", "metadata": {}, "source": ["### T-n <PERSON><PERSON>or <PERSON>ll-Rates"]}, {"cell_type": "code", "execution_count": null, "id": "9cfbcc0e-a828-42c6-a238-4d9b7b60d598", "metadata": {}, "outputs": [], "source": ["vendor_fillrate_sql = f\"\"\"\n", "    with base as\n", "    (\n", "        select distinct\n", "            poi.item_id as item_id, icd.name as item_name, poi.upc, p.outlet_id, o.name as outlet_name, p.vendor_name, o.facility_id,\n", "            p.id po_id,\n", "            p.po_number,\n", " \n", "                    \n", "            case when (ps.schedule_date_time + interval '330' minute) is null and extract(hour from issue_date + interval '330' minute) between 19 and 23\n", "                        then date(issue_date + interval '330' minute + interval '0' day) \n", "                when (ps.schedule_date_time + interval '330' minute) is null and extract(hour from issue_date + interval '330' minute) between 0 and 18\n", "                        then date(issue_date + interval '330' minute + interval '1' day) \n", "                when extract(hour from ps.schedule_date_time + interval '330' minute) between 15 AND 23\n", "                    then date(ps.schedule_date_time + interval '330' minute)\n", "                else date(ps.schedule_date_time + interval '330' minute - interval '1' day) end as date_,\n", "            units_ordered as total_po,\n", "            grn.quan as total_grn\n", "        from \n", "            po.purchase_order p\n", "        left join\n", "            po.po_schedule ps on p.id=ps.po_id_id\n", "        inner join\n", "            po.purchase_order_items poi on p.id=poi.po_id\n", "            and p.issue_date >= current_date - interval '9' day\n", "        inner join\n", "            retail.console_outlet o on o.id=p.outlet_id\n", "        inner join\n", "            po.purchase_order_status posa on posa.po_id = p.id\n", "        inner join\n", "            po.purchase_order_state posta on posta.id = posa.po_state_id\n", "        inner join \n", "                rpc.item_category_details icd on icd.item_id = poi.item_id and icd.l0_id in (1487)\n", "        left join\n", "                (select po_id, item_id, sum(quantity) quan from po.po_grn grn where insert_ds_ist >= cast(current_date - interval '10' day as varchar) group by 1,2) grn\n", "            on \n", "                grn.item_id = poi.item_id \n", "                and grn.po_id = p.id\n", "        where \n", "            (posta.name not in ('Cancelled', 'Rejected', 'Cancelled post Creation') or (posta.name in ('Expired') and grn.quan is not null))\n", "            \n", "    )\n", "    \n", "    select date_, outlet_id, outlet_name, business_type_id, item_id, item_name, sum(po_qty) as total_po_qty, sum(grn_qty) as total_grn_qty from \n", "    (select b.date_ + interval '1' day as date_, b.outlet_id, b.outlet_name, wom.cloud_store_id, co.business_type_id, b.item_id, b.item_name, sum(total_po) as po_qty, sum(total_grn) as grn_qty from base b\n", "    left join retail.warehouse_outlet_mapping wom on wom.warehouse_id = b.outlet_id\n", "    left join retail.console_outlet co on co.id = b.outlet_id\n", "    group by 1,2,3,4,5,6,7\n", "    order by date_ desc)\n", "    where date_ <= current_date - interval '0' day\n", "    group by 1,2,3,4,5,6\n", "    order by 1 desc,2\n", "    \"\"\"\n", "\n", "vendor_fillrate_df = read_sql_query(vendor_fillrate_sql, CON_TRINO)\n", "vendor_fillrate_df[\"total_grn_qty\"] = vendor_fillrate_df[\"total_grn_qty\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "cb923ea3-5623-47a5-8a1b-54ab28621dd5", "metadata": {}, "outputs": [], "source": ["## <PERSON><PERSON>d\n", "vendor_fillrate_df_bk = (\n", "    vendor_fillrate_df[vendor_fillrate_df[\"business_type_id\"] != 7]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "## Frontend\n", "vendor_fillrate_df_fk = (\n", "    vendor_fillrate_df[vendor_fillrate_df[\"business_type_id\"] == 7]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "vendor_fillrate_df_fk = pd.merge(\n", "    vendor_fillrate_df_fk,\n", "    store_cluster_df.rename(columns={\"ssc_id\": \"outlet_id\"}),\n", "    on=[\"outlet_id\"],\n", "    how=\"inner\",\n", ")\n", "vendor_fillrate_df_fk = (\n", "    vendor_fillrate_df_fk[vendor_fillrate_df_fk[\"cluster\"].str.contains(\"HP\")]\n", "    .reset_index()\n", "    .drop(columns={\"index\", \"cluster\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b9869f1a-25a3-446f-a3dc-43cf2082dde7", "metadata": {}, "outputs": [], "source": ["vendor_fillrate_df_bk.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "d65e6423-dd78-4992-a6ec-356034b6ac0f", "metadata": {}, "outputs": [], "source": ["vendor_fillrate_df_fk.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "50b329ac-337a-4725-8479-f7e1eb0ec4fd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "12cc8d42-23cd-4f9f-94f2-b3da65576b74", "metadata": {}, "source": ["### TEA Tagging"]}, {"cell_type": "code", "execution_count": null, "id": "a53967d0-912c-4b1a-a821-962a17d8cdff", "metadata": {}, "outputs": [], "source": ["tea_tagging_sql = f\"\"\"\n", "\n", "    select iotm.item_id, iotm.outlet_id, iotm.tag_value as backend_outlet_id\n", "    from rpc.item_outlet_tag_mapping iotm\n", "    inner join rpc.item_category_details icd on icd.item_id = iotm.item_id and icd.l0_id in (1487)\n", "    where iotm.active = 1 and iotm.tag_type_id = 8\n", "    \n", "    \"\"\"\n", "\n", "tea_tagging_df = read_sql_query(tea_tagging_sql, CON_TRINO)\n", "tea_tagging_df[\"backend_outlet_id\"] = tea_tagging_df[\"backend_outlet_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "20245bad-bebd-450d-b972-d4e61184fc2d", "metadata": {}, "outputs": [], "source": ["# tea_tagging_df.dtypes"]}, {"cell_type": "markdown", "id": "a64fcff1-c8f1-4f49-bf21-345c05dc68a1", "metadata": {}, "source": ["### VFR Cleaning"]}, {"cell_type": "code", "execution_count": null, "id": "b279f41a-73a7-402f-ab65-61fb44616311", "metadata": {}, "outputs": [], "source": ["fill_rate_threshold = pb.from_sheets(\n", "    \"1SuXf5kzWniOdLSl0Br-vxJc18QS4Yww-ZZT5WVozSRg\", \"Fill-Rate Threshold\"\n", ")\n", "fill_rate_threshold_val = float(fill_rate_threshold.iloc[:, 0][0])\n", "fill_rate_threshold_val"]}, {"cell_type": "code", "execution_count": null, "id": "3ce41dc5-8c55-4f84-94e3-1878e39d663a", "metadata": {}, "outputs": [], "source": ["# tea_tagging_df[(tea_tagging_df['backend_outlet_id'] == 1279) & (tea_tagging_df['item_id'] == 10024765)]"]}, {"cell_type": "code", "execution_count": null, "id": "5a479ae5-d42f-432f-acde-a3a3bb2519db", "metadata": {}, "outputs": [], "source": ["## Backend\n", "\n", "vendor_fillrate_df_bk_mapping = pd.merge(\n", "    vendor_fillrate_df_bk,\n", "    tea_tagging_df.rename(\n", "        columns={\"outlet_id\": \"fe_outlet_id\", \"backend_outlet_id\": \"outlet_id\"}\n", "    ),\n", "    on=[\"outlet_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "vendor_fillrate_df_bk_mapping[\"fill_rate\"] = (\n", "    vendor_fillrate_df_bk_mapping[\"total_grn_qty\"]\n", "    / vendor_fillrate_df_bk_mapping[\"total_po_qty\"]\n", ")\n", "vendor_fillrate_df_bk_mapping[\"fill_rate_flag\"] = np.where(\n", "    vendor_fillrate_df_bk_mapping[\"fill_rate\"] <= fill_rate_threshold_val, 1, 0\n", ")\n", "\n", "\n", "vendor_fillrate_df_bk_mapping_flag = (\n", "    vendor_fillrate_df_bk_mapping[vendor_fillrate_df_bk_mapping[\"fill_rate_flag\"] == 1]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "vendor_fillrate_df_bk_mapping_flag = (\n", "    vendor_fillrate_df_bk_mapping_flag.drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "# [['date_','item_id','fe_outlet_id','fill_rate_flag']].rename(columns = {'fe_outlet_id':'outlet_id'})\n", "vendor_fillrate_df_bk_mapping_flag = (\n", "    vendor_fillrate_df_bk_mapping_flag[\n", "        ~(vendor_fillrate_df_bk_mapping_flag[\"fe_outlet_id\"].isna())\n", "    ]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "vendor_fillrate_df_bk_mapping_flag\n", "\n", "\n", "# vendor_fillrate_df_bk_mapping_flag[vendor_fillrate_df_bk_mapping_flag['outlet_id'].isna()]"]}, {"cell_type": "code", "execution_count": null, "id": "c2f55771-52be-4485-ac21-88a939788da7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b9cea0e9-736e-417e-a7d0-9e7568335104", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "17bc2a3f-392f-41fb-86e0-2bb20a092e6a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c2ce5254-c53e-406c-8063-5e59c8001009", "metadata": {}, "outputs": [], "source": ["## Frontend\n", "vendor_fillrate_df_fk_mapping = vendor_fillrate_df_fk.copy()\n", "vendor_fillrate_df_fk_mapping[\"fill_rate\"] = (\n", "    vendor_fillrate_df_fk_mapping[\"total_grn_qty\"]\n", "    / vendor_fillrate_df_fk_mapping[\"total_po_qty\"]\n", ")\n", "vendor_fillrate_df_fk_mapping[\"fill_rate_flag\"] = np.where(\n", "    vendor_fillrate_df_fk_mapping[\"fill_rate\"] <= fill_rate_threshold_val, 1, 0\n", ")\n", "\n", "\n", "vendor_fillrate_df_fk_mapping_flag = (\n", "    vendor_fillrate_df_fk_mapping[vendor_fillrate_df_fk_mapping[\"fill_rate_flag\"] == 1]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "# vendor_fillrate_df_fk_mapping_flag = vendor_fillrate_df_fk_mapping_flag[['date_','item_id','outlet_id','fill_rate_flag']].drop_duplicates().reset_index().drop(columns = {'index'})\n", "vendor_fillrate_df_fk_mapping_flag"]}, {"cell_type": "code", "execution_count": null, "id": "adb76b1c-8080-44ce-9075-feb8750d7f11", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "79388413-de48-4e7d-9ba7-debc2eaf2cac", "metadata": {}, "outputs": [], "source": ["to_be_removed_assortment = (\n", "    pd.concat(\n", "        [\n", "            vendor_fillrate_df_bk_mapping_flag[\n", "                [\"date_\", \"item_id\", \"fe_outlet_id\", \"fill_rate_flag\"]\n", "            ]\n", "            .drop_duplicates()\n", "            .rename(columns={\"fe_outlet_id\": \"outlet_id\"}),\n", "            vendor_fillrate_df_fk_mapping_flag[\n", "                [\"date_\", \"item_id\", \"outlet_id\", \"fill_rate_flag\"]\n", "            ].drop_duplicates(),\n", "        ]\n", "    )\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "to_be_removed_assortment[\"outlet_id\"] = to_be_removed_assortment[\"outlet_id\"].astype(\n", "    int\n", ")\n", "to_be_removed_assortment"]}, {"cell_type": "code", "execution_count": null, "id": "cc7b9bd1-e156-4dee-9c7c-7910746c0cb7", "metadata": {}, "outputs": [], "source": ["# fnv_assortment_df[(fnv_assortment_df['outlet_id'] == 1477) & (fnv_assortment_df['item_id'] == 10076448) & (pd.to_datetime(fnv_assortment_df['date_']) == '2022-07-22')]"]}, {"cell_type": "code", "execution_count": null, "id": "4a8d6423-032b-4bb1-879b-eb436d399929", "metadata": {}, "outputs": [], "source": ["## Item Name\n", "item_name_sql = f\"\"\"\n", "    SELECT item_id, name as item_name FROM rpc.item_category_details icd\n", "    GROUP BY 1,2\n", "    \"\"\"\n", "item_name_df = read_sql_query(item_name_sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "689c49c0-4e5a-4aa2-a663-5b857e36bb95", "metadata": {}, "outputs": [], "source": ["to_be_removed_assortment_city = (\n", "    pd.concat(\n", "        [\n", "            vendor_fillrate_df_bk_mapping_flag[\n", "                [\"date_\", \"item_id\", \"fe_outlet_id\", \"total_po_qty\", \"total_grn_qty\"]\n", "            ]\n", "            .drop_duplicates()\n", "            .rename(columns={\"fe_outlet_id\": \"outlet_id\"}),\n", "            vendor_fillrate_df_fk_mapping_flag[\n", "                [\"date_\", \"item_id\", \"outlet_id\", \"total_po_qty\", \"total_grn_qty\"]\n", "            ].drop_duplicates(),\n", "        ]\n", "    )\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "to_be_removed_assortment_city = pd.merge(\n", "    to_be_removed_assortment_city, outlet_city_df, on=[\"outlet_id\"], how=\"left\"\n", ")\n", "to_be_removed_assortment_city = (\n", "    to_be_removed_assortment_city[\n", "        [\"date_\", \"city\", \"item_id\", \"total_po_qty\", \"total_grn_qty\"]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "to_be_removed_assortment_city = pd.merge(\n", "    to_be_removed_assortment_city, item_name_df, on=[\"item_id\"], how=\"left\"\n", ")\n", "to_be_removed_assortment_city = (\n", "    to_be_removed_assortment_city.groupby([\"date_\", \"city\", \"item_id\", \"item_name\"])\n", "    .agg({\"total_po_qty\": \"sum\", \"total_grn_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "to_be_removed_assortment_city[\"fill_rate\"] = (\n", "    to_be_removed_assortment_city[\"total_grn_qty\"]\n", "    / to_be_removed_assortment_city[\"total_po_qty\"]\n", ")\n", "to_be_removed_assortment_city = to_be_removed_assortment_city.drop(\n", "    columns={\"total_po_qty\", \"total_grn_qty\"}\n", ")\n", "\n", "to_be_removed_assortment_city"]}, {"cell_type": "code", "execution_count": null, "id": "0d4b032a-533d-4ccb-b26d-78d8f0afbca9", "metadata": {}, "outputs": [], "source": ["to_be_removed_assortment.dtypes"]}, {"cell_type": "markdown", "id": "a59279a3-6c0d-4d3e-bb63-4d08352cf956", "metadata": {}, "source": ["### Clean Assortment"]}, {"cell_type": "code", "execution_count": null, "id": "0266fa45-d594-4a44-92df-8218b5270ae0", "metadata": {}, "outputs": [], "source": ["fnv_assortment_df[\"date_\"] = pd.to_datetime(fnv_assortment_df[\"date_\"])\n", "to_be_removed_assortment[\"date_\"] = pd.to_datetime(to_be_removed_assortment[\"date_\"])\n", "\n", "\n", "fnv_clean_assortment_df = pd.merge(\n", "    fnv_assortment_df,\n", "    to_be_removed_assortment,\n", "    on=[\"date_\", \"item_id\", \"outlet_id\"],\n", "    how=\"left\",\n", ")\n", "fnv_clean_assortment_df = (\n", "    fnv_clean_assortment_df[fnv_clean_assortment_df[\"fill_rate_flag\"] != 1]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "fnv_clean_assortment_df = fnv_clean_assortment_df[[\"date_\", \"item_id\", \"outlet_id\"]]\n", "fnv_clean_assortment_df[\"flag\"] = 1\n", "fnv_clean_assortment_df"]}, {"cell_type": "code", "execution_count": null, "id": "3673c2fd-07c6-4ca2-b91e-c2d8b7224bcc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "c5927088-9224-4d62-a71f-ddc584983c73", "metadata": {}, "source": ["### Availability Calculation"]}, {"cell_type": "code", "execution_count": null, "id": "273c81b1-8f21-4279-b361-16e8a3732a8a", "metadata": {}, "outputs": [], "source": ["availability_sql = f\"\"\"\n", "\n", "    select\n", "        date(m.updated_at) date_,\n", "        extract(hour from m.updated_at) as hour_,\n", "        case \n", "        when l0_id in (1487) then 'FnV'\n", "        else 'others' end as product_cat,\n", "        CASE\n", "        WHEN trim(lower(co.location)) in ('gurgaon','hrncr','hr-ncr') then 'gurgaon'  \n", "        WHEN trim(lower(co.location)) in ('upncr','up-ncr') then 'noida' \n", "        WHEN trim(lower(co.location)) in ('bangalore') then 'bengaluru'  \n", "        WHEN trim(lower(co.location)) in ('kanpur') then 'kanpur' \n", "        WHEN trim(lower(co.location)) in ('zirakpur') then 'zirakpur'   \n", "        WHEN trim(lower(co.location)) in ('faridabad') then 'faridabad' \n", "        else trim(lower(co.location))\n", "        end as city,\n", "        m.outlet_id,\n", "        m.outlet_name,\n", "        m.item_id,\n", "        case when current_inv < 0 then 0 else current_inv end as net_inv,\n", "        case when iw.weights is null then 0 else iw.weights::float end weights\n", "    FROM metrics.fnv_hourly_details m\n", "    INNER JOIN lake_retail.console_outlet  co ON m.outlet_id = co.id\n", "    \n", "    LEFT JOIN  lake_rpc.item_category_details ipm ON  m.item_id = ipm.item_id \n", "    LEFT JOIN  (SELECT city, item_id, weights  -- order_hour,\n", "                FROM metrics.city_item_cart_penetration\n", "                WHERE updated_at = (SELECT max(updated_at) FROM metrics.city_item_cart_penetration)\n", "                AND assortment_type = 'FnV'\n", "                ) iw ON m.item_id = iw.item_id AND \n", "                CASE\n", "                WHEN trim(lower(co.location)) in ('gurgaon','hrncr','hr-ncr') then 'gurgaon'  \n", "                WHEN trim(lower(co.location)) in ('upncr','up-ncr') then 'noida' \n", "                WHEN trim(lower(co.location)) in ('bangalore') then 'bengaluru'  \n", "                WHEN trim(lower(co.location)) in ('kanpur') then 'kanpur' \n", "                WHEN trim(lower(co.location)) in ('zirakpur') then 'zirakpur'   \n", "                WHEN trim(lower(co.location)) in ('faridabad') then 'faridabad' \n", "                else trim(lower(co.location))\n", "                end = lower(iw.city)\n", "                \n", "    WHERE\n", "        m.updated_at >= current_date - interval '7 day'\n", "        and hour_ between 6 and 23\n", "        \n", "    \"\"\"\n", "\n", "availability_df = read_sql_query(availability_sql, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "80504f9f-02fb-4cd3-a336-8b9b9b82f5a7", "metadata": {}, "outputs": [], "source": ["availability_df[\"date_\"] = pd.to_datetime(availability_df[\"date_\"])\n", "\n", "availability_clean_df = pd.merge(\n", "    availability_df,\n", "    fnv_clean_assortment_df,\n", "    on=[\"date_\", \"item_id\", \"outlet_id\"],\n", "    how=\"left\",\n", ")\n", "availability_clean_df = (\n", "    availability_clean_df[availability_clean_df[\"flag\"] == 1]\n", "    .reset_index()\n", "    .drop(columns={\"index\", \"flag\"})\n", ")\n", "availability_clean_df"]}, {"cell_type": "code", "execution_count": null, "id": "acbf53be-450f-4ff1-8e23-b18c336b7b27", "metadata": {}, "outputs": [], "source": ["availability_clean_df.isna().sum()"]}, {"cell_type": "markdown", "id": "572c69c3-b7fe-4fbc-a958-deca9a718652", "metadata": {}, "source": ["### Weighted Outlet Availability"]}, {"cell_type": "code", "execution_count": null, "id": "a0e746f3-5d76-4e24-b792-0cac592062fe", "metadata": {}, "outputs": [], "source": ["availability_clean_df_live_sku = availability_clean_df[\n", "    availability_clean_df[\"net_inv\"] > 0\n", "]\n", "availability_clean_df_live_sku = (\n", "    availability_clean_df_live_sku.groupby(\n", "        [\"date_\", \"product_cat\", \"hour_\", \"city\", \"outlet_id\", \"outlet_name\"]\n", "    )\n", "    .agg({\"item_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"live_sku\"})\n", ")\n", "\n", "\n", "availability_clean_df_total_sku = (\n", "    availability_clean_df.groupby(\n", "        [\"date_\", \"product_cat\", \"hour_\", \"city\", \"outlet_id\", \"outlet_name\"]\n", "    )\n", "    .agg({\"item_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"total_sku\"})\n", ")\n", "availability_clean_df_total_sku\n", "\n", "availability_clean_df_net_inv_weights = availability_clean_df[\n", "    availability_clean_df[\"net_inv\"] > 0\n", "]\n", "availability_clean_df_net_inv_weights = (\n", "    availability_clean_df_net_inv_weights.groupby(\n", "        [\"date_\", \"product_cat\", \"hour_\", \"city\", \"outlet_id\", \"outlet_name\"]\n", "    )\n", "    .agg({\"weights\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"weights\": \"net_inv_weights\"})\n", ")\n", "\n", "availability_clean_df_total_weights = (\n", "    availability_clean_df.groupby(\n", "        [\"date_\", \"product_cat\", \"hour_\", \"city\", \"outlet_id\", \"outlet_name\"]\n", "    )\n", "    .agg({\"weights\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"weights\": \"total_weights\"})\n", ")\n", "availability_clean_df_total_weights\n", "\n", "\n", "wt_avail_outlet = (\n", "    availability_clean_df[\n", "        [\n", "            \"date_\",\n", "            \"product_cat\",\n", "            \"hour_\",\n", "            \"city\",\n", "            \"outlet_id\",\n", "            \"outlet_name\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "wt_avail_outlet = pd.merge(\n", "    wt_avail_outlet,\n", "    availability_clean_df_live_sku,\n", "    on=[\"date_\", \"product_cat\", \"hour_\", \"city\", \"outlet_id\", \"outlet_name\"],\n", "    how=\"left\",\n", ")\n", "wt_avail_outlet = pd.merge(\n", "    wt_avail_outlet,\n", "    availability_clean_df_total_sku,\n", "    on=[\"date_\", \"product_cat\", \"hour_\", \"city\", \"outlet_id\", \"outlet_name\"],\n", "    how=\"left\",\n", ")\n", "wt_avail_outlet = pd.merge(\n", "    wt_avail_outlet,\n", "    availability_clean_df_net_inv_weights,\n", "    on=[\"date_\", \"product_cat\", \"hour_\", \"city\", \"outlet_id\", \"outlet_name\"],\n", "    how=\"left\",\n", ")\n", "wt_avail_outlet = pd.merge(\n", "    wt_avail_outlet,\n", "    availability_clean_df_total_weights,\n", "    on=[\"date_\", \"product_cat\", \"hour_\", \"city\", \"outlet_id\", \"outlet_name\"],\n", "    how=\"left\",\n", ")\n", "\n", "wt_avail_outlet[\"availability\"] = (\n", "    wt_avail_outlet[\"live_sku\"] / wt_avail_outlet[\"total_sku\"]\n", ")\n", "wt_avail_outlet[\"weighted_availability\"] = (\n", "    wt_avail_outlet[\"net_inv_weights\"] / wt_avail_outlet[\"total_weights\"]\n", ")\n", "\n", "wt_avail_outlet = wt_avail_outlet.drop(columns={\"net_inv_weights\", \"total_weights\"})\n", "\n", "wt_avail_outlet"]}, {"cell_type": "code", "execution_count": null, "id": "ac1c7085-6ba5-4a09-97ca-3366bf1fb8c8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "1d1055cb-fa0e-44a6-8d40-b6ac7b933bac", "metadata": {}, "source": ["### City-Store Weights"]}, {"cell_type": "code", "execution_count": null, "id": "26c0c1a3-b2e4-40ae-ad3a-3f41d86c9c73", "metadata": {}, "outputs": [], "source": ["city_store_weights_sql = f\"\"\"\n", "    \n", "    SELECT outlet_id, store_weight\n", "    FROM metrics.city_store_penetration\n", "    WHERE updated_at = (SELECT max(updated_at) FROM metrics.city_store_penetration)\n", "    \n", "    \"\"\"\n", "city_store_weights = read_sql_query(city_store_weights_sql, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "8e5c406a-e4e1-48d7-87ed-e1d38902fb8c", "metadata": {}, "outputs": [], "source": ["city_store_weights"]}, {"cell_type": "code", "execution_count": null, "id": "292bf730-898d-491e-b841-856fa9f4c441", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "1b32f263-164e-44d0-a3dc-c7a49ba71cde", "metadata": {}, "source": ["## Weighted City Availability"]}, {"cell_type": "code", "execution_count": null, "id": "f741fe69-14b5-402b-80f0-700f2247edc2", "metadata": {}, "outputs": [], "source": ["wt_avail_city = pd.merge(\n", "    wt_avail_outlet, city_store_weights, on=[\"outlet_id\"], how=\"left\"\n", ")\n", "wt_avail_city[\"store_weight\"] = wt_avail_city[\"store_weight\"].fillna(0).astype(float)\n", "wt_avail_city[\"final_wtd_avail\"] = (\n", "    wt_avail_city[\"weighted_availability\"] * wt_avail_city[\"store_weight\"]\n", ")\n", "wt_avail_city"]}, {"cell_type": "code", "execution_count": null, "id": "a25c6916-e787-4eb5-8ac5-7d85aae77155", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "214b6e38-cf37-4302-85d3-40d7c8e27094", "metadata": {}, "outputs": [], "source": ["final_wt_avail_city_sql = \"\"\"\n", "    \n", "    SELECT date_, product_cat, city, hour_, sum(final_wtd_avail)*1.00/sum(store_weight) weighted_availability\n", "    FROM wt_avail_city\n", "    GROUP BY 1,2,3,4\n", "\n", "    \"\"\"\n", "\n", "final_wt_avail_city = sqldf(final_wt_avail_city_sql, locals())"]}, {"cell_type": "code", "execution_count": null, "id": "2b7b1b25-51ee-43be-827e-5ff70dbf7b33", "metadata": {}, "outputs": [], "source": ["final_wt_avail_city[\"date_\"] = pd.to_datetime(final_wt_avail_city[\"date_\"]).dt.strftime(\n", "    \"%Y-%m-%d\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ea9fa396-d655-465e-adbc-2fc4f1511b59", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    final_wt_avail_city, \"1SuXf5kzWniOdLSl0Br-vxJc18QS4Yww-ZZT5WVozSRg\", \"wtd_raw_data\"\n", ")\n", "pb.to_sheets(\n", "    to_be_removed_assortment_city,\n", "    \"1SuXf5kzWniOdLSl0Br-vxJc18QS4Yww-ZZT5WVozSRg\",\n", "    \"city_item_raw\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "aea2bf1a-1ace-4d67-81cd-da3ef6b05b9a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8b04961a-8cea-4213-83a8-6aac2f918c30", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}