{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ignore warning\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sqlalchemy as sqla\n", "import pandas as pd\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "\n", "con = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["end_date = (datetime.now() - timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "start_date = (datetime.now() - timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "\n", "# start_date='2020-01-10'\n", "# end_date='2020-01-10'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["query = \"\"\"\n", "WITH item_availability AS\n", "  (SELECT DISTINCT item_id,\n", "                   \"item_name \" as item_name,\n", "                   date(a.order_date) AS date_of_snapshot,\n", "                   recent_assortment,\n", "                   assortment_date,\n", "                   new_substate,\n", "                   facility_id,\n", "                   date_of_activation,\n", "                   actual_quantity,\n", "                   blocked_quantity,\n", "                   availability,\n", "                   facility_name,\n", "                   active_flag,\n", "                   MONTH,\n", "                   l0,\n", "                   l0_id,\n", "                   is_gb,\n", "                   uom,\n", "                   weight_in_gm,\n", "                   buckets,\n", "                   bucket_x,\n", "                   app_live,\n", "                   inv_flag,\n", "                   unreliable,\n", "                   unorderable\n", "   FROM rpc_daily_availability AS a\n", "   INNER JOIN\n", "     (SELECT max(order_date) AS order_date\n", "      FROM rpc_daily_availability\n", "      WHERE date(order_date) BETWEEN '{start_date}' AND '{end_date}' ) AS b ON a.order_date=b.order_date\n", "   WHERE date(a.order_date) BETWEEN '{start_date}' AND '{end_date}' ) ,\n", "     item_vendor_facility AS\n", "  (SELECT a.*,\n", "          b.vendor_id\n", "   FROM item_availability AS a\n", "   LEFT JOIN lake_vms.vms_vendor_facility_alignment AS b ON a.item_id=b.item_id\n", "   AND a.facility_id=b.facility_id\n", "   AND b.active=1) ,\n", "     item_brand_details AS\n", "  (SELECT ivl.*,\n", "          brand_id,\n", "          brand_name,\n", "          manufacture_name,\n", "          manufacturer_id\n", "   FROM item_vendor_facility ivl\n", "   LEFT JOIN\n", "     (SELECT *\n", "      FROM\n", "        (SELECT DISTINCT item_id,\n", "                         rpp.NAME AS item_name,\n", "                         row_number() OVER(PARTITION BY item_id\n", "                                           ORDER BY item_name DESC) AS rank_1,\n", "                         is_pl AS is_gb,\n", "                         brand_id,\n", "                         rpb.NAME AS brand_name,\n", "                         rpm.NAME AS manufacture_name,\n", "                         rpm.id AS manufacturer_id\n", "         FROM lake_rpc.product_product AS rpp\n", "         INNER JOIN lake_rpc.product_brand AS rpb ON rpp.brand_id=rpb.id\n", "         INNER JOIN lake_rpc.product_manufacturer AS rpm ON rpm.id=rpb.manufacturer_id)item_details\n", "      WHERE rank_1=1 )item_details ON ivl.item_id=item_details.item_id),\n", "     po_details AS\n", "  (SELECT ibd.*,\n", "          pfa.po_cycle\n", "   FROM item_brand_details ibd\n", "   LEFT JOIN lake_vms.vendor_manufacturer_physical_facility_attributes pfa ON ibd.facility_id = pfa.facility_id\n", "   AND ibd.manufacturer_id = pfa.manufacturer_id\n", "   AND ibd.vendor_id = pfa.vendor_id),\n", "     vendor_name AS\n", "  (SELECT po.*,\n", "          vv.vendor_name\n", "   FROM po_details po\n", "   LEFT JOIN lake_vms.vms_vendor vv ON po.vendor_id = vv.id\n", "   AND vv.active = 1),\n", "     facility_check AS\n", "  (SELECT min(oms_order.install_ts) AS start_date,\n", "          max(oms_order.install_ts) AS end_date,\n", "          facility_id\n", "   FROM lake_oms_bifrost.oms_order\n", "   INNER JOIN lake_oms_bifrost.oms_suborder ON oms_order.id=oms_suborder.order_id\n", "   INNER JOIN lake_oms_bifrost.oms_merchant ON oms_suborder.backend_merchant_id=oms_merchant.id\n", "   INNER JOIN lake_retail.console_outlet_cms_store ON oms_merchant.external_id=lake_retail.console_outlet_cms_store.cms_store\n", "   INNER JOIN lake_retail.console_outlet ON lake_retail.console_outlet_cms_store.outlet_id=lake_retail.console_outlet.id\n", "   GROUP BY 3),\n", "     availability_with_facility_flag AS\n", "  ( SELECT item_id,\n", "           date_of_snapshot AS order_date,\n", "           recent_assortment,\n", "           assortment_date,\n", "           new_substate,\n", "           a.facility_id,\n", "           date_of_activation,\n", "           actual_quantity,\n", "           blocked_quantity,\n", "           availability,\n", "           facility_name,\n", "           active_flag,\n", "           l0,\n", "           l0_id,\n", "           is_gb,\n", "           uom,\n", "           weight_in_gm,\n", "           buckets,\n", "           bucket_x,\n", "           vendor_id,\n", "           po_cycle,\n", "           vendor_name,\n", "           item_name,\n", "           brand_id,\n", "           brand_name,\n", "           manufacture_name,\n", "           manufacturer_id AS manufacture_id,\n", "           app_live,\n", "           inv_flag,\n", "           unreliable,\n", "            unorderable,\n", "           CASE\n", "               WHEN (a.facility_id=b.facility_id\n", "                     AND a.date_of_snapshot>=b.start_date\n", "                     AND a.date_of_snapshot<=b.end_date) THEN 1\n", "               ELSE 0\n", "           END AS facility_flag\n", "   FROM vendor_name AS a\n", "   LEFT JOIN facility_check AS b ON a.facility_id=b.facility_id) ,\n", "     item_transfer_flag AS\n", "  (SELECT item_id,\n", "          facility_id,\n", "          CASE\n", "              WHEN tag_value IS NOT NULL THEN 'yes'\n", "              ELSE 'no'\n", "          END AS transfer_flag\n", "   FROM lake_rpc.item_outlet_tag_mapping AS a\n", "   LEFT JOIN lake_retail.console_outlet AS b ON a.outlet_id=b.id\n", "   WHERE tag_type_id=8\n", "     AND a.active=1\n", "   GROUP BY 1,\n", "            2,\n", "            3) ,\n", "     availability_transfer AS\n", "  ( SELECT a.*,\n", "           b.transfer_flag\n", "   FROM availability_with_facility_flag AS a\n", "   LEFT JOIN item_transfer_flag AS b ON a.item_id=b.item_id\n", "   AND a.facility_id=b.facility_id)\n", "   \n", "SELECT *\n", "FROM availability_transfer\n", "\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_check = pd.read_sql_query(sql=query, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_check.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_check.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_check.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_check.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_details = \"\"\"\n", "with item_wise_order_details as\n", "(\n", "select a.*\n", "from\n", "(\n", "SELECT distinct\n", "pp.item_id,\n", "p.type as product_type,\n", "p.type_id as p_type,\n", "cat1.id as l1_id,\n", "cat1.name as l1,\n", "cat2.id as l2_id,\n", "cat2.name as l2,\n", "row_number() over(partition by pp.item_id order by pb.id desc) as rank_3\n", "--P.ENABLED_FLAG\n", "FROM\n", "lake_cms.GR_PRODUCT P\n", "INNER JOIN lake_cms.GR_PRODUCT_CATEGORY_MAPPING PCM ON P.ID = PCM.PRODUCT_ID\n", "INNER JOIN lake_cms.GR_CATEGORY CAT2 ON PCM.CATEGORY_ID = CAT2.ID AND PCM.IS_PRIMARY=TRUE\n", "INNER JOIN lake_cms.GR_CATEGORY CAT1 ON CAT2.PARENT_CATEGORY_ID = CAT1.ID\n", "INNER JOIN lake_cms.GR_CATEGORY CAT ON CAT1.PARENT_CATEGORY_ID = CAT.ID\n", "inner join lake_rpc.item_product_mapping ipm on ipm.product_id=p.id and ipm.item_id is not null\n", "left join lake_rpc.product_product pp on pp.item_id=ipm.item_id\n", "left join lake_rpc.product_brand pb on pb.id=pp.brand_id\n", "left join lake_rpc.product_manufacturer rpm on pb.manufacturer_id=rpm.id\n", "where pp.active =1 and pp.approved=1\n", "and cat.ID not in (9,1487)\n", ")as a\n", "where rank_3=1\n", ")\n", "select * from item_wise_order_details\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_details_data = pd.read_sql_query(sql=item_details, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_frame = availability_check.merge(item_details_data, on=\"item_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_frame[\"order_date\"] = pd.to_datetime(final_data_frame.order_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_frame[\"manufacture_id\"] = final_data_frame.manufacture_id.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_frame[\"manufacture_id\"] = final_data_frame.manufacture_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_frame[\"po_cycle\"] = final_data_frame.po_cycle.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_frame[\"po_cycle\"] = final_data_frame.po_cycle.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_frame[\"l0_id\"] = final_data_frame.l0_id.fillna(0)\n", "final_data_frame[\"vendor_id\"] = final_data_frame.vendor_id.fillna(0)\n", "final_data_frame[\"brand_id\"] = final_data_frame.brand_id.fillna(0)\n", "final_data_frame[\"actual_quantity\"] = final_data_frame.actual_quantity.fillna(0)\n", "final_data_frame[\"blocked_quantity\"] = final_data_frame.blocked_quantity.fillna(0)\n", "final_data_frame[\"new_substate\"] = final_data_frame.new_substate.fillna(0)\n", "final_data_frame[\"manufacture_id\"] = final_data_frame.manufacture_id.fillna(0)\n", "final_data_frame[\"l0_id\"] = final_data_frame.l0_id.astype(int)\n", "final_data_frame[\"item_id\"] = final_data_frame.item_id.astype(int)\n", "final_data_frame[\"new_substate\"] = final_data_frame.new_substate.astype(int)\n", "final_data_frame[\"facility_id\"] = final_data_frame.facility_id.astype(int)\n", "final_data_frame[\"actual_quantity\"] = final_data_frame.actual_quantity.astype(int)\n", "final_data_frame[\"blocked_quantity\"] = final_data_frame.blocked_quantity.astype(int)\n", "final_data_frame[\"vendor_id\"] = final_data_frame.vendor_id.astype(int)\n", "final_data_frame[\"brand_id\"] = final_data_frame.brand_id.astype(int)\n", "final_data_frame[\"manufacture_id\"] = final_data_frame.manufacture_id.astype(int)\n", "final_data_frame[\"app_live\"] = final_data_frame.app_live.astype(int)\n", "final_data_frame[\"inv_flag\"] = final_data_frame.inv_flag.astype(int)\n", "final_data_frame[\"unreliable\"] = np.where(\n", "    final_data_frame.unreliable == \"NA\", \"0\", final_data_frame.unreliable\n", ")\n", "final_data_frame[\"unorderable\"] = np.where(\n", "    final_data_frame.unorderable == \"NA\", \"0\", final_data_frame.unorderable\n", ")\n", "final_data_frame[\"unreliable\"] = final_data_frame.unreliable.astype(int)\n", "final_data_frame[\"unorderable\"] = final_data_frame.unorderable.astype(int)\n", "final_data_frame.transfer_flag = np.where(final_data_frame.transfer_flag.isna(), \"no\", \"yes\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_frame.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_frame = final_data_frame[\n", "    [\n", "        \"item_id\",\n", "        \"order_date\",\n", "        \"recent_assortment\",\n", "        \"assortment_date\",\n", "        \"new_substate\",\n", "        \"facility_id\",\n", "        \"date_of_activation\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"availability\",\n", "        \"facility_name\",\n", "        \"active_flag\",\n", "        \"l0\",\n", "        \"l0_id\",\n", "        \"is_gb\",\n", "        \"uom\",\n", "        \"weight_in_gm\",\n", "        \"buckets\",\n", "        \"bucket_x\",\n", "        \"vendor_id\",\n", "        \"po_cycle\",\n", "        \"vendor_name\",\n", "        \"item_name\",\n", "        \"brand_id\",\n", "        \"brand_name\",\n", "        \"manufacture_name\",\n", "        \"manufacture_id\",\n", "        \"facility_flag\",\n", "        \"transfer_flag\",\n", "        \"inv_flag\",\n", "        \"app_live\",\n", "        \"l2\",\n", "        \"l1_id\",\n", "        \"p_type\",\n", "        \"l1\",\n", "        \"l2_id\",\n", "        \"product_type\",\n", "        \"rank_3\",\n", "        \"unreliable\",\n", "        \"unorderable\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_frame.rename(columns={\"item_name\": \"item_name \"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"Item ID\"},\n", "    {\"name\": \"order_date\", \"type\": \"date\", \"description\": \"Date time of analysis\"},\n", "    {\n", "        \"name\": \"recent_assortment\",\n", "        \"type\": \"timestamp\",\n", "        \"description\": \"Date when the item facility combination is updated\",\n", "    },\n", "    {\n", "        \"name\": \"assortment_date\",\n", "        \"type\": \"timestamp\",\n", "        \"description\": \"Date when the item facility combination was first created\",\n", "    },\n", "    {\n", "        \"name\": \"new_substate\",\n", "        \"type\": \"smallint\",\n", "        \"description\": \"substate_id of item at order_date\",\n", "    },\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"Facility ID\"},\n", "    {\n", "        \"name\": \"date_of_activation\",\n", "        \"type\": \"timestamp\",\n", "        \"description\": \"First arrival of item in stock after becoming active\",\n", "    },\n", "    {\n", "        \"name\": \"actual_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Actual physical inventory at order_date\",\n", "    },\n", "    {\n", "        \"name\": \"blocked_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Blocked inventory at order_date\",\n", "    },\n", "    {\n", "        \"name\": \"availability\",\n", "        \"type\": \"varchar(30)\",\n", "        \"description\": \"'live' if unblocked inventory > 0 otherwise 'not live'\",\n", "    },\n", "    {\"name\": \"facility_name\", \"type\": \"varchar(500)\", \"description\": \"Facility Name\"},\n", "    {\n", "        \"name\": \"active_flag\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Active if substate_id = 1 otherwise inactive\",\n", "    },\n", "    {\"name\": \"l0\", \"type\": \"varchar(500)\", \"description\": \"Level 0 category name\"},\n", "    {\"name\": \"l0_id\", \"type\": \"float\", \"description\": \"Level 0 category id\"},\n", "    {\"name\": \"is_gb\", \"type\": \"varchar\", \"description\": \"is grofer's brand\"},\n", "    {\"name\": \"uom\", \"type\": \"varchar\", \"description\": \"unit of measurement\"},\n", "    {\"name\": \"weight_in_gm\", \"type\": \"float\", \"description\": \"Weight in gms\"},\n", "    {\"name\": \"buckets\", \"type\": \"varchar\", \"description\": \"Bucket A/B\"},\n", "    {\n", "        \"name\": \"bucket_x\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Y if item is Bucket X otherwise N\",\n", "    },\n", "    {\"name\": \"vendor_id\", \"type\": \"int\", \"description\": \"Vendor ID\"},\n", "    {\"name\": \"po_cycle\", \"type\": \"int\", \"description\": \"PO cycle of Vendor\"},\n", "    {\"name\": \"vendor_name\", \"type\": \"varchar(1000)\", \"description\": \"Vendor Name\"},\n", "    {\"name\": \"item_name \", \"type\": \"varchar(500)\", \"description\": \"Item Name\"},\n", "    {\"name\": \"brand_id\", \"type\": \"int\", \"description\": \"Brand ID\"},\n", "    {\"name\": \"brand_name\", \"type\": \"varchar(1000)\", \"description\": \"Brand Name\"},\n", "    {\n", "        \"name\": \"manufacture_name\",\n", "        \"type\": \"varchar(1000)\",\n", "        \"description\": \"Manufacturer Name\",\n", "    },\n", "    {\"name\": \"manufacture_id\", \"type\": \"int\", \"description\": \"Manufacturer ID\"},\n", "    {\"name\": \"facility_flag\", \"type\": \"int\", \"description\": \"Is facility active\"},\n", "    {\"name\": \"transfer_flag\", \"type\": \"varchar\", \"description\": \"is TEA\"},\n", "    {\"name\": \"inv_flag\", \"type\": \"int\", \"description\": \"1 if unblocked inventory > 0\"},\n", "    {\n", "        \"name\": \"app_live\",\n", "        \"type\": \"int\",\n", "        \"description\": \"status on the app at order_date\",\n", "    },\n", "    {\"name\": \"l2\", \"type\": \"varchar(1000)\", \"description\": \"Level 2 category name\"},\n", "    {\"name\": \"l1_id\", \"type\": \"float\", \"description\": \"Level 1 category ID\"},\n", "    {\"name\": \"p_type\", \"type\": \"float\", \"description\": \"Product type ID\"},\n", "    {\"name\": \"l1\", \"type\": \"varchar(1000)\", \"description\": \"Level 1 category name\"},\n", "    {\"name\": \"l2_id\", \"type\": \"float\", \"description\": \"Level 2 category ID\"},\n", "    {\n", "        \"name\": \"product_type\",\n", "        \"type\": \"varchar(1000)\",\n", "        \"description\": \"Product type name\",\n", "    },\n", "    {\"name\": \"rank_3\", \"type\": \"float\", \"description\": \"miscellaneous value\"},\n", "    {\"name\": \"unreliable\", \"type\": \"int\", \"description\": \"unreliable tag\"},\n", "    {\"name\": \"unorderable\", \"type\": \"int\", \"description\": \"unorderable tag\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"rpc_availability_snapshot\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"item_id\", \"facility_id\", \"order_date\"],\n", "    \"sortkey\": [\"order_date\", \"item_id\", \"facility_id\"],\n", "    \"distkey\": \"item_id\",\n", "    \"incremental_key\": \"order_date\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"Stores 11 pm snapshot of rpc_daily_availability table and other item details\",\n", "}\n", "\n", "pb.to_redshift(final_data_frame, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}