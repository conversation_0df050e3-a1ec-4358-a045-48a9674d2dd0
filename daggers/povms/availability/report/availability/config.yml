dag_name: availability
dag_type: report
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RURMNJJH
path: povms/availability/report/availability
paused: true
pool: povms_pool
project_name: availability
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 30 3 * * *
  start_date: '2021-08-09T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- availability_realtime_email_template.html
- specific_availability_realtime_email_template.html
tags: []
template_name: notebook
version: 74
