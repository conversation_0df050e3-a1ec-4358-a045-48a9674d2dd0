{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "import pandas as pd\n", "import pencilbox as pb\n", "import sqlalchemy as sqla\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "import numpy as np\n", "import json\n", "\n", "con = pb.get_connection(\"redshift\")\n", "retail = pb.get_connection(\"retail\")\n", "capacity_system = pb.get_connection(\"supply_orchestrator\")\n", "\n", "import os"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date = (datetime.today() + timedelta(hours=5.5) - timedelta(days=31)).date()\n", "end_date = (datetime.today() + timedelta(hours=5.5) - timedelta(days=1)).date()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Base table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "\n", "select a.*, oz.zone\n", "\n", "from rpc_daily_availability as a\n", "\n", "inner join\n", "\n", "(select max(order_date) as maxi \n", "from rpc_daily_availability \n", "where date(order_date) = current_date \n", ") as b\n", "\n", "on a.order_date = b.maxi\n", "\n", "left join (select distinct facility_id, zone from metrics.outlet_zone_mapping) oz\n", "on a.facility_id = oz.facility_id\n", "\n", "where date(a.order_date) = current_date \n", "and facility_name not ilike '%%bulk%%'\n", "and facility_name not ilike '%%donate%%'\n", "and facility_name not ilike '%%feeder%%'\n", "and facility_name not ilike '%%grofers%%'\n", "and (l0_id::int)!=1487\n", "\"\"\"\n", "availability_value_l0 = pd.read_sql(sql=query, con=con)\n", "availability_value_l0.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Handling null values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_value_l0[\"l0\"] = np.where(\n", "    availability_value_l0.l0 == \"\", \"Mapping not present\", availability_value_l0.l0\n", ")\n", "\n", "availability_value_l0[\"l0_id\"] = np.where(\n", "    availability_value_l0.l0_id.isna(),\n", "    1000,\n", "    availability_value_l0.l0_id,\n", ")\n", "\n", "availability_value_l0[\"buckets\"] = np.where(\n", "    availability_value_l0.buckets.isna(),\n", "    \"Mapping not present\",\n", "    availability_value_l0.buckets,\n", ")\n", "availability_value_l0[availability_value_l0.buckets == \"Mapping not present\"].head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Active facility"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# facility_query = \"\"\"\n", "# select distinct\n", "# facility_id\n", "# from oms_order\n", "# inner join oms_suborder on oms_order.id=oms_suborder.order_id\n", "# inner join view_oms_merchant on oms_suborder.backend_merchant_id=view_oms_merchant.id\n", "# inner join Rt_console_outlet_cms_store on view_oms_merchant.external_id=Rt_console_outlet_cms_store.cms_store\n", "# inner join Pos_console_outlet on  Rt_console_outlet_cms_store.outlet_id=Pos_console_outlet.id\n", "# where date(oms_order.install_ts)='2020-03-20'\n", "# group by 1\n", "# \"\"\"\n", "# facility_data1 = pd.read_sql(sql=facility_query, con=con)\n", "\n", "facility_query = \"\"\"\n", "select distinct facility_id from\n", "    (select facility_id, count(distinct order_id) as order_count\n", "    from lake_ims.ims_order_details a\n", "    left join lake_retail.console_outlet o on a.outlet = o.id\n", "    where date(a.created_at) between current_date-interval '2 days' and current_date-interval '1 days'\n", "    and a.status_id <> 5\n", "    and business_type not ilike '%%b2b%%'\n", "    and business_type_id in (1,2,3)\n", "    and facility_id not in (29,139)\n", "    group by 1) b\n", "where order_count > 10\n", "\n", "\"\"\"\n", "\n", "facility_data = pd.read_sql_query(sql=facility_query, con=con)\n", "\n", "facility_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Active dark stores"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "select distinct facility_id::int,\n", "max(case when business_type_id=7 then 'Dark Store' when business_type_id=8 then 'Franchise Store' end) as store_type\n", "from lake_retail.console_outlet\n", "where \n", "business_type_id in (7)\n", "--business_type_id in (7,8)\n", "\n", "group by 1\n", "\"\"\"\n", "ds_mapping = pd.read_sql(sql, con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dark_stores_list = list(filter(None, ds_mapping[\"facility_id\"].astype(int).values))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_active = facility_data.append(ds_mapping[[\"facility_id\"]]).reset_index()\n", "all_active.drop([\"index\"], inplace=True, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Filter rows for active facilities"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_value = availability_value_l0.merge(all_active, on=\"facility_id\", how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Have at least 100 eligible live items\n", "\n", "eligible_facilities = (\n", "    availability_value[\n", "        # (availability_value.date_of_activation <= availability_value.order_date)\n", "        # &\n", "        ((availability_value.inv_flag == 1) | (availability_value.app_live == 1))\n", "    ]\n", "    .groupby(\"facility_id\")[\"item_id\"]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eligible_facilities = eligible_facilities[eligible_facilities.item_id > 100][\"facility_id\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_value = availability_value.merge(eligible_facilities, on=\"facility_id\", how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_value[availability_value.facility_id == 701].shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Map store type"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_value = availability_value.merge(ds_mapping, on=[\"facility_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_value.store_type = np.where(\n", "    availability_value.store_type.isna(), \"NDD\", availability_value.store_type\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Mapping facility name to shorter name and merging with base table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["names = availability_value.facility_name.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_mapping = pd.DataFrame({\"facility_name\": names})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in range(0, len(facility_mapping)):\n", "    facility_mapping.loc[i, \"Facility\"] = (\n", "        facility_mapping.loc[i, \"facility_name\"]\n", "        .replace(\" - \", \" \")\n", "        .replace(\"Super Store \", \"\")\n", "        .replace(\" Warehouse\", \"\")\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_value = availability_value.merge(facility_mapping, on=[\"facility_name\"], how=\"inner\")\n", "availability_value.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability_value[\"final_facility\"] = np.where(\n", "    availability_value.Facility.isna(),\n", "    availability_value.facility_name,\n", "    availability_value.Facility,\n", ")\n", "availability_value.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Facility level continuous items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query_last_30_days_cons = \"\"\"  with continuous_item as\n", "(\n", "\n", "select\n", "item_id,\n", "facility_id,\n", "facility_name,\n", "sum(case when (live_days=day_to_be_live) and day_to_be_live>0 THEN 1 ELSE 0 END) continuous_item\n", "from\n", "(\n", "select item_id,\n", "facility_id,\n", "facility_name,\n", "--COUNT(case when ((date_of_activation <= order_date or inv_flag=1) and (availability ='live' or app_live=1)) then order_date end ) as live_days,\n", "--COUNT(case when (date_of_activation<=order_date or inv_flag=1) THEN order_date END) as day_to_be_live\n", "\n", "COUNT(case when (app_live=1) then order_date end ) as live_days,\n", "COUNT(order_date) as day_to_be_live\n", "\n", "from rpc_daily_availability \n", "where new_substate=1 \n", "--and ((unreliable != '1' and unorderable != '1') \n", "--    or (inv_flag > 0 and unreliable = '1' and unorderable = '1')\n", "--    or (inv_flag > 0 and unorderable = '1')\n", "--    or (inv_flag > 0 and unreliable = '1')\n", "--)\n", "and date(order_date)  between %(start_date)s and %(end_date)s\n", "group by  1,2,3\n", ")as a\n", "group by  1,2,3\n", "\n", ")\n", "\n", "select facility_id,\n", "facility_name,\n", "count(case when continuous_item = 1 then item_id else null end),\n", "count(item_id) as total_count\n", "from continuous_item\n", "group by 1,2\n", "\n", "\"\"\"\n", "\n", "data_last_30_days_cons = pd.read_sql(\n", "    sql=query_last_30_days_cons,\n", "    con=con,\n", "    params={\"start_date\": start_date, \"end_date\": end_date},\n", ")\n", "\n", "data_last_30_days_cons.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_last_30_days_cons = data_last_30_days_cons.merge(\n", "    facility_mapping, on=[\"facility_name\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_last_30_days_cons[\"final_facility\"] = np.where(\n", "    data_last_30_days_cons.Facility.isna(),\n", "    data_last_30_days_cons.facility_name,\n", "    data_last_30_days_cons.Facility,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_last_30_days_cons = data_last_30_days_cons.merge(all_active, on=\"facility_id\", how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_last_30_days_cons = data_last_30_days_cons.merge(ds_mapping, on=\"facility_id\", how=\"left\")\n", "data_last_30_days_cons.store_type = np.where(\n", "    data_last_30_days_cons.store_type.isna(), \"NDD\", data_last_30_days_cons.store_type\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["continous_availability_cons = (\n", "    data_last_30_days_cons.groupby([\"store_type\", \"facility_id\"])[[\"count\", \"total_count\"]]\n", "    .sum()\n", "    .reset_index()\n", ").rename(\n", "    columns={\n", "        \"count\": \"total_continous_availabile_item\",\n", "        \"total_count\": \"total_continous_eligible_item\",\n", "    }\n", ")\n", "\n", "# continous_availability_cons"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### L0 level continous items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query_last_30_days = \"\"\"  with continuous_item as\n", "(\n", "select\n", "item_id,\n", "l0,\n", "l0_id,\n", "facility_id,\n", "facility_name,\n", "sum(case when (live_days=day_to_be_live) and day_to_be_live>0 THEN 1 ELSE 0 END) continuous_item\n", "from\n", "(\n", "select item_id,\n", "l0,\n", "l0_id,\n", "facility_id,\n", "facility_name,\n", "--COUNT(case when ((date_of_activation <= order_date or inv_flag=1) and (availability ='live' or app_live=1)) then order_date end ) as live_days,\n", "--COUNT(case when (date_of_activation<=order_date or inv_flag=1) THEN order_date END) as day_to_be_live\n", "\n", "COUNT(case when  app_live=1 then order_date end ) as live_days,\n", "COUNT(order_date) as day_to_be_live\n", "\n", "from rpc_daily_availability \n", "where new_substate=1\n", "--and ((unreliable != '1' and unorderable != '1') \n", " --  or (inv_flag > 0 and unreliable = '1' and unorderable = '1')\n", "-- or (inv_flag > 0 and unorderable = '1')\n", " -- or (inv_flag > 0 and unreliable = '1')\n", "--)\n", "and date(order_date)  between %(start_date)s and %(end_date)s\n", "group by  1,2,3,4,5\n", ")as a\n", "group by  1,2,3,4,5\n", ")\n", "\n", "select \n", "l0,\n", "l0_id,\n", "facility_id,\n", "facility_name,\n", "count(case when continuous_item = 1 then item_id else null end),\n", "count(item_id) as total_count\n", "from continuous_item\n", "group by 1,2,3,4\n", "\n", "\"\"\"\n", "\n", "data_last_30_days = pd.read_sql(\n", "    sql=query_last_30_days,\n", "    con=con,\n", "    params={\"start_date\": start_date, \"end_date\": end_date},\n", ")\n", "\n", "\n", "data_last_30_days.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_last_30_days[\"l0\"] = np.where(\n", "    data_last_30_days.l0 == \"\", \"Mapping not present\", data_last_30_days.l0\n", ")\n", "data_last_30_days[\"l0_id\"] = np.where(\n", "    data_last_30_days.l0_id.isna(),\n", "    1000,\n", "    data_last_30_days.l0_id,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_last_30_days = data_last_30_days.merge(ds_mapping, on=\"facility_id\", how=\"left\")\n", "data_last_30_days.store_type = np.where(\n", "    data_last_30_days.store_type.isna(), \"NDD\", data_last_30_days.store_type\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_last_30_days = data_last_30_days.merge(facility_mapping, on=[\"facility_name\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_last_30_days[\"final_facility\"] = np.where(\n", "    data_last_30_days.Facility.isna(),\n", "    data_last_30_days.facility_name,\n", "    data_last_30_days.Facility,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_last_30_days1 = data_last_30_days.merge(all_active, on=\"facility_id\", how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_last_30_days1.head(2)\n", "\n", "\n", "l0_continous_availability = (\n", "    data_last_30_days1.groupby([\"store_type\", \"l0\", \"l0_id\"])[[\"count\", \"total_count\"]]\n", "    .sum()\n", "    .reset_index()\n", ").rename(\n", "    columns={\n", "        \"count\": \"total_continous_availabile_item\",\n", "        \"total_count\": \"total_continous_eligible_item\",\n", "    }\n", ")\n", "\n", "# l0_continous_availability"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### Code for unorderable/unreliable mailer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# orderable(substate = 1)_unorderable_uneliable_items\n", "\n", "unorderable_unreliable_items = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        & (\n", "            ((availability_value.unreliable == \"1\") & (availability_value.inv_flag <= 0))\n", "            | ((availability_value.unorderable == \"1\") & (availability_value.inv_flag <= 0))\n", "        )\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")\n", "unorderable_unreliable_items = unorderable_unreliable_items.rename(\n", "    columns={\"item_id\": \"unorderable_unreliable_items\"}\n", ")\n", "\n", "unorderable_unreliable_items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["unorderable_items_list = availability_value[\n", "    (availability_value.new_substate == 1)\n", "    & (availability_value.unorderable == \"1\")\n", "    & (availability_value.inv_flag > 0)\n", "]\n", "\n", "pb.to_sheets(\n", "    unorderable_items_list,\n", "    \"1AElu_-VzW5Zfk6-EjSc12VEWk9u-t24r2vJDcEfMSXk\",\n", "    \"Unorderable Items\",\n", ")\n", "\n", "unreliable_items_list = availability_value[\n", "    (availability_value.new_substate == 1)\n", "    & (availability_value.unreliable == \"1\")\n", "    & (availability_value.inv_flag > 0)\n", "]\n", "\n", "pb.to_sheets(\n", "    unreliable_items_list,\n", "    \"1AElu_-VzW5Zfk6-EjSc12VEWk9u-t24r2vJDcEfMSXk\",\n", "    \"Unreliable Items\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_item_x = (\n", "    availability_value[(availability_value.new_substate == 1)]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"total_item\"})\n", ")\n", "\n", "\n", "# orderable eligle item\n", "\n", "active_item_x = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        #         & (\n", "        #             (\n", "        #                 (availability_value.unreliable != \"1\")\n", "        #                 & (availability_value.unorderable != \"1\")\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #         )\n", "        #         & (\n", "        #             (availability_value.date_of_activation <= availability_value.order_date)\n", "        #             | (availability_value.inv_flag == 1)\n", "        #         )\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"total_active_item\"})\n", ")\n", "\n", "Q = total_item_x.merge(active_item_x, how=\"left\", on=[\"store_type\", \"facility_id\", \"facility_name\"])\n", "\n", "Q"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# orderable(substate = 1)_unorderable_items\n", "\n", "unorderable_items = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        & ((availability_value.unorderable == \"1\"))\n", "        #        & (availability_value.date_of_activation <= availability_value.order_date)\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ").rename(columns={\"item_id\": \"unorderable_items\"})\n", "\n", "\n", "unorderable_items_live = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        & ((availability_value.unorderable == \"1\"))\n", "        #      & (availability_value.date_of_activation <= availability_value.order_date)\n", "        & ((availability_value.app_live == 1))\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ").rename(columns={\"item_id\": \"unorderable_live_items\"})\n", "\n", "\n", "unorderable_items_bucketA = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        & ((availability_value.unorderable == \"1\"))\n", "        # & (availability_value.date_of_activation <= availability_value.order_date)\n", "        & (availability_value.buckets == \"A\")\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ").rename(columns={\"item_id\": \"unorderable_BucketA_items\"})\n", "\n", "\n", "unorderable_items_bucketA_live = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        & ((availability_value.unorderable == \"1\"))\n", "        #   & (availability_value.date_of_activation <= availability_value.order_date)\n", "        & (availability_value.buckets == \"A\")\n", "        & ((availability_value.app_live == 1))\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ").rename(columns={\"item_id\": \"unorderable_BucketA_live_items\"})\n", "\n", "\n", "unorderable_items_bucketX = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        & ((availability_value.unorderable == \"1\"))\n", "        #  & (availability_value.date_of_activation <= availability_value.order_date)\n", "        & (availability_value.bucket_x == \"Y\")\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ").rename(columns={\"item_id\": \"unorderable_BucketX_items\"})\n", "\n", "\n", "unorderable_items_bucketX_live = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        & ((availability_value.unorderable == \"1\"))\n", "        # & (availability_value.date_of_activation <= availability_value.order_date)\n", "        & (availability_value.bucket_x == \"Y\")\n", "        & ((availability_value.app_live == 1))\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ").rename(columns={\"item_id\": \"unorderable_BucketX_live_items\"})\n", "\n", "Q1 = Q.merge(unorderable_items, how=\"left\", on=[\"store_type\", \"facility_id\", \"facility_name\"])\n", "AA0 = Q1.merge(\n", "    unorderable_items_live,\n", "    how=\"left\",\n", "    on=[\"store_type\", \"facility_id\", \"facility_name\"],\n", ")\n", "AA = AA0.merge(\n", "    unorderable_items_bucketA,\n", "    how=\"left\",\n", "    on=[\"store_type\", \"facility_id\", \"facility_name\"],\n", ")\n", "BB = AA.merge(\n", "    unorderable_items_bucketA_live,\n", "    how=\"left\",\n", "    on=[\"store_type\", \"facility_id\", \"facility_name\"],\n", ")\n", "CC = BB.merge(\n", "    unorderable_items_bucketX,\n", "    how=\"left\",\n", "    on=[\"store_type\", \"facility_id\", \"facility_name\"],\n", ")\n", "DD = CC.merge(\n", "    unorderable_items_bucketX_live,\n", "    how=\"left\",\n", "    on=[\"store_type\", \"facility_id\", \"facility_name\"],\n", ")\n", "\n", "DD[\"Unorderable_Available_Items\"] = round(\n", "    (DD[\"unorderable_live_items\"] / DD[\"unorderable_items\"]) * 100, 1\n", ")\n", "DD[\"Unorderable_Available_BucketA_Items\"] = round(\n", "    (DD[\"unorderable_BucketA_live_items\"] / DD[\"unorderable_BucketA_items\"]) * 100, 1\n", ")\n", "DD[\"Unorderable_Available_BucketX_Items\"] = round(\n", "    (DD[\"unorderable_BucketX_live_items\"] / DD[\"unorderable_BucketX_items\"]) * 100, 1\n", ")\n", "\n", "\n", "DD.drop(\n", "    columns=[\n", "        \"unorderable_live_items\",\n", "        \"unorderable_BucketA_live_items\",\n", "        \"unorderable_BucketX_live_items\",\n", "    ],\n", "    inplace=True,\n", ")\n", "\n", "\n", "DD = DD.merge(facility_mapping, on=[\"facility_name\"], how=\"left\")\n", "\n", "DD[\"final_facility\"] = np.where(\n", "    DD.Facility.isna(),\n", "    DD.facility_name,\n", "    DD.Facility,\n", ")\n", "\n", "DDFInal = DD[\n", "    [\n", "        \"store_type\",\n", "        \"Facility\",\n", "        \"total_item\",\n", "        \"total_active_item\",\n", "        \"unorderable_items\",\n", "        \"unorderable_BucketA_items\",\n", "        \"unorderable_BucketX_items\",\n", "        \"Unorderable_Available_Items\",\n", "        \"Unorderable_Available_BucketA_Items\",\n", "        \"Unorderable_Available_BucketX_Items\",\n", "    ]\n", "]\n", "\n", "DDFInal.fillna(0, inplace=True)\n", "\n", "DDFInal[\n", "    [\n", "        \"total_item\",\n", "        \"total_active_item\",\n", "        \"unorderable_items\",\n", "        \"unorderable_BucketA_items\",\n", "        \"unorderable_BucketX_items\",\n", "    ]\n", "] = DDFInal[\n", "    [\n", "        \"total_item\",\n", "        \"total_active_item\",\n", "        \"unorderable_items\",\n", "        \"unorderable_BucketA_items\",\n", "        \"unorderable_BucketX_items\",\n", "    ]\n", "].astype(\n", "    int\n", ")\n", "DDFInal\n", "\n", "DD_rollup = round(\n", "    DDFInal.groupby([\"store_type\", \"Facility\"]).agg(\n", "        {\n", "            \"total_item\": \"sum\",\n", "            \"total_active_item\": \"sum\",\n", "            \"unorderable_items\": \"sum\",\n", "            \"unorderable_BucketA_items\": \"sum\",\n", "            \"unorderable_BucketX_items\": \"sum\",\n", "            \"Unorderable_Available_Items\": \"mean\",\n", "            \"Unorderable_Available_BucketA_Items\": \"mean\",\n", "            \"Unorderable_Available_BucketX_Items\": \"mean\",\n", "        }\n", "    ),\n", "    1,\n", ").reset_index()\n", "\n", "DD_rollup[\"Facility\"] = \"Pan India\"\n", "\n", "DD_rollup = round(\n", "    DD_rollup.groupby([\"store_type\", \"Facility\"]).agg(\n", "        {\n", "            \"total_item\": \"sum\",\n", "            \"total_active_item\": \"sum\",\n", "            \"unorderable_items\": \"sum\",\n", "            \"unorderable_BucketA_items\": \"sum\",\n", "            \"unorderable_BucketX_items\": \"sum\",\n", "            \"Unorderable_Available_Items\": \"mean\",\n", "            \"Unorderable_Available_BucketA_Items\": \"mean\",\n", "            \"Unorderable_Available_BucketX_Items\": \"mean\",\n", "        }\n", "    ),\n", "    1,\n", ").reset_index()\n", "\n", "DDFInal = DDFInal.append(DD_rollup)\n", "\n", "DDFInal[\n", "    [\n", "        \"Unorderable_Available_Items\",\n", "        \"Unorderable_Available_BucketA_Items\",\n", "        \"Unorderable_Available_BucketX_Items\",\n", "    ]\n", "] = (\n", "    DDFInal[\n", "        [\n", "            \"Unorderable_Available_Items\",\n", "            \"Unorderable_Available_BucketA_Items\",\n", "            \"Unorderable_Available_BucketX_Items\",\n", "        ]\n", "    ].astype(str)\n", "    + \"%\"\n", ")\n", "\n", "# DDFInal"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DDFInal_ndd = DDFInal[DDFInal.store_type == \"NDD\"]\n", "DDFInal_dark = DDFInal[DDFInal.store_type == \"Dark Store\"]\n", "DDFInal_gm = DDFInal[DDFInal.store_type == \"Franchise Store\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# orderable(substate = 1)_uneliable_items\n", "\n", "unreliable_items = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        & ((availability_value.unreliable == \"1\"))\n", "        #  & (availability_value.date_of_activation <= availability_value.order_date)\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ").rename(columns={\"item_id\": \"unreliable_items\"})\n", "\n", "unreliable_items_live = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        & ((availability_value.unreliable == \"1\"))\n", "        # & (availability_value.date_of_activation <= availability_value.order_date)\n", "        & ((availability_value.app_live == 1))\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ").rename(columns={\"item_id\": \"unreliable_live_items\"})\n", "\n", "\n", "unreliable_items_bucketA = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        & ((availability_value.unreliable == \"1\"))\n", "        # & (availability_value.date_of_activation <= availability_value.order_date)\n", "        & (availability_value.buckets == \"A\")\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ").rename(columns={\"item_id\": \"unreliable_BucketA_items\"})\n", "\n", "\n", "unreliable_items_bucketA_live = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        & ((availability_value.unreliable == \"1\"))\n", "        # & (availability_value.date_of_activation <= availability_value.order_date)\n", "        & (availability_value.buckets == \"A\")\n", "        & ((availability_value.app_live == 1))\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ").rename(columns={\"item_id\": \"unreliable_BucketA_live_items\"})\n", "\n", "\n", "unreliable_items_bucketX = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        & ((availability_value.unreliable == \"1\"))\n", "        # & (availability_value.date_of_activation <= availability_value.order_date)\n", "        & (availability_value.bucket_x == \"Y\")\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ").rename(columns={\"item_id\": \"unreliable_BucketX_items\"})\n", "\n", "\n", "unreliable_items_bucketX_live = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        & ((availability_value.unreliable == \"1\"))\n", "        # & (availability_value.date_of_activation <= availability_value.order_date)\n", "        & (availability_value.bucket_x == \"Y\")\n", "        & ((availability_value.app_live == 1))\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ").rename(columns={\"item_id\": \"unreliable_BucketX_live_items\"})\n", "\n", "\n", "Q2 = Q.merge(unreliable_items, how=\"left\", on=[\"store_type\", \"facility_id\", \"facility_name\"])\n", "A0 = Q2.merge(unreliable_items_live, how=\"left\", on=[\"store_type\", \"facility_id\", \"facility_name\"])\n", "A = A0.merge(\n", "    unreliable_items_bucketA,\n", "    how=\"left\",\n", "    on=[\"store_type\", \"facility_id\", \"facility_name\"],\n", ")\n", "B = A.merge(\n", "    unreliable_items_bucketA_live,\n", "    how=\"left\",\n", "    on=[\"store_type\", \"facility_id\", \"facility_name\"],\n", ")\n", "C = B.merge(\n", "    unreliable_items_bucketX,\n", "    how=\"left\",\n", "    on=[\"store_type\", \"facility_id\", \"facility_name\"],\n", ")\n", "D = C.merge(\n", "    unreliable_items_bucketX_live,\n", "    how=\"left\",\n", "    on=[\"store_type\", \"facility_id\", \"facility_name\"],\n", ")\n", "\n", "D[\"Unreliable_Available_Items\"] = round(\n", "    (D[\"unreliable_live_items\"] / D[\"unreliable_items\"]) * 100, 1\n", ")\n", "D[\"Unreliable_Available_BucketA_Items\"] = round(\n", "    (D[\"unreliable_BucketA_live_items\"] / D[\"unreliable_BucketA_items\"]) * 100, 1\n", ")\n", "D[\"Unreliable_Available_BucketX_Items\"] = round(\n", "    (D[\"unreliable_BucketX_live_items\"] / D[\"unreliable_BucketX_items\"]) * 100, 1\n", ")\n", "\n", "D.drop(\n", "    columns=[\n", "        \"unreliable_live_items\",\n", "        \"unreliable_BucketA_live_items\",\n", "        \"unreliable_BucketX_live_items\",\n", "    ],\n", "    inplace=True,\n", ")\n", "\n", "D = D.merge(facility_mapping, on=[\"facility_name\"], how=\"left\")\n", "\n", "D[\"final_facility\"] = np.where(\n", "    D.Facility.isna(),\n", "    D.facility_name,\n", "    D.Facility,\n", ")\n", "\n", "DFInal = D[\n", "    [\n", "        \"store_type\",\n", "        \"Facility\",\n", "        \"total_item\",\n", "        \"total_active_item\",\n", "        \"unreliable_items\",\n", "        \"unreliable_BucketA_items\",\n", "        \"unreliable_BucketX_items\",\n", "        \"Unreliable_Available_Items\",\n", "        \"Unreliable_Available_BucketA_Items\",\n", "        \"Unreliable_Available_BucketX_Items\",\n", "    ]\n", "]\n", "\n", "\n", "DFInal.fillna(0, inplace=True)\n", "\n", "DFInal[\n", "    [\n", "        \"total_item\",\n", "        \"total_active_item\",\n", "        \"unreliable_items\",\n", "        \"unreliable_BucketA_items\",\n", "        \"unreliable_BucketX_items\",\n", "    ]\n", "] = DFInal[\n", "    [\n", "        \"total_item\",\n", "        \"total_active_item\",\n", "        \"unreliable_items\",\n", "        \"unreliable_BucketA_items\",\n", "        \"unreliable_BucketX_items\",\n", "    ]\n", "].astype(\n", "    int\n", ")\n", "\n", "\n", "DRollup = round(\n", "    DFInal.groupby([\"store_type\", \"Facility\"]).agg(\n", "        {\n", "            \"total_item\": \"sum\",\n", "            \"total_active_item\": \"sum\",\n", "            \"unreliable_items\": \"sum\",\n", "            \"unreliable_BucketA_items\": \"sum\",\n", "            \"unreliable_BucketX_items\": \"sum\",\n", "            \"Unreliable_Available_Items\": \"mean\",\n", "            \"Unreliable_Available_BucketA_Items\": \"mean\",\n", "            \"Unreliable_Available_BucketX_Items\": \"mean\",\n", "        }\n", "    ),\n", "    1,\n", ").reset_index()\n", "\n", "DRollup[\"Facility\"] = \"Pan India\"\n", "\n", "DRollup = round(\n", "    DRollup.groupby([\"store_type\", \"Facility\"]).agg(\n", "        {\n", "            \"total_item\": \"sum\",\n", "            \"total_active_item\": \"sum\",\n", "            \"unreliable_items\": \"sum\",\n", "            \"unreliable_BucketA_items\": \"sum\",\n", "            \"unreliable_BucketX_items\": \"sum\",\n", "            \"Unreliable_Available_Items\": \"mean\",\n", "            \"Unreliable_Available_BucketA_Items\": \"mean\",\n", "            \"Unreliable_Available_BucketX_Items\": \"mean\",\n", "        }\n", "    ),\n", "    1,\n", ").reset_index()\n", "\n", "DFInal = DFInal.append(DRollup)\n", "\n", "DFInal[\n", "    [\n", "        \"Unreliable_Available_Items\",\n", "        \"Unreliable_Available_BucketA_Items\",\n", "        \"Unreliable_Available_BucketX_Items\",\n", "    ]\n", "] = (\n", "    DFInal[\n", "        [\n", "            \"Unreliable_Available_Items\",\n", "            \"Unreliable_Available_BucketA_Items\",\n", "            \"Unreliable_Available_BucketX_Items\",\n", "        ]\n", "    ].astype(str)\n", "    + \"%\"\n", ")\n", "\n", "# DFInal"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DFInal_ndd = DFInal[DFInal.store_type == \"NDD\"]\n", "DFInal_dark = DFInal[DFInal.store_type == \"Dark Store\"]\n", "DFInal_gm = DFInal[DFInal.store_type == \"Franchise Store\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Code for availability report"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Facility level availability calculation on overall assortment "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# oderable item\n", "total_item = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        #         & (availability_value.unreliable != \"1\")\n", "        #         & (availability_value.unorderable != \"1\")\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\", \"zone\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")\n", "total_item = total_item.rename(columns={\"item_id\": \"total_item\"})\n", "\n", "total_item.head(2)\n", "\n", "\n", "# orderable eligle item\n", "active_item = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        #         & (\n", "        #             (\n", "        #                 (availability_value.unreliable != \"1\")\n", "        #                 & (availability_value.unorderable != \"1\")\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #         )\n", "        #         & (\n", "        #             (availability_value.date_of_activation <= availability_value.order_date)\n", "        #             | (availability_value.inv_flag == 1)\n", "        #         )\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")\n", "\n", "active_item = active_item.rename(columns={\"item_id\": \"total_active_item\"})\n", "\n", "\n", "total_data = total_item.merge(\n", "    active_item, on=[\"store_type\", \"facility_id\", \"facility_name\"], how=\"left\"\n", ")\n", "\n", "\n", "availability_value[\"app_live\"] = availability_value.app_live.astype(int)\n", "availability_value[\"inv_flag\"] = availability_value.inv_flag.astype(int)\n", "\n", "# All oderable eligle live items\n", "\n", "availability_item = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        #         & (\n", "        #             (\n", "        #                 (availability_value.unreliable != \"1\")\n", "        #                 & (availability_value.unorderable != \"1\")\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #         )\n", "        #         & (\n", "        #             (availability_value.date_of_activation <= availability_value.order_date)\n", "        #             | (availability_value.inv_flag == 1)\n", "        #        )\n", "        & ((availability_value.app_live == 1))\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")\n", "\n", "availability_item = availability_item.rename(columns={\"item_id\": \"available_item\"})\n", "\n", "availability_data = total_data.merge(\n", "    availability_item, on=[\"store_type\", \"facility_id\", \"facility_name\"], how=\"left\"\n", ")\n", "\n", "\n", "availability_data[\"Availability\"] = round(\n", "    ((availability_data.available_item / availability_data.total_active_item) * 100), 1\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Facility level availability calculation on Bucket A and Bucket X"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Bucket A\n", "availability_bucketA = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        #         & (\n", "        #             (\n", "        #                 (availability_value.unreliable != \"1\")\n", "        #                 & (availability_value.unorderable != \"1\")\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #         )\n", "        #         & (\n", "        #             (availability_value.date_of_activation <= availability_value.order_date)\n", "        #             | (availability_value.inv_flag == 1)\n", "        #         )\n", "        & ((availability_value.app_live == 1))\n", "        & (availability_value.buckets == \"A\")\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")\n", "\n", "\n", "availability_bucketA = availability_bucketA.rename(columns={\"item_id\": \"bucketA_availability\"})\n", "\n", "\n", "availability_bucketA_total = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        #         & (\n", "        #             (\n", "        #                 (availability_value.unreliable != \"1\")\n", "        #                 & (availability_value.unorderable != \"1\")\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #         )\n", "        #         & (\n", "        #             (availability_value.date_of_activation <= availability_value.order_date)\n", "        #             | (availability_value.inv_flag == 1)\n", "        #         )\n", "        & (availability_value.buckets == \"A\")\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")\n", "\n", "\n", "availability_bucketA_total = availability_bucketA_total.rename(\n", "    columns={\"item_id\": \"bucketA_total_item\"}\n", ")\n", "\n", "bucket_data = availability_data.merge(\n", "    availability_bucketA_total,\n", "    on=[\"store_type\", \"facility_id\", \"facility_name\"],\n", "    how=\"left\",\n", ")\n", "\n", "bucket_availability = bucket_data.merge(\n", "    availability_bucketA, on=[\"store_type\", \"facility_id\", \"facility_name\"], how=\"left\"\n", ")\n", "\n", "\n", "bucket_availability[\"BucketA_Availability\"] = round(\n", "    ((bucket_availability.bucketA_availability / bucket_availability.bucketA_total_item) * 100),\n", "    1,\n", ")\n", "\n", "\n", "## Bucket X\n", "\n", "availability_bucketX_total = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        #         & (\n", "        #             (\n", "        #                 (availability_value.unreliable != \"1\")\n", "        #                 & (availability_value.unorderable != \"1\")\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #         )\n", "        #         & (\n", "        #             (availability_value.date_of_activation <= availability_value.order_date)\n", "        #             | (availability_value.inv_flag == 1)\n", "        #         )\n", "        & (availability_value.bucket_x == \"Y\")\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")\n", "\n", "availability_bucketX_total = availability_bucketX_total.rename(\n", "    columns={\"item_id\": \"bucketX_total_item\"}\n", ")\n", "\n", "\n", "availability_bucketX = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        #         & (\n", "        #             (\n", "        #                 (availability_value.unreliable != \"1\")\n", "        #                 & (availability_value.unorderable != \"1\")\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #         )\n", "        #         & (\n", "        #             (availability_value.date_of_activation <= availability_value.order_date)\n", "        #             | (availability_value.inv_flag == 1)\n", "        #         )\n", "        & ((availability_value.app_live == 1))\n", "        & (availability_value.bucket_x == \"Y\")\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")\n", "\n", "\n", "availability_bucketX = availability_bucketX.rename(columns={\"item_id\": \"bucketX_availability\"})\n", "\n", "bucketX_data = bucket_availability.merge(\n", "    availability_bucketX_total,\n", "    on=[\"store_type\", \"facility_id\", \"facility_name\"],\n", "    how=\"left\",\n", ")\n", "\n", "bucketX_availability = bucketX_data.merge(\n", "    availability_bucketX, on=[\"store_type\", \"facility_id\", \"facility_name\"], how=\"left\"\n", ")\n", "\n", "bucketX_availability[\"BucketX_Availability\"] = round(\n", "    ((bucketX_availability.bucketX_availability / bucketX_availability.bucketX_total_item) * 100),\n", "    1,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Facility level availability calculation on overall GB, GB grocery and GB FMCG"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# GB\n", "\n", "IS_GB_TOTAL = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        #         & (\n", "        #             (\n", "        #                 (availability_value.unreliable != \"1\")\n", "        #                 & (availability_value.unorderable != \"1\")\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #         )\n", "        #         & (\n", "        #             (availability_value.date_of_activation <= availability_value.order_date)\n", "        #             | (availability_value.inv_flag == 1)\n", "        #         )\n", "        & (availability_value.is_gb == \"TRUE\")\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")\n", "\n", "\n", "IS_GB_TOTAL = IS_GB_TOTAL.rename(columns={\"item_id\": \"total_is_gb\"})\n", "\n", "\n", "IS_GB_availability = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        #         & (\n", "        #             (\n", "        #                 (availability_value.unreliable != \"1\")\n", "        #                 & (availability_value.unorderable != \"1\")\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #         )\n", "        #         & (\n", "        #             (availability_value.date_of_activation <= availability_value.order_date)\n", "        #             | (availability_value.inv_flag == 1)\n", "        #         )\n", "        & ((availability_value.app_live == 1))\n", "        & (availability_value.is_gb == \"TRUE\")\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")\n", "\n", "IS_GB_availability = IS_GB_availability.rename(columns={\"item_id\": \"availability_is_gb\"})\n", "\n", "\n", "is_gb_total = bucketX_availability.merge(\n", "    IS_GB_TOTAL, on=[\"store_type\", \"facility_id\", \"facility_name\"], how=\"left\"\n", ")\n", "\n", "\n", "is_gb_avail = is_gb_total.merge(\n", "    IS_GB_availability, on=[\"store_type\", \"facility_id\", \"facility_name\"], how=\"left\"\n", ")\n", "\n", "is_gb_avail[\"gb_availability\"] = round(\n", "    ((is_gb_avail.availability_is_gb / is_gb_avail.total_is_gb) * 100), 1\n", ")\n", "\n", "# GB Grocery\n", "\n", "IS_GB_TOTAL_Grocery = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        #         & (\n", "        #             (\n", "        #                 (availability_value.unreliable != \"1\")\n", "        #                 & (availability_value.unorderable != \"1\")\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #         )\n", "        #         & (\n", "        #             (availability_value.date_of_activation <= availability_value.order_date)\n", "        #             | (availability_value.inv_flag == 1)\n", "        #         )\n", "        & (availability_value.is_gb == \"TRUE\")\n", "        & (availability_value.l0_id == 16)\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")\n", "\n", "\n", "IS_GB_TOTAL_Grocery = IS_GB_TOTAL_Grocery.rename(columns={\"item_id\": \"total_is_gb_grocery\"})\n", "\n", "\n", "IS_GB_TOTAL_grocery_live = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        #         & (\n", "        #             (\n", "        #                 (availability_value.unreliable != \"1\")\n", "        #                 & (availability_value.unorderable != \"1\")\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #         )\n", "        #         & (\n", "        #             (availability_value.date_of_activation <= availability_value.order_date)\n", "        #             | (availability_value.inv_flag == 1)\n", "        #         )\n", "        & (availability_value.is_gb == \"TRUE\")\n", "        & (availability_value.l0_id == 16)\n", "        & ((availability_value.app_live == 1))\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")\n", "\n", "\n", "IS_GB_TOTAL_grocery_live = IS_GB_TOTAL_grocery_live.rename(\n", "    columns={\"item_id\": \"availability_is_gb_grocery\"}\n", ")\n", "\n", "IS_GB_TOTAL_grocery_stepal = is_gb_avail.merge(\n", "    IS_GB_TOTAL_Grocery, on=[\"store_type\", \"facility_id\", \"facility_name\"], how=\"left\"\n", ")\n", "\n", "IS_GB_TOTAL_grocery_stepal_live = IS_GB_TOTAL_grocery_stepal.merge(\n", "    IS_GB_TOTAL_grocery_live,\n", "    on=[\"store_type\", \"facility_id\", \"facility_name\"],\n", "    how=\"left\",\n", ")\n", "\n", "\n", "# GB FMCG\n", "\n", "gb_fmcg = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        #         & (\n", "        #             (\n", "        #                 (availability_value.unreliable != \"1\")\n", "        #                 & (availability_value.unorderable != \"1\")\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #         )\n", "        #         & (\n", "        #             (availability_value.date_of_activation <= availability_value.order_date)\n", "        #             | (availability_value.inv_flag == 1)\n", "        #         )\n", "        & (availability_value.is_gb == \"TRUE\")\n", "        & (\n", "            #             ~availability_value.l0.isin(\n", "            #                 [\"Grocery & Staples\", \"Home and Kitchen\", \"Furnishing & Home Needs\"]\n", "            #             )\n", "            ~availability_value.l0_id.isin([1379, 1047, 16, 1878, 1879])\n", "        )\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")\n", "\n", "\n", "gb_fmcg = gb_fmcg.rename(columns={\"item_id\": \"total_fmcg_gb\"})\n", "\n", "\n", "gb_fmcg_live = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        #         & (\n", "        #             (\n", "        #                 (availability_value.unreliable != \"1\")\n", "        #                 & (availability_value.unorderable != \"1\")\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #         )\n", "        #         & (\n", "        #             (availability_value.date_of_activation <= availability_value.order_date)\n", "        #             | (availability_value.inv_flag == 1)\n", "        #         )\n", "        & (availability_value.is_gb == \"TRUE\")\n", "        & ((availability_value.app_live == 1))\n", "        & (~availability_value.l0_id.isin([1379, 1047, 16, 1878, 1879]))\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")\n", "\n", "\n", "gb_fmcg_live = gb_fmcg_live.rename(columns={\"item_id\": \"total_fmcg_gb_live\"})\n", "\n", "gb_fmcg_total = IS_GB_TOTAL_grocery_stepal_live.merge(\n", "    gb_fmcg, on=[\"store_type\", \"facility_id\", \"facility_name\"], how=\"left\"\n", ")\n", "\n", "gb_fmcg_total_live = gb_fmcg_total.merge(\n", "    gb_fmcg_live, on=[\"store_type\", \"facility_id\", \"facility_name\"], how=\"left\"\n", ")\n", "\n", "\n", "# unorderable but live\n", "\n", "incative_live = (\n", "    availability_value[\n", "        ((availability_value.app_live == 1))\n", "        #         & (availability_value.date_of_activation <= availability_value.order_date)\n", "        #         & (~availability_value.new_substate.isin([1]))\n", "        &\n", "        #         (\n", "        #             (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.inv_flag <= 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag <= 0)\n", "        #             )\n", "        #             |\n", "        (availability_value.new_substate != 1)\n", "        #         )\n", "    ]\n", "    .groupby([\"store_type\", \"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")\n", "\n", "incative_live = incative_live.rename(columns={\"item_id\": \"inactive_live_items\"})\n", "\n", "\n", "final_data_for_facility = gb_fmcg_total_live.merge(\n", "    incative_live, on=[\"store_type\", \"facility_id\", \"facility_name\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_for_facility[\"GB_staple_availability\"] = round(\n", "    (\n", "        final_data_for_facility.availability_is_gb_grocery\n", "        / final_data_for_facility.total_is_gb_grocery\n", "    )\n", "    * 100,\n", "    1,\n", ")\n", "\n", "\n", "final_data_for_facility[\"GB_fmcg_availability\"] = round(\n", "    (final_data_for_facility.total_fmcg_gb_live / final_data_for_facility.total_fmcg_gb) * 100,\n", "    1,\n", ")\n", "\n", "final_data_for_facility = final_data_for_facility.merge(\n", "    continous_availability_cons, on=[\"store_type\", \"facility_id\"], how=\"left\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### unorderable_items AND unreliable_items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_for_facility = final_data_for_facility.merge(\n", "    unorderable_items, on=[\"store_type\", \"facility_id\", \"facility_name\"], how=\"left\"\n", ")\n", "\n", "final_data_for_facility = final_data_for_facility.merge(\n", "    unreliable_items, on=[\"store_type\", \"facility_id\", \"facility_name\"], how=\"left\"\n", ")\n", "\n", "final_data_for_facility = final_data_for_facility.merge(\n", "    unorderable_unreliable_items,\n", "    on=[\"store_type\", \"facility_id\", \"facility_name\"],\n", "    how=\"left\",\n", ")\n", "\n", "final_data_for_facility[\n", "    [\"unorderable_items\", \"unreliable_items\", \"unorderable_unreliable_items\"]\n", "] = (\n", "    final_data_for_facility[\n", "        [\"unorderable_items\", \"unreliable_items\", \"unorderable_unreliable_items\"]\n", "    ]\n", "    .fillna(0)\n", "    .astype(int)\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Availability calculation on zone and pan india level"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Pan India\n", "\n", "pan_india = final_data_for_facility[\n", "    [\n", "        \"store_type\",\n", "        \"facility_name\",\n", "        \"zone\",\n", "        \"total_item\",\n", "        \"total_active_item\",\n", "        \"available_item\",\n", "        \"Availability\",\n", "        \"bucketA_total_item\",\n", "        \"bucketA_availability\",\n", "        \"BucketA_Availability\",\n", "        \"bucketX_total_item\",\n", "        \"bucketX_availability\",\n", "        \"BucketX_Availability\",\n", "        \"total_is_gb\",\n", "        \"availability_is_gb\",\n", "        \"gb_availability\",\n", "        \"total_is_gb_grocery\",\n", "        \"availability_is_gb_grocery\",\n", "        \"GB_staple_availability\",\n", "        \"total_fmcg_gb\",\n", "        \"total_fmcg_gb_live\",\n", "        \"GB_fmcg_availability\",\n", "        \"inactive_live_items\",\n", "        \"total_continous_availabile_item\",\n", "        \"total_continous_eligible_item\",\n", "        \"unorderable_items\",\n", "        \"unreliable_items\",\n", "        \"unorderable_unreliable_items\",\n", "    ]\n", "]\n", "\n", "\n", "pan_india[\"facility_name\"] = \"Pan India\"\n", "\n", "pan_india_rollup = round(\n", "    pan_india.groupby([\"store_type\", \"facility_name\"]).agg(\n", "        {\n", "            \"total_item\": \"sum\",\n", "            \"total_active_item\": \"sum\",\n", "            \"available_item\": \"sum\",\n", "            \"inactive_live_items\": \"sum\",\n", "            \"bucketX_total_item\": \"sum\",\n", "            \"bucketA_total_item\": \"sum\",\n", "            \"bucketA_availability\": \"sum\",\n", "            \"bucketX_availability\": \"sum\",\n", "            \"total_is_gb\": \"sum\",\n", "            \"availability_is_gb\": \"sum\",\n", "            \"total_is_gb_grocery\": \"sum\",\n", "            \"availability_is_gb_grocery\": \"sum\",\n", "            \"total_fmcg_gb\": \"sum\",\n", "            \"total_fmcg_gb_live\": \"sum\",\n", "            \"inactive_live_items\": \"sum\",\n", "            \"total_continous_availabile_item\": \"sum\",\n", "            \"total_continous_eligible_item\": \"sum\",\n", "            \"unorderable_items\": \"sum\",\n", "            \"unreliable_items\": \"sum\",\n", "            \"unorderable_unreliable_items\": \"sum\",\n", "        }\n", "    ),\n", "    1,\n", ").reset_index()\n", "\n", "\n", "zone_rollup = round(\n", "    final_data_for_facility.groupby([\"store_type\", \"zone\"]).agg(\n", "        {\n", "            \"total_item\": \"sum\",\n", "            \"total_active_item\": \"sum\",\n", "            \"available_item\": \"sum\",\n", "            \"inactive_live_items\": \"sum\",\n", "            \"bucketX_total_item\": \"sum\",\n", "            \"total_continous_availabile_item\": \"sum\",\n", "            \"unorderable_items\": \"sum\",\n", "            \"unreliable_items\": \"sum\",\n", "            \"unorderable_unreliable_items\": \"sum\",\n", "            \"bucketA_total_item\": \"sum\",\n", "            \"bucketA_availability\": \"sum\",\n", "            \"bucketX_availability\": \"sum\",\n", "            \"total_is_gb\": \"sum\",\n", "            \"availability_is_gb\": \"sum\",\n", "            \"total_is_gb_grocery\": \"sum\",\n", "            \"availability_is_gb_grocery\": \"sum\",\n", "            \"total_fmcg_gb\": \"sum\",\n", "            \"total_fmcg_gb_live\": \"sum\",\n", "            \"inactive_live_items\": \"sum\",\n", "            \"total_continous_availabile_item\": \"sum\",\n", "            \"total_continous_eligible_item\": \"sum\",\n", "        }\n", "    ),\n", "    1,\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_for_facility.available_item = np.where(\n", "    final_data_for_facility.available_item.isna(),\n", "    0,\n", "    final_data_for_facility.available_item,\n", ")\n", "final_data_for_facility.total_active_item = np.where(\n", "    final_data_for_facility.total_active_item.isna(),\n", "    0,\n", "    final_data_for_facility.total_active_item,\n", ")\n", "\n", "pan_india_rollup[\"Availability\"] = round(\n", "    ((pan_india_rollup.available_item * 100.00 / pan_india_rollup.total_active_item)),\n", "    1,\n", ")\n", "\n", "zone_rollup[\"Availability\"] = round(\n", "    zone_rollup[\"available_item\"] * 100.00 / zone_rollup[\"total_active_item\"], 1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_for_facility.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_for_facility.bucketA_availability = np.where(\n", "    final_data_for_facility.bucketA_availability.isna(),\n", "    0,\n", "    final_data_for_facility.bucketA_availability,\n", ")\n", "final_data_for_facility.bucketA_total_item = np.where(\n", "    final_data_for_facility.bucketA_total_item.isna(),\n", "    0,\n", "    final_data_for_facility.bucketA_total_item,\n", ")\n", "\n", "pan_india_rollup[\"BucketA_Availability\"] = round(\n", "    ((pan_india_rollup.bucketA_availability * 100.00 / pan_india_rollup.bucketA_total_item)),\n", "    1,\n", ")\n", "zone_rollup[\"BucketA_Availability\"] = round(\n", "    zone_rollup[\"bucketA_availability\"] * 100.00 / zone_rollup[\"bucketA_total_item\"], 1\n", ")\n", "\n", "final_data_for_facility.bucketX_availability = np.where(\n", "    final_data_for_facility.bucketX_availability.isna(),\n", "    0,\n", "    final_data_for_facility.bucketX_availability,\n", ")\n", "\n", "final_data_for_facility.bucketX_total_item = np.where(\n", "    final_data_for_facility.bucketX_total_item.isna(),\n", "    0,\n", "    final_data_for_facility.bucketX_total_item,\n", ")\n", "\n", "pan_india_rollup[\"BucketX_Availability\"] = round(\n", "    ((pan_india_rollup.bucketX_availability * 100.00 / pan_india_rollup.bucketX_total_item)),\n", "    1,\n", ")\n", "zone_rollup[\"BucketX_Availability\"] = round(\n", "    zone_rollup[\"bucketX_availability\"] * 100.00 / zone_rollup[\"bucketX_total_item\"], 1\n", ")\n", "\n", "final_data_for_facility.total_is_gb = np.where(\n", "    final_data_for_facility.total_is_gb.isna(), 0, final_data_for_facility.total_is_gb\n", ")\n", "final_data_for_facility.availability_is_gb = np.where(\n", "    final_data_for_facility.availability_is_gb.isna(),\n", "    0,\n", "    final_data_for_facility.availability_is_gb,\n", ")\n", "\n", "pan_india_rollup[\"gb_availability\"] = round(\n", "    ((pan_india_rollup.availability_is_gb * 100.00 / pan_india_rollup.total_is_gb)),\n", "    1,\n", ")\n", "zone_rollup[\"gb_availability\"] = round(\n", "    zone_rollup[\"availability_is_gb\"] * 100.00 / zone_rollup[\"total_is_gb\"], 1\n", ")\n", "\n", "\n", "final_data_for_facility.total_is_gb_grocery = np.where(\n", "    final_data_for_facility.total_is_gb_grocery.isna(),\n", "    0,\n", "    final_data_for_facility.total_is_gb_grocery,\n", ")\n", "final_data_for_facility.availability_is_gb_grocery = np.where(\n", "    final_data_for_facility.availability_is_gb_grocery.isna(),\n", "    0,\n", "    final_data_for_facility.availability_is_gb_grocery,\n", ")\n", "pan_india_rollup[\"GB_staple_availability\"] = round(\n", "    ((pan_india_rollup.availability_is_gb_grocery * 100.00 / pan_india_rollup.total_is_gb_grocery)),\n", "    1,\n", ")\n", "zone_rollup[\"GB_staple_availability\"] = round(\n", "    zone_rollup[\"availability_is_gb_grocery\"] * 100.00 / zone_rollup[\"total_is_gb_grocery\"],\n", "    1,\n", ")\n", "\n", "\n", "final_data_for_facility.total_fmcg_gb = np.where(\n", "    final_data_for_facility.total_fmcg_gb.isna(),\n", "    0,\n", "    final_data_for_facility.total_fmcg_gb,\n", ")\n", "final_data_for_facility.total_fmcg_gb_live = np.where(\n", "    final_data_for_facility.total_fmcg_gb_live.isna(),\n", "    0,\n", "    final_data_for_facility.total_fmcg_gb_live,\n", ")\n", "pan_india_rollup[\"GB_fmcg_availability\"] = round(\n", "    ((pan_india_rollup.total_fmcg_gb_live * 100.00 / pan_india_rollup.total_fmcg_gb)),\n", "    1,\n", ")\n", "zone_rollup[\"GB_fmcg_availability\"] = round(\n", "    zone_rollup[\"total_fmcg_gb_live\"] * 100.00 / zone_rollup[\"total_fmcg_gb\"], 1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_for_facility[\"total_continous_availabile_item\"] = round(\n", "    final_data_for_facility[\"total_continous_availabile_item\"]\n", "    * 100\n", "    / final_data_for_facility[\"total_continous_eligible_item\"],\n", "    1,\n", ")\n", "pan_india_rollup[\"total_continous_availabile_item\"] = round(\n", "    pan_india_rollup[\"total_continous_availabile_item\"]\n", "    * 100\n", "    / pan_india_rollup[\"total_continous_eligible_item\"],\n", "    1,\n", ")\n", "\n", "zone_rollup[\"total_continous_availabile_item\"] = round(\n", "    zone_rollup[\"total_continous_availabile_item\"]\n", "    * 100\n", "    / zone_rollup[\"total_continous_eligible_item\"],\n", "    1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["zone_rollup[\"facility_name\"] = np.where(zone_rollup[\"zone\"].isna(), \"\", zone_rollup[\"zone\"])\n", "\n", "zone_rollup[\"rank\"] = np.where(\n", "    zone_rollup[\"zone\"] == \"North\",\n", "    1,\n", "    np.where(\n", "        zone_rollup[\"zone\"] == \"South\",\n", "        2,\n", "        np.where(\n", "            zone_rollup[\"zone\"] == \"West\",\n", "            3,\n", "            np.where(zone_rollup[\"zone\"] == \"East\", 4, 5),\n", "        ),\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["zone_rollup = zone_rollup.sort_values(by=[\"rank\"], ascending=True).reset_index()\n", "zone_rollup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_for_facility[\"rank\"] = np.where(\n", "    final_data_for_facility[\"zone\"] == \"North\",\n", "    1,\n", "    np.where(\n", "        final_data_for_facility[\"zone\"] == \"South\",\n", "        2,\n", "        np.where(\n", "            final_data_for_facility[\"zone\"] == \"West\",\n", "            3,\n", "            np.where(final_data_for_facility[\"zone\"] == \"East\", 4, 5),\n", "        ),\n", "    ),\n", ")\n", "\n", "final_data_for_facility = final_data_for_facility.sort_values(\n", "    by=[\"rank\", \"facility_name\"], ascending=True\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_for_facility.drop(\n", "    columns=[\n", "        \"bucketA_total_item\",\n", "        \"bucketA_availability\",\n", "        \"bucketX_availability\",\n", "        \"total_is_gb\",\n", "        \"availability_is_gb\",\n", "        \"facility_id\",\n", "        \"total_fmcg_gb_live\",\n", "        \"total_fmcg_gb\",\n", "        \"availability_is_gb_grocery\",\n", "        \"total_is_gb_grocery\",\n", "        \"total_continous_eligible_item\",\n", "        \"rank\",\n", "        \"index\",\n", "    ],\n", "    inplace=True,\n", ")\n", "\n", "\n", "pan_india.drop(\n", "    columns=[\n", "        \"bucketA_total_item\",\n", "        \"bucketA_availability\",\n", "        \"bucketX_availability\",\n", "        \"total_is_gb\",\n", "        \"availability_is_gb\",\n", "        \"total_fmcg_gb_live\",\n", "        \"total_fmcg_gb\",\n", "        \"availability_is_gb_grocery\",\n", "        \"total_is_gb_grocery\",\n", "        \"total_continous_eligible_item\",\n", "    ],\n", "    inplace=True,\n", ")\n", "zone_rollup.drop(\n", "    columns=[\n", "        \"bucketA_total_item\",\n", "        \"bucketA_availability\",\n", "        \"bucketX_availability\",\n", "        \"total_is_gb\",\n", "        \"availability_is_gb\",\n", "        \"total_fmcg_gb_live\",\n", "        \"total_fmcg_gb\",\n", "        \"availability_is_gb_grocery\",\n", "        \"total_is_gb_grocery\",\n", "        \"total_continous_eligible_item\",\n", "        \"rank\",\n", "        \"index\",\n", "    ],\n", "    inplace=True,\n", ")\n", "\n", "\n", "final_facility_1 = pan_india_rollup.append(zone_rollup)\n", "final_facility = final_facility_1.append(final_data_for_facility)\n", "\n", "final_facility = final_facility.merge(facility_mapping, on=[\"facility_name\"], how=\"left\")\n", "\n", "final_facility[\"final_facility\"] = np.where(\n", "    final_facility.Facility.isna(),\n", "    final_facility.facility_name,\n", "    final_facility.Facility,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Category availability"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ref_dict = {\n", "    \"Biscuits, Namkeen & Chocolates\": \"Biscuits, Snacks & Chocolates\",\n", "    \"Fresh & Frozen Food\": \"Frozen Food\",\n", "    \"Fresh and Frozen Food\": \"Frozen Food\",\n", "    \"Fruits & Vegetables\": \"Vegetables & Fruits\",\n", "    \"Household Needs\": \"Household Items\",\n", "    \"Best Value\": \"Specials\",\n", "}\n", "for key, value in ref_dict.items():\n", "    availability_value[\"l0\"] = availability_value[\"l0\"].replace(key, value)\n", "\n", "\n", "availability_value[\"l0_id\"] = availability_value.l0_id.astype(int)\n", "\n", "\n", "ref_dict = {1558: 13, 1560: 18, 909: 343}\n", "for key, value in ref_dict.items():\n", "    availability_value[\"l0_id\"] = availability_value[\"l0_id\"].replace(key, value)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Category level availability calculation on overall assortment "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_item_1 = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        #         & (availability_value.unreliable != \"1\")\n", "        #         & (availability_value.unorderable != \"1\")\n", "    ]\n", "    .groupby([\"store_type\", \"l0\", \"l0_id\"])[[\"item_id\"]]\n", "    .count()\n", "    .reset_index()\n", ")\n", "total_item_1 = total_item_1.rename(columns={\"item_id\": \"total_item\"})\n", "\n", "\n", "active_item_1 = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        #         & (\n", "        #             (\n", "        #                 (availability_value.unreliable != \"1\")\n", "        #                 & (availability_value.unorderable != \"1\")\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #         )\n", "        #         & (\n", "        #             (availability_value.date_of_activation <= availability_value.order_date)\n", "        #             | (availability_value.inv_flag == 1)\n", "        #         )\n", "    ]\n", "    .groupby([\"store_type\", \"l0\", \"l0_id\"])[[\"item_id\"]]\n", "    .count()\n", "    .reset_index()\n", ")\n", "active_item_1 = active_item_1.rename(columns={\"item_id\": \"total_active_item\"})\n", "\n", "\n", "availability_data_1 = total_item_1.merge(\n", "    active_item_1, on=[\"store_type\", \"l0\", \"l0_id\"], how=\"left\"\n", ")\n", "\n", "availability_item_1 = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        #         & (\n", "        #             (\n", "        #                 (availability_value.unreliable != \"1\")\n", "        #                 & (availability_value.unorderable != \"1\")\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #         )\n", "        #         & (\n", "        #             (availability_value.date_of_activation <= availability_value.order_date)\n", "        #             | (availability_value.inv_flag == 1)\n", "        #         )\n", "        & ((availability_value.app_live == 1))\n", "    ]\n", "    .groupby([\"store_type\", \"l0\", \"l0_id\"])[[\"item_id\"]]\n", "    .count()\n", "    .reset_index()\n", ")\n", "availability_item_1 = availability_item_1.rename(columns={\"item_id\": \"available_item\"})\n", "\n", "\n", "availability_data_1 = availability_data_1.merge(\n", "    availability_item_1, on=[\"store_type\", \"l0\", \"l0_id\"], how=\"left\"\n", ")\n", "\n", "\n", "availability_data_1[\"Availability\"] = round(\n", "    ((availability_data_1.available_item / availability_data_1.total_active_item) * 100),\n", "    1,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Category level availability calculation on Bucket A and Bucket X"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Bucket A\n", "availability_bucketA_1 = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        #         & (\n", "        #             (\n", "        #                 (availability_value.unreliable != \"1\")\n", "        #                 & (availability_value.unorderable != \"1\")\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #         )\n", "        #         & (\n", "        #             (availability_value.date_of_activation <= availability_value.order_date)\n", "        #             | (availability_value.inv_flag == 1)\n", "        #         )\n", "        & ((availability_value.app_live == 1))\n", "        & (availability_value.buckets == \"A\")\n", "    ]\n", "    .groupby([\"store_type\", \"l0_id\", \"l0\"])[[\"item_id\"]]\n", "    .count()\n", "    .reset_index()\n", ")\n", "\n", "availability_bucketA_1 = availability_bucketA_1.rename(columns={\"item_id\": \"bucketA_availability\"})\n", "\n", "availability_bucketA_total_1 = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        #         & (\n", "        #             (\n", "        #                 (availability_value.unreliable != \"1\")\n", "        #                 & (availability_value.unorderable != \"1\")\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #         )\n", "        #         & (\n", "        #             (availability_value.date_of_activation <= availability_value.order_date)\n", "        #             | (availability_value.inv_flag == 1)\n", "        #         )\n", "        & (availability_value.buckets == \"A\")\n", "    ]\n", "    .groupby([\"store_type\", \"l0_id\", \"l0\"])[[\"item_id\"]]\n", "    .count()\n", "    .reset_index()\n", ")\n", "\n", "availability_bucketA_total_1 = availability_bucketA_total_1.rename(\n", "    columns={\"item_id\": \"bucketA_total_item\"}\n", ")\n", "\n", "bucket_data_1 = availability_data_1.merge(\n", "    availability_bucketA_total_1, on=[\"store_type\", \"l0_id\", \"l0\"], how=\"left\"\n", ")\n", "\n", "bucket_availability_1 = bucket_data_1.merge(\n", "    availability_bucketA_1, on=[\"store_type\", \"l0_id\", \"l0\"], how=\"left\"\n", ")\n", "\n", "bucket_availability_1[\"BucketA_Availability\"] = round(\n", "    ((bucket_availability_1.bucketA_availability / bucket_availability_1.bucketA_total_item) * 100),\n", "    1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Bucket X\n", "\n", "availability_bucketX_total_1 = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        #         & (\n", "        #             (\n", "        #                 (availability_value.unreliable != \"1\")\n", "        #                 & (availability_value.unorderable != \"1\")\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #         )\n", "        #         & (\n", "        #             (availability_value.date_of_activation <= availability_value.order_date)\n", "        #             | (availability_value.inv_flag == 1)\n", "        #         )\n", "        & (availability_value.bucket_x == \"Y\")\n", "    ]\n", "    .groupby([\"store_type\", \"l0_id\", \"l0\"])[[\"item_id\"]]\n", "    .count()\n", "    .reset_index()\n", ")\n", "\n", "availability_bucketX_total_1 = availability_bucketX_total_1.rename(\n", "    columns={\"item_id\": \"bucketX_total_item\"}\n", ")\n", "\n", "availability_bucketX_1 = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        #         & (\n", "        #             (\n", "        #                 (availability_value.unreliable != \"1\")\n", "        #                 & (availability_value.unorderable != \"1\")\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #         )\n", "        #         & (\n", "        #             (availability_value.date_of_activation <= availability_value.order_date)\n", "        #             | (availability_value.inv_flag == 1)\n", "        #         )\n", "        & ((availability_value.app_live == 1))\n", "        & (availability_value.bucket_x == \"Y\")\n", "    ]\n", "    .groupby([\"store_type\", \"l0_id\", \"l0\"])[[\"item_id\"]]\n", "    .count()\n", "    .reset_index()\n", ")\n", "\n", "availability_bucketX_1 = availability_bucketX_1.rename(columns={\"item_id\": \"bucketX_availability\"})\n", "\n", "bucketX_data_1 = bucket_availability_1.merge(\n", "    availability_bucketX_total_1, on=[\"store_type\", \"l0_id\", \"l0\"], how=\"left\"\n", ")\n", "\n", "bucketX_availability_1 = bucketX_data_1.merge(\n", "    availability_bucketX_1, on=[\"store_type\", \"l0_id\", \"l0\"], how=\"left\"\n", ")\n", "\n", "bucketX_availability_1[\"BucketX_Availability\"] = round(\n", "    (\n", "        (bucketX_availability_1.bucketX_availability / bucketX_availability_1.bucketX_total_item)\n", "        * 100\n", "    ),\n", "    1,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Category level availability calculation on GB"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# GB Availability\n", "IS_GB_TOTAL_1 = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        #         & (\n", "        #             (\n", "        #                 (availability_value.unreliable != \"1\")\n", "        #                 & (availability_value.unorderable != \"1\")\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #         )\n", "        #         & (\n", "        #             (availability_value.date_of_activation <= availability_value.order_date)\n", "        #             | (availability_value.inv_flag == 1)\n", "        #         )\n", "        & (availability_value.is_gb == \"TRUE\")\n", "    ]\n", "    .groupby([\"store_type\", \"l0\", \"l0_id\"])[[\"item_id\"]]\n", "    .count()\n", "    .reset_index()\n", ")\n", "IS_GB_TOTAL_1 = IS_GB_TOTAL_1.rename(columns={\"item_id\": \"total_is_gb\"})\n", "\n", "IS_GB_availability_1 = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        #         & (\n", "        #             (\n", "        #                 (availability_value.unreliable != \"1\")\n", "        #                 & (availability_value.unorderable != \"1\")\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unreliable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #             | (\n", "        #                 (availability_value.unorderable == \"1\")\n", "        #                 & (availability_value.inv_flag > 0)\n", "        #             )\n", "        #         )\n", "        #         & (\n", "        #             (availability_value.date_of_activation <= availability_value.order_date)\n", "        #             | (availability_value.inv_flag == 1)\n", "        #         )\n", "        & ((availability_value.app_live == 1))\n", "        & (availability_value.is_gb == \"TRUE\")\n", "    ]\n", "    .groupby([\"store_type\", \"l0_id\", \"l0\"])[[\"item_id\"]]\n", "    .count()\n", "    .reset_index()\n", ")\n", "\n", "IS_GB_availability_1 = IS_GB_availability_1.rename(columns={\"item_id\": \"availability_is_gb\"})\n", "\n", "is_gb_total_1 = bucketX_availability_1.merge(\n", "    IS_GB_TOTAL_1, on=[\"store_type\", \"l0_id\", \"l0\"], how=\"left\"\n", ")\n", "\n", "is_gb_avail_1 = is_gb_total_1.merge(\n", "    IS_GB_availability_1, on=[\"store_type\", \"l0_id\", \"l0\"], how=\"left\"\n", ")\n", "\n", "is_gb_avail_1[\"gb_availability\"] = round(\n", "    ((is_gb_avail_1.availability_is_gb / is_gb_avail_1.total_is_gb) * 100), 1\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Inactive live items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inactive_item_l0 = (\n", "    availability_value[\n", "        (\n", "            #             (\n", "            #                 (availability_value.unreliable == \"1\")\n", "            #                 & (availability_value.inv_flag <= 0)\n", "            #             )\n", "            #             | (\n", "            #                 (availability_value.unorderable == \"1\")\n", "            #                 & (availability_value.inv_flag <= 0)\n", "            #             )\n", "            (availability_value.new_substate != 1)\n", "        )\n", "        #         & (availability_value.date_of_activation <= availability_value.order_date)\n", "        & ((availability_value.app_live == 1))\n", "    ]\n", "    .groupby([\"store_type\", \"l0_id\", \"l0\"])[[\"item_id\"]]\n", "    .count()\n", "    .reset_index()\n", ")\n", "\n", "inactive_item_l0 = inactive_item_l0.rename(columns={\"item_id\": \"inactive_live_item\"})\n", "\n", "\n", "l0_final_data = is_gb_avail_1.merge(inactive_item_l0, on=[\"store_type\", \"l0\", \"l0_id\"], how=\"outer\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["l0_final_data = l0_final_data.merge(\n", "    l0_continous_availability, on=[\"store_type\", \"l0\", \"l0_id\"], how=\"left\"\n", ")\n", "\n", "l0_final_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["l0_final_data[pd.isnull(l0_final_data.total_continous_availabile_item) == True]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["l0_final_data.total_item = l0_final_data.total_item.fillna(0)\n", "\n", "l0_final_data.total_active_item = l0_final_data.total_active_item.fillna(0)\n", "l0_final_data.available_item = l0_final_data.available_item.fillna(0)\n", "l0_final_data.total_item = l0_final_data.total_item.astype(int)\n", "l0_final_data.total_active_item = l0_final_data.total_active_item.astype(int)\n", "l0_final_data.available_item = l0_final_data.available_item.astype(int)\n", "\n", "\n", "l0_final_data.Availability = l0_final_data.Availability.fillna(0)\n", "l0_final_data.BucketA_Availability = l0_final_data.BucketA_Availability.fillna(0)\n", "l0_final_data.BucketX_Availability = l0_final_data.BucketX_Availability.fillna(0)\n", "l0_final_data.gb_availability = l0_final_data.gb_availability.fillna(0)\n", "l0_final_data.inactive_live_item = l0_final_data.inactive_live_item.fillna(0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Category level Pan India availability calculation on GB"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india_1 = l0_final_data[\n", "    [\n", "        \"store_type\",\n", "        \"l0\",\n", "        \"total_item\",\n", "        \"total_active_item\",\n", "        \"available_item\",\n", "        \"Availability\",\n", "        \"bucketA_total_item\",\n", "        \"bucketA_availability\",\n", "        \"BucketA_Availability\",\n", "        \"bucketX_total_item\",\n", "        \"bucketX_availability\",\n", "        \"BucketX_Availability\",\n", "        \"total_is_gb\",\n", "        \"availability_is_gb\",\n", "        \"gb_availability\",\n", "        \"inactive_live_item\",\n", "        \"total_continous_availabile_item\",\n", "        \"total_continous_eligible_item\",\n", "    ]\n", "]\n", "\n", "pan_india_1[\"l0\"] = \"Pan India\"\n", "\n", "pan_india_rollup_1 = round(\n", "    pan_india_1.groupby([\"store_type\", \"l0\"]).agg(\n", "        {\n", "            \"total_item\": \"sum\",\n", "            \"total_active_item\": \"sum\",\n", "            \"available_item\": \"sum\",\n", "            \"bucketA_total_item\": \"sum\",\n", "            \"bucketA_availability\": \"sum\",\n", "            \"bucketX_total_item\": \"sum\",\n", "            \"bucketX_availability\": \"sum\",\n", "            \"total_is_gb\": \"sum\",\n", "            \"availability_is_gb\": \"sum\",\n", "            \"inactive_live_item\": \"sum\",\n", "            \"total_continous_availabile_item\": \"sum\",\n", "            \"total_continous_eligible_item\": \"sum\",\n", "        }\n", "    ),\n", "    1,\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["l0_final_data.available_item = np.where(\n", "    l0_final_data.available_item.isna(), 0, l0_final_data.available_item\n", ")\n", "l0_final_data.total_active_item = np.where(\n", "    l0_final_data.total_active_item.isna(), 0, l0_final_data.total_active_item\n", ")\n", "pan_india_rollup_1[\"Availability\"] = round(\n", "    (pan_india_rollup_1.available_item * 100 / pan_india_rollup_1.total_active_item),\n", "    1,\n", ")\n", "\n", "\n", "l0_final_data.bucketA_availability = np.where(\n", "    l0_final_data.bucketA_availability.isna(), 0, l0_final_data.bucketA_availability\n", ")\n", "\n", "l0_final_data.bucketA_total_item = np.where(\n", "    l0_final_data.bucketA_total_item.isna(), 0, l0_final_data.bucketA_total_item\n", ")\n", "\n", "pan_india_rollup_1[\"BucketA_Availability\"] = round(\n", "    ((pan_india_rollup_1.bucketA_availability * 100 / pan_india_rollup_1.bucketA_total_item)),\n", "    1,\n", ")\n", "\n", "\n", "l0_final_data.bucketX_availability = np.where(\n", "    l0_final_data.bucketX_availability.isna(), 0, l0_final_data.bucketX_availability\n", ")\n", "\n", "l0_final_data.bucketX_total_item = np.where(\n", "    l0_final_data.bucketX_total_item.isna(), 0, l0_final_data.bucketX_total_item\n", ")\n", "pan_india_rollup_1[\"BucketX_Availability\"] = round(\n", "    ((pan_india_rollup_1.bucketX_availability * 100 / pan_india_rollup_1.bucketX_total_item)),\n", "    1,\n", ")\n", "\n", "\n", "l0_final_data.total_is_gb = np.where(l0_final_data.total_is_gb.isna(), 0, l0_final_data.total_is_gb)\n", "l0_final_data.availability_is_gb = np.where(\n", "    l0_final_data.availability_is_gb.isna(), 0, l0_final_data.availability_is_gb\n", ")\n", "\n", "pan_india_rollup_1[\"gb_availability\"] = round(\n", "    (pan_india_rollup_1.availability_is_gb * 100 / pan_india_rollup_1.total_is_gb), 1\n", ")\n", "\n", "l0_final_data[\"total_continous_availabile_item\"] = round(\n", "    l0_final_data.total_continous_availabile_item\n", "    * 100\n", "    / l0_final_data.total_continous_eligible_item,\n", "    1,\n", ")\n", "pan_india_rollup_1[\"total_continous_availabile_item\"] = round(\n", "    pan_india_rollup_1.total_continous_availabile_item\n", "    * 100\n", "    / pan_india_rollup_1.total_continous_eligible_item,\n", "    1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["is_gb_avail_1.drop(\n", "    columns=[\n", "        \"bucketA_total_item\",\n", "        \"bucketA_availability\",\n", "        \"bucketX_availability\",\n", "        \"total_is_gb\",\n", "        \"availability_is_gb\",\n", "        \"l0_id\",\n", "    ],\n", "    inplace=True,\n", ")\n", "\n", "pan_india_1.drop(\n", "    columns=[\n", "        \"bucketA_total_item\",\n", "        \"bucketA_availability\",\n", "        \"bucketX_availability\",\n", "        \"total_is_gb\",\n", "        \"availability_is_gb\",\n", "    ],\n", "    inplace=True,\n", ")\n", "\n", "final_l0 = pan_india_rollup_1.append(l0_final_data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Formatting"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_facility.total_item = final_facility.total_item.astype(int)\n", "final_facility.total_active_item = final_facility.total_active_item.astype(int)\n", "final_facility.available_item = final_facility.available_item.astype(int)\n", "\n", "final_facility.bucketX_total_item = final_facility.bucketX_total_item.fillna(0).astype(int)\n", "final_facility.inactive_live_items = final_facility.inactive_live_items.fillna(0).astype(int)\n", "\n", "final_facility.Availability = final_facility.Availability.fillna(0).astype(int)\n", "final_facility.BucketA_Availability = final_facility.BucketA_Availability.fillna(0).astype(int)\n", "final_facility.BucketX_Availability = final_facility.BucketX_Availability.fillna(0).astype(int)\n", "final_facility.gb_availability = final_facility.gb_availability.fillna(0).astype(int)\n", "\n", "final_facility.total_continous_availabile_item = (\n", "    final_facility.total_continous_availabile_item.fillna(0).astype(int)\n", ")\n", "\n", "final_facility.GB_staple_availability = final_facility.GB_staple_availability.fillna(0).astype(int)\n", "final_facility.GB_fmcg_availability = final_facility.GB_fmcg_availability.fillna(0).astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_facility = final_facility[final_facility.Availability > 10]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_facility.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_facility[\"total_item\"] = final_facility[\"total_item\"].apply(lambda x: format(x, \",d\"))\n", "\n", "\n", "final_facility[\"total_active_item\"] = final_facility[\"total_active_item\"].apply(\n", "    lambda x: format(x, \",d\")\n", ")\n", "\n", "\n", "final_facility[\"available_item\"] = final_facility[\"available_item\"].apply(lambda x: format(x, \",d\"))\n", "\n", "\n", "final_facility[\"bucketX_total_item\"] = final_facility[\"bucketX_total_item\"].apply(\n", "    lambda x: format(x, \",d\")\n", ")\n", "\n", "\n", "final_facility[\"inactive_live_items\"] = final_facility[\"inactive_live_items\"].apply(\n", "    lambda x: format(x, \",d\")\n", ")\n", "\n", "final_facility[\"unorderable_items\"] = final_facility[\"unorderable_items\"].apply(\n", "    lambda x: format(x, \",d\")\n", ")\n", "\n", "final_facility[\"unreliable_items\"] = final_facility[\"unreliable_items\"].apply(\n", "    lambda x: format(x, \",d\")\n", ")\n", "\n", "final_facility[\"unorderable_unreliable_items\"] = final_facility[\n", "    \"unorderable_unreliable_items\"\n", "].apply(lambda x: format(x, \",d\"))\n", "\n", "# final_facility[\"\"] = final_facility[\"\"].apply(\n", "#     lambda x: format(x, \",d\")\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_l0.total_continous_availabile_item = (\n", "    final_l0.total_continous_availabile_item.fillna(0).astype(int).astype(str) + \"%\"\n", ")\n", "final_l0.Availability = final_l0.Availability.fillna(0).astype(int).astype(str) + \"%\"\n", "final_l0.BucketA_Availability = (\n", "    final_l0.BucketA_Availability.fillna(0).astype(int).astype(str) + \"%\"\n", ")\n", "final_l0.BucketX_Availability = (\n", "    final_l0.BucketX_Availability.fillna(0).astype(int).astype(str) + \"%\"\n", ")\n", "final_l0.gb_availability = final_l0.gb_availability.fillna(0).astype(int).astype(str) + \"%\"\n", "\n", "final_facility.Facility = np.where(\n", "    final_facility.Facility.isna(),\n", "    final_facility.facility_name,\n", "    final_facility.Facility,\n", ")\n", "\n", "final_facility.bucketX_total_item = np.where(\n", "    final_facility.bucketX_total_item == \"0\", \"-\", final_facility.bucketX_total_item\n", ")\n", "\n", "final_facility.GB_staple_availability = final_facility.GB_staple_availability.astype(str) + \"%\"\n", "final_facility.GB_fmcg_availability = final_facility.GB_fmcg_availability.astype(str) + \"%\"\n", "\n", "final_facility.total_continous_availabile_item = (\n", "    final_facility.total_continous_availabile_item.astype(str) + \"%\"\n", ")\n", "final_facility.Availability = final_facility.Availability.astype(str) + \"%\"\n", "final_facility.BucketA_Availability = final_facility.BucketA_Availability.astype(str) + \"%\"\n", "final_facility.BucketX_Availability = final_facility.BucketX_Availability.astype(str) + \"%\"\n", "final_facility.gb_availability = final_facility.gb_availability.astype(str) + \"%\"\n", "\n", "final_l0.Availability = np.where(final_l0.Availability == \"0%\", \"- \", final_l0.Availability)\n", "final_l0.BucketA_Availability = np.where(\n", "    final_l0.BucketA_Availability == \"0%\", \"-\", final_l0.BucketA_Availability\n", ")\n", "final_l0.BucketX_Availability = np.where(\n", "    final_l0.BucketX_Availability == \"0%\", \"-\", final_l0.BucketX_Availability\n", ")\n", "final_l0.gb_availability = np.where(final_l0.gb_availability == \"0%\", \"-\", final_l0.gb_availability)\n", "final_l0.available_item = np.where(final_l0.available_item == \"0\", \"-\", final_l0.available_item)\n", "\n", "final_l0.total_active_item = np.where(\n", "    final_l0.total_active_item == \"0\", \"-\", final_l0.total_active_item\n", ")\n", "final_l0.total_item = np.where(final_l0.total_item == \"0\", \"-\", final_l0.total_item)\n", "final_l0.bucketX_total_item = final_l0.bucketX_total_item.astype(int)\n", "final_l0.bucketX_total_item = np.where(\n", "    final_l0.bucketX_total_item == \"0\", \"-\", final_l0.bucketX_total_item\n", ")\n", "\n", "final_facility.Availability = np.where(\n", "    final_facility.Availability == \"0%\", \"- \", final_facility.Availability\n", ")\n", "final_facility.BucketA_Availability = np.where(\n", "    final_facility.BucketA_Availability == \"0%\",\n", "    \"-\",\n", "    final_facility.BucketA_Availability,\n", ")\n", "final_facility.BucketX_Availability = np.where(\n", "    final_facility.BucketX_Availability == \"0%\",\n", "    \"-\",\n", "    final_facility.BucketX_Availability,\n", ")\n", "final_facility.gb_availability = np.where(\n", "    final_facility.gb_availability == \"0%\", \"-\", final_facility.gb_availability\n", ")\n", "final_facility.available_item = np.where(\n", "    final_facility.available_item == \"0\", \"-\", final_facility.available_item\n", ")\n", "\n", "final_facility.GB_staple_availability = np.where(\n", "    final_facility.GB_staple_availability == \"0%\",\n", "    \"-\",\n", "    final_facility.GB_staple_availability,\n", ")\n", "final_facility.GB_fmcg_availability = np.where(\n", "    final_facility.GB_fmcg_availability == \"0\", \"-\", final_facility.GB_fmcg_availability\n", ")\n", "\n", "final_facility[\"facility_check\"] = np.where(\n", "    final_facility.final_facility.isna(),\n", "    final_facility.facility_name,\n", "    final_facility.final_facility,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final_facility"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# len(final_facility_dark.facility_check.unique())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Separate views"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_facility_ndd = final_facility[final_facility.store_type == \"NDD\"]\n", "final_l0_ndd = final_l0[final_l0.store_type == \"NDD\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_facility_dark = final_facility[final_facility.store_type == \"Dark Store\"]\n", "final_l0_dark = final_l0[final_l0.store_type == \"Dark Store\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final_facility_gm = final_facility[final_facility.store_type == \"Franchise Store\"]\n", "# final_l0_gm = final_l0[final_l0.store_type == \"Franchise Store\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### Summary View"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary_ndd = final_facility_ndd[\n", "    (final_facility_ndd.facility_name == \"Pan India\")\n", "    | (final_facility_ndd.facility_name == \"North\")\n", "    | (final_facility_ndd.facility_name == \"South\")\n", "    | (final_facility_ndd.facility_name == \"East\")\n", "    | (final_facility_ndd.facility_name == \"West\")\n", "]\n", "\n", "summary_dark = final_facility_dark[(final_facility_dark.facility_name == \"Pan India\")]\n", "\n", "# summary_gm = final_facility_gm[(final_facility_gm.facility_name == \"Pan India\")]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df_out = summary_ndd.append([summary_dark, summary_gm])\n", "df_out = summary_ndd.append([summary_dark])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_out.facility_name = df_out.facility_name + \" - \" + df_out.store_type"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary = df_out.to_dict(orient=\"rows\")\n", "\n", "items = final_facility_ndd.to_dict(orient=\"rows\")\n", "items1 = final_l0_ndd.to_dict(orient=\"rows\")\n", "\n", "items2 = final_facility_dark.to_dict(orient=\"rows\")\n", "items3 = final_l0_dark.to_dict(orient=\"rows\")\n", "\n", "# items4 = final_facility_gm.to_dict(orient=\"rows\")\n", "# items5 = final_l0_gm.to_dict(orient=\"rows\")\n", "\n", "\n", "# time_period_considered = (datetime.now()).strftime(\"%Y-%m-%d\")\n", "\n", "# secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "\n", "\n", "!pwd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["unorderable_df_ndd = DDFInal_ndd.to_dict(orient=\"rows\")\n", "unorderable_df_dark = DDFInal_dark.to_dict(orient=\"rows\")\n", "unorderable_df_gm = DDFInal_gm.to_dict(orient=\"rows\")\n", "\n", "unreliable_df_ndd = DFInal_ndd.to_dict(orient=\"rows\")\n", "unreliable_df_dark = DFInal_dark.to_dict(orient=\"rows\")\n", "unreliable_df_gm = DFInal_gm.to_dict(orient=\"rows\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "\n", "\n", "import os\n", "from jinja2 import Template\n", "\n", "\n", "# to_email= email[\"email\"].to_list()     ### using wiw external sheet for email id\n", "# to_email = json.loads(secrets.get(\"availability_report_emails\"))\n", "import datetime as dt\n", "\n", "ld = dt.datetime.now().strftime(\"%Y-%m-%d\")\n", "\n", "sheet_id = \"1VBcmA-8XkeYpVNn6BlOttzWLklAJYuBE8tDzDP7sE8I\"\n", "email_id = pb.from_sheets(sheet_id, \"email_av\")\n", "\n", "from_email = \"<EMAIL>\"\n", "to_email = list(email_id[\"email\"])\n", "# from_email = \"<EMAIL>\"\n", "\n", "subject = \"Availability Report\" + ld\n", "\n", "cwd = os.getcwd()\n", "with open(\n", "    os.path.join(\n", "        cwd,\n", "        \"availability_realtime_email_template.html\",\n", "    ),\n", "    \"r\",\n", ") as f:\n", "    t = Template(f.read())\n", "\n", "\n", "rendered = t.render(\n", "    summary=summary,\n", "    products=items,\n", "    products1=items1,\n", "    products2=items2,\n", "    products3=items3,\n", "    # products4=items4,\n", "    # products5=items5,\n", "    # time_period_considered=time_period_considered,\n", ")\n", "\n", "\n", "pb.send_email(from_email, to_email, subject, html_content=rendered)\n", "\n", "print(\"mail sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from_email1 = \"<EMAIL>\"\n", "from_email = \"<EMAIL>\"\n", "# to_email = json.loads(secrets.get(\"availability_report_emails\"))\n", "to_email1 = [\"<EMAIL>\"]\n", "\n", "subject1 = \"Unorderable and Unreliable Items Availability Report for \"\n", "with open(\n", "    os.path.join(\n", "        cwd,\n", "        \"specific_availability_realtime_email_template.html\",\n", "    ),\n", "    \"r\",\n", ") as f1:\n", "    t1 = Template(f1.read())\n", "\n", "\n", "rendered1 = t1.render(\n", "    products=unreliable_df_ndd,\n", "    productsr2=unreliable_df_dark,\n", "    productsr3=unreliable_df_gm,\n", "    products1=unorderable_df_ndd,\n", "    productso2=unorderable_df_dark,\n", "    productso3=unorderable_df_gm,\n", "    time_period_considered=time_period_considered,\n", ")\n", "\n", "\n", "pb.send_email(from_email1, to_email1, subject1, html_content=rendered1)\n", "\n", "print(\"specific mail sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}