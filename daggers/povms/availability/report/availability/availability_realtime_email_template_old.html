<head>
<style type="text/css">
.tg {
    border-collapse: collapse;
    border-spacing: 0;
    border-color: #aaa;
    width: 100%;
    max-width: 690px;
}

.tg td {
    font-family: Roboto, sans-serif;
    font-size: 12px;
    padding: 5px 5px;
    border-style: solid;
    border-width: 1px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    color: #333;
    border-left-width:1px;
    border-right-width:1px;
    background-color: #fff;
}

.tg tr:last-child td{
    background-color: #aaa;
}
.tg th {
    font-family: Arial, sans-serif;
    font-size: 12px;
    font-weight: normal;
    padding: 5px 5px;
    border-style: solid;
    border-width: 6px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    color: #fff;
    border-left-width:1px;
    border-right-width:1px;
    background-color: #f38630;
}

.tg .tg-0lax {
    text-align: center;
    vertical-align: center
}

.tg .tg-dg7a {
    background-color: #F0F0F0;
    text-align: center;
    vertical-align: center
}

</style>
</head>
<p>Hi All,<br/>
This mail contains the availability numbers across different facilities and L0s for {{time_period_considered}}. <br/><br/>
<p>  
Additionally, for live availability numbers across Facility and Ptype level, please refer to the tracker :<br/>
Link:
<a href="https://docs.google.com/spreadsheets/d/11CqeLp3X4xixWHjJjIfFP6_74dNM-7e0pt8Yh_NBO5w/edit?ts=5f8f97b2#gid=809408328">Inventory - Daily Availability Tracker - NDD</a><br/>
<p>   
Availability Numbers for previous days please refer below Tableau Link: 
<a href="https://tableau.grofer.io/#/views/InventoryDashboardNew/FacilitywiseAvailability">Inventory Dashboard</a><br/>  
</p>
</br>

<p><b> Summary </b></p>
<table class="tg" border="1">
    
    <tbody>
        <tr>
            <th colspan="3"> </th>
            <th colspan="6">Availability</th>
        </tr>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">Facility Name</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Total Item</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Live Item</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Overall</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Bucket X</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Bucket A</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">GB</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">GB Grocery & Staples </span></th>
            <th class="tg-0lax"><span style="font-weight:bold">GB FMCG </span></th>
        </tr>
        {% for product in summary %}
        <tr>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.facility_name}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_active_item}}</b>
            </td>
           <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.available_item}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Availability}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.BucketX_Availability}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.BucketA_Availability}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.gb_availability}}</b>
            </td>
           <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
             <b>{{ product.GB_staple_availability}}</b>
           </td>
           <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
             <b>{{ product.GB_fmcg_availability}}</b>
           </td>
           
        </tr>
        {% endfor %}

    </tbody>
</table>

    <p></p>
    <p></p>
    <p></p>
    <p></p>

<br/>
<p><b> NDD Facility level </b></p>
<table class="tg" border="1">
    
    <tbody>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">Facility Name</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Total Active item</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Total Active Eligible Item</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Total Eligible Live Item</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Availability</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">BucketA Availability</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Bucket X Total Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">BucketX Availability</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">GB Availability</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">GB Availability for Grocery & Staples </span></th>
            <th class="tg-0lax"><span style="font-weight:bold">GB Availability for FMCG </span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Non-Active Live Items </span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Continuous Live Items (30 days)</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unorderable & Unreliable Items </span></th>
        </tr>
        {% for product in products %}
        <tr>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.facility_check}}</b>
            </td>
              <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_item}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_active_item}}</b>
            </td>
           <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.available_item}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Availability}}</b>
            </td>
             <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.BucketA_Availability}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.bucketX_total_item}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.BucketX_Availability}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.gb_availability}}</b>
            </td>
           <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
             <b>{{ product.GB_staple_availability}}</b>
           </td>
           <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
             <b>{{ product.GB_fmcg_availability}}</b>
           </td>
          <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
             <b>{{ product.inactive_live_items}}</b>
           </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
             <b>{{ product.total_continous_availabile_item}}</b>
           </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
             <b>{{ product.unorderable_unreliable_items}}</b>
           </td>
             
           
        </tr>
        {% endfor %}

    </tbody>
</table>

    <p></p>
    <p></p>
    <p></p>
    <p></p>
<p><b> NDD L0 level </b></p>
<table class="tg" border="1">
    
    <tbody>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">Category</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Total Active Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Total Eligible Item</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Total Eligible Live Item</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Availability</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">BucketA Availability</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Bucket X Total Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">BucketX Availability</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">GB Availability</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Non-Active Live Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Continuous Live Items (30 days)</span></th>
        </tr>
        {% for product in products1 %}
        <tr>
            
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.l0}}</b>
            </td>
              <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_item}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_active_item}}</b>
            </td>
           <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.available_item}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Availability}}</b>
            </td>
             <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.BucketA_Availability}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.bucketX_total_item}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.BucketX_Availability}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.gb_availability}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.inactive_live_item}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_continous_availabile_item}}</b>
            </td>
        </tr>
        {% endfor %}

    </tbody>
</table>

    <p></p>
    <p></p>
    <p></p>
    <p></p>

<br/>
<p><b> E3 Facility level </b></p>
<table class="tg" border="1">
    
    <tbody>
         <tr>
            <th colspan="5"> </th>
            <th colspan="6">Availability</th>
        </tr>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">Store Type</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Facility Name</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Active Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Active Eligible Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Eligible Live Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Overall</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Bucket X</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Bucket A</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">GB</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">GB Grocery & Staples</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">GB FMCG</span></th>
        </tr>
        {% for product in products2 %}
        <tr>
            
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.store_type}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Facility}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_item}}</b>
            </td>
              <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_active_item}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.available_item}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Availability}}</b>
            </td>
             <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.BucketX_Availability}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.BucketA_Availability}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.gb_availability}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.GB_staple_availability}}</b>
            </td>
             <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.GB_fmcg_availability}}</b>
            </td>
        </tr>
        {% endfor %}

    </tbody>
</table>

    <p></p>
    <p></p>
    <p></p>
    <p></p>

<p><b> E3 L0 level </b></p>
<table class="tg" border="1">
    
    <tbody>
         <tr>
            <th colspan="5"> </th>
            <th colspan="4">Availability</th>
        </tr>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">Store Type</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">L0</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Active Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Active Eligible Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Eligible Live Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Overall</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Bucket X</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Bucket A</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">GB</span></th>
        </tr>
        {% for product in products3 %}
        <tr>
            
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.store_type}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.l0}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_item}}</b>
            </td>
              <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_active_item}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.available_item}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Availability}}</b>
            </td>
             <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.BucketX_Availability}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.BucketA_Availability}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.gb_availability}}</b>
            </td>
        </tr>
        {% endfor %}

    </tbody>
</table>


<p></p>
    <p></p>
    <p></p>
    <p></p>

<br/>
<p><b> GM Facility level </b></p>
<table class="tg" border="1">
    
    <tbody>
         <tr>
            <th colspan="5"> </th>
            <th colspan="6">Availability</th>
        </tr>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">Store Type</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Facility Name</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Active Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Active Eligible Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Eligible Live Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Overall</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Bucket X</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Bucket A</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">GB</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">GB Grocery & Staples</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">GB FMCG</span></th>
        </tr>
        {% for product in products4 %}
        <tr>
            
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.store_type}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Facility}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_item}}</b>
            </td>
              <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_active_item}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.available_item}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Availability}}</b>
            </td>
             <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.BucketX_Availability}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.BucketA_Availability}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.gb_availability}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.GB_staple_availability}}</b>
            </td>
             <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.GB_fmcg_availability}}</b>
            </td>
        </tr>
        {% endfor %}

    </tbody>
</table>

    <p></p>
    <p></p>
    <p></p>
    <p></p>

<p><b> GM L0 level </b></p>
<table class="tg" border="1">
    
    <tbody>
         <tr>
            <th colspan="5"> </th>
            <th colspan="4">Availability</th>
        </tr>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">Store Type</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">L0</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Active Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Active Eligible Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Eligible Live Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Overall</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Bucket X</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Bucket A</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">GB</span></th>
        </tr>
        {% for product in products5 %}
        <tr>
            
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.store_type}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.l0}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_item}}</b>
            </td>
              <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_active_item}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.available_item}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Availability}}</b>
            </td>
             <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.BucketX_Availability}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.BucketA_Availability}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.gb_availability}}</b>
            </td>
        </tr>
        {% endfor %}

    </tbody>
</table>




<p>Best Regards,<br />
Data Bangalore
</p>
<p></p>