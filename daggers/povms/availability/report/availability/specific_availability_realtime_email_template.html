<head>
<style type="text/css">
.tg {
    border-collapse: collapse;
    border-spacing: 0;
    border-color: #aaa;
    width: 100%;
    max-width: 690px;
}

.tg td {
    font-family: Roboto, sans-serif;
    font-size: 12px;
    padding: 5px 5px;
    border-style: solid;
    border-width: 1px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    color: #333;
    border-left-width:1px;
    border-right-width:1px;
    background-color: #fff;
}

.tg tr:last-child td{
    background-color: #aaa;
}
.tg th {
    font-family: Arial, sans-serif;
    font-size: 12px;
    font-weight: normal;
    padding: 5px 5px;
    border-style: solid;
    border-width: 6px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    color: #fff;
    border-left-width:1px;
    border-right-width:1px;
    background-color: #f38630;
}

.tg .tg-0lax {
    text-align: center;
    vertical-align: center
}

.tg .tg-dg7a {
    background-color: #F0F0F0;
    text-align: center;
    vertical-align: center
}
    
</style>

</head>

<p>Hi All,<br/>
This mail contains the availability numbers across different facilities for unorderable and unreliable items for {{time_period_considered}}.<br/>
</p>

<p>  
Additionally, for unreliable and unorderable items raw data, please refer to the tracker :<br/>
Link:
<a href="https://docs.google.com/spreadsheets/d/1AElu_-VzW5Zfk6-EjSc12VEWk9u-t24r2vJDcEfMSXk/edit#gid=0">Unreliable and Unorderable Items</a><br/>
<p>   




<p><b> Unreliable Items in NDD Facilities </b></p>
<table class="tg" border="1">
    
    <tbody>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">Store Type</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Facility Name</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Total Active item</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Total Active Eligible Item</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unreliable Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unreliable Bucket A Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unreliable Bucket X Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unreliable Available Items </span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unreliable Available Bucket A Items </span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unreliable Available BucketX Items </span></th>
        </tr>
        {% for product in products %}
        <tr>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.store_type}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Facility}}</b>
            </td>
              <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_item}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_active_item}}</b>
            </td>
           <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.unreliable_items}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.unreliable_BucketA_items}}</b>
            </td>
             <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.unreliable_BucketX_items}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Unreliable_Available_Items}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Unreliable_Available_BucketA_Items}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Unreliable_Available_BucketX_Items}}</b>
            </td>
           
        </tr>
        {% endfor %}

    </tbody>
</table>

    <p></p>
    <p></p>
    <p></p>
    <p></p>

<br/>
<p><b> Unreliable Items in E3 Facilities</b></p>
<table class="tg" border="1">
    
    <tbody>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">Store Type</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Facility Name</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Total Active item</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Total Active Eligible Item</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unreliable Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unreliable Bucket A Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unreliable Bucket X Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unreliable Available Items </span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unreliable Available Bucket A Items </span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unreliable Available BucketX Items </span></th>
        </tr>
        {% for product in productsr2 %}
        <tr>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.store_type}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Facility}}</b>
            </td>
              <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_item}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_active_item}}</b>
            </td>
           <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.unreliable_items}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.unreliable_BucketA_items}}</b>
            </td>
             <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.unreliable_BucketX_items}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Unreliable_Available_Items}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Unreliable_Available_BucketA_Items}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Unreliable_Available_BucketX_Items}}</b>
            </td>
           
        </tr>
        {% endfor %}

    </tbody>
</table>

    <p></p>
    <p></p>
    <p></p>
    <p></p>
<br/>
<p><b> Unreliable Items in GM Facilities</b></p>
<table class="tg" border="1">
    
    <tbody>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">Store Type</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Facility Name</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Total Active item</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Total Active Eligible Item</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unreliable Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unreliable Bucket A Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unreliable Bucket X Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unreliable Available Items </span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unreliable Available Bucket A Items </span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unreliable Available BucketX Items </span></th>
        </tr>
        {% for product in productsr3 %}
        <tr>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.store_type}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Facility}}</b>
            </td>
              <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_item}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_active_item}}</b>
            </td>
           <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.unreliable_items}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.unreliable_BucketA_items}}</b>
            </td>
             <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.unreliable_BucketX_items}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Unreliable_Available_Items}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Unreliable_Available_BucketA_Items}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Unreliable_Available_BucketX_Items}}</b>
            </td>
           
        </tr>
        {% endfor %}

    </tbody>
</table>

    <p></p>
    <p></p>
    <p></p>
    <p></p>

<br/>
<p><b> Unorderable items in NDD Facilities </b></p>
<table class="tg" border="1">
    
    <tbody>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">Store Type</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Facility</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Total Active Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Total Active Eligible Item</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unorderable Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unorderable Bucket A Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unorderable Bucket X Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unorderable Available Items </span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unorderable Available Bucket A Items </span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unorderable Available Bucket X Items </span></th>
        </tr>
        {% for product in products1 %}
        <tr>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.store_type}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Facility}}</b>
            </td>
              <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_item}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_active_item}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.unorderable_items}}</b>
            </td>
           <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.unorderable_BucketA_items}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.unorderable_BucketX_items}}</b>
            </td>
             <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Unorderable_Available_Items}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Unorderable_Available_BucketA_Items}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Unorderable_Available_BucketX_Items}}</b>
            </td>
        </tr>
        {% endfor %}

    </tbody>
</table>

    <p></p>
    <p></p>
    <p></p>
    <p></p>
<br/>
<p><b> Unorderable items in E3 Facilities </b></p>
<table class="tg" border="1">
    
    <tbody>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">Store Type</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Facility</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Total Active Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Total Active Eligible Item</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unorderable Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unorderable Bucket A Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unorderable Bucket X Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unorderable Available Items </span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unorderable Available Bucket A Items </span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unorderable Available Bucket X Items </span></th>
        </tr>
        {% for product in productso2 %}
        <tr>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.store_type}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Facility}}</b>
            </td>
              <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_item}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_active_item}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.unorderable_items}}</b>
            </td>
           <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.unorderable_BucketA_items}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.unorderable_BucketX_items}}</b>
            </td>
             <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Unorderable_Available_Items}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Unorderable_Available_BucketA_Items}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Unorderable_Available_BucketX_Items}}</b>
            </td>
        </tr>
        {% endfor %}

    </tbody>
</table>

    <p></p>
    <p></p>
    <p></p>
    <p></p>

<br/>
<p><b> Unorderable items in GM Facilities </b></p>
<table class="tg" border="1">
    
    <tbody>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">Store Type</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Facility</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Total Active Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Total Active Eligible Item</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unorderable Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unorderable Bucket A Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unorderable Bucket X Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unorderable Available Items </span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unorderable Available Bucket A Items </span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Unorderable Available Bucket X Items </span></th>
        </tr>
        {% for product in productso3 %}
        <tr>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.store_type}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Facility}}</b>
            </td>
              <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_item}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_active_item}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.unorderable_items}}</b>
            </td>
           <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.unorderable_BucketA_items}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.unorderable_BucketX_items}}</b>
            </td>
             <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Unorderable_Available_Items}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Unorderable_Available_BucketA_Items}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Unorderable_Available_BucketX_Items}}</b>
            </td>
        </tr>
        {% endfor %}

    </tbody>
</table>
<br/>

<p>Best Regards,<br />
Data Bangalore
</p>
<p></p>