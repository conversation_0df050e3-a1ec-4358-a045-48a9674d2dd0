{"cells": [{"cell_type": "code", "execution_count": null, "id": "e2835b40-9703-4b62-acdd-c490ba0adf8a", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "e4cc404b-7f27-425b-8079-42d77299d813", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "markdown", "id": "e98ac925-533d-453f-8782-022eedb791d7", "metadata": {}, "source": ["# Active ARS Details"]}, {"cell_type": "code", "execution_count": null, "id": "92659801-4d9d-413a-8b3b-7fd21eda5774", "metadata": {}, "outputs": [], "source": ["def outlets():\n", "    outlets = \"\"\"\n", "    \n", "    select rcl.name as city_name,\n", "        om.outlet_id as hot_outlet_id, \n", "        om.facility_id, cf.facility_name,\n", "        case when wom.cloud_store_id is null then om.outlet_id else wom.cloud_store_id end as inv_outlet_id,\n", "        rco.business_type_id,\n", "        case when rco.business_type_id !=7 then 'be' else 'fe' end as taggings\n", "        \n", "            from lake_po.physical_facility_outlet_mapping om\n", "            \n", "            left join (select id, tax_location_id,\n", "                case when id = 581 then 12 else business_type_id end as business_type_id\n", "                    from lake_retail.console_outlet\n", "                        ) rco on rco.id = om.outlet_id\n", "            \n", "            left join (select id, name as facility_name \n", "                from lake_crates.facility\n", "                        ) cf on cf.id = om.facility_id\n", "            \n", "            left join (select distinct warehouse_id, cloud_store_id from lake_retail.warehouse_outlet_mapping \n", "                where active = 1\n", "                        ) wom on wom.warehouse_id = om.outlet_id\n", "            \n", "            left join lake_retail.console_location rcl on rcl.id = rco.tax_location_id\n", "            \n", "                where rco.business_type_id in (1,12,7,19,20,21)\n", "                and om.active = 1 and ars_active = 1 and is_primary = 1\n", "                and om.outlet_name not like '%%SSC%%'\n", "                and om.outlet_name not like '%%MODI%%'\n", "                and om.outlet_name not like '%%hot ff%%'\n", "    \n", "    \"\"\"\n", "    return read_sql_query(outlets, redshift)\n", "\n", "\n", "outlets = outlets()"]}, {"cell_type": "code", "execution_count": null, "id": "47d5d621-8b85-411c-9b40-99842b1c8580", "metadata": {}, "outputs": [], "source": ["outlets.head()"]}, {"cell_type": "code", "execution_count": null, "id": "921b03ec-b992-43a2-b730-c9220d358637", "metadata": {}, "outputs": [], "source": ["# all inv outlets\n", "all_inv_outlet_id_list = tuple(list(outlets[\"inv_outlet_id\"].unique()))\n", "\n", "# all hot outlets\n", "all_hot_outlet_id_list = tuple(list(outlets[\"hot_outlet_id\"].unique()))\n", "\n", "# be hot outlets\n", "all_hot_name = outlets[\n", "    [\"hot_outlet_id\", \"inv_outlet_id\", \"facility_id\", \"facility_name\"]\n", "].rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_hot_outlet_id\",\n", "        \"inv_outlet_id\": \"be_inv_outlet_id\",\n", "        \"facility_id\": \"be_facility_id\",\n", "        \"facility_name\": \"be_facility_name\",\n", "    }\n", ")\n", "\n", "# all facility id\n", "all_facility_id_list = tuple(list(outlets[\"facility_id\"].unique()))\n", "\n", "# frontend outlets\n", "frontend_outlet_details = (\n", "    outlets[outlets[\"taggings\"] == \"fe\"].reset_index().drop(columns={\"index\"})\n", ")\n", "\n", "fe_facility_id_list = tuple(list(frontend_outlet_details[\"facility_id\"].unique()))"]}, {"cell_type": "markdown", "id": "044eccc6-fb19-4bad-b0a6-72b605475c15", "metadata": {}, "source": ["# item Details"]}, {"cell_type": "code", "execution_count": null, "id": "90a2c77c-018d-4dde-bf78-31543afbb0f6", "metadata": {}, "outputs": [], "source": ["def item_details():\n", "    item_details = \"\"\"\n", "    \n", "    with\n", "    item_details as\n", "        (select rpp.item_id,\n", "            l0, l1, l2, pb.manufacturer_id, pm.name as manufacturer_name\n", "\n", "                    from rpc.product_product rpp\n", "\n", "                        join rpc.item_category_details cd on cd.item_id = rpp.item_id\n", "\n", "                        left join rpc.product_brand pb on pb.id = rpp.brand_id\n", "                        left join rpc.product_manufacturer pm on pm.id = pb.manufacturer_id\n", "                        \n", "                        left join \n", "                            (select distinct item_id from rpc.item_tag_mapping where active = true\n", "                                and cast(tag_value as int) = 3 and tag_type_id = 3\n", "                            ) i on i.item_id = rpp.item_id\n", "\n", "                            where rpp.id in (select max(id) as id from rpc.product_product where active = 1 and approved = 1 group by item_id)\n", "                                 and i.item_id is null and rpp.handling_type <> '8'\n", "        )\n", "        \n", "            select * from item_details\n", "    \n", "    \"\"\"\n", "    return read_sql_query(item_details, trino)\n", "\n", "\n", "item_details = item_details()"]}, {"cell_type": "code", "execution_count": null, "id": "989d9cc6-2fb1-4897-8209-102fb78c8f74", "metadata": {}, "outputs": [], "source": ["item_details.head()"]}, {"cell_type": "markdown", "id": "d994f9c0-c3ec-4747-a986-812bfed8b067", "metadata": {}, "source": ["# Active Assortment Details"]}, {"cell_type": "code", "execution_count": null, "id": "0a44c879-ebe8-47ec-9f4e-6b34ebc2960f", "metadata": {}, "outputs": [], "source": ["def active_assortment():\n", "    active_assortment = f\"\"\"\n", "    \n", "    with\n", "    active_assortment as\n", "        (select item_id, facility_id\n", "            from rpc.product_facility_master_assortment\n", "                where active = 1 and master_assortment_substate_id = 1 and substate_reason_id = 7\n", "        )\n", "\n", "            select cast(current_timestamp as timestamp) as updated_at_ist, * from active_assortment\n", "    \"\"\"\n", "    return read_sql_query(active_assortment, trino)\n", "\n", "\n", "active_assortment = active_assortment()"]}, {"cell_type": "code", "execution_count": null, "id": "54f94ed2-4772-462d-b68f-2bd13c88f25f", "metadata": {}, "outputs": [], "source": ["active_assortment.head()"]}, {"cell_type": "code", "execution_count": null, "id": "239459dd-8707-417c-861d-cd405fa51749", "metadata": {}, "outputs": [], "source": ["adding_item_details = pd.merge(\n", "    active_assortment, item_details, on=[\"item_id\"], how=\"inner\"\n", ")\n", "\n", "adding_outlet_details = pd.merge(\n", "    adding_item_details, frontend_outlet_details, on=[\"facility_id\"], how=\"inner\"\n", ")\n", "\n", "adding_outlet_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "08d735fe-8fdf-4f4f-9997-f92738e2cee2", "metadata": {}, "outputs": [], "source": ["adding_outlet_details[\"assortment\"] = np.where(\n", "    adding_outlet_details[\"l0\"] == \"Beauty & Cosmetics\", \"Beauty\", \"30 Mins\"\n", ")\n", "\n", "adding_outlet_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0c457e5f-10f5-44c0-9bcf-03f6a296fdeb", "metadata": {}, "outputs": [], "source": ["item_id_list = tuple(list(adding_outlet_details[\"item_id\"].unique()))\n", "len(item_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "47e3dad4-a7c3-4289-bd89-dc4a4dfb67b2", "metadata": {}, "outputs": [], "source": ["facility_id_list = tuple(list(adding_outlet_details[\"facility_id\"].unique()))\n", "len(facility_id_list)"]}, {"cell_type": "markdown", "id": "0fd8c14c-dbb2-4daf-8ec9-fc751a607cf9", "metadata": {}, "source": ["# Tea Taggings Details"]}, {"cell_type": "code", "execution_count": null, "id": "eef214e5-dc59-48bc-9dd3-4e60b8ac132e", "metadata": {}, "outputs": [], "source": ["def tea_taggings():\n", "    tea_taggings = f\"\"\"\n", "    \n", "    with\n", "    tea_taggings as\n", "        (select item_id, outlet_id as hot_outlet_id, cast(tag_value as int) as be_hot_outlet_id\n", "            from rpc.item_outlet_tag_mapping\n", "                where tag_type_id = 8 and active = 1\n", "        )\n", "        \n", "            select * from tea_taggings\n", "                where item_id in {item_id_list}\n", "    \"\"\"\n", "    return read_sql_query(tea_taggings, trino)\n", "\n", "\n", "tea_taggings = tea_taggings()"]}, {"cell_type": "code", "execution_count": null, "id": "703262e6-5e19-4726-bf3e-44982cc698f5", "metadata": {}, "outputs": [], "source": ["tea_taggings.head()"]}, {"cell_type": "code", "execution_count": null, "id": "03571526-7ccd-4281-b872-b304f431f3db", "metadata": {}, "outputs": [], "source": ["adding_tea_tagging_outlet = pd.merge(\n", "    tea_taggings, all_hot_name, on=[\"be_hot_outlet_id\"], how=\"inner\"\n", ")\n", "\n", "adding_tea_tagging_outlet.head()"]}, {"cell_type": "markdown", "id": "b783ba60-408d-4e66-9e98-7f8bbd1e50b8", "metadata": {}, "source": ["# ARS Mapping Details"]}, {"cell_type": "code", "execution_count": null, "id": "6ed5ae60-984b-4eaf-ab90-9132d7be3eae", "metadata": {}, "outputs": [], "source": ["def ars_mapping():\n", "    ars_mapping = \"\"\"\n", "    \n", "    with\n", "    ars_mapping as\n", "        (select facility_id as be_facility_id, outlet_id as hot_outlet_id\n", "\n", "            from po.bulk_facility_outlet_mapping\n", "\n", "                where active = true\n", "        )\n", "\n", "            select * from ars_mapping\n", "    \n", "    \"\"\"\n", "    return read_sql_query(ars_mapping, trino)\n", "\n", "\n", "ars_mapping = ars_mapping()"]}, {"cell_type": "code", "execution_count": null, "id": "7450f7da-4de6-4a44-a67a-c52ec77254ed", "metadata": {}, "outputs": [], "source": ["ars_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "172e4903-a7e8-41bc-9150-9ffc078497aa", "metadata": {}, "outputs": [], "source": ["adding_ars_mapping = pd.merge(\n", "    adding_tea_tagging_outlet,\n", "    ars_mapping,\n", "    on=[\"be_facility_id\", \"hot_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_ars_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "493ff329-77c5-4369-a451-643eb4f375e5", "metadata": {}, "outputs": [], "source": ["adding_tea_details = pd.merge(\n", "    adding_outlet_details,\n", "    adding_ars_mapping,\n", "    on=[\"item_id\", \"hot_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "\n", "adding_tea_details[\"be_inv_outlet_id\"] = np.where(\n", "    (adding_tea_details[\"be_hot_outlet_id\"] == 0),\n", "    0,\n", "    adding_tea_details[\"be_inv_outlet_id\"],\n", ")\n", "\n", "adding_tea_details[\"be_facility_id\"] = np.where(\n", "    (adding_tea_details[\"be_hot_outlet_id\"] == 0),\n", "    0,\n", "    adding_tea_details[\"be_facility_id\"],\n", ")\n", "\n", "adding_tea_details[\"be_facility_name\"] = np.where(\n", "    (adding_tea_details[\"be_hot_outlet_id\"] == 0),\n", "    \"Direct Frontend Alignment\",\n", "    adding_tea_details[\"be_facility_name\"],\n", ")\n", "\n", "adding_tea_details[[\"be_hot_outlet_id\", \"be_inv_outlet_id\", \"be_facility_id\"]] = (\n", "    adding_tea_details[[\"be_hot_outlet_id\", \"be_inv_outlet_id\", \"be_facility_id\"]]\n", "    .fillna(-1)\n", "    .astype(int)\n", ")\n", "\n", "adding_tea_details[\"be_facility_name\"] = adding_tea_details[\"be_facility_name\"].fillna(\n", "    \"Non-TEA\"\n", ")\n", "\n", "\n", "adding_tea_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "12193a7b-1fb9-45e2-90bf-e2d99155b9ac", "metadata": {}, "outputs": [], "source": ["del [\n", "    active_assortment,\n", "    adding_outlet_details,\n", "    tea_taggings,\n", "    adding_tea_tagging_outlet,\n", "    ars_mapping,\n", "    adding_ars_mapping,\n", "    item_details,\n", "]\n", "\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "7c6566b6-6b45-4773-92fc-973c4b7af3c9", "metadata": {}, "outputs": [], "source": ["be_inv_outlet_list = (\n", "    adding_tea_details[[\"be_inv_outlet_id\"]]\n", "    .rename(columns={\"be_inv_outlet_id\": \"inv_outlet_id\"})\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "fe_inv_outlet_list = (\n", "    adding_tea_details[[\"hot_outlet_id\"]]\n", "    .rename(columns={\"hot_outlet_id\": \"inv_outlet_id\"})\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "all_inv_outlet_list = be_inv_outlet_list.append(fe_inv_outlet_list)\n", "all_inv_outlet_list = tuple(list(all_inv_outlet_list[\"inv_outlet_id\"].unique()))\n", "print(all_inv_outlet_list)"]}, {"cell_type": "code", "execution_count": null, "id": "b8fc3cd8-fa5e-4cb1-817e-1d1c9b5896a0", "metadata": {}, "outputs": [], "source": ["be_hot_outlet_list = (\n", "    adding_tea_details[[\"be_hot_outlet_id\"]]\n", "    .rename(columns={\"be_hot_outlet_id\": \"hot_outlet_id\"})\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "fe_hot_outlet_list = (\n", "    adding_tea_details[[\"hot_outlet_id\"]]\n", "    .rename(columns={\"hot_outlet_id\": \"hot_outlet_id\"})\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "all_hot_outlet_list = be_hot_outlet_list.append(fe_hot_outlet_list)\n", "all_hot_outlet_list = tuple(list(all_hot_outlet_list[\"hot_outlet_id\"].unique()))\n", "print(all_hot_outlet_list)"]}, {"cell_type": "markdown", "id": "7e58acda-ed43-496b-901b-09a07d54c819", "metadata": {}, "source": ["# Inventory Details"]}, {"cell_type": "code", "execution_count": null, "id": "9e82d5cf-b30f-455f-aeda-818fc0a48a6c", "metadata": {}, "outputs": [], "source": ["def inventory():\n", "    inventory = f\"\"\"\n", "    \n", "    with\n", "    inv as\n", "        (select item_id, outlet_id as hot_outlet_id, sum(quantity) as actual_inv\n", "            from ims.ims_item_inventory\n", "                where active = 1\n", "                    group by 1,2\n", "        )\n", "        \n", "            select * from inv\n", "                where hot_outlet_id in {all_inv_outlet_list}\n", "    \"\"\"\n", "    return read_sql_query(inventory, trino)\n", "\n", "\n", "inventory = inventory()"]}, {"cell_type": "code", "execution_count": null, "id": "20bc1211-a51c-4723-93b3-56e0011f0426", "metadata": {}, "outputs": [], "source": ["inventory.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0a83cae0-26f6-4df7-be26-1d12a0547f8e", "metadata": {}, "outputs": [], "source": ["be_inv = inventory.rename(\n", "    columns={\"hot_outlet_id\": \"be_inv_outlet_id\", \"actual_inv\": \"be_actual_inv\"}\n", ")\n", "be_inv.head()"]}, {"cell_type": "code", "execution_count": null, "id": "72209c50-f984-4b3c-8d08-4bf4146f45a3", "metadata": {}, "outputs": [], "source": ["adding_fe_inv_details = pd.merge(\n", "    adding_tea_details, inventory, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "adding_be_inv_details = pd.merge(\n", "    adding_fe_inv_details, be_inv, on=[\"item_id\", \"be_inv_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_inv_details[[\"actual_inv\", \"be_actual_inv\"]] = (\n", "    adding_be_inv_details[[\"actual_inv\", \"be_actual_inv\"]].fillna(0).astype(int)\n", ")\n", "\n", "adding_be_inv_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "370ffd4f-fcaf-4a25-a80a-eb9f00998006", "metadata": {}, "outputs": [], "source": ["del [adding_tea_details, inventory, adding_fe_inv_details]\n", "\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "23c7d510-67ed-408a-a3e7-ab3e60e0f383", "metadata": {}, "source": ["# Blocked Inventory Details"]}, {"cell_type": "code", "execution_count": null, "id": "76d40711-fe7a-4ecf-b36a-33ee3a3ad54a", "metadata": {}, "outputs": [], "source": ["def blocked_inv():\n", "    blocked_inv = f\"\"\"\n", "    \n", "    with\n", "    blocked as\n", "        (select item_id, outlet_id as hot_outlet_id,\n", "            sum(quantity) as ttl_blocked_qty\n", "                from ims.ims_item_blocked_inventory\n", "                    where active = 1 and blocked_type in (1,2,5) and quantity > 0\n", "                        group by 1,2\n", "        )\n", "        \n", "            select * from blocked \n", "                where hot_outlet_id in {all_inv_outlet_list}\n", "    \"\"\"\n", "    return read_sql_query(blocked_inv, trino)\n", "\n", "\n", "blocked_inv = blocked_inv()"]}, {"cell_type": "code", "execution_count": null, "id": "bcd9a5b9-f4e6-4be8-98c0-f521e8b6db73", "metadata": {}, "outputs": [], "source": ["blocked_inv.head()"]}, {"cell_type": "code", "execution_count": null, "id": "eb192066-83d6-44c5-9004-eeb1d1b9a69f", "metadata": {}, "outputs": [], "source": ["be_blocked = blocked_inv.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_inv_outlet_id\",\n", "        \"ttl_blocked_qty\": \"be_ttl_blocked_qty\",\n", "    }\n", ")\n", "be_blocked.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a899a92a-d5fa-43cb-9cd9-8965496d042a", "metadata": {}, "outputs": [], "source": ["adding_fe_blocked_details = pd.merge(\n", "    adding_be_inv_details, blocked_inv, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_blocked_details = pd.merge(\n", "    adding_fe_blocked_details,\n", "    be_blocked,\n", "    on=[\"item_id\", \"be_inv_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_be_blocked_details[[\"ttl_blocked_qty\", \"be_ttl_blocked_qty\"]] = (\n", "    adding_be_blocked_details[[\"ttl_blocked_qty\", \"be_ttl_blocked_qty\"]]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_be_blocked_details[\"net_inventory\"] = np.where(\n", "    (\n", "        adding_be_blocked_details[\"actual_inv\"]\n", "        - adding_be_blocked_details[\"ttl_blocked_qty\"]\n", "    )\n", "    < 0,\n", "    0,\n", "    (\n", "        adding_be_blocked_details[\"actual_inv\"]\n", "        - adding_be_blocked_details[\"ttl_blocked_qty\"]\n", "    ),\n", ")\n", "\n", "adding_be_blocked_details[\"be_net_inventory\"] = np.where(\n", "    (\n", "        adding_be_blocked_details[\"be_actual_inv\"]\n", "        - adding_be_blocked_details[\"be_ttl_blocked_qty\"]\n", "    )\n", "    < 0,\n", "    0,\n", "    (\n", "        adding_be_blocked_details[\"be_actual_inv\"]\n", "        - adding_be_blocked_details[\"be_ttl_blocked_qty\"]\n", "    ),\n", ")\n", "\n", "\n", "adding_be_blocked_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a0da1299-1065-4054-955d-b5194e74838f", "metadata": {}, "outputs": [], "source": ["del [\n", "    blocked_inv,\n", "    adding_fe_blocked_details,\n", "    adding_be_inv_details,\n", "]\n", "\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "88831a6f-fc74-42dd-a771-9feb0216e1f7", "metadata": {}, "source": ["# Pending Putaway Details"]}, {"cell_type": "code", "execution_count": null, "id": "a5d14010-50fa-4d11-9d7d-3b9bccea0cb6", "metadata": {}, "outputs": [], "source": ["def pen_put():\n", "    pen_put = f\"\"\"\n", "    \n", "    with\n", "    pp as\n", "        (select rpc.item_id, outlet_id as hot_outlet_id, sum(quantity) as pending_putaway\n", "            from ims.ims_good_inventory igi\n", "                join rpc.product_product rpc on rpc.upc = igi.upc_id and igi.variant_id = rpc.variant_id\n", "                    where igi.active = 1 and igi.inventory_update_type_id in (28,76)\n", "                        group by 1,2\n", "        )\n", "        \n", "            select * from pp \n", "                where hot_outlet_id in {all_inv_outlet_list}\n", "    \"\"\"\n", "    return read_sql_query(pen_put, trino)\n", "\n", "\n", "pen_put = pen_put()"]}, {"cell_type": "code", "execution_count": null, "id": "f5e2bd1b-df56-444c-8aac-8c57fa90d5e0", "metadata": {}, "outputs": [], "source": ["pen_put.head()"]}, {"cell_type": "code", "execution_count": null, "id": "dc0e546e-44af-46a9-b806-d853ce9aaec5", "metadata": {}, "outputs": [], "source": ["be_pen_put = pen_put.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_inv_outlet_id\",\n", "        \"pending_putaway\": \"be_pending_putaway\",\n", "    }\n", ")\n", "be_pen_put.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6a71e3d0-40b3-4e02-be53-1e8b413ed08a", "metadata": {}, "outputs": [], "source": ["adding_fe_pen_putaway_details = pd.merge(\n", "    adding_be_blocked_details, pen_put, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_pen_putaway_details = pd.merge(\n", "    adding_fe_pen_putaway_details,\n", "    be_pen_put,\n", "    on=[\"item_id\", \"be_inv_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "adding_be_pen_putaway_details[[\"pending_putaway\", \"be_pending_putaway\"]] = (\n", "    adding_be_pen_putaway_details[[\"pending_putaway\", \"be_pending_putaway\"]]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_be_pen_putaway_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "35ee1bb0-c33c-49b7-b49b-c6a9b0e97912", "metadata": {}, "outputs": [], "source": ["del [\n", "    pen_put,\n", "    adding_be_blocked_details,\n", "    adding_fe_pen_putaway_details,\n", "]\n", "\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "83c1030d-9eae-4329-bfb4-6624a2a074f3", "metadata": {}, "source": ["# frontend min max details"]}, {"cell_type": "code", "execution_count": null, "id": "f0788a6a-d513-45b6-948f-3c1ff0f04690", "metadata": {}, "outputs": [], "source": ["def min_max():\n", "    min_max = f\"\"\"\n", "    \n", "    with\n", "    min_max as\n", "        (select facility_id, item_id, min_quantity, max_quantity\n", "        \n", "            from ars.item_min_max_quantity\n", "        )\n", "        \n", "            select * from min_max \n", "                where facility_id in {facility_id_list}\n", "    \n", "    \"\"\"\n", "    return read_sql_query(min_max, trino)\n", "\n", "\n", "min_max = min_max()"]}, {"cell_type": "code", "execution_count": null, "id": "bab317d4-ff28-4427-89e8-94f668979413", "metadata": {}, "outputs": [], "source": ["min_max.head()"]}, {"cell_type": "code", "execution_count": null, "id": "543d3f0b-522b-4d0d-a33c-4eb485b5d05f", "metadata": {}, "outputs": [], "source": ["adding_min_max_details = pd.merge(\n", "    adding_be_pen_putaway_details, min_max, on=[\"item_id\", \"facility_id\"], how=\"left\"\n", ")\n", "\n", "adding_min_max_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7b1d6924-40f3-4e09-b57b-9e6834378f61", "metadata": {}, "outputs": [], "source": ["del [\n", "    adding_be_pen_putaway_details,\n", "    min_max,\n", "]\n", "\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "9356a3ed-dad6-4ce8-a01c-5f68e63b7dbc", "metadata": {}, "source": ["# item city weights details"]}, {"cell_type": "code", "execution_count": null, "id": "5f86736e-9e20-4026-ad84-2a7b9a9ab642", "metadata": {}, "outputs": [], "source": ["def item_city_weight():\n", "    item_city_weight = \"\"\"\n", "    \n", "    with\n", "    item_city_weights as\n", "        (select city as city_name, item_id, weights as item_city_weight\n", "            from metrics.city_item_cart_penetration icp\n", "                where icp.updated_at = (select max(updated_at) as updated_at from metrics.city_item_cart_penetration)\n", "        )\n", "            select * from item_city_weights\n", "    \n", "    \"\"\"\n", "    return read_sql_query(item_city_weight, redshift)\n", "\n", "\n", "item_city_weight = item_city_weight()"]}, {"cell_type": "code", "execution_count": null, "id": "908f0a82-877f-43f2-b881-9ca037f5688d", "metadata": {}, "outputs": [], "source": ["item_city_weight.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0bbf0838-1173-46dc-8182-cfaf25a2cd07", "metadata": {}, "outputs": [], "source": ["adding_item_city_weight = pd.merge(\n", "    adding_min_max_details,\n", "    item_city_weight,\n", "    on=[\"item_id\", \"city_name\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_item_city_weight[\"item_city_weight\"] = (\n", "    adding_item_city_weight[\"item_city_weight\"].fillna(0).astype(float)\n", ")\n", "\n", "adding_item_city_weight.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6366aeee-d9cd-4847-abdf-7e5dad8de233", "metadata": {}, "outputs": [], "source": ["del [\n", "    adding_min_max_details,\n", "    item_city_weight,\n", "]\n", "\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "572ba28e-4f23-4f00-b58d-cfa33e61ae22", "metadata": {}, "outputs": [], "source": ["be_facility_id_list = tuple(list(adding_item_city_weight[\"be_facility_id\"].unique()))\n", "len(be_facility_id_list)"]}, {"cell_type": "markdown", "id": "9f918b61-527c-4354-ab06-91377216b705", "metadata": {}, "source": ["# city store weights"]}, {"cell_type": "code", "execution_count": null, "id": "bd8fe991-d8bf-4a03-958f-d354f24b09d2", "metadata": {}, "outputs": [], "source": ["def city_store_weight():\n", "    city_store_weight = \"\"\"\n", "    \n", "    with\n", "    city_store_weights as\n", "        (select facility_id, store_weight\n", "            from metrics.city_store_penetration\n", "                where updated_at = (select max(updated_at) as updated_at from metrics.city_store_penetration)\n", "        )\n", "\n", "            select * from city_store_weights\n", "    \n", "    \"\"\"\n", "    return read_sql_query(city_store_weight, redshift)\n", "\n", "\n", "city_store_weight = city_store_weight()"]}, {"cell_type": "code", "execution_count": null, "id": "fc3348b6-7cae-4026-9580-a93d98118fdb", "metadata": {}, "outputs": [], "source": ["city_store_weight.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c20d9f9f-c8a2-42a2-8829-3277d19f2691", "metadata": {}, "outputs": [], "source": ["adding_city_store_weight = pd.merge(\n", "    adding_item_city_weight,\n", "    city_store_weight,\n", "    on=[\"facility_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_city_store_weight[\"store_weight\"] = (\n", "    adding_city_store_weight[\"store_weight\"].fillna(0).astype(float)\n", ")\n", "\n", "adding_city_store_weight[\"hour_\"] = (datetime.now() + timedelta(hours=5.5)).hour\n", "\n", "adding_city_store_weight.head()"]}, {"cell_type": "code", "execution_count": null, "id": "957ee4c0-5336-4c03-a7cb-2956b9e2b2a5", "metadata": {}, "outputs": [], "source": ["del [adding_item_city_weight, city_store_weight]\n", "\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "185e4a46-dd33-4788-815e-882effe3ab28", "metadata": {}, "source": ["# city hour weights"]}, {"cell_type": "code", "execution_count": null, "id": "fed07b08-55db-4cb3-962c-48e6a4c48b7a", "metadata": {}, "outputs": [], "source": ["def city_hour_weight():\n", "    city_hour_weight = \"\"\"\n", "    \n", "    with\n", "    city_hour_weights as\n", "        (select city as city_name, order_hour as hour_, weights as hour_weight\n", "            from metrics.city_hour_cart_penetration\n", "                where updated_at = (select max(updated_at) as updated_at from metrics.city_hour_cart_penetration)\n", "        )\n", "\n", "            select * from city_hour_weights\n", "    \n", "    \"\"\"\n", "    return read_sql_query(city_hour_weight, redshift)\n", "\n", "\n", "city_hour_weight = city_hour_weight()"]}, {"cell_type": "code", "execution_count": null, "id": "d9492002-c13a-487e-86ec-2340a1fa8c8f", "metadata": {}, "outputs": [], "source": ["city_hour_weight.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2ddf5f6d-4398-4b02-b9bc-94fe57ce3e6d", "metadata": {}, "outputs": [], "source": ["adding_city_hour_weight = pd.merge(\n", "    adding_city_store_weight,\n", "    city_hour_weight,\n", "    on=[\"city_name\", \"hour_\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_city_hour_weight[\"hour_weight\"] = (\n", "    adding_city_hour_weight[\"hour_weight\"].fillna(0).astype(float)\n", ")\n", "\n", "adding_city_hour_weight.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e835d5a5-933e-473b-95db-827d3b390465", "metadata": {}, "outputs": [], "source": ["del [adding_city_store_weight, city_hour_weight]\n", "\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "a993f930-3f9e-48c8-9e14-1960c0a806fd", "metadata": {}, "source": ["# cluster weights"]}, {"cell_type": "code", "execution_count": null, "id": "485b9d36-3f07-4953-bb55-5677f2c3f014", "metadata": {}, "outputs": [], "source": ["def cluster_weight():\n", "    cluster_weight = \"\"\"\n", "    \n", "    with\n", "    cluster_weights as\n", "        (select cluster as city_name, weight as cluster_weight\n", "            from metrics.cluster_weight\n", "                where updated_at = (select max(updated_at) as updated_at from metrics.cluster_weight)\n", "        )\n", "\n", "            select * from cluster_weights\n", "    \n", "    \"\"\"\n", "    return read_sql_query(cluster_weight, redshift)\n", "\n", "\n", "cluster_weight = cluster_weight()"]}, {"cell_type": "code", "execution_count": null, "id": "91e199d6-d160-4dbe-8e52-ab9239e726b1", "metadata": {}, "outputs": [], "source": ["cluster_weight.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2855f3db-3f0e-4310-b3d3-2bcdb983273c", "metadata": {}, "outputs": [], "source": ["adding_cluster_weight = pd.merge(\n", "    adding_city_hour_weight,\n", "    cluster_weight,\n", "    on=[\"city_name\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_cluster_weight[\"cluster_weight\"] = (\n", "    adding_cluster_weight[\"cluster_weight\"].fillna(0).astype(float)\n", ")\n", "\n", "adding_cluster_weight.head()"]}, {"cell_type": "code", "execution_count": null, "id": "deac8ca4-3ad8-4b86-acac-11fb125b2aef", "metadata": {}, "outputs": [], "source": ["del [adding_city_hour_weight, cluster_weight]\n", "\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "f7ec9a0d-4cf7-4074-aba2-a460c4e2a832", "metadata": {}, "source": ["# vendor details query"]}, {"cell_type": "code", "execution_count": null, "id": "56b25fee-291f-453a-a095-f7832ca7dc09", "metadata": {}, "outputs": [], "source": ["def vendor_details():\n", "    vendor_details = f\"\"\"\n", "    \n", "    with\n", "    vendor_details as\n", "        (select vfa.facility_id as be_facility_id, vfa.item_id, vfa.vendor_id, v.vendor_name,\n", "\n", "            case when ifg.load_size is null then vpfa.load_size else ifg.load_size end as load_size,\n", "            case when ifg.load_type is null then vpfa.load_type else ifg.load_type end as load_type,\n", "            case when ifg.group_id is null then 0 else ifg.group_id end as group_id,\n", "\n", "            case when ifg.po_days is null then mpfa.po_days else ifg.po_days end as po_days,\n", "            case when ifg.po_cycle is null then mpfa.po_cycle else ifg.po_cycle end as po_cycle,\n", "\n", "            ipfa.case_sensitivity_type\n", "\n", "\n", "                from vms.vms_vendor_facility_alignment vfa\n", "\n", "                    join\n", "                        (select distinct item_id, brand_id \n", "                            from rpc.product_product\n", "                                where id in (select max(id) as id from rpc.product_product where active = 1 and approved = 1 group by item_id)\n", "                        ) rpc on rpc.item_id = vfa.item_id\n", "\n", "                    left join\n", "                        rpc.product_brand pb on pb.id = rpc.brand_id and pb.active = 1\n", "\n", "                    left join\n", "                        vms.vms_vendor v on v.id = vfa.vendor_id and v.active = 1\n", "\n", "                    left join (select\n", "                        ifg.alignment_id,\n", "                        ifg.group_id,\n", "                        load_size,\n", "                        load_type,\n", "                        po_cycle,\n", "                        po_days\n", "\n", "                            from vms.item_facility_group_mapping ifg\n", "\n", "                                left join (select id as group_id, load_size, load_type, po_cycle_length as po_cycle,\n", "                                    po_days from vms.load_group_attributes \n", "                                        where active = true) lga on lga.group_id = ifg.group_id\n", "\n", "                                    where ifg.active = true) ifg on ifg.alignment_id = vfa.id\n", "                    left join\n", "                        vms.vendor_physical_facility_attributes vpfa on vpfa.vendor_id = vfa.vendor_id\n", "                            and vpfa.facility_id = vfa.facility_id and vpfa.active = 1\n", "\n", "                    left join\n", "                        vms.vendor_manufacturer_physical_facility_attributes mpfa on mpfa.vendor_id = vfa.vendor_id\n", "                            and mpfa.manufacturer_id = pb.manufacturer_id and mpfa.facility_id = vfa.facility_id and mpfa.active = 1 and mpfa.po_model = 2\n", "\n", "                    left join\n", "                        vms.vendor_item_physical_facility_attributes ipfa on ipfa.facility_id = vfa.facility_id\n", "                            and ipfa.item_id = vfa.item_id and ipfa.vendor_id = vfa.vendor_id and ipfa.active = 1\n", "\n", "                        where vfa.active = 1\n", "            )\n", "\n", "                select * from vendor_details\n", "                    where be_facility_id in {be_facility_id_list}\n", "    \n", "    \"\"\"\n", "    return read_sql_query(vendor_details, trino)\n", "\n", "\n", "vendor_details = vendor_details()"]}, {"cell_type": "code", "execution_count": null, "id": "a93350f3-64ab-4c34-b408-d0f6d18e9cb4", "metadata": {}, "outputs": [], "source": ["vendor_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "26b9ed29-217d-440f-998f-5c458be663ec", "metadata": {}, "outputs": [], "source": ["adding_vendor_details = pd.merge(\n", "    adding_cluster_weight,\n", "    vendor_details,\n", "    on=[\"item_id\", \"be_facility_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_vendor_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "df9270b1-bd49-42dc-aae5-4d430acd7355", "metadata": {}, "outputs": [], "source": ["del [\n", "    adding_cluster_weight,\n", "    vendor_details,\n", "]\n", "\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "90ab15fd-bd34-4c5f-b21b-0f72f58e47d6", "metadata": {}, "source": ["# vendor tat details"]}, {"cell_type": "code", "execution_count": null, "id": "ecf4d103-b378-471d-9b8d-4f1e0f1f2797", "metadata": {}, "outputs": [], "source": ["def vendor_tat():\n", "    vendor_tat = f\"\"\"\n", "    \n", "    with\n", "    tat as\n", "        (select outlet_id as be_hot_outlet_id, item_id, vendor_id, tat_days\n", "            from po.item_outlet_vendor_tat\n", "                where active = 1\n", "        )\n", "\n", "            select * from tat\n", "                where be_hot_outlet_id in {all_hot_outlet_list}\n", "    \"\"\"\n", "    return read_sql_query(vendor_tat, trino)\n", "\n", "\n", "vendor_tat = vendor_tat()"]}, {"cell_type": "code", "execution_count": null, "id": "a71ef56b-91d9-4cdc-a332-83d8f4386018", "metadata": {}, "outputs": [], "source": ["vendor_tat.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a40d41f5-8cc4-4a64-85a2-4d821bcfa4a2", "metadata": {}, "outputs": [], "source": ["adding_vendor_tat_details = pd.merge(\n", "    adding_vendor_details,\n", "    vendor_tat,\n", "    on=[\"item_id\", \"be_hot_outlet_id\", \"vendor_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_vendor_tat_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ecfda5c8-db54-41da-8773-3022d2ab1fa5", "metadata": {}, "outputs": [], "source": ["del [\n", "    adding_vendor_details,\n", "    vendor_tat,\n", "]\n", "\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "4a938076-d976-4a9b-85fa-6475ba5a55f0", "metadata": {}, "outputs": [], "source": ["final = adding_vendor_tat_details[\n", "    [\n", "        \"updated_at_ist\",\n", "        \"hot_outlet_id\",\n", "        \"assortment\",\n", "        \"item_id\",\n", "        \"min_quantity\",\n", "        \"max_quantity\",\n", "        \"actual_inv\",\n", "        \"ttl_blocked_qty\",\n", "        \"net_inventory\",\n", "        \"pending_putaway\",\n", "        \"be_hot_outlet_id\",\n", "        \"be_actual_inv\",\n", "        \"be_ttl_blocked_qty\",\n", "        \"be_net_inventory\",\n", "        \"be_pending_putaway\",\n", "        \"vendor_id\",\n", "        \"vendor_name\",\n", "        \"load_size\",\n", "        \"load_type\",\n", "        \"group_id\",\n", "        \"po_days\",\n", "        \"po_cycle\",\n", "        \"case_sensitivity_type\",\n", "        \"tat_days\",\n", "        \"item_city_weight\",\n", "        \"store_weight\",\n", "        \"hour_weight\",\n", "        \"cluster_weight\",\n", "    ]\n", "]\n", "\n", "del [adding_vendor_tat_details]\n", "\n", "gc.collect()\n", "\n", "final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9a576165-92d7-4640-a295-1dcf88220079", "metadata": {}, "outputs": [], "source": ["final[[\"min_quantity\", \"max_quantity\"]] = (\n", "    final[[\"min_quantity\", \"max_quantity\"]].fillna(0).astype(int)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "df80cb01-884a-470d-acd9-d93226f3d6eb", "metadata": {}, "outputs": [], "source": ["final.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "c519b9d7-bbe5-402c-b4c0-b6e20b3f6378", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"updated_at_ist\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"table update timestamp in ist\",\n", "    },\n", "    {\n", "        \"name\": \"hot_outlet_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend hot outlet id\",\n", "    },\n", "    {\"name\": \"assortment\", \"type\": \"varchar\", \"description\": \"type of assortment\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item id details\"},\n", "    {\"name\": \"min_quantity\", \"type\": \"integer\", \"description\": \"frontend min quantity\"},\n", "    {\"name\": \"max_quantity\", \"type\": \"integer\", \"description\": \"frontend max quantity\"},\n", "    {\"name\": \"actual_inv\", \"type\": \"integer\", \"description\": \"frontend actual inv\"},\n", "    {\n", "        \"name\": \"ttl_blocked_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend blocked inv\",\n", "    },\n", "    {\n", "        \"name\": \"net_inventory\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend net inventory\",\n", "    },\n", "    {\n", "        \"name\": \"pending_putaway\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend pending putaway inv\",\n", "    },\n", "    {\n", "        \"name\": \"be_hot_outlet_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend hot outlet id\",\n", "    },\n", "    {\"name\": \"be_actual_inv\", \"type\": \"integer\", \"description\": \"backend actual inv\"},\n", "    {\n", "        \"name\": \"be_ttl_blocked_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend blocked inv\",\n", "    },\n", "    {\n", "        \"name\": \"be_net_inventory\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend net inventory\",\n", "    },\n", "    {\n", "        \"name\": \"be_pending_putaway\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend pending putaway inv\",\n", "    },\n", "    {\"name\": \"vendor_id\", \"type\": \"float\", \"description\": \"backend aligned vendor id\"},\n", "    {\n", "        \"name\": \"vendor_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"backend aligned vendor name\",\n", "    },\n", "    {\"name\": \"load_size\", \"type\": \"float\", \"description\": \"backend aligned load size\"},\n", "    {\"name\": \"load_type\", \"type\": \"float\", \"description\": \"backend aligned load type\"},\n", "    {\n", "        \"name\": \"group_id\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend aligned vendor group id\",\n", "    },\n", "    {\n", "        \"name\": \"po_days\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"backend aligned vendor po day\",\n", "    },\n", "    {\n", "        \"name\": \"po_cycle\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend aligned vendor po cycle\",\n", "    },\n", "    {\n", "        \"name\": \"case_sensitivity_type\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend aligned vendor case sensitivity typecycle\",\n", "    },\n", "    {\"name\": \"tat_days\", \"type\": \"float\", \"description\": \"backend aligned vendor tat\"},\n", "    {\"name\": \"item_city_weight\", \"type\": \"float\", \"description\": \"city item weight\"},\n", "    {\"name\": \"store_weight\", \"type\": \"float\", \"description\": \"city store weight\"},\n", "    {\"name\": \"hour_weight\", \"type\": \"float\", \"description\": \"city hour weight\"},\n", "    {\"name\": \"cluster_weight\", \"type\": \"float\", \"description\": \"city cluster weight\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "a044033a-c979-4fe4-bb91-c650fb7b68bf", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"thirty_min_hourly_snapshot\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"updated_at_ist\", \"item_id\", \"hot_outlet_id\"],\n", "    \"sortkey\": [\"updated_at_ist\", \"item_id\", \"hot_outlet_id\"],\n", "    \"incremental_key\": \"item_id\",\n", "    \"load_type\": \"append\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"30 mins assortment inventory snapshot\",\n", "}\n", "pb.to_redshift(final, **kwargs)\n", "\n", "print(\"final_base write complete\")"]}, {"cell_type": "code", "execution_count": null, "id": "84322dd7-9449-40d3-8b3a-eb2c2020e667", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}