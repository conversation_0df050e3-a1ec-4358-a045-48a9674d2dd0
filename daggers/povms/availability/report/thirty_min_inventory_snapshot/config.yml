alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: thirty_min_inventory_snapshot
dag_type: report
escalation_priority: low
executor:
  config:
    cpu:
      limit: 2
      request: 1
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 8G
      request: 1G
    node_selectors:
      nodetype: airflow-dags-spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: povms
notebook:
  parameters: null
  retries: 1
owner:
  email: <EMAIL>
  slack_id: U03RR39TSTZ
path: povms/availability/report/thirty_min_inventory_snapshot
paused: true
project_name: availability
schedule:
  end_date: '2024-02-13T18:30:00'
  interval: 1 * * * *
  start_date: '2024-02-12T09:13:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
pool: povms_pool
