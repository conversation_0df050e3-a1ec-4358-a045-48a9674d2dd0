{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "tags": []}, "outputs": [], "source": ["import os\n", "import sys\n", "import time\n", "\n", "st = time.time()\n", "\n", "\n", "def fetchDataFromDB(query):\n", "    import pencilbox as pb\n", "    import pandas as pd\n", "    import time\n", "\n", "    con = pb.get_connection(\"[Warehouse] Trino\")\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            df = pd.read_sql_query(sql=query, con=con)\n", "            return df\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error fetching data (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to fetch data after {max_retries} attempts: {e}\")\n", "                return pd.DataFrame()\n", "\n", "\n", "def fetchPackagedItemUniverse():\n", "    query = \"\"\"\n", "  SELECT *\n", "  FROM\n", "    (SELECT icd.item_id,\n", "            icd.name,\n", "            icd.l0,\n", "            icd.l1,\n", "            icd.l2,\n", "            icd.product_type,\n", "            CASE\n", "                WHEN id.perishable = 1 THEN 'PERISHABLE'\n", "                ELSE 'PACKAGED'\n", "            END AS item_type,\n", "            id.storage_type AS storage_type_raw,\n", "            CASE\n", "                WHEN id.storage_type IN ('1',\n", "                                        '8',\n", "                                        '11') THEN 'REGULAR'\n", "                WHEN id.storage_type IN ('4',\n", "                                        '5') THEN 'HEAVY'\n", "                WHEN id.storage_type IN ('2',\n", "                                        '6') THEN 'COLD'\n", "                WHEN id.storage_type IN ('3',\n", "                                        '7') THEN 'FROZEN'\n", "                ELSE 'REGULAR'\n", "            END AS storage_type,\n", "            CASE\n", "                WHEN id.handling_type IN ('8') THEN 'PACKAGING MATERIAL'\n", "                WHEN id.handling_type IN ('6') THEN 'MEDICINAL'\n", "                ELSE 'REGULAR'\n", "            END AS handling_type,\n", "            id.variant_mrp AS mrp,\n", "            id.variant_description,\n", "            id.weight_in_gm,\n", "            id.length_in_cm,\n", "            id.height_in_cm,\n", "            id.breadth_in_cm,\n", "            id.shelf_life,\n", "            coalesce(itf.item_factor, 0.01) AS item_factor,\n", "            DATE_DIFF('day',date(icd.created_at), CURRENT_DATE) AS item_catalog_age,\n", "            rank() OVER (PARTITION BY id.item_id\n", "                        ORDER BY id.id DESC) AS variant_rank,\n", "                        itm.tag_value AS custom_storage_type_raw,\n", "                        CASE\n", "                            WHEN itm.tag_value = '1' THEN 'BEAUTY'\n", "                            WHEN itm.tag_value = '2' THEN 'BOUQUET'\n", "                            WHEN itm.tag_value = '3' THEN 'PREMIUM'\n", "                            WHEN itm.tag_value = '4' THEN 'BOOKS'\n", "                            WHEN itm.tag_value = '5' THEN 'NON_VEG'\n", "                            WHEN itm.tag_value = '6' THEN 'ICE_CREAM'\n", "                            WHEN itm.tag_value = '7' THEN 'TOYS'\n", "                            WHEN itm.tag_value = '8' THEN 'ELECTRONICS' -- when itm.tag_value = '9' then 'FESTIVE'\n", "\n", "                            WHEN itm.tag_value = '10' THEN 'VERTICAL_CHUTES'\n", "                            WHEN itm.tag_value = '11' THEN 'BEST_SERVED_COLD'\n", "                            WHEN itm.tag_value = '12' THEN 'CRITICAL_SKUS'\n", "                            WHEN itm.tag_value = '13' THEN 'LARGE'\n", "                            WHEN itm.tag_value = '14' THEN 'APPAREL'\n", "                            WHEN itm.tag_value = '15' THEN 'SPORTS'\n", "                            WHEN itm.tag_value = '16' THEN 'PET_CARE'\n", "                            WHEN itm.tag_value = '17' THEN 'HOME_DECOR'\n", "                            WHEN itm.tag_value = '18' THEN 'KITCHEN_DINING'\n", "                            WHEN itm.tag_value = '19' THEN 'HOME_FURNISHING'\n", "                            WHEN itm.tag_value = '20' THEN 'LONGTAIL_OTHERS'\n", "                            WHEN itm.tag_value IS NULL THEN 'NO_CONFIG'\n", "                            ELSE 'UNKNOWN_CONFIG'\n", "                        END AS custom_storage_type,\n", "                        pb.manufacturer_id,\n", "                        id.manufacturer AS manufacturer_name,\n", "                        pb.name AS brand_name,\n", "                      coalesce(id.outer_case_size,1) as outer_case_size,\n", "                      coalesce(id.inner_case_size,1) as inner_case_size,\n", "                        case when itm2.tag_value = '1' then TRUE else FALSE end as is_high_value,\n", "                        itm2.tag_value as high_value_tag_raw\n", "    FROM rpc.item_category_details icd\n", "    INNER JOIN rpc.product_product id ON id.item_id = icd.item_id\n", "    AND id.active = 1\n", "    AND id.approved = 1\n", "    AND id.lake_active_record\n", "    LEFT JOIN supply_etls.item_factor itf ON itf.item_id = icd.item_id\n", "    LEFT JOIN rpc.item_tag_mapping itm ON itm.tag_type_id = 7\n", "    AND itm.active = TRUE\n", "    AND itm.lake_active_record\n", "    AND itm.item_id = icd.item_id\n", "    LEFT JOIN rpc.product_brand pb ON id.brand_id = pb.id\n", "    AND pb.lake_active_record\n", "    AND pb.active = 1\n", "    LEFT JOIN rpc.item_tag_mapping itm2 on itm2.item_id = icd.item_id\n", "      AND itm2.active \n", "      AND itm2.tag_type_id = 3\n", "      AND itm2.lake_active_record\n", "    WHERE icd.lake_active_record\n", "      AND perishable != 1\n", "      AND id.handling_type != '8'\n", "      AND id.storage_type NOT IN ('3',\n", "                                  '7')\n", "      AND icd.l0_id != 1487 -- removing vegetables and fruits\n", "\n", "      AND icd.l0 NOT IN ('wholesale store',\n", "                          'Trial new tree',\n", "                          'Specials')-- removing test and flyer/freebie l0s\n", "  ) AS x\n", "  WHERE variant_rank = 1\n", "\"\"\"\n", "    print(query)\n", "    return fetchDataFromDB(query)\n", "\n", "\n", "def fetchPackagedBackEndOutletDetails():\n", "    # https://reports.grofer.io/queries/186767/source#204197\n", "    query = \"\"\"\n", "        WITH bo AS\n", "  (SELECT om.facility_id,\n", "          om.outlet_id AS outlet_id,\n", "          om.outlet_name AS outlet_name,\n", "          rcl.name AS city_name,\n", "          rcs.name AS state,\n", "          wom.cloud_store_id AS inv_outlet_id\n", "   FROM po.physical_facility_outlet_mapping om\n", "   INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "   AND rco.business_type_id != 7\n", "   AND rco.lake_active_record\n", "   INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "   AND rcl.lake_active_record\n", "   INNER JOIN retail.console_state AS rcs ON rcs.id = rcl.state_id\n", "   AND rcs.lake_active_record\n", "   INNER JOIN\n", "     (SELECT DISTINCT warehouse_id,\n", "                      cloud_store_id\n", "      FROM retail.warehouse_outlet_mapping\n", "      WHERE active = 1\n", "        AND lake_active_record) wom ON wom.warehouse_id = om.outlet_id\n", "   WHERE om.active = 1\n", "     AND om.ars_active = 1\n", "     AND om.lake_active_record\n", "     AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "     AND om.facility_id != 273 )\n", "SELECT *\n", "FROM bo\n", "WHERE outlet_id IN\n", "    (SELECT DISTINCT backend_outlet_id\n", "     FROM rpc.transfer_tag_rules\n", "     WHERE active\n", "       AND lake_active_record\n", "       AND category = 'PACKAGING_GOODS')\n", "    \"\"\"\n", "    print(query)\n", "    return fetchDataFromDB(query)\n", "\n", "\n", "def getFestivePtypes(lookahead=30):\n", "    query = f\"\"\"\n", "  WITH festive_items AS\n", "  (WITH latest_uploads AS\n", "     (SELECT festival_name,\n", "             item_id,\n", "             max(bu_job_id) AS bu_job\n", "      FROM ars_etls.bulk_process_festival_quantity_planned bpf\n", "      WHERE (sale_end_date >= CURRENT_DATE\n", "             AND sale_end_date <= CURRENT_DATE + interval '{lookahead}' DAY)\n", "        OR (sale_start_date >= CURRENT_DATE\n", "            AND sale_start_date <= CURRENT_DATE + interval '{lookahead}' DAY)\n", "        AND active\n", "        AND festival_name IS NOT NULL\n", "      GROUP BY 1,\n", "               2) SELECT DISTINCT f.item_id\n", "   FROM ars_etls.bulk_process_festival_quantity_planned f\n", "   INNER JOIN latest_uploads lu ON f.item_id = lu.item_id\n", "   AND f.bu_job_id = lu.bu_job\n", "   WHERE f.planned_qty > 0\n", "     AND f.festival_name IS NOT NULL\n", "   GROUP BY 1)\n", "SELECT DISTINCT cat.l0,\n", "                cat.l1,\n", "                cat.l2,\n", "                cat.product_type\n", "FROM rpc.item_category_details cat\n", "WHERE cat.item_id IN\n", "    ( SELECT item_id\n", "     FROM festive_items)\n", "  AND cat.lake_active_record\n", "  \"\"\"\n", "    print(query)\n", "    return fetchDataFromDB(query)\n", "\n", "\n", "def pushToGoogleSheetsWithRetry(df, sheet_id, sheet_name):\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "    import pencilbox as pb\n", "    import time\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            pb.to_sheets(df, sheet_id, sheet_name)\n", "            return\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error fetching data (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to fetch data after {max_retries} attempts: {e}\")\n", "                return"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from datetime import datetime, timedelta, date\n", "import math\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "FileName = \"Dead/Excess Inventory Analysis\"\n", "# https://docs.google.com/spreadsheets/d/1C_WmQseZAZTNqeZo5hLlOVyLgRYPdU8jziQzG-m4-vc/edit?usp=sharing\n", "SheetId = \"1C_WmQseZAZTNqeZo5hLlOVyLgRYPdU8jziQzG-m4-vc\"\n", "SheetName = \"raw\"\n", "doi_threshold = 30\n", "\n", "USE_LOCAL_CSV_DATA = False\n", "\n", "if USE_LOCAL_CSV_DATA:\n", "    festive_ptypes_df_raw = pd.read_csv(\"festive_ptypes_df.csv\")\n", "    packaged_universe_df = pd.read_csv(\"universe.csv\")\n", "    be_df = pd.read_csv(\"be_df.csv\")\n", "else:\n", "    festive_ptypes_df_raw = getFestivePtypes()\n", "    festive_ptypes_df_raw.to_csv(\"festive_ptypes_df.csv\", index=False)\n", "    packaged_universe_df = fetchPackagedItemUniverse()\n", "    packaged_universe_df.to_csv(\"universe.csv\", index=False)\n", "    be_df = fetchPackagedBackEndOutletDetails()\n", "    be_df.to_csv(\"be_df.csv\", index=False)\n", "\n", "overall_df = pd.DataFrame()\n", "\n", "\n", "def generateExcessInventoryData(df):\n", "    outlet_ids = \", \".join(f\"'{item}'\" for item in map(str, df[\"outlet_id\"].unique()))\n", "    inv_outlet_ids = \", \".join(map(str, df[\"inv_outlet_id\"].unique()))\n", "\n", "    lt_cpd_looback = 90\n", "    mt_cpd_looback = 60\n", "    if not USE_LOCAL_CSV_DATA:\n", "        query = f\"\"\"\n", "      SELECT co.name AS Outlet,\n", "            w.outlet_id,\n", "            wasl.zone_identifier ZONE,\n", "            wil.item_id,\n", "            sum(wil.quantity) qty,\n", "            count(DISTINCT wasl.id) AS active_locations\n", "    FROM warehouse_location.warehouse_item_location wil\n", "    JOIN warehouse_location.warehouse_storage_location wasl on wil.location_id = wasl.id\n", "    JOIN warehouse_location.warehouse_outlet_mapping w ON w.warehouse_id = wasl.warehouse_id\n", "    JOIN warehouse_location.warehouse_shelf s ON s.id = wasl.shelf_id\n", "    JOIN warehouse_location.warehouse_rack r ON r.id = s.rack_id\n", "    JOIN warehouse_location.warehouse_rack_type rt ON rt.id = r.rack_type_id\n", "    JOIN warehouse_location.warehouse_aisle a ON a.id = r.aisle_id\n", "    JOIN warehouse_location.warehouse_zone z ON z.id = a.zone_id\n", "    JOIN warehouse_location.warehouse_floor f ON z.floor_id = f.id\n", "    JOIN retail.console_outlet co ON co.id = w.outlet_id\n", "    WHERE w.outlet_id IN ({inv_outlet_ids})\n", "      AND wasl.is_active = 1\n", "      and wil.quantity > 0\n", "    GROUP BY 1,\n", "              2,\n", "              3,\n", "              4\n", "    ORDER BY 1,\n", "              2,\n", "              3\n", "      \"\"\"\n", "        print(query)\n", "        inventory_df = fetchDataFromDB(query)\n", "        inventory_df.to_csv(\"location_wise_inventory_df.csv\", index=False)\n", "\n", "        query = f\"\"\"\n", "      WITH fo AS\n", "    (SELECT om.facility_id,\n", "            om.outlet_id AS outlet_id,\n", "            mo_map.frontend_merchant_id AS merchant_id,\n", "            om.outlet_name AS outlet_name,\n", "            rcl.name AS city_name,\n", "            rcs.name AS state\n", "    FROM po.physical_facility_outlet_mapping om\n", "    INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "    AND rco.business_type_id IN (7)\n", "    AND rco.lake_active_record\n", "    INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "    AND rcl.lake_active_record\n", "    INNER JOIN dwh.dim_merchant_outlet_facility_mapping mo_map ON mo_map.facility_id = om.facility_id\n", "    AND rco.business_type_id = 7\n", "    AND is_current\n", "    AND is_current_mapping_active\n", "    AND is_backend_merchant_active\n", "    AND is_frontend_merchant_active\n", "    INNER JOIN retail.console_state AS rcs ON rcs.id = rcl.state_id\n", "    AND rcs.lake_active_record\n", "    WHERE om.active = 1\n", "      AND om.ars_active = 1\n", "      AND om.lake_active_record\n", "      AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "      AND om.facility_id != 273 )\n", "  SELECT cast(tea.tag_value AS int) AS outlet_id,\n", "        ss.item_id,\n", "        sum(ss.aps_adjusted) AS fe_cpd_total,\n", "        sum(CASE\n", "                WHEN pfma.master_assortment_substate_id = 1 THEN 1\n", "                ELSE 0\n", "            END) AS count_active_stores,\n", "        sum(CASE\n", "                WHEN pfma.master_assortment_substate_id = 3 THEN 1\n", "                ELSE 0\n", "            END) AS count_temp_inactive_stores,\n", "        sum(CASE\n", "                WHEN pfma.master_assortment_substate_id != 1\n", "                      AND pfma.master_assortment_substate_id != 3 THEN 1\n", "                ELSE 0\n", "            END) AS count_inactive_stores\n", "  FROM ars.outlet_item_aps_derived_cpd ss\n", "  INNER JOIN rpc.item_outlet_tag_mapping tea ON tea.outlet_id = ss.outlet_id\n", "  AND tea.item_id = ss.item_id\n", "  AND tea.active = 1\n", "  AND tea.tag_value IN ({outlet_ids})\n", "  AND tea.tag_type_id = 8\n", "  AND tea.lake_active_record\n", "  INNER JOIN fo ON fo.outlet_id = tea.outlet_id\n", "  INNER JOIN rpc.product_facility_master_assortment pfma ON pfma.facility_id = fo.facility_id\n", "  AND pfma.item_id = tea.item_id\n", "  AND pfma.active = 1\n", "  AND pfma.lake_active_record\n", "  WHERE insert_ds_ist = cast(CURRENT_DATE - interval '1' DAY AS varchar)\n", "    AND for_date = CURRENT_DATE - interval '1' DAY\n", "    AND ss.lake_active_record\n", "  GROUP BY 1,\n", "          2\n", "      \"\"\"\n", "        print(query)\n", "        cpd_df = fetchDataFromDB(query)\n", "        cpd_df.to_csv(\"cpd_df.csv\", index=False)\n", "\n", "        longTermItemCpdQuery = f\"\"\"\n", "          WITH fo AS\n", "      (SELECT om.facility_id,\n", "              om.outlet_id AS outlet_id,\n", "              mo_map.frontend_merchant_id AS merchant_id,\n", "              om.outlet_name AS outlet_name,\n", "              rcl.name AS city_name,\n", "              rcs.name AS state\n", "      FROM po.physical_facility_outlet_mapping om\n", "      INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "      AND rco.business_type_id IN (7)\n", "      AND rco.lake_active_record\n", "      INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "      AND rcl.lake_active_record\n", "      INNER JOIN dwh.dim_merchant_outlet_facility_mapping mo_map ON mo_map.facility_id = om.facility_id\n", "      AND rco.business_type_id = 7\n", "      AND is_current\n", "      AND is_current_mapping_active\n", "      AND is_backend_merchant_active\n", "      AND is_frontend_merchant_active\n", "      INNER JOIN retail.console_state AS rcs ON rcs.id = rcl.state_id\n", "      AND rcs.lake_active_record\n", "      WHERE om.active = 1\n", "        AND om.ars_active = 1\n", "        AND om.lake_active_record\n", "        AND om.facility_id != 273 )\n", "        \n", "    SELECT cast(tea.tag_value as int) as be_outlet_id,\n", "          ord.item_id,\n", "          sum(availability_factor) AS availability_factor,\n", "          count(distinct(ord.facility_id)) AS available_store_count,\n", "          sum(order_quantity) AS order_quantity,\n", "          sum(potential_order_quantity) AS potential_order_quantity\n", "    FROM ars.daily_orders_and_availability ord\n", "    INNER JOIN fo ON fo.facility_id = ord.facility_id\n", "    INNER JOIN rpc.item_outlet_tag_mapping tea ON tea.outlet_id = fo.outlet_id\n", "    AND tea.item_id = ord.item_id\n", "    AND tea.active = 1\n", "    AND tea.tag_value in ({outlet_ids})\n", "    AND tea.tag_type_id = 8\n", "    AND tea.lake_active_record\n", "    WHERE ord.insert_ds_ist >= cast(CURRENT_DATE - interval '{lt_cpd_looback}' DAY AS varchar)\n", "      AND ord.insert_ds_ist < cast(CURRENT_DATE AS varchar)\n", "      AND ord.lake_active_record\n", "    GROUP BY 1,2\n", "         \"\"\"\n", "        print(longTermItemCpdQuery)\n", "        lt_cpd_df = fetchDataFromDB(longTermItemCpdQuery)\n", "        lt_cpd_df.to_csv(\"lt_cpd_df.csv\", index=False)\n", "\n", "        mTermItemCpdQuery = f\"\"\"\n", "          WITH fo AS\n", "      (SELECT om.facility_id,\n", "              om.outlet_id AS outlet_id,\n", "              mo_map.frontend_merchant_id AS merchant_id,\n", "              om.outlet_name AS outlet_name,\n", "              rcl.name AS city_name,\n", "              rcs.name AS state\n", "      FROM po.physical_facility_outlet_mapping om\n", "      INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "      AND rco.business_type_id IN (7)\n", "      AND rco.lake_active_record\n", "      INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "      AND rcl.lake_active_record\n", "      INNER JOIN dwh.dim_merchant_outlet_facility_mapping mo_map ON mo_map.facility_id = om.facility_id\n", "      AND rco.business_type_id = 7\n", "      AND is_current\n", "      AND is_current_mapping_active\n", "      AND is_backend_merchant_active\n", "      AND is_frontend_merchant_active\n", "      INNER JOIN retail.console_state AS rcs ON rcs.id = rcl.state_id\n", "      AND rcs.lake_active_record\n", "      WHERE om.active = 1\n", "        AND om.ars_active = 1\n", "        AND om.lake_active_record\n", "        AND om.facility_id != 273 )\n", "        \n", "    SELECT cast(tea.tag_value as int) as be_outlet_id,\n", "           ord.item_id,\n", "          sum(availability_factor) AS availability_factor,\n", "          count(distinct(ord.facility_id)) AS available_store_count,\n", "          sum(order_quantity) AS order_quantity,\n", "          sum(potential_order_quantity) AS potential_order_quantity\n", "    FROM ars.daily_orders_and_availability ord\n", "    INNER JOIN fo ON fo.facility_id = ord.facility_id\n", "    INNER JOIN rpc.item_outlet_tag_mapping tea ON tea.outlet_id = fo.outlet_id\n", "    AND tea.item_id = ord.item_id\n", "    AND tea.active = 1\n", "    AND tea.tag_value in ({outlet_ids})\n", "    AND tea.tag_type_id = 8\n", "    AND tea.lake_active_record\n", "    WHERE ord.insert_ds_ist >= cast(CURRENT_DATE - interval '{mt_cpd_looback}' DAY AS varchar)\n", "      AND ord.insert_ds_ist < cast(CURRENT_DATE AS varchar)\n", "      AND ord.lake_active_record\n", "    GROUP BY 1,2\n", "         \"\"\"\n", "        print(mTermItemCpdQuery)\n", "        mt_cpd_df = fetchDataFromDB(mTermItemCpdQuery)\n", "        mt_cpd_df.to_csv(\"mt_cpd_df.csv\", index=False)\n", "    else:\n", "        cpd_df = pd.read_csv(\"cpd_df.csv\")\n", "        inventory_df = pd.read_csv(\"location_wise_inventory_df.csv\")\n", "        lt_cpd_df = pd.read_csv(\"lt_cpd_df.csv\")\n", "        mt_cpd_df = pd.read_csv(\"mt_cpd_df.csv\")\n", "\n", "    if inventory_df.shape[0] == 0:\n", "        return pd.DataFrame()\n", "\n", "    inventory_df.rename(columns={\"outlet_id\": \"inv_outlet_id\"}, inplace=True)\n", "    inventory_df[\"storage_location_type\"] = inventory_df[\"ZONE\"].str.split(\"_\").str[0]\n", "    inventory_df = inventory_df[inventory_df[\"storage_location_type\"].isin([\"REGULAR\", \"SPR\"])]\n", "\n", "    inventory_df_grouped = inventory_df.pivot_table(\n", "        index=[\"item_id\", \"inv_outlet_id\"],\n", "        columns=\"storage_location_type\",\n", "        values=[\"qty\"],\n", "        aggfunc={\"qty\": \"sum\"},\n", "    )\n", "    inventory_df_grouped = inventory_df_grouped.fillna(0)\n", "    inventory_df_grouped = inventory_df_grouped.reset_index()\n", "    flat_cols = [\"_\".join(filter(None, col)) for col in inventory_df_grouped.columns.tolist()]\n", "    inventory_df_grouped.columns = flat_cols\n", "\n", "    inventory_df_total = (\n", "        inventory_df.groupby([\"inv_outlet_id\", \"item_id\"]).agg({\"qty\": \"sum\"}).reset_index()\n", "    )\n", "\n", "    inventory_df_total.columns = [\"inv_outlet_id\", \"item_id\", \"total_qty\"]\n", "    inventory_df_grouped = pd.merge(\n", "        inventory_df_grouped, inventory_df_total, on=[\"inv_outlet_id\", \"item_id\"], how=\"left\"\n", "    )\n", "    inventory_df_grouped = pd.merge(inventory_df_grouped, be_df, on=\"inv_outlet_id\", how=\"left\")\n", "    inventory_df_grouped = pd.merge(\n", "        inventory_df_grouped, cpd_df, on=[\"outlet_id\", \"item_id\"], how=\"left\"\n", "    )\n", "    inventory_df_grouped = pd.merge(\n", "        inventory_df_grouped, packaged_universe_df, on=[\"item_id\"], how=\"inner\"\n", "    )\n", "\n", "    inventory_df_grouped.fillna(0, inplace=True)\n", "\n", "    inventory_df_grouped[\"doi\"] = inventory_df_grouped.apply(\n", "        lambda row: row[\"total_qty\"] / row[\"fe_cpd_total\"] if row[\"fe_cpd_total\"] != 0 else 10000,\n", "        axis=1,\n", "    )\n", "    inventory_df_grouped[\"doi\"].fillna(inventory_df_grouped[\"doi\"].max(), inplace=True)\n", "    inventory_df_grouped.sort_values(\n", "        by=[\"inv_outlet_id\", \"doi\"], ascending=[False, False], inplace=True\n", "    )\n", "    inventory_df_grouped[\"doi\"] = inventory_df_grouped[\"doi\"].round(2)\n", "\n", "    inventory_df_grouped = inventory_df_grouped[inventory_df_grouped[\"doi\"] > doi_threshold]\n", "    festive_ptypes_df = festive_ptypes_df_raw[[\"product_type\"]]\n", "    festive_ptypes_df.drop_duplicates(inplace=True)\n", "    festive_ptypes_df[\"festive_flag\"] = True\n", "    inventory_df_grouped = pd.merge(\n", "        inventory_df_grouped, festive_ptypes_df, on=[\"product_type\"], how=\"left\"\n", "    )\n", "    inventory_df_grouped.festive_flag.fillna(False, inplace=True)\n", "    inventory_df_grouped = inventory_df_grouped[~inventory_df_grouped[\"festive_flag\"]]\n", "    # inventory_df_grouped = inventory_df_grouped[~inventory_df_grouped['l1'].isin(['Festive & Occasion Needs','Pooja Needs','Festive Gifting','Candles & Diyas','Decorative Lights'])]\n", "    # inventory_df_grouped = inventory_df_grouped[~inventory_df_grouped['product_type'].isin(festive_ptypes_df['product_type'].values)]\n", "    # inventory_df_grouped = inventory_df_grouped[~inventory_df_grouped['product_type'].str.lower().isin(diwali_ptypes['ptypes'].values)]\n", "    inventory_df_grouped[\"inactive_flag\"] = inventory_df_grouped.apply(\n", "        lambda x: False if x[\"count_active_stores\"] + x[\"count_temp_inactive_stores\"] > 0 else True,\n", "        axis=1,\n", "    )\n", "    inventory_df_grouped = inventory_df_grouped[\n", "        (inventory_df_grouped[\"item_catalog_age\"] > 100) | (inventory_df_grouped[\"inactive_flag\"])\n", "    ]\n", "\n", "    inventory_df_grouped[\"regular_storage_space_used\"] = (\n", "        inventory_df_grouped[\"qty_REGULAR\"] * inventory_df_grouped[\"item_factor\"]\n", "    )\n", "    inventory_df_grouped.sort_values(\n", "        by=[\"doi\", \"regular_storage_space_used\"], ascending=[False, False], inplace=True\n", "    )\n", "\n", "    inventory_df_grouped[\"min_inventory_in_regular\"] = inventory_df_grouped[\"fe_cpd_total\"].apply(\n", "        lambda x: max(math.ceil(x * 30), 20)\n", "    )\n", "    inventory_df_grouped[\"min_inventory_in_regular\"] = inventory_df_grouped.apply(\n", "        lambda x: x[\"min_inventory_in_regular\"]\n", "        if x[\"count_active_stores\"] + x[\"count_temp_inactive_stores\"] > 0\n", "        else 0,\n", "        axis=1,\n", "    )\n", "    inventory_df_grouped[\n", "        \"excess_inventory / qty to transfer for deep storage\"\n", "    ] = inventory_df_grouped.apply(\n", "        lambda row: row[\"qty_REGULAR\"] - row[\"min_inventory_in_regular\"], axis=1\n", "    )\n", "    inventory_df_grouped = inventory_df_grouped[\n", "        inventory_df_grouped[\"excess_inventory / qty to transfer for deep storage\"] > 0\n", "    ]\n", "    inventory_df_grouped[\"excess_regular_space_used\"] = inventory_df_grouped.apply(\n", "        lambda row: row[\"excess_inventory / qty to transfer for deep storage\"] * row[\"item_factor\"],\n", "        axis=1,\n", "    )\n", "    inventory_df_grouped = inventory_df_grouped[\n", "        inventory_df_grouped[\"excess_regular_space_used\"] > 1\n", "    ]\n", "\n", "    if lt_cpd_df.shape[0] > 0:\n", "        lt_cpd_df = lt_cpd_df[[\"be_outlet_id\", \"item_id\", \"potential_order_quantity\"]]\n", "        lt_columnName = \"availability_adjusted_\" + str(lt_cpd_looback) + \"_day_sales_qty\"\n", "        lt_cpd_df.columns = [\"outlet_id\", \"item_id\", lt_columnName]\n", "        inventory_df_grouped = pd.merge(\n", "            inventory_df_grouped, lt_cpd_df, on=[\"outlet_id\", \"item_id\"], how=\"left\"\n", "        )\n", "\n", "    if mt_cpd_df.shape[0] > 0:\n", "        mt_cpd_df = mt_cpd_df[[\"be_outlet_id\", \"item_id\", \"potential_order_quantity\"]]\n", "        mt_columnName = \"availability_adjusted_\" + str(mt_cpd_looback) + \"_day_sales_qty\"\n", "        mt_cpd_df.columns = [\"outlet_id\", \"item_id\", mt_columnName]\n", "        inventory_df_grouped = pd.merge(\n", "            inventory_df_grouped, mt_cpd_df, on=[\"outlet_id\", \"item_id\"], how=\"left\"\n", "        )\n", "\n", "    # inventory_df_grouped['excess_regular_storage_space_used_cumsum'] = inventory_df_grouped['excess_regular_space_used'].cumsum()\n", "    print(inventory_df_grouped.excess_regular_space_used.sum())\n", "    return inventory_df_grouped\n", "\n", "\n", "overall_df = generateExcessInventoryData(be_df)\n", "\n", "import datetime\n", "\n", "today = datetime.date.today()\n", "date_string = today.strftime(\"%d %b %y\")\n", "\n", "overall_df[\"date_\"] = date_string\n", "\n", "pushToGoogleSheetsWithRetry(overall_df, SheetId, SheetName)\n", "\n", "time_taken = time.time() - st\n", "print(f\"<PERSON><PERSON><PERSON> executed in {time_taken:.2f} seconds\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}