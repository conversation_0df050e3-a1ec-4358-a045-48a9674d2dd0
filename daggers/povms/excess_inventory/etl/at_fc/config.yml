alert_configs:
  slack:
  - channel: excess_inventory_dag_alerts
dag_name: at_fc
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U06RTRAMJQ4
path: povms/excess_inventory/etl/at_fc
paused: true
pool: povms_pool
project_name: excess_inventory
schedule:
  end_date: '2025-05-27T00:00:00'
  interval: 30 0 * * *
  start_date: '2025-01-09T00:00:00'
schedule_type: fixed
sla: 840 minutes
support_files: []
tags: []
template_name: notebook
version: 6
