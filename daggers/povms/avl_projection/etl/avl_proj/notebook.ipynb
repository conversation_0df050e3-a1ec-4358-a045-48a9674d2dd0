{"cells": [{"cell_type": "code", "execution_count": null, "id": "c2f08722-1c27-4358-abe5-947f811ce686", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib==3.8"]}, {"cell_type": "code", "execution_count": null, "id": "11d355ba-2af1-4b7b-9274-83015e92812c", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import date, datetime, timedelta\n", "import time\n", "import gc\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 10000000)\n", "trino = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "37c0529f-fb8e-4c50-b6ab-e2a90239d016", "metadata": {}, "outputs": [], "source": ["def gsheet_to_df(sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            df = pb.from_sheets(sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Read Runtime\n", "            formatted_duration = end_time - start_time\n", "\n", "            print(\n", "                f\"Read {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration}\"\n", "            )\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "f097290c-6bd6-4fc0-81f9-fcbd4ec8d651", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", round((end - start) / 60, 2), \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "e5a9cad5-8253-456c-97c3-77fd3e38d331", "metadata": {}, "outputs": [], "source": ["sheet = gsheet_to_df(\"10ZAAN0GrYSxqf9_Ep4No8lD55Y0SUZtgIcD2os2_GFU\", \"Limit\")"]}, {"cell_type": "markdown", "id": "46cd789c-a202-4511-b473-ff35e9ebd3ee", "metadata": {}, "source": ["data = {\n", "    'threshold' : ['0.87']\n", "}\n", "sheet = pd.DataFrame(data)"]}, {"cell_type": "code", "execution_count": null, "id": "46542bc8-9e8f-4a9f-afa0-c603238bce3e", "metadata": {}, "outputs": [], "source": ["sheet[\"threshold\"] = sheet[\"threshold\"].astype(float)"]}, {"cell_type": "markdown", "id": "5829a3b0-1fe6-481e-962b-2b7ef6a04e9b", "metadata": {}, "source": ["sheet = pd.read_csv('fc-central-heads-alert - Limit.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "7d91e2d1-d9a1-44f9-9a45-4ab0dce49338", "metadata": {}, "outputs": [], "source": ["def avl():\n", "    trino = pb.get_connection(\"[Warehouse] Trino\")\n", "    sql = \"\"\"\n", "\n", "with store_carts as (\n", "    select outlet_id as outlet_id, \n", "        cast(avg(c) as int) as carts \n", "    from (\n", "            select outlet_id, \n", "                date(cart_checkout_ts_ist) dt, \n", "                count(distinct order_id) c \n", "            from dwh.fact_sales_order_details x \n", "            where cart_checkout_ts_ist  >= current_date-interval '7' day\n", "                and order_create_ts_ist >=  current_date-interval '7' day\n", "                and order_create_dt_ist >= current_date-interval '7' day\n", "                and order_current_status = 'DELIVERED' \n", "                and order_type not in ('InternalForwardOrder', 'InternalReverseOrder', 'DropShippingInternalReverseOrder') \n", "            group by 1,2\n", "        ) \n", "    group by 1\n", "),\n", "\n", "active_outlets as (\n", "\n", "select hot_outlet_id as outlet_id, facility_id, city_name\n", "    from supply_etls.outlet_details\n", "    where ars_check = 1\n", "    and business_type_id = 7\n", "\n", "),\n", "\n", "-- Raw data at be<>fe<>item<>inv\n", "    -- Considering only packaged goods\n", "    -- And also not considering blocked inv\n", "pg_ass as (\n", "    select item_id from supply_etls.item_details where assortment_type = 'Packaged Goods'\n", "),\n", "    \n", "    -- Assortment Details of only packaged goods and active outlets\n", "pfma_ass as (\n", "    select pfom.facility_id, pfom.outlet_id, pcma.item_id, ao.city_name\n", "    from \n", "    (select item_id,facility_id from rpc.product_facility_master_assortment \n", "    where master_assortment_substate_id in (1) and active = 1) pcma \n", "        inner join\n", "    pg_ass on pg_ass.item_id = pcma.item_id\n", "        inner join \n", "    (select * from po.physical_facility_outlet_mapping where active = 1) pfom on pcma.facility_id = pfom.facility_id\n", "        inner join\n", "    active_outlets ao on ao.outlet_id = pfom.outlet_id\n", "   \n", "),\n", "\n", "be_fe_item as\n", "(select iotm.outlet_id,iotm.item_id,cast(iotm.tag_value as integer) as be_outlet_id,pa.facility_id,pfom.facility_id as be_facility_id, pa.city_name\n", "    from \n", "    (select item_id,tag_value,outlet_id\n", "    from rpc.item_outlet_tag_mapping where tag_type_id = 8 and active = 1 and lake_active_record = True) iotm \n", "        inner join \n", "    pfma_ass pa on pa.outlet_id = iotm.outlet_id and pa.item_id = iotm.item_id\n", "        inner join\n", "    (select * from po.physical_facility_outlet_mapping where active = 1) pfom on  cast(iotm.tag_value as integer) = pfom.outlet_id\n", "),\n", "\n", "actual_inv as (\n", "    select item_id, outlet_id, quantity from ims.ims_item_inventory where active = 1 and lake_active_record\n", "),\n", "\n", "blocked_inv as (\n", "    select outlet_id, item_id, quantity from ims.ims_item_blocked_inventory \n", "    -- where blocked_type in (1,2,5) and lake_active_record\n", "),\n", "\n", "fbi as (\n", "    select outlet_id, item_id, sum(quantity) as bq\n", "    from blocked_inv\n", "    --where blocked_type in (1,2,5) and lake_active_record  --and outlet_id in (3232)\n", "    group by 1,2\n", "),\n", "\n", "final_inv_raw as (\n", "    select ai.outlet_id, ai.item_id, ai.quantity as aq, \n", "    case when ai.quantity - coalesce(bq,0) < 0 then 0 else ai.quantity - coalesce(bq,0) end as current_inv,\n", "    case when ai.quantity - coalesce(bq,0) <= 0 then 0 else 1 end inv_flag\n", "    from actual_inv ai\n", "    left join fbi bi on ai.item_id = bi.item_id and ai.outlet_id = bi.outlet_id\n", "),\n", "\n", "final_inv as (\n", "select bfi.outlet_id, bfi.be_outlet_id, bfi.item_id, pfom.facility_id as be_facility_id, bfi.city_name, coalesce(fir.current_inv,0) as current_inv, coalesce(fir.inv_flag,0) as inv_flag\n", "from\n", "be_fe_item bfi \n", "    inner join \n", "(select * from po.physical_facility_outlet_mapping where active =1) pfom on pfom.outlet_id = bfi.be_outlet_id\n", "    left join\n", "final_inv_raw fir on fir.outlet_id = bfi.outlet_id and fir.item_id = bfi.item_id \n", "\n", "),\n", "\n", "sto_transit_time as (\n", "    with node_facility_mapping as (\n", "        select cast(logistic_node_id as varchar) as store_id,\n", "                facility_id, f.name as facility_name\n", "        from retail.console_outlet_logistic_mapping lm\n", "        inner join retail.console_outlet co on co.id = lm.outlet_id\n", "        inner join retail.warehouse_facility f on f.id = co.facility_id\n", "        where lm.lake_active_record and co.lake_active_record and f.lake_active_record\n", "    ),\n", "    \n", "    in_transit_time as (\n", "        select consignment_id, source_store_id, destination_store_id, \n", "                date_diff('hour', min(logs.install_ts), max(logs.install_ts)) + 3  as delivery_tat ---- add 3 for grn_time and 5 hours for 90%% accuracy\n", "        from transit_server.transit_consignment cons\n", "        inner join transit_server.transit_consignment_state_logs logs on cons.id = logs.consignment_id\n", "        where logs.lake_active_record and cons.lake_active_record\n", "        and cons.insert_ds_ist >= cast(current_date - interval '27' day as varchar)\n", "        and logs.insert_ds_ist >= cast(current_date - interval '27' day as varchar)\n", "        and cons.state = 'COMPLETED'\n", "        and logs.to_state in ('COMPLETED','READY_FOR_DISPATCH')\n", "        GROUP BY 1,2,3\n", "        HAVING count(*) > 1\n", "        AND date_diff('hour', min(logs.install_ts), max(logs.install_ts)) > 0\n", "    ),\n", "    \n", "    cleaned_data as (\n", "        select iit.source_store_id, iit.destination_store_id, \n", "                approx_percentile(delivery_tat, 0.5) as median_tat\n", "        from in_transit_time iit\n", "        group by 1,2\n", "    ),\n", "    \n", "    final_tt as (\n", "        select sn.facility_id as sender_facility_id, sn.facility_name as sender_facility_name, \n", "                dn.facility_id as destination_facility_id, dn.facility_name as destination_facility_name, \n", "                median_tat\n", "        from cleaned_data cd\n", "        inner join node_facility_mapping sn on sn.store_id = cd.source_store_id\n", "        inner join node_facility_mapping dn on dn.store_id = cd.destination_store_id\n", "    )\n", "        \n", "    select * from final_tt),\n", "\n", "dt AS\n", "    (SELECT id as sto_id,\n", "            max(dispatch_time) as dispatch_time\n", "     FROM po.sto\n", "     WHERE created_at >= current_date - interval '2' day \n", "     and lake_active_record\n", "     and active = 1\n", "     group by 1\n", "     ),\n", "\n", "sto_details as (\n", "    select isd.merchant_outlet_id as outlet_id, isi.item_id, isi.reserved_quantity, isi.billed_quantity,\n", "    isd.created_at,\n", "    date_add('minute',330,isd.created_at) as created_at_ist,\n", "    \n", "    date_add('hour',coalesce(stt.median_tat,15),dt.dispatch_time+ interval '330' minute) as expected_time_stamp_ist,\n", "    \n", "    (case when date_diff('hour', current_timestamp, date_add('hour',coalesce(stt.median_tat,15),dt.dispatch_time + interval '330' minute)) >0 then\n", "    sequence(hour(current_timestamp), hour(current_timestamp)+date_diff('hour', current_timestamp, date_add('hour',coalesce(stt.median_tat,15),dt.dispatch_time + interval '330' minute)))\n", "    else sequence(-1,-1) end)  as hour_tuple,\n", "    \n", "    coalesce(bfi.be_outlet_id,-1) as be_outlet_id,\n", "    coalesce(bfi.be_facility_id,-1) as be_facility_id\n", "    \n", "    from (select sto_id,merchant_outlet_id ,created_at\n", "    from ims.ims_sto_details where created_at >= current_date - interval '2' day and lake_active_record and sto_state in (1,2,5)) isd\n", "    inner join \n", "    (select sto_id,item_id, reserved_quantity,billed_quantity\n", "    from ims.ims_sto_item where created_at >= current_date - interval '2' day and lake_active_record) isi\n", "    on isd.sto_id = isi.sto_id\n", "    inner join \n", "    (select sto_id,dispatch_time from dt \n", "    ) dt on dt.sto_id = isi.sto_id\n", "    inner join be_fe_item bfi on bfi.outlet_id = isd.merchant_outlet_id and bfi.item_id = isi.item_id\n", "    left join\n", "    sto_transit_time stt on stt.sender_facility_id = bfi.be_facility_id and stt.destination_facility_id = bfi.facility_id\n", "),\n", "\n", "\n", "\n", "sto_details_max as (\n", "    select be_outlet_id,outlet_id,max(expected_time_stamp_ist) as max_expected_time_stamp_ist, max(created_at_ist) as latest_sto_creation_time,\n", "    date_diff('hour',current_timestamp,max(expected_time_stamp_ist) ) as hours_for_depletion,\n", "    \n", "    case when date_diff('hour', current_timestamp, max(expected_time_stamp_ist)) >0 then\n", "    sequence(hour(current_timestamp), hour(current_timestamp)+date_diff('hour', current_timestamp, max(expected_time_stamp_ist)))\n", "    else sequence(-1,-1) end as hour_tuple_max\n", "\n", "    from sto_details where reserved_quantity>0 or billed_quantity>0\n", "    group by 1,2\n", "    having max(expected_time_stamp_ist) >=current_timestamp\n", "),\n", "sto_details_max_final as (\n", "    select be_outlet_id,outlet_id,max_expected_time_stamp_ist, latest_sto_creation_time,\n", "     hour_value %% 24 as hour_final\n", "    from\n", "        sto_details_max,UNNEST(hour_tuple_max) as t(hour_value)\n", ") ,\n", "\n", "-- cpd details\n", "cpd_cte as (\n", "select outlet_id,item_id,aps_adjusted as aqs,cpd\n", "from ars.outlet_item_aps_derived_cpd where insert_ds_ist = cast(current_date as varchar)\n", "),\n", "bhw_cte as (\n", "select backend_facility_id as be_facility_id,order_hour as hour_,cast(weights as real) as bhw,\n", "pfom.outlet_id as be_outlet_id\n", "from (select * from supply_etls.backend_hour_weights\n", "where updated_at = (select max(updated_at) from supply_etls.backend_hour_weights)) bhw_\n", "inner join \n", "(select * from po.physical_facility_outlet_mapping where active = 1) pfom on cast(cast(bhw_.backend_facility_id as double) as integer) = pfom.facility_id\n", "\n", "),\n", "-- bhw_cte as (\n", "-- select city, order_hour as hour_, cast(weights as real) as bhw\n", "-- from supply_etls.city_hour_weights where updated_at = (select max(updated_at) from supply_etls.city_hour_weights)\n", "\n", "-- ),\n", "\n", "-- biw_cte as (\n", "-- select cast(backend_facility_id as integer) as be_facility_id,item_id,cast(weights as real)  as biw,weights\n", "-- from supply_etls.backend_item_weights\n", "-- where updated_at = (select max(updated_at) from supply_etls.backend_item_weights)\n", "-- ),\n", "biw_cte as (\n", "select city, item_id,cast(weights as real)  as biw  from supply_etls.city_item_weights\n", "where updated_at = (select max(updated_at) from supply_etls.city_item_weights)\n", "),\n", "\n", "\n", "sto_depl as (\n", "select bc.be_outlet_id,outlet_id,sum(bhw) as mul\n", "    from sto_details_max_final sdmf inner join\n", "    bhw_cte bc on\n", "            bc.hour_ = sdmf.hour_final and bc.be_outlet_id = sdmf.be_outlet_id\n", "    group by\n", "    1,2\n", "),\n", "\n", "hour_multiplier as (\n", "select cast(be_facility_id as integer) as be_facility_id , \n", "sum(case when hour_ in (\n", "    extract(hour from current_timestamp),\n", "    (extract(hour from current_timestamp)+1)%%24, \n", "    (extract(hour from current_timestamp)+2)%%24,\n", "    (extract(hour from current_timestamp)+3)%%24, \n", "    (extract(hour from current_timestamp)+4)%%24)\n", "then bhw else 0 end) as w_4,\n", "\n", "sum(case when hour_ in (\n", "    extract(hour from current_timestamp),\n", "    (extract(hour from current_timestamp) + 1) %% 24,\n", "    (extract(hour from current_timestamp) + 2) %% 24,\n", "    (extract(hour from current_timestamp) + 3) %% 24,\n", "    (extract(hour from current_timestamp) + 4) %% 24,\n", "    (extract(hour from current_timestamp) + 5) %% 24,\n", "    (extract(hour from current_timestamp) + 6) %% 24,\n", "    (extract(hour from current_timestamp) + 7) %% 24,\n", "    (extract(hour from current_timestamp) + 8) %% 24\n", ")\n", "then bhw else 0 end) as w_8,\n", "\n", "sum(case when hour_ in (\n", "    extract(hour from current_timestamp),\n", "    (extract(hour from current_timestamp) + 1) %% 24,\n", "    (extract(hour from current_timestamp) + 2) %% 24,\n", "    (extract(hour from current_timestamp) + 3) %% 24,\n", "    (extract(hour from current_timestamp) + 4) %% 24,\n", "    (extract(hour from current_timestamp) + 5) %% 24,\n", "    (extract(hour from current_timestamp) + 6) %% 24,\n", "    (extract(hour from current_timestamp) + 7) %% 24,\n", "    (extract(hour from current_timestamp) + 8) %% 24,\n", "    (extract(hour from current_timestamp) + 9) %% 24,\n", "    (extract(hour from current_timestamp) + 10) %% 24,\n", "    (extract(hour from current_timestamp) + 11) %% 24,\n", "    (extract(hour from current_timestamp) + 12) %% 24\n", ")\n", "then bhw else 0 end) as w_12,\n", "\n", "sum(case when hour_ in (\n", "    extract(hour from current_timestamp),\n", "    (extract(hour from current_timestamp) + 1) %% 24,\n", "    (extract(hour from current_timestamp) + 2) %% 24,\n", "    (extract(hour from current_timestamp) + 3) %% 24,\n", "    (extract(hour from current_timestamp) + 4) %% 24,\n", "    (extract(hour from current_timestamp) + 5) %% 24,\n", "    (extract(hour from current_timestamp) + 6) %% 24,\n", "    (extract(hour from current_timestamp) + 7) %% 24,\n", "    (extract(hour from current_timestamp) + 8) %% 24,\n", "    (extract(hour from current_timestamp) + 9) %% 24,\n", "    (extract(hour from current_timestamp) + 10) %% 24,\n", "    (extract(hour from current_timestamp) + 11) %% 24,\n", "    (extract(hour from current_timestamp) + 12) %% 24,\n", "    (extract(hour from current_timestamp) + 13) %% 24,\n", "    (extract(hour from current_timestamp) + 14) %% 24,\n", "    (extract(hour from current_timestamp) + 15) %% 24,\n", "    (extract(hour from current_timestamp) + 16) %% 24\n", ")\n", "then bhw else 0 end) as w_16\n", "\n", "from bhw_cte group by be_facility_id\n", "),\n", "\n", "raw_depl_sto as (\n", "select fi.be_facility_id, fi.be_outlet_id,fi.outlet_id,fi.item_id, fi.inv_flag,fi.current_inv,\n", "case when fi.current_inv-coalesce(cpd_cte.aqs,0)*coalesce(hm.w_4,0) <0.5 then 0 else 1 end as depl_4_flag,\n", "cpd_cte.aqs,\n", "hm.w_4,\n", "hm.w_8,\n", "hm.w_12,\n", "hm.w_16,\n", "coalesce(max_expected_time_stamp_ist,date '1990-01-01' )     as open_sto_connect_time_stamp,\n", "coalesce(sdm.hours_for_depletion,0) as hours_for_depletion,\n", "coalesce(latest_sto_creation_time,date '1990-01-01') as latest_sto_creation_time,\n", "coalesce(sdp.mul,0) as actual_depl_in_days,\n", "case when \n", "    fi.current_inv\n", "     -coalesce(cpd_cte.aqs,0)*\n", "       coalesce(sdp.mul,0)\n", "        <0.5 \n", "    then 0 else 1 end as sto_connect_flag_depl,\n", "fi.city_name ,\n", "\n", "case when fi.current_inv-coalesce(cpd_cte.aqs,0)*coalesce(hm.w_8,0) <0.5 then 0 else 1 end as depl_8_flag,\n", "case when fi.current_inv-coalesce(cpd_cte.aqs,0)*coalesce(hm.w_12,0) <0.5 then 0 else 1 end as depl_12_flag,\n", "case when fi.current_inv-coalesce(cpd_cte.aqs,0)*coalesce(hm.w_16,0) <0.5 then 0 else 1 end as depl_16_flag,\n", "case when sum(case when coalesce(expected_time_stamp_ist, current_timestamp - interval '1' hour) \n", "    between current_timestamp and current_timestamp + interval '4' hour \n", "    and coalesce(greatest(sd.reserved_quantity, sd.billed_quantity), 0) > 0 then 1 else 0 end) > 0 \n", "    then 1 else 0 end as sto_4_flag,\n", "\n", "case when sum(case when coalesce(expected_time_stamp_ist, current_timestamp - interval '1' hour) \n", "    between current_timestamp and current_timestamp + interval '8' hour \n", "    and coalesce(greatest(sd.reserved_quantity, sd.billed_quantity), 0) > 0 then 1 else 0 end) > 0 \n", "    then 1 else 0 end as sto_8_flag,\n", "\n", "case when sum(case when coalesce(expected_time_stamp_ist, current_timestamp - interval '1' hour) \n", "    between current_timestamp and current_timestamp + interval '12' hour \n", "    and coalesce(greatest(sd.reserved_quantity, sd.billed_quantity), 0) > 0 then 1 else 0 end) > 0 \n", "    then 1 else 0 end as sto_12_flag,\n", "\n", "case when sum(case when coalesce(expected_time_stamp_ist, current_timestamp - interval '1' hour) \n", "    between current_timestamp and current_timestamp + interval '16' hour \n", "    and coalesce(greatest(sd.reserved_quantity, sd.billed_quantity), 0) > 0 then 1 else 0 end) > 0 \n", "    then 1 else 0 end as sto_16_flag,\n", "\n", "case when sum(case when \n", "    coalesce(greatest(sd.reserved_quantity,sd.billed_quantity),0) > 0 \n", "    and\n", "    coalesce(expected_time_stamp_ist,current_timestamp - interval '1' hour) > current_timestamp\n", "    then 1 else 0 end) >0 then 1 else 0 end as sto_connect\n", "  \n", "from \n", "final_inv fi left join\n", "cpd_cte on cpd_cte.outlet_id = fi.outlet_id and cpd_cte.item_id = fi.item_id\n", "left join hour_multiplier hm on hm.be_facility_id = fi.be_facility_id\n", "left join sto_details sd on sd.outlet_id = fi.outlet_id and sd.item_id = fi.item_id\n", "left join sto_details_max sdm on sdm.outlet_id = fi.outlet_id and sdm.be_outlet_id = fi.be_outlet_id\n", "left join sto_depl sdp on sdp.be_outlet_id = fi.be_outlet_id and sdp.outlet_id = fi.outlet_id\n", "group by 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18\n", "\n", "),\n", "wap_agg as (\n", "select \n", "rds.be_facility_id, rds.be_outlet_id as hot_be_outlet_id, \n", "rds.outlet_id, \n", "-- case when sum(coalesce(biw,0)) = 0 then sum(inv_flag*coalesce(biw,0)) else sum(inv_flag*coalesce(biw,0))/sum(coalesce(biw,0)) end as on_hand_avl,\n", "sum(inv_flag*coalesce(biw,0))/sum(coalesce(biw,0)) as on_hand_avl,\n", "sum(inv_flag) as inv_flag,\n", "case when sum(coalesce(biw,0)) = 0 then\n", "sum(greatest(sto_connect_flag_depl,sto_connect)*coalesce(biw,0))\n", "else sum(greatest(sto_connect_flag_depl,sto_connect)*coalesce(biw,0))/sum(coalesce(biw,0)) end as sto_connect,\n", "\n", "max(open_sto_connect_time_stamp) as last_sto_connect_time,\n", "max(rds.hours_for_depletion) as hours_for_depletion,\n", "max(rds.latest_sto_creation_time) as latest_sto_creation_time,\n", "max(rds.actual_depl_in_days) as actual_depl_in_days,\n", "\n", "count(*) as items,\n", "case when sum(coalesce(biw,0)) = 0 then sum(greatest(depl_4_flag,sto_4_flag)*coalesce(biw,0)) \n", "else sum(greatest(depl_4_flag,sto_4_flag)*coalesce(biw,0))/sum(coalesce(biw,0)) end as ct_4_avl,\n", "sum(case when inv_flag>0 and depl_4_flag<0.5 then 1 else 0 end) as ct_4_oos,\n", "sum(case when rds.current_inv=1 and depl_4_flag<0.5 then 1 else 0 end) as ct_4_on_hand_1_oos,\n", "sum(case when inv_flag>0 and depl_4_flag<0.5 and sto_4_flag>0 then 1 else 0 end ) as sto_4_oos,\n", "sum(case when rds.current_inv=1 and depl_4_flag<0.5 and sto_4_flag > 0 then 1 else 0 end) as sto_4_on_hand_1_oos,\n", "sum(case when inv_flag=0 and sto_4_flag>0 then 1 else 0 end ) as sto_4_oos_on_hand,\n", "sum(depl_4_flag) as depl_4_flag,\n", "sum(sto_4_flag) as sto_4_flag,\n", "case when sum(coalesce(biw,0)) = 0 then sum(greatest(depl_8_flag,sto_8_flag)*coalesce(biw,0)) else \n", "sum(greatest(depl_8_flag,sto_8_flag)*coalesce(biw,0))/sum(coalesce(biw,0)) end as ct_8_avl,\n", "sum(case when inv_flag>0 and depl_8_flag<0.5 then 1 else 0 end) as ct_8_oos,\n", "sum(case when rds.current_inv=1 and depl_8_flag<0.5 then 1 else 0 end) as ct_8_on_hand_1_oos,\n", "sum(case when inv_flag>0 and depl_8_flag<0.5 and sto_8_flag>0 then 1 else 0 end ) as sto_8_oos,\n", "sum(case when rds.current_inv=1 and depl_8_flag<0.5 and sto_8_flag > 0 then 1 else 0 end) as sto_8_on_hand_1_oos,\n", "sum(case when inv_flag=0 and sto_8_flag>0 then 1 else 0 end ) as sto_8_oos_on_hand,\n", "sum(depl_8_flag) as depl_8_flag,\n", "sum(sto_8_flag) as sto_8_flag,\n", "case when sum(coalesce(biw,0)) = 0 then sum(greatest(depl_12_flag,sto_12_flag)*coalesce(biw,0)) else \n", "sum(greatest(depl_12_flag,sto_12_flag)*coalesce(biw,0))/sum(coalesce(biw,0)) end as ct_12_avl,\n", "sum(case when inv_flag>0 and depl_12_flag<0.5 then 1 else 0 end) as ct_12_oos,\n", "sum(case when rds.current_inv=1 and depl_12_flag<0.5 then 1 else 0 end) as ct_12_on_hand_1_oos,\n", "sum(case when inv_flag>0 and depl_12_flag<0.5 and sto_12_flag>0 then 1 else 0 end ) as sto_12_oos,\n", "sum(case when rds.current_inv=1 and depl_12_flag<0.5 and sto_12_flag > 0 then 1 else 0 end) as sto_12_on_hand_1_oos,\n", "sum(case when inv_flag=0 and sto_12_flag>0 then 1 else 0 end ) as sto_12_oos_on_hand,\n", "sum(depl_12_flag) as depl_12_flag,\n", "sum(sto_12_flag) as sto_12_flag,\n", "case when sum(coalesce(biw,0)) = 0 then sum(greatest(depl_16_flag,sto_16_flag)*coalesce(biw,0)) else \n", "sum(greatest(depl_16_flag,sto_16_flag)*coalesce(biw,0))/sum(coalesce(biw,0)) end as ct_16_avl,\n", "sum(case when inv_flag>0 and depl_16_flag<0.5 then 1 else 0 end) as ct_16_oos,\n", "sum(case when rds.current_inv=1 and depl_16_flag<0.5 then 1 else 0 end) as ct_16_on_hand_1_oos,\n", "sum(case when inv_flag>0 and depl_16_flag<0.5 and sto_16_flag>0 then 1 else 0 end ) as sto_16_oos,\n", "sum(case when rds.current_inv=1 and depl_16_flag<0.5 and sto_16_flag > 0 then 1 else 0 end) as sto_16_on_hand_1_oos,\n", "sum(case when inv_flag=0 and sto_16_flag>0 then 1 else 0 end ) as sto_16_oos_on_hand,\n", "sum(depl_16_flag) as depl_16_flag,\n", "sum(sto_16_flag) as sto_16_flag\n", "from \n", "raw_depl_sto rds left join biw_cte bc\n", "on rds.city_name = bc.city and rds.item_id = bc.item_id\n", "group by 1,2,3\n", ")\n", "select * from wap_agg\n", "-- raw_depl_sto where outlet_id = 2836\n", "\n", "    \"\"\"\n", "    return read_sql_query(sql, trino)\n", "\n", "\n", "# read_sql_query(sql, trino)\n", "df = avl()\n", "print(df.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "d88351e9-6727-4b61-81d4-65354f0742bb", "metadata": {}, "outputs": [], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "6ba28ce2-d92e-4a03-93dc-d429d2bba55a", "metadata": {}, "outputs": [], "source": ["threshold = sheet[\"threshold\"].iloc[0]"]}, {"cell_type": "markdown", "id": "ada6416e-1158-4406-9f21-9d35f7ca7ded", "metadata": {}, "source": ["Filter out outlets where avl is greater than 87 so that they dont appear in the alert"]}, {"cell_type": "code", "execution_count": null, "id": "080d3ba1-1a23-4b8e-bfc0-49d08d8cf5a2", "metadata": {}, "outputs": [], "source": ["df_87 = tuple(df.query(f\"sto_connect >= @threshold\")[\"outlet_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "a9ae7c3d-2eaa-4599-bb18-f383cabe9363", "metadata": {}, "outputs": [], "source": ["def filter_85(x):\n", "    x = x.query(\"sto_connect < 0.85 and sto_connect >0.00\")\n", "    x = x.sort_values(by=\"sto_connect\")\n", "    return x"]}, {"cell_type": "code", "execution_count": null, "id": "686eaf81-e9ac-4e38-bf41-e18b7d5ad9e0", "metadata": {}, "outputs": [], "source": ["new = df.groupby(by=\"hot_be_outlet_id\").apply(filter_85).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "15d54787-3c83-4ad6-b90f-29a49c9c1198", "metadata": {}, "outputs": [], "source": ["df_alert = df.query(\"outlet_id not in @df_87\")[\n", "    [\n", "        \"hot_be_outlet_id\",\n", "        \"outlet_id\",\n", "        \"on_hand_avl\",\n", "        \"sto_connect\",\n", "        \"last_sto_connect_time\",\n", "        \"latest_sto_creation_time\",\n", "        \"ct_12_avl\",\n", "        \"inv_flag\",\n", "    ]\n", "].copy()"]}, {"cell_type": "code", "execution_count": null, "id": "6a4ce710-f71a-432a-8c25-b98046f3ae3a", "metadata": {}, "outputs": [], "source": ["df_alert[\"last_sto_connect_time\"] = pd.to_datetime(df_alert[\"last_sto_connect_time\"])\n", "df_alert[\"latest_sto_creation_time\"] = pd.to_datetime(df_alert[\"latest_sto_creation_time\"])"]}, {"cell_type": "code", "execution_count": null, "id": "1ac7c194-bbf7-477a-9395-897878aa3c4b", "metadata": {}, "outputs": [], "source": ["df_alert.shape"]}, {"cell_type": "code", "execution_count": null, "id": "53d12a3f-bae6-40b1-aaf4-39ae1abe6dc2", "metadata": {}, "outputs": [], "source": ["df_alert.columns"]}, {"cell_type": "code", "execution_count": null, "id": "bb6e83f4-0b14-40a2-aefc-a13bf8d90147", "metadata": {}, "outputs": [], "source": ["df_test = df_alert.query(\"inv_flag > 100\")[\n", "    [\n", "        \"hot_be_outlet_id\",\n", "        \"outlet_id\",\n", "        \"on_hand_avl\",\n", "        \"sto_connect\",\n", "        \"last_sto_connect_time\",\n", "        \"latest_sto_creation_time\",\n", "        \"ct_12_avl\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "a45e897e-668d-4192-b6ab-38c95669e45d", "metadata": {}, "outputs": [], "source": ["df_test.shape"]}, {"cell_type": "code", "execution_count": null, "id": "59b75613-73d3-4b25-b1f7-7be676d493bd", "metadata": {}, "outputs": [], "source": ["def be():\n", "    trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "    sql = \"\"\"\n", "    with \n", "active_outlets as (\n", "    select hot_outlet_id as hot_be_outlet_id, max(hot_outlet_name) as be_name\n", "    from supply_etls.outlet_details\n", "    where ars_check = 1\n", "    and business_type_id != 7\n", "    group by 1\n", ")\n", "select * from active_outlets\n", "    \"\"\"\n", "    return read_sql_query(sql, trino)\n", "\n", "\n", "be_df = be()"]}, {"cell_type": "code", "execution_count": null, "id": "c211c5fe-058c-4155-b734-af98b6ebb56c", "metadata": {}, "outputs": [], "source": ["def fe():\n", "    trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "    sql = \"\"\"\n", "    with \n", "active_outlets as (\n", "    select hot_outlet_id as outlet_id, max(hot_outlet_name) as fe_name\n", "    from supply_etls.outlet_details\n", "    where ars_check = 1\n", "    and business_type_id = 7\n", "    group by 1\n", ")\n", "select * from active_outlets\n", "    \"\"\"\n", "    return read_sql_query(sql, trino)\n", "\n", "\n", "fe_df = fe()"]}, {"cell_type": "code", "execution_count": null, "id": "e9595feb-ee87-497a-90ba-e77d25f8ceae", "metadata": {}, "outputs": [], "source": ["df_csv_1 = pd.merge(df, fe_df, on=\"outlet_id\", how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "id": "13378727-7476-4356-a791-901051655396", "metadata": {}, "outputs": [], "source": ["df_csv = pd.merge(df_csv_1, be_df, on=\"hot_be_outlet_id\", how=\"inner\")[\n", "    [\n", "        \"be_name\",\n", "        \"fe_name\",\n", "        \"on_hand_avl\",\n", "        \"sto_connect\",\n", "        \"latest_sto_creation_time\",\n", "        \"items\",\n", "        \"ct_12_avl\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "f16d1c7e-394e-4168-96d1-0e8a367d269c", "metadata": {}, "outputs": [], "source": ["def live_outlets():\n", "    trino = pb.get_connection(\"[Warehouse] Trino\")\n", "    sql = \"\"\"\n", "    select outlet_id, od.hot_outlet_name as fe_name  from \n", "    dwh.dim_outlet do inner join supply_etls.outlet_details od on od.hot_outlet_id = do.outlet_id\n", "    where do.is_outlet_active = 1 and do.live_date >= current_date - interval '10' year and is_current and do.business_type_id = 7\n", "    group by 1,2\n", "\n", "    \"\"\"\n", "    return read_sql_query(sql, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "14f747bd-c413-4834-a70f-b2fa110e082b", "metadata": {}, "outputs": [], "source": ["def last_inward_sto():\n", "    trino = pb.get_connection(\"[Warehouse] Trino\")\n", "    sql = \"\"\"\n", "    with fe as (\n", "    select hot_outlet_id, hot_outlet_name from supply_etls.outlet_details\n", "    where business_type_id = 7 and ars_check = 1\n", "    ),\n", "\n", "    be as (\n", "    select inv_outlet_id, hot_outlet_name, hot_outlet_id from supply_etls.outlet_details\n", "    where business_type_id !=7 and ars_check = 1\n", "    )\n", "\n", "    select fe.hot_outlet_name as fe_name,be.hot_outlet_name as be_name,\n", "    max(created_at + interval '330' minute) as last_sto_creation_ts_ist from ims.ims_sto_details isd\n", "    inner join fe on fe.hot_outlet_id = isd.merchant_outlet_id \n", "    inner join be on be.inv_outlet_id = isd.outlet_id\n", "    where sto_state = 4 and lake_active_record \n", "    group by 1,2\n", "    \"\"\"\n", "    return read_sql_query(sql, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "74e085d4-e5c0-4e0b-87b2-4964b006c709", "metadata": {}, "outputs": [], "source": ["df_lo = live_outlets()"]}, {"cell_type": "code", "execution_count": null, "id": "665416a6-b494-4df4-b56d-6fc542154592", "metadata": {}, "outputs": [], "source": ["df_lis = last_inward_sto()"]}, {"cell_type": "code", "execution_count": null, "id": "d2dc39c1-921c-4564-8d77-9f50ecdb0e19", "metadata": {}, "outputs": [], "source": ["df_csv = pd.merge(df_csv, df_lo, on=\"fe_name\", how=\"inner\")[\n", "    [\n", "        \"be_name\",\n", "        \"fe_name\",\n", "        \"on_hand_avl\",\n", "        \"sto_connect\",\n", "        \"latest_sto_creation_time\",\n", "        \"items\",\n", "        \"ct_12_avl\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "68dc959a-8b49-4d35-a986-f705dc6d24c8", "metadata": {}, "outputs": [], "source": ["df_csv = pd.merge(df_csv, df_lis, on=[\"fe_name\", \"be_name\"], how=\"inner\")[\n", "    [\n", "        \"be_name\",\n", "        \"fe_name\",\n", "        \"on_hand_avl\",\n", "        \"sto_connect\",\n", "        \"latest_sto_creation_time\",\n", "        \"last_sto_creation_ts_ist\",\n", "        \"items\",\n", "        \"ct_12_avl\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "d612180c-b8de-49e3-8855-8354bcaf483e", "metadata": {}, "outputs": [], "source": ["def converge(row):\n", "    if row[\"latest_sto_creation_time\"] == \"1990-01-01 00:00:00.000\":\n", "        return pd.to_datetime(row[\"last_sto_creation_ts_ist\"]).strftime(\"%Y-%m-%d %H:%M\")\n", "    else:\n", "        return pd.to_datetime(row[\"latest_sto_creation_time\"]).strftime(\"%Y-%m-%d %H:%M\")"]}, {"cell_type": "code", "execution_count": null, "id": "15f75ef0-2fd7-4603-bd04-89c80a9fff0a", "metadata": {}, "outputs": [], "source": ["df_csv[\"column\"] = df_csv.apply(converge, axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "70711ed1-2473-428d-aa64-7f9dec1066b4", "metadata": {}, "outputs": [], "source": ["df_csv.drop(columns=[\"latest_sto_creation_time\", \"last_sto_creation_ts_ist\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "b752814d-ebef-48af-8e1d-aa18152c60f6", "metadata": {}, "outputs": [], "source": ["df_csv.rename(columns={\"column\": \"latest_sto_creation_time\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "da4fe12d-9aed-4d21-89bd-6479375746d0", "metadata": {}, "outputs": [], "source": ["df_csv[df_csv[\"latest_sto_creation_time\"] == \"1990-01-01 00:00:00.000\"]"]}, {"cell_type": "code", "execution_count": null, "id": "ea2ce0ae-95c5-425b-bee8-562cc1d764cb", "metadata": {}, "outputs": [], "source": ["df_csv.to_csv(\"raw.csv\", index=\"false\")"]}, {"cell_type": "code", "execution_count": null, "id": "f4ec2bf5-69b4-46a9-a582-c1299fced4f1", "metadata": {}, "outputs": [], "source": ["df_test_final = pd.merge(df_test, fe_df, on=\"outlet_id\", how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "id": "dfbb2509-25d0-4aeb-b5e8-2c600b14d0d6", "metadata": {}, "outputs": [], "source": ["df_test_final.shape"]}, {"cell_type": "code", "execution_count": null, "id": "00bbaf21-d853-484b-86f8-a1baaa0620ef", "metadata": {}, "outputs": [], "source": ["df_test_complete = pd.merge(df_test_final, be_df, on=\"hot_be_outlet_id\", how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "id": "0faa945d-46b1-4796-8793-1cbaea4437f4", "metadata": {}, "outputs": [], "source": ["df_test_complete.shape"]}, {"cell_type": "code", "execution_count": null, "id": "4bad9ae7-faf3-4991-8fdd-1af86a3d4219", "metadata": {}, "outputs": [], "source": ["df_test_complete.columns"]}, {"cell_type": "code", "execution_count": null, "id": "2d4efe0a-d9fc-432e-89c3-5b8a704beec7", "metadata": {}, "outputs": [], "source": ["col = list(df_test_complete.columns)\n", "col.remove(\"latest_sto_creation_time\")\n", "col"]}, {"cell_type": "code", "execution_count": null, "id": "933571c2-c886-435c-bcbf-887b0153354a", "metadata": {}, "outputs": [], "source": ["df_test_complete = pd.merge(\n", "    df_test_complete[col],\n", "    df_csv[[\"be_name\", \"fe_name\", \"latest_sto_creation_time\"]],\n", "    on=[\"fe_name\", \"be_name\"],\n", "    how=\"inner\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7cad43ab-2b04-4cdb-afeb-04c69c181fa6", "metadata": {}, "outputs": [], "source": ["df_test_complete.drop(\n", "    [\"hot_be_outlet_id\", \"outlet_id\", \"last_sto_connect_time\"], axis=1, inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ccd5557a-4001-4d09-8dcc-b8f37361d20d", "metadata": {}, "outputs": [], "source": ["df_test_complete = df_test_complete[\n", "    [\"be_name\", \"fe_name\", \"on_hand_avl\", \"sto_connect\", \"latest_sto_creation_time\", \"ct_12_avl\"]\n", "].sort_values(by=[\"be_name\", \"sto_connect\"])"]}, {"cell_type": "code", "execution_count": null, "id": "af5010ae-1069-4297-8f14-7202c968fd35", "metadata": {}, "outputs": [], "source": ["df_test_complete"]}, {"cell_type": "code", "execution_count": null, "id": "6845a658-995c-49e9-81de-0a827f1c978d", "metadata": {}, "outputs": [], "source": ["df_test_complete.rename(\n", "    columns={\n", "        \"be_name\": \"Warehouse\",\n", "        \"fe_name\": \"Store\",\n", "        \"on_hand_avl\": \"Current_Avail_%\",\n", "        \"sto_connect\": \"Avail_%_Post_Indent_Fulfilment\",\n", "        \"latest_sto_creation_time\": \"last_sto_creation_time\",\n", "        \"ct_12_avl\": \"current_time+12hrs_avl\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "da1bf98a-5ab7-4222-93fa-fffae29c2c2d", "metadata": {}, "outputs": [], "source": ["df_test_complete.columns"]}, {"cell_type": "code", "execution_count": null, "id": "0ea75fa8-dc2e-4c56-800e-aecc3dd56a0e", "metadata": {}, "outputs": [], "source": ["df_test_complete[\"Current_Avail_%\"] = (df_test_complete[\"Current_Avail_%\"] * 100).apply(\n", "    lambda x: round(x, 1)\n", ")\n", "df_test_complete[\"current_time+12hrs_avl\"] = (\n", "    df_test_complete[\"current_time+12hrs_avl\"] * 100\n", ").apply(lambda x: round(x, 1))\n", "df_test_complete[\"Avail_%_Post_Indent_Fulfilment\"] = (\n", "    df_test_complete[\"Avail_%_Post_Indent_Fulfilment\"] * 100\n", ").apply(lambda x: round(x, 1))"]}, {"cell_type": "code", "execution_count": null, "id": "f7e1914a-9f1a-4b5e-9636-0780b5eec54e", "metadata": {}, "outputs": [], "source": ["df_test_complete"]}, {"cell_type": "code", "execution_count": null, "id": "6d7296bc-6eba-461a-bccb-fda412bd52db", "metadata": {}, "outputs": [], "source": ["# Define the color function for conditional formatting\n", "def color_sort(value):\n", "    if value is None:\n", "        return \"white\"\n", "    percent = float(value)\n", "    if percent < 0.5:\n", "        return \"white\"\n", "    elif percent < 80:\n", "        return \"#f7a4a4\"  # light red\n", "    elif 80 <= percent <= 85:\n", "        return \"#fae8e8\"  # light pink\n", "    else:\n", "        return \"white\"\n", "\n", "\n", "# Create formatting dictionary\n", "formatting = {\"Current_Avail_%\": color_sort, \"Avail_%_Post_Indent_Fulfilment\": color_sort}\n", "\n", "\n", "# Function to render the DataFrame as a Matplotlib table\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5,\n", "    row_height=0.625,\n", "    font_size=14,\n", "    header_color=\"#40466e\",\n", "    row_colors=[\"#f5f5f5\", \"w\"],\n", "    edge_color=\"grey\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    cell_loc=\"left\",\n", "    conditional_formatting=None,\n", "    **kwargs\n", "):\n", "    fig, ax = plt.subplots(\n", "        figsize=(\n", "            (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        )\n", "    )\n", "    ax.axis(\"off\")\n", "\n", "    # Create the table\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, cellLoc=cell_loc, **kwargs\n", "    )\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    # Set column width\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        row, col = k\n", "        cell.set_edgecolor(edge_color)\n", "\n", "        # Header row\n", "        if row == 0:\n", "            cell.set_text_props(weight=\"bold\", color=\"white\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[row % len(row_colors)])\n", "\n", "            # Make the first column bold\n", "            if col == 0:\n", "                cell.set_text_props(weight=\"bold\")\n", "\n", "            # Apply conditional formatting if provided\n", "            if conditional_formatting and data.columns[col] in conditional_formatting:\n", "                formatting_func = conditional_formatting[data.columns[col]]\n", "                cell_val = data.iloc[row - 1, col]\n", "                cell.set_facecolor(formatting_func(cell_val))\n", "\n", "    return fig, ax\n", "\n", "\n", "# Apply the render_mpl_table function and save the figure\n", "fig, ax = render_mpl_table(df_test_complete, conditional_formatting=formatting)\n", "\n", "# Save the figure as PNG\n", "fig.savefig(\"stores.png\", bbox_inches=\"tight\", dpi=72)\n", "plt.show()\n", "\n", "print(\"Saved styled DataFrame as PNG.\")\n", "# prints png file"]}, {"cell_type": "code", "execution_count": null, "id": "19554f40-9d99-4682-b48f-01f7425a9df1", "metadata": {}, "outputs": [], "source": ["# channel = \"alerts-test\"\n", "\n", "channel = sheet[\"channel\"].iloc[0]\n", "text_req = f\"Stores where Avail_%_Post_Indent_Fulfilment < {sheet['threshold'].iloc[0]}\"\n", "pb.send_slack_message(\n", "    channel=channel,\n", "    text=text_req,\n", "    files=[\"stores.png\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2c89ad8c-5f13-4032-92f4-0a5436e6466b", "metadata": {}, "outputs": [], "source": ["# channel = \"alerts-test\"\n", "# fc-heads-central-team\n", "channel = sheet[\"channel\"].iloc[0]\n", "text_req = f\"Stores Availability raw_file (ct_12_avl --- current_time + 12_hour avl) is \"\n", "pb.send_slack_message(\n", "    channel=channel,\n", "    text=text_req,\n", "    files=[\"raw.csv\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4f3df27c-a0ad-4e9e-8839-fba74dbff3e8", "metadata": {}, "outputs": [], "source": ["# channel = \"alerts-test\"\n", "\n", "channel = \"bl-rep-ops-core\"\n", "text_req = f\"Stores where Avail_%_Post_Indent_Fulfilment < {sheet['threshold'].iloc[0]}\"\n", "pb.send_slack_message(\n", "    channel=channel,\n", "    text=text_req,\n", "    files=[\"stores.png\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f20c83bc-3482-43b4-9260-cdbd1393cda5", "metadata": {}, "outputs": [], "source": ["# channel = \"alerts-test\"\n", "# fc-heads-central-team\n", "channel = \"bl-rep-ops-core\"\n", "text_req = f\"Stores Availability raw_file (ct_12_avl --- current_time + 12_hour avl) is \"\n", "pb.send_slack_message(\n", "    channel=channel,\n", "    text=text_req,\n", "    files=[\"raw.csv\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "de64ffbb-f711-4736-9329-250c1a6d85d3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}