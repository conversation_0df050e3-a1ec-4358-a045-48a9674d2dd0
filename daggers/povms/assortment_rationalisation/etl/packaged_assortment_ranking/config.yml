alert_configs:
  slack:
  - channel: assortment-alerts-plus-discussions
dag_name: packaged_assortment_ranking
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U06RTRAMJQ4
path: povms/assortment_rationalisation/etl/packaged_assortment_ranking
paused: false
pool: povms_pool
project_name: assortment_rationalisation
schedule:
  end_date: '2025-08-18T00:00:00'
  interval: 30 19 * * *
  start_date: '2025-02-19T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 7
