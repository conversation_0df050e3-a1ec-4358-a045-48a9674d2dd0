{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fetchDataFromDB(query):\n", "    import pencilbox as pb\n", "    import pandas as pd\n", "    import time\n", "\n", "    con = pb.get_connection(\"[Warehouse] Trino\")\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            df = pd.read_sql_query(sql=query, con=con)\n", "            return df\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error fetching data (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to fetch data after {max_retries} attempts: {e}\")\n", "                return pd.DataFrame()\n", "\n", "\n", "def getValidOutletsInCity(city_name, lookback):\n", "    # https://redash-queries.grofer.io/queries/337349/source\n", "    query = f\"\"\"\n", "  WITH fo AS\n", "      (SELECT om.facility_id,\n", "              om.outlet_id AS outlet_id,\n", "              mo_map.frontend_merchant_id AS merchant_id,\n", "              om.outlet_name AS outlet_name,\n", "              rcl.id AS city_id,\n", "              rcl.name AS city_name,\n", "              rcs.name AS state\n", "      FROM po.physical_facility_outlet_mapping om\n", "      INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "      AND rco.business_type_id IN (7)\n", "      AND rco.company_type_id NOT IN (771, 767)\n", "      AND rco.lake_active_record\n", "      INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "      AND rcl.lake_active_record\n", "      INNER JOIN dwh.dim_merchant_outlet_facility_mapping mo_map ON mo_map.facility_id = om.facility_id\n", "      AND rco.business_type_id = 7\n", "      AND is_current\n", "      AND is_current_mapping_active\n", "      AND is_backend_merchant_active\n", "      AND is_frontend_merchant_active\n", "      INNER JOIN retail.console_state AS rcs ON rcs.id = rcl.state_id\n", "      AND rcs.lake_active_record\n", "      INNER JOIN\n", "        (SELECT distinct cast(merchant_id AS int) merchant_id\n", "          FROM serviceability.ser_store_polygons\n", "          WHERE TYPE in ('STORE_POLYGON','LONGTAIL_POLYGON')\n", "            AND is_active = TRUE\n", "            AND lake_active_record) poly ON poly.merchant_id = mo_map.frontend_merchant_id\n", "      WHERE om.active = 1\n", "        AND om.ars_active = 1\n", "        AND om.lake_active_record\n", "        AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "        AND om.outlet_name NOT LIKE '%%Draft%%'\n", "        AND om.outlet_name NOT LIKE '%%Test Store%%'\n", "        AND om.outlet_name NOT LIKE '%%Dummy%%'\n", "        AND om.facility_id != 273\n", "        AND om.outlet_id IN\n", "          (SELECT DISTINCT outlet_id\n", "            FROM po.bulk_facility_outlet_mapping\n", "            WHERE active\n", "              AND lake_active_record) AND rcl.name = '{city_name}' )\n", "SELECT *\n", "FROM\n", "  (SELECT fo.outlet_id,\n", "          fo.facility_id,\n", "          fo.city_id,\n", "          fo.merchant_id,\n", "          count(DISTINCT (order_create_dt_ist)) AS facility_days,\n", "          sum(product_quantity) AS qty_shipped\n", "   FROM dwh.fact_sales_order_item_details ord\n", "   INNER JOIN fo ON fo.merchant_id = ord.frontend_merchant_id\n", "   WHERE order_current_status = 'DELIVERED'\n", "     AND is_internal_order = FALSE\n", "     AND order_create_dt_ist >= CURRENT_DATE - interval '{lookback}' DAY\n", "     and order_create_dt_ist < CURRENT_DATE\n", "     AND frontend_merchant_id IN\n", "       (SELECT merchant_id\n", "        FROM fo)\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4) X\n", "WHERE qty_shipped > ({lookback} * 300 * 5)\n", "and facility_days > 7 -- outbound live at store for atleast 7 days\n", "-- lookback days * 300 orders a day * 5 items per order\n", "   \"\"\"\n", "    print(\"getValidOutletsInCity \", query)\n", "    df = fetchDataFromDB(query)\n", "    return df\n", "\n", "\n", "def getAssortmentUniverse(outlets_df):\n", "    city_id = \",\".join(map(str, outlets_df[\"city_id\"].unique()))\n", "    facility_ids = \",\".join(map(str, outlets_df[\"facility_id\"].unique()))\n", "    query = f\"\"\"\n", "   WITH fo AS\n", "  (SELECT om.facility_id,\n", "          om.outlet_id AS outlet_id,\n", "          mo_map.frontend_merchant_id AS merchant_id,\n", "          om.outlet_name AS outlet_name,\n", "          rcl.name AS city_name,\n", "          rcs.name AS state\n", "   FROM po.physical_facility_outlet_mapping om\n", "   INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "   AND rco.business_type_id IN (7)\n", "   AND rco.company_type_id NOT IN (771)\n", "   AND rco.lake_active_record\n", "   INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "   AND rcl.lake_active_record\n", "   INNER JOIN dwh.dim_merchant_outlet_facility_mapping mo_map ON mo_map.facility_id = om.facility_id\n", "   AND rco.business_type_id = 7\n", "   AND is_current\n", "   AND is_current_mapping_active\n", "   AND is_backend_merchant_active\n", "   AND is_frontend_merchant_active\n", "   INNER JOIN retail.console_state AS rcs ON rcs.id = rcl.state_id\n", "   AND rcs.lake_active_record\n", "   WHERE om.active = 1\n", "     AND om.ars_active = 1\n", "     AND om.lake_active_record\n", "     AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "     AND om.facility_id != 273 )\n", "     \n", "SELECT pfma.item_id,\n", "       pfma.assortment_type,\n", "       iotm.tag_value as be_id,\n", "       count(distinct(pfma.facility_id)) AS count_stores,\n", "       coalesce(sum(inv.quantity),0) AS fe_inv_quantity,\n", "       avg(DATE_DIFF('day',date(pfma.created_at), current_date)) as assortment_age,\n", "       avg(DATE_DIFF('day',date(pfma.updated_at), current_date)) as assortment_update_age\n", "FROM rpc.product_facility_master_assortment pfma\n", "INNER JOIN fo ON fo.facility_id = pfma.facility_id\n", "INNER JOIN rpc.item_category_details cat ON cat.item_id = pfma.item_id\n", "AND cat.lake_active_record\n", "INNER JOIN rpc.item_outlet_tag_mapping iotm ON iotm.tag_type_id = 8\n", "AND iotm.active = 1\n", "AND iotm.lake_active_record\n", "AND iotm.outlet_id = fo.outlet_id\n", "AND iotm.item_id = pfma.item_id\n", "LEFT JOIN ims.ims_item_inventory inv ON inv.outlet_id = fo.outlet_id\n", "AND inv.item_id = pfma.item_id\n", "AND inv.active = 1\n", "AND inv.lake_active_record\n", "WHERE pfma.lake_active_record\n", "  AND pfma.master_assortment_substate_id IN (1,\n", "                                             3)\n", "  AND pfma.active = 1\n", "  AND pfma.facility_id IN ({facility_ids})\n", "GROUP BY 1,\n", "         2,\n", "         3\n", "   \"\"\"\n", "    print(\"getItemLevelSalesInCity \", query)\n", "    df = fetchDataFromDB(query)\n", "    return df\n", "\n", "\n", "def getBeInvCountForItems(universe_df):\n", "    be_ids = \",\".join(map(str, universe_df[\"be_id\"].unique()))\n", "    # https://redash-queries.grofer.io/queries/337533/source\n", "    query = f\"\"\"\n", "   WITH bo AS\n", "  (SELECT om.facility_id,\n", "          om.outlet_id AS outlet_id,\n", "          om.outlet_name AS outlet_name,\n", "          rcl.name AS city_name,\n", "          rcs.name AS state,\n", "          wom.cloud_store_id AS inv_outlet_id\n", "   FROM po.physical_facility_outlet_mapping om\n", "   INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "   AND rco.business_type_id != 7\n", "   AND rco.lake_active_record\n", "   INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "   AND rcl.lake_active_record\n", "   INNER JOIN retail.console_state AS rcs ON rcs.id = rcl.state_id\n", "   AND rcs.lake_active_record\n", "   INNER JOIN\n", "     (SELECT DISTINCT warehouse_id,\n", "                      cloud_store_id\n", "      FROM retail.warehouse_outlet_mapping\n", "      WHERE active = 1\n", "        AND lake_active_record) wom ON wom.warehouse_id = om.outlet_id\n", "   WHERE om.active = 1\n", "     AND om.ars_active = 1\n", "     AND om.lake_active_record\n", "     AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "     AND om.facility_id != 273 )\n", "     \n", "SELECT inv.item_id,\n", "       bo.outlet_id,\n", "       sum(quantity) as quantity\n", "FROM ims.ims_item_inventory inv\n", "LEFT JOIN bo ON bo.inv_outlet_id = inv.outlet_id\n", "WHERE inv.active = 1\n", "  AND inv.lake_active_record\n", "  AND bo.outlet_id IN ({be_ids})\n", "  AND quantity > 0\n", "  GROUP BY 1,2\n", "   \"\"\"\n", "    print(\"getBeInvCountForItems \", query)\n", "    df = fetchDataFromDB(query)\n", "    return df\n", "\n", "\n", "def getItemSalesInStores(outlets_df, lookback):\n", "    # https://reports.grofer.io/queries/186802/source?p_lookback=1&p_merchant_ids=30375\n", "    merchant_ids = \",\".join(map(str, outlets_df[\"merchant_id\"].unique()))\n", "    query = f\"\"\"     \n", "  SELECT ipo.item_id,\n", "        sum(ord.product_quantity * ipo.multiplier) AS quantity,\n", "        sum(total_retained_margin) as total_retained_margin,\n", "        sum(total_selling_price) as total_selling_price\n", "  FROM dwh.fact_sales_order_item_details AS ord\n", " INNER JOIN dwh.dim_item_product_offer_mapping ipo ON ipo.product_id = ord.product_id\n", "  AND is_current\n", "  WHERE order_current_status = 'DELIVERED'\n", "    AND is_internal_order = FALSE\n", "    AND order_create_dt_ist >= CURRENT_DATE - interval '{lookback}' DAY\n", "    AND order_create_dt_ist < CURRENT_DATE\n", "    and ord.frontend_merchant_id in ({merchant_ids})\n", "    group by 1\n", "  \"\"\"\n", "    print(\"getItemSalesInCity \", query)\n", "    df = fetchDataFromDB(query)\n", "    return df\n", "\n", "\n", "def getItemAvailabilityInStores(outlets_df, lookback):\n", "    facility_ids = \",\".join(map(str, outlets_df[\"facility_id\"].unique()))\n", "    query = f\"\"\"\n", "SELECT item_id,\n", "       facility_id,\n", "       sum(availability_factor) AS availability_factor,\n", "       count(distinct(facility_id)) AS available_store_count,\n", "       sum(order_quantity) AS order_quantity,\n", "       sum(potential_order_quantity) AS potential_order_quantity\n", "FROM ars.daily_orders_and_availability\n", "WHERE facility_id IN ({facility_ids})\n", "  AND insert_ds_ist >= cast(CURRENT_DATE - interval '{lookback}' DAY AS varchar)\n", "  AND insert_ds_ist < cast(CURRENT_DATE AS varchar)\n", "  AND lake_active_record\n", "GROUP BY 1,\n", "         2\n", "  \"\"\"\n", "    print(\"getItemAvailabilityInStores \", query)\n", "    df = fetchDataFromDB(query)\n", "    return df\n", "\n", "\n", "def fetchPackagedItemUniverse():\n", "    query = \"\"\"\n", "  SELECT *\n", "  FROM\n", "    (SELECT icd.item_id,\n", "            icd.name,\n", "            icd.l0,\n", "            icd.l1,\n", "            icd.l2,\n", "            icd.product_type,\n", "            CASE\n", "                WHEN id.perishable = 1 THEN 'PERISHABLE'\n", "                ELSE 'PACKAGED'\n", "            END AS item_type,\n", "            id.storage_type AS storage_type_raw,\n", "            CASE\n", "                WHEN id.storage_type IN ('1',\n", "                                        '8',\n", "                                        '11') THEN 'REGULAR'\n", "                WHEN id.storage_type IN ('4',\n", "                                        '5') THEN 'HEAVY'\n", "                WHEN id.storage_type IN ('2',\n", "                                        '6') THEN 'COLD'\n", "                WHEN id.storage_type IN ('3',\n", "                                        '7') THEN 'FROZEN'\n", "                ELSE 'REGULAR'\n", "            END AS storage_type,\n", "            CASE\n", "                WHEN id.handling_type IN ('8') THEN 'PACKAGING MATERIAL'\n", "                WHEN id.handling_type IN ('6') THEN 'MEDICINAL'\n", "                ELSE 'REGULAR'\n", "            END AS handling_type,\n", "            id.variant_mrp AS mrp,\n", "            id.variant_description,\n", "            id.weight_in_gm,\n", "            id.length_in_cm,\n", "            id.height_in_cm,\n", "            id.breadth_in_cm,\n", "            id.shelf_life,\n", "            coalesce(itf.item_factor, 0.01) AS item_factor,\n", "            DATE_DIFF('day',date(icd.created_at), CURRENT_DATE) AS item_catalog_age,\n", "            rank() OVER (PARTITION BY id.item_id\n", "                        ORDER BY id.id DESC) AS variant_rank,\n", "                        itm.tag_value AS custom_storage_type_raw,\n", "                        CASE\n", "                            WHEN itm.tag_value = '1' THEN 'BEAUTY'\n", "                            WHEN itm.tag_value = '2' THEN 'BOUQUET'\n", "                            WHEN itm.tag_value = '3' THEN 'PREMIUM'\n", "                            WHEN itm.tag_value = '4' THEN 'BOOKS'\n", "                            WHEN itm.tag_value = '5' THEN 'NON_VEG'\n", "                            WHEN itm.tag_value = '6' THEN 'ICE_CREAM'\n", "                            WHEN itm.tag_value = '7' THEN 'TOYS'\n", "                            WHEN itm.tag_value = '8' THEN 'ELECTRONICS' -- when itm.tag_value = '9' then 'FESTIVE'\n", "\n", "                            WHEN itm.tag_value = '10' THEN 'VERTICAL_CHUTES'\n", "                            WHEN itm.tag_value = '11' THEN 'BEST_SERVED_COLD'\n", "                            WHEN itm.tag_value = '12' THEN 'CRITICAL_SKUS'\n", "                            WHEN itm.tag_value = '13' THEN 'LARGE'\n", "                            WHEN itm.tag_value = '14' THEN 'APPAREL'\n", "                            WHEN itm.tag_value = '15' THEN 'SPORTS'\n", "                            WHEN itm.tag_value = '16' THEN 'PET_CARE'\n", "                            WHEN itm.tag_value = '17' THEN 'HOME_DECOR'\n", "                            WHEN itm.tag_value = '18' THEN 'KITCHEN_DINING'\n", "                            WHEN itm.tag_value = '19' THEN 'HOME_FURNISHING'\n", "                            WHEN itm.tag_value = '20' THEN 'LONGTAIL_OTHERS'\n", "                            WHEN itm.tag_value IS NULL THEN 'NO_CONFIG'\n", "                            ELSE 'UNKNOWN_CONFIG'\n", "                        END AS custom_storage_type,\n", "                        pb.manufacturer_id,\n", "                        id.manufacturer AS manufacturer_name,\n", "                        pb.name AS brand_name,\n", "                      coalesce(id.outer_case_size,1) as outer_case_size,\n", "                      coalesce(id.inner_case_size,1) as inner_case_size,\n", "                        case when itm2.tag_value = '1' then TRUE else FALSE end as is_high_value,\n", "                        itm2.tag_value as high_value_tag_raw\n", "    FROM rpc.item_category_details icd\n", "    INNER JOIN rpc.product_product id ON id.item_id = icd.item_id\n", "    AND id.active = 1\n", "    AND id.approved = 1\n", "    AND id.lake_active_record\n", "    LEFT JOIN supply_etls.item_factor itf ON itf.item_id = icd.item_id\n", "    LEFT JOIN rpc.item_tag_mapping itm ON itm.tag_type_id = 7\n", "    AND itm.active = TRUE\n", "    AND itm.lake_active_record\n", "    AND itm.item_id = icd.item_id\n", "    LEFT JOIN rpc.product_brand pb ON id.brand_id = pb.id\n", "    AND pb.lake_active_record\n", "    AND pb.active = 1\n", "    LEFT JOIN rpc.item_tag_mapping itm2 on itm2.item_id = icd.item_id\n", "      AND itm2.active \n", "      AND itm2.tag_type_id = 3\n", "      AND itm2.lake_active_record\n", "    WHERE icd.lake_active_record\n", "      AND perishable != 1\n", "      AND id.handling_type != '8'\n", "      AND id.storage_type NOT IN ('3',\n", "                                  '7')\n", "      AND icd.l0_id != 1487 -- removing vegetables and fruits\n", "\n", "      AND icd.l0 NOT IN ('wholesale store',\n", "                          'Trial new tree',\n", "                          'Specials')-- removing test and flyer/freebie l0s\n", "  ) AS x\n", "  WHERE variant_rank = 1\n", "\"\"\"\n", "    print(query)\n", "    return fetchDataFromDB(query)\n", "\n", "\n", "def fetchFrontEndOutletDetails():\n", "    # https://redash-queries.grofer.io/queries/332215\n", "    query = \"\"\"\n", "        WITH fo AS\n", "      (SELECT om.facility_id,\n", "              om.outlet_id AS outlet_id,\n", "              mo_map.frontend_merchant_id AS merchant_id,\n", "              om.outlet_name AS outlet_name,\n", "              rcl.name AS city_name,\n", "              rcs.name AS state\n", "      FROM po.physical_facility_outlet_mapping om\n", "      INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "      AND rco.business_type_id IN (7)\n", "      AND rco.company_type_id NOT IN (771, 767)\n", "      AND rco.lake_active_record\n", "      INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "      AND rcl.lake_active_record\n", "      INNER JOIN dwh.dim_merchant_outlet_facility_mapping mo_map ON mo_map.facility_id = om.facility_id\n", "      AND rco.business_type_id = 7\n", "      AND is_current\n", "      AND is_current_mapping_active\n", "      AND is_backend_merchant_active\n", "      AND is_frontend_merchant_active\n", "      INNER JOIN retail.console_state AS rcs ON rcs.id = rcl.state_id\n", "      AND rcs.lake_active_record\n", "      INNER JOIN\n", "        (SELECT distinct cast(merchant_id AS int) as merchant_id\n", "          FROM serviceability.ser_store_polygons\n", "          WHERE TYPE in ('STORE_POLYGON','LONGTAIL_POLYGON')\n", "            AND is_active = TRUE\n", "            AND lake_active_record) poly ON poly.merchant_id = mo_map.frontend_merchant_id\n", "      WHERE om.active = 1\n", "        AND om.ars_active = 1\n", "        AND om.lake_active_record\n", "        AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "        AND om.outlet_name NOT LIKE '%%Draft%%'\n", "        AND om.outlet_name NOT LIKE '%%Test Store%%'\n", "        AND om.outlet_name NOT LIKE '%%Dummy%%'\n", "        AND om.facility_id != 273\n", "        AND om.outlet_id IN\n", "          (SELECT DISTINCT outlet_id\n", "            FROM po.bulk_facility_outlet_mapping\n", "            WHERE active\n", "              AND lake_active_record) )\n", "    SELECT *\n", "    FROM fo\n", "    \"\"\"\n", "    # 273 is a test dark store mapped to multiple merchant ids leading to duplicate rows\n", "    print(query)\n", "    return fetchDataFromDB(query)\n", "\n", "\n", "def pushD<PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "    df,\n", "    tableName,\n", "    description=None,\n", "    primaryKeys=[\"insert_ds_ist\"],\n", "    load_type=\"upsert\",\n", "    environ=\"playground\",\n", "    partition_key=[],\n", "):\n", "    from datetime import datetime, timezone, timedelta\n", "    import time\n", "    import pandas as pd\n", "\n", "    utc_now = datetime.now(timezone.utc)\n", "    ist_now = utc_now + <PERSON><PERSON><PERSON>(hours=5, minutes=30)\n", "    formatted_date = ist_now.strftime(\"%Y-%m-%d\")\n", "    df[\"insert_ds_ist\"] = formatted_date\n", "    df[\"updated_at\"] = pd.to_datetime(utc_now)\n", "    column_dtypes = [\n", "        {\n", "            \"name\": col,\n", "            \"type\": str(df[col].dtype)\n", "            .replace(\"int64\", \"integer\")\n", "            .replace(\"float64\", \"real\")\n", "            .replace(\"str\", \"varchar\")\n", "            .replace(\"datetime64[ns]\", \"timestamp(6)\")\n", "            .replace(\"object\", \"varchar\")\n", "            .replace(\"bool\", \"boolean\")\n", "            .replace(\"datetime64[ns, UTC]\", \"timestamp(6)\"),\n", "            \"description\": col,\n", "        }\n", "        for col in df.columns\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": environ,\n", "        \"table_name\": tableN<PERSON>,\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": primary<PERSON><PERSON><PERSON>,\n", "        \"load_type\": load_type,\n", "        \"table_description\": (description if description else tableName),\n", "    }\n", "\n", "    if len(partition_key) > 0:\n", "        kwargs[\"partition_key\"] = partition_key\n", "\n", "    import pencilbox as pb\n", "\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            pb.to_trino(data_obj=df, **kwargs)\n", "            return\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error pushDfToTrino (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to pushDfToTrino after {max_retries} attempts: {e}\")\n", "                return\n", "\n", "\n", "def split_array(arr, n=7):\n", "    \"\"\"\n", "    Split an array into n parts.\n", "\n", "    Parameters:\n", "    arr (list): The input array.\n", "    n (int): The number of parts to split the array into. Defaults to 7.\n", "\n", "    Returns:\n", "    list: A list of n sub-arrays.\n", "    \"\"\"\n", "    # Calculate the size of each part\n", "    part_size = len(arr) // n\n", "\n", "    # Calculate the remainder\n", "    remainder = len(arr) % n\n", "\n", "    # Initialize the result list\n", "    result = []\n", "\n", "    # Initialize the start index\n", "    start = 0\n", "\n", "    # Loop through each part\n", "    for i in range(n):\n", "        # Calculate the end index for this part\n", "        end = start + part_size + (1 if i < remainder else 0)\n", "\n", "        # Append this part to the result list\n", "        result.append(arr[start:end])\n", "\n", "        # Update the start index for the next part\n", "        start = end\n", "\n", "    return result\n", "\n", "\n", "def get_day_of_week():\n", "    import datetime\n", "\n", "    today = datetime.datetime.today()\n", "    return today.weekday()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "USE_LOCAL_CSV_DATA = False\n", "TRINO_ENV = \"supply_etls\"\n", "\n", "if USE_LOCAL_CSV_DATA:\n", "    outlets_raw = pd.read_csv(\"all_outlets.csv\")\n", "    catalog_df = pd.read_csv(\"catalog_df.csv\")\n", "else:\n", "    outlets_raw = fetchFrontEndOutletDetails()\n", "    outlets_raw.to_csv(\"all_outlets.csv\", index=False)\n", "    catalog_df = fetchPackagedItemUniverse()\n", "    catalog_df.to_csv(\"catalog_df.csv\", index=False)\n", "\n", "cities = outlets_raw[\"city_name\"].unique()\n", "\n", "lookback = 60\n", "high_value_min_abs_margin = 100\n", "high_value_min_mrp = 400\n", "new_assortment_age_threshold = lookback\n", "availability_factor_threshold = 0.66\n", "high_throughput_threshold = 0.90\n", "ptype_head_threshold = high_throughput_threshold\n", "ptype_cutoff = 0.99\n", "\n", "\n", "def createAssortmentRanking(city):\n", "    if not USE_LOCAL_CSV_DATA:\n", "        outlets_df = getValidOutletsInCity(city, lookback)\n", "        if outlets_df.shape[0] == 0:\n", "            return None\n", "        outlets_df.to_csv(city + \"_outlets.csv\", index=False)\n", "        universe_df = getAssortmentUniverse(outlets_df)\n", "        universe_df.to_csv(city + \"_universe.csv\", index=False)\n", "        universe_df = universe_df[(universe_df[\"be_id\"] != \"0\") & (universe_df[\"be_id\"] != 0)]\n", "        be_df = getBeInvCountForItems(universe_df)\n", "        be_df.to_csv(city + \"_be_df.csv\", index=False)\n", "        sales_df = getItemSalesInStores(outlets_df, lookback)\n", "        sales_df.to_csv(city + \"_itemSales.csv\", index=False)\n", "        item_availability_df = getItemAvailabilityInStores(outlets_df, lookback)\n", "        item_availability_df.to_csv(city + \"_item_availability_df.csv\", index=False)\n", "    else:\n", "        outlets_df = pd.read_csv(city + \"_outlets.csv\")\n", "        universe_df = pd.read_csv(city + \"_universe.csv\")\n", "        universe_df = universe_df[(universe_df[\"be_id\"] != \"0\") & (universe_df[\"be_id\"] != 0)]\n", "        be_df = pd.read_csv(city + \"_be_df.csv\")\n", "        sales_df = pd.read_csv(city + \"_itemSales.csv\")\n", "        item_availability_df = pd.read_csv(city + \"_item_availability_df.csv\")\n", "\n", "    item_availability_df[\"availability_factor\"] = pd.to_numeric(\n", "        item_availability_df[\"availability_factor\"], errors=\"coerce\"\n", "    )\n", "    item_availability_df[\"available_store_count\"] = pd.to_numeric(\n", "        item_availability_df[\"available_store_count\"], errors=\"coerce\"\n", "    )\n", "    item_availability_df[\"order_quantity\"] = pd.to_numeric(\n", "        item_availability_df[\"order_quantity\"], errors=\"coerce\"\n", "    )\n", "    item_availability_df[\"potential_order_quantity\"] = pd.to_numeric(\n", "        item_availability_df[\"potential_order_quantity\"], errors=\"coerce\"\n", "    )\n", "\n", "    pivoted_df = universe_df.pivot_table(\n", "        index=[\"item_id\"],\n", "        columns=\"assortment_type\",\n", "        values=[\"count_stores\"],\n", "        aggfunc={\"count_stores\": \"sum\"},\n", "    )\n", "    pivoted_df = pivoted_df.fillna(0)\n", "    pivoted_df = pivoted_df.reset_index()\n", "    lt_only_items = pivoted_df[(pivoted_df[\"count_stores\", \"EXPRESS\"] == 0)].reset_index()\n", "    # removing lt only items\n", "    if lt_only_items.shape[0] > 0:\n", "        universe_df = universe_df[~universe_df[\"item_id\"].isin(lt_only_items[\"item_id\"])]\n", "        pivoted_df = pivoted_df[~pivoted_df[\"item_id\"].isin(lt_only_items[\"item_id\"])]\n", "\n", "    if (\"count_stores\", \"LONGTAIL\") in pivoted_df.columns:\n", "        # removing all items which are LT in even one store.\n", "        lt_items = pivoted_df[(pivoted_df[\"count_stores\", \"LONGTAIL\"] != 0)].reset_index()\n", "    else:\n", "        # handle the case where the column is not present\n", "        print(\"Column 'LONGTAIL' is not present in the DataFrame, so no filtering needed\")\n", "        lt_items = pd.DataFrame()\n", "\n", "    if lt_items.shape[0] > 0:\n", "        universe_df = universe_df[~universe_df[\"item_id\"].isin(lt_items[\"item_id\"])]\n", "\n", "    data_df = (\n", "        universe_df.groupby([\"item_id\", \"be_id\"])\n", "        .agg(\n", "            {\n", "                \"count_stores\": \"sum\",\n", "                \"fe_inv_quantity\": \"sum\",\n", "                \"assortment_age\": \"mean\",\n", "                \"assortment_update_age\": \"mean\",\n", "            }\n", "        )\n", "        .reset_index()\n", "    )\n", "    be_df = be_df.rename(columns={\"outlet_id\": \"be_id\", \"quantity\": \"be_inv_quantity\"})\n", "    data_df[\"be_id\"] = pd.to_numeric(data_df[\"be_id\"], errors=\"coerce\")\n", "    data_df = data_df[~(pd.isna(data_df[\"be_id\"]) | data_df[\"be_id\"] == 0)]\n", "    data_df = pd.merge(data_df, be_df, on=[\"item_id\", \"be_id\"], how=\"left\")\n", "\n", "    data_df = (\n", "        data_df.groupby([\"item_id\"])\n", "        .agg(\n", "            {\n", "                \"count_stores\": \"sum\",\n", "                \"fe_inv_quantity\": \"sum\",\n", "                \"assortment_age\": \"mean\",\n", "                \"assortment_update_age\": \"mean\",\n", "                \"be_inv_quantity\": \"sum\",\n", "            }\n", "        )\n", "        .reset_index()\n", "    )\n", "\n", "    sales_df = sales_df.rename(columns={\"quantity\": \"sales_quantity\"})\n", "    sales_df[\"margin_percent\"] = sales_df.apply(\n", "        lambda row: row[\"total_retained_margin\"] / row[\"total_selling_price\"]\n", "        if row[\"total_selling_price\"] != 0\n", "        else 0,\n", "        axis=1,\n", "    )\n", "\n", "    data_df = pd.merge(data_df, sales_df, on=[\"item_id\"], how=\"left\")\n", "    item_availability_df = (\n", "        item_availability_df.groupby(\"item_id\")\n", "        .agg(\n", "            availability_factor=(\"availability_factor\", \"sum\"),\n", "            available_store_count=(\"facility_id\", \"nunique\"),\n", "            order_quantity=(\"order_quantity\", \"sum\"),\n", "            potential_order_quantity=(\"potential_order_quantity\", \"sum\"),\n", "        )\n", "        .reset_index()\n", "    )\n", "    data_df = pd.merge(data_df, item_availability_df, on=[\"item_id\"], how=\"left\")\n", "    data_df = pd.merge(data_df, catalog_df, on=[\"item_id\"], how=\"inner\")\n", "    data_df[\"sales_quantity\"] = data_df.apply(\n", "        lambda row: max(row[\"order_quantity\"], row[\"sales_quantity\"]), axis=1\n", "    )\n", "    data_df[\"city_facility_days\"] = outlets_df[\"facility_days\"].sum()\n", "    data_df[\"city_quantity_shipped\"] = data_df.sales_quantity.sum()\n", "    data_df[\"city_store_count\"] = len(outlets_df[\"facility_id\"].unique())\n", "    data_df.fillna(0, inplace=True)\n", "\n", "    def safe_weighted_calculation(group, value_col, weight_col=\"sales_quantity\"):\n", "        total_weight = group[weight_col].sum()\n", "        if total_weight == 0:\n", "            return 0\n", "        return (group[weight_col] * group[value_col]).sum() / total_weight\n", "\n", "    ptype_weighted_avg_margin = (\n", "        data_df[data_df[\"margin_percent\"] != 0]\n", "        .groupby([\"l0\", \"l1\", \"l2\", \"product_type\"])\n", "        .apply(lambda x: safe_weighted_calculation(x, \"margin_percent\", \"sales_quantity\"))\n", "        .reset_index(name=\"weighted_avg_margin\")\n", "    )\n", "\n", "    data_df = pd.merge(\n", "        data_df, ptype_weighted_avg_margin, on=[\"l0\", \"l1\", \"l2\", \"product_type\"], how=\"left\"\n", "    )\n", "    data_df[\"margin_percent\"] = data_df.apply(\n", "        lambda x: x[\"weighted_avg_margin\"]\n", "        if x[\"margin_percent\"] == 0 and pd.notna(x[\"weighted_avg_margin\"])\n", "        else x[\"margin_percent\"],\n", "        axis=1,\n", "    )\n", "    data_df.drop(\"weighted_avg_margin\", axis=1, inplace=True)\n", "\n", "    data_df[\"unit_price\"] = data_df.apply(\n", "        lambda x: x[\"total_selling_price\"] / x[\"sales_quantity\"]\n", "        if x[\"sales_quantity\"] > 0\n", "        else x[\"mrp\"],\n", "        axis=1,\n", "    )\n", "    data_df[\"high_margin_flag\"] = (\n", "        (data_df[\"margin_percent\"] * data_df[\"unit_price\"]) > high_value_min_abs_margin\n", "    ) | (data_df[\"unit_price\"] > high_value_min_mrp)\n", "\n", "    # all zero items\n", "    zero_items_df = data_df[\n", "        (data_df[\"fe_inv_quantity\"] == 0)\n", "        & (data_df[\"be_inv_quantity\"] == 0)\n", "        & (data_df[\"sales_quantity\"] == 0)\n", "        & (data_df[\"availability_factor\"] == 0)\n", "    ]\n", "    data_df = data_df[~data_df[\"item_id\"].isin(zero_items_df[\"item_id\"])]\n", "\n", "    data_df[\"new_assortment_flag\"] = data_df[\"assortment_age\"] < new_assortment_age_threshold\n", "\n", "    def identify_experimental_products(row):\n", "        if row[\"city_store_count\"] < 5:\n", "            return False\n", "        elif row[\"count_stores\"] < max(0.5 * row[\"city_store_count\"], 3):\n", "            return True\n", "        else:\n", "            return False\n", "\n", "    data_df[\"experimental_limited_coverage_flag\"] = data_df.apply(\n", "        identify_experimental_products, axis=1\n", "    )\n", "\n", "    data_df[\"available_store_count\"] = data_df.apply(\n", "        lambda x: x[\"available_store_count\"]\n", "        if x[\"available_store_count\"] <= x[\"city_store_count\"]\n", "        else x[\"city_store_count\"],\n", "        axis=1,\n", "    )\n", "    data_df[\"availability_factor_mod\"] = data_df.apply(\n", "        lambda x: x[\"availability_factor\"] / x[\"available_store_count\"] * x[\"city_store_count\"]\n", "        if x[\"available_store_count\"] > 0\n", "        else 0,\n", "        axis=1,\n", "    )\n", "    data_df[\"low_availability_flag\"] = (\n", "        data_df[\"availability_factor_mod\"] < availability_factor_threshold\n", "    )\n", "\n", "    # scaled cart pen incorporating store foot sprint and time available during the analysis window\n", "    # availability_factor_mod is scaling done on store footprint dimension\n", "    # (data_df['city_facility_days'] / data_df['availability_factor_mod']) is the scaling factor on the time dimension\n", "\n", "    data_df[\"cartPen\"] = data_df.apply(\n", "        lambda x: (x[\"sales_quantity\"] / x[\"city_quantity_shipped\"])\n", "        * (x[\"city_facility_days\"] / x[\"availability_factor_mod\"])\n", "        if x[\"availability_factor_mod\"] > 0\n", "        else 0,\n", "        axis=1,\n", "    )\n", "\n", "    # setting cartPen values of low availability and experimental items as 0, so that they don't figure out in the high throughput list\n", "    data_df[\"cartPen_mod\"] = data_df.apply(\n", "        lambda row: row[\"cartPen\"]\n", "        if not (\n", "            row[\"low_availability_flag\"]\n", "            and row[\"experimental_limited_coverage_flag\"]\n", "            and row[\"new_assortment_flag\"]\n", "        )\n", "        else 0,\n", "        axis=1,\n", "    )\n", "    data_df.sort_values(by=\"cartPen_mod\", ascending=False, inplace=True)\n", "    data_df[\"ptype_first_item_flag\"] = (\n", "        data_df.groupby([\"l0\", \"l1\", \"l2\", \"product_type\"]).cumcount() == 0\n", "    )\n", "    data_df.reset_index(drop=True, inplace=True)\n", "\n", "    data_df[\"cumsum_cartPen\"] = data_df[\"cartPen_mod\"].cumsum()\n", "    total_cartPen = data_df[\"cartPen_mod\"].sum()\n", "    data_df[\"cum_cartPen_percent\"] = data_df[\"cumsum_cartPen\"] / total_cartPen\n", "\n", "    data_df[\"high_throughput_flag\"] = data_df[\"cum_cartPen_percent\"] < high_throughput_threshold\n", "    data_df[\"immunity_flag\"] = (\n", "        data_df[\"high_throughput_flag\"]\n", "        | data_df[\"low_availability_flag\"]\n", "        | data_df[\"new_assortment_flag\"]\n", "    )\n", "\n", "    # ptype ros ranking\n", "    grouped = data_df.groupby([\"l0\", \"l1\", \"l2\", \"product_type\"])\n", "    ptype_df = pd.DataFrame(\n", "        {\n", "            \"total_cartPen\": grouped[\"cartPen_mod\"].sum(),\n", "            \"weighted_margin\": grouped.apply(safe_weighted_calculation, value_col=\"margin_percent\"),\n", "            \"weighted_item_factor\": grouped.apply(\n", "                safe_weighted_calculation, value_col=\"item_factor\"\n", "            ),\n", "        }\n", "    ).reset_index()\n", "    # Calculate ROI, handling potential division by zero\n", "    ptype_df[\"ros\"] = np.where(\n", "        ptype_df[\"weighted_item_factor\"] != 0,\n", "        ptype_df[\"total_cartPen\"] * ptype_df[\"weighted_margin\"] / ptype_df[\"weighted_item_factor\"],\n", "        0,\n", "    )\n", "    ptype_df.sort_values(by=\"ros\", inplace=True, ascending=False)\n", "    ptype_df.reset_index(inplace=True, drop=True)\n", "    ptype_df[\"ros\"] = ptype_df[\"ros\"].round(6)\n", "    ptype_df[\"ptype_rank\"] = ptype_df[\"ros\"].rank(\n", "        method=\"dense\", ascending=False\n", "    )  # return on space - higher the value, smaller the rank, thus ascending = false\n", "    ptype_df.sort_values(by=\"total_cartPen\", inplace=True, ascending=False)\n", "    ptype_df[\"total_cartPen_rescaled\"] = ptype_df.total_cartPen / ptype_df.total_cartPen.sum()\n", "    ptype_df[\"cartPen_cumsum\"] = ptype_df[\"total_cartPen_rescaled\"].cumsum()\n", "    ptype_df[\"ptype_rank\"] = ptype_df.apply(\n", "        lambda x: x[\"ptype_rank\"] if x[\"cartPen_cumsum\"] < ptype_cutoff else np.nan, axis=1\n", "    )\n", "    ptype_df_filt = ptype_df[[\"l0\", \"l1\", \"l2\", \"product_type\", \"ptype_rank\"]]\n", "\n", "    data_df = pd.merge(data_df, ptype_df_filt, on=[\"l0\", \"l1\", \"l2\", \"product_type\"], how=\"left\")\n", "    data_df = data_df.sort_values(by=\"ptype_rank\")\n", "\n", "    def assign_ranks(group):\n", "        if group[\"cartPen_mod\"].sum() == 0:\n", "            return group.assign(item_rank_2=np.nan)\n", "        else:\n", "            # Sort the group by cart<PERSON>en in descending order\n", "            group = group.sort_values(\"cartPen_mod\", ascending=False)\n", "            # Calculate cumulative sum and percentage of cartPen\n", "            group[\"ptype_cum_cartPen\"] = group[\"cartPen_mod\"].cumsum()\n", "            group[\"ptype_cum_cartPen_percent\"] = (\n", "                group[\"ptype_cum_cartPen\"] / group[\"cartPen_mod\"].sum()\n", "            )\n", "\n", "            # Select top x% contributing items\n", "            top_95_percent_indexes = group[\n", "                group[\"ptype_cum_cartPen_percent\"] <= ptype_head_threshold\n", "            ].index\n", "\n", "            # always add just one more item in the head threshold set, if possible, to handle cases when just 1,2 items make up the entire sale\n", "            if len(top_95_percent_indexes) < len(group):\n", "                additional_index = group.index[len(top_95_percent_indexes)]\n", "                if group.loc[additional_index, \"sales_quantity\"] > 0:\n", "                    top_95_percent_indexes = top_95_percent_indexes.append(\n", "                        pd.Index([additional_index])\n", "                    )\n", "\n", "            top_items = group.loc[top_95_percent_indexes]\n", "            top_items[\"item_rank_2\"] = range(1, len(top_items) + 1)\n", "            result = group.merge(top_items[[\"item_id\", \"item_rank_2\"]], on=\"item_id\", how=\"left\")\n", "            # result.drop(['ptype_cum_cartPen_percent', 'ptype_cum_cartPen'], axis=1, inplace=True)\n", "            return result\n", "\n", "    data_df = data_df.groupby(\n", "        [\"l0\", \"l1\", \"l2\", \"product_type\", \"ptype_rank\"], group_keys=False, dropna=False\n", "    ).apply(assign_ranks)\n", "\n", "    data_df[\"ros\"] = data_df[\"cartPen_mod\"] * data_df[\"margin_percent\"] / data_df[\"item_factor\"]\n", "    data_df = data_df.sort_values(\n", "        [\"cartPen_mod\", \"ros\", \"item_factor\", \"unit_price\"], ascending=[False, False, True, True]\n", "    )\n", "    data_df.reset_index(drop=True, inplace=True)\n", "    data_df[\"item_rank_3\"] = data_df.index + 1\n", "\n", "    def populateItemRank(row):\n", "        if row[\"high_throughput_flag\"]:\n", "            return [1, 1]\n", "        elif row[\"new_assortment_flag\"]:\n", "            return [2, 2]\n", "        elif row[\"experimental_limited_coverage_flag\"]:\n", "            return [3, 3]\n", "        elif row[\"low_availability_flag\"]:\n", "            return [4, 4]\n", "        elif row[\"ptype_first_item_flag\"]:\n", "            return [5, 5]\n", "        elif not pd.isna(row[\"item_rank_2\"]) and not pd.isna(row[\"ptype_rank\"]):\n", "            return [row[\"item_rank_2\"] + (10 ** 5) * row[\"ptype_rank\"], 6]\n", "        elif not pd.isna(row[\"item_rank_3\"]):\n", "            return [row[\"item_rank_3\"] + 10 ** 9, 7]\n", "\n", "    data_df[[\"raw_item_rank\", \"item_set_id\"]] = data_df.apply(\n", "        lambda row: pd.Series(populateItemRank(row)), axis=1, result_type=\"expand\"\n", "    )\n", "    data_df = data_df.sort_values(\n", "        [\"raw_item_rank\", \"cartPen_mod\", \"ros\", \"item_factor\", \"unit_price\"],\n", "        ascending=[True, False, False, True, True],\n", "    )\n", "    data_df.reset_index(drop=True, inplace=True)\n", "    data_df[\"final_item_rank\"] = data_df.index + 1\n", "\n", "    data_df.to_csv(city + \"_final_ranking_output.csv\", index=False)\n", "\n", "    data_df[\"city\"] = city\n", "    data_df[\"city_id\"] = outlets_df[\"city_id\"].unique()[0]\n", "    for col in data_df.columns:\n", "        if data_df[col].dtype == \"object\":\n", "            data_df[col] = data_df[col].astype(str)\n", "\n", "    if not USE_LOCAL_CSV_DATA:\n", "        pushDfToTrino(\n", "            data_df,\n", "            \"city_assortment_ranking\",\n", "            description=\"city_assortment_ranking\",\n", "            primaryKeys=[\"item_id\", \"city\"],\n", "            load_type=\"upsert\",\n", "            environ=TRINO_ENV,\n", "        )\n", "        pushDfToTrino(\n", "            data_df,\n", "            \"city_assortment_ranking_log\",\n", "            description=\"city_assortment_ranking_log\",\n", "            primaryKeys=[\"item_id\", \"city\"],\n", "            load_type=\"upsert\",\n", "            environ=TRINO_ENV,\n", "            partition_key=[\"insert_ds_ist\"],\n", "        )\n", "\n", "    return data_df\n", "\n", "\n", "overall_df = pd.DataFrame()\n", "cities_2d_arr = split_array(cities)\n", "day_of_week = get_day_of_week()\n", "\n", "if len(cities_2d_arr) > day_of_week:\n", "    for city in cities_2d_arr[day_of_week]:\n", "        print(\"generating ranking for city\", city)\n", "        overall_df = pd.concat([overall_df, createAssortmentRanking(city)])\n", "\n", "print(\"Done!!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 4}