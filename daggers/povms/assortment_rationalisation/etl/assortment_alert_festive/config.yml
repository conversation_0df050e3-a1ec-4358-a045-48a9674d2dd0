alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: assortment_alert_festive
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RR41CKQX
path: povms/assortment_rationalisation/etl/assortment_alert_festive
paused: True
pool: povms_pool
project_name: assortment_rationalisation
schedule:
  end_date: '2025-04-18T00:00:00'
  interval: 30 5,13 * * *
  start_date: '2025-01-20T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
