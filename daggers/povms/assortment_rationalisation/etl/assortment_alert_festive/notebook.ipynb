{"cells": [{"cell_type": "code", "execution_count": null, "id": "247f3a37-7cd4-4b85-9512-68aa285e2d48", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "from pytz import timezone\n", "import requests\n", "from requests.exceptions import HTTPError\n", "from tabulate import tabulate\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "start_time = time.time()"]}, {"cell_type": "code", "execution_count": null, "id": "3312ca44-06ec-420f-b08c-fa0143a51bca", "metadata": {}, "outputs": [], "source": ["fyi_channel = \"bl-festive-execution-category\""]}, {"cell_type": "code", "execution_count": null, "id": "70cdebf7-995b-48c4-8ffa-00de57c68f8e", "metadata": {}, "outputs": [], "source": ["def assortment_alerts():\n", "    sql = f\"\"\"\n", "    with items_expected_assort_type as (\n", "select distinct item_id, assortment_type, location_id as cluster_id, event_name,sale_end_date,sale_start_date from ars.bulk_process_event_planner ep \n", "join (\n", "SELECT sei.id AS id,\n", "        CONCAT(name, '_', cast(start_date as varchar)) AS event_name\n", " FROM rpc.supply_event_info sei\n", " JOIN rpc.supply_event se ON se.id = sei.supply_event_id\n", " WHERE sei.active = TRUE\n", "   AND se.active = TRUE\n", "   AND lower(name) NOT LIKE '%%winter%%'\n", " ORDER BY sei.id DESC) ed on ed.id = ep.event_id\n", "where sale_start_date between current_date and current_date + interval '30' day and partition_field is not null and status = 'approved' \n", "and planned_qty > 0\n", "),\n", "\n", "fo as (SELECT om.facility_id,\n", "          om.outlet_id AS outlet_id,\n", "          om.outlet_name AS outlet_name,\n", "          rcl.id AS city_id,\n", "          rcl.name as city_name,\n", "          cluster_id,\n", "          case when type_id = 3 then 'Longtail only' else 'express_and_longtail' end as \"store_type\" -- lT ONLY STORES\n", "   FROM po.physical_facility_outlet_mapping om\n", "   INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "   AND rco.business_type_id IN (7) --- ONLY DARK STORES\n", "   AND rco.company_type_id NOT IN (771,\n", "                                   767) -- Hp and bISTRO STORES\n", "   AND rco.lake_active_record\n", "   INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "   AND rcl.lake_active_record\n", "   INNER JOIN dwh.dim_merchant_outlet_facility_mapping mo_map ON mo_map.facility_id = om.facility_id\n", "   AND rco.business_type_id = 7\n", "   AND is_current\n", "   AND is_current_mapping_active\n", "   AND is_backend_merchant_active\n", "   AND is_frontend_merchant_active\n", "   inner join (select cluster_id, city_id from rpc.ams_city_cluster_mapping where cluster_id > 15000 and active and lake_active_record) as clust on clust.city_id = om.city_id\n", "   WHERE om.active = 1\n", "     AND om.ars_active = 1\n", "     AND om.lake_active_record\n", "     AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "     AND om.outlet_name NOT LIKE '%%Draft%%'\n", "     AND om.outlet_name NOT LIKE '%%Test Store%%'\n", "     AND om.outlet_name NOT LIKE '%%Dummy%%'\n", "     AND om.outlet_name not like '%%Digital Goods%%'\n", "     AND om.facility_id not in ( 273,3253 ) -- kumbh specific and test store\n", "     and outlet_id in (select distinct outlet_id from po.bulk_facility_outlet_mapping where active and lake_active_record)\n", "     ),\n", "     \n", "outlets as (\n", "\n", "with express_outlets as (select cluster_id, 'EXPRESS' as \"flag\",\n", "count(distinct facility_id) as express_stores \n", "from fo\n", "where store_type = 'express_and_longtail'\n", "group by 1,2),\n", "\n", "-- select * from express_outlets\n", "\n", "longtail_outlets as (select cluster_id, 'LONGATIL' AS \"flag\", count(distinct facility_id) as longtail_stores from fo\n", "where facility_id in (select distinct long_tail_facility_id from po.long_tail_facility_store_mapping where active = 1 and lake_active_record)\n", "group by 1,2),\n", "\n", "--select * from longtail_outlets\n", "\n", "hybrid_outlets as (with hybrid_fac as ((select long_tail_facility_id as facility_id from po.long_tail_facility_store_mapping where active = 1 and lake_active_record)\n", "union (select distinct facility_id from fo where store_type = 'express_and_longtail' and facility_id not in (select distinct adjacent_facility_id from \n", "po.long_tail_facility_store_mapping where active = 1 and lake_active_record)))\n", "select cluster_id, 'HYBRID' AS \"flag\",count(distinct facility_id) as hybrid_stores from fo where facility_id in (select distinct facility_id from hybrid_fac)\n", "group by 1,2\n", "\n", ")\n", "\n", "select cluster_id, flag, express_stores as outlet_count from express_outlets union select cluster_id, flag, longtail_stores as outlet_count from longtail_outlets \n", "union select cluster_id, flag, hybrid_stores as outlet_count from hybrid_outlets),\n", "\n", "\n", "active_facility as (select cluster_id, item_id,\n", "count(distinct ma.facility_id) as active_facility_count \n", "from rpc.product_facility_master_assortment ma inner join fo on fo.facility_id = ma.facility_id\n", "where ma.active = 1 and master_assortment_substate_id in (1,3) group by 1,2)\n", "\n", "-- select * from active_facility where item_id = 10161564 and cluster_id = 15002\n", "\n", "select at.item_id,pp.name as item_name, pp.product_type,  assortment_type, at.cluster_id, c.cluster_name,\n", "event_name,sale_end_date,sale_start_date, outlet_count, coalesce(active_facility_count,0) as active_facility_count, outlet_count - coalesce(active_facility_count,0) as missing_outlets_assort\n", "from items_expected_assort_type at inner join outlets o on o.cluster_id = at.cluster_id and o.flag = at.assortment_type\n", "left join active_facility af on af.cluster_id = at.cluster_id and at.item_id = af.item_id\n", "inner join rpc.ams_cluster c on c.cluster_id = at.cluster_id and c.lake_active_record\n", "inner join rpc.item_category_details pp on pp.item_id = at.item_id\n", "where outlet_count > coalesce(active_facility_count,0)\n", "order by outlet_count - coalesce(active_facility_count,0)\n", "    \"\"\"\n", "    return pd.read_sql_query(con=con, sql=sql)"]}, {"cell_type": "code", "execution_count": null, "id": "6bf11d3d-ab1d-4d39-aae2-68095329747e", "metadata": {}, "outputs": [], "source": ["assortment_alerts_raw = assortment_alerts()"]}, {"cell_type": "code", "execution_count": null, "id": "1c1d2558-f3c1-4714-a067-9<PERSON>ebeda5e49", "metadata": {}, "outputs": [], "source": ["df = assortment_alerts_raw.sort_values(by=[\"missing_outlets_assort\"], ascending=False).reset_index(\n", "    drop=True\n", ")\n", "\n", "df = df.head(500)\n", "alert_df = df.copy()\n", "\n", "alert_df.columns = [\n", "    \"item_id\",\n", "    \"item_name\",\n", "    \"product_type\",\n", "    \"assortment_type\",\n", "    \"cluster_id\",\n", "    \"cluster_name\",\n", "    \"event_name\",\n", "    \"sale_end_date\",\n", "    \"sale_start_date\",\n", "    \"outlet_count\",\n", "    \"active_facility_count\",\n", "    \"missing_outlets_assort\",\n", "]\n", "alert_df"]}, {"cell_type": "code", "execution_count": null, "id": "37f332d2-1726-45da-9dea-01c5d20393eb", "metadata": {}, "outputs": [], "source": ["alert_df.to_csv(\"missing_festive_assortment.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "df81a7be-4088-4892-8898-e8825b5bd6da", "metadata": {}, "outputs": [], "source": ["def render_mpl_table(\n", "    data,\n", "    col_width=5.0,\n", "    row_height=0.625,\n", "    font_size=20,\n", "    header_color=\"#E96125\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs)\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        #         elif k[0] == data.shape[0]:\n", "        #             cell.set_text_props(weight=\"bold\", ma=\"center\")\n", "        # cell.set_facecolor('#f1f1f2')\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "fig, ax = render_mpl_table(alert_df.head(50), header_columns=0)\n", "fig.savefig(\"missing_festive_assortment.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "32b698d5-81a4-4117-b715-5f6a96978004", "metadata": {}, "outputs": [], "source": ["text_req = (\n", "    \"<@U07N5A3KTPE> <@U058T9CV2BV> Top 500 missing assortment for festive items in a cluster for \"\n", "    + str(date.today() + <PERSON><PERSON><PERSON>(hours=5.5))\n", ")\n", "pb.send_slack_message(\n", "    channel=fyi_channel, text=text_req, files=[\"./missing_festive_assortment.png\"]\n", ")\n", "pb.send_slack_message(channel=fyi_channel, files=[\"./missing_festive_assortment.csv\"])\n", "text_req"]}, {"cell_type": "code", "execution_count": null, "id": "e32345e5-bb59-4632-8af0-50026ce944cb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}