{"cells": [{"cell_type": "code", "execution_count": null, "id": "37acadea-532f-4cfa-b986-eeb1e48066f2", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import numpy as np\n", "import pandas as pd\n", "import math\n", "from datetime import date, timedelta\n", "import warnings\n", "import pencilbox as pb\n", "import time\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "4850ab22-4977-43b8-8752-712c29053ab1", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"insert_ds_ist\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"city_name\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility_id\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"outlet_id\"},\n", "    {\"name\": \"merchant_id\", \"type\": \"integer\", \"description\": \"merchant_id\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "    {\n", "        \"name\": \"num_item_available_hours\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"num_item_available_hours\",\n", "    },\n", "    {\"name\": \"num_item_total_hours\", \"type\": \"integer\", \"description\": \"num_item_total_hours\"},\n", "    {\"name\": \"active\", \"type\": \"integer\", \"description\": \"active\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "b93e940d-84d7-4d80-a424-fc45e8875286", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"daily_availability_snapshot\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"insert_ds_ist\", \"city_name\", \"facility_id\", \"item_id\"],\n", "    \"partition_key\": [\"insert_ds_ist\", \"city_name\"],\n", "    # \"incremental_key\": \"updated_at_ist\",\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "    \"table_description\": \"daily_availability_snapshot\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "a1ec5186-97b4-4b62-a8d5-d4ba54295f72", "metadata": {}, "outputs": [], "source": ["num_iterations = 120"]}, {"cell_type": "code", "execution_count": null, "id": "d169df6c-0255-4b35-8e11-44cad19e9204", "metadata": {}, "outputs": [], "source": ["import time\n", "import traceback\n", "\n", "!pip install tenacity\n", "\n", "from tenacity import retry, stop_after_attempt, wait_fixed\n", "\n", "\n", "@retry(stop=stop_after_attempt(3), wait=wait_fixed(60), reraise=True)\n", "def to_trino_with_retry(*args, **kwargs):\n", "    return pb.to_trino(*args, **kwargs)\n", "\n", "\n", "job_status = {i: \"Pending\" for i in range(51, num_iterations)}\n", "\n", "for i in range(51, num_iterations):\n", "    try:\n", "        start = time.time()\n", "        s3_object_name = f\"Arpit_Bansal/temp_folder/availability_{i}\"\n", "        # to_trino_with_retry(pd.read_parquet(f\"s3://prod-dse-projects/{s3_object_name}\"), **kwargs)\n", "        end = time.time()\n", "        duration = end - start\n", "        if duration > 60:\n", "            print(f\"Data pushed for param {i} in table in: {duration / 60:.2f} min\")\n", "        else:\n", "            print(f\"Data pushed for param {i} in table in: {duration:.2f} s\")\n", "        job_status[i] = \"Success\"\n", "\n", "    except BaseException as e:\n", "        traceback.print_exc()\n", "        print(f\"Failed to push data to the table. Error: {e}\")\n", "        job_status[i] = \"Failed\"\n", "\n", "# Display Job Status Summary\n", "print(\"\\n=== Job Execution Summary ===\")\n", "print(f\"Status: {job_status}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}