{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip uninstall -y openpyxl"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install openpyxl==3.1.5"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import requests\n", "import datetime\n", "from pytz import timezone\n", "import boto3\n", "import os\n", "import sys\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import json\n", "import openpyxl\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st = time.time()\n", "\n", "spreadSheetID = \"1MjhCGmV0YGpEMaHibWLPkNzDgEOtV_ANehA-W0pabe8\"\n", "EXECUTION_MODE = \"PROD\"\n", "ENVIRON = \"supply_etls\"\n", "deactivation_immunity_reason_codes_sheet = \"deactivation_immunity_reason_codes\"\n", "reactivation_immunity_reason_codes_sheet = \"reactivation_immunity_reason_codes\"\n", "polygon_change_immunity_reason_codes_sheet = \"polygon_change_immunity_reason_codes\"\n", "slack_alert_channel = \"assortment-upload-loggers\"\n", "ifsr_lookback = 15"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def sendSlackAlert(execution_mode, text):\n", "    if execution_mode == \"PROD\":\n", "        try:\n", "            import pencilbox as pb\n", "\n", "            pb.send_slack_message(channel=slack_alert_channel, text=text)\n", "        except Exception as e:\n", "            print(f\"Error sending Slack message: {e}, {text}\")\n", "        # handle the error further if needed\n", "    else:\n", "        print(\"local environment, just printing to console\")\n", "        print(text)\n", "\n", "\n", "def readFromSheetsWithRetry(spreadSheetId, sheetName, raiseExceptionFlag=True, mode=EXECUTION_MODE):\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            if mode == \"LOCAL\":\n", "                script_dir = \"/home/<USER>/inventory-planning-playbooks/\"\n", "                sys.path.insert(0, script_dir)\n", "                from utils.commonFunc import readSpreadsheet\n", "\n", "                values = readSpreadsheet(spreadSheetId, sheetName)\n", "                df = pd.DataFrame(values[1:], columns=values[0])\n", "                return df\n", "            else:\n", "                df = pb.from_sheets(spreadSheetId, sheetName)\n", "                return df\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error fetching data (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to fetch data after {max_retries} attempts: {e}\")\n", "                if raiseExceptionFlag:\n", "                    raise Exception(\n", "                        \"Failed to readFromSheetsWithRetry after {} attempts\".format(max_retries)\n", "                    ) from e\n", "                else:\n", "                    sendSlackAlert(\n", "                        mode,\n", "                        \"hybrid rationalizor dag failed to read sheet\" + spreadSheetId + sheetName,\n", "                    )\n", "                    return pd.DataFrame()\n", "\n", "\n", "def fetchDataFromDB(query):\n", "    import pencilbox as pb\n", "    import pandas as pd\n", "    import time\n", "\n", "    con = pb.get_connection(\"[Warehouse] Trino\")\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            df = pd.read_sql_query(sql=query, con=con)\n", "            return df\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error fetching data (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to fetch data after {max_retries} attempts: {e}\")\n", "                return pd.DataFrame()\n", "\n", "\n", "def getHybridAssortmentUniverse():\n", "    query = \"\"\"\n", "WITH fo AS\n", "  (SELECT om.facility_id,\n", "          om.outlet_id AS outlet_id,\n", "          mo_map.frontend_merchant_id AS merchant_id,\n", "          om.outlet_name AS outlet_name,\n", "          rcl.id AS city_id,\n", "          rcl.name AS city_name,\n", "          rcs.name AS state\n", "   FROM po.physical_facility_outlet_mapping om\n", "   INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "   AND rco.business_type_id IN (7)\n", "   AND rco.company_type_id NOT IN (771,\n", "                                   767)\n", "   AND rco.lake_active_record\n", "   INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "   AND rcl.lake_active_record\n", "   INNER JOIN dwh.dim_merchant_outlet_facility_mapping mo_map ON mo_map.facility_id = om.facility_id\n", "   AND rco.business_type_id = 7\n", "   AND is_current\n", "   AND is_current_mapping_active\n", "   AND is_backend_merchant_active\n", "   AND is_frontend_merchant_active\n", "   INNER JOIN retail.console_state AS rcs ON rcs.id = rcl.state_id\n", "   AND rcs.lake_active_record\n", "   INNER JOIN\n", "     (SELECT DISTINCT cast(merchant_id AS int) merchant_id\n", "      FROM serviceability.ser_store_polygons\n", "      WHERE TYPE IN ('STORE_POLYGON',\n", "                     'LONGTAIL_POLYGON')\n", "        AND is_active = TRUE\n", "        AND lake_active_record ) poly ON poly.merchant_id = mo_map.frontend_merchant_id\n", "   WHERE om.active = 1\n", "     AND om.ars_active = 1\n", "     AND om.lake_active_record\n", "     AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "     AND om.outlet_name NOT LIKE '%%Draft%%'\n", "     AND om.outlet_name NOT LIKE '%%Test Store%%'\n", "     AND om.outlet_name NOT LIKE '%%Dummy%%'\n", "     AND om.facility_id != 273\n", "     AND om.outlet_id IN\n", "       (SELECT DISTINCT outlet_id\n", "        FROM po.bulk_facility_outlet_mapping\n", "        WHERE active\n", "          AND lake_active_record ) ),\n", "     hybrid_city_item_combinations AS\n", "  (SELECT DISTINCT fo.city_id,\n", "                   pfma.item_id\n", "   FROM rpc.product_facility_master_assortment pfma\n", "   INNER JOIN fo ON fo.facility_id = pfma.facility_id\n", "   WHERE json_extract_scalar(meta, '$.launch_type') = 'HYBRID'\n", "     AND master_assortment_substate_id IN (1,\n", "                                           3)\n", "     AND lake_active_record\n", "     AND active = 1 ),\n", "     request AS\n", "  (SELECT *\n", "   FROM\n", "     (SELECT ifsr.item_id,\n", "             ifsr.facility_id,\n", "             ifsr.state,\n", "             date(ifsr.updated_at) AS updated_at,\n", "             ifsr.assortment_type,\n", "             ifsr.request_type,\n", "             TRY_CAST(JSON_EXTRACT(ifsr.meta,'$.request_reason_type') AS varchar) AS reason,\n", "             ifsr.request_id AS job_id,\n", "             rank() OVER (PARTITION BY ifsr.item_id,\n", "                                       ifsr.facility_id\n", "                          ORDER BY ifsr.updated_at DESC) AS update_rank,\n", "                         ifsr.created_by\n", "      FROM rpc.item_facility_state_request ifsr\n", "      INNER JOIN fo ON fo.facility_id = ifsr.facility_id\n", "      INNER JOIN hybrid_city_item_combinations hcic ON hcic.item_id = ifsr.item_id\n", "      AND fo.city_id = hcic.city_id\n", "      WHERE ifsr.insert_ds_ist >=CAST (CURRENT_DATE - interval '1000' DAY AS varchar)\n", "        AND active = 1\n", "        AND ifsr.state IN (1,\n", "                           3,\n", "                           2,\n", "                           4)\n", "        AND ifsr.status = 'COMPLETED'\n", "        AND NOT (ifsr.state = 4\n", "                 AND ifsr.created_by = 14) ) AS X\n", "   WHERE update_rank = 1)\n", "SELECT pfma.item_id,\n", "       fo.city_id,\n", "       fo.city_name,\n", "       pfma.facility_id,\n", "       pfma.master_assortment_substate_id,\n", "       pfma.assortment_type,\n", "       req.request_type,\n", "       req.reason AS request_reason,\n", "       req.job_id AS request_job_id\n", "FROM rpc.product_facility_master_assortment pfma\n", "INNER JOIN fo ON fo.facility_id = pfma.facility_id\n", "INNER JOIN hybrid_city_item_combinations hcic ON hcic.item_id = pfma.item_id\n", "AND hcic.city_id = fo.city_id\n", "LEFT JOIN request req ON req.facility_id = pfma.facility_id\n", "AND req.item_id = pfma.item_id\n", "WHERE pfma.lake_active_record\n", "  AND pfma.active = 1\n", "    \"\"\"\n", "    print(query)\n", "    return fetchDataFromDB(query)\n", "\n", "\n", "def getFrontEndOutlets():\n", "    query = \"\"\"\n", "        WITH fo AS\n", "      (SELECT om.facility_id,\n", "              om.outlet_id AS outlet_id,\n", "              mo_map.frontend_merchant_id AS merchant_id,\n", "              om.outlet_name AS outlet_name,\n", "              rcl.id AS city_id,\n", "              rcl.name AS city_name,\n", "              rcs.name AS state\n", "      FROM po.physical_facility_outlet_mapping om\n", "      INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "      AND rco.business_type_id IN (7)\n", "      AND rco.company_type_id NOT IN (771, 767)\n", "      AND rco.lake_active_record\n", "      INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "      AND rcl.lake_active_record\n", "      INNER JOIN dwh.dim_merchant_outlet_facility_mapping mo_map ON mo_map.facility_id = om.facility_id\n", "      AND rco.business_type_id = 7\n", "      AND is_current\n", "      AND is_current_mapping_active\n", "      AND is_backend_merchant_active\n", "      AND is_frontend_merchant_active\n", "      INNER JOIN retail.console_state AS rcs ON rcs.id = rcl.state_id\n", "      AND rcs.lake_active_record\n", "      INNER JOIN\n", "        (SELECT distinct cast(merchant_id AS int) merchant_id\n", "          FROM serviceability.ser_store_polygons\n", "          WHERE TYPE in ('STORE_POLYGON','LONGTAIL_POLYGON')\n", "            AND is_active = TRUE\n", "            AND lake_active_record) poly ON poly.merchant_id = mo_map.frontend_merchant_id\n", "      WHERE om.active = 1\n", "        AND om.ars_active = 1\n", "        AND om.lake_active_record\n", "        AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "        AND om.outlet_name NOT LIKE '%%Draft%%'\n", "        AND om.outlet_name NOT LIKE '%%Test Store%%'\n", "        AND om.outlet_name NOT LIKE '%%Dummy%%'\n", "        AND om.facility_id != 273\n", "        AND om.outlet_id IN\n", "          (SELECT DISTINCT outlet_id\n", "            FROM po.bulk_facility_outlet_mapping\n", "            WHERE active\n", "              AND lake_active_record) )\n", "    SELECT *\n", "    FROM fo\n", "    \"\"\"\n", "    # 273 is a test dark store mapped to multiple merchant ids leading to duplicate rows\n", "    print(query)\n", "    return fetchDataFromDB(query)\n", "\n", "\n", "def getHybridOutletsARSData():\n", "    query = \"\"\"\n", "  WITH fo AS\n", "      (SELECT om.facility_id,\n", "              om.outlet_id AS outlet_id,\n", "              mo_map.frontend_merchant_id AS merchant_id,\n", "              om.outlet_name AS outlet_name,\n", "              rcl.name AS city_name,\n", "              rcs.name AS state\n", "      FROM po.physical_facility_outlet_mapping om\n", "      INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "      AND rco.business_type_id IN (7)\n", "      AND rco.company_type_id NOT IN (771, 767)\n", "      AND rco.lake_active_record\n", "      INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "      AND rcl.lake_active_record\n", "      INNER JOIN dwh.dim_merchant_outlet_facility_mapping mo_map ON mo_map.facility_id = om.facility_id\n", "      AND rco.business_type_id = 7\n", "      AND is_current\n", "      AND is_current_mapping_active\n", "      AND is_backend_merchant_active\n", "      AND is_frontend_merchant_active\n", "      INNER JOIN retail.console_state AS rcs ON rcs.id = rcl.state_id\n", "      AND rcs.lake_active_record\n", "      INNER JOIN\n", "        (SELECT distinct cast(merchant_id AS int) merchant_id\n", "          FROM serviceability.ser_store_polygons\n", "          WHERE TYPE in ('STORE_POLYGON','LONGTAIL_POLYGON')\n", "            AND is_active = TRUE\n", "            AND lake_active_record) poly ON poly.merchant_id = mo_map.frontend_merchant_id\n", "      WHERE om.active = 1\n", "        AND om.ars_active = 1\n", "        AND om.lake_active_record\n", "        AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "        AND om.outlet_name NOT LIKE '%%Draft%%'\n", "        AND om.outlet_name NOT LIKE '%%Test Store%%'\n", "        AND om.outlet_name NOT LIKE '%%Dummy%%'\n", "        AND om.facility_id != 273\n", "        AND om.outlet_id IN\n", "          (SELECT DISTINCT outlet_id\n", "            FROM po.bulk_facility_outlet_mapping\n", "            WHERE active\n", "              AND lake_active_record) ),\n", "     coverage AS\n", "  (SELECT long_tail_facility_id,\n", "          adjacent_facility_id\n", "   FROM po.long_tail_facility_store_mapping\n", "   WHERE active = 1\n", "     AND lake_active_record),\n", "     base AS\n", "  (SELECT *,\n", "          'express_stores' AS tagging\n", "   FROM fo\n", "   WHERE facility_id NOT IN\n", "       (SELECT adjacent_facility_id\n", "        FROM coverage)\n", "     AND facility_id NOT IN\n", "       (SELECT long_tail_facility_id\n", "        FROM coverage)\n", "   UNION ALL SELECT *,\n", "                    'longtail_stores' AS tagging\n", "   FROM fo\n", "   WHERE facility_id IN\n", "       (SELECT long_tail_facility_id\n", "        FROM coverage) )\n", "SELECT *\n", "FROM base\n", "  \"\"\"\n", "    print(query)\n", "    return fetchDataFromDB(query)\n", "\n", "\n", "def pushToGoogleSheetsWithRetry(df, sheet_id, sheet_name):\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "    import pencilbox as pb\n", "    import time\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            pb.to_sheets(df, sheet_id, sheet_name)\n", "            return\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error pushing data (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to push data after {max_retries} attempts: {e}\")\n", "                return\n", "\n", "\n", "def getFestivePlannedItemIds(lookahead):\n", "    query = f\"\"\"\n", "  WITH festive_items AS (\n", "  SELECT\n", "    DISTINCT item_id\n", "  FROM\n", "    ars.bulk_process_event_planner\n", "  WHERE\n", "    active = 1\n", "    and planned_qty > 0\n", "    AND partition_field IS NOT NULL\n", "    AND (\n", "      sale_end_date + interval '2' DAY >= CURRENT_DATE\n", "      AND sale_start_date - interval '{lookahead}' DAY <= CURRENT_DATE\n", "    )\n", ")\n", "SELECT\n", "  item_id\n", "FROM\n", "  festive_items\n", "  \"\"\"\n", "    print(query)\n", "    return fetchDataFromDB(query)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["deactivation_immunity_reason_codes = readFromSheetsWithRetry(\n", "    spreadSheetID, deactivation_immunity_reason_codes_sheet\n", ")\n", "if deactivation_immunity_reason_codes.shape[0] > 0:\n", "    deactivation_immunity_reason_codes = deactivation_immunity_reason_codes.reason_code.unique()\n", "else:\n", "    deactivation_immunity_reason_codes = []\n", "print(\"deactivation_immunity_reason_codes\", deactivation_immunity_reason_codes)\n", "\n", "reactivation_immunity_reason_codes = readFromSheetsWithRetry(\n", "    spreadSheetID, reactivation_immunity_reason_codes_sheet\n", ")\n", "if reactivation_immunity_reason_codes.shape[0] > 0:\n", "    reactivation_immunity_reason_codes = reactivation_immunity_reason_codes.reason_code.unique()\n", "else:\n", "    reactivation_immunity_reason_codes = []\n", "print(\"reactivation_immunity_reason_codes\", reactivation_immunity_reason_codes)\n", "\n", "polygon_change_immunity_reason_codes = readFromSheetsWithRetry(\n", "    spreadSheetID, polygon_change_immunity_reason_codes_sheet\n", ")\n", "if polygon_change_immunity_reason_codes.shape[0] > 0:\n", "    polygon_change_immunity_reason_codes = polygon_change_immunity_reason_codes.reason_code.unique()\n", "else:\n", "    polygon_change_immunity_reason_codes = []\n", "print(\"polygon_change_immunity_reason_codes\", polygon_change_immunity_reason_codes)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hybrid_outlets = getHybridOutletsARSData()\n", "assortment_universe = getHybridAssortmentUniverse()\n", "frontend_outlets_df = getFrontEndOutlets()\n", "festive_item_ids_df = getFestivePlannedItemIds(10)\n", "\n", "festive_item_ids_df.to_csv(\"festive_item_ids_df.csv\", index=False)\n", "hybrid_outlets.to_csv(\"hybrid_outlets.csv\", index=False)\n", "assortment_universe.to_csv(\"ast_universe.csv\", index=False)\n", "frontend_outlets_df.to_csv(\"frontend_outlets.csv\", index=False)\n", "\n", "if festive_item_ids_df.shape[0] > 0:\n", "    festive_item_ids = festive_item_ids_df[\"item_id\"].unique()\n", "else:\n", "    festive_item_ids = []\n", "\n", "# hybrid_outlets = pd.read_csv('hybrid_outlets.csv')\n", "# assortment_universe = pd.read_csv('ast_universe.csv')\n", "# frontend_outlets_df = pd.read_csv('frontend_outlets.csv')\n", "\n", "hybrid_outlets = hybrid_outlets[[\"facility_id\", \"tagging\"]]\n", "hybrid_outlets = hybrid_outlets.merge(\n", "    frontend_outlets_df[[\"facility_id\", \"city_id\"]], on=\"facility_id\", how=\"inner\"\n", ")\n", "\n", "ideal_universe = assortment_universe[[\"item_id\", \"city_id\"]].drop_duplicates()\n", "ideal_universe = ideal_universe.merge(hybrid_outlets, on=[\"city_id\"])\n", "\n", "# filter for frontend outlet ids\n", "comparison_df = pd.merge(\n", "    assortment_universe,\n", "    ideal_universe[[\"item_id\", \"facility_id\", \"tagging\"]],\n", "    on=[\"facility_id\", \"item_id\"],\n", "    how=\"outer\",\n", ")\n", "\n", "# assortment upload csv file column names - item_id, city_name, backend_facility_id, frontend_facility_id, master_assortment_substate, assortment_type, substate_reason_type, request_reason_type\n", "\n", "# step 1 - identify stores where item should be deactivated\n", "to_be_disabled_outlets = comparison_df[pd.isna(comparison_df[\"tagging\"])]\n", "to_be_disabled_outlets = to_be_disabled_outlets[\n", "    to_be_disabled_outlets[\"master_assortment_substate_id\"].isin([1, 3])\n", "    & ~(to_be_disabled_outlets[\"request_reason\"].isin(deactivation_immunity_reason_codes))\n", "]\n", "\n", "if to_be_disabled_outlets.shape[0] > 0:\n", "    step1_df = to_be_disabled_outlets[[\"item_id\", \"facility_id\"]]\n", "    step1_df.rename(columns={\"facility_id\": \"frontend_facility_id\"}, inplace=True)\n", "    step1_df[\"master_assortment_substate\"] = \"Inactive\"\n", "    step1_df[\"assortment_type\"] = \"EXPRESS_ALL\"\n", "    step1_df[\"substate_reason_type\"] = \"BAU\"\n", "    step1_df[\"request_reason_type\"] = \"AUTO-DEACTIVATION_FOR_HYBRID_SKU\"\n", "    step1_df[\"city_name\"] = \"\"\n", "    step1_df[\"backend_facility_id\"] = \"\"\n", "\n", "    step1_df = step1_df[~step1_df[\"item_id\"].isin(festive_item_ids)]\n", "\n", "    step1_df = step1_df[\n", "        [\n", "            \"item_id\",\n", "            \"city_name\",\n", "            \"backend_facility_id\",\n", "            \"frontend_facility_id\",\n", "            \"master_assortment_substate\",\n", "            \"assortment_type\",\n", "            \"substate_reason_type\",\n", "            \"request_reason_type\",\n", "        ]\n", "    ]\n", "    step1_df.to_csv(\"step1.csv\", index=False)\n", "    # pushToGoogleSheetsWithRetry(step1_df, spreadSheetID, 'Step 1 - to_be_disabled_outlets')\n", "else:\n", "    print(\"no step 1 file generated\")\n", "    step1_df = pd.DataFrame()\n", "    # pushToGoogleSheetsWithRetry(pd.DataFrame(), spreadSheetID, 'Step 1 - to_be_disabled_outlets')\n", "\n", "# step 2 - identify stores where item is not even activated but it should be.\n", "# NOTE - this does not suggest outlets for which assortment mapping has been disabled (i.e. state in 2,4) - it only suggests outlets with no assortment mapping yet for that item\n", "# the assumption is that the disabled cases were done due to some constrait like space, etc, so re-enabling it should not be done in an automated script.\n", "\n", "to_be_activated_outlets = comparison_df[pd.isna(comparison_df[\"master_assortment_substate_id\"])]\n", "\n", "existing_substates = (\n", "    assortment_universe[assortment_universe[\"master_assortment_substate_id\"].isin([1, 3])]\n", "    .groupby([\"item_id\", \"city_id\", \"master_assortment_substate_id\"])\n", "    .agg(count_stores=(\"facility_id\", \"nunique\"))\n", "    .reset_index()\n", ")\n", "final_substates = existing_substates.loc[\n", "    existing_substates.groupby([\"item_id\", \"city_id\"])[\"count_stores\"].idxmax()\n", "][[\"item_id\", \"city_id\", \"master_assortment_substate_id\"]]\n", "final_substates[\"master_assortment_substate\"] = final_substates[\n", "    \"master_assortment_substate_id\"\n", "].apply(lambda x: \"Active\" if x == 1 else \"Temporarily Inactive\")\n", "final_substates = final_substates[[\"item_id\", \"city_id\", \"master_assortment_substate\"]]\n", "\n", "\n", "def idealAssortmentType(x):\n", "    if pd.isna(x):\n", "        return np.nan\n", "    elif x == \"longtail_stores\":\n", "        return \"LONGTAIL\"\n", "    elif x == \"express_stores\":\n", "        return \"EXPRESS\"\n", "    else:\n", "        return x\n", "\n", "\n", "if to_be_activated_outlets.shape[0] > 0:\n", "    to_be_activated_outlets[\"ideal_assortment_type\"] = to_be_activated_outlets.tagging.apply(\n", "        idealAssortmentType\n", "    )\n", "    step2_df = to_be_activated_outlets[[\"item_id\", \"facility_id\", \"ideal_assortment_type\"]]\n", "    step2_df = step2_df.merge(frontend_outlets_df[[\"facility_id\", \"city_id\"]], on=\"facility_id\")\n", "    step2_df = step2_df.merge(final_substates, on=[\"item_id\", \"city_id\"], how=\"left\")\n", "    step2_df = step2_df[\n", "        [\"item_id\", \"facility_id\", \"master_assortment_substate\", \"ideal_assortment_type\"]\n", "    ]\n", "    step2_df.rename(\n", "        columns={\"facility_id\": \"frontend_facility_id\", \"ideal_assortment_type\": \"assortment_type\"},\n", "        inplace=True,\n", "    )\n", "    step2_df[\"substate_reason_type\"] = \"BAU\"\n", "    step2_df[\"request_reason_type\"] = \"AUTO-ACTIVATION_FOR_HYBRID\"\n", "    step2_df[\"city_name\"] = \"\"\n", "    step2_df[\"backend_facility_id\"] = \"\"\n", "\n", "    step2_df = step2_df[\n", "        [\n", "            \"item_id\",\n", "            \"city_name\",\n", "            \"backend_facility_id\",\n", "            \"frontend_facility_id\",\n", "            \"master_assortment_substate\",\n", "            \"assortment_type\",\n", "            \"substate_reason_type\",\n", "            \"request_reason_type\",\n", "        ]\n", "    ]\n", "    step2_df[\"assortment_type\"] = step2_df[\"assortment_type\"].apply(\n", "        lambda x: \"EXPRESS_ALL\" if x == \"EXPRESS\" else x\n", "    )\n", "    step2_df.to_csv(\"step2.csv\", index=False)\n", "    # pushToGoogleSheetsWithRetry(step2_df, spreadSheetID, 'Step 2 - to_be_activated_outlets')\n", "else:\n", "    print(\"no step 2 file generated\")\n", "    step2_df = pd.DataFrame()\n", "    # pushToGoogleSheetsWithRetry(pd.DataFrame(), spreadSheetID, 'Step 2 - to_be_activated_outlets')\n", "\n", "# step 3 - identify items whose polygon/assortment type for any store is incorrect and needs to be updated\n", "\n", "comparison_df[\"ideal_assortment_type\"] = comparison_df.tagging.apply(idealAssortmentType)\n", "assortment_type_mismatch_outlets = comparison_df[\n", "    ~(\n", "        (comparison_df[\"assortment_type\"] == comparison_df[\"ideal_assortment_type\"])\n", "        | (\n", "            (comparison_df[\"assortment_type\"] == \"SUPER_LONGTAIL\")\n", "            & (comparison_df[\"ideal_assortment_type\"] == \"LONGTAIL\")\n", "        )\n", "    )\n", "    & ~pd.isna(comparison_df[\"tagging\"])\n", "    & (comparison_df[\"master_assortment_substate_id\"].isin([1, 3]))\n", "]\n", "assortment_type_mismatch_outlets = assortment_type_mismatch_outlets[\n", "    ~assortment_type_mismatch_outlets[\"request_reason\"].isin(polygon_change_immunity_reason_codes)\n", "]\n", "\n", "if assortment_type_mismatch_outlets.shape[0] > 0:\n", "    step3_df = assortment_type_mismatch_outlets[\n", "        [\"item_id\", \"facility_id\", \"master_assortment_substate_id\", \"ideal_assortment_type\"]\n", "    ]\n", "    step3_df.rename(\n", "        columns={\n", "            \"facility_id\": \"frontend_facility_id\",\n", "            \"ideal_assortment_type\": \"assortment_type\",\n", "            \"master_assortment_substate_id\": \"master_assortment_substate\",\n", "        },\n", "        inplace=True,\n", "    )\n", "    step3_df[\"assortment_type\"] = step3_df[\"assortment_type\"].apply(\n", "        lambda x: \"EXPRESS_ALL\" if x == \"EXPRESS\" else x\n", "    )\n", "    step3_df[\"master_assortment_substate\"] = step3_df[\"master_assortment_substate\"].apply(\n", "        lambda x: \"Active\" if x == 1 else \"Temporarily Inactive\"\n", "    )  # note - retaining master_assortment_substate whatever was already present on the store and not - overwriting with value identified in final_substates df above\n", "    step3_df[\"substate_reason_type\"] = \"BAU\"\n", "    step3_df[\"request_reason_type\"] = \"AUTO-ASSORTMENT_TYPE_CHANGE_HYBRID\"\n", "    step3_df[\"city_name\"] = \"\"\n", "    step3_df[\"backend_facility_id\"] = \"\"\n", "    step3_df = step3_df[\n", "        [\n", "            \"item_id\",\n", "            \"city_name\",\n", "            \"backend_facility_id\",\n", "            \"frontend_facility_id\",\n", "            \"master_assortment_substate\",\n", "            \"assortment_type\",\n", "            \"substate_reason_type\",\n", "            \"request_reason_type\",\n", "        ]\n", "    ]\n", "    step3_df.to_csv(\"step3.csv\", index=False)\n", "    # pushToGoogleSheetsWithRetry(step3_df, spreadSheetID, 'Step 3 - assortment_type_mismatch_outlets')\n", "else:\n", "    print(\"no step 3 file generated\")\n", "    step3_df = pd.DataFrame()\n", "    # pushToGoogleSheetsWithRetry(pd.DataFrame(), spreadSheetID, 'Step 3 - assortment_type_mismatch_outlets')\n", "\n", "# step 4 - identifying lt stores where the item has been deactivated - this impacts multiple stores in its polygon, so its critical\n", "lt_stores_where_item_deactivated = comparison_df[\n", "    (comparison_df[\"ideal_assortment_type\"] == \"LONGTAIL\")\n", "    & (~pd.isna(comparison_df[\"master_assortment_substate_id\"]))\n", "    & (~comparison_df[\"master_assortment_substate_id\"].isin([1, 3]))\n", "]\n", "lt_stores_where_item_deactivated = lt_stores_where_item_deactivated[\n", "    ~lt_stores_where_item_deactivated[\"request_reason\"].isin(reactivation_immunity_reason_codes)\n", "]\n", "\n", "if lt_stores_where_item_deactivated.shape[0] > 0:\n", "    step4_df = lt_stores_where_item_deactivated[\n", "        [\"item_id\", \"city_id\", \"facility_id\", \"ideal_assortment_type\"]\n", "    ]\n", "    step4_df = step4_df.merge(final_substates, on=[\"item_id\", \"city_id\"], how=\"left\")\n", "    step4_df = step4_df[\n", "        [\"item_id\", \"facility_id\", \"master_assortment_substate\", \"ideal_assortment_type\"]\n", "    ]\n", "    step4_df.rename(\n", "        columns={\"facility_id\": \"frontend_facility_id\", \"ideal_assortment_type\": \"assortment_type\"},\n", "        inplace=True,\n", "    )\n", "    step4_df[\"substate_reason_type\"] = \"BAU\"\n", "    step4_df[\"request_reason_type\"] = \"RE-ACTIVATION_FOR_HYBRID_LT_STORE\"\n", "    step4_df[\"city_name\"] = \"\"\n", "    step4_df[\"backend_facility_id\"] = \"\"\n", "\n", "    step4_df = step4_df[\n", "        [\n", "            \"item_id\",\n", "            \"city_name\",\n", "            \"backend_facility_id\",\n", "            \"frontend_facility_id\",\n", "            \"master_assortment_substate\",\n", "            \"assortment_type\",\n", "            \"substate_reason_type\",\n", "            \"request_reason_type\",\n", "        ]\n", "    ]\n", "    step4_df[\"assortment_type\"] = step4_df[\"assortment_type\"].apply(\n", "        lambda x: \"EXPRESS_ALL\" if x == \"EXPRESS\" else x\n", "    )\n", "    step4_df.to_csv(\"step4.csv\", index=False)\n", "    # pushToGoogleSheetsWithRetry(step4_df, spreadSheetID, 'Step 4 - LT Stores Mandatory Activation')\n", "else:\n", "    print(\"no step 4 file generated\")\n", "    step4_df = pd.DataFrame()\n", "    # pushToGoogleSheetsWithRetry(pd.DataFrame(), spreadSheetID, 'Step 4 - LT Stores Mandatory Activation')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logTable = \"assortment_bulk_upload_dag_log_v2\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def upload_to_bu(file_path, upload_type):\n", "    try:\n", "        url = \"https://retail-internal.grofer.io/bulk_upload/v1/upload_jobs\"\n", "        payload = {\n", "            \"file\": file_path,\n", "            \"created_by\": \"<PERSON><PERSON>\",\n", "            \"user_id\": 14,\n", "            \"is_auto_po\": True,\n", "            \"upload_type_id\": upload_type,\n", "            \"content_type\": \"text/csv\",\n", "            \"params\": {\"large_upload\": True},\n", "        }\n", "        response = requests.post(url, json=payload)\n", "        print(response.status_code, response.json())\n", "        return response.status_code, response.json()\n", "    except Exception as e:\n", "        print(\"error in bulk upload api\", e)\n", "        return 404, {}\n", "\n", "\n", "def pushD<PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "    df,\n", "    tableName,\n", "    description=None,\n", "    primaryKeys=[\"insert_ds_ist\"],\n", "    load_type=\"upsert\",\n", "    environ=\"playground\",\n", "    partition_key=[],\n", "):\n", "    from datetime import datetime, timezone, timedelta\n", "    import time\n", "    import pandas as pd\n", "\n", "    utc_now = datetime.now(timezone.utc)\n", "    ist_now = utc_now + <PERSON><PERSON><PERSON>(hours=5, minutes=30)\n", "    formatted_date = ist_now.strftime(\"%Y-%m-%d\")\n", "    df[\"insert_ds_ist\"] = formatted_date\n", "    df[\"updated_at\"] = pd.to_datetime(utc_now)\n", "    tableStructure = {\n", "        \"item_id\": \"integer\",\n", "        \"city_name\": \"varchar\",\n", "        \"backend_facility_id\": \"real\",\n", "        \"frontend_facility_id\": \"real\",\n", "        \"master_assortment_substate\": \"varchar\",\n", "        \"assortment_type\": \"varchar\",\n", "        \"substate_reason_type\": \"varchar\",\n", "        \"request_reason_type\": \"varchar\",\n", "        \"uid\": \"integer\",\n", "        \"response\": \"varchar\",\n", "        \"status_code\": \"integer\",\n", "        \"source\": \"varchar\",\n", "        \"insert_ds_ist\": \"varchar\",\n", "        \"updated_at\": \"timestamp(6)\",\n", "    }\n", "    column_dtypes = [\n", "        {\n", "            \"name\": col,\n", "            \"type\": str(df[col].dtype)\n", "            .replace(\"int64\", \"integer\")\n", "            .replace(\"float64\", \"real\")\n", "            .replace(\"str\", \"varchar\")\n", "            .replace(\"datetime64[ns]\", \"timestamp(6)\")\n", "            .replace(\"object\", \"varchar\")\n", "            .replace(\"bool\", \"boolean\")\n", "            .replace(\"datetime64[ns, UTC]\", \"timestamp(6)\"),\n", "            \"description\": col,\n", "        }\n", "        for col in df.columns\n", "    ]\n", "\n", "    for val in column_dtypes:\n", "        if val[\"name\"] in tableStructure:\n", "            val[\"type\"] = tableStructure[val[\"name\"]]\n", "\n", "        if val[\"type\"] == \"integer\":\n", "            df[val[\"name\"]] = pd.to_numeric(df[val[\"name\"]], errors=\"coerce\")\n", "        elif val[\"type\"] == \"real\":\n", "            df[val[\"name\"]] = pd.to_numeric(df[val[\"name\"]], errors=\"coerce\")\n", "        elif val[\"type\"] == \"varchar\":\n", "            df[val[\"name\"]] = df[val[\"name\"]].fillna(\"\").astype(\"str\")\n", "\n", "    kwargs = {\n", "        \"schema_name\": environ,\n", "        \"table_name\": tableN<PERSON>,\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": primary<PERSON><PERSON><PERSON>,\n", "        \"load_type\": load_type,\n", "        \"table_description\": (description if description else tableName),\n", "    }\n", "\n", "    if len(partition_key) > 0:\n", "        kwargs[\"partition_key\"] = partition_key\n", "\n", "    import pencilbox as pb\n", "\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            pb.to_trino(data_obj=df, **kwargs)\n", "            return\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error pushDfToTrino (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to pushDfToTrino after {max_retries} attempts: {e}\")\n", "                return\n", "\n", "\n", "def processFile(input_df, chunk_size=20000):\n", "    if input_df.shape[0] == 0:\n", "        print(\"nothing to process, returning\")\n", "        return\n", "    elif input_df.shape[0] < chunk_size:\n", "        list_df = [input_df]\n", "    else:\n", "        list_df = [input_df[i : i + chunk_size] for i in range(0, input_df.shape[0], chunk_size)]\n", "\n", "    responses = []\n", "    df_logs = pd.DataFrame()\n", "    for df in list_df:\n", "        uid = int(time.time())\n", "        local_file_path = f\"product_team_assortment_request_upload_bot_{uid}.xlsx\"\n", "        df.to_excel(local_file_path, index=False, engine=\"openpyxl\")\n", "        file_path = f\"assortment/{local_file_path}\"\n", "        secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "        bucket_name = \"retail-bulk-upload\"\n", "        aws_key = secrets.get(\"aws_key\")\n", "        aws_secret = secrets.get(\"aws_secret\")\n", "        session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "        s3 = session.resource(\"s3\")\n", "        bucket_obj = s3.Bucket(bucket_name)\n", "        bucket_obj.upload_file(local_file_path, file_path)\n", "        status_code, responseJson = upload_to_bu(file_path, 100)\n", "        os.remove(local_file_path)\n", "        df[\"uid\"] = uid\n", "        responses.append(responseJson)\n", "        df[\"response\"] = json.dumps(responseJson)\n", "        df[\"status_code\"] = status_code\n", "        df_logs = pd.concat([df_logs, df])\n", "        time.sleep(2)\n", "    df_logs[\"source\"] = \"povms_assortment_rationalisation_etl_retail_bot_bulk_upload\"\n", "    pushDfToTrino(\n", "        df_logs,\n", "        logTable,\n", "        description=\"manual upload via dag log table\",\n", "        load_type=\"append\",\n", "        environ=ENVIRON,\n", "        partition_key=[\"insert_ds_ist\"],\n", "    )\n", "    return responses"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config_sheet = \"dag_configurations\"\n", "try:\n", "    config_df = readFromSheetsWithRetry(\n", "        spreadSheetID, config_sheet, raiseExceptionFlag=False, mode=EXECUTION_MODE\n", "    )\n", "    if config_df.shape[0] > 0 and \"param\" in config_df.columns:\n", "        MAX_RECORDS_PER_RUN = int(\n", "            config_df[config_df[\"param\"] == \"MAX_RECORDS_PER_RUN\"][\"value\"].values[0]\n", "        )\n", "    else:\n", "        MAX_RECORDS_PER_RUN = 80000\n", "except Exception as e:\n", "    MAX_RECORDS_PER_RUN = 80000"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fetchOpenIFSRRequests(lookback=ifsr_lookback):\n", "    query = f\"\"\"\n", "    SELECT\n", "  distinct facility_id as frontend_facility_id,\n", "  item_id\n", "FROM\n", "  rpc.item_facility_state_request\n", "WHERE\n", "  insert_ds_ist >= CAST (\n", "    CURRENT_DATE - interval '{lookback}' DAY AS varchar\n", "  )\n", "  AND active = 1\n", "  AND state IN (1, 3)\n", "  AND status = 'OPEN'\n", "  and lake_active_record\n", "    \"\"\"\n", "    return fetchDataFromDB(query)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df = pd.concat([step1_df, step2_df, step3_df, step4_df])\n", "print(final_df.shape, step1_df.shape, step2_df.shape, step3_df.shape, step4_df.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_ifsr_requests_df = fetchOpenIFSRRequests()\n", "open_ifsr_requests_df.to_csv(\"open_ifsr_requests_df.csv\", index=False)\n", "open_ifsr_requests_df = open_ifsr_requests_df[[\"frontend_facility_id\", \"item_id\"]].drop_duplicates()\n", "open_ifsr_requests_df[\"open_ifsr_flag\"] = True\n", "final_df = final_df.merge(open_ifsr_requests_df, on=[\"frontend_facility_id\", \"item_id\"], how=\"left\")\n", "final_df = final_df[pd.isna(final_df[\"open_ifsr_flag\"])]\n", "final_df.drop(columns=\"open_ifsr_flag\", inplace=True)\n", "print(final_df.shape, open_ifsr_requests_df.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# restricting max file size in one single run\n", "if final_df.shape[0] > MAX_RECORDS_PER_RUN:\n", "    final_df = final_df.head(MAX_RECORDS_PER_RUN)\n", "\n", "if final_df.shape[0] > 0:\n", "    responses = processFile(final_df)\n", "    json_string = json.dumps(responses)\n", "    print(json_string)\n", "    sendSlackAlert(EXECUTION_MODE, \"hybrid rationalization dag output job ids \" + json_string)\n", "else:\n", "    print(\"nothing to update, doing nothing\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}