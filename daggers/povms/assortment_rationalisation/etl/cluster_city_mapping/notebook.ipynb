{"cells": [{"cell_type": "code", "execution_count": null, "id": "9f7d4fd9-50d9-4f19-bf30-48577a63fecf", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "import requests\n", "import json"]}, {"cell_type": "code", "execution_count": null, "id": "e776dd9d-8bef-4462-aaf3-abf6174fed78", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "dced0a89-f190-4a36-be7a-b553170b3e1d", "metadata": {}, "outputs": [], "source": ["def cluster_city_map():\n", "    sql = f\"\"\"select \n", "    distinct om.city_id,\n", "    cl.name as city_name, \n", "    cm.cluster_id, \n", "    cluster_name,\n", "    cs.name as state_name\n", "from \n", "    po.physical_facility_outlet_mapping om\n", "inner join \n", "    retail.console_location cl on cl.id = om.city_id and cl.lake_active_record\n", "    and om.ars_active = 1 and om.active = 1\n", "left join \n", "    rpc.ams_city_cluster_mapping cm on cm.city_id = om.city_id\n", "    and cm.active and cm.lake_active_record\n", "left join \n", "    rpc.ams_cluster c on c.cluster_id = cm.cluster_id and c.lake_active_record\n", "LEFT JOIN retail.console_state cs on cs.id = cl.state_id and cs.lake_active_record\n", "    \n", "    where facility_id in (select facility_id from lake_retail.console_outlet where business_type_id = 7)\n", "-- and outlet_name not like '%%Draft%%'\n", "and outlet_name not like '%%Test Store%%'\n", "and outlet_name not like '%%Dummy%%' \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "3ae061ed-a38c-4c17-9948-6c221e38667e", "metadata": {}, "outputs": [], "source": ["cluster_city_map_df = cluster_city_map()"]}, {"cell_type": "code", "execution_count": null, "id": "d9979341-22b5-4d86-b9dc-09383fcd7ae3", "metadata": {}, "outputs": [], "source": ["# cluster_city_map_df[cluster_city_map_df['city_id'].isin([321,322,324,325,326])]"]}, {"cell_type": "code", "execution_count": null, "id": "185d9313-cf60-4bed-87f0-a1db21bb997b", "metadata": {}, "outputs": [], "source": ["# cluster_id count against each city_id\n", "cluster_city_map_df2 = (\n", "    cluster_city_map_df.groupby([\"city_id\"])\n", "    .agg({\"cluster_id\": \"count\"})\n", "    .reset_index()\n", "    .rename(columns={\"cluster_id\": \"count_clusters\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3221f9b9-c976-49db-9e49-02eb7f4aeb6c", "metadata": {}, "outputs": [], "source": ["# each city shiuld have 3 clusters mapped, in case it is not select those in missing_mapping\n", "missing_mapping = cluster_city_map_df2[cluster_city_map_df2[\"count_clusters\"] < 3]\n", "missing_mapping = missing_mapping.merge(\n", "    cluster_city_map_df[[\"city_id\", \"state_name\", \"city_name\"]], how=\"left\", on=\"city_id\"\n", ")\n", "missing_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "5cb2ea9d-be88-423b-9ef3-6a9af1ebec89", "metadata": {}, "outputs": [], "source": ["## adding city name as one cluster\n", "new_cluster_df = missing_mapping[[\"city_id\", \"city_name\"]]\n", "new_cluster_df = new_cluster_df.copy()\n", "new_cluster_df.rename(columns={\"city_id\": \"cluster_id\", \"city_name\": \"cluster_name\"}, inplace=True)\n", "new_cluster_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "653c9ee5-6ea9-494d-882d-5fa8b21cb1a9", "metadata": {}, "outputs": [], "source": ["## adding any missing new cluster\n", "cluster_city_map_df3 = cluster_city_map_df[[\"cluster_id\", \"cluster_name\", \"state_name\"]]\n", "cluster_city_map_df3 = cluster_city_map_df3[\n", "    (cluster_city_map_df3[\"cluster_id\"] > 15000) | (cluster_city_map_df3[\"cluster_id\"].isna())\n", "]\n", "cluster_city_map_df3 = cluster_city_map_df3.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "7daf09d6-c844-43ac-bdf6-daa6b0e197b3", "metadata": {}, "outputs": [], "source": ["df = cluster_city_map_df3[[\"cluster_name\", \"state_name\"]]\n", "df = df.copy()\n", "df.loc[:, \"state_name\"] = (\n", "    df[\"state_name\"].str.replace(r\"\\s*\\band\\b\\s*\", \" \", regex=True).str.strip()\n", ")\n", "df = df[df[\"cluster_name\"] != \"NCR\"]\n", "state_mapping = {\n", "    \"Andhra Pradesh\": \"Andhra_Telangana\",\n", "    \"Telangana\": \"Andhra_Telangana\",\n", "    \"Haryana\": \"Punjab_Haryana\",\n", "    \"Punjab\": \"Punjab_Haryana\",\n", "    \"Chandigarh\": \"Punjab_Haryana\",\n", "}\n", "\n", "df[\"state_name\"] = df[\"state_name\"].replace(state_mapping)\n", "df = df.drop_duplicates()\n", "df[\"cluster_name\"] = df[\"state_name\"].str.replace(\" \", \"_\")"]}, {"cell_type": "code", "execution_count": null, "id": "ee8b971f-ed00-4597-9b42-2b63348d8b08", "metadata": {}, "outputs": [], "source": ["df = df.drop_duplicates()\n", "df"]}, {"cell_type": "code", "execution_count": null, "id": "55b3360d-d194-4643-809f-2e0e3813ce71", "metadata": {}, "outputs": [], "source": ["# df.loc[len(df)] = [ 'Himachal', 'Himachal']"]}, {"cell_type": "code", "execution_count": null, "id": "c4b7b17c-6693-420c-816f-16c2c37098d2", "metadata": {}, "outputs": [], "source": ["df = df.merge(cluster_city_map_df3[[\"cluster_id\", \"cluster_name\"]], how=\"left\", on=[\"cluster_name\"])"]}, {"cell_type": "code", "execution_count": null, "id": "516989d7-1519-4aca-8f59-a3c5e3069888", "metadata": {}, "outputs": [], "source": ["df = df.drop_duplicates()\n", "df"]}, {"cell_type": "code", "execution_count": null, "id": "ad9f8005-26a0-468e-98e8-cc7d171da14b", "metadata": {}, "outputs": [], "source": ["df[\"cluster_id\"] = df[\"cluster_id\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "5a920fa9-0ac7-4fe6-9424-8b952d9afaaa", "metadata": {}, "outputs": [], "source": ["df = df.astype({\"cluster_id\": \"int\"})"]}, {"cell_type": "code", "execution_count": null, "id": "db746900-793e-443b-b60a-8f4265ca0fa8", "metadata": {}, "outputs": [], "source": ["df2 = df.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "2bf83fd9-b41b-4783-bf02-e9a78f060825", "metadata": {}, "outputs": [], "source": ["# Identify rows where 'cluster_id' is missing\n", "missing_ids = df[\"cluster_id\"] == 0"]}, {"cell_type": "code", "execution_count": null, "id": "32e3a7fe-e0d8-425e-944f-5f6c759453a5", "metadata": {}, "outputs": [], "source": ["missing_ids"]}, {"cell_type": "code", "execution_count": null, "id": "43b2a692-8554-4f69-bbd4-e38c261efadf", "metadata": {}, "outputs": [], "source": ["# Assign new 'cluster_id' to missing entries\n", "if missing_ids.any():\n", "    max_id = df[\"cluster_id\"].max() if pd.notnull(df[\"cluster_id\"]).any() else 0\n", "    new_ids = range(max_id + 1, max_id + 1 + missing_ids.sum())\n", "    df2.loc[missing_ids, \"cluster_id\"] = new_ids"]}, {"cell_type": "code", "execution_count": null, "id": "ea56af0e-67b4-4188-9b2c-a245b911b628", "metadata": {}, "outputs": [], "source": ["df2 = df2.merge(df[[\"cluster_name\", \"cluster_id\"]], how=\"left\", on=\"cluster_name\")"]}, {"cell_type": "code", "execution_count": null, "id": "1f039e7a-2b65-4ca3-bdc8-e2ccc7eea0f5", "metadata": {}, "outputs": [], "source": ["df = df2.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "4d276f32-c9c3-402b-839c-698240c9d3d9", "metadata": {}, "outputs": [], "source": ["df2 = df2[df2[\"cluster_id_y\"] == 0]"]}, {"cell_type": "code", "execution_count": null, "id": "b3acd969-b2c7-4b6c-a7f3-5bf2571dfc27", "metadata": {}, "outputs": [], "source": ["df2 = df2.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "e0cbaf75-5d2c-458f-bd10-3d8f06fe68bb", "metadata": {}, "outputs": [], "source": ["df2.rename(columns={\"cluster_id_x\": \"cluster_id\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "0a2467e6-ff2d-4d53-89a9-df9074ef81ab", "metadata": {}, "outputs": [], "source": ["df2 = df2[[\"cluster_id\", \"cluster_name\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "4d344267-3dc4-4b20-a812-8c9e1a416a00", "metadata": {}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "id": "9ac7206d-9ddf-4e2a-a6da-3f3f9ab45b7c", "metadata": {}, "outputs": [], "source": ["new_cluster_df = new_cluster_df.append(df2)"]}, {"cell_type": "code", "execution_count": null, "id": "33156dcf-38ea-438e-9aec-459119ac0601", "metadata": {}, "outputs": [], "source": ["# # Append df2 and new_cluster_df\n", "# combined_df = pd.concat([df2, new_cluster_df], ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "26375b8d-94ce-49ba-98cb-f19800e8be26", "metadata": {}, "outputs": [], "source": ["new_cluster_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6b5f5740-2568-45cd-a152-1063d1f451e7", "metadata": {}, "outputs": [], "source": ["df3 = missing_mapping[[\"city_id\", \"state_name\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "b9f04db8-152a-4145-89b2-4266a70a3b0e", "metadata": {}, "outputs": [], "source": ["# df['state_name'] = np.where(df['state_name'].isin(['Andhra Pradesh','Telangana']),'Andhra_Telangana',np.where(df['state_name'].isin(['Haryana','Punjab']), 'Punjab_Haryana',df['state_name']))"]}, {"cell_type": "code", "execution_count": null, "id": "103af23f-4632-482e-9819-098289852881", "metadata": {}, "outputs": [], "source": ["df3.loc[:, \"state_name\"] = (\n", "    df3[\"state_name\"].str.replace(r\"\\s*\\band\\b\\s*\", \" \", regex=True).str.strip()\n", ")\n", "state_mapping = {\n", "    \"Andhra Pradesh\": \"Andhra_Telangana\",\n", "    \"Telangana\": \"Andhra_Telangana\",\n", "    \"Haryana\": \"Punjab_Haryana\",\n", "    \"Punjab\": \"Punjab_Haryana\",\n", "    \"Chandigarh\": \"Punjab_Haryana\",\n", "}\n", "\n", "df3[\"state_name\"] = df3[\"state_name\"].replace(state_mapping)\n", "df3 = df3.drop_duplicates()\n", "df3[\"cluster_name\"] = df3[\"state_name\"].str.replace(\" \", \"_\")"]}, {"cell_type": "code", "execution_count": null, "id": "ca76b79d-18f2-4adb-b351-04c88e4856a9", "metadata": {}, "outputs": [], "source": ["df3 = df3[[\"city_id\", \"cluster_name\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "781fafec-70b1-4847-a2a7-26e61932ce8d", "metadata": {}, "outputs": [], "source": ["df = df[[\"cluster_name\", \"cluster_id_x\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "00558663-dbbb-47a6-bd3a-39ebd356b19c", "metadata": {}, "outputs": [], "source": ["df3 = df3.merge(df, on=[\"cluster_name\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "8b979105-a4d7-432c-80ce-ffbcab70bf93", "metadata": {}, "outputs": [], "source": ["df3.drop(columns={\"cluster_name\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "ee12f259-1119-4abc-bc20-88115e770c0b", "metadata": {}, "outputs": [], "source": ["df3.rename(columns={\"cluster_id_x\": \"cluster_id\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "fb66df46-aafe-4969-98f2-7a408f50377e", "metadata": {}, "outputs": [], "source": ["df4 = df3.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "61f6dcf5-7c1e-4e3f-a9b5-57a9ba222f2b", "metadata": {}, "outputs": [], "source": ["df4[\"cluster_id\"] = 15000"]}, {"cell_type": "code", "execution_count": null, "id": "676dd532-0a83-4f08-9ba3-1b68b058a1ea", "metadata": {}, "outputs": [], "source": ["df5 = df4.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "9e7e0b4a-9e34-4295-8453-8975b6331d15", "metadata": {}, "outputs": [], "source": ["df5[\"cluster_id\"] = df5[\"city_id\"]"]}, {"cell_type": "code", "execution_count": null, "id": "7d23d106-b2b0-45f4-aeab-9dd82d9c937a", "metadata": {}, "outputs": [], "source": ["city_cluster_mapping = pd.concat([df3, df4, df5], ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "832d92c3-bb29-49c2-a469-a23fdec13069", "metadata": {}, "outputs": [], "source": ["city_cluster_mapping[\"active\"] = True"]}, {"cell_type": "code", "execution_count": null, "id": "2259fc0b-a1b9-42d6-a714-d21c02733d3b", "metadata": {}, "outputs": [], "source": ["city_cluster_mapping"]}, {"cell_type": "code", "execution_count": null, "id": "65ee6722-0ca2-47db-a1c4-a87a45e5d79e", "metadata": {}, "outputs": [], "source": ["new_cluster_df"]}, {"cell_type": "code", "execution_count": null, "id": "1705cf64-6fc2-480d-9ffa-7370ed33d183", "metadata": {}, "outputs": [], "source": ["def post_dataframes_to_api(ams_cluster_df: pd.DataFrame, ams_city_cluster_mapping_df: pd.DataFrame):\n", "    if ams_cluster_df.empty:\n", "        print(\"ams_cluster_df is empty. Skipping AMS cluster POST request.\")\n", "    else:\n", "        ams_cluster_payload = {\"data\": ams_cluster_df.to_dict(orient=\"records\")}\n", "        ams_cluster_api_url = \"https://retail-internal.grofer.io/rpc/v1/ams-cluster-mappings/\"\n", "        headers = {\"Content-Type\": \"application/json\"}\n", "        response1 = requests.post(\n", "            ams_cluster_api_url, headers=headers, data=json.dumps(ams_cluster_payload)\n", "        )\n", "        if response1.status_code == 200:\n", "            print(\"AMS Cluster data posted successfully.\")\n", "        else:\n", "            print(f\"Failed to post AMS Cluster data. Status code: {response1.status_code}\")\n", "\n", "    if ams_city_cluster_mapping_df.empty:\n", "        print(\n", "            \"ams_city_cluster_mapping_df is empty. Skipping AMS city cluster mapping POST request.\"\n", "        )\n", "    else:\n", "        ams_city_cluster_mapping_payload = {\n", "            \"data\": ams_city_cluster_mapping_df.to_dict(orient=\"records\")\n", "        }\n", "        ams_city_cluster_mapping_api_url = (\n", "            \"https://retail-internal.grofer.io/rpc/v1/ams-city-cluster-mappings/\"\n", "        )\n", "        response2 = requests.post(\n", "            ams_city_cluster_mapping_api_url,\n", "            headers=headers,\n", "            data=json.dumps(ams_city_cluster_mapping_payload),\n", "        )\n", "        if response2.status_code == 200:\n", "            print(\"AMS City Cluster Mapping data posted successfully.\")\n", "        else:\n", "            print(\n", "                f\"Failed to post AMS City Cluster Mapping data. Status code: {response2.status_code}\"\n", "            )\n", "\n", "    return \"Success\""]}, {"cell_type": "code", "execution_count": null, "id": "c2b77cc0-f963-4b61-96ec-6360e43a65cd", "metadata": {}, "outputs": [], "source": ["post_dataframes_to_api(new_cluster_df, city_cluster_mapping)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}