{"cells": [{"cell_type": "code", "execution_count": null, "id": "bb314726-1f10-42b8-87db-f205f57ae75c", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "69fba3bd-10a3-424d-ba2f-cf4858436c7c", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "8ed9eb24-9d2b-4d20-9774-705ffa06cc19", "metadata": {}, "outputs": [], "source": ["def city_ptype_rank(city_id):\n", "    sql = f\"\"\"With fo as (select facility_id, outlet_id, city_id, outlet_name from lake_po.physical_facility_outlet_mapping where active=1 and ars_active=1\n", "        and facility_id in (select facility_id from lake_retail.console_outlet where business_type_id = 7) \n", "        and city_id = {city_id}\n", "   group by 1,2,3,4\n", "),\n", "\n", "base as (select city_id, l2, product_type,y.item_id, sum(multiplier*product_quantity) as quantity_sold, \n", "rank() over(partition by city_id,l2, product_type order by \n", "sum(multiplier*product_quantity) desc) as item_ptype_rank\n", "from dwh.fact_sales_order_item_details x\n", "inner join dwh.dim_item_product_offer_mapping y on x.product_id = y.product_id\n", "inner join rpc.item_category_details icd on icd.item_id = y.item_id\n", "inner join fo on fo.outlet_id = x.outlet_id\n", "where cart_checkout_ts_ist > current_date- interval '60' day\n", "and order_current_status = 'DELIVERED' \n", "and order_create_dt_ist > current_date- interval '60' day\n", "group by 1,2,3,4)\n", "\n", "select fo.city_id,l2, product_type, item_id from base b inner join fo on fo.city_id = b.city_id where item_ptype_rank <=3\n", "GROUP BY 1,2,3,4\n", "\"\"\"\n", "    return pd.read_sql_query(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "0d243b7a-b798-4aa4-bffb-0527a39ce777", "metadata": {}, "outputs": [], "source": ["active_outlets = f\"\"\"\n", "WITH fo AS\n", "      (SELECT om.facility_id,\n", "              om.outlet_id AS outlet_id,\n", "              om.city_id,\n", "              om.outlet_name AS outlet_name\n", "      FROM po.physical_facility_outlet_mapping om\n", "      INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "      AND rco.business_type_id IN (7)\n", "      AND rco.company_type_id NOT IN (771)\n", "      AND rco.lake_active_record\n", "      INNER JOIN dwh.dim_merchant_outlet_facility_mapping mo_map ON mo_map.facility_id = om.facility_id\n", "      AND is_current\n", "      AND is_current_mapping_active\n", "      AND is_backend_merchant_active\n", "      AND is_frontend_merchant_active\n", "      WHERE om.active = 1\n", "        AND om.ars_active = 1\n", "        AND om.lake_active_record\n", "        AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "        AND om.outlet_name NOT LIKE '%%Draft%%'\n", "        AND om.outlet_name NOT LIKE '%%Test Store%%'\n", "        AND om.outlet_name NOT LIKE '%%Dummy%%'\n", "        AND om.facility_id != 273\n", "        AND om.outlet_id IN\n", "          (SELECT DISTINCT outlet_id\n", "            FROM po.bulk_facility_outlet_mapping\n", "            WHERE active\n", "              AND lake_active_record) )\n", "    SELECT *\n", "    FROM fo\"\"\"\n", "active_outlets_raw = pd.read_sql_query(sql=active_outlets, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "95e18144-a48f-4d37-84c4-b47adba8a94e", "metadata": {}, "outputs": [], "source": ["active_outlets_raw.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bed65b47-8e28-4693-878b-db39f153fb64", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import time\n", "\n", "\n", "def city_ptype_rank_by_distinct_city(active_outlets_raw):\n", "    \"\"\"\n", "    Processes city_ptype_rank for distinct city_id values from active_outlets_raw.\n", "\n", "    Args:\n", "    - active_outlets_raw (pd.DataFrame): DataFrame containing 'city_id' and 'facility_id'.\n", "\n", "    Returns:\n", "    - city_ptype_rank_combined (pd.DataFrame): Combined city_ptype_rank results for all distinct city_ids.\n", "    \"\"\"\n", "    # Extract distinct city_id values\n", "    distinct_cities = active_outlets_raw[\"city_id\"].unique()\n", "    total_cities = len(distinct_cities)\n", "    print(f\"Found {total_cities} distinct city IDs.\")\n", "\n", "    # Initialize an empty DataFrame to store results\n", "    city_ptype_rank_combined = pd.DataFrame()\n", "\n", "    # Iterate over each distinct city_id\n", "    for i, city_id in enumerate(distinct_cities, start=1):\n", "        print(f\"\\n🔄 Processing city_ptype_rank for city_id: {city_id} ({i}/{total_cities})\")\n", "\n", "        # Record start time\n", "        start_time = time.time()\n", "\n", "        # Call the city_ptype_rank function for the current city_id\n", "        city_ptype_rank_raw = city_ptype_rank(city_id)\n", "\n", "        # Append results to the combined DataFrame\n", "        city_ptype_rank_combined = pd.concat(\n", "            [city_ptype_rank_combined, city_ptype_rank_raw], ignore_index=True\n", "        )\n", "\n", "        # Calculate elapsed time\n", "        elapsed_time = time.time() - start_time\n", "        remaining_cities = total_cities - i\n", "\n", "        print(\n", "            f\"✅ Completed city_id: {city_id} in {elapsed_time:.2f} seconds. {remaining_cities} cities left.\"\n", "        )\n", "\n", "    print(\"\\n✅ All distinct city IDs processed for city_ptype_rank!\")\n", "    return city_ptype_rank_combined"]}, {"cell_type": "code", "execution_count": null, "id": "3239c69b-bb71-4650-b43d-5c5549304e01", "metadata": {}, "outputs": [], "source": ["city_ptype_rank_combined_raw = city_ptype_rank_by_distinct_city(active_outlets_raw)"]}, {"cell_type": "code", "execution_count": null, "id": "533b5b89-8512-4227-99e8-ade3325d6696", "metadata": {}, "outputs": [], "source": ["city_ptype_rank_combined_raw.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2f1de926-135c-4b06-bea9-572681049a83", "metadata": {}, "outputs": [], "source": ["EXECUTION_MODE = \"PROD\"\n", "logTable = \"city_ptype_top_items\"\n", "df_logs = city_ptype_rank_combined_raw.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "3c119b56-1ffa-4722-ab45-44bde60d028b", "metadata": {}, "outputs": [], "source": ["df_logs = city_ptype_rank_combined_raw.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "be48bb6a-fba8-4662-a20a-1638fee039f6", "metadata": {}, "outputs": [], "source": ["df_logs.head()"]}, {"cell_type": "code", "execution_count": null, "id": "fd14d542-683c-4add-a6f5-9c0887a6969f", "metadata": {}, "outputs": [], "source": ["columns_to_convert = [\"city_id\", \"item_id\"]"]}, {"cell_type": "code", "execution_count": null, "id": "3522fbe7-bd93-4028-8ec3-bb544a367c4b", "metadata": {}, "outputs": [], "source": ["df_logs[columns_to_convert] = df_logs[columns_to_convert].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "63037f3e-8974-4cd0-9918-4bfd8d64372c", "metadata": {}, "outputs": [], "source": ["df_logs.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "3823068c-3e0b-40cb-92d1-2e4d73d5b4f7", "metadata": {}, "outputs": [], "source": ["def pushD<PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "    df,\n", "    tableName,\n", "    description=None,\n", "    primaryKeys=[\"insert_ds_ist\"],\n", "    load_type=\"upsert\",\n", "    environ=\"supply_etls\",\n", "    partition_key=[],\n", "):\n", "    from datetime import datetime, timezone, timedelta\n", "    import time\n", "    import pandas as pd\n", "\n", "    utc_now = datetime.now(timezone.utc)\n", "    ist_now = utc_now + <PERSON><PERSON><PERSON>(hours=5, minutes=30)\n", "    formatted_date = ist_now.strftime(\"%Y-%m-%d\")\n", "    df[\"insert_ds_ist\"] = formatted_date\n", "    df[\"updated_at\"] = pd.to_datetime(utc_now)\n", "    column_dtypes = [\n", "        {\n", "            \"name\": col,\n", "            \"type\": str(df[col].dtype)\n", "            .replace(\"int64\", \"integer\")\n", "            .replace(\"float64\", \"real\")\n", "            .replace(\"str\", \"varchar\")\n", "            .replace(\"datetime64[ns]\", \"timestamp(6)\")\n", "            .replace(\"object\", \"varchar\")\n", "            .replace(\"bool\", \"boolean\")\n", "            .replace(\"datetime64[ns, UTC]\", \"timestamp(6)\"),\n", "            \"description\": col,\n", "        }\n", "        for col in df.columns\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": environ,\n", "        \"table_name\": tableN<PERSON>,\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": primary<PERSON><PERSON><PERSON>,\n", "        \"load_type\": load_type,\n", "        \"table_description\": (description if description else tableName),\n", "    }\n", "\n", "    if len(partition_key) > 0:\n", "        kwargs[\"partition_key\"] = partition_key\n", "\n", "    import pencilbox as pb\n", "\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            pb.to_trino(data_obj=df, **kwargs)\n", "            return\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error pushDfToTrino (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to pushDfToTrino after {max_retries} attempts: {e}\")\n", "                return"]}, {"cell_type": "code", "execution_count": null, "id": "afcb7f20-ca5a-47ea-bf5e-622f26fde7dd", "metadata": {}, "outputs": [], "source": ["pushDfToTrino(\n", "    df_logs,\n", "    logTable,\n", "    description=\"longtail store replication data log table\",\n", "    load_type=\"append\",\n", "    environ=\"supply_etls\",\n", "    partition_key=[\"insert_ds_ist\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "06ee61df-2ce0-4dcd-bcb5-528be3e20f4d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}