{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip uninstall -y openpyxl"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install openpyxl==3.1.5"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import requests\n", "import datetime\n", "from pytz import timezone\n", "import boto3\n", "import os\n", "import sys\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import json\n", "import openpyxl"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["slack_alert_channel = \"assortment-upload-loggers\"\n", "EXECUTION_MODE = \"PROD\"\n", "spreadSheetId = \"1mGuEUevGwt7MPz869i3vOBD16cdLNuUmjDUbuv_oNGQ\"\n", "sheetName = \"input\"\n", "logTable = \"assortment_bulk_upload_dag_log_v2\"\n", "CHUNK = 2000\n", "configSheetName = \"config\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["req_columns = [\n", "    \"item_id\",\n", "    \"city_name\",\n", "    \"backend_facility_id\",\n", "    \"frontend_facility_id\",\n", "    \"master_assortment_substate\",\n", "    \"assortment_type\",\n", "    \"substate_reason_type\",\n", "    \"request_reason_type\",\n", "]\n", "column_types = {\n", "    \"item_id\": \"int\",\n", "    \"city_name\": \"str\",\n", "    \"backend_facility_id\": \"int\",\n", "    \"frontend_facility_id\": \"int\",\n", "    \"master_assortment_substate\": \"str\",\n", "    \"assortment_type\": \"str\",\n", "    \"substate_reason_type\": \"str\",\n", "    \"request_reason_type\": \"str\",\n", "}\n", "\n", "\n", "def convert_columns(df, columnTypeJson=column_types):\n", "    # Iterate over each column in the dataframe\n", "    for col in df.columns:\n", "        if col in columnTypeJson and columnTypeJson[col] == \"str\":\n", "            df[col] = df[col].astype(str)\n", "        elif col in columnTypeJson and columnTypeJson[col] == \"int\":\n", "            df[col] = pd.to_numeric(df[col])\n", "            df[col] = df[col].apply(lambda x: int(x) if not np.isnan(x) else x)\n", "    return df\n", "\n", "\n", "def check_required_columns(df, req_columns):\n", "    # Get the current columns of the DataFrame\n", "    current_columns = set(df.columns)\n", "    # Convert required columns to a set\n", "    required = set(req_columns)\n", "    # Check if all required columns are present\n", "    return required.issubset(current_columns)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def sendSlackAlert(execution_mode, text):\n", "    if execution_mode == \"PROD\":\n", "        try:\n", "            pb.send_slack_message(channel=slack_alert_channel, text=text)\n", "        except Exception as e:\n", "            print(f\"Error sending Slack message: {e}, {text}\")\n", "        # handle the error further if needed\n", "    else:\n", "        print(\"local environment, just printing to console\")\n", "        print(text)\n", "\n", "\n", "def upload_to_bu(file_path, upload_type):\n", "    try:\n", "        url = \"https://retail-internal.grofer.io/bulk_upload/v1/upload_jobs\"\n", "        payload = {\n", "            \"file\": file_path,\n", "            \"created_by\": \"<PERSON><PERSON>\",\n", "            \"user_id\": 14,\n", "            \"is_auto_po\": True,\n", "            \"upload_type_id\": upload_type,\n", "            \"content_type\": \"text/csv\",\n", "            \"params\": {\"large_upload\": True},\n", "        }\n", "        response = requests.post(url, json=payload)\n", "        return response.status_code, response.json()\n", "    except Exception as e:\n", "        print(\"error in bulk upload api\", e)\n", "        return 404, {}\n", "\n", "\n", "def readFromSheetsWithRetry(spreadSheetId, sheetName, raiseExceptionFlag=True, mode=EXECUTION_MODE):\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            if mode == \"LOCAL\":\n", "                script_dir = \"/home/<USER>/inventory-planning-playbooks/\"\n", "                sys.path.insert(0, script_dir)\n", "                from utils.commonFunc import readSpreadsheet\n", "\n", "                values = readSpreadsheet(spreadSheetId, sheetName)\n", "                df = pd.DataFrame(values[1:], columns=values[0])\n", "                return df\n", "            else:\n", "                df = pb.from_sheets(spreadSheetId, sheetName)\n", "                return df\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error fetching data (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to fetch data after {max_retries} attempts: {e}\")\n", "                if raiseExceptionFlag:\n", "                    raise Exception(\n", "                        \"Failed to read sheets after {} attempts\".format(max_retries)\n", "                    ) from e\n", "                else:\n", "                    sendSlackAlert(\n", "                        mode,\n", "                        \"povms_assortment_rationalisation_etl_retail_bot_bulk_upload dag failed to read sheet\"\n", "                        + spreadSheetId\n", "                        + sheetName,\n", "                    )\n", "                    return pd.DataFrame()\n", "\n", "\n", "def pushD<PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "    df,\n", "    tableName,\n", "    description=None,\n", "    primaryKeys=[\"insert_ds_ist\"],\n", "    load_type=\"upsert\",\n", "    environ=\"playground\",\n", "    partition_key=[],\n", "):\n", "    from datetime import datetime, timezone, timedelta\n", "    import time\n", "    import pandas as pd\n", "\n", "    utc_now = datetime.now(timezone.utc)\n", "    ist_now = utc_now + <PERSON><PERSON><PERSON>(hours=5, minutes=30)\n", "    formatted_date = ist_now.strftime(\"%Y-%m-%d\")\n", "    df[\"insert_ds_ist\"] = formatted_date\n", "    df[\"updated_at\"] = pd.to_datetime(utc_now)\n", "    column_dtypes = [\n", "        {\n", "            \"name\": col,\n", "            \"type\": str(df[col].dtype)\n", "            .replace(\"int64\", \"integer\")\n", "            .replace(\"float64\", \"real\")\n", "            .replace(\"str\", \"varchar\")\n", "            .replace(\"datetime64[ns]\", \"timestamp(6)\")\n", "            .replace(\"object\", \"varchar\")\n", "            .replace(\"bool\", \"boolean\")\n", "            .replace(\"datetime64[ns, UTC]\", \"timestamp(6)\"),\n", "            \"description\": col,\n", "        }\n", "        for col in df.columns\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": environ,\n", "        \"table_name\": tableN<PERSON>,\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": primary<PERSON><PERSON><PERSON>,\n", "        \"load_type\": load_type,\n", "        \"table_description\": (description if description else tableName),\n", "    }\n", "\n", "    if len(partition_key) > 0:\n", "        kwargs[\"partition_key\"] = partition_key\n", "\n", "    import pencilbox as pb\n", "\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            pb.to_trino(data_obj=df, **kwargs)\n", "            return\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error pushDfToTrino (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to pushDfToTrino after {max_retries} attempts: {e}\")\n", "                return\n", "\n", "\n", "def processFile(input_df, chunk_size=CHUNK, time_delay=60):\n", "    if input_df.shape[0] == 0:\n", "        print(\"nothing to process, returning\")\n", "        return\n", "    elif input_df.shape[0] < chunk_size:\n", "        list_df = [input_df]\n", "    else:\n", "        list_df = [input_df[i : i + chunk_size] for i in range(0, input_df.shape[0], chunk_size)]\n", "\n", "    responses = []\n", "    df_logs = pd.DataFrame()\n", "    for df in list_df:\n", "        uid = int(time.time())\n", "        local_file_path = f\"product_team_assortment_request_upload_bot_{uid}.xlsx\"\n", "        df.to_excel(local_file_path, index=False, engine=\"openpyxl\")\n", "        file_path = f\"assortment/{local_file_path}\"\n", "        secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "        bucket_name = \"retail-bulk-upload\"\n", "        aws_key = secrets.get(\"aws_key\")\n", "        aws_secret = secrets.get(\"aws_secret\")\n", "        session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "        s3 = session.resource(\"s3\")\n", "        bucket_obj = s3.Bucket(bucket_name)\n", "        bucket_obj.upload_file(local_file_path, file_path)\n", "        status_code, responseJson = upload_to_bu(file_path, 100)\n", "        os.remove(local_file_path)\n", "        df[\"uid\"] = uid\n", "        responses.append(responseJson)\n", "        df[\"response\"] = json.dumps(responseJson)\n", "        df[\"status_code\"] = status_code\n", "        df_logs = pd.concat([df_logs, df])\n", "        time.sleep(time_delay)\n", "    df_logs[\"source\"] = \"povms_assortment_rationalisation_etl_retail_bot_bulk_upload\"\n", "    pushDfToTrino(\n", "        df_logs,\n", "        logTable,\n", "        description=\"manual upload via dag log table\",\n", "        load_type=\"append\",\n", "        environ=\"supply_etls\",\n", "        partition_key=[\"insert_ds_ist\"],\n", "    )\n", "    return responses\n", "\n", "\n", "def pushToGoogleSheetsWithRetry(df, sheet_id, sheet_name):\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            pb.to_sheets(df, sheet_id, sheet_name)\n", "            return\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error pushing data (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to push data after {max_retries} attempts: {e}\")\n", "                return"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["input_df = readFromSheetsWithRetry(spreadSheetId, sheetName)\n", "print(\"original shape\", input_df.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["input_df.dropna(inplace=True)\n", "print(\"revised shape\", input_df.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["input_df = convert_columns(input_df)\n", "print(\"final_shape\", input_df.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["input_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "    config_df = readFromSheetsWithRetry(\n", "        spreadSheetId, configSheetName, raiseExceptionFlag=False, mode=EXECUTION_MODE\n", "    )\n", "    if config_df.shape[0] > 0 and \"param\" in config_df.columns:\n", "        MAX_RECORDS_PER_RUN = int(\n", "            config_df[config_df[\"param\"] == \"MAX_RECORDS_PER_RUN\"][\"value\"].values[0]\n", "        )\n", "        MAX_RECORDS_PER_REQUEST = int(\n", "            config_df[config_df[\"param\"] == \"MAX_RECORDS_PER_REQUEST\"][\"value\"].values[0]\n", "        )\n", "        TIME_DELAY_PER_REQUEST = int(\n", "            config_df[config_df[\"param\"] == \"TIME_DELAY_PER_REQUEST\"][\"value\"].values[0]\n", "        )\n", "        EXCLUDE_SELLER_ITEMS = int(\n", "            config_df[config_df[\"param\"] == \"EXCLUDE_SELLER_ITEMS\"][\"value\"].values[0]\n", "        )\n", "    else:\n", "        MAX_RECORDS_PER_RUN = 2000\n", "        MAX_RECORDS_PER_REQUEST = 2000\n", "        TIME_DELAY_PER_REQUEST = 60\n", "        EXCLUDE_SELLER_ITEMS = 1\n", "except Exception as e:\n", "    MAX_RECORDS_PER_RUN = 2000\n", "    MAX_RECORDS_PER_REQUEST = 2000\n", "    TIME_DELAY_PER_REQUEST = 60\n", "    EXCLUDE_SELLER_ITEMS = 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fetchDataFromDB(query, max_retries=3, retry_delay=10):\n", "    import pencilbox as pb\n", "    import pandas as pd\n", "    import time\n", "\n", "    con = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            df = pd.read_sql_query(sql=query, con=con)\n", "            return df\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error fetching data (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to fetch data after {max_retries} attempts: {e}\")\n", "                return pd.DataFrame()\n", "\n", "\n", "def get3PSellerItemIDs():\n", "    query = \"\"\"\n", "  select distinct(item_id) from seller.seller_product_mappings\n", "  \"\"\"\n", "    print(query)\n", "    return fetchDataFromDB(query)\n", "\n", "\n", "if EXCLUDE_SELLER_ITEMS == 1:\n", "    seller_items = get3PSellerItemIDs()\n", "    if seller_items.shape[0] > 0:\n", "        seller_items = seller_items[\"item_id\"].tolist()\n", "\n", "    else:\n", "        seller_items = []\n", "else:\n", "    seller_items = []"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["req_columns = [\n", "    \"item_id\",\n", "    \"city_name\",\n", "    \"backend_facility_id\",\n", "    \"frontend_facility_id\",\n", "    \"master_assortment_substate\",\n", "    \"assortment_type\",\n", "    \"substate_reason_type\",\n", "    \"request_reason_type\",\n", "]\n", "flag = check_required_columns(input_df, req_columns)\n", "if not flag or input_df.shape[0] == 0:\n", "    print(\"invalid columns in the google sheet, doing nothing\", input_df.columns)\n", "else:\n", "    if input_df.shape[0] > MAX_RECORDS_PER_RUN:\n", "        print(\"truncating the file to max \", MAX_RECORDS_PER_RUN, input_df.shape[0])\n", "        input_df = input_df.head(MAX_RECORDS_PER_RUN)\n", "\n", "    seller_records_removed = 0\n", "    if input_df.shape[0] > 0 and EXCLUDE_SELLER_ITEMS == 1:\n", "        original_shape = input_df.shape[0]\n", "        input_df = input_df[~pd.to_numeric(input_df[\"item_id\"], errors=\"coerce\").isin(seller_items)]\n", "        seller_records_removed = original_shape - input_df.shape[0]\n", "        print(\n", "            \"excluding seller items from the input file\",\n", "            original_shape,\n", "            input_df.shape[0],\n", "            seller_records_removed,\n", "        )\n", "\n", "    if input_df.shape[0] > 0:\n", "        responses = processFile(\n", "            input_df, chunk_size=MAX_RECORDS_PER_REQUEST, time_delay=TIME_DELAY_PER_REQUEST\n", "        )\n", "        json_string = json.dumps(responses)\n", "        print(json_string)\n", "        if seller_records_removed > 0:\n", "            message_string = (\n", "                \"response of the file upload \"\n", "                + json_string\n", "                + \" seller records removed \"\n", "                + str(seller_records_removed)\n", "            )\n", "        else:\n", "            message_string = \"response of the file upload \" + json_string\n", "        sendSlackAlert(EXECUTION_MODE, message_string)\n", "        print(\"cleaning up the input sheet\")\n", "        pushToGoogleSheetsWithRetry(\n", "            pd.DataFrame({\"response\": [json_string]}), spreadSheetId, sheetName\n", "        )\n", "    else:\n", "        sendSlackAlert(\n", "            EXECUTION_MODE,\n", "            \"no records to process seller records removed \" + str(seller_records_removed),\n", "        )\n", "        pushToGoogleSheetsWithRetry(pd.DataFrame({\"response\": [\"-\"]}), spreadSheetId, sheetName)\n", "        print(\"no records to process\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}