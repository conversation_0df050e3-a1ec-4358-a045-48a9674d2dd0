{"cells": [{"cell_type": "code", "execution_count": null, "id": "0483efec-d2b3-4785-a717-7c63cac9a883", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "fb06e57e-aca7-41f9-9570-7b3da197671b", "metadata": {"tags": []}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "c8fe43d7-c57e-410d-ab0d-4140929fd155", "metadata": {}, "outputs": [], "source": ["def existing_cities():\n", "    sql = f\"\"\"\n", "WITH fo AS (\n", "    SELECT \n", "        facility_id,\n", "        outlet_id,\n", "        city_id,\n", "        outlet_name\n", "    FROM \n", "        po.physical_facility_outlet_mapping om\n", "    WHERE \n", "        active = 1\n", "        AND ars_active = 1\n", "        AND facility_id IN (\n", "            SELECT facility_id\n", "            FROM lake_retail.console_outlet\n", "            WHERE business_type_id = 7\n", "        )\n", "        AND outlet_name NOT LIKE '%%Test%%'\n", "        AND outlet_name NOT LIKE '%%Dummy%%'\n", "    GROUP BY \n", "        facility_id,\n", "        outlet_id,\n", "        city_id,\n", "        outlet_name\n", "),\n", "base AS (\n", "    SELECT \n", "        city_id, \n", "        COUNT(DISTINCT order_id) AS cart_count \n", "    FROM \n", "        dwh.fact_sales_order_details od \n", "    INNER JOIN \n", "        fo ON fo.outlet_id = od.outlet_id \n", "    WHERE \n", "        order_current_status = 'DELIVERED'\n", "        AND is_internal_order = False \n", "        AND cart_checkout_ts_ist BETWEEN current_date - interval '7' day AND current_date\n", "        AND order_create_dt_ist >= current_date - interval '30' day\n", "    GROUP BY \n", "        city_id\n", ")\n", "SELECT \n", "    DISTINCT city_id \n", "FROM \n", "    base \n", "WHERE \n", "    cart_count >= 100\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "e6b2d42e-d172-41b3-800f-4c16edf3fbcd", "metadata": {}, "outputs": [], "source": ["existing_cities_df = existing_cities()"]}, {"cell_type": "code", "execution_count": null, "id": "52a1faee-5368-45ad-87d6-fe10fd1e0859", "metadata": {}, "outputs": [], "source": ["existing_cities_df[\"flag\"] = \"existing_city\""]}, {"cell_type": "code", "execution_count": null, "id": "ae641cb1-7659-42ee-8b24-7e4282382870", "metadata": {}, "outputs": [], "source": ["## bistro store gets dropped and only stores with OB date in next 30 days are included"]}, {"cell_type": "code", "execution_count": null, "id": "4910cc7f-c72e-4fab-a620-24ca502be6ef", "metadata": {}, "outputs": [], "source": ["def upcoming_all_stores():\n", "    sql = f\"\"\"\n", "    WITH fo AS (\n", "    SELECT \n", "        facility_id,\n", "        outlet_id,\n", "        city_id,\n", "        outlet_name\n", "    FROM \n", "        po.physical_facility_outlet_mapping om\n", "    WHERE \n", "        active = 1\n", "        AND ars_active = 1\n", "        AND facility_id IN (\n", "            SELECT facility_id\n", "            FROM lake_retail.console_outlet\n", "            WHERE business_type_id = 7 and company_type_id not in (771) -- bistro store\n", "        )\n", "        and om.outlet_name not like '%%Draft%%'\n", "    and om.outlet_name not like '%%Test Store%%'\n", "    and om.outlet_name not like '%%Dummy%%'\n", "    GROUP BY \n", "        facility_id,\n", "        outlet_id,\n", "        city_id,\n", "        outlet_name\n", ")\n", "    select fo.facility_id,\n", "    fo.city_id,\n", "    fo2.facility_id as sister_facility_id,\n", "    fo2.city_id as sister_city_id\n", "    from supply_etls.e3_new_darkstores nd\n", "    join fo\n", "    on fo.facility_id = nd.facility_id\n", "    left join fo fo2\n", "    on fo2.facility_id = nd.sister_store_facility_id\n", "    where final_ob_date >= current_date\n", "    and final_ob_date <= current_date + interval '30' day\n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "5b3602dc-d3ed-40fb-a26d-e33cea4a62da", "metadata": {}, "outputs": [], "source": ["upcoming_all_stores_df = upcoming_all_stores()"]}, {"cell_type": "code", "execution_count": null, "id": "31da67a9-4178-4023-93bc-169ecda4756a", "metadata": {}, "outputs": [], "source": ["upcoming_all_stores_df2 = upcoming_all_stores_df.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "4b6dbd6c-c653-49c5-a3a0-1ec8591e4590", "metadata": {}, "outputs": [], "source": ["upcoming_all_stores_df2.shape"]}, {"cell_type": "code", "execution_count": null, "id": "2d45c1c1-2b3e-49c9-ad4a-07bbe4796959", "metadata": {}, "outputs": [], "source": ["upcoming_all_stores_df2 = upcoming_all_stores_df2.merge(\n", "    existing_cities_df, how=\"left\", on=\"city_id\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d1540176-1565-4e43-8af5-71b09a68d57a", "metadata": {}, "outputs": [], "source": ["upcoming_all_stores_df2.dropna(subset=[\"sister_facility_id\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "3f8ca912-8b62-43ca-ae9b-452ac3db0a11", "metadata": {}, "outputs": [], "source": ["upcoming_all_stores_df2 = upcoming_all_stores_df2[\n", "    upcoming_all_stores_df2[\"city_id\"] == upcoming_all_stores_df2[\"sister_city_id\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "2d9848cb-207f-4926-b2c5-4460d72dfdbc", "metadata": {}, "outputs": [], "source": ["upcoming_all_stores_df2 = upcoming_all_stores_df2[upcoming_all_stores_df2[\"flag\"].notna()]"]}, {"cell_type": "code", "execution_count": null, "id": "fb9d8185-02b6-4382-bba3-902e6c68ae26", "metadata": {}, "outputs": [], "source": ["upcoming_all_stores_df2.drop(columns=[\"city_id\", \"sister_city_id\", \"flag\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "755ffe6e-edc1-437d-8ee6-e12fd522cc92", "metadata": {}, "outputs": [], "source": ["upcoming_all_stores_df2[\"updated_at\"] = pd.to_datetime(datetime.now() + timedelta(hours=5.5))"]}, {"cell_type": "code", "execution_count": null, "id": "992fe190-1d6f-4aa9-9c19-6fa5ff175203", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"new_store_replication_input\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"Facility id\"},\n", "        {\n", "            \"name\": \"sister_facility_id\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"sister_facility_id\",\n", "        },\n", "        {\n", "            \"name\": \"updated_at\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"Time of updation\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"facility_id\"],\n", "    \"partition_key\": [\"updated_at\"],  # ** Note below points while selecting this\n", "    # \"incremental_key\": \"update_ts\",\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"new store deatils for replication\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "466aaaf2-7f94-44b8-b66d-d95c27c074fd", "metadata": {}, "outputs": [], "source": ["pb.to_trino(data_obj=upcoming_all_stores_df2, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "7a59ae9f-0ff8-4eb1-8299-45c85598cb7a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}