{"cells": [{"cell_type": "code", "execution_count": null, "id": "553a1422-f4cb-4de8-aba0-db9cfdeaec2d", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import numpy as np\n", "import pandas as pd\n", "import math\n", "from datetime import date, timedelta\n", "import warnings\n", "import pencilbox as pb\n", "import time\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "5ac20c0c-eab0-46c8-a7b0-6c9083ffc8c5", "metadata": {}, "outputs": [], "source": ["city_outlet_df = pd.read_sql_query(\n", "    \"\"\"\n", "select\n", "  distinct city_name, count(distinct outlet_id) as num_outlets from dwh.fact_sales_order_details fsod\n", "WHERE\n", "  fsod.order_current_status = 'DELIVERED'\n", "  AND fsod.is_internal_order = FALSE\n", "  AND fsod.order_create_dt_ist >= CURRENT_DATE - INTERVAL '1' DAY\n", "  AND fsod.total_selling_price > 0\n", "  AND fsod.total_procured_quantity > 0\n", "  AND fsod.city_name not like '%%B2B%%'\n", "  AND fsod.city_name not in ('Not in service area', 'test1207898732')\n", "  AND fsod.order_type NOT IN ('InternalForwardOrder', 'InternalReverseOrder', 'DropShippingInternalReverseOrder')\n", "  group by 1\n", "\"\"\",\n", "    con=con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5930f491-a4b3-4aaf-ad27-3c415bdd1305", "metadata": {}, "outputs": [], "source": ["sorted_df = city_outlet_df.sort_values(by=\"num_outlets\", ascending=False).reset_index(drop=True)\n", "\n", "groups = []  # List of groups, each group is a list of city names\n", "group_sums = []  # Keeps track of sum of outlets in each group\n", "\n", "for _, row in sorted_df.iterrows():\n", "    city = row[\"city_name\"]\n", "    outlets = row[\"num_outlets\"]\n", "\n", "    placed = False\n", "    for i in range(len(groups)):\n", "        if group_sums[i] + outlets < 100:\n", "            groups[i].append(city)\n", "            group_sums[i] += outlets\n", "            placed = True\n", "            break\n", "\n", "    if not placed:\n", "        # Start a new group\n", "        groups.append([city])\n", "        group_sums.append(outlets)"]}, {"cell_type": "code", "execution_count": null, "id": "a43e23bc-e1ba-4ea9-b52b-cada5502cc1d", "metadata": {}, "outputs": [], "source": ["len(groups)"]}, {"cell_type": "code", "execution_count": null, "id": "397979ac-743d-4273-9b74-34818bea4afd", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"dim_customer_key\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"city_name\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "    {\"name\": \"num_express_merchants\", \"type\": \"integer\", \"description\": \"num_express_merchants\"},\n", "    {\"name\": \"num_longtail_merchants\", \"type\": \"integer\", \"description\": \"num_longtail_merchants\"},\n", "    {\n", "        \"name\": \"num_inactive_express_merchants\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"num_inactive_express_merchants\",\n", "    },\n", "    {\n", "        \"name\": \"num_inactive_longtail_merchants\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"num_inactive_longtail_merchants\",\n", "    },\n", "    {\n", "        \"name\": \"num_express_merchants_checks_passed\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"num_express_merchants_checks_passed\",\n", "    },\n", "    {\n", "        \"name\": \"num_longtail_merchants_checks_passed\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"num_longtail_merchants_checks_passed\",\n", "    },\n", "    {\"name\": \"item_area_serviced\", \"type\": \"real\", \"description\": \"item_area_serviced\"},\n", "    {\"name\": \"city_area_serviced\", \"type\": \"real\", \"description\": \"city_area_serviced\"},\n", "    {\"name\": \"item_coverage\", \"type\": \"real\", \"description\": \"item_coverage\"},\n", "    {\"name\": \"active\", \"type\": \"integer\", \"description\": \"active\"},\n", "    {\n", "        \"name\": \"num_super_longtail_merchants\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"num_super_longtail_merchants\",\n", "    },\n", "    {\"name\": \"num_unicorn_merchants\", \"type\": \"integer\", \"description\": \"num_unicorn_merchants\"},\n", "    {\n", "        \"name\": \"num_inactive_super_longtail_merchants\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"num_inactive_super_longtail_merchants\",\n", "    },\n", "    {\n", "        \"name\": \"num_inactive_unicorn_merchants\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"num_inactive_unicorn_merchants\",\n", "    },\n", "    {\n", "        \"name\": \"num_super_longtail_merchants_checks_passed\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"num_super_longtail_merchants_checks_passed\",\n", "    },\n", "    {\n", "        \"name\": \"num_unicorn_merchants_checks_passed\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"num_unicorn_merchants_checks_passed\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "96172103-7e17-42d8-8691-32b8d0ab1252", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"city_assortment_coverage\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"insert_ds_ist\", \"city_name\", \"item_id\"],\n", "    \"partition_key\": [\"insert_ds_ist\", \"city_name\"],\n", "    # \"incremental_key\": \"updated_at_ist\",\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "    \"table_description\": \"assortment_coverage\",\n", "    \"run_maintenance\": <PERSON><PERSON><PERSON>,\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "a7b64d4e-b644-4e03-a8f9-aa015ff7bf17", "metadata": {}, "outputs": [], "source": ["QUERY_TEMPLATE = \"\"\"\n", "    with assortment_polygon as (\n", "      SELECT DISTINCT \n", "        CURRENT_DATE AS insert_ds_ist,\n", "        mo.frontend_merchant_city_name,\n", "        pfma.item_id,\n", "        CAST(mo.frontend_merchant_id AS VARCHAR) AS merchant_id,\n", "        mo.pos_outlet_id,\n", "        mo.facility_id,\n", "        pfma.master_assortment_substate_id,\n", "        pfma.assortment_type,\n", "        CASE\n", "            WHEN pfma.assortment_type = 'EXPRESS' THEN 'STORE_POLYGON'\n", "            WHEN pfma.assortment_type = 'UNICORN' THEN 'UNICORN'\n", "            WHEN pfma.assortment_type = 'SUPER_LONGTAIL' THEN 'SUPER_LONGTAIL'\n", "            ELSE 'LONGTAIL_POLYGON'\n", "        END AS type,\n", "        CASE\n", "            WHEN (pt.cess IS NULL OR pt.cgst IS NULL OR pt.igst IS NULL OR pt.sgst IS NULL OR tm.on_invoice_margin_value is NULL or vfa.vendor_id is NULL) THEN 0 \n", "            ELSE 1 \n", "        END AS assortment_check\n", "    FROM\n", "        dwh.dim_merchant_outlet_facility_mapping mo\n", "        INNER JOIN dwh.dim_outlet do \n", "            ON do.outlet_id = mo.pos_outlet_id\n", "            AND do.is_current\n", "            AND do.is_outlet_active = 1\n", "        INNER JOIN rpc.product_facility_master_assortment pfma \n", "            ON pfma.facility_id = mo.facility_id\n", "            AND pfma.active = 1\n", "            AND pfma.lake_active_record\n", "        INNER JOIN po.physical_facility_outlet_mapping pfom\n", "            ON pfom.facility_id = mo.facility_id\n", "            AND pfom.outlet_id = mo.pos_outlet_id\n", "            AND pfom.active = 1\n", "            AND pfom.lake_active_record\n", "        LEFT JOIN (\n", "            SELECT DISTINCT \n", "                iotm.item_id, \n", "                iotm.outlet_id, \n", "                pfom2.city_id AS be_city_id,\n", "                CAST(iotm.tag_value AS INT) AS be_outlet_id, \n", "                pfom2.facility_id AS be_facility_id \n", "            FROM \n", "                rpc.item_outlet_tag_mapping iotm\n", "                INNER JOIN po.physical_facility_outlet_mapping pfom2 \n", "                    ON pfom2.outlet_id = CAST(iotm.tag_value AS INT) \n", "                    AND pfom2.active = 1 \n", "                    AND pfom2.ars_active = 1\n", "            WHERE \n", "                iotm.tag_type_id = 8\n", "                AND iotm.active = 1\n", "        ) iotm \n", "            ON iotm.item_id = pfma.item_id \n", "            AND iotm.outlet_id = mo.pos_outlet_id\n", "        LEFT JOIN vms.vms_vendor_facility_alignment vfa \n", "            ON vfa.facility_id = CASE \n", "                                    WHEN COALESCE(iotm.be_outlet_id, 0) = 0 THEN mo.facility_id \n", "                                    ELSE iotm.be_facility_id \n", "                                 END\n", "            AND vfa.item_id = pfma.item_id \n", "            AND vfa.active = 1\n", "        LEFT JOIN rpc.tot_margin tm \n", "            ON tm.item_id = pfma.item_id \n", "            AND tm.facility_id = CASE \n", "                                    WHEN COALESCE(iotm.be_outlet_id, 0) = 0 THEN mo.facility_id \n", "                                    ELSE iotm.be_facility_id \n", "                                 END\n", "            AND vfa.vendor_id = tm.vendor_id\n", "            AND tm.active = 1\n", "        LEFT JOIN rpc.product_tax pt \n", "            ON pt.item_id = pfma.item_id \n", "            AND pt.location = 0\n", "            AND pt.active = 1 \n", "            AND pt.lake_active_record\n", "    WHERE\n", "        mo.is_pos_outlet_active = 1\n", "        AND mo.is_mapping_enabled\n", "        AND mo.is_current\n", "        AND mo.is_current_mapping_active\n", "        AND mo.frontend_merchant_city_name IN {city}\n", "        AND COALESCE(mo.is_bistro_outlet, FALSE) != TRUE\n", "        --AND pfma.item_id%%10 = 0\n", "    ),\n", "\n", "    item_level_polygon AS (SELECT ap.insert_ds_ist,\n", "      frontend_merchant_city_name,\n", "      ap.item_id,\n", "      count(\n", "        distinct case\n", "          when ap.assortment_type = 'EXPRESS'\n", "          and ap.master_assortment_substate_id in (1, 3) then ap.merchant_id\n", "        end\n", "      ) as num_express_merchants,\n", "      count(\n", "        distinct case\n", "          when ap.assortment_type = 'LONGTAIL'\n", "          and ap.master_assortment_substate_id in (1, 3) then ap.merchant_id\n", "        end\n", "      ) as num_longtail_merchants,\n", "      count(\n", "        distinct case\n", "          when ap.assortment_type = 'SUPER_LONGTAIL'\n", "          and ap.master_assortment_substate_id in (1, 3) then ap.merchant_id\n", "        end\n", "      ) as num_super_longtail_merchants,\n", "      count(\n", "        distinct case\n", "          when ap.assortment_type = 'UNICORN'\n", "          and ap.master_assortment_substate_id in (1, 3) then ap.merchant_id\n", "        end\n", "      ) as num_unicorn_merchants,      \n", "      count(\n", "        distinct case\n", "          when ap.assortment_type = 'EXPRESS'\n", "          and ap.master_assortment_substate_id not in (1, 3) then ap.merchant_id\n", "        end\n", "      ) as num_inactive_express_merchants,\n", "      count(\n", "        distinct case\n", "          when ap.assortment_type = 'LONGTAIL'\n", "          and ap.master_assortment_substate_id not in (1, 3) then ap.merchant_id\n", "        end\n", "      ) as num_inactive_longtail_merchants,\n", "      count(\n", "        distinct case\n", "          when ap.assortment_type = 'SUPER_LONGTAIL'\n", "          and ap.master_assortment_substate_id not in (1, 3) then ap.merchant_id\n", "        end\n", "      ) as num_inactive_super_longtail_merchants,\n", "      count(\n", "        distinct case\n", "          when ap.assortment_type = 'UNICORN'\n", "          and ap.master_assortment_substate_id not in (1, 3) then ap.merchant_id\n", "        end\n", "      ) as num_inactive_unicorn_merchants,\n", "        count(\n", "        distinct case\n", "          when ap.assortment_type = 'EXPRESS'\n", "          and ((ap.master_assortment_substate_id = 1 and ap.assortment_check = 1)\n", "          or (ap.master_assortment_substate_id = 3))\n", "          then ap.merchant_id\n", "        end\n", "      ) as num_express_merchants_checks_passed,\n", "      count(\n", "        distinct case\n", "          when ap.assortment_type = 'LONGTAIL'\n", "          and ((ap.master_assortment_substate_id = 1 and ap.assortment_check = 1)\n", "          or (ap.master_assortment_substate_id = 3))\n", "          then ap.merchant_id\n", "        end\n", "      ) as num_longtail_merchants_checks_passed,\n", "        count(\n", "        distinct case\n", "          when ap.assortment_type = 'SUPER_LONGTAIL'\n", "          and ((ap.master_assortment_substate_id = 1 and ap.assortment_check = 1)\n", "          or (ap.master_assortment_substate_id = 3))\n", "          then ap.merchant_id\n", "        end\n", "      ) as num_super_longtail_merchants_checks_passed,\n", "      count(\n", "        distinct case\n", "          when ap.assortment_type = 'UNICORN'\n", "          and ((ap.master_assortment_substate_id = 1 and ap.assortment_check = 1)\n", "          or (ap.master_assortment_substate_id = 3))\n", "          then ap.merchant_id\n", "        end\n", "      ) as num_unicorn_merchants_checks_passed,      \n", "      round(\n", "        ST_Area(\n", "          to_spherical_geography(\n", "            COALESCE(geometry_union_agg(ST_GeometryFromText(case when ((ap.master_assortment_substate_id = 1 and ap.assortment_check = 1) or ap.master_assortment_substate_id = 3) then ssp.polygon end)), ST_GeometryFromText('POLYGON EMPTY'))\n", "          )\n", "        ) / 1000000.0,\n", "        2\n", "      ) as item_area_serviced\n", "    FROM\n", "      serviceability.ser_store_polygons ssp\n", "      INNER JOIN assortment_polygon ap on ap.merchant_id = ssp.merchant_id\n", "      and ap.type = ssp.type\n", "    WHERE\n", "      is_active = true\n", "      and lake_active_record\n", "      and ssp.type IN ('STORE_POLYGON', 'LON<PERSON><PERSON><PERSON>_POLYGON', 'SUPER_LONGTAIL', 'UNICORN')\n", "      and ST_IsValid(ST_GeometryFromText(ssp.polygon))\n", "    GROUP BY\n", "      1,\n", "      2,\n", "      3),\n", "\n", "      city_level_polygon_list AS (SELECT\n", "      distinct\n", "      ap.insert_ds_ist,\n", "      frontend_merchant_city_name,\n", "      ssp.polygon\n", "    FROM\n", "      serviceability.ser_store_polygons ssp\n", "      INNER JOIN assortment_polygon ap on ap.merchant_id = ssp.merchant_id\n", "      and ap.type = ssp.type\n", "    WHERE\n", "      is_active = true\n", "      and lake_active_record\n", "      and ssp.type IN ('STORE_POLYGON')\n", "      and ST_IsValid(ST_GeometryFromText(ssp.polygon))\n", "      and ((ap.master_assortment_substate_id = 1 and ap.assortment_check = 1) \n", "      or (ap.master_assortment_substate_id = 3))\n", "    ),\n", "\n", "      city_level_polygon AS (SELECT\n", "      insert_ds_ist,\n", "      frontend_merchant_city_name,\n", "      round(\n", "        ST_Area(\n", "          to_spherical_geography(\n", "            COALESCE(geometry_union_agg(ST_GeometryFromText(polygon)), ST_GeometryFromText('POLYGON EMPTY'))\n", "          )\n", "        ) / 1000000.0,\n", "        2\n", "      ) as city_area_serviced\n", "    FROM\n", "      city_level_polygon_list\n", "    GROUP BY\n", "      1,\n", "      2)\n", "\n", "      select ilp.insert_ds_ist, \n", "           ilp.frontend_merchant_city_name as city_name, \n", "           ilp.item_id,\n", "           COALESCE(ilp.num_express_merchants, 0) as num_express_merchants, \n", "           COALESCE(ilp.num_longtail_merchants, 0) as num_longtail_merchants,  \n", "           COALESCE(ilp.num_inactive_express_merchants, 0) as num_inactive_express_merchants,  \n", "           COALESCE(ilp.num_inactive_longtail_merchants, 0) as num_inactive_longtail_merchants, \n", "           COALESCE(ilp.num_express_merchants_checks_passed, 0) as num_express_merchants_checks_passed, \n", "           COALESCE(ilp.num_longtail_merchants_checks_passed, 0) as num_longtail_merchants_checks_passed, \n", "           COALESCE(ilp.item_area_serviced, 0) as item_area_serviced, \n", "           COALESCE(clp.city_area_serviced, 0) as city_area_serviced, \n", "           least(100.00*COALESCE(ilp.item_area_serviced, 0)/COALESCE(clp.city_area_serviced, 0), 100.00) as item_coverage,\n", "           1 as active,\n", "           COALESCE(ilp.num_super_longtail_merchants, 0) as num_super_longtail_merchants, \n", "           COALESCE(ilp.num_unicorn_merchants, 0) as num_unicorn_merchants,  \n", "           COALESCE(ilp.num_inactive_super_longtail_merchants, 0) as num_inactive_super_longtail_merchants,  \n", "           COALESCE(ilp.num_inactive_unicorn_merchants, 0) as num_inactive_unicorn_merchants, \n", "           COALESCE(ilp.num_super_longtail_merchants_checks_passed, 0) as num_super_longtail_merchants_checks_passed, \n", "           COALESCE(ilp.num_unicorn_merchants_checks_passed, 0) as num_unicorn_merchants_checks_passed\n", "        from item_level_polygon ilp\n", "      inner join city_level_polygon clp on ilp.insert_ds_ist = clp.insert_ds_ist and ilp.frontend_merchant_city_name = clp.frontend_merchant_city_name\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "7a526933-61e6-48a5-8b2d-47e93757dba2", "metadata": {}, "outputs": [], "source": ["import concurrent.futures\n", "import time\n", "import psycopg2  # Uncomment or change based on actual DB used\n", "\n", "import traceback\n", "\n", "!pip install tenacity\n", "\n", "from tenacity import retry, stop_after_attempt, wait_fixed\n", "\n", "\n", "@retry(stop=stop_after_attempt(3), wait=wait_fixed(60), reraise=True)\n", "def to_trino_with_retry(*args, **kwargs):\n", "    return pb.to_trino(*args, **kwargs)\n", "\n", "\n", "# Prepare list of parameters\n", "params_list = [tuple(group + [\"abc\"]) if len(group) == 1 else tuple(group) for group in groups]\n", "\n", "# Dictionary to track job statuses\n", "job_status = {params: \"Pending\" for params in params_list}\n", "failed_jobs = []\n", "\n", "\n", "def run_query(params):\n", "    start = time.time()\n", "    to_trino_with_retry(QUERY_TEMPLATE.format(city=params), **kwargs)\n", "    end = time.time()\n", "    duration = end - start\n", "    return duration\n", "\n", "\n", "# Run queries in parallel using ThreadPoolExecutor\n", "with concurrent.futures.ThreadPoolExecutor(max_workers=9) as executor:\n", "    future_to_param = {executor.submit(run_query, params): params for params in params_list}\n", "\n", "    for future in concurrent.futures.as_completed(future_to_param):\n", "        params = future_to_param[future]\n", "        try:\n", "            duration = future.result()\n", "            if duration > 60:\n", "                print(\n", "                    f\"Data for {[x for x in params if x != 'abc']} pushed in table in: {duration / 60:.2f} min\"\n", "                )\n", "            else:\n", "                print(\n", "                    f\"Data for {[x for x in params if x != 'abc']} pushed in table in: {duration:.2f} s\"\n", "                )\n", "            job_status[params] = \"Success\"\n", "\n", "        except BaseException as e:\n", "            traceback.print_exc()\n", "            print(f\"Failed to push data for {params} to the table. Error: {e}\")\n", "            job_status[params] = \"Failed\"\n", "            failed_jobs.append(params)\n", "\n", "success_records = [params for params, status in job_status.items() if status == \"Success\"]\n", "failed_records = [params for params, status in job_status.items() if status == \"Failed\"]\n", "pending_records = [params for params, status in job_status.items() if status == \"Pending\"]\n", "\n", "count_success = len([city for group in success_records for city in group if city != \"abc\"])\n", "count_failed = len([city for group in failed_records for city in group if city != \"abc\"])\n", "count_pending = len([city for group in pending_records for city in group if city != \"abc\"])\n", "failed_cities = [city for group in failed_records for city in group if city != \"abc\"]\n", "pending_cities = [city for group in pending_records for city in group if city != \"abc\"]\n", "\n", "# Display Job Status Summary\n", "print(\"\\n=== Job Execution Summary ===\")\n", "for params, status in job_status.items():\n", "    print(f\"Params: {params}, Status: {status}\")\n", "\n", "message = f\"\"\"\n", "Summary for Assortment Coverage Dagger: \n", "Records updated for {count_success} cities\n", "\"\"\"\n", "\n", "if count_failed > 0:\n", "    message += f\"\"\"Updation failed for {count_failed} cities\n", "Failed Cities: {', '.join(failed_cities)}\n", "\"\"\"\n", "\n", "if count_pending > 0:\n", "    message += f\"\"\"Updation pending for {count_pending} cities\n", "Pending Cities: {', '.join(pending_cities)}\n", "\"\"\"\n", "\n", "print(message)\n", "\n", "pb.send_slack_message(\n", "    channel=\"assortment-upload-loggers\",\n", "    text=message,\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}