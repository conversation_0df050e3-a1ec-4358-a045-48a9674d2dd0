{"cells": [{"cell_type": "code", "execution_count": null, "id": "d38c0ffc-fa28-460b-ab0c-32b0245e5212", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import numpy as np\n", "import pandas as pd\n", "import math\n", "from datetime import date, timedelta\n", "import warnings\n", "import pencilbox as pb\n", "import time\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "2759bb7c-5f9a-4e94-a0d4-7cc338967772", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"insert_ds_ist\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"city_name\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility_id\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"outlet_id\"},\n", "    {\"name\": \"merchant_id\", \"type\": \"integer\", \"description\": \"merchant_id\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"be_facility_id\"},\n", "    {\"name\": \"be_outlet_id\", \"type\": \"integer\", \"description\": \"be_outlet_id\"},\n", "    {\n", "        \"name\": \"master_assortment_substate_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"master_assortment_substate_id\",\n", "    },\n", "    {\"name\": \"assortment_type\", \"type\": \"varchar\", \"description\": \"assortment_type\"},\n", "    {\"name\": \"polygon_type\", \"type\": \"varchar\", \"description\": \"polygon_type\"},\n", "    {\"name\": \"tax_check\", \"type\": \"integer\", \"description\": \"tax_check\"},\n", "    {\"name\": \"margin_check\", \"type\": \"integer\", \"description\": \"margin_check\"},\n", "    {\"name\": \"vendor_check\", \"type\": \"integer\", \"description\": \"vendor_check\"},\n", "    {\"name\": \"assortment_check\", \"type\": \"integer\", \"description\": \"assortment_check\"},\n", "    {\"name\": \"polygon_check\", \"type\": \"integer\", \"description\": \"polygon_check\"},\n", "    {\"name\": \"active\", \"type\": \"integer\", \"description\": \"active\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "c6c12fa2-9d2d-4ac5-b278-ffc962f95f60", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"daily_assortment_snapshot\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"insert_ds_ist\", \"city_name\", \"facility_id\", \"item_id\"],\n", "    \"partition_key\": [\"insert_ds_ist\", \"city_name\"],\n", "    # \"incremental_key\": \"updated_at_ist\",\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "    \"table_description\": \"daily_assortment_snapshot\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "f2e1fee0-5750-4c81-91b6-ff52d20f12dc", "metadata": {}, "outputs": [], "source": ["QUERY_TEMPLATE = \"\"\"\n", "with assortment_polygon as (\n", "  SELECT\n", "    DISTINCT CURRENT_DATE AS insert_ds_ist,\n", "    mo.frontend_merchant_city_name AS city_name,\n", "    mo.facility_id,\n", "    mo.pos_outlet_id AS outlet_id,\n", "    pfma.item_id,\n", "    CAST(mo.frontend_merchant_id AS VARCHAR) AS merchant_id,\n", "    COALESCE(iotm.be_facility_id, 0) AS be_facility_id,\n", "    COALESCE(iotm.be_outlet_id, 0) AS be_outlet_id,\n", "    pfma.master_assortment_substate_id,\n", "    pfma.assortment_type,\n", "    CASE\n", "      WHEN pfma.assortment_type = 'EXPRESS' THEN 'STORE_POLYGON'\n", "      WHEN pfma.assortment_type = 'UNICORN' THEN 'UNICORN'\n", "      WHEN pfma.assortment_type = 'SUPER_LONGTAIL' THEN 'SUPER_LONGTAIL'\n", "      ELSE 'LONGTAIL_POLYGON'\n", "    END AS polygon_type,\n", "    CASE\n", "      WHEN (\n", "        pt.cess IS NULL\n", "        OR pt.cgst IS NULL\n", "        OR pt.igst IS NULL\n", "        OR pt.sgst IS NULL\n", "      ) THEN 0\n", "      ELSE 1\n", "    END AS tax_check,\n", "    CASE\n", "      WHEN (tm.on_invoice_margin_value is NULL) THEN 0\n", "      ELSE 1\n", "    END AS margin_check,\n", "    CASE\n", "      WHEN (vfa.vendor_id is NULL) THEN 0\n", "      ELSE 1\n", "    END AS vendor_check,\n", "    CASE\n", "      WHEN (\n", "        pt.cess IS NULL\n", "        OR pt.cgst IS NULL\n", "        OR pt.igst IS NULL\n", "        OR pt.sgst IS NULL\n", "        OR tm.on_invoice_margin_value is NULL\n", "        or vfa.vendor_id is NULL\n", "      ) THEN 0\n", "      ELSE 1\n", "    END AS assortment_check\n", "  FROM\n", "    dwh.dim_merchant_outlet_facility_mapping mo\n", "    INNER JOIN dwh.dim_outlet do ON do.outlet_id = mo.pos_outlet_id\n", "    AND do.is_current\n", "    AND do.is_outlet_active = 1\n", "    INNER JOIN rpc.product_facility_master_assortment pfma ON pfma.facility_id = mo.facility_id\n", "    AND pfma.active = 1\n", "    AND pfma.lake_active_record\n", "    INNER JOIN po.physical_facility_outlet_mapping pfom ON pfom.facility_id = mo.facility_id\n", "    AND pfom.outlet_id = mo.pos_outlet_id\n", "    AND pfom.active = 1\n", "    AND pfom.lake_active_record\n", "    LEFT JOIN (\n", "      SELECT\n", "        DISTINCT iotm.item_id,\n", "        iotm.outlet_id,\n", "        pfom2.city_id AS be_city_id,\n", "        TRY_CAST(iotm.tag_value AS INT) AS be_outlet_id,\n", "        pfom2.facility_id AS be_facility_id\n", "      FROM\n", "        rpc.item_outlet_tag_mapping iotm\n", "        INNER JOIN po.physical_facility_outlet_mapping pfom2 ON pfom2.outlet_id = CAST(iotm.tag_value AS INT)\n", "        AND pfom2.active = 1\n", "        AND pfom2.ars_active = 1\n", "      WHERE\n", "        iotm.tag_type_id = 8\n", "        AND iotm.active = 1\n", "    ) iotm ON iotm.item_id = pfma.item_id\n", "    AND iotm.outlet_id = mo.pos_outlet_id\n", "    LEFT JOIN vms.vms_vendor_facility_alignment vfa ON vfa.facility_id = CASE\n", "      WHEN COALESCE(iotm.be_outlet_id, 0) = 0 THEN mo.facility_id\n", "      ELSE iotm.be_facility_id\n", "    END\n", "    AND vfa.item_id = pfma.item_id\n", "    AND vfa.active = 1\n", "    LEFT JOIN rpc.tot_margin tm ON tm.item_id = pfma.item_id\n", "    AND tm.facility_id = CASE\n", "      WHEN COALESCE(iotm.be_outlet_id, 0) = 0 THEN mo.facility_id\n", "      ELSE iotm.be_facility_id\n", "    END\n", "    AND vfa.vendor_id = tm.vendor_id\n", "    AND tm.active = 1\n", "    LEFT JOIN rpc.product_tax pt ON pt.item_id = pfma.item_id\n", "    AND pt.location = 0\n", "    AND pt.active = 1\n", "    AND pt.lake_active_record\n", "  WHERE\n", "    mo.is_pos_outlet_active = 1\n", "    AND mo.is_mapping_enabled\n", "    AND mo.is_current\n", "    AND mo.is_current_mapping_active\n", "    AND COALESCE(mo.is_bistro_outlet, FALSE) != TRUE\n", ")\n", "SELECT\n", "  DISTINCT ap.insert_ds_ist,\n", "  ap.city_name,\n", "  ap.facility_id,\n", "  ap.outlet_id,\n", "  CAST(ap.merchant_id AS INT) AS merchant_id,\n", "  ap.item_id,\n", "  ap.be_facility_id,\n", "  ap.be_outlet_id,\n", "  ap.master_assortment_substate_id,\n", "  ap.assortment_type,\n", "  ap.polygon_type,\n", "  ap.tax_check,\n", "  ap.margin_check,\n", "  ap.vendor_check,\n", "  ap.assortment_check,\n", "  CASE\n", "    WHEN ssp.is_active = true then 1\n", "    else 0\n", "  end as polygon_check,\n", "  1 as active\n", "FROM\n", "  assortment_polygon ap\n", "  LEFT JOIN serviceability.ser_store_polygons ssp on ap.merchant_id = ssp.merchant_id\n", "  and ap.polygon_type = ssp.type\n", "  and ssp.is_active = true\n", "  and ssp.lake_active_record\n", "  and ssp.type IN (\n", "    'STORE_POLYGON',\n", "    'LONGTAIL_POLYGON',\n", "    'SUPER_LONGTAIL',\n", "    'UNICORN'\n", "  )\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "7c2c7989-9088-4cac-8e52-9d0a227f07e7", "metadata": {}, "outputs": [], "source": ["import time\n", "import traceback\n", "\n", "!pip install tenacity\n", "\n", "from tenacity import retry, stop_after_attempt, wait_fixed\n", "\n", "\n", "@retry(stop=stop_after_attempt(3), wait=wait_fixed(60), reraise=True)\n", "def to_trino_with_retry(*args, **kwargs):\n", "    return pb.to_trino(*args, **kwargs)\n", "\n", "\n", "job_status = \"Pending\"\n", "\n", "try:\n", "    start = time.time()\n", "    to_trino_with_retry(QUERY_TEMPLATE, **kwargs)\n", "    end = time.time()\n", "    duration = end - start\n", "    if duration > 60:\n", "        print(f\"Data pushed in table in: {duration / 60:.2f} min\")\n", "    else:\n", "        print(f\"Data pushed in table in: {duration:.2f} s\")\n", "    job_status = \"Success\"\n", "\n", "except BaseException as e:\n", "    traceback.print_exc()\n", "    print(f\"Failed to push data to the table. Error: {e}\")\n", "    job_status = \"Failed\"\n", "\n", "# Display Job Status Summary\n", "print(\"\\n=== Job Execution Summary ===\")\n", "print(f\"Status: {job_status}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}