{"cells": [{"cell_type": "code", "execution_count": null, "id": "f8137fc5-d9dc-4880-b47a-ee7116423e9a", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import numpy as np\n", "import pandas as pd\n", "import math\n", "from datetime import date, timedelta\n", "import warnings\n", "import pencilbox as pb\n", "import glob\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "83d504b4-84f0-4245-b83d-c754871758a9", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_colwidth\", None)\n", "pd.set_option(\"display.max_columns\", None)\n", "pd.set_option(\"display.max_rows\", 1600)"]}, {"cell_type": "code", "execution_count": null, "id": "0510bdf8-b5fb-4854-989d-367c841d8884", "metadata": {}, "outputs": [], "source": ["icd_df = pd.read_sql_query(\n", "    \"select distinct item_id, name, l0, l1, l2, product_type from rpc.item_category_details where lake_active_record\",\n", "    con=con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "55e23b06-2593-459d-b615-53b9b5559b43", "metadata": {}, "outputs": [], "source": ["packaged_items_df = pd.read_sql_query(\n", "    \"\"\"\n", "SELECT *\n", "FROM\n", "  (SELECT icd.item_id,\n", "          icd.name,\n", "          icd.l0,\n", "          icd.l1,\n", "          icd.l2,\n", "          icd.product_type,\n", "          CASE\n", "              WHEN id.perishable = 1 THEN 'PERISHABLE'\n", "              ELSE 'PACKAGED'\n", "          END AS item_type,\n", "          id.storage_type AS storage_type_raw,\n", "          CASE\n", "              WHEN id.storage_type IN ('1',\n", "                                       '8',\n", "                                       '11') THEN 'REGULAR'\n", "              WHEN id.storage_type IN ('4',\n", "                                       '5') THEN 'HEAVY'\n", "              WHEN id.storage_type IN ('2',\n", "                                       '6') THEN 'COLD'\n", "              WHEN id.storage_type IN ('3',\n", "                                       '7') THEN 'FROZEN'\n", "              ELSE 'REGULAR'\n", "          END AS storage_type,\n", "          CASE\n", "              WHEN id.handling_type IN ('8') THEN 'PACKAGING MATERIAL'\n", "              WHEN id.handling_type IN ('6') THEN 'MEDICINAL'\n", "              ELSE 'REGULAR'\n", "          END AS handling_type,\n", "          id.variant_mrp AS mrp,\n", "          id.variant_description,\n", "          id.weight_in_gm,\n", "          id.length_in_cm,\n", "          id.height_in_cm,\n", "          id.breadth_in_cm,\n", "          id.shelf_life,\n", "          coalesce(itf.item_factor, 0.01) AS item_factor,\n", "          DATE_DIFF('day',date(icd.created_at), CURRENT_DATE) AS item_catalog_age,\n", "          rank() OVER (PARTITION BY id.item_id\n", "                       ORDER BY id.id DESC) AS variant_rank,\n", "                      itm.tag_value AS custom_storage_type_raw,\n", "                      CASE\n", "                          WHEN itm.tag_value = '1' THEN 'BEAUTY'\n", "                          WHEN itm.tag_value = '2' THEN 'BOUQUET'\n", "                          WHEN itm.tag_value = '3' THEN 'PREMIUM'\n", "                          WHEN itm.tag_value = '4' THEN 'BOOKS'\n", "                          WHEN itm.tag_value = '5' THEN 'NON_VEG'\n", "                          WHEN itm.tag_value = '6' THEN 'ICE_CREAM'\n", "                          WHEN itm.tag_value = '7' THEN 'TOYS'\n", "                          WHEN itm.tag_value = '8' THEN 'ELECTRONICS' -- when itm.tag_value = '9' then 'FESTIVE'\n", "\n", "                          WHEN itm.tag_value = '10' THEN 'VERTICAL_CHUTES'\n", "                          WHEN itm.tag_value = '11' THEN 'BEST_SERVED_COLD'\n", "                          WHEN itm.tag_value = '12' THEN 'CRITICAL_SKUS'\n", "                          WHEN itm.tag_value = '13' THEN 'LARGE'\n", "                          WHEN itm.tag_value = '14' THEN 'APPAREL'\n", "                          WHEN itm.tag_value = '15' THEN 'SPORTS'\n", "                          WHEN itm.tag_value = '16' THEN 'PET_CARE'\n", "                          WHEN itm.tag_value = '17' THEN 'HOME_DECOR'\n", "                          WHEN itm.tag_value = '18' THEN 'KITCHEN_DINING'\n", "                          WHEN itm.tag_value = '19' THEN 'HOME_FURNISHING'\n", "                          WHEN itm.tag_value = '20' THEN 'LONGTAIL_OTHERS'\n", "                          WHEN itm.tag_value IS NULL THEN 'NO_CONFIG'\n", "                          ELSE 'UNKNOWN_CONFIG'\n", "                      END AS custom_storage_type,\n", "                      pb.manufacturer_id,\n", "                      id.manufacturer AS manufacturer_name,\n", "                      pb.name AS brand_name,\n", "                      coalesce(id.outer_case_size,1) AS outer_case_size,\n", "                      coalesce(id.inner_case_size,1) AS inner_case_size,\n", "                      CASE\n", "                          WHEN itm2.tag_value = '1' THEN TRUE\n", "                          ELSE FALSE\n", "                      END AS is_high_value,\n", "                      itm2.tag_value AS high_value_tag_raw,\n", "                      CASE\n", "                          WHEN itm3.tag_value = '1' THEN 'upc scan'\n", "                          WHEN itm3.tag_value = '2' THEN 'serial scan'\n", "                          WHEN itm3.tag_value = '3' THEN 'qr scan'\n", "                          WHEN itm3.tag_value = '4' THEN 'no scan'\n", "                          ELSE 'unknown'\n", "                      END AS scan_type,\n", "                      itm3.tag_value AS scan_type_raw\n", "   FROM rpc.item_category_details icd\n", "   INNER JOIN rpc.product_product id ON id.item_id = icd.item_id\n", "   AND id.active = 1\n", "   AND id.approved = 1\n", "   AND id.lake_active_record\n", "   LEFT JOIN supply_etls.item_factor itf ON itf.item_id = icd.item_id\n", "   LEFT JOIN rpc.item_tag_mapping itm ON itm.tag_type_id = 7\n", "   AND itm.active = TRUE\n", "   AND itm.lake_active_record\n", "   AND itm.item_id = icd.item_id\n", "   LEFT JOIN rpc.product_brand pb ON id.brand_id = pb.id\n", "   AND pb.lake_active_record\n", "   AND pb.active = 1\n", "   LEFT JOIN rpc.item_tag_mapping itm2 ON itm2.item_id = icd.item_id\n", "   AND itm2.active\n", "   AND itm2.tag_type_id = 3\n", "   AND itm2.lake_active_record\n", "   LEFT JOIN rpc.item_tag_mapping itm3 ON itm3.item_id = icd.item_id\n", "   AND itm3.active\n", "   AND itm3.tag_type_id = 5\n", "   AND itm3.lake_active_record\n", "   WHERE icd.lake_active_record\n", "     AND perishable != 1\n", "     AND id.handling_type != '8'\n", "     AND id.storage_type NOT IN ('3',\n", "                                 '7')\n", "     AND icd.l0_id != 1487 -- removing vegetables and fruits\n", "\n", "     AND icd.l0 NOT IN ('wholesale store',\n", "                        'Trial new tree',\n", "                        'Specials',\n", "                        'Bistro')-- removing test and flyer/freebie l0s\n", " ) AS x\n", "WHERE variant_rank = 1\n", "\"\"\",\n", "    con=con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a7966491-28d1-45ac-961e-501a5fabe0c2", "metadata": {}, "outputs": [], "source": ["dec_packaged_goods_blended_cpd_city_df = pd.read_parquet(\n", "    \"s3://prod-dse-projects/Arpit_Bansal/temp_folder/New_SKU_Estimated_CPD\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "64ca2da7-055c-416d-98e5-e1e4eb8991cc", "metadata": {}, "outputs": [], "source": ["dec_packaged_goods_blended_cpd_city_df = dec_packaged_goods_blended_cpd_city_df.merge(icd_df).merge(\n", "    packaged_items_df.drop(columns=[\"l0\", \"l1\", \"l2\", \"name\", \"product_type\"])\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "56f07803-bdf5-4316-b2cb-796dc7c76c97", "metadata": {}, "outputs": [], "source": ["!pip install polars"]}, {"cell_type": "code", "execution_count": null, "id": "f134d7f4-8c62-4b50-80a1-de35ed72d18f", "metadata": {}, "outputs": [], "source": ["import polars as pl\n", "\n", "\n", "def compute_cpd_percentile_product_type_city(df: pd.DataFrame) -> pd.DataFrame:\n", "    pl_df = pl.from_pandas(df)\n", "\n", "    return (\n", "        pl_df.group_by([\"city_name\", \"l0\", \"l1\", \"product_type\"])\n", "        .agg(\n", "            [\n", "                pl.col(\"blended_cpd\").count().alias(\"blended_cpd_count\"),\n", "                pl.col(\"blended_cpd\").median().alias(\"blended_cpd_50%\"),\n", "                pl.col(\"item_factor\").count().alias(\"item_factor_count\"),\n", "                pl.col(\"item_factor\").median().alias(\"item_factor_50%\"),\n", "            ]\n", "        )\n", "        .to_pandas()\n", "    )\n", "\n", "\n", "def compute_cpd_percentile_l1_city(df: pd.DataFrame) -> pd.DataFrame:\n", "    pl_df = pl.from_pandas(df)\n", "\n", "    return (\n", "        pl_df.group_by([\"city_name\", \"l0\", \"l1\"])\n", "        .agg(\n", "            [\n", "                pl.col(\"blended_cpd\").count().alias(\"blended_cpd_count\"),\n", "                pl.col(\"blended_cpd\").median().alias(\"blended_cpd_50%\"),\n", "                pl.col(\"item_factor\").count().alias(\"item_factor_count\"),\n", "                pl.col(\"item_factor\").median().alias(\"item_factor_50%\"),\n", "            ]\n", "        )\n", "        .to_pandas()\n", "    )\n", "\n", "\n", "def compute_cpd_percentile_l0_city(df: pd.DataFrame) -> pd.DataFrame:\n", "    pl_df = pl.from_pandas(df)\n", "\n", "    return (\n", "        pl_df.group_by([\"city_name\", \"l0\"])\n", "        .agg(\n", "            [\n", "                pl.col(\"blended_cpd\").count().alias(\"blended_cpd_count\"),\n", "                pl.col(\"blended_cpd\").median().alias(\"blended_cpd_50%\"),\n", "                pl.col(\"item_factor\").count().alias(\"item_factor_count\"),\n", "                pl.col(\"item_factor\").median().alias(\"item_factor_50%\"),\n", "            ]\n", "        )\n", "        .to_pandas()\n", "    )\n", "\n", "\n", "def compute_cpd_percentile_city(df: pd.DataFrame) -> pd.DataFrame:\n", "    pl_df = pl.from_pandas(df)\n", "\n", "    return (\n", "        pl_df.group_by([\"city_name\"])\n", "        .agg(\n", "            [\n", "                pl.col(\"blended_cpd\").count().alias(\"blended_cpd_count\"),\n", "                pl.col(\"blended_cpd\").median().alias(\"blended_cpd_50%\"),\n", "                pl.col(\"item_factor\").count().alias(\"item_factor_count\"),\n", "                pl.col(\"item_factor\").median().alias(\"item_factor_50%\"),\n", "            ]\n", "        )\n", "        .to_pandas()\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "187915c1-58a3-461e-a1fe-0b2e5245a3f6", "metadata": {}, "outputs": [], "source": ["cpd_percentile_product_type_city_df = compute_cpd_percentile_product_type_city(\n", "    dec_packaged_goods_blended_cpd_city_df\n", ")\n", "cpd_percentile_l1_city_df = compute_cpd_percentile_l1_city(dec_packaged_goods_blended_cpd_city_df)\n", "cpd_percentile_l0_city_df = compute_cpd_percentile_l0_city(dec_packaged_goods_blended_cpd_city_df)\n", "cpd_percentile_city_df = compute_cpd_percentile_city(dec_packaged_goods_blended_cpd_city_df)"]}, {"cell_type": "code", "execution_count": null, "id": "e76372bc-5c31-4a56-b438-8f8874de5177", "metadata": {}, "outputs": [], "source": ["city_lat_lon_df = pd.read_sql_query(\n", "    \"\"\"\n", "WITH city_outlet_coords as (\n", "  SELECT\n", "    DISTINCT mo.frontend_merchant_city_name as city_name,\n", "    CAST(mo.frontend_merchant_id AS VARCHAR) AS merchant_id,\n", "    mo.pos_outlet_id,\n", "    mo.facility_id,\n", "    CAST(do.geo_location_lat as double) as facility_lat,\n", "    CAST(do.geo_location_lon as double) as facility_lon\n", "  FROM\n", "    dwh.dim_merchant_outlet_facility_mapping mo\n", "    INNER JOIN dwh.dim_outlet do ON do.outlet_id = mo.pos_outlet_id\n", "    AND do.is_current\n", "    AND do.is_outlet_active = 1\n", "    AND do.geo_location_lat is not null\n", "    AND do.geo_location_lon is not null\n", "  WHERE\n", "    mo.is_pos_outlet_active = 1\n", "    AND mo.is_mapping_enabled\n", "    AND mo.is_current\n", "    AND mo.is_current_mapping_active\n", "    AND COALESCE(mo.is_bistro_outlet, FALSE) != TRUE\n", ")\n", "\n", "select\n", "  distinct city_name,\n", "  avg(facility_lat) as city_lat,\n", "  avg(facility_lon) as city_lon\n", "from\n", "  city_outlet_coords\n", "group by\n", "  1\n", "\"\"\",\n", "    con=con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ab7ce318-4a4d-420a-a9b7-1fb154e78798", "metadata": {}, "outputs": [], "source": ["def haversine_distance(lat1, lon1, lat2, lon2):\n", "    \"\"\"\n", "    Calculate the great-circle distance between two points on the Earth's surface.\n", "\n", "    Parameters:\n", "    - lat1, lon1: Latitude and Longitude of point 1 (in decimal degrees)\n", "    - lat2, lon2: Latitude and Longitude of point 2 (in decimal degrees)\n", "\n", "    Returns:\n", "    - Distance in kilometers (float)\n", "    \"\"\"\n", "    # Radius of the Earth in kilometers\n", "    R = 6371.0\n", "\n", "    # Convert degrees to radians\n", "    phi1 = math.radians(lat1)\n", "    phi2 = math.radians(lat2)\n", "    delta_phi = math.radians(lat2 - lat1)\n", "    delta_lambda = math.radians(lon2 - lon1)\n", "\n", "    # Haversine formula\n", "    a = (\n", "        math.sin(delta_phi / 2) ** 2\n", "        + math.cos(phi1) * math.cos(phi2) * math.sin(delta_lambda / 2) ** 2\n", "    )\n", "\n", "    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))\n", "\n", "    # Distance in kilometers\n", "    distance = R * c\n", "    return distance"]}, {"cell_type": "code", "execution_count": null, "id": "7df12a90-ec32-4631-801c-e809bc6767c4", "metadata": {}, "outputs": [], "source": ["city_pairs = city_lat_lon_df.rename(\n", "    columns={\"city_name\": \"city1\", \"city_lat\": \"lat1\", \"city_lon\": \"lon1\"}\n", ").merge(\n", "    city_lat_lon_df.rename(columns={\"city_name\": \"city2\", \"city_lat\": \"lat2\", \"city_lon\": \"lon2\"}),\n", "    how=\"cross\",\n", ")\n", "\n", "# Step 2: Apply the haversine function row-wise\n", "city_pairs[\"distance_km\"] = city_pairs.apply(\n", "    lambda row: haversine_distance(row[\"lat1\"], row[\"lon1\"], row[\"lat2\"], row[\"lon2\"]), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9b4566b9-2b34-44fd-8e0c-d96ca87e2a5a", "metadata": {}, "outputs": [], "source": ["city_pairs = city_pairs.query(\"city1 != city2\")"]}, {"cell_type": "code", "execution_count": null, "id": "17576999-3727-41ae-8766-288929dda6d5", "metadata": {}, "outputs": [], "source": ["city_carts = pd.read_sql_query(\n", "    \"\"\"\n", "select\n", "  distinct city_name as city_2, count(distinct cart_id) as num_carts from dwh.fact_sales_order_details fsod\n", "WHERE\n", "  fsod.order_current_status = 'DELIVERED'\n", "  AND fsod.is_internal_order = FALSE\n", "  AND fsod.order_create_dt_ist = CURRENT_DATE - INTERVAL '1' DAY\n", "  AND fsod.total_selling_price > 0\n", "  AND fsod.total_procured_quantity > 0\n", "  AND fsod.city_name not like '%%B2B%%'\n", "  AND fsod.city_name not in ('Not in service area', 'test1207898732')\n", "  group by 1\n", "\"\"\",\n", "    con=con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7aa72d82-1348-43de-b75e-11e6e9aee17c", "metadata": {}, "outputs": [], "source": ["city_carts = city_carts.rename(columns={\"city_2\": \"city2\"})"]}, {"cell_type": "code", "execution_count": null, "id": "582be58b-ab5f-4a5a-9ef3-334626c1904b", "metadata": {}, "outputs": [], "source": ["city_pairs = city_pairs.merge(city_carts)"]}, {"cell_type": "code", "execution_count": null, "id": "4efd8ed5-430a-455a-8b57-d7c97a7679d3", "metadata": {}, "outputs": [], "source": ["similar_small_city_df = (\n", "    city_pairs.query(\n", "        f\"num_carts < {city_carts.num_carts.quantile(0.75)} and city1 not in @cpd_percentile_product_type_city_df.city_name.unique() and city2 in @cpd_percentile_product_type_city_df.city_name.unique()\"\n", "    )\n", "    .sort_values(\"distance_km\")\n", "    .groupby([\"city1\"], sort=False, as_index=False)\n", "    .first()\n", "    .sort_values(\"distance_km\", ascending=False)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d3c25dba-8656-4f85-a0ad-5b54aafa1b69", "metadata": {}, "outputs": [], "source": ["similar_small_city_df"]}, {"cell_type": "code", "execution_count": null, "id": "caa2eb5b-e0c7-4cb1-ad0d-6a56c5ae2cd0", "metadata": {}, "outputs": [], "source": ["concat_cpd_percentile_product_type_city_df = pd.concat(\n", "    [\n", "        cpd_percentile_product_type_city_df,\n", "        cpd_percentile_product_type_city_df.merge(\n", "            similar_small_city_df[[\"city1\", \"city2\"]]\n", "            .drop_duplicates()\n", "            .rename(columns={\"city2\": \"city_name\"})\n", "        )\n", "        .drop(columns=\"city_name\")\n", "        .drop_duplicates()\n", "        .rename(columns={\"city1\": \"city_name\"}),\n", "    ],\n", "    axis=0,\n", "    ignore_index=True,\n", ")\n", "concat_cpd_percentile_l1_city_df = pd.concat(\n", "    [\n", "        cpd_percentile_l1_city_df,\n", "        cpd_percentile_l1_city_df.merge(\n", "            similar_small_city_df[[\"city1\", \"city2\"]]\n", "            .drop_duplicates()\n", "            .rename(columns={\"city2\": \"city_name\"})\n", "        )\n", "        .drop(columns=\"city_name\")\n", "        .drop_duplicates()\n", "        .rename(columns={\"city1\": \"city_name\"}),\n", "    ],\n", "    axis=0,\n", "    ignore_index=True,\n", ")\n", "concat_cpd_percentile_l0_city_df = pd.concat(\n", "    [\n", "        cpd_percentile_l0_city_df,\n", "        cpd_percentile_l0_city_df.merge(\n", "            similar_small_city_df[[\"city1\", \"city2\"]]\n", "            .drop_duplicates()\n", "            .rename(columns={\"city2\": \"city_name\"})\n", "        )\n", "        .drop(columns=\"city_name\")\n", "        .drop_duplicates()\n", "        .rename(columns={\"city1\": \"city_name\"}),\n", "    ],\n", "    axis=0,\n", "    ignore_index=True,\n", ")\n", "concat_cpd_percentile_city_df = pd.concat(\n", "    [\n", "        cpd_percentile_city_df,\n", "        cpd_percentile_city_df.merge(\n", "            similar_small_city_df[[\"city1\", \"city2\"]]\n", "            .drop_duplicates()\n", "            .rename(columns={\"city2\": \"city_name\"})\n", "        )\n", "        .drop(columns=\"city_name\")\n", "        .drop_duplicates()\n", "        .rename(columns={\"city1\": \"city_name\"}),\n", "    ],\n", "    axis=0,\n", "    ignore_index=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b5893b2f-2973-45a5-9e0f-51805ceb8571", "metadata": {}, "outputs": [], "source": ["sku_cutoff = 0"]}, {"cell_type": "code", "execution_count": null, "id": "9131dd41-4ef2-4eef-9a30-47d43afb5eab", "metadata": {}, "outputs": [], "source": ["city_cpd = \"blended_cpd_50%\"\n", "city_cpd_query = \"`blended_cpd_50%`\"\n", "city_item_factor = \"item_factor_50%\"\n", "city_item_factor_query = \"`item_factor_50%`\"\n", "\n", "city_cpd_df = pd.concat(\n", "    [\n", "        icd_df.merge(packaged_items_df)[[\"l0\", \"l1\", \"product_type\"]]\n", "        .drop_duplicates(ignore_index=True)\n", "        .dropna()\n", "        .merge(\n", "            concat_cpd_percentile_product_type_city_df.query(f\"blended_cpd_count >= {sku_cutoff}\")\n", "        )[[\"city_name\", \"l0\", \"l1\", \"product_type\", city_cpd, city_item_factor]]\n", "        .drop_duplicates()\n", "        .assign(evaluated_cpd_type=\"ptype\"),\n", "        icd_df.merge(packaged_items_df)[[\"l0\", \"l1\", \"product_type\"]]\n", "        .drop_duplicates(ignore_index=True)\n", "        .dropna()\n", "        .merge(\n", "            concat_cpd_percentile_product_type_city_df[[\"city_name\"]].drop_duplicates(\n", "                ignore_index=True\n", "            ),\n", "            how=\"cross\",\n", "        )\n", "        .merge(\n", "            concat_cpd_percentile_product_type_city_df.query(\n", "                f\"blended_cpd_count >= {sku_cutoff}\"\n", "            ).assign(a=1),\n", "            how=\"left\",\n", "        )\n", "        .query(\"a!=1\")[[\"city_name\", \"l0\", \"l1\", \"product_type\"]]\n", "        .drop_duplicates(ignore_index=True)\n", "        .merge(\n", "            concat_cpd_percentile_l1_city_df.query(f\"blended_cpd_count >= {sku_cutoff}\"),\n", "            on=[\"city_name\", \"l0\", \"l1\"],\n", "        )[[\"city_name\", \"l0\", \"l1\", \"product_type\", city_cpd, city_item_factor]]\n", "        .drop_duplicates()\n", "        .assign(evaluated_cpd_type=\"l1\"),\n", "        icd_df.merge(packaged_items_df)[[\"l0\", \"l1\", \"product_type\"]]\n", "        .drop_duplicates(ignore_index=True)\n", "        .dropna()\n", "        .merge(\n", "            concat_cpd_percentile_product_type_city_df[[\"city_name\"]].drop_duplicates(\n", "                ignore_index=True\n", "            ),\n", "            how=\"cross\",\n", "        )\n", "        .merge(\n", "            concat_cpd_percentile_product_type_city_df.query(\n", "                f\"blended_cpd_count >= {sku_cutoff}\"\n", "            ).assign(a=1),\n", "            how=\"left\",\n", "        )\n", "        .query(\"a!=1\")[[\"city_name\", \"l0\", \"l1\", \"product_type\"]]\n", "        .drop_duplicates(ignore_index=True)\n", "        .merge(\n", "            concat_cpd_percentile_l1_city_df.query(f\"blended_cpd_count >= {sku_cutoff}\").assign(\n", "                a=1\n", "            ),\n", "            how=\"left\",\n", "        )\n", "        .query(\"a!=1\")[[\"city_name\", \"l0\", \"l1\", \"product_type\"]]\n", "        .drop_duplicates(ignore_index=True)\n", "        .merge(\n", "            concat_cpd_percentile_l0_city_df.query(f\"blended_cpd_count >= {sku_cutoff}\"),\n", "            on=[\"city_name\", \"l0\"],\n", "        )[[\"city_name\", \"l0\", \"l1\", \"product_type\", city_cpd, city_item_factor]]\n", "        .drop_duplicates()\n", "        .assign(evaluated_cpd_type=\"l0\"),\n", "        icd_df.merge(packaged_items_df)[[\"l0\", \"l1\", \"product_type\"]]\n", "        .drop_duplicates(ignore_index=True)\n", "        .dropna()\n", "        .merge(\n", "            concat_cpd_percentile_product_type_city_df[[\"city_name\"]].drop_duplicates(\n", "                ignore_index=True\n", "            ),\n", "            how=\"cross\",\n", "        )\n", "        .merge(\n", "            concat_cpd_percentile_product_type_city_df.query(\n", "                f\"blended_cpd_count >= {sku_cutoff}\"\n", "            ).assign(a=1),\n", "            how=\"left\",\n", "        )\n", "        .query(\"a!=1\")[[\"city_name\", \"l0\", \"l1\", \"product_type\"]]\n", "        .drop_duplicates(ignore_index=True)\n", "        .merge(\n", "            concat_cpd_percentile_l1_city_df.query(f\"blended_cpd_count >= {sku_cutoff}\"),\n", "            on=[\"city_name\", \"l0\", \"l1\"],\n", "        )[[\"city_name\", \"l0\", \"l1\", city_cpd, city_item_factor]]\n", "        .drop_duplicates()\n", "        .assign(evaluated_cpd_type=\"l1\"),\n", "        icd_df.merge(packaged_items_df)[[\"l0\", \"l1\", \"product_type\"]]\n", "        .drop_duplicates(ignore_index=True)\n", "        .dropna()\n", "        .merge(\n", "            concat_cpd_percentile_product_type_city_df[[\"city_name\"]].drop_duplicates(\n", "                ignore_index=True\n", "            ),\n", "            how=\"cross\",\n", "        )\n", "        .merge(\n", "            concat_cpd_percentile_product_type_city_df.query(\n", "                f\"blended_cpd_count >= {sku_cutoff}\"\n", "            ).assign(a=1),\n", "            how=\"left\",\n", "        )\n", "        .query(\"a!=1\")[[\"city_name\", \"l0\", \"l1\", \"product_type\"]]\n", "        .drop_duplicates(ignore_index=True)\n", "        .merge(\n", "            concat_cpd_percentile_l1_city_df.query(f\"blended_cpd_count >= {sku_cutoff}\").assign(\n", "                a=1\n", "            ),\n", "            how=\"left\",\n", "        )\n", "        .query(\"a!=1\")[[\"city_name\", \"l0\", \"l1\", \"product_type\"]]\n", "        .drop_duplicates(ignore_index=True)\n", "        .merge(\n", "            concat_cpd_percentile_l0_city_df.query(f\"blended_cpd_count >= {sku_cutoff}\"),\n", "            on=[\"city_name\", \"l0\"],\n", "        )[[\"city_name\", \"l0\", city_cpd, city_item_factor]]\n", "        .drop_duplicates()\n", "        .assign(evaluated_cpd_type=\"l0\"),\n", "    ],\n", "    axis=0,\n", "    ignore_index=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0e308e6e-f302-463f-85ae-35eced8862c4", "metadata": {}, "outputs": [], "source": ["city_cpd_df[\"insert_ds_ist\"] = date.today()"]}, {"cell_type": "code", "execution_count": null, "id": "3cc9b2c1-452c-4662-940b-e60b296b82f1", "metadata": {}, "outputs": [], "source": ["city_cpd_df = city_cpd_df.rename(\n", "    columns={\"blended_cpd_50%\": \"expected_cpd\", \"item_factor_50%\": \"expected_item_factor\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c81ae15f-05f8-4444-bf3a-c431059efb4a", "metadata": {}, "outputs": [], "source": ["city_cpd_df = city_cpd_df[\n", "    [\n", "        \"insert_ds_ist\",\n", "        \"city_name\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"product_type\",\n", "        \"expected_cpd\",\n", "        \"expected_item_factor\",\n", "        \"evaluated_cpd_type\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "da431fdb-dbaa-4a78-bf7b-02f2745f1e80", "metadata": {}, "outputs": [], "source": ["city_cpd_df"]}, {"cell_type": "code", "execution_count": null, "id": "7b04f034-ed3d-4cec-89ef-b3db8b823be5", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"dim_customer_key\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"city_name\"},\n", "    {\"name\": \"l0\", \"type\": \"varchar\", \"description\": \"l0\"},\n", "    {\"name\": \"l1\", \"type\": \"varchar\", \"description\": \"l1\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar\", \"description\": \"product_type\"},\n", "    {\"name\": \"expected_cpd\", \"type\": \"real\", \"description\": \"expected_cpd\"},\n", "    {\"name\": \"expected_item_factor\", \"type\": \"real\", \"description\": \"expected_item_factor\"},\n", "    {\"name\": \"evaluated_cpd_type\", \"type\": \"varchar\", \"description\": \"evaluated_cpd_type\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "0444fb56-97ad-4b9d-aa45-909ce360b5ea", "metadata": {}, "outputs": [], "source": ["kwargs_1 = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"new_items_expected_cpd\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"insert_ds_ist\", \"city_name\", \"l0\", \"l1\", \"product_type\"],\n", "    \"partition_key\": [\"insert_ds_ist\", \"city_name\"],\n", "    # \"incremental_key\": \"updated_at_ist\",\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"assortment_coverage\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "86c2ebe4-a9f0-4af0-96ae-3f8424ed4063", "metadata": {}, "outputs": [], "source": ["kwargs_2 = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"new_items_expected_cpd_log\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"insert_ds_ist\", \"city_name\", \"l0\", \"l1\", \"product_type\"],\n", "    \"partition_key\": [\"insert_ds_ist\", \"city_name\"],\n", "    # \"incremental_key\": \"updated_at_ist\",\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "    \"table_description\": \"assortment_coverage\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "0daa735d-8179-40d9-99de-fa06865e2d8c", "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "# Dictionary to track job statuses\n", "job_status = \"Pending\"\n", "\n", "\n", "def run_query():\n", "    \"\"\"Function to execute the query with parameters and track execution\"\"\"\n", "    max_tries = 3\n", "    global job_status\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(city_cpd_df, **kwargs_1)\n", "            pb.to_trino(city_cpd_df, **kwargs_2)\n", "            end = time.time()\n", "            duration = end - start\n", "            time.sleep(15)\n", "            job_status = \"Success\"\n", "            return duration\n", "\n", "        except BaseException as e:\n", "            if attempt == max_tries - 1:\n", "                job_status = f\"Failed: {e}\"\n", "            else:\n", "                time.sleep(60)\n", "\n", "\n", "try:\n", "    duration = run_query()\n", "    if duration > 60:\n", "        print(f\"Data pushed in table in: {duration / 60:.2f} min\")\n", "    else:\n", "        print(f\"Data pushed in table in: {duration:.2f} s\")\n", "except Exception as e:\n", "    print(f\"Failed to push data to the table. Error: {e}\")\n", "\n", "# Display Job Status Summary\n", "print(\"\\n=== Job Execution Summary ===\")\n", "print(job_status)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}