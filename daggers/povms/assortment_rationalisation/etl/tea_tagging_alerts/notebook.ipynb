{"cells": [{"cell_type": "code", "execution_count": null, "id": "5a955b34-7f0c-4997-a119-3f0308fabbb3", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import requests\n", "import json"]}, {"cell_type": "code", "execution_count": null, "id": "d7698285-0359-4839-b022-788d22dd8a76", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "3c4177fd-649e-423d-8039-5c65d278ddca", "metadata": {}, "outputs": [], "source": ["alert_channel = \"bl-rep-ops-core\"\n", "alert_channel_tech = \"assortment-alerts-plus-discussions\""]}, {"cell_type": "code", "execution_count": null, "id": "38cb5415-b776-4864-9dc6-c425f53ca109", "metadata": {}, "outputs": [], "source": ["data = f\"\"\"WITH fo AS (\n", "  SELECT\n", "    om.facility_id,\n", "    om.outlet_id AS outlet_id,\n", "    mo_map.frontend_merchant_id AS merchant_id,\n", "    om.outlet_name AS outlet_name,\n", "    rcl.id as city_id,\n", "    rcl.name AS city_name,\n", "    rcs.name AS state\n", "  FROM\n", "    po.physical_facility_outlet_mapping om\n", "    INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "    AND rco.business_type_id IN (7)\n", "    AND rco.company_type_id NOT IN (771, 767)\n", "    AND rco.lake_active_record\n", "    INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "    AND rcl.lake_active_record\n", "    INNER JOIN dwh.dim_merchant_outlet_facility_mapping mo_map ON mo_map.facility_id = om.facility_id\n", "    AND rco.business_type_id = 7\n", "    AND is_current\n", "    AND is_current_mapping_active\n", "    AND is_backend_merchant_active\n", "    AND is_frontend_merchant_active\n", "    INNER JOIN retail.console_state AS rcs ON rcs.id = rcl.state_id\n", "    AND rcs.lake_active_record\n", "    INNER JOIN (\n", "      SELECT\n", "        distinct cast(merchant_id AS int) merchant_id\n", "      FROM\n", "        serviceability.ser_store_polygons\n", "      WHERE\n", "        TYPE in ('STORE_POLYGON', 'LONGTAIL_POLYGON')\n", "        AND is_active = TRUE\n", "        AND lake_active_record\n", "    ) poly ON poly.merchant_id = mo_map.frontend_merchant_id\n", "  WHERE\n", "    om.active = 1\n", "    AND om.ars_active = 1\n", "    AND om.lake_active_record\n", "    AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "    AND om.outlet_name NOT LIKE '%%Draft%%'\n", "    AND om.outlet_name NOT LIKE '%%Test Store%%'\n", "    AND om.outlet_name NOT LIKE '%%Dummy%%'\n", "    AND om.facility_id != 273\n", "    AND om.outlet_id IN (\n", "      SELECT\n", "        DISTINCT outlet_id\n", "      FROM\n", "        po.bulk_facility_outlet_mapping\n", "      WHERE\n", "        active\n", "        AND lake_active_record\n", "    )\n", "),\n", "\n", "item_cat as (\n", "    select p.item_id, \n", "    p.name as item_name,\n", "    l0,l1,l2,product_type,\n", "    p.weight_in_gm,\n", "    p.variant_mrp as mrp,cms_food_type,\n", "    CASE\n", "        WHEN p.handling_type='8' THEN 'PACKAGING_MATERIAL'\n", "        WHEN d.l0_id=1487 THEN 'FNV'\n", "        WHEN d.l1_id=183 THEN 'ICE_CREAM'\n", "        WHEN d.l2_id=1185 THEN 'MILK'\n", "        WHEN p.storage_type IN ('3',\n", "                                   '7') THEN 'FROZEN'\n", "        WHEN p.perishable=1 THEN 'PERISHABLE'\n", "        WHEN p.storage_type IN ('2',\n", "                                   '6') THEN 'COLD'\n", "        WHEN (p.handling_type in ('6','10') or tag_value ='21') THEN 'MEDICINAL'\n", "        ELSE 'GROCERY'\n", "        END AS category,\n", "    case when (p.storage_type) in ('1','8') then 'REGULAR'\n", "        when (p.storage_type) in ('4','5') then 'HEAVY'\n", "        when (p.storage_type) in ('2','6') then 'COLD'\n", "        when (p.storage_type) in ('3','7') then 'FROZEN'\n", "        else 'REGULAR' end as storage_type1\n", "\n", "    from rpc.product_product p\n", "    inner join (select item_id, max(id) as max_id from rpc.product_product where active = 1 and approved = 1 group by 1) s on s.max_id = p.id\n", "    inner join rpc.item_category_details d on p.item_id = d.item_id\n", "    inner join rpc.product_brand pb on pb.id = p.brand_id and pb.active = 1\n", "    left join rpc.item_tag_mapping tm on tm.item_id = p.item_id and tm.tag_type_id = 7 \n", "    ),\n", "    \n", "final as (select pfma.item_id,item_name,ic.category, fo.outlet_id, fo.facility_id,fo.outlet_name,master_assortment_substate_id,assortment_type, cast(tag_value as int) as tag_value, \n", "om.outlet_name as be_name,om.facility_id as be_facility_id,\n", "bfom.facility_id  as ars_mapped_be,\n", "tr.backend_outlet_ids, TRY_CAST(JSON_EXTRACT(backend_outlet_ids,'$.EXPRESS_BACKEND_ID' ) as int) as express_be,\n", "       TRY_CAST(JSON_EXTRACT(backend_outlet_ids,'$.LONGTAIL_BACKEND_ID' ) as int) as longtail_be,\n", "tr.id as tr_rule, tr.category AS rule_category, frontend_facility_id,tr.item_id as item_id_tr,tr.city_id as tr_city_id, manufacturer_id,\n", "au2.email as tea_rule_updated_by,\n", "date(iotm.updated_at + interval '330' minute)\n", "as tea_updated_at,\n", "date(tr.updated_at + interval '330' minute)\n", "as tea_rule_updated_at,\n", "case when pm.item_id is not null then 'seller_item' end as \"seller_flag\"\n", "from rpc.product_facility_master_assortment pfma\n", "inner join fo on fo.facility_id = pfma.facility_id \n", "inner join rpc.item_outlet_tag_mapping iotm on iotm.outlet_id = fo.outlet_id  and iotm.item_id = pfma.item_id and tag_type_id = 8 and iotm.active = 1 and iotm.lake_active_record\n", "inner join po.physical_facility_outlet_mapping om on om.outlet_id = cast(iotm.tag_value as int)\n", "left join po.bulk_facility_outlet_mapping bfom on bfom.facility_id = om.facility_id and fo.outlet_id = bfom.outlet_id and bfom.active and bfom.lake_active_record\n", "inner join item_cat ic on ic.item_id = pfma.item_id\n", "left join rpc.transfer_tag_rules tr on tr.id = iotm.updated_by and tr.active and tr.lake_active_record\n", "left join retail.auth_user au2 on au2.id = tr.updated_by\n", "left join seller.seller_product_mappings pm on pm.item_id = pfma.item_id\n", "where pfma.master_assortment_substate_id in (1,3) and pfma.active = 1  and ic.category in ('COLD','GROCERY','MEDICINAL','PACKAGING_MATERIAL')\n", "--and pfma.item_id = 10137531 and fo.facility_id = 2089\n", "and bfom.facility_id is null\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25)\n", "\n", "\n", "select *, case when (rule_category = category and (longtail_be =  tag_value or express_be = tag_value)) then 'Manual' else 'Tech' end as \"Issue_bucket\" from final where be_facility_id != 0\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "77e069db-0681-46cb-9ac0-62d23f818806", "metadata": {}, "outputs": [], "source": ["data_raw = pd.read_sql_query(sql=data, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "3dd33b2f-1537-4ec0-9071-ce5cfcc6e303", "metadata": {}, "outputs": [], "source": ["data_raw.shape"]}, {"cell_type": "code", "execution_count": null, "id": "5c40cc66-8f45-45bf-afd2-8d0895926311", "metadata": {}, "outputs": [], "source": ["# data_raw2 = data_raw.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "0110b184-2b7a-4aba-8eaf-60724cee6043", "metadata": {}, "outputs": [], "source": ["data_raw2 = data_raw[data_raw[\"Issue_bucket\"] == \"Manual\"]"]}, {"cell_type": "code", "execution_count": null, "id": "f449198f-6666-491b-a654-f1ebd3122c8b", "metadata": {}, "outputs": [], "source": ["data_raw3 = data_raw[data_raw[\"Issue_bucket\"] == \"Tech\"]"]}, {"cell_type": "code", "execution_count": null, "id": "dcf9a38a-438b-4c5f-887f-a67251b58b4d", "metadata": {}, "outputs": [], "source": ["conditions = [\n", "    (data_raw2[\"item_id_tr\"] > 0) & (data_raw2[\"frontend_facility_id\"] > 0),\n", "    (data_raw2[\"item_id_tr\"] > 0) & (data_raw2[\"tr_city_id\"] > 0),\n", "    (data_raw2[\"manufacturer_id\"] > 0) & (data_raw2[\"frontend_facility_id\"] > 0),\n", "    (data_raw2[\"manufacturer_id\"] > 0) & (data_raw2[\"tr_city_id\"] > 0),\n", "    (data_raw2[\"frontend_facility_id\"] > 0),\n", "    (data_raw2[\"tr_city_id\"] > 0),\n", "]\n", "\n", "choices = [\n", "    \"Item-Frontend-Rule\",\n", "    \"Item-City-Rule\",\n", "    \"Manufacturer-Frontend-Rule\",\n", "    \"Manufacturer-City-Rule\",\n", "    \"Frontend-Rule\",\n", "    \"City-Rule\",\n", "]\n", "\n", "data_raw2[\"rule_to_change\"] = np.select(conditions, choices, default=None)"]}, {"cell_type": "code", "execution_count": null, "id": "2eacf7c9-7b88-4976-884c-48c6d8607b55", "metadata": {}, "outputs": [], "source": ["data_raw2"]}, {"cell_type": "code", "execution_count": null, "id": "b4e1bf23-1bd7-4223-af0e-4da78522daa3", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    data_raw2,\n", "    \"1D-znUUl6DZ4vLVgUyu1wj09z0HpWrxv3PGhJf9OIUMc\",\n", "    \"tea tagging issues\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f5f0a387-ad90-4d88-972c-aad2e6143527", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    data_raw3,\n", "    \"19NF3Bud0ZJqj2bTt5meGMN_CLyZN5hbHjx6RnB0tkH8\",\n", "    \"tea tagging tech issues\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5d949adf-4ac3-44f9-b033-a654bf6c88f6", "metadata": {}, "outputs": [], "source": ["if data_raw2.shape[0] > 0:\n", "    channel = alert_channel\n", "    text = \"<@U06CEPG9RAP><@U03RGPZ1Q9L> Check and correct the tea tagging : Detailed sheet here - <https://docs.google.com/spreadsheets/d/1D-znUUl6DZ4vLVgUyu1wj09z0HpWrxv3PGhJf9OIUMc/edit?gid=0#gid=0 | Tea tagging mismatches>\"\n", "else:\n", "    print(\"<PERSON><PERSON> failed\")\n", "\n", "pb.send_slack_message(channel=channel, text=text)"]}, {"cell_type": "code", "execution_count": null, "id": "6908984e-3a5c-4c40-aee8-bb929478f511", "metadata": {}, "outputs": [], "source": ["if data_raw3.shape[0] > 0:\n", "    channel = alert_channel_tech\n", "    text = \"<@U06Q6SGUUMD><@U03RR41CKQX> Tech issues in tea tagging : Detailed sheet here - <https://docs.google.com/spreadsheets/d/19NF3Bud0ZJqj2bTt5meGMN_CLyZN5hbHjx6RnB0tkH8/edit?gid=0#gid=0 | Tea tagging mismatches>\"\n", "else:\n", "    print(\"<PERSON><PERSON> failed\")\n", "\n", "pb.send_slack_message(channel=channel, text=text)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}