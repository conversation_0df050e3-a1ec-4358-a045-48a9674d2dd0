{"cells": [{"cell_type": "code", "execution_count": null, "id": "97d46f6f-d239-4553-842b-aea682a84abe", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import requests\n", "import json"]}, {"cell_type": "code", "execution_count": null, "id": "f0e42e54-c02f-4849-b4e9-6b06fc477fc1", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "raw", "id": "a8b74756-3a7a-4f07-ae9c-9a47cc791f80", "metadata": {}, "source": ["Logic : \n", "1. First take out the existing mapping data from the table \n", "2. Take out new mapping data\n", "3. Add a flag to new mapping to keep\n", "4. From existing mapping select the ones where LT = adjacent facility\n", "5. If any entry exists where any LT store is adjacent facility of any other LT store drop the row where that store is adjacent facility of any other store\n", "6. <PERSON><PERSON>ve all other entries"]}, {"cell_type": "markdown", "id": "36f7ee06-1aa6-4970-b441-659f2222b933", "metadata": {}, "source": ["### take out existing table data"]}, {"cell_type": "code", "execution_count": null, "id": "65412ee1-3ace-4747-a8b6-e9d78a910e7c", "metadata": {}, "outputs": [], "source": ["existing_mapping_query = \"\"\"\n", "select long_tail_facility_id as longtail_facility_id, adjacent_facility_id as express_facility_id  from po.long_tail_facility_store_mapping where active = 1\n", "\"\"\"\n", "existing_mapping_query_raw = pd.read_sql_query(sql=existing_mapping_query, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "83a34f3b-e0ed-47fd-b7c5-c30009192c8f", "metadata": {}, "outputs": [], "source": ["existing_mapping_query_raw2 = existing_mapping_query_raw.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "a98fb121-d638-4be8-be64-981661fc84e7", "metadata": {}, "outputs": [], "source": ["existing_mapping_query_raw2.loc[:, \"existing_flag\"] = \"existing\""]}, {"cell_type": "code", "execution_count": null, "id": "d72c1c4d-56bb-4d2d-9460-fdb353deda86", "metadata": {}, "outputs": [], "source": ["# existing_mapping_query_raw[\"flag\"] = np.where(\n", "#     existing_mapping_query_raw[\"longtail_facility_id\"]\n", "#     == existing_mapping_query_raw[\"express_facility_id\"],\n", "#     1,\n", "#     0,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "b50fbe78-85a4-4759-a240-f51f1837d903", "metadata": {}, "outputs": [], "source": ["existing_mapping_query_raw2 = existing_mapping_query_raw2[\n", "    existing_mapping_query_raw2[\"longtail_facility_id\"]\n", "    != existing_mapping_query_raw2[\"express_facility_id\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "47791c24-8867-4c4e-a4a2-03a1945247ca", "metadata": {}, "outputs": [], "source": ["existing_mapping_query_raw2.head()"]}, {"cell_type": "markdown", "id": "de79da4b-6ca3-4809-9455-d161c22b9e38", "metadata": {}, "source": ["### take out new mapping from query for polygon coverage"]}, {"cell_type": "code", "execution_count": null, "id": "5836031e-981d-4832-8488-cfdfe3058378", "metadata": {}, "outputs": [], "source": ["new_mapping_query = \"\"\"\n", "with longtail_express_merchant_mapping as (WITH fo AS (\n", "    SELECT \n", "        om.facility_id,\n", "        om.outlet_id,\n", "        om.city_id,\n", "        mo_map.frontend_merchant_id AS merchant_id,\n", "        outlet_name\n", "    FROM \n", "        po.physical_facility_outlet_mapping om\n", "        INNER JOIN dwh.dim_merchant_outlet_facility_mapping mo_map ON mo_map.facility_id = om.facility_id\n", "   AND is_current\n", "   AND is_current_mapping_active\n", "   AND is_backend_merchant_active\n", "   AND is_frontend_merchant_active\n", "    WHERE \n", "        active = 1\n", "        AND ars_active = 1\n", "        AND om.facility_id IN (\n", "            SELECT facility_id\n", "            FROM lake_retail.console_outlet\n", "            WHERE business_type_id = 7 AND company_type_id NOT IN (771,\n", "                                   767)\n", "        )\n", "        AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "     AND om.outlet_name NOT LIKE '%%Draft%%'\n", "     AND om.outlet_name NOT LIKE '%%Test Store%%'\n", "     AND om.outlet_name NOT LIKE '%%Dummy%%'\n", "     and lower(om.outlet_name) NOT LIKE '%%HP%%'\n", "     AND om.facility_id != 273 \n", "        --and om.facility_id = 976\n", "    GROUP BY \n", "        1,2,3,4,5\n", "),\n", "base AS (SELECT \n", "    a.merchant_id as longtail_merchant_id, \n", "    b.merchant_id as express_merchant_id,\n", "     (st_area(st_intersection(ST_GeometryFromText(b.polygon),ST_GeometryFromText(a.polygon))))*100 / st_area(ST_GeometryFromText(b.polygon)) as perc_area_covered_of_express_merchant\n", "FROM\n", "    serviceability.ser_store_polygons a\n", "JOIN \n", "    serviceability.ser_store_polygons b on a.id != b.id\n", "    \n", "WHERE \n", "    a.type='LONGTAIL_POLYGON'\n", "    and b.type='STORE_POLYGON'\n", "    and a.is_active=true \n", "    and b.is_active=true \n", "    and st_intersects(ST_GeometryFromText(b.polygon), ST_GeometryFromText(a.polygon))\n", "GROUP BY 1,2,3\n", "order by 1,2,3)\n", "\n", "select b.*, fo.facility_id as express_facility_id, fo2.facility_id as longtail_facility_id from base b\n", "inner join fo on fo.merchant_id = cast(b.express_merchant_id as int)\n", "inner join fo as fo2 on fo2.merchant_id = cast(b.longtail_merchant_id as int)\n", "--where express_merchant_id = '31283'\n", ")\n", "\n", "--select * from longtail_express_merchant_mapping \n", "\n", "SELECT ce.longtail_facility_id,\n", "       ce.express_facility_id\n", "FROM longtail_express_merchant_mapping AS ce\n", "JOIN (\n", "    SELECT express_facility_id\n", "    FROM longtail_express_merchant_mapping\n", "    GROUP BY express_facility_id\n", "    HAVING SUM(perc_area_covered_of_express_merchant) > 70\n", ") AS subquery ON ce.express_facility_id = subquery.express_facility_id\n", "group by 1,2\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "a4d17318-ae90-4f24-943d-619e8c6be33a", "metadata": {}, "outputs": [], "source": ["new_mapping_query_raw = pd.read_sql_query(sql=new_mapping_query, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "a473d725-e0d4-4faf-860d-8e58e308a2f2", "metadata": {}, "outputs": [], "source": ["new_mapping_query_raw = new_mapping_query_raw[\n", "    new_mapping_query_raw[\"longtail_facility_id\"] != new_mapping_query_raw[\"express_facility_id\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "62445ec4-2372-4317-8f72-53e783b135ae", "metadata": {}, "outputs": [], "source": ["new_mapping_query_raw.loc[:, \"new_flag\"] = \"new\""]}, {"cell_type": "code", "execution_count": null, "id": "72bf043e-79ea-4248-8cbc-521af5717e04", "metadata": {}, "outputs": [], "source": ["new_mapping_query_raw.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6647dfc3-625c-4d31-ab9e-6633240a0b1b", "metadata": {}, "outputs": [], "source": ["new_mapping_query_raw2 = new_mapping_query_raw.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "f5deceb1-d872-4b7c-8771-0d2e946c43fe", "metadata": {}, "outputs": [], "source": ["## taking out the lines which we need to delete- present in older datframe but not in new dataframe\n", "## left join new df on old df and take out the null values"]}, {"cell_type": "code", "execution_count": null, "id": "bb5646ec-8084-4848-a4fd-e0741de4030e", "metadata": {}, "outputs": [], "source": ["mapping_to_keep = existing_mapping_query_raw2.merge(\n", "    new_mapping_query_raw2, how=\"left\", on=[\"longtail_facility_id\", \"express_facility_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "67c2fc04-b478-4b0b-a8a6-ecaafc7287dc", "metadata": {}, "outputs": [], "source": ["remove = mapping_to_keep[mapping_to_keep[\"new_flag\"].isna()]"]}, {"cell_type": "code", "execution_count": null, "id": "1c096414-e4c7-4fc4-9b62-dcd1feb7059b", "metadata": {}, "outputs": [], "source": ["mapping_to_keep2 = new_mapping_query_raw2.merge(\n", "    existing_mapping_query_raw2, how=\"left\", on=[\"longtail_facility_id\", \"express_facility_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2a80b158-69c6-4371-bdff-c96d06c059c0", "metadata": {}, "outputs": [], "source": ["add = mapping_to_keep2[mapping_to_keep2[\"existing_flag\"].isna()]"]}, {"cell_type": "code", "execution_count": null, "id": "5435b236-67b7-4763-96b6-9c44f9d453fb", "metadata": {}, "outputs": [], "source": ["if add.shape[0] > 0:\n", "    add.loc[:, \"flag\"] = 1"]}, {"cell_type": "code", "execution_count": null, "id": "37bd35af-2c52-4c14-9561-103bddb0322b", "metadata": {}, "outputs": [], "source": ["add"]}, {"cell_type": "code", "execution_count": null, "id": "586d9234-0bf0-4808-9a11-851ea58aaca3", "metadata": {}, "outputs": [], "source": ["if remove.shape[0] > 0:\n", "    remove.loc[:, \"flag\"] = 0"]}, {"cell_type": "code", "execution_count": null, "id": "a6818c68-9cc7-49c4-b140-ae62c80d20a1", "metadata": {}, "outputs": [], "source": ["remove"]}, {"cell_type": "code", "execution_count": null, "id": "c486aa8d-1f57-41e4-b68b-e7dd66618469", "metadata": {}, "outputs": [], "source": ["final_state = add.append(remove)"]}, {"cell_type": "code", "execution_count": null, "id": "6d71d10e-fc42-47dd-b420-2da1d3ad61fe", "metadata": {}, "outputs": [], "source": ["final_state.drop(columns=[\"new_flag\", \"existing_flag\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "778ba579-f9c5-4f55-a40c-e5754d36462c", "metadata": {}, "outputs": [], "source": ["final_state = final_state.append(existing_mapping_query_raw)"]}, {"cell_type": "code", "execution_count": null, "id": "7fbf9a4c-44bb-4611-b638-bc09ced61b22", "metadata": {}, "outputs": [], "source": ["final_state.drop_duplicates(\n", "    subset=[\"longtail_facility_id\", \"express_facility_id\"], keep=\"first\", inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c15800d5-4b3b-48b4-872e-6b862e0608c9", "metadata": {}, "outputs": [], "source": ["merged_df = final_state.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "f1e13bc9-cfd8-4606-adef-dbd8dc3bd17f", "metadata": {}, "outputs": [], "source": ["merged_df[merged_df[\"flag\"] == 0].shape"]}, {"cell_type": "code", "execution_count": null, "id": "82003699-9644-4ace-b3ad-1a459689e43d", "metadata": {}, "outputs": [], "source": ["# Find all unique `longtail_facility_id` values\n", "longtail_set = set(merged_df[\"longtail_facility_id\"])\n", "\n", "# Update the flag column\n", "merged_df[\"flag\"] = merged_df.apply(\n", "    lambda row: 0\n", "    if row[\"express_facility_id\"] in longtail_set\n", "    and row[\"longtail_facility_id\"] != row[\"express_facility_id\"]\n", "    else row[\"flag\"],\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c34e453f-aff0-468a-944b-a2fb09d31f65", "metadata": {}, "outputs": [], "source": ["final_df = merged_df[merged_df[\"flag\"].notna()]"]}, {"cell_type": "code", "execution_count": null, "id": "92a1ecaf-361e-414c-8859-f3d91dd8ccb2", "metadata": {}, "outputs": [], "source": ["final_df.rename(columns={\"flag\": \"active\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "ef2460cf-db66-474f-b23f-46e43623f451", "metadata": {}, "outputs": [], "source": ["final_df"]}, {"cell_type": "code", "execution_count": null, "id": "db25d2c2-0beb-4c51-b3da-a03127789c8f", "metadata": {}, "outputs": [], "source": ["for idx, row in final_df.iterrows():\n", "    url = \"https://retail-internal.grofer.io/po/v1/update-longtail-facility-adjacent-facility-mapping/\"\n", "\n", "    payload = json.dumps(\n", "        {\n", "            \"longtail_facility_id\": int(row[\"longtail_facility_id\"]),\n", "            \"adjacent_facility_id\": int(row[\"express_facility_id\"]),\n", "            \"active\": int(row[\"active\"]),\n", "        }\n", "    )\n", "    headers = {\"Content-Type\": \"application/json\"}\n", "\n", "    response = requests.request(\"POST\", url, headers=headers, data=payload)\n", "    print(row)\n", "    print(response.text)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}