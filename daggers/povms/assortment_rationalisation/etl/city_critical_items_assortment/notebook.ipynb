{"cells": [{"cell_type": "code", "execution_count": null, "id": "d738251e-76d5-44ad-904c-f6706fc1214f", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import requests\n", "import json"]}, {"cell_type": "code", "execution_count": null, "id": "eccd38b4-89d7-4084-86be-b334a63bb46c", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "c384f223-19b5-4926-921c-82af9447eb5c", "metadata": {}, "outputs": [], "source": ["## current filters are non-perishable, non-medicinal/packaging, non-frozen non seller items\n", "## non bistro but that is already removed from catalog"]}, {"cell_type": "code", "execution_count": null, "id": "d557328e-ea26-4700-a171-8ef09c682785", "metadata": {"tags": []}, "outputs": [], "source": ["filters = {\n", "    \"item_type\": [\"PACKAGED\"],\n", "    \"handling_type\": [\"REGULAR\"],\n", "    \"storage_type\": [\"HEAVY\", \"COLD\", \"REGULAR\"],\n", "    \"item_flag\": [\"blinkit_item\"],\n", "    \"critical_skus_perc_cut\": [0.85],\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "32f92faa-17cd-44a5-8273-bdd716ea73c2", "metadata": {}, "outputs": [], "source": ["frontend_outlets = f\"\"\"\n", " \n", "  SELECT\n", "    om.facility_id,\n", "    om.outlet_id AS outlet_id,\n", "    mo_map.frontend_merchant_id AS merchant_id,\n", "    om.outlet_name AS outlet_name,\n", "    rcl.id as city_id,\n", "    rcl.name AS city_name,\n", "    case when type_id = 3 then 'LONGTAIL_ONLY' else 'EXPRESS' end as store_type,\n", "    case when long_tail_facility_id is not null then 'LONGTAIL' else 'EXPRESS_ONLY' end as store_type_ams\n", "  FROM\n", "    po.physical_facility_outlet_mapping om\n", "    INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "    AND rco.business_type_id IN (7)\n", "    AND rco.company_type_id NOT IN (771, 767)\n", "    AND rco.lake_active_record\n", "    INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "    AND rcl.lake_active_record\n", "    INNER JOIN dwh.dim_merchant_outlet_facility_mapping mo_map ON mo_map.facility_id = om.facility_id\n", "    AND rco.business_type_id = 7\n", "    AND is_current\n", "    AND is_current_mapping_active\n", "    AND is_backend_merchant_active\n", "    AND is_frontend_merchant_active\n", "    INNER JOIN (\n", "      SELECT\n", "        distinct cast(merchant_id AS int) merchant_id\n", "      FROM\n", "        serviceability.ser_store_polygons\n", "      WHERE\n", "        TYPE in ('STORE_POLYGON', 'LONGTAIL_POLYGON')\n", "        AND is_active = TRUE\n", "        AND lake_active_record\n", "    ) poly ON poly.merchant_id = mo_map.frontend_merchant_id\n", "    left join (select distinct long_tail_facility_id from po.long_tail_facility_store_mapping where active = 1) long_\n", "    on long_.long_tail_facility_id = om.facility_id\n", "  WHERE\n", "    om.active = 1\n", "    AND om.ars_active = 1\n", "    AND om.lake_active_record\n", "    AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "    AND om.outlet_name NOT LIKE '%%Draft%%'\n", "    AND om.outlet_name NOT LIKE '%%Test Store%%'\n", "    AND om.outlet_name NOT LIKE '%%Dummy%%'\n", "    AND om.facility_id not in (273,2943)\n", "    AND om.outlet_id IN (\n", "      SELECT\n", "        DISTINCT outlet_id\n", "      FROM\n", "        po.bulk_facility_outlet_mapping\n", "      WHERE\n", "        active\n", "        AND lake_active_record\n", "    )\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "8f01de27-f55f-4a9e-b3ae-af121d19eb71", "metadata": {}, "outputs": [], "source": ["frontend_outlets_raw = pd.read_sql_query(sql=frontend_outlets, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "73a57e92-4d18-4825-a300-4304ceb08f3e", "metadata": {}, "outputs": [], "source": ["frontend_outlets_raw"]}, {"cell_type": "code", "execution_count": null, "id": "bfb6be5f-b000-4810-9bbf-e67b5f82fa2f", "metadata": {}, "outputs": [], "source": ["item_catalog = f\"\"\"\n", "  SELECT\n", "    *\n", "  FROM\n", "    (\n", "      SELECT\n", "        icd.item_id,\n", "        icd.name,\n", "        icd.l0_id,\n", "        icd.l0,\n", "        icd.l1_id,\n", "        icd.l1,\n", "        icd.l2_id,\n", "        icd.l2,\n", "        icd.product_type_id,\n", "        icd.product_type,\n", "        CASE\n", "          WHEN id.perishable = 1 THEN 'PERISHABLE'\n", "          ELSE 'PACKAGED'\n", "        END AS item_type,\n", "        id.storage_type AS storage_type_raw,\n", "        CASE\n", "          WHEN id.storage_type IN (\n", "            '1',\n", "            '8',\n", "            '11'\n", "          ) THEN 'REGULAR'\n", "          WHEN id.storage_type IN ('4', '5') THEN 'HEAVY'\n", "          WHEN id.storage_type IN ('2', '6') THEN 'COLD'\n", "          WHEN id.storage_type IN ('3', '7') THEN 'FROZEN'\n", "          ELSE 'REGULAR'\n", "        END AS storage_type,\n", "        CASE\n", "          WHEN id.handling_type IN ('8') THEN 'PACKAGING MATERIAL'\n", "          WHEN id.handling_type IN ('6') THEN 'MEDICINAL'\n", "          ELSE 'REGULAR'\n", "        END AS handling_type,\n", "        id.variant_mrp AS mrp,\n", "        id.variant_description,\n", "        id.weight_in_gm,\n", "        id.length_in_cm,\n", "        id.height_in_cm,\n", "        id.breadth_in_cm,\n", "        id.shelf_life,\n", "        coalesce(itf.item_factor, 0.01) AS item_factor,\n", "        DATE_DIFF('day', date(icd.created_at), CURRENT_DATE) AS item_catalog_age,\n", "        rank() OVER (\n", "          PARTITION BY id.item_id\n", "          ORDER BY\n", "            id.id DESC\n", "        ) AS variant_rank,\n", "        itm.tag_value AS custom_storage_type_raw,\n", "        case\n", "          when itm.tag_value = '1' then 'BEAUTY'\n", "          when itm.tag_value = '2' then 'BOUQUET'\n", "          when itm.tag_value = '3' then 'PREMIUM'\n", "          when itm.tag_value = '4' then 'BOOKS'\n", "          when itm.tag_value = '5' then 'NON_VEG'\n", "          when itm.tag_value = '6' then 'ICE_CREAM'\n", "          when itm.tag_value = '7' then 'TOYS'\n", "          when itm.tag_value = '8' then 'ELECTRONICS' -- when itm.tag_value = '9' then 'FESTIVE'\n", "          when itm.tag_value = '10' then 'VERTICAL_CHUTES'\n", "          when itm.tag_value = '11' then 'BEST_SERVED_COLD'\n", "          when itm.tag_value = '12' then 'CRITICAL_SKUS'\n", "          when itm.tag_value = '13' then 'LARGE'\n", "          when itm.tag_value = '14' then 'APPAREL'\n", "          when itm.tag_value = '15' then 'SPORTS'\n", "          when itm.tag_value = '16' then 'PET_CARE'\n", "          when itm.tag_value = '17' then 'HOME_DECOR'\n", "          when itm.tag_value = '18' then 'KITCHEN_DINING'\n", "          when itm.tag_value = '19' then 'HOME_FURNISHING'\n", "          when itm.tag_value = '20' then 'LONGTAIL_OTHERS'\n", "          when itm.tag_value = '21' then 'PHARMA'\n", "          when itm.tag_value = '22' then 'PAAS'\n", "          when itm.tag_value = '23' then 'PALLET'\n", "          when itm.tag_value = '24' then 'LARGE_FRAGILE'\n", "          WHEN itm.tag_value IS NULL THEN 'NO_CONFIG'\n", "          ELSE 'UNKNOWN_CONFIG'\n", "        END AS custom_storage_type,\n", "        pb.manufacturer_id,\n", "        id.manufacturer AS manufacturer_name,\n", "        pb.name AS brand_name,\n", "        coalesce(id.outer_case_size, 1) AS outer_case_size,\n", "        coalesce(id.inner_case_size, 1) AS inner_case_size,\n", "        CASE\n", "          WHEN itm2.tag_value = '1' THEN TRUE\n", "          ELSE FALSE\n", "        END AS is_high_value,\n", "        itm2.tag_value AS high_value_tag_raw,\n", "        CASE\n", "          WHEN itm3.tag_value = '1' THEN 'upc scan'\n", "          WHEN itm3.tag_value = '2' THEN 'serial scan'\n", "          WHEN itm3.tag_value = '3' THEN 'qr scan'\n", "          WHEN itm3.tag_value = '4' THEN 'no scan'\n", "          ELSE 'unknown'\n", "        END AS scan_type,\n", "        itm3.tag_value AS scan_type_raw,\n", "        cms_food_type,\n", "        case when spm.item_id is not null then 'seller_item' else 'blinkit_item' end as \"item_flag\"\n", "      FROM\n", "        rpc.item_category_details icd\n", "        INNER JOIN rpc.product_product id ON id.item_id = icd.item_id\n", "        AND id.active = 1\n", "        AND id.approved = 1\n", "        AND id.lake_active_record\n", "        LEFT JOIN supply_etls.item_factor itf ON itf.item_id = icd.item_id\n", "        LEFT JOIN rpc.item_tag_mapping itm ON itm.tag_type_id = 7\n", "        AND itm.active = TRUE\n", "        AND itm.lake_active_record\n", "        AND itm.item_id = icd.item_id\n", "        LEFT JOIN rpc.product_brand pb ON id.brand_id = pb.id\n", "        AND pb.lake_active_record\n", "        AND pb.active = 1\n", "        LEFT JOIN rpc.item_tag_mapping itm2 ON itm2.item_id = icd.item_id\n", "        AND itm2.active\n", "        AND itm2.tag_type_id = 3\n", "        AND itm2.lake_active_record\n", "        LEFT JOIN rpc.item_tag_mapping itm3 ON itm3.item_id = icd.item_id\n", "        AND itm3.active\n", "        AND itm3.tag_type_id = 5\n", "        AND itm3.lake_active_record\n", "        left join seller.seller_product_mappings spm on spm.item_id = id.item_id\n", "      WHERE\n", "        icd.lake_active_record\n", "        AND perishable != 1\n", "        AND id.handling_type != '8'\n", "        AND id.storage_type NOT IN ('3', '7')\n", "        AND icd.l0_id != 1487 -- removing vegetables and fruits\n", "        AND icd.l0 NOT IN (\n", "          'wholesale store',\n", "          'Trial new tree',\n", "          'Specials',\n", "          'Bistro',\n", "          'HP', 'Digital Goods'\n", "        ) -- removing test and flyer/freebie l0s and bistro\n", "    ) AS x\n", "  WHERE\n", "    variant_rank = 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "0d2feef2-50aa-4d7c-9790-b639b38926ce", "metadata": {}, "outputs": [], "source": ["item_catalog_raw = pd.read_sql_query(sql=item_catalog, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "8f179f39-a738-4575-b94b-6b4cdc58e627", "metadata": {}, "outputs": [], "source": ["item_catalog_raw.head()"]}, {"cell_type": "code", "execution_count": null, "id": "86e5b8d9-794b-472d-803d-22ae454b4607", "metadata": {}, "outputs": [], "source": ["item_catalog_filtered = item_catalog_raw[\n", "    item_catalog_raw[\"item_type\"].isin(filters[\"item_type\"])\n", "    & item_catalog_raw[\"handling_type\"].isin(filters[\"handling_type\"])\n", "    & item_catalog_raw[\"storage_type\"].isin(filters[\"storage_type\"])\n", "    & item_catalog_raw[\"item_flag\"].isin(filters[\"item_flag\"])\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "820f28e1-d2a9-4c34-82a2-0101a3e513fb", "metadata": {}, "outputs": [], "source": ["## adding city Hybrid flag"]}, {"cell_type": "code", "execution_count": null, "id": "7be56690-0fe1-4b9c-8927-1ad88c8b9b5c", "metadata": {}, "outputs": [], "source": ["city_hybrid = f\"\"\"\n", "select pfma.item_id, pfma.city_id, launch_type\n", "FROM\n", "    rpc.product_facility_master_assortment pfma\n", "where pfma.active = 1\n", "and pfma.lake_active_record\n", "and launch_type = 'HYBRID'\n", "group by 1,2,3\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "bd397c9a-61b9-49c3-bf4e-efd266882a29", "metadata": {}, "outputs": [], "source": ["city_hybrid_raw = pd.read_sql_query(sql=city_hybrid, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "6243bf8e-85b2-41e0-8ed5-2e2dfba95ba4", "metadata": {}, "outputs": [], "source": ["city_hybrid_raw"]}, {"cell_type": "code", "execution_count": null, "id": "c5a8cd52-3e77-4099-b860-2d881affc79a", "metadata": {}, "outputs": [], "source": ["pfma_mapping = f\"\"\"\n", "select\n", "fe_city_id,\n", "fe_city_name,\n", "item_id,\n", "store_assortment_type,\n", "assortment_status_id,\n", "count(fe_facility_id) as fe_facility_count\n", "from supply_etls.inventory_metrics_tea_tagging where \n", "handling_type = 'Non Packaging Material' \n", "and assortment_type = 'Packaged Goods' \n", "and assortment_status_id in (1,2,3,4)\n", "and fe_facility_id in (select facility_id from po.physical_facility_outlet_mapping where active=1 and ars_active=1\n", "        and facility_id in (select facility_id from lake_retail.console_outlet where business_type_id = 7)\n", "        ) group by 1,2,3,4,5 \"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "31d09e92-4569-47b2-b92c-6c1569ff85e8", "metadata": {}, "outputs": [], "source": ["pfma_mapping_raw = pd.read_sql_query(sql=pfma_mapping, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "382ec3e7-b10e-49af-b5cc-09232e33a1c9", "metadata": {}, "outputs": [], "source": ["# Step 1: Convert columns to categories if there are many repeated values (saves memory)\n", "pfma_mapping_raw[\"fe_city_name\"] = pfma_mapping_raw[\"fe_city_name\"].astype(\"category\")\n", "pfma_mapping_raw[\"store_assortment_type\"] = pfma_mapping_raw[\"store_assortment_type\"].astype(\n", "    \"category\"\n", ")\n", "pfma_mapping_raw[\"assortment_status_id\"] = pfma_mapping_raw[\"assortment_status_id\"].astype(\n", "    \"int8\"\n", ")  # if values are small\n", "\n", "# Step 2: Use sort + drop_duplicates (faster than groupby+idxmax on large datasets)\n", "pfma_mapping_sorted = pfma_mapping_raw.sort_values(\n", "    by=[\"item_id\", \"fe_city_name\", \"fe_city_id\", \"fe_facility_count\"],\n", "    ascending=[True, True, True, False],\n", "    kind=\"stable\",  # stable sort is faster for large frames\n", ")\n", "\n", "# Step 3: Drop duplicates keeping only top record per group\n", "pfma_top_facility = pfma_mapping_sorted.drop_duplicates(\n", "    subset=[\"item_id\", \"fe_city_name\", \"fe_city_id\"], keep=\"first\"\n", ")[\n", "    [\n", "        \"item_id\",\n", "        \"fe_city_name\",\n", "        \"fe_city_id\",\n", "        \"store_assortment_type\",\n", "        \"assortment_status_id\",\n", "        \"fe_facility_count\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "85fbe433-378e-44b9-ba8c-15d7ab71ef3d", "metadata": {}, "outputs": [], "source": ["pfma_top_facility2 = pfma_top_facility.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "ffaed3b3-ac1d-4542-9a50-37c4c61f86ba", "metadata": {}, "outputs": [], "source": ["pfma_top_facility2.rename(columns={\"fe_city_id\": \"city_id\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "76febcd2-4ff1-4b45-be74-c32c1a8fa03a", "metadata": {}, "outputs": [], "source": ["pfma_top_facility2 = pfma_top_facility2.merge(\n", "    city_hybrid_raw, on=[\"item_id\", \"city_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "bcffd24d-3951-4f9b-bd5d-5b4cb0fb153d", "metadata": {}, "outputs": [], "source": ["pfma_top_facility2 = pfma_top_facility2.merge(\n", "    item_catalog_filtered[\n", "        [\n", "            \"item_id\",\n", "            \"name\",\n", "            \"l0\",\n", "            \"l1\",\n", "            \"l2\",\n", "            \"product_type\",\n", "            \"item_type\",\n", "            \"storage_type_raw\",\n", "            \"storage_type\",\n", "            \"handling_type\",\n", "            \"mrp\",\n", "            \"is_high_value\",\n", "            \"high_value_tag_raw\",\n", "            \"scan_type\",\n", "            \"scan_type_raw\",\n", "            \"cms_food_type\",\n", "            \"item_flag\",\n", "        ]\n", "    ],\n", "    how=\"left\",\n", "    on=[\"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "eb906f3a-7957-42f5-b4be-873c3be9780c", "metadata": {}, "outputs": [], "source": ["pfma_top_facility2 = pfma_top_facility2[pfma_top_facility2[\"name\"].notna()]"]}, {"cell_type": "code", "execution_count": null, "id": "a75e8ccb-aecb-46ad-a787-eceeb13a5aaf", "metadata": {}, "outputs": [], "source": ["city_weights = f\"\"\"\n", "with base as (\n", "  select\n", "    city,\n", "    item_id,\n", "    cart_penetration,\n", "    max(try_cast(weights as double)) as weight\n", "  from\n", "    supply_etls.city_item_weights ciw\n", "  where\n", "    assortment_type = 'Packaged Goods'\n", "    and updated_at = (\n", "      select\n", "        max(updated_at)\n", "      from\n", "        supply_etls.city_item_weights\n", "      where\n", "        assortment_type = 'Packaged Goods'\n", "    )\n", "    and item_id not in (\n", "      select\n", "        item_id\n", "      from\n", "        supply_etls.active_sampling_sku\n", "      where\n", "        updated_at = (\n", "          select\n", "            max(updated_at)\n", "          from\n", "            supply_etls.active_sampling_sku\n", "        )\n", "    )\n", "  group by\n", "    1,\n", "    2,\n", "    3\n", ")\n", "\n", "select b.*, id as city_id from base b inner join retail.console_location cl on cl.name = b.city\n", "and lake_active_record\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "743ed14d-72af-4b30-bd19-ac28c18436b9", "metadata": {}, "outputs": [], "source": ["city_weights_raw = pd.read_sql_query(sql=city_weights, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "5b0bc71c-5546-4882-a532-8f3070f76842", "metadata": {}, "outputs": [], "source": ["city_weights_raw2 = city_weights_raw.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "95d7baec-e511-40a5-bcd0-4fa3db5879a6", "metadata": {}, "outputs": [], "source": ["city_weights_raw2[[\"weight\", \"cart_penetration\"]] = city_weights_raw2[\n", "    [\"weight\", \"cart_penetration\"]\n", "].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "229e3a4f-1529-41cb-baea-f0d1941ef49a", "metadata": {}, "outputs": [], "source": ["# Step 1: Sort\n", "city_weights_raw2 = city_weights_raw2.sort_values(\n", "    by=[\"city\", \"city_id\", \"weight\"], ascending=[True, True, False]\n", ")\n", "\n", "# Step 2: Total weight per city\n", "city_weights_raw2[\"total_weight\"] = city_weights_raw2.groupby([\"city\", \"city_id\"])[\n", "    \"weight\"\n", "].transform(\"sum\")\n", "\n", "# Step 3: Cumulative weight per city\n", "city_weights_raw2[\"cum_weight\"] = city_weights_raw2.groupby([\"city\", \"city_id\"])[\"weight\"].cumsum()\n", "\n", "# Step 4: Cumulative weight % and critical flag\n", "city_weights_raw2[\"cum_weight_pct\"] = (\n", "    city_weights_raw2[\"cum_weight\"] / city_weights_raw2[\"total_weight\"]\n", ")\n", "city_weights_raw2[\"critical_skus\"] = (\n", "    city_weights_raw2[\"cum_weight_pct\"] <= filters[\"critical_skus_perc_cut\"][0]\n", ").astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "512b58c6-bfa2-40f3-b0d1-7b6654f5e2a7", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit = city_weights_raw2[city_weights_raw2[\"critical_skus\"] == 1]"]}, {"cell_type": "code", "execution_count": null, "id": "28e43846-57ea-452f-939a-a3c<PERSON><PERSON>baacb", "metadata": {}, "outputs": [], "source": ["city_weights_raw2[city_weights_raw2[\"critical_skus\"] == 1].groupby([\"city\", \"critical_skus\"]).agg(\n", "    item_count=(\"item_id\", \"count\")\n", ").reset_index().sort_values(\"item_count\", ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "032be209-7d86-44fb-ad19-a14ab0d422a5", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0ea91385-bf3e-4171-aead-ffc2141c2a91", "metadata": {}, "outputs": [], "source": ["pfma_top_facility2 = pfma_top_facility2.merge(\n", "    city_weights_raw_crit[[\"city_id\", \"item_id\", \"critical_skus\"]],\n", "    how=\"left\",\n", "    on=[\"city_id\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "581ab849-63d9-4293-b4f0-b547178fa8cf", "metadata": {}, "outputs": [], "source": ["pfma_top_facility2 = pfma_top_facility2[pfma_top_facility2[\"critical_skus\"] == 1]"]}, {"cell_type": "code", "execution_count": null, "id": "0574a1fe-6c0f-4e1c-a671-8a6e2ab8b28f", "metadata": {}, "outputs": [], "source": ["critical_skus_base = pfma_top_facility2.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "c9194611-4a78-414e-8e48-94f10bb44674", "metadata": {}, "outputs": [], "source": ["critical_skus_base"]}, {"cell_type": "code", "execution_count": null, "id": "08c14a9e-0522-4f00-a0a3-4db7e30d8a1a", "metadata": {}, "outputs": [], "source": ["critical_skus_base.rename(\n", "    columns={\n", "        \"store_assortment_type\": \"city_assortment_type\",\n", "        \"assortment_status_id\": \"city_assortment_substate\",\n", "        \"launch_type\": \"hybrid_flag\",\n", "        \"name\": \"item_name\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c92e3908-c09b-42b2-aac7-e7ac5f71e4f2", "metadata": {}, "outputs": [], "source": ["critical_skus_base.drop(\n", "    columns={\"fe_facility_count\", \"high_value_tag_raw\", \"scan_type_raw\", \"storage_type_raw\"},\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2c22076b-9eac-4203-85f6-e04974776941", "metadata": {}, "outputs": [], "source": ["critical_skus_base[\"hybrid_flag\"] = critical_skus_base[\"hybrid_flag\"].fillna(\"NON_HYBRID\")"]}, {"cell_type": "code", "execution_count": null, "id": "19f256cd-03f3-466a-be58-94342c92a477", "metadata": {}, "outputs": [], "source": ["critical_skus_base[[\"fe_city_name\", \"city_assortment_type\"]] = critical_skus_base[\n", "    [\"fe_city_name\", \"city_assortment_type\"]\n", "].astype(str)\n", "critical_skus_base[\"city_assortment_substate\"] = critical_skus_base[\n", "    \"city_assortment_substate\"\n", "].astype(int)\n", "critical_skus_base[\"is_high_value\"] = critical_skus_base[\"is_high_value\"].astype(bool)"]}, {"cell_type": "code", "execution_count": null, "id": "6fa6b105-3266-4a72-b977-8d6915261f4b", "metadata": {}, "outputs": [], "source": ["critical_skus_base.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "bddf5142-dda8-4bdb-8356-d23c6eb4d1c2", "metadata": {}, "outputs": [], "source": ["logTable = \"city_critical_assortment_log\"\n", "mainTable = \"city_critical_assortment\""]}, {"cell_type": "code", "execution_count": null, "id": "6c99f9e4-5655-4a2d-8373-f252c206c5b9", "metadata": {}, "outputs": [], "source": ["def pushD<PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "    df,\n", "    tableName,\n", "    description=None,\n", "    primaryKeys=[\"insert_ds_ist\"],\n", "    load_type=\"upsert\",\n", "    environ=\"supply_etls\",\n", "    partition_key=[],\n", "):\n", "    from datetime import datetime, timezone, timedelta\n", "    import time\n", "    import pandas as pd\n", "\n", "    utc_now = datetime.now(timezone.utc)\n", "    ist_now = utc_now + <PERSON><PERSON><PERSON>(hours=5, minutes=30)\n", "    formatted_date = ist_now.strftime(\"%Y-%m-%d\")\n", "    df[\"insert_ds_ist\"] = formatted_date\n", "    df[\"updated_at\"] = pd.to_datetime(utc_now)\n", "    column_dtypes = [\n", "        {\n", "            \"name\": col,\n", "            \"type\": str(df[col].dtype)\n", "            .replace(\"int64\", \"integer\")\n", "            .replace(\"float64\", \"real\")\n", "            .replace(\"str\", \"varchar\")\n", "            .replace(\"datetime64[ns]\", \"timestamp(6)\")\n", "            .replace(\"object\", \"varchar\")\n", "            .replace(\"bool\", \"boolean\")\n", "            .replace(\"datetime64[ns, UTC]\", \"timestamp(6)\"),\n", "            \"description\": col,\n", "        }\n", "        for col in df.columns\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": environ,\n", "        \"table_name\": tableN<PERSON>,\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": primary<PERSON><PERSON><PERSON>,\n", "        \"load_type\": load_type,\n", "        \"table_description\": (description if description else tableName),\n", "    }\n", "\n", "    if len(partition_key) > 0:\n", "        kwargs[\"partition_key\"] = partition_key\n", "\n", "    import pencilbox as pb\n", "\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            pb.to_trino(data_obj=df, **kwargs)\n", "            return\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error pushDfToTrino (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to pushDfToTrino after {max_retries} attempts: {e}\")\n", "                return"]}, {"cell_type": "code", "execution_count": null, "id": "5ccd6d90-c588-42f6-a821-4be4ede844f0", "metadata": {}, "outputs": [], "source": ["pushDfToTrino(\n", "    critical_skus_base,\n", "    mainTable,\n", "    description=\"critical_skus_city_assortment_current_snapshot\",\n", "    primaryKeys=[\"fe_city_name\", \"city_id\"],\n", "    load_type=\"truncate\",\n", "    environ=\"supply_etls\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "822f91f2-0f46-4344-b9f1-a71e9be7d8a4", "metadata": {}, "outputs": [], "source": ["pushDfToTrino(\n", "    critical_skus_base,\n", "    logTable,\n", "    description=\"critical_skus_city_assortment_logs\",\n", "    load_type=\"append\",\n", "    environ=\"supply_etls\",\n", "    partition_key=[\"insert_ds_ist\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "615a0fba-92e4-4c92-9a4b-2627a0fb20b9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}