alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: critical_assortment_rationalisation
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RR41CKQX
path: povms/assortment_rationalisation/etl/critical_assortment_rationalisation
paused: false
pool: povms_pool
project_name: assortment_rationalisation
schedule:
  end_date: '2025-09-13T00:00:00'
  interval: 30 19 * * *
  start_date: '2025-06-16T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 4
