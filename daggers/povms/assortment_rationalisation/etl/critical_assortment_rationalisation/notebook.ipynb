{"cells": [{"cell_type": "code", "execution_count": null, "id": "52aeefa5-c4e4-43f1-bc8b-609260787386", "metadata": {}, "outputs": [], "source": ["!pip install geopy"]}, {"cell_type": "code", "execution_count": null, "id": "e8f80e69-dbfa-471c-9546-e8b179281f0d", "metadata": {}, "outputs": [], "source": ["!pip uninstall -y openpyxl\n", "!pip install openpyxl==3.1.5"]}, {"cell_type": "code", "execution_count": null, "id": "2f418cdf-7600-4251-b962-31dc445b2c24", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import requests\n", "import json\n", "from geopy.distance import geodesic\n", "import gc\n", "import time\n", "import datetime\n", "import openpyxl"]}, {"cell_type": "code", "execution_count": null, "id": "7241b857-b182-42bb-8489-005418b87aac", "metadata": {}, "outputs": [], "source": ["import sys"]}, {"cell_type": "code", "execution_count": null, "id": "f5c70367-4a93-48fa-87ff-417738b2a9ca", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "da9a43c8-a614-4e9e-ac45-9d9b472db936", "metadata": {}, "outputs": [], "source": ["filters = {\n", "    \"handling_type\": [\"REGULAR\"],\n", "    \"item_flag\": [\"blinkit_item\"],\n", "    \"critical_skus_perc_cut\": [0.85],\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "2a4d26a6-c09e-4cec-bb9d-c57e3c6bd720", "metadata": {}, "outputs": [], "source": ["BulkUploadTable = \"critical_skus_expansion_upload_log\"\n", "SisterCityTable = \"sister_city_emerging_cities\"\n", "CriticalAssortmentTable = \"city_critical_assortment_v2\"\n", "CriticalAssortmentLogTable = \"city_critical_assortment_log_v2\"\n", "NotActiveAssortmentReasons = \"critical_skus_gap_reason_table\"\n", "NotActiveAssortmentReasonsLog = \"critical_skus_gap_reason_table_log\""]}, {"cell_type": "code", "execution_count": null, "id": "a329400e-fd9c-4a31-bdee-e1604f8242a4", "metadata": {}, "outputs": [], "source": ["EXECUTION_MODE = \"PROD\"\n", "spreadSheetId = \"1ouImIRAQRmN4fae0837nxx5k0WUtfbaUNzD2xi8fH0w\"\n", "sheetName = \"Critical_SKUs_DAG\"\n", "sheetName2 = \"Critical_SKUs_cross_city_expansion_city_exclude\"\n", "sheetName3 = \"Items_exclude\""]}, {"cell_type": "code", "execution_count": null, "id": "774f263a-776e-436c-bfeb-fcb68a32f8f5", "metadata": {}, "outputs": [], "source": ["slack_alert_channel = \"assortment-upload-loggers\""]}, {"cell_type": "code", "execution_count": null, "id": "ea6de1b0-9d3e-40e9-889e-1db5ddcf402e", "metadata": {}, "outputs": [], "source": ["def pushD<PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "    df,\n", "    tableName,\n", "    description=None,\n", "    primaryKeys=[\"insert_ds_ist\"],\n", "    load_type=\"upsert\",\n", "    environ=\"supply_etls\",\n", "    partition_key=[],\n", "):\n", "    from datetime import datetime, timezone, timedelta\n", "    import time\n", "    import pandas as pd\n", "\n", "    utc_now = datetime.now(timezone.utc)\n", "    ist_now = utc_now + <PERSON><PERSON><PERSON>(hours=5, minutes=30)\n", "    formatted_date = ist_now.strftime(\"%Y-%m-%d\")\n", "    df[\"insert_ds_ist\"] = formatted_date\n", "    df[\"updated_at\"] = pd.to_datetime(utc_now)\n", "    column_dtypes = [\n", "        {\n", "            \"name\": col,\n", "            \"type\": str(df[col].dtype)\n", "            .replace(\"int64\", \"integer\")\n", "            .replace(\"float64\", \"real\")\n", "            .replace(\"str\", \"varchar\")\n", "            .replace(\"datetime64[ns]\", \"timestamp(6)\")\n", "            .replace(\"object\", \"varchar\")\n", "            .replace(\"bool\", \"boolean\")\n", "            .replace(\"datetime64[ns, UTC]\", \"timestamp(6)\"),\n", "            \"description\": col,\n", "        }\n", "        for col in df.columns\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": environ,\n", "        \"table_name\": tableN<PERSON>,\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": primary<PERSON><PERSON><PERSON>,\n", "        \"load_type\": load_type,\n", "        \"table_description\": (description if description else tableName),\n", "    }\n", "\n", "    if len(partition_key) > 0:\n", "        kwargs[\"partition_key\"] = partition_key\n", "\n", "    import pencilbox as pb\n", "\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            pb.to_trino(data_obj=df, **kwargs)\n", "            return\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error pushDfToTrino (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to pushDfToTrino after {max_retries} attempts: {e}\")\n", "                return"]}, {"cell_type": "code", "execution_count": null, "id": "047cfc44-2edb-4a6b-9248-9e9e9b035beb", "metadata": {}, "outputs": [], "source": ["def readFromSheetsWithRetry(spreadSheetId, sheetName, raiseExceptionFlag=True, mode=EXECUTION_MODE):\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            if mode == \"LOCAL\":\n", "                script_dir = \"/home/<USER>/inventory-planning-playbooks/\"\n", "                sys.path.insert(0, script_dir)\n", "                from utils.commonFunc import readSpreadsheet\n", "\n", "                values = readSpreadsheet(spreadSheetId, sheetName)\n", "                df = pd.DataFrame(values[1:], columns=values[0])\n", "                return df\n", "            else:\n", "                df = pb.from_sheets(spreadSheetId, sheetName)\n", "                return df\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error fetching data (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to fetch data after {max_retries} attempts: {e}\")\n", "                if raiseExceptionFlag:\n", "                    raise Exception(\n", "                        \"Failed to read sheets after {} attempts\".format(max_retries)\n", "                    ) from e\n", "                else:\n", "                    sendSlackAlert(\n", "                        mode,\n", "                        \"povms_assortment_rationalisation_etl_longtail_store_replication dag failed to read sheet\"\n", "                        + spreadSheetId\n", "                        + sheetName,\n", "                    )\n", "                    return pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "id": "6550472a-f602-4f60-88c5-c1c284221066", "metadata": {}, "outputs": [], "source": ["req_columns = [\n", "    \"item_id\",\n", "    \"city_name\",\n", "    \"backend_facility_id\",\n", "    \"frontend_facility_id\",\n", "    \"master_assortment_substate\",\n", "    \"assortment_type\",\n", "    \"substate_reason_type\",\n", "    \"request_reason_type\",\n", "]\n", "output_column_types = {\n", "    \"item_id\": \"int\",\n", "    \"city_name\": \"str\",\n", "    \"backend_facility_id\": \"int\",\n", "    \"frontend_facility_id\": \"int\",\n", "    \"master_assortment_substate\": \"str\",\n", "    \"assortment_type\": \"str\",\n", "    \"substate_reason_type\": \"str\",\n", "    \"request_reason_type\": \"str\",\n", "}\n", "\n", "input_column_types = {\n", "    \"new_lt_store\": \"int\",\n", "    \"sister_lt_store\": \"int\",\n", "    \"new_lt_store_city_id\": \"int\",\n", "    \"is_replicated\": \"int\",\n", "    \"all_checks_passed\": \"int\",\n", "}\n", "\n", "\n", "def convert_columns(df, columnTypeJson):\n", "    # Iterate over each column in the dataframe\n", "    for col in df.columns:\n", "        if col in columnTypeJson and columnTypeJson[col] == \"str\":\n", "            df[col] = df[col].astype(str)\n", "        elif col in columnTypeJson and columnTypeJson[col] == \"int\":\n", "            df[col] = pd.to_numeric(df[col])\n", "            df[col] = df[col].apply(lambda x: int(x) if not np.isnan(x) else x)\n", "    return df\n", "\n", "\n", "def check_required_columns(df, req_columns):\n", "    # Get the current columns of the DataFrame\n", "    current_columns = set(df.columns)\n", "    # Convert required columns to a set\n", "    required = set(req_columns)\n", "    # Check if all required columns are present\n", "    return required.issubset(current_columns)"]}, {"cell_type": "code", "execution_count": null, "id": "2cd7da41-6e45-445b-a191-e7b32f5ba524", "metadata": {}, "outputs": [], "source": ["def sendSlackAlert(execution_mode, text):\n", "    if execution_mode == \"PROD\":\n", "        try:\n", "            pb.send_slack_message(channel=slack_alert_channel, text=text)\n", "        except Exception as e:\n", "            print(f\"Error sending Slack message: {e}, {text}\")\n", "        # handle the error further if needed\n", "    else:\n", "        print(\"local environment, just printing to console\")\n", "        print(text)"]}, {"cell_type": "code", "execution_count": null, "id": "519a420d-b388-48b0-bcff-85c4b2b4cd0e", "metadata": {}, "outputs": [], "source": ["def upload_to_bu(file_path, upload_type):\n", "    try:\n", "        url = \"https://retail-internal.grofer.io/bulk_upload/v1/upload_jobs\"\n", "        payload = {\n", "            \"file\": file_path,\n", "            \"created_by\": \"<PERSON><PERSON>\",\n", "            \"user_id\": 14,\n", "            \"is_auto_po\": True,\n", "            \"upload_type_id\": upload_type,\n", "            \"content_type\": \"text/csv\",\n", "            \"params\": {\"large_upload\": True},\n", "        }\n", "        response = requests.post(url, json=payload)\n", "        return response.status_code, response.json()\n", "    except Exception as e:\n", "        print(\"error in bulk upload api\", e)\n", "        return 404, {}"]}, {"cell_type": "code", "execution_count": null, "id": "db27ea36-0c12-44f8-a8f0-6cf5d1ee17e2", "metadata": {}, "outputs": [], "source": ["def processFile(input_df, chunk_size=35000):\n", "    if input_df.shape[0] == 0:\n", "        print(\"nothing to process, returning\")\n", "        return\n", "    elif input_df.shape[0] < chunk_size:\n", "        list_df = [input_df]\n", "    else:\n", "        list_df = [input_df[i : i + chunk_size] for i in range(0, input_df.shape[0], chunk_size)]\n", "\n", "    responses = []\n", "    df_logs = pd.DataFrame()\n", "    all_job_ids = []\n", "    for df in list_df:\n", "        uid = int(time.time())\n", "        local_file_path = f\"critical_assortment_expansion_assortment_request_upload_bot_{uid}.xlsx\"\n", "        df.to_excel(local_file_path, index=False, engine=\"openpyxl\")\n", "        file_path = f\"assortment/{local_file_path}\"\n", "        secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "        bucket_name = \"retail-bulk-upload\"\n", "        aws_key = secrets.get(\"aws_key\")\n", "        aws_secret = secrets.get(\"aws_secret\")\n", "        session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "        s3 = session.resource(\"s3\")\n", "        bucket_obj = s3.Bucket(bucket_name)\n", "        bucket_obj.upload_file(local_file_path, file_path)\n", "        status_code, responseJson = upload_to_bu(file_path, 100)\n", "        os.remove(local_file_path)\n", "        df[\"uid\"] = uid\n", "        # if status_code:\n", "        df[\"job_id\"] = responseJson.get(\"id\") if responseJson else 0  # Ensure it's a list\n", "        # all_job_ids = pd.DataFrame({'job_id': job_ids})\n", "        df[\"status_code\"] = status_code\n", "        df_logs = pd.concat([df_logs, df])\n", "        time.sleep(2)\n", "    df_logs[\"source\"] = \"povms_assortment_rationalisation_etl_critical_assortment_rationalisation\"\n", "    pushDfToTrino(\n", "        df_logs,\n", "        BulkUploadTable,\n", "        description=\"critical skus assortment auto expasnion data log table\",\n", "        load_type=\"append\",\n", "        environ=\"supply_etls\",\n", "        partition_key=[\"insert_ds_ist\"],\n", "    )\n", "    return df_logs"]}, {"cell_type": "markdown", "id": "ecbdcfa0-f9e8-4c5b-8e1d-d30c0cdf3027", "metadata": {"tags": []}, "source": ["## Critical SKUs city wise calculation"]}, {"cell_type": "markdown", "id": "428f573b-8f1d-49d4-97cf-ebacb54df175", "metadata": {}, "source": ["### city weights for items"]}, {"cell_type": "code", "execution_count": null, "id": "5872f472-b139-4b0d-b4a5-2eef9cefb9e4", "metadata": {}, "outputs": [], "source": ["city_weights = f\"\"\"\n", "with base as (\n", "  select\n", "    city,\n", "    item_id,\n", "    cart_penetration,\n", "    assortment_type,\n", "    max(try_cast(weights as double)) as weight\n", "  from\n", "    supply_etls.city_item_weights ciw\n", "    where updated_at = (\n", "      select\n", "        max(updated_at)\n", "      from\n", "        supply_etls.city_item_weights\n", "    )\n", "    and item_id not in (\n", "      select\n", "        item_id\n", "      from\n", "        supply_etls.active_sampling_sku\n", "      where\n", "        updated_at = (\n", "          select\n", "            max(updated_at)\n", "          from\n", "            supply_etls.active_sampling_sku\n", "        )  and is_bau = false\n", "    )\n", "  group by\n", "    1,\n", "    2,\n", "    3,\n", "    4\n", ")\n", "\n", "select b.*, id as city_id from base b inner join retail.console_location cl on cl.name = b.city\n", "and lake_active_record\"\"\"\n", "city_weights_raw = pd.read_sql_query(sql=city_weights, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "85e63fdb-7133-48fa-a210-67dd6e2cdf11", "metadata": {}, "outputs": [], "source": ["city_weights_raw[[\"weight\", \"cart_penetration\"]] = city_weights_raw[\n", "    [\"weight\", \"cart_penetration\"]\n", "].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "60d1e473-bb13-4f9b-93ef-5d3afce45953", "metadata": {}, "outputs": [], "source": ["city_weights_raw[city_weights_raw[\"item_id\"] == 10004491]"]}, {"cell_type": "markdown", "id": "214d9759-5710-43a5-b5c4-9a1b049dbc15", "metadata": {}, "source": ["### item calatog to consider"]}, {"cell_type": "code", "execution_count": null, "id": "e3d2ffcd-6764-4afd-94db-497b32e9653c", "metadata": {}, "outputs": [], "source": ["item_catalog = f\"\"\"\n", "  SELECT\n", "    *\n", "  FROM\n", "    (\n", "      SELECT\n", "        icd.item_id,\n", "        icd.name,\n", "        icd.l0_id,\n", "        icd.l0,\n", "        icd.l1_id,\n", "        icd.l1,\n", "        icd.l2_id,\n", "        icd.l2,\n", "        icd.product_type_id,\n", "        icd.product_type,\n", "        CASE\n", "          WHEN id.perishable = 1 THEN 'PERISHABLE'\n", "          ELSE 'PACKAGED'\n", "        END AS item_type,\n", "        id.storage_type AS storage_type_raw,\n", "        CASE\n", "          WHEN id.storage_type IN (\n", "            '1',\n", "            '8',\n", "            '11'\n", "          ) THEN 'REGULAR'\n", "          WHEN id.storage_type IN ('4', '5') THEN 'HEAVY'\n", "          WHEN id.storage_type IN ('2', '6') THEN 'COLD'\n", "          WHEN id.storage_type IN ('3', '7') THEN 'FROZEN'\n", "          ELSE 'REGULAR'\n", "        END AS storage_type,\n", "        CASE\n", "          WHEN id.handling_type IN ('8') THEN 'PACKAGING MATERIAL'\n", "          WHEN id.handling_type IN ('6') THEN 'MEDICINAL'\n", "          ELSE 'REGULAR'\n", "        END AS handling_type,\n", "        id.variant_mrp AS mrp,\n", "        id.variant_description,\n", "        id.weight_in_gm,\n", "        id.length_in_cm,\n", "        id.height_in_cm,\n", "        id.breadth_in_cm,\n", "        id.shelf_life,\n", "        coalesce(itf.item_factor, 0.01) AS item_factor,\n", "        DATE_DIFF('day', date(icd.created_at), CURRENT_DATE) AS item_catalog_age,\n", "        rank() OVER (\n", "          PARTITION BY id.item_id\n", "          ORDER BY\n", "            id.id DESC\n", "        ) AS variant_rank,\n", "        itm.tag_value AS custom_storage_type_raw,\n", "        case\n", "          when itm.tag_value = '1' then 'BEAUTY'\n", "          when itm.tag_value = '2' then 'BOUQUET'\n", "          when itm.tag_value = '3' then 'PREMIUM'\n", "          when itm.tag_value = '4' then 'BOOKS'\n", "          when itm.tag_value = '5' then 'NON_VEG'\n", "          when itm.tag_value = '6' then 'ICE_CREAM'\n", "          when itm.tag_value = '7' then 'TOYS'\n", "          when itm.tag_value = '8' then 'ELECTRONICS' -- when itm.tag_value = '9' then 'FESTIVE'\n", "          when itm.tag_value = '10' then 'VERTICAL_CHUTES'\n", "          when itm.tag_value = '11' then 'BEST_SERVED_COLD'\n", "          when itm.tag_value = '12' then 'CRITICAL_SKUS'\n", "          when itm.tag_value = '13' then 'LARGE'\n", "          when itm.tag_value = '14' then 'APPAREL'\n", "          when itm.tag_value = '15' then 'SPORTS'\n", "          when itm.tag_value = '16' then 'PET_CARE'\n", "          when itm.tag_value = '17' then 'HOME_DECOR'\n", "          when itm.tag_value = '18' then 'KITCHEN_DINING'\n", "          when itm.tag_value = '19' then 'HOME_FURNISHING'\n", "          when itm.tag_value = '20' then 'LONGTAIL_OTHERS'\n", "          when itm.tag_value = '21' then 'PHARMA'\n", "          when itm.tag_value = '22' then 'PAAS'\n", "          when itm.tag_value = '23' then 'PALLET'\n", "          when itm.tag_value = '24' then 'LARGE_FRAGILE'\n", "          WHEN itm.tag_value IS NULL THEN 'NO_CONFIG'\n", "          ELSE 'UNKNOWN_CONFIG'\n", "        END AS custom_storage_type,\n", "        pb.manufacturer_id,\n", "        id.manufacturer AS manufacturer_name,\n", "        pb.name AS brand_name,\n", "        coalesce(id.outer_case_size, 1) AS outer_case_size,\n", "        coalesce(id.inner_case_size, 1) AS inner_case_size,\n", "        CASE\n", "          WHEN itm2.tag_value = '1' THEN TRUE\n", "          ELSE FALSE\n", "        END AS is_high_value,\n", "        itm2.tag_value AS high_value_tag_raw,\n", "        CASE\n", "          WHEN itm3.tag_value = '1' THEN 'upc scan'\n", "          WHEN itm3.tag_value = '2' THEN 'serial scan'\n", "          WHEN itm3.tag_value = '3' THEN 'qr scan'\n", "          WHEN itm3.tag_value = '4' THEN 'no scan'\n", "          ELSE 'unknown'\n", "        END AS scan_type,\n", "        itm3.tag_value AS scan_type_raw,\n", "        cms_food_type,\n", "        case when spm.item_id is not null then 'seller_item' else 'blinkit_item' end as \"item_flag\"\n", "      FROM\n", "        rpc.item_category_details icd\n", "        INNER JOIN rpc.product_product id ON id.item_id = icd.item_id\n", "        AND id.active = 1\n", "        AND id.approved = 1\n", "        AND id.lake_active_record\n", "        LEFT JOIN supply_etls.item_factor itf ON itf.item_id = icd.item_id\n", "        LEFT JOIN rpc.item_tag_mapping itm ON itm.tag_type_id = 7\n", "        AND itm.active = TRUE\n", "        AND itm.lake_active_record\n", "        AND itm.item_id = icd.item_id\n", "        LEFT JOIN rpc.product_brand pb ON id.brand_id = pb.id\n", "        AND pb.lake_active_record\n", "        AND pb.active = 1\n", "        LEFT JOIN rpc.item_tag_mapping itm2 ON itm2.item_id = icd.item_id\n", "        AND itm2.active\n", "        AND itm2.tag_type_id = 3\n", "        AND itm2.lake_active_record\n", "        LEFT JOIN rpc.item_tag_mapping itm3 ON itm3.item_id = icd.item_id\n", "        AND itm3.active\n", "        AND itm3.tag_type_id = 5\n", "        AND itm3.lake_active_record\n", "        left join seller.seller_product_mappings spm on spm.item_id = id.item_id\n", "      WHERE\n", "        icd.lake_active_record\n", "        AND id.handling_type != '8'\n", "        AND icd.l0 NOT IN (\n", "          'wholesale store',\n", "          'Trial new tree',\n", "          'Specials',\n", "          'Bistro',\n", "          'HP', 'Digital Goods'\n", "        ) -- removing test and flyer/freebie l0s and bistro\n", "    ) AS x\n", "  WHERE\n", "    variant_rank = 1\n", "\"\"\"\n", "item_catalog_raw = pd.read_sql_query(sql=item_catalog, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "c4e4d22e-76c0-4c76-ae0b-3e12e19aaf64", "metadata": {}, "outputs": [], "source": ["item_catalog_raw[\"storage_type\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "a4bc3000-19d9-4d75-b7b0-c95989e40ed6", "metadata": {}, "outputs": [], "source": ["item_catalog_filtered = item_catalog_raw[\n", "    item_catalog_raw[\"handling_type\"].isin(filters[\"handling_type\"])\n", "    & item_catalog_raw[\"item_flag\"].isin(filters[\"item_flag\"])\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "b2caf9f1-9fbb-4b80-b2b9-484cbfb148a7", "metadata": {}, "outputs": [], "source": ["city_weights_raw = city_weights_raw.merge(\n", "    item_catalog_filtered[\n", "        [\n", "            \"item_id\",\n", "            \"name\",\n", "            \"l0\",\n", "            \"l1\",\n", "            \"l2\",\n", "            \"product_type\",\n", "            \"item_type\",\n", "            \"storage_type\",\n", "            \"handling_type\",\n", "            \"mrp\",\n", "            \"is_high_value\",\n", "            \"scan_type\",\n", "            \"cms_food_type\",\n", "            \"item_flag\",\n", "        ]\n", "    ],\n", "    how=\"left\",\n", "    on=[\"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2b85cf8b-e2ff-4e6a-a71f-4b8e9af679d1", "metadata": {}, "outputs": [], "source": ["city_weights_raw = city_weights_raw[city_weights_raw[\"name\"].notna()]"]}, {"cell_type": "code", "execution_count": null, "id": "348588ff-8c8a-4610-b918-324fabbd0b03", "metadata": {}, "outputs": [], "source": ["city_weights_raw[\"assortment_type\"] = np.where(\n", "    (city_weights_raw[\"assortment_type\"] == \"Packaged Goods\")\n", "    & (city_weights_raw[\"storage_type\"] == \"FROZEN\"),\n", "    \"Frozen\",\n", "    np.where(\n", "        (city_weights_raw[\"assortment_type\"] == \"Perishable\")\n", "        | (city_weights_raw[\"item_type\"] == \"PERISHABLE\"),\n", "        \"Perishable\",\n", "        np.where(city_weights_raw[\"assortment_type\"] == \"FnV\", \"FnV\", \"Packaged Goods\"),\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1367e724-fec8-43eb-86ae-033e04470c12", "metadata": {}, "outputs": [], "source": ["city_weights_raw[\"assortment_type\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "fdb2d9f4-32f5-48cc-9dc9-7ca79158be62", "metadata": {}, "outputs": [], "source": ["# Step 1: Sort\n", "city_weights_raw = city_weights_raw.sort_values(\n", "    by=[\"city\", \"city_id\", \"assortment_type\", \"weight\"], ascending=[True, True, True, False]\n", ")\n", "\n", "# Step 2: Total weight per city-assortment\n", "city_weights_raw[\"total_weight\"] = city_weights_raw.groupby([\"city\", \"city_id\", \"assortment_type\"])[\n", "    \"weight\"\n", "].transform(\"sum\")\n", "\n", "# Step 3: Cumulative weight per city-assortment\n", "city_weights_raw[\"cum_weight\"] = city_weights_raw.groupby([\"city\", \"city_id\", \"assortment_type\"])[\n", "    \"weight\"\n", "].cumsum()\n", "\n", "# Step 4: Cumulative weight % and critical flag\n", "city_weights_raw[\"cum_weight_pct\"] = (\n", "    city_weights_raw[\"cum_weight\"] / city_weights_raw[\"total_weight\"]\n", ")\n", "city_weights_raw[\"critical_skus\"] = (\n", "    city_weights_raw[\"cum_weight_pct\"] <= filters[\"critical_skus_perc_cut\"][0]\n", ").astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "046d5c74-3a61-4d67-9c12-28099d36366f", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit = city_weights_raw[city_weights_raw[\"critical_skus\"] == 1]"]}, {"cell_type": "code", "execution_count": null, "id": "8017da1a-4942-4cfd-b7e8-e40677ea77f2", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit[\"assortment_type\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "df5b5105-6c2d-4283-860e-8f791e8d88d5", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit.groupby([\"city\", \"assortment_type\"])[\"item_id\"].count().reset_index(\n", "    name=\"item_count\"\n", ").sort_values(by=\"item_count\", ascending=False).query(\"assortment_type == 'FnV'\")"]}, {"cell_type": "code", "execution_count": null, "id": "bb51bfcf-563f-48bc-9587-18cc732d3cb7", "metadata": {}, "outputs": [], "source": ["del city_weights_raw"]}, {"cell_type": "code", "execution_count": null, "id": "f0b18382-2090-4b1d-b181-8c62fbdaeb61", "metadata": {}, "outputs": [], "source": ["gc.collect()"]}, {"cell_type": "markdown", "id": "d08c2376-1c68-4aa4-8693-e886b289f293", "metadata": {}, "source": ["## emerging cities and sister city mapping of critical skus"]}, {"cell_type": "markdown", "id": "a1fd9c6d-0f94-4f8e-a758-425339e9a156", "metadata": {}, "source": ["### emerging cities calculation"]}, {"cell_type": "code", "execution_count": null, "id": "cb9d821e-02c3-42d4-8d78-c9a11d1ab2f0", "metadata": {}, "outputs": [], "source": ["def emerging_cities():\n", "    sql = f\"\"\"\n", "    WITH fo AS\n", "  (SELECT facility_id,\n", "          outlet_id,\n", "          city_id,\n", "          outlet_name\n", "   FROM po.physical_facility_outlet_mapping\n", "   WHERE active=1\n", "     AND ars_active=1\n", "     AND facility_id IN\n", "       (SELECT facility_id\n", "        FROM lake_retail.console_outlet\n", "        WHERE business_type_id = 7)\n", "        and outlet_id in (select distinct outlet_id from po.bulk_facility_outlet_mapping bfom where active)\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4)\n", "select distinct outlet_id,facility_id, outlet_name, fo.city_id, cl.name as city_name from fo  \n", "inner join rpc.ams_city_cluster_mapping ccm on ccm.city_id = fo.city_id\n", "inner join rpc.ams_cluster ac on ac.cluster_id = ccm.cluster_id\n", "inner join retail.console_location cl on cl.id = fo.city_id\n", "where ccm.active and ccm.lake_active_record\n", "and ac.lake_active_record and ac.cluster_name in ('PAN_India_Emerging')\n", "\n", "\n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "cad8b95e-0bc0-4d5f-bffd-40edde50d6e6", "metadata": {}, "outputs": [], "source": ["emerging_cities_raw = emerging_cities()"]}, {"cell_type": "code", "execution_count": null, "id": "b50811e0-2e87-48e4-8694-29202e852eb3", "metadata": {}, "outputs": [], "source": ["emerging_cities_raw[\"city_id\"].nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "c72fe176-b666-4923-820a-16849b77a6b0", "metadata": {}, "outputs": [], "source": ["emerging_cities_raw = emerging_cities_raw[[\"facility_id\", \"city_id\", \"city_name\"]]\n", "emerging_cities_raw.shape"]}, {"cell_type": "code", "execution_count": null, "id": "7a151bf0-1e97-4f46-a381-af8e658e4c0f", "metadata": {}, "outputs": [], "source": ["def state_cluster():\n", "    sql = f\"\"\"\n", "            SELECT \n", "                om.city_id,cl.name as city_name, ccm.cluster_id, cluster_name\n", "                from  po.physical_facility_outlet_mapping om\n", "                inner join rpc.ams_city_cluster_mapping ccm on ccm.city_id = om.city_id\n", "inner join rpc.ams_cluster ac on ac.cluster_id = ccm.cluster_id\n", "inner join retail.console_location cl on cl.id = om.city_id\n", "   WHERE om.active=1\n", "     AND ars_active=1\n", "     and ccm.active and ccm.lake_active_record\n", "and ac.lake_active_record and ccm.cluster_id >15000\n", "group by 1,2,3,4\n", "       \n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "82bdd0bf-6be9-4e78-b5c9-b539ae0c3ca0", "metadata": {}, "outputs": [], "source": ["state_cluster_raw = state_cluster()"]}, {"cell_type": "code", "execution_count": null, "id": "0b1f624a-a824-4cfa-b13c-e3391313e56f", "metadata": {}, "outputs": [], "source": ["state_cluster_raw"]}, {"cell_type": "code", "execution_count": null, "id": "74fa6a50-7af6-4336-ab66-bb071f5cd0da", "metadata": {}, "outputs": [], "source": ["emerging_cities_raw = emerging_cities_raw.merge(\n", "    state_cluster_raw, on=[\"city_id\", \"city_name\"], how=\"left\"\n", ")\n", "emerging_cities_raw[emerging_cities_raw[\"cluster_name\"].isna()]"]}, {"cell_type": "code", "execution_count": null, "id": "e12c8c3b-3391-4cd6-aba2-056fa2b57d7b", "metadata": {}, "outputs": [], "source": ["emerging_cities_raw = emerging_cities_raw[emerging_cities_raw[\"cluster_name\"].notna()]\n", "emerging_cities_raw.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0350e05c-12f6-441f-8968-7de6d2c35970", "metadata": {}, "outputs": [], "source": ["input_facility_ids = emerging_cities_raw[\"facility_id\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "8b6fabfd-f020-4553-bf8e-313a7391ae0f", "metadata": {}, "outputs": [], "source": ["def fetch_new_city_lat_long(input_facility_ids):\n", "    sql = f\"\"\"\n", "        SELECT \n", "            do.outlet_id,\n", "            do.facility_id,\n", "            city_id,\n", "            geo_location_lat AS latitude,\n", "            geo_location_lon AS longitude\n", "        FROM dwh.dim_outlet do \n", "        INNER JOIN po.physical_facility_outlet_mapping om \n", "            ON om.facility_id = do.facility_id\n", "        WHERE active = 1\n", "          AND ars_active = 1\n", "          AND is_current\n", "          and is_outlet_active = 1\n", "          AND do.facility_id in {tuple(input_facility_ids)}\n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=con)\n", "\n", "\n", "# Correct function call\n", "fetch_new_city_lat_long_raw = fetch_new_city_lat_long(input_facility_ids)"]}, {"cell_type": "code", "execution_count": null, "id": "ef62196d-c7b1-45fd-89b4-e284601e1037", "metadata": {}, "outputs": [], "source": ["fetch_new_city_lat_long_raw2 = fetch_new_city_lat_long_raw.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "d3678c0e-9f4f-4a62-b89e-da7e7f05cba0", "metadata": {}, "outputs": [], "source": ["fetch_new_city_lat_long_raw2[fetch_new_city_lat_long_raw2[\"facility_id\"] == 2729]"]}, {"cell_type": "code", "execution_count": null, "id": "508fc6f7-bc19-4b36-b04d-830f4926d687", "metadata": {}, "outputs": [], "source": ["fetch_new_city_lat_long_raw = fetch_new_city_lat_long_raw[\n", "    fetch_new_city_lat_long_raw[\"latitude\"].notna()\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "3fb240b6-dfcd-4e54-8a33-bb79cdf9f244", "metadata": {}, "outputs": [], "source": ["emerging_cities_raw = emerging_cities_raw.merge(\n", "    fetch_new_city_lat_long_raw[[\"facility_id\", \"latitude\", \"longitude\"]],\n", "    how=\"left\",\n", "    on=\"facility_id\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "316d65c7-e46a-4e74-8a46-3941e27b7df5", "metadata": {}, "outputs": [], "source": ["# emerging_cities_raw[emerging_cities_raw['latitude'].notna()]"]}, {"cell_type": "code", "execution_count": null, "id": "95c41533-0454-4749-b2c6-9c27ded62e0d", "metadata": {}, "outputs": [], "source": ["# emerging_cities_raw"]}, {"cell_type": "code", "execution_count": null, "id": "35b11972-f3d5-4bab-8e20-80731b6861ec", "metadata": {}, "outputs": [], "source": ["emerging_cities_raw = emerging_cities_raw[emerging_cities_raw[\"latitude\"].notna()]"]}, {"cell_type": "code", "execution_count": null, "id": "f094a647-bc15-4da1-a6fa-c6c90fa11c52", "metadata": {}, "outputs": [], "source": ["emerging_cities_raw"]}, {"cell_type": "code", "execution_count": null, "id": "11c37196-b2d8-4baf-92f9-4d0d59d84ae5", "metadata": {}, "outputs": [], "source": ["def fetch_stores():\n", "    sql = f\"\"\"\n", "            SELECT \n", "                om.outlet_id,\n", "                om.facility_id,om.city_id,\n", "                geo_location_lat as latitude,\n", "                geo_location_lon as longitude,\n", "                is_current\n", "             FROM po.physical_facility_outlet_mapping om\n", "             left join  dwh.dim_outlet do \n", "                  on om.facility_id = do.facility_id \n", "                inner join rpc.ams_city_cluster_mapping ccm on ccm.city_id = om.city_id\n", "inner join rpc.ams_cluster ac on ac.cluster_id = ccm.cluster_id\n", "inner join retail.console_location cl on cl.id = om.city_id\n", "where ccm.active and ccm.lake_active_record\n", "and ac.lake_active_record and ac.cluster_name in ('PAN_India_Core')\n", "   and om.active=1\n", "     AND ars_active=1\n", "     AND om.facility_id IN\n", "       (SELECT facility_id\n", "        FROM lake_retail.console_outlet\n", "        WHERE business_type_id = 7)\n", "        and om.outlet_id in (select distinct outlet_id from po.bulk_facility_outlet_mapping bfom where active)\n", "     and is_current AND is_outlet_active = 1\n", "\n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "fd09b725-1deb-449a-8b4f-04106598b248", "metadata": {}, "outputs": [], "source": ["existing_stores = fetch_stores()"]}, {"cell_type": "code", "execution_count": null, "id": "db69b846-22a8-448e-8ca6-0200ece78a62", "metadata": {}, "outputs": [], "source": ["existing_stores = existing_stores[existing_stores[\"latitude\"].notna()]"]}, {"cell_type": "code", "execution_count": null, "id": "d5b85687-dbc4-4fa9-af82-63d03d0bf0ea", "metadata": {}, "outputs": [], "source": ["existing_stores2 = existing_stores.merge(state_cluster_raw, on=\"city_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "931760b2-d049-425d-a626-0ca79c16559b", "metadata": {}, "outputs": [], "source": ["existing_stores2[\"latitude\"] = pd.to_numeric(existing_stores2[\"latitude\"], errors=\"coerce\")\n", "existing_stores2[\"longitude\"] = pd.to_numeric(existing_stores2[\"longitude\"], errors=\"coerce\")"]}, {"cell_type": "code", "execution_count": null, "id": "53109184-25fb-4d5c-965d-c000326ccb1a", "metadata": {}, "outputs": [], "source": ["# Group existing stores to city-level averages\n", "existing_cities = (\n", "    existing_stores2.dropna(subset=[\"latitude\", \"longitude\"])  # Ensure no missing values\n", "    .groupby([\"city_id\", \"cluster_id\", \"cluster_name\"], as_index=False)[[\"latitude\", \"longitude\"]]\n", "    .mean()\n", ")\n", "\n", "\n", "def find_sister_city(row):\n", "    current_city_id = row[\"city_id\"]\n", "    current_cluster_id = row[\"cluster_id\"]\n", "    current_coords = (row[\"latitude\"], row[\"longitude\"])\n", "\n", "    other_cities = existing_cities[existing_cities[\"city_id\"] != current_city_id].copy()\n", "\n", "    same_cluster = other_cities[other_cities[\"cluster_id\"] == current_cluster_id].copy()\n", "    if not same_cluster.empty:\n", "        same_cluster[\"distance\"] = same_cluster.apply(\n", "            lambda x: geodesic(current_coords, (x[\"latitude\"], x[\"longitude\"])).kilometers, axis=1\n", "        )\n", "        return same_cluster.loc[same_cluster[\"distance\"].idxmin(), \"city_id\"]\n", "\n", "    other_cities[\"distance\"] = other_cities.apply(\n", "        lambda x: geodesic(current_coords, (x[\"latitude\"], x[\"longitude\"])).kilometers, axis=1\n", "    )\n", "    return other_cities.loc[other_cities[\"distance\"].idxmin(), \"city_id\"]\n", "\n", "\n", "# Apply only if data is not empty\n", "if not emerging_cities_raw.empty and not existing_cities.empty:\n", "    emerging_cities_raw[\"sister_city_id\"] = emerging_cities_raw.apply(find_sister_city, axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "3309fa2b-f033-4edf-9f50-754e9f8bfba9", "metadata": {}, "outputs": [], "source": ["emerging_cities_raw[emerging_cities_raw[\"city_name\"] == \"Begusarai\"]"]}, {"cell_type": "code", "execution_count": null, "id": "c0e1f7bc-8142-47a3-ad55-9349f6d37c4c", "metadata": {}, "outputs": [], "source": ["emerging_cities_raw_final = emerging_cities_raw[\n", "    [\"city_id\", \"city_name\", \"cluster_id\", \"cluster_name\", \"sister_city_id\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "bb295c74-143a-4e07-a7cc-c15c69447903", "metadata": {}, "outputs": [], "source": ["del emerging_cities_raw\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "97c43914-85a2-4407-b6f0-8c078528cfd8", "metadata": {}, "outputs": [], "source": ["emerging_cities_raw_final = emerging_cities_raw_final.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "366a5238-1ee8-4955-9410-65b6ef4aa3c7", "metadata": {}, "outputs": [], "source": ["emerging_cities_raw_final = emerging_cities_raw_final.merge(\n", "    state_cluster_raw.rename(\n", "        columns={\"cluster_id\": \"sister_cluster_id\", \"cluster_name\": \"sister_cluster_name\"}\n", "    ),\n", "    left_on=\"sister_city_id\",\n", "    right_on=\"city_id\",\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "dbdb9484-9a9a-4dbe-9eb4-e6bd0fe4a24d", "metadata": {}, "outputs": [], "source": ["emerging_cities_raw_final.rename(\n", "    columns={\"city_id_x\": \"city_id\", \"city_name_x\": \"city_name\", \"city_name_y\": \"sister_city_name\"},\n", "    inplace=True,\n", ")\n", "emerging_cities_raw_final.drop(columns=\"city_id_y\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "fdba8583-4b67-40df-96ef-c78af9bed997", "metadata": {}, "outputs": [], "source": ["## final emerging city and sister city mapping"]}, {"cell_type": "code", "execution_count": null, "id": "d09ca35b-164e-4998-90bf-f3da290e779d", "metadata": {}, "outputs": [], "source": ["emerging_cities_raw_final  ## sister city and emerging city mapping"]}, {"cell_type": "code", "execution_count": null, "id": "43bac17f-d6b4-4a88-8a9f-d3ee527fe26b", "metadata": {}, "outputs": [], "source": ["pushDfToTrino(\n", "    emerging_cities_raw_final,\n", "    SisterCityTable,\n", "    description=\"core_sister_city_of_emerging_cities_current_snapshot\",\n", "    primaryKeys=[\"city_name\", \"city_id\"],\n", "    load_type=\"truncate\",\n", "    environ=\"supply_etls\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9fd8ddff-42af-46b4-84ff-20aa1e95f288", "metadata": {}, "outputs": [], "source": ["emerging_cities_sister_city = emerging_cities_raw_final[[\"city_id\", \"sister_city_id\", \"city_name\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "ff1e454d-6127-4088-837e-8886e806dcb8", "metadata": {}, "outputs": [], "source": ["del emerging_cities_raw_final"]}, {"cell_type": "markdown", "id": "cad54e31-dcc1-494d-a2ec-9300e52e4ed0", "metadata": {}, "source": ["### adding sister city weights"]}, {"cell_type": "code", "execution_count": null, "id": "4a5fa4e8-cd2d-499d-b9d6-703bdc72e473", "metadata": {}, "outputs": [], "source": ["emerging_cities_sister_city = emerging_cities_sister_city.merge(\n", "    city_weights_raw_crit, how=\"left\", left_on=[\"sister_city_id\"], right_on=\"city_id\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f13c806f-0dbc-445d-8076-3a4c1111c6d7", "metadata": {}, "outputs": [], "source": ["emerging_cities_sister_city[\"assortment_type\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "09cd416b-3035-442c-9c4b-5457c992a500", "metadata": {}, "outputs": [], "source": ["emerging_cities_sister_city.drop(columns=[\"city\", \"city_id_y\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "769df7a2-178b-4bb9-b549-7c0f848f4825", "metadata": {}, "outputs": [], "source": ["emerging_cities_sister_city.rename(\n", "    columns={\"city_id_x\": \"city_id\", \"city_name\": \"city\"}, inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a4155462-63d2-408d-acc2-fe9ed2fe7611", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit.columns"]}, {"cell_type": "code", "execution_count": null, "id": "b5277ec8-283b-4059-b3e2-77ad700020e0", "metadata": {"tags": []}, "outputs": [], "source": ["emerging_cities_sister_city = emerging_cities_sister_city[\n", "    [\n", "        \"city\",\n", "        \"item_id\",\n", "        \"cart_penetration\",\n", "        \"weight\",\n", "        \"city_id\",\n", "        \"name\",\n", "        \"assortment_type\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"product_type\",\n", "        \"item_type\",\n", "        \"storage_type\",\n", "        \"handling_type\",\n", "        \"mrp\",\n", "        \"is_high_value\",\n", "        \"scan_type\",\n", "        \"cms_food_type\",\n", "        \"item_flag\",\n", "        \"total_weight\",\n", "        \"cum_weight\",\n", "        \"cum_weight_pct\",\n", "        \"critical_skus\",\n", "        \"sister_city_id\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "4e9a8b77-ab84-4b83-a5f4-9700e9613584", "metadata": {}, "outputs": [], "source": ["emerging_cities_sister_city.shape"]}, {"cell_type": "markdown", "id": "80b6f245-8489-4846-b58f-b40a04ab5034", "metadata": {}, "source": ["## Same city plus sister city weighted skus list"]}, {"cell_type": "code", "execution_count": null, "id": "8fa0bb2c-ded3-4660-94c3-60efefb10fd7", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit.shape"]}, {"cell_type": "code", "execution_count": null, "id": "ba9b904e-316f-45f4-84b6-5a5c1224e9a5", "metadata": {}, "outputs": [], "source": ["emerging_cities_sister_city[\"source\"] = \"sister city\""]}, {"cell_type": "code", "execution_count": null, "id": "b2e8ce9a-d369-4d7c-a7a6-8bebdae20388", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit.loc[:, \"source\"] = \"same city\""]}, {"cell_type": "code", "execution_count": null, "id": "17d589c1-279d-4b4f-8799-b6f159ce6c8b", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit.loc[:, \"sister_city_id\"] = 0"]}, {"cell_type": "code", "execution_count": null, "id": "39952a2b-2b14-4e99-aa91-e2584b641edc", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit = city_weights_raw_crit.append(emerging_cities_sister_city)"]}, {"cell_type": "code", "execution_count": null, "id": "fb25fd99-bfab-4f06-88d1-ee5a7dec4929", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit = city_weights_raw_crit.drop_duplicates(\n", "    subset=[\"item_id\", \"city_id\"], keep=\"first\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "df874970-abaf-40f4-b4b6-02f118aff295", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit[city_weights_raw_crit[\"city\"] == \"Belgaum\"].groupby(\n", "    [\"city\", \"source\", \"assortment_type\"]\n", ").agg({\"item_id\": \"count\"}).reset_index().sort_values(by=\"item_id\", ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "9ed3bce6-2ecf-40ca-919d-07cff094f013", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit.shape  ## final table for all types of weights - sister and same"]}, {"cell_type": "code", "execution_count": null, "id": "593fff7b-3a52-42d7-bfca-0c5844fcf058", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit[\"is_high_value\"] = city_weights_raw_crit[\"is_high_value\"].astype(bool)"]}, {"cell_type": "code", "execution_count": null, "id": "87f103a7-c0e4-403c-a317-0e90a1c99810", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit.rename(columns={\"assortment_type\": \"item_category\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "c139e0fb-9511-41aa-bf3b-4ab6a3f1a2fe", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "c03d1f6c-962c-4051-bd4e-364ad16a2440", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit[(city_weights_raw_crit[\"item_id\"] == 10004491)].shape"]}, {"cell_type": "code", "execution_count": null, "id": "c73ba2af-5725-4c24-9ddd-5293ce61c34e", "metadata": {}, "outputs": [], "source": ["pushDfToTrino(\n", "    city_weights_raw_crit,\n", "    CriticalAssortmentTable,\n", "    description=\"critical_skus_city_assortment_current_snapshot\",\n", "    primaryKeys=[\"city\", \"city_id\"],\n", "    load_type=\"truncate\",\n", "    environ=\"supply_etls\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "48c9d085-e8c1-4077-911c-f535c1b1018e", "metadata": {}, "outputs": [], "source": ["pushDfToTrino(\n", "    city_weights_raw_crit,\n", "    CriticalAssortmentLogTable,\n", "    description=\"critical_skus_city_assortment_current_snapshot\",\n", "    primaryKeys=[\"city\", \"city_id\"],\n", "    load_type=\"append\",\n", "    environ=\"supply_etls\",\n", ")"]}, {"cell_type": "markdown", "id": "cfce553f-6539-4caa-a58e-0b5ff50e3039", "metadata": {}, "source": ["## Assortment check facility wise"]}, {"cell_type": "markdown", "id": "5d78aeb1-8662-45a9-9ac9-20eac7c3993a", "metadata": {}, "source": ["### All frontend active outlets"]}, {"cell_type": "code", "execution_count": null, "id": "5f84e4af-3846-4909-897c-7c9891b8b4ee", "metadata": {}, "outputs": [], "source": ["frontend_outlets = f\"\"\"\n", " \n", "  SELECT\n", "    om.facility_id,\n", "    om.outlet_id AS outlet_id,\n", "    mo_map.frontend_merchant_id AS merchant_id,\n", "    om.outlet_name AS outlet_name,\n", "    rcl.id as city_id,\n", "    rcl.name AS city_name,\n", "    case when type_id = 3 then 'LONGTAIL_ONLY' else 'EXPRESS' end as store_type,\n", "    case when long_tail_facility_id is not null then 'LONGTAIL' else 'EXPRESS_ONLY' end as store_type_ams\n", "  FROM\n", "    po.physical_facility_outlet_mapping om\n", "    INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "    AND rco.business_type_id IN (7)\n", "    AND rco.company_type_id NOT IN (771, 767)\n", "    AND rco.lake_active_record\n", "    INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "    AND rcl.lake_active_record\n", "    INNER JOIN dwh.dim_merchant_outlet_facility_mapping mo_map ON mo_map.facility_id = om.facility_id\n", "    AND rco.business_type_id = 7\n", "    AND is_current\n", "    AND is_current_mapping_active\n", "    AND is_backend_merchant_active\n", "    AND is_frontend_merchant_active\n", "    INNER JOIN (\n", "      SELECT\n", "        distinct cast(merchant_id AS int) merchant_id\n", "      FROM\n", "        serviceability.ser_store_polygons\n", "      WHERE\n", "        TYPE in ('STORE_POLYGON', 'LONGTAIL_POLYGON')\n", "        AND is_active = TRUE\n", "        AND lake_active_record\n", "    ) poly ON poly.merchant_id = mo_map.frontend_merchant_id\n", "    left join (select distinct long_tail_facility_id from po.long_tail_facility_store_mapping where active = 1) long_\n", "    on long_.long_tail_facility_id = om.facility_id\n", "  WHERE\n", "    om.active = 1\n", "    AND om.ars_active = 1\n", "    AND om.lake_active_record\n", "    AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "    AND om.outlet_name NOT LIKE '%%Draft%%'\n", "    AND om.outlet_name NOT LIKE '%%Test Store%%'\n", "    AND om.outlet_name NOT LIKE '%%Dummy%%'\n", "    AND om.facility_id not in (273,2943)\n", "    AND om.outlet_id IN (\n", "      SELECT\n", "        DISTINCT outlet_id\n", "      FROM\n", "        po.bulk_facility_outlet_mapping\n", "      WHERE\n", "        active\n", "        AND lake_active_record\n", "    )\"\"\"\n", "frontend_outlets_raw = pd.read_sql_query(sql=frontend_outlets, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "cd648f33-a2cc-4044-9a91-af5f9b163e12", "metadata": {}, "outputs": [], "source": ["frontend_outlets_raw[\"facility_id\"].nunique()"]}, {"cell_type": "markdown", "id": "e43be0ed-75be-425f-a224-a235793d965e", "metadata": {}, "source": ["### City all active outlets assortment mapping check of critical skus"]}, {"cell_type": "code", "execution_count": null, "id": "6bfc9477-126b-4958-9661-d275be1b0713", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit = city_weights_raw_crit.merge(frontend_outlets_raw, how=\"left\", on=\"city_id\")"]}, {"cell_type": "markdown", "id": "bdcf4042-40d3-4a39-b91d-e128e37cfd1a", "metadata": {}, "source": ["### City hybrid SKUs"]}, {"cell_type": "code", "execution_count": null, "id": "7084bfba-282b-42b7-b416-c0fa09f8154c", "metadata": {}, "outputs": [], "source": ["city_hybrid = f\"\"\"\n", "select pfma.item_id, pfma.city_id, launch_type\n", "FROM\n", "    rpc.product_facility_master_assortment pfma\n", "where pfma.active = 1\n", "and pfma.lake_active_record\n", "and launch_type = 'HYBRID' and master_assortment_substate_id in (1,3)\n", "group by 1,2,3\"\"\"\n", "city_hybrid_raw = pd.read_sql_query(sql=city_hybrid, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "1492d075-92d5-4bda-b961-4882dc490293", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit = city_weights_raw_crit.merge(\n", "    city_hybrid_raw, how=\"left\", on=[\"item_id\", \"city_id\"]\n", ")"]}, {"cell_type": "markdown", "id": "3c99bc75-4930-4c77-8d6c-d97fae73d74a", "metadata": {}, "source": ["### Sister city Hybrid SKUs mapping"]}, {"cell_type": "code", "execution_count": null, "id": "6c311e2c-ce7c-4727-bea7-b3aca2861927", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit = city_weights_raw_crit.merge(\n", "    city_hybrid_raw.rename(\n", "        columns={\"launch_type\": \"sister_city_launch_type\", \"city_id\": \"sister_city_id\"}\n", "    ),\n", "    how=\"left\",\n", "    on=[\"item_id\", \"sister_city_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6cd39ff9-445d-44fa-a602-dc1aa3091e95", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit.columns"]}, {"cell_type": "code", "execution_count": null, "id": "d5c81a9e-9f4a-403e-bcbc-5229469058c3", "metadata": {}, "outputs": [], "source": ["del existing_stores\n", "del existing_stores2\n", "del fetch_new_city_lat_long_raw\n", "del fetch_new_city_lat_long_raw2\n", "del item_catalog_filtered\n", "del state_cluster_raw\n", "del emerging_cities_sister_city\n", "del item_catalog_raw\n", "del existing_cities\n", "del city_hybrid_raw\n", "del frontend_outlets_raw"]}, {"cell_type": "code", "execution_count": null, "id": "1c8be37b-373b-411f-887d-665f4b12eba7", "metadata": {}, "outputs": [], "source": ["gc.collect()"]}, {"cell_type": "markdown", "id": "d584be04-0063-457d-a98d-064d38e28be8", "metadata": {}, "source": ["### City assortment type and assortment state calculation"]}, {"cell_type": "code", "execution_count": null, "id": "1147ad31-2b97-4d8b-848f-09d470821e61", "metadata": {}, "outputs": [], "source": ["pfma_mapping = f\"\"\"\n", "with fo as (SELECT\n", "    om.facility_id,\n", "    om.outlet_id AS outlet_id,\n", "    mo_map.frontend_merchant_id AS merchant_id,\n", "    om.outlet_name AS outlet_name,\n", "    rcl.id as city_id,\n", "    rcl.name AS city_name,\n", "    case when type_id = 3 then 'LONGTAIL_ONLY' else 'EXPRESS' end as store_type,\n", "    case when long_tail_facility_id is not null then 'LONGTAIL' else 'EXPRESS_ONLY' end as store_type_ams\n", "  FROM\n", "    po.physical_facility_outlet_mapping om\n", "    INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "    AND rco.business_type_id IN (7)\n", "    AND rco.company_type_id NOT IN (771, 767)\n", "    AND rco.lake_active_record\n", "    INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "    AND rcl.lake_active_record\n", "    INNER JOIN dwh.dim_merchant_outlet_facility_mapping mo_map ON mo_map.facility_id = om.facility_id\n", "    AND rco.business_type_id = 7\n", "    AND is_current\n", "    AND is_current_mapping_active\n", "    AND is_backend_merchant_active\n", "    AND is_frontend_merchant_active\n", "    INNER JOIN (\n", "      SELECT\n", "        distinct cast(merchant_id AS int) merchant_id\n", "      FROM\n", "        serviceability.ser_store_polygons\n", "      WHERE\n", "        TYPE in ('STORE_POLYGON', 'LONGTAIL_POLYGON')\n", "        AND is_active = TRUE\n", "        AND lake_active_record\n", "    ) poly ON poly.merchant_id = mo_map.frontend_merchant_id\n", "    left join (select distinct long_tail_facility_id from po.long_tail_facility_store_mapping where active = 1) long_\n", "    on long_.long_tail_facility_id = om.facility_id\n", "  WHERE\n", "    om.active = 1\n", "    AND om.ars_active = 1\n", "    AND om.lake_active_record\n", "    AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "    AND om.outlet_name NOT LIKE '%%Draft%%'\n", "    AND om.outlet_name NOT LIKE '%%Test Store%%'\n", "    AND om.outlet_name NOT LIKE '%%Dummy%%'\n", "    AND om.facility_id not in (273,2943)\n", "    AND om.outlet_id IN (\n", "      SELECT\n", "        DISTINCT outlet_id\n", "      FROM\n", "        po.bulk_facility_outlet_mapping\n", "      WHERE\n", "        active\n", "        AND lake_active_record))\n", "  \n", "    SELECT\n", "        pfma.city_id,\n", "        pfma.assortment_type,\n", "        pfma.master_assortment_substate_id,\n", "        pfma.item_id,\n", "        RANK() OVER (\n", "            PARTITION BY pfma.item_id, pfma.city_id\n", "            ORDER BY COUNT(fo.facility_id) DESC\n", "        ) AS rank_\n", "    FROM\n", "        rpc.product_facility_master_assortment pfma\n", "    INNER JOIN\n", "        fo ON fo.facility_id = pfma.facility_id\n", "    WHERE\n", "        pfma.active = 1\n", "        AND pfma.lake_active_record\n", "        AND pfma.master_assortment_substate_id IN (1, 3)\n", "        group  by 1,2,3,4\n", "    \"\"\"\n", "pfma_mapping_raw = pd.read_sql_query(sql=pfma_mapping, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "5e94c1db-ab81-42d0-9f15-3fc4c166fbb9", "metadata": {}, "outputs": [], "source": ["# Define type priority\n", "priority_types = [\"UNICORN\", \"SUPER_LONGTAIL\"]\n", "\n", "# Step 1: Separate unicorn/super_longtail\n", "high_priority = pfma_mapping_raw[pfma_mapping_raw[\"assortment_type\"].isin(priority_types)].copy()\n", "\n", "# Step 2: Take the top rank == 1 row from EXPRESS or LONGTAIL\n", "rank_1 = pfma_mapping_raw[\n", "    (pfma_mapping_raw[\"assortment_type\"].isin([\"EXPRESS\", \"LONGTAIL\"]))\n", "    & (pfma_mapping_raw[\"rank_\"] == 1)\n", "].copy()\n", "\n", "# Step 3: Combine both, giving priority to unicorn/super_longtail\n", "# Drop duplicates on (city_id, item_id), keeping high-priority rows\n", "combined = pd.concat([high_priority, rank_1], ignore_index=True)\n", "final_df = combined.drop_duplicates(subset=[\"city_id\", \"item_id\"], keep=\"first\")"]}, {"cell_type": "code", "execution_count": null, "id": "b89589f6-0f77-4db5-aa85-4e455f4a0f26", "metadata": {}, "outputs": [], "source": ["pfma_mapping_raw = final_df.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "b546e542-3329-4d42-8b9e-2d7bfa838b8e", "metadata": {}, "outputs": [], "source": ["del high_priority\n", "del rank_1\n", "del combined\n", "del final_df"]}, {"cell_type": "code", "execution_count": null, "id": "b909f756-eb36-401f-ba6b-ec7c183a31bc", "metadata": {"tags": []}, "outputs": [], "source": ["pfma_mapping_raw.drop(columns=\"rank_\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "101ba8d8-f4ce-455e-9d4f-a426c3dee97a", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit = city_weights_raw_crit.merge(\n", "    pfma_mapping_raw, how=\"left\", on=[\"item_id\", \"city_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f848f427-1407-4955-af16-538f1cb2b6d1", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit.columns"]}, {"cell_type": "markdown", "id": "c193874c-59e9-473e-aa0e-c2c55a7df0a5", "metadata": {}, "source": ["### Sister city assortment type and assortment state"]}, {"cell_type": "code", "execution_count": null, "id": "d7ecefef-2f41-416d-b471-87ac880eb6fb", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit = city_weights_raw_crit.merge(\n", "    pfma_mapping_raw.rename(\n", "        columns={\n", "            \"city_id\": \"sister_city_id\",\n", "            \"assortment_type\": \"sister_city_assortment_type\",\n", "            \"master_assortment_substate_id\": \"sister_city_master_assortment_substate_id\",\n", "        }\n", "    ),\n", "    how=\"left\",\n", "    on=[\"item_id\", \"sister_city_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "583fd084-97ad-41c1-ae62-677cb34e45bb", "metadata": {}, "outputs": [], "source": ["pfma_mapping_raw.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "6c8ec111-d823-4912-b3b1-85b525822746", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit = city_weights_raw_crit.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "50c16070-cc50-4764-a97a-7b1640c035fa", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit.head()"]}, {"cell_type": "code", "execution_count": null, "id": "48e5aa31-865d-4d32-9bac-7b0421b71486", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit.shape"]}, {"cell_type": "code", "execution_count": null, "id": "b4829871-078d-42b1-a568-3dc43528a970", "metadata": {}, "outputs": [], "source": ["del pfma_mapping_raw\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "4e7cf2a5-8715-4dc3-a3db-e95930efa278", "metadata": {}, "outputs": [], "source": ["# city_weights_raw_crit.rename(columns = {'assortment_type_x':'item_category','assortment_type_y':'assortment_type'},inplace = True)"]}, {"cell_type": "code", "execution_count": null, "id": "618d18c1-b433-4cfc-8cc8-c975f45fe3e6", "metadata": {}, "outputs": [], "source": ["pfma_mapping_store = f\"\"\"\n", "with fo as (SELECT\n", "    om.facility_id,\n", "    om.outlet_id AS outlet_id,\n", "    mo_map.frontend_merchant_id AS merchant_id,\n", "    om.outlet_name AS outlet_name,\n", "    rcl.id as city_id,\n", "    rcl.name AS city_name,\n", "    case when type_id = 3 then 'LONGTAIL_ONLY' else 'EXPRESS' end as store_type,\n", "    case when long_tail_facility_id is not null then 'LONGTAIL' else 'EXPRESS_ONLY' end as store_type_ams\n", "  FROM\n", "    po.physical_facility_outlet_mapping om\n", "    INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "    AND rco.business_type_id IN (7)\n", "    AND rco.company_type_id NOT IN (771, 767)\n", "    AND rco.lake_active_record\n", "    INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "    AND rcl.lake_active_record\n", "    INNER JOIN dwh.dim_merchant_outlet_facility_mapping mo_map ON mo_map.facility_id = om.facility_id\n", "    AND rco.business_type_id = 7\n", "    AND is_current\n", "    AND is_current_mapping_active\n", "    AND is_backend_merchant_active\n", "    AND is_frontend_merchant_active\n", "    INNER JOIN (\n", "      SELECT\n", "        distinct cast(merchant_id AS int) merchant_id\n", "      FROM\n", "        serviceability.ser_store_polygons\n", "      WHERE\n", "        TYPE in ('STORE_POLYGON', 'LONGTAIL_POLYGON')\n", "        AND is_active = TRUE\n", "        AND lake_active_record\n", "    ) poly ON poly.merchant_id = mo_map.frontend_merchant_id\n", "    left join (select distinct long_tail_facility_id from po.long_tail_facility_store_mapping where active = 1) long_\n", "    on long_.long_tail_facility_id = om.facility_id\n", "  WHERE\n", "    om.active = 1\n", "    AND om.ars_active = 1\n", "    AND om.lake_active_record\n", "    AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "    AND om.outlet_name NOT LIKE '%%Draft%%'\n", "    AND om.outlet_name NOT LIKE '%%Test Store%%'\n", "    AND om.outlet_name NOT LIKE '%%Dummy%%'\n", "    AND om.facility_id not in (273,2943)\n", "    AND om.outlet_id IN (\n", "      SELECT\n", "        DISTINCT outlet_id\n", "      FROM\n", "        po.bulk_facility_outlet_mapping\n", "      WHERE\n", "        active\n", "        AND lake_active_record))\n", "  \n", "    SELECT\n", "        pfma.facility_id,\n", "        pfma.assortment_type,\n", "        pfma.master_assortment_substate_id,\n", "        pfma.item_id\n", "    FROM\n", "        rpc.product_facility_master_assortment pfma\n", "    INNER JOIN\n", "        fo ON fo.facility_id = pfma.facility_id\n", "    WHERE\n", "        pfma.active = 1\n", "        AND pfma.lake_active_record\n", "        AND pfma.master_assortment_substate_id IN (1, 3)\n", "        and item_id in {tuple(city_weights_raw_crit['item_id'].unique())}\n", "        group  by 1,2,3,4\n", "    \"\"\"\n", "pfma_mapping_store_raw = pd.read_sql_query(sql=pfma_mapping_store, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "1c59618b-b358-4686-98b8-5ae462ff7a9c", "metadata": {}, "outputs": [], "source": ["pfma_mapping_store_raw.shape"]}, {"cell_type": "code", "execution_count": null, "id": "3c2e8923-6fe3-4b80-8bbf-4ea0261cb0fb", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit = city_weights_raw_crit.merge(\n", "    pfma_mapping_store_raw.rename(\n", "        columns={\n", "            \"assortment_type\": \"store_assortment_type\",\n", "            \"master_assortment_substate_id\": \"store_master_assortment_substate_id\",\n", "        }\n", "    ),\n", "    how=\"left\",\n", "    on=[\"item_id\", \"facility_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ac13d19c-58a7-4b8f-8c24-38dc382de384", "metadata": {"tags": []}, "outputs": [], "source": ["del pfma_mapping_store_raw\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "d08d7a64-9dd1-401f-be54-5416d081951d", "metadata": {}, "outputs": [], "source": ["immunity_reasons = readFromSheetsWithRetry(\n", "    spreadSheetId, sheetName, raiseExceptionFlag=False, mode=\"EXECUTION_MODE\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "34bfba34-ae21-45c6-b26d-cd5f7ca4c409", "metadata": {}, "outputs": [], "source": ["city_exclude = readFromSheetsWithRetry(\n", "    spreadSheetId, sheetName2, raiseExceptionFlag=False, mode=\"EXECUTION_MODE\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "cb03ca29-3841-4993-88d2-8da70c112959", "metadata": {}, "outputs": [], "source": ["Items_exclude = readFromSheetsWithRetry(\n", "    spreadSheetId, sheetName3, raiseExceptionFlag=False, mode=\"EXECUTION_MODE\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c4558638-ffed-4c3a-8f81-c5cbdbab1b66", "metadata": {}, "outputs": [], "source": ["city_exclude[\"city_exclude_flag\"] = 1\n", "Items_exclude[\"Items_exclude_flag\"] = 1"]}, {"cell_type": "code", "execution_count": null, "id": "e48f7d00-2b31-4b6c-b763-d9789e389185", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit = city_weights_raw_crit.merge(city_exclude, how=\"left\", on=\"city\")"]}, {"cell_type": "code", "execution_count": null, "id": "255eb772-315a-4ed5-9f74-aba38d922f07", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit = city_weights_raw_crit.merge(Items_exclude, how=\"left\", on=\"l2\")"]}, {"cell_type": "code", "execution_count": null, "id": "b82dfebc-9d5d-435c-9bc2-d2d428458257", "metadata": {}, "outputs": [], "source": ["del city_exclude\n", "del Items_exclude"]}, {"cell_type": "code", "execution_count": null, "id": "a0be9a92-b9fb-48ca-9a33-61f74f137105", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit = city_weights_raw_crit[\n", "    ~city_weights_raw_crit[\"store_master_assortment_substate_id\"].isin([1, 3])\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "d6abf1e8-d2e0-4776-ae75-b437625ddf8f", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit.shape"]}, {"cell_type": "code", "execution_count": null, "id": "5e91d7cd-38f5-44e1-84b6-ffec111e8dc8", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit[\"remarks\"] = np.where(\n", "    (city_weights_raw_crit[\"assortment_type\"] == 0)\n", "    & (city_weights_raw_crit[\"sister_city_assortment_type\"] == 0),\n", "    \"Not active in city or sister city\",\n", "    None,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "acc434a2-d32c-461b-bbf0-05404c2b1f6a", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit[\"remarks\"] = np.where(\n", "    (city_weights_raw_crit[\"remarks\"].isna()) & (city_weights_raw_crit[\"launch_type\"] == \"HYBRID\"),\n", "    \"Hybrid sku\",\n", "    city_weights_raw_crit[\"remarks\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "03de5f1c-0585-41df-9750-e3ca2609e35c", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit[\"remarks\"] = np.where(\n", "    (city_weights_raw_crit[\"remarks\"].isna())\n", "    & (\n", "        city_weights_raw_crit[\"assortment_type\"].isin([\"SUPER_LONGTAIL\", \"UNICORN\"])\n", "        | city_weights_raw_crit[\"sister_city_assortment_type\"].isin([\"SUPER_LONGTAIL\", \"UNICORN\"])\n", "    ),\n", "    \"Super longtail or unicorn skus\",\n", "    city_weights_raw_crit[\"remarks\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "913b3579-c2ba-4350-bf06-e500c5665555", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit[\"remarks\"] = np.where(\n", "    (city_weights_raw_crit[\"remarks\"].isna())\n", "    & (\n", "        (\n", "            (city_weights_raw_crit[\"master_assortment_substate_id\"] != 0)\n", "            & (\n", "                (\n", "                    (city_weights_raw_crit[\"assortment_type\"] == \"EXPRESS\")\n", "                    & (city_weights_raw_crit[\"store_type\"] != \"EXPRESS\")\n", "                )\n", "                | (\n", "                    (city_weights_raw_crit[\"assortment_type\"] == \"LONGTAIL\")\n", "                    & (city_weights_raw_crit[\"store_type_ams\"] != \"LONGTAIL\")\n", "                )\n", "            )\n", "        )\n", "        | (\n", "            (city_weights_raw_crit[\"master_assortment_substate_id\"] == 0)\n", "            & (\n", "                (\n", "                    (city_weights_raw_crit[\"sister_city_assortment_type\"] == \"EXPRESS\")\n", "                    & (city_weights_raw_crit[\"store_type\"] != \"EXPRESS\")\n", "                )\n", "                | (\n", "                    (city_weights_raw_crit[\"sister_city_assortment_type\"] == \"LONGTAIL\")\n", "                    & (city_weights_raw_crit[\"store_type_ams\"] != \"LONGTAIL\")\n", "                )\n", "            )\n", "        )\n", "        # |\n", "        # (city_weights_raw_crit['store_type'] == 'LONGTAIL_ONLY') |\n", "        # (city_weights_raw_crit['store_type_ams'] == 'EXPRESS_ONLY')\n", "    ),\n", "    \"Store type not aligned or standalone longtail/express only outlet\",\n", "    city_weights_raw_crit[\"remarks\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f148a44b-d77e-4f52-be8a-32e2dafbd6f5", "metadata": {}, "outputs": [], "source": ["cms_stores = f\"\"\"\n", "select * from retail.console_outlet_cms_store where active = 1 and cms_update_active = 1 \"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "083ead4e-ecf3-44fe-8103-702e9194430f", "metadata": {}, "outputs": [], "source": ["cms_stores_raw = pd.read_sql_query(sql=cms_stores, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "cee3ae02-71e3-48d4-812e-9fbf9e38f516", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit = city_weights_raw_crit.merge(\n", "    cms_stores_raw[[\"outlet_id\", \"cms_update_active\"]], how=\"left\", on=\"outlet_id\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5e5a5355-2448-4108-a915-a7d131d418a1", "metadata": {}, "outputs": [], "source": ["del cms_stores_raw"]}, {"cell_type": "code", "execution_count": null, "id": "5e27b57d-fc49-4cb4-a7a4-ba1443015abe", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit[\"remarks\"] = np.where(\n", "    (city_weights_raw_crit[\"remarks\"].isna()) & (city_weights_raw_crit[\"cms_update_active\"] == 0),\n", "    \"Merchant not active\",\n", "    city_weights_raw_crit[\"remarks\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "791e616d-0a0d-4412-b368-1a5a3051a47e", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit[\"remarks\"] = np.where(\n", "    (city_weights_raw_crit[\"remarks\"].isna())\n", "    & (city_weights_raw_crit[\"item_category\"].isin([\"FnV\", \"Frozen\", \"Perishable\"])),\n", "    \"FnV/Perishable/Frozen sku\",\n", "    city_weights_raw_crit[\"remarks\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "68b3895f-ef9d-4837-a06f-db2bae355dc6", "metadata": {}, "outputs": [], "source": ["# upload_creation[(upload_creation['item_id']==10005915)&(upload_creation['city']=='Nashik')].remarks"]}, {"cell_type": "code", "execution_count": null, "id": "a1dd0823-242f-4a8a-a655-cc88de65735c", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit[\"remarks\"] = np.where(\n", "    (city_weights_raw_crit[\"remarks\"].isna()) & (city_weights_raw_crit[\"Items_exclude_flag\"] == 1),\n", "    \"Items excluded from expansion\",\n", "    city_weights_raw_crit[\"remarks\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6d839ab4-4c81-40fe-9969-657746670eee", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit[\"remarks\"] = np.where(\n", "    (city_weights_raw_crit[\"remarks\"].isna())\n", "    & (\n", "        (city_weights_raw_crit[\"city_exclude_flag\"] == 1)\n", "        & (city_weights_raw_crit[\"sister_city_id\"] > 0)\n", "    ),\n", "    \"city excluded from cross expansion\",\n", "    city_weights_raw_crit[\"remarks\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "858adef9-5e07-4f5b-8db1-51cc57558f92", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit[city_weights_raw_crit[\"remarks\"].isna()].shape"]}, {"cell_type": "code", "execution_count": null, "id": "e91dc0b8-1b55-4483-8537-930b04e16693", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit[\"remarks\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "e8eec81f-0a5c-44a1-8a5e-12be1b5c0a83", "metadata": {}, "outputs": [], "source": ["upload_creation = city_weights_raw_crit[city_weights_raw_crit[\"remarks\"].isna()]"]}, {"cell_type": "code", "execution_count": null, "id": "536ccae9-c8f9-4b9c-848b-c68bb946fc50", "metadata": {}, "outputs": [], "source": ["upload_creation.shape"]}, {"cell_type": "code", "execution_count": null, "id": "a37ab9a3-7485-422a-9827-0e6cd0b621b4", "metadata": {}, "outputs": [], "source": ["reasons = f\"\"\"\n", "  SELECT\n", "    *\n", "  FROM\n", "    (\n", "      SELECT\n", "        item_id,\n", "        facility_id,\n", "        state,\n", "        date(updated_at) AS updated_at,\n", "        assortment_type,\n", "        request_type,\n", "        TRY_CAST(\n", "          JSON_EXTRACT(meta, '$.request_reason_type') AS varchar\n", "        ) AS reason,\n", "        request_id AS job_id,\n", "        rank() OVER (\n", "          PARTITION BY item_id,\n", "          facility_id\n", "          ORDER BY\n", "            updated_at DESC\n", "        ) AS update_rank,\n", "        created_by\n", "      FROM\n", "        rpc.item_facility_state_request\n", "      WHERE\n", "        insert_ds_ist >= CAST (\n", "          CURRENT_DATE - interval '100' DAY AS varchar\n", "        )\n", "        AND active = 1\n", "        AND state IN (\n", "          1,\n", "          3,\n", "          2,\n", "          4\n", "        )\n", "        and facility_id in {tuple(upload_creation['facility_id'].unique())}\n", "        AND status = 'COMPLETED'\n", "        AND item_id IN {tuple(upload_creation['item_id'].unique())}\n", "        AND NOT (\n", "          state = 4\n", "          AND created_by = 14\n", "        )\n", "    ) AS X\n", "  WHERE\n", "    update_rank = 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "348d43c2-a7ad-4435-9a22-48946c41264c", "metadata": {}, "outputs": [], "source": ["reasons_raw = pd.read_sql_query(sql=reasons, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "8d1c4073-f0c4-4a4b-ab04-e2c413528ed1", "metadata": {}, "outputs": [], "source": ["upload_creation = upload_creation.merge(reasons_raw, on=[\"facility_id\", \"item_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "532386e4-6ccc-4ef2-955e-53a1102f9dc3", "metadata": {}, "outputs": [], "source": ["immunity_reasons[\"Type\"].fillna(\"outlet\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "3d30c3cc-2258-48ef-84db-3ab5288622ea", "metadata": {}, "outputs": [], "source": ["immunity_reasons.dropna(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "d6a47ba0-3139-44e8-a19b-58e0c34b7c7b", "metadata": {}, "outputs": [], "source": ["upload_creation = upload_creation.merge(immunity_reasons, on=[\"reason\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "6b6c52de-59ad-4ab0-aa66-24318e40a60b", "metadata": {}, "outputs": [], "source": ["del immunity_reasons\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "3a233c6b-6ce3-4771-890a-fa1de48de782", "metadata": {}, "outputs": [], "source": ["upload_creation[\"should_not_add\"] = np.where(\n", "    (\n", "        (\n", "            (upload_creation[\"Type\"] == \"city\")\n", "            & (upload_creation[\"master_assortment_substate_id\"] == 0)\n", "        )\n", "        | (\n", "            (upload_creation[\"Type\"] == \"outlet\")\n", "            & (upload_creation[\"master_assortment_substate_id\"] >= 0)\n", "        )\n", "    ),\n", "    1,\n", "    0,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f40fb817-718b-4509-abe5-214196e6ea89", "metadata": {}, "outputs": [], "source": ["upload_creation[\"remarks\"] = np.where(\n", "    (upload_creation[\"should_not_add\"] == 1),\n", "    \"Immunity reason code due to \" + upload_creation[\"reason\"].astype(str),\n", "    upload_creation[\"remarks\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6b475d85-36c1-401a-a516-e6366f72c109", "metadata": {}, "outputs": [], "source": ["upload_creation[upload_creation[\"remarks\"].isna()].shape"]}, {"cell_type": "code", "execution_count": null, "id": "02849584-b0eb-497d-a567-bb2cd385ab23", "metadata": {}, "outputs": [], "source": ["open_requests = f\"\"\"\n", "select item_id, facility_id, status from rpc.item_facility_state_request where status = 'OPEN' and insert_ds_ist >= cast(current_date - interval '20' day as varchar) and active = 1\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "176eaf46-8980-42de-8916-52ee0d946361", "metadata": {}, "outputs": [], "source": ["open_requests_raw = pd.read_sql_query(sql=open_requests, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "ec4cd519-62b9-431a-8fd7-bdf04d22d945", "metadata": {}, "outputs": [], "source": ["open_requests_raw[\n", "    (open_requests_raw[\"item_id\"] == 10005915) & (open_requests_raw[\"facility_id\"] == 2821)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "1f1bbfe3-34b1-46b8-b528-ff393a6a9197", "metadata": {}, "outputs": [], "source": ["upload_creation = upload_creation.merge(\n", "    open_requests_raw, how=\"left\", on=[\"item_id\", \"facility_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "269a5867-cc80-4b9d-9537-5badf58cf62c", "metadata": {}, "outputs": [], "source": ["upload_creation[\"remarks\"] = np.where(\n", "    (upload_creation[\"remarks\"].isna()) & (upload_creation[\"status\"] == \"OPEN\"),\n", "    \"Open request exists\",\n", "    upload_creation[\"remarks\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "56d316d4-9f66-4785-b088-63b9fd5d1d94", "metadata": {}, "outputs": [], "source": ["upload_creation[\"remarks\"] = np.where(\n", "    (upload_creation[\"remarks\"].isna()), \"Upload created\", upload_creation[\"remarks\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "221904be-68e2-4aea-b185-3b58398fc6e5", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit = city_weights_raw_crit.merge(\n", "    upload_creation[[\"item_id\", \"facility_id\", \"remarks\"]],\n", "    how=\"left\",\n", "    on=[\"item_id\", \"facility_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f403d635-ae3a-4948-8b6a-050161196cec", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit[\"remarks_x\"] = np.where(\n", "    city_weights_raw_crit[\"remarks_x\"].isna(),\n", "    city_weights_raw_crit[\"remarks_y\"],\n", "    city_weights_raw_crit[\"remarks_x\"],\n", ")\n", "city_weights_raw_crit.rename(columns={\"remarks_x\": \"remarks\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "2de21a0d-3e94-4e83-b473-6f3c547d84d0", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit"]}, {"cell_type": "code", "execution_count": null, "id": "7f95eadf-69c3-41fa-ad68-a6b9c45ec4c6", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit[\"cms_food_type\"] = city_weights_raw_crit[\"cms_food_type\"].astype(str)\n", "city_weights_raw_crit[\"launch_type\"] = city_weights_raw_crit[\"launch_type\"].astype(str)\n", "city_weights_raw_crit[\"sister_city_launch_type\"] = city_weights_raw_crit[\n", "    \"sister_city_launch_type\"\n", "].astype(str)\n", "city_weights_raw_crit[\"assortment_type\"] = city_weights_raw_crit[\"assortment_type\"].astype(str)\n", "city_weights_raw_crit[\"sister_city_assortment_type\"] = city_weights_raw_crit[\n", "    \"sister_city_assortment_type\"\n", "].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "cd24ab84-b8ab-44cc-8253-7341473f067b", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit[\"sister_city_assortment_type\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "78edfaf1-a205-4ba4-9b51-8c5ab2a15479", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit.drop(columns=[\"remarks_y\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "3671106c-16ea-4b9c-b4fd-44773305bf31", "metadata": {}, "outputs": [], "source": ["pushDfToTrino(\n", "    city_weights_raw_crit,\n", "    NotActiveAssortmentReasons,\n", "    description=\"critical_skus_city_assortment_current_snapshot\",\n", "    primaryKeys=[\"city\", \"city_id\"],\n", "    load_type=\"truncate\",\n", "    environ=\"supply_etls\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b6a969da-a479-41da-98ab-3a2fa66684b4", "metadata": {}, "outputs": [], "source": ["pushDfToTrino(\n", "    city_weights_raw_crit,\n", "    NotActiveAssortmentReasonsLog,\n", "    description=\"critical_skus_city_assortment_current_snapshot\",\n", "    primaryKeys=[\"city\", \"city_id\"],\n", "    load_type=\"append\",\n", "    environ=\"supply_etls\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "42a8cfa1-596c-401b-a4b0-b44a7392a180", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit[\"cms_food_type\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "f6a167a9-334d-4668-abb6-46a8b54ff56d", "metadata": {}, "outputs": [], "source": ["city_weights_raw_crit[\"item_category\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "6024f921-54fa-479f-a19f-e1041bcfad63", "metadata": {}, "outputs": [], "source": ["del city_weights_raw_crit\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "71cddf95-3736-409f-8624-0179b7d22ca5", "metadata": {}, "outputs": [], "source": ["upload_creation = upload_creation[upload_creation[\"remarks\"] == \"Upload created\"]"]}, {"cell_type": "code", "execution_count": null, "id": "1cd7ed59-b743-4230-b95a-70d5364ba322", "metadata": {}, "outputs": [], "source": ["upload_creation.shape"]}, {"cell_type": "code", "execution_count": null, "id": "44a86d28-bbe5-4b21-acce-c75de0b0eb5c", "metadata": {}, "outputs": [], "source": ["upload_creation[\"upload_master_assortment_substate_id\"] = np.where(\n", "    upload_creation[\"master_assortment_substate_id\"] > 0,\n", "    upload_creation[\"master_assortment_substate_id\"],\n", "    upload_creation[\"sister_city_master_assortment_substate_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3212a1f9-e5e1-498b-920c-bba2bc8efa25", "metadata": {}, "outputs": [], "source": ["upload_creation[\"upload_assortment_type\"] = np.where(\n", "    upload_creation[\"assortment_type_x\"] != 0,\n", "    upload_creation[\"assortment_type_x\"],\n", "    upload_creation[\"sister_city_assortment_type\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d747866f-62a7-4158-80cb-f3213d24d16c", "metadata": {}, "outputs": [], "source": ["upload_creation[\"request_reason_type\"] = np.where(\n", "    upload_creation[\"source\"] == \"same city\",\n", "    \"CITY_TOP_SKUS_AUTO_EXPANSION\",\n", "    \"CROSS_CITY_TOP_SKUS_AUTO_EXPANSION\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "76ea6ec2-d6cd-4baa-b30d-91840358ca2f", "metadata": {}, "outputs": [], "source": ["upload_creation.rename(columns={\"facility_id\": \"frontend_facility_id\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "702a3d94-8701-4094-9a8a-81b636dd10d3", "metadata": {}, "outputs": [], "source": ["upload_creation = upload_creation[\n", "    [\n", "        \"upload_assortment_type\",\n", "        \"upload_master_assortment_substate_id\",\n", "        \"frontend_facility_id\",\n", "        \"item_id\",\n", "        \"request_reason_type\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "33812f0f-e925-4836-b888-f59463146b8b", "metadata": {}, "outputs": [], "source": ["upload_creation[\"master_assortment_substate\"] = np.where(\n", "    upload_creation[\"upload_master_assortment_substate_id\"] == 3, \"Temporarily Inactive\", \"Active\"\n", ")\n", "upload_creation = upload_creation.drop(columns=\"upload_master_assortment_substate_id\")\n", "upload_creation[\"city_name\"] = \"\"\n", "upload_creation[\"backend_facility_id\"] = \"\"\n", "upload_creation[\"substate_reason_type\"] = \"BAU\"\n", "upload_creation[\"assortment_type\"] = np.where(\n", "    upload_creation[\"upload_assortment_type\"] == \"LONG<PERSON><PERSON>\", \"LON<PERSON><PERSON><PERSON>\", \"EXPRESS_ALL\"\n", ")\n", "request_df = upload_creation[\n", "    [\n", "        \"item_id\",\n", "        \"city_name\",\n", "        \"backend_facility_id\",\n", "        \"frontend_facility_id\",\n", "        \"master_assortment_substate\",\n", "        \"assortment_type\",\n", "        \"substate_reason_type\",\n", "        \"request_reason_type\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "d9ee060a-a0d5-4bc1-8fb7-024770b99255", "metadata": {}, "outputs": [], "source": ["request_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "f203e442-7356-4175-9bbc-923bf871decd", "metadata": {}, "outputs": [], "source": ["del upload_creation\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "1d546069-6d89-44ed-8fea-d1a1774a3ec5", "metadata": {}, "outputs": [], "source": ["import boto3\n", "import os\n", "import sys"]}, {"cell_type": "code", "execution_count": null, "id": "56b24827-091d-49ce-82a1-7efc15a91a0d", "metadata": {}, "outputs": [], "source": ["if request_df.shape[0] > 0:\n", "    request_df = convert_columns(request_df, output_column_types)\n", "    req_columns = [\n", "        \"item_id\",\n", "        \"city_name\",\n", "        \"backend_facility_id\",\n", "        \"frontend_facility_id\",\n", "        \"master_assortment_substate\",\n", "        \"assortment_type\",\n", "        \"substate_reason_type\",\n", "        \"request_reason_type\",\n", "    ]\n", "    flag = check_required_columns(request_df, req_columns)\n", "    if not flag or request_df.shape[0] == 0:\n", "        print(\"invalid columns in the final df for upload, doing nothing\", input_df.columns)\n", "    else:\n", "        output_df = processFile(request_df)\n", "        job_ids = output_df.job_id.unique()\n", "        # for job_id in job_ids:\n", "        print(\"job_id:\", job_ids)\n", "        sendSlackAlert(\n", "            EXECUTION_MODE,\n", "            f\"response of the file upload critical assortment rationalisation : \" + str(job_ids),\n", "        )\n", "        # output_df['replicated_stores'] = input_facility_id\n", "        print(\"uploading the output file\")\n", "        # pushToGoogleSheetsWithRetry\n", "        # (output_df, spreadSheetId, writesheetName)\n", "else:\n", "    print(\"Nothing to upload\")"]}, {"cell_type": "code", "execution_count": null, "id": "56867783-6bbc-4121-bad9-804db121b334", "metadata": {}, "outputs": [], "source": ["print(\"Done\")"]}, {"cell_type": "code", "execution_count": null, "id": "d2210a60-b09d-486c-9284-19d75fbdd450", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}