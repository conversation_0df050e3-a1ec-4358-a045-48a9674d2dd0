alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: packaged_assortment_cut
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RR41CKQX
path: povms/assortment_rationalisation/etl/packaged_assortment_cut
paused: false
pool: povms_pool
project_name: assortment_rationalisation
schedule:
  end_date: '2025-02-01T00:00:00'
  interval: 0 0 * * MON
  start_date: '2024-12-06T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 5
