{"cells": [{"cell_type": "code", "execution_count": null, "id": "8e4ce0e5-a783-4262-8d06-65ff1f832506", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "a50155f2-fccf-49b8-8d44-68dee1dbcb6f", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "markdown", "id": "b427004b-74de-486c-88c1-17546a4c8350", "metadata": {}, "source": ["## items to not touch"]}, {"cell_type": "code", "execution_count": null, "id": "d8f05640-4c5d-43d3-b91c-c6aee6d959df", "metadata": {}, "outputs": [], "source": ["current_festive_articles = f\"\"\"\n", "with lastest_uploads as (\n", "        select distinct\n", "            item_id,\n", "            festival_name as active_festival\n", "        from ars_etls.bulk_process_festival_quantity_planned bpf\n", "        where sale_end_date >= current_date\n", "            and active  and festival_name is not null\n", "    )\n", "\n", "select * from lastest_uploads\n", "\n", "\"\"\"\n", "current_festive_articles_raw = pd.read_sql_query(sql=current_festive_articles, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "c8aba8a8-7e44-473f-907c-5fd196ac5939", "metadata": {}, "outputs": [], "source": ["festive_tag_items = f\"\"\"\n", "With fo as (select facility_id, outlet_id, city_id, outlet_name from lake_po.physical_facility_outlet_mapping where active=1 and ars_active=1\n", "        and facility_id in (select facility_id from lake_retail.console_outlet where business_type_id = 7)\n", "   group by 1,2,3,4\n", ")\n", "select \n", "     distinct item_id,\n", "     reason_text as active_festival\n", "    from rpc.product_facility_master_assortment pfma\n", "    inner join rpc.product_master_assortment_substate_reasons sr on sr.id = pfma.substate_reason_id and sr.active = 1 \n", "    where facility_id in (select distinct facility_id from fo ) and reason_type = 'FESTIVE'\n", "    \"\"\"\n", "\n", "festive_tag_items = pd.read_sql_query(sql=festive_tag_items, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "8432e4d3-b397-4589-adb8-ed3de8e4b588", "metadata": {}, "outputs": [], "source": ["festive_tag_items = festive_tag_items.append(current_festive_articles_raw)"]}, {"cell_type": "code", "execution_count": null, "id": "dda8d814-d0df-4dca-a3e5-13d594a3989c", "metadata": {}, "outputs": [], "source": ["festive_tag_items"]}, {"cell_type": "code", "execution_count": null, "id": "1a621079-e4a5-4034-a951-6ff44a06e93a", "metadata": {}, "outputs": [], "source": ["festive_tag_items = festive_tag_items.drop_duplicates()"]}, {"cell_type": "markdown", "id": "b602237d-fe7f-4a3b-b6b1-aa4bf6463021", "metadata": {}, "source": ["## active ptype count at the store"]}, {"cell_type": "code", "execution_count": null, "id": "13c4c73a-0081-4fa3-a44a-0cb4d89fb1ae", "metadata": {}, "outputs": [], "source": ["active_ptype_count = f\"\"\"With fo as (select facility_id, outlet_id, city_id, outlet_name from lake_po.physical_facility_outlet_mapping where active=1 and ars_active=1\n", "        and facility_id in (select facility_id from lake_retail.console_outlet where business_type_id = 7)\n", "group by 1,2,3,4\n", "),\n", "item_details as (\n", "select p.item_id, \n", "product_type,\n", "d.name,\n", "l0,l1,l2,\n", "    CASE\n", "        WHEN p.handling_type='8' THEN 'PACKAGING_MATERIAL'\n", "        WHEN d.l0_id=1487 THEN 'FNV'\n", "        WHEN d.l1_id=183 THEN 'ICE_CREAM'\n", "        WHEN d.l2_id=1185 THEN 'MILK'\n", "        WHEN p.storage_type IN ('3',\n", "                                   '7') THEN 'FROZEN'\n", "        WHEN p.perishable=1 THEN 'PERISHABLE'\n", "        WHEN p.storage_type IN ('2',\n", "                                   '6') THEN 'COLD'\n", "        WHEN p.handling_type = '6' THEN 'MEDICINAL'\n", "        ELSE 'GROCERY'\n", "        END AS category,\n", "    case when (p.storage_type) in ('1','8') then 'REGULAR'\n", "        when (p.storage_type) in ('4','5') then 'HEAVY'\n", "        when (p.storage_type) in ('2','6') then 'COLD'\n", "        when (p.storage_type) in ('3','7') then 'FROZEN'\n", "        else 'REGULAR' end as item_storage_type\n", "    from rpc.product_product p\n", "    inner join (select item_id, max(id) as max_id from rpc.product_product where active = 1 group by 1) s on s.max_id = p.id\n", "    inner join rpc.item_category_details d on p.item_id = d.item_id\n", "    \n", ")\n", "select fo.facility_id,l2,product_type, count(distinct ma.item_id) as active_ptype_items\n", "from rpc.product_facility_master_assortment ma \n", "inner join fo on fo.facility_id = ma.facility_id and ma.active = 1 and ma.master_assortment_substate_id  not in (2,4)\n", "left join item_details id on id.item_id = ma.item_id\n", "group by 1,2,3\n", "\n", "\"\"\"\n", "active_ptype_count = pd.read_sql_query(sql=active_ptype_count, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "2667c305-3bce-4532-928f-1bdd91f45017", "metadata": {}, "outputs": [], "source": ["active_ptype_count.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8ad0d304-3d5c-4d2b-b17d-a32525aaafe2", "metadata": {}, "outputs": [], "source": ["active_ptype_count2 = active_ptype_count.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "a794cbd7-1558-4ee2-b318-f99ca86c9534", "metadata": {}, "outputs": [], "source": ["active_ptype_count3 = active_ptype_count.copy()"]}, {"cell_type": "markdown", "id": "764bfe28-c7af-43e2-ac82-589ad8413ff0", "metadata": {}, "source": ["## Special storage"]}, {"cell_type": "code", "execution_count": null, "id": "a68e262c-5124-4137-aa0d-4a9c27a44737", "metadata": {}, "outputs": [], "source": ["special_storage = f\"\"\"With fo as (select facility_id, outlet_id, city_id, outlet_name from lake_po.physical_facility_outlet_mapping where active=1 and ars_active=1\n", "    and facility_id in (select facility_id from lake_retail.console_outlet where business_type_id = 7)\n", "    ),\n", "\n", "storage as (\n", "    select \n", "        distinct\n", "        pfsc.facility_id,\n", "        pfsc.facility_name,\n", "        pfsc.storage_type,\n", "        pfsc.base_storage_type,\n", "        pfsc.threshold,\n", "        pfsc.storage_capacity as storage_cap\n", "    from\n", "        lake_ars.physical_facility_storage_capacity pfsc\n", "    inner join\n", "        (\n", "            select \n", "                distinct\n", "                facility_id,\n", "                storage_type,\n", "                base_storage_type,\n", "                max(updated_at) as updated_at_\n", "            from \n", "                lake_ars.physical_facility_storage_capacity \n", "            group by 1,2,3\n", "        ) sb2 on sb2.facility_id = pfsc.facility_id\n", "                and sb2.storage_type = pfsc.storage_type\n", "                and sb2.base_storage_type = pfsc.base_storage_type\n", "                and sb2.updated_at_ = pfsc.updated_at\n", "    where active \n", "    and pfsc.lake_active_record\n", ")\n", "\n", "        select\n", " fo.facility_id,\n", "            fo.outlet_name,\n", "            CASE when storage_type = 'BEAUTY_REGULAR' then 'BEAUTY'\n", "    when storage_type = 'BOOKS_REGULAR' then 'BOOKS'\n", "    when storage_type = 'TOYS_REGULAR' then 'TOYS'\n", "    when storage_type IN ('ELECTRONICS_REGULAR','ELECTRONICS_HEAVY')  then 'ELECTRONICS'\n", "    when storage_type = 'APPAREL_REGULAR' then 'APPAREL'\n", "    when storage_type = 'SPORTS_REGULAR' then 'SPORTS'\n", "    when storage_type = 'PET_CARE_REGULAR' then 'PET_CARE'\n", "    when storage_type = 'HOME_DECOR_REGULAR' then 'HOME_DECOR'\n", "    when storage_type = 'KITCHEN_DINING_REGULAR' then 'KITCHEN_DINING'\n", "           END AS cat,\n", "             base_storage_type\n", "        from fo\n", "        left join\n", "            storage s\n", "        on\n", "            s.facility_id = fo.facility_id\n", "            \n", "            where storage_type in ('<PERSON>LECT<PERSON><PERSON>CS_REGULAR','BOOKS_REGULAR','PET_CARE_REGULAR','SPORTS_REGULAR','TOYS_REGULAR','KITCHEN_DINING_REGULAR','APPAREL_REGULAR','BEAUTY_REGULAR','ELECTRONICS_HEAVY','HOME_DECOR_REGULAR')\n", "            \n", "\"\"\"\n", "special_storage = pd.read_sql_query(sql=special_storage, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "409424af-d3ee-449d-9612-ccb6dfc9e9f5", "metadata": {}, "outputs": [], "source": ["special_storage.head()"]}, {"cell_type": "markdown", "id": "e688f8c3-fcfd-4995-8ff1-3c0d69e1ec2a", "metadata": {}, "source": ["## items with special storage"]}, {"cell_type": "code", "execution_count": null, "id": "a23217f5-2b42-41dd-8462-bd86761b7c28", "metadata": {}, "outputs": [], "source": ["item_tag_type = f\"\"\"select \n", "distinct itm.item_id, \n", "case \n", "    when cast(tag_value as int) = 1 then 'BEAUTY'\n", "    when cast(tag_value as int) = 4 then 'BOOKS'\n", "    when cast(tag_value as int) = 7 then 'TOYS'\n", "    when cast(tag_value as int) = 8 then 'ELECTRONICS'\n", "    when cast(tag_value as int) = 14 then 'APPAREL'\n", "    when cast(tag_value as int) = 15 then 'SPORTS'\n", "    when cast(tag_value as int) = 16 then 'PET_CARE'\n", "    when cast(tag_value as int) = 17 then 'HOME_DECOR'\n", "    when cast(tag_value as int) = 18 then 'KITCHEN_DINING'\n", "end as cat\n", "from rpc.item_tag_mapping itm \n", "where tag_type_id = 7 and cast(tag_value as int) in (14,15,16,17,18,1,4,8,7) and active group by 1,2\"\"\"\n", "item_tag_type = pd.read_sql_query(sql=item_tag_type, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "ce6fc9bc-2a4a-436c-bd16-5b4f2a84185b", "metadata": {}, "outputs": [], "source": ["item_tag_type.head()"]}, {"cell_type": "markdown", "id": "3c90c14a-9e9e-46e2-8e7b-0eb619df3c82", "metadata": {}, "source": ["## item details"]}, {"cell_type": "code", "execution_count": null, "id": "c7ff3184-c91c-4e64-ae8c-dbfea430b2e3", "metadata": {}, "outputs": [], "source": ["item_details = f\"\"\"\n", "with item_details as (\n", "select p.item_id, \n", "product_type,\n", "d.name,\n", "l0,l1,l2,\n", "variant_mrp as mrp,\n", "weight_in_gm,\n", "    CASE\n", "        WHEN p.handling_type='8' THEN 'PACKAGING_MATERIAL'\n", "        WHEN d.l0_id=1487 THEN 'FNV'\n", "        WHEN d.l1_id=183 THEN 'ICE_CREAM'\n", "        WHEN d.l2_id=1185 THEN 'MILK'\n", "        WHEN p.storage_type IN ('3',\n", "                                   '7') THEN 'FROZEN'\n", "        WHEN p.perishable=1 THEN 'PERISHABLE'\n", "        WHEN p.storage_type IN ('2',\n", "                                   '6') THEN 'COLD'\n", "        WHEN p.handling_type = '6' THEN 'MEDICINAL'\n", "        ELSE 'GROCERY'\n", "        END AS category,\n", "    case when (p.storage_type) in ('1','8') then 'REGULAR'\n", "        when (p.storage_type) in ('4','5') then 'HEAVY'\n", "        when (p.storage_type) in ('2','6') then 'COLD'\n", "        when (p.storage_type) in ('3','7') then 'FROZEN'\n", "        else 'REGULAR' end as item_storage_type\n", "    from rpc.product_product p\n", "    inner join (select item_id, max(id) as max_id from rpc.product_product where active = 1 group by 1) s on s.max_id = p.id\n", "    inner join rpc.item_category_details d on p.item_id = d.item_id\n", "    \n", ")\n", "\n", "select * from item_details where item_storage_type in ('REGULAR','HEAVY') and category in ('MEDICINAL','GROCERY')\n", "\n", "\"\"\"\n", "item_details = pd.read_sql_query(sql=item_details, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "1d542ad9-2f37-4a9d-b4b0-1347752de749", "metadata": {}, "outputs": [], "source": ["item_details.head()"]}, {"cell_type": "markdown", "id": "0c42c50a-c7e6-49fe-9e94-7ad90fcc4199", "metadata": {}, "source": ["## item factor"]}, {"cell_type": "code", "execution_count": null, "id": "74b26cca-4ec4-4a6c-8307-c41bafe57a99", "metadata": {}, "outputs": [], "source": ["item_factor = f\"\"\"\n", "with ptype_item_factor as (\n", "with base as (\n", "select d.item_id, product_type, coalesce(if_.item_factor,0) as if_\n", "from \n", "rpc.item_category_details d \n", "left join\n", "    supply_etls.item_factor if_\n", "on \n", "    if_.item_id = d.item_id\n", "and\n", "    if_.updated_at is not null\n", "    -- where d.item_id = 10021233\n", "    ),\n", "\n", "base2 as (select  product_type, avg(if_) as ptype_avg from base group by 1),\n", "\n", "base3 as (select base.item_id, base.if_, \n", "case when ptype_avg > 0 then ptype_avg else 1.00 end as p_type_avg from base left join base2 on base.product_type = base2.product_type )\n", "\n", "select item_id, case when if_ >0 then if_ else p_type_avg end as item_factor from base3\n", "\n", ")\n", "\n", "select * from ptype_item_factor\n", "\"\"\"\n", "item_factor = pd.read_sql_query(sql=item_factor, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "b94b927b-0e05-4f88-8be6-bef6f4d81a6e", "metadata": {}, "outputs": [], "source": ["item_factor.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e43551eb-dfe1-42b2-bfb4-772057b37770", "metadata": {}, "outputs": [], "source": ["item_inventory = f\"\"\"\n", "With fo as (select facility_id, outlet_id, city_id, outlet_name from lake_po.physical_facility_outlet_mapping where active=1 and ars_active=1\n", "        and facility_id in (select facility_id from lake_retail.console_outlet where business_type_id = 7) \n", "   group by 1,2,3,4\n", ")\n", "\n", "select item_id, fo.outlet_id, coalesce(quantity,0) as inventory\n", "from fo \n", "left join ims.ims_item_inventory ii on ii.outlet_id = fo.outlet_id and ii.active = 1 \n", "group by 1,2,3\n", "\"\"\"\n", "item_inventory = pd.read_sql_query(sql=item_inventory, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "e9c4bb7b-ed57-409e-bf6f-a7b9ad711420", "metadata": {}, "outputs": [], "source": ["item_inventory.head()"]}, {"cell_type": "markdown", "id": "af2d3e99-ce34-429a-a3ef-2bfc1a0cc01a", "metadata": {}, "source": ["## available for last 30 days"]}, {"cell_type": "code", "execution_count": null, "id": "b8cad594-993f-408f-9ea4-52e12f68a01b", "metadata": {}, "outputs": [], "source": ["daily_order_availability = f\"\"\"\n", "With fo as (select facility_id, outlet_id, city_id, outlet_name from po.physical_facility_outlet_mapping where active=1 and ars_active=1\n", "        and facility_id in (select facility_id from retail.console_outlet where business_type_id = 7) \n", "   group by 1,2,3,4\n", "),\n", "\n", "base as (select\n", "   fo.facility_id, \n", "   fo.outlet_id,\n", "   ma.item_id,\n", "   master_assortment_substate_id\n", "from rpc.product_facility_master_assortment ma \n", "inner join fo on fo.facility_id = ma.facility_id and ma.active = 1 and ma.master_assortment_substate_id != 4\n", "left join  rpc.product_master_assortment_substate_reasons pmar\n", "on pmar.id = ma.substate_reason_id\n", "inner join retail.auth_user au on au.id = ma.created_by\n", " where ma.master_assortment_substate_id in (1,3) and reason_text not in ('SAMPLING') and assortment_type = 'EXPRESS' \n", " and email != '<EMAIL>'\n", " group by 1,2,3,4)\n", "\n", "select \n", "   b.facility_id, \n", "   b.outlet_id,\n", "   b.item_id,\n", "   avail_day_count,\n", "   coalesce(tot_order_qty,0) as quantity_ordered\n", "   from base b left join \n", "    (   select\n", "            doa.facility_id, \n", "            doa.item_id,\n", "            sum(orders) as tot_orders,\n", "            sum(order_quantity) as tot_order_qty,\n", "            count(case when available_hours >= 9 then order_date end) as avail_day_count\n", "        from\n", "            ars.daily_orders_and_availability doa\n", "        where\n", "            insert_ds_ist >= cast(current_date - interval '45' day as varchar)\n", "        and\n", "            order_date >= current_date -interval '45' day\n", "        group by 1,2\n", "    ) doa on doa.item_id = b.item_id and doa.facility_id = b.facility_id\n", "where avail_day_count >= 30\n", "group by 1,2,3,4,5\n", "\n", "\"\"\"\n", "daily_order_availability = pd.read_sql_query(sql=daily_order_availability, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "71b14dbe-4ee4-46ca-890f-dee3a2d447e8", "metadata": {}, "outputs": [], "source": ["daily_order_availability.head()"]}, {"cell_type": "markdown", "id": "423e837d-fedb-44f3-9cc0-8f6bc27967c7", "metadata": {}, "source": ["## First cut"]}, {"cell_type": "code", "execution_count": null, "id": "b7668420-2cbb-4e25-bfc5-1ff1a02eec02", "metadata": {}, "outputs": [], "source": ["first_cut = daily_order_availability[daily_order_availability[\"quantity_ordered\"] == 0]"]}, {"cell_type": "code", "execution_count": null, "id": "8763b21e-6d5f-4275-a0c6-a65b2873b62c", "metadata": {}, "outputs": [], "source": ["first_cut = first_cut.merge(festive_tag_items, on=\"item_id\", how=\"left\")\n", "first_cut = first_cut[first_cut[\"active_festival\"].isna()]\n", "first_cut = first_cut.merge(item_details, on=\"item_id\", how=\"left\")\n", "first_cut = first_cut.merge(item_tag_type, on=\"item_id\", how=\"left\")\n", "first_cut = first_cut.merge(\n", "    special_storage,\n", "    left_on=[\"facility_id\", \"cat\", \"item_storage_type\"],\n", "    right_on=[\"facility_id\", \"cat\", \"base_storage_type\"],\n", "    how=\"left\",\n", ")\n", "first_cut = first_cut[first_cut[\"base_storage_type\"].isna()]\n", "first_cut = first_cut.drop(columns=[\"cat\", \"outlet_name\", \"base_storage_type\"])\n", "first_cut.head()"]}, {"cell_type": "markdown", "id": "805c87cf-f68c-4988-ae5f-e9842e9b8965", "metadata": {}, "source": ["## ptype width maintaining"]}, {"cell_type": "code", "execution_count": null, "id": "0cec928d-e920-49ef-876d-d2446d73baec", "metadata": {}, "outputs": [], "source": ["first_cut_c_ptype = (\n", "    first_cut.groupby([\"facility_id\", \"outlet_id\", \"l2\", \"product_type\"])\n", "    .agg({\"item_id\": \"count\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"removed_ptype_items\"})\n", ")\n", "active_ptype_count2 = active_ptype_count2.merge(\n", "    first_cut_c_ptype[[\"facility_id\", \"l2\", \"product_type\", \"removed_ptype_items\"]],\n", "    how=\"left\",\n", "    on=[\"facility_id\", \"l2\", \"product_type\"],\n", ")\n", "active_ptype_count2 = active_ptype_count2.fillna(0)\n", "active_ptype_count2"]}, {"cell_type": "code", "execution_count": null, "id": "0ae37e19-76d6-45ce-bcf7-4d5045c06edb", "metadata": {}, "outputs": [], "source": ["active_ptype_count2[\"final_ptype_count\"] = (\n", "    active_ptype_count2[\"active_ptype_items\"] - active_ptype_count2[\"removed_ptype_items\"]\n", ")\n", "not_to_be_removed = active_ptype_count2[\n", "    (active_ptype_count2[\"final_ptype_count\"] < 3)\n", "    & (active_ptype_count2[\"removed_ptype_items\"] > 0)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "a6831128-4c13-457f-b46f-5113d3f174f2", "metadata": {}, "outputs": [], "source": ["not_to_be_removed.sort_values(by=[\"removed_ptype_items\"], ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "ae5b2b17-0503-4777-950a-2331d6b9b45c", "metadata": {}, "outputs": [], "source": ["city_ptype_rank = f\"\"\"With fo as (select facility_id, outlet_id, city_id, outlet_name from lake_po.physical_facility_outlet_mapping where active=1 and ars_active=1\n", "        and facility_id in (select facility_id from lake_retail.console_outlet where business_type_id = 7) \n", "   group by 1,2,3,4\n", "),\n", "\n", "base as (select city_id, l2, product_type,y.item_id, sum(multiplier*product_quantity) as quantity_sold, \n", "rank() over(partition by city_id,l2, product_type order by \n", "sum(multiplier*product_quantity) desc) as item_ptype_rank\n", "from dwh.fact_sales_order_item_details x\n", "inner join dwh.dim_item_product_offer_mapping y on x.product_id = y.product_id\n", "inner join rpc.item_category_details icd on icd.item_id = y.item_id\n", "inner join fo on fo.outlet_id = x.outlet_id\n", "where cart_checkout_ts_ist > current_date- interval '60' day\n", "and order_current_status = 'DELIVERED' \n", "and order_create_dt_ist > current_date- interval '60' day\n", "group by 1,2,3,4)\n", "\n", "select facility_id,l2, product_type, item_id from base b inner join fo on fo.city_id = b.city_id where item_ptype_rank <=3\n", "\"\"\"\n", "city_ptype_rank = pd.read_sql_query(sql=city_ptype_rank, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "61e52ca4-1183-49db-9be8-ac0950beda06", "metadata": {}, "outputs": [], "source": ["city_ptype_rank.head()"]}, {"cell_type": "code", "execution_count": null, "id": "030891d8-898f-4d34-8122-c4cec3ddce34", "metadata": {}, "outputs": [], "source": ["not_to_be_removed = not_to_be_removed.merge(\n", "    city_ptype_rank, on=[\"l2\", \"product_type\", \"facility_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f173a452-0a5d-44ac-ae9e-183e2f739a31", "metadata": {}, "outputs": [], "source": ["not_to_be_removed"]}, {"cell_type": "code", "execution_count": null, "id": "a3adc03f-d3fe-4adf-a632-01296c755cc9", "metadata": {}, "outputs": [], "source": ["not_to_be_removed = not_to_be_removed[[\"facility_id\", \"item_id\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "c421d40d-18c4-443e-86d0-8fb968f486ec", "metadata": {}, "outputs": [], "source": ["not_to_be_removed[\"flag\"] = 1"]}, {"cell_type": "code", "execution_count": null, "id": "c3326926-3396-45a3-8d7b-eb42fe096e16", "metadata": {}, "outputs": [], "source": ["first_cut = first_cut.merge(not_to_be_removed, on=[\"facility_id\", \"item_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "b03bd49d-cff8-48d6-a975-81cadb097a38", "metadata": {}, "outputs": [], "source": ["first_cut"]}, {"cell_type": "code", "execution_count": null, "id": "2b194afa-f389-47c4-bf77-e7f28acf3148", "metadata": {}, "outputs": [], "source": ["first_cut_final = first_cut[first_cut[\"flag\"].isna()]\n", "first_cut_final = first_cut_final.merge(item_factor, how=\"left\", on=\"item_id\")\n", "first_cut_final = first_cut_final.merge(item_inventory, how=\"left\", on=[\"item_id\", \"outlet_id\"])\n", "first_cut_final[\"tot_item_space\"] = first_cut_final[\"inventory\"] * first_cut_final[\"item_factor\"]\n", "first_cut_final = first_cut_final[first_cut_final[\"category\"] == \"GROCERY\"]"]}, {"cell_type": "code", "execution_count": null, "id": "58678211-e557-41d7-84e0-89d714dff07c", "metadata": {}, "outputs": [], "source": ["first_cut_final"]}, {"cell_type": "markdown", "id": "80b4d600-866f-4d26-9aa7-bb7c16d288a9", "metadata": {}, "source": ["## get assortmemt cut type and description"]}, {"cell_type": "code", "execution_count": null, "id": "dc79720c-0b96-4c0c-8f18-b9bfaeafbd0a", "metadata": {}, "outputs": [], "source": ["first_cut_final_copy = first_cut_final.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "10bd020c-f4fd-49d4-8a79-7952e84ee8af", "metadata": {}, "outputs": [], "source": ["first_cut_final_copy[\"assortment_cut_type\"] = \"FIRST_CUT\"\n", "first_cut_final_copy[\n", "    \"Description\"\n", "] = \"No sale in last 45 days, item available for >30 days, no special storage assigned, no active festive item, ptype width maintained with min 3 articles post removal\""]}, {"cell_type": "code", "execution_count": null, "id": "43677269-9e49-45dd-b20c-b6934168941d", "metadata": {}, "outputs": [], "source": ["first_cut_final_copy.head()"]}, {"cell_type": "markdown", "id": "80a1f13a-6b8e-47d7-9c58-8d7f0787a624", "metadata": {}, "source": ["## third cut"]}, {"cell_type": "code", "execution_count": null, "id": "deb3e2c4-34f9-49d8-a619-3358477820d9", "metadata": {}, "outputs": [], "source": ["sales_data_copy = daily_order_availability.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "1c6a4eee-e96c-4ceb-84ad-8cd345c72903", "metadata": {}, "outputs": [], "source": ["sales_data_copy[\"cpd\"] = (\n", "    sales_data_copy[\"quantity_ordered\"] * 1.00 / sales_data_copy[\"avail_day_count\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "55ad34d9-0f3f-44e5-a048-3014879a7283", "metadata": {}, "outputs": [], "source": ["sales_data_copy = sales_data_copy.merge(item_details, on=\"item_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "5c3a6dee-43b9-477e-901b-005a7d93ce82", "metadata": {}, "outputs": [], "source": ["def func_second_set(df):\n", "\n", "    ### Buckets on Price per gm\n", "    df1 = df.copy()\n", "\n", "    ## Divide into 3 buckets (B1,B2,B3) using 30th, 60th percentile values based on price per gram\n", "    q3_ppg = df1.groupby([\"l2\", \"product_type\"])[\"price_per_gm\"].quantile(0.3).reset_index()\n", "    q3_ppg.rename(columns={\"price_per_gm\": \"q3_price_per_gm\"}, inplace=True)\n", "\n", "    q6_ppg = df1.groupby([\"l2\", \"product_type\"])[\"price_per_gm\"].quantile(0.6).reset_index()\n", "    q6_ppg.rename(columns={\"price_per_gm\": \"q6_price_per_gm\"}, inplace=True)\n", "\n", "    q_ppg = q3_ppg.merge(q6_ppg, how=\"left\", on=[\"l2\", \"product_type\"])\n", "\n", "    df1 = df1.merge(q_ppg, how=\"left\", on=[\"l2\", \"product_type\"])\n", "    df1[\"bucket\"] = np.where(\n", "        df1[\"price_per_gm\"] <= df1[\"q3_price_per_gm\"],\n", "        \"B1\",\n", "        np.where(df1[\"price_per_gm\"] <= df1[\"q6_price_per_gm\"], \"B2\", \"B3\"),\n", "    )\n", "    sorted_df1 = df1.sort_values(\n", "        by=[\"facility_id\", \"l2\", \"product_type\", \"bucket\", \"cpd\"],\n", "        ascending=[True, True, True, True, False],\n", "    )\n", "\n", "    ptype_bucket_sum = (\n", "        sorted_df1.groupby([\"facility_id\", \"l2\", \"product_type\", \"bucket\"])[\"cpd\"]\n", "        .sum()\n", "        .reset_index()\n", "    )\n", "    ptype_bucket_sum.rename(columns={\"cpd\": \"tot_cpd\"}, inplace=True)\n", "\n", "    ## Top 80 percentile items from each l2-ptype combinations on price per gram\n", "    sorted_df1[\"cum_cpd\"] = sorted_df1.groupby([\"facility_id\", \"l2\", \"product_type\", \"bucket\"])[\n", "        \"cpd\"\n", "    ].cumsum()\n", "    sorted_df1 = sorted_df1.merge(\n", "        ptype_bucket_sum, how=\"left\", on=[\"facility_id\", \"l2\", \"product_type\", \"bucket\"]\n", "    )\n", "\n", "    sorted_df1[\"quantile_val\"] = sorted_df1[\"cum_cpd\"] / sorted_df1[\"tot_cpd\"]\n", "    fin_set_on_ppg = sorted_df1[sorted_df1[\"quantile_val\"] > 0.8]\n", "    fin_set_on_ppg = fin_set_on_ppg.copy()\n", "\n", "    fin_set_on_ppg.loc[:, \"source\"] = \"bottom 20 percent on ptype level price per gram\"\n", "\n", "    ### Buckets on gm\n", "    df2 = df.copy()\n", "\n", "    ## Divide into 3 buckets (B1,B2,B3) using 30th, 60th percentile values based on grams\n", "    q3_ppg = df2.groupby([\"l2\", \"product_type\"])[\"weight_in_gm\"].quantile(0.3).reset_index()\n", "    q3_ppg.rename(columns={\"weight_in_gm\": \"q3_weight_in_gm\"}, inplace=True)\n", "\n", "    q6_ppg = df2.groupby([\"l2\", \"product_type\"])[\"weight_in_gm\"].quantile(0.6).reset_index()\n", "    q6_ppg.rename(columns={\"weight_in_gm\": \"q6_weight_in_gm\"}, inplace=True)\n", "\n", "    q_ppg = q3_ppg.merge(q6_ppg, how=\"left\", on=[\"l2\", \"product_type\"])\n", "    df2 = df2.merge(q_ppg, how=\"left\", on=[\"l2\", \"product_type\"])\n", "    df2[\"bucket\"] = np.where(\n", "        df2[\"weight_in_gm\"] <= df2[\"q3_weight_in_gm\"],\n", "        \"B1\",\n", "        np.where(df2[\"weight_in_gm\"] <= df2[\"q6_weight_in_gm\"], \"B2\", \"B3\"),\n", "    )\n", "\n", "    ## Top 80 percentile items from each l2-ptype combinations on grams\n", "    sorted_df2 = df2.sort_values(\n", "        by=[\"facility_id\", \"l2\", \"product_type\", \"bucket\", \"cpd\"],\n", "        ascending=[True, True, True, True, False],\n", "    )\n", "    ptype_bucket_sum = (\n", "        sorted_df2.groupby([\"facility_id\", \"l2\", \"product_type\", \"bucket\"])[\"cpd\"]\n", "        .sum()\n", "        .reset_index()\n", "    )\n", "    ptype_bucket_sum.rename(columns={\"cpd\": \"tot_cpd\"}, inplace=True)\n", "\n", "    sorted_df2[\"cum_cpd\"] = sorted_df2.groupby([\"facility_id\", \"l2\", \"product_type\", \"bucket\"])[\n", "        \"cpd\"\n", "    ].cumsum()\n", "    sorted_df2 = sorted_df2.merge(\n", "        ptype_bucket_sum, how=\"left\", on=[\"facility_id\", \"l2\", \"product_type\", \"bucket\"]\n", "    )\n", "\n", "    sorted_df2[\"quantile_val\"] = sorted_df2[\"cum_cpd\"] / sorted_df2[\"tot_cpd\"]\n", "    fin_set_on_gm = sorted_df2[sorted_df2[\"quantile_val\"] > 0.8]\n", "    fin_set_on_gm = fin_set_on_gm.copy()\n", "\n", "    fin_set_on_gm.loc[:, \"source\"] = \"bottom 20 percent on ptype level grams\"\n", "\n", "    ### Buckets on price\n", "    #     df3 = df.copy()\n", "\n", "    #     ## Divide into 3 buckets (B1,B2,B3) using 30th, 60th percentile values based on price\n", "    #     q3_ppg = df3.groupby([\"l2\", \"product_type\"])[\"mrp\"].quantile(0.3).reset_index()\n", "    #     q3_ppg.rename(columns={\"mrp\": \"q3_asp\"}, inplace=True)\n", "\n", "    #     q6_ppg = df3.groupby([\"l2\", \"product_type\"])[\"mrp\"].quantile(0.6).reset_index()\n", "    #     q6_ppg.rename(columns={\"mrp\": \"q6_asp\"}, inplace=True)\n", "\n", "    #     q_ppg = q3_ppg.merge(q6_ppg, how=\"left\", on=[\"l2\", \"product_type\"])\n", "    #     df3 = df3.merge(q_ppg, how=\"left\", on=[\"l2\", \"product_type\"])\n", "    #     df3[\"bucket\"] = np.where(df3[\"mrp\"] <= df3[\"q3_asp\"], \"B1\",\n", "    #                                    np.where(df3[\"mrp\"] <= df3[\"q6_asp\"], \"B2\", \"B3\"))\n", "\n", "    #     ## Top 80 percentile items from each l2-ptype combinations on price\n", "    #     sorted_df3 = df3.sort_values(by=[\"facility_id\", \"l2\", \"product_type\", \"bucket\", \"cpd\"], ascending=[True, True, True, True, False])\n", "    #     ptype_bucket_sum = sorted_df3.groupby([\"facility_id\", \"l2\", \"product_type\", \"bucket\"])[\"cpd\"].sum().reset_index()\n", "    #     ptype_bucket_sum.rename(columns={\"cpd\": \"tot_cpd\"}, inplace=True)\n", "\n", "    #     sorted_df3[\"cum_cpd\"] = sorted_df3.groupby([\"facility_id\", \"l2\", \"product_type\", \"bucket\"])[\"cpd\"].cumsum()\n", "    #     sorted_df3 = sorted_df3.merge(ptype_bucket_sum, how=\"left\", on=[\"facility_id\", \"l2\", \"product_type\", \"bucket\"])\n", "    #     sorted_df3[\"quantile_val\"] = sorted_df3[\"cum_cpd\"]/sorted_df3[\"tot_cpd\"]\n", "\n", "    #     fin_set_on_price = sorted_df3[sorted_df3[\"quantile_val\"] > 0.8]\n", "    #     fin_set_on_price[\"source\"] = \"top 80 percentile on ptype level price\"\n", "\n", "    #\n", "\n", "    second_set = fin_set_on_ppg.append([fin_set_on_gm])\n", "\n", "    return second_set"]}, {"cell_type": "code", "execution_count": null, "id": "876a17b3-46a9-4750-8dc6-2d6b8b24fac2", "metadata": {}, "outputs": [], "source": ["sales_data_copy[\"cpd\"] = sales_data_copy[\"cpd\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "3ca8c563-4c07-4c67-b186-266b71c9460a", "metadata": {}, "outputs": [], "source": ["sales_data_copy.loc[:, \"price_per_gm\"] = np.round(\n", "    sales_data_copy[\"mrp\"] * 1.00 / sales_data_copy[\"weight_in_gm\"], 2\n", ")\n", "sales_data_copy.loc[:, \"mrp\"] = np.round(sales_data_copy[\"mrp\"], 0)"]}, {"cell_type": "code", "execution_count": null, "id": "a0850489-67ca-47d9-ba58-33a0ab5cebe6", "metadata": {}, "outputs": [], "source": ["second_set = func_second_set(sales_data_copy)"]}, {"cell_type": "code", "execution_count": null, "id": "080ea0c1-7403-4965-bbbc-051bcf5e4d3e", "metadata": {}, "outputs": [], "source": ["second_set_copy = second_set.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "8f27ceea-27f6-4549-88c9-e9d1bb0237bc", "metadata": {}, "outputs": [], "source": ["second_set = second_set.drop(\n", "    columns=[\n", "        \"price_per_gm\",\n", "        \"q3_price_per_gm\",\n", "        \"q6_price_per_gm\",\n", "        \"bucket\",\n", "        \"cum_cpd\",\n", "        \"tot_cpd\",\n", "        \"quantile_val\",\n", "        \"q3_weight_in_gm\",\n", "        \"q6_weight_in_gm\",\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8a75cdc2-1774-4d7c-9739-668c1e141299", "metadata": {}, "outputs": [], "source": ["second_set2 = second_set.groupby([\"facility_id\", \"item_id\"])[\"source\"].count().reset_index()\n", "second_set2 = second_set2.rename(columns={\"source\": \"count\"})"]}, {"cell_type": "code", "execution_count": null, "id": "6090f879-fcd1-4149-9a9d-a9137744e361", "metadata": {}, "outputs": [], "source": ["second_set2"]}, {"cell_type": "code", "execution_count": null, "id": "decc6c88-6f62-45b8-975a-0b1b4ba76c55", "metadata": {}, "outputs": [], "source": ["second_set2 = second_set2[second_set2[\"count\"] > 1]\n", "third_set = second_set.copy()\n", "third_set = third_set.merge(second_set2, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "third_set = third_set[third_set[\"count\"].notna()]\n", "third_set = third_set[third_set[\"cpd\"] < 0.14]\n", "third_set = third_set.drop(columns=[\"weight_in_gm\", \"mrp\", \"cpd\", \"source\", \"source\", \"count\"])\n", "third_set = third_set.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "c0b5bab6-ddaf-45fa-b96a-33aa3994814d", "metadata": {}, "outputs": [], "source": ["third_set"]}, {"cell_type": "code", "execution_count": null, "id": "b69c4b4a-aaaf-4288-9585-1057a0c96845", "metadata": {}, "outputs": [], "source": ["third_set = third_set.merge(festive_tag_items, on=\"item_id\", how=\"left\")\n", "third_set = third_set[third_set[\"active_festival\"].isna()]\n", "third_set = third_set.merge(item_tag_type, on=\"item_id\", how=\"left\")\n", "third_set = third_set.merge(\n", "    special_storage,\n", "    left_on=[\"facility_id\", \"cat\", \"item_storage_type\"],\n", "    right_on=[\"facility_id\", \"cat\", \"base_storage_type\"],\n", "    how=\"left\",\n", ")\n", "third_set = third_set[third_set[\"base_storage_type\"].isna()]\n", "third_set = third_set.drop(columns=[\"cat\", \"outlet_name\", \"base_storage_type\"])\n", "third_set.head()"]}, {"cell_type": "code", "execution_count": null, "id": "88da7943-b850-4721-9c51-ea304631db70", "metadata": {}, "outputs": [], "source": ["third_set"]}, {"cell_type": "code", "execution_count": null, "id": "99740f4f-1746-4f48-99ab-89cefe1983a1", "metadata": {}, "outputs": [], "source": ["# third_set_copy = second_set.copy()\n", "# third_set_copy = third_set_copy.merge(third_set,on = ['item_id','facility_id'], how = 'left')"]}, {"cell_type": "code", "execution_count": null, "id": "b73628b7-e7a9-4ee5-bd34-0d472323b142", "metadata": {}, "outputs": [], "source": ["third_cut_c_ptype = (\n", "    third_set.groupby([\"facility_id\", \"outlet_id\", \"l2\", \"product_type\"])\n", "    .agg({\"item_id\": \"count\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"removed_ptype_items\"})\n", ")\n", "active_ptype_count3 = active_ptype_count3.merge(\n", "    third_cut_c_ptype[[\"facility_id\", \"l2\", \"product_type\", \"removed_ptype_items\"]],\n", "    how=\"left\",\n", "    on=[\"facility_id\", \"l2\", \"product_type\"],\n", ")\n", "active_ptype_count3 = active_ptype_count3.fillna(0)\n", "active_ptype_count3"]}, {"cell_type": "code", "execution_count": null, "id": "f86970aa-0ffa-40a4-9621-af4d5da283e5", "metadata": {}, "outputs": [], "source": ["active_ptype_count3[\"final_ptype_count\"] = (\n", "    active_ptype_count3[\"active_ptype_items\"] - active_ptype_count3[\"removed_ptype_items\"]\n", ")\n", "not_to_be_removed2 = active_ptype_count3[\n", "    (active_ptype_count3[\"final_ptype_count\"] < 3)\n", "    & (active_ptype_count3[\"removed_ptype_items\"] > 0)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "b0bd7feb-bd1a-4f8b-a8b2-a62e467822d8", "metadata": {}, "outputs": [], "source": ["not_to_be_removed2"]}, {"cell_type": "code", "execution_count": null, "id": "14ca8047-9b10-432a-9fed-92785183e84a", "metadata": {}, "outputs": [], "source": ["not_to_be_removed2.sort_values(by=[\"removed_ptype_items\"], ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "b2d34cc0-c43a-4540-9f61-4f3083d4572e", "metadata": {}, "outputs": [], "source": ["not_to_be_removed2 = not_to_be_removed2.merge(\n", "    city_ptype_rank, on=[\"l2\", \"product_type\", \"facility_id\"], how=\"left\"\n", ")\n", "not_to_be_removed2 = not_to_be_removed2[[\"facility_id\", \"item_id\"]]\n", "not_to_be_removed2[\"flag\"] = 1"]}, {"cell_type": "code", "execution_count": null, "id": "85cbf799-b110-4a70-a8d9-2d259f467994", "metadata": {}, "outputs": [], "source": ["third_set = third_set.merge(not_to_be_removed2, on=[\"facility_id\", \"item_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "8c7be05c-e64a-4f4a-87e6-824a5914bdbd", "metadata": {}, "outputs": [], "source": ["third_set_final = third_set[third_set[\"flag\"].isna()]\n", "third_set_final = third_set_final.merge(item_factor, how=\"left\", on=\"item_id\")\n", "third_set_final = third_set_final.merge(item_inventory, how=\"left\", on=[\"item_id\", \"outlet_id\"])\n", "third_set_final[\"tot_item_space\"] = third_set_final[\"inventory\"] * third_set_final[\"item_factor\"]\n", "third_set_final = third_set_final[third_set_final[\"category\"] == \"GROCERY\"]"]}, {"cell_type": "code", "execution_count": null, "id": "6580cf76-a6b2-4bd4-957e-18b2d5e6d3f1", "metadata": {}, "outputs": [], "source": ["aggressive_cut = first_cut_final.append(third_set_final)"]}, {"cell_type": "code", "execution_count": null, "id": "d4cd4655-853f-47d1-a4e5-b9d70b2809a3", "metadata": {}, "outputs": [], "source": ["aggressive_cut = aggressive_cut.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "b55621ce-f1e9-43d7-a18b-4c2e13057929", "metadata": {}, "outputs": [], "source": ["aggressive_cut_copy = aggressive_cut.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "92473c80-ca3b-4515-b871-7d2f66b91b6e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d988def2-d1c2-4091-9d0f-eb95e3a38f16", "metadata": {}, "outputs": [], "source": ["aggressive_cut_copy[\"assortment_cut_type\"] = \"AGGRESSIVE_CUT\"\n", "aggressive_cut_copy[\n", "    \"Description\"\n", "] = \"bottom 20% sale contributing based price per gram and garmmage, item available for >30 days, no special storage assigned, no active festive item, ptype width maintained with min 3 articles post removal\""]}, {"cell_type": "code", "execution_count": null, "id": "3a17e617-288a-4a46-ab17-c4170db725d6", "metadata": {}, "outputs": [], "source": ["aggressive_cut_copy"]}, {"cell_type": "code", "execution_count": null, "id": "f51caee7-7a9f-48c5-80e7-e368d8393d8f", "metadata": {}, "outputs": [], "source": ["final_df = aggressive_cut_copy.append(first_cut_final_copy)"]}, {"cell_type": "code", "execution_count": null, "id": "d8c0d6ad-acb2-4420-840d-196d626a0251", "metadata": {}, "outputs": [], "source": ["final_df[\"insert_ds_ist\"] = pd.to_datetime(datetime.now() + timedelta(hours=5.5))"]}, {"cell_type": "code", "execution_count": null, "id": "20a411d8-754a-4fe7-a422-f3431b0e34db", "metadata": {}, "outputs": [], "source": ["final_df = final_df.drop(\n", "    columns=[\n", "        \"active_festival\",\n", "        \"flag\",\n", "        \"inventory\",\n", "        \"tot_item_space\",\n", "        \"mrp\",\n", "        \"weight_in_gm\",\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a29afccd-6278-4a36-898c-70c06d93f5df", "metadata": {}, "outputs": [], "source": ["final_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "d7e4a646-0acc-49ac-9d41-58a52dcdb8d1", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"item_facility_assortment_cut\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"Facility id\"},\n", "        {\n", "            \"name\": \"outlet_id\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"outlet id\",\n", "        },\n", "        {\n", "            \"name\": \"item_id\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Item id\",\n", "        },\n", "        {\"name\": \"avail_day_count\", \"type\": \"INTEGER\", \"description\": \"days available\"},\n", "        {\n", "            \"name\": \"quantity_ordered\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"ordered quantity in available days\",\n", "        },\n", "        {\n", "            \"name\": \"product_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"product type\",\n", "        },\n", "        {\"name\": \"name\", \"type\": \"varchar\", \"description\": \"item name\"},\n", "        {\n", "            \"name\": \"l0\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"l0\",\n", "        },\n", "        {\n", "            \"name\": \"l1\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"l1\",\n", "        },\n", "        {\"name\": \"l2\", \"type\": \"varchar\", \"description\": \"l2\"},\n", "        {\n", "            \"name\": \"category\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"category\",\n", "        },\n", "        {\n", "            \"name\": \"item_storage_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"storage type\",\n", "        },\n", "        {\n", "            \"name\": \"item_factor\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Item factor\",\n", "        },\n", "        {\"name\": \"assortment_cut_type\", \"type\": \"varchar\", \"description\": \"cut type\"},\n", "        {\n", "            \"name\": \"Description\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"description of cut\",\n", "        },\n", "        {\n", "            \"name\": \"insert_ds_ist\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"Time of updation\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"facility_id\", \"outlet_id\", \"item_id\", \"assortment_cut_type\"],\n", "    \"partition_key\": [\"insert_ds_ist\"],  # ** Note below points while selecting this\n", "    # \"incremental_key\": \"update_ts\",\n", "    \"load_type\": \"append\",  # append, truncate or upsert,\n", "    \"table_description\": \"assortment cut data\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "357c44d2-6224-4d17-9406-5db706d2f563", "metadata": {}, "outputs": [], "source": ["pb.to_trino(data_obj=final_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "d2f1ee65-18c6-467c-aa5a-a8a061c282bf", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}