{"cells": [{"cell_type": "code", "execution_count": null, "id": "3882d323-5002-4530-9c59-5b15b7d8ec16", "metadata": {}, "outputs": [], "source": ["!pip uninstall -y openpyxl"]}, {"cell_type": "code", "execution_count": null, "id": "8d46205a-2423-483e-a89f-fdcdc4c0e359", "metadata": {}, "outputs": [], "source": ["!pip install openpyxl==3.1.5"]}, {"cell_type": "code", "execution_count": null, "id": "d58c7755-fff7-4dc6-a85d-6388829bafc5", "metadata": {"tags": []}, "outputs": [], "source": ["# !pip uninstall -y openpyxl\n", "\n", "# !pip install openpyxl==3.1.5\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "import sys\n", "import json\n", "\n", "EXECUTION_MODE = \"PROD\"\n", "ENVIRON = \"supply_etls\"\n", "\n", "if EXECUTION_MODE == \"MAC\":\n", "    script_dir = \"/Users/<USER>/Downloads/dev/repl_product_repo/\"\n", "    EXECUTION_MODE = \"LOCAL\"\n", "    ENVIRON = \"\"\n", "else:\n", "    script_dir = \"/home/<USER>/inventory-planning-playbooks/\"\n", "    if EXECUTION_MODE == \"LOCAL\":\n", "        ENVIRON = \"playground\"\n", "\n", "slack_alert_channel = \"assortment-upload-loggers\"\n", "sheet_id = \"1XOziCTMahK-CVFuOPKlKWjHCuC7dTdaqewWs_mIysHs\"\n", "rules_config_sheet_name = \"rules\"\n", "rules_validation_config_sheet_range_index = 7\n", "configSheetName = \"config\"\n", "CHUNK = 20000\n", "source = \"povms_assortment_rationalisation_etl_rule_engine_dag\"\n", "logTable = \"assortment_bulk_upload_dag_log_v2\"\n", "ruleEngineLogTable = \"assortment_rule_engine_dag_log\"\n", "output_sheet_id = \"10C2_dd44OLihCx3XGD62glK8Yb_gx1N8nlCoSnDOgEk\"\n", "output_sheet_name = \"result\"\n", "num_rules_processed_at_a_time = 15\n", "\n", "\n", "def fetchDataFromDB(query, max_retries=3, retry_delay=10, raiseExceptionFlag=True):\n", "    import pencilbox as pb\n", "    import pandas as pd\n", "    import time\n", "\n", "    con = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            df = pd.read_sql_query(sql=query, con=con)\n", "            return df\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error fetching data (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                if raiseExceptionFlag:\n", "                    raise Exception(f\"Error fetching data after {max_retries} retries: {e}\")\n", "                else:\n", "                    return pd.DataFrame()\n", "\n", "\n", "def sendSlackAlert(text, slack_alert_channel=slack_alert_channel, execution_mode=EXECUTION_MODE):\n", "    if execution_mode == \"PROD\":\n", "        try:\n", "            import pencilbox as pb\n", "\n", "            pb.send_slack_message(channel=slack_alert_channel, text=text)\n", "        except Exception as e:\n", "            print(f\"Error sending Slack message: {e}, {text}\")\n", "        # handle the error further if needed\n", "    else:\n", "        print(\"local environment, just printing to console\")\n", "        print(text)\n", "\n", "\n", "def readFromSheetsWithRetry(\n", "    spreadSheetId, sheetRange, raiseExceptionFlag=True, mode=EXECUTION_MODE\n", "):\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            if mode == \"LOCAL\":\n", "                sys.path.insert(0, script_dir)\n", "                from utils.commonFunc import readSpreadsheet\n", "\n", "                values = readSpreadsheet(spreadSheetId, sheetRange)\n", "                max_length = len(values[0])\n", "                # Pad shorter rows with None/np.nan values to ensure consistent shape\n", "                padded_rows = []\n", "                for row in values[1:]:\n", "                    # If row is shorter than headers, extend it with None values\n", "                    if len(row) < max_length:\n", "                        padded_row = row + [None] * (max_length - len(row))\n", "                    else:\n", "                        padded_row = row\n", "                    padded_rows.append(padded_row)\n", "                df = pd.DataFrame(padded_rows, columns=values[0])\n", "                return df\n", "            else:\n", "                import pencilbox as pb\n", "\n", "                df = pb.from_sheets(spreadSheetId, sheetRange)\n", "                return df\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error fetching data (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to fetch data after {max_retries} attempts: {e}\")\n", "                if raiseExceptionFlag:\n", "                    raise Exception(\n", "                        \"Failed to pushToTrinoMoqLogs after {} attempts\".format(max_retries)\n", "                    ) from e\n", "                else:\n", "                    sendSlackAlert(\n", "                        \"assortment rule engine failed to read sheet\" + spreadSheetId + sheetRange\n", "                    )\n", "                    return pd.DataFrame()\n", "\n", "\n", "def pushD<PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "    df,\n", "    tableName,\n", "    description=None,\n", "    primaryKeys=[\"insert_ds_ist\"],\n", "    load_type=\"upsert\",\n", "    environ=\"playground\",\n", "    partition_key=[],\n", "):\n", "    from datetime import datetime, timezone, timedelta\n", "    import time\n", "    import pandas as pd\n", "\n", "    utc_now = datetime.now(timezone.utc)\n", "    ist_now = utc_now + <PERSON><PERSON><PERSON>(hours=5, minutes=30)\n", "    formatted_date = ist_now.strftime(\"%Y-%m-%d\")\n", "    df[\"insert_ds_ist\"] = formatted_date\n", "    df[\"updated_at\"] = pd.to_datetime(utc_now)\n", "\n", "    query = f\"describe {environ}.{tableName}\"\n", "    columnTypes = fetchDataFromDB(query, max_retries=0, raiseExceptionFlag=False)\n", "    print(\"columnTypes\", columnTypes.shape)\n", "    if columnTypes.shape[0] > 0:\n", "        columnTypes = columnTypes[[\"Column\", \"Type\"]]\n", "        current_dtypes = df.dtypes\n", "        for index, row in columnTypes.iterrows():\n", "            column = row[\"Column\"]\n", "            dtype = row[\"Type\"]\n", "            if column in df.columns:\n", "                if \"varchar\" in dtype or \"char\" in dtype:\n", "                    if current_dtypes[column] != \"object\":\n", "                        df[column] = df[column].astype(\n", "                            \"object\"\n", "                        )  # Pandas doesn't have a direct char type, using object instead\n", "                elif \"integer\" in dtype or \"bigint\" in dtype or \"int\" in dtype:\n", "                    if current_dtypes[column] not in [\"int64\", \"int32\"]:\n", "                        df[column] = pd.to_numeric(df[column], errors=\"coerce\")\n", "                elif \"double\" in dtype or \"float\" in dtype or \"real\" in dtype:\n", "                    if current_dtypes[column] not in [\"float64\", \"float32\"]:\n", "                        df[column] = pd.to_numeric(df[column], errors=\"coerce\")\n", "                elif \"timestamp\" in dtype:\n", "                    if current_dtypes[column] != \"datetime64[ns]\":\n", "                        df[column] = pd.to_datetime(df[column])\n", "\n", "    column_dtypes = [\n", "        {\n", "            \"name\": col,\n", "            \"type\": str(df[col].dtype)\n", "            .replace(\"int64\", \"integer\")\n", "            .replace(\"float64\", \"real\")\n", "            .replace(\"str\", \"varchar\")\n", "            .replace(\"datetime64[ns]\", \"timestamp(6)\")\n", "            .replace(\"object\", \"varchar\")\n", "            .replace(\"bool\", \"boolean\")\n", "            .replace(\"datetime64[ns, UTC]\", \"timestamp(6)\"),\n", "            \"description\": col,\n", "        }\n", "        for col in df.columns\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": environ,\n", "        \"table_name\": tableN<PERSON>,\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": primary<PERSON><PERSON><PERSON>,\n", "        \"load_type\": load_type,\n", "        \"table_description\": (description if description else tableName),\n", "    }\n", "    print(\"kwargs\", kwargs)\n", "    if len(partition_key) > 0:\n", "        kwargs[\"partition_key\"] = partition_key\n", "\n", "    import pencilbox as pb\n", "\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            pb.to_trino(data_obj=df, **kwargs)\n", "            return\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error pushDfToTrino (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to pushDfToTrino after {max_retries} attempts: {e}\")\n", "                return\n", "\n", "\n", "def pushToGoogleSheetsWithRetry(df, sheet_id, sheet_name, mode=EXECUTION_MODE):\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "    import time\n", "    import os\n", "    import sys\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            if mode == \"LOCAL\":\n", "                sys.path.insert(0, script_dir)\n", "                from utils.commonFunc import (\n", "                    pushDataFrameToFileUsingSpreadSheetId,\n", "                    prepFileForGoogleSheets,\n", "                )\n", "\n", "                pushDataFrameToFileUsingSpreadSheetId(\n", "                    prepFileForGoogleSheets(df), sheet_id, sheet_name\n", "                )\n", "                return\n", "            else:\n", "                import pencilbox as pb\n", "\n", "                pb.to_sheets(df, sheet_id, sheet_name)\n", "                return\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error pushing data (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to push data after {max_retries} attempts: {e}\")\n", "                return\n", "\n", "\n", "rules_sheet_df = readFromSheetsWithRetry(sheet_id, rules_config_sheet_name)\n", "rules_validation_config_df = rules_sheet_df.iloc[\n", "    0 : (rules_validation_config_sheet_range_index - 1)\n", "]\n", "print(\"rules_validation_config_df\", rules_validation_config_df.shape)\n", "rules_config_df = rules_sheet_df.iloc[(rules_validation_config_sheet_range_index - 2) :]\n", "rules_config_df.reset_index(drop=True, inplace=True)\n", "rules_config_df = pd.DataFrame(rules_config_df.values[1:], columns=rules_config_df.iloc[0])\n", "\n", "print(\"rules_config_df\", rules_config_df.shape)\n", "\n", "if rules_validation_config_df.shape[0] == 0:\n", "    text = \"something went wrong, could not read the validation config\"\n", "    print(text)\n", "    sendSlackAlert(text, slack_alert_channel)\n", "    sys.exit()\n", "\n", "if rules_config_df.shape[0] == 0:\n", "    print(\"nothing to process\")\n", "    sys.exit()\n", "\n", "# validate rules\n", "invalid_rules = []\n", "rules_validation_config_df = rules_validation_config_df.set_index(\"remark\").T\n", "rules_validation_config_df.rename(columns={\"rule_id\": \"param\"}, inplace=True)\n", "\n", "\n", "def validate_rules_config(rules_config_df, validation_config_df):\n", "    \"\"\"\n", "    Validate each row of rules_config_df using the rules defined in validation_config_df\n", "\n", "    Parameters:\n", "    -----------\n", "    rules_config_df : pandas.DataFrame\n", "        DataFrame containing rule configurations\n", "    validation_config_df : pandas.DataFrame\n", "        DataFrame containing validation rules\n", "\n", "    Returns:\n", "    --------\n", "    list\n", "        List of validation errors with row and column information\n", "    \"\"\"\n", "    validation_errors = []\n", "\n", "    # Create a dictionary for faster validation rule lookup\n", "    validation_dict = {}\n", "    for _, row in validation_config_df.iterrows():\n", "        param = row[\"param\"]\n", "        validation_dict[param] = {\n", "            \"data_type\": row[\"data_type\"],\n", "            \"allowed_values\": row[\"allowed_values\"],\n", "            \"mandatory\": row[\"mandatory\"].lower() == \"yes\" if row[\"mandatory\"] else False,\n", "            \"requires_operator\": row[\"requires_operator\"].lower() == \"yes\"\n", "            if row[\"requires_operator\"]\n", "            else False,\n", "        }\n", "\n", "    # Iterate through each row in rules_config_df\n", "    for row_idx, row in rules_config_df.iterrows():\n", "        for column, value in row.items():\n", "            # Skip columns not in validation_dict\n", "            if column not in validation_dict:\n", "                continue\n", "\n", "            validation_rule = validation_dict[column]\n", "\n", "            # Check if the field is mandatory\n", "            if validation_rule[\"mandatory\"] and (pd.isna(value) or value == \"\"):\n", "                validation_errors.append(\n", "                    {\n", "                        \"row\": row_idx,\n", "                        \"column\": column,\n", "                        \"error\": f\"Mandatory field '{column}' is empty\",\n", "                    }\n", "                )\n", "                continue\n", "\n", "            # Skip validation for empty non-mandatory fields\n", "            if pd.isna(value) or value == \"\":\n", "                continue\n", "\n", "            # Validate based on data type\n", "            data_type = validation_rule[\"data_type\"]\n", "\n", "            if data_type == \"string\":\n", "                # No specific validation for strings\n", "                pass\n", "\n", "            elif data_type == \"integer\":\n", "                try:\n", "                    value = int(value)\n", "                except ValueError:\n", "                    validation_errors.append(\n", "                        {\n", "                            \"row\": row_idx,\n", "                            \"column\": column,\n", "                            \"error\": f\"'{column}' value '{value}' is not a valid integer\",\n", "                        }\n", "                    )\n", "\n", "            elif data_type == \"float\":\n", "                try:\n", "                    value = float(value)\n", "                except ValueError:\n", "                    validation_errors.append(\n", "                        {\n", "                            \"row\": row_idx,\n", "                            \"column\": column,\n", "                            \"error\": f\"'{column}' value '{value}' is not a valid float\",\n", "                        }\n", "                    )\n", "\n", "            elif data_type == \"boolean\":\n", "                value = value.upper()\n", "                if value.lower() not in [\"true\", \"false\"]:\n", "                    validation_errors.append(\n", "                        {\n", "                            \"row\": row_idx,\n", "                            \"column\": column,\n", "                            \"error\": f\"'{column}' value '{value}' is not a valid boolean\",\n", "                        }\n", "                    )\n", "\n", "            elif data_type == \"operator\":\n", "                if value not in [\">=\", \">\", \"<=\", \"<\", \"=\", \"!=\"]:\n", "                    validation_errors.append(\n", "                        {\n", "                            \"row\": row_idx,\n", "                            \"column\": column,\n", "                            \"error\": f\"'{column}' value '{value}' is not a valid operator\",\n", "                        }\n", "                    )\n", "\n", "            # Check allowed values if specified\n", "            allowed_values = validation_rule[\"allowed_values\"]\n", "            if allowed_values and allowed_values != \"nan\":\n", "                # Handle different formats of allowed values\n", "                if \",\" in allowed_values:\n", "                    # Comma-separated list\n", "                    allowed_list = [v.strip() for v in allowed_values.split(\",\")]\n", "                    if str(value) not in allowed_list:\n", "                        validation_errors.append(\n", "                            {\n", "                                \"row\": row_idx,\n", "                                \"column\": column,\n", "                                \"error\": f\"'{column}' value '{value}' is not in allowed values: {allowed_values}\",\n", "                            }\n", "                        )\n", "\n", "            if validation_rule[\"requires_operator\"]:\n", "                if param + \"_operator\" not in row.columns or (\n", "                    param + \"_operator\" in row.columns and row[param + \"_operator\"] is None\n", "                ):\n", "                    validation_errors.append(\n", "                        {\n", "                            \"row\": row_idx,\n", "                            \"column\": column,\n", "                            \"error\": f\"'{column}'_operator value '{value}' is missing\",\n", "                        }\n", "                    )\n", "\n", "    return validation_errors\n", "\n", "\n", "validation_errors = validate_rules_config(rules_config_df, rules_validation_config_df)\n", "\n", "if validation_errors:\n", "    alert_string = f\"Found {len(validation_errors)} validation errors:\\n\"\n", "    print(alert_string)\n", "    alert_string = \"\"\n", "    failed_rows = set()\n", "    for error in validation_errors:\n", "        logString = f\"Row {error['row']}, Column '{error['column']}': {error['error']}\"\n", "        alert_string = alert_string + logString\n", "        print(logString)\n", "        failed_rows.add(error[\"row\"])\n", "\n", "    sendSlackAlert(alert_string, slack_alert_channel=slack_alert_channel)\n", "    failed_rows = list(failed_rows)\n", "else:\n", "    print(\"All validations passed!\")\n", "    failed_rows = []\n", "\n", "filtered_rules_config_df = rules_config_df[~rules_config_df.index.isin(failed_rows)]\n", "\n", "if filtered_rules_config_df.shape[0] == 0:\n", "    print(\"nothing to process\")\n", "    sys.exit()\n", "\n", "temp_df = filtered_rules_config_df.copy()\n", "pushDfToTrino(\n", "    temp_df,\n", "    ruleEngineLogTable,\n", "    \"assortment rule engine log table\",\n", "    primaryKeys=[],\n", "    load_type=\"append\",\n", "    environ=ENVIRON,\n", "    partition_key=[\"insert_ds_ist\"],\n", ")\n", "\n", "base_query = \"\"\"\n", "WITH city_cluster_mapping as (\n", "  SELECT\n", "    m.city_id,\n", "    c.cluster_id,\n", "    c.cluster_name\n", "  FROM\n", "    rpc.ams_city_cluster_mapping m\n", "    INNER JOIN rpc.ams_cluster c ON c.cluster_id = m.cluster_id\n", "    AND c.cluster_id > 15000\n", "    AND m.lake_active_record = TRUE\n", "    AND m.active = TRUE\n", "    AND c.lake_active_record = TRUE\n", "),\n", "fo AS (\n", "  SELECT\n", "    om.facility_id,\n", "    om.outlet_id AS outlet_id,\n", "    mo_map.frontend_merchant_id AS merchant_id,\n", "    om.outlet_name AS outlet_name,\n", "    rcl.id as outlet_city_id,\n", "    rcl.name AS city_name,\n", "    rcs.id as state_id,\n", "    rcs.name AS state,\n", "    ccm.cluster_id,\n", "    ccm.cluster_name,\n", "    case\n", "      when is_express >= 1\n", "      and is_longtail >= 1 then 'LONGTAIL'\n", "      when is_express >= 1\n", "      and is_longtail = 0 then 'EXPRESS'\n", "      when is_express = 0\n", "      and is_longtail >= 1 then 'STANDALONE_LONGTAIL'\n", "    end as outlet_type\n", "  FROM\n", "    po.physical_facility_outlet_mapping om\n", "    INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "    AND rco.business_type_id IN (7)\n", "    AND rco.company_type_id NOT IN (771, 767)\n", "    AND rco.lake_active_record\n", "    INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "    AND rcl.lake_active_record\n", "    INNER JOIN dwh.dim_merchant_outlet_facility_mapping mo_map ON mo_map.facility_id = om.facility_id\n", "    AND rco.business_type_id = 7\n", "    AND is_current\n", "    AND is_current_mapping_active\n", "    AND is_backend_merchant_active\n", "    AND is_frontend_merchant_active\n", "    INNER JOIN retail.console_state AS rcs ON rcs.id = rcl.state_id\n", "    AND rcs.lake_active_record\n", "    INNER JOIN (\n", "      SELECT\n", "        cast(merchant_id AS int) merchant_id,\n", "        sum(\n", "          case\n", "            when TYPE = 'STORE_POLYGON' then 1\n", "            else 0\n", "          end\n", "        ) as is_express,\n", "        sum(\n", "          case\n", "            when TYPE = 'LONGTAIL_POLYGON' then 1\n", "            else 0\n", "          end\n", "        ) as is_longtail\n", "      FROM\n", "        serviceability.ser_store_polygons\n", "      WHERE\n", "        TYPE in ('STORE_POLYGON', 'LONGTAIL_POLYGON')\n", "        AND is_active = TRUE\n", "        AND lake_active_record\n", "      group by\n", "        1\n", "    ) poly ON poly.merchant_id = mo_map.frontend_merchant_id\n", "    inner join city_cluster_mapping ccm on ccm.city_id = rcl.id\n", "  WHERE\n", "    om.active = 1\n", "    AND om.ars_active = 1\n", "    AND om.lake_active_record\n", "    AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "    AND om.outlet_name NOT LIKE '%%Draft%%'\n", "    AND om.outlet_name NOT LIKE '%%Test Store%%'\n", "    AND om.outlet_name NOT LIKE '%%Dummy%%'\n", "    AND om.facility_id != 273\n", "    AND om.outlet_id IN (\n", "      SELECT\n", "        DISTINCT outlet_id\n", "      FROM\n", "        po.bulk_facility_outlet_mapping\n", "      WHERE\n", "        active\n", "        AND lake_active_record\n", "    )\n", "),\n", "halal_items as (\n", "  SELECT\n", "    DISTINCT ipo.item_id,\n", "    1 as halal_flag\n", "  FROM\n", "    cms.gr_product_attribute_mapping pam\n", "    JOIN cms.gr_product p ON pam.product_id = p.id\n", "    INNER JOIN dwh.dim_item_product_offer_mapping ipo ON ipo.product_id = pam.product_id\n", "    AND ipo.is_current\n", "  WHERE\n", "    pam.attribute_id = 8433\n", "    AND pam.lake_active_record = TRUE\n", "    AND p.enabled_flag = TRUE\n", "    AND p.lake_active_record = TRUE\n", "    AND pam.value = 'Yes'\n", "),\n", "catalog_ as (\n", "  SELECT\n", "    *\n", "  FROM\n", "    (\n", "      SELECT\n", "        icd.item_id,\n", "        icd.name,\n", "        icd.l0_id,\n", "        icd.l0,\n", "        icd.l1_id,\n", "        icd.l1,\n", "        icd.l2_id,\n", "        icd.l2,\n", "        icd.product_type_id,\n", "        icd.product_type,\n", "        CASE\n", "          WHEN id.perishable = 1 THEN 'PERISHABLE'\n", "          ELSE 'PACKAGED'\n", "        END AS item_type,\n", "        id.storage_type AS storage_type_raw,\n", "        CASE\n", "          WHEN id.storage_type IN (\n", "            '1',\n", "            '8',\n", "            '11'\n", "          ) THEN 'REGULAR'\n", "          WHEN id.storage_type IN ('4', '5') THEN 'HEAVY'\n", "          WHEN id.storage_type IN ('2', '6') THEN 'COLD'\n", "          WHEN id.storage_type IN ('3', '7') THEN 'FROZEN'\n", "          ELSE 'REGULAR'\n", "        END AS storage_type,\n", "        CASE\n", "          WHEN id.handling_type IN ('8') THEN 'PACKAGING MATERIAL'\n", "          WHEN id.handling_type IN ('6') THEN 'MEDICINAL'\n", "          ELSE 'REGULAR'\n", "        END AS handling_type,\n", "        id.variant_mrp AS mrp,\n", "        id.variant_description,\n", "        id.weight_in_gm,\n", "        id.length_in_cm,\n", "        id.height_in_cm,\n", "        id.breadth_in_cm,\n", "        id.shelf_life,\n", "        coalesce(itf.item_factor, 0.01) AS item_factor,\n", "        DATE_DIFF('day', date(icd.created_at), CURRENT_DATE) AS item_catalog_age,\n", "        rank() OVER (\n", "          PARTITION BY id.item_id\n", "          ORDER BY\n", "            id.id DESC\n", "        ) AS variant_rank,\n", "        itm.tag_value AS custom_storage_type_raw,\n", "        case\n", "          when itm.tag_value = '1' then 'BEAUTY'\n", "          when itm.tag_value = '2' then 'BOUQUET'\n", "          when itm.tag_value = '3' then 'PREMIUM'\n", "          when itm.tag_value = '4' then 'BOOKS'\n", "          when itm.tag_value = '5' then 'NON_VEG'\n", "          when itm.tag_value = '6' then 'ICE_CREAM'\n", "          when itm.tag_value = '7' then 'TOYS'\n", "          when itm.tag_value = '8' then 'ELECTRONICS' -- when itm.tag_value = '9' then 'FESTIVE'\n", "          when itm.tag_value = '10' then 'VERTICAL_CHUTES'\n", "          when itm.tag_value = '11' then 'BEST_SERVED_COLD'\n", "          when itm.tag_value = '12' then 'CRITICAL_SKUS'\n", "          when itm.tag_value = '13' then 'LARGE'\n", "          when itm.tag_value = '14' then 'APPAREL'\n", "          when itm.tag_value = '15' then 'SPORTS'\n", "          when itm.tag_value = '16' then 'PET_CARE'\n", "          when itm.tag_value = '17' then 'HOME_DECOR'\n", "          when itm.tag_value = '18' then 'KITCHEN_DINING'\n", "          when itm.tag_value = '19' then 'HOME_FURNISHING'\n", "          when itm.tag_value = '20' then 'LONGTAIL_OTHERS'\n", "          when itm.tag_value = '21' then 'PHARMA'\n", "          when itm.tag_value = '22' then 'PAAS'\n", "          when itm.tag_value = '23' then 'PALLET'\n", "          when itm.tag_value = '24' then 'LARGE_FRAGILE'\n", "          WHEN itm.tag_value IS NULL THEN 'NO_CONFIG'\n", "          ELSE 'UNKNOWN_CONFIG'\n", "        END AS custom_storage_type,\n", "        pb.manufacturer_id,\n", "        id.manufacturer AS manufacturer_name,\n", "        id.brand_id,\n", "        pb.name AS brand_name,\n", "        coalesce(id.outer_case_size, 1) AS outer_case_size,\n", "        coalesce(id.inner_case_size, 1) AS inner_case_size,\n", "        CASE\n", "          WHEN itm2.tag_value = '1' THEN TRUE\n", "          ELSE FALSE\n", "        END AS is_high_value,\n", "        itm2.tag_value AS high_value_tag_raw,\n", "        CASE\n", "          WHEN itm3.tag_value = '1' THEN 'upc_scan'\n", "          WHEN itm3.tag_value = '2' THEN 'serial_scan'\n", "          WHEN itm3.tag_value = '3' THEN 'qr_scan'\n", "          WHEN itm3.tag_value = '4' THEN 'no_scan'\n", "          WHEN itm3.tag_value is null then null\n", "          ELSE 'unknown'\n", "        END AS scan_type,\n", "        itm3.tag_value AS scan_type_raw,\n", "        cms_food_type,\n", "        coalesce(hi.halal_flag, 0) as halal_flag\n", "      FROM\n", "        rpc.item_category_details icd\n", "        INNER JOIN rpc.product_product id ON id.item_id = icd.item_id\n", "        AND id.active = 1\n", "        AND id.approved = 1\n", "        AND id.lake_active_record\n", "        LEFT JOIN supply_etls.item_factor itf ON itf.item_id = icd.item_id\n", "        LEFT JOIN rpc.item_tag_mapping itm ON itm.tag_type_id = 7\n", "        AND itm.active = TRUE\n", "        AND itm.lake_active_record\n", "        AND itm.item_id = icd.item_id\n", "        LEFT JOIN rpc.product_brand pb ON id.brand_id = pb.id\n", "        AND pb.lake_active_record\n", "        AND pb.active = 1\n", "        LEFT JOIN rpc.item_tag_mapping itm2 ON itm2.item_id = icd.item_id\n", "        AND itm2.active\n", "        AND itm2.tag_type_id = 3\n", "        AND itm2.lake_active_record\n", "        LEFT JOIN rpc.item_tag_mapping itm3 ON itm3.item_id = icd.item_id\n", "        AND itm3.active\n", "        AND itm3.tag_type_id = 5\n", "        AND itm3.lake_active_record\n", "        left join halal_items hi on hi.item_id = icd.item_id\n", "      WHERE\n", "        icd.lake_active_record\n", "        AND perishable != 1\n", "        AND id.handling_type != '8'\n", "        AND id.storage_type NOT IN ('3', '7') -- frozen\n", "        AND icd.l0_id != 1487 -- removing vegetables and fruits\n", "        AND icd.l0 NOT IN (\n", "          'wholesale store',\n", "          'Trial new tree',\n", "          'Specials'\n", "        ) -- removing test and flyer/freebie l0s\n", "    ) AS x\n", "  WHERE\n", "    variant_rank = 1\n", "),\n", "store_storage_mapping as (\n", "  SELECT\n", "    facility_id,\n", "    storage_type,\n", "    base_storage_type,\n", "    storage_capacity,\n", "    threshold,\n", "    (\n", "      case\n", "        when storage_capacity > 0\n", "        and threshold > 0 then 1\n", "        else 0\n", "      end\n", "    ) as storage_available_flag\n", "  FROM\n", "    ars.physical_facility_storage_capacity pfsc\n", "  WHERE\n", "    active\n", "    AND lake_active_record\n", "),\n", "cpd_base as (\n", "  select\n", "    fo.facility_id,\n", "    c.item_id,\n", "    c.aps_adjusted as aps_cpd,\n", "    rank() over (\n", "      partition by fo.outlet_id\n", "      order by\n", "        c.updated_at desc\n", "    ) as rank_\n", "  from\n", "    ars.outlet_item_aps_derived_cpd c\n", "    inner join fo on fo.outlet_id = c.outlet_id\n", "  where\n", "    insert_ds_ist >= cast(current_date - interval '1' day as varchar)\n", "),\n", "tea as (\n", "  select\n", "    facility_id,\n", "    item_id,\n", "    coalesce(try_cast(iotm.tag_value as integer), 0) as be_outlet_id\n", "  from\n", "    rpc.item_outlet_tag_mapping iotm\n", "    INNER JOIN fo on fo.outlet_id = iotm.outlet_id\n", "  where\n", "    iotm.tag_type_id = 8\n", "    AND iotm.active = 1\n", "    AND iotm.lake_active_record\n", ")\n", "select\n", "  fo.*,\n", "  c.*,\n", "  pfma.master_assortment_substate_id,\n", "  pfma.assortment_type,\n", "  coalesce(ssm.storage_available_flag, 0) as dedicated_space_available,\n", "  ssm.storage_type as store_storage_type,\n", "  ssm.base_storage_type,\n", "  ssm.storage_capacity,\n", "  ssm.threshold,\n", "  coalesce(cb.aps_cpd, 0) as aps_cpd,\n", "  tea.be_outlet_id\n", "from\n", "  rpc.product_facility_master_assortment pfma\n", "  inner join fo on fo.facility_id = pfma.facility_id\n", "  inner join catalog_ c on c.item_id = pfma.item_id\n", "  INNER JOIN tea on tea.facility_id = fo.facility_id\n", "  AND tea.item_id = c.item_id\n", "  left join store_storage_mapping ssm on ssm.facility_id = fo.facility_id\n", "  and ssm.storage_type = concat(c.custom_storage_type, '_', c.storage_type)\n", "  left join cpd_base cb on cb.item_id = pfma.item_id\n", "  and cb.facility_id = pfma.facility_id\n", "  and cb.rank_ = 1\n", "where\n", "  pfma.active = 1\n", "  and pfma.lake_active_record\n", "  and pfma.master_assortment_substate_id in (1, 3) and \n", "\n", "\"\"\"\n", "filtered_rules_config_df.sort_values(by=\"rule_engine_reason_code\", inplace=True)\n", "query_clauses_chunked = []\n", "query_clauses = []\n", "\n", "# Group rules by rule_engine_reason_code and item_query_clause\n", "rule_groups = {}\n", "\n", "# First pass: build item and outlet clauses for each row\n", "rule_data = []\n", "for _, row in filtered_rules_config_df.iterrows():\n", "    item_query_clause = \"\"\n", "    outlet_query_clause = \"\"\n", "    for col in filtered_rules_config_df.columns:\n", "        # non null value\n", "        if row[col] is not None and row[col] != \"\":\n", "            # no implication columns\n", "            if col in [\"rule_id\", \"rule_engine_reason_code\", \"check_type\"]:\n", "                continue\n", "\n", "            # special handling for dedicated_space_available col\n", "            if col == \"dedicated_space_available\":\n", "                outlet_query_clause = (\n", "                    outlet_query_clause + \" coalesce(ssm.storage_available_flag, 0) = 1 and \"\n", "                )\n", "                continue\n", "\n", "            # if operator column, then ignore\n", "            data_type = rules_validation_config_df[\n", "                rules_validation_config_df[\"param\"] == col\n", "            ].data_type.values[0]\n", "            if data_type == \"operator\":\n", "                continue\n", "\n", "            operator_required = rules_validation_config_df[\n", "                rules_validation_config_df[\"param\"] == col\n", "            ].requires_operator.values[0]\n", "            attribute_type = rules_validation_config_df[\n", "                rules_validation_config_df[\"param\"] == col\n", "            ].attribute_type.values[0]\n", "\n", "            if operator_required and operator_required.lower() == \"yes\":\n", "                operator_col = col + \"_operator\"\n", "                operator_value = row[operator_col]\n", "                if attribute_type == \"item\":\n", "                    item_query_clause = (\n", "                        item_query_clause + col + \" \" + operator_value + \" \" + row[col] + \" and \"\n", "                    )\n", "                else:\n", "                    outlet_query_clause = (\n", "                        outlet_query_clause + col + \" \" + operator_value + \" \" + row[col] + \" and \"\n", "                    )\n", "            else:\n", "                if col == \"item_id\":  # special handling for item_id column\n", "                    item_query_clause = (\n", "                        item_query_clause + \"pfma.\" + col + \" = \" + row[col] + \" and \"\n", "                    )\n", "                elif data_type == \"string\":\n", "                    if attribute_type == \"item\":\n", "                        item_query_clause = item_query_clause + col + \" = '\" + row[col] + \"' and \"\n", "                    else:\n", "                        outlet_query_clause = (\n", "                            outlet_query_clause + col + \" = '\" + row[col] + \"' and \"\n", "                        )\n", "                else:\n", "                    if attribute_type == \"item\":\n", "                        item_query_clause = item_query_clause + col + \" = \" + row[col] + \" and \"\n", "                    else:\n", "                        outlet_query_clause = outlet_query_clause + col + \" = \" + row[col] + \" and \"\n", "\n", "    if len(outlet_query_clause) < 5 or len(item_query_clause) < 5:\n", "        alert = \"something went wrong, this should never happen, rule probably misconfigured, check logs\"\n", "        print(alert, row, outlet_query_clause, item_query_clause)\n", "        sendSlackAlert(alert, slack_alert_channel)\n", "        sys.exit()\n", "\n", "    outlet_query_clause = outlet_query_clause[:-5]  # removing the last and\n", "    item_query_clause = item_query_clause[:-5]  # removing the last and\n", "\n", "    # Store the rule data\n", "    rule_data.append(\n", "        {\n", "            \"rule_engine_reason_code\": row[\"rule_engine_reason_code\"],\n", "            \"check_type\": row[\"check_type\"],\n", "            \"item_query_clause\": item_query_clause,\n", "            \"outlet_query_clause\": outlet_query_clause,\n", "        }\n", "    )\n", "\n", "# Second pass: group by rule_engine_reason_code and item_query_clause\n", "for rule in rule_data:\n", "    key = (rule[\"rule_engine_reason_code\"], rule[\"item_query_clause\"], rule[\"check_type\"])\n", "    if key not in rule_groups:\n", "        rule_groups[key] = []\n", "    rule_groups[key].append(rule[\"outlet_query_clause\"])\n", "\n", "# Third pass: build final query clauses\n", "if filtered_rules_config_df.shape[0] > 0:\n", "    last_reason_code = filtered_rules_config_df.rule_engine_reason_code.values[0]\n", "else:\n", "    last_reason_code = \"\"\n", "\n", "for (reason_code, item_clause, check_type), outlet_clauses in rule_groups.items():\n", "    # Combine outlet clauses with AND\n", "    combined_outlet_clause = \" or \".join([f\"({clause})\" for clause in outlet_clauses])\n", "\n", "    if check_type == \"1\":\n", "        query_clause = f\"{item_clause} and not ({combined_outlet_clause})\"\n", "    else:\n", "        query_clause = f\"{item_clause} and ({combined_outlet_clause})\"\n", "\n", "    if len(query_clauses) < num_rules_processed_at_a_time or reason_code == last_reason_code:\n", "        query_clauses.append(query_clause)\n", "        last_reason_code = reason_code\n", "    else:\n", "        query_clauses_chunked.append(query_clauses)\n", "        query_clauses = [query_clause]  # Start new chunk with current clause\n", "        last_reason_code = reason_code\n", "\n", "# Don't forget to add the last chunk if it has any clauses\n", "if query_clauses:\n", "    query_clauses_chunked.append(query_clauses)\n", "\n", "\n", "def fetchDataForQueryClauses(query_clauses_arr):\n", "    for index, clause in enumerate(query_clauses_arr):\n", "        if index == 0:\n", "            if len(query_clauses_arr) == 1:  # only one element in the arr\n", "                final_query = base_query + f\" ( ( {clause} ) )\"\n", "            else:\n", "                final_query = base_query + f\" ( ( {clause} ) \"\n", "        elif index == len(query_clauses_arr) - 1:\n", "            final_query = final_query + f\" or ( {clause} ) ) \"\n", "        else:\n", "            final_query = final_query + f\" or ( {clause} )\"\n", "\n", "    print(final_query)\n", "    return fetchDataFromDB(final_query)\n", "\n", "\n", "invalid_df = pd.DataFrame()\n", "for i in query_clauses_chunked:\n", "    df = fetchDataForQueryClauses(i)\n", "    invalid_df = pd.concat([invalid_df, df])\n", "\n", "if invalid_df.shape[0] == 0:\n", "    print(\"nothing to process\", invalid_df.shape)\n", "    sys.exit()\n", "\n", "\n", "def createOperatorMask(base_mask, col, row, df):\n", "    operator_col = col + \"_operator\"\n", "    operator_value = row[operator_col]\n", "    if operator_value == \"=\":\n", "        base_mask = base_mask & (df[col].astype(float) == float(row[col]))\n", "    elif operator_value == \"!=\":\n", "        base_mask = base_mask & (df[col].astype(float) != float(row[col]))\n", "    elif operator_value == \">\":\n", "        base_mask = base_mask & (df[col].astype(float) > float(row[col]))\n", "    elif operator_value == \"<\":\n", "        base_mask = base_mask & (df[col].astype(float) <= float(row[col]))\n", "    elif operator_value == \">=\":\n", "        base_mask = base_mask & (df[col].astype(float) >= float(row[col]))\n", "    elif operator_value == \"<=\":\n", "        base_mask = base_mask & (df[col].astype(float) >= float(row[col]))\n", "    else:\n", "        print(\"invalid operator\", col, operator_value, row)\n", "    return base_mask\n", "\n", "\n", "def attribute_reasons(df):\n", "    df.loc[:, \"reason_code\"] = None\n", "    for index, row in rules_config_df.iterrows():\n", "        item_mask = pd.Series(True, index=df.index)\n", "        outlet_mask = pd.Series(True, index=df.index)\n", "        lookUpCols = rules_validation_config_df[\n", "            rules_validation_config_df[\"attribute_type\"].isin([\"item\", \"outlet\"])\n", "        ].param.values\n", "        for col in rules_config_df.columns:\n", "            if row[col] is not None and row[col] != \"\" and col in lookUpCols:\n", "                attribute_type = rules_validation_config_df[\n", "                    rules_validation_config_df[\"param\"] == col\n", "                ].attribute_type.values[0]\n", "                operator_required = rules_validation_config_df[\n", "                    rules_validation_config_df[\"param\"] == col\n", "                ].requires_operator.values[0]\n", "                if operator_required and operator_required.lower() == \"yes\":\n", "                    if attribute_type == \"item\":\n", "                        item_mask = createOperatorMask(item_mask, col, row, df)\n", "                    else:\n", "                        outlet_mask = createOperatorMask(outlet_mask, col, row, df)\n", "                else:\n", "                    if attribute_type == \"item\":\n", "                        item_mask = item_mask & (df[col].astype(str) == str(row[col]))\n", "                    else:\n", "                        outlet_mask = outlet_mask & (df[col].astype(str) == str(row[col]))\n", "\n", "        if row[\"check_type\"] == \"0\":\n", "            mask = item_mask & outlet_mask\n", "            df.loc[mask, \"reason_code\"] = row[\"rule_engine_reason_code\"]\n", "        else:\n", "            mask = item_mask & ~(outlet_mask)\n", "            df.loc[mask, \"reason_code\"] = row[\"rule_engine_reason_code\"]\n", "\n", "    left_over_mask = pd.isna(df[\"reason_code\"])\n", "    df.loc[left_over_mask, \"reason_code\"] = \"could not assign reason\"\n", "    return df\n", "\n", "\n", "invalid_df = attribute_reasons(invalid_df)\n", "\n", "\n", "def createARSFile(df):\n", "    # item_id\tcity_name\tbackend_facility_id\tfrontend_facility_id\tmaster_assortment_substate\tassortment_type\tsubstate_reason_type\trequest_reason_type\n", "    ars_file_df = df[[\"item_id\", \"facility_id\", \"reason_code\"]]\n", "    ars_file_df.loc[:, \"city_name\"] = None\n", "    ars_file_df.loc[:, \"backend_facility_id\"] = None\n", "    ars_file_df.loc[:, \"master_assortment_substate\"] = \"Inactive\"\n", "    ars_file_df.loc[:, \"assortment_type\"] = \"EXPRESS_ALL\"\n", "    ars_file_df.loc[:, \"substate_reason_type\"] = \"BAU\"\n", "    ars_file_df.rename(\n", "        columns={\"facility_id\": \"frontend_facility_id\", \"reason_code\": \"request_reason_type\"},\n", "        inplace=True,\n", "    )\n", "    ars_file_df = ars_file_df[\n", "        [\n", "            \"item_id\",\n", "            \"city_name\",\n", "            \"backend_facility_id\",\n", "            \"frontend_facility_id\",\n", "            \"master_assortment_substate\",\n", "            \"assortment_type\",\n", "            \"substate_reason_type\",\n", "            \"request_reason_type\",\n", "        ]\n", "    ]\n", "    return ars_file_df\n", "\n", "\n", "def upload_to_bu(file_path, upload_type):\n", "    import requests\n", "\n", "    try:\n", "        url = \"https://retail-internal.grofer.io/bulk_upload/v1/upload_jobs\"\n", "        payload = {\n", "            \"file\": file_path,\n", "            \"created_by\": \"<PERSON><PERSON>\",\n", "            \"user_id\": 14,\n", "            \"is_auto_po\": True,\n", "            \"upload_type_id\": upload_type,\n", "            \"content_type\": \"text/csv\",\n", "            \"params\": {\"large_upload\": True},\n", "        }\n", "        response = requests.post(url, json=payload)\n", "        return response.status_code, response.json()\n", "    except Exception as e:\n", "        print(\"error in bulk upload api\", e)\n", "        return 404, {}\n", "\n", "\n", "def processFile(input_df, chunk_size=CHUNK, time_delay=10):\n", "    import pencilbox as pb\n", "    import boto3\n", "    import openpyxl\n", "\n", "    if chunk_size == 0:\n", "        print(\"nothing to process due to 0 chunk size, returning\")\n", "        return\n", "    elif input_df.shape[0] == 0:\n", "        print(\"nothing to process, returning\")\n", "        return\n", "    elif input_df.shape[0] < chunk_size:\n", "        list_df = [input_df]\n", "    else:\n", "        list_df = [input_df[i : i + chunk_size] for i in range(0, input_df.shape[0], chunk_size)]\n", "\n", "    responses = []\n", "    df_logs = pd.DataFrame()\n", "    for df in list_df:\n", "        uid = int(time.time())\n", "        local_file_path = f\"product_team_assortment_rule_engine_upload_bot_{uid}.xlsx\"\n", "        df.to_excel(local_file_path, index=False, engine=\"openpyxl\")\n", "        file_path = f\"assortment/{local_file_path}\"\n", "        secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "        bucket_name = \"retail-bulk-upload\"\n", "        aws_key = secrets.get(\"aws_key\")\n", "        aws_secret = secrets.get(\"aws_secret\")\n", "        session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "        s3 = session.resource(\"s3\")\n", "        bucket_obj = s3.Bucket(bucket_name)\n", "        bucket_obj.upload_file(local_file_path, file_path)\n", "        status_code, responseJson = upload_to_bu(file_path, 100)\n", "        os.remove(local_file_path)\n", "        df[\"uid\"] = uid\n", "        responses.append(responseJson)\n", "        df[\"response\"] = json.dumps(responseJson)\n", "        df[\"status_code\"] = status_code\n", "        df_logs = pd.concat([df_logs, df])\n", "        time.sleep(time_delay)\n", "    df_logs[\"source\"] = source\n", "    pushDfToTrino(\n", "        df_logs,\n", "        logTable,\n", "        description=\"manual upload via dag log table\",\n", "        load_type=\"append\",\n", "        environ=ENVIRON,\n", "        partition_key=[\"insert_ds_ist\"],\n", "    )\n", "    return responses\n", "\n", "\n", "try:\n", "    config_df = readFromSheetsWithRetry(\n", "        sheet_id, configSheetName, raiseExceptionFlag=False, mode=EXECUTION_MODE\n", "    )\n", "    if config_df.shape[0] > 0 and \"param\" in config_df.columns:\n", "        MAX_RECORDS_PER_RUN = int(\n", "            config_df[config_df[\"param\"] == \"MAX_RECORDS_PER_RUN\"][\"value\"].values[0]\n", "        )\n", "        MAX_RECORDS_PER_REQUEST = int(\n", "            config_df[config_df[\"param\"] == \"MAX_RECORDS_PER_REQUEST\"][\"value\"].values[0]\n", "        )\n", "        TIME_DELAY_PER_REQUEST = int(\n", "            config_df[config_df[\"param\"] == \"TIME_DELAY_PER_REQUEST\"][\"value\"].values[0]\n", "        )\n", "    else:\n", "        MAX_RECORDS_PER_RUN = 2000\n", "        MAX_RECORDS_PER_REQUEST = 2000\n", "        TIME_DELAY_PER_REQUEST = 60\n", "except Exception as e:\n", "    MAX_RECORDS_PER_RUN = 2000\n", "    MAX_RECORDS_PER_REQUEST = 2000\n", "    TIME_DELAY_PER_REQUEST = 60\n", "\n", "pushToGoogleSheetsWithRetry(invalid_df, output_sheet_id, \"raw\")\n", "\n", "could_not_assign_reason_df = invalid_df[invalid_df[\"reason_code\"] == \"could not assign reason\"]\n", "invalid_df = invalid_df[invalid_df[\"reason_code\"] != \"could not assign reason\"]\n", "\n", "input_df = createARSFile(invalid_df)\n", "input_df = input_df.drop_duplicates(subset=[\"frontend_facility_id\", \"item_id\"], keep=\"last\")\n", "\n", "total_records = input_df.shape[0]\n", "\n", "pushToGoogleSheetsWithRetry(input_df, output_sheet_id, output_sheet_name)\n", "\n", "if input_df.shape[0] > 0:\n", "    if input_df.shape[0] > MAX_RECORDS_PER_RUN:\n", "        print(\"truncating the file to max \", MAX_RECORDS_PER_RUN, input_df.shape[0])\n", "        input_df = input_df.head(MAX_RECORDS_PER_RUN)\n", "\n", "    responses = processFile(\n", "        input_df, chunk_size=MAX_RECORDS_PER_REQUEST, time_delay=TIME_DELAY_PER_REQUEST\n", "    )\n", "    if responses:\n", "        json_string = json.dumps(responses)\n", "        print(json_string)\n", "        sendSlackAlert(\"response of the rule engine dag \" + json_string)\n", "\n", "    cpd_impact = invalid_df.drop_duplicates(\n", "        subset=[\"facility_id\", \"item_id\"], keep=\"last\"\n", "    ).aps_cpd.sum()\n", "\n", "    text = f\"\"\"\n", "  Summary: \n", "  Total records in the dataframe were {total_records}\n", "  Records Processed: {input_df.shape[0]}\n", "  CPD Impact = {cpd_impact}\n", "  \"\"\"\n", "    sendSlack<PERSON><PERSON><PERSON>(text)\n", "\n", "    if could_not_assign_reason_df.shape[0] > 0:\n", "        sendSlackAlert(\n", "            f\"Assortment Rule Engine: Could not assign reason for some items {could_not_assign_reason_df.shape[0]}, <@U06RTRAMJQ4> to check and fix\"\n", "        )"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}