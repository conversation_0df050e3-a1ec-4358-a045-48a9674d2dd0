alert_configs:
  slack:
  - channel: assortment-alerts-plus-discussions
dag_name: assortment_rule_engine
dag_type: etl
escalation_priority: low
execution_timeout: 60
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U06RTRAMJQ4
path: povms/assortment_rationalisation/etl/assortment_rule_engine
paused: false
pool: povms_pool
project_name: assortment_rationalisation
schedule:
  end_date: '2025-07-18T00:00:00'
  interval: 30 10,21 * * *
  start_date: '2025-06-11T00:00:00'
schedule_type: fixed
sla: 60 minutes
support_files: []
tags: []
template_name: notebook
version: 18
