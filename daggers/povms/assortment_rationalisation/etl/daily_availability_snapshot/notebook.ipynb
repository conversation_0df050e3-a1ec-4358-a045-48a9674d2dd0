{"cells": [{"cell_type": "code", "execution_count": null, "id": "d12f72c9-ceef-473d-b80f-3a513c50893f", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import numpy as np\n", "import pandas as pd\n", "import math\n", "from datetime import date, timedelta\n", "import warnings\n", "import pencilbox as pb\n", "import time\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "51c72ad2-8ece-40b8-a0c3-f05d82a2116a", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"insert_ds_ist\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"city_name\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility_id\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"outlet_id\"},\n", "    {\"name\": \"merchant_id\", \"type\": \"integer\", \"description\": \"merchant_id\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "    {\n", "        \"name\": \"num_item_available_hours\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"num_item_available_hours\",\n", "    },\n", "    {\"name\": \"num_item_total_hours\", \"type\": \"integer\", \"description\": \"num_item_total_hours\"},\n", "    {\"name\": \"active\", \"type\": \"integer\", \"description\": \"active\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "351adf4e-221b-4525-94b1-4277d34e30c0", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"daily_availability_snapshot\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"insert_ds_ist\", \"city_name\", \"facility_id\", \"item_id\"],\n", "    \"partition_key\": [\"insert_ds_ist\", \"city_name\"],\n", "    # \"incremental_key\": \"updated_at_ist\",\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "    \"table_description\": \"daily_availability_snapshot\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "51141711-ee9f-4019-a2bb-be09d71f9a44", "metadata": {}, "outputs": [], "source": ["QUERY_TEMPLATE = \"\"\"\n", "select\n", "  distinct CURRENT_DATE - interval '1' day AS insert_ds_ist,\n", "  mo.frontend_merchant_city_name AS city_name,\n", "  mo.facility_id,\n", "  mo.pos_outlet_id as outlet_id,\n", "  mo.frontend_merchant_id AS merchant_id,\n", "  his.item_id,\n", "  count(\n", "    case\n", "      when current_inventory > 0 then date_ist\n", "    end\n", "  ) as num_item_available_hours,\n", "  count(date_ist) as num_item_total_hours,\n", "  1 as active\n", "from\n", "  dwh.dim_merchant_outlet_facility_mapping mo\n", "  INNER JOIN dwh.dim_outlet do ON do.outlet_id = mo.pos_outlet_id\n", "  AND do.is_current\n", "  AND do.is_outlet_active = 1\n", "  INNER JOIN po.physical_facility_outlet_mapping pfom ON pfom.facility_id = mo.facility_id\n", "  AND pfom.outlet_id = mo.pos_outlet_id\n", "  AND pfom.active = 1\n", "  AND pfom.lake_active_record\n", "  INNER JOIN supply_etls.hourly_inventory_snapshots his on his.outlet_id = pfom.outlet_id\n", "  and his.facility_id = pfom.facility_id\n", "  AND his.date_ist = current_date - interval '1' day\n", "WHERE\n", "  mo.is_pos_outlet_active = 1\n", "  AND mo.is_mapping_enabled\n", "  AND mo.is_current\n", "  AND mo.is_current_mapping_active\n", "  AND COALESCE(mo.is_bistro_outlet, FALSE) != TRUE\n", "group by\n", "  1,\n", "  2,\n", "  3,\n", "  4,\n", "  5,\n", "  6,\n", "  9\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "ddbb28a2-7fcb-46b4-928b-eb5a1476a381", "metadata": {}, "outputs": [], "source": ["import time\n", "import traceback\n", "\n", "!pip install tenacity\n", "\n", "from tenacity import retry, stop_after_attempt, wait_fixed\n", "\n", "\n", "@retry(stop=stop_after_attempt(3), wait=wait_fixed(60), reraise=True)\n", "def to_trino_with_retry(*args, **kwargs):\n", "    return pb.to_trino(*args, **kwargs)\n", "\n", "\n", "job_status = \"Pending\"\n", "\n", "try:\n", "    start = time.time()\n", "    to_trino_with_retry(QUERY_TEMPLATE, **kwargs)\n", "    end = time.time()\n", "    duration = end - start\n", "    if duration > 60:\n", "        print(f\"Data pushed in table in: {duration / 60:.2f} min\")\n", "    else:\n", "        print(f\"Data pushed in table in: {duration:.2f} s\")\n", "    job_status = \"Success\"\n", "\n", "except BaseException as e:\n", "    traceback.print_exc()\n", "    print(f\"Failed to push data to the table. Error: {e}\")\n", "    job_status = \"Failed\"\n", "\n", "# Display Job Status Summary\n", "print(\"\\n=== Job Execution Summary ===\")\n", "print(f\"Status: {job_status}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}