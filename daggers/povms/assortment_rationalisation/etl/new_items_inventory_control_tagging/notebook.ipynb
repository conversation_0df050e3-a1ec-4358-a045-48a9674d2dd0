{"cells": [{"cell_type": "code", "execution_count": null, "id": "4576b1c2-a06f-4179-8099-0b137ba833c1", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import numpy as np\n", "import pandas as pd\n", "import math\n", "from datetime import date, timedelta\n", "import warnings\n", "import pencilbox as pb\n", "import glob\n", "import boto3\n", "import time\n", "import requests\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "0833b2d0-1cad-4089-bf7b-ea516a46f3c2", "metadata": {}, "outputs": [], "source": ["HO = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "5b50210c-30ff-4852-9829-1f5c8c86164c", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_colwidth\", None)\n", "pd.set_option(\"display.max_columns\", None)\n", "pd.set_option(\"display.max_rows\", 1600)"]}, {"cell_type": "code", "execution_count": null, "id": "086f02f0-f185-4f25-a494-7cc7876f42c2", "metadata": {}, "outputs": [], "source": ["new_items_df = pd.read_sql_query(\n", "    \"\"\"\n", "select distinct l0, l1, product_type, item_id, name from rpc.item_category_details\n", "where created_at >= current_date - interval '1' day and lake_active_record\"\"\",\n", "    con=con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "73d695eb-6b78-477a-9f99-ed214f738c08", "metadata": {}, "outputs": [], "source": ["packaged_items_df = pd.read_sql_query(\n", "    \"\"\"\n", "SELECT *\n", "FROM\n", "  (SELECT icd.item_id,\n", "          icd.name,\n", "          icd.l0,\n", "          icd.l1,\n", "          icd.l2,\n", "          icd.product_type,\n", "          CASE\n", "              WHEN id.perishable = 1 THEN 'PERISHABLE'\n", "              ELSE 'PACKAGED'\n", "          END AS item_type,\n", "          id.storage_type AS storage_type_raw,\n", "          CASE\n", "              WHEN id.storage_type IN ('1',\n", "                                       '8',\n", "                                       '11') THEN 'REGULAR'\n", "              WHEN id.storage_type IN ('4',\n", "                                       '5') THEN 'HEAVY'\n", "              WHEN id.storage_type IN ('2',\n", "                                       '6') THEN 'COLD'\n", "              WHEN id.storage_type IN ('3',\n", "                                       '7') THEN 'FROZEN'\n", "              ELSE 'REGULAR'\n", "          END AS storage_type,\n", "          CASE\n", "              WHEN id.handling_type IN ('8') THEN 'PACKAGING MATERIAL'\n", "              WHEN id.handling_type IN ('6') THEN 'MEDICINAL'\n", "              ELSE 'REGULAR'\n", "          END AS handling_type,\n", "          id.variant_mrp AS mrp,\n", "          id.variant_description,\n", "          id.weight_in_gm,\n", "          id.length_in_cm,\n", "          id.height_in_cm,\n", "          id.breadth_in_cm,\n", "          id.shelf_life,\n", "          coalesce(itf.item_factor, 0.01) AS item_factor,\n", "          DATE_DIFF('day',date(icd.created_at), CURRENT_DATE) AS item_catalog_age,\n", "          rank() OVER (PARTITION BY id.item_id\n", "                       ORDER BY id.id DESC) AS variant_rank,\n", "                      itm.tag_value AS custom_storage_type_raw,\n", "                      CASE\n", "                          WHEN itm.tag_value = '1' THEN 'BEAUTY'\n", "                          WHEN itm.tag_value = '2' THEN 'BOUQUET'\n", "                          WHEN itm.tag_value = '3' THEN 'PREMIUM'\n", "                          WHEN itm.tag_value = '4' THEN 'BOOKS'\n", "                          WHEN itm.tag_value = '5' THEN 'NON_VEG'\n", "                          WHEN itm.tag_value = '6' THEN 'ICE_CREAM'\n", "                          WHEN itm.tag_value = '7' THEN 'TOYS'\n", "                          WHEN itm.tag_value = '8' THEN 'ELECTRONICS' -- when itm.tag_value = '9' then 'FESTIVE'\n", "\n", "                          WHEN itm.tag_value = '10' THEN 'VERTICAL_CHUTES'\n", "                          WHEN itm.tag_value = '11' THEN 'BEST_SERVED_COLD'\n", "                          WHEN itm.tag_value = '12' THEN 'CRITICAL_SKUS'\n", "                          WHEN itm.tag_value = '13' THEN 'LARGE'\n", "                          WHEN itm.tag_value = '14' THEN 'APPAREL'\n", "                          WHEN itm.tag_value = '15' THEN 'SPORTS'\n", "                          WHEN itm.tag_value = '16' THEN 'PET_CARE'\n", "                          WHEN itm.tag_value = '17' THEN 'HOME_DECOR'\n", "                          WHEN itm.tag_value = '18' THEN 'KITCHEN_DINING'\n", "                          WHEN itm.tag_value = '19' THEN 'HOME_FURNISHING'\n", "                          WHEN itm.tag_value = '20' THEN 'LONGTAIL_OTHERS'\n", "                          WHEN itm.tag_value IS NULL THEN 'NO_CONFIG'\n", "                          ELSE 'UNKNOWN_CONFIG'\n", "                      END AS custom_storage_type,\n", "                      pb.manufacturer_id,\n", "                      id.manufacturer AS manufacturer_name,\n", "                      pb.name AS brand_name,\n", "                      coalesce(id.outer_case_size,1) AS outer_case_size,\n", "                      coalesce(id.inner_case_size,1) AS inner_case_size,\n", "                      CASE\n", "                          WHEN itm2.tag_value = '1' THEN TRUE\n", "                          ELSE FALSE\n", "                      END AS is_high_value,\n", "                      itm2.tag_value AS high_value_tag_raw,\n", "                      CASE\n", "                          WHEN itm3.tag_value = '1' THEN 'upc scan'\n", "                          WHEN itm3.tag_value = '2' THEN 'serial scan'\n", "                          WHEN itm3.tag_value = '3' THEN 'qr scan'\n", "                          WHEN itm3.tag_value = '4' THEN 'no scan'\n", "                          ELSE 'unknown'\n", "                      END AS scan_type,\n", "                      itm3.tag_value AS scan_type_raw\n", "   FROM rpc.item_category_details icd\n", "   INNER JOIN rpc.product_product id ON id.item_id = icd.item_id\n", "   AND id.active = 1\n", "   AND id.approved = 1\n", "   AND id.lake_active_record\n", "   LEFT JOIN supply_etls.item_factor itf ON itf.item_id = icd.item_id\n", "   LEFT JOIN rpc.item_tag_mapping itm ON itm.tag_type_id = 7\n", "   AND itm.active = TRUE\n", "   AND itm.lake_active_record\n", "   AND itm.item_id = icd.item_id\n", "   LEFT JOIN rpc.product_brand pb ON id.brand_id = pb.id\n", "   AND pb.lake_active_record\n", "   AND pb.active = 1\n", "   LEFT JOIN rpc.item_tag_mapping itm2 ON itm2.item_id = icd.item_id\n", "   AND itm2.active\n", "   AND itm2.tag_type_id = 3\n", "   AND itm2.lake_active_record\n", "   LEFT JOIN rpc.item_tag_mapping itm3 ON itm3.item_id = icd.item_id\n", "   AND itm3.active\n", "   AND itm3.tag_type_id = 5\n", "   AND itm3.lake_active_record\n", "   WHERE icd.lake_active_record\n", "     AND perishable != 1\n", "     AND id.handling_type != '8'\n", "     AND id.storage_type NOT IN ('3',\n", "                                 '7')\n", "     AND icd.l0_id != 1487 -- removing vegetables and fruits\n", "\n", "     AND icd.l0 NOT IN ('wholesale store',\n", "                        'Trial new tree',\n", "                        'Specials',\n", "                        'Bistro')-- removing test and flyer/freebie l0s\n", " ) AS x\n", "WHERE variant_rank = 1\n", "\"\"\",\n", "    con=con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ba7ec608-b09e-4aa4-bcaa-40371f04c0fb", "metadata": {}, "outputs": [], "source": ["new_packaged_items_df = new_items_df.merge(packaged_items_df[[\"item_id\"]].drop_duplicates())"]}, {"cell_type": "code", "execution_count": null, "id": "6dfe6d65-dc46-4057-888b-81480ecdea91", "metadata": {}, "outputs": [], "source": ["city_item_expected_cpd_df = pd.read_sql_query(\n", "    \"\"\"select * from supply_etls.new_items_expected_cpd where insert_ds_ist is not null and city_name is not null\"\"\",\n", "    con=con,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "47c18830-6e66-4d1d-99fd-4e4b87345c94", "metadata": {}, "outputs": [], "source": ["item_expected_cpd_df = pd.concat(\n", "    [\n", "        city_item_expected_cpd_df.groupby([\"l0\", \"l1\", \"product_type\"], as_index=False).agg(\n", "            {\"expected_cpd\": \"median\"}\n", "        ),\n", "        city_item_expected_cpd_df[\n", "            city_item_expected_cpd_df[\"product_type\"].isna()\n", "            & ~city_item_expected_cpd_df[\"l1\"].isna()\n", "        ]\n", "        .groupby([\"l0\", \"l1\"], as_index=False)\n", "        .agg({\"expected_cpd\": \"median\"}),\n", "        city_item_expected_cpd_df[\n", "            city_item_expected_cpd_df[\"product_type\"].isna()\n", "            & city_item_expected_cpd_df[\"l1\"].isna()\n", "        ]\n", "        .groupby([\"l0\"], as_index=False)\n", "        .agg({\"expected_cpd\": \"median\"}),\n", "    ],\n", "    axis=0,\n", "    ignore_index=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d8c36985-62f8-45a9-ad76-4db0a21faacd", "metadata": {}, "outputs": [], "source": ["ptype_merged_df = new_packaged_items_df.merge(\n", "    item_expected_cpd_df[[\"l0\", \"l1\", \"product_type\", \"expected_cpd\"]].drop_duplicates(),\n", "    on=[\"l0\", \"l1\", \"product_type\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6c781115-9cf3-4d6c-b877-92939005cca4", "metadata": {}, "outputs": [], "source": ["l1_merged_df = (\n", "    new_packaged_items_df.merge(\n", "        ptype_merged_df[[\"item_id\"]].drop_duplicates().assign(a=1), how=\"left\"\n", "    )\n", "    .query(\"a!=1\")\n", "    .drop(columns=\"a\")\n", "    .merge(\n", "        item_expected_cpd_df[\n", "            item_expected_cpd_df[\"product_type\"].isna() & ~item_expected_cpd_df[\"l1\"].isna()\n", "        ][[\"l0\", \"l1\", \"expected_cpd\"]].drop_duplicates(),\n", "        on=[\"l0\", \"l1\"],\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "87579a1b-1bf9-44c7-ad49-ec906a44c833", "metadata": {}, "outputs": [], "source": ["l0_merged_df = (\n", "    new_packaged_items_df.merge(\n", "        ptype_merged_df[[\"item_id\"]].drop_duplicates().assign(a=1), how=\"left\"\n", "    )\n", "    .query(\"a!=1\")\n", "    .drop(columns=\"a\")\n", "    .merge(l1_merged_df[[\"item_id\"]].drop_duplicates().assign(a=1), how=\"left\")\n", "    .query(\"a!=1\")\n", "    .drop(columns=\"a\")\n", "    .merge(\n", "        item_expected_cpd_df[\n", "            item_expected_cpd_df[\"product_type\"].isna() & item_expected_cpd_df[\"l1\"].isna()\n", "        ][[\"l0\", \"expected_cpd\"]].drop_duplicates(),\n", "        on=[\"l0\"],\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "928ce258-60b0-4c6d-ba05-ec22f4fa8e9d", "metadata": {}, "outputs": [], "source": ["new_packaged_items_cpd_df = pd.concat(\n", "    [ptype_merged_df, l1_merged_df, l0_merged_df], axis=0, ignore_index=True\n", ").drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "f43ebcec-fd75-41fb-879d-440b81ed7ab2", "metadata": {}, "outputs": [], "source": ["inventory_control_new_packaged_items_df = new_packaged_items_cpd_df.query(\n", "    \"expected_cpd < 0.33\"\n", ").sort_values(\"expected_cpd\", ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "8cb8e336-7f62-4966-b39f-63b26313a72c", "metadata": {}, "outputs": [], "source": ["inventory_control_new_packaged_items_df[\"tag_value\"] = 1\n", "inventory_control_new_packaged_items_df[\"active\"] = 1"]}, {"cell_type": "code", "execution_count": null, "id": "36bd0e21-c60f-4b7c-8c53-c1e74b4d32d9", "metadata": {}, "outputs": [], "source": ["inventory_control_new_packaged_items_df = inventory_control_new_packaged_items_df[\n", "    [\"item_id\", \"tag_value\", \"active\"]\n", "].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "37148240-9036-4fb3-9875-a5c3b6b35927", "metadata": {}, "outputs": [], "source": ["bulk_params = {\n", "    \"upload_type_id\": 74,\n", "    \"created_by\": \"<PERSON><PERSON><PERSON>\",\n", "    \"user_id\": 14,\n", "    \"is_auto_po\": True,\n", "    \"params\": {\"tag_type_id\": 3, \"is_auto_po\": True},\n", "}\n", "\n", "\n", "def publish_csv_to_bulk_upload(**bulk_params):\n", "    print(\"Starting Bulk Upload\")\n", "    payload = {}\n", "    payload.update(bulk_params)\n", "    url = \"http://bulk-upload-retail.prod-sgp-k8s.grofer.io/bulk_upload/\" + \"v1/upload_jobs\"\n", "    response = requests.post(url, json=payload)\n", "    print(bulk_params)\n", "    print(response)\n", "    return response\n", "\n", "\n", "def upload_high_asp(df, name):\n", "\n", "    df.to_csv(HO + \"/\" + name + \"_\" + today_date + \".csv\", index=False)\n", "\n", "    csv_file_path = HO + \"/\" + name + \"_\" + today_date + \".csv\"\n", "\n", "    file_name = \"/\" + name + \"_\" + today_date + \".csv\"\n", "\n", "    aws_file_path = \"item_tag_mapping{file_path}\".format(file_path=file_name)\n", "\n", "    secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "    bucket_name = \"retail-bulk-upload\"\n", "    aws_key = secrets.get(\"aws_key\")\n", "    aws_secret = secrets.get(\"aws_secret\")\n", "    session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "    s3 = session.resource(\"s3\")\n", "    bucket_obj = s3.Bucket(bucket_name)\n", "    bucket_obj.upload_file(csv_file_path, aws_file_path)\n", "\n", "    bulk_params[\"file\"] = aws_file_path\n", "\n", "    bulk_response = publish_csv_to_bulk_upload(**bulk_params)\n", "    if 200 <= bulk_response.status_code <= 299:\n", "        bu_job_id = bulk_response.json()[\"id\"]\n", "        print(\"File Uploaded with job id {id}\".format(id=bu_job_id))"]}, {"cell_type": "code", "execution_count": null, "id": "2ea6a941-2293-460e-95bc-f52fad081560", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"insert_ds_ist\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "    {\"name\": \"tag_value\", \"type\": \"integer\", \"description\": \"tag_value\"},\n", "    {\"name\": \"active\", \"type\": \"integer\", \"description\": \"active\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "9908f4ff-1c1b-4885-842b-233bb066d434", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"new_items_inventory_control_tagging\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"insert_ds_ist\", \"item_id\"],\n", "    \"partition_key\": [\"insert_ds_ist\"],\n", "    # \"incremental_key\": \"updated_at_ist\",\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "    \"table_description\": \"new_items_inventory_control_tagging\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "4f2d7492-1263-488e-a84d-61ca03b4552a", "metadata": {}, "outputs": [], "source": ["max_tries = 3\n", "name = \"high_asp_uploads\"\n", "today_date = date.today().strftime(\"%Y-%m-%d\")\n", "job_status = \"Pending\"\n", "bulk_upload_status = \"Pending\"\n", "print(inventory_control_new_packaged_items_df.shape[0])\n", "print(HO + \"/\" + name + \"_\" + today_date + \".csv\")\n", "if inventory_control_new_packaged_items_df.shape[0] > 0:\n", "    for attempt in range(max_tries):\n", "        try:\n", "            upload_high_asp(inventory_control_new_packaged_items_df, name)\n", "            bulk_upload_status = \"Success\"\n", "            print(\"Bulk Upload Successful.\")\n", "            break\n", "        except BaseException as e:\n", "            if attempt == max_tries - 1:\n", "                bulk_upload_status = \"Failed\"\n", "                print(f\"Bulk Upload failed. Error: {e}\")\n", "            else:\n", "                time.sleep(60)\n", "\n", "if bulk_upload_status == \"Success\":\n", "    for attempt in range(max_tries):\n", "        try:\n", "            inventory_control_new_packaged_items_df[\"insert_ds_ist\"] = date.today()\n", "            inventory_control_new_packaged_items_df = inventory_control_new_packaged_items_df[\n", "                [\"insert_ds_ist\", \"item_id\", \"tag_value\", \"active\"]\n", "            ]\n", "            pb.to_trino(inventory_control_new_packaged_items_df, **kwargs)\n", "            job_status = \"Success\"\n", "            print(\"Data pushed to table\")\n", "            break\n", "        except BaseException as e:\n", "            if attempt == max_tries - 1:\n", "                job_status = \"Failed\"\n", "                print(f\"Failed to push data to the table. Error: {e}\")\n", "            else:\n", "                time.sleep(60)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}