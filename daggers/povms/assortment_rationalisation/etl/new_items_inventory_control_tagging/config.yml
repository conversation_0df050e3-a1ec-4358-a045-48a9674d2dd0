alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: new_items_inventory_control_tagging
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
  retries: 3  
owner:
  email: <EMAIL>
  slack_id: U040ZG10XRP
path: povms/assortment_rationalisation/etl/new_items_inventory_control_tagging
paused: false
pool: povms_pool
project_name: assortment_rationalisation
schedule:
  end_date: '2025-09-18T00:00:00'
  interval: 30 3 * * *
  start_date: '2025-06-23T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 3
