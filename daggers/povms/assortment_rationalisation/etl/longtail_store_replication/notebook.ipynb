{"cells": [{"cell_type": "code", "execution_count": null, "id": "96ca1db0-dc24-4a13-a1de-c1884f3b6dea", "metadata": {}, "outputs": [], "source": ["!pip uninstall -y openpyxl\n", "!pip install openpyxl==3.1.5"]}, {"cell_type": "code", "execution_count": null, "id": "be11afa4-f2e6-486b-ba8d-e79b0e04f643", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import requests\n", "import datetime\n", "from pytz import timezone\n", "import boto3\n", "import os\n", "import sys\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import json\n", "import openpyxl"]}, {"cell_type": "code", "execution_count": null, "id": "44c7b17e-7be5-4e9a-8867-64d035871344", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "678b6953-59eb-4e4e-a3f2-ef102ab70772", "metadata": {}, "outputs": [], "source": ["l0_id_to_remove_temp = (675, -1)"]}, {"cell_type": "code", "execution_count": null, "id": "7442e318-e72d-4bfa-bff6-a12417af7584", "metadata": {}, "outputs": [], "source": ["slack_alert_channel = \"assortment-alerts-plus-discussions\"\n", "EXECUTION_MODE = \"PROD\"\n", "spreadSheetId = \"1SfMAMyyqe4DY_-2QrCULfz2V57vsCZo0yeXceWgmmAQ\"\n", "sheetName = \"Dag input\"\n", "logTable = \"longtail_store_replication_log\""]}, {"cell_type": "code", "execution_count": null, "id": "3ea87f51-66a3-4060-bd3d-ba48c4a3c339", "metadata": {}, "outputs": [], "source": ["req_columns = [\n", "    \"item_id\",\n", "    \"city_name\",\n", "    \"backend_facility_id\",\n", "    \"frontend_facility_id\",\n", "    \"master_assortment_substate\",\n", "    \"assortment_type\",\n", "    \"substate_reason_type\",\n", "    \"request_reason_type\",\n", "]\n", "output_column_types = {\n", "    \"item_id\": \"int\",\n", "    \"city_name\": \"str\",\n", "    \"backend_facility_id\": \"int\",\n", "    \"frontend_facility_id\": \"int\",\n", "    \"master_assortment_substate\": \"str\",\n", "    \"assortment_type\": \"str\",\n", "    \"substate_reason_type\": \"str\",\n", "    \"request_reason_type\": \"str\",\n", "}\n", "\n", "input_column_types = {\n", "    \"new_lt_store\": \"int\",\n", "    \"sister_lt_store\": \"int\",\n", "    \"new_lt_store_city_id\": \"int\",\n", "    \"is_replicated\": \"int\",\n", "    \"all_checks_passed\": \"int\",\n", "}\n", "\n", "\n", "def convert_columns(df, columnTypeJson):\n", "    # Iterate over each column in the dataframe\n", "    for col in df.columns:\n", "        if col in columnTypeJson and columnTypeJson[col] == \"str\":\n", "            df[col] = df[col].astype(str)\n", "        elif col in columnTypeJson and columnTypeJson[col] == \"int\":\n", "            df[col] = pd.to_numeric(df[col])\n", "            df[col] = df[col].apply(lambda x: int(x) if not np.isnan(x) else x)\n", "    return df\n", "\n", "\n", "def check_required_columns(df, req_columns):\n", "    # Get the current columns of the DataFrame\n", "    current_columns = set(df.columns)\n", "    # Convert required columns to a set\n", "    required = set(req_columns)\n", "    # Check if all required columns are present\n", "    return required.issubset(current_columns)"]}, {"cell_type": "code", "execution_count": null, "id": "685831f3-e65f-4e6e-98c8-e5b72dbdb46d", "metadata": {}, "outputs": [], "source": ["def sendSlackAlert(execution_mode, text):\n", "    if execution_mode == \"PROD\":\n", "        try:\n", "            pb.send_slack_message(channel=slack_alert_channel, text=text)\n", "        except Exception as e:\n", "            print(f\"Error sending Slack message: {e}, {text}\")\n", "        # handle the error further if needed\n", "    else:\n", "        print(\"local environment, just printing to console\")\n", "        print(text)"]}, {"cell_type": "code", "execution_count": null, "id": "534d87f3-6563-476e-b3e9-68a165e751a5", "metadata": {}, "outputs": [], "source": ["def upload_to_bu(file_path, upload_type):\n", "    try:\n", "        url = \"https://retail-internal.grofer.io/bulk_upload/v1/upload_jobs\"\n", "        payload = {\n", "            \"file\": file_path,\n", "            \"created_by\": \"<PERSON><PERSON>\",\n", "            \"user_id\": 14,\n", "            \"is_auto_po\": True,\n", "            \"upload_type_id\": upload_type,\n", "            \"content_type\": \"text/csv\",\n", "        }\n", "        response = requests.post(url, json=payload)\n", "        return response.status_code, response.json()\n", "    except Exception as e:\n", "        print(\"error in bulk upload api\", e)\n", "        return 404, {}"]}, {"cell_type": "code", "execution_count": null, "id": "dc73b84b-bbfc-474c-8fe3-e9d1827170dd", "metadata": {}, "outputs": [], "source": ["def readFromSheetsWithRetry(spreadSheetId, sheetName, raiseExceptionFlag=True, mode=EXECUTION_MODE):\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            if mode == \"LOCAL\":\n", "                script_dir = \"/home/<USER>/inventory-planning-playbooks/\"\n", "                sys.path.insert(0, script_dir)\n", "                from utils.commonFunc import readSpreadsheet\n", "\n", "                values = readSpreadsheet(spreadSheetId, sheetName)\n", "                df = pd.DataFrame(values[1:], columns=values[0])\n", "                return df\n", "            else:\n", "                df = pb.from_sheets(spreadSheetId, sheetName)\n", "                return df\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error fetching data (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to fetch data after {max_retries} attempts: {e}\")\n", "                if raiseExceptionFlag:\n", "                    raise Exception(\n", "                        \"Failed to read sheets after {} attempts\".format(max_retries)\n", "                    ) from e\n", "                else:\n", "                    sendSlackAlert(\n", "                        mode,\n", "                        \"povms_assortment_rationalisation_etl_longtail_store_replication dag failed to read sheet\"\n", "                        + spreadSheetId\n", "                        + sheetName,\n", "                    )\n", "                    return pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "id": "c44a3b28-e2b2-4239-9fec-55b93cef4797", "metadata": {}, "outputs": [], "source": ["def pushD<PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "    df,\n", "    tableName,\n", "    description=None,\n", "    primaryKeys=[\"insert_ds_ist\"],\n", "    load_type=\"upsert\",\n", "    environ=\"supply_etls\",\n", "    partition_key=[],\n", "):\n", "    from datetime import datetime, timezone, timedelta\n", "    import time\n", "    import pandas as pd\n", "\n", "    utc_now = datetime.now(timezone.utc)\n", "    ist_now = utc_now + <PERSON><PERSON><PERSON>(hours=5, minutes=30)\n", "    formatted_date = ist_now.strftime(\"%Y-%m-%d\")\n", "    df[\"insert_ds_ist\"] = formatted_date\n", "    df[\"updated_at\"] = pd.to_datetime(utc_now)\n", "    column_dtypes = [\n", "        {\n", "            \"name\": col,\n", "            \"type\": str(df[col].dtype)\n", "            .replace(\"int64\", \"integer\")\n", "            .replace(\"float64\", \"real\")\n", "            .replace(\"str\", \"varchar\")\n", "            .replace(\"datetime64[ns]\", \"timestamp(6)\")\n", "            .replace(\"object\", \"varchar\")\n", "            .replace(\"bool\", \"boolean\")\n", "            .replace(\"datetime64[ns, UTC]\", \"timestamp(6)\"),\n", "            \"description\": col,\n", "        }\n", "        for col in df.columns\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": environ,\n", "        \"table_name\": tableN<PERSON>,\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": primary<PERSON><PERSON><PERSON>,\n", "        \"load_type\": load_type,\n", "        \"table_description\": (description if description else tableName),\n", "    }\n", "\n", "    if len(partition_key) > 0:\n", "        kwargs[\"partition_key\"] = partition_key\n", "\n", "    import pencilbox as pb\n", "\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            pb.to_trino(data_obj=df, **kwargs)\n", "            return\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error pushDfToTrino (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to pushDfToTrino after {max_retries} attempts: {e}\")\n", "                return"]}, {"cell_type": "code", "execution_count": null, "id": "11596a1d-b3f1-4358-bb36-c6357eed0ef6", "metadata": {}, "outputs": [], "source": ["def processFile(input_df, chunk_size=3000):\n", "    if input_df.shape[0] == 0:\n", "        print(\"nothing to process, returning\")\n", "        return\n", "    elif input_df.shape[0] < chunk_size:\n", "        list_df = [input_df]\n", "    else:\n", "        list_df = [input_df[i : i + chunk_size] for i in range(0, input_df.shape[0], chunk_size)]\n", "\n", "    responses = []\n", "    df_logs = pd.DataFrame()\n", "    all_job_ids = []\n", "    for df in list_df:\n", "        uid = int(time.time())\n", "        local_file_path = f\"longtail_replication_assortment_request_upload_bot_{uid}.xlsx\"\n", "        df.to_excel(local_file_path, index=False, engine=\"openpyxl\")\n", "        file_path = f\"assortment/{local_file_path}\"\n", "        secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "        bucket_name = \"retail-bulk-upload\"\n", "        aws_key = secrets.get(\"aws_key\")\n", "        aws_secret = secrets.get(\"aws_secret\")\n", "        session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "        s3 = session.resource(\"s3\")\n", "        bucket_obj = s3.Bucket(bucket_name)\n", "        bucket_obj.upload_file(local_file_path, file_path)\n", "        status_code, responseJson = upload_to_bu(file_path, 100)\n", "        os.remove(local_file_path)\n", "        df[\"uid\"] = uid\n", "        # if status_code:\n", "        df[\"job_id\"] = responseJson.get(\"id\") if responseJson else 0  # Ensure it's a list\n", "        # all_job_ids = pd.DataFrame({'job_id': job_ids})\n", "        df[\"status_code\"] = status_code\n", "        df_logs = pd.concat([df_logs, df])\n", "        time.sleep(2)\n", "    df_logs[\"source\"] = \"povms_assortment_rationalisation_etl_longtail_store_replication\"\n", "    pushDfToTrino(\n", "        df_logs,\n", "        logTable,\n", "        description=\"longtail store replication data log table\",\n", "        load_type=\"append\",\n", "        environ=\"supply_etls\",\n", "        partition_key=[\"insert_ds_ist\"],\n", "    )\n", "    return df_logs"]}, {"cell_type": "code", "execution_count": null, "id": "8ea89355-a03b-4770-b7f1-547979cd19ef", "metadata": {}, "outputs": [], "source": ["# def pushToGoogleSheetsWithRetry(df, sheet_id, sheet_name, mode = EXECUTION_MODE, folderId = '1SUX0F45AXDs8Junz7-ZAsXZZC8zFXW0d', fileName = 'testing'):\n", "#     # folderId needed for local execution, default folder = https://drive.google.com/drive/folders/1SUX0F45AXDs8Junz7-ZAsXZZC8zFXW0d\n", "#     # fileName = also mandatory is local mode\n", "#     max_retries = 3  # adjust the number of retries as needed\n", "#     retry_delay = 10  # seconds\n", "#     import time\n", "#     import os\n", "#     import sys\n", "#     for attempt in range(max_retries + 1):\n", "#         try:\n", "#             if mode == 'LOCAL':\n", "#                 if fileName is None:\n", "#                     print(\"error filename not passed in the function, doing nothing, it is mandatory for local mode\")\n", "#                     return\n", "#                 script_dir = '/home/<USER>/inventory-planning-playbooks/'\n", "#                 sys.path.insert(0, script_dir)\n", "#                 from utils.commonFunc import pushDataFrameToFile, prepFileForGoogleSheets\n", "#                 pushDataFrameToFile(prepFileForGoogleSheets(df), fileName, sheet_name, folderId, None)\n", "#                 return\n", "#             else:\n", "#                 import pencilbox as pb\n", "#                 pb.to_sheets(df, sheet_id, sheet_name)\n", "#                 return\n", "#         except Exception as e:\n", "#             if attempt < max_retries:\n", "#                 print(f\"Error pushing data (attempt {attempt+1}/{max_retries}): {e}\")\n", "#                 time.sleep(retry_delay * (attempt + 1))\n", "#             else:\n", "#                 print(f\"Failed to push data after {max_retries} attempts: {e}\")\n", "#                 return"]}, {"cell_type": "code", "execution_count": null, "id": "4200b5c5-320d-4615-bcff-388214a01020", "metadata": {}, "outputs": [], "source": ["input_df = readFromSheetsWithRetry(spreadSheetId, sheetName)\n", "print(\"original shape\", input_df.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "e5c0473d-4549-4b1c-8c15-db2e34605f42", "metadata": {}, "outputs": [], "source": ["input_df.dropna(inplace=True)\n", "print(\"revised shape\", input_df.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "8a872e35-5f26-4209-ae25-0b6666a95b26", "metadata": {}, "outputs": [], "source": ["input_df = convert_columns(input_df, input_column_types)\n", "print(\"final_shape\", input_df.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "1048ea56-78a6-49d5-ac13-6d0e2eb32f63", "metadata": {}, "outputs": [], "source": ["input_df.rename(\n", "    columns={\n", "        \"new_lt_store\": \"facility_id\",\n", "        \"sister_lt_store\": \"sister_store_facility_id\",\n", "        \"new_lt_store_city_id\": \"city_id\",\n", "    },\n", "    inplace=True,\n", ")\n", "input_df = input_df[(input_df[\"all_checks_passed\"] == 1) & (input_df[\"is_replicated\"] == 0)]\n", "input_df = input_df.copy()\n", "input_df.drop(columns={\"is_replicated\", \"all_checks_passed\"}, inplace=True)\n", "input_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ecb7099e-d8c3-4542-9274-2270111c4520", "metadata": {}, "outputs": [], "source": ["def replacement_store(input_replacement_store_id, l0_id_to_remove_temp):\n", "    sql = f\"\"\"\n", "select facility_id, ma.item_id, master_assortment_substate_id, assortment_type, category,product_type\n", "from rpc.product_facility_master_assortment ma \n", "inner join \n", "    (\n", "    select p.item_id, \n", "    p.name as item_name,l0,l0_id,l1,l1_id,l2,l2_id,product_type,product_type_id,\n", "    p.weight_in_gm,\n", "    p.variant_mrp as mrp,\n", "    CASE\n", "        WHEN p.handling_type='8' THEN 'PACKAGING_MATERIAL'\n", "        WHEN d.l0_id=1487 THEN 'FNV'\n", "        WHEN d.l1_id=183 THEN 'ICE_CREAM'\n", "        WHEN d.l2_id=1185 THEN 'MILK'\n", "        WHEN p.storage_type IN ('3',\n", "                                   '7') THEN 'FROZEN'\n", "        WHEN p.perishable=1 THEN 'PERISHABLE'\n", "        WHEN p.storage_type IN ('2',\n", "                                   '6') THEN 'COLD'\n", "        WHEN p.handling_type = '6' THEN 'MEDICINAL'\n", "        ELSE 'GROCERY'\n", "        END AS category,\n", "    case when (p.storage_type) in ('1','8') then 'REGULAR'\n", "        when (p.storage_type) in ('4','5') then 'HEAVY'\n", "        when (p.storage_type) in ('2','6') then 'COLD'\n", "        when (p.storage_type) in ('3','7') then 'FROZEN'\n", "        else 'REGULAR' end as storage_type1\n", "\n", "    from rpc.product_product p\n", "    inner join (select item_id, max(id) as max_id from rpc.product_product where active = 1 group by 1) s on s.max_id = p.id\n", "    inner join rpc.item_category_details d on p.item_id = d.item_id\n", "    ) pp on pp.item_id = ma.item_id\n", "where ma.active = 1 and ma.master_assortment_substate_id in (1,3) and \n", "facility_id = {input_replacement_store_id} and assortment_type = 'LONGTAIL' \n", "and facility_id in (select distinct facility_id from po.physical_facility_outlet_mapping where ars_active = 1 and active = 1\n", "and outlet_id in (select distinct outlet_id from po.bulk_facility_outlet_mapping where active and lake_active_record))\n", "and l0_id not in {l0_id_to_remove_temp}\n", "\"\"\"\n", "    return pd.read_sql_query(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "73b86ba2-72b9-4b25-b810-ae4a755f8bed", "metadata": {}, "outputs": [], "source": ["seller_items = f\"\"\"\n", "select distinct(item_id) from lake_seller.seller_product_mappings\"\"\"\n", "seller_items_raw = pd.read_sql_query(sql=seller_items, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "3853175b-f6aa-4bce-bd8b-d235f96b1ac4", "metadata": {}, "outputs": [], "source": ["seller_items_raw[\"seller_flag\"] = 1"]}, {"cell_type": "code", "execution_count": null, "id": "9498e08f-e70f-4ea4-b781-43dbafc486f7", "metadata": {}, "outputs": [], "source": ["seller_items_raw.head()"]}, {"cell_type": "code", "execution_count": null, "id": "febc1e83-24a6-42cf-9f3e-44c1ac5c6fa8", "metadata": {}, "outputs": [], "source": ["def assortment_replication(\n", "    city_id, input_replacement_store_id, res, input_facility_id, seller_items_raw\n", "):\n", "    # Fetch replacement store data\n", "    print(\"Fetch replacement store data\")\n", "    replacement_store_raw = replacement_store(input_replacement_store_id, l0_id_to_remove_temp)\n", "    replacement_store_raw[\"flag\"] = \"replacement\"\n", "    replacement_store_raw = replacement_store_raw.rename(\n", "        columns={\"master_assortment_substate_id\": \"active_flag\"}\n", "    )\n", "\n", "    # Remove 'facility_id' column\n", "    print(\"removing seller items\")\n", "    replacement_store_raw = replacement_store_raw.drop(columns=\"facility_id\")\n", "    final_df = replacement_store_raw.drop_duplicates(subset=\"item_id\", keep=\"first\").reset_index(\n", "        drop=True\n", "    )\n", "    final_df = final_df.merge(seller_items_raw, how=\"left\", on=\"item_id\")\n", "    final_df[\"seller_flag\"].fillna(0, inplace=True)\n", "    final_df = final_df[final_df[\"seller_flag\"] == 0]\n", "    final_df = final_df.copy()\n", "\n", "    print(\"creating dataframe for upload\")\n", "    final_df = final_df.drop(columns={\"flag\", \"category\", \"product_type\", \"seller_flag\"})\n", "    final_df[\"master_assortment_substate\"] = np.where(\n", "        final_df[\"active_flag\"] == 3, \"Temporarily Inactive\", \"Active\"\n", "    )\n", "    final_df = final_df.drop(columns=\"active_flag\")\n", "    final_df[\"city_name\"] = \"\"\n", "    final_df[\"backend_facility_id\"] = \"\"\n", "    final_df[\"request_reason_type\"] = \"longtail_store_replication\"\n", "    final_df[\"substate_reason_type\"] = \"BAU\"\n", "    final_df[\"frontend_facility_id\"] = input_facility_id\n", "    final_df[\"assortment_type\"] = np.where(\n", "        final_df[\"assortment_type\"] == \"LONG<PERSON><PERSON>\", \"LONG<PERSON><PERSON>\", \"EXPRESS_ALL\"\n", "    )\n", "    request_df = final_df[\n", "        [\n", "            \"item_id\",\n", "            \"city_name\",\n", "            \"backend_facility_id\",\n", "            \"frontend_facility_id\",\n", "            \"master_assortment_substate\",\n", "            \"assortment_type\",\n", "            \"substate_reason_type\",\n", "            \"request_reason_type\",\n", "        ]\n", "    ]\n", "    # request_df_file = f\"{res}_longtail_store_replication.csv\"\n", "    # request_df.to_csv(request_df_file, index=False)\n", "    # print(f\"final new store data saved to {request_df_file}\")\n", "    return request_df"]}, {"cell_type": "code", "execution_count": null, "id": "1bb663e6-f1c0-4ba6-a8ff-da39b0a48659", "metadata": {}, "outputs": [], "source": ["# Iterate over rows in the input dataframe\n", "if input_df.shape[0] > 0:\n", "    for index, row in input_df.iterrows():\n", "        # Extract values for the current row\n", "        input_facility_id = row[\"facility_id\"]\n", "        input_replacement_store_id = row[\"sister_store_facility_id\"]\n", "        city_id = row[\"city_id\"]\n", "        res = input_facility_id\n", "        seller_items_raw = seller_items_raw\n", "\n", "        # call the replication function\n", "        print(\"running func for replication\")\n", "        request_df = assortment_replication(\n", "            city_id, input_replacement_store_id, res, input_facility_id, seller_items_raw\n", "        )\n", "        print(\"Replication done for: \", input_facility_id)\n", "        print(\"-\" * 50)  # Separator for better readability\n", "        print(\"converting to dtypes for upload\")\n", "        request_df = convert_columns(request_df, output_column_types)\n", "        req_columns = [\n", "            \"item_id\",\n", "            \"city_name\",\n", "            \"backend_facility_id\",\n", "            \"frontend_facility_id\",\n", "            \"master_assortment_substate\",\n", "            \"assortment_type\",\n", "            \"substate_reason_type\",\n", "            \"request_reason_type\",\n", "        ]\n", "        flag = check_required_columns(request_df, req_columns)\n", "        if not flag or request_df.shape[0] == 0:\n", "            print(\"invalid columns in the final df for upload, doing nothing\", input_df.columns)\n", "        else:\n", "            output_df = processFile(request_df)\n", "            job_ids = output_df.job_id.unique()\n", "            # for job_id in job_ids:\n", "            print(\"job_id:\", job_ids)\n", "            sendSlackAlert(\n", "                EXECUTION_MODE,\n", "                f\"response of the file upload longtail store {res} : \" + str(job_ids),\n", "            )\n", "            # output_df['replicated_stores'] = input_facility_id\n", "            print(\"uploading the output file\")\n", "            # pushToGoogleSheetsWithRetry\n", "            # (output_df, spreadSheetId, writesheetName)\n", "else:\n", "    print(\"input df is empty\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}