{"cells": [{"cell_type": "code", "execution_count": null, "id": "127c6192-8c24-4fed-a084-f8bf5211a7aa", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import requests\n", "import json"]}, {"cell_type": "code", "execution_count": null, "id": "0e59982f-cc88-4645-b8a5-3775f8ec00e8", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "c1df06d4-4e78-4fd4-b242-1799a4b4c02e", "metadata": {}, "outputs": [], "source": ["inactive_longtail_stores = \"\"\"with base as (select long_tail_facility_id, outlet_name, case when longtail_facility_id is null then 'no longtail polygon updated' end as \"comments\", max(date(lo.updated_at)) as last_update_date\n", "from po.long_tail_facility_store_mapping lo\n", "left join consumer_etls.longtail_express_merchant_mapping lm\n", "on lm.longtail_facility_id = lo.long_tail_facility_id \n", "inner join po.physical_facility_outlet_mapping om on om.facility_id = lo.long_tail_facility_id and ars_active = 1 and om.active = 1\n", "where lo.active = 1\n", "and longtail_facility_id is null\n", "and lo.updated_at <= current_date - interval '15' day\n", "group by 1,2,3)\n", "\n", "select long_tail_facility_id, adjacent_facility_id, active, date(updated_at) as last_updated_at from po.long_tail_facility_store_mapping\n", "where long_tail_facility_id in (select distinct long_tail_facility_id from base ) and active = 1\n", "\"\"\"\n", "inactive_longtail_stores_raw = pd.read_sql_query(sql=inactive_longtail_stores, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "c5100493-f4db-4fae-90b7-4f09bcbee659", "metadata": {}, "outputs": [], "source": ["inactive_longtail_stores_raw"]}, {"cell_type": "code", "execution_count": null, "id": "6b15d2d7-9329-4b07-afa2-45737dfd51ae", "metadata": {}, "outputs": [], "source": ["inactive_longtail_stores_raw.drop(columns=\"last_updated_at\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "516ce76e-331c-4084-a508-0b565fb8ba54", "metadata": {}, "outputs": [], "source": ["inactive_longtail_stores_raw[\"active\"] = 0"]}, {"cell_type": "code", "execution_count": null, "id": "1f1e86f3-0a68-484a-9733-3b341d01345f", "metadata": {}, "outputs": [], "source": ["final_df = inactive_longtail_stores_raw.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "b73e6ccd-12d1-4b76-afd2-6c82e4dbe732", "metadata": {}, "outputs": [], "source": ["for idx, row in final_df.iterrows():\n", "    url = \"https://retail-internal.grofer.io/po/v1/update-longtail-facility-adjacent-facility-mapping/\"\n", "\n", "    payload = json.dumps(\n", "        {\n", "            \"longtail_facility_id\": int(row[\"long_tail_facility_id\"]),\n", "            \"adjacent_facility_id\": int(row[\"adjacent_facility_id\"]),\n", "            \"active\": int(row[\"active\"]),\n", "        }\n", "    )\n", "    headers = {\"Content-Type\": \"application/json\"}\n", "\n", "    response = requests.request(\"POST\", url, headers=headers, data=payload)\n", "    print(row)\n", "    print(response.text)"]}, {"cell_type": "code", "execution_count": null, "id": "df93a080-2d44-4f61-82ad-95b8eabe3e1b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}