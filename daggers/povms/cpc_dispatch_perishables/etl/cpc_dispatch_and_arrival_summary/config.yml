alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: cpc_dispatch_and_arrival_summary
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03R8Q6UZ71
path: povms/cpc_dispatch_perishables/etl/cpc_dispatch_and_arrival_summary
paused: true
pool: povms_pool
project_name: cpc_dispatch_perishables
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 0 3 * * *
  start_date: '2022-07-28T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 3
