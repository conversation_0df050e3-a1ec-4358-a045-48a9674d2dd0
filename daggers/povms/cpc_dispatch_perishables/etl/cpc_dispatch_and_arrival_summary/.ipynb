{"cells": [{"cell_type": "code", "execution_count": 1, "id": "6b1024cd-f941-4a79-9d85-5edfbfd3eaec", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "import json\n", "import math\n", "\n", "# import boto3F\n", "import io\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")\n", "\n", "start_time = time.time()"]}, {"cell_type": "code", "execution_count": 2, "id": "f40a2918-40d6-4a1a-bd25-3b78d850339e", "metadata": {}, "outputs": [], "source": ["# pd.set_option('display.max_columns', None)\n", "# pd.set_option('display.max_rows', 500)\n", "pd.options.mode.chained_assignment = None  # default='warn'"]}, {"cell_type": "markdown", "id": "1f024531-55cc-4448-8e42-ba6b501e965d", "metadata": {}, "source": ["Fetching STO Details"]}, {"cell_type": "code", "execution_count": 3, "id": "a0f07dca-1841-4eed-a836-3e5b5fbb5ec7", "metadata": {}, "outputs": [], "source": ["start_date = date.today() - <PERSON><PERSON><PERSON>(days= 1)\n", "end_date = date.today()"]}, {"cell_type": "code", "execution_count": 4, "id": "f94d9571-7ded-4548-8f5a-1f64313ec7e5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sto_id</th>\n", "      <th>item_id</th>\n", "      <th>sender_outlet_id</th>\n", "      <th>sender_outlet_name</th>\n", "      <th>receiving_outlet_id</th>\n", "      <th>receiver_outlet_name</th>\n", "      <th>billed_quantity</th>\n", "      <th>trip_started_at</th>\n", "      <th>consignment_received_at</th>\n", "      <th>sto_created_at</th>\n", "      <th>sto_billed_at</th>\n", "      <th>sto_dispatched_at</th>\n", "      <th>grn_created_at</th>\n", "      <th>inward_quantity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5130878</td>\n", "      <td>10005407</td>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2866</td>\n", "      <td>SS Panchkula Industrial Area ES1 (Crof)</td>\n", "      <td>5</td>\n", "      <td>2022-07-26 03:26:31.267</td>\n", "      <td>2022-07-26 05:06:19.450</td>\n", "      <td>2022-07-25 22:06:06.996</td>\n", "      <td>NaT</td>\n", "      <td>None</td>\n", "      <td>2022-07-26 05:11:12.873</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5130878</td>\n", "      <td>10043867</td>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2866</td>\n", "      <td>SS Panchkula Industrial Area ES1 (Crof)</td>\n", "      <td>43</td>\n", "      <td>2022-07-26 03:26:31.267</td>\n", "      <td>2022-07-26 05:06:19.450</td>\n", "      <td>2022-07-25 22:06:06.996</td>\n", "      <td>NaT</td>\n", "      <td>None</td>\n", "      <td>2022-07-26 05:11:12.873</td>\n", "      <td>43</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5130878</td>\n", "      <td>10043865</td>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2866</td>\n", "      <td>SS Panchkula Industrial Area ES1 (Crof)</td>\n", "      <td>16</td>\n", "      <td>2022-07-26 03:26:31.267</td>\n", "      <td>2022-07-26 05:06:19.450</td>\n", "      <td>2022-07-25 22:06:06.996</td>\n", "      <td>NaT</td>\n", "      <td>None</td>\n", "      <td>2022-07-26 05:11:12.873</td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>5130878</td>\n", "      <td>10032019</td>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2866</td>\n", "      <td>SS Panchkula Industrial Area ES1 (Crof)</td>\n", "      <td>5</td>\n", "      <td>2022-07-26 03:26:31.267</td>\n", "      <td>2022-07-26 05:06:19.450</td>\n", "      <td>2022-07-25 22:06:06.996</td>\n", "      <td>NaT</td>\n", "      <td>None</td>\n", "      <td>2022-07-26 05:11:12.873</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5130878</td>\n", "      <td>10028965</td>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2866</td>\n", "      <td>SS Panchkula Industrial Area ES1 (Crof)</td>\n", "      <td>3</td>\n", "      <td>2022-07-26 03:26:31.267</td>\n", "      <td>2022-07-26 05:06:19.450</td>\n", "      <td>2022-07-25 22:06:06.996</td>\n", "      <td>NaT</td>\n", "      <td>None</td>\n", "      <td>2022-07-26 05:11:12.873</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13345</th>\n", "      <td>5131323</td>\n", "      <td>10098520</td>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2314</td>\n", "      <td>Super Store Delhi Ashram ES24 PR (FNV)</td>\n", "      <td>3</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-25 22:36:52.662</td>\n", "      <td>NaT</td>\n", "      <td>None</td>\n", "      <td>2022-07-26 05:50:20.913</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13346</th>\n", "      <td>5131323</td>\n", "      <td>10098509</td>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2314</td>\n", "      <td>Super Store Delhi Ashram ES24 PR (FNV)</td>\n", "      <td>3</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-25 22:36:52.662</td>\n", "      <td>NaT</td>\n", "      <td>None</td>\n", "      <td>2022-07-26 05:50:20.913</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13347</th>\n", "      <td>5131323</td>\n", "      <td>10098514</td>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2314</td>\n", "      <td>Super Store Delhi Ashram ES24 PR (FNV)</td>\n", "      <td>3</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-25 22:36:52.662</td>\n", "      <td>NaT</td>\n", "      <td>None</td>\n", "      <td>2022-07-26 05:50:20.913</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13348</th>\n", "      <td>5131323</td>\n", "      <td>10002268</td>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2314</td>\n", "      <td>Super Store Delhi Ashram ES24 PR (FNV)</td>\n", "      <td>24</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-25 22:36:52.662</td>\n", "      <td>NaT</td>\n", "      <td>None</td>\n", "      <td>2022-07-26 05:50:20.913</td>\n", "      <td>24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13349</th>\n", "      <td>5131323</td>\n", "      <td>10002241</td>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2314</td>\n", "      <td>Super Store Delhi Ashram ES24 PR (FNV)</td>\n", "      <td>6</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-25 22:36:52.662</td>\n", "      <td>NaT</td>\n", "      <td>None</td>\n", "      <td>2022-07-26 05:50:20.913</td>\n", "      <td>6</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>13350 rows × 14 columns</p>\n", "</div>"], "text/plain": ["        sto_id   item_id  sender_outlet_id                 sender_outlet_name  \\\n", "0      5130878  10005407              3229           SS Mohali F&V CPC (MODI)   \n", "1      5130878  10043867              3229           SS Mohali F&V CPC (MODI)   \n", "2      5130878  10043865              3229           SS Mohali F&V CPC (MODI)   \n", "3      5130878  10032019              3229           SS Mohali F&V CPC (MODI)   \n", "4      5130878  10028965              3229           SS Mohali F&V CPC (MODI)   \n", "...        ...       ...               ...                                ...   \n", "13345  5131323  10098520              2472  Super Store Bamnoli F&V CPC (SSC)   \n", "13346  5131323  10098509              2472  Super Store Bamnoli F&V CPC (SSC)   \n", "13347  5131323  10098514              2472  Super Store Bamnoli F&V CPC (SSC)   \n", "13348  5131323  10002268              2472  Super Store Bamnoli F&V CPC (SSC)   \n", "13349  5131323  10002241              2472  Super Store Bamnoli F&V CPC (SSC)   \n", "\n", "       receiving_outlet_id                     receiver_outlet_name  \\\n", "0                     2866  SS Panchkula Industrial Area ES1 (Crof)   \n", "1                     2866  SS Panchkula Industrial Area ES1 (Crof)   \n", "2                     2866  SS Panchkula Industrial Area ES1 (Crof)   \n", "3                     2866  SS Panchkula Industrial Area ES1 (Crof)   \n", "4                     2866  SS Panchkula Industrial Area ES1 (Crof)   \n", "...                    ...                                      ...   \n", "13345                 2314   Super Store Delhi Ashram ES24 PR (FNV)   \n", "13346                 2314   Super Store Delhi Ashram ES24 PR (FNV)   \n", "13347                 2314   Super Store Delhi Ashram ES24 PR (FNV)   \n", "13348                 2314   Super Store Delhi Ashram ES24 PR (FNV)   \n", "13349                 2314   Super Store Delhi Ashram ES24 PR (FNV)   \n", "\n", "       billed_quantity         trip_started_at consignment_received_at  \\\n", "0                    5 2022-07-26 03:26:31.267 2022-07-26 05:06:19.450   \n", "1                   43 2022-07-26 03:26:31.267 2022-07-26 05:06:19.450   \n", "2                   16 2022-07-26 03:26:31.267 2022-07-26 05:06:19.450   \n", "3                    5 2022-07-26 03:26:31.267 2022-07-26 05:06:19.450   \n", "4                    3 2022-07-26 03:26:31.267 2022-07-26 05:06:19.450   \n", "...                ...                     ...                     ...   \n", "13345                3                     NaT                     NaT   \n", "13346                3                     NaT                     NaT   \n", "13347                3                     NaT                     NaT   \n", "13348               24                     NaT                     NaT   \n", "13349                6                     NaT                     NaT   \n", "\n", "               sto_created_at sto_billed_at sto_dispatched_at  \\\n", "0     2022-07-25 22:06:06.996           NaT              None   \n", "1     2022-07-25 22:06:06.996           NaT              None   \n", "2     2022-07-25 22:06:06.996           NaT              None   \n", "3     2022-07-25 22:06:06.996           NaT              None   \n", "4     2022-07-25 22:06:06.996           NaT              None   \n", "...                       ...           ...               ...   \n", "13345 2022-07-25 22:36:52.662           NaT              None   \n", "13346 2022-07-25 22:36:52.662           NaT              None   \n", "13347 2022-07-25 22:36:52.662           NaT              None   \n", "13348 2022-07-25 22:36:52.662           NaT              None   \n", "13349 2022-07-25 22:36:52.662           NaT              None   \n", "\n", "               grn_created_at  inward_quantity  \n", "0     2022-07-26 05:11:12.873                5  \n", "1     2022-07-26 05:11:12.873               43  \n", "2     2022-07-26 05:11:12.873               16  \n", "3     2022-07-26 05:11:12.873                5  \n", "4     2022-07-26 05:11:12.873                3  \n", "...                       ...              ...  \n", "13345 2022-07-26 05:50:20.913                3  \n", "13346 2022-07-26 05:50:20.913                3  \n", "13347 2022-07-26 05:50:20.913                3  \n", "13348 2022-07-26 05:50:20.913               24  \n", "13349 2022-07-26 05:50:20.913                6  \n", "\n", "[13350 rows x 14 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["def STO_base ():\n", "    sql = f\"\"\"select sto_id, item_id, sender_outlet_id,sender_outlet_name,receiving_outlet_id, receiver_outlet_name, billed_quantity,trip_started_at,consignment_received_at,\n", "             max(sto_created_at) as sto_created_at,max(sto_billed_at) as sto_billed_at, max(sto_dispatched_at) as sto_dispatched_at,max(grn_created_at) as grn_created_at, sum(inward_quantity) as inward_quantity\n", "             from metrics.esto_details es\n", "             left join lake_retail.console_outlet co on co.id = es.sender_outlet_id\n", "             where item_id in (select distinct item_id from consumer.dark_stores_fnv_assortment)\n", "             and sender_outlet_id in (2666,3229,2672,2472,1280,1674,2925)\n", "             and receiving_outlet_id not in (2340)\n", "             and sto_created_at between '{start_date}' || ' 20:00:00' and '{end_date}' || ' 02:00:00'\n", "             group by 1,2,3,4,5,6,7,8,9\n", "        \"\"\"\n", "    return(pd.read_sql_query(sql=sql, con=CON_REDSHIFT))\n", "\n", "df_base_list= STO_base()\n", "df_base_list"]}, {"cell_type": "code", "execution_count": 5, "id": "5665fb85-bb59-4fdd-880b-d3c488fc23cf", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sto_id</th>\n", "      <th>item_id</th>\n", "      <th>sender_outlet_id</th>\n", "      <th>sender_outlet_name</th>\n", "      <th>receiving_outlet_id</th>\n", "      <th>receiver_outlet_name</th>\n", "      <th>billed_quantity</th>\n", "      <th>trip_started_at</th>\n", "      <th>consignment_received_at</th>\n", "      <th>sto_created_at</th>\n", "      <th>sto_billed_at</th>\n", "      <th>sto_dispatched_at</th>\n", "      <th>grn_created_at</th>\n", "      <th>inward_quantity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5130459</td>\n", "      <td>10038440</td>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>1485</td>\n", "      <td>Super Store Delhi Dwarka ES14 PR (FNV)</td>\n", "      <td>5</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-25 21:04:15.411</td>\n", "      <td>2022-07-26 00:58:02.423</td>\n", "      <td>None</td>\n", "      <td>NaT</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5130459</td>\n", "      <td>10005113</td>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>1485</td>\n", "      <td>Super Store Delhi Dwarka ES14 PR (FNV)</td>\n", "      <td>3</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-25 21:04:15.411</td>\n", "      <td>2022-07-26 00:58:02.423</td>\n", "      <td>None</td>\n", "      <td>NaT</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5131047</td>\n", "      <td>10002238</td>\n", "      <td>1674</td>\n", "      <td>Super Store Gurgaon G2 F&amp;V CPC (SSC)</td>\n", "      <td>3672</td>\n", "      <td>SS Gurgaon Sector 10 ES56 (NM)</td>\n", "      <td>14</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-25 22:18:45.581</td>\n", "      <td>2022-07-26 02:05:58.616</td>\n", "      <td>None</td>\n", "      <td>2022-07-26 05:10:02.130</td>\n", "      <td>14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>5131068</td>\n", "      <td>10008908</td>\n", "      <td>1674</td>\n", "      <td>Super Store Gurgaon G2 F&amp;V CPC (SSC)</td>\n", "      <td>1439</td>\n", "      <td>Super Store Gurgaon Sector 62 ES24 (NM)</td>\n", "      <td>4</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-25 22:19:01.016</td>\n", "      <td>NaT</td>\n", "      <td>None</td>\n", "      <td>2022-07-26 06:10:58.573</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5130498</td>\n", "      <td>10003542</td>\n", "      <td>1674</td>\n", "      <td>Super Store Gurgaon G2 F&amp;V CPC (SSC)</td>\n", "      <td>2821</td>\n", "      <td>SS Gurgaon Gwal Pahari ES31 (NM)</td>\n", "      <td>4</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-25 21:04:34.487</td>\n", "      <td>2022-07-26 00:49:33.571</td>\n", "      <td>None</td>\n", "      <td>2022-07-26 06:01:07.000</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17883</th>\n", "      <td>5130429</td>\n", "      <td>10038441</td>\n", "      <td>1674</td>\n", "      <td>Super Store Gurgaon G2 F&amp;V CPC (SSC)</td>\n", "      <td>1953</td>\n", "      <td>Super Store Faridabad Arjun Vatika ES6 PR (NM)</td>\n", "      <td>2</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-25 21:04:07.111</td>\n", "      <td>2022-07-26 00:51:27.900</td>\n", "      <td>None</td>\n", "      <td>NaT</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17884</th>\n", "      <td>5130429</td>\n", "      <td>10063257</td>\n", "      <td>1674</td>\n", "      <td>Super Store Gurgaon G2 F&amp;V CPC (SSC)</td>\n", "      <td>1953</td>\n", "      <td>Super Store Faridabad Arjun Vatika ES6 PR (NM)</td>\n", "      <td>1</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-25 21:04:07.111</td>\n", "      <td>2022-07-26 00:51:27.900</td>\n", "      <td>None</td>\n", "      <td>NaT</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17885</th>\n", "      <td>5131718</td>\n", "      <td>10004052</td>\n", "      <td>2925</td>\n", "      <td>Super Store Noida F&amp;V CPC (SSC)</td>\n", "      <td>2283</td>\n", "      <td>Super Store UPNCR Noida Sector 45 ES20 PR (CC)</td>\n", "      <td>4</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-25 23:47:52.960</td>\n", "      <td>2022-07-26 02:10:21.704</td>\n", "      <td>None</td>\n", "      <td>2022-07-26 06:38:02.742</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17886</th>\n", "      <td>5131330</td>\n", "      <td>10002255</td>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>3532</td>\n", "      <td>SS Delhi Madangir ES160 PR (FNV)</td>\n", "      <td>5</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-25 22:36:57.442</td>\n", "      <td>2022-07-26 01:40:50.426</td>\n", "      <td>None</td>\n", "      <td>2022-07-26 05:33:32.362</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17887</th>\n", "      <td>5132003</td>\n", "      <td>10012985</td>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>1485</td>\n", "      <td>Super Store Delhi Dwarka ES14 PR (FNV)</td>\n", "      <td>19</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 00:49:47.327</td>\n", "      <td>2022-07-26 01:30:26.287</td>\n", "      <td>None</td>\n", "      <td>NaT</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>17888 rows × 14 columns</p>\n", "</div>"], "text/plain": ["        sto_id   item_id  sender_outlet_id  \\\n", "0      5130459  10038440              2472   \n", "1      5130459  10005113              2472   \n", "2      5131047  10002238              1674   \n", "3      5131068  10008908              1674   \n", "4      5130498  10003542              1674   \n", "...        ...       ...               ...   \n", "17883  5130429  10038441              1674   \n", "17884  5130429  10063257              1674   \n", "17885  5131718  10004052              2925   \n", "17886  5131330  10002255              2472   \n", "17887  5132003  10012985              2472   \n", "\n", "                         sender_outlet_name  receiving_outlet_id  \\\n", "0         Super Store Bamnoli F&V CPC (SSC)                 1485   \n", "1         Super Store Bamnoli F&V CPC (SSC)                 1485   \n", "2      Super Store Gurgaon G2 F&V CPC (SSC)                 3672   \n", "3      Super Store Gurgaon G2 F&V CPC (SSC)                 1439   \n", "4      Super Store Gurgaon G2 F&V CPC (SSC)                 2821   \n", "...                                     ...                  ...   \n", "17883  Super Store Gurgaon G2 F&V CPC (SSC)                 1953   \n", "17884  Super Store Gurgaon G2 F&V CPC (SSC)                 1953   \n", "17885       Super Store Noida F&V CPC (SSC)                 2283   \n", "17886     Super Store Bamnoli F&V CPC (SSC)                 3532   \n", "17887     Super Store Bamnoli F&V CPC (SSC)                 1485   \n", "\n", "                                 receiver_outlet_name  billed_quantity  \\\n", "0              Super Store Delhi Dwarka ES14 PR (FNV)                5   \n", "1              Super Store Delhi Dwarka ES14 PR (FNV)                3   \n", "2                      SS Gurgaon Sector 10 ES56 (NM)               14   \n", "3             Super Store Gurgaon Sector 62 ES24 (NM)                4   \n", "4                    SS Gurgaon Gwal Pahari ES31 (NM)                4   \n", "...                                               ...              ...   \n", "17883  Super Store Faridabad Arjun Vatika ES6 PR (NM)                2   \n", "17884  Super Store Faridabad Arjun Vatika ES6 PR (NM)                1   \n", "17885  Super Store UPNCR Noida Sector 45 ES20 PR (CC)                4   \n", "17886                SS Delhi Madangir ES160 PR (FNV)                5   \n", "17887          Super Store Delhi Dwarka ES14 PR (FNV)               19   \n", "\n", "      trip_started_at consignment_received_at          sto_created_at  \\\n", "0                 NaT                     NaT 2022-07-25 21:04:15.411   \n", "1                 NaT                     NaT 2022-07-25 21:04:15.411   \n", "2                 NaT                     NaT 2022-07-25 22:18:45.581   \n", "3                 NaT                     NaT 2022-07-25 22:19:01.016   \n", "4                 NaT                     NaT 2022-07-25 21:04:34.487   \n", "...               ...                     ...                     ...   \n", "17883             NaT                     NaT 2022-07-25 21:04:07.111   \n", "17884             NaT                     NaT 2022-07-25 21:04:07.111   \n", "17885             NaT                     NaT 2022-07-25 23:47:52.960   \n", "17886             NaT                     NaT 2022-07-25 22:36:57.442   \n", "17887             NaT                     NaT 2022-07-26 00:49:47.327   \n", "\n", "                sto_billed_at sto_dispatched_at          grn_created_at  \\\n", "0     2022-07-26 00:58:02.423              None                     NaT   \n", "1     2022-07-26 00:58:02.423              None                     NaT   \n", "2     2022-07-26 02:05:58.616              None 2022-07-26 05:10:02.130   \n", "3                         NaT              None 2022-07-26 06:10:58.573   \n", "4     2022-07-26 00:49:33.571              None 2022-07-26 06:01:07.000   \n", "...                       ...               ...                     ...   \n", "17883 2022-07-26 00:51:27.900              None                     NaT   \n", "17884 2022-07-26 00:51:27.900              None                     NaT   \n", "17885 2022-07-26 02:10:21.704              None 2022-07-26 06:38:02.742   \n", "17886 2022-07-26 01:40:50.426              None 2022-07-26 05:33:32.362   \n", "17887 2022-07-26 01:30:26.287              None                     NaT   \n", "\n", "       inward_quantity  \n", "0                    0  \n", "1                    0  \n", "2                   14  \n", "3                    4  \n", "4                    4  \n", "...                ...  \n", "17883                2  \n", "17884                0  \n", "17885                4  \n", "17886                5  \n", "17887                0  \n", "\n", "[17888 rows x 14 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["def STO_base_2_all ():\n", "    sql = f\"\"\"select sto_id, item_id, sender_outlet_id,sender_outlet_name,receiving_outlet_id, receiver_outlet_name, billed_quantity,trip_started_at,consignment_received_at,\n", "             max(sto_created_at) as sto_created_at,max(sto_billed_at) as sto_billed_at, max(sto_dispatched_at) as sto_dispatched_at,max(grn_created_at) as grn_created_at, sum(inward_quantity) as inward_quantity\n", "             from metrics.esto_details es\n", "             left join lake_retail.console_outlet co on co.id = es.sender_outlet_id\n", "             where item_id in (select distinct item_id from lake_rpc.item_details where active= 1 and approved =1 and perishable =1)\n", "             and sender_outlet_id in (2666,3229,2672,2472,1280,1674,2925)\n", "             and receiving_outlet_id not in (2340)\n", "             and sto_created_at between '{start_date}' || ' 20:00:00' and '{end_date}' || ' 02:00:00'\n", "             group by 1,2,3,4,5,6,7,8,9\n", "        \"\"\"\n", "    return(pd.read_sql_query(sql=sql, con=CON_REDSHIFT))\n", "\n", "df_base_list_transit= STO_base_2_all()\n", "df_base_list_transit"]}, {"cell_type": "code", "execution_count": 6, "id": "1db3d640-bd44-4b2d-ab27-eb0dab1f0f0a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sender_outlet_id</th>\n", "      <th>receiving_outlet_id</th>\n", "      <th>trip_started_at</th>\n", "      <th>consignment_received_at</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2672</td>\n", "      <td>2836</td>\n", "      <td>2022-07-26 03:03:34.939</td>\n", "      <td>2022-07-26 05:54:38.943</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>3229</td>\n", "      <td>2856</td>\n", "      <td>2022-07-26 01:40:30.819</td>\n", "      <td>2022-07-26 04:59:14.042</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>1674</td>\n", "      <td>2909</td>\n", "      <td>2022-07-26 02:39:48.617</td>\n", "      <td>2022-07-26 04:24:48.983</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>3229</td>\n", "      <td>2858</td>\n", "      <td>2022-07-26 01:00:10.700</td>\n", "      <td>2022-07-26 05:20:53.526</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>2472</td>\n", "      <td>2693</td>\n", "      <td>2022-07-26 03:38:20.340</td>\n", "      <td>2022-07-26 04:58:50.326</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16122</th>\n", "      <td>2472</td>\n", "      <td>3448</td>\n", "      <td>2022-07-26 02:02:29.626</td>\n", "      <td>2022-07-26 02:42:40.505</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16594</th>\n", "      <td>2472</td>\n", "      <td>3253</td>\n", "      <td>2022-07-26 01:36:50.646</td>\n", "      <td>NaT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16668</th>\n", "      <td>2472</td>\n", "      <td>2552</td>\n", "      <td>2022-07-26 02:41:56.751</td>\n", "      <td>2022-07-26 05:08:11.706</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16686</th>\n", "      <td>1674</td>\n", "      <td>2823</td>\n", "      <td>2022-07-26 02:40:52.277</td>\n", "      <td>2022-07-26 04:29:57.606</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17305</th>\n", "      <td>2925</td>\n", "      <td>2887</td>\n", "      <td>2022-07-26 02:55:52.530</td>\n", "      <td>2022-07-26 03:53:02.641</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>183 rows × 4 columns</p>\n", "</div>"], "text/plain": ["       sender_outlet_id  receiving_outlet_id         trip_started_at  \\\n", "6                  2672                 2836 2022-07-26 03:03:34.939   \n", "13                 3229                 2856 2022-07-26 01:40:30.819   \n", "16                 1674                 2909 2022-07-26 02:39:48.617   \n", "18                 3229                 2858 2022-07-26 01:00:10.700   \n", "22                 2472                 2693 2022-07-26 03:38:20.340   \n", "...                 ...                  ...                     ...   \n", "16122              2472                 3448 2022-07-26 02:02:29.626   \n", "16594              2472                 3253 2022-07-26 01:36:50.646   \n", "16668              2472                 2552 2022-07-26 02:41:56.751   \n", "16686              1674                 2823 2022-07-26 02:40:52.277   \n", "17305              2925                 2887 2022-07-26 02:55:52.530   \n", "\n", "      consignment_received_at  \n", "6     2022-07-26 05:54:38.943  \n", "13    2022-07-26 04:59:14.042  \n", "16    2022-07-26 04:24:48.983  \n", "18    2022-07-26 05:20:53.526  \n", "22    2022-07-26 04:58:50.326  \n", "...                       ...  \n", "16122 2022-07-26 02:42:40.505  \n", "16594                     NaT  \n", "16668 2022-07-26 05:08:11.706  \n", "16686 2022-07-26 04:29:57.606  \n", "17305 2022-07-26 03:53:02.641  \n", "\n", "[183 rows x 4 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df_base_arrival = df_base_list_transit[['sender_outlet_id','receiving_outlet_id','trip_started_at','consignment_received_at']]\n", "df_base_arrival = df_base_arrival[df_base_arrival['trip_started_at'].isna() == False]\n", "df_base_arrival = df_base_arrival.drop_duplicates()\n", "df_base_arrival"]}, {"cell_type": "code", "execution_count": 7, "id": "11c24ecf-37fd-4387-9c03-6eb813466bfc", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sender_outlet_id</th>\n", "      <th>receiving_outlet_id</th>\n", "      <th>grn_created_at</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3229</td>\n", "      <td>2866</td>\n", "      <td>2022-07-26 05:11:12.873</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3229</td>\n", "      <td>2860</td>\n", "      <td>2022-07-26 05:33:56.076</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3229</td>\n", "      <td>2884</td>\n", "      <td>2022-07-26 05:34:16.051</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2672</td>\n", "      <td>4094</td>\n", "      <td>2022-07-26 05:46:28.065</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2672</td>\n", "      <td>2413</td>\n", "      <td>2022-07-26 05:12:34.077</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>347</th>\n", "      <td>2472</td>\n", "      <td>3232</td>\n", "      <td>2022-07-26 06:08:20.090</td>\n", "    </tr>\n", "    <tr>\n", "      <th>348</th>\n", "      <td>2472</td>\n", "      <td>3231</td>\n", "      <td>2022-07-26 05:30:02.503</td>\n", "    </tr>\n", "    <tr>\n", "      <th>349</th>\n", "      <td>2472</td>\n", "      <td>2549</td>\n", "      <td>2022-07-26 05:00:49.296</td>\n", "    </tr>\n", "    <tr>\n", "      <th>350</th>\n", "      <td>2472</td>\n", "      <td>3531</td>\n", "      <td>2022-07-26 05:39:57.044</td>\n", "    </tr>\n", "    <tr>\n", "      <th>351</th>\n", "      <td>2472</td>\n", "      <td>2554</td>\n", "      <td>2022-07-26 05:33:37.687</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>352 rows × 3 columns</p>\n", "</div>"], "text/plain": ["     sender_outlet_id  receiving_outlet_id          grn_created_at\n", "0                3229                 2866 2022-07-26 05:11:12.873\n", "1                3229                 2860 2022-07-26 05:33:56.076\n", "2                3229                 2884 2022-07-26 05:34:16.051\n", "3                2672                 4094 2022-07-26 05:46:28.065\n", "4                2672                 2413 2022-07-26 05:12:34.077\n", "..                ...                  ...                     ...\n", "347              2472                 3232 2022-07-26 06:08:20.090\n", "348              2472                 3231 2022-07-26 05:30:02.503\n", "349              2472                 2549 2022-07-26 05:00:49.296\n", "350              2472                 3531 2022-07-26 05:39:57.044\n", "351              2472                 2554 2022-07-26 05:33:37.687\n", "\n", "[352 rows x 3 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df_grn_time = df_base_list[['sender_outlet_id','receiving_outlet_id','grn_created_at']]\n", "df_grn_time = df_grn_time[df_grn_time['grn_created_at'].isna() == False]\n", "df_grn_time = df_grn_time.drop_duplicates().reset_index()\n", "df_grn_time = df_grn_time[['sender_outlet_id','receiving_outlet_id','grn_created_at']]\n", "df_grn_time"]}, {"cell_type": "code", "execution_count": 8, "id": "09ab70c4-0ce2-4936-a5d6-91bf3a6df0b1", "metadata": {}, "outputs": [], "source": ["df_grn_agg_min=df_grn_time.groupby(['receiving_outlet_id']).min().reset_index()\n", "df_grn_agg_min = df_grn_agg_min.rename(columns = {'grn_created_at':'Grn_start_time'})\n", "df_grn_agg_max=df_grn_time.groupby(['receiving_outlet_id']).max().reset_index()\n", "df_grn_agg_max = df_grn_agg_max.rename(columns = {'grn_created_at':'Grn_end_time'})"]}, {"cell_type": "code", "execution_count": 9, "id": "080c4636-e145-43f8-93d5-bb473f44e156", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>receiving_outlet_id</th>\n", "      <th>sender_outlet_id</th>\n", "      <th>Grn_start_time</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>976</td>\n", "      <td>2925</td>\n", "      <td>2022-07-26 05:43:58.615</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>995</td>\n", "      <td>1674</td>\n", "      <td>2022-07-26 05:03:40.078</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1010</td>\n", "      <td>1674</td>\n", "      <td>2022-07-26 06:04:45.764</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1024</td>\n", "      <td>2472</td>\n", "      <td>2022-07-26 05:26:28.874</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1122</td>\n", "      <td>1674</td>\n", "      <td>2022-07-26 05:54:15.863</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   receiving_outlet_id  sender_outlet_id          Grn_start_time\n", "0                  976              2925 2022-07-26 05:43:58.615\n", "1                  995              1674 2022-07-26 05:03:40.078\n", "2                 1010              1674 2022-07-26 06:04:45.764\n", "3                 1024              2472 2022-07-26 05:26:28.874\n", "4                 1122              1674 2022-07-26 05:54:15.863"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df_grn_agg_min.head()"]}, {"cell_type": "code", "execution_count": 10, "id": "2e48bcfd-e53c-4699-8427-7d5ce3ae2a53", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>receiving_outlet_id</th>\n", "      <th>sender_outlet_id</th>\n", "      <th>Grn_end_time</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>976</td>\n", "      <td>2925</td>\n", "      <td>2022-07-26 05:44:57.472</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>995</td>\n", "      <td>1674</td>\n", "      <td>2022-07-26 05:04:27.625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1010</td>\n", "      <td>1674</td>\n", "      <td>2022-07-26 06:05:21.592</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1024</td>\n", "      <td>2472</td>\n", "      <td>2022-07-26 05:26:28.874</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1122</td>\n", "      <td>1674</td>\n", "      <td>2022-07-26 05:55:15.629</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   receiving_outlet_id  sender_outlet_id            Grn_end_time\n", "0                  976              2925 2022-07-26 05:44:57.472\n", "1                  995              1674 2022-07-26 05:04:27.625\n", "2                 1010              1674 2022-07-26 06:05:21.592\n", "3                 1024              2472 2022-07-26 05:26:28.874\n", "4                 1122              1674 2022-07-26 05:55:15.629"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df_grn_agg_max.head()"]}, {"cell_type": "code", "execution_count": 11, "id": "bdab60d4-1f82-4517-81d4-3d0163654e63", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(205, 4)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sender_outlet_id</th>\n", "      <th>sender_outlet_name</th>\n", "      <th>receiving_outlet_id</th>\n", "      <th>receiver_outlet_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2866</td>\n", "      <td>SS Panchkula Industrial Area ES1 (Crof)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2860</td>\n", "      <td>SS Chandigarh Sector 34 Allen ES2 (Crof)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2884</td>\n", "      <td>SS Zirakpur Golden Square ES1 (Crof)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2672</td>\n", "      <td>Super Store Lucknow F&amp;V CPC (SSC)</td>\n", "      <td>4094</td>\n", "      <td>SS Kanpur Ghantaghar ES7 PR (CC)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2672</td>\n", "      <td>Super Store Lucknow F&amp;V CPC (SSC)</td>\n", "      <td>2413</td>\n", "      <td>SS Kanpur Arya Nagar ES3 PR (CC)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   sender_outlet_id                 sender_outlet_name  receiving_outlet_id  \\\n", "0              3229           SS Mohali F&V CPC (MODI)                 2866   \n", "1              3229           SS Mohali F&V CPC (MODI)                 2860   \n", "2              3229           SS Mohali F&V CPC (MODI)                 2884   \n", "3              2672  Super Store Lucknow F&V CPC (SSC)                 4094   \n", "4              2672  Super Store Lucknow F&V CPC (SSC)                 2413   \n", "\n", "                       receiver_outlet_name  \n", "0   SS Panchkula Industrial Area ES1 (Crof)  \n", "1  SS Chandigarh Sector 34 Allen ES2 (Crof)  \n", "2      SS Zirakpur Golden Square ES1 (Crof)  \n", "3          SS Kanpur Ghantaghar ES7 PR (CC)  \n", "4          SS Kanpur Arya Nagar ES3 PR (CC)  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["df_base_aana_jaana = df_base_list[['sender_outlet_id','sender_outlet_name','receiving_outlet_id','receiver_outlet_name']]\n", "df_base_aana_jaana = df_base_aana_jaana.drop_duplicates().reset_index().drop(columns = {'index'})\n", "print(df_base_aana_jaana.shape)\n", "df_base_aana_jaana.head()"]}, {"cell_type": "code", "execution_count": 12, "id": "443d980f-0ec0-413c-8e65-8797400f8fd9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(205, 6)\n"]}], "source": ["df_base_alert = pd.merge(df_base_aana_jaana, df_base_arrival,  how='left', on=[\"sender_outlet_id\",\"receiving_outlet_id\"])\n", "print(df_base_alert.shape)\n", "df_base_alert = pd.merge(df_base_alert, df_grn_agg_min,  how='left', on=[\"sender_outlet_id\",\"receiving_outlet_id\"])\n", "df_base_alert = pd.merge(df_base_alert, df_grn_agg_max,  how='left', on=[\"sender_outlet_id\",\"receiving_outlet_id\"])\n", "df_base_alert.to_csv('hi.csv')"]}, {"cell_type": "code", "execution_count": 13, "id": "4a30e43f-c76f-4b64-a6f2-df5d95194787", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sender_outlet_id</th>\n", "      <th>sender_outlet_name</th>\n", "      <th>receiving_outlet_id</th>\n", "      <th>receiver_outlet_name</th>\n", "      <th>trip_started_at</th>\n", "      <th>consignment_received_at</th>\n", "      <th>Grn_start_time</th>\n", "      <th>Grn_end_time</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2866</td>\n", "      <td>SS Panchkula Industrial Area ES1 (Crof)</td>\n", "      <td>2022-07-26 03:26:31.267</td>\n", "      <td>2022-07-26 05:06:19.450</td>\n", "      <td>2022-07-26 05:11:12.873</td>\n", "      <td>2022-07-26 05:12:42.931</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2860</td>\n", "      <td>SS Chandigarh Sector 34 Allen ES2 (Crof)</td>\n", "      <td>2022-07-26 04:13:00.517</td>\n", "      <td>2022-07-26 04:57:53.730</td>\n", "      <td>2022-07-26 05:33:56.076</td>\n", "      <td>2022-07-26 05:34:38.053</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2884</td>\n", "      <td>SS Zirakpur Golden Square ES1 (Crof)</td>\n", "      <td>2022-07-26 03:59:23.714</td>\n", "      <td>2022-07-26 05:02:29.406</td>\n", "      <td>2022-07-26 05:34:16.051</td>\n", "      <td>2022-07-26 05:35:10.090</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2672</td>\n", "      <td>Super Store Lucknow F&amp;V CPC (SSC)</td>\n", "      <td>4094</td>\n", "      <td>SS Kanpur Ghantaghar ES7 PR (CC)</td>\n", "      <td>2022-07-26 02:50:16.522</td>\n", "      <td>2022-07-26 05:38:13.138</td>\n", "      <td>2022-07-26 05:46:28.065</td>\n", "      <td>2022-07-26 05:46:28.065</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2672</td>\n", "      <td>Super Store Lucknow F&amp;V CPC (SSC)</td>\n", "      <td>2413</td>\n", "      <td>SS Kanpur Arya Nagar ES3 PR (CC)</td>\n", "      <td>2022-07-26 03:09:42.626</td>\n", "      <td>2022-07-26 04:54:07.007</td>\n", "      <td>2022-07-26 05:12:34.077</td>\n", "      <td>2022-07-26 05:12:34.077</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>1485</td>\n", "      <td>Super Store Delhi Dwarka ES14 PR (FNV)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:52:40.564</td>\n", "      <td>2022-07-26 05:53:40.242</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2868</td>\n", "      <td>SS Delhi Prahladpur ES79 (FNV)</td>\n", "      <td>2022-07-26 02:41:22.489</td>\n", "      <td>2022-07-26 05:25:07.143</td>\n", "      <td>2022-07-26 05:48:18.858</td>\n", "      <td>2022-07-26 05:48:18.858</td>\n", "    </tr>\n", "    <tr>\n", "      <th>202</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>1024</td>\n", "      <td>Super Store Delhi Jhilmil ES1 (FNV)</td>\n", "      <td>2022-07-26 03:14:20.216</td>\n", "      <td>2022-07-26 05:26:29.842</td>\n", "      <td>2022-07-26 05:26:28.874</td>\n", "      <td>2022-07-26 05:26:28.874</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2549</td>\n", "      <td>Super Store Delhi Defence Colony ES71 (FNV)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:00:49.296</td>\n", "      <td>2022-07-26 05:00:49.296</td>\n", "    </tr>\n", "    <tr>\n", "      <th>204</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2554</td>\n", "      <td>Super Store Delhi Panchsheel Vihar ES64 (FNV)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:33:37.687</td>\n", "      <td>2022-07-26 05:33:37.687</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>205 rows × 8 columns</p>\n", "</div>"], "text/plain": ["     sender_outlet_id                 sender_outlet_name  receiving_outlet_id  \\\n", "0                3229           SS Mohali F&V CPC (MODI)                 2866   \n", "1                3229           SS Mohali F&V CPC (MODI)                 2860   \n", "2                3229           SS Mohali F&V CPC (MODI)                 2884   \n", "3                2672  Super Store Lucknow F&V CPC (SSC)                 4094   \n", "4                2672  Super Store Lucknow F&V CPC (SSC)                 2413   \n", "..                ...                                ...                  ...   \n", "200              2472  Super Store Bamnoli F&V CPC (SSC)                 1485   \n", "201              2472  Super Store Bamnoli F&V CPC (SSC)                 2868   \n", "202              2472  Super Store Bamnoli F&V CPC (SSC)                 1024   \n", "203              2472  Super Store Bamnoli F&V CPC (SSC)                 2549   \n", "204              2472  Super Store Bamnoli F&V CPC (SSC)                 2554   \n", "\n", "                              receiver_outlet_name         trip_started_at  \\\n", "0          SS Panchkula Industrial Area ES1 (Crof) 2022-07-26 03:26:31.267   \n", "1         SS Chandigarh Sector 34 Allen ES2 (Crof) 2022-07-26 04:13:00.517   \n", "2             SS Zirakpur Golden Square ES1 (Crof) 2022-07-26 03:59:23.714   \n", "3                 SS Kanpur Ghantaghar ES7 PR (CC) 2022-07-26 02:50:16.522   \n", "4                 SS Kanpur Arya Nagar ES3 PR (CC) 2022-07-26 03:09:42.626   \n", "..                                             ...                     ...   \n", "200         Super Store Delhi Dwarka ES14 PR (FNV)                     NaT   \n", "201                 SS Delhi Prahladpur ES79 (FNV) 2022-07-26 02:41:22.489   \n", "202            Super Store Delhi Jhilmil ES1 (FNV) 2022-07-26 03:14:20.216   \n", "203    Super Store Delhi Defence Colony ES71 (FNV)                     NaT   \n", "204  Super Store Delhi Panchsheel Vihar ES64 (FNV)                     NaT   \n", "\n", "    consignment_received_at          Grn_start_time            Grn_end_time  \n", "0   2022-07-26 05:06:19.450 2022-07-26 05:11:12.873 2022-07-26 05:12:42.931  \n", "1   2022-07-26 04:57:53.730 2022-07-26 05:33:56.076 2022-07-26 05:34:38.053  \n", "2   2022-07-26 05:02:29.406 2022-07-26 05:34:16.051 2022-07-26 05:35:10.090  \n", "3   2022-07-26 05:38:13.138 2022-07-26 05:46:28.065 2022-07-26 05:46:28.065  \n", "4   2022-07-26 04:54:07.007 2022-07-26 05:12:34.077 2022-07-26 05:12:34.077  \n", "..                      ...                     ...                     ...  \n", "200                     NaT 2022-07-26 05:52:40.564 2022-07-26 05:53:40.242  \n", "201 2022-07-26 05:25:07.143 2022-07-26 05:48:18.858 2022-07-26 05:48:18.858  \n", "202 2022-07-26 05:26:29.842 2022-07-26 05:26:28.874 2022-07-26 05:26:28.874  \n", "203                     NaT 2022-07-26 05:00:49.296 2022-07-26 05:00:49.296  \n", "204                     NaT 2022-07-26 05:33:37.687 2022-07-26 05:33:37.687  \n", "\n", "[205 rows x 8 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["df_base_alert['Grn_start_time'] = pd.to_datetime(df_base_alert['Grn_start_time'])\n", "df_base_alert['Grn_end_time'] = pd.to_datetime(df_base_alert['Grn_end_time'])\n", "\n", "df_base_alert"]}, {"cell_type": "code", "execution_count": 14, "id": "b2cd11cb-75a4-482f-a92e-251e6a3a8a41", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sto_id</th>\n", "      <th>item_id</th>\n", "      <th>sender_outlet_id</th>\n", "      <th>sender_outlet_name</th>\n", "      <th>receiving_outlet_id</th>\n", "      <th>receiver_outlet_name</th>\n", "      <th>billed_quantity</th>\n", "      <th>inward_quantity</th>\n", "      <th>grn_created_at</th>\n", "      <th>hour</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5130878</td>\n", "      <td>10005407</td>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2866</td>\n", "      <td>SS Panchkula Industrial Area ES1 (Crof)</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>2022-07-26 05:11:12.873</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5130878</td>\n", "      <td>10043867</td>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2866</td>\n", "      <td>SS Panchkula Industrial Area ES1 (Crof)</td>\n", "      <td>43</td>\n", "      <td>43</td>\n", "      <td>2022-07-26 05:11:12.873</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5130878</td>\n", "      <td>10043865</td>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2866</td>\n", "      <td>SS Panchkula Industrial Area ES1 (Crof)</td>\n", "      <td>16</td>\n", "      <td>16</td>\n", "      <td>2022-07-26 05:11:12.873</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>5130878</td>\n", "      <td>10032019</td>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2866</td>\n", "      <td>SS Panchkula Industrial Area ES1 (Crof)</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>2022-07-26 05:11:12.873</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5130878</td>\n", "      <td>10028965</td>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2866</td>\n", "      <td>SS Panchkula Industrial Area ES1 (Crof)</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>2022-07-26 05:11:12.873</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13345</th>\n", "      <td>5131323</td>\n", "      <td>10098520</td>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2314</td>\n", "      <td>Super Store Delhi Ashram ES24 PR (FNV)</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>2022-07-26 05:50:20.913</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13346</th>\n", "      <td>5131323</td>\n", "      <td>10098509</td>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2314</td>\n", "      <td>Super Store Delhi Ashram ES24 PR (FNV)</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>2022-07-26 05:50:20.913</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13347</th>\n", "      <td>5131323</td>\n", "      <td>10098514</td>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2314</td>\n", "      <td>Super Store Delhi Ashram ES24 PR (FNV)</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>2022-07-26 05:50:20.913</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13348</th>\n", "      <td>5131323</td>\n", "      <td>10002268</td>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2314</td>\n", "      <td>Super Store Delhi Ashram ES24 PR (FNV)</td>\n", "      <td>24</td>\n", "      <td>24</td>\n", "      <td>2022-07-26 05:50:20.913</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13349</th>\n", "      <td>5131323</td>\n", "      <td>10002241</td>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2314</td>\n", "      <td>Super Store Delhi Ashram ES24 PR (FNV)</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>2022-07-26 05:50:20.913</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>13350 rows × 10 columns</p>\n", "</div>"], "text/plain": ["        sto_id   item_id  sender_outlet_id                 sender_outlet_name  \\\n", "0      5130878  10005407              3229           SS Mohali F&V CPC (MODI)   \n", "1      5130878  10043867              3229           SS Mohali F&V CPC (MODI)   \n", "2      5130878  10043865              3229           SS Mohali F&V CPC (MODI)   \n", "3      5130878  10032019              3229           SS Mohali F&V CPC (MODI)   \n", "4      5130878  10028965              3229           SS Mohali F&V CPC (MODI)   \n", "...        ...       ...               ...                                ...   \n", "13345  5131323  10098520              2472  Super Store Bamnoli F&V CPC (SSC)   \n", "13346  5131323  10098509              2472  Super Store Bamnoli F&V CPC (SSC)   \n", "13347  5131323  10098514              2472  Super Store Bamnoli F&V CPC (SSC)   \n", "13348  5131323  10002268              2472  Super Store Bamnoli F&V CPC (SSC)   \n", "13349  5131323  10002241              2472  Super Store Bamnoli F&V CPC (SSC)   \n", "\n", "       receiving_outlet_id                     receiver_outlet_name  \\\n", "0                     2866  SS Panchkula Industrial Area ES1 (Crof)   \n", "1                     2866  SS Panchkula Industrial Area ES1 (Crof)   \n", "2                     2866  SS Panchkula Industrial Area ES1 (Crof)   \n", "3                     2866  SS Panchkula Industrial Area ES1 (Crof)   \n", "4                     2866  SS Panchkula Industrial Area ES1 (Crof)   \n", "...                    ...                                      ...   \n", "13345                 2314   Super Store Delhi Ashram ES24 PR (FNV)   \n", "13346                 2314   Super Store Delhi Ashram ES24 PR (FNV)   \n", "13347                 2314   Super Store Delhi Ashram ES24 PR (FNV)   \n", "13348                 2314   Super Store Delhi Ashram ES24 PR (FNV)   \n", "13349                 2314   Super Store Delhi Ashram ES24 PR (FNV)   \n", "\n", "       billed_quantity  inward_quantity          grn_created_at  hour  \n", "0                    5                5 2022-07-26 05:11:12.873   5.0  \n", "1                   43               43 2022-07-26 05:11:12.873   5.0  \n", "2                   16               16 2022-07-26 05:11:12.873   5.0  \n", "3                    5                5 2022-07-26 05:11:12.873   5.0  \n", "4                    3                3 2022-07-26 05:11:12.873   5.0  \n", "...                ...              ...                     ...   ...  \n", "13345                3                3 2022-07-26 05:50:20.913   5.0  \n", "13346                3                3 2022-07-26 05:50:20.913   5.0  \n", "13347                3                3 2022-07-26 05:50:20.913   5.0  \n", "13348               24               24 2022-07-26 05:50:20.913   5.0  \n", "13349                6                6 2022-07-26 05:50:20.913   5.0  \n", "\n", "[13350 rows x 10 columns]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["df_summary = df_base_list[['sto_id','item_id','sender_outlet_id','sender_outlet_name','receiving_outlet_id','receiver_outlet_name',\n", "                             'billed_quantity','inward_quantity','grn_created_at']]\n", "\n", "df_summary['hour'] = df_summary['grn_created_at'].dt.hour\n", "df_summary"]}, {"cell_type": "code", "execution_count": 15, "id": "5a78ec50-3bac-47aa-8633-5e70d448f13e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sender_outlet_id</th>\n", "      <th>sender_outlet_name</th>\n", "      <th>receiving_outlet_id</th>\n", "      <th>receiver_outlet_name</th>\n", "      <th>billed_quantity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>1473</td>\n", "      <td>Super Store Delhi Prahlad Vihar ES8 PR (FNV)</td>\n", "      <td>948</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>1477</td>\n", "      <td>Super Store Delhi Shalimar Bagh ES10 PR (FNV)</td>\n", "      <td>603</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>1495</td>\n", "      <td>Super Store Delhi Badli ES19 (FNV)</td>\n", "      <td>655</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>2307</td>\n", "      <td>Super Store Delhi Paschim Vihar ES32 PR (FNV)</td>\n", "      <td>1086</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>2447</td>\n", "      <td>Super Store Delhi Prashant Vihar ES63 (FNV)</td>\n", "      <td>965</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2880</td>\n", "      <td>SS Ludhiana Model Town ES3 (Crof)</td>\n", "      <td>523</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2882</td>\n", "      <td>SS Chandigarh Dhanas Sector 25 ES3 (Crof)</td>\n", "      <td>377</td>\n", "    </tr>\n", "    <tr>\n", "      <th>202</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2884</td>\n", "      <td>SS Zirakpur Golden Square ES1 (Crof)</td>\n", "      <td>878</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2921</td>\n", "      <td>SS Jalandhar Baradari ES2 (Crof)</td>\n", "      <td>569</td>\n", "    </tr>\n", "    <tr>\n", "      <th>204</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>3877</td>\n", "      <td>SS Chandigarh Sector 17 ES4 (Crof)</td>\n", "      <td>456</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>205 rows × 5 columns</p>\n", "</div>"], "text/plain": ["     sender_outlet_id                 sender_outlet_name  receiving_outlet_id  \\\n", "0                1280  Super Store Budhpur CPC F&V (SSC)                 1473   \n", "1                1280  Super Store Budhpur CPC F&V (SSC)                 1477   \n", "2                1280  Super Store Budhpur CPC F&V (SSC)                 1495   \n", "3                1280  Super Store Budhpur CPC F&V (SSC)                 2307   \n", "4                1280  Super Store Budhpur CPC F&V (SSC)                 2447   \n", "..                ...                                ...                  ...   \n", "200              3229           SS Mohali F&V CPC (MODI)                 2880   \n", "201              3229           SS Mohali F&V CPC (MODI)                 2882   \n", "202              3229           SS Mohali F&V CPC (MODI)                 2884   \n", "203              3229           SS Mohali F&V CPC (MODI)                 2921   \n", "204              3229           SS Mohali F&V CPC (MODI)                 3877   \n", "\n", "                              receiver_outlet_name  billed_quantity  \n", "0     Super Store Delhi Prahlad Vihar ES8 PR (FNV)              948  \n", "1    Super Store Delhi Shalimar Bagh ES10 PR (FNV)              603  \n", "2               Super Store Delhi Badli ES19 (FNV)              655  \n", "3    Super Store Delhi Paschim Vihar ES32 PR (FNV)             1086  \n", "4      Super Store Delhi Prashant Vihar ES63 (FNV)              965  \n", "..                                             ...              ...  \n", "200              SS Ludhiana Model Town ES3 (Crof)              523  \n", "201      SS Chandigarh Dhanas Sector 25 ES3 (Crof)              377  \n", "202           SS Zirakpur Golden Square ES1 (Crof)              878  \n", "203               SS Jalandhar Baradari ES2 (Crof)              569  \n", "204             SS Chandigarh Sector 17 ES4 (Crof)              456  \n", "\n", "[205 rows x 5 columns]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["df_summary_main = df_summary.groupby(['sender_outlet_id','sender_outlet_name','receiving_outlet_id','receiver_outlet_name']).agg({'billed_quantity':'sum'}).reset_index()\n", "df_summary_main"]}, {"cell_type": "code", "execution_count": 16, "id": "93a926b4-3f95-4a5e-a552-e63909a54132", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sender_outlet_id</th>\n", "      <th>receiving_outlet_id</th>\n", "      <th>Before_6_GRN</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1280</td>\n", "      <td>1473</td>\n", "      <td>948</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1280</td>\n", "      <td>1477</td>\n", "      <td>603</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1280</td>\n", "      <td>1495</td>\n", "      <td>655</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1280</td>\n", "      <td>2307</td>\n", "      <td>1086</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1280</td>\n", "      <td>2447</td>\n", "      <td>965</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>161</th>\n", "      <td>3229</td>\n", "      <td>2866</td>\n", "      <td>430</td>\n", "    </tr>\n", "    <tr>\n", "      <th>162</th>\n", "      <td>3229</td>\n", "      <td>2878</td>\n", "      <td>507</td>\n", "    </tr>\n", "    <tr>\n", "      <th>163</th>\n", "      <td>3229</td>\n", "      <td>2882</td>\n", "      <td>377</td>\n", "    </tr>\n", "    <tr>\n", "      <th>164</th>\n", "      <td>3229</td>\n", "      <td>2884</td>\n", "      <td>878</td>\n", "    </tr>\n", "    <tr>\n", "      <th>165</th>\n", "      <td>3229</td>\n", "      <td>3877</td>\n", "      <td>456</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>166 rows × 3 columns</p>\n", "</div>"], "text/plain": ["     sender_outlet_id  receiving_outlet_id  Before_6_GRN\n", "0                1280                 1473           948\n", "1                1280                 1477           603\n", "2                1280                 1495           655\n", "3                1280                 2307          1086\n", "4                1280                 2447           965\n", "..                ...                  ...           ...\n", "161              3229                 2866           430\n", "162              3229                 2878           507\n", "163              3229                 2882           377\n", "164              3229                 2884           878\n", "165              3229                 3877           456\n", "\n", "[166 rows x 3 columns]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["df_summary_6 = df_summary[df_summary['grn_created_at'].isna() == False]\n", "df_summary_6 = df_summary[df_summary['hour'] < 6 ]\n", "df_summary_6 = df_summary_6.groupby(['sender_outlet_id','receiving_outlet_id']).agg({'inward_quantity':'sum'}).reset_index()\n", "df_summary_6 = df_summary_6.rename(columns = {'inward_quantity':'Before_6_GRN'})\n", "df_summary_6\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 17, "id": "897159d0-b22c-4153-8ee4-9481df3f6e21", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sender_outlet_id</th>\n", "      <th>receiving_outlet_id</th>\n", "      <th>Before_7_GRN</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1280</td>\n", "      <td>1473</td>\n", "      <td>948</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1280</td>\n", "      <td>1477</td>\n", "      <td>603</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1280</td>\n", "      <td>1495</td>\n", "      <td>655</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1280</td>\n", "      <td>2307</td>\n", "      <td>1086</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1280</td>\n", "      <td>2447</td>\n", "      <td>965</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>189</th>\n", "      <td>3229</td>\n", "      <td>2878</td>\n", "      <td>507</td>\n", "    </tr>\n", "    <tr>\n", "      <th>190</th>\n", "      <td>3229</td>\n", "      <td>2880</td>\n", "      <td>523</td>\n", "    </tr>\n", "    <tr>\n", "      <th>191</th>\n", "      <td>3229</td>\n", "      <td>2882</td>\n", "      <td>377</td>\n", "    </tr>\n", "    <tr>\n", "      <th>192</th>\n", "      <td>3229</td>\n", "      <td>2884</td>\n", "      <td>878</td>\n", "    </tr>\n", "    <tr>\n", "      <th>193</th>\n", "      <td>3229</td>\n", "      <td>3877</td>\n", "      <td>456</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>194 rows × 3 columns</p>\n", "</div>"], "text/plain": ["     sender_outlet_id  receiving_outlet_id  Before_7_GRN\n", "0                1280                 1473           948\n", "1                1280                 1477           603\n", "2                1280                 1495           655\n", "3                1280                 2307          1086\n", "4                1280                 2447           965\n", "..                ...                  ...           ...\n", "189              3229                 2878           507\n", "190              3229                 2880           523\n", "191              3229                 2882           377\n", "192              3229                 2884           878\n", "193              3229                 3877           456\n", "\n", "[194 rows x 3 columns]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["df_summary_7 = df_summary[df_summary['grn_created_at'].isna() == False]\n", "df_summary_7 = df_summary[df_summary['hour'] < 7 ]\n", "df_summary_7 = df_summary_7.groupby(['sender_outlet_id','receiving_outlet_id']).agg({'inward_quantity':'sum'}).reset_index()\n", "df_summary_7 = df_summary_7.rename(columns = {'inward_quantity':'Before_7_GRN'})\n", "df_summary_7"]}, {"cell_type": "code", "execution_count": 18, "id": "2a611cc9-5cc8-448d-8fb0-82a9dc9495f8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sender_outlet_id</th>\n", "      <th>receiving_outlet_id</th>\n", "      <th>Before_8_GRN</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1280</td>\n", "      <td>1473</td>\n", "      <td>948</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1280</td>\n", "      <td>1477</td>\n", "      <td>603</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1280</td>\n", "      <td>1495</td>\n", "      <td>655</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1280</td>\n", "      <td>2307</td>\n", "      <td>1086</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1280</td>\n", "      <td>2447</td>\n", "      <td>965</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>189</th>\n", "      <td>3229</td>\n", "      <td>2878</td>\n", "      <td>507</td>\n", "    </tr>\n", "    <tr>\n", "      <th>190</th>\n", "      <td>3229</td>\n", "      <td>2880</td>\n", "      <td>523</td>\n", "    </tr>\n", "    <tr>\n", "      <th>191</th>\n", "      <td>3229</td>\n", "      <td>2882</td>\n", "      <td>377</td>\n", "    </tr>\n", "    <tr>\n", "      <th>192</th>\n", "      <td>3229</td>\n", "      <td>2884</td>\n", "      <td>878</td>\n", "    </tr>\n", "    <tr>\n", "      <th>193</th>\n", "      <td>3229</td>\n", "      <td>3877</td>\n", "      <td>456</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>194 rows × 3 columns</p>\n", "</div>"], "text/plain": ["     sender_outlet_id  receiving_outlet_id  Before_8_GRN\n", "0                1280                 1473           948\n", "1                1280                 1477           603\n", "2                1280                 1495           655\n", "3                1280                 2307          1086\n", "4                1280                 2447           965\n", "..                ...                  ...           ...\n", "189              3229                 2878           507\n", "190              3229                 2880           523\n", "191              3229                 2882           377\n", "192              3229                 2884           878\n", "193              3229                 3877           456\n", "\n", "[194 rows x 3 columns]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["df_summary_8 = df_summary[df_summary['grn_created_at'].isna() == False]\n", "df_summary_8 = df_summary[df_summary['hour'] < 8]\n", "df_summary_8 = df_summary_8.groupby(['sender_outlet_id','receiving_outlet_id']).agg({'inward_quantity':'sum'}).reset_index()\n", "df_summary_8 = df_summary_8.rename(columns = {'inward_quantity':'Before_8_GRN'})\n", "df_summary_8"]}, {"cell_type": "markdown", "id": "ed50b0fd-4445-48f6-aba8-d1537dcc0693", "metadata": {}, "source": ["Total DS that did not Scan"]}, {"cell_type": "code", "execution_count": 19, "id": "a7c15854-bafe-49c5-b385-79b83466d6a1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sender_outlet_id</th>\n", "      <th>Total DS that did not scan</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1280</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2472</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2925</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3229</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   sender_outlet_id  Total DS that did not scan\n", "0              1280                           1\n", "1              2472                          21\n", "2              2925                           2\n", "3              3229                           1"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["df_base_summary = df_base_alert[df_base_alert['trip_started_at'].isna() == True]\n", "df_base_summary = df_base_summary.groupby(['sender_outlet_id']).agg({'receiving_outlet_id':'count'}).reset_index()\n", "df_base_summary = df_base_summary.rename(columns = {'receiving_outlet_id':'Total DS that did not scan'})\n", "df_base_summary\n", "\n"]}, {"cell_type": "markdown", "id": "0b9d0c9e-c1f5-4532-901a-781ac79bc1db", "metadata": {}, "source": ["Total Eligible DS"]}, {"cell_type": "code", "execution_count": 20, "id": "47e4eacb-2f89-485c-a6fe-cea8df4b9559", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sender_outlet_id</th>\n", "      <th>Total DS Trips</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1280</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1674</td>\n", "      <td>43</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2472</td>\n", "      <td>56</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2666</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2672</td>\n", "      <td>26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2925</td>\n", "      <td>36</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3229</td>\n", "      <td>11</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   sender_outlet_id  Total DS Trips\n", "0              1280              21\n", "1              1674              43\n", "2              2472              56\n", "3              2666              12\n", "4              2672              26\n", "5              2925              36\n", "6              3229              11"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["df_base_summary_eligible = df_base_alert.copy()\n", "df_base_summary_eligible = df_base_summary_eligible.groupby(['sender_outlet_id']).agg({'receiving_outlet_id':'count'}).reset_index()\n", "df_base_summary_eligible = df_base_summary_eligible.rename(columns = {'receiving_outlet_id':'Total DS Trips'})\n", "df_base_summary_eligible\n"]}, {"cell_type": "code", "execution_count": 21, "id": "92e46607-cd01-4505-83cf-09930ed28044", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sender_outlet_id</th>\n", "      <th>Total DS</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1280</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1674</td>\n", "      <td>43</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2472</td>\n", "      <td>56</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2666</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2672</td>\n", "      <td>26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2925</td>\n", "      <td>36</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3229</td>\n", "      <td>11</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   sender_outlet_id  Total DS\n", "0              1280        21\n", "1              1674        43\n", "2              2472        56\n", "3              2666        12\n", "4              2672        26\n", "5              2925        36\n", "6              3229        11"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["df_base_summary_truth = df_base_list[['sender_outlet_id','receiving_outlet_id']].drop_duplicates()\n", "df_base_summary_truth = df_base_summary_truth.groupby(['sender_outlet_id']).agg({'receiving_outlet_id':'count'}).reset_index()\n", "df_base_summary_truth = df_base_summary_truth.rename(columns = {'receiving_outlet_id':'Total DS'})\n", "df_base_summary_truth"]}, {"cell_type": "code", "execution_count": 22, "id": "50ca56c1-41e1-4235-a306-370ca6a4ad96", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sender_outlet_id</th>\n", "      <th>sender_outlet_name</th>\n", "      <th>receiving_outlet_id</th>\n", "      <th>receiver_outlet_name</th>\n", "      <th>billed_quantity</th>\n", "      <th>Before_6_GRN</th>\n", "      <th>Before_7_GRN</th>\n", "      <th>Before_8_GRN</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>1473</td>\n", "      <td>Super Store Delhi Prahlad Vihar ES8 PR (FNV)</td>\n", "      <td>948</td>\n", "      <td>948</td>\n", "      <td>948</td>\n", "      <td>948</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>1477</td>\n", "      <td>Super Store Delhi Shalimar Bagh ES10 PR (FNV)</td>\n", "      <td>603</td>\n", "      <td>603</td>\n", "      <td>603</td>\n", "      <td>603</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>1495</td>\n", "      <td>Super Store Delhi Badli ES19 (FNV)</td>\n", "      <td>655</td>\n", "      <td>655</td>\n", "      <td>655</td>\n", "      <td>655</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>2307</td>\n", "      <td>Super Store Delhi Paschim Vihar ES32 PR (FNV)</td>\n", "      <td>1086</td>\n", "      <td>1086</td>\n", "      <td>1086</td>\n", "      <td>1086</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>2447</td>\n", "      <td>Super Store Delhi Prashant Vihar ES63 (FNV)</td>\n", "      <td>965</td>\n", "      <td>965</td>\n", "      <td>965</td>\n", "      <td>965</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2880</td>\n", "      <td>SS Ludhiana Model Town ES3 (Crof)</td>\n", "      <td>523</td>\n", "      <td>0</td>\n", "      <td>523</td>\n", "      <td>523</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2882</td>\n", "      <td>SS Chandigarh Dhanas Sector 25 ES3 (Crof)</td>\n", "      <td>377</td>\n", "      <td>377</td>\n", "      <td>377</td>\n", "      <td>377</td>\n", "    </tr>\n", "    <tr>\n", "      <th>202</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2884</td>\n", "      <td>SS Zirakpur Golden Square ES1 (Crof)</td>\n", "      <td>878</td>\n", "      <td>878</td>\n", "      <td>878</td>\n", "      <td>878</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2921</td>\n", "      <td>SS Jalandhar Baradari ES2 (Crof)</td>\n", "      <td>569</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>204</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>3877</td>\n", "      <td>SS Chandigarh Sector 17 ES4 (Crof)</td>\n", "      <td>456</td>\n", "      <td>456</td>\n", "      <td>456</td>\n", "      <td>456</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>205 rows × 8 columns</p>\n", "</div>"], "text/plain": ["     sender_outlet_id                 sender_outlet_name  receiving_outlet_id  \\\n", "0                1280  Super Store Budhpur CPC F&V (SSC)                 1473   \n", "1                1280  Super Store Budhpur CPC F&V (SSC)                 1477   \n", "2                1280  Super Store Budhpur CPC F&V (SSC)                 1495   \n", "3                1280  Super Store Budhpur CPC F&V (SSC)                 2307   \n", "4                1280  Super Store Budhpur CPC F&V (SSC)                 2447   \n", "..                ...                                ...                  ...   \n", "200              3229           SS Mohali F&V CPC (MODI)                 2880   \n", "201              3229           SS Mohali F&V CPC (MODI)                 2882   \n", "202              3229           SS Mohali F&V CPC (MODI)                 2884   \n", "203              3229           SS Mohali F&V CPC (MODI)                 2921   \n", "204              3229           SS Mohali F&V CPC (MODI)                 3877   \n", "\n", "                              receiver_outlet_name  billed_quantity  \\\n", "0     Super Store Delhi Prahlad Vihar ES8 PR (FNV)              948   \n", "1    Super Store Delhi Shalimar Bagh ES10 PR (FNV)              603   \n", "2               Super Store Delhi Badli ES19 (FNV)              655   \n", "3    Super Store Delhi Paschim Vihar ES32 PR (FNV)             1086   \n", "4      Super Store Delhi Prashant Vihar ES63 (FNV)              965   \n", "..                                             ...              ...   \n", "200              SS Ludhiana Model Town ES3 (Crof)              523   \n", "201      SS Chandigarh Dhanas Sector 25 ES3 (Crof)              377   \n", "202           SS Zirakpur Golden Square ES1 (Crof)              878   \n", "203               SS Jalandhar Baradari ES2 (Crof)              569   \n", "204             SS Chandigarh Sector 17 ES4 (Crof)              456   \n", "\n", "     Before_6_GRN  Before_7_GRN  Before_8_GRN  \n", "0             948           948           948  \n", "1             603           603           603  \n", "2             655           655           655  \n", "3            1086          1086          1086  \n", "4             965           965           965  \n", "..            ...           ...           ...  \n", "200             0           523           523  \n", "201           377           377           377  \n", "202           878           878           878  \n", "203             0             0             0  \n", "204           456           456           456  \n", "\n", "[205 rows x 8 columns]"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["df_publishing = pd.merge(df_summary_main, df_summary_6,  how='left', on=[\"sender_outlet_id\",\"receiving_outlet_id\"])\n", "df_publishing = pd.merge(df_publishing, df_summary_7,  how='left', on=[\"sender_outlet_id\",\"receiving_outlet_id\"])\n", "df_publishing = pd.merge(df_publishing, df_summary_8,  how='left', on=[\"sender_outlet_id\",\"receiving_outlet_id\"])\n", "df_publishing[['Before_6_GRN','Before_7_GRN','Before_8_GRN']] = df_publishing[['Before_6_GRN','Before_7_GRN','Before_8_GRN']].fillna(0)\n", "df_publishing[['Before_6_GRN','Before_7_GRN','Before_8_GRN']] = df_publishing[['Before_6_GRN','Before_7_GRN','Before_8_GRN']].astype(int)\n", "df_publishing\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 23, "id": "7eda2fea-bd07-4ec8-a0b0-fb6676bdb720", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sender_outlet_id</th>\n", "      <th>sender_outlet_name</th>\n", "      <th>Total DS</th>\n", "      <th>Total DS Trips</th>\n", "      <th>Total DS that did not scan</th>\n", "      <th>billed_quantity</th>\n", "      <th>Before_6_GRN</th>\n", "      <th>Before_7_GRN</th>\n", "      <th>Before_8_GRN</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>21</td>\n", "      <td>21</td>\n", "      <td>1.0</td>\n", "      <td>15213</td>\n", "      <td>14372</td>\n", "      <td>14372</td>\n", "      <td>14372</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1674</td>\n", "      <td>Super Store Gurgaon G2 F&amp;V CPC (SSC)</td>\n", "      <td>43</td>\n", "      <td>43</td>\n", "      <td>0.0</td>\n", "      <td>65670</td>\n", "      <td>59492</td>\n", "      <td>64187</td>\n", "      <td>64187</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>56</td>\n", "      <td>56</td>\n", "      <td>21.0</td>\n", "      <td>43099</td>\n", "      <td>39661</td>\n", "      <td>41559</td>\n", "      <td>41559</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2666</td>\n", "      <td>Super Store Jaipur F&amp;V CPC (SSC)</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>0.0</td>\n", "      <td>2923</td>\n", "      <td>2923</td>\n", "      <td>2923</td>\n", "      <td>2923</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2672</td>\n", "      <td>Super Store Lucknow F&amp;V CPC (SSC)</td>\n", "      <td>26</td>\n", "      <td>26</td>\n", "      <td>0.0</td>\n", "      <td>10437</td>\n", "      <td>8043</td>\n", "      <td>10437</td>\n", "      <td>10437</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2925</td>\n", "      <td>Super Store Noida F&amp;V CPC (SSC)</td>\n", "      <td>36</td>\n", "      <td>36</td>\n", "      <td>2.0</td>\n", "      <td>43553</td>\n", "      <td>16677</td>\n", "      <td>36498</td>\n", "      <td>36498</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>11</td>\n", "      <td>11</td>\n", "      <td>1.0</td>\n", "      <td>5744</td>\n", "      <td>4652</td>\n", "      <td>5175</td>\n", "      <td>5175</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   sender_outlet_id                    sender_outlet_name  Total DS  \\\n", "0              1280     Super Store Budhpur CPC F&V (SSC)        21   \n", "1              1674  Super Store Gurgaon G2 F&V CPC (SSC)        43   \n", "2              2472     Super Store Bamnoli F&V CPC (SSC)        56   \n", "3              2666      Super Store Jaipur F&V CPC (SSC)        12   \n", "4              2672     Super Store Lucknow F&V CPC (SSC)        26   \n", "5              2925       Super Store Noida F&V CPC (SSC)        36   \n", "6              3229              SS Mohali F&V CPC (MODI)        11   \n", "\n", "   Total DS Trips  Total DS that did not scan  billed_quantity  Before_6_GRN  \\\n", "0              21                         1.0            15213         14372   \n", "1              43                         0.0            65670         59492   \n", "2              56                        21.0            43099         39661   \n", "3              12                         0.0             2923          2923   \n", "4              26                         0.0            10437          8043   \n", "5              36                         2.0            43553         16677   \n", "6              11                         1.0             5744          4652   \n", "\n", "   Before_7_GRN  Before_8_GRN  \n", "0         14372         14372  \n", "1         64187         64187  \n", "2         41559         41559  \n", "3          2923          2923  \n", "4         10437         10437  \n", "5         36498         36498  \n", "6          5175          5175  "]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["df_publishing_final = df_publishing.groupby(['sender_outlet_id','sender_outlet_name']).agg({'billed_quantity':'sum','Before_6_GRN':'sum','Before_7_GRN':'sum','Before_8_GRN':'sum'}).reset_index()\n", "df_publishing_final\n", "df_publishing_final = pd.merge(df_publishing_final, df_base_summary_truth,  how='left', on=[\"sender_outlet_id\"])\n", "df_publishing_final = pd.merge(df_publishing_final, df_base_summary_eligible,  how='left', on=[\"sender_outlet_id\"])\n", "df_publishing_final = pd.merge(df_publishing_final, df_base_summary,  how='left', on=[\"sender_outlet_id\"])\n", "df_publishing_final = df_publishing_final[['sender_outlet_id','sender_outlet_name','Total DS','Total DS Trips','Total DS that did not scan',\n", "                        'billed_quantity','Before_6_GRN','Before_7_GRN','Before_8_GRN']]\n", "df_publishing_final['Total DS that did not scan'] = df_publishing_final['Total DS that did not scan'].fillna(0)\n", "df_publishing_final"]}, {"cell_type": "code", "execution_count": null, "id": "daa20190-1825-40b7-b5ec-ad22af1308ac", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "a8a180c1-e94f-4e05-bb03-d6957009e2c6", "metadata": {}, "source": ["List of DS that did not <PERSON>an"]}, {"cell_type": "code", "execution_count": 24, "id": "535b5fd0-96e0-482a-8bd1-8276be128957", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sender_outlet_id</th>\n", "      <th>sender_outlet_name</th>\n", "      <th>receiving_outlet_id</th>\n", "      <th>receiver_outlet_name</th>\n", "      <th>trip_started_at</th>\n", "      <th>consignment_received_at</th>\n", "      <th>Grn_start_time</th>\n", "      <th>Grn_end_time</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>2936</td>\n", "      <td>SS Delhi Jahangirpuri ES80 (FNV)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:10:06.861</td>\n", "      <td>2022-07-26 05:11:18.946</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>2925</td>\n", "      <td>Super Store Noida F&amp;V CPC (SSC)</td>\n", "      <td>3573</td>\n", "      <td>SS UPNCR Noida Sector 67 ES56 (CC)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 06:03:39.531</td>\n", "      <td>2022-07-26 06:05:56.710</td>\n", "    </tr>\n", "    <tr>\n", "      <th>56</th>\n", "      <td>2925</td>\n", "      <td>Super Store Noida F&amp;V CPC (SSC)</td>\n", "      <td>3297</td>\n", "      <td>SS Meerut Mangal Pandey ES1 (CC)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>70</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>3507</td>\n", "      <td>SS Delhi Bharat Vihar ES99 PR (FNV)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:32:02.111</td>\n", "      <td>2022-07-26 05:33:38.032</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2942</td>\n", "      <td>SS Delhi Krishna Nagar ES91 PR (FNV)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>3236</td>\n", "      <td>SS Delhi Jheel Khurenja ES128 (FNV)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:05:08.457</td>\n", "      <td>2022-07-26 05:05:08.457</td>\n", "    </tr>\n", "    <tr>\n", "      <th>89</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2708</td>\n", "      <td>Super Store Delhi Grand Trunk ES61 (FNV)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 06:37:49.504</td>\n", "      <td>2022-07-26 06:37:49.504</td>\n", "    </tr>\n", "    <tr>\n", "      <th>97</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>3249</td>\n", "      <td>SS Delhi New Gokulpuri ES133 (FNV)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:31:56.107</td>\n", "      <td>2022-07-26 05:32:57.457</td>\n", "    </tr>\n", "    <tr>\n", "      <th>101</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2551</td>\n", "      <td>SS Delhi Sant Nagar ES70 (FNV)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:32:06.879</td>\n", "      <td>2022-07-26 05:33:13.249</td>\n", "    </tr>\n", "    <tr>\n", "      <th>102</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2312</td>\n", "      <td>Super Store Delhi Lajpat Nagar ES37 (FNV)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:30:40.847</td>\n", "      <td>2022-07-26 05:34:10.293</td>\n", "    </tr>\n", "    <tr>\n", "      <th>105</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2878</td>\n", "      <td>SS Mohali C-118 Phase 7 ES1 (Crof)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:09:48.074</td>\n", "      <td>2022-07-26 05:10:32.078</td>\n", "    </tr>\n", "    <tr>\n", "      <th>119</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>1431</td>\n", "      <td>Super Store Delhi Chattarpur ES2 PR (FNV)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:22:53.289</td>\n", "      <td>2022-07-26 05:23:46.136</td>\n", "    </tr>\n", "    <tr>\n", "      <th>123</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2306</td>\n", "      <td>Super Store Delhi Vasant Kunj ES35 (FNV)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:08:10.866</td>\n", "      <td>2022-07-26 05:09:58.280</td>\n", "    </tr>\n", "    <tr>\n", "      <th>126</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>3535</td>\n", "      <td>SS Delhi Ghitorni ES156 (FNV)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:12:32.494</td>\n", "      <td>2022-07-26 05:12:32.494</td>\n", "    </tr>\n", "    <tr>\n", "      <th>144</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2311</td>\n", "      <td>SS Delhi Tilak Nagar ES45 (FNV)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:22:37.215</td>\n", "      <td>2022-07-26 05:22:37.215</td>\n", "    </tr>\n", "    <tr>\n", "      <th>152</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2450</td>\n", "      <td>SS Delhi Laxmi Nagar ES57 PR (FNV)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:53:49.360</td>\n", "      <td>2022-07-26 05:53:49.360</td>\n", "    </tr>\n", "    <tr>\n", "      <th>178</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2705</td>\n", "      <td>Super Store Delhi Rajouri Garden ES62 (FNV)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 06:04:19.178</td>\n", "      <td>2022-07-26 06:04:19.178</td>\n", "    </tr>\n", "    <tr>\n", "      <th>182</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>3231</td>\n", "      <td>SS Delhi Tihar Village ES125 PR (FNV)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:28:20.033</td>\n", "      <td>2022-07-26 05:30:02.503</td>\n", "    </tr>\n", "    <tr>\n", "      <th>188</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2545</td>\n", "      <td>Super Store Delhi Sector 8 Dwarka ES52 PR (FNV)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:18:21.424</td>\n", "      <td>2022-07-26 05:18:21.424</td>\n", "    </tr>\n", "    <tr>\n", "      <th>193</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2313</td>\n", "      <td>Super Store Delhi Malviya Nagar ES38 (FNV)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:45:46.269</td>\n", "      <td>2022-07-26 05:46:28.203</td>\n", "    </tr>\n", "    <tr>\n", "      <th>197</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>3230</td>\n", "      <td>SS Delhi Sheikh <PERSON> ES26 PR (FNV)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:46:26.274</td>\n", "      <td>2022-07-26 05:46:26.274</td>\n", "    </tr>\n", "    <tr>\n", "      <th>199</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2702</td>\n", "      <td>SS Delhi Dwarka Sector 28 ES44 PR (FNV)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:44:01.266</td>\n", "      <td>2022-07-26 05:44:01.266</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>1485</td>\n", "      <td>Super Store Delhi Dwarka ES14 PR (FNV)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:52:40.564</td>\n", "      <td>2022-07-26 05:53:40.242</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2549</td>\n", "      <td>Super Store Delhi Defence Colony ES71 (FNV)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:00:49.296</td>\n", "      <td>2022-07-26 05:00:49.296</td>\n", "    </tr>\n", "    <tr>\n", "      <th>204</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2554</td>\n", "      <td>Super Store Delhi Panchsheel Vihar ES64 (FNV)</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:33:37.687</td>\n", "      <td>2022-07-26 05:33:37.687</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     sender_outlet_id                 sender_outlet_name  receiving_outlet_id  \\\n", "29               1280  Super Store Budhpur CPC F&V (SSC)                 2936   \n", "52               2925    Super Store Noida F&V CPC (SSC)                 3573   \n", "56               2925    Super Store Noida F&V CPC (SSC)                 3297   \n", "70               2472  Super Store Bamnoli F&V CPC (SSC)                 3507   \n", "71               2472  Super Store Bamnoli F&V CPC (SSC)                 2942   \n", "73               2472  Super Store Bamnoli F&V CPC (SSC)                 3236   \n", "89               2472  Super Store Bamnoli F&V CPC (SSC)                 2708   \n", "97               2472  Super Store Bamnoli F&V CPC (SSC)                 3249   \n", "101              2472  Super Store Bamnoli F&V CPC (SSC)                 2551   \n", "102              2472  Super Store Bamnoli F&V CPC (SSC)                 2312   \n", "105              3229           SS Mohali F&V CPC (MODI)                 2878   \n", "119              2472  Super Store Bamnoli F&V CPC (SSC)                 1431   \n", "123              2472  Super Store Bamnoli F&V CPC (SSC)                 2306   \n", "126              2472  Super Store Bamnoli F&V CPC (SSC)                 3535   \n", "144              2472  Super Store Bamnoli F&V CPC (SSC)                 2311   \n", "152              2472  Super Store Bamnoli F&V CPC (SSC)                 2450   \n", "178              2472  Super Store Bamnoli F&V CPC (SSC)                 2705   \n", "182              2472  Super Store Bamnoli F&V CPC (SSC)                 3231   \n", "188              2472  Super Store Bamnoli F&V CPC (SSC)                 2545   \n", "193              2472  Super Store Bamnoli F&V CPC (SSC)                 2313   \n", "197              2472  Super Store Bamnoli F&V CPC (SSC)                 3230   \n", "199              2472  Super Store Bamnoli F&V CPC (SSC)                 2702   \n", "200              2472  Super Store Bamnoli F&V CPC (SSC)                 1485   \n", "203              2472  Super Store Bamnoli F&V CPC (SSC)                 2549   \n", "204              2472  Super Store Bamnoli F&V CPC (SSC)                 2554   \n", "\n", "                                receiver_outlet_name trip_started_at  \\\n", "29                  SS Delhi Jahangirpuri ES80 (FNV)             NaT   \n", "52                SS UPNCR Noida Sector 67 ES56 (CC)             NaT   \n", "56                  SS Meerut Mangal Pandey ES1 (CC)             NaT   \n", "70               SS Delhi Bharat Vihar ES99 PR (FNV)             NaT   \n", "71              SS Delhi Krishna Nagar ES91 PR (FNV)             NaT   \n", "73               SS Delhi Jheel Khurenja ES128 (FNV)             NaT   \n", "89          Super Store Delhi Grand Trunk ES61 (FNV)             NaT   \n", "97                SS Delhi New Gokulpuri ES133 (FNV)             NaT   \n", "101                   SS Delhi Sant Nagar ES70 (FNV)             NaT   \n", "102        Super Store Delhi Lajpat Nagar ES37 (FNV)             NaT   \n", "105               SS Mohali C-118 Phase 7 ES1 (Crof)             NaT   \n", "119        Super Store Delhi Chattarpur ES2 PR (FNV)             NaT   \n", "123         Super Store Delhi Vasant Kunj ES35 (FNV)             NaT   \n", "126                    SS Delhi Ghitorni ES156 (FNV)             NaT   \n", "144                  SS Delhi Tilak Nagar ES45 (FNV)             NaT   \n", "152               SS Delhi Laxmi Nagar ES57 PR (FNV)             NaT   \n", "178      Super Store Delhi Rajouri Garden ES62 (FNV)             NaT   \n", "182            SS Delhi Tihar Village ES125 PR (FNV)             NaT   \n", "188  Super Store Delhi Sector 8 Dwarka ES52 PR (FNV)             NaT   \n", "193       Super Store Delhi Malviya Nagar ES38 (FNV)             NaT   \n", "197              SS Delhi Sheikh <PERSON> ES26 PR (FNV)             NaT   \n", "199          SS Delhi Dwarka Sector 28 ES44 PR (FNV)             NaT   \n", "200           Super Store Delhi Dwarka ES14 PR (FNV)             NaT   \n", "203      Super Store Delhi Defence Colony ES71 (FNV)             NaT   \n", "204    Super Store Delhi Panchsheel Vihar ES64 (FNV)             NaT   \n", "\n", "    consignment_received_at          Grn_start_time            Grn_end_time  \n", "29                      NaT 2022-07-26 05:10:06.861 2022-07-26 05:11:18.946  \n", "52                      NaT 2022-07-26 06:03:39.531 2022-07-26 06:05:56.710  \n", "56                      NaT                     NaT                     NaT  \n", "70                      NaT 2022-07-26 05:32:02.111 2022-07-26 05:33:38.032  \n", "71                      NaT                     NaT                     NaT  \n", "73                      NaT 2022-07-26 05:05:08.457 2022-07-26 05:05:08.457  \n", "89                      NaT 2022-07-26 06:37:49.504 2022-07-26 06:37:49.504  \n", "97                      NaT 2022-07-26 05:31:56.107 2022-07-26 05:32:57.457  \n", "101                     NaT 2022-07-26 05:32:06.879 2022-07-26 05:33:13.249  \n", "102                     NaT 2022-07-26 05:30:40.847 2022-07-26 05:34:10.293  \n", "105                     NaT 2022-07-26 05:09:48.074 2022-07-26 05:10:32.078  \n", "119                     NaT 2022-07-26 05:22:53.289 2022-07-26 05:23:46.136  \n", "123                     NaT 2022-07-26 05:08:10.866 2022-07-26 05:09:58.280  \n", "126                     NaT 2022-07-26 05:12:32.494 2022-07-26 05:12:32.494  \n", "144                     NaT 2022-07-26 05:22:37.215 2022-07-26 05:22:37.215  \n", "152                     NaT 2022-07-26 05:53:49.360 2022-07-26 05:53:49.360  \n", "178                     NaT 2022-07-26 06:04:19.178 2022-07-26 06:04:19.178  \n", "182                     NaT 2022-07-26 05:28:20.033 2022-07-26 05:30:02.503  \n", "188                     NaT 2022-07-26 05:18:21.424 2022-07-26 05:18:21.424  \n", "193                     NaT 2022-07-26 05:45:46.269 2022-07-26 05:46:28.203  \n", "197                     NaT 2022-07-26 05:46:26.274 2022-07-26 05:46:26.274  \n", "199                     NaT 2022-07-26 05:44:01.266 2022-07-26 05:44:01.266  \n", "200                     NaT 2022-07-26 05:52:40.564 2022-07-26 05:53:40.242  \n", "203                     NaT 2022-07-26 05:00:49.296 2022-07-26 05:00:49.296  \n", "204                     NaT 2022-07-26 05:33:37.687 2022-07-26 05:33:37.687  "]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["df_not_scanned = df_base_alert[df_base_alert['trip_started_at'].isna() == True]\n", "df_not_scanned"]}, {"cell_type": "markdown", "id": "022fb729-22f9-4f2e-abc1-ebe1e8645bee", "metadata": {}, "source": ["Vehicle Recieved on Time"]}, {"cell_type": "code", "execution_count": 25, "id": "17e76ab1-f88c-40fa-8b2a-5fe206715b84", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sender_outlet_id</th>\n", "      <th>sender_outlet_name</th>\n", "      <th>receiving_outlet_id</th>\n", "      <th>receiver_outlet_name</th>\n", "      <th>trip_started_at</th>\n", "      <th>consignment_received_at</th>\n", "      <th>Grn_start_time</th>\n", "      <th>Grn_end_time</th>\n", "      <th>hour</th>\n", "      <th>minute</th>\n", "      <th>time_barrier</th>\n", "      <th>vehicle_on_time</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2866</td>\n", "      <td>SS Panchkula Industrial Area ES1 (Crof)</td>\n", "      <td>2022-07-26 03:26:31.267</td>\n", "      <td>2022-07-26 05:06:19.450</td>\n", "      <td>2022-07-26 05:11:12.873</td>\n", "      <td>2022-07-26 05:12:42.931</td>\n", "      <td>5</td>\n", "      <td>6</td>\n", "      <td>506</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2860</td>\n", "      <td>SS Chandigarh Sector 34 Allen ES2 (Crof)</td>\n", "      <td>2022-07-26 04:13:00.517</td>\n", "      <td>2022-07-26 04:57:53.730</td>\n", "      <td>2022-07-26 05:33:56.076</td>\n", "      <td>2022-07-26 05:34:38.053</td>\n", "      <td>4</td>\n", "      <td>57</td>\n", "      <td>457</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2884</td>\n", "      <td>SS Zirakpur Golden Square ES1 (Crof)</td>\n", "      <td>2022-07-26 03:59:23.714</td>\n", "      <td>2022-07-26 05:02:29.406</td>\n", "      <td>2022-07-26 05:34:16.051</td>\n", "      <td>2022-07-26 05:35:10.090</td>\n", "      <td>5</td>\n", "      <td>2</td>\n", "      <td>502</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2672</td>\n", "      <td>Super Store Lucknow F&amp;V CPC (SSC)</td>\n", "      <td>4094</td>\n", "      <td>SS Kanpur Ghantaghar ES7 PR (CC)</td>\n", "      <td>2022-07-26 02:50:16.522</td>\n", "      <td>2022-07-26 05:38:13.138</td>\n", "      <td>2022-07-26 05:46:28.065</td>\n", "      <td>2022-07-26 05:46:28.065</td>\n", "      <td>5</td>\n", "      <td>38</td>\n", "      <td>538</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2672</td>\n", "      <td>Super Store Lucknow F&amp;V CPC (SSC)</td>\n", "      <td>2413</td>\n", "      <td>SS Kanpur Arya Nagar ES3 PR (CC)</td>\n", "      <td>2022-07-26 03:09:42.626</td>\n", "      <td>2022-07-26 04:54:07.007</td>\n", "      <td>2022-07-26 05:12:34.077</td>\n", "      <td>2022-07-26 05:12:34.077</td>\n", "      <td>4</td>\n", "      <td>54</td>\n", "      <td>454</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>195</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>3235</td>\n", "      <td>SS Delhi Uttam Nagar ES59 PR (FNV)</td>\n", "      <td>2022-07-26 01:34:41.179</td>\n", "      <td>2022-07-26 05:09:33.124</td>\n", "      <td>2022-07-26 05:55:21.002</td>\n", "      <td>2022-07-26 05:55:21.002</td>\n", "      <td>5</td>\n", "      <td>9</td>\n", "      <td>509</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>196</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2443</td>\n", "      <td>Super Store Delhi Fortis Vasantkunj ES42 PR (FNV)</td>\n", "      <td>2022-07-26 01:22:47.300</td>\n", "      <td>2022-07-26 03:09:57.685</td>\n", "      <td>2022-07-26 05:21:25.663</td>\n", "      <td>2022-07-26 05:22:24.868</td>\n", "      <td>3</td>\n", "      <td>9</td>\n", "      <td>309</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>198</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2308</td>\n", "      <td>Super Store Delhi Sangam Vihar ES39 PR (FNV)</td>\n", "      <td>2022-07-26 02:56:28.418</td>\n", "      <td>2022-07-26 05:33:51.542</td>\n", "      <td>2022-07-26 05:54:44.949</td>\n", "      <td>2022-07-26 05:54:44.949</td>\n", "      <td>5</td>\n", "      <td>33</td>\n", "      <td>533</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2868</td>\n", "      <td>SS Delhi Prahladpur ES79 (FNV)</td>\n", "      <td>2022-07-26 02:41:22.489</td>\n", "      <td>2022-07-26 05:25:07.143</td>\n", "      <td>2022-07-26 05:48:18.858</td>\n", "      <td>2022-07-26 05:48:18.858</td>\n", "      <td>5</td>\n", "      <td>25</td>\n", "      <td>525</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>202</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>1024</td>\n", "      <td>Super Store Delhi Jhilmil ES1 (FNV)</td>\n", "      <td>2022-07-26 03:14:20.216</td>\n", "      <td>2022-07-26 05:26:29.842</td>\n", "      <td>2022-07-26 05:26:28.874</td>\n", "      <td>2022-07-26 05:26:28.874</td>\n", "      <td>5</td>\n", "      <td>26</td>\n", "      <td>526</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>172 rows × 12 columns</p>\n", "</div>"], "text/plain": ["     sender_outlet_id                 sender_outlet_name  receiving_outlet_id  \\\n", "0                3229           SS Mohali F&V CPC (MODI)                 2866   \n", "1                3229           SS Mohali F&V CPC (MODI)                 2860   \n", "2                3229           SS Mohali F&V CPC (MODI)                 2884   \n", "3                2672  Super Store Lucknow F&V CPC (SSC)                 4094   \n", "4                2672  Super Store Lucknow F&V CPC (SSC)                 2413   \n", "..                ...                                ...                  ...   \n", "195              2472  Super Store Bamnoli F&V CPC (SSC)                 3235   \n", "196              2472  Super Store Bamnoli F&V CPC (SSC)                 2443   \n", "198              2472  Super Store Bamnoli F&V CPC (SSC)                 2308   \n", "201              2472  Super Store Bamnoli F&V CPC (SSC)                 2868   \n", "202              2472  Super Store Bamnoli F&V CPC (SSC)                 1024   \n", "\n", "                                  receiver_outlet_name  \\\n", "0              SS Panchkula Industrial Area ES1 (Crof)   \n", "1             SS Chandigarh Sector 34 Allen ES2 (Crof)   \n", "2                 SS Zirakpur Golden Square ES1 (Crof)   \n", "3                     SS Kanpur Ghantaghar ES7 PR (CC)   \n", "4                     SS Kanpur Arya Nagar ES3 PR (CC)   \n", "..                                                 ...   \n", "195                 SS Delhi Uttam Nagar ES59 PR (FNV)   \n", "196  Super Store Delhi Fortis Vasantkunj ES42 PR (FNV)   \n", "198       Super Store Delhi Sangam Vihar ES39 PR (FNV)   \n", "201                     SS Delhi Prahladpur ES79 (FNV)   \n", "202                Super Store Delhi Jhilmil ES1 (FNV)   \n", "\n", "            trip_started_at consignment_received_at          Grn_start_time  \\\n", "0   2022-07-26 03:26:31.267 2022-07-26 05:06:19.450 2022-07-26 05:11:12.873   \n", "1   2022-07-26 04:13:00.517 2022-07-26 04:57:53.730 2022-07-26 05:33:56.076   \n", "2   2022-07-26 03:59:23.714 2022-07-26 05:02:29.406 2022-07-26 05:34:16.051   \n", "3   2022-07-26 02:50:16.522 2022-07-26 05:38:13.138 2022-07-26 05:46:28.065   \n", "4   2022-07-26 03:09:42.626 2022-07-26 04:54:07.007 2022-07-26 05:12:34.077   \n", "..                      ...                     ...                     ...   \n", "195 2022-07-26 01:34:41.179 2022-07-26 05:09:33.124 2022-07-26 05:55:21.002   \n", "196 2022-07-26 01:22:47.300 2022-07-26 03:09:57.685 2022-07-26 05:21:25.663   \n", "198 2022-07-26 02:56:28.418 2022-07-26 05:33:51.542 2022-07-26 05:54:44.949   \n", "201 2022-07-26 02:41:22.489 2022-07-26 05:25:07.143 2022-07-26 05:48:18.858   \n", "202 2022-07-26 03:14:20.216 2022-07-26 05:26:29.842 2022-07-26 05:26:28.874   \n", "\n", "               Grn_end_time  hour  minute  time_barrier  vehicle_on_time  \n", "0   2022-07-26 05:12:42.931     5       6           506                1  \n", "1   2022-07-26 05:34:38.053     4      57           457                1  \n", "2   2022-07-26 05:35:10.090     5       2           502                1  \n", "3   2022-07-26 05:46:28.065     5      38           538                0  \n", "4   2022-07-26 05:12:34.077     4      54           454                1  \n", "..                      ...   ...     ...           ...              ...  \n", "195 2022-07-26 05:55:21.002     5       9           509                1  \n", "196 2022-07-26 05:22:24.868     3       9           309                1  \n", "198 2022-07-26 05:54:44.949     5      33           533                0  \n", "201 2022-07-26 05:48:18.858     5      25           525                1  \n", "202 2022-07-26 05:26:28.874     5      26           526                1  \n", "\n", "[172 rows x 12 columns]"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["df_consignment = df_base_alert[df_base_alert['consignment_received_at'].isna() == False]\n", "df_consignment ['hour'] = df_consignment ['consignment_received_at'].dt.hour\n", "df_consignment ['minute'] = df_consignment ['consignment_received_at'].dt.minute\n", "df_consignment ['time_barrier'] = df_consignment ['hour']*100 + df_consignment ['minute']\n", "df_consignment['vehicle_on_time'] = np.where(df_consignment['time_barrier'] < 531,1,0)\n", "df_consignment\n"]}, {"cell_type": "markdown", "id": "bf345edd-19f4-4266-b6a1-a0da159fa839", "metadata": {}, "source": ["Late Vehicle"]}, {"cell_type": "code", "execution_count": 26, "id": "69d5db7d-bd83-4f54-8a50-56ce71514fee", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sender_outlet_id</th>\n", "      <th>Count of Vehicle Arrived Late</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1280</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1674</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2472</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2666</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2672</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2925</td>\n", "      <td>18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3229</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   sender_outlet_id  Count of Vehicle Arrived Late\n", "0              1280                              3\n", "1              1674                              1\n", "2              2472                              8\n", "3              2666                              1\n", "4              2672                              4\n", "5              2925                             18\n", "6              3229                              1"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["df_consignment_late = df_consignment[df_consignment['vehicle_on_time'] ==0]\n", "ds_late_vehicle = df_consignment_late.copy()\n", "df_consignment_late = df_consignment_late.groupby(['sender_outlet_id']).agg({'receiving_outlet_id':'count'}).reset_index()\n", "df_consignment_late = df_consignment_late.rename(columns = {'receiving_outlet_id':'Count of Vehicle Arrived Late'})\n", "\n", "df_consignment_late"]}, {"cell_type": "code", "execution_count": 27, "id": "45ebcd01-c5f8-40ef-82cf-644a0030dd9f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sender_outlet_id</th>\n", "      <th>sender_outlet_name</th>\n", "      <th>Total DS</th>\n", "      <th>Total DS Trips</th>\n", "      <th>Total DS that did not scan</th>\n", "      <th>billed_quantity</th>\n", "      <th>Before_6_GRN</th>\n", "      <th>Before_7_GRN</th>\n", "      <th>Before_8_GRN</th>\n", "      <th>Count of Vehicle Arrived Late</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>21</td>\n", "      <td>21</td>\n", "      <td>1.0</td>\n", "      <td>15213</td>\n", "      <td>14372</td>\n", "      <td>14372</td>\n", "      <td>14372</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1674</td>\n", "      <td>Super Store Gurgaon G2 F&amp;V CPC (SSC)</td>\n", "      <td>43</td>\n", "      <td>43</td>\n", "      <td>0.0</td>\n", "      <td>65670</td>\n", "      <td>59492</td>\n", "      <td>64187</td>\n", "      <td>64187</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>56</td>\n", "      <td>56</td>\n", "      <td>21.0</td>\n", "      <td>43099</td>\n", "      <td>39661</td>\n", "      <td>41559</td>\n", "      <td>41559</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2666</td>\n", "      <td>Super Store Jaipur F&amp;V CPC (SSC)</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>0.0</td>\n", "      <td>2923</td>\n", "      <td>2923</td>\n", "      <td>2923</td>\n", "      <td>2923</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2672</td>\n", "      <td>Super Store Lucknow F&amp;V CPC (SSC)</td>\n", "      <td>26</td>\n", "      <td>26</td>\n", "      <td>0.0</td>\n", "      <td>10437</td>\n", "      <td>8043</td>\n", "      <td>10437</td>\n", "      <td>10437</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2925</td>\n", "      <td>Super Store Noida F&amp;V CPC (SSC)</td>\n", "      <td>36</td>\n", "      <td>36</td>\n", "      <td>2.0</td>\n", "      <td>43553</td>\n", "      <td>16677</td>\n", "      <td>36498</td>\n", "      <td>36498</td>\n", "      <td>18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>11</td>\n", "      <td>11</td>\n", "      <td>1.0</td>\n", "      <td>5744</td>\n", "      <td>4652</td>\n", "      <td>5175</td>\n", "      <td>5175</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   sender_outlet_id                    sender_outlet_name  Total DS  \\\n", "0              1280     Super Store Budhpur CPC F&V (SSC)        21   \n", "1              1674  Super Store Gurgaon G2 F&V CPC (SSC)        43   \n", "2              2472     Super Store Bamnoli F&V CPC (SSC)        56   \n", "3              2666      Super Store Jaipur F&V CPC (SSC)        12   \n", "4              2672     Super Store Lucknow F&V CPC (SSC)        26   \n", "5              2925       Super Store Noida F&V CPC (SSC)        36   \n", "6              3229              SS Mohali F&V CPC (MODI)        11   \n", "\n", "   Total DS Trips  Total DS that did not scan  billed_quantity  Before_6_GRN  \\\n", "0              21                         1.0            15213         14372   \n", "1              43                         0.0            65670         59492   \n", "2              56                        21.0            43099         39661   \n", "3              12                         0.0             2923          2923   \n", "4              26                         0.0            10437          8043   \n", "5              36                         2.0            43553         16677   \n", "6              11                         1.0             5744          4652   \n", "\n", "   Before_7_GRN  Before_8_GRN  Count of Vehicle Arrived Late  \n", "0         14372         14372                              3  \n", "1         64187         64187                              1  \n", "2         41559         41559                              8  \n", "3          2923          2923                              1  \n", "4         10437         10437                              4  \n", "5         36498         36498                             18  \n", "6          5175          5175                              1  "]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["df_publishing_final = pd.merge(df_publishing_final, df_consignment_late,  how='left', on=[\"sender_outlet_id\"])\n", "df_publishing_final = df_publishing_final.fillna(0)\n", "df_publishing_final"]}, {"cell_type": "markdown", "id": "1116bf12-d8be-4d42-989c-ed48bc0313f0", "metadata": {}, "source": ["Push to Google Sheets"]}, {"cell_type": "code", "execution_count": 28, "id": "db3b72ee-861a-4083-b97b-465b84b0f56a", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Updated sheet raw_data'"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["pb.to_sheets(df_base_alert, '1Z_7RQr9vqXaSWCAJ-yQjcFdxi4-aLe616nkiRBNWWk4', 'raw_data')"]}, {"cell_type": "code", "execution_count": null, "id": "56a0b5b7-240c-4d88-aca1-7e2007fbc254", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 29, "id": "f74b060e-b76d-4507-a5ae-d041d4f1fcee", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Updated sheet DS_with_no_scan'"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["pb.to_sheets(df_not_scanned, '1Z_7RQr9vqXaSWCAJ-yQjcFdxi4-aLe616nkiRBNWWk4', 'DS_with_no_scan')"]}, {"cell_type": "code", "execution_count": 30, "id": "c8976788-251b-4c29-b3ec-e223fe0a1387", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Updated sheet late_vehicles'"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["pb.to_sheets(ds_late_vehicle, '1Z_7RQr9vqXaSWCAJ-yQjcFdxi4-aLe616nkiRBNWWk4', 'late_vehicles')"]}, {"cell_type": "code", "execution_count": 31, "id": "cf5b4fc8-b173-47f0-86f9-c90afcfa9999", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sender_outlet_id</th>\n", "      <th>sender_outlet_name</th>\n", "      <th>receiving_outlet_id</th>\n", "      <th>receiver_outlet_name</th>\n", "      <th>consignment_received_at</th>\n", "      <th>Grn_start_time</th>\n", "      <th>Grn_end_time</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>53</th>\n", "      <td>2925</td>\n", "      <td>Super Store Noida F&amp;V CPC (SSC)</td>\n", "      <td>2886</td>\n", "      <td>SS UPNCR Noida Sector 19 ES14 (CC)</td>\n", "      <td>2022-07-26 05:30:33.417000</td>\n", "      <td>not available</td>\n", "      <td>not available</td>\n", "    </tr>\n", "    <tr>\n", "      <th>56</th>\n", "      <td>2925</td>\n", "      <td>Super Store Noida F&amp;V CPC (SSC)</td>\n", "      <td>3297</td>\n", "      <td>SS Meerut Mangal Pandey ES1 (CC)</td>\n", "      <td>not available</td>\n", "      <td>not available</td>\n", "      <td>not available</td>\n", "    </tr>\n", "    <tr>\n", "      <th>60</th>\n", "      <td>2925</td>\n", "      <td>Super Store Noida F&amp;V CPC (SSC)</td>\n", "      <td>2734</td>\n", "      <td>SS UPNCR Ghaziabad Niti Khand-2 ES32 PR (CC)</td>\n", "      <td>2022-07-26 03:49:25.384000</td>\n", "      <td>not available</td>\n", "      <td>not available</td>\n", "    </tr>\n", "    <tr>\n", "      <th>68</th>\n", "      <td>2925</td>\n", "      <td>Super Store Noida F&amp;V CPC (SSC)</td>\n", "      <td>2741</td>\n", "      <td>Super Store UPNCR Shyam Park ES39 PR (CC)</td>\n", "      <td>not available</td>\n", "      <td>not available</td>\n", "      <td>not available</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2942</td>\n", "      <td>SS Delhi Krishna Nagar ES91 PR (FNV)</td>\n", "      <td>not available</td>\n", "      <td>not available</td>\n", "      <td>not available</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82</th>\n", "      <td>2925</td>\n", "      <td>Super Store Noida F&amp;V CPC (SSC)</td>\n", "      <td>1976</td>\n", "      <td>Super Store UPNCR Sakipur ES16 PR (CC)</td>\n", "      <td>2022-07-26 04:47:17.905000</td>\n", "      <td>not available</td>\n", "      <td>not available</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2921</td>\n", "      <td>SS Jalandhar Baradari ES2 (Crof)</td>\n", "      <td>2022-07-26 05:20:46.242000</td>\n", "      <td>not available</td>\n", "      <td>not available</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>2448</td>\n", "      <td>Super Store Delhi Rana Pratap Bagh ES46 PR (FNV)</td>\n", "      <td>2022-07-26 05:19:00.091000</td>\n", "      <td>not available</td>\n", "      <td>not available</td>\n", "    </tr>\n", "    <tr>\n", "      <th>150</th>\n", "      <td>2925</td>\n", "      <td>Super Store Noida F&amp;V CPC (SSC)</td>\n", "      <td>1823</td>\n", "      <td>Super Store UPNCR Gyani Border ES11 (CC)</td>\n", "      <td>not available</td>\n", "      <td>not available</td>\n", "      <td>not available</td>\n", "    </tr>\n", "    <tr>\n", "      <th>160</th>\n", "      <td>1674</td>\n", "      <td>Super Store Gurgaon G2 F&amp;V CPC (SSC)</td>\n", "      <td>3658</td>\n", "      <td>SS Gurgaon Sector 93 ES48 PR (NM)</td>\n", "      <td>2022-07-26 05:24:10.912000</td>\n", "      <td>not available</td>\n", "      <td>not available</td>\n", "    </tr>\n", "    <tr>\n", "      <th>186</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>3242</td>\n", "      <td>SS Delhi Ganesh Nagar ES127 PR (FNV)</td>\n", "      <td>2022-07-26 05:59:18.405000</td>\n", "      <td>not available</td>\n", "      <td>not available</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     sender_outlet_id                    sender_outlet_name  \\\n", "53               2925       Super Store Noida F&V CPC (SSC)   \n", "56               2925       Super Store Noida F&V CPC (SSC)   \n", "60               2925       Super Store Noida F&V CPC (SSC)   \n", "68               2925       Super Store Noida F&V CPC (SSC)   \n", "71               2472     Super Store Bamnoli F&V CPC (SSC)   \n", "82               2925       Super Store Noida F&V CPC (SSC)   \n", "90               3229              SS Mohali F&V CPC (MODI)   \n", "93               1280     Super Store Budhpur CPC F&V (SSC)   \n", "150              2925       Super Store Noida F&V CPC (SSC)   \n", "160              1674  Super Store Gurgaon G2 F&V CPC (SSC)   \n", "186              2472     Super Store Bamnoli F&V CPC (SSC)   \n", "\n", "     receiving_outlet_id                              receiver_outlet_name  \\\n", "53                  2886                SS UPNCR Noida Sector 19 ES14 (CC)   \n", "56                  3297                  SS Meerut Mangal Pandey ES1 (CC)   \n", "60                  2734      SS UPNCR Ghaziabad Niti Khand-2 ES32 PR (CC)   \n", "68                  2741         Super Store UPNCR Shyam Park ES39 PR (CC)   \n", "71                  2942              SS Delhi Krishna Nagar ES91 PR (FNV)   \n", "82                  1976            Super Store UPNCR Sakipur ES16 PR (CC)   \n", "90                  2921                  SS Jalandhar Baradari ES2 (Crof)   \n", "93                  2448  Super Store Delhi Rana Pratap Bagh ES46 PR (FNV)   \n", "150                 1823          Super Store UPNCR Gyani Border ES11 (CC)   \n", "160                 3658                 SS Gurgaon Sector 93 ES48 PR (NM)   \n", "186                 3242              SS Delhi Ganesh Nagar ES127 PR (FNV)   \n", "\n", "        consignment_received_at Grn_start_time   Grn_end_time  \n", "53   2022-07-26 05:30:33.417000  not available  not available  \n", "56                not available  not available  not available  \n", "60   2022-07-26 03:49:25.384000  not available  not available  \n", "68                not available  not available  not available  \n", "71                not available  not available  not available  \n", "82   2022-07-26 04:47:17.905000  not available  not available  \n", "90   2022-07-26 05:20:46.242000  not available  not available  \n", "93   2022-07-26 05:19:00.091000  not available  not available  \n", "150               not available  not available  not available  \n", "160  2022-07-26 05:24:10.912000  not available  not available  \n", "186  2022-07-26 05:59:18.405000  not available  not available  "]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["df_no_grn = df_base_alert.copy()\n", "df_no_grn['send_alert'] = np.where((df_no_grn['Grn_start_time'].isna()) & (df_no_grn['Grn_end_time'].isna()), 1, 0)\n", "\n", "df_no_grn = df_no_grn[df_no_grn['send_alert'] == 1]\n", "df_no_grn = df_no_grn[['sender_outlet_id', 'sender_outlet_name', 'receiving_outlet_id','receiver_outlet_name', 'consignment_received_at', 'Grn_start_time','Grn_end_time']]\n", "\n", "df_no_grn = df_no_grn.fillna('not available')\n", "df_no_grn"]}, {"cell_type": "code", "execution_count": 32, "id": "bdcc6194-4020-4db5-bbb1-ef76fc0a406c", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Updated sheet DS_with_no_grn'"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["pb.to_sheets(df_no_grn, '1Z_7RQr9vqXaSWCAJ-yQjcFdxi4-aLe616nkiRBNWWk4', 'DS_with_no_grn')"]}, {"cell_type": "code", "execution_count": 33, "id": "1741782a-dd57-46d7-8f35-a721b5dbdca0", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sender_outlet_id</th>\n", "      <th>sender_outlet_name</th>\n", "      <th>receiving_outlet_id</th>\n", "      <th>receiver_outlet_name</th>\n", "      <th>trip_started_at</th>\n", "      <th>consignment_received_at</th>\n", "      <th>Grn_start_time</th>\n", "      <th>Grn_end_time</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>3446</td>\n", "      <td>SS Delhi West Patel Nagar ES132 (FNV)</td>\n", "      <td>2022-07-26 02:27:25.397</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:34:32.170</td>\n", "      <td>2022-07-26 05:35:50.354</td>\n", "    </tr>\n", "    <tr>\n", "      <th>68</th>\n", "      <td>2925</td>\n", "      <td>Super Store Noida F&amp;V CPC (SSC)</td>\n", "      <td>2741</td>\n", "      <td>Super Store UPNCR Shyam Park ES39 PR (CC)</td>\n", "      <td>2022-07-26 03:56:03.352</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>74</th>\n", "      <td>2925</td>\n", "      <td>Super Store Noida F&amp;V CPC (SSC)</td>\n", "      <td>2890</td>\n", "      <td>SS UPNCR Ghaziabad Shastri Nagar ES44 PR (CC)</td>\n", "      <td>2022-07-26 02:28:41.581</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:51:09.305</td>\n", "      <td>2022-07-26 05:52:26.115</td>\n", "    </tr>\n", "    <tr>\n", "      <th>108</th>\n", "      <td>2925</td>\n", "      <td>Super Store Noida F&amp;V CPC (SSC)</td>\n", "      <td>2739</td>\n", "      <td>SS UPNCR Noida Panchsheel Green ES59 PR (CC)</td>\n", "      <td>2022-07-26 03:51:32.333</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 06:21:09.024</td>\n", "      <td>2022-07-26 06:23:20.810</td>\n", "    </tr>\n", "    <tr>\n", "      <th>117</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2709</td>\n", "      <td>SS Delhi Mahipalpur ES34 (FNV)</td>\n", "      <td>2022-07-26 01:57:45.544</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:02:20.669</td>\n", "      <td>2022-07-26 05:05:26.530</td>\n", "    </tr>\n", "    <tr>\n", "      <th>118</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2692</td>\n", "      <td>SS Delhi IP Extension ES78 PR (FNV)</td>\n", "      <td>2022-07-26 01:32:41.679</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:49:51.092</td>\n", "      <td>2022-07-26 05:50:44.033</td>\n", "    </tr>\n", "    <tr>\n", "      <th>150</th>\n", "      <td>2925</td>\n", "      <td>Super Store Noida F&amp;V CPC (SSC)</td>\n", "      <td>1823</td>\n", "      <td>Super Store UPNCR Gyani Border ES11 (CC)</td>\n", "      <td>2022-07-26 04:22:33.817</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>191</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>3253</td>\n", "      <td>SS Delhi RK Puram Sector-12 ES117 PR (FNV)</td>\n", "      <td>2022-07-26 01:36:50.646</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:21:26.539</td>\n", "      <td>2022-07-26 05:22:56.285</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     sender_outlet_id                 sender_outlet_name  receiving_outlet_id  \\\n", "43               1280  Super Store Budhpur CPC F&V (SSC)                 3446   \n", "68               2925    Super Store Noida F&V CPC (SSC)                 2741   \n", "74               2925    Super Store Noida F&V CPC (SSC)                 2890   \n", "108              2925    Super Store Noida F&V CPC (SSC)                 2739   \n", "117              2472  Super Store Bamnoli F&V CPC (SSC)                 2709   \n", "118              2472  Super Store Bamnoli F&V CPC (SSC)                 2692   \n", "150              2925    Super Store Noida F&V CPC (SSC)                 1823   \n", "191              2472  Super Store Bamnoli F&V CPC (SSC)                 3253   \n", "\n", "                              receiver_outlet_name         trip_started_at  \\\n", "43           SS Delhi West Patel Nagar ES132 (FNV) 2022-07-26 02:27:25.397   \n", "68       Super Store UPNCR Shyam Park ES39 PR (CC) 2022-07-26 03:56:03.352   \n", "74   SS UPNCR Ghaziabad Shastri Nagar ES44 PR (CC) 2022-07-26 02:28:41.581   \n", "108   SS UPNCR Noida Panchsheel Green ES59 PR (CC) 2022-07-26 03:51:32.333   \n", "117                 SS Delhi Mahipalpur ES34 (FNV) 2022-07-26 01:57:45.544   \n", "118            SS Delhi IP Extension ES78 PR (FNV) 2022-07-26 01:32:41.679   \n", "150       Super Store UPNCR Gyani Border ES11 (CC) 2022-07-26 04:22:33.817   \n", "191     SS Delhi RK Puram Sector-12 ES117 PR (FNV) 2022-07-26 01:36:50.646   \n", "\n", "    consignment_received_at          Grn_start_time            Grn_end_time  \n", "43                      NaT 2022-07-26 05:34:32.170 2022-07-26 05:35:50.354  \n", "68                      NaT                     NaT                     NaT  \n", "74                      NaT 2022-07-26 05:51:09.305 2022-07-26 05:52:26.115  \n", "108                     NaT 2022-07-26 06:21:09.024 2022-07-26 06:23:20.810  \n", "117                     NaT 2022-07-26 05:02:20.669 2022-07-26 05:05:26.530  \n", "118                     NaT 2022-07-26 05:49:51.092 2022-07-26 05:50:44.033  \n", "150                     NaT                     NaT                     NaT  \n", "191                     NaT 2022-07-26 05:21:26.539 2022-07-26 05:22:56.285  "]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["df_not_recieved = df_base_alert[(df_base_alert['consignment_received_at'].isna() == True) & (df_base_alert['trip_started_at'].isna() == False)]\n", "df_not_recieved"]}, {"cell_type": "markdown", "id": "a5dad244-0580-4625-a6df-7255414bb0ec", "metadata": {}, "source": ["Adhoc_Requirements"]}, {"cell_type": "code", "execution_count": 34, "id": "3d601623-fbfb-40bd-82ca-379cf5534d9b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sto_id</th>\n", "      <th>item_id</th>\n", "      <th>sender_outlet_id</th>\n", "      <th>sender_outlet_name</th>\n", "      <th>receiving_outlet_id</th>\n", "      <th>receiver_outlet_name</th>\n", "      <th>billed_quantity</th>\n", "      <th>inward_quantity</th>\n", "      <th>consignment_received_at</th>\n", "      <th>grn_created_at</th>\n", "      <th>hour_grn</th>\n", "      <th>hour</th>\n", "      <th>minute</th>\n", "      <th>time_barrier</th>\n", "      <th>vehicle_on_time</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5130878</td>\n", "      <td>10005407</td>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2866</td>\n", "      <td>SS Panchkula Industrial Area ES1 (Crof)</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>2022-07-26 05:06:19.450</td>\n", "      <td>2022-07-26 05:11:12.873</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>6.0</td>\n", "      <td>506.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5130878</td>\n", "      <td>10043867</td>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2866</td>\n", "      <td>SS Panchkula Industrial Area ES1 (Crof)</td>\n", "      <td>43</td>\n", "      <td>43</td>\n", "      <td>2022-07-26 05:06:19.450</td>\n", "      <td>2022-07-26 05:11:12.873</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>6.0</td>\n", "      <td>506.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5130878</td>\n", "      <td>10043865</td>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2866</td>\n", "      <td>SS Panchkula Industrial Area ES1 (Crof)</td>\n", "      <td>16</td>\n", "      <td>16</td>\n", "      <td>2022-07-26 05:06:19.450</td>\n", "      <td>2022-07-26 05:11:12.873</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>6.0</td>\n", "      <td>506.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>5130878</td>\n", "      <td>10032019</td>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2866</td>\n", "      <td>SS Panchkula Industrial Area ES1 (Crof)</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>2022-07-26 05:06:19.450</td>\n", "      <td>2022-07-26 05:11:12.873</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>6.0</td>\n", "      <td>506.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5130878</td>\n", "      <td>10028965</td>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2866</td>\n", "      <td>SS Panchkula Industrial Area ES1 (Crof)</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>2022-07-26 05:06:19.450</td>\n", "      <td>2022-07-26 05:11:12.873</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>6.0</td>\n", "      <td>506.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13345</th>\n", "      <td>5131323</td>\n", "      <td>10098520</td>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2314</td>\n", "      <td>Super Store Delhi Ashram ES24 PR (FNV)</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:50:20.913</td>\n", "      <td>5.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13346</th>\n", "      <td>5131323</td>\n", "      <td>10098509</td>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2314</td>\n", "      <td>Super Store Delhi Ashram ES24 PR (FNV)</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:50:20.913</td>\n", "      <td>5.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13347</th>\n", "      <td>5131323</td>\n", "      <td>10098514</td>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2314</td>\n", "      <td>Super Store Delhi Ashram ES24 PR (FNV)</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:50:20.913</td>\n", "      <td>5.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13348</th>\n", "      <td>5131323</td>\n", "      <td>10002268</td>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2314</td>\n", "      <td>Super Store Delhi Ashram ES24 PR (FNV)</td>\n", "      <td>24</td>\n", "      <td>24</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:50:20.913</td>\n", "      <td>5.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13349</th>\n", "      <td>5131323</td>\n", "      <td>10002241</td>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>2314</td>\n", "      <td>Super Store Delhi Ashram ES24 PR (FNV)</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>NaT</td>\n", "      <td>2022-07-26 05:50:20.913</td>\n", "      <td>5.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>13350 rows × 15 columns</p>\n", "</div>"], "text/plain": ["        sto_id   item_id  sender_outlet_id                 sender_outlet_name  \\\n", "0      5130878  10005407              3229           SS Mohali F&V CPC (MODI)   \n", "1      5130878  10043867              3229           SS Mohali F&V CPC (MODI)   \n", "2      5130878  10043865              3229           SS Mohali F&V CPC (MODI)   \n", "3      5130878  10032019              3229           SS Mohali F&V CPC (MODI)   \n", "4      5130878  10028965              3229           SS Mohali F&V CPC (MODI)   \n", "...        ...       ...               ...                                ...   \n", "13345  5131323  10098520              2472  Super Store Bamnoli F&V CPC (SSC)   \n", "13346  5131323  10098509              2472  Super Store Bamnoli F&V CPC (SSC)   \n", "13347  5131323  10098514              2472  Super Store Bamnoli F&V CPC (SSC)   \n", "13348  5131323  10002268              2472  Super Store Bamnoli F&V CPC (SSC)   \n", "13349  5131323  10002241              2472  Super Store Bamnoli F&V CPC (SSC)   \n", "\n", "       receiving_outlet_id                     receiver_outlet_name  \\\n", "0                     2866  SS Panchkula Industrial Area ES1 (Crof)   \n", "1                     2866  SS Panchkula Industrial Area ES1 (Crof)   \n", "2                     2866  SS Panchkula Industrial Area ES1 (Crof)   \n", "3                     2866  SS Panchkula Industrial Area ES1 (Crof)   \n", "4                     2866  SS Panchkula Industrial Area ES1 (Crof)   \n", "...                    ...                                      ...   \n", "13345                 2314   Super Store Delhi Ashram ES24 PR (FNV)   \n", "13346                 2314   Super Store Delhi Ashram ES24 PR (FNV)   \n", "13347                 2314   Super Store Delhi Ashram ES24 PR (FNV)   \n", "13348                 2314   Super Store Delhi Ashram ES24 PR (FNV)   \n", "13349                 2314   Super Store Delhi Ashram ES24 PR (FNV)   \n", "\n", "       billed_quantity  inward_quantity consignment_received_at  \\\n", "0                    5                5 2022-07-26 05:06:19.450   \n", "1                   43               43 2022-07-26 05:06:19.450   \n", "2                   16               16 2022-07-26 05:06:19.450   \n", "3                    5                5 2022-07-26 05:06:19.450   \n", "4                    3                3 2022-07-26 05:06:19.450   \n", "...                ...              ...                     ...   \n", "13345                3                3                     NaT   \n", "13346                3                3                     NaT   \n", "13347                3                3                     NaT   \n", "13348               24               24                     NaT   \n", "13349                6                6                     NaT   \n", "\n", "               grn_created_at  hour_grn  hour  minute  time_barrier  \\\n", "0     2022-07-26 05:11:12.873       5.0   5.0     6.0         506.0   \n", "1     2022-07-26 05:11:12.873       5.0   5.0     6.0         506.0   \n", "2     2022-07-26 05:11:12.873       5.0   5.0     6.0         506.0   \n", "3     2022-07-26 05:11:12.873       5.0   5.0     6.0         506.0   \n", "4     2022-07-26 05:11:12.873       5.0   5.0     6.0         506.0   \n", "...                       ...       ...   ...     ...           ...   \n", "13345 2022-07-26 05:50:20.913       5.0   NaN     NaN           NaN   \n", "13346 2022-07-26 05:50:20.913       5.0   NaN     NaN           NaN   \n", "13347 2022-07-26 05:50:20.913       5.0   NaN     NaN           NaN   \n", "13348 2022-07-26 05:50:20.913       5.0   NaN     NaN           NaN   \n", "13349 2022-07-26 05:50:20.913       5.0   NaN     NaN           NaN   \n", "\n", "       vehicle_on_time  \n", "0                    1  \n", "1                    1  \n", "2                    1  \n", "3                    1  \n", "4                    1  \n", "...                ...  \n", "13345                0  \n", "13346                0  \n", "13347                0  \n", "13348                0  \n", "13349                0  \n", "\n", "[13350 rows x 15 columns]"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["df_summary_list = df_base_list[['sto_id','item_id','sender_outlet_id','sender_outlet_name','receiving_outlet_id','receiver_outlet_name',\n", "                             'billed_quantity','inward_quantity','consignment_received_at','grn_created_at']]\n", "\n", "df_summary_list['hour_grn'] = df_summary_list['grn_created_at'].dt.hour\n", "df_summary_list ['hour'] = df_summary_list ['consignment_received_at'].dt.hour\n", "df_summary_list ['minute'] = df_summary_list ['consignment_received_at'].dt.minute\n", "df_summary_list ['time_barrier'] = df_summary_list ['hour']*100 + df_summary_list ['minute']\n", "df_summary_list['vehicle_on_time'] = np.where(df_summary_list['time_barrier'] < 531,1,0)\n", "df_summary_list"]}, {"cell_type": "markdown", "id": "4c1c2a5b-2c69-4076-ab76-e6cba0d496d9", "metadata": {}, "source": ["GRN DONE LATE WITH VEHICLE ON TIME"]}, {"cell_type": "code", "execution_count": 35, "id": "61531bd4-bf17-4474-a9b6-1286f368ecb1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sender_outlet_id</th>\n", "      <th>receiving_outlet_id</th>\n", "      <th>Before_6_GRN</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1280</td>\n", "      <td>1477</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1280</td>\n", "      <td>1495</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1280</td>\n", "      <td>2307</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1280</td>\n", "      <td>2447</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1280</td>\n", "      <td>2451</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>78</th>\n", "      <td>3229</td>\n", "      <td>2859</td>\n", "      <td>264</td>\n", "    </tr>\n", "    <tr>\n", "      <th>79</th>\n", "      <td>3229</td>\n", "      <td>2860</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>80</th>\n", "      <td>3229</td>\n", "      <td>2866</td>\n", "      <td>430</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81</th>\n", "      <td>3229</td>\n", "      <td>2882</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82</th>\n", "      <td>3229</td>\n", "      <td>3877</td>\n", "      <td>456</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>83 rows × 3 columns</p>\n", "</div>"], "text/plain": ["    sender_outlet_id  receiving_outlet_id  Before_6_GRN\n", "0               1280                 1477             4\n", "1               1280                 1495            10\n", "2               1280                 2307             4\n", "3               1280                 2447             5\n", "4               1280                 2451             6\n", "..               ...                  ...           ...\n", "78              3229                 2859           264\n", "79              3229                 2860             4\n", "80              3229                 2866           430\n", "81              3229                 2882             6\n", "82              3229                 3877           456\n", "\n", "[83 rows x 3 columns]"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["df_summary_list = df_summary_list[df_summary_list['vehicle_on_time'] ==1]\n", "df_summary_list = df_summary_list[df_summary_list['hour_grn'] <6]\n", "df_summary_list = df_summary_list.groupby(['sender_outlet_id','receiving_outlet_id']).agg({'inward_quantity':'sum'}).reset_index()\n", "df_summary_list = df_summary_list.rename(columns = {'inward_quantity':'Before_6_GRN'})\n", "df_summary_list"]}, {"cell_type": "code", "execution_count": 36, "id": "bf57c045-bddd-4e0b-808d-cb0e1c31e6df", "metadata": {}, "outputs": [], "source": ["df_grn_before_6_vehicle_on_time = pd.merge(df_summary_main, df_summary_list,  how='left', on=[\"sender_outlet_id\",\"receiving_outlet_id\"])\n", "\n"]}, {"cell_type": "code", "execution_count": 37, "id": "ab20d067-5ceb-4c70-8e50-c3a6941cb2ea", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sender_outlet_id</th>\n", "      <th>sender_outlet_name</th>\n", "      <th>receiving_outlet_id</th>\n", "      <th>receiver_outlet_name</th>\n", "      <th>billed_quantity</th>\n", "      <th>Before_6_GRN</th>\n", "      <th>percent_grn_before_6</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>1473</td>\n", "      <td>Super Store Delhi Prahlad Vihar ES8 PR (FNV)</td>\n", "      <td>948</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>1477</td>\n", "      <td>Super Store Delhi Shalimar Bagh ES10 PR (FNV)</td>\n", "      <td>603</td>\n", "      <td>4.0</td>\n", "      <td>0.006633</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>1495</td>\n", "      <td>Super Store Delhi Badli ES19 (FNV)</td>\n", "      <td>655</td>\n", "      <td>10.0</td>\n", "      <td>0.015267</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>2307</td>\n", "      <td>Super Store Delhi Paschim Vihar ES32 PR (FNV)</td>\n", "      <td>1086</td>\n", "      <td>4.0</td>\n", "      <td>0.003683</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>2447</td>\n", "      <td>Super Store Delhi Prashant Vihar ES63 (FNV)</td>\n", "      <td>965</td>\n", "      <td>5.0</td>\n", "      <td>0.005181</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2880</td>\n", "      <td>SS Ludhiana Model Town ES3 (Crof)</td>\n", "      <td>523</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2882</td>\n", "      <td>SS Chandigarh Dhanas Sector 25 ES3 (Crof)</td>\n", "      <td>377</td>\n", "      <td>6.0</td>\n", "      <td>0.015915</td>\n", "    </tr>\n", "    <tr>\n", "      <th>202</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2884</td>\n", "      <td>SS Zirakpur Golden Square ES1 (Crof)</td>\n", "      <td>878</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2921</td>\n", "      <td>SS Jalandhar Baradari ES2 (Crof)</td>\n", "      <td>569</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>204</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>3877</td>\n", "      <td>SS Chandigarh Sector 17 ES4 (Crof)</td>\n", "      <td>456</td>\n", "      <td>456.0</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>205 rows × 7 columns</p>\n", "</div>"], "text/plain": ["     sender_outlet_id                 sender_outlet_name  receiving_outlet_id  \\\n", "0                1280  Super Store Budhpur CPC F&V (SSC)                 1473   \n", "1                1280  Super Store Budhpur CPC F&V (SSC)                 1477   \n", "2                1280  Super Store Budhpur CPC F&V (SSC)                 1495   \n", "3                1280  Super Store Budhpur CPC F&V (SSC)                 2307   \n", "4                1280  Super Store Budhpur CPC F&V (SSC)                 2447   \n", "..                ...                                ...                  ...   \n", "200              3229           SS Mohali F&V CPC (MODI)                 2880   \n", "201              3229           SS Mohali F&V CPC (MODI)                 2882   \n", "202              3229           SS Mohali F&V CPC (MODI)                 2884   \n", "203              3229           SS Mohali F&V CPC (MODI)                 2921   \n", "204              3229           SS Mohali F&V CPC (MODI)                 3877   \n", "\n", "                              receiver_outlet_name  billed_quantity  \\\n", "0     Super Store Delhi Prahlad Vihar ES8 PR (FNV)              948   \n", "1    Super Store Delhi Shalimar Bagh ES10 PR (FNV)              603   \n", "2               Super Store Delhi Badli ES19 (FNV)              655   \n", "3    Super Store Delhi Paschim Vihar ES32 PR (FNV)             1086   \n", "4      Super Store Delhi Prashant Vihar ES63 (FNV)              965   \n", "..                                             ...              ...   \n", "200              SS Ludhiana Model Town ES3 (Crof)              523   \n", "201      SS Chandigarh Dhanas Sector 25 ES3 (Crof)              377   \n", "202           SS Zirakpur Golden Square ES1 (Crof)              878   \n", "203               SS Jalandhar Baradari ES2 (Crof)              569   \n", "204             SS Chandigarh Sector 17 ES4 (Crof)              456   \n", "\n", "     Before_6_GRN  percent_grn_before_6  \n", "0             0.0              0.000000  \n", "1             4.0              0.006633  \n", "2            10.0              0.015267  \n", "3             4.0              0.003683  \n", "4             5.0              0.005181  \n", "..            ...                   ...  \n", "200           0.0              0.000000  \n", "201           6.0              0.015915  \n", "202           0.0              0.000000  \n", "203           0.0              0.000000  \n", "204         456.0              1.000000  \n", "\n", "[205 rows x 7 columns]"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["df_grn_before_6_vehicle_on_time = df_grn_before_6_vehicle_on_time.fillna(0)\n", "df_grn_before_6_vehicle_on_time['percent_grn_before_6'] = df_grn_before_6_vehicle_on_time['Before_6_GRN']/df_grn_before_6_vehicle_on_time['billed_quantity']\n", "df_grn_before_6_vehicle_on_time"]}, {"cell_type": "code", "execution_count": 38, "id": "f9314227-08cf-4d33-bff6-5c55afe21e09", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sender_outlet_id</th>\n", "      <th>sender_outlet_name</th>\n", "      <th>receiving_outlet_id</th>\n", "      <th>receiver_outlet_name</th>\n", "      <th>billed_quantity</th>\n", "      <th>Before_6_GRN</th>\n", "      <th>percent_grn_before_6</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>1473</td>\n", "      <td>Super Store Delhi Prahlad Vihar ES8 PR (FNV)</td>\n", "      <td>948</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>1477</td>\n", "      <td>Super Store Delhi Shalimar Bagh ES10 PR (FNV)</td>\n", "      <td>603</td>\n", "      <td>4.0</td>\n", "      <td>0.006633</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>1495</td>\n", "      <td>Super Store Delhi Badli ES19 (FNV)</td>\n", "      <td>655</td>\n", "      <td>10.0</td>\n", "      <td>0.015267</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>2307</td>\n", "      <td>Super Store Delhi Paschim Vihar ES32 PR (FNV)</td>\n", "      <td>1086</td>\n", "      <td>4.0</td>\n", "      <td>0.003683</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>2447</td>\n", "      <td>Super Store Delhi Prashant Vihar ES63 (FNV)</td>\n", "      <td>965</td>\n", "      <td>5.0</td>\n", "      <td>0.005181</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>199</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2878</td>\n", "      <td>SS Mohali C-118 Phase 7 ES1 (Crof)</td>\n", "      <td>507</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2880</td>\n", "      <td>SS Ludhiana Model Town ES3 (Crof)</td>\n", "      <td>523</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2882</td>\n", "      <td>SS Chandigarh Dhanas Sector 25 ES3 (Crof)</td>\n", "      <td>377</td>\n", "      <td>6.0</td>\n", "      <td>0.015915</td>\n", "    </tr>\n", "    <tr>\n", "      <th>202</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2884</td>\n", "      <td>SS Zirakpur Golden Square ES1 (Crof)</td>\n", "      <td>878</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>2921</td>\n", "      <td>SS Jalandhar Baradari ES2 (Crof)</td>\n", "      <td>569</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>160 rows × 7 columns</p>\n", "</div>"], "text/plain": ["     sender_outlet_id                 sender_outlet_name  receiving_outlet_id  \\\n", "0                1280  Super Store Budhpur CPC F&V (SSC)                 1473   \n", "1                1280  Super Store Budhpur CPC F&V (SSC)                 1477   \n", "2                1280  Super Store Budhpur CPC F&V (SSC)                 1495   \n", "3                1280  Super Store Budhpur CPC F&V (SSC)                 2307   \n", "4                1280  Super Store Budhpur CPC F&V (SSC)                 2447   \n", "..                ...                                ...                  ...   \n", "199              3229           SS Mohali F&V CPC (MODI)                 2878   \n", "200              3229           SS Mohali F&V CPC (MODI)                 2880   \n", "201              3229           SS Mohali F&V CPC (MODI)                 2882   \n", "202              3229           SS Mohali F&V CPC (MODI)                 2884   \n", "203              3229           SS Mohali F&V CPC (MODI)                 2921   \n", "\n", "                              receiver_outlet_name  billed_quantity  \\\n", "0     Super Store Delhi Prahlad Vihar ES8 PR (FNV)              948   \n", "1    Super Store Delhi Shalimar Bagh ES10 PR (FNV)              603   \n", "2               Super Store Delhi Badli ES19 (FNV)              655   \n", "3    Super Store Delhi Paschim Vihar ES32 PR (FNV)             1086   \n", "4      Super Store Delhi Prashant Vihar ES63 (FNV)              965   \n", "..                                             ...              ...   \n", "199             SS Mohali C-118 Phase 7 ES1 (Crof)              507   \n", "200              SS Ludhiana Model Town ES3 (Crof)              523   \n", "201      SS Chandigarh Dhanas Sector 25 ES3 (Crof)              377   \n", "202           SS Zirakpur Golden Square ES1 (Crof)              878   \n", "203               SS Jalandhar Baradari ES2 (Crof)              569   \n", "\n", "     Before_6_GRN  percent_grn_before_6  \n", "0             0.0              0.000000  \n", "1             4.0              0.006633  \n", "2            10.0              0.015267  \n", "3             4.0              0.003683  \n", "4             5.0              0.005181  \n", "..            ...                   ...  \n", "199           0.0              0.000000  \n", "200           0.0              0.000000  \n", "201           6.0              0.015915  \n", "202           0.0              0.000000  \n", "203           0.0              0.000000  \n", "\n", "[160 rows x 7 columns]"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["df_grn_less_before_6_vehicle_on_time = df_grn_before_6_vehicle_on_time[df_grn_before_6_vehicle_on_time['percent_grn_before_6'] < 0.9]\n", "\n", "df_grn_less_before_6_vehicle_on_time"]}, {"cell_type": "markdown", "id": "986335af-0e69-4427-86b0-f9cbfba011b1", "metadata": {}, "source": ["Pushing Raw to Sheet"]}, {"cell_type": "code", "execution_count": 39, "id": "dbeda624-8780-482d-a1b4-4b17222a774a", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Updated sheet DS_late_GRN_Vehicle_on_time'"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["pb.to_sheets(df_grn_less_before_6_vehicle_on_time, '1Z_7RQr9vqXaSWCAJ-yQjcFdxi4-aLe616nkiRBNWWk4', 'DS_late_GRN_Vehicle_on_time')"]}, {"cell_type": "markdown", "id": "fd1d2bed-88ce-4fd1-a869-676a6ba274b3", "metadata": {}, "source": [" "]}, {"cell_type": "code", "execution_count": 40, "id": "4b6fbc98-42d5-40a7-98c1-9d618d741bbe", "metadata": {}, "outputs": [], "source": ["df_grn_less_before_6_vehicle_on_time = df_grn_less_before_6_vehicle_on_time.groupby(['sender_outlet_id','sender_outlet_name']).agg({'receiving_outlet_id':'count'}).reset_index()"]}, {"cell_type": "code", "execution_count": 41, "id": "7f750f24-1946-4a06-92d5-408ac5597c03", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sender_outlet_id</th>\n", "      <th>sender_outlet_name</th>\n", "      <th>Number of DS with GRN after 6</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1674</td>\n", "      <td>Super Store Gurgaon G2 F&amp;V CPC (SSC)</td>\n", "      <td>33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>52</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2666</td>\n", "      <td>Super Store Jaipur F&amp;V CPC (SSC)</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2672</td>\n", "      <td>Super Store Lucknow F&amp;V CPC (SSC)</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2925</td>\n", "      <td>Super Store Noida F&amp;V CPC (SSC)</td>\n", "      <td>36</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>6</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   sender_outlet_id                    sender_outlet_name  \\\n", "0              1280     Super Store Budhpur CPC F&V (SSC)   \n", "1              1674  Super Store Gurgaon G2 F&V CPC (SSC)   \n", "2              2472     Super Store Bamnoli F&V CPC (SSC)   \n", "3              2666      Super Store Jaipur F&V CPC (SSC)   \n", "4              2672     Super Store Lucknow F&V CPC (SSC)   \n", "5              2925       Super Store Noida F&V CPC (SSC)   \n", "6              3229              SS Mohali F&V CPC (MODI)   \n", "\n", "   Number of DS with GRN after 6  \n", "0                             20  \n", "1                             33  \n", "2                             52  \n", "3                              3  \n", "4                             10  \n", "5                             36  \n", "6                              6  "]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["df_grn_less_before_6_vehicle_on_time = df_grn_less_before_6_vehicle_on_time.rename(columns = {'receiving_outlet_id':'Number of DS with GRN after 6'})\n", "df_grn_less_before_6_vehicle_on_time"]}, {"cell_type": "code", "execution_count": 42, "id": "1127f876-cdd7-4b8c-bce4-baf1750291a8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sender_outlet_id</th>\n", "      <th>sender_outlet_name</th>\n", "      <th>Total DS</th>\n", "      <th>Total DS Trips</th>\n", "      <th>Total DS that did not scan</th>\n", "      <th>billed_quantity</th>\n", "      <th>Before_6_GRN</th>\n", "      <th>Before_7_GRN</th>\n", "      <th>Before_8_GRN</th>\n", "      <th>Count of Vehicle Arrived Late</th>\n", "      <th>Number of DS with GRN after 6</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1280</td>\n", "      <td>Super Store Budhpur CPC F&amp;V (SSC)</td>\n", "      <td>21</td>\n", "      <td>21</td>\n", "      <td>1.0</td>\n", "      <td>15213</td>\n", "      <td>14372</td>\n", "      <td>14372</td>\n", "      <td>14372</td>\n", "      <td>3</td>\n", "      <td>20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1674</td>\n", "      <td>Super Store Gurgaon G2 F&amp;V CPC (SSC)</td>\n", "      <td>43</td>\n", "      <td>43</td>\n", "      <td>0.0</td>\n", "      <td>65670</td>\n", "      <td>59492</td>\n", "      <td>64187</td>\n", "      <td>64187</td>\n", "      <td>1</td>\n", "      <td>33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2472</td>\n", "      <td>Super Store Bamnoli F&amp;V CPC (SSC)</td>\n", "      <td>56</td>\n", "      <td>56</td>\n", "      <td>21.0</td>\n", "      <td>43099</td>\n", "      <td>39661</td>\n", "      <td>41559</td>\n", "      <td>41559</td>\n", "      <td>8</td>\n", "      <td>52</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2666</td>\n", "      <td>Super Store Jaipur F&amp;V CPC (SSC)</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>0.0</td>\n", "      <td>2923</td>\n", "      <td>2923</td>\n", "      <td>2923</td>\n", "      <td>2923</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2672</td>\n", "      <td>Super Store Lucknow F&amp;V CPC (SSC)</td>\n", "      <td>26</td>\n", "      <td>26</td>\n", "      <td>0.0</td>\n", "      <td>10437</td>\n", "      <td>8043</td>\n", "      <td>10437</td>\n", "      <td>10437</td>\n", "      <td>4</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2925</td>\n", "      <td>Super Store Noida F&amp;V CPC (SSC)</td>\n", "      <td>36</td>\n", "      <td>36</td>\n", "      <td>2.0</td>\n", "      <td>43553</td>\n", "      <td>16677</td>\n", "      <td>36498</td>\n", "      <td>36498</td>\n", "      <td>18</td>\n", "      <td>36</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3229</td>\n", "      <td>SS Mohali F&amp;V CPC (MODI)</td>\n", "      <td>11</td>\n", "      <td>11</td>\n", "      <td>1.0</td>\n", "      <td>5744</td>\n", "      <td>4652</td>\n", "      <td>5175</td>\n", "      <td>5175</td>\n", "      <td>1</td>\n", "      <td>6</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   sender_outlet_id                    sender_outlet_name  Total DS  \\\n", "0              1280     Super Store Budhpur CPC F&V (SSC)        21   \n", "1              1674  Super Store Gurgaon G2 F&V CPC (SSC)        43   \n", "2              2472     Super Store Bamnoli F&V CPC (SSC)        56   \n", "3              2666      Super Store Jaipur F&V CPC (SSC)        12   \n", "4              2672     Super Store Lucknow F&V CPC (SSC)        26   \n", "5              2925       Super Store Noida F&V CPC (SSC)        36   \n", "6              3229              SS Mohali F&V CPC (MODI)        11   \n", "\n", "   Total DS Trips  Total DS that did not scan  billed_quantity  Before_6_GRN  \\\n", "0              21                         1.0            15213         14372   \n", "1              43                         0.0            65670         59492   \n", "2              56                        21.0            43099         39661   \n", "3              12                         0.0             2923          2923   \n", "4              26                         0.0            10437          8043   \n", "5              36                         2.0            43553         16677   \n", "6              11                         1.0             5744          4652   \n", "\n", "   Before_7_GRN  Before_8_GRN  Count of Vehicle Arrived Late  \\\n", "0         14372         14372                              3   \n", "1         64187         64187                              1   \n", "2         41559         41559                              8   \n", "3          2923          2923                              1   \n", "4         10437         10437                              4   \n", "5         36498         36498                             18   \n", "6          5175          5175                              1   \n", "\n", "   Number of DS with GRN after 6  \n", "0                             20  \n", "1                             33  \n", "2                             52  \n", "3                              3  \n", "4                             10  \n", "5                             36  \n", "6                              6  "]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["df_grn_before_6_vehicle_on_time_final = pd.merge(df_publishing_final, df_grn_less_before_6_vehicle_on_time,  how='left', on=[\"sender_outlet_id\",\"sender_outlet_name\"])\n", "\n", "df_grn_before_6_vehicle_on_time_final"]}, {"cell_type": "code", "execution_count": 43, "id": "c52bc737-edbb-4b36-8907-39175e241151", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Updated sheet summary'"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["pb.to_sheets(df_publishing_final, '1Z_7RQr9vqXaSWCAJ-yQjcFdxi4-aLe616nkiRBNWWk4', 'summary')"]}, {"cell_type": "code", "execution_count": null, "id": "d640f69f-4d02-4875-b1d9-96e174f06a6a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "deff1e60-00d6-42f4-9920-79cd00fe4b99", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4bb2f7bc-fe74-40f8-894e-a5b856140658", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c641cde1-f933-4d58-896e-556c97450840", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8aaea694-b35e-4de8-b4c9-43ea8d6e3835", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3ca6eadc-4c0e-481f-8489-3fd8cf3351eb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "083bbd75-8cd6-4ecf-b261-c170d30d864d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "119c0d44-74ca-4ced-aaf3-e30eeb53276d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "836dc273-ee5b-426e-b82c-6829ec38b218", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "18b744bf-39f8-4676-95a9-fa37b82d1602", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1bc6abdd-b943-43b5-a318-748aba2e878a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "99692ff0-2ff5-4ef9-b526-64779a6ab00e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f882d045-b5a8-4a45-a5e5-6001df1d4f34", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "daf3f7cb-7cf8-4bac-a1d7-84f8a1b3ae2b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9e059550-f07e-4b50-a547-e6ee1e970e48", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "22cdc61e-368a-4030-9e14-252b173e8d6a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5dd21f1e-c5d9-4a83-8790-d49e3edb9509", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "579d8abe-58c6-4ab3-b94d-0c05aa13b620", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}