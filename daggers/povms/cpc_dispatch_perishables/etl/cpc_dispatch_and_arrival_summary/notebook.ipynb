{"cells": [{"cell_type": "code", "execution_count": null, "id": "6b1024cd-f941-4a79-9d85-5edfbfd3eaec", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "import json\n", "import math\n", "\n", "# import boto3F\n", "import io\n", "\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")\n", "\n", "start_time = time.time()"]}, {"cell_type": "code", "execution_count": null, "id": "f40a2918-40d6-4a1a-bd25-3b78d850339e", "metadata": {}, "outputs": [], "source": ["# pd.set_option('display.max_columns', None)\n", "# pd.set_option('display.max_rows', 500)\n", "pd.options.mode.chained_assignment = None  # default='warn'"]}, {"cell_type": "markdown", "id": "1f024531-55cc-4448-8e42-ba6b501e965d", "metadata": {}, "source": ["Fetching STO Details"]}, {"cell_type": "code", "execution_count": null, "id": "a0f07dca-1841-4eed-a836-3e5b5fbb5ec7", "metadata": {}, "outputs": [], "source": ["start_date = date.today() - <PERSON><PERSON><PERSON>(days=1)\n", "end_date = date.today()"]}, {"cell_type": "code", "execution_count": null, "id": "f94d9571-7ded-4548-8f5a-1f64313ec7e5", "metadata": {}, "outputs": [], "source": ["def STO_base():\n", "    sql = f\"\"\"select sto_id, item_id, sender_outlet_id,sender_outlet_name,receiving_outlet_id, receiver_outlet_name, billed_quantity,trip_started_at,consignment_received_at,\n", "             max(sto_created_at) as sto_created_at,max(sto_invoice_created_at) as sto_billed_at,max(grn_started_at) as grn_created_at, sum(inwarded_quantity) as inward_quantity\n", "             from metrics.esto_details es\n", "             left join lake_retail.console_outlet co on co.id = es.sender_outlet_id\n", "             where item_id in (select distinct item_id from consumer.dark_stores_fnv_assortment)\n", "             and sender_outlet_id in (2666,3229,2672,2472,1280,1674,2925)\n", "             and receiving_outlet_id not in (2340)\n", "             and sto_created_at between '{start_date}' || ' 20:00:00' and '{end_date}' || ' 02:00:00'\n", "             group by 1,2,3,4,5,6,7,8,9\n", "        \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=CON_REDSHIFT)\n", "\n", "\n", "df_base_list = STO_base()\n", "df_base_list"]}, {"cell_type": "code", "execution_count": null, "id": "5665fb85-bb59-4fdd-880b-d3c488fc23cf", "metadata": {}, "outputs": [], "source": ["def STO_base_2_all():\n", "    sql = f\"\"\"select sto_id, item_id, sender_outlet_id,sender_outlet_name,receiving_outlet_id, receiver_outlet_name, billed_quantity,trip_started_at,consignment_received_at,\n", "             max(sto_created_at) as sto_created_at,max(sto_invoice_created_at) as sto_billed_at,max(grn_started_at) as grn_created_at, sum(inwarded_quantity) as inward_quantity\n", "             from metrics.esto_details es\n", "             left join lake_retail.console_outlet co on co.id = es.sender_outlet_id\n", "             where item_id in (select distinct item_id from lake_rpc.item_details where active= 1 and approved =1 and perishable =1)\n", "             and sender_outlet_id in (2666,3229,2672,2472,1280,1674,2925)\n", "             and receiving_outlet_id not in (2340)\n", "             and sto_created_at between '{start_date}' || ' 20:00:00' and '{end_date}' || ' 02:00:00'\n", "             group by 1,2,3,4,5,6,7,8,9\n", "        \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=CON_REDSHIFT)\n", "\n", "\n", "df_base_list_transit = STO_base_2_all()\n", "df_base_list_transit"]}, {"cell_type": "code", "execution_count": null, "id": "1db3d640-bd44-4b2d-ab27-eb0dab1f0f0a", "metadata": {}, "outputs": [], "source": ["df_base_arrival = df_base_list_transit[\n", "    [\n", "        \"sender_outlet_id\",\n", "        \"receiving_outlet_id\",\n", "        \"trip_started_at\",\n", "        \"consignment_received_at\",\n", "    ]\n", "]\n", "df_base_arrival = df_base_arrival[df_base_arrival[\"trip_started_at\"].isna() == False]\n", "df_base_arrival = df_base_arrival.drop_duplicates()\n", "df_base_arrival[\n", "    (df_base_arrival[\"sender_outlet_id\"] == 1674) & (df_base_arrival[\"receiving_outlet_id\"] == 3670)\n", "]\n", "df_base_arrival"]}, {"cell_type": "code", "execution_count": null, "id": "11c24ecf-37fd-4387-9c03-6eb813466bfc", "metadata": {}, "outputs": [], "source": ["df_grn_time = df_base_list[[\"sender_outlet_id\", \"receiving_outlet_id\", \"grn_created_at\"]]\n", "df_grn_time = df_grn_time[df_grn_time[\"grn_created_at\"].isna() == False]\n", "df_grn_time = df_grn_time.drop_duplicates().reset_index()\n", "df_grn_time = df_grn_time[[\"sender_outlet_id\", \"receiving_outlet_id\", \"grn_created_at\"]]\n", "df_grn_time"]}, {"cell_type": "code", "execution_count": null, "id": "09ab70c4-0ce2-4936-a5d6-91bf3a6df0b1", "metadata": {}, "outputs": [], "source": ["df_grn_agg_min = df_grn_time.groupby([\"receiving_outlet_id\"]).min().reset_index()\n", "df_grn_agg_min = df_grn_agg_min.rename(columns={\"grn_created_at\": \"Grn_start_time\"})\n", "df_grn_agg_max = df_grn_time.groupby([\"receiving_outlet_id\"]).max().reset_index()\n", "df_grn_agg_max = df_grn_agg_max.rename(columns={\"grn_created_at\": \"Grn_end_time\"})"]}, {"cell_type": "code", "execution_count": null, "id": "080c4636-e145-43f8-93d5-bb473f44e156", "metadata": {}, "outputs": [], "source": ["df_grn_agg_min.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2e48bcfd-e53c-4699-8427-7d5ce3ae2a53", "metadata": {}, "outputs": [], "source": ["df_grn_agg_max.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bdab60d4-1f82-4517-81d4-3d0163654e63", "metadata": {}, "outputs": [], "source": ["df_base_aana_jaana = df_base_list[\n", "    [\n", "        \"sender_outlet_id\",\n", "        \"sender_outlet_name\",\n", "        \"receiving_outlet_id\",\n", "        \"receiver_outlet_name\",\n", "    ]\n", "]\n", "df_base_aana_jaana = df_base_aana_jaana.drop_duplicates().reset_index().drop(columns={\"index\"})\n", "print(df_base_aana_jaana.shape)\n", "df_base_aana_jaana.head()"]}, {"cell_type": "code", "execution_count": null, "id": "443d980f-0ec0-413c-8e65-8797400f8fd9", "metadata": {}, "outputs": [], "source": ["df_base_alert = pd.merge(\n", "    df_base_aana_jaana,\n", "    df_base_arrival,\n", "    how=\"left\",\n", "    on=[\"sender_outlet_id\", \"receiving_outlet_id\"],\n", ")\n", "print(df_base_alert.shape)\n", "df_base_alert = pd.merge(\n", "    df_base_alert,\n", "    df_grn_agg_min,\n", "    how=\"left\",\n", "    on=[\"sender_outlet_id\", \"receiving_outlet_id\"],\n", ")\n", "df_base_alert = pd.merge(\n", "    df_base_alert,\n", "    df_grn_agg_max,\n", "    how=\"left\",\n", "    on=[\"sender_outlet_id\", \"receiving_outlet_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4a30e43f-c76f-4b64-a6f2-df5d95194787", "metadata": {}, "outputs": [], "source": ["df_base_alert[\"Grn_start_time\"] = pd.to_datetime(df_base_alert[\"Grn_start_time\"])\n", "df_base_alert[\"Grn_end_time\"] = pd.to_datetime(df_base_alert[\"Grn_end_time\"])\n", "\n", "df_base_alert"]}, {"cell_type": "code", "execution_count": null, "id": "b2cd11cb-75a4-482f-a92e-251e6a3a8a41", "metadata": {}, "outputs": [], "source": ["df_summary = df_base_list[\n", "    [\n", "        \"sto_id\",\n", "        \"item_id\",\n", "        \"sender_outlet_id\",\n", "        \"sender_outlet_name\",\n", "        \"receiving_outlet_id\",\n", "        \"receiver_outlet_name\",\n", "        \"billed_quantity\",\n", "        \"inward_quantity\",\n", "        \"grn_created_at\",\n", "    ]\n", "]\n", "\n", "df_summary[\"hour\"] = df_summary[\"grn_created_at\"].dt.hour\n", "df_summary"]}, {"cell_type": "code", "execution_count": null, "id": "5a78ec50-3bac-47aa-8633-5e70d448f13e", "metadata": {}, "outputs": [], "source": ["df_summary_main = (\n", "    df_summary.groupby(\n", "        [\n", "            \"sender_outlet_id\",\n", "            \"sender_outlet_name\",\n", "            \"receiving_outlet_id\",\n", "            \"receiver_outlet_name\",\n", "        ]\n", "    )\n", "    .agg({\"billed_quantity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "df_summary_main"]}, {"cell_type": "code", "execution_count": null, "id": "93a926b4-3f95-4a5e-a552-e63909a54132", "metadata": {}, "outputs": [], "source": ["df_summary_6 = df_summary[df_summary[\"grn_created_at\"].isna() == False]\n", "df_summary_6 = df_summary[df_summary[\"hour\"] < 6]\n", "df_summary_6 = (\n", "    df_summary_6.groupby([\"sender_outlet_id\", \"receiving_outlet_id\"])\n", "    .agg({\"inward_quantity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "df_summary_6 = df_summary_6.rename(columns={\"inward_quantity\": \"Before_6_GRN\"})\n", "df_summary_6"]}, {"cell_type": "code", "execution_count": null, "id": "897159d0-b22c-4153-8ee4-9481df3f6e21", "metadata": {}, "outputs": [], "source": ["df_summary_7 = df_summary[df_summary[\"grn_created_at\"].isna() == False]\n", "df_summary_7 = df_summary[df_summary[\"hour\"] < 7]\n", "df_summary_7 = (\n", "    df_summary_7.groupby([\"sender_outlet_id\", \"receiving_outlet_id\"])\n", "    .agg({\"inward_quantity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "df_summary_7 = df_summary_7.rename(columns={\"inward_quantity\": \"Before_7_GRN\"})\n", "df_summary_7"]}, {"cell_type": "code", "execution_count": null, "id": "2a611cc9-5cc8-448d-8fb0-82a9dc9495f8", "metadata": {}, "outputs": [], "source": ["df_summary_8 = df_summary[df_summary[\"grn_created_at\"].isna() == False]\n", "df_summary_8 = df_summary[df_summary[\"hour\"] < 8]\n", "df_summary_8 = (\n", "    df_summary_8.groupby([\"sender_outlet_id\", \"receiving_outlet_id\"])\n", "    .agg({\"inward_quantity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "df_summary_8 = df_summary_8.rename(columns={\"inward_quantity\": \"Before_8_GRN\"})\n", "df_summary_8"]}, {"cell_type": "markdown", "id": "ed50b0fd-4445-48f6-aba8-d1537dcc0693", "metadata": {}, "source": ["Total DS that did not Scan"]}, {"cell_type": "code", "execution_count": null, "id": "a7c15854-bafe-49c5-b385-79b83466d6a1", "metadata": {}, "outputs": [], "source": ["df_base_summary = df_base_alert[df_base_alert[\"trip_started_at\"].isna() == True]\n", "df_base_summary = (\n", "    df_base_summary.groupby([\"sender_outlet_id\"])\n", "    .agg({\"receiving_outlet_id\": \"count\"})\n", "    .reset_index()\n", ")\n", "df_base_summary = df_base_summary.rename(\n", "    columns={\"receiving_outlet_id\": \"Total DS that did not scan\"}\n", ")\n", "df_base_summary"]}, {"cell_type": "markdown", "id": "0b9d0c9e-c1f5-4532-901a-781ac79bc1db", "metadata": {}, "source": ["Total Eligible DS"]}, {"cell_type": "code", "execution_count": null, "id": "47e4eacb-2f89-485c-a6fe-cea8df4b9559", "metadata": {}, "outputs": [], "source": ["df_base_summary_eligible = df_base_alert.copy()\n", "df_base_summary_eligible = (\n", "    df_base_summary_eligible.groupby([\"sender_outlet_id\"])\n", "    .agg({\"receiving_outlet_id\": \"count\"})\n", "    .reset_index()\n", ")\n", "df_base_summary_eligible = df_base_summary_eligible.rename(\n", "    columns={\"receiving_outlet_id\": \"Total DS Trips\"}\n", ")\n", "df_base_summary_eligible"]}, {"cell_type": "code", "execution_count": null, "id": "92e46607-cd01-4505-83cf-09930ed28044", "metadata": {}, "outputs": [], "source": ["df_base_summary_truth = df_base_list[[\"sender_outlet_id\", \"receiving_outlet_id\"]].drop_duplicates()\n", "df_base_summary_truth = (\n", "    df_base_summary_truth.groupby([\"sender_outlet_id\"])\n", "    .agg({\"receiving_outlet_id\": \"count\"})\n", "    .reset_index()\n", ")\n", "df_base_summary_truth = df_base_summary_truth.rename(columns={\"receiving_outlet_id\": \"Total DS\"})\n", "df_base_summary_truth"]}, {"cell_type": "code", "execution_count": null, "id": "50ca56c1-41e1-4235-a306-370ca6a4ad96", "metadata": {}, "outputs": [], "source": ["df_publishing = pd.merge(\n", "    df_summary_main,\n", "    df_summary_6,\n", "    how=\"left\",\n", "    on=[\"sender_outlet_id\", \"receiving_outlet_id\"],\n", ")\n", "df_publishing = pd.merge(\n", "    df_publishing,\n", "    df_summary_7,\n", "    how=\"left\",\n", "    on=[\"sender_outlet_id\", \"receiving_outlet_id\"],\n", ")\n", "df_publishing = pd.merge(\n", "    df_publishing,\n", "    df_summary_8,\n", "    how=\"left\",\n", "    on=[\"sender_outlet_id\", \"receiving_outlet_id\"],\n", ")\n", "df_publishing[[\"Before_6_GRN\", \"Before_7_GRN\", \"Before_8_GRN\"]] = df_publishing[\n", "    [\"Before_6_GRN\", \"Before_7_GRN\", \"Before_8_GRN\"]\n", "].fillna(0)\n", "df_publishing[[\"Before_6_GRN\", \"Before_7_GRN\", \"Before_8_GRN\"]] = df_publishing[\n", "    [\"Before_6_GRN\", \"Before_7_GRN\", \"Before_8_GRN\"]\n", "].astype(int)\n", "df_publishing"]}, {"cell_type": "code", "execution_count": null, "id": "7eda2fea-bd07-4ec8-a0b0-fb6676bdb720", "metadata": {}, "outputs": [], "source": ["df_publishing_final = (\n", "    df_publishing.groupby([\"sender_outlet_id\", \"sender_outlet_name\"])\n", "    .agg(\n", "        {\n", "            \"billed_quantity\": \"sum\",\n", "            \"Before_6_GRN\": \"sum\",\n", "            \"Before_7_GRN\": \"sum\",\n", "            \"Before_8_GRN\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "df_publishing_final\n", "df_publishing_final = pd.merge(\n", "    df_publishing_final, df_base_summary_truth, how=\"left\", on=[\"sender_outlet_id\"]\n", ")\n", "df_publishing_final = pd.merge(\n", "    df_publishing_final, df_base_summary_eligible, how=\"left\", on=[\"sender_outlet_id\"]\n", ")\n", "df_publishing_final = pd.merge(\n", "    df_publishing_final, df_base_summary, how=\"left\", on=[\"sender_outlet_id\"]\n", ")\n", "df_publishing_final = df_publishing_final[\n", "    [\n", "        \"sender_outlet_id\",\n", "        \"sender_outlet_name\",\n", "        \"Total DS\",\n", "        \"Total DS Trips\",\n", "        \"Total DS that did not scan\",\n", "        \"billed_quantity\",\n", "        \"Before_6_GRN\",\n", "        \"Before_7_GRN\",\n", "        \"Before_8_GRN\",\n", "    ]\n", "]\n", "df_publishing_final[\"Total DS that did not scan\"] = df_publishing_final[\n", "    \"Total DS that did not scan\"\n", "].fillna(0)\n", "df_publishing_final"]}, {"cell_type": "code", "execution_count": null, "id": "daa20190-1825-40b7-b5ec-ad22af1308ac", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "a8a180c1-e94f-4e05-bb03-d6957009e2c6", "metadata": {}, "source": ["List of DS that did not <PERSON>an"]}, {"cell_type": "code", "execution_count": null, "id": "535b5fd0-96e0-482a-8bd1-8276be128957", "metadata": {}, "outputs": [], "source": ["df_not_scanned = df_base_alert[df_base_alert[\"trip_started_at\"].isna() == True]\n", "df_not_scanned"]}, {"cell_type": "markdown", "id": "022fb729-22f9-4f2e-abc1-ebe1e8645bee", "metadata": {}, "source": ["Vehicle Recieved on Time"]}, {"cell_type": "code", "execution_count": null, "id": "17e76ab1-f88c-40fa-8b2a-5fe206715b84", "metadata": {}, "outputs": [], "source": ["df_consignment = df_base_alert[df_base_alert[\"consignment_received_at\"].isna() == False]\n", "df_consignment[\"hour\"] = df_consignment[\"consignment_received_at\"].dt.hour\n", "df_consignment[\"minute\"] = df_consignment[\"consignment_received_at\"].dt.minute\n", "df_consignment[\"time_barrier\"] = df_consignment[\"hour\"] * 100 + df_consignment[\"minute\"]\n", "df_consignment[\"vehicle_on_time\"] = np.where(df_consignment[\"time_barrier\"] < 531, 1, 0)\n", "df_consignment"]}, {"cell_type": "markdown", "id": "bf345edd-19f4-4266-b6a1-a0da159fa839", "metadata": {}, "source": ["Late Vehicle"]}, {"cell_type": "code", "execution_count": null, "id": "69d5db7d-bd83-4f54-8a50-56ce71514fee", "metadata": {}, "outputs": [], "source": ["df_consignment_late = df_consignment[df_consignment[\"vehicle_on_time\"] == 0]\n", "ds_late_vehicle = df_consignment_late.copy()\n", "ds_late_vehicle = ds_late_vehicle[\n", "    [\n", "        \"sender_outlet_id\",\n", "        \"sender_outlet_name\",\n", "        \"receiving_outlet_id\",\n", "        \"receiver_outlet_name\",\n", "        \"trip_started_at\",\n", "        \"consignment_received_at\",\n", "        \"Grn_start_time\",\n", "        \"Grn_end_time\",\n", "    ]\n", "]\n", "df_consignment_late = (\n", "    df_consignment_late.groupby([\"sender_outlet_id\"])\n", "    .agg({\"receiving_outlet_id\": \"count\"})\n", "    .reset_index()\n", ")\n", "df_consignment_late = df_consignment_late.rename(\n", "    columns={\"receiving_outlet_id\": \"Count of Vehicle Arrived Late\"}\n", ")\n", "\n", "df_consignment_late"]}, {"cell_type": "code", "execution_count": null, "id": "45ebcd01-c5f8-40ef-82cf-644a0030dd9f", "metadata": {}, "outputs": [], "source": ["df_publishing_final = pd.merge(\n", "    df_publishing_final, df_consignment_late, how=\"left\", on=[\"sender_outlet_id\"]\n", ")\n", "df_publishing_final = df_publishing_final.fillna(0)\n", "df_publishing_final"]}, {"cell_type": "markdown", "id": "1116bf12-d8be-4d42-989c-ed48bc0313f0", "metadata": {}, "source": ["Push to Google Sheets"]}, {"cell_type": "code", "execution_count": null, "id": "db3b72ee-861a-4083-b97b-465b84b0f56a", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(df_base_alert, \"1Z_7RQr9vqXaSWCAJ-yQjcFdxi4-aLe616nkiRBNWWk4\", \"raw_data\")"]}, {"cell_type": "code", "execution_count": null, "id": "56a0b5b7-240c-4d88-aca1-7e2007fbc254", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f74b060e-b76d-4507-a5ae-d041d4f1fcee", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(df_not_scanned, \"1Z_7RQr9vqXaSWCAJ-yQjcFdxi4-aLe616nkiRBNWWk4\", \"DS_with_no_scan\")"]}, {"cell_type": "code", "execution_count": null, "id": "c8976788-251b-4c29-b3ec-e223fe0a1387", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(ds_late_vehicle, \"1Z_7RQr9vqXaSWCAJ-yQjcFdxi4-aLe616nkiRBNWWk4\", \"late_vehicles\")"]}, {"cell_type": "code", "execution_count": null, "id": "cf5b4fc8-b173-47f0-86f9-c90afcfa9999", "metadata": {}, "outputs": [], "source": ["df_no_grn = df_base_alert.copy()\n", "df_no_grn[\"send_alert\"] = np.where(\n", "    (df_no_grn[\"Grn_start_time\"].isna()) & (df_no_grn[\"Grn_end_time\"].isna()), 1, 0\n", ")\n", "\n", "df_no_grn = df_no_grn[df_no_grn[\"send_alert\"] == 1]\n", "df_no_grn = df_no_grn[\n", "    [\n", "        \"sender_outlet_id\",\n", "        \"sender_outlet_name\",\n", "        \"receiving_outlet_id\",\n", "        \"receiver_outlet_name\",\n", "        \"consignment_received_at\",\n", "        \"Grn_start_time\",\n", "        \"Grn_end_time\",\n", "    ]\n", "]\n", "\n", "df_no_grn = df_no_grn.fillna(\"not available\")\n", "df_no_grn"]}, {"cell_type": "code", "execution_count": null, "id": "bdcc6194-4020-4db5-bbb1-ef76fc0a406c", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(df_no_grn, \"1Z_7RQr9vqXaSWCAJ-yQjcFdxi4-aLe616nkiRBNWWk4\", \"DS_with_no_grn\")"]}, {"cell_type": "code", "execution_count": null, "id": "1741782a-dd57-46d7-8f35-a721b5dbdca0", "metadata": {}, "outputs": [], "source": ["df_not_recieved = df_base_alert[\n", "    (df_base_alert[\"consignment_received_at\"].isna() == True)\n", "    & (df_base_alert[\"trip_started_at\"].isna() == False)\n", "]\n", "df_not_recieved"]}, {"cell_type": "markdown", "id": "a5dad244-0580-4625-a6df-7255414bb0ec", "metadata": {}, "source": ["Adhoc_Requirements"]}, {"cell_type": "code", "execution_count": null, "id": "3d601623-fbfb-40bd-82ca-379cf5534d9b", "metadata": {}, "outputs": [], "source": ["df_summary_list = df_base_list[\n", "    [\n", "        \"sto_id\",\n", "        \"item_id\",\n", "        \"sender_outlet_id\",\n", "        \"sender_outlet_name\",\n", "        \"receiving_outlet_id\",\n", "        \"receiver_outlet_name\",\n", "        \"billed_quantity\",\n", "        \"inward_quantity\",\n", "        \"grn_created_at\",\n", "    ]\n", "]\n", "df_summary_list = pd.merge(\n", "    df_summary_list,\n", "    df_base_arrival,\n", "    how=\"left\",\n", "    on=[\"sender_outlet_id\", \"receiving_outlet_id\"],\n", ")\n", "df_summary_list[\"hour_grn\"] = df_summary_list[\"grn_created_at\"].dt.hour\n", "df_summary_list[\"hour\"] = df_summary_list[\"consignment_received_at\"].dt.hour\n", "df_summary_list[\"minute\"] = df_summary_list[\"consignment_received_at\"].dt.minute\n", "df_summary_list[\"time_barrier\"] = df_summary_list[\"hour\"] * 100 + df_summary_list[\"minute\"]\n", "df_summary_list[\"vehicle_on_time\"] = np.where(df_summary_list[\"time_barrier\"] < 531, 1, 0)\n", "df_summary_list"]}, {"cell_type": "markdown", "id": "4c1c2a5b-2c69-4076-ab76-e6cba0d496d9", "metadata": {}, "source": ["GRN DONE LATE WITH VEHICLE ON TIME"]}, {"cell_type": "code", "execution_count": null, "id": "646c8c49-0105-44e5-b191-c9e42f492be0", "metadata": {}, "outputs": [], "source": ["df_vehicle_timing = (\n", "    df_summary_list.groupby([\"sender_outlet_id\", \"receiving_outlet_id\"])\n", "    .agg({\"vehicle_on_time\": \"sum\"})\n", "    .reset_index()\n", ")\n", "df_vehicle_timing"]}, {"cell_type": "code", "execution_count": null, "id": "61531bd4-bf17-4474-a9b6-1286f368ecb1", "metadata": {}, "outputs": [], "source": ["df_summary_list = df_summary_list[df_summary_list[\"hour_grn\"] < 6]\n", "df_summary_list = (\n", "    df_summary_list.groupby([\"sender_outlet_id\", \"receiving_outlet_id\"])\n", "    .agg({\"inward_quantity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "df_summary_list = df_summary_list.rename(columns={\"inward_quantity\": \"Before_6_GRN\"})\n", "df_summary_list"]}, {"cell_type": "code", "execution_count": null, "id": "bf57c045-bddd-4e0b-808d-cb0e1c31e6df", "metadata": {}, "outputs": [], "source": ["df_grn_before_6_vehicle_on_time = pd.merge(\n", "    df_summary_main,\n", "    df_summary_list,\n", "    how=\"left\",\n", "    on=[\"sender_outlet_id\", \"receiving_outlet_id\"],\n", ")\n", "\n", "df_grn_before_6_vehicle_on_time = pd.merge(\n", "    df_grn_before_6_vehicle_on_time,\n", "    df_vehicle_timing,\n", "    how=\"left\",\n", "    on=[\"sender_outlet_id\", \"receiving_outlet_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ab20d067-5ceb-4c70-8e50-c3a6941cb2ea", "metadata": {}, "outputs": [], "source": ["df_grn_before_6_vehicle_on_time = df_grn_before_6_vehicle_on_time.fillna(0)\n", "df_grn_before_6_vehicle_on_time[\"percent_grn_before_6\"] = (\n", "    df_grn_before_6_vehicle_on_time[\"Before_6_GRN\"]\n", "    / df_grn_before_6_vehicle_on_time[\"billed_quantity\"]\n", ") * 100\n", "df_grn_before_6_vehicle_on_time"]}, {"cell_type": "code", "execution_count": null, "id": "f9314227-08cf-4d33-bff6-5c55afe21e09", "metadata": {"tags": []}, "outputs": [], "source": ["df_grn_less_before_6_vehicle_on_time = df_grn_before_6_vehicle_on_time[\n", "    df_grn_before_6_vehicle_on_time[\"percent_grn_before_6\"] < 90\n", "]\n", "\n", "df_grn_less_before_6_vehicle_on_time = df_grn_less_before_6_vehicle_on_time[\n", "    df_grn_less_before_6_vehicle_on_time[\"vehicle_on_time\"] > 0\n", "]\n", "df_grn_less_before_6_vehicle_on_time = df_grn_less_before_6_vehicle_on_time[\n", "    [\n", "        \"sender_outlet_id\",\n", "        \"sender_outlet_name\",\n", "        \"receiving_outlet_id\",\n", "        \"receiver_outlet_name\",\n", "        \"billed_quantity\",\n", "        \"Before_6_GRN\",\n", "        \"percent_grn_before_6\",\n", "    ]\n", "]\n", "\n", "\n", "df_grn_less_before_6_vehicle_on_time[\"percent_grn_before_6\"] = (\n", "    df_grn_less_before_6_vehicle_on_time[\"percent_grn_before_6\"].astype(\"str\") + \"%\"\n", ")\n", "\n", "df_grn_less_before_6_vehicle_on_time"]}, {"cell_type": "code", "execution_count": null, "id": "f13061a7-3bf8-4350-a238-3fca9bd4640a", "metadata": {}, "outputs": [], "source": ["df_grn_less_before_6_vehicle_on_time = pd.merge(\n", "    df_grn_less_before_6_vehicle_on_time,\n", "    df_base_arrival,\n", "    how=\"left\",\n", "    on=[\"sender_outlet_id\", \"receiving_outlet_id\"],\n", ")\n", "\n", "df_grn_less_before_6_vehicle_on_time = df_grn_less_before_6_vehicle_on_time[\n", "    [\n", "        \"sender_outlet_id\",\n", "        \"sender_outlet_name\",\n", "        \"receiving_outlet_id\",\n", "        \"receiver_outlet_name\",\n", "        \"billed_quantity\",\n", "        \"Before_6_GRN\",\n", "        \"percent_grn_before_6\",\n", "        \"consignment_received_at\",\n", "    ]\n", "]\n", "\n", "df_grn_less_before_6_vehicle_on_time"]}, {"cell_type": "markdown", "id": "986335af-0e69-4427-86b0-f9cbfba011b1", "metadata": {}, "source": ["Pushing Raw to Sheet"]}, {"cell_type": "code", "execution_count": null, "id": "dbeda624-8780-482d-a1b4-4b17222a774a", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    df_grn_less_before_6_vehicle_on_time,\n", "    \"1Z_7RQr9vqXaSWCAJ-yQjcFdxi4-aLe616nkiRBNWWk4\",\n", "    \"DS_with_late_GRN_&_Vehicle_on_time\",\n", ")"]}, {"cell_type": "markdown", "id": "fd1d2bed-88ce-4fd1-a869-676a6ba274b3", "metadata": {}, "source": [" "]}, {"cell_type": "code", "execution_count": null, "id": "4b6fbc98-42d5-40a7-98c1-9d618d741bbe", "metadata": {}, "outputs": [], "source": ["df_grn_less_before_6_vehicle_on_time = (\n", "    df_grn_less_before_6_vehicle_on_time.groupby([\"sender_outlet_id\", \"sender_outlet_name\"])\n", "    .agg({\"receiving_outlet_id\": \"count\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7f750f24-1946-4a06-92d5-408ac5597c03", "metadata": {}, "outputs": [], "source": ["df_grn_less_before_6_vehicle_on_time = df_grn_less_before_6_vehicle_on_time.rename(\n", "    columns={\"receiving_outlet_id\": \"Number of DS with GRN after 6 but vehicle on time\"}\n", ")\n", "df_grn_less_before_6_vehicle_on_time"]}, {"cell_type": "code", "execution_count": null, "id": "e6a19daf-6873-4f6e-ac78-c05a77f79bdb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1127f876-cdd7-4b8c-bce4-baf1750291a8", "metadata": {}, "outputs": [], "source": ["df_grn_before_6_vehicle_on_time_final = pd.merge(\n", "    df_publishing_final,\n", "    df_grn_less_before_6_vehicle_on_time,\n", "    how=\"left\",\n", "    on=[\"sender_outlet_id\", \"sender_outlet_name\"],\n", ")\n", "\n", "df_grn_before_6_vehicle_on_time_final[\n", "    \"Number of DS with GRN after 6 but vehicle on time\"\n", "] = df_grn_before_6_vehicle_on_time_final[\n", "    \"Number of DS with GRN after 6 but vehicle on time\"\n", "].fillna(\n", "    0\n", ")\n", "df_grn_before_6_vehicle_on_time_final"]}, {"cell_type": "code", "execution_count": null, "id": "c52bc737-edbb-4b36-8907-39175e241151", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    df_grn_before_6_vehicle_on_time_final,\n", "    \"1Z_7RQr9vqXaSWCAJ-yQjcFdxi4-aLe616nkiRBNWWk4\",\n", "    \"SUMMARY\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d640f69f-4d02-4875-b1d9-96e174f06a6a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "deff1e60-00d6-42f4-9920-79cd00fe4b99", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4bb2f7bc-fe74-40f8-894e-a5b856140658", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c641cde1-f933-4d58-896e-556c97450840", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8aaea694-b35e-4de8-b4c9-43ea8d6e3835", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3ca6eadc-4c0e-481f-8489-3fd8cf3351eb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "083bbd75-8cd6-4ecf-b261-c170d30d864d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "119c0d44-74ca-4ced-aaf3-e30eeb53276d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "836dc273-ee5b-426e-b82c-6829ec38b218", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "18b744bf-39f8-4676-95a9-fa37b82d1602", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1bc6abdd-b943-43b5-a318-748aba2e878a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "99692ff0-2ff5-4ef9-b526-64779a6ab00e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f882d045-b5a8-4a45-a5e5-6001df1d4f34", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "daf3f7cb-7cf8-4bac-a1d7-84f8a1b3ae2b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9e059550-f07e-4b50-a547-e6ee1e970e48", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "22cdc61e-368a-4030-9e14-252b173e8d6a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5dd21f1e-c5d9-4a83-8790-d49e3edb9509", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "579d8abe-58c6-4ab3-b94d-0c05aa13b620", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}