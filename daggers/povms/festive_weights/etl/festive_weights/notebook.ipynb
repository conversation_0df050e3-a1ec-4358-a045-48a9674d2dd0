{"cells": [{"cell_type": "code", "execution_count": null, "id": "9f518748-f34f-4dfb-bfca-90a91486a44e", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "import json\n", "import shutil\n", "\n", "import boto3\n", "import io\n", "\n", "import uuid\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import gc\n", "import os\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "6fd3ae22-62cc-4066-ab66-03a4191019e9", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "012326c5-0722-48b1-8ad1-e21c7e149df6", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "def to_trino(df_to_trino, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)\n", "\n", "\n", "def gsheet_to_df(sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            df = pb.from_sheets(sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Read Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Read {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and created a dataframe of size {size}\"\n", "            )\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "d5e592e7-d72d-40ad-b0dc-4566ca0692e3", "metadata": {}, "outputs": [], "source": ["run_local = False"]}, {"cell_type": "markdown", "id": "98227e49-751e-497d-ae9f-78ffd1d8a1fa", "metadata": {}, "source": ["### Reading Festive Sheet"]}, {"cell_type": "code", "execution_count": null, "id": "4a9fe7db-4586-48e3-a590-171c9e855d59", "metadata": {}, "outputs": [], "source": ["if run_local:\n", "    input_sheet = pd.read_csv(\"input.csv\")\n", "else:\n", "    input_sheet = gsheet_to_df(\"1dL1grhTKlu5RcGH4T0It1Rg5y3MnKxweCKu8ssWixC8\", \"date_input\")"]}, {"cell_type": "markdown", "id": "3e09949a-e26c-47c6-9779-8e8e4c5dbaac", "metadata": {}, "source": ["### Whether to update only for the list of festivals"]}, {"cell_type": "code", "execution_count": null, "id": "a5301e8a-05e9-4d64-94f3-aaada89fbc40", "metadata": {}, "outputs": [], "source": ["if run_local:\n", "    manual = pd.read_csv(\"manual.csv\")\n", "    list_of_festivals = pd.read_csv(\"list_of_festivals.csv\")\n", "else:\n", "    manual = gsheet_to_df(\"1zeYlST37g5ig8u5DwCvSVoGZtI6VgfJMWgIGCW-w3Cs\", \"manual\")\n", "    list_of_festivals = gsheet_to_df(\n", "        \"1zeYlST37g5ig8u5DwCvSVoGZtI6VgfJMWgIGCW-w3Cs\", \"list_of_festivals\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "85cdd2e4-0a85-4409-b26d-b8626d7762b1", "metadata": {}, "outputs": [], "source": ["if manual[\"manual\"].iloc[0] == \"yes\":\n", "    festive_tuple_raw = tuple(list_of_festivals[\"festival\"].to_numpy())\n", "    festive_tuple = tuple(s.strip() for s in festive_tuple_raw)"]}, {"cell_type": "code", "execution_count": null, "id": "a60dd447-7f09-4f8a-82c5-5c5b6a0581bf", "metadata": {}, "outputs": [], "source": ["festive_tuple"]}, {"cell_type": "code", "execution_count": null, "id": "5a926e0c-e42e-43c8-9988-69c8c306bef2", "metadata": {}, "outputs": [], "source": ["input_fil = input_sheet[input_sheet[\"festival_name_in_bulk_upload\"].isin(festive_tuple)].copy()"]}, {"cell_type": "code", "execution_count": null, "id": "f7ef0c0b-418a-48c9-bb8a-d28c06205088", "metadata": {}, "outputs": [], "source": ["input_fil[\"start\"] = pd.to_datetime(input_fil[\"sale_start_date\"], format=\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "0d840501-4b06-472f-b625-b47b6eb876d8", "metadata": {}, "outputs": [], "source": ["input_fil_new = input_fil[\n", "    input_fil[\"start\"] <= pd.Timestamp.today().normalize() + pd.Timedelta(days=150)\n", "].copy()"]}, {"cell_type": "code", "execution_count": null, "id": "b40225e0-3d47-4112-8d67-36f62a48a7bb", "metadata": {}, "outputs": [], "source": ["col = [\"festival_name_in_bulk_upload\", \"sale_start_date\"]"]}, {"cell_type": "code", "execution_count": null, "id": "98cfc0ed-1ffc-4a5f-94af-4d53503307d2", "metadata": {}, "outputs": [], "source": ["input_fil_new[col]"]}, {"cell_type": "markdown", "id": "b4a392fa-de46-43e0-83e5-c6ee7ce33d59", "metadata": {}, "source": ["column_dtypes_interim = [\n", "    {\"name\": \"festival_name_in_bulk_upload\", \"type\": \"varchar\", \"description\": \"Name of the festival as entered in bulk upload\"},\n", "    {\"name\": \"sale_start_date\", \"type\": \"varchar\", \"description\": \"Start date of the sale period\"},\n", "]"]}, {"cell_type": "markdown", "id": "f1ed6d67-0275-4e1b-8442-4e1266333ad8", "metadata": {}, "source": ["kwargs_interim = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"festive_sale_start\",\n", "    \"column_dtypes\": column_dtypes_interim,\n", "    \"primary_key\": [\n", "        \"festival_name_in_bulk_upload\",\n", "        \"sale_start_date\"\n", "    ],\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"this is interim table\"\n", "}\n"]}, {"cell_type": "markdown", "id": "dcf687f5-b07e-4edc-8f38-8ddc2111d0ae", "metadata": {}, "source": ["to_trino(input_fil_new[col], **kwargs_interim)"]}, {"cell_type": "markdown", "id": "4d958481-dd51-483d-9dbe-2d1d6f30d3b8", "metadata": {}, "source": ["### City_Item_Weights"]}, {"cell_type": "code", "execution_count": null, "id": "68c892f5-69bb-43e0-a461-54bafc2a4cc6", "metadata": {}, "outputs": [], "source": ["def city_item_weights():\n", "    avl = f\"\"\"\n", "        SELECT \n", "          CONCAT(name, '_', cast(start_date as varchar)) AS event_name,\n", "          eoid.event_id\n", "      FROM rpc.supply_event_info sei\n", "      JOIN rpc.supply_event se ON se.id = sei.supply_event_id\n", "      join ars.event_outlet_item_distribution eoid on eoid.event_id = sei.id\n", "      WHERE sei.active = TRUE\n", "        AND se.active = TRUE\n", "        and CONCAT(name, '_', cast(start_date as varchar)) in  {festive_tuple}\n", "        and eoid.partition_field is not null\n", "        group by 1,2\n", "    \"\"\"\n", "    sql = f\"\"\"\n", "    with  event_name as \n", "    (\n", "      SELECT sei.id AS id,\n", "          CONCAT(name, '_', cast(start_date as varchar)) AS event_name\n", "      FROM rpc.supply_event_info sei\n", "      JOIN rpc.supply_event se ON se.id = sei.supply_event_id\n", "      WHERE sei.active = TRUE\n", "        AND se.active = TRUE\n", "        and CONCAT(name, '_', cast(start_date as varchar)) in  {festive_tuple}\n", "     ),\n", "   \n", "    plan_qty_raw as \n", "    (\n", "    select\n", "      eoid.event_id,\n", "      en.event_name,\n", "      od.city_name,\n", "      outlet_id,\n", "      eoid.item_id,\n", "      id.p_type,\n", "      sum(quantity) as plan_qty\n", "    from\n", "      ars.event_outlet_item_distribution eoid\n", "      inner join supply_etls.item_details id on eoid.item_id = id.item_id\n", "      and id.assortment_type = 'Packaged Goods'\n", "      inner join (\n", "        select\n", "          hot_outlet_id,\n", "          city_name\n", "        from\n", "          supply_etls.outlet_details\n", "        where\n", "          ars_active = 1\n", "          and business_type_id = 7\n", "      ) od on od.hot_outlet_id = eoid.outlet_id\n", "      inner join\n", "      event_name en on en.id = eoid.event_id\n", "      where partition_field is not null\n", "\n", "    --   where event_id = (select id from event_name) and partition_field is not null -- event_id = 14 is for <PERSON><PERSON>\n", "    group by\n", "      1,2,3,4,5,6\n", "    ),\n", "\n", "    festive_weight_city as \n", "    (\n", "    select\n", "      event_id,\n", "      event_name,\n", "      city_name,\n", "      item_id,\n", "      sum(plan_qty) as tot_qty\n", "    from\n", "      plan_qty_raw\n", "    group by\n", "      1,2,3,4\n", "    ),\n", "    festive_weight_city_sum as \n", "    (\n", "    select\n", "      event_id,\n", "      event_name,\n", "      city_name,\n", "      sum(tot_qty) as final_qty\n", "    from\n", "      festive_weight_city\n", "    group by\n", "      1,2,3\n", "    ),\n", "    festive_ciw as \n", "    (\n", "    select\n", "      fwc.event_id,\n", "      fwc.event_name,\n", "      fwc.city_name,\n", "      item_id,\n", "      fwc.tot_qty,\n", "      fwcs.final_qty,\n", "      cast(fwc.tot_qty as double) / fwcs.final_qty as weight\n", "    from\n", "      festive_weight_city fwc\n", "      inner join festive_weight_city_sum fwcs on fwc.city_name = fwcs.city_name and fwc.event_id = fwcs.event_id\n", "    )\n", "    select \n", "    fc.event_name,\n", "    fc.city_name,\n", "    fc.item_id,\n", "    round(coalesce(fc.weight,0),10) as weight,\n", "    cast(current_timestamp AT TIME ZONE 'Asia/Kolkata' as timestamp) as updated_at_ist\n", "    from\n", "    festive_ciw fc \n", "    \"\"\"\n", "    # df = pd.read_sql_query(sql,CON_TRINO)\n", "    column_dtypes = [\n", "        {\"name\": \"event_name\", \"type\": \"varchar\", \"description\": \"Name of the event\"},\n", "        {\n", "            \"name\": \"city_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Name of the city -- tax city location\",\n", "        },\n", "        {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"Unique identifier for the item\"},\n", "        {\"name\": \"weight\", \"type\": \"real\", \"description\": \"Weight of the item\"},\n", "        {\n", "            \"name\": \"updated_at_ist\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"Timestamp of the last update in IST\",\n", "        },\n", "    ]\n", "    kwargs = {\n", "        \"schema_name\": \"supply_etls\",\n", "        \"table_name\": \"festive_city_item_weight\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"event_name\", \"city_name\", \"item_id\"],\n", "        \"partition_key\": [\"event_name\"],\n", "        \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "        \"table_description\": \"It has Festive City Item Weights in it\",\n", "    }\n", "    to_trino(sql, **kwargs)\n", "    df = read_sql_query(avl, CON_TRINO)\n", "    print(\n", "        f\"\\U0001f60e \\U0001f60e \\U0001f60e City Festive Weights are pushed into table successfully \\U0001f680\"\n", "    )\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "7834b1d0-c8e3-4afb-b7f9-5c4e7792a289", "metadata": {}, "outputs": [], "source": ["if manual[\"city\"].iloc[0] == \"yes\":\n", "    avl_df = city_item_weights()"]}, {"cell_type": "code", "execution_count": null, "id": "f83334e1-ac8a-403a-aae0-f4d87ea56ea1", "metadata": {}, "outputs": [], "source": ["if manual[\"city\"].iloc[0] == \"yes\":\n", "    avl_df"]}, {"cell_type": "code", "execution_count": null, "id": "6cda8b9c-d914-4850-b981-1c6c2f7e67a2", "metadata": {}, "outputs": [], "source": ["if manual[\"city\"].iloc[0] == \"yes\":\n", "    avl_tuple = tuple(avl_df[\"event_name\"].to_numpy())\n", "    print(f\"Data available only for {avl_tuple}\")"]}, {"cell_type": "markdown", "id": "bd7ec18c-c337-4914-b47d-227934c2208c", "metadata": {}, "source": ["### Backend Item Weights Festive"]}, {"cell_type": "markdown", "id": "464c9141-a587-48e3-94be-b4fd1b4099e6", "metadata": {}, "source": ["For every festival -- logic runs separately."]}, {"cell_type": "code", "execution_count": null, "id": "0f4f2c11-b6cf-44df-8b8a-a17c3f17df1e", "metadata": {}, "outputs": [], "source": ["list_of_festivals.head()"]}, {"cell_type": "code", "execution_count": null, "id": "dd2c5e9b-a5fd-4932-a5af-d5bf9a1baffe", "metadata": {}, "outputs": [], "source": ["list_of_festivals[\"festival\"] = list_of_festivals[\"festival\"].str.strip()"]}, {"cell_type": "code", "execution_count": null, "id": "20e4662f-dc0a-4002-acde-3254e174e3d6", "metadata": {}, "outputs": [], "source": ["if manual[\"backend\"].iloc[0] == \"yes\":\n", "    for i in festive_tuple:\n", "        tea_date = list_of_festivals[list_of_festivals[\"festival\"] == i][\"tea_date\"].iloc[0]\n", "        # tea_date = input_fil_new[col][input_fil_new[col][\"festival_name_in_bulk_upload\"] == i][\n", "        #     \"sale_start_date\"\n", "        # ].iloc[0]\n", "        festive_name = i\n", "        avl = f\"\"\"\n", "            SELECT \n", "              CONCAT(name, '_', cast(start_date as varchar)) AS event_name,\n", "              eoid.event_id\n", "          FROM rpc.supply_event_info sei\n", "          JOIN rpc.supply_event se ON se.id = sei.supply_event_id\n", "          join ars.event_outlet_item_distribution eoid on eoid.event_id = sei.id\n", "          WHERE sei.active = TRUE\n", "            AND se.active = TRUE\n", "            and CONCAT(name, '_', cast(start_date as varchar)) = '{festive_name}'\n", "            and eoid.partition_field is not null\n", "            group by 1,2\n", "        \"\"\"\n", "        dummy = read_sql_query(avl, CON_TRINO)\n", "        if dummy.empty:\n", "            print(f\"no data for {festive_name}\")\n", "            continue\n", "        sql = f\"\"\"\n", "        with  event_name as \n", "          (\n", "            SELECT sei.id AS id,\n", "                CONCAT(name, '_', cast(start_date as varchar)) AS event_name\n", "                -- cast(fss.sale_start_date as date) as sale_start_date\n", "            FROM rpc.supply_event_info sei\n", "            JOIN rpc.supply_event se ON se.id = sei.supply_event_id\n", "            -- JOIN blinkit_staging.interim.festive_sale_start fss on fss.festival_name_in_bulk_upload = CONCAT(se.name, '_', cast(sei.start_date as varchar))\n", "            WHERE sei.active = TRUE\n", "              AND se.active = TRUE\n", "              and CONCAT(name, '_', cast(start_date as varchar)) = '{festive_name}'\n", "           ),\n", "\n", "        plan_qty_raw as \n", "        (\n", "        select\n", "          eoid.event_id,\n", "          en.event_name,\n", "        --   en.sale_start_date,\n", "          od.city_name,\n", "          outlet_id,\n", "          eoid.item_id,\n", "          id.p_type,\n", "          sum(quantity) as plan_qty\n", "        from\n", "          ars.event_outlet_item_distribution eoid\n", "          inner join supply_etls.item_details id on eoid.item_id = id.item_id\n", "          and id.assortment_type = 'Packaged Goods'\n", "          inner join (\n", "            select\n", "              hot_outlet_id,\n", "              city_name\n", "            from\n", "              supply_etls.outlet_details\n", "            where\n", "              ars_active = 1\n", "              and business_type_id = 7\n", "          ) od on od.hot_outlet_id = eoid.outlet_id\n", "\n", "          inner join\n", "          event_name en on en.id = eoid.event_id\n", "          where eoid.partition_field is not null\n", "        --   where event_id = (select id from event_name) and partition_field is not null -- event_id = 14 is for <PERSON><PERSON>\n", "        group by\n", "          1,2,3,4,5,6\n", "        ),\n", "\n", "        fmt as (\n", "        select\n", "          pqr.event_name,\n", "          pqr.event_id,\n", "          irm.be_facility_id,\n", "          pqr.outlet_id,\n", "          pqr.item_id,\n", "          cast(pqr.plan_qty as double) as plan_qty\n", "        from\n", "          blinkit_iceberg.supply_etls.inventory_replenishment_metrics irm\n", "          inner join \n", "          plan_qty_raw pqr on pqr.item_id = irm.item_id and pqr.outlet_id = irm.fe_hot_outlet_id\n", "        where\n", "          irm.insert_ds_ist = date '{tea_date}'\n", "          and \n", "          irm.hour_  = (select max(hour_) from blinkit_iceberg.supply_etls.inventory_replenishment_metrics\n", "          where insert_ds_ist = date '{tea_date}')\n", "          and (\n", "            active_outlet_flag = 1\n", "            or active_outlet_flag is null\n", "          )\n", "          and item_substate in (1, 3)\n", "          group by \n", "          1, 2, 3, 4, 5, 6\n", "        ),\n", "\n", "        festive_weight_sum as (\n", "        select\n", "          fmt.event_name,\n", "          fmt.event_id,\n", "          fmt.be_facility_id,\n", "          cast(sum(plan_qty) as double) as sum_qty\n", "        from\n", "          fmt\n", "        group by\n", "          1,2,3\n", "        ),\n", "\n", "        festive_weight as (\n", "        select \n", "        fmt.event_name,\n", "        fmt.be_facility_id,\n", "        fmt.item_id,\n", "        round(coalesce(fmt.plan_qty/nullif(fws.sum_qty,0),0),10) as weight\n", "        from fmt inner join\n", "        festive_weight_sum fws on\n", "        fmt.event_id = fws.event_id\n", "        and fmt.be_facility_id = fws.be_facility_id\n", "\n", "        )\n", "          select event_name, be_facility_id, item_id,\n", "          weight, cast(current_timestamp AT TIME ZONE 'Asia/Kolkata' as timestamp) as updated_at_ist\n", "          from festive_weight \n", "        \"\"\"\n", "        column_dtypes = [\n", "            {\"name\": \"event_name\", \"type\": \"varchar\", \"description\": \"Name of the event\"},\n", "            {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"be_facility_id\"},\n", "            {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"Unique identifier for the item\"},\n", "            {\"name\": \"weight\", \"type\": \"real\", \"description\": \"Weight of the item\"},\n", "            {\n", "                \"name\": \"updated_at_ist\",\n", "                \"type\": \"timestamp\",\n", "                \"description\": \"Timestamp of the last update in IST\",\n", "            },\n", "        ]\n", "        kwargs = {\n", "            \"schema_name\": \"supply_etls\",\n", "            \"table_name\": \"festive_backend_item_weight\",\n", "            \"column_dtypes\": column_dtypes,\n", "            \"primary_key\": [\"event_name\", \"be_facility_id\", \"item_id\"],\n", "            \"partition_key\": [\"event_name\"],\n", "            \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "            \"table_description\": \"It has Festive Backend Item Weights in it\",\n", "        }\n", "        to_trino(sql, **kwargs)\n", "        print(f\"done for the event {festive_name}\")\n", "    print(\n", "        f\"\\U0001f60e \\U0001f60e \\U0001f60e Backend Festive Weights are pushed into table successfully \\U0001f680\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "f3f46b89-ba09-4652-9039-97ca4e9d41a6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}