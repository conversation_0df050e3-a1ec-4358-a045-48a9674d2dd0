{"cells": [{"cell_type": "code", "execution_count": null, "id": "b7a5e1cf-57cc-4368-b7e7-6fed43f22e15", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import datetime as dt\n", "import time\n", "import numpy as np\n", "from calendar import monthrange\n", "from datetime import timedelta, datetime, date\n", "import gc\n", "import warnings\n", "import math\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "ba197eda-0063-4a96-81a1-952ae467eaaf", "metadata": {}, "outputs": [], "source": ["CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "198340be-6036-4fc3-a15e-bf982c2a7d00", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)\n", "pd.set_option(\"display.float_format\", lambda x: \"%.3f\" % x)\n", "# pd.set_option(\"display.max_rows\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "27785d02-2f05-43fc-9230-71ef1ab2b534", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 1\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", round((end - start) / 60, 2), \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "2bd56433-f145-4a45-b45b-eeb369c88c12", "metadata": {}, "outputs": [], "source": ["df = pb.from_sheets(\n", "    sheetid=\"1W10L4H5OAJ8bEp2QTBMrE-tD7Jd8CAgREK0UaAN4fyg\", sheetname=\"backfill\"\n", ")\n", "backfill = df.iloc[:, 0][0]"]}, {"cell_type": "code", "execution_count": null, "id": "9d758217-c6c6-4c27-9df1-4fdf56084a7c", "metadata": {}, "outputs": [], "source": ["if backfill == \"yes\":\n", "    start_date = df.iloc[:, 1][0]\n", "else:\n", "    start_date = (\n", "        pd.to_datetime(datetime.now()) + timedelta(hours=5.5) - timedelta(days=7)\n", "    ).strftime(\"%Y-%m-%d\")\n", "\n", "end_date = (\n", "    pd.to_datetime(start_date) + timedelta(hours=5.5) + timedelta(days=6)\n", ").strftime(\"%Y-%m-%d\")\n", "\n", "start_date, end_date"]}, {"cell_type": "markdown", "id": "9472a46b-0631-4dfe-9823-17cefda6d39d", "metadata": {"tags": []}, "source": ["## User Metrics"]}, {"cell_type": "code", "execution_count": null, "id": "28eecf08-0950-4c3e-9f5c-5d3ee21ab953", "metadata": {}, "outputs": [], "source": ["l1_customer_query = f\"\"\"\n", "    WITH base AS (\n", "        SELECT DATE(a.cart_checkout_ts_ist) AS date_, a.cart_checkout_ts_ist, cl.name AS city, a.outlet_id, a.dim_master_customer_key, a.cart_id, \n", "        l0, l1, cart_rank, SUM(product_quantity) AS product_quantity\n", "        FROM dwh.fact_sales_order_details a \n", "        JOIN dwh.fact_sales_order_item_details b on a.cart_id = b.cart_id\n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = b.product_id\n", "        LEFT JOIN lake_rpc.item_category_details icd ON icd.item_id = ipom.item_id\n", "        JOIN lake_retail.console_outlet co ON co.id = a.outlet_id AND co.active = 1 AND co.business_type_id = 7\n", "        LEFT JOIN lake_retail.console_location cl ON cl.id = co.tax_location_id\n", "        WHERE a.cart_checkout_ts_ist >= DATE('{start_date}') - interval '270 days'\n", "        GROUP BY 1,2,3,4,5,6,7,8,9\n", "    ),\n", "\n", "    new_base AS (\n", "        SELECT city, l0, l1, dim_master_customer_key, COUNT(DISTINCT cart_id) AS order_count\n", "        FROM base\n", "        GROUP BY 1,2,3,4\n", "    ),\n", "\n", "    ntc AS (\n", "        WITH rand_base AS (\n", "            SELECT city, l0, l1, dim_master_customer_key \n", "            FROM new_base\n", "            WHERE order_count = 1 \n", "            GROUP BY 1,2,3,4\n", "        )\n", "\n", "        SELECT b.date_, b.city, b.l0, b.l1, COUNT(DISTINCT b.dim_master_customer_key) AS cust_cnt_ntc\n", "        FROM base b\n", "        JOIN rand_base nb ON nb.l1 = b.l1 AND nb.l0 = b.l0 AND nb.city = b.city AND nb.dim_master_customer_key = b.dim_master_customer_key\n", "        WHERE date_ BETWEEN DATE('{start_date}') AND DATE('{end_date}')\n", "        GROUP BY 1,2,3,4\n", "    ),\n", "\n", "    ntp AS (\n", "        SELECT date_, city, l0, l1, COUNT(DISTINCT CASE WHEN cart_rank = 1 THEN dim_master_customer_key END) AS new_cust_ntp\n", "        FROM base\n", "        WHERE date_ BETWEEN DATE('{start_date}') AND DATE('{end_date}')\n", "        GROUP BY 1,2,3,4\n", "    ),\n", "\n", "    collated_base as (\n", "        SELECT date_, city, l0, l1 \n", "        FROM ntc \n", "        GROUP BY 1,2,3,4\n", "        UNION\n", "        SELECT date_, city, l0, l1\n", "        FROM ntp \n", "        GROUP BY 1,2,3,4\n", "    )\n", "\n", "    SELECT cb.date_, cb.city, cb.l0, cb.l1, cust_cnt_ntc, new_cust_ntp\n", "    FROM collated_base cb\n", "    LEFT JOIN ntp ON ntp.date_ = cb.date_ AND ntp.city = cb.city AND ntp.l1 = cb.l1 AND ntp.l0 = cb.l0\n", "    LEFT JOIN ntc ON ntc.date_ = cb.date_ AND ntc.city = cb.city AND ntc.l1 = cb.l1 AND ntc.l0 = cb.l0\n", "    WHERE cb.city IS NOT NULL\n", "\"\"\"\n", "l1_customer_df = read_sql_query(l1_customer_query, CON_REDSHIFT)\n", "\n", "l0_customer_query = f\"\"\"\n", "    WITH base AS (\n", "        SELECT DATE(a.cart_checkout_ts_ist) AS date_, a.cart_checkout_ts_ist, cl.name AS city, a.outlet_id, a.dim_master_customer_key, a.cart_id, l0, cart_rank, SUM(product_quantity) AS product_quantity\n", "        FROM dwh.fact_sales_order_details a \n", "        JOIN dwh.fact_sales_order_item_details b on a.cart_id = b.cart_id\n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = b.product_id\n", "        LEFT JOIN lake_rpc.item_category_details icd ON icd.item_id = ipom.item_id\n", "        JOIN lake_retail.console_outlet co ON co.id = a.outlet_id AND co.active = 1 AND co.business_type_id = 7\n", "        LEFT JOIN lake_retail.console_location cl ON cl.id = co.tax_location_id\n", "        WHERE a.cart_checkout_ts_ist >= DATE('{start_date}') - interval '365 days'\n", "        GROUP BY 1,2,3,4,5,6,7,8\n", "    ),\n", "\n", "    new_base AS (\n", "        SELECT city, l0, dim_master_customer_key, COUNT(DISTINCT cart_id) AS order_count\n", "        FROM base\n", "        GROUP BY 1,2,3\n", "    ),\n", "\n", "    ntc AS (\n", "        WITH rand_base AS (\n", "            SELECT city, l0, dim_master_customer_key \n", "            FROM new_base\n", "            WHERE order_count = 1 \n", "            GROUP BY 1,2,3\n", "        )\n", "\n", "        SELECT b.date_, b.city, b.l0, COUNT(DISTINCT b.dim_master_customer_key) AS cust_cnt_ntc\n", "        FROM base b\n", "        JOIN rand_base nb ON nb.l0 = b.l0 AND nb.city = b.city AND nb.dim_master_customer_key = b.dim_master_customer_key\n", "        WHERE date_ BETWEEN DATE('{start_date}') AND DATE('{end_date}')\n", "        GROUP BY 1,2,3\n", "    ),\n", "\n", "    ntp AS (\n", "        SELECT date_, city, l0, COUNT(DISTINCT CASE WHEN cart_rank = 1 THEN dim_master_customer_key END) AS new_cust_ntp\n", "        FROM base\n", "        WHERE date_ BETWEEN DATE('{start_date}') AND DATE('{end_date}')\n", "        GROUP BY 1,2,3\n", "    ),\n", "\n", "    collated_base as (\n", "        SELECT date_, city, l0 \n", "        FROM ntc \n", "        GROUP BY 1,2,3\n", "        UNION\n", "        SELECT date_, city, l0\n", "        FROM ntp \n", "        GROUP BY 1,2,3\n", "    )\n", "\n", "    SELECT cb.date_, cb.city, cb.l0, 'All' AS l1, cust_cnt_ntc, new_cust_ntp\n", "    FROM collated_base cb\n", "    LEFT JOIN ntp ON ntp.date_ = cb.date_ AND ntp.city = cb.city AND ntp.l0 = cb.l0\n", "    LEFT JOIN ntc ON ntc.date_ = cb.date_ AND ntc.city = cb.city AND ntc.l0 = cb.l0\n", "    WHERE cb.city IS NOT NULL\n", "\"\"\"\n", "l0_customer_df = read_sql_query(l0_customer_query, CON_REDSHIFT)\n", "\n", "customer_df = pd.concat([l1_customer_df, l0_customer_df])\n", "customer_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "5d00741f-e09f-4a93-bd73-32efbce39a35", "metadata": {}, "outputs": [], "source": ["customer_pan_df = (\n", "    customer_df.groupby([\"l0\", \"l1\", \"date_\"])\n", "    .agg({\"cust_cnt_ntc\": \"sum\", \"new_cust_ntp\": \"sum\"})\n", "    .reset_index()\n", ")\n", "customer_pan_df[\"city\"] = \"Pan India\"\n", "\n", "customer_pan_df = customer_pan_df[\n", "    [\"city\", \"l0\", \"l1\", \"date_\", \"cust_cnt_ntc\", \"new_cust_ntp\"]\n", "]\n", "\n", "customer_agg_df = pd.concat([customer_df, customer_pan_df])\n", "\n", "customer_agg_df = customer_agg_df.rename(\n", "    columns={\"cust_cnt_ntc\": \"ntc\", \"new_cust_ntp\": \"ntp\"}\n", ")\n", "customer_agg_df[[\"ntc\", \"ntp\"]] = customer_agg_df[[\"ntc\", \"ntp\"]].fillna(0).astype(int)\n", "customer_agg_df.head(1)"]}, {"cell_type": "markdown", "id": "0c0134d0-e829-4c26-bcea-42fb96a3b872", "metadata": {"tags": []}, "source": ["## Revenue Metrics"]}, {"cell_type": "code", "execution_count": null, "id": "45b74236-64aa-4119-8dd5-009377a02fb9", "metadata": {}, "outputs": [], "source": ["sales_query = f\"\"\"\n", "    WITH sales_base AS (\n", "        WITH item_mapping as (\n", "            SELECT DISTINCT ipr.product_id, \n", "            CASE \n", "                WHEN ipr.item_id IS NULL THEN ipom_0.item_id \n", "                ELSE ipr.item_id \n", "            END AS item_id, \n", "            CASE \n", "                WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1) \n", "                ELSE COALESCE(ipom_0.multiplier,1) \n", "            END AS multiplier\n", "            FROM lake_rpc.item_product_mapping ipr \n", "            LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id \n", "            LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", "        ),\n", "\n", "        sales AS (\n", "            SELECT DATE(oid.cart_checkout_ts_ist) AS order_date, oid.outlet_id, oid.product_id, im.item_id, oid.cart_id AS order_id, im.multiplier, \n", "            ((oid.procured_quantity - oid.total_doorstep_return_quantity) * im.multiplier) AS sales_quantity,\n", "            sales_quantity * (oid.unit_selling_price * 1.00000 / im.multiplier) AS selling_gmv,\n", "            sales_quantity * (oid.unit_mrp * 1.00000 / im.multiplier) AS mrp_gmv,\n", "            sales_quantity * (oid.unit_retained_margin * 1.00000 / im.multiplier) AS rm,\n", "            sales_quantity * (oid.unit_brand_fund * 1.00000 / im.multiplier) AS bf,\n", "            sales_quantity * (oid.unit_self_fund * 1.00000 / im.multiplier) AS sf,\n", "            oid.total_doorstep_return_selling_price * 1.0000 AS rl\n", "            FROM dwh.fact_sales_order_item_details oid\n", "            JOIN item_mapping im on im.product_id = oid.product_id \n", "            WHERE oid.cart_checkout_ts_ist BETWEEN ('{start_date}' || ' 00:00:00')::timestamp AND ('{end_date}' || ' 23:59:59')::timestamp \n", "            AND oid.is_internal_order = false \n", "            AND (oid.order_type NOT ILIKE '%%internal%%' OR oid.order_type IS NULL) \n", "            AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED'\n", "        ),\n", "\n", "        pre_summary AS (\n", "            SELECT a.*, cl.name AS city\n", "            FROM sales a\n", "            JOIN lake_retail.console_outlet co ON co.id = a.outlet_id AND co.active = 1 AND co.business_type_id = 7\n", "            LEFT JOIN lake_retail.console_location cl ON cl.id = co.tax_location_id\n", "        ),\n", "\n", "        summary AS (\n", "            SELECT city, item_id, order_date, order_id, \n", "            SUM(sales_quantity) AS quantity, SUM(selling_gmv) AS selling_gmv, SUM(mrp_gmv) AS mrp_gmv, SUM(rm) AS rm, SUM(bf) AS bf, SUM(sf) AS sf, \n", "            SUM(rl) AS rl\n", "            FROM pre_summary\n", "            GROUP BY 1,2,3,4\n", "        )\n", "\n", "        SELECT * FROM summary\n", "    ),\n", "    \n", "    dump_base AS (\n", "        WITH dump AS (\n", "            SELECT DATE(pos_timestamp + interval '5.5 Hours') AS date_, outlet_id, item_id, SUM(il.\"delta\") AS dump_quantity, \n", "            SUM(il.\"delta\" * il.weighted_lp) AS dump_value\n", "            FROM lake_ims.ims_inventory_log il\n", "            JOIN (\n", "                SELECT DISTINCT item_id, variant_id \n", "                FROM lake_rpc.product_product\n", "            ) rpp ON rpp.variant_id = il.variant_id\n", "            WHERE pos_timestamp BETWEEN ('{start_date}' || ' 00:00:00')::timestamp - interval '5.5 Hours' AND ('{end_date}' || ' 23:59:59')::timestamp - interval '5.5 Hours'\n", "            AND inventory_update_type_id IN (11,12,13,64,87,88,89,7,33,9,34,63,67)\n", "            GROUP BY 1,2,3\n", "        ),\n", "    \n", "        pre_summary AS (    \n", "            SELECT date_, cl.name AS city, outlet_id, item_id, dump_quantity, dump_value\n", "            FROM dump a\n", "            JOIN lake_retail.console_outlet co ON co.id = outlet_id AND co.active = 1 AND co.business_type_id = 7\n", "            LEFT JOIN lake_retail.console_location cl ON cl.id = co.tax_location_id\n", "        )\n", "    \n", "        SELECT date_, city, l0, l1, SUM(dump_value) AS dump\n", "        FROM (\n", "            SELECT date_, city, a.item_id, cd.l0, cd.l1, SUM(dump_value) AS dump_value\n", "            FROM pre_summary a\n", "            LEFT JOIN lake_rpc.item_category_details cd ON cd.item_id = a.item_id\n", "            GROUP BY 1,2,3,4,5\n", "        )\n", "        GROUP BY 1,2,3,4\n", "    ),\n", "    \n", "    l0_mapping AS (\n", "        SELECT a.*, l0, l1 \n", "        FROM sales_base a \n", "        LEFT JOIN lake_rpc.item_category_details cd ON cd.item_id = a.item_id\n", "    ),\n", "    \n", "    tot_users AS (\n", "        SELECT city, order_date, COUNT(DISTINCT order_id) AS tot_carts\n", "        FROM l0_mapping\n", "        GROUP BY 1,2\n", "    ),\n", "\n", "    agg_base AS (\n", "        SELECT city, order_date, l0, l1, COUNT(DISTINCT order_id) AS carts, \n", "        SUM(sales) AS qty_sold, SUM(selling_gmv) AS sp_gmv, SUM(mrp_gmv) AS mrp_gmv, SUM(rm) AS rm, SUM(bf) AS bf, SUM(sf) AS sf, SUM(rl) AS rl\n", "        FROM (\n", "            SELECT city, order_date, order_id, l0, l1, \n", "            SUM(quantity) AS sales, SUM(selling_gmv) AS selling_gmv, SUM(mrp_gmv) AS mrp_gmv, SUM(rm) AS rm, SUM(bf) AS bf, SUM(sf) AS sf, SUM(rl) AS rl\n", "            FROM l0_mapping\n", "            GROUP BY 1,2,3,4,5\n", "        )\n", "        GROUP BY 1,2,3,4\n", "    ),\n", "\n", "    pre_summary AS (\n", "        SELECT a.city, a.order_date AS date_, a.l0, a.l1, tot_carts, carts, qty_sold, sp_gmv, mrp_gmv, \n", "        rm, bf, sf, rl, dump\n", "        FROM agg_base a\n", "        LEFT JOIN dump_base b ON a.order_date = b.date_ AND a.l0 = b.l0 AND a.city = b.city AND a.l1 = b.l1\n", "        LEFT JOIN tot_users c ON a.order_date = c.order_date AND a.city = c.city\n", "    )\n", "\n", "    SELECT * FROM pre_summary\n", "    ORDER BY city, l0, l1\n", "\"\"\"\n", "sales_df = read_sql_query(sales_query, CON_REDSHIFT)"]}, {"cell_type": "markdown", "id": "e4e86433-d728-479d-9706-1017a860b1fd", "metadata": {}, "source": ["### Aggregated Data"]}, {"cell_type": "code", "execution_count": null, "id": "6024e865-c67c-44eb-8b6c-56726fa5500d", "metadata": {}, "outputs": [], "source": ["# --- All L1 --- #\n", "l1_all_df = (\n", "    sales_df.groupby([\"city\", \"l0\", \"tot_carts\", \"date_\"])\n", "    .agg(\n", "        {\n", "            \"carts\": \"sum\",\n", "            \"qty_sold\": \"sum\",\n", "            \"sp_gmv\": \"sum\",\n", "            \"mrp_gmv\": \"sum\",\n", "            \"rm\": \"sum\",\n", "            \"bf\": \"sum\",\n", "            \"sf\": \"sum\",\n", "            \"rl\": \"sum\",\n", "            \"dump\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "l1_all_df[\"l1\"] = \"All\"\n", "l1_all_df = l1_all_df[\n", "    [\n", "        \"city\",\n", "        \"date_\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"tot_carts\",\n", "        \"carts\",\n", "        \"qty_sold\",\n", "        \"sp_gmv\",\n", "        \"mrp_gmv\",\n", "        \"rm\",\n", "        \"bf\",\n", "        \"sf\",\n", "        \"rl\",\n", "        \"dump\",\n", "    ]\n", "]\n", "\n", "sales_agg_df = pd.concat([sales_df, l1_all_df])\n", "\n", "# --- Pan India L1 --- #\n", "l1_pan_df = (\n", "    sales_agg_df.groupby([\"l0\", \"l1\", \"date_\"])\n", "    .agg(\n", "        {\n", "            \"tot_carts\": \"sum\",\n", "            \"carts\": \"sum\",\n", "            \"qty_sold\": \"sum\",\n", "            \"sp_gmv\": \"sum\",\n", "            \"mrp_gmv\": \"sum\",\n", "            \"rm\": \"sum\",\n", "            \"bf\": \"sum\",\n", "            \"sf\": \"sum\",\n", "            \"rl\": \"sum\",\n", "            \"dump\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "l1_pan_df[\"city\"] = \"Pan India\"\n", "l1_pan_df = l1_pan_df[\n", "    [\n", "        \"city\",\n", "        \"date_\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"tot_carts\",\n", "        \"carts\",\n", "        \"qty_sold\",\n", "        \"sp_gmv\",\n", "        \"mrp_gmv\",\n", "        \"rm\",\n", "        \"bf\",\n", "        \"sf\",\n", "        \"rl\",\n", "        \"dump\",\n", "    ]\n", "]\n", "\n", "sales_agg_final_df = pd.concat([sales_agg_df, l1_pan_df]).sort_values(\n", "    by=[\"city\", \"l1\", \"date_\"], ascending=[True, True, False]\n", ")\n", "\n", "sales_agg_final_df.head(1)"]}, {"cell_type": "markdown", "id": "366ab5af-8d78-47bd-82cf-a8f8989bcde3", "metadata": {"tags": []}, "source": ["## Availability Metric"]}, {"cell_type": "markdown", "id": "f40f69a7-055b-4a69-a8a0-2130ceb49abc", "metadata": {"tags": []}, "source": ["### Availability Data"]}, {"cell_type": "code", "execution_count": null, "id": "e3b62365-1c6e-407d-aae7-87deb4faad69", "metadata": {}, "outputs": [], "source": ["avail_query = f\"\"\"\n", "    CREATE TEMPORARY TABLE avail_base AS (\n", "        WITH active_stores AS (\n", "            WITH outbound_base AS (\n", "                SELECT outlet_id, DATE(od.cart_checkout_ts_ist) AS date_, COUNT(DISTINCT cart_id) AS carts\n", "                FROM dwh.fact_sales_order_details od\n", "                WHERE od.cart_checkout_ts_ist BETWEEN ('{start_date}' || ' 00:00:00')::timestamp AND ('{end_date}' || ' 23:59:59')::timestamp\n", "                AND od.is_internal_order = false  \n", "                AND (od.order_type NOT ILIKE '%%internal%%' OR od.order_type IS NULL) AND od.order_current_status = 'DELIVERED'\n", "                GROUP BY 1,2\n", "            )\n", "        \n", "            SELECT DISTINCT facility_id, date_ \n", "            FROM outbound_base a\n", "            JOIN lake_retail.console_outlet co ON co.id = a.outlet_id AND co.active = 1 AND co.business_type_id = 7\n", "            WHERE carts > 5\n", "        ), \n", "        \n", "        perishable_assortment AS (\n", "            SELECT DISTINCT item_id\n", "            FROM metrics.perishable_assortment \n", "            WHERE updated_at BETWEEN ('{start_date}') AND ('{end_date}')\n", "        ),\n", "        \n", "        item_details AS (\n", "            SELECT DISTINCT rpc.item_id, l0 AS l0_category, \n", "            CASE \n", "                WHEN l0_id = 1487 THEN 'FnV' \n", "                WHEN l2_id IN (1185, 1961, 1367, 63, 1732, 1733, 1734, 1369) THEN 'Perishable' \n", "                WHEN perishable = 1 THEN 'Perishable' \n", "                WHEN fd.item_id IS NOT NULL THEN 'Perishable' \n", "                ELSE 'Packaged Goods' \n", "            END AS assortment_type \n", "            FROM lake_rpc.product_product rpc \n", "            LEFT JOIN lake_rpc.item_category_details cd ON cd.item_id = rpc.item_id\n", "            LEFT JOIN (SELECT item_id FROM perishable_assortment) fd ON fd.item_id = rpc.item_id\n", "            LEFT JOIN (\n", "                SELECT DISTINCT item_id \n", "                FROM lake_rpc.item_tag_mapping \n", "                WHERE active = true AND CAST(tag_value as int) = 3 AND tag_type_id = 3\n", "            ) i ON i.item_id = rpc.item_id\n", "            WHERE rpc.id in (SELECT MAX(id) AS id FROM lake_rpc.product_product pp WHERE pp.active = 1 AND pp.approved = 1 GROUP BY item_id) \n", "            AND i.item_id IS NULL AND rpc.handling_type <> 8\n", "        ),\n", "    \n", "        rpc_pre AS (\n", "            SELECT order_date AS updated_at, facility_id, item_id, actual_quantity - blocked_quantity AS current_inv \n", "            FROM consumer.rpc_daily_availability \n", "            WHERE order_date BETWEEN ('{start_date}' || ' 00:00:00')::timestamp AND ('{end_date}' || ' 23:59:59')::timestamp AND new_substate = 1\n", "        ),\n", "        \n", "        grocery_pre AS (\n", "            SELECT a.updated_at, a.facility_id, item_id, current_inv \n", "            FROM rpc_pre a\n", "            JOIN active_stores os ON os.facility_id = a.facility_id AND os.date_ = DATE(a.updated_at)\n", "        ),\n", "        \n", "        perishable_pre AS (\n", "            SELECT updated_at, item_id, a.facility_id, current_inv\n", "            FROM metrics.perishable_hourly_details_v2 a\n", "            JOIN active_stores os ON os.facility_id = a.facility_id AND os.date_ = DATE(a.updated_at)\n", "            WHERE updated_at BETWEEN ('{start_date}' || ' 00:00:00')::timestamp AND ('{end_date}' || ' 23:59:59')::timestamp   \n", "        ),\n", "    \n", "        fnv_pre AS (\n", "            SELECT updated_at, item_id, a.facility_id, current_inv\n", "            FROM metrics.fnv_hourly_details a\n", "            JOIN active_stores os ON os.facility_id = a.facility_id AND os.date_ = DATE(a.updated_at)\n", "            WHERE updated_at BETWEEN ('{start_date}' || ' 00:00:00')::timestamp AND ('{end_date}' || ' 23:59:59')::timestamp \n", "        ),\n", "        \n", "        grocery_hourly_base AS (\n", "            SELECT DATE(a.updated_at) AS date_, EXTRACT(hour FROM a.updated_at) AS hour_, a.item_id, a.facility_id, \n", "            CASE WHEN current_inv > 0 THEN 1 ELSE 0 END AS is_available\n", "            FROM grocery_pre a \n", "            WHERE a.item_id IN (SELECT DISTINCT item_id FROM item_details WHERE assortment_type IN ('Packaged Goods'))\n", "        ),\n", "        \n", "        perishable_hourly_base AS (\n", "            SELECT DATE(a.updated_at) AS date_, EXTRACT(hour FROM a.updated_at) AS hour_, a.item_id, a.facility_id, \n", "            CASE WHEN current_inv > 0 THEN 1 ELSE 0 END AS is_available\n", "            FROM perishable_pre a \n", "            WHERE a.item_id IN (SELECT DISTINCT item_id FROM item_details WHERE assortment_type IN ('Perishable'))\n", "        ),\n", "    \n", "        fnv_hourly_base AS (\n", "            SELECT DATE(a.updated_at) AS date_, EXTRACT(hour FROM a.updated_at) AS hour_, a.item_id, a.facility_id, \n", "            CASE WHEN current_inv > 0 THEN 1 ELSE 0 END AS is_available\n", "            FROM fnv_pre a \n", "            WHERE a.item_id IN (SELECT DISTINCT item_id FROM item_details WHERE assortment_type IN ('FnV'))\n", "        ),\n", "        \n", "        agg_base AS (\n", "            SELECT DISTINCT date_, hour_, facility_id, item_id, is_available\n", "            FROM grocery_hourly_base\n", "            UNION\n", "            SELECT DISTINCT date_, hour_, facility_id, item_id, is_available\n", "            FROM perishable_hourly_base\n", "            UNION\n", "            SELECT DISTINCT date_, hour_, facility_id, item_id, is_available \n", "            FROM fnv_hourly_base\n", "        )\n", "        \n", "        SELECT * FROM agg_base\n", "        WHERE hour_ BETWEEN 6 AND 23\n", "    );\n", "    \n", "    WITH base AS (\n", "        SELECT date_, hour_, item_id, cl.name AS city, co.id AS outlet_id, is_available\n", "        FROM avail_base a\n", "        JOIN lake_retail.console_outlet co ON co.facility_id = a.facility_id AND co.active = 1 AND co.business_type_id = 7\n", "        LEFT JOIN lake_retail.console_location cl ON cl.id = co.tax_location_id\n", "    ),\n", "    \n", "    city_store_weights AS (\n", "        SELECT DISTINCT city, outlet_id, CAST(store_weight AS float) AS sw \n", "        FROM metrics.city_store_penetration \n", "        WHERE updated_at = (SELECT MAX(updated_at) FROM metrics.city_store_penetration)\n", "    ),\n", "    \n", "    city_hour_weights AS (\n", "        SELECT DISTINCT city, order_hour AS hour_, CAST(weights AS float) AS hw \n", "        FROM metrics.city_hour_cart_penetration \n", "        WHERE updated_at = (SELECT MAX(updated_at) FROM metrics.city_hour_cart_penetration)\n", "    ),\n", "    \n", "    score_merge_base AS (\n", "        SELECT date_, a.hour_, a.city, a.outlet_id, item_id, is_available, sw, hw\n", "        FROM base a\n", "        LEFT JOIN city_store_weights csw ON csw.city = a.city AND csw.outlet_id = a.outlet_id\n", "        LEFT JOIN city_hour_weights chw ON chw.city = a.city AND chw.hour_ = a.hour_\n", "    ),\n", "    \n", "    agg_store_weights AS (\n", "        SELECT date_, city, item_id, SUM(sw) AS tot_sw\n", "        FROM (\n", "            SELECT DISTINCT date_, city, item_id, sw\n", "            FROM score_merge_base\n", "        )\n", "        GROUP BY 1,2,3\n", "    ),\n", "    \n", "    agg_hour_weights AS (\n", "        SELECT date_, city, item_id, SUM(hw) AS tot_hw\n", "        FROM (\n", "            SELECT DISTINCT date_, city, item_id, hw\n", "            FROM score_merge_base\n", "        )\n", "        GROUP BY 1,2,3\n", "    ),\n", "    \n", "    final_base AS (\n", "        SELECT a.date_, a.city, a.item_id, outlet_id, hour_, is_available, sw, tot_sw, hw, tot_hw,\n", "        is_available * (sw * 1.00000 / NULLIF(tot_sw,0)) * (hw * 1.00000 / NULLIF(tot_hw,0)) AS wt_available,\n", "        1 * (sw * 1.00000 / NULLIF(tot_sw,0)) * (hw * 1.00000 / NULLIF(tot_hw,0)) AS tot_available\n", "        FROM score_merge_base a\n", "        LEFT JOIN agg_store_weights b ON a.city = b.city AND a.item_id = b.item_id AND a.date_ = b.date_\n", "        LEFT JOIN agg_hour_weights c ON a.city = c.city AND a.item_id = c.item_id AND a.date_ = c.date_\n", "    ),\n", "    \n", "    city_weights AS (\n", "        SELECT DISTINCT city, CAST(weight AS float) AS city_weight\n", "        FROM metrics.city_weight\n", "        WHERE updated_at = (SELECT MAX(updated_at) FROM metrics.city_weight)\n", "    )\n", "    \n", "    SELECT a.city, b.city_weight, item_id, date_, SUM(is_available) AS avail_hour, COUNT(is_available) AS tot_hour, \n", "    SUM(wt_available) / SUM(tot_available) AS wt_score\n", "    FROM final_base a\n", "    LEFT JOIN city_weights b ON a.city = b.city\n", "    GROUP BY 1,2,3,4\n", "    \"\"\"\n", "avail_base_df = read_sql_query(avail_query, CON_REDSHIFT)\n", "avail_base_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "a4bbf07c-e033-47d8-85e3-52070daa9871", "metadata": {}, "outputs": [], "source": ["avail_base_pan_df = avail_base_df.copy()\n", "\n", "avail_base_pan_df[\"pan_wt_score\"] = (\n", "    avail_base_pan_df[\"wt_score\"] * avail_base_pan_df[\"city_weight\"]\n", ")\n", "\n", "avail_base_pan_df = (\n", "    avail_base_pan_df.groupby([\"item_id\", \"date_\"])\n", "    .agg(\n", "        {\n", "            \"avail_hour\": \"sum\",\n", "            \"tot_hour\": \"sum\",\n", "            \"pan_wt_score\": \"sum\",\n", "            \"city_weight\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "avail_base_pan_df[\"city\"] = \"Pan India\"\n", "avail_base_pan_df[\"pan_wt_score\"] = (\n", "    avail_base_pan_df[\"pan_wt_score\"] / avail_base_pan_df[\"city_weight\"]\n", ")\n", "\n", "avail_base_pan_df = avail_base_pan_df.rename(columns={\"pan_wt_score\": \"wt_score\"})\n", "avail_base_pan_df = avail_base_pan_df[\n", "    [\"city\", \"city_weight\", \"item_id\", \"date_\", \"avail_hour\", \"tot_hour\", \"wt_score\"]\n", "]\n", "\n", "avail_base_df = pd.concat([avail_base_df, avail_base_pan_df])\n", "\n", "del [avail_base_pan_df]\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "3219a3bf-ac0e-4724-aa9c-3626db3eb7bf", "metadata": {}, "outputs": [], "source": ["sales_query = f\"\"\"\n", "    WITH sales_base AS (\n", "        WITH item_mapping as (\n", "            SELECT DISTINCT ipr.product_id, \n", "            CASE \n", "                WHEN ipr.item_id IS NULL THEN ipom_0.item_id \n", "                ELSE ipr.item_id \n", "            END AS item_id, \n", "            CASE \n", "                WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1) \n", "                ELSE COALESCE(ipom_0.multiplier,1) \n", "            END AS multiplier\n", "            FROM lake_rpc.item_product_mapping ipr \n", "            LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id \n", "            LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", "        ),\n", "\n", "        sales AS (\n", "            SELECT DATE(oid.cart_checkout_ts_ist) AS order_date, oid.outlet_id, oid.product_id, im.item_id, oid.cart_id AS order_id, im.multiplier, \n", "            ((oid.procured_quantity - oid.total_doorstep_return_quantity) * im.multiplier) AS sales_quantity,\n", "            sales_quantity * (oid.unit_selling_price * 1.00000 / im.multiplier) AS selling_gmv,\n", "            sales_quantity * (oid.unit_mrp * 1.00000 / im.multiplier) AS mrp_gmv,\n", "            sales_quantity * (oid.unit_retained_margin * 1.00000 / im.multiplier) AS rm,\n", "            sales_quantity * (oid.unit_brand_fund * 1.00000 / im.multiplier) AS bf,\n", "            sales_quantity * (oid.unit_self_fund * 1.00000 / im.multiplier) AS sf,\n", "            oid.total_doorstep_return_selling_price * 1.0000 AS rl\n", "            FROM dwh.fact_sales_order_item_details oid\n", "            JOIN item_mapping im on im.product_id = oid.product_id \n", "            WHERE oid.cart_checkout_ts_ist BETWEEN ('{start_date}' - 15 || ' 00:00:00')::timestamp AND ('{start_date}' - 1 || ' 23:59:59')::timestamp \n", "            AND oid.is_internal_order = false \n", "            AND (oid.order_type NOT ILIKE '%%internal%%' OR oid.order_type IS NULL) \n", "            AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED'\n", "        ),\n", "\n", "        pre_summary AS (\n", "            SELECT a.*, cl.name AS city\n", "            FROM sales a\n", "            JOIN lake_retail.console_outlet co ON co.id = a.outlet_id AND co.active = 1 AND co.business_type_id = 7\n", "            LEFT JOIN lake_retail.console_location cl ON cl.id = co.tax_location_id\n", "        ),\n", "\n", "        summary AS (\n", "            SELECT city, item_id, SUM(selling_gmv) AS gmv, SUM(rm) AS rm\n", "            FROM pre_summary\n", "            GROUP BY 1,2\n", "        )\n", "\n", "        SELECT * FROM summary    \n", "    ),\n", "    \n", "    pre_summary AS (\n", "        SELECT city, a.item_id, l0, l1, gmv, rm\n", "        FROM sales_base a\n", "        LEFT JOIN lake_rpc.item_category_details cd ON cd.item_id = a.item_id\n", "    ),\n", "    \n", "    city_weights AS (\n", "        SELECT DISTINCT city, CAST(weight AS float) AS city_weight\n", "        FROM metrics.city_weight\n", "        WHERE updated_at = (SELECT MAX(updated_at) FROM metrics.city_weight)\n", "    ),\n", "    \n", "    city_item_weights AS (\n", "        SELECT DISTINCT city, item_id, CAST(weights AS float) AS item_weight \n", "        FROM metrics.city_item_cart_penetration \n", "        WHERE updated_at = (SELECT MAX(updated_at) FROM metrics.city_item_cart_penetration)\n", "    ),\n", "    \n", "    agg_base AS (\n", "        SELECT a.city, a.item_id, l0, l1, gmv, rm, city_weight, item_weight\n", "        FROM pre_summary a\n", "        LEFT JOIN city_weights b ON a.city = b.city\n", "        LEFT JOIN city_item_weights c ON a.city = c.city AND a.item_id = c.item_id\n", "    ),\n", "    \n", "    city_total_score AS (\n", "        SELECT item_id, SUM(city_weight) AS tot_city_weight\n", "        FROM (\n", "            SELECT DISTINCT item_id, city_weight\n", "            FROM agg_base\n", "        )\n", "        GROUP BY 1\n", "    ),\n", "    \n", "    pan_india AS (\n", "        SELECT 'Pan India' AS city, item_id, l0, l1, SUM(gmv) AS gmv, SUM(rm) AS rm, SUM(city_weight * item_weight) / NULLIF(SUM(city_weight),0) AS item_score\n", "        FROM agg_base\n", "        GROUP BY 1,2,3,4\n", "    ),\n", "    \n", "    summary AS (\n", "        SELECT DISTINCT city, item_id, l0, l1, gmv, rm, item_weight AS item_score \n", "        FROM agg_base\n", "        UNION\n", "        SELECT * FROM pan_india\n", "    )\n", "    \n", "    SELECT * FROM summary\n", "    \"\"\"\n", "sku_sales_df = read_sql_query(sales_query, CON_REDSHIFT)\n", "sku_sales_df.shape"]}, {"cell_type": "markdown", "id": "7865b0b6-0a6f-44aa-9781-d6e6aec79546", "metadata": {"tags": []}, "source": ["### Assortment Classification"]}, {"cell_type": "code", "execution_count": null, "id": "ee26d6be-f1aa-4488-ba74-a059e8b2e112", "metadata": {}, "outputs": [], "source": ["skus_calc_df = sku_sales_df.copy()\n", "\n", "skus_calc_all_df = skus_calc_df.copy()\n", "skus_calc_all_df[\"l1\"] = \"All\"\n", "skus_calc_all_df = skus_calc_all_df[\n", "    [\"city\", \"item_id\", \"l0\", \"l1\", \"gmv\", \"rm\", \"item_score\"]\n", "]\n", "\n", "skus_calc_df = pd.concat([skus_calc_df, skus_calc_all_df])\n", "\n", "del skus_calc_all_df\n", "gc.collect()\n", "\n", "skus_calc_df[\"i_rm\"] = skus_calc_df[\"rm\"] / skus_calc_df[\"gmv\"]\n", "\n", "l0_agg_df = (\n", "    skus_calc_df.groupby([\"city\", \"l0\", \"l1\"])\n", "    .agg({\"gmv\": \"sum\", \"rm\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"gmv\": \"l1_gmv\"})\n", ")\n", "l0_agg_df[\"l1_rm\"] = l0_agg_df[\"rm\"] / l0_agg_df[\"l1_gmv\"]\n", "\n", "# --- RM > 1.5 L0 RM --- #\n", "skus_calc_df = pd.merge(\n", "    skus_calc_df,\n", "    l0_agg_df[[\"city\", \"l0\", \"l1\", \"l1_rm\", \"l1_gmv\"]],\n", "    on=[\"l0\", \"l1\", \"city\"],\n", "    how=\"left\",\n", ")\n", "skus_calc_df[\"rm_flag\"] = np.where(\n", "    skus_calc_df[\"i_rm\"] >= 1.5 * skus_calc_df[\"l1_rm\"], 1, 0\n", ")\n", "\n", "# --- Top 80% GMV Contributing --- #\n", "skus_calc_df = skus_calc_df.sort_values(\n", "    by=[\"city\", \"l0\", \"l1\", \"gmv\"], ascending=[True, True, True, False]\n", ")\n", "\n", "skus_calc_df[\"l1_cum_gmv\"] = skus_calc_df.groupby([\"city\", \"l0\", \"l1\"])[\"gmv\"].cumsum()\n", "skus_calc_df[\"l1_per_gmv\"] = skus_calc_df[\"l1_cum_gmv\"] / skus_calc_df[\"l1_gmv\"]\n", "skus_calc_df[\"gmv_flag\"] = np.where(skus_calc_df[\"l1_per_gmv\"] <= 0.8, 1, 0)\n", "\n", "skus_calc_df = skus_calc_df[\n", "    [\"city\", \"item_id\", \"item_score\", \"l0\", \"l1\", \"rm_flag\", \"gmv_flag\"]\n", "].drop_duplicates()\n", "skus_calc_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "2c60df68-d182-4e0d-8788-de5d69af39f2", "metadata": {}, "outputs": [], "source": ["calc_df = pd.merge(avail_base_df, skus_calc_df, on=[\"city\", \"item_id\"], how=\"inner\")\n", "calc_df[\"wt_avail_score\"] = calc_df[\"wt_score\"] * calc_df[\"item_score\"]\n", "\n", "# --- RM Availability --- #\n", "rm_avail_df = (\n", "    calc_df[calc_df[\"rm_flag\"] == 1]\n", "    .groupby([\"city\", \"l0\", \"l1\", \"date_\"])\n", "    .agg(\n", "        {\n", "            \"item_id\": \"nunique\",\n", "            \"avail_hour\": \"sum\",\n", "            \"tot_hour\": \"sum\",\n", "            \"wt_avail_score\": \"sum\",\n", "            \"item_score\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "rm_avail_df[\"rm_avail\"] = rm_avail_df[\"avail_hour\"] / rm_avail_df[\"tot_hour\"]\n", "rm_avail_df[\"rm_wt_avail\"] = rm_avail_df[\"wt_avail_score\"] / rm_avail_df[\"item_score\"]\n", "\n", "rm_avail_df = rm_avail_df.rename(columns={\"item_id\": \"rm_skus\"})\n", "\n", "rm_avail_df = rm_avail_df[\n", "    [\"city\", \"l0\", \"l1\", \"date_\", \"rm_skus\", \"rm_avail\", \"rm_wt_avail\"]\n", "].drop_duplicates()\n", "\n", "# --- GMV Availability --- #\n", "gmv_avail_df = (\n", "    calc_df[calc_df[\"gmv_flag\"] == 1]\n", "    .groupby([\"city\", \"l0\", \"l1\", \"date_\"])\n", "    .agg(\n", "        {\n", "            \"avail_hour\": \"sum\",\n", "            \"tot_hour\": \"sum\",\n", "            \"wt_avail_score\": \"sum\",\n", "            \"item_score\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "gmv_avail_df[\"gmv_avail\"] = gmv_avail_df[\"avail_hour\"] / gmv_avail_df[\"tot_hour\"]\n", "gmv_avail_df[\"gmv_wt_avail\"] = (\n", "    gmv_avail_df[\"wt_avail_score\"] / gmv_avail_df[\"item_score\"]\n", ")\n", "\n", "gmv_avail_df = gmv_avail_df[\n", "    [\"city\", \"l0\", \"l1\", \"date_\", \"gmv_avail\", \"gmv_wt_avail\"]\n", "].drop_duplicates()\n", "\n", "# --- <PERSON><PERSON> Bases --- #\n", "availability_df = calc_df[[\"city\", \"l0\", \"l1\", \"date_\"]].drop_duplicates()\n", "availability_df = availability_df.merge(\n", "    gmv_avail_df, on=[\"city\", \"date_\", \"l0\", \"l1\"], how=\"left\"\n", ")\n", "availability_df = availability_df.merge(\n", "    rm_avail_df, on=[\"city\", \"date_\", \"l0\", \"l1\"], how=\"left\"\n", ")\n", "\n", "del [gmv_avail_df, rm_avail_df]\n", "gc.collect()\n", "\n", "availability_df.head(1)"]}, {"cell_type": "markdown", "id": "e5fa6f33-b5f5-4fb9-a168-338eb3d01bdc", "metadata": {"tags": []}, "source": ["## New Assortment Metric"]}, {"cell_type": "code", "execution_count": null, "id": "d4a11c8b-5832-4910-a931-9490157b6d46", "metadata": {}, "outputs": [], "source": ["CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "markdown", "id": "4960b408-7afc-4ff5-bfa2-f7f563d1f007", "metadata": {"tags": []}, "source": ["### First GRN Data"]}, {"cell_type": "code", "execution_count": null, "id": "aa6ca0d7-ac96-45de-83a8-ea985e746067", "metadata": {}, "outputs": [], "source": ["first_time_grn = f\"\"\"\n", "    WITH item_details AS (\n", "        SELECT DISTINCT rpc.item_id, l0, l1\n", "        FROM rpc.product_product rpc \n", "        LEFT JOIN rpc.item_category_details cd ON cd.item_id = rpc.item_id \n", "        LEFT JOIN (\n", "            SELECT DISTINCT item_id \n", "            FROM rpc.item_tag_mapping \n", "            WHERE active = true AND CAST(tag_value AS int) = 3 AND tag_type_id = 3\n", "        ) i ON i.item_id = rpc.item_id \n", "        WHERE rpc.id IN (SELECT MAX(id) AS id FROM rpc.product_product pp WHERE pp.active = 1 AND pp.approved = 1 GROUP BY item_id) \n", "        AND i.item_id IS NULL AND rpc.handling_type <> CAST(8 AS varchar)\n", "    ),\n", "\n", "    frontend_city_details AS (\n", "        SELECT bfom.facility_id, cl.name AS city \n", "        FROM po.bulk_facility_outlet_mapping bfom \n", "        JOIN retail.console_outlet rco ON rco.id = bfom.outlet_id \n", "        JOIN retail.console_location cl ON cl.id = rco.tax_location_id \n", "        WHERE bfom.active = true \n", "        GROUP BY 1,2\n", "    ),\n", "\n", "    first_grn_date AS (\n", "        SELECT CAST('Pan India' AS varchar) AS city, item_id, MIN(created_at + interval '330' minute) AS first_inward_date \n", "        FROM po.po_grn \n", "        GROUP BY 1,2 \n", "        UNION\n", "        SELECT cd.city, item_id, MIN(grn.created_at + interval '330' minute) AS first_inward_date \n", "        FROM po.po_grn grn \n", "        JOIN retail.console_outlet rco ON rco.id = grn.outlet_id\n", "        JOIN frontend_city_details cd ON cd.facility_id = rco.facility_id\n", "        GROUP BY 1,2\n", "    ),\n", "\n", "    final_first_grn_date AS (\n", "        SELECT fgd.city, fgd.item_id, l0, l1, DATE(first_inward_date) AS fidate_\n", "        FROM first_grn_date fgd \n", "        JOIN item_details id ON id.item_id = fgd.item_id\n", "    )\n", "    \n", "    SELECT city, item_id, l0, l1, fidate_\n", "    FROM final_first_grn_date\n", "    WHERE fidate_ BETWEEN DATE('{start_date}') - interval '60' day AND DATE('{start_date}') \n", "    \"\"\"\n", "first_grn_df = read_sql_query(first_time_grn, CON_TRINO)\n", "first_grn_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "081fbf7d-6863-4de7-bd7d-e2fca43b6e30", "metadata": {}, "outputs": [], "source": ["new_skus_count_df = (\n", "    first_grn_df.groupby([\"city\", \"l0\", \"l1\"])\n", "    .agg({\"item_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"new_sku\"})\n", ")\n", "\n", "new_skus_all_count_df = (\n", "    first_grn_df.groupby([\"city\", \"l0\"])\n", "    .agg({\"item_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"new_sku\"})\n", ")\n", "new_skus_all_count_df[\"l1\"] = \"All\"\n", "\n", "new_skus_all_count_df = new_skus_all_count_df[\n", "    [\"city\", \"l0\", \"l1\", \"new_sku\"]\n", "].drop_duplicates()\n", "\n", "new_skus_count_df = pd.concat([new_skus_count_df, new_skus_all_count_df])"]}, {"cell_type": "code", "execution_count": null, "id": "98d30256-55b6-4272-9a07-b526df9d4fa2", "metadata": {}, "outputs": [], "source": ["daily_sales_query = f\"\"\"\n", "    WITH sales_base AS (\n", "        WITH item_mapping as (\n", "            SELECT DISTINCT ipr.product_id, \n", "            CASE \n", "                WHEN ipr.item_id IS NULL THEN ipom_0.item_id \n", "                ELSE ipr.item_id \n", "            END AS item_id, \n", "            CASE \n", "                WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1) \n", "                ELSE COALESCE(ipom_0.multiplier,1) \n", "            END AS multiplier\n", "            FROM lake_rpc.item_product_mapping ipr \n", "            LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id \n", "            LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", "        ),\n", "\n", "        sales AS (\n", "            SELECT DATE(oid.cart_checkout_ts_ist) AS order_date, oid.outlet_id, oid.product_id, im.item_id, oid.cart_id AS order_id, im.multiplier, \n", "            ((oid.procured_quantity - oid.total_doorstep_return_quantity) * im.multiplier) AS sales_quantity,\n", "            sales_quantity * (oid.unit_selling_price * 1.00000 / im.multiplier) AS selling_gmv\n", "            FROM dwh.fact_sales_order_item_details oid\n", "            JOIN item_mapping im on im.product_id = oid.product_id \n", "            WHERE oid.cart_checkout_ts_ist BETWEEN ('{start_date}' - 15 || ' 00:00:00')::timestamp AND ('{end_date}' || ' 23:59:59')::timestamp \n", "            AND oid.is_internal_order = false \n", "            AND (oid.order_type NOT ILIKE '%%internal%%' OR oid.order_type IS NULL) \n", "            AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED'\n", "        ),\n", "\n", "        pre_summary AS (\n", "            SELECT a.*, cl.name AS city\n", "            FROM sales a\n", "            JOIN lake_retail.console_outlet co ON co.id = a.outlet_id AND co.active = 1 AND co.business_type_id = 7\n", "            LEFT JOIN lake_retail.console_location cl ON cl.id = co.tax_location_id\n", "        ),\n", "        \n", "        summary AS (\n", "            SELECT city, item_id, order_date AS date_, order_id, SUM(selling_gmv) AS selling_gmv\n", "            FROM pre_summary\n", "            GROUP BY 1,2,3,4\n", "        )\n", "        \n", "        SELECT * FROM summary    \n", "    ),\n", "    \n", "    item_sales AS (\n", "        SELECT city, item_id, date_, SUM(selling_gmv) AS item_gmv, COUNT(DISTINCT order_id) AS user_buying\n", "        FROM sales_base\n", "        GROUP BY 1,2,3\n", "    ),\n", "    \n", "    pre_summary AS (\n", "        SELECT city, item_id, date_, item_gmv, user_buying \n", "        FROM item_sales\n", "        UNION\n", "        SELECT 'Pan India' AS city, item_id, date_, SUM(item_gmv) AS item_gmv, SUM(user_buying) AS user_buying\n", "        FROM item_sales\n", "        GROUP BY 1,2,3\n", "    )\n", "    \n", "    SELECT * FROM pre_summary\n", "    \"\"\"\n", "new_assortment_df = read_sql_query(daily_sales_query, CON_REDSHIFT)\n", "new_assortment_df[\"date_\"] = pd.to_datetime(new_assortment_df[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "8d607487-f207-4fa2-8d30-6be28fd225df", "metadata": {}, "outputs": [], "source": ["assortment_calc_df = pd.merge(\n", "    new_assortment_df,\n", "    first_grn_df[[\"item_id\", \"city\", \"l0\", \"l1\"]],\n", "    on=[\"item_id\", \"city\"],\n", "    how=\"inner\",\n", ")\n", "\n", "assortment_agg_df = (\n", "    assortment_calc_df.groupby([\"city\", \"date_\", \"l0\", \"l1\"])\n", "    .agg({\"item_gmv\": \"sum\", \"user_buying\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "assortment_all_agg_df = (\n", "    assortment_calc_df.groupby([\"city\", \"date_\", \"l0\"])\n", "    .agg({\"item_gmv\": \"sum\", \"user_buying\": \"sum\"})\n", "    .reset_index()\n", ")\n", "assortment_all_agg_df[\"l1\"] = \"All\"\n", "\n", "assortment_all_agg_df = assortment_all_agg_df[\n", "    [\"city\", \"date_\", \"l0\", \"l1\", \"item_gmv\", \"user_buying\"]\n", "].drop_duplicates()\n", "\n", "assortment_agg_df = pd.concat([assortment_agg_df, assortment_all_agg_df])\n", "\n", "assortment_agg_df.shape"]}, {"cell_type": "markdown", "id": "df9808c6-1959-412d-88c0-14b0b88cd4b5", "metadata": {}, "source": ["## Search Metrics"]}, {"cell_type": "markdown", "id": "c4279a56-db1b-467d-8fc5-f9eaf9b00453", "metadata": {}, "source": ["### Keywords < 10% ATC"]}, {"cell_type": "code", "execution_count": null, "id": "c7374efd-a9a6-4214-95bf-0e10fa75df5b", "metadata": {}, "outputs": [], "source": ["try:\n", "    query = f\"\"\"\n", "        CREATE TEMPORARY TABLE keyword_pid AS (\n", "            SELECT keyword, product_id, co.l0, cd.l1, key_prod_atc\n", "            FROM (\n", "                SELECT TRIM(lower(properties__search_actual_keyword))::varchar AS keyword, properties__product_id AS product_id, l1_category_id, l0_category_id,\n", "                COUNT(device_uuid) AS key_prod_atc \n", "                FROM spectrum.mobile_event_data k\n", "                JOIN dwh.dim_product p ON k.properties__product_id = p.product_id AND is_current = true AND is_product_enabled = true\n", "                JOIN dwh.dim_merchant_outlet_facility_mapping dmm ON dmm.frontend_merchant_id = traits__merchant_id AND is_express_store = True AND dmm.is_current = True \n", "                WHERE at_date_ist BETWEEN DATE('{start_date}') AND DATE('{end_date}')\n", "                AND name = 'Product Added' AND properties__search_actual_keyword IS NOT NULL\n", "                AND properties__search_actual_keyword NOT IN ('-NA-', '#-NA', '')\n", "                AND properties__search_keyword_parent IN ('type-to-search', 'type_to_search')\n", "                AND at_date_ist >= '2023-01-01' AND traits__user_id IS NOT NULL AND traits__user_id NOT IN (-1,0)\n", "                AND traits__user_id NOT IN ('14647274', '9961423','9709403','13957980','13605597','3927621','14740725','4144617','10045662')\n", "                AND traits__city_id IS NOT NULL AND traits__merchant_id IS NOT NULL AND traits__merchant_name IS NOT NULL AND keyword NOT IN ('',' ','#-na')\n", "                AND len(keyword) > 3\n", "                GROUP BY 1,2,3,4\n", "            ) a\n", "            LEFT JOIN (SELECT DISTINCT l1_id, l1 FROM lake_rpc.item_category_details) cd ON cd.l1_id = l1_category_id\n", "            LEFT JOIN (SELECT DISTINCT l0_id, l0 FROM lake_rpc.item_category_details) co ON co.l0_id = l0_category_id\n", "        );\n", "\n", "        CREATE TEMPORARY TABLE search AS (\n", "            SELECT at_date_ist, traits__merchant_id, pos_outlet_id, LOWER(device_uuid) AS device_uuid, TRIM(LOWER(properties__search_actual_keyword))::varchar AS keyword \n", "            FROM spectrum.mobile_event_data\n", "            JOIN dwh.dim_merchant_outlet_facility_mapping dmm ON dmm.frontend_merchant_id = traits__merchant_id AND is_express_store = True AND dmm.is_current = True \n", "            WHERE at_date_ist BETWEEN DATE('{start_date}') AND DATE('{end_date}')\n", "            AND name = 'Product Searched'\n", "            AND properties__search_actual_keyword is NOT NULL\n", "            AND properties__search_actual_keyword NOT IN ('-NA-', '#-NA', '')\n", "            AND properties__search_keyword_parent IN ('type-to-search', 'type_to_search')\n", "            AND at_date_ist >= '2023-01-01'\n", "            AND traits__user_id IS NOT NULL AND traits__user_id NOT IN (-1,0)\n", "            AND traits__user_id NOT IN ('14647274', '9961423','9709403','13957980','13605597','3927621','14740725','4144617','10045662')\n", "            AND traits__city_id IS NOT NULL AND traits__merchant_id IS NOT NULL \n", "            AND traits__merchant_name IS NOT NULL AND keyword NOT IN ('',' ','#-na')\n", "            AND len(keyword) > 3\n", "            GROUP BY 1,2,3,4,5\n", "        );\n", "\n", "        CREATE TEMPORARY TABLE atc_pids AS (\n", "            SELECT at_date_ist, traits__merchant_id, pos_outlet_id, LOWER(device_uuid) AS device_uuid, TRIM(lower(properties__search_actual_keyword))::varchar AS keyword, \n", "            properties__product_id AS product_id\n", "            FROM spectrum.mobile_event_data\n", "            JOIN dwh.dim_merchant_outlet_facility_mapping dmm ON dmm.frontend_merchant_id = traits__merchant_id AND is_express_store = True AND dmm.is_current = True\n", "            WHERE at_date_ist BETWEEN DATE('{start_date}') AND DATE('{end_date}')\n", "            AND name = 'Product Added'\n", "            AND properties__search_actual_keyword IS NOT NULL\n", "            AND properties__search_actual_keyword NOT IN ('-NA-', '#-NA', '')\n", "            AND properties__search_keyword_parent IN ('type-to-search', 'type_to_search')\n", "            AND at_date_ist >= '2023-01-01'\n", "            AND traits__user_id IS NOT NULL\n", "            AND traits__user_id NOT IN (-1,0)\n", "            AND traits__user_id NOT IN ('14647274', '9961423','9709403','13957980','13605597','3927621','14740725','4144617','10045662')\n", "            AND traits__city_id IS NOT NULL\n", "            AND traits__merchant_id IS NOT NULL\n", "            AND traits__merchant_name IS NOT NULL\n", "            AND keyword NOT IN ('',' ','#-na')\n", "            AND len(keyword) > 3\n", "            GROUP BY 1,2,3,4,5,6\n", "        );\n", "\n", "        WITH keyword_atc AS (\n", "            SELECT keyword AS keyword1, SUM(key_prod_atc) AS key_tot_atc\n", "            FROM keyword_pid\n", "            GROUP BY 1 \n", "            HAVING key_tot_atc >= 100\n", "        ),\n", "\n", "        l1_atc AS (\n", "            SELECT l0, l1, SUM(key_prod_atc) AS l1_tot_atc\n", "            FROM keyword_pid k\n", "            GROUP BY 1,2\n", "        ),\n", "\n", "        keyword_l1 AS (\n", "            SELECT k.keyword, key_tot_atc, k.l0, k.l1, l1_tot_atc, SUM(key_prod_atc) AS key_l1_atc, \n", "            (key_l1_atc * 100.00 / key_tot_atc) AS l1_in_key,\n", "            (key_l1_atc * 100.00 / l1_tot_atc) AS key_in_l1, ROW_NUMBER() OVER(PARTITION BY k.l0, k.l1 ORDER BY key_l1_atc DESC) AS keyword_rank_in_l1\n", "            FROM keyword_pid k \n", "            JOIN keyword_atc a ON k.keyword = a.keyword1\n", "            JOIN l1_atc l ON l.l1 = k.l1 AND l.l0 = k.l0\n", "            GROUP BY 1,2,3,4,5\n", "            HAVING key_l1_atc > 10 \n", "        ),\n", "\n", "        pre_final AS (\n", "            SELECT l0, l1, keyword\n", "            FROM keyword_l1\n", "            WHERE key_in_l1 >= 0.1 AND l1_in_key >= 10\n", "            GROUP BY 1,2,3\n", "        ),\n", "\n", "        final_base AS (\n", "            SELECT s.pos_outlet_id, s.keyword, u.l0, u.l1, COUNT(s.device_uuid) AS searches,\n", "            COUNT(ap.device_uuid) AS atcs, (atcs * 1.00) / NULLIF(searches,0) AS search_conv\n", "            FROM search s\n", "            LEFT JOIN atc_pids ap ON s.device_uuid = ap.device_uuid AND s.at_date_ist = ap.at_date_ist AND s.keyword = ap.keyword AND s.traits__merchant_id = ap.traits__merchant_id\n", "            JOIN pre_final u ON u.keyword = s.keyword\n", "            GROUP BY 1,2,3,4\n", "        ),\n", "\n", "        pre_summary AS (\n", "            SELECT cl.name AS city, pos_outlet_id, l0, l1, keyword\n", "            FROM final_base a\n", "            JOIN lake_retail.console_outlet co ON co.id = pos_outlet_id AND co.active = 1 AND co.business_type_id = 7\n", "            LEFT JOIN lake_retail.console_location cl ON cl.id = co.tax_location_id\n", "            WHERE search_conv <= 0.1\n", "            GROUP BY 1,2,3,4,5\n", "        ),\n", "\n", "        city_l1 AS (\n", "            SELECT city, l0, l1, COUNT(DISTINCT keyword) AS keys\n", "            FROM pre_summary\n", "            GROUP BY 1,2,3\n", "        ),\n", "\n", "        city_l0 AS (\n", "            SELECT city, l0, 'All' AS l1, COUNT(DISTINCT keyword) AS keys\n", "            FROM pre_summary\n", "            GROUP BY 1,2,3\n", "        ),\n", "\n", "        all_l1 AS (\n", "            SELECT 'Pan India' AS city, l0, l1, COUNT(DISTINCT keyword) AS keys\n", "            FROM pre_summary\n", "            GROUP BY 1,2,3\n", "        ),\n", "\n", "        all_l0 AS (\n", "            SELECT 'Pan India' AS city, l0, 'All' AS l1, COUNT(DISTINCT keyword) AS keys\n", "            FROM pre_summary\n", "            GROUP BY 1,2,3 \n", "        ),\n", "\n", "        merged_base AS (\n", "            SELECT * FROM city_l1\n", "            UNION \n", "            SELECT * FROM city_l0\n", "            UNION\n", "            SELECT * FROM all_l1\n", "            UNION\n", "            SELECT * FROM all_l0\n", "        )\n", "\n", "        SELECT * FROM merged_base\n", "    \"\"\"\n", "    keyword_df = read_sql_query(query, CON_REDSHIFT)\n", "    keyword_df.head(1)\n", "except:\n", "    keyword_df = pd.DataFrame(columns=[\"city\", \"l0\", \"l1\", \"keys\"])\n", "    pass"]}, {"cell_type": "markdown", "id": "5cea33f2-9a16-47c7-9a69-81115e194539", "metadata": {"tags": []}, "source": ["### Share of Voices"]}, {"cell_type": "code", "execution_count": null, "id": "cf41c90d-8c0d-4518-b1af-1af63527433c", "metadata": {}, "outputs": [], "source": ["try:\n", "    impression_query = f\"\"\"\n", "        WITH shown_raw AS (\n", "            SELECT mid.at_date_ist, LOWER(mid.device_uuid) AS device_uuid, mid.traits__merchant_id AS merchant_id, \n", "            COALESCE(mid.properties__child_widget_id, mid.properties__widget_id) AS product_id, mid.session_uuid, 1 AS row_count\n", "            FROM spectrum.mobile_impression_data mid\n", "            WHERE mid.at_date_ist BETWEEN DATE('{start_date}') AND DATE('{end_date}') AND mid.name = 'Product Shown'\n", "            GROUP BY 1,2,3,4,5,6\n", "        ),\n", "\n", "        shown AS (\n", "            SELECT at_date_ist, merchant_id, product_id, SUM(row_count) AS impressions\n", "            FROM shown_raw\n", "            GROUP BY 1, 2, 3\n", "        ),\n", "\n", "        atc_raw AS (\n", "            SELECT med.at_date_ist, med.traits__merchant_id AS merchant_id, LOWER(med.device_uuid) AS device_uuid, med.session_uuid, \n", "            med.properties__product_id AS product_id, 1 AS row_count\n", "            FROM spectrum.mobile_event_data med\n", "            WHERE med.at_date_ist BETWEEN DATE('{start_date}') AND DATE('{end_date}') AND med.name = 'Product Added'\n", "            GROUP BY 1,2,3,4,5,6\n", "        ),\n", "\n", "        atc AS (\n", "            SELECT at_date_ist, merchant_id, product_id, SUM(row_count) AS atc\n", "            FROM atc_raw\n", "            GROUP BY 1, 2, 3\n", "        ),\n", "\n", "        pre_final AS (\n", "            SELECT s.at_date_ist AS date_, pos_outlet_id, s.product_id, ipm.item_id, cd.l0, cd.l1, l2, impressions, atc\n", "            FROM shown s\n", "            LEFT JOIN atc ac ON ac.at_date_ist = s.at_date_ist AND ac.merchant_id = s.merchant_id AND ac.product_id = s.product_id\n", "            JOIN lake_rpc.item_product_mapping ipm ON s.product_id = ipm.product_id\n", "            JOIN lake_rpc.item_category_details cd ON cd.item_id = ipm.item_id\n", "            JOIN dwh.dim_merchant_outlet_facility_mapping dmm ON dmm.frontend_merchant_id = s.merchant_id AND is_express_store = True AND dmm.is_current = True \n", "        ),\n", "\n", "        final AS (\n", "            SELECT date_, cl.name AS city, pos_outlet_id, item_id, l0, l1, l2, impressions, atc\n", "            FROM pre_final a\n", "            JOIN lake_retail.console_outlet co ON co.id = pos_outlet_id AND co.active = 1 AND co.business_type_id = 7\n", "            JOIN lake_retail.console_location cl ON co.tax_location_id = cl.id\n", "        )\n", "\n", "        SELECT date_, city, l0, l1, SUM(impressions) AS impression, SUM(atc) AS atc\n", "        FROM final\n", "        GROUP BY 1,2,3,4\n", "        \"\"\"\n", "    impressions_df = read_sql_query(impression_query, CON_REDSHIFT)\n", "    impressions_df.shape\n", "except:\n", "    impressions_df = pd.DataFrame(\n", "        columns=[\"date_\", \"city\", \"l0\", \"l1\", \"impression\", \"atc\"]\n", "    )\n", "    pass"]}, {"cell_type": "code", "execution_count": null, "id": "4c16577d-f179-48fe-ba12-cef0dfcd31d0", "metadata": {}, "outputs": [], "source": ["l0_agg_impressions_df = (\n", "    impressions_df.groupby([\"date_\", \"city\", \"l0\"])\n", "    .agg({\"impression\": \"sum\", \"atc\": \"sum\"})\n", "    .reset_index()\n", ")\n", "l0_agg_impressions_df[\"l1\"] = \"All\"\n", "\n", "l0_agg_impressions_df = l0_agg_impressions_df[\n", "    [\"date_\", \"city\", \"l0\", \"l1\", \"impression\", \"atc\"]\n", "].drop_duplicates()\n", "\n", "impressions_agg_df = pd.concat([impressions_df, l0_agg_impressions_df])\n", "\n", "city_agg_impressions_df = (\n", "    impressions_agg_df.groupby([\"date_\", \"l0\", \"l1\"])\n", "    .agg({\"impression\": \"sum\", \"atc\": \"sum\"})\n", "    .reset_index()\n", ")\n", "city_agg_impressions_df[\"city\"] = \"Pan India\"\n", "\n", "city_agg_impressions_df = city_agg_impressions_df[\n", "    [\"date_\", \"city\", \"l0\", \"l1\", \"impression\", \"atc\"]\n", "].drop_duplicates()\n", "\n", "impressions_agg_df = pd.concat([impressions_agg_df, city_agg_impressions_df])\n", "\n", "city_total_df = (\n", "    impressions_agg_df[impressions_agg_df[\"l1\"] != \"All\"]\n", "    .groupby([\"date_\", \"city\"])\n", "    .agg({\"impression\": \"sum\"})\n", "    .reset_index()\n", ")\n", "city_total_df = city_total_df.rename(columns={\"impression\": \"tot_imp\"})\n", "\n", "impressions_agg_df = impressions_agg_df.merge(\n", "    city_total_df, on=[\"date_\", \"city\"], how=\"left\"\n", ")\n", "\n", "impressions_agg_df[\"sov\"] = (\n", "    impressions_agg_df[\"impression\"] / impressions_agg_df[\"tot_imp\"]\n", ")\n", "\n", "impressions_agg_df[\"conv\"] = (\n", "    impressions_agg_df[\"atc\"] / impressions_agg_df[\"impression\"]\n", ")\n", "\n", "impressions_agg_df = impressions_agg_df[\n", "    [\"date_\", \"city\", \"l0\", \"l1\", \"sov\", \"conv\"]\n", "].drop_duplicates()\n", "\n", "impressions_agg_df.head(1)"]}, {"cell_type": "markdown", "id": "a6313f4b-0092-4bd0-90c4-a60a27a13889", "metadata": {"tags": []}, "source": ["## Merge All DataFrames"]}, {"cell_type": "code", "execution_count": null, "id": "a55d9ca9-70e6-4dd3-a0af-ed34b2ebbf7b", "metadata": {}, "outputs": [], "source": ["customer_agg_df[\"date_\"] = pd.to_datetime(customer_agg_df[\"date_\"])\n", "impressions_agg_df[\"date_\"] = pd.to_datetime(impressions_agg_df[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "670f924c-d55a-47c8-a307-5efe0b7c2438", "metadata": {}, "outputs": [], "source": ["merged_df = pd.merge(\n", "    sales_agg_final_df,\n", "    availability_df,\n", "    on=[\"l0\", \"city\", \"date_\", \"l0\", \"l1\"],\n", "    how=\"left\",\n", ")\n", "merged_df[\"date_\"] = pd.to_datetime(merged_df[\"date_\"])\n", "\n", "merged_df = pd.merge(merged_df, new_skus_count_df, on=[\"l0\", \"city\", \"l1\"], how=\"left\")\n", "\n", "merged_df = pd.merge(merged_df, keyword_df, on=[\"l0\", \"city\", \"l1\"], how=\"left\")\n", "\n", "merged_df = pd.merge(\n", "    merged_df, assortment_agg_df, on=[\"l0\", \"l1\", \"city\", \"date_\"], how=\"left\"\n", ")\n", "\n", "merged_df = pd.merge(\n", "    merged_df, impressions_agg_df, on=[\"l0\", \"l1\", \"city\", \"date_\"], how=\"left\"\n", ")\n", "\n", "merged_df = pd.merge(\n", "    merged_df, customer_agg_df, on=[\"l0\", \"l1\", \"city\", \"date_\"], how=\"left\"\n", ")\n", "\n", "merged_df = merged_df[~merged_df[\"l0\"].isna()]\n", "merged_df.shape"]}, {"cell_type": "markdown", "id": "975cbf74-eb40-464b-8e6e-fa0129789c78", "metadata": {}, "source": ["## Upload to Table (ETL)"]}, {"cell_type": "markdown", "id": "7e5f479c-e018-48eb-a6d8-f2ad248d5023", "metadata": {"tags": []}, "source": ["### Weekly PnL Details"]}, {"cell_type": "code", "execution_count": null, "id": "7f2d86fc-4e03-424e-ac21-f1c1bf99a8b0", "metadata": {}, "outputs": [], "source": ["merged_df = merged_df[\n", "    [\n", "        \"city\",\n", "        \"date_\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"tot_carts\",\n", "        \"carts\",\n", "        \"qty_sold\",\n", "        \"sp_gmv\",\n", "        \"mrp_gmv\",\n", "        \"rm\",\n", "        \"bf\",\n", "        \"sf\",\n", "        \"rl\",\n", "        \"dump\",\n", "        \"gmv_avail\",\n", "        \"gmv_wt_avail\",\n", "        \"rm_skus\",\n", "        \"rm_avail\",\n", "        \"rm_wt_avail\",\n", "        \"new_sku\",\n", "        \"item_gmv\",\n", "        \"user_buying\",\n", "        \"ntc\",\n", "        \"ntp\",\n", "        \"sov\",\n", "        \"conv\",\n", "        \"keys\",\n", "    ]\n", "].drop_duplicates()\n", "merged_df[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))\n", "merged_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "a27abca5-399c-402b-a245-c00bf376273f", "metadata": {}, "outputs": [], "source": ["columns = [\n", "    {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"City\"},\n", "    {\"name\": \"date_\", \"type\": \"datetime\", \"description\": \"Date of Upload\"},\n", "    {\"name\": \"l0\", \"type\": \"varchar\", \"description\": \"L0 Category\"},\n", "    {\"name\": \"l1\", \"type\": \"varchar\", \"description\": \"L1 Category\"},\n", "    {\"name\": \"tot_carts\", \"type\": \"float\", \"description\": \"Total Carts\"},\n", "    {\"name\": \"carts\", \"type\": \"float\", \"description\": \"Category Carts\"},\n", "    {\"name\": \"qty_sold\", \"type\": \"float\", \"description\": \"Catgeory Sales Qty\"},\n", "    {\"name\": \"sp_gmv\", \"type\": \"float\", \"description\": \"Selling Price GMV\"},\n", "    {\"name\": \"mrp_gmv\", \"type\": \"float\", \"description\": \"MRP GMV\"},\n", "    {\"name\": \"rm\", \"type\": \"float\", \"description\": \"Retained Margin\"},\n", "    {\"name\": \"bf\", \"type\": \"float\", \"description\": \"Brand Funding\"},\n", "    {\"name\": \"sf\", \"type\": \"float\", \"description\": \"Self Funding\"},\n", "    {\"name\": \"rl\", \"type\": \"float\", \"description\": \"Return Loss\"},\n", "    {\"name\": \"dump\", \"type\": \"float\", \"description\": \"Total Dump Qty\"},\n", "    {\"name\": \"gmv_avail\", \"type\": \"float\", \"description\": \"Top GMV SkUs Availability\"},\n", "    {\n", "        \"name\": \"gmv_wt_avail\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Top GMV Weighted SkUs Availability\",\n", "    },\n", "    {\"name\": \"rm_skus\", \"type\": \"float\", \"description\": \"RM SKUs Count\"},\n", "    {\"name\": \"rm_avail\", \"type\": \"float\", \"description\": \"RM Availability\"},\n", "    {\"name\": \"rm_wt_avail\", \"type\": \"float\", \"description\": \"RM Weighted Availability\"},\n", "    {\"name\": \"new_sku\", \"type\": \"float\", \"description\": \"New Assortment Count\"},\n", "    {\"name\": \"item_gmv\", \"type\": \"float\", \"description\": \"New Assortment GMV\"},\n", "    {\n", "        \"name\": \"user_buying\",\n", "        \"type\": \"float\",\n", "        \"description\": \"New Assortment Per Day Users\",\n", "    },\n", "    {\"name\": \"ntc\", \"type\": \"float\", \"description\": \"New to Category\"},\n", "    {\"name\": \"ntp\", \"type\": \"float\", \"description\": \"New to Category and Platform\"},\n", "    {\"name\": \"sov\", \"type\": \"float\", \"description\": \"Share of Voices\"},\n", "    {\"name\": \"conv\", \"type\": \"float\", \"description\": \"Impression Conversion\"},\n", "    {\n", "        \"name\": \"keys\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Searches with less than 10 percent ATC\",\n", "    },\n", "    {\"name\": \"updated_at\", \"type\": \"datetime\", \"description\": \"Date Time of Run\"},\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"category_pnl_daily_details_v3\",\n", "    \"column_dtypes\": columns,\n", "    \"primary_key\": [\"city\", \"l0\", \"l1\", \"date_\", \"updated_at\"],\n", "    \"sortkey\": [\"city\", \"l0\", \"l1\", \"date_\", \"updated_at\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"append\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"Category PnL Data Daily Snapshot\",  # Description of the table being sent to redshift\n", "}\n", "\n", "pb.to_redshift(merged_df, **kwargs)"]}, {"cell_type": "markdown", "id": "8bbd9499-10fb-4a3e-9491-353c2428af48", "metadata": {"tags": []}, "source": ["### Assortment Classification"]}, {"cell_type": "markdown", "id": "f00f27f7-026d-46ab-bddf-0e993e9ee741", "metadata": {}, "source": ["#### New Assortment"]}, {"cell_type": "code", "execution_count": null, "id": "a219c79f-ec3f-490d-a38f-424513d89665", "metadata": {}, "outputs": [], "source": ["new_assortment = first_grn_df[[\"city\", \"item_id\"]].drop_duplicates()\n", "new_assortment[\"new_flag\"] = 1"]}, {"cell_type": "markdown", "id": "8c5d1298-8d92-44a1-b484-ea3325f3617f", "metadata": {"tags": []}, "source": ["#### Top SKUs Calculations"]}, {"cell_type": "code", "execution_count": null, "id": "4c4fc27b-2e9b-4800-8908-7febe36f5c7c", "metadata": {}, "outputs": [], "source": ["top_sku = sku_sales_df.groupby([\"item_id\"]).agg({\"gmv\": \"sum\"}).reset_index()\n", "top_sku[\"city\"] = \"Pan India\"\n", "top_sku[\"rank\"] = top_sku.groupby([\"city\"])[\"gmv\"].rank(method=\"dense\", ascending=False)\n", "\n", "top_sku = top_sku[top_sku[\"rank\"] <= 100][[\"item_id\"]].drop_duplicates()\n", "top_sku[\"item_id\"] = top_sku[\"item_id\"].astype(int)\n", "top_sku[\"top_flag\"] = 1"]}, {"cell_type": "markdown", "id": "82131149-c069-4a97-ac40-451f4eb5c5fe", "metadata": {"tags": []}, "source": ["#### Weekly Average Availability "]}, {"cell_type": "code", "execution_count": null, "id": "dad67788-6009-4235-aa05-275d28a3d020", "metadata": {}, "outputs": [], "source": ["weekly_avail_df = (\n", "    avail_base_df.groupby([\"city\", \"item_id\"])\n", "    .agg({\"avail_hour\": \"sum\", \"tot_hour\": \"sum\", \"wt_score\": \"mean\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "markdown", "id": "dcb6c76f-f50c-4f3e-b96f-b369dbccc879", "metadata": {"tags": []}, "source": ["#### Weekly Average Sales"]}, {"cell_type": "code", "execution_count": null, "id": "dbb0c7b1-8edf-422b-a121-8d8b14962f80", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "    WITH sales_base AS (\n", "        WITH item_mapping as (\n", "            SELECT DISTINCT ipr.product_id, \n", "            CASE \n", "                WHEN ipr.item_id IS NULL THEN ipom_0.item_id \n", "                ELSE ipr.item_id \n", "            END AS item_id, \n", "            CASE \n", "                WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1) \n", "                ELSE COALESCE(ipom_0.multiplier,1) \n", "            END AS multiplier\n", "            FROM lake_rpc.item_product_mapping ipr \n", "            LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id \n", "            LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", "        ),\n", "\n", "        sales AS (\n", "            SELECT DATE(oid.cart_checkout_ts_ist) AS order_date, oid.outlet_id, oid.product_id, im.item_id, oid.cart_id AS order_id, \n", "            j.dim_customer_key AS customer_id, im.multiplier, \n", "            ((oid.procured_quantity - oid.total_doorstep_return_quantity) * im.multiplier) AS sales_quantity,\n", "            sales_quantity * (oid.unit_selling_price * 1.00000 / im.multiplier) AS selling_gmv\n", "            FROM dwh.fact_sales_order_item_details oid\n", "            JOIN dwh.fact_sales_order_details j ON oid.cart_id = j.cart_id\n", "            JOIN item_mapping im on im.product_id = oid.product_id \n", "            WHERE oid.cart_checkout_ts_ist BETWEEN ('{end_date}' - 30 || ' 00:00:00')::timestamp AND ('{end_date}' || ' 23:59:59')::timestamp \n", "            AND oid.is_internal_order = false \n", "            AND (oid.order_type NOT ILIKE '%%internal%%' OR oid.order_type IS NULL) \n", "            AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED'\n", "        ),\n", "\n", "        pre_summary AS (\n", "            SELECT a.*, cl.name AS city\n", "            FROM sales a\n", "            JOIN lake_retail.console_outlet co ON co.id = a.outlet_id AND co.active = 1 AND co.business_type_id = 7\n", "            LEFT JOIN lake_retail.console_location cl ON cl.id = co.tax_location_id\n", "        ),\n", "        \n", "        summary AS (\n", "            SELECT city, item_id, order_date, order_id, customer_id, SUM(sales_quantity) AS quantity, SUM(selling_gmv) AS gmv\n", "            FROM pre_summary\n", "            GROUP BY 1,2,3,4,5\n", "        )\n", "        \n", "        SELECT * FROM summary    \n", "    ),\n", "        \n", "    daily_agg AS (\n", "        SELECT city, item_id, order_date, COUNT(DISTINCT order_id) AS carts, COUNT(DISTINCT customer_id) AS users, SUM(quantity) AS sales, SUM(gmv) AS gmv\n", "        FROM sales_base\n", "        GROUP BY 1,2,3\n", "    ),\n", "        \n", "    base AS (\n", "        SELECT city, item_id, SUM(carts) AS carts, SUM(users) AS users, SUM(sales) AS sales, SUM(gmv) AS gmv, AVG(sales) AS l7_sales, AVG(gmv) AS l7_gmv\n", "        FROM daily_agg\n", "        WHERE order_date BETWEEN DATE('{start_date}') AND DATE('{end_date}')\n", "        GROUP BY 1,2\n", "    ),\n", "        \n", "    l30_average AS (\n", "        SELECT city, item_id, AVG(sales) AS l30_sales, AVG(gmv) AS l30_gmv\n", "        FROM daily_agg\n", "        GROUP BY 1,2\n", "    ),\n", "    \n", "    merge_base AS (\n", "        SELECT a.*, l30_sales, l30_gmv\n", "        FROM base a\n", "        LEFT JOIN l30_average b ON a.item_id = b.item_id AND a.city = b.city\n", "    )\n", "    \n", "    SELECT * FROM merge_base\n", "    \"\"\"\n", "weekly_sales_df = read_sql_query(query, CON_REDSHIFT)"]}, {"cell_type": "markdown", "id": "d37b02a7-3eb8-45ac-97ab-deb735c4403c", "metadata": {"tags": []}, "source": ["#### Merge All Frames"]}, {"cell_type": "code", "execution_count": null, "id": "4ce2b049-dbf5-485e-9518-fba741124a4d", "metadata": {}, "outputs": [], "source": ["assortment_class_df = skus_calc_df[skus_calc_df[\"city\"] != \"Pan India\"][\n", "    [\"city\", \"item_id\", \"l0\", \"l1\", \"rm_flag\", \"gmv_flag\"]\n", "].drop_duplicates()\n", "\n", "assortment_class_df = assortment_class_df.merge(top_sku, on=[\"item_id\"], how=\"left\")\n", "assortment_class_df[\"top_flag\"] = np.where(\n", "    assortment_class_df[\"top_flag\"].isna(), 0, assortment_class_df[\"top_flag\"]\n", ").astype(int)\n", "\n", "assortment_class_df = assortment_class_df.merge(\n", "    new_assortment, on=[\"item_id\", \"city\"], how=\"left\"\n", ")\n", "assortment_class_df[\"new_flag\"] = np.where(\n", "    assortment_class_df[\"new_flag\"].isna(), 0, assortment_class_df[\"new_flag\"]\n", ").astype(int)\n", "\n", "assortment_class_df = assortment_class_df[\n", "    ~(\n", "        (assortment_class_df[\"top_flag\"] == 0)\n", "        & (assortment_class_df[\"rm_flag\"] == 0)\n", "        & (assortment_class_df[\"gmv_flag\"] == 0)\n", "        & (assortment_class_df[\"new_flag\"] == 0)\n", "    )\n", "]\n", "\n", "assortment_class_df = assortment_class_df.merge(\n", "    weekly_avail_df, on=[\"item_id\", \"city\"], how=\"left\"\n", ")\n", "assortment_class_df = assortment_class_df.merge(\n", "    weekly_sales_df, on=[\"item_id\", \"city\"], how=\"left\"\n", ")\n", "\n", "assortment_class_df[\"avail\"] = (\n", "    assortment_class_df[\"avail_hour\"] / assortment_class_df[\"tot_hour\"]\n", ")\n", "\n", "assortment_class_df[\"date_\"] = start_date\n", "\n", "assortment_class_df = assortment_class_df[\n", "    [\n", "        \"city\",\n", "        \"date_\",\n", "        \"item_id\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"rm_flag\",\n", "        \"gmv_flag\",\n", "        \"top_flag\",\n", "        \"new_flag\",\n", "        \"avail\",\n", "        \"wt_score\",\n", "        \"carts\",\n", "        \"users\",\n", "        \"sales\",\n", "        \"gmv\",\n", "        \"l7_sales\",\n", "        \"l7_gmv\",\n", "        \"l30_sales\",\n", "        \"l30_gmv\",\n", "    ]\n", "]\n", "\n", "assortment_class_df[\"updated_at\"] = pd.to_datetime(\n", "    datetime.today() + <PERSON><PERSON><PERSON>(hours=5.5)\n", ")\n", "assortment_class_df[\"item_id\"] = assortment_class_df[\"item_id\"].astype(int)\n", "\n", "assortment_class_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "f6205009-4b09-4f9c-9feb-67c21b0431f2", "metadata": {}, "outputs": [], "source": ["columns = [\n", "    {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"City\"},\n", "    {\"name\": \"date_\", \"type\": \"datetime\", \"description\": \"Date of Upload\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"Item ID\"},\n", "    {\"name\": \"l0\", \"type\": \"varchar\", \"description\": \"L0 Category\"},\n", "    {\"name\": \"l1\", \"type\": \"varchar\", \"description\": \"L1 Category\"},\n", "    {\"name\": \"rm_flag\", \"type\": \"integer\", \"description\": \"RM Flag\"},\n", "    {\"name\": \"gmv_flag\", \"type\": \"integer\", \"description\": \"GMV Flag\"},\n", "    {\"name\": \"top_flag\", \"type\": \"integer\", \"description\": \"Top Flag\"},\n", "    {\"name\": \"new_flag\", \"type\": \"integer\", \"description\": \"Top Flag\"},\n", "    {\"name\": \"avail\", \"type\": \"float\", \"description\": \"Availability\"},\n", "    {\"name\": \"wt_score\", \"type\": \"float\", \"description\": \"Weighted Availability\"},\n", "    {\"name\": \"carts\", \"type\": \"float\", \"description\": \"Category Carts\"},\n", "    {\"name\": \"users\", \"type\": \"float\", \"description\": \"Customers\"},\n", "    {\"name\": \"sales\", \"type\": \"float\", \"description\": \"L7 Total Sales\"},\n", "    {\"name\": \"gmv\", \"type\": \"float\", \"description\": \"L7 Total GMV\"},\n", "    {\"name\": \"l7_sales\", \"type\": \"float\", \"description\": \"L7 Average Sales\"},\n", "    {\"name\": \"l7_gmv\", \"type\": \"float\", \"description\": \"L7 Average GMV\"},\n", "    {\"name\": \"l30_sales\", \"type\": \"float\", \"description\": \"L30 Average Sales\"},\n", "    {\"name\": \"l30_gmv\", \"type\": \"float\", \"description\": \"L30 Average GMV\"},\n", "    {\"name\": \"updated_at\", \"type\": \"datetime\", \"description\": \"Date Time of Run\"},\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"category_pnl_item_details_v2\",\n", "    \"column_dtypes\": columns,\n", "    \"primary_key\": [\"city\", \"item_id\", \"l0\", \"l1\", \"date_\"],\n", "    \"sortkey\": [\"city\", \"item_id\", \"l0\", \"l1\", \"date_\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"append\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"Category PnL Assortment Classification Snapshot\",  # Description of the table being sent to redshift\n", "}\n", "\n", "pb.to_redshift(assortment_class_df, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}