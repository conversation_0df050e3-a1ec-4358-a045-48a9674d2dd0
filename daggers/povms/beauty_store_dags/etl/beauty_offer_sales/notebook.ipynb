{"cells": [{"cell_type": "code", "execution_count": null, "id": "0929aff6-6734-420c-a45b-f12d8b9cdd45", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import os\n", "import time\n", "import json\n", "\n", "!pip install matplotlib\n", "import matplotlib.pyplot as plt\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "966ade78-990f-43f5-abd1-12f0bc91bc9a", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)\n", "pd.options.mode.chained_assignment = None  # default='warn'"]}, {"cell_type": "code", "execution_count": null, "id": "14963271-a140-407e-9e2d-8039d44ffc8c", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pytz\n", "import calendar\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")"]}, {"cell_type": "code", "execution_count": null, "id": "6f795d6b-8773-450f-a7df-9391b04862ff", "metadata": {}, "outputs": [], "source": ["item_list_df = pb.from_sheets(\"1Yc4uxPdCE3JP8kWCInExn1tu6KLz4rZCVnEeJQQKMB0\", \"PIDs\")"]}, {"cell_type": "code", "execution_count": null, "id": "30a72d4a-d463-4b43-bcc4-2a5<PERSON>aedebeb", "metadata": {}, "outputs": [], "source": ["item_list_df.to_csv(\"csv file.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "9782555a-4eb0-4c3f-ba2f-0568fecded14", "metadata": {}, "outputs": [], "source": ["item_list_df = pd.read_csv(\"csv file.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "fa867c0a-a270-41b8-b1b5-c530dd680d86", "metadata": {}, "outputs": [], "source": ["item_list = tuple(list(item_list_df[\"PID\"]))"]}, {"cell_type": "code", "execution_count": null, "id": "8b73c00c-4981-48cc-8f89-52f05c246e7c", "metadata": {}, "outputs": [], "source": ["sale_query = f\"\"\"\n", "WITH\n", "cancelled AS (\n", "    SELECT\n", "        order_id,\n", "        MIN(install_ts + INTERVAL '5' HOUR + INTERVAL '30' MINUTE) AS cancelled_ts_ist\n", "    FROM\n", "       oms_bifrost.oms_order_event\n", "\n", "    WHERE\n", "        insert_ds_ist >= '2023-05-05'\n", "        AND event_type_key = 'state_change'\n", "        AND UPPER(JSON_EXTRACT_SCALAR(extra, '$.to')) LIKE '%%CANCELLED%%'\n", "    \n", "    GROUP BY \n", "        1\n", "),\n", "\n", "\n", "ff as (\n", "SELECT \n", "    CAST(\n", "        o.install_ts + INTERVAL '5' HOUR + INTERVAL '30' MINUTE\n", "        AS DATE\n", "    ) AS order_create_date_ist,\n", "    EXTRACT(\n", "        hour from o.install_ts + INTERVAL '5' HOUR + INTERVAL '30' MINUTE\n", "    ) AS order_create_hour_ist,\n", "\n", "    o.id AS order_id,\n", "    o.cart_id,\n", "        CAST(cancelled_ts_ist AS DATE) AS cancelled_date_ist,\n", "    EXTRACT(HOUR FROM co.cancelled_ts_ist) AS cancelled_hour_ist,\n", "    oi.quantity as quantity,\n", "     oi.quantity * oi.selling_price as gmv\n", "    -- o.current_status AS order_status,\n", "    -- co.cancelled_ts_ist AS order_cancelled_ts_ist\n", "\n", "FROM     \n", "    oms_bifrost.oms_order_item \n", "        AS oi\n", "JOIN        \n", "oms_bifrost.oms_order\n", "        AS o\n", "        ON oi.order_id = o.id\n", "        AND o.insert_ds_ist >= '2023-05-05'\n", "JOIN    \n", "    oms_bifrost.oms_cart \n", "        AS c \n", "        ON o.cart_id = c.id\n", "        AND c.insert_ds_ist  >= '2023-05-05'\n", "JOIN \n", "    oms_bifrost.oms_merchant \n", "        AS m \n", "        ON o.merchant_id = m.id\n", "        AND m.insert_ds_ist >='2023-05-05'\n", "LEFT JOIN \n", "      dwh.city_zone_mapping\n", "        AS czm \n", "        ON m.external_id = czm.frontend_merchant_id  \n", "LEFT JOIN \n", "    cancelled \n", "        AS co \n", "        ON o.id = co.order_id                                          \n", "inner join\n", "    lake_rpc.item_product_mapping b on oi.product_id = b.product_id and b.active > 0\n", "    INNER JOIN lake_cms.gr_product_category_mapping pcm ON b.product_id = pcm.product_id\n", "    INNER JOIN lake_cms.gr_category cat2 ON pcm.category_id = cat2.id AND pcm.is_primary=true\n", "    INNER JOIN lake_cms.gr_category cat1 ON cat2.parent_category_id = cat1.id\n", "    INNER JOIN lake_cms.gr_category cat ON cat1.parent_category_id= cat.id\n", "Inner join dwh.dim_product dp on dp.product_id = b.product_id and dp.is_product_enabled = true and dp.is_current = true\n", "WHERE \n", "    oi.insert_ds_ist >= '2023-05-05'\n", "    AND LOWER(o.type) NOT LIKE '%%internal%%'\n", "    AND o.total_cost > 0\n", "    and (dp.product_id in {item_list} or cat.id = 13)\n", "GROUP BY\n", "    1,2,3,4,5,6,7,8\n", "),\n", "\n", "-- select * from ff limit 100\n", "\n", "final as (select order_create_date_ist, order_create_hour_ist, order_id, cart_id, sum(quantity) as quantity, sum(gmv) as gmv\n", "from ff\n", "where \n", "    cancelled_date_ist is null\n", "group by 1,2,3,4)\n", "    \n", "    select * from final where gmv >= 749\n", "    \"\"\"\n", "sales_df = pd.read_sql(sale_query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "8f0873cd-2488-47cb-9b74-93535a4a8b8a", "metadata": {}, "outputs": [], "source": ["sales_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "6a1a6b98-cf90-4105-b0e6-0f04ed9ad2e0", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    sales_df,\n", "    \"1Yc4uxPdCE3JP8kWCInExn1tu6KLz4rZCVnEeJQQKMB0\",\n", "    \"data\",\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}