alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: beauty_offer_sales
dag_type: etl
escalation_priority: low
executor:
  config:
    service_account_name: blinkit-prod-airflow-primary-eks-role
    cpu:
      limit: 1
      request: 0.5
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: airflow-dags-spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RYT77SBG
path: povms/beauty_store_dags/etl/beauty_offer_sales
paused: true
project_name: beauty_store_dags
schedule:
  end_date: '2023-08-03T00:00:00'
  interval: 30 * * * *
  start_date: '2023-05-05T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
pool: povms_pool
