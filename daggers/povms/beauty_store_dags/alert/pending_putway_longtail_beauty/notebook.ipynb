{"cells": [{"cell_type": "code", "execution_count": null, "id": "c4c5715d-944d-45a9-91d1-85a89edc7f7d", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "fdda1bf1-53a6-478a-9f14-fa947b3c0aa5", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1sXnYtESJhQU8rNvhEuVvXnsLMfurTJ043bw4rDaOwWc\"\n", "\n", "df_1 = pb.from_sheets(sheet_id, \"Putway Summary\")"]}, {"cell_type": "code", "execution_count": null, "id": "d5c5394d-0262-4941-af67-0845036f44bd", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5.5,\n", "    row_height=1.2,\n", "    font_size=40,\n", "    header_color=\"#fed8b1\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, cellLoc=\"left\", **kwargs\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "55c72f2b-a200-45dc-bbb2-a17420661c7f", "metadata": {}, "outputs": [], "source": ["fig, ax = render_mpl_table(\n", "    df_1,\n", "    header_columns=0,\n", ")\n", "fig.savefig(\"pending_putway_longtail_beauty.png\")\n", "file_check = \"./pending_putway_longtail_beauty.png\"\n", "filepath = file_check\n", "channel = \"blinkit-wh-beauty-skus\"\n", "pb.send_slack_message(channel=channel, files=[filepath])"]}, {"cell_type": "code", "execution_count": null, "id": "9e395232-c694-4a6b-ac87-c1bb1d85c248", "metadata": {}, "outputs": [], "source": ["# pending_putway_longtail_beauty = \"/pending_putway_longtail_beauty.png\""]}, {"cell_type": "code", "execution_count": null, "id": "3f26f2e8-4b8a-4654-ba83-d7880dee1095", "metadata": {}, "outputs": [], "source": ["# fig, ax = render_mpl_table(df_1)\n", "# fig.savefig(pending_putway_longtail_beauty)"]}, {"cell_type": "code", "execution_count": null, "id": "7ea315f6-1b39-4669-a3ee-c08e106260e6", "metadata": {}, "outputs": [], "source": ["# pb.send_slack_message(\n", "#     channel=\"blinkit-wh-beauty-skus\",\n", "#     text=\"Pending Putway Longtail Beauty\",\n", "#     files=[\"/pending_putway_longtail_beauty.png\"],\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "4b1a3842-a74a-421b-a161-f2d4ef93cce3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}