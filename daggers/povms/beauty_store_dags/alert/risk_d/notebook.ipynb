{"cells": [{"cell_type": "code", "execution_count": null, "id": "6c8aa56e-3fa7-4cbc-b1c3-8686768b2cba", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import datetime as dt\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "\n", "!pip install tqdm\n", "from tqdm import tqdm\n", "import psycopg2\n", "from pandas import read_sql_query\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)\n", "pd.set_option(\"display.float_format\", lambda x: \"%.3f\" % x)"]}, {"cell_type": "code", "execution_count": null, "id": "27f05704-250f-4042-9902-8d96a683115c", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "f23e8cf3-9119-480d-aeb0-dcf92f42e97f", "metadata": {}, "outputs": [], "source": ["def df_1():\n", "    df_1 = \"\"\"\n", "    \n", "with\n", "run_id_details as\n", "    (select facility_id, run_id, date(completed_at + interval '330' minute) as run_date,\n", "        row_number() over(partition by facility_id, date(completed_at + interval '330' minute) order by (started_at + interval '330' minute)) as rnk\n", "            from ars.job_run\n", "                where started_at >= cast(current_date - interval '1' day as date)\n", "                    and json_extract_scalar(simulation_params, '$.run') = 'multi_business'\n", "                    -- and json_extract_path_text(simulation_params, 'type',true) = 'any_day_po'\n", "                    and success = 1\n", "    ),\n", "item_details as\n", "    (select * from supply_etls.item_details),\n", "\n", "final_indent as\n", "    (select  fi.outlet_id,fi.item_id as pp,\n", "        physical_facility_id as facility_id, fi.facility_name, fi.item_id,cast(fi.trigger_doi_date as date) trigger_doi_date ,rid.run_date,error_type,error_details,\n", "        auto_po_eligible\n", "        \n", "            from  ars.final_indent fi join run_id_details rid on rid.run_id =  fi.run_id and fi.run_id in (select distinct run_id from run_id_details where rnk = 1)\n", "            where current_inventory = 0\n", "            and (open_po_quantity is null or open_po_quantity =0)\n", "                \n", "                and l0_id in (13)\n", "                    \n", "                    -- group by 1,2,3,4,5,6,7,8,9,10,11\n", "    ),\n", "\n", "-- cpd as\n", "--     (select item_id, run_id, avg_cpd from ars.case_incorporation_result\n", "--         where (insert_ds_ist = cast(current_date  as varchar))\n", "--         and run_id in (select distinct run_id from run_id_details where rnk = 1)\n", "    -- ),\n", "\n", "final_view as\n", "    (select fi.run_date, fi.outlet_id, facility_id, facility_name, fi.item_id,id.item_name,id.l0_category,id.assortment_type,fi.trigger_doi_date,error_type,error_details,id.l0_id,pp,auto_po_eligible\n", "        \n", "            from final_indent fi\n", "            join item_details id on id.item_id = fi.item_id\n", "            \n", "                -- join\n", "                --     cpd on cpd.item_id = fi.item_id and cpd.run_id = fi.run_id\n", "                    \n", "    )\n", "    select facility_name, \n", "    count(distinct ma.item_id) as Risk_D,\n", "    count(case when error_type = 'Case Error' then ma.item_id end) as Case_Error,\n", "    count(case when error_type = 'Load Error' then ma.item_id end) as Load_Error,\n", "    count(case when error_type = 'Other Errors' and error_details  LIKE   '%%margin%%'  then ma.item_id end) as margin_missing,\n", "    count(case when auto_po_eligible = 1 then ma.item_id end) as Auto_raised\n", "    \n", "    \n", "    from final_view  ma\n", "    where run_date = Cast(current_date as date)\n", "    -- 2076,2078,1320\n", "    group by 1\n", "    order by 2 desc\n", "\n", "\n", "       \"\"\"\n", "    return read_sql_query(df_1, trino)\n", "\n", "\n", "df_1 = df_1()"]}, {"cell_type": "code", "execution_count": null, "id": "08c9c4a7-d252-4f20-9e37-921f499dc7b4", "metadata": {}, "outputs": [], "source": ["df_1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8ed7ac8c-7dbd-4370-9d8b-16db09c5d104", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5,\n", "    row_height=1,\n", "    font_size=22,\n", "    header_color=\"#8ce6ff\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, cellLoc=\"left\", **kwargs\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "582ef2e0-c935-4311-8598-addc688d5530", "metadata": {}, "outputs": [], "source": ["fig, ax = render_mpl_table(\n", "    df_1,\n", "    header_columns=0,\n", ")\n", "fig.savefig(\"Risk_D.png\")\n", "file_check = \"./Risk_D.png\"\n", "filepath = file_check\n", "channel = \"bl-beauty_alerts\"\n", "pb.send_slack_message(channel=channel, files=[filepath])"]}, {"cell_type": "code", "execution_count": null, "id": "8d709aee-ecae-496b-b975-eaf1d63e355f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7fb49982-39c9-4802-97bd-84661b918b4a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}