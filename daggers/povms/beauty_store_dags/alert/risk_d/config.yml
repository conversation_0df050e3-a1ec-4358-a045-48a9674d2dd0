alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: risk_d
dag_type: alert
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03R91S1LKH
path: povms/beauty_store_dags/alert/risk_d
paused: false
pool: povms_pool
project_name: beauty_store_dags
schedule:
  end_date: '2024-11-05T00:00:00'
  interval: '30 1 * * *  '
  start_date: '2024-06-10T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 6
