{"cells": [{"cell_type": "code", "execution_count": null, "id": "2dfd8ec0-07fc-4943-ba46-21cd262ecffe", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "7536799b-f451-41e7-b0c6-9df62f113377", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1x4QZvcYn7VEwsc8yTTohZ3fQtJI7HnaODR1ThFLLWzs\"\n", "\n", "df_1 = pb.from_sheets(sheet_id, \"Alert\")"]}, {"cell_type": "code", "execution_count": null, "id": "84fc2b12-e462-4ac6-a856-7961b3ff8e89", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5.5,\n", "    row_height=1.2,\n", "    font_size=40,\n", "    header_color=\"#fed8b1\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, cellLoc=\"left\", **kwargs\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "64e1dcbe-dcaf-46b9-bd97-a18a3a10bb63", "metadata": {}, "outputs": [], "source": ["fig, ax = render_mpl_table(\n", "    df_1,\n", "    header_columns=0,\n", ")\n", "fig.savefig(\"truncation_alert.png\")\n", "file_check = \"./truncation_alert.png\"\n", "filepath = file_check\n", "channel = \"bl-beauty_alerts\"\n", "pb.send_slack_message(channel=channel, files=[filepath])"]}, {"cell_type": "code", "execution_count": null, "id": "ca0cdb64-caf3-4b80-a22a-08b806650e77", "metadata": {}, "outputs": [], "source": ["# pending_putway_longtail_beauty = \"/pending_putway_longtail_beauty.png\""]}, {"cell_type": "code", "execution_count": null, "id": "b373822c-4fdf-4d7b-8fb4-3d40bfee7018", "metadata": {}, "outputs": [], "source": ["# fig, ax = render_mpl_table(df_1)\n", "# fig.savefig(pending_putway_longtail_beauty)"]}, {"cell_type": "code", "execution_count": null, "id": "ff8b9f84-f42f-41eb-9cea-8ba2e780e494", "metadata": {}, "outputs": [], "source": ["# pb.send_slack_message(\n", "#     channel=\"blinkit-wh-beauty-skus\",\n", "#     text=\"Pending Putway Longtail Beauty\",\n", "#     files=[\"/pending_putway_longtail_beauty.png\"],\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "63785ae3-1361-4205-9da1-1c944563ce82", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1x4QZvcYn7VEwsc8yTTohZ3fQtJI7HnaODR1ThFLLWzs\"\n", "\n", "df_1 = pb.from_sheets(sheet_id, \"Alert 2\")"]}, {"cell_type": "code", "execution_count": null, "id": "1f48a919-6718-4cc5-b913-01ee261d10c7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d909e78c-c96c-4f39-bf98-8f07859c8aba", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=6,\n", "    row_height=1,\n", "    font_size=30,\n", "    header_color=\"#fed8b1\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, cellLoc=\"left\", **kwargs\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "a4657269-8a03-41e0-8bb3-e2bfd99bc096", "metadata": {}, "outputs": [], "source": ["fig, ax = render_mpl_table(\n", "    df_1,\n", "    header_columns=0,\n", ")\n", "fig.savefig(\"top_15_truncation_stores.png\")\n", "file_check = \"./top_15_truncation_stores.png\"\n", "filepath = file_check\n", "channel = \"bl-beauty_alerts\"\n", "pb.send_slack_message(channel=channel, files=[filepath])"]}, {"cell_type": "code", "execution_count": null, "id": "7d6f5324-e4ac-465f-8270-dfd84b73dc21", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}