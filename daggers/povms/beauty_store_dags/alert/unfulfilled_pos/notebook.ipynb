{"cells": [{"cell_type": "code", "execution_count": null, "id": "5b3d87e6-1dca-4bad-9ea5-a1d690c9a095", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9d6fbf7a-d8cc-4146-a5a2-3979930fefd5", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import datetime as dt\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "!pip install matplotlib\n", "import matplotlib.pyplot as plt\n", "\n", "!pip install tqdm\n", "from tqdm import tqdm\n", "\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)\n", "pd.set_option(\"display.float_format\", lambda x: \"%.3f\" % x)"]}, {"cell_type": "code", "execution_count": null, "id": "b5eca1c7-cff7-4dff-a19a-f3118bb74142", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "c7af9e73-beed-4fe3-92e3-a8cd9dbca5ee", "metadata": {}, "outputs": [], "source": ["def df_1():\n", "    df_1 = \"\"\"\n", "    \n", "with base as (select\n", "po.po_number,\n", "po.id,\n", "case when poi.run_id is null or poi.run_id = '0' then 'ad-hoc' else poi.run_id end as run_id,\n", "case when po.po_type_id in (1,2) then 'parent_po'\n", "    when po.po_type_id in (3,4,5) then 'child_po'\n", "        else 'internal_po' end as po_type,\n", "date(po.created_at + interval '330' minute) as po_issue_date,\n", "date(po.expiry_date + interval '330' minute) as po_expiry_date,\n", "ps.po_scheduled_date,\n", "tat.tat_days as vendor_tat,\n", "po.outlet_id as be_outlet_id,\n", "rco.name as outlet_name,\n", "rco.facility_id,\n", "rco.facility_name,\n", "po.vendor_id,\n", "po.vendor_name,\n", "poi.manufacturer_id,\n", "poi.manufacturer_name,\n", "poi.item_id,\n", "poi.upc,\n", "poi.name || ' ' || poi.uom_text as item_name,\n", "poi.mrp,\n", "poi.landing_rate,\n", "poi.units_ordered as po_quantity,\n", "poi.units_ordered - poi.remaining_quantity as grn_qty,\n", "-- cast(from_unixtime(grn.created_at/1000000 + 5.5*3600, 'UTC') as timestamp) as grn_ts,\n", "poi.remaining_quantity,\n", "case when poi.bucket_type = 'A' then 'A' else 'B' end as bucket_ab,\n", "case when poi.is_bucket_x = 1 then 'Yes' else 'No' end as bucket_x,\n", "case when poi.is_pl = 1 then 'Yes' else 'No' end as is_pl,\n", "case when poi.is_critical = 1 then 'Yes' else 'No' end as is_critical,\n", "case when po.is_multiple_grn = 1 then 'Yes' else 'No' end as is_multi_grn,\n", "ma.status,\n", "case when poi.storage_type = 1 then 'regular' when poi.storage_type = 2 then 'fridge'\n", "    when poi.storage_type = 3 then 'freezer' when poi.storage_type = 4 then 'large'\n", "    when poi.storage_type = 5 then 'heavy' when poi.storage_type = 6 then 'non_veg_cold'\n", "    when poi.storage_type = 7 then 'non_veg_frozen' when poi.storage_type = 8 then 'fast_moving'\n", "        else 'not_define' end as storage_type,\n", "pos.po_status,au.first_name\n", "from po.purchase_order_items poi\n", "left join po.purchase_order po on po.id = poi.po_id\n", "left join (select po_id_id, date(schedule_date_time + interval '330' minute) as po_scheduled_date\n", "    from po.po_schedule) ps on ps.po_id_id = po.id\n", "left join (select id, name, rco.facility_id, pf.facility_name from retail.console_outlet rco\n", "    left join (select facility_id, internal_facility_identifier as facility_name from po.physical_facility\n", "    where id in (select max(id) as id from po.physical_facility pf group by facility_id)) pf on pf.facility_id = rco.facility_id)\n", "    rco on rco.id = po.outlet_id\n", "left join (select item_id, facility_id, mas.name as status\n", "    from rpc.product_facility_master_assortment ma\n", "        left join rpc.product_master_assortment_substate mas on mas.id = ma.master_assortment_substate_id\n", "        where ma.active = 1) ma on ma.item_id = poi.item_id and ma.facility_id = rco.facility_id\n", "left join (select po_id, po_state_id, ppos.name as po_status,assignee_id from po.purchase_order_status pos\n", "    left join po.purchase_order_state ppos on ppos.id = pos.po_state_id) pos on pos.po_id = po.id\n", "    -- left join lake_po.po_grn grn on grn.item_id = poi.item_id and grn.po_id = po.id\n", "   \n", "left join (select item_id, outlet_id, vendor_id, tat_days from po.item_outlet_vendor_tat\n", "    where updated_at in (select max(updated_at) as updated_at from po.item_outlet_vendor_tat group by item_id, outlet_id, vendor_id)\n", "    and active = 1) tat on tat.item_id = poi.item_id and tat.vendor_id = po.vendor_id and tat.outlet_id = po.outlet_id\n", " left join lake_retail.auth_user as au on au.id = po.created_by\n", "where \n", "-- (pos.po_state_id in (2,3,4,10,13,14,15) or po.is_multiple_grn = 1)\n", "-- and pos.po_state_id not in (4,5,8,10) and po.active = 1\n", "-- and\n", "po_scheduled_date between cast(current_date as timestamp)-interval '30' day - interval '330' minute and\n", "    cast(current_date as timestamp)+interval '1' day - interval '330' minute\n", "-- and po.po_type_id !=11\n", "-- po.insert_ds_ist between cast(cast(current_date as timestamp)-interval '60' day as varchar) and\n", "--     cast(cast(current_date as timestamp)-interval '0' day as varchar)\n", "and rco.facility_id in (1,1876,92,264,554,555,43,1873,1872,603,517,1206,1320,513,2006)\n", "and poi.item_id in (select item_id from lake_rpc.item_category_details where l0_id = 13)\n", "-- and po.vendor_id in (554,874,1246,1266,1312,1332,1333,1587,1594,1623,1687,9937,10097,11064,11150,11311,11727,12087,12751,14467,14922,15457,15513,16259,16295,16403,16880,17326,18071,18076,18093,18120,18260,18264,18617,19110,19358,814,17521,18220,1163,17381,1148,11067,12449,12553,12754,14900,15792,16882,17396,17514,17522,17613,18068,18238,18815,19171,19214,19218,19376,1022,1031,1054,1055,1142,1480,1490,1616,4414,10200,10753,11328,12747,14455,15804,16830,17617,18058,18070,18216,18221,18044\n", "-- )\n", "--  po.po_number in ()\n", "order by po.created_at)\n", "\n", "\n", ",po_base as (select * from(\n", "Select \n", "    be_outlet_id,\n", "    facility_id,\n", "    facility_name,\n", "    vendor_id,\n", "    vendor_name,\n", "    item_id,\n", "    sum(grn_qty) as grn_qty,\n", "    sum(po_quantity) ordered_qty\n", "from base b\n", "group by 1,2,3,4,5,6 )where grn_qty = 0)\n", "\n", "\n", ",tea_tagging as(\n", "Select\n", "    b.item_id,\n", "    be_outlet_id,\n", "    outlet_id as fe_outlet_id\n", "from po_base b \n", "join  lake_rpc.item_outlet_tag_mapping iotm\n", "    on iotm.item_id=b.item_id\n", "    and cast(b.be_outlet_id as varchar)=cast(iotm.tag_value as varchar))\n", "    \n", "--Select * from lake_rpc.item_outlet_tag_mapping where item_id=10000238 and tag_value=4305\n", "\n", "\n", "--Select * from dwh.fact_sales_order_item_details where product_id=496211\t--and order_create_ts_ist>= current_date - interval - '120' day \n", "\n", "\n", ",sale as (\n", "Select \n", "    be_outlet_id,\n", "    tea.item_id,\n", "    sum(procured_quantity) units_sold\n", "from  tea_tagging tea\n", "    join dwh.dim_item_product_offer_mapping ipm \n", "        on ipm.item_id=tea.item_id\n", "    join dwh.fact_sales_order_item_details fsoid\n", "        on fsoid.product_id=ipm.product_id\n", "        and fsoid.outlet_id=tea.fe_outlet_id\n", "        and is_internal_order=false\n", "        and order_create_dt_ist>= current_date - interval '7' day\n", "        and city_name is not null\n", "group by 1,2)\n", "\n", "\n", "\n", "\n", "\n", "\n", "Select \n", "    po.be_outlet_id,\n", "    po.facility_id,\n", "    facility_name,\n", "    vendor_id,\n", "    vendor_name,\n", "    po.item_id,\n", "    icd.name as item_name,\n", "    units_sold,\n", "    ordered_qty,\n", "    grn_qty\n", "from po_base po\n", "    join sale s\n", "        on s.be_outlet_id=po.be_outlet_id\n", "        and s.item_id=po.item_id\n", "    join lake_rpc.item_category_details icd\n", "         on icd.item_id=po.item_id\n", "    join lake_rpc.product_facility_master_assortment fm on fm.facility_id = po.facility_id and fm.item_id = po.item_id and fm.active = 1 and master_assortment_substate_id = 1\n", "order by units_sold desc ,units_sold/ordered_qty desc\n", "limit 10\n", "\n", "\n", "\n", " \"\"\"\n", "    return read_sql_query(df_1, trino)\n", "\n", "\n", "df_1 = df_1()"]}, {"cell_type": "code", "execution_count": null, "id": "2ee232e0-912f-43a2-bb6b-b5c6a5297f42", "metadata": {}, "outputs": [], "source": ["df_1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "066021f0-bee4-44e7-8d7e-b262707b9308", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5,\n", "    row_height=1,\n", "    font_size=22,\n", "    header_color=\"#b1d1fe\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array(\n", "            [col_width, row_height]\n", "        )\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        bbox=bbox,\n", "        colLabels=data.columns,\n", "        cellLoc=\"left\",\n", "        **kwargs\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "a2eef1e3-8ff7-49c5-90d1-d1b2fd434371", "metadata": {}, "outputs": [], "source": ["fig, ax = render_mpl_table(\n", "    df_1,\n", "    header_columns=0,\n", ")\n", "fig.savefig(\"Unfulfilled_pos.png\")\n", "file_check = \"./Unfulfilled_pos.png\"\n", "filepath = file_check\n", "channel = \"bl-beauty_alerts\"\n", "pb.send_slack_message(channel=channel, files=[filepath])"]}, {"cell_type": "code", "execution_count": null, "id": "bd4d0160-07c9-4375-98e8-3440ebf57454", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "bc12c123-94da-4774-8915-3a81f3d3b8c6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}